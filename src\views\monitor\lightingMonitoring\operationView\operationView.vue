<template>
  <PageContainer title type="list" :isClose="false" @setWebsocket="setWebsocket">
    <template slot="content">
      <div class="sino-all-content">
        <div class="top">
          <div class="toptip">运行总览</div>
          <div class="top_right">
            <div class="overview">
              <span>今日运行模式</span>
              <div class="pattern_box active_pattern" @click="() => (showTodayModeDetails = true)">
                <img style="width: 30px; height: 30px" :src="todayPattern.patternActiveSrc" alt="" />
                <span>{{ todayPattern.patternName }}</span>
              </div>
            </div>
            <el-radio-group v-model="automatic" size="mini" style="margin: auto 0" @input="automaticChange">
              <el-radio-button label="1">自动</el-radio-button>
              <el-radio-button label="2">
                手动<span v-if="second !== null">({{ second }}s)</span>
              </el-radio-button>
            </el-radio-group>
            <div class="heade-pattern">
              <div class="pattern-item" @click="handleClick('first')">
                <svg-icon :name="activeModelTab == 'first' ? 'chartModeActive' : 'chartMode'" class="pattern-icon" />
                <span :style="{ color: activeModelTab == 'first' ? '#3562DB' : '#414653' }">图形模式</span>
              </div>
              <div class="pattern-item" @click="handleClick('second')">
                <svg-icon :name="activeModelTab == 'second' ? 'listModeActive' : 'listMode'" class="pattern-icon" />
                <span :style="{ color: activeModelTab == 'second' ? '#3562DB' : '#414653' }">列表模式</span>
              </div>
            </div>
          </div>
        </div>
        <div v-if="activeModelTab == 'first'" class="graphics-box">
          <div class="monitor-content-left">
            <el-tree
              ref="tree"
              v-loading="treeLoading"
              class="tree_self"
              :data="entityMenuData"
              :props="defaultProps"
              :filter-node-method="filterNode"
              :default-expanded-keys="expanded"
              node-key="id"
              highlight-current
              @node-click="handleNodeClick"
              @check="treeChecked"
            ></el-tree>
          </div>
          <div class="monitor-content-right">
            <graphics-mode ref="scadaShow" :requestHttp="requestHttp" :entityMenuCode="selectTreeMenuCode" :projectId="projectCode" />
          </div>
        </div>
        <transition v-if="activeModelTab == 'second'" name="fade">
          <div v-show="fadeShow" class="top_echart">
            <div class="top_echart_item top_echart_left">
              <div class="left_img"></div>
              <div class="left_main">
                <div class="left_main_item">
                  <p>回路总数</p>
                  <p>{{ loopNum.outPutTotal }}<span>个</span></p>
                </div>
                <div class="left_main_item">
                  <p>模块总数</p>
                  <p>{{ loopNum.actuatorTotal }}<span>个</span></p>
                </div>
              </div>
            </div>
            <div class="top_echart_item top_echart_center">
              <echarts slot="content" ref="setPieChart" domId="setPieChart" />
            </div>
            <div class="top_echart_item top_echart_right">
              <div class="right_item" style="background: #3562db">
                <p class="right_item_value">{{ loopNum.offlineTotal }}</p>
                <p class="right_item_title">模块离线数</p>
              </div>
              <div class="right_item" style="background: #00bc6d">
                <p class="right_item_value">{{ loopNum.moduleFailureTotal }}</p>
                <p class="right_item_title">模块故障数</p>
              </div>
              <div class="right_item" style="background: #ff9435; cursor: pointer" @click="() => (showCircuitList = true)">
                <p class="right_item_value">{{ loopNum.controlReturnTotal }}</p>
                <p class="right_item_title">回路控反不一致</p>
              </div>
            </div>
          </div>
        </transition>
        <div v-if="activeModelTab == 'second'" class="middle_type" :style="{ height: fadeShow ? 'calc(74% - 55px)' : 'calc(100% - 55px)' }">
          <el-tabs v-model="activeTab" style="padding-bottom: 10px" @tab-click="handleTabsClick">
            <el-tab-pane label="按控制模块管理" name="modular"></el-tab-pane>
            <el-tab-pane label="按分组管理" name="group"></el-tab-pane>
            <el-tab-pane label="按回路管理" name="loop"></el-tab-pane>
            <el-tab-pane label="按服务空间管理" name="area"></el-tab-pane>
          </el-tabs>
          <i v-if="fadeShow" title="展开" class="el-icon-top fadeBtn" @click="fadeChange"></i>
          <i v-else title="关闭" class="el-icon-bottom fadeBtn" @click="fadeChange"></i>
          <div class="tab-content">
            <div class="search-form">
              <div>
                <el-select
                  v-if="activeTab == 'modular' || activeTab == 'loop'"
                  v-model="searchForm.constructionId"
                  class="sino_sdcp_input mr15"
                  clearable
                  placeholder="请选择设备服务建筑"
                  @change=";(searchForm.floorId = ''), (paginationData.page = 1), searchFromChange(false)"
                >
                  <el-option v-for="item in buildingOption" :key="item.id" :label="item.ssmName" :value="item.id"></el-option>
                </el-select>
                <el-select
                  v-if="activeTab == 'modular' || activeTab == 'loop'"
                  v-model="searchForm.floorId"
                  class="sino_sdcp_input mr15"
                  clearable
                  placeholder="请选择设备服务楼层"
                  @change=";(paginationData.page = 1), searchFromChange(false)"
                >
                  <el-option v-for="item in floorOption" :key="item.id" :label="item.ssmName" :value="item.id"></el-option>
                </el-select>
                <el-select
                  v-if="activeTab == 'group' || activeTab == 'area'"
                  v-model="searchForm.switchStatus"
                  class="sino_sdcp_input mr15"
                  clearable
                  placeholder="请选择开关状态"
                  @change=";(paginationData.page = 1), searchFromChange(false)"
                >
                  <el-option v-for="item in statusOption" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
                <el-select
                  v-if="activeTab == 'area'"
                  v-model="searchForm.spaceTypeId"
                  class="sino_sdcp_input mr15"
                  clearable
                  filterable
                  placeholder="请选择空间类型"
                  @change=";(paginationData.page = 1), searchFromChange(false)"
                >
                  <el-option v-for="item in areaOption" :key="item.id" :label="item.dictName" :value="item.id"></el-option>
                </el-select>
                <el-input
                  v-if="activeTab == 'group'"
                  v-model.trim="searchForm.groupName"
                  clearable
                  placeholder="搜索分组名称"
                  class="sino_sdcp_input mr15"
                  @input="debounceFun"
                ></el-input>
                <!-- @blur="searchFromChange(false)" -->
              </div>
              <el-input
                v-if="activeTab == 'modular' || activeTab == 'loop'"
                v-model.trim="searchForm.surveryName"
                clearable
                :placeholder="activeTab == 'modular' ? '搜索执行器名称' : '搜索回路名称'"
                class="sino_sdcp_input mr15"
                @input="debounceFun"
              ></el-input>
              <el-input
                v-if="activeTab == 'area'"
                v-model.trim="searchForm.spaceName"
                clearable
                placeholder="搜索空间名称"
                class="sino_sdcp_input mr15"
                @input="debounceFun"
              ></el-input>
            </div>
            <div style="height: 40px; margin: 5px 0">
              <el-button v-if="automatic == 1" type="primary" @click="btnConfig('3')">一键强开</el-button>
              <el-button v-if="automatic == 1" type="primary" @click="btnConfig('4')">一键强关</el-button>
              <el-button v-if="automatic == 2" type="primary" @click="btnConfig('1')">一键打开</el-button>
              <el-button v-if="automatic == 2" type="primary" @click="btnConfig('0')">一键关闭</el-button>
            </div>
            <div class="equ-content" :style="{ overflow: activeTab == 'modular' ? 'auto' : 'hidden' }">
              <div v-if="activeTab == 'modular'" class="modular_manage">
                <div v-for="(item, index) in modularManageData" :key="index" class="rectangle_box" :class="{ activeItem: item.checked }">
                  <div class="box_title">
                    <el-checkbox
                      ref="checkBoxRef"
                      :value="item.checked"
                      name="checkBoxId"
                      :true-label="item.actuatorId + '_true'"
                      :false-label="item.actuatorId + '_false'"
                      :checked="item.checked"
                      @change="changeCheckBox"
                    >
                    </el-checkbox>
                    <span>{{ item.localName.length ? item.localName.toString() : item.localName }}</span>
                    <div v-if="item.moduleDisplay == true" class="legend-right-box linkBg"><span class="link"></span><i>在线</i></div>
                    <div v-else-if="item.moduleDisplay == false" class="legend-right-box notlinkBg"><span class="notlink"></span><i>离线</i></div>
                    <div v-else></div>
                  </div>
                  <div class="box_content">
                    <div v-for="(itemD, idx) in item.child" :key="idx" class="box_content_one">
                      <el-tooltip class="item" effect="dark" :content="itemD.loopName || '--'" placement="top">
                        <div class="loopName">{{ itemD.loopName || '--' }}</div>
                      </el-tooltip>
                      <div class="light_bg">
                        <span :class="itemD.outputStatus == '1' ? 'onlight' : 'offlight'"><div></div></span>
                      </div>
                      <div>
                        <el-switch
                          v-if="automatic == 2 || manualControlList.includes(itemD.actuatorId + '_' + itemD.outPutId)"
                          v-model="itemD.outputStatus"
                          active-text="开"
                          inactive-text="关"
                          active-value="1"
                          inactive-value="0"
                          :width="32"
                          active-color="#3562DB"
                          :disabled="itemD.forceSwitch == 3 || itemD.forceSwitch == 4"
                          @change="loopStatusChange($event, itemD)"
                        >
                        </el-switch>
                        <span v-if="automatic == 1 && manualControlList.includes(itemD.actuatorId + '_' + itemD.outPutId)">({{ controlSecond }}s)</span>
                        <el-popover
                          v-if="automatic == 1 && !manualControlList.includes(itemD.actuatorId + '_' + itemD.outPutId)"
                          popper-class="poper_btns"
                          placement="top"
                          width="auto"
                          trigger="click"
                        >
                          <div class="switch_btns">
                            <div v-for="(hasItem, h) in btnGroupColors" :key="h">
                              <!-- 四个状态渲染 不为自己的其他三个  并且手动状态只对自动状态开放 -->
                              <el-button
                                v-if="hasItem.state != itemD.forceSwitch && !((itemD.forceSwitch == 3 || itemD.forceSwitch == 4) && hasItem.state == 6)"
                                type="primary"
                                :style="{
                                  backgroundColor: hasItem.color,
                                  color: '#fff!important',
                                  border: 'none'
                                }"
                                @click="btnConfig(hasItem.state, itemD)"
                                >{{ hasItem.name }}</el-button
                              >
                            </div>
                          </div>
                          <el-button
                            slot="reference"
                            type="primary"
                            style="margin: 0"
                            :style="{ backgroundColor: btnGroupColors.find((e) => e.state == (itemD.forceSwitch ?? '5'))?.color, color: '#fff!important', border: 'none' }"
                            >{{ btnGroupColors.find((e) => e.state == (itemD.forceSwitch ?? '5'))?.name }}</el-button
                          >
                        </el-popover>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div v-else class="other_manage">
                <el-table
                  :key="activeTab"
                  ref="tableList"
                  v-loading="tableLoading"
                  :data="tableData"
                  :border="true"
                  stripe
                  height="calc(100% - 30px)"
                  :cell-style="{ padding: '8px' }"
                  @selection-change="handleSelectionChange"
                >
                  <el-table-column type="selection" width="55" align="center"></el-table-column>
                  <el-table-column v-for="(item, index) in tableTitle" :key="index" :prop="item.value" :label="item.label" show-overflow-tooltip>
                    <template slot-scope="scope">
                      <span v-if="item.value == 'spaceType'">{{ getSpaceTypeToId(scope.row.spaceType) }}</span>
                      <span v-else>{{ scope.row[item.value] }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" min-width="150">
                    <template slot-scope="scope">
                      <!-- loop -->
                      <div v-if="automatic == 1 && activeTab == 'loop' && !manualControlList.includes(scope.row.actuatorId + '_' + scope.row.outPutId)" class="operationBtn">
                        <span v-if="scope.row.forceSwitch != 3" @click="btnConfig('3', scope.row)">强开</span>
                        <span v-if="scope.row.forceSwitch != 4" @click="btnConfig('4', scope.row)">强关</span>
                        <span class="operation_line"> | </span>
                        <span v-if="scope.row.forceSwitch != 5" @click="btnConfig('5', scope.row)">自动</span>
                        <span @click="btnConfig('6', scope.row)">手动</span>
                      </div>
                      <div v-if="(automatic == 2 || manualControlList.includes(scope.row.actuatorId + '_' + scope.row.outPutId)) && activeTab == 'loop'" class="operationBtn">
                        <span v-if="scope.row.outputStatus != 1" @click="loopStatusChange('1', scope.row)">开启</span>
                        <span v-if="scope.row.outputStatus != 0" @click="loopStatusChange('0', scope.row)">关闭</span>
                        <span v-if="automatic == 1 && manualControlList.includes(scope.row.actuatorId + '_' + scope.row.outPutId)">({{ controlSecond }}s)</span>
                      </div>
                      <!-- group -->
                      <div v-if="automatic == 1 && activeTab == 'group' && !manualControlList.includes(scope.row.groupId)" class="operationBtn">
                        <span @click="btnConfig('3', scope.row)">强开</span>
                        <span @click="btnConfig('4', scope.row)">强关</span>
                        <span class="operation_line"> | </span>
                        <span @click="btnConfig('5', scope.row)">自动</span>
                        <span @click="btnConfig('6', scope.row)">手动</span>
                      </div>
                      <div v-if="(automatic == 2 || manualControlList.includes(scope.row.groupId)) && activeTab == 'group'" class="operationBtn">
                        <span @click="btnConfig('1', scope.row)">开启</span>
                        <span @click="btnConfig('0', scope.row)">关闭</span>
                        <span v-if="automatic == 1 && manualControlList.includes(scope.row.groupId)">({{ controlSecond }}s)</span>
                      </div>
                      <!-- area -->
                      <div v-if="automatic == 1 && activeTab == 'area' && !manualControlList.includes(scope.row.spaceId)" class="operationBtn">
                        <span @click="btnConfig('3', scope.row)">强开</span>
                        <span @click="btnConfig('4', scope.row)">强关</span>
                        <span class="operation_line"> | </span>
                        <span @click="btnConfig('5', scope.row)">自动</span>
                        <span @click="btnConfig('6', scope.row)">手动</span>
                      </div>
                      <div v-if="(automatic == 2 || manualControlList.includes(scope.row.spaceId)) && activeTab == 'area'" class="operationBtn">
                        <span @click="btnConfig('1', scope.row)">开启</span>
                        <span @click="btnConfig('0', scope.row)">关闭</span>
                        <span v-if="automatic == 1 && manualControlList.includes(scope.row.spaceId)">({{ controlSecond }}s)</span>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
                <el-pagination
                  class="pagination"
                  :current-page="paginationData.page"
                  :page-sizes="[15, 30, 50, 100]"
                  :page-size="paginationData.pageSize"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="paginationData.total"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                ></el-pagination>
              </div>
            </div>
          </div>
        </div>
        <template v-if="automaticChangeDialogShow">
          <automaticChangeDialog
            :automaticChangeDialogShow="automaticChangeDialogShow"
            :dialogData="automaticDialogData"
            @closeAutomaticDialog="closeAutomaticDialog"
            @automaticSubmit="automaticSubmit"
          />
        </template>
        <template v-if="controlReportDialogShow">
          <controlReportDialog :controlReportDialogShow="controlReportDialogShow" :dialogData="controlDialogData" @closeControlDialog="closeControlDialog" />
        </template>
        <todayModeDetails v-if="showTodayModeDetails" :todayPattern="todayPattern" :projectCode="projectCode" :visible.sync="showTodayModeDetails" />
        <circuitList v-if="showCircuitList" :visible.sync="showCircuitList" />
        <modeSwitch v-if="showModeSwitch" :type="automatic" :visible.sync="showModeSwitch" @modeSwitch="modeSwitch" />
        <!-- <audio
          class="audio"
          controls
          ref="audio"
          src="http://ihcrssjt.logimis.com:10001/ipsm/9d4249e0d3fa42c2b326429210e81e31.mp3?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=minioadmin%2F20220908%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20220908T025328Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=2bf278d8994c32e3dab45498aea88fba8ed412d6e2ebbf047941ece2f863c1dc"
          ></audio> -->
      </div>
    </template>
  </PageContainer>
</template>
<script type="text/ecmascript-6">
// import { objectExpression } from 'babel-types'
import moment from 'moment'
import sunshine_weather_active from '@/assets/images/lightingMonitoring/sunshine_weather_active.png'
import automaticChangeDialog from '../components/automaticChangeDialog.vue'
import controlReportDialog from '../components/controlReportDialog.vue'
import Dict from '../components/dict.js'
import { monitorTypeList } from '@/util/dict.js'
export default {
  name: 'OperationView',
  components: {
    automaticChangeDialog,
    controlReportDialog,
    todayModeDetails: () => import('../components/todayModeDetailsDialog'),
    circuitList: () => import('../components/circuitListDialog'),
    modeSwitch: () => import('../components/modeSwitchDialog'),
    graphicsMode: () => import('../../airMenu/components/graphicsMode')
  },
  data() {
    return {
      requestHttp: __PATH.VUE_IEMC_API,
      activeModelTab: 'second',
      treeLoading: true,
      expanded: [],
      // checkedData: {},
      selectTreeMenuCode: '',
      defaultProps: {
        children: 'children',
        label: 'name',
        value: 'code'
      },
      entityMenuData: [],
      showModeSwitch: false,
      showCircuitList: false,
      showTodayModeDetails: false, // 今日模式详情
      projectCode: monitorTypeList.find((item) => item.projectName == '照明监测').projectCode,
      automatic: '1',
      controlSecond: null, // 控制模块手动操作倒计时
      second: null, // 手动操作倒计时
      timerSecond: null, // 定时器
      todayPattern: {
        patternName: '晴天模式',
        patternActiveSrc: sunshine_weather_active
      },
      patternTypeIconList: Dict.patternTypeIconList,
      checkBoxGroup: [], // 控制器选中
      selectionData: [], // table选中
      automaticChangeDialogShow: false, // 自动切换弹窗
      automaticDialogData: {}, // 自动切换弹窗数据
      controlReportDialogShow: false, // 控制报表弹窗显示
      controlDialogData: {}, // 控制报表弹窗数据
      btnGroupColors: [
        {
          name: '强开',
          color: '#34B253',
          value: 'forcedOpen',
          state: '3'
        },
        {
          name: '强关',
          color: '#5188FC',
          value: 'forcedClose',
          state: '4'
        },
        {
          name: '自动',
          color: '#8671DE',
          value: 'auto',
          state: '5'
        },
        {
          name: '手动',
          color: '#FFBB52',
          value: 'manual',
          state: '6'
        }
      ],
      fadeShow: true, // 淡入淡出
      loopNum: {
        openTotal: 0,
        closeTotal: 0,
        unknownTotal: 0,
        outPutTotal: 0,
        actuatorTotal: 0,
        offlineTotal: 0,
        moduleFailureTotal: 0,
        controlReturnTotal: 0
      },
      activeTab: 'modular',
      buildingOption: [],
      // floorOption: [],
      floorAllOption: [],
      areaOption: [],
      statusOption: [
        {
          id: '1',
          name: '开启'
        },
        {
          id: '0',
          name: '关闭'
        }
      ],
      eventStatus: true, // 查询节流
      searchForm: {
        switchStatus: '',
        constructionId: '',
        floorId: '',
        surveryName: '',
        groupName: '',
        spaceName: '',
        spaceTypeId: '',
        type: 1
      },
      tableData: [],
      tableLoading: false,
      tableTitle: [],
      tableGroupTitle: [
        { label: '分组名称', value: 'groupName' },
        { label: '回路数量', value: 'loopsNum' },
        { label: '开关状态', value: 'state' }
      ],
      tableLoopTitle: [
        { label: '回路名称', value: 'loopsName' },
        { label: '状态', value: 'state' }
      ],
      tableAreaTitle: [
        { label: '基础空间名称', value: 'spaceName' },
        { label: '空间类型', value: 'spaceType' },
        { label: '支路数量', value: 'loopsNum' },
        { label: '状态', value: 'state' }
      ],
      paginationData: {
        page: 1,
        pageSize: 15,
        total: 0
      },
      modularManageData: [
        // 模块管理数据
      ],
      tabType: ['modular', 'group', 'loop', 'area'],
      manualControlList: [], // 控制模块手动选中列表
      inputTimer: null // 节流定时器
    }
  },
  computed: {
    floorOption() {
      return this.floorAllOption.filter((item) => item.pid == this.searchForm.constructionId)
    }
  },
  mounted() {
    this.getStructureTree()
    this.getAreaTypeList()
    this.getMonitorStatusList()
    this.searchFromChange()
    this.getTreeData()
    // this.setWebsocket()
  },
  methods: {
    format() {
      return ''
    },
    setWebsocket() {
      let projectCode = sessionStorage.getItem('websocket') ? JSON.parse(sessionStorage.getItem('websocket')).projectCode : ''
      // 匹配设备相同的接收消息刷新页面
      if (projectCode == this.projectCode) {
        // 收到消息刷新页面
        console.log('照明匹配成功')
        this.paginationData.page = 1
        this.searchFromChange(true)
      }
    },
    // Tab切换
    handleClick(type) {
      this.activeModelTab = type
      if (this.activeModelTab == 'second') {
        // this.getTableData()
        this.getStructureTree()
        this.getAreaTypeList()
        this.getMonitorStatusList()
        this.searchFromChange()
      } else {
        if (this.entityMenuData.length > 0) {
          this.expanded = [this.entityMenuData[0].id]
          this.$nextTick(() => {
            this.$refs.tree.setCurrentKey(this.entityMenuData[0].id)
          })
          this.selectTreeMenuCode = this.entityMenuData[0].code
          // this.checkedData = this.entityMenuData[0]
          // this.$refs.scadaShow.getScadaList()
        }
      }
    },
    // 图形模式切换
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    treeChecked(data, checked) {
      // this.checkedData = data
      this.$refs.tree.setCheckedNodes([data])
    },
    getTreeData() {
      this.treeLoading = true
      this.$api
        .getEntityMenuList(
          {
            projectId: this.projectCode
          },
          this.requestHttp
        )
        .then((res) => {
          this.treeLoading = false
          let list = this.$tools.transData(res.data, 'code', 'parentId', 'children')
          this.entityMenuData = list
        })
    },
    // 图纸模式树状图切换
    async handleNodeClick(data, checked) {
      await (this.selectTreeMenuCode = data.code)
      this.$refs.tree.setCheckedNodes([data])
      // this.checkedData = data
      this.$refs.scadaShow.getScadaList()
    },
    // 手动自动切换
    automaticChange(val) {
      this.automatic = val
      this.showModeSwitch = true
    },
    modeSwitch(status) {
      if (!status) {
        this.automatic = this.automatic == '1' ? '2' : '1'
        return
      }
      this.manualControlList = [] // 手动的列表置空
      // 手动状态开始倒计时
      const flag = this.automatic == 2
      if (flag) {
        // 监听鼠标移动事件
        document.addEventListener('mousemove', this.windowAddEvent)
      } else {
        // 取消监听mousemove
        // document.removeEventListener("mousemove", this.windowAddEvent)
        this.windowRemoveEvent()
      }
      // 切换 数据重新加载
      this.searchFromChange()
    },
    windowAddEvent() {
      console.log('监听鼠标移动事件')
      let that = this
      // that.second = 60
      let second = 60
      if (that.automatic == 2) {
        that.second = second
      } else {
        that.controlSecond = second
      }
      // 清除定时器
      clearInterval(that.timerSecond)
      that.timerSecond = setInterval(() => {
        // that.second--
        second--
        if (that.automatic == 2) {
          that.second = second
        } else {
          that.controlSecond = second
        }
        if (second == 0) {
          that.windowRemoveEvent()
        }
      }, 600)
    },
    windowRemoveEvent() {
      console.log('取消监听鼠标移动事件')
      document.removeEventListener('mousemove', this.windowAddEvent)
      this.second = null
      this.controlSecond = null
      clearInterval(this.timerSecond)
      this.automatic = 1
      this.manualControlList = [] // 手动的列表置空
    },
    // 强开强关 开启关闭
    btnConfig(type, row) {
      // console.log(type);
      const viewType = this.tabType.indexOf(this.activeTab) + 1
      // 有值代表单独操作
      if (row) {
        // 自动转其他类型需要提交弹窗(控制模块/回路模块)
        if (row.forceSwitch == 5) {
          this.automaticDialogData = this.btnGroupColors.find((e) => e.state == type)
          Object.assign(this.automaticDialogData, {
            ...row,
            state: this.automaticDialogData.state,
            viewType: viewType
          })
          this.automaticChangeDialogShow = true
        } else {
          // 强开强关转其他 分组和空间操作
          if (viewType == 1 || viewType == 3) {
            // 控制 回路 单控
            this.$confirm('此操作将改变回路运行状态, 是否继续?', '提示', {
              cancelButtonClass: 'el-button--primary is-plain',
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              let params = {
                type: viewType,
                actuatorId: row.actuatorId,
                outputNum: row.outPutId,
                outputStatus: type == 3 ? '1' : type == 4 ? '0' : type == 5 ? null : type,
                forceSwitch: type == 1 || type == 0 ? null : type
              }
              this.controlSubmit(params, 'alone')
            })
          } else if (viewType == 2 || viewType == 4) {
            // 分组群控 建筑群控
            if (type == 6) {
              // 切换手动
              this.automaticDialogData = this.btnGroupColors.find((e) => e.state == type)
              Object.assign(this.automaticDialogData, {
                ...row,
                state: this.automaticDialogData.state,
                viewType: viewType
              })
              this.automaticChangeDialogShow = true
            } else {
              // 强开强关开启关闭
              this.$confirm('此操作将改变回路运行状态, 是否继续?', '提示', {
                cancelButtonClass: 'el-button--primary is-plain',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                let params = {
                  type: viewType,
                  // groupId: row.groupId,
                  outputStatus: type == 3 ? '1' : type == 4 ? '0' : type == 5 ? null : type,
                  forceSwitch: type == 1 || type == 0 ? null : type
                }
                if (viewType == 2) {
                  Object.assign(params, {
                    groupId: row.groupId
                  })
                } else {
                  Object.assign(params, {
                    serviceSpaceId: row.spaceId
                  })
                }
                if (type == '5') {
                  this.controlSubmit(params, 'alone')
                } else {
                  this.controlSubmit(params, 'group')
                }
              })
            }
          } else {
            //
          }
        }
      } else {
        // 没值代表批量操作
        let params = {}
        // 执行器
        if (viewType == 1) {
          if (!this.checkBoxGroup.length) return this.$message.warning('请选中数据后再进行批量操作！')
          const groupControl = this.checkBoxGroup.map((item) => {
            let sameData = this.modularManageData.find((e) => e.actuatorId == item)
            if (sameData.child.length) {
              let dealData = sameData.child.map((item) => {
                return item.actuatorId + '_' + item.outPutId
              })
              return dealData
            }
          })
          // console.log(groupControl.flat());
          params = {
            type: viewType,
            groupControl: groupControl.flat().toString(),
            outputStatus: type == 3 ? '1' : type == 4 ? '0' : type == 5 ? null : type, // 强开同时开启 自动忽略穿5
            forceSwitch: type == 1 || type == 0 ? null : type
          }
        } else {
          if (!this.selectionData.length) return this.$message.warning('请选中数据后再进行批量操作！')
          if (viewType == 2) {
            // 分组
            let dealData = Array.from(this.selectionData, ({ groupId }) => groupId)
            params = {
              type: viewType,
              groupId: dealData.toString(),
              outputStatus: type == 3 ? '1' : type == 4 ? '0' : type == 5 ? null : type, // 强关同时关闭
              forceSwitch: type == 1 || type == 0 ? null : type
            }
          } else if (viewType == 3) {
            // 回路
            console.log(this.selectionData)
            let dealData = this.selectionData.map((item) => {
              return item.actuatorId + '_' + item.outPutId
            })
            params = {
              type: viewType,
              groupControl: dealData.toString(),
              outputStatus: type == 3 ? '1' : type == 4 ? '0' : type == 5 ? null : type, // 强关同时关闭
              forceSwitch: type == 1 || type == 0 ? null : type // 开关转为自动
            }
          } else {
            // 建筑空间
            let dealData = Array.from(this.selectionData, ({ spaceId }) => spaceId)
            params = {
              type: viewType,
              serviceSpaceId: dealData.toString(),
              outputStatus: type == 3 ? '1' : type == 4 ? '0' : type == 5 ? null : type, // 强关同时关闭
              forceSwitch: type == 1 || type == 0 ? null : type
            }
          }
        }
        this.controlSubmit(params, 'group')
      }
    },
    // 获取选中执行器id
    changeCheckBox(val) {
      let [data, flag] = val.split('_')
      // 没有增加 有了删除
      if (flag == 'true') {
        this.checkBoxGroup.push(data)
      } else {
        this.checkBoxGroup.splice(this.checkBoxGroup.indexOf(data), 1)
      }
      this.modularManageData.map((item) => {
        item.checked = this.checkBoxGroup.some((e) => e == item.actuatorId)
      })
      this.$forceUpdate()
      console.log(this.checkBoxGroup)
    },
    handleTabsClick() {
      console.log('tab切换--', this.activeTab)
      this.checkBoxGroup = [] // 选中重置(控制模块)
      this.selectionData = [] // 选中重置(table选中)
      this.manualControlList = [] // 手动控制列表重置
      this.windowRemoveEvent() // 解除手动状态的监听
      const column = [
        {
          name: 'group',
          value: 'tableGroupTitle'
        },
        {
          name: 'loop',
          value: 'tableLoopTitle'
        },
        {
          name: 'area',
          value: 'tableAreaTitle'
        }
      ]
      const tabType = ['modular', 'group', 'loop', 'area']
      // 获取表格列
      this.tableTitle = this.activeTab === 'modular' ? [] : this[column.filter((e) => e.name == this.activeTab)[0].value]
      // 获取选中tab的类型
      Object.assign(this.searchForm, {
        switchStatus: '',
        constructionId: '',
        floorId: '',
        surveryName: '',
        groupName: '',
        spaceName: '',
        spaceTypeId: '',
        type: tabType.indexOf(this.activeTab) + 1
      })
      Object.assign(this.paginationData, {
        page: 1,
        pageSize: 15,
        total: 0
      })
      this.searchFromChange(false)
    },
    // 获取空间 建筑 楼层信息
    getStructureTree() {
      this.$api.getStructureTree({}).then((res) => {
        const data = res.data
        if (data && data.length) {
          this.buildingOption = data.filter((e) => e.ssmType == 3)
          this.floorAllOption = data.filter((e) => e.ssmType == 4)
        }
      })
    },
    // 获取空间类型
    getAreaTypeList() {
      this.$api.valveTypeList({ typeValue: 'SP' }).then((res) => {
        const data = res.data
        if (data && data.length) {
          this.areaOption = data
          // console.log(this.areaOption)
        }
      })
    },
    // 获取总览数据
    getMonitorStatusList() {
      this.$api.getCountRunToDayState({ projectCode: this.projectCode }).then((res) => {
        if (res.code == '200') {
          // 遍历对象res.data
          let data = res.data[1]
          console.log(data)
          let weatherPattern = res.data[2].weatherPattern
          // 今日运行模式
          this.todayPattern = {
            patternActiveSrc: this.patternTypeIconList.find((item) => item.patternCode == weatherPattern.patternType)?.patternActiveSrc,
            patternName: weatherPattern.patternName,
            patternType: weatherPattern.patternType,
            id: weatherPattern.id
          }
        }
      })
    },
    // 查询事件
    searchFromChange(flag = true) {
      // 是否需要重载总览数据
      // console.log('查询参数--', this.searchForm);
      const params = {
        projectCode: this.projectCode,
        ...this.searchForm,
        ...this.paginationData
      }
      delete params.total
      this.tableLoading = true
      this.$api.groupOperationMonitoring(params).then((res) => {
        const data = res.data
        this.tableLoading = false
        if (this.activeTab === 'modular') {
          // console.log(data)
          this.modularManageData = data ?? []
          this.modularManageData.map((item) => {
            item.checked = false
          })
          this.$forceUpdate()
        } else {
          this.tableData = data.list ?? []
          this.paginationData.total = data.count
        }
      })
      flag ? this.searchCountOverview() : ''
      flag ? this.getMonitorStatusList() : ''
      var that = this
      if (!that.eventStatus) {
        return
      }
      that.eventStatus = false
      setTimeout(() => {
        try {
          that.getEquipmentRealTimeData()
        } catch (error) {}
        that.eventStatus = true
      }, 1500)
    },
    // 查询顶部总览
    searchCountOverview() {
      const arr = [
        {
          name: '开启',
          value: 0,
          color: '#3562DB'
        },
        {
          name: '关闭',
          value: 0,
          color: '#FF9435'
        },
        {
          name: '其他',
          value: 0,
          color: '#00BC6D'
        }
      ]
      this.$api.countOverview().then((res) => {
        if (res.code == 200) {
          // console.log(res);
          this.loopNum = res.data
          arr[0].value = res.data.openTotal == '-' ? 0 : res.data.openTotal
          arr[1].value = res.data.closeTotal == '-' ? 0 : res.data.closeTotal
          arr[2].value = res.data.unknownTotal == '-' ? 0 : res.data.unknownTotal
          this.$nextTick(() => {
            console.log(arr)
            this.$refs.setPieChart.init(this.setPieChart(arr))
          })
        }
      })
    },
    // 统计图echarts
    setPieChart(data) {
      return {
        color: data.map((v) => v.color),
        tooltip: {
          formatter: '{b0}<br />{c0}个<br />{d}%'
        },
        legend: {
          orient: 'vertical',
          top: 'center',
          left: '60%',
          itemWidth: 10,
          itemHeight: 10,
          itemGap: 20,
          data: data.map((v) => v.name),
          formatter: function (name) {
            return name + '  ' + data.find((v) => v.name == name).value + ' 个'
          }
        },
        series: [
          {
            type: 'pie',
            radius: [0, '70%'],
            center: ['35%', '50%'],
            data: data,
            label: {
              show: false
            }
          }
        ]
      }
    },
    // table 选中事件
    handleSelectionChange() {
      this.selectionData = this.$refs.tableList.selection
    },
    // 获取 实时设备数据
    getEquipmentRealTimeData() {
      // console.log('data');
    },
    // 回路执行器 回路单控（手动/ 自动） 改变 auto判断自动手动
    loopStatusChange(val, item, auto) {
      console.log('switch--', val, item)
      // this.$confirm('此操作将改变回路运行状态, 是否继续?', '提示', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning'
      // }).then(() => {
      const params = {
        type: this.tabType.indexOf(this.activeTab) + 1,
        actuatorId: item.actuatorId,
        outputNum: item.outPutId,
        outputStatus: val == 3 ? '1' : val == 4 ? '0' : val == 5 ? null : val,
        forceSwitch: val == 1 || val == 0 ? null : val
      }
      this.controlSubmit(params, 'alone')
      // })
    },
    // 提交控制 type 单控或者群控
    controlSubmit(params, type) {
      this.tableLoading = true
      console.log('params--', params)
      this.$api.lightOpenOrClose(params).then((res) => {
        this.tableLoading = false
        // console.log(res);
        if (res.code == 200) {
          if (type == 'alone') {
            if (res.data.code == 200) {
              this.$message.success(res.data.returnMap[0].outputStatus)
            } else {
              this.$message.warning(res.data.returnMap[0].outputStatus)
            }
            this.searchFromChange()
          } else {
            res.data.date = moment(res.data.date).format('HH:mm:ss')
            this.controlDialogData = res.data
            this.controlReportDialogShow = true
          }
        } else {
          this.$message.warning(res.message)
          this.searchFromChange()
        }
      })
    },
    // 输入框节流
    debounceFun() {
      if (this.inputTimer) {
        clearTimeout(this.inputTimer)
      }
      this.inputTimer = setTimeout(() => {
        this.inputTimer = null
        this.paginationData.page = 1
        this.searchFromChange(false)
      }, 1000)
    },
    handleSizeChange(val) {
      this.paginationData.page = 1
      this.paginationData.pageSize = val
      this.searchFromChange(false)
    },
    handleCurrentChange(val) {
      this.paginationData.page = val
      this.searchFromChange(false)
    },
    getSpaceTypeToId(id) {
      const obj = this.areaOption.find((e) => e.id == id)
      return obj ? obj.dictName : ''
    },
    closeAutomaticDialog(data) {
      // this.automaticSubmit(data)
      this.automaticChangeDialogShow = false
    },
    closeControlDialog() {
      this.controlReportDialogShow = false
      this.checkBoxGroup = [] // 选中重置
      this.selectionData = []
      this.searchFromChange()
    },
    // 切换控制提交
    automaticSubmit(data) {
      // 切换为手动的list
      if (data.state == 6) {
        if (data.viewType == 1 || data.viewType == 3) {
          this.manualControlList.push(data.actuatorId + '_' + data.outPutId)
        } else if (data.viewType == 2) {
          this.manualControlList.push(data.groupId)
        } else if (data.viewType == 4) {
          this.manualControlList.push(data.spaceId)
        }
        // 开启监听60s
        this.controlSecond = 60
        document.addEventListener('mousemove', this.windowAddEvent)
      } else {
        // 强开强关
        if (data.viewType == 1 || data.viewType == 3) {
          this.loopStatusChange(data.state, data, true)
        } else if (data.viewType == 2 || data.viewType == 4) {
          let params = {
            type: data.viewType,
            // groupId: data.groupId,
            outputStatus: data.state == 3 ? '1' : data.state == 4 ? '0' : data.state,
            forceSwitch: data.state
          }
          if (data.viewType == 2) {
            Object.assign(params, {
              groupId: data.groupId
            })
          } else {
            Object.assign(params, {
              serviceSpaceId: data.spaceId
            })
          }
          this.controlSubmit(params, 'group')
        }
      }
      this.automaticChangeDialogShow = false
    },
    // 改变高度
    fadeChange() {
      this.fadeShow = !this.fadeShow
      if (this.fadeShow) {
        setTimeout(() => {
          this.getMonitorStatusList()
        }, 300)
      }
    }
  }
}
</script>
<style src="@/assets/styles/sinomis-ui.css" scoped></style>
<style lang="scss" scoped>
.sino-all-content {
  height: calc(100% - 0px);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  border-radius: 0;
  background-color: #f5f5fa;
  .top {
    border-radius: 10px;
    height: 48px;
    line-height: 48px;
    background-color: #fff;
    display: flex;
    .toptip {
      font-size: 18px;
      font-weight: 600;
      color: #333333;
      line-height: 21px;
      width: auto;
      border-bottom: none;
    }
    .top_right {
      flex-grow: 1;
      display: flex;
      justify-content: flex-end;
      padding: 0 20px;
      box-sizing: border-box;
      .overview {
        display: flex;
        .pattern_box {
          box-sizing: border-box;
          height: 38px;
          line-height: 38px;
          padding: 0 30px;
          border-radius: 24px;
          margin: auto 15px;
          cursor: pointer;
          img {
            vertical-align: middle;
            margin-right: 10px;
          }
          span {
            font-size: 14px;
            font-family: 'HarmonyOS_Sans_SC';
            color: #333;
          }
        }
        .active_pattern {
          background: #fffae8;
          border: 1px solid #ffe8ab;
        }
      }
      .heade-pattern {
        display: flex;
        margin: 0 16px;
        // position: absolute;
        // right: 16px;
        // top: 50%;
        // margin: 0 !important;
        // transform: translateY(-50%);
        .pattern-item {
          cursor: pointer;
          font-size: 15px;
          .pattern-icon {
            font-size: 16px;
            margin-right: 6px;
          }
        }
        .pattern-item:last-child {
          margin-left: 16px;
        }
      }
    }
  }
  .top_echart {
    width: 100%;
    height: calc(26% - 10px);
    display: flex;
    justify-content: space-between;
    .top_echart_item {
      width: calc(100% / 3 - 6px);
      height: 100%;
      background: #fff;
    }
    .top_echart_left {
      display: flex;
      align-items: center;
      .left_img {
        background: url('~@/assets/images/lightingMonitoring/lightingImg.png') no-repeat;
        background-size: 66px;
        background-position: center;
        width: 66px;
        height: 73px;
        box-sizing: content-box;
        padding: 0px 48px;
        border-right: 1px solid #e4e7ed;
      }
      .left_main {
        flex: 1;
        display: flex;
        align-items: center;
        .left_main_item {
          width: 50%;
          text-align: center;
          p:first-child {
            font-size: 15px;
            font-weight: 500;
            color: #333333;
            line-height: 18px;
            margin-bottom: 16px;
          }
          p:last-child {
            font-size: 24px;
            font-weight: bold;
            color: #333333;
            margin-bottom: 0;
            span {
              font-size: 14px;
              font-weight: 500;
              color: #7f848c;
              margin-left: 4px;
            }
          }
        }
      }
    }
    .top_echart_center {
    }
    .top_echart_right {
      padding: 0px 24px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      p {
        margin: 0;
      }
      .right_item {
        width: calc(100% / 3 - 10px);
        padding: 21px 0px;
        border-radius: 4px;
        text-align: center;
        color: #ffffff;
      }
      .right_item_value {
        font-size: 30px;
        font-weight: 500;
        line-height: 35px;
      }
      .right_item_title {
        font-size: 15px;
        font-weight: 500;
        line-height: 18px;
        margin-top: 8px;
      }
    }
  }
  .middle_type {
    width: 100%;
    // height: calc(50% - 55px);
    box-sizing: border-box;
    background-color: #fff;
    padding: 3px;
    border-radius: 10px;
    position: relative;
    transition: height 0.3s linear;
    overflow: hidden;
    .fadeBtn {
      position: absolute;
      top: 10px;
      right: 40px;
      font-size: 25px;
      cursor: pointer;
    }
    .tab-content {
      height: calc(100% - 55px);
      padding: 0 10px;
      .search-form {
        display: flex;
        justify-content: space-between;
      }
      .equ-content {
        height: calc(100% - 90px);
        padding-top: 6px;
        // overflow: auto;
        > div {
          width: 100%;
        }
        .modular_manage {
          display: flex;
          flex-wrap: wrap;
          .rectangle_box {
            background: #fff;
            border: 1px solid #e4e7ed;
            border-radius: 4px;
            margin: 0 10px 10px 0;
            overflow: hidden;
            .box_title {
              height: 45px;
              display: flex;
              line-height: 45px;
              justify-content: space-between;
              padding: 0 15px;
              box-sizing: border-box;
              box-shadow: 0px 0px 5px 0px rgba(18, 31, 62, 0.12);
              .notlinkBg {
                background-color: #f2f2f2;
                color: #606266;
              }
              .linkBg {
                background-color: #e4f2e6;
                color: #34b253;
              }
              .legend-right-box {
                width: 50px;
                height: 18px;
                border-radius: 2px;
                margin: auto 0;
                display: flex;
                margin-left: 15px;
                span {
                  display: inline-block;
                  width: 18px;
                  height: 18px;
                  margin-right: 2px;
                  line-height: 18px;
                  padding: 2px;
                }
                i {
                  font-size: 13px;
                  line-height: 18px;
                  // i标签不倾斜
                  font-style: normal;
                }
                .notlink {
                  background: url('~@/assets/images/lightingMonitoring/notlink.png') no-repeat;
                  background-size: 100% 100%;
                }
                .link {
                  background: url('~@/assets/images/lightingMonitoring/link.png') no-repeat;
                  background-size: 100% 100%;
                }
              }
            }
            .box_content {
              display: flex;
              padding: 33px 16px 16px 0;
              flex-wrap: wrap;
              .box_content_one {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                width: 120px;
                margin-bottom: 14px;
                // height: 130px;
                text-align: center;
                // flex: 1;
                padding: 16px 16px;
                margin-left: 16px;
                box-sizing: border-box;
                background: #faf9fc;
                border: 1px solid #faf9fc;
                border-radius: 8px;
                > div {
                  width: 100%;
                }
                .light_bg {
                  // width: 26px;
                  // height: 34px;
                  > span {
                    display: inline-block;
                    width: 26px;
                    height: 34px;
                    position: relative;
                    div {
                      position: absolute;
                      bottom: 0;
                      right: -10px;
                      width: 6px;
                      height: 6px;
                      border-radius: 50%;
                      background: #34b253;
                    }
                  }
                  .onlight {
                    background: url('~@/assets/images/lightingMonitoring/onlight.png') no-repeat;
                    background-size: 100% 100%;
                    > div {
                      background: #34b253;
                    }
                  }
                  .offlight {
                    background: url('~@/assets/images/lightingMonitoring/offlight.png') no-repeat;
                    background-size: 100% 100%;
                    > div {
                      background: #ff4848;
                    }
                  }
                }
                &:hover {
                  background: #e6effc;
                  border: 1px solid #5089fc;
                }
              }
            }
          }
          .activeItem {
            border: 1px solid #3562db;
          }
        }
        .other_manage {
          height: 100%;
          .pagination {
            height: 30px !important;
            padding-top: 2px !important;
          }
          .operationBtn {
            span {
              font-size: 14px;
              font-family: 'HarmonyOS_Sans_SC_Medium';
              color: #5188fc;
              margin: auto 10px;
              cursor: pointer;
            }
            .operation_line {
              color: #d8dee7;
            }
          }
        }
      }
    }
  }
  .graphics-box {
    height: calc(100% - 16px);
    display: flex;
    justify-content: space-between;
    margin-top: 16px;
    ::v-deep .monitor-content-left {
      width: 246px;
      height: 100%;
      background: #fff;
      border-radius: 4px;
      padding: 16px;
      overflow: auto;
      .el-tree {
        height: 100%;
      }
      .el-tree-node {
        .el-tree-node__content {
          padding: 6px 0;
          height: auto;
        }
      }
      .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
        background-color: #d9e1f8;
      }
    }
    .monitor-content-right {
      height: 100%;
      width: calc(100% - 260px);
      .scada-preview {
        padding: 0;
      }
    }
  }
}
</style>
<style lang="scss">
.middle_type {
  .el-tabs__nav-wrap {
    padding-left: 15px;
  }
  .el-tabs__nav-wrap::after {
    height: 1px;
    background-color: #d8dee7;
  }
  .el-switch__label {
    color: #2d2d2d;
  }
  .el-switch__label.is-active {
    color: #424242;
  }
  // .el-switch__core::after {
  //   width: 14px;
  //   height: 14px;
  //   top: -3px;
  //   left: -5px;
  //   box-shadow: 0 2px 7px 0 #a4a4b7;
  // }
  .el-table thead {
    height: 40px !important;
    line-height: 40px !important;
  }
  .pagination {
    // height: auto!important;
    padding-top: 15px !important;
  }
}
.poper_btns {
  max-height: 200px !important;
  overflow: hidden !important;
}
.switch_btns {
  display: flex;
  flex-wrap: wrap;
  div {
    // width: 50%;
  }
  .el-button {
    // width: 25%;
    margin: 5px 10px !important;
    border: none !important;
  }
}
.loopName {
  width: 100%;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
