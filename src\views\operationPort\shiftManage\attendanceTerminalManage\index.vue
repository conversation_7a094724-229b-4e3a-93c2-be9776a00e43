<!--
 * @Description:
-->
<template>
  <PageContainer>
    <div slot="header" class="control-btn-header">
      <div class="search-from">
        <el-input v-model="searchForm.name" placeholder="搜索设备名称、位置" clearable style="width: 200px"></el-input>
        <el-select v-model="searchForm.state" placeholder="状态" clearable>
          <el-option v-for="item in attendanceStateList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <div style="display: inline-block">
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="searchFormEvent">查询</el-button>
        </div>
      </div>
      <div class="event-btn">
        <el-button v-auth="'attendanceTerminalManage:add'" type="primary" icon="el-icon-plus" @click="control('add')">绑定设备</el-button>
        <el-button type="primary" @click="managePsdConfig">管理密码配置</el-button>
      </div>
    </div>
    <div slot="content" class="table-content">
      <TablePage
        ref="table"
        v-loading="tableLoading"
        :showPage="true"
        :tableColumn="tableColumn"
        :data="tableData"
        height="calc(100% - 40px)"
        :pageData="pageData"
        :pageProps="pageProps"
        @pagination="paginationChange"
      >
      </TablePage>
      <PsdConfigDialog v-if="psdConfigDialogShow" :visible.sync="psdConfigDialogShow" @close="() => (psdConfigDialogShow = false)"> </PsdConfigDialog>
      <PreSaleStateDialog v-if="preSaleStateDialogShow" :visible.sync="preSaleStateDialogShow" :selectItems="selectItems" @close="() => (preSaleStateDialogShow = false)">
      </PreSaleStateDialog>
    </div>
  </PageContainer>
</template>
<script lang="jsx">
import { attendanceStateList } from '../component/dict.js'
export default {
  name: 'attendanceTerminalManage',
  components: {
    PsdConfigDialog: () => import('./psdConfigDialog.vue'),
    PreSaleStateDialog: () => import('./preSaleStateDialog.vue')
  },
  beforeRouteEnter(to, from, next) {
    let names = []
    to.matched.map((v, i) => {
      if (i > 0) {
        v.components.default.name && names.push(v.components.default.name)
      }
    })
    // 进入页面时，先将当前页面的 name 信息存入 keep-alive 全局状态
    setTimeout(() => {
      next((vm) => {
        vm.$store.commit('keepAlive/add', names)
      })
    }, 0)
  },
  async beforeRouteLeave(to, from, next) {
    // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
    if (!['addAttendanceTerminalManage'].includes(to.name)) {
      // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      psdConfigDialogShow: false,
      preSaleStateDialogShow: false,
      selectItems: {},
      tableLoading: false,
      searchForm: {
        name: '',
        state: ''
      },
      attendanceStateList,
      tableColumn: [
        {
          prop: 'name',
          label: '设备名称'
        },
        {
          prop: 'spaceDesc',
          label: '位置描述'
        },
        {
          prop: 'sn',
          label: 'SN码'
        },
        {
          prop: 'ip',
          label: 'IP地址'
        },
        {
          prop: 'mac',
          label: 'MAC地址'
        },
        {
          prop: 'versionName',
          label: '软件版本'
        },
        {
          prop: 'createdTime',
          label: '添加时间',
          formatter: (scope) => {
            return this.$tools.dateToStr(scope.row.createdTime)
          }
        },
        {
          prop: 'state',
          label: '状态',
          render: (h, row) => {
            const stateData = attendanceStateList.find((v) => v.value === row.row.state)
            return <span style={{ color: stateData?.color }}>{stateData?.label}</span>
          }
        },
        {
          prop: 'preSaleState',
          label: '授权状态',
          render: (h, row) => {
            return (
              <span style={{ color: '#3562DB', cursor: 'pointer' }} onClick={() => this.openPreSaleStateDialog(row.row)}>
                查看详情
              </span>
            )
          }
        },
        {
          width: 180,
          prop: 'operation',
          label: '操作',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span v-auth={'attendanceTerminalManage:detail'} class="operationBtn-span" onClick={() => this.control('detail', row.row)}>
                  查看
                </span>
                <span v-auth={'attendanceTerminalManage:edit'} class="operationBtn-span" onClick={() => this.control('edit', row.row)}>
                  编辑
                </span>
                <span v-auth={'attendanceTerminalManage:del'} class="operationBtn-span operationBtn-del" onClick={() => this.control('del', row.row)}>
                  删除
                </span>
              </div>
            )
          }
        }
      ],
      tableData: [],
      pageData: {
        current: 1,
        size: 15,
        total: 0
      },
      pageProps: {
        page: 'current',
        pageSize: 'size',
        total: 'total'
      }
    }
  },
  watch: {},
  activated() {
    this.searchFormEvent()
  },
  mounted() {
    this.searchFormEvent()
  },
  methods: {
    // 查询
    searchFormEvent() {
      this.getPlanTemplate()
    },
    // 重置查询
    resetForm() {
      Object.assign(this.$data.searchForm, this.$options.data().searchForm)
      this.searchFormEvent()
    },
    control(type, row) {
      if (['add', 'edit', 'detail'].includes(type)) {
        // 添加 编辑 复制
        let query = type !== 'add' ? { id: row?.id ?? '', sn: row?.sn ?? '' } : {}
        this.$router.push({
          path: '/shiftManage/attendanceTerminalManage/addAttendanceTerminalManage',
          query: {
            type,
            ...query
          }
        })
      } else if (type == 'del') {
        // 删除
        this.$confirm('确认要删除当前考勤终端吗？删除后数据不可恢复。', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '删除',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$api.supplierAssess.removeAttendanceTerminal({ id: row.id }, { 'operation-type': 3, 'operation-id': row.id, 'operation-name': row.name }).then((res) => {
            if (res.code == 200 && res.data) {
              this.$message({ message: '删除成功', type: 'success' })
              this.isLastPage(1)
              this.searchFormEvent()
            } else {
              this.$message({ message: res.msg, type: 'error' })
            }
          })
        })
      }
    },
    // 打开授权状态弹窗
    openPreSaleStateDialog(row) {
      this.selectItems = row
      this.preSaleStateDialogShow = true
    },
    // 打开管理密码配置弹窗
    managePsdConfig() {
      this.psdConfigDialogShow = true
    },
    // 获取预案模板列表
    getPlanTemplate() {
      let param = {
        ...this.searchForm,
        pageSize: this.pageData.size,
        page: this.pageData.current
      }
      this.tableLoading = true
      this.$api.supplierAssess
        .queryAttendanceTerminalByPage(param)
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.records
            this.pageData.total = res.data.total
          }
          this.tableLoading = false
        })
        .catch(() => {
          this.tableLoading = false
        })
    },
    // 判断当前页是否是最后一页
    isLastPage(deleteNum) {
      let deleteAfterPage = Math.ceil((this.pageData.total - deleteNum) / this.pageData.size)
      let currentPage = this.pageData.current > deleteAfterPage ? deleteAfterPage : this.pageData.current
      this.pageData.current = currentPage < 1 ? 1 : currentPage
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getPlanTemplate()
    }
  }
}
</script>
<style lang="scss" scoped>
.control-btn-header {
  padding: 0px 10px 10px 10px !important;
  .search-from {
    padding-right: 180px;
    position: relative;
    & > div {
      margin-top: 10px;
      margin-right: 10px;
    }
  }
  .event-btn {
    padding-top: 10px;
  }
}
.table-content {
  margin-top: 15px;
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 15px);
  padding: 10px;
}
</style>
