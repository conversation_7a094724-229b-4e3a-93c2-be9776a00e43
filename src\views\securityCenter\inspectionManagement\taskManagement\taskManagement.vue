<template>
  <div style="height: 100%;">
    <!-- <sinoPanel title="任务管理" type="list" :isClose="false" style="background-color: rgb(245, 246, 251);">
      <template slot="content">
        <div :style="{ display: from == 'taskAnlysis' ? 'block' : 'none' }" class="backBar" @click="goAnalysis">
          <span class="icon el-icon-arrow-left"></span>
          <span class="header-title">巡检统计</span>
        </div>
      </template>
    </sinoPanel> -->
    <div class="special_box">
      <div class="left">
        <div class="leftCon">
          <div class="toptip">
            <span class="green_line"></span>
            计划类型
          </div>
          <template>
            <div class="tabsBox">
              <el-tree
                ref="tree"
                :data="dictionaryArr"
                :props="treeProps"
                node-key="id"
                :highlight-current="true"
                :check-strictly="true"
                @node-click="handleClick"
              >
              </el-tree>
            </div>
          </template>
        </div>
      </div>
      <div class="content_box">
        <div class="top_content">
          <div style="display: flex; align-items: center; margin-bottom: 10px;">
            <el-input
              v-model="searchDataObj.planName"
              style="width: 155px;"
              placeholder="请输入计划名称"
              maxlength="25"
              onkeyup="if(value.length>25)value=value.slice(0,25)"
              @keyup.enter.native="_searchByCondition"
            ></el-input>
            <el-input
              v-model="searchDataObj.departmentName"
              style="width: 155px; margin: 0 10px;"
              placeholder="请输入小组"
              maxlength="25"
              onkeyup="if(value.length>25)value=value.slice(0,25)"
              @keyup.enter.native="_searchByCondition"
            ></el-input>
            <el-input
              v-model="searchDataObj.planPersonName"
              style="width: 155px;"
              placeholder="请输入巡检人员"
              maxlength="25"
              onkeyup="if(value.length>25)value=value.slice(0,25)"
              @keyup.enter.native="_searchByCondition"
            ></el-input>
            <el-select v-model="searchDataObj.status" style="width: 155px; margin: 0 10px;" filterable placeholder="请输入任务状态">
              <el-option v-for="(item, index) in statusList" :key="index" :label="item.label" :value="item.id"> </el-option>
            </el-select>
            <el-date-picker
              v-model="searchDataObj.timeLine"
              style="width: 355px; margin-right: 10px;"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              align="right"
            >
            </el-date-picker>
            <el-button type="primary" plain @click="_resetCondition">重置</el-button>
            <el-button type="primary" @click="_searchByCondition">查询</el-button>
          </div>
        </div>
        <el-button icon="el-icon-download" type="primary" style="margin-bottom: 20px; width: 115px;" @click="exportExcel">导出Excel</el-button>
        <div class="table_list" style="text-align: right;">
          <el-table
            v-loading="tableLoading"
            :data="tableData"
            border
            height="calc(100% - 10px)"
            style="width: 100%;"
            :cell-style="{ padding: '8px' }"
            stripe
            :header-cell-style="{ background: '#f2f4fbd1' }"
            :empty-text="emptyText"
            highlight-current-row
            @selection-change="handleSelectionChange"
          >
            <el-table-column label="序号" type="index" width="80" align="center">
              <template slot-scope="scope">
                <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="taskName" show-overflow-tooltip label="任务名称"></el-table-column>
            <el-table-column align="center" prop="planTypeName" show-overflow-tooltip label="计划类型"></el-table-column>
            <el-table-column align="center" prop="planName" show-overflow-tooltip label="计划名称"></el-table-column>
            <el-table-column align="center" prop show-overflow-tooltip label="周期类型" width="80">
              <template slot-scope="scope">
                <span>{{ cycleTypeFn(scope.row) }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" prop show-overflow-tooltip label="应巡日期">
              <template slot-scope="scope">
                <span>{{ scope.row.taskStartTime + '-' + scope.row.taskEndTime }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" show-overflow-tooltip label="巡检小组/人员">
              <template slot-scope="scope" :class="scope.row">
                <span>{{ scope.row.planPersonName || filterDepartment(scope.row) }}</span>
              </template>
            </el-table-column>
            <!-- <el-table-column
        show-overflow-tooltip
        label="巡检人员"
        >
        <template slot-scope="scope" :class="scope.row">
          <span>{{ scope.row.planPersonName || scope.row.distributionTeamName}}</span>
        </template>
        </el-table-column> -->
            <el-table-column align="center" prop="totalCount" show-overflow-tooltip label="应巡点数" width="80"></el-table-column>
            <el-table-column align="center" prop="hasCount" show-overflow-tooltip label="实巡点数" width="80"></el-table-column>
            <el-table-column align="center" prop="anomalyCount" show-overflow-tooltip label="异常点数" width="80"></el-table-column>
            <el-table-column align="center" show-overflow-tooltip label="完成状态">
              <template slot-scope="scope" :class="scope.row">
                <span
                  class="table_using"
                  :class="{
                    font_color: scope.row.taskStatus == '1'
                  }"
                >{{ scope.row.taskStatus == '1' ? '未完成' : '已完成' }}</span
                >
              </template>
            </el-table-column>
            <el-table-column align="center" prop="upDepartName" show-overflow-tooltip label="操作" width="80">
              <template slot-scope="scope">
                <el-link type="primary" :underline="false" @click="viewDetails(scope.row)">详情</el-link>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="" style="padding-top: 10px; text-align: right;">
          <el-pagination
            :current-page="paginationData.currentPage"
            :page-sizes="[15, 30, 50, 100]"
            :page-size="paginationData.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="paginationData.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import moment from 'moment'
import axios from 'axios'
export default {
  name: 'taskManagement',
  components: {},
  data() {
    return {
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      LOGINDATA: '',
      statusList: [
        {
          id: '2',
          label: '已完成'
        },
        {
          id: '1',
          label: '未完成'
        },
        {
          id: '0',
          label: '全部'
        }
      ],
      searchDataObj: {
        planName: '',
        planPersonName: '',
        departmentName: '',
        // cycleType:"",
        status: '0',
        timeLine: [],
        startTime: '',
        endTime: ''
      },
      treeProps: {
        children: 'children',
        label: 'dictName',
        isLeaf: 'leaf'
      },
      tableData: [],
      dictionaryArr: [], // 计划类型
      emptyText: '暂无数据',
      tableLoading: false,
      pageSize: '',
      currentPage: '',
      listRole: '',
      userIds: '',
      activeType: [],
      tableClickArry: [],
      dialogVisible: false,
      searchFrom: {},
      cycleTypeArr: [
        {
          cycleType: 8,
          label: '单次'
        },
        {
          cycleType: 6,
          label: '每日'
        },
        {
          cycleType: 0,
          label: '每周'
        },
        {
          cycleType: 2,
          label: '每月'
        },
        {
          cycleType: 3,
          label: '季度'
        },
        {
          cycleType: 5,
          label: '全年'
        }
      ],
      from: ''
    }
  },
  created() {
    // 获取登录信息
    this.LOGINDATA = JSON.parse(sessionStorage.getItem('LOGINDATA'))
    // 获取计划类型
    this._findDictionaryTableList()
    if (this.$route.query.from && this.$route.query.from == 'taskAnlysis') {
      this.from = this.$route.query.from
      this.searchDataObj.departmentName = this.$route.query.TeamName
      console.log(this.$route.query.status)
      this.searchDataObj.status = this.$route.query.status
      if (JSON.parse(this.$route.query.filters).timeIterval.length > 0) {
        const timeIterval = [JSON.parse(this.$route.query.filters).timeIterval[0] + ' 00:00:00', JSON.parse(this.$route.query.filters).timeIterval[1] + ' 23:59:59']
        this.searchDataObj.timeLine = timeIterval
      }
      console.log('1321', this.searchDataObj)
    }
  },
  mounted() {
    // 获取任务管理列表
    // this._findTaskList();
  },
  methods: {
    // 获取字典列表
    _findDictionaryTableList() {
      let data = {
        dictCode: '',
        dictName: '',
        pageNo: 1,
        pageSize: 99999,
        dictType: 'plan_type'
      }
      this.$api.findDictionaryTableList(data).then((res) => {
        const { code, data, message } = res
        if (code == 200) {
          this.dictionaryArr = data.list
          this.activeType = this.dictionaryArr[0].id // 默认选中第一条
          this.dictionaryArr[0].avtiveItem = true
          this.$nextTick(() => {
            // 获取计划列表
            this._findTaskList(this.activeType)
            this.$refs.tree.setCurrentKey(this.activeType)
          })
        } else {
          this.$message.error(message)
        }
      })
    },

    // 查询表格
    _findTaskList(type) {
      this.tableLoading = true
      let obj = this.LOGINDATA
      const { paginationData, searchDataObj } = this
      const taskStartTime = searchDataObj.timeLine.length > 0 ? moment(searchDataObj.timeLine[0]).format('YYYY-MM-DD HH:mm:ss') : ''
      const taskEndTime = searchDataObj.timeLine.length > 0 ? moment(searchDataObj.timeLine[1]).format('YYYY-MM-DD HH:mm:ss') : ''
      let data = {
        planTypeId: type,
        planName: searchDataObj.planName,
        planPersonName: searchDataObj.planPersonName,
        taskStatus: searchDataObj.status == '0' ? '' : searchDataObj.status, // 状态
        cycleType: searchDataObj.cycleType,
        pageNo: paginationData.currentPage,
        pageSize: paginationData.pageSize,
        departmentName: searchDataObj.departmentName,
        taskStartTime,
        taskEndTime
      }
      // this.$api.findTaskList(data).then(res => {
      this.$api.findPlanSchedule(data).then((res) => {
        this.tableLoading = false
        if (res.code == 200) {
          this.tableData = res.data.list
          this.paginationData.total = parseInt(res.data.sum)
          if (res.data.list.length == 0) {
            this.emptyText = '未查到任何记录，请放大查询条件试试！'
          }
        }
      })
    },
    // 点击切换计划类型
    handleClick(val) {
      this.activeType = val.id
      if (this.dictionaryArr.length) {
        this.dictionaryArr.forEach((item, index) => {
          if (item.id == val.id) {
            item.avtiveItem = true
          } else {
            item.avtiveItem = false
          }
        })
      }
      this._findTaskList(this.activeType)
    },

    // 重置查询条件
    _resetCondition() {
      this.paginationData = {
        currentPage: 1,
        pageSize: 15,
        total: 0
      }
      this.searchDataObj = {
        planName: '',
        planPersonName: '',
        departmentName: '',
        cycleType: '',
        status: '1',
        timeLine: []
      }
      this._findTaskList(this.activeType)
    },
    // 条件查询
    _searchByCondition() {
      console.log(this.searchDataObj)
      this.paginationData.currentPage = 1
      this._findTaskList(this.activeType)
    },
    // 查看详情
    viewDetails(row) {
      this.$router.push({
        path: 'newTaskManagement/scheduleDetails',
        query: {
          id: row.id,
          status: row.taskStatus,
          startTime: row.executeStartTime || '',
          endTime: row.executeEndTime || '',
          taskName: row.taskName,
          cycleType: row.cycleType,
          executeStartTime: row.executeStartTime || '',
          executeEndTime: row.executeEndTime,
          distributionTeamName: this.filterDepartment(row),
          cycleRole: row.cycleRole,
          planPersonName: row.planPersonName
        }
      })
    },
    /**
     * 点击table表格
     */
    handleSelectionChange(val) {
      this.tableClickArry = val
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this._findTaskList(this.activeType)
    },
    // 每页的条数
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.paginationData.currentPage = 1
      this._findTaskList(this.activeType)
    },
    // 周期类型
    cycleTypeFn(row) {
      const item = this.cycleTypeArr.filter((i) => i.cycleType == row.cycleType)
      return item[0].label
    },
    // 科室去重
    filterDepartment(department) {
      const nameArr = department.distributionTeamName.split(',')
      const teamName = Array.from(new Set(nameArr))
      return teamName.toString()
    },
    // 导出
    exportExcel() {
      const LOGINDATA = JSON.parse(sessionStorage.getItem('LOGINDATA'))
      const { paginationData, searchDataObj } = this
      const taskStartTime = searchDataObj.timeLine.length > 0 ? moment(searchDataObj.timeLine[0]).format('YYYY-MM-DD HH:mm:ss') : ''
      const taskEndTime = searchDataObj.timeLine.length > 0 ? moment(searchDataObj.timeLine[1]).format('YYYY-MM-DD HH:mm:ss') : ''
      let params = {
        unitCode: LOGINDATA.unitCode,
        hospitalCode: LOGINDATA.hospitalCode,
        hospitalName: LOGINDATA.hospitalName,
        userId: LOGINDATA.id,
        staffId: LOGINDATA.id,
        userName: LOGINDATA.name,
        positionType: LOGINDATA.positionType,
        unitName: LOGINDATA.unitName,
        roleCode: LOGINDATA.roleCode,
        userTeamId: LOGINDATA.controlGroupIds,
        controlGroupIds: LOGINDATA.controlGroupIds,
        platformFlag: 1,
        planTypeId: this.tableData[0].planTypeId,
        planName: searchDataObj.planName,
        planPersonName: searchDataObj.planPersonName,
        taskStatus: searchDataObj.status == '0' ? '' : searchDataObj.status, // 状态
        pageNo: paginationData.currentPage,
        pageSize: paginationData.pageSize,
        departmentName: searchDataObj.departmentName,
        taskStartTime,
        taskEndTime
      }
      const formData = new FormData()
      Object.keys(params).forEach((key) => {
        formData.append(key, params[key])
      })
      axios({
        method: 'post',
        url: __PATH.VUE_AQ_URL + 'planTaskNew/taskExport',
        data: formData,
        responseType: 'blob',
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      })
        .then((res) => {
          let name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
          const blob = new Blob([res.data])
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
        })
        .catch((err) => {
          console.log(err)
          this.$message.error(res.message || '导出失败')
        })
    },
    // 返回数据统计
    goAnalysis() {
      this.$router.push({
        path: '/dataStatisticsList',
        query: {
          from: 'task'
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: linear-gradient(to right, #d9e1f8, #fff);
}

::v-deep .el-tree-node__content {
  height: 32px;
}

.leftCon {
  overflow-y: auto;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tabsBox {
  text-align: center;
  margin-top: 20px;
  margin-bottom: 30px;
  // max-height:650px;
  overflow-y: auto;
  flex: 1;
}

.backBar {
  height: 20px;
}

.backBar:hover {
  cursor: pointer;
}

.tabsItem {
  padding: 8px 0;
  width: 200px;
  margin: 0 auto;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #5b5b5b;
  cursor: pointer;

  .item {
    width: 200px !important;
  }

  >span {
    width: 200px !important;
    margin: 0 auto !important;
  }
}

.taskManagement {
  :deep(.el-dialog) {
    width: 720px;
    height: 455px;
  }
}

.sino-filter-col {
  display: flex;
  justify-content: space-between;

  .el-form-item {
    display: flex;

    :deep(.el-form-item__label) {
      width: 96px !important;
    }
  }

  .input_box {
    width: 200px;
  }
}

.tabsItem:hover {
  color: #5188fc;
}

:deep(.el-tabs__nav-wrap.is-left::after) {
  height: 0 !important;
}

:deep(.el-tabs__active-bar) {
  height: 0 !important;
}

:deep(.el-tabs__nav) {
  display: flex;
  flex-direction: column;
  align-items: center;
}

:deep(.el-tabs__header) {
  float: none !important;
}

:deep(.is-active) {
  background: #eee;
  border-radius: 4px;
  width: 180px;
  height: 35px;
  line-height: 35px;
  text-align: center !important;
}

:deep(.el-tabs__item.is-active) {
  color: #5188fc;
}

:deep(.el-tabs__item:hover) {
  color: #5188fc;
}

.content {
  display: flex;
}

.special_box {
  height: 100%;
  display: flex;
  padding: 10px;
  box-sizing: border-box;
}

.content_box {
  width: 79%;
  border-radius: 10px;
  padding: 20px 25px 25px;
  background: #fff;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.top_content {
  width: 100%;
  display: flex;
  flex-direction: column;
  // margin-bottom:20px;
}

.table_list {
  flex: 1;
}

.el-input {
  width: 200px;
}

.viewButton {
  color: #5188fc;
  cursor: pointer;
}

.green_line {
  display: inline-block;
  width: 4px;
  height: 16px;
  background: #5188fc;
  margin-right: 10px;
  vertical-align: middle;
}

.toptip {
  box-sizing: border-box;
  padding-left: 26px;
  height: 50px;
  width: 100%;
  line-height: 50px;
  text-align: left;
  border-bottom: 1px solid rgb(216 222 231 / 100%);
}

.left_content {
  margin: 20px 20px 0 35px;
}

.topTitle {
  display: flex;
  align-items: center;
  padding-bottom: 20px;
  border-bottom: 1px solid #ddd;
}

.line {
  width: 4px;
  height: 16px;
  background-color: #5188fc;
  margin-right: 10px;
}

.left {
  width: 268px;
  min-width: 14%;
  // height: 97%;
  border-radius: 10px;
  background-color: #fff;
  // margin: 10px;
  // margin-top: 10px;
  margin-right: 10px;

  .middle_tools {
    margin-top: 10px !important;
  }
}

.right {
  position: relative;
  width: calc(100% - 300px);
  // flex: 1;
  // width: 56%;
  height: 97%;
  text-align: left;
  background-color: #fff;
  padding-left: 30%;
  // float: left;
  box-sizing: border-box;
  padding: 20px 10px 10px;
  margin: 10px 10px 10px 0;
  margin-left: 288px;
  border-radius: 10px;

  .top_filters {
    input,
    select {
      margin-right: 15px;
      width: 200px;
      height: 40px;
    }

    .search_button {
      background-color: #5188fc;
      color: #fff;
    }

    .search_button:hover {
      opacity: 0.7;
    }
  }

  .middle_tools {
    // margin-top: 20px;
    margin-bottom: 10px;
  }
}

.deleteButton {
  color: #f43530;
  cursor: pointer;
}

.font_color {
  color: red;
}

@media screen and (max-width: 1600px) {
  .personDialog .el-dialog {
    height: 545px;
  }

  .toptip {
    height: 35px;
    line-height: 35px;
  }

  .left_content {
    height: 415px;
  }

  .left .middle_tools {
    line-height: 36px;
    box-shadow: 0 -4px 4px 0 #f3f4f8;
    box-shadow: 0 -4px 4px 0 #f3f4f8;
  }
}
</style>