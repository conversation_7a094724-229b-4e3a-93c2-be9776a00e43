<template>
  <PageContainer>
    <div slot="header">
      <div class="search-header">
        <div class="search-from">
          <el-input v-model.trim="formInline.compound" placeholder="科室/班组名称" style="width: 200px;"></el-input>
          <el-input
            v-model.number="formInline.startMoney"
            placeholder="开始区间"
            style="margin-left: 8px; width: 200px;"
            oninput="value=value.replace(/[^\d.-]/g,'')"
            @input="handleInputMin"
          ></el-input>
          <span>至</span>
          <el-input
            v-model.number="formInline.endMoney"
            placeholder="结束区间"
            style="margin-left: 8px; width: 200px;"
            oninput="value=value.replace(/[^\d.-]/g,'')"
            @input="handleInput"
          ></el-input>
          <el-button type="primary" @click="reset">重置</el-button>
          <el-button type="primary" @click="searchClick()">查询</el-button>
        </div>
        <div>
          <el-button style="margin-right: 32px;" type="primary" icon="el-icon-upload2" @click="exportClickExport">导出</el-button>
          <el-button v-for="item of dateList" :key="item.id" type="primary" :class="state != item.id ? 'disabled-button' : 'sure'" @click="switchingTime(item.id)">{{
            item.name
          }}</el-button>
          <el-date-picker
            v-if="state == 5"
            v-model="formInline.dealTimelist"
            :editable="false"
            :clearable="false"
            style="margin-left: 10px;"
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="handleTime"
          ></el-date-picker>
        </div>
      </div>
    </div>
    <div slot="content" style="height: 100%; margin-top: 16px; background-color: #fff;">
      <div class="table-content">
        <el-table v-loading="tableLoading" stripe :data="purchaseTable" :border="true" :height="tableHeight" @selection-change="handleSelectionChange">
          <!-- <template slot="empty">
            <p class="tableEmptyLabel">{{ $store.state.tableLabel }}</p>
          </template> -->
          <el-table-column type="selection" width="55" fixed="left" align="center"></el-table-column>
          <el-table-column type="index" label="序号" width="65">
            <template slot-scope="scope">
              <span>{{ (paginationData.currentPage - 1) * 20 + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="teamName" label="科室/班组名称" show-overflow-tooltip width="180"></el-table-column>
          <el-table-column prop="userName" label="姓名" show-overflow-tooltip></el-table-column>
          <el-table-column prop="riskCount" label="发现隐患(随手拍)" show-overflow-tooltip></el-table-column>
          <el-table-column prop="riskMoney" :label="titleList[0].label || '发现奖励'" show-overflow-tooltip>
            <template slot-scope="scope">
              <span :class="titleList[0].rewardsPunishmentsStatus == 1 ? 'green' : titleList[0].rewardsPunishmentsStatus == 2 ? 'red' : 'green'"
              >{{ Math.abs(scope.row.riskMoney) }}元</span
              >
            </template>
          </el-table-column>
          <el-table-column prop="completeCount" show-overflow-tooltip label="整改完工"></el-table-column>
          <el-table-column prop="completeMoney" show-overflow-tooltip :label="titleList[1].label || '完工奖励'">
            <template slot-scope="scope">
              <span :class="titleList[1].rewardsPunishmentsStatus == 1 ? 'green' : titleList[1].rewardsPunishmentsStatus == 2 ? 'red' : 'green'"
              >{{ Math.abs(scope.row.completeMoney) }}元</span
              >
            </template>
          </el-table-column>
          <el-table-column prop="hangCount" show-overflow-tooltip label="挂单数量"></el-table-column>
          <el-table-column prop="hangMoney" show-overflow-tooltip :label="titleList[2].label || '挂单惩罚'">
            <template slot-scope="scope">
              <span :class="titleList[2].rewardsPunishmentsStatus == 1 ? 'green' : titleList[2].rewardsPunishmentsStatus == 2 ? 'red' : 'red'"
              >{{ Math.abs(scope.row.hangMoney) }}元</span
              >
            </template>
          </el-table-column>
          <el-table-column prop="cancelCount" show-overflow-tooltip label="取消工单"></el-table-column>
          <el-table-column prop="cancelMoney" show-overflow-tooltip :label="titleList[3].label || '取消惩罚'">
            <template slot-scope="scope">
              <span :class="titleList[3].rewardsPunishmentsStatus == 1 ? 'green' : titleList[3].rewardsPunishmentsStatus == 2 ? 'red' : 'red'"
              >{{ Math.abs(scope.row.cancelMoney) }}元</span
              >
            </template>
          </el-table-column>
          <!-- <el-table-column
                prop="patrolCount"
                show-overflow-tooltip
                label="巡检点数"
              ></el-table-column>
              <el-table-column prop="patrolMoney" show-overflow-tooltip :label="titleList[4].label || '巡检奖励'">
                <template slot-scope="scope">
                  <span :class="titleList[4].rewardsPunishmentsStatus == 1?'green':titleList[4].rewardsPunishmentsStatus == 2?'red':'green'">{{  Math.abs(scope.row.patrolMoney) }}元</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="investigationCount"
                show-overflow-tooltip
                label="排查点数"
              ></el-table-column>
              <el-table-column prop="investigationMoney" show-overflow-tooltip :label="titleList[5].label || '排查奖励'">
                <template slot-scope="scope">
                  <span :class="titleList[5].rewardsPunishmentsStatus == 1?'green':titleList[5].rewardsPunishmentsStatus == 2?'red':'green'">{{  Math.abs(scope.row.investigationMoney) }}元</span>
                </template>
              </el-table-column> -->
          <el-table-column prop="moneyCount" show-overflow-tooltip label="总奖惩">
            <template slot-scope="scope" style="cursor: pointer;">
              <span :class="scope.row.moneyCount >= 0 ? 'green' : 'red'" style="text-decoration: underline; cursor: pointer;" @click="dblclick(scope.row)"
              >{{ scope.row.moneyCount }}元</span
              >
              <!-- <span @click="dblclick(scope.row)" v-if="scope.row.moneyCount >= 0" style="color: #0acbc3;text-decoration: underline;cursor:pointer"
                    >{{ scope.row.moneyCount }}元</span
                  > -->
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :current-page="paginationData.currentPage"
          :page-sizes="[15, 30, 50, 100]"
          :page-size="paginationData.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="paginationData.total"
          @size-change="firstSizeNumChange"
          @current-change="firstCurrentPageChange"
        ></el-pagination>
      </div>
      <exportDialog
        ref="dialog-export"
        :exportType="exportType"
        :materRows="multipleSelection"
        :formInline="formInline"
        :dialogVisibleExport="dialogVisibleExport"
        @closeDialog="closeDialog"
      ></exportDialog>
    </div>
  </PageContainer>
</template>

<script>
import exportDialog from '@/components/recordExport/recordExport.vue'  // 导出 
export default {
  name: 'punishmentSystemList',
  components: { exportDialog},
  data() {
    return {
      dialogVisibleExport: false,
      exportType: 5,
      formInline: {
        compound: '',
        startMoney: '',
        endMoney: '',
        dealTimelist: [], // 时间
        beginTime: '',
        endTime: '',
        dateType: ''
      },
      dateList: [
        {
          name: '今日',
          id: 1
        },
        {
          name: '本周',
          id: 2
        },
        {
          name: '本月',
          id: 3
        },
        {
          name: '本年',
          id: 4
        },
        {
          name: '自定义',
          id: 5
        }
      ],
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      purchaseTable: [],
      titleList: [
        {
          rewardsPunishmentsStatus: '1',
          label: '发现奖励'
        },
        {
          rewardsPunishmentsStatus: '1',
          label: '完工奖励'
        },
        {
          rewardsPunishmentsStatus: '2',
          label: '挂单惩罚'
        },
        {
          rewardsPunishmentsStatus: '2',
          label: '取消惩罚'
        },
        {
          rewardsPunishmentsStatus: '1',
          label: '巡检奖励'
        },
        {
          rewardsPunishmentsStatus: '1',
          label: '排查奖励'
        }
      ],
      tableLoading: false,
      propsType: {
        children: 'children',
        label: 'dictLabel',
        value: 'dictValue',
        checkStrictly: true
      },
      props: {
        label: 'gridName',
        value: 'id',
        children: 'children',
        checkStrictly: true
      },
      multipleSelection: [],
      dialogVisible: false,
      state: 3
    }
  },
  computed: {
    tableHeight() {
      return document.body.clientHeight - 260
    }
  },
  mounted() {
    this.requestType()
    this.requestList()
  },
  methods: {
    handleInputMin(value) {
      if (value != '') {
        if (value.indexOf('.') > -1) {
          this.formInline.startMoney = value.slice(0, value.indexOf('.') + 3)
        } else {
          this.formInline.startMoney = value
        }
      }
    },
    handleInput(value) {
      if (value != '') {
        if (value.indexOf('.') > -1) {
          this.formInline.endMoney = value.slice(0, value.indexOf('.') + 3)
        } else {
          this.formInline.endMoney = value
        }
      }
    },
    switchingTime(id) {
      if (id != this.state) {
        this.state = id
        this.formInline.dealTimelist = []
        this.formInline.beginTime = ''
        this.formInline.endTime = ''
        if (this.state == 5) {
          let date = new Date()
          this.formInline.dealTimelist = [date, date]
          this.formInline.beginTime = this.$tools.getCurrentDate(date)
          this.formInline.endTime = this.$tools.getCurrentDate(date)
        }
        this.requestList()
      }
    },
    handleTime(val) {
      this.formInline.beginTime = val[0]
      this.formInline.endTime = val[1]
    },
    exportClickExport() {
      this.formInline.dateType = this.state
      this.dialogVisibleExport = true
    },
    // eslint-disable-next-line vue/no-dupe-keys
    closeDialog() {
      this.dialogVisibleExport = false
    },
    // 勾选
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    searchClick() {
      //   this.$store.commit('changeImasTableLabel')
      //   this.$store.commit('changeNetworkError', '查询失败，请检查您的网络…')
      this.paginationData.currentPage = 1
      this.requestList()
    },
    // 详情
    dblclick(row) {
      //   this.$store.commit('amountDetailsRow', row)
      // this.$router.push({
      //   path: "/amountDetails",
      //   query: {},
      // });
      this.dialogVisible = true
    },
    closeDialogList() {
      this.dialogVisible = false
    },
    // 获取列表
    requestList() {
      if (this.formInline.startMoney && this.formInline.endMoney) {
        if (JSON.parse(this.formInline.startMoney) > JSON.parse(this.formInline.endMoney)) {
          return this.$message.error('开始区间不能大于结束区间')
        }
      }
      let params = {}
      this.tableLoading = true
      params = {
        ...this.paginationData,
        ...this.formInline
      }
      params.dateType = this.state
      params.ids = ''
      params.roleCode = JSON.parse(sessionStorage.getItem('LOGINDATA')).roleCode,
      params.controlGroupIds = JSON.parse(sessionStorage.getItem('LOGINDATA')).controlGroupIds
      delete params.total
      delete params.dealTimelist
      this.$api
        .ipsmUserPenaltiesList(params)
        .then((res) => {
          if (res.code == '200') {
            this.purchaseTable = res.data.list
            this.paginationData.total = res.data.sum * 1
          } else if (res.message) {
            this.$message.error(res.message)
          }
          this.tableLoading = false
        })
        .catch(() => {
          this.tableLoading = false
        })
    },
    reset() {
      //   this.$store.commit('changeImasTableLabel', 'init')
      //   this.$store.commit('changeNetworkError', '查询失败，请检查您的网络…')
      this.formInline.compound = ''
      this.formInline.startMoney = ''
      this.formInline.endMoney = ''
      this.formInline.dealTimelist = []
      this.formInline.beginTime = ''
      this.formInline.endTime = ''
      this.state = 3
      this.searchClick()
    },
    // 页面字典项
    requestType() {
      this.$api.ipsmGetColumnName({}).then((res) => {
        if (res.code == 200) {
          this.titleList = res.data
        } else {
          this.$message.error(res.message)
        }
      })
    },

    // 分页
    firstSizeNumChange(val) {
      this.paginationData.currentPage = 1
      this.paginationData.pageSize = val
      this.requestList()
    },
    firstCurrentPageChange(val) {
      this.paginationData.currentPage = val
      this.requestList()
    }
  }
}
</script>

<style lang="scss" scoped>
.search-header {
  padding: 10px 16px;

  .search-from {
    padding-bottom: 12px;

    & > div {
      margin-top: 12px;
      margin-right: 10px;
    }
  }
}

.table-content {
  padding: 16px;
  height: calc(100% - 80px);
}

.bths {
  > span {
    cursor: pointer;
  }

  > span:nth-child(1) {
    color: #558bf9;
    margin-right: 5px;
  }
}

::v-deep .el-textarea__inner {
  width: 60% !important;
}

.disabled-button {
  background-color: #77affd;
  border-color: #77affd;
}

.sure:focus {
  background-color: #5288fc;
  border-color: #5288fc;
  color: #fff;
}

.red {
  color: rgb(255 0 0);
}

.green {
  color: rgb(10 203 195);
}
</style>
