<template>
  <div>
    <quill-editor
      ref="myQuillEditor"
      v-model="content"
      v-loading="uploading"
      class="editor"
      :options="editorOption"
      @blur="onEditorBlur($event)"
      @focus="onEditorFocus($event)"
      @change="onEditorChange($event)"
    ></quill-editor>
    <div style="text-align: right; margin-right: 10px; margin-bottom: -5px">{{ TiLength }}/500</div>
    <el-upload
      class="avatar-uploader"
      style="width: 0; height: 0"
      action="string"
      name="multipartFile"
      :show-file-list="false"
      accept=".jpg, .jpeg, .png, .JPG, .JPEG, .bmp"
      :before-upload="beforeUpload"
      :http-request="httpRequest"
    ></el-upload>
    <el-upload
      class="video-uploader"
      style="width: 0; height: 0"
      action="string"
      name="multipartFile"
      :show-file-list="false"
      accept=".mp4, .MP4"
      :before-upload="beforeVideoUpload"
      :http-request="videoHttpRequest"
    ></el-upload>
  </div>
</template>
<script>
// 工具栏配置
const toolbarOptions = [
  ['bold', 'italic'], // 加粗 斜体
  [{ header: 1 }, { header: 2 }], // 1、2级标题
  [{ list: 'ordered' }, { list: 'bullet' }], // 有序、无序列表
  [{ size: ['small', false, 'large', 'huge'] }], // 字体大小
  [{ header: [1, 2, 3, 4, 5, 6, false] }], // 标题
  [{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色
  [{ align: [] }], // 对齐方式
  ['clean'], // 清除文本格式
  ['image', 'video'] // 图片、视频
]
import { quillEditor } from 'vue-quill-editor'
import Quill from 'quill'
import axios from 'axios'
// 自定义视频格式
class VideoBlot extends Quill.import('formats/video') {
  static create(value) {
    const node = super.create(value)
    node.setAttribute('controls', 'controls')
    node.setAttribute('preload', 'auto')
    node.setAttribute('src', value)
    node.setAttribute('class', 'ql-video')
    return node
  }
}
// 注册自定义视频格式
Quill.register('formats/video', VideoBlot, true)
export default {
  name: 'editor',
  components: {
    quillEditor
  },
  props: {
    /* 编辑器的内容*/
    value: {
      type: String
    },
    /* 图片大小*/
    maxSize: {
      type: Number,
      default: 4000 // kb
    }
  },
  data() {
    return {
      baseUrl: '',
      uploading: false, // 上传loading动画
      TiLength: 0,
      content: this.value,
      quillUpdateImg: false, // 根据图片上传状态来确定是否显示loading动画，刚开始是false,不显示
      editorOption: {
        theme: 'snow', // or 'bubble'
        placeholder: '请输入内容...',
        modules: {
          toolbar: {
            container: toolbarOptions, // 工具栏
            handlers: {
              image: function (value) {
                if (value) {
                  document.querySelector('.avatar-uploader input').click()
                } else {
                  this.quill.format('image', false)
                }
              },
              video: function (value) {
                if (value) {
                  document.querySelector('.video-uploader input').click()
                } else {
                  this.quill.format('video', false)
                }
              }
            }
          }
        }
      }
    }
  },
  watch: {
    // 监听内容变化
    content: {
      handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          this.$nextTick(() => {
            this.processExistingImages()
          })
        }
      },
      deep: true
    },
    // 监听props中的value变化
    value: {
      handler(newVal) {
        if (this.content !== newVal) {
          this.content = newVal
          this.$nextTick(() => {
            this.processExistingImages()
          })
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.TiLength = this.$refs.myQuillEditor.quill.getLength() - 1
    // 为quill编辑器添加自定义视频处理
    const quillEditor = this.$refs.myQuillEditor
    const quill = quillEditor.quill
    // 重新绑定视频处理器，确保this指向正确
    const toolbar = quill.getModule('toolbar')
    toolbar.addHandler('video', (value) => {
      if (value) {
        // 使用视频上传器而不是图片上传器
        document.querySelector('.video-uploader input').click()
      } else {
        quill.format('video', false)
      }
    })
    // 在内容加载后处理所有图片
    this.$nextTick(() => {
      this.processExistingImages()
    })
  },
  methods: {
    // 处理已存在的图片
    processExistingImages() {
      if (!this.$refs.myQuillEditor) return
      const quill = this.$refs.myQuillEditor.quill
      if (!quill || !quill.root) return
      // 获取编辑器中所有图片
      const images = quill.root.querySelectorAll('img')
      // 为每个图片添加类名和样式
      images.forEach((img) => {
        img.classList.add('ql-image')
        // 确保图片样式
        img.style.maxWidth = '300px'
        img.style.maxHeight = '200px'
        img.style.objectFit = 'contain'
        img.style.width = 'auto'
        img.style.height = 'auto'
      })
      // 获取并处理编辑器中所有视频元素
      const videos = quill.root.querySelectorAll('video')
      // 为每个视频添加类名和样式
      videos.forEach((video) => {
        video.classList.add('ql-video')
        // 确保视频样式
        video.style.maxWidth = '300px'
        video.style.maxHeight = '200px'
        video.style.width = 'auto'
        video.style.height = 'auto'
        video.style.display = 'inline-block'
        // 添加控制属性
        video.setAttribute('controls', 'controls')
        video.setAttribute('preload', 'metadata')
        video.setAttribute('controlsList', 'nodownload')
      })
    },
    onEditorBlur() {
      // 失去焦点事件
    },
    onEditorFocus() {
      // 获得焦点事件
    },
    onEditorChange(event) {
      if (event.quill) {
        event.quill.deleteText(500, 1)
        if (this.content === '') {
          this.TiLength = 0
        } else {
          this.TiLength = event.quill.getLength() - 1
        }
        // 处理编辑器中的图片
        this.$nextTick(() => {
          this.processExistingImages()
        })
      }
      // 内容改变事件
      this.$emit('input', this.content)
    },
    // 图片上传请求
    httpRequest(item) {
      const formData = new FormData()
      formData.append('file', item.file)
      this.uploading = true
      axios
        .post(__PATH.VUE_SYS_API + 'SysMenu/upload', formData, {
          headers: {
            Authorization: 'Bearer ' + this.$store.state.user.token
          }
        })
        .then((res) => {
          if (res.data.code == 200) {
            const quill = this.$refs.myQuillEditor.quill
            // 获取编辑器当前内容长度，将内容插入到末尾
            let length = quill.getLength() - 1
            // 如果有选择区域，则使用选择的位置
            if (quill.getSelection()) {
              length = quill.getSelection().index
            }
            // 先插入一个换行，确保内容在新行开始
            if (length > 0 && quill.getText(length - 1, 1) !== '\n') {
              quill.insertText(length, '\n')
              length += 1
            }
            const fileUrl = this.$tools.imgUrlTranslation(res.data.data)
            // 插入图片并添加类名
            quill.insertEmbed(length, 'image', fileUrl)
            // 获取刚插入的图片并添加类名
            setTimeout(() => {
              const images = quill.root.querySelectorAll('img')
              if (images.length > 0) {
                const lastImage = images[images.length - 1]
                lastImage.classList.add('ql-image')
              }
            }, 0)
            // 插入后再添加一个换行
            quill.insertText(length + 1, '\n')
            // 调整光标到最后
            quill.setSelection(length + 2)
          } else {
            this.$message.error(res.data.msg || '图片插入失败')
          }
        })
        .finally(() => {
          this.uploading = false
        })
    },
    // 视频上传请求
    videoHttpRequest(item) {
      const formData = new FormData()
      formData.append('file', item.file)
      this.uploading = true
      axios
        .post(__PATH.VUE_SYS_API + 'SysMenu/upload', formData, {
          headers: {
            Authorization: 'Bearer ' + this.$store.state.user.token
          }
        })
        .then((res) => {
          if (res.data.code == 200) {
            const quill = this.$refs.myQuillEditor.quill
            // 获取编辑器当前内容长度，将内容插入到末尾
            let length = quill.getLength() - 1
            // 如果有选择区域，则使用选择的位置
            if (quill.getSelection()) {
              length = quill.getSelection().index
            }
            // 先插入一个换行，确保内容在新行开始
            if (length > 0 && quill.getText(length - 1, 1) !== '\n') {
              quill.insertText(length, '\n')
              length += 1
            }
            const fileUrl = this.$tools.imgUrlTranslation(res.data.data)
            // 插入视频
            quill.insertEmbed(length, 'video', fileUrl)
            // 插入后再添加一个换行
            quill.insertText(length + 1, '\n')
            // 调整光标到最后
            quill.setSelection(length + 2)
          } else {
            this.$message.error(res.data.msg || '视频插入失败')
          }
        })
        .finally(() => {
          this.uploading = false
        })
    },
    // 图片上传前
    beforeUpload(file) {
      const isImage = file.type.indexOf('image/') !== -1
      const isLt5M = file.size / 1024 / 1024 < 5
      if (!isImage) {
        this.$message.error('只能上传图片文件!')
        return false
      }
      if (!isLt5M) {
        this.$message.error('上传图片大小不能超过 5MB!')
        return false
      }
      this.quillUpdateImg = true
      return true
    },
    // 视频上传前
    beforeVideoUpload(file) {
      const isVideo = file.type.indexOf('video/') !== -1
      const isLt50M = file.size / 1024 / 1024 < 50
      if (!isVideo) {
        this.$message.error('只能上传视频文件!')
        return false
      }
      if (!isLt50M) {
        this.$message.error('上传视频大小不能超过 50MB!')
        return false
      }
      this.quillUpdateImg = true
      return true
    }
  }
}
</script>
<style>
.editor em {
  font-style: italic !important;
}
.editor strong {
  font-weight: bold !important;
}
.editor {
  line-height: normal !important;
  height: 300px;
}
.ql-container {
  height: calc(100% - 12px);
}
.ql-snow .ql-tooltip[data-mode='link']::before {
  content: '请输入链接地址:';
}
.ql-snow .ql-tooltip.ql-editing a.ql-action::after {
  border-right: 0px;
  content: '保存';
  padding-right: 0px;
}
.ql-snow .ql-tooltip[data-mode='video']::before {
  content: '请输入视频地址:';
}
.ql-snow .ql-picker.ql-size .ql-picker-label::before,
.ql-snow .ql-picker.ql-size .ql-picker-item::before {
  content: '14像素';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='small']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='small']::before {
  content: '10像素';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='large']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='large']::before {
  content: '18像素';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='huge']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='huge']::before {
  content: '32像素';
}
.ql-snow .ql-picker.ql-header .ql-picker-label::before,
.ql-snow .ql-picker.ql-header .ql-picker-item::before {
  content: '文本';
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='1']::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='1']::before {
  content: '标题1';
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='2']::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='2']::before {
  content: '标题2';
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='3']::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='3']::before {
  content: '标题3';
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='4']::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='4']::before {
  content: '标题4';
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='5']::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='5']::before {
  content: '标题5';
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='6']::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='6']::before {
  content: '标题6';
}
.ql-snow .ql-picker.ql-font .ql-picker-label::before,
.ql-snow .ql-picker.ql-font .ql-picker-item::before {
  content: '标准字体';
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value='serif']::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value='serif']::before {
  content: '衬线字体';
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value='monospace']::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value='monospace']::before {
  content: '等宽字体';
}
/* 图片相关样式 */
.ql-editor .ql-image {
  display: inline-block;
  max-width: 300px;
  max-height: 200px;
  margin: 5px;
  object-fit: contain;
}
/* 确保所有编辑器中的图片都应用相同的样式 */
.ql-editor img {
  display: inline-block;
  max-width: 300px !important;
  max-height: 200px !important;
  margin: 5px;
  object-fit: contain !important;
  width: auto !important;
  height: auto !important;
}
/* 视频相关样式 */
.ql-editor .ql-video {
  display: inline-block;
  width: 300px;
  height: 200px;
  margin: 5px;
  vertical-align: top;
}
/* 确保内容按顺序显示 */
.ql-editor p {
  display: block;
  clear: both;
  margin-bottom: 10px;
}
/* 图片和视频的容器 */
.ql-editor p img,
.ql-editor p .ql-video {
  display: inline-block;
  vertical-align: top;
  margin: 5px;
}
/* 修复视频标签样式 */
.ql-editor video.ql-video {
  display: inline-block;
  width: 300px;
  height: 200px;
  margin: 5px;
  vertical-align: top;
}
</style>
