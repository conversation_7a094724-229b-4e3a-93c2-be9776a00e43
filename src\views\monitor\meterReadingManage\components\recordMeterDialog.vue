<template>
  <div>
    <el-dialog
      v-if="dialogVisible"
      v-dialogDrag
      :modal="false"
      :close-on-click-modal="false"
      title="录入抄表"
      width="40%"
      :visible.sync="dialogVisible"
      custom-class="model-dialog"
      :before-close="closeDialog"
    >
      <div class="dialog-content">
        <el-form ref="formInline" :model="queryForm" :inline="true" class="form-inline" :rules="rules" label-width="100px">
          <el-form-item label="设备名称" prop="deviceName">
            <el-input v-model.trim="queryForm.deviceName" placeholder="请输入设备名称" disabled></el-input>
          </el-form-item>
          <el-form-item label="设备编码" prop="deviceCode">
            <el-input v-model.trim="queryForm.deviceCode" placeholder="请输入设备编码" disabled> </el-input>
          </el-form-item>
          <el-form-item label="本次读数" prop="value">
            <el-input v-model="queryForm.value" placeholder="请输入本次读数"></el-input>
          </el-form-item>
          <el-form-item label="抄表时间" prop="recordTime">
            <el-date-picker v-model="queryForm.recordTime" value-format="yyyy-MM-dd HH:mm:ss" type="datetime" placeholder="选择日期时间"> </el-date-picker>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" plain @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="submitFormData">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import moment from 'moment'
export default {
  name: 'recordMeterDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    deviceData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    selectItem: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      dialogVisible: this.visible,
      queryForm: {
        deviceName: '',
        deviceCode: '',
        value: '',
        recordTime: moment().format('YYYY-MM-DD HH:mm:ss')
      },
      rules: {
        deviceName: { required: true, message: '请输入设备名称', trigger: 'change' },
        deviceCode: { required: true, message: '请输入设备编码', trigger: 'change' },
        value: { required: true, message: '请输入本次读数', trigger: 'change' },
        recordTime: { required: true, message: '请选择抄表时间', trigger: 'change' }
      }
    }
  },
  computed: {},
  mounted() {
    this.queryForm.deviceName = this.deviceData.deviceName
    this.queryForm.deviceCode = this.deviceData.deviceCode
    if (Object.keys(this.selectItem).length) {
      this.queryForm.value = this.selectItem.numericalValue
      this.queryForm.recordTime = this.selectItem.recordTime
    }
  },
  methods: {
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', false)
      this.$refs['formInline'].resetFields()
    },
    submitFormData() {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          let { value, recordTime } = this.queryForm
          let params = {
            deviceId: this.deviceData.id,
            numericalValue: Number(value),
            recordTime
          }
          let apiKey = 'meterAddRecord'
          if (Object.keys(this.selectItem).length) {
            params.id = this.selectItem.id
            apiKey = 'meterEditRecord'
          }
          this.$api[apiKey](params).then((res) => {
            if (res.code == 200) {
              this.$message.success('抄表成功')
              this.closeDialog()
            } else {
              this.$message.error(res.msg)
            }
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  background: #fff;
  padding: 10px;
  max-height: 500px;
  overflow-y: auto;
  .form-inline {
    margin-top: 24px;
    .el-input {
      width: 240px;
    }
  }
}
</style>
