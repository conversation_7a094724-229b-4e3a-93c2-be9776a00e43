<template>
  <el-dialog v-if="visible" v-dialogDrag title="岗位人员列表" width="60%" :visible.sync="dialogShow" custom-class="model-dialog" :before-close="closeDialog">
    <div class="sapce_content">
      <div class="search-from">
        <el-input v-model="searchForm.name" placeholder="搜索姓名、工号、手机号" clearable style="width: 200px"></el-input>
        <el-select v-model="searchForm.postCode" placeholder="岗位" collapse-tags multiple @change="changePost">
          <el-option v-for="item in shiftPostList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <div style="display: inline-block">
          <el-button type="primary" plain @click="resetEvent">重置</el-button>
          <el-button type="primary" @click="searchEvent">查询</el-button>
        </div>
      </div>
      <TablePage
        ref="tablePage"
        v-loading="tableLoading"
        class="tablePage"
        height="calc(100% - 80px)"
        :pageData="pageData"
        :pageProps="pageProps"
        border
        row-key="id"
        :tableColumn="tableColumn"
        :data="tableData"
        @pagination="paginationChange"
      ></TablePage>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
    </span>
  </el-dialog>
</template>
<script lang="jsx">
export default {
  name: 'peopleListByShiftPostDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    shiftPostList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      searchForm: {
        name: '',
        postCode: []
      },
      tableLoading: false,
      tableColumn: [
        {
          prop: 'staffName',
          label: '姓名'
        },
        {
          prop: 'staffNum',
          label: '工号'
        },
        {
          prop: 'sex',
          label: '性别',
          formatter: (scope) => {
            return scope.row.sex == 1 ? '男' : scope.row.sex == 0 ? '女' : ''
          }
        },
        {
          prop: 'phone',
          label: '手机号'
        },
        {
          prop: 'phoneOffice',
          label: '办公号码'
        },
        {
          prop: 'unit',
          label: '归属单位'
        }
      ],
      tableData: [],
      pageData: {
        current: 1,
        size: 15,
        total: 0
      },
      pageProps: {
        page: 'current',
        pageSize: 'size',
        total: 'total'
      }
    }
  },
  computed: {
    dialogShow() {
      return this.visible
    }
  },
  mounted() {
    this.initSelect()
    this.getDataList()
  },
  methods: {
    // 获取列表数据
    getDataList() {
      this.tableData = []
      this.tableLoading = true
      const params = {
        pageSize: this.pageData.size,
        page: this.pageData.current,
        postCode: this.searchForm.postCode.toString(),
        name: this.searchForm.name
      }
      this.$api.supplierAssess
        .queryPersonByDutyPostByPage(params)
        .then((res) => {
          this.tableLoading = false
          if (res.code === '200') {
            this.tableData = res.data.records
            this.pageData.total = res.data.total
          } else {
            this.tableData = []
            this.pageData.total = 0
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getDataList()
    },
    initSelect() {
      this.searchForm.postCode = Array.from(this.shiftPostList, ({ value }) => value)
    },
    changePost(val) {
      if (val.length == 0) {
        this.initSelect()
      }
    },
    // 表单搜索按钮点击
    searchEvent() {
      this.getDataList()
    },
    // 表单重置按钮点击
    resetEvent() {
      this.pageData.current = 1
      this.searchForm.name = ''
      this.initSelect()
      this.searchEvent()
    },
    closeDialog() {
      this.$emit('closeDialog')
    }
  }
}
</script>
<style lang="scss" scoped>
.model-dialog {
  width: 40% !important;
  .sapce_content {
    background: #fff;
    padding: 10px;
    border-radius: 4px;
    height: 400px;
    width: 100%;
    overflow: hidden;
    .search-from {
      height: 40px;
      & > div {
        margin-right: 10px;
      }
    }
  }
}
::v-deep .el-checkbox__input {
  display: inline-block !important;
}
</style>
