<script>
import HousingForm from './components/HousingForm.vue'
import logo from '@/assets/images/logo.png'
export default {
  name: 'HousingDetail',
  components: {
    HousingForm,
    BillList: () => import('./components/BillList.vue'),
    ContractList: () => import('./components/ContractList.vue'),
    HistoryList: () => import('./components/HistoryList.vue'),
    MeterRecordList: () => import('./components/MeterRecordList.vue'),
    TenantList: () => import('./components/TenantList.vue')
  },
  data: () => ({
    currentTab: 'HousingForm',
    // 编辑状态
    editable: false,
    // 房间信息
    baseData: null,
    pageLoading: false,
    housingId: '',
    spaceName: '',
    houseStateName: '',
    updateDate: '',
    houseName: '',
    houseMainPhoto: logo
  }),
  computed: {
    isFormTab() {
      return this.currentTab === 'HousingForm'
    },
    houseBaseInfo() {
      return {
        spaceName: this.spaceName ?? '',
        roomName: this.baseData?.houseName ?? '',
        price: this.baseData?.hirePrice ?? '',
        amount: this.baseData?.hireRent ?? ''
      }
    }
  },
  mounted() {
    this.housingId = this.$route.query.id
    if (this.housingId) {
      this.getRoomDetail()
    }
  },
  methods: {
    /**
     * 获取房间详情
     */
    getRoomDetail() {
      this.pageLoading = true
      this.$api.rentalHousingApi
        .getHouseById({ id: this.housingId })
        .then((res) => {
          if (res.code === '200') {
            this.baseData = res.data
            this.spaceName = this.baseData.spaceNames ?? ''
            this.houseStateName = this.baseData.houseStateName ?? ''
            this.updateDate = this.baseData.updateDate ?? ''
            const urlStr = this.baseData.housePictureUrl ?? ''
            const photos = urlStr.split(',')
            this.houseMainPhoto = logo
            for (let i = 0; i < photos.length; i++) {
              const path = photos[i]
              if (/.jpg|.jpeg|.png|.png/i.test(path)) {
                this.houseMainPhoto = this.$tools.imgUrlTranslation(path)
                break
              }
            }
            this.$timerId = setTimeout(() => {
              this.$refs.contentRef?.revertData(this.baseData)
            }, 100)
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取房间详情失败'))
        .finally(() => (this.pageLoading = false))
    },
    onTabBeforeLeave() {
      return new Promise((resolve, reject) => {
        // 如果是房间信息的编辑状态，需要询问是否保存
        if (this.isFormTab && this.editable) {
          this.$confirm('是否保存对房间信息的更改?', '保存确认')
            .then(() => {
              this.onSubmit()
              reject()
            })
            .catch(resolve)
        } else {
          resolve()
        }
      })
    },
    // 提交表单信息
    onSubmit() {
      const validateTask = this.$refs.contentRef?.validate()
      if (!validateTask) {
        this.editable = false
        return
      }
      validateTask
        .then((params) => {
          params.userId = this.$store.getters.userId // 用户id
          params.userName = this.$store.getters.userName // 用户名
          params.id = this.housingId
          this.pageLoading = true
          // do request
          return this.$api.rentalHousingApi.saveHouseInfo(params)
        })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('操作成功')
            this.editable = false
            this.getRoomDetail()
          } else {
            throw res.message || '操作失败'
          }
        })
        .catch((msg) => msg && this.$message.error(msg))
        .finally(() => (this.pageLoading = false))
    },
    resetData() {
      this.$refs.contentRef.revertData(this.baseData)
      this.editable = false
    }
  }
}
</script>
<template>
  <PageContainer v-loading="pageLoading" footer class="housing-detail">
    <template #header>
      <div class="housing-detail__nav">
        <!-- <SvgIcon name="el-icon-arrow-left" /> -->
        房间详情
      </div>
      <div class="housing-detail__header">
        <div class="housing-detail__image">
          <el-image :src="houseMainPhoto" alt="house photo" class="housing-detail__img" :preview-src-list="[houseMainPhoto]"></el-image>
        </div>
        <div class="housing-detail__info">
          <h2>{{ houseBaseInfo.roomName }}</h2>
          <el-form label-suffix="：" label-width="100px">
            <el-row>
              <el-col :span="10">
                <el-form-item label="空间位置">
                  {{ spaceName }}
                </el-form-item>
              </el-col>
              <el-col :span="14">
                <el-form-item label="状态">
                  {{ houseStateName }}
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item label="更新时间">
                  {{ updateDate }}
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
    </template>
    <template #content>
      <el-tabs v-model="currentTab" :before-leave="onTabBeforeLeave">
        <el-tab-pane label="基础信息" name="HousingForm"></el-tab-pane>
        <el-tab-pane label="租户信息" name="TenantList"></el-tab-pane>
        <el-tab-pane label="合同信息" name="ContractList"></el-tab-pane>
        <el-tab-pane label="账单信息" name="BillList"></el-tab-pane>
        <el-tab-pane label="抄表记录" name="MeterRecordList"></el-tab-pane>
        <el-tab-pane label="历史记录" name="HistoryList"></el-tab-pane>
      </el-tabs>
      <div class="housing-detail__content">
        <keep-alive>
          <component :is="currentTab" :id="housingId" ref="contentRef" :base-info="houseBaseInfo" style="padding: 0 16px 0 0" :readonly="!editable" large></component>
        </keep-alive>
      </div>
    </template>
    <template #footer>
      <template v-if="!editable">
        <el-button type="primary" plain @click="$router.back()">关闭</el-button>
        <el-button v-if="isFormTab" type="primary" @click="editable = true">编辑</el-button>
      </template>
      <template v-else>
        <el-button type="primary" plain @click="resetData">取消</el-button>
        <el-button type="primary" :loading="pageLoading" @click="onSubmit">保存</el-button>
      </template>
    </template>
  </PageContainer>
</template>
<style scoped lang="scss">
.housing-detail {
  &__nav {
    padding: 10px;
    font-size: 16px;
    border-bottom: 1px solid #eee;
  }
  &__header {
    padding: 10px;
    display: flex;
  }
  &__image {
    width: 200px;
    height: 130px;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
    overflow: hidden;
    text-align: center;
  }
  &__img {
    max-width: 100%;
    height: 100%;
  }
  &__info {
    padding-left: 20px;
    flex: 1;
    h2 {
      padding-left: 16px;
    }
    .el-form-item {
      margin-bottom: 0px;
    }
  }
  &__content {
    padding: 16px 0 16px 16px;
    height: calc(100% - 40px);
    overflow: hidden;
    .housing-form {
      height: 100%;
      overflow: auto;
    }
  }
  ::v-deep(.container-content) {
    padding-top: 2px;
    margin-top: 10px;
    background: #fff;
  }
  ::v-deep(.el-tabs__nav-scroll) {
    padding-left: 16px;
  }
}
</style>
