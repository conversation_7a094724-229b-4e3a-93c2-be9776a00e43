<template>
  <PageContainer>
    <div slot="header" class="control-btn-header">
      <div class="search-from">
        <el-input v-model.trim="searchFrom.paramName" placeholder="监测项目名称" clearable style="width: 260px"></el-input>
        <div style="display: inline-block">
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="searchForm">查询</el-button>
          <el-button type="primary" icon="el-icon-plus" @click="control('add')">新增</el-button>
        </div>
        <el-image-viewer v-if="showViewer" :on-close="() => (showViewer = false)" :url-list="iconPathList" />
      </div>
    </div>
    <div slot="content" class="table-content">
      <TablePage
        ref="table"
        v-loading="tableLoading"
        :showPage="true"
        :tableColumn="tableColumn"
        :data="tableData"
        height="calc(100% - 40px)"
        :pageData="pageData"
        :pageProps="pageProps"
        @pagination="paginationChange"
        @row-dblclick="control('detail', $event)"
      />
      <el-dialog
        v-dialogDrag
        :visible.sync="dialogVisible"
        :before-close="dialogClosed"
        :modal="false"
        :close-on-click-modal="false"
        :title="dialogTitle"
        width="40%"
        custom-class="model-dialog"
      >
        <div class="content" style="padding: 10px">
          <el-form ref="formInline" :model="formInline" :rules="rules" :inline="true" class="advanced-search-form" label-position="right" label-width="110px">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="监测项目全称：" prop="paramName">
                  <el-input
                    v-model.trim="formInline.paramName"
                    :disabled="disabled"
                    placeholder="请输入监测项目全称"
                    autocomplete="off"
                    show-word-limit
                    style="width: calc(100% - 0px)"
                  ></el-input>
                </el-form-item>
                <el-form-item label="简称：" prop="paramAlias">
                  <el-input
                    v-model.number="formInline.paramAlias"
                    :disabled="disabled"
                    placeholder="请输入简称"
                    autocomplete="off"
                    show-word-limit
                    style="width: calc(100% - 0px)"
                  ></el-input>
                </el-form-item>
                <br />
                <el-form-item label="编码：" prop="paramNo">
                  <el-input v-model.number="formInline.paramNo" :disabled="disabled" placeholder="请输入编码" autocomplete="off" style="width: calc(100% - 0px)"></el-input>
                </el-form-item>
                <br />
                <el-form-item label="单位：" prop="paramUnit">
                  <el-input v-model.number="formInline.paramUnit" :disabled="disabled" placeholder="请输入单位" autocomplete="off" style="width: calc(100% - 0px)"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="应用图标" prop="staffName" label-width="100px">
                  <span style="font-size: 10px; color: #7f848c">(请点击上传图片，分辨率120*120)</span>
                  <div style="display: flex">
                    <el-upload
                      action=""
                      :class="{ hide: iconFileList.length == 1 }"
                      list-type="picture-card"
                      :file-list="iconFileList"
                      accept=".png"
                      :limit="1"
                      :before-upload="beforeAvatarUpload"
                      :http-request="(file) => httpRequset(file, 'default')"
                      :on-remove="(file, fileList) => handleRemove(file, fileList, 'default')"
                      :on-change="(file, fileList) => fileChange(file, fileList, 'default')"
                      :disabled="disabled"
                    >
                      <i class="el-icon-circle-plus-outline" style="color: #3562db"
                      ><br /><span style="font-size: 10px; color: #7f848c">应用默认图标</span><span style="font-size: 10px; color: #7f848c">(20*20)</span></i
                      >
                    </el-upload>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="数据别名：" prop="dataAlias">
                  <el-button type="primary" icon="el-icon-plus" plain @click="addDataAlias">添加</el-button>
                  <template v-if="formInline.aliasData?.length">
                    <div v-for="(item, i) in formInline.aliasData" :key="i" class="data-alias-row">
                      <el-input v-model="formInline.aliasData[i].dataValue" :disabled="disabled" autocomplete="off" style="width: 80px"></el-input>
                      <span style="margin: auto 8px">-</span>
                      <el-input v-model="formInline.aliasData[i].dataName" :disabled="disabled" autocomplete="off" style="width: 80px"></el-input>
                      <span class="data-alias-delete" @click="deleteAlias(i)"><i class="el-icon-delete"></i>删除</span>
                    </div>
                  </template>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="dialogClosed">取 消</el-button>
          <el-button v-if="!disabled" type="primary" @click="submit">确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </PageContainer>
</template>
<script lang="jsx">
import moment from 'moment'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
export default {
  name: 'DictManagement',
  components: {
    ElImageViewer
  },
  data() {
    return {
      iconFileList: [],
      disabled: false,
      formInline: {
        paramName: null,
        paramAlias: null,
        paramNo: null,
        paramUnit: null,
        paramIcon: null,
        aliasData: []
      },
      rules: {
        paramAlias: [{ required: true, message: '请输入字典名称', trigger: 'blur' }],
        paramNo: [{ required: true, message: '请输入字典编码', trigger: 'blur' }]
      },
      dialogTitle: '',
      dialogVisible: false,
      tableLoading: false,
      showViewer: false,
      iconPathList: [], // 图片列表
      searchFrom: {
        paramName: '' // 监测项目全名
      },
      tableColumn: [
        {
          prop: '',
          label: '序号',
          formatter: (scope) => {
            return (this.pageData.current - 1) * this.pageData.size + scope.$index + 1
          },
          width: 80
        },
        {
          prop: 'paramName',
          label: '监测项目全称',
          width: 400
        },
        {
          prop: 'paramAlias',
          label: '简称'
        },
        {
          prop: 'paramNo',
          label: '编码'
        },
        {
          prop: 'paramUnit',
          label: '单位'
        },
        {
          prop: 'paramIcon',
          label: '图标',
          render: (h, row) => {
            return (
              <span style="color: #3562DB; cursor: pointer;" onClick={() => this.viewImage(row.row)}>
                点击查看
              </span>
            )
          }
        },
        {
          width: 200,
          prop: 'updateTime',
          label: '更新时间',
          formatter: (scope) => {
            // return scope.row
            return scope.row.updateTime ? moment(scope.row.updateTime).format('YYYY-MM-DD HH:MM:DD') : ''
          }
        },
        {
          width: 100,
          prop: 'operation',
          label: '操作',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span class="operationBtn-span" style="color: #121F3E" onClick={() => this.control('edit', row.row)}>
                  编辑
                </span>
                <span class="operationBtn-span" style="color: #fa403c" onClick={() => this.control('del', row.row)}>
                  删除
                </span>
              </div>
            )
          }
        }
      ],
      tableData: [],
      pageData: {
        current: 1,
        size: 15,
        total: 0
      },
      pageProps: {
        page: 'current',
        pageSize: 'size',
        total: 'total'
      }
    }
  },
  watch: {},
  mounted() {
    this.getApplicationList()
  },
  methods: {
    // 新增数据别名表单
    addDataAlias() {
      this.formInline.aliasData.push({
        dataValue: '',
        dataName: ''
      })
    },
    deleteAlias(index) {
      this.formInline.aliasData.splice(index, 1)
    },
    fileChange(file, fileList, type) {
      this.fileList = fileList
      this.iconFileList = fileList
    },
    handleRemove(file, fileList, type) {
      this.formInline.paramIcon = ''
      this.iconFileList = []
    },
    httpRequset(file, type) {
      const params = new FormData()
      params.append('file', file.file)
      this.$api.dictionaryUploadIcon(params).then((res) => {
        if (res.code == 200) {
          this.formInline.paramIcon = res.data
          this.$message({
            message: '上传成功',
            type: 'success'
          })
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 5
      if (!isJPG) {
        this.$message.error('上传图片只能是 png 格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 5MB!')
      }
      return isJPG && isLt2M
    },
    // 新增/编辑字典
    submit() {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          if (this.dialogTitle == '新增字典') {
            let { paramName, paramAlias, paramNo, paramUnit, paramIcon, aliasData } = this.formInline
            let params = {
              paramName,
              paramAlias,
              paramNo,
              paramUnit,
              paramIcon,
              aliasData
            }
            this.$api.saveDictAlias(params, { 'operation-type': 1 }).then((res) => {
              if (res.code == 200) {
                this.$message.success(res.message)
                this.getApplicationList()
              } else {
                this.$message.error(res.message)
              }
              this.dialogClosed()
            })
          } else {
            let { paramName, paramAlias, paramNo, paramUnit, paramIcon, id, aliasData } = this.formInline
            let params = {
              paramName,
              paramAlias,
              paramNo,
              paramUnit,
              paramIcon,
              id,
              aliasData
            }
            this.$api.updateDictAlias(params, { 'operation-type': 2, 'operation-id': params.id, 'operation-name': params.paramName }).then((res) => {
              if (res.code == 200) {
                this.$message.success(res.message)
                this.getApplicationList()
              } else {
                this.$message.error(res.message)
              }
              this.dialogClosed()
            })
          }
        }
      })
    },
    // 弹窗取消
    dialogClosed() {
      this.$refs.formInline.resetFields()
      this.formInline.paramIcon = null
      this.iconFileList = []
      this.dialogVisible = false
    },
    // 查看图片
    viewImage(row) {
      let images = []
      if (!row.paramIcon) {
        this.$message.info('当前没有可预览的图片')
        return
      }
      if (row.paramIcon) images.push(this.$tools.imgUrlTranslation(row.paramIcon))
      this.iconPathList = images
      this.showViewer = true
    },
    // 查询
    searchForm() {
      this.getApplicationList()
    },
    // 重置查询
    resetForm() {
      Object.assign(this.$data.searchFrom, this.$options.data().searchFrom)
      this.searchForm()
    },
    control(type, row) {
      this.formInline.aliasData = []
      this.dialogTitle = type == 'add' ? '新增字典' : type == 'edit' ? '编辑字典' : '查看详情'
      this.disabled = type == 'detail'
      // this.disabled = type == 'view'
      if (['add', 'edit', 'detail'].includes(type)) {
        if (type != 'add') {
          let data = {
            id: row.id
          }
          this.$api.selectOneDictAlias(data).then((res) => {
            if (res.code == 200) {
              Object.assign(this.formInline, res.data)
              this.iconFileList = [{ url: this.$tools.imgUrlTranslation(res.data.paramIcon) }]
            }
          })
        }
        this.dialogVisible = true
      } else if (type == 'del') {
        // 删除
        this.$confirm('删除后将无法恢复，是否确定删除？', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$api.deleteDictAlias({ id: row.id }, { 'operation-type': 3, 'operation-id': row.id, 'operation-name': row.paramName }).then((res) => {
            if (res.code == 200) {
              this.$message({ message: '应用删除成功', type: 'success' })
              this.isLastPage(1)
              this.searchForm()
            } else {
              this.$message({ message: res.msg, type: 'error' })
            }
          })
        })
      }
    },
    // 获取应用列表
    getApplicationList() {
      let param = {
        ...this.searchFrom,
        pageSize: this.pageData.size,
        page: this.pageData.current
      }
      this.tableLoading = true
      this.$api
        .selectDictAliasList(param)
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.records
            this.pageData.total = res.data.total
          }
          this.tableLoading = false
        })
        .catch(() => {
          this.tableLoading = false
        })
    },
    // 判断当前页是否是最后一页
    isLastPage(deleteNum) {
      let deleteAfterPage = Math.ceil((this.pageData.total - deleteNum) / this.pageData.size)
      let currentPage = this.pageData.current > deleteAfterPage ? deleteAfterPage : this.pageData.current
      this.pageData.current = currentPage < 1 ? 1 : currentPage
    },
    paginationChange(pagination) {
      // this.pageData.size = pagination.pageSize
      // this.pageData.current = pagination.page
      Object.assign(this.pageData, pagination)
      this.getApplicationList()
    }
  }
}
</script>
<style lang="scss" scoped>
.hide {
  ::v-deep .el-upload--picture-card {
    display: none;
  }
}
.control-btn-header {
  padding: 10px !important;
  .search-from {
    & > div {
      margin-right: 10px;
    }
  }
}
.table-content {
  margin-top: 15px;
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 15px);
  padding: 10px;
}
.data-alias-row {
  width: 100%;
  .data-alias-delete {
    color: #fa403c;
    cursor: pointer;
    margin-left: 10px;
    i {
      margin-right: 6px;
    }
  }
}
.content {
  width: 100%;
  max-height: 500px !important;
  overflow: auto;
  background-color: #fff !important;
}
</style>
<style lang="scss">
.operationBtn-span {
  margin-right: 10px;
}
</style>
