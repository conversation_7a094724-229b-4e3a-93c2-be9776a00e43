<template>
  <div class="personalInfo drag_class">
    <div v-scrollbarHover style="height: 100%">
      <div class="user-content">
        <div class="user-box">
          <div class="user-png">
            <el-avatar size="medium" :src="$tools.imgUrlTranslation($store.state.user?.userInfo?.user?.avatar)">
              <i class="el-icon-user-solid" />
            </el-avatar>
          </div>
          <div class="user-info">
            <div class="user-name">{{ serverTransZh }}，{{ $store.state.user.userInfo.user.staffName }}</div>
            <div class="user-job">开始您一天的工作吧！</div>
          </div>
          <p class="clickBtn" @click="subscribeShow = true"><i class="el-icon-plus"></i> 订阅</p>
          <el-dropdown class="dropdown-btn" trigger="click" @command="(val) => $emit('all-more-Oper', val, 'personalInfo')">
            <span class="more-operations" @click.stop.prevent>· · ·</span>
            <el-dropdown-menu slot="dropdown" class="dropdownSelect">
              <el-dropdown-item command="edit">编辑</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
        <div class="msg-box">
          <div class="date-box" :class="{ 'border-r': weatherBoxShow }">
            <div><svg-icon name="calendar" /></div>
            <span>{{ serverTransTime[0] }} &nbsp; {{ serverTransTime[1] }} &nbsp; {{ serverTransTime[2] }}</span>
          </div>
          <div v-if="weatherBoxShow" class="weather-box">
            <div><svg-icon name="umbrella" /></div>
            <span>{{ weatherZHCN }}</span>
          </div>
        </div>
      </div>
      <div class="personalInfo-title">快捷导航</div>
      <quickNavigation />
      <subscribeDialog v-if="subscribeShow" :visible.sync="subscribeShow" />
    </div>
  </div>
</template>
<script>
import moment from 'moment'
import axios from 'axios'
export default {
  name: 'personalInfo',
  components: {
    subscribeDialog: () => import('./dialog/subscribeDialog.vue'),
    quickNavigation: () => import('./quickNavigation.vue')
  },
  props: {
    item: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      serverTime: '',
      serverTransTime: [],
      serverTransZh: '',
      weatherZHCN: '',
      timer: null,
      weatherBoxShow: false,
      subscribeShow: false // 消息订阅弹窗
    }
  },
  mounted() {
    this.getServerTime()
    this.getLocalCity()
    // this.getWeatherByCityCode()
  },
  methods: {
    moreOperEvent(val) {
      console.log(val)
    },
    // 获取服务器时间
    getServerTime() {
      this.$api
        .getSysTime()
        .then((res) => {
          console.log(res.data)
          this.serverTime = res.data
        })
        .finally(() => {
          const weekdays = '星期日_星期一_星期二_星期三_星期四_星期五_星期六'.split('_')
          this.timer = setInterval(() => {
            // moment时间增加
            this.serverTime = moment(this.serverTime).add(1, 'second')
            let weekZHCN = weekdays[moment(this.serverTime).day()]
            this.serverTransTime = [this.serverTime.format('YYYY-MM-DD日'), weekZHCN, this.serverTime.format('HH:mm')]
            // 根据HH判断当前是早上>0中午>11下午>13晚上>18
            const HH = this.serverTime.format('HH')
            if (HH > 0 && HH < 11) {
              this.serverTransZh = '早上好'
            } else if (HH >= 11 && HH < 13) {
              this.serverTransZh = '中午好'
            } else if (HH >= 13 && HH < 18) {
              this.serverTransZh = '下午好'
            } else {
              this.serverTransZh = '晚上好'
            }
          }, 1000)
        })
    },
    // 获取当前ip所在位置
    getLocalCity() {
      // https://lbs.qq.com/ 腾讯位置服务创建应用获取位置信息的key 日上限10000次 并发5次
      const data = {
        key: 'BEEBZ-MJA64-SMHUC-FX57V-6QYVK-UGBPG',
        output: 'jsonp'
      }
      // 获取地理位置信息的接口
      const url = 'https://apis.map.qq.com/ws/location/v1/ip'
      this.$jsonp(url, data)
        .then((res) => {
          console.log(res)
          const location = res.result.location
          // https://console.qweather.com/ 注册和风天气创建应用获取key 日上限1000次
          const key = 'b01f8089367743dea421770d3906a95f'
          // 根据城市，获取当前天气和时间
          axios.get(`https://devapi.qweather.com/v7/weather/now?location=${location.lng},${location.lat}&key=${key}`).then(res => {
            console.log(res.data.now.temp)
            const data = res.data
            if (data.code == 200) {
              this.weatherBoxShow = true
              const weather = data.now.text + ' ' + data.now.temp + '℃，' + data.now.windDir + ' ' + data.now.windScale + '级'
              this.weatherZHCN = weather
            }
          })
        })
        .catch((error) => {
          console.log(error)
        })
    },
    // 获取天气
    getWeatherByCityCode() {
      this.weatherBoxShow = false
      this.$api.getWeatherByCityCode().then((res) => {
        if (res.code == 200) {
          this.weatherBoxShow = true
          const data = res.data
          let weather = ''
          if (data.textDay != data.textNight) {
            weather = data.textDay + '转' + data.textNight
          } else {
            weather = data.textDay
          }
          weather += ' ' + data.tempMin + '℃~' + data.tempMax + '℃，' + data.windDirDay + ' ' + data.windScaleDay + '级'
          // const weather =
          this.weatherZHCN = weather
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.dropdownSelect {
  margin: 0;
  padding: 3px 0;
  .el-dropdown-menu__item {
    line-height: 30px;
    padding: 0 15px;
  }
}
.personalInfo {
  width: 100%;
  height: 100%;
  background: #fff;
  border-radius: 4px;
  padding: 16px;
}
.personalInfo-title {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  line-height: 19px;
  margin: 7% 0 2% 1%;
}
.user-content {
  background: linear-gradient(180deg, #ecf3fd 0%, rgba(236, 243, 253, 0) 100%);
  border-radius: 4px 4px 4px 4px;
  padding: 16px 0 10px 16px;
}
.user-box {
  height: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .user-png {
    height: 100%;
    width: 50px;

    ::v-deep .el-avatar {
      width: 100%;
      height: 100%;
      line-height: 50px;
    }
  }

  .user-info {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-left: 10px;

    .user-name {
      font-size: 18px;
      font-weight: 600;
      color: #121f3e;
      font-family: 'PingFang SC-Bold', 'PingFang SC';
    }

    .user-job {
      font-size: 14px;
      color: #999;
    }
  }

  .clickBtn {
    color: #3562db;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #3562db;

    font-size: 14px;
    font-family: 'PingFang SC-Regular', 'PingFang SC';
    text-align: center;
    line-height: 22px;
    background: transparent;
    border-radius: 4px;
    padding: 2px 8px;
    margin: 0;
    &:hover {
      cursor: pointer;
    }
  }
  .more-operations {
    width: 48px;
    height: 24px;
    text-align: center;
    line-height: 24px;
    display: inline-block;
    cursor: pointer;
    margin-left: 16px;
  }
}

.msg-box {
  margin-top: 9%;
  display: flex;
  justify-content: space-between;

  .date-box,
  .weather-box {
    width: calc(50% - 8px);
    height: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    div {
      height: 40px;
      line-height: 40px;

      svg {
        font-size: 22px;
      }
    }
    span {
      font-size: 14px;
      font-family: 'PingFang SC-Regular', 'PingFang SC';
      color: #666666;
    }
  }
  .border-r {
    border-right: 1px solid #e5e5e5;
  }
}
</style>
