<template>
  <el-dialog title="添加设备" width="1050px" :visible.sync="dialogShow" :close-on-click-modal="false" custom-class="model-dialog" :before-close="closeDialog">
    <div class="harvester_content">
      <div class="table_box">
        <div class="search_box">
          <div class="search_select">
            <el-input v-model="searchForm.assetsName" placeholder="设备名称" style="width: 200px"></el-input>
            <el-select ref="sysOfcode" v-model="searchForm.sysOfcode" clearable placeholder="所属品类" @clear="handleClear1">
              <el-option hidden :value="searchForm.sysOfcode" :label="categoryName"> </el-option>
              <el-tree :data="categoryList" :props="categoryProps" :expand-on-click-node="false" :check-on-click-node="true" @node-click="handleNodeClick1"> </el-tree>
            </el-select>
            <el-select ref="treeSelect" v-model="searchForm.spaceId" placeholder="设备位置" clearable @clear="treeHandleClear">
              <el-option hidden :value="searchForm.spaceId" :label="regionName"> </el-option>
              <el-tree :data="spaceData" :props="spaceProps" :expand-on-click-node="false" :check-on-click-node="true" @node-click="treeHandleNodeClick"> </el-tree>
            </el-select>
            <div style="display: inline-block">
              <el-button type="primary" plain @click="resetForm">重置</el-button>
              <el-button type="primary" @click="inquire">查询</el-button>
            </div>
          </div>
        </div>
        <TablePage
          ref="treeTable"
          v-loading="tableLoading"
          class="table_div"
          row-key="id"
          :showPage="true"
          :tableColumn="tableColumn"
          :data="tableData"
          default-expand-all
          height="calc(100% - 80px)"
          :pageData="pageData"
          :pageProps="pageProps"
          @pagination="paginationChange"
          @selection-change="handleSelectionChange"
        >
        </TablePage>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="groupSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script lang="jsx">
import { transData } from '@/util'
export default {
  name: 'selectMonitorDevice',
  props: {
    dialogShow: {
      type: Boolean,
      default: false
    },
    harvesterCheckType: {
      // 选中类型为 单选还是多选
      type: String,
      default: 'radio'
    }
  },
  data() {
    return {
      systemCodeList: [], // 所属品类
      parameterList: [], // 物联checkBox列表
      searchForm: {
        assetsName: '',
        spaceId: ''
      },
      categoryName: '', // 品类name
      categoryList: [], // 品类list
      category: [], // 品类list
      categoryProps: {
        label: 'dictionaryDetailsName',
        isLeaf: 'dictionaryDetailsCode',
        children: 'children'
      },
      tableData: [],
      tableLoading: false,
      multipleCheckData: [], // 多选数据
      checkRadio: '',
      pageData: {
        current: 1,
        size: 15,
        total: 0
      },
      pageProps: {
        page: 'current',
        pageSize: 'size',
        total: 'total'
      },
      selectedRows: {}, // 选中的行
      spaceData: [], // 空间数据
      regionName: '', // 空间名称
      spaceProps: {
        label: 'ssmName',
        isLeaf: 'leaf',
        children: 'children'
      } // 空间数据配置
    }
  },
  computed: {
    tableColumn() {
      return [
        {
          label: '',
          width: 80,
          align: 'center',
          render: (h, row) => {
            return (
              <el-radio
                v-model={this.checkRadio}
                label={row.row.id}
                onChange={() => {
                  this.harvesterListRadioCheck(row.row)
                }}
              >
                &nbsp;
              </el-radio>
            )
          },
          hasJudge: this.harvesterCheckType == 'radio'
        },
        {
          type: 'selection',
          align: 'center',
          width: 80,
          hasJudge: this.harvesterCheckType == 'checkbox'
        },
        {
          prop: 'assetsName',
          label: '设备名称',
          required: true
        },
        {
          prop: 'assetsCode',
          label: '设备编码',
          required: true
        },
        {
          prop: 'systemTypeName',
          label: '设备分类',
          required: true
        },
        {
          prop: 'deviceTypeName',
          label: '设备类型'
        },
        {
          prop: 'spaceName',
          label: '所在位置',
          required: true
        },
        {
          prop: 'relevantDeptName',
          label: '所属科室',
          required: true
        },
        {
          prop: 'deviceModel',
          label: '型号',
          required: true
        }
      ]
    }
  },
  mounted() {
    this.spaceTreeListFn()
    this.getSensorList()
    this.systemType()
  },
  methods: {
    // 重置
    resetForm() {
      this.pageData = {
        current: 1,
        size: 15,
        total: 0
      }
      this.pageProps = {
        page: 'current',
        pageSize: 'size',
        total: 'total'
      }
      this.searchForm = {
        assetsName: '',
        spaceId: ''
      }
      this.treeHandleClear()
      this.categoryName = ''
      this.multipleCheckData = []
      this.parameterList = []
      this.tableData = []
      this.getSensorList()
    },
    // 查询
    inquire() {
      this.multipleCheckData = []
      this.parameterList = []
      this.tableData = []
      this.getSensorList()
    },
    // 所属品类
    systemType() {
      let data = {
        page: 1,
        pageSize: 200,
        enable: 1
      }
      this.$api.getCategoryManagementList(data).then((res) => {
        if (res.code == '200') {
          this.category = res.data.records
          res.data.records.map((e) => {
            e.leaf = false
          })
          this.categoryList = transData(res.data.records, 'dictionaryDetailsCode', 'parentId', 'children')
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 选择品类下拉树 数据
    handleNodeClick1(data) {
      if (data.level == 1) {
        this.searchForm.sysOfcode = data.dictionaryDetailsCode
        this.searchForm.sysOf1code = ''
      } else {
        const parentCode = this.getParentCode(this.categoryList, data.dictionaryDetailsCode)
        if (parentCode) {
          this.searchForm.sysOfcode = parentCode
        }
        this.searchForm.sysOf1code = data.dictionaryDetailsCode
      }
      this.categoryName = data.dictionaryDetailsName
      this.$refs.sysOfcode.blur()
    },
    getParentCode(data, childCode) {
      for (const node of data) {
        if (node.children && node.children.length) {
          for (const child of node.children) {
            if (child.dictionaryDetailsCode === childCode) {
              return node.dictionaryDetailsCode // 返回父节点的 code
            }
          }
          const parentCode = this.getParentCode(node.children, childCode)
          if (parentCode) {
            return parentCode
          }
        }
      }
      return null // 如果没有找到父节点
    },
    // 空间数据清除
    treeHandleClear() {
      this.searchForm.spaceId = ''
      this.regionName = ''
    },
    // 选择下拉树 数据
    treeHandleNodeClick(data) {
      this.searchForm.spaceId = data.id
      this.regionName = data.ssmName
      this.$refs.treeSelect.blur()
    },
    spaceTreeListFn() {
      this.$api.spaceTreeList().then((res) => {
        if (res.code == 200) {
          this.spaceData = transData(res.data, 'id', 'pid', 'children')
        }
      })
    },
    // 品类数据清除
    handleClear1() {
      this.searchForm.sysOfcode = ''
      this.searchForm.sysOf1code = ''
      this.categoryName = ''
    },
    // 设备列表
    getSensorList() {
      this.tableLoading = true
      let param = {
        ...this.searchForm,
        pageSize: this.pageData.size,
        page: this.pageData.current
      }
      this.$api.getlMonitoringList(param).then((res) => {
        this.tableLoading = false
        if (res.code == '200') {
          this.tableData = res.data.records
          this.pageData.total = res.data.total
        } else {
          this.tableData = []
          this.parameterList = []
        }
      })
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getSensorList()
    },
    closeDialog() {
      this.$emit('closeDialog')
    },
    // 列表单选点击
    harvesterListRadioCheck(currentRow) {
      this.selectedRows = currentRow
    },
    // 列表多选
    handleSelectionChange(val) {
      this.multipleCheckData = val
    },
    groupSubmit() {
      if (this.multipleCheckData.length > 0) {
        this.$emit('submitDialog', this.multipleCheckData)
        this.closeDialog()
      } else {
        this.$message({
          message: '请选择设备！',
          type: 'warning'
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog__body {
  height: calc(70vh - 110px);
  display: flex;
  padding: 15px 10px;
  overflow: auto;
}
.model-dialog {
  margin-top: 8vh !important;
  .harvester_content {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .table_box {
      padding: 10px 20px 10px 10px;
      background: #fff;
    }
    .table_box {
      flex: 1;
      height: 0;
      display: flex;
      flex-direction: column;
      .search_box {
        .search_select {
          margin-bottom: 10px;
          > div {
            margin-right: 10px;
          }
        }
        .search_input {
          display: flex;
          align-items: center;
          width: 500px;
          margin-bottom: 15px;
          p {
            width: 90px;
            margin: 0;
            margin-right: 15px;
          }
        }
      }
      .table_div {
        height: calc(100% - 10px);
        height: 0;
      }
    }
  }
}
</style>
