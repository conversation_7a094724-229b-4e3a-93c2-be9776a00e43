<template>
  <el-dialog
    v-if="alarmShowDialogVisible"
    v-dialogDrag
    custom-class="polling-dialog"
    :modal="false"
    :close-on-click-modal="false"
    :visible.sync="alarmShowDialogVisible"
    :before-close="beforeClose()"
  >
    <span slot="title">
      报警提醒
    </span>
    <div class="polling-content">
      <div class="polling-content-item">
        <img src="@/assets/images/monitor/alarm-segment-header.png" alt="">
        <span>报警对象：</span>
        <span>{{ alarmData.data.alarmData.alarmObjectName }}</span>
      </div>
      <div class="polling-content-item">
        <img src="@/assets/images/monitor/alarm-segment-header.png" alt="">
        <span>报警类型：</span>
        <span>{{ alarmData.data.alarmData.incidentName }}</span>
      </div>
      <div class="polling-content-item">
        <img src="@/assets/images/monitor/alarm-segment-header.png" alt="">
        <span>报警等级：</span>
        <div class="grade">{{ alarmLevelItem[alarmData.data.alarmData.alarmLevel].text }}</div>
      </div>
      <div class="polling-content-item">
        <img src="@/assets/images/monitor/alarm-segment-header.png" alt="">
        <span>报警时间：</span>
        <span>{{ moment(Number(alarmData.data.alarmData.alarmStartTime)).format('YYYY-MM-DD HH:mm:ss') }}</span>
      </div>
    </div>
    <span slot="footer">
      <el-button class="cancel" type="primary" plain @click="reminderLater()">稍后提醒</el-button>
      <el-button class="confirm" type="primary" @click="submitPollingForm">现在处理</el-button>
    </span>
  </el-dialog>
</template>

<script>
import moment from 'moment'
export default {
  data() {
    return {
      moment,
      alarmData: {},
      alarmLevelItem: {
        0: { text: '通知', color: '#3562DB' },
        1: { text: '一般', color: '#3562DB' },
        2: { text: '紧急', color: '#FF9435' },
        3: { text: '重要', color: '#FA403C' }
      },
      alarmShowDialogVisible: false
    }
  },
  methods: {
    getData(data) {
      this.alarmData = data
      this.alarmShowDialogVisible = true
    },
    beforeClose() {
      let uesrId = this.$store.state.user.userInfo.userId
      window.localStorage.removeItem(`boilerAlarm${uesrId}`)
    },
    submitPollingForm() {
      this.$router.push({
        path: '/alarmRecord/index',
        query: {
          projectCode: this.alarmData.data.alarmData.projectCode
        }
      })
    },
    reminderLater() {
      let uesrId = this.$store.state.user.userInfo.userId
      let timeout = 1000 * 60 * 5
      // let timeout = 1000 * 20
      let time = new Date().getTime() + timeout
      let data = {
        ...this.alarmData,
        time
      }
      window.localStorage.setItem(`boilerAlarm${uesrId}`, JSON.stringify(data))
      this.alarmShowDialogVisible = false
      setTimeout(() => {
        this.alarmShowDialogVisible = true
      }, timeout)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .polling-dialog {
  width: 350px;
  background: url("~@/assets/images/monitor/alarm-dialog.png") no-repeat;
  background-size: 100% 100%;
  .el-dialog__footer{
    .cancel{
      background: url("~@/assets/images/monitor/cancel-button.png") no-repeat;
      background-size: 100% 100%;
      border-color: transparent;
      span{
        color: #fff;
        opacity: .8;
      }
    }
    .confirm{
      background: url("~@/assets/images/monitor/ok-button.png") no-repeat;
      background-size: 100% 100%;
      border-color: transparent;
    }
  }

  .el-dialog__header {
    padding: 20px 10px 10px 26px;
    span {
      font-size: 20px;
      font-family: "HarmonyOS Sans SC-Medium", "HarmonyOS Sans SC";
      font-weight: 500;
      color: #dce9ff;
    }

    .el-dialog__headerbtn .el-dialog__close {
      color: #FFA7A6;
      font-size: 20px;
    }
  }

  .polling-content {
    .polling-content-item{
      display: flex;
      align-items: center;
      span{
        color: #fff;
        opacity: .8;
      }
      .grade{
        width: 32px;
        height: 18px;
        background: #F53F3F;
        border-radius: 2px;
        color: #fff;
        font-size: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

  }
}
</style>