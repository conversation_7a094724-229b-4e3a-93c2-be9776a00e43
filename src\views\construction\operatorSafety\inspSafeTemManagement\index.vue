<template>
  <PageContainer>
    <div slot="header" class="control-btn-header">
      <div class="search-from">
        <el-input v-model.trim="searchForm.templateName" placeholder="模板名称" clearable style="width: 200px"></el-input>
        <el-select v-model="searchForm.templateType" placeholder="请选择类型" class="ml-16" clearable filterable>
          <el-option v-for="item in typeList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
        </el-select>
        <div style="display: inline-block">
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="search">查询</el-button>
        </div>
      </div>
    </div>
    <div slot="content" class="table-content">
      <div class="btn-group">
        <el-button type="primary" @click="handleListEvent('add')">新增</el-button>
      </div>
      <TablePage
        ref="table"
        v-loading="tableLoading"
        :showPage="true"
        :tableColumn="tableColumn"
        :data="tableData"
        height="calc(100% - 40px - 50px)"
        :pageData="pageData"
        :pageProps="pageProps"
        @pagination="paginationChange"
      ></TablePage>
    </div>
  </PageContainer>
</template>
<script lang="jsx">
export default {
  name: 'inspSafeTemManagement',
  data() {
    return {
      tableLoading: false,
      typeList: [
        {
          id: '0',
          name: '日常巡检'
        },
        {
          id: '1',
          name: '专业巡检'
        }
      ],
      searchForm: {
        templateName: '',
        templateType: ''
      },
      tableColumn: [
        {
          prop: '',
          label: '序号',
          formatter: (scope) => {
            return (this.pageData.currentPage - 1) * this.pageData.size + scope.$index + 1
          },
          width: 80
        },
        {
          prop: 'templateName',
          label: '模板名称',
          required: true
        },
        {
          prop: 'templateExplain',
          label: '模板说明'
        },
        {
          prop: 'createByName',
          label: '添加人'
        },
        {
          prop: 'createDate',
          label: '更新时间'
        },
        {
          prop: 'operation',
          label: '操作',
          width: 180,
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span class="operationBtn-span" style="color: #3562db" onClick={() => this.handleListEvent('detail', row.row)}>
                  详情
                </span>
                <span class="operationBtn-span" style="color: #3562db" onClick={() => this.handleListEvent('edit', row.row)}>
                  编辑
                </span>
                <span class="operationBtn-span" style="color: #3562db" onClick={() => this.handleListEvent('copy', row.row)}>
                  复制
                </span>
                <span class="operationBtn-span" style="color: #fa403c" onClick={() => this.handleListEvent('del', row.row)}>
                  删除
                </span>
              </div>
            )
          }
        }
      ],
      pageProps: {
        page: 'currentPage',
        pageSize: 'size',
        total: 'total'
      },
      tableData: [],
      pageData: {
        currentPage: 1,
        size: 15,
        total: 0
      }
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    handleListEvent(type, row) {
      switch (type) {
        case 'add':
        case 'edit':
        case 'copy':
          this.$router.push({ name: 'inspSafeTemManagementAdd', query: { id: row ? row.id : '', type: type } })
          break
        case 'detail':
          this.$router.push({ name: 'inspSafeTemManagementDetail', query: { id: row.id } })
          break
        case 'del':
          this.$confirm('是否删除当前模板？', '提示', {
            cancelButtonClass: 'el-button--primary is-plain',
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.$api.workMaintainDel({ id: row.id }).then((res) => {
              if (res.code === '200') {
                this.$message.success('删除成功')
                this.getList()
              } else {
                this.$message.error(res.msg)
              }
            })
          })
      }
    },
    // 查询
    search() {
      this.getList()
    },
    // 重置查询
    resetForm() {
      this.searchForm = {
        templateName: '',
        templateType: ''
      }
      this.pageData.page = 1
      this.searchForm()
    },
    // 获取应用列表
    getList() {
      let param = {
        ...this.searchForm,
        size: this.pageData.size,
        currentPage: this.pageData.currentPage
      }
      this.tableLoading = true
      this.$api
        .workMaintainList(param)
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.list
            this.pageData.total = res.data.count
          }
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getList()
    }
  }
}
</script>
<style lang="scss" scoped>
.hide {
  ::v-deep .el-upload--picture-card {
    display: none;
  }
}
.control-btn-header {
  padding: 10px !important;
  .search-from {
    & > div {
      margin-right: 10px;
    }
  }
}
.table-content {
  margin-top: 15px;
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 15px);
  padding: 10px;
  .btn-group {
    height: 50px;
  }
}
</style>
