<template>
  <!-- 运行统计 -->
  <div class="top_operating operating_common drag_class">
    <div class="operating_box">
      <div class="box_icon duration-icon"></div>
      <div class="box_content">
        <span>运行时长</span>
        <span><span class="big_num">{{ elevatorOthersData.runTime ?? 0 }}</span> 时</span>
      </div>
    </div>
    <div class="operating_box">
      <div class="box_icon distance-icon"></div>
      <div class="box_content">
        <span>运行距离</span>
        <span><span class="big_num">{{ elevatorOthersData.runDistance ?? 0 }}</span> 米</span>
      </div>
    </div>
    <div class="operating_box">
      <div class="box_icon numbers-icon"></div>
      <div class="box_content">
        <span>运行次数</span>
        <span><span class="big_num">{{ elevatorOthersData.runNum ?? 0 }}</span> 次</span>
      </div>
    </div>
    <div class="operating_box">
      <div class="box_icon numbers-icon"></div>
      <div class="box_content">
        <span>开门次数</span>
        <span><span class="big_num">{{ elevatorOthersData.openCount ?? 0 }}</span> 次</span>
      </div>
    </div>
  </div>
</template>
<script>
import moment from 'moment'
import { monitorTypeList } from '@/util/dict.js'
export default {
  data() {
    return {
      requestHttp: __PATH.VUE_IEMC_ELEVATOR_API,
      projectCode: monitorTypeList.find((item) => item.projectName == '电梯监测').projectCode, // 电梯code
      elevatorOthersData: {
        openCount: '0',
        runDistance: '0',
        runNum: '0',
        runTime: '0',
        alarmCount: '0'
      },
      timer: null
    }
  },
  mounted() {
    this.getElevatorStatistics()
    this.scheduledTasks()
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
  },
  methods: {
    scheduledTasks() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
      this.timer = setInterval(() => {
        this.getElevatorStatistics()
      }, 30000)
    },
  },
  methods: {
    // 获取电梯类型数据 左上角统计 及电梯健康分布数据
    getElevatorStatistics() {
      const params = {
        projectCode: this.projectCode,
        startTime: moment().date(1).format('YYYY-MM-DD'),
        endTime: moment().format('YYYY-MM-DD')
      }
      this.$api.getElevatorStatistics(params, this.requestHttp).then((res) => {
        if (res.code === '200') {
          const { list, recordList, ...othersData } = res.data
          // 其他统计项渲染
          this.elevatorOthersData = othersData
          // 电梯健康分布渲染
        }
      })
    }
  }
}
</script>
<style  lang="scss" scoped>
.top_operating {
  width: 100%;
  height: 100%;
  padding: 10px
}
.operating_common {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  // align-items: center;
  .operating_box {
    width: calc(50% - 8px);
    height: calc(50% - 16px);
    display: flex;
    .box_icon {
      margin: auto 0;
      height: 80%;
      aspect-ratio: 1;
    }
    .duration-icon {
      background: url('~@/assets/images/elevator/duration-icon.png') no-repeat;
      background-size: 100% 100%;
    }
    .distance-icon {
      background: url('~@/assets/images/elevator/distance-icon.png') no-repeat;
      background-size: 100% 100%;
    }
    .numbers-icon {
      background: url('~@/assets/images/elevator/numbers-icon.png') no-repeat;
      background-size: 100% 100%;
    }
    .box_content {
      display: flex;
      flex-direction: column;
      justify-content: space-evenly;
      padding-left: 10px;
      > span {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      > span:first-child {
        font-size: 15px;
        font-family: Alibaba PuHuiTi-Regular, Alibaba PuHuiTi;
        color: #a2b7d9;
      }
      > span:last-child {
        font-size: 12px;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        color: #ccced3;
      }
      .big_num {
        font-size: 20px;
        font-family: Arial-Bold, Arial;
        font-weight: bold;
        color: #c9dfff;
      }
    }
  }
}
</style>
