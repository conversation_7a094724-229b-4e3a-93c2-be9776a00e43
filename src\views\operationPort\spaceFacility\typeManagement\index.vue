<template>
  <PageContainer>
    <div slot="content" class="configuration-content">
      <div class="configuration-content-left">
        <div v-loading class="left_content">
          <el-input v-model="filterText" placeholder="输入关键字进行过滤" suffix-icon="el-icon-search" clearable> </el-input>
          <el-tree
            ref="tree"
            v-loading="treeLoading"
            style="margin-top: 10px"
            :data="treeData"
            :props="defaultProps"
            node-key="id"
            :highlight-current="true"
            :filter-node-method="filterNode"
            @node-click="handleNodeClick"
          >
          </el-tree>
        </div>
      </div>
      <div class="configuration-content-right">
        <div style="height: 100%">
          <div class="search-from">
            <div>
              <el-input v-model="filters.name" suffix-icon="el-icon-search" placeholder="搜索名称"></el-input>
            </div>
            <div>
              <el-button type="primary" plain class="ml-16" @click="resetForm">重置</el-button>
              <el-button type="primary" @click="searchForm">查询</el-button>
            </div>
          </div>
          <el-button type="primary" icon="el-icon-plus" @click="handleListEvent('add')">添加字典值</el-button>
          <div class="contentTable">
            <div class="contentTable-main table-content">
              <el-table v-loading="tableLoading" border style="width: 100%" height="calc(100% - 20px)" row-key="id" :data="tableData" stripe>
                <el-table-column prop="meetingDictName" label="名称" show-overflow-tooltip width="140"></el-table-column>
                <el-table-column prop="meetingDictCode" label="编码" show-overflow-tooltip></el-table-column>
                <el-table-column prop="color" label="颜色" show-overflow-tooltip width="200">
                  <template slot-scope="scope">
                    <div class="colorClass">
                      <el-color-picker v-model="scope.row.color" disabled size="mini"></el-color-picker>
                      <span> {{ scope.row.color }}</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="dictImage" label="图片" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span v-if="!scope.row.dictImage"></span>
                    <img
                      v-else
                      :src="$tools.imgUrlTranslation(scope.row.dictImage) || ''"
                      alt=""
                      style="width: 48px; height: 48px; cursor: pointer"
                      @click="viewImage(scope.row)"
                    />
                  </template>
                </el-table-column>
                <el-table-column prop="parentDictName" label="上级字典" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span>{{ scope.row.parentDictName || '-' }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="status" show-overflow-tooltip label="状态">
                  <template slot-scope="scope">
                    <div v-if="scope.row.status === 1" class="status-color2"><span class="dot"></span><span>停用</span></div>
                    <div v-if="scope.row.status === 0" class="status-color1"><span class="dot"></span><span>启用</span></div>
                  </template>
                </el-table-column>
                <el-table-column prop="presetsType" label="预设" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span>{{ scope.row.presetsType === 1 ? '是' : '否' }}</span>
                  </template>
                </el-table-column>
                <el-table-column show-overflow-tooltip label="操作" width="200" align="center">
                  <template slot-scope="scope">
                    <el-button type="text" @click="handleListEvent('detail', scope.row)">查看</el-button>
                    <el-button type="text" @click="handleListEvent('edit', scope.row)">编辑</el-button>
                    <el-dropdown @command="(val) => handleListEvent(val, scope.row)">
                      <span style="color: #3562db; margin-left: 10px; cursor: pointer"> 更多 <i class="el-icon-arrow-down"></i> </span>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item command="addSubordinate" :disabled="scope.row.level == 3">新增下级</el-dropdown-item>
                        <el-dropdown-item command="status">{{ scope.row.status === 0 ? '停用' : '启用' }}</el-dropdown-item>
                        <el-dropdown-item command="del" class="deleteItem">删除</el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="contentTable-footer">
              <el-pagination
                :current-page="pagination.pageNo"
                :page-sizes="[15, 30, 50, 100]"
                :page-size="pagination.pageSize"
                layout="total, sizes, prev, pager, next"
                :total="pageTotal"
                @size-change="paginationSizeChange"
                @current-change="paginationCurrentChange"
              >
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
      <el-dialog
        v-dialogDrag
        :visible.sync="dialogVisible"
        :before-close="dialogClosed"
        :modal="false"
        :close-on-click-modal="false"
        :title="diaTitle"
        width="40%"
        custom-class="model-dialog"
      >
        <div class="diaContent" style="padding: 10px">
          <el-form ref="formInline" :model="formInline" label-width="100px" :rules="rules" label-position="right">
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="上级字典">
                  <zk-select-tree
                    v-if="operationType !== 'detail'"
                    v-model="formInline.parentId"
                    :data="allDictTreeData"
                    :props="propsType"
                    :disabled="operationType === 'edit'"
                    @getData="getItemData"
                  ></zk-select-tree>
                  <span v-else>{{ formInline.parentDictName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="字典名称" prop="meetingDictName">
                  <el-input v-if="operationType !== 'detail'" v-model="formInline.meetingDictName" maxlength="50" placeholder="请输入字典名称"></el-input>
                  <span v-else>{{ formInline.meetingDictName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="字典编码" prop="meetingDictCode">
                  <el-input
                    v-if="operationType !== 'detail'"
                    v-model="formInline.meetingDictCode"
                    maxlength="50"
                    placeholder="请输入字典编码"
                    :disabled="operationType === 'edit'"
                  ></el-input>
                  <span v-else>{{ formInline.meetingDictCode }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="颜色">
                  <el-input v-model="formInline.color" readonly placeholder="请选择颜色" :disabled="operationType === 'detail'">
                    <el-color-picker slot="prepend" v-model="formInline.color" size="small " :disabled="operationType === 'detail'"></el-color-picker>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="启用状态" prop="status">
                  <el-select v-if="operationType !== 'detail'" v-model="formInline.status" placeholder="请选择状态" style="width: 100%">
                    <el-option v-for="item in statusList" :key="item.value" :label="item.name" :value="item.value"></el-option>
                  </el-select>
                  <span v-else>{{ formInline.status === 0 ? '启用' : '停用' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="图片">
                  <el-upload
                    action=""
                    list-type="picture-card"
                    :class="{ hide: hideUpload }"
                    :disabled="operationType === 'detail'"
                    :file-list="fileList"
                    accept=".jpg, .jpeg, .png, .JPG, .JPEG, svg"
                    :limit="1"
                    :before-upload="beforeAvatarUpload"
                    :on-preview="handlePictureCardPreview"
                    :http-request="httpRequset"
                    :on-remove="handleRemove"
                    :on-change="fileChange"
                  >
                    <i class="el-icon-plus"><br /></i>
                    <div slot="tip" class="el-upload__tip">支持JPG/PNG/SVG,且大小不超过2M</div>
                  </el-upload>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="dialogClosed">取 消</el-button>
          <el-button type="primary" @click="submit('formInline')">确 定</el-button>
        </span>
      </el-dialog>
      <el-dialog :visible.sync="dialogImageVisible">
        <img width="100%" :src="dialogImageUrl" alt="" />
      </el-dialog>
    </div>
  </PageContainer>
</template>
<script>
import axios from 'axios'
import store from '@/store/index'
import { transData } from '@/util'

export default {
  name: 'configManage',
  data() {
    return {
      defaultProps: {
        label: 'name',
        value: 'value'
      },
      filterText: '',
      checkedData: {},
      treeData: [],
      tableLoading: false,
      treeLoading: false,
      dialogVisible: false,
      filters: {
        name: '' // 名称
      },
      propsType: {
        children: 'children',
        label: 'meetingDictName',
        value: 'id'
      },
      formInline: {
        parentId: '', // 父级id
        parentDictName: '', // 字典父级名称
        meetingDictCode: '', // 	报警字典编码
        meetingDictName: '', // 报警字典名称
        color: '', // 颜色
        dictImage: '', // 图片url
        status: '' // 状态 0-启用 1-停用
      },
      pagination: {
        pageNo: 1,
        pageSize: 15
      },
      diaTitle: '',
      pageTotal: 0,
      statusList: [
        {
          name: '启用',
          value: 0
        },
        {
          name: '禁用',
          value: 1
        }
      ],
      rules: {
        meetingDictName: [{ required: true, message: '请输入字典名称', trigger: 'blur' }],
        meetingDictCode: [{ required: true, message: '请输入字典编码', trigger: 'blur' }],
        status: [{ required: true, message: '请选择状态', trigger: 'change' }]
      },
      tableData: [],
      multipleSelection: [],
      allDictTreeData: [],
      operationType: '',
      fileList: [],
      rowId: '',
      hideUpload: false,
      dialogImageUrl: '',
      dialogImageVisible: false
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  mounted() {
    this.getMeetingDictEnum()
  },
  methods: {
    // 过滤树
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    getMeetingDictEnum() {
      this.$api.meetingDictEnum().then((res) => {
        console.log(res)
        if (res.code === '200') {
          let treeData = []
          for (const key in res.data) {
            if (Object.prototype.hasOwnProperty.call(res.data, key)) {
              const element = res.data[key]
              treeData.push({
                id: key,
                name: element
              })
            }
          }
          if (treeData.length) {
            this.treeData = treeData
            this.checkedData = treeData[0]
            this.$nextTick(() => {
              this.$refs.tree.setCurrentKey(treeData[0].id)
            })
            this.searchForm()
          } else {
            this.treeData = []
          }
        }
      })
    },
    getDataList() {
      this.tableLoading = true
      const params = {
        meetingDictName: this.filters.name,
        dictType: this.checkedData.id,
        page: {
          current: this.pagination.pageNo,
          size: this.pagination.pageSize,
          total: this.pageTotal
        }
      }
      this.$api.getDictionariesTree(params).then((res) => {
        this.tableLoading = false
        if (res.code == '200') {
          this.tableData = res.data.records
          this.pageTotal = res.data.total
        } else {
          this.$message.error('获取字典列表失败！')
        }
      })
    },
    paginationSizeChange(val) {
      this.pagination.pageNo = 1
      this.pagination.pageSize = val
      this.searchForm()
    },
    paginationCurrentChange(val) {
      this.pagination.pageNo = val
      this.searchForm()
    },
    handleNodeClick(data) {
      this.checkedData = data
      this.$refs.tree.setCurrentKey(this.checkedData.id)
      this.resetForm()
    },
    resetForm() {
      this.pagination.pageNo = 1
      this.filters.name = ''
      this.searchForm()
    },
    searchForm() {
      this.getDataList()
    },
    // 弹窗取消
    dialogClosed() {
      this.dialogVisible = false
      this.hideUpload = false
      this.rowId = ''
      this.dialogImageUrl = ''
      this.fileList = []
      this.formInline = {
        parentId: '', // 父级id
        parentDictName: '', // 字典父级名称
        meetingDictCode: '', // 	报警字典编码
        meetingDictName: '', // 报警字典名称
        color: '', // 颜色
        dictImage: '', // 图片url
        status: '' // 状态 0-启用 1-禁用
      }
      this.$refs.formInline.resetFields()
    },
    // 图片相关
    fileChange(file, fileList) {
      this.fileList = fileList
    },
    beforeAvatarUpload(file) {
      const isLt2M = file.size / 1024 / 1024 > 2
      if (isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
        return false
      }
      if (file.name.indexOf(',') != -1) {
        this.$message.error('非法的文件名')
        return false
      }
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogImageVisible = true
    },
    // 查看图片
    viewImage(row) {
      if (!row.dictImage) {
        this.$message.info('当前没有可预览的图片')
        return
      }
      this.dialogImageUrl = this.$tools.imgUrlTranslation(row.dictImage)
      this.dialogImageVisible = true
    },
    handleRemove(file, fileList) {
      this.hideUpload = false
      this.formInline.dictImage = ''
      this.fileList = fileList
    },
    httpRequset(item) {
      let formData = new FormData()
      formData.append('file', item.file)
      this.$api.spaceaUpload(formData).then((res) => {
        if (res.code == 200) {
          this.formInline.dictImage = res.data
          this.$message({
            message: '上传成功',
            type: 'success'
          })
        } else {
          this.$message.error(res.msg)
        }
      })
    },

    // 获取上级字典值
    getDictList() {
      this.$api.meetingDictTree({ dictType: this.checkedData.id }).then((res) => {
        if (res.code == 200) {
          let treeData = res.data.filter((item) => item.level !== 3)
          this.allDictTreeData = transData(treeData, 'id', 'parentId', 'children')
        }
      })
    },
    getItemData(item) {
      this.formInline.parentDictName = item.name
    },
    alarmDictIdChange(value) {
      let node = this.$refs.alarmDictId.getCheckedNodes()
      this.formInline.parentDictName = node[0].label
    },
    handleListEvent(type, row = {}) {
      this.operationType = type
      if (type == 'status') {
        return this.updateMettingDictStatus(row)
      } else if (type == 'del') {
        return this.delMettingDictData(row)
      }
      const dialogTitle = {
        add: '新增',
        edit: '修改',
        detail: '查看',
        addSubordinate: '新增下级'
      }
      this.diaTitle = dialogTitle[this.operationType] + '字典值'
      this.dialogVisible = true
      this.getDictList()
      if (type == 'edit' || type == 'detail') {
        this.rowId = row.id
        this.getMettingDetail(this.rowId)
      }
      if (type == 'addSubordinate') {
        this.formInline.parentId = row.id
        this.formInline.parentDictName = row.name
      }
    },
    submit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let params = {
            ...this.formInline,
            dictType: this.checkedData.id
          }
          if (this.operationType == 'edit') {
            params.id = this.rowId
          }
          this.$api.meetingDictSaveOrUpdate(params).then((res) => {
            if (res.code == 200) {
              this.$message.success('保存成功')
              this.searchForm()
              this.dialogClosed()
            } else {
              this.$message.error(res.message)
            }
          })
        } else {
          return false
        }
      })
    },
    // 获取字典详情
    getMettingDetail(id) {
      this.$api.meetingDictDetail({ id: id }).then((res) => {
        if (res.code == 200) {
          this.formInline = res.data
          if (res.data.dictImage) {
            this.hideUpload = true
            this.fileList = [
              {
                url: this.$tools.imgUrlTranslation(res.data.dictImage)
              }
            ]
          } else {
            this.hideUpload = this.operationType == 'detail'
            this.fileList = []
          }
        }
      })
    },
    // 改变字典状态
    updateMettingDictStatus(row) {
      this.$api.meetingDictStatus({ id: row.id, status: row.status == 0 ? 1 : 0 }).then((res) => {
        if (res.code == 200) {
          this.$message({ message: res.msg, type: 'success' })
          this.getDataList()
        } else {
          this.$message({ message: res.msg, type: 'error' })
        }
      })
    },
    // 删除字典
    delMettingDictData(row) {
      if (row.status === 0) {
        this.$message.error('已启用的字典不允许删除！')
        return false
      }
      this.$confirm('确认删除当前字典?', '提示', {
        cancelButtonClass: 'el-button--primary is-plain',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$api.meetingDictDelete({ id: row.id }).then((res) => {
            if (res.code == 200) {
              this.$message({ message: '删除成功', type: 'success' })
              this.getDataList()
            } else {
              this.$message({ message: res.message, type: 'error' })
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: linear-gradient(to right, #d9e1f8, #fff);
}
::v-deep .el-tree-node__content {
  height: 32px;
}
.configuration-content {
  height: 100%;
  display: flex;
  .configuration-content-left {
    width: 246px;
    height: 100%;
    padding: 10px;
    background: #fff;
    border-radius: 4px;
    margin-right: 12px;
    .left_content {
      height: calc(100% - 60px);
      overflow: auto;
    }
  }
  .configuration-content-right {
    height: 100%;
    min-width: 0;
    padding: 10px 16px;
    background: #fff;
    border-radius: 4px;
    flex: 1;
    .search-from {
      display: flex;
      justify-content: space-between;
      padding-bottom: 12px;
      ::v-deep .el-select,
      .el-input {
        width: 200px !important;
      }
      & > div {
        margin-right: 10px;
      }
      & > button {
        margin-top: 12px;
      }
    }
    .contentTable {
      height: calc(100% - 95px);
      display: flex;
      margin-top: 10px;
      flex-direction: column;
      .colorClass {
        display: flex;
        align-items: center;
        height: 32px;
        line-height: 32px;
      }
      ::v-deep .el-color-picker__trigger {
        border: none !important ;
        padding: 6px !important;
      }
      .contentTable-main {
        flex: 1;
        overflow: auto;
      }
      .contentTable-footer {
        padding: 10px 0 0;
      }
    }
  }
  .dot {
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    vertical-align: middle;
  }
  span {
    margin-left: 5px;
  }
  .status-color1 {
    .dot {
      background: #009a29;
    }
    color: #009a29;
  }
  .status-color2 {
    .dot {
      background: #cb2634;
    }
    color: #cb2634;
  }
  .diaContent {
    width: 100%;
    max-height: 500px !important;
    overflow: auto;
    background-color: #fff !important;
  }
  .ml-16 {
    margin-left: 16px;
  }
}
::v-deep .el-color-picker--small .el-color-picker__trigger {
  height: 32px !important;
  width: 32px !important;
}
::v-deep .el-color-picker--small {
  height: 32px !important;
}
::v-deep .el-input-group__prepend {
  padding: 0px !important;
  border: none !important;
}
::v-deep .deleteItem {
  color: #f53f3f !important;
}
.hide {
  ::v-deep .el-upload--picture-card {
    display: none;
  }
}
</style>
