<template>
  <div style="width: 100%;height: calc(100% - 66px);    background: #fff;">
    <el-button :type="!isShow ? 'primary' : 'default'" @click="setBaseUrl('new')"
      style="margin: 16px; ">新iot绘制</el-button>
    <el-button :type="isShow ? 'primary' : 'default'" @click="setBaseUrl('old')"
      style="margin: 16px;margin-left: 0;">旧iot绘制</el-button>
    <meta2d-vue ref="meta2dVue" :userInfo="userInfo" :baseUrl="baseUrl" :key="componentKey" v-if="!isShow" />
    <meta2d-vue ref="meta2dVue" :userInfo="userInfo" :baseUrl="baseUrl1" :key="componentKey" v-if="isShow" />
  </div>
</template>
<script>
export default {
  name: 'meta-2d',
  data() {
    return {
      isShow: false,
      componentKey: 0, // 用于强制重新加载组件
      requestHttp: __PATH.VUE_MONITOR_API,
      loading: false,
      baseUrl: __PATH.VUE_MONITOR_API,
      baseUrl1: __PATH.VUE_IEMC_API,
      userInfo: {
        avatarUrl: '',
        captcha: '',
        createdAt: '',
        deletedAt: '',
        email: '',
        id: '1',
        phone: '',
        updatedAt: '',
        username: 'admin',
        vip: 1,
        vipExpiry: '',
        isVip: true // 该属性是计算出来的，不是数据库中的
      }
    }
  },
  created() {
    // 网关需要token 组件获取token通过localStorage
    window.localStorage.setItem('token', 'Bearer ' + this.$store.state.user.token)
  },
  mounted() {
    console.log(this.$refs.meta2dVue, 666)
    console.log(this.$router.currentRoute.path, 777)
  },
  methods: {
    setBaseUrl(type) {
      if (type === 'new') {
        this.isShow = false
      } else if (type === 'old') {
        this.isShow = true
      }
      this.componentKey += 1;
    }
  }
}
</script>
<style lang="scss" scoped>
.scada-edit {
  height: 100%;
  overflow: hidden;
}
</style>