<template>
  <div class="taskDetail">
    <!-- 基本信息 -->
    <div class="baseInfo">
      <div class="toptip" style="padding: 0; border: 0; font-weight: bold;">
        <span class="green_line"></span>
        基本信息
      </div>
      <el-form ref="form" :model="form" label-width="80px">
        <el-row :gutter="60">
          <el-col :span="8">
            <el-form-item label="任务名称" prop="planName">
              <span>{{ form.taskName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="周期类型" prop="planName">
              <span>{{ form.taskName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="应巡日期" prop="planName">
              <span>{{ form.taskName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="应巡时间" prop="planName">
              <span>{{ form.taskName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="完成期限" prop="planName">
              <span>{{ form.taskName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="巡检部门" prop="planName">
              <span>{{ form.taskName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="巡检人员" prop="planName">
              <span>{{ form.taskName }}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <!-- 任务点 -->
    <div class="taskPoint">
      <div class="toptip" style="padding: 0; border: 0; font-weight: bold;">
        <span class="green_line"></span>
        任务点
      </div>
    </div>
  </div>
</template>
<script>
export default {
  components: {},
  props: ['taskId'],
  data() {
    return {
      planId: '',
      form: {
        taskName: 'name',
        cycleType: ''
      },
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      }
    }
  },
  created() {
    this.getInspectionPointList()
  },
  methods: {
    getInspectionPointList() {
      const data = {
        taskId: this.taskId,
        pageNo: this.paginationData.currentPage,
        pageSize: this.paginationData.pageSize
      }
      this.$api.getInspectionPointList(data).then(res => {
        if (res.code == '200') {
          console.log(res)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.taskDetail {
  padding: 0 15px 15px;
  width: 100%;
  height: 100%;
  background-color: #fff;

  .baseInfo {
    .el-col-8 {
      height: 45px;
    }
  }
}
</style>
