###
 # @Author: hedd
 # @Date: 2023-05-12 11:39:47
 # @LastEditTime: 2024-01-26 16:32:46
 # @FilePath: \ihcrs_pc\.env.bjsyzyyy
 # @Description: 北京顺义中医院
###
#页面标题
VUE_APP_TITLE = 智慧医院后勤综合监管平台
# 网关地址
VUE_APP_UNIFIED_SERVER = http://**********:20007/
# 字典地址
VUE_APP_DICTIONAR_SERVER = http://**********:20007/iemc_dict/
# minio地址
VUE_APP_MINIO_SERVER = http://**********:9000/
# websocket地址
VUE_APP_WS_SERVER = ws://**********:20007/
# 消息服务websocket地址
VUE_APP_NEWS_WS_SERVER = ''
# rtsp服务websocket地址
VUE_APP_RTSP_LIVE_WS_SERVER = ''
# 路由前缀
VUE_APP_BASE_URL_SUFFIX = /
# 医院版本
VUE_APP_HOSPITAL_NODE_ENV = bjsyzyyy


# 能耗接口地址
VUE_APP_ENERGY_API = ''
# 资产一站式
VUE_APP_IEMS_IOMS_API = ''
# 资产
VUE_APP_IEMS_API = ''
# 入口筛查
VUE_APP_BASE_SAFE =  http://*************:9291/
# VUE_IEMC_API: '', // iemc
# VUE_IEMC_ELEVATOR_API: 'http://*************:8099/', // iemc电梯
# VUE_SYS_API: 'http://**********:20007/' + 'sinomis-authweb/', // 系统管理
# VUE_WARN_API: '', // 报警
# VUE_ICIS_API: 'http://**********:20007/' + 'inspection/', // 巡检
# VUE_IOMS_API: '', // 一站式
# VUE_AQ_URL: '', //安全
# VUE_APP_IMWS_API: '', // 医废
# VUE_SPACE_API: 'http://**********:20007/' + 'base_info/', // 基础信息
# VUE_SPACE_FILE: 'http://**********:20007/' + 'base_info_file/', // 基础信息上传
# VUE_ICIS_LZP: '', // 网关基础服务
# VUE_NEWS_API: '', // 消息公告
# DICTIONAR_URL: '', // iemc字典
# WS_URL: '', // iemc 报警websocket
# VUE_MINIO_API: 'http://**********:9000/', // minio
# WS_NEWS_API: '', // 消息websocket
# VUE_APP_RTSP_LIVE_WS_SERVER: '', // rtsp websocket
# VUE_APP_HOSPITAL_NODE_ENV: 'bjsyzyyy', // 对应医院环境 sinomis/bjsjtyy/gqgjzyy/jxsdeyy
# VUE_ENERGY_API: '', // 能耗接口地址
# SPACE_API: 'http://**********:20007/'+'sinomis-service-manage/', // 空间
