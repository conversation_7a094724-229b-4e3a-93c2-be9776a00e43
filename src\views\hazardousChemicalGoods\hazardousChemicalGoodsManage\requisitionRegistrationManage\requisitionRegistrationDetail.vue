<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="content-title" @click="$router.go(-1)"><i class="el-icon-arrow-left"></i>
        领用申请单</div>
      <div class="content_box">
        <div class="content-table" id="printApplyDiv" style="width:800px">
          <h2 class="sino-detail-title" style=" letter-spacing: 12px;text-align: center">领用申请单</h2>
          <div class="detailInfo" style="display: flex;flex-wrap: wrap;">
            <div v-for="(item,index) in applyList" :key="index" class="list" style="width:calc(100%/3)">
              <div class="item" style="display: flex">
                <div class="item-label">{{item.label}}</div>
                <div class="item-value">{{detailInfo[item.value]}}</div>
              </div>
            </div>
          </div>
          <el-table v-loading="tableLoading" :data="tableData" row-key="id" stripe border :header-cell-style="rowClass"
            style="width:100%;margin-top:20px" :cell-style="cellStyle">
            <el-table-column label="序号" type="index" width="60"></el-table-column>
            <el-table-column prop="materialTypeName" label="危化品分类"></el-table-column>
            <el-table-column prop="materialCode" label="危化品编码"></el-table-column>
            <el-table-column prop="materialName" label="危化品名称"></el-table-column>
            <el-table-column prop="model" label="规格型号"></el-table-column>
            <el-table-column prop="basicUnitName" label="基础单位"></el-table-column>
            <el-table-column prop="amount" label="报废数量"></el-table-column>
            <!-- <el-table-column prop="trademark" label="品牌"></el-table-column> -->
            <el-table-column prop="manufacturerName" label="生产厂家"></el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">返回</el-button>
      <!-- <el-button type="primary" @click="print">打印</el-button> -->
    </div>
  </PageContainer>
</template>
<script>
export default {
  name: 'requisitionRegistrationDetail',
  data() {
    return {
      tableLoading: false,
      tableData: [],
      applyList: [
        {
          label: "领用单号：",
          value: "recordNumber"
        },
        {
          label: "申请人：",
          value: "createName"
        },
        {
          label: "申请时间：",
          value: "createTime"
        },
        {
          label: "申请人电话：",
          value: "applyPeoplePhone"
        },
        {
          label: "领用类型：",
          value: "receiveTypeName"
        },
        {
          label: "总数量：",
          value: "applyCount"
        },
        {
          label: "备注：",
          value: "remarks",
        }
      ],
      detailInfo: {
        recordNumber: '',
        createName: '',
        createTime: '',
        applyPeoplePhone: '',
        projectTypeName: '',
        applyCount: '',
        remarks: '',
      },
    }
  },

  mounted() {
    this.getApplyInfoData()
  },
  methods: {
    //获取领用申请详情
    getApplyInfoData() {
      this.tableLoading = true
      const userInfo = this.$store.state.user.userInfo.user
      let params = {
        id: this.$route.query.applyId,
        userId: userInfo.staffId,
        userName: userInfo.staffName,
      }
      this.$api.getReceiveApplyRecordById(params).then((res) => {
        this.tableLoading = false
        if (res.code == '200') {
          this.detailInfo = res.data
          this.tableData = res.data.materialRecordList
        }
      })
    },
    rowClass() {
      return "color:#000;padding:0";
    },
    cellStyle() {
      return "color:#000;padding:0";
    },
    //打印
    print() {
      let subOutputRankPrint = document.getElementById("printApplyDiv");
      let newContent = subOutputRankPrint.innerHTML;
      let oldContent = document.body.innerHTML;
      document.body.innerHTML = newContent;
      window.print();
      window.location.reload();
      document.body.innerHTML = oldContent;
      return false;
    },
  }
}
</script>
<style scoped lang="scss">
.container-content > div {
  background-color: #fff;
  height: 100%;
  position: relative;
  .content-title {
    border-bottom: 1px solid #e4e7ed;
    padding: 12px 24px;
    cursor: pointer;
    margin-bottom: 24px;
  }
  .content_box {
    padding: 0 24px;
    background: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow-y: auto;
    height: calc(100% - 90px);
    .content-table {
      h2 {
        letter-spacing: 12px;
        text-align: center;
      }
      .detailInfo {
        margin: auto;
        display: flex;
        flex-wrap: wrap;
        .list {
          display: inline-block;
          width: 33.3333%;
          line-height: 25px;
          color: #000;
          .item {
            display: flex;
            align-items: center;
            .item-label {
              font-weight: bold;
              color: #000;
            }
          }
        }
      }
    }
  }
}
::v-deep .el-table__header {
  width: 100% !important;
}
::v-deep .el-table .el-table__header .el-table__cell {
  color: #333 !important;
  font-weight: bold !important;
  border-color: #000 !important;
}
::v-deep .el-table {
  border-color: #000 !important;
  border-bottom: 1px solid #000;
  border-right: 1px solid #000;
  .el-table__header {
    .el-table__cell {
      border-bottom: 1px solid #000;
      border-right: 1px solid #000;
      background-color: #ffffff !important;
    }
  }
  .el-table__row {
    .el-table__cell {
      border-bottom: 1px solid #000;
      border-right: 1px solid #000;
      padding: 0;
    }
  }
}
</style>
