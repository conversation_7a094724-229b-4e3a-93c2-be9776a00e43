<!-- 巡检监测 -->
<template>
  <ContentCard :title="item.componentTitle" :scrollbarHover="true" :cstyle="{ height: '100%' }" class="drag_class"  :hasMoreOper="['edit']" @more-oper-event="(val) => $emit('all-more-Oper', val, 'patrolMonitor')">
    <div slot="title-right" class="data-btns">
      <span v-for="item in dataTypeList" :key="item.type" :class="{ 'active-btn': selectDataType == item.type }" @click="changeDateType(item.type)">{{item.name}}</span>
    </div>
    <div slot="content" class="patrol-main">
      <div class="main-head">
        <el-progress type="circle" :width="120" :stroke-width="24" strokeLinecap="" color="#FF9435" define-back-color="#E4E7ED" :show-text="false" :percentage="patrolData.percentage"/>
        <p class="info-box">
          <span class="info-chart"></span>
          <span class="info-name">巡检完成率</span>
          <span class="info-num">{{ patrolData.percentage }}</span>
          <span class="info-unit">%</span>
        </p>
      </div>
      <div class="main-bottom">
        <div class="bottom-item">
          <p class="item-name">应巡</p>
          <p>
            <span class="item-num">{{ patrolData.sum }}</span>
            <span class="item-unit">次</span>
          </p>
        </div>
        <div class="bottom-item">
          <p class="item-name">已巡</p>
          <p>
            <span class="item-num">{{ patrolData.accomplishCount }}</span>
            <span class="item-unit">次</span>
          </p>
        </div>
      </div>
    </div>
  </ContentCard>
</template>

<script>
import { dataTypeList } from '@/util/dict.js'
export default {
  name: 'patrolMonitor',
  props: {
    item: {
      type: Object,
      default: () => {}
    },
    requestInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dataTypeList,
      selectDataType: 'day',
      assetInfo: {}, // 资产id
      patrolData: {}
    }
  },
  computed: {

  },
  watch: {
    requestInfo: {
      handler(val, oldVal) {
        if (val.entityMenuCode != oldVal.entityMenuCode) {
          this.getSpaceAssetInfo()
        }
      },
      deep: true
    }
  },
  created() {
    this.getSpaceAssetInfo()
  },
  methods: {
    // 获取一级菜单下监测实体绑定的资产信息
    getSpaceAssetInfo() {
      this.$api.GetSpaceAssetInfo(this.requestInfo).then((res) => {
        if (res.code == 200) {
          this.assetInfo = res.data
          this.spatialEquipmentStatistics()
        }
      })
    },
    // 获取巡检数据
    spatialEquipmentStatistics() {
      let params = {
        systemCode: 1,
        dateType: this.selectDataType,
        roomCode: this.assetInfo.spaceTypeIds,
        engineerCode: this.assetInfo.assetIds
      }
      this.$api.SpatialEquipmentStatistics(params).then((res) => {
        if (res.code == 200) {
          this.patrolData = res.data
        }
      })
    },
    // 切换日期类型
    changeDateType(type) {
      this.selectDataType = type
      this.spatialEquipmentStatistics()
    }
  }
}

</script>

<style lang="scss" scoped>
.patrol-main {
  height: 100%;

  p {
    margin: 0;
  }

  .main-head {
    height: 65%;
    display: flex;
    align-items: center;
    justify-content: space-evenly;

    .info-box {
      display: flex;
      align-items: center;
    }

    .info-chart {
      display: inline-block;
      width: 8px;
      height: 8px;
      background-color: #ff9435;
    }

    .info-name {
      font-size: 14px;
      font-weight: 500;
      color: #121f3e;
      margin-left: 6px;
    }

    .info-num {
      font-size: 24px;
      font-weight: bold;
      color: #121f3e;
      margin-left: 10px;
      line-height: 22px;
    }

    .info-unit {
      font-size: 15px;
      font-weight: 500;
      color: #ccced3;
      margin-left: 4px;
      line-height: 22px;
    }
  }

  .main-bottom {
    height: 35%;
    display: flex;
    justify-content: space-between;

    .bottom-item {
      padding: 16px;
      width: calc(50% - 5px);
      height: 100%;
      background: #faf9fc;
      border-radius: 4px;
      display: flex;
      flex-direction: column;
      justify-content: space-evenly;
      text-align: center;

      .item-name {
        font-size: 15px;
        font-weight: 500;
        color: #121f3e;
        line-height: 18px;
      }

      .item-num {
        font-size: 20px;
        font-weight: bold;
        color: #121f3e;
        line-height: 36px;
      }

      .item-unit {
        font-size: 15px;
        font-weight: 500;
        color: #ccced3;
        line-height: 18px;
        margin-left: 4px;
      }
    }
  }
}

.data-btns {
  position: absolute;
  right: 36px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  & > span {
    width: 44px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    margin: 0 4px;
    background-color: #f6f5fa;
    font-size: 14px;
    font-family: "PingFang SC-Medium", "PingFang SC";
    border: 1px solid #ededf5;
    border-radius: 2px;
    color: #7f848c;
    cursor: pointer;
  }

  .active-btn {
    background-color: #3562db !important;
    color: #fff !important;
    border-color: #3562db !important;
  }
}
</style>
