<template>
  <ControlStrategy :projectCode="projectCode" />
</template>

<script>
import ControlStrategy from '../airAndLightingCom/controlStrategy.vue'
import { monitorTypeList } from '@/util/dict.js'
export default {
  components: {
    ControlStrategy
  },
  data() {
    return {
      projectCode: monitorTypeList.find((item) => item.projectName == '照明监测').projectCode
    }
  },
  mounted() {
    
  },

  methods: {
    
  }
}
</script>
<style lang="scss" scoped>

</style>
