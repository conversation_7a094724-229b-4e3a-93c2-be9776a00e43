<template>
  <div style="height: 100%;">
    <div class="content_box">
      <div style="display: flex;" class="sino-tabs">
        <div v-if="type == 'check'" class="sino-tabs-item">
          <span :class="['item-text', isActive == 1 ? 'active' : '']" @click="tabsSwitch(1)">风险点详情</span>
        </div>
        <div class="sino-tabs-item">
          <span :class="['item-text', isActive == 2 ? 'active' : '']" @click="tabsSwitch(2)">风险巡查清单</span>
        </div>
        <div v-if="type == 'check'" class="sino-tabs-item">
          <span :class="['item-text', isActive == 3 ? 'active' : '']" @click="tabsSwitch(3)">风险巡查记录</span>
        </div>
      </div>
      <div class="continer">
        <addRisk v-if="isActive == 1" :checkType="checkType"></addRisk>
        <riskTroubleshooting v-if="isActive == 2"></riskTroubleshooting>
        <risktroubleshootLists v-if="isActive == 3"></risktroubleshootLists>
      </div>
    </div>
  </div>
</template>

<script>
import { transData } from '@/util'
import addRisk from '../riskPointManagementList/addRisk.vue'
import riskTroubleshooting from './riskTroubleshooting'
import risktroubleshootLists from './risktroubleshootLists'
export default {
  name: 'riskDetail',
  components: {
    riskTroubleshooting,
    addRisk,
    risktroubleshootLists
  },
  beforeRouteEnter(to, from, next) {
    if (to.meta.title) {
      const typeList = {
        add: '新增巡查清单',
        edit: '编辑巡查清单',
        check: '详情'
      }
      to.meta.title = typeList[to.query.type] ?? '详情'
    }
    next((vm) => {
      //  从风险排查清单进来的默认选中风险排查清单标签
      if (from.name === 'RiskPointsChecklist') {
        vm.isActive = 2
      } else if (to.name == 'ipsmRiskDetail') {
        vm.isActive = 3
      }
    })
  },
  data() {
    return {
      checkType: 'check',
      isActive: 2,
      type: '',
      title: '',
      loading: false
    }
  },
  //   activated(){
  //     this.type = this.$route.query.type
  //     if(this.type !='check'){
  //       this.isActive = 2
  //     }
  //   },
  created() {
    this.type = this.$route.query.type
  },
  methods: {
    tabsSwitch(type) {
      this.isActive = type
    },
    complete() {}
  }
}
</script>

<style lang="scss" scoped>
.sino-tabs {
  box-sizing: border-box;
  height: 50px;
  line-height: 50px;
  padding-left: 25px;
  margin: 15px 15px 0;
  display: flex;
  text-align: left;
  background: #fff;
  border-bottom: 1px solid #d8dee7;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;

  .sino-tabs-item {
    display: inline-block;
    // width: 100px
    padding-right: 24px;

    .item-text {
      height: 48px;
      font-size: 16px;
      display: inline-block;
      cursor: pointer;
      color: rgb(96 98 102 / 100%);
    }

    .active {
      color: #5188fc;
      border-bottom: 3px solid #5188fc;
    }
  }
}

.content_box {
  height: calc(100% - 20px);
  overflow-y: auto;
  // margin: 15px;
  // padding: 20px;
  background: #f0f0f3;
  display: flex;
  flex-direction: column;
}

.continer {
  height: calc(100% - 80px);
}
</style>
