<template>
  <ContentCard :title="item.componentTitleShow" :scrollbarHover="true" :cstyle="{ width: '100%', height: '100%' }"
    class="drag_class" :hasMoreOper="['edit']"
    @more-oper-event="(val) => $emit('all-more-Oper', val, item.componentName, item.chartType)">
    <div slot="content" class="content_top">
      <p><span><i v-if="type == 1">今日</i>运行时长</span> <em>{{ jobsData.runTime }}</em></p>
      <p><span><i v-if="type == 1">今日</i>运行距离</span> <em>{{ jobsData.runDistance }}</em></p>
      <p><span><i v-if="type == 1">今日</i>运行次数</span> <em>{{ jobsData.runNum }}</em></p>
      <p><span><i v-if="type == 1">今日</i>开门次数</span> <em>{{ jobsData.doorOpenedCnt }}</em></p>
    </div>
  </ContentCard>
</template>
<script>
export default {
  name: 'wbnjxx',
  props: {
    item: {
      type: Object,
      default: () => { }
    },
    systemCode: {
      type: String,
      default: ''
    }
  },
  created() {
  },
  mounted() {
    this.dtyxData()
  },
  data() {
    return {
      jobsData: {},
      type: "",
    }
  },
  methods: {
    dtyxData() {
      if (this.item.componentDataType === 'jryx') {
        this.$api.dtToDayOperation().then((res) => {
          if (res.code === "200") {
            this.type = 1
            this.jobsData = res.data
          } else {
            this.hasChart = false
          }
        })
      } else if (this.item.componentDataType === 'zgyx') {
        this.$api.dtTotalOperation().then((res) => {
          if (res.code === "200") {
            this.type = 2
            this.jobsData = res.data
          } else {
            this.hasChart = false
          }
        })
      }
    },
  }
}
</script>
<style lang="scss" scoped>
.content_top {
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  justify-content: center;

  p {
    flex: 1 1 calc(50% - 10px);
    height: 47%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    line-height: 26px;
    font-size: 14px;
    color: #666;
    background-color: #FAF9FC;
    padding: 10px;
    margin: 5px;

    span {
      display: inline;
    }

    i {
      font-style: normal;
    }

    em {
      font-style: normal;
      font-size: 18px;
      font-weight: bold;
    }
  }
}
</style>
