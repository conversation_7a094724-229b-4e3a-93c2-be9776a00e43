<template>
  <el-dialog class="sino-dialog-tableList" :title="title" :visible.sync="dialogVisibleWz" :before-close="closeDialog">
    <div class="content_box">
      <div class="left_tree">
        <el-tree
          ref="areaTree"
          v-loading="treeLoading"
          :data="treeData"
          :props="defaultRegionL"
          node-key="id"
          :default-expanded-keys="defaultKeys"
          :current-node-key="id"
          @node-click="handleNodeClick"
        ></el-tree>
      </div>
      <div class="right_table">
        <div class="top-search">
          <el-input v-model="dialogFilters.regionName" placeholder="输入区域名称" style="margin-right: 10px;"></el-input>
          <el-button class="sino-button-search" @click="dialogReset">重置</el-button>
          <el-button class="sino-button-sure-search" @click="dialogSearch">查询</el-button>
        </div>
        <div class="hasSelected">
          <div style="color: #5b5b5b; font-weight: 600;">已选择：</div>
          <span v-if="!multipleSelection.length">暂无</span>
          <div v-for="(item, index) in multipleSelection" v-else :key="index" style="color: #5b5b5b; font-weight: 600;">
            <span>{{ item.address }}</span>
            <span v-show="multipleSelection.length > 1 && index !== multipleSelection.length - 1">、</span>
          </div>
        </div>
        <div class="tabel-list">
          <el-table
            ref="materialTable"
            v-loading="dialogTableLoading"
            :data="tableWz"
            :border="true"
            stripe
            height="300"
            :cell-style="{ padding: '8px' }"
            style="overflow: auto;"
            :row-key="getRowKeys"
            :header-cell-class-name="cellClass"
            class="table"
            @selection-change="handleSelectionChangeDialog"
          >
            <el-table-column :reserve-selection="true" type="selection" width="55"></el-table-column>
            <el-table-column type="index" label="序号" width="65">
              <template slot-scope="scope">
                <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="address" label="网格名称" show-overflow-tooltip></el-table-column>
            <el-table-column prop="gridLevel" label="网格级别" show-overflow-tooltip></el-table-column>
            <el-table-column prop="regionFloorName" label="上级网格" show-overflow-tooltip></el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <div class="table-page">
      <el-pagination
        class="selectSpace"
        :current-page="paginationData.currentPage"
        :page-sizes="[15, 30, 50, 100]"
        :page-size="paginationData.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        style="margin-bottom: 20px; position: absolute; bottom: 41px; right: 20px;"
        :total="paginationData.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button class="sino-button-cancel" @click="closeDialog">取 消</el-button>
      <el-button type="primary" class="sino-button-sure" @click="sureWz">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script type="text/ecmascript-6">
export default {
  name: '',
  props: {
    title: {
      type: String,
      default: '选择区域'
    },
    dialogVisibleWz: {
      type: Boolean,
      default: false
    },

    spaceIds: {
      type: String,
      default: ''
    },
    spaceCategoryId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      tableWz: [],
      dialogTableLoading: false,
      dialogFilters: {
        regionName: ''
      },
      treeData: [],
      defaultRegionL: {
        children: 'children',
        label: 'gridName',
        value: 'id'
      },
      id: '',
      treeLoading: false,
      defaultKeys: [],
      multipleSelection: []
    }
  },
  created() {
    // console.log(this.spaceIds);
    this.getRegionTypeByCompanyCodeFn() // 获取表格数据
    this.getRegionListFn() // 获取树结构
  },
  methods: {
    getRowKeys(row) {
      return row.id
    },
    cellClass(row) {
      if (row.columnIndex === 0) {
        return 'DisableSelection'
      }
    },
    handleSelectionChangeDialog(val) {
      // console.log(val)
      this.multipleSelection = val
      if (val.length > 1) {
        // 将复选框多选改为单选
        this.$refs.materialTable.clearSelection()
        this.$refs.materialTable.toggleRowSelection(val.pop())
      }
    },
    /**
     * 点击树状图
     */
    handleNodeClick(val) {
      this.id = val.id
      this.paginationData = {
        currentPage: 1,
        pageSize: 15,
        total: 0
      }
      this.$refs.materialTable.clearSelection()
      this.getRegionTypeByCompanyCodeFn(this.id)
    },
    /**
     * 获取table列表数据
     */
    getRegionTypeByCompanyCodeFn(ids) {
      console.log('获取table数据')
      this.dialogTableLoading = true
      this.$api
        .getRegionTypeByCompanyCode({
          spaceCategoryId: '',
          pageNo: this.paginationData.currentPage,
          pageSize: this.paginationData.pageSize,
          personCode: JSON.parse(sessionStorage.getItem('LOGINDATA')).id,
          // regionName: this.dialogFilters.regionName,
          id: ids,
          unitCode: 'ZXYSZGDW',
          hospitalCode: 'ZKYXYY'
        })
        .then((res) => {
          if (res.code == 200) {
            this.tableWz = res.data.list.map((item, index) => {
              item.id = index + 1
              return item
            })
            this.paginationData.total = res.data.count
          }
          this.dialogTableLoading = false
        })
    },
    /**
     * 获取区域数据
     */
    getRegionListFn() {
      this.treeLoading = true
      var _self = this
      this.$api
        .getRegionList({
          // unitCode: JSON.parse(sessionStorage.getItem("LOGINDATA")).unitCode,
          // hospitalCode: JSON.parse(sessionStorage.getItem("LOGINDATA")).hospitalCode
          unitCode: 'ZXYSZGDW',
          hospitalCode: 'ZKYXYY'
        })
        .then((res) => {
          this.treeLoading = false
          if (res.code == 200) {
            res.data.list.forEach((element) => {
              if (element.gridLevel == 3) {
                this.defaultKeys.push(element.id)
              }
            })
            this.treeData = this.$tools.transData(res.data.list, 'id', 'parentGridId', 'children')
            if (this.treeData && this.treeData.length > 0) {
              this.id = this.treeData[0].id
              _self.getRegionTypeByCompanyCodeFn(this.treeData[0].id)
            }
          }
        })
    },
    /**
     * 点击确认
     */
    sureWz() {
      if (this.multipleSelection.length > 1) {
        this.$message.error('最多只能选择一个区域!')
        return
      }
      this.$emit('addMaterials', this.multipleSelection)
      this.closeDialog()
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.getRegionTypeByCompanyCodeFn()
    },
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.getRegionTypeByCompanyCodeFn()
    },
    closeDialog() {
      this.$emit('closeDialog')
    },
    checkSelectable(val) {
      return val.taskId
    },
    getindex(val) {
      return val.id
    },

    // 重置
    dialogReset() {
      this.paginationData = {
        currentPage: 1,
        pageSize: 15,
        total: 0
      }
      this.id = ''
      this.$nextTick(() => {
        this.$refs.areaTree.setCurrentKey()
      })
      this.dialogFilters.regionName = ''
      this.getRegionTypeByCompanyCodeFn()
    },

    // 条件查询
    dialogSearch() {
      this.paginationData.currentPage = 1
      this.getRegionTypeByCompanyCodeFn()
    },

    checkboxSelect(row) {
      let roomCode = []
      this.pitchData.forEach((item) => {
        roomCode.push(item.backId)
      })
      if (roomCode.indexOf(row.regionCode) > -1) {
        return false
      } else {
        return true
      }
    }
  }
}
</script>
<style lang="scss" type="stylesheet/stylus" scoped>
:deep(.el-dialog) {
  height: 580px;
}
.content_box {
  display: flex;
  background-color: #fff;
}
.selectSpace :deep(.el-input__inner) {
  height: 25px !important;
  line-height: 25px !important;
}
.left_tree {
  width: 248px;
  height: 403px;
  overflow-y: auto;
  margin-right: 10px;
}
.right_table {
  flex: 1;
}
thead :deep(.el-checkbox__inner) {
  display: none !important;
}
.table :deep(.DisableSelection > .cell) {
  display: none !important;
}
.sino-dialog-tableList {
  .hasSelected {
    display: flex;
    align-items: center;
    margin: 15px 0;
    flex-wrap: wrap;
  }
}
</style>
