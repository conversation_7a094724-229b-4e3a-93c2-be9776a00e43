<template>
  <div v-cloak id="app">
    <RouterView />
    <audio ref="audio" controls="controls" muted="muted" hidden src="@/assets/images/elevator/elevatorAlarmAudio.mp3"></audio>
    <!-- <el-button style="position: fixed;top: 0;z-index: 9999;" @click="sendAlarm">发送报警</el-button> -->
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
let lockReconnects = false
let websocketAlarm = null
let iemcLockReconnects = false
let iemcWebsocket = null
let elevatorLockReconnects = false
let elevatorWebsocket = null
export default {
  provide() {
    return {
      generateI18nTitle: this.generateI18nTitle
    }
  },
  data() {
    return {
      SpeechMsg: new SpeechSynthesisUtterance(),
      SpeechSynth: window.speechSynthesis,
      playList: [],
      elevatorPlayCount: 0
    }
  },
  computed: {
    ...mapGetters({
      isLogin: 'user/isLogin',
      socketMsgs: 'socket/socketMsgs'
    })
  },
  watch: {
    // $route: {
    //   handler: 'routeChange',
    //   immediate: true
    // },
    '$store.state.keepAlive.list'(val) {
      process.env.NODE_ENV == 'development' && console.log(`[ keepAliveList ] ${val}`)
    },
    '$store.state.settings.mode': {
      handler() {
        if (this.$store.state.settings.mode == 'pc') {
          // this.$store.commit('settings/updateThemeSetting', {
          //   sidebarCollapse: this.$store.state.settings.sidebarCollapseLastStatus
          // })
        } else if (this.$store.state.settings.mode == 'mobile') {
          this.$store.commit('settings/toggleSidebarCollapse', false)
          // this.$store.commit('settings/updateThemeSetting', {
          //   sidebarCollapse: true
          // })
        }
        document.body.setAttribute('data-mode', this.$store.state.settings.mode)
      },
      immediate: true
    },
    '$store.state.settings.layout': {
      handler() {
        document.body.setAttribute('data-layout', this.$store.state.settings.layout)
      },
      immediate: true
    },
    '$store.state.settings.theme': {
      handler() {
        document.body.setAttribute('data-theme', this.$store.state.settings.theme)
      },
      immediate: true
    },
    '$store.state.settings.showHeader': {
      handler() {
        document.body.removeAttribute('data-no-main-sidebar')
        if (this.$store.state.settings.showHeader || (this.$store.state.menu.routes.length < 1 && !this.$store.state.settings.alwaysShowMainSidebar)) {
          document.body.setAttribute('data-no-main-sidebar', '')
        }
      },
      immediate: true
    },
    '$store.state.menu.routes': {
      handler() {
        document.body.removeAttribute('data-no-main-sidebar')
        if (this.$store.state.settings.showHeader || (this.$store.state.menu.routes.length < 1 && !this.$store.state.settings.alwaysShowMainSidebar)) {
          document.body.setAttribute('data-no-main-sidebar', '')
        }
      },
      immediate: true,
      deep: true
    },
    '$store.state.settings.sidebarCollapse': {
      handler() {
        document.body.removeAttribute('data-sidebar-no-collapse')
        document.body.removeAttribute('data-sidebar-collapse')
        if (this.$store.state.settings.sidebarCollapse) {
          document.body.setAttribute('data-sidebar-collapse', '')
        } else {
          document.body.setAttribute('data-sidebar-no-collapse', '')
        }
      },
      immediate: true
    },
    isLogin(flag) {
      if (flag) {
        this.getMessageWebsocket()
        this.getWebsocket()
        this.getIemcWebsocket(1)
        this.getElevatorWebsocket(1)
      } else {
        websocketAlarm && websocketAlarm.close()
      }
    },
    socketMsgs(data) {
      const jsonData = JSON.parse(data)
      const alarmData = jsonData.data
      if (alarmData?.webAlarmResponseWay?.includes('2')) {
        this.playList.push({
          playCount: alarmData.webAlarmResponseNum,
          playText: alarmData.webBroadcastMsg
        })
      }
      console.log('报警播报:', this.playList)
      setTimeout(() => {
        this.playNextAudio()
      }, 300)
    }
  },
  mounted() {
    // 检测浏览器路由改变页面不刷新问题,hash模式的工作原理是hashchange事件
    window.addEventListener(
      'hashchange',
      () => {
        let currentPath = window.location.hash.slice(1)
        if (this.$route.path !== currentPath) {
          this.$router.push(currentPath)
        }
      },
      false
    )
    window.onresize = () => {
      this.$store.commit('settings/setMode', document.body.clientWidth)
    }
    window.onresize()
    this.SpeechMsg.addEventListener('end', () => {
      // 语音播放结束后，延迟2秒播放下一条
      setTimeout(() => {
        this.playNextAudio()
      }, 1000)
    })
    if (this.$store.getters['user/isLogin']) {
      // this.getMessageWebsocket()
      // this.getWebsocket()
      // this.getIemcWebsocket(1)
      // this.getElevatorWebsocket(1)
    } else {
      websocketAlarm && websocketAlarm.close()
    }
  },
  methods: {
    // 路由 title 转国际化，如果没有配置则默认显示 title
    generateI18nTitle(key, defaultTitle) {
      let title
      if (this.$te(key)) {
        title = this.$t(key)
      } else {
        title = defaultTitle
      }
      return title
    },
    // 监听路由变化，更新页面 title
    routeChange() {
      // console.log('routeChange', this.$route.name)
      // this.$route.meta.title && this.$store.commit('settings/setTitle', this.generateI18nTitle(this.$route.meta.i18n, this.$route.meta.title))
    },
    sendAlarm() {
      const alarmData = {
        data: {
          webAlarmResponseWay: '1,2',
          webAlarmResponseNum: 2,
          webBroadcastMsg: '中国医学科学院肿瘤医院深圳医院>主院区>地下建筑物>B1' + Math.random(),
          alarmData: {
            alarmId: 'BJ2024712101921720750742401'
          }
        }
      }
      this.$store.commit('socket/setSocketMsgs', JSON.stringify(alarmData))
    },
    // 播放下一条语音
    playNextAudio() {
      if (this.playList && this.playList.length) {
        if (this.playList[0].playCount > 0) {
          this.handleSpeak(this.playList[0].playText)
          this.playList[0].playCount--
        } else {
          // 播放次数为0，从播放列表中移除
          this.playList.shift()
          if (this.playList.length > 0) {
            this.handleSpeak(this.playList[0].playText)
          }
        }
      } else {
        // this.SpeechSynth.cancel()
        console.log('播放列表为空')
      }
    },
    // 播放语音事件
    handleSpeak(text) {
      this.SpeechMsg.text = text // 文字内容
      this.SpeechMsg.lang = 'zh-CN' // 使用的语言:中文
      this.SpeechMsg.volume = 1 // 声音音量：（0-1）
      this.SpeechMsg.rate = 1 // 语速：（0.1-10）
      this.SpeechMsg.pitch = 2 // 音高：（0-2）
      this.SpeechSynth.speak(this.SpeechMsg) // 播放
    },
    // 消息服务 websocket
    getMessageWebsocket() {
      let vm = this
      let userInfo = this.$store.state.user.userInfo.user
      let lockReconnects = false // 避免重复连接
      let websocketImserver = null
      let createAlarmWebSocket = function (data) {
        websocketImserver = vm.$api.imserverPush(data)
        if (websocketImserver) {
          websocketImserver.onopen = function () {
            console.log('启动消息noticeWebsocket链接')
            heartCheck.reset().start()
          }
          websocketImserver.onmessage = function (message) {
            console.log(JSON.parse(message.data), 'websocketImserver消息')
            try {
              const data = JSON.parse(message.data)
              // 209 心跳
              if (data.code == 209) {
              } else if (data.code == 200) {
                // 收到信息后存储到vuex中
                vm.$store.commit('socket/setNoticeSocketMsgs', JSON.stringify(data))
                // vm.setSocketMsgs = data
              }
            } catch (e) {}
          }
          websocketImserver.onclose = function (e) {
            heartCheck.reset()
            // console.log(e, 'websocketAlarm关闭')
            //   // 监听链接关闭 关闭时重新连接
            //   reconnect(userInfo.staffId)
          }
          websocketImserver.onerror = function () {
            console.log('websocketImserver异常')
            reconnect(userInfo.staffId)
          }
        }
      }
      createAlarmWebSocket(userInfo.staffId)
      function reconnect(data) {
        if (lockReconnects) return
        lockReconnects = true
        setTimeout(function () {
          // 没连接上会一直重连，设置延迟避免请求过多
          createAlarmWebSocket(data)
          lockReconnects = false
        }, 5000)
      }
      var heartCheck = {
        timeout: 540000, // 9分钟发一次心跳
        timeoutObj: null,
        reset: function () {
          clearTimeout(this.timeoutObj)
          return this
        },
        start: function () {
          var self = this
          this.timeoutObj = setInterval(function () {
            // 这里发送一个心跳，后端收到后，返回一个心跳消息，
            websocketImserver.send(
              JSON.stringify({
                code: '209',
                data: 'heart'
              })
            )
          }, this.timeout)
        }
      }
    },
    /**
     * 报警 websocket
     */
    getWebsocket() {
      let vm = this
      let userInfo = this.$store.state.user.userInfo.user
      let createAlarmWebSocket = function (data) {
        websocketAlarm = vm.$api.AlarmPush(data)
        if (websocketAlarm) {
          websocketAlarm.onopen = function () {
            console.log('启动websocketAlarm链接')
            heartCheck.reset().start()
          }
          websocketAlarm.onmessage = function (message) {
            // console.log('websocketAlarm接收到消息：', JSON.parse(message.data))
            if (JSON.parse(message.data)?.data?.alarmCode == 200) {
              try {
                const alarmData = JSON.parse(message.data)
                // 收到信息后存储到vuex中
                vm.$store.commit('socket/setSocketMsgs', JSON.stringify(alarmData))
              } catch (e) {}
            }
          }
          websocketAlarm.onclose = function (e) {
            heartCheck.reset()
            console.log(e, 'websocketAlarm关闭')
            //   // 监听链接关闭 关闭时重新连接
            //   reconnect(userInfo.staffId)
          }
          websocketAlarm.onerror = function () {
            console.log('websocketAlarm异常')
            reconnect(userInfo.staffId)
          }
        }
      }
      createAlarmWebSocket(userInfo.staffId)
      function reconnect(data) {
        if (lockReconnects) return
        lockReconnects = true
        setTimeout(function () {
          // 没连接上会一直重连，设置延迟避免请求过多
          createAlarmWebSocket(data)
          lockReconnects = false
        }, 5000)
      }
      var heartCheck = {
        timeout: 540000, // 9分钟发一次心跳
        timeoutObj: null,
        reset: function () {
          clearTimeout(this.timeoutObj)
          return this
        },
        start: function () {
          var self = this
          this.timeoutObj = setInterval(function () {
            // 实时发送心跳，判断socket是否通畅
            websocketAlarm.send(`{code:4,toUserId: ${userInfo.staffId}}`)
          }, this.timeout)
        }
      }
    },
    // 火灾音视频、给排水、空调监测
    getIemcWebsocket(staffId = this.$store.state.user.userInfo.user) {
      let vm = this
      let createAlarmWebSocket = function (data) {
        iemcWebsocket = vm.$api.IemcWebsocket(data)
        if (iemcWebsocket) {
          iemcWebsocket.onopen = function () {
            console.log('启动iemcWebsocket链接')
            heartCheck.reset().start()
          }
          iemcWebsocket.onmessage = function (message) {
            // console.log('iemcWebsocket接收到消息：', JSON.parse(message.data))
            if (JSON.parse(message.data).surveyEntityCode) {
              try {
                const itemData = JSON.parse(message.data)
                // 收到信息后存储到vuex中
                vm.$store.commit('socket/setSocketIemcMsgs', JSON.stringify(itemData))
              } catch (e) {}
            }
          }
          iemcWebsocket.onclose = function (e) {
            heartCheck.reset()
            // console.log(e, 'iemcWebsocket关闭')
            //   // 监听链接关闭 关闭时重新连接
            //   reconnect(staffId)
          }
          iemcWebsocket.onerror = function () {
            console.log('iemcWebsocket异常')
            reconnect(staffId)
          }
        }
      }
      createAlarmWebSocket(staffId)
      function reconnect(data) {
        if (iemcLockReconnects) return
        iemcLockReconnects = true
        setTimeout(function () {
          // 没连接上会一直重连，设置延迟避免请求过多
          createAlarmWebSocket(data)
          iemcLockReconnects = false
        }, 5000)
      }
      var heartCheck = {
        timeout: 540000, // 9分钟发一次心跳
        timeoutObj: null,
        reset: function () {
          clearTimeout(this.timeoutObj)
          return this
        },
        start: function () {
          var self = this
          this.timeoutObj = setInterval(function () {
            // 这里发送一个心跳，后端收到后，返回一个心跳消息，
            iemcWebsocket.send(`{code:4,toUserId: ${staffId}}`)
          }, this.timeout)
        }
      }
    },
    // 电梯websocket
    getElevatorWebsocket(staffId = this.$store.state.user.userInfo.user) {
      let vm = this
      let createAlarmWebSocket = function (data) {
        elevatorWebsocket = vm.$api.elevatorWebsocket(data)
        if (elevatorWebsocket) {
          elevatorWebsocket.onopen = function () {
            console.log('启动elevatorWebsocket链接')
            heartCheck.reset().start()
          }
          elevatorWebsocket.onmessage = function (message) {
            // console.log('elevatorWebsocket接收到消息：', JSON.parse(message.data))
            try {
              const itemData = JSON.parse(message.data)
              if (itemData.surveyEntityCode || itemData.projectCode) {
                // 电梯执行报警相关逻辑
                vm.executeElevatorEvent(itemData)
                // 收到信息后存储到vuex中
                vm.$store.commit('socket/setSocketElevatorMsgs', JSON.stringify(itemData))
              }
            } catch (e) {}
          }
          elevatorWebsocket.onclose = function (e) {
            heartCheck.reset()
            // console.log(e, 'elevatorWebsocket关闭')
            //   // 监听链接关闭 关闭时重新连接
            //   reconnect(staffId)
          }
          elevatorWebsocket.onerror = function () {
            console.log('elevatorWebsocket异常')
            reconnect(staffId)
          }
        }
      }
      createAlarmWebSocket(staffId)
      function reconnect(data) {
        if (elevatorLockReconnects) return
        elevatorLockReconnects = true
        setTimeout(function () {
          // 没连接上会一直重连，设置延迟避免请求过多
          createAlarmWebSocket(data)
          elevatorLockReconnects = false
        }, 5000)
      }
      var heartCheck = {
        timeout: 540000, // 9分钟发一次心跳
        timeoutObj: null,
        reset: function () {
          clearTimeout(this.timeoutObj)
          return this
        },
        start: function () {
          var self = this
          this.timeoutObj = setInterval(function () {
            // 这里发送一个心跳，后端收到后，返回一个心跳消息，
            elevatorWebsocket.send(`{code:4,toUserId: ${staffId}}`)
          }, this.timeout)
        }
      }
    },
    // 电梯执行报警相关逻辑
    executeElevatorEvent(data) {
      const openVioce = localStorage.getItem('elevatorOpenAlarmVoice') // 初始化为null 只有关闭'false'才执行业务
      // const allowPlayRoutes = ['/elevatorMenu/elevatorMonitor', '/elevatorMenu/elevatorOverview', '/elevatorMenu/elevatorAlarmCenter', '/elevatorMenu/elevatorAnalysis']
      // 判断当前消息是否为报警消息
      // if (data.isAlarm == 1 && allowPlayRoutes.includes(this.$route.path) && openVioce !== 'false') {
      if (data.isAlarm == 1) {
        // 取消默认跳转
        // 跳转电梯运行监测 显示报警提示
        // if (this.$route.path == '/elevatorMenu/elevatorMonitor') {
        //   this.$router.push({
        //     path: '/elevatorMenu/elevatorMonitor',
        //     query: {
        //       isAlarm: 1,
        //       alarmMsg: data.alarmMsg
        //     }
        //   })
        // }
        // 播放报警声音
        if (openVioce !== 'false') {
          this.elevatorPlayCount++
          this.playElevatorAlarmAudio()
        }
      }
    },
    playElevatorAlarmAudio() {
      this.$refs.audio.play()
      this.$refs.audio.onended = () => {
        this.elevatorPlayCount-- // 点击次数递减
        if (this.elevatorPlayCount > 0) {
          this.playElevatorAlarmAudio() // 继续播放声音
        }
      }
    }
  }
  // metaInfo() {
  //   return {
  //     title: this.$store.state.settings.enableDynamicTitle && this.$store.state.settings.title,
  //     titleTemplate: (title) => {
  //       return title ? `${title} - ${process.env.VUE_APP_TITLE}` : process.env.VUE_APP_TITLE
  //     }
  //   }
  // }
}
</script>
<style scoped>
#app {
  height: 100%;
}
[v-cloak] {
  display: none !important;
}
</style>
