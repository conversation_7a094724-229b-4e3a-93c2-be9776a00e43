<template>
  <PageContainer>
    <div slot="content" class="dutyManagement">
      <div v-loading="isLoading" class="content">
        <div class="content-left">
          <div class="change_month user_select_none">
            <i class="el-icon-arrow-left" @click="changeDateData('-', 'month')"></i>
            <span>{{ yearData }} 年 {{ monthData }} 月</span>
            <i class="el-icon-arrow-right" @click="changeDateData('+', 'month')"></i>
            <el-button class="click-today" type="primary" @click="changeDateToday">今天</el-button>
          </div>
          <calendarUpdate v-model="calendarDate">
            <template slot="dateCell" slot-scope="{ data }">
              <div class="calendar_content" @click="handleSelectDate(data)">
                <p class="calendar_content_date">
                  {{ Number(data.day.split('-')[2]) }}
                </p>
                <div v-for="(item, index) in calendarData" :key="index">
                  <div v-if="data.day == item.dutyDate">
                    <p v-if="item.dayShift || item.nightShift" class="selected"></p>
                    <p>{{ item.dayShift && item.dayShift.dutyPersonName ? '早班：' + item.dayShift.dutyPersonName : '' }}</p>
                    <p>{{ item.middleShift && item.middleShift.dutyPersonName ? '中班：' + item.middleShift.dutyPersonName : '' }}</p>
                    <p>{{ item.nightShift && item.nightShift.dutyPersonName ? '晚班：' + item.nightShift.dutyPersonName : '' }}</p>
                  </div>
                </div>
              </div>
            </template>
          </calendarUpdate>
        </div>
        <div class="content-right">
          <div class="top-title"><span class="green_line"></span>今日值班信息</div>
          <div class="aside-body">
            <div v-for="(duty, index) in dutyData.dutyList" :key="index">
              <p>
                <span class="font-weight">{{ duty.shiftName }}：</span>
                {{ duty.inspectionName ? duty.inspectionName : '-' }}
              </p>
            </div>
            <p><span class="font-weight">值班说明：</span></p>
            <p>早班时间：{{ dayShift }}</p>
            <p>中班时间：{{ middleShift }}</p>
            <p>晚班时间：{{ nightShift }}</p>
            <el-button class="click-today" type="primary" @click="signInEvent">签到</el-button>
          </div>
        </div>
      </div>
      <template v-if="dutyDataDialogShow">
        <dutyDataDialog :visible.sync="dutyDataDialogShow" :currentPropDate="currentPropDate" :pageType="pageType" @update:visible="changeDialogVis" />
      </template>
    </div>
  </PageContainer>
</template>
<script>

import moment from 'moment'
export default {
  name: 'dutyManagementIndex',
  components: {
    calendarUpdate: () => import('@/views/monitor/lightingMonitoring/components/calendarUpdate'),
    dutyDataDialog: () => import('../components/dutyDataDialog/index.vue')
  },
  data() {
    return {
      isLoading: false,
      yearData: moment().format('YYYY'),
      monthData: moment().format('M'),
      calendarData: [],
      dutyData: [], // 今日值班信息
      dayShift: '00:00 ~ 08:00', // 早班时间
      middleShift: '07:30 ~ 14:30', // 早班时间
      nightShift: '14:00 ~ 21:00', // 晚班时间
      dutyDataDialogShow: false, // 值班数据弹窗
      pageType: 'sign'
    }
  },
  computed: {
    calendarDate() {
      return this.yearData + '-' + this.monthData + '-01'
    }
  },
  // watch监听yearData改变
  watch: {
    monthData(val) {
      this.getSelectAllCalendar()
    }
  },
  created() {
    this.getSelectAllCalendar()
    this.getDutyToday()
  },
  mounted() {},
  methods: {
    // 根据年/月获取运行日历管理
    getSelectAllCalendar() {
      this.isLoading = true
      let data = {
        startTime: moment().startOf('month').format('YYYY-MM-DD'),
        endTime: moment().endOf('month').format('YYYY-MM-DD')
      }
      this.$api.getDutyRota(data).then((res) => {
        this.isLoading = false
        if (res.code == 200) {
          this.calendarData = res.data
        } else if (res.message) {
          this.$message.error(res.message)
        }
      })
    },
    // 获取今日值班信息
    getDutyToday() {
      this.$api.getDutyToday({}).then((res) => {
        if (res.code == 200) {
          this.dutyData = res.data
        } else if (res.message) {
          this.$message.error(res.message)
        }
      })
    },
    // 改变年份
    changeDateData(type, dateType) {
      if (type === '+') {
        if (dateType == 'month' && this.monthData == 12) {
          this.monthData = 1
          this.yearData = Number(this.yearData) + 1 + ''
        } else {
          this.monthData = Number(this.monthData) + 1
        }
      } else {
        if (dateType == 'month' && this.monthData == 1) {
          this.monthData = 12
          this.yearData = Number(this.yearData) - 1 + ''
        } else {
          this.monthData = Number(this.monthData) - 1
        }
      }
    },
    // 恢复选中当前月
    changeDateToday() {
      this.monthData = moment().format('M')
    },
    // 选择日期
    handleSelectDate(data) {
      // moment 比对当前日期和e.day
      let currentDate = moment().format('YYYY-MM-DD')
      // 选中日期大于等于当前日期，排班操作启动
      if (moment(data.day).isSameOrAfter(currentDate)) {
        this.currentPropDate = moment(data.day).format('YYYY-MM-DD')
        this.pageType = 'duty'
        this.dutyDataDialogShow = true
      } else {
        this.$message.warning('只能排班今天及以后的日期')
      }
    },
    // 签到
    signInEvent() {
      this.pageType = 'sign'
      this.dutyDataDialogShow = true
    },
    changeDialogVis() {
      this.dutyDataDialogShow = false
      this.getDutyToday()
      this.getSelectAllCalendar()
    }
  }
}
</script>
<style lang="scss" type="text/css" scoped>
.dutyManagement {
  width: 100%;
  height: 100%;
  .content {
    height: calc(100%);
    display: flex;
    .content-left {
      width: 78%;
      height: 100%;
      background-color: #fff;
      border-radius: 10px;
      padding: 10px;
      display: block;
      .change_month {
        text-align: center;
        height: 40px;
        line-height: 40px;
        width: 100%;
        position: relative;
        i {
          display: inline-block;
          width: 64px;
          height: 32px;
          line-height: 32px;
          margin: auto 0;
          font-size: 16px;
          text-align: center;
          cursor: pointer;
          background: #f1f6ff;
          border: 1px solid #5188fc;
          &:hover {
            color: #5b8afd;
            background-color: #f1f6ff;
          }
        }
        i:first-child {
          border-radius: 4px 0 0 4px;
        }
        i:last-child {
          border-radius: 0 4px 4px 0;
        }
        span {
          margin: auto 50px;
          color: #121f3e;
          font-weight: 500;
        }
        .click-today {
          position: absolute;
          right: 10px;
          top: 5px;
        }
      }
      .calendar_content {
        width: 100%;
        height: 100%;
        padding: 8px;
        box-sizing: border-box;
        position: relative;
        .calendar_content_date {
          text-align: right;
          font-size: 18px;
          font-family: DIN-Medium, DIN;
        }
      }
    }
    .content-right {
      width: calc(22% - 14px);
      margin-left: 14px;
      background-color: #fff;
      border-radius: 10px;
      padding: 20px 15px;
      .top-title {
        height: 40px;
        line-height: 40px;
        font-size: 16px;
        border-bottom: 1px solid #ebeef5;
        .green_line {
          display: inline-block;
          width: 8px;
          height: 16px;
          border-radius: 0 8px 8px 0;
          background: #3562db;
          margin-right: 10px;
          vertical-align: middle;
        }
      }
      .aside-body {
        height: calc(100% - 40px - 1px);
        overflow-y: auto;
        padding-top: 10px;
      }
    }
  }
  .user_select_none {
    -moz-user-select: none; /* 火狐 */
    -webkit-user-select: none; /* webkit浏览器 */
    -ms-user-select: none; /* IE10 */
    -khtml-user-select: none; /* 早期浏览器 */
    user-select: none;
  }
}
</style>
