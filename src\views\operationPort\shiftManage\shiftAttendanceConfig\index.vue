<template>
  <PageContainer>
    <div slot="header" class="control-btn-header">
      <div class="search-from">
        <el-input v-model="searchFrom.nameOrChargePersonName" placeholder="值班考勤组名称或负责人" clearable
          style="width: 200px"></el-input>
        <div style="display: inline-block">
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="searchForm">查询</el-button>
        </div>
      </div>
      <div class="event-btn">
        <el-button v-auth="'shiftAttendanceConfig:add'" type="primary" icon="el-icon-plus"
          @click="control('add')">新建</el-button>
      </div>
    </div>
    <div slot="content" class="table-content">
      <TablePage ref="table" v-loading="tableLoading" :showPage="true" :tableColumn="tableColumn" :data="tableData"
        height="calc(100% - 40px)" :pageData="pageData" :pageProps="pageProps" @pagination="paginationChange">
      </TablePage>
    </div>
  </PageContainer>
</template>
<script lang="jsx">
import { auth } from '@/util'
import { attendanceGroupTypeList } from '../component/dict.js'
export default {
  name: 'shiftAttendanceConfig',
  components: {},
  beforeRouteEnter(to, from, next) {
    let names = []
    to.matched.map((v, i) => {
      if (i > 0) {
        v.components.default.name && names.push(v.components.default.name)
      }
    })
    // 进入页面时，先将当前页面的 name 信息存入 keep-alive 全局状态
    setTimeout(() => {
      next((vm) => {
        vm.$store.commit('keepAlive/add', names)
      })
    }, 0)
  },
  async beforeRouteLeave(to, from, next) {
    // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
    if (!['addShiftAttendance'].includes(to.name)) {
      // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      tableLoading: false,
      searchFrom: {
        nameOrChargePersonName: ''
      },
      tableColumn: [
        {
          prop: 'name',
          label: '值班考勤组名称'
        },
        {
          prop: 'type',
          label: '类型',
          formatter: (scope) => {
            return scope.row.type ? attendanceGroupTypeList.find((v) => v.value == scope.row.type)?.label : ''
          }
        },
        {
          prop: 'signTimeStr',
          label: '班次'
        },
        {
          prop: 'dutyPostName',
          label: '值班岗'
        },
        {
          prop: 'personNum',
          label: '人数'
        },
        {
          prop: 'chargePersonName',
          label: '负责人'
        },
        {
          width: 180,
          prop: 'operation',
          label: '操作',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span class="operationBtn-span" onClick={() => this.control('detail', row.row)}>
                  查看
                </span>
                {this.roleVisible('shiftAttendanceConfig:edit', row.row) ? (
                  <span class="operationBtn-span" onClick={() => this.control('edit', row.row)}>
                    编辑
                  </span>
                ) : (
                  ''
                )}
                {this.roleVisible('shiftAttendanceConfig:del', row.row) ? (
                  <span class="operationBtn-span operationBtn-del" onClick={() => this.control('del', row.row)}>
                    删除
                  </span>
                ) : (
                  ''
                )}
              </div>
            )
          }
        }
      ],
      tableData: [],
      pageData: {
        current: 1,
        size: 15,
        total: 0
      },
      pageProps: {
        page: 'current',
        pageSize: 'size',
        total: 'total'
      }
    }
  },
  watch: {},
  activated() {
    this.searchForm()
  },
  mounted() {
    this.searchForm()
  },
  methods: {
    roleVisible(menuRole, row) {
      // 有按钮权限或者 负责人可操作
      return auth(menuRole) || row.chargePersonId == this.$store.state.user.userInfo.user.staffId
    },
    // 查询
    searchForm() {
      this.getTableData()
    },
    // 重置查询
    resetForm() {
      Object.assign(this.$data.searchFrom, this.$options.data().searchFrom)
      this.searchForm()
    },
    control(type, row) {
      if (['add', 'edit', 'detail'].includes(type)) {
        // 添加 编辑 复制
        let query = type !== 'add' ? { id: row?.id ?? '' } : {}
        this.$router.push({
          path: '/shiftManage/shiftAttendanceConfig/addShiftAttendance',
          query: {
            type,
            ...query
          }
        })
      } else if (type == 'del') {
        // 删除
        this.$confirm('确认要删除当前值班考勤组吗？', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '删除',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$api.supplierAssess.deleteDutyAttendanceById({ id: row.id }, { 'operation-type': 3, 'operation-id': row.id, 'operation-name': row.name }).then((res) => {
            if (res.code == 200 && res.data) {
              this.$message({ message: '删除成功', type: 'success' })
              this.isLastPage(1)
              this.searchForm()
            } else {
              this.$message({ message: res.message, type: 'error' })
            }
          })
        })
      }
    },
    // 获取预案模板列表
    getTableData() {
      let param = {
        ...this.searchFrom,
        pageSize: this.pageData.size,
        page: this.pageData.current
      }
      this.tableLoading = true
      this.$api.supplierAssess
        .queryDutyAttendanceByPage(param)
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.records
            this.pageData.total = res.data.total
          }
          this.tableLoading = false
        })
        .catch(() => {
          this.tableLoading = false
        })
    },
    // 判断当前页是否是最后一页
    isLastPage(deleteNum) {
      let deleteAfterPage = Math.ceil((this.pageData.total - deleteNum) / this.pageData.size)
      let currentPage = this.pageData.current > deleteAfterPage ? deleteAfterPage : this.pageData.current
      this.pageData.current = currentPage < 1 ? 1 : currentPage
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getTableData()
    }
  }
}
</script>
<style lang="scss" scoped>
.control-btn-header {
  padding: 0px 10px 10px 10px !important;

  .search-from {
    padding-right: 180px;
    position: relative;

    &>div {
      margin-top: 10px;
      margin-right: 10px;
    }
  }

  .event-btn {
    padding-top: 10px;
  }
}

.table-content {
  margin-top: 15px;
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 15px);
  padding: 10px;
}
</style>
