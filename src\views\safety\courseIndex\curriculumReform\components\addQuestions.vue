<template>
  <el-drawer
    :visible.sync="drawerDialog"
    :with-header="true"
    size="55%"
    :show-close="true"
    :before-close="closeDrawer"
    v-loading="drawerLoading"
  >
    <div slot="title" class="coursrDrawer">选择试题</div>
    <div class="drawer_conter">
      <add-questions 
        ref="addQuestions" 
        :subjectId="subjectId" 
        :courseName="courseName" 
        :type="type"
        @isOk="isOk"
        @planIsOk="planIsOk"
      ></add-questions>
    </div>
    <div class="drawer_footer">
      <el-button type="primary" plain @click="closeDrawer">取消</el-button>
      <el-button type="primary" @click="submitBtn">录入题库</el-button>
    </div>
  </el-drawer>
</template>

<script>
import addQuestions from "../../../courseIndex/components/addQuestions.vue";
export default {
  components:{
    addQuestions
  },
  props: {
    type:{
      type:String,
      default:''
    },
    drawerDialog: {
      type: Boolean,
      default: false,
    },
    subjectId:{
      type:Number,
      default:null
    },
    courseName:{
      type:String,
      default:''
    }
  },
  data() {
    return {
      seachForm: {
        quesTypeCode: "",
        quesInfo: "",
      },
      drawerLoading:false
    };
  },
  methods: {
    closeDrawer() {
      this.$refs.addQuestions.formInfo.exercisesList = [],
      this.drawerLoading = false
      this.$emit("closeDrawer", false);
    },
    // 录入题库
    submitBtn() {
      this.drawerLoading = true
      this.$refs.addQuestions.getValidate()
    },
    // 添加试题
    isOk(list){
      var newList = []
      newList = JSON.parse(JSON.stringify(list))
      newList.forEach((item) => {
        if (item.type == "2") {
          item.answer = item.answer.join(",");
        }
        item.options.forEach((i,ind)=>{
          i.id=this.$tools.addLetter(ind)
        })
        item.options = JSON.stringify(item.options);
        item.isDraft = true
      });
      newList = newList.filter((obj) => delete obj.courseList);
      this.$api.addQuestions({questions:newList}).then((res) => {
        if (res.code == 200) {
          this.closeDrawer()
          // 查看录入试题
            this.$emit('getEnterQuestionsList')
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    // 计划新增试题
    planIsOk(list){
      console.log(list,'list');
      this.$emit('planIsOk',list)
      this.closeDrawer()
    }
  },
};
</script>

<style lang="scss" scoped>
.coursrDrawer {
  color: #333333;
  font-weight: 500;
  font-size: 18px;
}
::v-deep .el-drawer__header {
  height: 56px;
  line-height: 56px !important;
  margin-bottom: 0 !important;
  border-bottom: 1px solid #dcdfe6;
  box-shadow: -4px 0 4px 0 rgba(203, 205, 220, 0.24);
}
.drawer_conter {
  width: 100%;
  padding: 24px;
  height: calc(100% - 80px);
  font-size: 14px;
}
::v-deep .el-form-item {
  margin-bottom: 16px;
}
.drawer_footer {
  padding: 16px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: right;
  color: #7f848c;
}
</style>
