<template>
  <PageContainer>
    <div slot="content" class="content" style="height: 100%">
      <div class="answer_top">
        <p>这是考试名称<span>（共20分）</span></p>
        <span>所属科目 总科目/二级分类/三级分类</span>
      </div>
      <div class="answer_content">
        <div class="content_left">
          <div class="exercisesItem">
            <div class="exercisesTop">
              <div class="exercisesType">
                {{
                  itemInfo.type == "1"
                    ? "单选题"
                    : itemInfo.type == "2"
                    ? "多选题"
                    : "判断题"
                }}
              </div>
              {{ itemInfo.score || 0 }}分
            </div>
            <div class="exercisesName">
              {{ itemInfo.topic }}
            </div>
            <el-radio-group
              v-if="itemInfo.type == '1'"
              class="radio"
              v-model="itemInfo.userAnswer"
            >
              <el-radio
                v-for="(j, index) in itemInfo.options"
                :key="index"
                :label="j.id"
                >{{ j.id }}. {{ j.label }}</el-radio
              >
            </el-radio-group>
            <el-checkbox-group
              v-if="itemInfo.type == '2'"
              v-model="itemInfo.userAnswer"
              class="radio"
            >
              <el-checkbox
                v-for="(j, index) in itemInfo.options"
                :key="index"
                :label="j.id"
                >{{ j.id }}. {{ j.label }}</el-checkbox
              >
            </el-checkbox-group>
            <el-radio-group
              v-if="itemInfo.type == '3'"
              v-model="itemInfo.userAnswer"
            >
              <el-radio :label="'1'">正确</el-radio>
              <el-radio :label="'2'">错误</el-radio>
            </el-radio-group>
            <div class="btns">
              <el-button type="primary" plain @click="submit('0')"
                >取消</el-button
              >
              <el-button
                type="primary"
                :disabled="indexNum == 0"
                @click="upQuestions"
                >上一题</el-button
              >
              <el-button
                type="primary"
                :disabled="indexNum == exercisesList.length - 1"
                @click="nextQuestions"
                >下一题</el-button
              >
              <el-button type="primary" @click="submit('1')">提交</el-button>
            </div>
          </div>
        </div>
        <div class="content_right">
          <p>
            全部题目<span
              >（{{ indexNum + 1 }}/{{ exercisesList.length }}）</span
            >
          </p>
          <div class="answerNum">
            <div
              v-for="(item, index) in exercisesList"
              :key="index"
              :class="[
                'item',
                indexNum == index ? 'isActive' : '',
                isfinished(item) ? 'isfinished' : '',
              ]"
              @click="goOnQuestions(index)"
            >
              {{ index + 1 }}
              <img
                v-if="isfinished(item)"
                src="../../../../assets/images/icon_finish.png"
                alt=""
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </PageContainer>
</template>

<script type="text/ecmascript-6">
import moment from 'moment'
export default {
  name: 'answerIng',
  data(){
      return{
        radio:'3',
        periodId:'',//课时id
        courseId:'',//课程id
        routeInfo:{},
        exercisesList:[],//试题列表
        itemInfo:{},
        indexNum:0,
        noLength:0,//没有做的试题长度
      }
  },
  created() {
    this.routeInfo= JSON.parse(sessionStorage.getItem('routeInfo'))
    this.periodId = this.$route.query.periodId
    this.courseId = this.$route.query.courseId
    setTimeout(()=>{
      this.getQuestionsList()
    },500)
  },
  methods: {
    getQuestionsList(){
      let params = {
        periodId:this.periodId,
        userId:this.routeInfo.userId
      }
      this.$api.questionsList(params).then(res=>{
        if(res.code=='200'){
          res.data.forEach(i => {
            i.options = JSON.parse(i.options)
            if(i.type=='2'){
              i.userAnswer = i.userAnswer ? i.userAnswer.split(','):[]
            }else{
              i.userAnswer = i.userAnswer||''
            }
          });
          this.exercisesList = res.data
          this.itemInfo = this.exercisesList[0]
        }else{
          this.$message.error(res.msg)
        }
      })
    },
    nextQuestions(){
      this.indexNum+=1
      this.itemInfo = this.exercisesList[this.indexNum]
    },
    upQuestions(){
      this.indexNum-=1
      this.itemInfo = this.exercisesList[this.indexNum]
    },
    goOnQuestions(index){
      this.indexNum = index
      this.itemInfo = this.exercisesList[index]
    },
    submit(type){
      if(type=='1'){ // 提交试题
        this.noLength = 0
        this.exercisesList.forEach(k=>{
          if(!this.isfinished(k)){
            this.noLength += 1
          }
        })
        if(this.noLength){
          this.$confirm(`还有 ${this.noLength} 道题没有答题，确认提交吗？`, "提示", {
            confirmButtonText: "确认",
            cancelButtonText: "取消",
            type: "warning",
          }).then(() => {
            this.exercisesList.forEach(i=>{
              if(!i.userAnswer){
                i.isTrue=false
              }
            })
            this.isOk('userAnswer')
          });
        }else {
          this.isOk('userAnswer')
        }
      }else{ // 保存试题
        this.isOk('saveUserAnswer')
      }
    },
    // 提交试题
    isOk(str){
      var newList = JSON.parse(JSON.stringify(this.exercisesList))
      newList.forEach(i=>{
        i.options = JSON.stringify(i.options)
        if(i.type=='2'){
          i.userAnswer = i.userAnswer.join(',')
        }
        i.isTrue=i.userAnswer&&i.userAnswer==i.answer
      })
      let params = {
        courseId:this.courseId,
        periodId:this.periodId,
        userCoursePeriodQuestions:newList,
        userId:this.routeInfo.userId
      }
      this.$api[str](params).then(res=>{
        if(res.code=='200'){
          this.$router.go(-2)
        }else{
          this.$message.error(res.msg)
        }
      })
    },
    // 是否完成
    isfinished(item){
     return item.type!='2'?Boolean(item.userAnswer):Boolean(item.userAnswer.length)
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  padding: 16px 24px 24px 24px;
  .answer_top {
    height: 58px;
    color: #7f848c;
    font-size: 14px;
    p {
      font-size: 20px;
      font-weight: 600;
      color: #333;
      margin-bottom: 16px;
      span {
        font-weight: normal;
      }
    }
  }
  .answer_content {
    height: calc(100% - 60px);
    display: flex;
    .content_left {
      flex: 1;
      margin-right: 24px;
      background-color: #fff;
      padding: 24px;
      .exercisesItem {
        overflow: hidden;
        background-color: #fff;
        .exercisesTop {
          margin-bottom: 10px;
          color: #7f848c;
          .exercisesType {
            display: inline-block;
            width: 58px;
            height: 22px;
            line-height: 22px;
            text-align: center;
            border-radius: 4px;
            color: #86909c;
          }
        }
        .exercisesName {
          line-height: 20px;
          margin-bottom: 40px;
          word-break: break-all;
        }
        .el-radio {
          margin-left: 38px;
          font-size: 14px;
          color: #7f848c !important;
          line-height: 18px;
          margin-bottom: 40px;
          display: flex;
        }
        .btns {
          display: flex;
          justify-content: center;
        }
      }
    }
    .content_right {
      width: 324px;
      height: 100%;
      p {
        font-size: 18px;
        font-weight: 500;
        color: #333;
        margin-bottom: 16px;
        margin-bottom: 24px;
        span {
          font-size: 16px;
          font-weight: normal;
          color: #7f848c;
        }
      }
      .answerNum {
        width: 100%;
        height: calc(100% - 60px);
        display: flex;
        flex-wrap: wrap;
        align-content: flex-start; // 替代原先的space-between布局方式
        .item {
          width: 52px;
          height: 52px;
          background-color: #fff;
          line-height: 52px;
          text-align: center;
          margin: 0 16px 16px 0; // 间隙为5px
          font-size: 16px;
          cursor: pointer;
          &:nth-child(5n) {
            // 去除第3n个的margin-right
            margin-right: 0;
          }
        }
        .isActive {
          background: #e6effc;
          border: 1px solid #3562db;
        }
        .isfinished {
          position: relative;
          img {
            position: absolute;
            bottom: 0;
            right: 0;
          }
        }
      }
    }
  }
}
::v-deep .el-radio {
  display: block;
  margin: 10px 0;
  .el-radio__label {
    white-space: normal;
  }
}
::v-deep .el-checkbox {
  display: block;
  line-height: 30px;
  margin: 10px 0;
}
::v-deep .el-checkbox-group {
  margin-left: 38px;
}
</style>
