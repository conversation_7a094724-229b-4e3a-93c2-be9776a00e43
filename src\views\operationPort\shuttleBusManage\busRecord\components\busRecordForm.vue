<template>
  <el-dialog
    v-dialogDrag
    class="component dictionary-value-edit"
    :title="'新建记录'"
    width="750px"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :visible.sync="dialogRrcordShow"
    custom-class="model-dialog"
    :before-close="closeRecordDialog"
  >
    <div class="content" style="padding: 10px; display: flex">
      <el-form ref="recordForm" :model="formModel" :rules="rules" label-width="95px">
        <el-form-item label="车次编号" prop="trainNo" style="width: 460px">
          <div style="display: flex">
            <el-input v-model="formModel.trainNo" placeholder="请输入车次编号" clearable :disabled="true"></el-input>
            <el-button class="form-btn-btn" type="primary" @click="generate">选择</el-button>
          </div>
        </el-form-item>
        <el-form-item label="路线编号" style="width: 460px">
          <el-input v-model="formModel.lineCode" placeholder="请输入路线编号" :disabled="true" clearable></el-input>
        </el-form-item>
        <el-form-item label="路线名称" style="width: 460px">
          <el-input v-model="formModel.lineName" placeholder="请输入路线名称" :disabled="true" clearable></el-input>
        </el-form-item>
        <el-form-item label="班车类型" style="width: 460px">
          <el-select v-model="formModel.busType" placeholder="请选择班车类型" :disabled="true">
            <el-option v-for="item in regularBusList" :key="item.code" :label="item.name" :value="item.code"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="乘客类型" style="width: 460px">
          <el-select v-model="formModel.passengerType" placeholder="请选择乘客类型" :disabled="true">
            <el-option v-for="item in userTypeList" :key="item.code" :label="item.name" :value="item.code"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="发车时间">
          <el-time-picker v-model="formModel.departTime" :disabled="true" format="HH:mm" value-format="HH:mm" placeholder="请选择发车时间" style="width: 365px"> </el-time-picker>
        </el-form-item>
        <el-form-item label="发车日期">
          <el-date-picker v-model="formModel.departDate" type="date" placeholder="选择日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width: 365px"> </el-date-picker>
        </el-form-item>
        <el-form-item label="乘客人数" style="width: 460px">
          <el-input
            v-model.number="formModel.numberOfPassengers"
            :min="0"
            :max="500"
            @input="handleInputLimit"
            @blur="handleBlurAdjust"
            placeholder="请输入乘客人数（0-500）"
            clearable
          ></el-input>
        </el-form-item>
      </el-form>
    </div>
    <addBusRecord :epuipmentVisible="epuipmentVisible" :regularBusList="regularBusList" :userTypeList="userTypeList" @busClose="busClose" @sure="sure" />
    <template #footer>
      <el-button type="primary" plain @click="closeRecordDialog">取消</el-button>
      <el-button type="primary" @click="onSubmit">确认</el-button>
    </template>
  </el-dialog>
</template>
<script>
import { uploadAcceptDict } from '@/util/dict.js'
export default {
  name: 'DictionaryValueEdit',
  components: {
    addBusRecord: () => import('./addBusRecord.vue')
  },
  props: {
    dialogRrcordShow: {
      type: Boolean,
      default: false
    },
    regularBusList: {
      type: Array,
      default: () => []
    },
    userTypeList: {
      type: Array,
      default: () => []
    }
  },
  events: ['update:visible', 'success'],
  data: function () {
    return {
      uploadAcceptDict,
      epuipmentVisible: false,
      formModel: {
        trainNo: '',
        lineCode: '',
        lineName: '',
        departTime: '',
        departDate: '',
        busType: '',
        lineId: '',
        passengerType: '',
        numberOfPassengers: '',
        trainNumberId: ''
      },
      value1: '',
      rules: {
        trainNo: [{ required: true, message: '请选择班车' }]
      },
      dictData: [],
      busRoadMapList: [
        {
          value: '0',
          label: '路线1'
        },
        {
          value: '1',
          label: '路线2'
        },
        {
          value: '2',
          label: '路线3'
        }
      ], // 班车路线
      currentTime: new Date(), // 默认设置为当前时间
      loadingStatus: false
    }
  },
  watch: {},
  methods: {
    // dialog点击右上角关闭按钮，重置表单
    closeRecordDialog() {
      this.$emit('closeRecordDialog')
    },
    // 实时输入限制
    handleInputLimit(value) {
      if (value > 500) {
        this.formModel.numberOfPassengers = 500
        this.$message.warning('最大值不能超过500')
      } else if (value < 0) {
        this.formModel.numberOfPassengers = 0
      }
    },
    // 失焦时修正值
    handleBlurAdjust() {
      if (this.formModel.numberOfPassengers === null || this.formModel.numberOfPassengers === '') {
        this.formModel.numberOfPassengers = 0
      } else {
        this.formModel.numberOfPassengers = Math.min(500, Math.max(0, Number(this.formModel.numberOfPassengers)))
      }
    },
    // 表单提交
    onSubmit() {
      this.$refs.recordForm
        .validate()
        .catch(() => Promise.reject())
        .then(() => {
          this.loadingStatus = true
          const params = {
            trainNo: this.formModel.trainNo, // 	车次编号
            lineCode: this.formModel.lineCode, // 路线编号
            lineName: this.formModel.lineName, // 路线名称
            departTime: this.formModel.departTime, // 发车时间
            busType: this.formModel.busType, // 班车类型
            passengerType: this.formModel.passengerType, // 乘客类型
            numberOfPassengers: this.formModel.numberOfPassengers, // 乘客人数
            departDate: this.formModel.departDate, // 发车日期
            trainNumberId: this.formModel.trainNumberId, // 车次ID
            lineId: this.formModel.lineId
          }
          return this.$api.fileManagement.recordSave(params)
        })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('操作成功')
            this.$emit('success')
            this.closeRecordDialog()
          } else {
            throw res.msg || res.message
          }
        })
        .catch((msg) => {
          msg && this.$message.error(msg)
        })
        .finally(() => {
          this.loadingStatus = false
        })
    },
    // 班车选择弹窗
    generate() {
      this.epuipmentVisible = true
      this.regularBusList = this.regularBusList // 班车类型
      this.userTypeList = this.userTypeList // 乘客类型
    },
    busClose() {
      this.epuipmentVisible = false
    },
    // 确定选择的班车
    sure(data) {
      console.log(data, '6666666666666666666')
      this.formModel.trainNo = data[0].trainNo
      this.formModel.lineCode = data[0].lineCode
      this.formModel.lineName = data[0].lineName
      this.formModel.busType = data[0].busType
      this.formModel.passengerType = data[0].passengerType
      this.formModel.trainNumberId = data[0].id
      this.formModel.lineId = data[0].lineId
      this.formModel.departTime = data[0].departTime
    },
    resetForm() {
      this.formModel = {
        trainNo: '',
        lineCode: '',
        lineName: '',
        departTime: '',
        departDate: '',
        busType: '',
        passengerType: '',
        numberOfPassengers: '',
        trainNumberId: ''
      }
    }
  }
}
</script>
<style lang="scss">
.component.dictionary-value-edit {
  .el-form {
    background-color: #fff;
    padding: 10px 16px 0;
    .el-form-item {
      .el-cascader,
      .el-select {
        width: 100%;
      }
    }
    &.readonly {
      .el-form-item__label::before {
        display: none;
      }
      .el-form-item {
        .el-input__inner,
        .el-cascader,
        .el-select {
          background-color: transparent;
          border: none;
        }
        .el-input__suffix {
          display: none;
        }
        .el-upload-list__item-status-label {
          display: none;
        }
        .el-upload__tip {
          display: none;
        }
      }
    }
  }
  .form-btn-btn {
    margin-left: 8px;
    height: 32px;
    margin-top: 5px;
  }
  .content {
    width: 100%;
    max-height: 500px !important;
    overflow: auto;
    background-color: #fff !important;
  }
  .dictionary-value-edit {
    &__color {
      vertical-align: middle;
      .el-input-group__prepend {
        padding: 0;
        line-height: 0;
        border-radius: 4px 0 0 4px;
      }
      .el-color-picker {
        height: 30px;
      }
      .el-color-picker__trigger {
        border: none;
        height: 30px;
        width: 30px;
        padding: 2px;
      }
      .el-input__inner {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      }
    }
    &__upload {
      &.hide {
        .el-upload {
          opacity: 0;
          transition-duration: 0.5s;
          pointer-events: none;
        }
      }
    }
  }
}
</style>
