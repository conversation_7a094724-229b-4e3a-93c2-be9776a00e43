<script>
import axios from 'axios'
export default {
  name: 'ContractList',
  props: {
    id: String
  },
  data: () => ({
    tableLoading: false,
    tableData: []
  }),
  computed: {
    OperateType() {
      return {
        // 查看
        VIEW: 'view',
        // 下载
        DOWNLOAD: 'DOWNLOAD'
      }
    }
  },
  filters: {
    contractStatusFilters(val) {
      if (!val) return ''
      let type = {
        0: '未开始',
        1: '履约中',
        2: '已结束',
        3: '即将过期',
        4: '已过期'
      }
      return type[val] || '未知状态'
    }
  },
  mounted() {
    const params = {
      pageNum: 1,
      pageSize: 999,
      houseId: this.id
    }
    this.tableLoading = true
    this.$api.rentalHousingApi
      .queryHouseContractByPage(params)
      .then((res) => {
        if (res.code === '200') {
          this.tableData = res.data.records
        } else {
          throw res.message
        }
      })
      .catch((msg) => this.$message.error(msg || '获取合同列表失败'))
      .finally(() => (this.tableLoading = false))
  },
  methods: {
    // 表格相关操作总线处理
    onOperate(type, row) {
      switch (type) {
        case this.OperateType.VIEW:
          this.$router.push({
            name: 'ContractDetails',
            query: { id: row.id }
          })
          break
        case this.OperateType.DOWNLOAD:
          if (!row.attachmentUrl) {
            return this.$message.error('暂无可下载合同文件')
          }
          this.downloadContractFile(row)
          break
      }
    },
    /** 合同附件下载 */
    downloadContractFile(row) {
      // const fileUrl = this.$tools.imgUrlTranslation(path)
      // fetch(fileUrl)
      //   .then((res) => res.blob())
      //   .then((blob) => {
      //     const extension = path.substring(path.lastIndexOf('.'))
      //     const url = window.URL.createObjectURL(blob)
      //     const a = document.createElement('a')
      //     a.href = url
      //     a.download = contractNum + extension
      //     a.click()
      //     window.URL.revokeObjectURL(url)
      //   })
      //   .catch(() => this.$message.error('下载失败'))
      let params = {
        ids: row.id
      }
      this.downloadPort(params)
    },
    downloadPort(params) {
      axios
        .post(__PATH.VUE_RHMS_API + 'houseContract/downloadContractBatch', params, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer ' + this.$store.state.user.token,
            hospitalCode: this.$store.state.user.userInfo.user.hospitalCode,
            unitCode: this.$store.state.user.userInfo.user.unitCode
          },
          responseType: 'blob'
        })
        .then((res) => {
          this.download(res)
        })
    },
    download(res) {
      const downloadElement = document.createElement('a')
      const href = window.URL.createObjectURL(res.data) // 创建下载的链接
      downloadElement.style.display = 'none'
      downloadElement.href = href
      downloadElement.download = this.$tools.getFileName(res) // 下载后文件名
      document.body.appendChild(downloadElement)
      downloadElement.click() // 点击下载
      document.body.removeChild(downloadElement) // 下载完成移除元素
      window.URL.revokeObjectURL(href) // 释放掉blob对象
    }
  }
}
</script>
<template>
  <div class="contract-list">
    <el-table v-loading="tableLoading" height="100%" :data="tableData" border stripe table-layout="auto" class="tableAuto" row-key="id">
      <el-table-column prop="contractNum" label="合同号" />
      <el-table-column prop="status" label="合同状态">
        <template #default="{ row }">
          {{ row.status | contractStatusFilters }}
        </template>
      </el-table-column>
      <el-table-column prop="secondPartyName" label="承租人" />
      <el-table-column prop="rentingUnitPrice" label="租金单价（元/㎡）" />
      <el-table-column prop="rentingMoney" label="出租租金（元）" />
      <el-table-column prop="rentingStartTime" label="合同租期开始时间" />
      <el-table-column prop="rentingEndTime" label="合同租期结束时间" />
      <el-table-column prop="residueRentingDay" label="剩余租期（天）" />
      <el-table-column prop="name" label="操作" width="120px">
        <template #default="{ row }">
          <el-button type="text" @click="onOperate(OperateType.VIEW, row)">查看</el-button>
          <el-button type="text" @click="onOperate(OperateType.DOWNLOAD, row)">下载</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<style lang="scss" scoped>
.contract-list {
  height: 100%;
}
</style>
