<template>
  <div ref="hiddenStatus" style="height: 100%; width: 100%;"></div>
</template>

<script>
import * as echarts from 'echarts'
export default {
  name: 'peiCharts',
  props: {
    pieData: {
      type: Object,
      default() {
        return {}
      }
    },
    // eslint-disable-next-line vue/require-prop-type-constructor
    deep: true
  },
  data() {
    return {
      data: ''
    }
  },
  watch: {
    pieData(val) {
      this.data = val
      this.pieCharts(this.data)
    }
  },
  methods: {
    pieCharts(data) {
      let myChart = echarts.init(this.$refs.hiddenStatus)
      // 绘制图表
      var option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'right',
          align: 'left',
          top: 'center',
          textStyle: {
            color: '#909399'
          },
          icon: 'rect', // 形状
          itemWidth: 7, // 宽
          itemHeight: 7, // 高
          formatter: function (name) {
            var oa = data.array
            var num = oa.reduce((sum, e) => sum + e.value, 0)
            for (var i = 0; i < oa.length; i++) {
              if (name === oa[i].name) {
                return ' ' + name + '   (' + oa[i].value + ')   ' + ((oa[i].value / num) * 100).toFixed(2) + '%'
              }
            }
          }
        },
        toolbox: {
          show: true,
          feature: {
            mark: { show: false },
            dataView: { show: false, readOnly: false },
            magicType: {
              show: false,
              type: ['pie', 'funnel']
            },
            restore: { show: false },
            saveAsImage: { show: false }
          }
        },
        // 设置饼状图每个颜色块的颜色
        color: ['#FA403C', '#3562DB', '#FF9435', '#FFBE00'],
        series: [
          {
            name: '',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['40%', '50%'],
            roseType: 'radius',
            label: { show: false },
            emphasis: {
              label: {
                normal: {
                  show: false,
                  position: 'inside',
                  formatter: '{b}:{d}%'
                }
              }
            },
            data: data.array
          }
        ]
      }
      myChart.on('click', function (param) {
        // 添加点击事件
        console.log(param)
        // myChart.dispatchAction({ type: 'highlight', dataIndex: param.dataIndex }) // 激活点击区域高亮
        // if (param.dataIndex !== number) {
        //   // 当鼠标点击的时候 消除上一个扇区的高亮
        //   myChart.dispatchAction({ type: 'downplay', dataIndex: number })
        // }
        // number = param.dataIndex // 接住当前扇区的dataIndex
      })
      myChart.clear()
      myChart.setOption(option) // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    }
  }
}
</script>

<style></style>
