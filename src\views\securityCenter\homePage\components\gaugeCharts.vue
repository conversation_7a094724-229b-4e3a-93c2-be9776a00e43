<template>
  <div ref="gaugeCharts" style="height: 100%; width: 100%;"></div>
</template>

<script>
// 引入基本模板
// let echarts = require('echarts/lib/echarts')
import * as echarts from 'echarts'
export default {
  name: 'gaugeCharts',
  props: {
    chartsData: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      data: ''
    }
  },
  watch: {
    chartsData(val) {
      this.data = val
      this.getCharts()
    }
  },
  //   mounted() {
  //     this.pieCharts()
  //   },
  methods: {
    getCharts() {
      let myChart = echarts.init(this.$refs.gaugeCharts)
      // 绘制图表
      var option = {
        series: [
          {
            type: 'gauge',
            radius: '90%',
            center: ['50%', '60%'],
            progress: {
              show: true,
              width: 3
            },
            itemStyle: {
              color: '#3562db'
            },
            axisLine: {
              lineStyle: {
                width: 3
              }
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false,
              length: 8,
              lineStyle: {
                width: 2,
                color: '#999'
              }
            },
            axisLabel: {
              show: false,
              distance: 25,
              color: '#999',
              fontSize: 12
            },
            anchor: {
              show: true,
              showAbove: true,
              size: 8,
              itemStyle: {
                borderWidth: 1,
                borderColor: '#3562db'
              }
            },
            title: {
              show: false
            },
            detail: {
              valueAnimation: true,
              fontSize: 12,
              offsetCenter: [0, '70%']
            },
            data: [
              {
                value: this.data.split('%')[0] || 0
              }
            ]
          }
        ]
      }
      myChart.clear()
      myChart.setOption(option) // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    }
  }
}
</script>

<style></style>
