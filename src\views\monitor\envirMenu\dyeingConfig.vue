<!-- 染色区间配置 -->
<template>
  <PageContainer :footer="true" class="dyeingConfig">
    <div slot="header" class="control-btn-header">
      <i class="el-icon-arrow-left" @click="() => { $router.go(-1) }" />
      <span>染色区间配置</span>
    </div>
    <div slot="content" v-loading="loading" class="content-slider">
      <div v-for="(item, index) in configData" :key="item.paramName" class="slider-item">
        <div class="slider-item-title">{{item.paramName}}</div>
        <div class="slider-item-control">
          <vue-slider v-model="item.valueList" :height="8" contained tooltip="always" :min="item.minNumber" :max="item.maxNumber" :marks="getMarks(item.minNumber, item.maxNumber, item.unit)" :process="(v) => getProcess(v, item.entities)" @change="(v, i) => sliderChange(v, i, index)">
            <template v-slot:dot="{ focus }">
              <div :class="['custom-dot', { focus }]"></div>
            </template>
            <template v-slot:tooltip="{ value }">{{value}}</template>
            <template v-slot:process="{ style, index }">
              <div class="vue-slider-process" :style="style">
                <div class="merge-tooltip">{{item.nameList[index]}}</div>
              </div>
            </template>
          </vue-slider>
        </div>
      </div>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="() => { $router.go(-1) }">取消</el-button>
      <el-button type="primary" @click="updateDyeingConfig">保存</el-button>
    </div>
  </PageContainer>
</template>

<script>
import VueSlider from 'vue-slider-component'
import 'vue-slider-component/theme/antd.css'
import { monitorTypeList } from '@/util/dict.js'
import { debounce } from 'lodash/function'
export default {
  name: 'dyeingConfig',
  components: {
    VueSlider
  },
  data() {
    return {
      configData: [],
      loading: true,
      requestInfo: {
        projectCode: monitorTypeList.find(item => item.projectName == '环境监测').projectCode
      }
    }
  },
  computed: {
    getMarks() {
      return (minNum, maxNum, unit) => {
        return {
          [minNum]: {
            // label: minNum,
            style: {
              display: 'none'
            },
            labelStyle: {
              color: '#7F848C',
              fontSize: '14px'
              // transform: 'none'
            }
          },
          [maxNum]: {
            label: maxNum.toString() + unit,
            style: {
              display: 'none'
            },
            labelStyle: {
              color: '#7F848C',
              fontSize: '14px'
              // transform: 'none'
            }
          }
        }
      }
    },
    getProcess() {
      return (dotsPos, list) => {
        let newArr = []
        list.forEach((item, index) => {
          newArr.push([index == 0 ? 0 : dotsPos[index - 1], index == (list.length - 1) ? 100 : dotsPos[index], { backgroundColor: item.colour, color: item.colour }])
        })
        return newArr
      }
    }
  },
  mounted() {
    this.getDyeingConfig()
  },
  methods: {
    // 保存染色配置
    updateDyeingConfig() {
      let list = []
      this.configData.forEach(item => {
        item.entities.forEach((v, i) => {
          if (i == item.entities.length - 1) {
            v.max = item.maxNumber
          }
          v.ratio = Number((Math.abs(v.max - v.min) / Math.abs(item.maxNumber - item.minNumber) * 100).toFixed(2))
          list.push(v)
        })
      })
      let params = {
        ...this.requestInfo,
        list
      }
      this.$api.UpdateDyeingConfig(params).then(res => {
        if (res.code == 200) {
          this.$message({ message: '保存成功', type: 'success'})
          this.$router.go(-1)
        } else {
          this.$message({ message: res.msg, type: 'error'})
        }
      })
    },
    // 获取染色配置
    getDyeingConfig() {
      this.$api.GetDyeingConfig(this.requestInfo).then(res => {
        this.loading = false
        if (res.code == 200) {
          res.data.forEach(item => {
            item.nameList = item.entities.map(v => v.configName)
            item.valueList = item.entities.map(v => v.max).slice(0, item.entities.length - 1)
          })
          this.configData = res.data
        } else {
          this.$message({ message: res.msg, type: 'error'})
        }
      })
    },
    // 滑动事件
    sliderChange: debounce(function(value, index, parentIndex) {
      let v = typeof value == 'number' ? value : value[index]
      // this.configData[parentIndex].entities[index].min = index ? this.configData[parentIndex].entities[index - 1].max : this.configData[parentIndex].minNumber
      this.configData[parentIndex].entities[index].max = v
      this.configData[parentIndex].entities[index + 1].min = this.configData[parentIndex].entities[index].max
    }, 300)
  }
}

</script>

<style lang="scss" scoped>
.dyeingConfig {
  .control-btn-header {
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    align-items: center;
    padding: 13px 16px;
    font-size: 14px;
    font-weight: 500;
    color: #121f3e;
    line-height: 14px;

    .el-icon-arrow-left {
      font-size: 13px;
      margin-right: 8px;
      cursor: pointer;
      color: #7f848c;
    }
  }

  ::v-deep .container-header {
    border-radius: 4px 4px 0 0;
  }

  ::v-deep .container-content {
    border-radius: 0 0 4px 4px;
  }

  .content-slider {
    height: 100%;
    background: #fff;
    overflow: auto;
    padding: 0 0 32px 32px;

    ::v-deep .vue-slider {
      .vue-slider-process {
        border-radius: 0;

        .merge-tooltip {
          position: absolute;
          top: -22px;
          font-size: 14px;
          left: 50%;
          transform: translateX(-50%);
          white-space: nowrap;
        }
      }

      .vue-slider-dot {
        display: flex;

        .custom-dot {
          cursor: pointer;
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          bottom: 3px;
          width: 9px;
          height: 27px;
          background: url("../../../assets/images/monitor/pointerIcon.png") no-repeat center/ 100%;
        }

        .vue-slider-dot-tooltip {
          position: absolute;
          top: 40px;
          font-size: 14px;
          color: #7f848c;
        }
      }
    }

    .slider-item {
      margin-top: 32px;
      display: flex;
      min-width: 500px;
      width: 60%;
      align-items: center;
      border: 1px solid #e4e7ed;
      padding: 40px;

      .slider-item-title {
        width: 100px;
        height: 46px;
        background: rgb(53 98 219 / 10%);
        border-radius: 0;
        font-size: 14px;
        color: #121f3e;
        text-align: center;
        line-height: 46px;
      }

      .slider-item-control {
        flex: 1;
        padding: 0 0 15px 40px;
      }
    }
  }
}
</style>
