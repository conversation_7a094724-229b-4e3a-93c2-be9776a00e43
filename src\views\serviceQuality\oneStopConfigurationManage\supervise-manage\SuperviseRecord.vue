<script>
import tableListMixin from '@/mixins/tableListMixin'
import { SuperviseDictType } from '@/views/serviceQuality/oneStopConfigurationManage/supervise-manage/constant'
export default {
  name: 'SuperviseRecord',
  mixins: [tableListMixin],
  data() {
    return {
      searchFrom: {
        name: '',
        phone: '',
        workNum: '',
        overTimeType: '',
        workType: '',
        serviceType: '',
        notifyType: [],
        id: ''
      },
      tableLoading: false,
      tableData: [],
      // 表单选项
      options: {
        // 项目类型
        projectList: [],
        // 超时类型选项
        overTimeList: [],
        // 通知方式
        notifyList: [],
        // 超时时间单位选项
        unitList: [],
        // 服务类型
        serviceList: [],
        // 工单类型
        workTypeList: []
      }
    }
  },
  computed: {
    OperateType() {
      return {
        View: 'View'
      }
    }
  },
  created() {
    this.getOptions()
  },
  mounted() {

    const query = this.$route.query
    // 默认可能携带ID过来
    this.searchFrom.id = query.id || ''
    if (query.page) {
      // 如果有参数，则还原查询数据
      this.pagination.current = Number(query.page)
      this.pagination.size = Number(query.size)
      this.searchFrom.name = query.name || ''
      this.searchFrom.phone = query.phone || ''
      this.searchFrom.workNum = query.workNum || ''
      this.searchFrom.workType = query.workType || ''
      this.searchFrom.serviceType = query.serviceType || ''
      this.searchFrom.overTimeType = query.overTimeType || ''
      if (query.notifyType) {
        this.searchFrom.notifyType = query.notifyType.split(',')
      }
      const newQuery = {}
      // 还原督办规则ID查询
      if (this.searchFrom.id) {
        newQuery.id = this.searchFrom.id
      }
      this.$router.replace({ query: newQuery })
    }
    this.getDataList()
  },
  methods: {
       workTypeChange(val) {
      this.options.serviceList = []
      this.getItemList(val)
    },
    getOptions() {
      // 获取项目类型
      // this.$api.getProjectList({ page: 1, pageSize: 9999 }).then((res) => {
      //   if (res.code === '200') {
      //     this.options.projectList = res.data.records
      //   }
      // })
      // 获取超时类型、超时时间单位、通知类型字典
      const types = [SuperviseDictType.NotifyType, SuperviseDictType.OverTimeType, SuperviseDictType.TimeUnit]
      this.$api.dictQueryByTypes({ dictTypeList: types }).then((res) => {
        if (res.code === '200') {
          res.data.forEach((it) => {
            if (it.dictType === SuperviseDictType.NotifyType) {
              this.options.notifyList = it.dictList
            } else if (it.dictType === SuperviseDictType.TimeUnit) {
              this.options.unitList = it.dictList
            } else if (it.dictType === SuperviseDictType.OverTimeType) {
              this.options.overTimeList = it.dictList
            }
          })
        }
      })
      // 获取工单类型选项
      this.$api.workOderType({ page: 1, pageSize: 999, ssmType: 2 }).then((res) => {
        if (res.code === '200') {
          this.options.workTypeList = res.data
        }
      })
    },
     // 服务事项
    getItemList(id) {
      let params = {
        id: id,
        nodeLevel: 1
      }
      this.$api.getItemList(params).then((res) => {
        if (res.code === '200') {
          this.options.serviceList = res.data.list.filter((it) => it.parent === '#')
        }
      })
    },
    // 点击搜索，重置分页并获取数据
    onSearch() {
      this.pagination.current = 1
      this.getDataList()
    },
    // 获取分页列表数据
    getDataList() {
      this.tableLoading = true
      this.tableData = []
      const params = {
        pageNo: this.pagination.current,
        pageSize: this.pagination.size,
        receiverName: this.searchFrom.name,
        receiverPhone: this.searchFrom.phone,
        workNum: this.searchFrom.workNum,
        timeoutTypeCode: this.searchFrom.overTimeType,
        workTypeCode: this.searchFrom.workType,
        itemTypeCode: this.searchFrom.serviceType,
        pushTypeStr: this.searchFrom.notifyType.join(),
        configId: this.searchFrom.id
      }
      this.$api.superviseRecordQueryByPage(params)
        .then((res) => {
          if (res.code === '200') {
            this.tableData = res.data.list
            this.pagination.total = res.data.count
          } else {
            throw res.msg
          }
        })
        .catch((msg) => this.$message.error(msg || '获取列表数据失败'))
        .finally(() => (this.tableLoading = false))
    },
    // 重置搜索表单，并触发搜索
    resetForm() {
      this.$refs.formRef.resetFields()
      this.onSearch()
    },
    // 列表操作相关
    onOperate(command, row) {
      switch (command) {
        case this.OperateType.View:
          const query = {
            size: this.pagination.size,
            page: this.pagination.current
          }
          // 合并查询条件到路由查询参数
          Object.assign(query, this.$route.query, this.searchFrom, {
            notifyType: this.searchFrom.notifyType.join()
          })
          this.$router.replace({ query }).then(() => {
            this.$router.push({ name: 'superviseDetail', query: { id: row.id } })
          })
          break
      }
    }
  }
}
</script>
<template>
  <PageContainer footer class="supervise-record">
    <template #content>
      <div class="supervise-record__search">
        <el-form ref="formRef" class="supervise-record__search__form" :model="searchFrom" inline>
          <el-form-item prop="name">
            <el-input v-model="searchFrom.name" placeholder="接收人"></el-input>
          </el-form-item>
          <el-form-item prop="phone">
            <el-input v-model="searchFrom.phone" placeholder="接收人手机号"></el-input>
          </el-form-item>
          <el-form-item prop="workNum">
            <el-input v-model="searchFrom.workNum" placeholder="工单号"></el-input>
          </el-form-item>
          <el-form-item prop="overTimeType">
            <el-select v-model="searchFrom.overTimeType" placeholder="超时类型" clearable>
              <el-option v-for="item in options.overTimeList" :key="item.dictCode" :label="item.dictName" :value="item.dictCode"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="workType">
            <el-select v-model="searchFrom.workType" placeholder="工单类型" @change="workTypeChange" clearable>
              <el-option v-for="item in options.workTypeList" :key="item.workTypeCode" :label="item.workTypeName" :value="item.workTypeCode"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="serviceType">
            <el-select v-model="searchFrom.serviceType" placeholder="服务事项" clearable :disabled="searchFrom.workType == ''">
              <el-option v-for="item in options.serviceList" :key="item.id" :label="item.text" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="notifyType">
            <el-select v-model="searchFrom.notifyType" placeholder="通知方式" multiple collapse-tags clearable>
              <el-option v-for="item in options.notifyList" :key="item.dictCode" :label="item.dictName" :value="item.dictCode"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div class="supervise-record__search__action">
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </div>
      </div>
      <div class="supervise-record__table table-content">
        <el-table
          v-loading="tableLoading"
          height="100%"
          :data="tableData"
          border
          stripe
          table-layout="auto"
          class="tableAuto"
          row-key="id"
          @row-dblclick="onOperate(OperateType.View, $event)"
        >
          <el-table-column label="序号" type="index" width="55" />
          <el-table-column label="工单类型" prop="workTypeName" width="120px" show-overflow-tooltip></el-table-column>
          <el-table-column label="服务事项" prop="itemTypeName" width="120px" show-overflow-tooltip></el-table-column>
          <el-table-column label="超时类型" prop="timeoutTypeName" width="160px" show-overflow-tooltip></el-table-column>
          <el-table-column label="工单号" prop="workNum" width="160px" show-overflow-tooltip></el-table-column>
          <el-table-column label="接收人" prop="receiverStr" show-overflow-tooltip></el-table-column>
          <el-table-column label="通知方式" prop="pushTypeStr" show-overflow-tooltip></el-table-column>
          <el-table-column label="督办时间" prop="pushTime" width="160px"></el-table-column>
          <el-table-column label="操作" width="80px">
            <template #default="{ row }">
              <el-button type="text" @click="onOperate(OperateType.View, row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        class="supervise-record__pagination"
        :current-page="pagination.current"
        :page-sizes="pagination.pageSizeOptions"
        :page-size="pagination.size"
        :layout="pagination.layoutOptions"
        :total="pagination.total"
        @size-change="paginationSizeChange"
        @current-change="paginationCurrentChange"
      >
      </el-pagination>
    </template>
    <template #footer>
      <el-button type="primary" plain @click="$router.back()">返回</el-button>
    </template>
  </PageContainer>
</template>
<style scoped lang="scss">
.supervise-record {
  ::v-deep(.container-content) {
    display: flex;
    flex-flow: column nowrap;
    padding: 16px;
    height: 100%;
    overflow: hidden;
    background-color: #fff;
  }
  &__search {
    display: flex;
    justify-content: space-between;
    line-height: 40px;
    &__form {
      flex: 1;
      .el-form-item {
        width: 200px;
        margin-bottom: 4px;
        .el-input {
          width: 200px;
        }
      }
    }
  }
  &__table {
    margin-top: 16px;
    flex: 1;
    overflow: hidden;
  }
  &__actions {
    margin-top: 8px;
  }
  &__pagination {
    margin-top: 10px;
  }
}
</style>
