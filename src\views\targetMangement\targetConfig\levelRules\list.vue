<template>
  <div class="tableContainer">
    <el-button type="primary" @click="addLevel">添加等级规则</el-button>
    <el-table v-loading="tableLoading" :data="list" border stripe height="calc(100% - 96px)" style="width: 100%; margin-top: 16px">
      <el-table-column prop="name" label="名称" show-overflow-tooltip :resizable="false"> </el-table-column>
      <el-table-column prop="scopeContent" label="等级内容" show-overflow-tooltip :resizable="false">
        <template slot-scope="scope">
          <div class="columnContent">
            <div class="columnList">
              <div>等级名称</div>
              <div>分值范围</div>
            </div>
            <div v-for="(item, index) in scope.row.cycleLevelList" :key="index" class="columnList">
              <div>{{ item.name }}</div>
              <div>{{ item.min }}-{{ item.max }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="auto" label="状态" show-overflow-tooltip :resizable="false">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.auto" :active-value="1" :inactive-value="0" @change="(e) => setState(scope.row)"> </el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" show-overflow-tooltip :resizable="false">
        <template slot-scope="scope">
          <el-button type="text" @click="addLevel(scope.row)">编辑</el-button>
          <el-button type="text" :disabled="scope.row.defaultState === 1" @click="del(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      style="margin-top: 16px"
      :current-page="pagination.page"
      :page-sizes="[15, 30, 50, 100]"
      :page-size="pagination.size"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pagination.total"
      @size-change="(val) => paginatChange('pageSize', val)"
      @current-change="(val) => paginatChange('page', val)"
    >
    </el-pagination>
  </div>
</template>
<script>
export default {
  name: 'cycleList',
  props: {
    type: {
      type: String,
      default: '1'
    }
  },
  data() {
    return {
      tableLoading: false,
      list: [],
      pagination: {
        pageSize: 15,
        page: 1,
        total: 0
      }
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    addLevel(row) {
      this.$router.push({ path: '/targetConfig/updateLevelRules', query: { id: row ? row.id : '', type: this.type } })
    },

    del(row) {
      this.$confirm('是否确认删除本条周期规则?', '提示', {
        cancelButtonClass: 'el-button--primary is-plain',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$api.delTargetConfig({ cycleLevel: this.type, id: row.id }, { 'operation-type': 3, 'operation-id': row.id, 'operation-name': row.name}).then((res) => {
            if (res.code === '200') {
              this.$message.success('删除成功')
              this.getList()
            }
          })
        })
        .catch(() => {})
    },
    setState(row) {
      if (row.auto === 1) {
        // 因为这里用的是change事件，进入逻辑之前，值已经被修改了，所以取反，
        // 关闭，准备开启
        this.$api.haveOpenCycleRule({ id: row.id }).then((res) => {
          if (res.code === '200') {
            if (!res.data) {
              this.$api.openRule({ id: row.id }).then((res) => {
                if (res.code === '200') {
                  this.$message.success('更改成功')
                  this.getList()
                }
              })
            } else {
              this.$alert('同时仅可以生效一条等级规则', '提示', {
                confirmButtonText: '确定',
                callback: (action) => {
                  // 还原值
                  row.auto = 0
                }
              })
            }
          }
        })
      } else {
        this.$api.closeRule({ id: row.id }).then((res) => {
          if (res.code === '200') {
            this.$message.success('更改成功')
            this.getList()
          }
        })
      }
    },
    getList() {
      this.$api.targetConfigList({
        cycleLevel: this.type,
        ...this.pagination
      })
        .then((res) => {
          if (res.code === '200') {
            this.list = res.data.records
            this.pagination.total = res.data.total
          }
        })
    },
    paginatChange(name, val) {
      this.pagination[name] = val
      if (name === 'pageSize') {
        this.pagination.page = 1
      }
      this.getList()
    }
  }
}
</script>
<style lang="scss" scoped>
.tableContainer {
  width: 100%;
  height: 100%;
  padding-top: 16px;
}
.columnContent {
  padding: 5px;
  .columnList {
    font-style: 12px;
    color: #7f848c;
    display: flex;
    div {
      width: 30%;
      margin-right: 20px;
      padding: 5px;
    }
    &:first-child {
      font-style: 12px;
      color: #666666;
    }
  }
}
</style>
