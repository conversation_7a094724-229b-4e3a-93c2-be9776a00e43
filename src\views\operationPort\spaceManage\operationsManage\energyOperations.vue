<template>
  <div class="energy-content">
    <component :is="energyModel" :energyConsumptionName="energyConsumptionName"></component>
  </div>
</template>
<script>
export default {
  name: 'energyOperations',
  components: {
    energyOverview: () => import('@/views/energy/overview/energyOverview'),
    powerOverview: () => import('@/views/energy/powerMenu/overview'),
    waterOverview: () => import('@/views/energy/waterMenu/overview'),
    coldEnergyMenu: () => import('@/views/energy/coldEnergyMenu/overview')
    // gasOverview: () => import('@/views/energy/gasMenu/overview')
  },
  props: {
    energyModel: {
      type: String,
      default: 'energyOverview'
    }
  },
  data() {
    return {
      energyList: [
        {
          energyOverview: '',
          powerOverview: '电力',
          waterOverview: '用水'
          // gasOverview: '蒸汽'
        }
      ],
      energyConsumptionName: ''
    }
  },
  watch: {
    energyModel(val) {
      this.energyConsumptionName = this.energyList[val]
      console.log(val)
    }
  },
  mounted() {
    console.log(this.energyModel)
  }
}
</script>
<style lang="scss" scoped>
.energy-content {
  width: 100%;
  height: 100%;
}
</style>
