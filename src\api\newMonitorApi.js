import { getRequest, postRequest, postQs, postRequest1, postRequest2, dataExport, postFile } from './httpApi.js'
export default {
  // New监测---------------------------
  // 图片上传
  imageUploadOss: postRequest('fileController/upload'), // 品类管理list
  newUploadFiles: postFile('fileController/upload', __PATH.VUE_MONITOR_API, 'formdata'), // 上传文件
  // 系统设置-品类管理-----------------------------------------------------------------
  getCategoryManagementList: getRequest('dictionary/queryCategoryDetails/rewrite'), // 品类管理list
  getCategoryManagementDetailById: getRequest('dictionary/getDetailById'), // 品类管理详情
  addDetailCategoryManagement: postRequest('dictionary/addDetail'), // 保存品类管理
  getCategoryEditDetail: postRequest('dictionary/editDetail'), // 品类管理编辑保存
  deleteCategoryManagementData: postQs('dictionary/deleteDetail', __PATH.VUE_MONITOR_API, 'form'), // 品类管理删除
  // 系统设置-区间状态-----------------------------------------------------------------
  getIntervalStateDeviceType: getRequest('dictionary/queryRootSubCategoryDetails'), // 设备类型查询
  getIntervalStatePage: postRequest('sysSection/page'), // 区间状态列表
  getIntervalStateEnable: postQs('sysSection/enable', __PATH.VUE_MONITOR_API, 'form'), // 区间状态启禁用
  getIntervalStateDeleteById: postQs('sysSection/deleteById', __PATH.VUE_MONITOR_API, 'form'), // 区间状态删除
  getIntervalStateParameter: getRequest('sysSection/parameter/list'), // 设备类型-监测参数
  getIntervalStateSave: postRequest('sysSection/save'), // 区间状态保存
  getIntervalStateQueryById: getRequest('sysSection/queryById'), // 区间状态详情
  // 系统设置-系统管理-----------------------------------------------------------------
  getSystemManagementPage: postRequest('sysSettings/page'), // 系统管理list
  getSystemManagementSave: postRequest('sysSettings/save'), // 系统管理编辑保存
  getSystemManagementQueryById: getRequest('sysSettings/queryById'), // 系统管理详情
  getSystemManagementList: postRequest('sysSettingsChildConfig/list'), // 通用功能-系统管理
  getSystemManagementEnable: postQs('/sysSettings/enable', __PATH.VUE_MONITOR_API, 'form'), // 系统管理-启用禁用
  getSystemManagementDeleteById: postQs('sysSettings/deleteById', __PATH.VUE_MONITOR_API, 'form'), // 系统管理删除
  getSystemManagementAlllist: getRequest('sysSettings/list'), // 系统管理所有数据列表
  // --------------运行总览----------------------------------------------
  getOperationalOverview: getRequest('deviceAnalysis/getDeviceSummary'), // 运行总览list
  // getModuleMenuTree: getRequest('moduleMenuSettings/queryListBySystemCode'), // 运行总览-通过系统编码查询运行总览模块菜单右侧树
  getModuleMenuTree: getRequest('moduleMenuSettings/queryListBySystemCode/tile'), // 运行总览-通过系统编码查询运行总览模块菜单右侧树
  getDeviceAnalysis: getRequest('deviceAnalysis/getDeviceAnalysis'), // 运行总览-获取设备分析数据
  saveModuleMenuSettings: postRequest('moduleMenuSettings/saveModuleMenuSettings'), // 运行总览-保存模块菜单设置
  getProductList: getRequest('envMonitor/product/list'), // 运行总览-环境监测
  getFactoryCodeByAgv: getRequest('assetsInfo/getFactoryCodeByAgv'), // 运行总览-agv设备 id
  getCarryTrends: postRequest('agv/carryTrends'), // 运行总览-agv运送数量趋势
  getMasterOrMonitoredByCode: getRequest('assetsInfo/getMasterOrMonitoredByCode'), // 运行总览-根据系统编码查询被监测设备或者主设备
  getAssetsCountBySysCode: getRequest('assetsInfo/getAssetsCountBySysCode'), // 运行总览-根据系统编码查询被监测设备或者主设备
  getAirconditionList: postRequest('assetsEnergy/energy/list/aircondition'), // 运行总览-空调能耗
  dtToDayOperation: postRequest('operation/overview/dtToDayOperation'), // 运行总览-今日运行时长
  dtTotalOperation: postRequest('operation/overview/dtTotalOperation'), // 运行总览-总运行时长
  queryAlarmConfigByPage: postRequest('alarm-center/alarm/fieldsConfig/queryFieldsConfigList'), // 运行总览-报警次数排行报警类型
  selectAlarmCountByAlarmType: postRequest('alarm-center/sinomis-alarm/alarm/record/selectAlarmCountByAlarmType'), // 运行总览-报警次数排行
  // --------------运行监测----------------------------------------------
  // -------------------------------------------------------------图纸模式
  getDictionaryDetailsCode(dictionaryDetailsCode) {
    return getRequest(`assetsInfo/query/image/list/${dictionaryDetailsCode}`)()
  }, // 图形模式-scadalist
  getScadaData: postRequest('scalaDrawing/getScalaEntityMenuList'), // 获取scada数据
  // -------------------------------------------------------------列表模式
  getOperationalMonitoringList: postRequest('assetsInfo/page'), // 运行监测list
  addOperationalMonitoring: postRequest('assetsInfo/save'), // 保存运行监测
  getSelectedDeptNew: getRequest('basedata/departmentManager/department-manager/getSelectedDept'), // 归口部门下拉
  getAssetCoding: getRequest('assetsInfo/genAssetsCode'), // 运行总览list
  getQueryRootSubCategoryDetails: getRequest('dictionary/querySubCategoryDetails'), // 运行监测新增 系统类型
  querySubCategoryDetailsRelatedAssets: getRequest('dictionary/querySubCategoryDetailsRelatedAssets'), // 运行监测新增 系统类型
  getQueryGenAssetsCode: getRequest('basedata/assets/genAssetsCode'), // 运行监测 新增-自动生成编码
  getDeviceProduct: postRequest('jetlinks/device-product/_query/no-paging'), // 运行监测新增 所属产品
  getSpaceTreeList: getRequest('basedata/space/structure/spaceTree'), // 运行监测新增 空间树
  getQueryCategoryByCategoryId: getRequest('dictionary/queryCategoryByCategoryId'), // 运行监测-资产状态
  getCustomGroupingTree: postRequest('assetsGroup/tree'), // 运行监测-自定义分组树
  getCustomGroupingListGroup: postRequest('assetsGroup/list/group'), // 运行监测-自定义分组的设备列表
  getCustomGroupingsaveRelated: postRequest('assetsGroup/saveRelated'), // 运行监测-自定义分组设备列表保存
  getCustomGroupingSave: postRequest('assetsGroup/save'), // 运行监测-自定义分组-新增
  getQueryByIdDisplay: getRequest('assetsInfo/queryById/display'), // 运行监测-列表-回显编辑
  getQueryDeleteById: postQs('assetsInfo/deleteById', __PATH.VUE_MONITOR_API, 'form'), // 运行监测-列表-删除
  getCustomGroupingDeleteById: postQs('assetsGroup/deleteById', __PATH.VUE_MONITOR_API, 'form'), // 运行监测-自定义分组-删除
  getCustomGroupingQueryById(id) {
    return getRequest(`assetsGroup/queryById?id=${id}`)()
  }, // 运行监测-自定义分组-查询
  getQueryDataById: getRequest('assetsInfo/queryById'), // 运行监测-详情
  getQueryrealtimeData: getRequest('assetsInfo/query/realtimeData'), // 运行监测-详情-实时数据
  getQueryFunction(id) {
    return getRequest(`assetsInfo/query/function/${id}`)()
  }, // 运行监测-控制参数
  getQueryInstanceFunction(deviceId, functionId, params = {}) {
    return postRequest1(`jetlinks/device/instance/${deviceId}/function/${functionId}`, __PATH.VUE_MONITOR_API, params)()
  }, // 运行监测-控制参数 执行
  getRecordStatus(deviceId, params = {}) {
    return getRequest(`assetsCameraRecord/queryDevice/recordStatus/${deviceId}`, __PATH.VUE_MONITOR_API, params)()
  }, // 获取录像状态
  startRecording(deviceId, params = {}) {
    return postRequest1(`assetsCameraRecord/device/instance/${deviceId}/function/startRecording`, __PATH.VUE_MONITOR_API, params)()
  }, // 开始录像
  stopRecording(deviceId, params = {}) {
    return postRequest1(`assetsCameraRecord/device/instance/${deviceId}/function/stopRecording`, __PATH.VUE_MONITOR_API, params)()
  }, // 停止录像
  getQueryPropertiesRow(deviceId, property, belongAssetsName, params = {}) {
    return postRequest1(`assetsInfo/device/instance/${deviceId}/properties/row`, __PATH.VUE_MONITOR_API, params)({ property, belongAssetsName })
  }, // 运行监测-历史数据
  getCustomIotAssets(id) {
    return getRequest(`assetsInfo/query/iotAssets/${id}`)()
  }, // 运行监测-历史数据-被监测设备绑定列表
  getCustomIotAssetsProperties(id, factoryCode, filter) {
    return getRequest(`assetsInfo/query/properties/${id}?factoryCode=${factoryCode}&filter=${filter}`)()
  }, // 运行监测-历史数据-被监测设备绑定参数列表
  getAssetsAlertPage: postRequest('alarm-center/sinomis-alarm/alarm/record/selectAlarmRecordAll'), // 运行监测-报警记录列表
  getTopRunTimeData(groupId, params = {}) {
    return postRequest1('alarm-center/sinomis-alarm/alarm/record/getTopData', __PATH.VUE_MONITOR_API, params)({ groupId })
  }, // 运行监测-获取报警运行排行
  getSelectDiffProjectCodeAlarm(groupId, params = {}) {
    return postRequest1('/alarm-center/sinomis-alarm/alarm/record/selectAlarmGroupByType', __PATH.VUE_MONITOR_API, params)({ groupId })
  }, // 运行监测-获取报警分析统计饼图
  getAlarmTrendPcByAlarmType(groupId, params = {}) {
    return postRequest1('alarm-center/sinomis-alarm/alarm/record/getAlarmTrendPcByAlarmType', __PATH.VUE_MONITOR_API, params)({ groupId })
  }, // 运行监测-获取报警分析折现图
  getElevatorRunTimeData: getRequest('deviceAnalysis/elevator/getRunTimeData'), // 运行监测-获取电梯运行排行
  getAirConditionOfflineCountTrend: getRequest('deviceAnalysis/getDeviceOfflineCount'), // 运行监测-获取空调离线次数排行
  getAirConditionRunningTimeDataTrend: getRequest('deviceAnalysis/airconditioner/getMaxRunTime'), // 运行监测-获取空调运行时长排行
  getPropertiesHistoryData(params = {}) {
    return postRequest1('data/center/iot/model/properties/line/statistics', __PATH.VUE_MONITOR_API, params)()
  }, // 运行监测-历史数据图形
  getCustomIotExport(deviceId, property, belongAssetsName, params = {}) {
    return postRequest2(`assetsInfo/device/instance/${deviceId}/properties/row/export`, __PATH.VUE_MONITOR_API, params)({ property, belongAssetsName })
  }, // 运行监测-历史数据-导出
  getCustomEvents(id) {
    return getRequest(`assetsInfo/query/events/${id}`)()
  }, // 运行监测-事件记录
  getQueryInstanceEvent(deviceId, eventId, params = {}) {
    return postRequest1(`jetlinks/device-instance/${deviceId}/event/${eventId}`, __PATH.VUE_MONITOR_API, params)()
  }, // 运行监测-事件记录列表
  getCustomGroupingQueryList(id) {
    return getRequest(`assetsCameraRelated/queryList?id=${id}`)()
  }, // 运行监测-详情-监控画面
  getOperationalMonitoringQuery: postRequest('assetsIot/device/page'), // 运行监测-配置-添加配置列表
  getlMonitoringList: postRequest('assetsClient/assets/list'), // 运行监测-监测设备列表
  getDeviceProperties(id) {
    return getRequest(`assetsIot/device/properties/${id}`)()
  }, // 运行监测-配置-添加配置列表
  getOperationalMonitoringPage: postRequest('assetsCameraRelated/page'), // 运行监测-配置-关联摄像头
  getOperationalFolderList: postRequest('data/folders/list'), // 运行监测-配置-scala图纸列表
  getOperationalFolderId(folderId) {
    return postRequest1(`data/folders/image/list/${folderId}`)()
  }, // 运行监测-配置-图纸二级联动
  getOperationalCollection(collection) {
    return postRequest1(`data/${collection}/get`)()
  }, // 运行监测-配置-图纸
  getOperationalImageSelectById: postQs('scalaImage/getImageSelectById', __PATH.VUE_MONITOR_API, 'form'), // 运行监测-配置-图纸三级联动
  getOperationalRelate: postRequest('assetsInfo/save/relate'), // 运行监测-配置保存
  getTaskByTaskPointId: postRequest('inspect/planTaskNew/getTaskByTaskPointId'), // 运行监测-维保年检记录列表
  getTaskPointReleaseDetail: postQs('inspect/taskPointRelease/detail', __PATH.VUE_MONITOR_API, 'form'), // 运行监测-维保年检记录详情
  getAssetsPositionPage: postRequest('assetsPosition/page'), // 运行监测-定位轨迹
  getDoctorVisitRecordByPage: postRequest('his/visit/getDoctorVisitRecordByPage'), // 运行监测-出诊记录
  getPatientVisitRecordByPage: postRequest('his/visit/getPatientVisitRecordByPage'), // 运行监测-就诊记录
  getChairTotal: postRequest('data/center/adszqlywsbproperties/adsZqlYwsbProperties/chairTotal'), // 运行监测-就诊记录统计数据
  getAdsAreaPeopleTotal: postRequest('data/center/adsareapeopletotal/adsAreaPeopleTotal/pageList'), // 运行监测-通行记录
  // ----------------------场景策略-----------------------------------------
  getScenarioStrategyList: getRequest('scenes/queryScenes'), // 场景策略-列表
  getSceneByIdOrCreateNew: getRequest('scenes/getSceneByIdOrCreateNew'), // 场景策略-新增查询周期字典-详情
  getSceneWeatherTypes: getRequest('scenes/weatherTypes'), // 场景策略-新增查询天气字典
  getaddScene: postRequest('scenes/addScene'), // 场景策略-新增保存
  getupdateScene: postRequest('scenes/updateScene'), // 场景策略-编辑更新
  getQueryDeleteScene: postQs('scenes/deleteScene', __PATH.VUE_MONITOR_API, 'form'), // 场景策略-删除单条
  getQueryBatchDelete: postRequest('scenes/batchDelete'), // 场景策略-批量删除
  getQueryBatchUpdateStatus: postRequest('scenes/batchUpdateStatus'), // 场景策略-批量启停用
  getQueryAssetsLinked: getRequest('scenes/queryAssetsLinked'), // 场景策略-设备列表
  getQueryAssetsNotLinked: postRequest('scenes/queryAssetsNotLinked'), // 场景策略-设备-选择设备弹窗
  getQueryTimeScheduleId(timeScheduleId) {
    return getRequest(`scenes/bindAssetsList/${timeScheduleId}`)()
  }, // 场景策略-设备-绑定设备
  getQueryAssetsBindAssets: postRequest('scenes/bindAssets'), // 场景策略-设备-选择设备绑定
  getQueryDeleteBindAssets: postRequest('scenes/deleteBindAssets'), // 场景策略-设备-批量删除
  getupdateBindAssetsStatus: postRequest('scenes/updateBindAssetsStatus'), // 场景策略-批量隔离
  getQueryAssetsExecuteBatch: postRequest('scenes/executeBatch'), // 场景策略-批量执行
  getQueryAssetsFunctionsList: postRequest('scenes/assets/functionsList'), // 场景策略-批量设定
  getQueryDeleteBindAssetById: postQs('scenes/deleteBindAssetById', __PATH.VUE_MONITOR_API, 'form'), // 场景策略-设备删除单条
  getQueryExecuteById: postQs('scenes/executeById', __PATH.VUE_MONITOR_API, 'form'), // 场景策略-设备执行单条
  getQueryUpdateSortOrder: postRequest('scenes/updateSortOrder'), // 场景策略-设备-更新排序
  getQueryAssetsByIdFunctionsList: postQs('scenes/assetById/functionsList', __PATH.VUE_MONITOR_API, 'form'), // 场景策略-设备-控制设定
  getQuerySaveFunctionsList: postRequest('scenes/asset/saveFunctionsList'), // 场景策略-设备-控制设定保存
  getAssetParameters: getRequest('scenes/getAssetParameters'), // 场景策略-设备-控制设定
  // ---------------------运行日历--------------------------------
  getSceneNamesByDate: getRequest('sceneExecutionHistory/getSceneNamesByDateRewrite'), // 运行日历-日历列表名称
  getMonthlyExecutionHistory: getRequest('sceneExecutionHistory/getMonthlyExecutionHistory'), // 运行日历-表格列表
  getQueryByDate: getRequest('sceneExecutionHistory/queryByDate'), // 运行日历-日历详情
  getAssetsExecutionHistory: postRequest('sceneExecutionHistory/getAssetsExecutionHistoryRewrite'), // 运行日历-日历详情下的设备
  // 配电系统-历史数据
  getHistoryDataList: postRequest('assetsInfo/device/properties/history/data/list'), // 配电系统-历史数据列表
  getHistoryDataExportBatch: dataExport('assetsInfo/device/instance/properties/row/export/batch'), // 配电系统-历史数据-原始数据批量导出
  getHistoryDataExport: dataExport('assetsInfo/device/properties/history/data/export'), // 配电系统-历史数据-极值数据导出
  // 配电系统-统计分析
  getAssetsEnergyList: postRequest('assetsEnergy/energy/list'), // 配电系统-用能统计分析列表
  getAssetsEnergySegment: postRequest('assetsEnergy/energy/segment'), // 配电系统-分时段统计分析列表
  energyConfigSave: postRequest('energyConfig/save'), // 配电系统-分时段配置保存
  energyConfigList: postRequest('energyConfig/list'), // 配电系统-分时段配置回显
  // ----------------------综合监控-----------------------------------------
  getAssetsDetailByIntegratedMonitor: getRequest('assetsInfo/getAssetsDetailByIntegratedMonitor'), // 综合监控-基本信息
  getRealtimeDataByPage: getRequest('assetsInfo/query/realtimeData/byPage'), // 综合监控-监测信息
  getRelatedCameraList: getRequest('assetsClient/device/relatedCamera/list'), // 综合监控-监控列表
  getTaskRecordByTaskPointId: postRequest('inspect/planTaskNew/getTaskRecordByTaskPointId'), // 综合监控-维保年检列表
  selectAlarmLevelByObjectId: postRequest('alarm-center/sinomis-alarm/alarm/record/selectAlarmLevelByObjectId'), // 综合监控-报警数量统计
  getAlarmTypeByAlarmObjectId: postQs('alarm-center/alarm/record/getAlarmTypeByAlarmObjectId', __PATH.VUE_MONITOR_API, 'form'), // 综合监控-应急预案报警类型
  getConfigPageByTypes: postRequest('preplan/preplanBasic/getConfigPageByTypes'), // 综合监控-应急预案列表
  getAlarmThirdSystemList: postRequest('alarm-center/alarm/fieldsConfig/getAlarmThirdSystemList') // 综合监控-应急预案报警分类
}
