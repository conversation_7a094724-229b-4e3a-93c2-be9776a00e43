<template>
  <ContentCard
    :title="item.componentTitleShow"
    :scrollbarHover="true"
    :cstyle="{ height: '100%', width: '100%' }"
    class="drag_class"
    :hasMoreOper="['edit']"
    @more-oper-event="(val) => $emit('all-more-Oper', val, 'alarmLargeAnalysis')"
  >
    <div slot="content" class="operation-list" style="height: 100%; width: 100%">
      <div class="call_the_police" style="width: 100%">
        <TimeQuery ref="timeQuery" @submit="submit" />
        <el-row class="display_graphics">
          <el-col v-loading="loading.PoliceInfoLoading" class="display_graphics_left" :xs="12" :md="12" :lg="10" :xl="8" style="height: 300px">
            <div class="cardContent-left">
              <div v-for="item in alarmStatisticsList" :key="item.title" class="left-item">
                <p class="item-title">{{ item.title }}</p>
                <p class="item-value" :style="{ color: item.color }">{{ item.value || 0 }}<span>个</span></p>
                <img class="item-icon" :src="item.icon" :alt="item.title" />
              </div>
            </div>
            <div class="callThePolice">
              <echarts
                :ref="`callThePolice`"
                :domId="`callThePolice`"
                :onLegendClickBool="true"
                :isMonitor="false"
                style="margin-left: -80px; width: 100%"
                @onClickChartLegend="(data) => jumpAlarmRecord(data.data.projectCode, queryParams.dateRange)"
                @onClickChart="(data) => jumpAlarmRecord(data.data.projectCode, queryParams.dateRange)"
              />
            </div>
          </el-col>
          <el-col v-loading="loading.PoliceTrendLoading" class="display_graphics_right" :xs="24" :md="24" :lg="14" :xl="16" style="height: 300px">
            <echarts
              :ref="`alarmTrend`"
              :domId="`alarmTrend`"
              :isMonitor="false"
              style="margin-top: 15px; width: 100%"
              @onClickChart="(data) => jumpAlarmRecord(systemCode, queryParams.dateRange)"
            />
          </el-col>
        </el-row>
      </div>
    </div>
  </ContentCard>
</template>
<script>
import alarmStatistics from '@/assets/images/monitor/alarm-statistics.png'
import alarmAlarm from '@/assets/images/monitor/alarm-pending.png'
import alarmDoing from '@/assets/images/monitor/alarm-doing.png'
import TimeQuery from './timeQuery.vue'
import mixins from '../../mixins/chartMixin.js'
import moment from 'moment'
import * as echarts from 'echarts'
import { newMonitorTypeList } from '@/util/newDict.js'
import { auth } from '@/util'
export default {
  name: 'alarmLargeAnalysis',
  components: {
    TimeQuery // 时间选择
  },
  mixins: [mixins],
  props: {
    item: {
      type: Object,
      default: () => {}
    },
    systemCode: {
      type: String,
      default: ''
    },
    fireControl: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      // 报警数日期描述
      dateText: null,
      rows: [],
      alarmStatisticsList: [
        {
          title: '报警总数',
          color: '#3562DB',
          icon: alarmStatistics,
          value: 0
        },
        {
          title: '未处理',
          color: '#FA403C',
          icon: alarmAlarm,
          value: 0
        },
        {
          title: '处理中',
          color: '#FF9435',
          icon: alarmDoing,
          value: 0
        }
      ],
      // hourDayOrMouth: 0,
      policeInfo: {},
      loading: {
        PoliceInfoLoading: false,
        PoliceTrendLoading: false
      }
    }
  },
  mounted() {
    setTimeout(() => {
      this.getEcharPieBig()
      this.onPoliceTrend()
    }, 100)
  },
  methods: {
    // 跳转报警中心
    jumpAlarmRecord(systemCode, dateRange) {
      // this.$router.push({
      //   name: 'AlarmRecordIndex',
      //   query: {
      //     projectCode: systemCode,
      //     dataRange: dateRange
      //   }
      // })
    },
    submit(data) {
      this.dateText = data.dateText
      data['projectCode'] = this.systemCode
      this.queryParams = data
      this.getEcharPieBig()
      this.onPoliceTrend()
    },
    // 报警趋势折线图
    onPoliceTrend(id) {
      this.loading.PoliceTrendLoading = true
      const params = {
        projectCode: this.systemCode,
        alarmType: '', // 报警类型
        startTime: this.queryParams.startTime,
        endTime: this.queryParams.endTime,
        hourDayOrMouth: this.queryParams.hourDayOrMouth // 趋势跨度(0:小时 1:日 2:月 3:年)
      }
      let groupId = id || ''
      this.$api.getAlarmTrendPcByAlarmType(groupId, params).then((res) => {
        this.loading.PoliceTrendLoading = false
        this.$refs.alarmTrend.init(this.setLineChart(res.data || []))
      })
    },
    // 报警统计
    getEcharPieBig(id) {
      let params = {
        projectCode: this.systemCode,
        alarmType: '', // 报警类型
        startTime: this.queryParams.startTime,
        endTime: this.queryParams.endTime,
        hourDayOrMouth: this.queryParams.hourDayOrMouth // 趋势跨度(0:小时 1:日 2:月 3:年)
      }
      let groupId = id || ''
      let newArr = []
      this.loading.PoliceInfoLoading = true
      this.$api
        .getSelectDiffProjectCodeAlarm(groupId, params)
        .then((res) => {
          this.loading.PoliceInfoLoading = false
          if (res.code === '200') {
            ;(res.data?.policeList ?? []).forEach((item) => {
              newArr.push({
                name: item.alarmTypeName,
                value: item.count
              })
            })
            this.alarmStatisticsList[0].value = res.data?.total ?? 0
            this.alarmStatisticsList[1].value = res.data?.noDealCount ?? 0
            this.alarmStatisticsList[2].value = res.data?.isDealCount ?? 0
            this.$refs.callThePolice.init(this.setPieChart(newArr))
          } else {
            this.$refs.callThePolice.init(this.setPieChart())
          }
        })
        .catch(() => {
          this.loading.PoliceInfoLoading = false
          this.$refs.callThePolice.init(this.setPieChart())
        })
    },
    // 饼图
    setPieChart(data) {
      let option
      var colors = ['#5e81ec', '#ffc855', '#98e79b', '#00d695', '#00b29a', '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc']
      if (data.length) {
        option = {
          title: {
            text: '报警占比统计',
            left: 'right',
            textStyle: {
              fontSize: 15,
              fontWeight: 500
            }
          },
          legend: {
            top: 30,
            right: 0,
            orient: 'vertical', // 设置为垂直方向
            data: data.map((item) => item.name),
            formatter: (name) => {
              let item = data.find((v) => v.name == name)
              return `${name}  ： ${item.value}个`
            },
            type: 'scroll', // 添加可滚动的图例
            padding: [5, 5, 5, 5], // 可选：设置图例的内边距
            itemGap: 10 // 可选：设置图例项之间的间距
          },
          tooltip: {
            trigger: 'item',
            formatter: '{b} : {c} ({d}%)',
            confine: true
          },
          color: colors,
          calculable: true,
          series: [
            {
              type: 'pie',
              radius: ['65%', '90%'],
              avoidLabelOverlap: false,
              label: {
                show: false,
                position: 'center'
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: 15
                }
              },
              labelLine: {
                show: false
              },
              data: data
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        echarts.init(this.$refs.callThePolice).resize()
      })
      return option
    },
    // 折线图
    setLineChart(data = []) {
      function colorAdd(transparency) {
        return [
          `rgba(255, 100, 97, ${transparency})`,
          `rgba(255, 148, 53, ${transparency})`,
          `rgba(53, 98, 219, ${transparency})`,
          `rgba(0, 188, 109, ${transparency})`,
          `rgba(115, 192, 222, ${transparency})`,
          `rgba(154, 96, 180, ${transparency})`,
          `rgba(250, 200, 88, ${transparency})`,
          `rgba(9, 205, 143, ${transparency})`
        ]
      }
      let colorHalf = colorAdd('.5')
      let colorZero = colorAdd('0')
      let option
      if (data.length) {
        option = {
          color: colorHalf,
          title: {
            text: '报警趋势图',
            left: 'center'
          },
          tooltip: {
            trigger: 'axis'
          },
          legend: {
            show: true,
            top: 35
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: data.map((x) => {
              return x.time
            }),
            axisLabel: {
              rotate: 0,
              formatter: function (value) {
                const dateTimeParts = value.split(' ')
                if (dateTimeParts.length === 2) {
                  return `${dateTimeParts[0]}\n${dateTimeParts[1]}`
                } else {
                  return dateTimeParts[0] // 直接返回日期
                }
              }
            }
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: '{value}'
            }
          },
          series: this.dataArr(data, colorHalf, colorZero),
          dataZoom: [
            {
              type: 'slider',
              start: 0,
              end: 70,
              xAxisIndex: [0],
              height: 10
            },
            {
              type: 'slider',
              start: 0,
              end: 70,
              xAxisIndex: [0],
              height: 10
            }
          ],
          grid: {
            left: '1%',
            right: '4%',
            bottom: '1%',
            containLabel: true
          }
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        echarts.init(this.$refs.alarmTrend).resize()
      })
      return option
    },
    dataArr(data, colorHalf, colorZero) {
      let arr = []
      data.forEach((v) => {
        v.list.length &&
          v.list.forEach((sonV, index) => {
            let obj = arr.find((ele) => ele.alarmType == sonV.alarmType)
            if (obj) {
              obj.data.push(sonV.count)
            } else {
              let dataObj = {
                name: sonV.alarmTypeName,
                alarmType: sonV.alarmType,
                type: 'line',
                data: [sonV.count],
                areaStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: colorHalf[(index + 1) % colorHalf.length]
                      },
                      {
                        offset: 1,
                        color: colorZero[(index + 1) % colorZero.length]
                      }
                    ])
                  }
                }
              }
              arr.push(dataObj)
            }
          })
      })
      return arr
    }
  }
}
</script>
<style lang="scss" scoped>
.call_the_police {
  background: white;
  padding: 15px;
  display: flex;
  flex-direction: column;
  .semicircle::before {
    content: ' ';
    display: inline-block;
    width: 7px;
    height: 13px;
    background: #3562db;
    border-radius: 0 7px 7px 0;
    opacity: 1;
    margin-right: 4px;
  }
  .semicircle {
    font-size: 15px;
    display: flex;
    align-items: center;
  }
  .display_graphics {
    flex: 1;
    // overflow: auto;
    // display: flex;
    &_left {
      // width: 28%;
      // margin-right: 1%;
      height: 100%;
      // flex: 1;
      display: flex;
      flex-direction: column;
      .cardContent-left {
        display: flex;
        // flex-direction: column;
        justify-content: center;
        width: 100%;
        p {
          margin: 0;
        }
        .left-item {
          flex: 1;
          padding: 14px 22px;
          background: #faf9fc;
          border-radius: 4px;
          margin-bottom: 7px;
          position: relative;
          margin-right: 16px;
          .item-title {
            font-size: 15px;
            font-weight: 500;
            color: #121f3e;
          }
          .item-value {
            margin-top: 4px;
            font-size: 30px;
            & > span {
              font-size: 15px;
              font-weight: 500;
              color: #ccced3;
            }
          }
          .item-icon {
            position: absolute;
            right: 22px;
            bottom: 14px;
            width: 40px;
            height: 40px;
          }
        }
        & :last-child {
          margin-right: 0;
        }
      }
      .callThePolice {
        flex: 1;
        width: 100%;
        margin-top: 20px;
      }
    }
    &_right {
      position: relative;
      height: 100%;
      // width: 72%;
      .location_sele {
        position: absolute;
        right: 0;
        top: 0;
        z-index: 50;
        .el-select {
          width: 90px;
        }
      }
    }
  }
}
</style>
