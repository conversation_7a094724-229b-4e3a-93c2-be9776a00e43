<template>
  <PageContainer :footer="true">
    <div slot="content" v-loading="formLoading" class="form-content">
      <ContentCard title="基本信息">
        <div slot="content" class="footer-role">
          <el-form ref="formInline" :model="formInline" :rules="rules">
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="7">
                <el-form-item label="系统类型" prop="systemTypeCode" label-width="110px">
                  <el-select v-model="formInline.systemTypeCode" placeholder="请选择系统类型" @change="systemTypeChange"
                    :disabled="disabledData">
                    <el-option v-for="(item, index) in systemType" :key="index" :label="item.dictionaryDetailsName"
                      :value="item.dictionaryDetailsCode"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :md="7">
                <el-form-item label="设备类型" prop="deviceTypeCode" label-width="110px">
                  <el-select v-model="formInline.deviceTypeCode" placeholder="请选择设备类型" class="ml-16"
                    @change="deviceTypeChange" :disabled="disabledData">
                    <el-option v-for="(item, index) in deviceType" :key="index" :label="item.dictionaryDetailsName"
                      :value="item.dictionaryDetailsCode"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </ContentCard>
      <ContentCard title="参数配置">
        <div slot="content">
          <el-form ref="sonFormInline" :model="sonFormInline" :rules="rules2" :validate-on-rule-change="false">
            <el-row :gutter="24" style="margin: 0 0 20px">
              <el-col :md="7">
                <el-form-item label="监测参数" prop="monitoringParameter" label-width="110px">
                  <el-select v-model="sonFormInline.monitoringParameter" placeholder="请选择检测参数" class="ml-16"
                    @change="monitoringParameterChange">
                    <el-option v-for="(item, index) in monitoringParameterList" :key="index" :label="item.metadataName"
                      :value="item.metadataTag"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :md="3"> <el-button type="primary" style="margin-top:3px"
                  @click="addSonFrom">添加</el-button></el-col>
            </el-row>
          </el-form>
          <el-table ref="table" :resizable="false" border :data="sonFormInline.propertiesList" height="250px"
            style="width: 50%">
            <el-table-column type="index" width="70" label="序号"> </el-table-column>
            <el-table-column prop="metadataName" label="参数名称" show-overflow-tooltip />
            <el-table-column prop="metadataTag" label="参数标识" show-overflow-tooltip />
            <el-table-column label="操作">
              <template slot-scope="scope">
                <el-button type="text" @click="configuration(scope.$index, scope.row.valueType)">配置</el-button>
                <el-button type="text" @click="delSonRow(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </ContentCard>
      <addIntervalFormState v-if="dialogVisible" ref="addIntervalFormState" :dialogVisible="dialogVisible"
        @closeDialog="closeDialog" :dataType="dataType" :dataPropertiesConfigList="dataPropertiesConfigList"
        @submitDialog="submitDialog">
      </addIntervalFormState>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">关闭</el-button>
      <el-button v-if="$route.query?.type != 'detail'" type="primary" @click="submitForm()">保存</el-button>
    </div>
  </PageContainer>
</template>
<script>
import addIntervalFormState from './addIntervalFormState.vue'
export default {
  name: 'addIntervalForm',
  components: {
    addIntervalFormState
  },
  data() {
    return {
      disabledData: false,
      systemType: [],
      deviceType: [],
      monitoringParameterList: [],
      tableData: [
        {
          stateName: null,
          colour: null
        }
      ],
      formInline: {
        id: "",
        systemTypeCode: "",
        deviceTypeCode: "",
        enable: 1,
        propertiesList: [],
      },
      currentRowIndex: null,
      sonFormInline: {
        monitoringParameter: "",
        propertiesList: []
      },
      monitorMetadataName: "",
      monitorMetadataTag: "",
      dataType: "",
      formLoading: false,
      rules: {
        systemTypeCode: {
          required: true,
          message: '请选择系统类型',
          trigger: 'change'
        },
        deviceTypeCode: {
          required: true,
          message: '请选择设备类型',
          trigger: 'change'
        }
      },
      rules2: {
        monitoringParameter: {
          required: true,
          message: '请选择监测参数',
          trigger: 'change'
        },
      },
      setType: null,
      dialogVisible: false,
      dataPropertiesConfigList: [],
    }
  },
  mounted() {
    this.initEvent()
    this.getQueryCategoryByCategoryId()
    if (this.$route.query.id) {
      this.getSurveyByOne()
      this.disabledData = true
    }
  },
  methods: {
    //获取产品类型
    getQueryCategoryByCategoryId() {
      let data = {
        dictionaryCategoryId: 'PRODUCT_CATEGORY',
        level: 1
      }
      this.$api.getQueryCategoryByCategoryId(data).then(res => {
        if (res.code === '200') {
          this.systemType = res.data
        }
      })
    },
    //产品类型选择
    systemTypeChange(val) {
      this.formInline.deviceTypeCode = ''
      this.sonFormInline.monitoringParameter = ''
      this.$api.getIntervalStateDeviceType({ dictionaryCode: val }).then(res => {
        if (res.code === '200') {
          this.deviceType = res.data[0].children
        }
      })
    },
    deviceTypeChange(val) {
      if (val) {
        this.getIntervalStateParameter()
      }
    },
    //获取监测参数
    getIntervalStateParameter() {
      let data = {
        deviceTypeCode: this.formInline.deviceTypeCode,
        systemTypeCode: this.formInline.systemTypeCode
      }
      this.$api.getIntervalStateParameter(data).then(res => {
        if (res.code === '200') {
          this.monitoringParameterList = res.data
        }
      })
    },
    // 监测参数选择
    monitoringParameterChange(val) {
      if (val) {
        this.monitorMetadataName = this.monitoringParameterList.find(item => {
          return item.metadataTag === val
        }).metadataName
        this.monitorMetadataTag = val
        this.dataType = this.monitoringParameterList.find(item => {
          return item.metadataTag === val
        }).valueType
      }
    },
    // 配置
    configuration(index, valueType) {
      this.dataType = valueType;
      this.currentRowIndex = index; // 保存当前行索引
      this.dialogVisible = true
      this.dataPropertiesConfigList = this.sonFormInline.propertiesList[this.currentRowIndex].propertiesConfigList
    },
    // 配置关闭
    closeDialog() {
      this.dialogVisible = false
    },
    // 配置保存
    submitDialog(data) {
      console.log(data, 'data');

      this.sonFormInline.propertiesList[this.currentRowIndex].propertiesConfigList = []
      data.forEach(item => {
        this.sonFormInline.propertiesList[this.currentRowIndex].propertiesConfigList.push({
          sectionColor: item.sectionColor,
          sectionName: item.sectionName,
          sectionValue: item.sectionValue,
          sectionText: item.sectionText,
        });
      });
      this.dialogVisible = false
    },
    // 删除
    delSonRow(i) {
      this.sonFormInline.propertiesList.splice(i, 1)
    },
    // 初始化
    sonFormReset() {
      this.sonFormInline = {
        monitoringParameter: "",
        propertiesList: []
      }
      this.$nextTick(() => {
        this.$refs.sonFormInline.resetFields()
      })
    },
    addSonFrom() {
      this.$refs.sonFormInline.validate((valid) => {
        if (valid) {
          let obj = {
            metadataName: this.monitorMetadataName,
            metadataTag: this.monitorMetadataTag,
            valueType: this.dataType,
            propertiesConfigList: []
          }
          try {
            // 检查是否已存在相同的 metadataTag
            const exists = this.sonFormInline.propertiesList.some(item => item.metadataTag === obj.metadataTag);
            if (exists) {
              // 提示用户
              this.$message.error('添加失败：该 参数标识 已存在！')
              return; // 退出函数，不进行添加
            }
            // 添加新对象到数组
            this.sonFormInline.propertiesList.push(obj)
          } catch { }
        }
      })
    },
    initEvent() {
      this.$nextTick(() => {
        this.$refs.formInline.resetFields()
      })
    },
    // 获取监测项详情
    getSurveyByOne() {
      this.formLoading = true
      this.$api
        .getIntervalStateQueryById({
          id: this.$route.query.id
        })
        .then((res) => {
          console.log(res.data, 'res.data');
          this.systemTypeChange(res.data.systemTypeCode)
          this.formInline = res.data
          this.deviceTypeChange(res.data.deviceTypeCode)
          this.sonFormInline.propertiesList = this.formInline.propertiesList
          this.formLoading = false
        })
    },
    submitForm() {
      this.$refs.formInline.validate((valid) => {
        if (valid) {
          this.formInline.propertiesList = this.sonFormInline.propertiesList
          if (!this.sonFormInline.propertiesList.length) return this.$message.error('至少添加一条参数配置')
          this.formLoading = true
          this.$api.getIntervalStateSave(this.formInline).then((res) => {
            this.formLoading = false
            if (res.code == 200) {
              this.$message.success(res.message)
              this.$router.go(-1)
            } else {
              this.$message.error(res.message)
            }
          })
        } else {
          return false
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.on_range {
  display: flex;
  margin-top: 10px;

  ::v-deep .el-select,
  .el-input {
    width: 100px !important;
  }
}

::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
  appearance: none !important;
}

::v-deep input[type='\2018number\2019'] {
  appearance: textfield !important;
}

.btn_row {
  border-radius: 4px;
  display: flex;
  align-items: center;

  .search-aside-item {
    display: inline-block;
    font-size: 14px;
    padding: 0 30px;
    height: 32px;
    line-height: 32px;
    font-family: PingFangSC-Regular;
    color: $color-primary;
    border: 1px solid $color-primary;
    background: #fff;
    margin-right: 20px;
    border-radius: 4px;
    cursor: pointer;

    &:hover,
    &:focus {
      color: #fff;
      font-family: PingFangSC-Regular;
      border-color: $color-primary;
      background-color: $color-primary;
      font-weight: 500;
    }
  }

  .search-aside-item-active {
    color: #fff;
    font-family: PingFangSC-Regular;
    border-color: $color-primary;
    background-color: $color-primary;
    font-weight: 500;
  }
}

.form-content {
  height: calc(100% - 0px);
  overflow-y: auto;
  background-color: #fff;

  .box-card {
    padding-left: 3%;
    margin-bottom: 3px;
  }

  .form-btn-title {
    font-size: 14px;
    font-family: 'PingFang SC-Regular', 'PingFang SC';
    font-weight: 400;
    color: #121f3e;
    margin-right: 8px;
  }

  .form-btn-btn {
    padding: 4px 10px;
    margin: 0 8px;
  }

  .assets-info {
    font-size: 14px;
    font-family: 'PingFang SC-Regular', 'PingFang SC';
    font-weight: 400;
    color: #121f3e;
    margin-right: 15px;

    >span {
      color: #3562db;
    }
  }

  .assets-info-close {
    cursor: pointer;

    &:hover {
      color: #3562db;
      font-weight: 600;
    }
  }

  .parameter-box {
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    margin-bottom: 20px;

    .parameter-title {
      font-size: 14px;
      font-family: 'PingFang SC-Regular', 'PingFang SC';
      font-weight: 400;
      color: #121f3e;
      background: #faf9fc;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 48px;
      line-height: 48px;
      padding: 0 10px;

      &>span {
        &:first-child {
          font-size: 16px;
        }
      }
    }

    .unit-style {
      font-size: 14px;
      font-family: 'PingFang SC-Regular', 'PingFang SC';
      font-weight: 400;
      color: #121f3e;
      height: 40px;
      line-height: 40px;
    }
  }

  ::v-deep .el-select,
  .el-input {
    width: fit-content;
  }

  .camera-tag {
    background: #f6f5fa;
    border-radius: 4px;
    font-size: 14px;
    font-family: 'PingFang SC-Regular', 'PingFang SC';
    font-weight: 400;
    color: #121f3e;
    border: none;
    margin-right: 8px;

    ::v-deep .el-tag__close {
      color: #121f3e;

      &:hover {
        color: #fff;
        background-color: #3562db;
      }
    }
  }
}
</style>