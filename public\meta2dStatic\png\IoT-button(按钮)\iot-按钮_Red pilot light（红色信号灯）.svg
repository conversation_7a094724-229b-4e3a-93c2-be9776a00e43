<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 150 150"><defs><style>.cls-1{fill:url(#未命名的渐变_74);}.cls-2{fill:#eee;}.cls-3{fill:#e1727e;}.cls-4{fill:#f01313;}.cls-5{fill:#f44758;}.cls-6{fill:#e80000;}.cls-7{fill:#e2e2e2;opacity:0.44;}.cls-8{opacity:0.3;fill:url(#未命名的渐变_49);}</style><linearGradient id="未命名的渐变_74" x1="0.11" y1="74.94" x2="150" y2="74.94" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#6b6b6b"/><stop offset="0.01" stop-color="#767676"/><stop offset="0.03" stop-color="#959595"/><stop offset="0.04" stop-color="#aaa"/><stop offset="0.37" stop-color="#ccc"/><stop offset="0.74" stop-color="#eaeaea"/><stop offset="0.94" stop-color="#f6f6f6"/><stop offset="0.95" stop-color="#ededed"/><stop offset="0.96" stop-color="#d4d4d4"/><stop offset="0.97" stop-color="#ababab"/><stop offset="0.99" stop-color="#737373"/><stop offset="0.99" stop-color="#666"/></linearGradient><linearGradient id="未命名的渐变_49" x1="81" y1="206.77" x2="81" y2="179.42" gradientTransform="matrix(1.1, 0, 0, -0.77, -12.43, 270.79)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#fff"/><stop offset="1" stop-color="#fff"/></linearGradient></defs><title>iot-按钮</title><g id="图层_20" data-name="图层 20"><rect class="cls-1" x="0.11" width="149.89" height="149.89" rx="5.65"/><circle class="cls-2" cx="75.06" cy="74.94" r="62.19"/><circle class="cls-3" cx="75.06" cy="74.94" r="57.36"/><circle class="cls-4" cx="75.06" cy="74.94" r="51.44"/><circle class="cls-5" cx="75.06" cy="74.94" r="26.22"/><circle class="cls-6" cx="75.06" cy="74.94" r="22"/><path class="cls-7" d="M132.42,74.94c0,31.68-25.68-10.78-57.36-10.78S17.69,106.62,17.69,74.94a57.37,57.37,0,0,1,114.73,0Z"/><ellipse class="cls-8" cx="76.46" cy="121.75" rx="33.58" ry="10.55"/></g></svg>