<template>
  <PageContainer>
    <div slot="content" class="role-content">
      <el-row :gutter="16" style="flex: 1; overflow: auto">
        <el-col :xl="15" :lg="24" :xs="24" style="height: 100%; padding-top: 16px">
          <div class="role-content-left">
            <div class="header">
              <div style="width: 240px; display: flex; align-items: center">
                <div v-for="(item, index) in statusList" :key="index" style="font-size: 14px; margin-right: 20px">
                  <span style="display: inline-block; width: 8px; height: 8px" :class="item.color"></span>
                  {{ item.label }}
                </div>
              </div>
              <div class="change_month" style="flex: 1">
                <i class="el-icon-arrow-left" @click="clickPrevious"></i>
                <span>{{ monthData }}</span>
                <i class="el-icon-arrow-right" @click="clickNext"></i>
              </div>
              <div class="today" @click="clickToday">
                <span> 今天 </span>
              </div>
            </div>
            <el-calendar ref="uploadImgBtn" v-model="calendarDate">
              <template slot="dateCell" slot-scope="{ data }">
                <div class="calendar_content" @click="clickDay(data)">
                  <div style="text-align: right; margin-bottom: 10px">
                    {{ data.day.split('-')[2] }}
                  </div>
                  <div v-for="(item, index) in tableTimeData" :key="index">
                    <!-- 设备 -->
                    <div v-if="systemType != '3' && data.day == item.dateStr && item.taskStatus != 3">
                      <div :class="item.taskStatus == '1' ? 'triangle noOk' : 'triangle isOK'"></div>
                      <div v-if="item.inspectionSum > 0" class="calNumber" @click="dataChange(data.day, '1')">
                        <span>设备巡检</span>
                        <span class="num">{{ item.inspectionFinishedSum }}/{{ item.inspectionSum }}</span>
                      </div>
                      <div v-if="item.maintainSum > 0" class="calNumber1" @click="dataChange(data.day, '2')">
                        <span>设备保养</span>
                        <span class="num">{{ item.maintainFinishedSum }}/{{ item.maintainSum }}</span>
                      </div>
                    </div>
                    <!-- 综合巡检 -->
                    <div v-if="systemType == '3' && data.day == item.dateStr && item.taskStatus != 3">
                      <div :class="item.taskStatus == '1' ? 'triangle noOk' : 'triangle isOK'"></div>
                      <el-popover v-if="tableTimeData[index].planTypeVo.length > 2" placement="right" width="180" trigger="hover" popper-class="popperClass">
                        <div
                          v-for="(val, cindex) in tableTimeData[index].planTypeVo"
                          :key="cindex"
                          @click="dataChange(data.day, '3', tableTimeData[index].planTypeVo[cindex].menuType)"
                        >
                          <div class="calNumber2" :style="{ background: cindex % 2 == 0 ? '#3562db' : '#ff9435' }">
                            <span>{{ val.planTypeName }}</span>
                            <span class="num2">{{ val.planTypeFinishedQuantity }}/{{ val.planTypeQuantity }}</span>
                          </div>
                        </div>
                        <div slot="reference">
                          <div
                            v-for="(value, indexVal) in tableTimeData[index].planTypeVo.slice(0, 2)"
                            :key="indexVal"
                            class="calNumber2"
                            :style="{ background: indexVal % 2 == 0 ? '#3562db' : '#ff9435' }"
                          >
                            <span>{{ value.planTypeName }}</span>
                            <span class="num2">{{ value.planTypeFinishedQuantity }}/{{ value.planTypeQuantity }}</span>
                            <span v-if="indexVal == 1">....</span>
                          </div>
                        </div>
                      </el-popover>
                      <div v-else>
                        <div
                          v-for="(val, cindex) in tableTimeData[index].planTypeVo"
                          :key="cindex"
                          class="calNumber2"
                          :style="{ background: cindex % 2 == 0 ? '#3562db' : '#ff9435' }"
                          @click="dataChange(data.day, '3', tableTimeData[index].planTypeVo[cindex].menuType)"
                        >
                          <span>{{ val.planTypeName }}</span>
                          <span class="num2">{{ val.planTypeFinishedQuantity }}/{{ val.planTypeQuantity }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </el-calendar>
          </div>
        </el-col>
        <el-col :xl="9" :lg="24" :xs="24" style="height: 100%; padding-top: 16px">
          <div class="role-content-right">
            <el-tabs v-if="systemType != '3'" v-model="activeName">
              <el-tab-pane label="设备巡检" name="inspection" />
              <el-tab-pane label="设备保养" name="maintain" />
            </el-tabs>
            <div class="dataChange">
              <div>
                <span v-for="(item, index) in btnList" :key="item.value" :class="btnTimeActive == index ? 'timeBtn active' : 'timeBtn'" @click="onTimeBtn(item)">{{
                  item.label
                }}</span>
              </div>
            </div>
            <!-- 任务率 -->
            <div class="tasksNumber">
              <div class="item">
                <span style="margin-bottom: 5px">任务总数</span>
                <span style="color: #121f3e">{{ totalTasks }}</span>
              </div>
              <div class="item">
                <span style="margin-bottom: 5px">已完成</span>
                <span style="color: #08cb83">{{ completedQuantity }}</span>
              </div>
              <div class="item">
                <span style="margin-bottom: 5px">完成率</span>
                <span style="color: #ff9435">{{ completionRate }}</span>
              </div>
            </div>
            <!-- 任务状态 -->
            <div class="tabChange">
              <span v-for="(item, index) in tabList" :key="item.value" :class="btnTabType == index ? 'timeBtn active' : 'timeBtn'" @click="onTypeBtn(item)">{{ item.label }}</span>
            </div>
            <!-- 任务表格 -->
            <div v-if="tabType === 'task'" class="table-content">
              <el-table
                v-loading="tableLoading"
                border
                title="双击查看详情"
                :data="tableData"
                :height="tableHeight"
                style="width: 100%; margin-top: 20px"
                @sort-change="handleSortChange"
                @row-dblclick="dblclick"
              >
                <el-table-column prop="taskName" label="任务名称" min-width="160" show-overflow-tooltip sortable="custom"></el-table-column>
                <el-table-column v-if="systemType != '3'" prop="systemCode" label="任务类型" min-width="120" show-overflow-tooltip sortable="custom">
                  <div slot-scope="scope">
                    <span>{{ scope.row.systemCode == '1' ? '设备巡检' : '设备保养' }}</span>
                  </div>
                </el-table-column>
                <el-table-column prop="taskStartTime" label="时间" min-width="160" sortable="custom" show-overflow-tooltip></el-table-column>
                <el-table-column prop="distributionTeamName" label="部门" min-width="160" sortable="custom" show-overflow-tooltip></el-table-column>
                <el-table-column prop="taskStatus" label="状态" sortable="custom" min-width="100" show-overflow-tooltip>
                  <div slot-scope="scope" class="taskStatus" :style="{ color: scope.row.taskStatus == 1 ? '#FA403C' : scope.row.taskStatus == 2 ? '#08cb83' : '#FF9435' }">
                    <span class="taskStatusIcon" :style="{ background: scope.row.taskStatus == 1 ? '#FA403C' : scope.row.taskStatus == 2 ? '#08cb83' : '#FF9435' }"></span>
                    {{ scope.row.taskStatus == 1 ? '未完成' : scope.row.taskStatus == 2 ? '已完成' : '进行中' }}
                  </div>
                </el-table-column>
              </el-table>
            </div>
            <div v-if="tabType === 'team'" class="table-content">
              <el-table v-loading="tableLoading" border :data="tableData" :height="tableHeight" style="width: 100%; margin-top: 20px">
                <el-table-column prop="distributionTeamName" label="部门名称" min-width="160" show-overflow-tooltip></el-table-column>
                <el-table-column prop="taskCount" label="任务总数" min-width="160" show-overflow-tooltip></el-table-column>
                <el-table-column prop="accomplishCount" label="已完成" min-width="160" show-overflow-tooltip></el-table-column>
                <el-table-column prop="unfinishedCount" label="未完成" min-width="160" show-overflow-tooltip></el-table-column>
                <el-table-column prop="finishingRate" label="完成率" min-width="160" show-overflow-tooltip></el-table-column>
              </el-table>
            </div>
            <div class="contentTable-footer">
              <el-pagination
                :current-page="pagination.current"
                :page-sizes="pagination.pageSizeOptions"
                :page-size="pagination.size"
                :layout="pagination.layoutOptions"
                :pager-count="countNum"
                :total="pagination.total"
                @size-change="paginationSizeChange"
                @current-change="paginationCurrentChange"
              >
              </el-pagination>
            </div>
          </div>
        </el-col>
      </el-row>
      <!-- 自定义日期选择 -->
      <el-dialog
        v-dialogDrag
        :visible.sync="dialogVisible"
        :before-close="dialogClosed"
        :modal="false"
        :close-on-click-modal="false"
        title="自定义日期选择"
        width="35%"
        custom-class="model-dialog"
      >
        <div class="content" style="padding: 10px">
          <el-form ref="formInline" :model="formInline" :rules="rules" label-position="right" label-width="110px">
            <el-form-item label="开始日期：" prop="startTime">
              <el-date-picker v-model="formInline.startTime" value-format="yyyy-MM-dd" type="date" placeholder="选择日期"> </el-date-picker>
            </el-form-item>
            <el-form-item label="结束日期：" prop="endTime">
              <el-date-picker v-model="formInline.endTime" value-format="yyyy-MM-dd" type="date" placeholder="选择日期"> </el-date-picker>
            </el-form-item>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="dialogClosed">取 消</el-button>
          <el-button type="primary" @click="submit">确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </PageContainer>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin.js'
import fecha from 'element-ui/src/utils/date' // element-ui中处理时间的工具类
import moment from 'moment'
export default {
  name: 'calendar',
  mixins: [tableListMixin],
  data() {
    return {
      moment,
      countNum: 5,
      monthData: '',
      systemType: '',
      dialogVisible: false,
      totalTasks: '', // 任务总数
      completedQuantity: '', // 已完成数量
      completionRate: '', // 完成率
      statusList: [
        {
          label: '已完成',
          color: 'bgc1'
        },
        {
          label: '未完成',
          color: 'bgc2'
        },
        {
          label: '当前日期',
          color: 'bgc3'
        }
      ], // 完成状态
      todayStatusList: [
        {
          label: '未完成',
          value: '1',
          color: '#FA403C'
        },
        {
          label: '已完成',
          value: '2',
          color: '#08CB83'
        },
        {
          label: '进行中',
          value: '3',
          color: '#FF9435'
        }
      ],
      calendarDate: new Date(),
      activeName: 'inspection',
      activeStatus: 'activeStatus',
      tabPosition: '今日',
      btnList: [
        {
          value: '0',
          label: '今日',
          type: 'day'
        },
        {
          value: '1',
          label: '本周',
          type: 'week'
        },
        {
          value: '2',
          label: '本月',
          type: 'month'
        },
        {
          value: '3',
          label: '自定义',
          type: 'custom'
        }
      ],
      tabList: [
        {
          value: '0',
          label: '任务',
          type: 'task'
        },
        {
          value: '1',
          label: '班组',
          type: 'team'
        }
      ],
      tableData: [], // 今日任务状态
      tableTimeData: [], // 日历数据
      tableLoading: false,
      formInline: {
        startTime: '',
        endTime: ''
      },
      startTime: '',
      endTime: '',
      timeType: 'day',
      tabType: 'task',
      btnTimeActive: '0',
      btnTabType: '0',
      rules: {
        startTime: [{ required: true, message: '请选择开始日期', trigger: 'change' }],
        endTime: [{ required: true, message: '请选择结束日期', trigger: 'change' }]
      },
      orderItems: {},
      isSort: '' // 是否排序
    }
  },
  watch: {
    activeName() {
      this.init()
      this.changeStatistics()
    },
    calendarDate(val) {}
  },
  created() {
    this.startTime = moment().format('YYYY-MM-DD')
    this.endTime = moment().format('YYYY-MM-DD')
    this.init()
    this.changeMonth(this.calendarDate)
    this.changeStatistics()
  },
  mounted() {
    if (this.systemType == '3') {
      document.getElementsByClassName('table-content')[0].style.height = 'calc(100% - 280px)'
    } else {
      document.getElementsByClassName('table-content')[0].style.height = 'calc(100% - 320px)'
    }
    this.$nextTick(() => {
      // 点击前一个月
      let prevBtn = document.querySelector('.el-calendar__button-group .el-button-group>button:nth-child(1)')
      prevBtn.addEventListener('click', () => {
        this.changeMonth(this.calendarDate)
      })
      // 点击下一个月
      let nextBtn = document.querySelector('.el-calendar__button-group .el-button-group>button:nth-child(3)')
      nextBtn.addEventListener('click', () => {
        this.changeMonth(this.calendarDate)
      })
      // 点击今天
      let todayBtn = document.querySelector('.el-calendar__button-group .el-button-group>button:nth-child(2)')
      todayBtn.addEventListener('click', () => {
        this.changeMonth(this.calendarDate)
      })
    })
  },
  methods: {
    // 获取日历板上第一天
    getMondayDay(Month) {
      let now = new Date(Month)
      let day = now.getDay() || 7
      return fecha.format(new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1 - day), 'yyyy-MM-dd')
    },
    // 获取日历板上最后一天
    getLastSundayDay(Month) {
      let monday = new Date(this.getMondayDay(Month))
      let sunday = monday.setDate(monday.getDate() + 41)
      return fecha.format(new Date(sunday), 'yyyy-MM-dd')
    },
    // 改变月份
    changeMonth(date) {
      let month = (date.getMonth() + 1).toString().padStart(2, '0')
      let year = date.getFullYear()
      let oneday = year + '-' + month // 获取某月
      this.monthData = year + '年' + month + '月'
      let startTime = this.getMondayDay(oneday)
      let endTime = this.getLastSundayDay(oneday)
      this.tableTimeData = []
      let data = {
        startDate: startTime,
        endDate: endTime
      }
      if (this.$route.meta.type) {
        this.$api.insCalendarList(data).then((res) => {
          if (res.code == 200) {
            this.tableTimeData = res.data
          }
        })
      } else {
        this.$api.selectCalendarList(data).then((res) => {
          if (res.code == 200) {
            this.tableTimeData = res.data
          }
        })
      }
    },
    init() {
      if (this.$route.meta.type) {
        this.systemType = this.$route.meta.type
      } else if (this.activeName == 'inspection') {
        this.systemType = '1'
      } else {
        this.systemType = '2'
      }
    },
    // 任务状态
    getTodayStatus(val) {
      let newArr = []
      newArr = this.todayStatusList.filter((item) => {
        return val == item.value
      })
      return newArr[0]
    },
    // 查询工作日历列表
    // 统计任务数量
    getTaskNum() {
      let type = ''
      if (this.timeType === 'day') {
        type = '1'
      } else if (this.timeType === 'week') {
        type = '2'
      } else if (this.timeType === 'month') {
        type = '3'
      } else if (this.timeType == 'custom') {
        type = '4'
      }
      let data = {
        statisticsDifferentiation: this.systemType,
        statisticalDimension: type,
        startDate: this.formInline.startTime,
        endDate: this.formInline.endTime
      }
      this.$api.getTaskStatistics(data).then((res) => {
        if (res.code == 200) {
          this.totalTasks = res.data.totalTasks
          this.completedQuantity = res.data.completedQuantity
          this.completionRate = res.data.completionRate
        }
      })
    },
    // 任务table
    getDataList() {
      this.tableLoading = true
      let data = {
        systemIdentificationClassification: this.$route.meta.type ? '2' : '1',
        statisticsDifferentiation: this.systemType,
        pageNo: this.pagination.current,
        pageSize: this.pagination.size,
        startDate: `${this.startTime}`,
        endDate: `${this.endTime}`
      }
      if (this.timeType == 'custom') {
        data.startDate = `${this.formInline.startTime}`
        data.endDate = `${this.formInline.endTime}`
      }
      if ((this.isSort = 'sort')) {
        data.column = this.orderItems.column
        data.asc = this.orderItems.asc
      }
      this.$api.getTaskDynamics(data).then((res) => {
        this.tableLoading = true
        if (res.code == 200) {
          this.tableData = res.data.list
          this.pagination.total = parseInt(res.data.sum)
        }
        this.tableLoading = false
      })
    },
    // 按部门统计列表
    getTableByDept() {
      this.tableLoading = true
      const params = {
        systemCode: this.systemType,
        pageNo: this.pagination.current,
        pageSize: this.pagination.size,
        taskStartTime: `${this.startTime}`,
        taskEndTime: `${this.endTime}`
      }
      if (this.timeType == 'custom') {
        params.taskStartTime = `${this.formInline.startTime}`
        params.taskEndTime = `${this.formInline.endTime}`
      }
      this.$api.getChratsByDepartment(params).then((res) => {
        if (res.code == '200') {
          this.tableData = res.data.list
          this.pagination.total = parseInt(res.data.sum)
        } else {
          this.$message.error(res.message || '请求失败')
        }
        this.tableLoading = false
      })
    },
    clickPrevious() {
      this.$refs.uploadImgBtn.$children[0].$children[0].$el.click()
    },
    clickNext() {
      this.$refs.uploadImgBtn.$children[0].$children[2].$el.click()
    },
    clickToday() {
      this.$refs.uploadImgBtn.$children[0].$children[1].$el.click()
    },
    onTimeBtn(val) {
      this.pagination.size = 15
      this.pagination.current = 1
      this.pagination.total = 0
      this.totalTasks = '0'
      this.completedQuantity = '0'
      this.completionRate = '0.00%'
      this.btnTimeActive = val.value
      this.timeType = val.type
      if (this.timeType === 'day') {
        this.startTime = moment().format('YYYY-MM-DD')
        this.endTime = moment().format('YYYY-MM-DD')
        this.changeStatistics()
      } else if (this.timeType === 'week') {
        this.startTime = moment().weekday(1).format('YYYY-MM-DD')
        this.endTime = moment().weekday(7).format('YYYY-MM-DD')
        this.changeStatistics()
      } else if (this.timeType === 'month') {
        this.startTime = moment().startOf('month').format('YYYY-MM-DD')
        this.endTime = moment().endOf('month').format('YYYY-MM-DD')
        this.changeStatistics()
      } else if (this.timeType === 'custom') {
        this.dialogVisible = true
      }
    },
    // 切换方法公用
    changeStatistics() {
      this.getTaskNum()
      if (this.tabType === 'task') {
        this.getDataList()
      } else if (this.tabType === 'team') {
        this.getTableByDept()
      }
    },
    onTypeBtn(val) {
      this.pagination.size = 15
      this.pagination.current = 1
      this.pagination.total = 0
      this.btnTabType = val.value
      this.tabType = val.type
      this.changeStatistics()
    },
    // 关闭弹窗
    dialogClosed() {
      this.$refs.formInline.resetFields()
      this.dialogVisible = false
    },
    // 自定义日期确定
    submit() {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          this.changeStatistics()
          this.dialogClosed()
        }
      })
    },
    dataChange(data, type, menuType) {
      this.$router.push({
        name: type != '3' ? 'calendarProgressDetail' : 'calProgressDetail',
        query: {
          type: type,
          menuType: menuType || '',
          taskStartTime: data,
          taskEndTime: data
        }
      })
    },
    // 双击表格
    dblclick(val) {
      this.$router.push({
        name: this.$route.meta.type ? 'calTaskDetail' : 'calendarTaskDetail',
        query: {
          type: 'detail',
          taskId: val.id,
          row: val
        }
      })
    },
    clickDay(data) {
      this.monthData = moment(data.day).format('YYYY年MM月')
      this.changeMonth(new Date(data.day))
    },
    // table排序
    handleSortChange({ order, prop }) {
      this.orderItems = {}
      if (order == 'ascending') {
        this.orderItems.asc = true
        this.orderItems.column = prop
        this.isSort = 'sort'
      } else if (order == 'descending') {
        this.orderItems.asc = false
        this.orderItems.column = prop
        this.isSort = 'sort'
      } else {
        this.isSort = 'noSort'
        this.orderItems = {}
      }
      this.getDataList()
    }
  }
}
</script>
<style>
.el-popover.popperClass {
  background-color: #ededf5;
}
</style>
<style lang="scss" scoped>
.page-container {
  margin: 0 15px 15px !important;
}
.table-content {
  background: #fff;
  border-radius: 4px;
  // height: calc(100% - 292px);
  height: calc(100% - 260px);
  // padding: 10px;
  // overflow-y: auto;
}
.contentTable-footer {
  padding: 5px 0 0;
  ::v-deep .el-select {
    width: 100px !important;
  }
}
.role-content {
  height: 100%;
  display: flex;
  .role-content-left {
    border-radius: 6px;
    height: 100%;
    background-color: #fff;
    overflow: auto;
    .header {
      position: relative;
      display: flex;
      align-items: center;
      padding: 12px 20px 10px;
      .change_month {
        text-align: center;
        height: 40px;
        line-height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        i {
          display: inline-block;
          width: 24px;
          height: 24px;
          line-height: 24px;
          margin: auto 0;
          font-size: 14px;
          text-align: center;
          cursor: pointer;
          background: #f1f6ff;
          border-radius: 12px;
          &:hover {
            color: #fff;
            background-color: #3562db;
          }
        }
        span {
          margin: auto 24px;
          font-size: 20px;
          color: #4e8cda;
        }
      }
      .today {
        width: 160px;
        text-align: right;
        font-size: 14px;
        cursor: pointer;
        span {
          display: inline-block;
          width: 48px;
          height: 30px;
          border-radius: 4px;
          background-color: rgb(53 98 219 / 20%);
          text-align: center;
          line-height: 30px;
          color: #3562db;
        }
      }
    }
    .calendar_content {
      width: 100%;
      height: 100%;
      padding: 8px;
      position: relative;
      .triangle {
        position: absolute;
        top: 0;
        left: 0;
        width: 0;
        height: 0;
        border-right: 24px solid transparent;
      }
      .noOk {
        border-top: 24px solid #fa403c;
      }
      .isOK {
        border-top: 24px solid #08cb83;
      }
      .calNumber,
      .calNumber1 {
        width: 100%;
        height: 30px;
        margin: 0 auto;
        font-size: 14px;
        background-color: #3562db;
        border-radius: 4px;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        .num {
          margin-left: 5px;
          width: 30px;
          height: 28px;
          line-height: 28px;
          text-align: center;
          // border-radius: 28px;
          // background-color: rgb(255 255 255 / 20%);
        }
      }
      .calNumber1 {
        margin-top: 5px;
        background-color: #ff9435;
      }
    }
  }
  .role-content-right {
    border-radius: 6px;
    height: 100%;
    background-color: #fff;
    padding: 12px 16px;
    .dataChange {
      margin-top: 20px;
      display: flex;
      justify-content: center;
      .timeBtn {
        width: 44px;
        height: 20px;
        padding: 5px 10px;
        margin-right: 8px;
        background-color: #ededf5;
        border: 1px solid #dcdfe6;
        border-radius: 2px;
        font-size: 14px;
        cursor: pointer;
      }
      .active {
        background-color: #3562db;
        color: #fff;
      }
      ::v-deep .el-radio-button__orig-radio:checked + .el-radio-button__inner {
        color: #fff;
        background-color: #3562db;
        border-color: #3562db;
        box-shadow: -1px 0 0 0 #3562db;
      }
      ::v-deep .el-radio-button__inner:hover {
        color: #3562db;
      }
      ::v-deep .el-radio-button__inner {
        border: 1px solid #dcdfe6;
        background-color: #ededf5;
        border-radius: 2px;
        margin-right: 8px;
        padding: 5px 8px;
      }
    }
    .tabChange {
      justify-content: left !important;
      .timeBtn {
        display: inline-block;
        width: 60px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        margin-right: 0;
        background-color: #ededf5;
        // border: 1px solid #dcdfe6;
        border-radius: 2px;
        font-size: 14px;
        cursor: pointer;
      }
      .active {
        background-color: #3562db;
        color: #fff;
      }
    }
    .tasksNumber {
      display: flex;
      justify-content: space-between;
      margin: 20px 30px;
      .item {
        padding: 20px 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        span {
          font-size: 20px;
          font-weight: 700;
        }
      }
    }
  }
}
::v-deep .el-calendar-table thead th::before {
  content: '周';
}
::v-deep .el-calendar-table thead th {
  font-weight: 500;
  font-size: 14px;
}
::v-deep .el-calendar__body {
  width: 100%;
  padding: 12px 20px 20px;
  height: calc(100% - 60px);
}
// ::v-deep .current{
//   width: 134px;
// }
::v-deep .el-calendar-table .el-calendar-day {
  height: 114px;
  // z-index: inherit;
  // width: 80px;
  padding: 0;
}
.bgc1 {
  background-color: #08cb83;
}
.bgc2 {
  background-color: #fa403c;
}
.bgc3 {
  background-color: #ccced3;
}
::v-deep .el-calendar__header {
  padding: 0;
}
::v-deep .el-calendar__header .el-calendar__title {
  display: none;
}
::v-deep .el-calendar__button-group {
  display: none;
}
.content {
  width: 100%;
  min-height: 200px !important;
  overflow: auto;
  background-color: #fff !important;
}
.taskStatus {
  position: relative;
  display: inline-block;
  padding-left: 12px;
  .taskStatusIcon {
    display: inline-block;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 8px;
    height: 8px;
    border-radius: 100%;
  }
}
::v-deep .el-calendar-table:not(.is-range) td.prev,
::v-deep .el-calendar-table:not(.is-range) td.next {
  color: #c0c4cc;
  opacity: 0.7;
}
.calNumber2 {
  width: 100%;
  height: 30px;
  margin: 5px auto;
  font-size: 14px;
  background-color: #3562db;
  border-radius: 4px;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  .num2 {
    margin-left: 5px;
    width: 30px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    // border-radius: 28px;
    // background-color: rgb(255 255 255 / 20%);
  }
}
::v-deep .el-pagination .el-pagination__sizes {
  float: none;
}
::v-deep .el-pagination__jump {
  margin-left: 0;
}
::v-deep .el-select el-select--mini {
  width: 100px;
}
</style>
