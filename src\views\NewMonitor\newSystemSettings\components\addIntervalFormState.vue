<template>
  <el-dialog title="配置" :visible.sync="dialogVisible" :before-close="closeDialog" width="800px">
    <div class="sino-content">
      <ContentCard title="区间配置">
        <div slot="content">
          <el-form ref="filters" :rules="rules" label-position="right" label-width="100px" :model="filters">
            <el-row :gutter="24" style="margin: 0 0 20px">
              <el-col :md="10">
                <el-form-item label="区间状态名称" prop="sectionName" label-width="110px">
                  <el-input v-model="filters.sectionName" maxlength="16" placeholder="请输入区间状态名称"></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="12">
                <el-form-item label="区间状态颜色" prop="sectionColor" label-width="110px">
                  <el-input type="text" v-model="filters.sectionColor"
                    style="width: 120px;vertical-align: top;margin-right: 5px;"></el-input>
                  <el-color-picker v-model="filters.sectionColor" :predefine="predefineColors"> </el-color-picker>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24" style="margin: 0 0 20px">
              <el-col>
                <el-form-item label="区间类型" label-width="110px">
                  <div v-if="localDataType.type == 'enum' || localDataType.type == 'boolean'">
                    <div class="on_range" style="margin-top: 0px;">
                      <div>
                        <el-select v-model="rangeArr[2].symbol" placeholder="请选择">
                          <el-option label="=" value="="> </el-option>
                          <el-option label="!=" value="!="> </el-option>
                        </el-select>
                        <el-select v-model="rangeArr[2].value" placeholder="请选择" style="margin-left: 16px"
                          v-if="localDataType.type == 'boolean'" @change="booleanChange">
                          <el-option :label="localDataType.falseText" :value="localDataType.falseValue">
                          </el-option>
                          <el-option :label="localDataType.trueText" :value="localDataType.trueValue">
                          </el-option>
                        </el-select>
                        <el-select v-model="rangeArr[2].value" placeholder="请选择" style="margin-left: 16px"
                          v-if="localDataType.type == 'enum'" @change="enumChange">
                          <el-option v-for="(item, index) in localDataType.elements" :key="index" :label="item.text"
                            :value="item.value">
                          </el-option>
                        </el-select>
                      </div>
                    </div>
                  </div>
                  <div v-else>
                    <div class="btn_row">
                      <div v-for="(item, index) in intervalTypeArr" :key="index" :class="{
                        'search-aside-item': true,
                        'search-aside-item-active': filters.section === item.status
                      }" @click="filters.section = item.status">
                        {{ item.name }}
                      </div>
                    </div>
                    <div class="on_range">
                      <div>
                        <el-select v-model="rangeArr[0].symbol" placeholder="请选择">
                          <el-option label=">" value=">"> </el-option>
                          <el-option label=">=" value=">="> </el-option>
                          <el-option label="=" value="=" v-if="filters.section === 1"> </el-option>
                          <el-option label="<=" value="<=" v-if="filters.section === 1"> </el-option>
                          <el-option label="<" value="<" v-if="filters.section === 1"> </el-option>
                        </el-select>
                        <el-input v-model="rangeArr[0].value" style="margin-left: 16px" type="number"></el-input>
                      </div>
                    </div>
                    <div class="on_range">
                      <div v-if="filters.section !== 1">
                        <el-select v-model="rangeArr[1].symbol" placeholder="请选择">
                          <el-option label=">" value=">" v-if="filters.section === 1"> </el-option>
                          <el-option label=">=" value=">=" v-if="filters.section === 1"> </el-option>
                          <el-option label="=" value="=" v-if="filters.section === 1"> </el-option>
                          <el-option label="<=" value="<="> </el-option>
                          <el-option label="<" value="<"> </el-option>
                        </el-select>
                        <el-input v-model="rangeArr[1].value" style="margin-left: 16px" type="number"></el-input>
                      </div>
                    </div>
                  </div>
                  <el-button type="primary" style="margin-left: 150px" @click="addSonFrom">添加</el-button>
                </el-form-item>
              </el-col>
            </el-row>

          </el-form>
          <el-table ref="table" :resizable="false" border :data="propertiesConfigList" height="250px"
            style="width: 100%">
            <el-table-column prop="sectionName" label="区间状态名称" show-overflow-tooltip />
            <el-table-column prop="sectionText" label="区间值" show-overflow-tooltip />
            <el-table-column prop="sectionColor" label="状态颜色" show-overflow-tooltip>
              <template slot-scope="scope">
                <span :style="{ color: scope.row.sectionColor }">{{ scope.row.sectionColor }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template slot-scope="scope">
                <el-button type="text" @click="delSonRow(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </ContentCard>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog(false)">取 消</el-button>
      <el-button type="primary" @click="submitForm">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { monitorTypeList } from '@/util/dict.js'
import { stringify } from 'qs';
export default {
  name: 'addTreeNode',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    dataType: {
      type: String,
      default: ''
    },
    dataPropertiesConfigList: {
      type: Array,
      default: () => []
    },
  },
  data() {
    return {
      tableData: [
        {
          sectionName: null,
          sectionColor: null
        }
      ],
      rangeArr: [
        { symbol: '>', value: null },
        { symbol: '<', value: null },
        { symbol: '=', value: null }
      ],
      symbol: ['>', '>=', '=', '<=', '<'],
      intervalTypeArr: [
        { name: '区间', status: 0 },
        { name: '半开区间', status: 1 }
      ],
      // 预定颜色
      predefineColors: [],
      filters: {
        sectionName: '',
        sectionColor: '',
        section: 0,
      },
      propertiesConfigList: [],
      formLoading: false,
      rules: {
        sectionName: {
          required: true,
          message: '请输入区间状态名称',
          trigger: 'change'
        },
        sectionColor: {
          required: true,
          message: '请选择区间状态颜色',
          trigger: 'change'
        }
      },
      setType: null,
      localDataType: {},
      booleanSelectedValue: null,
      enumSelectedValue: null,
    }
  },
  activated() {
    this.initEvent()
  },
  mounted() {
    this.initEvent()
  },
  methods: {
    delSonRow(i) {
      this.propertiesConfigList.splice(i, 1)
    },
    sonFormReset() {
      this.filters = {
        sectionName: '',
        sectionColor: '',
        section: 0
      }
      this.rangeArr = [
        { symbol: '>', value: null },
        { symbol: '>', value: null },
        { symbol: '=', value: null }
      ]
      this.$nextTick(() => {
        if (this.localDataType.type == 'enum' || this.localDataType.type == 'boolean') {
          this.filters.section = 2
        }
        if (this.localDataType.type == 'enum') {
          this.rangeArr[2].value = this.localDataType.elements[0].text
          this.enumSelectedValue = this.localDataType.elements[0].value

        }
        if (this.localDataType.type == 'boolean') {
          this.rangeArr[2].value = this.localDataType.falseText
          this.booleanSelectedValue = this.localDataType.falseValue
        }
        this.$refs.filters.resetFields()
      })
    },
    booleanChange(value) {
      this.rangeArr[2].value = value === this.localDataType.falseValue
        ? this.localDataType.falseText
        : this.localDataType.trueText;
      this.booleanSelectedValue = value === this.localDataType.falseValue
        ? this.localDataType.falseValue
        : this.localDataType.trueValue
    },
    enumChange(value) {
      this.rangeArr[2].value = this.localDataType.elements.find(item => {
        return item.value === value
      }).text
      this.enumSelectedValue = this.localDataType.elements.find(item => {
        return item.value === value
      }).value
    },
    addSonFrom() {
      this.$refs.filters.validate((valid) => {
        if (valid) {
          let obj = {
            maxCompare: null,
            maxValue: null,
            minCompare: null,
            minValue: null,
            sectionValue: null,
            sectionText: null,
            ...this.filters
          }
          if (this.filters.section == 0) {
            if (!this.rangeArr[0].value || !this.rangeArr[1].value) return this.$message('区间范围值为必填')
            let arr = [...this.rangeArr].sort((a, b) => b.value - a.value)

            obj.maxCompare = arr[0].symbol
            obj.maxValue = arr[0].value
            obj.minCompare = arr[1].symbol
            obj.minValue = arr[1].value
            obj.sectionText = `${arr[0].symbol}${arr[0].value},${arr[1].symbol}${arr[1].value}`
            if (arr[0].symbol === '=') {
              obj.sectionValue = `==${arr[0].value},${arr[1].symbol}${arr[1].value}`
            } else if (arr[1].symbol === '=') {
              obj.sectionValue = `${arr[0].symbol}${arr[0].value},==${arr[1].value}`
            } else {
              obj.sectionValue = `${arr[0].symbol}${arr[0].value},${arr[1].symbol}${arr[1].value}`
            }

          } else if (this.filters.section == 1) {
            if (!this.rangeArr[0].value) return this.$message.error('区间范围值为必填')
            obj.minCompare = this.rangeArr[0].symbol
            obj.minValue = this.rangeArr[0].value
            obj.sectionText = `${this.rangeArr[0].symbol}${this.rangeArr[0].value}`
            if (this.rangeArr[0].symbol === '=') {
              obj.sectionValue = `==${this.rangeArr[0].value}`
            } else {
              obj.sectionValue = `${this.rangeArr[0].symbol}${this.rangeArr[0].value}`
            }
          } else {
            if (!this.rangeArr[2].value) return this.$message.error('区间类型为必选')
            obj.minCompare = this.rangeArr[2].symbol
            obj.minValue = this.rangeArr[2].value
            if (this.booleanSelectedValue) {
              obj.sectionText = `${this.rangeArr[2].symbol}${this.rangeArr[2].value}`
              if (this.rangeArr[2].symbol === '=') {
                obj.sectionValue = `==${this.booleanSelectedValue}`
              } else {
                obj.sectionValue = `${this.rangeArr[2].symbol}${this.booleanSelectedValue}`
              }
            }
            if (this.enumSelectedValue) {
              obj.sectionText = `${this.rangeArr[2].symbol}${this.rangeArr[2].value}`
              if (this.rangeArr[2].symbol === '=') {
                obj.sectionValue = `==${this.enumSelectedValue}`
              } else {
                obj.sectionValue = `${this.rangeArr[2].symbol}${this.enumSelectedValue}`
              }

            }
          }
          try {
            this.propertiesConfigList.push(obj)
            this.sonFormReset()
          } catch { }
        }
      })
    },
    initEvent() {
      this.$nextTick(() => {
        this.localDataType = JSON.parse(this.dataType)
        if (this.localDataType.type == 'enum' || this.localDataType.type == 'boolean') {
          this.filters.section = 2
        }
        if (this.localDataType.type == 'enum') {
          this.rangeArr[2].value = this.localDataType.elements[0].text
          this.enumSelectedValue = this.localDataType.elements[0].value

        }
        if (this.localDataType.type == 'boolean') {
          this.rangeArr[2].value = this.localDataType.falseText
          this.booleanSelectedValue = this.localDataType.falseValue
        }
        this.setType = this.$route.query.type
        this.propertiesConfigList = this.dataPropertiesConfigList
      })
    },
    submitForm() {
      if (!this.propertiesConfigList.length) return this.$message.error('至少添加一条区间配置')
      this.formLoading = true
      this.$emit('submitDialog', this.propertiesConfigList)
    },
    /**
     * 关闭弹窗
     */
    closeDialog(refresh = true) {
      this.$emit('closeDialog', refresh)
      this.filters = {
        sectionName: '',
        sectionColor: '',
        section: 0,
      }
      this.propertiesConfigList = []
      this.$forceUpdate()
    }
  }
}
</script>
<style lang="scss" scoped>
.on_range {
  display: flex;
  margin-top: 10px;

  ::v-deep .el-select,
  .el-input {
    width: 100px !important;
  }
}

::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
  appearance: none !important;
}

::v-deep input[type='\2018number\2019'] {
  appearance: textfield !important;
}

.btn_row {
  border-radius: 4px;
  display: flex;
  align-items: center;

  .search-aside-item {
    display: inline-block;
    font-size: 14px;
    padding: 0 30px;
    height: 32px;
    line-height: 32px;
    font-family: PingFangSC-Regular;
    color: $color-primary;
    border: 1px solid $color-primary;
    background: #fff;
    margin-right: 20px;
    border-radius: 4px;
    cursor: pointer;

    &:hover,
    &:focus {
      color: #fff;
      font-family: PingFangSC-Regular;
      border-color: $color-primary;
      background-color: $color-primary;
      font-weight: 500;
    }
  }

  .search-aside-item-active {
    color: #fff;
    font-family: PingFangSC-Regular;
    border-color: $color-primary;
    background-color: $color-primary;
    font-weight: 500;
  }
}

.form-content {
  height: calc(100% - 0px);
  overflow-y: auto;

  .form-title {
    height: 40px;
    line-height: 40px;
    background: #fff;
    border-bottom: 1px solid #dcdfe6;
    font-size: 15px;
    font-family: 'PingFang SC-Medium', 'PingFang SC';
    font-weight: 500;
    color: #121f3e;
    padding-left: 15px;
  }

  .box-card {
    padding-left: 3%;
    margin-bottom: 3px;
  }

  .form-btn-title {
    font-size: 14px;
    font-family: 'PingFang SC-Regular', 'PingFang SC';
    font-weight: 400;
    color: #121f3e;
    margin-right: 8px;
  }

  .form-btn-btn {
    padding: 4px 10px;
    margin: 0 8px;
  }

  .assets-info {
    font-size: 14px;
    font-family: 'PingFang SC-Regular', 'PingFang SC';
    font-weight: 400;
    color: #121f3e;
    margin-right: 15px;

    >span {
      color: #3562db;
    }
  }

  .assets-info-close {
    cursor: pointer;

    &:hover {
      color: #3562db;
      font-weight: 600;
    }
  }

  .parameter-box {
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    margin-bottom: 20px;

    .parameter-title {
      font-size: 14px;
      font-family: 'PingFang SC-Regular', 'PingFang SC';
      font-weight: 400;
      color: #121f3e;
      background: #faf9fc;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 48px;
      line-height: 48px;
      padding: 0 10px;

      &>span {
        &:first-child {
          font-size: 16px;
        }
      }
    }

    .unit-style {
      font-size: 14px;
      font-family: 'PingFang SC-Regular', 'PingFang SC';
      font-weight: 400;
      color: #121f3e;
      height: 40px;
      line-height: 40px;
    }
  }

  ::v-deep .el-select,
  .el-input {
    width: fit-content;
  }

  .camera-tag {
    background: #f6f5fa;
    border-radius: 4px;
    font-size: 14px;
    font-family: 'PingFang SC-Regular', 'PingFang SC';
    font-weight: 400;
    color: #121f3e;
    border: none;
    margin-right: 8px;

    ::v-deep .el-tag__close {
      color: #121f3e;

      &:hover {
        color: #fff;
        background-color: #3562db;
      }
    }
  }
}
</style>
