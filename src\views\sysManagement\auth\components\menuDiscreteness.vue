<template>
  <div>
    <ContentCard :title="type == 'new' ? '新建菜单' : type == 'edit' ? '编辑菜单' : type == 'details' ? '查看菜单' : ''"></ContentCard>
    <el-form ref="ruleForm" style="padding-left: 10px; padding-right: 10px" :model="form" :rules="rules">
      <div style="display: flex; justify-content: space-between">
        <el-form-item label="菜单类型" label-width="85px">
          <el-radio-group v-model="form.radio" style="width: 200px" :disabled="type == 'details'">
            <el-radio label="1">菜单</el-radio>
            <el-radio label="2">按钮</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="上级菜单" label-width="85px">
          <el-select ref="treeSelect" v-model="form.superiorMenu" clearable placeholder="请选择上级菜单" :disabled="type == 'details'" @focus="getMenu" @clear="handleClear">
            <el-option hidden :value="form.superiorMenu" :label="areaName"> </el-option>
            <el-tree :data="menuList" :props="defaultProps" :expand-on-click-node="false" :check-on-click-node="true" @node-click="handleNodeClick"> </el-tree>
          </el-select>
        </el-form-item>
        <el-form-item label="菜单名称" label-width="85px" prop="menuName">
          <el-input v-model="form.menuName" placeholder="请输入菜单名称" class="ipt" :disabled="type == 'details'" maxlength="50"></el-input>
        </el-form-item>
      </div>
      <div style="display: flex; justify-content: space-between">
        <el-form-item label="页签名称" label-width="85px" prop="pageTitle">
          <el-input v-model="form.pageTitle" placeholder="请输入页签标题" class="ipt" disabled></el-input>
        </el-form-item>
        <el-form-item label="链接地址" label-width="85px">
          <el-input v-model="form.chainedAddress" placeholder="请输入链接地址" class="ipt" :disabled="type == 'details'" maxlength="50"></el-input>
        </el-form-item>
        <el-form-item label="排序号" label-width="85px" prop="sortNumber">
          <el-input
            v-model="form.sortNumber"
            placeholder="请输入排序号"
            class="ipt"
            :disabled="type == 'details'"
            @input="(value) => (form.sortNumber = value.replace(/[^\d]/g, ''))"
          ></el-input>
        </el-form-item>
      </div>
      <el-form-item label="菜单图标" label-width="85px">
        <span style="font-size: 10px; color: #7f848c">支持{{ uploadAcceptDict['picture'].type }},且大小不超过{{ uploadAcceptDict['picture'].fileSize }}M</span>
        <div style="display: flex">
          <el-upload
            action=""
            :headers="headers"
            list-type="picture-card"
            :class="{ hide: hideUpload }"
            :file-list="fileList"
            :accept="uploadAcceptDict['picture'].type"
            :limit="1"
            :before-upload="beforeAvatarUpload"
            :http-request="httpRequset"
            :on-remove="handleRemove"
            :on-change="fileChange"
            :disabled="type == 'details'"
          >
            <i class="el-icon-circle-plus-outline" style="color: #3562db"
            ><br /><span style="font-size: 10px; color: #7f848c">菜单默认图标</span><span style="font-size: 10px; color: #7f848c">(20*20)</span></i
            >
          </el-upload>
          &nbsp;
          <el-upload
            action=""
            :headers="headers"
            list-type="picture-card"
            :class="{ hide2: hideUpload2 }"
            :file-list="fileList2"
            :accept="uploadAcceptDict['picture'].type"
            :limit="1"
            :before-upload="beforeAvatarUpload"
            :http-request="httpRequset2"
            :on-remove="handleRemove2"
            :on-change="fileChange2"
            :disabled="type == 'details'"
          >
            <!-- <img v-if="imageUrl" :src="imageUrl" class="avatar" /> -->
            <i class="el-icon-circle-plus-outline" style="color: #3562db"
            ><br /><span style="font-size: 10px; color: #7f848c">菜单选中图标</span><span style="font-size: 10px; color: #7f848c">(32*32)</span></i
            >
          </el-upload>
        </div>
      </el-form-item>
      <el-form-item label="备注" label-width="85px">
        <el-input
          v-model="form.remarks"
          style="width: 60%"
          placeholder="请输入备注"
          type="textarea"
          maxlength="200"
          show-word-limit
          :autosize="{ minRows: 4, maxRows: 4 }"
          :disabled="type == 'details'"
        ></el-input>
      </el-form-item>
    </el-form>
    <div class="buttom">
      <el-button v-if="type != 'details'" type="primary" @click="confirm('ruleForm')">保存</el-button>
      <el-button type="primary" plain @click="cancel">取消</el-button>
    </div>
  </div>
</template>
<script>
import store from '@/store/index'
import axios from 'axios'
import { uploadAcceptDict } from '@/util/dict.js'
export default {
  props: {
    type: String,
    menuId: Number,
    selectMenu: Object
  },
  data() {
    return {
      uploadAcceptDict,
      form: {
        superiorMenu: '',
        radio: '',
        menuName: '',
        pageTitle: '',
        chainedAddress: '',
        sortNumber: '',
        remarks: ''
      },
      icon: {},
      formData: '',
      formData2: '',
      imageUrl: '',
      menuList: [],
      fileList: [],
      fileList2: [],
      hideUpload: false,
      hideUpload2: false,
      areaName: '',
      rules: {
        menuName: [{ required: true, message: '请输入菜单名称', trigger: 'blur' }],
        // pageTitle: [{ required: true, message: '请输入页签名称', trigger: 'blur' }],
        sortNumber: [{ required: true, message: '请输入排序号', trigger: 'blur' }]
      },
      defaultProps: {
        children: 'children',
        label: 'menuName'
      }
    }
  },
  computed: {
    headers() {
      return {
        Authorization: 'Bearer ' + store.state.user.token
      }
    }
  },
  created() {
    if (this.type != 'new') {
      this.$api.getMenuInfo({ menuId: this.menuId }).then((res) => {
        if (res.code == 200) {
          // parentId == 0 ? '' : parentId 0代表顶级菜单
          this.form.superiorMenu = res.data.menuInfo.parentId == 0 ? '' : res.data.menuInfo.parentId
          this.form.radio = JSON.stringify(res.data.menuInfo.menuType)
          this.form.menuName = res.data.menuInfo.menuName
          this.form.chainedAddress = res.data.menuInfo.pathUrl
          this.form.sortNumber = res.data.menuInfo.orderNum
          this.form.remarks = res.data.menuInfo.remark
          this.areaName = res.data.menuInfo.parentName
          let url = JSON.parse(res.data.menuInfo.icon)
          if (url.approve != undefined) {
            this.fileList = [
              {
                url: this.$tools.imgUrlTranslation(url.approve)
              }
            ]
            this.hideUpload = true
            this.icon.approve = url.approve
          } else {
            this.hideUpload = false
          }
          if (url.pitch != undefined) {
            this.fileList2 = [
              {
                url: this.$tools.imgUrlTranslation(url.pitch)
              }
            ]
            this.hideUpload2 = true
            this.icon.pitch = url.pitch
          } else {
            this.hideUpload2 = false
          }
        }
      })
    } else {
      this.form.radio = '1'
      this.form.superiorMenu = this.selectMenu.value
      this.areaName = this.selectMenu.label
    }
  },
  methods: {
    handleClear() {
      this.form.superiorMenu = ''
      this.areaName = ''
    },
    handleNodeClick(data) {
      this.form.superiorMenu = data.menuId
      this.areaName = data.menuName
      this.$refs.treeSelect.blur()
    },
    getMenu() {
      this.$api.SysMenu({ userId: this.$store.state.user.userInfo.userId }).then((res) => {
        if (res.code == 200) {
          this.menuList = res.data
        }
      })
    },
    confirm(formName) {
      let params = {
        menuType: Number(this.form.radio), // 菜单类型
        parentId: this.form.superiorMenu, // 上级菜单
        menuName: this.form.menuName, // 菜单名称
        pathUrl: this.form.chainedAddress, // 链接地址
        orderNum: this.form.sortNumber, // 排序号
        state: 0,
        remark: this.form.remarks
      }
      if (this.icon.approve == undefined && this.icon.pitch == undefined) {
        params.icon = ''
      } else {
        params.icon = JSON.stringify(this.icon)
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.type == 'new') {
            params.userId = this.$store.state.user.userInfo.userId
            this.$api.menuAdd(params, { 'operation-type': 1 }).then((res) => {
              if (res.code == 200) {
                this.$message({
                  message: res.data,
                  type: 'success'
                })
                this.$emit('menuDiscreteness')
                this.$emit('child-event', false)
              } else {
                this.$message.error(res.data)
              }
            })
          } else {
            params.menuId = this.menuId
            this.$api.edit(params, { 'operation-type': 2 }).then((res) => {
              if (res.code == 200) {
                this.$message({
                  message: res.data,
                  type: 'success'
                })
                this.$emit('menuDiscreteness')
                this.$emit('child-event', false)
              } else {
                this.$message.error(res.data)
              }
            })
          }
        } else {
          return false
        }
      })
    },
    cancel() {
      this.$emit('child-event', false)
    },
    fileChange(file, fileList) {
      this.fileList = fileList
    },
    beforeAvatarUpload(file) {
      const fileSize = this.uploadAcceptDict['picture'].fileSize
      const isLt2M = file.size / 1024 / 1024 < fileSize
      if (!isLt2M) {
        this.$message.error(`上传文件大小不能超过 ${fileSize}MB!`)
        return false
      }
      const extension = file.name.substring(file.name.lastIndexOf('.'))
      const isFormat = this.uploadAcceptDict['picture'].type.includes(extension)
      if (!isFormat) {
        this.$message.error(`上传图片只能是 ${this.uploadAcceptDict['picture'].type}格式!`)
        return false
      }
    },
    handleRemove(file, fileList) {
      this.icon.approve = undefined
      // this.fileList = fileList
      this.hideUpload = false
    },
    httpRequset() {
      this.formData = new FormData()
      this.fileList.forEach((item) => {
        this.formData.append('file', item.raw)
      })
      axios
        .post(__PATH.VUE_SYS_API + 'SysMenu/upload', this.formData, {
          headers: {
            Authorization: 'Bearer ' + store.state.user.token
          }
        })
        .then((res) => {
          if (res.data.code == 200) {
            this.hideUpload = true
            this.icon.approve = res.data.data
            this.$message({
              showClose: true,
              message: res.data.msg,
              type: 'success'
            })
          } else {
            this.$message.error(res.data.msg)
          }
        })
    },
    fileChange2(file, fileList) {
      this.fileList2 = fileList
    },
    handleRemove2(file, fileList) {
      this.icon.pitch = undefined
      this.hideUpload2 = false
    },
    httpRequset2() {
      this.formData2 = new FormData()
      this.fileList2.forEach((item) => {
        this.formData2.append('file', item.raw)
      })
      axios
        .post(__PATH.VUE_SYS_API + 'SysMenu/upload', this.formData2, {
          headers: {
            Authorization: 'Bearer ' + store.state.user.token
          }
        })
        .then((res) => {
          if (res.data.code == 200) {
            this.icon.pitch = res.data.data
            this.hideUpload2 = true
            this.$message({
              showClose: true,
              message: res.data.msg,
              type: 'success'
            })
          } else {
            this.$message.error(res.data.msg)
          }
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.top {
  border-left: 3px solid #759bfa;
  color: #759bfa;
  font-weight: 700;
  padding-left: 15px;
}
.ipt {
  width: 200px;
}
.buttom {
  position: absolute;
  bottom: 1.8%;
  height: 50px;
  line-height: 50px;
  width: calc(100% - 290px);
  background: rgb(255 255 255 / 80%);
  box-shadow: 0 0 12px 3px rgb(0 0 0 / 10%);
  text-align: right;
  padding-right: 10px;
}
::v-deep .el-upload--picture-card {
  border: 1px dashed #409eff !important;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.hide {
  ::v-deep .el-upload--picture-card {
    display: none;
  }
  ::v-deep .el-upload-list__item {
    background-color: #efefef;
  }
}
.hide2 {
  ::v-deep .el-upload--picture-card {
    display: none;
  }
  ::v-deep .el-upload-list__item {
    background-color: #efefef;
  }
}
</style>
