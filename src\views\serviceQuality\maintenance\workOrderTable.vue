<template>
  <PageContainer>
    <div slot="content" ref="contentRef">
      <div class="back" @click="goBack">
        <i class="el-icon-arrow-left"></i>
        <span>返回</span>
      </div>
      <el-table v-loading="tableLoading" :data="tableData" border style="width: 100%" height="88%" @row-click="handleClick">
        <el-table-column label="序号" type="index" width="50" align="center" :resizable="false">
          <template slot-scope="scope">
            {{ (pageNo - 1) * pageSize + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="工单号" width="180" :resizable="false">
          <template slot-scope="scope">
            <span style="color: #3562db">{{ scope.row.workNum }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="workTypeName" label="工单类型" width="180" show-overflow-tooltip :resizable="false"> </el-table-column>
        <el-table-column prop="sourcesDeptName" label="所属科室" show-overflow-tooltip :resizable="false"> </el-table-column>
        <el-table-column prop="localtionNames" label="服务地点" show-overflow-tooltip :resizable="false"> </el-table-column>
        <el-table-column label="服务事项" show-overflow-tooltip :resizable="false">
          <template slot-scope="scope">
            <span>{{ scope.row.itemTypeName }}-{{ scope.row.itemDetailName }}-{{ scope.row.itemServiceName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="designateDeptName" label="服务部门" show-overflow-tooltip :resizable="false"> </el-table-column>
        <el-table-column prop="designatePersonName" label="服务人员" show-overflow-tooltip :resizable="false"> </el-table-column>
        <el-table-column prop="designatePersonPhone" label="联系方式" show-overflow-tooltip :resizable="false"> </el-table-column>
        <el-table-column prop="questionDescription" label="说明" show-overflow-tooltip :resizable="false"> </el-table-column>
        <el-table-column label="状态" :resizable="false">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.flowcode == '1'" type="danger">未受理</el-tag>
            <el-tag v-else-if="scope.row.flowcode == '2'" type="danger">未派工</el-tag>
            <el-tag v-else-if="scope.row.flowcode == '3'" type="success">已派工</el-tag>
            <el-tag v-else-if="scope.row.flowcode == '4'" type="warning">已挂单</el-tag>
            <el-tag v-else-if="scope.row.flowcode == '5'" type="success">已完工</el-tag>
            <el-tag v-else-if="scope.row.flowcode == '6'" type="danger">已取消</el-tag>
            <el-tag v-else-if="scope.row.flowcode == '7'" type="info">暂存</el-tag>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="pageNo"
        :page-sizes="[15, 30, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
      </el-pagination>
      <template v-if="workOrderDetailCenterShow">
        <el-dialog :visible.sync="workOrderDetailCenterShow" custom-class="detailDialog main" :close-on-click-modal="false" :before-close="workOrderDetailCloseDialog">
          <template slot="title">
            <span class="dialog-title">{{ dialogTitle }}</span>
          </template>
          <workOrderDetailList v-if="detailObj.workTypeCode == '1'" :rowData="detailObj" />
          <workOrderDetailList3 v-if="detailObj.workTypeCode == '2'" :rowData="detailObj" />
          <workOrderDetailList4 v-if="detailObj.workTypeCode == '5'" :rowData="detailObj" />
          <workOrderDetailList7 v-if="detailObj.workTypeCode == '8'" :rowData="detailObj" />
          <workOrderDetailList2 v-if="detailObj.workTypeCode == '17'" :rowData="detailObj" :isHandle="true" @close="workOrderDetailCloseDialog" />
          <workOrderDetailList5 v-if="detailObj.workTypeCode == '11'" :rowData="detailObj" />
          <workOrderDetailList6 v-if="detailObj.workTypeCode == '3'" :rowData="detailObj" />
        </el-dialog>
      </template>
    </div>
  </PageContainer>
</template>
<script>
import store from '@/store/index'
import workOrderDetailList from './components/workOrderDetailList.vue'
import workOrderDetailList2 from '@/views/equipmentCenter/maintenanceWorkOrderManagement/workOrderDetail.vue'
import workOrderDetailList3 from '@/views/serviceQuality/cleaning/components/workOrderDetailList.vue'
import workOrderDetailList4 from '@/views/serviceQuality/complaintManagement/workOrderDetailList.vue'
import workOrderDetailList5 from '@/views/serviceQuality/snapshot/components/workOrderDetailList.vue'
import workOrderDetailList6 from '@/views/serviceQuality/transport/components/workOrderDetailList.vue'
import workOrderDetailList7 from '@/views/serviceQuality/workOrderSelf/workOrderDetailList.vue'
export default {
  name: 'WorkOrder',
  components: {
    workOrderDetailList,
    workOrderDetailList2,
    workOrderDetailList3,
    workOrderDetailList4,
    workOrderDetailList5,
    workOrderDetailList6,
    workOrderDetailList7
  },
  provide() {
    return { closeAndRefresh: this.closeAndRefresh }
  },
  data() {
    return {
      workNum: '', // 工单号
      flowCode: '', // 工单状态
      feedbackFlag: '', // 跟踪状态
      showTimeType: '1', // 申报时间
      dateVal: '', // 自定义时间
      tableData: [], // 表格数据
      total: 0, // 总条数
      pageNo: 1, // 当前页
      pageSize: 15, // 每页条数
      tableLoading: false, // 表格加载状态
      flowcodeOption: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '未受理',
          value: '1'
        },
        {
          label: '暂存',
          value: '7'
        },
        {
          label: '未派工',
          value: '2'
        },
        {
          label: '已派工',
          value: '3'
        },
        {
          label: '已挂单',
          value: '4'
        },
        {
          label: '已完工',
          value: '5'
        },
        {
          label: '已取消',
          value: '6'
        }
      ],
      feedbackFlagOption: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '未督办',
          value: '2'
        },
        {
          label: '已督办',
          value: '3'
        },
        {
          label: '未回访',
          value: '0'
        },
        {
          label: '已回访',
          value: '1'
        },
        {
          label: '未回复',
          value: '5'
        },
        {
          label: '已回复',
          value: '4'
        }
      ],
      showTimeTypeOption: [
        {
          label: '本年',
          value: '3'
        },
        {
          label: '昨天',
          value: '5'
        },
        {
          label: '今天',
          value: '1'
        },
        {
          label: '本周',
          value: '6'
        },
        {
          label: '本月',
          value: '2'
        },
        {
          label: '其他',
          value: '4'
        }
      ],
      countData: {},
      workOrderDetailCenterShow: false,
      detailObj: {},
      dialogTitle: '',
      free1: '',
      typeName: ''
    }
  },
  created() {
    this.typeName = this.$route.query.typeName
  },
  mounted() {
    this.execute()
  },
  methods: {
    execute() {
      if (this.typeName == 'today') {
        this.getTodayList()
      } else if (this.typeName == 'complaint') {
        this.getComplaintList()
      } else if (this.typeName == 'MaintenanceCost') {
        this.getMaintenanceCostList()
      } else if (this.typeName == 'bydept') {
        this.getBydeptList()
      } else if (this.typeName == 'byteam') {
        this.getByteamList()
      } else if (this.typeName == 'byitem') {
        this.getByitemList()
      } else if (this.typeName == 'byWorker') {
        this.getByWorkerList()
      } else if (this.typeName == 'byWorkType') {
        this.getByWorkTypeList()
      } else if (this.typeName == 'byWorkTypeFinish') {
        this.getByWorkTypeFinishList()
      } else if (this.typeName == 'byRegion') {
        this.getByRegionList()
      } else if (this.typeName == 'location') {
        this.getByLocationList()
      } else if (this.typeName == 'byFlowCode') {
        this.getByFlowCodeList()
      } else if (this.typeName == 'bydeptPro') {
        this.getBydeptProList()
      } else {
        this.getList()
      }
    },
    getList() {
      const userInfo = store.state.user.userInfo.user
      let params = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        selectType: 5,
        isTimeOut: 1,
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
      this.tableLoading = true
      this.$api.getCallCenterData(params).then((res) => {
        this.tableLoading = false
        this.tableData = res.body.result
        this.total = parseInt(res.body.countOverTimes)
      })
    },
    getByLocationList() {
      const userInfo = store.state.user.userInfo.user
      let params = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        sourcesDept: this.$route.query.historyDept,
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ',
        workTypeCode: this.$route.query.workTypeCode,
        showTimeType: '7'
      }
      let arr = this.$route.query.historyLocaltion.split(',')
      if (arr[0]) {
        params.region = arr[0]
      }
      if (arr[1]) {
        params.buliding = arr[1]
      }
      if (arr[2]) {
        params.storey = arr[2]
      }
      if (arr[3]) {
        params.room = arr[3]
      }
      this.tableLoading = true
      this.$api.getWorkOrderDataList(params).then((res) => {
        this.tableLoading = false
        this.tableData = res.rows
        this.total = res.total
      })
    },
    getBydeptList() {
      const userInfo = store.state.user.userInfo.user
      let params = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        startTime: this.$route.query.startTime,
        endTime: this.$route.query.endTime,
        sourcesDept: this.$route.query.sourcesDept,
        showTimeType: this.$route.query.showTimeType,
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
      this.tableLoading = true
      this.$api.getWorkOrderDataList(params).then((res) => {
        this.tableLoading = false
        this.tableData = res.rows
        this.total = res.total
      })
    },
    getBydeptProList() {
      const userInfo = store.state.user.userInfo.user
      let params = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        startTime: this.$route.query.startTime,
        endTime: this.$route.query.endTime,
        sourcesDept: this.$route.query.sourcesDept,
        showTimeType: this.$route.query.showTimeType,
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ',
        workTypeCode: this.$route.query.workTypeCode
      }
      this.tableLoading = true
      this.$api.getWorkOrderDataList(params).then((res) => {
        this.tableLoading = false
        this.tableData = res.rows
        this.total = res.total
      })
    },
    getByteamList() {
      const userInfo = store.state.user.userInfo.user
      let params = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        startTime: this.$route.query.startTime,
        endTime: this.$route.query.endTime,
        designateDeptCode: this.$route.query.designateDeptCode,
        showTimeType: this.$route.query.showTimeType,
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
      if (this.$route.query.flowcode == '5') {
        params.flowcode = '5'
      }
      this.tableLoading = true
      this.$api.getWorkOrderDataList(params).then((res) => {
        this.tableLoading = false
        this.tableData = res.rows
        this.total = res.total
      })
    },
    getByitemList() {
      const userInfo = store.state.user.userInfo.user
      let params = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        startTime: this.$route.query.startTime,
        endTime: this.$route.query.endTime,
        itemServiceThreeCode: this.$route.query.itemServiceCode,
        showTimeType: this.$route.query.showTimeType,
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
      this.tableLoading = true
      this.$api.getWorkOrderDataList(params).then((res) => {
        this.tableLoading = false
        this.tableData = res.rows
        this.total = res.total
      })
    },
    getByWorkerList() {
      const userInfo = store.state.user.userInfo.user
      let params = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        startTime: this.$route.query.startTime,
        endTime: this.$route.query.endTime,
        designatePersonCode: this.$route.query.designatePersonCode,
        showTimeType: this.$route.query.showTimeType,
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
      this.tableLoading = true
      this.$api.getWorkOrderDataList(params).then((res) => {
        this.tableLoading = false
        this.tableData = res.rows
        this.total = res.total
      })
    },
    getByWorkTypeList() {
      const userInfo = store.state.user.userInfo.user
      let params = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        startTime: this.$route.query.startTime,
        endTime: this.$route.query.endTime,
        workTypeCode: this.$route.query.workTypeCode,
        showTimeType: this.$route.query.showTimeType,
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
      this.tableLoading = true
      this.$api.getWorkOrderDataList(params).then((res) => {
        this.tableLoading = false
        this.tableData = res.rows
        this.total = res.total
      })
    },
    getByFlowCodeList() {
      const userInfo = store.state.user.userInfo.user
      let params = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        startTime: this.$route.query.startTime,
        endTime: this.$route.query.endTime,
        workTypeCode: '',
        flowcode: this.$route.query.flowcode,
        showTimeType: this.$route.query.showTimeType,
        free1: this.$route.query.free1,
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
      this.tableLoading = true
      this.$api.getWorkOrderDataList(params).then((res) => {
        this.tableLoading = false
        this.tableData = res.rows
        this.total = res.total
      })
    },
    getByWorkTypeFinishList() {
      const userInfo = store.state.user.userInfo.user
      let params = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        startTime: this.$route.query.startTime,
        endTime: this.$route.query.endTime,
        workTypeCode: this.$route.query.workTypeCode,
        flowcode: '5',
        showTimeType: this.$route.query.showTimeType,
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
      this.tableLoading = true
      this.$api.getWorkOrderDataList(params).then((res) => {
        this.tableLoading = false
        this.tableData = res.rows
        this.total = res.total
      })
    },
    getByRegionList() {
      const userInfo = store.state.user.userInfo.user
      let params = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        startTime: this.$route.query.startTime,
        endTime: this.$route.query.endTime,
        showTimeType: this.$route.query.showTimeType,
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ',
        region: this.$route.query.regionId
      }
      this.tableLoading = true
      this.$api.getWorkOrderDataList(params).then((res) => {
        this.tableLoading = false
        this.tableData = res.rows
        this.total = res.total
      })
    },
    getTodayList() {
      const userInfo = store.state.user.userInfo.user
      let params = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        showTimeType: '1',
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
      this.tableLoading = true
      this.$api.getWorkOrderDataList(params).then((res) => {
        this.tableLoading = false
        this.tableData = res.rows
        this.total = res.total
      })
    },
    getComplaintList() {
      const userInfo = store.state.user.userInfo.user
      let params = {
        workTypeCode: 5,
        workTypeName: 'TS',
        showTimeType: '1',
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
      this.tableLoading = true
      this.$api.getWorkOrderDataList(params).then((res) => {
        this.tableLoading = false
        this.tableData = res.rows
        this.total = res.total
      })
    },
    getMaintenanceCostList() {
      const userInfo = store.state.user.userInfo.user
      let showTimeType = ''
      if (this.$route.query.totalCostDateType == 'week') {
        showTimeType = '6'
      } else if (this.$route.query.totalCostDateType == 'month') {
        showTimeType = '2'
      } else if (this.$route.query.totalCostDateType == 'year') {
        showTimeType = '3'
      }
      let params = {
        flowcode: '5',
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        showTimeType,
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
      this.tableLoading = true
      this.$api.getWorkOrderDataList(params).then((res) => {
        this.tableLoading = false
        this.tableData = res.rows
        this.total = res.total
      })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.execute()
    },
    handleCurrentChange(val) {
      this.pageNo = val
      this.execute()
    },
    handleClick(row) {
      let workTypeCodeArr = ['1', '2', '5', '17', '11', '3', '8']
      if (!workTypeCodeArr.includes(row.workTypeCode)) {
        return this.$message.error('该工单类型无法查看详情')
      }
      this.detailObj = row
      if (this.detailObj.flowtype) {
        this.dialogTitle = `${this.detailObj.workTypeName}（${this.detailObj.flowtype}）`
      } else {
        this.dialogTitle = `${this.detailObj.workTypeName}`
      }

      this.workOrderDetailCenterShow = true
    },
    workOrderDetailCloseDialog() {
      this.workOrderDetailCenterShow = false
    },
    goBack() {
      this.$router.go(-1)
    },
    closeAndRefresh() {
      this.workOrderDetailCenterShow = false
      this.execute()
    }
  }
}
</script>
<style lang="scss" scoped>
.container-content > div {
  height: 100%;
  background-color: #fff;
  padding: 16px;
  margin-top: 16px;
}

.el-table {
  margin-bottom: 12px;
}

::v-deep .el-date-editor .el-range__icon {
  line-height: 24px;
}

::v-deep .el-dialog {
  height: 80vh;
  margin-top: 10vh;
}

::v-deep .el-dialog__wrapper {
  overflow: hidden;
}

::v-deep .el-dialog__body {
  height: 93%;
}

::v-deep .el-table__row:hover {
  cursor: pointer;
}

.back {
  color: #121f3e;
  cursor: pointer;
  margin-bottom: 16px;
}
</style>
