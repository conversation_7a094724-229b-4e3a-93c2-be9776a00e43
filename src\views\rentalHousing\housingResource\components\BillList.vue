<script>
export default {
  name: '<PERSON><PERSON><PERSON>',
  props: {
    id: String
  },
  data: () => ({
    tableLoading: false,
    tableData: [],
    searchForm: {
      name: '',
      period: ['', ''],
      tenant: '',
      contract: ''
    }
  }),
  computed: {
    OperateType() {
      return {
        // 查看
        VIEW: 'view',
        // 下载
        DOWNLOAD: 'DOWNLOAD'
      }
    }
  },
  mounted() {
    this.onSearch()
  },
  methods: {
    // 表格相关操作总线处理
    onOperate(type, row) {
      switch (type) {
        case this.OperateType.VIEW:
          // this.$router.push({})
          break
        case this.OperateType.DOWNLOAD:
          //
          break
      }
    },
    onSearch() {
      const params = {
        pageNum: 1,
        pageSize: 999,
        houseId: this.id,
        billName: this.searchForm.name,
        billStartCycle: this.searchForm.period[0],
        billEndCycle: this.searchForm.period[1],
        contractNum: this.searchForm.contract,
        userName: this.searchForm.tenant
      }
      console.log(this.searchForm.period);
      this.tableLoading = true
      this.$api.rentalHousingApi.queryHouseBillByPage(params)
        .then(res => {
          if (res.code === '200') {
            this.tableData = res.data.records
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取合同列表失败'))
        .finally(() => this.tableLoading = false)
    },
    onReset() {
      this.$refs.formRef.resetFields()
      this.onSearch()
    }
  }
}
</script>
<template>
  <div class="bill-list">
    <el-form ref="formRef" class="bill-list__form" :model="searchForm" inline>
      <el-form-item prop="name">
        <el-input v-model="searchForm.name" placeholder="搜索账单名称" clearable></el-input>
      </el-form-item>
      <el-form-item prop="period">
        <el-date-picker v-model="searchForm.period" type="daterange" value-format="yyyy-MM-dd" start-placeholder="账单周期" end-placeholder="账单周期" clearable></el-date-picker>
      </el-form-item>
      <el-form-item prop="tenant">
        <el-input v-model="searchForm.tenant" placeholder="搜索承租人" clearable></el-input>
      </el-form-item>
      <el-form-item prop="contract">
        <el-input v-model="searchForm.contract" placeholder="搜索合同号" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" plain @click="onReset">重置</el-button>
        <el-button type="primary" @click="onSearch">搜索</el-button>
      </el-form-item>
    </el-form>
    <div class="bill-list__table">
      <el-table v-loading="tableLoading" height="100%" :data="tableData" border stripe table-layout="auto" class="tableAuto" row-key="id">
        <el-table-column prop="billNum" show-overflow-tooltip label="账单编号" />
        <el-table-column prop="billName" show-overflow-tooltip label="账单名称" />
        <el-table-column prop="billCycle" show-overflow-tooltip label="账单周期" />
        <el-table-column prop="billMoney" show-overflow-tooltip label="账单金额（元）" />
        <el-table-column prop="userName" show-overflow-tooltip label="承租人" />
        <el-table-column prop="contractNum" show-overflow-tooltip label="合同号" />
      </el-table>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.bill-list {
  height: 100%;
  &__table {
    height: calc(100% - 62px);
  }
}
</style>
