<template>
  <el-dialog custom-class="model-dialog" title="导出" :visible.sync="dialogVisibleExport" @close="closeDialog">
    <div v-loading="dialogTableLoading" style="width: 100%; background-color: #fff; padding: 10px; min-height: 200px;">
      <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">全选</el-checkbox>
      <el-checkbox-group v-model="checkedArr" style="margin-left: 20px;" @change="handleNameArrChange">
        <el-checkbox v-for="item in titles" :key="item.EnglishName" :label="item" style="margin-top: 10px; width: 20%;">{{ item.ChineseName }}</el-checkbox>
      </el-checkbox-group>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="closeDialog">取 消</el-button>
      <el-button type="primary" class="sino-button-sure" @click="getExportData(1)">导出全部记录</el-button>
      <el-button type="primary" class="sino-button-sure" :disabled="materRows.length < 1" @click="getExportData(0)">导出选中记录</el-button>
    </span>
  </el-dialog>
</template>
<script>
import axios from 'axios'
import $ from 'jquery'
export default {
  // 风险列表导出功能
  name: 'exportDialog',
  props: {
    dialogVisibleExport: {
      type: Boolean,
      default: false
    },
    startTime: {
      type: String,
      default: ''
    },
    endTime: {
      type: String,
      default: ''
    },
    filters: {
      type: Object,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: ''
    },
    // eslint-disable-next-line vue/require-prop-type-constructor
    materRows: '',
    exportType: {},
    // eslint-disable-next-line vue/require-prop-type-constructor
    activeTitle: ''
  },
  data() {
    return {
      dialogTableLoading: false,
      checkAll: false,
      isIndeterminate: true,
      titles: [],
      checkedArr: [],
      typeTitles: []
    }
  },
  watch: {
    materRows(n, o) {},
    dialogVisibleExport(val) {
      if (this.activeTitle != '1') {
        this.titles.forEach((item, index) => {
          if (item.EnglishName.indexOf('riskAnalyse') != -1) {
            this.titles.splice(index, 1)
          }
        })
      } else {
        this.titles = []
        this.getExportField()
      }
    }
  },
  mounted() {
    this.getExportField()
  },
  methods: {
    // 获取导出字段
    getExportField() {
      let params = {
        exportType: 8
      }
      this.$api.ipsmRiskManageGetExportFields(params).then((res) => {
        for (var i in res.data.list) {
          this.titles.push({
            EnglishName: res.data.list[i],
            ChineseName: res.data.list[i].split('_')[1]
          })
        }
      })
    },
    // 导出
    getExportData(type) {
      // type   1全部 0选中
      if (this.dialogTableLoading) {
        return
      }
      if (this.checkedArr.length < 1) {
        this.$message.error('请至少选择一个导出字段')
        return
      }
      let field = []
      field = this.checkedArr.map((i) => {
        return i.EnglishName
      })
      let idArr = []
      idArr = this.materRows.map((o) => {
        return o.id
      })
      this.dialogTableLoading = true
      var params = {
        riskType: this.activeTitle == 1 ? this.exportType.dictValue : '',
        fields: field.join(','),
        ids: type == 0 ? idArr.join(',') : '',
        controlGroupIds: JSON.parse(sessionStorage.getItem('LOGINDATA')).controlGroupIds,
        riskLevel: this.filters.companyCode || '',
        riskLevel: this.activeTitle == 3 ? this.exportType.dictValue : '',
        riskName: this.filters.compoundQuery
      }
      this.$api
        .ipsmRiskManageExportExcel(params)
        .then((res) => {
          if (res.status == 200 && !res.data.code) {
            let name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
            console.log(name)
            let blob = new Blob([res.data]) // { type: "application/vnd.ms-excel" }
            let url = window.URL.createObjectURL(blob) // 创建一个临时的url指向blob对象
            // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
            let a = document.createElement('a')
            a.href = url
            a.download = decodeURI(name)
            a.click()
            // 释放这个临时的对象url
            window.URL.revokeObjectURL(url)
            if (res.data.code == 500) {
              this.$message.error(res.data.message)
            } else if (res.status == 200 && !res.data.code) {
              this.$message.success('导出成功')
            } else {
              this.$message.error('导出失败')
            }
          }
          this.closeDialog()
          this.dialogTableLoading = false
        })
        .catch((res) => {
          this.$message.success('导出失败')
          this.dialogTableLoading = false
        })
    },
    closeDialog() {
      this.checkedArr = []
      this.$emit('closeDialog')
    },
    handleNameArrChange(val) {
      this.checkedArr = val
    },
    handleCheckAllChange(val) {
      this.checkedArr = val ? this.titles : []
      this.isIndeterminate = false
    }
  }
  // filters: {
  //   checkListFilter: function(value) {
  //     if (!value) return "";
  //     return value.split("-")[1];
  //   }
  // }
}
</script>
<style lang="scss" type="stylesheet/stylus">
.metera-dialog .el-dialog__body {
  height: 330px;
  overflow-y: auto;
}
</style>
