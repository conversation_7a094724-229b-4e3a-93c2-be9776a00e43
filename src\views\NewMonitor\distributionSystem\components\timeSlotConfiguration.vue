<template>
  <el-dialog v-if="dialogShow" :title="title" width="45%" :visible.sync="dialogShow" custom-class="model-dialog">
    <div class="time_slot">
      <div v-for="(item, index) in timeRanges" :key="index" class="time_item">
        <span class="time_name">{{ item.name }}</span>
        <div class="batch_time">
          <div v-for="(v, i) in item.ranges" :key="i" class="slot_time_picker">
            <el-time-select placeholder="起始时间" v-model="v.timeSegmentStart" :picker-options="{
              start: '00:00',
              step: '00:30',
              end: '24:00'
            }">
            </el-time-select>
            <el-time-select placeholder="结束时间" v-model="v.timeSegmentEnd" :picker-options="{
              start: '00:00',
              step: '00:30',
              end: '24:00',
              minTime: v.timeSegmentStart
            }">
            </el-time-select>
          </div>
          <span class="add_del" @click="addTimeRange(item)">+</span>
          <span v-if="item.ranges.length > 1" class="add_del" @click="removeTimeRange(item)">-</span>
        </div>
      </div>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="cancel">取消</el-button>
      <el-button type="primary" @click="submit">确定</el-button>
    </div>
  </el-dialog>
</template>
<script>
import moment from 'moment'
export default {
  data() {
    return {
      timeSegmentStart: '',
      timeSegmentEnd: '',
      dialogShow: false,
      title: '分时段配置',
      timeRanges: [
        {
          name: '尖峰',
          timeSegmentType: 1,
          ranges: []
        },
        {
          name: '高峰',
          timeSegmentType: 2,
          ranges: []
        },
        {
          name: '平时',
          timeSegmentType: 3,
          ranges: []
        },
        {
          name: '谷峰',
          timeSegmentType: 4,
          ranges: []
        }
      ]
    }
  },
  mounted() {
  },
  methods: {
    getData() {
      this.dialogShow = true
      this.getList()
    },
    cancel() {
      this.dialogShow = false
    },
    getList() {
      this.$api.energyConfigList({ dictionaryDetailsCode: 'PDXT' }).then(res => {
        if (res.data.length) {
          this.timeRanges.forEach(ele => {
            ele.ranges = []
            res.data.forEach(item => {
              if (ele.timeSegmentType == item.timeSegmentType) {
                // 提取开始时间和结束时间的小时和分钟部分
                ele.ranges.push({
                  timeSegmentStart: item.timeSegmentStart.substring(0, 5), // 直接赋值为字符串
                  timeSegmentEnd: item.timeSegmentEnd.substring(0, 5)
                })

              }
            })
          })
        }
      })
    },
    addTimeRange(item) {
      item.ranges.push({ timeSegmentStart: ['00:00'], timeSegmentEnd: ['24:00'] })
    },
    removeTimeRange(item) {
      item.ranges.splice(item.ranges.length - 1, 1)
    },
    checkTimeRange() {
      return new Promise((resolve, reject) => {
        // 将所有时间范围的时间段合并成一个数组
        const arr = this.timeRanges.reduce((arr, item) => {
          return [...arr, ...item.ranges]
        }, [])
        const len = arr.length
        for (let i = 0; i < len - 1; i++) {
          const item = arr[i]
          for (let j = i + 1; j < len; j++) {
            const nextItem = arr[j]
            // 定义第一个时间范围
            const range1Start = moment(item.timeSegmentStart, 'HH:mm')
            const range1End = moment(item.timeSegmentEnd, 'HH:mm')
            // 定义第二个时间范围
            const range2Start = moment(nextItem.timeSegmentStart, 'HH:mm')
            const range2End = moment(nextItem.timeSegmentEnd, 'HH:mm')
            // 判断两个时间范围是否互相重叠 && 开始时间不能等于结束时间
            const isOverlap = (range1Start.isBefore(range2End) && range1End.isAfter(range2Start)) || item.timeSegmentStart == nextItem.timeSegmentEnd
            if (isOverlap) {
              reject()
            }
          }
        }
        resolve()
      })
    },
    submit() {
      this.checkTimeRange().then(() => {
        const arr = this.timeRanges.reduce((arr, item) => {
          return [
            ...arr,
            ...item.ranges.map(ele => {
              return {
                dictionaryDetailsCode: 'PDXT',
                timeSegmentType: item.timeSegmentType,
                timeSegmentStart: ele.timeSegmentStart + ':00',
                timeSegmentEnd: ele.timeSegmentEnd + ':00'
              }
            })
          ]
        }, [])
        this.$api.energyConfigSave({ list: arr }).then(res => {
          if (res.code == '200') {
            this.dialogShow = false
          }
        })
      }, () => {
        this.$message.error('时间范围相重叠，请重新选择！')
      })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-date-editor .el-range__icon {
  line-height: 24px;
}

.time_slot {
  display: flex;
  flex-direction: column;

  .time_item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .batch_time {
      flex: 1;
      display: flex;
      flex-wrap: wrap;
    }

    .slot_time_picker {
      margin-right: 10px;
      margin-bottom: 10px;
    }

    .time_name {
      display: inline-block;
      font-size: 14px;
      margin-right: 20px;
    }

    .add_del {
      font-weight: 500;
      cursor: pointer;
      color: #3562db;
      margin-left: 20px;
      font-size: 28px;
    }
  }
}
</style>
