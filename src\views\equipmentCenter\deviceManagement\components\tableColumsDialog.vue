<template>
  <div class="dialog_page">
    <el-dialog title="列表自定义调整" :visible.sync="dialogVisible" width="55%" :before-close="handleCloseChange"
      v-loading="dialogLoading">
      <div class="dialog_content">
        <div class="tableTips">
          <span class="el-icon-warning-outline"></span>
          <span>勾选需要显示的列。拖动行进行排序。</span>
        </div>
        <div class="table_box">
          <el-table ref="multipleTable" :data="tableData" row-key="sort" height="100%" border
            @selection-change="handleSelectionChange" v-loading="tableLoading">
            <el-table-column :selectable="checkSelectable" type="selection" width="55"
              :reserve-selection="true"></el-table-column>
            <el-table-column type="index" width="60" label="序号">
              <template slot-scope="scope">
                <span>{{ scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column label="列字段名" prop="fieldName"></el-table-column>
            <el-table-column label="状态" prop="unChecked" :align="'center'">
              <template slot-scope="scope">
                <span>{{ scope.row.unChecked ? '' : '必选' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="宽度" prop="width" width="220">
              <template slot-scope="scope">
                <el-input v-model="scope.row.width" placeholder="请输入宽度" @keyup.native="proving">
                  <template slot="append">px</template>
                </el-input>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleDefault">恢复 默认</el-button>
        <el-button @click="handleCloseChange">取 消</el-button>
        <el-button type="primary" @click="handleSaveChange">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import Sortable from "sortablejs";
import store from '@/store/index'
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      tableData: [
        {
          unChecked: 'false',
          fieldName: '资产名称',
          column: 'assetName',
          sort: '1',
          isChecked: '1',
          width: '150',
        },
        {
          unChecked: 'true',
          fieldName: '资产编码',
          column: 'assetCode',
          sort: '2',
          isChecked: '1',
          width: '150',
        },
        {
          unChecked: 'true',
          fieldName: '品牌',
          column: 'assetBrand',
          sort: '3',
          isChecked: '1',
          width: '150',
        },
        {
          unChecked: 'true',
          fieldName: '型号',
          column: 'assetModel',
          sort: '4',
          isChecked: '1',
          width: '150',
        },
        {
          unChecked: 'false',
          fieldName: '生产日期',
          column: 'startDate',
          sort: '5',
          isChecked: '0',
          width: '150',
        },
        {
          unChecked: 'false',
          fieldName: 'SN码',
          column: 'assetSn',
          sort: '6',
          isChecked: '0',
          width: '150',
        },
        {
          unChecked: 'false',
          fieldName: 'NFC',
          column: 'nfcCode',
          sort: '7',
          isChecked: '0',
          width: '150',
        },
        {
          unChecked: 'false',
          fieldName: 'RFID',
          column: 'rfid',
          sort: '8',
          isChecked: '0',
          width: '150',
        },
        {
          unChecked: 'true',
          fieldName: '所在区域',
          column: 'regionReverseName',
          sort: '9',
          isChecked: '1',
          width: '150',
        },
        {
          unChecked: 'false',
          fieldName: '计量单位',
          column: 'unitOfMeasurement',
          sort: '10',
          isChecked: '0',
          width: '150',
        },
        {
          unChecked: 'false',
          fieldName: '启用日期',
          column: 'startDate',
          sort: '11',
          isChecked: '0',
          width: '150',
        },
        {
          unChecked: 'false',
          fieldName: '使用期限',
          column: 'serviceLife',
          sort: '12',
          isChecked: '0',
          width: '150',
        },
        {
          unChecked: 'false',
          fieldName: '使用时长',
          column: 'useDuration',
          sort: '13',
          isChecked: '0',
          width: '150',
        },
        {
          unChecked: 'false',
          fieldName: '资产状态',
          column: 'assetStatusName',
          sort: '14',
          isChecked: '0',
          width: '150',
        },
        {
          unChecked: 'false',
          fieldName: '是否危险源',
          column: 'isDangerSource',
          sort: '15',
          isChecked: '0',
          width: '150',
        },
        {
          unChecked: 'false',
          fieldName: '归口部门',
          column: 'centralizedDepartmentName',
          sort: '16',
          isChecked: '0',
          width: '150',
        },
        {
          unChecked: 'true',
          fieldName: '专业类别',
          column: 'professionalCategoryName',
          sort: '17',
          isChecked: '1',
          width: '150',
        },
        {
          unChecked: 'true',
          fieldName: '系统类别',
          column: 'systemCategoryName',
          sort: '18',
          isChecked: '1',
          width: '150',
        },
        {
          unChecked: 'false',
          fieldName: '使用部门',
          column: 'userDepartmentName',
          sort: '19',
          isChecked: '0',
          width: '150',
        },
        {
          unChecked: 'false',
          fieldName: '设备IP',
          column: 'deviceIp',
          sort: '20',
          isChecked: '0',
          width: '150',
        },
        {
          unChecked: 'false',
          fieldName: '上级设备',
          column: 'superiorDeviceName',
          sort: '21',
          isChecked: '0',
          width: '150',
        },
        {
          unChecked: 'true',
          fieldName: '更新时间',
          column: 'updateTime',
          sort: '22',
          isChecked: '1',
          width: '150',
        },
      ],
      selectData: [],
      dialogLoading: false,
      tableLoading: false,
      id: '',
      loginInfo: '',
      userInfo: '',
      loginTable: '',
      configParamsList: [],
      newList: [],
    };
  },
  created() {
    // this.loginInfo = JSON.parse(localStorage.getItem('configParams'))
    this.userInfo = this.$store.state.user.userInfo.user
    if (localStorage.getItem('configParamsList')) {
      this.loginTable = JSON.parse(localStorage.getItem('configParamsList'))
      this.newList = this.loginTable.filter(item => item.id == this.userInfo.staffId)
      if (this.newList.length > 0) {
        this.tableData = this.newList[0].list
      }
    } else {
      this.tableData = this.tableData
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initSort();
    });
    this.getTableCloumn()
  },
  methods: {
    /** 获取列数据 */
    getTableCloumn() {
      this.$nextTick(() => {
        this.tableData.forEach((item) => {
          if (!item.unChecked || item.isChecked === '1') {
            this.$refs.multipleTable.toggleRowSelection(item, true);
          }
        })
      })
    },
    /** table 多选 */
    handleSelectionChange(val) {
      if (val.length > 10) {
        this.$refs.multipleTable.toggleRowSelection(val.pop(), false);
        this.$message.error('最多只能选择10个');
      } else {
        this.selectData = val;
      }
    },
    /** 保存并关闭 */
    handleSaveChange() {
      let params = {
        id: '',
        list: []
      }
      if (!this.selectData.length) {
        this.$message.error('暂无数据可保存！')
        return false
      }
      this.tableData.forEach((item) => {
        if (this.selectData.find((val) => val.fieldName == item.fieldName)) {
          item.isChecked = '1'
        } else {
          item.isChecked = '0'
        }
      })
      if (!localStorage.getItem('configParamsList')) {
        params.id = this.userInfo.staffId
        params.list = this.tableData
        this.configParamsList.push(params)
        localStorage.setItem("configParamsList", JSON.stringify(this.configParamsList))
      } else {
        this.configParamsList = JSON.parse(localStorage.getItem('configParamsList'))
        if (this.newList.length <= 0) {
          params.id = this.userInfo.staffId
          params.list = this.tableData
          this.configParamsList.push(params)
          localStorage.setItem("configParamsList", JSON.stringify(this.configParamsList))
        } else if (this.newList.length > 0) {
          this.configParamsList = this.configParamsList.filter(item => item.id !== this.newList[0].id)
          params.id = this.userInfo.staffId
          params.list = this.tableData
          this.configParamsList.push(params)
          localStorage.setItem("configParamsList", JSON.stringify(this.configParamsList))
        }
      }
      this.handleCloseChange()
    },
    /** 关闭dialog */
    handleCloseChange() {
      this.$emit("init");
      this.$emit("handleCloseChange");
    },
    checkSelectable(val) {
      // if(val.fieldName == '资产名称') {
        return val.sort != '1'
      // }
    },
    /*
    恢复默认
    */
    handleDefault() {
      let params = {
        id: this.userInfo.staffId,
        list: [
          {
            unChecked: 'false',
            fieldName: '资产名称',
            column: 'assetName',
            sort: '1',
            isChecked: '1',
            width: '150',
          },
          {
            unChecked: 'true',
            fieldName: '资产编码',
            column: 'assetCode',
            sort: '2',
            isChecked: '1',
            width: '150',
          },
          {
            unChecked: 'true',
            fieldName: '品牌',
            column: 'assetBrand',
            sort: '3',
            isChecked: '1',
            width: '150',
          },
          {
            unChecked: 'true',
            fieldName: '型号',
            column: 'assetModel',
            sort: '4',
            isChecked: '1',
            width: '150',
          },
          {
            unChecked: 'false',
            fieldName: '生产日期',
            column: 'startDate',
            sort: '5',
            isChecked: '0',
            width: '150',
          },
          {
            unChecked: 'false',
            fieldName: 'SN码',
            column: 'assetSn',
            sort: '6',
            isChecked: '0',
            width: '150',
          },
          {
            unChecked: 'false',
            fieldName: 'NFC',
            column: 'nfcCode',
            sort: '7',
            isChecked: '0',
            width: '150',
          },
          {
            unChecked: 'false',
            fieldName: 'RFID',
            column: 'rfid',
            sort: '8',
            isChecked: '0',
            width: '150',
          },
          {
            unChecked: 'true',
            fieldName: '所在区域',
            column: 'regionReverseName',
            sort: '9',
            isChecked: '1',
            width: '150',
          },
          {
            unChecked: 'false',
            fieldName: '计量单位',
            column: 'unitOfMeasurement',
            sort: '10',
            isChecked: '0',
            width: '150',
          },
          {
            unChecked: 'false',
            fieldName: '启用日期',
            column: 'startDate',
            sort: '11',
            isChecked: '0',
            width: '150',
          },
          {
            unChecked: 'false',
            fieldName: '使用期限',
            column: 'serviceLife',
            sort: '12',
            isChecked: '0',
            width: '150',
          },
          {
            unChecked: 'false',
            fieldName: '使用时长',
            column: 'useDuration',
            sort: '13',
            isChecked: '0',
            width: '150',
          },
          {
            unChecked: 'false',
            fieldName: '资产状态',
            column: 'assetStatusName',
            sort: '14',
            isChecked: '0',
            width: '150',
          },
          {
            unChecked: 'false',
            fieldName: '是否危险源',
            column: 'isDangerSource',
            sort: '15',
            isChecked: '0',
            width: '150',
          },
          {
            unChecked: 'false',
            fieldName: '归口部门',
            column: 'centralizedDepartmentName',
            sort: '16',
            isChecked: '0',
            width: '150',
          },
          {
            unChecked: 'true',
            fieldName: '专业类别',
            column: 'professionalCategoryName',
            sort: '17',
            isChecked: '1',
            width: '150',
          },
          {
            unChecked: 'true',
            fieldName: '系统类别',
            column: 'systemCategoryName',
            sort: '18',
            isChecked: '1',
            width: '150',
          },
          {
            unChecked: 'false',
            fieldName: '使用部门',
            column: 'userDepartmentName',
            sort: '19',
            isChecked: '0',
            width: '150',
          },
          {
            unChecked: 'false',
            fieldName: '设备IP',
            column: 'deviceIp',
            sort: '20',
            isChecked: '0',
            width: '150',
          },
          {
            unChecked: 'false',
            fieldName: '上级设备',
            column: 'superiorDeviceName',
            sort: '21',
            isChecked: '0',
            width: '150',
          },
          {
            unChecked: 'true',
            fieldName: '更新时间',
            column: 'updateTime',
            sort: '22',
            isChecked: '1',
            width: '150',
          },
        ]
      }
      if (localStorage.getItem('configParamsList')) {
        this.loginTable = JSON.parse(localStorage.getItem('configParamsList'))
        this.newList = this.loginTable.filter(item => item.id == this.userInfo.staffId)
        if (this.newList.length > 0) {
          this.configParamsList = this.loginTable.filter(item => item.id !== this.newList[0].id)
          this.configParamsList.push(params)
          localStorage.setItem("configParamsList", JSON.stringify(this.configParamsList))
        }
      } else {
        localStorage.setItem("configParamsList", JSON.stringify(this.tableData))
      }
      this.handleCloseChange()
    },
    /** table 拖动行 排序 */
    initSort() {
      const el = this.$refs.multipleTable.$el.querySelector(
        ".el-table__body-wrapper > table > tbody"
      );
      let _this = this;
      const ops = {
        animation: 200, //动画时长
        handle: ".el-table__row", //可拖拽区域class
        //拖拽中事件
        onMove: ({ dragged, related }) => {
          const oldRow = _this.tableData[dragged.rowIndex]; //旧位置数据
          const newRow = _this.tableData[related.rowIndex]; //被拖拽的新数据
        },
        //拖拽结束事件
        onEnd: (evt) => {
          const curRow = _this.tableData.splice(evt.oldIndex, 1)[0];
          _this.tableData.splice(evt.newIndex, 0, curRow);
        },
      };
      Sortable.create(el, ops);
    },
    /** 输入框输入限制 */
    proving(e) {
      if (e.target.value && e.target.value.length == 1) {
        e.target.value = e.target.value.toString().replace(/[^1-9]/g, ""); //只能输入正整数.replace(/[^1-9]/g, '')
      } else {
        e.target.value = e.target.value.toString().replace(/[^0-9]/g, ""); //只能输入正整数.replace(/[^1-9]/g, '')
      }
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-dialog__body {
  margin: 0px;
}

.table_box {
  height: 450px;
}

.tableTips {
  margin-bottom: 15px;
  display: flex;
  align-items: center;

  span:first-child {
    color: #108ee9;
    font-size: 16px;
  }

  span:last-child {
    margin-left: 5px;
    font-weight: bold;
  }
}

::v-deep .el-dialog__footer {
  position: static;
}
</style>