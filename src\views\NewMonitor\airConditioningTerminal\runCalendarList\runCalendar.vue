<template>
  <PageContainer title="运行日历" type="list" :isClose="false"
    style="background-color: rgb(245, 246, 251); overflow: hidden;">
    <template slot="content">
      <div class="runCalendar">
        <div class="top" style="display: flex;">
          <div class="toptip">
            <span class="green_line"></span>
            运行日历
          </div>
          <el-radio-group v-model="selectTabType" style="margin: auto 30px;" size="mini">
            <el-radio-button label="month">月</el-radio-button>
            <el-radio-button label="year">年</el-radio-button>
          </el-radio-group>
          <div class="top_year">
            <div class="user_select_none">
              <i class="el-icon-arrow-left" @click="changeDateData('-', 'year')"></i>
              <span>{{ yearData }}年</span>
              <i class="el-icon-arrow-right" @click="changeDateData('+', 'year')"></i>
            </div>
          </div>
          <div v-if="!selectDateUpdata" class="top_btn">
            <el-button type="primary" @click="planConfig">表格查看</el-button>
          </div>
          <div v-if="selectDateUpdata" class="top_btn">
            <el-button type="primary" @click="planConfig">日历查看</el-button>
          </div>
        </div>
        <div v-loading="isLoading" class="content">
          <div v-if="!selectDateUpdata">
            <div v-if="selectTabType === 'month'" class="content-left">
              <div class="change_month">
                <i class="el-icon-arrow-left" @click="changeDateData('-', 'month')"></i>
                <span>{{ monthData }} 月</span>
                <i class="el-icon-arrow-right" @click="changeDateData('+', 'month')"></i>
              </div>
              <calendarUpdate v-model="calendarDate">
                <template slot="dateCell" slot-scope="{ data }">
                  <div class="calendar_content" @click="clickDay(data.day)" :style="{
                    'background-color': Object.values(dataList).some(item =>
                      item.date === data.day) ? '#F2F8FE' : ''
                  }">
                    <el-popover placement="left" width="180" trigger="click" popper-class="popperClass" ref="popoverA"
                      :v-model="popoverVisible">
                      <template #default>
                        <div v-if="dataList.sceneNames && dataList.sceneNames.length > 0">
                          <p>{{ dataList.date }}</p>
                          <div v-for="( obj, i ) in dataList.sceneNames ">
                            <p>{{ obj }}</p>
                          </div>
                          <span class="calendar_content_span" @click="viewDetails(data.day)"
                            style="display: block; border-top: 1px solid #ddd;color: #121f3e;padding-top: 15px;cursor: pointer">
                            查看详情
                          </span>
                        </div>
                        <div v-else>
                          暂无数据
                        </div>
                      </template>
                      <template #reference>
                        <p class="calendar_content_date">
                          {{ Number(data.day.split('-')[2]) }}
                        </p>
                      </template>
                    </el-popover>
                  </div>
                </template>
              </calendarUpdate>
            </div>
            <div v-else class="content-left content-left_year">
              <div v-for="(  item, index  ) in 12  " :key="index">
                <calendarUpdate v-model="calendarYearDate[index]" showModule="year">
                  <template slot="dateCell" slot-scope="{ data }">
                    <div class="calendar_content_year user_select_none" @click="clickDay(data.day)" :style="{
                      'background-color': Object.values(dataList).some(item => item.date === data.day) ? '#F2F8FE' : '',
                      border: Object.values(dataList).some(item => item.date === data.day) ? '1px solid #5188FC' : 'none'
                    }
                      ">
                      <el-popover placement="left" width="180" trigger="click" popper-class="popperClass"
                        :ref="'popperClass-' + index" :value="popoverVisibleList[index]">
                        <template #default>
                          <div v-if="dataList.sceneNames && dataList.sceneNames.length > 0">
                            <div v-for="( obj, i ) in dataList.sceneNames ">
                              <p>{{ obj }}</p>
                            </div>
                            <span class="calendar_content_span" @click="viewDetails(data.day, index)"
                              style="display: block; border-top: 1px solid #ddd;color: #121f3e;padding-top: 15px;cursor: pointer">
                              查看详情
                            </span>
                          </div>
                          <div v-else>
                            暂无数据
                          </div>
                        </template>
                        <template #reference>
                          <p class="calendar_content_date">
                            {{ Number(data.day.split('-')[2]) }}
                          </p>
                        </template>
                      </el-popover>
                    </div>
                  </template>
                </calendarUpdate>
              </div>
            </div>
          </div>
          <div v-else class="table-box">
            <div class="tableContainer">
              <div class="change_month" v-if="selectTabType == 'month'">
                <i class="el-icon-arrow-left" @click="changeDateData('-', 'month')"></i>
                <span>{{ monthData }} 月</span>
                <i class="el-icon-arrow-right" @click="changeDateData('+', 'month')"></i>
              </div>
              <el-table v-loading="tableLoading" height="calc(100% - 96px)" :data="tableData" border stripe>
                <el-table-column label="日期" prop="jobExecutionTime" show-overflow-tooltip
                  align="center"></el-table-column>
                <el-table-column label="操作" show-overflow-tooltip align="center">
                  <template slot-scope="scope">
                    <el-button type="text" @click="viewDetails(scope.row.jobExecutionTime)">查看详情</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <el-pagination :current-page="pagination.page" :page-sizes="[15, 30, 50, 100]"
                :page-size="pagination.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="pageTotal"
                @size-change="handleSizeChange" @current-change="handleCurrentChange">
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
    </template>
  </PageContainer>
</template>
<script>
import moment from 'moment'
import calendarUpdate from './calendarUpdate'
import Dict from './dict.js'
export default {
  name: 'runCalendar',
  components: {
    calendarUpdate
  },
  props: {
    systemCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isLoading: false,
      dataList: {},
      popoverVisible: false,
      popoverVisibleList: new Array(12).fill(false),
      tableData: [],
      pagination: {
        pageSize: 15,
        page: 1
      },
      pageTotal: 0,
      selectTabType: 'month',
      yearData: moment().format('YYYY'),
      monthData: moment().format('M'),
      calendarYearDate: [],
      selectDateList: [], // 选中日期需要改变模式的数组
      selectDateUpdata: false, // 是否选中计划配置
      dataForm: {
        jjDate: [],
        checkDateList: [],
        radioDate: ''
      },
      checkDateList: [], // 选中日期类型
    }
  },
  computed: {
    expireTimeOption() {
      return {
        disabledDate(time) {
          return time.getTime() < Number(moment().format('x'))
        }
      }
    },
    calendarDate() {
      return this.yearData + '-' + this.monthData + '-01'
    },

  },
  // watch监听yearData改变
  watch: {
    yearData(val) {
      this.calendarYearDate = []
      for (let i = 1; i <= 12; i++) {
        let index = i <= 9 ? '0' + i : i
        this.calendarYearDate.push(val + '-' + index + '-01')
      }
      this.dataForm.jjDate = [
        val == moment().format('YYYY') ? moment().add(1, 'd').format('YYYY-MM-DD') : moment(val).startOf('year').format('YYYY') + '-01-01',
        moment(val).endOf('year').format('YYYY-MM-DD')
      ]
      console.log(val, 'yearData');
      if (this.selectDateUpdata) {
        this.getTableData()
      } else {
        this.getSceneNamesByDate('init', moment().format('YYYY-MM-DD'))
      }

    },
    selectTabType(val) {
      console.log(val, 'selectTabType');
      if (this.selectDateUpdata) {
        this.getTableData()
      } else {
        this.getSceneNamesByDate('init', moment().format('YYYY-MM-DD'))
      }

    },
    monthData(val) {
      if (this.selectDateUpdata) {
        this.getTableData()
      } else {
        this.getSceneNamesByDate('init', moment().format('YYYY-MM-DD'))
      }
    }
  },
  created() {
    var that = this
    for (let i = 1; i <= 12; i++) {
      let index = i <= 9 ? '0' + i : i
      this.calendarYearDate.push(this.yearData + '-' + index + '-01')
    }
    this.dataForm.jjDate = [that.yearData + moment().add(1, 'd').format('-MM-DD'), moment(that.yearData).endOf('year').format('YYYY-MM-DD')]
    this.getSceneNamesByDate('init', moment().format('YYYY-MM-DD'))
  },
  mounted() {
    console.log(this.popoverVisibleList);
  },
  methods: {
    clickDay(data) {
      this.popoverVisible = true
      this.getSceneNamesByDate('init', data);
    },
    viewDetails(date, index) {
      document.body.click()
      this.popoverVisible = false
      let path = `${this.$route.meta.jumpAddress}/calendarDetails`
      this.$router.push({
        path: path,
        query: {
          date: date,
          systemCode: this.systemCode,
        }
      })
    },
    // 获取日历列表名称
    getSceneNamesByDate(type, date) {
      this.isLoading = true
      let params = {
        date: date
      }
      if (type == 'init') this.dataList = []
      this.$api.getSceneNamesByDate(params).then(res => {
        this.isLoading = false
        if (res.code === '200') {
          this.dataList = res.data
        }
      })
    },
    // 获取表格列表数据
    getTableData() {
      this.tableLoading = true
      let params =
        this.selectTabType == 'year'
          ? {
            year: this.yearData
          }
          : {
            year: this.yearData,
            month: this.monthData < 10 ? '0' + this.monthData : '' + this.monthData,
          }
      params['systemCode'] = this.systemCode
      let obj = {
        ...this.pagination,
        ...params
      }
      this.tableData = []
      this.$api
        .getMonthlyExecutionHistory(obj)
        .then((res) => {
          this.tableLoading = false
          if (res.code == 200) {
            this.tableData = res.data ? res.data.records : []
            this.pageTotal = res.data ? res.data.total : 0
          } else if (res.message) {
            this.tableData = []
            this.pagination.total = 0
            this.$message.error(res.message)
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    // 改变年份
    changeDateData(type, dateType) {
      if (type === '+') {
        if (dateType == 'month' && this.monthData == 12) {
          this.monthData = 1
          this.yearData = Number(this.yearData) + 1 + ''
        } else if (dateType == 'year') {
          this.yearData = Number(this.yearData) + 1 + ''
          this.calendarYearDate = this.calendarYearDate.map((item) => {
            return moment(item).add(1, 'year').format('YYYY-MM-DD')
          })
        } else {
          this.monthData = Number(this.monthData) + 1
        }
      } else {
        if (dateType == 'month' && this.monthData == 1) {
          this.monthData = 12
          this.yearData = Number(this.yearData) - 1 + ''
        } else if (dateType == 'year') {
          this.yearData = Number(this.yearData) - 1 + ''
          this.calendarYearDate = this.calendarYearDate.map((item) => {
            return moment(item).subtract(1, 'year').format('YYYY-MM-DD')
          })
        } else {
          this.monthData = Number(this.monthData) - 1
        }
      }
    },

    // 计划配置改变时间区间
    changejjDateData() {
      this.selectDateList = []
      this.dataForm.checkDateList = []
    },
    selectDayDate(day) {
      console.log('选择年的点击');
      // moment日期比较 只能配置当前以后的日期
      if (!this.selectDateUpdata || moment(day).isBefore(moment())) return
      if (this.selectDateList.includes(day)) {
        this.selectDateList.splice(this.selectDateList.indexOf(day), 1)
      } else {
        this.selectDateList.push(day)
      }
    },
    // 表格
    planConfig() {
      this.selectDateUpdata == false ? this.selectDateUpdata = true : this.selectDateUpdata = false
      this.getTableData()
    },


    handleCurrentChange(val) {
      this.pagination.page = val
      this.getTableData()
    },
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.pagination.page = 1
      this.getTableData()
    },
  }
}
</script>
<style src="@/assets/styles/sinomis-ui.css" scoped></style>
<style lang="scss" type="text/css" scoped>
.runCalendar {
  width: 100%;
  height: 100%;

  .top {
    border-radius: 10px;
    height: 48px;
    line-height: 48px;
    background-color: #fff;
    justify-content: space-between;

    .toptip {
      border-bottom: none;
      width: auto;
      font-size: 16px;
      font-weight: 400;
    }

    .top_year {
      flex: 1;

      >div {
        display: flex;
        color: #595959;

        i {
          display: inline-block;
          width: 32px;
          height: 32px;
          line-height: 32px;
          margin: auto 0;
          text-align: center;
          cursor: pointer;

          &:hover {
            background-color: #eeeef0;
            border-radius: 50%;
          }
        }

        span {
          margin: 0 10px;
        }
      }
    }

    .top_btn {
      margin: auto 0;
      margin-right: 20px;
    }
  }

  .content {
    height: calc(100% - 66px);
    margin-top: 10px;
    display: flex;

    .content-left {
      width: 100%;
      height: 100%;
      background-color: #fff;
      border-radius: 10px;
      padding: 10px;
      display: block;

      .calendar_content {
        width: 100%;
        height: 100%;
        padding: 8px;
        box-sizing: border-box;
        position: relative;

        .el-popover__reference {
          width: 100%;
          height: 100%;
          text-align: center;
        }

        .calendar_content_date {
          text-align: right;
          // color: #121f3e;
          font-size: 18px;
          font-family: DIN-Medium, DIN;
          width: 100%;
          height: 100%;
        }

        .calendar_content_image {
          width: 30px;
          height: 30px;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);

          img {
            width: 100%;
            height: 100%;
          }
        }

        .time_box {
          width: calc(100% - 16px);
          display: flex;
          position: absolute;
          transform: translateY(-50%);

          i {
            margin: 0 6px;
          }

          div {
            flex: 1;
            border-top: 1px dashed #99a2b1;
            margin: auto;
          }
        }
      }
    }

    .content-left_year {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-around;
      overflow: scroll;

      >div {
        width: 24%;
      }

      .calendar_content_year {
        height: 36px;
      }

      .calendar_content_date {
        font-size: 14px;
        font-family: DIN-Medium, DIN;
        position: relative;

        .holiday {
          position: absolute;
          top: 2px;
          right: 2px;
          color: #ff4848;
          font-size: 10px;
          line-height: 10px;
          transform: scale(0.83);
        }
      }
    }

    .content-right {
      width: calc(18% - 10px);
      margin-left: 10px;
      background-color: #fff;
      border-radius: 10px;
      padding: 20px 15px;

      .icon_explain {
        padding: 10px;

        .icon_explain_title {
          font-size: 16px;
          font-weight: 600;
          color: #121f3e;
          height: 30px;
          line-height: 30px;
        }

        .icon_explain_content {
          padding-left: 20px;
          display: flex;
          height: 30px;
          line-height: 30px;

          i {
            width: 14px;
            height: 14px;
            margin: auto 0;
          }

          span {
            font-size: 14px;
            color: #595959;
            margin-left: 10px;
          }
        }

        .el-date-editor .el-range-separator {
          width: auto;
        }

        .left_right_structure {
          display: flex;
          margin: 5px 0;

          .left_structure {
            width: 35%;
            margin-top: 5px;
          }

          .right_structure {
            width: 65%;

            .el-checkbox,
            .el-radio {
              display: block;
              margin: 5px;
            }
          }
        }
      }
    }
  }

  .user_select_none {
    -moz-user-select: none;
    /* 火狐 */
    -webkit-user-select: none;
    /* webkit浏览器 */
    -ms-user-select: none;
    /* IE10 */
    -khtml-user-select: none;
    /* 早期浏览器 */
    user-select: none;
  }
}

.change_month {
  text-align: center;
  height: 40px;
  width: 100%;

  i {
    display: inline-block;
    width: 64px;
    height: 32px;
    line-height: 32px;
    margin: auto 0;
    font-size: 16px;
    text-align: center;
    cursor: pointer;
    background: #f1f6ff;
    border: 1px solid #5188fc;

    &:hover {
      color: #5b8afd;
      background-color: #f1f6ff;
    }
  }

  i:first-child {
    border-radius: 4px 0 0 4px;
  }

  i:last-child {
    border-radius: 0 4px 4px 0;
  }

  span {
    margin: auto 50px;
    color: #121f3e;
    font-weight: 500;
  }
}

.el-table {
  margin-bottom: 12px;
}

::v-deep .el-table__row:hover {
  cursor: pointer;
}

.table-box {
  height: 100%;
  padding: 16px;
  width: 100%;
  background: #fff;

  .tableContainer {
    height: 100%;

    .el-table {
      height: calc(100% - 96px) !important;
    }
  }
}
</style>
