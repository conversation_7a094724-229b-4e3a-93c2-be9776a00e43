<template>
  <PageContainer style="position: relative">
    <div slot="content" class="whole">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="待审核" name="0"> </el-tab-pane>
        <el-tab-pane label="全部" name="1"></el-tab-pane>
        <el-tab-pane label="审核配置" name="2"></el-tab-pane>
      </el-tabs>
      <div v-if="activeName == 0 || activeName == 1" style="height: 100%">
        <div style="display: flex">
          <el-input v-model="filters.keywords" placeholder="房间编号/名称" style="width: 200px"></el-input>&nbsp;
          <el-select placeholder="全部状态" v-if="activeName == 1" v-model="filters.status">
            <el-option v-for="item in statusList" :key="item.value" :value="item.value" :label="item.label"></el-option> </el-select
          >&nbsp;
          <el-button type="primary" @click="inquiry">查询</el-button>
          <el-button type="primary" plain @click="rest">重置</el-button>
        </div>
        <div slot="content" style="height: 100%">
          <div class="contentTable">
            <div class="contentTable-main table-content">
              <el-table :data="tableData" stripe border height="calc(100% - 20px)" v-loading="loading">
                <el-table-column prop="approvalNumber" label="编号" show-overflow-tooltip></el-table-column>
                <el-table-column prop="ssmName" width="150" label="房间名称" show-overflow-tooltip></el-table-column>
                <el-table-column prop="roomCode" width="150" label="房间号" show-overflow-tooltip></el-table-column>
                <el-table-column prop="simName" label="位置" show-overflow-tooltip></el-table-column>
                <el-table-column prop="dmName" width="100" label="归属科室" show-overflow-tooltip></el-table-column>
                <el-table-column prop="initiator" width="100" label="发起人" show-overflow-tooltip></el-table-column>
                <el-table-column prop="initiationTime" label="发起时间" show-overflow-tooltip></el-table-column>
                <el-table-column prop="statusName" width="150" label="状态" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <div class="state">
                      <span :class="['approveStatus', `approveStatus${scope.row.status}`]">{{ scope.row.statusName }}</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="" width="150" label="操作" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span style="cursor: pointer; padding: 0 5px; color: #3562db" @click="lookOver(scope.row)">查看</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <div class="contentTable-footer">
            <el-pagination
              :current-page="pagination.current"
              :page-sizes="[15, 30, 50, 100]"
              :page-size="pagination.size"
              :total="total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            ></el-pagination>
          </div>
        </div>
      </div>
      <div v-else>
        <div class="configure">
          <div class="title">管理员审批 <span class="unseal" v-if="approveRadio == 1">开启</span><span class="shut" v-else>关闭</span></div>
          <div class="txt">开启后,提交的单据需要管理员审核通过后设施数据才会更新</div>
          <div class="btn">
            <div class="txt">关闭后,提交的单据将会自动通过</div>
            <el-button type="primary" @click="dialogApproveVisible = true">设置</el-button>
          </div>
        </div>
      </div>
      <el-drawer :visible.sync="drawer" :with-header="false" :wrapperClosable="false">
        <div class="drawer">
          <div class="drawer_title" @click="closeDrawer"><i class="el-icon-arrow-left"></i>{{ roomDetails.dmName }}</div>
          <div><svg-icon name="right-arrow" />基本信息</div>
          <div class="roomDetails">
            <div class="txt">
              <div class="box">
                <div class="box_l">房间名称：</div>
                <div class="box_r">{{ roomDetails.ssmName }}</div>
              </div>
              <div class="box">
                <div class="box_l">房间号：</div>
                <div class="box_r">{{ roomDetails.roomCode }}</div>
              </div>
            </div>
            <div class="txt">
              <div class="box">
                <div class="box_l">位置：</div>
                <div class="box_r">{{ roomDetails.simName }}</div>
              </div>
              <div class="box">
                <div class="box_l">归属科室：</div>
                <div class="box_r">{{ roomDetails.dmName }}</div>
              </div>
            </div>
            <div class="txt">
              <div class="box">
                <div class="box_l">发起人：</div>
                <div class="box_r">{{ roomDetails.initiator }}</div>
              </div>
              <div class="box">
                <div class="box_l">发起人科室：</div>
                <div class="box_r">{{ roomDetails.initiatorDepartment }}</div>
              </div>
            </div>
            <div class="txt">
              <div class="box">
                <div class="box_l">发起时间：</div>
                <div class="box_r">{{ roomDetails.initiationTime }}</div>
              </div>
              <div class="box">
                <div class="box_l">状态：</div>
                <div class="state box_r">
                  <span :class="['approveStatus', `approveStatus${roomDetails.status}`]">{{ roomDetails.statusName }}</span>
                </div>
              </div>
            </div>
          </div>
          <div><svg-icon name="right-arrow" />已知设备</div>
          <el-table :data="tableData2" border style="width: 100%; margin-top: 10px; height: 250px" :height="tableHeight">
            <el-table-column prop="facilityTypeName" label="设施类型" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="quantityBefore" label="变更前" width="120"> </el-table-column>
            <el-table-column prop="quantityAfter" label="变更后" width="120"> </el-table-column>
          </el-table>
          <el-pagination
            :current-page="pagination2.current"
            :page-sizes="[15, 30, 50, 100]"
            :page-size="pagination2.size"
            :total="total2"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange2"
            @current-change="handleCurrentChange2"
          ></el-pagination>
          <div style="margin-top: 12px"><svg-icon name="right-arrow" />未分类设备</div>
          <el-table :data="tableData3" border style="width: 100%; margin-top: 10px; height: 250px" :height="tableHeight">
            <el-table-column prop="deviceQuantity" label="数量"> </el-table-column>
            <el-table-column prop="deviceDescription" label="描述" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="primaryFacilityTypeName" label="设施类型"> </el-table-column>
            <el-table-column prop="" label="图片">
              <template slot-scope="scope">
                <div>
                  <span class="common" @click="toView(scope.row.deviceImage)">查看({{ deviceImage(scope.row.deviceImage) }})</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="" label="操作">
              <template slot-scope="scope">
                <div v-if="roomDetails.status == '0'">
                  <span v-if="scope.row.primaryFacilityTypeId == -1" class="common" @click="specifiesType(scope.row)">指定类型</span>
                  <span v-else>已指定</span>
                </div>
              </template>
            </el-table-column>
          </el-table>

          <div>
            <div style="display: flex" class="btn">
              <el-button type="primary" plain @click="closeDrawer">关闭</el-button>
              <el-button type="primary" v-if="roomDetails.status == '0'" @click="overruleThrough(2)">驳回</el-button>
              <el-button type="primary" v-if="roomDetails.status == '0'" @click="overruleThrough(1)">通过</el-button>
            </div>
          </div>
        </div>
      </el-drawer>
      <el-dialog :visible.sync="dialogApproveVisible" title="设置" width="400px" custom-class="model-dialog">
        <div class="diaContent" style="padding: 40px 10px; background-color: #fff; width: 100%; text-align: center">
          管理员审批：<el-radio-group v-model="switchFlag">
            <el-radio :label="1">开启</el-radio>
            <el-radio :label="0">关闭</el-radio>
          </el-radio-group>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="dialogApproveVisible = false">取 消</el-button>
          <el-button type="primary" @click="saveConfig">确 定</el-button>
        </span>
      </el-dialog>
      <el-dialog :visible.sync="dialogSpecifiesTypeVisible" title="选择设施类型" width="400px" custom-class="model-dialog">
        <div class="diaContent" style="padding: 40px 10px; background-color: #fff; width: 100%; text-align: center">
          设施类型： <el-cascader :options="dictTypeList" :props="cascaderProps" :show-all-levels="false" @change="handleChange" v-model="selectedPath"></el-cascader>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="dialogSpecifiesTypeVisible = false">取 消</el-button>
          <el-button type="primary" @click="specifiedType">确 定</el-button>
        </span>
      </el-dialog>
      <imgCarousel :dialogVisibleImg="dialogVisibleImg" :imgArr="imgArr" @closeDialog="closeDialogImg"></imgCarousel>
    </div>
  </PageContainer>
</template>

<script>
import imgCarousel from '../../spaceManage/common/imgCarousel.vue'
import tableListMixin from '@/mixins/tableListMixin.js'
import { transData } from '@/util'
export default {
  mixins: [tableListMixin],
  components: {
    imgCarousel
  },
  data() {
    return {
      dialogVisibleImg: false,
      imgArr: [],

      dictTypeList: [],
      dictTypeList2: [],
      loading: false,
      approveRadio: '',
      switchFlag: '',
      switchFlagId: '',
      dialogApproveVisible: false,
      dialogSpecifiesTypeVisible: false,
      pagination: {
        current: 1,
        size: 15
      }, // 分页数据
      total: 0, // 数据总条数
      pagination2: {
        current: 1,
        size: 15
      }, // 分页数据
      total2: 0, // 数据总条数
      roomDetails: '',
      drawer: false,
      activeName: '0',
      tableData2: [],
      tableData3: [],

      tableData: [],
      filters: {
        keywords: '',
        status: '0'
      },
      statusList: [
        {
          value: '0',
          label: '待审核'
        },
        {
          value: '1',
          label: '已通过'
        },
        {
          value: '2',
          label: '已驳回'
        },

        {
          value: '3',
          label: '已通过(自动通过)'
        }
      ],
      cascaderProps: {
        label: 'meetingDictName',
        value: 'meetingDictCode',
        children: 'children',
        emitPath: true
      },
      selectedPath: [],
      specifiesTypeId: '',
      deviceQuantity:'',
      params: {}
    }
  },
  mounted() {
    this.getTableList()
  },
  methods: {
    toView(img) {
      if (img) {
        this.dialogVisibleImg = true
        let imgArr = img.split(',')
        imgArr.forEach((item) => {
          item = this.$tools.imgUrlTranslation(item)
          this.imgArr.push(item)
        })
      } else {
        this.$message.error('暂无图片')
      }
    },
    deviceImage(img) {
      if (img) {
        let imgArr = img.split(',')
        return imgArr.length
      } else {
        return 0
      }
    },
    closeDialogImg() {
      this.dialogVisibleImg = false
      this.imgArr = []
    },
    //2驳回，1通过
    overruleThrough(status) {
      let params = {
        isApproved: status,
        spaceId: this.roomDetails.id,
        submissionRecordId: this.roomDetails.registrationId
      }
      if (status == 2) {
        this.$confirm('驳回后设施将不会被更新', {
          confirmButtonText: '驳回',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.$api.reviewDeviceSubmission(params).then((res) => {
              if (res.code == 200) {
                this.$message({ message: '保存成功', type: 'success' })
                this.drawer = false
                this.inquiry()
              } else {
                this.$message({ message: res.msg, type: 'error' })
              }
            })
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'
            })
          })
      } else {
        if (this.tableData3.length > 0) {
          this.$confirm('存在未指定类型设备，未指定类型的设施将不会被更新', {
            confirmButtonText: '继续提交',
            cancelButtonText: '返回指定',
            type: 'warning'
          })
            .then(() => {
              this.$api.reviewDeviceSubmission(params).then((res) => {
                if (res.code == 200) {
                  this.$message({ message: '保存成功', type: 'success' })
                  this.drawer = false
                  this.inquiry()
                } else {
                  this.$message({ message: res.msg, type: 'error' })
                }
              })
            })
            .catch(() => {})
        } else {
          this.$api.reviewDeviceSubmission(params).then((res) => {
            if (res.code == 200) {
              this.$message({ message: '保存成功', type: 'success' })
              this.drawer = false
              this.inquiry()
            } else {
              this.$message({ message: res.msg, type: 'error' })
            }
          })
        }
      }
    },
    specifiedType() {
      this.$confirm('确认指定类型并添加到已知设备?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$api.specifiedType(this.params).then((res) => {
            if (res.code == 200) {
              this.$message({ message: '保存成功', type: 'success' })
              this.dialogSpecifiesTypeVisible = false
              this.getUnclassifiedFacilities(this.roomDetails)
              this.getTableList2(this.roomDetails)
              this.selectedPath = []
            } else {
              this.$message({ message: res.msg, type: 'error' })
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          })
        })
    },
    handleChange(path) {
      this.params = {
        spaceId: this.roomDetails.id,
        submissionRecordId: this.roomDetails.registrationId,
        submissionDeviceId: this.specifiesTypeId,
         deviceQuantity: this.deviceQuantity,
        deptId: this.$store.state.user.userInfo.user.deptId,
        spaceId: this.roomDetails.id,
        userId: this.$store.state.user.userInfo.user.staffId
      }

      this.params.primaryFacilityTypeId = path[0] + '/' + path[1]
      const primaryFacilityTypeName = this.dictTypeList2.find((item) => item.meetingDictCode === path[0])
      const primaryFacilityTypeName2 = this.dictTypeList2.find((item) => item.meetingDictCode === path[1])
      this.params.primaryFacilityTypeName = primaryFacilityTypeName.meetingDictName + '/' + primaryFacilityTypeName2.meetingDictName
      this.params.secondaryFacilityTypeId = path[2]
      const secondaryFacilityTypeName = this.dictTypeList2.find((item) => item.meetingDictCode === path[2])
      this.params.secondaryFacilityTypeName = secondaryFacilityTypeName.meetingDictName
    },
    getDictList() {
      const params = {
        dictType: 'facility',
        status: '0',
        page: {
          current: 1,
          size: 99999,
          total: 0
        }
      }
      this.$api.meetingDictGetList(params).then((res) => {
        if (res.code == '200') {
          this.processOptions(transData(res.data.list, 'id', 'parentId', 'children'))
          this.dictTypeList2 = res.data.list
        } else {
          this.$message.error('获取字典列表失败！')
        }
      })
    },

    //只保留三级数据
    processOptions(options) {
      this.dictTypeList = options
        .filter((parent) => {
          return parent.children && parent.children.some((child) => child.children && child.children.length > 0)
        })
        .map((parent) => ({
          ...parent,
          children: parent.children
            .filter((child) => {
              return child.children && child.children.length > 0
            })
            .map((child) => ({
              ...child,

              children: child.children
            }))
        }))
    },

    //指定类型
    specifiesType(row) {
      this.specifiesTypeId = row.id
      this.deviceQuantity=row.deviceQuantity
      this.dialogSpecifiesTypeVisible = true
      this.getDictList()
    },
    getUnclassifiedFacilities(row) {
      let params = {
        spaceId: row.id,
        submissionRecordId: row.registrationId
      }
      this.$api.getUnclassifiedFacilities(params).then((res) => {
        if (res.code == 200) {
          this.tableData3 = res.data
        }
      })
    },
    getTableList2(row) {
      let params = {
        currentPage: this.pagination2.current,
        pageSize: this.pagination2.size,
        spaceId: row.id,
        submissionRecordId: row.registrationId
      }
      this.$api.getSpaceFacilityRecordList(params).then((res) => {
        if (res.code == 200) {
          this.tableData2 = res.data.records
          this.total2 = res.data.total
        }
      })
    },
    getTableList() {
      this.loading = true
      let params = {
        currentPage: this.pagination.current,
        pageSize: this.pagination.size,
        status: this.filters.status,
        keywords: this.filters.keywords
      }
      this.$api.getSpaceFacilityRegistrationList(params).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.records
          this.total = res.data.total
        }
        this.loading = false
      })
    },
    getSpaceFacilityConfig() {
      this.$api.getSpaceFacilityConfig().then((res) => {
        if (res.code == 200 && res.data) {
          this.approveRadio = res.data.switchFlag || 0
          this.switchFlag = res.data.switchFlag || 0
          this.switchFlagId = res.data.id
        } else {
          this.approveRadio = 0
          this.switchFlag = 0
        }
      })
    },
    saveConfig() {
      this.$api.saveConfig({ switchFlag: this.switchFlag, id: this.switchFlagId }).then((res) => {
        if (res.code == 200) {
          this.$message({ message: '保存成功', type: 'success' })
          this.dialogApproveVisible = false
          this.getSpaceFacilityConfig()
        } else {
          this.$message({ message: res.msg, type: 'error' })
        }
      })
    },
    closeDrawer() {
      this.drawer = false
    },
    lookOver(row) {
      this.drawer = true
      this.getTableList2(row)
      this.getUnclassifiedFacilities(row)
      this.roomDetails = row
    },
    handleClick(tab, event) {
      if (tab.name == 0) {
        this.filters.keywords = ''
        this.filters.status = '0'
        this.getTableList()
      } else if (tab.name == 1) {
        this.filters.keywords = ''
        this.filters.status = ''
        this.getTableList()
      } else if (tab.name == 2) {
        this.getSpaceFacilityConfig()
      }
    },
    inquiry() {
      this.pagination.current = 1
      this.getTableList()
    },
    rest() {
      this.filters.keywords = ''
      this.filters.status = ''
      this.pagination.current = 1
      this.getTableList()
    },
    handleSizeChange(val) {
      this.pagination.size = val
      this.getTableList()
    },
    handleCurrentChange(val) {
      this.pagination.current = val
      this.getTableList()
    },
    handleSizeChange2(val) {
      this.pagination2.size = val
      this.getTableList2(this.roomDetails)
    },
    handleCurrentChange2(val) {
      this.pagination2.current = val
      this.getTableList2(this.roomDetails)
    }
  }
}
</script>

<style  lang="scss" scoped>
.whole {
  width: 100%;
  height: 100%;
  background-color: #fff;
  padding: 0 15px;
  .contentTable {
    height: calc(100% - 138px);
    background: #fff;
    border-radius: 4px;
    padding: 16px 0 0;
    display: flex;
    flex-direction: column;
    // overflow: auto;
    .contentTable-main {
      flex: 1;
      overflow: auto;
    }
    .contentTable-footer {
      padding: 10px 0 0;
    }
  }
}
.el-tab-pane {
  padding: 10px 0;
}
.drawer {
  height: 100%;
  position: relative;
  padding: 10px;
  .drawer_title {
    line-height: 30px;
    cursor: pointer;
  }
  .drawer_title:hover {
    color: #3562db;
  }
  .btn {
    position: absolute;
    right: 5%;
    bottom: 1%;
  }
}
.roomDetails {
  .txt {
    width: 100%;
    display: flex;
    > .box {
      display: flex;
      line-height: 35px;
      width: 50%;
      > div {
        width: 50%;
      }
      .box_l {
        width: 40%;
        text-align: right;
        color: #96989a;
      }
      .box_r {
        width: 60%;
        text-align: left;
        color: #333333;
      }
    }
  }
}
.configure {
  width: 100%;
  height: 130px;
  border: 1px solid #e4e7ed;
  border-radius: 10px;
  padding: 15px;
  .title {
    margin-bottom: 10px;
    font-size: 18px;
    font-weight: bold;
    .unseal {
      font-size: 14px;
      display: inline-block;
      font-weight: 300;
      padding: 0 5px;
      background-color: #e8ffea;
      color: #00b42a;
      border-radius: 5px;
    }
    .shut {
      font-size: 14px;
      display: inline-block;
      font-weight: 300;
      padding: 0 5px;
      border-radius: 5px;

      background-color: #e5e6eb;
      color: #96989a;
    }
  }
  .txt {
    line-height: 30px;
    font-size: 14px;
  }
  .btn {
    display: flex;
    justify-content: space-between;
  }
}
.state {
  .approveStatus {
    padding: 5px;
    border-radius: 4px;
  }
  .approveStatus0 {
    color: #3562db;
  }
  .approveStatus1 {
    color: #00b42a;
  }
  .approveStatus2 {
    color: #f53f3f;
  }
  .approveStatus3 {
    color: #00b42a;
  }
}
.common {
  color: #3562db;
  cursor: pointer;
}
</style>
