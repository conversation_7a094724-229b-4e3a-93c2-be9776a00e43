<template>
  <PageContainer v-loading="contentLoading">
    <div slot="header" class="table-content">
      <div class="topFilter">
        <div class="backBar">
          <span style="cursor: pointer" @click="$router.go(-1)">
            <i class="el-icon-arrow-left"></i>
            培训详情
          </span>
        </div>
      </div>
      <div class="tabsList">
        <el-tabs @tab-click="handleClick">
          <el-tab-pane v-for="(item, index) in tabsList" :key="index" :label="item.name"
            :class="{ active: activeIndex == index }"></el-tab-pane>
        </el-tabs>
        <el-button v-if="activeIndex == '1'" type="primary" class="btn" :disabled="multipleSelection.length < 1"
          @click="exportClickExport">导出</el-button>
      </div>
      <!-- 培训信息 -->
      <div class="baseInfo" v-if="activeIndex == 0">
        <h1>基础信息</h1>
        <div class="contenter">
          <div class="itemInfo">
            <div class="title">培训名称：</div>
            <div class="value">{{ dataList.name }}</div>
          </div>
          <div class="itemInfo">
            <div class="title">培训类型：</div>
            <div class="value">{{ dataList.subjectName }}</div>
          </div>
          <div class="itemInfo">
            <div class="title">所属单位：</div>
            <div class="value">{{ dataList.deptName }}</div>
          </div>
          <div class="itemInfo">
            <div class="title">培训老师:</div>
            <div class="value">{{ dataList.teacherName }}</div>
          </div>
          <div class="itemInfo">
            <div class="title">培训时间：</div>
            <div class="value">{{ dataList.trainStartTime }}-{{ dataList.trainEndTime }}</div>
          </div>
          <div class="itemInfo">
            <div class="title" style="width: 100px;">培训方式：</div>
            <div class="value">{{ dataList.type == 0 ? '线上会议' : '线下会议' }}</div>
          </div>
          <div class="itemInfo">
            <div class="title">培训地址：</div>
            <div class="value">{{ dataList.address }}</div>
          </div>
          <div class="itemInfo" v-if="activeFlag == '0'">
            <div class="title">必修学员：</div>
            <div class="value">{{ statusNames }}</div>
          </div>
        </div>
        <div class="statusClass" v-if="activeFlag == '2'">
          <img v-if="dataList.taskStatus == '0'" src="../../../../assets/images/signOk.png" alt="">
          <img v-if="dataList.taskStatus == '4'" src="../../../../assets/images/signNo.png" alt="">
          <img v-if="dataList.taskStatus == '5'" src="../../../../assets/images/finish.png" alt="">
        </div>
      </div>
      <!-- 签到信息 -->
      <div class="baseInfoSy" v-if="activeIndex == 1">
        <div class="contenter">
          <el-table ref="signTable" :data="dataList.signs" border style="width: 100%" height="100%"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" fixed="left" align="center"></el-table-column>
            <el-table-column prop="name" label="姓名" width="180">
            </el-table-column>
            <el-table-column prop="sex" label="性别" width="180">
              <template slot-scope="scope">
                <span>{{ scope.row.sex == '1' ? '女' : '男' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="phone" label="联系电话">
            </el-table-column>
            <el-table-column prop="deptName" label="所属部门" width="180">
            </el-table-column>
            <el-table-column prop="signInStatus" label="签到状态" width="180">
              <template slot-scope="scope">
                <span>{{ scope.row.signInStatus == '0' ? '未签到' : '已签到' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="signTime" label="签到时间" width="180">
            </el-table-column>
            <el-table-column label="签到内容" width="180">
              <template slot-scope="scope">
                <img class="imgClass" v-for="(item, index) in scope.row.fileList" :key="index" :src="item.viewAddress"
                  alt="">
              </template>
            </el-table-column>
            <el-table-column prop="type" label="培训方式" width="180">
              <template slot-scope="scope">
                <span>{{ scope.row.type == '0' ? '线上会议' : '线下培训' }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>

      </div>
      <!-- 考试信息 -->
      <div class="baseInfo" v-if="activeIndex == 2 || activeIndex == '3'">
        <h1>基础信息</h1>
        <div class="contenter">
          <div class="itemInfo">
            <span class="title">试卷名称：</span>
            <span class="value">{{ examinationInfo.name }}</span>
          </div>
          <div class="itemInfo">
            <span class="title">所属单位：</span>
            <span class="value">{{ examinationInfo.deptName }}</span>
          </div>
          <div class="itemInfo">
            <span class="title">所属科目：</span>
            <span class="value">{{ examinationInfo.subjectName }}</span>
          </div>
          <div class="itemInfo">
            <span class="title">考试期限：</span>
            <span class="value">{{ examinationInfo.startTime }}至{{ examinationInfo.endTime }}</span>
          </div>
          <div class="itemInfo">
            <span class="title">答题时长：</span>
            <span class="value">{{ examinationInfo.duration }}分钟</span>
          </div>
          <div class="itemInfo">
            <span class="title">试卷总分：</span>
            <span class="value">{{ examinationInfo.score }}分</span>
          </div>
          <div class="itemInfo">
            <span class="title">通过分数：</span>
            <span class="value">{{ examinationInfo.passScore }}分</span>
          </div>
          <div class="itemInfo">
            <span class="title">已答题人数：</span>
            <span class="value">{{ examinationInfo.alRStudentNum }}人</span>
          </div>
          <div class="itemInfo">
            <span class="title">未答题人数：</span>
            <span class="value">{{ examinationInfo.noStudentNum }}人</span>
          </div>
          <div class="itemInfo">
            <span class="title">考试状态：</span>
            <span class="value">{{ examinationInfo.taskStatusPc | filterStatus }}</span>
          </div>
        </div>
      </div>
    </div>

    <div slot="content" class="courseContent">
      <!-- 培训信息 -->
      <div v-if="activeIndex == 0">
        <div class="top">
          <h1>培训课件</h1>
        </div>
        <div class="courseware_content">
          <div v-for="(k, index) in dataList.files" :key="k.id" class="item">
            <div>
              <img src="../../../../assets/images/doc.png" alt="" />
              <span>{{ index + 1 }}.{{ k.originalFilename }}</span>
            </div>
            <div class="operate">
              <span @click="ckeckFile(k, 'examine')">查看</span>
              <!-- <span @click="download(k)">下载</span> -->
            </div>
          </div>
          <div class="top">
            <h1>培训记录</h1>
          </div>
          <div class="recored_content">
            <div v-for="(k, index) in dataList.trainRecordVo" :key="k.id" class="item">
              <div>
                <img src="../../../../assets/images/doc.png" alt="" />
                <span>{{ index + 1 }}.{{ k.originalFilename }}</span>
              </div>
              <div class="operate">
                <span @click="ckeckFile(k, 'examine')">查看</span>
                <!-- <span @click="download(k)">下载</span> -->
              </div>
            </div>
          </div>
          <div class="top">
            <h1>培训备注：</h1>
          </div>
          <p>{{ dataList.remark }}</p>
        </div>
      </div>
      <!-- 考试信息 -->
      <div v-if="activeIndex == '2'" class="table_content">
        <div class="top">
          <span>参考信息</span>
          <!-- <div>
            <el-button type="primary" plain>派发</el-button>
            <el-button type="primary" :disabled="multipleSelection.length<1" @click="exportClickExport">导出</el-button>
          </div> -->
        </div>
        <div class="contentiner">
          <el-table :data="examinationInfo.examRecord" style="width: 100%;height:100%;overflow: auto;" border stripe
            title="双击查看详情" @row-dblclick="openDetails">
            <el-table-column type="selection" width="55" fixed="left" align="center"></el-table-column>
            <el-table-column prop="name" label="姓名" align="center" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="" label="性别" align="center" show-overflow-tooltip>
              <template slot-scope="scope">
                <span>{{ scope.row.gender == '0' ? '男' : '女' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="mobilePhone" label="联系电话" align="center" show-overflow-tooltip></el-table-column>
            <el-table-column prop="laboratoryName" label="所属部门" align="center" show-overflow-tooltip></el-table-column>
            <el-table-column prop="actualDuration" label="答题时长" align="center" show-overflow-tooltip></el-table-column>
            <el-table-column prop="submitTime" label="提交时间" align="center" show-overflow-tooltip></el-table-column>
            <el-table-column prop="actualScore" label="考试得分" align="center" show-overflow-tooltip></el-table-column>
            <el-table-column label="通过状态" align="center" show-overflow-tooltip>
              <template slot-scope="scope">
                <div class="statusBtn">
                  <span v-if="scope.row.pass == '1'" class="auditNo">
                    <img src="../../../../assets/images/icon-wrapper.png" alt="" />
                    未通过
                  </span>
                  <span v-if="scope.row.pass == '0'" class="relwase">
                    <img src="../../../../assets/images/pass.png" alt="">
                    通过</span>
                  <span v-if="scope.row.pass == '2'" class="relwaseNo">未参与</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="taskTeamName" label="操作" align="center" show-overflow-tooltip>
              <template slot-scope="scope">
                <div class="operateBths">
                  <el-link v-if="scope.row.pass != '2'" type="primary" @click="openDetails(scope.row)">查看</el-link>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <!-- 试题信息 -->
      <div v-if="activeIndex == '3'" class="question_content">
        <div v-for="(k, ind) in dataList.questions" :key="k.id" :name="k.id"
          :class="['exercisesItem', k.isExpand ? 'expand' : '']" :ref="'exercisesItem' + ind">
          <div class="exercisesTop">
            <div class="left">
              <div class="exercisesType">
                {{
                  k.type == "1" ? "单选题" : k.type == "2" ? "多选题" : "判断题"
                }}
              </div>
              <span>得分：2分</span>
            </div>
            <div class="right">
              <span @click="isExpandBtn(k, index)">{{
                k.isExpand ? "折叠" : "展开"
              }}</span>
            </div>
          </div>
          <div :class="['exercisesName', k.isExpand ? '' : 'title']">
            {{ k.topic }}
          </div>
          <el-radio-group v-if="k.type == '1'" class="radio" v-model="k.answer" disabled>
            <el-radio v-for="(j, index) in k.options" :key="index" :label="j.id">{{ j.id }}. {{ j.label }}</el-radio>
          </el-radio-group>
          <el-checkbox-group v-if="k.type == '2'" v-model="k.answer" class="radio" disabled>
            <el-checkbox v-for="(j, index) in k.options" :key="index" :label="j.id">{{ j.id }}. {{ j.label }}
            </el-checkbox>
          </el-checkbox-group>
          <p>答案：{{ k | getAnswer }}</p>
          <p>
            解析：
            {{ k.analysis }}
          </p>
        </div>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import moment from "moment";
import axios from 'axios'
export default {
  data() {
    return {
      tabsList: [{
        name: '培训信息',
        value: '0',
      },
      {
        name: '签到信息',
        value: '1',
      },
      {
        name: '考试内容',
        value: '2',
      },
      {
        name: '考试记录',
        value: '3',
      }
      ],
      contentLoading: false,
      routeInfo: "",
      moment,
      id: "",
      examine: '', // 查看文件状态
      dataList: {}, // 模板信息
      statusNames: '',
      coursewareList: [{
        id: '0',
        name: '课件名称'
      }],
      idsy: [],
      activeIndex: 0,
      examinationInfo: {},
      multipleSelection: [],
      activeFlag: '0'
    };
  },
  created() {
    this.routeInfo = JSON.parse(sessionStorage.getItem("routeInfo"));
    this.id = this.$route.query.id
    this.activeFlag = this.$route.query.activeFlag
    if (this.activeFlag == '2') {
      this.tabsList = [{
        name: '培训信息',
        value: '0',
      }]
    }
    if (this.id) {
      this.getDetails();
    }
  },
  filters: {
    filterStatus(val) {
      return val == '1' ? '已结束' : val == '2' ? '已作答' : val == '3' ? '进行中' : '未作答'
    },
    getAnswer(val) {
      if (val.type == '3') {
        return val.answer == '1' ? '正确' : '错误'
      } else if (val.type == '2') {
        return val.answer.toString()
      } else {
        return val.answer
      }
    },
  },
  methods: {
    // 切换tabs
    handleClick(tab, event) {
      this.activeIndex = tab.index
      console.log(this.activeIndex,);
      if (tab.index == '1') {
        this.multipleSelection = []
      }
    },
    // 获取详情
    getDetails() {
      this.contentLoading = true;
      this.$api.trainTasksDetail({
        id: this.id
      }).then(res => {
        this.examinationInfo = res.data.examInfos ? res.data.examInfos[0] : {}
        this.statusNames = res.data.studentsList ? res.data.studentsList.map(item => item.name).join(',') : ''
        if (res.data.questions && res.data.questions.length) {
          res.data.questions.forEach(k => {
            k.isExpand = false
            k.options = JSON.parse(k.options)
            if (k.type == '2') {
              k.answer = k.answer.split(',')
            }
          });
        }
        this.dataList = res.data
      })
      this.contentLoading = false;
    },
    // 查看文件
    ckeckFile(k, examine) {
      k.url = JSON.stringify([{
        url: k.viewAddress
      }])
      this.$router.push({
        path: '/seeFile',
        query: {
          itemInfo: JSON.stringify(k),
          type: 'see'
        }
      })

    },
    // 下载试题类型模板
    download(k) {
      this.idsy = []
      this.idsy.push(k.id)
      let params = {
        ids: this.idsy
      }
      console.log(params, 'PARAMS789');
      let httpname = '/minio/downloadBatch'
      axios({
        method: 'post',
        url: __PATH.BASE_URL_LABORATORY + httpname,
        data: params,
        responseType: 'blob',
        headers: {
          "Content-Type": "application/json",
        }
      })
        .then((res) => {
          console.log(JSON.stringify(res.data));
          let name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
          let blob = new Blob([res.data]) // { type: "application/vnd.ms-excel" }
          let url = window.URL.createObjectURL(blob) // 创建一个临时的url指向blob对象
          // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
          let a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
          // 释放这个临时的对象url
          window.URL.revokeObjectURL(url)
        })
        .catch((res) => {
          this.$message.error('下载失败！')
        })
    },
    // 全部下载
    allDownload() {
      let httpname = 'trainTmp/downloadCoursewares'
      let params = {
        id: this.dataList.id,
      }
      axios({
        method: 'post',
        url: __PATH.BASE_URL_LABORATORY + httpname,
        data: params,
        responseType: 'blob',
        headers: {
          "Content-Type": "application/json",
        }
      })
        .then((res) => {
          console.log(JSON.stringify(res.data));
          let name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
          let blob = new Blob([res.data]) // { type: "application/vnd.ms-excel" }
          let url = window.URL.createObjectURL(blob) // 创建一个临时的url指向blob对象
          // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
          let a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
          // 释放这个临时的对象url
          window.URL.revokeObjectURL(url)
        })
        .catch((res) => {
          this.$message.error('下载失败！')
        })
    },
    // 展开试题
    isExpandBtn(item, index) {
      item.isExpand = !item.isExpand;
    },
    openDetails(val) {
      this.$router.push({
        path: "/answerInfo",
        query: {
          respondent: val.respondent,
          recordId: this.dataList.taskId //任务id
        },
      });
    },
    //勾选
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    exportClickExport() {
      let params = {
        signIds: this.multipleSelection.map(k => k.id)
      }
      axios({
        method: "post",
        url: __PATH.BASE_URL_LABORATORY + '/trainPlan/taskInfoSignExport',
        data: params,
        headers: {
          "Content-Type": "application/json",
          token: this.routeInfo.token,
        },
        responseType: 'blob',
      }).then((res) => {
        console.log(res, 'res');
        if (res.status == 200 && !res.data.code) {
          let name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
          let blob = new Blob([res.data]) // { type: "application/vnd.ms-excel" }
          let url = window.URL.createObjectURL(blob) // 创建一个临时的url指向blob对象
          // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
          let a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
          // 释放这个临时的对象url
          window.URL.revokeObjectURL(url)
        } else {
          this.$message.error('导出失败')
        }
      })
    }
  },
};

</script>
<style lang="scss" scoped>
.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);

  .baseInfo {
    padding: 0 24px 24px 24px;
    position: relative;

    .contenter {
      padding-top: 24px;
      font-size: 14px;
      display: flex;
      flex-wrap: wrap;

      .itemInfo {
        width: 33.3%;
        margin-bottom: 16px;
        display: flex;
        justify-content: flex-start;

        .title {
          width: 100px;
          color: #666;
          margin-top: 3px;
        }

        .value {
          width: 200px;
          line-height: 20px;
        }
      }
    }

    .statusClass {
      position: absolute;
      right: 20px;
      top: 20px;

      img {
        width: 100px;
        height: 100px;
      }
    }
  }
}

.topFilter {
  padding: 15px;
  height: 60px;
  background-color: #fff;

  .backBar {
    color: #121f3e;
    height: 30px;
    border-bottom: 1px solid #dcdfe6;
  }
}

.courseContent {
  height: 100%;
  margin-top: 16px;
  background-color: #fff;
  padding: 16px;
  overflow: auto;

  .top {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;

    span {
      font-size: 14px;
      color: #3562DB;
    }
  }

  .courseware_content {
    .item {
      height: 56px;
      font-size: 14px;
      line-height: 56px;
      display: flex;
      justify-content: space-between;

      img {
        vertical-align: middle;
        margin-right: 12px;
      }

      .operate {
        span {
          cursor: pointer;
          color: #3562DB;
          margin-right: 16px;
        }
      }

    }
  }
}

.recored_content {
  .item {
    height: 56px;
    font-size: 14px;
    line-height: 56px;
    display: flex;
    justify-content: space-between;

    img {
      vertical-align: middle;
      margin-right: 12px;
    }

    .operate {
      span {
        cursor: pointer;
        color: #3562DB;
        margin-right: 16px;
      }
    }

  }
}

.itemIndex {
  width: 730px;
  white-space: nowrap;
  /* 不换行 */
  overflow: hidden;
  /* 超出部分隐藏 */
  text-overflow: ellipsis;
}

.tabsList {
  margin-bottom: 10px;
  padding-left: 15px;
  width: 100%;
  height: 50px;
  position: relative;

  .btn {
    position: absolute;
    right: 10px;
    top: 0px;
  }
}

::v-deep .el-radio {
  display: block;
  margin: 10px 0;
}

::v-deep .el-checkbox {
  display: block;
  margin: 10px 0;
}

::v-deep .el-checkbox-group {
  margin-left: 38px;
}

.baseInfoSy {
  padding: 0 24px 24px 24px;
  width: 100%;
  height: 650px;

  .contenter {
    // padding-top: 24px;
    font-size: 14px;


    .itemInfo {
      margin-bottom: 16px;
      display: flex;

      .title {
        width: 120px;
        color: #666;
        margin-top: 3px;
      }

      .value {
        flex: 1;
        line-height: 20px;
      }
    }
  }
}

.table_content {
  height: calc(100% - 70px);

  // overflow: auto;
  .top {
    display: flex;
    justify-content: space-between;
  }

  .contentiner {
    height: 100%;
  }

  .statusBtn {
    font-size: 14px;
    display: flex;
    justify-content: center;

    .auditIng {
      width: 58px;
      height: 24px;
      background-color: #fff7e8;
      border-radius: 4px;
      color: #d25f00;
    }

    .auditNo {
      width: 78px;
      height: 24px;
      background-color: #ffece8;
      border-radius: 4px;
      color: #cb2634;

      img {
        vertical-align: middle;
      }
    }

    .relwase {
      width: 58px;
      height: 24px;
      background-color: #e8ffea;
      border-radius: 4px;
      color: #009a29;

      img {
        vertical-align: middle;
      }
    }

    .inProgress {
      width: 86px;
      height: 24px;
      background-color: #E6EFFC;
      border-radius: 4px;
      color: #2749BF;
    }

    .relwaseNo {
      width: 58px;
      height: 24px;
      background-color: #F2F4F9;
      border-radius: 4px;
      color: #86909C;
    }
  }

  .operateBths {
    color: #3562DB;

    .el-link {
      margin-right: 8px;
    }
  }
}

.question_content {
  height: calc(100% - 70px);
  margin-top: 16px;
  overflow: auto;

  .exercisesItem {
    height: 90px;
    overflow: hidden;
    background-color: #fff;
    margin-bottom: 16px;
    padding: 16px;
    border-bottom: 4px solid #faf9fc;
    font-size: 14px;

    .exercisesTop {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;

      .left {
        display: flex;
        align-items: center;
        justify-content: center;

        span {
          color: #7f848c;
        }
      }

      .right {
        color: #ccced3;
        display: flex;
        align-items: center;

        .line {
          width: 2px;
          height: 14px;
          margin: 0 10px 0 26px;
          background-color: #dcdfe6;
        }

        span {
          color: #3562db;
          margin-left: 16px;
          cursor: pointer;
        }

        i {
          color: #3562db;
          cursor: pointer;
          margin-left: 16px;
        }
      }

      .exercisesType {
        width: 58px;
        height: 22px;
        line-height: 22px;
        text-align: center;
        border-radius: 4px;
        color: #86909c;
        background-color: #ededf5;
        margin: 0 10px;
      }
    }

    .exercisesName {
      line-height: 20px;
      margin-bottom: 16px;
    }

    .title {
      overflow: hidden;
      -webkit-line-clamp: 2;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
    }

    .el-radio {
      margin-left: 38px;
      font-size: 14px;
      color: #7f848c !important;
      line-height: 30px;
    }

    p {
      font-size: 14px;
      color: #7f848c !important;
      line-height: 20px;
      margin-bottom: 16px;
    }
  }

  .expand {
    height: auto;
  }
}

.imgClass {
  width: 80px;
  height: 80px
}
</style>
