<template>
  <div ref="listMode" class="listMode">
    <div v-loading="loading" class="type-main">
      <div v-if="listData.length" class="type-content">
        <div v-for="(item, index) in listData" :key="item.surveyEntityCode" class="card-content">
          <div class="card-heade" @click="goToDetails(item)">
            <div class="heade-info">
              <p class="heade-name">{{ item.surveyEntityName }}</p>
              <span
                v-for="alarm in item.policeHistoryGroup"
                :key="alarm.alarmLevel"
                class="info-icon-box"
                :style="{ display: !alarmType[alarm.alarmLevel] ? 'none' : '' }"
                @click.stop="viewAlarm(alarm)"
              >
                <svg-icon v-if="[0, 1, 2, 3].includes(alarm.alarmLevel)" :name="alarmType[alarm.alarmLevel]" class="info-icon" />
              </span>
            </div>
            <div v-if="item.entityTypeId != '2887'" class="heade-control">
              <p class="btn-item" :class="{ 'btn-active': item.surveyStatus == 1 }">自动</p>
              <p class="btn-item" :class="{ 'btn-active': item.surveyStatus == 0 }">手动</p>
              <!-- <svg-icon name="on_icon" class="on-icon" /> -->
              <!-- <svg-icon v-else-if="item.status == 1" name="off_icon" class="on-icon" />
              <svg-icon v-else-if="item.status == 6" name="break_icon" class="on-icon" style="cursor: no-drop;" /> -->
            </div>
          </div>
          <div class="card-main">
            <!-- <div class="card-img">
              <img v-if="item.status == 0" src="../../../../assets/images/monitor/sb_img_kai.png" alt="水泵开" />
              <img v-else src="../../../../assets/images/monitor/sb_img_guan.png" alt="水泵关" />
            </div> -->
            <div class="main-left">
              <div class="left-content">
                <div
                  v-for="v in item.parameterList.slice((item.leftPaging.page - 1) * item.leftPaging.size, item.leftPaging.page * item.leftPaging.size)"
                  :key="v.parameterId"
                  class="left-item"
                >
                  <p class="item-label">{{ v.parameterName }}</p>
                  <p class="item-value" style="margin-top: 9px">
                    <span style="font-size: 20px; font-weight: 600" :style="{ color: v.color }">{{ v.parameterValue || '-' }}</span>
                    <span style="font-size: 12px; color: #ccced3; margin-left: 4px">{{ v.parameterUnit || '' }}</span>
                  </p>
                </div>
              </div>
              <div class="left-footer">
                <el-pagination
                  layout="prev, pager, next"
                  hide-on-single-page
                  :page-size="item.leftPaging.size"
                  :current-page="item.leftPaging.page"
                  :total="item.parameterList.length"
                  @current-change="
                    (val) => {
                      pageChange(val, index, 'left')
                    }
                  "
                >
                </el-pagination>
              </div>
            </div>
            <!-- <div v-if="item.controlParameterList" class="main-right">
                <div v-for="(v, i) in item.controlParameterList" :key="v.parameterId + i + 'left'" class="right-item">
                  <p class="item-label">{{ v.parameterName }}</p>
                  <p class="item-value" style="margin-top: 9px; padding-right: 16px">
                    <span style="font-size: 18px; font-weight: bold">{{ v.parameterValue || '-' }}</span>
                    <span style="font-size: 12px; color: #121f3e; font-weight: bold">{{ v.parameterUnit || '' }}</span>
                    <span v-if="item.surveyStatus == 0" class="control-btn">
                      <i class="el-icon-caret-top"></i>
                      <i class="el-icon-caret-bottom"></i>
                    </span>
                  </p>
                </div>
              </div> -->
            <div class="main-right">
              <div v-if="item.controlParameterList.length" class="right-content">
                <div
                  v-for="v in item.controlParameterList.slice((item.rightPaging.page - 1) * item.rightPaging.size, item.rightPaging.page * item.rightPaging.size)"
                  :key="v.parameterId"
                  class="right-item"
                >
                  <p class="item-label">
                    <svg-icon v-if="v.parameterIcon" :name="v.parameterIcon" class="item-icon" />
                    <span :style="{ width: v.parameterIcon ? '75%' : '100%' }">{{ v.parameterName }}</span>
                  </p>
                  <p v-if="v.child && v.child.length" class="item-value">
                    <el-select v-model="v.parameterValue" placeholder="请选择" style="width: 100px">
                      <el-option v-for="item in v.child" :key="item.value" :label="item.paramName" :value="item.value"> </el-option>
                    </el-select>
                  </p>
                  <p v-else class="item-value">
                    <el-input v-model="v.parameterValue" style="width: 100px">
                      <el-button slot="append">{{ v.parameterUnit || '' }}</el-button>
                    </el-input>
                  </p>
                </div>
              </div>
              <div v-else class="right-noData">
                <img src="../../../../assets/images/monitor/noDataImg.png" />
                <p>暂无可操作项</p>
              </div>
              <div class="right-footer">
                <el-pagination
                  layout="prev, pager, next"
                  hide-on-single-page
                  :page-size="item.rightPaging.size"
                  :current-page="item.rightPaging.page"
                  :total="item.controlParameterList.length"
                  @current-change="
                    (val) => {
                      pageChange(val, index, 'right')
                    }
                  "
                >
                </el-pagination>
              </div>
              <el-button v-if="item.surveyStatus == 0" class="right-btn" type="primary" size="mini" @click="application(item.surveyEntityCode, item.controlParameterList)"
              >应用</el-button
              >
            </div>
          </div>
        </div>
      </div>
      <div v-else class="echart-null">
        <img src="@/assets/images/monitor/null.png" alt="" />
        <div>暂无数据~</div>
      </div>
    </div>
    <div class="listMode-footer">
      <el-pagination
        :current-page="pagination.current"
        :page-sizes="pagination.pageSizeOptions"
        :page-size="pagination.size"
        :layout="pagination.layoutOptions"
        :total="pagination.total"
        @size-change="paginationSizeChange"
        @current-change="paginationCurrentChange"
      />
    </div>
    <alarm-dialog v-if="alarmDialog" :visible.sync="alarmDialog" :alarmTypeItem="alarmTypeItem" />
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import tableListMixin from '@/mixins/tableListMixin.js'
import alarmDialog from '../../airMenu/components/alarmDialog'
import { monitorTypeList } from '@/util/dict.js'
import { arrTrans } from '@/util'
export default {
  name: 'listMode',
  components: {
    alarmDialog
  },
  mixins: [tableListMixin],
  data() {
    return {
      alarmDialog: false,
      loading: false,
      requestInfo: {},
      listData: [],
      alarmType: {
        3: 'urgent_icon',
        2: 'serious_icon',
        1: 'commonly_icon',
        0: 'tips_icon'
      },
      alarmTypeItem: {},
      monitorTypeList: Object.freeze(monitorTypeList)
    }
  },
  computed: {
    arrTrans() {
      return (list) => {
        return arrTrans(3, list)
      }
    },
    ...mapGetters({
      socketIemcMsgs: 'socket/socketIemcMsgs'
    })
  },
  watch: {
    socketIemcMsgs(data) {
      let itemData = JSON.parse(data)
      let newList = JSON.parse(JSON.stringify(this.listData))
      newList.forEach((item) => {
        if (item.surveyEntityCode == itemData.surveyEntityCode) {
          item.leftPaging = { page: 1, size: 6 }
          item.rightPaging = { page: 1, size: 3 }
          Object.assign(item, itemData)
        }
      })
      this.listData = newList
    }
  },
  created() {},
  methods: {
    // 查看报警记录
    viewAlarm(item) {
      this.alarmTypeItem = { ...item, projectCode: this.requestInfo.projectCode }
      this.alarmDialog = true
    },
    // 获取检测项列表
    init(params) {
      this.pagination.current = 1
      this.requestInfo = params
      this.getDataList()
    },
    application(surveyEntityCode, controlParameterList) {
      let data = {
        imsCode: surveyEntityCode,
        paramList: controlParameterList.map((ele) => {
          return {
            paramId: ele.parameterId,
            value: ele.parameterValue
          }
        })
      }
      this.$api.setControl(data).then((res) => {
        this.getDataList()
      })
    },
    // 获取检测项列表
    getDataList() {
      let params = {
        ...this.requestInfo,
        page: this.pagination.current,
        pageSize: this.pagination.size
      }
      this.loading = true
      this.listData = []
      this.$api.getRealMonitoringList(params).then((res) => {
        this.loading = false
        if (res.code == 200) {
          this.listData = res.data.list ? this.setData(res.data.list) : []
          this.pagination.total = res.data.totalCount ? res.data.totalCount : 0
          console.log(this.listData)
        }
      })
    },
    setData(list) {
      list.forEach((item) => {
        if (!item.leftPaging) {
          item.leftPaging = { page: 1, size: 6 }
        }
        if (!item.rightPaging) {
          item.rightPaging = { page: 1, size: 3 }
        }
      })
      return list
    },
    goToDetails(item) {
      const projectData = this.monitorTypeList.find((item) => item.projectCode == this.requestInfo.projectCode)
      let path = ''
      if (projectData.projectName == '风冷热泵监测') {
        path = '/airCooled/airCooledMonitor/monitorDetails'
      } else if (projectData.projectName == '冷热源监测') {
        path = '/coldHeat/coldMonitor/monitorDetails'
      }
      if (!path) return
      this.$router.push({
        path: path,
        query: {
          surveyCode: item.surveyEntityCode,
          surveyName: item.surveyEntityName,
          projectCode: this.requestInfo.projectCode,
          assetId: item.assetId
        }
      })
    },
    // 前端分页
    pageChange(page, index, type) {
      let newObj = JSON.parse(JSON.stringify(this.listData[index]))
      newObj[type + 'Paging'].page = page
      this.listData.splice(index, 1, newObj)
    }
  }
}
</script>
<style lang="scss" scoped>
.listMode {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  ::v-deep .el-loading-mask {
    left: 16px;
  }
  p {
    margin: 0;
  }
  .type-main {
    overflow: auto;
    flex: 1;
    // background: #fff;
    ::v-deep .box-card {
      width: 100%;
      padding: 16px 0;
      .card-title {
        padding: 0 16px;
      }
      .card-body {
        width: 100%;
        margin-top: 0;
      }
    }
    .type-content {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
    }
    .echart-null {
      margin: 0 auto;
      width: 50%;
      text-align: center;
      color: #8a8c8f;
    }
    .card-content {
      border-radius: 4px;
      margin-top: 16px;
      margin-left: 16px;
      background: #fff;
      width: calc(100% / 3 - 16px);
      display: flex;
      flex-direction: column;
      .card-heade {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        box-shadow: 0 0 5px 0 rgb(18 31 62 / 12%);
        cursor: pointer;
        .heade-info {
          display: flex;
          align-items: center;
          .heade-name {
            font-size: 15px;
            color: #121f3e;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .info-icon-box {
            cursor: pointer;
            .info-icon {
              margin-left: 7px;
              font-size: 18px;
              color: #fff;
            }
          }
        }
        .heade-control {
          display: flex;
          align-items: center;
          margin-left: 8px;
          .btn-item {
            cursor: no-drop;
            margin-right: 8px;
            padding: 3px 0;
            text-align: center;
            min-width: 42px;
            font-size: 14px;
            line-height: 14px;
            color: #7f848c;
            background: #f6f5fa;
            border-radius: 2px;
            border: 1px solid #ededf5;
          }
          .btn-active {
            background: #3562db;
            color: #fff;
            border-color: #3562db;
          }
        }
      }
      .card-main {
        padding: 16px;
        height: 290px;
        display: flex;
      }
    }
  }
  .on-icon {
    z-index: 1001;
    font-size: 20px;
    cursor: pointer;
  }
  .card-img {
    width: 60px;
    height: 60px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .main-left {
    width: 60%;
    height: 100%;
    border-right: 1px solid #dcdfe6;
    .left-content {
      height: 84%;
      width: 100%;
      display: flex;
      flex-wrap: wrap;
    }
    .left-item {
      width: 50%;
    }
    .left-footer {
      height: 16%;
      display: flex;
      justify-content: space-between;
      position: relative;
      ::v-deep .el-pagination {
        padding: 0;
        position: absolute;
        right: 16px;
        bottom: 0;
      }
    }
  }
  .main-right {
    width: 40%;
    height: 100%;
    .right-content {
      height: 75%;
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      padding-left: 24px;
    }
    .right-noData {
      height: 75%;
      width: 100%;
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;
      img {
        width: 80%;
      }
      p {
        margin-top: 10px;
        color: #ccced3;
        font-size: 12px;
      }
    }
    .right-item {
      width: 100%;
    }
    .right-btn {
      height: 9%;
      font-size: 12px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 auto;
    }
    .right-footer {
      height: 16%;
      display: flex;
      justify-content: space-between;
      position: relative;
      ::v-deep .el-pagination {
        padding: 0;
        position: absolute;
        right: 0;
        bottom: 0;
      }
    }
  }
  .item-label {
    font-size: 14px;
    color: #414653;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .item-value {
    position: relative;
    color: #121f3e;
    line-height: 20px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    .control-btn {
      position: absolute;
      display: flex;
      flex-direction: column;
      color: #c0c4cc;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      font-size: 14px;
      i {
        cursor: pointer;
      }
      i:hover {
        color: #3562db;
      }
      .el-icon-caret-top,
      .el-icon-caret-bottom {
        margin-top: -2px;
        margin-bottom: -2px;
      }
    }
  }
  .listMode-footer {
    padding: 10px 0 10px 16px;
    ::v-deep .el-pagination {
      .btn-next,
      .btn-prev {
        background: transparent;
      }
      .el-pager li {
        background: transparent;
      }
    }
  }
}
</style>
