<!--
 * @Description: 培训演练
-->
<template>
  <div class="trainingDrill">
    <div v-for="(item, i) in drillList" :key="i" class="trainingDrill-box">
      <div class="box-title">
        <span>{{ item.firstTitle }}</span
        ><span>- {{ item.secondTitle }}</span>
      </div>
      <div class="box-plan-text">{{ item.thirdTitle }}</div>
      <div class="box-plan-echart">
        <div :id="item.echartsId"></div>
      </div>
      <div class="box-complete">
        <span>完成数量:</span><span>{{ item.completed }}</span>
      </div>
      <div class="box-footer-static">
        <div>
          <span>{{ item.total }}</span
          ><span>总数</span>
        </div>
        <div>
          <span>{{ item.nextMonth }}</span
          ><span>下月计划</span>
        </div>
        <div>
          <span>{{ item.unCompleted }}</span
          ><span>待执行</span>
        </div>
      </div>
    </div>
    <div class="center-line"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
export default {
  name: 'trainingDrill',
  data() {
    return {
      drillList: [
        {
          firstTitle: '安全培训',
          secondTitle: '培训计划(年)',
          thirdTitle: '当前计划完成度',
          echartsId: 'trainingDrill1',
          completed: 0,
          total: 0,
          nextMonth: 0,
          unCompleted: 0
        },
        {
          firstTitle: '预案演练',
          secondTitle: '演练计划(年)',
          thirdTitle: '当前演练完成度',
          echartsId: 'trainingDrill2',
          completed: 0,
          total: 0,
          nextMonth: 0,
          unCompleted: 0
        }
      ]
    }
  },
  mounted() {
    this.getQueryTaskProgress()
    this.getTrainPlanCount()
  },
  methods: {
    getQueryTaskProgress() {
      let userInfo = this.$store.state.user.userInfo.user
      let data = {
        userId: userInfo.staffId,
        nextMonthFlag: 1
      }
      this.$api.getQueryTaskProgress(data).then((res) => {
        if (res.code == '200') {
          const data = res.data
          const stateList = ['completed', 'total', 'nextMonth', 'unCompleted']
          stateList.forEach((item) => {
            this.drillList[1][item] = data[item] || 0
          })
          this.initEchart(this.drillList[1].echartsId, data.rate)
        }
      })
    },
    getTrainPlanCount() {
      this.$api.getTrainPlanCount().then((res) => {
        if (res.code == '200') {
          const data = res.data
          const stateList = ['completed', 'total', 'nextMonth', 'unCompleted']
          stateList.forEach((item) => {
            this.drillList[0][item] = data[item] || 0
          })
          this.initEchart(this.drillList[0].echartsId, data.completed / data.total * 100)
        }
      })
    },
    initEchart(id, data) {
      let getchart = echarts.init(document.getElementById(id))
      let option = {
        tooltip: {
          show: false
        },
        series: [
          {
            type: 'gauge',
            startAngle: 180,
            endAngle: 0,
            min: 0,
            max: 100,
            radius: '166%',
            center: ['50%', '100%'],
            itemStyle: {
              color: '#1FFAFF'
            },
            progress: {
              show: true,
              width: 13
            },
            pointer: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false
            },
            axisLabel: {
              show: false
            },
            anchor: {
              show: false
            },
            title: {
              show: false
            },
            detail: {
              valueAnimation: true,
              width: '60%',
              lineHeight: 20,
              borderRadius: 8,
              offsetCenter: [0, '-20%'],
              fontSize: 20,
              fontWeight: 'bolder',
              formatter: '{value}%',
              color: '#1FFAFF'
            },
            data: [{ value: (data || 0).toFixed(2) }]
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.trainingDrill {
  width: 100%;
  height: 100%;
  display: flex;
  font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
  overflow: hidden;
  position: relative;
  .center-line {
    position: absolute;
    width: 1px;
    height: 100%;
    background: linear-gradient(225deg, rgba(104, 168, 255, 0) 0%, #a4caff 49%, rgba(104, 168, 255, 0) 100%);
    left: 50%;
    top: 0;
  }
  .trainingDrill-box {
    width: 50%;
    height: 100%;
    padding: 10px 0px 5px 0px;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .box-title {
      height: 20px;
      line-height: 20px;
      font-size: 16px;
      color: #1ffaff;
      span {
        &:last-child {
          color: #fff;
        }
      }
    }
    .box-plan-echart {
      // height: calc(77% - 100px);
      width: 70%;
      // width: 167px;
      aspect-ratio: 2 / 1;
      margin: 0 auto;
      & > div {
        width: 100%;
        height: 100%;
        background: url('~@/assets/images/safetyDataCockpit/training-drill-echart.png') no-repeat;
        background-size: 100% 100%;
      }
    }
    .box-plan-text {
      height: 20px;
      line-height: 20px;
      font-size: 14px;
      color: #dceaff;
    }
    .box-complete {
      height: 20px;
      line-height: 20px;
      span {
        font-size: 14px;
        color: #fff;
        &:last-child {
          margin-left: 6px;
          font-family: DIN;
          font-weight: bold;
          color: #1ffaff;
        }
      }
    }
    .box-footer-static {
      height: 23%;
      padding: 0 10%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      background: url('~@/assets/images/safetyDataCockpit/training-drill-bg.png') no-repeat;
      background-size: 100% 100%;
      div {
        display: flex;
        flex-direction: column;
        span {
          font-size: 17px;
          font-family: DIN;
          color: #fff;
          font-weight: bold;
          &:last-child {
            font-size: 14px;
            color: #7eaef9;
          }
        }
      }
    }
  }
}
</style>
