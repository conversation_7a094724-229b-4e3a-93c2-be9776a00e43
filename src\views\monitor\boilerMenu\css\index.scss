// 表格样式
.diablo-table.el-table, .diablo-table .el-table__expanded-cell{
  background-color: transparent;
}
.diablo-table.el-table::before{
  background-color: transparent !important;
 }
.diablo-table{
  div.el-table__fixed-body-wrapper, div.el-table__body-wrapper {
    background: center;
  }
  .el-table__fixed-header-wrapper .el-table__header tr {
    background: center;
  }
  table.el-table__header thead th {
    background: transparent!important;
  }
  table.el-table__header thead tr {
    background-color: transparent;
  }
  .el-table__body{
    //-webkit-border-horizontal-spacing: 13px;  // 水平间距
    -webkit-border-vertical-spacing: 4px;  // 垂直间距
  }
  .el-table__header .el-table__cell .cell{
    background-color: rgba(255, 255, 255, 0.05);
    padding: 10px 0;
    text-align: center;
  }
  .el-table__cell .cell{
    color: #fff;
    text-align: center;
  }
  .el-table__header .el-table__cell{
    color: #fff;
    font-weight: 500;
  }
  th.el-table__cell.is-leaf{
    border-bottom: 0px solid transparent;
  }
  td.el-table__cell{
    border-bottom: 0px solid transparent;
  }
  .el-table__body-wrapper tr {
    background-color: rgba(255, 255, 255, 0.05);
  }
  table.el-table__body {
    tr.hover-row,
    tr.current-row,
    tr:hover {
      box-sizing: border-box;
      td.el-table__cell {
        background-color: rgba(244, 219, 103, 0.10) !important;
        .cell {
          color: rgba(244, 219, 103, 1) !important;
        }
        .cell .el-button--text span{
          color: rgba(244, 219, 103, 1) !important;
        }
      }
    }
  }
}

// 按钮样式
.dialog_btn{
  cursor: pointer;
  width: 80px;
  height: 32px;
  box-shadow: 0px 0px 4px 0px rgba(35,139,255,0.4);
  border-radius: 4px;
  border: 1px solid;
  border-image: linear-gradient(90deg, rgba(49, 165, 255, 0.4), rgba(49, 165, 255, 0.4)) 1 1;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #FFFFFF;
  margin-left: 16px;
}
.query{
  background: linear-gradient(180deg, rgba(72,117,188,0) 0%, rgba(72,117,188,0.2) 50%),
            linear-gradient(90deg, rgba(72,117,188,0) 50%, rgba(240, 240, 241, 0.6) 200%),
            linear-gradient(-180deg, rgba(72,117,188,0) 0%, rgba(72,117,188,0.2) 70%);
}
.reset{
  background: linear-gradient(180deg, rgba(72,117,188,0) 80%, rgba(72,117,188,0.2) 180%),
            linear-gradient(90deg, rgba(72,117,188,0) 90%, rgba(240, 240, 241, 0.6) 300%),
            linear-gradient(-180deg, rgba(72,117,188,0) 0%, rgba(72,117,188,0.2) 70%);
}

// 时间弹出层
.my-date-picker{
  ::v-deep .el-input__prefix{
    color: #2181F4;
  }
}
.diabloPopperClass.el-picker-panel.el-popper {
  background: rgba(0, 0, 0, 0.50) linear-gradient(226deg, rgba(0, 0, 0, 0.50) 0%, rgba(17,110,219,0) 100%);
  border-radius: 8px;
  border: 1px solid;
  border-image: linear-gradient(180deg, rgba(64, 158, 255, 1), rgba(130, 191, 255, 1), rgba(64, 158, 255, 1)) 1 1;

  div.el-date-range-picker__header {
    color: #fff;
  }

  table.el-date-table tbody {
    tr:first-child th {
      color: #fff;
    }

    .prev-month .next-month {
      color: #606266;
    }

    .available {
      color: #fff;
    }

    td span {
      // border-radius: 0;
    }

    .today span {
      color: #FFE3A6;
    }
  }

  table.is-week-mode {
    .available:hover {
      color: #FFE3A6 !important;
    }
  }

  div.el-date-range-picker__content.is-left,
  div.el-date-range-picker__content.is-right,
  div.el-picker-panel__body {
    background: rgba(19, 25, 30, .7);
    border: 1px solid #9D928D;
  }

  .el-date-table td.disabled div {
    background: lightslategray;
  }

  div.el-date-range-picker__content.is-left {
    border-right: 0.0625rem solid #325ea3;
  }

  .el-date-table td.in-range div,
  .el-date-table td.in-range div:hover,
  .el-date-table.is-week-mode .el-date-table__row.current div,
  .el-date-table.is-week-mode .el-date-table__row:hover div {
    background-color: rgba(244, 219, 103, .3);
  }

  .el-date-table td.end-date span,
  .el-date-table td.start-date span,
  .el-date-table td.current:not(.disabled) span {
    background-color: rgba(244, 219, 103, 1);
    color: #2B2B45;
  }

  .el-date-table td.start-date div,
  .el-date-table td.end-date div,
  .el-month-table td .cell {
    // border-radius: 0;
  }

  .el-date-table th,
  .el-date-picker__time-header,
  .el-date-picker__header--bordered {
    border-bottom: none;
  }

  .el-picker-panel__icon-btn,
  .el-date-picker__header-label {
    color: #fff
  }

  .el-date-table th {
    color: #7eaef9;
  }

  .el-month-table td .cell {
    color: #fff;
  }

  .el-month-table td.today .cell,
  .el-month-table td .cell:hover {
    color: #FFE3A6;
  }

  .el-month-table td.current:not(.disabled) .cell {
    background-color: #FEE4A8;
    color: #2B2B45;
  }

  .el-month-table td.disabled .cell {
    background-color: lightslategray;
    color: #C0C4CC;
  }

  .el-date-table td.end-date:hover span {
    color: #2B2B45;
  }

  table.el-date-table tbody {

    .end-date,
    .start-date {
      span {
        color: #2B2B45 !important;
      }
    }
  }

  .el-picker-panel__footer {
    border-top: none;
    background: rgba(19, 25, 30, .7) linear-gradient(226deg, #116EDB 0%, rgba(17,110,219,0) 100%);
  }

  .el-time-panel {
    background: #2B2B45;

    .el-time-spinner__item {
      color: #fff;
    }

    .el-time-spinner__item:hover:not(.disabled):not(.active) {
      background: center;
      color: #FFE3A6;
    }

    .active {
      color: #FFE3A6;
    }

    .el-time-panel__footer {
      .cancel {
        color: #FFF;
      }
    }
  }
}