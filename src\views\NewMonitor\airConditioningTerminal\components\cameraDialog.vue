<template>
  <el-dialog v-if="cameraDialogShow" title="选择关联摄像机" width="70%" :visible.sync="cameraDialogShow"
    custom-class="model-dialog" :before-close="closeDialog">
    <div class="camera_content" style="padding: 10px 20px 10px 10px;">
      <div class="header_operation">
        <div class="search_box">
          <div class="search_select">
            <el-input v-model="cameraParameter.assetsInfoName" placeholder="请输入摄像机名称"></el-input>
          </div>
          <div class="search_select">
            <el-input v-model="cameraParameter.equipIp" placeholder="请输入IP"></el-input>
          </div>
          <div class="search_select">
            <el-select ref="treeSelect" v-model="cameraParameter.spaceLocation" clearable placeholder="空间位置"
              @clear="handleClear">
              <el-option hidden :value="cameraParameter.spaceLocation" :label="areaName"> </el-option>
              <el-tree :data="serverSpaces" :props="serverDefaultProps" :load="serverLoadNode" lazy
                :expand-on-click-node="false" :check-on-click-node="true" @node-click="handleNodeClick">
              </el-tree>
            </el-select>
          </div>
          <div class="search_select">
            <el-select ref="sysOfcode" v-model="cameraParameter.sysOfcode" clearable placeholder="选择品类"
              @clear="handleClear1">
              <el-option hidden :value="cameraParameter.sysOfcode" :label="categoryName"> </el-option>
              <el-tree :data="categoryList" :props="categoryProps" :expand-on-click-node="false"
                :check-on-click-node="true" @node-click="handleNodeClick1">
              </el-tree>
            </el-select>
          </div>
        </div>
        <div class="header_btn">
          <el-button type="primary" plain @click="userReset">重置</el-button>
          <el-button type="primary" @click="userQuery">查询</el-button>
          <!-- <el-button @click="userReset">新增</el-button> -->
        </div>
      </div>
      <div class="table_div">
        <el-table ref="cameraTable" row-key="id" :data="cameraTableData" tooltip-effect="dark" style="width: 100%;"
          height="310px" border @selection-change="userSelectChange">
          <el-table-column type="selection" :reserve-selection="true" width="55" align="center"> </el-table-column>
          <el-table-column type="index" label="序号" width="55" align="center">
            <template slot-scope="scope">
              <span>{{ (pageinationData.page - 1) * pageinationData.pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="assetsName" label="摄像机名称" align="center"> </el-table-column>
          <el-table-column prop="equipIp" label="ip" align="center"> </el-table-column>
          <el-table-column prop="spaceLocation" label="空间" align="center"> </el-table-column>
          <el-table-column prop="factoryText" label="厂家" align="center"> </el-table-column>
        </el-table>
      </div>
      <div style="padding: 6px;">
        <el-pagination class="pagination" :current-page="pageinationData.page" :page-sizes="[15, 30, 50, 100]"
          :page-size="pageinationData.pageSize" layout="total, sizes, prev, pager, next, jumper"
          :total="pageinationData.total" @size-change="cameraHandleSizeChange"
          @current-change="cameraHhandleCurrentChange"></el-pagination>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="groupSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { transData, ListTree } from '@/util'
export default {
  name: 'cameraDialog',
  props: {
    cameraDialogShow: {
      type: Boolean,
      default: false
    },
    cameraDialogData: {
      type: String,
      default: ''
    },
    limitSelectLength: {
      type: Number,
      default: 4
    }
  },
  data() {
    return {
      cameraParameter: {
        assetsInfoName: '', // 名称
        equipIp: '', // ip
        spaceLocation: '' // 空间id
        // icmVidicon: "" //摄像机
      },
      // spaceList: [],
      serverSpaces: [], // 空间位置
      spaces: [], // 空间位置
      serverDefaultProps: {
        label: 'ssmName',
        isLeaf: 'leaf',
        children: 'children'
      },
      areaName: '', // 选中 下拉树的name
      categoryName: "",//品类name
      categoryList: [],//品类list
      category: [],//品类list
      categoryProps: {
        label: 'dictionaryDetailsName',
        isLeaf: 'dictionaryDetailsCode',
        children: 'children'
      },
      checkedData: [],
      cameraTableData: [], // 数据
      pageinationData: {
        page: 1,
        pageSize: 15,
        total: 0
      }
    }
  },
  mounted() {
    this.getStaffListByPage()
    this.getTreelist()
    this.getTableData()
  },
  methods: {
    // 获取列表
    getStaffListByPage() {
      let data = {
        assetsInfoId: this.$route.query.id,
        pageSize: this.pageinationData.pageSize,
        page: this.pageinationData.page,
        ...this.cameraParameter
      }
      this.$api.getOperationalMonitoringPage(data).then((res) => {
        if (res.code == 200) {
          this.cameraTableData = res.data.records
          this.pageinationData.total = res.data.total
          let cameraId = this.cameraDialogData?.split(',') ?? []
          if (cameraId.length && this.cameraTableData.length) {
            cameraId.forEach((id) => {
              let selectRow = this.cameraTableData.find((e) => e.id == id)
              if (selectRow) {
                this.$refs.cameraTable.toggleRowSelection(selectRow)
              }
            })
          }
          this.cameraTableData.forEach((row) => { })
        }
      })
    },
    // 下拉树懒加载
    serverLoadNode(node, resolve) {
      if (node.level == 0) {
        return resolve(node.data)
      } else if (node.level > 0 && node.level < 4) {
        return resolve(node.data.children)
      } else {
        return this.spaceTreeChange(node.data, resolve)
      }
    },
    // 获取服务空间树形结构
    getTreelist() {
      this.$api.getSpaceTreeList({}).then((res) => {
        if (res.code == 200) {
          this.spaces = res.data
          // 增加 懒加载节点
          res.data.map((e) => {
            e.leaf = false
          })
          // 转换为树形结构数据
          this.serverSpaces = transData(res.data, 'id', 'pid', 'children')
        }
      })
    },
    // 服务空间树结构点击获取自己及父节点ID并请求数据实现懒加载
    spaceTreeChange(value, resolve) {
      const child = ListTree(this.spaces, value.pid)
      child.push(value.id)
      let treeId = child.toString()
      let data = {
        current: 1,
        functionDictId: '',
        simCode: treeId,
        size: 999
      }
      this.$api.getSpaceInfoList(data).then((res) => {
        if (res.code == 200) {
          if (typeof resolve === 'function') {
            let treeNodeData = JSON.parse(JSON.stringify(res.data.records))
            treeNodeData.map((e) => {
              e.leaf = true
            })
            resolve(treeNodeData)
          }
        }
      })
    },
    // 空间数据清除
    handleClear() {
      this.cameraParameter.spaceLocation = ''
      this.areaName = ''
    },
    // 选择下拉树 数据
    handleNodeClick(data) {
      this.cameraParameter.spaceLocation = data.id
      this.areaName = data.ssmName
      this.$refs.treeSelect.blur()
    },
    // 品类数据下拉列表
    getTableData() {
      let data = {
        page: 1,
        pageSize: 200,
        enable: 1
      }
      this.$api
        .getCategoryManagementList(data)
        .then((res) => {
          if (res.code == '200') {
            this.category = res.data.records
            res.data.records.map((e) => {
              e.leaf = false
            })
            this.categoryList = transData(res.data.records, 'dictionaryDetailsCode', 'parentId', 'children')
          } else {
            this.$message.error(res.message)
          }
        })
    },
    // 选择品类下拉树 数据
    handleNodeClick1(data) {
      if (data.level == 1) {
        this.cameraParameter.sysOfcode = data.dictionaryDetailsCode
        this.cameraParameter.sysOf1code = ''
      } else {
        this.cameraParameter.sysOfcode = ''
        this.cameraParameter.sysOf1code = data.dictionaryDetailsCode
      }
      this.categoryName = data.dictionaryDetailsName
      this.$refs.sysOfcode.blur()
    },
    // 品类数据清除
    handleClear1() {
      this.cameraParameter.sysOfcode = ''
      this.cameraParameter.sysOf1code = ''
      this.categoryName = ''
    },
    // 弹框分页
    cameraHandleSizeChange(val) {
      this.pageinationData.pageSize = val
      this.getStaffListByPage()
    },
    cameraHhandleCurrentChange(val) {
      this.pageinationData.page = val
      this.getStaffListByPage()
    },
    // 点击新增
    // userReset() {
    //   this.$router.push({
    //     path: '/CameraManagement'
    //   })
    // },
    // 重置
    userReset() {
      this.cameraParameter = {
        assetsInfoName: '', // 名称
        equipIp: '', // ip
        spaceLocation: '', // 空间id
        sysOfcode: '',//品类1级
        sysOf1code: '',//品类2级
      }
      this.handleClear()
      this.userQuery()
    },
    // 点击查询
    userQuery() {
      this.pageinationData.page = 1
      this.getStaffListByPage()
    },
    userSelectChange(rows) {
      this.checkedData = rows

    },
    closeDialog() {
      this.$emit('closeCameraDialog')
    },
    groupSubmit() {
      if (this.checkedData.length <= this.limitSelectLength && this.checkedData.length) {
        console.log(this.checkedData, 'this.checkedData');
        this.$emit('submitCameraDialog', this.checkedData)

      } else {
        this.$message({
          message: this.checkedData.length == 0 ? '请选择至少一项' : '最多选中' + this.$tools.toChinesNum(this.limitSelectLength) + '条',
          type: 'warning'
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.model-dialog {
  .camera_content {
    margin: 0 auto;
    background: #fff;
    border-radius: 4px;
    // display: flex;
    width: 100%;
    height: 100%;

    .header_operation {
      // padding: 24px;
      margin-bottom: 10px;
      display: flex;

      .search_box {
        display: flex;

        >div {
          margin-right: 16px;
        }

        .search_input {
          width: 200px;
        }
      }
    }

    .table_div {
      height: calc(100% - 100px);
    }

    .paging_box {
      display: flex;
      justify-content: flex-end;
      padding: 6px;
    }
  }
}
</style>
