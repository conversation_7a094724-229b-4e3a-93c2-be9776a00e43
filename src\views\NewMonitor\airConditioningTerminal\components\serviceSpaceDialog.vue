<template>
  <el-dialog v-if="serviceSpaceDialogShow" title="选择服务的空间" :visible.sync="serviceSpaceDialogShow"
    custom-class="model-dialog" :before-close="closeDialog">
    <div class="sceneForm_content" style="padding: 10px 20px 10px 10px;">
      <el-tree ref="tree" node-key="id" show-checkbox :props="serverDefaultProps"
        :default-expanded-keys="serverExpandKeys" :default-checked-keys="serverTreechecked" :data="serverSpaces"
        :load="serverLoadNode" lazy @node-click="spaceTreeChange" @check="spacesTreeChange">
      </el-tree>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="groupSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { transData } from '@/util'
export default {
  name: 'serviceSpaceDialog',
  props: {
    serviceSpaceDialogShow: {
      type: Boolean,
      default: false
    },
    spaceData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      serverDefaultProps: {
        label: 'ssmName',
        isLeaf: 'leaf',
        children: 'children'
      },
      spaces: [],
      treechecked: [],
      serverExpandKeys: [], // 服务空间默认展开
      serverTreechecked: [], // 服务空间回显
      serverSpaces: [] // 服务空间 tree数据
    }
  },
  mounted() {
    this.spaceNames = []
    console.log(this.spaceNames)
    this.spaceData?.forEach((item) => {
      this.spaceNames.push(item)
      // 服务空间 初始化 选中
      if (item.id) {
        this.serverExpandKeys.push(item.parentId)
        this.treechecked.push(item.id)
      } else {
        if (item.parentId) {
          this.treechecked.push(item.parentId)
        } else {
          if (item.constructId) {
            this.treechecked.push(item.constructId)
          }
        }
      }
    })
    this.getTreelist()
  },
  methods: {
    // 获取服务空间树形结构
    getTreelist() {
      let data = {}
      this.$api.getStructureTree(data).then((res) => {
        if (res.code == 200) {
          this.spaces = res.data
          // 增加 懒加载节点
          res.data.map((e) => {
            e.leaf = e.leaf == 0
            // e.leaf = false
            e.disabled = true
            // 懒加载 默认选中节点
            if (e.ssmType != 4 && !this.serverExpandKeys.includes(e.id)) {
              this.serverExpandKeys.push(e.id)
            }
          })
          // 转换为树形结构数据
          this.serverSpaces = transData(res.data, 'id', 'pid', 'children')
        }
      })
    },
    serverLoadNode(node, resolve) {
      if (node.level == 0) {
        return resolve(node.data)
      } else if (node.level > 0 && node.level < 4) {
        // console.log(node.data.children, 'node.data')
        return resolve(node.data.children || [])
      } else {
        return this.spaceTreeChange(node.data, resolve)
      }
    },
    // 服务空间树结构点击获取自己及父节点ID并请求数据实现懒加载
    spaceTreeChange(value, resolve) {
      const child = this.ListTree(this.spaces, value.pid)
      const childList = this.spaces.filter((item) => child.includes(item.id))
      const childNameList = Array.from(childList, ({ ssmName }) => ssmName)
      child.push(value.id)
      childNameList.push(value.ssmName)
      let treeId = child.toString()
      let treeName = childNameList.toString()
      // if (typeof resolve === "function") {
      // currentPage = 1;
      // pageSize = 999;
      // }
      let data = {
        current: 1,
        functionDictId: '',
        simCode: treeId,
        size: 999
      }
      this.$api.getSpaceInfoList(data).then((res) => {
        if (res.code == 200) {
          if (typeof resolve === 'function') {
            let treeNodeData = JSON.parse(JSON.stringify(res.data.records))
            // console.log(treeNodeData,"treeNodeData");
            treeNodeData.map((e) => {
              e.modelCode = e.id
              e.parentGroupId = treeId
              e.parentGroupName = treeName
              e.leaf = true
            })
            // console.log(treeNodeData,"treeNodeData");
            resolve(treeNodeData)
            // if (this.setType == "edit") {
            this.$nextTick(() => {
              this.$refs.tree.setCheckedKeys(this.treechecked)
            })
            // }
          }
          //  else {
          //   this.spaceDate = res.data.records;
          // }
        }
      })
    },
    // 服务空间树结构切换
    spacesTreeChange() {
      // console.log(this.$refs.tree.getCheckedNodes(true), "this.$refs.tree");
      this.treechecked = []
      // 获取已选中的数据
      let checkSpaceData = this.$refs.tree.getCheckedNodes(true)
      // 获取提交可使用的参数格式
      this.spaceNames = checkSpaceData?.map((item) => {
        if (!item.parentGroupId && !item.parentGroupName) {
          return item
        } else {
          let codeList = item.parentGroupId.split(',')
          let nameList = item.parentGroupName.split(',')
          return {
            constructId: codeList[2],
            constructName: nameList[2],
            floorId: codeList[3],
            floorName: nameList[3],
            spaceType: item.functionDictId,
            id: item.id,
            allParentName: item.allSpaceName,
            gridName: item.ssmName ?? item.id,
          }
        }
      })
      // console.log(this.spaceNames, "this.spaceNames");
      // 获取已选中的末层节点 并赋值
      this.spaceNames?.forEach((item) => {
        // 服务空间 初始化 选中
        if (item.id) {
          this.treechecked.push(item.id)
        } else {
          if (item.floorId) {
            this.treechecked.push(item.floorId)
          } else {
            if (item.constructId) {
              this.treechecked.push(item.constructId)
            }
          }
        }
      })
      // console.log(this.treechecked, "this.treechecked");
    },
    // 封装的方法--获取当前节点和父节点ID
    ListTree(list, rootid) {
      var arr = []
      list.forEach((item) => {
        if (item.id === rootid) {
          arr.push(item.id)
          const newArr = this.ListTree(list, item.pid)
          if (newArr) {
            arr.unshift(...newArr)
          }
        }
      })
      return arr
    },
    closeDialog() {
      this.$emit('closeServiceSpaceDialog')
    },
    groupSubmit() {
      // this.spaceNames = this.$refs.tree.getCheckedNodes(true);
      let arr = Array.from(this.spaceNames, ({ gridName }) => gridName)
      this.$emit('submitServiceSpaceDialog', {
        spaceNames: this.spaceNames,
        spacesName: arr.toString()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.model-dialog {

  // width: 30% !important;
  // height: 30% !important;
  // min-width: 562px !important;
  // min-height: 376px !important;
  .sceneForm_content {
    margin: 0 auto;
    background: #fff;
    border-radius: 4px;
    width: 100%;
    // height: 100%;
    height: 55vh;
    overflow-y: scroll;
  }
}
</style>
