<template>
  <PageContainer>
    <div slot="content">
      <div class="wc top-box">
        <div class="completion-rate">
          <div class="title">
            <svg-icon name="right-arrow" />
            <span>任务完成率</span>
          </div>
          <div class="total-cost">
            <span>完成率</span>
            <span style="color: #121f3e;">{{ completionRate }}%</span>
            <span>任务数</span>
            <span @click="gotaskList()">{{ taskNum }}</span>
          </div>
          <div id="completionRateChart"></div>
          <div class="btns">
            <span :class="{ 'active-btn': completionRateType == 'day' }" @click="changeCompletionRateType('day')">今日</span>
            <span :class="{ 'active-btn': completionRateType == 'month' }" @click="changeCompletionRateType('month')">本月</span>
            <span :class="{ 'active-btn': completionRateType == 'year' }" @click="changeCompletionRateType('year')">本年</span>
          </div>
        </div>
        <div class="last-10">
          <div class="title">
            <svg-icon name="right-arrow" />
            <span>近10日任务执行情况</span>
          </div>
          <div id="last10Chart"></div>
        </div>
      </div>
      <div class="wc bottom-box">
        <div class="table-1">
          <div class="title">
            <svg-icon name="right-arrow" />
            <span>部门任务执行分析</span>
          </div>
          <el-table v-loading="loading" :data="departmentTaskTableData" border style="width: 100%;" height="85%" size="small">
            <el-table-column prop="teamName" width="200" label="部门" align="center" show-overflow-tooltip :resizable="false"> </el-table-column>
            <el-table-column prop="count" label="任务总数" align="center" show-overflow-tooltip :resizable="false">
              <template slot-scope="scope">
                <span class="click-style" @click="gotaskList2('1', scope.row.id)">{{ scope.row.count || '0' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="hasCount" label="已完成" align="center" show-overflow-tooltip :resizable="false">
              <template slot-scope="scope">
                <span class="click-style" @click="gotaskList2('2', scope.row.id)">{{ scope.row.hasCount || '0' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="percentage" label="完成率" align="center" show-overflow-tooltip :resizable="false">
              <template slot-scope="scope">
                <span>{{ scope.row.percentage }}%</span>
              </template>
            </el-table-column>
          </el-table>
          <div class="btns">
            <span :class="{ 'active-btn': departmentalTaskType == 'day' }" @click="changeDepartmentalTaskType('day')">今日</span>
            <span :class="{ 'active-btn': departmentalTaskType == 'month' }" @click="changeDepartmentalTaskType('month')">本月</span>
            <span :class="{ 'active-btn': departmentalTaskType == 'year' }" @click="changeDepartmentalTaskType('year')">本年</span>
          </div>
        </div>
        <div class="table-2">
          <div class="title">
            <svg-icon name="right-arrow" />
            <span>巡检点分析</span>
          </div>
          <el-table v-loading="loading2" :data="taskPointData" border style="width: 100%;" height="85%" size="small">
            <el-table-column prop="taskPointName" width="200" label="巡检点" align="center" show-overflow-tooltip :resizable="false">
              <template slot-scope="scope">
                <span>{{ scope.row.taskPointName || '无' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="sum" label="应巡次数" align="center" show-overflow-tooltip :resizable="false">
              <template slot-scope="scope">
                <span class="click-style" @click="goPointList('point', '', scope.row.taskPointId)">{{ scope.row.sum || '0' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="accomplishCount" label="实巡次数" align="center" show-overflow-tooltip :resizable="false">
              <template slot-scope="scope">
                <span class="click-style" @click="goPointList('point', '1', scope.row.taskPointId)">{{ scope.row.accomplishCount || '0' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="percentage" label="完成率" align="center" show-overflow-tooltip :resizable="false">
              <template slot-scope="scope">
                <span>{{ scope.row.percentage }}%</span>
              </template>
            </el-table-column>
          </el-table>
          <div class="btns">
            <span :class="{ 'active-btn': taskPointType == 'day' }" @click="changeTaskPointType('day')">今日</span>
            <span :class="{ 'active-btn': taskPointType == 'month' }" @click="changeTaskPointType('month')">本月</span>
            <span :class="{ 'active-btn': taskPointType == 'year' }" @click="changeTaskPointType('year')">本年</span>
          </div>
        </div>
        <div class="table-3">
          <div class="title">
            <svg-icon name="right-arrow" />
            <span>巡检合格率</span>
          </div>
          <el-table v-loading="loading3" :data="tableData" border style="width: 100%;" height="85%" size="small">
            <el-table-column prop="planName" width="200" label="计划名称" align="center" show-overflow-tooltip :resizable="false"> </el-table-column>
            <el-table-column prop="accomplishCount" label="实巡点数" align="center" show-overflow-tooltip :resizable="false">
              <template slot-scope="scope">
                <span class="click-style" @click="goPointList('plan', '', scope.row.planId)">{{ scope.row.accomplishCount || '0' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="qualifiedCount" label="合格点数" align="center" show-overflow-tooltip :resizable="false">
              <template slot-scope="scope">
                <span class="click-style" @click="goPointList('plan', '2', scope.row.planId)">{{ scope.row.qualifiedCount || '0' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="percentage" label="合格率" align="center" show-overflow-tooltip :resizable="false">
              <template slot-scope="scope">
                <span>{{ scope.row.percentage }}%</span>
              </template>
            </el-table-column>
          </el-table>
          <div class="btns">
            <span :class="{ 'active-btn': percentOfPassType == 'day' }" @click="changePercentOfPassType('day')">今日</span>
            <span :class="{ 'active-btn': percentOfPassType == 'month' }" @click="changePercentOfPassType('month')">本月</span>
            <span :class="{ 'active-btn': percentOfPassType == 'year' }" @click="changePercentOfPassType('year')">本年</span>
          </div>
        </div>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import * as echarts from 'echarts'
export default {
  name: 'taskManagement',
  data() {
    return {
      completionRateType: 'month',
      departmentalTaskType: 'month',
      taskPointType: 'month',
      percentOfPassType: 'month',
      departmentTaskTableData: [],
      tableData: [],
      completionRate: 0,
      taskNum: 0,
      last10Data1: [],
      last10Data2: [],
      loading: false,
      loading2: false,
      loading3: false,
      taskPointData: []
    }
  },
  watch: {
    '$store.state.settings.sidebarCollapse': {
      handler(val) {
        this.$nextTick(() => {
          let echartsDom = ['completionRateChart', 'last10Chart']
          setTimeout(() => {
            echartsDom.forEach((item) => {
              echarts.init(document.getElementById(item)).resize()
            })
          }, 250)
        })
      },
      deep: true
    }
  },
  mounted() {
    this.getCompletionRateChartData()
    this.getLast10ChartData()
    this.getDepartmentTask()
    this.getTaskPointListData()
    this.getPercentOfPassList()
  },
  methods: {
    goPointList(chartType, ststus, id) {
      if (chartType == 'point') {
        this.$router.push({ path: '/cleaningService/inspectionPointList', query: { dateType: this.taskPointType, chartType, taskStatus: ststus, pointCode: id } })
      } else {
        this.$router.push({ path: '/cleaningService/inspectionPointList', query: { dateType: this.percentOfPassType, chartType, taskStatus: ststus, pointCode: id } })
      }
    },
    gotaskList() {
      this.$router.push({ path: '/cleaningService/inspectionTaskList', query: { dateType: this.completionRateType, chartType: 'rate', taskStatus: '' } })
    },
    gotaskList2(type, teamId) {
      // 任务总数
      if (type == '1') {
        this.$router.push({ path: '/cleaningService/inspectionTaskList', query: { teamId, dateType: this.departmentalTaskType, chartType: 'department', taskStatus: '' } })
      } else if (type == '2') {
        this.$router.push({ path: '/cleaningService/inspectionTaskList', query: { teamId, dateType: this.departmentalTaskType, chartType: 'department', taskStatus: '2' } })
        // 已完成
      }
    },
    getPercentOfPassList() {
      let params = {
        dateType: this.percentOfPassType,
        planTypeId: 'cleaning'
        // pageSize: 100,
        // pageNo: 1
      }
      this.loading3 = true
      this.$api.getPercentOfPassList(params).then((res) => {
        this.loading3 = false
        if (res.code == 200) {
          this.tableData = res.data.list
        }
      })
    },
    getTaskPointListData() {
      let params = {
        planTypeId: 'cleaning',
        dateType: this.taskPointType
        // pageSize: 100,
        // pageNo: 1
      }
      this.loading2 = true
      this.$api.taskPointList(params).then((res) => {
        this.loading2 = false
        if (res.code == 200) {
          this.taskPointData = res.data.list
        }
      })
    },
    getDepartmentTask() {
      let params = {
        dateType: this.departmentalTaskType,
        planTypeId: 'cleaning',
        pageSize: 100,
        pageNo: 1
      }
      this.loading = true
      this.$api.departmentTask(params).then((res) => {
        this.loading = false
        if (res.code == 200) {
          this.departmentTaskTableData = res.data.list
        }
      })
    },
    getLast10ChartData() {
      let params = {
        planTypeId: 'cleaning'
      }
      this.$api.getDailyTaskList(params).then((res) => {
        if (res.code == 200) {
          let arr1 = []
          let arr2 = []
          res.data.list.forEach((item) => {
            arr1.push({
              name: item.createTime,
              value: item.accomplishCount
            })
            arr2.push({
              name: item.createTime,
              value: item.unfinishedCount
            })
          })
          this.last10Data1 = arr1
          this.last10Data2 = arr2
          this.initLast10Chart()
        }
      })
    },
    getCompletionRateChartData() {
      let params = {
        dateType: this.completionRateType,
        planTypeId: 'cleaning'
      }
      this.$api.onePersonTaskQuantity(params).then((res) => {
        if (res.code == 200) {
          this.taskNum = res.data.list[0].sum
          this.completionRate = res.data.list[0].percentage
          this.initCompletionRateChart()
        }
      })
    },
    changeCompletionRateType(type) {
      this.completionRateType = type
      this.getCompletionRateChartData()
    },
    changeDepartmentalTaskType(type) {
      this.departmentalTaskType = type
      this.getDepartmentTask()
    },
    changeTaskPointType(type) {
      this.taskPointType = type
      this.getTaskPointListData()
    },
    changePercentOfPassType(type) {
      this.percentOfPassType = type
      this.getPercentOfPassList()
    },
    initCompletionRateChart() {
      const getchart = echarts.init(document.getElementById('completionRateChart'))
      const option = {
        series: [
          {
            type: 'gauge',
            radius: '90%',
            center: ['50%', '55%'],
            itemStyle: {
              color: '#3562db'
            },
            progress: {
              show: true,
              width: 10
            },
            axisLine: {
              lineStyle: {
                width: 10
              }
            },
            axisTick: {
              show: false
            },
            splitLine: {
              length: 8,
              lineStyle: {
                width: 2,
                color: '#999'
              }
            },
            axisLabel: {
              distance: 16,
              color: '#999',
              fontSize: 14
            },
            anchor: {
              show: true,
              showAbove: true,
              size: 25,
              itemStyle: {
                borderWidth: 5,
                borderColor: '#3562db'
              }
            },
            title: {
              show: false
            },
            detail: {
              show: false,
              valueAnimation: true,
              fontSize: 24,
              offsetCenter: [0, '70%']
            },
            data: [
              {
                value: this.completionRate
              }
            ]
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    initLast10Chart() {
      // const data = this.last10Data1
      // data.sort((a, b) => {
      //   return a.value - b.value
      // })
      const nameList = Array.from(this.last10Data1, ({ name }) => name.slice(5))
      const getchart = echarts.init(document.getElementById('last10Chart'))
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          top: '12%',
          left: '4%',
          right: '5%',
          bottom: '1%',
          containLabel: true
        },
        yAxis: {
          type: 'value'
        },
        legend: {},
        xAxis: [
          {
            type: 'category',
            data: nameList,
            axisLabel: {
              formatter: function (val) {
                var strs = val.split('') // 字符串数组
                var str = ''
                // strs循环 forEach 每五个字符增加换行符
                strs.forEach((item, index) => {
                  if (index % 6 === 0 && index !== 0) {
                    str += '\n'
                  }
                  str += item
                })
                return str
              }
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            }
          }
        ],
        series: [
          {
            name: '未巡检',
            type: 'bar',
            stack: 'bb',
            barWidth: 10,
            data: this.last10Data2,
            label: {
              show: false
            },
            itemStyle: {
              color: '#FA403C'
            }
          },
          {
            name: '已巡检',
            type: 'bar',
            stack: 'bb',
            barWidth: 10,
            data: this.last10Data1,
            label: {
              show: false
            },
            itemStyle: {
              color: '#08CB83'
            }
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.container-content > div {
  height: 100%;
  display: flex;
  align-items: apace-between;
  flex-wrap: wrap;
}

.wc {
  width: 100%;
  border-radius: 4px;
}

.top-box {
  height: 49%;
  display: flex;
  justify-content: space-between;
}

.top-box > div {
  background-color: #fff;
  padding: 12px;
  box-sizing: border-box;
}

.completion-rate {
  width: 33%;
  position: relative;
}

.last-10 {
  width: 66.5%;
}

.bottom-box {
  height: 49%;
  display: flex;
  justify-content: space-between;
}

.bottom-box > div {
  position: relative;
  padding: 12px;
  width: 33%;
  box-sizing: border-box;
  background-color: #fff;
}

.title span {
  font-size: 15px;
}

.btns {
  position: absolute;
  width: 140px;
  height: 24px;
  right: 3%;
  top: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.btns > span {
  width: 30%;
  background-color: #f6f5fa;
  font-size: 12px;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #ededf5;
  color: #7f848c;
  cursor: pointer;
}

.active-btn {
  background-color: #3562db !important;
  color: #fff !important;
  border-color: #3562db !important;
}

.total-cost {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 7%;
}

.total-cost > span:nth-child(1),
.total-cost > span:nth-child(3) {
  font-size: 15px;
  color: #121f3e;
  margin-right: 12px;
}

.total-cost > span:nth-child(2),
.total-cost > span:nth-child(4) {
  font-size: 20px;
  color: #3562db;
  font-weight: 700;
  margin-right: 5px;
}

.total-cost > span:nth-child(2) {
  margin-right: 24px;
}

.total-cost > span:nth-child(4) {
  cursor: pointer;
}

.total-cost > span:nth-child(4):hover {
  text-decoration: underline;
}

#completionRateChart,
#last10Chart {
  width: 100%;
  height: 75%;
}

#last10Chart {
  margin-top: 5%;
}

.el-table {
  margin-top: 16px;
}

.click-style {
  cursor: pointer;
  color: #3562db;
}

.click-style:hover {
  text-decoration: underline;
}
</style>
