<template>
  <el-dialog :title="type === '1' ? '编辑文件夹' : '创建文件夹'" :visible="visible" width="560px"
    :before-close="handleClosesubmit">
    <el-form ref="form" label-position="right" label-width="100px" :model="formData" :rules="rules">
      <el-row :gutter="24" style="margin: 0">
        <el-col :md="24">
          <el-form-item label="文件夹名称" prop="folderName">
            <el-input v-model="formData.folderName" placeholder="请输入文件夹名称"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer">
      <el-button @click="handleClosesubmit">取消</el-button>
      <el-button type="primary" @click="handleSubmit">
        {{ type === '1' ? '保存' : '创建' }}
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import { mapGetters } from 'vuex'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    currentNode: {
      type: Object,
      default: () => null
    },
    type: {
      type: String,
      default: '0'
    },
    folderType: {
      type: String,
      default: '1'
    }
  },
  data() {
    return {
      formData: { folderName: '', visibleRange: '0' },
      rules: {
        folderName: [
          {
            required: true,
            message: '请输入文件夹名称',
            trigger: ['change', 'blur']
          }
        ],
      },
      disabledId: ''
    }
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  watch: {
    visible: {
      handler(val) {
        if (val && this.type === '1') {
          this.$nextTick(() => {
            this.handleGetData()
          })
        }
      },
      immediate: true
    }
  },
  methods: {
    handleGetData() {
      this.$api.fileManagement.folderDetail({ id: this.currentNode.id }).then((res) => {
        const { folderName, folderId, visibleRange } = res.data
        this.formData = {
          folderName,
          folderId,
          visibleRange,
        }
      })
    },
    handleClosesubmit() {
      this.$emit('update:visible', false)
    },
    handleSubmit() {
      this.$refs.form.validate((res) => {
        if (res) {
          const isEdit = this.type === '1'
          const params = {
            ...this.formData,
            folderType: this.folderType
          }
          const { folderId } = this.formData
          const { id: parentId } = this.currentNode
          if (isEdit) {
            params.folderId = folderId
          } else {
            params.parentId = parentId
          }
          const func = isEdit ? this.$api.fileManagement.updateFolder : this.$api.fileManagement.insertFolder
          func(params).then((res) => {
            if (res.code === '200') {
              const text = isEdit ? '保存' : '创建'
              this.$message.success(text + '成功')
              this.handleClosesubmit()
              this.$emit('success')
            } else {
              this.$message.error('操作失败')
            }
          })
        }
      })
    }
  }
}
</script>
<style></style>
