<template>
  <div class="btn-container">
    <el-button v-for="btn in btnList" :key="btn.id" :type="btn.type" class="common-btn" :loading="doPublicLoading" @click="handleClick(btn.fun, btn.path)">
      <i :class="btn.class" style="margin-right: 5px;"></i>
      {{ btn.textName }}
    </el-button>
  </div>
</template>

<script>
import utils from '../../../utils/utils'
// import axiosConter from '../../../axios/centralControl'
export default {
  name: 'headerButton',
  props: {
    btnName: {
      type: String
    }
  },
  data() {
    return {
      doPublicLoading: false, // 按钮--遮罩层标识
      varObje: {
        goBack: {
          id: utils.guid(),
          type: 'primary',
          fun: 'goBack',
          textName: '返回',
          icon: 'el-icon-refresh',
          path: '' // 方法的参数,
        },
        0: {
          id: utils.guid(),
          type: 'primary',
          fun: 'stop',
          flag: 'start',
          textName: '开始',
          class: 'el-icon-video-play',
          path: '1'
        },
        1: {
          id: utils.guid(),
          type: 'primary',
          fun: 'stop',
          flag: 'stop',
          textName: '暂停',
          class: 'el-icon-video-pause',
          path: '0'
        }
      },
      btnList: [
        {
          id: utils.guid(),
          type: 'primary',
          textName: '发布',
          fun: 'release',
          flag: 'release',
          class: 'el-icon-position',
          path: ''
        },
        {
          id: utils.guid(),
          type: 'primary',
          fun: 'router',
          flag: 'scan',
          textName: '预览',
          class: 'el-icon-view',
          path: '/degreeSatisfaction/questManagement/preview'
        },

        {
          id: utils.guid(),
          type: 'primary',
          fun: 'router',
          textName: '返回列表',
          class: 'el-icon-refresh-left',
          path: '/degreeSatisfaction/questManagement'
        }
      ]
    }
  },
  mounted() {},
  created() {
    // 查询问卷信息
    this.init()
    this.initGoBackBtn()
    console.log(localStorage.getItem('questId'), '86468413')
  },

  methods: {
    initGoBackBtn() {
      if (this.varObje[this.btnName]) {
        for (let index = 0; index < this.btnList.length; index++) {
          const item = this.btnList[index]
          if (item.flag === 'scan') {
            this.btnList[index] = this.varObje[this.btnName]
          }
        }
      }
    },
    init() {
      let params = {
        questionId: localStorage.getItem('questId')
      }
      this.$api.findPaper(params).then((res) => {
        console.log(res, 'res')
        if (res.status == 200) {
          this.jugem(res.data)
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 判断显示那个按钮 发布 暂停 开始
    jugem(val) {
      console.log(val.status, 'val.status')
      if (val.status != 'design') {
        for (var a = 0; a < this.btnList.length; a++) {
          // 删除开始和发布和暂停
          if (['release', 'stop', 'start'].includes(this.btnList[a].flag)) {
            this.btnList.splice(a, 1)
          }
        }
        // 把开始或者暂停保存到 数组中
        this.btnList.unshift(this.varObje[val.statusRun])
        console.log(this.btnList, 'vthis.btnList')
      }
    },
    //  发布接口
    release(path) {
      // let questionLength = localStorage.getItem('questionLength')
      // questionLength = JSON.parse(questionLength)
      // if (!questionLength) return this.$message.error('请先进行添加问题')

      this.doPublicLoading = true
      this.$api.publishPvq({ id: localStorage.getItem('questId') }).then((res) => {
        if (res.status == 200) {
          this.$message({
            message: res.message,
            type: 'success'
          })
        } else {
          this.$message.error(res.message)
        }
        this.doPublicLoading = false
      })
      // axiosConter.centralControl(
      //   'publicPvq',
      //   (res) => {
      //     if (res.code == 200) {
      //       utils.setLocalStorage('localData', res.data)
      //       this.init()
      //       this.$message.success(res.message)
      //     } else {
      //       this.$message.error(res.message)
      //     }
      //     this.doPublicLoading = false
      //   },
      //   data
      // )
    },
    // 暂停或者开始问卷接口
    stop(path) {
      var data = {
        id: localStorage.getItem('questId'), // id:问卷ID,  statusRun: 暂停 0  正常 1 删除 2
        statusRun: path
      }
      this.$api.changePvqStatus(data).then((res) => {
        if (res.status == 200) {
          this.init()
          this.$message.success(res.message)
        } else {
          this.$message.error(res.message)
        }
      })
    },
    goBack() {
      this.$router.go(-1)
    },
    // 路由跳转函数
    router(path) {
      console.log(path, 'name')
      this.$router.push({
        path
      })
    },
    // 中控
    handleClick(fun, path) {
      console.log(fun, path, 'fun, path')
      this[fun](path)
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
.btn-container {
  height: 54px;
  line-height: 54px;
  text-align: right;
  color: #606266;
  border-top-right-radius: 10px;
  border-top-left-radius: 10px;
  padding-right: 20px;

  .common-btn {
    width: auto;
  }
}
</style>
