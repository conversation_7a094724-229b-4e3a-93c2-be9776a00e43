<template>
  <div class="conten">
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-form-item :label="systemType == '2' ? '保养人员:' : '巡检人员:'" prop="implementPersonName">
        <el-select v-model="form.implementPersonName" filterable placeholder="请选择人员" style="width: 100%">
          <el-option v-for="item in personList" :key="item.id" :label="item.staffName" :value="item.id"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="systemType == '2' ? '保养状态:' : '巡检状态:'" prop="taskStatus">
        <el-radio v-model="form.taskStatus" label="1">
          {{ systemType == '2' ? '完成全部保养' : '完成全部巡检' }}
        </el-radio>
      </el-form-item>
      <el-form-item :label="systemType == '2' ? '保养情况说明:' : '巡检情况说明:'" prop="details">
        <el-input
          v-model="form.details"
          type="textarea"
          :placeholder="systemType == '2' ? '请填写保养情况说明' : '请填写巡检情况说明'"
          resize="none"
          :autosize="{ minRows: 3, maxRows: 3 }"
          maxlength="200"
          show-word-limit
        >
        </el-input>
      </el-form-item>
      <el-form-item label="图片:" prop="image">
        <el-upload
          action="string"
          list-type="picture-card"
          :multiple="true"
          :file-list="fileList"
          :on-change="fileChange"
          :on-preview="handlePictureCardPreview"
          :on-remove="deletImg"
          :http-request="handleUpload"
          :before-upload="beforeUpload"
          accept=".jpg,.jpeg,.png"
          :limit="3"
          :on-exceed="exceedMeth"
        >
          <i class="el-icon-plus"></i>
          <div slot="tip" class="el-upload__tip">最多上传三张jpg/png文件</div>
        </el-upload>
        <el-dialog :visible.sync="dialogVisible" :append-to-body="true">
          <img width="100%" :src="dialogImageUrl" alt="" />
        </el-dialog>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import axios from 'axios'
export default {
  components: {},
  props: ['detail'],
  data() {
    return {
      systemType: '',
      dialogVisible: false,
      dialogImageUrl: '',
      form: {
        implementPersonName: '',
        taskStatus: '1',
        details: ''
      },
      rules: {
        implementPersonName: [{ required: true, message: '请选择巡检人员', trigger: 'blur' }],
        taskStatus: [{ required: true }]
      },
      personList: [],
      fileList: [],
      attachmentUrl: ''
    }
  },
  created() {
    if (this.$route.path.indexOf('/InspectionManagement') != -1) {
      this.systemType = '1'
    } else if (this.$route.path.indexOf('/MaintenanceManagement') != -1) {
      this.systemType = '2'
    } else if (this.$route.path.indexOf('/comInspectionManagement') != -1) {
      this.systemType = '3'
    }
    if (this.detail.planPersonName) {
      const persons = this.detail.planPersonName.split(',')
      const personCodes = this.detail.planPersonCode.split(',')
      persons.forEach((i, index) => {
        const item = {
          staffName: i,
          id: personCodes[index]
        }
        this.personList.push(item)
      })
      if (this.personList.length == 1) {
        this.form.implementPersonName = this.personList[0].id
      }
    } else {
      let params = {
        current: 1,
        size: 9999,
        sex: '',
        pmId: '',
        postId: '', // 岗位
        stationStatus: '',
        officeId: this.detail.distributionTeamId
      }
      this.$api.staffList(params).then((res) => {
        if (res.code == 200) {
          this.personList = res.data.records
        }
      })
    }
  },
  methods: {
    beforeUpload(file) {
      let fileName = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()
      if (fileName != 'png' && fileName != 'jpg' && fileName != 'bmp' && fileName != 'tif' && fileName != 'gif' && fileName != 'jpeg') {
        this.$message({
          message: '上传图片只能是png、jpg、jpeg、bmp、gif、tif格式',
          type: 'warning'
        })
        return false
      }
      let sizes = 0
      this.fileList.forEach((i) => {
        sizes += i.raw.size
      })
      if (sizes / 1024 > 10000) {
        this.$message({
          type: 'warning',
          message: '上传文件大小不能超过10MB'
        })
        return false
      }
    },
    // 图片超过最大长度时的事件
    exceedMeth() {
      this.$message({
        type: 'info',
        message: '最多上传3张图片'
      })
    },
    // 图片上传
    handleUpload() {
      this.fileList.forEach((i) => {
        return (i.status = 'uploading')
      })
      const urlData = new FormData()
      this.fileList.forEach((item) => {
        urlData.append('file', item.raw)
        urlData.append('platform', 1)
      })
      axios({
        method: 'post',
        url: __PATH.VUE_ICIS_API + 'file/upload',
        data: urlData,
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      })
        .then((res) => {
          this.fileList.forEach((i) => {
            return (i.status = 'done')
          })
          this.attachmentUrl = res.data.data.fileKey
        })
        .catch(() => {
          this.$message.error(res.data.message)
        })
    },
    deletImg(file, fileList) {
      this.fileList = fileList
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    fileChange(file, fileList) {
      this.fileList = fileList
    },
    changeStatusSave() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.fileList.every((i) => (i.status = 'done'))) {
            const params = {
              id: this.detail.id,
              updateCode: this.$store.state.user.userInfo.userId,
              updateName: this.$store.state.user.userInfo.username,
              taskStatus: 2,
              attachmentUrl: this.attachmentUrl,
              implementPersonCode: this.form.implementPersonName,
              implementPersonName: this.personList.find((i) => i.id == this.form.implementPersonName).staffName || '',
              details: this.form.details,
              planPersonCode: this.detail.planPersonCode,
              planPersonName: this.detail.planPersonName,
              distributionTeamId: this.detail.distributionTeamId,
              distributionTeamName: this.detail.distributionTeamName
            }
            this.$api.taskStatusUpdata(params).then((res) => {
              if (res.code == '200') {
                this.$message({
                  type: 'success',
                  message: '状态修改成功'
                })
                this.$emit('updata')
              } else {
                this.$message({
                  type: 'error',
                  message: res.message || '修改失败'
                })
              }
            })
          } else {
            this.$message({
              type: 'error',
              message: '当前有图片正在上传'
            })
          }
        } else {
          return false
        }
      })
    }
  }
}
</script>
