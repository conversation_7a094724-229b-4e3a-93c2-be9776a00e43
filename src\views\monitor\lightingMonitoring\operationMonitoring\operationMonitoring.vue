<template>
  <PageContainer title class="OperationMonitoring">
    <template #content>
      <div class="OperationMonitoring__left">
        <ZkRenderTree
          ref="treeRef"
          v-loading="treeLoading"
          :check-strictly="true"
          :data="treeData"
          :props="defaultProps"
          class="OperationMonitoring__tree"
          node-key="id"
          :highlight-current="true"
          @node-click="handleNodeClick"
        >
        </ZkRenderTree>
      </div>
      <div class="OperationMonitoring__right">
        <div class="OperationMonitoring__header">
          <el-form ref="formRef" :model="searchFrom" class="OperationMonitoring__header__form" label-position="left" inline>
            <el-form-item prop="name">
              <el-input v-model="searchFrom.name" placeholder="监测项名称" suffix-icon="el-icon-search" clearable></el-input>
            </el-form-item>
            <!-- <el-form-item prop="lightingStatus">
              <el-select v-model="searchFrom.lightingStatus" placeholder="请选择状态" clearable>
                <el-option label="开启" value="1"></el-option>
                <el-option label="关闭" value="0"></el-option>
              </el-select>
            </el-form-item> -->
            <el-form-item>
              <el-button type="primary" plain @click="onReset">重置</el-button>
              <el-button type="primary" @click="onSearch">查询</el-button>
            </el-form-item>
          </el-form>
          <div class="OperationMonitoring__header__action">
            <div class="pattern-item" @click="switchPattern(viewType.GRAPH)">
              <svg-icon :name="currentViewType === 1 ? 'chartModeActive' : 'chartMode'" class="pattern-icon" />
              <span :style="{ color: currentViewType === 1 ? '#3562DB' : '#414653' }">图形模式</span>
            </div>
            <div class="pattern-item" @click="switchPattern(viewType.LIST)">
              <svg-icon :name="currentViewType === 2 ? 'listModeActive' : 'listMode'" class="pattern-icon" />
              <span :style="{ color: currentViewType === 2 ? '#3562DB' : '#414653' }">列表模式</span>
            </div>
          </div>
        </div>
        <div class="OperationMonitoring__content">
          <GraphicsMode v-if="currentViewType === viewType.GRAPH" ref="scadaRef" :requestHttp="requestHttp" :entityMenuCode="currentNodeCode" :projectId="projectCode" />
          <LightingList v-else ref="listRef" :query="queryParam" />
        </div>
      </div>
    </template>
  </PageContainer>
</template>
<script>
import { monitorTypeList } from '@/util/dict'
import LightingList from './components/LightingList.vue'
import GraphicsMode from '../../airMenu/components/graphicsMode'
export default {
  name: 'OperationMonitoring',
  components: {
    LightingList,
    GraphicsMode
  },
  data: () => ({
    treeLoading: false,
    treeData: [],
    currentNodeKey: 0,
    currentNodeCode: '',
    defaultProps: {
      label: 'name'
    },
    searchFrom: {
      name: '',
      lightingStatus: ''
    },
    // 当前数据展示模式
    currentViewType: 2,
    // 组态图需要调用的http接口
    requestHttp: __PATH.VUE_IEMC_API
  }),
  computed: {
    // 查看模式
    viewType() {
      return {
        // 组态图模式
        GRAPH: 1,
        // 列表模式
        LIST: 2
      }
    },
    // 查询实体参数
    queryParam() {
      return {
        surveyName: this.searchFrom.name,
        lightingStatus: this.searchFrom.lightingStatus,
        entityMenuCode: this.currentNodeCode,
        projectCode: this.projectCode
      }
    },
    // 默认检索类型-照明监测类型
    projectCode() {
      return monitorTypeList.find((item) => item.projectName === '照明监测').projectCode
    }
  },
  mounted() {
    this.initTree()
  },
  methods: {
    // 初始化左侧设备分组树
    initTree() {
      this.treeLoading = true
      this.$api
        .GetEntityMenuList({ projectId: this.projectCode })
        .then((res) => {
          if (res.code === '200') {
            let list = this.$tools.transData(res.data, 'id', 'parentId', 'children')
            this.treeData = list
            const [first] = list
            // 如果有数据就默认选中第一个节点
            if (first) {
              this.$nextTick(() => {
                const treeRef = this.$refs.treeRef.getTreeRef()
                treeRef.setCurrentKey(first.id)
                this.handleNodeClick(first)
              })
            }
          }
        })
        .finally(() => (this.treeLoading = false))
    },
    // 树节点被点击之后
    handleNodeClick(node) {
      if (this.currentNodeKey !== node.id) {
        this.currentNodeKey = node.id
        this.currentNodeCode = node.code
        this.onSearch()
      }
    },
    // 模式切换
    switchPattern(type) {
      if (this.currentViewType !== type) {
        this.currentViewType = type
        // take some times
        this.$nextTick(this.onSearch)
      }
    },
    // 点击重置
    onReset() {
      this.$refs.formRef.resetFields()
      this.onSearch()
    },
    // 点击搜索
    onSearch() {
      if (this.currentViewType === this.viewType.GRAPH) {
        this.$refs.scadaRef.getScadaList()
      } else {
        this.$refs.listRef.search()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.OperationMonitoring {
  ::v-deep(.container-content) {
    height: 100%;
    display: flex;
    flex-flow: row nowrap;
    overflow: hidden;
  }
  &__left {
    width: 220px;
    background: #fff;
  }
  &__right {
    flex: 1;
    margin-left: 16px;
    overflow: hidden;
    display: flex;
    flex-flow: column nowrap;
  }
  &__tree {
    height: 100%;
    border-radius: 4px;
    padding: 16px 0 16px 16px;
    overflow: hidden;
    ::v-deep(.el-tree) {
      height: 100%;
      padding-right: 16px;
      overflow: auto;
    }
  }
  &__header {
    background: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-radius: 4px;
    &__form {
      ::v-deep(.el-form-item) {
        margin-bottom: 0;
        .el-form-item__content {
          line-height: inherit;
        }
      }
    }
    &__action {
      display: flex;
      .pattern-item {
        margin-right: 16px;
        cursor: pointer;
        font-size: 15px;
        display: inline-block;
        .pattern-icon {
          margin-right: 4px;
        }
      }
    }
  }
  &__content {
    margin-top: 16px;
    flex: 1;
    overflow: hidden;
    .scada-preview {
      padding: 0;
    }
  }
}
</style>
