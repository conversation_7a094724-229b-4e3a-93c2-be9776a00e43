<template>
  <PageContainer>
    <div slot="header">
      <div class="search-header">
        <div class="search-from">
          <el-input
            v-model="taskBookName"
            style="width: 200px; margin-right: 20px;"
            placeholder="任务书名称"
            maxlength="25"
            onkeyup="if(value.length>25)value=value.slice(0,25)"
          ></el-input>
          <template>
            <el-select v-model="taskBookType" filterable placeholder="请选择任务书类型">
              <el-option v-for="item in taskBookTypeArr" :key="item.id" :label="item.dictName" :value="item.id"> </el-option>
            </el-select>
          </template>
          <el-button type="primary" plain @click="_resetCondition">重置</el-button>
          <el-button type="primary" @click="_searchByCondition">查询</el-button>
        </div>
        <div>
          <el-button type="primary" icon="el-icon-plus" @click="addFn('add')">新增</el-button>
          <el-button type="primary" icon="el-icon-edit" :disabled="selectedTableList.length !== 1" @click="updateFn('edit')">编辑</el-button>
        </div>
      </div>
    </div>
    <div slot="content" style="height: 100%; margin-top: 16px; background-color: #fff;">
      <div class="table-content">
        <el-table
          ref="materialTable"
          v-loading="tableLoading"
          :header-cell-style="{ background: '#f2f4fbd1' }"
          highlight-current-row
          :empty-text="emptyText"
          :row-key="getRowKeys"
          :data="tableData"
          :height="tableHeight"
          border
          style="width: 100%;"
          :cell-style="{ padding: '8px' }"
          stripe
          @selection-change="handleSelectionChange"
          @row-dblclick="toDetails"
        >
          <el-table-column :reserve-selection="true" type="selection" width="55" align="center"></el-table-column>
          <el-table-column label="序号" type="index" width="80">
            <template slot-scope="scope">
              <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="projectName" show-overflow-tooltip label="任务书名称"></el-table-column>
          <!-- <el-table-column
            prop="equipmentTypeName"
            show-overflow-tooltip
            label="任务书类型"
          ></el-table-column> -->
          <el-table-column prop="projectExplain" show-overflow-tooltip label="任务书说明"></el-table-column>
          <el-table-column prop="createPersonName" show-overflow-tooltip label="添加人"></el-table-column>
          <el-table-column prop="updateTime" show-overflow-tooltip label="更新时间"></el-table-column>
          <el-table-column label="操作" width="130" align="center">
            <template slot-scope="scope">
              <el-button size="mini" type="danger" @click="deleteTaskBook(scope.$index, scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :current-page="paginationData.currentPage"
          :page-sizes="[15, 30, 50, 100]"
          :page-size="paginationData.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="paginationData.total"
          class="table-page pagination"
          style="margin-top: 10px;"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
  </PageContainer>
</template>
<script type="text/ecmascript-6">
export default {
  name: 'feedbackRecord',
  components: {},
  data() {
    return {
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      LOGINDATA: '',
      taskBookTypeArr: [
        {
          id: '0',
          dictName: '日常巡检'
        },
        {
          id: '1',
          dictName: '专业巡检'
        }
      ],
      taskBookName: '', // 任务书名称
      taskBookType: '', // 任务书类型
      searchDataObj: {
        workTypeCode: '',
        endDate: '',
        startDate: '',
        dateLine: '',
        unionSel: ''
      },
      tableData: [],
      selectedTableList: [],
      emptyText: '暂无数据',
      tableLoading: false,
      pageSize: '',
      currentPage: '',
      listRole: '',
      userIds: '',
      taskBookTypeName: ''
    }
  },
  computed: {
    tableHeight() {
      return document.body.clientHeight - 270
    }
  },
  created() {
    // 获取登录信息
    this.LOGINDATA = JSON.parse(sessionStorage.getItem('LOGINDATA'))
    this._findDictionaryTableList() // 获取任务书类型
  },
  mounted() {
    // 获取任务书列表
    this._findTaskBookList()
  },
  methods: {
    // 获取任务书类型
    _findDictionaryTableList() {
      let data = {
        dictCode: '',
        dictName: '',
        pageNo: 1,
        pageSize: 9999,
        dictType: 'task_book_type'
      }
      this.$api.findDictionaryTableList(data).then((res) => {
        const { code, data, message } = res
        if (code == 200) {
          // this.taskBookTypeArr = data.list;
        } else {
          this.$message.error(message)
        }
      })
    },
    // 新增
    addFn(type) {
      console.log(type)
      this.$router.push({
        path: 'assignmentManagement/addAssignment',
        query: {
          type: type
        }
      })
    },
    updateFn(type) {
      console.log(type)
      this.$router.push({
        path: 'assignmentManagement/addAssignment',
        query: {
          type: type,
          id: this.selectedTableList[0].id
        }
      })
    },
    // 查询表格
    _findTaskBookList() {
      this.tableLoading = true
      let obj = this.LOGINDATA
      const { taskBookName, taskBookType, paginationData } = this
      let params = {
        projectName: taskBookName,
        equipmentTypeId: taskBookType,
        pageNo: paginationData.currentPage,
        pageSize: paginationData.pageSize
      }
      this.$api.findTaskBookList(params).then((res) => {
        this.tableLoading = false
        if (res.code == 200) {
          this.tableData = res.data.list
          this.paginationData.total = parseInt(res.data.count)
          if (res.data.list.length == 0) {
            this.emptyText = '未查到任何记录，请放大查询条件试试！'
          }
        }
      })
    },
    // 条件查询
    _searchByCondition() {
      this.paginationData.currentPage = 1
      this._findTaskBookList()
    },
    // 重置查询条件
    _resetCondition() {
      this.paginationData = {
        currentPage: 1,
        pageSize: 15,
        total: 0
      }
      this.taskBookName = ''
      this.taskBookType = ''
      this._findTaskBookList()
    },
    // 删除列表
    deleteTaskBook(index, row) {
      this.$confirm('此操作将永久删除该任务书, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let data = {
          ids: row.id
        }
        this.$api.deleteTaskBookList(data).then((res) => {
          const { code, data, message } = res
          if (code == 200) {
            this.$message.success(message)
            this._searchByCondition()
            this.$refs.materialTable.clearSelection() // 清空表格选中状态
          } else {
            this.$message.error(message)
          }
        })
      })
    },
    // 双击查看详情
    toDetails(val) {
      this.$router.push({
        name: 'assignmentDetail',
        query: {
          rowId: val.id,
          activeName: this.activeName
        }
      })
    },
    // 表格,勾选表格，对表格进行操作
    handleSelectionChange(val) {
      this.selectedTableList = val
    },

    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this._findTaskBookList()
    },
    // 每页的条数
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.paginationData.currentPage = 1
      this._findTaskBookList()
    },
    getRowKeys(row) {
      return row.id
    }
  }
}
</script>
<style lang="scss" scoped>
.search-header {
  padding: 10px 16px;

  .search-from {
    padding-bottom: 12px;

    & > div {
      margin-top: 12px;
      margin-right: 10px;
    }
  }
}

.table-content {
  padding: 16px;
  height: calc(100% - 80px);
}
</style>