<template>
  <PageContainer>
    <div slot="header">
      <div class="searchForm">
        <div class="search-box">
          <el-select v-model="alarmQueryParam.sysCode" clearable placeholder="请选择报警系统" @change="getAlarmTypeList(alarmQueryParam.sysCode)">
            <el-option v-for="item in alarmSystemList" :key="item.imhMonitorCode" :label="item.imhMonitorName" :value="item.imhMonitorCode"></el-option>
          </el-select>
          <el-cascader
            v-model="alarmQueryParam.alarmTypeId"
            :options="alarmTypeData"
            :props="alarmPropsType"
            clearable
            :show-all-levels="false"
            placeholder="请选择报警类型"
            class="ml-16"
          ></el-cascader>
          <el-input v-model="alarmQueryParam.surveyName" placeholder="请输入报警实体对象" clearable> </el-input>
          <div class="ml-16">
            <el-button type="primary" plain @click="resetData">重置</el-button>
            <el-button type="primary" @click="searchClick">查询</el-button>
            <el-button type="primary" :disabled="multipleSelection.length < 1" @click="batchDelete">批量删除</el-button>
            <el-button type="primary" :disabled="isEnable" @click="bathOperation('0')">批量启用</el-button>
            <el-button type="primary" :disabled="isUnEnable" @click="bathOperation('1')">批量禁用</el-button>
            <el-button type="primary" @click="addfn()">新增</el-button>
          </div>
        </div>
      </div>
    </div>
    <div slot="content" ref="contentRef" class="table-box">
      <div class="tableContainer">
        <el-table
          v-loading="tableLoading"
          height="calc(100% - 80px)"
          :data="tableData"
          border
          stripe
          table-layout="auto"
          class="tableAuto"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55"> </el-table-column>
          <el-table-column label="序号" type="index" width="55">
            <template slot-scope="scope">
              <span>{{ (pagination.page - 1) * pagination.pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="报警系统" prop="sysName" show-overflow-tooltip></el-table-column>
          <el-table-column label="报警类型" prop="iatName" show-overflow-tooltip></el-table-column>
          <el-table-column label="报警描述" prop="description" show-overflow-tooltip></el-table-column>
          <el-table-column label="实体类型" prop="entityTypeName" show-overflow-tooltip></el-table-column>
          <el-table-column label="报警等级" prop="levelName" show-overflow-tooltip>
            <template slot-scope="scope">
              <span class="alarmLevel" :style="{ background: alarmLevelItem[scope.row.levelId].color }">
                {{ scope.row.levelName }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="实体对象" prop="surveyName" width="200">
            <template slot-scope="scope">
              <div class="surveyNameClass" @click="getSurveyNameDetail(scope.row.surveyName)">
                {{ scope.row.surveyName }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="状态" prop="state" show-overflow-tooltip>
            <template slot-scope="scope">
              <span v-if="scope.row.state === 0" class="enable">已启用</span>
              <span v-if="scope.row.state === 1" class="unEnable">已禁用</span>
            </template>
          </el-table-column>
          <el-table-column label=" 操作">
            <template slot-scope="scope">
              <el-button type="text" @click="handleOperation('detail', scope.row)">查看</el-button>
              <el-button type="text" @click="handleOperation('changeState', scope.row)">{{ scope.row.state === 0 ? '禁用' : scope.row.state === 1 ? '启用' : '' }}</el-button>
              <el-button type="text" :disabled="scope.row.state === 0" :class="[scope.row.state == 0 ? 'text-grey' : '']" @click="handleOperation('edit', scope.row)"
                >修改</el-button
              >
              <el-button type="text" :disabled="scope.row.state === 0" :class="[scope.row.state == 0 ? 'text-grey' : '']" @click="handleOperation('delete', scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :current-page="pagination.page"
          :page-sizes="[30, 50, 100]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageTotal"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
      <el-drawer title="实体对象" :visible.sync="drawer" :direction="direction" :before-close="handleDrawerClose" size="30%">
        <div class="surveyNameBody">
          {{ surveyName }}
        </div>
      </el-drawer>
    </div>
  </PageContainer>
</template>
<script>
export default {
  name: 'commonAlarmConfiguration',
  props: {
    requestHttp: {
      type: String,
      default: __PATH.VUE_IEMC_API
    },
    alarmDetailsPath: {
      type: String,
      default: 'managementConfig/alarmConfiguration/alarmConfigForm'
    }
  },
  data() {
    return {
      drawer: false, // 实体对象抽屉
      direction: 'rtl',
      surveyName: '',
      pagination: {
        pageSize: 30,
        page: 1
      },
      pageTotal: 0,
      tableData: [],
      detailsId: '', // 双击行的ID
      departs: [],
      alarmPropsType: {
        children: 'child',
        label: 'name',
        value: 'id',
        checkStrictly: true
      },
      alarmQueryParam: {
        sysCode: '', // 报警系统
        alarmTypeId: '', // 报警类型
        surveyName: '' // 实体对象
      },
      alarmSystemList: [],
      entityTypeList: [],
      alarmRuleList: [],
      tableLoading: false,
      multipleSelection: [],
      alarmTypeData: [], // 报警类型树
      isEnable: true, // 启用
      isUnEnable: true, // 禁用
      alarmLevelItem: {
        0: { text: '通知', color: '#3562DB' },
        1: { text: '一般', color: '#3562DB' },
        2: { text: '紧急', color: '#FF9435' },
        3: { text: '重要', color: '#FA403C' }
      }
    }
  },
  created() {
    this.getTableData()
    this.getAlarmSystemList()
    this.getAlarmTypeList()
  },
  activated() {
    this.getTableData()
  },
  methods: {
    // 报警对象抽屉
    getSurveyNameDetail(name) {
      this.surveyName = name
      this.drawer = true
    },
    // 详情弹窗关闭
    handleDrawerClose() {
      this.drawer = false
    },
    addfn() {
      const queryData = {
        requestHttp: this.requestHttp
      }
      this.$router.push({
        path: '/' + this.alarmDetailsPath,
        query: queryData
      })
    },
    // 列表操作
    handleOperation(type, item) {
      if (type === 'edit') {
        // 编辑
        this.$router.push({
          path: '/managementConfig/alarmConfiguration/alarmConfigForm',
          query: {
            id: item.id,
            requestHttp: this.requestHttp
          }
        })
      } else if (type === 'changeState') {
        // 启用停用
        if (item.state === 0) {
          this.changeState(item.id, 1)
        } else if (item.state === 1) {
          this.changeState(item.id, 0)
        }
      } else if (type === 'delete') {
        // 删除
        this.$confirm('确认要删除当前报警配置吗?删除后将无法恢复。', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$api.deleteAlarmDeployData({ id: item.id }, { 'operation-type': 3, 'operation-id': item.id, 'operation-name': item.name }).then((res) => {
            if (res.code === '200') {
              this.$message.success('删除成功')
              this.getTableData()
            } else {
              this.$message.warning(res.msg)
            }
          })
        })
      } else if (type === 'detail') {
        // 详情
        this.$router.push({
          path: '/managementConfig/alarmConfiguration/alarmConfigForm',
          query: {
            id: item.id,
            requestHttp: this.requestHttp,
            activeType: 'detail'
          }
        })
      }
    },
    // 改变启用状态
    changeState(id, e) {
      this.$api.changeAlarmDeployStateData({ id: id, state: e }).then((res) => {
        if (res.code == 200) {
          this.getTableData()
          this.$message.success(res.message)
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 批量启用/停用
    bathOperation(type) {
      let ids = []
      this.multipleSelection.forEach((item) => {
        ids.push(item.id)
      })
      this.$api.changeAlarmDeployStateData({ id: ids.join(','), state: type }).then((res) => {
        if (res.code == 200) {
          this.getTableData()
          this.$message.success(res.message)
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 批量删除
    batchDelete() {
      if (this.multipleSelection.some((e) => e.state === 0)) {
        return this.$message({
          message: '启用的报警不支持删除！',
          type: 'warning'
        })
      }
      this.$confirm('是否批量删除报警配置?删除后将无法恢复。', '提示', {
        cancelButtonClass: 'el-button--primary is-plain',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let ids = []
        this.multipleSelection.forEach((item) => {
          ids.push(item.id)
        })
        this.$api.deleteAlarmDeployData({ id: ids.join(',') }, { 'operation-type': 3 }).then((res) => {
          if (res.code === '200') {
            this.$message.success('删除成功')
            this.getTableData()
          } else {
            this.$message.warning(res.message)
          }
        })
      })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
      if (this.multipleSelection.length && this.multipleSelection.length > 0) {
        let enableShow = this.multipleSelection.every((e) => e.state === 0)
        let unEnableShow = this.multipleSelection.every((e) => e.state === 1)
        if (enableShow) {
          this.isUnEnable = false
        } else {
          this.isUnEnable = true
        }
        if (unEnableShow) {
          this.isEnable = false
        } else {
          this.isEnable = true
        }
      } else {
        this.isUnEnable = true
        this.isEnable = true
      }
    },
    getAlarmSystemList() {
      let data = {
        projectCodes: ''
      }
      this.$api.getAlarmSystem(data, {}, this.requestHttp).then((res) => {
        if (res.code == 200) {
          this.alarmSystemList = res.data
        }
      })
    },
    getAlarmTypeList(val) {
      this.alarmQueryParam.alarmTypeId = ''
      this.$api
        .getAlarmDeployTypeList({
          sysCode: val || ''
        })
        .then((res) => {
          // h获取实体类型数据
          if (res.code == 200) {
            this.alarmTypeData = res.data
          }
        })
    },
    resetData() {
      // 将查询条件置为空
      this.alarmQueryParam = {
        sysCode: '', // 报警系统
        alarmTypeId: '', // 报警类型
        surveyName: '' // 实体对象
      }
      this.getAlarmTypeList()
      this.getTableData()
    },
    getTableData() {
      // 查询
      let data = {
        ...this.pagination,
        ...this.alarmQueryParam
      }
      if (data.alarmTypeId) {
        let alarmTypeId = data.alarmTypeId
        data.alarmTypeId = alarmTypeId[alarmTypeId.length - 1]
      } else {
        data.alarmTypeId = ''
      }
      this.$api.getAlarmDeployPageData(data).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.records
          this.pageTotal = res.data.total
        }
      })
    },
    searchClick() {
      this.pagination.page = 1
      this.getTableData()
    },
    // 删除
    delData(row) {
      this.$confirm('确定删除?', '消息', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api
          .policeDeleteById(
            {
              id: row.id
            },
            { 'operation-type': 3, 'operation-id': row.id, 'operation-name': row.alarmName },
            this.requestHttp
          )
          .then((res) => {
            if (res.code == 200) {
              this.$message.success(res.message)
              this.getTableData()
            }
          })
      })
    },
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.pagination.page = 1
      this.getTableData()
    },
    handleCurrentChange(val) {
      this.pagination.page = val
      this.getTableData()
    }
  }
}
</script>
<style lang="scss" scoped>
.searchForm {
  display: flex;
  height: 80px;
  padding: 0 16px;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  .el-input {
    width: 200px;
    margin-left: 16px;
  }
  > div {
    margin-right: 20px;
  }
}
.search-box {
  display: flex;
  align-items: center;
}
.container-content > div {
  height: 100%;
  margin-top: 16px;
}
.el-table {
  margin-bottom: 12px;
}
::v-deep .el-table__row:hover {
  cursor: pointer;
}
.table-box {
  background-color: #fff;
  height: 100%;
  padding: 16px;
  .tableContainer {
    height: 100%;
    .el-table {
      height: calc(100% - 80px) !important;
      margin-bottom: 20px;
    }
    .surveyNameClass {
      width: 160px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .alarmLevel {
      padding: 3px 6px;
      border-radius: 4px;
      color: #fff;
      line-height: 14px;
    }
  }
}
.ml-16 {
  margin-left: 16px;
}
.enable {
  color: #33cc00;
}
.unEnable {
  color: #7f848c;
}
.surveyNameBody {
  padding: 0 20px;
}
::v-deep .text-grey {
  color: #ccced3 !important;
}
</style>
<style lang="scss">
.alarm-configuration {
  .operationBtn-span {
    margin-right: 10px;
    color: #3562db;
  }
}
</style>
