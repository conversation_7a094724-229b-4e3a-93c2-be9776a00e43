<template>
  <PageContainer>
    <div slot="content" class="monitor-setting">
      <div class="monitor-setting-left">
        <ContentCard title="安全台账管理" :cstyle="{ height: '100%' }">
          <template slot="content">
            <div class="filter">
              <el-input v-model="filterText" suffix-icon="el-icon-search" placeholder="请输入" clearable></el-input>
            </div>
            <el-tree
              ref="tree"
              key="ledgerId"
              :data="treeData"
              node-key="ledgerId"
              size="small"
              :highlight-current="true"
              :props="defaultProps"
              :filter-node-method="filterNode"
              :expand-on-click-node="false"
              check-on-click-node
              draggable
              default-expand-all
              :allow-drag="isDrag"
              @node-click="nodeClick"
              @node-drop="nodeDrop"
            >
              <div slot-scope="{ node, data }" class="custom-tree-node">
                <el-tooltip class="item" effect="dark" :content="node.label" placement="top">
                  <div class="custom-tree-node-wrapper">
                    <span class="custom-tree-node-label">
                      <i v-if="data.ledgerLevel !== 1" :class="data.ledgerType === 0 ? 'el-icon-folder' : 'el-icon-document'"></i>
                      {{ node.label }}
                    </span>
                    <span class="operate-btns">
                      <dot-dropdown v-if="data.ledgerLevel === 1" :events="directoryList" :data="{ node, data }" @addNode="opertaionDirNode" />
                      <dot-dropdown v-if="data.ledgerType === 0 && data.ledgerLevel !== 1" :events="folderList" :data="{ node, data }" @addNode="opertaionFolderNode" />
                      <dot-dropdown v-if="data.ledgerType === 1" :events="classificationList" :data="{ node, data }" @addNode="opertaionClassNode" />
                    </span>
                  </div>
                </el-tooltip>
              </div>
            </el-tree>
          </template>
        </ContentCard>
      </div>
      <div class="monitor-setting-right">
        <div class="right-heade">
          <el-input v-model="fileName" placeholder="文件名称" suffix-icon="el-icon-search" clearable />
          <el-select v-model="uploadYear" placeholder="全部上传年份">
            <el-option v-for="item in options" :key="item" :label="item" :value="item"> </el-option>
          </el-select>
          <div style="display: inline-block">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button type="primary" plain @click="reset">重置</el-button>
            <el-button v-auth="'fileManager:update'" size="small" type="primary" @click="uploadFile">上传文件</el-button>
            <el-button v-auth="'fileManager:delete'" size="small" :disabled="!selectionList?.length" type="danger" @click="batchDel">删除</el-button>
          </div>
          <span v-if="showSearchText" class="searchClassText">搜索完毕，共{{ tableData?.length }}个文件</span>
        </div>
        <div class="right-content">
          <div class="table-content">
            <el-table ref="multipleTable" v-loading="tableLoading" height="100%" :data="tableData" row-key="id" @selection-change="handleSelectionChange">
              <el-table-column type="selection" width="55" align="center" :reserve-selection="true"> </el-table-column>
              <el-table-column prop="dataName" label="名称" align="center" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="fileName" label="附件" align="center" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span class="fileNameClick" @click="control('view', scope.row)">{{ scope.row.fileName }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="fileMenuName" label="所属分类" align="center" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="createName" label="上传人" align="center" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="createTime" label="上传时间" align="center" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="updateName" label="修改人" align="center" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="updateTime" label="修改时间" align="center" show-overflow-tooltip> </el-table-column>
              <el-table-column show-overflow-tooltip align="center" label="操作" width="200">
                <template slot-scope="scope">
                  <el-button v-auth="'fileManager:detail'" type="text" class="copy" @click="control('view', scope.row)"> 查看</el-button>
                  <el-button v-auth="'fileManager:edit'" type="text" class="delet" @click="control('edit', scope.row)"> 编辑</el-button>
                  <el-dropdown>
                    <span class="el-dropdown-link"> 更多<i class="el-icon-arrow-down el-icon--right"></i> </span>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item v-if="scope.row.showDownButton == 1" v-auth="'fileManager:export'" @click.native="control('download', scope.row)">下载 </el-dropdown-item>
                      <el-dropdown-item v-auth="'fileManager:accessRecord'" @click.native="accessRecord(scope.row)">访问记录 </el-dropdown-item>
                      <el-dropdown-item v-auth="'fileManager:delete'" @click.native="control('del', scope.row)"><span style="color: red">删除</span> </el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </template>
              </el-table-column>
            </el-table>
            <div class="paginationClass">
              <div class="tipsClass">
                <i class="el-icon-info" style="color: #3664dd"></i>已选择{{ selectionList?.length }}项，支持跨页选择
                <el-button style="margin-left: 10px" type="text" @click="clearSelectionTable">清空</el-button>
              </div>
              <div>
                <el-pagination
                  :current-page="pageData.current"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="pageData.total"
                  :page-size="pageData.size"
                  :page-sizes="[15, 30, 50]"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                >
                </el-pagination>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 新增编辑文件夹或者分类 -->
      <el-dialog
        v-if="visible"
        v-dialogDrag
        :modal="false"
        custom-class="interDialogDiv"
        width="30%"
        append-to-body
        :visible="visible"
        :close-on-click-modal="false"
        :before-close="closeDialog"
      >
        <template slot="title">
          <span class="dialog-title">{{ visibleType === 'add' ? (form.ledgerType === 1 ? '创建分类' : '创建文件夹') : form.ledgerType === 1 ? '编辑分类' : '编辑文件夹' }}</span>
        </template>
        <el-form ref="form" class="form-data" style="width: 100%" :model="form" @submit.native.prevent>
          <el-form-item label="标题" prop="ledgerName" :rules="{ required: true, message: '请输入标题', trigger: 'blur' }">
            <el-input v-model="form.ledgerName" placeholder="请输入标题" maxlength="50" show-word-limit />
          </el-form-item>
          <el-form-item>
            <span slot="label">
              <span style="font-size: 18px">权限设置</span>
            </span>
          </el-form-item>
          <el-form-item>
            <span slot="label">
              <span style="font-size: 16px">可查看</span>
            </span>
            <el-radio v-model="form.viewPerm" label="0">所有人</el-radio>
            <el-radio v-model="form.viewPerm" label="1">指定成员</el-radio>
            <span v-if="form.viewPerm == 1" style="width: 200px; display: inline-block" @click="clickspanPeopleSee">
              <el-input placeholder="选择人员"> </el-input>
            </span>
            <div v-if="form.viewPerm == 1">
              <span>已选人员：</span><span v-for="item in selectData" :key="item.id">{{ item.staffName }}{{ '、' }}</span>
            </div>
            <div v-else><span>已选人员：所有人</span></div>
          </el-form-item>
          <el-form-item>
            <span slot="label">
              <span style="font-size: 16px">可查看和下载</span>
            </span>
            <el-radio v-model="form.downloadPerm" :disabled="form.viewPerm == 1" label="0">所有人</el-radio>
            <el-radio v-model="form.downloadPerm" label="1">指定成员</el-radio>
            <span v-if="form.downloadPerm == 1" style="width: 200px; display: inline-block" @click="clickspanPeopleDownload">
              <el-input placeholder="选择人员"> </el-input>
            </span>
            <div v-if="form.downloadPerm == 1">
              <span>已选人员：</span><span v-for="item in selectDataDownload" :key="item.id">{{ item.staffName }}{{ '、' }}</span>
            </div>
            <div v-else><span>已选人员：所有人</span></div>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="submit">确 定</el-button>
        </span>
      </el-dialog>
      <!-- 删除文件夹或者分类 -->
      <el-dialog
        v-if="delVisible"
        v-dialogDrag
        :modal="false"
        custom-class="interDialogDiv"
        width="30%"
        append-to-body
        :visible="delVisible"
        :close-on-click-modal="false"
        :before-close="closeDelDialog"
      >
        <template slot="title">
          <span class="dialog-title">删除文件夹</span>
        </template>
        <el-radio-group v-model="isMoveFile" class="radioModification">
          <el-radio :label="1">仅删除当前文件夹（内容将上移一层）</el-radio>
          <el-radio :label="0">删除当前文件夹及文件夹里的内容</el-radio>
        </el-radio-group>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="closeDelDialog">取 消</el-button>
          <el-button type="primary" @click="delSubmit()">确 定</el-button>
        </span>
      </el-dialog>
      <!-- 上传文件 -->
      <UploadFile
        v-if="uploadVisible"
        v-model="uploadVisible"
        :currentForm="editForm"
        :type="handerType"
        :list="treeData"
        :checkedTreeNode="checkedTreeNode"
        @close="uploadVisible = false"
        @success="uploadSuccess"
      >
      </UploadFile>
      <!-- 查看文件 -->
      <Preview v-if="viewVisible" v-model="viewVisible" :row="checkRow" @close="viewVisible = false"></Preview>
      <!-- 访问记录弹窗 -->
      <el-dialog custom-class="interDialogDiv" title="文档信息" :visible.sync="dialogVisible" width="55%">
        <div>
          <div class="titleClass">
            <div class="iconSpanClass"></div>
            <div class="statistics">访问统计</div>
          </div>
          <div class="interview">
            <div class="interviewTop">
              <div class="interviewInterview">访问人数</div>
              <div class="interviewInterviewNumber">{{ accessUsers }}</div>
            </div>
            <div class="interviewTop">
              <div class="interviewInterview">访问次数</div>
              <div class="interviewInterviewNumber">{{ accessCounts }}</div>
            </div>
          </div>
          <div class="titleClass" style="margin-top: 30px">
            <div class="iconSpanClass"></div>
            <div class="statistics">访问记录</div>
          </div>
          <div class="monitor-setting-right">
            <div class="right-headecopy">
              <el-input v-model="pageDataDialog.userName" placeholder="访问者" suffix-icon="el-icon-search" />
              <el-input v-model="pageDataDialog.deptName" placeholder="所属部门" suffix-icon="el-icon-search" />
              <el-select v-model="pageDataDialog.operate" style="width: 120px" placeholder="全部操作">
                <el-option v-for="item in queryFileOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
              <el-time-picker
                v-model="queryFileTime"
                style="width: 30%"
                is-range
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                placeholder="选择时间范围"
                value-format="yyyy-MM-dd HH:mm:ss"
                @change="changeTime"
              >
              </el-time-picker>
              <div>
                <el-button type="primary" @click="searchQueryFile">查询</el-button>
                <el-button type="primary" plain @click="resetQueryFile">重置</el-button>
              </div>
            </div>
            <div style="margin-top: 0px">
              <div>
                <el-table v-loading="queryFileTableLoading" height="240px" :data="queryFileTableData">
                  <el-table-column prop="userName" label="访问者" align="center" show-overflow-tooltip> </el-table-column>
                  <el-table-column prop="deptName" label="所属部门" align="center" show-overflow-tooltip> </el-table-column>
                  <el-table-column prop="fileMenuName" label="操作" align="center" show-overflow-tooltip>
                    <template slot-scope="scope">{{ scope.row.operate == 0 ? '查看' : '下载' }}</template>
                  </el-table-column>
                  <el-table-column prop="accessTime" label="访问时间" align="center" show-overflow-tooltip>
                    <template slot-scope="scope">
                      <span>{{ formatDateTime(scope.row.accessTime) }}</span>
                    </template>
                  </el-table-column>
                </el-table>
                <el-pagination
                  style="margin-top: 10px"
                  :current-page="pageDataDialog.current"
                  layout="->,total, sizes, prev, pager, next, jumper"
                  :total="pageDataDialog.total"
                  :page-size="pageDataDialog.size"
                  :page-sizes="[15, 30, 50]"
                  @size-change="handleSizeChangeDialog"
                  @current-change="handleCurrentChangeDialog"
                >
                </el-pagination>
              </div>
            </div>
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">关 闭</el-button>
        </span>
      </el-dialog>
      <!-- 权限修改弹窗 -->
      <el-dialog title="权限修改" :before-close="modificationCancel" :visible.sync="dialogModification" custom-class="interDialogDiv" width="30%" :close-on-click-modal="false">
        <div class="modification">
          <div class="modificationSetting">权限设置</div>
          <div class="modificationSee">可查看</div>
          <div v-if="form.viewPerm == 1" class="modificationSeeTop">
            <div class="modificationSeeText">
              <span>已选人员：</span><span v-for="item in authoritySeeList" :key="item.id">{{ item.staffName }}{{ '、' }}</span>
            </div>
            <div style="font-size: 14px">(共{{ authoritySeeList?.length }}人)</div>
          </div>
          <div v-else class="modificationSeeTop">
            <div>已选人员：所有人</div>
          </div>
          <div class="modificationSee">可查看和下载</div>
          <div v-if="form.downloadPerm == 1" class="modificationSeeTop">
            <div class="modificationSeeText">
              <span>已选人员：</span><span v-for="item in authorityDownloadist" :key="item.id">{{ item.staffName }}{{ '、' }}</span>
            </div>
            <div style="font-size: 14px">(共{{ authorityDownloadist?.length }}人)</div>
          </div>
          <div v-else class="modificationSeeTop">
            <div>已选人员：所有人</div>
          </div>
          <div style="font-size: 16px">请选择此修改的应用范围</div>
          <el-radio-group v-model="form.updatePermRange" class="radioModification" @change="$forceUpdate()">
            <el-radio :label="0">仅应用于此文件夹（文件分类）</el-radio>
            <el-radio :label="1">将更改应用于下级目录</el-radio>
          </el-radio-group>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="modificationCancel">取 消</el-button>
          <el-button type="primary" @click="modificationTrue">确 定</el-button>
        </span>
      </el-dialog>
      <!-- 选择人员弹窗 -->
      <el-dialog v-dialogDrag title="添加成员" width="50%" :visible.sync="memberShow" custom-class="interDialogDiv" :before-close="closeDialogPeople" append-to-body>
        <div class="sapce_content">
          <div class="left">
            <div v-loading="treeLoading" class="sino_tree_box">
              <el-collapse v-model="activeName" style="padding: 0 10px 0 20px" accordion @change="handelChange">
                <el-collapse-item v-for="(list, index) in collapseData" :key="index" :title="list.unitComName" :name="list.umId">
                  <div class="sino_tree_box" style="margin: 0">
                    <el-tree
                      ref="tree"
                      v-loading="isLoading"
                      class="filter-tree"
                      :data="treeDataPeple"
                      :props="defaultPropsList"
                      node-key="id"
                      highlight-current
                      @node-click="nodeClickPeople"
                    ></el-tree>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
          </div>
          <div v-if="peopleTyle == 'see'" class="center">
            <el-input v-model.trim="userInfo" placeholder="搜索姓名、手机号、部门" clearable suffix-icon="el-icon-search" @input="nameInput"></el-input>
            <el-checkbox-group v-model="staffSelect" v-loading="loading" @change="handleCheck">
              <el-checkbox v-for="item in staffData" :key="item.id" :label="item.id" style="display: block">
                <div class="personCheck">
                  <img src="../../../assets/images/avatar.png" />
                  <div class="info">
                    <div class="name">{{ item.staffName }}</div>
                    <div class="mobile">{{ item.mobile }}</div>
                  </div>
                </div>
              </el-checkbox>
            </el-checkbox-group>
          </div>
          <div v-if="peopleTyle == 'download'" class="center">
            <el-input v-model.trim="userInfo" placeholder="搜索姓名、手机号、部门" clearable suffix-icon="el-icon-search" @input="nameInput"></el-input>
            <el-checkbox-group v-model="staffSelectDownload" @change="handleCheckDownload">
              <el-checkbox v-for="item in staffDataDownload" :key="item.id" :label="item.id" style="display: block">
                <div class="personCheck">
                  <img src="../../../assets/images/avatar.png" />
                  <div class="info">
                    <div class="name">{{ item.staffName }}</div>
                    <div class="mobile">{{ item.mobile }}</div>
                  </div>
                </div>
              </el-checkbox>
            </el-checkbox-group>
          </div>
          <div v-if="peopleTyle == 'see'" class="right">
            <div class="top">
              <span>
                <span class="label">已选:</span>
                <span class="num">{{ selectData.length ? selectData.length : 0 }}</span>
                <span class="dept">名用户</span>
              </span>
              <span class="clear" @click="clear">清空</span>
            </div>
            <div v-for="(item, index) in selectData" :key="index" class="item-list">
              <div style="display: flex">
                <img src="../../../assets/images/avatar.png" />
                <div class="info">
                  <div class="name">{{ item.staffName }}</div>
                  <div class="mobile">{{ item.mobile }}</div>
                </div>
              </div>
              <div class="remove" @click="remove(item, index)"><i class="el-icon-close"></i></div>
            </div>
          </div>
          <div v-if="peopleTyle == 'download'" class="right">
            <div class="top">
              <span>
                <span class="label">已选:</span>
                <span class="num">{{ selectDataDownload.length ? selectDataDownload.length : 0 }}</span>
                <span class="dept">名用户</span>
              </span>
              <span class="clear" @click="clearDownload">清空</span>
            </div>
            <div v-for="(item, index) in selectDataDownload" :key="index" class="item-list">
              <div style="display: flex">
                <img src="../../../assets/images/avatar.png" />
                <div class="info">
                  <div class="name">{{ item.staffName }}</div>
                  <div class="mobile">{{ item.mobile }}</div>
                </div>
              </div>
              <div class="remove" @click="removeDownload(item, index)"><i class="el-icon-close"></i></div>
            </div>
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="closeDialogPeople">取 消</el-button>
          <el-button type="primary" @click="submitPeople()">确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </PageContainer>
</template>
<script lang="jsx">
import { transData, ListTree } from '@/util'
import tableListMixin from '@/mixins/tableListMixin.js'
import DotDropdown from './components/DotDropdown.vue'
import UploadFile from './components/UploadFile.vue'
import Preview from './components/preview.vue'
import axios from 'axios'
import qs from 'qs'
import { auth } from '@/util'
export default {
  name: 'fileManager',
  components: {
    DotDropdown,
    UploadFile,
    Preview
  },
  mixins: [tableListMixin],
  data() {
    return {
      keyWord: '', // 编码/名称/通用名/规格型号/SN
      treeDataPeple: [],
      defaultPropsList: {
        label: 'deptName',
        value: 'id',
        children: 'list'
      },
      treeLoading: false,
      isLoading: false,
      loading: false,
      selectData: [],
      selectDataDownload: [],
      activeName: '',
      collapseData: [],
      staffData: [], // 人员列表
      staffDataDownload: [], // 人员列表
      staffSelect: [],
      staffSelectDownload: [],
      officeId: '',
      pmId: '',
      userInfo: '',
      seeList: [],
      downloadList: [],
      authoritySeeList: [],
      authorityDownloadist: [],
      showPeople: false,
      echoPeopleSeeList: [],
      echoPeopleDownloadList: [],
      peopleTyle: '',
      options: [],
      accessRecordId: '',
      queryFileTableData: [],
      modificationShow: false,
      dialogModification: false,
      memberShow: false, // 成员
      dialogVisible: false,
      showSearchText: false,
      queryFileOptions: [
        {
          value: '0',
          label: '查看'
        },
        {
          value: '1',
          label: '下载'
        }
      ],
      value: '',
      isShowTooltip: false,
      fileName: '',
      uploadYear: '',
      filterText: '',
      treeData: [],
      treeId: '', // 当前选中节点
      defaultProps: {
        children: 'children',
        label: 'ledgerName',
        value: 'ledgerId'
      },
      dragNodeObj: {},
      directoryList: [
        {
          label: '创建文件夹',
          funcName: 'addDir',
          auth: 'fileManager:addFile'
        },
        {
          label: '创建分类',
          funcName: 'addClass',
          auth: 'fileManager:addClassify'
        }
      ],
      folderList: [
        {
          label: '创建文件夹',
          funcName: 'addDir',
          auth: 'fileManager:addFile'
        },
        {
          label: '创建分类',
          funcName: 'addClass',
          auth: 'fileManager:addClassify'
        },
        {
          label: '文件夹设置',
          funcName: 'dirSet',
          auth: 'fileManager:editFile'
        },
        {
          label: '删除文件夹',
          funcName: 'delDir',
          auth: 'fileManager:delFile'
        }
      ],
      classificationList: [
        {
          label: '设置分类',
          funcName: 'classSet',
          auth: 'fileManager:editClassify'
        },
        {
          label: '删除分类',
          funcName: 'delClass',
          auth: 'fileManager:delClassify'
        }
      ],
      checkedTeamData: {},
      pageData: {
        current: 1,
        size: 15,
        total: 0
      },
      queryFileTableLoading: false,
      queryFileTime: null,
      pageDataDialog: {
        current: 1,
        size: 15,
        total: 0,
        userName: '', // 访问者
        deptName: '', // 所属部门
        operate: '', // 操作
        startTime: null, // 开始时间
        endTime: null // 结束时间
      },
      checkedTreeNode: {},
      tableLoading: false,
      tableData: [],
      selectionList: [],
      visible: false,
      form: {
        createName: '',
        ledgerLevel: '',
        ledgerName: '',
        ledgerType: '',
        parentLedgerIds: '',
        parentLedgerName: '',
        viewPerm: '0',
        downloadPerm: '0',
        viewUserIds: [],
        downloadUserIds: [],
        updatePermRange: 0
      },
      visibleType: 'add',
      delVisible: false,
      delId: '',
      // 查看预览
      viewVisible: false,
      checkRow: {},
      // 上传
      uploadVisible: false,
      editForm: {},
      handerType: 'add',
      accessCounts: '',
      accessUsers: '',
      logInforId: '',
      isMoveFile: 1,
      formData: {},
      formDataCopy: {},
      dirSetShow: ''
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  mounted() {
    this.getUnitListFn()
    this.logInforId = JSON.parse(sessionStorage.getItem('ihcrs_userInfo')).user.staffId
    this.$api.yearCombo().then((res) => {
      this.options = res.data
    })
    this.getTreeList()
  },
  methods: {
    //  获取单位列表
    getUnitListFn() {
      this.$api.getUnitList({}).then((res) => {
        this.treeLoading = false
        if (res.code == 200) {
          this.collapseData = this.unitList = res.data
          this.staffListByPageFn()
        }
      })
    },
    nameInput() {
      this.staffListByPageFn()
      this.staffSelect = this.selectData.map((item) => {
        return item.id
      })
      this.staffSelectDownload = this.selectDataDownload.map((item) => {
        return item.id
      })
    },
    handelChange(val) {
      this.activeName = val
      this.officeId = ''
      this.pmId = val
      this.getDeptListFn(val)
      this.staffListByPageFn()
    },
    // 部门列表
    getDeptListFn(unitId) {
      this.isLoading = true
      this.$api
        .getDeptList({
          unitId: unitId
        })
        .then((res) => {
          if (res.code == 200) {
            this.departList = res.data
            this.treeDataPeple = transData(res.data, 'id', 'pid', 'list')
          }
        })
        .finally(() => {
          this.isLoading = false
        })
    },
    //  获取人员信息列表
    staffListByPageFn() {
      this.loading = true
      this.$api
        .getPostMemberListByPage({
          pmId: this.pmId,
          current: 1,
          size: 99999,
          officeId: this.officeId,
          userInfo: this.userInfo
        })
        .then((res) => {
          if (res.code == 200) {
            this.staffData = res.data.records
            this.staffDataDownload = res.data.records
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 选择
    handleCheck(list) {
      // console.log(list);
      // if (this.peopleTyle == 'see') {
      //   this.seeList = this.staffSelect
      // } else if (
      //   this.peopleTyle == 'download'
      // ) {
      //   this.downloadList = this.staffSelect
      // }
      let arr = []
      list.forEach((el) => {
        this.staffData.forEach((item) => {
          if (el === item.id) {
            arr.push(item)
          }
        })
      })
      this.selectData = arr
    },
    // 选择
    handleCheckDownload(list) {
      // console.log(list);
      // if (this.peopleTyle == 'see') {
      //   this.seeList = this.staffSelect
      // } else if (
      //   this.peopleTyle == 'download'
      // ) {
      //   this.downloadList = this.staffSelect
      // }
      let arr = []
      list.forEach((el) => {
        this.staffDataDownload.forEach((item) => {
          if (el === item.id) {
            arr.push(item)
          }
        })
      })
      // this.selectDataDownload = this.removeSame(this.selectDataDownload.concat(arr))
      this.selectDataDownload = arr
    },
    removeSame(array) {
      let newArray = []
      for (let item of array) {
        // 使用JSON.stringfy()方法将数组和数组元素转换为JSON字符串之后再使用includes()方法进行判断
        if (JSON.stringify(newArray).includes(JSON.stringify(item))) {
          continue
        } else {
          // 不存在的添加到数组中
          newArray.push(item)
        }
      }
      return newArray
    },
    nodeClickPeople(val) {
      this.pmId = val.umId
      this.officeId = val.id
      this.staffListByPageFn()
    },
    // 移除
    remove(list, index) {
      this.selectData.splice(index, 1)
      this.staffSelect = this.selectData.map((item) => {
        return item.id
      })
    },
    // 移除
    removeDownload(list, index) {
      this.selectDataDownload.splice(index, 1)
      this.staffSelectDownload = this.selectDataDownload.map((item) => {
        return item.id
      })
    },
    // 清空
    clear() {
      this.selectData = []
      this.staffSelect = []
      this.$nextTick(() => {
        this.$refs.tree.setCheckedKeys([])
      })
    },
    // 清空
    clearDownload() {
      this.selectDataDownload = []
      this.staffSelectDownload = []
      this.$nextTick(() => {
        this.$refs.tree.setCheckedKeys([])
      })
    },
    // 关闭人员弹框
    closeDialogPeople() {
      if (this.visibleType == 'add') {
        if (this.peopleTyle == 'see') {
          this.clear()
          this.form.viewUserIds = []
        } else {
          this.clearDownload()
          this.form.downloadUserIds = []
        }
      }
      this.memberShow = false
      this.authoritySeeList = this.selectData
      this.authorityDownloadist = this.selectDataDownload
    },
    submitPeople() {
      if (this.peopleTyle == 'see') {
        if (this.selectData.length < 1) {
          this.$message({
            message: '请选择至少一条数据',
            type: 'warning'
          })
          return
        }
        this.form.viewUserIds = this.staffSelect
        this.authoritySeeList = this.selectData
      } else {
        if (this.selectDataDownload.length < 1) {
          this.$message({
            message: '请选择至少一条数据',
            type: 'warning'
          })
          return
        }
        this.form.downloadUserIds = this.staffSelectDownload
        this.authorityDownloadist = this.selectDataDownload
      }
      this.memberShow = false
    },
    // 格式化时间
    formatDateTime(timestamp) {
      const date = new Date(timestamp)
      const year = date.getFullYear()
      const month = ('0' + (date.getMonth() + 1)).slice(-2) // 月份是从0开始的
      const day = ('0' + date.getDate()).slice(-2)
      const hours = ('0' + date.getHours()).slice(-2)
      const minutes = ('0' + date.getMinutes()).slice(-2)
      const seconds = ('0' + date.getSeconds()).slice(-2)
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    },
    // 访问记录选择时间查询
    changeTime(val) {
      this.pageDataDialog.startTime = this.queryFileTime[0]
      this.pageDataDialog.endTime = this.queryFileTime[1]
    },
    // 访问记录列表查询
    queryFileGetList() {
      this.queryFileTableLoading = true
      this.$api
        .queryFileAccessRecordByPage({
          page: {
            current: this.pageDataDialog.current,
            size: this.pageDataDialog.size,
            total: this.pageDataDialog.total
          },
          userName: this.pageDataDialog.userName,
          deptName: this.pageDataDialog.deptName,
          operate: this.pageDataDialog.operate,
          startTime: this.pageDataDialog.startTime,
          endTime: this.pageDataDialog.endTime,
          fileId: this.accessRecordId
        })
        .then((res) => {
          if (res.code == 200) {
            this.accessCounts = res.data.accessCounts
            this.accessUsers = res.data.accessUsers
            this.queryFileTableLoading = false
            this.queryFileTableData = res.data.page.records
            this.pageDataDialog.total = res.data.page.total
          }
        })
    },
    // 选择人员(查看)
    clickspanPeopleSee() {
      this.peopleTyle = 'see'
      this.memberShow = true
    },
    // 选择人员(查看和下载)
    clickspanPeopleDownload() {
      this.peopleTyle = 'download'
      this.memberShow = true
    },
    // 访问记录
    accessRecord(row) {
      this.accessRecordId = row.id
      this.queryFileGetList()
      this.dialogVisible = true
    },
    // 选中清空
    clearSelectionTable() {
      this.$refs.multipleTable.clearSelection()
    },
    // 操作目录
    opertaionDirNode(fnName, data) {
      this.visibleType = 'add'
      this.form.parentLedgerIds = `${data.data.parentLedgerIds},${data.data.ledgerId}`
      this.form.parentLedgerName = data.data.ledgerName
      this.form.ledgerLevel = data.data.ledgerLevel
      if (fnName === 'addDir') {
        this.form.viewUserIds = [this.logInforId]
        this.staffSelect = [this.logInforId]
        let arr = []
        this.staffSelect.forEach((el) => {
          this.staffData.forEach((item) => {
            if (el === item.id) {
              arr.push(item)
            }
          })
        })
        this.selectData = arr
        this.form.downloadUserIds = [this.logInforId]
        this.staffSelectDownload = [this.logInforId]
        let arr1 = []
        this.staffSelectDownload.forEach((el) => {
          this.staffDataDownload.forEach((item) => {
            if (el === item.id) {
              arr1.push(item)
            }
          })
        })
        this.selectDataDownload = arr1
        this.form.ledgerType = 0
      } else if (fnName === 'addClass') {
        this.form.viewUserIds = [this.logInforId]
        this.staffSelect = [this.logInforId]
        let arr = []
        this.staffSelect.forEach((el) => {
          this.staffData.forEach((item) => {
            if (el === item.id) {
              arr.push(item)
            }
          })
        })
        this.selectData = arr
        this.form.downloadUserIds = [this.logInforId]
        this.staffSelectDownload = [this.logInforId]
        let arr1 = []
        this.staffSelectDownload.forEach((el) => {
          this.staffDataDownload.forEach((item) => {
            if (el === item.id) {
              arr1.push(item)
            }
          })
        })
        this.selectDataDownload = arr1
        this.form.ledgerType = 1
      }
      this.visible = true
    },
    // 操作目录下的子级
    opertaionFolderNode(fnName, data) {
      this.formDataCopy = data
      if (fnName === 'addDir') {
        this.opertaionDirNode(fnName, data)
      } else if (fnName === 'addClass') {
        this.opertaionDirNode(fnName, data)
      } else if (fnName === 'dirSet') {
        this.dirSetShow = 'dirSet'
        this.modificationShow = false
        this.visibleType = 'edit'
        this.form.parentLedgerIds = `${data.data.parentLedgerIds},${data.data.ledgerId}`
        this.form.parentLedgerName = data.data.parentLedgerName
        this.form.ledgerLevel = data.data.ledgerLevel
        this.form.ledgerName = data.data.ledgerName
        this.form.id = data.data.id
        this.form.ledgerType = 0
        this.form.viewPerm = data.data.viewPerm.toString()
        this.form.downloadPerm = data.data.downloadPerm.toString()
        this.form.downloadUserIds = []
        this.form.viewUserIds = []
        if (data.data.viewUserIds != null) {
          this.form.viewUserIds = data.data.viewUserIds.split(',')
          this.staffSelect = data.data.viewUserIds.split(',')
          let arr = []
          this.staffSelect.forEach((el) => {
            this.staffData.forEach((item) => {
              if (el === item.id) {
                arr.push(item)
              }
            })
          })
          this.selectData = arr
          this.authoritySeeList = arr
        }
        if (data.data.downUserIds != null) {
          this.form.downloadUserIds = data.data.downUserIds.split(',')
          this.staffSelectDownload = data.data.downUserIds.split(',')
          let arr1 = []
          this.staffSelectDownload.forEach((el) => {
            this.staffDataDownload.forEach((item) => {
              if (el === item.id) {
                arr1.push(item)
              }
            })
          })
          this.selectDataDownload = arr1
          this.authorityDownloadist = arr1
        }
        this.visible = true
      } else if (fnName === 'delDir') {
        // delDirOrClass
        this.delVisible = true
        this.isMoveFile = 1
        this.delId = data.data.ledgerId
      }
    },
    // 操作科室
    opertaionClassNode(fnName, data) {
      this.formData = data
      if (fnName === 'classSet') {
        this.dirSetShow = 'classSet'
        this.modificationShow = false
        this.visibleType = 'edit'
        this.form.parentLedgerIds = `${data.data.parentLedgerIds},${data.data.ledgerId}`
        this.form.parentLedgerName = data.data.parentLedgerName
        this.form.ledgerLevel = data.data.ledgerLevel
        this.form.ledgerName = data.data.ledgerName
        this.form.id = data.data.id
        this.form.ledgerType = 1
        this.form.viewPerm = data.data.viewPerm.toString()
        this.form.downloadPerm = data.data.downloadPerm.toString()
        this.visible = true
        this.form.viewUserIds = []
        this.form.downloadUserIds = []
        if (data.data.viewUserIds != null) {
          this.form.viewUserIds = data.data.viewUserIds.split(',')
          this.staffSelect = data.data.viewUserIds.split(',')
          let arr = []
          this.staffSelect.forEach((el) => {
            this.staffData.forEach((item) => {
              if (el === item.id) {
                arr.push(item)
              }
            })
          })
          this.selectData = arr
          this.authoritySeeList = arr
        }
        if (data.data.downUserIds != null) {
          this.form.downloadUserIds = data.data.downUserIds.split(',')
          this.staffSelectDownload = data.data.downUserIds.split(',')
          let arr1 = []
          this.staffSelectDownload.forEach((el) => {
            this.staffDataDownload.forEach((item) => {
              if (el === item.id) {
                arr1.push(item)
              }
            })
          })
          this.selectDataDownload = arr1
          this.authorityDownloadist = arr1
        }
      } else if (fnName === 'delClass') {
        this.isMoveFile = 0
        this.delId = data.data.ledgerId
        this.$confirm('是否删除分类（删除分类会同时删除掉分类下的所有文件数据）?', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.delSubmit()
        })
      }
    },
    // 权限修改弹窗取消
    modificationCancel() {
      this.modificationShow = false
      this.dialogModification = false
    },
    // 权限修改弹窗确定
    modificationTrue() {
      this.modificationShow = true
      this.visible = false
      this.submit()
      this.dialogModification = false
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          // if (this.form.viewUserIds.length > 0 && this.form.downloadUserIds.length == 0) {
          //   this.$message.error('请选择可查看和下载权限人员')
          //   return
          // } else if (this.form.downloadUserIds.length && this.form.viewUserIds.length == 0 && this.form
          //   .viewPerm ==
          //   1) {
          //   this.$message.error('请选择可查看权限人员')
          //   return
          // } else if (this.form.viewUserIds.length == 0 && this.form.downloadUserIds.length == 0 && this.form
          //   .viewPerm ==
          //   1) {
          //   this.$message.error('请选择权限人员')
          //   return
          // }
          if (this.form.viewPerm == 1) {
            this.form.downloadPerm = '1'
          }
          if (this.form.viewPerm == 0) {
            this.form.viewUserIds = []
          }
          if (this.form.downloadPerm == 0) {
            this.form.downloadUserIds = []
          }
          let arr = [...this.form.viewUserIds, ...this.form.downloadUserIds]
          this.form.viewUserIds = Array.from(new Set(arr))
          let fn = this.visibleType === 'add' ? 'addDirOrClass' : 'updateDirOrClass'
          if (this.visibleType === 'add') {
            this.$api[fn](this.form).then((res) => {
              if (res.code === '200') {
                this.$message({
                  message: '保存成功',
                  type: 'success'
                })
                this.closeDialog()
                this.getTreeList()
              }
            })
          } else {
            if (this.modificationShow) {
              this.$api[fn](this.form).then((res) => {
                if (res.code === '200') {
                  this.$message({
                    message: '保存成功',
                    type: 'success'
                  })
                  this.closeDialog()
                  this.getTreeList()
                }
              })
            } else {
              this.dialogModification = true
              this.form.updatePermRange = 0
            }
          }
        }
      })
    },
    closeDialog() {
      this.form = {
        createName: '',
        ledgerLevel: '',
        ledgerName: '',
        ledgerType: '',
        parentLedgerIds: '',
        parentLedgerName: '',
        viewPerm: '0',
        downloadPerm: '0'
      }
      this.visible = false
    },
    uploadSuccess() {
      this.pageData.current = 1
      this.getTableList()
    },
    filterNode(value, data) {
      if (!value) return true
      return data.ledgerName.indexOf(value) !== -1
    },
    isDrag(node) {
      return this.$auth('fileManager:edit')
    },
    nodeClick(data, node) {
      this.pageData.current = 1
      this.checkedTreeNode = data
      this.getTableList()
    },
    nodeDrop(source, target, ev) {
      if (!this.$auth('fileManager:edit')) {
        this.$message.error('权限不足')
        return
      }
      this.dragNodeObj = {}
      this.dragNodeObj.sourceFileMenuId = source.data.ledgerId
      this.dragNodeObj.sourceLedgerType = source.data.ledgerType
      this.dragNodeObj.targetFileMenuId = target.data.ledgerId
      this.dragNodeObj.targetLedgerType = target.data.ledgerType
      this.$confirm('移动后将继承新位置的权限数据。', '确认移动', {
        cancelButtonClass: 'el-button--primary is-plain',
        confirmButtonText: '确认移动',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.pageData.current = 1
        this.$api.dragMoveFile(this.dragNodeObj).then((res) => {
          this.$message[res.code == 200 ? 'success' : 'error'](res[`${res.code == 200 ? 'msg' : 'message'}`])
          this.getTreeList()
        })
        this.getTreeList()
      })
      this.getTreeList()
    },
    closeDelDialog() {
      this.delVisible = false
    },
    delSubmit() {
      let params = {
        isMoveFile: this.isMoveFile,
        ledgerId: this.delId
      }
      this.$api.delDirOrClass(params).then((res) => {
        if (res.code == 200) {
          this.delVisible = false
          this.getTreeList()
          this.$message({
            message: '删除成功',
            type: 'success'
          })
        }
      })
    },
    search() {
      this.showSearchText = true
      this.pageData.current = 1
      this.getTableList()
    },
    getTreeList() {
      this.treeLoading = true
      this.$api
        .fileDirTree()
        .then((res) => {
          if (res.code === '200') {
            let list = this.$tools.transData(res.data, 'ledgerId', 'parentLedgerId', 'children')
            this.treeData = list
            this.checkedTreeNode.ledgerId = res.data[0].ledgerId
            this.checkedTreeNode.ledgerName = res.data[0].ledgerName
            this.getTableList()
          } else {
            this.treeData = []
          }
        })
        .finally(() => {
          this.treeLoading = false
        })
    },
    getTableList() {
      this.tableLoading = true
      const page = { ...this.pageData }
      let params = {
        dataName: this.fileName,
        uploadYear: this.uploadYear,
        fileMenuId: this.checkedTreeNode.ledgerId || '',
        page
      }
      delete params.page.total
      this.$api
        .fileList(params)
        .then((res) => {
          if (res.code === '200') {
            this.tableLoading = false
            this.tableData = res.data.records
            this.pageData.total = res.data.total
          }
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    // 分页
    handleSizeChange(val) {
      this.pageData.size = val
      this.getTableList()
    },
    // 分页
    handleCurrentChange(val) {
      this.pageData.current = val
      this.getTableList()
    },
    // 弹窗列表分页
    handleSizeChangeDialog(val) {
      this.pageDataDialog.size = val
      this.queryFileGetList()
    },
    // 弹窗列表分页
    handleCurrentChangeDialog(val) {
      this.pageDataDialog.current = val
      this.queryFileGetList()
    },
    // 重置
    reset() {
      this.fileName = ''
      this.uploadYear = ''
      this.pageData.current = 1
      this.search()
      this.showSearchText = false
    },
    // 访问记录重置
    resetQueryFile() {
      this.pageDataDialog.userName = ''
      this.pageDataDialog.deptName = ''
      this.pageDataDialog.operate = ''
      this.pageDataDialog.startTime = null
      this.pageDataDialog.endTime = null
      this.queryFileTime = null
      this.pageDataDialog.current = 1
      this.queryFileGetList()
    },
    // 访问记录查询
    searchQueryFile() {
      this.pageDataDialog.current = 1
      this.queryFileGetList()
    },
    handleSelectionChange(data) {
      this.selectionList = data
    },
    batchDel() {
      this.$confirm('是否删除文件?文件将在回收站内保留90天', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let ids = this.selectionList.map((el) => el.id).join(',')
        this.$api
          .delFile({
            ids: ids
          })
          .then((res) => {
            if (res.code === '200') {
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
              this.getTableList()
              this.clearSelectionTable()
            }
          })
      })
    },
    // 删除
    del(row) {
      this.$confirm('是否删除文件?文件将在回收站内保留90天', '信息提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api
          .remove(
            {
              menuId: row.menuId
            },
            {
              'operation-type': 3,
              'operation-id': row.menuId,
              'operation-name': row.menuName
            }
          )
          .then((res) => {
            if (res.code == 200) {
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
              this.getTreeList()
              this.getTableList()
            } else {
              this.$message.error(res.data)
            }
          })
      })
      // .catch(() => {
      //   this.$message({
      //     type: 'info',
      //     message: '已取消删除'
      //   })
      // })
    },
    uploadFile() {
      this.handerType = 'add'
      this.uploadVisible = true
    },
    control(type, row) {
      switch (type) {
        case 'view':
          let fileTypeList = ['.pdf', '.docx', '.xlsx', '.png', '.jpg', '.jpeg', '.bmp', '.mp4', '.mp3', '.ogg', '.m4a', '.wav']
          if (fileTypeList.includes(row.fileExtension)) {
            this.checkRow = row
            this.viewVisible = true
          } else {
            this.$message.warning('暂不支持此类文件在线预览，若需查看请下载')
          }
          this.$api.insertFileAccessRecord({
            fileId: row.id,
            operate: 0
          })
          break
        case 'edit':
          this.editForm = row
          this.handerType = 'edit'
          this.uploadVisible = true
          break
        case 'download':
          this.downLoading = true
          const userInfo = this.$store.state.user.userInfo.user
          let params = {
            fileName: row.fileName,
            fileUrl: row.fileUrl
          }
          axios({
            method: 'get',
            url: __PATH.SPACE_API + 'SecurityLedger/downloadFile',
            params: params,
            responseType: 'blob',
            headers: {
              Authorization: 'Bearer ' + this.$store.state.user.token
            }
          })
            .then((res) => {
              this.downLoading = false
              let name = row.fileName
              let blob = new Blob([res.data]) // {type: "application/vnd.ms-excel" }
              let url = window.URL.createObjectURL(blob) // 创建一个临时的url指向blob对象
              // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
              let a = document.createElement('a')
              a.href = url
              a.download = decodeURI(name)
              a.click()
              // 释放这个临时的对象url
              window.URL.revokeObjectURL(url)
            })
            .catch((res) => {
              this.downLoading = false
              this.$message.error('下载失败！')
            })
          this.$api.insertFileAccessRecord({
            fileId: row.id,
            operate: 1
          })
          break
        case 'del':
          this.$confirm('是否删除文件?文件将在回收站内保留90天', '提示', {
            cancelButtonText: '取消',
            confirmButtonText: '确定',
            type: 'warning'
          }).then(() => {
            this.$api
              .delFile({
                ids: row.id
              })
              .then((res) => {
                if (res.code === '200') {
                  this.$message({
                    type: 'success',
                    message: '删除成功!'
                  })
                  this.clearSelectionTable()
                  this.getTableList()
                }
              })
          })
          // .catch(() => {
          //   this.$message({
          //     type: 'info',
          //     message: '已取消删除'
          //   })
          // })
          break
        default:
          break
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.monitor-setting {
  height: 100%;
  display: flex;
  .monitor-setting-left {
    width: 246px;
    height: 100%;
    // padding: 8px 16px 16px 16px;
    background: #fff;
    border-radius: 4px;
    ::v-deep .box-card {
      .card-title {
        position: relative;
        .select-servive {
          position: absolute;
          right: 0;
          top: 2px;
          cursor: pointer;
          font-family: PingFangSC-Regular, 'PingFang SC';
          font-size: 14px;
          color: $color-primary;
          font-weight: 600;
          padding-left: 15px;
          i {
            font-weight: 600;
            font-size: 13px;
            margin-right: 2px;
          }
        }
      }
      .card-body {
        border-top: 1px solid #dcdfe6;
        padding-top: 10px;
        // firefox隐藏滚动条
        scrollbar-width: none;
        // chrome隐藏滚动条
        &::-webkit-scrollbar {
          display: none;
        }
        .filter {
          margin-bottom: 10px;
        }
        .custom-tree-node {
          padding-right: 10px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;
          min-width: 130px;
          .custom-tree-node-wrapper {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            .custom-tree-node-label {
              width: calc(100% - 40px);
              display: inline-block;
              display: inline-block;
              white-space: nowrap;
              /* 防止换行 */
              overflow: hidden;
              /* 隐藏超出部分 */
              text-overflow: ellipsis;
              text-align: left;
            }
          }
        }
        .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
          background-color: #d9e1f8;
        }
      }
    }
  }
  .el-tree-node__content {
    position: relative;
    .operate-btns {
      width: 30px;
      position: absolute;
      right: 2px;
    }
  }
  .monitor-setting-right {
    height: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
    .right-heade {
      padding: 0 200px 10px 10px !important;
      margin-left: 16px;
      background: #fff;
      border-radius: 4px;
      ::v-deep .el-input {
        width: 200px;
      }
      & > div {
        margin-right: 10px;
        margin-top: 10px;
      }
    }
    .right-headecopy {
      display: flex;
      padding: 0 0px 10px 10px !important;
      margin-left: 16px;
      background: #fff;
      border-radius: 4px;
      ::v-deep .el-input {
        width: 120px;
      }
      & > div {
        margin-right: 10px;
        margin-top: 10px;
      }
    }
    .right-content {
      margin-left: 16px;
      margin-top: 16px;
      background: #fff;
      padding: 10px;
      border-radius: 4px;
      flex: 1;
      overflow: hidden;
      .btns-group {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        & > div {
          display: flex;
        }
        .btns-group-control {
          > div {
            margin-left: 10px;
          }
          // & > div, & > button {
          //   margin-right: 10px;
          // }
        }
      }
      .table-content {
        height: calc(100% - 45px);
        ::v-deep .el-table .el-table__body-wrapper td.el-table__cell {
          border-right: 1px solid #ededf5;
          .tooltip-over-td {
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }
}
::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: linear-gradient(to right, #d9e1f8, #fff);
  font-weight: bold;
}
::v-deep .interDialogDiv {
  background: #fff;
  border-radius: 4px;
  .el-dialog__header {
    height: 56px;
    padding: 15px 20px;
    background: #fff;
    border-bottom: 1px solid $color-text-secondary;
    box-shadow: 0 0 12px 3px rgb(0 0 0 / 10%);
    .el-dialog__title,
    .dialog-title {
      font-size: 18px;
      font-family: 'PingFang SC-Medium', 'PingFang SC';
      font-weight: 500;
      color: $color-text;
    }
    .el-dialog__close {
      color: $color-text;
      font-weight: 600;
      font-size: 18px;
    }
  }
  .el-dialog__body {
    // height: 300px;
    padding: 15px;
    // max-height: 600px;
    overflow-y: hidden;
    display: flex;
    .dialog-content {
      flex: 1;
      .el-row {
        height: 100%;
      }
    }
    // height: calc(100% - 110px);
  }
  .el-dialog__footer {
    max-height: 56px;
    padding: 10px 20px;
    background: #fff;
    box-shadow: 0 0 12px 3px rgb(0 0 0 / 10%);
  }
}
::v-deep .el-dialog__body {
  padding-top: 6px;
  padding-bottom: 0px;
}
::v-deep .el-date-editor .el-range__icon {
  line-height: 24px;
}
.radioGroup {
  margin: 24px;
  .el-radio {
    margin-top: 16px;
  }
}
.radioModification {
  margin: 24px;
  margin-top: 0px;
  .el-radio {
    margin-top: 16px;
    display: block;
  }
}
.paginationClass {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
  .tipsClass {
    line-height: 32px;
  }
}
.el-dropdown-link {
  color: #3562db;
  padding-left: 10px;
  cursor: pointer;
}
.searchClassText {
  margin-left: 100px;
  font-size: 18px;
}
.titleClass {
  display: flex;
  justify-content: start;
  padding: 0 30px;
  .iconSpanClass {
    height: 24px;
    width: 4px;
    background-color: #3664dd;
    margin-right: 10px;
  }
  .statistics {
    font-size: 16px;
  }
}
.interview {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
  padding: 0 30px;
  .interviewTop {
    height: 150px;
    width: 450px;
    border: 1px solid #e1e6ed;
    .interviewInterview {
      font-size: 20px;
      text-align: center;
      line-height: 75px;
    }
    .interviewInterviewNumber {
      font-size: 40px;
      text-align: center;
    }
  }
}
.fileNameClick {
  cursor: pointer;
  color: #189eff;
}
.modification {
  margin-left: 10px;
  .modificationSetting {
    font-size: 18px;
    margin-bottom: 10px;
  }
  .modificationSee {
    font-size: 16px;
    margin-bottom: 10px;
  }
  .modificationSeeTop {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    .modificationSeeText {
      margin-left: 20px;
      font-size: 14px;
      width: 280px;
      /* 设置div的宽度 */
      white-space: nowrap;
      /* 保证文字在一行内显示 */
      overflow: hidden;
      /* 超出部分隐藏 */
      text-overflow: ellipsis;
      /* 使用省略号表示文字被截断 */
    }
  }
}
.interDialogDiv {
  .sapce_content {
    margin: 0 auto;
    background: #fff;
    padding: 10px 0;
    border-radius: 4px;
    height: 400px;
    display: flex;
    width: 100%;
    .left {
      text-align: center;
      width: 300px;
      height: 100%;
      background-color: #fff;
      border-radius: 5px;
      overflow: hidden;
    }
    .center {
      width: 300px;
      border-left: 1px solid #dcdfe6;
      background-color: #fff;
      height: 100%;
      margin-left: 10px;
      border-radius: 5px;
      padding-left: 16px;
      overflow: scroll;
      // display: flex;
      .personCheck {
        width: 100%;
        display: flex;
        padding: 10px 0;
        img {
          vertical-align: middle;
        }
      }
    }
    .right {
      width: calc(100% - 620px);
      border-left: 1px solid #dcdfe6;
      background-color: #fff;
      height: 100%;
      margin-left: 10px;
      border-radius: 5px;
      padding-left: 16px;
      overflow: scroll;
      .top {
        display: flex;
        padding: 0 12px;
        justify-content: space-between;
        .label,
        .dept {
          font-size: 14px;
          color: #7f848c;
        }
        .num {
          font-size: 14px;
          color: #333333;
          margin-left: 10px;
        }
        .dept {
          margin-left: 10px;
        }
        .clear {
          font-size: 12px;
          color: #3562db;
          cursor: pointer;
        }
      }
      .item-list {
        display: flex;
        cursor: pointer;
        padding: 0 12px;
        justify-content: space-between;
        margin-top: 8px;
        .remove {
          margin: auto 0;
        }
      }
      .item-list:hover {
        background: #e6effc;
      }
    }
    .info {
      margin-left: 8px;
      .name {
        font-weight: 500;
        color: #333333;
      }
      .mobile {
        font-size: 12px;
        color: #7f848c;
      }
    }
  }
}
.sino_tree_box {
  margin-top: 10px;
  height: calc(100% - 40px);
  overflow: auto;
  .custom-tree-node-label {
    display: inline-block;
    width: 200px;
    white-space: nowrap;
    /* 防止换行 */
    overflow: hidden;
    /* 隐藏超出部分 */
    text-overflow: ellipsis;
    text-align: left;
  }
}
::v-deep .el-tree .el-tree-node__content {
  height: 36px;
  line-height: 16px;
  padding: 0;
}
::v-deep .el-tree-node {
  .is-leaf + .el-checkbox .el-checkbox__inner {
    display: inline-block;
  }
  .el-checkbox .el-checkbox__inner {
    display: none;
  }
}
::v-deep .el-collapse-item__content {
  padding-bottom: 0px !important;
}
::v-deep .el-checkbox__input {
  display: inline-block !important;
  margin-bottom: 26px !important;
}
</style>
