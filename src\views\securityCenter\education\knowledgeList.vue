<template>
  <PageContainer>
    <div slot="header">
      <div class="search-header">
        <div class="search-from">
          <el-input v-model.trim="repositoryName" style="margin-right: 18px; width: 300px;" placeholder="请输入文档名称查询"></el-input>
          <el-button type="primary" @click="reset">重置</el-button>
          <el-button type="primary" @click="searchClick">查询</el-button>
          <el-button type="primary" @click="addDate">上传</el-button>
        </div>
        <div>
          <el-button type="primary" :disabled="multipleSelection.length != 1" @click="downLoad">下载</el-button>
          <el-button type="primary" :disabled="multipleSelection.length != 1" @click="deleteData">删除</el-button>
        </div>
      </div>
    </div>
    <div slot="content" style="height: 100%; margin-top: 16px; background-color: #fff;">
      <div class="table-content">
        <el-table
          v-loading="tableLoading"
          :data="tableData"
          reserve-selection="true"
          :row-key="getRowKeys"
          border
          stripe
          :cell-style="{ padding: '8px' }"
          :height="tableHeight"
          @selection-change="handleSelectionChange"
        >
          <template slot="empty">
            <p class="tableEmptyLabel">{{ $store.state.tableLabel }}</p>
          </template>
          <el-table-column type="selection" width="55" align="center"></el-table-column>
          <el-table-column type="index" label="序号" width="65">
            <template slot-scope="scope">
              <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="repositoryName" show-overflow-tooltip label="文档名称"></el-table-column>
          <el-table-column prop="repositoryExplain" show-overflow-tooltip label="文档说明"></el-table-column>
          <el-table-column prop="uploadUserName" show-overflow-tooltip label="上传人"></el-table-column>
          <el-table-column prop="uploadTime" show-overflow-tooltip label="上传时间"></el-table-column>
        </el-table>
        <div class="table-page">
          <el-pagination
            class="pagination imas_page"
            :current-page="paginationData.currentPage"
            :page-sizes="[15, 30, 50, 100]"
            :page-size="paginationData.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="paginationData.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
    </div>
  </PageContainer>
</template>

<script>
export default {
  name: 'knowledgeList',
  data() {
    return {
      tableData: [],
      multipleSelection: [],
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      tableLoading: false,
      repositoryName: ''
    }
  },
  computed: {
    // eslint-disable-next-line vue/no-dupe-keys
    tableHeight() {
      return document.body.clientHeight - 260
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    addDate() {
      this.$router.push({
        name: 'newAttachment'
      })
    },
    searchClick() {
      // this.$store.commit('changeNetworkError', '查询失败，请检查您的网络…')
      // this.$store.commit('changeImasTableLabel')
      this.paginationData.currentPage = 1
      this.init()
    },
    getRowKeys(row) {
      return row.id
    },
    init() {
      this.tableLoading = true
      this.$api
        .ipsmListData({
          pageSize: this.paginationData.pageSize,
          currentPage: this.paginationData.currentPage,
          repositoryName: this.repositoryName
        })
        .then((res) => {
          this.tableLoading = this.$store.state.loadingShow
          if (res.code == '200') {
            this.paginationData.total = parseInt(res.data.total)
            this.tableData = res.data.list
          } else if (res.message) {
            this.$message.error(res.message)
          }
          this.tableLoading = false
        })
    },
    reset() {
      // this.$store.commit('changeNetworkError', '查询失败，请检查您的网络…')
      // this.$store.commit('changeImasTableLabel', 'init')
      this.paginationData = {
        currentPage: 1,
        pageSize: 15,
        total: 0
      }
      this.repositoryName = ''
      this.init()
    },
    // 删除
    deleteData() {
      this.$confirm('确定要删除这条知识库吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api
          .ipsmRepositoryDelete({
            repositoryId: this.multipleSelection[0].repositoryId
          })
          .then((res) => {
            if (res.code == 200) {
              // 删除最后一页的最后一条数据时跳转回最后一页的上一页
              this.paginationData.currentPage = this.$tools.paginationData(this.paginationData.total, this.paginationData.pageSize, this.paginationData.currentPage)
              this.$message.success(res.message)
              this.init()
            } else {
              this.$message.error(res.message)
            }
          })
      })
    },
    // 文档下载
    downLoad(row) {
      let baseInfo = JSON.parse(sessionStorage.getItem('LOGINDATA'))
      let unitCode = baseInfo.unitCode
      let hospitalCode = baseInfo.hospitalCode
      let url = `${__PATH.VUE_AQ_URL}repository/download?unitCode=${unitCode}&hospitalCode=${hospitalCode}&repositoryUrl=${this.multipleSelection[0].repositoryUrl || ''}`
      // location.href = url;
      this.getBlob(url).then((res) => {
        let a = document.createElement('a')
        let fileType = this.multipleSelection[0].repositoryUrl.split('.')[this.multipleSelection[0].repositoryUrl.split('.').length - 1]
        a.style.display = 'none'
        a.download = this.multipleSelection[0].repositoryName + '.' + fileType
        a.href = window.URL.createObjectURL(res)
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
      })
    },
    getBlob(url) {
      return new Promise((resolve) => {
        const xhr = new XMLHttpRequest()
        xhr.open('GET', url, true)
        xhr.responseType = 'blob'
        xhr.setRequestHeader('Authorization', 'Bearer ' + this.$store.state.user.token)
        xhr.onload = () => {
          if (xhr.status === 200) {
            resolve(xhr.response)
          }
        }
        xhr.send()
      })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleSizeChange(val) {
      this.paginationData.currentPage = 1
      this.paginationData.pageSize = val
      this.init()
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.init()
    }
  }
}
</script>

<style lang="scss" scoped>
.search-header {
  padding: 10px 16px;

  .search-from {
    padding-bottom: 12px;

    & > div {
      margin-top: 12px;
      margin-right: 10px;
    }
  }
}

.table-content {
  padding: 16px;
  height: calc(100% - 80px);
}

</style>
