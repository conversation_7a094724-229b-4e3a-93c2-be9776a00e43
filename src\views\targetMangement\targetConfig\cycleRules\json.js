/*
 * @Author: hedd
 * @Date: 2024-02-21 13:55:19
 * @LastEditTime: 2024-02-21 14:11:37
 * @FilePath: \ihcrs_pc\src\views\targetMangement\targetConfig\cycleRules\json.js
 * @Description:
 */
import { cloneDeep } from 'lodash'
import dayjs from 'dayjs'
const yearList = [
  {
    value: 1,
    label: '上一年',
    disabled: false,
    children: [
    ]
  },
  {
    value: 2,
    label: '当前年',
    disabled: false,
    children: []
  },
  {
    value: 3,
    label: '下一年',
    disabled: false,
    children: []
  }
]

function setDayData (flag, year, monthObj, dayTotal) {
  for (let i = 0; i < dayTotal; i++) {
    let day = {
      value: i + 1,
      label: `${i + 1}日`,
      disabled: !!(flag && year === 3 && monthObj.value === 1 && i + 1 !== 1)
    }
    monthObj.children.push(day)
  }
}

export const mockData = function (flag = false) {
  let arr = cloneDeep(yearList)
  arr.forEach(el => {
    for (let i = 0; i < 12; i++) {
      let month = {
        value: i + 1,
        label: `${i + 1}月`,
        disabled: !!(flag && el.value === 3 && i + 1 > 1),
        children: []
      }
      el.children.push(month)
      let bigMonth = [1, 3, 5, 7, 8, 10, 12]
      let smallMonth = [4, 6, 9, 11]
      if (bigMonth.includes(i + 1)) {
        // 这里需要全部push 31天数据
        setDayData(flag, el.value, el.children[i], 31)
      } else if (smallMonth.includes(i + 1)) {
        // 这里push 30的数据
        setDayData(flag, el.value, el.children[i], 30)
      } else {
        // 这里处理2月的天数
        // 获取当前年
        let year = dayjs().year()
        let dayTotal = 28
        if (el.value === 1 && (year - 1) % 4 === 0) {
          dayTotal = 29
        }
        if (el.value === 2 && year % 4 === 0) {
          dayTotal = 29
        }
        if (el.value === 3 && (year + 1) % 4 === 0) {
          dayTotal = 29
        }
        setDayData(flag, el.value, el.children[i], dayTotal)
      }
    }
  })
  return arr
}
