<template>
  <div>
    <el-dialog custom-class="model-dialog" title="文件导入" :visible="visible" :before-close="close" modal-append-to-body width="600px" :modal="false">
      <div v-loading="loading" class="dialog-content" element-loading-text="文件导入中">
        <el-upload class="upload-demo" drag action="" :limit="1" accept=".xls,.xlsx" :before-upload="beforeAvatarUpload" :http-request="(file) => httpRequset(file)">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div slot="tip" class="el-upload__tip">只能上传xls/xlsx文件</div>
        </el-upload>
        <el-link type="primary" @click="download(type)">下载模板</el-link>
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  model: {
    prop: 'visible',
    event: 'update:visible'
  },
  props: {
    visible: Boolean,
    url: String,
    type: {
      type: String,
      default: 'unit'
    }
  },
  data() {
    return {
      loading: false,
      fileList: []
    }
  },
  methods: {
    close() {
      this.$emit('update:visible')
    },
    beforeAvatarUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < 2
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 500MB!')
      }
      return isLt2M
    },
    httpRequset(file) {
      this.loading = true
      this.$api.constructionUpload(__PATH.SPACE_API, this.url, file).then((res) => {
        if (res.code == 200) {
          this.$message.success('导入成功')
          this.loading = false
          this.$emit('update:visible')
          this.$emit('success')
        }
      })
    },
    download(type) {
      let fn = type === 'unit' ? 'constructionDownloadUnit' : 'constructionDownloadUser'
      let name = type === 'unit' ? '单位导入模板.xlsx' : '人员导入模板.xlsx'
      this.$api[fn]().then((res) => {
        let url = window.URL.createObjectURL(new Blob([res]), { type: 'application/vnd.ms-excel;charset=UTF-8' })
        let link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', name)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link) // 下载完成移除元素
        window.URL.revokeObjectURL(url) // 释放掉blob对象
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  background: #fff;
  padding: 16px;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
</style>
