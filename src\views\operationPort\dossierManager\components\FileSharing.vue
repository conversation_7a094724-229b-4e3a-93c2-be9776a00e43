<template>
  <el-dialog :title="title" :visible="visible" width="55%" :before-close="handleClosesubmit">
    <el-form ref="form" label-position="right" label-width="140px" :model="formData" :rules="rules">
      <el-row :gutter="24" style="margin: 0">
        <el-col :md="24">
          <el-form-item :label="`${name}范围`">
            <el-radio-group v-model="formData.shareType">
              <el-radio label="0">全员可见</el-radio>
              <el-radio label="1">指定范围</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col v-if="showItem" :md="12">
          <el-form-item :label="`${name}范围（部门）`" prop="shareDeptId">
            <el-cascader
              v-model="formData.shareDeptId"
              :placeholder="`请选择${name}范围（部门）`"
              :options="deptList"
              collapse-tags
              :props="{
                value: 'id',
                label: 'deptName',
                multiple: true,
                checkStrictly: true,
                emitPath: false
              }"
              :show-all-levels="false"
              clearable
              filterable
              size="small"
              style="width: 100%"
              @change="selectDept"
            >
            </el-cascader>
          </el-form-item>
        </el-col>
        <el-col v-if="showItem" :md="12">
          <el-form-item :label="`${name}范围（成员）`">
            <el-select v-model="formData.shareMemberId" collapse-tags multiple filterable :placeholder="`请选择${name}范围（成员）`" @change="handleSelectChange">
              <el-option v-for="item in personList" :key="item.id" :label="item.staffName" :value="item.id"> </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :md="12">
          <el-form-item :label="`${name}开始日期`">
            <el-date-picker v-model="formData.shareStartDate" :pickerOptions="pickerOptions" value-format="timestamp" type="date" :placeholder="`请选择${name}开始日期`">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :md="12">
          <el-form-item :label="`${name}结束日期`">
            <el-date-picker v-model="formData.shareEndDate" :pickerOptions="endPickerOptions" value-format="timestamp" type="date" :placeholder="`请选择${name}结束日期`" clearable>
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer">
      <el-button @click="handleClosesubmit">取消</el-button>
      <el-button type="primary" @click="handleSubmit">提交</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { transData } from '@/util'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    current: {
      type: Object,
      default: () => ({})
    },
    title: {
      type: String,
      default: '档案共享'
    },
    update: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const that = this
    return {
      formData: {
        shareType: '0',
        shareDeptId: [],
        shareMemberId: [],
        shareStartDate: Date.now(),
        shareEndDate: '',
        shareDeptName: '',
        shareMemberName: ''
      },
      pickerOptions: {
        disabledDate(date) {
          return that.formData.shareEndDate && date.getTime() >= that.formData.shareEndDate
        }
      },
      endPickerOptions: {
        disabledDate(date) {
          return that.formData.shareStartDate && date.getTime() <= that.formData.shareStartDate
        }
      },
      rules: {
        shareDeptId: [
          {
            required: true,
            message: '请选择共享范围（部门）',
            trigger: ['change', 'blur']
          }
        ]
      },
      showItem: false,
      deptList: [],
      personList: []
    }
  },
  computed: {
    name() {
      return this.title === '档案共享' ? '共享' : '借阅'
    }
  },
  watch: {
    formData: {
      handler(val) {
        this.showItem = val.shareType !== '0'
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    this.getDeptList()
  },
  methods: {
    handleSelectChange(val) {
      if (val.length) {
        this.formData.shareMemberName = this.personList
          .filter((item) => val.includes(item.id))
          .map((item) => item.staffName)
          .join(',')
      } else {
        this.formData.shareMemberName = ''
      }
    },
    findNameById(tree, targetIds) {
      const names = []
      function search(node) {
        if (targetIds.includes(node.id)) {
          names.push(node.deptName)
        }
        if (node.children) {
          node.children.forEach((child) => search(child))
        }
      }
      tree.forEach((node) => search(node))
      return names.join(',')
    },
    // 选择巡检部门
    selectDept(val) {
      if (val.length > 0) {
        this.formData.shareDeptName = this.findNameById(this.deptList, val)
        this.getLersonnelList(val)
      } else {
        this.personList = []
      }
      this.editRule = true
    },
    // 获取人员列表
    getLersonnelList(deptIds) {
      let params = {
        current: 1,
        size: 9999,
        officeId: deptIds.join(',')
      }
      this.$api.staffList(params).then((res) => {
        if (res.code == 200) {
          this.personList = res.data.records
        }
      })
    },
    // 获取部门列表
    getDeptList() {
      this.$api.getSelectedDept().then((res) => {
        if (res.code == '200') {
          this.deptList = transData(res.data, 'id', 'pid', 'children')
        }
      })
    },
    handleClosesubmit() {
      this.$refs.form.resetFields()
      this.$emit('update:visible', false)
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (!valid) return
        let message = '是否确认共享档案？'
        let title = '共享档案'
        if (this.update) {
          const { archiveId, archiveShareId } = this.current
          const params = {
            archiveId,
            archiveShareId,
            ...this.formData,
            shareMemberId: this.formData.shareMemberId.join(','),
            shareDeptId: this.formData.shareDeptId.join(',')
          }
          this.$api.fileManagement.archiveShareUpdate(params).then((res) => {
            if (res.code === '200') {
              this.$message.success('调整成功')
              this.handleClosesubmit()
              this.$emit('success')
            } else {
              this.$message.error('调整失败')
            }
          })
          return
        }
        if (this.title !== '档案共享') {
          message = '是否确认借阅合同？'
          title = '借阅合同'
        }
        this.$confirm(message, title, {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        })
          .then(() => {
            const { archiveId } = this.current
            const params = {
              archiveId,
              ...this.formData,
              shareMemberId: this.formData.shareMemberId.join(','),
              shareDeptId: this.formData.shareDeptId.join(',')
            }
            this.$api.fileManagement.insertShare(params).then((res) => {
              if (res.code === '200') {
                this.handleClosesubmit()
                this.$message.success(`${this.name}成功`)
                this.$emit('success')
              } else {
                this.$message.error(`${this.name}失败`)
              }
            })
          })
          .catch(() => {})
      })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-select {
  width: 100%;
}
::v-deep .el-date-editor.el-input {
  width: 100%;
}
::v-deep .el-input--small .el-input__inner {
  height: 32px !important;
}
</style>
