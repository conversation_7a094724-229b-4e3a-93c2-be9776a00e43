<template>
  <PageContainer>
    <div slot="content" class="role-content" style="height: 100%">
      <div class="role-content-right">
        <div style="height: 100%">
          <div class="search-from">
            <div>
              <el-date-picker
                v-model="dataRange"
                type="daterange"
                unlink-panels
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                clearable
                style="width: 240px; margin-right: 10px"
              />
              <el-button type="primary" plain @click="resetForm">重置</el-button>
              <el-button type="primary" @click="searchForm">查询</el-button>
            </div>
          </div>
          <div class="contentTable">
            <div class="contentTable-main table-content">
              <el-table v-loading="tableLoading" border style="width: 100%" :data="tableData" height="100%" stripe>
                <el-table-column prop="flowNo" label="流水号" show-overflow-tooltip></el-table-column>
                <el-table-column prop="placeName" label="场所名称" show-overflow-tooltip></el-table-column>
                <el-table-column prop="doorNo" label="对应门号" show-overflow-tooltip></el-table-column>
                <el-table-column prop="deviceName" label="设备名称" show-overflow-tooltip></el-table-column>
                <el-table-column prop="userNo" label="工号" show-overflow-tooltip></el-table-column>
                <el-table-column prop="userName" label="用户姓名" show-overflow-tooltip></el-table-column>
                <el-table-column prop="deptName" label="部门名称" show-overflow-tooltip></el-table-column>
                <el-table-column prop="cardNo" label="卡号" show-overflow-tooltip></el-table-column>
                <el-table-column prop="recDic" label="门方向" show-overflow-tooltip width="90px">
                  <template slot-scope="scope">
                    <span>{{ scope.row.recDic == 0 ? '进门' : scope.row.recDic == 1 ? '出门' : '无' }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="recStatus" label="记录状态" show-overflow-tooltip width="100px">
                  <template slot-scope="scope">
                    <!-- <span :class="scope.row.recStatus == 0 ? 'record' : 'delete'"> -->
                    <span>
                      {{ scope.row.recDic == 0 ? '无效' : scope.row.recDic == 1 ? '有效' : '报警' }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="recTime" label="记录时间" show-overflow-tooltip></el-table-column>
              </el-table>
            </div>
            <div class="contentTable-footer">
              <el-pagination
                :current-page="pagination.current"
                :page-sizes="pagination.pageSizeOptions"
                :page-size="pagination.size"
                :layout="pagination.layoutOptions"
                :total="pagination.total"
                @size-change="paginationSizeChange"
                @current-change="paginationCurrentChange"
              >
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import dayjs from 'dayjs'
import tableListMixin from '@/mixins/tableListMixin.js'
export default {
  name: 'DeviceManagementIndex',
  mixins: [tableListMixin],
  data() {
    return {
      subLoading: false,
      dataRange: [], // 时间范围
      tableLoading: false,
      tableData: [],
      drawerPageNo: 1,
      drawerPageSize: 10
    }
  },
  created() {
    this.getDataList()
  },
  methods: {
    getDataList() {
      let data = {
        pageNum: this.pagination.current,
        pageSize: this.pagination.size,
        startTime: this.dataRange.length ? this.dataRange[0] : '',
        endTime: this.dataRange.length ? this.dataRange[1] : ''
      }
      this.tableLoading = true
      this.$api.getAccessControlRecords(data).then((res) => {
        if (res.code == '200') {
          this.tableData = res.data.records
          this.pagination.total = parseInt(res.data.total)
        }
      })
      this.tableLoading = false
    },
    // 重置
    resetForm() {
      this.dataRange = []
      this.pagination.size = 15
      this.pagination.current = 1
      this.getDataList()
    },
    // 查询
    searchForm() {
      this.pagination.current = 1
      this.getDataList()
    },
    // 操作记录分页
    pagingLoad() {
      if (this.activities.length < this.drawerTotal) {
        this.drawerPageNo += 1
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.el-tree-node__content {
  width: 100%;
  .custom-tree-node {
    display: inline-block;
    width: 100%;
    overflow: hidden;
    .item {
      display: inline-block;
      width: calc(100%);
      // width: calc(100% - 10px);
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
  }
}
::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: linear-gradient(to right, #d9e1f8, #fff);
}
::v-deep .el-tree-node__content {
  height: 32px;
}
.role-content {
  height: 100%;
  display: flex;
  .role-content-right {
    height: 100%;
    min-width: 0;
    padding: 20px;
    background: #fff;
    border-radius: 4px;
    flex: 1;
    .search-from {
      padding-bottom: 12px;
      // display: flex;
      // justify-content: space-between;
      & > div {
        margin-right: 10px;
      }
      & > button {
        margin-top: 12px;
      }
    }
    .contentTable {
      height: calc(100% - 47px);
      display: flex;
      flex-direction: column;
      .contentTable-main {
        flex: 1;
        overflow: auto;
      }
      .contentTable-footer {
        padding: 10px 0 0;
      }
    }
  }
  .content {
    width: 100%;
    max-height: 500px !important;
    overflow: auto;
    background-color: #fff !important;
  }
}
::v-deep .el-tabs__nav-scroll {
  width: 80%;
  margin: 0 auto;
}
::v-deep .el-drawer__open .el-drawer.rtl {
  background: #f6f5fa;
  border-radius: 4px;
}
::v-deep .el-drawer__header {
  height: 56px;
  padding: 15px 20px;
  margin-bottom: 0;
  background: #fff;
  border-bottom: 1px solid $color-text-secondary;
  box-shadow: 0 0 12px 3px rgb(0 0 0 / 10%);
  & > span {
    font-size: 18px;
    font-family: 'PingFang SC-Medium', 'PingFang SC';
    font-weight: 500;
    color: $color-text;
  }
  .el-dialog__close {
    color: $color-text;
    font-weight: 600;
    font-size: 18px;
  }
}
::v-deep .el-drawer__body {
  background-color: #fff;
  margin: 20px;
  padding: 20px 10px;
  height: calc(100% - 80px);
  overflow-y: auto;
}
::v-deep .el-timeline-item__timestamp.is-bottom {
  font-size: 14px;
  position: absolute;
  left: -100px;
  top: -5px;
  font-weight: 600;
  color: #121f3e;
}
::v-deep .el-timeline {
  padding-left: 120px;
}
.timeContent {
  height: 100%;
  overflow: auto;
  .time {
    font-size: 14px;
    font-family: 'PingFang SC-Regular', 'PingFang SC';
    color: #414653;
  }
  .continer {
    display: flex;
    // justify-content: space-between;
    flex-wrap: wrap;
    .item {
      height: 32px;
      flex: 1;
      padding: 0 16px;
      background-color: #faf9fc;
      line-height: 32px;
      white-space: nowrap;
      margin-bottom: 20px;
      margin-right: 10px;
    }
    .itemContent {
      height: 32px;
      width: 220px;
      padding: 0 16px;
      background-color: #faf9fc;
      line-height: 32px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      cursor: pointer;
      margin-right: 10px;
    }
  }
}
::v-deep .el-timeline-item__node--normal {
  background: #fff;
  border: 2px solid #3562db;
}
.noData {
  display: inline-block;
  padding-bottom: 10px;
  width: 100%;
  margin: 0;
  font-size: 14px;
  color: #999;
  text-align: center;
}
.record {
  color: #66b1ff !important;
}
.delete {
  color: red !important;
}
::v-deep .el-tree-node {
  white-space: normal;
  // outline: 0;
}
.rightBtn {
  height: 36px;
  margin: 0;
  margin-right: 10px;
}
.leadFile {
  display: flex;
}
.leadFile_item {
  margin: 10px 35px;
  color: #66b1ff;
  cursor: pointer;
}
::v-deep .el-message-box.no-close-btn .el-message-box__headerbtn {
  display: none;
}
</style>
