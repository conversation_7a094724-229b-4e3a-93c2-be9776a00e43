<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div v-loading="pageLoading" class="content_box">
        <el-form ref="reportForm" :model="reportForm" :inline="true" class="form-inline" :rules="rules" label-width="140px">
          <!-- <el-form-item v-if="type && type == 'pro'" label="医院名称" prop="hospitalCode">
            <el-select v-model="reportForm.hospitalCode" placeholder="请选择医院" @change="hospitalChanged">
              <el-option v-for="item in hospitalList" :key="item.hospitalCode" :label="item.hospitalName" :value="item.hospitalCode"></el-option>
            </el-select>
          </el-form-item>
          <br /> -->
          <el-form-item label="检查类型" prop="checkType">
            <el-select v-model="reportForm.checkType" placeholder="请选择检查类型">
              <el-option v-for="item in checkTypeList" :key="item.id" :label="item.dictName" :value="item.dictCode"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="隐患区域" prop="region">
            <el-cascader
              ref="myCascader"
              v-model="reportForm.questionAddressCode"
              :options="gridList"
              :props="{ checkStrictly: true, value: 'id', label: 'gridName', multiple: true }"
              placeholder="请选择隐患区域"
              @change="hangdleChange"
            ></el-cascader>
          </el-form-item>
          <br />
          <el-form-item label="隐患分类" prop="questionDetailCode">
            <el-cascader
              ref="classifyCascader"
              v-model="reportForm.questionDetailCode"
              :options="hiddenClassifyList"
              :props="{ expandTrigger: 'hover', checkStrictly: true, value: 'id', emitPath: true }"
              placeholder="请选择隐患分类"
              @change="classifyChange"
            ></el-cascader>
          </el-form-item>
          <el-form-item label="隐患等级" prop="riskCode" class="left-margin">
            <el-select v-model="reportForm.riskCode" placeholder="请选择隐患等级">
              <el-option v-for="item in hiddenLevelList" :key="item.id" :label="item.dictName" :value="item.dictCode"></el-option>
            </el-select>
          </el-form-item>
          <br />
          <el-form-item label="责任部门" prop="department">
            <el-select v-model="reportForm.dutyDeptCode" placeholder="请选择责任部门" filterable>
              <el-option v-for="item in responsibleDepartmentList" :key="item.id" :label="item.teamName" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="要求整改完成时间" prop="date" class="left-margin">
            <el-date-picker
              v-model="reportForm.rectificationPlanTime"
              type="date"
              placeholder="选择日期"
              :picker-options="pickerOptions"
              value-format="yyyy-MM-dd"
            ></el-date-picker>
          </el-form-item>
          <br />
          <el-form-item label="描述" prop="questionContent">
            <el-input v-model="reportForm.questionContent" type="textarea" show-word-limit maxlength="120" placeholder="请输入"></el-input>
          </el-form-item>
          <br />
          <el-form-item label="附件">
            <el-upload
              ref="uploadFile"
              drag
              multiple
              class="mterial_file"
              action="string"
              list-type="picture-card"
              :file-list="fileEcho"
              :http-request="httpRequest"
              accept=".jpg, .jpeg, .png, .JPG, .JPEG, .bmp"
              :limit="3"
              :on-exceed="handleExceed"
              :beforeUpload="beforeAvatarUpload"
              :on-remove="handleRemove"
              :on-change="fileChange"
              :auto-upload="false"
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text" style="top: 33px">
                将文件拖到此处，或
                <em>点击上传</em>
              </div>
              <div slot="tip" class="el-upload__tip">可上传三张以内,小于10M的图片</div>
            </el-upload>
            <!-- <el-dialog :visible.sync="dialogVisible" style="z-index: 99999">
              <img width="50%" :src="dialogImageUrl" alt />
            </el-dialog> -->
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div slot="footer">
      <!-- <el-button type="primary" plain @click="reset">关闭</el-button> -->
      <el-button type="primary" @click="submitCheck()">保存</el-button>
    </div>
  </PageContainer>
</template>

<script>
import axios from 'axios'
import { transData } from '@/util'
export default {
  name: 'dangerReporting',
  data() {
    return {
      pageLoading: false,
      rules: {
        questionDetailCode: [{ required: true, message: '请选择隐患分类', trigger: 'change' }],
        riskCode: [{ required: true, message: '请选择隐患等级', trigger: 'change' }],
        questionContent: [{ required: true, message: '请输入描述', trigger: 'blur' }]
      },
      reportForm: {
        checkType: '', // 检查类型
        questionAddressCode: '', // 隐患区域编码
        questionAddressCodes: '',
        questionAddress: '', // 隐患区域名称
        questionDetailCode: '', // 隐患分类编码
        questionDetailCodes: '',
        questionDetailType: '', // 隐患分类名称
        riskCode: '', // 隐患等级编码
        riskName: '', // 隐患等级名称
        dutyDeptCode: '', // 责任部门编码
        dutyDeptName: '', // 责任部门名称
        rectificationPlanTime: '', // 完成时间
        questionContent: '', // 描述
        questionAttachmentUrl: [], // 附件
        questionBelongs: '', // 问题归属(0:上级单位；1:医院)
        hospitalName: ''
      },
      fileEcho: [],
      dialogVisible: false,
      dialogImageUrl: '',
      gridList: [],
      checkTypeList: [],
      hiddenClassifyList: [],
      hiddenLevelList: [],
      responsibleDepartmentList: [],
      formData: '',
      pickerOptions: {
        disabledDate(date) {
          let startTime = new Date(new Date(new Date().toLocaleDateString()).getTime())
          return date.getTime() < startTime
        }
      }
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.$api.ipsmGetDictList({ dictType: 'check_type' }).then((res) => {
        if (res.code == 200) {
          this.checkTypeList = res.data
        } else {
          this.$message.error(res.message)
        }
      })
      this.$api.ipsmGetHiddenClassifyList().then((res) => {
        if (res.code == 200) {
          this.hiddenClassifyList = res.data
        } else {
          this.$message.error(res.message)
        }
      })
      this.$api.ipsmGetDictList({ dictType: 'hidden_trouble_grade_type' }).then((res) => {
        if (res.code == 200) {
          this.hiddenLevelList = res.data
        } else {
          this.$message.error(res.message)
        }
      })
      this.$api.ipsmGetResponsibleDepartment().then((res) => {
        if (res.code == 200) {
          this.responsibleDepartmentList = res.data
        } else {
          this.$message.error(res.message)
        }
      })
      this.$api.ipsmGetHiddenGridList().then((res) => {
        if (res.code == 200) {
          let treeList = transData(res.data, 'id', 'parentId', 'children')
          this.gridList = treeList
        } else {
          this.$message.error(res.message)
        }
      })
      this.$api.ipsmGetSelectTimeOut({ timeOutFlag: '2' }).then((res) => {
        if (res.code == 200) {
          this.reportForm.rectificationPlanTime = res.data.rectificationPlanTime
        } else {
          this.$message.error(res.message)
        }
      })
    },
    submitCheck() {
      this.$refs.reportForm.validate((valid) => {
        if (valid) {
          if (this.fileEcho && this.fileEcho.length > 0) {
            this.httpRequest()
          } else {
            this.submit()
          }
        }
      })
    },
    submit() {
      let loginData = JSON.parse(sessionStorage.getItem('LOGINDATA'))
      this.reportForm.questionDetailCode = this.reportForm.questionDetailCodes.toString()
      this.reportForm.questionAddressCode = this.reportForm.questionAddressCodes.toString()
      this.reportForm.questionAddress = this.reportForm.questionAddress.toString()
      this.reportForm.questionAttachmentUrl = this.reportForm.questionAttachmentUrl.toString()
      this.hiddenLevelList.forEach((item) => {
        if (item.dictCode == this.reportForm.riskCode) {
          this.reportForm.riskName = item.dictName
        }
      })
      if (this.reportForm.dutyDeptCode) {
        this.responsibleDepartmentList.forEach((item) => {
          if (item.id == this.reportForm.dutyDeptCode) {
            this.reportForm.dutyDeptName = item.teamName
          }
        })
      }
      this.reportForm.questionBelongs = '1'
      this.reportForm.hospitalName = loginData.hospitalName
      this.reportForm.hospitalCode = loginData.hospitalCode
      this.pageLoading = true
      this.$api.ipsmReportSaved(this.reportForm).then((res) => {
        if (res.code == 200) {
          this.$message.success(res.message)
          this.reset()
        } else {
          this.$message.error(res.message)
        }
        this.pageLoading = false
      })
    },
    reset() {
      this.reportForm = {
        checkType: '', // 检查类型
        questionAddressCode: '', // 隐患区域编码
        questionAddressCodes: '',
        questionAddress: '', // 隐患区域名称
        questionDetailCode: '', // 隐患分类编码
        questionDetailCodes: '',
        questionDetailType: '', // 隐患分类名称
        riskCode: '', // 隐患等级编码
        riskName: '', // 隐患等级名称
        dutyDeptCode: '', // 责任部门编码
        dutyDeptName: '', // 责任部门名称
        rectificationPlanTime: '', // 完成时间
        questionContent: '', // 描述
        questionAttachmentUrl: [], // 附件
        questionBelongs: '' // 问题归属(0:上级单位；1:医院)
      }
      this.fileEcho = []
      this.formData = ''
      this.$refs.reportForm.resetFields()
    },
    fileChange(file, fileList) {
      let imgSize = Number(file.size / 1024 / 1024)
      if (imgSize > 10) {
        // 删除文件
        fileList.splice(fileList.length - 1, 1)
        return this.$message.error('上传图片大小不能超过 10MB!')
      }
      this.fileEcho = fileList
    },
    httpRequest() {
      this.formData = new FormData()
      this.fileEcho.forEach((item) => {
        this.formData.append('file', item.raw)
      })
      // this.reportForm.questionAttachmentUrl.push(item.file)
      let loginData = JSON.parse(sessionStorage.getItem('LOGINDATA'))
      this.formData.append('hospitalCode', loginData.hospitalCode)
      axios({
        method: 'post',
        url: __PATH.VUE_AQ_URL + 'file/upload',
        data: this.formData,
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      })
        .then((res) => {
          this.reportForm.questionAttachmentUrl = res.data.data.fileKey
          this.submit()
        })
        .catch(() => {
          this.$message.error(res.data.message)
        })
    },
    handleExceed() {
      this.$message.error('最多上传三张图片')
    },
    // handlePictureCardPreview(file) {
    //   this.dialogImageUrl = file.url
    //   this.dialogVisible = true
    // },
    beforeAvatarUpload(file) {
      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        this.$message.error('上传图片大小不能超过 10MB!')
        return false
      }
      if (file.name.indexOf(',') != -1) {
        this.$message.error('非法的文件名')
        return false
      }
    },
    handleRemove(file, fileList) {
      this.fileEcho = fileList
      //   this.formInline.attachmentUrl = [];
      //   fileList.forEach((item) => {
      //     this.formInline.attachmentUrl.push(item.raw);
      //   });
    },
    hangdleChange(val) {
      let list = this.$refs.myCascader.getCheckedNodes()
      let name = []
      list.forEach((e) => {
        name.push(e.pathLabels.join('>'))
      })
      this.reportForm.questionAddressCodes = val.join('>')
      this.reportForm.questionAddress = name
    },
    classifyChange(val) {
      let list = this.$refs.classifyCascader.getCheckedNodes()
      let name = []
      list.forEach((e) => {
        name.push(e.pathLabels.join(','))
      })
      this.reportForm.questionDetailType = name.toString()
      this.reportForm.questionDetailCodes = val.join(',')
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-textarea .el-input__count {
  line-height: normal;
}

.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
  padding: 10px;
  overflow-y: auto;
}

::v-deep .el-textarea__inner {
  min-height: 120px !important;
}

.content_box {
  height: calc(100% - 30px);
  margin-top: 10px;
  padding: 20px 25px 25px;
  background: #fff;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.form-inline {
  .el-input,
  .el-select,
  .el-cascader {
    width: 300px;
  }
}

.el-textarea {
  width: 400px;
}

::v-deep .mterial_file > .el-upload-list {
  position: absolute;
  top: 0;
  width: 500px;
  margin-left: 430px;
  max-height: 160px;
  // overflow: auto;
}

::v-deep .mterial_file > .el-upload--picture-card {
  border: none;
  width: 300px;
}

::v-deep .mterial_file .el-upload .el-upload-dragger {
  width: 300px;
  height: 148px;
}

::v-deep .mterial_file .el-upload__text {
  position: absolute;
  left: 57px;
  color: #606266;
  font-size: 14px;
  text-align: center;
}

::v-deep .el-input__inner {
  height: 40px;
  line-height: 40px;
}
</style>
