<!-- 抄表示数 -->
<template>
  <PageContainer>
    <div slot="header" class="control-btn-header">
      <div class="search-from">
        <el-select v-model="queryForm.recordType" placeholder="抄表类型" clearable>
          <el-option label="自动" :value="0"> </el-option>
          <el-option label="手动" :value="1"> </el-option>
        </el-select>
        <el-select v-model="queryForm.deviceType" placeholder="设备类型" clearable>
          <el-option v-for="item in deviceTypeList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
        </el-select>
        <el-select v-model="queryForm.projectCode" placeholder="监测系统" clearable>
          <el-option v-for="item in sysList" :key="item.projectCode" :label="item.projectName" :value="item.projectCode"> </el-option>
        </el-select>
        <el-select v-model="queryForm.cycle" placeholder="抄表周期" @change="() => (dataRange = [])">
          <el-option label="时" :value="0"> </el-option>
          <el-option label="日" :value="1"> </el-option>
          <el-option label="月" :value="2"> </el-option>
          <el-option label="年" :value="3"> </el-option>
        </el-select>
        <el-date-picker
          v-model="dataRange"
          :type="datePickerType"
          range-separator="至"
          unlink-panels
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          :picker-options="pickerOptions"
          :default-time="defaultTime"
          style="width: 400px"
        ></el-date-picker>
        <el-select v-model="queryForm.departmentCode" placeholder="监测科室" clearable filterable>
          <el-option v-for="item in deptList" :key="item.id" :label="item.deptName" :value="item.id"></el-option>
        </el-select>
        <el-select ref="treeSelect" v-model="queryForm.serviceReginonCode" placeholder="服务区域" clearable @clear="handleClear">
          <el-option hidden :value="queryForm.serviceReginonCode" :label="areaName"> </el-option>
          <el-tree
            :data="serverSpaces"
            :props="serverDefaultProps"
            :load="serverLoadNode"
            lazy
            :expand-on-click-node="false"
            :check-on-click-node="true"
            @node-click="handleNodeClick"
          >
          </el-tree>
        </el-select>
        <el-input v-model="queryForm.codeOrName" placeholder="设备名称/设备编码" clearable style="width: 200px"></el-input>
        <div style="display: inline-block">
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="searchForm">查询</el-button>
          <el-button type="primary" icon="el-icon-download" :loading="exportLoading" @click="exportEvent">导出</el-button>
        </div>
      </div>
    </div>
    <div slot="content" class="table-content">
      <TablePage
        ref="table"
        v-loading="tableLoading"
        :showPage="true"
        :tableColumn="tableColumn"
        :data="tableData"
        height="calc(100% - 40px)"
        :pageData="pageData"
        @pagination="paginationChange"
      />
    </div>
  </PageContainer>
</template>
<script lang="jsx">
import { transData, ListTree } from '@/util'
import moment from 'moment'
export default {
  name: 'meterReadingNum',
  data() {
    return {
      exportLoading: false,
      queryForm: {
        codeOrName: '',
        deviceType: '',
        recordType: '',
        projectCode: '',
        serviceReginonCode: '',
        departmentCode: '',
        cycle: 0
      },
      dataRange: [moment().format('YYYY-MM-DD') + ' 00:00:00', moment().format('YYYY-MM-DD HH:mm:ss')],
      pickerOptions: {
        disabledDate: (time) => {
          const date = moment(time)
          const now = moment()
          switch (this.queryForm.cycle) {
            case 0: // 时：最大范围一个月
              return date.isBefore(now.clone().subtract(1, 'month')) || date.isAfter(now)
            case 1: // 日：最大范围三个月
              return date.isBefore(now.clone().subtract(3, 'months').startOf('day')) || date.isAfter(now.clone().endOf('day'))
            case 2: // 月：最大范围一年
              return date.format('YYYYMM') < now.clone().subtract(1, 'year').format('YYYYMM') || date.format('YYYYMM') > now.format('YYYYMM')
            case 3: // 年：最大范围三年
              return date.isBefore(now.clone().subtract(3, 'years').startOf('month')) || date.isAfter(now.clone().endOf('month'))
            default:
              return false
          }
        }
      },
      serverDefaultProps: {
        label: 'ssmName',
        isLeaf: 'leaf',
        children: 'children'
      },
      areaName: '', // 选中 下拉树的name
      spaces: [], // 空间位置
      serverSpaces: [], // 空间位置
      deviceTypeList: [], // 设备类型
      sysList: [], // 监测系统
      deptList: [], // 科室列表
      tableData: [],
      pageData: {
        page: 1,
        pageSize: 15,
        total: 0
      },
      tableLoading: false,
      tableColumn: [
        {
          prop: 'serialNumber',
          label: '序号',
          formatter: (scope) => {
            return (this.pageData.page - 1) * this.pageData.pageSize + scope.$index + 1
          }
        },
        {
          prop: 'deviceCode',
          label: '设备编码'
        },
        {
          prop: 'deviceName',
          label: '设备名称'
        },
        {
          prop: 'deviceTypeName',
          label: '设备类型'
        },
        {
          prop: 'recordTypeName',
          label: '抄表类型'
        },
        {
          prop: 'projectName',
          label: '监测系统'
        },
        {
          prop: 'serviceReginonName',
          label: '服务区域'
        },
        {
          prop: 'departmentName',
          label: '监测科室'
        },
        {
          prop: 'recordTime',
          label: '抄表时间'
        },
        {
          prop: 'numericalValue',
          label: '抄表读数'
        }
      ]
    }
  },
  computed: {
    datePickerType() {
      let newObj = {
        0: 'datetimerange',
        1: 'daterange',
        2: 'monthrange',
        3: 'monthrange'
      }
      return newObj[this.queryForm.cycle]
    },
    defaultTime() {
      return this.queryForm.cycle == 0 ? ['00:00:00', moment().format('HH:mm:ss')] : ['00:00:00', '23:59:59']
    }
  },
  created() {
    this.getSystemList()
    this.getDeptList()
    this.getTreelist()
    this.getDeviceTypeList()
  },
  methods: {
    exportEvent() {
      let param = {
        ...this.queryForm,
        startDate: this.dataRange[0],
        endDate: this.dataRange[1],
        pageSize: this.pageData.pageSize,
        page: this.pageData.page
      }
      this.exportLoading = true
      this.$api
        .meterExportRecord(param)
        .then((res) => {
          let url = window.URL.createObjectURL(new Blob([res]), { type: 'application/vnd.ms-excel;charset=UTF-8' })
          let link = document.createElement('a')
          link.style.display = 'none'
          link.href = url
          link.setAttribute('download', '抄表示数.xlsx')
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link) // 下载完成移除元素
          window.URL.revokeObjectURL(url) // 释放掉blob对象
        })
        .finally(() => {
          this.exportLoading = false
        })
    },
    getRecordDataPage() {
      let param = {
        ...this.queryForm,
        startDate: this.dataRange[0],
        endDate: this.dataRange[1],
        pageSize: this.pageData.pageSize,
        page: this.pageData.page
      }
      this.tableLoading = true
      this.$api
        .GetRecordDataPage(param)
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.records
            this.pageData.total = res.data.total
          }
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    // 获取设备类型
    getDeviceTypeList() {
      this.$api.GetDeviceTypeList().then((res) => {
        if (res.code == '200') {
          this.deviceTypeList = res.data
        }
      })
    },
    // 获取科室
    getDeptList() {
      this.$api.getSelectedDept().then((res) => {
        if (res.code == '200') {
          this.deptList = res.data
        }
      })
    },
    // 获取监测系统
    getSystemList() {
      this.$api.GetMeterProjectList().then((res) => {
        if (res.code == 200) {
          this.sysList = res.data
        }
      })
    },
    // 获取服务空间树形结构
    getTreelist() {
      this.$api.getStructureTree({}).then((res) => {
        if (res.code == 200) {
          this.spaces = res.data
          // 增加 懒加载节点
          res.data.map((e) => {
            e.leaf = false
          })
          // 转换为树形结构数据
          this.serverSpaces = transData(res.data, 'id', 'pid', 'children')
        }
      })
    },
    spaceTreeChange(value, resolve) {
      const child = ListTree(this.spaces, value.pid)
      child.push(value.id)
      let treeId = child.toString()
      let data = {
        current: 1,
        functionDictId: '',
        simCode: treeId,
        size: 999
      }
      this.$api.getSpaceInfoList(data).then((res) => {
        if (res.code == 200) {
          if (typeof resolve === 'function') {
            let treeNodeData = JSON.parse(JSON.stringify(res.data.records))
            treeNodeData.map((e) => {
              e.leaf = true
            })
            resolve(treeNodeData)
          }
        }
      })
    },
    // 空间数据清除
    handleClear() {
      this.queryForm.serviceReginonCode = ''
      this.areaName = ''
    },
    // 选择下拉树 数据
    handleNodeClick(data) {
      this.queryForm.serviceReginonCode = data.id
      this.areaName = data.ssmName
      this.$refs.treeSelect.blur()
    },
    // 下拉树懒加载
    serverLoadNode(node, resolve) {
      if (node.level == 0) {
        return resolve(node.data)
      } else if (node.level > 0 && node.level < 4) {
        return resolve(node.data.children)
      } else {
        return this.spaceTreeChange(node.data, resolve)
      }
    },
    // 查询
    searchForm() {
      if (![0, 1].includes(this.queryForm.recordType)) {
        this.$message({
          type: 'warning',
          message: '请选择抄表类型'
        })
        return
      }
      if (!this.queryForm.deviceType) {
        this.$message({
          type: 'warning',
          message: '请选择设备类型'
        })
        return
      }
      if (!this.dataRange || !this.dataRange.length) {
        this.$message({
          type: 'warning',
          message: '请选择抄表日期'
        })
        return
      }
      this.pageData.page = 1
      this.getRecordDataPage()
    },
    // 重置查询
    resetForm() {
      this.tableData = []
      this.areaName = ''
      this.dataRange = [moment().format('YYYY-MM-DD') + ' 00:00:00', moment().format('YYYY-MM-DD HH:mm:ss')]
      Object.assign(this.$data.queryForm, this.$options.data().queryForm)
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getRecordDataPage()
    }
  }
}
</script>
<style lang="scss" scoped>
.control-btn-header {
  padding: 0px 10px 10px 10px !important;
  ::v-deep .search-from {
    & > div {
      margin-top: 10px;
      margin-right: 10px;
    }
    .el-date-editor {
      .el-range__icon,
      .el-range__close-icon {
        height: 25px;
        line-height: 25px !important;
      }
    }
  }
}
.table-content {
  margin-top: 15px;
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 15px);
  padding: 10px;
}
</style>
<style lang="scss">
.operationBtn-span {
  margin-right: 10px;
}
</style>
