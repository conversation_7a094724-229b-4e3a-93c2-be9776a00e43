<template>
  <PageContainer :footer="true">
    <div slot="content" v-loading="pageLoading" class="addMessage-content">
      <div class="content-left">
        <el-form ref="formInline" :model="formInline" :rules="rules" :disabled="$route.query.type == 'detail'">
          <el-row :gutter="24">
            <el-col :md="14">
              <el-form-item label="消息分类" prop="msgCatId" label-width="100px">
                <el-select v-model="formInline.msgCatId" placeholder="请选择消息分类" style="width: 100%">
                  <el-option v-for="item in msgTypeList" :key="item.id" :label="item.label" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :md="14">
              <el-form-item label="消息标题" prop="msgTitle" label-width="100px">
                <el-input v-model="formInline.msgTitle" maxlength="20" show-word-limit placeholder="请输入消息标题" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :md="14">
              <el-form-item label="发布范围" prop="msgScp" label-width="100px">
                <el-input :value="scopeList[formInline.msgScp]?.name ?? ''" placeholder="请选择发布范围" readonly suffix-icon="el-icon-arrow-down" @focus="selectScope" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :md="24">
              <el-form-item label="发布时间" prop="publishType" label-width="100px">
                <el-radio-group v-model="formInline.publishType" @change="() => (formInline.publishTime = '')">
                  <el-radio :label="0">即刻发送</el-radio>
                  <el-radio :label="1">定时发送</el-radio>
                </el-radio-group>
                <el-date-picker
                  v-show="formInline.publishType == 1"
                  v-model="formInline.publishTime"
                  popper-class="elDatePicker"
                  :picker-options="pickerOptions"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  style="margin-left: 8px"
                  type="datetime"
                  placeholder="选择日期时间"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :md="24">
              <el-form-item label="消息正文" prop="msgText" label-position="top" label-width="100px" class="msgText">
                <Editor ref="myTextEditor" v-model="formInline.msgText" class="myTextEditor"></Editor>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="content-right">
        <p class="right-title">预览</p>
        <div class="right-model">
          <p class="model-type">{{ formInline.msgCatId && msgTypeList.length ? msgTypeList.find((item) => item.id == formInline.msgCatId)?.label : '' }}</p>
          <div class="model-main">
            <p v-show="!!formInline.msgTitle" class="model-title">
              <svg-icon name="notice_icon" class="noticeIcon" />
              <span>{{ formInline.msgTitle }}</span>
            </p>
            <p v-show="[0, 1].includes(formInline.publishType)" class="model-subtitle">发布时间：{{ formInline.publishTime ? formInline.publishTime : nowDate }}</p>
            <p v-show="!!formInline.deptId.length" class="model-subtitle">发布部门：{{ selectDeptName }}</p>
            <div class="model-msgText" v-html="formInline.msgText"></div>
          </div>
        </div>
      </div>
      <select-scope-dialog
        v-if="isSelectScope"
        :visible.sync="isSelectScope"
        :detailed="{ type: formInline.msgScp, userId: formInline.userId, deptId: formInline.deptId }"
        @selectFinish="selectFinish"
      />
    </div>
    <div slot="footer">
      <el-button
        type="primary"
        plain
        @click="
          () => {
            $router.go(-1)
          }
        "
      >取消</el-button
      >
      <el-button type="primary" :disabled="$route.query.type == 'detail'" @click="submitForm('formInline', 1)">存草稿</el-button>
      <el-button type="primary" :disabled="$route.query.type == 'detail'" @click="submitForm('formInline', 3)">发布</el-button>
    </div>
  </PageContainer>
</template>
<script>
import moment from 'moment'
moment.locale('zh-cn')
import selectScopeDialog from './components/selectScopeDialog.vue'
export default {
  name: 'addMessage',
  components: {
    selectScopeDialog
  },
  // 路由前置守卫动态修改meta中的title
  beforeRouteEnter(to, from, next) {
    if (to.meta.title) {
      const typeList = {
        add: '新增消息',
        edit: '编辑消息',
        detail: '消息详情'
      }
      to.meta.title = typeList[to.query.type] ?? '应用详情'
    }
    next()
  },
  async beforeRouteLeave(to, from, next) {
    if (!['MessageRelease'].includes(to.name)) {
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      pageLoading: false,
      isSelectScope: false, // 发布范围选择
      nowDate: moment().format('YYYY-MM-DD') + ' xx:xx:xx',
      selectDeptName: '',
      formInline: {
        msgCatId: '', // 消息类别
        msgTitle: '', // 消息标题
        msgScp: '', // 范围类型(0:全部 1:部门 2:角色 3:用户)
        publishType: '', // 发布时间类型
        publishTime: '', // 发布时间
        msgText: '', // 消息正文
        userId: [], // 用户id集合
        deptId: [] // 部门id集合
      },
      scopeList: [
        { name: '全部', value: 0 },
        { name: '部门', value: 1 },
        { name: '角色', value: 2 },
        { name: '用户', value: 3 }
      ], // 部门列表
      msgTypeList: [], // 消息类型列表
      pickerOptions: {
        disabledDate(date) {
          return date.getTime() < new Date().getTime() - 24 * 60 * 60 * 1000
        },
        selectableRange: '00:00:00 - 23:59:59'
      },
      rules: {}
    }
  },
  watch: {
    'formInline.publishTime'() {
      console.log(this.formInline.publishTime)
      this.selectable()
    }
  },
  activated() {
    this.init()
  },
  created() {
    if (!this.$store.state.keepAlive.list.includes('departmentManage')) {
      this.init()
    }
  },
  methods: {
    init() {
      let validateStaffId = (rule, value, callback) => {
        if (![0, 1].includes(this.formInline.publishType)) {
          callback(new Error('请选择发布时间'))
        } else {
          if (this.formInline.publishType == 1 && !this.formInline.publishTime) {
            callback(new Error('请选择发布时间'))
          } else {
            callback()
          }
        }
      }
      this.$refs.myTextEditor.content = ''
      Object.assign(this.$data, this.$options.data())
      this.$set(this.$data, 'rules', {
        msgCatId: [{ required: true, message: '请选择消息分类', trigger: ['blur', 'change'] }],
        msgTitle: [{ required: true, message: '请输入消息标题', trigger: ['blur', 'change'] }],
        msgScp: [{ required: true, message: '请选择发布范围', trigger: ['blur', 'change'] }],
        publishType: [{ required: true, validator: validateStaffId, trigger: ['blur', 'change'] }],
        msgText: [{ required: true, message: '请输入消息正文', trigger: ['blur', 'change'] }]
      })
      this.$nextTick(() => {
        this.$refs.formInline.resetFields()
      })
      if (this.$route.query.type != 'add') {
        this.getMessageInfo()
      }
      this.getMsgType()
    },
    // 获取部门数据
    getDeptTreeList(ids) {
      this.$api.getSelectedDept({}).then((res) => {
        if (res.code == 200) {
          // res.data.map(item => ids.includes(item.id))
          this.selectDeptName = res.data
            .filter((item) => ids.includes(item.id))
            .map((v) => v.deptName)
            .join(',')
        }
      })
    },
    selectable() {
      const date = moment(this.formInline.publishTime).startOf('day').format('x')
      const nowDate = moment().startOf('day').format('x')
      // 如果选择的是今天 则需要禁用已经过去的时间节点
      if (date <= nowDate) {
        // 默认选择的最新时间 是当前时间的两分钟后 （留出2分钟的富裕时间）
        this.pickerOptions.selectableRange = `${moment().add(2, 'minutes').format('HH:mm:ss')} - 23:59:59`
      } else {
        // 如果是以后的日期，则不需要禁用时间节点
        this.pickerOptions.selectableRange = '00:00:00 - 23:59:59'
      }
    },
    // 选择发布范围完成
    selectFinish(data) {
      this.formInline.msgScp = data.msgScp
      this.formInline.userId = data.userId
      this.formInline.deptId = data.deptId
      this.getDeptTreeList(data.deptId)
    },
    // 发布范围选择
    selectScope() {
      this.isSelectScope = true
    },
    // 提交
    submitForm(formName, msgStatus) {
      let param = {
        msgStatus,
        // msgSysId: 0,
        // msgCatId: 20,
        msgType: 0,
        msgSysTypeName: this.formInline.msgCatId ? this.msgTypeList.find((item) => item.id == this.formInline.msgCatId).label : '',
        createId: this.$store.state.user.userInfo.userId,
        createName: this.$store.state.user.userInfo.user.staffName,
        publishWay: 4,
        ...this.formInline
      }
      param.userId = param.userId.join(',')
      param.deptId = param.deptId.join(',')
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.$route.query.type != 'add') {
            this.$api.UpdateMessage(param, { 'operation-type': 2, 'operation-id': param.msgId, 'operation-name': param.msgTitle }).then((res) => {
              if (res.code == 200) {
                this.$message({ message: '编辑成功', type: 'success' })
                this.$router.go(-1)
              } else {
                this.$message({ message: res.msg, type: 'error' })
              }
            })
          } else {
            this.$api.InsertMessage(param, { 'operation-type': 1 }).then((res) => {
              if (res.code == 200) {
                this.$message({ message: '提交成功', type: 'success' })
                this.$router.go(-1)
              } else {
                this.$message({ message: res.msg, type: 'error' })
              }
            })
          }
        }
      })
    },
    // 获取消息类型
    getMsgType() {
      this.$api.GetMsgType({}).then((res) => {
        if (res.code == 200) {
          this.msgTypeList = res.data
        }
      })
    },
    // 获取消息详情
    getMessageInfo() {
      this.pageLoading = true
      this.$api
        .GetMessageInfo({ msgId: this.$route.query.id })
        .then((res) => {
          this.pageLoading = false
          debugger
          if (res.code == 200) {
            let { msgId, msgText, publishTime, publishType, msgScp, msgTitle, msgCatId, deptId, userId } = res.data
            this.$refs.myTextEditor.content = msgText
            this.formInline = {
              msgId,
              msgCatId,
              msgTitle,
              msgScp,
              publishType,
              publishTime,
              msgText,
              userId: userId ? userId.split(',') : '',
              deptId: deptId ? deptId.split(',') : ''
            }
            this.getDeptTreeList(this.formInline.deptId)
          }
        })
        .catch(() => {
          this.pageLoading = false
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.addMessage-content {
  background: #fff;
  border-radius: 4px;
  height: 100%;
  padding: 10px;
  overflow-y: auto;
  display: flex;
  .content-left {
    width: 50%;
    padding: 20px 10px 10px;
    ::v-deep .msgText {
      display: flex;
      flex-direction: column;
      .el-form-item__content {
        margin-left: 22px !important;
      }
    }
    .myTextEditor {
      height: auto;
    }
  }
  .content-right {
    width: 50%;
    position: relative;
    p {
      margin: 0;
    }
    .right-title {
      text-align: center;
      font-size: 18px;
      font-weight: 600;
      color: #121f3e;
      padding: 10px 0;
    }
    .right-model {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      width: 360px;
      height: 730px;
      padding: 57px 20px 37px 30px;
      background: url('../../../../assets/images/sysManagement/phoneModel.png') no-repeat center / 100% 100%;
      .model-type {
        font-size: 16px;
        font-weight: 600;
        padding: 10px 0 20px;
        position: absolute;
        width: calc(100% - 60px);
        background: #fff;
        text-align: center;
        // left: 50%;
        // transform: translateX(-50%);
      }
      .model-main {
        height: 100%;
        padding-top: 60px;
        overflow-y: auto;
        overflow-x: hidden;
      }
      .model-title {
        display: flex;
        align-items: center;
        span {
          font-size: 18px;
          font-weight: 600;
        }
        .noticeIcon {
          margin-right: 8px;
          font-size: 26px;
        }
      }
      .model-subtitle {
        margin-top: 4px;
        font-size: 14px;
        font-weight: 400;
        color: #86909c;
        line-height: 20px;
      }
    }
  }
}
</style>
<style lang="scss">
.elDatePicker {
  .el-picker-panel__footer {
    .el-button--text {
      display: none;
    }
  }
}
.addMessage-content {
  .model-msgText {
    margin-top: 10px;
    p {
      margin: 0 !important;
    }
  }
}
</style>
