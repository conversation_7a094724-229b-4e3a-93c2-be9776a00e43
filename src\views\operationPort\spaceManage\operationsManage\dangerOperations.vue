<template>
  <div class="">
    <template v-for="(item, index) in dlayoutItems">
      <dash-item
        v-if="item.id == 'dangerStatus'"
        v-bind.sync="dlayoutItems[index]"
        :key="item.id"
        @moveEnd="moveEnd"
        @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)"
      >
        <ContentCard
          title="隐患状态分析"
          :scrollbarHover="true"
          :cstyle="{ height: '100%' }"
          class="drag_class"
          :hasMoreOper="['edit']"
          @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')"
        >
          <div slot="content" style="height: 100%;">
            <div id="dangerStatus" class="full-style"></div>
          </div>
        </ContentCard>
      </dash-item>
      <dash-item
        v-else-if="item.id == 'dangerType'"
        v-bind.sync="dlayoutItems[index]"
        :key="item.id"
        @moveEnd="moveEnd"
        @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)"
      >
        <ContentCard
          title="隐患类型分析"
          :scrollbarHover="true"
          :cstyle="{ height: '100%' }"
          class="drag_class"
          :hasMoreOper="['edit']"
          @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')"
        >
          <div slot="content" style="height: 100%;">
            <div id="dangerType" class="full-style"></div>
          </div>
        </ContentCard>
      </dash-item>
      <dash-item
        v-else-if="item.id == 'dangerTrend'"
        v-bind.sync="dlayoutItems[index]"
        :key="item.id"
        @moveEnd="moveEnd"
        @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)"
      >
        <ContentCard
          title="近12个月隐患趋势"
          :scrollbarHover="true"
          :cstyle="{ height: '100%' }"
          class="drag_class"
          :hasMoreOper="['edit']"
          @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')"
        >
          <div slot="content" style="height: 100%;">
            <div id="dangerTrend" class="full-style"></div>
          </div>
        </ContentCard>
      </dash-item>
      <dash-item
        v-else-if="item.id == 'realTime'"
        v-bind.sync="dlayoutItems[index]"
        :key="item.id"
        @moveEnd="moveEnd"
        @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)"
      >
        <ContentCard
          title="隐患实时动态"
          :scrollbarHover="true"
          :cstyle="{ height: '100%' }"
          class="drag_class"
          :hasMoreOper="['edit']"
          @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')"
        >
          <div slot="content" style="height: 100%;">
            <!-- <div class="btns" style="right: 5%;">
              <span :class="{ 'active-btn': dynamicType == 'day' }" @click="changeDynamicType('day')">日</span>
              <span :class="{ 'active-btn': dynamicType == 'week' }" @click="changeDynamicType('week')">周</span>
              <span :class="{ 'active-btn': dynamicType == 'month' }" @click="changeDynamicType('month')">月</span>
              <span :class="{ 'active-btn': dynamicType == 'year' }" @click="changeDynamicType('year')">年</span>
            </div> -->
            <div class="full-style">
              <el-table v-loading="tableLoading" :data="tableData" :border="true" height="88%">
                <el-table-column prop="createTime" label="反馈时间" show-overflow-tooltip width="200"></el-table-column>
                <el-table-column prop="createPersonName" label="反馈人" show-overflow-tooltip width="180"></el-table-column>
                <el-table-column prop="questionCode" label="隐患编号" show-overflow-tooltip width="250">
                  <template slot-scope="scope">
                    <el-link type="primary" @click="hiddenDetail(scope.row)">{{ scope.row.questionCode }}</el-link>
                  </template>
                </el-table-column>
                <el-table-column prop="riskName" label="隐患等级" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span class="riskStatusIcon" :class="'riskStatus' + scope.row.riskCode"></span>
                    <span>{{ scope.row.riskName }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="questionDetailType" label="隐患分类" show-overflow-tooltip></el-table-column>
                <el-table-column prop="flowType" label="处理状态" show-overflow-tooltip width="250">
                  <template slot-scope="scope">
                    <span class="enable">
                      <span class="riskStatusIcon" :class="'flowStatus' + scope.row.flowCode"></span>
                      {{ scope.row.flowType }}
                    </span>
                  </template>
                </el-table-column>
              </el-table>
              <el-pagination
                :current-page="paginationData.currentPage"
                :page-sizes="[15, 20, 30, 40]"
                :page-size="paginationData.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="paginationData.total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              ></el-pagination>
            </div>
          </div>
        </ContentCard>
      </dash-item>
      <dash-item
        v-else-if="item.id == 'overview'"
        v-bind.sync="dlayoutItems[index]"
        :key="item.id"
        @moveEnd="moveEnd"
        @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)"
      >
        <ContentCard
          title="隐患总览"
          :scrollbarHover="true"
          :cstyle="{ height: '100%' }"
          class="drag_class"
          :hasMoreOper="['edit']"
          @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')"
        >
          <div slot="content" style="height: 100%;">
            <div class="full-style">
              <div class="contenWrap">
                <div class="content-left">
                  <div class="item-wrap">
                    <div class="items" style="background-color: #FAF9FC; color: #121F3E;">
                      <div @click="getHiddenList('', hiddeStateData.allCount)">
                        <div class="numText">{{ hiddeStateData.allCount }}</div>
                        <div class="itemText">隐患总数</div>
                      </div>
                    </div>
                    <div class="items" style="background-color: rgba(255,148,53,0.1); color: #FF9435;">
                      <div @click="getHiddenList('wzg', hiddeStateData.rectifyCount)">
                        <div class="numText">{{ hiddeStateData.rectifyCount }}</div>
                        <div class="itemText">未整改隐患</div>
                      </div>
                    </div>
                  </div>
                  <div class="item-wrap">
                    <div class="items" style="background-color: rgba(255,100,97,0.1); color: #FA403C;">
                      <div @click="getHiddenList('1', hiddeStateData.generalCount)">
                        <div class="numText">{{ hiddeStateData.generalCount }}</div>
                        <div class="itemText">一般隐患（本年）</div>
                      </div>
                    </div>
                    <div class="items" style="background-color: rgba(255,211,53,0.1); color: #FFBE00;">
                      <div @click="getHiddenList('2', hiddeStateData.majorCount)">
                        <div class="numText">{{ hiddeStateData.majorCount }}</div>
                        <div class="itemText">重大隐患（本年）</div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="content-right">
                  <div class="chartsWrap">
                    <div id="dangerRate" style="width: 100%; height: 45%;"></div>
                    <div>
                      <div style="text-align: center;color: #414653;">隐患整改率</div>
                      <div style="text-align: center;color: #414653;">(本年)</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ContentCard>
      </dash-item>
      <dash-item
        v-else-if="item.id == 'departmentDanger'"
        v-bind.sync="dlayoutItems[index]"
        :key="item.id"
        @moveEnd="moveEnd"
        @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)"
      >
        <ContentCard
          title="科室隐患分布"
          :scrollbarHover="true"
          :cstyle="{ height: '100%' }"
          class="drag_class"
          :hasMoreOper="['edit']"
          @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')"
        >
          <div slot="content" style="height: 100%;">
            <div class="btns">
              <span :class="{ 'active-btn': departmentDangerType == 'day' }" @click="changeDepartmentRateType('day')">日</span>
              <span :class="{ 'active-btn': departmentDangerType == 'week' }" @click="changeDepartmentRateType('week')">周</span>
              <span :class="{ 'active-btn': departmentDangerType == 'month' }" @click="changeDepartmentRateType('month')">月</span>
              <span :class="{ 'active-btn': departmentDangerType == 'year' }" @click="changeDepartmentRateType('year')">年</span>
            </div>
            <div class="full-style" style="position: relative;">
              <div id="departmentChart" class="full-style"></div>
            </div>
          </div>
        </ContentCard>
      </dash-item>
    </template>
  </div>
</template>
<script>
import danger1 from '@/assets/images/danger1.png'
import danger2 from '@/assets/images/danger2.png'
import danger3 from '@/assets/images/danger3.png'
import { DashItem } from 'vue-responsive-dash'
import * as echarts from 'echarts'
export default {
  name: 'dangerOperations',
  components: {
    DashItem
  },
  props: {
    dlayoutItems: {
      type: Array,
      default: () => []
    },
    componentData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      danger1,
      danger2,
      danger3,
      orderStatisticsName: [],
      seriesData: [],
      xAxisData: [],
      lineData: [],
      echartsDom: ['dangerStatus', 'dangerType', 'dangerTrend', 'dangerRate', 'departmentChart'],
      tableLoading: false,
      tableData: [],
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      hiddeStateData: {},
      departmentDangerType: 'year',
      dynamicType: 'year'
    }
  },
  watch: {
    '$store.state.settings.sidebarCollapse': {
      handler(val) {
        this.$nextTick(() => {
          let echartsDom = this.echartsDom
          setTimeout(() => {
            echartsDom.forEach((item) => {
              echarts.init(document.getElementById(item)).resize()
            })
          }, 250)
        })
      },
      deep: true
    }
  },
  mounted() {
    setTimeout(() => {
      this.getDangerStatusData()
      this.getDangerTypeData()
      this.getDangerTrendData()
      this.getHiddenDangerList()
      this.getOverviewData()
      this.getDepartmentDangerData()
    }, 300)
  },
  methods: {
    moveEnd(val) {
      console.log('@@@@@@@@@@@@@@@', this.dlayoutItems)
    },
    getDepartmentDangerData() {
      this.$api.selectDeptDangerDistribution({ dateType: this.departmentDangerType }).then((res) => {
        if (res.code == 200) {
          let data = res.data
          let xAxisData = []
          data.forEach((item) => {
            xAxisData.push(item.deptName)
          })
          this.initDepartmentDangerChart(data, xAxisData)
        }
      })
    },
    initDepartmentDangerChart(data, xAxisData) {
      // 如果没数据，给个默认值
      if (data.length == 0) {
        data = [0]
        xAxisData = ['暂无数据']
      }
      const seriesData = []
      data.forEach(i => {
        const item = {
          waitRectifySum: i.waitRectifySum, // 整改
          rectifySum: i.rectifySum, // 完结
          creditSum: i.creditSum // 挂账
        }
        seriesData.push(item)
      })
      const getchart = echarts.init(document.getElementById('departmentChart'))
      console.log('data', data)
      const option = {
        color: ['#3562DB', '#FF9435', '#00BC6D'],
        tooltip: {
          trigger: 'item',
          position: 'left'
        },
        legend: {
          itemHeight: 8,
          itemWidth: 8,
          icon: 'circle',
          top: '1%',
          textStyle: {
            color: '#666666'
          }
        },
        grid: {
          top: '12%',
          left: '4%',
          right: '5%',
          bottom: '5%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xAxisData,
          axisLabel: {
            rotate: data[0] == 0 ? 0 : 30,
            margin: 15,
            formatter: function (val) {
              var strs = val.split('') // 字符串数组
              var str = ''
              // strs循环 forEach 每五个字符增加换行符
              strs.forEach((item, index) => {
                if (index % 6 === 0 && index !== 0) {
                  str += '\n'
                }
                str += item
              })
              return str
            }
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        yAxis: [
          {
            type: 'value',
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            }
          }
        ],
        series: [
          {
            name: '待整改',
            type: 'bar',
            stack: 'Ad',
            barWidth: 15,
            emphasis: {
              focus: 'series'
            },
            data: seriesData.map(i => i.waitRectifySum),
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 1
            }
          },
          {
            name: '已完结',
            type: 'bar',
            stack: 'Ad',
            barWidth: 15,
            emphasis: {
              focus: 'series'
            },
            data: seriesData.map(i => i.rectifySum),
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 1
            }
          },
          {
            name: '已挂账',
            type: 'bar',
            stack: 'Ad',
            barWidth: 15,
            emphasis: {
              focus: 'series'
            },
            data: seriesData.map(i => i.rectifySum),
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 1
            }
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    changeDepartmentRateType(type) {
      this.departmentDangerType = type
      this.getDepartmentDangerData()
    },
    initDangerRateChart() {
      let myChart = echarts.init(document.getElementById('dangerRate'))
      myChart.setOption({
        series: [
          {
            type: 'gauge',
            radius: '100%',
            center: ['50%', '50%'],
            itemStyle: {
              color: '#3562db'
            },
            axisLine: {
              lineStyle: {
                width: 10,
                color: [
                  [0.3, '#67e0e3'],
                  [0.7, '#37a2da'],
                  [1, '#fd666d']
                ]
              }
            },
            axisTick: {
              distance: -2,
              length: 2,
              lineStyle: {
                color: '#fff',
                width: 1
              }
            },
            pointer: {
              width: 2
            },
            splitLine: {
              distance: -2,
              length: 8,
              lineStyle: {
                width: 2,
                color: '#fff'
              }
            },
            axisLabel: {
              show: false,
              distance: 25,
              color: '#999',
              fontSize: 12
            },
            anchor: {
              show: false
            },
            title: {
              show: false
            },
            detail: {
              valueAnimation: true,
              fontSize: 16,
              color: '#333333',
              formatter: function (params) {
                return params + '%'
              },
              offsetCenter: [0, '70%']
            },
            data: [
              {
                value: this.hiddeStateData.finishRate.slice(0, -1)
              }
            ]
          }
        ]
      })
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
    goHiddenList() {},
    getOverviewData() {
      this.$api.getHospitalQuestionStateAnalysis({}).then((res) => {
        if (res.code == 200) {
          this.hiddeStateData = res.data
          this.initDangerRateChart()
        }
      })
    },
    getHiddenDangerList() {
      let data = {
        pageNo: this.paginationData.currentPage,
        pageSize: this.paginationData.pageSize
      }
      this.$api.ipsmGetHiddenDangerList(data).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.list
          this.paginationData.total = parseInt(res.data.total)
        }
      })
    },
    getDangerTrendData() {
      this.$api.ipsmGetHiddenDangerTrend({}).then((res) => {
        if (res.code == 200) {
          this.lineData = res.data
          this.initDangerTrendChart()
        }
      })
    },
    initDangerTrendChart() {
      let myChart = echarts.init(document.getElementById('dangerTrend'))
      var option = {
        color: ['#3562DB', '#FA403C'],
        tooltip: {
          show: true,
          trigger: 'axis'
        },
        legend: {
          data: this.lineData.IData
        },
        grid: {
          left: '2%',
          right: '3%',
          bottom: '50',
          top: '15',
          containLabel: true
        },
        dataZoom: [
          {
            fillerColor: '#BBC3CE',
            backgroundColor: '#fff',
            height: 10,
            type: 'slider',
            bottom: 10,
            textStyle: {
              color: '#000'
            },
            start: 0,
            end: 85
          },
          {
            type: 'inside', // 支持内部鼠标滚动平移
            start: 0,
            end: 85,
            zoomOnMouseWheel: false, // 关闭滚轮缩放
            moveOnMouseWheel: true, // 开启滚轮平移
            moveOnMouseMove: true // 鼠标移动能触发数据窗口平移
          }
        ],
        xAxis: [
          {
            type: 'category',
            data: this.lineData.xAxis,
            axisLabel: {
              color: function (value, index) {
                return value == time ? '#21aced' : '#909399'
              },
              clickable: true
              // show: true,
              // rotate: 0,
              // // interval:5,
              // textStyle: {
              //   color: '#909399' // 更改坐标轴文字颜色
              // }
            },
            axisLine: {
              lineStyle: {
                color: '#D8DEE7' // 更改坐标轴颜色
              }
            },
            // 去除x轴上的刻度线
            axisTick: {
              show: false
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '个',
            axisLabel: {
              formatter: '{value}',
              show: true,
              textStyle: {
                color: '#909399' // 更改坐标轴文字颜色
              }
            },
            // 控制y轴线是否显示
            axisLine: {
              lineStyle: {
                color: '#D8DEE7' // 更改坐标轴颜色
              }
            },
            // 网格样式
            splitLine: {
              show: true,
              lineStyle: {
                //                color: ['rgba(255,255,255,.3)'],
                color: ['#f5f5f5'],
                width: 1,
                type: 'dashed'
              }
            },
            // 去除y轴上的刻度线
            axisTick: {
              show: false
            }
          }
        ],
        series: [
          {
            // symbol: 'none',
            name: this.lineData.array[0].name,
            data: this.lineData.array[0].data,
            type: 'line',
            areaStyle: {
              color: {
                x: 0,
                y: 1,
                x2: 0,
                y2: 0,
                colorStops: [{
                  offset: 0, color: 'rgba(63,99,211,0)'
                }, {
                  offset: 1, color: 'rgba(63,99,211,1)'
                }]
              }
            }
          },
          {
            // symbol: 'none',
            name: this.lineData.array[1].name,
            data: this.lineData.array[1].data,
            type: 'line',
            areaStyle: {
              color: {
                x: 0,
                y: 1,
                x2: 0,
                y2: 0,
                colorStops: [{
                  offset: 0, color: 'rgba(250,64,60,0)'
                }, {
                  offset: 1, color: 'rgba(250,64,60,0.21)'
                }]
              }
            }
          }
        ]
      }
      let time = ''
      // 点击x轴线
      myChart.getZr().on('click', (params) => {
        const pointInPixel = [params.offsetX, params.offsetY]
        if (myChart.containPixel('grid', pointInPixel)) {
          const xIndex = myChart.convertFromPixel({ seriesIndex: 0 }, [params.offsetX, params.offsetY])[0]
          time = option.xAxis[0].data[xIndex]
          myChart.resize()
          this.$router.push({
            name: 'dangerList',
            query: {
              currMonth: time
            }
          })
        }
      })
      // 点击x轴坐标
      // myChart.on('click', 'xAxis.category', function (params, node) {
      //   time = params.value
      //   myChart.resize()
      //   console.log(time)
      // })
      myChart.clear()
      myChart.setOption(option) // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
    getDangerTypeData() {
      let params = {
        placeIds: '',
        flowCode: '',
        questionCode: ''
      }
      this.$api.getHiddenDangerTypeAnalysis(params).then((res) => {
        if (res.code == '200') {
          this.seriesData = res.data.seriesData
          this.xAxisData = res.data.xAxisData
          this.getPowerMonthTypeNoEchart()
        }
      })
    },
    getPowerMonthTypeNoEchart() {
      const getchart = echarts.init(document.getElementById('dangerType'))
      let arr = []
      this.xAxisData.forEach((item) => {
        let obj = {
          name: item
        }
        arr.push(obj)
      })
      this.seriesData.forEach((item, i) => {
        arr.forEach((item2, n) => {
          if (i == n) {
            item2.value = item
          }
        })
      })
      const data = arr
      data.sort((a, b) => {
        return a.value - b.value
      })
      const nameList = Array.from(data, ({ name }) => name)
      const option = {
        tooltip: {
          trigger: 'item',
          position: 'left'
        },
        grid: {
          top: '4%',
          left: '4%',
          right: '5%',
          bottom: '5%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: nameList,
          axisLabel: {
            rotate: 30,
            margin: 15,
            formatter: function (val) {
              var strs = val.split('') // 字符串数组
              var str = ''
              // strs循环 forEach 每五个字符增加换行符
              strs.forEach((item, index) => {
                if (index % 6 === 0 && index !== 0) {
                  str += '\n'
                }
                str += item
              })
              return str
            }
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        yAxis: [
          {
            type: 'value',
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            }
          }
        ],
        series: [
          {
            type: 'bar',
            barWidth: 10,
            data: data,
            itemStyle: {
              color: '#3562db',
              borderRadius: [3, 3, 0, 0]
            }
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    echartsResize() {
      let echartsDom = this.echartsDom
      this.$nextTick(() => {
        setTimeout(() => {
          echartsDom.forEach((item) => {
            echarts.init(document.getElementById(item)).resize()
          })
        }, 100)
      })
    },
    getDangerStatusData() {
      let params = {
        placeIds: '',
        flowCode: '',
        questionCode: ''
      }
      this.$api.getStateStatistics(params).then((res) => {
        if (res.code == '200') {
          this.orderStatisticsName = res.data.array
          this.initDangerStatusCharts()
        }
      })
    },
    initDangerStatusCharts() {
      let arr = []
      this.orderStatisticsName.forEach((item) => {
        let obj = {
          name: item.name,
          value: item.value,
          rate: item.rate
        }
        if (item.name == '待整改') obj.color = '#3562DB'
        if (item.name == '已挂账') obj.color = '#08CB83'
        if (item.name == '已完结') obj.color = '#FF9435'
        if (item.name == '已整改') obj.color = '#0CA6ED'
        arr.push(obj)
      })
      const getchart = echarts.init(document.getElementById('dangerStatus'))
      const option = {
        color: arr.map(i => i.color),
        tooltip: {
          trigger: 'item'
        },
        legend: {
          show: false
        },
        series: [
          {
            type: 'pie',
            radius: '60%',
            center: ['48%', '50%'],
            label: {
              color: '#333333',
              formatter: function (params) {
                return params.data.name + ' ' + params.data.rate + ' ' + params.data.value + '件'
              }
            },
            data: arr
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    getHiddenList(val, num) {
      if (num == 0) {
        return this.$message.error('隐患数为0')
      }
      this.$router.push({
        name: 'dangerList',
        query: {
          dateType: this.hiddenGrade,
          riskCode: val != 'wzg' ? val : '',
          flowCode: val != 'wzg' ? '' : 'wzg'
        }
      })
    },
    hiddenDetail(row) {
      this.$router.push({
        name: 'hiddenManagementDetails3',
        query: { id: row.id }
      })
    },
    changeDynamicType(type) {
      this.dynamicType = type
    },
    handleSizeChange(val) {
      this.paginationData.currentPage = 1
      this.paginationData.pageSize = val
      this.getHiddenDangerList()
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.getHiddenDangerList()
    }
  }
}
</script>
<style lang="scss" scoped>
.full-style {
  width: 100%;
  height: 100%;
  .contenWrap {
    display: flex;
    height: 100%;
    .content-left {
      width: 66%;
      height: 100%;
      .item-wrap {
        display: flex;
        height: calc(50% - 5px);
        margin-bottom: 10px;
        .items {
          width: calc(50% - 5px);
          background-color: #ccc;
          display: flex;
          justify-content: center;
          align-items: center;
          .numText {
            text-align: center;
            font-size: 20px;
            font-weight: bold;
          }
          .itemText {
            color: #414653;
          }
        }
        .items:first-child {
          margin-right: 10px;
        }
      }
    }
    .content-right {
      width: 34%;
      height: 100%;
      .chartsWrap {
        margin-left: 10px;
        background-color: #FAF9FC;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
      }
    }
  }
}
.status {
  width: 100%;
  height: 95%;
  display: flex;
  flex-wrap: wrap;
}

.status > div {
  width: 48%;
  height: 120px;
  max-height: 130px;
  margin-right: 8px;
  margin-bottom: 8px;
  background-color: #faf9fc;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
  padding: 16px;
}

.status img {
  width: 20%;
}

.status > div > span:nth-child(2) {
  display: inline-block;
  margin: 8px 0;
}
.status > div > span:nth-child(3) {
  font-size: 14px;
}

.jump {
  cursor: pointer;
}

.btns {
  position: absolute;
  width: 180px;
  height: 24px;
  right: 10%;
  top: 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 99999;
}
.btns > span {
  width: 25%;
  margin-right: 8px;
  background-color: #FAF9FC;
  font-size: 12px;
  border-radius: 4px;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #7f848c;
  cursor: pointer;
}
.active-btn {
  background-color: #E6EFFC !important;
  color: #3562DB !important;
}
.box-card {
  padding: 24px;
}
::v-deep .svg-icon {
  display: none;
}
.riskStatusIcon {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
}
.riskStatus1 {
  background-color: #FFBE00;
}
.riskStatus2 {
  background-color: #FA403C;
}
.flowStatus0 {
  background-color: #F53F3F;
}
.flowStatus1 {
  background-color: #3562DB;
}
.flowStatus2 {
  background-color: #0CA6ED;
}
.flowStatus3 {
  background-color: #08CB83;
}
.flowStatus4 {
  background-color: #00B42A;
}
.flowStatus5 {
  background-color: #F2F3F5;
}
</style>
