<template>
  <PageContainer>
    <div slot="content" class="targetResult-body">
      <div class="targetResult-content-title">
        <span @click="goBack"> <i class="el-icon-arrow-left"></i><span style="margin-left: 10px">指标跟踪</span> </span>
      </div>
      <div class="targetResult-content">
        <div class="targetResult-content-top">
          <div class="topTitle">{{ trackInfo.nodeName || '' }}</div>
          <el-row :gutter="20" class="chartBox">
            <el-col :span="8" style="height: 100%">
              <div class="chartBox-left">
                <img src="@/assets/images/targetAnalysis/targetResult-record.png" alt="" />
                <div class="chartLeft-info">
                  <p>
                    <span>指标名称：</span><span>{{ trackInfo.libraryName || '-' }}</span>
                  </p>
                  <p>
                    <span>指标说明：</span><span>{{ trackInfo.illustrate || '-' }}</span>
                  </p>
                  <p>
                    <span>计算方法：</span><span>{{ trackInfo.calculationWay || '-' }}</span>
                  </p>
                </div>
              </div>
            </el-col>
            <el-col v-if="chartData.length" :span="16" style="height: 100%">
              <div id="Echart" style="height: 280px; width: 100%"></div>
            </el-col>
          </el-row>
        </div>
        <div :class="['targetResult-content-bottom', chartData.length ? 'tableHieght' : 'tableHieght1']">
          <div class="contentTable">
            <div class="contentTable-main table-content">
              <el-table v-loading="tableLoading" border style="width: 100%" height="100%" :data="tableData" :row-class-name="tableRowClassName">
                <el-table-column prop="cycleName" label="周期名称" show-overflow-tooltip></el-table-column>
                <el-table-column prop="startTime" label="开始时间" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span>{{ scope.row.startTime || '-' }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="endTime" label="结束时间" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span>{{ scope.row.endTime || '-' }}</span>
                  </template>
                </el-table-column>
                <el-table-column v-if="chartData.length" prop="score" label="得分" show-overflow-tooltip></el-table-column>
                <el-table-column prop="data" label="数据" show-overflow-tooltip></el-table-column>
              </el-table>
            </div>
            <div class="contentTable-footer">
              <el-pagination
                :current-page="pagination.current"
                :page-sizes="pagination.pageSizeOptions"
                :page-size="pagination.size"
                :layout="pagination.layoutOptions"
                :total="pagination.total"
                @size-change="paginationSizeChange"
                @current-change="paginationCurrentChange"
              >
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import * as echarts from 'echarts'
import tableListMixin from '@/mixins/tableListMixin.js'
export default {
  name: 'targetResult',
  mixins: [tableListMixin],
  data() {
    return {
      tableLoading: false,
      tableData: [],
      chartData: [],
      trackInfo: {
        calculationWay: '', // 计算方法
        nodeName: '', // 名称
        illustrate: '', //  指标说明
        libraryName: '' // 指标名称
      }
    }
  },
  computed: {},
  created() {
    this.getDataList()
  },
  methods: {
    getDataList() {
      this.tableLoading = true
      const { libraryId, manageId, planId, nodeCode } = this.$route.query
      let data = {
        libraryId: libraryId,
        manageId: manageId,
        planId: planId,
        nodeCode: nodeCode || '',
        page: this.pagination.current,
        pageSize: this.pagination.size
      }
      this.$api.getTargetTrackById(data).then((res) => {
        this.tableLoading = false
        if (res.code == 200) {
          this.trackInfo = res.data
          this.tableData = res.data.list.records
          this.chartData = res.data.dataList
          this.pagination.total = res.data.list.total
          if (this.chartData) {
            this.$nextTick(() => {
              this.getChartData(res.data.dataList)
            })
          } else {
            this.chartData = []
          }
        } else if (res.message) {
          this.$message.error(res.message)
        }
      })
    },
    // 图标渲染
    getChartData(list) {
      const getchart = echarts.init(document.getElementById('Echart'))
      getchart.resize()
      var xdata = list.map((item) => {
        return item.cycleName || ''
      })
      var scoreData = list.map((item) => {
        return item.score != undefined ? String(item.score) : ''
      })
      var infoData = list.map((item) => {
        return item.data || ''
      })
      let leftMax = Math.max.apply(null, scoreData) // 获取左边y轴数组的最大值
      let rightMax = Math.max.apply(null, infoData) || 100 // 获取右边y轴数组的最大值
      let yMax = Math.max(leftMax, rightMax) || 100 // 获取两个最大值之中的最大值
      var seriesObj = [
        {
          type: 'line',
          name: '得分',
          data: scoreData,
          symbolSize: 8,
          zlevel: 6,
          itemStyle: {
            normal: {
              color: '#FA403C',
              lineStyle: {
                // 线的颜色
                color: '#FA403C',
                width: 1.5
              },
              areaStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: 'rgba(250, 64, 60, 0.2)'
                    },
                    {
                      offset: 1,
                      color: 'rgba(250, 64, 60, 0.1)'
                    }
                  ]
                }
              }
            }
          }
        }
      ]
      var seriesObj1 = [
        {
          type: 'line',
          name: '得分',
          data: scoreData,
          symbolSize: 8,
          yAxisIndex: 0,
          itemStyle: {
            normal: {
              color: '#FA403C',
              lineStyle: {
                // 线的颜色
                color: '#FA403C',
                width: 1.5
              },
              areaStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: 'rgba(250, 64, 60, 0.2)'
                    },
                    {
                      offset: 1,
                      color: 'rgba(250, 64, 60, 0.1)'
                    }
                  ]
                }
              }
            }
          }
        },
        {
          type: 'line',
          name: '数据',
          data: infoData,
          // showSymbol: false,/
          symbolSize: 8,
          yAxisIndex: 1,
          itemStyle: {
            normal: {
              color: '#4764CC',
              lineStyle: {
                // 线的颜色
                color: '#4764CC',
                width: 1.5
              },
              areaStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: 'rgba(71, 100, 204, 0.2)'
                    },
                    {
                      offset: 1,
                      color: 'rgba(71, 100, 204, 0.1)'
                    }
                  ]
                }
              }
            }
          }
        }
      ]
      const option = {
        legend: {
          top: '2%'
        },
        backgroundColor: '#fff',
        tooltip: {
          trigger: 'axis',
          show: true
        },
        grid: {
          left: '2%',
          right: '8%',
          bottom: '10%',
          top: '15%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: xdata,
            boundaryGap: true,
            axisLine: {
              lineStyle: {
                color: 'rgba(65,97,128,0.15)'
              }
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              color: '#666666'
            }
          }
        ],
        // yAxis: [
        //   {
        //     type: 'value'
        //   }
        // ],
        yAxis: [
          {
            name: '得分',
            type: 'value',
            min: 0,
            max: yMax,
            axisLabel: {
              formatter: '{value} 分'
            }
          },
          {
            name: '数据',
            min: '0',
            type: 'value',
            max: yMax
          }
        ],
        series: this.chartData.length ? seriesObj1 : seriesObj
      }
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // table隔行变色
    tableRowClassName({ row, rowIndex }) {
      if (rowIndex % 2 == 1) {
        return 'warning-row'
      } else {
        return 'success-row'
      }
    },
    // 返回
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>
<style lang="scss" scoped>
.targetResult-body {
  height: 100%;
  width: 100%;
  background: #fff;
  .targetResult-content-title {
    border-bottom: 1px solid #e4e7ed;
    padding: 13px 24px;
    cursor: pointer;
  }
  .targetResult-content {
    padding: 8px 24px;
    height: calc(100% - 50px);
    .targetResult-content-top {
      margin-bottom: 16px;
      .topTitle {
        font-size: 16px;
        font-weight: 600;
        padding: 16px 0 16px 0;
        color: #333333;
      }
      .chartBox {
        margin: auto;
        .chartBox-left {
          display: flex;
          .chartLeft-info {
            margin: auto 0;
            p {
              margin-bottom: 4px !important;
              padding: 0 5px;
            }
            p span:nth-child(1) {
              font-size: 14px;
              font-weight: 300;
              color: #7f848c;
              margin: 0 16px;
            }
            p span:nth-child(2) {
              font-size: 14px;
              font-weight: 400;
              color: #333333;
            }
          }
        }
        .chartBox-right {
          width: 50%;
          height: 100%;
        }
      }
    }
    .tableHieght {
      height: calc(100% - 359px);
    }
    .tableHieght1 {
      height: calc(100% - 185px);
    }
    .targetResult-content-bottom {
      background: #fff;
      border-radius: 4px;
      flex: 1;
      .contentTable {
        height: calc(100%);
        display: flex;
        flex-direction: column;
        .contentTable-main {
          flex: 1;
          overflow: auto;
        }
        .contentTable-footer {
          padding: 10px 0 0;
        }
        .el-table {
          height: calc(100% - 5px);
        }
      }
    }
    .content {
      width: 100%;
      max-height: 500px !important;
      overflow: auto;
      background-color: #fff !important;
    }
  }
}
.record {
  color: #3562db !important;
}
::v-deep .warning-row {
  background-color: #f5f5f5 !important;
}
</style>
