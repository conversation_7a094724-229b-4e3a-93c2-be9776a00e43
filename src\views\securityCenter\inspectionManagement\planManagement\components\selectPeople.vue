<template>
  <el-dialog class="sino-dialog-people" :title="title" :visible.sync="dialogVisiblePo" @close="closeDialog">
    <div class="peopleBox">
      <div class="left">
        <el-input v-model.trim="filterText" clearable placeholder="输入关键字进行过滤"></el-input>
        <el-tree
          ref="tree"
          v-loading="treeLoading"
          style="margin-top: 10px;"
          :data="data"
          :props="defaultProps"
          :default-expanded-keys="expanded"
          :filter-node-method="filterNode"
          node-key="id"
          highlight-current
          @node-click="handleNodeClick"
          @check="treeChecked"
        ></el-tree>
      </div>
      <div class="rightBox">
        <div class="rights">
          <div v-if="showSelection" class="top-search" style="margin-bottom: 15px;">
            <el-input v-model.trim="dialogFilters.name" placeholder="姓名" style="margin-right: 5px; width: 190px;"></el-input>
            <el-input v-model.trim="dialogFilters.mobilePhone" placeholder="手机号" style="margin-right: 10px; width: 190px;"></el-input>
            <el-button type="primary" plain @click="dialogReset">重置</el-button>
            <el-button type="primary" @click="dialogSearch">查询</el-button>
          </div>
          <div class="hasSelected">
            <div style="color: #5b5b5b; font-weight: 600;">已选择：</div>
            <span v-if="!newHasSelectedArr.length">暂无</span>
            <div v-else v-for="(item, index) in newHasSelectedArr" :key="index" style="color: #5188fc;">
              <span>{{ item.name || item.staffName }}</span>
              <span v-show="newHasSelectedArr.length > 1 && index !== newHasSelectedArr.length - 1">、</span>
            </div>
          </div>
          <div class="table-list">
            <el-table
              ref="materialTable"
              v-loading="dialogtableLoading"
              :data="tableWz"
              :border="true"
              stripe
              :row-key="
                (row) => {
                  return row.staffId
                }
              "
              :height="showSelection == true ? '300px' : '360px'"
              :cell-style="{ padding: '8px' }"
              style="overflow: auto;"
              :header-cell-style="$tools.setHeaderCell(3)"
              @select="select"
              @select-all="selectAll"
            >
              <el-table-column align="center" type="selection" width="55"></el-table-column>
              <el-table-column align="center" type="index" label="序号" width="65"></el-table-column>
              <el-table-column width="100" show-overflow-tooltip label="姓名" align="center">
                <template slot-scope="scope">
                  <span>{{ activeName == '0' ? scope.row.name : scope.row.staffName }}</span>
                </template>
              </el-table-column>
              <el-table-column show-overflow-tooltip label="所属科室/部门" align="center">
                <template slot-scope="scope">
                  <!-- <span>{{
                    activeName == "0"
                      ? scope.row.officeName
                      : scope.row.groupName
                  }}</span> -->
                  <span>{{ scope.row.controlTeamName }}</span>
                </template>
              </el-table-column>
              <!-- <el-table-column
                prop="jobNumber"
                label="工号"
                show-overflow-tooltip
              ></el-table-column> -->
              <el-table-column align="center" prop="mobilePhone" label="手机号" show-overflow-tooltip></el-table-column>
            </el-table>
            <el-pagination
              :current-page="paginationData.currentPage"
              :page-sizes="[15, 20, 30, 50]"
              :page-size="paginationData.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="paginationData.total"
              style="margin-bottom: 20px; position: absolute; bottom: 50px; right: 20px;"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            ></el-pagination>
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button  type="primary" plain @click="closeDialog">取 消</el-button>
          <el-button  type="primary" @click="sureWz">确 定</el-button>
        </span>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import { transData } from '@/util'
export default {
  props: {
    title: {
      type: String,
      default: '选择人员'
    },
    dialogVisiblePo: {
      type: Boolean,
      default: false
    },
    flag: {
      type: String,
      default: 'maintainFlag'
    },
    parentComData: {
      default: []
    }
  },
  data() {
    return {
      filterText: '',
      tableWz: [],
      buttonLoading: false,
      tableSort: '',
      tableOrderBy: '',
      multipleSelectArr: [],
      activeName: '0',
      type: '2',
      defaultProps: {
        children: 'children',
        label: function (data, node) {
          return data.teamName
        },
        value: 'id'
      },
      data: [],
      officeName: '', // 查找条件,班组名称
      LOGINDATA: '',
      treeNodeData: [],
      checkList: [], // 多选框保存数据
      deptName: '',
      dialogFilters: {
        name: '',
        mobilePhone: ''
      },
      dialogtableLoading: false,
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      showSelection: true,
      multipleSelectArr1: [],
      multipleSelectArr2: [],
      tableW: [],
      multipleSelectArrs: [],
      newArr: [],
      hasSelectedArr1: [],
      hasSelectedArr2: [],
      hasSelectedArrs: [],
      newHasSelectedArr: [],
      treeLoading: false,
      checkedData: '',
      expanded: [],
      defaultKeys: ''
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    },
    screenHeight(n, o) {}
  },
  created() {},
  mounted() {
    // 获取登录信息
    this.LOGINDATA = JSON.parse(sessionStorage.getItem('LOGINDATA'))
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true
      return data.teamName.indexOf(value) !== -1
    },
    treeChecked(data, checked) {
      this.checkedData = data
      this.$refs.tree.setCheckedNodes([data])
    },
    parentComponentFun() {
      this.newHasSelectedArr = this.parentComData
      this.hasSelectedArrs = this.parentComData
      this.multipleSelectArr = this.parentComData
      this.init()
    },

    init() {
      this.treeLoading = true
      // 获取管控小组
      this.$api.ipsmGetControlGroupInfoList({}).then((res) => {
        this.treeLoading = false
        let planText = {
          id: '#',
          teamName: '安全管控部门',
          parentId: '',
          allParentIds: '',
          level: 0
        }
        let list = res.data.list
        list.push(planText)
        this.data = transData(list, 'id', 'parentId', 'children')
        this.$nextTick(() => {
          this.expanded = this.tableCode ? [this.tableCode] : [this.data[0].id]
          this.$refs.tree.setCurrentKey(this.tableCode ? this.tableCode : this.data[0].id)
        })
        let checkItem = list.find((item) => {
          return item.id == this.tableCode
        })
        this.checkedData = checkItem || this.data[0]
        this.tableCode = this.tableCode ? this.tableCode : this.data[0].id
        this.tableData = []
        this.getTableData()
      })
    },
    /**
     * 树状图点击
     */
    handleNodeClick(data, checked) {
      this.defaultKeys = data.id
      // sessionStorage.setItem("defaultKeys", this.defaultKeys);
      this.paginationData.currentPage = 1
      // this.$store.commit('changeTableLabel', 'search')
      this.checkedData = data
      this.tableCode = data.id
      this.$refs.tree.setCheckedNodes([data])
      this.getTableData(data.id)
    },
    // 查询人员
    getTableData(val) {
      this.tableLoading = true
      let data = {
        currentPage: this.paginationData.currentPage,
        pageSize: this.paginationData.pageSize,
        controlTeamId: this.checkedData.id == '#' ? '' : this.checkedData.id, // 选第一级传空
        ...this.dialogFilters
      }
      this.$api
        .getControlTeamUserList(data)
        .then((res) => {
          this.tableLoading = false
          if (res.code == 200) {
            this.tableLoading = false
            this.tableWz = res.data.list
            this.paginationData.total = parseInt(res.data.sum)
          }
          this.$nextTick(() => {
            this.newHasSelectedArr.forEach((i) => {
              this.tableWz.find((element) => {
                if (element.id == i.id) {
                  this.$refs.materialTable.toggleRowSelection(element, true)
                }
              })
            })
          })
        })
        .catch(() => {
          this.tableLoading = this.$store.state.loadingShow
        })
    },
    // 条件查询
    _searchByCondition() {},

    // 选择表格
    select(val, row) {
      console.log(val, row)
      if (this.dialogFilters.name != '' || this.dialogFilters.mobilePhone != '') {
        if (this.multipleSelectArr.every((i) => i.id != row.id)) {
          this.multipleSelectArr.push(row)
        } else {
          this.multipleSelectArr.some((item, i) => {
            if (item.id == row.id) {
              this.multipleSelectArr.splice(i, 1)
              return true
            }
          })
        }
      } else {
        this.multipleSelectArr = val
      }
      if (this.activeName == 0) {
        // 院外   解决切换tab栏表格无法清空上一个tab栏选中状态问题
        this.hasSelectedArr1 = [...this.multipleSelectArr, ...this.hasSelectedArr2] // 要实时显示在页面的已选择人员
        if (this.multipleSelectArr.length > 0) {
          this.multipleSelectArr1 = [...this.multipleSelectArr, ...this.multipleSelectArr1]
        }
        if (this.multipleSelectArr.length == 0 && this.multipleSelectArr2.length > 0) {
          this.multipleSelectArrs = [...this.multipleSelectArr1, ...this.multipleSelectArr2]
        }
      } else {
        // 院内
        this.hasSelectedArr2 = [...this.multipleSelectArr, ...this.hasSelectedArr1] // 要实时显示在页面的已选择人员
        if (this.multipleSelectArr.length > 0) {
          this.multipleSelectArr2 = [...this.multipleSelectArr, ...this.multipleSelectArr2]
        }
        if (this.multipleSelectArr.length == 0 && this.multipleSelectArr1.length > 0) {
          this.multipleSelectArrs = [...this.multipleSelectArr2, ...this.multipleSelectArr1]
        }
      }

      // ----------------实时显示
      this.hasSelectedArrs = [...this.hasSelectedArr1, ...this.hasSelectedArr2]
      console.log('实时显示已选择人员', this.hasSelectedArrs)
      // 去重
      this.newHasSelectedArr = []
      for (var i = 0; i < this.hasSelectedArrs.length; i++) {
        if (this.newHasSelectedArr.indexOf(this.hasSelectedArrs[i]) === -1) {
          this.newHasSelectedArr.push(this.hasSelectedArrs[i])
        }
      }
      console.log(this.newHasSelectedArr)

      // ----------------------传值
      this.multipleSelectArrs = [...this.multipleSelectArr1, ...this.multipleSelectArr2]
      // //去重
      this.newArr = []
      for (var i = 0; i < this.multipleSelectArrs.length; i++) {
        if (this.newArr.indexOf(this.multipleSelectArrs[i]) === -1) {
          this.newArr.push(this.multipleSelectArrs[i])
        }
      }
      console.log('传给父组件的人员', this.hasSelectedArrs)
    },
    // 全选按钮
    selectAll(val) {
      this.newHasSelectedArr = val
      this.hasSelectedArrs = val
    },
    // 获取当前页选中状态的集合
    handleSelectionChangeDialog(selection) {
      selection.forEach((i) => {
        if (this.newHasSelectedArr.filter((item) => item.id != i.id)) {
          this.newHasSelectedArr.push(i)
        }
      })
    },
    // 点击确定
    sureWz() {
      this.$emit('multipleSelectArr', this.hasSelectedArrs)
      this.closeDialog()
    },

    // 选择 院外 复选框，，查询人员
    contentClick() {
      this._getPeopleListInfo()
    },
    // 重置查询条件
    dialogReset() {
      this.dialogFilters = {
        name: '',
        mobilePhone: ''
      }
      this.init()
    },
    // 条件查询
    dialogSearch() {
      this.paginationData.currentPage = 1
      this.getTableData()
    },
    closeDialog() {
      this.dialogFilters = {
        name: '',
        mobilePhone: ''
      }
      this.$emit('closeDialog')
    },

    handleSizeChange(val) {
      this.paginationData.currentPage = 1
      this.paginationData.pageSize = val
      this.getTableData(this.checkedData.id)
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.getTableData(this.checkedData.id)
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: linear-gradient(to right, #d9e1f8, #fff);
}

::v-deep .el-tree-node__content {
  height: 32px;
}

:deep(.el-dialog__body) {
  height: 535px !important;

  .el-tree {
    height: calc(100% - 42px) !important;
    overflow: auto;
  }
}

.hasSelected {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
  min-height: 20px;
  max-height: 38px;
  height: auto;
  overflow-y: auto;
}

.tabBox {
  min-height: 300px;
  max-height: 338px;
  height: auto;
  overflow-y: auto;
}

.sino-dialog-people {
  .peopleBox {
    height: 100%;
    display: flex;
    width: 100%;
    justify-content: space-between;
    .left {
      width: 30%;
    }
    .rightBox {
      width: 70%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .rights {
        margin-left: 25px;
        display: flex;
        flex-direction: column;
        flex-wrap: nowrap;
        justify-content: space-between;
        padding-top: 0 !important;
        max-width: 595px;

        .top-search {
          display: flex;
          flex-wrap: nowrap;
          align-items: center;
        }
      }
    }
  }

  :deep(.el-tabs__nav-wrap) {
    border-bottom: 0 !important;
  }

  :deep(.el-tabs__active-bar) {
    height: 0 !important;
  }

  :deep(.el-tabs__nav-wrap.is-left::after) {
    height: 0 !important;
  }

  :deep(.el-tabs__item.is-active) {
    background: #5188fc !important;
    color: #ffff;
    border: 1px solid #ccc;
    border-left: none;
    border-right: none;
  }

  :deep(.el-tabs__item.is-active:hover) {
    color: #ffff !important;
  }

  :deep(.el-tabs__item:hover) {
    color: #5188fc !important;
  }

  :deep(.el-tabs__nav) {
    float: none !important;
    text-align: center;
    border: 0;
  }

  :deep(.el-tabs__item) {
    border: 1px solid #ccc;
    width: 50%;
    height: 35px;
    line-height: 35px;
  }

  :deep(.el-tabs__item:first-child) {
    border-left: 1px solid #ccc;
  }

  .dialog-footer {
    position: absolute;
    bottom: 20px;
    right: 20px;
  }

  :deep(.el-dialog__body) {
    height: 500px;
  }
}
</style>
