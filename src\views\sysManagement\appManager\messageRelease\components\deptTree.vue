<!--
 * @Description:
-->
<template>
  <div>
    <div v-if="checkbox" style="padding-left: 24px;">
      <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">全选</el-checkbox>
    </div>
    <el-tree
      ref="deptTree"
      :data="deptTree"
      :show-checkbox="checkbox"
      node-key="id"
      :props="defaultProps"
      highlight-current
      :current-node-key="checkbox ? '' : selectKeys[0]"
      :default-checked-keys="selectKeys"
      :default-expanded-keys="selectKeys"
      @node-click="treeChange"
      @check="treeCheck">
    </el-tree>
  </div>
</template>

<script>
import { transData } from '@/util'
export default {
  name: 'deptTree',
  props: {
    checkbox: {
      type: Boolean,
      default: true
    },
    selectKeys: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      allDeptId: [],
      checkAll: false,
      isIndeterminate: false,
      deptTree: [], // 部门数据
      defaultProps: {
        children: 'children',
        label: 'deptName'
      }
    }
  },
  computed: {

  },
  created() {
    this.getDeptTreeList()
  },
  methods: {
    // 全选
    handleCheckAllChange(val) {
      this.$emit('deptCheck', val ? this.allDeptId : [])
      this.$refs.deptTree.setCheckedKeys(val ? this.allDeptId : [])
      this.isIndeterminate = false
    },
    treeCheck(data, checked) {
      this.$emit('deptCheck', checked.checkedKeys)
      this.checkAll = checked.checkedKeys.length === this.allDeptId.length
      this.isIndeterminate = checked.checkedKeys.length > 0 && checked.checkedKeys.length < this.allDeptId.length
    },
    treeChange(data) {
      if (!this.checkbox) {
        this.$emit('deptCheck', [data.id])
      }
    },
    // 获取部门数据
    getDeptTreeList() {
      this.$api.getSelectedDept({}).then((res) => {
        if (res.code == 200) {
          const deptList = res.data
          this.allDeptId = res.data.map(item => item.id)
          this.checkAll = this.selectKeys.length === this.allDeptId.length
          this.isIndeterminate = this.selectKeys.length > 0 && this.selectKeys.length < this.allDeptId.length
          this.deptTree = transData(deptList, 'id', 'pid', 'children')
        }
      })
    }
  }
}

</script>

<style lang="scss" scoped>
::v-deep .el-tree {
  .el-tree-node__label {
    font-size: 14px;
  }
}
</style>
