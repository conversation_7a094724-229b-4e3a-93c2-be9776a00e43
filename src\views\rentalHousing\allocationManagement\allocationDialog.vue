<template>
  <el-dialog
    v-dialogDrag
    class="component housing-edit"
    :title="title"
    width="1200px"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    custom-class="model-dialog"
    destroy-on-close
    @close="onDialogClosed"
  >
    <div class="dialog-content-box">
      <div class="housing-tree">
        <div class="space-tree__search">
          <el-input v-model="treeSearchKeyWord" placeholder="输入关键字搜索" suffix-icon="el-icon-search"></el-input>
        </div>
        <el-tree
          ref="treeRef"
          v-loading="treeLoading"
          class="space-tree__tree"
          :data="treeData"
          node-key="id"
          size="small"
          :highlight-current="true"
          :filter-node-method="filterNode"
          :default-expanded-keys="expandedIds"
          :expand-on-click-node="false"
          @current-change="onCurrentChange"
        >
          <template #default="{ node, data }">
            <div class="custom-tree-node">
              <div class="custom-tree-node-wrapper">
                <span class="custom-tree-node-label">
                  {{ data.spaceName }}
                </span>
              </div>
            </div>
          </template>
        </el-tree>
      </div>
      <div class="housing-list-box">
        <div class="housing-list-search">
          <el-form ref="formRef" class="housing-list__form" :model="searchForm" inline>
            <el-form-item prop="roomNo">
              <el-input v-model="searchForm.roomNo" placeholder="全部房间号" maxlength="25" clearable></el-input>
            </el-form-item>
            <el-form-item prop="type">
              <el-select v-model="searchForm.type" style="width: 120px" placeholder="全部房型" clearable>
                <el-option v-for="item of formOptions.type" :key="item.dictCode" :label="item.dictName" :value="item.dictCode"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="mode">
              <el-select v-model="searchForm.mode" style="width: 120px" placeholder="房源类型" clearable>
                <el-option v-for="item of formOptions.mode" :key="item.dictCode" :label="item.dictName" :value="item.dictCode"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="rent">
              <el-input
                v-model="searchForm.rent[0]"
                style="width: 105px"
                type="number"
                :min="0"
                :max="maxPrice"
                placeholder="出租租金"
                @change="onInputNumberChange(0, $event)"
              ></el-input>
              至
              <el-input
                v-model="searchForm.rent[1]"
                style="width: 105px"
                type="number"
                :min="0"
                :max="maxPrice"
                placeholder="出租租金"
                @change="onInputNumberChange(1, $event)"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" plain @click="onReset">重置</el-button>
              <el-button type="primary" @click="onSearch">查询</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="table-box">
          <div class="tableList">
            <el-table v-loading="tableLoading" height="100%" :data="tableData" border stripe table-layout="auto" class="tableAuto" row-key="id">
              <el-table-column prop="radio" width="55">
                <template slot-scope="scope">
                  <el-radio v-model="selectedRow" :label="scope.row.id" name="" @change="handleRadioChange(scope.row)"></el-radio>
                </template>
              </el-table-column>
              <el-table-column prop="houseName" show-overflow-tooltip label="房间名称"></el-table-column>
              <el-table-column prop="spaceNames" show-overflow-tooltip label="空间位置"></el-table-column>
              <el-table-column prop="houseTypeName" label="房型" width="90px"></el-table-column>
              <el-table-column prop="floorArea" label="建筑面积(㎡)" width="100px"></el-table-column>
              <el-table-column prop="hirePrice" label="出租单价(元/㎡/月)" width="140px"></el-table-column>
              <el-table-column prop="hireRent" label="出租租金(元/月)" width="120px"></el-table-column>
              <el-table-column prop="houseStateName" label="状态" width="90px"></el-table-column>
              <el-table-column prop="remark" show-overflow-tooltip label="备注"></el-table-column>
            </el-table>
          </div>
          <div class="tablePagination">
            <el-pagination
              :layout="pagination.layoutOptions"
              :current-page="pagination.current"
              :page-sizes="pagination.pageSizeOptions"
              :page-size="pagination.size"
              :total="pagination.total"
              @size-change="paginationSizeChange"
              @current-change="paginationCurrentChange"
            ></el-pagination>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <el-button type="primary" plain @click="onDialogClosed">取消</el-button>
      <el-button type="primary" @click="onSubmit">确认</el-button>
    </template>
  </el-dialog>
</template>
<script>
const TreeRootId = '-1'
const TreeRoot = {
  spaceName: '所有小区',
  id: TreeRootId,
  spaceLevel: 0,
  children: []
}
import { HouseStatus } from '@/views/rentalHousing/housingResource/constant'
export default {
  props: {
    title: {
      type: String,
      default: '分配房间'
    },
    dialogVisible: {
      type: Boolean,
      default: false
    },
    roomName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      pagination: {
        size: 15, // 每页请求条数
        current: 1, // 当前页码
        total: 0, // 总条数
        layoutOptions: 'total, sizes, ->, prev, pager, next, jumper',
        // layoutOptions: 'total, sizes, prev, pager, next, jumper',
        pageSizeOptions: [15, 30, 50, 100]
      }, // 分页信息
      tableLoading: false,
      treeLoading: false,
      treeSearchKeyWord: '',
      // 当前树选中的节点data-id
      currentTreeNodeId: TreeRootId,
      treeData: [TreeRoot],
      expandedIds: [TreeRootId],
      currentSpaceId: '',
      // 表单搜索项
      formOptions: {
        // 房型
        type: [],
        // 状态
        status: HouseStatus,
        // 性质
        mode: []
      },
      searchForm: {
        // 房号
        roomNo: '',
        // 房型
        type: '',
        // 性质
        mode: '',
        // 租金范围
        rent: ['', ''],
        // 出租状态
        status: '0'
      },
      tableData: [],
      selectedRow: '',
      roomInfo: {}
    }
  },
  watch: {
    currentTreeNodeId: {
      handler(newVal, oldVal) {
        this.searchForm.roomNo = this.roomName
        this.onSearch()
      },
      immediate: true
    }
  },
  computed: {
    maxPrice() {
      return 99999.99
    }
  },
  mounted() {
    this.getDictData()
    this.getTreeListData()
  },
  methods: {
    onReset() {
      this.searchForm = {
        // 房号
        roomNo: '',
        // 房型
        type: '',
        // 性质
        mode: '',
        // 租金范围
        rent: ['', ''],
        // 出租状态
        status: '0'
      }
      this.pagination.current = 1
      this.onSearch()
    },
    onSearch() {
      this.getDataList()
    },
    // 分页获取数据
    getDataList() {
      // 防止树节点切换太快造成请求异常。
      if (this.tableLoading) {
        this.$tools.cancelAjax()
      }
      this.tableData = []
      const params = {
        spaceId: this.currentTreeNodeId,
        houseName: this.searchForm.roomNo,
        pageNum: this.pagination.current,
        pageSize: this.pagination.size,
        houseNatureCode: this.searchForm.mode,
        houseState: this.searchForm.status,
        houseTypeCode: this.searchForm.type,
        hireRentMin: this.searchForm.rent[0],
        hireRentMax: this.searchForm.rent[1]
      }
      // 如果节点ID的长度小于3认为是根节点，传空查全部
      if (params.spaceId && params.spaceId.length < 3) {
        params.spaceId = ''
      }
      this.tableLoading = true
      this.$api.rentalHousingApi
        .queryHouseByPage(params)
        .then((res) => {
          if (res.code === '200') {
            this.$nextTick(() => {
              this.pagination.total = res.data.total
              this.tableData = res.data.records
            })
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取房源列表失败'))
        .finally(() => (this.tableLoading = false))
    },
    // 获取数据字典
    getDictData() {
      const params = {
        pageNo: 1,
        pageSize: 9999
      }
      this.$api.rentalHousingApi.queryDictPage(params).then((res) => {
        if (res.code === '200') {
          res.data.records.forEach((it) => {
            // 只取启用的
            if (it.dictState !== '1') return
            if (it.dictType === 'fangxing') {
              this.formOptions.type.push(it)
            } else if (it.dictType === 'leixing') {
              this.formOptions.mode.push(it)
            }
          })
        }
      })
    },
    // 设置树选中节点
    setTreeSelected() {
      if (!this.$refs.treeRef) return
      this.$refs.treeRef.setCurrentKey(this.currentTreeNodeId)
    },
    /** 获取树数据 */
    getTreeListData() {
      this.treeLoading = true
      this.$api.rentalHousingApi
        .allSpaceList()
        .then((res) => {
          if (res.code === '200') {
            const data = this.$tools.transData(res.data, 'id', 'parentId', 'children')
            this.treeData[0].children = data
            this.$nextTick(() => {
              // 选中节点
              this.setTreeSelected()
            })
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取空间信息失败'))
        .finally(() => (this.treeLoading = false))
    },
    // 当前选中节点发生变化
    onCurrentChange(nodeData, treeNode) {
      if (this.currentTreeNodeId === nodeData.id) {
        return
      }
      // set key
      this.currentTreeNodeId = nodeData.id
      this.onSearch()
      // 获取展开层
      const ids = []
      let node = treeNode
      while (node) {
        const nextNode = node.parent
        // 树会存在顶级节点。
        if (!nextNode) break
        const data = node.data
        ids.unshift(data.id)
        node = nextNode
      }
      this.expandedIds = ids
    },
    /** 树结构过滤 */
    filterNode(value, data) {
      if (!value) return true
      return data.spaceName.includes(value)
    },
    /** 单选 */
    handleRadioChange(row) {
      this.roomInfo = row
    },
    // 分页数量变化事件
    paginationSizeChange(size) {
      this.pagination.size = size
      this.getDataList()
    },
    // 分页页码变化事件
    paginationCurrentChange(current) {
      this.pagination.current = current
      this.getDataList()
    },
    onDialogClosed() {
      this.$emit('close')
    },
    onSubmit() {
      if (JSON.stringify(this.roomInfo) === '{}') {
        return this.$message.error('请选择房间！')
      }
      this.$emit('verify', this.roomInfo)
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content-box {
  width: 100%;
  height: 550px;
  display: flex;
  background: #fff;

  .housing-tree {
    padding: 15px;
    width: 250px;
    box-sizing: border-box;
    border-right: solid 1px #eee;
    .space-tree__search {
      height: 40px;
      line-height: 40px;
      margin-bottom: 20px;
    }
    ::v-deep(.el-tree) {
      position: relative;
      padding-right: 16px;
      height: calc(100% - 48px);
      overflow: auto;
      .el-tree-node__content {
        line-height: 32px;
        height: 32px;
      }
      .el-tree-node.is-current > .el-tree-node__content {
        color: #3562db;
        background: #e6effc;
      }
    }
  }
  .housing-list-box {
    width: calc(100% - 250px);
    height: 100%;
    padding: 15px;
    box-sizing: border-box;
    .table-box {
      height: calc(100% - 62px);
      .tableList {
        height: calc(100% - 42px);
      }
      .tablePagination {
        padding-top: 10px;
      }
    }
  }
}
.el-radio {
  display: flex;
  justify-content: center;
  align-items: center;
}
::v-deep .el-radio__label {
  display: none;
}
</style>