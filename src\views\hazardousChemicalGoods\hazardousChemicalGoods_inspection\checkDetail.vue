<template>
  <PageContainer v-loading="blockLoading" :footer="true">
    <div slot="content" class="inner">
      <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
        <span class="green_line"></span>
        基本信息
      </div>
      <el-row>
        <el-col :span="8">
          <span>仓库名称：</span>
          <span>{{ checkInfo.warehouseName }}</span>
        </el-col>
        <el-col :span="8">
          <span>仓库地址：</span>
          <span>{{ checkInfo.hospitalAddress }}</span>
        </el-col>
        <el-col :span="24">
          <span>检查表名称：</span>
          <span>{{ tableDetail.projectName }}</span>
        </el-col>
        <el-col :span="24">
          <span>检查表说明：</span>
          <span>{{ tableDetail.projectExplain }}</span>
        </el-col>
      </el-row>
      <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
        <span class="green_line"></span>
        检查人和单位信息
      </div>
      <el-row>
        <el-col :span="8">
          <span>检查人：</span>
          <span>{{ checkInfo.inspectorName }}</span>
        </el-col>
        <el-col :span="8">
          <span>检查人电话：</span>
          <span>{{ checkInfo.inspectorPhone }}</span>
        </el-col>
      </el-row>
      <el-row v-for="(item, index) in JSON.parse(checkInfo.assistantInspectors)" :key="index" class="peerReviewer">
        <el-col :span="8">
          <span>同行检查人：</span>
          <span>{{ item.peerReviewerName }}</span>
        </el-col>
        <el-col :span="8">
          <span>检查人电话：</span>
          <span>{{ item.peerReviewerPhone }}</span>
        </el-col>
        <el-col :span="24">
          <el-checkbox v-model="item.isInspectionResults" readonly></el-checkbox>
          同行检查人已知晓
        </el-col>
      </el-row>
      <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
        <span class="green_line"></span>
        现场负责人信息
      </div>
      <el-row>
        <el-col :span="8">
          <span>负责人姓名：</span>
          <span>{{ checkInfo.responsiblePersonName }}</span>
        </el-col>
        <el-col :span="8">
          <span>负责人电话：</span>
          <span>{{ checkInfo.responsiblePersonPhone }}</span>
        </el-col>
      </el-row>
      <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
        <span class="green_line"></span>
        检查表历史记录
      </div>
      <div v-if="tableDetail.equipmentTypeId == '1'" class="inspection-content">
        <div v-for="(item, index) in tableDetail.maintainProjectdetailsReleaseList" :key="index" class="content-block">
          <div class="porject-name">
            <div class="porject porject-index">
              <span class="index-text">{{ index + 1 }}</span>
            </div>
            <div class="porject porject-input">
              巡检项目：
              <span class="gray">{{ item.detailName }}</span>
            </div>
          </div>
          <div class="content-block">
            <div v-for="(e, i) in item.maintainProjectdetailsTermReleaseList" :key="i" class="termContent">
              <span class="index-text">{{ index + 1 + '.' + (i + 1) }}&nbsp;&nbsp;</span>
              <div class="termContent-input">
                巡检要点：
                <span class="gray">{{ e.content }}</span>
              </div>
              <template>
                <div v-if="e.isNum == '0'" class="termContent-tools tools-number">
                  <div class="termContent-number">
                    正常范围：
                    <span class="gray">{{ e.rangeStart }}</span>
                    至
                    <span class="gray">{{ e.rangeEnd }}</span>
                    <span>
                      &nbsp;单位：
                      <span class="gray">{{ e.einheitName ? e.einheitName : '无' }}</span>
                    </span>
                  </div>
                  <div class="contentText">
                    <span>巡检结果：{{ e.contentStandard }}</span>
                  </div>
                </div>
                <div v-if="e.isNum == '2'" class="termContent-tools">
                  <div class="contentText">
                    <span>巡检结果：{{ e.contentStandard }}</span>
                  </div>
                </div>
                <div v-if="e.isNum == '3'" class="termContent-tools tools-radio">
                  <el-radio-group v-model="e.contentStandard" disabled>
                    <el-radio v-for="(radions, radionKey) in JSON.parse(e.termJson)" :key="radionKey" :label="radions.contText">
                      {{ radions.contText }}
                    </el-radio>
                  </el-radio-group>
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
      <div v-else class="inspection-content" style="margin-top: 10px">
        <div class="content-block">
          <el-table :data="tableDetail.maintainProjectdetailsReleaseList" :style="{ width: '80%' }">
            <el-table-column label="序号" type="index" width="80"> </el-table-column>
            <el-table-column prop="detailName" label="巡检内容" show-overflow-tooltip> </el-table-column>
            <el-table-column label="标准要求" show-overflow-tooltip>
              <template slot-scope="scope">
                <span>{{ scope.row.maintainProjectdetails.standardRequirements }}</span>
              </template>
            </el-table-column>
            <el-table-column label="巡检依据" show-overflow-tooltip>
              <template slot-scope="scope">
                <span>{{ scope.row.maintainProjectdetails.inspectionBasis }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <el-row>
        <el-col :span="24" style="display: flex">
          <span>附件资料：&nbsp;&nbsp;</span>
          <span v-if="imageUrl.length" class="resultImgBox">
            <template v-for="(item, index) in imageUrl">
              <el-image :key="index" style="max-height: 120px; max-width: 100px; margin-right: 5px" :src="item" fit="scale-down" :preview-src-list="[item]"></el-image>
            </template>
          </span>
          <span v-else>暂无</span>
        </el-col>
        <el-col :span="8">
          <span>检查时间：</span>
          <span>{{ checkInfo.inspectionDate }}</span>
        </el-col>
        <el-col :span="8">
          <span>填表时间：</span>
          <span>{{ checkInfo.createDate }}</span>
        </el-col>
      </el-row>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">关闭</el-button>
    </div>
  </PageContainer>
</template>
<script>
export default {
  components: {},
  data() {
    return {
      blockLoading: false,
      checkInfo: {},
      tableDetail: {},
      readonly: true,
      imageUrl: []
    }
  },
  created() {
    this.checkInfo = JSON.parse(sessionStorage.getItem('checkInfo'))
    this.imageUrl = []
    if (this.checkInfo.attachedFiles) {
      let imagneList = this.checkInfo.attachedFiles.split(',')
      imagneList.forEach((item) => {
        this.imageUrl.push(sessionStorage.getItem('ihcrs_picPrefix') + '/insp/' + item)
      })
    }
    this.getCheckTableDetailData()
  },
  beforeDestroy() {
    sessionStorage.removeItem('checkInfo')
  },
  methods: {
    getCheckTableDetailData() {
      this.blockLoading = true
      this.$api
        .getCheckTableDetail({
          id: this.$route.query.id
        })
        .then((res) => {
          if (res.code == '200') {
            this.tableDetail = res.data
          } else {
            this.$message.error(res.message || '获取失败')
          }
        })
        .finally(() => {
          this.blockLoading = false
        })
    },
    formate(row) {
      if (this.projectType == '1') {
        // 数值
        if (row.isNum == '0') {
          return row.rangeStart + '-' + row.rangeEnd + (row.einheitName || '')
        } else if (row.isNum == '1' || row.isNum == '2') {
          // 无 || 文本
          return '无'
        } else if (row.isNum == '3') {
          // 选项
          const option = JSON.parse(row.termJson)
          const contTexts = option.map((i) => i.contText)
          return contTexts.join('、')
        }
      } else {
        return row.inspectionBasis
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.inner {
  height: 100%;
  padding: 20px;
  overflow: auto;
  background-color: #fff;
  .el-row {
    padding: 0 20px;
    .el-col {
      margin: 15px 0;
    }
  }
  .peerReviewer {
    .el-col {
      margin-bottom: 0;
    }
    .el-col:last-child {
      margin-bottom: 25px;
    }
  }
  .inspection-content {
    width: 1200px;
    padding: 20px 0 0 42px;
    font-size: 14px;
    font-weight: 400;
    color: rgb(96 98 102 / 100%);
    .content-block {
      margin-bottom: 20px;
      .porject-name {
        .porject {
          display: inline-block;
        }
        .porject-index {
          width: 30px;
          .index-icon {
            position: relative;
            right: 8px;
            top: 1px;
          }
        }
        .porject-input {
          width: 810px;
          .el-input {
            display: inline-block;
            width: 90%;
          }
        }
        .porject-button {
          width: 200px;
          padding-left: 30px;
        }
      }
      .termContent {
        padding-left: 52px;
        margin-top: 20px;
        .termContent-input {
          display: inline-block;
          width: auto;
          margin-right: 30px;
          .el-input {
            display: inline-block;
            width: 400px;
          }
          .termContent-button {
            height: 42px;
            line-height: 42px;
            color: rgb(44 199 197 / 100%);
            margin-right: 20px;
            cursor: pointer;
          }
          .button-detele {
            color: rgb(252 44 97 / 100%);
          }
        }
        .termContent-tools {
          padding-left: 75px;
          margin-top: 20px;
          .termContent-number {
            display: inline-block;
          }
          .contentText {
            margin-top: 10px;
          }
        }
        .termContent-radio {
          .radio-text {
            .el-input {
              display: inline-block;
              width: 300px;
            }
          }
        }
      }
    }
  }
  ::v-deep .el-radio-group {
    .el-radio__label {
      color: #121f3e !important;
    }
    .el-radio__input.is-checked .el-radio__inner {
      border-color: #3562db;
      background-color: #3562db;
    }
    .el-radio__inner::after {
      background-color: #f5f7fa;
    }
  }
  .maintain-list-item {
    display: inline-block;
    width: 33%;
    overflow: hidden;
    text-overflow: ellipsis;
    .list-item {
      display: block;
      height: 40px;
      line-height: 40px;
      font-size: 15px;
      .label {
        display: inline-block;
        width: 100px;
        text-align: right;
        font-weight: 400;
        color: rgb(96 98 102 / 100%);
      }
      .value {
        font-weight: 400;
        color: rgb(144 147 153 / 100%);
      }
    }
  }
  .maintain-list-block {
    line-height: 20px;
    .list-item {
      font-size: 15px;
    }
    .label {
      display: inline-block;
      width: 100px;
      vertical-align: top;
      text-align: right;
      font-weight: 400;
      color: rgb(96 98 102 / 100%);
    }
    .value {
      display: inline-block;
      width: 880px;
      font-weight: 400;
      color: rgb(144 147 153 / 100%);
    }
  }
}
</style>
