import store from '@/store/index'
import getFileName from './getFileName'
export function deepClone(target) {
  // 定义一个变量
  let result
  // 如果当前需要深拷贝的是一个对象的话
  if (typeof target === 'object') {
    // 如果是一个数组的话
    if (Array.isArray(target)) {
      result = [] // 将result赋值为一个数组，并且执行遍历
      for (let i in target) {
        // 递归克隆数组中的每一项
        result.push(deepClone(target[i]))
      }
      // 判断如果当前的值是null的话；直接赋值为null
    } else if (target === null) {
      result = null
      // 判断如果当前的值是一个RegExp对象的话，直接赋值
    } else if (target.constructor === RegExp) {
      result = target
    } else {
      // 否则是普通对象，直接for in循环，递归赋值对象的所有值
      result = {}
      for (let i in target) {
        result[i] = deepClone(target[i])
      }
    }
    // 如果不是对象的话，就是基本数据类型，那么直接赋值
  } else {
    result = target
  }
  // 返回最终结果
  return result
}
function hasPermission(permission) {
  if (store.state.settings.openPermission) {
    return store.state.user.permissions.some((v) => {
      return v.pathUrl === permission
    })
  } else {
    return true
  }
}
export function auth(value) {
  let auth
  if (typeof value === 'string') {
    auth = hasPermission(value)
  } else {
    auth = value.some((item) => {
      return hasPermission(item)
    })
  }
  return auth
}
export function authAll(value) {
  const auth = value.every((item) => {
    return hasPermission(item)
  })
  return auth
}

export function ListTree(list, rootid) {
  var arr = []
  list.forEach((item) => {
    if (item.id === rootid) {
      arr.push(item.id)
      const newArr = ListTree(list, item.pid)
      if (newArr) {
        arr.unshift(...newArr)
      }
    }
  })
  return arr
}

export function transData(a, idStr, pidStr, chindrenStr, extraParameter) {
  let r = [],
    hash = {},
    id = idStr,
    pid = pidStr,
    children = chindrenStr,
    i = 0,
    j = 0,
    len = a.length
  for (; i < len; i++) {
    hash[a[i][id]] = a[i]
  }
  for (; j < len; j++) {
    let aVal = a[j],
      hashVP = hash[aVal[pid]]
    if (hashVP) {
      !hashVP[children] && (hashVP[children] = [])
      hashVP[children].push(aVal)
    } else {
      r.push(aVal)
    }
    //            查找已部署节点id集
    if (extraParameter && aVal.state == '1') extraParameter.push(aVal.id)
  }
  return r
}

/**
 * 一维数组转换多维数组
 * @param {Number} num
 * @param {Array} arr
 * @returns
 */
export function arrTrans(num, arr) {
  const iconsArr = []
  arr.forEach((item, index) => {
    const page = Math.floor(index / num)
    if (!iconsArr[page]) {
      iconsArr[page] = []
    }
    iconsArr[page].push(item)
  })
  return iconsArr
}
export function listToTree(list, id, pid) {
  const temp = {}
  const tree = []
  list.forEach((item) => {
    temp[item[id]] = item
  })
  for (const i in temp) {
    if (temp[i][pid] === '' || temp[i][pid] === null || temp[i][pid] === undefined) {
      temp[i][pid] = 0
    }
    if (temp[i][pid] !== 0 && temp[temp[i][pid]] !== undefined) {
      if (!temp[temp[i][pid]].children) {
        temp[temp[i][pid]].children = []
      }
      temp[temp[i][pid]].children.push(temp[i])
    } else {
      tree.push(temp[i])
    }
  }
  return tree
}
export function treeToList(tree) {
  const list = []
  const newTree = deepClone(tree)
  const loop = (data) => {
    data.forEach((item) => {
      list.push(item)
      if (item.children) {
        loop(item.children)
        delete item.children
      }
    })
  }
  loop(newTree)
  return list
}
export function treeToListData(tree) {
  const list = []
  const newTree = deepClone(tree)
  const loop = (data) => {
    data.forEach((item) => {
      list.push(item)
      if (item.childList) {
        loop(item.childList)
        delete item.childList
      }
    })
  }
  loop(newTree)
  return list
}
// 导出文件
export function downloadFile(res) {
  let fileName = getFileName(res.headers['content-disposition'])
  if (window.navigator.msSaveBlob) {
    window.navigator.msSaveBlob(new Blob([res.data], { type: res.headers['content-type'] }), fileName)
  } else {
    let url = window.URL.createObjectURL(new Blob([res.data], { type: res.headers['content-type'] }))
    let link = document.createElement('a')
    link.style.display = 'none'
    link.href = url
    link.setAttribute('download', fileName)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link) // 下载完成移除元素
    window.URL.revokeObjectURL(url) // 释放掉blob对象
  }
}
// 优化响应式
export function optimize(array, bind) {
  return array.reduce((acc, cur) => {
    for (const key of Object.keys(cur)) {
      // 将不包含的属性都进行非响应式优化
      if (bind.includes(key)) continue
      Object.defineProperty(cur, [key], { enumerable: false })
    }
    acc.push(cur)
    return acc
  }, [])
}
/**
 * 16进制转换rgb，并设置透明度
 * @param {Number} num
 * @param {Array} arr
 * @returns
 */
export function getColor(thisColor, thisOpacity) {
  var theColor = thisColor.toLowerCase()
  // 十六进制颜色值的正则表达式
  var r = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/
  // 如果是16进制颜色
  if (theColor && r.test(theColor)) {
    if (theColor.length === 4) {
      var sColorNew = '#'
      for (var i = 1; i < 4; i += 1) {
        sColorNew += theColor.slice(i, i + 1).concat(theColor.slice(i, i + 1))
      }
      theColor = sColorNew
    }
    // 处理六位的颜色值
    var sColorChange = []
    for (var i = 1; i < 7; i += 2) {
      sColorChange.push(parseInt('0x' + theColor.slice(i, i + 2)))
    }
    return 'rgba(' + sColorChange.join(',') + ',' + thisOpacity + ')'
  }
  return theColor
}
