<template>
  <div style="width: 100%; height: 100%;">
    <!-- <div class="control-btn-header">
      <div class="statistics">
        <div class="item pure">
          <span>资产总数</span>
          <span>{{ countData.totalAssets }}</span>
        </div>
        <div class="item pure">
          <span>闲置</span>
          <span>{{ countData.idleAssets }}</span>
        </div>
        <div class="item pure">
          <span>正常</span>
          <span>{{ countData.normalAssets }}</span>
        </div>
        <div class="item pure">
          <span>报废</span>
          <span>{{ countData.retiredAssets }}</span>
        </div>
        <div class="item pure">
          <span>停用</span>
          <span>{{ countData.deactivationAsset }}</span>
        </div>
        <div class="item pure">
          <span>待维修</span>
          <span>{{ countData.repairedAssets }}</span>
        </div>
        <div class="item pure">
          <span>转科中</span>
          <span>{{ countData.transferringAssets }}</span>
        </div>
      </div>
    </div> -->
    <div style=" height: 100%;">
      <div class="content">
        <!-- <el-row :gutter="16">
          <el-col :xs="24" :md="24" :lg="12" style="padding: 10px 5px 10px 0;">
            <div class="col" style="height: 100%;">
              <div class="title">
                <svg-icon name="right-arrow" />
                <span>资产使用年限</span>
              </div>
              <div ref="taskAnalysis" class="charts"></div>
            </div>
          </el-col>
          <el-col :xs="24" :md="24" :lg="12" style="padding: 10px 0 10px 5px;">
            <div class="col">
              <div class="title">
                <svg-icon name="right-arrow" />
                <span>专业类别</span>
              </div>
              <div id="workOdertTrendEcharts"></div>
            </div>
          </el-col>
        </el-row> -->
        <el-row :gutter="24" style="height: 100%;">
          <el-col :xs="24" :md="24" :lg="12" style="width: 100%;">
            <div class="col" style="height: 100%; width: 100%;">
              <div class="contentTable">
                  <div class="contentTable-main table-content">
              <el-table :data="tableData" :height="tableHeight">
                <el-table-column type="index" label="序号" width="50" align="center">
                  <template slot-scope="scope">
                    <span>{{ (pagination.current - 1) * pagination.size + scope.$index + 1 }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="资产名称" prop="" width="200px" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span class="color_blue" @click="ViewFn(scope.row)">
                      {{ scope.row.assetName }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column v-for="(column, index) in tableColumn" :key="index" :prop="column.prop" :label="column.label" :min-width="column.minWidth" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <table-render v-if="column.render" :sc="scope" :row="scope.row" :render="column.render"></table-render>
                    <div v-else-if="!column.formatter">
                      {{ scope.row[column.prop] }}
                    </div>
                    <div v-else>
                      {{ column.formatter(scope.row) }}
                    </div>
                  </template>
                </el-table-column>
              </el-table>
                 </div>
                </div>
                <div class="contentTable-footer">
              <el-pagination
                style="margin-top: 3px;"
                :current-page="pagination.current"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
                :page-size="pagination.size"
                :page-sizes="[15, 30, 50]"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              ></el-pagination>
                </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <!-- <div class="buttom">1</div> -->
    </div>
  </div>
</template>

<script>
import store from '@/store/index'
// import * as echarts from 'echarts'
import moment from 'moment'
import tableListMixin from '@/mixins/tableListMixin.js'
import * as echarts from 'echarts'
// let echarts = require('echarts/lib/echarts')
// require('echarts/lib/chart/bar')
// require('echarts/lib/component/tooltip')
// require('echarts/lib/component/graphic')
// require('echarts/lib/component/legend')
export default {
  name: 'property',
  mixins: [tableListMixin],

  props: {
    placeIds: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      moment,
      echartsData: [],
      flowCode: '', // 工单状态
      free1: '',
      orderStatisticsName: [],
      orderType: 'year',
      seriesData: [],
      tableData: [],
      pagination: {
        current: 1,
        size: 15
      },
      total: 0,
      countData: {},
      tableColumn: [],
      detailObj: {},
      dialogTitle: '',
      workOrderDetailCenterShow: false,
      iomsTableColumn: [
        {
          prop: 'assetCode',
          label: '资产编码'
        },
        {
          prop: 'professionalCategoryName',
          label: '专业类别'
        },
        {
          prop: 'assetModel',
          label: '型号'
        },
        {
          prop: 'assetStatusName',
          label: '使用状态'
        },
        {
          prop: 'centralizedDepartmentName',
          label: '归口部门'
        },
        {
          prop: 'startDate',
          label: '启用日期'
        }
      ],
      workTypeCode: ''
    }
  },
  // computed: {
  //   tableHeight() {
  //     return document.body.clientHeight - 660
  //   }
  // },
  watch: {
    '$store.state.settings.sidebarCollapse': {
      handler(val) {
        this.$nextTick(() => {
          let echartsDom = ['workOdertTypeEcharts', 'workOdertTrendEcharts']
          setTimeout(() => {
            echartsDom.forEach((item) => {
              echarts.init(document.getElementById(item)).resize()
            })
          }, 250)
        })
      },
      deep: true
    }
  },

  mounted() {
    // this.getReckonCount()
    // this.getWorkOderType()
    // this.getWorkOderTrend()
    this.getTableList()
  },
  methods: {
    getTableList() {
      let params = {
        regionCode: this.placeIds,
        currentPage: this.pagination.current,
        pageSize: this.pagination.size
      }

      this.$api.getAssetServiceList(params).then((res) => {
        this.tableColumn = this.iomsTableColumn
        this.tableData = res.data.list.forEach((item) => {
          if (item.startDate) {
            item.startDate = moment(item.startDate).format('YYYY-MM-DD')
          }
        })
        this.tableData = res.data.list
        this.total = res.data.sum
      })
    },
    // 资产使用年限
    getWorkOderType() {
      let params = {
        regionCode: this.placeIds
      }
      this.$api.getAssetsServiceLife(params).then((res) => {
        if (res.code == '200') {
          this.echartsData = res.data.array
          this.$nextTick(() => {
            this.setEcharts()
          })
        }
      })
    },
    setEcharts() {
      let myChart = echarts.init(this.$refs.taskAnalysis)
      let pieData = []
      this.echartsData.forEach((i) => {
        const item = {}
        item.value = i.value || 0
        item.name = i.name
        item.completion = i.proportion
        item.label = {}
        pieData.push(item)
      })
      pieData.sort((a, b) => a.value - b.name)
      if (pieData.length > 0) {
        pieData[0].label = {
          show: true,
          position: 'center',
          fontSize: 14,
          color: '#989898',
          formatter(params) {
            let text = params.data.name
            let value_format = params.data.value
            let proportion = params.data.completion
            return (text = `{time|${text}}\n ${value_format}件`)
          },
          rich: {
            time: {
              fontSize: 30,
              color: '#666',
              lineHeight: 35
            }
          }
        }
      }
      myChart.setOption({
        tooltip: {
          formatter(params) {
            let text = params.data.name
            let value_format = params.data.value
            let proportion = params.data.completion
            return (text = `${text}\n ${value_format}件`)
          }
        },
        legend: {
          type: 'scroll',
          orient: 'vertical',
          top: '30%',
          right: '7%',
          selected: {},
          formatter: function (name) {
            var oa = pieData
            for (var i = 0; i < pieData.length; i++) {
              if (name === oa[i].name) {
                return ' ' + name + '(' + oa[i].value + '件' + ')' + '' + oa[i].completion + ''
              }
            }
          }
        },
        series: [
          {
            name: '',
            type: 'pie',
            radius: ['50%', '65%'],
            avoidLabelOverlap: false,
            label: {
              normal: {
                show: false,
                position: 'center'
              }
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 14,
                color: '#989898',
                backgroundColor: '#fff',
                width: 135,
                formatter(params) {
                  let text = params.data.name
                  let value_format = params.data.value
                  let proportion = params.data.completion
                  return (text = `{time|${text}}\n ${value_format}件`)
                },
                rich: {
                  time: {
                    fontSize: 30,
                    color: '#666',
                    lineHeight: 35
                  }
                }
              }
            },
            labelLine: {
              show: false
            },
            data: pieData
          }
        ]
      })
    },
    getReckonCount() {
      let params = {
        regionCode: this.placeIds
      }
      this.$api.getAssetOverview(params).then((res) => {
        if (res.code == 200) {
          this.countData = res.data
        }
      })
    },
    changeFlowCode(val) {
      if (val == '30') {
        this.free1 = val
        this.flowCode = null
      } else {
        this.flowCode = val
        this.free1 = ''
      }
      this.getTableList()
    },
    ViewFn(row) {
      this.$router.push({
        path: '/detailsAssets',
        query: { row: row }
      })
    },
    getWorkOderTrend() {
      let params = {
        regionCode: this.placeIds
      }
      this.$api.getAssetClassification(params).then((res) => {
        if (res.code === '200') {
          const arr = res.data
          this.workOderTrendEchart(arr)
        }
      })
    },
    workOderTrendEchart(val) {
      const getchart = echarts.init(document.getElementById('workOdertTrendEcharts'))
      let arr = []
      val.forEach((item) => {
        let obj = {
          name: item.assetType,
          value: item.assetNumber
        }
        arr.push(obj)
      })

      const data = arr
      data.sort((a, b) => {
        return a.value - b.value
      })
      const nameList = Array.from(data, ({ name }) => name)
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          top: '4%',
          left: '4%',
          right: '5%',
          bottom: '5%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: nameList,
          axisLabel: {
            formatter: function (val) {
              return val
            }
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        yAxis: [
          {
            type: 'value'
          }
        ],
        series: [
          {
            type: 'bar',
            barWidth: 10,
            data: data,
            itemStyle: {
              color: '#3562db'
            }
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    handleSizeChange(val) {
      this.pagination.size = val
      this.getTableList()
    },
    handleCurrentChange(val) {
      this.pagination.current = val
      this.getTableList()
    }
  }
}
</script>

<style lang="scss" scoped>
.control-btn-header {
  background-color: #fff;

  & > div {
    display: flex;
    padding: 10px 5px;
  }
}

.content {
  height: 100%;

  ::v-deep .el-row {
    height: 100%;

    .el-col {
      height: 100%;
    }
  }
}

.buttom {
  height: 50%;
  background-color: #fff;
  border-radius: 5px;
}

.ipt {
  width: 200px;
  margin-right: 10px;
}

.col {
  background-color: #fff;
  height: 100%;
  border-radius: 5px;
  padding: 10px;
}

.charts,
#workOdertTrendEcharts {
  position: relative;
  width: 100%;
  height: 95%;
}

.title span {
  font-size: 15px;
}

.table-block-text {
  width: 80px;
  line-height: 24px;
  text-align: center;
  cursor: pointer;
  color: #fff;
}

.color_blue {
  color: #5188fc;
  cursor: pointer;
}

.statistics {
  display: flex;
  justify-content: space-between;
  height: 78px;
}

.statistics .item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-around;
  flex: 1;
  background-color: rgb(53 98 219 / 6%);
  margin-right: 8px;
  border-radius: 4px;
}

.statistics .item span:nth-child(2) {
  font-size: 18px;
  color: #3562db;
  font-weight: 700;
}

.statistics .hover-style {
  background-color: #fff;
  box-shadow: 0 0 10px 0 rgb(32 56 114 / 13%);
  border-bottom: 3px solid #3562db;
}

.statistics .pointer-style {
  cursor: pointer;
}

.statistics .pure {
  background-color: #fff;
}

.statistics .pure span:nth-child(2) {
  color: #121f3e;
}

.color_blue {
  color: #5188fc;
  cursor: pointer;
}

.contentTable {
  height: calc(100% - 35px);
  background: #fff;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  overflow: auto;

  .contentTable-main {
    flex: 1;
    overflow: auto;
  }

  .contentTable-footer {
    padding: 10px 0 0;
  }

  .alarmLevel {
    padding: 3px 6px;
    border-radius: 4px;
    color: #fff;
    line-height: 14px;
  }

  .alarmStatus {
    position: relative;
    display: inline-block;
    padding-left: 12px;

    .alarmStatusIcon {
      display: inline-block;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 8px;
      height: 8px;
      border-radius: 100%;
    }
  }

  .collectIcon {
    font-size: 16px;
    margin-right: 4px;
  }
}
</style>
