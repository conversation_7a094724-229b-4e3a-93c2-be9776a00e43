<template>
  <div class="main-container">
    <div class="whole">
      <div class="page-title">问卷预览</div>
      <el-row style="border-bottom: 1px solid #d8dee7;">
        <HeaderButton :btnName="btnName" />
      </el-row>
      <div class="question-main-container">
        <el-row :gutter="10">
          <el-col :xs="1" :sm="1" :md="2" :lg="4" :xl="4">
            <div class="grid-content"></div>
          </el-col>
          <el-col :xs="22" :sm="22" :md="20" :lg="16" :xl="16">
            <div class="question-title">{{ questionPreviewList.name }}</div>
            <div class="question-content">
              <div v-for="qSubject in questionsCopy" :key="qSubject.id" class="preview-container">
                <component
                  :is="questionPreview[qSubject.type]"
                  ref="child"
                  :previewOption="qSubject"
                  :index="qSubject.indexCopy"
                  :pathName="pathName"
                  :diff="diff"
                  :isQuestionNum="questionPreviewList.isQuestionNum == 1"
                />
              </div>
            </div>
          </el-col>
          <!-- <el-col :xs="1" :sm="1" :md="2" :lg="4" :xl="4">
            <div class="grid-content">
              <el-button type="primary" @click="submitQuestinAnswer">提交</el-button>
            </div>
          </el-col> -->
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import HeaderButton from '../questionDesign/component/HeaderButton/HeaderButton.vue'
// import PreviewMatrix from "../questionDesign/component/QuestionPreview/PreviewMatrix/previewMatrix";
import PreviewFillBlank from '../questionDesign/component/QuestionPreview/PreviewFillBlank/PreviewFillBlank'
import PreviewRadio from '../questionDesign/component/QuestionPreview/PreviewRadio/PreviewRadio'
import PreviewCheckBox from '../questionDesign/component/QuestionPreview/PreviewCheckBox/PreviewCheckBox'
import PreviewSelect from '../questionDesign/component/QuestionPreview/PreviewSelect/PreviewSelect'
// import PreviewMulSelect from "../questionDesign/component/QuestionPreview/PreviewMulSelect/PreviewMulSelect";
// import PreviewSort from "../questionDesign/component/QuestionPreview/PreviewSort/PreviewSort";
// import PreviewParagraph from "../questionDesign/component/QuestionPreview/PreviewParagraph/PreviewParagraph";
// import PreviewCard from '../components/PreviewCard/PreviewCard'
import utils from '../utils/utils'
export default {
  components: {
    HeaderButton,
    // PreviewMatrix,
    PreviewFillBlank,
    PreviewRadio,
    PreviewCheckBox,
    PreviewSelect
    // PreviewMulSelect,
    // PreviewSort,
    // PreviewParagraph
  },
  data() {
    return {
      isCheck: false,
      flags: false,
      currentParams: '',
      diff: 'preview',
      templateMode: {
        checkBox: 'checkBoxFun',
        singleChoice: 'singleChoiceFun',
        select: 'selectFun'
      },
      questionPreview: {
        radio: 'PreviewRadio',
        checkbox: 'PreviewCheckBox',
        input: 'PreviewFillBlank',
        array: 'PreviewMatrix',
        paragraph: 'PreviewParagraph',
        sort: 'PreviewSort',
        select: 'PreviewSelect',
        nd_select: 'PreviewMulSelect'
      },
      andConfig: {
        true: 'addDoSetAndRelation', // add关系添加
        false: 'delDoSetAndRelation' // 取消选中
      },
      config: {
        1: 'doSetOrRelation', // 执行题目逻辑设计时或or的逻辑
        2: 'doSetAndRelation' // 执行题目逻辑设计时且and的逻辑
      },
      // （单选、多选、下拉）结果集
      judgmentList: [],
      questionPreviewList: {}, // 当前问卷的题目列表
      questionsCopy: [], // 需要展示的数据结果集
      questionLogicArray: [], // 保存需要有逻辑处理的题 结果集
      btnName: 'goBack', // 右上角的功能按钮，goBack表示返回
      pathName: {
        check: {
          isShowSubjectType: false,
          isDisable: true,
          isQuestionNum: true,
          isSetDefaultValue: true
        },
        preview: {
          isShowSubjectType: true,
          isDisable: false,
          isQuestionNum: true,
          isSetDefaultValue: false
        },
        title: {
          isShowSubjectType: true,
          isDisable: false,
          isQuestionNum: true,
          isSetDefaultValue: false
        }
      }
    }
  },
  mounted() {
    this.getCurrentQuestionAllSubject()
    this.openTime = moment(this.obtainEast8Date()).format('YYYY-MM-DD HH:mm:ss')
  },
  methods: {
    obtainEast8Date() {
      var timezone = 8 // 目标时区时间，东八区   东时区正数 西市区负数
      var offset_GMT = new Date().getTimezoneOffset() // 本地时间和格林威治的时间差，单位为分钟
      var nowDate = new Date().getTime() // 本地时间距 1970 年 1 月 1 日午夜（GMT 时间）之间的毫秒数
      var targetDate = new Date(nowDate + offset_GMT * 60 * 1000 + timezone * 60 * 60 * 1000)
      return targetDate
    },
    // 提交
    submitQuestinAnswer() {
      const childList = this.$refs.child
      console.log(childList, 'childList')
      let answerRecords = []
      let isBreak = []
      for (let index = 0; index < childList.length; index++) {
        const item = childList[index]
        // 调用子组件的doValided方法进行题目验证，子组件必须有doValided和checkValided方法，详见子组件
        const isValided = item.doValided()
        if (item.$vnode.tag.indexOf('Paragraph') < 0) {
          if (isValided) {
            isBreak.push(index)
          } else {
            answerRecords = answerRecords.concat(item.answerVal)
          }
        }
      }
      let queList = this.questionPreviewList.questions
      this.flags = false
      if (queList && queList.length) {
        queList.every((item, index) => {
          if (item.lengthArr && item.lengthArr.length) {
            if ((item.maxSelect !== '' && item.maxSelect !== item.minSelect && item.lengthArr.length > item.maxSelect) || item.lengthArr.length < item.minSelect) {
              console.log('不规范呀呀呀')
              this.isCheck = true
              this.$set(item, 'checksed', true)
              this.flags = true
              // return false;
            } else {
              this.isCheck = false
              this.$set(item, 'checksed', false)
              console.log('规范')
              this.flags = false
              return true
            }
          }
        })
      }
      if (isBreak.length === 0) {
        // 当前问卷题目均为非必答题时，阻止未做答任何题目点击提交
        if (answerRecords.length === 0) {
          this.$message.error('您未作答！请您作答任意一个题均可')
          return
        }
        // const loginInfo = localStorage.getItem('loginInfo')
        // const currentLoginInfo = loginInfo ? JSON.parse(loginInfo) : {}
        const params = {
          questionId: localStorage.getItem('questId'), // 当前问卷ID
          openTime: this.openTime, // 问卷开始做答的时间
          subTime: moment(this.obtainEast8Date()).format('YYYY-MM-DD HH:mm:ss'), // 问卷做答结束的时间
          subName: '董文杰6', // 当前答卷人
          deviceType: 'phone',
          userId: '5662b1b798af0c716f2673aac6d39e44', // 当前答卷人的ID
          answerRecords: JSON.stringify(answerRecords),
          hospitalCode: 'BJSJTYY',
          unitCode: 'BJSYGJ'
        }
        if (this.signatureImg) params.signature = this.signatureImg
        this.currentParams = params
        this.saveApi()
      }
    },
    saveApi() {
      let params = this.currentParams
      this.$api.saveAnswer(params).then((res) => {
        if (res.status == 200) {
          this.$message({
            message: res.message,
            type: 'success'
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 答题结束
    getCurrentQuestionAllSubject() {
      const params = {
        questionId: localStorage.getItem('questId'),
        userId: this.$store.state.user.userInfo.userId
      }
      this.$api.getPaperQuestions(params).then((res) => {
        if (res.status == 200) {
          this.questionPreviewList = res.data
          this.transformQuestionListData(res.data)
          var indexCount = 0
          this.arr = res.data.questions
          for (let index = 0; index < this.arr.length; index++) {
            const element = this.arr[index]
            if (element.type == 'paragraph') {
              indexCount = indexCount + 1
            } else {
              element['indexCopy'] = index - indexCount
            }
          }
        } else {
          this.$message.error(res.message)
        }
      })
    },
    transformQuestionListData(data) {
      var arr = []
      data.questions.forEach((item) => {
        if (!item.hasLogic) {
          this.questionsCopy.push(item)
        } else {
          var obj = JSON.parse(JSON.stringify(item))
          obj.dataArr = []
          for (let index = 0; index < item.questionLogics.length; index++) {
            const element = item.questionLogics[index]
            obj.dataArr.push({
              ...element,
              logicType: item.logicType
            })
          }
          arr.push(obj)
        }
      })
      console.log(this.questionsCopy)
      this.questionLogicArray = arr
    },
    // 单选起调函数
    redioFun(obj) {
      var index = this.judgmentList.indexOf(obj.old)
      if (index > -1) {
        this.judgmentList.splice(index, 1)
      }
      this.judgmentList.push(obj.news)
      this.addDoSetAndRelation(obj.news)
      this.delDoSetAndRelation(obj.old)
    },
    // 下拉框起调函数
    selectFun(obj) {
      var index = this.judgmentList.indexOf(obj.old)
      if (index > -1) {
        this.judgmentList.splice(index, 1)
      }
      this.judgmentList.push(obj.news)
      this.addDoSetAndRelation(obj.news)
      this.delDoSetAndRelation(obj.old)
    },
    // 多选起调函数
    checkBoxFun(obj, val) {
      if (val) {
        this.judgmentList.push(obj.id)
      } else {
        var index = this.judgmentList.indexOf(obj.id)
        if (index > -1) {
          this.judgmentList.splice(index, 1)
        }
      }
      this[this.andConfig[val]](obj.id)
    },
    // 保存答案 结果集  （模板模式住主函数）
    addAnswer(str, obj, val) {
      this[str](obj, val)
    },
    // 且关系处理 删除函数
    delQuestionsCopy(element) {
      for (var a = 0; a < this.questionsCopy.length; a++) {
        let item = this.questionsCopy[a]
        if (element.id == item.id) {
          this.questionsCopy.splice(a, 1)
          return
        }
      }
    },
    // 取消展示
    delDoSetAndRelation(id) {
      var arr = this.questionLogicArray
      for (var a = 0; a < arr.length; a++) {
        var dataArr = arr[a].dataArr
        var obj = this[this.config[arr[a].logicType]](dataArr, id)
        if (obj.switchFlag && !obj.orAndFlag) {
          this.delQuestionsCopy(arr[a])
        }
      }
    },
    // 显示添加
    addDoSetAndRelation(id) {
      var arr = this.questionLogicArray
      for (var a = 0; a < arr.length; a++) {
        var dataArr = arr[a].dataArr
        var obj = this[this.config[arr[a].logicType]](dataArr, id)
        if (obj.switchFlag && obj.orAndFlag) {
          this.addQuestionsCopy(arr[a])
        }
      }
    },
    // or逻辑处理函数
    doSetOrRelation(dataArr, id) {
      var switchFlag = true
      var orAndFlag = false
      for (var b = 0; b < dataArr.length; b++) {
        var flag = this.existence(dataArr[b].optionId)
        if (!orAndFlag) {
          orAndFlag = flag
        }
        if (dataArr[b].optionId == id) {
          switchFlag = true
        }
      }
      return { switchFlag: switchFlag, orAndFlag: orAndFlag }
    },
    // and逻辑处理函数
    doSetAndRelation(dataArr, id) {
      var switchFlag = false
      var orAndFlag = true
      for (var b = 0; b < dataArr.length; b++) {
        var flag = this.existence(dataArr[b].optionId)
        if (orAndFlag) {
          orAndFlag = flag
        }
        if (dataArr[b].optionId == id) {
          switchFlag = true
        }
      }
      return { switchFlag: switchFlag, orAndFlag: orAndFlag }
    },
    // 数组添加函数
    addQuestionsCopy(item) {
      var flag = -1
      for (let index = 0; index < this.questionsCopy.length; index++) {
        const element = this.questionsCopy[index]
        if (item.id == element.id) {
          flag = -1
          break
        }
        if (element.sort > item.sort) {
          flag = index
          break
        }
      }
      if (flag != -1) {
        this.questionsCopy.splice(flag, 0, item)
      }
    },
    // 存在判断
    existence(id) {
      return this.judgmentList.includes(id)
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
.main-container {
  height: 100%;
  padding: 15px;
  margin: 0 !important;
  .wholi {
    width: 100%;
    height: 100%;
    border-radius: 5px;
    background-color: #fff !important;
  }
  .page-title {
    height: 50px;
    line-height: 50px;
    color: #606266;
    font-size: 14px;
    padding-left: 25px;
    border-bottom: 1px solid #d8dee7;
  }
  .question-title {
    background-color: #5076df;
    height: 50px;
    line-height: 50px;
    text-align: center;
    font-size: 14px;
    color: #fff;
  }
  .question-main-container {
    background-color: #fff;
    height: calc(100% - 110px);
    .el-row,
    .el-col {
      height: 100%;
    }
    .grid-content {
      min-height: 36px;
    }
    .question-content {
      margin-top: 10px;
      background-color: #fff;
      // height: 100%;
      height: calc(100% - 80px);
      overflow-y: auto;
      padding: 20px;
    }
    .preview-container {
      padding: 15px;
      background-color: #fff;
      border-bottom: 1px solid #d8dee7;
    }
  }
}
.main-container {
  height: 100%;
  padding: 15px;
  margin: 0 !important;
  .whole {
    width: 100%;
    height: 100%;
    border-radius: 5px;
    background-color: #fff !important;
  }
}
</style>
