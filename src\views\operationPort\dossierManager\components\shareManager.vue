<template>
  <div class="tab_content">
    <div class="right-heade">
      <el-input v-model="formData.archiveInfo" placeholder="文件名/文号/摘要" suffix-icon="el-icon-search" clearable />
      <el-button type="primary" @click="search">查询</el-button>
      <el-button type="primary" plain @click="reset">重置</el-button>
      <div>
        <el-button type="danger" size="small" @click="handleCancelSharing">取消共享</el-button>
      </div>
    </div>
    <div class="right-content">
      <div class="table-content">
        <TablePage
          ref="tablePage"
          v-loading="tableLoading"
          v-scrollHideTooltip
          :tableColumn="tableColumn"
          :data="tableData"
          border
          height="100%"
          :showPage="true"
          :pageData="pageData"
          :pageProps="{
            page: 'current',
            pageSize: 'size',
            total: 'total'
          }"
          @pagination="paginationChange"
          @selection-change="handleSelectionChange"
        >
        </TablePage>
      </div>
    </div>
  </div>
</template>
<script lang="jsx">
import tableListMixin from '@/mixins/tableListMixin.js'
import dictMixin from '@/views/operationPort/contractManagement/mixins/index.js'
export default {
  mixins: [tableListMixin, dictMixin],
  data() {
    return {
      tableColumn: [
        {
          type: 'selection',
          align: 'center',
          width: '50'
        },
        {
          prop: 'archiveName',
          label: '文件名称',
          align: 'center'
        },
        {
          prop: 'archiveNumber',
          label: '文号',
          align: 'center'
        },
        {
          prop: 'folderName',
          label: '文件夹',
          align: 'center'
        },
        {
          prop: 'archiveModel',
          label: '分类',
          align: 'center',
          render: (h, row) => {
            const item = this.classification.find((item) => item.value === row.row.archiveModel)
            return item ? item.label : ''
          }
        },
        {
          prop: 'archiveDate',
          label: '成文日期',
          align: 'center'
        },
        {
          prop: 'shareDeptName',
          label: '共享部门',
          align: 'center'
        },
        {
          prop: 'shareMemberName',
          label: '共享成员',
          align: 'center'
        },
        {
          prop: 'effectiveDate',
          label: '有效期限',
          align: 'center'
        }
      ],
      formData: {
        archiveInfo: ''
      },
      tableLoading: false,
      tableData: [],
      pageData: {
        current: 1,
        size: 15,
        total: 0
      },
      checkedData: []
    }
  },
  created() {
    this.handleQueryTablelist()
  },
  methods: {
    handleQueryTablelist() {
      const parmas = {
        ...this.formData,
        ...this.pageData,
        archiveType: '1'
      }
      this.$api.fileManagement.shareManagerListByPage(parmas).then((res) => {
        this.tableData = res.data.records
        this.pageData.total = res.data.total
      })
    },
    search() {
      this.pageData.current = 1
      this.handleQueryTablelist()
    },
    reset() {
      Object.keys(this.formData).forEach((key) => {
        this.formData[key] = ''
      })
      this.search()
    },
    handleSelectionChange(e) {
      this.checkedData = e
    },
    // 分页事件
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getTableList()
    },
    handleCancelSharing() {
      const rows = this.checkedData
      if (rows.length < 1) {
        this.$message.warning('请至少选择一条数据！')
        return
      }
      this.$confirm('取消后文档在阅读空间不可见', '取消共享', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      })
        .then(() => {
          const idList = rows.map((item) => item.archiveShareId)
          this.$api.fileManagement.cancelShare({ idList }).then(() => {
            this.$message.success('取消成功')
            this.handleQueryTablelist()
          })
        })
        .catch(() => {})
    }
  }
}
</script>
<style lang="scss" scoped>
.tab_content {
  width: 100%;
  height: calc(100% - 40px);
  .right-heade {
    border-radius: 4px;
    ::v-deep .el-input {
      width: 200px;
    }
    & > div {
      margin-right: 10px;
      margin-top: 10px;
    }
  }
  .right-content {
    background: #fff;
    border-radius: 4px;
    overflow: hidden;
    width: 100%;
    height: calc(100% - 50px);
    margin-top: 16px;
    .btns-group {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
      & > div {
        display: flex;
      }
      .btns-group-control {
        > div {
          margin-left: 10px;
        }
        // & > div, & > button {
        //   margin-right: 10px;
        // }
      }
    }
    .table-content {
      height: calc(100% - 85px);
      ::v-deep .el-table .el-table__body-wrapper td.el-table__cell {
        border-right: 1px solid #ededf5;
        .tooltip-over-td {
          display: block;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
}
</style>
