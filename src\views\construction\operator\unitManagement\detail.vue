<template>
  <pageContainer footer>
    <div slot="header" class="header_content">
      <div @click="goBack">
        <i class="el-icon-arrow-left"></i>
        <span class="title_text">单位详情</span>
      </div>
    </div>
    <div slot="content" class="page_content">
      <div class="content_top">
        <el-descriptions>
          <div slot="title" style="display: flex; align-items: center">
            <span style="margin-right: 10px">{{ detail.companyName }}</span>
            <el-tag size="small" type="success">{{ detail.enableStatus == '0' ? '启用' : '禁用' }}</el-tag>
          </div>
          <el-descriptions-item label="信用代码">{{ detail.creditCode }}</el-descriptions-item>
          <el-descriptions-item label="法人姓名">{{ detail.legalPersonName }} </el-descriptions-item>
          <el-descriptions-item label="手机号">{{ detail.phone }} </el-descriptions-item>
        </el-descriptions>
      </div>
      <div class="content_bottom">
        <el-tabs v-model="activeName" @tab-click="handleTabsClick">
          <el-tab-pane label="营业执照" name="1">
            <div style="padding: 10px; box-sizing: border-box" v-loading="paneLoading">
              <el-form ref="form" :model="detail" label-width="130px" :disabled="disabled" :rules="rules">
                <el-row :gutter="10">
                  <el-col :span="8">
                    <el-form-item label="单位名称" prop="companyName">
                      <el-input v-model="detail.companyName" placeholder="请输入单位名称" maxlength="50"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="信用代码" prop="creditCode">
                      <el-input v-model="detail.creditCode" placeholder="请输入单位名称" maxlength="50"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="法人姓名" prop="companyName">
                      <el-input v-model="detail.legalPersonName" placeholder="请输入单位名称" maxlength="50"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="10">
                  <el-col :span="8">
                    <el-form-item label="手机号码" prop="phone">
                      <el-input v-model="detail.phone" placeholder="请输入手机号码" maxlength="11"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="启用状态" prop="enableStatus">
                      <el-radio-group v-model="detail.enableStatus">
                        <el-radio label="0">启用</el-radio>
                        <el-radio label="1">禁用</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="10">
                  <el-col :span="24">
                    <el-form-item label="备注">
                      <el-input v-model="detail.remark" type="textarea" placeholder="请输入备注" show-word-limit maxlength="200"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="10">
                  <el-col :span="8" v-if="idCardImageRInfo.isShow">
                    <el-form-item :label="idCardImageRInfo.label" :required="idCardImageRInfo.required">
                      <el-upload
                        action=""
                        list-type="picture-card"
                        :file-list="idCardImageRList"
                        :accept="uploadAcceptDict['picture'].type"
                        :limit="1"
                        :disabled="disabled"
                        :http-request="(file) => httpRequset(file, 'idCardImageR')"
                        :on-remove="(file, fileList) => handleRemove(fileList, 'idCardImageR')"
                        :on-preview="handlePreview"
                        :on-exceed="exceedMeth"
                      >
                        <i class="el-icon-plus"><br /></i>
                        <div slot="tip" class="el-upload__tip">支持{{ uploadAcceptDict['picture'].type }},且大小不超过{{ 5 }}M</div>
                      </el-upload>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" v-if="idCardImageSInfo.isShow">
                    <el-form-item :label="idCardImageSInfo.label" :required="idCardImageSInfo.required">
                      <el-upload
                        action=""
                        list-type="picture-card"
                        :file-list="idCardImageSList"
                        :accept="uploadAcceptDict['picture'].type"
                        :limit="1"
                        :disabled="disabled"
                        :before-upload="beforeAvatarUpload"
                        :http-request="(file) => httpRequset(file, 'idCardImageS')"
                        :on-remove="(file, fileList) => handleRemove(fileList, 'idCardImageS')"
                        :on-preview="handlePreview"
                        :on-exceed="exceedMeth"
                      >
                        <i class="el-icon-plus"><br /></i>
                        <div slot="tip" class="el-upload__tip">支持{{ uploadAcceptDict['picture'].type }},且大小不超过{{ 20 }}M</div>
                      </el-upload>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="10" v-if="businessImageInfo.isShow">
                  <el-col :span="24">
                    <el-form-item :label="businessImageInfo.label" :required="businessImageInfo.required">
                      <el-upload
                        action=""
                        list-type="picture-card"
                        :file-list="businessImageList"
                        :accept="uploadAcceptDict['picture'].type"
                        :limit="1"
                        :disabled="disabled"
                        :before-upload="beforeAvatarUpload"
                        :http-request="(file) => httpRequset(file, 'businessImage')"
                        :on-remove="(file, fileList) => handleRemove(fileList, 'businessImage')"
                        :on-preview="handlePreview"
                        :on-exceed="exceedMeth"
                      >
                        <i class="el-icon-plus"><br /></i>
                        <div slot="tip" class="el-upload__tip">支持{{ uploadAcceptDict['picture'].type }},且大小不超过{{ 20 }}M</div>
                      </el-upload>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
          </el-tab-pane>
          <el-tab-pane label="施工安全协议" name="2">
            <div class="fileContent" v-if="activeName == 2">
              <filesComponent :id="detail.id" :fieldName="'attachmentInfo'" />
            </div>
          </el-tab-pane>
          <el-tab-pane label="消防安全承诺" name="3">
            <div class="fileContent" v-if="activeName == 3">
              <filesComponent :id="detail.id" :fieldName="'fireControlInfo'" />
            </div>
          </el-tab-pane>
          <el-tab-pane label="其他" name="4">
            <div class="fileContent" v-if="activeName == 4">
              <filesComponent :id="detail.id" :fieldName="'elseFile'" />
            </div>
          </el-tab-pane>
          <el-tab-pane label="证照信息" name="5">
            <div class="fileContent" v-if="activeName == 5">
              <certificateComponent ref="certificateComponent" :disabled="disabled" :detail="detail" />
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      <Preview v-if="viewVisible" v-model="viewVisible" :list="fileList" />
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="goBack">关闭</el-button>
      <el-button type="primary" v-if="disabled && !['2', '3', '4'].includes(activeName)" @click="handEditClick">编辑</el-button>
      <el-button type="primary" v-if="!disabled && !['2', '3', '4'].includes(activeName)" @click="handSaveClick">保存</el-button>
    </div>
  </pageContainer>
</template>
<script lang="jsx">
import { uploadAcceptDict } from '@/util/dict.js'
import dayjs from 'dayjs'
export default {
  components: {
    Preview: () => import('../components/preview.vue'),
    filesComponent: () => import('./filesComponent.vue'),
    certificateComponent: () => import('./certificateComponent.vue'),
  },
  data() {
    return {
      uploadAcceptDict,
      rules: {
        companyName: [{ required: true, message: '请输入单位名称', trigger: 'blur' }],
        legalPersonName: [{ required: true, message: '请输入法人姓名', trigger: 'blur' }]
      },
      detail: {},
      activeName: '1',
      viewVisible: false,
      fileList: [],
      idCardImageRList: [],
      idCardImageSList: [],
      businessImageList: [],
      handleType: '',
      disabled: false,
      certificateList: [],
      paneLoading: false,
      idCardImageRInfo: {
        label: '法人身份证正面',
        isShow: true,
        required: false
      },
      idCardImageSInfo: {
        label: '法人身份证反面',
        isShow: true,
        required: false
      },
      businessImageInfo: {
        label: '营业执照',
        isShow: true,
        required: false
      },
    }
  },
  mounted() {
    this.getCertificateList()
    let { handType } = this.$route.query
    this.handleType = handType
    if (handType == 'view') {
      this.disabled = true
    } else {
      this.disabled = false
    }
    this.getDetail()
  },
  methods: {
    // 获取证照信息
    getCertificateList() {
      const params = {
        fileName: '',
        enableStatus: '',
        type: 1,
        page: 1,
        pageSize: 999999
      }
      this.$api
        .getConstructionCertificateList(params)
        .then((res) => {
          if (res.code === '200') {
            if (res.data.records && res.data.records.length) {
              let field = ['idCardImageR', 'idCardImageS', 'businessImage']
              field.forEach((item) => {
                let findData = res.data.records.find((i) => i.fileCode === item)
                if (findData) {
                  this[`${item}Info`].label = findData.fileName
                  this[`${item}Info`].isShow = findData.enableStatus === 1 ? true : findData.enableStatus === 0 ? false : true
                  this[`${item}Info`].required = findData.writed === 1 ? true : findData.writed === 0 ? false : true
                }
              })
            }
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '查询失败'))
    },
    // tabs点击
    handleTabsClick(){
      this.disabled = true
      if(['1','5'].includes(this.activeName)){
        this.idCardImageRList = []
        this.idCardImageSList = []
        this.businessImageList = []
        this.certificateList = []
        this.detail = {}
        this.getDetail()
      }
    },
    // 编辑
    handEditClick() {
      this.disabled = false
    },
    // 保存
    handSaveClick() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          delete this.detail.attachmentInfo
          delete this.detail.fireControlInfo
          delete this.detail.elseFile
          let arr = [];
          if(this.activeName == 1){
            if (this.idCardImageRInfo.required && this.idCardImageRInfo.isShow && !this.idCardImageRList.length) {
              return this.$message.error(`请上传${this.idCardImageRInfo.label}`)
            }
            if (this.idCardImageSInfo.required && this.idCardImageSInfo.isShow && !this.idCardImageSList.length) {
              return this.$message.error(`请上传${this.idCardImageSInfo.label}`)
            }
            if (this.businessImageInfo.required && this.businessImageInfo.isShow && !this.businessImageList.length) {
              return this.$message.error(`请上传${this.businessImageInfo.label}`)
            }
          }
          if(this.activeName == 5){
           let certificateList = this.$refs.certificateComponent.certificateList
            console.log(certificateList);
            if(certificateList.length){
              let status = certificateList.filter((v) => v.writed == 1).every((item) => item.url.length)
            if (!status) {
              return this.$message.error('请完善证照信息')
            }
            certificateList.forEach((item) => {
              if (item.url.length) {
                arr.push({
                  fileCode: item.fileCode,
                  fileName: item.fileName,
                  url: JSON.stringify(
                    item.url.map((i) => {
                      return {
                        url: i.uploadPath,
                        name: i.name
                      }
                    })
                  )
                })
              }
            })
            }
          }
          this.$api.editConstructionUnit({ ...this.detail,fileAttachment: arr.length ? JSON.stringify(arr) : '', platForm: 1 }).then((res) => {
            if (res.code == 200) {
              this.$message.success('修改成功')
              this.disabled = true
              this.businessImageList = []
              this.idCardImageRList = []
              this.idCardImageSList = []
              this.getDetail()
            } else {
              this.$message.error(res.msg || res.message)
            }
          })
        }
      })
    },
    // 身份证图片、营业执照图片放大查看
    handlePreview(file) {
      this.showPic(file.url)
    },
    getDetail() {
      this.paneLoading = true
      this.$api.unitConstructionDetail({ id: this.$route.query.id }).then((res) => {
        this.paneLoading = false
        if (res.code == 200) {
          if (res.data.businessImage) {
            res.data.businessImage.split(',').forEach((el) => {
              if (el) {
                this.businessImageList.push({
                  name: '',
                  url: this.$tools.imgUrlTranslation(el),
                  uploadPath: el
                })
              }
            })
          }
          if (res.data.idCardImageR) {
            res.data.idCardImageR.split(',').forEach((el) => {
              if (el) {
                this.idCardImageRList.push({
                  name: '',
                  url: this.$tools.imgUrlTranslation(el),
                  uploadPath: el
                })
              }
            })
          }
          if (res.data.idCardImageS) {
            res.data.idCardImageS.split(',').forEach((el) => {
              if (el) {
                this.idCardImageSList.push({
                  name: '',
                  url: this.$tools.imgUrlTranslation(el),
                  uploadPath: el
                })
              }
            })
          }
          let arr = ['attachmentInfo', 'fireControlInfo', 'elseFile']
          arr.forEach((key) => {
            if (res.data[key]) {
              res.data[key] = JSON.parse(res.data[key])
              res.data[key].forEach((el) => {
                el.name = el.attachment || el.name
                el.url = this.$tools.imgUrlTranslation(el.url)
                el.uploadPath = el.url
              })
            } else {
              res.data[key] = []
            }
          })
          if (res.data.fileAttachment) {
            let arr = JSON.parse(res.data.fileAttachment)
            arr.forEach((item) => {
              if (item.url) {
                item.url = JSON.parse(item.url).map((i) => {
                  return {
                    name: i.name,
                    url: this.$tools.imgUrlTranslation(i.url),
                    uploadPath: i.url
                  }
                })
              }
            })
            this.certificateList = arr
          }
          this.detail = res.data
        } else {
          this.$message.error(res.msg || res.message)
        }
      }).catch(()=>{}).finally(()=>{
            this.paneLoading = false
          })
    },
    // 图片上传大小校验
    beforeAvatarUpload(file) {
      const fileSize = file.size / 1024 / 1024 < 20
      if (!fileSize) {
        this.$message.error('上传图片大小不能超过 20MB!')
      }
      return fileSize
    },
    exceedMeth() {
      this.$message({
        type: 'info',
        message: '最多上传1个文件'
      })
    },
    // 文件、图片上传
    httpRequset(file, type) {
      this.$api.constructionUpload(__PATH.SPACE_API, 'SecurityLedger/upload', file).then((res) => {
        if (res.code == 200) {
          let obj = {
            attachment: res.data.name,
            name: res.data.name,
            createBy: this.$store.state.user.userInfo.user.staffName,
            url: res.data.picUrl,
            uploadPath: res.data.fileKey,
            createDate: dayjs().format('YYYY-MM-DD HH:mm:ss')
          }
          this[`${type}List`].push(obj)
          if (type == 'businessImage' || type == 'idCardImageR' || type == 'idCardImageS') {
            this.detail[type] = this[`${type}List`].map((v) => v.uploadPath).join(',')
          } else {
            this.detail[type] = JSON.stringify(this[`${type}List`])
          }
        }
      })
    },
    // 文件移除
    handleRemove(fileList, key) {
      this[`${key}List`] = fileList
      if (key == 'businessImage' || key == 'idCardImageR' || key == 'idCardImageS') {
        this.detail[key] = this[`${key}List`].map((v) => v.uploadPath).join(',') || ''
      } else {
        this.detail[key] = JSON.stringify(this[`${key}List`]) || ''
      }
    },
    // 放大查看图片
    showPic(item) {
      this.viewVisible = true
      this.fileList = [item]
    },
    // 下载文件
    downFile(url) {
      window.open(url)
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>
<style lang="scss" scoped>
.header_content {
  width: 100%;
  height: 60px;
  line-height: 60px;
  padding: 0 16px;
  border-bottom: 1px solid #ccc;
  font-size: 18px;
  > div {
    width: 100px;
    cursor: pointer;
  }
}
.page_content {
  height: 100%;
  width: 100%;
  background: #fff;
  padding: 16px;
  box-sizing: border-box;
  .content_bottom {
    height: calc(100% - 85px);
    .el-tabs {
      height: 100%;
    }
  }
}
.fileContent {
  height: 100%;
  box-sizing: border-box;
  padding: 10px;
}
::v-deep .el-tabs__content {
  height: calc(100% - 40px);
  overflow-y: auto;
  .el-tab-pane {
    height: 100%;
  }
}
::v-deep .el-upload-list__item {
  transition: none !important; /* 禁用动画 */
}
</style>
