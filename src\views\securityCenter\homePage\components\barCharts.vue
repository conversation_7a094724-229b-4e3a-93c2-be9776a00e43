<template>
  <div ref="riskTypeCharts" style="height: 100%; width: 100%;"></div>
</template>

<script>
export default {
  name: 'barCharts',
  props: {
    barData: {
      type: Object,
      default() {
        return {}
      }
    },
    // eslint-disable-next-line vue/require-prop-type-constructor
    deep: true
  },
  data() {
    return {
      data: ''
    }
  },
  watch: {
    barData(val) {
      this.data = val
      this.getCharts()
    }
  },
  methods: {
    getCharts() {
      let riskTypeCharts = echarts.init(this.$refs.riskTypeCharts)
      const option = {
        color: ['#3398DB'],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        grid: {
          top: '10%',
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: this.data.xAxis,
            // boundaryGap: [0, 0.01],
            axisLabel: {
              show: true,
              rotate: 40,
              textStyle: {
                color: '#909399' // 更改坐标轴文字颜色
              }
            },
            axisLine: {
              lineStyle: {
                color: '#D8DEE7' // 更改坐标轴颜色
              }
            }
            // 网格样式
            // splitLine: {
            //   show: false,
            //   lineStyle: {
            //     color: ['#f5f5f5'],
            //     width: 1,
            //     type: 'dashed'
            //   }
            // },
            // 去除x轴上的刻度线
            // axisTick: {
            //   show: false
            // }
          }
        ],
        yAxis: [
          {
            type: 'value',
            axisLabel: {
              formatter: '{value}',
              show: true,
              textStyle: {
                color: '#909399' // 更改坐标轴文字颜色
              }
            },
            // 控制y轴线是否显示
            axisLine: {
              show: false
              // lineStyle: {
              //   color: '#D8DEE7' // 更改坐标轴颜色
              // }
            },
            // 去除y轴上的刻度线
            axisTick: {
              show: false
            }
            // 网格样式
            // splitLine: {
            //   show: true,
            //   lineStyle: {
            //     color: ['#f5f5f5'],
            //     width: 1,
            //     type: 'dashed'
            //   }
            // }
          }
        ],
        series: [
          {
            name: '',
            type: 'bar',
            barWidth: '20%',
            data: this.data.list
          }
        ]
      }
      riskTypeCharts.setOption(option)
      riskTypeCharts.on('click', (params) => {
        this.$emit('getBarType', params.data.xAxis)
      })
      riskTypeCharts.clear()
      riskTypeCharts.setOption(option) // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        riskTypeCharts.resize()
      })
    }
  }
}
</script>

<style></style>
