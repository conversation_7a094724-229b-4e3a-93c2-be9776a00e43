<template>
  <div style="height: 100%;padding: 10px" class="drag_class">
    <div class="card_box_title card_box_short_bg">电梯品牌分布</div>
    <div v-if="!elevatorBrandShow" class="echart-null">
      <img src="@/assets/images/null.png" alt="" />
      <div>暂无数据~</div>
    </div>
    <div v-else style="width: 100%; height: calc(100% - 31px)">
      <div id="elevatorBrand" ref="elevatorBrand"></div>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
export default {
  data() {
    return {
      elevatorBrandShow: false,
      elevatorBrandData: [],
      timer: null
    }
  },
  mounted() {
    this.getIaasStatistics()
    this.scheduledTasks()
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
  },
  methods: {
    scheduledTasks() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
      this.timer = setInterval(() => {
        this.getIaasStatistics()
      }, 30000)
    },
    echartsResize() {
      this.$nextTick(() => {
        setTimeout(() => {
          if (document.getElementById('elevatorBrand')) {
            echarts.init(document.getElementById('elevatorBrand')).resize()
          }
        }, 50)
      })
    },
    getIaasStatistics() {
      this.$api.getIaasStatistics({}).then((res) => {
        if (res.code === '200') {
          // 获取电梯品牌数据
          if (res.data.hasOwnProperty('brand')) {
            this.elevatorBrandShow = true
            this.$nextTick(() => {
              this.setElevatorBrandEcharts(res.data.brand)
            })
          } else {
            this.elevatorBrandShow = false
          }
        }
      })
    },
    // 电梯品牌数据echarts
    setElevatorBrandEcharts(data) {
      const getchart = echarts.init(document.getElementById('elevatorBrand'))
      const sum = data.totalNum
      const gap = (6 * sum) / 100
      const pieData = []
      const gapData = {
        name: '',
        value: gap,
        itemStyle: {
          color: 'transparent'
        }
      }
      for (let i = 0; i < data.brandPic.length; i++) {
        pieData.push({
          name: data.brandPic[i].assetBrand,
          value: data.brandPic[i].brandNum,
          itemStyle: {
            normal: {}
          }
        })
        data.brandPic > 1 ? pieData.push(gapData) : ''
      }
      // const nameList = pieData.map((item) => item.name).filter((item) => item)
      // var objData = this.array2obj(pieData, 'name')
      let option = {
        tooltip: {
          trigger: 'item',
          formatter: function (params) {
            if (params.name) {
              return params.marker + params.name + ' : ' + params.value + '    (' + ((params.value / sum) * 100).toFixed(2) + '%)'
            }
          }
        },
        title: [
          {
            text: sum,
            left: '48%',
            top: '36%',
            textAlign: 'center',
            textStyle: {
              fontSize: '24',
              fontWeight: '600',
              color: '#FFF',
              textAlign: 'center'
            }
          },
          {
            text: '总数',
            x: '47%',
            y: '52%',
            textAlign: 'center',
            textStyle: {
              fontSize: '14',
              fontWeight: '400',
              color: '#A3A9AD',
              textAlign: 'center'
            }
          }
        ],
        // legend: {
        //   type: 'scroll',
        //   orient: 'vertical',
        //   top: '70%',
        //   x: 'center',
        //   pageIconColor: '#5188fc', // 激活的分页按钮颜色
        //   pageIconInactiveColor: '#c2c6ce', // 没激活的分页按钮颜色
        //   pageTextStyle: {
        //     color: '#FFF' // 设置图例数字的颜色
        //   },
        //   data: nameList,
        //   itemWidth: 8,
        //   itemHeight: 8,
        //   itemGap: 4,
        //   formatter: function (name) {
        //     return '{c|' + objData[name].value + '}{a|' + name + '}'
        //   },
        //   textStyle: {
        //     rich: {
        //       a: {
        //         align: 'center',
        //         fontSize: 13,
        //         color: 'rgba(255,255,255,1)'
        //       },
        //       c: {
        //         align: 'center',
        //         width: 30,
        //         fontSize: 14,
        //         color: '#00C2FF'
        //       }
        //     }
        //   }
        // },
        series: [
          {
            type: 'pie',
            roundCap: true,
            radius: ['45%', '52%'],
            center: ['50%', '50%'],
            color: ['RGBA(100, 210, 255, 1)', 'RGBA(10, 132, 255, 1)', 'RGBA(244, 220, 110, 1)', 'RGBA(212, 222, 236, 1)', 'RGBA(94, 92, 230, 1)', 'RGBA(255, 255, 255, 1)'],
            labelLine: {
              show: true,
              length: 10,
              length2: 5
            },
            label: {
              show: true,
              color: '#fff',
              overflow: 'break',
              formatter: function (params) {
                return params.data.name + ' ' + params.data.value + '个'
              }
            },
            data: pieData
          },
          {
            name: '',
            type: 'pie',
            startAngle: 80,
            radius: ['40%'],
            hoverAnimation: false,
            center: ['50%', '50%'],
            tooltip: {
              show: false
            },
            itemStyle: {
              color: 'rgba(10, 25, 40, 1)',
              borderWidth: 1,
              borderColor: '#1E2C39'
            },
            data: [sum]
          },
          {
            name: '',
            type: 'pie',
            startAngle: 80,
            radius: ['36%'],
            hoverAnimation: false,
            center: ['50%', '50%'],
            tooltip: {
              show: false
            },
            itemStyle: {
              color: 'rgba(21, 36, 50, 1)'
            },
            data: [sum]
          },
          {
            name: '',
            type: 'pie',
            startAngle: 80,
            radius: ['58%', '59%'],
            center: ['50%', '50%'],
            hoverAnimation: false,
            tooltip: {
              show: false
            },
            itemStyle: {
              normal: {
                color: 'rgba(10, 25, 40, .1)',
                borderColor: '#1E2C39',
                borderWidth: 1
              }
            },
            data: [sum]
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.card_box_short_bg {
  background: url('~@/assets/images/elevator/module-bg.png') no-repeat;
  background-size: 100% 100%;
}
.card_box_title {
  height: 31px;
  width: 100%;
  line-height: 31px;
  padding-left: 10px;
  font-size: 15px;
  font-family: Alibaba PuHuiTi-Medium, Alibaba PuHuiTi;
  font-weight: 500;
  color: #b7cfff;
}
.card_box_bg {
  background: url('~@/assets/images/elevator/card-title-bg.png') no-repeat;
  background-size: 100% 100%;
}
.echarts_title {
  height: 20px;
  font-size: 15px;
  font-family: NotoSansHans-Medium, NotoSansHans;
  font-weight: 600;
  color: #393a3d;
  line-height: 20px;
  text-align: center;
}
.echart-null {
  margin: 0 auto;
  height: calc(100% - 31px);
  width: 50%;
  text-align: center;
  color: #8a8c8f;
  img {
    max-width: 100%;
    max-height: calc(100% - 20px);
  }
  div {
    font-size: 14px;
  }
}
#elevatorBrand{
  width: 100%;
  height: 100%;
  z-index: 2;
}
</style>
