<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="content_box">
        <div class="toptip">
          <span class="green_line"></span>
          基本信息
        </div>
        <el-form ref="basicForm" :model="basicForm" :inline="true" label-width="120px" :rules="basicFormRules">
          <el-form-item label="医院名称:">
            <el-input v-if="!allDisabled" v-model="basicForm.hospitalName" disabled></el-input>
            <span v-else class="detaillClass">{{ basicForm.hospitalName }}</span>
          </el-form-item>
          <el-form-item label="院区名称:">
            <el-input v-if="!allDisabled" v-model="basicForm.hospitalName" disabled></el-input>
            <span v-else class="detaillClass">{{ basicForm.hospitalName }}</span>
          </el-form-item>
          <el-form-item label="填表人:">
            <el-input v-if="!allDisabled" v-model="basicForm.writeName" disabled></el-input>
            <span v-else class="detaillClass">{{ basicForm.writeName }}</span>
          </el-form-item>
          <el-form-item label="联系电话:">
            <el-input v-if="!allDisabled" v-model="basicForm.telphone" disabled></el-input>
            <span v-else class="detaillClass">{{ basicForm.telphone }}</span>
          </el-form-item>
          <br />
          <el-form-item label="设备名称:" prop="eqName">
            <el-input v-if="!allDisabled" v-model="basicForm.eqName" maxlength="30" show-word-limit></el-input>
            <span v-else class="detaillClass">{{ basicForm.eqName }}</span>
          </el-form-item>
          <el-form-item label="设备型号:">
            <el-input v-if="!allDisabled" v-model="basicForm.eqType" maxlength="20" show-word-limit></el-input>
            <span v-else class="detaillClass">{{ basicForm.eqType }}</span>
          </el-form-item>
          <el-form-item label="设备编码:">
            <el-input v-if="!allDisabled" v-model="basicForm.eqCode" maxlength="30" show-word-limit></el-input>
            <span v-else class="detaillClass">{{ basicForm.eqCode }}</span>
          </el-form-item>
          <br />
          <el-form-item label="专业类别:" prop="majorType">
            <el-select v-if="!allDisabled" v-model="basicForm.majorType" placeholder="请选择专业类别" @change="typeChanged(1)">
              <el-option v-for="item in majorTypeList" :key="item.id" :label="item.baseName" :value="item.id"></el-option>
            </el-select>
            <span v-else class="detaillClass">{{ basicForm.majorType }}</span>
          </el-form-item>
          <el-form-item label="系统大类:">
            <el-select v-if="!allDisabled" v-model="basicForm.systemBigType" placeholder="请选择系统大类" @change="typeChanged(2)">
              <el-option v-for="item in systemBigTypeList" :key="item.id" :label="item.baseName" :value="item.id"></el-option>
            </el-select>
            <span v-else class="detaillClass">{{ basicForm.systemBigType }}</span>
          </el-form-item>
          <el-form-item label="系统小类:">
            <el-select v-if="!allDisabled" v-model="basicForm.systemSmallType" placeholder="请选择系统小类" @change="typeChanged(3)">
              <el-option v-for="item in systemSmallTypeList" :key="item.id" :label="item.baseName" :value="item.id"></el-option>
            </el-select>
            <span v-else class="detaillClass">{{ basicForm.systemSmallType }}</span>
          </el-form-item>
          <el-form-item label="设备大类:">
            <el-select v-if="!allDisabled" v-model="basicForm.eqBigType" placeholder="请选择设备大类" @change="typeChanged(4)">
              <el-option v-for="item in eqBigTypeList" :key="item.id" :label="item.baseName" :value="item.id"></el-option>
            </el-select>
            <span v-else class="detaillClass">{{ basicForm.eqBigType }}</span>
          </el-form-item>
          <el-form-item label="设备小类:">
            <el-select v-if="!allDisabled" v-model="basicForm.eqSamllType" placeholder="请选择设备小类" @change="typeChanged(5)">
              <el-option v-for="item in eqSamllTypeList" :key="item.id" :label="item.baseName" :value="item.id"></el-option>
            </el-select>
            <span v-else class="detaillClass">{{ basicForm.eqSamllType }}</span>
          </el-form-item>
          <el-form-item label="类目名称:">
            <el-input v-if="!allDisabled" v-model="basicForm.className" maxlength="30" show-word-limit></el-input>
            <span v-else class="detaillClass">{{ basicForm.className }}</span>
          </el-form-item>
          <el-form-item label="类目代码:">
            <el-input v-if="!allDisabled" v-model="basicForm.classCode" maxlength="30" show-word-limit></el-input>
            <span v-else class="detaillClass">{{ basicForm.classCode }}</span>
          </el-form-item>
          <el-form-item label="安装地点:">
            <el-input v-if="!allDisabled" v-model="basicForm.installLocation" maxlength="30" show-word-limit></el-input>
            <span v-else class="detaillClass">{{ basicForm.installLocation }}</span>
          </el-form-item>
          <el-form-item label="首次投运日期:">
            <el-date-picker v-if="!allDisabled" v-model="basicForm.firstTime" type="date" value-format="yyyy-MM-dd" placeholder="选择首次投运日期"> </el-date-picker>
            <span v-else class="detaillClass">{{ basicForm.firstTime }}</span>
          </el-form-item>
          <el-form-item label="整体拆除日期:">
            <el-date-picker v-if="!allDisabled" v-model="basicForm.removeTime" type="date" value-format="yyyy-MM-dd" placeholder="选择整体拆除日期"> </el-date-picker>
            <span v-else class="detaillClass">{{ basicForm.removeTime }}</span>
          </el-form-item>
        </el-form>
        <div class="toptip">
          <span class="green_line"></span>
          安全责任信息
        </div>
        <el-form ref="safeForm" :model="safeForm" label-width="120px" :inline="true">
          <el-form-item label="使用部门:">
            <el-input v-if="!allDisabled" v-model="safeForm.useDept" maxlength="20" show-word-limit></el-input>
            <span v-else class="detaillClass">{{ safeForm.useDept }}</span>
          </el-form-item>
          <el-form-item label="安全责任人:">
            <el-input v-if="!allDisabled" v-model="safeForm.safeDuty" maxlength="20" show-word-limit></el-input>
            <span v-else class="detaillClass">{{ safeForm.safeDuty }}</span>
          </el-form-item>
          <el-form-item label="联系电话:">
            <el-input v-if="!allDisabled" v-model="safeForm.safePhone" maxlength="11" show-word-limit></el-input>
            <span v-else class="detaillClass">{{ safeForm.safePhone }}</span>
          </el-form-item>
          <el-form-item label="状态:">
            <el-select v-if="!allDisabled" v-model="safeForm.facilityStateCode" placeholder="请选择状态">
              <el-option v-for="item in stateList" :key="item.id" :label="item.dictName" :value="item.id"></el-option>
            </el-select>
            <span v-else class="detaillClass">{{ safeForm.facilityStateName }}</span>
          </el-form-item>
          <br />
          <el-form-item label="生产单位名称:">
            <el-input v-if="!allDisabled" v-model="safeForm.produceUnit" maxlength="30" show-word-limit></el-input>
            <span v-else class="detaillClass">{{ safeForm.produceUnit }}</span>
          </el-form-item>
          <el-form-item label="生产单位地址:">
            <el-input v-if="!allDisabled" v-model="safeForm.produceLocation" maxlength="30" show-word-limit></el-input>
            <span v-else class="detaillClass">{{ safeForm.produceLocation }}</span>
          </el-form-item>
          <el-form-item label="生产单位电话:">
            <el-input v-if="!allDisabled" v-model="safeForm.producePhone" maxlength="11" show-word-limit></el-input>
            <span v-else class="detaillClass">{{ safeForm.producePhone }}</span>
          </el-form-item>
          <br />
          <el-form-item label="安装单位名称:">
            <el-input v-if="!allDisabled" v-model="safeForm.installUnit" maxlength="30" show-word-limit></el-input>
            <span v-else class="detaillClass">{{ safeForm.installUnit }}</span>
          </el-form-item>
          <el-form-item label="安装单位地址:">
            <el-input v-if="!allDisabled" v-model="safeForm.installUnitLocation" maxlength="30" show-word-limit></el-input>
            <span v-else class="detaillClass">{{ safeForm.installUnitLocation }}</span>
          </el-form-item>
          <el-form-item label="安装单位电话:">
            <el-input v-if="!allDisabled" v-model="safeForm.installPhone" maxlength="11" show-word-limit></el-input>
            <span v-else class="detaillClass">{{ safeForm.installPhone }}</span>
          </el-form-item>
          <br />
          <el-form-item label="运维单位名称:">
            <el-input v-if="!allDisabled" v-model="safeForm.maintainUnit" maxlength="30" show-word-limit></el-input>
            <span v-else class="detaillClass">{{ safeForm.maintainUnit }}</span>
          </el-form-item>
          <el-form-item label="运维单位地址:">
            <el-input v-if="!allDisabled" v-model="safeForm.maintainLocation" maxlength="30" show-word-limit></el-input>
            <span v-else class="detaillClass">{{ safeForm.maintainLocation }}</span>
          </el-form-item>
          <el-form-item label="运维单位电话:">
            <el-input v-if="!allDisabled" v-model="safeForm.maintainPhone" maxlength="11" show-word-limit></el-input>
            <span v-else class="detaillClass">{{ safeForm.maintainPhone }}</span>
          </el-form-item>
        </el-form>
        <div class="toptip">
          <span class="green_line"></span>
          设备参数
        </div>
        <div v-if="showParamBox" class="param-box">
          <el-form ref="paramForm" :model="paramsForm" label-width="150px" :inline="true" :disabled="allDisabled">
            <el-form-item v-for="(item, index) in paramsList" :key="index" :label="item.fieldName + ':'" :prop="item.flag" :rules="createRule(item)">
              <div v-if="!allDisabled">
                <el-input v-if="item.dataType" v-model="paramsForm[item.flag]" show-word-limit maxlength="30" @blur="paramInputBlur($event, item)">
                  <template slot="append">{{ item.units }}</template>
                </el-input>
                <el-select v-else v-model="paramsForm[item.flag]" :placeholder="'请选择' + item.fieldName" @change="paramSelectChanged($event, item)">
                  <el-option v-for="(item2, index2) in item.child" :key="index2" :label="item2.typeName" :value="item2.typeName"></el-option>
                </el-select>
              </div>
              <span v-else class="detaillClass">{{ paramsForm[item.flag] }}</span>
            </el-form-item>
          </el-form>
        </div>
        <div class="upload-box">
          <div class="toptip">
            <span class="green_line"></span>
            设备资料
          </div>
          <el-upload
            v-if="handleType != 'check'"
            class="upload-demo"
            action="string"
            :limit="1"
            :on-exceed="handleExceed"
            :on-change="fileChange"
            multiple
            :file-list="fileList"
            :show-file-list="false"
            :http-request="handleUpload"
          >
            <el-button type="primary" class="upload-btn">上传</el-button>
          </el-upload>
        </div>
        <div class="upload-table">
          <el-table :data="tableData" border style="width: 100%;" stripe>
            <el-table-column type="index" width="80" label="序号" align="center"> </el-table-column>
            <el-table-column prop="fileName" show-overflow-tooltip label="档案名称" min-width="120" align="center">
              <template slot-scope="scope">{{ scope.row.fileName.split('.')[0] }} </template>
            </el-table-column>
            <el-table-column prop="fileType" show-overflow-tooltip label="档案类型" min-width="120" align="center"> </el-table-column>
            <el-table-column prop="fileSize" show-overflow-tooltip label="档案大小" min-width="120" align="center">
              <template slot-scope="scope">
                {{ (scope.row.fileSize / 1024 / 1024).toFixed(2) + 'MB' }}
              </template>
            </el-table-column>
            <el-table-column prop="createTime" show-overflow-tooltip label="上传日期" min-width="120" align="center"></el-table-column>
            <el-table-column prop="" show-overflow-tooltip label="操作" min-width="160" align="center">
              <template slot-scope="scope">
                <el-button v-if="handleType != 'add'" type="primary" size="mini" @click="download(scope.row)">下载</el-button>
                <el-button v-if="handleType != 'check'" type="danger" size="mini" @click="remove(scope)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">关闭</el-button>
      <el-button v-if="handleType != 'check'" type="primary" @click="submit()">保存</el-button>
    </div>
  </PageContainer>
</template>

<script>
import axios from 'axios'
export default {
  name: 'UnitBasicInformation',
  beforeRouteEnter(to, from, next) {
    if (to.meta.title) {
      const typeList = {
        add: '新增台账',
        edit: '编辑台账',
        check: '台账详情'
      }
      to.meta.title = typeList[to.query.handleType] ?? '台账详情'
    }
    next()
  },
  data() {
    return {
      loading: false,
      showParamBox: true,
      basicForm: {
        hospitalName: '',
        writeName: '', // 填表人
        eqName: '', // 设备名称
        eqType: '', // 设备型号
        eqCode: '', // 设备编码
        telphone: '',
        className: '', // 类目名称
        classCode: '', // 类目代码
        installLocation: '', // 安装地点
        firstTime: '', // 首次投运日期
        removeTime: '', // 整体拆除日期
        majorType: '',
        majorName: '',
        systemBigType: '',
        systemBigName: '',
        systemSmallType: '',
        systemSmallName: '',
        eqBigType: '',
        eqBigName: '',
        eqSamllType: '',
        eqSamllName: ''
      },
      safeForm: {
        useDept: '', // 使用部门
        safeDuty: '', // 安全责任人
        safePhone: '', // 联系电话
        produceUnit: '', // 生产单位名称
        produceLocation: '', // 生产单位地址
        producePhone: '', // 生产单位电话
        installUnit: '', // 安装单位名称
        installUnitLocation: '', // 安装单位地址
        installPhone: '', // 安装单位电话
        maintainUnit: '', // 运维单位名称
        maintainLocation: '', // 运维单位地址
        maintainPhone: '', // 运维单位电话
        facilityStateCode: '', // 状态code
        facilityStateName: '' // 状态name
      },
      majorTypeList: [], // 专业类别列表
      systemBigTypeList: [], // 系统大类列表
      systemSmallTypeList: [], // 系统小类列表
      eqBigTypeList: [], // 设备大类列表
      eqSamllTypeList: [], // 设备小类列表
      paramsList: [], // 数值型参数
      paramsForm: {}, // 设备参数表单
      loginData: {}, // 登录信息
      ledgerId: '',
      basicFormRules: {
        eqName: [{ required: true, message: '请输入设备名称', trigger: 'blur' }],
        majorType: [{ required: true, message: '请选择专业类别', trigger: 'change' }]
      },
      echoData: {},
      stateList: [],
      eqParamList: [],
      fileList: [],
      tableData: [],
      allDisabled: false,
      handleType: ''
    }
  },
  created() {
    this.ledgerId = this.$route.query.ledgerId
    this.loginData = JSON.parse(sessionStorage.getItem('LOGINDATA')) || ''
    if (this.loginData) {
      this.basicForm.hospitalName = this.loginData.hospitalName
      this.basicForm.writeName = this.loginData.name
      this.basicForm.telphone = this.loginData.phone
    }
    let handleType = this.$route.query.handleType
    this.handleType = this.$route.query.handleType
    if (handleType == 'check') {
      this.allDisabled = true
    }
    if (handleType == 'edit' || handleType == 'check') {
      this.echoData = this.$route.query.echoData

      if (this.echoData.eqParam) {
        this.eqParamList = JSON.parse(this.echoData.eqParam)
      }
      if (handleType == 'edit') {
        this.getAllTypeList(this.echoData)
      }
      this.basicForm.hospitalName = this.echoData.hospitalName
      this.basicForm.writeName = this.echoData.writeName
      this.basicForm.telphone = this.echoData.telphone
      this.basicForm.eqName = this.echoData.eqName
      this.basicForm.eqType = this.echoData.eqType
      this.basicForm.eqCode = this.echoData.eqCode

      this.basicForm.majorType = this.echoData.majorName
      this.basicForm.systemBigType = this.echoData.systemBigName
      this.basicForm.systemSmallType = this.echoData.systemSmallName
      this.basicForm.eqBigType = this.echoData.eqBigName
      this.basicForm.eqSamllType = this.echoData.eqSamllName

      this.basicForm.className = this.echoData.className
      this.basicForm.classCode = this.echoData.classCode
      this.basicForm.installLocation = this.echoData.installLocation
      this.basicForm.firstTime = this.echoData.firstTime
      this.basicForm.removeTime = this.echoData.removeTime
      this.safeForm.useDept = this.echoData.useDept
      this.safeForm.safeDuty = this.echoData.safeDuty
      this.safeForm.safePhone = this.echoData.safePhone
      this.safeForm.staffNumber = this.echoData.staffNumber
      this.safeForm.produceUnit = this.echoData.produceUnit
      this.safeForm.produceLocation = this.echoData.produceLocation
      this.safeForm.producePhone = this.echoData.producePhone
      this.safeForm.installUnit = this.echoData.installUnit
      this.safeForm.installUnitLocation = this.echoData.installUnitLocation
      this.safeForm.installPhone = this.echoData.installPhone
      this.safeForm.maintainUnit = this.echoData.maintainUnit
      this.safeForm.maintainLocation = this.echoData.maintainLocation
      this.safeForm.maintainPhone = this.echoData.maintainPhone
      this.safeForm.facilityStateCode = this.echoData.facilityStateCode
      this.safeForm.facilityStateName = this.echoData.facilityStateName
      if (this.echoData.fileUploadList && this.echoData.fileUploadList.length) {
        this.tableData = this.echoData.fileUploadList
      }
    }
  },
  mounted() {
    this.getTypeList()
    this.getStateList()
  },
  methods: {
    async submit() {
      const valid = await this.$refs.basicForm.validate().catch((err) => {
        return false
      })
      if (!valid) return
      if (this.paramsList && this.paramsList.length) {
        const valid = await this.$refs.paramForm.validate().catch((err) => {
          return false
        })
        if (!valid) return
      }
      if (this.basicForm.majorType) {
        let obj = this.majorTypeList.find((item) => {
          return item.id == this.basicForm.majorType
        })
        this.basicForm.majorName = obj.baseName
      }
      if (this.basicForm.systemBigType) {
        let obj = this.systemBigTypeList.find((item) => {
          return item.id == this.basicForm.systemBigType
        })
        this.basicForm.systemBigName = obj.baseName
      }
      if (this.basicForm.systemSmallType) {
        let obj = this.systemSmallTypeList.find((item) => {
          return item.id == this.basicForm.systemSmallType
        })
        this.basicForm.systemSmallName = obj.baseName
      }
      if (this.basicForm.eqBigType) {
        let obj = this.eqBigTypeList.find((item) => {
          return item.id == this.basicForm.eqBigType
        })
        this.basicForm.eqBigName = obj.baseName
      }
      if (this.basicForm.eqSamllType) {
        let obj = this.eqSamllTypeList.find((item) => {
          return item.id == this.basicForm.eqSamllType
        })
        this.basicForm.eqSamllName = obj.baseName
      }
      if (this.safeForm.facilityStateCode) {
        let obj = this.stateList.find((item) => {
          return item.id == this.safeForm.facilityStateCode
        })
        this.safeForm.facilityStateName = obj.dictName
      }
      let params = { ...this.basicForm, ...this.safeForm }
      params.eqParam = JSON.stringify(this.paramsList)
      params.ledgerId = this.ledgerId
      if (this.$route.query.handleType == 'edit') {
        params.id = this.echoData.id
      }
      params.fileUploadList = JSON.stringify(this.tableData)
      this.$api.ipsmSaveKeyLedge(params).then((res) => {
        if (res.code == 200) {
          this.$message.success(res.message)
          this.$router.go(-1)
        } else {
          this.$message.error(res.message)
        }
      })
    },
    cancel() {
      this.$router.go(-1)
    },
    getTypeList() {
      this.$api.ipsmEquipmentListData({ parentId: -1 }).then((res) => {
        if (res.code == 200) {
          this.majorTypeList = res.data
          if (this.$route.query.handleType == 'edit') {
            this.basicForm.majorType = this.echoData.majorType || ''
          }
        } else {
          this.$message.error(res.message)
        }
      })
    },
    typeChanged(val) {
      if (val == 1) {
        this.basicForm.systemBigType = ''
        this.basicForm.systemSmallType = ''
        this.basicForm.eqBigType = ''
        this.basicForm.eqSamllType = ''
        this.$api.ipsmEquipmentListData({ parentId: this.basicForm.majorType }).then((res) => {
          if (res.code == 200) {
            this.systemBigTypeList = res.data
          } else {
            this.$message.error(res.message)
          }
        })
      }
      if (val == 2) {
        this.basicForm.systemSmallType = ''
        this.basicForm.eqBigType = ''
        this.basicForm.eqSamllType = ''
        this.$api.ipsmEquipmentListData({ parentId: this.basicForm.systemBigType }).then((res) => {
          if (res.code == 200) {
            this.systemSmallTypeList = res.data
          } else {
            this.$message.error(res.message)
          }
        })
      }
      if (val == 3) {
        this.basicForm.eqBigType = ''
        this.basicForm.eqSamllType = ''
        this.$api.ipsmEquipmentListData({ parentId: this.basicForm.systemSmallType }).then((res) => {
          if (res.code == 200) {
            this.eqBigTypeList = res.data
          } else {
            this.$message.error(res.message)
          }
        })
      }
      if (val == 4) {
        this.basicForm.eqSamllType = ''
        this.$api.ipsmEquipmentListData({ parentId: this.basicForm.eqBigType }).then((res) => {
          if (res.code == 200) {
            this.eqSamllTypeList = res.data
          } else {
            this.$message.error(res.message)
          }
        })
      }
      if (val == 5) {
        this.$api.ipsmGetEquipmentList({ typeId: this.basicForm.eqSamllType }).then((res) => {
          if (res.code == 200) {
            this.transParams(res.data)
          } else {
            this.$message.error(res.message)
          }
        })
      }
    },
    transParams(data) {
      let arr = [] // 枚举型
      data.forEach((item) => {
        if (item.dataType == '枚举型') {
          arr.push(item)
        }
      })
      let dataInfo = {}
      arr.forEach((item, index) => {
        let id = item.id
        if (!dataInfo[id]) {
          dataInfo[id] = {
            id,
            fieldName: item.fieldName,
            child: []
          }
        }
        dataInfo[id].child.push(item)
      })
      arr = Object.values(dataInfo)
      let arr2 = [] // 数值型
      data.forEach((item, index) => {
        if (item.dataType == '数值型') {
          arr2.push(item)
        }
      })
      arr.forEach((item) => {
        item.child.forEach((item2, index2) => {
          if (!item2.spImportantType) {
            item.child.splice(index2, 1)
          }
        })
      })
      this.paramsList = [...arr, ...arr2]
      this.paramsList.forEach((item, index) => {
        item.flag = 'yqf' + index
      })
      this.$refs.paramForm.resetFields()
      this.paramsForm = {}
      this.showParamBox = false
      this.$nextTick(() => {
        setTimeout(() => {
          this.showParamBox = true
        }, 100)
      })
    },
    paramInputBlur(e, item) {
      item.evalue = e.target.value
      console.log(item)
    },
    paramSelectChanged(e, item) {
      item.evalue = e
      // this.$forceUpdate()
      console.log(item)
    },
    getAllTypeList(data) {
      if (data.majorType) {
        this.$api.ipsmEquipmentListData({ parentId: data.majorType }).then((res) => {
          if (res.code == 200) {
            this.systemBigTypeList = res.data
            this.basicForm.systemBigType = this.echoData.systemBigType || ''
          } else {
            this.$message.error(res.message)
          }
        })
      }
      if (data.systemBigType) {
        this.$api.ipsmEquipmentListData({ parentId: data.systemBigType }).then((res) => {
          if (res.code == 200) {
            this.systemSmallTypeList = res.data
            this.basicForm.systemSmallType = this.echoData.systemSmallType || ''
          } else {
            this.$message.error(res.message)
          }
        })
      }
      if (data.systemSmallType) {
        this.$api.ipsmEquipmentListData({ parentId: data.systemSmallType }).then((res) => {
          if (res.code == 200) {
            this.eqBigTypeList = res.data
            this.basicForm.eqBigType = this.echoData.eqBigType || ''
          } else {
            this.$message.error(res.message)
          }
        })
      }
      if (data.eqBigType) {
        this.$api.ipsmEquipmentListData({ parentId: data.eqBigType }).then((res) => {
          if (res.code == 200) {
            this.eqSamllTypeList = res.data
            this.basicForm.eqSamllType = this.echoData.eqSamllType || ''
          } else {
            this.$message.error(res.message)
          }
        })
      }
      if (data.eqSamllType) {
        this.$api.ipsmGetEquipmentList({ typeId: data.eqSamllType }).then((res) => {
          if (res.code == 200) {
            this.transParams(res.data)
            this.paramsList.forEach((item, index) => {
              this.eqParamList.forEach((item2) => {
                if (item.id == item2.id) {
                  item.evalue = item2.evalue
                  // this.paramsForm[item.flag] = item2.evalue
                  this.$set(this.paramsForm, item.flag, item2.evalue)
                }
              })
            })
          } else {
            this.$message.error(res.message)
          }
        })
      }
    },
    getStateList() {
      let params = {
        dictType: 'Equipment_status_type',
        pageNo: 1,
        pageSize: 999
      }
      if (this.handleType != 'add') {
        params.hospitalCode = this.echoData.hospitalCode
      }
      this.$api.ipsmFindDictionaryTableList(params).then((res) => {
        if (res.code == 200) {
          this.stateList = res.data.list
          if (this.safeForm.facilityStateCode) {
            let flag = false
            this.stateList.forEach((item) => {
              if (item.id == this.safeForm.facilityStateCode) {
                flag = true
              }
            })
            if (!flag) {
              this.safeForm.facilityStateCode = ''
            }
          }
        } else {
          this.$message.error(res.message)
        }
      })
    },
    fileChange(file, fileList) {
      this.fileList = fileList
    },
    handleUpload() {
      let formData = new FormData()
      this.fileList.forEach((item) => {
        console.log(item)
        formData.append('file', item.raw)
      })
      this.fileList = []
      let loginData = JSON.parse(sessionStorage.getItem('LOGINDATA'))
      formData.append('hospitalCode', loginData.hospitalCode)
      axios({
        method: 'post',
        url: __PATH.VUE_AQ_URL + 'fileuplocad/upload',
        data: formData,
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      }).then((res) => {
        console.log('res', res)
        if (res.data.code == 200) {
          this.tableData = [...this.tableData, ...res.data.data]
        } else {
          this.$message.error('上传失败!')
        }
      })
    },
    remove(row) {
      this.$confirm('确认删除该文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.tableData.splice(row.$index, 1)
          if (this.handleType == 'edit') {
            this.$api.ipsmRemoveKeyLedgeFile({ id: row.row.id }).then((res) => {})
          }
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    handleExceed() {
      this.$message.error('每次只允许上传单个文件!')
    },
    download(row) {
      let fileName = row.fileName.split('.')[0]
      let baseInfo = JSON.parse(sessionStorage.getItem('LOGINDATA'))
      let data = {
        unitCode: baseInfo.unitCode || 'BJSYGJ',
        hospitalCode: baseInfo.hospitalCode || 'BJSJTYY',
        id: row.id
      }
      axios({
        method: 'get',
        url: __PATH.VUE_AQ_URL + 'repository/standingBookDownload',
        params: data,
        responseType: 'blob',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      }).then((res) => {
        let name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
        let blob = new Blob([res.data]) // { type: "application/vnd.ms-excel" }
        let url = window.URL.createObjectURL(blob) // 创建一个临时的url指向blob对象
        // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
        let a = document.createElement('a')
        a.href = url
        a.download = decodeURI(name)
        a.click()
        // 释放这个临时的对象url
        window.URL.revokeObjectURL(url)
      })
    },
    createRule(item) {
      if (item.constraintCondition && item.constraintCondition == 'M') {
        return { required: true, message: '请输入' + item.fieldName, trigger: 'blur' }
      } else if (item.child && item.child.length && item.child[0].constraintCondition == 'M') {
        return { required: true, message: '请选择' + item.fieldName, trigger: 'change' }
      } else {
        return {}
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-textarea .el-input__count {
  line-height: normal;
}

.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
  padding: 10px;
  overflow-y: auto;
}

::v-deep .el-textarea__inner {
  min-height: 120px !important;
}

.content_box {
  height: calc(100% - 30px);
  margin-top: 10px;
  padding: 20px 25px 25px;
  background: #fff;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.form-inline {
  .el-input,
  .el-select,
  .el-cascader {
    width: 300px;
  }
}

.el-textarea {
  width: 400px;
}

.toptip {
  border: 0;
}

.upload-btn {
  width: 60px;
  height: 30px;
  //   min-width: 60px;
  background-color: #4387f7;
  //   transform: translateY(3px);
  margin-bottom: 10px;
  padding: 8px;
}

.param-box {
  .el-form-item {
    margin-bottom: 30px;
  }

  .el-input {
    width: 200px;
  }
}

.detaillClass {
  display: inline-block;
  width: 200px;
}
</style>
