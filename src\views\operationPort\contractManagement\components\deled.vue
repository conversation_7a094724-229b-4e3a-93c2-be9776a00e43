<template>
  <div class="tab_content">
    <div class="right-heade">
      <el-input v-model="formData.archiveInfo" placeholder="编号/名称" suffix-icon="el-icon-search" clearable />
      <el-button type="primary" @click="search">查询</el-button>
      <el-button type="primary" plain @click="reset">重置</el-button>
      <div>
        <el-button type="primary" size="small" @click="handleRecovery">恢复</el-button>
        <el-button type="danger" size="small" @click="handleCompletelyDelete">彻底删除</el-button>
      </div>
    </div>
    <div class="right-content">
      <div class="table-content">
        <TablePage
          ref="tablePage"
          v-loading="tableLoading"
          v-scrollHideTooltip
          :tableColumn="tableColumn"
          :data="tableData"
          border
          height="100%"
          :showPage="true"
          :pageData="pageData"
          :pageProps="{
            page: 'current',
            pageSize: 'size',
            total: 'total'
          }"
          @pagination="paginationChange"
          @selection-change="handleSelectionChange"
        >
        </TablePage>
      </div>
    </div>
    <Recovery :visible.sync="visible" :infoList="infoList" @success="handleQueryTablelist" />
  </div>
</template>
<script lang="jsx">
import tableListMixin from '@/mixins/tableListMixin.js'
import moment from 'moment'
import Recovery from '@/views/operationPort/dossierManager/components/Recovery.vue'
export default {
  components: { Recovery },
  mixins: [tableListMixin],
  data() {
    return {
      moment,
      optionsList: [
        {
          label: '文件',
          value: '0'
        },
        {
          label: '图片',
          value: '1'
        },
        {
          label: '资料',
          value: '2'
        }
      ],
      tableColumn: [
        {
          type: 'selection',
          align: 'center',
          width: '50'
        },
        {
          prop: 'archiveName',
          label: '合同名称',
          align: 'center'
        },
        {
          prop: 'archiveNumber',
          label: '合同编号',
          align: 'center'
        },
        {
          prop: 'folderName',
          label: '原文件夹',
          align: 'center'
        },
        {
          prop: 'createName',
          label: '创建人',
          align: 'center'
        },
        {
          prop: 'createTime',
          label: '创建时间',
          align: 'center',
          render: (h, row) => {
            return row.row.createTime ? this.moment(row.row.createTime).format('YYYY-MM-DD HH:mm:ss') : ''
          }
        },
        {
          prop: 'archiveOwnerName',
          label: '所有者',
          align: 'center'
        },
        {
          prop: 'archiveOwnerDeptName',
          label: '所有者部门',
          align: 'center'
        },
        {
          prop: 'updateTime',
          label: '删除时间',
          align: 'center',
          render: (h, row) => {
            return row.row.updateTime ? this.moment(row.row.updateTime).format('YYYY-MM-DD HH:mm:ss') : ''
          }
        },
        {
          prop: 'updateName',
          label: '操作人',
          align: 'center'
        }
      ],
      formData: {
        archiveInfo: ''
      },
      tableLoading: false,
      tableData: [],
      pageData: {
        current: 1,
        size: 15,
        total: 0
      },
      checkedData: [],
      infoList: [],
      visible: false
    }
  },
  computed: {
    isMy() {
      return this.$route.path === '/contractManagement/myContract'
    }
  },
  created() {
    this.handleQueryTablelist()
  },
  methods: {
    handleQueryTablelist() {
      const isMine = this.isMy
      const parmas = {
        isMine,
        isRecycle: '1', // 回收站
        archiveType: '0',
        ...this.formData,
        ...this.pageData
      }
      this.$api.fileManagement.queryByPage(parmas).then((res) => {
        this.tableData = res.data.records
        this.pageData.total = res.data.total
      })
    },
    search() {
      this.pageData.current = 1
      this.handleQueryTablelist()
    },
    reset() {
      this.formData.archiveInfo = ''
      this.handleQueryTablelist()
    },
    handleRecovery() {
      const rows = this.checkedData
      if (rows.length < 1) {
        this.$message.warning('请至少选择一条数据！')
        return
      }
      const idList = rows.map((v) => v.archiveId)
      this.$api.fileManagement.recoverFile({ idList }).then((res) => {
        if (res.code === '200') {
          if (res.data && res.data.length) {
            this.infoList = res.data
            this.visible = true
            return
          }
          this.$message.success('恢复成功')
          this.handleQueryTablelist()
        } else {
          this.$message.error('恢复失败')
        }
      })
    },
    // 分页事件
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getTableList()
    },
    handleSelectionChange(e) {
      this.checkedData = e
    },
    handleCompletelyDelete() {
      const rows = this.checkedData
      if (rows.length < 1) {
        this.$message.warning('请至少选择一条数据！')
        return
      }
      const message = rows.map((item) => item.archiveName).join('，')
      this.$confirm('确认删除“' + message + '”等数据吗？', '彻底删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const idList = rows.map((v) => v.archiveId)
          this.$api.fileManagement.deleteFile({ idList }).then((res) => {
            if (res.code === '200') {
              this.$message.success('删除成功')
              this.handleQueryTablelist()
            } else {
              this.$message.error('删除失败')
            }
          })
        })
        .catch(() => {})
    }
  }
}
</script>
<style lang="scss" scoped>
.tab_content {
  width: 100%;
  height: calc(100% - 40px);
  .right-heade {
    border-radius: 4px;
    ::v-deep .el-input {
      width: 200px;
    }
    & > div {
      margin-right: 10px;
      margin-top: 10px;
    }
  }
  .right-content {
    background: #fff;
    border-radius: 4px;
    overflow: hidden;
    width: 100%;
    height: calc(100% - 50px);
    margin-top: 16px;
    .btns-group {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
      & > div {
        display: flex;
      }
      .btns-group-control {
        > div {
          margin-left: 10px;
        }
        // & > div, & > button {
        //   margin-right: 10px;
        // }
      }
    }
    .table-content {
      height: calc(100% - 85px);
      ::v-deep .el-table .el-table__body-wrapper td.el-table__cell {
        border-right: 1px solid #ededf5;
        .tooltip-over-td {
          display: block;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
}
</style>
