<template>
  <div>
    <el-dialog title="研判记录" :visible.sync="dialogVisible" :before-close="closeDialog" custom-class="model-dialog">
      <div style="min-height: 300px; width: 100%; padding: 10px; background-color: #fff;">
        <el-table :border="true" :data="dataList">
          <el-table-column prop="createPersonName" label="研判人" show-overflow-tooltip></el-table-column>
          <el-table-column prop="judgeType" label="研判方式" show-overflow-tooltip></el-table-column>
          <el-table-column prop="riskLevel" label="研判结果" show-overflow-tooltip>
            <template slot-scope="scope">
              <span v-if="scope.row.riskLevel == '重大风险'" style="color: rgb(255 0 0);">重大风险</span>
              <span v-if="scope.row.riskLevel == '较大风险'" style="color: rgb(255 97 0);">较大风险</span>
              <span v-if="scope.row.riskLevel == '一般风险'" style="color: rgb(255 255 0);">一般风险</span>
              <span v-if="scope.row.riskLevel == '低风险'" style="color: rgb(0 0 255);">低风险</span>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="研判时间" show-overflow-tooltip></el-table-column>
        </el-table>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button class="sino-button-cancel" @click="closeDialog">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script type="text/ecmascript-6">
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    dataList: {
      type: Array
    }
  },
  data() {
    return {
      purchaseTable: []
    }
  },
  mounted() {
    this.purchaseTable = this.dataList
  },
  methods: {
    closeDialog() {
      this.$emit('closeDialog')
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .content {
  min-height: 200px;
  width: 100%;
  padding: 10px;
  background-color: #fff;
}
</style>
