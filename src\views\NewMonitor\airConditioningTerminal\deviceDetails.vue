<template>
  <PageContainer v-loading="loading" class="monitorDetails">
    <div slot="header" class="monitorDetails-heade">
      <div>
        <i class="el-icon-arrow-left" @click="
          () => {
            $router.push($route.meta.jumpAddress)
            currentPattern = 2
          }
        "></i>
        <span>{{ $route.query.assetsName }}</span>
      </div>
      <div class="heade-pattern">
        <div class="pattern-item" @click="switchPattern(1)">
          <svg-icon :name="currentPattern == 1 ? 'chartModeActive' : 'chartMode'" class="pattern-icon" />
          <span :style="{ color: currentPattern == 1 ? '#3562DB' : '#414653' }">图形模式</span>
        </div>
        <div class="pattern-item" @click="switchPattern(2)">
          <svg-icon :name="currentPattern == 2 ? 'listModeActive' : 'listMode'" class="pattern-icon" />
          <span :style="{ color: currentPattern == 2 ? '#3562DB' : '#414653' }">列表模式</span>
        </div>
      </div>
    </div>
    <div slot="content" class="content-details">
      <div v-if="currentPattern == 2" class="content-heade">
        <ContentCard title="基础信息">
          <div slot="content">
            <div class="heade-info">
              <p class="heade-p">
                <span>设备名称：</span>
                <span>{{ detailsData.assetsName }}</span>
              </p>
              <p class="heade-p">
                <span>设备编码：</span>
                <span>{{ detailsData.assetsCode }}</span>
              </p>
              <p class="heade-p">
                <span>通用名称：</span>
                <span>{{ detailsData.commonName }}</span>
              </p>
              <p class="heade-p">
                <span>所属品类：</span>
                <span>{{ detailsData.sysOf1Name }}</span>
              </p>
              <p class="heade-p">
                <span>模型ID：</span>
                <span>{{ detailsData.modelCode }}</span>
              </p>
              <p class="heade-p">
                <span>模型坐标：</span>
                <span
                  v-if="detailsData.modelPositionX !== null || detailsData.modelPositionY !== null || detailsData.modelPositionZ !== null">{{
                    detailsData.modelPositionX }},{{ detailsData.modelPositionY }},{{ detailsData.modelPositionZ }}</span>
              </p>
              <p class="heade-p">
                <span>所属区域：</span>
                <span>{{ detailsData.spaceLocationName }}</span>
              </p>
            </div>
          </div>
        </ContentCard>
        <ContentCard title="关联信息">
          <div slot="content">
            <div class="heade-info">
              <p class="heade-p" style="width: 100%">
                <span>关联空间：</span>
                <span>{{ detailsData.spaceListName }}</span>
              </p>
              <el-tabs v-if="dialogDetail.length > 1" v-model="activeWorkOderType" class="tabs"
                @tab-click="tabsClick(activeWorkOderType)">
                <el-tab-pane v-for="(item, index) in dialogDetail" :key="index" :label="item" :name="item" />
              </el-tabs>
              <div v-if="activeWorkOderType === '实时数据'" class="securityOperationMonitor-content">
                <div class="statistics-list" v-if="statisticsList.length">
                  <div v-for="(item, index) in statisticsList" :key="index" class="statistics-content">
                    <div v-if="item.status != '异常'" class="statistics-item">
                      <div style="flex: 1; display: flex; justify-content: space-between">
                        <div style="width: 100%">
                          <p class="item-title">{{ item.metadataName }}</p>
                          <p class="item-value" :style="{ color: item.sectionColor }" :title="item.valueText">
                            {{ item.valueText }}
                            <span :style="{ color: item.sectionColor }">{{ item.sectionName }}</span>
                          </p>
                        </div>
                        <el-tooltip effect="dark" content="详情" placement="top-start">
                          <img src="../../../assets/images/newMonitor/configuration.png" alt=""
                            @click="detailPage(item)" />
                        </el-tooltip>
                      </div>
                      <p class="item-title" style="font-size: 13px; color: #c2c4c8">更新时间: {{ item.dateTime }}</p>
                    </div>
                    <div v-else class="statistics-item"
                      style="border: 1px solid #f53f3f; background: linear-gradient(180deg, #ffffff 55%, #ffe2e2 100%)">
                      <div style="display: flex">
                        <div>
                          <p class="item-title">{{ item.title }}</p>
                          <p class="item-value" style="color: #fa403c">{{ item.status }}</p>
                        </div>
                      </div>
                      <p class="item-title" style="font-size: 13px; color: #c2c4c8">更新时间: {{ item.upTime }}</p>
                    </div>
                  </div>
                  <div v-if="statisticsList.length === 0"
                    style="width: 100%; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center; padding: 50px; margin-top: 80px">
                    <img src="@/assets/images/newMonitor/no-chat.png" />
                    <span style="color: #909399">暂无数据</span>
                  </div>
                </div>
              </div>
              <div v-if="activeWorkOderType === '控制参数'">
                <div class="statistics-list">
                  <div v-if="controlList.length === 0"
                    style="width: 100%; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center; padding: 50px; margin-top: 80px">
                    <img src="@/assets/images/newMonitor/no-chat.png" />
                    <span style="color: #909399">暂无数据</span>
                  </div>
                  <div v-for="(item, index) in controlList" :key="index" class="control-content"
                    @click="oncontrolList(item, index)">
                    <p v-if="$route.query.systemCode !== 'AFXT'" :class="{ active: activeIndex === index }">{{
                      item.metadataName }}</p>
                  </div>
                  <div v-for="(item, index) in controlList" :key="index" class="control-content" @click="openVideo">
                    <p v-if="$route.query.systemCode === 'AFXT' && detailsData.product === 'hkws_afxt_afsxj_002'"
                      :class="{ active: activeIndex === index }">{{ item.metadataName }}</p>
                  </div>
                </div>
              </div>
              <div v-if="activeWorkOderType === '历史数据'">
                <historical-data ref="historical" :historicalData="historicalData" :detailsData="detailsData" />
              </div>
              <div v-if="activeWorkOderType === '事件记录'">
                <incident-data />
              </div>
              <div v-if="activeWorkOderType === '报警记录'">
                <alarmRecord-data />
              </div>
              <div v-if="activeWorkOderType === '监控画面'">
                <monitoring-screen />
              </div>
              <div v-if="activeWorkOderType === '维保记录'">
                <maintenance-record ref="maintenance" :key="maintenanceKey" :selectiveType="2" />
              </div>
              <div v-if="activeWorkOderType === '年检记录'">
                <maintenance-record ref="maintenance" :key="maintenanceKey" :selectiveType="5" />
              </div>
              <div v-if="activeWorkOderType === '定位轨迹'">
                <positioning-track :factoryCode="factoryCode" />
              </div>
              <div v-if="activeWorkOderType === '出诊记录'">
                <outcall-record :factoryCode="factoryCode" />
              </div>
              <div v-if="activeWorkOderType === '就诊记录'">
                <outpatient-record :factoryCode="factoryCode" />
              </div>
              <div v-if="activeWorkOderType === '通行记录'">
                <passage-record :factoryCode="factoryCode" />
              </div>
            </div>
          </div>
        </ContentCard>
      </div>
      <div v-if="currentPattern == 1" class="securityOperationMonitor-mode-content">
        <!-- <div class="monitor-content-left">
          <div v-for="(item, index) in scadaList" :key="index">
            <p @click="handleTeamClick(item.id, index)" :class="{ 'active': currentSelectedIndex === index }">{{ item.name
            }}</p>
          </div>
        </div> -->
        <div class="monitor-content-right">
          <graphics-mode ref="scadaShow" :checkedTeamData="checkedTeamData" />
        </div>
      </div>
      <!-- 弹窗事件 -->
      <template v-if="DialogShow">
        <popUpEvent :DialogShow="DialogShow" :booleDialogShow="booleDialogShow" :enumDialogShow="enumDialogShow"
          :metadataTag="metadataTag" :dataType="dataType" :title="title" @submitDialog="submitParamsGather"
          @closeDialog="closeHarvesterDialog" />
      </template>
      <!-- 云平台视频 -->
      <template v-if="videoDialgoShow">
        <videoDialgo :videoDialgoShow="videoDialgoShow" :title="title1" :factoryCode="factoryCode"
          @submitDialog="submitParamsGather" @closeVideoDialog="closeVideoDialog" />
      </template>
    </div>
  </PageContainer>
</template>
<script>
import moment from 'moment'
import historicalData from './historicalData.vue'
import incidentData from './incidentData.vue'
import alarmRecordData from './alarmRecordData.vue'
import maintenanceRecord from './maintenanceRecord.vue'
import monitoringScreen from './monitoringScreen.vue'
import positioningTrack from './positioningTrack.vue'
import outcallRecord from './outcallRecord.vue'
import outpatientRecord from './outpatientRecord.vue'
import passageRecord from './passageRecord.vue'
import popUpEvent from './components/popUpEvent.vue'
import videoDialgo from './components/videoDialgo.vue'
import graphicsMode from '../components/graphicsMode.vue'
moment.locale('zh-cn')
export default {
  name: 'deviceDetails',
  components: {
    graphicsMode,
    historicalData,
    incidentData,
    alarmRecordData,
    maintenanceRecord,
    monitoringScreen,
    popUpEvent,
    videoDialgo,
    positioningTrack,
    outcallRecord,
    outpatientRecord,
    passageRecord,
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (from.path == '/allAlarm/alarmDetail') {
        vm.activeWorkOderType = '报警记录'
      }
      if (vm.$refs.historical) {
        vm.$refs.historical.selectAssetsProperties = ''
      }
    })
  },
  data() {
    return {
      maintenanceKey: 0, // 用于重新渲染组件的 key
      currentPattern: 2, // 当前列表模式
      DialogShow: false, // 弹窗
      videoDialgoShow: false, // 弹窗
      title1: '云平台视频监控', // 视频弹窗title
      factoryCode: '',
      metadataTag: '',
      booleDialogShow: false, // 布尔值弹窗
      enumDialogShow: false, // 枚举值弹窗
      dataType: [], // 布尔值弹窗数据
      title: '', // 布尔值弹窗title
      detailsData: {},
      loading: true,
      statisticsList: [],
      activeIndex: null,
      requestInfo: {
        projectCode: '',
        dataRange: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')], // 时间范围
        timeType: 0 // 0: 当天 1: 本月 2: 本年 3: 自定义
      },
      activeWorkOderType: '实时数据',
      // dialogDetail: ['实时数据', '控制参数', '历史数据', '事件记录', '报警记录', '监控画面', '维保记录', '年检记录'],
      dialogDetail: ['实时数据', '历史数据', '报警记录', '监控画面'],
      controlList: [],
      checkedTeamData: '',
      historicalData: {}
    }
  },
  watch: {
    $route() {
      this.activeWorkOderType = '实时数据'
    }
  },
  activated() {
    this.$nextTick(() => {
      this.tabsClick('实时数据')
      this.requestInfo.projectCode = this.$route.query.projectCode
      this.getDetails()
      this.getQueryFunctionList()
    })
  },
  mounted() {
    this.$nextTick(() => {
      this.tabsClick('实时数据')
      this.requestInfo.projectCode = this.$route.query.projectCode
      this.getDetails()
      this.getQueryFunctionList()
    })
  },
  methods: {
    // 实时数据跳转历史数据
    detailPage(data) {
      this.historicalData = data
      this.activeWorkOderType = '历史数据'
    },
    // scada图纸获取
    setScadaShow() {
      this.$refs.scadaShow.oneScadaChange(this.checkedTeamData)
    },
    // 图纸/列表模式切换
    switchPattern(type) {
      if (this.currentPattern != type) {
        this.currentPattern = type
        if (type === 1) {
          if (this.checkedTeamData) {
            this.$nextTick(() => {
              this.setScadaShow()
            })
          } else {
            this.$message.warning('暂未绑定设备图纸！')
          }
        } else {
          this.getDetails()
        }
      }
    },
    tabsClick(val) {
      if (val == '实时数据') {
        this.getQueryrealtimeDataList()
      } else if (val == '控制参数') {
        this.getQueryFunctionList()
        this.getDetails()
      }
      this.$nextTick(() => {
        if (val === '维保记录') {
          this.activeWorkOderType = '维保记录'
          this.maintenanceKey += 1
        } else if (val === '年检记录') {
          this.activeWorkOderType = '年检记录'
          this.maintenanceKey += 1
        }
        if (this.$refs.maintenance) {
          this.$refs.maintenance.showTable = false
          this.$refs.maintenance.selectType = val === '维保记录' ? 2 : 5
        }
      })
    },
    // 获取实时数据
    getQueryrealtimeDataList() {
      this.$api.getQueryrealtimeData({ id: this.$route.query.id }).then((res) => {
        if (res.code == 200) {
          this.statisticsList = res.data
        }
      })
    },
    // 获取控制参数
    getQueryFunctionList() {
      this.$api.getQueryFunction(this.$route.query.id).then((res) => {
        if (res.code == 200) {
          this.controlList = res.data
        }
      })
    },
    oncontrolList(item, index) {
      this.activeIndex = index
      let data = JSON.parse(item.valueType)
      this.dataType = data
      if (this.dataType) {
        this.DialogShow = true
        if (data.valueType.type === 'enum') {
          this.enumDialogShow = true
        } else if (data.valueType.type === 'boolean') {
          this.booleDialogShow = true
        }
        this.metadataTag = item.metadataTag
        this.title = item.metadataName
      }
    },
    // 云平台视频
    openVideo() {
      this.videoDialgoShow = true
    },
    closeVideoDialog() {
      this.videoDialgoShow = false
    },
    // 关联空间tag事件
    tagSpaceHandleClose(id) {
      // 根据id 删除spaceNames中的对应项
      const index = this.spaceNames.findIndex((e) => e.spaceId === id)
      this.spaceNames.splice(index, 1)
    },
    updateDetail(arr, item, condition) {
      const index = arr.indexOf(item);
      if (condition) {
        if (index === -1) arr.push(item);
      } else {
        if (index !== -1) arr.splice(index, 1);
      }
    },
    // 获取详情
    getDetails() {
      this.$api
        .getQueryDataById({ id: this.$route.query.id })
        .then((res) => {
          this.loading = false
          if (res.code == 200) {
            this.detailsData = res.data
            this.checkedTeamData = this.detailsData.assetsScale.scaleImageIdEquipment
            this.factoryCode = res.data.factoryCode
            const { haveLocation, sysOf1Code } = res.data;
            this.updateDetail(this.dialogDetail, '定位轨迹', haveLocation);
            this.updateDetail(this.dialogDetail, '通行记录', sysOf1Code === 'ZTJX' || sysOf1Code === 'RLLSXT');
            this.updateDetail(this.dialogDetail, '出诊记录', sysOf1Code === 'YWSB');
            this.updateDetail(this.dialogDetail, '就诊记录', sysOf1Code === 'YWSB');
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
    // 控制参数弹窗----------------
    submitParamsGather(data) {
      let obj = {
        [data.id]: data.value
      }
      this.$api.getQueryInstanceFunction(this.$route.query.id, data.metadataTag, obj).then((res) => {
        if (res.status === 200) {
          this.$message.success(res.message)
          this.closeHarvesterDialog()
        } else {
          this.$message.error(res.message)
          this.closeHarvesterDialog()
        }
      })
    },
    closeHarvesterDialog() {
      this.enumDialogShow = false
      this.booleDialogShow = false
      this.DialogShow = false
    }
  }
}
</script>
<style lang="scss" scoped>
.securityOperationMonitor-mode-content {
  height: calc(100% - 16px);
  margin-top: 16px;
  display: flex;

  ::v-deep .monitor-content-left {
    width: 246px;
    height: 100%;
    background: #fff;
    border-radius: 4px;
    padding: 16px;
    overflow: auto;

    p {
      padding: 10px 15px;
      color: #333;
      border-radius: 4px;
      margin-bottom: 0;
      cursor: pointer;
    }

    p.active {
      background: #e6effc;
      color: #3562db;
    }
  }

  .monitor-content-right {
    height: 100%;
    flex: 1;
  }
}

.monitorDetails {
  margin: 16px 16px 16px;
  background: #fff;

  p {
    margin: 0;
  }

  ::v-deep .container-header {
    margin-left: 16px;
  }

  .monitorDetails-heade {
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 13px 16px;
    font-size: 14px;
    font-weight: 500;
    color: #121f3e;
    line-height: 14px;

    .heade-pattern {
      display: flex;

      .pattern-item {
        cursor: pointer;
        font-size: 15px;

        .pattern-icon {
          font-size: 16px;
          margin-right: 6px;
        }
      }

      .pattern-item:last-child {
        margin-left: 16px;
      }
    }

    .el-icon-arrow-left {
      font-size: 13px;
      margin-right: 8px;
      cursor: pointer;
      color: #7f848c;
    }
  }

  ::v-deep .container-header {
    border-radius: 4px 4px 0 0;
  }

  .content-details {
    height: 100%;
    overflow: auto;

    .content-heade {
      padding: 24px 16px 16px;
      background: #fff;
      border-radius: 0 0 4px 4px;
      margin-left: 16px;
      height: 100%;
    }

    .heade-info {
      padding: 0 0 14px 16px;

      .heade-p {
        display: inline-block;
        width: 25%;
        font-size: 14px;
        line-height: 35px;
      }

      .statistics-list {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
      }

      .statistics-content {
        width: 18.3%;
        height: 127px;
        margin: 16px 20px 0 0;
      }

      .statistics-content:hover {
        box-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 2px 7px 3px rgba(0, 0, 0, 0.09);
      }

      .statistics-item {
        padding: 10px 24px;
        border-radius: 4px;
        background: #faf9fc;

        img {
          width: 16px;
          height: 16px;
          margin-top: 10px;
        }

        .item-title {
          font-size: 15px;
          font-weight: 500;
          color: #121f3e;
          margin-top: 10px;
        }

        .item-value {
          height: 40px;
          font-size: 18px;
          font-weight: bold;
          color: #121f3e;
          line-height: 20px;
          margin-top: 5px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          text-overflow: ellipsis;

          &>span {
            margin-left: 4px;
            font-size: 15px;
            font-weight: 500;
            color: #ccced3;
          }
        }
      }

      .control-content {
        width: 18%;
        height: 70px;
        margin-top: 16px;
        display: inline-block;
        margin-right: 20px;

        p {
          width: 100%;
          height: 100%;
          background: #faf9fc;
          line-height: 70px;
          text-align: center;
          font-size: 16px;
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #e4e7ed;
          cursor: pointer;
        }

        p.active {
          background: #e6effc;
          color: #3562db;
          border: 1px solid #3562db;
        }
      }

      .control-item {
        padding: 24px;
        border-radius: 4px;
        background: #faf9fc;

        .item-title {
          font-size: 15px;
          font-weight: 500;
          color: #121f3e;
          margin-top: 5px;
          width: 80px;
        }
      }
    }

    ::v-deep .box-card {
      padding: 0;
      height: auto;

      .card-body {
        overflow: hidden;
      }
    }

    .control-btn-header {
      padding: 0;

      &>div {
        margin-right: 10px;
        margin-top: 10px;
      }

      .btn-item {
        border: 1px solid #3562db;
        color: #3562db;
        font-family: none;
      }

      .btn-active {
        color: #fff;
        background: #3562db;
      }
    }

    .content-main {
      display: flex;
      flex-wrap: wrap;

      ::v-deep .box-card {
        width: calc(50% - 16px);
        margin-left: 16px;
        margin-top: 15px;
        padding: 24px 10px 0 24px;

        .card-body {
          height: 250px !important;
        }
      }
    }
  }
}

.content_item {
  .typeTabs {
    // padding: 0 16px;
    height: 100%;
  }

  .query_row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 16px 0;
    padding: 0 16px;

    .query_left {
      display: flex;
      align-items: center;

      .up_down {
        display: flex;
        margin-right: 6px;

        .jump_date {
          cursor: pointer;
          margin-left: 16px;
          font-size: 14px;
          color: #3562db;
        }

        .no_clicl {
          cursor: not-allowed;
          opacity: 0.5;
          pointer-events: none;
          color: #414653;
        }
      }

      .re_btn {
        margin-left: 10px;
      }
    }

    .query_right {
      display: flex;
      margin-top: 9px;

      .pattern-item {
        cursor: pointer;
        font-size: 15px;
        padding: 5px 15px;
        background: #f6f5fa;

        .pattern-icon {
          font-size: 16px;
          margin-right: 6px;
        }
      }
    }
  }

  .btn_row {
    padding: 0 16px;
    background: #fff;
    border-radius: 4px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    .harmonicOrder {
      display: flex;
      align-items: center;

      span {
        font-size: 14px;
      }
    }
  }

  .choice_row {
    margin: 16px 0;
    padding: 0 16px;
  }

  .my_tabel {
    padding: 0 16px;
    height: calc(100% - 230px);
  }
}
</style>
