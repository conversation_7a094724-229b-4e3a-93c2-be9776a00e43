<template>
  <PageContainer :footer="true">
    <div slot="header" class="file_header">
      <div class="topFilter_left">
        <span>{{ name }}</span>
      </div>
    </div>
    <div slot="content" class="file_content">
      <div class="content-box">
        <div v-if="doctype == 'pdf'" class="content-box">
          <pdf v-for="item in numPages" :key="item" :page="item" :src="fileUrl"></pdf>
        </div>
        <div v-else-if="doctype == 'jpg' || doctype == 'png' || doctype == 'gif'" class="content-box flex-c">
          <el-image style="width: 50%" :src="fileUrl" :preview-src-list="[fileUrl]"> </el-image>
        </div>
        <iframe v-else id="iframe1" frameborder="no" border="0" marginwidth="0" marginheight="0" scrolling="no" allowtransparency="yes" :src="currentProtocol + fileUrl"></iframe>
      </div>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="onCancel">取消</el-button>
    </div>
  </PageContainer>
</template>
<script>
import { Base64 } from 'js-base64'
// import pdf from "vue-pdf";
export default {
  // components: {
  //   pdf,
  // },
  data() {
    return {
      frequencyNum: 0,
      numPages: 0,
      doctype: '',
      fileUrl: '',
      currentProtocol: __PATH.VUE_PREVIEW_URL,
      type: '',
      countDown: '',
      routeInfo: {},
      studyFlag: true
    }
  },
  created() {
    this.routeInfo = JSON.parse(sessionStorage.getItem('routeInfo'))
    // this.routeInfo = JSON.parse(localStorage.getItem("routeInfo"));
    console.log(this.$route.query.url, 'this.$route.query.urlthis.$route.query.url')
    this.url = this.$route.query.url
    this.name = this.$route.query.name
    if (this.url) {
      this.fileUrl = encodeURIComponent(Base64.encode(this.url))
      let newArr = this.fileUrl.split('.')
      this.doctype = newArr[newArr.length - 1]
    } else {
      this.doctype = ''
    }
  },
  mounted() {
    if (this.doctype == 'pdf') {
      this.getNumPages()
    }
  },
  methods: {
    getNumPages() {
      let loadingTask = pdf.createLoadingTask(this.fileUrl)
      loadingTask.promise
        .then((pdf) => {
          this.numPages = pdf.numPages
        })
        .catch((err) => {
          console.error('pdf 加载失败', err)
        })
    },
    // 取消
    onCancel() {
      this.$router.go(-1)
    }
  }
}
</script>
<style lang="scss" scoped>
.file_header {
  background: #fff;
  border-radius: 4px;
  height: 80px;
  padding: 0 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .topFilter_left {
    color: #121f3e;
    height: 30px;
    font-size: 20px;
    // border-bottom: 1px solid #dcdfe6;
    span {
      font-weight: 600;
      margin-left: 20px;
    }
  }
  .topFilter_right {
    width: 225px;
    height: 40px;
    background-color: #ededf5;
    border-radius: 40px;
    line-height: 40px;
    text-align: center;
    font-size: 14px;
    color: #3562db;
    img {
      vertical-align: middle;
    }
    .time {
      color: #333;
    }
  }
}
.file_content {
  margin-top: 16px;
  height: 100%;
  padding: 16px;
  overflow: auto;
  background-color: #fff;
  .content-box {
    width: 100%;
    height: 100%;
  }
  #iframe1 {
    width: 100%;
    height: 100%;
  }
}
.flex-c {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
