<template>
  <el-dialog v-dialogDrag custom-class="model-dialog" append-to-body :visible="visible" :close-on-click-modal="false"
    :before-close="closeDialog" width="50%" title="选择预案">
    <div class="dialog-content">
      <div style="position: relative; padding: 10px; margin-bottom: 10px; background: #fff;border-radius: 4px;">
        <el-input v-model="searchFrom.planName" placeholder="预案名称" clearable style="width: 200px;"></el-input>
        <el-select v-model="searchFrom.planType" placeholder="预案类型" clearable style="margin-left: 10px;"
          @change="getDictionaryList()">
          <el-option v-for="item in planTypeList" :key="item.imhId" :label="item.imhMonitorName"
            :value="item.imhId"></el-option>
        </el-select>
        <el-select v-model="searchFrom.alarmType" placeholder="报警类型" clearable :disabled="!searchFrom.planType"
          style="margin-left: 10px;">
          <el-option v-for="item in alarmTypeList" :key="item.value" :label="item.name" :value="item.value"></el-option>
        </el-select>
        <el-button plain type="primary" class="ml-16" @click="resetForm">重置</el-button>
        <el-button type="primary" @click="searchForm">查询</el-button>
      </div>
      <div style="height: 400px;background: #fff; padding: 10px; border-radius: 4px;">
        <TablePage ref="table" v-loading="tableLoading" :showPage="true" :tableColumn="tableColumn" :data="tableData"
          height="calc(100% - 40px)" :pageData="pageData" :pageProps="pageProps" @pagination="paginationChange">
        </TablePage>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="saveTemplate">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script lang="jsx">
export default {
  name: 'selectPreplan',
  components: {
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selectTemplate: '',
      selectPlanName: '',
      searchFrom: {
        planName: '',
        planType: '',
        alarmType: ''
      },
      planTypeList: [], // 预案类型列表
      tableLoading: false,
      tableColumn: [
        {
          prop: 'select',
          label: '',
          align: 'center',
          width: 60,
          render: (h, row) => {
            return (
              <el-radio v-model={this.selectTemplate} label={row.row.id}  onChange={() => this.handlePrePlanEvent(row.row.planName)}><span></span></el-radio>
            )
          }
        },
        {
          prop: 'icon',
          label: '',
          width: 100,
          render: (h, row) => {
            return (
              <img style="width:48px; height:48px;" src={this.$tools.imgUrlTranslation(JSON.parse(row.row.regulationsFlow)[0]?.url ?? '')}></img>
            )
          }
        },
        {
          width: 180,
          prop: 'planName',
          label: '预案名称',
          render: (h, row) => {
            return (
              <div class="planName">
                <p>{row.row.planName}</p>
                <p>{row.row.regulationsDesc}</p>
              </div>
            )
          }
        },
        {
          width: 120,
          prop: 'planType',
          label: '预案类型',
          formatter: (scope) => {
            return scope.row.planType ? this.planTypeList.find(v => v.imhId == scope.row.planType)?.imhMonitorName : ''
          }
        },
        {
          prop: 'path',
          label: '法规文案',
          showOverflowTooltip: false,
          render: (h, row) => {
            return (
              <div class="twoLinesEllipsis" domPropsInnerHTML={row.row.regulationsText}></div>
            )
          }
        },
        {
          prop: 'regulationsDoc',
          label: '法规文档',
          render: (h, row) => {
            return (
              <div style="cursor: pointer; color: #3562DB;" onClick={() => window.open(this.$tools.imgUrlTranslation(JSON.parse(row.row.regulationsDoc)[0]?.url ?? ''), '_blank')}>
                {JSON.parse(row.row.regulationsDoc)[0]?.name ?? ''}
              </div>
            )
          }
        }
      ],
      tableData: [],
      alarmTypeList: [],
      pageData: {
        current: 1,
        size: 15,
        total: 0
      },
      pageProps: {
        page: 'current',
        pageSize: 'size',
        total: 'total'
      }
    }
  },
  computed: {

  },
  created() {
    this.getAlarmSystem()
    this.getPlanList()
  },
  methods: {
    saveTemplate() {
      if (!this.selectTemplate) this.$message.warning('请选择预案')
      let obj = {
        planId: this.selectTemplate,
        planName: this.selectPlanName
      }
      this.$emit('savePrePlan', obj)
      this.closeDialog()
    },
    // 获取报警类型
    getDictionaryList() {
      this.searchFrom.alarmType = ''
      this.$api.getDictionaryList({ projectCode: this.planTypeList.find(v => v.imhId == this.searchFrom.planType).imhMonitorCode, dictType: 1 }).then((res) => {
        if (res.code == 200) {
          this.alarmTypeList = res.data
        }
      })
    },
    // 获取预案模板列表
    getPlanList() {
      let param = {
        ...this.searchFrom,
        planCategory: '0,1',
        pageSize: this.pageData.size,
        page: this.pageData.current
      }
      this.tableLoading = true
      this.$api.GetPlanList(param).then(res => {
        if (res.code == 200) {
          this.tableData = res.data.records
          this.pageData.total = res.data.total
        }
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    // 获取预案类型
    getAlarmSystem() {
      this.$api.getAlarmSystem({ projectCodes: '' }).then((res) => {
        if (res.code == 200) {
          this.planTypeList = res.data
        }
      })
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', !this.visible)
    },
    // 查询
    searchForm() {
      this.getPlanList()
    },
    // 重置查询
    resetForm() {
      Object.assign(this.$data.searchFrom, this.$options.data().searchFrom)
      this.searchForm()
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getPlanList()
    },
    handlePrePlanEvent(e) {
      this.selectPlanName = e
    }
  }
}

</script>

<style lang="scss" scoped>
.dialog-content {
  overflow: hidden;
  .el-input {
    width: 200px;
  }
  .el-pagination {
    margin-top: 10px;
  }
  .ml-16 {
    margin-left: 16px;
  }
  ::v-deep(.planName) {
    p {
      margin: 0px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>
