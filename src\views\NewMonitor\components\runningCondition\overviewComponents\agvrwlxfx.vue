<!-- 运行监测 -->
<template>
    <ContentCard :title="item.componentTitleShow" :scrollbarHover="true" :cstyle="{ width: '100%', height: '100%' }"
        class="drag_class" :hasMoreOper="['edit']"
        @more-oper-event="(val) => $emit('all-more-Oper', val, item.componentName, item.chartType)">
        <div slot="title-right" class="data-btns">
            <span :class="{ 'active-btn': totalCostDateType == 'all' }" @click="changeTime('all')">全部</span>
            <!-- <span :class="{ 'active-btn': totalCostDateType == 'day' }" @click="changeTime('day')">今日</span> -->
            <span :class="{ 'active-btn': totalCostDateType == 'week' }" @click="changeTime('week')">本周</span>
            <span :class="{ 'active-btn': totalCostDateType == 'month' }" @click="changeTime('month')">本月</span>
            <span :class="{ 'active-btn': totalCostDateType == 'year' }" @click="changeTime('year')">本年</span>
        </div>
        <div slot="content" style="height: 300px;">
            <div class="agvrwlxfx">
                <echarts :ref="`agvrwlxfx`" :domId="`agvrwlxfx`" style="width:100%;height: 100%;" />
            </div>
            <div v-if="!hasChart"
                style="width: 100%; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center; padding: 50px">
                <img src="@/assets/images/newMonitor/no-chat.png" />
                <span>暂无数据</span>
            </div>
        </div>
    </ContentCard>
</template>

<script>
import moment from 'moment'
import * as echarts from 'echarts'
export default {
    name: 'agvrwlxfx',
    props: {
        item: {
            type: Object,
            default: () => { }
        },
        systemCode: {
            type: String,
            default: ''
        },
        deviceId: {
            type: String,
            default: ''
        },
    },
    data() {
        return {
            hasChart: false,
            totalCostDateType: 'all',
            dateTime: {
                startTime: '',
                endTime: '',
            },
        }
    },
    mounted() {
        setTimeout(() => {
            if (this.deviceId) {
                this.getQueryInstanceFunction()
            }
        }, 150)
    },
    methods: {
        changeTime(type) {
            this.totalCostDateType = type
            if (type == 'all') {
                this.dateTime.startTime = ''
                this.dateTime.endTime = ''
            } else if (type == 'day') {
                this.dateTime.startTime = moment().format('YYYY-MM-DD 00:00:00');
                this.dateTime.endTime = moment().format('YYYY-MM-DD 23:59:59');
            } else if (type == 'week') {
                this.dateTime.startTime = moment().startOf('isoWeek').format('YYYY-MM-DD 00:00:00');
                this.dateTime.endTime = moment().endOf('isoWeek').format('YYYY-MM-DD 23:59:59');
            } else if (type == 'month') {
                this.dateTime.startTime = moment().startOf('month').format('YYYY-MM-DD  00:00:00')
                this.dateTime.endTime = moment().endOf('month').format('YYYY-MM-DD  23:59:59')
            } else if (type == 'year') {
                this.dateTime.startTime = moment().startOf('year').format('YYYY-MM-DD  00:00:00')
                this.dateTime.endTime = moment().endOf('year').format('YYYY-MM-DD  23:59:59')
            }
            this.getQueryInstanceFunction()
        },
        getQueryInstanceFunction() {
            this.$api.getQueryInstanceFunction(this.deviceId, 'jobTypes', this.dateTime).then((res) => {
                if (res.status === 200) {
                    this.hasChart = true
                    this.appendEchartsData(res.result[0])
                } else {
                    this.hasChart = false
                }
            })
        },
        appendEchartsData(data) {
            const baseData = {
                tooltip: {
                    trigger: 'item'
                },
                legend: {
                    top: 'center',
                    right: '0%',
                    orient: 'vertical',
                },
                series: [
                    {
                        name: "",
                        type: 'pie',
                        radius: '90%',
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        },
                        data: [],
                        label: {
                            formatter: '{c}', // {b}是名称，{c}是值
                            position: 'outside', // 标签位置
                            show: true // 显示标签
                        }
                    }
                ]
            }
            if (data && data.length > 0) {
                data.forEach((item) => {
                    baseData.series[0].data.push({
                        value: item.jobCount,
                        name: item.jobTypeStr
                    })
                })
            }
            this.$refs[`agvrwlxfx`].init(baseData)
        }
    }
}
</script>
<style lang="scss" scoped>
.data-btns {
    position: absolute;
    right: 7%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &>span {
        width: 44px;
        height: 20px;
        line-height: 20px;
        text-align: center;
        margin: 0 4px;
        background-color: #f6f5fa;
        font-size: 14px;
        font-family: 'PingFang SC-Medium', 'PingFang SC';
        border: none;
        border-radius: 2px;
        color: #7f848c;
        cursor: pointer;
    }

    .active-btn {
        background-color: #e6effc !important;
        color: #3562db !important;
        border-color: #e6effc !important;
    }
}

.operation-list {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
}

.agvrwlxfx {
    flex: 1;
    width: 100%;
    height: 200px;
    margin-top: 5px;
    margin-left: -15px;
}
</style>