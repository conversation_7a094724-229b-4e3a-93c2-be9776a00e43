<template>
  <div class="sino_page">
    <el-container>
      <el-aside>
        <div class="title_box"><svg-icon name="right-arrow" /> 组织结构</div>
        <el-collapse v-model="activeName" style="padding: 0 10px 0 20px;" accordion @change="handelChange">
          <el-collapse-item v-for="(list, index) in collapseData" :key="index" :title="list.unitComName" :name="list.umId">
            <div class="sino_tree_box" style="margin: 0;">
              <el-tree ref="tree" class="filter-tree" :data="treeData" :props="defaultProps" highlight-current node-key="id" @node-click="nodeClick"></el-tree>
            </div>
          </el-collapse-item>
        </el-collapse>
      </el-aside>
      <el-main>
        <div class="sino_table">
          <div class="sino_table_seach" style="display: flex; margin-bottom: 10px;">
            <el-select v-model="filters.stationStatus" class="sino_sdcp_input mr_15" placeholder="请选择在职状态" clearable @change="changeHandlerStatus">
              <el-option v-for="item in stationStatusList" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
            <el-select v-model="filters.postId" class="sino_sdcp_input mr_15" placeholder="请选择岗位" clearable @change="changeHandlerPost">
              <el-option v-for="item in postList" :key="item.id" :label="item.postName" :value="item.id"></el-option>
            </el-select>
            <el-select v-model="filters.sex" class="sino_sdcp_input mr_15" placeholder="请选择性别" clearable @change="changeHandlerSex">
              <el-option v-for="item in sexList" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>

            <el-button v-show="!isDialog" class="float_rt" size="medium" type="primary" @click="AddStaff">新增</el-button>
            <el-button class="float_rt" size="medium" type="primary" @click="exportFn">导出</el-button>
            <el-button class="float_rt" size="medium" type="primary" @click="importFn">导入</el-button>
            <el-button class="float_rt" size="medium" type="primary" @click="upDownFn">导出模板</el-button>
            <el-button class="float_rt" size="medium" type="primary" @click="adQuery">高级查询</el-button>
          </div>
          <div class="sino_table_data">
            <el-table :data="tableData" height="300px" stripe border @selection-change="handleSelectionChange" @row-click="rowClick">
              <el-table-column v-if="checkbox" type="selection" width="50" align="center"></el-table-column>
              <el-table-column v-else align="center" width="50">
                <template slot-scope="scope">
                  <el-radio v-model="radioObj" :label="scope.row">&nbsp;</el-radio>
                </template>
              </el-table-column>
              <el-table-column type="index" label="序号" width="50" align="center">
                <template slot-scope="scope">
                  <span>{{ (pagination.current - 1) * pagination.size + scope.$index + 1 }}</span>
                </template>
              </el-table-column>
              <el-table-column v-for="item in tableHeaders" :key="item.porp" :prop="item.porp" :label="item.label" :width="item.width" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span v-if="item.porp == 'sex'">
                    {{ scope.row.sex == 1 ? '男' : '女' }}
                  </span>
                  <span v-else-if="item.porp == 'stationStatus'">
                    {{ scope.row.stationStatus == 0 ? '在职' : '离职' }}
                  </span>
                  <span v-else-if="item.porp == 'staffName'" class="color_blue" >
                    <!-- @click="ViewFn(scope.row)" -->
                    {{ scope.row.staffName }}
                  </span>
                  <span v-else-if="item.porp == 'activationFlag'">
                    {{ scope.row.activationFlag == 0 ? '已激活' : '未激活' }}
                  </span>
                  <span v-else>{{ scope.row[item.porp] }}</span>
                </template>
              </el-table-column>
              <el-table-column v-if="!isDialog" fixed="right" label="操作" width="150">
                <template slot-scope="scope">
                  <el-button type="text" size="small" @click="EditStaff(scope.row)">编辑</el-button>
                  <el-button type="text" size="small" @click="DelStaff(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination
              :current-page="pagination.current"
              :page-sizes="[15, 30, 50, 100]"
              :page-size="pagination.size"
              :total="total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            ></el-pagination>
          </div>
        </div>
      </el-main>
    </el-container>
    <sinoImportFile :importDialog="importDialog" :addFileName="addFileName" @cancelFile="cancelFile" @getTableData="getUnitListFn" />
    <sino-advanced-search class="advanced-search" :closeState="closeSearch" @close="close" @searchList="searchList" @resetSearch="resetSearch">
      <template slot="content">
        <el-form ref="formInline" class="sino_form" :model="filters" :inline="true" label-width="80px">
          <el-form-item label="在职状态" prop="stationStatus">
            <el-select v-model="filters.stationStatus" class="sino_sdcp_input mr_15" placeholder="请选择在职状态" clearable @change="changeHandlerStatus">
              <el-option v-for="item in stationStatusList" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="岗位" prop="postId">
            <el-select v-model="filters.postId" class="sino_sdcp_input mr_15" placeholder="请选择岗位" clearable @change="changeHandlerPost">
              <el-option v-for="item in postList" :key="item.id" :label="item.postName" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="所属单位" prop="pmId">
            <el-select v-model="filters.pmId" class="sino_form_input" placeholder="请选择所属单位" clearable @change="changeHandlerPm">
              <el-option v-for="item in unitList" :key="item.umId" :label="item.unitComName" :value="item.umId"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="所属部门" prop="officeId">
            <el-select v-model="filters.officeId" class="sino_form_input" placeholder="请选择所属部门" clearable filterable @change="changeHandlerOffice">
              <el-option v-for="item in departList" :key="item.id" :label="item.deptName" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="账号状态" prop="activationFlag">
            <el-select v-model="filters.activationFlag" class="sino_form_input" placeholder="请选择账号状态" clearable @change="changeHandlerFlag">
              <el-option v-for="item in actionList" :key="item.id" :label="item.value" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="人员姓名" prop="staffName">
            <el-input v-model="filters.staffName" class="sino_form_input" placeholder="请输入人员姓名" show-word-limit @change="changeHandlerStaffName"> </el-input>
          </el-form-item>
          <el-form-item label="籍贯" prop="nativePlace">
            <el-input v-model="filters.nativePlace" class="sino_form_input" placeholder="请输入籍贯" show-word-limit @change="changeHandlerNativePlace"> </el-input>
          </el-form-item>
          <el-form-item label="民族" prop="nation">
            <el-select v-model="filters.nation" class="sino_form_input" placeholder="请选择民族" clearable @change="changeHandlerNation">
              <el-option v-for="item in nationList" :key="item.code" :label="item.nation" :value="item.code"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="性别" prop="sex">
            <el-select v-model="filters.sex" class="sino_form_input" placeholder="请选择性别" clearable @change="changeHandlerSex">
              <el-option v-for="item in sexList" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="职工工号" prop="staffNumber">
            <el-input v-model="filters.staffNumber" class="sino_form_input" placeholder="请输入职工工号" show-word-limit @change="changeHandlerStaffNumber"> </el-input>
          </el-form-item>
          <el-form-item label="身份证号" prop="idCard">
            <el-input v-model="filters.idCard" class="sino_form_input" placeholder="请输入身份证号" show-word-limit @change="changeHandlerIdCard"> </el-input>
          </el-form-item>
          <el-form-item label="办公电话" prop="phone">
            <el-input v-model="filters.phone" class="sino_form_input" placeholder="请输入办公电话" show-word-limit @change="changeHandlerPhone"> </el-input>
          </el-form-item>
          <el-form-item label="手机号码" prop="mobile">
            <el-input v-model="filters.mobile" class="sino_form_input" placeholder="请输入手机号码" show-word-limit @change="changeHandlerMobile"> </el-input>
          </el-form-item>
          <!-- <el-form-item label="登录账号" prop="nativePlace">
            <el-input
              class="sino_form_input"
              placeholder="请输入登录账号"
              show-word-limit
              v-model="filters.nativePlace"
            >
            </el-input>
          </el-form-item> -->
        </el-form>
      </template>
    </sino-advanced-search>
  </div>
</template>

<script>
import sinoAdvancedSearch from '../common/sinoAdvancedSearch.vue'
import store from '@/store/index'
import axios from 'axios'
import sinoImportFile from '../common/ImportFile.vue'
export default {
  name: 'staffManagement',
  components: {
    sinoAdvancedSearch,
    sinoImportFile
  },
  props: {
    isDialog: {
      type: Boolean,
      default: false
    },
    nature: {
      type: Boolean,
      default: false // true为院内
    },
    checkbox: {
      type: Boolean,
      default: true
    },
    notInIds: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      activeName: '',
      collapseData: [],
      treeData: [],
      treeLoading: true,
      checkedTreeNode: '',
      defaultProps: {
        label: 'deptName',
        children: 'list'
      },
      filters: {
        stationStatus: '',
        postId: '',
        pmId: '',
        officeId: '',
        activationFlag: '',
        staffName: '',
        nativePlace: '',
        nation: null,
        sex: '',
        staffNumber: '',
        idCard: '',
        phone: '',
        mobile: ''
      },
      unitTypeList: [],
      tableHeight: '',
      tableData: [],
      tableHeaders: [
        {
          porp: 'staffNumber',
          label: '职工工号'
        },
        {
          porp: 'staffName',
          label: '人员姓名'
        },
        {
          porp: 'sex',
          label: '性别'
        },
        {
          porp: 'mobile',
          label: '手机号码'
        },
        {
          porp: 'phone',
          label: '办公电话'
        },
        {
          porp: 'entryData',
          label: '入职天数'
        },
        {
          porp: 'unitName',
          label: '归属单位'
        },
        {
          porp: 'officeName',
          label: '所属部门'
        },
        {
          porp: 'postName',
          label: '岗位'
        },
        {
          porp: 'jobName',
          label: '职务'
        },
        {
          porp: 'stationStatus',
          label: '在职状态'
        },
        {
          porp: 'activationFlag',
          label: '激活状态'
        }
      ],
      multipleSelection: [],
      radioObj: {},
      // -----------------------------Pagination
      pagination: {
        current: 1,
        size: 15
      }, // 分页数据
      total: 0, // 数据总条数
      // -----------------------------table opera
      sexList: [
        { id: 1, name: '男' },
        { id: 2, name: '女' }
      ],
      stationStatusList: [
        { id: 0, name: '在职' },
        { id: 1, name: '离职' }
      ],
      postList: [],
      closeSearch: true,
      unitList: [],
      departList: [],
      actionList: [
        { id: 0, value: '已激活' },
        { id: 1, value: '未激活' }
      ],
      nationList: [],
      importDialog: false,
      addFileName: 'staff'
    }
  },
  computed: {},
  created() {},
  mounted() {
    this.getUnitListFn() // 单位列表
    this.postListFn() // 岗位列表
  },
  methods: {
    //  ----------------------------------------------Tree_Fn
    handelChange(val) {
      this.activeName = val
      this.getDeptListFn(val)
      this.filters.pmId = val
      this.filters.officeId = ''
      this.staffListByPageFn()
    },
    nodeClick(val) {
      this.checkedTreeNode = val
      this.filters.pmId = val.umId
      this.filters.officeId = val.id
      this.staffListByPageFn()
    },
    //  获取单位列表
    getUnitListFn() {
      this.$api.getUnitList({}).then((res) => {
        this.treeLoading = false
        if (res.code == 200) {
          if (this.nature) {
            // 判断院内 / 院外
            res.data.map((list) => {
              console.log(list, 'list')
              // 院内：1，院外：2
              list.nature == 1 ? this.collapseData.push(list) : ''
            })
            this.unitList = this.collapseData
          } else {
            this.collapseData = this.unitList = res.data
          }
          this.staffListByPageFn()
        }
      })
    },
    // 部门列表
    getDeptListFn(unitId) {
      this.treeData = []
      this.$api
        .getDeptList({
          unitId: unitId
        })
        .then((res) => {
          if (res.code == 200) {
            this.departList = res.data
            this.treeData = this.$tools.transData(res.data, 'id', 'pid', 'list')
          }
        })
    },
    // ---------------------------------------------------------- TabelFn
    //  获取人员信息列表
    staffListByPageFn() {
      this.$api
        .staffListByPage({
          notInIds: this.notInIds,
          ...this.filters,
          ...this.pagination,
          nature: this.nature ? 1 : ''
        })
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.records
            this.pagination.current = res.data.current
            this.pagination.size = res.data.size
            this.total = res.data.total
          }
        })
    },
    handleSizeChange(val) {
      this.pagination.current = 1
      this.pagination.size = val
      this.staffListByPageFn()
    },
    handleCurrentChange(val) {
      this.pagination.current = val
      this.staffListByPageFn()
    },
    AddStaff() {
      this.$router.push({
        path: '/staffMess',
        query: { type: 'Add' }
      })
    },
    EditStaff(val) {
      this.$router.push({
        path: '/staffMess',
        query: { type: 'Edit', id: val.id }
      })
    },
    DelStaff(val) {
      this.$confirm('确认删除选中的人员信息吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$api
            .deleteStaff({
              id: val.id
            })
            .then((res) => {
              if (res.code == 200) {
                this.staffListByPageFn()
                this.$message({
                  message: res.msg,
                  type: 'success'
                })
              }
            })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    ViewFn(val) {
      this.$router.push({
        path: '/staffMess',
        query: { type: 'View', id: val.id }
      })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    rowClick(val) {
      this.radioObj = val
      console.log(this.radioObj, 'this.radioObj')
    },
    //  导出
    exportFn() {
      let staffPageListReqVo = {
        ...this.filters,
        size: this.pagination.size
      }
      let Ids = []
      this.multipleSelection.forEach((item) => {
        Ids.push(item.id)
      })
      staffPageListReqVo.ids = Ids.join(',')
      const userInfo = store.state.user.userInfo.user

      let menuList = this.$store.state.menu.routes
      let firstTitle = ''
      let routeList = []
      this.$router.currentRoute.matched.filter(item => item.name).forEach(el => {
        routeList.push(el.meta.title)
      })
      if (menuList.length) {
        firstTitle = menuList[this.$store.state.menu.headerActived].meta.title
        routeList.unshift(firstTitle)
      }

      axios({
        method: 'post',
        url: __PATH.VUE_SPACE_API + 'hospitalStaff/hospital-staff/exportList',
        data: staffPageListReqVo,
        responseType: 'arraybuffer',
        headers: {
          'Content-Type': 'application/json;charset=utf-8',
          Authorization: 'Bearer ' + store.state.user.token,
          hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
          unitCode: userInfo.unitCode ?? 'BJSYGJ',
          'operation-type': 4,
          'operation-content': encodeURIComponent(routeList.join(','))
        }
      })
        .then((res) => {
          this.$message.success(res.message || '导出成功')
          this.$tools.downloadFile(res, this)
        })
        .catch((res) => {
          this.$message.error(res.message || '导出失败')
        })
    },
    //  模板下载
    upDownFn() {
      const userInfo = store.state.user.userInfo.user
      axios({
        method: 'post',
        url: __PATH.VUE_SPACE_API + 'hospitalStaff/hospital-staff/exportStaffModel',
        data: '',
        responseType: 'arraybuffer',
        headers: {
          'Content-Type': 'application/json;charset=utf-8',
          Authorization: 'Bearer ' + store.state.user.token,
          hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
          unitCode: userInfo.unitCode ?? 'BJSYGJ'
        }
      })
        .then((res) => {
          this.$message.success(res.message || '导出成功')
          this.$tools.downloadFile(res, this)
        })
        .catch((res) => {
          this.$message.error(res.message || '导出失败')
        })
    },
    //  导入
    importFn() {
      this.importDialog = true
    },
    cancelFile() {
      this.importDialog = false
    },
    // ------------------------------------------------------------ Table_Search
    //  获取岗位列表
    postListFn() {
      this.$api.selectByList().then((res) => {
        if (res.code == 200) {
          this.postList = res.data
        }
      })
    },

    changeHandlerStatus(val) {
      this.filters.stationStatus = val
      this.staffListByPageFn()
    },
    changeHandlerPost(val) {
      this.filters.postId = val
      this.staffListByPageFn()
    },
    changeHandlerSex(val) {
      this.filters.sex = val
      this.staffListByPageFn()
    },
    // ------------------------------------------高级搜索
    changeHandlerPm(val) {
      this.getDeptListFn(val)
      this.filters.pmId = val
      this.staffListByPageFn()
    },
    changeHandlerOffice(val) {
      this.filters.officeId = val
      this.staffListByPageFn()
    },
    changeHandlerFlag(val) {
      this.filters.activationFlag = val
      this.staffListByPageFn()
    },
    changeHandlerStaffName(val) {
      this.filters.staffName = val
      this.staffListByPageFn()
    },
    changeHandlerNativePlace(val) {
      this.filters.nativePlace = val
      this.staffListByPageFn()
    },
    changeHandlerNation(val) {
      this.filters.nation = val
      this.staffListByPageFn()
    },
    changeHandlerStaffNumber(val) {
      this.filters.staffNumber = val
      this.staffListByPageFn()
    },
    changeHandlerIdCard(val) {
      this.filters.idCard = val
      this.staffListByPageFn()
    },
    changeHandlerPhone(val) {
      this.filters.phone = val
      this.staffListByPageFn()
    },
    changeHandlerMobile(val) {
      this.filters.mobile = val
      this.staffListByPageFn()
    },
    // 民族列表
    getNationListFn() {
      this.$api.getNationList({}).then((res) => {
        if (res.code == 200) {
          this.nationList = res.data
        }
      })
    },
    adQuery() {
      this.closeSearch = !this.closeSearch
      this.getNationListFn()
    },
    searchList() {
      this.closeSearch = true
      this.staffListByPageFn()
    },
    close(data) {
      this.closeSearch = data
    },
    resetSearch() {
      this.filters = {
        stationStatus: '',
        postId: '',
        pmId: '',
        officeId: '',
        activationFlag: '',
        staffName: '',
        nativePlace: '',
        nation: null,
        sex: '',
        staffNumber: '',
        idCard: '',
        phone: '',
        mobile: ''
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.el-main {
  padding: 6px 24px 16px;
  overflow: hidden;

  .sino_panel {
    height: 100%;
    border-radius: 10px;
    background: #fff;
    position: relative;
    font-size: 14px;
  }

  .sino_page {
    height: 100%;
    position: relative;

    .el-aside {
      width: 260px;
      margin: 0 16px 0 0;
      overflow: hidden;
    }

    .sino_page_left {
      margin: 0 0 0 16px !important;
    }

    .el-aside,
    .el-main {
      // height: 100%;
      background: #fff;
      border-radius: 10px;
      padding: 0;

      .el-collapse {
        height: calc(100% - 40px);
        overflow: auto;
      }
    }
  }
}

.title_box {
  box-sizing: border-box;
  padding-left: 24px;
  height: 50px;
  width: 100%;
  line-height: 50px;
  text-align: left;
  border-bottom: 1px solid #d8dee7;

  .title-tip {
    display: inline-block;
    width: 4px;
    height: 16px;
    margin-right: 8px;
    position: relative;
    top: 2px;
    background: #5188fc;
  }

  .title_name {
    font-size: 16px;
    font-weight: bold;
  }

  .title_btn_icon {
    float: right;
  }

  .title_btn_icon i {
    margin-right: 20px;
    cursor: pointer;
  }
}

.sino_sdcp_input {
  width: 150px;
  margin-right: 10px;
}

.el-collapse {
  height: 330px;
  overflow: auto;
}
</style>
