<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    title="管理密码配置"
    width="30%"
    :visible="visible"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <div class="content">
      <el-form ref="ruleForm" :model="form" :rules="rules" label-width="auto">
        <el-form-item prop="psdConfig" label="管理密码">
          <el-input v-model.trim="form.psdConfig" placeholder="请输入密码" maxlength="6"> </el-input>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="confirm('ruleForm')">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  name: 'psdConfigDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        psdConfig: ''
      },
      rules: {
        psdConfig: [{ required: true, message: '请输入管理密码', trigger: 'change' }]
      }
    }
  },
  mounted() {
    this.getManagePsd()
  },
  methods: {
    // 初始化获取管理密码
    getManagePsd() {
      this.$api.supplierAssess.getAttendanceTerminalManagePassword().then((res) => {
        if (res.code == 200) {
          this.form.psdConfig = res.data
        }
      })
    },
    // 关闭弹窗
    closeDialog() {
      this.form.psdConfig = ''
      this.$emit('close')
    },
    confirm(formName) {
      let params = {
        password: this.form.psdConfig
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$api.supplierAssess.attendanceTerminalManagePassword(params).then((res) => {
            if (res.code == 200) {
              this.$message.success('设置考勤密码成功')
              this.$emit('close')
            } else {
              this.$message.error(res.msg)
              this.$emit('close')
            }
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  max-height: 300px !important;
  overflow: auto;
  background-color: #fff !important;
  padding: 10px 35px;
}
.model-dialog {
  padding: 0 !important;
}
::v-deep .el-form-item__error {
  height: 20px;
  width: 300px;
  color: #f56c6c;
  font-size: 12px;
  line-height: 20px;
  padding-top: 4px;
  position: absolute;
  left: 0;
}
::v-deep .el-dialog__body {
  padding: 0 !important;
}
</style>
