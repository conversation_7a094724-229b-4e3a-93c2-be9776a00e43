<template>
  <div class="wing-container">
    <div class="table-search">
      <div class="table-search-left">
        <el-input v-model="searchInfo.userNameOrEmployeeId" style="width: 200px; margin-right: 8px" placeholder="姓名、工号" maxlength="60"></el-input>
        <el-select v-model="searchInfo.deptCode" filterable clearable placeholder="所属科室" style="margin-right: 8px" @change="changeDeptName">
          <el-option v-for="item in deptList" :key="item.id" :label="item.deptName" :value="item.id"> </el-option>
        </el-select>
        <el-select v-model="searchInfo.applicationTypeId" placeholder="申请类型" style="width: 200px; margin-right: 8px">
          <el-option v-for="item in applicationTypeOptions" :key="item.dictCode" :label="item.dictName" :value="item.dictCode"> </el-option>
        </el-select>
        <el-date-picker
          v-model="searchInfo.date"
          type="datetimerange"
          start-placeholder="申请时间"
          range-separator="至"
          style="width: 380px; margin-right: 8px"
          value-format="yyyy-MM-dd HH:mm:ss"
          end-placeholder="申请时间"
          :default-time="['00:00:00', '23:59:59']"
        >
        </el-date-picker>
      </div>
      <div class="table-search-right">
        <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db; margin-left: 20px" @click="resetCondition">重置</el-button>
        <el-button type="primary" style="font-size: 14px" @click="searchByCondition">查询</el-button>
      </div>
    </div>
    <div class="btn-box">
      <el-button type="primary" style="font-size: 14px; background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db" @click="handExport">导出</el-button>
    </div>
    <div class="table-box">
      <div class="table-list">
        <el-table
          ref="materialTable"
          v-loading="tableLoading"
          :data="tableData"
          height="calc(100% - 10px)"
          border
          :header-cell-style="{ background: '#F6F5FA' }"
          style="width: 100%"
          :cell-style="{ padding: '8px 0 8px 0' }"
          stripe
          highlight-current-row
          :empty-text="emptyText"
          row-key="id"
          @row-dblclick="handRowDblclick"
        >
          <el-table-column label="序号" type="index" width="70">
            <template slot-scope="scope">
              <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="userName" label="姓名"></el-table-column>
          <el-table-column show-overflow-tooltip prop="userEmployeeId" label="工号"></el-table-column>
          <el-table-column show-overflow-tooltip prop="userGender" label="性别">
            <template slot-scope="scope">
              {{ scope.row.userGender | sexFilter }}
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="userIdCard" label="身份证号"></el-table-column>
          <el-table-column show-overflow-tooltip prop="userEntryTime" label="入职日期"></el-table-column>
          <el-table-column show-overflow-tooltip prop="userDepartment" label="所属科室"></el-table-column>
          <el-table-column show-overflow-tooltip prop="processName" label="申请类型" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="allocationRoomName" label="分配房源" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="processInstanceId" label="流程ID" align="center"></el-table-column>
          <el-table-column label="操作" align="center" width="120">
            <template slot-scope="scope">
              <el-link type="danger" @click="handRushDoClick(scope.row)"> 催办 </el-link>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="table-pagination" style="padding-top: 10px; text-align: right">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="paginationData.currentPage"
          :page-sizes="[15, 30, 50, 100]"
          :page-size="paginationData.pageSize"
          :total="paginationData.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>
  <script>
import axios from 'axios'
export default {
  components: {},
  data() {
    return {
      tableLoading: false,
      emptyText: '暂无数据',
      searchInfo: {
        userNameOrEmployeeId: '', // 姓名或工号
        applicationTypeId: '', // 申请类型
        deptCode: '', // 部门
        deptName: '',
        date: [] // 申请时间
      },
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      deptList: [],
      applicationTypeOptions: [],
      tableData: []
    }
  },
  filters: {
    sexFilter(val) {
      let sex = {
        1: '男',
        2: '女'
      }
      if (!val) return ''
      if (val === '男') return val
      if (val === '女') return val
      return sex[val]
    },
    acceptAdjustmentFilter(val) {
      if (!val) return ''
      return val == 1 ? '是' : '否'
    }
  },
  mounted() {
    this.getDeptDataList()
    this.getTypeDataList()
    this.searchByCondition()
  },
  methods: {
    // 双击进详情
    handRowDblclick(row) {
      this.$router.push({
        path: 'allocationDetail',
        query: {
          id: row.id || '',
          activeName: '0'
        }
      })
    },
    // 导出
    handExport() {
      const userInfo = this.$store.state.user.userInfo.user
      const params = {
        pageSize: this.paginationData.pageSize,
        pageNum: this.paginationData.currentPage,
        queryType: '3',
        userNameOrEmployeeId: this.searchInfo.userNameOrEmployeeId,
        userDepartmentId: this.searchInfo.deptCode,
        userDepartment: this.searchInfo.deptName,
        userName: userInfo.staffName,
        userId: userInfo.staffId,
        processType: this.searchInfo.applicationTypeId,
        startTime: this.searchInfo.date.length ? this.searchInfo.date[0] : '',
        endTime: this.searchInfo.date.length ? this.searchInfo.date[1] : '',
        unitCode: userInfo.unitCode ?? 'BJSYGJ',
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY'
      }
      axios({
        method: 'post',
        url: __PATH.VUE_RHMS_API + 'allocation/houseAllocationExport',
        data: params,
        responseType: 'blob',
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token,
          unitCode: userInfo.unitCode ?? 'BJSYGJ',
          hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY'
        }
      })
        .then((res) => {
          const name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
          const blob = new Blob([res.data])
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
        })
        .catch(() => {
          this.$message.error('下载失败')
        })
    },
    changeDeptName(val) {
      if (val) {
        this.searchInfo.deptName = this.deptList.find((i) => i.id === val).deptName
      } else {
        this.searchInfo.deptName = ''
      }
    },
    /** 重置 */
    resetCondition() {
      this.paginationData = {
        currentPage: 1,
        pageSize: 15,
        total: 0
      }
      this.searchInfo = {
        userNameOrEmployeeId: '',
        applicantId: '',
        deptCode: '',
        deptName: '',
        date: []
      }
      this.getTableData()
    },
    /** 查询 */
    searchByCondition() {
      this.paginationData.currentPage = 1
      this.getTableData()
    },
    /** 获取列表 */
    getTableData() {
      const userInfo = this.$store.state.user.userInfo.user
      let params = {
        pageSize: this.paginationData.pageSize,
        pageNum: this.paginationData.currentPage,
        queryType: '3',
        userNameOrEmployeeId: this.searchInfo.userNameOrEmployeeId,
        userDepartmentId: this.searchInfo.deptCode,
        userDepartment: this.searchInfo.deptName,
        userName: userInfo.staffName,
        userId: userInfo.staffId,
        processType: this.searchInfo.applicationTypeId,
        startTime: this.searchInfo.date.length ? this.searchInfo.date[0] : '',
        endTime: this.searchInfo.date.length ? this.searchInfo.date[1] : ''
      }
      this.tableLoading = true
      this.$api.rentalHousingApi
        .getRoomAllocationList(params)
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.records
            this.paginationData.total = res.data.total
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取失败'))
        .finally(() => (this.tableLoading = false))
    },
    /** 催办 */
    handRushDoClick(row) {
      this.$confirm('确认发送催办短信？', '发送通知', { type: 'warning' }).then(() => {
        let params = {
          id: row.id || ''
        }
        this.$api.rentalHousingApi
          .submitUrge(params)
          .then((res) => {
            if (res.code === '200') {
              this.$message.success('催办成功')
              this.getTableData()
            } else {
              throw res.message
            }
          })
          .catch((msg) => msg && this.$message.error(msg || '催办失败'))
      })
    },
    /** 获取科室 */
    getDeptDataList() {
      this.$api.getSelectedDept().then((res) => {
        if (res.code == '200') {
          this.deptList = res.data
        }
      })
    },
    /** 获取申请类型 */
    getTypeDataList() {
      const params = {
        pageSize: 99999,
        pageNum: 1,
        dictName: '',
        dictType: 'shenqingleixing'
      }
      this.$api.rentalHousingApi
        .queryDictPage(params)
        .then((res) => {
          if (res.code === '200') {
            this.applicationTypeOptions = res.data.records
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取列表数据失败'))
        .finally(() => {})
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.getTableData()
    },
    // 每页的条数
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.paginationData.currentPage = 1
      this.getTableData()
    }
  }
}
</script>
  <style lang="scss" scoped>
.wing-container {
  height: 100%;
  .table-search {
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    .table-search-left,
    .table-search-right {
      flex-shrink: 0;
    }
    .table-search-left {
      display: flex;
      align-items: center;
    }
  }
  .btn-box {
    margin-bottom: 10px;
  }
  .table-box {
    height: calc(100% - 82px);
    .table-list {
      height: calc(100% - 82px);
    }
  }
}
</style>