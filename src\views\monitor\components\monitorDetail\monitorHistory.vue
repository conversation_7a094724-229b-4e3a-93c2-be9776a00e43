<!--
 * @Description:
-->
<template>
  <div v-loading="loading" class="monitor-history">
    <div v-if="currentPattern == 1" class="history-echarts">
      <div v-if="detailsData.length" class="history-have-echarts">
        <ContentCard v-for="(item, index) in detailsData" :key="item.paramId" :title="item.paramName + (item.unit || '')">
          <echarts slot="content" :ref="'chart' + index" onTimeLineChange :domId="'chart' + index" @timelinechanged="(v) => timelinechanged(index, v)" />
        </ContentCard>
      </div>
      <div v-else class="empty-box"><span>暂无数据</span></div>
    </div>
    <div v-if="currentPattern == 2" class="history-table">
      <TablePage
        ref="table"
        border
        :showPage="true"
        :tableColumn="tableColumn"
        :data="tableData"
        height="calc(100% - 40px)"
        :pageData="pageData"
        :pageProps="pageProps"
        @pagination="paginationChange"
      >
      </TablePage>
    </div>
  </div>
</template>
<script>
export default {
  name: 'monitorHistory',
  props: {
    currentPattern: {
      type: Number,
      default: 2
    },
    searchForm: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      loading: false,
      detailsData: {},
      tableColumn: [
        {
          prop: 'time',
          label: '时间',
          width: 300
        },
        {
          prop: 'harvesterName',
          label: '传感器',
          width: 300
        },
        {
          prop: 'data',
          label: '监测参数'
        }
      ],
      tableData: [],
      pageData: {
        current: 1,
        size: 15,
        total: 0
      },
      pageProps: {
        page: 'current',
        pageSize: 'size',
        total: 'total'
      }
    }
  },
  watch: {
    currentPattern: {
      handler() {
        this.getDetails()
      }
    }
  },
  activated() {
    // this.getDetails()
  },
  mounted() {
    this.getDetails()
  },
  methods: {
    // 获取详情
    getDetails() {
      this.loading = true
      this.pageData.current = 1
      if (this.currentPattern == 1) {
        this.getEchartsData()
      } else {
        this.getTableData()
      }
    },
    getEchartsData() {
      let { projectCode, type, monitorParams, surveyCode, dataRange } = this.searchForm
      const params = {
        paramId: monitorParams,
        surveyCode,
        type,
        startTime: dataRange[0],
        endTime: dataRange[1]
      }
      this.$api
        .getChartData(params)
        .then((res) => {
          this.loading = false
          if (res.code == 200) {
            this.detailsData = res.data
            this.$nextTick(() => {
              ;(this.detailsData || []).forEach((item, index) => {
                this.$refs['chart' + index][0].init(this.chartOptions(item.data))
                // if (item.unit) {
                //   this.$refs['chart' + index][0].init(this.chartOptions(item, this.searchForm.timeType == 2 ? Number(res.data.yearMouth[res.data.yearMouth.length - 1]) - 1 : 0))
                // } else {
                //   this.$refs['chart' + index][0].init(this.heatChart(item, this.searchForm.timeType == 2 ? Number(res.data.yearMouth[res.data.yearMouth.length - 1]) - 1 : 0))
                // }
              })
            })
            console.log(this.detailsData)
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
    getTableData() {
      this.loading = false
      let { projectCode, monitorParams, surveyCode, dataRange } = this.searchForm
      const params = {
        page: this.pageData.current,
        pageSize: this.pageData.size,
        // projectCode: projectCode,
        paramId: monitorParams,
        surveyCode,
        startTime: dataRange[0],
        endTime: dataRange[1]
      }
      this.$api.getHistoryRealMonitorData(params).then((res) => {
        if (res.code == '200') {
          this.tableData = res.data.records
          this.pageData.total = res.data.total
        } else {
          this.tableData = []
          this.pageData.total = 0
        }
      })
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getTableData()
    },
    // 图表分页
    timelinechanged(chartIndex, month) {
      let { projectCode, timeType, dataRange } = this.searchForm
      let params = {
        startTime: dataRange[0],
        endTime: dataRange[1],
        timeType,
        yearMouth: month.currentIndex + 1,
        paramId: this.detailsData.list[chartIndex].paramId,
        ...this.$route.query
      }
      this.$api.YearTimeByParamId(params).then((res) => {
        if (res.code == 200) {
          if (res.data.unit) {
            this.$refs['chart' + chartIndex][0].init(this.chartOptions(res.data, month.currentIndex))
          } else {
            this.$refs['chart' + chartIndex][0].init(this.heatChart(res.data, month.currentIndex))
          }
        }
      })
    },
    heatChart(item, index = 0) {
      let option
      if (item.valueList.length) {
        option = {
          tooltip: {
            formatter: '{a} <br> {b}'
          },
          dataZoom: [
            {
              type: 'inside',
              start: 0,
              end: 100
            }
          ],
          grid: {
            left: '20',
            right: '70',
            bottom: this.searchForm.timeType == 2 ? '60' : '25',
            top: '20',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            splitArea: {
              show: false
            }
          },
          yAxis: {
            type: 'category',
            data: item.dictList,
            axisLabel: {
              color: '#999',
              textStyle: {
                fontSize: 12
              }
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#F3F4F4'
              }
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            splitArea: {
              show: false
            }
          },
          visualMap: {
            show: false,
            min: 0,
            max: 5,
            inRange: {
              color: ['#5291FF', '#C7DBFF']
            }
          },
          series: [
            {
              name: item.paramName,
              type: 'heatmap',
              data: item.valueList.map((v) => [v.time, item.dictList.indexOf(v.valueName), parseInt(v.value)]),
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      if (this.searchForm.timeType == 2) {
        option.timeline = {
          axisType: 'category',
          realtime: false,
          autoPlay: false,
          controlStyle: {
            showPlayBtn: false
          },
          left: 20,
          right: 20,
          currentIndex: index,
          data: this.detailsData?.yearMouth ?? [],
          label: {
            formatter: function (s) {
              return Number(s) + '月'
            }
          }
        }
      }
      return option
    },
    chartOptions(item) {
      let option
      if (item.length && item[0].dataList.length) {
        const seriesData = []
        const xAxisData = []
        const color = ['#FA403C', '#00BC6D', '#3562DB']
        item.forEach((e) => {
          xAxisData.push(e.time)
          e.dataList.forEach((params, i) => {
            if (seriesData.length < e.dataList.length) {
              seriesData.push([])
              seriesData[i] = {
                name: params.name,
                type: 'line',
                data: [
                  {
                    name: e.time,
                    value: params.value,
                    unit: params.unit
                  }
                ],
                itemStyle: {
                  normal: {
                    borderWidth: 5,
                    color: color[i] || this.$tools.randomRgbColor()
                  }
                }
              }
            } else {
              seriesData[i].data.push({
                name: e.time,
                value: params.value,
                unit: params.unit
              })
            }
          })
        })
        console.log(seriesData)
        option = {
          backgroundColor: '#fff',
          color: ['#73A0FA', '#73DEB3', '#FFB761'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              crossStyle: {
                color: '#999'
              },
              lineStyle: {
                type: 'dashed'
              }
            },
            formatter: function (params) {
              let returnStr = params[0].name + '<br>'
              params.forEach((item) => {
                returnStr += item.seriesName + ': ' + (item.data.value || '-') + ' ' + (item.data.unit || '') + '<br>'
              })
              return returnStr
            }
          },
          dataZoom: [
            {
              type: 'inside',
              start: 0,
              end: 100
            }
          ],
          grid: {
            left: '20',
            right: '70',
            bottom: '25',
            top: '20',
            containLabel: true
          },
          xAxis: {
            // type: 'time',
            data: xAxisData,
            boundaryGap: false,
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            }
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              color: '#999',
              textStyle: {
                fontSize: 12
              }
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#F3F4F4'
              }
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            }
          },
          series: seriesData
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      return option
    }
  }
}
</script>
<style lang="scss" scoped>
.monitor-history {
  width: 100%;
  height: 100%;
  .history-echarts,
  .history-table {
    width: 100%;
    height: 100%;
    padding: 0 16px 10px 16px;
    background: #fff;
  }
  .history-echarts {
    overflow-y: scroll;
    .history-have-echarts {
      width: 100%;
      // height: 100%;
      display: flex;
      flex-wrap: wrap;
      ::v-deep .box-card {
        width: calc(50% - 16px);
        margin-right: 16px;
        margin-top: 15px;
        padding: 24px 10px 0 24px;
        .card-body {
          height: 250px !important;
        }
      }
    }
    .empty-box {
      width: 100%;
      height: 100%;
      background: #fff;
      display: flex;
      > span {
        margin: auto;
        color: #909399;
      }
    }
  }
}
</style>
