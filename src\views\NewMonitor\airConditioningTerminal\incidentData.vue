<template>
    <div>
        <div class="content_item" v-if="eventList.length">
            <div class="content-left">
                <p v-for="(item, index) in eventList" :key="index" @click="clickEvent(item, index)"
                    :class="{ 'active': currentSelectedIndex === index }">{{ item.metadataName }}</p>
            </div>
            <div class="content-right">
                <TablePage ref="table" v-loading="tableLoading" :showPage="true" :tableColumn="tableColumn" height="480"
                    :data="tableData" :pageData="pageData" @pagination="paginationChange" />
            </div>
        </div>
        <div v-if="eventList.length === 0"
            style="width: 100%; height: 100%; display: flex; flex-direction: column;  align-items: center; justify-content: center; padding: 50px;margin-top:80px;">
            <img src="@/assets/images/newMonitor/no-chat.png" />
            <span style="color: #909399;">暂无数据</span>
        </div>
    </div>
</template>
<script lang="jsx">
import moment from 'moment'
import { monitorTypeList } from '@/util/dict.js'
moment.locale('zh-cn')
export default {
    name: 'monitorDetails',
    data() {
        return {
            eventList: [],
            currentSelectedIndex: 0,
            tableData: [],
            pageData: {
                page: 1,
                pageSize: 15,
                total: 0
            },
            tableLoading: false,
            activeEnergyList: [], // 事件对应的数据
        }
    },
    computed: {
        tableColumn() {
            return this.listData.map(item => {
                const column = {
                    prop: item.id,
                    label: item.name,
                     width: item.valueType && item.valueType.type === 'date' ? '200px' : 'auto', // 设置宽度
                    render: (h, { row }) => {
                        const valueId = row[item.id];
                        const valueAsString = valueId !== undefined && valueId !== null ? String(valueId) : '';
                        if (item.valueType) {
                            switch (item.valueType.type) {
                                case 'enum':
                                    const elements = item.valueType.elements ?
                                        item.valueType.elements.find(el => el.value === valueAsString) : null;
                                    return h('span', elements ? elements.text : '');
                                case 'boolean':
                                    if (row[item.id] === undefined || row[item.id] === null) {
                                        return h('span', '');
                                    }
                                    return h('span', row[item.id] == 'true' ? item.valueType.trueText : item.valueType.falseText);
                                case 'date':
                                    if (valueId) {
                                        return h('span', moment(valueId).format('YYYY-MM-DD HH:mm:ss'));
                                    }
                                    return h('span', '');
                                default:
                                    return h('span', row[item.id]);
                            }
                        }
                        return h('span', row[item.id]);
                    }
                };
                return column;
            });
        }
    },
    mounted() {
        this.getCustomEvent()
    },
    methods: {
        // 获取事件列表
        getCustomEvent() {
            this.$api.getCustomEvents(this.$route.query.id).then(res => {
                if (res.code === '200') {
                    this.eventList = res.data
                    this.eventList.map(item => {
                        return item.valueType = JSON.parse(item.valueType)
                    })
                    this.listData = this.eventList[0]?.valueType.properties
                    this.getQueryInstanceEvent(this.eventList[0]?.factoryCode, this.eventList[0]?.metadataTag)
                }
            })
        },
        // 根据事件获取table
        getQueryInstanceEvent(deviceId, eventId) {
            let param = {
                pageSize: this.pageData.pageSize,
                pageIndex: this.pageData.page - 1,
            }
            this.$api.getQueryInstanceEvent(deviceId, eventId, param).then(res => {
                if (res.status === 200) {
                    this.tableData = res.result?.data
                    this.pageData.total = res.result?.total
                }
            })
        },
        // 改变选中
        clickEvent(val, index) {
            this.currentSelectedIndex = index;
            this.listData = this.eventList[index].valueType.properties
            this.getQueryInstanceEvent(val.factoryCode, val.metadataTag)
        },
        paginationChange(pagination) {
            Object.assign(this.pageData, pagination)
            this.getApplicationList()
        },

    }
}
</script>
<style lang="scss" scoped>
.content_item {
    height: 100%;
    display: flex;
    margin-top: 15px;

    .content-left {
        width: 15%;
        margin-right: 10px;
        font-size: 16px;

        p {
            padding: 10px 15px;
            color: #333;
            border-radius: 4px;
            margin-bottom: 0;
            cursor: pointer;
        }

        p.active {
            background: #E6EFFC;
            color: #3562DB;
        }
    }

    .content-right {
        width: 85%;
        border-left: 1px solid #ddd;
        padding-left: 10px;
    }

}
</style>