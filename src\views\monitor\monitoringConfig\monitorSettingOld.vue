<template>
  <PageContainer>
    <div slot="content" class="monitor-setting">
      <div class="monitor-setting-left">
        <ContentCard title="监测实体配置" :cstyle="{ height: '100%' }">
          <span slot="title-right" class="select-servive" @click="handleTeamEvent('add', 1)"><i class="el-icon-plus"></i>分组</span>
          <el-tree
            slot="content"
            ref="teamTree"
            class="team-tree"
            :check-strictly="true"
            :data="teamTreeData"
            :props="defaultProps"
            node-key="id"
            :expand-on-click-node="false"
            :highlight-current="true"
            :render-content="renderContent"
            :default-expanded-keys="expandedTeam"
            @node-click="handleTeamClick"
            @node-expand="(data) => expandedTeam.push(data.id)"
            @node-collapse="(data) => (expandedTeam = expandedTeam.filter((item) => item.id == data.id))"
          ></el-tree>
        </ContentCard>
      </div>
      <div class="monitor-setting-right">
        <div class="right-heade">
          <el-input v-model="searchFrom.sensorName" placeholder="监测实体" suffix-icon="el-icon-search" clearable />
          <el-input v-model="searchFrom.parameterName" placeholder="监测参数" suffix-icon="el-icon-search" clearable />
          <el-input v-model="searchFrom.harvesterName" placeholder="传感器名称" suffix-icon="el-icon-search" clearable />
          <div style="display: inline-block">
            <el-button type="primary" plain @click="resetForm">重置</el-button>
            <el-button type="primary" @click="searchForm">查询</el-button>
          </div>
        </div>
        <div class="right-content">
          <div class="btns-group">
            <div>
              <el-tooltip ref="tooltip" transition="tooltip" :disabled="!multipleSelection.length" popper-class="mut-tooltip" effect="light" placement="right">
                <div slot="content" class="group-config-type" @click.stop>
                  <el-tree
                    :check-strictly="true"
                    :data="teamTreeData"
                    default-expand-all
                    :props="defaultProps"
                    node-key="id"
                    :highlight-current="false"
                    @node-click="selectGroupConfig"
                  ></el-tree>
                </div>
                <el-button type="primary" style="width: auto" :disabled="!multipleSelection.length">批量修改分组至<i class="el-icon-arrow-right el-icon--right"></i></el-button>
              </el-tooltip>
              <el-button type="primary" plain :disabled="!multipleSelection.length" @click="delMonitor">删除</el-button>
            </div>
            <div class="btns-group-control">
              <el-button type="primary" @click="handleMonitorEvent('add', {})">添加</el-button>
              <el-button v-if="monitorData.hasImportBtn" type="primary" @click="exportTemplate()">下载模板</el-button>
              <el-upload v-if="monitorData.hasImportBtn" action="" :show-file-list="false" accept=".xlsx, .xls" :http-request="importMonitor">
                <el-button type="primary" plain>导入</el-button>
              </el-upload>
              <!-- <el-upload v-if="monitorData.hasImportBtn" action="" :show-file-list="false" accept=".xlsx, .xls"
                :http-request="importMonitorScada">
                <el-button type="primary" plain>导入SCADA<el-tooltip placement="top">
                    <i class="el-icon-warning"></i>
                    <div slot="content" class="popper">必填项为监测实体名称，参数名称</div>
                  </el-tooltip></el-button>
              </el-upload> -->
            </div>
          </div>
          <div class="table-content">
            <TablePage
              ref="tablePage"
              v-loading="tableLoading"
              :tableColumn="tableColumn"
              :data="tableData"
              border
              height="calc(100% - 40px)"
              :showPage="true"
              :pageData="pageData"
              :span-method="objectSpanMethod"
              @pagination="paginationChange"
              @selection-change="handleSelectionChange"
            >
            </TablePage>
          </div>
        </div>
      </div>
      <addTreeNode
        ref="addNode"
        :dialogVisible="dialogVisible"
        :checkedData="handleEventData"
        :treeLevel="treeLevel"
        :requestHttp="requestHttp"
        :monitorType="projectCode"
        @closeDialog="closeDialog"
      ></addTreeNode>
      <!-- 导出选择 -->
      <!-- <template v-if="exportDialogShow">
        <exportDialog :exportDialogShow="exportDialogShow" @submitExportDialog="submitExportDialog" @closeExportDialog="closeExportDialog" />
      </template> -->
    </div>
  </PageContainer>
</template>
<script lang="jsx">
import addTreeNode from './components/addTreeNode'
import exportDialog from './components/monitorExport'
import { monitorTypeList } from '@/util/dict.js'
export default {
  name: 'monitorSettingOld',
  components: { addTreeNode, exportDialog },
  props: {
    projectCode: {
      type: String,
      default: ''
    },
    requestHttp: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      defaultProps: {
        children: 'children',
        label: 'name',
        value: 'code'
      },
      teamTreeData: [],
      expandedTeam: [],
      checkedTeamData: {},
      searchFrom: {
        sensorName: '',
        parameterName: '',
        harvesterName: ''
      },
      isShowTooltip: false,
      tableLoading: false,
      exportDialogShow: false, // 导出选择
      tableData: [],
      tableColumn: [],
      tableStartColumn: [
        {
          prop: 'sensorName',
          type: 'selection',
          align: 'center',
          width: '50'
        },
        {
          prop: 'sensorName',
          label: '序号',
          type: 'index',
          width: '80',
          formatter: (scope) => {
            // 当前分页重置
            // return scope.$index + 1
            // 跨分页序号
            return (this.pageData.page - 1) * this.pageData.pageSize + scope.$index + 1
          }
        },
        {
          prop: 'sensorName',
          label: '监测实体'
        },
        {
          prop: 'parameterName',
          label: '监测参数'
        },
        {
          prop: 'sensorNo',
          label: '监测项编号'
        },
        {
          prop: 'harvesterName',
          label: '传感器名称'
        }
      ],
      tableEndColumn: [
        {
          prop: 'iemMenuName',
          label: '归属分组',
          render: (h, row) => {
            return (
              <el-select ref="treeSelect" v-model={row.row.iemMenuCode} class="monitor-select">
                <el-option hidden value={row.row.iemMenuCode} label={row.row.iemMenuName}></el-option>
                <el-tree
                  data={this.teamTreeData}
                  props={{ props: this.defaultProps }}
                  node-key="id"
                  check-strictly={true}
                  highlight-current={true}
                  expand-on-click-node={false}
                  onNode-click={(e, node, obj) => {
                    this.handleNodeClick(e, node, obj, row.row)
                  }}
                ></el-tree>
              </el-select>
            )
          }
        },
        {
          prop: 'sensorName',
          width: '130',
          label: '操作',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span class="operationBtn-span" onClick={() => this.handleMonitorEvent('copy', row.row)}>
                  复制
                </span>
                <span class="operationBtn-span" onClick={() => this.handleMonitorEvent('edit', row.row)}>
                  修改
                </span>
                <span class="operationBtn-span" onClick={() => this.handleMonitorEvent('del', row.row, false)}>
                  删除
                </span>
              </div>
            )
          }
        }
      ],
      elevatorTableColumn: [
        {
          prop: 'entityTypeName',
          label: '实体类型'
        },
        {
          prop: 'assetName',
          label: '设备资产名称'
        },
        {
          prop: 'upstreamEntityName',
          label: '上游实体'
        }
      ],
      medicalGasTableColumn: [
        {
          prop: 'harvesterId',
          label: '传感器编号'
        }
      ],
      pageData: {
        page: 1,
        pageSize: 15,
        total: 0
      },
      monitorData: monitorTypeList.find((item) => item.projectCode == this.projectCode), // 当前监测类型对应字典数据
      multipleSelection: [],
      spanArr: [],
      handleEventData: {},
      dialogVisible: false, // tree节点弹窗
      treeLevel: 1, // tree节点弹窗
      groupConfigList: [] // 分组配置列表
    }
  },
  activated() {
    this.tableColumn = [...this.tableStartColumn, ...this[this.monitorData.tableColumn || 'medicalGasTableColumn'], ...this.tableEndColumn]
    // console.log('activated')
    this.getTableData()
  },
  created() {
    this.tableColumn = [...this.tableStartColumn, ...this[this.monitorData.tableColumn || 'medicalGasTableColumn'], ...this.tableEndColumn]
    this.getTreelist()
    this.getDataServerList()
  },
  mounted() {},
  methods: {
    // 获取table列表
    getTableData() {
      // 未获取到实体菜单时 不请求列表
      if (!this.checkedTeamData?.code ?? false) {
        return
      }
      let data = {
        menuCode: this.checkedTeamData.code,
        projectCode: this.projectCode,
        pageSize: this.pageData.pageSize,
        page: this.pageData.page,
        ...this.searchFrom,
        airPage: false
      }
      this.tableLoading = true
      this.tableData = []
      this.$api.getSurveyParameterList(data, {}, this.requestHttp).then((res) => {
        this.tableLoading = false
        if (res.code == 200) {
          this.tableData = res.data.list
          this.pageData.total = parseInt(res.data.totalCount)
        } else if (res.message) {
          this.$message.error(res.message)
        }
      })
    },
    // 获取分组树列表
    getTreelist() {
      this.$api
        .GetEntityMenuList(
          {
            projectId: this.projectCode
          },
          {},
          this.requestHttp
        )
        .then((res) => {
          this.treeLoading = false
          // this.treeList = res.data
          let list = this.$tools.transData(res.data, 'code', 'parentId', 'children')
          this.teamTreeData = list
          if (this.teamTreeData.length) {
            this.checkedTeamData = this.teamTreeData[0]
            // this.geTreeName(this.treeData[0].parentIds, this.treeData[0].name)
            this.$nextTick(() => {
              this.$refs.teamTree?.setCurrentKey(this.teamTreeData[0])
            })
            // 获取列表
            this.getTableData()
          } else {
            this.tableData = []
            this.pageData.total = 0
            this.tableLoading = false
          }
        })
    },
    // 监测实体列合并
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      const dataProvider = this.tableData
      const cellValue = row[column.property]
      // console.log(cellValue, column, rowIndex, columnIndex)
      const endIndex = this.tableColumn.length - 1
      const cellIndexList = [0, 1, endIndex] // 第一二列和最后一列合并
      if (cellValue || cellIndexList.includes(columnIndex)) {
        if (column.label == '监测实体' || cellIndexList.includes(columnIndex)) {
          // 上一条数据
          const prevRow = dataProvider[rowIndex - 1]
          // 下一条数据
          let nextRow = dataProvider[rowIndex + 1]
          // 当上一条数据等于下一条数据
          if (prevRow && prevRow[column.property] === cellValue) {
            return { rowspan: 0, colspan: 0 }
          } else {
            let rowspan = 1
            while (nextRow && nextRow[column.property] === cellValue) {
              rowspan++
              nextRow = dataProvider[rowspan + rowIndex]
            }
            if (rowspan > 1) {
              return { rowspan, colspan: 1 }
            }
          }
        }
      }
    },
    // 树状图点击
    handleTeamClick(data) {
      this.pageData.page = 1
      this.checkedTeamData = data
      this.$refs.teamTree.setCurrentKey(this.checkedTeamData)
      this.getTableData()
    },
    renderContent(h, { node, data, store }) {
      const notTeamNodeRole = data.notTeamNodeRole?.split(',') ?? []
      const nowProjectName = monitorTypeList.find((item) => item.projectCode == this.projectCode).projectName
      // 空调监测为非树形结构
      return (
        <span class="custom-tree-node">
          {this.isShowTooltip ? (
            <el-tooltip class="item" effect="dark" content={node.label} placement="top-start">
              <span
                onMouseenter={(e) => {
                  this.visibilityChange(e)
                }}
              >
                {node.label}
              </span>
            </el-tooltip>
          ) : (
            <span
              onMouseenter={(e) => {
                this.visibilityChange(e)
              }}
            >
              {node.label}
            </span>
          )}
          <span>
            {!notTeamNodeRole.includes('add') && nowProjectName != '空调监测' && nowProjectName != '照明监测' && (
              <i
                class="el-icon-plus"
                onClick={(e) => {
                  this.handleTeamEvent('add', 2, data), e.stopPropagation()
                }}
              ></i>
            )}
            {!notTeamNodeRole.includes('edit') && (
              <i
                class="el-icon-edit"
                onClick={(e) => {
                  this.handleTeamEvent('edit', null, data, node), e.stopPropagation()
                }}
              ></i>
            )}
            {!notTeamNodeRole.includes('del') && (
              <i
                class="el-icon-delete"
                onClick={(e) => {
                  this.handleTeamEvent('del', null, data, node), e.stopPropagation()
                }}
              ></i>
            )}
          </span>
        </span>
      )
    },
    visibilityChange(e) {
      const item = e.target
      const itemWidth = item.offsetWidth
      const itemScrollWidth = item.scrollWidth
      if (itemWidth < itemScrollWidth) {
        this.isShowTooltip = true
      } else {
        this.isShowTooltip = false
      }
    },
    // 重置查询表单
    resetForm() {
      this.searchFrom = {
        sensorName: '',
        parameterName: '',
        harvesterName: ''
      }
      this.searchForm()
    },
    // 查询
    searchForm() {
      this.pageData.page = 1
      this.getTableData()
    },
    // 获取多选列表
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 分页事件
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getTableData()
    },
    // 导入
    importMonitor(file) {
      const fileData = new FormData()
      fileData.append('file', file.file)
      this.$api.uploadTask(fileData).then((res) => {
        if (res.code == 200) {
          let userInfo = this.$store.state.user.userInfo.user
          let data = {
            projectCode: this.projectCode,
            minioUrl: res.data.picUrl,
            createName: userInfo.staffName
          }
          this.$api.recordExcelImportLog(data).then((res) => {
            console.log(res)
          })
        } else {
          this.$message.error(res.data.msg)
        }
      })
      const params = {
        file: file.file,
        projectCode: this.projectCode,
        userId: this.$store.state.user.userInfo.userId
      }
      this.$api.importSurveyExcel(params).then((res) => {
        if (res.code == 200) {
          this.$message.success('导入成功')
          this.getTableData()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 导入scada
    importMonitorScada(file) {
      const params = {
        file: file.file,
        projectCode: this.projectCode,
        menuCode: this.checkedTeamData.code
      }
      this.$api.importParamScada(params).then((res) => {
        if (res.code == 200) {
          this.$message.success('导入成功')
          this.getTableData()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 下载模板
    exportTemplate() {
      // this.exportDialogShow = true
      let data = {
        projectCode: this.projectCode,
        menuCode: this.checkedTeamData.code
      }
      this.$api.exportTemplateExcel(data).then((res) => {
        this.exportDialogShow = false
        let url = window.URL.createObjectURL(new Blob([res]), { type: 'application/vnd.ms-excel;charset=UTF-8' })
        let link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', '监测实体导入模板.xlsx')
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link) // 下载完成移除元素
        window.URL.revokeObjectURL(url) // 释放掉blob对象
      })
    },
    // 导出确定
    submitExportDialog(val) {
      let data = {
        projectCode: this.projectCode,
        manufacturer: val.manufacturer,
        professionalCategoryCode: val.professionalCategoryCode,
        menuId: this.checkedTeamData.code,
        drawingId: this.checkedTeamData.isiId
      }
      this.$api.exportTemplateExcel(data).then((res) => {
        this.exportDialogShow = false
        let url = window.URL.createObjectURL(new Blob([res]), { type: 'application/vnd.ms-excel;charset=UTF-8' })
        let link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', '监测实体导入模板.xlsx')
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link) // 下载完成移除元素
        window.URL.revokeObjectURL(url) // 释放掉blob对象
      })
    },
    // 导出取消
    closeExportDialog() {
      this.exportDialogShow = false
    },
    // 删除
    delMonitor() {
      const row = {
        sensorCode: Array.from(this.multipleSelection.map((item) => item.sensorCode)).toString()
      }
      this.handleMonitorEvent('del', row, true)
    },
    // 获取数据主机
    getDataServerList() {
      this.$api.getDataServerList({}, {}, this.requestHttp).then((res) => {
        if (res.code == '200') {
          sessionStorage.setItem('dataServer', JSON.stringify(res.data))
        }
      })
    },
    // 监测项事件
    handleMonitorEvent(type, row, isMultipleChoice) {
      if (type === 'del') {
        let content = isMultipleChoice ? '确定要删除该批监测实体吗？删除后将无法对该批实体进行监测!' : '确定要删除监测实体吗？删除后将无法对该实体进行监测!'
        let title = isMultipleChoice ? '批量删除' : '删除'
        this.$confirm(content, title, {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 确定删除回调的内容
          const userInfo = this.$store.state.user.userInfo.user
          this.$api
            .delSurveyAndParameter(
              {
                surveyCode: row.sensorCode,
                userId: userInfo.staffId,
                userName: userInfo.staffName
              },
              {
                'operation-type': 3,
                'operation-id': row.sensorCode,
                'operation-name': row.sensorName
              },
              this.requestHttp
            )
            .then((res) => {
              if (res.code == 200) {
                this.$message.success(res.message)
                this.getTableData()
              }
            })
        })
        return
      }
      if (type == 'add' && (!this.checkedTeamData.code || !this.teamTreeData.length)) {
        return this.$message.warning('请先添加分组')
      }
      this.$router.push({
        path: '/monitoringConfig/' + (this.monitorData?.formPath ?? '/medicalGasMonitorForm'),
        query: {
          type: type,
          projectCode: this.projectCode,
          entityMenuCode: this.checkedTeamData.code,
          isiId: this.checkedTeamData.isiId,
          sensorCode: type != 'add' ? row.sensorCode : '',
          monitortype: this.monitorData.type
        }
      })
    },
    // 分组事件
    handleTeamEvent(type, addType, data, node) {
      if (type == 'add') {
        Object.assign(this.handleEventData, {
          ...data,
          state: 'add'
        })
        this.treeLevel = addType // 1同级 2下级
        this.dialogVisible = true
      } else if (type == 'edit') {
        Object.assign(this.handleEventData, {
          ...data,
          state: 'edit'
        })
        this.dialogVisible = true
        this.$refs.addNode.echoData()
      } else if (type == 'del') {
        this.$confirm('确定删除?', '消息', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.$api
              .delEntityMenu(
                {
                  id: data.id
                },
                {
                  'operation-type': 3,
                  'operation-id': data.code,
                  'operation-name': data.name
                },
                this.requestHttp
              )
              .then((res) => {
                if (res.code == 200) {
                  this.$message.success(res.message)
                  this.getTreelist()
                } else {
                  this.$message.error(res.message)
                }
              })
          })
          .catch(() => {})
      }
    },
    closeDialog(refresh) {
      refresh && this.getTreelist()
      this.dialogVisible = false
    },
    // 修改分组
    selectGroupConfig(item, node, self) {
      let ids = Array.from(this.multipleSelection, ({ sensorCode }) => sensorCode)
      const params = {
        imsCodes: ids,
        iemCode: item.code
      }
      this.updataListBelongGroup(params, 'lots')
    },
    // 选择下拉树 数据
    handleNodeClick(data, node, obj, row) {
      const params = {
        imsCodes: row.sensorCode,
        iemCode: data.code
      }
      this.updataListBelongGroup(params, 'once')
    },
    // 更新实体所属分组
    updataListBelongGroup(params, type) {
      this.$api
        .updateSurveyGroup(
          params,
          {
            'operation-type': 2
          },
          this.requestHttp
        )
        .then((res) => {
          if (res.code === '200') {
            this.$message({
              message: '更改分组成功',
              type: 'success'
            })
            if (type === 'lots') {
              // 关闭tooltip
              this.$refs.tooltip.showPopper = false
            }
            this.getTreelist()
          } else {
            this.$message.warning(res.message)
          }
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.monitor-setting {
  height: 100%;
  display: flex;
  .monitor-setting-left {
    width: 246px;
    height: 100%;
    // padding: 8px 16px 16px 16px;
    background: #fff;
    border-radius: 4px;
    ::v-deep .box-card {
      .card-title {
        position: relative;
        .select-servive {
          position: absolute;
          right: 0;
          top: 2px;
          cursor: pointer;
          font-family: PingFangSC-Regular, 'PingFang SC';
          font-size: 14px;
          color: $color-primary;
          font-weight: 600;
          padding-left: 15px;
          i {
            font-weight: 600;
            font-size: 13px;
            margin-right: 2px;
          }
        }
      }
      .card-body {
        border-top: 1px solid #dcdfe6;
        padding-top: 10px;
        // firefox隐藏滚动条
        scrollbar-width: none;
        // chrome隐藏滚动条
        &::-webkit-scrollbar {
          display: none;
        }
        .custom-tree-node {
          flex: 1;
          display: flex;
          justify-content: space-between;
          padding-right: 10px;
          & > span {
            &:first-child {
              flex: 1;
              display: block;
              align-items: center;
              text-overflow: ellipsis;
              white-space: nowrap;
              overflow: hidden;
              color: #121f3e;
              font-size: 14px;
              font-family: 'PingFang SC-Regular', 'PingFang SC';
            }
            &:last-child {
              display: flex;
              align-items: center;
              i {
                margin-left: 6px;
                cursor: pointer;
                color: #3562db;
              }
            }
          }
        }
        .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
          background-color: #d9e1f8;
        }
      }
    }
    .team-tree {
      ::v-deep .custom-tree-node {
        overflow: hidden;
      }
    }
  }
  .monitor-setting-right {
    height: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
    .right-heade {
      padding: 0 200px 10px 10px !important;
      margin-left: 16px;
      background: #fff;
      border-radius: 4px;
      ::v-deep .el-input {
        width: 200px;
      }
      & > div {
        margin-right: 10px;
        margin-top: 10px;
      }
    }
    .right-content {
      margin-left: 16px;
      margin-top: 16px;
      background: #fff;
      padding: 10px;
      border-radius: 4px;
      flex: 1;
      overflow: hidden;
      .btns-group {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        & > div {
          display: flex;
        }
        .btns-group-control {
          > div {
            margin-left: 10px;
          }
          // & > div, & > button {
          //   margin-right: 10px;
          // }
        }
      }
      .table-content {
        height: calc(100% - 45px);
        ::v-deep .el-table .el-table__body-wrapper td.el-table__cell {
          border-right: 1px solid #ededf5;
        }
      }
    }
  }
}
</style>
<style lang="scss">
.monitor-setting {
  .operationBtn-span {
    margin-right: 10px;
    color: #3562db;
  }
  .monitor-select {
    width: auto;
  }
}
.mut-tooltip {
  border: none !important;
  padding: 0 !important;
  .group-config-type {
    width: 180px;
    max-height: 300px;
    overflow-y: scroll;
    border-radius: 4px;
    padding: 5px;
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 30%);
    .el-tree {
      .el-tree-node {
        margin: 3px 0;
      }
      .el-tree-node__label {
        font-size: 14px;
        color: #121f3e;
        font-family: 'PingFang SC-Regular', 'PingFang SC';
      }
      // color: $color-primary;
      // padding: 10px 25px;
      // font-size: 14px;
    }
  }
  .popper__arrow {
    border-width: 4px;
  }
}
</style>
