<template>
  <PageContainer v-if="routeInfo.isFalg == 0">
    <div slot="header">
      <div class="search-header">
        <div class="search-from">
          <el-input v-model="formInline.courseName" placeholder="请输入课程名称" style="width: 200px"></el-input>
          <el-cascader v-model="formInline.courseType" clearable class="sino_sdcp_input mr15" :options="courseTypeList"
            :props="props" placeholder="请选择类型"></el-cascader>
          <el-select v-model="formInline.courseStatus" placeholder="请选择状态">
            <el-option v-for="item in courseStatusList" :key="item.value" :label="item.name"
              :value="item.value"></el-option>
          </el-select>
          <el-date-picker v-model="timeLine" type="daterange" value-format="yyyy-MM-dd" range-separator="至"
            start-placeholder="开始日期" end-placeholder="结束日期">
          </el-date-picker>
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="search">查询</el-button>
        </div>
        <div class="btns">
          <el-button class="el-icon-plus" type="primary" @click="addCourse('add')">创建课程</el-button>
          <div>
            <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="checkAllBtn">全选</el-checkbox>
            <el-button type="primary" plain class="delBtn" :disabled="!checkDataList.length"
              @click="deleteCourse()">删除</el-button>
          </div>
        </div>
      </div>
    </div>
    <div slot="content" style="
        height: calc(100% - 16px);
        margin-top: 16px;
        background-color: #fff; ">
      <div class="course-content" v-loading="tableLoading">
        <el-row :gutter="24" class="overview">
          <el-col v-for="(item, index) in tableData" :key="index" :span="6">
            <div class="item" @click="courseDetils(item.id)">
              <div class="course_cove" :style="{ backgroundImage: 'url(' + item.coverUrl + ')' }">
                <div class="status-ing" v-if="item.approvalState == '1'">
                  审核中
                </div>
                <div class="statusFail" v-if="item.approvalState == '2'">
                  <i class="el-icon-warning-outline"></i>未通过
                </div>
                <div class="statusOK" v-if="item.approvalState == '3'">
                  <img src="../../../assets/images/pass.png" alt="">
                  已通过
                </div>
                <p @click.stop class="chioce">
                  <el-checkbox v-model="item.checked" @change="checkItem()"></el-checkbox>
                </p>
              </div>
              <div class="text">
                <div class="headline">
                  <div class="draft" v-if="item.approvalState == '0'">草稿</div>
                  <p>{{ item.courseName }}</p>
                </div>
                <p class="introduce">{{ item.subjectName }}</p>
                <p class="describe">{{ item.comments }}</p>
                <div class="course_time introduce">
                  <div class="coursr_titName">
                    <span>{{ item.updateTime }}</span>
                    <span>来自 {{ item.createName }}</span>
                    <span>{{ item.tunoppoluad }}课时</span>
                  </div>
                  <div class="course_bottom" @click.stop>
                    <i class="el-icon-edit" @click="addCourse('edit', item.id)"></i>
                    <i class="el-icon-delete" @click="deleteCourse(item.id)"></i>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="contentTable-footer">
        <el-pagination style="margin-top: 3px" :current-page="paginationData.startSize"
          layout="total, sizes, prev, pager, next, jumper" :total="paginationData.total"
          :page-size="paginationData.pageSize" :page-sizes="[15, 30, 50, 100]" @size-change="handleSizeChange"
          @current-change="handleCurrentChange"></el-pagination>
      </div>
    </div>
  </PageContainer>
   <div v-else>
  <permissionPrompt></permissionPrompt>
  </div>
</template>

<script>
import axios from "axios";
import * as path from "path-browserify";
import permissionPrompt from "@/views/safety/courseIndex/components/permissionPrompt.vue";
export default {
   components: {permissionPrompt },
  data() {
    return {
      routeInfo: "", //路由传过来的值
      timeLine: [],
      formInline: {
        courseName: "",
        courseType: "",
        courseStatus: "",
        startTime: "",
        endTime: "",
      },
      courseTypeList: [],
      props: {
        children: "childList",
        label: "name",
        value: "id",
        checkStrictly: true,
        emitPath: false
      },
      courseStatusList: [
        {
          value: "0",
          name: "草稿",
        },
        {
          value: "1",
          name: "审核中",
        },
        {
          value: "2",
          name: "未通过",
        },
        {
          value: "3",
          name: "已通过",
        },
      ],
      isIndeterminate: false,
      checkAll: false,
      tableData: [],
      tableLoading: false,
      paginationData: {
        startSize: 1,
        pageSize: 15,
        total: 0,
      },
      userInfo: '',
      tableData: [],
      checkDataList: []
    };
  },
  created() {
    this.routeInfo = JSON.parse(sessionStorage.getItem('routeInfo'))
     if (this.routeInfo.isFalg == 1) {
      return
    }
    // this.userInfo =  store.state.user.userInfo.user
    // console.log(this.userInfo,'this.routeInfo00000000');
    // console.log(sessionStorage.getItem('routeInfo'),'sessionStorage+++++++++++++++++++++++++++++++++++++');
    // if (this.$route.query) {
    //   localStorage.setItem("routeInfo", JSON.stringify(this.$route.query));
    //   this.routeInfo = JSON.parse(sessionStorage.getItem("routeInfo"));
    // }
    this.getTblleList();
    this.getCourseList();
  },
  watch: {
    timeLine(val) {
      if (val.length > 0) {
        this.formInline.startTime = val[0];
        this.formInline.endTime = val[1];
      } else {
        this.formInline.startTime = "";
        this.formInline.endTime = "";
      }
    },
  },
  methods: {
    getTblleList() {
      let data = {
        pageNo: 1,
        pageSize: 999,
      };
      this.$api.ipsmFicationlList(data).then((res) => {
        if (res.code == 200) {
          this.courseTypeList = res.data.records;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 通知单列表
    getCourseList() {
      this.tableLoading = true;
      let params = {
        pageNo: this.paginationData.startSize,
        pageSize: this.paginationData.pageSize,
        courseName: this.formInline.courseName,
        subjectId: this.formInline.courseType,
        approvalState: this.formInline.courseStatus,
        startTime: this.formInline.startTime,
        endTime: this.formInline.endTime,
      };
      this.$api.getCourseList(params).then((res) => {
        if (res.code == 200) {
          res.data.list.forEach((item) => {
            item.checked = false;
          });
          this.tableData = res.data.list;
          this.paginationData.total = res.data.total;
        } else {
          this.$message.error(res.message);
        }
        this.tableLoading = false;
      });
    },
    checkAllBtn(val) {
      if (val) {
        this.tableData.forEach((item) => {
          item.checked = true;
        });
        this.checkDataList = this.tableData
      } else {
        this.tableData.forEach((item) => {
          item.checked = false;
        });
        this.checkDataList = []
      }
      this.isIndeterminate = false;
    },
    checkItem() {
      this.checkDataList = []
      this.tableData.forEach(i => {
        if (i.checked) {
          this.checkDataList.push(i)
        }
      })
      this.checkAll = this.checkDataList.length === this.tableData.length;
      this.isIndeterminate = this.checkDataList.length > 0 && this.checkDataList.length < this.tableData.length;
    },
    // 查询
    search() {
      this.paginationData.startSize = 1;
      this.getCourseList();
    },
    // 重置
    resetForm() {
      this.formInline.courseName = "";
      this.formInline.courseType = "";
      this.formInline.courseStatus = "";
      this.timeLine = [];
      this.paginationData.startSize = 1;
      this.getCourseList();
    },
    // 创建课程
    async addCourse(type, id) {
      console.log(type,'type');
      
      if (type == 'edit') {
        this.$api.isUpdateCourse({ courseId: id }).then(res => {
          if (res.code == '200') {
            this.$router.push({
              path: "addCourse",
              query: {
                typeStr: type,
                id: id || '',
              },
            });
          } else {
            this.$message.error(res.msg)
          }
        })
      } else {
        console.log('sssssssssssssssssssss');
        
        this.$router.push({
          path: "addCourse",
          query: {
            typeStr: type,
            id: id || '',
          },
        });
      }
    },
    // 查看详情
    courseDetils(id) {
      this.$router.push({
        path: "courseInfo",
        query: {
          id: id
        }
      });
    },
    // 删除课程
    deleteCourse(id) {
      this.$confirm('删除后将无法恢复，是否确定删除？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let ids = id ? id : this.checkDataList.map(i => i.id).join(',')
        let params = {
          id: ids,
          // userCode: this.routeInfo.userId
          userCode: "ddbf968c90e381505145d2afacbd0a97"
        }
        this.$api.deleteCourse(params).then((res) => {
          if (res.code == 200) {
            this.$message.success(res.msg)
            this.getCourseList()
          } else {
            this.$message.error(res.msg);
          }
        });
      })
    },
    handleSizeChange(val) {
      this.paginationData.startSize = 1
      this.paginationData.pageSize = val
      this.getCourseList()
    },
    handleCurrentChange(val) {
      this.paginationData.startSize = val
      this.getCourseList()
    },
  },
};
</script>

<style lang="scss" scoped>
.search-header {
  padding: 10px 16px;

  .search-from {
    padding-bottom: 12px;

    &>div {
      margin-top: 12px;
      margin-right: 10px;
    }
  }
}

.btns {
  display: flex;
  justify-content: space-between;

  .delBtn {
    margin-left: 16px;
  }
}

.course-content {
  padding: 16px;
  height: calc(100% - 40px);
  overflow: auto;
}

.item {
  // width: 372px;
  height: 304px;
  margin-bottom: 30px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e6eb;
  display: flex;
  flex-direction: column;

  .text {
    padding: 16px 24px;
    font-family: PingFang SC-Regular, PingFang SC;
  }
}

.course_cove {
  width: 100%;
  height: 60%;
  border-radius: 8px 8px 0px 0px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: relative;

  .status-ing {
    width: 58px;
    height: 24px;
    font-size: 14px;
    color: #fff;
    line-height: 24px;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.4);
    border-radius: 4px;
    position: absolute;
    left: 8px;
    top: 8px;
  }

  .statusFail {
    width: 79px;
    height: 24px;
    background: #ffece8;
    border-radius: 4px;
    font-size: 14px;
    color: #cb2634;
    line-height: 24px;
    text-align: center;
    position: absolute;
    left: 8px;
    top: 8px;

    i {
      margin-right: 6px;
    }
  }

  .statusOK {
    width: 79px;
    height: 24px;
    background: #E8FFEA;
    border-radius: 4px;
    font-size: 14px;
    color: #009A29;
    line-height: 24px;
    text-align: center;
    position: absolute;
    left: 8px;
    top: 8px;

    img {
      vertical-align: middle;
    }
  }

  .chioce {
    zoom: 120%;
    position: absolute;
    right: 8px;
    top: 8px;
  }
}

.headline {
  display: flex;

  p {
    width: 80%;
    font-size: 16px;
    line-height: 22px;
    color: #333333;
    font-weight: 600;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .draft {
    width: 52px;
    height: 100%;
    background: #fff7e8;
    border-radius: 4px;
    color: #d25f00;
    font-size: 14px;
    line-height: 22px;
    margin-right: 8px;
    text-align: center;
  }
}

.introduce {
  margin: 6px 0;
  font-size: 12px;
  color: #999999;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.describe {
  height: 20px;
  margin-bottom: 20px;
  font-size: 14px;
  color: #666666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.course_time {
  display: flex;
  justify-content: space-between;

  .course_bottom {
    i {
      color: #3562db;
      font-size: 14px;
      cursor: pointer;
    }

    .el-icon-edit {
      margin-right: 10px;
    }
  }
}

.contentTable-footer {
  padding: 0 10px;
}
</style>
