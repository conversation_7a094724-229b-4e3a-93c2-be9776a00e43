<template>
  <div class="recycleBin-content">
    <div class="recycleBin-header">
      <el-input v-model="archiveInfo" placeholder="记录名称、培训名称、备注" suffix-icon="el-icon-search" clearable />
      <el-button type="primary" plain @click="reset">重置</el-button>
      <el-button type="primary" @click="search">搜索</el-button>
      <div class="recycleBin-operation">
        <el-button size="small" type="primary" :disabled="!multipleSelection.length" @click="handleRecovery">恢复</el-button>
        <el-button type="danger" :disabled="!multipleSelection.length" @click="handleCompletelyDelete">彻底删除</el-button>
      </div>
    </div>
    <div class="recycleBin-table">
      <el-table v-loading="tableLoading" height="100%" :data="tableData" border stripe table-layout="auto" class="tableAuto" row-key="id" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" :reserve-selection="true"> </el-table-column>
        <el-table-column prop="archiveName" label="文档名称" show-overflow-tooltip> </el-table-column>
        <el-table-column prop="archiveNumber" label="文号" show-overflow-tooltip> </el-table-column>
        <el-table-column prop="folderName" label="文件夹" show-overflow-tooltip> </el-table-column>
        <el-table-column label="分类" prop="archiveModel" show-overflow-tooltip>
          <template #default="{ row }">
            <span>文档 </span>
          </template>
        </el-table-column>
        <el-table-column label="成文日期" prop="archiveDate" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ moment(row.archiveDate).format('YYYY-MM-DD') || '' }} </span>
          </template>
        </el-table-column>
        <el-table-column label="所有者" prop="archiveOwnerName" show-overflow-tooltip> </el-table-column>
        <el-table-column label="所有者部门" prop="archiveOwnerDeptName" show-overflow-tooltip> </el-table-column>
        <el-table-column label="摘要信息" prop="remark" show-overflow-tooltip> </el-table-column>
      </el-table>
      <el-pagination
        class="recycleBin-pagination"
        :current-page="pagination.page"
        :page-sizes="pagination.pageSizeOptions"
        :page-size="pagination.size"
        :layout="pagination.layoutOptions"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
      </el-pagination>
    </div>
    <Recovery :visible.sync="visible" :infoList="infoList" :folderType="'2'" @success="handleQueryTablelist" />
  </div>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin'
import Recovery from '../safetyManageSystem/components/Recovery.vue'
import moment from 'moment'
export default {
  // 组件注册
  components: { Recovery },
  mixins: [tableListMixin],
  data() {
    // 数据定义
    return {
      visible: false, // 恢复弹窗
      moment,
      tableData: [],
      archiveInfo: '',
      tableLoading: false,
      multipleSelection: [],
      infoList: []
    }
  },
  mounted() {
    this.handleQueryTablelist()
  },
  methods: {
    handleQueryTablelist() {
      const archiveInfo = this.archiveInfo
      const parmas = {
        isRecycle: '1', // 回收站
        archiveInfo,
        isMine: false,
        archiveType: '3', // 0：合同 1：文件  2:安全管理制度 3：教育与培训档案
        size: this.pagination.size,
        current: this.pagination.current
      }
      this.$api.fileManagement.queryByPage(parmas).then((res) => {
        this.tableData = res.data.records
        this.pagination.total = res.data.total
      })
    },
    search() {
      this.pagination.current = 1
      this.handleQueryTablelist()
    },
    reset() {
      this.archiveInfo = ''
      this.search()
    },
    handleRecovery() {
      const rows = this.multipleSelection
      const idList = rows.map((v) => v.archiveId)
      this.$api.fileManagement.recoverFile({ idList }).then((res) => {
        if (res.code === '200') {
          if (res.data && res.data.length) {
            this.infoList = res.data
            this.visible = true
            return
          }
          this.$message.success('恢复成功')
          this.handleQueryTablelist()
        } else {
          this.$message.error('恢复失败')
        }
      })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleCompletelyDelete() {
      const rows = this.multipleSelection
      const message = rows.map((item) => item.archiveName).join('，')
      this.$confirm('确认删除“' + message + '”等数据吗？', '彻底删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const idList = rows.map((v) => v.archiveId)
          this.$api.fileManagement.deleteFile({ idList }).then((res) => {
            if (res.code === '200') {
              this.$message.success('删除成功')
              this.handleQueryTablelist()
            } else {
              this.$message.error('删除失败')
            }
          })
        })
        .catch(() => {})
    },
    handleSizeChange(val) {
      this.pagination.size = val
      this.handleQueryTablelist()
    },
    handleCurrentChange(val) {
      this.pagination.current = val
      this.handleQueryTablelist()
    }
  }
}
</script>
<style lang="scss" scoped>
.recycleBin-content {
  width: 100%;
  height: 100%;
  padding: 16px 0;
  .recycleBin-header {
    .el-input {
      width: 300px;
      margin-right: 16px;
    }
  }
  .recycleBin-operation {
    margin: 12px 0;
  }
  .recycleBin-table {
    height: calc(100% - 180px);
  }
  .recycleBin-pagination {
    margin-top: 16px;
  }
}
</style>
