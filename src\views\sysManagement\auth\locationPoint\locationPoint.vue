<template>
  <PageContainer>
    <div slot="header">
      <div class="search-header">
        <div class="search-from">
          <el-input
            v-model="locationPointCode"
            style="width: 217px; margin-right: 15px;"
            placeholder="定位点编号"
            maxlength="25"
            onkeyup="if(value.length>25)value=value.slice(0,25)"
          ></el-input>
          <el-input
            v-model="locationPointName"
            style="width: 217px; margin-right: 15px;"
            placeholder="定位点名称"
            maxlength="25"
            onkeyup="if(value.length>25)value=value.slice(0,25)"
          ></el-input>
          <el-select v-model="locationPointType" filterable placeholder="定位点类型">
            <el-option v-for="item in locationPointTypeList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
          <el-button type="primary" @click="_resetCondition">重置</el-button>
          <el-button type="primary" @click="_searchByCondition">查询</el-button>
          <el-button type="primary" icon="el-icon-plus" @click="addFn('add')">新增</el-button>
          <el-button type="primary" icon="el-icon-upload2" @click="importBtn">批量导入</el-button>
        </div>
      </div>
    </div>
    <div slot="content" style="height: 100%;">
      <div class="contentTable">
        <div class="contentTable-main table-content">
          <el-table
            ref="materialTable"
            v-loading="tableLoading"
            :data="tableData"
            border
            style="width: 100%;"
            stripe
            :header-cell-style="{ background: '#f2f4fbd1' }"
            :height="tableHeight"
            highlight-current-row
            :empty-text="emptyText"
            :row-key="getRowKeys"
            @row-dblclick="toDetails"
          >
            <!-- <el-table-column
                    :reserve-selection="true"
                    type="selection"
                    width="55"
                    align="center"
                  ></el-table-column> -->
            <el-table-column align="center" label="序号" type="index" width="80">
              <template slot-scope="scope">
                <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="locationPointCode" show-overflow-tooltip label="定位点编号"></el-table-column>
            <el-table-column align="center" prop="locationPointName" show-overflow-tooltip label="定位点名称"></el-table-column>
            <!-- <el-table-column align="center" prop="deviceUuid" show-overflow-tooltip label="设备UUID"></el-table-column> -->
            <!-- <el-table-column align="center" prop="deviceMinor" show-overflow-tooltip label="设备minor"></el-table-column> -->
            <!-- <el-table-column align="center" prop="deviceMajor" show-overflow-tooltip label="设备major"></el-table-column> -->
            <el-table-column align="center" prop="locationPointType" show-overflow-tooltip label="定位点类型">
              <template slot-scope="scope">
                <span>{{ scope.row.locationPointType == '1' ? '蓝牙定位' : 'RFID定位' }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="remarks" show-overflow-tooltip label="描述"></el-table-column>
            <el-table-column align="center" prop="createDate" show-overflow-tooltip label="创建时间"></el-table-column>
            <el-table-column align="center" label="操作" width="200">
              <template slot-scope="scope">
                <el-link :underline="false" type="danger" style="margin-right: 10px;" @click="deleteLocationPoint(scope.row)">删除</el-link>
                <el-link :underline="false" type="primary" @click="updateFn('edit', scope.row)">编辑</el-link>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="contentTable-footer">
          <el-pagination
            class="table-page pagination"
            style="margin-top: 10px;"
            :current-page="paginationData.currentPage"
            :page-sizes="[15, 30, 50, 100]"
            :page-size="paginationData.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="paginationData.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
      <!-- 批量导入 -->
      <el-dialog
        v-dialogDrag
        title="批量导入"
        :visible.sync="dialogVisible"
        width="40%"
        :before-close="dialogCancel"
        class="classify-dialog"
        custom-class="model-dialog"
        :close-on-click-modal="false"
      >
        <div class="content" style="padding: 10px; display: flex">
          <div>上传附件：</div>
          <div class="upload-file">
            <el-upload
              ref="uploadFile"
              action="string"
              :limit="1"
              :http-request="httpRequest"
              :beforeUpload="beforeAvatarUpload"
              :file-list="fileList"
              :on-exceed="handleExceed"
              :on-remove="handleRemove"
              accept=".xls,.xlsx"
            >
              <el-button size="small" type="primary">点击上传</el-button>
              <span class="templateClass" @click.stop="templateBtn">模板下载</span>
              <div slot="tip" class="el-upload__tip">只能上传 .xls、.xlsx格式</div>
            </el-upload>
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="dialogCancel">取 消</el-button>
          <el-button type="primary" :loading="subLoading" @click="submitImport">确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </PageContainer>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin.js'
import axios from 'axios'
import store from '@/store/index'
export default {
  name: 'locationPoint',
  mixins: [tableListMixin],
  beforeRouteEnter(to, from, next) {
    let names = []
    to.matched.map((v, i) => {
      if (i > 0) {
        v.components.default.name && names.push(v.components.default.name)
      }
    })
    // 进入页面时，先将当前页面的 name 信息存入 keep-alive 全局状态
    setTimeout(() => {
      next((vm) => {
        vm.$store.commit('keepAlive/add', names)
      })
    }, 0)
  },
  async beforeRouteLeave(to, from, next) {
    // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
    if (!['addLocation'].includes(to.name)) {
      // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      LOGINDATA: '',
      taskBookTypeArr: [],
      locationPointName: '', // 定位点名称
      locationPointCode: '', // 定位点编号
      locationPointType: '', // 定位点类型
      locationPointTypeList: [
        {
          label: '蓝牙标签',
          value: '1'
        }
        // {
        //   label: 'RFID标签',
        //   value: '2'
        // }
      ], // 定位点类型列表
      searchDataObj: {
        workTypeCode: '',
        endDate: '',
        startDate: '',
        dateLine: '',
        unionSel: ''
      },
      tableData: [],
      selectedTableList: [],
      emptyText: '暂无数据',
      tableLoading: false,
      pageSize: '',
      currentPage: '',
      listRole: '',
      userIds: '',
      taskBookTypeName: '',
      dialogVisible: false,
      fileList: [],
      subLoading: false
    }
  },
  activated() {
    this._findLocationPointList()
  },
  created() {
    // 获取登录信息
    this.LOGINDATA = JSON.parse(sessionStorage.getItem('LOGINDATA'))
  },
  mounted() {
    // 获取定位点列表
    this._findLocationPointList()
  },
  methods: {
    // 新增
    addFn(type) {
      const uuids = []
      const rfids = []
      this.tableData.forEach((i) => uuids.push(i.deviceUuid))
      this.tableData.forEach((i) => rfids.push(i.rfidCode))
      this.$router.push({
        name: 'addLocation',
        query: {
          type: type,
          uuids,
          rfids
        }
      })
    },
    updateFn(type, row) {
      const uuids = []
      const rfids = []
      this.tableData.forEach((i) => uuids.push(i.deviceUuid))
      this.tableData.forEach((i) => rfids.push(i.rfidCode))
      this.$router.push({
        name: 'addLocation',
        query: {
          type: type,
          id: row.id,
          uuids,
          rfids
        }
      })
    },
    // 查询表格
    _findLocationPointList() {
      this.tableLoading = true
      let obj = this.LOGINDATA
      const { locationPointName, locationPointCode, locationPointType, paginationData } = this
      let data = {
        locationPointName: locationPointName,
        locationPointCode: locationPointCode,
        locationPointType: locationPointType,
        pageNo: paginationData.currentPage,
        pageSize: paginationData.pageSize
      }
      this.$api.findLocationPointList(data).then((res) => {
        this.tableLoading = true
        if (res.code == 200) {
          this.tableData = res.data.list
          this.paginationData.total = parseInt(res.data.count)
          if (res.data.list.length == 0) {
            this.emptyText = '未查到任何记录，请放大查询条件试试！'
          }
        }
        this.tableLoading = false
      })
    },
    // 条件查询
    _searchByCondition() {
      this.paginationData.currentPage = 1
      this._findLocationPointList()
    },
    // 重置查询条件
    _resetCondition() {
      this.paginationData = {
        currentPage: 1,
        pageSize: 15,
        total: 0
      }
      this.locationPointCode = ''
      this.locationPointName = ''
      this.locationPointType = ''
      this._findLocationPointList()
    },
    // 删除列表
    deleteLocationPoint(row) {
      this.$confirm('确定要删除该定位点吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let data = {
          id: row.id,
          locationPointName: row.locationPointName
        }
        this.$api.deleteLocationListList(data, { 'operation-type': 3, 'operation-id': row.id, 'operation-name': row.locationPointName}).then((res) => {
          const { code, data, message } = res
          if (code == 200) {
            this.$message.success(message)
            this._findLocationPointList()
          } else {
            this.$message.error(message)
          }
        })
      })
    },
    // 双击查看详情
    toDetails(row) {
      this.$router.push({
        name: 'addLocation',
        query: {
          data: JSON.stringify(row),
          type: 'details'
        }
      })
    },

    // 表格,勾选表格，对表格进行操作
    handleSelectionChange(val) {
      this.selectedTableList = val
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this._findLocationPointList()
    },
    // 每页的条数
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.paginationData.currentPage = 1
      this._findLocationPointList()
    },
    getRowKeys(row) {
      return row.id
    },
    // 批量导入...................................
    importBtn() {
      this.fileList = []
      this.dialogVisible = true
    },
    dialogCancel() {
      this.fileList = []
      this.dialogVisible = false
    },
    // 模板下载
    templateBtn() {
      axios({
        method: 'get',
        url: __PATH.VUE_ICIS_API + 'locationPoint/downloadPoint',
        responseType: 'blob',
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      })
        .then((res) => {
          this.downLoading = false
          let name = '模板.xls'
          let blob = new Blob([res.data]) // { type: "application/vnd.ms-excel" }
          let url = window.URL.createObjectURL(blob) // 创建一个临时的url指向blob对象
          // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
          let a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
          // 释放这个临时的对象url
          window.URL.revokeObjectURL(url)
        })
        .catch((res) => {
          console.log(res)
          this.$message.error('导出失败！')
        })
    },
    submitImport() {
      let formData = new FormData()
      const userInfo = store.state.user.userInfo.user
      formData.append('unitCode', userInfo.unitCode)
      formData.append('hospitalCode', userInfo.hospitalCode)
      if (this.fileList && this.fileList.length) {
        formData.append('file', this.fileList[0])
      }
      axios
        .post(__PATH.VUE_ICIS_API + 'locationPoint/importPoint', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
            Authorization: 'Bearer ' + this.$store.state.user.token
          }
        })
        .then((res) => {
          if (res.data.code == 200) {
            this._resetCondition()
            this.$message.success(res.data.message)
          } else {
            this.$message.error(res.data.message || '导入失败！')
          }
        })
        .catch(() => {})
      this.dialogVisible = false
    },
    /**
     * 文件
     */
    httpRequest(item) {
      this.fileList.push(item.file)
    },
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
    },
    handleRemove(file, fileList) {
      this.fileList = []
    },
    beforeAvatarUpload(file) {
      const isLt50M = file.size / 1024 / 1024 < 50
      if (!isLt50M) {
        this.$message.error('上传文件大小不能超过 50MB!')
        return false
      }
      if (file.name.indexOf(',') != -1) {
        this.$message.error('非法的文件名')
        return false
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.search-header {
  padding: 0 8px;

  .search-from {
    padding-bottom: 12px;

    & > div {
      margin-top: 12px;
      margin-right: 10px;
    }
  }
}

.contentTable {
  height: calc(100% - 16px);
  background: #fff;
  border-radius: 4px;
  padding: 16px;
  display: flex;
  margin-top: 16px;
  flex-direction: column;
  overflow: auto;

  .contentTable-main {
    flex: 1;
    overflow: auto;
  }

  .contentTable-footer {
    padding: 10px 0 0;
  }
}
.templateClass {
  color:#3562db;
  margin-left: 24px;
  cursor: pointer;
}
</style>
