<template>
  <PageContainer>
    <div slot="content" class="role-content" style="height: 100%">
      <div style="height: 100%">
        <div class="search-from">
          <div class="time">
            <span style="margin-left: 0px;" :class="{ active: timeType == '1' }"
              @click="changeTimeType('1')">损坏率统计</span>
            <span :class="{ active: timeType == '2' }" @click="changeTimeType('2')">丢失率统计</span>
            <span :class="{ active: timeType == '3' }" @click="changeTimeType('3')">洗涤费用统计</span>
          </div>
          <div class="date-type-selector">
            <div>
              <el-radio-group v-model="dateType" @change="handleDateTypeChange">
                <el-radio-button label="3">日</el-radio-button>
                <el-radio-button label="2">月</el-radio-button>
                <el-radio-button label="1">年</el-radio-button>
                <!-- <el-radio-button label="custom">自定义</el-radio-button> -->
              </el-radio-group>
            </div>
            <div>
              <el-date-picker v-if="dateType == '3'" v-model="dateRange" type="daterange"
                style="margin-left: 10px; height: 32px;" range-separator="至" start-placeholder="开始日期"
                end-placeholder="结束日期" :value-format="valueFormat" :clearable="false"></el-date-picker>
              <el-date-picker v-if="dateType == '2'" v-model="monthRange" type="monthrange" range-separator="至"
                start-placeholder="开始月份" end-placeholder="结束月份" @change="handleMonthRangeChange" value-format="yyyy-MM"
                style="margin-left: 10px; height: 32px; " />
              <el-date-picker v-if="dateType == '1'" style="margin-left: 10px; height: 40px; margin-top: 8px;"
                v-model="currentDate" type="year" value-format="yyyy" @change="handleDateChange"
                :clearable="false"></el-date-picker>
            </div>
            <div class="btns">
              <el-button type="primary" plain @click="resetForm">重置</el-button>
              <el-button type="primary" @click="searchForm">查询</el-button>
            </div>
          </div>
          <!-- 日期选择器 -->
          <div class="date-picker-wrapper">
            <div class="date-navigation">
              <span v-if="dateType == '3'" class="date-navigation-lable">{{ dateRange[0] }} - {{ dateRange[1] }}</span>
              <span v-if="dateType == '2'" class="date-navigation-lable">{{ dateMonth.startDate }} 至 {{
                dateMonth.endDate }}</span>
              <span v-if="dateType == '1'" class="date-navigation-lable">{{ yearRange.start }} - {{ yearRange.end
              }}</span>
            </div>
          </div>
        </div>
        <div class="data-display">
          <div class="chart-container">
            <div id="statis"></div>
          </div>
          <div class="contentTable">
            <div class="contentTable-main table-content">
              <el-table v-loading="tableLoading" border style="width: 100%" :data="tableData" height="100%" stripe>
                <el-table-column type="index" label="序号" width="80">
                  <template slot-scope="scope">
                    <span>{{ (pagination.current - 1) * pagination.size + scope.$index + 1 }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="title" label="布草名称" show-overflow-tooltip></el-table-column>
                <el-table-column prop="category_name" label="布草分类" show-overflow-tooltip></el-table-column>
                <el-table-column prop="spec_name" label="布草规格" show-overflow-tooltip></el-table-column>
                <el-table-column v-if="timeType == '1'" prop="damage_rate" label="损坏率"
                  show-overflow-tooltip></el-table-column>
                <el-table-column v-if="timeType == '2'" prop="damage_rate" label="丢失率"
                  show-overflow-tooltip></el-table-column>
                <el-table-column v-if="timeType == '3'" prop="nums" label="数量" show-overflow-tooltip></el-table-column>
                <el-table-column v-if="timeType == '3'" prop="price" label="单价" show-overflow-tooltip></el-table-column>
                <el-table-column v-if="timeType == '3'" prop="money" label="总价" show-overflow-tooltip></el-table-column>
              </el-table>
            </div>
            <div class="contentTable-footer">
              <el-pagination :current-page="pagination.current" :page-sizes="pagination.pageSizeOptions"
                :page-size="pagination.size" :layout="pagination.layoutOptions" :total="pagination.total"
                @size-change="paginationSizeChange" @current-change="paginationCurrentChange">
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PageContainer>
</template>
<script>
/* eslint-disable */
import * as echarts from 'echarts';
import moment from 'moment'
import tableListMixin from '@/mixins/tableListMixin.js'
export default {
  name: 'StatisticalAnalysis',
  mixins: [tableListMixin],
  data() {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = String(currentDate.getMonth() + 1).padStart(2, '0');
    const currentMonthStr = `${currentYear}-${currentMonth}`;
    return {
      monthRange: [currentMonthStr, currentMonthStr], // 默认选中当前月
      dateMonth: {
        startDate: this.getFirstDayOfMonth(currentMonthStr),
        endDate: this.getLastDayOfMonth(currentMonthStr)
      },
      value1: '',
      tableLoading: false,
      timeType: '1',
      dateType: '3', // day, month, year, custom
      currentDate: new Date().getFullYear().toString(), // 默认当天
      dateRange: [this.formatDate(new Date(), 'yyyy-MM-dd'), this.formatDate(new Date(), 'yyyy-MM-dd')],
      valueFormat: 'yyyy-MM-dd',
      tableData: [], // 原始数据
      chart: null,
      format: 'yyyy',
      circleArr: [],
      yearRange: {},
    }
  },
  mounted() {
    this.fetchDamageData('getGoodsDamageList');
    this.updateDateRange(this.currentDate)
  },
  methods: {
    changeTimeType(e) {
      this.timeType = e
      this.dateType = '3'
      if (this.timeType == '1') {
        this.fetchDamageData('getGoodsDamageList');
      } else if (this.timeType == '2') {
        this.fetchDamageData('getGoodsLossList');
      } else if (this.timeType == '3') {
        this.fetchDamageData('getGoodsWashList');
      }
      this.pagination.current = 1
      this.pagination.size = 15
      this.pagination.total = 0
    },
    formatDate(date, fmt) {
      const o = {
        'M+': date.getMonth() + 1,
        'd+': date.getDate(),
        'h+': date.getHours(),
        'm+': date.getMinutes(),
        's+': date.getSeconds(),
        'q+': Math.floor((date.getMonth() + 3) / 3),
        'S': date.getMilliseconds()
      };
      if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
      }
      for (const k in o) {
        if (new RegExp('(' + k + ')').test(fmt)) {
          fmt = fmt.replace(RegExp.$1,
            (RegExp.$1.length === 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)));
        }
      }
      return fmt;
    },
    // 选择月
    handleMonthRangeChange(val) {
      if (val && val.length === 2) {
        // 处理开始月份 - 设置为该月1日
        const startMonth = val[0];
        this.dateMonth.startDate = this.getFirstDayOfMonth(startMonth);

        // 处理结束月份 - 设置为该月最后一天
        const endMonth = val[1];
        this.dateMonth.endDate = this.getLastDayOfMonth(endMonth);
      } else {
        // 清空选择时，重置为默认值（当前月）
        const currentDate = new Date();
        const currentYear = currentDate.getFullYear();
        const currentMonth = String(currentDate.getMonth() + 1).padStart(2, '0');
        const currentMonthStr = `${currentYear}-${currentMonth}`;

        this.monthRange = [currentMonthStr, currentMonthStr];
        this.dateMonth.startDate = this.getFirstDayOfMonth(currentMonthStr);
        this.dateMonth.endDate = this.getLastDayOfMonth(currentMonthStr);
      }
    },
    // 获取月份的第一天
    getFirstDayOfMonth(monthStr) {
      const [year, month] = monthStr.split('-').map(Number);
      const date = new Date(year, month - 1, 1);
      return this.formatDate(date);
    },

    // 获取月份的最后一天
    getLastDayOfMonth(monthStr) {
      const [year, month] = monthStr.split('-').map(Number);
      // 下个月的第0天就是本月的最后一天
      const date = new Date(year, month, 0);
      return this.formatDate(date);
    },

    // 格式化日期为 YYYY-MM-DD
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    // 点击日期类型
    handleDateTypeChange() {
      if (this.dateType == '1') {
        this.currentDate = this.formatDate(new Date(), this.format);
      } else {
        this.dateRange = [
          this.formatDate(new Date(), this.valueFormat),
          this.formatDate(new Date(), this.valueFormat)
        ];
      }
      this.updateChart();
    },
    handleDateChange(year) {
      this.updateDateRange(year)

    },
    // 选择年
    updateDateRange(year) {
      if (!year) return
      const yearNum = parseInt(year)
      const startDate = new Date(yearNum, 0, 1) // 1月1日
      const endDate = new Date(yearNum, 11, 31) // 12月31日
      // 格式化日期显示
      this.yearRange = {
        start: this.formatDate(startDate, this.valueFormat),
        end: this.formatDate(endDate, this.valueFormat)
      }
    },

    // 获取损坏统计
    fetchDamageData(str) {
      this.tableData = []
      this.circleArr = []
      // 模拟数据获取
      let data = {
        goodsId: '0',
        dateType: '3',
        page: this.pagination.current,
        pagesize: this.pagination.size,
      }
      if (this.dateType == '3') {
        data.startTime = this.dateRange.length ? this.dateRange[0] : ''
        data.entTime = this.dateRange.length ? this.dateRange[1] : ''
      } else if (this.dateType == '1') {
        data.startTime = this.yearRange.start
        data.entTime = this.yearRange.end
      } else {
        data.startTime = this.dateMonth.startDate
        data.entTime = this.dateMonth.endDate
      }
      if (this.dateType == '1') {
        data.dateType = '1'
      } else if (this.dateType == '2') {
        data.dateType = '2'
      }
      this.tableLoading = true
      this.$api[str](data).then((res) => {
        if (res.code == '200') {
          if (res.data.length == '0') {
            this.circleArr = [
              {
                title: this.formatDate(new Date(), 'yyyy-MM-dd'),
                damage_rate: '0%'
              }
            ]
          } else {
            this.circleArr = res.data
          }
          this.tableData = res.data
          this.pagination.total = parseInt(res.count)
          this.initChart();
        }
      })
      this.tableLoading = false
      this.updateChart();
    },
    // 初始化图表
    initChart() {
      this.chart = echarts.init(document.getElementById('statis'));
      this.updateChart();
    },
    // 重置
    resetForm() {
      if (this.timeType == '1') {
        this.dateRange = []
        this.currentDate = ''
        this.fetchDamageData('getGoodsDamageList');
      } else if (this.timeType == '2') {
        this.dateRange = []
        this.currentDate = ''
        this.fetchDamageData('getGoodsLossList');
      } else if (this.timeType == '3') {
        this.dateRange = []
        this.currentDate = ''
        this.fetchDamageData('getGoodsWashList');
      }
    },
    //查询
    searchForm() {
      if (this.timeType == '1') {
        this.fetchDamageData('getGoodsDamageList');
      } else if (this.timeType == '2') {
        this.fetchDamageData('getGoodsLossList');
      } else if (this.timeType == '3') {
        this.fetchDamageData('getGoodsWashList');
      }
    },
    //分页相关
    paginationSizeChange(val) {
      this.pagination.current = 1
      this.pagination.size = val
      if (this.timeType == '1') {
        this.fetchDamageData('getGoodsDamageList');
      } else if (this.timeType == '2') {
        this.fetchDamageData('getGoodsLossList');
      } else if (this.timeType == '3') {
        this.fetchDamageData('getGoodsWashList');
      }
    },
    paginationCurrentChange(val) {
      this.pagination.current = val
      if (this.timeType == '1') {
        this.fetchDamageData('getGoodsDamageList');
      } else if (this.timeType == '2') {
        this.fetchDamageData('getGoodsLossList');
      } else if (this.timeType == '3') {
        this.fetchDamageData('getGoodsWashList');
      }
    },
    updateChart() {
      if (!this.chart) return;
      const xAxisData = this.circleArr.map(item => item.title);
      const seriesData = this.circleArr.map(item => parseFloat(item.damage_rate));
      const option = {
        grid: {
          top: '12%',
          left: '1%',
          right: '1%',
          bottom: '1%',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: xAxisData
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: '{value}%' // 添加百分号
          },
        },
        series: [{
          data: seriesData,
          type: 'bar',
          barWidth: 30,
          showBackground: true,
          backgroundStyle: {
            color: 'rgba(180, 180, 180, 0.2)'
          }
        }]
      };

      this.chart.setOption(option);
      window.addEventListener('resize', () => {
        this.chart.resize()
      })
    }

  }
}
</script>
<style lang="scss" scoped>
.role-content {
  height: 100%;
  background-color: #fff;
  padding: 0px 10px;
}

.time {
  display: flex;
  height: 50px;

  span {
    line-height: 50px;
    font-size: 15px;
    color: #414653;
    margin: 0 16px;
    cursor: pointer;
  }
}

.active {
  color: #3562db !important;
  border-bottom: 2px solid #3562db;
}

.date-filter-container {
  padding: 20px;
}

.date-type-selector {
  width: 100%;
  height: 50px;
  display: flex;
  align-items: center;
}

.date-picker-wrapper {
  width: 100%;
  height: 40px;
  line-height: 40px;
}

.date-navigation {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

.custom-date-range {
  flex: 1;
}

.data-display {
  height: calc(100% - 142px);
  // width: 100%;
  // height: 100%;
}

.chart-container {
  padding: 20px 10px;
  border-radius: 4px;
}

#statis {
  width: 100%;
  height: 200px;
}

.contentTable {
  height: calc(100% - 245px);
  display: flex;
  flex-direction: column;

  .contentTable-main {
    flex: 1;
    overflow: auto;
  }

  .contentTable-footer {
    padding: 10px 0 0;
  }
}

.date-navigation-lable {
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #333333;
}

::v-deep .el-radio-button__inner {
  height: 32px;
  line-height: 7px;
}

.btns {
  margin-left: 10px;
}
</style>
