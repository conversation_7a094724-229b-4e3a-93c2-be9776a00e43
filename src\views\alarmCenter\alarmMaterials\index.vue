<template>
  <PageContainer>
    <div slot="content" class="alarmConfiguration-body">
      <div class="alarmConfiguration-content-title">
        <div class="title">报警配置</div>
      </div>
      <div class="alarmConfiguration-content">
        <div style="height: 100%">
          <div class="search-from">
            <div>
              <el-select v-model="searchForm.alarmSystemCode" multiple collapse-tags placeholder="请选择报警系统" @change="alarmSystemChange">
                <el-option v-for="item in alarmSystemList" :key="item.thirdSystemCode" :label="item.thirdSystemName" :value="item.thirdSystemCode"></el-option>
              </el-select>
              <el-select v-model="searchForm.alarmTypeCode" multiple collapse-tags placeholder="请选择报警类型" filterable :disabled="!searchForm.alarmSystemCode" class="ml-16">
                <el-option v-for="item in alarmTypeList" :key="item.id" :label="item.alarmDictName" :value="item.id"> </el-option>
              </el-select>
              <el-cascader
                v-model="searchForm.assetTypeCode"
                :options="treeData"
                :props="riskPropsType"
                placeholder="请选择设备类型"
                clearable
                class="cascaderWid"
                collapse-tags
                style="margin-left: 15px; height: 32px"
              ></el-cascader>
              <el-select v-model="searchForm.emergencyResourceIds" multiple collapse-tags placeholder="请选择应急资源" filterable class="ml-16">
                <el-option v-for="item in fieldsConfigData" :key="item.id" :label="item.name" :value="item.id"> </el-option>
              </el-select>
              <el-button type="primary" plain style="margin-left: 10px" @click="reset">重置</el-button>
              <el-button type="primary" @click="search">查询</el-button>
            </div>
            <div>
              <el-button type="primary" icon="el-icon-plus" @click="handleListEvent('add')">新建附近物资</el-button>
            </div>
          </div>
          <div class="contentTable">
            <div class="contentTable-main table-content">
              <el-table v-loading="tableLoading" border style="width: 100%" :data="tableData" :height="tableHeight">
                <el-table-column prop="alarmSystemName" label="报警系统" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span>{{ scope.row.alarmSystemName }}</span>
                    <span v-if="scope.row.alarmSystemCount">({{ scope.row.alarmSystemCount }})</span>
                  </template>
                </el-table-column>
                <el-table-column prop="alarmTypeName" label="报警类型" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span>{{ scope.row.alarmTypeName }}</span>
                    <span v-if="scope.row.alarmTypeCount">({{ scope.row.alarmTypeCount }})</span>
                  </template>
                </el-table-column>
                <el-table-column prop="assetTypeName" label="设备类型" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span>{{ scope.row.assetTypeName }} </span>
                    <span v-if="scope.row.assetTypeCount">({{ scope.row.assetTypeCount }})</span>
                  </template>
                </el-table-column>
                <el-table-column prop="emergencyResourceNames" label="应急资源类型" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span>{{ scope.row.emergencyResourceNames }} </span>
                    <span v-if="scope.row.emergencyResourceCount">({{ scope.row.emergencyResourceCount }})</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="180" fixed="right">
                  <template slot-scope="scope">
                    <el-button type="text" class="record" @click="goDetail(scope.row)">查看</el-button>
                    <el-button type="text" class="record" @click="operation(scope.row, 'edit')">编辑</el-button>
                    <el-button type="text" class="record" @click="operation(scope.row, 'delete')">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="contentTable-footer">
              <el-pagination
                :current-page="pagination.current"
                :page-sizes="pagination.pageSizeOptions"
                :page-size="pagination.size"
                :layout="pagination.layoutOptions"
                :total="pagination.total"
                @size-change="paginationSizeChange"
                @current-change="paginationCurrentChange"
              >
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin.js'
export default {
  name: 'alarmMaterialsIndex',
  mixins: [tableListMixin],
  beforeRouteEnter(to, from, next) {
    // 进入页面时，先将当前页面的 name 信息存入 keep-alive 全局状态（当前页面的name需要和路由的name一致）
    next((vm) => {
      // 二级页面存储当前级，多级页面存储多级
      vm.$store.commit('keepAlive/add', 'alarmConfigIndex')
    })
  },
  async beforeRouteLeave(to, from, next) {
    // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
    if (!['addAlarmMaterialsConfig'].includes(to.name)) {
      // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      direction: 'rtl',
      searchForm: {
        alarmSystemCode: '', // 报警系统
        alarmTypeCode: '', // 报警类型
        assetTypeCode: '', // 设备类型
        emergencyResourceIds: '' // 应急资源
      },
      alarmSystemList: [],
      alarmTypeList: [],
      treeData: [],
      fieldsConfigData: [],
      riskPropsType: {
        children: 'children',
        label: 'name',
        value: 'id',
        checkStrictly: true,
        multiple: true
      },
      tableLoading: false,
      tableData: [],
      produceUnfold: false,
      sureUnfold: false
    }
  },
  mounted() {
    this.getAlarmSystemList()
    this.getDataList()
    this.getEquipmentData()
    this.getfieldsConfigData()
  },
  methods: {
    // 列表数据
    getDataList() {
      this.tableLoading = true
      let data = {
        alarmSystemCode: this.searchForm.alarmSystemCode ? this.searchForm.alarmSystemCode.join(',') : '',
        alarmTypeCode: this.searchForm.alarmTypeCode ? this.searchForm.alarmTypeCode.join(',') : '',
        assetTypeCode: this.searchForm.assetTypeCode ? this.searchForm.assetTypeCode.join(',') : '',
        emergencyResourceIds: this.searchForm.emergencyResourceIds ? this.searchForm.emergencyResourceIds.join(',') : '',
        pageNo: this.pagination.current,
        pageSize: this.pagination.size
      }
      this.$api.getAlarmMaterialsList(data).then((res) => {
        this.tableLoading = false
        if (res.code == 200) {
          this.tableData = res.data ? res.data.records : []
          this.pagination.total = res.data ? res.data.total : 0
        } else if (res.message) {
          this.tableData = []
          this.pagination.total = 0
          this.$message.error(res.message)
        }
      })
    },
    // 报警系统
    getAlarmSystemList() {
      this.$api.getAlarmThirdSystemData().then((res) => {
        if (res.code == 200) {
          this.alarmSystemList = res.data
        }
      })
    },
    // 获取设备类型
    getEquipmentData() {
      this.$api.getEquipmentType().then((res) => {
        if (res.code == '200') {
          this.treeData = res.data
        }
      })
    },
    //获取应急资源
    // 获取应急资源
    getfieldsConfigData() {
      this.$api.getfieldsConfigList().then((res) => {
        if (res.code == '200') {
          this.fieldsConfigData = res.data
        }
      })
    },
    // 获取实体类型
    getAlarmTypeData(thirdSystemCode) {
      let params = {
        thirdSystemCode: thirdSystemCode ? thirdSystemCode.join(',') : ''
      }
      this.$api.getAlarmThirdTypeData(params).then((res) => {
        if (res.code == 200) {
          this.alarmTypeList = res.data
        }
      })
    },
    // 报警系统改变
    alarmSystemChange(el) {
      this.getAlarmTypeData(el)
    },
    handleListEvent(type) {
      console.log(type, 'type')

      this.$router.push({
        path: '/alarmMaterials/addAlarmMaterialsConfig',
        query: {
          type: type
        }
      })
    },
    // 重置
    reset() {
      this.searchForm = {
        alarmSystemCode: '', // 报警系统
        alarmTypeCode: '', // 报警类型
        assetTypeCode: '', // 设备类型
        emergencyResourceIds: '' // 应急资源
      }
      this.pagination.size = 15
      this.pagination.current = 1
      this.getDataList()
    },
    // 查询
    search() {
      this.pagination.current = 1
      this.getDataList()
    },

    // 列表操作
    operation(row, type) {
      // 编辑
      if (type == 'edit') {
        this.$router.push({
          path: '/alarmMaterials/addAlarmMaterialsConfig',
          query: {
            baseId: row.id,
            type: type
          }
        })
      } else if (type == 'delete') {
        this.$confirm('确认删除该报警附近物资吗？', '信息提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '继续删除',
          cancelButtonText: '放弃',
          type: 'warning'
        })
          .then(() => {
            this.$api.getAlarmMaterialsDelete({ id: row.id }).then((res) => {
              if (res.code == '200') {
                this.$message({
                  type: 'success',
                  message: '删除成功'
                })
                this.getDataList()
              } else {
                this.$message({
                  type: 'error',
                  message: res.message || '删除失败'
                })
              }
            })
          })
          .catch(() => {})
      }
    },
    // 详情
    goDetail(row) {
      this.$router.push({
        path: '/alarmMaterials/addAlarmMaterialsConfig',
        query: {
          baseId: row.id,
          type: 'detail'
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.alarmConfiguration-body {
  height: 100%;
  width: 100%;
  background: #fff;
  .alarmConfiguration-content-title {
    display: flex;
    justify-content: space-between;
    padding: 23px 24px 0 24px;
    .title {
      font-weight: bold;
      font-size: 16px;
      color: #333333;
    }
  }
  .alarmConfiguration-content {
    padding: 8px 24px;
    height: calc(100% - 50px);
    .search-from {
      padding-bottom: 12px;
      display: flex;
      justify-content: space-between;
      & > div {
        margin-right: 10px;
      }
      & > button {
        margin-top: 12px;
      }
    }
    .contentTable {
      margin-top: 16px;
      height: calc(100% - 80px);
      display: flex;
      flex-direction: column;
      .contentTable-main {
        flex: 1;
        overflow: auto;
      }
      .contentTable-footer {
        padding: 10px 0 0;
      }
      .alarmState {
        padding: 2px 8px;
        background: #e6effc;
        border-radius: 4px;
      }
      .alarmState3 {
        background: #ffece8;
        color: #cb2634;
      }
      .alarmState2 {
        background: #fff7e8;
        color: #d25f00;
      }
      .alarmState1 {
        background: #e8ffea;
        color: #009a29;
      }
      .alarmState0 {
        background: #e6effc;
        color: #2749bf;
      }
    }
  }
}
.record {
  color: #3562db !important;
}
.ml-16 {
  margin-left: 16px;
}
.columnList {
  cursor: default;
  font-style: 12px;
  color: #7f848c;
  display: flex;
  div {
    width: 30%;
    margin-right: 20px;
    padding: 5px;
  }
}
.down-popover {
  .groupTips {
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    .title {
      width: 30%;
      text-align: left;
    }
    .extra {
      height: 30px;
      margin-top: 5px;
    }
    .group {
      width: 40%;
      text-align: left !important;
      .groupType {
        display: inline-block;
        padding: 1px 4px;
        border-radius: 2px;
        border: 1px solid #dcdfe6;
        color: #cacaca;
        margin-left: 8px;
        font-size: 10px;
      }
      .groupLable {
        display: inline-block;
        height: 16px;
        width: 100px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .phone {
      width: 30%;
      text-align: left;
    }
    .bubble {
      display: inline-block;
      width: 31px;
      height: 16px;
      background: #e4e7ed;
      border-radius: 99px 99px 99px 99px;
      font-weight: 400;
      font-size: 12px;
      color: #666666;
      line-height: 14px;
      text-align: center;
      margin: 0px 10px;
    }
  }
}
::v-deep .popoverBtn.el-button--text {
  color: #666666 !important;
}
::v-deep .el-dialog__body {
  padding: 10px 20px !important;
}
::v-deep .el-input__inner {
  height: 32px !important;
  line-height: 32px !important;
}
::v-deep .el-cascader__tags {
  max-height: 32px !important;
  margin-top: 5px;
  flex-wrap: nowrap !important;
}
.noticeBody {
  padding: 0 20px;
}
.produceBox {
  .produceBox-title {
    padding: 5px 0;
    font-size: 16px;
  }
  .produceBox-content {
    .noticeWay {
      display: inline-block;
      width: 150px;
    }
    .noticeDeptOrPerson {
      display: inline-block;
      width: 240px;
      margin-left: 60px;
      text-align: left;
    }
    .noticeContactWay {
      margin-left: 60px;
    }
  }
}
</style>