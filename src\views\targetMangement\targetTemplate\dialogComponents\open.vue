<template>
  <div class="tmpContainer">
    <div class="content-top">
      <div class="topContent">
        <div class="title isActive">
          <div class="num">1</div>
          <div>选择指标</div>
        </div>
        <div class="line" :style="{ background: !step === true ? '#3562DB' : '#cccccc' }"></div>
        <div :class="{ isActive: !step === true }" class="title">
          <div class="num">2</div>
          <span>配置权重</span>
        </div>
      </div>
    </div>
    <div v-if="step" class="content-bottom">
      <div class="leftTree">
        <div class="search">
          <el-input v-model="filterText" placeholder="请输入" clearable suffix-icon="el-icon-search"> </el-input>
        </div>
        <el-tree
          ref="tree"
          v-loading="treeLoading"
          :data="treeList"
          :props="defaultProps"
          node-key="node"
          :highlight-current="true"
          :filter-node-method="filterNode"
          @node-click="handleNodeClick"
        ></el-tree>
      </div>
      <div class="rightTable">
        <div class="topFilter">
          <div>
            <el-input
              v-model.trim="keyWord"
              placeholder="请输入指标名称"
              style="width: 200px"
              clearable
              maxlength="25"
              onkeyup="if(value.length>25)value=value.slice(0,25)"
            ></el-input>
            <el-button type="primary" plain style="margin-left: 10px" @click="resetForm">重置</el-button>
            <el-button type="primary" @click="searchForm">查询</el-button>
          </div>
        </div>
        <div class="tableContainer">
          <el-table ref="multipleTable" v-loading="tableLoading" border style="width: 100%" :data="tableData" :row-class-name="tableRowClassName">
            <el-table-column prop="name" label="指标名称" show-overflow-tooltip></el-table-column>
            <el-table-column prop="type" label="指标类型" show-overflow-tooltip>
              <template slot-scope="scope">
                <span> {{ formatName(scope.row.type) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="nodeName" label="指标分类" show-overflow-tooltip></el-table-column>
            <el-table-column prop="dataType" label="数据类型" show-overflow-tooltip>
              <template slot-scope="scope">
                <span> {{ scope.row.dataType == 0 ? '数值' : scope.row.dataType == 1 ? '百分比' : '时长' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="remark" label="参考值" show-overflow-tooltip></el-table-column>
            <el-table-column prop="illustrate" label="指标说明" show-overflow-tooltip></el-table-column>
            <el-table-column prop="illustrate" label="操作" show-overflow-tooltip>
              <template slot-scope="scope">
                <el-button type="text" @click="selectSingleRow(scope.row)">选择</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            :current-page="pagination.current"
            :page-sizes="pagination.pageSizeOptions"
            :page-size="pagination.size"
            :layout="pagination.layoutOptions"
            :total="pagination.total"
            @size-change="paginationSizeChange"
            @current-change="paginationCurrentChange"
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <div v-else class="content-bottom">
      <div class="leftTree">
        <el-form ref="form" :model="selectList" label-width="150px" label-position="top">
          <el-form-item label="指标名称：">
            <span>{{ selectList.name }}</span>
          </el-form-item>
          <el-form-item label="指标权重：">
            <span>
              <el-input v-model="selectList.weights" placeholder="请输入指标权重" @input="(val) => percentageChange(val, selectList, 'weights')">
                <template slot="append">%</template>
              </el-input>
            </span>
          </el-form-item>
          <el-form-item label="参考值：">
            <span>{{ selectList.remark }}</span>
          </el-form-item>
          <el-form-item label="指标说明">
            <span>{{ selectList.illustrate }}</span>
          </el-form-item>
        </el-form>
      </div>
      <div class="rightTable">
        <div v-if="selectList.dataType == 0" class="numType">
          <div class="typeItem">
            <span>分值类型：</span>
            <el-radio-group v-model="numberType" @input="changeNumType">
              <el-radio :label="0">得分机制</el-radio>
              <el-radio :label="1">扣分机制</el-radio>
            </el-radio-group>
          </div>
          <div class="typeItem">
            <span>得分类型：</span>
            <el-radio-group v-model="intervalType" @input="changeInterval">
              <el-radio :label="0">区间机制</el-radio>
              <el-radio :label="1">数量机制</el-radio>
            </el-radio-group>
          </div>
        </div>
        <el-table
          :key="tableKey"
          border
          stripe
          style="width: 100%"
          :height="selectList.dataType === 0 ? 'calc(100% - 88px)' : 'calc(100% - 48px)'"
          :data="selectList.calculationMethodList"
          :row-class-name="tableRowClassName"
        >
          <el-table-column v-if="numberType == 0" prop="score" label="得分值">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.score"
                :controls="false"
                :min="0"
                :precision="0"
                placeholder="请输入得分"
                @change="(val) => percentageChange(val, scope.row, 'score')"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column v-if="numberType == 1" prop="scoreValue" label="扣分">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.scoreValue"
                :controls="false"
                :min="0"
                :precision="0"
                placeholder="请输入分值"
                @change="(val) => percentageChange(val, scope.row, 'score')"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column v-if="intervalType == 0" prop="min" label="最小值">
            <template slot-scope="scope">
              <el-input
                v-if="selectList.dataType === 0 || selectList.dataType === 1"
                v-model="scope.row.min"
                placeholder="请输入最小值"
                @change="(val) => percentageChange(val, scope.row, 'min')"
              >
                <template v-if="selectList.dataType === 1" slot="append">%</template>
              </el-input>
              <div v-if="selectList.dataType === 2" class="dateHours">
                <el-input v-model="scope.row.minTime[0].hours"></el-input>
                <div style="white-space: nowrap">小时</div>
                <el-input v-model="scope.row.minTime[0].minute" @change="(e) => minTimeChange(e, scope.row)"></el-input>
                <div style="white-space: nowrap">分钟</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column v-if="intervalType == 0" prop="max" label="最大值">
            <template slot-scope="scope">
              <el-input
                v-if="selectList.dataType === 0 || selectList.dataType === 1"
                v-model="scope.row.max"
                placeholder="请输入最大值"
                @change="(val) => regMax(val, scope.row, selectList.dataType)"
              >
                <template v-if="selectList.dataType === 1" slot="append">%</template>
              </el-input>
              <div v-if="selectList.dataType === 2" class="dateHours">
                <el-input v-model="scope.row.maxTime[0].hours"></el-input>
                <div style="white-space: nowrap">小时</div>
                <el-input v-model="scope.row.maxTime[0].minute" @change="(e) => maxTimeChange(e, scope.row)"></el-input>
                <div style="white-space: nowrap">分钟</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column v-if="intervalType == 1" prop="number" label="每x个">
            <template slot-scope="scope">
              <el-input
                v-model.number="scope.row.number"
                :controls="false"
                :min="0"
                :precision="0"
                placeholder="请输入个数"
                @change="(val) => percentageChange(val, scope.row)"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="操作" show-overflow-tooltip width="100">
            <template slot-scope="scope">
              <el-button type="text" @click="del(scope.$index)"> 删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="addBtn">
          <el-button v-if="numberType == 0" type="primary" @click="add">添加得分计算方法</el-button>
          <el-button v-if="numberType == 1" type="primary" @click="add">添加扣分计算方法</el-button>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'openTargetTemplate',
  props: {
    step: {
      type: Boolean,
      default: true
    },
    selectedId: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      treeLoading: false,
      treeList: [],
      defaultProps: {
        label: 'nodeName',
        children: 'nodeList'
      },
      treeLevel: '',
      nodeName: '',
      nodeCode: '',
      nodeType: '',
      filterText: '',
      keyWord: '',
      tableLoading: false,
      tableData: [],
      pagination: {
        current: 1,
        size: 10,
        total: 0
      },
      selectList: {
        scoreMechanism: 0, // 0: 得分 1: 扣分
        intervalNumber: 0, // 0: 区间 1: 数量
        calculationMethodList: []
      },
      minHours: [],
      maxHours: [],
      numberType: 0,
      intervalType: 0,
      tableKey: Math.random()
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  mounted() {
    this.getTreeList()
  },
  methods: {
    formatName(type) {
      if (type == 0) {
        return '组织架构'
      } else if (type == 1) {
        return '设备'
      } else if (type == 2) {
        return '空间'
      } else if (type == 3) {
        return '值班考勤'
      } else if (type == 4) {
        return '业务'
      }
    },
    // ====================================================================第一步=======================================================
    // 获取树列表
    getTreeList() {
      this.treeLoading = true
      this.$api
        .getLibraryTreeList({})
        .then((res) => {
          if (res.code === '200') {
            this.treeList = res.data
          }
        })
        .finally(() => {
          this.treeLoading = false
        })
    },
    // 过滤节点
    filterNode(value, data) {
      if (!value) return true
      return data.nodeName.indexOf(value) !== -1
    },
    // 左侧树节点点击
    handleNodeClick(val, node) {
      this.treeLevel = node.level
      this.nodeName = val.nodeName
      this.nodeCode = val.node
      this.nodeType = this.treeLevel == '1' ? val.node : val.type
      this.getDataList()
    },
    // 获取右侧列表数据
    getDataList() {
      this.tableLoading = true
      let data = {
        nodeCode: this.treeLevel == 1 ? '' : this.nodeCode,
        nodeName: this.treeLevel == 1 ? '' : this.nodeName,
        name: this.keyWord,
        page: this.pagination.current,
        pageSize: this.pagination.size,
        type: this.nodeType,
        excludeIds: this.selectedId
      }
      this.$api
        .getLibraryPageList(data)
        .then((res) => {
          this.tableLoading = false
          if (res.code == 200) {
            this.tableData = res.data ? res.data.records : []
            this.pagination.total = res.data ? res.data.total : 0
          } else if (res.message) {
            this.tableData = []
            this.pagination.total = 0
            this.$message.error(res.message)
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    paginationSizeChange(val) {
      this.pagination.size = val
      this.getDataList()
    },
    paginationCurrentChange(val) {
      this.pagination.current = val
      this.getDataList()
    },
    // 重置
    resetForm() {
      this.keyWord = ''
      this.pagination.size = 15
      this.pagination.current = 1
      this.getDataList()
    },
    // 查询
    searchForm() {
      this.pagination.current = 1
      this.getDataList()
    },
    // table隔行变色
    tableRowClassName({ row, rowIndex }) {
      if (rowIndex % 2 == 1) {
        return 'warning-row'
      } else {
        return 'success-row'
      }
    },
    // 处理表格数据单行选中
    selectSingleRow(row, flag = true) {
      this.selectList = row
      if (flag) {
        this.selectList.indicatorLibraryId = row.id
        this.selectList.calculationMethodList = []
      }
      delete this.selectList.id
      this.$emit('choose', this.selectList)
    },
    //  ====================================================================第二步=======================================================
    // 权重校验
    percentageChange(val, row, name) {
      if (name === 'score') {
        let reg = /^(?:0|[1-9][0-9]?|100)$/
        if (!reg.test(val)) {
          this.$message.error('请输入0~100的正整数')
          row[name] = ''
          return
        }
      } else {
        let reg = /^([1-9]\d*|[0]{1,1})$/
        if (!reg.test(val)) {
          this.$message.error('输入错误')
          row[name] = ''
          return
        }
      }
      if (name === 'min') {
        if (row.max && Number(val) > Number(row.max)) {
          this.$message.error('最小值不能大于最大值')
          row[name] = ''
        }
      }
    },
    // 最大值校验
    regMax(val, row, type) {
      if (type === 1) {
        // 百分比类型，最大值只能是100
        this.percentageChange(val, row, 'max')
      } else {
        let reg = /^[0-9]*[1-9][0-9]*$/
        if (!reg.test(val)) {
          this.$message.error('最大值输入错误')
          row.max = ''
          return
        }
        if (row.min && Number(val) < Number(row.min)) {
          this.$message.error('最小值不能大于最大值')
          row.max = ''
        }
      }
    },
    // 时间最小值最大值校验
    minTimeChange(val, row, name) {
      let reg = /^(?:0|[1-9][0-9]?|59)$/
      if (!reg.test(val)) {
        this.$message.error('请输入0~59的正整数')
        row.minTime[0].minute = ''
      }
      if (row.maxTime[0].hours && row.maxTime[0].minute && row.minTime[0].hours) {
        let totalMax = row.maxTime[0].hours * 60 + row.maxTime[0].minute
        let totalMin = row.minTime[0].hours * 60 + val
        if (totalMin > totalMax) {
          this.$message.error('时间输入错误')
          row.minTime[0].hours = ''
          row.minTime[0].minute = ''
        }
      }
    },
    maxTimeChange(val, row, name) {
      let reg = /^(?:0|[1-9][0-9]?|59)$/
      if (!reg.test(val)) {
        this.$message.error('请输入0~59的正整数')
        row.maxTime[0].minute = ''
      }
      if (row.minTime[0].hours && row.minTime[0].minute && row.maxTime[0].hours) {
        let totalMin = Number(row.minTime[0].hours) * 60 + Number(row.minTime[0].minute)
        let totalMax = Number(row.maxTime[0].hours * 60) + Number(val)
        if (totalMax < totalMin) {
          this.$message.error('时间输入错误')
          row.maxTime[0].hours = ''
          row.maxTime[0].minute = ''
        }
      }
    },
    add() {
      this.selectList.calculationMethodList.push({
        score: '',
        min: '',
        max: '',
        maxTime: [
          {
            hours: '',
            minute: ''
          }
        ],
        minTime: [
          {
            hours: '',
            minute: ''
          }
        ]
      })
    },
    del(index) {
      this.selectList.calculationMethodList.splice(index, 1)
    },
    changeNumType(val) {
      this.selectList.calculationMethodList = []
      this.selectList.scoreMechanism = val
      this.tableKey = Math.random()
    },
    changeInterval(val) {
      this.selectList.intervalNumber = val
    }
  }
}
</script>
<style lang="scss" scoped>
.tmpContainer {
  width: 100%;
  height: 100%;
}
.content-top {
  width: 100%;
  height: 43px;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  .topContent {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 60%;
    height: 100%;
    .title {
      color: #86909c;
      display: flex;
      align-items: center;
      flex-direction: column;
      .num {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        text-align: center;
      }
    }
    .isActive {
      color: #3562db;
      .num {
        background: #3562db;
        color: #fff;
      }
    }
  }
  .line {
    width: 60%;
    height: 2px;
    background: #eee;
  }
}
.content-bottom {
  width: 100%;
  height: calc(100% - 43px);
  display: flex;
  justify-content: space-between;
  .leftTree {
    background-color: #fff;
    padding: 16px;
    width: 250px;
    height: 425px;
    overflow: auto;
    .search {
      margin-bottom: 16px;
    }
    :deep(.el-tree) {
      height: calc(100% - 48px);
      overflow: auto;
    }
  }
  .rightTable {
    background-color: #fff;
    width: calc(100% - 268px);
    height: 425px;
    padding: 16px;
    .numType {
      .typeItem {
        width: 50%;
        span {
          margin-right: 10px;
        }
      }
      height: 40px;
      display: flex;
    }
    .topFilter {
      width: 100%;
      margin-bottom: 16px;
      display: flex;
      justify-content: space-between;
      :deep(.el-input) {
        width: 200px;
      }
    }
    .tableContainer {
      height: calc(100% - 48px);
      .el-table {
        height: calc(100% - 48px);
        margin-bottom: 16px;
      }
    }
    .addBtn {
      margin-top: 16px;
      text-align: right;
    }
  }
  .targetForm {
    width: 100%;
    height: 425px;
    padding: 16px;
    background: #fff;
  }
}
::v-deep .el-form-item {
  margin-bottom: 0;
  color: #333333;
  .el-form-item__label {
    color: #7f848c;
    height: 40px;
    padding-bottom: 0;
  }
}
.dateHours {
  display: flex;
  align-items: center;
  justify-content: space-between;
  ::v-deep .el-input {
    padding: 5px;
  }
}
</style>
