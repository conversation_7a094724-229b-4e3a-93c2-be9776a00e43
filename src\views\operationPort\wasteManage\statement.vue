<template>
  <PageContainer>
    <div slot="header">
      <div class="searchForm">
        <div class="search-box">
          <el-cascader
            v-model="officeId"
            :options="deptOptions"
            :props="{ expandTrigger: 'hover', checkStrictly: true, label: 'deptName', value: 'id' }"
            placeholder="所属科室"
            clearable
            filterable
          ></el-cascader>
          <el-select v-model="wasteCode" placeholder="医废类型" style="margin-left: 16px">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
          <el-date-picker v-model="beginGatherTime" type="datetime" placeholder="选择开始时间" value-format="yyyy-MM-dd HH:mm:ss"> </el-date-picker>
          <el-date-picker v-model="endGatherTime" type="datetime" placeholder="选择结束时间" value-format="yyyy-MM-dd HH:mm:ss"> </el-date-picker>
          <div style="margin-left: 16px">
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button type="primary" plain @click="reset">重置</el-button>
          </div>
        </div>
      </div>
    </div>
    <div slot="content" ref="contentRef">
      <div class="table-box">
        <el-table v-loading="tableLoading" :data="tableData" border style="width: 100%" height="90%">
          <el-table-column label="上级科室" prop="officeParName" :resizable="false" align="center" show-overflow-tooltip=""></el-table-column>
          <el-table-column label="所属科室" prop="officeName" :resizable="false" align="center" show-overflow-tooltip=""></el-table-column>
          <el-table-column label="总袋数/总重量(Kg)" prop="totalbags" width="160" :resizable="false" align="center"></el-table-column>
          <el-table-column label="总袋数/感染类(Kg)" prop="infectedbags" width="160" :resizable="false" align="center"></el-table-column>
          <el-table-column label="总袋数/损伤类(Kg)" prop="damagebags" width="160" :resizable="false" align="center"></el-table-column>
          <el-table-column label="总袋数/病理类(Kg)" prop="pathoybags" width="160" :resizable="false" align="center"></el-table-column>
          <el-table-column label="总袋数/药物类(Kg)" prop="medicinebags" width="160" :resizable="false" align="center"></el-table-column>
          <el-table-column label="总袋数/化学类(Kg)" prop="chemistrybags" width="160" :resizable="false" align="center"></el-table-column>
          <el-table-column label="总袋数/涉疫类(Kg)" prop="epidemicbags" width="160" :resizable="false" align="center"></el-table-column>
          <el-table-column label="总袋数/其他(Kg)" prop="otherbags" width="160" :resizable="false" align="center"></el-table-column>
        </el-table>
        <el-pagination
          :current-page="pageNo"
          :page-sizes="[15, 30, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import store from '@/store/index'
import { transData } from '@/util'
export default {
  name: 'statement',
  data() {
    return {
      dateType: 'day',
      dateVal: '',
      tableLoading: false,
      tableData: [],
      pageNo: 1,
      pageSize: 15,
      total: 0,
      officeId: '',
      deptOptions: [],
      wasteCode: '',
      options: [
        {
          value: '0',
          label: '损伤类'
        },
        {
          value: '1',
          label: '病理类'
        },
        {
          value: '2',
          label: '化学类'
        },
        {
          value: '3',
          label: '感染类'
        },
        {
          value: '4',
          label: '药物类'
        },
        {
          value: '5',
          label: '涉疫类'
        },
        {
          value: '6',
          label: '其他'
        }
      ],
      beginGatherTime: '',
      endGatherTime: ''
    }
  },
  created() {},
  mounted() {
    this.getTableData()
    this.getDeptList()
  },
  methods: {
    changeDateType(type) {
      this.dateType = type
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.pageNo = 1
      this.getTableData()
    },
    getTableData() {
      this.tableLoading = true
      let params = {
        officeId: this.officeId[this.officeId.length - 1],
        wasteCode: this.wasteCode,
        beginGatherTime: this.beginGatherTime,
        endGatherTime: this.endGatherTime,
        pageNo: this.pageNo,
        pageSize: this.pageSize
      }
      this.$api.getNewClassifyStatistics(params).then((res) => {
        this.tableLoading = false
        this.tableData = res.data.rows
        this.total = res.data.total
      })
    },
    handleCurrentChange(val) {
      this.pageNo = val
      this.getTableData()
    },
    goManualEntry() {
      this.$router.push({ path: '/wasteManage/inoutManage/manualEntry' })
    },
    handleSearch() {
      this.pageNo = 1
      this.getTableData()
    },
    reset() {
      this.officeId = ''
      this.wasteCode = ''
      this.beginGatherTime = ''
      this.endGatherTime = ''
      this.pageNo = 1
      this.getTableData()
    },
    // 获取部门列表
    getDeptList() {
      this.$api.getSelectedDept({}).then((res) => {
        if (res.code == 200) {
          this.deptOptions = transData(res.data, 'id', 'pid', 'children')
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.searchForm {
  display: flex;
  height: 80px;
  padding: 0 16px;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .el-input {
    width: 200px;
    margin-left: 16px;
  }

  > div {
    margin-right: 20px;
  }
}

.search-box {
  display: flex;
  align-items: center;
}

.container-content > div {
  height: 100%;
  margin-top: 16px;
}

.el-table {
  margin-bottom: 12px;
}

::v-deep .el-date-editor .el-range__icon {
  line-height: 24px;
}

.statistics {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
  height: 78px;
}

.statistics .item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-around;
  flex: 1;
  background-color: rgb(53 98 219 / 6%);
  margin-right: 8px;
  border-radius: 4px;
}

.statistics .item span:nth-child(2) {
  font-size: 18px;
  color: #3562db;
  font-weight: 700;
}

.statistics .hover-style {
  background-color: #fff;
  box-shadow: 0 0 10px 0 rgb(32 56 114 / 13%);
  border-bottom: 3px solid #3562db;
}

.statistics .pointer-style {
  cursor: pointer;
}

.statistics .pure {
  background-color: #fff;
}

.statistics .pure span:nth-child(2) {
  color: #121f3e;
}

::v-deep .el-dialog {
  height: 80vh;
  margin-top: 10vh;
}

::v-deep .el-dialog__wrapper {
  overflow: hidden;
}

::v-deep .el-dialog__body {
  height: 93%;
}

::v-deep .el-table__row:hover {
  cursor: pointer;
}

.btns {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.btns .date-btn {
  width: 100px;
  height: 32px;
  border: 1px solid #3562db;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  color: #3562db;
  margin-right: 12px;
  cursor: pointer;
}

.active-btn {
  background-color: #3562db;
  color: #fff !important;
}

.cards {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.cards > div {
  width: 49%;
  background-color: #fff;
  border-radius: 4px;
  height: 90px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.cards img {
  width: 45px;
  height: 45px;
}

.cards span:nth-child(2) {
  margin: 0 16px;
}

.cards span:nth-child(3) {
  font-size: 28px;
  font-weight: 700;
}

.cards span:nth-child(4) {
  font-size: 14px;
  color: #ccced3;
  margin-left: 5px;
  transform: translateY(3px);
}

.table-box {
  background-color: #fff;
  height: 80vh;
  padding: 16px;
}
</style>
