<!--
 * @Author: hedd
 * @Date: 2023-08-08 15:26:10
 * @LastEditTime: 2023-09-19 16:27:54
 * @FilePath: \ihcrs_pc\src\views\monitor\boilerMenu\boilerMonitor.vue
 * @Description:
-->
<template>
  <PageContainer>
    <div slot="content" v-loading="loading" class="boilerMonitor-content">
      <div class="content-title">
        <div ref="tabs" class="entity-tabs">
          <div
            v-for="(item, index) in entityTabList"
            :key="index"
            :class="{
              'entity-tab-name': true,
              'entity-tab-name-active': item.code === entityTabActive
            }"
            @click="changeActiveTab(item.code)"
          >
            {{ item.name }}
          </div>
        </div>
        <div class="modules-type">
          <div
            :class="{
              'pattern-item': true,
              'pattern-item-active': currentPattern == 1
            }"
            @click="switchPattern(1)"
          >
            <svg-icon :name="currentPattern == 1 ? 'listModeActiveYellow' : 'listModeActive'" class="pattern-icon" />
            <span>剖面图模式</span>
          </div>
          <div
            :class="{
              'pattern-item': true,
              'pattern-item-active': currentPattern == 2
            }"
            @click="switchPattern(2)"
          >
            <svg-icon :name="currentPattern == 2 ? 'chartModeActiveYellow' : 'chartModeActive'" class="pattern-icon" />
            <span>系统图模式</span>
          </div>
        </div>
      </div>
      <div class="content-aside">
        <graphics-mode v-if="currentPattern == 1" ref="scadaShow" scadaType="profile" :surveyEntityCode="searchFrom.entityMenuCode" :projectId="searchFrom.projectCode" />
        <graphics-mode v-else ref="scadaShow" :entityMenuCode="searchFrom.entityMenuCode" :projectId="searchFrom.projectCode" />
        <div v-if="currentPattern == 1" class="video-list-box">
          <div class="video-header">视频监控</div>
          <div class="video-list">
            <div v-for="(item, index) in videoList" :key="index" class="video-list-li" @click="openVideoDialog(item)">
              <img src="@/assets/images/monitor/monitoring_points.png" alt="" />
              <p>{{ item.vidiconName }}</p>
            </div>
            <div v-if="!videoList.length" class="no-video">暂无关联摄像机列表</div>
          </div>
        </div>
      </div>
      <template v-if="cameraTalkDialogShow">
        <el-dialog
          custom-class="camera-loader"
          :visible.sync="cameraTalkDialogShow"
          :close-on-click-modal="false"
          :close-on-press-escape="false"
          @before-close="() => (cameraTalkDialogShow = false)"
        >
          <div class="camera-talk-content">
            <div class="talk-header">
              <div class="left-title">{{ videoName }}</div>
              <div class="right-icon" @click="() => (cameraTalkDialogShow = false)"></div>
            </div>
            <div class="talk-content">
              <rtspCavas ref="rtspCavas" :rtspUrl="videoUrl" :videoName="videoName" :hasCavas="Boolean(videoList.length)" class="videoflv"></rtspCavas>
            </div>
          </div>
        </el-dialog>
      </template>
      <AlarmDialog ref="alarmDialog" />
    </div>
  </PageContainer>
</template>
<script>
import { monitorTypeList } from '@/util/dict.js'
import mixin from './mixin/mixin.js'
export default {
  name: 'boilerMonitor',
  components: {
    graphicsMode: () => import('@/views/monitor/airMenu/components/graphicsMode')
  },
  mixins: [mixin],
  data() {
    return {
      loading: false,
      entityTabList: [], // 实体列表
      videoList: [], // 视频列表
      entityTabActive: '', // 选中的实体
      currentPattern: 1, // 1剖面图模式 2系统图模式
      searchFrom: {
        projectCode: monitorTypeList.find((item) => item.projectName == '锅炉监测').projectCode,
        entityMenuCode: '' // 菜单code
      },
      videoUrl: '',
      videoName: '',
      cameraTalkDialogShow: false
    }
  },
  computed: {},
  // 监听entityTabActive变化
  watch: {
    entityTabActive: {
      handler: function (val, oldVal) {
        this.searchFrom.entityMenuCode = val
        this.$nextTick(() => {
          if (this.currentPattern == 1) {
            this.$refs['scadaShow']?.getSurveyImgByCode()
            this.getVideoList()
          } else {
            this.$refs['scadaShow']?.getScadaList()
          }
        })
      },
      deep: true
    }
  },
  created() {},
  mounted() {
    this.getEntityTabList('1')
    // 监听滚动条滚动
    this.$refs['tabs'].addEventListener('DOMMouseScroll', this.handlerMouserScroll, false)
    this.$refs['tabs'].addEventListener('mousewheel', this.handlerMouserScroll, false)
  },
  beforeDestroy() {
    this.$refs['tabs'].removeEventListener('DOMMouseScroll', this.handlerMouserScroll)
    this.$refs['tabs'].removeEventListener('mousewheel', this.handlerMouserScroll)
  },
  methods: {
    // 滚动条滚动
    handlerMouserScroll(event) {
      let detail = event.wheelDelta || event.detail
      let moveForwardStep = -1
      let moveBackStep = 1
      let step = 0
      step = detail > 0 ? moveForwardStep * 50 : moveBackStep * 50
      this.$refs['tabs'].scrollBy({
        left: step
      })
    },
    // 切换选中实体
    changeActiveTab(code) {
      this.entityTabActive = code
      // this.getVideoList()
    },
    // 切换选中模式
    switchPattern(type) {
      if (this.currentPattern != type) {
        this.currentPattern = type
        this.getEntityTabList(type)
      }
    },
    // 获取实体列表
    getEntityTabList(type) {
      this.$api.getBoilerScale({ scaleType: type }).then((res) => {
        if (res.data.length) {
          if (type == 1) {
            const data = res.data.map((item) => {
              return {
                name: item.imsName,
                code: item.imsCode
              }
            })
            this.entityTabList = data
            this.entityTabActive = data[0].code
            this.getVideoList()
          } else {
            const data = res.data.map((item) => {
              return {
                name: item.iemName,
                code: item.iemCode
              }
            })
            this.entityTabList = data.length > 1 ? data : []
            this.entityTabActive = data[0].code
          }
          if (this.$route.query.surveyCode) {
            this.changeActiveTab(this.$route.query.surveyCode)
          }
        } else {
          this.entityTabList = []
        }
      })
    },
    // 根据选中实体获取视频列表
    getVideoList() {
      this.$api
        .getVideoList(
          {
            surveyCode: this.entityTabActive
          }
          // this.requestHttp
        )
        .then((res) => {
          if (res.code == 200) {
            this.videoList = res.data
          } else {
            this.videoList = []
          }
        })
    },
    // 打开视频弹窗
    openVideoDialog(item) {
      console.log(item)
      this.cameraTalkDialogShow = true
      const params = {
        cameraId: item.vidiconId
      }
      this.$api.getHlvAddress(params).then((res) => {
        if (res.code === '200') {
          this.videoName = item.vidiconName
          this.videoUrl = res.data
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.boilerMonitor-content {
  height: 100%;
  width: 100%;
  background: url('@/assets/images/monitor/boilerMonitor-scada-bg.png') no-repeat;
  background-size: 100% 100%;
  font-family: PingFang SC-Regular, PingFang SC;
  .content-title {
    width: 100%;
    height: 56px;
    display: flex;
    justify-content: space-between;
    flex-wrap: nowrap;
    align-items: center;
    padding: 0 24px;
    .entity-tabs {
      flex: 1;
      display: flex;
      overflow: hidden;
      .entity-tab-name {
        text-align: center;
        width: fit-content;
        white-space: nowrap;
        height: 30px;
        line-height: 30px;
        font-size: 16px;
        color: #99b1dd;
        background: url('@/assets/images/monitor/entity-tab-default.png') no-repeat;
        background-size: 100% 100%;
        cursor: pointer;
        padding: 0 14px;
        &:hover {
          background: url('@/assets/images/monitor/entity-tab-select.png') no-repeat;
          background-size: 100% 100%;
        }
      }
      .entity-tab-name-active {
        color: #ffffff;
        background: url('@/assets/images/monitor/entity-tab-select.png') no-repeat;
        background-size: 100% 100%;
      }
    }
    .modules-type {
      display: flex;
      padding-left: 20px;
      // margin: 0 !important;
      .pattern-item {
        cursor: pointer;
        font-size: 15px;
        color: #2181f4;
        .pattern-icon {
          font-size: 16px;
          margin-right: 6px;
        }
      }
      .pattern-item:last-child {
        margin-left: 16px;
      }
      .pattern-item-active {
        color: #f4db67;
      }
    }
  }
  .content-aside {
    width: 100%;
    height: calc(100% - 56px);
    position: relative;
    .video-list-box {
      position: absolute;
      z-index: 999;
      top: 24px;
      left: 24px;
      padding: 20px;
      width: 308px;
      background: linear-gradient(180deg, #101d29 0%, #081a2b 100%);
      border: 1px solid #3b4c5b;
      max-height: calc(100% - 100px);
      display: flex;
      flex-direction: column;
      .video-header {
        height: 30px;
        line-height: 30px;
        font-size: 16px;
        font-family: Alibaba PuHuiTi-Medium, Alibaba PuHuiTi;
        color: #b7cfff;
        padding-left: 39px;
        background: url('@/assets/images/monitor/boilerMonitor-video-title.png') no-repeat;
        background-size: 100% 100%;
        margin-bottom: 20px;
      }
      .video-list {
        height: calc(100% - 30px);
        overflow-y: auto;
        .video-list-li {
          height: 52px;
          background: linear-gradient(to left, #f4db67, #f4db67) left top no-repeat, linear-gradient(to bottom, #f4db67, #f4db67) left top no-repeat,
            linear-gradient(to left, #f4db67, #f4db67) right top no-repeat, linear-gradient(to bottom, #f4db67, #f4db67) right top no-repeat,
            linear-gradient(to left, #f4db67, #f4db67) left bottom no-repeat, linear-gradient(to bottom, #f4db67, #f4db67) left bottom no-repeat,
            linear-gradient(to left, #f4db67, #f4db67) right bottom no-repeat, linear-gradient(to left, #f4db67, #f4db67) right bottom no-repeat;
          background-size: 2px 8px, 8px 2px, 2px 8px, 8px 2px;
          background-color: #051634;
          padding: 0 20px;
          cursor: pointer;
          display: flex;
          align-items: center;
          img {
            height: 100%;
          }
          p {
            font-size: 16px;
            color: #a2b7d9;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin: 0;
          }
        }
        .video-list-li + .video-list-li {
          margin-top: 16px;
        }
        overflow-x: hidden;
        overflow-y: auto;
        overscroll-behavior: contain;
        // firefox隐藏滚动条
        scrollbar-width: none;
        // chrome隐藏滚动条
        &::-webkit-scrollbar {
          display: none;
        }
      }
      .no-video {
        text-align: center;
        font-size: 16px;
        color: #a2b7d9;
      }
    }
  }
}
::v-deep .camera-loader {
  width: 30%;
  height: 50%;
  background: url('~@/assets/images/elevator/page-bg.png') no-repeat center;
  background-size: 100% 100%;
  box-shadow: none;
  .el-dialog__body {
    height: 100%;
    width: 100%;
    padding: 0;
    .camera-talk-content {
      width: 100%;
      height: 100%;
      .talk-header {
        height: 30px;
        padding-left: 45px;
        padding-right: 10px;
        background: url('~@/assets/images/elevator/card-title-long-bg.png') no-repeat;
        background-size: 100% 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .left-title {
          font-size: 15px;
          font-family: Alibaba PuHuiTi-Medium, Alibaba PuHuiTi;
          font-weight: 500;
          color: #b7cfff;
        }
        .right-icon {
          width: 24px;
          height: 24px;
          background: url('~@/assets/images/elevator/dialog-close.png') no-repeat;
          background-size: 100% 100%;
          cursor: pointer;
        }
      }
      .talk-content {
        height: calc(100% - 30px);
        background: rgba(6, 18, 78, 0.5);
        border: 1px solid #3563a9;
        border-top: none;
        // padding: 24px;
        box-sizing: border-box;
      }
    }
  }
  .el-dialog__header,
  .el-dialog__footer {
    display: none;
  }
}
</style>
