// export js方法
import default_weather from '@/assets/images/lightingMonitoring/default_weather.png'
import default_weather_ctive from '@/assets/images/lightingMonitoring/default_weather_ctive.png'
import sunshine_weather from '@/assets/images/lightingMonitoring/sunshine_weather.png'
import sunshine_weather_active from '@/assets/images/lightingMonitoring/sunshine_weather_active.png'
import overcast_weather from '@/assets/images/lightingMonitoring/overcast_weather.png'
import overcast_weather_active from '@/assets/images/lightingMonitoring/overcast_weather_active.png'
import rainstorm_weather from '@/assets/images/lightingMonitoring/rainstorm_weather.png'
import rainstorm_weather_active from '@/assets/images/lightingMonitoring/rainstorm_weather_active.png'
import dustStorm_weather from '@/assets/images/lightingMonitoring/dustStorm_weather.png'
import dustStorm_weather_active from '@/assets/images/lightingMonitoring/dustStorm_weather_active.png'
export default {
  // 模式类型
  patternTypeList: [
    {
      dictId: '1',
      dictName: '晴'
    },
    {
      dictId: '2',
      dictName: '阴'
    },
    {
      dictId: '3',
      dictName: '暴雨'
    },
    {
      dictId: '4',
      dictName: '沙尘暴'
    }
  ],
  patternTypeIconList: [
    {
      patternCode: '0',
      patternName: '默认模式',
      patternSrc: default_weather,
      patternActiveSrc: default_weather_ctive
    },
    {
      patternCode: '1',
      patternName: '晴',
      patternSrc: sunshine_weather,
      patternActiveSrc: sunshine_weather_active
    },
    {
      patternCode: '2',
      patternName: '阴',
      patternSrc: overcast_weather,
      patternActiveSrc: overcast_weather_active
    },
    {
      patternCode: '3',
      patternName: '暴雨',
      patternSrc: rainstorm_weather,
      patternActiveSrc: rainstorm_weather_active
    },
    {
      patternCode: '4',
      patternName: '沙尘暴',
      patternSrc: dustStorm_weather,
      patternActiveSrc: dustStorm_weather_active
    }
  ]
}
