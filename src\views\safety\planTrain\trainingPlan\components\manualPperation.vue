<template>
  <div class="inner">
    <div class="top">
      <span>考题配置
        <span class="examSum">总共题数：{{ questionsScore.nums || 0 }}题 总分数：{{ questionsScore.sumScore || 0 }}分</span></span>
      <div class="operate">
        <span class="allIsExpand" @click="allFold">{{
          isAllFold ? "全部展开" : "全部折叠"
        }}</span>
        <div class="passScore">
          <el-button type="primary" @click="batchQuestions">批量录题</el-button>
          <el-button type="primary" @click="recordQuestions">逐题录题</el-button>
        </div>
      </div>
    </div>
    <div class="contener">
      <div v-for="(k, ind) in questionsList" :key="k.id" :name="k.id"
        :class="['exercisesItem', k.isExpand ? 'expand' : '']" :ref="'exercisesItem' + ind">
        <div class="exercisesTop">
          <div class="left">
            <div class="exercisesType">
              {{
                k.type == "1" ? "单选题" : k.type == "2" ? "多选题" : "判断题"
              }}
            </div>
            <span>({{ k.id }})</span>
          </div>
          <div class="right">
            <el-input type="number" placeholder="小题分数" min="1" style="width: 160px" v-model="k.score"
              @change="scoreChange(k, ind)">
              <template slot="append">分</template>
            </el-input>
            <i class="el-icon-upload2" @click="moveUp(ind)"></i>
            <i class="el-icon-download" @click="moveDown(ind)"></i>
            <i class="el-icon-delete" @click="deleteQuestions(ind)"></i>
            <div class="line"></div>
            <span @click="isExpandBtn(k, ind)">{{
              k.isExpand ? "折叠" : "展开"
            }}</span>
          </div>
        </div>
        <div :class="['exercisesName', k.isExpand ? '' : 'title']">
          {{ k.topic }}
        </div>
        <el-radio-group v-if="k.type == '1'" class="radioClass" v-model="k.answer" disabled>
          <el-radio v-for="(j, index) in k.options" :key="index" :label="j.id">{{ j.id }}. {{ j.label }}</el-radio>
        </el-radio-group>
        <el-checkbox-group v-if="k.type == '2'" v-model="k.answer" class="checkboxClass" disabled>
          <el-checkbox v-for="(j, index) in k.options" :key="index" :label="j.id">{{ j.id }}. {{ j.label
            }}</el-checkbox>
        </el-checkbox-group>
        <p>答案：{{ k | getAnswer }}</p>
        <p>
          解析：
          {{ k.analysis }}
        </p>
      </div>
    </div>
    <el-button class="footer" type="primary" @click="questionsBank">题库选择试题</el-button>
    <!-- 批量录题 -->
    <el-dialog class="changeStatusDialog" title="批量录题" :visible.sync="dialogVisible" width="50%"
      :before-close="handleClose">
      <el-form label-width="140px" :model="formInfo" :rules="rules" ref="formInfo" class="formInfo">
        <!-- <el-form-item label="所属科目" prop="subjectId">
            <el-cascader
              v-model="formInfo.subjectId"
              clearable
              class="sino_sdcp_input mr15"
              :options="treeData"
              :props="props"
              placeholder="请选择类型"
              @change="cascaderChange"
              style="width: 300px"
            ></el-cascader>
          </el-form-item>
          <el-form-item label="知识点所属课程" prop="courseCode">
            <el-select
              v-model="formInfo.courseCode"
              filterable
              placeholder="全部题型"
              style="width: 300px"
              @change="courseChange"
            >
              <el-option
                v-for="item in courseList"
                :key="item.id"
                :label="item.courseName"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item> -->
        <el-form-item label="">
          <el-upload ref="uploadFile2" drag multiple class="mterial_file file" action="string"
            :file-list="fileQuestions" :http-request="httpRequest" accept=".excel,.xlsx" :limit="1"
            :on-exceed="handleExceed" :before-upload="beforeAvatarUpload" :on-remove="handleRemove"
            :on-change="fileChange">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text" style="top: 100px">
              将文件拖到此处，或
              <em>点击上传</em>
            </div>
            <div slot="tip" class="el-upload__tip">支持上传单个Excel文件</div>
          </el-upload>
        </el-form-item>
        <span>下载模板文件：</span>
        <span v-for="(item, index) in exTypeList" :key="index" @click="downloadTemplate(item.id)" class="exType">{{
          item.label }}</span>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" plain @click="handleClose">取消</el-button>
        <el-button type="primary" @click="batchOk">确认</el-button>
      </span>
    </el-dialog>
    <!-- 逐道录题 -->
    <add-questions :drawerDialog="addDrawerDialog" :subjectId="subjectId" :type="planType" @planIsOk="planIsOk"
      @closeDrawer="closeAddDrawer"></add-questions>
    <!-- 添加试题 -->
    <test-questions ref="testQuestions" :drawerDialog="drawerDialog" :subjectId="subjectId"
      @testQuestions="testQuestions" @closeDrawer="closeDrawer"></test-questions>
  </div>
</template>
<script>
import axios from "axios";
import addQuestions from '../../../courseIndex/curriculumReform/components/addQuestions.vue'
import testQuestions from "../../../courseIndex/components/testQuestions.vue";
export default {
  props: {
    subjectId: {
      type: Number,
      default: null
    },
    tmId: {
      type: String,
      default: '',
    },
    // noAutoList:{
    //   type:Array,
    //   default:[]
    // },
    scoreInfo: {
      type: Object,
      default: {}
    }
  },
  components: {
    addQuestions,
    testQuestions
  },
  data() {
    return {
      topicList: [],
      checked: false,
      questionsList: [],
      dialogVisible: false,
      formInfo: {
        subjectId: '',
        courseCode: ''
      },
      courseList: [],
      fileQuestions: [],
      exTypeList: [
        {
          id: "1",
          label: "单选题",
        },
        {
          id: "2",
          label: "多选题",
        },
        {
          id: "3",
          label: "判断题",
        },
      ],
      courseList: [],
      treeData: [],
      props: {
        children: "childList",
        label: "name",
        value: "id",
        checkStrictly: true,
        emitPath: false,
      },
      defaultProps: {
        children: "childList",
        label: "name",
      },
      rules: {
        // subjectId: [
        //   { required: true, message: "请选择所属科目", trigger: "change" },
        // ],
        // courseCode: [
        //   { required: true, message: "请选择课程名称", trigger: "change" },
        // ],
      },
      routeInfo: {},
      addDrawerDialog: false,
      planType: 'plan',
      drawerDialog: false,
      questionsScore: {
        nums: 0,
        sumScore: 0
      },
      isAllFold: true,
    };
  },
  watch: {
    questionsList(val) {
      if (val.length) {
        this.questionsScore.sumScore = null
        val.forEach(item => {
          this.questionsScore.nums = val.length
          this.questionsScore.sumScore += item.score == '' ? 0 : parseInt(item.score)
        });
      }
    }
  },
  filters: {
    getAnswer(val) {
      if (val.type == '3') {
        return val.answer == '1' ? '正确' : '错误'
      } else {
        return val.answer
      }
    }
  },
  created() {
    this.routeInfo = JSON.parse(sessionStorage.getItem("routeInfo"));
    if (this.tmId) {
      this.getTrainDetail()
    }
  },
  methods: {
    getTrainDetail() {
      console.log(this.setingOpty, 'this.setingOpty123');
      this.$api.trainPlanDetal({ id: this.tmId }).then(res => {
        if (res.code == 200) {
          this.questionsList = res.data.examParamVo.noAutoQuestions
        }
      })
    },
    scoreChange() {
      this.questionsScore.sumScore = null
      this.questionsList.forEach((item) => {
        this.questionsScore.sumScore += item.score == '' ? 0 : parseInt(item.score)
      })
    },
    // 提交
    submit(type) {
      this.$emit('submitAuto', this.questionsList, this.questionsScore, type)
    },
    // 上移选择项
    moveUp(index) {
      if (index != 0) {
        let nums = this.questionsList;
        [nums[index], nums[index - 1]] = [nums[index - 1], nums[index]];
        this.questionsList = [...nums];
      } else {
        this.$message.error('禁止上移')
      }
    },
    //下移选择项
    moveDown(index) {
      if (this.questionsList.length && index != this.questionsList.length - 1) {
        let nums = this.questionsList;
        [nums[index], nums[index + 1]] = [nums[index + 1], nums[index]];
        this.questionsList = [...nums];
      } else {
        this.$message.error('禁止下移')
      }
    },
    deleteQuestions(index) {
      this.questionsList.splice(index, 1);
    },
    // 展开试题
    isExpandBtn(item, index) {
      item.isExpand = !item.isExpand
      this.$forceUpdate()
    },
    // 全部展开
    allFold() {
      this.questionsList.forEach(item => item.isExpand = !item.isExpand)
      this.isAllFold = !this.isAllFold
    },
    batchQuestions() {
      this.dialogVisible = true
    },
    handleClose() {
      this.dialogVisible = false
      this.fileQuestions = []
    },
    // 下载模板
    downloadTemplate(id) {
      axios({
        method: 'get',
        url: __PATH.BASE_URL_LABORATORY + 'question/template',
        params: { type: id },
        responseType: 'blob',
        headers: {
          "Content-Type": "application/json",
          token: this.routeInfo.token || ''
        }
      })
        .then((res) => {
          console.log(res, 'res');
          let name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
          let blob = new Blob([res.data]) // { type: "application/vnd.ms-excel" }
          let url = window.URL.createObjectURL(blob) // 创建一个临时的url指向blob对象
          // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
          let a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
          // 释放这个临时的对象url
          window.URL.revokeObjectURL(url)
        })
        .catch((res) => {
          this.$message.error('导出失败！')
        })
    },
    // 批量确定
    batchOk() {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          if (!this.fileQuestions.length) {
            return this.$message.error('请先选择模板')
          }
          let formData = new FormData()
          formData.append('hospitalCode', this.routeInfo.hospitalCode)
          formData.append('unitCode', this.routeInfo.unitCode)
          formData.append('subjectId', this.formInfo.subjectId)
          formData.append('systemCode', this.routeInfo.systemCode)
          formData.append('file', this.fileQuestions[0].raw)
          formData.append('courseId', this.formInfo.courseCode)
          formData.append('courseName', this.formInfo.courseName)
          formData.append('isDraft', false)
          formData.append('importType', '0')
          axios({
            method: 'post',
            url: __PATH.BASE_URL_LABORATORY + 'question/import',
            data: formData,
            headers: {
              "Content-Type": "application/x-www-form-urlencoded;charset=utf8",
              token: this.routeInfo.token
            }
          }).then(res => {
            if (res.data.code == '200') {
              this.getAllQuestions()
              this.handleClose()
            } else {
              this.$message.error(res.data.msg)
            }

          }).catch((res) => {
            this.$message.error('上传失败')
          })
        }
      });

    },
    // 查询批量录题列表
    getAllQuestions() {
      let formData = new FormData()
      formData.append('hospitalCode', this.routeInfo.hospitalCode)
      formData.append('unitCode', this.routeInfo.unitCode)
      formData.append('subjectId', this.formInfo.subjectId)
      formData.append('systemCode', this.routeInfo.systemCode)
      formData.append('file', this.fileQuestions[0].raw)
      formData.append('courseId', this.formInfo.courseCode)
      formData.append('courseName', this.formInfo.courseName)
      formData.append('isDraft', false)
      formData.append('importType', '1')
      axios({
        method: 'post',
        url: __PATH.BASE_URL_LABORATORYs + 'question/import',
        data: formData,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded;charset=utf8",
          token: this.routeInfo.token
        }
      }).then(res => {
        if (res.data.code == '200') {
          this.questionsList.push(...res.data.data)
        } else {
          this.$message.error(res.data.msg)
        }

      }).catch((res) => {
        this.$message.error('查询失败')
      })
    },
    fileChange(file, fileList, val) {
      this.fileQuestions = fileList
    },
    httpRequest() { },
    handleExceed() {
      this.$message.error("最多上传一个附件");
    },
    beforeAvatarUpload(file) {
      const isLt50M = file.size / 1024 / 1024 < 50;
      if (!isLt50M) {
        this.$message.error("上传图片大小不能超过 50MB!");
        return false;
      }
      if (file.name.indexOf(",") != -1) {
        this.$message.error("非法的文件名");
        return false;
      }
    },
    handleRemove(file, fileList, val) {
      this.fileQuestions = fileList;
    },
    closeAddDrawer() {
      this.addDrawerDialog = false
    },
    recordQuestions() {
      this.addDrawerDialog = true
    },
    // 逐道录题------------------------------------
    planIsOk(list) {
      list.forEach(k => {
        k.isExpand = false
        if (k.type == '2') {
          k.answer = k.answer.join(',')
        }
        k.options.forEach((i, ind) => {
          i.id = this.$tools.addLetter(ind)
        })
      })
      this.questionsList.push(...list)
      console.log(this.questionsList, 'this.questionsList');
    },
    // 题库添加----------------------
    testQuestions(list) {
      list.forEach(el => {
        if (el.type == 2) {
          el.answer = el.answer.join(',')
        }
      })
      this.questionsList.push(...list)
      console.log(this.questionsList, 'this.questionsList题库');
    },
    closeDrawer() {
      this.drawerDialog = false
    },
    questionsBank() {
      this.drawerDialog = true
      this.$refs.testQuestions.getTblleList();
    }
  },
};
</script>
<style lang="scss" scoped>
.inner {
  background-color: #fff;
  height: calc(100% - 200px);
  padding: 16px 22px;

  .top {
    .examSum {
      font-size: 14px;
      color: #7f848c;
    }
  }

  .operate {
    display: flex;
    justify-content: space-between;
    margin-top: 16px;

    .examSum {
      font-size: 14px;
      color: #7f848c;
    }

    .passScore {
      font-size: 14px;
    }

    .allIsExpand {
      font-size: 14px;
      color: #3562db;
      cursor: pointer;
    }
  }

  .contener {
    background-color: #faf9fc;
    height: calc(100% - 80px);
    overflow: auto;
    padding: 16px;

    // margin-bottom: 16px;
    .exercisesItem {
      height: 100px;
      overflow: hidden;
      background-color: #fff;
      margin-bottom: 16px;
      padding: 16px;
      border-bottom: 4px solid #faf9fc;
      font-size: 14px;

      .exercisesTop {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;

        .left {
          display: flex;
          align-items: center;
          justify-content: center;

          span {
            color: #7f848c;
          }
        }

        .right {
          color: #ccced3;
          display: flex;
          align-items: center;

          .line {
            width: 2px;
            height: 14px;
            margin: 0 10px 0 26px;
            background-color: #dcdfe6;
          }

          span {
            color: #3562db;
            margin-left: 16px;
            cursor: pointer;
          }

          i {
            color: #3562db;
            cursor: pointer;
            margin-left: 16px;
          }
        }

        .exercisesType {
          width: 58px;
          height: 22px;
          line-height: 22px;
          text-align: center;
          border-radius: 4px;
          color: #86909c;
          background-color: #ededf5;
          margin: 0 10px;
        }
      }

      .exercisesName {
        line-height: 20px;
        margin-bottom: 16px;
      }

      .title {
        overflow: hidden;
        -webkit-line-clamp: 2;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
      }

      .el-radio {
        margin-left: 38px;
        font-size: 14px;
        color: #7f848c !important;
        line-height: 30px;
      }

      p {
        font-size: 14px;
        color: #7f848c !important;
        line-height: 20px;
        margin-bottom: 16px;
      }
    }

    .expand {
      height: auto;
    }
  }
}

::v-deep .radioClass .el-radio {
  display: block;
  margin: 10px 0;
}

::v-deep .checkbox .el-checkbox {
  display: block;
  margin: 10px 0;
}

::v-deep .el-checkbox-group {
  margin-left: 38px;
}

.changeStatusDialog {
  .exType {
    color: #3562db;
    margin: 16px 16px 0 0;
    cursor: pointer;
  }
}
</style>
