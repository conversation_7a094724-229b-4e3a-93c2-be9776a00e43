<template>
  <div class="tab_content">
    <div class="right-heade">
      <el-input v-model="formData.archiveInfo" placeholder="编号/名称/备注" suffix-icon="el-icon-search" clearable />
      <el-cascader
        v-model="formData.shareDeptId"
        placeholder="签订部门"
        :options="deptList"
        :props="{
          value: 'id',
          label: 'deptName',
          checkStrictly: true,
          emitPath: false
        }"
        clearable
        filterable
        size="small"
      ></el-cascader>
      <el-date-picker
        v-model="formData.datetimerange"
        value-format="timestamp"
        type="datetimerange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="handleDateChange"
      >
      </el-date-picker>
      <el-select v-model="formData.contractState">
        <el-option v-for="item in optionList" :key="item.value" placeholder="状态" :label="item.label" :value="item.value"></el-option>
      </el-select>
      <div>
        <el-button type="primary" @click="search">查询</el-button>
        <el-button type="primary" plain @click="reset">重置</el-button>
      </div>
    </div>
    <div class="right-content">
      <div class="table-content">
        <TablePage
          ref="tablePage"
          v-loading="tableLoading"
          v-scrollHideTooltip
          :tableColumn="tableColumn"
          :data="tableData"
          border
          height="100%"
          :showPage="true"
          :pageData="pageData"
          :pageProps="{
            page: 'current',
            pageSize: 'size',
            total: 'total'
          }"
          @pagination="paginationChange"
        >
        </TablePage>
      </div>
    </div>
    <ViewAttachments :id="currentId" :visible.sync="visible" :isShare="true" />
  </div>
</template>
<script lang="jsx">
import tableListMixin from '@/mixins/tableListMixin.js'
import ViewAttachments from '@/views/operationPort/dossierManager/components/ViewAttachments.vue'
import { transData } from '@/util'
import { status } from '../json/index.js'
import moment from 'moment'
import dictMixin from '../mixins/index.js'
export default {
  components: { ViewAttachments },
  mixins: [tableListMixin, dictMixin],
  data() {
    return {
      optionList: status,
      moment,
      tableColumn: [
        {
          prop: 'archiveName',
          label: '合同名称',
          align: 'center'
        },
        {
          prop: 'archiveNumber',
          label: '合同编号',
          align: 'center'
        },
        {
          prop: 'departmentName',
          label: '签订部门',
          align: 'center'
        },
        {
          prop: 'archiveModel',
          label: '分类',
          align: 'center',
          render: (h, row) => {
            const item = this.contractClassification.find((item) => item.value === row.row.archiveModel)
            return item ? item.label : ''
          }
        },
        {
          prop: 'category',
          label: '类别',
          align: 'center',
          render: (h, row) => {
            const item = this.categoryList.find((item) => item.value === row.row.category)
            return item ? item.label : ''
          }
        },
        {
          prop: 'archiveDate',
          label: '签订日期',
          align: 'center',
          render: (h, row) => {
            return row.row.archiveDate ? this.moment(row.row.archiveDate).format('YYYY-MM-DD') : ''
          }
        },
        {
          prop: 'startDate',
          label: '生效日期',
          align: 'center',
          render: (h, row) => {
            return row.row.startDate ? this.moment(row.row.startDate).format('YYYY-MM-DD') : ''
          }
        },
        {
          prop: 'endDate',
          label: '结束日期',
          align: 'center',
          render: (h, row) => {
            return row.row.endDate ? this.moment(row.row.endDate).format('YYYY-MM-DD') : ''
          }
        },
        {
          prop: 'contractState',
          label: '状态',
          align: 'center',
          render: (h, row) => {
            return ['未开始', '履约中', '已结束'][row.row.contractState]
          }
        },
        {
          prop: 'shareEndDate',
          label: '借阅结束日期',
          align: 'center',
          render: (h, row) => {
            return row.row.shareEndDate ? this.moment(row.row.shareEndDate).format('YYYY-MM-DD') : ''
          }
        },
        {
          prop: 'handler',
          label: '操作',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span class="operationBtn-span" style="color: #3562DB" onClick={() => this.handleViewAtta(row.row)}>
                  查看附件
                </span>
                <span class="operationBtn-span" style="color: #3562DB" onClick={() => this.handleGotoDetail(row.row)}>
                  详情
                </span>
              </div>
            )
          }
        }
      ],
      formData: {
        startTime: null,
        endTime: null,
        datetimerange: [],
        archiveInfo: '',
        shareDeptId: ''
      },
      tableLoading: false,
      tableData: [],
      pageData: {
        current: 1,
        size: 15,
        total: 0
      },
      currentId: '',
      visible: false,
      deptList: []
    }
  },
  created() {
    this.handleQueryTablelist()
    this.getDeptList()
  },
  methods: {
    // 获取部门列表
    getDeptList() {
      this.$api.getSelectedDept().then((res) => {
        if (res.code == '200') {
          this.deptList = transData(res.data, 'id', 'pid', 'children')
        }
      })
    },
    handleDateChange(e) {
      this.formData.startTime = e && e[0]
      this.formData.endTime = e && e[1]
    },
    handleGotoDetail(row) {
      this.$router.push(`/dossierManager/myContractDetails?id=${row.archiveId}&notShowEdit=true`)
    },
    handleViewAtta(row) {
      this.currentId = row.archiveShareId
      this.visible = true
    },
    handleQueryTablelist() {
      const parmas = {
        ...this.formData,
        ...this.pageData,
        archiveType: '0'
      }
      delete parmas.datetimerange
      this.$api.fileManagement.shareListByPage(parmas).then((res) => {
        this.tableData = res.data.records
        this.pageData.total = res.data.total
      })
    },
    // 分页事件
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getTableList()
    },
    search() {
      this.pageData.current = 1
      this.handleQueryTablelist()
    },
    reset() {
      Object.keys(this.formData).forEach((key) => {
        this.formData[key] = ''
        if (key === 'datetimerange') {
          this.formData[key] = []
        }
        if (['startTime', 'endTime'].includes(key)) {
          this.formData[key] = null
        }
      })
      this.search()
    }
  }
}
</script>
<style lang="scss" scoped>
.tab_content {
  width: 100%;
  height: calc(100% - 40px);
  .right-heade {
    display: flex;
    border-radius: 4px;
    ::v-deep .el-input {
      width: 200px;
    }
    & > div {
      margin-right: 10px;
      margin-top: 10px;
    }
  }
  .right-content {
    background: #fff;
    border-radius: 4px;
    overflow: hidden;
    width: 100%;
    height: calc(100% - 50px);
    margin-top: 16px;
    .btns-group {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
      & > div {
        display: flex;
      }
      .btns-group-control {
        > div {
          margin-left: 10px;
        }
        // & > div, & > button {
        //   margin-right: 10px;
        // }
      }
    }
    .table-content {
      height: calc(100% - 45px);
      ::v-deep .el-table .el-table__body-wrapper td.el-table__cell {
        border-right: 1px solid #ededf5;
        .tooltip-over-td {
          display: block;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
}
</style>
