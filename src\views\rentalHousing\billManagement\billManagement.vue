<template>
  <div style="height: 100%">
    <div class="special_box">
      <div class="content_box">
        <div class="tabsMenu">
          <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="账单列表" name="billList"></el-tab-pane>
            <el-tab-pane label="公租房账单表" name="roomBillList"></el-tab-pane>
          </el-tabs>
        </div>
        <div class="component-div">
          <component :is="activeName" ref="contentRef" style="padding: 0 16px 0 0" large></component>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import billList from './billList'
import roomBillList from './roomBillList'
export default {
  name: 'punishmentSystemList',
  data() {
    return {
      activeName: 'billList'
    }
  },
  components: { billList, roomBillList },
  computed: {},
  created() {},
  mounted() {},
  activated() {},
  methods: {
    handleClick() {}
  }
}
</script>
<style lang="scss" scoped>
.tabsItem {
  padding: 8px 0;
  width: 200px;
  margin: 0 auto;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #5b5b5b;
  cursor: pointer;

  .item {
    width: 200px !important;
  }

  > span {
    width: 200px !important;
    margin: 0 auto !important;
  }
}
.tabsItem:hover {
  color: #5188fc;
}
:deep(.el-tabs--left) {
  width: 200px;
  margin: 20px auto 0;
}
:deep(.el-table__row) {
  :last-child > .cell {
    display: flex;
    justify-content: space-between;
  }
}
.special_box {
  height: 100%;
  display: flex;
  .content_box {
    width: calc(100% - 30px);
    margin: 15px;
    border-radius: 10px;
    padding: 20px 25px 25px;
    background: #fff;
    display: flex;
    flex-direction: column;

    .tabsMenu {
      margin-bottom: 15px;

      :deep(.el-tabs__item) {
        width: 100px;
      }
    }
    .component-div {
      height: calc(100% - 40px);
    }

    .table_list {
      .disable {
        color: #414653;

        span {
          display: inline-block;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: #ccced3;
        }
      }

      .enable {
        color: #08cb83;

        span {
          display: inline-block;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: #08cb83;
        }
      }
      .acceptanceText {
        cursor: pointer;
      }
      .acceptanceColor0 {
        color: #414653;
      }
      .acceptanceColor1 {
        color: #08cb83;
      }
      .acceptanceColor2 {
        color: #f56c6c;
      }
    }
  }
}
:deep(.el-tabs__nav) {
  width: 100%;
  .el-tabs__item {
    padding: 0 10px;
    width: 50%;
    text-align: center;
  }
}
::v-deep .el-icon-time {
  position: relative;
}
::v-deep .el-icon-time::before {
  position: absolute;
  top: -3px;
  left: 4px;
}
</style>
<style lang="scss">
.messageIndex {
  z-index: 3000 !important;
}
.recordContent {
  .recordWrap {
    display: flex;
    margin: 5px 0;
    .recordTitle {
      width: 70px;
      text-align: right;
      display: block;
    }
    .textRecord {
      width: calc(100% - 70px);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .recordWrap:nth-child(n + 5) {
    text-align: right;
  }
}
</style>
