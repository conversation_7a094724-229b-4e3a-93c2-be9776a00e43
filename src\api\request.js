/*
 * @Author: hedd
 * @Date: 2023-03-23 10:11:51
 * @LastEditTime: 2025-06-11 16:47:13
 * @FilePath: \ihcrs_pc\src\api\request.js
 * @Description:
 */
import axios from 'axios'
import router from '@/router/index'
import store from '@/store/index'
import storage from '@/util/storage'
import { Message } from 'element-ui'
import { generateSignature } from './signature.js'
let source = axios.CancelToken.source()
const toLogin = () => {
  store.dispatch('user/logout').then((res) => {
    router.push({
      path: '/login',
      query: {
        // redirect: router.currentRoute.fullPath
      }
    })
  })
}
const api = axios.create({
  // 超时时间 单位是ms，这里设置了50s的超时时间
  timeout: 5 * 10000,
  // responseType: 'json'
  withCredentials: false // 禁止携带cookie
})
api.interceptors.request.use((request) => {
  let menuList = store.state.menu.routes
  let firstTitle = ''
  let routeList = []
  router.currentRoute.matched
    .filter((item) => item.name)
    .forEach((el) => {
      routeList.push(el.meta.title)
    })
  if (menuList.length) {
    firstTitle = menuList[store.state.menu.headerActived].meta.title
    routeList.unshift(firstTitle)
    request.headers['operation-content'] = encodeURIComponent(routeList.join(','))
  }
  if (request.headers['operation-name']) {
    request.headers['operation-name'] = encodeURIComponent(request.headers['operation-name'])
  }
  if (store.getters['user/isLogin']) {
    if (!request.headers.Authorization) {
      request.headers.Authorization = 'Bearer ' + store.state.user.token
    }
  }
  // 配合this.$tools.cancelAjax()使用，取消请求
  request.cancelToken = new axios.CancelToken((cancel) => {
    if (!window.axiosCancel) window.axiosCancel = []
    window.axiosCancel.push({ cancel })
  })
  const signatureData = generateSignature(request)
  if (Object.keys(signatureData).length && __PATH.OPEN_SIGNATURE) {
    const { signature, timestamp, nonce } = signatureData
    request.headers['X-Timestamp'] = timestamp
    request.headers['X-Nonce'] = nonce
    request.headers['X-Signature'] = signature
  }
  return request
})
api.interceptors.response.use(
  (response) => {
    // 如果接口请求时发现 Authorization 失效，则立马跳转到登录页
    if (response.data.code == 401 && store.getters['user/isLogin']) {
      store.commit('user/removeUserData')
      toLogin()
      Message.error(response.data.msg)
      return Promise.reject(response.data)
    }
    if (
      ['application/vnd.ms-excel;charset=utf-8', 'application/vnd.ms-excel', 'application/octet-stream;charset=utf-8', 'application/zip'].includes(response.headers['content-type'])
    ) {
      return Promise.resolve(response)
    } else {
      return Promise.resolve(response.data)
    }
  },
  (error) => {
    console.log(error)
    if (error.message === 'Network Error') {
      return Promise.reject({ message: '网络请求失败，请重试' })
    }
    if (error.response && error.response.data.message) {
      Message.error(error.response.data.message)
    }
    return Promise.reject(error)
  }
)
export default api
