<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 150 150"><defs><style>.cls-1{fill:url(#未命名的渐变_105);}.cls-2{stroke:#fff;stroke-miterlimit:10;fill:url(#未命名的渐变_62);}</style><linearGradient id="未命名的渐变_105" y1="75.22" x2="150.44" y2="75.22" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#e2e2e2"/><stop offset="0.03" stop-color="#d0d0d0"/><stop offset="0.06" stop-color="#bcbcbc"/><stop offset="0.09" stop-color="#b5b5b5"/><stop offset="0.49" stop-color="#f6f6f6"/><stop offset="0.63" stop-color="#f3f3f3"/><stop offset="0.73" stop-color="#eaeaea"/><stop offset="0.82" stop-color="#dbdbdb"/><stop offset="0.9" stop-color="#c6c6c6"/><stop offset="0.97" stop-color="#aaa"/><stop offset="1" stop-color="#9b9b9b"/></linearGradient><radialGradient id="未命名的渐变_62" cx="75.22" cy="75.22" r="61.11" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#f6f6f6"/><stop offset="0.19" stop-color="#ededed"/><stop offset="0.5" stop-color="#d4d4d4"/><stop offset="0.89" stop-color="#ababab"/><stop offset="1" stop-color="#9e9e9e"/></radialGradient></defs><title>iot-按钮</title><g id="图层_13" data-name="图层 13"><circle class="cls-1" cx="75.22" cy="75.22" r="75.22"/><circle class="cls-2" cx="75.22" cy="75.22" r="61.11"/></g></svg>