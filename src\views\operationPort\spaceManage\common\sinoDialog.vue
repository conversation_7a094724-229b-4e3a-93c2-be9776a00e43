<template>
  <el-dialog
    :class="customStyle ? 'sino_dialog sino_dialog_custom' : 'sino_dialog'"
    :title="title"
    :visible.sync="dialogTableVisible"
    custom-class="model-dialog"
    @close="closeDialog"
  >
    <div class="sino_dialog_content" style="background: #fff">
      <slot></slot>
    </div>
    <template v-if="shwoFooter == 'sureAndCancel'">
      <span slot="footer" class="dialog_footer">
        <el-button type="primary" plain @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="sureDialog">确 定</el-button>
      </span>
    </template>
    <template v-else-if="shwoFooter == 'cancel'">
      <span slot="footer" class="dialog_footer">
        <el-button type="primary" plain @click="dialogTableVisible = false">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: '标题'
    },
    shwoFooter: {
      type: String,
      default: 'sureAndCancel'
    },
    customStyle: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogTableVisible: false
    }
  },

  mounted() {},
  methods: {
    sureDialog() {
      this.$emit('sureDialog')
      // this.dialogTableVisible = false
    },
    closeDialog() {
      this.$emit('closeDialog')
      this.dialogTableVisible = false
    },
    handleClose(done) {
      this.$emit('closeDialog')
      done()
    }
  }
}
</script>

<style lang="scss" scoped>
.sino_dialog_content {
  height: 100%;
  width: 100%;
  min-height: 10vh;
  overflow: hidden;
  box-sizing: border-box;
}

.sino_dialog_footer {
  border-radius: 0 0 10px 10px;
  width: 100%;
  height: 60px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
</style>
