<!-- 电力数据 -->
<template>
  <PageContainer>
    <div slot="header" class="pow_header">
      <div class="title">
        <div class="title_p">电力数据</div>
      </div>
      <div class="tabs">
        <el-tabs v-model="queryParams.menuCode" @tab-click="selectEntityGroup()">
          <el-tab-pane v-for="item in tabs" :key="item.code" :label="item.name" :name="item.code"> </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <div slot="content" class="pow_content">
      <div class="content_left">
        <div class="all_tree">
          <el-tabs v-model="isKeyDevice">
            <el-tab-pane label="配电设备" name="1"> </el-tab-pane>
            <el-tab-pane label="重点设备" name="0"> </el-tab-pane>
          </el-tabs>
          <el-input v-model="filterText" suffix-icon="el-icon-search" style="margin-bottom: 16px" placeholder="输入关键字进行过滤"> </el-input>
          <div class="my_tree">
            <el-tree
              ref="tree"
              class="filter-tree"
              :data="treeData"
              highlight-current
              node-key="code"
              :props="{
                children: 'children',
                label: 'name'
              }"
              :render-content="renderContent"
              default-expand-all
              :filter-node-method="filterNode"
              @node-click="nodeClick"
            >
              <!-- <span slot-scope="{ node }" class="custom-tree-node">
                <el-tooltip class="item" effect="dark" :content="node.label" placement="top-start">
                  <span>{{ node.label }}</span>
                </el-tooltip>
              </span> -->
            </el-tree>
          </div>
        </div>
      </div>
      <div class="content_right">
        <div class="typeTabs">
          <el-tabs v-model="choiceTypeTab">
            <el-tab-pane v-for="item in typeTabs" :key="item.type" :label="item.name" :name="item.type">
              <div class="query_row">
                <div class="query_left">
                  <div v-if="item.dateTypeArr.length > 1">
                    <div
                      v-for="(v, i) in item.dateTypeArr"
                      :key="i"
                      :class="{
                        'search-aside-item': true,
                        'search-aside-item-active': selectedTimeType.dateType === v.dateType
                      }"
                      @click="onDateType(v)"
                    >
                      {{ v.name }}
                    </div>
                  </div>
                  <!-- 日选择 -->
                  <div v-if="selectedTimeType.dateType == 'day'">
                    <el-date-picker
                      key="day"
                      v-model="dateTime.day"
                      type="date"
                      placeholder="请选择日期"
                      :picker-options="pickerOptions"
                      :clearable="false"
                      value-format="yyyy-MM-dd"
                    >
                    </el-date-picker>
                  </div>
                  <!-- 月选择 -->
                  <div v-else-if="selectedTimeType.dateType == 'month'">
                    <el-date-picker
                      key="month"
                      v-model="dateTime.month"
                      type="month"
                      placeholder="请选择日期"
                      :picker-options="pickerOptions"
                      :clearable="false"
                      value-format="yyyy-MM-dd"
                    >
                    </el-date-picker>
                  </div>
                  <!-- 年选择 -->
                  <div v-else-if="selectedTimeType.dateType == 'year'">
                    <el-date-picker
                      key="year"
                      v-model="dateTime.year"
                      type="year"
                      placeholder="请选择日期"
                      :picker-options="pickerOptions"
                      :clearable="false"
                      value-format="yyyy-MM-dd"
                    >
                    </el-date-picker>
                  </div>
                  <!-- 自定义时间范围选择 -->
                  <div v-else>
                    <el-date-picker
                      key="daterange"
                      v-model="dateTime.custom"
                      type="daterange"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      :picker-options="pickerOptions"
                      :clearable="false"
                      value-format="yyyy-MM-dd"
                    >
                    </el-date-picker>
                  </div>
                  <div v-if="selectedTimeType.dateType != 'custom'" class="up_down">
                    <span class="jump_date" @click="jumpDate('subtract')">上一{{ selectedTimeType.unit }}</span>
                    <span class="jump_date" :class="{ no_clicl: isNext }" @click="jumpDate('add')">下一{{ selectedTimeType.unit }}</span>
                  </div>
                  <el-button type="primary" plain class="re_btn" @click="reset">重置</el-button>
                  <el-button type="primary" @click="submit">查询</el-button>
                </div>
                <div v-if="choiceTypeTab != 2" class="query_right">
                  <div class="pattern-item" @click="handleClick('0')">
                    <svg-icon :name="activeTab == '0' ? 'chartModeActive' : 'chartMode'" class="pattern-icon" />
                    <span :style="{ color: activeTab == '0' ? '#3562DB' : '#414653' }">图形模式</span>
                  </div>
                  <div class="pattern-item" @click="handleClick('1')">
                    <svg-icon :name="activeTab == '1' ? 'listModeActive' : 'listMode'" class="pattern-icon" />
                    <span :style="{ color: activeTab == '1' ? '#3562DB' : '#414653' }">列表模式</span>
                  </div>
                </div>
              </div>
              <div v-if="item.typeArr" class="btn_row">
                <div
                  v-for="(v, i) in item.typeArr"
                  :key="i"
                  :class="{
                    'search-aside-item': true,
                    'search-aside-item-active': queryParams.selectedSonType === v.paramId
                  }"
                  @click="onTypeQuick(v)"
                >
                  {{ v.paramName }}
                </div>
                <div v-if="choiceTypeTab == 3" class="harmonicOrder">
                  <span>谐波阶数</span>
                  <el-select v-model="harmonicOrder" multiple collapse-tags style="margin: 0 8px" value-key="paramName" placeholder="请选择" @change="getList">
                    <el-option v-for="item in harmonicOrderArr" :key="item.paramName" :label="item.paramName" :value="item"> </el-option>
                  </el-select>
                </div>
              </div>
              <div v-if="choiceTypeTab == 3" class="choice_row">
                <el-radio-group v-model="harmonicRadio" @change="getList">
                  <el-radio
                    v-for="item in queryParams.selectedSonType == 1 ? harmonicVoltageArr : harmonicCurrentArr"
                    :key="item.paramId"
                    :value="item.paramId"
                    :label="item.paramId"
                  >
                    {{ item.paramName }}
                  </el-radio>
                </el-radio-group>
              </div>
              <div v-if="choiceTypeTab == 0" class="choice_row">
                <el-checkbox-group v-model="checkList" :min="1" @change="getList">
                  <el-checkbox v-for="item in mutuallyArr" :key="item.paramId" :value="item.paramId" :label="item.paramId">
                    {{ item.paramName }}
                  </el-checkbox>
                </el-checkbox-group>
              </div>
              <div v-if="choiceTypeTab == 1" class="choice_row">
                <el-radio-group v-model="radio" @change="getList">
                  <el-radio v-for="item in mutuallyArr" :key="item.paramId" :value="item.paramId" :label="item.paramId">
                    {{ item.paramName }}
                  </el-radio>
                </el-radio-group>
              </div>
              <div v-if="activeTab == 1 || choiceTypeTab == 2" class="my_tabel">
                <TablePage id="my-table" ref="table" v-loading="tabelLoading" :showPage="false" :tableColumn="tableColumn" :data="tableData" height="100%" />
              </div>
              <div v-else-if="activeTab == 0 && choiceTypeTab != 2" class="my_tabel">
                <echarts :ref="`powerECharts${item.type}`" :domId="`powerECharts${item.type}`" width="100%" height="100%" />
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>
  </PageContainer>
</template>

<script lang="jsx">
import power from './mixin/power.js'
import moment from 'moment'
export default {
  name: 'power',
  mixins: [power],
  data() {
    return {
      isKeyDevice: '1', // 是否早重点设备
      deviceName: '',
      paramName: '',
      tableColumn: [],
      tableData: [],
      tabelLoading: false,
      // 谐波监测  单选
      harmonicRadio: null,
      // 电压单选数据
      harmonicVoltageArr: [
        {
          paramName: 'A相电压',
          paramId: '0'
        },
        {
          paramName: 'B相电压',
          paramId: '1'
        },
        {
          paramName: 'C相电压',
          paramId: '2'
        }
      ],
      // 电流单选数据
      harmonicCurrentArr: [
        {
          paramName: 'A相电流',
          paramId: '0'
        },
        {
          paramName: 'B相电流',
          paramId: '1'
        },
        {
          paramName: 'C相电流',
          paramId: '2'
        }
      ],
      // 原始数据  多选
      checkList: [],
      // 极值数据  多选
      radio: null,
      mutuallyArr: [],
      harmonicOrderArr: [],
      harmonicOrder: [],
      activeTab: '0', // 图形列表切换   0 图形  1 列表
      queryParams: {
        queryModel: null, //  时间类型
        endTime: null,
        menuCode: null, //  顶部菜单code
        startTime: null,
        monitorEntityCode: '', //  左侧实体code
        selectedSonType: null //  选择的类型
      },
      dateTime: {
        day: null,
        month: null,
        year: null,
        custom: []
      }, // 查询的日期时间
      selectedTimeType: {},
      choiceTypeTab: null,
      tabs: [],
      filterText: '', //  分组input搜索
      treeData: [],
      pickerMinDate: '',
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          this.pickerMinDate = minDate.getTime()
          if (maxDate) {
            this.pickerMinDate = ''
          }
        },
        disabledDate: (time) => {
          if (this.pickerMinDate !== '' && (this.choiceTypeTab == 0 || this.choiceTypeTab == 1)) {
            // 原始数据范围不能大于3天   极值数据不得大于30天
            const dayNum = this.choiceTypeTab == 0 ? 2 * 24 * 3600 * 1000 : 29 * 24 * 3600 * 1000
            let maxTime = this.pickerMinDate + dayNum
            let minTime = this.pickerMinDate - dayNum
            if (maxTime > new Date()) {
              maxTime = new Date()
            }
            return time.getTime() > maxTime || time.getTime() < minTime || time.getTime() > Date.now()
          }
          return time.getTime() > Date.now()
        }
      },
      isShowTooltip: false
    }
  },
  computed: {
    isNext() {
      if (this.selectedTimeType.dateType == 'day' && this.dateTime.day) {
        return moment(this.dateTime.day).add(1, 'days').valueOf() > Date.now()
      } else if (this.selectedTimeType.dateType == 'month' && this.dateTime.month) {
        return moment(this.dateTime.month).add(1, 'month').valueOf() > Date.now()
      } else if (this.selectedTimeType.dateType == 'year' && this.dateTime.year) {
        return moment(this.dateTime.year).add(1, 'year').valueOf() > Date.now()
      } else {
        return false
      }
    }
  },
  watch: {
    choiceTypeTab: function (val) {
      let obj = this.typeTabs.find((ele) => ele.type == val)
      this.onDateType(obj.dateTypeArr[0])
      this.onTypeQuick(obj.typeArr[0])
      // if (obj.typeArr.length) {
      //   this.queryParams.paramIds = obj.typeArr[0].status
      //   this.queryParams.paramNames = obj.typeArr[0].paramNames
      // }
    },
    filterText: function (val) {
      this.$refs.tree.filter(val)
    },
    isKeyDevice(val) {
      this.selectEntityGroup()
    }
  },
  mounted() {
    this.choiceTypeTab = this.typeTabs[0].type
    this.toTheRightOfTime()
    this.GetEntityMenuList()
  },
  methods: {
    renderContent(h, { node, data, store }) {
      const notTeamNodeRole = data.notTeamNodeRole?.split(',') ?? []
      // const nowProjectName = monitorTypeList.find(item => item.projectCode == this.projectCode).projectName
      // 空调监测为非树形结构
      return (
        <span class="custom-tree-node">
          <el-tooltip class="item" effect="dark" content={node.label} placement="top-start" disabled={!this.isShowTooltip}>
            <span
              onMouseenter={(e) => {
                this.visibilityChange(e)
              }}
            >
              {node.label}
            </span>
          </el-tooltip>
        </span>
      )
    },
    visibilityChange(e) {
      const item = e.target
      const itemWidth = item.offsetWidth
      const itemScrollWidth = item.scrollWidth
      if (itemWidth < itemScrollWidth) {
        this.isShowTooltip = true
      } else {
        this.isShowTooltip = false
      }
    },
    queryParamList(paramId) {
      this.$api.queryParamList({ paramId }).then((res) => {
        if (res.code == 200) {
          this.harmonicRadio = '0'
          if (res.data && res.data.length) {
            this.harmonicOrder = [res.data[0]]
            this.harmonicOrderArr = res.data
          }
          this.getList()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 原始数据
    handleOriginalDataSurface(data) {
      // 表头
      this.tableColumn = [
        {
          label: '设备名称',
          prop: 'deviceName',
          width: 160
        },
        {
          label: '日期',
          prop: 'date',
          width: 160
        },
        ...this.checkList.map((ele) => {
          let item = this.mutuallyArr.find((v) => v.paramId == ele)
          return {
            label: `${item.paramName}${item.unitName ? '(' + item.unitName + ')' : ''}`,
            prop: `value${ele}`
          }
        })
      ]
      // 表数据
      let dateObj = {}
      data.forEach((ele) => {
        let key = ele.date
        if (!dateObj[key]) {
          dateObj[key] = {
            date: key,
            deviceName: this.deviceName
          }
        }
        dateObj[key]['value' + ele.paramId] = ele.paramValue
      })
      this.tableData = Object.values(dateObj)
      console.log('this.tableData===========', this.tableData)
    },
    handleOriginalDataSurfaceEcharts(data) {
      let startTime = moment(this.queryParams.startTime).format('YYYY-MM-DD') + ' 00:00:00'
      let endTime = moment(this.queryParams.endTime).format('YYYY-MM-DD') + ' 24:00:00'
      let dateObj = {}
      data.forEach((ele) => {
        let key = ele.date
        if (!dateObj[key]) {
          dateObj[key] = {
            date: key,
            deviceName: this.deviceName,
            list: []
          }
        }
        dateObj[key].list.push({
          name: ele.paramName,
          value: ele.paramValue
        })
      })
      let newData = Object.values(dateObj)
      // 通过时间进行排序
      let sortData = newData.sort((a, b) => {
        var dateTimeA = new Date(a.date)
        var dateTimeB = new Date(b.date)
        if (dateTimeA < dateTimeB) {
          return -1
        } else if (dateTimeA > dateTimeB) {
          return 1
        } else {
          return 0
        }
      })
      console.log('Object.values(dateObj)===========', sortData)
      let title = `${this.deviceName} ${this.queryParams.startTime}至${this.queryParams.endTime} ${this.paramName}`
      this.$refs['powerECharts' + this.choiceTypeTab][0].init(this.setLineChart(sortData, title, startTime, endTime))
      this.$refs['powerECharts' + this.choiceTypeTab][0].chartResize()
    },
    // 极值数据
    handleExtremumDataSurface(data) {
      // 类型对象
      let item = this.mutuallyArr.find((ele) => this.radio == ele.paramId)
      // 表头
      this.tableColumn = [
        {
          label: '设备名称',
          prop: 'deviceName',
          width: 160
        },
        {
          label: '日期',
          prop: 'date',
          width: 160
        },
        {
          label: `${item.paramName}${item.unitName ? '(' + item.unitName + ')' : ''}`,
          align: 'center',
          subMerge: [
            {
              label: '最大值',
              prop: 'maxValue'
            },
            {
              label: '发生时间',
              prop: 'maxValueCreateTime'
            },
            {
              label: '最小值',
              prop: 'minValue'
            },
            {
              label: '发生时间',
              prop: 'minValueCreateTime'
            },
            {
              label: '平均值',
              prop: 'avgValue'
            }
          ]
        }
      ]
      // 表数据
      this.tableData = data.reduce((arr, item) => {
        return [
          ...arr,
          {
            ...item,
            ...item.limitValue,
            deviceName: this.deviceName
          }
        ]
      }, [])
    },
    handleExtremumDataSurfaceEcharts(data) {
      // 类型对象
      let item = this.mutuallyArr.find((ele) => this.radio == ele.paramId)
      // 图表
      let echarsData = data.map((x) => {
        return {
          date: x.date,
          list: [
            {
              name: '最大值',
              value: x.limitValue.maxValue
            },
            {
              name: '平均值',
              value: x.limitValue.avgValue
            },
            {
              name: '最小值',
              value: x.limitValue.minValue
            }
          ]
        }
      })
      let title = `${this.deviceName} ${this.queryParams.startTime}至${this.queryParams.endTime} ${this.paramName}`
      this.$refs['powerECharts' + this.choiceTypeTab][0].init(this.setLineChart(echarsData, title))
      this.$refs['powerECharts' + this.choiceTypeTab][0].chartResize()
    },
    // 平均功率因数
    handleAveragePower(data) {
      // 表头
      this.tableColumn = [
        {
          label: '设备名称',
          prop: 'deviceName',
          width: 160
        },
        {
          label: '日期',
          prop: 'date',
          width: 160
        },
        {
          label: '正向有功电能',
          prop: 'positiveActiveEnergy'
        },
        {
          label: '反向有功电能',
          prop: 'reverseActiveEnergy'
        },
        {
          label: '正向无功电能',
          prop: 'positiveReactiveEnergy'
        },
        {
          label: '反向无功电能',
          prop: 'reverseReactiveEnergy'
        },
        {
          label: '平均功率因数',
          prop: 'avgEnergy'
        }
      ]
      // 表数据
      this.tableData = data.reduce((arr, item) => {
        return [
          ...arr,
          {
            ...item,
            deviceName: item.deviceName ? item.deviceName : this.deviceName
          }
        ]
      }, [])
    },
    // 谐波监测
    handleHarmonicMonitoring(data) {
      // 表头
      this.tableColumn = [
        {
          label: '设备名称',
          prop: 'deviceName',
          width: 160
        },
        {
          label: '采集时间',
          prop: 'date',
          width: 160
        },
        ...data[0].electDataResponse.map((ele) => {
          let item = this.mutuallyArr.find((v) => v.paramId == ele)
          return {
            label: `${ele.paramName}`,
            prop: `value${ele.paramId}`
          }
        })
      ]
      // 表数据
      data.forEach((ele) => {
        let harmonic = ele?.electDataResponse.reduce((acc, v) => {
          acc['value' + v.paramId] = v.paramValue
          return acc
        }, {})
        let obj = {
          deviceName: this.deviceName,
          date: ele.date,
          ...harmonic
        }
        this.tableData.push(obj)
      })
    },
    handleHarmonicMonitoringEcharts(data) {
      let startTime = moment(this.queryParams.startTime).format('YYYY-MM-DD') + ' 00:00:00'
      let endTime = moment(this.queryParams.endTime).format('YYYY-MM-DD') + ' 24:00:00'
      let newData = data.map((ele) => {
        return {
          date: ele.date,
          list: ele.electDataResponse.map((v) => {
            return {
              name: v.paramName,
              value: v.paramValue
            }
          })
        }
      })
      let title = `${this.deviceName} ${this.queryParams.startTime} ${this.paramName}`
      this.$refs['powerECharts' + this.choiceTypeTab][0].init(this.setLineChart(newData, title, startTime, endTime))
      this.$refs['powerECharts' + this.choiceTypeTab][0].chartResize()
    },
    // 图形模式
    handleChartMode(typeTab, data) {
      if (typeTab.type == '0') {
        // 原始数据
        this.handleOriginalDataSurfaceEcharts(data)
      } else if (typeTab.type == '1') {
        // 极值数据
        this.handleExtremumDataSurfaceEcharts(data)
      } else if (typeTab.type == '2') {
        // 平均功率因数
        this.handleAveragePower(data)
      } else if (typeTab.type == '3') {
        // 谐波监测
        this.handleHarmonicMonitoringEcharts(data)
      }
    },
    // 列表模式
    handleListMode(typeTab, data) {
      if (typeTab.type == '0') {
        // 原始数据
        this.handleOriginalDataSurface(data)
      } else if (typeTab.type == '1') {
        // 极值数据
        this.handleExtremumDataSurface(data)
      } else if (typeTab.type == '2') {
        // 平均功率因数
        this.handleAveragePower(data)
      } else if (typeTab.type == '3') {
        // 谐波监测
        this.handleHarmonicMonitoring(data.reverse())
      }
    },
    getList() {
      // console.log(this.checkList)
      this.tabelLoading = true
      this.handleDateChange()
      const typeTab = this.typeTabs.find((ele) => ele.type == this.choiceTypeTab)
      let monitorEntityDetail
      switch (this.choiceTypeTab) {
        case '0': // 原始数据 --
          monitorEntityDetail = this.checkList.toString()
          break
        case '1': // 极值数据 --
          monitorEntityDetail = this.radio
          break
        case '3': // 谐波监测 --
          const arr = this.queryParams.selectedSonType == 1 ? this.harmonicVoltageArr : this.harmonicCurrentArr
          const paramName = arr.find((ele) => ele.paramId == this.harmonicRadio).paramName
          monitorEntityDetail = this.harmonicOrder
            .map((ele) => {
              return ele.paramObj.find((v) => v.paramName == paramName)?.paramId || ''
            })
            .toString()
          break
        default:
          monitorEntityDetail = '1'
          break
      }
      const data = {
        ...this.queryParams,
        monitorEntityDetail,
        projectCode: this.projectCode
      }
      this.$api[typeTab.api](data)
        .then((res) => {
          this.tableColumn = []
          this.tableData = []
          if (!res.data.length) return this.$refs['powerECharts' + this.choiceTypeTab][0].init(this.setLineChart([]))
          this.$nextTick(() => {
            if (this.activeTab == 0) {
              // 图形模式
              this.handleChartMode(typeTab, res.data)
            } else {
              // 列表模式
              this.handleListMode(typeTab, res.data)
            }
            this.tabelLoading = false
          })
        })
        .catch((err) => {
          console.error(err)
          this.tabelLoading = false
        })
    },
    handleDateChange() {
      // 时间类型(0:日 1:月 2:年 3:自定义)
      let type = this.queryParams.queryModel
      switch (type) {
        case 0: // 日
          this.queryParams.startTime = this.dateTime.day
          this.queryParams.endTime = this.dateTime.day
          break
        case 1: // 月
          this.queryParams.startTime = moment(this.dateTime.month).startOf('month').format('YYYY-MM-DD')
          this.queryParams.endTime = moment(this.dateTime.month).endOf('month').format('YYYY-MM-DD')
          break
        case 2: // 年
          this.queryParams.startTime = moment(this.dateTime.year).startOf('year').format('YYYY-MM-DD')
          this.queryParams.endTime = moment(this.dateTime.year).endOf('year').format('YYYY-MM-DD')
          break
        case 3: // 自定义
          this.queryParams.startTime = this.dateTime.custom[0]
          this.queryParams.endTime = this.dateTime.custom[1]
          break
      }
    },
    nodeClick(data) {
      // console.log(data)
      this.queryParams.monitorEntityCode = data.code
      this.deviceName = data.name
      this.getList()
    },
    getQueryParamList() {
      this.$api.queryElectMonitoCondition().then((res) => {
        this.typeTabs[0].typeArr = res.data
        this.typeTabs[1].typeArr = res.data
        this.onTypeQuick(this.typeTabs[0].typeArr[0])
        // this.getList()
      })
    },
    selectEntityGroup() {
      this.queryParams.monitorEntityCode = ''
      let data = {
        entityMenuCode: this.queryParams.menuCode,
        projectCode: this.projectCode
      }
      if (this.isKeyDevice == 1) {
        this.$api.selectEntityGroup(data).then((res) => {
          this.treeData = res.data.reduce((arr, item) => {
            let obj = {
              name: item.entityTypeName,
              code: item.entityTypeId,
              lv: '1',
              children: [
                ...item.list.map((ele) => {
                  return {
                    name: ele.imsName,
                    code: ele.imsCode,
                    lv: '2'
                  }
                })
              ]
            }
            return [...arr, obj]
          }, [])
          if (this.treeData.length && this.treeData[0].children.length) {
            this.queryParams.monitorEntityCode = this.treeData[0].children[0].code
            this.deviceName = this.treeData[0].children[0].name
            this.$nextTick(() => {
              this.$refs['tree'].setCurrentKey(this.treeData[0].children[0].code)
            })
          }
          this.getQueryParamList()
        })
      } else {
        this.$api.GetElectricityKeyDevice({...data, keyDevice: this.isKeyDevice}).then(res => {
          this.treeData = res.data.map(item => {
            return {
              name: item.imsName,
              code: item.imsCode,
              lv: '2'
            }
          })
          if (this.treeData.length) {
            this.queryParams.monitorEntityCode = this.treeData[0].code
            this.deviceName = this.treeData[0].name
            this.$nextTick(() => {
              this.$refs['tree'].setCurrentKey(this.treeData[0].code)
            })
          }
          this.getQueryParamList()
        })
      }
    },
    GetEntityMenuList() {
      this.$api.GetEntityMenuList({ projectId: this.projectCode }).then((res) => {
        this.tabs = res.data.filter((ele) => ele.level == 1)
        this.queryParams.menuCode = this.tabs[0].code
        this.selectEntityGroup()
      })
    },
    reset() {
      this.resetDateTime()
      this.getList()
    },
    submit() {
      this.getList()
    },
    jumpDate(type) {
      if (this.selectedTimeType.dateType == 'day' && this.dateTime.day) {
        this.dateTime.day = moment(this.dateTime.day)[type](1, 'days').format('YYYY-MM-DD')
      } else if (this.selectedTimeType.dateType == 'month' && this.dateTime.month) {
        this.dateTime.month = moment(this.dateTime.month)[type](1, 'month').format('YYYY-MM-DD')
      } else if (this.selectedTimeType.dateType == 'year' && this.dateTime.year) {
        this.dateTime.year = moment(this.dateTime.year)[type](1, 'year').format('YYYY-MM-DD')
      }
      this.getList()
    },
    onDateType(v) {
      this.selectedTimeType = v
      this.queryParams.queryModel = v.status
      this.resetDateTime()
    },
    resetDateTime() {
      let current = moment().format('YYYY-MM-DD')
      this.dateTime = {
        day: current,
        month: current,
        year: current,
        custom: [current, current]
      }
      // 极值数据  时间范围默认7天
      if (this.choiceTypeTab == 1) {
        let sevenDaysAgo = moment().subtract(6, 'days').format('YYYY-MM-DD')
        this.dateTime.custom = [sevenDaysAgo, current]
      }
    },
    onTypeQuick(item) {
      if (item) {
        this.queryParams.selectedSonType = item.paramId
        this.paramName = item.paramName
        // 默认选中一个
        if (item.paramObj?.length && this.choiceTypeTab == 0) {
          // 原始数据  多选
          this.mutuallyArr = item.paramObj
          this.checkList = [item.paramObj[0].paramId]
          this.getList()
        } else if (item.paramObj?.length && this.choiceTypeTab == 1) {
          // 极值数据 单选
          this.mutuallyArr = item.paramObj
          this.radio = item.paramObj[0].paramId
          this.getList()
        } else if (this.choiceTypeTab == 3) {
          // 谐波监测 获取谐波监测数据
          this.queryParamList(item.paramId)
        }
      } else if (this.choiceTypeTab == 2) {
        this.getList()
      }
    },
    // Tab切换
    handleClick(type) {
      this.activeTab = type
      this.getList()
    },
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    /**
     * 将时间选择器的icon放在右边
     */
    toTheRightOfTime() {
      let ElRangeCloseIcon = document.getElementsByClassName('el-range__close-icon')[0]
      ElRangeCloseIcon.innerHTML = '<i class="el-icon-date"></i>'
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .is-current .el-tree-node__content {
  background: rgb(53 98 219 / 20%) !important;
}

::v-deep .el-tab-pane {
  height: calc(100%) !important;
}

::v-deep .el-tabs {
  height: calc(100%) !important;
}

::v-deep .el-tabs__content {
  height: calc(100%) !important;
}

.choice_row ::v-deep .el-checkbox__input.is-disabled + span.el-checkbox__label {
  color: #3562db !important;
}

.search-aside-item {
  display: inline-block;
  font-size: 14px;
  padding: 0 30px;
  height: 32px;
  line-height: 32px;
  font-family: PingFangSC-Regular;
  color: $color-primary;
  border: 1px solid $color-primary;
  background: #fff;
  margin-right: 20px;
  border-radius: 4px;
  cursor: pointer;

  &:hover,
  &:focus {
    color: #fff;
    font-family: PingFangSC-Regular;
    border-color: $color-primary;
    background-color: $color-primary;
    font-weight: 500;
  }
}

.search-aside-item-active {
  color: #fff;
  font-family: PingFangSC-Regular;
  border-color: $color-primary;
  background-color: $color-primary;
  font-weight: 500;
}

.team-tree {
  ::v-deep .custom-tree-node {
    overflow: hidden;
  }
}

::v-deep .el-tabs__header .el-tabs__nav-wrap::after {
  height: 1px !important;
  background-color: #dcdfe6 !important;
}

.pow_header {
  background: #fff;
  border-radius: 4px;

  .title {
    border-bottom: 1px solid #dcdfe6;

    &_p {
      padding: 19px 16px;
    }
  }

  .tabs {
    padding: 0 16px;
  }
}

.pow_content {
  margin-top: 16px;
  // background: #fff;
  height: 100%;
  display: flex;

  .content_left {
    background: #fff;
    width: 220px;
    height: 100%;
    margin-right: 16px;

    .all_tree {
      height: 100%;
      padding: 16px;
      ::v-deep .el-tabs {
        height: auto !important;
        margin-bottom: 16px;
        .el-tabs__content {
          display: none;
        }
      }
      .my_tree {
        height: calc(100% - 75px - 32px);
        overflow-y: auto;
        overflow-x: hidden;
        ::v-deep .el-tree-node__content {
          width: 100%;

          .custom-tree-node {
            display: inline-block;
            width: 100%;
            height: 21px;
            .item {
              display: inline-block;
              width: calc(100% - 20px);
              text-overflow: ellipsis;
              white-space: nowrap;
              overflow: hidden;
            }
          }
        }
      }
    }
  }

  .content_right {
    background: #fff;
    width: 0;
    flex: 1;

    .typeTabs {
      padding: 0 16px;
      height: 100%;
      // border-bottom: 1px solid #dcdfe6;
    }

    .query_row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 16px 0;
      padding: 0 16px;

      .query_left {
        display: flex;
        align-items: center;

        .up_down {
          display: flex;
          margin-right: 6px;

          .jump_date {
            cursor: pointer;
            margin-left: 16px;
            font-size: 14px;
            color: #3562db;
          }

          .no_clicl {
            cursor: not-allowed;
            opacity: 0.5;
            pointer-events: none;
            color: #414653;
          }
        }

        .re_btn {
          margin-left: 10px;
        }
      }

      .query_right {
        display: flex;

        .pattern-item {
          cursor: pointer;
          font-size: 15px;

          .pattern-icon {
            font-size: 16px;
            margin-right: 6px;
          }
        }

        .pattern-item:last-child {
          margin-left: 16px;
        }
      }
    }

    .btn_row {
      padding: 0 16px;
      background: #fff;
      border-radius: 4px;
      display: flex;
      align-items: center;
      flex-wrap: wrap;

      .harmonicOrder {
        display: flex;
        align-items: center;

        span {
          font-size: 14px;
        }
      }
    }

    .choice_row {
      margin: 16px 0;
      padding: 0 16px;
    }

    .my_tabel {
      padding: 0 16px;
      height: calc(100% - 230px);
    }
  }
}
</style>
