<template>
  <PageContainer v-loading="contentLoading">
    <div slot="header" class="table-content">
      <div class="topFilter">
        <div class="backBar">
          <span style="cursor: pointer" @click="$router.go(-1)">
            <i class="el-icon-arrow-left"></i>
            考试任务详情
          </span>
        </div>
      </div>
      <el-tabs v-model="activeName" class="paneClass">
        <el-tab-pane label="考试信息" name="0"></el-tab-pane>
        <el-tab-pane label="试题信息" name="1"></el-tab-pane>
      </el-tabs>
      <div class="baseInfo">
        <h1>基础信息</h1>
        <div class="contenter">
          <div class="itemInfo">
            <span class="title">试卷名称：</span>
            <span class="value">{{formInfo.name}}</span>
          </div>
          <div class="itemInfo">
            <span class="title">所属单位：</span>
            <span class="value">{{formInfo.deptName}}</span>
          </div>
          <div class="itemInfo">
            <span class="title">所属科目：</span>
            <span class="value">{{formInfo.subjectName}}</span>
          </div>
          <div class="itemInfo">
            <span class="title">考试期限：</span>
            <span class="value">{{formInfo.startTime}}至{{formInfo.endTime}}</span>
          </div>
          <div class="itemInfo">
            <span class="title">答题时长：</span>
            <span class="value">{{formInfo.duration||'-'}}分钟</span>
          </div>
          <div class="itemInfo">
            <span class="title">试卷总分：</span>
            <span class="value">{{formInfo.score||'-'}}分</span>
          </div>
          <div class="itemInfo">
            <span class="title">通过分数：</span>
            <span class="value">{{formInfo.passScore||'-'}}分</span>
          </div>
          <div class="itemInfo">
            <span class="title">已答题人数：</span>
            <span class="value">{{formInfo.alRStudentNum||'-'}}人</span>
          </div>
          <div class="itemInfo">
            <span class="title">未答题人数：</span>
            <span class="value">{{formInfo.noStudentNum||'-'}}人</span>
          </div>
          <div class="itemInfo">
            <span class="title">考试状态：</span>
            <span class="value">{{formInfo.taskStatusPc | filterStatus}}</span>
          </div>
        </div>
      </div>
    </div>
    <div slot="content" class="courseContent">
      <div v-if="activeName == '0'" class="table_content">
        <div class="top">
          <span>参考信息</span>
          <div>
            <el-button type="primary" plain :disabled="isDistribute || multipleSelection.length==0" @click="distribute">派发</el-button>
            <el-button type="primary" :disabled="multipleSelection.length<1" @click="exportClickExport">导出</el-button>
          </div>
        </div>
        <div class="contentiner">
        <el-table  
            :data="formInfo.examRecord" 
            style="width: 100%;height:100%;overflow: auto;"  
            border 
            stripe 
            title="双击查看详情" 
            @row-dblclick="openDetails"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" fixed="left" align="center"></el-table-column>
            <el-table-column prop="name" label="姓名" align="center" show-overflow-tooltip>
              <template slot-scope="scope">
                {{ scope.row.name}}
                <span v-if="scope.row.resitSequence">(补考{{scope.row.resitSequence}})</span>
              </template>
            </el-table-column>
            <el-table-column prop="" label="性别" align="center" show-overflow-tooltip>
              <template slot-scope="scope">
                <span>{{scope.row.gender=='0'?'女':'男'}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="mobilePhone" label="联系电话" align="center" show-overflow-tooltip></el-table-column>
            <el-table-column prop="laboratoryName" label="所属部门" align="center" show-overflow-tooltip></el-table-column>
            <el-table-column prop="actualDuration" label="答题时长" align="center" show-overflow-tooltip>
              <template slot-scope="scope">
                {{ scope.row.actualDuration || '-'}} 分钟
              </template>
            </el-table-column>
            <el-table-column prop="submitTime" label="提交时间" align="center" show-overflow-tooltip></el-table-column>
            <el-table-column prop="actualScore" label="考试得分" align="center" show-overflow-tooltip>
              <template slot-scope="scope">
                {{ scope.row.actualScore || '-'}} 分
              </template>
            </el-table-column>
            <el-table-column label="通过状态" align="center" show-overflow-tooltip>
              <template slot-scope="scope">
                <div class="statusBtn">
                  <span v-if="scope.row.pass == '1'" class="auditNo">
                    <img src="../../../assets/images/icon-wrapper.png" alt="" />
                    未通过
                  </span>
                  <span v-if="scope.row.pass == '0'" class="relwase"
                    >
                    <img src="../../../assets/images/pass.png" alt="">
                    通过</span
                  >
                  <span v-if="scope.row.pass == '2'" class="relwaseNo"
                    >未参与</span
                  >
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="taskTeamName"
              label="操作"
              align="center"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <div class="operateBths">
                  <el-link v-if="scope.row.pass != '2'" type="primary" @click="openDetails(scope.row)">查看</el-link>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div v-else class="question_content">
        <div
          v-for="(k, ind) in formInfo.questions"
          :key="k.id"
          :name="k.id"
          :class="['exercisesItem', k.isExpand ? 'expand' : '']"
          :ref="'exercisesItem' + ind"
        >
          <div class="exercisesTop">
            <div class="left">
              <div class="exercisesType">
                {{
                  k.type == "1" ? "单选题" : k.type == "2" ? "多选题" : "判断题"
                }}
              </div>
               <span>分数: {{ k.score || '0' }} 分</span>
            </div>
            <div class="right">
              <span @click="isExpandBtn(k, index)">{{
                k.isExpand ? "折叠" : "展开"
              }}</span>
            </div>
          </div>
          <div :class="['exercisesName', k.isExpand ? '' : 'title']">
            {{ k.topic }}
          </div>
          <el-radio-group
            v-if="k.type == '1'"
            class="radio"
            v-model="k.answer"
            disabled
          >
            <el-radio v-for="(j, index) in k.options" :key="index" :label="j.id"
              >{{ j.id }}. {{ j.label }}</el-radio
            >
          </el-radio-group>
          <el-checkbox-group
            v-if="k.type == '2'"
            v-model="k.answer"
            class="radio"
            disabled
          >
            <el-checkbox
              v-for="(j, index) in k.options"
              :key="index"
              :label="j.id"
              >{{ j.id }}. {{ j.label }}</el-checkbox
            >
          </el-checkbox-group>
          <p>答案：{{ k | getAnswer }}</p>
          <p>
            解析：
            {{ k.analysis }}
          </p>
        </div>
      </div>
      <exportDialog
        :exportType="exportType"
        :materRows="multipleSelection"
        :dialogVisibleExport="dialogVisibleExport"
        @closeDialog="closeDialog"
        ref="dialogExport"
      ></exportDialog>
    </div>
  </PageContainer>
</template>
<script>
import moment from "moment";
import exportDialog from "@components/recordExport/recordExport";  //导出
export default {
  components: { exportDialog},
  data() {
    return {
      contentLoading: false,
      activeName: "0",
      routeInfo: "",
      moment,
      formInfo: {},
      id: "",
      exportType: 2,
      multipleSelection: [],
      dialogVisibleExport: false,
      isDistribute:true
    };
  },
  created() {
    this.routeInfo = JSON.parse(sessionStorage.getItem("routeInfo"));
    this.id = this.$route.query.id;
    if (this.id) {
      this.getDetails();
    }
  },
  filters: {
    getAnswer(val){
      if(val.type=='3'){
        return val.answer=='1'?'正确':'错误'
      }else if(val.type=='2'){
        return val.answer.toString()
      }else{
        return val.answer
      }
    },
    filterStatus(val){
      return val=='1'?'已结束':val=='2'?'已作答':val=='3'?'进行中':'未作答'
    }
  },
  methods: {
    // 获取详情
    getDetails() {
      this.contentLoading = true;
      this.$api.getRecordInfo({id:this.id}).then(res=>{
        if(res.data.questions.length){
          res.data.questions.forEach(item => {
            item.isExpand = false
            item.options = JSON.parse(item.options)
            if(item.type=='2'){
              item.answer = item.answer.split(',')
            }
          });
        }
        this.formInfo = res.data
      })
      this.contentLoading = false;
    },
    // 展开试题
    isExpandBtn(item, index) {
      item.isExpand = !item.isExpand;
    },
    openDetails(val){
      if(val.pass=='2'){return}
      this.$router.push({
        path: "answerInfo",
        query: {
          respondent: val.respondent,
          recordId:val.id //任务id
        },
      });
    },
    //勾选
    handleSelectionChange(val) {
      this.multipleSelection = val;
      const hasIdTwo = val.some(item => item.needDistribute == 1);
      this.isDistribute = hasIdTwo
    },
    distribute(){
      let studentIds = this.multipleSelection.map(item=>item.respondent).join(',')
      this.$router.push({
        path:'addTask',
        query:{
          studentIds,
          type:'exam'
        }
      })
    },
    exportClickExport(){
      this.dialogVisibleExport = true;
      this.$nextTick(()=>{
        this.$refs.dialogExport.getExportField()
      })
    },
    closeDialog() {
      this.dialogVisibleExport = false;
    },
  },
};
</script>
<style lang="scss" scoped>
.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
  .baseInfo {
    padding: 0 24px 24px 24px;
    .contenter {
      padding-top: 24px;
      display: flex;
      font-size: 14px;
      flex-wrap: wrap;
      .itemInfo {
        width: 50%;
        margin-bottom: 16px;
        .title {
          color: #666;
          margin-right: 46px;
        }
      }
    }
  }
}
.topFilter {
  padding: 15px;
  height: 60px;
  background-color: #fff;
  .backBar {
    color: #121f3e;
    height: 30px;
    border-bottom: 1px solid #dcdfe6;
  }
}
.paneClass {
  padding: 0 16px 16px 16px;
}
.courseContent {
  height: 100%;
  margin-top: 16px;
  background-color: #fff;
  padding: 16px;
  .question_content {
    height: calc(100% - 70px);
    margin-top: 16px;
    overflow: auto;
    .exercisesItem {
      height: 90px;
      overflow: hidden;
      background-color: #fff;
      margin-bottom: 16px;
      padding: 16px;
      border-bottom: 4px solid #faf9fc;
      font-size: 14px;
      .exercisesTop {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        .left {
          display: flex;
          align-items: center;
          justify-content: center;
          span {
            color: #7f848c;
          }
        }
        .right {
          color: #ccced3;
          display: flex;
          align-items: center;
          .line {
            width: 2px;
            height: 14px;
            margin: 0 10px 0 26px;
            background-color: #dcdfe6;
          }
          span {
            color: #3562db;
            margin-left: 16px;
            cursor: pointer;
          }
          i {
            color: #3562db;
            cursor: pointer;
            margin-left: 16px;
          }
        }
        .exercisesType {
          width: 58px;
          height: 22px;
          line-height: 22px;
          text-align: center;
          border-radius: 4px;
          color: #86909c;
          background-color: #ededf5;
          margin: 0 10px;
        }
      }
      .exercisesName {
        line-height: 20px;
        margin-bottom: 16px;
      }
      .title {
        overflow: hidden;
        -webkit-line-clamp: 2;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
      }
      .el-radio {
        margin-left: 38px;
        font-size: 14px;
        color: #7f848c !important;
        line-height: 30px;
      }
      p {
        font-size: 14px;
        color: #7f848c !important;
        line-height: 20px;
        margin-bottom: 16px;
      }
    }
    .expand {
      height: auto;
    }
  }
  .table_content {
    height: calc(100% - 70px);
    // overflow: auto;
    .top{
      display: flex;
      justify-content: space-between;
    }
    .contentiner{
      height: 100%;
    }
    .statusBtn {
      font-size: 14px;
      display: flex;
      justify-content: center;
      .auditIng {
        width: 58px;
        height: 24px;
        background-color: #fff7e8;
        border-radius: 4px;
        color: #d25f00;
      }
      .auditNo {
        width: 78px;
        height: 24px;
        background-color: #ffece8;
        border-radius: 4px;
        color: #cb2634;
        img {
          vertical-align: middle;
        }
      }
      .relwase {
        width: 58px;
        height: 24px;
        background-color: #e8ffea;
        border-radius: 4px;
        color: #009a29;
        img {
          vertical-align: middle;
        }
      }
      .inProgress{
        width: 86px;
        height: 24px;
        background-color: #E6EFFC ;
        border-radius: 4px;
        color: #2749BF;
      }
      .relwaseNo{
        width: 58px;
        height: 24px;
        background-color: #F2F4F9;
        border-radius: 4px;
        color: #86909C;   
      }
    }
    .operateBths{
      color: #3562DB;
      .el-link{
        margin-right: 8px;
      }
    }
  }
}
::v-deep .el-radio {
  display: block;
  margin: 10px 0;
}

::v-deep .el-checkbox {
  display: block;
  margin: 10px 0;
}
::v-deep .el-checkbox-group {
  margin-left: 38px;
}
</style>
