<template>
  <div>
    <el-row>
      <el-col :span="8">
        <div class="item">
          <span class="item-title">资产大类</span>
          <span class="item-content">{{ detailsInfo.assetsClassesName || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">68分类</span>
          <span class="item-content">{{ detailsInfo.typeName68 || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">是否计量</span>
          <span class="item-content">{{ detailsInfo.isMetering === '0' ? '是' : '否' || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">附属设备</span>
          <span class="item-content">{{ detailsInfo.affiliateMainName || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">中医诊疗设备</span>
          <span class="item-content">{{ detailsInfo.tcmdiagnosisEquipmentName || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">风险等级</span>
          <span class="item-content">{{ detailsInfo.riskLevel || '--' }}</span>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="item">
          <span class="item-title">资产小类</span>
          <span class="item-content">{{ detailsInfo.competentDeptTypeName || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">资产分类标准</span>
          <span class="item-content">{{ detailsInfo.medicalEquipmentName || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">是否防疫</span>
          <span class="item-content">{{ detailsInfo.isAntiepidemic === '0' ? '是' : '否' || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">特种设备</span>
          <span class="item-content">{{ detailsInfo.specialEquipmentName || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">检验类设备</span>
          <span class="item-content">{{ detailsInfo.inspectionEquipmentName || '--' }}</span>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="item">
          <span class="item-title">国标分类</span>
          <span class="item-content">{{ detailsInfo.financeTypeName || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">是否维保</span>
          <span class="item-content">{{ detailsInfo.isMaintenance === '0' ? '是' : '否' || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">类别属性</span>
          <span class="item-content">{{ detailsInfo.costTypeName || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">急救生命支持设备</span>
          <span class="item-content">{{ detailsInfo.emergencyEquipmentName || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">辐射设备</span>
          <span class="item-content">{{ detailsInfo.radiationEquipmentName || '--' }}</span>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
export default {
  name: 'CategoryInfo',
  props: ['detailsInfo'],
  data() {
    return {}
  },
  mounted() {},
  methods: {}
}
</script>

<style scoped lang="scss">
  .item {
    margin-bottom: 12px;
    font-size: 14px;
    display: flex;
    // height: 20px;
    // line-height: 20px;
    .item-title {
      color: #909399;
      min-width: 110px;
    }
    .item-content {
      color: #121f3e;
    }
  }
</style>
