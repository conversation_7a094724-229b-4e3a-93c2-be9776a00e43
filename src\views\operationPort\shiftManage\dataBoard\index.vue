<template>
  <PageContainer class="dataBoard">
    <template #content>
      <el-tabs class="dataBoard__nav" :value="currentTab" @tab-click="onTabClick">
        <el-tab-pane name="currentTime" label="实时看板"></el-tab-pane>
        <el-tab-pane name="originalData" label="原始数据"></el-tab-pane>
        <el-tab-pane name="dutyLog" label="值班记录"></el-tab-pane>
      </el-tabs>
      <component :is="currentComponent" class="dataBoard__content" />
    </template>
  </PageContainer>
</template>
<script>
import currentTab from './tabsComponents/currentTimeTab.vue'
import originalTab from './tabsComponents/originalDataTab.vue'
import dutyLogTab from './tabsComponents/dutyLogTab.vue'
export default {
  name: 'dataBoard',
  components: {
    currentTab,
    originalTab,
    dutyLogTab
  },
  data() {
    return {
      currentTab: 'currentTime',
      componentConfig: {
        currentTime: 'currentTab',
        originalData: 'originalTab',
        dutyLog: 'dutyLogTab'
      }
    }
  },
  computed: {
    currentComponent() {
      return this.componentConfig[this.currentTab]
    }
  },
  methods: {
    onTabClick(e) {
      console.log(e, '777777777777');

      const name = e.name
      if (name !== this.currentTab) {
        this.$router.push({ query: { name } })
        this.currentTab = name
      }
      if (name !== 'manual') {
        sessionStorage.removeItem('hazardousChemicalBaseInfo');
      }
    },
  }
}
</script>
<style lang="scss" scoped>
.page-container.dataBoard {
  ::v-deep(.container-content) {
    background: #fff;

    .dataBoard__nav {
      .el-tabs__nav-scroll {
        padding: 0 32px;
      }
    }

    .dataBoard__content {
      height: calc(100% - 40px);
      overflow: hidden;
    }
  }
}
</style>
