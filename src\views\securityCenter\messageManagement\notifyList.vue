<template>
  <PageContainer>
    <div slot="header">
      <div class="search-header">
        <div class="search-from">
          <el-input v-model.trim="formInline.repairCode" placeholder="编号" style="width: 200px;"></el-input>
          <el-cascader
            v-model="formInline.repairPlaceIds"
            clearable
            class="sino_sdcp_input mr15"
            :options="areaArr"
            :props="props"
            placeholder="隐患区域"
            @change="handleAreaChange"
          ></el-cascader>
          <el-select v-model="formInline.state" clearable placeholder="处理状态">
            <el-option v-for="(item, index) in taskStateOption" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
          <el-button type="primary" @click="reset">重置</el-button>
          <el-button type="primary" @click="searchClick()">查询</el-button>
          <el-button type="primary" icon="el-icon-search" @click="openSeach">高级搜索</el-button>
        </div>
      </div>
    </div>
    <div slot="content" style="height: 100%; margin-top: 16px; background-color: #fff;">
      <div class="table-content">
        <el-table v-loading="tableLoading" stripe :data="purchaseTable" :border="true" :height="tableHeight" title="双击查看详情" @row-dblclick="watchDetail">
          <template slot="empty">
            <p class="tableEmptyLabel">{{ $store.state.tableLabel }}</p>
          </template>
          <el-table-column type="selection" width="55" fixed="left" align="center"></el-table-column>
          <el-table-column type="index" label="序号" width="65">
            <template slot-scope="scope">
              <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="repairCode" label="编号" show-overflow-tooltip width="180"></el-table-column>
          <el-table-column prop="state" label="状态" show-overflow-tooltip width="150"></el-table-column>
          <el-table-column prop="questionDetailType" show-overflow-tooltip label="隐患分类" width="180"></el-table-column>
          <el-table-column prop="riskPlace" show-overflow-tooltip label="隐患区域" width="180"></el-table-column>
          <el-table-column prop="taskTeamName" show-overflow-tooltip label="反馈部门" width="180"></el-table-column>
          <el-table-column prop="createPersonName" show-overflow-tooltip width="130" label="反馈人"></el-table-column>
          <el-table-column prop="reportTime" show-overflow-tooltip width="180" label="反馈时间"></el-table-column>
          <el-table-column prop="riskLevel" show-overflow-tooltip label="隐患等级" width="130"></el-table-column>
          <el-table-column prop="dealTime" show-overflow-tooltip label="要求整改完成时间" width="180"></el-table-column>
        </el-table>
        <div class="table-page">
          <el-pagination
            style="margin-top: 3px;"
            :current-page="paginationData.currentPage"
            layout="total, sizes, prev, pager, next, jumper"
            :total="paginationData.total"
            :page-size="paginationData.pageSize"
            :page-sizes="[15, 30, 50]"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
      <advancedSearch :closeState="advancClose" class="advanced-search" @isCloseState="getData" @resetSearch="reset" @searchList="searchList">
        <template slot="content">
          <el-form ref="formInline" :model="formInline" :inline="true" class="advanced-search-form" label-position="right" label-width="80px">
            <div class="sino-filter-row">
              <div class="sino-filter-col">
                <el-form-item label="隐患等级" prop="riskLevel">
                  <el-select v-model="formInline.riskLevel" clearable placeholder="隐患等级">
                    <el-option v-for="item of riskLevelList" :key="item.dictCode" :label="item.dictName" :value="item.dictCode"></el-option>
                  </el-select>
                </el-form-item>
              </div>
            </div>
            <div class="sino-filter-row">
              <div class="sino-filter-col">
                <el-form-item label="反馈人" porp="createPersonCode">
                  <el-select v-model="formInline.createPersonCode" clearable placeholder="请选择">
                    <el-option v-for="(item, index) in companyList" :key="index" :label="item.name" :value="item.id"></el-option>
                  </el-select>
                </el-form-item>
              </div>
            </div>
            <div class="sino-filter-row">
              <div class="sino-filter-col">
                <el-form-item label="反馈时间" prop="reportTimeList">
                  <el-date-picker
                    v-model="formInline.reportTimeList"
                    :editable="false"
                    :clearable="false"
                    class="sino_sdcp_input mr15 filters-date-picker"
                    type="daterange"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    @change="handleTime"
                  ></el-date-picker>
                </el-form-item>
              </div>
            </div>
          </el-form>
        </template>
      </advancedSearch>
    </div>
  </PageContainer>
</template>

<script>
import tableListMixin from '@/mixins/tableListMixin.js'
import advancedSearch from '@/components/advancedSearch'
import { transData } from '@/util'
export default {
  name: 'notifyList',
  components: { advancedSearch },
  mixins: [tableListMixin],
  data() {
    return {
      formInline: {
        repairCode: '', // 处理编号/隐患名称
        // riskType: '', // 隐患类型
        // riskTypeSecond: '', // 隐患类型2级
        riskLevel: '', // 隐患等级
        reportTimeList: [], // 反馈时间
        reportTimeStart: '', // 反馈开始时间
        reportTimeEnd: '', // 反馈结束时间
        createPersonCode: '', // 反馈人
        // assignPersonCode: '', // 处理人
        state: '', // 处理状态
        repairPlaceId: '', // 隐患区域
        // dealTimelist: [], // 处理时间
        // dealTimeStart: '', // 处理时间开始
        // dealTimeEnd: '', // 处理时间结束
        repairPlaceIds: []
      },

      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      areaArr: [], // 隐患区域
      typeList: [],
      typeList2: [],
      componyList: [],
      // conductorList: [], // 处理人列表
      companyList: [], // 反馈人列表
      taskStateOption: [
        { label: '待认领', value: 0 },
        { label: '待整改', value: 1 },
        { label: '已挂帐', value: 3 },
        { label: '已整改', value: 2 },
        { label: '已完结', value: 4 },
        { label: '已取消', value: 5 }
      ],
      purchaseTable: [],
      advancClose: true,
      tableLoading: false,
      // assetsTypeIdArr: [],
      // assetsTypeIdArr2: [],
      riskLevelList: [],
      props: {
        label: 'gridName',
        value: 'id',
        children: 'children',
        checkStrictly: true
      },
      dialogVisibleImg: false,
      imgArr: [],
      stateType: ''
    }
  },
  mounted() {
    this.requestType()
    this.requestList()
  },
  methods: {
    searchList() {
      this.advancClose = true
      this.searchClick()
    },
    searchClick() {
      // this.$store.commit('changeImasTableLabel')
      // this.$store.commit('changeNetworkError', '查询失败，请检查您的网络…')
      this.paginationData.currentPage = 1
      this.requestList()
    },
    handleTime(val) {
      this.formInline.reportTimeStart = val[0]
      this.formInline.reportTimeEnd = val[1]
    },
    getData(data) {
      this.advancClose = data
    },
    // 查询列表
    requestList() {
      // if (this.$route.query.type =='3') {
      //   this.formInline.state = this.stateType;
      //   this.stateType =''
      // }
      // this.$store.commit('setStateType', '')
      let params = {}
      this.tableLoading = true
      params = {
        ...this.paginationData,
        ...this.formInline
      }
      // params.riskTypeId = this.formInline.riskType ? this.formInline.riskType : this.formInline.riskType
      // params.taskTeamId = JSON.parse(sessionStorage.getItem('LOGINDATA')).controlGroupIds
      // params.userType = JSON.parse(sessionStorage.getItem('LOGINDATA')).roleCode
      params.orderFormFlag = 1
      delete params.total
      delete params.reportTimeList
      // delete params.dealTimelist
      delete params.repairPlaceIds
      this.$api
        .ipsmGetWorkNoticeRecordList(params)
        .then((res) => {
          if (res.code == '200') {
            this.purchaseTable = res.data.list
            // this.purchaseTable = [{}];
            this.paginationData.total = res.data.sum * 1
          } else if (res.message) {
            this.$message.error(res.message)
          }
          this.tableLoading = false
        })
        .catch(() => {
          this.tableLoading = false
        })
    },
    handleAreaChange(val) {
      if (val) {
        this.formInline.repairPlaceId = val[val.length - 1]
      }
    },
    reset() {
      // this.$store.commit('changeImasTableLabel', 'init')
      // this.$store.commit('changeNetworkError', '查询失败，请检查您的网络…')
      this.formInline.repairCode = ''
      // this.assetsTypeIdArr = []
      // this.assetsTypeIdArr2 = []
      this.formInline.riskType = ''
      this.formInline.repairPlaceId = ''
      this.formInline.repairPlaceIds = []
      this.formInline.state = ''
      this.formInline.reportTimeList = []
      this.formInline.reportTimeStart = ''
      this.formInline.reportTimeEnd = ''
      this.$refs['formInline'].resetFields()
      this.formInline.createPersonCode = ''
      this.requestList()
    },
    requestType() {
      // // 各字典项
      // let vm = this
      // // 排查类别1级
      // // this.$http.getDictDataList({}).then((res) => {
      // //   this.typeList = res.data;
      // // });
      this.$api.ipsmGetGridList({}).then((res) => {
        if (res && res.code == 200) {
          this.areaArr = transData(res.data, 'id', 'parentId', 'children')
        }
      })
      // 隐患等级
      this.$api
        .ipsmGetDictList({
          dictType: 'hidden_trouble_grade_type'
        })
        .then((res) => {
          this.riskLevelList = res.data
        })
      // 反馈人 处理人
      this.$api.ipsmRiskWorkGetControlTeamUserList({}).then((res) => {
        // this.conductorList = res.data
        this.companyList = res.data
      })
    },
    watchDetail(row) {
      // this.$store.commit('planIdChange', row.id)
      // this.$store.commit('maintainCodeChange', row.maintainCode)
      this.$router.push({
        name: 'hiddenManagementDetails3',
        query: {
          id: row.workOrderId
        }
      })
    },
    openSeach() {
      this.advancClose = !this.advancClose
    },
    handleSizeChange(val) {
      this.paginationData.currentPage = 1
      this.paginationData.pageSize = val
      this.requestList()
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.requestList()
    }
  }
}
</script>

<style lang="scss" scoped>
.search-header {
  padding: 10px 16px;

  .search-from {
    padding-bottom: 12px;

    & > div {
      margin-top: 12px;
      margin-right: 10px;
      margin-bottom: 12px;
    }
  }
}

.table-content {
  padding: 16px;
  height: calc(100% - 80px);
}

.contentTable-footer {
  padding: 10px 0 0;
}

::v-deep .mterial_file > .el-upload-list {
  position: absolute;
  top: 0;
  width: 500px;
  margin-left: 310px;
  max-height: 160px;
  // overflow: auto;
}

::v-deep .mterial_file > .el-upload--picture-card {
  border: none;
  width: 300px;
}

::v-deep .mterial_file .el-upload .el-upload-dragger {
  width: 300px;
  height: 148px;
}

::v-deep .mterial_file .el-upload__text {
  position: absolute;
  left: 57px;
  color: #606266;
  font-size: 14px;
  text-align: center;
}

::v-deep .el-textarea {
  width: 300px;
}

.el-form {
  padding: 10px 20px;
}
</style>
