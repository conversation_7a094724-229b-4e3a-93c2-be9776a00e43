/*
 * @Description:
 */
import Layout from '@/layout'
import EmptyLayout from '@/layout/empty'
export default [
  {
    path: '/energyConsumption',
    component: Layout,
    redirect: '/energyConsumption/energyOverview',
    name: 'energyConsumption',
    meta: {
      title: '能耗总览',
      menuAuth: '/energyConsumption/energyOverview'
      // classType: 'menu-item-center' // 用于控制左侧菜单的样式 居中显示
    },
    children: [
      {
        path: 'energyOverview',
        name: 'energyOverview',
        component: () => import('@/views/energy/overview/energyOverview.vue'),
        meta: {
          title: '能耗总览',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/energyConsumption'
        }
      }
    ]
  },
  {
    path: '/powerMenu',
    component: Layout,
    redirect: '/powerMenu/powerOverview',
    name: 'powerMenu',
    meta: {
      title: '电',
      menuAuth: '/powerMenu/powerOverview'
      // arrangement: 'column',  // 用于控制左侧菜单的排列方式 一行三个
      // classType: 'row33' // 用于控制左侧菜单的样式 一行三个
    },
    children: [
      {
        path: 'powerOverview',
        name: 'powerOverview',
        component: () => import('@/views/energy/powerMenu/overview.vue'),
        meta: {
          title: '电',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/powerMenu'
        }
      }
    ]
  },
  {
    path: '/waterMenu',
    component: Layout,
    redirect: '/waterMenu/waterOverview',
    name: 'waterMenu',
    meta: {
      title: '水',
      menuAuth: '/waterMenu/waterOverview'
      // arrangement: 'column',  // 用于控制左侧菜单的排列方式 一行三个
      // classType: 'row33' // 用于控制左侧菜单的样式 一行三个
    },
    children: [
      {
        path: 'waterOverview',
        name: 'waterOverview',
        component: () => import('@/views/energy/waterMenu/overview.vue'),
        meta: {
          title: '水',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/waterMenu'
        }
      }
    ]
  },
  {
    path: '/gasMenu',
    component: Layout,
    redirect: '/gasMenu/gasOverview',
    name: 'gasMenu',
    meta: {
      title: '气',
      menuAuth: '/gasMenu/gasOverview'
      // arrangement: 'column',  // 用于控制左侧菜单的排列方式 一行三个
      // classType: 'row33' // 用于控制左侧菜单的样式 一行三个
    },
    children: [
      {
        path: 'gasOverview',
        name: 'gasOverview',
        component: () => import('@/views/energy/gasMenu/overview.vue'),
        meta: {
          title: '气',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/gasMenu'
        }
      }
    ]
  },
  {
    path: '/coldEnergyMenu',
    component: Layout,
    redirect: '/coldEnergyMenu/coldEnergyOverview',
    name: 'coldEnergyMenu',
    meta: {
      title: '冷热量',
      menuAuth: '/coldEnergyMenu/coldEnergyOverview'
      // arrangement: 'column',  // 用于控制左侧菜单的排列方式 一行三个
      // classType: 'row33' // 用于控制左侧菜单的样式 一行三个
    },
    children: [
      {
        path: 'coldEnergyOverview',
        name: 'coldEnergyOverview',
        component: () => import('@/views/energy/coldEnergyMenu/overview.vue'),
        meta: {
          title: '冷热量',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/coldEnergyMenu'
        }
      }
    ]
  },
  {
    path: '/targetMenu',
    component: Layout,
    redirect: '/targetMenu/targetEnergy',
    name: 'targetMenu',
    meta: {
      title: '指标能耗',
      menuAuth: '/targetMenu/targetEnergy'
      // arrangement: 'column',  // 用于控制左侧菜单的排列方式 一行三个
      // classType: 'row33' // 用于控制左侧菜单的样式 一行三个
    },
    children: [
      {
        path: 'targetEnergy',
        name: 'targetEnergy',
        component: () => import('@/views/energy/targetMenu/targetEnergy.vue'),
        meta: {
          title: '指标能耗',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/targetMenu'
        }
      }
    ]
  }
]
