<template>
  <div class="dialogContent">
    <div class="leftTree">
      <div class="search">
        <el-input v-model="filterText" placeholder="请输入" clearable suffix-icon="el-icon-search"> </el-input>
      </div>
      <el-tree
        ref="tree"
        v-loading="treeLoading"
        :data="treeList"
        :props="defaultProps"
        node-key="node"
        :highlight-current="true"
        :filter-node-method="filterNode"
        @node-click="handleNodeClick"
      ></el-tree>
    </div>
    <div class="rightTable">
      <div class="topFilter">
        <div>
          <el-input
            v-model.trim="keyWord"
            placeholder="请输入指标名称"
            style="width: 200px"
            clearable
            maxlength="25"
            onkeyup="if(value.length>25)value=value.slice(0,25)"
          ></el-input>
          <el-button type="primary" plain style="margin-left: 10px" @click="resetForm">重置</el-button>
          <el-button type="primary" @click="searchForm">查询</el-button>
        </div>
      </div>
      <div class="tableContainer">
        <el-table v-loading="tableLoading" border stripe style="width: 100%" :data="tableData" :row-class-name="tableRowClassName" @selection-change="handleSelectionChange">
          <el-table-column type="selection"> </el-table-column>
          <el-table-column prop="name" label="指标名称" show-overflow-tooltip></el-table-column>
          <el-table-column prop="type" label="指标类型" show-overflow-tooltip>
            <template slot-scope="scope">
              <span> {{ formatName(scope.row.type) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="nodeName" label="指标分类" show-overflow-tooltip></el-table-column>
          <el-table-column prop="dataType" label="数据类型" show-overflow-tooltip>
            <template slot-scope="scope">
              <span> {{ scope.row.dataType == 0 ? '数值' : scope.row.dataType == 1 ? '百分比' : '时长' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="参考值" show-overflow-tooltip></el-table-column>
          <el-table-column prop="illustrate" label="指标说明" show-overflow-tooltip></el-table-column>
        </el-table>
        <el-pagination
          :current-page="pagination.current"
          :page-sizes="pagination.pageSizeOptions"
          :page-size="pagination.size"
          :layout="pagination.layoutOptions"
          :total="pagination.total"
          @size-change="paginationSizeChange"
          @current-change="paginationCurrentChange"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
import { cloneDeep } from 'lodash'
export default {
  name: 'closeTargetTemplate',
  props: {
    selectedId: {
      type: Array,
      default: () => {}
    }
  },
  data() {
    return {
      treeLoading: false,
      treeList: [],
      defaultProps: {
        label: 'nodeName',
        children: 'nodeList'
      },
      treeLevel: '',
      nodeName: '',
      nodeCode: '',
      nodeType: '',
      filterText: '',
      keyWord: '',
      tableLoading: false,
      tableData: [],
      pagination: {
        current: 1,
        size: 10,
        total: 0
      }
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  mounted() {
    this.getTreeList()
  },
  methods: {
    formatName(type) {
      if (type == 0) {
        return '组织架构'
      } else if (type == 1) {
        return '设备'
      } else if (type == 2) {
        return '空间'
      } else if (type == 3) {
        return '值班考勤'
      } else if (type == 4) {
        return '业务'
      }
    },
    getTreeList() {
      this.treeLoading = true
      this.$api
        .getLibraryTreeList({})
        .then((res) => {
          if (res.code === '200') {
            this.treeList = res.data
          }
        })
        .finally(() => {
          this.treeLoading = false
        })
    },
    // 过滤节点
    filterNode(value, data) {
      if (!value) return true
      return data.nodeName.indexOf(value) !== -1
    },
    handleNodeClick(val, node) {
      this.treeLevel = node.level
      this.nodeName = val.nodeName
      this.nodeCode = val.node
      this.nodeType = this.treeLevel == '1' ? val.node : val.type
      this.getDataList()
    },
    getDataList() {
      this.tableLoading = true
      let data = {
        nodeCode: this.treeLevel == 1 ? '' : this.nodeCode,
        nodeName: this.treeLevel == 1 ? '' : this.nodeName,
        name: this.keyWord,
        page: this.pagination.current,
        pageSize: this.pagination.size,
        type: this.nodeType,
        excludeIds: this.selectedId
      }
      this.$api
        .getLibraryPageList(data)
        .then((res) => {
          this.tableLoading = false
          if (res.code == 200) {
            this.tableData = res.data ? res.data.records : []
            this.pagination.total = res.data ? res.data.total : 0
          } else if (res.message) {
            this.tableData = []
            this.pagination.total = 0
            this.$message.error(res.message)
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    paginationSizeChange(val) {
      this.pagination.size = val
      this.getDataList()
    },
    paginationCurrentChange(val) {
      this.pagination.current = val
      this.getDataList()
    },
    // 重置
    resetForm() {
      this.keyWord = ''
      this.pagination.size = 15
      this.pagination.current = 1
      this.getDataList()
    },
    // 查询
    searchForm() {
      this.pagination.current = 1
      this.getDataList()
    },
    // table隔行变色
    tableRowClassName({ row, rowIndex }) {
      if (rowIndex % 2 == 1) {
        return 'warning-row'
      } else {
        return 'success-row'
      }
    },
    handleSelectionChange(val) {
      let obj = cloneDeep(val)
      obj.forEach((el) => {
        el.indicatorLibraryId = el.id
        delete el.id
      })
      this.$emit('choose', obj)
    }
  }
}
</script>
<style lang="scss" scoped>
.dialogContent {
  width: 100%;
  display: flex;
  justify-content: space-between;
  .leftTree {
    background-color: #fff;
    padding: 16px;
    width: 250px;
    height: 425px;
    .search {
      margin-bottom: 16px;
    }
    :deep(.el-tree) {
      height: calc(100% - 48px);
      overflow: auto;
    }
  }
  .rightTable {
    background-color: #fff;
    width: calc(100% - 268px);
    height: 425px;
    padding: 16px;
    .topFilter {
      width: 100%;
      margin-bottom: 16px;
      display: flex;
      justify-content: space-between;
      :deep(.el-input) {
        width: 200px;
      }
    }
    .tableContainer {
      height: calc(100% - 48px);
      .el-table {
        height: calc(100% - 48px);
        margin-bottom: 16px;
      }
    }
  }
}
</style>
