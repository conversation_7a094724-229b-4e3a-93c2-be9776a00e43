<template>
  <PageContainer class="positionManage-list">
    <template #content>
      <div class="add-post-sop-container">
        <div class="page-header">
          <div class="back-btn" @click="goBack">
            <i class="el-icon-arrow-left"></i>
            <span>SOP详情</span>
          </div>
        </div>
        <div class="detail-content">
          <el-form :model="sopDetail" label-width="100px" class="add-post-sop-form">
            <el-row class="first-row">
              <el-col :span="11">
                <el-form-item label="SOP名称">
                  <div class="form-content">{{ sopDetail.name || '--' }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="11" :offset="2">
                <el-form-item label="标题">
                  <div class="form-content">{{ sopDetail.title || '--' }}</div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="应用岗位">
                  <div class="form-content position-container">
                    <div v-for="(name, index) in positionNameList" :key="`position-${index}`" class="position-item">
                      {{ name }}
                    </div>
                    <span v-if="positionNameList.length === 0" class="no-data">--</span>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="应用值班岗">
                  <div class="form-content position-container">
                    <div v-for="(name, index) in dutyPositionNameList" :key="`duty-${index}`" class="position-item">
                      {{ name }}
                    </div>
                    <span v-if="dutyPositionNameList.length === 0" class="no-data">--</span>
                  </div>
                  <div class="tip-text">※值班岗的SOP会优先于岗位的SOP显示</div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="备注">
              <div class="form-content remark-content">
                {{ sopDetail.remark || '--' }}
              </div>
            </el-form-item>
            <el-form-item label="内容">
              <div class="form-content content-wrapper">
                <div v-if="sopDetail.content" ref="contentDisplay" class="content-display" v-html="sopDetail.content"></div>
                <div v-else>--</div>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </template>
  </PageContainer>
</template>
<script>
export default {
  name: 'PostSOPDetail',
  components: {},
  data() {
    return {
      // SOP详情
      sopDetail: {
        id: undefined,
        name: '',
        title: '',
        positionId: '',
        positionName: '',
        dutyPositionId: '',
        dutyPositionName: '',
        createBy: '',
        createTime: '',
        remark: '',
        status: '1',
        content: ''
      },
      positionNameList: [],
      dutyPositionNameList: [],
      // 预览相关
      previewVisible: false,
      currentMediaType: 'image', // 'image' 或 'video'
      currentMediaUrl: ''
    }
  },
  created() {
    const id = this.$route.query.id
    this.getSopDetail(id)
  },
  mounted() {
    this.$nextTick(() => {
      this.addMediaClickEvents()
    })
  },
  updated() {
    this.$nextTick(() => {
      this.addMediaClickEvents()
    })
  },
  methods: {
    // 获取SOP详情
    getSopDetail(id) {
      // 调用API获取数据
      this.$api.supplierAssess
        .getSupPostSopDetail({ id: id })
        .then((res) => {
          if (res.code === '200' && res.data) {
            const detail = res.data
            this.sopDetail = {
              id: detail.id,
              name: detail.sopName || '',
              title: detail.sopTitle || '',
              positionId: detail.postId || '',
              positionName: detail.postName || '',
              dutyPositionId: detail.dutyPostId || '',
              dutyPositionName: detail.dutyPostName || '',
              createBy: detail.createBy || '',
              createTime: detail.createTime || '',
              remark: detail.remark || '',
              status: detail.status || '1',
              content: detail.content || ''
            }
            // 处理岗位和值班岗名称列表
            if (this.sopDetail.positionName) {
              this.positionNameList = this.sopDetail.positionName.split(',').filter((item) => item.trim() !== '')
            }
            if (this.sopDetail.dutyPositionName) {
              this.dutyPositionNameList = this.sopDetail.dutyPositionName.split(',').filter((item) => item.trim() !== '')
            }
            // 内容加载完毕后，添加点击事件
            this.$nextTick(() => {
              this.addMediaClickEvents()
            })
          } else {
            this.$message.error(res.msg || '获取SOP详情失败')
          }
        })
        .catch((err) => {
          console.error('获取SOP详情失败', err)
          this.$message.error('获取SOP详情失败')
        })
    },
    // 为内容中的图片和视频添加点击事件
    addMediaClickEvents() {
      if (!this.$refs.contentDisplay) return
      // 为图片添加点击事件
      const images = this.$refs.contentDisplay.querySelectorAll('img')
      images.forEach((img) => {
        img.style.cursor = 'pointer'
        // 设置图片尺寸限制
        this.adjustImageSize(img)
        img.addEventListener('click', () => {
          this.previewMedia('image', img.src)
        })
      })
      // 为视频添加点击事件
      const videos = this.$refs.contentDisplay.querySelectorAll('video')
      videos.forEach((video) => {
        // 设置视频宽度为100%以适应容器
        video.style.width = '100%'
        // 创建视频容器，如果视频不在容器中
        if (!video.parentNode.classList.contains('video-container')) {
          const container = document.createElement('div')
          container.className = 'video-container'
          // 将视频放入容器
          video.parentNode.insertBefore(container, video)
          container.appendChild(video)
          // 创建一个播放按钮覆盖层
          const overlay = document.createElement('div')
          overlay.className = 'video-overlay'
          overlay.innerHTML = '<i class="el-icon-video-play"></i>'
          container.appendChild(overlay)
          // 为覆盖层添加点击事件
          overlay.addEventListener('click', () => {
            this.previewMedia('video', video.src)
          })
        }
      })
    },
    // 调整图片大小
    adjustImageSize(img) {
      // 当图片加载完成后设置合适的大小
      img.onload = function () {
        const maxWidth = 800
        const maxHeight = 400
        if (img.naturalWidth > maxWidth || img.naturalHeight > maxHeight) {
          // 计算宽高比
          const ratio = Math.min(maxWidth / img.naturalWidth, maxHeight / img.naturalHeight)
          // 设置合适的尺寸
          img.style.width = Math.floor(img.naturalWidth * ratio) + 'px'
          img.style.height = Math.floor(img.naturalHeight * ratio) + 'px'
        }
      }
      // 如果图片已经加载完成，手动触发onload事件
      if (img.complete) {
        img.onload()
      }
    },
    // 预览媒体
    previewMedia(type, url) {
      this.currentMediaType = type
      this.currentMediaUrl = url
      this.previewVisible = true
    },
    // 编辑
    handleEdit() {
      this.$router.push({
        path: '/postManage/postSOP/addPostSOP',
        query: { id: this.sopDetail.id, type: 'edit' }
      })
    },
    // 打印
    handlePrint() {
      this.$alert('正在准备打印...', '提示', {
        confirmButtonText: '确定',
        callback: () => {
          window.print()
        }
      })
    },
    // 返回
    goBack() {
      this.$router.push('/postManage/postSOP')
    },
    // 导出
    handleExport() {
      this.$alert('正在准备导出...', '提示', {
        confirmButtonText: '确定',
        callback: () => {
          // 这里可以实现实际的导出逻辑
          this.$message({
            message: '导出功能开发中，敬请期待',
            type: 'warning'
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.positionManage-list {
  background-color: #fff;
  padding: 15px;
  .add-post-sop-container {
    height: 100%;
    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 0 0 10px 0;
      font-size: 16px;
      padding: 10px 0;
      border-bottom: 1px solid #eee;
      .back-btn {
        display: flex;
        align-items: center;
        cursor: pointer;
        i {
          margin-right: 5px;
          font-size: 18px;
        }
        span {
          font-size: 16px;
          font-weight: 500;
        }
      }
      .operation-btns {
        display: flex;
        align-items: center;
        .el-button {
          margin-left: 10px;
        }
      }
    }
  }
  .detail-content {
    overflow: auto;
    margin-top: 10px;
    height: calc(100% - 55px);
  }
  .add-post-sop-form {
    margin-top: 10px;
    height: calc(100% - 95px);
    .form-content {
      color: #333;
      padding: 0 10px;
      min-height: 32px;
      border-radius: 4px;
      display: flex;
      align-items: center;
    }
    .el-form-item {
      margin-bottom: 18px;
    }
  }
  .first-row {
    margin-bottom: 0;
    .el-form-item {
      margin-bottom: 18px;
    }
    .el-form-item__content {
      line-height: 32px;
    }
  }
  .simple-text-container {
    padding: 5px 0;
    display: flex;
    flex-wrap: wrap;
    .no-data {
      color: #909399;
      font-size: 14px;
      line-height: 32px;
    }
  }
  .position-container {
    flex-wrap: wrap;
    .position-item {
      margin-right: 15px;
      margin-bottom: 5px;
    }
    .no-data {
      color: #909399;
      font-size: 14px;
    }
  }
  .remark-content {
    white-space: pre-wrap;
    word-break: break-all;
    min-height: 32px;
    align-items: flex-start !important;
    line-height: 1.5 !important;
    padding: 10px !important;
    padding-left: 0 !important;
  }
  .content-wrapper {
    display: block !important;
    padding: 0 !important;
    background-color: transparent !important;
    min-height: 200px;
  }
  .content-display {
    padding: 10px;
    // background-color: #f8f8f8;
    border-radius: 4px;
    min-height: 200px;
    line-height: 1.5;
    max-height: 600px; // 限制最大高度，避免内容太多占满屏幕
    ::v-deep {
      p {
        // margin: 8px 0;
      }
      img {
        width: auto !important;
        max-width: 100%;
        height: auto;
        object-fit: contain;
        cursor: pointer;
        transition: all 0.3s;
        border-radius: 4px;
        max-height: 200px;
        &:hover {
          opacity: 0.9;
          box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
        }
      }
      .video-container {
        position: relative;
        margin: 10px 0;
        display: block;
        max-width: 100%;
        width: 100%;
        max-height: 400px;
        margin-left: auto;
        margin-right: auto;
      }
      video {
        max-width: 100%;
        height: auto;
        border-radius: 4px;
        max-height: 400px;
        display: block;
        margin: 0 auto;
      }
      .video-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(0, 0, 0, 0.3);
        color: #fff;
        font-size: 48px;
        cursor: pointer;
        border-radius: 4px;
        transition: all 0.3s;
        &:hover {
          background-color: rgba(0, 0, 0, 0.5);
        }
        i {
          font-size: 48px;
          color: #fff;
          filter: drop-shadow(0 0 2px rgba(0, 0, 0, 0.5));
        }
      }
      // 添加对表格的样式支持
      table {
        width: 100%;
        border-collapse: collapse;
        margin: 10px 0;
        th,
        td {
          border: 1px solid #dcdfe6;
          padding: 8px;
          text-align: left;
        }
        th {
          background-color: #f5f7fa;
        }
      }
      // 添加对列表的样式支持
      ul,
      ol {
        padding-left: 20px;
        margin: 8px 0;
      }
      // 添加对引用的样式支持
      blockquote {
        border-left: 4px solid #dcdfe6;
        padding: 0 15px;
        margin: 10px 0;
        color: #606266;
      }
    }
  }
  .tip-text {
    color: #909399;
    font-size: 12px;
    margin-top: 5px;
  }
  .section-title {
    margin: 20px 0 10px;
    color: #303133;
    font-weight: bold;
  }
  .process-timeline {
    margin-top: 20px;
    .process-card {
      .process-title {
        font-weight: bold;
        margin-bottom: 10px;
        color: #409eff;
      }
      .process-content {
        pre {
          white-space: pre-wrap;
          word-wrap: break-word;
          margin: 0;
          font-family: inherit;
        }
      }
      .process-note {
        margin-top: 10px;
        background-color: #fff8e6;
        padding: 10px;
        border-radius: 4px;
        .note-title {
          font-weight: bold;
          margin-bottom: 5px;
          color: #e6a23c;
        }
        pre {
          white-space: pre-wrap;
          word-wrap: break-word;
          margin: 0;
          font-family: inherit;
          color: #666;
        }
      }
    }
  }
}
</style>
