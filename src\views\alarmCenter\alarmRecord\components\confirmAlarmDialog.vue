<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    title="警情确认"
    width="40%"
    :visible.sync="visible"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <div class="content" style="padding: 10px;">
      <el-form ref="ruleForm" :model="form" :rules="rules">
        <el-form-item label="警情类型" prop="textarea">
          <el-radio-group v-model="form.radio" size="mini">
            <el-radio-button :label="2">误报</el-radio-button>
            <el-radio-button :label="1">真实报警</el-radio-button>
            <el-radio-button :label="3">演练</el-radio-button>
            <el-radio-button :label="4">调试</el-radio-button>
          </el-radio-group>
          <el-input v-model.trim="form.textarea" type="textarea" :rows="4" placeholder="请输入备注" maxlength="100" show-word-limit style="width: 70%;" class="ipt"> </el-input>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="confirm('ruleForm')">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  name: 'confirmAlarmDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    dialogDetail: {
      type: Object,
      default: () => {}
    },
    item: {
      type: Object,
      default: () => {
        return {}
      }
    },
    // 判断是否是批量处理
    isBatch: {
      type: Boolean,
      default: false
    },
    batchList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      form: {
        radio: 2,
        textarea: ''
      },
      rules: {
        textarea: [{ required: true, message: '请输入备注', trigger: 'change' }]
      }
    }
  },
  mounted() {},
  methods: {
    // 确警
    alarmAffirm() {
      let alarmId = this.isBatch ? this.batchList.map(item => item.alarmId).join(',') : this.item.alarmId
      let params = {
        alarmAffirm: this.form.radio,
        remark: this.form.textarea,
        alarmId: alarmId,
        projectCode: this.item.projectCode
      }
      this.$api.AlarmAffirm(params).then((res) => {
        if (res.code == 200) {
          this.$emit('update:visible', !this.visible)
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', !this.visible)
    },
    confirm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.alarmAffirm()
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.content {
  width: 100%;
  height: 100%;
  padding: 20px !important;
  background-color: #fff !important;
}

.model-dialog {
  padding: 0 !important;
}

.ipt {
  margin-top: 15px;
  margin-left: 80px;
}

::v-deep .el-radio-group {
  .el-radio-button {
    margin-right: 8px;

    .el-radio-button__inner {
      background: #f6f5fa;
      border-radius: 4px;
      border: 1px solid #dcdfe6;
      // width: 72px;
      height: 28px;
    }
  }
}

::v-deep .el-form-item__error {
  height: 20px;
  width: 300px;
  color: #f56c6c;
  font-size: 12px;
  line-height: 20px;
  padding-top: 4px;
  position: absolute;
  left: 78px;
}

::v-deep .el-radio-button__orig-radio:checked + .el-radio-button__inner {
  background-color: #3562db !important;
  border-color: #3562db;
}

::v-deep .el-dialog__body {
  padding: 0 !important;
}
</style>
