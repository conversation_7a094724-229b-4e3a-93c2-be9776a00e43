<script>
import { UsingStatusOptions } from '@/views/operationPort/constant'
export default {
  name: 'ColumnBase',
  props: {
    visible: Boolean
  },
  events: ['update:visible', 'success', 'config'],
  data: function() {
    return {
      formModel: {
        name: '',
        code: '',
        visible: 1,
        sortNo: ''
      },
      rules: {
        name: [{ required: true, message: '请输入状态名称' }],
        code: [{ required: true, message: '请输入状态编码' }],
        sortNo: [{ required: true, message: '请输入排序号' }]
      },
      // 业务表单列表
      formList: [],
      loadingStatus: false,
      statusOptions: UsingStatusOptions
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    }
  },
  watch: {
    dialogVisible(value) {
      if (value && !this.formList.length) {
        this.$api.SporadicProject.queryProjectBusinessFormAll().then((res) => {
          if (res.code === '200') {
            this.formList = res.data
          }
        })
      }
    }
  },
  methods: {
    onDialogClosed() {
      this.$refs.formRef.resetFields()
    },
    onSubmit() {
      this.$refs.formRef.validate().catch(() => Promise.reject())
        .then(() => {
          this.loadingStatus = true
          const params = {
            name: this.formModel.name,
            code: this.formModel.code,
            state: this.formModel.visible,
            shortNo: this.formModel.sortNo
          }
          return this.$api.SporadicProject.createProjectColumn(params)
        })
        .then(res => {
          if (res.code === '200') {
            this.onSuccess(res.data.id)
          } else {
            throw res.message
          }
        })
        .catch((msg) => {
          msg && this.$message.error(msg)
        })
        .finally(() => {
          this.loadingStatus = false
        })
    },
    onSuccess(id) {
      this.$emit('success')
      this.$confirm('是否开始进行列数据配置？', '创建成功', {
        cancelButtonText: '稍后配置',
        confirmButtonText: '确定',
        type: 'success'
      })
        .then(() => {
          this.$emit('config', id)
        })
        .finally(() => {
          this.dialogVisible = false
        })
    }
  }
}
</script>
<template>
  <el-dialog v-dialogDrag class="component column-base" custom-class="model-dialog" title="新增列表列" width="750px" :close-on-press-escape="false" :close-on-click-modal="false" :visible.sync="dialogVisible" @closed="onDialogClosed">
    <el-form ref="formRef" :model="formModel" :rules="rules" label-width="95px">
      <el-row :gutter="12">
        <el-col :span="12">
          <el-form-item label="列名称" prop="name">
            <el-input v-model="formModel.name" placeholder="请输入" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="列编码" prop="code">
            <el-input v-model="formModel.code" placeholder="请输入" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="显隐状态" prop="visible">
            <el-select v-model="formModel.visible" placeholder="请选择">
              <el-option :value="0" label="隐藏"></el-option>
              <el-option :value="1" label="显示"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序" prop="sortNo">
            <el-input v-model="formModel.sortNo" placeholder="请输入" onkeyup="value=value.replace(/^0|\D/,'')"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button type="primary" plain @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="onSubmit">确认</el-button>
    </template>
  </el-dialog>
</template>
<style lang="scss">
.component.column-base {
  .el-form {
    background-color: #fff;
    padding: 10px 16px 0;
    .el-form-item {
      .el-cascader,
      .el-select {
        width: 100%;
      }
    }
  }
}
</style>
