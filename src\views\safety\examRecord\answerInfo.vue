<template>
    <PageContainer v-loading="contentLoading">
      <div slot="header" class="table-content">
        <div class="topFilter">
          <div class="backBar">
            <span style="cursor: pointer" @click="$router.go(-1)">
              <i class="el-icon-arrow-left"></i>
              答题详情
            </span>
          </div>
        </div>
        <div class="baseInfo">
          <h1>基础信息</h1>
          <div class="contenter">
            <div class="itemInfo">
              <span class="title">姓名：</span>
              <span class="value">{{formInfo.name}}</span>
            </div>
            <div class="itemInfo">
              <span class="title">联系电话：</span>
              <span class="value">{{formInfo.phone}}</span>
            </div>
            <div class="itemInfo">
              <span class="title">试卷名称：</span>
              <span class="value">{{formInfo.examName}}</span>
            </div>
            <div class="itemInfo">
              <span class="title">所属科目：</span>
              <span class="value">{{formInfo.subjectName}}</span>
            </div>
            <div class="itemInfo">
              <span class="title">试卷总分：</span>
              <span class="value">{{formInfo.score}}分</span>
            </div>
            <div class="itemInfo">
              <span class="title">考试得分：</span>
              <span class="value">{{formInfo.actualScore}}分</span>
            </div>
            <div class="itemInfo">
              <span class="title">答题用时：</span>
              <span class="value">{{formInfo.actualDuration}}分</span>
            </div>
            <div class="itemInfo">
              <span class="title">提交时间：</span>
              <span class="value">{{formInfo.submitTime}}</span>
            </div>
          </div>
        </div>
      </div>
      <div slot="content" class="courseContent">
        <div  class="question_content">
          <div
            v-for="(k, ind) in formInfo.examInfos"
            :key="k.id"
            :name="k.id"
            :class="['exercisesItem', k.isExpand ? 'expand' : '']"
            :ref="'exercisesItem' + ind"
          >
            <div class="exercisesTop">
              <div class="left">
                <div class="exercisesType">
                  {{
                    k.type == "1" ? "单选题" : k.type == "2" ? "多选题" : "判断题"
                  }}
                </div>
                <span>得分: {{ k.correct=='0'?(k.score || '0'):'0' }} 分</span>
              </div>
              <div class="right">
                <span @click="isExpandBtn(k, index)">{{
                  k.isExpand ? "折叠" : "展开"
                }}</span>
              </div>
            </div>
            <div :class="['exercisesName', k.isExpand ? '' : 'title']">
              {{ k.topic }}
            </div>
            <el-radio-group
              v-if="k.type == '1'"
              class="radio"
              v-model="k.actualAnswer"
              disabled
            >
              <el-radio :class="k.answer.indexOf(k.actualAnswer)!=-1?'corretColor':'errorColor'" v-for="(j, index) in k.options" :key="index" :label="j.id"
                >{{ j.id }}. {{ j.label }}</el-radio
              >
            </el-radio-group>
            <el-checkbox-group
              v-if="k.type == '2'"
              v-model="k.actualAnswer"
              class="radio"
              disabled
            >
              <el-checkbox
                v-for="(j, index) in k.options"
                :class="j.correct=='0'?'corretColor':'errorColor'"
                :key="index"
                :label="j.id"
                >{{ j.id }}. {{ j.label }}</el-checkbox
              >
            </el-checkbox-group>
            <el-radio-group
              v-if="k.type == '3'"
              class="radio"
              v-model="k.actualAnswer"
              disabled
            >
              <el-radio :class="k.answer.indexOf(k.actualAnswer)!=-1?'corretColor':'errorColor'" v-for="(j, index) in determineList" :key="index" :label="j.id"
                >{{ j.id }}. {{ j.label }}</el-radio
              >
            </el-radio-group>
            <p>答案：{{ k | getAnswer }}</p>
            <p>
              解析：
              {{ k.analysis }}
            </p>
            <div class="isCorrect">
              <img  v-if="k.correct=='0'" src="../../../assets/images/correct.png" alt="" />
              <img v-else src="../../../assets/images/error.png" alt="">
            </div>
          </div>
        </div>
      </div>
    </PageContainer>
  </template>
  <script>
  import moment from "moment";
  export default {
    data() {
      return {
        contentLoading: false,
        activeName: "0",
        routeInfo: "",
        moment,
        formInfo: {},
        id: "",
        recordId: '',
        determineList:[
          {
            id:'1',
            label:'正确'
          },
          {
            id:'2',
            label:'错误'
          }
        ]
      };
    },
    created() {
      this.routeInfo = JSON.parse(sessionStorage.getItem("routeInfo"));
      this.id = this.$route.query.respondent || '';
      this.recordId = this.$route.query.recordId || '';
      if (this.id) {
        this.getDetails();
      }
    },
    filters: {
      getAnswer(val){
        if(val.type=='3'){
          return val.answer=='1'?'正确':'错误'
        }else{
          return val.answer
        }
      }
    },
    methods: {
      // 获取详情
      getDetails() {
        this.contentLoading = true;
        let params = {
            recordId:this.recordId,
            studentId:this.id
        }
        this.$api.getUserExamInfo(params).then(res=>{
         if(res.data.examInfos&&res.data.examInfos.length){
          res.data.examInfos.forEach(item => {
            item.isExpand = false
            item.options = JSON.parse(item.options)
            if(item.type=='2'){
              item.actualAnswer = item.actualAnswer?item.actualAnswer.split(','):[]
              let newArr1 = []
              item.actualAnswer.forEach(k=>{
                if(item.answer.indexOf(k)!=-1){
                  newArr1.push({id:k,correct:'0'})
                }else{
                  newArr1.push({id:k,correct:'1'})
                }
              })
              newArr1.forEach(g=>{
                item.options.forEach(o=>{
                  if(g.id==o.id){
                    o.correct = g.correct
                  }
                })
              })
            }
          });
        }
          this.formInfo = res.data
        })
        this.contentLoading = false;
      },
      // 展开试题
      isExpandBtn(item, index) {
        item.isExpand = !item.isExpand;
      },
      //多选回显
      colorStatus(answer,actualAnswer,item){
        console.log(answer,'answer');
        console.log(actualAnswer,'actualAnswer');
        console.log(item,'item');
        // let userNewArr = actualAnswer.split(',')
        let newArr = answer.split(',')
        actualAnswer.forEach(item=>{
          newArr.forEach(k=>{
            if(item==k){
              return 'corretColor'
            }else{
              return 'errorColor'
            }
          })
        })
      },
    },
  };
  </script>
  <style lang="scss" scoped>
  .table-content {
    background: #fff;
    border-radius: 4px;
    height: calc(100% - 0px);
    .baseInfo {
      padding: 0 24px 24px 24px;
      .contenter {
        padding-top: 24px;
        display: flex;
        font-size: 14px;
        flex-wrap: wrap;
        .itemInfo {
          width: 50%;
          margin-bottom: 16px;
          .title {
            color: #666;
            margin-right: 46px;
          }
        }
      }
    }
  }
  .topFilter {
    padding: 15px;
    height: 60px;
    background-color: #fff;
    .backBar {
      color: #121f3e;
      height: 30px;
      border-bottom: 1px solid #dcdfe6;
    }
  }
  .paneClass {
    padding: 0 16px 16px 16px;
  }
  .courseContent {
    height: 100%;
    margin-top: 16px;
    background-color: #fff;
    padding: 16px;
    .question_content {
      height: calc(100% - 70px);
      margin-top: 16px;
      overflow: auto;
      .exercisesItem {
        height: 90px;
        overflow: hidden;
        background-color: #fff;
        margin-bottom: 16px;
        padding: 16px;
        border-bottom: 4px solid #faf9fc;
        font-size: 14px;
        position: relative;
        .exercisesTop {
          display: flex;
          justify-content: space-between;
          margin-bottom: 10px;
          .left {
            display: flex;
            align-items: center;
            justify-content: center;
            span {
              color: #7f848c;
            }
          }
          .right {
            color: #ccced3;
            display: flex;
            align-items: center;
            .line {
              width: 2px;
              height: 14px;
              margin: 0 10px 0 26px;
              background-color: #dcdfe6;
            }
            span {
              color: #3562db;
              margin-left: 16px;
              cursor: pointer;
            }
            i {
              color: #3562db;
              cursor: pointer;
              margin-left: 16px;
            }
          }
          .exercisesType {
            width: 58px;
            height: 22px;
            line-height: 22px;
            text-align: center;
            border-radius: 4px;
            color: #86909c;
            background-color: #ededf5;
            margin: 0 10px;
          }
        }
        .exercisesName {
          line-height: 20px;
          margin-bottom: 16px;
        }
        .title {
          overflow: hidden;
          -webkit-line-clamp: 2;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
        }
        .el-radio {
          margin-left: 38px;
          font-size: 14px;
          color: #7f848c !important;
          line-height: 30px;
        }
        p {
          font-size: 14px;
          color: #7f848c !important;
          line-height: 20px;
          margin-bottom: 16px;
        }
        .isCorrect {
          position: absolute;
          top: 80px;
          right: 20px;
        }
      }
      .expand {
        height: auto;
      }
    }
    .table_content {
      height: calc(100% - 70px);
      // overflow: auto;
      .top{
        display: flex;
        justify-content: space-between;
      }
      .contentiner{
        height: 100%;
      }
      .statusBtn {
        font-size: 14px;
        display: flex;
        justify-content: center;
        .auditIng {
          width: 58px;
          height: 24px;
          background-color: #fff7e8;
          border-radius: 4px;
          color: #d25f00;
        }
        .auditNo {
          width: 78px;
          height: 24px;
          background-color: #ffece8;
          border-radius: 4px;
          color: #cb2634;
          img {
            vertical-align: middle;
          }
        }
        .relwase {
          width: 58px;
          height: 24px;
          background-color: #e8ffea;
          border-radius: 4px;
          color: #009a29;
          img {
            vertical-align: middle;
          }
        }
        .inProgress{
          width: 86px;
          height: 24px;
          background-color: #E6EFFC ;
          border-radius: 4px;
          color: #2749BF;
        }
        .relwaseNo{
          width: 58px;
          height: 24px;
          background-color: #F2F4F9;
          border-radius: 4px;
          color: #86909C;   
        }
      }
      .operateBths{
        color: #3562DB;
        .el-link{
          margin-right: 8px;
        }
      }
    }
  }
  ::v-deep .el-radio {
    display: block;
    margin: 10px 0;
  }
  
  ::v-deep .el-checkbox {
    display: block;
    margin: 10px 0;
  }
  ::v-deep .el-checkbox-group {
    margin-left: 38px;
  }
::v-deep .el-radio__input.is-disabled + .el-radio__label {
  color: #5a5a5a !important;
}
 
::v-deep .corretColor .el-radio__input.is-disabled.is-checked .el-radio__inner {
  background-color: #3562db !important;
  border-color: #3562db !important;
}
::v-deep .errorColor .el-radio__input.is-disabled.is-checked .el-radio__inner {
  background-color: red !important;
  border-color: red !important;
}

::v-deep .el-checkbox__input.is-disabled + .el-checkbox__label {
  color: #808080 !important;
}

::v-deep .corretColor .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
  background-color: #3562db !important;
  border-color: #3562db !important;
}
::v-deep .errorColor .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
  background-color: red !important;
  border-color: red !important;
}
  </style>
  