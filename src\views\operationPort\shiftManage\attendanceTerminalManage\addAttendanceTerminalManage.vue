<template>
  <PageContainer :footer="true">
    <div slot="content" v-loading="formLoading" class="form-content">
      <div class="form-title" @click="$router.go(-1)"><i class="el-icon-arrow-left"></i> {{ pageTitle }}</div>
      <div class="form-body">
        <el-form ref="formInline" :model="formInline" :rules="rules" label-width="auto">
          <div v-if="queryParams.type !== 'add'" class="content-detail">
            <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
              <span class="green_line"></span>
              运行信息
            </div>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="状态">
                  <!-- 状态(0:在线 1:故障 2:离线) -->
                  <span>{{ stateLabel(formInline.state) }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="SN码">
                  <span>{{ formInline.sn }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="IP地址">
                  <span>{{ formInline.ip }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="MAC地址">
                  <span>{{ formInline.mac }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="软件版本">
                  <span>{{ formInline.versionName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="最近通信时间">
                  <span>{{ formInline.communicationTime }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="添加时间">
                  <span>{{ formInline.createdTime }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
              <span class="green_line"></span>
              基本信息
            </div>
          </div>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="设备名称" prop="name">
                <el-input v-model="formInline.name" :disabled="queryParams.type === 'detail'" placeholder="请输入设备名称" maxlength="50"> </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="绑定码" prop="bindingCode">
                <el-input v-model="formInline.bindingCode" :disabled="queryParams.type !== 'add'" placeholder="请输入绑定码" maxlength="6"> </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="16">
              <el-form-item label="位置描述" prop="spaceDesc">
                <el-input v-model="formInline.spaceDesc" :disabled="queryParams.type === 'detail'" placeholder="请输入位置描述" type="textarea" :rows="4" maxlength="50">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="16">
              <el-form-item label="备注信息" prop="remark">
                <el-input v-model="formInline.remark" :disabled="queryParams.type === 'detail'" placeholder="请输入备注信息" type="textarea" :rows="4" maxlength="500"> </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="20">
              <el-form-item label="值班考勤组">
                <TablePage ref="tablePage" border :showPage="false" :tableColumn="tableColumn" :data="tableData"> </TablePage>
                <span v-if="queryParams.type !== 'detail'" class="text-style" @click="handleTableEvent('add')"><i class="el-icon-plus"></i>添加值班考勤组</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="指纹打卡部门" prop="deptName">
                <el-input v-model="formInline.deptName" placeholder="请选择打卡部门" readonly clearable suffix-icon="el-icon-arrow-down" @focus="control"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="queryParams.type === 'detail' || queryParams.type === 'edit'" :gutter="20">
            <el-col :span="20">
              <el-form-item label="指纹列表">
                <TablePage
                  ref="tablePage"
                  border
                  :showPage="true"
                  :tableColumn="fingerDataColumn"
                  :data="fingerData"
                  :pageData="pageData"
                  :pageProps="pageProps"
                  @pagination="handlePagination"
                >
                </TablePage>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <selectShiftAttendanceDialog
        v-if="selectShiftAttendanceDialogShow"
        :visible.sync="selectShiftAttendanceDialogShow"
        :defaultChecked="defaultCheckedShiftAttendanceData"
        @submitDialog="submitSelectShiftAttendanceDialog"
        @closeDialog="() => (selectShiftAttendanceDialogShow = false)"
      />
      <!-- 打卡部门 -->
      <deptDialog v-if="isDeptDialog" :visible.sync="isDeptDialog" :title="dialogTitle" :checkedIds="checkedIds" :nature="'2'" :isNotmultiSector="true" @selectDept="selectDept" />
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">关闭</el-button>
      <el-button v-if="queryParams.type != 'detail'" type="primary" @click="submitForm()">保存</el-button>
    </div>
  </PageContainer>
</template>
<script lang="jsx">
import { attendanceGroupTypeList } from '../component/dict.js'
export default {
  name: 'addAttendanceTerminalManage',
  components: {
    selectShiftAttendanceDialog: () => import('../component/selectShiftAttendanceDialog.vue'),
    deptDialog: () => import('../component/deptDialog.vue')
  },
  async beforeRouteLeave(to, from, next) {
    if (!['attendanceTerminalManage'].includes(to.name)) {
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      formInline: {
        spaceDesc: '',
        remark: '',
        name: '',
        bindingCode: '',
        deptName: '', // 部门名称
        deptId: '' // 部门id
      },
      tableData: [],
      isDeptDialog: false, // 打卡部门弹窗
      dialogTitle: '选择部门',
      formLoading: false,
      rules: {
        name: {
          required: true,
          message: '请输入设备名称',
          trigger: 'change'
        },
        bindingCode: {
          required: true,
          message: '请输入绑定码',
          trigger: 'change'
        }
      },
      fingerData: [],
      fingerDataColumn: [
        {
          prop: 'userName',
          label: '姓名'
        },
        {
          prop: 'deptName',
          label: '组织架构'
        },
        {
          prop: 'postName',
          label: '岗位'
        },
        {
          prop: 'dutyPostName',
          label: '值班岗'
        },
        {
          prop: 'fingerprintNum',
          label: '指纹数量'
        }
      ],
      tableColumn: [
        {
          prop: 'name',
          label: '值班考勤组名称'
        },
        {
          prop: 'type',
          label: '类型',
          formatter: (scope) => {
            return scope.row.type ? attendanceGroupTypeList.find((v) => v.value == scope.row.type)?.label : ''
          }
        },
        {
          prop: 'signRuleJson',
          label: '班次'
        },
        {
          prop: 'dutyPostName',
          label: '值班岗'
        },
        {
          prop: 'personNum',
          label: '人数'
        },
        {
          prop: 'chargePersonName',
          label: '负责人'
        },
        {
          prop: 'operation',
          label: '操作',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span class="operationBtn-span operationBtn-del" onClick={() => this.handleTableEvent('del', row.row)}>
                  移除
                </span>
              </div>
            )
          }
        }
      ],
      pageData: {
        current: 1,
        size: 15,
        total: 0
      },
      pageProps: {
        page: 'current',
        pageSize: 'size',
        total: 'total'
      },
      checkedIds: '',
      queryParams: {},
      selectShiftAttendanceDialogShow: false,
      defaultCheckedShiftAttendanceData: []
    }
  },
  computed: {
    pageTitle() {
      let title = '终端'
      switch (this.queryParams.type) {
        case 'add':
          title = '绑定' + title
          break
        case 'edit':
          title = '编辑' + title
          break
        case 'detail':
          title += '详情'
          break
      }
      return title
    }
  },
  activated() {
    this.initEvent()
    const params = this.$route.query
    if (params.type != 'add') {
      this.pageData = {
        current: 1,
        size: 15,
        total: 0
      }
      this.getFingerData()
    }
  },
  mounted() {
    const params = this.$route.query
    if (params.type != 'add') {
      this.getFingerData()
    }
    if (!this.$store.state.keepAlive.list.includes('attendanceTerminalManage')) {
      this.initEvent()
    }
  },
  methods: {
    stateLabel(state) {
      console.log(state)
      switch (state) {
        case 0:
          return '在线'
        case 1:
          return '故障'
        case 2:
          return '离线'
        default:
          return ''
      }
    },
    handlePagination(pageInfo) {
      this.pageData = pageInfo
      this.getFingerData()
    },
    initEvent() {
      // 重置form表单
      const data = this.$options.data()
      delete data.rules
      delete data.tableColumn
      this.$nextTick(() => {
        Object.assign(this.$data, data)
        this.queryParams = this.$route.query
        // this.$refs.formInline.resetFields()
        const findIndex = this.tableColumn.findIndex((i) => i.prop == 'operation')
        this.tableColumn[findIndex].hasJudge = this.queryParams.type !== 'detail'
        this.queryParams.type != 'add' ? this.getTerminalDetail() : ''
        if (this.queryParams.type != 'add') {
          this.getFingerData()
        }
      })
    },
    // 获取监测项详情
    getTerminalDetail() {
      this.formLoading = true
      this.$api.supplierAssess
        .getAttendanceTerminalOne({
          id: this.$route.query.id
        })
        .then((res) => {
          this.formInline = res.data
          this.$nextTick(() => {
            this.$refs.formInline.clearValidate()
          })
          this.tableData = res.data.list
          this.formLoading = false
        })
    },
    // 打卡部门弹窗
    control() {
      this.isDeptDialog = true
      if (this.formInline.deptId) {
        this.checkedIds = this.formInline.deptId
      }
    },
    // 选择打卡部门
    selectDept(list) {
      if (list && list.length) {
        this.formInline.deptId = list.map((item) => item.id).join(',')
        this.formInline.deptName = list.map((item) => item.deptName).join(',')
        this.formInline.deptId = list.map((item) => item.id).join(',')
        this.formInline.deptName = list.map((item) => item.deptName).join(',')
      }
    },
    getFingerData() {
      let params = {
        pageSize: this.pageData.size,
        page: this.pageData.current,
        sn: this.$route.query.sn || ''
      }
      this.$api.supplierAssess.getFingerprintList(params).then((res) => {
        if (res.code == 200) {
          this.fingerData = res.data.records
          this.pageData.total = res.data.total
        }
      })
    },
    // 值班考勤组事件
    handleTableEvent(type, row) {
      if (type == 'add') {
        this.defaultCheckedShiftAttendanceData = Array.from(this.tableData, ({ id }) => id) || []
        this.selectShiftAttendanceDialogShow = true
      } else if (type == 'del') {
        const index = this.tableData.findIndex((i) => i.id == row.id)
        this.tableData.splice(index, 1)
      }
    },
    // 增加打卡终端
    addEquipment() {},
    addEquipment() {},
    submitSelectShiftAttendanceDialog(list) {
      this.tableData = [...this.tableData, ...list]
      this.selectShiftAttendanceDialogShow = false
    },
    submitForm() {
      this.formLoading = true
      this.$refs.formInline.validate((valid) => {
        if (valid) {
          const submitKey = ['name', 'bindingCode', 'spaceDesc', 'remark', 'deptId', 'deptName']
          let params = {}
          Object.keys(this.formInline).forEach((key) => {
            if (submitKey.includes(key)) {
              params[key] = this.formInline[key]
            }
          })
          params.ids = Array.from(this.tableData, ({ id }) => id)
          let apiStr = 'bandingAttendanceTerminal'
          if (this.queryParams.type == 'edit') {
            apiStr = 'updateAttendanceTerminalInfo'
            params.id = this.$route.query.id
          }
          this.$api.supplierAssess[apiStr](params).then((res) => {
            this.formLoading = false
            if (res.code == '200') {
              this.$message({
                message: '保存成功',
                type: 'success'
              })
              this.$router.go(-1)
            } else {
              this.$message({
                message: res.msg,
                type: 'error'
              })
            }
          })
        } else {
          this.formLoading = false
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.form-content {
  height: calc(100% - 0px);
  background: #fff;
  overflow: hidden;
  .form-title {
    border-bottom: 1px solid #e4e7ed;
    padding: 12px 24px;
    cursor: pointer;
    margin-bottom: 24px;
  }
  .form-body {
    padding: 0 24px;
    background: #fff;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    height: calc(100% - 100px);
    .text-style {
      cursor: pointer;
      color: #3562db;
    }
    ::v-deep(.el-table) {
      .el-table__cell {
        line-height: normal;
      }
    }
    .toptip {
      // margin-bottom: 10px;
    }
    .content-detail {
      ::v-deep(.el-form-item) {
        margin-bottom: 0px;
      }
    }
  }
}
</style>
