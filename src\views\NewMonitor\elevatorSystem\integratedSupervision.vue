<template>
    <PageContainer>
        <div slot="content" ref="contentBox" class="staging-content"
            :style="dlayout.items.length === 0 ? { display: 'block' } : { display: 'flex' }">
            <div v-if="dlayout.items.length == 0"
                style="width: 100%; height: 100%; display: flex; flex-direction: column;  align-items: center; justify-content: center; padding: 50px;">
                <img src="@/assets/images/newMonitor/no-chat.png" />
                <span style="color: #909399;">暂无数据</span>
            </div>
            <dashboard v-if="dashExampleShow" id="dashExample"
                :style="{ 'width': editExampleShow ? 'calc(100% - 220px)' : '100%' }">
                <dash-layout v-bind="dlayout" :debug="false">
                    <template v-for="(item, index) in dlayout.items">
                        <dash-item v-if="item.status == 1" v-bind.sync="dlayout.items[index]"
                            :key="item.componentName + index" @moveEnd="moveEnd" @resizeEnd="resizeEnd">
                            <component :is="item.componentName" :ref="item.componentName" :systemCode="systemCode"
                                :dataType="item.componentDataType" :deviceId="deviceId" :item="item"
                                @all-more-Oper="allMoreOper" @select-changed="handleSelectChange">
                            </component>
                        </dash-item>
                    </template>
                </dash-layout>
            </dashboard>
            <div id="editExample" :style="{ 'width': editExampleShow ? '260px' : '0' }">
                <div class="round-box">
                    <div class="editExample-content">
                        <div v-for="(item, index) in dlayout.items" :key="index"
                            :class="{ 'editExample-content-item': true, 'active-editExample-item': activeExample.includes(item.id) }"
                            @click="addStaging(item)">
                            <span>{{ item.componentTitleShow }}</span>
                        </div>
                    </div>
                    <div class="editExample-footer">
                        <el-button type="primary" plain :disabled="hasStatusFalg" @click="cancelStaging">取消</el-button>
                        <el-button type="primary" @click="saveStaging">保存</el-button>
                    </div>
                </div>
            </div>
            <!-- </scaleScreen> -->
        </div>
    </PageContainer>
</template>

<script>
import { Dashboard, DashLayout, DashItem } from 'vue-responsive-dash'
// import { stagingConfig } from '@/util/dict.js'
import Dict from '@/views/monitor/lightingMonitoring/components/dict.js'
export default {
    name: 'powerOverview',
    components: {
        Dashboard,
        DashLayout,
        DashItem,
        Dashboard,
        todayModeDetails: () => import('@/views/monitor/lightingMonitoring/components/todayModeDetailsDialog'),
        sbmc: () => import('../components/runningCondition/overviewComponents/sbmc'),
        aqts: () => import('../components/runningCondition/overviewComponents/aqts'),
        jbxx: () => import('../components/runningCondition/overviewComponents/jbxx'),
        jkhm: () => import('../components/runningCondition/overviewComponents/jkhm'),
        jcxx: () => import('../components/runningCondition/overviewComponents/jcxx'),
        bjxx: () => import('../components/runningCondition/overviewComponents/bjxx'),
        wbnjxx: () => import('../components/runningCondition/overviewComponents/wbnjxx'),
        bjlxtj: () => import('../components/runningCondition/overviewComponents/bjlxtj'),
        bjjl: () => import('../components/runningCondition/overviewComponents/bjjl'),
        yjya: () => import('../components/runningCondition/overviewComponents/yjya'),
    },
    props: {

    },
    filters: {
        stateFilter(items) {
            return items.filter((item) => item.status = 1)
        }
    },
    data() {
        return {
            detailsType: '',
            patternTypeIconList: Dict.patternTypeIconList,
            interval: null,
            tabsActive: '',
            parentBox: {},
            dlayout:
            // x=> 4 8 12 小中大
            {
                breakpoint: 'xs',
                numberOfCols: 48,
                items: [
                    // { id: 'personalInfo', x: 0, y: 0, width: 8, height: 3, dragAllowFrom: '.drag_class', resizable: true, draggable: false },
                    // { id: 'quickNavigation', x: 0, y: 3, width: 8, height: 3, dragAllowFrom: '.drag_class', resizable: true, draggable: false },
                    // { id: 'msgReminder', x: 8, y: 0, width: 8, height: 6, dragAllowFrom: '.drag_class', resizable: true, draggable: false },
                    // { id: 'todoItems', x: 16, y: 0, width: 8, height: 6, dragAllowFrom: '.drag_class', resizable: true, draggable: false },
                    // { id: 'workOderType', x: 0, y: 6, width: 8, height: 5, dragAllowFrom: '.drag_class', resizable: true, draggable: false },
                    // { id: 'warnManageTable', x: 8, y: 6, width: 16, height: 5, dragAllowFrom: '.drag_class', resizable: true, draggable: false },
                    // { id: 'logisticsService', x: 0, y: 12, width: 8, height: 5, dragAllowFrom: '.drag_class', resizable: true, draggable: false },
                    // { id: 'wasteDeliveryRecord', x: 8, y: 12, width: 8, height: 5, dragAllowFrom: '.drag_class', resizable: true, draggable: false },
                    // { id: 'equipmentPatrolTask', x: 16, y: 12, width: 8, height: 5, dragAllowFrom: '.drag_class', resizable: true, draggable: false }
                ]
            },
            reservedItems: [],
            dashExampleShow: true, // 工作台显示隐藏
            editExampleShow: false, // 编辑工作台显示隐藏
            activeExample: [], // 当前激活的工作台
            firstLevelMenuList: [],
            systemCode: this.$route.meta.systemType,
            alarmStatisticsDialog: false,
            monitorStatisticsDialog: false, // 检测项统计弹窗
            moduleMenuTreeData: [{}],
            statisticalData: {},
            deviceId: '',
        }
    },
    computed: {
        hasStatusFalg() {
            return this.dlayout.items.filter((e) => e.status === 0).length === this.dlayout.items.length
        }
    },
    mounted() {
        this.parentBox.width = this.$refs.contentBox.offsetWidth
        this.parentBox.height = this.$refs.contentBox.offsetHeight
        this.getWorktopManageList()
    },
    methods: {
        handleSelectChange(selectedValue) {
            console.log('选择的值变化:', selectedValue,);
            if (this.$refs.jbxx) {
                this.$refs.jbxx.map((item) => {
                    item.jbxxData(selectedValue)
                })
            }
            if (this.$refs.jkhm) {
                this.$refs.jkhm.map((item) => {
                    item.jkhmElevatorList(selectedValue)
                })
            }
            if (this.$refs.jcxx) {
                this.$refs.jcxx.map((item) => {
                    item.jcxxDataList(selectedValue)
                })
            }
            if (this.$refs.wbnjxx) {
                this.$refs.wbnjxx.map((item) => {
                    item.wbnjData(selectedValue)
                })
            }
            if (this.$refs.bjlxtj) {
                this.$refs.bjlxtj.map((item) => {
                    item.bjlxtjFunction(selectedValue)
                })
            }
            if (this.$refs.bjjl) {
                this.$refs.bjjl.map((item) => {
                    item.bjjlTableData(selectedValue)
                })
            }
            if (this.$refs.bjxx) {
                this.$refs.bjxx.map((item) => {
                    item.bjxxData(selectedValue)
                })
            }
            if (this.$refs.yjya) {
                this.$refs.yjya.map((item) => {
                    item.getAlarmTypeByAlarmObjectId(selectedValue)
                })
            }
        },
        // 获取工作台管理列表
        getWorktopManageList() {
            this.editExampleShow = false
            this.$store.commit('settings/dragSidebarCollapse', false)
            this.$api.getModuleMenuTree({ systemCode: this.systemCode, systemType: 2 }).then((res) => {
                if (res.code == 200) {
                    const data = res.data
                        .filter(item => item.componentName !== '-');
                    const items = []
                    data.forEach((item) => {
                        items.push({
                            id: item.id,
                            componentName: item.componentName,
                            componentTitleShow: item.componentTitleShow,
                            componentDataType: item.componentDataType,
                            x: item.x,
                            y: item.y,
                            width: item.width,
                            height: item.height,
                            dragAllowFrom: '.drag_class',
                            resizable: false,
                            draggable: false,
                            path: item.path,
                            status: item.status,
                            chartType: item.chartType || ''
                        })
                    })
                    this.dashExampleShow = false
                    this.$nextTick(() => {
                        this.dashExampleShow = true
                        this.dlayout.items = items
                        this.reservedItems = JSON.parse(JSON.stringify(items))
                        const hasStatusLength = items.filter((e) => e.status === 0).length
                        hasStatusLength == items.length ? this.allMoreOper('edit') : ''
                    })
                }
            })
        },
        // 拖拽结束事件
        moveEnd(item) {
            console.log('moveEnd', this.dlayout)
        },
        // 缩放结束事件
        resizeEnd(item) {
            const resizeList = ['workOderType']
            if (resizeList.includes(item.id)) {
                this.$refs[item.id][0].echartsResize()
            }
        },
        // 添加/减少模块
        addStaging(item) {
            if (this.activeExample.includes(item.id)) {
                this.activeExample.splice(this.activeExample.indexOf(item.id), 1)
                this.dlayout.items.map((e) => {
                    if (e.id === item.id) {
                        e.status = 0
                    }
                })
            } else {
                this.activeExample.push(item.id)
                this.dlayout.items.map((e) => {
                    if (e.id === item.id) {
                        e.status = 1
                    }
                })
            }
        },
        // 取消编辑工作台模块
        cancelStaging() {
            this.editExampleShow = false
            this.dashExampleShow = false
            this.$nextTick(() => {
                this.dashExampleShow = true
                this.dlayout.items = JSON.parse(JSON.stringify(this.reservedItems))
                this.$store.commit('settings/dragSidebarCollapse', false)
            })
        },
        // 保存工作台模块
        saveStaging() {
            const items = this.dlayout.items
            const params = []
            items.forEach((item) => {
                params.push({
                    id: item.id,
                    componentName: item.componentName,
                    componentTitleShow: item.componentTitleShow,
                    componentDataType: item.componentDataType,
                    path: item.path,
                    status: item.status,
                    x: item.x,
                    y: item.y,
                    width: item.width,
                    height: item.height,
                    chartType: item.chartType || ''
                })
            })
            this.$api.saveModuleMenuSettings({ jsonList: JSON.stringify(params), userId: this.$store.state.user.userInfo.user.staffId, menuType: 12 }).then((res) => {
                if (res.code == 200) {
                    this.$message.success('保存成功')
                    this.getWorktopManageList()
                } else {
                    this.$message.error(res.message)
                }
            })
        },
        // 更多操作事件
        allMoreOper(type, component) {
            if (type === 'more') {
                if (component == 'alarmLargeAnalysis') { // 报警分析
                    this.alarmStatisticsDialog = true
                } else if (component == 'topTen') {
                    this.detailsType = chartType
                    this.monitorStatisticsDialog = true
                }
            } else if (type === 'edit') {
                this.dlayout.items.map((e) => {
                    e.resizable = true
                    e.draggable = true
                })
                this.activeExample = this.dlayout.items.filter((e) => e.status === 1).map((e) => e.id)
                this.editExampleShow = true
                this.$store.commit('settings/toggleSidebarCollapse', true)
                this.$store.commit('settings/dragSidebarCollapse', true)
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.page-container {
    margin: 0 auto;
    height: 100%;
    width: 100%;
    // max-width: 1620px;
}

.staging-content {
    height: 100%;
    width: 100%;
    display: flex;
    justify-content: space-between;

    #dashExample {
        height: 100%;
        // background: #f5f7f9;
        overflow-y: auto;
        overflow-x: hidden;

        :deep(.placeholder) {
            background: #e2e6eb !important;
            border-radius: 10px;
            opacity: 1;
        }
    }

    #editExample {
        height: 100%;
        // background-color: #fff;
        box-shadow: 10px 0 10px -10px #c7c7c7;
        box-shadow: 10px 0 10px -10px #c7c7c7;
        padding: 16px 0;
        display: flex;
        flex-direction: column;

        .round-box {
            width: 100%;
            height: 100%;
            background-color: #08305d0a;
            border-radius: 10px;
            padding: 10px 0;
            display: flex;
            flex-direction: column;
        }

        .editExample-title {
            >div {
                font-size: 16px;
                font-family: PingFangSC-Regular-Blod;
                color: #121f3e;
                margin: 0 0 12px 15px;
                height: 22px;
                line-height: 22px;
            }

            >p {
                height: 18px;
                font-size: 12px;
                line-height: 18px;
                margin: 0 0 12px 15px;
                color: #999;
            }
        }

        .editExample-content {
            flex: 1;
            // height: calc(100% - 50px);
            overflow-y: auto;
            padding: 10px 20px;

            .editExample-content-item {
                cursor: pointer;
                width: 100%;
                box-sizing: border-box;
                height: 40px;
                line-height: 40px;
                margin-bottom: 16px;
                border-radius: 4px;
                padding: 0 10px;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
                background-color: #fff;
                text-align: center;
                color: #121f3e;
                font-size: 13px;
                font-family: "PingFang SC-Regular", "PingFang SC";

                &:hover {
                    // color: #3562db;
                    // background: rgba(53, 98, 219, 0.2);
                    box-shadow: 0 4px 12px 0 rgb(17 19 23 / 10%);
                }
            }

            .active-editExample-item {
                background-color: #3562db !important;
                color: #fff !important;
                border-color: #3562db !important;
            }
        }

        .editExample-footer {
            text-align: right;
            padding: 5px 15px;
        }
    }
}

::-webkit-scrollbar-thumb:hover,
::-webkit-scrollbar-track:hover {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background-color: transparent;
}
</style>