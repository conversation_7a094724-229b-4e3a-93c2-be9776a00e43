<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-06-06 18:13:06
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-06-13 14:16:01
 * @FilePath: \ihcrs_pc\src\views\operationPort\spaceManage\operationsManage\spaceComponent.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="">
    <template v-for="(item, index) in dlayoutItems">
      <dash-item v-if="item.id == 'personalInfo'" v-bind.sync="dlayoutItems[index]" :key="item.id" @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)">
        <ContentCard
          title="空间总览"
          :scrollbarHover="true"
          :cstyle="{ height: '100%' }"
          class="drag_class"
          :hasMoreOper="['edit']"
          @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')"
        >
          <div slot="content" style="height: 100%; display: flex; align-items: center">
            <div class="cont">
              <div class="space" style="background-color: #3562db">
                <div>
                  <div>
                    <div @click="spaceTz(' ')" style="cursor: pointer">
                      <span style="font-weight: 700; font-size: 20px">{{ spaceData.totalCount }}</span>
                      <!-- &nbsp;<span style="color: #ccced3">个</span> -->
                    </div>
                  </div>

                  <div>空间总数</div>
                </div>
                <div><img src="../../../../assets/images/operationPort/kjzs.png" alt="" /></div>
              </div>
              <div class="space" style="background-color: #00bc6d">
                <div>
                  <div>
                    <div @click="spaceTz('0')" style="cursor: pointer">
                      <span style="font-weight: 700; font-size: 20px">{{ spaceData.idleCount }}</span>
                      <!-- &nbsp;<span style="color: #ccced3">个</span> -->
                    </div>
                  </div>
                  <div>闲置空间</div>
                </div>

                <div><img src="../../../../assets/images/operationPort/xzkj.png" alt="" /></div>
              </div>
              <div class="space" style="background-color: #3562db">
                <div>
                  <div>
                    <div>
                      <span style="font-weight: 700; font-size: 20px">{{ spaceData.totalArea }}</span>
                    </div>
                  </div>
                  <div>建筑面积 ( ㎡ )</div>
                </div>

                <div><img src="../../../../assets/images/operationPort/mj.png" alt="" /></div>
              </div>
              <div class="space" style="background-color: #ff9435">
                <div>
                  <div>
                    <div>
                      <span style="font-weight: 700; font-size: 20px">{{ spaceData.useArea }}</span
                      >&nbsp;<span style="color: #ccced3"></span>
                    </div>
                  </div>
                  <div>使用面积 ( ㎡ )</div>
                </div>

                <div><img src="../../../../assets/images/operationPort/mj.png" alt="" /></div>
              </div>
              <div class="space" style="background-color: #00bc6d">
                <div>
                  <div>
                    <div>
                      <span style="font-weight: 700; font-size: 20px">{{ spaceData.publicArea }}</span>
                    </div>
                  </div>
                  <div>公区面积 ( ㎡ )</div>
                </div>
                <div><img src="../../../../assets/images/operationPort/mj.png" alt="" /></div>
              </div>
            </div>
          </div>
        </ContentCard>
      </dash-item>
      <dash-item v-if="item.id == 'msgReminder'" v-bind.sync="dlayoutItems[index]" :key="item.id" @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)">
        <ContentCard
          title="空间功能用途Top10"
          :scrollbarHover="true"
          :cstyle="{ height: '100%' }"
          class="drag_class"
          :hasMoreOper="['edit']"
          @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')"
        >
          <div id="functionalUse" slot="content" style="height: 100%"></div>
        </ContentCard>
      </dash-item>
      <dash-item v-if="item.id == 'todoItems'" v-bind.sync="dlayoutItems[index]" :key="item.id" @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)">
        <ContentCard
          title="科室空间Top10"
          :scrollbarHover="true"
          :cstyle="{ height: '100%' }"
          class="drag_class"
          :hasMoreOper="['edit']"
          @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')"
        >
          <div id="officesStateAnalysis" slot="content" style="height: 100%"></div>
        </ContentCard>
      </dash-item>
      <dash-item v-if="item.id == 'workOderType'" v-bind.sync="dlayoutItems[index]" :key="item.id" @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)">
        <ContentCard
          title="空间状态分析"
          :scrollbarHover="true"
          :cstyle="{ height: '100%' }"
          class="drag_class"
          :hasMoreOper="['edit']"
          @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')"
        >
          <div id="spatialStateAnalysis" slot="content" style="height: 100%"></div>
        </ContentCard>
      </dash-item>
      <dash-item v-if="item.id == 'warnManageTable'" v-bind.sync="dlayoutItems[index]" :key="item.id" @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)">
        <ContentCard
          title="建筑空间分布"
          :scrollbarHover="true"
          :cstyle="{ height: '100%' }"
          class="drag_class"
          :hasMoreOper="['edit']"
          @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')"
        >
          <div id="distributionBuildingSpace" slot="content" style="height: 100%"></div>
        </ContentCard>
      </dash-item>
    </template>
  </div>
</template>
<script>
import { DashItem } from 'vue-responsive-dash'
import * as echarts from 'echarts'
export default {
  name: 'spaceOperations',
  components: {
    DashItem
  },
  props: {
    dlayoutItems: {
      type: Array,
      default: () => []
    },
    componentData: {
      type: Object,
      default: () => {}
    }
  },

  data() {
    return {
      pid: '',
      spaceData: {
        idleCount: 0,
        publicArea: 0,
        totalArea: 0,
        totalCount: 0,
        useArea: 0
      },
      spaceData2: {
        idleCount: 0,
        publicArea: 0,
        totalArea: 0,
        totalCount: 0,
        useArea: 0
      },
      modelCode: '',
      spaceStatusList: [],
      seriesData: [],
      xAxisData: [],
      seriesDatas: [],
      xAxisDatas: [],
      seriesData2: [],
      xAxisData2: [],
      // items: [
      //   { id: 'personalInfo', x: 0, y: 0, width: 8, height: 6, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
      //   { id: 'msgReminder', x: 8, y: 0, width: 8, height: 6, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
      //   { id: 'todoItems', x: 16, y: 0, width: 8, height: 6, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
      //   { id: 'workOderType', x: 0, y: 6, width: 8, height: 6, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
      //   { id: 'warnManageTable', x: 8, y: 6, width: 16, height: 6, dragAllowFrom: '.drag_class', resizable: true, draggable: true }
      // ],
      echartsDom: ['spatialStateAnalysis', 'functionalUse', 'officesStateAnalysis', 'distributionBuildingSpace']
    }
  },
  watch: {
    '$store.state.settings.sidebarCollapse': {
      handler(val) {
        this.$nextTick(() => {
          let echartsDom = this.echartsDom
          setTimeout(() => {
            echartsDom.forEach((item) => {
              echarts.init(document.getElementById(item)).resize()
            })
          }, 300)
        })
      },
      deep: true
    }
  },
  mounted() {
    setTimeout(() => {
      this.spaceTreeListFn()
      this.getSpaceMessageList()
      this.getSpaceMessageLists()
    }, 300)
  },
  methods: {
    echartsResize() {
      let echartsDom = this.echartsDom
      this.$nextTick(() => {
        setTimeout(() => {
          echartsDom.forEach((item) => {
            echarts.init(document.getElementById(item)).resize()
          })
        }, 300)
      })
    },
    spaceTz(val) {
      this.$router.push({
        path: '/spaceOperationsList',
        query: {
          status: val
        }
      })
    },
    getSpaceMessageLists() {
      const params = {
        modelCode: this.modelCode,
        queryType: 'dept',
        descColumn: 'roomAllCount,dataName',
        current: 1,
        size: 10,
        haveModel: '0'
      }
      this.$api.getRoomCountAndAreaPageList(params).then((res) => {
        if (res.code == 200) {
          res.data.records.forEach((item) => {
            if (item.dataName == null) {
              item.dataName = '未知'
            }
            this.seriesDatas.push(item.roomAllCount)
            this.xAxisDatas.push(item.dataName)
            this.$nextTick(() => {
              this.getPowerMonthTypeNoEcharts()
            })
          })
        }
      })
    },
    getPowerMonthTypeNoEcharts() {
      const getchart = echarts.init(document.getElementById('officesStateAnalysis'))
      let arr = []
      this.xAxisDatas.forEach((item) => {
        let obj = {
          name: item
        }
        arr.push(obj)
      })
      this.seriesDatas.forEach((item, i) => {
        arr.forEach((item2, n) => {
          if (i == n) {
            item2.value = item
          }
        })
      })
      const data = arr
      data.sort((a, b) => {
        return a.value + b.value
      })
      const nameList = Array.from(data, ({ name }) => name)
      const option = {
        tooltip: {
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          top: '4%',
          left: '-70',
          right: '5%',
          bottom: '5%',
          containLabel: true
        },
        yAxis: {
          type: 'category',
          data: nameList,
          inverse: true,
          splitLine: {
            show: false // 去掉辅助线
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            interval: 0, // 横轴信息全部显示
            // rotate: -30, // -30度角倾斜显示
            formatter: function (val, index) {
              var sort = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
              // var sort = [10, 9, 8, 7, 6, 5, 4, 3, 2, 1]
              var strs = val.split('') // 字符串数组
              var str = ''
              // strs循环 forEach 每五个字符增加换行符
              // strs.forEach((item, index) => {
              //   if (index % 12 === 0 && index !== 0) {
              //     str += '\n'
              //   }
              //   str += item
              // })
              if (val.length > 5) {
                str = val.substring(0, 4) + '..'
              } else {
                str = val
              }
              // return str
              // return [`${sort[index]} ${str}`]
              return [`{a${sort[index]}|${sort[index]}}    ${str}`]
            },
            margin: 120,
            align: 'left', // 文字左排序
            rich: {
              a1: {
                color: '#F53F3F',
                backgroundColor: '#FFECE8',
                width: 24,
                height: 17,
                align: 'center',
                borderRadius: 4
              },
              a2: {
                color: '#F53F3F',
                backgroundColor: '#FFECE8',
                width: 24,
                height: 17,
                align: 'center',
                borderRadius: 4
              },
              a3: {
                color: '#F53F3F',
                backgroundColor: '#FFECE8',
                width: 24,
                height: 17,
                align: 'center',
                borderRadius: 4
              },
              a4: {
                color: '#4E5969',
                backgroundColor: '#F2F3F5',
                width: 24,
                height: 17,
                align: 'center',
                borderRadius: 4
              },
              a5: {
                color: '#4E5969',
                backgroundColor: '#F2F3F5',
                width: 24,
                height: 17,
                align: 'center',
                borderRadius: 4
              },
              a6: {
                color: '#4E5969',
                backgroundColor: '#F2F3F5',
                width: 24,
                height: 17,
                align: 'center',
                borderRadius: 4
              },
              a7: {
                color: '#4E5969',
                backgroundColor: '#F2F3F5',
                width: 24,
                height: 17,
                align: 'center',
                borderRadius: 4
              },
              a8: {
                color: '#4E5969',
                backgroundColor: '#F2F3F5',
                width: 24,
                height: 17,
                align: 'center',
                borderRadius: 4
              },
              a9: {
                color: '#4E5969',
                backgroundColor: '#F2F3F5',
                width: 24,
                height: 17,
                align: 'center',
                borderRadius: 4
              },
              a10: {
                color: '#4E5969',
                backgroundColor: '#F2F3F5',
                width: 24,
                height: 17,
                align: 'center',
                borderRadius: 4
              }
            }
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        xAxis: [
          {
            type: 'value',
            splitLine: {
              show: false //去掉辅助线
            },
            axisLine: {
              show: false
            },
            axisLabel: {
              show: false
            }
          }
        ],
        series: [
          {
            type: 'bar',
            barWidth: 8,
            data: data,
            // itemStyle: {
            //   color: '#3562db'
            // }
            showBackground: true,
            backgroundStyle: {
              color: '#e4e7ed',
              borderRadius: 5
            },
            itemStyle: {
              color: function (params) {
                var nameList = ['#3562db', '#ff9435', '#ff6461', '#00bc6d', '#3562db', '#ff9435', '#ff6461', '#00bc6d', '#3562db', '#ff9435']
                return nameList[params.dataIndex]
              },
              barBorderRadius: [5, 5, 5, 5]
            },
            label: {
              normal: {
                show: true,
                position: 'right', //value的位置
                color: '#898989', //值的颜色
                fontSize: 12
              }
            }
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      getchart.resize()
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    getSpaceMessageList() {
      const params = {
        modelCode: this.modelCode,
        queryType: 'function',
        descColumn: 'roomAllCount,dataName',
        current: 1,
        size: 10,
        haveModel: '0'
      }
      this.$api.getRoomCountAndAreaPageList(params).then((res) => {
        if (res.code == 200) {
          res.data.records.forEach((item) => {
            this.seriesData.push(item.roomAllCount)
            this.xAxisData.push(item.dataName)

            this.$nextTick(() => {
              this.getPowerMonthTypeNoEchart()
            })
          })
        }
      })
    },
    getPowerMonthTypeNoEchart() {
      const getchart = echarts.init(document.getElementById('functionalUse'))
      let arr = []
      this.xAxisData.forEach((item) => {
        let obj = {
          name: item || '未知'
        }
        arr.push(obj)
      })
      this.seriesData.forEach((item, i) => {
        arr.forEach((item2, n) => {
          if (i == n) {
            item2.value = item
          }
        })
      })
      const data = arr
      data.sort((a, b) => {
        return a.value + b.value
      })
      const nameList = Array.from(data, ({ name }) => name)
      const option = {
        tooltip: {
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          top: '4%',
          left: '-70',
          right: '5%',
          bottom: '5%',
          containLabel: true
        },
        yAxis: {
          type: 'category',
          data: nameList,
          inverse: true,
          splitLine: {
            show: false // 去掉辅助线
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            interval: 0, // 横轴信息全部显示
            // rotate: -30, // -30度角倾斜显示
            formatter: function (val, index) {
              var sort = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
              // var sort = [10, 9, 8, 7, 6, 5, 4, 3, 2, 1]
              var strs = val.split('') // 字符串数组
              var str = ''
              // strs循环 forEach 每五个字符增加换行符
              // strs.forEach((item, index) => {
              //   if (index % 8 === 0 && index !== 0) {
              //     str += '\n'
              //   }
              //   str += item
              // })
              if (val.length > 5) {
                str = val.substring(0, 4) + '..'
              } else {
                str = val
              }
              // return str
              return [`{a${sort[index]}|${sort[index]}}    ${str}`]
            },
            margin: 120,
            align: 'left', // 文字左排序
            rich: {
              a1: {
                color: '#F53F3F',
                backgroundColor: '#FFECE8',
                width: 24,
                height: 17,
                align: 'center',
                borderRadius: 4
              },
              a2: {
                color: '#F53F3F',
                backgroundColor: '#FFECE8',
                width: 24,
                height: 17,
                align: 'center',
                borderRadius: 4
              },
              a3: {
                color: '#F53F3F',
                backgroundColor: '#FFECE8',
                width: 24,
                height: 17,
                align: 'center',
                borderRadius: 4
              },
              a4: {
                color: '#4E5969',
                backgroundColor: '#F2F3F5',
                width: 24,
                height: 17,
                align: 'center',
                borderRadius: 4
              },
              a5: {
                color: '#4E5969',
                backgroundColor: '#F2F3F5',
                width: 24,
                height: 17,
                align: 'center',
                borderRadius: 4
              },
              a6: {
                color: '#4E5969',
                backgroundColor: '#F2F3F5',
                width: 24,
                height: 17,
                align: 'center',
                borderRadius: 4
              },
              a7: {
                color: '#4E5969',
                backgroundColor: '#F2F3F5',
                width: 24,
                height: 17,
                align: 'center',
                borderRadius: 4
              },
              a8: {
                color: '#4E5969',
                backgroundColor: '#F2F3F5',
                width: 24,
                height: 17,
                align: 'center',
                borderRadius: 4
              },
              a9: {
                color: '#4E5969',
                backgroundColor: '#F2F3F5',
                width: 24,
                height: 17,
                align: 'center',
                borderRadius: 4
              },
              a10: {
                color: '#4E5969',
                backgroundColor: '#F2F3F5',
                width: 24,
                height: 17,
                align: 'center',
                borderRadius: 4
              }
            }
          }
        },
        xAxis: [
          {
            type: 'value',
            splitLine: {
              show: false // 去掉辅助线
            },
            axisLine: {
              show: false
            },
            axisLabel: {
              show: false
            }
          }
        ],
        series: [
          {
            type: 'bar',
            barWidth: 8,
            data: data,
            // itemStyle: {
            //   color: '#3562db'
            // }
            showBackground: true,
            backgroundStyle: {
              color: '#e4e7ed',
              borderRadius: 5
            },
            itemStyle: {
              color: function (params) {
                var nameList = ['#3562db', '#ff9435', '#ff6461', '#00bc6d', '#3562db', '#ff9435', '#ff6461', '#00bc6d', '#3562db', '#ff9435']
                return nameList[params.dataIndex]
              },
              barBorderRadius: [5, 5, 5, 5]
            },
            label: {
              normal: {
                show: true,
                position: 'right', //value的位置
                color: '#898989', //值的颜色
                fontSize: 12
              }
            }
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      getchart.resize()
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // getRoomCountAndArea2() {
    //   const params = {
    //     modelCode: 'BJSJTYY01001',
    //     haveModel: '0'
    //   }
    //   this.$api.getRoomCountAndArea(params).then((res) => {
    //     if (res.code === 200) {
    //       Object.assign(this.spaceData2, res.data)
    //       // 计算公共区域面积 = 建筑面积 - 使用面积
    //       this.spaceData2.publicArea = this.spaceData2.publicArea ? this.spaceData2.publicArea : this.spaceData2.totalArea - this.spaceData2.useArea
    //       this.seriesData2.push(this.spaceData2.totalCount)
    //       this.$nextTick(() => {
    //         this.getPowerMonthTypeNoEchart2()
    //       })
    //     }
    //   })
    // },
    getRoomCountAndArea2() {
      const params = {
        pid: this.pid,
        haveModel: '0'
      }

      this.$api.getRoomCountByPid(params).then((res) => {
        if (res.code === 200) {
          res.data.forEach((item) => {
            // if (__PATH.VUE_APP_HOSPITAL_NODE_ENV == 'bjsjtyy') {
            // if (item.ssmName == '综合急诊急救楼') {
            // this.xAxisData2.push(item.ssmName)
            // this.seriesData2.push(item.modelRoomCount)
            // }
            // } else {
            this.xAxisData2.push(item.ssmName)
            this.seriesData2.push(item.modelRoomCount)
            // }
          })

          this.$nextTick(() => {
            this.getPowerMonthTypeNoEchart2()
          })
        }
      })
    },
    getPowerMonthTypeNoEchart2() {
      const getchart = echarts.init(document.getElementById('distributionBuildingSpace'))
      let arr = []
      this.xAxisData2.forEach((item) => {
        let obj = {
          name: item
        }
        arr.push(obj)
      })
      this.seriesData2.forEach((item, i) => {
        arr.forEach((item2, n) => {
          if (i == n) {
            item2.value = item
          }
        })
      })
      const data = arr
      const nameList = Array.from(data, ({ name }) => name)
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          top: '4%',
          left: '4%',
          right: '5%',
          bottom: '5%',
          containLabel: true
        },
        dataZoom: [
          {
            type: 'slider',
            realtime: true,
            startValue: 0,
            endValue: 5,
            height: 4,
            fillerColor: 'rgba(17, 100, 210, 0.42)', // 滚动条颜色
            borderColor: 'rgba(17, 100, 210, 0.12)',
            handleSize: 0, // 两边手柄尺寸
            showDetail: false, // 拖拽时是否展示滚动条两侧的文字
            top: '96%',
            zoomLock: true // 是否只平移不缩放
            // moveOnMouseMove:true, //鼠标移动能触发数据窗口平移
            // zoomOnMouseWheel :true, //鼠标移动能触发数据窗口缩放
          },
          {
            type: 'inside', // 支持内部鼠标滚动平移
            start: 0,
            end: 40,
            zoomOnMouseWheel: false, // 关闭滚轮缩放
            moveOnMouseWheel: true, // 开启滚轮平移
            moveOnMouseMove: true // 鼠标移动能触发数据窗口平移
          }
        ],
        xAxis: {
          type: 'category',
          data: nameList,
          nameLocation: 'start',

          axisLabel: {
            interval: 0, // 横轴信息全部显示
            axisLabel: {
              interval: 0,
              formatter(val) {
                if (val.length > 5) {
                  return val.slice(0, 4) + '...'
                } else {
                  return val
                }
              }
            }
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        yAxis: [
          {
            type: 'value'
            // axisLabel: { formatter: '{value} 个' }
          }
        ],
        series: [
          {
            type: 'bar',
            barWidth: 10,
            data: data,
            itemStyle: {
              // color: '#00BC6D'
              color: function (params) {
                var nameList = ['#3562db', '#ff9435', '#ff6461', '#00bc6d']
                if (params.dataIndex % 2 == 0) {
                  return nameList[0]
                } else if (params.dataIndex % 3 == 0) {
                  return nameList[1]
                } else {
                  return nameList[3]
                }
              },
              barBorderRadius: [5, 5, 5, 5]
            }
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      getchart.resize()
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    getRoomCountAndArea() {
      const params = {
        modelCode: this.modelCode,
        haveModel: '0'
      }
      this.$api.getRoomCountAndArea(params).then((res) => {
        if (res.code === 200) {
          Object.assign(this.spaceData, res.data)
          // 计算公共区域面积 = 建筑面积 - 使用面积
          this.spaceData.publicArea = this.spaceData.publicArea ? this.spaceData.publicArea : this.spaceData.totalArea - this.spaceData.useArea
          let statusArr = []
          statusArr.push({
            value: this.spaceData.totalCount - this.spaceData.idleCount,
            name: '使用中',
            rate: this.getPercent(this.spaceData.totalCount - this.spaceData.idleCount, this.spaceData.totalCount)
          })
          statusArr.push({ value: this.spaceData.idleCount, name: '闲置', rate: this.getPercent(this.spaceData.idleCount, this.spaceData.totalCount) })

          this.spaceStatusList = statusArr
          this.$nextTick(() => {
            this.getSpaceStatus()
          })
        }
      })
    },
    getSpaceStatus() {
      const nameList = Array.from(this.spaceStatusList, (item) => item.name)
      const getchart = echarts.init(document.getElementById('spatialStateAnalysis'))
      const option = {
        color: ['#0378f1', '#08cb83'],
        tooltip: {
          trigger: 'item',
          show: false
        },
        legend: {
          orient: 'vertical',
          type: 'scroll',
          right: '5%',
          top: '38%',
          // bottom: 20,
          itemWidth: 15,
          itemHeight: 15,
          itemGap: 15,
          data: nameList,
          formatter: function (name) {
            var oa = option.series[0].data
            var num = oa.reduce((sum, e) => sum + e.value, 0)
            for (var i = 0; i < option.series[0].data.length; i++) {
              if (name === oa[i].name) {
                return ' ' + name + '  ' + oa[i].rate + '  ' + oa[i].value + '间'
              }
            }
          }
        },
        series: [
          {
            type: 'pie',
            roseType: 'radius',
            radius: ['60%', '80%'],
            center: ['30%', '50%'],
            data: this.spaceStatusList,
            label: {
              normal: {
                // show: false,
                // position: 'center'
                show: true,
                position: 'center',
                fontSize: 15,
                color: '#000',
                width: 85,
                formatter: function (name) {
                  var oa = option.series[0].data
                  for (var i = 0; i < option.series[0].data.length; i++) {
                    if (name.name === oa[i].name) {
                      return ' ' + name.name + ' \n ' + oa[i].rate
                    }
                  }
                },
                rich: {
                  time: {
                    fontSize: 15,
                    color: '#666',
                    lineHeight: 20
                  }
                }
              }
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 15,
                color: '#000',
                backgroundColor: '#fff',
                width: 85,
                formatter: function (name) {
                  var oa = option.series[0].data
                  for (var i = 0; i < option.series[0].data.length; i++) {
                    if (name.name === oa[i].name) {
                      return ' ' + name.name + ' \n ' + oa[i].rate
                    }
                  }
                },
                rich: {
                  time: {
                    fontSize: 15,
                    color: '#666',
                    lineHeight: 20
                  }
                }
              }
            },
            labelLine: {
              show: false
            }
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      getchart.resize()
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    spaceTreeListFn() {
      this.$api.spaceTreeList().then((res) => {
        if (res.code == 200) {
          let arr = []
          // this.spaces = res.data
          // this.treeData = transData(res.data, 'id', 'pid', 'list')
          // let treeLtst = JSON.parse(localStorage.getItem('treeRow')
          res.data.forEach((i) => {
            if (i.ssmType == '1') {
              this.modelCode = i.modelCode
            } else if (i.ssmType == '2') {
              arr.push(i.id)
            }
          })
          this.pid = arr.toString()
          this.getRoomCountAndArea()
          this.getRoomCountAndArea2()
        }
      })
    },
    // 算百分比
    getPercent(num, total) {
      num = parseFloat(num)
      total = parseFloat(total)
      if (isNaN(num) || isNaN(total)) {
        return '-'
      }
      return total <= 0 ? '0%' : Math.round((num / total) * 10000) / 100.0 + '%'
    }
  }
}
</script>
<style lang="scss" scoped>
.cont {
  width: 100%;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: space-between;

  .space {
    // margin-top: 2%;
    // background-color: #faf9fc;
    color: #fff;
    width: 19%;
    height: 80px;
    padding: 0 15px;
    display: flex;
    align-items: center;
    // flex-direction: column;
    border-radius: 5px;
    flex-wrap: wrap;
    justify-content: space-between;

    > div:nth-child(1) {
      height: 70%;
    }

    > div:nth-child(2) {
      display: flex;
      align-items: center;
      justify-content: space-between;

      img {
        width: 40px;
        height: 100%;
      }
    }
  }
}
</style>
