<!--
 * @Description:
-->
<template>
  <PageContainer class="project-config">
    <template #content>
      <div class="project-config__left">
        <el-tree :data="menuData" node-key="id" :current-node-key="currentKey" highlight-current @node-click="onTreeNodeClick"></el-tree>
      </div>
      <component :is="currentKey" class="project-config__right" />
    </template>
  </PageContainer>
</template>
<script>
export default {
  name: 'ProjectConfig',
  components: {
    FormConfig: () => import('./formConfig/list.vue'),
    // StatusConfig: () => import('./statusConfig/list.vue')
    BillConfig: () => import('./billConfig/list.vue'),
    WorkConfig: () => import('./WorkConfig/list.vue'),
    unitLicenseInfo: () => import('./unitLicenseInfo/list.vue'),
    personnelLicenseInfo: () => import('./personnelLicenseInfo/list.vue')
  },
  data() {
    return {
      menuData: [
        { id: 'FormConfig', label: '业务表单配置' },
        { id: 'BillConfig', label: '单据管理' },
        { id: 'WorkConfig', label: '施工审批提醒设置' },
        { id: 'unitLicenseInfo', label: '单位证照信息' },
        { id: 'personnelLicenseInfo', label: '人员证照信息' }
      ]
    }
  },
  computed: {
    currentKey() {
      const item = this.menuData.find((it) => it.id === this.$route.query.name) ?? this.menuData[0]
      return item.id
    }
  },
  methods: {
    onTreeNodeClick(data) {
      if (data.id !== this.currentKey) {
        this.$router.push({ query: { name: data.id } })
      }
    }
  }
}
</script>
<style scoped lang="scss">
.project-config {
  ::v-deep(> .container-content) {
    display: flex;
    background-color: #fff;
  }
  &__left {
    width: 280px;
    border-right: solid 1px #eee;
    padding: 16px;
    &::v-deep(.el-tree) {
      .el-tree-node__content {
        line-height: 36px;
        height: 36px;
      }
      .el-tree-node.is-current > .el-tree-node__content {
        color: #3562db;
        background: #e6effc;
      }
    }
  }
  &__right {
    flex: 1;
    overflow: hidden;
  }
}
</style>
