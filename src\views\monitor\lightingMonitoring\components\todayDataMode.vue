<template>
  <div class="todayDataMode">
    <transition name="fade">
      <div v-show="fadeShow" key="1" class="content_top">
        <div id="scatterEchart"></div>
        <div id="monitoringEchart"></div>
      </div>
    </transition>
    <transition name="fade">
      <div v-show="fadeShow" key="2" class="content_center">
        <el-table v-loading="tableLoading" :data="tableData" :border="true" stripe height="calc(100% - 0px)" :cell-style="{ padding: '3px 0px' }">
          <el-table-column prop="timeTableName" label="场景名称" show-overflow-tooltip min-width="30%%"> </el-table-column>
          <el-table-column prop="timeTable" label="时间表" show-overflow-tooltip min-width="30%%"> </el-table-column>
          <el-table-column prop="operation" label="控制参数下发时间设定" show-overflow-tooltip>
            <template slot-scope="scope">
              <div class="operation_box">
                <div
                  v-for="(item, index) in scope.row.operation"
                  :key="index"
                  class="operation_box_block"
                  :style="{
                    width: `calc(${item.width} - 2px)`,
                    'background-color': item.bgColor
                  }"
                >
                  <span v-if="index + 1 != scope.row.operation.length">{{ item.switchControl }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </transition>
    <div class="content_bottom" :style="{ height: fadeShow ? 'calc(29% - 10px)' : 'calc(100% - 10px)' }">
      <div v-if="type == 'init'" class="content-top" style="margin: 5px;">
        <el-input v-model="searchForm.dictName" placeholder="分组名称" style="width: 15%; margin-right: 15px;"> </el-input>
        <el-select v-model="searchForm.timeTableId" class="sino_sdcp_input mr15" style="width: 15%;" clearable placeholder="场景选择">
          <el-option v-for="item in sceneList" :key="item.id" :label="item.timeTableName" :value="item.id"></el-option>
        </el-select>
        <el-cascader
          v-model="searchForm.spacesId"
          class="sino_sdcp_input mr15"
          :options="buildingOptions"
          :show-all-levels="false"
          placeholder="建筑楼层空间"
          :props="{
            label: 'ssmName',
            value: 'id',
            multiple: true
          }"
        >
        </el-cascader>
        <el-select v-model="searchForm.spaceTypes" class="sino_sdcp_input mr15" multiple collapse-tags clearable placeholder="服务空间类型">
          <el-option v-for="item in areaOption" :key="item.id" :label="item.dictName" :value="item.id"></el-option>
        </el-select>
        <el-button plain type="primary" @click="reset()">重置</el-button>
        <el-button type="primary" @click="search()">查询</el-button>
        <i v-if="fadeShow" title="展开" class="el-icon-top fadeBtn" @click="fadeChange"></i>
        <i v-else title="关闭" class="el-icon-bottom fadeBtn" @click="fadeChange"></i>
      </div>
      <el-table
        v-loading="groppTableLoading"
        :data="groupTableData"
        :border="true"
        stripe
        :height="type == 'init' ? 'calc(100% - 50px)' : 'calc(100% - 0px)'"
        :cell-style="{ padding: '3px 0px' }"
      >
        <el-table-column prop="dictName" label="分组名称" show-overflow-tooltip min-width="10%"> </el-table-column>
        <el-table-column prop="count" label="回路数量" show-overflow-tooltip min-width="7%"> </el-table-column>
        <el-table-column prop="circuitName" label="回路名称" show-overflow-tooltip min-width="13%"> </el-table-column>
        <el-table-column prop="timeTableName" label="场景" show-overflow-tooltip min-width="10%"> </el-table-column>
        <el-table-column prop="timeTable" label="时间表" show-overflow-tooltip min-width="20%"> </el-table-column>
        <el-table-column prop="operation" label="时刻表预览" show-overflow-tooltip min-width="40%">
          <template slot-scope="scope">
            <div class="operation_box">
              <div
                v-for="(item, index) in scope.row.operation"
                :key="index"
                class="operation_box_block"
                :style="{
                  width: `calc(${item.width} - 2px)`,
                  'background-color': item.bgColor
                }"
              ></div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
import moment from 'moment'
import * as echarts from 'echarts'
export default {
  name: 'todayDataMode',
  components: {},
  props: {
    type: {
      type: String,
      default: 'init'
    }
  },
  data() {
    return {
      searchForm: {
        dictName: '',
        timeTableId: '',
        spacesId: [],
        spaceTypes: []
      },
      sceneList: [],
      buildingOptions: [],
      areaOption: [],
      tableData: [],
      tableLoading: false,
      groupTableData: [],
      groppTableLoading: false,
      fadeShow: true // 淡入淡出
    }
  },
  mounted() {
    this.unicomStatus()
    if (this.type == 'init') {
      this.getTodayScenes()
      this.getToDayLightingGroup()
      this.setEchartInit()
      this.getStructureTree()
      this.getAreaTypeList()
    } else {
      this.previewData()
    }
  },
  methods: {
    previewData() {
      this.$api.selectPreviewData().then((res) => {
        if (res.code == '200') {
          const data = res.data
          this.setMonitorEchart(data.getCountRunToDayState)
          this.sceneList = data.timeTable.map((item) => {
            item.operation = item.timeTableDataList ? this.jsonListToSectiongList(item.timeTableDataList) : []
            // 获取场景列表
            return {
              id: item.id,
              timeTableName: item.timeTableName
            }
          })
          this.tableData = data.timeTable

          data.toDayLightingGroup.map((item) => {
            item.operation = item.timeTableDataList ? this.jsonListToSectiongList(item.timeTableDataList) : []
          })
          this.groupTableData = data.toDayLightingGroup
        }
      })
    },
    // 获取联调状态
    unicomStatus() {
      this.$api.unicomStatus().then((res) => {
        if (res.code == 200) {
          let startTime = moment().format('YYYY-MM-DD') + ' 00:00:00'
          let endTime = moment().format('YYYY-MM-DD') + ' 24:00:00'
          this.getScatterEchart(startTime, endTime, res.data)
        }
      })
    },
    // 获取今日总览
    setEchartInit() {
      this.$api.getCountRunToDayState().then((res) => {
        if (res.code == '200') {
          this.setMonitorEchart(res.data)
        }
      })
    },
    setMonitorEchart(allData) {
      let startTime = moment().format('YYYY-MM-DD') + ' 00:00:00'
      let endTime = moment().format('YYYY-MM-DD') + ' 24:00:00'
      let dataArray = []
      let seriesData = []
      let [{ sunRise, sunSet }, data] = allData
      sunRise = moment().format('YYYY-MM-DD') + ' ' + sunRise + ':00'
      sunSet = moment().format('YYYY-MM-DD') + ' ' + sunSet + ':00'
      // 生成时间数组 和 时间对应数据的数组
      for (let key in data) {
        dataArray.push(key)
        seriesData.push({
          actuallyClosed: data[key]?.[0]?.actuallyClosed ?? 0,
          actuallyOpen: data[key]?.[0]?.actuallyOpen ?? 0,
          loopSum: data[key]?.[0]?.loopSum ?? 0,
          planClose: data[key]?.[0]?.planClose ?? 0,
          planOpen: data[key]?.[0]?.planOpen ?? 0
        })
      }
      this.getMonitorEchart(startTime, endTime, dataArray, seriesData, sunRise, sunSet)
    },
    // 获取总览散点图
    getScatterEchart(startTime, endTime, data) {
      const getchart = echarts.init(document.getElementById('scatterEchart'))
      getchart.resize()
      const option = {
        tooltip: {
          position: 'top',
          formatter: function (params) {
            return `${params.value[0]}`
          }
        },
        grid: {
          top: '10%',
          left: '3%',
          right: '4%',
          bottom: '0%',
          containLabel: true
        },
        singleAxis: [
          {
            type: 'time',
            interval: 60 * 60 * 1000, // 固定x轴时间间隔 间隔24小时，也就是一天
            min: startTime, // 开始时间时间戳
            max: endTime, // 结束时间时间戳 如果实际的最大日期不确定，也可以不设定这个属性
            boundaryGap: false,
            nameLocation: 'start',
            top: '50%',
            height: '10%',
            axisLabel: {
              show: false
            },
            splitLine: {
              show: false
            },
            axisTick: {
              show: false // 不显示坐标轴刻度线
            }
          }
        ],
        series: [
          {
            coordinateSystem: 'singleAxis',
            type: 'scatter',
            symbol: 'circle',
            data: [],
            symbolSize: function (dataItem) {
              return dataItem[1] * 4
            },
            label: {
              normal: {
                show: true,
                formatter: function (params) {
                  return params.data[2]
                },
                color: '#333',
                textStyle: {
                  fontSize: '15'
                }
              }
            },
            itemStyle: {
              normal: {
                color: '#FFF',
                borderColor: '#333333',
                opacity: 1
              }
            }
          }
        ]
      }
      for (let j = 0; j < data.length; j++) {
        option.series[0].data.push([data[j].date, 10, data[j].switchControl == 1 ? '开' : '关'])
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 获取运行模式总览
    getMonitorEchart(startTime, endTime, dataArray, seriesData, sunRise, sunSet) {
      const getchart = echarts.init(document.getElementById('monitoringEchart'))
      getchart.resize()
      const legendData = [
        '回路总数',
        '实际开启',
        '实际关闭',
        // "未知",
        '计划开启',
        '计划关闭'
      ]
      const legendField = [
        'loopSum',
        'actuallyOpen',
        'actuallyClosed',
        // 'unknown',
        'planOpen',
        'planClose'
      ]
      const legendColor = [
        '#5188FC',
        '#FFC265',
        '#868894',
        // "#59CC75",
        '#FFC265',
        '#868894'
      ]
      const legendLineType = [
        'solid',
        'solid',
        // "solid",
        'solid',
        'dashed',
        'dashed'
      ]
      const seriesObj = []
      for (let i = 0; i < legendData.length; i++) {
        seriesObj.push({
          name: legendData[i],
          type: 'line',
          step: 'start',
          data: [],
          symbol: 'none',
          lineStyle: {
            color: legendColor[i],
            width: 2,
            type: legendLineType[i]
          }
        })
        for (let j = 0; j < dataArray.length; j++) {
          // var num = parseInt(Math.random(1000) * 1000 + 1);
          // console.log(num, seriesData[j][legendField[i]]);
          seriesObj[i].data.push([dataArray[j], seriesData[j][legendField[i]]])
        }
      }
      seriesObj[0].markArea = {
        itemStyle: {
          color: 'rgba(255, 173, 177, 0.4)'
        },
        data: [
          [
            {
              name: '日出',
              xAxis: sunRise
            },
            {
              xAxis: moment(sunRise).add(2, 'minutes').format('YYYY-MM-DD HH:mm:ss')
            }
          ],
          [
            {
              name: '日落',
              xAxis: sunSet
            },
            {
              name: '日落',
              xAxis: moment(sunSet).add(2, 'minutes').format('YYYY-MM-DD HH:mm:ss')
            }
          ]
        ]
      }
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          // icon: "circle",
          data: legendData
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'time',
          interval: 60 * 60 * 1000, // 固定x轴时间间隔 间隔24小时，也就是一天
          min: startTime, // 开始时间时间戳
          max: endTime, // 结束时间时间戳 如果实际的最大日期不确定，也可以不设定这个属性
          boundaryGap: false,
          nameLocation: 'start',
          // x轴的字
          axisLabel: {
            show: true,
            showMinLabel: true,
            showMaxLabel: true,
            formatter: function (value, index) {
              // 格式化成月/日，只在第一个刻度显示年份
              var date = new Date(value)
              return date.getHours() + ':00'
            }
          },
          splitLine: {
            show: false
          },
          axisTick: {
            show: false // 不显示坐标轴刻度线
          },
          label: {}
        },
        yAxis: {
          type: 'value',
          // show: false,
          splitLine: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false // 不显示坐标轴刻度线
          }
        },
        series: seriesObj
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 获取今日场景
    getTodayScenes() {
      this.tableLoading = true
      this.$api.getToDayScenes().then((res) => {
        // console.log(res);
        if (res.code == '200') {
          this.tableLoading = false
          const data = res.data
          this.sceneList = data.map((item) => {
            item.operation = item.timeTableDataList ? this.jsonListToSectiongList(item.timeTableDataList) : []
            // 获取场景列表
            return {
              id: item.id,
              timeTableName: item.timeTableName
            }
          })
          console.log(data)
          this.tableData = data
        }
      })
    },
    // 获取今日场景对应的分组
    getToDayLightingGroup() {
      this.groppTableLoading = true
      const params = {
        ...this.searchForm
      }
      if (params.spacesId.length) {
        Object.assign(params, {
          spacesId: params.spacesId.map((e) => e[e.length - 1])
        })
      }
      if (params.spaceTypes.length) {
        Object.assign(params, {
          spaceTypes: params.spaceTypes.map((e) => e.toString())
        })
      }
      this.$api.getToDayLightingGroup(params).then((res) => {
        // console.log(res);
        this.groppTableLoading = false
        if (res.code == '200') {
          const data = res.data
          data.map((item) => {
            item.operation = item.timeTableDataList ? this.jsonListToSectiongList(item.timeTableDataList) : []
          })
          this.groupTableData = data
        }
      })
    },
    // 数据转换为时刻所需的格式
    jsonListToSectiongList(list) {
      const sectiongList = JSON.parse(JSON.stringify(list))
      let operation = []
      // 增加0点场景
      sectiongList.unshift({
        switchControl: sectiongList[0].switchControl == '1' ? '2' : '1',
        timePoint: '00:00'
      })
      // 增加24点场景
      sectiongList.push({
        switchControl: '',
        timePoint: '24:00'
      })
      // 场景时间排序
      sectiongList.sort((a, b) => {
        return a.timePoint.split(':')[0] - b.timePoint.split(':')[0]
      })
      // 生成时间段渲染页面
      sectiongList.reduce((pre, cur) => {
        const preTime = Number(pre.timePoint.split(':')[0]) * 60 + Number(pre.timePoint.split(':')[1])
        const curTime = Number(cur.timePoint.split(':')[0]) * 60 + Number(cur.timePoint.split(':')[1])
        const params = {
          startTime: pre.timePoint,
          endTime: cur.timePoint,
          switchControl: cur.switchControl == '1' ? '打开' : '关闭',
          width: ((curTime - preTime) / (24 * 60)) * 100 + '%',
          bgColor: pre.switchControl == '1' ? '#FFB936' : '#9099AF'
        }
        operation.push(params)
        return cur
      })
      return operation
    },
    // 获取空间 建筑 楼层信息
    getStructureTree() {
      this.$api.getStructureTree({}).then((res) => {
        const data = res.data
        if (data && data.length) {
          const buildingOption = data.filter((e) => e.ssmType == 3)
          const floorOption = data.filter((e) => e.ssmType == 4)
          this.buildingOptions = this.transData([...buildingOption, ...floorOption], 'id', 'pid', 'children')
        }
      })
    },
    transData(a, idStr, pidStr, chindrenStr, extraParameter) {
      let r = [], hash = {}, id = idStr, pid = pidStr, children = chindrenStr, i = 0, j = 0, len = a.length
      for (; i < len; i++) {
        hash[a[i][id]] = a[i]
      }
      for (; j < len; j++) {
        let aVal = a[j], hashVP = hash[aVal[pid]]
        if (hashVP) {
          !hashVP[children] && (hashVP[children] = [])
          hashVP[children].push(aVal)
        } else {
          r.push(aVal)
        }
        //            查找已部署节点id集
        if (extraParameter && aVal.state == '1') extraParameter.push(aVal.id)
      }
      return r
    },
    // 获取空间类型
    getAreaTypeList() {
      this.$api.getFunctionType({ typeValue: 'SP' }).then((res) => {
        const data = res.data
        if (data && data.length) {
          this.areaOption = data
        }
      })
    },
    reset() {
      this.searchForm = {
        dictName: '',
        timeTableId: '',
        spacesId: [],
        spaceTypes: []
      }
      this.getToDayLightingGroup()
    },
    search() {
      this.getToDayLightingGroup()
    },
    // 改变高度
    fadeChange() {
      this.fadeShow = !this.fadeShow
      if (this.fadeShow) {
        this.$nextTick(() => {
          setTimeout(() => {
            this.setEchartInit()
          }, 300)
        })
      }
    }
  }
}
</script>
<style src="@/assets/styles/sinomis-ui.css" scoped></style>
<style lang="scss" type="text/css" scoped>
.mr15 {
  margin-right: 15px;
}
.todayDataMode {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  .content_top {
    height: 44%;
    background-color: #fff;
    border-radius: 10px;
    #scatterEchart {
      // padding-top: 10px;
      width: 100%;
      height: calc(20% - 10px);
    }
    #monitoringEchart {
      width: 100%;
      height: 80%;
    }
  }
  .content_center {
    height: calc(27% - 10px);
    background-color: #fff;
    border-radius: 10px;
    padding: 10px;
    box-sizing: border-box;
  }
  .content_bottom {
    height: calc(29% - 10px);
    background-color: #fff;
    border-radius: 10px;
    padding: 10px;
    box-sizing: border-box;
    position: relative;
    transition: height 0.3s linear;
    overflow: hidden;
    .fadeBtn {
      position: absolute;
      top: 20px;
      right: 40px;
      font-size: 25px;
      cursor: pointer;
    }
  }
  .operation_box {
    height: 30px;
    display: flex;
    justify-content: space-between;
    .operation_box_block {
      height: 30px;
      text-align: right;
      // line-height: 30px;
      position: relative;
      span {
        position: absolute;
        display: inline-block;
        background: #fff;
        border-radius: 2px;
        border: 1px solid #34b253;
        font-size: 13px;
        padding: 0 3px;
        z-index: 10;
        right: 0;
        top: 50%;
        transform: translate(50%, -50%);
      }
    }
  }
  .el-table thead {
    height: 40px !important;
    line-height: 40px !important;
  }
}
</style>
