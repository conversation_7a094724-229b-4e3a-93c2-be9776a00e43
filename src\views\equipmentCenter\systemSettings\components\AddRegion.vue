<template>
  <el-dialog :title="current ? '编辑区域' : '新建区域'" :visible="visible" width="40%" custom-class="add-region" :before-close="handleClose">
    <div>
      <div class="baseInfo">
        <div class="baseInfo-title">
          <span class="green_line"></span>
          <span>基础信息</span>
        </div>
        <el-form ref="form" :model="formData" :rules="rules" label-width="80px">
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="区域名称" prop="regionName">
                <el-input v-model="formData.regionName" maxlength="16" placeholder="请输入区域名称"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item prop="regionComment" label="备注说明">
                <el-input v-model="formData.regionComment" show-word-limit type="textarea" maxlength="200" placeholder="请输入备注说明"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="inspectionStandards">
        <div class="inspectionStandards-title">
          <div>
            <span class="green_line"></span>
            <span>巡检标准</span>
          </div>
          <el-button type="text" @click="handleAdd">添加</el-button>
        </div>
        <el-table :data="tableData" border style="width: 100%">
          <el-table-column prop="dictCode" label="巡检类型">
            <template slot-scope="scope">
              <VirtualListSelect
                v-model="scope.row.dictCode"
                :options="selectData"
                :propsOptions="{
                  label: 'dictName',
                  value: 'id'
                }"
                placeholder="巡检类型"
                @change="handleChange($event, scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="dayNumber" label="日（次数）">
            <template slot-scope="scope">
              <el-input v-model="scope.row.dayNumber" placeholder="请输入" @input="handleInputNumber($event, scope.row, 'dayNumber')"></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="weekNumber" label="周（次数）">
            <template slot-scope="scope">
              <el-input v-model="scope.row.weekNumber" placeholder="请输入" @input="handleInputNumber($event, scope.row, 'weekNumber')"></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="monthNumber" label="月（次数）">
            <template slot-scope="scope">
              <el-input v-model="scope.row.monthNumber" placeholder="请输入" @input="handleInputNumber($event, scope.row, 'monthNumber')"></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="quarterNumber" label="季度（次数）">
            <template slot-scope="scope">
              <el-input v-model="scope.row.quarterNumber" placeholder="请输入" @input="handleInputNumber($event, scope.row, 'quarterNumber')"></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="yearNumber" label="年（次数）">
            <template slot-scope="scope">
              <el-input v-model="scope.row.yearNumber" placeholder="请输入" @input="handleInputNumber($event, scope.row, 'yearNumber')"></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="address" label="操作" width="60">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="handleDelete(scope)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import mixins from '../mixins/index.js'
export default {
  mixins: [mixins],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    current: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      formData: {
        regionName: '',
        regionComment: ''
      },
      tableData: [{ dictCode: '-1', dictName: '全部', dayNumber: '', weekNumber: '', monthNumber: '', quarterNumber: '', yearNumber: '' }],
      rules: {
        regionName: [{ required: true, message: '请输入区域名称', trigger: ['change', 'blur'] }]
      }
    }
  },
  watch: {
    current: {
      handler(val) {
        if (val) {
          this.formData = JSON.parse(JSON.stringify(val))
          this.tableData = val.planRegionDetailList
        }
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    this.handleGetSelectData()
  },
  methods: {
    handleInputNumber(value, row, key) {
      row[key] = Number(value.replace(/[^\d.]/g, ''))
    },
    handleChange(e, row) {
      const item = this.selectData.find((item) => item.id === e)
      row.dictName = item.dictName
    },
    handleAdd() {
      if (this.tableData[this.tableData.length - 1].dictCode === '-1') {
        this.$message.info('巡检类型为全部时不可新增巡检标准')
        return
      }
      const isTrue = this.tableData.some((item) => Object.keys(item).some((key) => item[key] === ''))
      if (isTrue) {
        this.$message.info('请先完善巡检标准信息')
        return
      }
      this.tableData.push({
        dictCode: '',
        dictName: '',
        dayNumber: '',
        weekNumber: '',
        monthNumber: '',
        quarterNumber: '',
        yearNumber: ''
      })
    },
    handleDelete(scope) {
      if (this.tableData.length === 1) {
        this.$message.info('至少保留一条数据')
        return
      }
      const { $index } = scope
      this.tableData.splice($index, 1)
    },
    handleClose() {
      this.$refs.form.resetFields()
      this.tableData = []
      this.$emit('update:visible', false)
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const isTrue = this.tableData.some((item) => Object.keys(item).some((key) => item[key] === ''))
          if (isTrue) {
            this.$message({
              type: 'info',
              message: '请填写完整巡检标准'
            })
            return
          }
          const type = this.$route.path.includes('systemSettings') ? '2' : '1'
          this.$api.savePlanRegion({ ...this.formData, planRegionDetailList: this.tableData, type }).then((res) => {
            if (res.code == 200) {
              this.$message({
                type: 'success',
                message: '添加成功'
              })
              this.$emit('success')
              this.handleClose()
            } else {
              this.$message({
                type: 'error',
                message: res.message
              })
            }
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .add-region {
  .el-dialog__body {
    max-height: 50vh;
    overflow: auto;
    padding: 10px 20px;
  }
  .baseInfo {
    &-title {
      margin-bottom: 16px;
    }
    .el-form-item {
      margin-bottom: 16px;
    }
    .el-form-item__error {
      padding: 0;
    }
  }
  .inspectionStandards {
    &-title {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;
    }
    .el-select {
      width: 100%;
    }
  }
}
</style>
>
