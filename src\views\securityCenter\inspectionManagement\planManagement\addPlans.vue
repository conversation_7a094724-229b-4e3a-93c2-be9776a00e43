<template>
  <div v-loading="pageLoading" style="height: 100%;">
    <div class="inner">
      <div class="content_box" :class="{ info: readonly }">
        <div class="topList">
          <div class="topTitle">
            <div class="green_line"></div>
            <span>基本信息</span>
          </div>
          <el-form ref="formInline" :model="formInline" :inline="true" class="form-inline" :rules="rules">
            <el-form-item>
              <el-form-item label="计划名称" prop="planName">
                <el-input v-model.trim="formInline.planName" :readonly="readonly" placeholder="请输入计划名称" class="width_lengthen"></el-input>
              </el-form-item>
              <el-form-item label="计划类型" prop="planTypeId">
                <el-select v-model.trim="formInline.planTypeId" :disabled="readonly" filterable placeholder="请选择" class="width_lengthen">
                  <el-option v-for="item in planTypeList" :key="item.id" :label="item.dictName" :value="item.id" :readonly="readonly"> </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="执行方式" prop="executeMode">
                <el-select v-model.trim="formInline.executeMode" filterable :disabled="readonly" placeholder="请选择" class="width_lengthen" @change="changeExecuteMode">
                  <el-option v-for="item in executeModeList" :key="item.executeMode" :label="item.name" :value="item.executeMode"> </el-option>
                </el-select>
              </el-form-item>
            </el-form-item>
            <div style="display: flex; width: 100%; flex-wrap: wrap;">
              <!--按钮     编辑、新增、复制时有按钮 -->
              <div style="display: flex; align-items: center;" :style="{ width: deptArrAll.length || peopleArr.length ? '100%' : '' }">
                <el-form-item
                  v-if="formInline.executeMode == '1'"
                  label="小组"
                  prop="equipmentTypeId"
                  class="itemLabel"
                  :style="{
                    width: deptArrAll.length || query.type == 'edit' || query.type == 'copy' ? '270px' : '400px',
                    marginRight: deptArrAll.length || query.type == 'edit' || query.type == 'copy' ? '0' : '50px'
                  }"

                >
                  <el-button style="background: #5188fc; color: #fff;" @click="selectDept">选择小组 </el-button>
                </el-form-item>
                <!-- 按钮   编辑、新增、复制时有按钮 -->
                <el-form-item
                  v-if="formInline.executeMode == '2'"
                  label="人员"
                  prop="equipmentTypeId"
                  class="itemLabel"
                  :style="{
                    width: peopleArr.length || query.type == 'edit' || query.type == 'copy' ? '230px' : '400px',
                    marginRight: peopleArr.length || query.type == 'edit' || query.type == 'copy' ? '0' : '50px'
                  }"
                >
                  <el-button style="background: #5188fc; color: #fff;" @click="selectPeople">选择人员 </el-button>
                </el-form-item>

                <!-- 部门/班组名称显示 -->
                <div v-if="deptArrAll && deptArrAll.length && query.type == 'add'" class="deptArrAllBox">
                  <div v-for="(item, index) in deptArrAll" :key="index">
                    <span>{{ item.teamName }}</span>
                    <span v-show="deptArrAll.length > 1 && index !== deptArrAll.length - 1">、</span>
                  </div>
                </div>
                <!-- 选择人员名称显示 -->
                <div v-if="peopleArr && peopleArr.length && query.type == 'add'" class="peopleArrBox">
                  <div v-for="(item, index) in peopleArr" :key="index">
                    <span>{{ item.name || item.staffName }}</span>
                    <span v-show="peopleArr.length > 1 && index !== peopleArr.length - 1">、</span>
                  </div>
                </div>

                <!-- 编辑/复制 时班组、选择人员回显 start-->
                <div v-if="(query.type == 'edit' || query.type == 'copy') && formInline.executeMode == '1'" style="display: flex; margin-bottom: 20px;">
                  <div v-if="deptArrAll.length == 0">{{ distributionTeamName }}</div>
                  <div v-for="(item, index) in deptArrAll" :key="index">
                    <span>{{ item.teamName }}</span>
                    <span v-show="deptArrAll.length > 1 && index !== deptArrAll.length - 1">、</span>
                  </div>
                </div>
                <div v-if="(query.type == 'edit' || query.type == 'copy') && formInline.executeMode == '2'" style="display: flex; margin-bottom: 20px;">
                  <div v-if="peopleArr.length == 0">{{ distributionPeopleName }}</div>
                  <div v-for="(item, index) in peopleArr" :key="index">
                    <span>{{ item.name || item.staffName }}</span>
                    <span v-show="peopleArr.length > 1 && index !== peopleArr.length - 1">、</span>
                  </div>
                </div>
                <!-- 编辑时班组、选择人员回显 end-->
              </div>
              <el-form-item label="周期类型" prop="cycleType" class="itemLabel">
                <el-select v-model.trim="formInline.cycleType" filterable placeholder="请选择" :disabled="readonly" class="width_lengthen" @change="changeCycleType">
                  <el-option v-for="item in cycleTypeList" :key="item.cycleType" :label="item.label" :value="item.cycleType"> </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                label="是否定位"
                prop="locationFlag"
                class="itemLabel"
                :style="{ width: formInline.executeMode == '1' || formInline.executeMode == '2' ? '' : '400px' }"
              >
                <el-radio v-model="formInline.locationFlag" :disabled="readonly" label="0">是</el-radio>
                <el-radio v-model="formInline.locationFlag" :disabled="readonly" label="1">否</el-radio>
              </el-form-item>
              <el-form-item
                label="是否允许扫码"
                prop="scanFlag"
                class="isScan itemLabel"
                :style="{ width: formInline.executeMode == '1' || formInline.executeMode == '2' ? '' : '400px' }"
              >
                <el-radio v-model="formInline.scanFlag" :disabled="readonly" label="0">是</el-radio>
                <el-radio v-model="formInline.scanFlag" :disabled="readonly" label="1">否</el-radio>
              </el-form-item>
            </div>

            <!-- 每周 -->
            <el-form-item v-if="formInline.cycleType == 0" label="开始日期" prop="startDate" class="dateNum itemLabel">
              <el-select v-model.trim="formInline.startDate" filterable placeholder="请选择" :disabled="readonly" class="width_lengthen">
                <el-option v-for="item in singleTimeList" :key="item.startTime" :label="item.label" :value="item.startTime"> </el-option>
              </el-select>
            </el-form-item>
            <!-- 季度 -->
            <el-form-item v-if="formInline.cycleType == 3" label="开始月份" prop="startMonth" class="dateNum itemLabel">
              <el-select v-model.trim="formInline.startMonth" :disabled="readonly" filterable placeholder="请选择" class="width_lengthen">
                <el-option v-for="item in startMonthList" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <!-- 每月 -->
            <el-form-item v-if="formInline.cycleType == 2 || formInline.cycleType == 3" label="开始日期" prop="startDate" class="dateNum itemLabel">
              <el-select v-model.trim="formInline.startDate" filterable placeholder="请选择" :disabled="readonly" class="width_lengthen">
                <el-option v-for="item in setMonths()" :key="item.value" :label="item.value" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <!-- 全年.、单次 -->
            <el-form-item v-if="formInline.cycleType == 5 || formInline.cycleType == 8" label="开始日期" prop="startDate" class="dateNum itemLabel">
              <el-date-picker v-model="formInline.startDate" :picker-options="pickerOptions" :disabled="readonly" type="date" placeholder="请选择日期"> </el-date-picker>
            </el-form-item>
            <el-form-item label="时间" prop="timeLine">
              <el-time-picker
                v-model="formInline.timeLine"
                :disabled="readonly"
                is-range
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                placeholder="选择时间范围"
                style="width: 300px;"
                value-format="HH:mm:ss"
                :editable="false"
              ></el-time-picker>
            </el-form-item>
            <!-- 每日除外，都显示 -->
            <el-form-item
              v-if="(formInline.cycleType == 8 || formInline.cycleType == 0 || formInline.cycleType == 2 || formInline.cycleType == 3 || formInline.cycleType == 5) && !readonly"
              label="完成期限"
              prop="finalTime"
              class=""
            >
              <el-input
                v-model="formInline.finalTime"
                placeholder="请输入完成期限"
                type="number"
                min="1"
                onkeyup="this.value = this.value.replace(/[^\d]/g,'');"
                onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)));"
              >
                <template slot="append">天</template>
              </el-input>
            </el-form-item>
            <!-- 查看时回显 -->
            <el-form-item
              v-if="(formInline.cycleType == 8 || formInline.cycleType == 0 || formInline.cycleType == 2 || formInline.cycleType == 3 || formInline.cycleType == 5) && readonly"
              label="完成期限"
              prop="finalTime"
              class=""
            >
              <span>{{ formInline.finalTime }}天</span>
            </el-form-item>
            <!-- 每日 -->
            <div v-if="!readonly" style="display: flex; align-items: center;">
              <el-form-item v-if="formInline.cycleType == 6" label="频次" prop="cycleRole" style="margin-left: 30px;" class="dayNum itemLabel">
                <el-input
                  v-model="formInline.cycleRole"
                  placeholder="请输入频次"
                  type="number"
                  min="1"
                  onkeyup="this.value = this.value.replace(/[^\d]/g,'');"
                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)));"
                >
                  <template slot="append">次</template>
                </el-input>
              </el-form-item>
              <el-form-item v-if="formInline.cycleType == 6" label="最小间隔" prop="minInterval" class="minNum itemLabel">
                <el-input
                  v-model="formInline.minInterval"
                  placeholder="请输入最小间隔"
                  type="number"
                  min="0"
                  onkeyup="this.value = this.value.replace(/[^\d]/g,'');"
                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)));"
                >
                  <template slot="append">分钟</template>
                </el-input>
              </el-form-item>
            </div>
            <div v-else style="display: flex; align-items: center;">
              <el-form-item v-if="formInline.cycleType == 6" label="频次" prop="cycleRole" style="width: 370px; text-align: left;">
                <span>{{ formInline.cycleRole }}次</span>
              </el-form-item>
              <el-form-item v-if="formInline.cycleType == 6" label="最小间隔" prop="minInterval" style="width: 370px; margin-left: 30px;">
                <span>{{ formInline.minInterval }}分钟</span>
              </el-form-item>
            </div>

            <el-form-item label="计划说明" class="itemLabel">
              <el-input
                v-model.trim="formInline.remarks"
                :disabled="readonly"
                style="width: 755px;"
                type="textarea"
                class="project-textarea"
                placeholder="请输入计划说明，最多200字"
                show-word-limit
                :autosize="{ minRows: 3, maxRows: 4 }"
                maxlength="200"
              ></el-input>
            </el-form-item>
          </el-form>
        </div>
        <div class="topList">
          <div class="topTitle">
            <div class="green_line"></div>
            <span>巡检点管理</span>
          </div>
          <div>
            <div style="width: 50%; margin: 15px 10px; display: flex; justify-content: space-between;">
              <div>
                <el-button style="background: #5188fc; color: #fff;" @click="selectTask">选择巡检点</el-button>
                <el-button style="background: #5188fc; color: #fff;" :disabled="multipleSelection.length < 1" @click="deleteTaskPoint">删除</el-button>
              </div>
              <div class="sortDiv" style="display: flex; align-items: center;">
                <span style="font-size: 14px;">按顺序执行：</span>
                <el-radio v-model="formInline.sortFlag" :disabled="readonly" label="0">是</el-radio>
                <el-radio v-model="formInline.sortFlag" :disabled="readonly" label="1">否</el-radio>
              </div>
            </div>
            <div style="margin-bottom: 10px;">
              <el-table
                class="taskPointTable"
                :data="tableData"
                border
                style="width: 100%; height: 230px;"
                :cell-style="{ padding: '8px' }"
                stripe
                height="280px"
                :header-cell-style="{ background: '#f2f4fbd1' }"
                @selection-change="handleSelectionChangeDialog"
                @select-all="selectAllMed"
              >
                <el-table-column align="center" type="selection" width="55"></el-table-column>
                <el-table-column align="center" type="index" label="序号" width="68"></el-table-column>
                <el-table-column align="center" prop="taskPointName" show-overflow-tooltip label="巡检点名称"></el-table-column>
                <el-table-column align="center" prop="taskPointTypeName" show-overflow-tooltip label="巡检点类型"></el-table-column>
                <el-table-column align="center" prop="projectName" show-overflow-tooltip label="任务书">
                  <template slot-scope="scope">
                    <span v-if="!scope.row.projectName || scope.row.projectName == ''" style="color: #ff1919;">无</span>
                    <span v-else style="color: #5188fc;">{{ scope.row.projectName }}</span>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="locationPointName" show-overflow-tooltip label="定位点名称">
                </el-table-column>
                <el-table-column align="center" width="325" show-overflow-tooltip label="操作">
                  <template slot-scope="scope">
                    <span style="color: #5188fc; cursor: pointer;" @click="addRowTask(scope.row)">添加任务书</span>
                  </template>
                </el-table-column>
              </el-table>
              <el-pagination
                class="pagination"
                style="margin-top: 10px; bottom: 90px; display: none;"
                :current-page="paginationData.currentPage"
                :page-sizes="[15, 30, 50, 100]"
                :page-size="paginationData.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="paginationData.total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              ></el-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="bottomBar">
      <div class="bottomWrap">
        <el-button type="primary" @click="completePlanFn">确定</el-button>
        <el-button type="primary" @click="saveFn">暂存</el-button>
        <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db;" @click="$router.go('-1')">取消</el-button>
      </div>
    </div>
    <!-- 选择巡检点 -->
    <SelectTaskPoint ref="taskPoints" :dialogVisibleWz="dialogVisibleWz" @closeDialog="closeDialogFn" @multipleSelection="multipleSelectionFn"/>
    <!-- 添加任务书 -->
    <AddRowTask ref="rowTask" :dialogVisibleSk="dialogVisibleSk" @closeDialog="closeDialogSkFn" @multipleSelection="multipleSelectionTaskFn" />
    <!-- 班组/部门 -->
    <SelectDept ref="selectDept" :parentComData="deptArrAll" :dialogVisibleDp="dialogVisibleDp" @closeDialog="closeDialogDpFn" @treeNodeData="treeNodeDataFn" />
    <!-- 按人员选择 -->
    <SelectPeople
      ref="selectPeople"
      :parentComData="peopleArr"
      :dialogVisiblePo="dialogVisiblePo"
      @closeDialog="closeDialogPoFn"
      @multipleSelectArr="multipleSelectArrFn"
      @teamIdsSelectedArr="teamIdsSelectedArrFn"
    />
  </div>
</template>
<script>
import SelectTaskPoint from './components/selectTaskPoint'
import AddRowTask from './components/addRowTask'
import SelectDept from './components/selectDept'
import SelectPeople from './components/selectPeople'
import moment from 'moment'
export default {
  name: 'addPlans',
  components: {
    SelectTaskPoint,
    AddRowTask,
    SelectDept,
    SelectPeople
  },
  data() {
    var setTimeFormat = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请选择时间'))
      } else {
        let date = new Date(), year = date.getFullYear(), month = date.getMonth(), day = date.getDate()
        let startTime = +new Date(`${year}-${month + 1}-${day} ${value[0]}`), endTime = +new Date(`${year}-${month + 1}-${day} ${value[1]}`)
        if (startTime == endTime) {
          callback(new Error('结束时间必须大于开始时间'))
        } else {
          callback()
        }
      }
    }
    return {
      moment,
      multipleSelection: [],
      title: '',
      pageOrigin: '', // 保存页面来源，是日常保养，还是日常巡检
      readonly: false,
      saveLoading: false,
      completeLoading: false,
      formInline: {
        executeMode: '', // 执行方式
        planTypeId: '', // 计划类型
        cycleType: 8, // 周期类型
        finalTime: '', // 完成期限
        planName: '', // 计划名称
        planTypeName: '',
        startDate: '', // 开始日期
        projectExplain: '',
        timeLine: '', // 时间
        cycleRole: '', // 频次
        minInterval: '', // 最小间隔
        remarks: '', // 计划说明
        startMonth: '', // 开始月份
        locationFlag: '0', // 是否定位
        scanFlag: '0', // 是否扫码
        sortFlag: '0', // 是否按顺序执行
        id: '' // 模板id，修改必传
      },
      distributionTeamId: '', // 按班组/部门
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      typeList: [],
      workTypeCodeList: [],
      contents: [
        {
          detailName: '',
          id: 0,
          workTermcontents: [
            {
              termType: '1',
              content: '',
              id: 0,
              rangeStart: '',
              rangeEnd: '',
              einheitCode: '',
              einheitName: '',
              isRadio: [
                {
                  contText: '',
                  isDefault: '0'
                }
              ]
            }
          ]
        }
      ],
      unitOptions: [],
      termTypeOptions: [],
      query: {},
      id: '',
      blockLoading: false,
      typeArr: [],
      typeTreeData: [],
      propsType: {
        children: 'children',
        label: 'typeName',
        value: 'typeId',
        checkStrictly: true
      },
      rules: {
        planName: [
          {
            required: true,
            message: '请输入计划名称',
            trigger: 'change'
          },
          {
            min: 1,
            max: 50,
            message: '长度在 1 到 50 个字符',
            trigger: 'blur'
          }
        ],
        planTypeId: [
          {
            required: true,
            message: '请选择计划类型',
            trigger: 'change'
          }
        ],
        executeMode: [
          {
            required: true,
            message: '请选择执行方式',
            trigger: 'change'
          }
        ],
        cycleRole: [
          {
            required: true,
            message: '请输入频次',
            trigger: 'blur'
          }
        ],
        minInterval: [
          {
            required: true,
            message: '请输入最小间隔',
            trigger: 'blur'
          }
        ],
        finalTime: [
          {
            required: true,
            message: '请输入完成期限',
            trigger: 'blur'
          }
        ],
        cycleType: [
          {
            required: true,
            message: '请选择周期类型',
            trigger: 'change'
          }
        ],
        timeLine: [
          {
            required: true,
            validator: setTimeFormat,
            trigger: 'change'
          }
        ],
        startMonth: [
          {
            required: true,
            message: '请选择月份',
            trigger: 'change'
          }
        ],
        startDate: [
          {
            required: true,
            message: '请选择开始日期',
            trigger: 'change'
          }
        ]
      },
      currentPage: 1,
      total: 0,
      fileList: [],

      tableData: [],
      dialogVisibleWz: false,
      dialogVisibleSk: false,
      dialogVisibleDp: false,
      dialogVisiblePo: false,
      executeModeList: [
        {
          executeMode: '1',
          name: '按小组'
        },
        {
          executeMode: '2',
          name: '按人员'
        }
      ], // 执行方式
      cycleTypeList: [
        {
          cycleType: 8,
          label: '单次'
        },
        {
          cycleType: 6,
          label: '每日'
        },
        {
          cycleType: 0,
          label: '每周'
        },
        {
          cycleType: 2,
          label: '每月'
        },
        {
          cycleType: 3,
          label: '季度'
        },
        {
          cycleType: 5,
          label: '全年'
        }
      ], // 周期类型
      planTypeList: [], // 计划类型
      singleTimeList: [
        {
          startTime: '1',
          label: '每周一'
        },
        {
          startTime: '2',
          label: '每周二'
        },
        {
          startTime: '3',
          label: '每周三'
        },
        {
          startTime: '4',
          label: '每周四'
        },
        {
          startTime: '5',
          label: '每周五'
        },
        {
          startTime: '6',
          label: '每周六'
        },
        {
          startTime: '7',
          label: '每周日'
        }
      ], // 单次开始日期
      startMonthList: [
        // 季度开始月份
        {
          value: 1,
          label: '第一个月'
        },
        {
          value: 2,
          label: '第二个月'
        },
        {
          value: 3,
          label: '第三个月'
        }
      ],
      selectTaskRow: '',
      seelctTaskArr: [],
      saveType: '', // 暂存或提交
      deptArr: '', // 班组
      deptArrs: '',
      deptArrAll: [],
      distributionTeamName: '',
      peopleArr: [],
      distributionPeopleName: '',
      distributionPeopleId: '',
      selectTaskPointRow: [],
      tableData1: [],
      teamIdsSelectesArr: [],
      distributionTeamArr: [],
      distributionPeopleArr: [],
      // 禁用今天之前的日期
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 3600 * 24 * 1000
        }
      },
      // 任务书暂存
      maintainPlanRegions: [],
      selectAll: false,
      pageLoading: false
    }
  },
  mounted() {
    this.query = this.$route.query
    if (this.query.type == 'edit' && this.query.id) {
      this.formInline.id = this.query.id
      this._getMaintainPlanDetail() // 获取详情
      this.title = '计划修改'
    } else if (this.query.type == 'add') {
      this.title = '计划新增'
    } else {
      this.title = '计划复制'
      this._getMaintainPlanDetail()
    }
    // 获取计划类型
    this._findDictionaryTableList()
  },
  methods: {
    proving() {
      // 最小间隔
      console.log('最小间隔')
      this.formInline.minInterval = this.formInline.minInterval.replace(/[^\.\d]/g, '')
      this.formInline.minInterval = this.formInline.minInterval.replace('.', '')
      this.formInline.minInterval = typeof this.formInline.minInterval == 'string' ? '' : this.formInline.minInterval
    },
    proving1() {
      // 频次
      console.log('频次')
      this.formInline.cycleRole = this.formInline.cycleRole.replace(/\D|^0/g, '')
      this.formInline.cycleRole = this.formInline.cycleRole.replace('.', '')
      this.formInline.cycleRole = typeof this.formInline.cycleRole == 'string' ? '' : this.formInline.cycleRole
    },
    proving2() {
      // 完成期限
      console.log('完成期限')
      this.formInline.finalTime = this.formInline.finalTime.replace(/\D|^0/g, '')
      this.formInline.finalTime = this.formInline.finalTime.replace('.', '')
      this.formInline.finalTime = this.formInline.finalTime.replace(/[`~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘’，。、]/g, '')
      console.log(this.formInline.finalTime)
    },
    // 切换周期类型,重置数据
    changeCycleType() {
      console.log(this.formInline.cycleType)
      // 重置开始日期
      this.formInline.startDate = ''
      this.formInline.finalTime = ''
      this.formInline.timeLine = ''
      this.formInline.cycleRole = ''
      this.formInline.minInterval = ''
      this.formInline.startMonth = ''
    },
    // 获取班组(院内)
    treeNodeDataFn(data) {
      this.deptArr = data
      if (this.query.type == 'edit' || this.query.type == 'copy') {
        this.deptArrAll = [...this.deptArr, ...this.deptArrs]
      } else {
        this.deptArrAll = [...this.deptArr, ...this.deptArrs]
      }
    },
    // 获取人员数据
    multipleSelectArrFn(data) {
      this.peopleArr = [...data]
      if (this.query.type == 'edit' || this.query.type == 'copy') {
      }
    },
    // 获取人员的同时，也获取到班组的id和班组名称（暂时未使用）
    teamIdsSelectedArrFn(data) {
      console.log(data, '人员对应班组信息')
      if (data) {
        this.teamIdsSelectesArr = data
      } else {
        this.teamIdsSelectesArr = []
      }
    },
    // 改变执行方式,清空上一个方式的值
    changeExecuteMode() {
      console.log(this.formInline.executeMode)
      this.peopleArr = []
      this.distributionPeopleName = ''
      this.deptArrAll = []
      this.distributionTeamName = ''
    },
    // 暂存
    saveFn() {
      console.log('暂存')
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          this.saveLoading = true
          this.saveType = 1
          this.completeFn(this.saveType)
        }
      })
    },
    // 点击确定按钮 (提交送审)
    completePlanFn() {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          this.completeLoading = true
          this.saveType = 2
          this.completeFn(this.saveType)
        }
      })
    },
    // 新增保存
    completeFn(types) {
      let obj = JSON.parse(sessionStorage.getItem('LOGINDATA'))
      this.seelctTaskArr = []
      const { formInline } = this
      if (this.planTypeList && this.planTypeList.length) {
        this.planTypeList.forEach((val, index) => {
          if (val.id == formInline.planTypeId) {
            formInline.planTypeName = val.dictName
          }
        })
      }
      // 编辑 、复制时传参任务点
      if (this.formInline.id == '' && this.query.type == 'add') {
        // 新增时不用再次勾选表格，自动默认新增所选任务点
        if (this.tableData.length) {
          this.tableData.forEach((item, index) => {
            this.seelctTaskArr.push({
              taskPointId: item.id, // 任务点id"
              taskPointName: item.taskPointName, // 任务点名称"
              taskBookId: item.projectId, // 任务书id"
              taskBookName: item.projectName // 任务书名称"
            })
          })
        }
      } else if (this.query.type == 'copy' || (this.query.type == 'edit' && this.tableData.length)) {
        console.log('编辑', this.tableData)
        this.tableData.forEach((val, indx) => {
          this.seelctTaskArr.push({
            taskPointId: val.id ? val.id : val.taskPointId, // 任务点id"
            taskPointName: val.taskPointName ? val.taskPointName : val.taskPointName, // 任务点名称"
            taskBookId: val.projectId ? val.projectId : val.taskBookId, // 任务书id"
            taskBookName: val.projectName ? val.projectName : val.taskBookName // 任务书名称"
          })
        })
      }
      // 班组
      let teamId =
        this.deptArrAll &&
        this.deptArrAll.length &&
        this.deptArrAll.map((val, index) => {
          return val.id
        })
      let teamName =
        this.deptArrAll &&
        this.deptArrAll.length &&
        this.deptArrAll.map((val, index) => {
          return val.teamName
        })
      // 人员
      let peopleId =
        this.peopleArr &&
        this.peopleArr.length &&
        this.peopleArr.map((val, index) => {
          return val.id || val.staffId
        })
      let peopleName =
        this.peopleArr &&
        this.peopleArr.length &&
        this.peopleArr.map((val, index) => {
          if (val.name) {
            return val.name
          } else {
            return val.staffName
          }
        })
      // 选择人员的同时，将班组id和班组name传过来
      let teamIds =
        this.peopleArr &&
        this.peopleArr.length &&
        this.peopleArr.map((val, index) => {
          if (val.groupId) {
            // return val.groupId
            return val.id
          } else {
            return val.controlTeamId
          }
        })
      console.log('id', teamIds)
      let teamNames =
        this.peopleArr &&
        this.peopleArr.length &&
        this.peopleArr.map((val, index) => {
          if (val.groupId) {
            // return val.groupName
            return val.id
          } else {
            // return val.officeName
            return val.controlTeamName
          }
        })
      console.log('name', teamNames)
      // 人员的userID
      let planPersonUserId =
        this.peopleArr &&
        this.peopleArr.length &&
        this.peopleArr.map((val, index) => {
          // return val.userId
          return val.id
        })
      // 训责人员
      // console.log(peopleId)
      // console.log(peopleName)
      // console.log(planPersonUserId,'planPersonUserId')
      // 任务点校验
      if (this.seelctTaskArr.length == 0) {
        this.$message.error('巡检点不能为空！')
        this.saveLoading = false
        this.completeLoading = false
        return
      }
      // 校验执行方式
      if (formInline.executeMode == '1') {
        if (!teamId && !this.distributionTeamId && !this.distributionTeamName) {
          this.$message.error('小组不能为空！')
          this.saveLoading = false
          this.completeLoading = false
          return
        }
      }
      if (formInline.executeMode == '2') {
        if (!peopleId && !this.distributionPeopleId && !this.distributionPeopleName) {
          this.$message.error('人员不能为空！')
          this.saveLoading = false
          this.completeLoading = false
          return
        }
      }
      if (formInline.cycleRole !== '' && formInline.cycleRole > 99) {
        this.$message.error('频次不能大于99！')
        this.saveLoading = false
        this.completeLoading = false
        return
      }
      let reg = /^[0]+[0-9]*$/g
      if (reg.test(formInline.minInterval)) {
        console.log(formInline.minInterval)
        this.$message.error('最小间隔格式不正确！')
        this.saveLoading = false
        this.completeLoading = false
        return
      }
      if (Number(formInline.minInterval) > 1440) {
        this.$message.error('最小间隔不能超过1440！')
        this.saveLoading = false
        this.completeLoading = false
        return
      }
      this.blockLoading = true
      const newTeamIds = [], newTeamNames = []
      for (let i = 0; i < teamIds.length; i++) {
        if (newTeamIds.indexOf(teamIds[i]) == -1) {
          newTeamIds.push(teamIds[i])
        }
      }
      for (let i = 0; i < teamNames.length; i++) {
        if (newTeamNames.indexOf(teamNames[i]) == -1) {
          newTeamNames.push(teamNames[i])
        }
      }
      this.pageLoading = true
      let data = {
        id: formInline.id,
        planName: formInline.planName,
        planTypeId: formInline.planTypeId, // 计划类型id
        planTypeName: formInline.planTypeName,
        executeMode: formInline.executeMode, // 执行方式
        distributionTeamId: teamId ? teamId.join(',') : teamIds ? newTeamIds.join(',') : this.distributionTeamId || '', // 班组id
        distributionTeamName: teamName ? teamName.join(',') : teamNames ? newTeamNames.join(',') : this.distributionTeamName || '', // 班组名称
        planPersonCode: peopleId ? peopleId.join(',') : this.distributionPeopleId || '',
        planPersonName: peopleName ? peopleName.join(',') : this.distributionPeopleName || '',
        planPersonUserId: planPersonUserId ? planPersonUserId.join(',') : '',
        planPersonUserName: peopleName ? peopleName.join(',') : '',
        cycleType: formInline.cycleType, // 周期类型
        locationFlag: formInline.locationFlag, // 是否定位
        scanFlag: formInline.scanFlag, // 是否扫码
        sortFlag: formInline.sortFlag, // 是否按顺序执行
        // 开始日期    周期类型为  单次，每周，每月、季度（开始月份-开始日期）、全年
        startDate:
          formInline.cycleType == '3'
            ? formInline.startMonth + '-' + formInline.startDate
            : formInline.cycleType == '8' || formInline.cycleType == '5'
              ? moment(formInline.startDate).format('YYYY-MM-DD')
              : formInline.startDate,
        // 开始时间和结束时间  周期类型为：单次、每日、 每周、每月、季度
        startTime: formInline.timeLine[0],
        endTime: formInline.timeLine[1],
        // 完成期限  周期类型为：单次、每周、每月、季度、全年
        finalTime: formInline.finalTime,
        // 频次  周期类型为：每日
        cycleRole: formInline.cycleRole,
        // 最小间隔  周期类型为：每日
        minInterval: formInline.minInterval,
        remarks: formInline.remarks,
        maintainPlanRegions: JSON.stringify(this.seelctTaskArr),
        saveType: types, // 1，暂存，，，2，提交送审  确定按钮
        maintainType: '1' // 保养类型 用不到
      }
      ;(data.companyCode = obj.companyCode), (data.companyName = obj.companyName)
      this.$api.addOrUpdateMaintainPlans(data).then((res) => {
        this.saveLoading = false
        this.completeLoading = false
        const { data, code, message } = res
        if (code == 200) {
          this.pageLoading = false
          this.$message.success(message)
          this.blockLoading = false
          this.$router.go(-1)
        }
      })
    },
    // 查询详情
    _getMaintainPlanDetail() {
      let data = {
        id: this.query.id
      }
      this.$api.getMaintainPlanDetail(data).then((res) => {
        const { code, data, message } = res
        if (code == '200') {
          console.log('====', data.data.maintainPlanRegions)
          this.maintainPlanRegions = data.data.maintainPlanRegions
          this.formInline.executeMode = data.data.executeMode
          this.formInline.planTypeId = data.data.planTypeId
          this.formInline.cycleType = data.data.cycleType
          this.formInline.finalTime = data.data.finalTime
          this.formInline.sortFlag = data.data.sortFlag
          if (this.title == '计划复制') {
            this.formInline.planName = ''
          } else {
            this.formInline.planName = data.data.planName
          }
          if (data.data.cycleType == '3') {
            this.formInline.startMonth = parseInt(data.data.startDate.substring(0, 1))
            this.formInline.startDate = data.data.startDate.split('-')[1]
          } else {
            this.formInline.startDate = data.data.startDate
          }
          this.formInline.planTypeName = data.data.planTypeName
          this.formInline.cycleRole = data.data.cycleRole
          this.formInline.minInterval = data.data.minInterval

          this.formInline.remarks = data.data.remarks
          this.formInline.locationFlag = data.data.locationFlag
          this.formInline.scanFlag = data.data.scanFlag
          let teamArrId = data.data.distributionTeamId ? data.data.distributionTeamId.split(',') : [], teamArrName = data.data.distributionTeamName ? data.data.distributionTeamName.split(',') : []
          if (teamArrId.length > 0 && teamArrName.length > 0) {
            this.distributionTeamArr = teamArrId.map((value, i) => ({
              teamCode: value,
              teamName: teamArrName[i]
            }))
          }
          this.deptArrAll = [...this.distributionTeamArr]

          let peopleArrId = data.data.planPersonCode ? data.data.planPersonCode.split(',') : [], peopleArrName = data.data.planPersonName ? data.data.planPersonName.split(',') : []
          if (peopleArrId.length > 0 && peopleArrName.length > 0) {
            this.distributionPeopleArr = peopleArrId.map((value, i) => ({
              staffId: value,
              staffName: peopleArrName[i]
            }))
          }
          this.peopleArr = [...this.distributionPeopleArr]

          this.formInline.timeLine = [data.data.startTime, data.data.endTime]
          this.tableData = data.data.maintainPlanRegions
          if (this.tableData.length > 0) {
            this.tableData.forEach((val, index) => {
              data.data.maintainPlanRegions.forEach((v, i) => {
                if (val.taskPointId == v.taskPointId) {
                  val.projectName = v.taskBookName
                }
              })
              val.id = val.taskPointId
            })
          }
          this.$forceUpdate()
        }
      })
    },
    // 选择
    handleSelectionChangeDialog(val) {
      this.multipleSelection = val
    },
    // 获取任务点数据
    multipleSelectionFn(data) {
      console.log('巡检点数据', data, '已存任务书列表', this.maintainPlanRegions)
      // this.tableData = data;
      // if (this.query.type == 'edit') {
      for (let i = 0; i < data.length; i++) {
        if (data[i] && this.maintainPlanRegions[i] && data[i].taskPointCode == this.maintainPlanRegions[i].taskPointCode) {
          data[i].projectId = this.maintainPlanRegions[i].taskBookId
          data[i].projectName = this.maintainPlanRegions[i].taskBookName
          // data[i].locationPointName = this.maintainPlanRegions[i].locationPointName
        }
      }
      data.filter((item) => {
        this.maintainPlanRegions.find((i) => {
          if (item.taskPointCode == i.taskPointCode) {
            item.projectId = i.taskBookId
            item.projectName = i.projectName
          }
        })
      })
      console.log('更新后表格数据', data)
      this.tableData = data
      // }
      // //解决重复选择列表任务点编号重复问题
      // if(data.length&&this.tableData.length){ //编辑时已经回显之前的任务点列表，再次选择新的任务点，解决重复
      //     this.tableData.forEach((v,i)=>{
      //       data.filter((item,index)=>{
      //         if(item.taskPointCode == v.taskPointCode){
      //           data.splice(index,1)
      //         }
      //       })
      //     })
      // }
      // let tableDatas = [...data,...this.tableData]
      // this.tableData1 =   tableDatas
      // //去重
      // for (var i = 0; i < tableDatas.length; i++) {
      //   if (this.tableData.indexOf(tableDatas[i]) === -1) {
      //     this.tableData.push(tableDatas[i])
      //   }
      // }
      // console.log(this.tableData)
    },
    // 添加任务书
    addRowTask(row) {
      console.log(row)
      this.selectTaskPointRow = row
      this.dialogVisibleSk = !this.dialogVisibleSk
      this.$nextTick(() => {
        this.$refs.rowTask.$refs.materialTable.clearSelection()
      })
    },
    // 获取任务书数据
    multipleSelectionTaskFn(data) {
      const item = {
        id: data[0].id,
        createPersonCode: data[0].createPersonCode,
        createPersonName: data[0].createPersonName,
        createTime: data[0].createTime,
        equipmentTypeName: data[0].equipmentTypeName,
        taskBookId: data[0].id,
        projectExplain: data[0].projectExplain,
        projectName: data[0].projectName,
        taskPointCode: this.selectTaskPointRow.taskPointCode,
        useState: data[0].useState,
        taskPointTypeName: this.selectTaskPointRow.taskPointTypeName,
        taskPointName: this.selectTaskPointRow.taskPointName
      }
      if (this.maintainPlanRegions.length > 0) {
        const repeatItem = this.maintainPlanRegions.find((i) => {
          if (i.taskPointCode == item.taskPointCode) {
            console.log(i)
            item.id = i.id
            item.sourceId = i.sourceId
            item.taskPointId = i.taskPointId
            item.planId = i.planId
            item.locationPointCode = i.locationPointCode
            item.locationPointId = i.locationPointId
            item.locationPointName = i.locationPointName
            item.customText = i.customText
            return item
          }
        })
        if (repeatItem) {
          this.maintainPlanRegions.filter((i, ind) => {
            if (i.taskPointCode == repeatItem.taskPointCode) {
              this.maintainPlanRegions[ind] = item
            }
          })
          // this.maintainPlanRegions.push(item)
        } else {
          this.maintainPlanRegions.push(item)
        }
      } else {
        this.maintainPlanRegions.push(item)
      }
      this.selectTaskPointRow.projectName = item.projectName
      this.selectTaskPointRow.projectId = item.id
    },
    // 全选
    selectAllMed() {
      this.selectAll = !this.selectAll
    },
    // 删除任务点
    deleteTaskPoint() {
      this.$confirm('确认删除该巡检点?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (this.multipleSelection.length && this.tableData.length) {
          this.multipleSelection.forEach((val, indx) => {
            this.tableData.forEach((item, index) => {
              if (val.taskPointCode == item.taskPointCode) {
                if (this.selectAll) {
                  this.maintainPlanRegions = []
                  this.selectAll = false
                } else {
                  this.maintainPlanRegions.find((i, ind) => {
                    if (i.taskPointCode == item.taskPointCode) {
                      this.maintainPlanRegions.splice(ind, ind + 1)
                    }
                  })
                }
                this.tableData.splice(index, this.multipleSelection.length)
              }
            })
            if (val.projectName) {
              // 清空删除行的任务书
              val.projectName = ''
            }
          })
        }
        // 解决任务点列表选中与否的状态
        this.$refs.taskPoints.$refs.materialTable.clearSelection() // 先取消删所有的选中状态
        if (this.tableData.length > 0) {
          this.tableData.forEach((v, i) => {
            this.$refs.taskPoints.$refs.materialTable.toggleRowSelection(v, true) // 然后让未删除的列表显示选中状态
          })
        } else {
          this.$refs.taskPoints.$refs.materialTable.clearSelection() // 先取消删所有的选中状态
        }
      })
    },
    setMonths() {
      let months = []
      for (let i = 1; i <= 30; i++) {
        months.push({
          value: i
        })
      }
      return months
    },
    // 计划类型
    _findDictionaryTableList() {
      let data = {
        dictCode: '',
        dictName: '',
        pageNo: 1,
        pageSize: 99999,
        dictType: 'plan_type'
      }
      this.$api.findDictionaryTableList(data).then((res) => {
        const { code, data, message } = res
        if (code == 200) {
          this.planTypeList = data.list
        } else {
          this.$message.error(message)
        }
      })
    },
    // 选择任务点
    selectTask() {
      this.dialogVisibleWz = !this.dialogVisibleWz
      this.listData = this.taskPointSelectData
      // 解决任务点列表选中与否的状态
      if (this.$refs.taskPoints.$refs.materialTable) this.$refs.taskPoints.$refs.materialTable.clearSelection() // 先取消删所有的选中状态
      if (this.tableData.length > 0) {
        this.tableData.filter((v, i) => {
          console.log('要选中的点', v)
          this.$nextTick(() => {
            this.$refs.taskPoints.$refs.materialTable.toggleRowSelection(v, true) // 然后让未删除的列表显示选中状态
          })
        })
        this.$refs.taskPoints._findTaskPointList()
      } else {
        if (this.$refs.taskPoints.$refs.materialTable) this.$refs.taskPoints.$refs.materialTable.clearSelection() // 先取消删所有的选中状态
      }
    },
    closeDialogFn() {
      this.dialogVisibleWz = false
    },

    closeDialogSkFn() {
      this.dialogVisibleSk = false
    },
    // 选择部门/班组
    selectDept() {
      this.dialogVisibleDp = !this.dialogVisibleDp
      this.$nextTick(() => {
        this.$refs.selectDept && this.$refs.selectDept.parentComponentFun()
      })
    },
    // 选择人员
    selectPeople() {
      this.dialogVisiblePo = !this.dialogVisiblePo
      this.$nextTick(() => {
        this.$refs.selectPeople && this.$refs.selectPeople.parentComponentFun()
      })
    },
    closeDialogDpFn() {
      this.dialogVisibleDp = false
    },
    closeDialogPoFn() {
      this.dialogVisiblePo = false
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
    },
    // 每页的条数
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.paginationData.currentPage = 1
    }
  }
}
</script>
<style lang="scss" scoped>
.inner {
  overflow: auto;
  margin: 15px;
  padding: 15px 60px;
  width: calc(100% - 30px);
  height: calc(100% - 82px);
  background-color: #fff;

  .topTitle {
    display: flex;
    align-items: center;
    font-weight: 800;
  }
}

.bottomBar {
  height: 52px;
  width: 100%;
  background-color: #fff;
  display: flex;
  justify-content: left;
  align-items: center;

  .bottomWrap {
    padding: 0 16px;
  }
}

.content_box {
  height: 100%;
  padding: 20px;
  background: #fff;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.deptArrAllBox,
.peopleArrBox {
  display: flex;
  margin-bottom: 20px;
  flex-wrap: wrap;
  margin-left: 10px;
}

.isScan {
  margin-left: 20px;
  width: 250px;

  .el-form-item__label {
    width: 120px !important;
  }

  :deep(.el-form-item__label) {
    width: 115px !important;
  }
}

.el-form--inline .el-form-item {
  margin-right: 50px;
}

.line {
  width: 5px;
  height: 20px;
  background: #5188fc;
  margin-right: 8px;
}

.form-inline .dayNum {
  :deep(.el-form-item__label) {
    width: 70px !important;
  }
}

.dateNum {
  margin-right: 80px;
}

.project-textarea textarea {
  height: 120px;
}

.sion-icon {
  cursor: pointer;
  padding-left: 3px;
  color: #5188fc;
}

.icon-disabled {
  cursor: not-allowed;
}

:deep(.el-form-item__label) {
  width: 100px !important;
}

.form-inline {
  .el-input,
  .el-select,
  .el-cascader {
    width: 300px;
  }
}

.form-inline {
  width: 100%;
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  .itemLabel {
    display: flex;
  }
}

.form-inline .width_lengthen {
  width: 300px;
}

:deep(.el-upload-dragger) {
  width: 255px;
  height: 164px;
}

:deep(.sino-vertical-file>.el-upload__tip) {
  top: -43px;
  left: 57px;
  color: #ccc;
}
</style>
