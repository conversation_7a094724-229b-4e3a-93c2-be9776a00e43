<template>
    <PageContainer :footer="false">
        <div slot="content" class="table-content">
            <div class="addAlarmConfig-content-title"><i class="el-icon-arrow-left" @click="goBack"></i>详情</div>
            <div class="content_box">
                <el-form ref="jobsData" :model="jobsData" :inline="true" class="form-inline" label-width="120px">
                    <el-row :gutter="20">
                        <el-col :md="6">
                            <el-form-item label="机器人名称：">
                                <span>{{ jobsData.robotName }}</span>
                            </el-form-item>
                        </el-col>
                        <el-col :md="6">
                            <el-form-item label="机器人类型：">
                                <span>{{ jobsData.robotModel }}</span>
                            </el-form-item>
                        </el-col>
                        <el-col :md="10">
                            <el-form-item label="开始时间：">
                                <span>{{ jobsData.startTime.split(' ')[0] }}  {{ jobsData.startTime.split(' ')[1]
                                    }}</span>
                            </el-form-item>
                        </el-col>
                        <el-col :md="6">
                            <el-form-item label="发起人：">
                                <span>{{ jobsData.userName }}</span>
                            </el-form-item>
                        </el-col>
                        <el-col :md="6">
                            <el-form-item label="出发地：">
                                <span>{{ jobsData.endPosition }}</span>
                            </el-form-item>
                        </el-col>
                        <el-col :md="6">
                            <el-form-item label="任务类型：">
                                <span> {{ getJobType(jobsData.jobType) }}</span>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <el-timeline>
                    <el-timeline-item v-for="(activity, index) in jobsData.jobs" :key="index"
                        :icon="activity.jobState == 5 ? 'el-icon-success' : 'el-icon-error'"
                        :size="index == 0 ? 'large' : 'large'" type="primary" :color="activity.color">
                        <p class="time">{{ activity.startTime.split(' ')[0] }} {{ activity.startTime.split(' ')[1] }}
                        </p>
                        <div class="continer">
                            <span class="item"> {{ activity.endPosition }}</span>&nbsp;&nbsp;&nbsp;
                            <span class="item">操作人 : {{ activity.endUserName }}</span>
                            <br />
                            <span class="item"> {{ getJobState(activity.jobState) }}</span>
                        </div>
                    </el-timeline-item>
                </el-timeline>
            </div>
        </div>
    </PageContainer>
</template>
<script>
export default {
    name: 'agvStatisticAnalysisDetails',
    data() {
        return {
            jobsData: {},
        }
    },

    mounted() {
        this.$nextTick(() => {
            this.jobsData = JSON.parse(localStorage.getItem('jobsData'));
            console.log(this.jobsData, ' this.jobsData');

        })
    },
    methods: {
        // 返回
        goBack() {
            this.$router.go(-1)
        },
        getJobState(state) {
            switch (state) {
                case 0:
                    return '全部';
                case 1:
                    return '任务队列中';
                case 2:
                    return '任务下发中';
                case 3:
                    return '任务执行中';
                case 4:
                    return '任务到达';
                case 5:
                    return '任务完成';
                case 6:
                    return '取消任务';
                case 7:
                    return '电量低终止';
                case 8:
                    return '无消毒液终止';
                case 9:
                    return '新任务终止';
                case 10:
                    return '系统设置结束';
                case 11:
                    return '资源不存在导致取消任务';
                case 12:
                    return '任务过期取消任务';
                case 13:
                    return '任务创建失败';
                case 14:
                    return 'APP异常';
                case 15:
                    return 'App放餐失败';
                case 16:
                    return 'APP上报的任务执行超时';
                case 17:
                    return '抽水传感器异常终止';
                default:
                    return '';
            }
        },
        getJobType(state) {
            switch (state) {
                case 0:
                    return '全部';
                case 10:
                    return '配送任务';
                case 20:
                    return '呼叫任务';
                case 30:
                    return '返程任务';
                case 40:
                    return '等待任务';
                case 50:
                    return '充电任务';
                case 60:
                    return '电梯等待区域任务';
                case 70:
                    return '管制等待区域任务';
                case 80:
                    return '定时消毒任务';
                case 90:
                    return '到跨楼宇等待位置任务';
                case 100:
                    return '即时消毒任务';
                case 110:
                    return '查房任务';
                case 120:
                    return '退药任务';
                case 130:
                    return '补位任务';
                case 140:
                    return '补货任务';
                case 150:
                    return '送餐任务';
                case 160:
                    return '回收任务';
                case 170:
                    return '宣教任务';
                default:
                    return '';
            }
        },
    }
}
</script>

<style lang="scss" scoped>
.table-content {
    background: #fff;
    border-radius: 4px;
    height: calc(100% - 0px);
    padding: 10px;
}

.addAlarmConfig-content-title {
    border-bottom: 1px solid #e4e7ed;
    padding: 13px 24px;
    color: #333333;

    .el-icon-arrow-left {
        cursor: pointer;
    }
}

.content_box {
    height: calc(100% - 30px);
    margin-top: 10px;
    padding: 20px 25px 25px;
    background: #fff;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
}

.time {
    margin-bottom: 5px;
}
</style>