<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="content_box">
        <el-form ref="formInline" :model="formInline" :inline="true" class="form-inline" label-width="100px" :rules="rules">
          <el-form-item label="定位点名称" prop="locationPointName">
            <el-input
              v-model.trim="formInline.locationPointName"
              :readonly="readonly"
              placeholder="请输入定位点名称"
              maxlength="50"
              :class="[query.type == 'details' ? 'detailClass' : 'width_lengthen']"
            ></el-input>
          </el-form-item>
          <el-form-item label="定位点编码" prop="locationPointCode">
            <el-input
              v-model.trim="formInline.locationPointCode"
              :readonly="readonly"
              placeholder="请输入定位点编码"
              maxlength="50"
              :class="[query.type == 'details' ? 'detailClass' : 'width_lengthen']"
            ></el-input>
          </el-form-item>
          <el-form-item label="定位点类型" prop="locationPointType">
            <el-radio-group v-model.trim="formInline.locationPointType" :disabled="query.type == 'details'">
              <el-radio v-for="item in locationPointTypeList" :key="item.value"  :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <!-- 蓝牙定位 -->
          <div v-if="formInline.locationPointType == '1'">
            <el-form-item label="设备UUID" prop="deviceUuid">
              <el-input
                v-model.trim="formInline.deviceUuid"
                placeholder="请输入设备UUID"
                maxlength="50"
                :class="[query.type == 'details' ? 'detailClass' : 'width_lengthen']"
              ></el-input>
            </el-form-item>
            <el-form-item label="设备minor" prop="deviceMinor">
              <el-input
                v-model.trim="formInline.deviceMinor"
                :readonly="readonly"
                placeholder="请输入设备minor"
                maxlength="50"
                :class="[query.type == 'details' ? 'detailClass' : 'width_lengthen']"
              ></el-input>
            </el-form-item>
            <el-form-item label="设备major" prop="deviceMajor">
              <el-input
                v-model.trim="formInline.deviceMajor"
                :readonly="readonly"
                placeholder="请输入设备major"
                maxlength="50"
                :class="[query.type == 'details' ? 'detailClass' : 'width_lengthen']"
              ></el-input>
            </el-form-item>
          </div>
          <!-- RFID标签 -->
          <div v-else>
            <el-form-item label="RFID编码" prop="rfidCode">
              <el-input
                v-model.trim="formInline.rfidCode"
                placeholder="请输入RFID编码"
                maxlength="50"
                :class="[query.type == 'details' ? 'detailClass' : 'width_lengthen']"
              ></el-input>
            </el-form-item>
          </div>
          <div style="width: 100%; display: flex;">
            <el-form-item label="备注" class="itemLabel">
              <el-input
                v-if="query.type !== 'details'"
                v-model.trim="formInline.remarks"
                style="width: 400px;"
                type="textarea"
                :class="[query.type == 'details' ? 'detailClass' : 'project-textarea']"
                placeholder="请输入备注，限制200字以内"
                show-word-limit
                :autosize="{ minRows: 4, maxRows: 6 }"
                maxlength="200"
              ></el-input>
              <div v-else style="width: 400px; height: 96px; color: #606266;">{{ formInline.remarks || '暂无' }}</div>
            </el-form-item>
          </div>
        </el-form>
      </div>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">关闭</el-button>
      <el-button v-if="$route.query.type != 'details'" type="primary" @click="submitForm()">保存</el-button>
    </div>
  </PageContainer>
</template>

<script>
export default {
  name: 'addLocation',
  beforeRouteEnter(to, from, next) {
    if (to.meta.title) {
      const typeList = {
        add: '新增定位点',
        edit: '编辑定位点',
        details: '定位点详情'
      }
      to.meta.title = typeList[to.query.type] ?? '定位点详情'
    }
    next()
  },
  async beforeRouteLeave(to, from, next) {
    if (!['locationPoint'].includes(to.name)) {
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      readonly: false,
      formInline: {
        locationPointName: '',
        locationPointCode: '',
        locationPointType: '1',
        deviceUuid: '',
        deviceMinor: '',
        deviceMajor: '',
        remarks: '',
        rfidCode: '', // RFID编码
        id: '' // 模板id，修改必传
      },
      parentDictList: [],
      unitOptions: [],
      termTypeOptions: [],
      query: {},
      id: '',
      blockLoading: false,
      locationPointTypeList: [
        {
          label: '蓝牙标签',
          value: '1'
        }
        // {
        //   label: 'RFID标签',
        //   value: '2'
        // }
      ], // 定位点类型列表
      rules: {},
      currentPage: 1,
      total: 0,
      fileList: [],
      parentName: '',
      updateId: '',
      // eslint-disable-next-line vue/no-dupe-keys
      title: '新增定位点管理'
    }
  },
  activated() {
    this.init()
  },
  created() {
    if (!this.$store.state.keepAlive.list.includes('departmentManage')) {
      this.init()
    }
  },
  methods: {
    init() {
      Object.assign(this.$data, this.$options.data())
      this.$set(this.$data, 'rules', {
        locationPointName: [{ required: true, message: '请输入定位点名称', trigger: 'change' }],
        locationPointCode: [{ required: true, message: '请输入定位点编码', trigger: 'change' }],
        locationPointType: [{ required: true, message: '请选择定位点类型', trigger: 'change' }],
        deviceUuid: [
          { required: true, message: '请输入设备UUID', trigger: 'change' }
          // {
          //   validator: (rule, value, callback) => {
          //     if (this.query.type == 'add') {
          //       if (this.$route.query.uuids.find((i) => i == value)) {
          //         callback('uuid已存在')
          //       } else {
          //         callback()
          //       }
          //     }
          //     callback()
          //   },
          //   trigger: 'change'
          // }
        ],
        rfidCode: [
          { required: true, message: '请输入RFID编码', trigger: 'change' },
          {
            validator: (rule, value, callback) => {
              if (this.query.type == 'add') {
                if (this.$route.query.rfids.find((i) => i == value)) {
                  callback('RFID编码已存在')
                } else {
                  callback()
                }
              }
              callback()
            },
            trigger: 'change'
          }
        ],
        deviceMinor: [{ required: true, message: '请输入设备minor', trigger: 'change' }],
        deviceMajor: [{ required: true, message: '请输入设备major', trigger: 'change' }]
      })
      this.$nextTick(() => {
        this.$refs.formInline.resetFields()
      })
      this.query = this.$route.query
      if (this.query.id) {
        this.detailId = this.query.id
        this.formInline.id = this.query.id
        this._getLocationPointDetails() // 获取定位点管理详情
      } else if (this.query.type == 'details') {
        this.formInline = JSON.parse(this.query.data)
        this.readonly = true
      }
    },
    _getLocationPointDetails() {
      let data = {
        id: this.detailId
      }
      this.$api.getLocationPointDetails(data).then((res) => {
        const { code, data, message } = res
        if (code == 200) {
          this.formInline = data
        }
      })
    },
    // 点击确定
    submitForm() {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          const { formInline } = this
          let data = {
            locationPointName: formInline.locationPointName,
            locationPointCode: formInline.locationPointCode,
            locationPointType: formInline.locationPointType,
            rfidCode: formInline.rfidCode,
            deviceUuid: formInline.deviceUuid,
            deviceMinor: formInline.deviceMinor,
            deviceMajor: formInline.deviceMajor,
            description: formInline.description,
            remarks: formInline.remarks,
            id: this.formInline.id
          }
          this.blockLoading = true
          let header = {

          }
          if (data.id) {
            header = {
              'operation-type': 2,
              'operation-id': this.formInline.id,
              'operation-name': data.locationPointName
            }
          } else {
            header = {
              'operation-type': 1

            }
          }
          this.$api.addLocationPoint(data, header).then((res) => {
            this.blockLoading = false
            if (res.code == 200) {
              this.$message.success(res.message)
              this.$router.go(-1)
            }
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-textarea {
  textarea {
    height: 100px;
    resize: none;
    // overflow: hidden;
    padding-bottom: 13px;
  }

  .el-input__count {
    height: 12px;
    line-height: 12px;
    background: rgb(255 255 255 / 50%);
  }
}

.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
  padding: 10px;
  overflow-y: auto;
}

.content_box {
  height: calc(100% - 30px);
  margin-top: 10px;
  padding: 20px 25px 25px;
  background: #fff;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.form-inline {
  .el-input,
  .el-select,
  .el-cascader {
    width: 300px;
  }
}

.form-inline .width_lengthen {
  width: 400px;
}

.detailClass ::v-deep .el-input__inner {
  border: none !important;
}

.detailClass ::v-deep .el-textarea__inner {
  border: none;
  resize: none;
}
</style>
