<template>
  <PageContainer  v-if="routeInfo.isFalg == 0">
    <div slot="content" class="role-content" style="height: 100%">
      <div class="content_top">
        <el-tabs v-model="activeName" @tab-click="handleClick" class="tabsMenu">
          <el-tab-pane label="我的试题" name="myQuestions"></el-tab-pane>
          <el-tab-pane label="公开题库" name="openQuestions"></el-tab-pane>
        </el-tabs>
        <div class="btns">
          <el-button type="primary" @click="batchQuestions">批量录题</el-button>
          <el-button type="primary" @click="addQuestion()">逐道录题</el-button>
          <el-button type="primary" :disabled="!checkDataList.length" @click="deleteQuestions()"
            >批量删除</el-button
          >
        </div>
      </div>
      <div class="content_box">
        <div class="open_conter">
          <div class="content_left">
            <div class="title">试题分类</div>
            <div class="tree">
              <el-tree
                ref="tree"
                v-loading="treeLoading"
                style="margin-top: 10px"
                :data="treeData"
                :props="defaultProps"
                :default-expanded-keys="expanded"
                node-key="id"
                highlight-current
                @node-click="handleNodeClick"
              >
                <span class="span-ellipsis" slot-scope="{ node }">
                  <span :title="node.label">{{ node.label }}</span>
                </span>
              </el-tree>
            </div>
          </div>
          <div class="content_right">
            <div class="seachTop">
              <el-select
                v-model="seachForm.quesTypeCode"
                filterable
                placeholder="全部题型"
                style="width: 200px; margin: 0 16px"
              >
                <el-option
                  v-for="item in quesTypeList"
                  :key="item.id"
                  :label="item.dictName"
                  :value="item.id"
                >
                </el-option>
              </el-select>
              <el-select
                v-model="seachForm.courseCode"
                filterable
                placeholder="所属课程"
                style="width: 200px; margin: 0 16px"
              >
                <el-option
                  v-for="item in courseList"
                  :key="item.id"
                  :label="item.courseName"
                  :value="item.id"
                >
                </el-option>
              </el-select>
              <el-input
                v-model="seachForm.topicName"
                style="width: 200px; margin: 0 16px"
                placeholder="题目关键字"
              >
              </el-input>
              <el-button type="primary" plain @click="resetForm"
                >重置</el-button
              >
              <el-button type="primary" @click="search">查询</el-button>
            </div>
            <div class="table">
              <div class="question-content" v-loading="tableLoading">
                <div class="questionList_top">
                  <el-checkbox
                    :indeterminate="isIndeterminate"
                    v-model="checkAll"
                    @change="checkAllBtn"
                    >全选</el-checkbox
                  >
                  <span @click="allFold">{{
                    isAllFold ? "全部展开" : "全部折叠"
                  }}</span>
                </div>
                <div
                  v-for="(item, index) in exercisesList"
                  :key="item.id"
                  :name="item.id"
                  :class="['exercisesItem', item.isExpand ? 'expand' : '']"
                  :ref="'exercisesItem' + index"
                >
                  <div class="exercisesTop">
                    <div class="left">
                      <el-checkbox
                        v-model="item.checked"
                        @change="checkItem()"
                      ></el-checkbox>
                      <div class="exercisesType">
                        {{
                          item.type == "1"
                            ? "单选题"
                            : item.type == "2"
                            ? "多选题"
                            : "判断题"
                        }}
                      </div>
                      <span>({{ item.id }})</span>
                    </div>
                    <div class="right">
                      <i class="el-icon-edit" @click="edit(item)"></i>
                      <i
                        class="el-icon-delete"
                        @click="deleteQuestions('one', item.id)"
                      ></i>
                      <div class="line"></div>
                      <span @click="isExpandBtn(item, index)">{{
                        item.isExpand ? "折叠" : "展开"
                      }}</span>
                    </div>
                  </div>
                  <div :class="['exercisesName', item.isExpand ? '' : 'title']">
                    {{ item.topic }}
                  </div>
                  <el-radio-group
                    v-if="item.type == '1'"
                    class="radio"
                    v-model="item.answer"
                    disabled
                  >
                    <el-radio
                      v-for="(item, index) in item.options"
                      :key="index"
                      :label="item.id"
                      >{{ item.id }}. {{ item.label }}</el-radio
                    >
                  </el-radio-group>
                  <el-checkbox-group
                    v-if="item.type == '2'"
                    v-model="item.answer"
                    class="radio"
                    disabled
                  >
                    <el-checkbox
                      v-for="(item, index) in item.options"
                      :key="index"
                      :label="item.id"
                      >{{ item.id }}. {{ item.label }}</el-checkbox
                    >
                  </el-checkbox-group>
                  <p>答案：{{ item| getAnswer }}</p>
                  <p>
                    解析：
                    {{ item.analysis }}
                  </p>
                </div>
              </div>
            </div>
            <div class="contentTable-footer">
              <el-pagination
                style="margin-top: 3px"
                :current-page="paginationData.pageNo"
                layout="total, sizes, prev, pager, next, jumper"
                :total="paginationData.total"
                :page-size="paginationData.pageSize"
                :page-sizes="[15, 30, 50, 100]"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              ></el-pagination>
            </div>
          </div>
        </div>
      </div>
      <!-- 批量录题 -->
      <el-dialog
        class="changeStatusDialog"
        title="批量录题"
        :visible.sync="dialogVisible"
        width="50%"
        :before-close="handleClose"
      >
        <el-form
          label-width="140px"
          :model="formInfo"
          :rules="rules"
          ref="formInfo"
          class="formInfo"
        >
          <el-form-item label="所属科目" prop="subjectId">
            <el-cascader
              v-model="formInfo.subjectId"
              clearable
              class="sino_sdcp_input mr15"
              :options="treeData"
              :props="props"
              placeholder="请选择类型"
              @change="cascaderChange"
              style="width: 300px"
            ></el-cascader>
          </el-form-item>
          <el-form-item label="知识点所属课程" prop="courseCode">
            <el-select
              v-model="formInfo.courseCode"
              filterable
              placeholder="全部题型"
              style="width: 300px"
              @change="courseChange"
            >
              <el-option
                v-for="item in courseList"
                :key="item.id"
                :label="item.courseName"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="">
            <el-upload
              ref="uploadFile2"
              drag
              multiple
              class="mterial_file file"
              action="string"
              :file-list="fileQuestions"
              :http-request="httpRequest"
              accept=".excel,.xlsx"
              :limit="1"
              :on-exceed="handleExceed"
              :before-upload="beforeAvatarUpload"
              :on-remove="handleRemove"
              :on-change="fileChange"
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text" style="top: 100px">
                将文件拖到此处，或
                <em>点击上传</em>
              </div>
              <div slot="tip" class="el-upload__tip">支持上传单个Excel文件</div>
            </el-upload>
          </el-form-item>
          <span>下载模板文件：</span>
          <span
            v-for="(item, index) in exTypeList"
            :key="index"
            @click="downloadTemplate(item.id)"
            class="exType"
            >{{ item.label }}</span
          >
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="handleClose">取消</el-button>
          <el-button type="primary" @click="batchOk">确认</el-button>
        </span>
      </el-dialog>
    </div>
  </PageContainer>
   <div v-else>
  <permissionPrompt></permissionPrompt>
  </div>
</template>

<script type="text/ecmascript-6">
import permissionPrompt from "@/views/safety/courseIndex/components/permissionPrompt.vue";
import axios from 'axios'
import moment from 'moment'
import qs from "qs";
export default {
  name: 'questionBankManagement',
  components: {permissionPrompt},
  data() {
    return {
      routeInfo:'',
      moment,
      activeName: 'myQuestions',
      treeLoading: false,
      treeData: [],
      defaultProps: {
        children: "childList",
        label: "name",
      },
      props: {
        children: "childList",
        label: "name",
        value: "id",
        checkStrictly: true,
        emitPath: false,
      },
      expanded: [],
      seachForm: {
        quesTypeCode: "",
        courseCode: "",
        topicName:'',
      },
      quesTypeList: [
        {
          id: 1,
          dictName: "单选题",
        },
        {
          id: 2,
          dictName: "多选题",
        },
        {
          id: 3,
          dictName: "判断题",
        }
      ],
      courseList:[],
      paginationData:{
        pageNo:1,
        pageSize:15,
        total:0
      },
      exercisesList: [],
      tableLoading: false,
      mySubmitFlag:false,
      subjectId:'',
      isAllFold:true,
      isIndeterminate: false,
      checkAll: false,
      checkDataList:[],
      dialogVisible:false,
      fileQuestions:[],
      exTypeList: [
        {
          id: "1",
          label: "单选题",
        },
        {
          id: "2",
          label: "多选题",
        },
        {
          id: "3",
          label: "判断题",
        },
      ],
      formInfo:{
        subjectId:'',
        courseCode:'',
        courseName:''
      },
      rules: {
        subjectId: [
          { required: true, message: "请选择所属科目", trigger: "change" },
        ],
        courseCode: [
          { required: true, message: "请选择课程名称", trigger: "change" },
        ],
      },
    }
  },
  beforeRouteEnter(to,from,next) {
    next((vm) => {
      if(from.name == "addQuestion") {
        vm.$route.meta.isrefer = true;
      } else {
        vm.$route.meta.isrefer = false;
      }
    })
  },
  created() {
    // if (this.$route.query) {
    //   sessionStorage.setItem("routeInfo", JSON.stringify(this.$route.query));
    //   this.routeInfo = JSON.parse(sessionStorage.getItem("routeInfo"));
    // }
    this.routeInfo = JSON.parse(sessionStorage.getItem("routeInfo"));
      if (this.routeInfo.isFalg == 1) {
      return
    }
    this.getTblleList();
    this.allCourseList()
     this.getQuetions()
  },
  activated(){
    this.getQuetions()
  },
  filters:{
    getAnswer(val){
      if(val.type=='3'){
        return val.answer=='1'?'正确':'错误'
      }else if(val.type=='2'){
        return val.answer.toString()
      }else{
        return val.answer
      }
    }
  },
  methods: {
    // 获取课程列表
    allCourseList(id) {
      this.$api.allCourseList({ subjectId: id || "" }).then((res) => {
        if (res.code == 200) {
          this.courseList = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 获取试题分类列表
    getTblleList() {
      this.treeLoading = true
      let data = {
        pageNo: 1,
        pageSize: 9999,
      };
      this.$api.ipsmFicationlList(data).then((res) => {
        if (res.code == 200) {
          this.treeData = res.data.records;
        } else {
          this.$message.error(res.message);
        }
        this.treeLoading = false
      });
    },
    // 选择科目
    cascaderChange(val) {
      this.allCourseList(val);
    },
    // 选择课程
    courseChange(val) {
      let obj= this.courseList.find(item=>item.id==val)
      this.formInfo.courseName =obj?.courseName
    },
    getQuetions(){
      this.tableLoading = true
      let data = {
        type:this.seachForm.quesTypeCode,
        courseId:this.seachForm.courseCode,
        subjectId:this.subjectId,
        topic:this.seachForm.topicName,
        systemCode:this.routeInfo.systemCode,
        pageNo: this.paginationData.pageNo,
        pageSize: this.paginationData.pageSize,
        isDraft:false,//是否查看
      };
      if(this.activeName=='myQuestions'){
        data.moduleType ='2'
        data.createBy =this.routeInfo.userId
      }else{
        data.moduleType ='1'
      }
      this.$api.getQuestionsList(data).then((res) => {
        if (res.code == 200) {
          res.data.list.forEach(i=>{
            i.options = JSON.parse(i.options)
              if(i.type == '2'){
                i.answer = i.answer.split(',')
              }
            i.isExpand=false
            i.checked=false
          })
          this.exercisesList = res.data.list;
          this.paginationData.total =res.data.total
        } else {
          this.$message.error(res.message);
        }
        this.tableLoading = false
      });
    },
    // 切换tabs
    handleClick(tab, event) {
      this.exercisesList=[]
      this.getTblleList()
      this.subjectId=''
      this.resetForm()
    },
    resetForm(){
      this.paginationData.pageNo = 1
      this.paginationData.pageSize = 15
      this.timeLine=[]
      this.seachForm={
        quesTypeCode: "",
        courseCode: "",
        topicName:''
      }
      this.getQuetions()
    },
    search(){
      this.paginationData.pageNo = 1
      this.getQuetions()
    },
    isExpandBtn(item,index){
      item.isExpand=!item.isExpand
    },
    // 逐题添加
    addQuestion(){
      this.$router.push({
        path:'addQuestion'
      })
    },
    //编辑试题
    edit(item){
      this.$router.push({
        path:'addQuestion',
        query:{
          type:'edit',
          id:item.id
        }
      })
    },
    // 删除试题
    deleteQuestions(type,ids){
      this.$confirm('删除后将无法恢复，是否确定删除？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let str =type=='one'?'deleteOneQuestions':'deleteQuestions'
      let data = {
        userId:this.routeInfo.userId
      }
      if(type=='one'){
        data.id=ids
      }else{
        data.ids =this.checkDataList.map(i=>i.id).join(',')
      }
      this.$api[str](data).then((res) => {
        if (res.code == 200) {
          this.getQuetions()
          this.$message.success(res.msg)
          if(type!='one'){
            this.checkDataList = []
            this.isIndeterminate = false;
            this.checkAll = false
          }
        } else {
          this.$message.error(res.msg);
        }
      });
      })
    },
    // 全部展开
    allFold(){
      this.exercisesList.forEach(item=>item.isExpand=!item.isExpand)
      this.isAllFold=!this.isAllFold
    },
    // 全选
    checkAllBtn(val) {
      if (val) {
        this.exercisesList.forEach((item) => {
          item.checked = true;
        });
        this.checkDataList =this.exercisesList
      }else{
        this.exercisesList.forEach((item) => {
          item.checked = false;
        });
        this.checkDataList = []
      }
      this.isIndeterminate = false;
    },
    checkItem() {
      this.checkDataList=[]
      this.exercisesList.forEach(i=>{
        if(i.checked){
          this.checkDataList.push(i)
        }
      })
      this.checkAll = this.checkDataList.length === this.exercisesList.length;
      this.isIndeterminate = this.checkDataList.length > 0 && this.checkDataList.length < this.exercisesList.length;
    },
    handleNodeClick(data){
      this.paginationData.pageNo = 1
      this.subjectId = data.id
      this.getQuetions()
    },
    handleCurrentChange(val) {
      this.paginationData.pageNo = val
      this.getQuetions()
    },
    // 每页的条数
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.paginationData.pageNo = 1
      this.getQuetions()
    },
    batchQuestions(){
      this.dialogVisible=true
    },
    handleClose(){
      this.dialogVisible=false
      this.$refs.formInfo.resetFields();
      this.formInfo.courseName=''
      this.fileQuestions=[]
      this.allCourseList()
    },
    // 下载试题类型模板
    downloadTemplate(id){
      axios({
        method: 'get',
        url: __PATH.BASE_URL_LABORATORY + 'question/template',
        params: {type:id},
        responseType: 'blob',
        headers: {
          "Content-Type": "application/json",
          token:this.routeInfo?this.routeInfo.token:''
        }
      })
        .then((res) => {
          console.log(res,'res');
          let name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
          let blob = new Blob([res.data]) // { type: "application/vnd.ms-excel" }
          let url = window.URL.createObjectURL(blob) // 创建一个临时的url指向blob对象
          // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
          let a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
          // 释放这个临时的对象url
          window.URL.revokeObjectURL(url)
        })
        .catch((res) => {
          this.$message.error('导出失败！')
        })
    },
    // 批量确定
    batchOk(){
      this.$refs.formInfo.validate((valid) => {
          if (valid) {
            if(!this.fileQuestions.length){
              return this.$message.error('请先选择模板')
            }
            let formData = new FormData()
            formData.append('hospitalCode', this.routeInfo.hospitalCode)
            formData.append('unitCode', this.routeInfo.unitCode)
            formData.append('subjectId', this.formInfo.subjectId)
            formData.append('systemCode', this.routeInfo.systemCode)
            formData.append('file', this.fileQuestions[0].raw)
            formData.append('courseId', this.formInfo.courseCode)
            formData.append('courseName', this.formInfo.courseName)
            formData.append('isDraft', false)
            formData.append('importType', '0')
            axios({
              method: 'post',
              url:  __PATH.BASE_URL_LABORATORY + 'question/import',
              data: formData,
              headers: {
                "Content-Type": "application/x-www-form-urlencoded;charset=utf8",
                token:this.routeInfo?this.routeInfo.token:''
              }
            }).then(res => {
              if (res.data.code == 200) {
                  this.handleClose()
                  this.getQuetions()
                  this.$message.success(res.data.msg);
              } else {
                this.$message.error(res.data.msg);
              }
            }).catch((res) => {
              this.$message.error('上传失败')
            })
          }
        });

    },
    fileChange(file, fileList,val) {
      this.fileQuestions = fileList
    },
    httpRequest() {},
    handleExceed() {
      this.$message.error("最多上传一个附件");
    },
    beforeAvatarUpload(file) {
      const isLt50M = file.size / 1024 / 1024 < 50;
      if (!isLt50M) {
        this.$message.error("上传图片大小不能超过 50MB!");
        return false;
      }
      if (file.name.indexOf(",") != -1) {
        this.$message.error("非法的文件名");
        return false;
      }
    },
    handleRemove(file, fileList,val) {
      this.fileQuestions = fileList;
    },
  }
}
</script>
<style lang="scss" scoped>
:deep(.el-tabs--left) {
  width: 200px;
  margin: 20px auto 0 auto;
}
.role-content {
  height: 100%;
  border-radius: 10px;
  padding: 16px;
  background: #fff;
}
.content_top {
  position: relative;
  overflow: visible;
  .btns {
    position: absolute;
    right: 0;
    bottom: 0px;
    z-index: 100;
  }
  .tabsMenu {
    margin-bottom: 15px;
    :deep(.el-tabs__item) {
      width: 100px;
    }
  }
  .top_content {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }
}
.content_box {
  height: 100%;
  .exercisesItem {
    height: 100px;
    overflow: hidden;
    background-color: #fff;
    margin-bottom: 16px;
    border-bottom: 4px solid #faf9fc;
    .exercisesTop {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
      .left {
        display: flex;
        align-items: center;
        justify-content: center;
        span {
          color: #7f848c;
        }
      }
      .right {
        color: #ccced3;
        display: flex;
        align-items: center;
        .line {
          width: 2px;
          height: 14px;
          margin: 0 10px 0 26px;
          background-color: #dcdfe6;
        }
        span {
          color: #3562db;
          margin-left: 16px;
          cursor: pointer;
        }
        i {
          color: #3562db;
          cursor: pointer;
          margin-left: 16px;
        }
      }
      .exercisesType {
        width: 58px;
        height: 22px;
        line-height: 22px;
        text-align: center;
        border-radius: 4px;
        color: #86909c;
        background-color: #ededf5;
        margin: 0 10px;
      }
    }
    .exercisesName {
      min-height: 40px;
      line-height: 20px;
      margin-bottom: 16px;
      word-break: break-all;
    }
    .title {
      overflow: hidden;
      -webkit-line-clamp: 2;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
    }
    .el-radio {
      margin-left: 38px;
      font-size: 14px;
      color: #7f848c !important;
      display: flex;
      line-height: 18px;
    }
    p {
      font-size: 14px;
      color: #7f848c !important;
      line-height: 20px;
      margin-bottom: 16px;
    }
  }
  .expand {
    height: auto;
  }
}
.open_conter {
  padding: 16px;
  height: 100%;
  display: flex;
  .content_left {
    flex: 0 0 auto;
    width: 240px;
    border-radius: 4px;
    border: 1px solid #e5e6eb;
    .title {
      margin: 16px;
      font-size: 16px;
    }
    .tree {
      height: calc(100% - 50px);
      overflow: auto;
    }
  }
  .content_right {
    flex: 1;
    min-width: 0;
    margin-left: 24px;
    .seachTop {
      height: 64px;
      background-color: #faf9fc;
      padding: 16px;
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      font-size: 14px;
      .expand {
        color: #3562db;
        font-size: 14px;
      }
    }
    .table {
      height: calc(100% - 150px);
      overflow: auto;
      font-size: 14px;
    }
  }
}
:deep(.el-tabs__nav) {
  width: 100%;
  .el-tabs__item {
    padding: 0 10px;
    width: 50%;
    text-align: center;
  }
}
.question-content {
  height: 100%;
  padding: 0 16px;
  overflow: auto;
  .questionList_top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    span {
      color: #3562db;
      cursor: pointer;
    }
  }
}
.changeStatusDialog {
  .exType {
    color: #3562db;
    margin: 16px 16px 0 0;
    cursor: pointer;
  }
}
.mterial_file {
  margin-bottom: 36px;
}
::v-deep .mterial_file > .el-upload-list {
  position: absolute;
  top: 0px;
  width: 260px;
  margin-left: 360px;
  max-height: 160px;
  // overflow: auto;
}
::v-deep .mterial_file > .el-upload--picture-card {
  border: none;
  width: 300px;
}
::v-deep .mterial_file .el-upload .el-upload-dragger {
  width: 300px;
  height: 148px;
}
::v-deep .mterial_file .el-upload__text {
  position: absolute;
  left: 57px;
  color: #606266;
  font-size: 14px;
  text-align: center;
}
::v-deep .el-radio {
  display: block;
  margin: 10px 0;
  .el-radio__label {
    white-space: normal;
  }
}
::v-deep .el-checkbox {
  display: block;
  margin: 10px 0;
}
::v-deep .el-checkbox-group {
  margin-left: 38px;
}
</style>
