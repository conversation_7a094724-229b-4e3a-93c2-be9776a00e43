<template>
  <div class="el-calendar_updata">
    <div v-if="validatedRange.length === 0" key="no-range" class="el-calendar__body" :class="showModule === 'year' ? 'el-calendar__body--year' : ''">
      <div v-if="showModule === 'year'" class="calendar_head">{{ i18nDate }}</div>
      <date-table ref="dataTable" :date="date" :first-day-of-week="realFirstDayOfWeek" />
    </div>
    <div v-else key="has-range" class="el-calendar__body">
      <div v-if="showModule === 'year'" class="calendar_head">{{ i18nDate }}</div>
      <date-table
        v-for="(range, index) in validatedRange"
        ref="dataTable"
        :key="index"
        :date="range[0]"
        :range="range"
        :hide-header="index !== 0"
        :first-day-of-week="realFirstDayOfWeek"
      />
    </div>
  </div>
</template>
<script>
import { Calendar } from 'element-ui'
export default {
  extends: Calendar,
  props: {
    showModule: {
      type: String,
      default: ''
    }
  },
  data() {
    return {}
  },
  computed: {
    i18nDate() {
      const month = this.date.getMonth() + 1
      return `${this.t('el.datepicker.month' + month)}`
    }
  },
  mounted() {
  },
  methods: {}
}
</script>
<style lang="scss">
.el-calendar_updata {
  height: calc(100% - 40px);

  .calendar_head {
    width: 100%;
    height: 32px;
    line-height: 32px;
    text-align: center;
    background: #f5f5fa;
    // border: 1px solid #eceef5;
  }

  .el-calendar__body {
    height: 100%;

    .el-calendar-table {
      height: 100%;

      thead th {
        height: 36px;
        line-height: 36px;
        background: #f2f5f8;
        border: 1px solid #d8dee7;
        text-align: center;
        font-size: 16px;
        // color: #121f3e;
      }

      .el-calendar-day {
        height: 100%;
        padding: 0;
      }
    }
  }

  .el-calendar__body--year {
    .el-calendar-table {
      border: 1px solid #eceef5;

      td.is-today {
        p {
          border-radius: 50%;
          color: #fff;
          background: #409eff;
        }
      }
    }

    thead {
      border-top: 1px solid #eceef5;
      border-bottom: 1px solid #eceef5;
    }

    thead th,
    tbody td {
      height: 36px;
      line-height: 36px;
      background: #fff !important;
      border: none !important;
      text-align: center;
      font-size: 16px;
      padding: 0;
      // padding-bottom: 100%;
      // width: 40px;
      aspect-ratio: 1;
      // color: #121f3e;
    }
  }
}
</style>
