<template>
  <el-dialog title="添加定位点" width="60%" :visible.sync="anchorPointShow" custom-class="model-dialog" :before-close="closeDialog">
    <div class="camera_content" style="padding: 10px 20px 10px 10px">
      <div class="header_operation">
        <div class="search_box">
          <div class="search_select">
            <el-input v-model="filter.query" placeholder="定位点名称、资产编码、SN"></el-input>
          </div>
        </div>
        <div class="header_btn">
          <el-button type="primary" plain @click="userReset">重置</el-button>
          <el-button type="primary" @click="userQuery">查询</el-button>
        </div>
      </div>
      <div class="table_div">
        <TablePage
          ref="table"
          :tableColumn="tableColumn"
          :data="tableData"
          :pageProps="pageProps"
          :showPage="true"
          height="calc(300px)"
          :pageData="pageinationData"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
          <template #radio="{ row }">
            <el-radio v-model="selectedRow" :label="row.deviceId" name="" @change="handleRadioChange(row)"></el-radio>
          </template>
          <template #coordinate="{ row }">
            <span v-if="row.x && row.y && row.z">{{ `${row.x},${row.y},${row.z}` }}</span>
          </template>
        </TablePage>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="submitAnchorPointDialog">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  props: {
    anchorPointShow: {
      type: Boolean,
      default: false
    },
    echoTableData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      filter: {
        query: '' // 名称
      },
      selectedRow: '',
      checkedData: {},
      tableData: [], // 数据
      pageProps: {
        page: 'pageNo',
        pageSize: 'pageSize',
        total: 'total'
      },
      pageinationData: {
        pageNo: 1,
        pageSize: 15,
        total: 0
      },
      tableColumn: [
        {
          prop: 'radio',
          label: '',
          width: '55',
          slot: 'radio'
        },
        {
          prop: 'deviceName',
          label: '定位点名称',
          align: 'center',
          showOverflowTooltip: true
        },
        {
          prop: 'assetCode',
          label: '资产编码',
          align: 'center',
          showOverflowTooltip: true
        },
        {
          prop: 'assetSn',
          label: 'SN',
          align: 'center',
          showOverflowTooltip: true
        },
        {
          prop: 'assetSubcategoryName',
          label: '类型',
          align: 'center',
          showOverflowTooltip: true
        },
        {
          prop: 'spaceName',
          label: '所在空间',
          align: 'center',
          showOverflowTooltip: true
        },
        {
          prop: 'x',
          label: '坐标',
          align: 'center',
          showOverflowTooltip: true,
          slot: 'coordinate'
        }
      ]
    }
  },
  mounted() {
    this.getStaffListByPage()
  },
  methods: {
    handleRadioChange(row) {
      this.checkedData = row
    },
    // 重置
    userReset() {
      this.filter = {
        query: '' // 名称
      }
      this.userQuery()
    },
    // 点击查询
    userQuery() {
      this.pageinationData.pageNo = 1
      this.getStaffListByPage()
    },
    // 获取列表
    getStaffListByPage() {
      let data = {
        ...this.pageinationData,
        query: this.filter.query
      }
      delete data.total
      this.$api.supplierAssess.getLocatePointList(data).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.list
          this.pageinationData.total = res.data.count
        }
      })
    },
    // 弹框分页
    handleSizeChange(val) {
      this.pageinationData.pageSize = val
      this.getStaffListByPage()
    },
    handleCurrentChange(val) {
      this.pageinationData.pageNo = val
      this.getStaffListByPage()
    },
    submitAnchorPointDialog() {
      if (JSON.stringify(this.checkedData) == '{}') {
        this.$message.error('请选择定位点！')
        return
      }
      if (this.echoTableData.length) {
        this.$confirm('此操作会替换当前已选定位点，是否继续?', '操作确认', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$emit('submitAnchorPointDialog', this.checkedData)
        })
      } else {
        this.$emit('submitAnchorPointDialog', this.checkedData)
      }
    },
    closeDialog() {
      this.$emit('closeAnchorPoint')
    }
  }
}
</script>
<style lang="scss" scoped>
.model-dialog {
  .camera_content {
    margin: 0 auto;
    background: #fff;
    border-radius: 4px;
    width: 100%;
    height: 100%;
    .header_operation {
      margin-bottom: 10px;
      display: flex;
      .search_box {
        display: flex;
        > div {
          margin-right: 16px;
        }
        .search_input {
          width: 200px;
        }
      }
    }
    .table_div {
      height: calc(100% - 100px);
    }
    .paging_box {
      display: flex;
      justify-content: flex-end;
      padding: 6px;
    }
  }
}
.el-radio {
  display: flex;
  justify-content: center;
  align-items: center;
}
::v-deep .el-radio__label {
  display: none;
}
</style>
