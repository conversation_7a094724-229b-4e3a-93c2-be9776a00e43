<template>
  <div class="content_box">
    <div class="dictionary-list">
      <div class="toptip">设备字典列表</div>
      <div class="tree-box">
        <el-tree v-if="showTree" ref="theTree" :highlight-current="true" :props="defaultProps" lazy :load="loadNode"
          node-key="id" :default-expanded-keys="defaultExpandNodes" @node-click="handleNodeClick"></el-tree>
      </div>
    </div>
    <div class="data-box" :style="showParamBox ? '' : 'width:75%'">
      <div class="toptip">数据字典</div>
      <div class="content-box">
        <div class="btns">
          <el-button type="primary" icon="el-icon-plus" size="small" class="sino-button-sure"
            @click="openAddDialog">字典分类录入</el-button>
          <el-button type="primary" icon="el-icon-edit" class="sino-button-sure" size="small"
            @click="openEditDialog">编辑</el-button>
        </div>
        <el-table :data="tableData" style="width: 100%" border height="85%" highlight-current-row
          @current-change="handleCurrentChange">
          <el-table-column type="index" label="序号" width="55"> </el-table-column>
          <el-table-column prop="baseName" label="字典名称">
            <template slot-scope="scope">
              {{ scope.row.data.baseName || '' }}
            </template>
          </el-table-column>
          <el-table-column prop="parentName" label="上级字典">
            <template slot-scope="scope">
              {{ (scope.row.parent.data && scope.row.parent.data.baseName) || '' }}
            </template>
          </el-table-column>
          <el-table-column prop="dictCode" label="字典Code" width="140">
            <template slot-scope="scope">
              {{ scope.row.data.dictCode || '' }}
            </template>
          </el-table-column>
          <el-table-column prop="dictCode" label="图标" width="140">
            <template slot-scope="scope">
              <img v-if="scope.row.data.iconKey" class="iconClass" :src="scope.row.data.iconKey" alt="" />
              <span v-else></span>
            </template>
          </el-table-column>
          <el-table-column prop="" width="200" label="操作">
            <template slot-scope="scope">
              <el-button type="danger" icon="el-icon-delete" :disabled="scope.row.data.isDefault==='0'" size="small"
                @click="removeDict(scope.row)">删除</el-button>
              <!-- <el-button v-if="scope.row.data.levelType == '5'" class="sino-button-sure" type="primary" icon="el-icon-edit" size="small" @click="getParam(scope.row)"
              >配置参数字典</el-button
              > -->
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div v-if="showParamBox" class="param-box">
      <div class="toptip">数据字典</div>
      <div class="content-box">
        <div class="btns">
          <el-button type="primary" icon="el-icon-plus" size="small" class="sino-button-sure"
            @click="openParamAddDialog">设备参数录入</el-button>
          <el-button type="primary" icon="el-icon-edit" size="small" class="sino-button-sure"
            @click="openParamAddDialog('edit')">编辑</el-button>
        </div>
        <el-table :data="paramsTableData" style="width: 100%" border height="85%" highlight-current-row
          @current-change="handleCurrentParamChange">
          <el-table-column type="index" label="序号" width="55"> </el-table-column>
          <el-table-column prop="fieldName" label="参数名称" width="100" show-overflow-tooltip></el-table-column>
          <el-table-column prop="dataType" label="数据类型"> </el-table-column>
          <el-table-column prop="units" label="计量单位" width="80"> </el-table-column>
          <el-table-column prop="constraintCondition" label="约束条件" width="100" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="" width="120" label="操作">
            <template slot-scope="scope">
              <el-button type="danger" icon="el-icon-delete" size="small" @click="removeParam(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <!-- 字典分类录入对话框 -->
    <el-dialog v-dialogDrag title="字典分类录入" :visible.sync="dialogVisible" width="40%" :before-close="handleClose"
      class="classify-dialog" custom-class="model-dialog">
      <div class="content" style="padding: 10px">
        <el-form ref="typeForm" :model="form" :rules="rules">
          <el-form-item label="编码" label-width="120px" prop="dictCode">
            <el-input v-model="form.dictCode" autocomplete="off" maxlength="20"></el-input>
          </el-form-item>
          <br />
          <el-form-item label="名称" label-width="120px" prop="baseName">
            <el-input v-model="form.baseName" autocomplete="off" maxlength="20"></el-input>
          </el-form-item>
          <br />
          <el-form-item label="字典图标" label-width="120px">
            <span style="font-size: 10px; color: #7f848c">(最大支持5M，仅支持png格式)</span>
            <div style="display: flex">
              <el-upload action="string" :headers="headers" list-type="picture-card" :file-list="fileList" accept=".png"
                :limit="1" :before-upload="beforeAvatarUpload" :http-request="httpRequset" :on-exceed="handleExceed"
                :on-remove="handleRemove" :on-change="fileChange">
                <i class="el-icon-circle-plus-outline" style="color: #3562db"><br /><span
                    style="font-size: 10px; color: #7f848c">默认图标</span><span
                    style="font-size: 10px; color: #7f848c">(20*20)</span></i>
              </el-upload>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" plain @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog v-dialogDrag title="字典分类编辑" :visible.sync="editDialogVisible" width="40%" :before-close="editHandleClose"
      custom-class="model-dialog">
      <div class="content" style="padding: 10px">
        <el-form ref="typeForm" :model="form" :rules="rules">
          <el-form-item label="编码" label-width="100px" prop="dictCode">
            <el-input v-model="form.dictCode" autocomplete="off" maxlength="20"></el-input>
          </el-form-item>
          <br />
          <el-form-item label="名称" label-width="100px" prop="baseName">
            <el-input v-model="form.baseName" autocomplete="off" maxlength="20"></el-input>
          </el-form-item>
          <br />
          <el-form-item label="字典图标" label-width="100px">
            <span style="font-size: 10px; color: #7f848c">(最大支持5M，仅支持png格式)</span>
            <div style="display: flex">
              <el-upload action="string" :headers="headers" list-type="picture-card" :file-list="editFileList"
                accept=".png" :limit="1" :before-upload="beforeAvatarUpload" :on-exceed="handleExceed"
                :http-request="httpRequset" :on-remove="handleRemove" :on-change="fileChange">
                <i class="el-icon-circle-plus-outline" style="color: #3562db"><br /><span
                    style="font-size: 10px; color: #7f848c">默认图标</span><span
                    style="font-size: 10px; color: #7f848c">(20*20)</span></i>
              </el-upload>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" plain @click="editHandleClose">取 消</el-button>
        <el-button type="primary" @click="editForm">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 参数字典新增 -->
    <el-dialog v-dialogDrag title="设备参数录入" :visible.sync="paramDialogVisible" width="40%"
      :before-close="handleParamClose" custom-class="model-dialog">
      <div class="param-dialog-box content">
        <el-form ref="paramForm" :model="sPImportantEq" :rules="rulesParam" class="param-form" style="width: 50%">
          <el-form-item label="参数名称" label-width="120px" prop="fieldName">
            <el-input v-model="sPImportantEq.fieldName" autocomplete="off" maxlength="20"></el-input>
          </el-form-item>
          <br />
          <el-form-item label="数据类型" label-width="120px" prop="dataType">
            <el-select v-model="sPImportantEq.dataType" placeholder="请选择数据类型" @change="dataTypeChanged">
              <el-option label="数值型" value="数值型"></el-option>
              <el-option label="枚举型" value="枚举型"></el-option>
            </el-select>
          </el-form-item>
          <br />
          <el-form-item label="计量单位" label-width="120px" prop="units">
            <!-- <el-input v-model="sPImportantEq.units" autocomplete="off" maxlength="20"></el-input> -->
            <el-select v-model="sPImportantEq.units" placeholder="请选择计量单位">
              <el-option v-for="item in unitsList" :key="item.val" :label="item.text" :value="item.val"></el-option>
            </el-select>
          </el-form-item>
          <br />
          <el-form-item label="约束条件" label-width="120px" prop="constraintCondition">
            <el-select v-model="sPImportantEq.constraintCondition" placeholder="请选择约束条件">
              <el-option label="M" value="M"></el-option>
              <el-option label="O" value="O"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div v-if="showMore" class="item-content">
          <div v-for="(item, index) in optionsList" :key="item.id" class="more">
            <span>选项{{ index + 1 }} </span>
            <el-input v-model="item.val" placeholder="请输入" style="margin-right: 8px" maxlength="20"></el-input>
            <i v-if="index == 0" class="el-icon-plus btn-plus" @click="addItems"></i>
            <span v-if="index != 0" class="handle-btn" @click="deleteVals(item, index)">删除</span>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" plain @click="handleParamClose">取 消</el-button>
        <el-button type="primary" @click="submitsPImportantEq">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import qs from 'qs'
import store from '@/store/index'
import axios from 'axios'
export default {
  data() {
    return {
      message: 'Hello Vue!',
      showTree: true,
      treeData: [],
      tableData: [],
      fileList: [],
      editFileList: [],
      paramsTableData: [],
      defaultProps: {
        children: 'children',
        label: 'baseName',
        isLeaf: 'leaf'
      },
      dialogVisible: false,
      form: {
        baseName: '',
        dictCode: ''
      },
      rules: {
        // dictCode:[
        //     { required: true, message: '请输入编码', trigger: 'blur' }
        // ],
        baseName: [{ required: true, message: '请输入名称', trigger: 'blur' }]
      },
      rulesParam: {
        fieldName: [{ required: true, message: '请输入参数名称', trigger: 'blur' }],
        dataType: [{ required: true, message: '请输入数据类型', trigger: 'blur' }],
        units: [{ required: true, message: '请输入计量单位', trigger: 'blur' }],
        constraintCondition: [{ required: true, message: '请输入约束条件', trigger: 'blur' }]
      },
      currentNode: '',
      currentRow: '',
      editDialogVisible: false,
      defaultExpandNodes: ['-1'],
      currentParamClassify: '',
      paramDialogVisible: false,
      sPImportantEq: {
        fieldName: '',
        dataType: '',
        units: '',
        constraintCondition: ''
      },
      showMore: false,
      optionsList: [
        {
          id: 1,
          val: ''
        }
      ],
      paramsList: [],
      currentParamRow: '',
      isEdit: false,
      tempArr: [],
      unitsList: [
        {
          text: '-',
          val: '-'
        },
        {
          text: 'kW',
          val: 'kW'
        },
        {
          text: 'kg/h',
          val: 'kg/h'
        },
        {
          text: 'kVA',
          val: 'kVA'
        },
        {
          text: 'kV',
          val: 'kV'
        },
        {
          text: 'V',
          val: 'V'
        },
        {
          text: '个',
          val: '个'
        },
        {
          text: 'A',
          val: 'A'
        },
        {
          text: 'm³',
          val: 'm³'
        },
        {
          text: 'm²',
          val: 'm²'
        },
        {
          text: 'm³/h',
          val: 'm³/h'
        },
        {
          text: 'm',
          val: 'm'
        },
        {
          text: 'r/min',
          val: 'r/min'
        },
        {
          text: 'MPa',
          val: 'MPa'
        },
        {
          text: '℃',
          val: '℃'
        },
        {
          text: 'kg',
          val: 'kg'
        },
        {
          text: '组',
          val: '组'
        },
        {
          text: '瓶',
          val: '瓶'
        },
        {
          text: 'Pa',
          val: 'Pa'
        },
        {
          text: 'm/s',
          val: 'm/s'
        },
        {
          text: '°',
          val: '°'
        },
        {
          text: 'mm',
          val: 'mm'
        },
        {
          text: 'm/min',
          val: 'm/min'
        }
      ],
      showParamBox: false,
      iconKey: ''
    }
  },
  computed: {
    headers() {
      return {
        Authorization: 'Bearer ' + store.state.user.token
      }
    }
  },
  watch: {
    currentParamClassify(val) {
      if (val) {
        this.showParamBox = true
      } else {
        this.showParamBox = false
      }
    }
  },
  mounted() { },
  methods: {
    transParams(data) {
      let arr = [] // 枚举型
      data.forEach((item) => {
        if (item.dataType == '枚举型') {
          arr.push(item)
        }
      })
      let dataInfo = {}
      arr.forEach((item, index) => {
        let id = item.id
        if (!dataInfo[id]) {
          dataInfo[id] = {
            id,
            fieldName: item.fieldName,
            dataType: item.dataType,
            units: item.units,
            constraintCondition: item.constraintCondition,
            typeId: item.typeId,
            importantTypeId: item.importantTypeId,
            child: []
          }
        }
        dataInfo[id].child.push(item)
      })
      arr = Object.values(dataInfo)
      let arr2 = [] // 数值型
      data.forEach((item, index) => {
        if (item.dataType == '数值型') {
          arr2.push(item)
        }
      })
      arr.forEach((item) => {
        item.child.forEach((item2, index2) => {
          if (!item2.spImportantType) {
            item.child.splice(index2, 1)
          }
        })
      })
      this.paramsList = [...arr, ...arr2]
      // this.paramsList.forEach((item, index) => {
      //   item.flag = 'yqf' + index
      // })
    },
    loadNode(node, resolve) {
      if (node.level == 0) {
        this.$api.equipmentListData({ parentId: -1 }).then((res) => {
          if (res.code == '200' && res.data.length > 0) {
            let data = [
              {
                baseName: '后勤设备',
                deleteFlag: '0',
                id: '-1',
                levelType: '0',
                parentId: '',
                children: res.data
              }
            ]
            setTimeout(() => {
              this.$refs.theTree.setCurrentKey('-1')
              this.currentNode = this.$refs.theTree.getCurrentNode()
              this.tableData = this.$refs.theTree.getNode('-1').childNodes
            }, 200)
            return resolve(data)
          } else {
            let data = [
              {
                baseName: '后勤设备',
                deleteFlag: '0',
                id: '-1',
                levelType: '0',
                parentId: '',
                children: []
              }
            ]
            setTimeout(() => {
              this.$refs.theTree.setCurrentKey('-1')
              this.currentNode = this.$refs.theTree.getCurrentNode()
              this.tableData = this.$refs.theTree.getNode('-1').childNodes
            }, 200)
            return resolve(data)
          }
        })
      } else {
        this.$api.equipmentListData({ parentId: node.data.id }).then((res) => {
          if (res.code == '200' && res.data.length > 0) {
            if (node.data.levelType == '4') {
              res.data.forEach((item) => {
                item.leaf = true
              })
            }
            return resolve(res.data)
          } else {
            return resolve([])
          }
        })
      }
    },
    handleNodeClick(data, node) {
      this.currentParamClassify = ''
      if (node.data.levelType == '5') {
        return this.$message.info('该分类是最后一级')
      }
      this.currentNode = data
      setTimeout(() => {
        this.tableData = node.childNodes
      }, 100)
    },
    handleClose() {
      this.dialogVisible = false
      this.$refs.typeForm.resetFields()
      setTimeout(() => {
        this.form.dictCode = ''
        this.form.baseName = ''
      }, 100)
    },
    editHandleClose() {
      this.editDialogVisible = false
      this.$refs.typeForm.resetFields()
      setTimeout(() => {
        this.form.dictCode = ''
        this.form.baseName = ''
      }, 100)
    },
    handleParamClose() {
      this.paramDialogVisible = false
      this.showMore = false
      this.$refs.paramForm.resetFields()
      setTimeout(() => {
        this.sPImportantEq.fieldName = ''
        this.sPImportantEq.dataType = ''
        this.sPImportantEq.units = ''
        this.sPImportantEq.constraintCondition = ''
        this.optionsList = [
          {
            id: 1,
            val: ''
          }
        ]
      }, 100)
    },
    submitForm() {
      this.$refs.typeForm.validate((valid) => {
        if (valid) {
          let params = {
            dictCode: this.form.dictCode,
            baseName: this.form.baseName,
            parentId: this.currentNode.id,
            levelType: parseInt(this.currentNode.levelType) + 1,
            deleteFlag: 0,
            iconKey: this.iconKey
          }
          this.$api.handleClassify(params).then((res) => {
            this.dialogVisible = false
            this.$refs.typeForm.resetFields()
            setTimeout(() => {
              this.form.dictCode = ''
              this.form.baseName = ''
            }, 100)
            if (res.result == 200) {
              this.$message.success('新增成功!')
              this.showTree = false
              this.setDefaultExpandedKeys()
              this.$nextTick(() => {
                this.showTree = true
              })
              this.$api.equipmentListData({ parentId: this.currentNode.id }).then((res) => {
                if (res.code == '200' && res.data.length > 0) {
                  if (res.code == 200) {
                    let arr = []
                    res.data.forEach((item) => {
                      let obj = {
                        data: item,
                        parent: {
                          data: this.currentNode
                        }
                      }
                      arr.push(obj)
                    })
                    this.tableData = arr
                  }
                }
              })
            } else {
              this.$message.error('新增失败，请重试')
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    setDefaultExpandedKeys() {
      let treeCompInstance = this.$refs.theTree
      let allNodes = treeCompInstance.store._getAllNodes()
      let defaultExpandNodes = []
      allNodes.forEach((node) => {
        node.expanded && defaultExpandNodes.push(node.data.id)
      })
      this.defaultExpandNodes = defaultExpandNodes
    },

    editForm() {
      this.$refs.typeForm.validate((valid) => {
        if (valid) {
          let params = {
            id: this.currentRow.data.id,
            dictCode: this.form.dictCode,
            baseName: this.form.baseName,
            parentId: this.currentNode.id,
            levelType: parseInt(this.currentNode.levelType) + 1,
            deleteFlag: 0,
            iconKey: this.iconKey
          }
          this.$api.handleClassify(params).then((res) => {
            this.editDialogVisible = false
            if (res.result == 200) {
              this.$message.success('修改成功!')
              this.showTree = false
              this.setDefaultExpandedKeys()
              this.$nextTick(() => {
                this.showTree = true
              })
              this.$api.equipmentListData({ parentId: this.currentNode.id }).then((res) => {
                if (res.code == '200' && res.data.length > 0) {
                  if (res.code == 200) {
                    let arr = []
                    res.data.forEach((item) => {
                      let obj = {
                        data: item,
                        parent: {
                          data: this.currentNode
                        }
                      }
                      arr.push(obj)
                    })
                    this.tableData = arr
                  }
                }
              })
            } else {
              this.$message.error('操作失败，请重试')
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    removeDict(scope) {
      this.$api.deleteDictData({
        dictCode: scope.data.id,
        type: '2'
      }).then((res) => {
        if (res.code == 200) {
          if (res.data) {
            this.$confirm('确定删除吗?', '提示', {
              cancelButtonClass: 'el-button--primary is-plain',
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            })
              .then(() => {
                let params = {
                  id: scope.data.id,
                  deleteFlag: 1
                }
                this.$api.deleteEquipment(params).then((res) => {
                  if (res.result == 200) {
                    this.$message.success('删除成功!')
                    this.showTree = false
                    this.setDefaultExpandedKeys()
                    this.$nextTick(() => {
                      this.showTree = true
                    })
                    this.$api.equipmentListData({ parentId: scope.data.parentId }).then((res) => {
                      if (res.code == '200' && res.data.length > 0) {
                        if (res.code == 200) {
                          let arr = []
                          res.data.forEach((item) => {
                            let obj = {
                              data: item,
                              parent: scope.parent
                            }
                            arr.push(obj)
                          })
                          this.tableData = arr
                        }
                      }
                    })
                  } else if (res.result == '400') {
                    this.$message.error(res.message)
                  } else {
                    this.$message.error('删除失败，请重试')
                  }
                })
              })
              .catch(() => {
                this.$message.info('已取消删除')
              })
          } else {
            this.$alert('无法删除正在使用的字典值', '提示', {
              confirmButtonText: '确定',
            });
          }
        } else {
          this.$message.error(res.message)
        }
      })
    },
    removeParam(scope) {
      this.$confirm('确定删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          let params = {
            'sPImportantEq.id': scope.id,
            'sPImportantEq.deleteFlag': 1
          }
          this.$api.deleteEquipment(params).then((res) => {
            if (res.result == 200) {
              this.$message.success('删除成功!')
              this.$api.getEquipmentList({ typeId: scope.typeId }).then((res) => {
                if (res.code == 200) {
                  this.transParams(res.data)
                  this.paramsTableData = this.paramsList
                } else {
                  this.$message.error(res.message)
                }
              })
            } else {
              this.$message.error('删除失败，请重试')
            }
          })
        })
        .catch(() => {
          this.$message.info('已取消删除')
        })
    },
    openAddDialog() {
      // return this.$message.info('功能未开放')
      if (this.currentNode.levelType == '5') {
        return this.$message.info('无法添加下级分类')
      } else {
        this.fileList = []
        this.dialogVisible = true
      }
    },
    openEditDialog() {
      this.editFileList = []
      // return this.$message.info('功能未开放')
      if (this.currentRow) {
        this.editDialogVisible = true
        this.form.baseName = this.currentRow.data.baseName
        this.form.dictCode = this.currentRow.data.dictCode
        if (this.currentRow.data.iconKey) {
          this.editFileList.push({
            url: this.currentRow.data.iconKey
          })
        }
      } else {
        this.$message.info('未选择字典')
      }
    },
    handleCurrentChange(val) {
      this.currentRow = val
    },
    handleCurrentParamChange(val) {
      this.currentParamRow = val
    },
    getParam(scope) {
      // return this.$message.info('功能未开放')
      // console.log(scope)
      this.currentParamClassify = scope
      this.$api.getEquipmentList({ typeId: scope.data.id }).then((res) => {
        if (res.code == 200) {
          this.transParams(res.data)
          this.paramsTableData = this.paramsList
        } else {
          this.$message.error(res.message)
        }
      })
    },
    openParamAddDialog(handleType) {
      this.tempArr = []
      if (!this.currentParamClassify) {
        return this.$message.error('请先选择设备小类')
      }
      if (handleType == 'edit' && !this.currentParamRow) {
        return this.$message.error('请先选择设备参数')
      }
      this.isEdit = false
      this.paramDialogVisible = true
      if (handleType == 'edit') {
        this.isEdit = true
        this.sPImportantEq.fieldName = this.currentParamRow.fieldName
        this.sPImportantEq.dataType = this.currentParamRow.dataType
        this.sPImportantEq.units = this.currentParamRow.units
        this.sPImportantEq.constraintCondition = this.currentParamRow.constraintCondition
        if (this.currentParamRow.dataType == '枚举型') {
          let arr = []
          this.currentParamRow.child.forEach((item, index) => {
            let obj = {
              id: index + 1,
              val: item.typeName,
              importantTypeId: item.importantTypeId
            }
            arr.push(obj)
          })
          this.showMore = true
          this.optionsList = arr
        }
      }
    },
    submitsPImportantEq() {
      this.$refs.paramForm.validate((valid) => {
        if (valid) {
          let sPImportantEq = this.sPImportantEq
          let params = {
            'sPImportantEq.fieldName': sPImportantEq.fieldName,
            'sPImportantEq.dataType': sPImportantEq.dataType,
            'sPImportantEq.units': sPImportantEq.units,
            'sPImportantEq.constraintCondition': sPImportantEq.constraintCondition,
            'sPImportantEq.deleteFlag': 0,
            'sPImportantEq.typeId': this.currentParamClassify.data.id,
            id: this.currentParamClassify.data.id,
            baseName: this.currentParamClassify.data.baseName
          }
          if (this.isEdit) {
            params['sPImportantEq.id'] = this.currentParamRow.id
          }
          if (sPImportantEq.dataType == '枚举型') {
            let spImportantTypesArr = []
            this.optionsList.forEach((item) => {
              let obj = {
                eumName: sPImportantEq.fieldName,
                typeName: item.val,
                deleteFlag: 0
              }
              if (this.isEdit) {
                obj.id = item.importantTypeId
              }
              spImportantTypesArr.push(obj)
            })
            spImportantTypesArr = [...spImportantTypesArr, ...this.tempArr]
            params.spImportantTypes = JSON.stringify(spImportantTypesArr)
          }
          this.$api.handleClassify(params).then((res) => {
            this.handleParamClose()
            if (res.result == 200) {
              if (!this.isEdit) {
                this.$message.success('新增成功!')
              } else {
                this.$message.success('修改成功!')
              }
              this.$api.getEquipmentList({ typeId: this.currentParamClassify.data.id }).then((res) => {
                if (res.code == 200) {
                  this.transParams(res.data)
                  this.paramsTableData = this.paramsList
                } else {
                  this.$message.error(res.message)
                }
              })
            } else {
              if (!this.isEdit) {
                this.$message.error('新增失败，请重试')
              } else {
                this.$message.error('修改失败，请重试')
              }
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    dataTypeChanged(val) {
      if (val == '枚举型') {
        this.showMore = true
      } else {
        this.showMore = false
      }
    },
    addItems() {
      let obj = {
        id: this.optionsList[this.optionsList.length - 1].id + 1,
        val: ''
      }
      this.optionsList.push(obj)
    },
    deleteVals(item, index) {
      if (this.isEdit) {
        item.deleteFlag = 1
        item.id = item.importantTypeId
        this.tempArr.push(item)
      }
      this.optionsList.splice(index, 1)
    },
    // 图片上传开始
    fileChange(file, fileList) {
      if (this.dialogVisible) {
        this.fileList = fileList
      } else {
        this.editFileList = fileList
      }
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 5
      if (!isJPG) {
        this.$message.error('上传图片只能是 png 格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 5MB!')
      }
      return isJPG && isLt2M
    },
    handleRemove(file, fileList) {
      this.iconKey = ''
      if (this.dialogVisible) {
        this.fileList = []
      } else {
        this.editFileList = []
      }
    },
    httpRequset() {
      this.formData = new FormData()
      if (this.dialogVisible) {
        this.fileList.forEach((item) => {
          this.formData.append('file', item.raw)
        })
      } else {
        this.editFileList.forEach((item) => {
          this.formData.append('file', item.raw)
        })
      }
      axios
        .post(__PATH.VUE_ICIS_API + 'file/upload', this.formData, {
          headers: {
            Authorization: 'Bearer ' + store.state.user.token
          }
        })
        .then((res) => {
          if (res.data.code == 200) {
            this.iconKey = res.data.data.fileKey
            this.$message({
              showClose: true,
              message: res.data.message,
              type: 'success'
            })
          } else {
            this.$message.error(res.data.msg)
          }
        })
    },
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
    }
    // 图片上传结束
  }
}
</script>

<style lang="scss" scoped>
.content_box {
  height: 100%;
  margin-right: 10px;
  // margin-top:10px;
  border-radius: 10px;
  background: #fff;
  display: flex;
  justify-content: space-between;
}

.dictionary-list {
  background-color: #fff;
  width: 30%;
  overflow: auto;
  height: 100%;
}

.data-box {
  background-color: #fff;
  width: 45%;
  height: 100%;
}

.item-title {
  color: #606266;
  font-weight: 700;
  height: 20px;
  display: flex;
  align-items: center;
  padding-left: 14px;
  margin-bottom: 24px;
  border-left: 3px solid #5188fc;
}

.tree-box {
  padding: 10px 24px;
  overflow: auto;
  height: calc(100% - 50px);
}

.btns {
  display: flex;
  margin-bottom: 12px;
}

.content-box {
  padding: 10px 24px;
  height: 100%;
}

.el-table__empty-block {
  height: 550px !important;
  border: none;
}

.param-box {
  width: 34%;
  background-color: #fff;
}
.hide {
  ::v-deep .el-upload--picture-card {
    display: none;
  }
}

::v-deep .el-button--danger {
  background: #f78989;
  border-color: #f78989;
  color: #fff;
  min-width: 55px;
  height: 30px;
}

::v-deep .el-button--danger:hover {
  background: #f78989;
  border-color: #f78989;
  color: #fff;
}

.param-dialog-box {
  display: flex;
}

.more {
  display: flex;
  margin-bottom: 12px;
}

.more span {
  display: flex;
  align-items: center;
  width: 20%;
}

.handle-btn {
  color: #f56c6c;
  cursor: pointer;
  margin-left: 5px;
}

.btn-plus {
  font-size: 20px;
  color: #409eff;
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-left: 5px;
}

.item-content {
  width: 40%;
  height: 300px;
  overflow-y: auto;
}

::v-deep .more .el-input {
  width: 200px !important;
}

::v-deep .el-form .el-input__inner {
  width: 220px !important;
}

.content {
  width: 100%;
  max-height: 420px !important;
  overflow: auto;
  background-color: #fff !important;
}
.iconClass {
  width: 20px;
  height: 20px;
}
::v-deep .el-upload-list__item {
  transition: none !important;
  -webkit-transition: nonne !important;
}
::v-deep .el-upload-list__item-name {
  transition: none !important;
  -webkit-transition: nonne !important;
}
</style>
