<template>
  <PageContainer>
    <div slot="header" class="control-btn-header">
      <div class="search-from">
        <el-input v-model="searchFrom.msgTitle" placeholder="消息标题" clearable style="width: 200px;"></el-input>
        <el-select v-model="searchFrom.msgStatus"  placeholder="请选择消息状态" clearable style="width: 200px;">
          <el-option label="已发布" :value="0"></el-option>
          <el-option label="草稿" :value="1"></el-option>
          <el-option label="已撤回" :value="2"></el-option>
        </el-select>
        <el-select v-model="searchFrom.msgCatId"  placeholder="请选择消息类型" clearable style="width: 200px;">
          <el-option v-for="item in msgTypeList" :key="item.id" :label="item.label" :value="item.id"></el-option>
        </el-select>
        <el-date-picker
            v-model="searchFrom.dataRange"
            type="daterange"
            unlink-panels
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            :picker-options="pickerOptions"
          >
          </el-date-picker>
        <div style="display: inline-block;">
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="searchForm">查询</el-button>
          <el-button type="primary" icon="el-icon-plus" @click="control('add')">新增</el-button>
        </div>
      </div>
    </div>
    <div slot="content" class="table-content">
      <TablePage
        ref="table"
        v-loading="tableLoading"
        :showPage="true"
        :tableColumn="tableColumn"
        :data="tableData"
        height="calc(100% - 40px)"
        :pageData="pageData"
        :pageProps="pageProps"
        @pagination="paginationChange"
        @row-dblclick="control('detail', $event)"
      />
    </div>
  </PageContainer>
</template>
<script lang="jsx">
export default {
  name: 'messageRelease',
  beforeRouteEnter(to, from, next) {
    let names = []
    to.matched.map((v, i) => {
      if (i > 0) {
        v.components.default.name && names.push(v.components.default.name)
      }
    })
    // 进入页面时，先将当前页面的 name 信息存入 keep-alive 全局状态
    setTimeout(() => {
      next((vm) => {
        vm.$store.commit('keepAlive/add', names)
      })
    }, 0)
  },
  async beforeRouteLeave(to, from, next) {
    // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
    if (!['AddMessage'].includes(to.name)) {
      // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      tableLoading: false,
      searchFrom: {
        msgTitle: '', // 应用名称
        msgStatus: '', // 消息状态
        msgCatId: '', // 消息类别
        dataRange: [] // 时间范围
      },
      tableColumn: [
        {
          prop: '',
          label: '序号',
          formatter: (scope) => {
            return (this.pageData.current - 1) * this.pageData.size + scope.$index + 1
          }
        },
        {
          prop: 'msgTitle',
          label: '消息标题'
        },
        {
          prop: 'msgCatId',
          label: '消息类型',
          formatter: (scope) => {
            return this.msgTypeList.find(item => item.id == scope.row.msgCatId)?.label
          }
        },
        {
          prop: 'msgStatus',
          label: '状态',
          formatter: (scope) => {
            if (scope.row.msgStatus == 0) {
              return '已发布'
            } else if (scope.row.msgStatus == 1) {
              return '草稿 '
            } else if (scope.row.msgStatus == 2) {
              return '已撤回'
            } else {
              return ''
            }
          }
        },
        {
          prop: 'readTimes',
          label: '阅读次数'
        },
        {
          prop: 'publishTime',
          label: '发布时间'
        },
        {
          prop: 'createName',
          label: '发布人'
        },
        {
          width: 200,
          prop: 'operation',
          label: '操作',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                {row.row.msgStatus == 1 || row.row.msgStatus == 2
                  ? (<span class="operationBtn-span" style="color: #fa403c" onClick={() => this.control('del', row.row)}>删除</span>)
                  : ('')
                }
                {row.row.msgStatus == 0
                  ? (<span class="operationBtn-span" style="color: #3562db" onClick={() => this.control('recall', row.row)}>撤回</span>)
                  : ('')
                }
                {row.row.msgStatus == 1 || row.row.msgStatus == 2
                  ? (<span class="operationBtn-span" style="color: #121F3E" onClick={() => this.control('edit', row.row)}>编辑</span>)
                  : ('')
                }
                {row.row.msgStatus == 1 || row.row.msgStatus == 2
                  ? (<span class="operationBtn-span" style="color: #08CB83" onClick={() => this.control('publish', row.row)}>发布</span>)
                  : ('')
                }
              </div>
            )
          }
        }
      ],
      pickerOptions: {
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      tableData: [],
      msgTypeList: [],
      pageData: {
        current: 1,
        size: 15,
        total: 0
      },
      pageProps: {
        page: 'current',
        pageSize: 'size',
        total: 'total'
      }
    }
  },
  watch: {

  },
  activated() {
    this.getMessageList()
  },
  mounted() {
    this.getMessageList()
    this.getMsgType()
  },
  methods: {
    // 获取消息类型
    getMsgType() {
      this.$api.GetMsgType({ }).then(res => {
        if (res.code == 200) {
          this.msgTypeList = res.data
        }
      })
    },
    // 查询
    searchForm() {
      this.getMessageList()
    },
    // 重置查询
    resetForm() {
      Object.assign(this.$data.searchFrom, this.$options.data().searchFrom)
      this.searchForm()
    },
    control(type, row) {
      if (['add', 'edit', 'detail'].includes(type)) {
        this.$router.push({
          path: '/appManager/messageRelease/addMessage',
          query: {
            type,
            id: row?.msgId ?? ''
          }
        })
      } else if (type == 'del') {  // 删除
        this.$confirm('删除后将无法恢复，是否确定删除？', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$api.DeleteMessage({msgId: row.msgId}, {'operation-type': 3, 'operation-id': row.msgId, 'operation-name': row.msgTitle}).then(res => {
            if (res.code == 200) {
              this.$message({ message: '消息删除成功', type: 'success'})
              this.isLastPage(1)
              this.searchForm()
            } else {
              this.$message({ message: res.msg, type: 'error'})
            }
          })
        })
      } else if (type == 'recall') {
        this.$confirm('是否确定撤回？', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$api.RevocationMessage({msgId: row.msgId}).then(res => {
            if (res.code == 200) {
              this.searchForm()
              this.$message({ message: '消息撤回成功', type: 'success'})
            } else {
              this.$message({ message: res.message, type: 'error'})
            }
          })
        })
      } else  if (type == 'publish') {
        this.$confirm('是否确定发布？', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$api.PublishMessage({msgId: row.msgId, userId: this.$store.state.user.userInfo.user.staffId, msgSysId: 0}).then(res => {
            if (res.code == 200) {
              this.searchForm()
              this.$message({ message: '消息发布成功', type: 'success'})
            } else {
              this.$message({ message: res.message, type: 'error'})
            }
          })
        })
      }
    },
    // 判断当前页是否是最后一页
    isLastPage (deleteNum) {
      let deleteAfterPage = Math.ceil((this.pageData.total - deleteNum) / this.pageData.size)
      let currentPage = this.pageData.current > deleteAfterPage ? deleteAfterPage : this.pageData.current
      this.pageData.current = currentPage < 1 ? 1 : currentPage
    },
    // 获取消息列表
    getMessageList() {
      let {msgTitle, msgStatus, msgCatId, dataRange} = this.searchFrom
      let param = {
        msgTitle, msgStatus, msgCatId,
        startTime: dataRange[0],
        endTime: dataRange[1],
        pageSize: this.pageData.size,
        page: this.pageData.current
      }
      this.tableLoading = true
      this.$api.GetMessageList(param).then(res => {
        if (res.code == 200) {
          this.tableData = res.data.records
          this.pageData.total = res.data.total
        }
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    paginationChange(pagination) {
      // this.pageData.size = pagination.pageSize
      // this.pageData.current = pagination.page
      Object.assign(this.pageData, pagination)
      this.getMessageList()
    }
  }
}
</script>
<style lang="scss" scoped>
.control-btn-header {
  padding: 0 10px 10px !important;

  .search-from {
    & > div {
      margin-top: 12px;
      margin-right: 10px;
    }
  }
}

.table-content {
  margin-top: 15px;
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 15px);
  padding: 10px;
}
</style>
<style lang="scss">
.operationBtn-span {
  margin-right: 10px;
}
</style>
