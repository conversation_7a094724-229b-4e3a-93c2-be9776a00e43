<!-- 运行监测 -->
<template>
  <ContentCard :title="item.componentTitleShow" :scrollbarHover="true" :cstyle="{ width: '100%', height: '100%' }"
    class="drag_class" :hasMoreOper="['edit']"
    @more-oper-event="(val) => $emit('all-more-Oper', val, item.componentName, item.chartType)">
    <div slot="content" style="width: 100%; height: 100%;">
      <div class="control-btn-header">
        <el-select v-model="requestInfo.alarmType" placeholder="请选择报警类型" style="width: 230px" filterable clearable>
          <el-option v-for="(item, index) in alarmTypeList" :key="index" :label="item.alarmDictName"
            :value="item.id"></el-option>
        </el-select>
        <el-button class="btn-item" :class="{ 'btn-active': requestInfo.timeType == 0 }" plain
          @click="timeTypeChange(0)">全部</el-button>
        <el-button class="btn-item" :class="{ 'btn-active': requestInfo.timeType == 1 }" plain
          @click="timeTypeChange(1)">今日</el-button>

        <el-button class="btn-item" :class="{ 'btn-active': requestInfo.timeType == 3 }" plain
          @click="timeTypeChange(3)">本月</el-button>
        <el-button class="btn-item" :class="{ 'btn-active': requestInfo.timeType == 4 }" plain
          @click="timeTypeChange(4)">本年</el-button>
        <el-button class="btn-item" :class="{ 'btn-active': requestInfo.timeType == 5 }" plain
          style="margin-right: 10px" @click="timeTypeChange(5)">自定义</el-button>
        <el-date-picker v-model="requestInfo.dataRange" type="daterange" unlink-panels
          :disabled="requestInfo.timeType != 5" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
          format="yyyy-MM-dd" value-format="yyyy-MM-dd" clearable @change="timeListData" />
        <div style="display: inline-block">
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="searchForm">查询</el-button>
        </div>
      </div>
      <div style="width: 100%; height: 79%;">
        <echarts :ref="`bjcsph${item.componentDataType}`" :domId="`bjcsph${item.componentDataType}`" width="100%"
          height="100%" />
      </div>
    </div>
  </ContentCard>
</template>
<script lang="jsx">
import moment from 'moment'
export default {
  name: 'bjcsph',
  props: {
    item: {
      type: Object,
      default: () => { }
    },
    systemCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      requestInfo: {
        alarmType: "",
        projectCode: '',
        dataRange: [], // 时间范围
        timeType: 0 // 1: 当天 2: 本月 3: 本年 4: 自定义
      },
      alarmTypeList: [],
      deviceId: "",
    }
  },
  mounted() {
    this.queryAlarmConfigByPage()
    this.bjcsphData()
  },
  methods: {
    // 报警类型
    queryAlarmConfigByPage() {
      const payload = {
        thirdSystemCode: this.systemCode,
        thirdSystemName: "",
      }
      this.$api.queryAlarmConfigByPage(payload).then((res) => {
        if (res.code === "200") {
          this.alarmTypeList = res.data
        }
      })
    },
    timeListData(val) {
      this.requestInfo.dataRange[0] = val[0] + ' 00:00:00'
      this.requestInfo.dataRange[1] = val[1] + ' 23:59:59'
    },
    // 日期选择
    timeTypeChange(type) {
      let newObj = {
        1: [moment().format('YYYY-MM-DD 00:00:00'), moment().format('YYYY-MM-DD 23:59:59')],
        2: [moment().startOf('week').format('YYYY-MM-DD 00:00:00'), moment().endOf('week').format('YYYY-MM-DD 23:59:59')],
        3: [moment().startOf('month').format('YYYY-MM-DD 00:00:00'), moment().endOf('month').format('YYYY-MM-DD 23:59:59')],
        4: [moment().startOf('year').format('YYYY-MM-DD 00:00:00'), moment().endOf('year').format('YYYY-MM-DD 23:59:59')],
        0: [],
        5: []
      }
      this.requestInfo.dataRange = newObj[type]
      this.requestInfo.timeType = type
      this.pageData.pageSize = 15
      this.pageData.pageNo = 1
    },
    // 重置查询表单
    resetForm() {
      this.requestInfo = {
        projectCode: '',
        dataRange: [], // 时间范围
        timeType: 0 // 1: 当天 2: 本月 3: 本年 4: 自定义
      },
        this.pageData = {
          pageSize: 15,
          pageNo: 1
        },
        this.bjcsphData()
    },
    searchForm() {
      this.bjcsphData()
    },
    bjcsphData() {
      const payload = {
        pageNo: 1,
        pageSize: 15,
        alarmType: this.requestInfo.alarmType,
        startTime: this.requestInfo.dataRange[0],
        endTime: this.requestInfo.dataRange[1],
        projectCode: this.systemCode,
      }
      this.$api.selectAlarmCountByAlarmType(payload).then((res) => {
        if (res.code === "200") {
          this.appendEchartsData(res.data)
        } else {
          this.hasChart = false
        }
      })

    },
    appendEchartsData(data) {
      const baseData = {
        grid: {
          left: '0%',
          right: '50px',
          bottom: '5%',
          top: '5%',
          containLabel: true
        },
        xAxis: {
          max: 'dataMax',
          show: false
        },
        yAxis: {
          type: 'category',
          data: [],
          inverse: true,
          axisLabel: {
            formatter: function (value) {
              return value.length > 5 ? value.substring(0, 5) + '...' : value; // 截断文字
            }
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{b} : {c}',
          confine: true
        },
        series: [
          {
            realtimeSort: true,
            name: 'X',
            type: 'bar',
            data: [],
            barWidth: 20,
            label: {
              show: true,
              position: 'right',
              valueAnimation: true
            }
          }
        ]
      }
      if (data && data && data.length > 0) {
        data.forEach((item) => {
          baseData.yAxis.data.push(item.name)
          baseData.series[0].data.push(item.count)
        })
      }
      this.$refs[`bjcsph${this.item.componentDataType}`].init(baseData)
    },
  }
}
</script>
<style lang="scss" scoped>
.data-btns {
  position: absolute;
  right: 7%;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &>span {
    width: 44px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    margin: 0 4px;
    background-color: #f6f5fa;
    font-size: 14px;
    font-family: 'PingFang SC-Medium', 'PingFang SC';
    border: none;
    border-radius: 2px;
    color: #7f848c;
    cursor: pointer;
  }

  .active-btn {
    background-color: #e6effc !important;
    color: #3562db !important;
    border-color: #e6effc !important;
  }
}

.operation-list {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
}

.control-btn-header {
  padding: 0;
  margin-bottom: 20px;

  &>div {
    margin-right: 10px;
    margin-top: 10px;
  }

  .btn-item {
    border: 1px solid #3562db;
    color: #3562db;
    font-family: none;
  }

  .btn-active {
    color: #fff;
    background: #3562db;
  }
}
</style>
