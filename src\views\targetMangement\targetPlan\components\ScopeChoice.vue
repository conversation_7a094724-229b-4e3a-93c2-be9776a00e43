<template>
  <div class="sino_page">
    <el-container>
      <el-aside v-if="scopeType === 1 || scopeType === 2 || scopeType === 5 || scopeType === 6">
        <div class="topSearch">
          <el-input v-model="filterText" placeholder="请输入关键字" clearable suffix-icon="el-icon-search"> </el-input>
        </div>
        <div class="sino_tree_box treeBox">
          <el-tree
            ref="tree"
            v-loading="treeLoading"
            :check-strictly="true"
            :data="treeData"
            :props="defaultProps"
            style="margin-top: 10px"
            :filter-node-method="filterNode"
            :highlight-current="true"
            :default-expanded-keys="expanded"
            @node-click="handleNodeClick"
          ></el-tree>
        </div>
      </el-aside>
      <el-main>
        <div class="sino_table">
          <el-table
            ref="sinoTable"
            v-loading="tableLoading"
            :data="tableData"
            :row-key="getRowKeys"
            height="300px"
            stripe
            header-row-class-name="header-row"
            border
            @select="handleSelectionChange"
          >
            <el-table-column type="selection" width="50" align="center" :reserve-selection="true"></el-table-column>
            <el-table-column v-for="(column, index) in tableColumn" :key="index" :prop="column.prop" :label="column.label" :min-width="column.minWidth" show-overflow-tooltip>
              <template slot-scope="scope">
                <span>{{ scope.row[column.prop] }}</span>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            v-if="scopeType === 1 || scopeType === 2 || scopeType === 5 || scopeType === 6"
            :current-page="pagination.current"
            :page-sizes="[15, 30, 50, 100]"
            :page-size="pagination.size"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </el-main>
    </el-container>
  </div>
</template>
<script>
import { transData } from '@/util'
export default {
  name: 'ScopeChoice',
  components: {},
  props: {
    scopeType: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      ishow: false,
      filterText: '',
      treeData: [],
      treeLoading: true,
      expanded: [],
      tableLoading: false,
      unitTypeList: [],
      tableColumn: [],
      tableHeight: '',
      tableData: [],
      multipleSelection: [],
      radioObj: {},
      // -----------------------------Pagination
      pagination: {
        current: 1,
        size: 15
      }, // 分页数据
      total: 0, // 数据总条数
      selectList: [],
      allSpaceTreeData: [],
      unitTableColumn: [
        {
          prop: 'unitComName',
          label: '名称'
        },
        {
          prop: 'unitComCode',
          label: '编码'
        }
      ],
      depTableColumn: [
        {
          prop: 'deptName',
          label: '名称'
        },
        {
          prop: 'deptCode',
          label: '编码'
        }
      ],
      personTableColumn: [
        {
          prop: 'staffName',
          label: '名称'
        },
        {
          prop: 'staffNumber',
          label: '编码'
        }
      ],
      jobTableColumn: [
        {
          prop: 'jobName',
          label: '名称'
        },
        {
          prop: 'jobCode',
          label: '编码'
        }
      ],
      postTableColumn: [
        {
          prop: 'postName',
          label: '名称'
        },
        {
          prop: 'postType',
          label: '岗位类型'
        },
        {
          prop: 'postNature',
          label: '岗位性质'
        },
        {
          prop: 'postDuty',
          label: '岗位职责'
        }
      ],
      spaceTableColumn: [
        {
          prop: 'localSpaceName',
          label: '名称'
        },
        {
          prop: 'modelCode',
          label: '编码'
        }
      ],
      assetsTableColumn: [
        {
          prop: 'assetName',
          label: '名称'
        },
        {
          prop: 'assetCode',
          label: '编码'
        }
      ]
    }
  },
  computed: {
    defaultProps() {
      if (this.scopeType === 1) {
        return { label: 'unitComName', children: 'children' }
      } else if (this.scopeType === 2) {
        return { label: 'unitComName', children: 'children' }
      } else if (this.scopeType === 5) {
        return { label: 'ssmName', children: 'children' }
      } else if (this.scopeType === 6) {
        return { label: 'baseName', children: 'children' }
      } else {
        return {}
      }
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    },
    scopeType(e) {
      this.getTableData(e)
    }
  },
  mounted() {
    this.getTableData(this.scopeType)
  },
  methods: {
    getRowKeys(row) {
      if (this.scopeType === 0) {
        return row.umId
      } else {
        return row.id
      }
    },
    getTableData(type) {
      switch (type) {
        case 0:
          this.tableColumn = this.unitTableColumn
          this.getUnitListFn()
          break
        case 1:
          this.tableColumn = this.depTableColumn
          this.getUnitTreeFn()
          break
        case 2:
          this.tableColumn = this.personTableColumn
          this.getUnitTreeFn()
          break
        case 3:
          this.tableColumn = this.postTableColumn
          this.postListFn()
          break
        case 4:
          this.tableColumn = this.jobTableColumn
          this.tableData = []
          break
        case 5:
          this.tableColumn = this.spaceTableColumn
          this.getSpaceTree()
          break
        case 6:
          this.tableColumn = this.assetsTableColumn
          this.getProfessionalCategory()
      }
    },
    // 单位列表
    getUnitListFn() {
      this.tableLoading = true
      this.$api
        .getUnitList({})
        .then((res) => {
          this.tableLoading = false
          if (res.code == 200) {
            this.tableData = res.data ? res.data : []
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    // 单位列表tree 树
    getUnitTreeFn() {
      this.treeLoading = true
      this.$api.getUnitList({}).then((res) => {
        this.treeLoading = false
        if (res.code == 200) {
          this.treeData = res.data ? res.data : []
          if (this.scopeType == 1) {
            this.getDeptListFn()
          } else if (this.scopeType == 2) {
            this.getPersonListFn()
          }
        }
      })
    },
    // 部门列表
    getDeptListFn() {
      this.tableLoading = true
      let data = {
        ...this.pagination,
        unitId: this.checkedData ? this.checkedData.umId : ''
      }
      this.$api.departList(data).then((res) => {
        this.tableLoading = false
        if (res.code == 200) {
          this.tableData = res.data.records
          this.total = res.data.total
        }
      })
    },
    //  获取岗位列表
    postListFn() {
      this.tableLoading = true
      this.$api.selectByList().then((res) => {
        this.tableLoading = false
        if (res.code == 200) {
          this.tableData = res.data
        }
      })
    },
    // 人员列表
    getPersonListFn() {
      this.tableLoading = true
      this.$api
        .staffListByPage({
          ...this.pagination,
          pmId: this.checkedData ? this.checkedData.umId : ''
        })
        .then((res) => {
          if (res.code == 200) {
            this.tableLoading = false
            this.tableData = res.data.records
            this.total = res.data.total
          }
        })
    },
    // 空间tree
    getSpaceTree() {
      this.treeLoading = true
      this.$api.getStructureTree({}).then((res) => {
        this.treeLoading = false
        if (res.code == '200') {
          this.treeData = transData(res.data, 'id', 'pid', 'children')
          this.getSpaceList()
        }
      })
    },
    // 获取空间列表
    getSpaceList() {
      let obj = this.checkedData
      let simCode = ''
      if (obj) {
        if (!obj.parentGridIds) {
          obj.parentGridIds = '#'
        }
        simCode = obj ? obj.parentGridIds + ',' + obj.id : ''
      } else {
        simCode = ''
      }
      this.tableLoading = true
      let data = {
        current: this.pagination.current,
        functionDictId: '',
        keywords: '',
        orderItems: [],
        simCode: simCode,
        size: this.pagination.size
      }
      this.$api.getSpaceInfoList(data).then((res) => {
        if (res.code == '200') {
          this.allSpaceTreeData = res.data.records
          this.tableData = res.data.records
          this.total = res.data.total
          this.tableLoading = false
        }
      })
    },
    // 获取资产分类
    getProfessionalCategory() {
      this.treeLoading = true
      this.$api.getProfessionalCategory().then((res) => {
        if (res.code == '200') {
          this.treeData = res.data
          this.getAssetDataList()
        }
        this.treeLoading = false
      })
    },
    // 获取资产列表
    getAssetDataList() {
      let data = {
        currentPage: this.pagination.current,
        pageSize: this.pagination.size,
        professionalCategoryCode: this.checkedData ? this.checkedData.id : '' // 专业类别
      }
      this.tableLoading = true
      this.$api.getAssetList(data).then((res) => {
        if (res.code == '200') {
          this.tableData = res.data.assetDetailsList
          this.total = parseInt(res.data.sum)
        }
      })
      this.tableLoading = false
    },
    // 过滤
    filterNode(value, data) {
      if (!value) return true
      if (this.scopeType === 1 || this.scopeType === 2) {
        return data.unitComName.indexOf(value) !== -1
      } else if (this.scopeType === 5) {
        return data.ssmName.indexOf(value) !== -1
      } else {
        return data.baseName.indexOf(value) !== -1
      }
    },
    // 树状图点击
    handleNodeClick(data) {
      this.pagination.current = 1
      this.checkedData = data
      if (this.scopeType === 1) {
        this.getDeptListFn()
      } else if (this.scopeType === 2) {
        this.getPersonListFn()
      } else if (this.scopeType === 5) {
        this.getSpaceList()
      } else {
        this.getAssetDataList()
      }
    },
    //  ----------------------------------------------Tree_Fn
    // ---------------------------------------------------------- TabelFn
    handleSizeChange(val) {
      this.pagination.current = 1
      this.pagination.size = val
      if (this.scopeType === 1) {
        this.getDeptListFn()
      } else if (this.scopeType === 2) {
        this.getPersonListFn()
      } else if (this.scopeType === 5) {
        this.getSpaceList()
      } else if (this.scopeType === 6) {
        this.getAssetDataList()
      }
    },
    handleCurrentChange(val) {
      this.pagination.current = val
      if (this.scopeType === 1) {
        this.getDeptListFn()
      } else if (this.scopeType === 2) {
        this.getPersonListFn()
      } else if (this.scopeType === 5) {
        this.getSpaceList()
      } else if (this.scopeType === 6) {
        this.getAssetDataList()
      }
    },
    handleSelectionChange(val, row) {
      this.$refs.sinoTable.clearSelection()
      if (val.some((i) => i.id == row.id)) {
        this.$refs.sinoTable.toggleRowSelection(row, true)
        this.multipleSelection = [row]
      } else {
        this.$refs.sinoTable.toggleRowSelection(row, false)
        this.multipleSelection = []
      }
      this.selectList = JSON.parse(JSON.stringify(this.multipleSelection))
    }
  }
}
</script>
<style lang="scss" scoped>
.el-main {
  padding: 6px 24px 8px;
  overflow: hidden;
  .sino_panel {
    height: 100%;
    border-radius: 10px;
    background: #fff;
    position: relative;
    font-size: 14px;
  }
  .sino_page {
    height: 100%;
    position: relative;
    .el-aside {
      width: 260px;
      margin: 0 16px 0 0;
      overflow: hidden;
    }
    .sino_page_left {
      margin: 0 0 0 16px !important;
    }
    .el-aside,
    .el-main {
      // height: 100%;
      background: #fff;
      border-radius: 10px;
      padding: 0;
      .el-collapse {
        height: calc(100% - 40px);
        overflow: auto;
      }
    }
  }
}
.el-collapse {
  height: 330px;
  overflow: auto;
}
.topSearch {
  padding: 5px 10px;
}
.treeBox {
  height: 300px;
  width: 100%;
  overflow-x: hidden;
  overflow-y: scroll;
}
.el-pagination {
  margin-top: 10px;
}
</style>
<style lang="scss">
.header-row {
  .el-checkbox {
    display: none !important;
  }
}
</style>
