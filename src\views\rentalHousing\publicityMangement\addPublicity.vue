<template>
  <PageContainer v-loading="pageLoading" :footer="true">
    <div slot="content" class="space-content" style="height: 100%">
      <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
        <span class="green_line"></span>
        基本信息
      </div>
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="公示名称" prop="title">
              <el-input v-model="ruleForm.title" :maxlength="50" show-word-limit placeholder="请输入公示名称"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="公示范围" prop="typeCode">
              <el-select v-model="ruleForm.typeCode" clearable filterable placeholder="请选择公示范围" @change="changeScope">
                <el-option v-for="item in scopeOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
              <el-select
                v-if="ruleForm.typeCode == '1'"
                v-model="deptValues"
                multiple
                clearable
                collapse-tags
                filterable
                placeholder="请选择公示范围"
                style="margin-left: 10px; width: 240px"
              >
                <el-option v-for="item in deptList" :key="item.id" :label="item.deptName" :value="item.id"> </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="14">
            <el-form-item label="公示概述" prop="overview">
              <el-input v-model="ruleForm.overview" :maxlength="200" :rows="3" show-word-limit type="textarea" placeholder="说明"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="14">
            <el-form-item label="公示内容" prop="editorContent" required>
              <editor ref="myTextEditor" v-model="ruleForm.editorContent" class="my-editor"></editor>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="14">
            <el-form-item label="定时发布时间">
              <el-date-picker
                v-model="ruleForm.timingTime"
                type="datetime"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                :picker-options="pickerOptions"
                placeholder="至少大于当前时间五分钟"
                @change="selectTime"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <noticeDetail ref="noticeDetail" :detailObj="detailObj"></noticeDetail>
    </div>
    <div slot="footer">
      <el-button type="primary" :disabled="pageLoading" @click="submitForm('0')">存草稿</el-button>
      <el-button type="primary" :disabled="pageLoading" @click="submitForm('1')">发布</el-button>
      <el-button type="primary" :disabled="pageLoading" @click="submitForm('preview')">预览</el-button>
      <el-button type="primary" :disabled="pageLoading" plain @click="$router.go(-1)">取消</el-button>
    </div>
  </PageContainer>
</template>
<script>
import editor from './quillEditor.vue'
import noticeDetail from './noticeDetail.vue'
export default {
  components: {
    editor,
    noticeDetail
  },
  data() {
    return {
      id: '',
      activeType: 'add',
      ruleForm: {
        title: '',
        typeCode: '',
        overview: '',
        editorContent: '',
        timingTime: ''
      },
      rules: {
        title: [
          { required: true, message: '请输入公示名称', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符之间', trigger: 'blur' }
        ],
        typeCode: [{ required: true, message: '请选择公示范围', trigger: 'blur' }],
        overview: [
          { required: true, message: '请输入公示概述', trigger: 'blur' },
          { min: 1, max: 200, message: '长度在 1 到 200 个字符之间', trigger: 'blur' }
        ],
        editorContent: [{ required: true, message: '请输入公示内容', trigger: 'change' }]
      },
      fileList: [],
      pageLoading: false,
      fileUrl: [],
      fileDetail: {},
      scopeOptions: [
        {
          value: '0',
          label: '全院公告',
          leaf: true
        },
        {
          value: '1',
          label: '部门公告',
          leaf: false
        }
      ],
      deptValues: [],
      deptList: [],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7
        }
      },
      detailObj: {}
    }
  },
  created() {
    this.id = this.$route.query.id || ''
    if (this.id) {
      this.getNoticeDetail()
    }
    this.getDeptListFn()
  },
  methods: {
    changeScope(val) {
      if (val == '1') {
        this.deptValues = []
      }
    },
    getDeptListFn() {
      this.$api
        .departList({
          current: 1,
          size: 999
        })
        .then((res) => {
          if (res.code == 200) {
            this.deptList = res.data.records
          }
        })
    },
    submitForm(status) {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          const params = {
            title: this.ruleForm.title,
            overview: this.ruleForm.overview,
            status,
            content: this.ruleForm.editorContent,
            timingTime: this.ruleForm.timingTime,
            typeCode: this.ruleForm.typeCode
          }
          if (params.typeCode == '1') {
            if (!this.deptValues.length) {
              return this.$message.warning('请选择部门')
            }
            const deptNames = []
            this.deptList.forEach((item) => {
              if (this.deptValues.some((i) => i == item.id)) {
                deptNames.push(item.deptName)
              }
            })
            params.officeId = this.deptValues.join(',')
            params.officeName = deptNames.join(',')
          }
          if (this.id) {
            params.noticeId = this.id
          }
          if (status == 'preview') {
            this.detailObj = params
            this.$refs.noticeDetail.dialogVisibleRole = true
          } else {
            this.pageLoading = true
            this.$api.rentalHousingApi.noticeSaveUpdata(params).then((res) => {
              if (res.code == '200') {
                this.$message({
                  showClose: true,
                  message: '添加公示成功!',
                  type: 'success',
                  duration: 2000,
                  onClose: () => {
                    this.$router.go(-1)
                  }
                })
              }
            })
          }
        }
      })
    },
    handleExceed() {
      this.$message.warning('当前限制选择 1 个文件，如需更改请删除原文件后再上传')
    },
    beforeAvatarUpload(file) {
      const fileSize = file.size / 1024 / 1024 < 20
      if (!fileSize) {
        this.$message.warning('上传图片大小不能超过 20MB!')
      }
      return fileSize
    },
    selectTime(value) {
      if (!value) return
      var thisTime = value
      thisTime = thisTime.replace(/-/g, '/')
      var time = new Date(thisTime)
      time = time.getTime()
      var now = Date.now()
      if (time < now + 5 * 60 * 1000) {
        this.$message.error('至少大于当前时间五分钟')
        this.ruleForm.timingTime = ''
      }
    },
    getNoticeDetail() {
      this.$api.rentalHousingApi.noticeDetail({ id: this.id }).then((res) => {
        if (res.code == '200') {
          this.ruleForm = res.data
          this.$refs.myTextEditor.content = res.data.content
          this.deptValues = res.data.officeId.split(',')
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.space-content {
  height: calc(100% - 30px);
  overflow: auto;
  padding: 20px 40px;
  background: #fff;
  border-radius: 0px 4px 4px 0px;
  .fileGroup {
    width: 100%;
    .fileItem {
      display: flex;
      width: 100%;
      .fileItemName {
        color: #409eff;
        .itemName {
          margin-right: 10px;
        }
      }
    }
  }
}
</style>
<style lang="scss">
.cascaderPopper {
  // background: #eee;
}
</style>
