<template>
    <PageContainer>
        <div slot="content" class="role-content" style="height: 100%">
            <div class="role-content-right">
                <div style="height: 100%">
                    <div class="search-from">
                        <div>
                            <el-input v-model.trim="seachFrom.nameorCodeorRoom" placeholder="名称/编码"
                                style="width: 200px; margin-right: 10px" clearable></el-input>
                            <el-date-picker v-model="seachFrom.datatime" type="date" value-format="yyyy-MM-dd"
                                style="width: 200px; margin-right: 10px" placeholder="选择日期"> </el-date-picker>
                            <el-cascader v-model="seachFrom.deptId" :options="deptOptions"
                                :props="{ expandTrigger: 'hover', checkStrictly: false, emitPath: false, multiple: true, label: 'deptName', value: 'id' }"
                                placeholder="所属科室" clearable filterable collapse-tags
                                style="width: 260px; margin-right: 10px"></el-cascader>
                            <el-button type="primary" plain @click="resetForm">重置</el-button>
                            <el-button type="primary" @click="searchForm">查询</el-button>
                        </div>
                        <div>
                            <el-button type="primary" @click="synchronous">同步</el-button>
                        </div>
                    </div>
                    <div class="contentTable">
                        <div class="contentTable-main table-content">
                            <el-table v-loading="tableLoading" border style="width: 100%" :data="tableData"
                                height="100%" stripe>
                                <el-table-column prop="doctorCode" label="编码" show-overflow-tooltip></el-table-column>
                                <el-table-column prop="doctorName" label="医生名称" show-overflow-tooltip></el-table-column>
                                <el-table-column prop="title" label="职称" show-overflow-tooltip></el-table-column>
                                <el-table-column prop="departmentName" label="所在科室"
                                    show-overflow-tooltip></el-table-column>
                                <el-table-column prop="chairName" label="出诊椅位" show-overflow-tooltip></el-table-column>
                                <el-table-column prop="visitDate" label="出诊日期" show-overflow-tooltip>
                                    <template slot-scope="scope">
                                        <span>{{ scope.row.visitDate }}&nbsp;{{ scope.row.visitBeginTime }}-{{
                                            scope.row.visitEndTime }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="numberTotal" label="网约号源数"
                                    show-overflow-tooltip></el-table-column>
                                <el-table-column prop="numberRegist" label="挂号数"
                                    show-overflow-tooltip></el-table-column>
                                <el-table-column prop="numberRemain" label="剩余号量"
                                    show-overflow-tooltip></el-table-column>
                                <el-table-column prop="numberWait" label="候诊数" show-overflow-tooltip></el-table-column>
                                <el-table-column prop="numberVisit" label="已诊数" show-overflow-tooltip></el-table-column>
                                <!-- <el-table-column prop="numberReturn" label="退号数"
                                    show-overflow-tooltip></el-table-column>
                                <el-table-column prop="" label="当前患者" show-overflow-tooltip></el-table-column> -->
                                <el-table-column prop="deptName" label="操作" show-overflow-tooltip>
                                    <template slot-scope="scope">
                                        <el-button type="text" @click="operating(scope.row)">查看</el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                        <div class="contentTable-footer">
                            <el-pagination :current-page="pagination.current" :page-sizes="pagination.pageSizeOptions"
                                :page-size="pagination.size" :layout="pagination.layoutOptions"
                                :total="pagination.total" @size-change="paginationSizeChange"
                                @current-change="paginationCurrentChange">
                            </el-pagination>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </PageContainer>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin.js'
import moment from 'moment'
import { transData } from '@/util'
export default {
    name: 'numSourceManagement',
    mixins: [tableListMixin],
    beforeRouteEnter(to, from, next) {
        // 进入页面时，先将当前页面的 name 信息存入 keep-alive 全局状态（当前页面的name需要和路由的name一致）
        next((vm) => {
            // 二级页面存储当前级，多级页面存储多级
            vm.$store.commit('keepAlive/add', 'numSourceManagement')
        })
    },
    async beforeRouteLeave(to, from, next) {
        // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
        if (!['registrationRecord'].includes(to.name)) {
            // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
            await this.$store.commit('keepAlive/clean')
        }
        next()
    },
    data() {
        return {
            seachFrom: {
                nameorCodeorRoom: '', // 名称/编码,
                datatime: moment().format('YYYY-MM-DD'), // 日期
                deptId: '' // 科室
            },
            deptOptions: [],
            tableLoading: false,
            tableData: []
        }
    },
    created() {
        this.getDeptList()
        this.getDataList()
    },
    activated() {
        this.getDataList()
    },
    methods: {
        // 获取部门列表
        getDeptList() {
            this.$api.getSelectedDept({}).then((res) => {
                if (res.code == 200) {
                    this.deptOptions = transData(res.data, 'id', 'pid', 'children')
                }
            })
        },
        getDataList() {
            let params = {
                page: this.pagination.current,
                pageSize: this.pagination.size,
                visitDate: this.seachFrom.datatime,
                departmentId: this.seachFrom.deptId ? this.seachFrom.deptId.join(',') : '',
                searchKeyword: this.seachFrom.nameorCodeorRoom
            }
            this.tableLoading = true
            this.$api.getDoctorPage(params).then((res) => {
                if (res.code == '200') {
                    this.tableData = res.data.records
                    this.pagination.total = parseInt(res.data.total)
                } else {
                    this.$message.error(res.message)
                }
            })
            this.tableLoading = false
        },
        synchronous() {
            this.$api.synHisDoctorData().then((res) => {
                if (res.code == '200') {
                    this.$message.success(res.msg)
                    this.resetForm()
                } else {
                    this.$message.error(res.msg)
                }
            })
        },
        // 重置
        resetForm() {
            this.seachFrom.nameorCodeorRoom = ''
            this.seachFrom.datatime = moment().format('YYYY-MM-DD')
            this.seachFrom.deptId = ''
            this.pagination.size = 15
            this.pagination.current = 1
            this.getDataList()
        },
        // 查询
        searchForm() {
            this.pagination.current = 1
            this.getDataList()
        },
        // 操作记录分页
        pagingLoad() {
            if (this.activities.length < this.drawerTotal) {
                this.drawerPageNo += 1
            }
        },
        operating(row) {
            this.$router.push({
                path: '/numSourceManagement/registrationRecord',
                query: {
                    doctorCode: row.doctorCode,
                    datatime: this.seachFrom.datatime
                }
            })
        }
    }
}
</script>
<style lang="scss" scoped>
.el-tree-node__content {
    width: 100%;

    .custom-tree-node {
        display: inline-block;
        width: 100%;
        overflow: hidden;

        .item {
            display: inline-block;
            width: calc(100%);
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
        }
    }
}

::v-deep .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
    color: #3562db;
    background: linear-gradient(to right, #d9e1f8, #fff);
}

::v-deep .el-tree-node__content {
    height: 32px;
}

.role-content {
    height: 100%;
    display: flex;

    .role-content-right {
        height: 100%;
        min-width: 0;
        padding: 20px;
        background: #fff;
        border-radius: 4px;
        flex: 1;

        .search-from {
            padding-bottom: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;

            &>div {
                margin-right: 10px;
            }

            &>button {
                margin-top: 12px;
            }
        }

        .contentTable {
            height: calc(100% - 40px);
            display: flex;
            flex-direction: column;

            .contentTable-main {
                flex: 1;
                overflow: auto;
            }

            .contentTable-footer {
                padding: 10px 0 0;
            }
        }
    }

    .content {
        width: 100%;
        max-height: 500px !important;
        overflow: auto;
        background-color: #fff !important;
    }
}

::v-deep .el-drawer__body {
    background-color: #fff;
    margin: 20px;
    padding: 20px 10px;
    height: calc(100% - 80px);
    overflow-y: auto;
}

::v-deep .el-timeline-item__timestamp.is-bottom {
    font-size: 14px;
    position: absolute;
    left: -100px;
    top: -5px;
    font-weight: 600;
    color: #121f3e;
}

::v-deep .el-timeline {
    padding-left: 120px;
}

.timeContent {
    height: 100%;
    overflow: auto;

    .time {
        font-size: 14px;
        font-family: 'PingFang SC-Regular', 'PingFang SC';
        color: #414653;
    }

    .continer {
        display: flex;
        flex-wrap: wrap;

        .item {
            height: 32px;
            flex: 1;
            padding: 0 16px;
            background-color: #faf9fc;
            line-height: 32px;
            white-space: nowrap;
            margin-bottom: 20px;
            margin-right: 10px;
        }

        .itemContent {
            height: 32px;
            width: 220px;
            padding: 0 16px;
            background-color: #faf9fc;
            line-height: 32px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            cursor: pointer;
            margin-right: 10px;
        }
    }
}

::v-deep .el-timeline-item__node--normal {
    background: #fff;
    border: 2px solid #3562db;
}

.noData {
    display: inline-block;
    padding-bottom: 10px;
    width: 100%;
    margin: 0;
    font-size: 14px;
    color: #999;
    text-align: center;
}

.record {
    color: #66b1ff !important;
}

.delete {
    color: red !important;
}

::v-deep .el-tree-node {
    white-space: normal;
}

.rightBtn {
    height: 36px;
    margin: 0;
    margin-right: 10px;
}

.leadFile {
    display: flex;
}

.leadFile_item {
    margin: 10px 35px;
    color: #66b1ff;
    cursor: pointer;
}

::v-deep .el-message-box.no-close-btn .el-message-box__headerbtn {
    display: none;
}

::v-deep .el-cascader {
    line-height: 32px;

    .el-input__inner {
        height: 32px !important;
    }
}
</style>
