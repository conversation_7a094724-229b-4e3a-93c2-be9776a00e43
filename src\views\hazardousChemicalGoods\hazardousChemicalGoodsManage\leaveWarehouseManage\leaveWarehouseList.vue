<script>
import tableListMixin from '@/mixins/tableListMixin'
export default {
  name: 'leaveWarehouseList',
  components: {
    warehousingEntryDetailDialog: () => import('../components/warehousingDetail.vue'),
    exportDialog: () => import('../components/exportDialog.vue')
  },
  mixins: [tableListMixin],
  data() {
    return {
      searchForm: {
        recordNumber: '', //出库单号
        officesId: '', //领用科室
        officesName: '',
        warehouseName: '', //仓库id
        dateLine: [] //出库时间
      },
      hcsPagination: {
        current: 1,
        size: 15,
        total: 0
      },
      deptOptions: [], //领用单位下拉
      warehouseOptions: [], //库房下拉
      multipleSelection: [],
      tableData: [],
      hcsTableData: [], //物品table
      tableLoadingStatus: false,
      hcsTableLoadingStatus: false,
      sectionDialogShow: false, // 详情
      exportDialogShow: false, //导出
      recordNumber: '' //计划单号
    }
  },
  mounted() {
    this.getDataList()
    this.getWarehouseListFn()
    this.getDeptList()
  },
  methods: {
    // 获取库房下拉
    getWarehouseListFn() {
      this.$api.getWarehouseList({ status: '0' }).then((res) => {
        if (res.code == '200') {
          this.warehouseOptions = res.data.list
        }
      })
    },
    //  获取部门列表
    getDeptList() {
      this.$api.getSelectedDept().then((res) => {
        if (res.code == 200) {
          this.deptOptions = res.data
        }
      })
    },
    //列表操作
    operating(type, row) {
      if (type === 'details') {
        this.sectionDialogShow = true
        this.recordNumber = row.recordNumber
      } else if (type === 'print') {
        this.$router.push({
          name: 'outboundDeliveryOrder',
          query: {
            currentType: 'list',
            inWarehouseId: row.recordNumber
          }
        })
      } else if (type === 'edit') {
        let detainInfo = {
          id: row.id,
          type: 'outWarehouseWarrant',
          recordNumber: row.recordNumber
        }
        this.$emit('openDetailComponent', detainInfo)
      } else if (type === 'export') {
        this.exportDialogShow = true
      } else if (type === 'update') {
        this.getHcsRecordData(row.recordNumber)
      } else if (type === 'approval') {
        this.$confirm('确定提交出库吗?', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let params = {
            userId: this.$store.state.user.userInfo.user.staffId,
            userName: this.$store.state.user.userInfo.user.staffName,
            userType: 1,
            id: row.id,
            recordNumber: row.recordNumber
          }
          this.$api.commitAuditoutwarehouseData(params).then((res) => {
            if (res.code == 200) {
              this.onSearch()
              this.$message({
                message: res.message,
                type: 'success'
              })
            } else {
              this.$message.error(res.msg)
            }
          })
        })
      } else if (type === 'delete') {
        this.deleteOutWarehouseDataFn(row)
      } else if (type === 'withdraw') {
        this.withdrawOutWarehouseDataFn(row)
      }
    },
    // 撤回
    withdrawOutWarehouseDataFn(row) {
      this.$confirm('确定撤回吗?', '提示', {
        cancelButtonClass: 'el-button--primary is-plain',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let params = {
          userId: this.$store.state.user.userInfo.user.staffId,
          userName: this.$store.state.user.userInfo.user.staffName,
          userType: 1,
          id: row.id,
          recordNumber: row.recordNumber
        }
        this.$api.withdrawOutWarehouseData(params).then((res) => {
          if (res.code == 200) {
            this.onSearch()
            this.$message({
              message: res.message,
              type: 'success'
            })
          } else {
            this.$message.error(res.msg)
          }
        })
      })
    },
    //删除
    deleteOutWarehouseDataFn(row) {
      this.$confirm('确定删除吗?', '提示', {
        cancelButtonClass: 'el-button--primary is-plain',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let params = {
          userId: this.$store.state.user.userInfo.user.staffId,
          userName: this.$store.state.user.userInfo.user.staffName,
          userType: 1,
          id: row.id,
          recordNumber: row.recordNumber
        }
        this.$api.deleteOutWarehouseRecordData(params).then((res) => {
          if (res.code == 200) {
            this.onSearch()
            this.$message({
              message: res.message,
              type: 'success'
            })
          } else {
            this.$message.error(res.msg)
          }
        })
      })
    },
    //table选择
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 获取列表数据
    getDataList() {
      this.tableData = []
      this.tableLoadingStatus = true
      const params = {
        pageSize: this.pagination.size,
        currentPage: this.pagination.current,
        userId: this.$store.state.user.userInfo.user.staffId,
        userName: this.$store.state.user.userInfo.user.staffName,
        ...this.searchForm
      }
      params.beginDate = params.dateLine.length ? params.dateLine[0] + ' ' + '00:00:00' : ''
      params.endDate = params.dateLine.length ? params.dateLine[1] + ' ' + '23:59:59' : ''
      delete params.dateLine
      this.$api
        .queryOutWarehouseByPage(params)
        .then((res) => {
          this.tableLoadingStatus = false
          if (res.code === '200') {
            this.tableData = res.data.list
            this.pagination.total = res.data.sum
            if (this.tableData && this.tableData.length) {
              this.getHcsRecordData(this.tableData[0].recordNumber)
            } else {
              this.hcsTableData = []
              this.hcsPagination.total = 0
            }
          } else {
            this.tableData = []
            this.pagination.total = 0
          }
        })
        .catch((err) => {
          this.tableLoadingStatus = false
        })
    },
    //获取危化品记录列表
    getHcsRecordData(recordNumber) {
      this.hcsTableData = []
      this.hcsTableLoadingStatus = true
      const params = {
        pageSize: this.hcsPagination.size,
        currentPage: this.hcsPagination.current,
        orderNumber: recordNumber,
        userId: this.$store.state.user.userInfo.user.staffId,
        userName: this.$store.state.user.userInfo.user.staffName
      }
      this.$api
        .getHcsRecordByPage(params)
        .then((res) => {
          this.hcsTableLoadingStatus = false
          if (res.code === '200') {
            this.hcsTableData = res.data.list
            this.hcsPagination.total = res.data.sum
          } else {
            this.hcsTableData = []
            this.hcsPagination.total = 0
          }
        })
        .catch((err) => {
          this.hcsTableLoadingStatus = false
        })
    },
    // 表单搜索按钮点击
    onSearch() {
      this.pagination.current = 1
      this.getDataList()
    },
    // 表单重置按钮点击
    onReset() {
      this.$refs.formRef.resetFields()
      this.onSearch()
    },
    //危化品记录列表分页
    paginationHcsCurrentChange(val) {
      this.hcsPagination.page = val
      this.getHcsRecordData(this.tableData[0].recordNumber)
    },
    paginationHcsSizeChange(val) {
      this.hcsPagination.size = val
      this.getHcsRecordData(this.tableData[0].recordNumber)
    },
    //导出
    submitExportDialog(checkList) {
      let arr = []
      checkList.forEach((item) => {
        arr.push(item.EnglishName)
      })
      let temp = JSON.stringify(arr)
      let temp1 = []
      this.multipleSelection.forEach((v, i) => {
        temp1[i] = this.multipleSelection[i].id
      })
      const userInfo = this.$store.state.user.userInfo.user
      temp1 = temp1.join(',')
      let url =
        __PATH.BASE_URL_HSC +
        'outwarehouseRecord/export?titles=' +
        temp +
        '&ids=' +
        temp1 +
        '&unitCode=' +
        userInfo.unitCode +
        '&hospitalCode=' +
        userInfo.hospitalCode +
        '&userId=' +
        userInfo.staffId +
        '&userName=' +
        userInfo.staffName
      var a = document.createElement('a')
      a.href = url
      a.target = '_self'
      a.click()
      this.exportDialogShow = false
    }
  }
}
</script>
<template>
  <PageContainer class="leaveWarehouseList">
    <template #content>
      <div class="leaveWarehouseList__header">
        <el-form ref="formRef" :model="searchForm" class="leaveWarehouseList__search" inline @submit.native.prevent="onSearch">
          <el-form-item prop="recordNumber">
            <el-input v-model="searchForm.recordNumber" clearable filterable placeholder="出库单号"></el-input>
          </el-form-item>
          <el-form-item prop="officesName">
            <!-- <el-select v-model="searchForm.officesId" placeholder="领用部门" filterable>
              <el-option v-for="item in deptOptions" :key="item.id" :label="item.deptName" :value="item.id"></el-option>
            </el-select> -->
            <el-input v-model="searchForm.officesName" clearable filterable placeholder="领用部门"></el-input>
          </el-form-item>
          <el-form-item prop="warehouseName">
            <el-select v-model="searchForm.warehouseName" filterable placeholder="请选择仓库">
              <el-option v-for="item of warehouseOptions" :key="item.id" :label="item.warehouseName" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="dateLine">
            <el-date-picker
              v-model="searchForm.dateLine"
              type="daterange"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="出库开始时间"
              end-placeholder="出库结束时间"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" plain @click="onReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="leaveWarehouseList__actions">
        <el-button
          type="primary"
          v-auth="'ckManagement:submit'"
          @click="operating('approval', multipleSelection[0])"
          :disabled="multipleSelection.length != 1 || multipleSelection[0].status != '暂存'"
        >
          提交出库</el-button
        >
        <el-button
          type="primary"
          v-auth="'ckManagement:edit'"
          @click="operating('edit', multipleSelection[0])"
          :disabled="multipleSelection.length != 1 || multipleSelection[0].status != '暂存'"
        >
          修改</el-button
        >
        <el-button
          type="danger"
          v-auth="'ckManagement:del'"
          plain
          @click="operating('delete', multipleSelection[0])"
          :disabled="multipleSelection.length != 1 || multipleSelection[0].status != '暂存'"
        >
          删除</el-button
        >
        <el-button type="primary" @click="operating('print', multipleSelection[0])" :disabled="multipleSelection.length != 1">打印</el-button>
        <el-button type="primary" v-auth="'ckManagement:export'" @click="operating('export', multipleSelection[0])" :disabled="multipleSelection.length < 1"> 导出</el-button>
        <!-- <el-button type="primary" @click="operating('withdraw',  multipleSelection[0])"
          :disabled="multipleSelection.length != 1 || multipleSelection[0].status != '待审核'"> 撤回</el-button> -->
      </div>
      <div class="leaveWarehouseList__approveTable">
        <el-table
          v-loading="tableLoadingStatus"
          height="calc(100% - 50px)"
          :data="tableData"
          border
          stripe
          table-layout="auto"
          class="tableAuto"
          row-key="id"
          @selection-change="handleSelectionChange"
          @row-dblclick="(row) => operating('details', row)"
          @row-click="(row) => operating('update', row)"
          title="双击列表查看详情"
        >
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <span
                :style="{
                  color: row.status == '暂存' ? '#FF9A2E' : '#008AF4'
                }"
                >{{ row.status }}</span
              >
            </template>
          </el-table-column>
          <el-table-column prop="recordNumber" label="出库单号" show-overflow-tooltip></el-table-column>
          <el-table-column prop="outwarehouseName" label="出库类型" show-overflow-tooltip></el-table-column>
          <el-table-column prop="outwarehouseCount" label="出库数量" show-overflow-tooltip></el-table-column>
          <el-table-column prop="warehouseName" label="出库仓库" show-overflow-tooltip></el-table-column>
          <el-table-column prop="officesName" label="领用部门" show-overflow-tooltip></el-table-column>
          <el-table-column prop="createName" label="出库人" show-overflow-tooltip></el-table-column>
          <el-table-column prop="createTime" label="出库时间" show-overflow-tooltip></el-table-column>
          <el-table-column prop="acceptorName" label="领用人" show-overflow-tooltip></el-table-column>
          <!-- <el-table-column prop="examinePerson" label="审核人" show-overflow-tooltip></el-table-column>
          <el-table-column prop="examineTime" label="审核时间" show-overflow-tooltip></el-table-column>
          <el-table-column prop="examineNotes" label="审核批注" show-overflow-tooltip></el-table-column> -->
        </el-table>
        <div class="leaveWarehouseList__pagination">
          <el-pagination
            :current-page="pagination.page"
            :page-sizes="pagination.pageSizeOptions"
            :page-size="pagination.size"
            :layout="pagination.layoutOptions"
            :total="pagination.total"
            @size-change="paginationSizeChange"
            @current-change="paginationCurrentChange"
          >
          </el-pagination>
        </div>
      </div>
      <div class="leaveWarehouseList__materialsTable">
        <el-table v-loading="hcsTableLoadingStatus" height="calc(100% - 50px)" :data="hcsTableData" border stripe table-layout="auto" class="tableAuto" row-key="id">
          <el-table-column type="index" width="65" label="序号"></el-table-column>
          <el-table-column prop="materialTypeName" label="危化品分类" show-overflow-tooltip></el-table-column>
          <!-- <el-table-column prop="materialCode" label="危化品编码" show-overflow-tooltip></el-table-column> -->
          <el-table-column prop="materialName" label="危化品名称" show-overflow-tooltip></el-table-column>
          <el-table-column prop="basicUnitName" label="基础单位" show-overflow-tooltip></el-table-column>
          <el-table-column prop="model" label="规格型号" show-overflow-tooltip></el-table-column>
          <el-table-column prop="operateCount" label="出库数量" show-overflow-tooltip></el-table-column>
          <!-- <el-table-column prop="unitPrice" label="进货单价（元）" show-overflow-tooltip></el-table-column>
          <el-table-column prop="amount" label="总金额（元）" show-overflow-tooltip></el-table-column> -->
          <el-table-column prop="serviceLife" label="有效期" show-overflow-tooltip></el-table-column>
          <!-- <el-table-column prop="trademark" label="品牌" show-overflow-tooltip></el-table-column> -->
          <!-- <el-table-column prop="supplierName" label="供应商" show-overflow-tooltip></el-table-column> -->
        </el-table>
        <div class="leaveWarehouseList__pagination">
          <el-pagination
            :current-page="hcsPagination.page"
            :page-sizes="pagination.pageSizeOptions"
            :page-size="hcsPagination.size"
            :layout="pagination.layoutOptions"
            :total="hcsPagination.total"
            @size-change="paginationHcsSizeChange"
            @current-change="paginationHcsCurrentChange"
          >
          </el-pagination>
        </div>
        <!--出库单详情 -->
        <template v-if="sectionDialogShow">
          <warehousingEntryDetailDialog
            :sectionDialogShow="sectionDialogShow"
            :warehouseType="'2'"
            :recordNumber="recordNumber"
            @submitSectionDialog="
              () => {
                sectionDialogShow = false
              }
            "
            @closeSectionDialog="
              () => {
                sectionDialogShow = false
              }
            "
          />
        </template>
        <!--导出 -->
        <template v-if="exportDialogShow">
          <exportDialog
            :exportDialogShow="exportDialogShow"
            @submitExportDialog="submitExportDialog"
            :exportType="3"
            @closeExportDialog="
              () => {
                exportDialogShow = false
              }
            "
          />
        </template>
      </div>
    </template>
  </PageContainer>
</template>
<style scoped lang="scss">
.leaveWarehouseList {
  ::v-deep(> .container-content) {
    height: 100%;
    width: 100%;
    background-color: #fff;
  }
  &__header {
    display: flex;
    justify-content: space-between;
  }
  &__actions {
    padding-bottom: 10px;
  }
  &__approveTable {
    height: calc(48%);
  }
  &__materialsTable {
    height: calc(38%);
  }
  &__pagination {
    margin: 10px;
  }
  &__tag {
    // 暂存
    &--0 {
      --color: #ff9a2e;
    }
    // 入库
    &--1 {
      --color: #3562db;
    }
    &:before {
      content: '';
      display: inline-block;
      vertical-align: middle;
      margin-right: 4px;
      height: 4px;
      width: 4px;
      border-radius: 4px;
      background-color: var(--color);
    }
  }
  &__table__color {
    display: inline-block;
    height: 16px;
    width: 16px;
    vertical-align: text-top;
    margin-right: 4px;
  }
  .text-red {
    color: #ff1919;
  }
  .profile {
    color: #008af4;
    cursor: pointer;
  }
}
.el-form-item {
  margin-bottom: 8px !important;
}
</style>
