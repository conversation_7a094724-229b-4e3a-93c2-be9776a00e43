<script>
import { UsingStatusOptions } from '@/views/operationPort/constant'
import ContentCard from '@/components/ContentCard/index.vue'
import StatusNameEdit from './StatusNameEdit.vue'
export default {
  name: 'StatusDetail',
  components: { StatusNameEdit, ContentCard },
  events: ['update:visible', 'success'],
  props: {
    id: Number,
    visible: Boolean,
    readonly: <PERSON><PERSON><PERSON>
  },
  data: function() {
    return {
      formModel: {
        name: '',
        code: '',
        status: 1,
        remark: '',
        formName: '',
        // 项目类型
        formId: ''
      },
      rules: {
        name: [{ required: true, message: '请输入状态名称' }]
      },
      // 状态节点列表
      tableData: [],
      // 名称编辑弹窗
      dialog: {
        show: false,
        name: ''
      },
      // 当前选中的节点
      selection: [],
      loadingStatus: false,
      statusOptions: UsingStatusOptions,
      // 是否预置数据
      isPreset: false,
      // 业务表单列表
      formList: []
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    },
    toFetch() {
      return this.dialogVisible && this.id
    },
    // 是否可以编辑项目类型
    canEditFormName() {
      return !this.readonly && this.isPreset && !this.formModel.formName
    }
  },
  watch: {
    toFetch(val) {
      val && this.getDetail()
    }
  },
  methods: {
    onDrawerClosed() {
      this.$refs.formRef.resetFields()
      this.$refs.tableRef.clearSelection()
    },
    getDetail() {
      this.loadingStatus = true
      this.$api.SporadicProject.queryProjectStateDetail({ id: this.id })
        .then((res) => {
          if (res.code === '200') {
            // form data
            this.formModel.name = res.data.name
            this.formModel.status = +res.data.state
            this.formModel.remark = res.data.remark
            this.formModel.formName = res.data.businessFormName
            this.formModel.code = res.data.code
            this.isPreset = res.data.presetsType === 1
            this.tableData = res.data.detailList
            if (!this.tableData.length && res.data.flowKey) {
              return this.$api.SporadicProject.getWorkNodeListByFlowKey({ flowKey: res.data.flowKey })
            }
          } else {
            throw res.message
          }
        })
        .then((res) => {
          if (!res) return
          if (res.code === '200') {
            this.tableData = res.data.map((it) => ({
              stateName: '',
              workNodeId: it.id,
              workNodeName: it.name
            }))
          } else {
            throw res.msg || '获取状态机配置列表失败'
          }
        })
        .then(() => {
          // 如果是预置数据，并且项目类型为空，则获取项目类型
          if (this.canEditFormName && !this.formList.length) {
            this.getFormList()
          }
        })
        .catch((msg) => this.$message.error(msg || '获取状态详情失败'))
        .finally(() => (this.loadingStatus = false))
    },
    // 提交修改的内容
    onSubmit() {
      this.$refs.formRef
        .validate()
        .catch(() => Promise.reject())
        .then(() => {
          const [first] = this.tableData
          const [last] = this.tableData.slice(-1)
          if (!first || !last) {
            return Promise.reject('缺少状态机数据')
          } else if (!first.stateName && !last.stateName) {
            return Promise.reject('第一行和最后一行的状态机名称不能为空')
          } else if (!first.stateName) {
            return Promise.reject('第一行的状态机名称不能为空')
          } else if (!last.stateName) {
            return Promise.reject('最后一行的状态机名称不能为空')
          }
        })
        .then(() => {
          this.loadingStatus = true
          const params = {
            id: this.id,
            name: this.formModel.name,
            remark: this.formModel.remark,
            state: this.formModel.status,
            detailList: this.tableData
          }
          // 如果是可编辑状态，需要补充项目类型（表单）信息
          if (this.canEditFormName) {
            params.businessFormId = this.formModel.formId
            const formInfo = this.formList.find((it) => it.id === this.formModel.formId)
            if (!formInfo) return
            params.businessFormName = formInfo.name
          }
          return this.$api.SporadicProject.updateProjectState(params)
        })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('操作成功')
            this.$emit('success')
            this.dialogVisible = false
          } else {
            throw res.message
          }
        })
        .catch((msg) => {
          msg && this.$message.error(msg)
        })
        .finally(() => {
          this.loadingStatus = false
        })
    },
    // 点击批量修改
    onBatchEdit() {
      if (!this.selection.length) {
        this.$message.info('请选择要批量编辑的行')
      } else {
        this.dialog.name = ''
        // 取最后一个状态机的名称
        for (let i = this.selection.length - 1; i > -1; i--) {
          const { stateName } = this.selection[i]
          if (stateName) {
            this.dialog.name = stateName
            break
          }
        }
        this.dialog.show = true
      }
    },
    // 修改状态机名称后同步修改流程节点的状态机名称
    onNameUpdate(name) {
      this.dialog.show = false
      this.selection.forEach((row) => {
        const rowIndex = this.tableData.indexOf(row)
        const newRow = Object.assign({}, row, { stateName: name })
        this.$set(this.tableData, rowIndex, newRow)
      })
      this.selection = []
      this.$refs.tableRef.clearSelection()
    },
    // 选中行发生变更
    onSelectionChange(rows) {
      this.selection = rows
    },
    // 获取项目类型列表
    getFormList() {
      const defaultError = '获取项目类型列表失败'
      return this.$api.SporadicProject.queryProjectBusinessFormAll()
        .then((res) => {
          if (res.code === '200') {
            this.formList = res.data
          } else {
            throw defaultError
          }
        })
        .catch(() => {
          throw defaultError
        })
    },
    // 当formId改变，重新拉取状态机列表数据
    onFormIdChange(value) {
      this.tableData = []
      this.$refs.tableRef.clearSelection()
      const formInfo = this.formList.find((it) => it.id === value)
      if (!formInfo) return
      // 根据表单id关联的流程key，获取状态机列表
      const param = { flowKey: formInfo.flowKey }
      this.loadingStatus = true
      this.$api.SporadicProject.getWorkNodeListByFlowKey(param)
        .then((res) => {
          if (res.code === '200') {
            this.tableData = res.data.map(it => ({
              workNodeName: it.name,
              workNodeId: it.id
            }))
          }
        })
        .finally(() => {
          this.loadingStatus = false
        })
    }
  }
}
</script>
<template>
  <el-drawer
    v-loading="loadingStatus"
    class="component status-detail"
    title="状态详情"
    size="960px"
    :visible.sync="dialogVisible"
    :close-on-press-escape="false"
    :wrapper-closable="false"
    @closed="onDrawerClosed"
  >
    <ContentCard title="基本信息">
      <template #content>
        <el-form ref="formRef" :disabled="readonly" :model="formModel" :rules="rules" label-width="95px">
          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="状态名称" prop="name">
                <el-input v-model="formModel.name" placeholder="请输入"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="状态编码" prop="code">
                <el-input :value="formModel.code" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item v-if="canEditFormName" label="项目类型" prop="formId">
                <el-select v-model="formModel.formId" placeholder="请选择" @change="onFormIdChange">
                  <el-option v-for="item of formList" :key="item.id" :value="item.id" :label="item.name"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item v-else label="项目类型" prop="formName">
                <el-input :value="formModel.formName" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="状态" prop="status">
                <el-select v-model="formModel.status" placeholder="请选择" :disabled="isPreset">
                  <el-option v-for="item of statusOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="备注" prop="remark" class="full-line">
            <el-input v-model="formModel.remark" type="textarea" :rows="2" placeholder="请输入"></el-input>
          </el-form-item>
        </el-form>
      </template>
    </ContentCard>
    <ContentCard title="状态机配置" class="status-detail__table">
      <template v-if="!readonly" #title-right>
        <el-button type="primary" plain @click="onBatchEdit">批量编辑</el-button>
      </template>
      <template #content>
        <el-table ref="tableRef" height="100%" :data="tableData" border stripe table-layout="auto" class="tableAuto" row-key="workNodeId" @selection-change="onSelectionChange">
          <el-table-column v-if="!readonly" type="selection" width="50px"></el-table-column>
          <el-table-column label="节点名称" prop="workNodeName" show-overflow-tooltip></el-table-column>
          <el-table-column label="节点ID" prop="workNodeId" show-overflow-tooltip></el-table-column>
          <el-table-column label="状态机名称" prop="stateName" show-overflow-tooltip :formatter="(row) => row.stateName || '-'"></el-table-column>
        </el-table>
      </template>
    </ContentCard>
    <div class="status-detail__footer">
      <el-button type="primary" plain @click="dialogVisible = false">取消</el-button>
      <el-button v-if="!readonly" type="primary" @click="onSubmit">确认</el-button>
    </div>
    <!-- 状态名称设置 -->
    <StatusNameEdit :visible.sync="dialog.show" :name="dialog.name" @update="onNameUpdate"></StatusNameEdit>
  </el-drawer>
</template>
<style lang="scss">
.component.status-detail {
  .el-drawer {
    .el-drawer__header {
      color: #333;
      padding: 9px 16px;
      border-bottom: solid 1px #eee;
      margin-bottom: 0px;
    }
    .el-drawer__body {
      display: flex;
      flex-flow: column nowrap;
      overflow: hidden;
    }
  }
  .box-card {
    padding: 16px;
    .card-body {
      overflow: hidden;
    }
    .card-title {
      .card-name {
        flex: 1;
      }
    }
  }
  @mixin normal-text {
    cursor: default;
    color: #666;
    border: none;
    background-color: transparent;
  }
  .el-form {
    .el-form-item {
      .el-select {
        width: 100%;
      }
      .el-textarea.is-disabled .el-textarea__inner {
        @include normal-text;
        resize: none;
        padding-top: 9px;
      }
      .el-input.is-disabled {
        .el-input__inner {
          @include normal-text;
        }
        .el-input__icon {
          display: none;
        }
      }
    }
  }
  .status-detail {
    &__table {
      flex: 1;
      margin-top: -30px;
      padding-bottom: 0;
      overflow: hidden;
    }
    &__footer {
      padding: 16px;
      text-align: right;
    }
  }
}
</style>
