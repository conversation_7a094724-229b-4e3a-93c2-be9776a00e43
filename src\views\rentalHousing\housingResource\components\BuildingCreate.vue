<script>
import { FloorNameOptions, FloorNameType } from '@/views/rentalHousing/housingResource/constant'
export default {
  name: 'BuildingCreate',
  props: {
    visible: Boolean,
    // 是否楼栋单元操作
    isCell: Boolean,
    treeNodeId: String
  },
  events: ['update:visible', 'success'],
  data: () => ({
    formModel: {
      // 楼栋名称
      buildingName: '',
      // 单元名称
      cellName: '',
      // 单元数量
      cellCount: 1,
      // 地上层数
      upGroundCount: 1,
      // 地下层数
      underGroundCount: 0,
      // 楼层命名方式
      floorNameType: FloorNameType.L,
      // 楼层名称
      floorNames: []
    },
    rules: {
      buildingName: [{ required: true, message: '请输入楼栋编号' }],
      cellName: [{ required: true, message: '请输入单元名称' }]
    },
    loadingStatus: false,
    // 上次选中的命名规则
    $lastNameType: '1',
    // 备份的自定义命名
    $floorNames: []
  }),
  computed: {
    title: function() {
      return this.isCell ? '创建单元' : '创建楼栋'
    },
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    },
    // 是否自定义名称
    isCustom() {
      return this.formModel.floorNameType === '3'
    },
    floorNameTypeOptions() {
      return FloorNameOptions
    }
  },
  watch: {
    dialogVisible(val) {
      val && this.reGenerateNames()
    }
  },
  methods: {
    onDialogClosed() {
      // dialog关闭时重置form表单
      this.$refs.formRef.resetFields()
      // 重置自定义命名
      this.$floorNames = []
      this.$lastNameType = this.formModel.floorNameType
    },
    onSubmit() {
      this.$refs.formRef
        .validate()
        .catch(() => Promise.reject())
        .then(() => {
          // 如果是自定义命名，需要校验自定义命名是否为空
          if (this.isCustom) {
            const hasEmpty = this.formModel.floorNames.some((it) => !it.label)
            if (hasEmpty) {
              throw '请填写所有楼层名称'
            }
          }
        })
        .then(() => {
          // loading
          this.loadingStatus = true
          // config request data
          const params = {
            // 地上层数
            overgroundNumber: this.formModel.upGroundCount,
            // 地下层数
            undergroundNumber: this.formModel.underGroundCount,
            // 楼层命名规则
            floorNameRule: this.formModel.floorNameType,
            floorNameStr: this.formModel.floorNames.map((it) => `${it.name}_${it.label}`).join(','),
            userId: this.$store.getters.userId, // 用户id
            userName: this.$store.getters.userName // 用户名
          }
          if (this.isCell) {
            // 单元名称
            params.spaceName = this.formModel.cellName
            params.buildingId = this.treeNodeId
            return this.$api.rentalHousingApi.saveUnitInfo(params)
          } else {
            // 单元数量
            params.unitNumber = this.formModel.cellCount
            // 楼栋名称
            params.spaceName = this.formModel.buildingName
            params.estateId = this.treeNodeId
            return this.$api.rentalHousingApi.saveBuildingInfo(params)
          }
        })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('操作成功')
            this.$emit('success')
            this.dialogVisible = false
          } else {
            throw res.message || '操作失败'
          }
        })
        .catch((msg) => {
          msg && this.$message.error(msg)
        })
        .finally(() => {
          this.loadingStatus = false
        })
    },
    // 重新生成楼层名称
    reGenerateNames() {
      const type = this.formModel.floorNameType
      // 生成楼层名称
      function getFloorName(number, under = false) {
        if (type === FloorNameType.L) {
          return under ? `-L${number}` : `L${number}`
        } else if (type === FloorNameType.F) {
          return under ? `-${number}F` : `${number}F`
        } else if (type === FloorNameType.ZH) {
          return under ? `负${number}层` : `${number}层`
        }
      }
      const { upGroundCount, underGroundCount } = this.formModel
      const list = []
      for (let i = upGroundCount; i > 0; i--) {
        list.push({
          name: `${i}`,
          label: getFloorName(i)
        })
      }
      for (let j = 1; j <= underGroundCount; j++) {
        list.push({
          name: `-${j}`,
          label: getFloorName(j, true)
        })
      }
      this.formModel.floorNames = list
    },
    // 楼层命名规则发生变化时候
    onGenTypeChange(type) {
      if (this.$lastNameType === FloorNameType.ZH) {
        // 如果是从自定义切出，备份一次自定义名称
        this.$floorNames = this.formModel.floorNames.map(it => Object.assign({}, it))
      }
      this.reGenerateNames()
      this.$lastNameType = type
    }
  }
}
</script>
<template>
  <el-dialog
    v-dialogDrag
    class="component building-create"
    :title="title"
    width="550px"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    custom-class="model-dialog"
    destroy-on-close
    @close="onDialogClosed"
  >
    <el-form ref="formRef" v-loading="loadingStatus" :model="formModel" :rules="rules" label-width="95px">
      <el-form-item v-if="!isCell" label="楼栋名称" prop="buildingName">
        <el-input v-model="formModel.buildingName" placeholder="请输入楼栋名称" maxlength="50"></el-input>
      </el-form-item>
      <el-form-item v-if="isCell" label="单元名称" prop="cellName">
        <el-input v-model="formModel.cellName" placeholder="请输入单元名称" maxlength="50"></el-input>
      </el-form-item>
      <!--楼栋新增时显示该字段-->
      <el-form-item v-if="!isCell" label="单元数量" prop="cellCount" style="margin-bottom: 10px">
        <el-input-number v-model="formModel.cellCount" placeholder="请输入单元数量" :step="1" :min="1" :max="10"></el-input-number>
      </el-form-item>
      <!--新增时显示该字段-->
      <el-form-item label="地上层数" prop="upGroundCount" style="margin-bottom: 10px">
        <el-input-number v-model="formModel.upGroundCount" placeholder="请输入地上层数" :step="1" :min="1" :max="99" @change="reGenerateNames"></el-input-number>
      </el-form-item>
      <el-form-item label="地下层数" prop="underGroundCount" style="margin-bottom: 0">
        <el-input-number v-model="formModel.underGroundCount" placeholder="请输入地下层数" :step="1" :min="0" :max="10" @change="reGenerateNames"></el-input-number>
      </el-form-item>
      <el-form-item label="楼层命名" prop="floorNameType" style="margin-bottom: 0">
        <el-radio-group v-model="formModel.floorNameType" @change="onGenTypeChange">
          <el-radio v-for="item of floorNameTypeOptions" :key="item.value" :label="item.value">{{ item.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <div class="building-create__table">
        <el-table size="mini" :data="formModel.floorNames" max-height="200px">
          <el-table-column label="实际层数" prop="name"></el-table-column>
          <el-table-column label="显示名称" prop="label">
            <template  #default="{row}">
              <el-input v-model="row.label" placeholder="请输入" maxlength="10" class="building-create__input--custom"></el-input>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-form>
    <template #footer>
      <el-button type="primary" plain @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" :loading="loadingStatus" @click="onSubmit">确认</el-button>
    </template>
  </el-dialog>
</template>
<style lang="scss" scoped>
.building-create {
  .el-form {
    background: #fff;
    width: 100%;
    padding: 16px;
    height: 100%;
  }
  .el-input-number {
    line-height: 30px;
  }
  &__table {
    padding: 0 0 0 90px;
  }
  &__input--custom {
    ::v-deep(.el-input__inner) {
      line-height: inherit;
      height: auto;
    }
  }
}
</style>
