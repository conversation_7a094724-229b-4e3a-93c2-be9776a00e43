<template>
    <PageContainer :footer="true">
        <div slot="header">
            <div class="searchForm">
                <div class="search-box">
                    <el-input v-model="assetsCodeName" :placeholder="'请输入设备名称编码'" clearable
                        style="width: 200px"></el-input>
                    <div class="ml-16">
                        <el-button type="primary" @click="search">查询</el-button>
                        <el-button type="primary" plain @click="reset">重置</el-button>
                    </div>
                </div>
            </div>
        </div>
        <div slot="content" ref="contentRef" class="table-box">
            <div class="tableContainer">
                <el-table v-loading="tableLoading" height="calc(100% - 96px)" :data="tableData" border stripe>
                    <el-table-column label="设备名称" prop="assetsName" show-overflow-tooltip
                        align="center"></el-table-column>
                    <el-table-column label="设备编码" prop="assetsCode" show-overflow-tooltip
                        align="center"></el-table-column>
                    <el-table-column label="控制参数" prop="executionParams" show-overflow-tooltip align="center">
                    </el-table-column>
                    <el-table-column label="执行状态" prop="executionStatus" show-overflow-tooltip align="center">
                        <template slot-scope="scope">
                            {{ scope.row.executionStatus === 1 ? '成功' : '失败' }}
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination :current-page="pagination.page" :page-sizes="[15, 30, 50, 100]"
                    :page-size="pagination.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="pageTotal"
                    @size-change="handleSizeChange" @current-change="handleCurrentChange">
                </el-pagination>
            </div>
        </div>
        <div slot="footer">
            <el-button type="primary" plain @click="$router.go(-1)">返回</el-button>
        </div>
    </PageContainer>
</template>
<script>
export default {
    name: 'calendarDetailsEquipment',
    data() {
        return {
            assetsCodeName: '',
            tableData: [],
            pagination: {
                pageSize: 15,
                page: 1
            },
            pageTotal: 0,
        }
    },
    mounted() {
        this.getTableData()
    },
    methods: {
        handleSizeChange(val) {
            this.pagination.pageSize = val
            this.pagination.page = 1
            this.getTableData()
        },
        getTableData() {
            this.tableLoading = true
            let data = {
                ...this.pagination,
                assetsCodeName: this.assetsCodeName,
                sceneId: this.$route.query.sceneId,
                timeScheduleId: this.$route.query.timeScheduleId,
                timeDate: this.$route.query.date
            }
            this.$api
                .getAssetsExecutionHistory(data)
                .then((res) => {
                    this.tableLoading = false
                    if (res.code == 200) {
                        this.tableData = res.data ? res.data.records : []
                        this.pageTotal = res.data ? res.data.total : 0
                    } else if (res.message) {
                        this.tableData = []
                        this.pagination.total = 0
                        this.$message.error(res.message)
                    }
                })
                .catch((err) => {
                    this.tableLoading = false
                })
        },

        handleCurrentChange(val) {
            this.pagination.page = val
            this.getTableData()
        },
        search() {
            this.pagination.page = 1
            this.getTableData()
        },
        reset() {
            this.assetsCodeName = ''
            this.pagination.page = 1
            this.pageTotal = 0
            this.getTableData()
        },

    }
}
</script>
<style lang="scss" scoped>
.searchForm {
    display: flex;
    height: 80px;
    padding: 0 16px;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .el-input {
        width: 200px;
        margin-left: 16px;
    }

    >div {
        margin-right: 20px;
    }
}

.search-box {
    display: flex;
    align-items: center;
}

.container-content>div {
    height: 100%;
    margin-top: 16px;
}

.el-table {
    margin-bottom: 12px;
}

::v-deep .el-table__row:hover {
    cursor: pointer;
}

.table-box {
    background-color: #fff;
    height: 100%;
    padding: 16px;

    .tableContainer {
        height: 100%;

        .el-table {
            height: calc(100% - 96px) !important;
        }
    }
}

.diaContent {
    width: 100%;
    max-height: 550px !important;
    overflow: auto;
    background-color: #fff !important;

    .el-input,
    .el-select,
    .el-cascader {
        width: 300px;
    }
}

.form-item {
    display: inline-block;
    margin-right: 20px;
}

.inputWidth {
    width: 820px;
}

.ml-16 {
    margin-left: 16px;
}

.dialog .el-dialog {
    width: 60% !important;
}
</style>
