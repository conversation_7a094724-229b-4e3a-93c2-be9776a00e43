<template>
  <el-dialog v-if="visible" v-dialogDrag :title="pageTitle" width="60%" :visible.sync="dialogShow"
    custom-class="model-dialog" :before-close="closeDialog">
    <div class="sapce_content">
      <div class="search-from">
        <el-input v-model="searchForm.nameOrChargePersonName" placeholder="值班考勤组名称或负责人" clearable
          style="width: 200px"></el-input>
        <div style="display: inline-block">
          <el-button type="primary" plain @click="resetEvent">重置</el-button>
          <el-button type="primary" @click="searchEvent">查询</el-button>
        </div>
      </div>
      <TablePage ref="tablePage" v-loading="tableLoading" class="tablePage" height="calc(100% - 80px)"
        :pageData="pageData" :pageProps="pageProps" border row-key="id" :tableColumn="tableColumn" :data="tableData"
        @pagination="paginationChange" @selection-change="attendanceSelectChange"></TablePage>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="submit">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script lang="jsx">
import { attendanceGroupTypeList } from './dict.js'
export default {
  name: 'selectShiftAttendanceDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'add'
    },
    defaultChecked: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      assetsCheckData: {},
      searchForm: {
        nameOrChargePersonName: ''
      },
      attendanceGroupTypeList,
      tableLoading: false,
      tableColumn: [
        {
          type: 'selection',
          align: 'center',
          reserveSelection: true,
          width: 60
        },
        {
          prop: 'name',
          label: '值班考勤组名称'
        },
        {
          prop: 'type',
          label: '类型',
          formatter: (scope) => {
            return scope.row.type ? attendanceGroupTypeList.find((v) => v.value == scope.row.type)?.label : ''
          }
        },
        {
          prop: 'signTimeStr',
          label: '班次'
        },
        {
          prop: 'dutyPostName',
          label: '值班岗'
        },
        {
          prop: 'personNum',
          label: '人数'
        },
        {
          prop: 'chargePersonName',
          label: '负责人'
        }
      ],
      tableData: [],
      pageData: {
        current: 1,
        size: 15,
        total: 0
      },
      pageProps: {
        page: 'current',
        pageSize: 'size',
        total: 'total'
      },
      checkedData: []
    }
  },
  computed: {
    dialogShow() {
      return this.visible
    },
    pageTitle() {
      return '添加终端'
    }
  },
  mounted() {
    this.getDataList()
  },
  methods: {
    // 获取列表数据
    getDataList() {
      this.tableData = []
      this.tableLoading = true
      const params = {
        pageSize: this.pageData.size,
        page: this.pageData.current,
        notAtIds: this.defaultChecked,
        isclient: true,
        ...this.searchForm
      }
      this.$api.supplierAssess
        .queryDutyAttendanceByPage(params)
        .then((res) => {
          this.tableLoading = false
          if (res.code === '200') {
            this.tableData = res.data.records
            this.pageData.total = res.data.total
          } else {
            this.tableData = []
            this.pageData.total = 0
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getDataList()
    },
    handleTableEvent(type, row) {
      this.defaultCheckedShiftData = {
        id: row.id
      }
      this.selectShiftDetailDialogShow = true
    },
    attendanceSelectChange(rows) {
      this.checkedData = rows
    },
    // 表单搜索按钮点击
    searchEvent() {
      this.getDataList()
    },
    // 表单重置按钮点击
    resetEvent() {
      this.pageData.current = 1
      this.searchForm = {
        nameOrChargePersonName: ''
      }
      this.searchEvent()
    },
    handlePrePlanEvent(currentRow) {
      this.assetsCheckData = currentRow ?? this.assetsCheckData
    },
    closeDialog() {
      this.$emit('closeDialog')
    },
    submit() {
      if (!this.checkedData.length) {
        this.$message({
          message: '请添加至少一个终端！',
          type: 'warning'
        })
      } else {
        this.$emit('submitDialog', this.checkedData)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.model-dialog {
  width: 40% !important;

  .sapce_content {
    background: #fff;
    padding: 10px;
    border-radius: 4px;
    height: 400px;
    width: 100%;
    overflow: hidden;

    .search-from {
      height: 40px;

      &>div {
        margin-right: 10px;
      }
    }
  }
}

::v-deep .el-checkbox__input {
  display: inline-block !important;
}
</style>
