<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="content_box">
        <el-form ref="formInline" style="margin: 10px 24px 0;" class="dialog-form" :model="formInline" :inline="true" :rules="rules" label-position="right" label-width="100px">
          <el-form-item label="文档名称：" prop="repositoryName">
            <el-input v-model="formInline.repositoryName" placeholder="请输入文档名称" maxlength="20" show-word-limit style="margin-top: 5px;"></el-input>
          </el-form-item>
          <br />
          <el-form-item label="文档说明：" class="remarks" prop="repositoryExplain">
            <el-input
              v-model="formInline.repositoryExplain"
              type="textarea"
              :rows="5"
              placeholder="请输入文档说明"
              maxlength="200"
              show-word-limit
              class="ipt-textarea"
              style="margin-top: 10px;"
            ></el-input>
          </el-form-item>
          <template>
            <div class="upload-file">
              <el-form-item class="is-required" label="上传附件：" prop="fileList">
                <el-upload
                  ref="uploadFile"
                  :disabled="readonly"
                  action
                  class="mterial_file"
                  drag
                  :limit="1"
                  multiple
                  :http-request="httpRequest"
                  :beforeUpload="beforeAvatarUpload"
                  :file-list="fileList"
                  :on-exceed="handleExceed"
                  :on-remove="handleRemove"
                  accept=".jpg, .jpeg, .png, .JPG, .JPEG, .gif, .word, .WORD, .Excel, .pdf, .PDF, .doc, .docx, .DOCX, .xlsx, .xls, .zip, .zipx, .tar, .7z, .mp4, .mp3"
                >
                  <i class="el-icon-upload"></i>
                  <div class="el-upload__text">
                    将文件拖到此处，或
                    <em>点击上传</em>
                  </div>
                  <div slot="tip" class="el-upload__tip">可上传单个文件，小于50M，支持音频、视频、office、图片、压缩包等类型</div>
                </el-upload>
              </el-form-item>
            </div>
          </template>
        </el-form>
      </div>
    </div>
    <div slot="footer">
      <el-button type="primary" @click="complete">保存</el-button>
      <el-button type="primary" @click="$router.go(-1)">取消</el-button>
    </div>
  </PageContainer>
</template>

<script>
import axios from 'axios'
export default {
  name: 'newAttachment',
  data() {
    return {
      loading: false,
      readonly: false,
      fileList: [],
      formInline: {
        repositoryName: '', // 名称
        repositoryExplain: '' // 说明
      },
      rules: {
        repositoryName: [{ required: true, message: '请输入文档名称', trigger: 'blur' }],
        repositoryExplain: [{ required: true, message: '请输入计划说明', trigger: 'blur' }]
      }
    }
  },
  methods: {
    // 限制文件大小
    beforeAvatarUpload(file) {
      const isLt50M = file.size / 1024 / 1024 < 50
      if (!isLt50M) {
        this.$message.error('上传图片大小不能超过 50MB!')
        return false
      }
      if (file.name.indexOf(',') != -1) {
        this.$message.error('非法的文件名')
        return false
      }
    },
    /**
     * 删除文件
     */
    handleRemove(file, fileList) {
      this.fileList = []
    },
    /**
     * 文件上传成功
     */
    handlePreview(file) {},
    /**
     * 图片文件
     */
    httpRequest(item) {
      this.fileList.push(item.file)
    },
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`)
    },
    // 点击确定
    complete() {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          if (!this.fileList[0]) {
            this.$message.error('请选择您要上传的文件')
            return
          }
          let formData = new FormData()
          let cacheInfo = JSON.parse(sessionStorage.getItem('LOGINDATA'))
          formData.append('unitCode', cacheInfo.unitCode)
          formData.append('hospitalCode', cacheInfo.hospitalCode)
          formData.append('userId', cacheInfo.userId)
          formData.append('userName', cacheInfo.name)
          formData.append('repositoryFile', this.fileList[0])
          formData.append('repositoryName', this.formInline.repositoryName)
          formData.append('repositoryExplain', this.formInline.repositoryExplain)
          this.loading = true
          axios
            .post(__PATH.VUE_AQ_URL + 'repository/upload', formData, {
              headers: {
                'Content-Type': 'multipart/form-data',
                Authorization: 'Bearer ' + this.$store.state.user.token
              }
            })
            .then((res) => {
              if (res.data.code == 200) {
                this.$message.success(res.data.message.replace(new RegExp('<br/>', 'g'), ' '))
                // this.$store.commit('keepAliveChange', false)
                this.$router.go(-1)
              } else {
                this.$message.error(res.data.message)
              }
              // if(res.data.code == 400){
              //   this.$message.success(res.data.message.replace(new RegExp("<br/>", "g"), " "));
              // }
            })
            .catch(() => {
            })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
  padding: 10px;
  overflow-y: auto;
}

.content_box {
  height: calc(100% - 30px);
  margin-top: 10px;
  padding: 20px 25px 25px;
  background: #fff;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.remarks {
  display: flex;
  margin-top: 20px;
  position: relative;

  .ipt-textarea {
    width: 300px;
    display: inline-block;
    margin-top: 20px;
  }
}

::v-deep .mterial_file .el-upload .el-upload-dragger {
  width: 300px;
  height: 148px;
}

.upload-file {
  width: 627px;
  display: flex;
  align-items: center;
}

.upload-file {
  align-items: flex-start;
  padding-top: 52px;
  position: relative;
  padding-bottom: 70px;
}

::v-deep .mterial_file > .el-upload-list {
  margin-top: 40px !important;
}
</style>
