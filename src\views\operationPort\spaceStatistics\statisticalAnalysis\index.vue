<template>
   <PageContainer>
      <div slot="content" class="analysis_content">
        <el-tabs v-model="choiceTypeTab" class="analysis_tabs" @tab-click="handleClick()" >
            <el-tab-pane
            v-for="item in typeTabs"
            :key="item.type"
            style="height: calc(100%);"
            :label="item.name"
            :name="item.type"
            >
              <div class="search_tem">
                <el-select v-if="choiceTypeTab==1" v-model="searchModel" placeholder="请选择" filterable>
                  <el-option
                    v-for="item in buildingList"
                    :key="item.id"
                    :label="item.ssmName"
                    :value="item.id">
                  </el-option>
                </el-select>
                <el-select v-if="choiceTypeTab==2" v-model="searchModel" placeholder="请选择" filterable>
                  <el-option
                    v-for="item in departmentList"
                    :key="item.id"
                    :label="item.deptName"
                    :value="item.id">
                  </el-option>
                </el-select>
                <el-select v-if="choiceTypeTab==3" v-model="searchModel" placeholder="请选择" filterable>
                  <el-option
                    v-for="item in spaceTypeList"
                    :key="item.id"
                    :label="item.dictName"
                    :value="item.id">
                  </el-option>
                </el-select>
                <el-select v-if="choiceTypeTab==4" v-model="searchModel" placeholder="请选择" filterable>
                  <el-option
                    v-for="item in stateList"
                    :key="item.type"
                    :label="item.name"
                    :value="item.type">
                  </el-option>
                </el-select>
                <el-button type="primary" plain class="re_btn" @click="reset">重置</el-button>
                <el-button type="primary" @click="submit">查询</el-button>
              </div>
            </el-tab-pane>
        </el-tabs>
        
        <!-- 按建筑 -->
        <el-row v-if="choiceTypeTab == 1" :gutter="24"  style="height: 40%; margin-top: 16px;">
          <el-col :xs="24" :md="24" :lg="12" style="height: 100%;">
            <ContentCard v-loading="loading.buildingAreaAnalysis" title="建筑面积分析" style="height: 100%;">
              <div slot="content" style="width: 100%; height: 100%;">
                <echarts ref="buildingAreaAnalysis" domId="buildingAreaAnalysis" width="100%" height="100%" />
              </div>
            </ContentCard>
          </el-col>
          <el-col :xs="24" :md="24" :lg="12" style="height: 100%;">
            <ContentCard v-loading="loading.analysisOfBuildingSpaceQuantity" title="建筑空间数量分析" style="height: 100%;">
              <div slot="content" style="width: 100%; height: 100%;">
                <echarts ref="analysisOfBuildingSpaceQuantity" domId="analysisOfBuildingSpaceQuantity" width="100%" height="100%" />
              </div>
            </ContentCard>
          </el-col>
        </el-row>
        <!-- 按部门 -->
        <el-row v-if="choiceTypeTab == 2" :gutter="24"  style="height: 40%; margin-top: 16px;">
          <el-col :xs="24" :md="24" :lg="12" style="height: 100%;">
            <ContentCard v-loading="loading.analysisOfDepartmentalBuildingArea" title="部门建筑面积分析" style="height: 100%;">
              <div slot="content" style="width: 100%; height: 100%;">
                <echarts ref="analysisOfDepartmentalBuildingArea" domId="analysisOfDepartmentalBuildingArea" width="100%" height="100%" />
              </div>
            </ContentCard>
          </el-col>
          <el-col :xs="24" :md="24" :lg="12" style="height: 100%;">
            <ContentCard v-loading="loading.departmentQuantityAnalysis" title="部门数量分析" style="height: 100%;">
              <div slot="content" style="width: 100%; height: 100%;">
                <echarts ref="departmentQuantityAnalysis" domId="departmentQuantityAnalysis" width="100%" height="100%" />
              </div>
            </ContentCard>
          </el-col>
        </el-row>
        <!-- 按功能用途 -->
        <el-row v-if="choiceTypeTab == 3" :gutter="24"  style="height: 40%; margin-top: 16px;">
          <el-col :xs="24" :md="24" :lg="24" style="height: 100%;">
            <ContentCard v-loading="loading.analysisOfSpatialFunctionalPurposes" title="空间功能用途分析" style="height: 100%;">
              <div slot="content" style="width: 100%; height: 100%;">
                <echarts ref="analysisOfSpatialFunctionalPurposes" domId="analysisOfSpatialFunctionalPurposes" width="100%" height="100%" />
              </div>
            </ContentCard>
          </el-col>
        </el-row>
        <!-- 按状态 -->
        <el-row v-if="choiceTypeTab == 4" :gutter="24"  style="height: 40%; margin-top: 16px;">
          <el-col :xs="24" :md="24" :lg="24" style="height: 100%;">
            <ContentCard v-loading="loading.spatialStateAnalysis" title="空间状态分析" style="height: 100%;">
              <div slot="content" style="width: 100%; height: 100%;">
                <echarts ref="spatialStateAnalysis" domId="spatialStateAnalysis" width="100%" height="100%" />
              </div>
            </ContentCard>
          </el-col>
        </el-row>
        
        <el-row :gutter="24"  style="height: 45%; margin-top: 16px; background: white;">
          <el-col :xs="24" :md="24" :lg="24" style="height: 100%;">
            <div v-if="choiceTypeTab == 1" style="height: 85%;">
              <el-table ref="tableRef" :data="tableData" height="100%" @sort-change="sortchange">
                <el-table-column type="index" label="序号" width="50" align="center">
                  <template slot-scope="scope">
                    <span>{{ (pagination.current - 1) * pagination.size + scope.$index + 1 }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="建筑名称" prop="buildingName" show-overflow-tooltip sortable/>
                <el-table-column label="楼层" prop="dataName" sortable />
                <el-table-column label="空间数量" prop="roomAllCount" sortable />
                <el-table-column label="空间占比" prop="proportion" sortable>
                  <template slot-scope="scope">{{ scope.row.proportion }}%</template>
                </el-table-column>
                <el-table-column label="建筑面积（㎡）" prop="totalArea" sortable/>
                <el-table-column label="使用面积（㎡）" prop="totalUseArea" sortable/>
              </el-table>
            </div>
            <div v-if="choiceTypeTab == 2" style="height: 85%;">
              <el-table ref="tableRef" :data="tableData" height="100%" @sort-change="sortchange">
                <el-table-column type="index" label="序号" width="50" align="center">
                  <template slot-scope="scope">
                    <span>{{ (pagination.current - 1) * pagination.size + scope.$index + 1 }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="部门名称" prop="dataName" show-overflow-tooltip sortable="custom"/>
                <el-table-column label="空间数量" prop="roomAllCount" sortable />
                <el-table-column label="空间占比" prop="proportion" sortable>
                  <template slot-scope="scope">{{ scope.row.proportion }}%</template>
                </el-table-column>
                <el-table-column label="建筑面积（㎡）" prop="totalArea" sortable/>
                <el-table-column label="使用面积（㎡）" prop="totalUseArea" sortable/>
              </el-table>
            </div>
            <div v-if="choiceTypeTab == 3" style="height: 85%;">
              <el-table ref="tableRef" :data="tableData" height="100%" @sort-change="sortchange">
                <el-table-column type="index" label="序号" width="50" align="center">
                  <template slot-scope="scope">
                    <span>{{ (pagination.current - 1) * pagination.size + scope.$index + 1 }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="功能用途" prop="dataName" show-overflow-tooltip sortable="custom"/>
                <el-table-column label="空间数量" prop="roomAllCount" sortable />
                <!-- <el-table-column label="空间占比" prop="proportion" sortable/> -->
                <el-table-column label="建筑面积（㎡）" prop="totalArea" sortable/>
                <el-table-column label="使用面积（㎡）" prop="totalUseArea" sortable/>
              </el-table>
            </div>
            <div v-if="choiceTypeTab == 4" style="height: 100%;">
              <el-table ref="tableRef" :data="tableData" height="100%">
                <el-table-column type="index" label="序号" width="50" align="center">
                  <template slot-scope="scope">
                    <span>{{ scope.$index + 1 }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="空间状态" prop="spatialState" show-overflow-tooltip/>
                <el-table-column label="空间数量" prop="roomAllCount" />
                <!-- <el-table-column label="空间占比" prop="proportion"/> -->
                <el-table-column label="建筑面积（㎡）" prop="totalArea"/>
                <el-table-column label="使用面积（㎡）" prop="totalUseArea"/>
              </el-table>
            </div>
            <div v-if="choiceTypeTab != 4" style="padding: 10px 10px 0;">
              <el-pagination
                :current-page="pagination.current"
                :page-sizes="[15, 30, 50, 100]"
                :page-size="pagination.size"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              ></el-pagination>
            </div>
          </el-col>
        </el-row>
        
      </div>
   </PageContainer>
</template>

<script>
import mixin from './mixin/mixin'
export default {
  mixins: [mixin],
  data() {
    return {
      buildingList: [], // 建筑搜索
      departmentList: [], // 部门搜索
      spaceTypeList: [], // 功能用途搜索
      stateList: [ // 状态搜索
        {
          name: '使用中',
          type: 2
        },
        {
          name: '闲置',
          type: 1
        }
      ],
      param1: '',
      param2: '',
      tableData: [],
      pagination: {
        current: 1,
        size: 15
      }, // 分页数据
      total: 0, // 数据总条数
      loading: {
        buildingAreaAnalysis: false,
        analysisOfBuildingSpaceQuantity: false,
        tabelLoading: false
      },
      choiceTypeTab: '1',
      searchArr: [],
      searchModel: null,
      typeTabs: [
        {
          name: '按建筑',
          type: '1',
          queryType: 'space'
        },
        {
          name: '按部门',
          type: '2',
          queryType: 'dept'
        },
        {
          name: '按功能用途',
          type: '3',
          queryType: 'function'
        },
        {
          name: '按状态',
          type: '4'
        }
      ],
      spaceData: {
        idleCount: 0,
        publicArea: 0,
        totalArea: 0,
        totalCount: 0,
        useArea: 0
      },
      hospitalCode: this.$store.state.user.userInfo.user.hospitalCode
    }
  },
  mounted() {
    this.getSpaceTree()
    this.valveTypeListFn()
    this.getDepartmentList()
    this.handleClick()
  },
  methods: {
    // 部门列表
    getDepartmentList() {
      this.$api.getSelectedDept({}).then((res) => {
        if (res.code == '200') {
          this.departmentList = res.data
        }
      })
    },
    // 空间tree
    getSpaceTree() {
      this.$api.getStructureTree({}).then((res) => {
        if (res.code == '200') {
          this.buildingList = res.data.filter(ele => ele.ssmType == 3)
        }
      })
    },
    getList() {
      if (this.choiceTypeTab == 4) return
      let obj = this.typeTabs.find(ele => ele.type == this.choiceTypeTab)
      const params = {
        modelCode: this.hospitalCode,
        queryType: obj.queryType,
        ascColumn: this.param1,
        descColumn: this.param2,
        current: this.pagination.current,
        size: this.pagination.size,
        haveModel: '0',
        ids: this.searchModel
      }
      this.$api.getRoomCountAndAreaPageList(params).then((res) => {
        if (res.code == 200) {
          res.data.records.forEach((item) => {
            item.proportion = Number(((item.roomAllCount / this.spaceData.totalCount) * 100).toFixed(2))
            if (params.queryType == 'dept') {
              if (item.dataName == null) {
                item.dataName = '未知'
              }
            }
          })
          this.tableData = res.data.records
          // console.log('tableData==========', this.tableData)
          this.total = res.data.total
        }
      })
    },
    oldGetRoomCountAndArea() {
      let obj = this.typeTabs.find(ele => ele.type == this.choiceTypeTab)
      const params = {
        modelCode: this.hospitalCode,
        haveModel: '0'
      }
      this.$api.getRoomCountAndArea(params).then((res) => {
        if (res.code === 200) {
          Object.assign(this.spaceData, res.data)
          // 计算公共区域面积 = 建筑面积 - 使用面积
          this.spaceData.publicArea = this.spaceData.publicArea ? this.spaceData.publicArea : this.spaceData.totalArea - this.spaceData.useArea
          this.getList()
        }
      })
    },
    getRoomCountAndArea() {
      let obj = this.typeTabs.find(ele => ele.type == this.choiceTypeTab)
      const params = {
        modelCode: this.hospitalCode,
        haveModel: '0'
      }
      this.$api.getRoomCountAndAreaByStatus(params).then((res) => {
        if (res.code === 200) {
          // 按状态的列表与图标
          if (this.choiceTypeTab == 4) {
            let data = res.data
            let tableData = [
              {
                spatialState: '闲置',
                type: 1,
                roomAllCount: data.idleCount,
                totalArea: data.idleTotalArea,
                totalUseArea: data.idleUseArea
              },
              {
                spatialState: '使用中',
                type: 2,
                roomAllCount: data.totalCount,
                totalArea: data.totalArea,
                totalUseArea: data.useArea
              }
            ]
            this.tableData = this.searchModel ? tableData.filter(ele => ele.type == this.searchModel) : tableData
            let echarsData = [
              {
                name: '使用中',
                value: data.totalCount
              },
              {
                name: '闲置',
                value: data.idleCount
              }
            ]
            this.$refs.spatialStateAnalysis.init(this.getSpaceStatus(echarsData, '个'))
            this.$refs.spatialStateAnalysis.chartResize()
          }
        }
      })
    },
    handleClick() {
      this.pagination.current = 1
      this.searchModel = null
      this.param1 = ''
      this.param2 = ''
      if (this.$refs.tableRef) {
        this.$refs.tableRef.clearSort()
      }
      
      this.tableData = []
      this.$nextTick(() => {
        this.oldGetRoomCountAndArea()
        // 获取图表数据
        if (this.choiceTypeTab == 2 || this.choiceTypeTab == 3) {
          this.getRoomCountList()
        } else if (this.choiceTypeTab == 1) {   // 按建筑
          this.byBuildingEchars()
        } else if (this.choiceTypeTab == 4) {
          this.getRoomCountAndArea()
        }
      })
    },
    byBuildingEchars() {
      let obj = this.typeTabs.find(ele => ele.type == this.choiceTypeTab)
      const params = {
        modelCode: this.hospitalCode,
        queryType: obj.queryType,
        current: 1,
        size: 99999,
        haveModel: '0'
      }
      this.$api.getRoomCountAndAreaPageList(params).then((res) => {
        if (res.code == 200) {
          // 建筑柱状图
          const data = res.data.records.reduce((acc, obj) => {
            const existingObj = acc.find(item => item.name === obj.buildingName)
            if (existingObj) {
              existingObj.value += obj.roomAllCount
            } else if (obj.buildingName) {
              acc.push({ name: obj.buildingName, value: obj.roomAllCount })
            }
            return acc
          }, [])
          // let data = res.data.records.map(ele => {
          //   return {
          //     name: ele.buildingName,
          //     value: ele.roomAllCount
          //   }
          // })
          let newData = data.sort((a, b) => b.value - a.value)
          // 建筑饼图
          const areaData = res.data.records.reduce((acc, obj) => {
            const existingObj = acc.find(item => item.name === obj.buildingName)
            if (existingObj) {
              existingObj.value += obj.totalArea
            } else if (obj.buildingName) {
              acc.push({ name: obj.buildingName, value: obj.totalArea })
            }
            return acc
          }, [])
          // let areaData = res.data.records.map(ele => {
          //   return {
          //     name: ele.buildingName,
          //     value: ele.totalArea
          //   }
          // })
          this.$refs.analysisOfBuildingSpaceQuantity.init(this.getBarChartStatus(newData, '个'))
          this.$refs.buildingAreaAnalysis.init(this.getSpaceStatus(areaData, '㎡'))
          this.$refs.analysisOfBuildingSpaceQuantity.chartResize()
          this.$refs.buildingAreaAnalysis.chartResize()
        }
      })
    },
    getRoomCountList() {
      let obj = this.typeTabs.find(ele => ele.type == this.choiceTypeTab)
      const params = {
        modelCode: this.hospitalCode,
        queryType: obj.queryType,
        descColumn: 'roomAllCount',
        haveModel: '0'
      }
      this.$api.getRoomCountListIgnoreVersion(params).then(res => {
        let data = res.data.filter(ele => {
          if (ele.dataName) {
            ele['name'] = ele.dataName
            ele['value'] = ele.roomAllCount
            return true
          }
          return false
        })
        let newData = data.sort((a, b) => b.value - a.value)
        let areaData = res.data.filter(ele => {
          if (ele.dataName) {
            ele['name'] = ele.dataName
            ele['value'] = ele.totalArea
            return true
          }
          return false
        })
        console.log('data===============', areaData)
        this.$nextTick(() => {
          if (this.choiceTypeTab == 3) {
            this.$refs.analysisOfSpatialFunctionalPurposes.init(this.getBarChartStatus(newData, '个'))
            this.$refs.analysisOfSpatialFunctionalPurposes.chartResize()
          } else if (this.choiceTypeTab == 2) {
            this.$refs.departmentQuantityAnalysis.init(this.getBarChartStatus(newData, '个'))
            this.$refs.analysisOfDepartmentalBuildingArea.init(this.getSpaceStatus(areaData, '㎡'))
            this.$refs.departmentQuantityAnalysis.chartResize()
            this.$refs.analysisOfDepartmentalBuildingArea.chartResize()
          }
        })
      })
    },
    submit() {
      this.pagination.current = 1
      if (this.choiceTypeTab == 4) {
        this.getRoomCountAndArea()
      } else {
        this.getList()
      }
    },
    reset() {
      this.pagination.current = 1
      this.searchModel = null
      if (this.choiceTypeTab == 4) {
        this.getRoomCountAndArea()
      } else {
        this.getList()
      }
    },
    // 获取空间功能类型字典列表
    valveTypeListFn() {
      this.$api
        .valveTypeList({
          typeValue: 'SP'
        })
        .then((res) => {
          if (res.code == 200) {
            this.spaceTypeList = res.data
          }
        })
    },
    sortchange(column, prop, order) {
      if (column.prop == 'dataName') {
        if (column.order == 'ascending') {
          this.param1 = column.prop
          this.param2 = ''
        } else {
          this.param1 = ''
          this.param2 = column.prop
        }
      }
      this.getList()
    },
    handleSizeChange(val) {
      this.pagination.size = val
      this.getList(this.choiceTypeTab)
    },
    handleCurrentChange(val) {
      this.pagination.current = val
      this.getList(this.choiceTypeTab)
    }
  }
}
</script>

<style scoped lang="scss" >
.analysis_content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .analysis_tabs {
    width: 100%;
    background: #fff;
    padding: 0 16px 16px;

    .search_tem {
      margin-top: 16px;
      display: flex;

      .re_btn {
        margin-left: 10px;
      }
    }
  }
}
</style>