<!--
 * @Description:
-->
<template>
  <!-- 医用气体报警中心 -->
  <elec-alarm-center :projectCode="projectCode" :requestHttp="requestHttp" />
</template>

<script>
import elecAlarmCenter from '../commonPage/elecAlarmCenter/index'
import { monitorTypeList } from '@/util/dict.js'
export default {
  name: 'alarmCenter',
  components: {elecAlarmCenter},
  beforeRouteEnter(to, from, next) {
    let names = []
    to.matched.map((v, i) => {
      if (i > 0) {
        v.components.default.name && names.push(v.components.default.name)
      }
    })
    next((vm) => {
      if (from.query.activeName) {
        vm.activeName = from.query.activeName
      }
      vm.$store.commit('keepAlive/add', names)
    })
  },
  async beforeRouteLeave(to, from, next) {
    // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
    if (!['policeDetails'].includes(to.name)) {
      // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      requestHttp: __PATH.VUE_IEMC_API,
      projectCode: monitorTypeList.find(item => item.projectName == '医用气体').projectCode
    }
  },
  computed: {

  },
  created() {

  },
  methods: {

  }
}

</script>

<style lang="scss" scoped>

</style>
