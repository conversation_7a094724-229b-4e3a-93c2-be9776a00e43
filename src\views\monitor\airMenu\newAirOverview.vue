<!-- 空调末端总览 -->
<template>
  <PageContainer>
    <div slot="content" class="staging-content">
      <div class="header_airConditioning">
        <p>运行总览</p>
        <div class="today_s_operation_mode">
          <span>今日运行模式</span>
          <div class="pattern_box active_pattern" @click="() => showTodayModeDetails = true">
            <img style="width: 30px; height: 30px;" :src="todayPattern.patternActiveSrc" alt="" />
            <span>{{ todayPattern.patternName }}</span>
          </div>
        </div>
      </div>
      <!-- <el-tabs v-model="tabsActive" class="content-head" @tab-click="tabClick">
        <el-tab-pane v-for="item in firstLevelMenuList" :key="item.code" :label="item.name" :name="item.code"></el-tab-pane>
      </el-tabs> -->
      <div class="content-main">
        <dashboard v-if="dashExampleShow" id="dashExample" :style="{ width: editExampleShow ? 'calc(100% - 220px)' : '100%' }">
          <dash-layout v-bind="dlayout" :debug="false">
            <template v-for="(item, index) in dlayout.items">
              <dash-item v-show="item.status == 1" v-bind.sync="dlayout.items[index]" :key="item.componentName + index" @moveEnd="moveEnd" @resizeEnd="resizeEnd">
                <component :is="item.componentName" :ref="item.componentName" :projectCode="projectCode" :item="item" @all-more-Oper="allMoreOper"></component>
              </dash-item>
            </template>
          </dash-layout>
        </dashboard>
        <div id="editExample" :style="{ width: editExampleShow ? '215px' : '0' }">
          <div class="round-box">
            <div class="editExample-title">
              <div>添加模块</div>
              <p>请点击卡片修改显示模块</p>
            </div>
            <div class="editExample-content">
              <div
                v-for="(item, index) in dlayout.items"
                :key="index"
                :class="{ 'editExample-content-item': true, 'active-editExample-item': activeExample.includes(item.id) }"
                @click="addStaging(item)"
              >
                <span>{{ item.componentTitle }}</span>
              </div>
            </div>
            <div class="editExample-footer">
              <el-button type="primary" plain :disabled="hasStatusFalg" @click="cancelStaging">取消</el-button>
              <el-button type="primary" @click="saveStaging">保存</el-button>
            </div>
          </div>
        </div>
      </div>
      <todayModeDetails v-if="showTodayModeDetails" :todayPattern="todayPattern" :visible.sync="showTodayModeDetails" />
      <alarmStatisticsDialog v-if="alarmStatisticsDialog" :projectCode="projectCode" :visible.sync="alarmStatisticsDialog" />
      <monitorStatisticsDialog v-if="monitorStatisticsDialog" :type="detailsType" :projectCode="projectCode" :entityList="entityList" :visible.sync="monitorStatisticsDialog" />

    </div>
  </PageContainer>
</template>

<script>
import sunshine_weather_active from '@/assets/images/lightingMonitoring/sunshine_weather_active.png'
import { Dashboard, DashLayout, DashItem } from 'vue-responsive-dash'
import { monitorTypeList } from '@/util/dict.js'
import monitorStatisticsDialog from './components/monitorStatisticsDialog'
import Dict from '@/views/monitor/lightingMonitoring/components/dict.js'
export default {
  name: 'powerOverview',
  components: {
    Dashboard,
    DashLayout,
    DashItem,
    monitorStatisticsDialog,
    todayModeDetails: () => import('@/views/monitor/lightingMonitoring/components/todayModeDetailsDialog'),
    operationMonitor: () => import('./overviewComponents/operationMonitor'),
    topTen: () => import('./overviewComponents/topTen'),
    alarmAnalysis: () => import('./overviewComponents/alarmAnalysis'),
    alarmStatisticsDialog: () => import('../components/alarmStatisticsDialog/index.vue')
  },
  data() {
    return {
      showTodayModeDetails: false, // 今日模式详情
      detailsType: '',
      patternTypeIconList: Dict.patternTypeIconList,
      todayPattern: {
        patternName: '晴天模式',
        patternActiveSrc: sunshine_weather_active
      },
      interval: null,
      tabsActive: '',
      // x=> 4 8 12 小中大
      dlayout: {
        breakpoint: 'xs',
        numberOfCols: 48,
        items: [
          {id: 121, componentName: 'operationMonitor', componentTitle: '设备统计', x: 0, y: 0, width: 16, height: 11, dragAllowFrom: '.drag_class', resizable: false, draggable: false, status: 1 },
          {id: 122, componentName: 'topTen', componentTitle: '运行时长Top10', x: 16, y: 0, width: 16, height: 11, dragAllowFrom: '.drag_class', resizable: false, draggable: false, status: 1, chartType: '1', barColor: '#3562DB' },
          {id: 107, componentName: 'topTen', componentTitle: '故障次数Top10', x: 32, y: 0, width: 16, height: 11, dragAllowFrom: '.drag_class', resizable: false, draggable: false, status: 1, chartType: '4', barColor: '#00BC6D' },
          {id: 123, componentName: 'alarmAnalysis', componentTitle: '报警分析', x: 0, y: 11, width: 32, height: 11, dragAllowFrom: '.drag_class', resizable: false, draggable: false, status: 1 },
          {id: 108, componentName: 'topTen', componentTitle: '离线次数Top10', x: 32, y: 11, width: 16, height: 11, dragAllowFrom: '.drag_class', resizable: false, draggable: false, status: 1, chartType: '5', barColor: '#FF9435' }
        ]
      },
      // stagingConfig: Object.freeze(stagingConfig),
      reservedItems: [],
      dashExampleShow: true, // 工作台显示隐藏
      editExampleShow: false, // 编辑工作台显示隐藏
      activeExample: [], // 当前激活的工作台
      firstLevelMenuList: [],
      projectCode: monitorTypeList.find((item) => item.projectName == '空调监测').projectCode,
      alarmStatisticsDialog: false,
      monitorStatisticsDialog: false, // 检测项统计弹窗
      entityList: [] // 设备列表
    }
  },
  computed: {
    hasStatusFalg() {
      return this.dlayout.items.filter((e) => e.status === 0).length === this.dlayout.items.length
    }
  },
  created() {
    this.getWorktopManageList()
    this.getEntityMenuList()
  },
  mounted () {
    this.getMonitorStatusList()
  },
  methods: {
    // 获取设备菜单
    getEntityMenuList() {
      this.$api.GetEntityMenuList({ projectId: this.projectCode }).then((res) => {
        if (res.code == 200) {
          this.entityList = res.data
        }
      })
    },
    // 获取总览数据
    getMonitorStatusList () {
      this.$api.getCountRunToDayState({projectCode: this.projectCode}).then((res) => {
        if (res.code == '200') {
          let weatherPattern = res.data[2].weatherPattern
          // 今日运行模式
          this.todayPattern = {
            patternActiveSrc: this.patternTypeIconList.find(
              (item) => item.patternCode == weatherPattern.patternType
            )?.patternActiveSrc,
            patternName: weatherPattern.patternName,
            patternType: weatherPattern.patternType,
            id: weatherPattern.id
          }
        }
      })
    },
    // // 鼠标移入
    // viewMouseover: debounce(function() {
    //   console.log('鼠标移入')
    //   clearTimeout(this.interval)
    // }, 1000),
    // // 鼠标移出
    // viewMouseleave: debounce(function() {
    //   console.log('鼠标移出')
    //   if (this.editExampleShow || this.alarmStatisticsDialog || this.transformerMonitorDialog || this.envirMonitorDialog) return
    //   let second = 5
    //   let index = -1
    //   clearTimeout(this.interval)
    //   this.interval = setInterval(() => {
    //     second--
    //     if (second < 1) {
    //       index += 1
    //       this.tabsActive = this.firstLevelMenuList[index].code
    //       index = index == (this.firstLevelMenuList.length - 1) ? -1 : index
    //       second = 5
    //     }
    //   }, 1000)
    // }, 1000),
    // 获取一级菜单
    // getParentMenuList() {
    //   this.$api.GetParentMenuList({projectId: this.projectCode}).then((res) => {
    //     if (res.code == 200) {
    //       this.firstLevelMenuList = res.data
    //       // this.tabsActive = res.data[0].code
    //       this.tabsActive = '13d4babbce69466395356c6ff5c40ea7'
    //       this.getWorktopManageList()
    //       // this.viewMouseleave()
    //     }
    //   })
    // },
    // tab切换
    tabClick(tab) {

    },
    // 获取工作台管理列表
    getWorktopManageList() {
      this.editExampleShow = false
      this.$store.commit('settings/dragSidebarCollapse', false)
      this.$api.getWorktopManageList({ userId: this.$store.state.user.userInfo.user.staffId, menuType: 12 }).then((res) => {
        if (res.code == 200) {
          const data = res.data
          if (data.length) {
            const items = []
            data.forEach((item) => {
              if (item.id === '107') {
                item.chartType = '4'
              } else if (item.id === '108') {
                item.chartType = '5'
              } else if (item.id === '122') {
                item.chartType = '1'
              }
              items.push({
                id: item.id,
                componentName: item.componentName,
                componentTitle: item.componentTitle,
                x: item.x,
                y: item.y,
                width: item.width,
                height: item.height,
                dragAllowFrom: '.drag_class',
                resizable: false,
                draggable: false,
                path: item.path,
                status: item.status,
                chartType: item.chartType || ''
              })
            })
            this.dashExampleShow = false
            this.$nextTick(() => {
              this.dashExampleShow = true
              this.dlayout.items = items
              const hasStatusLength = items.filter((e) => e.status === 0).length
              hasStatusLength == items.length ? this.allMoreOper('edit') : ''
            })
          }
        }
      })
    },
    // 拖拽结束事件
    moveEnd(item) {
      console.log('moveEnd', this.dlayout)
    },
    // 缩放结束事件
    resizeEnd(item) {
      const resizeList = ['transformerMonitor']
      if (resizeList.includes(item.id)) {
        this.$refs[item.id][0].echartsResize()
      }
    },
    // 添加/减少模块
    addStaging (item) {
      if (this.activeExample.includes(item.id)) {
        this.activeExample.splice(this.activeExample.indexOf(item.id), 1)
        this.dlayout.items.map((e) => {
          if (e.id === item.id) {
            e.status = 0
          }
        })
      } else {
        this.activeExample.push(item.id)
        this.dlayout.items.map((e) => {
          if (e.id === item.id) {
            e.status = 1
          }
        })
      }
    },
    // 取消编辑工作台模块
    cancelStaging() {
      this.editExampleShow = false
      this.dlayout.items = this.reservedItems
    },
    // 保存工作台模块
    saveStaging() {
      const items = this.dlayout.items
      const params = []
      items.forEach((item) => {
        params.push({
          id: item.id,
          componentName: item.componentName,
          componentTitle: item.componentTitle,
          path: item.path,
          status: item.status,
          x: item.x,
          y: item.y,
          width: item.width,
          height: item.height,
          chartType: item.chartType || ''
        })
      })
      this.$api.saveWorktopManage({ jsonList: JSON.stringify(params), userId: this.$store.state.user.userInfo.user.staffId, menuType: 12 }).then((res) => {
        if (res.code == 200) {
          this.$message.success('保存成功')
          this.getWorktopManageList()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 更多操作事件
    allMoreOper(type, component, chartType) {
      if (type === 'more') {
        if (component == 'alarmAnalysis') { // 报警分析
          this.alarmStatisticsDialog = true
        } else if (component == 'topTen') {
          this.detailsType = chartType
          this.monitorStatisticsDialog = true
        }
      } else if (type === 'edit') {
        this.reservedItems = JSON.parse(JSON.stringify(this.dlayout.items))
        this.dlayout.items.map((e) => {
          e.resizable = true
          e.draggable = true
        })
        this.activeExample = this.dlayout.items.filter((e) => e.status === 1).map((e) => e.id)
        this.editExampleShow = true
        this.$store.commit('settings/toggleSidebarCollapse', true)
        this.$store.commit('settings/dragSidebarCollapse', true)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  margin: 7px;
  height: 100%;
  width: calc(100% - 14px);
}

.staging-content {
  height: 100%;
  width: 100%;
  .header_airConditioning{
    // padding: 16px;
    // margin: 5px 4px 0px 4px;
    width: calc(100% - 20px);
    margin: 5px auto;
    box-sizing: border-box;
    background: #FFFFFF;
    border-radius: 10px;
    height: 48px;
    line-height: 48px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 15px;
    p{
      margin: 0;
      font-size: 18px;
      font-family: PingFang SC-Semibold, PingFang SC;
      font-weight: 600;
      color: #333333;
      line-height: 21px;
    }
    .today_s_operation_mode{
      display: flex;
      align-items: center;
      span{
        font-size: 14px;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        color: #333333;
        line-height: 14px;
        margin-right: 16px;
      }
      .pattern_box {
        box-sizing: border-box;
        height: 38px;
        line-height: 38px;
        padding: 0 30px;
        border-radius: 24px;
        margin: auto 15px;
        cursor: pointer;

        img {
          vertical-align: middle;
          margin-right: 10px;
        }

        span {
          font-size: 14px;
          font-family: "HarmonyOS_Sans_SC";
          color: #333;
        }
      }
      .active_pattern {
        background: #fffae8;
        border: 1px solid #ffe8ab;
      }
    }
  }
  :deep(.content-head) {
    margin: 10px 10px 0;
    background: #fff;
    border-radius: 4px;

    .el-tabs__header .el-tabs__nav-wrap::after {
      display: none;
    }

    .el-tabs__active-bar {
      bottom: 1px;
    }

    .el-tabs__item {
      padding: 0 20px !important;
    }

    .el-tabs__content {
      display: none;
    }
  }

  .content-main {
    width: 100%;
    height: calc(100% - 50px);
    display: flex;
    justify-content: space-between;
  }

  #dashExample {
    height: 100%;
    // background: #f5f7f9;
    overflow-y: auto;
    overflow-x: hidden;

    :deep(.placeholder) {
      background: #e2e6eb !important;
      border-radius: 10px;
      opacity: 1;
    }
  }

  #editExample {
    height: 100%;
    // background-color: #fff;
    box-shadow: 10px 0 10px -10px #c7c7c7;
    box-shadow: 10px 0 10px -10px #c7c7c7;
    padding: 16px 0;
    display: flex;
    flex-direction: column;

    .round-box {
      width: 100%;
      height: 100%;
      background-color: #08305d0a;
      border-radius: 10px;
      padding: 10px 0;
      display: flex;
      flex-direction: column;
    }

    .editExample-title {
      > div {
        font-size: 16px;
        font-family: PingFangSC-Regular-Blod;
        color: #121f3e;
        margin: 0 0 12px 15px;
        height: 22px;
        line-height: 22px;
      }

      > p {
        height: 18px;
        font-size: 12px;
        line-height: 18px;
        margin: 0 0 12px 15px;
        color: #999;
      }
    }

    .editExample-content {
      flex: 1;
      // height: calc(100% - 50px);
      overflow-y: auto;
      padding: 10px 20px;

      .editExample-content-item {
        cursor: pointer;
        width: 100%;
        box-sizing: border-box;
        height: 40px;
        line-height: 40px;
        margin-bottom: 16px;
        border-radius: 4px;
        padding: 0 10px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        background-color: #fff;
        text-align: center;
        color: #121f3e;
        font-size: 13px;
        font-family: "PingFang SC-Regular", "PingFang SC";

        &:hover {
          // color: #3562db;
          // background: rgba(53, 98, 219, 0.2);
          box-shadow: 0 4px 12px 0 rgb(17 19 23 / 10%);
        }
      }

      .active-editExample-item {
        background-color: #3562db !important;
        color: #fff !important;
        border-color: #3562db !important;
      }
    }

    .editExample-footer {
      text-align: right;
      padding: 5px 15px;
    }
  }
}
::-webkit-scrollbar-thumb:hover, ::-webkit-scrollbar-track:hover {
  background: transparent;
}
::-webkit-scrollbar-thumb {
  background-color: transparent;
}
</style>
