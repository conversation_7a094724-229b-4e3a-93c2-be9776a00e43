<!--
 * @Author: hedd
 * @Date: 2023-07-11 11:23:31
 * @LastEditTime: 2025-02-21 16:01:09
 * @FilePath: \ihcrs_pc\src\components\TablePage\components\tableColumn.vue
 * @Description:
-->
<template>
  <el-table-column
    v-bind="column"
    :min-width="column.minWidth"
    :reserve-selection="column.reserveSelection"
    :show-overflow-tooltip="column.showOverflowTooltip == false ? false : true"
    class="abc"
  >
    <template v-if="column.subMerge && column.subMerge.length">
      <TableColumn v-for="(sonColumn, sonIndex) in column.subMerge" :key="`${index}-${sonIndex}`" :column="sonColumn" :index="sonIndex" />
    </template>
    <template slot="header">
      <span :class="column.required ? 'required' : ''">{{ column.label }}</span>
    </template>
    <template slot-scope="scope">
      <TableRender v-if="column.render" :sc="scope" :row="scope.row" :render="column.render"></TableRender>
      <div v-else-if="column.formatter">
        {{ column.formatter(scope) }}
      </div>
      <template v-else-if="column.slot">
        <slot :name="column.slot" :row="scope.row" :scope-index="scope.$index"></slot>
      </template>
      <template v-else>
        {{ scope.row[column.prop] }}
      </template>
    </template>
  </el-table-column>
</template>
<script>
export default {
  components: {
    TableColumn: () => import('@/components/TablePage/components/tableColumn')
  },
  props: {
    index: {
      type: Number,
      default: 1
    },
    column: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  mounted() {}
}
</script>
