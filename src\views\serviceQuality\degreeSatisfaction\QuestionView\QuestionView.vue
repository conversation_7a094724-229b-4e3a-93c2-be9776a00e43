<template>
  <div class="main-container">
    <div class="whole">
      <div class="page-title">查看问卷</div>
      <el-row style="border-bottom: 1px solid #d8dee7;">
        <el-col :span="24">
          <HeaderButton :btnName="btnName" />
        </el-col>
      </el-row>
      <div class="question-main-container">
        <el-row :gutter="10">
          <el-col :xs="1" :sm="1" :md="2" :lg="4" :xl="4">
            <div class="grid-content"></div>
          </el-col>
          <el-col :xs="22" :sm="22" :md="20" :lg="16" :xl="16">
            <div class="question-title">{{ questionPreviewList.name }}</div>
            <div class="question-content">
              <div v-for="qSubject in questionPreviewList.questions" :key="qSubject.id" class="preview-container">
                <component :is="questionPreview[qSubject.type]" :previewOption="qSubject" :index="qSubject.indexCopy" :pathName="pathName" :diff="diff" />
              </div>
            </div>
          </el-col>
          <el-col :xs="1" :sm="1" :md="2" :lg="4" :xl="4">
            <div class="grid-content"></div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import HeaderButton from '../questionDesign/component/HeaderButton/HeaderButton.vue'
import PreviewFillBlank from '../questionDesign/component/QuestionPreview/PreviewFillBlank/PreviewFillBlank'
import PreviewRadio from '../questionDesign/component/QuestionPreview/PreviewRadio/PreviewRadio'
import PreviewCheckBox from '../questionDesign/component/QuestionPreview/PreviewCheckBox/PreviewCheckBox'
import PreviewSelect from '../questionDesign/component/QuestionPreview/PreviewSelect/PreviewSelect'
import utils from '../utils/utils'
export default {
  components: {
    HeaderButton,
    PreviewFillBlank,
    PreviewRadio,
    PreviewCheckBox,
    PreviewSelect
  },
  data() {
    return {
      questionPreview: {
        radio: 'PreviewRadio',
        checkbox: 'PreviewCheckBox',
        input: 'PreviewFillBlank',
        array: 'PreviewMatrix',
        paragraph: 'PreviewParagraph',
        sort: 'PreviewSort',
        select: 'PreviewSelect',
        nd_select: 'PreviewMulSelect'
      },
      diff: 'check',
      questionPreviewList: {}, // 当前问卷的题目列表
      btnName: 'goBack', // 右上角的功能按钮，goBack表示返回
      pathName: {
        check: {
          isShowSubjectType: false,
          isDisable: true,
          isQuestionNum: true,
          isSetDefaultValue: true
        },
        preview: {
          isShowSubjectType: true,
          isDisable: false,
          isQuestionNum: true,
          isSetDefaultValue: false
        },
        title: {
          isShowSubjectType: true,
          isDisable: false,
          isQuestionNum: true,
          isSetDefaultValue: false
        }
      }
    }
  },
  mounted() {
    this.getCurrentQuestionAllSubject(utils.getLocalStorage('answerid', ''))
  },
  methods: {
    getCurrentQuestionAllSubject(answerid) {
      const params = {
        questionId: localStorage.getItem('questId'),
        userId: this.$store.state.user.userInfo.userId,
        answerId: answerid
      }

      this.$api.getPaperQuestions(params).then((res) => {
        if (res.status == 200) {
          var arr = res.data.questions
          var indexCount = 0
          for (let index = 0; index < arr.length; index++) {
            const element = arr[index]
            if (element.type == 'paragraph') {
              indexCount = indexCount + 1
            } else {
              element['indexCopy'] = index - indexCount
            }
          }
          this.questionPreviewList = res.data
        } else {
          this.$message.error(res.message)
        }
      })
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
.main-container {
  height: 100%;
  padding: 15px;
  margin: 0 !important;
  .wholi {
    width: 100%;
    height: 100%;
    border-radius: 5px;
    background-color: #fff !important;
  }
  .page-title {
    height: 50px;
    line-height: 50px;
    color: #606266;
    font-size: 14px;
    padding-left: 25px;
    border-bottom: 1px solid #d8dee7;
  }
  .question-title {
    background-color: #5076df;
    height: 50px;
    line-height: 50px;
    text-align: center;
    font-size: 14px;
    color: #fff;
  }
  .question-main-container {
    background-color: #fff;
    height: calc(100% - 110px);
    .el-row,
    .el-col {
      height: 100%;
    }
    .grid-content {
      min-height: 36px;
    }
    .question-content {
      margin-top: 10px;
      background-color: #fff;
      // height: 100%;
      height: calc(100% - 80px);
      overflow-y: auto;
      padding: 20px;
    }
    .preview-container {
      padding: 15px;
      background-color: #fff;
      border-bottom: 1px solid #d8dee7;
    }
  }
}
.main-container {
  height: 100%;
  padding: 15px;
  margin: 0 !important;
  .whole {
    width: 100%;
    height: 100%;
    border-radius: 5px;
    background-color: #fff !important;
  }
}
</style>
