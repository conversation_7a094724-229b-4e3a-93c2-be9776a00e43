<template>
  <PageContainer>
    <div slot="content" class="whole">
      <div class="left">
        <div class="title_box"><span></span> 组织结构</div>
        <div class="left_d">
          <el-collapse v-model="activeName" style="padding: 0 10px 0 20px;" accordion @change="handelChange">
             <el-collapse-item v-for="(list, index) in collapseData" :key="index" :name="list.umId">
              <template slot="title">
                <el-tooltip class="item" effect="dark" :content="list.unitComName" placement="top-start">
                  <span>{{ list.unitComName }}</span>
                </el-tooltip>
              </template>
              <div class="sino_tree_box" style="margin: 0">
                <el-tree ref="tree" class="filter-tree" :data="treeData" :props="defaultProps" node-key="id" highlight-current @node-click="nodeClick"></el-tree>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
      <div class="right">
        <div style="display: flex; margin-bottom: 10px;">
          <el-input v-model="filters.staffName" class="sino_sdcp_input mr_15" placeholder="请输入姓名"></el-input>
          <el-input v-model="filters.mobile" class="sino_sdcp_input mr_15" placeholder="请输入手机号"></el-input>
          <el-select v-model="filters.stationStatus" class="sino_sdcp_input mr_15" placeholder="请选择在职状态" clearable>
            <el-option v-for="item in stationStatusList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
          <el-select v-model="filters.postId" class="sino_sdcp_input mr_15" placeholder="请选择岗位" clearable>
            <el-option v-for="item in postList" :key="item.id" :label="item.postName" :value="item.id"></el-option>
          </el-select>
          <el-select v-model="filters.sex" class="sino_sdcp_input mr_15" placeholder="请选择性别" clearable>
            <el-option v-for="item in sexList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
          <el-button type="primary" @click="inquiry">查询</el-button>
          <el-button type="primary" plain @click="reset">重置</el-button>
          <el-button type="primary" @click="exportFn">导出</el-button>
        </div>
        <div slot="content" style="height: 100%;">
          <div class="contentTable">
            <div class="contentTable-main table-content">
              <el-table :data="tableData" :height="tableHeight" stripe border @sort-change="handleSortChange">
                <el-table-column type="index" label="序号" width="50" align="center">
                  <template slot-scope="scope">
                    <span>{{ (pagination.current - 1) * pagination.size + scope.$index + 1 }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="职工工号" width="150" prop="staffNumber" show-overflow-tooltip> </el-table-column>
                <el-table-column label="人员姓名" width="150" prop="staffName" show-overflow-tooltip sortable="custom">
                  <template slot-scope="scope">
                    <span class="color_blue" @click="ViewFn(scope.row)">
                      {{ scope.row.staffName }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column label="性别" prop="sex" show-overflow-tooltip sortable="custom">
                  <template slot-scope="scope">
                    {{ scope.row.sex == 1 ? '男' : scope.row.sex == 2 ? '女' : '' }}
                  </template>
                </el-table-column>
                <el-table-column label="手机号码" prop="mobile" show-overflow-tooltip> </el-table-column>
                <el-table-column label="办公电话" prop="phone" show-overflow-tooltip> </el-table-column>
                <el-table-column label="归属单位" prop="unitName" show-overflow-tooltip> </el-table-column>
                <el-table-column label="所属部门" prop="officeName" show-overflow-tooltip> </el-table-column>
                <el-table-column label="岗位" prop="postName" show-overflow-tooltip sortable="custom"> </el-table-column>
                <el-table-column label="职务" prop="jobName" show-overflow-tooltip> </el-table-column>
                <el-table-column label="在职状态" prop="stationStatus" show-overflow-tooltip sortable="custom">
                  <template slot-scope="scope">
                    {{ scope.row.stationStatus == 0 ? '在职' : '离职' }}
                  </template>
                </el-table-column>
                <el-table-column label="激活状态" prop="activationFlag" show-overflow-tooltip sortable="custom">
                  <template slot-scope="scope">
                    {{ scope.row.activationFlag == 0 ? '已激活' : '未激活' }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <div class="contentTable-footer">
            <el-pagination
              :current-page="pagination.current"
              :page-sizes="[15, 30, 50, 100]"
              :page-size="pagination.size"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            ></el-pagination>
          </div>
        </div>
      </div>
    </div>
  </PageContainer>
</template>

<script>
import store from '@/store/index'
import axios from 'axios'
import { transData } from '@/util'
import tableListMixin from '@/mixins/tableListMixin.js'
export default {
  name: 'personnelManage',
  mixins: [tableListMixin],
  beforeRouteEnter(to, from, next) {
    let names = []
    to.matched.map((v, i) => {
      if (i > 0) {
        v.components.default.name && names.push(v.components.default.name)
      }
    })
    // 进入页面时，先将当前页面的 name 信息存入 keep-alive 全局状态
    setTimeout(() => {
      next((vm) => {
        vm.$store.commit('keepAlive/add', names)
      })
    }, 0)
  },
  async beforeRouteLeave(to, from, next) {
    // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
    if (!['staffMess'].includes(to.name)) {
      // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      postList: [],
      activeName: '',
      collapseData: [],
      treeData: [],
      treeLoading: true,
      checkedTreeNode: '',
      defaultProps: {
        children: 'children',
        label: 'deptName'
      },
      orderItems: [],
      filters: {
        stationStatus: '',
        postId: '',
        pmId: '',
        officeId: '',
        activationFlag: '',
        staffName: '',
        nativePlace: '',
        nation: null,
        sex: '',
        staffNumber: '',
        idCard: '',
        phone: '',
        mobile: ''
      },
      unitList: [],
      tableData: [],
      pagination: {
        current: 1,
        size: 15
      }, // 分页数据
      total: 0, // 数据总条数
      // -------------------Dialog_导入
      addFileName: 'staff',
      sexList: [
        { id: 1, name: '男' },
        { id: 2, name: '女' }
      ],
      stationStatusList: [
        { id: 0, name: '在职' },
        { id: 1, name: '离职' }
      ]
    }
  },
  created() {},
  activated() {
    this.staffListByPageFn()
  },
  mounted() {
    this.getUnitListFn()
    this.postListFn() // 岗位列表
  },
  methods: {
    handleSortChange({ order, prop }) {
      this.orderItems = []
      let obj = {}
      if (order == 'ascending') {
        obj.asc = true
        obj.column = prop
        this.orderItems.push(obj)
      } else if (order == 'descending') {
        obj.asc = false
        obj.column = prop
        this.orderItems.push(obj)
      } else {
        this.orderItems = []
      }
      this.pagination.current = 1
      this.pagination.size = 15
      this.staffListByPageFn()
    },
    inquiry() {
      this.pagination.current = 1
      this.pagination.size = 15
      this.filters.stationStatus = this.filters.stationStatus
      this.filters.sex = this.filters.sex
      this.filters.postId = this.filters.postId
      this.filters.staffName = this.filters.staffName
      this.filters.mobile = this.filters.mobile
      this.staffListByPageFn()
    },
    reset() {
      this.pagination.current = 1
      this.pagination.size = 15
      this.filters.stationStatus = ''
      this.filters.sex = ''
      this.filters.postId = ''
      this.filters.staffName = ''
      this.filters.mobile = ''
      this.staffListByPageFn()
    },
    handelChange(val) {
      this.activeName = val
      if (val != '') {
        this.pagination.current = 1
        this.pagination.size = 15
        this.getDeptListFn(val)
      }
      this.filters.pmId = val
      this.filters.officeId = ''
      this.staffListByPageFn()
    },
    nodeClick(val) {
      this.pagination.current = 1
      this.pagination.size = 15
      this.checkedTreeNode = val
      this.filters.pmId = val.umId
      this.filters.officeId = val.id
      this.staffListByPageFn()
    },
    //  获取岗位列表
    postListFn() {
      this.$api.selectByList().then((res) => {
        if (res.code == 200) {
          this.postList = res.data
        }
      })
    },
    // 单位列表
    getUnitListFn() {
      this.$api.getSelected({}).then((res) => {
        if (res.code == 200) {
          if (this.nature) {
            // 判断院内 / 院外
            res.data.map((list) => {
              // 院内：1，院外：2
              list.nature == 1 ? (this.unitList = this.collapseData.push(list)) : ''
            })
          } else {
            this.collapseData = this.unitList = res.data
          }
          this.staffListByPageFn()
        }
      })
    },
    // 部门列表
    getDeptListFn(unitId) {
      this.treeData = []
      this.$api
        .getSelectedDept({
          unitId: unitId
        })
        .then((res) => {
          if (res.code == 200) {
            this.treeData = transData(res.data, 'id', 'pid', 'children')
          }
        })
    },
    //  获取人员信息列表
    staffListByPageFn() {
      this.$api
        .staffListByPage({
          notInIds: this.notInIds,
          ...this.filters,
          ...this.pagination,
          nature: this.nature ? 1 : '',
          orderItems: this.orderItems
        })
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.records
            this.pagination.current = res.data.current
            this.pagination.size = res.data.size
            this.total = res.data.total
          }
        })
    },
    handleSizeChange(val) {
      this.pagination.current = 1
      this.pagination.size = val
      this.staffListByPageFn()
    },
    handleCurrentChange(val) {
      this.pagination.current = val
      this.staffListByPageFn()
    },
    // -----------------------------------Table-Fn
    ViewFn(row) {
      this.$router.push({
        path: '/staffMess',
        query: { type: 'View', id: row.id }
      })
    },
    //  导出
    exportFn() {
      let departmentExportVo = {
        ...this.filters,
        size: this.pagination.size
      }
      const userInfo = store.state.user.userInfo.user

      let menuList = this.$store.state.menu.routes
      let firstTitle = ''
      let routeList = []
      this.$router.currentRoute.matched.filter(item => item.name).forEach(el => {
        routeList.push(el.meta.title)
      })
      if (menuList.length) {
        firstTitle = menuList[this.$store.state.menu.headerActived].meta.title
        routeList.unshift(firstTitle)
      }
      axios({
        method: 'post',
        url: __PATH.VUE_SPACE_API + 'hospitalStaff/hospital-staff/exportList',
        data: departmentExportVo,
        responseType: 'arraybuffer',
        headers: {
          'Content-Type': 'application/json;charset=utf-8',
          Authorization: 'Bearer ' + store.state.user.token,
          hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
          unitCode: userInfo.unitCode ?? 'BJSYGJ',
          'operation-type': 4,
          'operation-content': encodeURIComponent(routeList.join(','))
        }
      })
        .then((res) => {
          this.$message.success(res.message || '导出成功')
          this.$tools.downloadFile(res, this)
        })
        .catch((res) => {
          this.$message.error(res.message || '导出失败')
        })
    }
  }
  // -----------------------------------Table-Search
}
</script>
<style lang="scss" scoped>
.whole {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;

  .left {
    width: 250px;
    height: 100%;
    background-color: #fff;
    border-radius: 5px;
    padding-right: 5px;
    padding-left: 10px;
    overflow: hidden;

    .left_d {
      height: calc(100% - 20px);
      overflow: auto;
    }
  }

  .right {
    width: calc(100% - 260px);
    background-color: #fff;
    height: 100%;
    border-radius: 5px;
    padding: 10px;
  }

  .ipt {
    width: 200px;
    margin-right: 10px;
  }
}

.title_box {
  box-sizing: border-box;
  padding-left: 20px;
  height: 50px;
  width: 100%;
  line-height: 50px;
  text-align: left;
  border-bottom: 1px solid #d8dee7;
}

.color_blue {
  color: #5188fc;
  cursor: pointer;
}

.sino_sdcp_input {
  width: 200px;
  margin-right: 10px;
}

.contentTable {
  height: calc(100% - 70px);
  background: #fff;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  overflow: auto;

  .contentTable-main {
    flex: 1;
    overflow: auto;
  }

  .contentTable-footer {
    padding: 10px 0 0;
  }

  .alarmLevel {
    padding: 3px 6px;
    border-radius: 4px;
    color: #fff;
    line-height: 14px;
  }

  .alarmStatus {
    position: relative;
    display: inline-block;
    padding-left: 12px;

    .alarmStatusIcon {
      display: inline-block;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 8px;
      height: 8px;
      border-radius: 100%;
    }
  }

  .collectIcon {
    font-size: 16px;
    margin-right: 4px;
  }
}
::v-deep .el-collapse-item__header {
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: block;
}
</style>
