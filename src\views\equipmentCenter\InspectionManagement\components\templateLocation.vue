<template>
  <el-dialog
    v-if="templateLocationVisible"
    :title="type == 'template' && systemType == '2' ? '保养模板选择' : type == 'template' && systemType != '2' ? '巡检模板选择' : '定位点选择'"
    :visible.sync="templateLocationVisible"
    :modal-append-to-body="false"
    destroy-on-close
    top="15vh"
    width="60%"
  >
    <div v-if="type == 'template'" class="templateContainer">
      <div v-if="systemType != '4'" v-loading class="templateType">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="模板分类" name="moban" />
          <el-tab-pane v-if="systemType != '6'" label="设备类别" name="shebei" />
        </el-tabs>
        <div class="tree">
          <el-tree
            ref="tree"
            v-loading="treeLoading"
            style="margin-top: 10px"
            :check-strictly="true"
            :data="treeData"
            :props="defaultProps"
            node-key="id"
            :highlight-current="true"
            @node-click="handleNodeClick"
          >
            <span slot-scope="{ node }" class="custom-tree-node" style="width: 100%">
              <el-tooltip v-if="node.label.length > 17" class="item" effect="dark" :content="node.label" placement="top-start">
                <span class="treeLabel">{{ node.label }}</span>
              </el-tooltip>
              <span v-else class="treeLabel">{{ node.label }}</span>
            </span>
          </el-tree>
        </div>
      </div>
      <div class="templateList" :class="systemType == '4' ? 'trueList' : ''">
        <div>
          <el-input v-model="projectName" placeholder="模板名称" style="width: 200px; margin: 0 10px 10px 0"></el-input>
          <el-button type="primary" @click="inquire">查询</el-button>
          <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db" @click="resetData">重置</el-button>
        </div>
        <el-table ref="singleTable" v-loading="loading" :data="tableData" highlight-current-row style="width: 100%" height="calc(100% - 42px)" @current-change="clickRow">
          <el-table-column width="55">
            <template slot-scope="scope">
              <el-radio v-model="tableRadio" :label="scope.row.id">
                <span></span>
              </el-radio>
            </template>
          </el-table-column>
          <el-table-column label="序号" type="index" width="55"> </el-table-column>
          <el-table-column prop="projectName" label="模板名称" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="equipmentTypeName" label="模板分类" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="projectExplain" label="模板说明" show-overflow-tooltip> </el-table-column>
        </el-table>
      </div>
    </div>
    <el-table v-if="type == 'location'" ref="singleTable" v-loading="loading" :data="tableData" highlight-current-row style="width: 100%" height="288" @current-change="clickRow">
      <el-table-column width="55">
        <template slot-scope="scope">
          <el-radio v-model="tableRadio" :label="scope.row.id">
            <span></span>
          </el-radio>
        </template>
      </el-table-column>
      <el-table-column label="序号" type="index" width="55"> </el-table-column>
      <el-table-column prop="locationPointCode" label="定位点编号" width="180" show-overflow-tooltip> </el-table-column>
      <el-table-column prop="locationPointName" label="定位点名称" width="180" show-overflow-tooltip> </el-table-column>
      <el-table-column label="类型">
        <template slot-scope="scope">
          <span>{{ scope.row.locationPointType == '1' ? '蓝牙定位点' : 'RFID定位点' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="remarks" label="描述" show-overflow-tooltip> </el-table-column>
    </el-table>
    <div class="" style="padding-top: 10px; text-align: right">
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        :current-page="paginationData.currentPage"
        :page-sizes="[10, 15, 30, 50]"
        :page-size="paginationData.pageSize"
        :total="paginationData.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db" @click="cancel">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  data() {
    return {
      systemType: '',
      type: 'template',
      loading: false,
      templateLocationVisible: false,
      tableRadio: '',
      tableData: [],
      paginationData: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      batchAddStatus: false,
      projectName: '',
      activeName: 'moban',
      treeLoading: false,
      treeData: [],
      defaultProps: {
        label(data, node) {
          return (data.dictName || data.baseName) + `  (${data.dataSum})`
        },
        children: 'children' || ''
      },
      checkedData: {}
    }
  },
  created() {
    if (this.$route.path.indexOf('/InspectionManagement') != -1) {
      this.systemType = '1'
    } else if (this.$route.path.indexOf('/MaintenanceManagement') != -1) {
      this.systemType = '2'
    } else if (this.$route.path.indexOf('/comInspectionManagement') != -1) {
      this.systemType = '3'
    } else if (this.$route.path.indexOf('/hazardousChemicalGoods_inspection') != -1) {
      this.systemType = '4'
    } else if (this.$route.path.indexOf('/annualManagement') != -1) {
      this.systemType = '5'
    } else if (this.$route.path.indexOf('/vp_planManagement') != -1) {
      this.systemType = '6'
    }
    if (this.systemType == '4') {
      this.getTemplateList()
    } else {
      this.getTemTree()
    }
  },
  methods: {
    handleClick(tab) {
      this.activeName = tab.name
      this.treeData = []
      this.tableData = []
      if (this.activeName == 'moban') {
        this.getTemTree()
      }
      if (this.activeName == 'shebei') {
        this.getEquTree()
      }
    },
    // 获取模板树形结构
    getTemTree() {
      this.treeLoading = true
      // moduleDifferentiation 1.设施设备巡检 2.设施保养 3.综合巡检
      this.$api.getTemTree({ moduleDifferentiation: this.systemType }).then((res) => {
        if (res.code == 200) {
          this.treeData = res.data
        }
        this.treeLoading = false
      })
    },
    // 获取设备树形结构
    getEquTree() {
      this.treeLoading = true
      this.$api.getEquTree({ systemIdentificationClassification: this.systemType }).then((res) => {
        if (res.code == 200) {
          this.treeData = res.data
        }
        this.treeLoading = false
      })
    },
    // 树状图点击
    handleNodeClick(data) {
      this.paginationData.current = 1
      this.checkedData = data
      this.tableData = []
      this.$refs.tree.setCurrentKey(this.checkedData.id)
      this.getTemplateList()
    },
    inquire() {
      this.paginationData = {
        currentPage: 1,
        pageSize: 10,
        total: 0
      }
      this.getTemplateList()
    },
    resetData() {
      this.paginationData = {
        currentPage: 1,
        pageSize: 10,
        total: 0
      }
      this.projectName = ''
      this.getTemplateList()
    },
    getTemplateList() {
      this.loading = true
      const params = {
        systemIdentificationClassification: this.systemType,
        dictTypeId: this.checkedData.id,
        pageNo: this.paginationData.currentPage,
        pageSize: this.paginationData.pageSize,
        projectName: this.projectName
      }
      if (this.systemType == '4') {
        // params.dictTypeId = '4'
      }
      this.$api.getInspectionTemplate(params).then((res) => {
        if (res.code == '200') {
          this.tableData = res.data.list
          this.paginationData.total = res.data.count
          this.loading = false
        }
      })
    },
    getLocationList() {
      this.loading = true
      this.$api
        .getLocationPoint({
          pageNo: this.paginationData.currentPage,
          pageSize: this.paginationData.pageSize
        })
        .then((res) => {
          if (res.code == '200') {
            this.tableData = res.data.list
            this.paginationData.total = res.data.count
            this.loading = false
          }
        })
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      if (this.type == 'template') {
        this.getTemplateList()
      } else {
        this.getLocationList()
      }
    },
    // 每页的条数
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.paginationData.currentPage = 1
      if (this.type == 'template') {
        this.getTemplateList()
      } else {
        this.getLocationList()
      }
    },
    cancel() {
      this.templateLocationVisible = false
      this.tableRadio = ''
      this.paginationData = {
        currentPage: 1,
        pageSize: 10,
        total: 0
      }
      this.treeData = []
      this.checkedData = {}
      this.$emit('closed')
    },
    clickRow(val) {
      this.tableRadio = val.id
    },
    confirm() {
      if (this.tableRadio == '') {
        this.$message({
          type: 'error',
          message: '请选择模板'
        })
        return
      }
      this.templateLocationVisible = false
      if (this.tableRadio) {
        const checkItem = this.tableData.find((i) => i.id == this.tableRadio)
        if (this.batchAddStatus) {
          this.$emit('correlation', checkItem)
          this.batchAddStatus = false
        } else {
          this.$emit('closed', checkItem)
        }
      }
      this.tableRadio = ''
      this.paginationData = {
        currentPage: 1,
        pageSize: 10,
        total: 0
      }
      this.checkedData = {}
    },
    // 批量添加
    batchAdd() {
      this.batchAddStatus = true
    }
  }
}
</script>
<style lang="scss" scoped>
.templateContainer {
  width: 100%;
  padding: 0;
  display: flex;
  height: 500px;
  .templateType {
    width: 20%;
    height: 100%;
    background: #fff;
    overflow: auto;
    padding: 0 16px;
  }
  .templateList {
    width: calc(100% - 20% - 10px);
    margin-left: 10px;
    background: #fff;
    padding: 16px;
  }
  .trueList {
    width: calc(100% - 10px);
  }
}
</style>
