<template>
  <div class="content">
    <div class="content_box">
      <div class="top_content">
        <div>
          <el-input
            v-model="filterData.planName"
            style="width: 200px; margin-right: 20px"
            placeholder="计划名称"
            maxlength="25"
            onkeyup="if(value.length>25)value=value.slice(0,25)"
            @keyup.enter.native="searchByCondition"
          ></el-input>
          <el-select v-model="filterData.cycleType" placeholder="请选择周期类型">
            <el-option v-for="item in cycleTypeList" :key="item.cycleType" :label="item.label" :value="item.cycleType"> </el-option>
          </el-select>
          <el-input
            v-model="filterData.dept"
            style="width: 180px; margin: 0 20px"
            placeholder="巡检部门"
            maxlength="25"
            onkeyup="if(value.length>25)value=value.slice(0,25)"
            @keyup.enter.native="searchByCondition"
          ></el-input>
          <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db" @click="resetCondition">重置</el-button>
          <el-button type="primary" style="font-size: 14px" @click="searchByCondition">查询</el-button>
        </div>
        <div>
          <el-button type="primary" icon="el-icon-plus" style="font-size: 14px" @click="addFn('add')">新增</el-button>
        </div>
      </div>
      <div class="table_list">
        <el-table
          ref="materialTable"
          v-loading="tableLoading"
          :data="tableData"
          height="calc(100% - 10px)"
          border
          :header-cell-style="{ background: '#F6F5FA' }"
          style="width: 100%"
          :cell-style="{ padding: '8px 0 8px 0' }"
          stripe
          highlight-current-row
          :empty-text="emptyText"
          row-key="id"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="60" align="center"></el-table-column>
          <el-table-column label="序号" type="index" width="60">
            <template slot-scope="scope">
              <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip label="计划名称">
            <template slot-scope="scope">
              <el-link type="primary" @click="planDetail(scope.row, 'detail')">{{ scope.row.planName }}</el-link>
            </template>
          </el-table-column>
          <el-table-column prop="planTypeName" label="计划类型"></el-table-column>
          <el-table-column label="周期类型" width="95">
            <template slot-scope="scope">
              <span>{{ cycleTypeFn(scope.row) }}</span>
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip label="开始日期">
            <template slot-scope="scope">
              <span>{{ scope.row.cycleType == '8' ? moment(scope.row.startDate).format('YYYY-MM-DD') : moment(scope.row.createStartTime).format('YYYY-MM-DD') }}</span>
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip label="结束日期">
            <template slot-scope="scope">
              <span>
                {{
                  scope.row.cycleType == '5' || scope.row.cycleType == '8'
                    ? moment(scope.row.startDate).add(scope.row.finalTime, 'd').format('YYYY-MM-DD')
                    : moment(scope.row.createEndTime).format('YYYY-MM-DD')
                }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="distributionTeamName" show-overflow-tooltip label="巡检部门" width="115"></el-table-column>
          <el-table-column label="状态" width="115">
            <template slot-scope="scope">
              <div>
                <span v-if="scope.row.useState == '1'" class="disable">
                  <span></span>
                  已禁用
                </span>
                <span v-if="scope.row.useState == '0'" class="enable">
                  <span></span>
                  已启用
                </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="完成率" width="115">
            <template slot-scope="scope">
              <span>{{ scope.row.percentageComplete + '%' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="230">
            <template slot-scope="scope">
              <el-link @click="copyPlan(scope.row)">复制</el-link>
              <el-link :type="scope.row.useState == '1' ? 'danger' : 'info'" :disabled="scope.row.useState == '0'" @click="operation(scope.row, 'delete')"> 删除 </el-link>
              <el-link :type="scope.row.useState == '1' ? 'primary' : 'info'" :disabled="scope.row.useState == '0'" @click="operation(scope.row, 'edit')">编辑</el-link>
              <el-link type="primary">
                <el-popconfirm title="是否修改此计划的状态？" @confirm="operation(scope.row, 'status')">
                  <span v-if="scope.row.useState == '1'" slot="reference">启用</span>
                  <span v-else slot="reference">禁用</span>
                </el-popconfirm>
              </el-link>
              <el-link :type="scope.row.useState == '0' ? 'primary' : 'info'" :disabled="scope.row.useState == '1'" @click="progress(scope.row)">进度</el-link>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        :current-page="paginationData.currentPage"
        :page-sizes="[15, 30, 50, 100]"
        :page-size="paginationData.pageSize"
        :total="paginationData.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
  </div>
</template>
<script type="text/ecmascript-6">
import moment from 'moment'
import { auth } from '@/util'
export default {
  name: 'videoPatrolPlanManagement',
  components: {},
  beforeRouteEnter(to, from, next) {
    let names = []
    to.matched.map((v, i) => {
      if (i > 0) {
        v.components.default.name && names.push(v.components.default.name)
      }
    })
    next((vm) => {
      if (from.query.activeName) {
        vm.activeName = from.query.activeName
      }
      vm.$store.commit('keepAlive/add', names)
    })
  },
  async beforeRouteLeave(to, from, next) {
    // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
    if (!['addPlans', 'progressDetail'].includes(to.name)) {
      // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      moment,
      isdownload: false,
      activeName: '',
      systemType: '',
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      // 周期类型
      cycleTypeList: [
        {
          cycleType: 8,
          label: '单次'
        },
        {
          cycleType: 6,
          label: '每日'
        },
        {
          cycleType: 0,
          label: '每周'
        },
        {
          cycleType: 2,
          label: '每月'
        },
        {
          cycleType: 3,
          label: '季度'
        },
        {
          cycleType: 5,
          label: '全年'
        }
      ],
      tableData: [],
      // 列表过滤条件
      filterData: {
        planName: '',
        cycleType: '',
        dept: ''
      },
      emptyText: '暂无数据',
      tableLoading: false,
      activeType: '',
      tableClickArry: [],
      useState: '',
      downLoadId: '',
      planTypeList: [],
      isHscInspection: false // 危化品巡检
    }
  },
  computed: {
    typeMenu() {
      return this.planTypeList.filter((i) => this.auth(`planManagement:${i.planTypeCode}`))
    }
  },
  created() {
    this.isHscInspection = this.$route.meta.type == '4'
    this.initEvent()
  },
  activated() {
    this.initEvent()
  },
  methods: {
    initEvent() {
      // // 危化品巡检
      // if (this.isHscInspection) {
      //   this.systemType = '4'
      //   this.getPlanList('')
      // } else {
      //   if (this.$route.path.indexOf('/InspectionManagement') != -1) {
      //     this.systemType = '1'
      //   } else if (this.$route.path.indexOf('/MaintenanceManagement') != -1) {
      //     this.systemType = '2'
      //   } else if (this.$route.path.indexOf('/comInspectionManagement') != -1) {
      //     this.systemType = '3'
      //   }
      //   this.getPlantType()
      // }
      // 视频巡检专用
      this.systemType = '6'
      this.getPlanList('')
    },
    auth,
    // 获取巡检分类字典
    getPlantType() {
      let params = {
        pageSize: 999,
        pageNo: 1,
        dictType: 'lnspection_type',
        dictName: ''
      }
      this.$api.sysDictData(params).then((res) => {
        if (res.code == 200) {
          const sortArr = []
          const lastArr = []
          this.planTypeList = []
          res.data.list.forEach((i) => {
            if (i.sort) {
              sortArr.push(i)
            } else {
              lastArr.push(i)
            }
          })
          sortArr.sort((a, b) => a.sort - b.sort)
          const menuList = sortArr.concat(lastArr)
          menuList.forEach((i) => {
            this.planTypeList.push({
              planTypeCode: i.dictCode,
              planTypeName: i.dictName
            })
          })
          const correctMenu = this.planTypeList.filter((i) => this.auth(`planManagement:${i.planTypeCode}`))
          if (this.$route.query.activeName) {
            this.activeName = this.$route.query.activeName
          } else {
            this.activeName = correctMenu[0].planTypeCode || ''
          }
          this.getPlanList(this.activeName)
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 获取计划列表
    getPlanList(typeCode) {
      const params = {
        pageNo: this.paginationData.currentPage,
        pageSize: this.paginationData.pageSize,
        planName: this.filterData.planName,
        cycleType: this.filterData.cycleType,
        distributionTeamName: this.filterData.dept,
        planTypeId: typeCode,
        systemCode: this.systemType, // 1:设备 2: 保养 3：巡检
        deleteFlag: 0 // 0:为删除 1:已删除
      }
      this.tableLoading = true
      this.$api.getPlansList(params).then((res) => {
        if (res.code == '200') {
          this.tableData = res.data.list
          this.paginationData.total = res.data.count
        }
        this.tableLoading = false
      })
    },
    // 切换tabs
    handleClick(tab, event) {
      this.paginationData.currentPage = 1
      this.paginationData.pageSize = 15
      this.$router.push({
        query: {
          activeName: tab.name
        }
      })
      this.getPlanList(tab.name)
    },
    // 新增
    addFn(type) {
      this.$router.push({
        path: 'vp_planManagement/addPlans',
        query: {
          activeType: type,
          id: 6, // 视频巡逻专用
          type: '3'
        }
      })
    },
    // 进度
    progress(row) {
      this.$router.push({
        path: 'vp_planManagement/vp_progressDetail',
        query: {
          id: row.id
        }
      })
    },
    handleSelectionChange(val) {
      this.tableClickArry = val
    },
    // 条件查询
    searchByCondition() {
      this.paginationData.currentPage = 1
      this.getPlanList(this.activeName)
    },
    // 重置查询条件
    resetCondition() {
      this.paginationData = {
        currentPage: 1,
        pageSize: 15,
        total: 0
      }
      this.filterData = {
        planName: '',
        cycleType: '',
        dept: ''
      }
      this.getPlanList(this.activeName)
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.getPlanList(this.activeName)
    },
    // 每页的条数
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.paginationData.currentPage = 1
      this.getPlanList(this.activeName)
    },
    // 周期类型
    cycleTypeFn(row) {
      const item = this.cycleTypeList.filter((i) => i.cycleType == row.cycleType)
      return item[0].label
    },
    // 列表操作
    operation(row, type) {
      // 编辑
      if (type == 'edit') {
        sessionStorage.setItem('row', JSON.stringify(row))
        this.$router.push({
          path: 'vp_planManagement/addPlans',
          query: {
            activeType: type,
            id: 6, // 视频巡逻专用
            type: '3',
            row
          }
        })
      } else if (type == 'status') {
        const status = row.useState == '1' ? 'enable' : 'disable'
        this.$api
          .updateMaintainPlanState({
            id: row.id,
            useState: status == 'enable' ? 0 : 1 // 0:启用，1：禁用
          })
          .then((res) => {
            if (res.code == '200') {
              this.$message({
                type: 'success',
                message: `${status == 'enable' ? '启用' : '禁用'}成功`
              })
              this.getPlanList(this.activeName)
            } else {
              this.$message({
                type: 'error',
                message: res.message || '修改计划状态失败'
              })
            }
          })
      } else if (type == 'delete') {
        this.$confirm('删除后将无法恢复，是否确定删除？', '信息提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        })
          .then(() => {
            this.$api.deletePlan({ id: row.id }, { 'operation-type': 3, 'operation-name': row.planName, 'operation-id': row.id }).then((res) => {
              if (res.code == '200') {
                this.$message({
                  type: 'success',
                  message: '删除成功'
                })
                this.getPlanList(this.activeName)
              } else {
                this.$message({
                  type: 'error',
                  message: res.message || '删除失败'
                })
              }
            })
          })
          .catch(() => {})
      }
    },
    // 计划详情
    planDetail(row, type) {
      sessionStorage.setItem('row', JSON.stringify(row))
      this.$router.push({
        path: 'vp_planManagement/addPlans',
        query: {
          activeType: type,
          id: 6, // 视频巡逻专用
          type: '3'
        }
      })
    },
    // 复制
    copyPlan(row) {
      sessionStorage.setItem('row', JSON.stringify(row))
      this.$router.push({
        path: 'vp_planManagement/addPlans',
        query: {
          activeType: 'copy',
          id: 6, // 视频巡逻专用
          type: '3',
          row
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  height: 100%;
  display: flex;
  .content_box {
    width: calc(100% - 30px);
    margin: 15px;
    border-radius: 10px;
    padding: 20px 25px 25px;
    background: #fff;
    display: flex;
    flex-direction: column;
    .top_content {
      width: 100%;
      display: flex;
      justify-content: space-between;
      margin-bottom: 25px;
    }
    .table_list {
      height: 100%;
      .disable {
        color: #414653;
        span {
          display: inline-block;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: #ccced3;
        }
      }
      .enable {
        color: #08cb83;
        span {
          display: inline-block;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: #08cb83;
        }
      }
      .el-link {
        margin: 0 4px;
      }
    }
  }
}
</style>
