<template>
  <PageContainer>
    <div slot="content" class="form-content">
      <div class="form-title"><i class="el-icon-arrow-left" @click="$router.go(-1)"></i>预案详情</div>
      <div class="content-main">
        <el-tabs v-model="activeTabs">
          <el-tab-pane label="基础信息" name="1">
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="11">
                <p class="info-item">
                  <span class="info-label">预案名称：</span>
                  <span class="info-value">{{ planData.planName }}</span>
                </p>
              </el-col>
              <el-col :md="11">
                <p class="info-item">
                  <span class="info-label">关联预案类型：</span>
                  <span class="info-value">{{ planData.planType ? planTypeList.find((v) => v.thirdSystemCode == planData.planType)?.thirdSystemName : '' }}</span>
                </p>
              </el-col>
            </el-row>
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="11">
                <p class="info-item">
                  <span class="info-label">报警类型：</span>
                  <span class="info-value">{{ planData.alarmTypeName }}</span>
                </p>
              </el-col>
              <el-col :md="11">
                <p class="info-item">
                  <span class="info-label">空间类型：</span>
                  <span class="info-value">{{ planData.spaceTypeName }}</span>
                </p>
              </el-col>
            </el-row>
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="11">
                <p class="info-item">
                  <span class="info-label">说明：</span>
                  <span class="info-value">{{ planData.regulationsDesc }}</span>
                </p>
              </el-col>
              <el-col :md="11">
                <p class="info-item">
                  <span class="info-label">预案号：</span>
                  <span class="info-value">{{ planData.versionNo }}</span>
                </p>
              </el-col>
            </el-row>
            <!-- <el-row :gutter="24" style="margin: 0">
              <el-col :md="22">
                <p class="info-item">
                  <span class="info-label">说明：</span>
                  <span class="info-value">{{planData.regulationsDesc}}</span>
                </p>
              </el-col>
            </el-row> -->
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="11">
                <p class="info-item">
                  <span class="info-label">法规流程图：</span>
                  <span class="info-value">
                    <div v-if="planData.regulationsFlow?.length > 0">
                      <img :src="$tools.imgUrlTranslation(planData.regulationsFlow[0].url)" @click="viewImage" />
                    </div>
                    <el-image-viewer v-if="showViewer" :on-close="() => (showViewer = false)" :url-list="iconPathList" />
                  </span>
                </p>
              </el-col>
              <el-col :md="11">
                <p class="info-item">
                  <span class="info-label">法规文档：</span>
                  <span class="info-value">
                    <div style="cursor: pointer; color: #3562db" @click="viewFile">
                      {{ planData.regulationsDoc?.length > 0 ? planData.regulationsDoc[0].name : '' }}
                    </div>
                  </span>
                </p>
              </el-col>
            </el-row>
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="22">
                <p class="info-item">
                  <span class="info-label">法规文案：</span>
                  <span class="info-value" v-html="planData.regulationsText"></span>
                </p>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane v-if="$route.query?.planType == 0" label="预案流程" name="2">
            <flowChart v-if="activeTabs == 2" type="view" :eventData="addEventData" />
          </el-tab-pane>
          <el-tab-pane label="往期版本" name="3">
            <div class="table-main">
              <TablePage
                ref="table"
                v-loading="tableLoading"
                :showPage="true"
                :tableColumn="tableColumn"
                :data="tableData"
                height="calc(100% - 40px)"
                :pageData="pageData"
                :pageProps="pageProps"
                @pagination="paginationChange"
              >
              </TablePage>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <div slot="footer"></div>
  </PageContainer>
</template>
<script lang="jsx">
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import flowChart from '../../components/flowChart'
export default {
  name: 'planDetails',
  components: {
    ElImageViewer,
    flowChart
  },
  async beforeRouteLeave(to, from, next) {
    if (!['planList'].includes(to.name)) {
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      activeTabs: '1',
      planData: {},
      planTypeList: [],
      showViewer: false,
      iconPathList: [], // 图片列表
      addEventData: {
        1: {}, // 演习
        2: {}, // 演习-启动预案
        3: {}, // 演习-自定义
        4: {}, // 确警
        5: {}, // 确警-启动预案
        6: {} // 确警-自定义
      },
      tableLoading: false,
      tableData: [],
      pageData: {
        current: 1,
        size: 15,
        total: 0
      },
      pageProps: {
        page: 'current',
        pageSize: 'size',
        total: 'total'
      },
      tableColumn: []
    }
  },
  computed: {},
  watch: {
    activeTabs(val) {
      if (val == 3) {
        this.getHistoryPlan()
      }
    }
  },
  activated() {
    this.init()
  },
  created() {
    if (!this.$store.state.keepAlive.list.includes('planList')) {
      this.init()
    }
  },
  methods: {
    init() {
      Object.assign(this.$data, this.$options.data())
      this.tableColumn = [
        {
          prop: 'versionNo',
          label: '版本号'
        },
        {
          prop: 'updateName',
          label: '编辑人'
        },
        {
          prop: 'updateTimeStr',
          label: '修改时间'
        },
        {
          width: 150,
          prop: 'operation',
          label: '操作',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                {this.planData.newStatus == 1 ? (
                  <span class="operationBtn-span" style="color: #3562DB" onClick={() => this.control('detail', row.row)}>
                    查看
                  </span>
                ) : (
                  ''
                )}
                <span class="operationBtn-span" style="color: #3562DB" onClick={() => this.control('download', row.row)}>
                  下载
                </span>
                {row.row.planCategory != 2 ? (
                  <span class="operationBtn-span" style="color: #3562DB" onClick={() => this.control('repeal', row.row)}>
                    废除
                  </span>
                ) : (
                  ''
                )}
              </div>
            )
          }
        }
      ]
      this.getAlarmSystem()
      this.getPlanDetail()
    },
    control(type, row) {
      console.log(type, row)
      if (type == 'detail') {
        // 详情
        this.activeTabs = '1'
        this.$router.push({
          path: '/planManage/planList/planDetails',
          query: {
            type,
            planType: row?.planType ?? '',
            id: row?.id ?? ''
          }
        })
        this.init()
      } else if (type == 'repeal') {
        // 废除
        this.$confirm('确认要废除当前预案吗？废除后数据不可恢复。', '废除预案', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '废除预案',
          cancelButtonText: '取消操作',
          type: 'warning'
        }).then(() => {
          this.$api.RepealPlan({ id: row.id, planSource: row.planSource }).then((res) => {
            if (res.code == 200 && res.data) {
              this.$message({ message: '预案废除成功', type: 'success' })
              this.isLastPage(1)
              this.searchForm()
            } else {
              this.$message({ message: res.msg, type: 'error' })
            }
          })
        })
      } else if (type == 'download') {
        // 下载
        const a = document.createElement('a')
        const name = JSON.parse(row.regulationsDoc)[0]?.name ?? ''
        const url = this.$tools.imgUrlTranslation(JSON.parse(row.regulationsDoc)[0]?.url ?? '')
        // 这里是将url转成blob地址，
        fetch(url)
          .then((res) => res.blob())
          .then((blob) => {
            // 将链接地址字符内容转变成blob地址
            a.href = URL.createObjectURL(blob)
            a.download = name || '' // 下载文件的名字
            document.body.appendChild(a)
            a.click()
            // 在资源下载完成后 清除 占用的缓存资源
            window.URL.revokeObjectURL(a.href)
            document.body.removeChild(a)
          })
      }
    },
    // 获取往期版本
    getHistoryPlan() {
      let param = {
        planSource: this.planData.planSource,
        pageSize: this.pageData.size,
        page: this.pageData.current
      }
      this.tableLoading = true
      this.$api
        .GetHistoryPlan(param)
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.records
            this.pageData.total = res.data.total
          }
          this.tableLoading = false
        })
        .catch(() => {
          this.tableLoading = false
        })
    },
    viewFile() {
      window.open(this.$tools.imgUrlTranslation(this.planData.regulationsDoc[0].url), '_blank')
    },
    // 查看图片
    viewImage() {
      let img = this.planData.regulationsFlow
      if (!img.length) {
        this.$message.info('当前没有可预览的图片')
        return
      }
      this.iconPathList = [this.$tools.imgUrlTranslation(img[0].url)]
      this.showViewer = true
    },
    // 获取预案类型
    getAlarmSystem() {
      this.$api.getAlarmThirdSystemData().then((res) => {
        if (res.code == 200) {
          this.planTypeList = res.data
        }
      })
    },
    // 获取预案详情
    getPlanDetail() {
      this.$api.GetPlanDetail({ id: this.$route.query?.id }).then((res) => {
        if (res.code == 200) {
          let { regulationsFlow, regulationsDoc, warnEventList, noticeEventList, confirmEventList } = res.data
          res.data.regulationsFlow = JSON.parse(regulationsFlow)
          res.data.regulationsDoc = JSON.parse(regulationsDoc)
          this.planData = res.data
          if (this.$route.query?.planType != 0) return
          warnEventList.forEach((item) => {
            if (Object.keys(this.addEventData[item.stepType]).includes('warnEventList')) {
              this.addEventData[item.stepType].warnEventList.push(item)
            } else {
              this.addEventData[item.stepType].warnEventList = [item]
            }
          })
          noticeEventList.forEach((item) => {
            item.noticeWay = Number(item.noticeWay)
            item.noticeType = Number(item.noticeType)
            if (Object.keys(this.addEventData[item.stepType]).includes('noticeEventList')) {
              this.addEventData[item.stepType].noticeEventList.push(item)
            } else {
              this.addEventData[item.stepType].noticeEventList = [item]
            }
          })
          confirmEventList.forEach((item) => {
            if (Object.keys(this.addEventData[item.stepType]).includes('confirmEventList')) {
              this.addEventData[item.stepType].confirmEventList.push(item)
            } else {
              this.addEventData[item.stepType].confirmEventList = [item]
            }
          })
        }
      })
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getHistoryPlan()
    }
  }
}
</script>
<style lang="scss" scoped>
.form-content {
  height: 100%;
  overflow-y: auto;
  p {
    margin: 0px;
  }
  .form-title {
    height: 40px;
    line-height: 40px;
    background: #fff;
    border-bottom: 1px solid #dcdfe6;
    font-size: 15px;
    font-family: 'PingFang SC-Medium', 'PingFang SC';
    font-weight: 500;
    color: #121f3e;
    padding-left: 15px;
    i {
      width: 20px;
      cursor: pointer;
    }
  }
  .content-main {
    height: calc(100% - 40px);
    width: 100%;
    background: #fff;
    padding: 24px 0px 0px 0px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    ::v-deep(.el-tabs) {
      height: 100%;
      .el-tabs__nav-wrap {
        padding-left: 60px;
        &::after {
          display: none;
        }
      }
      .el-tabs__content {
        height: calc(100% - 40px);
        .el-tab-pane {
          height: 100%;
          overflow: auto;
        }
      }
    }
    .info-item {
      margin-top: 24px;
      display: flex;
      span {
        display: inline-block;
        font-size: 14px;
        // line-height: 14px;
      }
      .info-label {
        text-align: right;
        min-width: 140px;
        display: inline-block;
        font-weight: 400;
        color: #666666;
      }
      .info-value {
        display: inline-block;
        font-weight: 500;
        color: #333333;
        flex: 1;
        word-wrap: break-word;
        word-break: break-all;
        img {
          width: 100px;
          height: 100px;
          cursor: pointer;
        }
      }
    }
    .table-main {
      padding: 24px;
      height: 100%;
      width: 100%;
    }
  }
}
</style>
