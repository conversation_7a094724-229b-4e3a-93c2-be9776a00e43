<template>
  <PageContainer>
    <div slot="content" class="whole">
      <div class="left">
        <el-collapse v-model="activeNames">
          <el-collapse-item title="应用管理" name="1">
            <el-tree
              ref="tree"
              :data="treeData"
              :props="defaultProps"
              highlight-current
              :default-expand-all="true"
              node-key="menuId"
              :current-node-key="treeId"
              @node-click="treexz"
            ></el-tree>
          </el-collapse-item>
        </el-collapse>
      </div>
      <div v-if="!show" class="right">
        <div style="display: flex;">
          <el-input v-model.trim="menuName" placeholder="请输入菜单名称" class="ipt"></el-input>
          <el-select v-model="menuStatus" placeholder="请选择菜单状态" class="ipt">
            <el-option v-for="item in menuList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
          <el-button type="primary" @click="inquiry">查询</el-button>
          <el-button type="primary" plain @click="rest">重置</el-button>
          <el-button type="primary" @click="newly('new')">新增</el-button>
        </div>
        <div slot="content" style="height: 100%;">
          <div class="contentTable">
            <div class="contentTable-main table-content">
              <el-table
                v-loading="loading"
                :data="tableData"
                :height="tableHeight"
                :header-cell-style="{ 'text-align': 'center' }"
                tooltip-effect="dark"
                style="width: 100%; height: 100%; overflow-x: auto;"
                stripe
              >
                <!-- <el-table-column type="selection" width="55" align="center"> </el-table-column> -->
                <el-table-column type="index" label="序号" prop="" width="50" align="center">
                  <template slot-scope="scope">
                    <span>{{ (currentPage - 1) * pageSize + scope.$index + 1 }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="menuName" label="名称" align="center" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span style="cursor: pointer; color: #2841db;" @click="newly('details', scope.row.menuId)">{{ scope.row.menuName }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="parentName" label="所属模块" align="center" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="pathUrl" label="地址" align="center" min-width="200"> </el-table-column>
                <el-table-column prop="state" label="状态" align="center" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span v-if="scope.row.state == 0">已启用</span>
                    <span v-if="scope.row.state == 1">已停用</span>
                  </template>
                </el-table-column>
                <el-table-column prop="orderNum" label="排序" align="center" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="" label="操作" align="center" show-overflow-tooltip min-width="180">
                  <template v-if="scope.row.appFix != 0" slot-scope="scope">
                    <span style="cursor: pointer; color: red; padding: 0 5px;" @click="dele(scope.row)">删除</span>
                    <span style="cursor: pointer; padding: 0 5px;" @click="newly('edit', scope.row.menuId)">编辑</span>
                    <span v-if="scope.row.state == 0" style="cursor: pointer; color: #2841db; padding: 0 5px;" @click="switchState(scope.row)" >停用</span
                    >
                    <span v-if="scope.row.state == 1" style="cursor: pointer; color: #2841db; padding: 0 5px;" @click="switchState(scope.row)">启用</span
                    >
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </div>
      <addMenu
        v-if="show"
        class="right"
        style="padding: 0;"
        :type="type"
        :menuId="menuId"
        @child-event="parentEvent"
        @menuDiscreteness="menuDiscreteness"
      ></addMenu>
    </div>
  </PageContainer>
</template>

<script>
import addMenu from './addMenu.vue'
import tableListMixin from '@/mixins/tableListMixin.js'

export default {
  name: 'menuManagement',
  components: {
    addMenu
  },
  mixins: [tableListMixin],
  data() {
    return {
      activeNames: ['1'],
      loading: true,
      menuName: '',
      menuStatus: '',
      menuId: '',
      show: false,
      treeData: [],
      treeId: '', // 当前选中节点
      defaultProps: {
        children: 'children',
        label: 'menuName'
      },
      menuList: [
        {
          id: '0',
          name: '已启用'
        },
        {
          id: '1',
          name: '已停用'
        }
      ],
      currentPage: 1,
      pageSize: 15,
      tableData: []
    }
  },
  mounted() {
    this.getTreeList()
  },
  methods: {
    switchState(row) {
      let params = {
        id: JSON.stringify(row.menuId)
      }
      if (row.state == 0) {
        params.state = 1
      } else {
        params.state = 0
      }
      this.$api.removeOrDisuseMenu(params).then((res) => {
        if (res.code == 200) {
          this.$message({
            type: 'success',
            message: res.msg
          })
          this.getTableList()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    menuDiscreteness() {
      this.getTreeList()
      this.getTableList()
    },
    inquiry() {
      let params = {
        userId: this.$store.state.user.userInfo.userId,
        menuName: this.menuName,
        state: this.menuStatus,
        menuCategory: 1
      }
      this.$api.SysAppMenuList(params).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data
        }
      })
    },
    treexz(val) {
      this.treeId = val.menuId
      this.getTableList()
    },
    getTreeList() {
      this.$api
        .SysAppMenu({
          userId: this.$store.state.user.userInfo.userId,
          menuCategory: 1
        })
        .then((res) => {
          if (res.code == 200) {
            this.treeData = res.data
            this.treeId = this.treeId == '' ? res.data[0]?.menuId ?? '' : this.treeId
            this.$nextTick(() => {
              this.$refs.tree.setCurrentKey(this.treeId)
            })
            this.getTableList()
          }
        })
    },
    getTableList() {
      let params = {
        userId: this.$store.state.user.userInfo.userId,
        menuId: this.treeId,
        menuCategory: 1
      }
      this.$api.indexSelectApp(params).then((res) => {
        this.loading = false
        if (res.code == 200) {
          this.tableData = res.data
        }
      })
    },
    // 重置
    rest() {
      this.menuName = ''
      this.menuStatus = ''
      this.inquiry()
    },
    newly(type, menuId) {
      this.type = type
      this.menuId = menuId
      this.show = true
    },
    // 删除
    dele(row) {
      this.$confirm('删除后将无法恢复，是否确定删除?', '信息提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$api.remove({ menuId: row.menuId },  {'operation-type': 3, 'operation-name': row.menuName, 'operation-id': row.menuId}).then((res) => {
            if (res.code == 200) {
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
              this.getTreeList()
              this.getTableList()
            } else {
              this.$message.error(res.data)
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    parentEvent(data) {
      this.show = data
    }
  }
}
</script>

<style lang="scss" scoped>
.whole {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;

  .left {
    width: 250px;
    height: 100%;
    background-color: #fff;
    border-radius: 10px;
    padding-right: 5px;
    padding-left: 10px;
    overflow: hidden;

    ::v-deep .el-collapse {
      height: 100%;

      .el-collapse-item {
        height: 100%;

        .el-collapse-item__wrap {
          height: calc(100% - 55px);
          overflow: auto;
        }
      }
    }
  }

  .right {
    width: calc(100% - 260px);
    height: 100%;
    background-color: #fff;
    border-radius: 10px;
    padding: 10px;
  }

  .ipt {
    width: 200px;
    margin-right: 10px;
  }
}

::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: linear-gradient(to right, #d9e1f8, #fff);
  font-weight: bold;
}

.contentTable {
  height: calc(100% - 30px);
  background: #fff;
  border-radius: 4px;
  display: flex;
  padding: 10px 0 0;
  flex-direction: column;
  overflow: auto;

  .contentTable-main {
    flex: 1;
    overflow: auto;
  }

  .contentTable-footer {
    padding: 10px 0 0;
  }

  .alarmLevel {
    padding: 3px 6px;
    border-radius: 4px;
    color: #fff;
    line-height: 14px;
  }

  .alarmStatus {
    position: relative;
    display: inline-block;
    padding-left: 12px;

    .alarmStatusIcon {
      display: inline-block;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 8px;
      height: 8px;
      border-radius: 100%;
    }
  }

  .collectIcon {
    font-size: 16px;
    margin-right: 4px;
  }
}
</style>
