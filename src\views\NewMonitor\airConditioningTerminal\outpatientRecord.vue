<template>
    <PageContainer>
        <div slot="header">

            <div class="searchForm">
                <div class="search-box">
                    <el-input v-model="searchFrom.searchKeyword" clearable filterable placeholder="名称/编码" class="ml-16"
                        @blur="event => searchForm.searchKeyword = event.target.value.replace(/\s+/g, '')"></el-input>
                    <el-date-picker v-model="dataRange" type="date" placeholder="选择日期" value-format="yyyy-MM-dd"
                        class="ml-16" @change="dataRangeChange">
                    </el-date-picker>
                    <el-time-select placeholder="起始时间" v-model="startTime" :picker-options="{
                        start: '00:00',
                        step: '01:00',
                        end: '24:00'
                    }" @change="startTimeChange">
                    </el-time-select>
                    <el-time-select placeholder="结束时间" v-model="endTime" :picker-options="{
                        start: '00:00',
                        step: '01:00',
                        end: '24:00',
                        minTime: startTime
                    }" @change="endTimeChange">
                    </el-time-select>
                    <el-select v-model="searchFrom.registType" placeholder="请选择挂号方式" class="ml-16">
                        <el-option v-for="item in policeLevelList" :key="item.value" :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                    <div class="ml-16">
                        <el-button type="primary" plain @click="reset">重置</el-button>
                        <el-button type="primary" @click="search">查询</el-button>
                    </div>
                </div>
            </div>
            <div class="topalarm">
                <p v-for="(item, index) in countList" :key="index">
                    <span class="item-name">{{ item.name }} <i>{{ dataList[item.key] | formatSeconds }}</i></span>
                </p>
            </div>
        </div>
        <div slot="content" ref="contentRef" class="table-box">
            <div class="tableContainer">
                <TablePage ref="table" v-loading="tableLoading" :showPage="true" :tableColumn="tableColumn" height="330"
                    :data="tableData" :pageData="pageData" @pagination="paginationChange" :pageProps="pageProps" />
            </div>
        </div>
    </PageContainer>
</template>
<script lang="jsx">
import moment from 'moment'
export default {
    name: 'outpatientRecord',
    filters: {
        formatSeconds(seconds) {
            if (!seconds) return '---'
            const hours = Math.floor(seconds / 3600)
            const minutes = Math.floor((seconds % 3600) / 60)
            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`
        }
    },
    props: {
        factoryCode: {
            type: String,
            default: ''
        },
    },
    data() {
        return {
            tableLoading: false,
            searchFrom: {
                searchKeyword: '',//患者名称
                beginTime: '',//开始日期
                endTime: '',//结束日期
                registType: '', // 挂号方式
            }, // 搜索条件
            dataRange: moment(new Date()).format('YYYY-MM-DD'), // 时间范围
            startTime: '00:00',
            endTime: '24:00',
            policeLevelList: [{ value: 0, label: '现场号' }, { value: 1, label: '网约' }],
            tableColumn: [
                {
                    prop: '',
                    label: '序号',
                    formatter: (scope) => {
                        return (this.pageData.pageNo - 1) * this.pageData.pageSize + scope.$index + 1
                    },
                    width: 60
                },
                {
                    prop: 'caseCode',
                    label: '病例编码'
                },
                {
                    prop: 'patientName',
                    label: '患者名称'
                },

                {
                    prop: 'registType',
                    label: '挂号方式',
                    formatter: (row) => {
                        switch (row.row.registType) {
                            case 0:
                                return '现场号';
                            case 1:
                                return '网约';
                            default:
                                return '未知'; // 处理未定义的情况
                        }
                    }
                },
                {
                    prop: 'visitStatus',
                    label: '初复诊',
                    formatter: (row) => {
                        switch (row.row.visitStatus) {
                            case '0':
                                return '复诊';
                            case '1':
                                return '初诊';
                            default:
                                return '未知'; // 处理未定义的情况
                        }
                    }
                },
                {
                    prop: 'doctorName',
                    label: '主治医生'
                },
                {
                    prop: 'chairName',
                    label: '就诊椅位'
                },
                {
                    prop: 'visitBeginTime',
                    label: '就诊时间',
                },
            ],
            tableData: [],
            pageData: {
                pageNo: 1,
                pageSize: 15,
                total: 0
            },
            pageProps: {
                page: 'pageNo',
                pageSize: 'pageSize',
                total: 'total'
            },
            countList: [
                { name: '空闲时长', key: 'freeCount', },
                { name: '正常使用时长', key: 'normalCount', },
                { name: '异常占用时长', key: 'exceptionalCount', }
            ],
            dataList: {},
        }
    },
    mounted() {
        this.updateSearchTimes();
        this.getApplicationList()
        this.bjxxData()
    },
    methods: {
        updateSearchTimes() {
            this.searchFrom.beginTime = moment(new Date()).format('YYYY-MM-DD') + ' ' + this.startTime + ':00';
            this.searchFrom.endTime = moment(new Date()).format('YYYY-MM-DD') + ' ' + (this.endTime == '24:00' ? '23:59:59' : this.endTime + ':00');
        },
        dataRangeChange(val) {
            if (val) {
                this.startTime = '00:00'
                this.endTime = '24:00'
                this.searchFrom.beginTime = val + ' ' + this.startTime + ':00'
                this.searchFrom.endTime = val + ' ' + (this.endTime == '24:00' ? '23:59:59' : this.endTime + ':00')
            }
        },
        startTimeChange(val) {
            this.searchFrom.beginTime = this.searchFrom.beginTime.split(" ")[0] + ' ' + val + ':00'
        },
        endTimeChange(val) {
            this.searchFrom.endTime = this.searchFrom.endTime.split(" ")[0] + ' ' + (val == '24:00' ? '23:59:59' : val + ':00')
        },
        bjxxData() {
            let data = {
                monitorDeviceId: this.$route.query.id,
                dateType: "day",
                sTime: this.dataRange ? this.searchFrom.beginTime : '',
                eTime: this.dataRange ? this.searchFrom.endTime : ''
            }
            this.$api.getChairTotal(data).then((res) => {
                if (res.code === 200) {
                    this.dataList = res.result
                } else {
                    this.hasChart = false
                }
            })
        },
        // 获取就诊记录列表
        getApplicationList() {
            let param = {
                chairCode: this.$route.query.id,
                ...this.searchFrom,
                pageSize: this.pageData.pageSize,
                pageNo: this.pageData.pageNo
            }
            this.tableLoading = true
            this.$api
                .getPatientVisitRecordByPage(param)
                .then((res) => {
                    if (res.code == 200) {
                        this.tableData = res.data.records
                        this.pageData.total = res.data.total
                    }
                    this.tableLoading = false
                })
                .catch(() => {
                    this.tableLoading = false
                })
        },
        // 分页
        paginationChange(pagination) {
            Object.assign(this.pageData, pagination)
            this.getApplicationList()
        },
        // 重置查询
        reset() {
            Object.assign(this.searchFrom, {
                searchKeyword: '',//患者名称
                beginTime: '',//开始日期
                endTime: '',//结束日期
                registType: '', // 挂号方式
            })
            this.dataRange = moment(new Date()).format('YYYY-MM-DD')
            this.startTime = '00:00'
            this.endTime = '24:00'
            this.updateSearchTimes()
            this.getApplicationList()
            this.bjxxData()
        },
        // 查询
        search() {
            this.getApplicationList()
            this.bjxxData()
        },
    }
}
</script>
<style lang="scss" scoped>
.topalarm {
    display: flex;
    justify-content: space-between;
    gap: 15px;
    margin-left: -10px;
    margin-top: 5px;

    p {
        flex: 1;
        text-align: center;
        align-items: center;
        background: #faf9fc;
        color: #808081;
        padding: 10px 0;
        margin-bottom: 0;

        em {
            display: flex;
            justify-content: center;
            align-items: center;
            font-style: normal;
        }

        img {
            margin-right: 8px;
        }

        i {
            color: #333;
            display: block;
            font-weight: bold;
            font-size: 20px;
            font-style: normal;
        }

    }
}

.searchForm {
    display: flex;
    height: 50px;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin-left: -28px;

    .el-input {
        width: 200px;
        margin-left: 16px;
    }

    >div {
        margin-right: 20px;
    }
}

.search-box {
    display: flex;
    align-items: center;
}

.container-content>div {
    height: 100%;
    margin-top: 16px;
}

.el-table {
    margin-bottom: 12px;
}

::v-deep .el-table__row:hover {
    cursor: pointer;
}

.table-box {
    background-color: #fff;
    height: 100%;

    .tableContainer {
        height: 100%;

        .el-table {
            height: calc(100% - 96px) !important;
        }
    }
}

.diaContent {
    width: 100%;
    max-height: 550px !important;
    overflow: auto;
    background-color: #fff !important;

    .el-input,
    .el-select,
    .el-cascader {
        width: 300px;
    }
}

.form-item {
    display: inline-block;
    margin-right: 20px;
}

.inputWidth {
    width: 820px;
}

.ml-16 {
    margin-left: 16px;
}

.dialog .el-dialog {
    width: 60% !important;
}
</style>