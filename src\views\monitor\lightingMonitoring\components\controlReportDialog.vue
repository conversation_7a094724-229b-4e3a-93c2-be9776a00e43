<template>
  <el-dialog v-if="controlReportDialogShow" title="控制报表" :visible.sync="controlReportDialogShow" custom-class="control-dialog" :before-close="closeDialog">
    <div class="timeline_content">
      <el-timeline>
        <el-timeline-item v-for="(item, index) in [dialogData]" :key="index" placement="top">
          <div class="time_row" @click="collectEvent('item', index)">
            <span>{{ item.date }}</span>
            <span>执行反馈</span><span>总共控制：{{ item.total }}条</span>
            <span>执行成功：{{ item.isSuccess }}条</span>
            <span>执行失败：{{ item.isFail }}条</span>
            <i :ref="'itemright' + index" style="display: inline-block;" class="el-icon-arrow-right title-icon"></i>
            <i v-show="false" :ref="'itemdown' + index" class="el-icon-arrow-down title-icon"></i>
          </div>
          <TransitionHeight :ref="'item' + index" :heigtRef="'heigtBox' + index">
            <div class="detail_ul">
              <div v-for="(loop, idx) in item.returnMap" :key="idx">
                <span>{{ loop.outPutName }}</span>
                <span>{{ operationList[loop.forceSwitch ?? loop.outputStatus] }}</span>
                <span><i :class="loop.code == 200 ? 'el-icon-circle-check' : 'el-icon-warning-outline'" :style="{ color: loop.code == 200 ? '#34b253' : '#ff4848' }"></i></span>
                <span v-if="loop.code == 200" style="float: right; padding: 0 40px; font-weight: 600;">成功</span>
                <el-button v-else slot="reference" type="primary" style="float: right;" @click="resend(loop)">重新发送</el-button>
              </div>
              <!-- <div>
                  <span>回路名称1</span>
                  <span>开启</span>
                  <span><i class="el-icon-circle-check" style="color: #34b253"></i></span>
                  <span style="float: right;padding: 0 40px; font-weight: 600;">成功</span>
                </div> -->
            </div>
          </TransitionHeight>
        </el-timeline-item>
      </el-timeline>
    </div>
  </el-dialog>
</template>
<script>
import TransitionHeight from '@/components/transitionHeight/transitionHeight.vue'
import moment from 'moment'
export default {
  name: 'controlReportDialog',
  components: {
    TransitionHeight
  },
  props: {
    controlReportDialogShow: {
      type: Boolean,
      default: false
    },
    dialogData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      controlLineList: [
        {
          time: '11:42:12',
          sumNum: 5,
          successNum: 3,
          errorNum: 2
        },
        {
          time: '11:45:12',
          sumNum: 8,
          successNum: 5,
          errorNum: 3
        },
        {
          time: '12:22:12',
          sumNum: 4,
          successNum: 3,
          errorNum: 1
        }
      ],
      operationList: ['关闭', '开启', '', '强开', '强关']
    }
  },
  mounted() {
    // console.log(this.dialogData);
    // this.dialogData.date = moment(this.dialogData.date).format('HH:mm:ss')
  },
  methods: {
    // 展开关闭事件
    collectEvent(box, i) {
      if (this.$refs[box + 'right' + i][0].style.display === 'inline-block') {
        this.$refs[box + 'right' + i][0].style.display = 'none'
        this.$refs[box + 'down' + i][0].style.display = 'inline-block'
        this.$refs[box + i][0].show = true
      } else {
        this.$refs[box + 'right' + i][0].style.display = 'inline-block'
        this.$refs[box + 'down' + i][0].style.display = 'none'
        this.$refs[box + i][0].show = false
      }
    },
    // 重新发送
    resend(item) {
      let type = item.forceSwitch ?? item.outputStatus
      let params = {
        type: 1,
        actuatorId: item.actuatorId,
        outputNum: item.outPutNum,
        outputStatus: type == 3 ? '1' : type == 4 ? '0' : type == 5 ? null : type,
        forceSwitch: type == 1 || type == 0 ? null : type
      }
      this.$api.lightOpenOrClose(params).then((res) => {
        console.log(res)
        if (res.code == 200) {
          if (res.data.code == 200) {
            this.$message.success(res.data.returnMap[0].outputStatus)
          } else {
            this.$message.warning(res.data.returnMap[0].outputStatus)
          }
          // this.searchFromChange()
        } else {
          this.searchFromChange()
        }
        this.dialogData.returnMap.map((e) => {
          if (e.actuatorId == params.actuatorId && e.outPutNum == params.outPutNum) {
            e.code = res.code
          }
        })
      })
    },
    closeDialog() {
      this.$emit('closeControlDialog')
    }
  }
}
</script>
<style src="@/assets/styles/sinomis-ui.css" scoped></style>
<style lang="scss" scoped>
.control-dialog {
  width: 50% !important;
  height: 70% !important;
  min-width: 562px !important;
  min-height: 376px !important;

  .timeline_content {
    padding: 30px 50px;

    .el-timeline-item__timestamp {
      // font-size: 16px;
      display: none;
    }

    .time_row {
      height: 30px;
      line-height: 30px;
      cursor: pointer;

      span {
        margin: 0 15px;
        font-size: 16px;
      }

      span:nth-child(2) {
        font-weight: 600;
      }

      i {
        font-size: 20px;
        float: right;
        color: #5188fc;
      }
    }

    .detail_ul {
      margin-top: 15px;

      > div {
        height: 45px;
        line-height: 45px;
        padding: 0 10px;
        font-size: 16px;

        span {
          margin: 0 15px;

          i {
            font-size: 20px;
          }
        }
      }
    }
  }
}
</style>
