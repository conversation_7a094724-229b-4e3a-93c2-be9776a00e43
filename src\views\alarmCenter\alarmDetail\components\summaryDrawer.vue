<template>
  <el-drawer :visible="visible" title="总结分析" :before-close="handleClose">
    <div class="drawer_content">
      <el-form ref="ruleForm" :model="form" label-width="120px" :rules="rules">
        <el-form-item label="总结分析：" prop="summaryAnalysis">
          <el-input v-model="form.summaryAnalysis" type="textarea" placeholder="请输入总结分析。" rows="3"></el-input>
        </el-form-item>
        <el-form-item label="分析文件：">
          <el-upload
            action=""
            :file-list="fileList"
            multiple
            drag
            :accept="uploadAcceptDict['annexGeneralized'].type"
            :http-request="httpRequset"
            :on-remove="handleRemove"
            :before-upload="beforeUpload"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div slot="tip" class="el-upload__tip">单个文件最大{{ uploadAcceptDict['annexGeneralized'].fileSize }}M，可上传多个文件</div>
            <el-button slot="tip" class="el-upload__tip" size="small" icon="el-icon-full-screen" @click="scanUpload">扫码上传</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item label="院内参与人员：">
          <el-tag v-for="(item, index) in userList" :key="item.id" closable type="info" style="margin-right: 5px" @close="delUser(index)">{{ item.staffName }}</el-tag>
          <el-button type="primary" icon="el-icon-plus" @click="chooseUser">选择成员</el-button>
        </el-form-item>
        <el-form-item label="其他参与人员：">
          <el-input v-model="form.otherPerson" type="textarea" placeholder="请输入其他参与人员。" rows="3"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <div class="drawer_footer">
      <el-button plain @click="handleClose">取消</el-button>
      <el-button type="primary" @click="submit(0)">提交</el-button>
    </div>
    <selectPersDialog v-if="userVisible" selectMode="1" :visible="userVisible" :modal="false" @updateVisible="closeDialog" @advancedSearchFn="selectPersChange" />
    <el-dialog
      v-if="scanVisible"
      v-dialogDrag
      :modal="false"
      :close-on-click-modal="false"
      title="扫码上传"
      width="30%"
      :visible="scanVisible"
      custom-class="model-dialog"
      :before-close="closeScanDialog"
    >
      <div ref="qrCode" class="qrCode">
        <img :src="qrCodeImg" alt="" />
        <p class="DialogUploadCode__tip">
          请移动设备与客户端保持同一网段 <br />
          支持文件格式：jpg、png、pdf、doc、txt、xls
        </p>
      </div>
    </el-dialog>
  </el-drawer>
</template>
<script>
import { uploadAcceptDict } from '@/util/dict.js'
export default {
  components: {
    SelectPersDialog: () => import('@/views/alarmCenter/linkageConfiguration/components/dialogConfig.vue')
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    selectItems: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      uploadAcceptDict,
      form: {
        summaryAnalysis: '',
        fileUrl: '',
        hospitalPerson: '',
        otherPerson: ''
      },
      rules: {},
      fileList: [],
      userVisible: false,
      userList: [],
      qrCodeImg: '',
      webSocket: null,
      scanVisible: false
    }
  },
  watch: {
    scanVisible(val) {
      if (!val) {
        if (this.webSocket) {
          this.webSocket.close()
          this.webSocket = null
        }
        console.log(this.webSocket)
      }
    }
  },
  methods: {
    chooseUser() {
      this.userVisible = true
    },
    closeDialog() {
      this.userVisible = false
    },
    // 选择人员事件
    selectPersChange(data) {
      this.userList = this.userList.concat(data)
      let arr = []
      data.forEach((el) => {
        if (el.staffHumber) {
          arr.push(`${el.staffName}(${el.staffHumber})`)
        } else {
          arr.push(el.staffName)
        }
      })
      this.form.hospitalPerson = arr.join('，')
    },
    delUser(index) {
      this.userList.splice(index, 1)
    },
    beforeUpload(file) {
      const fileSize = this.uploadAcceptDict['annexGeneralized'].fileSize
      const isLt5M = file.size / 1024 / 1024 < fileSize
      if (!isLt5M) {
        this.$message.error(`上传文件大小不能超过 ${fileSize}MB!`)
        return false
      }
      const extension = file.name.substring(file.name.lastIndexOf('.'))
      const isFormat = this.uploadAcceptDict['annexGeneralized'].type.includes(extension)
      if (!isFormat) {
        this.$message.error(`上传图片只能是 ${this.uploadAcceptDict['annexGeneralized'].type}格式!`)
        return false
      }
    },
    httpRequset(file) {
      const params = new FormData()
      params.append('file', file.file)
      this.$api.uploadTask(params).then((res) => {
        if (res.code === '200') {
          this.$message({
            message: '上传成功',
            type: 'success'
          })
          this.fileList.push({
            name: res.data.name,
            url: res.data.picUrl
          })
          this.form.fileUrl = JSON.stringify(this.fileList)
        } else {
          this.$message({
            message: res.message,
            type: 'warning'
          })
        }
      })
    },
    handleRemove(file, fileList) {
      this.fileList = fileList.map((el) => {
        return {
          name: el.name,
          url: el.url
        }
      })
      this.form.fileUrl = JSON.stringify(this.fileList)
    },
    submit() {
      let params = {
        ...this.form,
        limAlarmId: this.selectItems.map((el) => el.alarmId).toString(),
        createName: this.$store.state.user.userInfo.user.staffName,
        operationSource: 0
      }
      this.$api.summarySave(params).then((res) => {
        if (res.code == 200) {
          this.$message.success('处理成功')
          this.handleClose()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    handleClose() {
      this.form = {
        summaryAnalysis: '',
        fileUrl: '',
        hospitalPerson: '',
        otherPerson: ''
      }
      this.fileList = []
      this.userList = []
      this.$emit('close')
    },
    // 扫码上传
    scanUpload() {
      let params = {
        alarmId: this.selectItems.map((el) => el.alarmId).toString()
      }
      this.$api.createScan(params).then((res) => {
        if (res.code == 200) {
          this.qrCodeImg = `data:image/png;base64,${res.data}`
        }
        if (!this.webSocket) {
          this.webSocket = this.fileUploadSocket()
          this.webSocket.onmessage = this.onUploadSocketMessage.bind(this)
          this.webSocket.onerror = () => {
            this.$message.error('二维码上传交互服务异常')
          }
        }
        this.scanVisible = true
      })
    },
    /** 报警详情，二维码扫码上传文件后消息 */
    fileUploadSocket() {
      return new WebSocket(`${__PATH.WS_ALARM_URL}alarmServer/wartime/${this.selectItems[0].alarmId}`)
    },
    onUploadSocketMessage(msg) {
      let arr = JSON.parse(msg.data)
      this.fileList = this.fileList.concat(
        arr.data.map((el) => {
          return {
            name: el.operationName,
            url: el.operationUrl
          }
        })
      )
      this.form.fileUrl = JSON.stringify(this.fileList)
    },
    closeScanDialog() {
      this.scanVisible = false
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-drawer__header {
  padding: 0 16px;
  height: 56px;
  border-bottom: 1px solid #ccc;
  color: #333;
  font-weight: 500;
  font-size: 16px;
  margin: 0;
}
.drawer_content {
  padding: 16px;
  width: 100%;
  height: calc(100% - 56px);
  overflow: auto;
}
.drawer_footer {
  padding: 0 16px;
  height: 56px;
  border-top: 1px solid #ccc;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.qrCode {
  width: 100%;
  height: 400px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #fff;
  flex-direction: column;
}
</style>
