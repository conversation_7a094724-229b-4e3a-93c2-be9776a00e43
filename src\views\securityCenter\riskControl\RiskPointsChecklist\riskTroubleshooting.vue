<template>
  <div style="height: 100%;">
    <div class="content_box">
      <el-form ref="formInline" class="dialog-form" :model="formInline" :inline="true" :rules="rules" label-position="right" label-width="110px">
        <el-form-item label="风险点名称：" prop="riskName">
          <span>{{ formInline.riskName }}</span>
        </el-form-item>
        <br />
        <el-form-item label="管控状态：" prop="status">
          <div v-if="!disabled">
            <el-radio v-model="formInline.status" label="0">启用</el-radio>
            <el-radio v-model="formInline.status" label="1">禁用</el-radio>
          </div>
          <span v-else>{{ formInline.status == '0' ? '启用' : '禁用' }}</span>
        </el-form-item>
        <br />
        <el-form-item label="排查类型：" prop="investigationType">
          <el-checkbox-group v-if="!disabled" v-model="formInline.investigationType" :min="1">
            <el-checkbox label="1">匿名用户</el-checkbox>
            <el-checkbox label="2">系统用户</el-checkbox>
          </el-checkbox-group>
          <span v-else>{{ getName(formInline.investigationType) }}</span>
        </el-form-item>
        <template>
          <div style="margin-bottom: 20px;">
            <div class="content-table">
              <div class="table-title">
                <span class="title"> <i></i> 排查内容 </span>
                <span class="line"></span>
              </div>
              <div class="inspection-content">
                <div class="content-block">
                  <el-form
                    ref="formInline.contentArray"
                    class="dialog-form"
                    :model="formInline.contentArray"
                    :inline="true"
                    :rules="rules"
                    label-position="right"
                    label-width="110px"
                  >
                    <div v-for="(item, index) in contentArray" :key="index" class="content-line">
                      <div v-if="disabled" style="height: 24px; margin-left: 20px;">
                        <img :src="icon" alt="" />
                        内容{{ index + 1 }}
                      </div>
                      <el-form-item label="排查内容" prop="investigationContent">
                        <el-input
                          v-if="!disabled"
                          v-model="item.investigationContent"
                          type="textarea"
                          :rows="3"
                          class="project-textarea width_lengthen"
                          placeholder="请输入排查内容"
                          show-word-limit
                          maxlength="500"
                        ></el-input>
                        <span v-else class="width_lengthen" style="display: inline-block;">{{ item.investigationContent }}</span>
                      </el-form-item>
                      <el-form-item label="标准项" prop="standard">
                        <el-input
                          v-if="!disabled"
                          v-model="item.standard"
                          maxlength="500"
                          show-word-limit
                          placeholder="请输入标准项"
                          type="textarea"
                          :rows="3"
                          class="project-textarea width_lengthen"
                        ></el-input>
                        <span v-else class="width_lengthen" style="display: inline-block;">{{ item.standard }}</span>
                      </el-form-item>
                      <el-form-item label="参考依据" prop="referenceResources">
                        <el-input
                          v-if="!disabled"
                          v-model="item.referenceResources"
                          maxlength="500"
                          show-word-limit
                          placeholder="请输入参考依据"
                          type="textarea"
                          :rows="3"
                          class="project-textarea width_lengthen"
                        ></el-input>
                        <span v-else class="width_lengthen" style="display: inline-block;">{{ item.referenceResources }}</span>
                      </el-form-item>
                      <br />
                      <div v-if="!disabled" class="termContent-input">
                        <span class="termContent-button button-detele" style="color: #fc2c61;" @click="delTroubleshoot(index)">
                          <i class="el-icon-delete"></i>
                          <span>删除</span>
                        </span>
                        <span class="termContent-button button-add" @click="addTroubleshoot">
                          <i class="el-icon-plus"></i>
                          <span>添加组</span>
                        </span>
                      </div>
                    </div>
                  </el-form>
                </div>
              </div>
            </div>
          </div>
        </template>
      </el-form>
    </div>
    <div class="bottomBar">
      <div class="bottomWrap">
        <el-button v-if="!disabled" type="primary" @click="complete">确定</el-button>
        <el-button type="primary" plain @click="close">关闭</el-button>
      </div>
    </div>
  </div>
</template>
<script>
import icon from '@/assets/images/inspectionCenter/ic-document.png'
export default {
  data() {
    return {
      icon,
      loading: false,
      formInline: {
        riskName: '',
        status: '1',
        investigationType: ['1', '2']
      },
      rules: {
        investigationStandard: [{ required: true, message: '请选择排查标准', trigger: 'blur' }],
        status: [{ required: true, message: '请选择管控状态', trigger: 'blur' }]
      },
      title: '',
      contentArray: [
        {
          investigationContent: '',
          standard: '',
          referenceResources: ''
        }
      ],
      disabled: false,
      row: {},
      contentInfo: {}
    }
  },
  // activated() {
  //   this.row = JSON.parse(sessionStorage.getItem('RiskPointsChecklistRow'))
  //   this.disabled = this.$route.query.type == 'check'
  //   this.$route.query.type != 'add' ? this.getRiskInvestigationDetail() : ''
  //   this.formInline.riskName = this.row.riskName
  // },
  created() {
    this.disabled = this.$route.query.type == 'check'
    this.row = JSON.parse(sessionStorage.getItem('RiskPointsChecklistRow'))
    this.formInline.riskName = this.row.riskName
  },
  mounted() {
    this.$route.query.type != 'add' ? this.getRiskInvestigationDetail() : ''
  },
  methods: {
    // 获取排查name
    getName(arr) {
      let newArr = []
      arr.forEach((item) => {
        if (item == '1') {
          newArr.push('匿名用户')
        } else if (item == '2') {
          newArr.push('系统用户')
        }
      })
      return newArr.length ? newArr.toString() : ''
    },
    // 提交
    complete() {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          this.loading = true
          let data = {
            ...this.formInline,
            riskId: this.row.id,
            riskName: this.row.riskName,
            investigationType: this.formInline.investigationType.join(','),
            contentArray: JSON.stringify(this.contentArray)
          }
          let url = this.$route.query.type == 'add' ? 'ipsmSaveRiskInvestigation' : 'ipsmUpdateRiskInvestigation'
          if (this.$route.query.type == 'edit') {
            data.riskInvestigationId = this.contentInfo.riskInvestigationId
            data.createPersonId = this.contentInfo.createPersonId
            data.createPersonName = this.contentInfo.createPersonName
          }
          this.$api[url](data).then((res) => {
            this.loading = false
            if (res.code == 200) {
              this.contentArray = [
                {
                  investigationContent: '',
                  standard: '',
                  referenceResources: ''
                }
              ]
              this.$router.go(-1)
              this.$message.success(res.message)
            } else {
              this.$message.error(res.message)
            }
          })
        }
      })
    },
    // 获取排查清单详情
    getRiskInvestigationDetail() {
      this.loading = true
      this.$api.ipsmGetRiskInvestigationDetail({ riskId: this.row.id }).then((res) => {
        if (res.code == 200) {
          this.contentInfo = res.data.riskInvestigationDetail
          this.formInline.riskName = this.contentInfo.riskName
          this.formInline.status = this.contentInfo.status
          this.formInline.investigationType = this.contentInfo.investigationType.split(',')
          this.contentArray = this.contentInfo.investigationContents
        } else {
          this.formInline.status = ''
          this.formInline.investigationType = []
          // this.contentArray=[]
          this.$message.error(res.message)
        }
        this.loading = false
      })
    },
    // 添加排查内容
    addTroubleshoot() {
      this.contentArray.push({
        investigationContent: '',
        standard: '',
        referenceResources: ''
      })
    },
    // 删除排查内容
    delTroubleshoot(i) {
      this.contentArray.splice(i, 1)
      if (this.contentArray.length == 0) {
        this.contentArray = [
          {
            investigationContent: '',
            standard: '',
            referenceResources: ''
          }
        ]
      }
    },
    // 关闭当前页面
    close() {
      this.$refs['formInline'].resetFields()
      this.contentArray = [
        {
          investigationContent: '',
          standard: '',
          referenceResources: ''
        }
      ]
      this.$router.go(-1)
    }
  }
}
</script>
<style lang="scss" scoped>
.termContent-button {
  height: 42px;
  line-height: 42px;
  color: #5188fc;
  margin-right: 20px;
  cursor: pointer;
}

.bottomBar {
  height: 52px;
  width: 100%;
  background-color: #fff;
  display: flex;
  justify-content: right;
  align-items: center;

  .bottomWrap {
    padding: 0 16px;
  }
}

.content_box {
  height: calc(100% - 70px);
  overflow-y: auto;
  margin: 15px;
  padding: 20px;
  background: #fff;
  display: flex;
  flex-direction: column;
}

.width_lengthen {
  width: 766px;
}

.content-table .table-title {
  .title {
    width: 80px;
    padding: 0;
    font-size: 14px;
    color: #606266;

    i {
      display: inline-block;
      width: 8px;
      height: 16px;
      border-radius: 0 8px 8px 0;
      background: #3562db;
      margin-right: 8px;
      position: relative;
      top: 2px;
    }
  }

  .line {
    display: inline-block;
    width: calc(100% - 85px);
    height: 1px;
    border-top: 1px dashed #dcdfe6;
    position: relative;
    top: -2px;
    left: 2px;
  }
}
</style>
