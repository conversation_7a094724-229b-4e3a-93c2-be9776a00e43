<!--
 * @Author: hedd
 * @Date: 2023-02-27 14:35:20
 * @LastEditTime: 2025-06-12 15:00:48
 * @FilePath: \ihcrs_pc\src\views\operationPort\spaceManage\operationsManage\commonOperations.vue
 * @Description:
-->
<template>
  <PageContainer>
    <div slot="header" class="control-btn-header">
      <span class="component-name">{{ selectComponent.componentName }}</span>
      <div v-if="selectComponent.componentRouter === 'energyOperations'" class="energy-tabs">
        <div v-for="(item, index) in energyTabList" :key="index" :class="{ 'active-energy-tab': item.value === energyModel }" @click="changeEnergyTabs(item)">
          {{ item.label }}
        </div>
      </div>
      <div v-else>
        <div v-if="editDashExample" class="batch-control">
          <el-button type="primary" plain @click="cancelStaging">取消</el-button>
          <el-button type="primary" @click="saveStaging">保存</el-button>
        </div>
        <div v-else>
          <el-radio-group v-model="pageModel" size="mini">
            <el-radio-button label="grap">图形</el-radio-button>
            <el-radio-button label="list">列表</el-radio-button>
          </el-radio-group>
        </div>
      </div>
    </div>
    <div slot="content" class="staging-content">
      <div v-if="selectComponent.componentRouter === 'energyOperations'" style="width: 100%; height: 100%">
        <energyOperations :energyModel="energyModel"></energyOperations>
      </div>
      <div v-else style="width: 100%; height: 100%" class="scrollbar-transparent">
        <dashboard v-if="pageModel === 'grap' && dashExampleShow" id="dashExample" :key="Math.random()">
          <dash-layout v-bind="dlayout" :debug="false">
            <component
              :is="selectComponent.componentRouter"
              :ref="selectComponent.componentRouter"
              :componentData="selectComponent"
              :dlayoutItems="dlayout.items"
              @resizeEnd="resizeEnd"
              @all-more-Oper="allMoreOper"
              @toList="toList"
            ></component>
          </dash-layout>
        </dashboard>
        <div v-if="pageModel === 'list'" class="data-Manage">
          <dataManage :componentData="selectComponent" :assetParmas="assetParmas"></dataManage>
        </div>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import { Dashboard, DashLayout } from 'vue-responsive-dash'
export default {
  name: 'commonOperations',
  components: {
    Dashboard,
    DashLayout,
    spaceOperations: () => import('./spaceOperations'),
    iomsOperations: () => import('./iomsOperations'),
    dataManage: () => import('../dataManage'),
    dangerOperations: () => import('./dangerOperations'),
    wasteOperations: () => import('./wasteOperations'),
    riskOperations: () => import('./riskOperations'),
    assetOperations: () => import('./assetOperations'),
    assetOperationsDM: () => import('./assetOperationsDM'),
    energyOperations: () => import('./energyOperations')
  },
  beforeRouteEnter(to, from, next) {
    const names = ['spaceLedger', 'Details', 'addRisk']
    next((vm) => {
      if (names.includes(from.name) && !to.query.pageModel) {
        vm.pageModel = 'list'
      }
    })
  },
  data() {
    return {
      pageModel: this.$route.query.pageModel || 'grap',
      energyModel: 'energyOverview',
      energyTabList: [
        {
          label: '综合能耗',
          value: 'energyOverview'
        },
        {
          label: '水能耗',
          value: 'waterOverview'
        },
        {
          label: '电能耗',
          value: 'powerOverview'
        },
        {
          label: '冷热量能耗',
          value: 'coldEnergyMenu'
        }
      ],
      componentsList: Object.freeze([
        {
          componentName: '空间',
          componentRouter: 'spaceOperations',
          menuType: 1,
          dlayoutItems: [
            { id: 'personalInfo', x: 0, y: 0, width: 24, height: 3 },
            { id: 'msgReminder', x: 0, y: 9, width: 12, height: 6 },
            { id: 'todoItems', x: 12, y: 9, width: 12, height: 6 },
            { id: 'workOderType', x: 0, y: 3, width: 8, height: 6 },
            { id: 'warnManageTable', x: 8, y: 3, width: 16, height: 6 }
          ]
        },
        {
          componentName: '工单',
          componentRouter: 'iomsOperations',
          menuType: 2,
          dlayoutItems: [
            { id: 'serviceOverview', x: 0, y: 5, width: 8, height: 5 },
            { id: 'last6months', x: 8, y: 0, width: 9, height: 5 },
            { id: 'orderOverview', x: 8, y: 5, width: 16, height: 5 },
            { id: 'annualOrderTrend', x: 8, y: 10, width: 16, height: 5 },
            { id: 'itemsTop10', x: 17, y: 0, width: 7, height: 5 },
            { id: 'departTop5', x: 0, y: 10, width: 8, height: 5 },
            { id: 'reportBuildingTop5', x: 0, y: 15, width: 8, height: 5 },
            { id: 'workMonthComparison', x: 8, y: 15, width: 8, height: 5 },
            { id: 'groupWorkOrder', x: 16, y: 15, width: 8, height: 5 },
            { id: 'costOfConsumables', x: 0, y: 20, width: 8, height: 5 },
            { id: 'serviceEfficiency', x: 0, y: 0, width: 8, height: 5 }
          ]
        },
        {
          componentName: '医废',
          componentRouter: 'wasteOperations',
          menuType: 6,
          dlayoutItems: [
            { id: 'wasteTopLeft', x: 0, y: 0, width: 12, height: 6 },
            { id: 'wasteTopCenter', x: 12, y: 0, width: 6, height: 6 },
            { id: 'wasteTopRight', x: 18, y: 0, width: 6, height: 6 },
            { id: 'wasteBottomLeft', x: 0, y: 6, width: 12, height: 6 },
            { id: 'wasteBottomRight', x: 12, y: 6, width: 12, height: 6 }
          ]
        },
        {
          componentName: '隐患',
          componentRouter: 'dangerOperations',
          menuType: 3,
          dlayoutItems: [
            { id: 'dangerStatus', x: 0, y: 0, width: 8, height: 5, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
            { id: 'dangerType', x: 8, y: 0, width: 9, height: 5, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
            { id: 'dangerTrend', x: 8, y: 5, width: 16, height: 5, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
            { id: 'realTime', x: 0, y: 10, width: 24, height: 6, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
            { id: 'overview', x: 17, y: 0, width: 7, height: 5, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
            { id: 'departmentDanger', x: 0, y: 5, width: 8, height: 5, dragAllowFrom: '.drag_class', resizable: true, draggable: true }
          ]
        },
        {
          componentName: '风险',
          componentRouter: 'riskOperations',
          menuType: 4,
          dlayoutItems: [
            { id: '1', name: '风险等级分析', x: 0, y: 0, width: 24, height: 3, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
            { id: '2', name: '风险类型分析', x: 0, y: 3, width: 10, height: 6, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
            { id: '3', name: '风险巡查任务分析', x: 10, y: 3, width: 14, height: 6, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
            { id: '4', name: '风险部门分布', x: 0, y: 9, width: 12, height: 6, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
            { id: '5', name: '科室任务巡检分析', x: 12, y: 9, width: 12, height: 6, dragAllowFrom: '.drag_class', resizable: true, draggable: true }
          ]
        },
        {
          componentName: '资产',
          componentRouter: 'assetOperations',
          menuType: 5,
          dlayoutItems: [
            { id: '1', name: '资产总览', x: 0, y: 0, width: 8, height: 7, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
            { id: '2', name: '工作日历', x: 8, y: 0, width: 16, height: 7, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
            { id: '3', name: '专业类别分析', x: 0, y: 7, width: 8, height: 5, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
            { id: '4', name: '资产使用年限分析', x: 8, y: 7, width: 8, height: 5, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
            { id: '5', name: '资产巡检分析', x: 16, y: 7, width: 8, height: 5, dragAllowFrom: '.drag_class', resizable: true, draggable: true }
          ]
        },
        {
          componentName: '资产',
          componentRouter: 'assetOperationsDM',
          menuType: 8, //  世纪坛资产为8   其他为5
          dlayoutItems: [
            { id: '1', name: '资产总览', x: 0, y: 0, width: 16, height: 4, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
            { id: '2', name: '资产使用年限分析', x: 16, y: 0, width: 8, height: 4, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
            { id: '3', name: '资产状态分析', x: 0, y: 4, width: 16, height: 4, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
            { id: '4', name: '资产归口部门分析', x: 16, y: 4, width: 8, height: 4, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
            { id: '5', name: '资产巡检分析', x: 0, y: 8, width: 24, height: 4, dragAllowFrom: '.drag_class', resizable: true, draggable: true }
          ]
        },
        {
          componentName: '能源能耗',
          componentRouter: 'energyOperations'
        }
      ]),
      dlayout:
        // x=> 4 8 12 小中大
        {
          breakpoint: 'xs',
          numberOfCols: 24,
          items: []
        },
      dashExampleShow: false,
      editDashExample: false,
      assetParmas: {}
    }
  },
  computed: {
    selectComponent() {
      let componentRouter = this.$route?.name ?? 'spaceOperations'
      // 世纪坛医院访问世纪坛资产
      if (componentRouter === 'assetOperations' && __PATH.VUE_APP_HOSPITAL_NODE_ENV === 'bjsjtyy') {
        // if (componentRouter === 'assetOperations') {
        componentRouter = 'assetOperationsDM'
      }
      return this.componentsList.find((e) => e.componentRouter === componentRouter)
    }
  },
  watch: {
    pageModel(val) {
      if (val != 'list') {
        this.assetParmas = {}
      }
    }
    // dashExampleShow(val) {
    //   console.log('dashExampleShow', val, this.pageModel)
    // }
  },
  mounted() {
    this.getDragManageList()
  },
  methods: {
    toList(params) {
      this.assetParmas = params
      this.pageModel = 'list'
    },
    // 获取可拖拽列表
    getDragManageList() {
      const params = {
        userId: this.$store.state.user.userInfo.user.staffId,
        menuType: this.selectComponent.menuType
      }
      this.$api.getWorktopManageList(params).then((res) => {
        if (res.code == 200) {
          const data = res.data
          let componentItems = []
          // 有接口数据取接口数据 无接口数据取字典默认配置数据
          if (data.length) {
            componentItems = data
          } else {
            componentItems = this.selectComponent.dlayoutItems
          }
          // 遍历增加dragAllowFrom、resizable、draggable字段
          const items = componentItems.map((item) => {
            return {
              id: item.id,
              x: item.x,
              y: item.y,
              width: item.width,
              height: item.height,
              dragAllowFrom: '.drag_class',
              resizable: false,
              draggable: false
            }
          })
          this.dashExampleShow = false
          this.$nextTick(() => {
            this.dashExampleShow = true
            this.dlayout.items = items
          })
        }
      })
    },
    // 缩放结束事件
    resizeEnd(val, item) {
      this.$refs[item].echartsResize()
    },
    changeEnergyTabs(item) {
      this.energyModel = item.value
    },
    // 取消编辑工作台模块
    cancelStaging() {
      this.editDashExample = false
      this.getDragManageList()
    },
    // 保存工作台模块
    saveStaging() {
      console.log(this.dlayout.items)
      const items = this.dlayout.items
      if (!items.length) {
        return this.$message.warning('无可配置模块')
      }
      const jsonList = items.map((e) => {
        return {
          id: e.id,
          x: e.x,
          y: e.y,
          width: e.width,
          height: e.height
        }
      })
      const params = {
        jsonList: JSON.stringify(jsonList),
        userId: this.$store.state.user.userInfo.user.staffId,
        menuType: this.selectComponent.menuType
      }
      console.log('save提交', params)
      this.$api.saveWorktopManage(params).then((res) => {
        if (res.code == 200) {
          this.$message.success('保存成功')
          this.editDashExample = false
          this.getDragManageList()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 更多操作事件
    allMoreOper(type, component) {
      this.editDashExample = true
      this.dlayout.items.map((e) => {
        e.resizable = true
        e.draggable = true
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.page-container {
  margin: 0 auto;
  margin-top: 10px;
  height: 100%;
  max-width: 1620px;
  padding: 5px;
  ::v-deep .container-header {
    width: calc(100% - 20px);
    margin-left: 10px;
    background: none;
    // .control-btn-header {
    //   justify-content: flex-start;
    // }
  }
}
.control-btn-header {
  display: flex;
  justify-content: space-between;
  padding: 0 10px !important;
  height: 52px;
  line-height: 52px;
  box-sizing: border-box;
  .component-name {
    font-size: 16px;
    padding-left: 5px;
    font-family: 'PingFang SC-Medium', 'PingFang SC';
    font-weight: 550;
    color: #121f3e;
  }
  .energy-tabs {
    flex: auto;
    display: flex;
    margin-left: 28px;
    & > div {
      margin: auto 0;
      border-radius: 4px;
      // width: 66px;
      padding: 0 10px;
      height: 26px;
      line-height: 26px;
      text-align: center;
      background: #f6f5fa;
      color: #7f848c;
      font-size: 14px;
      margin-right: 10px;
      cursor: pointer;
    }
    .active-energy-tab {
      background: rgb(53 98 219 / 10%);
      color: #3562db;
    }
  }
}
.staging-content {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding-top: 5px;
  #dashExample {
    height: 100%;
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    :deep(.placeholder) {
      background: #e2e6eb !important;
      border-radius: 10px;
      opacity: 1;
    }
  }
  .data-Manage {
    height: calc(100% - 5px);
    width: 100%;
    padding: 5px 10px 10px;
    .page-container {
      margin: 0;
    }
    // overflow-y: ahuto;
  }
}
::v-deep .el-radio-button__orig-radio:checked + .el-radio-button__inner {
  background-color: #3562db;
  border-color: #3562db;
  box-shadow: -1px 0 0 0 #3562db;
}
::-webkit-scrollbar-thumb:hover,
::-webkit-scrollbar-track:hover {
  background: transparent;
}
::-webkit-scrollbar-thumb {
  background-color: transparent;
}
</style>
