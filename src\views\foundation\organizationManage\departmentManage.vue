<template>
  <PageContainer>
    <div slot="content" class="whole">
      <div class="left">
        <div class="title_box" @click="testRouter"><span></span> 组织结构</div>
        <div class="left_d">
          <el-collapse v-model="activeName" style="padding: 0 10px 0 20px" accordion @change="handelChange">
            <el-collapse-item v-for="(list, index) in collapseData" :key="index" :name="list.umId">
              <template slot="title">
                <el-tooltip class="item" effect="dark" :content="list.unitComName" placement="top-start">
                  <span>{{ list.unitComName }}</span>
                </el-tooltip>
              </template>
              <div class="sino_tree_box" style="margin: 0">
                <el-tree ref="tree" class="filter-tree" :data="treeData" :props="defaultProps" node-key="id" highlight-current @node-click="nodeClick"></el-tree>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
      <div class="right">
        <div style="display: flex; margin-bottom: 10px">
          <el-input v-model="filters.parentDeptName" class="ipt" placeholder="请输入上级部门名称" clearable> </el-input>
          <el-input v-model="filters.deptName" class="ipt" placeholder="请输入部门名称" clearable> </el-input>
          <el-input v-model="filters.principalName" class="ipt" placeholder="请输入部门负责人名称" clearable> </el-input>
          <el-button type="primary" @click="inquiry">查询</el-button>
          <el-button type="primary" plain @click="reset">重置</el-button>
          <el-button type="primary" @click="exportFn">导出</el-button>
        </div>
        <div slot="content" style="height: 100%">
          <div class="contentTable">
            <div class="contentTable-main table-content">
              <el-table :data="tableData" :height="tableHeight" stripe border @sort-change="handleSortChange">
                <el-table-column type="index" label="序号" width="50" align="center">
                  <template slot-scope="scope">
                    <span>{{ (pagination.current - 1) * pagination.size + scope.$index + 1 }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="部门名称" prop="deptName" show-overflow-tooltip sortable="custom">
                  <template slot-scope="scope">
                    <span class="color_blue" @click="ViewFn(scope.row)">
                      {{ scope.row.deptName }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column label="归属单位" prop="unitName" show-overflow-tooltip sortable="custom"> </el-table-column>
                <el-table-column label="部门电话" prop="deptPhone" show-overflow-tooltip> </el-table-column>
                <el-table-column label="上级部门" prop="parentDeptName" show-overflow-tooltip sortable="custom"> </el-table-column>
                <el-table-column label="组织机构编码" prop="officeCode" show-overflow-tooltip> </el-table-column>
                <el-table-column label="财务核算码" prop="financeCode" show-overflow-tooltip> </el-table-column>
                <el-table-column label="部门负责人" prop="principalName" show-overflow-tooltip sortable="custom"> </el-table-column>
                <el-table-column label="负责人电话" prop="principalPhone" show-overflow-tooltip> </el-table-column>
                <el-table-column label="备注" prop="remark" show-overflow-tooltip> </el-table-column>
              </el-table>
            </div>
          </div>
          <div class="contentTable-footer">
            <el-pagination
              :current-page="pagination.current"
              :page-sizes="[15, 30, 50, 100]"
              :page-size="pagination.size"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            ></el-pagination>
          </div>
        </div>
      </div>
    </div>
  </PageContainer>
</template>

<script>
import store from '@/store/index'
import axios from 'axios'
import { transData } from '@/util'
import tableListMixin from '@/mixins/tableListMixin.js'
export default {
  name: 'departmentManage',
  mixins: [tableListMixin],
  beforeRouteEnter(to, from, next) {
    let names = []
    to.matched.map((v, i) => {
      if (i > 0) {
        v.components.default.name && names.push(v.components.default.name)
      }
    })
    // 进入页面时，先将当前页面的 name 信息存入 keep-alive 全局状态
    setTimeout(() => {
      next((vm) => {
        vm.$store.commit('keepAlive/add', names)
      })
    }, 0)
  },
  async beforeRouteLeave(to, from, next) {
    // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
    if (!['depetMess'].includes(to.name)) {
      // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      activeName: '',
      collapseData: [],
      treeData: [],
      treeLoading: true,
      checkedTreeNode: '',
      defaultProps: {
        children: 'children',
        label: 'deptName'
      },
      filters: {
        unitId: '',
        parentDeptName: '',
        deptPidId: '',
        deptName: '',
        principalName: ''
      },
      unitList: [],
      tableData: [],
      pagination: {
        current: 1,
        size: 15
      }, // 分页数据
      total: 0, // 数据总条数
      // -------------------Dialog_导入
      addFileName: 'depet',
      orderItems: []
    }
  },
  created() {},
  activated() {
    this.departListFn()
  },
  mounted() {
    this.getUnitListFn()
    this.departListFn()
  },
  methods: {
    testRouter() {
      this.$router.push('/wasteManage')
    },
    handleSortChange({ order, prop }) {
      this.orderItems = []
      let obj = {}
      if (order == 'ascending') {
        obj.asc = true
        obj.column = prop
        this.orderItems.push(obj)
      } else if (order == 'descending') {
        obj.asc = false
        obj.column = prop
        this.orderItems.push(obj)
      } else {
        this.orderItems = []
      }
      this.pagination.current = 1
      this.pagination.size = 15
      this.departListFn()
    },
    inquiry() {
      this.pagination.current = 1
      this.pagination.size = 15

      this.filters.parentDeptName = this.filters.parentDeptName
      this.filters.deptName = this.filters.deptName
      this.filters.principalName = this.filters.principalName
      this.departListFn()
    },
    reset() {
      this.pagination.current = 1
      this.pagination.size = 15
      this.filters.parentDeptName = ''
      this.filters.deptName = ''
      this.filters.principalName = ''
      this.departListFn()
    },
    handelChange(val) {
      this.activeName = val
      if (val != '') {
        this.pagination.current = 1
        this.pagination.size = 15
        this.getDeptListFn(val)
      }
      this.filters.unitId = val
      this.filters.officeId = ''
      this.departListFn()
    },
    nodeClick(val) {
      this.checkedTreeNode = val
      this.filters.unitId = val.umId
      this.filters.deptPidId = val.id
      this.pagination.current = 1
      this.pagination.size = 15
      this.departListFn()
    },
    // 单位列表
    getUnitListFn() {
      this.$api.getSelected({}).then((res) => {
        if (res.code == 200) {
          this.collapseData = this.unitList = res.data
        }
      })
    },
    // 部门列表
    getDeptListFn(unitId) {
      this.treeData = []
      this.$api
        .getSelectedDept({
          unitId: unitId
        })
        .then((res) => {
          if (res.code == 200) {
            this.treeData = transData(res.data, 'id', 'pid', 'children')
          }
        })
    },
    //  获取部门列表(下拉)
    departListFn() {
      this.$api
        .departList({
          ...this.filters,
          ...this.pagination,
          orderItems: this.orderItems
        })
        .then((res) => {
          // this.treeLoading = false;
          if (res.code == 200) {
            this.tableData = res.data.records
            this.pagination.current = res.data.current
            this.pagination.size = res.data.size
            this.total = res.data.total
          }
        })
    },
    handleSizeChange(val) {
      this.pagination.current = 1
      this.pagination.size = val
      this.departListFn()
    },
    handleCurrentChange(val) {
      this.pagination.current = val
      this.departListFn()
    },
    // -----------------------------------Table-Fn
    ViewFn(row) {
      this.$router.push({
        path: '/depetMess',
        query: { type: 'View', id: row.id }
      })
    },
    //  导出
    exportFn() {
      let departmentExportVo = {
        ...this.filters,
        size: this.pagination.size
      }
      const userInfo = store.state.user.userInfo.user

      let menuList = this.$store.state.menu.routes
      let firstTitle = ''
      let routeList = []
      this.$router.currentRoute.matched.filter(item => item.name).forEach(el => {
        routeList.push(el.meta.title)
      })
      if (menuList.length) {
        firstTitle = menuList[this.$store.state.menu.headerActived].meta.title
        routeList.unshift(firstTitle)
      }
      axios({
        method: 'post',
        url: __PATH.VUE_SPACE_API + 'departmentManager/department-manager/exportDepartmentList',
        data: departmentExportVo,
        responseType: 'arraybuffer',
        headers: {
          'Content-Type': 'application/json;charset=utf-8',
          Authorization: 'Bearer ' + store.state.user.token,
          hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
          unitCode: userInfo.unitCode ?? 'BJSYGJ',

          'operation-type': 4,
          'operation-content': encodeURIComponent(routeList.join(','))
        }
      })
        .then((res) => {
          this.$message.success(res.message || '导出成功')
          this.$tools.downloadFile(res, this)
        })
        .catch((res) => {
          this.$message.error(res.message || '导出失败')
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.whole {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;

  .left {
    width: 250px;
    height: 100%;
    background-color: #fff;
    border-radius: 5px;
    padding-right: 5px;
    padding-left: 10px;
    overflow: hidden;

    .left_d {
      height: calc(100% - 20px);
      overflow: auto;
    }
  }

  .right {
    width: calc(100% - 260px);
    background-color: #fff;
    height: 100%;
    border-radius: 5px;
    padding: 10px;
  }

  .ipt {
    width: 200px;
    margin-right: 10px;
  }
}

.title_box {
  box-sizing: border-box;
  padding-left: 20px;
  height: 50px;
  width: 100%;
  line-height: 50px;
  text-align: left;
  border-bottom: 1px solid #d8dee7;
}

.color_blue {
  color: #5188fc;
  cursor: pointer;
}

.contentTable {
  height: calc(100% - 70px);
  background: #fff;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  overflow: auto;

  .contentTable-main {
    flex: 1;
    overflow: auto;
  }

  .contentTable-footer {
    padding: 10px 0 0;
  }

  .alarmLevel {
    padding: 3px 6px;
    border-radius: 4px;
    color: #fff;
    line-height: 14px;
  }

  .alarmStatus {
    position: relative;
    display: inline-block;
    padding-left: 12px;

    .alarmStatusIcon {
      display: inline-block;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 8px;
      height: 8px;
      border-radius: 100%;
    }
  }

  .collectIcon {
    font-size: 16px;
    margin-right: 4px;
  }
}
::v-deep .el-collapse-item__header {
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: block;
}
</style>
