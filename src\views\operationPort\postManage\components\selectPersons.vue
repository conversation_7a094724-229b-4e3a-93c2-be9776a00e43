<template>
  <el-dialog v-if="memberShow" v-dialogDrag title="添加成员" width="50%" :visible.sync="memberShow"
    custom-class="model-dialog" :before-close="closeDialog">
    <div class="sapce_content">
      <div class="left">
        <el-input v-model="treeSearchKeyWord" placeholder="输入关键字搜索" suffix-icon="el-icon-search"></el-input>
        <div v-loading="treeLoading" class="sino_tree_box">
          <el-tree ref="treePersonRef" v-loading="treeLoading" class="space-tree__tree" :data="treeData" node-key="id"
            :props="defaultProps" size="small" :highlight-current="true" :expand-on-click-node="false"
            :filter-node-method="filterNode" @node-click="nodeClick">
          </el-tree>
        </div>
      </div>
      <div class="center">
        <el-input v-model.trim="userInfo" placeholder="搜索姓名、手机号、部门" clearable suffix-icon="el-icon-search"
          @input="nameInput"></el-input>
        <el-checkbox v-if="checkType == 'checkbox'&&staffData.length" v-model="allChecked"
          :indeterminate="isIndeterminate" class="selectAll" @change="checkAllChange">全选</el-checkbox>
        <el-checkbox-group v-model="staffSelect" @change="changegroup" v-if="staffData.length>0">
          <el-checkbox v-for="item in staffData" :key="item[nodeKey]" :label="item[nodeKey]" style="display: block"
            @change="(val) => checkboxchange(item, val)">
            <div class="personCheck">
              <img src="@/assets/images/avatar.png" />
              <div class="info">
                <div class="name">{{ item.staffName }}</div>
                <div class="mobile">{{ item.mobile }}</div>
              </div>
            </div>
          </el-checkbox>
        </el-checkbox-group>
        <div v-else class="empty">
          暂无符合要求成员
        </div>
      </div>
      <div class="right">
        <div class="top">
          <span>
            <span class="label">已选:</span>
            <span class="num">{{ selectData.length ? selectData.length : 0 }}</span>
            <span class="dept">名用户</span>
          </span>
          <span class="clear" @click="clear">清空</span>
        </div>
        <div v-for="(item, index) in selectData" :key="index" class="item-list">
          <div style="display: flex">
            <img src="@/assets/images/avatar.png" />
            <div class="info">
              <div class="name">{{ item.staffName }}</div>
              <div class="mobile">{{ item.mobile }}</div>
            </div>
          </div>
          <div class="remove" @click="remove(item, index)"><i class="el-icon-close"></i></div>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="submit">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { transData } from '@/util'
export default {
  name: 'spaceDialog',
  props: {
    memberShow: {
      type: Boolean,
      default: false
    },
    /*
      是否为单选
      checkbox 多选
      radio 单选
    */
    checkType: {
      type: String,
      default: 'checkbox'
    },
    defaultChecked: {
      type: Array,
      default: () => []
    },
    nodeKey: {
      type: String,
      default: 'id'
    },
    //根据岗位过滤信息
    postCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      keyWord: '', // 编码/名称/通用名/规格型号/SN
      treeData: [],
      defaultProps: {
        children: 'child',
        label: 'deptName'
      },
      // 树搜索关键字
      treeSearchKeyWord: '',
      treeLoading: false,
      selectData: [],
      staffData: [], // 人员列表
      staffSelect: [],
      checkItem: {}, // 选中数据
      userInfo: '',
      isIndeterminate: false,
      allChecked: false // 全选
    }
  },
  mounted() {
    this.getUnitListFn()
  },
  watch: {
    treeSearchKeyWord(val) {
      this.$refs.treePersonRef.filter(val)
    },
  },
  methods: {
    // 树节点过滤方法
    filterNode(value, data) {
      if (!value) return true
      return data.deptName.includes(value)
    },
    //  获取单位列表
    getUnitListFn() {
      this.$api.supplierAssess.getDeptTreeData({ name: '' }).then((res) => {
        this.treeLoading = false
        if (res.code == 200) {
          this.treeData = res.data
          this.checkItem = this.treeData[0]
          this.$nextTick(() => {
            const firstNode = this.$refs.treePersonRef.$el.querySelector('.el-tree-node__content');
            if (firstNode) {
              firstNode.classList.add('highlight-node'); // 添加自定义的类来控制样式。
            }
          });
          this.staffListByPageFn('init')
        }
      })
    },
    // 全选改变
    checkAllChange(val) {
      this.isIndeterminate = false
      if (val) {
        this.staffSelect = this.staffData.map((item) => {
          return item[this.nodeKey]
        })
        this.selectData = this.staffData.map((item) => {
          return item
        })
      } else {
        this.staffSelect = []
        this.selectData = []
      }
    },
    nameInput() {
      this.staffListByPageFn()
      this.staffSelect = this.selectData.map((item) => {
        return item[this.nodeKey]
      })
    },
    nodeClick(data, node) {
      this.$nextTick(() => {
        const firstNode = this.$refs.treePersonRef.$el.querySelector('.el-tree-node__content');
        if (firstNode) {
          firstNode.classList.remove('highlight-node'); // 添加自定义的类来控制样式。
        }
      });
      this.checkItem = data
      this.staffListByPageFn()
      this.isIndeterminate = false
      this.allChecked = false
    },
    //  获取人员信息列表
    staffListByPageFn(type) {
      this.$api.supplierAssess
        .queryUserInfoListByPage({
          page: 1,
          pageSize: 999,
          postCode: this.postCode ? this.postCode : '',
          deptId: this.checkItem.id ? this.checkItem.id : '',
          umId: this.checkItem.umId ? this.checkItem.umId : '',
          userName: this.userInfo,
          showType: 2
        })
        .then((res) => {
          if (res.code == 200) {
            this.staffData = res.data.records
            if (this.defaultChecked.length && type == 'init') {
              this.staffSelect = this.defaultChecked
              this.selectData = this.staffData.filter((item) => {
                return this.defaultChecked.includes(item[this.nodeKey])
              })
            }
          }
        })
    },
    // 控制单选
    changegroup(list) {
      if (this.checkType == 'radio') {
        this.staffSelect = [list[list.length - 1]]
        this.selectData = this.staffData.filter((el) => el[this.nodeKey] == this.staffSelect[0])
      } else {
        this.allChecked = list.length == this.staffData.length
        this.isIndeterminate = list.length > 0 && list.length < this.staffData.length
      }
    },
    // 选择
    checkboxchange(item, val) {
      if (!val) {
        this.selectData = this.selectData.filter((el) => {
          return el[this.nodeKey] !== item[this.nodeKey]
        })
      } else {
        if (this.selectData.includes(item)) {
          return
        } else {
          this.selectData.push(item)
        }
      }
    },
    // 移除
    remove(list, index) {
      this.selectData.splice(index, 1)
      this.staffSelect = this.selectData.map((item) => {
        return item[this.nodeKey]
      })
    },
    // 清空
    clear() {
      this.selectData = []
      this.staffSelect = []
      this.allChecked = false
      this.$nextTick(() => {
        this.$refs.treeRef.setCheckedKeys([])
      })
    },
    closeDialog() {
      this.$emit('closeMemberDialog')
    },
    submit() {
      if (this.selectData.length < 1) {
        this.$message({
          message: '请选择至少一条数据',
          type: 'warning'
        })
      } else {
        this.$emit('submitMemberDialog', this.selectData)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.model-dialog {
  .sapce_content {
    margin: 0 auto;
    background: #fff;
    padding: 10px 0;
    border-radius: 4px;
    height: 400px;
    display: flex;
    width: 100%;
    .left {
      text-align: center;
      width: 300px;
      height: 100%;
      background-color: #fff;
      border-radius: 5px;
      padding: 0 16px;
      overflow: hidden;
    }
    .center {
      width: 300px;
      border-left: 1px solid #dcdfe6;
      background-color: #fff;
      height: 100%;
      margin-left: 10px;
      border-radius: 5px;
      padding-left: 16px;
      overflow: scroll;
      // display: flex;
      .personCheck {
        width: 100%;
        display: flex;
        padding: 10px 0;
        img {
          vertical-align: middle;
        }
      }
    }
    .right {
      width: calc(100% - 620px);
      border-left: 1px solid #dcdfe6;
      background-color: #fff;
      height: 100%;
      margin-left: 10px;
      border-radius: 5px;
      padding-left: 16px;
      overflow: scroll;
      .top {
        display: flex;
        padding: 0 12px;
        justify-content: space-between;
        .label,
        .dept {
          font-size: 14px;
          color: #7f848c;
        }
        .num {
          font-size: 14px;
          color: #333333;
          margin-left: 10px;
        }
        .dept {
          margin-left: 10px;
        }
        .clear {
          font-size: 12px;
          color: #3562db;
          cursor: pointer;
        }
      }
      .item-list {
        display: flex;
        cursor: pointer;
        padding: 0 12px;
        justify-content: space-between;
        margin-top: 8px;
        .remove {
          margin: auto 0;
        }
      }
      .item-list:hover {
        background: #e6effc;
      }
    }
    .info {
      margin-left: 8px;
      .name {
        font-weight: 500;
        color: #333333;
      }
      .mobile {
        font-size: 12px;
        color: #7f848c;
      }
    }
  }
}
.sino_tree_box {
  margin-top: 10px;
  height: calc(100% - 40px);
  overflow: auto;
  .custom-tree-node-label {
    display: inline-block;
    width: 200px;
    white-space: nowrap; /* 防止换行 */
    overflow: hidden; /* 隐藏超出部分 */
    text-overflow: ellipsis;
    text-align: left;
  }
}
::v-deep .el-tree .el-tree-node__content {
  height: 36px;
  line-height: 16px;
  padding: 0;
}
::v-deep .el-tree-node {
  .is-leaf + .el-checkbox .el-checkbox__inner {
    display: inline-block;
  }
  .el-checkbox .el-checkbox__inner {
    display: none;
  }
}
::v-deep .el-collapse-item__content {
  padding-bottom: 0px !important;
}
::v-deep .el-checkbox__input {
  display: inline-block !important;
  margin-bottom: 26px !important;
}
.selectAll {
  margin: 10px 0;
}
::v-deep.selectAll .el-checkbox__input {
  margin-bottom: 0 !important;
}
::v-deep .selectAll .el-checkbox__label {
  padding-left: 20px;
}
::v-deep .highlight-node {
  color: #3562db;
  background: #e6effc;
}
.empty {
  height: calc(100% - 40px);
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
