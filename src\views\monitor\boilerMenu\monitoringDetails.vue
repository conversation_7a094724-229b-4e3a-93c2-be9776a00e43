<template>
  <PageContainer>
    <div slot="content" class="elevator-content">
      <div ref="largeScreenMonitoring" class="largeScreenMonitoring">
        <div class="largeScreen_content">
          <div class="md_title">
            <i class="el-icon-arrow-left" @click="$router.go(-1)"></i>
            <span>1#蒸汽锅炉</span>
          </div>
          <div class="monitor_data">
            <div class="monitor_data_item">
              <p>温度：</p>
              <span>B1锅炉房</span>
            </div>
            <div class="monitor_data_item">
              <p>监测编号：</p>
              <span>123455643</span>
            </div>
          </div>
          <div class="query_and_echart">
            <h3 class="query_and_echart_title" >监测参数</h3>
            <div class="date_time">
              <el-date-picker
                v-model="dateRange"
                class="my-date-picker"
                popper-class="diabloPopperClass"
                type="date"
                range-separator="至"
                start-placeholder="开始月份"
                end-placeholder="结束月份"
                :picker-options="pickerOptions"
                :clearable="false"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
              <div class="dialog_btn reset">重置</div>
              <div class="dialog_btn query">查询</div>
            </div>
            <div class="md_current_query">
              <p>锅炉压力</p>
              <b>8.1<span>Mpa</span></b>
              <div v-if="!environmentalMonitoringShow" class="echart-null">
                <img src="@/assets/images/null.png" alt="" />
                <div>暂无数据~</div>
              </div>
              <div v-else style="width: 100%; height: calc(100% - 92px);">
                <div id="environmentalMonitoring"></div>
              </div>
            </div>

          </div>
        </div>
      </div>
      <AlarmDialog ref="alarmDialog" />
    </div>
  </PageContainer>
</template>

<script>
import mixin from './mixin/mixin.js'
import * as echarts from 'echarts'
export default {
  mixins: [mixin],
  data() {
    return {
      environmentalMonitoringShow: true,
      dateRange: null,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      }
    }
  },
  mounted() {
    setTimeout(() => {
      this.getenvironmentalMonitoringEcharts()
    }, 1)
  },
  methods: {
    getenvironmentalMonitoringEcharts() {
      var data = [
        {
          name: '00:00',
          value: 37
        },
        {
          name: '01:00',
          value: 27
        },
        {
          name: '02:00',
          value: 17
        },
        {
          name: '03:00',
          value: 7
        },
        {
          name: '04:00',
          value: 57
        },
        {
          name: '05:00',
          value: 57
        },
        {
          name: '06:00',
          value: 54
        }
      ]
      this.setenvironmentalMonitoringEcharts(data)
    },
    // 能耗分析柱状图
    setenvironmentalMonitoringEcharts(data) {
      console.log('data===========', data)
      const getchart = echarts.init(document.getElementById('environmentalMonitoring'))
      console.log(getchart)
      let textColor = '#86909C'
      let option = {
        color: ['#3562DB'],
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: data.map(x => x.name),
          triggerEvent: true,
          axisLine: {
            show: false
          },
          axisLabel: {
            color: textColor,
            fontSize: 12
          }
        },
        yAxis: {
          type: 'value',
          name: '单位：m³',
          nameTextStyle: {
            color: textColor
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: textColor,
            fontSize: 13
          },
          // y轴轴线颜色
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed',
              color: 'rgba(255,255,255,0.2)'
            }
          }
        },
        dataZoom: [
          {
            type: 'slider',
            realtime: true,
            start: 0,
            end: 20,  // 数据窗口范围的结束百分比。范围是：0 ~ 100。
            height: 5, // 组件高度
            left: 5, // 左边的距离
            right: 5, // 右边的距离
            bottom: 10, // 下边的距离
            show: data.length > 6,  // 是否展示
            showDetail: false, // 拖拽时是否展示滚动条两侧的文字
            zoomLock: true,         // 是否只平移不缩放
            moveOnMouseMove: false, // 鼠标移动能触发数据窗口平移
            // zoomOnMouseWheel: false, // 鼠标移动能触发数据窗口缩放
            // 下面是自己发现的一个问题，当点击滚动条横向拖拽拉长滚动条时，会出现文字重叠，导致效果很不好，以此用下面四个属性进行设置，当拖拽时，始终保持显示六个柱状图，可结合自己情况进行设置。添加这个属性前后的对比见**图二**
            startValue: 0, // 从头开始。
            endValue: 6,  // 最多六个
            minValueSpan: 6,  // 放大到最少几个
            maxValueSpan: 6  //  缩小到最多几个
          },
          {
            type: 'inside',  // 支持内部鼠标滚动平移
            start: 0,
            end: 20,
            zoomOnMouseWheel: false,  // 关闭滚轮缩放
            moveOnMouseWheel: true, // 开启滚轮平移
            moveOnMouseMove: true  // 鼠标移动能触发数据窗口平移
          }
        ],
        series: [
          {
            data: data.map(x => x.value),
            type: 'line',
            itemStyle: {
              color: '#F4DB67'
            },
            barWidth: 10,
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: '#F4DB67'
                }, {
                  offset: 1,
                  color: 'rgba(244, 219, 103, .1)'
                }])
              }
            }
          }
        ],
        grid: { // 让图表占满容器
          top: '30px',
          left: '16px',
          right: '16px',
          bottom: '20px',
          containLabel: true
        }
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    }
  }
}
</script>

<style lang="scss">
@import './css/index.scss';
</style>
<style lang="scss" scoped>
#environmentalMonitoring {
  width: 100%;
  height: 100%;
  z-index: 2;
}
.echart-null {
  margin: 0 auto;
  height: calc(100% - 31px);
  width: 50%;
  text-align: center;
  color: #8a8c8f;

  img {
    max-width: 100%;
    max-height: calc(100% - 20px);
  }

  div {
    font-size: 14px;
  }
}
.elevator-content {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  .largeScreenMonitoring {
    width: 100%;
    height: 100%;
    overflow: hidden;
    .largeScreen_content {
      height: calc(100% - 0px);
      width: 100%;
      padding: 16px;
      display: flex;
      flex-direction: column;
      // justify-content: space-between;
      background: url('~@/assets/images/elevator/elevator-bg.png') no-repeat;
      background-size: 100% 100%;
      .query_and_echart{
        width: 100%;
        height: calc(100% - 100px);
        background: linear-gradient(180deg, #101D29 0%, #081A2B 100%);
        padding: 20px;
        .md_current_query{
          height: calc(100% - 90px);
          padding: 0 24px;
          p{
            font-size: 16px;
            font-family: Alibaba PuHuiTi-Regular, Alibaba PuHuiTi;
            font-weight: 400;
            color: #A2B7D9;
            margin: 0;
            margin-bottom: 5px;
          }
          b{
            font-size: 32px;
            font-family: DIN-Bold, DIN;
            font-weight: bold;
            color: #EEFBFE;
            line-height: 38px;
            display: inline-block;
            margin-bottom: 20px;
            span{
              font-size: 14px;
              font-family: PingFang SC-Regular, PingFang SC;
              font-weight: 400;
              color: #CCCED3;
            }
          }
        }
        .query_and_echart_title{
          font-size: 20px;
          font-family: HarmonyOS Sans SC-Medium, HarmonyOS Sans SC;
          font-weight: 500;
          color: #DCE9FF;
          margin: 0;
        }
        .date_time{
          display: flex;
          margin: 20px 0;
          ::v-deep .el-input {
            border-radius: 4px;

            .el-input__inner {
              background: rgb(3 23 81 / 50%);
              border: 1px solid #193382;
              color: #fff;
            }

            .el-input-group__append,
            .el-input-group__prepend {
              padding: 0 10px;
              background: rgb(3 23 81 / 50%);
              border-color: #193382;
              color: #fff;
            }
          }
          ::v-deep .el-input__prefix{
            color: #2181F4;
          }
        }
      }
      .monitor_data{
        display: flex;
        align-items: center;
        padding-left: 20px;
        &_item{
          display: flex;
          align-items: center;
          margin: 10px 0;
          margin-right: 100px;
          p{
            font-size: 16px;
            font-family: Alibaba PuHuiTi-Regular, Alibaba PuHuiTi;
            font-weight: 400;
            color: #A2B7D9;
          }
          span{
            font-size: 16px;
            font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
            font-weight: 400;
            color: #FFFFFF;
          }
        }
      }
      .md_title{
        position: relative;
        display: flex;
        align-items: center;
        background: url('~@/assets/images/monitor/title_style.png') no-repeat;
        background-position: 15px 15px;
        .el-icon-arrow-left{
          cursor: pointer;
          color: #FFFFFF;
        }
        span{
          margin-left: 10px;
          font-size: 20px;
          font-family: HarmonyOS Sans SC-Medium, HarmonyOS Sans SC;
          font-weight: 500;
          color: #DCE9FF;
        }
      }
    }
  }
}
</style>
