<template>
  <div class="setOrEdit">
    <el-dialog v-dialogDrag custom-class="model-dialog" :title="ifType == '0' ? '添加角色' : '编辑角色'" :visible.sync="dialogVisible" width="748px" :before-close="closeDialog">
      <div class="sino-content">
        <el-form ref="addRole" :model="filters" :rules="rules" label-position="right" label-width="100px">
          <el-form-item label="角色名称：" prop="roleName">
            <el-input v-model="filters.roleName" placeholder="请输入" maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="角色编码：">
            <el-input v-model="filters.roleCode" placeholder="系统自动生成" disabled></el-input>
          </el-form-item>
          <el-form-item label="排序号：">
            <el-input v-model="filters.roleSort" maxlength="10" placeholder="请输入"></el-input>
          </el-form-item>
          <br />
          <el-form-item label="备注：">
            <el-input v-model="filters.remarks" maxlength="200" placeholder="请输入备注" style="width: 550px;" show-word-limit type="textarea"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" plain @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="editSubmit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
// import { userRoleEditRole, getDictListByModuleIdentity } from "@/common/api/assets";
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    ifType: {
      type: Number,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: false
    },
    roleData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      rules: {
        roleName: [{ required: true, message: '请输入角色名', trigger: 'blur' }],
        roleCode: [{ required: true, message: '请输入角色编码', trigger: 'blur' }]
      },
      filters: {
        roleName: '',
        roleCode: '',
        // isSys: '1',
        remarks: '',
        personName: '',
        roleSort: ''
      },
      sysIdentityList: []
    }
  },
  mounted() {
    var obj = JSON.parse(sessionStorage.getItem('LOGINDATA'))
    this.filters.personName = obj.name
    if (this.ifType == 1) {
      //   this.getRoleData()
      this.getSysIdentityList()
    }
  },
  methods: {
    // getRoleData() {
    //   if (this.roleData) {
    //     this.filters.roleName = this.roleData.roleName
    //     this.filters.roleCode = this.roleData.roleCode
    //     this.filters.isSys = this.roleData.isSys
    //     this.filters.remarks = this.roleData.remarks
    //     this.filters.roleSort = this.roleData.roleSort
    //   }
    // },
    closeDialog() {
      this.$refs.addRole.resetFields()
      this.$emit('closeDialog')
    },
    getSysIdentityList() {
      let data = {
        dictType: 'identity_type',
        roleCode: this.roleData.id
      }
      // 获取详情
      this.$api.ipsmGetRoleDetail(data).then((res) => {
        this.filters = res.data
      })
    },
    /**
     * 保存新增/修改的角色
     */
    editSubmit() {
      this.$refs['addRole'].validate((valid) => {
        if (valid) {
          let data = {
            roleName: this.filters.roleName,
            // role_number: this.filters.roleCode,
            roleSort: this.filters.roleSort,
            // isSys: this.filters.isSys,
            remarks: this.filters.remarks,
            personName: this.filters.personName,
            createBy: JSON.parse(sessionStorage.getItem('LOGINDATA')).userName,
            roleCode: ''
          }
          if (this.ifType == 1) {
            // 修改
            data.roleCode = this.roleData.id
            this.$api.ipsmUpdate(data).then((res) => {
              if (res.code == 200) {
                this.$message.success(res.message)
                this.$refs.addRole.resetFields()
                this.filters.remarks = ''
                this.$emit('closeDialog')
                this.$emit('init')
              } else if (res.message) {
                this.$message.error(res.$message)
              }
            })
          } else {
            // 新增
            this.$api.ipsmInsert(data).then((res) => {
              if (res.code == 200) {
                this.$message.success(res.message)
                this.$refs.addRole.resetFields()
                this.filters.remarks = ''
                this.$emit('closeDialog')
                this.$emit('init')
              } else if (res.$message) {
                this.$message.error(res.message)
              }
            })
          }
        } else {
          this.$tools.focusFunc()
        }
      })
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
.el-input {
  width: 200px;
}

.sino-content {
  background-color: #fff;
  width: 100%;
  padding: 10px;
}
</style>
