import Layout from '@/layout'
export default [
  {
    path: '/housingResource',
    component: Layout,
    redirect: '/housingResource/index',
    name: 'housingResource',
    meta: {
      title: '房源管理',
      menuAuth: '/housingResource/index'
      // icon: 'sidebar-breadcrumb'
    },
    children: [
      {
        path: 'index',
        name: 'housingResource',
        component: () => import('@/views/rentalHousing/housingResource/housingList.vue'),
        meta: {
          title: '房源管理',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/housingResource'
        }
      },
      {
        path: 'detail',
        name: 'housingDetail',
        component: () => import('@/views/rentalHousing/housingResource/HousingDetail.vue'),
        meta: {
          title: '房间详情',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/housingResource'
        }
      }
    ]
  },
  {
    path: '/rentalHousingContractManagement',
    component: Layout,
    redirect: { name: 'rentalHousingContractManagementIndex' },
    name: 'contractManagement',
    meta: {
      title: '合同管理',
      menuAuth: '/rentalHousingContractManagement/index'
      // icon: 'sidebar-breadcrumb'
    },
    children: [
      {
        path: 'index',
        name: 'rentalHousingContractManagementIndex',
        component: () => import('@/views/rentalHousing/contractManagement/contractManagement.vue'),
        meta: {
          title: '合同管理',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/rentalHousingContractManagement'
        }
      },
      {
        path: 'details',
        name: 'rentalHousingContractManagementDetail',
        component: () => import('@/views/rentalHousing/contractManagement/ContractDetails.vue'),
        meta: {
          title: '详情',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/rentalHousingContractManagement'
        }
      }
    ]
  },
  {
    path: '/billManagement',
    component: Layout,
    redirect: '/billManagement/index',
    name: 'billManagement',
    meta: {
      title: '账单管理',
      menuAuth: '/billManagement/index'
      // icon: 'sidebar-breadcrumb'
    },
    children: [
      {
        path: 'index',
        name: 'billManagementIndex',
        component: () => import('@/views/rentalHousing/billManagement/billManagement.vue'),
        meta: {
          title: '账单管理',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/billManagement'
        }
      },
      {
        path: 'ContractDetails',
        name: 'ContractDetails',
        component: () => import('@/views/rentalHousing/contractManagement/ContractDetails.vue'),
        meta: {
          title: '详情',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/billManagement'
        }
      }
    ]
  },
  {
    path: '/functionSettings',
    component: Layout,
    redirect: '/functionSettings/index',
    name: 'functionSettings',
    meta: {
      title: '功能设置',
      menuAuth: '/functionSettings/index'
      // icon: 'sidebar-breadcrumb'
    },
    children: [
      {
        path: 'index',
        name: 'functionSettingsIndex',
        component: () => import('@/views/rentalHousing/functionSettings/functionSettings.vue'),
        meta: {
          title: '功能设置',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/functionSettings'
        }
      }
    ]
  },
  {
    path: '/rentalHousingApproveManage',
    component: Layout,
    redirect: { name: 'roomApproveManage' },
    name: 'rentalHousingApproveManage',
    meta: {
      title: '审批管理',
      menuAuth: '/rentalHousingApproveManage/index',
      badge: 0
      // icon: 'sidebar-breadcrumb'
    },
    children: [
      {
        path: 'roomApproveManage',
        name: 'roomApproveManage',
        component: () => import('@/views/microApp/index.vue'),
        meta: {
          title: '审批管理',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/rentalHousingApproveManage'
        }
      }
    ]
  },
  {
    path: '/allocationManagement',
    component: Layout,
    redirect: '/allocationManagement/index',
    name: 'allocationManagement',
    meta: {
      title: '配房管理',
      menuAuth: '/allocationManagement/index'
      // icon: 'sidebar-breadcrumb'
    },
    children: [
      {
        path: 'index',
        name: 'allocationManagementIndex',
        component: () => import('@/views/rentalHousing/allocationManagement/list.vue'),
        meta: {
          title: '配房管理',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/allocationManagement'
        }
      },
      {
        path: 'allocationDetail',
        name: 'allocationDetail',
        component: () => import('@/views/rentalHousing/allocationManagement/details.vue'),
        meta: {
          title: '配房管理详情',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/allocationManagement'
        }
      }
    ]
  },
  {
    path: '/statisticalAnalysisList',
    component: Layout,
    redirect: '/statisticalAnalysisList/index',
    name: 'statisticalAnalysisList',
    meta: {
      title: '统计分析'
    },
    children: [
      {
        path: 'index',
        name: 'statisticalAnalysisListIndex',
        component: () => import('@/views/rentalHousing/statisticalAnalysis/statisticalAnalysisList.vue'),
        meta: {
          title: '统计分析',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/statisticalAnalysisList'
        }
      }
    ]
  },
  {
    path: '/housingDict',
    component: Layout,
    redirect: '/housingDict/index',
    meta: {
      title: '字典配置',
      menuAuth: '/housingDict/index'
      // icon: 'sidebar-breadcrumb'
    },
    children: [
      {
        path: 'index',
        name: 'housingDictIndex',
        component: () => import('@/views/rentalHousing/dictionary/DictionaryList.vue'),
        meta: {
          title: '字典配置',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/housingDict'
        }
      }
    ]
  },
  {
    path: '/fileManagement',
    component: Layout,
    redirect: '/fileManagement/index',
    meta: {
      title: '文件管理',
      menuAuth: '/fileManagement/index'
    },
    children: [
      {
        path: 'index',
        name: 'fileManagementIndex',
        component: () => import('@/views/rentalHousing/fileManagement/index.vue'),
        meta: {
          title: '文件管理',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/fileManagement'
        }
      },
      {
        path: 'addFile',
        name: 'addFile',
        component: () => import('@/views/rentalHousing/fileManagement/addFile.vue'),
        meta: {
          title: '新增',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/fileManagement'
        }
      }
    ]
  },
  {
    path: '/publicityMangement',
    component: Layout,
    redirect: '/publicityMangement/index',
    meta: {
      title: '公示管理',
      menuAuth: '/publicityMangement/index'
    },
    children: [
      {
        path: 'index',
        name: 'publicityMangementIndex',
        component: () => import('@/views/rentalHousing/publicityMangement/index.vue'),
        meta: {
          title: '公示管理',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/publicityMangement'
        }
      },
      {
        path: '/addPublicity',
        name: 'addPublicity',
        component: () => import('@/views/rentalHousing/publicityMangement/addPublicity.vue'),
        meta: {
          title: '新增',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/publicityMangement'
        }
      }
    ]
  }
]
