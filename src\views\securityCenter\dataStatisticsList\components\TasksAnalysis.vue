<template>
  <div>
    <!-- echarts -->
    <div class="echartsWrap">
      <div ref="taskAnalysis" class="charts" style="margin-right: 50px;"></div>
      <div ref="timeAnalysis" class="charts"></div>
    </div>
    <!-- table -->
    <div>
      <el-table stripe :data="tableData" border style="width: 100%;">
        <el-table-column type="index" label="序号" width="55" align="center"></el-table-column>
        <el-table-column prop="teamName" label="安全组织"  align="center"></el-table-column>
          <el-table-column prop="count" label="应巡次数" align="center">
            <template slot-scope="scope">
              <el-link type="primary" :underline="false" @click="goTaskList(scope, scope.row.count || 0 )">{{ scope.row.count || 0 }}</el-link>
            </template>
				  </el-table-column>
          <el-table-column prop="hasCount" label="已巡次数"  align="center">
            <template slot-scope="scope">
              <el-link type="primary" :underline="false" @click="goTaskList(scope, scope.row.hasCount || 0 )">{{ scope.row.hasCount || 0 }}</el-link>
            </template>
          </el-table-column>
          <el-table-column prop="sumTime" label="未巡次数"  align="center">
            <template slot-scope="scope">
              <el-link type="primary" :underline="false" @click="goTaskList(scope, scope.row.unfinished || 0 )">{{ scope.row.unfinished || 0 }}</el-link>
            </template>
          </el-table-column>
          <el-table-column prop="percentage" label="巡检完成率"  align="center">
            <template slot-scope="scope">
              <span>{{scope.row.percentage + '%'}}</span>
            </template>
          </el-table-column>
      </el-table>
      <div class="paging">
        <el-pagination
          style="margin-top: 10px;"
          :current-page="paginationData.pageNo"
          :page-sizes="[10, 30, 50, 100]"
          :page-size="paginationData.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="paginationData.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
// 引入echarts组件
// let echarts = require('echarts/lib/echarts')
// require('echarts/lib/chart/bar')
// require('echarts/lib/component/tooltip')
// require('echarts/lib/component/graphic')
// require('echarts/lib/component/legend')
import * as echarts from 'echarts'
import axios from 'axios'
export default {
  components: {},
  props: ['filters'],
  data() {
    return {
      tableData: [],
      echartsData: [],
      paginationData: {
        pageNo: 1,
        pageSize: 10,
        total: 0
      },
      dataTime: []
    }
  },
  mounted() {
    this.getData()
    this.getEchartsData()
  },
  methods: {
    setEcharts() {
      let myChart1 = echarts.init(this.$refs.taskAnalysis)
      let myChart2 = echarts.init(this.$refs.timeAnalysis)
      let data = []
      this.echartsData.map((i) => {
        // 层级为房间时再push
        // if (i.teamName.length < 5) {
        data.push(i.teamName)
        // } else {
        //   data.push(i.teamName.slice(0,4) + '...')
        // }
      })
      let xData1 = []
      this.echartsData.filter((i) => xData1.push(i.hasCount))
      let xData2 = []
      this.echartsData.filter((i) => xData2.push(i.count - i.hasCount))
      myChart1.setOption({
        title: {
          text: '部门巡检任务分析',
          textStyle: {
            fontWeight: 600,
            fontSize: 14
          },
          left: 'center',
          bottom: 10
        },
        legend: {
          data: ['已巡任务', '未巡任务'],
          left: '10%',
          icon: 'circle'
        },
        xAxis: {
          type: 'category',
          data: data,
          nameLocation: 'start',
          axisLabel: {
            interval: 0,
            formatter(val) {
              if (val.length > 5) {
                return val.slice(0, 4) + '...'
              } else {
                return val
              }
            }
          }
        },
        yAxis: {
          type: 'value',
          axisLine: {
            show: false
          }
        },
        tooltip: {},
        series: [
          {
            data: xData1,
            type: 'bar',
            stack: 'one',
            name: '已巡任务',
            color: '#61A5E8',
            barMaxWidth: 40,
            barMinWidth: 20
          },
          {
            data: xData2,
            type: 'bar',
            stack: 'one',
            name: '未巡任务',
            color: '#7ECF51',
            barMaxWidth: 40,
            barMinWidth: 20
          }
        ],
        dataZoom: [
          {
            type: 'slider',
            realtime: true,
            startValue: 0,
            endValue: 5,
            height: 4,
            fillerColor: 'rgba(17, 100, 210, 0.42)', // 滚动条颜色
            borderColor: 'rgba(17, 100, 210, 0.12)',
            handleSize: 0, // 两边手柄尺寸
            showDetail: false, // 拖拽时是否展示滚动条两侧的文字
            top: '96%',
            zoomLock: true // 是否只平移不缩放
            // moveOnMouseMove:true, //鼠标移动能触发数据窗口平移
            // zoomOnMouseWheel :true, //鼠标移动能触发数据窗口缩放
          },
          {
            type: 'inside', // 支持内部鼠标滚动平移
            start: 0,
            end: 40,
            zoomOnMouseWheel: false, // 关闭滚轮缩放
            moveOnMouseWheel: true, // 开启滚轮平移
            moveOnMouseMove: true // 鼠标移动能触发数据窗口平移
          }
        ]
      })
      let pieData = []
      this.echartsData.forEach((i) => {
        const item = {}
        item.value = i.sumTime || 0
        item.name = i.teamName
        item.completion = i.percentage + '%'
        item.label = {}
        pieData.push(item)
      })
      pieData.sort((a, b) => a.sumTime - b.sumTime)
      if (pieData.length > 0) {
        pieData[0].label = {
          show: true,
          position: 'center',
          fontSize: 14,
          color: '#989898',
          formatter(params) {
            let text = params.name
            let value_format = params.value
            if (text.length > 5) {
              return (text = `{time|${value_format}}小时\n${text.slice(0, 5) + '...' + '(' + params.data.completion + ')'}`)
            } else {
              return (text = `{time|${value_format}}小时\n${text + '(' + params.data.completion + ')'}`)
            }
          },
          rich: {
            time: {
              fontSize: 30,
              color: '#666',
              lineHeight: 35
            }
          }
        }
        console.log(pieData)
      }
      myChart2.setOption({
        title: {
          text: '巡检耗时分析',
          textStyle: {
            fontWeight: 600,
            fontSize: 14
          },
          left: 'center',
          bottom: 10
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          top: '5%',
          left: 'center',
          type: 'scroll',
          // selectedMode: false,
          selected: {},
          formatter: function (name) {
            let newName = ''
            if (name.length > 5) {
              newName = name.slice(0, 5) + '...'
            } else if (name.length <= 5 && name.length > 0) {
              newName = name
            }
            return newName
          }
        },
        series: [
          {
            name: '工时(小时)',
            type: 'pie',
            radius: ['50%', '65%'],
            avoidLabelOverlap: false,
            label: {
              normal: {
                show: false,
                position: 'center'
              }
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 14,
                color: '#989898',
                backgroundColor: '#fff',
                width: 135,
                formatter(params) {
                  let text = params.name
                  let value_format = params.value
                  if (text.length > 5) {
                    return (text = `{time|${value_format}}小时\n${text.slice(0, 5) + '...' + '(' + params.data.completion + ')'}`)
                  } else {
                    return (text = `{time|${value_format}}小时\n${text + '(' + params.data.completion + ')'}`)
                  }
                },
                rich: {
                  time: {
                    fontSize: 30,
                    color: '#666',
                    lineHeight: 35
                  }
                }
              }
            },
            labelLine: {
              show: false
            },
            data: pieData
          }
        ]
      })
    },
    handleSizeChange(val) {
      this.paginationData.pageNo = 1
      this.paginationData.pageSize = val
      this.getData()
    },
    handleCurrentChange(val) {
      this.paginationData.pageNo = val
      this.getData()
    },
    getEchartsData(data) {
      const params = {}
      if (data) {
        params.distributionTeamId = ''
        params.taskStartTime = ''
        params.taskEndTime = ''
      } else {
        params.distributionTeamId = this.filters.structureType
        params.taskStartTime = this.filters.timeIterval[0]
        params.taskEndTime = this.filters.timeIterval[1]
        params.exceptWeekend = this.filters.skipDate
        params.cycleTypes = this.filters.cycleType ? this.filters.cycleType.join(',') : ''
      }
      params.pageSize = 9999
      params.pageNo = 1
      this.$api.ipsmGetTasks(params).then((res) => {
        if (res.code == '200') {
          this.echartsData = res.data.list
          this.setEcharts()
        }
      })
    },
    getData(data) {
      const params = {}
      if (data) {
        params.distributionTeamId = ''
        params.taskStartTime = ''
        params.taskEndTime = ''
      } else {
        params.distributionTeamId = this.filters.structureType
        params.taskStartTime = this.filters.timeIterval[0]
        params.taskEndTime = this.filters.timeIterval[1]
        params.exceptWeekend = this.filters.skipDate
        params.cycleTypes = this.filters.cycleType ? this.filters.cycleType.join(',') : ''
      }
      params.pageSize = this.paginationData.pageSize
      params.pageNo = this.paginationData.pageNo
      this.$api.ipsmGetTasks(params).then((res) => {
        if (res.code == '200') {
          res.data.list.forEach((i) => {
            this.dataTime.push(i.sumTime)
          })
          this.tableData = res.data.list
          this.paginationData.total = res.data.sum
          this.setEcharts()
        }
      })
    },
    exportList() {
      const LOGINDATA = JSON.parse(sessionStorage.getItem('LOGINDATA'))
      const params = {
        unitCode: LOGINDATA.unitCode,
        hospitalCode: LOGINDATA.hospitalCode,
        taskStartTime: this.filters.timeIterval[0] || '',
        taskEndTime: this.filters.timeIterval[1] || '',
        exceptWeekend: this.filters.skipDate || '',
        cycleTypes: this.filters.cycleType ? this.filters.cycleType.join(',') : '',
        distributionTeamId: this.filters.structureType
      }
      const formData = new FormData()
      Object.keys(params).forEach((key) => {
        formData.append(key, params[key])
      })
      axios({
        method: 'post',
        url: __PATH.VUE_AQ_URL + 'planTaskNew/departmentTaskExport',
        data: formData,
        responseType: 'blob',
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      })
        .then((res) => {
          let name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
          const blob = new Blob([res.data])
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
        })
        .catch((err) => {
          console.log(err)
          this.$message.error('导出失败')
        })
    },
    goTaskList(scope, count) {
      if (count == 0) return this.$message.error('暂无更多')
      let status = ''
      if (scope.column.label == '应巡次数') {
        status = 0
      } else if (scope.column.label == '已巡次数') {
        status = 2
      } else if (scope.column.label == '未巡次数') {
        status = 1
      }
      this.$router.push({
        name: 'taskDataStatistics',
        query: {
          TeamName: scope.row.teamName,
          filters: JSON.stringify(this.filters),
          status,
          from: 'taskAnlysis'
        }
      })
    }
  }
}
</script>
<style scoped>
.echartsWrap {
  display: flex;
  justify-content: flex-start;
  margin: 20px 0;
}

.charts {
  width: 500px;
  height: 300px;
}
</style>
