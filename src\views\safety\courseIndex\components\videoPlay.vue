<template>
  <PageContainer :footer="true">
    <div slot="content" class="content" style="height: 100%">
      <div class="answer_top">
        <p>{{ itemInfo.periodName }}</p>
      </div>
      <div class="answer_content">
        <div class="content_left">
          <video-play
            ref="videoPlay"
            :flag="idName"
            :src="srcUrl"
            :type="type"
            :viewState="itemInfo.viewState"
            :frequency="frequency"
            @playEnd="playEnd"
            class="videoPlay"
          ></video-play>
        </div>
        <div class="content_right">
          <div :class="['videoItem', item.active ? 'active' : '']" v-for="(item, index) in listInfo" :key="index" @click="switchVideo(item)">
            <p>{{ item.periodName }}</p>
            <span v-if="type != 'see'">{{ item.periodSpace || 0 }}%</span>
          </div>
        </div>
      </div>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="onCancel">取消</el-button>
    </div>
  </PageContainer>
</template>

<script type="text/ecmascript-6">
import videoPlay from '@/components/video/dplayer' // 视频
//   import videoPlay from '@/components/video/video' // 视频
import moment from 'moment'
export default {
  name: 'answerIng',
  components: {
    videoPlay
  },
  data() {
    return {
      itemInfo: '',
      courseId: this.$route.query.courseId || '', //课程id
      id: this.$route.query.id || '', // 课时id
      type: this.$route.query.type || '',
      frequency: '',
      frequencyNum: 0,
      timer: null,
      listInfo: [],
      idName: 'play',
      lastTime: 0,
      srcUrl: '',
      routeInfo: {}
    }
  },
  created() {
    this.routeInfo = JSON.parse(sessionStorage.getItem('routeInfo'))
    this.frequency = this.$route.query.frequency
    this.getDetails(this.id)
  },
  methods: {
    // 获取详情
    getDetails(id) {
      let httpStr = this.type == 'see' ? 'getCourseDetails' : 'myCourseDetails'
      let data = {
        id: this.courseId
      }
      let params = {
        courseId: this.courseId,
        userId: this.routeInfo.userId
      }
      this.$api[httpStr](this.type == 'see' ? data : params).then((res) => {
        if (res.code == '200') {
          this.listInfo = res.data.coursePeriodDTOList.filter((i) => i.type == 1)
          this.listInfo.forEach((i) => {
            i.active = false
            if (i.id == id) {
              i.active = true
              this.itemInfo = i
              this.$refs.videoPlay.playedTime = this.itemInfo.userDuration
              this.$refs.videoPlay.lastTime = this.itemInfo.userDuration
              this.$refs.videoPlay.viewState = this.itemInfo.viewState
              this.lastTime = this.itemInfo.userDuration
            }
          })
          this.srcUrl = JSON.parse(this.itemInfo.url)[0].url
          console.log(this.itemInfo, 'this.itemInfo.url')
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    playEnd(val) {
      // 如果是查看或者没有试题,直接不处理
      if (this.type == 'see' || !this.itemInfo.questionCount) {
        return
      }
      if (val && !this.itemInfo.viewState) {
        this.$confirm('是否要开始考试?', '提示', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.saveStudy()
          setTimeout(() => {
            this.$router.push({
              path: 'answerIng',
              query: {
                periodId: this.itemInfo.id, //课时id
                courseId: this.itemInfo.courseId //课程id
              }
            })
          }, 500)
        })
      }
    },
    onCancel() {
      // 如果是分配给自己的，需要保存学习记录
      if (this.type != 'see' && !this.itemInfo.viewState) {
        this.saveStudy()
      }
      setTimeout(() => {
        this.$router.go(-1)
      }, 500)
    },
    // 切换视频
    switchVideo(item) {
      if (item.id == this.itemInfo.id) return
      this.$confirm('是否要切换视频?', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 如果不是自己的视频并且观看状态没有观看完需要保存记录
        if (this.type != 'see' && !this.itemInfo.viewState) {
          this.saveStudy(item, 'switch')
        } else {
          this.getDetails(item.id)
        }
        setTimeout(() => {
          this.$refs.videoPlay.dp.switchVideo({
            url: JSON.parse(this.itemInfo.url)[0].url,
            lastTime: this.itemInfo.userDuration
          })
        }, 500)
      })
    },
    // 保留学习记录
    saveStudy(item, type) {
      let allTime = Math.round(this.$refs.videoPlay.dp.video.duration)
      let videoDuration = Math.round(this.$refs.videoPlay.dp.video.currentTime)
      let params = {
        courseId: this.itemInfo.courseId, //课程id
        duration: allTime, //视频总长
        periodId: this.itemInfo.id, //课时id
        userId: this.routeInfo.userId,
        textDuration: 0,
        videoDuration: videoDuration //阅读时长
      }
      this.$api.seeClassHour(params).then((res) => {
        if (res.code == '200') {
          this.$message.success('保留学习成功')
          if (type == 'switch') {
            this.getDetails(item.id)
          }
        } else {
          this.$message.error(res.msg)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  padding: 16px 24px 24px 24px;
  background-color: #fff;
  .answer_top {
    height: 48px;
    color: #7f848c;
    font-size: 14px;
    p {
      font-size: 20px;
      font-weight: 600;
      color: #333;
      margin-bottom: 16px;
      span {
        font-weight: normal;
      }
    }
  }
  .answer_content {
    height: calc(100% - 40px);
    display: flex;
    .content_left {
      flex: 1;
      height: 100%;
      //   margin-right: 24px;
      background-color: #fff;
    }
    .content_right {
      width: 324px;
      height: 100%;
      overflow: auto;
      //   padding: 0 24px;
      .videoItem {
        height: 50px;
        line-height: 50px;
        font-size: 16px;
        display: flex;
        padding: 0 24px;
        cursor: pointer;
        span {
          width: 24px;
          margin-left: 24px;
        }
        p {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-weight: 500;
        }
      }
      .active {
        background-color: #3562db;
        border-radius: 4px;
        color: #fff;
      }
    }
  }
}
</style>
