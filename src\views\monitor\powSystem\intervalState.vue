<!-- 区间状态配置 -->
<!--
 * @Author: hedd
 * @Date: 2023-04-06 16:32:35
 * @LastEditTime: 2024-07-24 15:59:23
 * @FilePath: \ihcrs_pc\src\views\monitor\monitoringConfig\cameraConfiguration.vue
 * @Description:
-->
<template>
  <monitorSetting :projectCode="monitorData.projectCode" :monitorData="monitorData"></monitorSetting>
</template>
<script>
import monitorSetting from './monitorSetting'
// import { monitorTypeList } from '@/util/dict.js'
export default {
  name: 'intervalState',
  components: { monitorSetting },
  beforeRouteEnter(to, from, next) {
    let names = []
    to.matched.map((v, i) => {
      if (i > 0) {
        v.components.default.name && names.push(v.components.default.name)
      }
    })
    // 进入页面时，先将当前页面的 name 信息存入 keep-alive 全局状态
    setTimeout(() => {
      next((vm) => {
        vm.$store.commit('keepAlive/add', names)
      })
    }, 0)
  },
  async beforeRouteLeave(to, from, next) {
    // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
    if (!['intervalMonitorForm'].includes(to.name)) {
      // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      monitorData: {
        projectName: '区间状态配置',
        // projectCode: 'IEMC-Electricity',
        projectCode: '',
        formPath: 'intervalState/intervalMonitorForm',
        hasImportBtn: true
      }
      // projectCode: monitorTypeList.find(item => item.projectName == '区间状态配置').projectCode
    }
  },
  computed: {},
  created() {},
  methods: {}
}
</script>
<style lang="scss" scoped>
// 123
</style>
