<template>
  <PageContainer>
    <div slot="content" class="alarmConfiguration-body">
      <div class="alarmConfiguration-content-title">
        <div class="title">报警配置</div>
        <el-button type="primary" icon="el-icon-plus" @click="handleListEvent('add')">新增</el-button>
      </div>
      <div class="alarmConfiguration-content">
        <div style="height: 100%">
          <div class="search-from">
            <div>
              <el-select v-model="searchForm.alarmSystemCode" placeholder="请选择报警系统" @change="alarmSystemChange">
                <el-option v-for="item in alarmSystemList" :key="item.thirdSystemCode" :label="item.thirdSystemName"
                  :value="item.thirdSystemCode"></el-option>
              </el-select>
              <el-select v-model="searchForm.alarmTypeCode" placeholder="请选择报警类型" filterable
                :disabled="!searchForm.alarmSystemCode" class="ml-16">
                <el-option v-for="item in alarmTypeList" :key="item.id" :label="item.alarmDictName" :value="item.id">
                </el-option>
              </el-select>
              <el-select v-model="searchForm.alarmLevel" placeholder="请选择报警等级" class="ml-16">
                <el-option v-for="item in alarmLevelList" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
              <el-button type="primary" plain style="margin-left: 10px" @click="reset">重置</el-button>
              <el-button type="primary" @click="search">查询</el-button>
            </div>
          </div>
          <div class="contentTable">
            <div class="contentTable-main table-content">
              <el-table v-loading="tableLoading" border style="width: 100%" :data="tableData" :height="tableHeight">
                <el-table-column prop="alarmSystemName" label="报警系统" show-overflow-tooltip></el-table-column>
                <el-table-column prop="alarmTypeName" label="报警类型" show-overflow-tooltip></el-table-column>
                <el-table-column prop="alarmLevel" label="报警等级" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <div v-for="(item, index) in scope.row.alarmConfigs" :key="index" class="columnList">
                      <span :class="['alarmState', `alarmState${item.alarmLevel}`]">
                        {{ alarmLevelText[item.alarmLevel].text || '' }}
                      </span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="alarmModel" label="进入战时" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <div v-for="(item, index) in scope.row.alarmConfigs" :key="index" class="columnList">
                      <span> {{ item.alarmModel == 0 ? '是' : '否' }}</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="preplanName" label="启动预案" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <div v-for="(item, index) in scope.row.alarmConfigs" :key="index" class="columnList">
                      <span> {{ item.preplanName || '--' }}</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="workOrderName" label="自动派发工单" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <div v-for="(item, index) in scope.row.alarmConfigs" :key="index" class="columnList">
                      <span> {{ item.autoSendOrder == 0 ? '否' : '是' }}</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="workOrderName" label="超时提升等级" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <div v-for="(item, index) in scope.row.alarmConfigs" :key="index" class="columnList">
                      <span> {{ item.upgraded == '0' ? '是' : '否' }}</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="alarmHandle" label="通知方式" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <div v-for="(item, index) in scope.row.alarmConfigs" :key="index" class="columnList"
                      @click="toNoticeWay(item.id)">
                      <span
                        v-if="item.alarmHandle && item.alarmHandle.split(',') && item.alarmHandle.split(',').length == 1 && item.alarmHandle.split(',').includes('0')">
                        报警产生时</span>
                      <span
                        v-if="item.alarmHandle && item.alarmHandle.split(',') && item.alarmHandle.split(',').length == 1 && item.alarmHandle.split(',').includes('1')">
                        确警时</span>
                      <span
                        v-if="item.alarmHandle && item.alarmHandle.split(',') && item.alarmHandle.split(',').length == 2">
                        报警产生时/确警时</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="180" fixed="right">
                  <template slot-scope="scope">
                    <el-button type="text" class="record" @click="goDetail(scope.row)">查看</el-button>
                    <el-button type="text" class="record" @click="operation(scope.row, 'edit')">编辑</el-button>
                    <el-button type="text" class="record" @click="operation(scope.row, 'delete')">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="contentTable-footer">
              <el-pagination :current-page="pagination.current" :page-sizes="pagination.pageSizeOptions"
                :page-size="pagination.size" :layout="pagination.layoutOptions" :total="pagination.total"
                @size-change="paginationSizeChange" @current-change="paginationCurrentChange">
              </el-pagination>
            </div>
          </div>
        </div>
        <el-drawer title="通知方式" :visible.sync="drawer" :direction="direction" :before-close="handleClose" size="39%">
          <div v-if="produceList.length || sureList.length" class="noticeBody">
            <div v-if="produceList && produceList.length" class="produceBox">
              <div v-for="(item, index) in produceList" :key="index" class="produceBox-content">
                <div class="header">
                  <span class="noticeWay"> 通知方式：{{ noticeWayText[item.way].text || '' }} </span>
                  <span class="noticeDeptOrPerson"> 通知部门或人员：维修小组（{{ produceList.length ? produceList.length : 0 }}）
                  </span>
                  <span class="noticeContactWay"> 联系方式 </span>
                  <span class="noticeContactWay" @click="() => (produceUnfold = !produceUnfold)">
                    <i :class="[produceUnfold ? ' el-icon-arrow-up' : 'el-icon-arrow-down']"></i>
                  </span>
                </div>
                <div v-show="!produceUnfold">
                  <div v-for="(el, indexI) in item.persons" :key="indexI">
                    <span class="noticeWay"> </span>
                    <span class="noticeDeptOrPerson">
                      {{ el.personName || el.departName }}
                    </span>
                    <span class="noticeContactWay">
                      {{ el.tel || '' }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div v-if="sureList && sureList.length" class="produceBox">
              <div class="produceBox-title">点击确警时</div>
              <div v-for="(item, index) in sureList" :key="index" class="produceBox-content">
                <div class="header">
                  <span class="noticeWay"> 通知方式：{{ noticeWayText[item.way].text || '' }} </span>
                  <span class="noticeDeptOrPerson"> 通知部门或人员：维修小组（{{ sureList.length ? sureList.length : 0 }}） </span>
                  <span class="noticeContactWay"> 联系方式 </span>
                  <span class="noticeContactWay" @click="() => (sureUnfold = !sureUnfold)">
                    <i :class="[sureUnfold ? ' el-icon-arrow-up' : 'el-icon-arrow-down']"></i>
                  </span>
                </div>
                <div v-show="!sureUnfold">
                  <div v-for="(el, indexI) in item.persons" :key="indexI">
                    <span class="noticeWay"> </span>
                    <span class="noticeDeptOrPerson">
                      {{ el.personName || el.departName }}
                    </span>
                    <span class="noticeContactWay">
                      {{ el.tel || '' }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-else class="noticeBody">暂无数据</div>
        </el-drawer>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin.js'
export default {
  name: 'alarmConfigIndex',
  mixins: [tableListMixin],
  beforeRouteEnter(to, from, next) {
    // 进入页面时，先将当前页面的 name 信息存入 keep-alive 全局状态（当前页面的name需要和路由的name一致）
    next((vm) => {
      // 二级页面存储当前级，多级页面存储多级
      vm.$store.commit('keepAlive/add', 'alarmConfigIndex')
    })
  },
  async beforeRouteLeave(to, from, next) {
    // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
    if (!['addAlarmConfig'].includes(to.name)) {
      // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      drawer: false,
      direction: 'rtl',
      searchForm: {
        alarmSystemCode: '', // 报警系统
        alarmTypeCode: '', // 报警类型
        alarmLevel: '' // 报警等级
      },
      alarmSystemList: [],
      alarmTypeList: [],
      alarmLevelList: [
        {
          value: '3',
          label: '重要'
        },
        {
          value: '2',
          label: '紧急'
        },
        {
          value: '1',
          label: '一般'
        },
        {
          value: '0',
          label: '通知'
        }
      ],
      alarmLevelText: {
        3: {
          text: '重要'
        },
        2: {
          text: '紧急'
        },
        1: {
          text: '一般'
        },
        0: {
          text: '通知'
        }
      },
      noticeWayText: {
        0: {
          text: '短信'
        },
        1: {
          text: '语音电话'
        },
        // '2': {
        //   text: '企业微信'
        // },
        // '3': {
        //   text: '微信服务号'
        // },
        4: {
          text: '系统通知'
        }
      },
      tableLoading: false,
      tableData: [],
      produceList: [],
      sureList: [],
      produceUnfold: false,
      sureUnfold: false
    }
  },
  activated() {
    this.getDataList()
  },
  mounted() {
    this.getAlarmSystemList()
    this.getDataList()
  },
  methods: {
    // 详情弹窗关闭
    handleClose() {
      this.drawer = false
    },
    // 列表数据
    getDataList() {
      this.tableLoading = true
      let data = {
        ...this.searchForm,
        page: this.pagination.current,
        pageSize: this.pagination.size
      }
      this.$api.getAlarmConfigList(data).then((res) => {
        this.tableLoading = false
        if (res.code == 200) {
          this.tableData = res.data ? res.data.records : []
          this.pagination.total = res.data ? res.data.total : 0
        } else if (res.message) {
          this.tableData = []
          this.pagination.total = 0
          this.$message.error(res.message)
        }
      })
    },
    // 报警系统
    getAlarmSystemList() {
      this.$api.getAlarmThirdSystemData().then((res) => {
        if (res.code == 200) {
          this.alarmSystemList = res.data
        }
      })
    },
    // 获取实体类型
    getAlarmTypeData(thirdSystemCode) {
      let params = {
        thirdSystemCode: thirdSystemCode
      }
      this.$api.getAlarmThirdTypeData(params).then((res) => {
        if (res.code == 200) {
          this.alarmTypeList = res.data
        }
      })
    },
    // 报警系统改变
    alarmSystemChange(el) {
      this.getAlarmTypeData(el)
    },
    handleListEvent() {
      this.$router.push('/alarmConfig/alarmConfigIndex/addAlarmConfig')
    },
    // 重置
    reset() {
      this.searchForm = {
        alarmSystemCode: '', // 报警系统
        alarmTypeCode: '', // 报警类型
        alarmLevel: '' // 报警等级
      }
      this.pagination.size = 15
      this.pagination.current = 1
      this.getDataList()
    },
    // 查询
    search() {
      this.pagination.current = 1
      this.getDataList()
    },
    // 查询报警配置的通知方式和通知人员
    getConfigPerson(alarmConfigId) {
      this.produceList = []
      this.sureList = []
      this.$api.getAlarmConfigNoticePerson({ alarmConfigId: alarmConfigId }).then((res) => {
        if (res.code == 200) {
          res.data.forEach((el) => {
            if (el.alarmConfigType === '0') {
              this.produceList = el.noticeWay
            } else if (el.alarmConfigType === '1') {
              this.sureList = el.noticeWay
            }
          })
        }
      })
    },
    // 通知方式详情
    toNoticeWay(e) {
      this.getConfigPerson(e)
      this.drawer = true
    },
    // 列表操作
    operation(row, type) {
      // 编辑
      if (type == 'edit') {
        this.$router.push({
          path: '/alarmConfig/alarmConfigIndex/addAlarmConfig',
          query: {
            baseId: row.baseId
          }
        })
      } else if (type == 'delete') {
        this.$confirm('确认删除该报警配置吗？', '信息提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '继续删除',
          cancelButtonText: '放弃',
          type: 'warning'
        })
          .then(() => {
            this.$api.deletAlarmConfigList({ baseId: row.baseId }, { 'operation-type': 3, 'operation-name': row.alarmSystemName, 'operation-id': row.baseId }).then((res) => {
              if (res.code == '200') {
                this.$message({
                  type: 'success',
                  message: '删除成功'
                })
                this.getDataList()
              } else {
                this.$message({
                  type: 'error',
                  message: res.message || '删除失败'
                })
              }
            })
          })
          .catch(() => { })
      } else if (type == 'check') {
        this.$router.push({
          path: '/alarmConfiguration/alarmConfigurationDetail',
          query: {
            planId: row.id
          }
        })
      }
    },
    // 详情
    goDetail(row) {
      this.$router.push({
        path: '/alarmConfig/alarmConfigIndex/addAlarmConfig',
        query: {
          baseId: row.baseId,
          activeType: 'detail'
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.alarmConfiguration-body {
  height: 100%;
  width: 100%;
  background: #fff;

  .alarmConfiguration-content-title {
    display: flex;
    justify-content: space-between;
    padding: 23px 24px 0 24px;

    .title {
      font-weight: bold;
      font-size: 16px;
      color: #333333;
    }
  }

  .alarmConfiguration-content {
    padding: 8px 24px;
    height: calc(100% - 50px);

    .search-from {
      padding-bottom: 12px;
      display: flex;
      justify-content: space-between;

      &>div {
        margin-right: 10px;
      }

      &>button {
        margin-top: 12px;
      }
    }

    .contentTable {
      margin-top: 16px;
      height: calc(100% - 80px);
      display: flex;
      flex-direction: column;

      .contentTable-main {
        flex: 1;
        overflow: auto;
      }

      .contentTable-footer {
        padding: 10px 0 0;
      }

      .alarmState {
        padding: 2px 8px;
        background: #e6effc;
        border-radius: 4px;
      }

      .alarmState3 {
        background: #ffece8;
        color: #cb2634;
      }

      .alarmState2 {
        background: #fff7e8;
        color: #d25f00;
      }

      .alarmState1 {
        background: #e8ffea;
        color: #009a29;
      }

      .alarmState0 {
        background: #e6effc;
        color: #2749bf;
      }
    }
  }
}

.record {
  color: #3562db !important;
}

.ml-16 {
  margin-left: 16px;
}

.columnList {
  cursor: default;
  font-style: 12px;
  color: #7f848c;
  display: flex;

  div {
    width: 30%;
    margin-right: 20px;
    padding: 5px;
  }
}

.down-popover {
  .groupTips {
    font-weight: 400;
    font-size: 14px;
    color: #666666;

    .title {
      width: 30%;
      text-align: left;
    }

    .extra {
      height: 30px;
      margin-top: 5px;
    }

    .group {
      width: 40%;
      text-align: left !important;

      .groupType {
        display: inline-block;
        padding: 1px 4px;
        border-radius: 2px;
        border: 1px solid #dcdfe6;
        color: #cacaca;
        margin-left: 8px;
        font-size: 10px;
      }

      .groupLable {
        display: inline-block;
        height: 16px;
        width: 100px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }

    .phone {
      width: 30%;
      text-align: left;
    }

    .bubble {
      display: inline-block;
      width: 31px;
      height: 16px;
      background: #e4e7ed;
      border-radius: 99px 99px 99px 99px;
      font-weight: 400;
      font-size: 12px;
      color: #666666;
      line-height: 14px;
      text-align: center;
      margin: 0px 10px;
    }
  }
}

::v-deep .popoverBtn.el-button--text {
  color: #666666 !important;
}

::v-deep .el-dialog__body {
  padding: 10px 20px !important;
}

.noticeBody {
  padding: 0 20px;
}

.produceBox {
  .produceBox-title {
    padding: 5px 0;
    font-size: 16px;
  }

  .produceBox-content {
    .noticeWay {
      display: inline-block;
      width: 150px;
    }

    .noticeDeptOrPerson {
      display: inline-block;
      width: 240px;
      margin-left: 60px;
      text-align: left;
    }

    .noticeContactWay {
      margin-left: 60px;
    }
  }
}
</style>
