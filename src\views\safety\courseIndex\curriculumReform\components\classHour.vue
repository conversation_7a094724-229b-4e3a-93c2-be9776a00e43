<template>
  <div class="inner">
    <el-form
      label-width="120px"
      :model="classHourInfo"
      ref="classHourInfo"
      class="demo-form-inline"
    >
      <div
        class="classHourItem"
        v-for="(item, index) in classHourInfo.classList"
        :key="index"
      >
        <!-- 课程目录 -->
        <div class="top">
          <span>课程目录</span>
          <span @click="delClassList(index)"
            ><i class="el-icon-delete"></i>删除课时</span
          >
        </div>
        <div class="classHourInfo">
          <div>
            <el-form-item
              label="课时名称"
              :prop="`classList.${index}.periodName`"
              :rules="classHourRules.periodName"
            >
              <el-input
                v-model="item.periodName"
                placeholder="请输入课时名称"
                show-word-limit
                maxlength="30"
                style="width: 300px"
              ></el-input>
            </el-form-item>
            <el-form-item
              label="课时类型"
              :prop="`classList.${index}.type`"
              :rules="classHourRules.type"
            >
              <el-select
                v-model="item.type"
                placeholder="请选择课时类型"
                style="width: 300px"
                @change="classTypeBtn(item)"
              >
                <el-option
                  v-for="item in classHourTypeList"
                  :key="item.id"
                  :label="item.label"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item v-if="item.type != '1'" label="课时阅读时长">
              <el-input
                v-model="item.duration"
                placeholder="请输入阅读时长"
                min="1"
                style="width: 300px"
              >
                <template slot="append">分钟</template>
              </el-input>
            </el-form-item>
          </div>
          <el-form-item
            v-if="item.type == '1'"
            label="课时视频"
            :prop="`classList.${index}.fileEcho`"
            :rules="classHourRules.fileEcho"
            class="fileClass"
          >
            <el-upload
              ref="uploadFile"
              drag
              multiple
              class="video_file video"
              action="string"
              :file-list="item.fileEcho"
              :http-request="
                () => {
                  return httpRequest(item,'0');
                }
              "
              accept=".MP4,.AVI,.WMV,.MPEG"
              :limit="1"
              :on-exceed="handleExceed"
              :before-upload="
                (file) => {
                  return beforeAvatarUpload(item,file,'0');
                }"
              :on-remove="
                (file, fileList) => {
                  return handleRemove(file, fileList, item);
                }
              "
              :on-change="
                (file, fileList) => {
                  return fileChange(file, fileList, item);
                }
              "
            >
              <i class="iconfont icon-shipin"></i>
              <div class="el-upload__text" style="top: 30px">添加视频</div>
              <div slot="tip" class="el-upload__tip">
                仅能添加一个视频，支持MP4、AVL、WMV、MPEG格式；文件最大不超过5G
              </div>
            </el-upload>
          </el-form-item>
          <el-form-item
            v-if="item.type == '2'"
            label="课时文档"
            :prop="`classList.${index}.fileEcho`"
            :rules="classHourRules.fileEcho"
            class="fileClass"
          >
            <el-upload
              ref="uploadFile"
              drag
              multiple
              class="video_file video"
              action="string"
              :file-list="item.fileEcho"
              :http-request="
                () => {
                  return httpRequest(item,'1');
                }
              "
              accept=".pdf,.doc,.docx,.pptx,.png,.jpg"
              :limit="1"
              :on-exceed="handleExceed"
              :before-upload="
                (file) => {
                  return beforeAvatarUpload(item,file,'1');
                }"
              :on-remove="
                (file, fileList) => {
                  return handleRemove(file, fileList, item);
                }
              "
              :on-change="
                (file, fileList) => {
                  return fileChange(file, fileList, item);
                }
              "
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text" style="top: 30px">添加文档</div>
              <div slot="tip" class="el-upload__tip">
                仅能添加一个文档，支持pdf、doc、pptx、png、jpg
              </div>
            </el-upload>
          </el-form-item>
          <el-form-item
            label="课时文本"
            v-if="item.type == '3'"
            :prop="`classList.${index}.document`"
            :rules="classHourRules.document"
          >
            <Editor
              ref="myTextEditor"
              v-model="item.document"
              class="my-editor"
            ></Editor>
          </el-form-item>
        </div>
        <!-- 课后习题 -->
        <div class="exercises">
          <span>课后习题</span>
          <span>建议添加10道题以内</span>
          <span @click="addQuestions(index)"
            ><i class="el-icon-plus"></i>添加试题</span
          >
          <div class="exercisesList" v-if="item.coursePeriodQuestionList.length">
            <div
              v-for="(k, ind) in item.coursePeriodQuestionList"
              :key="k.id"
              :name="k.id"
              :class="['exercisesItem', k.isExpand ? 'expand' : '']"
              :ref="'exercisesItem' + ind"
            >
              <div class="exercisesTop">
                <div class="left">
                  <div class="exercisesType">
                    {{
                      k.type == "1"
                        ? "单选题"
                        : k.type == "2"
                        ? "多选题"
                        : "判断题"
                    }}
                  </div>
                  <span>({{ k.free1 }})</span>
                </div>
                <div class="right">
                  <el-input
                    type="number"
                    placeholder="小题分数"
                    min="1"
                    style="width: 160px"
                    v-model="k.score"
                  >
                    <template slot="append">分</template>
                  </el-input>
                  <i class="el-icon-upload2" @click="moveUp(ind, item)"></i>
                  <i class="el-icon-download" @click="moveDown(ind, item)"></i>
                  <i class="el-icon-delete" @click="deletQuestions(ind,item)"></i>
                  <div class="line"></div>
                  <span @click="isExpandBtn(k, index)">{{
                    k.isExpand ? "折叠" : "展开"
                  }}</span>
                </div>
              </div>
              <div :class="['exercisesName', k.isExpand ? '' : 'title']">
                {{ k.topic }}
              </div>
              <el-radio-group
                v-if="k.type == '1'"
                class="radio"
                v-model="k.answer"
                disabled
              >
                <el-radio
                  v-for="(j, index) in k.options"
                  :key="index"
                  :label="j.id"
                  >{{ j.id }}. {{ j.label }}</el-radio
                >
              </el-radio-group>
              <el-checkbox-group
                v-if="k.type == '2'"
                v-model="k.answer"
                class="radio"
                disabled
              >
                <el-checkbox
                  v-for="(j, index) in k.options"
                  :key="index"
                  :label="j.id"
                  >{{ j.id }}. {{ j.label }}</el-checkbox
                >
              </el-checkbox-group>
              <p>答案：{{ k | getAnswer }}</p>
              <p>
                解析：
                {{ k.analysis }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </el-form>
    <el-button type="primary" @click="addClassHour">添加课时</el-button>
  </div>
</template>
<script>
import Editor from "@components/editor/Editor.vue";
import testQuestions from "../../components/testQuestions.vue";
import axios from "axios";
export default {
  components: {
    Editor,
    testQuestions,
  },
  props: {
    coursePeriodDTOList: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      classHourInfo: {
        classList: [],
      },
      classHourTypeList: [
        {
          id: 1,
          label: "视频",
        },
        {
          id: 2,
          label: "文档",
        },
        {
          id: 3,
          label: "文本",
        },
      ],
      classHourRules: {
        periodName: [
          { required: true, message: "请输入课时名称", trigger: "blur" },
          {
            min: 1,
            max: 30,
            message: "长度在 1 到 30 个字符",
            trigger: "blur",
          },
        ],
        type: [
          { required: true, message: "请选择课时类型", trigger: "change" },
        ],
        fileEcho: [
          { required: true, message: "请选择视频或文档", trigger: "change" },
        ],
        document: [
          { required: true, message: "请输入课时文本", trigger: "blur" },
        ]
        
      },
    };
  },
  created() {
    this.routeInfo = JSON.parse(sessionStorage.getItem("routeInfo"));
    this.classHourInfo.classList = this.coursePeriodDTOList
  },
  filters:{
    getAnswer(val){
      if(val.type=='3'){
        return val.answer=='1'?'正确':'错误'
      }else if(val.type=='2'){
        return val.answer.toString()
      }else{
        return val.answer
      }
    }
  },
  methods: {
    // 课时类型
    classTypeBtn(item) {
      item.fileEcho = [];
      item.url = "";
      item.doucument = "";
    },
    // 校验是否填全
    getValidate(type) {
      this.$refs.classHourInfo.validate((valid) => {
        if (valid) {
          this.$emit("submit", type);
        }
      });
    },
    // 添加试题
    addQuestions(index) {
      this.$emit("addQuestion", index);
    },
    addClassHour() {
      let obj = {
        periodName: "",
        type: 1,
        url: "",
        fileEcho: [],
        coursePeriodQuestionList: [],
      };
      this.classHourInfo.classList.push(obj);
    },
    //删除课时
    delClassList(index) {
      if(this.classHourInfo.classList.length==1){
        return  this.$message.error('保留一个课时')
      }
      this.classHourInfo.classList.splice(index, 1);
    },
    // 展开试题
    isExpandBtn(item,index){
      item.isExpand=!item.isExpand
    },
    // 上移选择项
    moveUp(index, item) {
      if (index != 0) {
        let nums = item.coursePeriodQuestionList;
        [nums[index], nums[index - 1]] = [nums[index - 1], nums[index]];
        item.coursePeriodQuestionList = [...nums];
      }else{
        this.$message.error('禁止上移')
      }
    },
    //下移选择项
    moveDown(index, item) {
      if(item.coursePeriodQuestionList.length&&index!=item.coursePeriodQuestionList.length-1){
        let nums = item.coursePeriodQuestionList;
        [nums[index], nums[index + 1]] = [nums[index + 1], nums[index]];
        item.coursePeriodQuestionList = [...nums];
      }else{
        this.$message.error('禁止下移')
      }
    },
    // 删除试题
    deletQuestions(index,item){
      // if(item.coursePeriodQuestionList.length==1){
      //   return this.$message.error('最少保留一个试题')
      // }
      item.coursePeriodQuestionList.splice(index, 1);
    },
    async fileChange(file, fileList, item) {
      item.fileEcho = fileList;
    },
    // 获取时长
    getTimes(item,file) {
        var content = file;
        //获取录音时长
        var url = URL.createObjectURL(content);
        //经测试，发现audio也可获取视频的时长
        var audioElement = new Audio(url);
        audioElement.addEventListener("loadedmetadata", (_event) => {
           //保存获取时间长度
          item.fileEcho[0].duration = parseInt(audioElement.duration)
        });
    },
    httpRequest(item,type) {
      let formData = new FormData();
      formData.append("file", item.fileEcho[0].raw);
      let url = type=='0'?'minio/uploadVideo':'minio/upload'
      axios({
        method: "post",
        url: __PATH.BASE_URL_LABORATORY + url,
        data: formData,
        headers: {
          token: this.routeInfo.token,
        },
      })
        .then((res) => {
          item.fileEcho[0].url = res.data.data.viewAddress;
          item.url = JSON.stringify(item.fileEcho);
          console.log(item.url,'item.url');
        })
        .catch(() => {
          this.$message.error("上传失败");
        });
    },
    handleExceed() {
      this.$message.error("最多上传一个");
    },
    beforeAvatarUpload(item,file,type) {
      if (file.name.indexOf(",") != -1) {
        this.$message.error("非法的文件名");
        return false;
      }
      if(type=='0'){
        this.getTimes(item,file)
      }
      if(type=='1'){ // 文档
        const isLt20M = file.size / 1024 / 1024 < 20;
        if (!isLt20M) {
          this.$message.error("上传文件大小不能超过 20MB!");
          return false;
        }
      }
    },
    handleRemove(file, fileList, item) {
      item.url = "";
      item.fileEcho = fileList;
    },
  },
};
</script>
<style lang="scss" scoped>
.inner {
  background-color: #f5f5fa;
}
.classHourInfo {
  display: flex;
}
.classHourItem {
  padding: 16px;
  background-color: #fff;
  position: relative;
  margin-bottom: 10px;
  .top {
    display: flex;
    justify-content: space-between;
    span:nth-child(2) {
      cursor: pointer;
      color: red;
      i {
        margin-right: 5px;
      }
    }
  }
}
.exercises {
  span:nth-child(2) {
    color: #ccced3;
    font-size: 14px;
    margin: 0 10px;
  }
  span:nth-child(3) {
    color: #3562db;
    font-size: 14px;
    cursor: pointer;
  }
  .exercisesList {
    margin-top: 10px;
    padding: 16px;
    font-size: 14px;
    background-color: #f5f5fa;
    .exercisesItem {
      height: 100px;
      overflow: hidden;
      background-color: #fff;
      margin-bottom: 16px;
      padding: 16px;
      border-bottom: 4px solid #faf9fc;
      .exercisesTop {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        .left {
          display: flex;
          align-items: center;
          justify-content: center;
          span {
            color: #7f848c;
          }
        }
        .right {
          color: #ccced3;
          display: flex;
          align-items: center;
          .line {
            width: 2px;
            height: 14px;
            margin: 0 10px 0 26px;
            background-color: #dcdfe6;
          }
          span {
            color: #3562db;
            margin-left: 16px;
            cursor: pointer;
          }
          i {
            color: #3562db;
            cursor: pointer;
            margin-left: 16px;
          }
        }
        .exercisesType {
          width: 58px;
          height: 22px;
          line-height: 22px;
          text-align: center;
          border-radius: 4px;
          color: #86909c;
          background-color: #ededf5;
          margin: 0 10px;
        }
      }
      .exercisesName {
        line-height: 20px;
        margin-bottom: 16px;
        word-break: break-all;
      }
      .title {
        overflow: hidden;
        -webkit-line-clamp: 2;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
      }
      .el-radio {
        margin-left: 38px;
        font-size: 14px;
        color: #7f848c !important;
        line-height: 18px;
        display: flex;
      }
      p {
        font-size: 14px;
        color: #7f848c !important;
        line-height: 20px;
        margin-bottom: 16px;
      }
    }
    .expand {
      height: auto;
    }
  }
}
.video_file {
  width: 500px;
  height: 90px;
  position: absolute;
  line-height: 90px;
}

::v-deep .el-collapse-item__arrow {
  display: none;
}
::v-deep .el-collapse-item__header {
  height: 100px;
  display: block;
  flex: none;
  border-bottom: none;
}
.video_file > .el-upload__tip {
  width: 260px;
  line-height: 20px;
  position: relative;
  right: -170px;
  top: -84px;
}
::v-deep .video_file > .el-upload-list {
  position: absolute;
  top: 0;
  width: 260px;
  margin-left: 430px;
  max-height: 100px;
  // overflow: auto;
}
::v-deep .video_file .el-upload .el-upload-dragger {
  width: 160px;
  height: 100px;
}
::v-deep .video_file .el-upload__text {
  position: absolute;
  left: 50px;
  color: #606266;
  font-size: 14px;
  text-align: center;
}
::v-deep .el-upload-dragger .el-icon-upload {
  font-size: 30px;
  color: #c0c4cc;
  margin: 40px 0 16px;
  line-height: 5px;
}
::v-deep .el-upload-dragger .icon-shipin {
  font-size: 30px;
  color: #c0c4cc;
  margin: 40px 0 16px;
  line-height: 5px;
}
::v-deep .ql-container {
  height: calc(100% - 60px);
}
::v-deep .fileClass .el-form-item__error {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
  position: absolute;
  top: 260%;
  width: 300px;
  left: 6px;
}
::v-deep .el-radio {
  display: block;
  margin: 10px 0;
  .el-radio__label {
    white-space: normal;
  }
}
::v-deep .el-checkbox {
  display: block;
  margin: 10px 0;
}
::v-deep .el-checkbox-group {
  margin-left: 38px;
}
</style>
