<template>
  <PageContainer>
    <div slot="header">
      <div class="time">
        <span :class="{ active: timeType == '1' }" @click="changeTimeType('1')">今日</span>
        <span :class="{ active: timeType == '6' }" @click="changeTimeType('6')">本周</span>
        <span :class="{ active: timeType == '2' }" @click="changeTimeType('2')">本月</span>
        <span :class="{ active: timeType == '4' }" @click="changeTimeType('4')">本季度</span>
        <span :class="{ active: timeType == '3' }" @click="changeTimeType('3')">本年</span>
      </div>
      <div class="btns">
        <div :class="['btn', { 'btn-actived': pageType == '1' }]" @click="changePageType('1')">人员分析</div>
        <div :class="['btn', { 'btn-actived': pageType == '2' }]" @click="changePageType('2')">科室分析</div>
        <div :class="['btn', { 'btn-actived': pageType == '3' }]" @click="changePageType('3')">运送类型</div>
        <div :class="['btn', { 'btn-actived': pageType == '4' }]" @click="changePageType('4')">服务起点</div>
        <div :class="['btn', { 'btn-actived': pageType == '5' }]" @click="changePageType('5')">服务终点</div>
        <div :class="['btn', { 'btn-actived': pageType == '6' }]" @click="changePageType('6')">时间分析</div>
      </div>
    </div>
    <div slot="content">
      <div class="cards">
        <div>
          <span>运送总数</span>
          <div class="cost-num">
            <span>
              <span>{{ cardData.all }}</span>
              <span>单</span>
            </span>
            <img src="@/assets/images/service/analysis-1.png" />
          </div>
        </div>
        <div>
          <span>完工率</span>
          <div class="cost-num">
            <span>
              <span>{{ cardData.completionRate }}</span>
              <!-- <span>%</span> -->
            </span>
            <img src="@/assets/images/service/analysis-2.png" />
          </div>
        </div>
        <div>
          <span>平均响应</span>
          <div class="cost-num">
            <span>
              <span>{{ cardData.response }}</span>
              <!-- <span>分钟</span> -->
            </span>
            <img src="@/assets/images/service/analysis-3.png" />
          </div>
        </div>
        <div>
          <span>平均完工</span>
          <div class="cost-num">
            <span>
              <span>{{ cardData.finishTime }}</span>
              <span></span>
            </span>
            <img src="@/assets/images/service/analysis-4.png" />
          </div>
        </div>
        <div>
          <span>满意度</span>
          <div class="cost-num">
            <span>
              <span>{{ cardData.evaluate }}</span>
              <!-- <span>%</span> -->
            </span>
            <img src="@/assets/images/service/analysis-5.png" />
          </div>
        </div>
      </div>
      <div class="chart-box">
        <div class="title">
          <svg-icon name="right-arrow" />
          <span>数据图表（单）</span>
        </div>
        <div v-if="pageType != '3' && pageType != '6'" id="chart1" key="myCharts"></div>
        <div v-if="pageType == '3'" key="circleChart" class="circles-box">
          <div id="circle-chart1"></div>
          <div id="circle-chart2"></div>
          <div id="circle-chart3"></div>
          <div id="circle-chart4"></div>
          <div id="circle-chart5"></div>
          <div id="circle-chart6"></div>
          <div id="circle-chart7"></div>
          <div id="circle-chart8"></div>
          <div id="circle-chart9"></div>
          <div id="circle-chart10"></div>
        </div>
        <div v-if="pageType == '6'" id="chart-time" key="chartTime"></div>
      </div>
      <div class="table-box">
        <div class="title">
          <svg-icon name="right-arrow" />
          <span>数据明细</span>
        </div>
        <el-table v-if="pageType == '1'" key="table1" :data="tableData" border style="width: 100%" height="85%" size="small">
          <el-table-column label="序号" type="index" width="50" align="center" :resizable="false"></el-table-column>
          <el-table-column prop="designate_person_name" label="姓名" width="180" :resizable="false" align="center"></el-table-column>
          <el-table-column prop="allOrder" label="服务量" width="180" show-overflow-tooltip :resizable="false" align="center">
            <template slot-scope="scope">
              <span class="jump-mark" @click="goDetails('person', scope.row)">{{ scope.row.allOrder }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="finishOrder" label="已完工" show-overflow-tooltip :resizable="false" align="center">
            <template slot-scope="scope">
              <span class="jump-mark" @click="goDetails('personfinish', scope.row)">{{ scope.row.finishOrder }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="finishRate" label="完成率" show-overflow-tooltip :resizable="false" align="center"></el-table-column>
          <el-table-column prop="totalWorkHours" label="工时总计" show-overflow-tooltip :resizable="false" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.totalWorkHours }} 小时</span>
            </template>
          </el-table-column>
          <el-table-column prop="response" label="平均响应" show-overflow-tooltip :resizable="false" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.response }} 分钟</span>
            </template>
          </el-table-column>
          <el-table-column prop="responseDiff" label="与标准平均响应" show-overflow-tooltip :resizable="false" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.responseDiff }} 分钟</span>
            </template>
          </el-table-column>
          <el-table-column prop="finishTime" label="平均完工" show-overflow-tooltip :resizable="false" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.finishTime }} 分钟</span>
            </template>
          </el-table-column>
          <el-table-column prop="finishTimeDiff" label="与标准平均完工" show-overflow-tooltip :resizable="false" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.finishTimeDiff }} 分钟</span>
            </template>
          </el-table-column>
        </el-table>
        <el-table v-if="pageType == '2'" key="table2" :data="tableData" border style="width: 100%" height="85%" size="small">
          <el-table-column label="序号" type="index" width="50" align="center" :resizable="false"></el-table-column>
          <el-table-column prop="sources_dept_name" label="科室" :resizable="false" align="center"></el-table-column>
          <el-table-column prop="allOrder" label="服务量" width="180" :resizable="false" align="center">
            <template slot-scope="scope">
              <span class="jump-mark" @click="goDetails('dept', scope.row)">{{ scope.row.allOrder }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="finishOrder" label="已完工" width="180" :resizable="false" align="center">
            <template slot-scope="scope">
              <span class="jump-mark" @click="goDetails('deptfinish', scope.row)">{{ scope.row.finishOrder }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="finishRate" label="完成率" width="180" :resizable="false" align="center"></el-table-column>
          <el-table-column prop="totalWorkHours" label="工时总计" width="180" :resizable="false" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.totalWorkHours }}</span>
              <span>小时</span>
            </template>
          </el-table-column>
          <el-table-column prop="response" label="平均响应" width="180" :resizable="false" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.response }}</span>
              <span>分钟</span>
            </template>
          </el-table-column>
          <el-table-column prop="finishTime" label="平均完工" width="180" :resizable="false" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.finishTime }}</span>
              <span>分钟</span>
            </template>
          </el-table-column>
        </el-table>
        <el-table v-if="pageType == '3'" key="table3" :data="tableData" border style="width: 100%" height="85%" size="small">
          <el-table-column label="序号" type="index" width="80" align="center" :resizable="false"></el-table-column>
          <el-table-column prop="name" label="运送类型" :resizable="false" align="center"></el-table-column>
          <el-table-column prop="value" label="服务量" :resizable="false" align="center">
            <template slot-scope="scope">
              <span class="jump-mark" @click="goDetails('transport', scope.row)">{{ scope.row.value }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="rate" label="占比" :resizable="false" align="center"></el-table-column>
        </el-table>
        <el-table v-if="pageType == '4'" key="table4" :data="tableData" border style="width: 100%" height="85%" size="small">
          <el-table-column label="序号" type="index" width="80" align="center" :resizable="false"></el-table-column>
          <el-table-column prop="transportStartLocalName" label="服务起点" :resizable="false" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="orderAll" label="服务总量" :resizable="false" align="center">
            <template slot-scope="scope">
              <span class="jump-mark" @click="goDetails('start', scope.row)">{{ scope.row.orderAll }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="biaoben" label="标本" :resizable="false" align="center">
            <template slot-scope="scope">
              <span class="jump-mark" @click="goDetails('start', scope.row, '10')">{{ scope.row.biaoben }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="bingren" label="病人" :resizable="false" align="center">
            <template slot-scope="scope">
              <span class="jump-mark" @click="goDetails('start', scope.row, '4')">{{ scope.row.bingren }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="songxue" label="血液" :resizable="false" align="center">
            <template slot-scope="scope">
              <span class="jump-mark" @click="goDetails('start', scope.row, '9')">{{ scope.row.songxue }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="peijian" label="陪检" :resizable="false" align="center">
            <template slot-scope="scope">
              <span class="jump-mark" @click="goDetails('start', scope.row, '8')">{{ scope.row.peijian }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="yaopin" label="药品" :resizable="false" align="center">
            <template slot-scope="scope">
              <span class="jump-mark" @click="goDetails('start', scope.row, '3')">{{ scope.row.yaopin }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="yaopin" label="货物" :resizable="false" align="center">
            <template slot-scope="scope">
              <span class="jump-mark" @click="goDetails('start', scope.row, '1')">{{ scope.row.huowu }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="yaopin" label="医疗器械" :resizable="false" align="center">
            <template slot-scope="scope">
              <span class="jump-mark" @click="goDetails('start', scope.row, '2')">{{ scope.row.qixie }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="yaopin" label="办公家具" :resizable="false" align="center">
            <template slot-scope="scope">
              <span class="jump-mark" @click="goDetails('start', scope.row, '5')">{{ scope.row.jiaju }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="yaopin" label="病房加床" :resizable="false" align="center">
            <template slot-scope="scope">
              <span class="jump-mark" @click="goDetails('start', scope.row, '6')">{{ scope.row.jiachaung }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="yaopin" label="其他" :resizable="false" align="center">
            <template slot-scope="scope">
              <span class="jump-mark" @click="goDetails('start', scope.row, '7')">{{ scope.row.qita }}</span>
            </template>
          </el-table-column>
        </el-table>
        <el-table v-if="pageType == '5'" key="table5" :data="tableData" border style="width: 100%" height="85%" size="small">
          <el-table-column label="序号" type="index" width="80" align="center" :resizable="false"></el-table-column>
          <el-table-column prop="transportEndLocalName" label="服务终点" :resizable="false" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="orderAll" label="服务总量" :resizable="false" align="center">
            <template slot-scope="scope">
              <span class="jump-mark" @click="goDetails('end', scope.row)">{{ scope.row.orderAll }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="biaoben" label="标本" :resizable="false" align="center">
            <template slot-scope="scope">
              <span class="jump-mark" @click="goDetails('end', scope.row, '10')">{{ scope.row.biaoben }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="bingren" label="病人" :resizable="false" align="center">
            <template slot-scope="scope">
              <span class="jump-mark" @click="goDetails('end', scope.row, '4')">{{ scope.row.bingren }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="songxue" label="血液" :resizable="false" align="center">
            <template slot-scope="scope">
              <span class="jump-mark" @click="goDetails('end', scope.row, '9')">{{ scope.row.songxue }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="peijian" label="陪检" :resizable="false" align="center">
            <template slot-scope="scope">
              <span class="jump-mark" @click="goDetails('end', scope.row, '8')">{{ scope.row.peijian }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="yaopin" label="药品" :resizable="false" align="center">
            <template slot-scope="scope">
              <span class="jump-mark" @click="goDetails('end', scope.row, '3')">{{ scope.row.yaopin }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="yaopin" label="货物" :resizable="false" align="center">
            <template slot-scope="scope">
              <span class="jump-mark" @click="goDetails('start', scope.row, '1')">{{ scope.row.huowu }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="yaopin" label="医疗器械" :resizable="false" align="center">
            <template slot-scope="scope">
              <span class="jump-mark" @click="goDetails('start', scope.row, '2')">{{ scope.row.qixie }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="yaopin" label="办公家具" :resizable="false" align="center">
            <template slot-scope="scope">
              <span class="jump-mark" @click="goDetails('start', scope.row, '5')">{{ scope.row.jiaju }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="yaopin" label="病房加床" :resizable="false" align="center">
            <template slot-scope="scope">
              <span class="jump-mark" @click="goDetails('start', scope.row, '6')">{{ scope.row.jiachaung }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="yaopin" label="其他" :resizable="false" align="center">
            <template slot-scope="scope">
              <span class="jump-mark" @click="goDetails('start', scope.row, '7')">{{ scope.row.qita }}</span>
            </template>
          </el-table-column>
        </el-table>
        <el-table v-if="pageType == '6'" key="table6" :data="tableData" border style="width: 100%" height="85%" size="small">
          <el-table-column label="序号" type="index" width="80" align="center" :resizable="false"></el-table-column>
          <el-table-column prop="xdata" label="时间" :resizable="false" align="center"></el-table-column>
          <el-table-column prop="ydata" label="服务总量" :resizable="false" align="center"></el-table-column>
          <el-table-column label="百分比" :resizable="false" align="center">
            <template slot-scope="scope">
              <div v-if="!scope.row.ydata">0%</div>
              <div v-else>{{ ((scope.row.ydata / timeTotal) * 100).toFixed(2) }}%</div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </PageContainer>
</template>
<script>
/* eslint-disable */
import jutils from 'jutils-src'
import * as echarts from 'echarts'
import store from '@/store/index'
export default {
  name: 'StatisticalAnalysis',
  beforeRouteEnter(to, from, next) {
    let names = []
    to.matched.map((v, i) => {
      if (i > 0) {
        v.components.default.name && names.push(v.components.default.name)
      }
    })
    // 进入页面时，先将当前页面的 name 信息存入 keep-alive 全局状态
    setTimeout(() => {
      next((vm) => {
        vm.$store.commit('keepAlive/add', names)
      })
    }, 0)
  },
  data() {
    return {
      tableData: [],
      cardData: {},
      timeType: '1',
      pageType: '1',
      biaobenArr: [],
      bingrenArr: [],
      songxueArr: [],
      yaopinArr: [],
      peijianArr: [],
      nameList: [],
      circle1Arr: [],
      circle2Arr: [],
      circle3Arr: [],
      circle4Arr: [],
      circle5Arr: [],
      circle6Arr: [],
      circle7Arr: [],
      circle8Arr: [],
      circle9Arr: [],
      circle10Arr: [],
      timeXData: [],
      timeYData: [],
      timeTotal: 0
    }
  },
  watch: {
    '$store.state.settings.sidebarCollapse': {
      handler(val) {
        this.$nextTick(() => {
          let echartsDom = [
            'chart1',
            'circle-chart1',
            'circle-chart2',
            'circle-chart3',
            'circle-chart4',
            'circle-chart5',
            'circle-chart6',
            'circle-chart7',
            'circle-chart8',
            'circle-chart9',
            'circle-chart10',
            'chart-time'
          ]
          setTimeout(() => {
            echartsDom.forEach((item) => {
              echarts.init(document.getElementById(item)).resize()
            })
          }, 250)
        })
      },
      deep: true
    }
  },
  mounted() {
    this.getReckonCount()
    this.getTransportStatistics()
    this.getPersonDeptTransportStatistics()
  },
  methods: {
    initChart() {
      const getchart = echarts.init(document.getElementById('chart1'))
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          top: '15%',
          left: '1%',
          right: '1%',
          bottom: '8%',
          containLabel: true
        },
        yAxis: {
          type: 'value'
        },
        legend: {},
        xAxis: [
          {
            type: 'category',
            data: this.nameList,
            // axisLabel: {
            //   formatter: function (val) {
            //     var strs = val.split('') // 字符串数组
            //     var str = ''
            //     // strs循环 forEach 每五个字符增加换行符
            //     strs.forEach((item, index) => {
            //       if (index % 6 === 0 && index !== 0) {
            //         str += '\n'
            //       }
            //       str += item
            //     })
            //     return str
            //   }
            // },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            }
          }
        ],
        series: [
          {
            name: '标本',
            type: 'bar',
            stack: 'bb',
            barWidth: 10,
            data: this.biaobenArr,
            label: {
              show: false
            },
            itemStyle: {
              color: '#3F63D3'
            }
          },
          {
            name: '病人',
            type: 'bar',
            stack: 'bb',
            barWidth: 10,
            data: this.bingrenArr,
            label: {
              show: false
            },
            itemStyle: {
              color: '#FF9435'
            }
          },
          {
            name: '血液',
            type: 'bar',
            stack: 'bb',
            barWidth: 10,
            data: this.songxueArr,
            label: {
              show: false
            },
            itemStyle: {
              color: '#FF6461'
            }
          },
          {
            name: '药品',
            type: 'bar',
            stack: 'bb',
            barWidth: 10,
            data: this.yaopinArr,
            label: {
              show: false
            },
            itemStyle: {
              color: '#237CEC'
            }
          },
          {
            name: '陪检',
            type: 'bar',
            stack: 'bb',
            barWidth: 10,
            data: this.peijianArr,
            label: {
              show: false
            },
            itemStyle: {
              color: '#0CA6ED'
            }
          },
          {
            name: '货物',
            type: 'bar',
            stack: 'bb',
            barWidth: 10,
            data: this.huowuArr,
            label: {
              show: false
            },
            itemStyle: {
              color: '#97cc79'
            }
          },
          {
            name: '医疗器械',
            type: 'bar',
            stack: 'bb',
            barWidth: 10,
            data: this.qixieArr,
            label: {
              show: false
            },
            itemStyle: {
              color: '#f7c862'
            }
          },
          {
            name: '办公家具',
            type: 'bar',
            stack: 'bb',
            barWidth: 10,
            data: this.jiajuArr,
            label: {
              show: false
            },
            itemStyle: {
              color: '#e37dca'
            }
          },
          {
            name: '病房加床',
            type: 'bar',
            stack: 'bb',
            barWidth: 10,
            data: this.jiachaungArr,
            label: {
              show: false
            },
            itemStyle: {
              color: '#f58558'
            }
          },
          {
            name: '其他',
            type: 'bar',
            stack: 'bb',
            barWidth: 10,
            data: this.qitaArr,
            label: {
              show: false
            },
            itemStyle: {
              color: '#4ba274'
            }
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    initTimeChart() {
      const getchart = echarts.init(document.getElementById('chart-time'))
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          top: '15%',
          left: '1%',
          right: '1%',
          bottom: '8%',
          containLabel: true
        },
        yAxis: {
          type: 'value'
        },
        legend: {
          show: false
        },
        xAxis: [
          {
            type: 'category',
            data: this.timeXData,
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            }
          }
        ],
        series: [
          {
            type: 'bar',
            barWidth: 10,
            data: this.timeYData,
            label: {
              show: true,
              position: 'top',
              color: '#414653'
            },
            itemStyle: {
              color: '#3F63D3'
            }
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    initCircleChart1() {
      const getchart = echarts.init(document.getElementById('circle-chart1'))
      const option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          bottom: '5%',
          left: 'center',
          formatter: (params) => {
            return params + '   ' + this.circle1Arr[0].value + '件'
          },
          data: [
            {
              name: '标本',
              icon: 'circle'
            }
          ]
        },
        series: [
          {
            type: 'pie',
            radius: ['50%', '70%'],
            center: ['50%', '40%'],
            avoidLabelOverlap: false,
            color: ['#3562DB', '#E4E7ED'],
            clockwise: false,
            tooltip: {
              show: false
            },
            label: {
              show: true,
              position: 'center',
              formatter: (params) => {
                if (this.circle1Arr[1].value == 0) {
                  return '0 %'
                } else {
                  return ((this.circle1Arr[0].value / this.circle1Arr[1].value) * 100).toFixed(2) + ' %'
                }
              },
              fontSize: 18
            },
            emphasis: {
              disabled: true
            },
            labelLine: {
              show: false
            },
            data: this.circle1Arr
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    initCircleChart2() {
      const getchart = echarts.init(document.getElementById('circle-chart2'))
      const option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          bottom: '5%',
          left: 'center',
          formatter: (params) => {
            return params + '   ' + this.circle2Arr[0].value + '件'
          },
          data: [
            {
              name: '病人',
              icon: 'circle'
            }
          ]
        },
        series: [
          {
            type: 'pie',
            radius: ['50%', '70%'],
            center: ['50%', '40%'],
            avoidLabelOverlap: false,
            color: ['#237CEC', '#E4E7ED'],
            clockwise: false,
            tooltip: {
              show: false
            },
            label: {
              show: true,
              position: 'center',
              formatter: (params) => {
                if (this.circle2Arr[1].value == 0) {
                  return '0 %'
                } else {
                  return ((this.circle2Arr[0].value / this.circle2Arr[1].value) * 100).toFixed(2) + ' %'
                }
              },
              fontSize: 18
            },
            emphasis: {
              disabled: true
            },
            labelLine: {
              show: false
            },
            data: this.circle2Arr
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    initCircleChart3() {
      const getchart = echarts.init(document.getElementById('circle-chart3'))
      const option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          bottom: '5%',
          left: 'center',
          formatter: (params) => {
            return params + '   ' + this.circle3Arr[0].value + '件'
          },
          data: [
            {
              name: '送血',
              icon: 'circle'
            }
          ]
        },
        series: [
          {
            type: 'pie',
            radius: ['50%', '70%'],
            center: ['50%', '40%'],
            avoidLabelOverlap: false,
            color: ['#0CA6ED', '#E4E7ED'],
            clockwise: false,
            tooltip: {
              show: false
            },
            label: {
              show: true,
              position: 'center',
              formatter: (params) => {
                if (this.circle3Arr[1].value == 0) {
                  return '0 %'
                } else {
                  return ((this.circle3Arr[0].value / this.circle3Arr[1].value) * 100).toFixed(2) + ' %'
                }
              },
              fontSize: 18
            },
            emphasis: {
              disabled: true
            },
            labelLine: {
              show: false
            },
            data: this.circle3Arr
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    initCircleChart4() {
      const getchart = echarts.init(document.getElementById('circle-chart4'))
      const option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          bottom: '5%',
          left: 'center',
          formatter: (params) => {
            return params + '   ' + this.circle4Arr[0].value + '件'
          },
          data: [
            {
              name: '药品',
              icon: 'circle'
            }
          ]
        },
        series: [
          {
            type: 'pie',
            radius: ['50%', '70%'],
            center: ['50%', '40%'],
            avoidLabelOverlap: false,
            color: ['#FF9435', '#E4E7ED'],
            clockwise: false,
            tooltip: {
              show: false
            },
            label: {
              show: true,
              position: 'center',
              formatter: (params) => {
                if (this.circle4Arr[1].value == 0) {
                  return '0 %'
                } else {
                  return ((this.circle4Arr[0].value / this.circle4Arr[1].value) * 100).toFixed(2) + ' %'
                }
              },
              fontSize: 18
            },
            emphasis: {
              disabled: true
            },
            labelLine: {
              show: false
            },
            data: this.circle4Arr
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    initCircleChart5() {
      const getchart = echarts.init(document.getElementById('circle-chart5'))
      const option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          bottom: '5%',
          left: 'center',
          formatter: (params) => {
            return params + '   ' + this.circle5Arr[0].value + '件'
          },
          data: [
            {
              name: '陪检',
              icon: 'circle'
            }
          ]
        },
        series: [
          {
            type: 'pie',
            radius: ['50%', '70%'],
            center: ['50%', '40%'],
            avoidLabelOverlap: false,
            color: ['#FF6461', '#E4E7ED'],
            clockwise: false,
            tooltip: {
              show: false
            },
            label: {
              show: true,
              position: 'center',
              formatter: (params) => {
                if (this.circle5Arr[1].value == 0) {
                  return '0 %'
                } else {
                  return ((this.circle5Arr[0].value / this.circle5Arr[1].value) * 100).toFixed(2) + ' %'
                }
              },
              fontSize: 18
            },
            emphasis: {
              disabled: true
            },
            labelLine: {
              show: false
            },
            data: this.circle5Arr
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    initCircleChart6() {
      const getchart = echarts.init(document.getElementById('circle-chart6'))
      const option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          bottom: '5%',
          left: 'center',
          formatter: (params) => {
            return params + '   ' + this.circle6Arr[0].value + '件'
          },
          data: [
            {
              name: '货物',
              icon: 'circle'
            }
          ]
        },
        series: [
          {
            type: 'pie',
            radius: ['50%', '70%'],
            center: ['50%', '40%'],
            avoidLabelOverlap: false,
            color: ['#97cc79', '#E4E7ED'],
            clockwise: false,
            tooltip: {
              show: false
            },
            label: {
              show: true,
              position: 'center',
              formatter: (params) => {
                if (this.circle6Arr[1].value == 0) {
                  return '0 %'
                } else {
                  return ((this.circle6Arr[0].value / this.circle6Arr[1].value) * 100).toFixed(2) + ' %'
                }
              },
              fontSize: 18
            },
            emphasis: {
              disabled: true
            },
            labelLine: {
              show: false
            },
            data: this.circle6Arr
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    initCircleChart7() {
      const getchart = echarts.init(document.getElementById('circle-chart7'))
      const option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          bottom: '5%',
          left: 'center',
          formatter: (params) => {
            return params + '   ' + this.circle7Arr[0].value + '件'
          },
          data: [
            {
              name: '医疗器械',
              icon: 'circle'
            }
          ]
        },
        series: [
          {
            type: 'pie',
            radius: ['50%', '70%'],
            center: ['50%', '40%'],
            avoidLabelOverlap: false,
            color: ['#f7c862', '#E4E7ED'],
            clockwise: false,
            tooltip: {
              show: false
            },
            label: {
              show: true,
              position: 'center',
              formatter: (params) => {
                if (this.circle7Arr[1].value == 0) {
                  return '0 %'
                } else {
                  return ((this.circle7Arr[0].value / this.circle7Arr[1].value) * 100).toFixed(2) + ' %'
                }
              },
              fontSize: 18
            },
            emphasis: {
              disabled: true
            },
            labelLine: {
              show: false
            },
            data: this.circle7Arr
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    initCircleChart8() {
      const getchart = echarts.init(document.getElementById('circle-chart8'))
      const option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          bottom: '5%',
          left: 'center',
          formatter: (params) => {
            return params + '   ' + this.circle8Arr[0].value + '件'
          },
          data: [
            {
              name: '办公家具',
              icon: 'circle'
            }
          ]
        },
        series: [
          {
            type: 'pie',
            radius: ['50%', '70%'],
            center: ['50%', '40%'],
            avoidLabelOverlap: false,
            color: ['#e37dca', '#E4E7ED'],
            clockwise: false,
            tooltip: {
              show: false
            },
            label: {
              show: true,
              position: 'center',
              formatter: (params) => {
                if (this.circle8Arr[1].value == 0) {
                  return '0 %'
                } else {
                  return ((this.circle8Arr[0].value / this.circle8Arr[1].value) * 100).toFixed(2) + ' %'
                }
              },
              fontSize: 18
            },
            emphasis: {
              disabled: true
            },
            labelLine: {
              show: false
            },
            data: this.circle8Arr
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    initCircleChart9() {
      const getchart = echarts.init(document.getElementById('circle-chart9'))
      const option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          bottom: '5%',
          left: 'center',
          formatter: (params) => {
            return params + '   ' + this.circle9Arr[0].value + '件'
          },
          data: [
            {
              name: '病房加床',
              icon: 'circle'
            }
          ]
        },
        series: [
          {
            type: 'pie',
            radius: ['50%', '70%'],
            center: ['50%', '40%'],
            avoidLabelOverlap: false,
            color: ['#f58558', '#E4E7ED'],
            clockwise: false,
            tooltip: {
              show: false
            },
            label: {
              show: true,
              formatter: (params) => {
                if (this.circle9Arr[1].value == 0) {
                  return '0 %'
                } else {
                  return ((this.circle9Arr[0].value / this.circle9Arr[1].value) * 100).toFixed(2) + ' %'
                }
              },
              position: 'center',
              fontSize: 18
            },
            emphasis: {
              disabled: true
            },
            labelLine: {
              show: false
            },
            data: this.circle9Arr
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    initCircleChart10() {
      const getchart = echarts.init(document.getElementById('circle-chart10'))
      const option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          bottom: '5%',
          left: 'center',
          formatter: (params) => {
            return params + '   ' + this.circle10Arr[0].value + '件'
          },
          data: [
            {
              name: '其他',
              icon: 'circle'
            }
          ]
        },
        series: [
          {
            type: 'pie',
            radius: ['50%', '70%'],
            center: ['50%', '40%'],
            color: ['#4ba274', '#E4E7ED'],
            avoidLabelOverlap: false,
            clockwise: false,
            tooltip: {
              show: false
            },
            label: {
              show: true,
              formatter: (params) => {
                if (this.circle10Arr[1].value == 0) {
                  return '0 %'
                } else {
                  return ((this.circle10Arr[0].value / this.circle10Arr[1].value) * 100).toFixed(2) + ' %'
                }
              },
              position: 'center',
              fontSize: 18
            },
            emphasis: {
              disabled: true
            },
            labelLine: {
              show: false
            },
            data: this.circle10Arr
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    getReckonCount(val) {
      const userInfo = store.state.user.userInfo.user
      let params = {
        workTypeCode: 3,
        workTypeName: 'YS',
        showTimeType: this.timeType,
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
      if (val == '4') {
        params.startTime = this.timeSlotChange()[0]
        params.endTime = this.timeSlotChange()[1]
      }
      this.$api.getReckonCount2(params).then((res) => {
        this.cardData = res.body.data
      })
    },
    changeTimeType(val) {
      this.timeType = val
      if (val == '4') {
        this.getReckonCount('4')
      } else {
        this.getReckonCount()
      }
      if (this.pageType == '3') {
        this.getTransportTypeStatistics()
      }
      if (this.pageType == '1' || this.pageType == '2') {
        this.getPersonDeptTransportStatistics()
      }
      if (this.pageType == '6') {
        this.getTimeTransportStatistics()
      }
      this.getTransportStatistics()
    },
    timeSlotChange() {
      let startTime, endTime
      let now = new Date() // 当前日期
      var nowDayOfWeek = now.getDay() // 今天本周的第几天
      var nowDay = now.getDate() // 当前日
      let nowMonth = now.getMonth() // 当前月
      let nowYear = now.getFullYear() // 当前年
      let jd = Math.ceil((nowMonth + 1) / 3)
      startTime = new Date(nowYear, (jd - 1) * 3, 1)
      endTime = new Date(nowYear, jd * 3, 0)
      console.log(startTime, endTime)
      return [jutils.formatDate(startTime, 'YYYY-MM-DD+HH:ii:ss'), jutils.formatDate(endTime, 'YYYY-MM-DD+HH:ii:ss')]
    },
    changePageType(val) {
      this.pageType = val
      if (val == '1' || val == '2' || val == '4' || val == '5') {
        this.getTransportStatistics()
      }
      if (val == '1' || val == '2') {
        this.getPersonDeptTransportStatistics()
      }
      if (val == '3') {
        this.getTransportTypeStatistics()
      }
      if (val == '6') {
        this.getTimeTransportStatistics()
      }
    },
    getTimeTransportStatistics() {
      const userInfo = store.state.user.userInfo.user
      let params = {
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
      if (this.timeType == '1') {
        params.statisticsType = 'day'
      } else if (this.timeType == '6') {
        params.statisticsType = 'week'
      } else if (this.timeType == '2') {
        params.statisticsType = 'month'
      } else if (this.timeType == '4') {
        params.statisticsType = 'quarter'
      } else if (this.timeType == '3') {
        params.statisticsType = 'year'
      }
      this.$api.getTimeTransportStatistics(params).then((res) => {
        this.timeXData = res.data.map.timeList
        this.timeYData = res.data.map.orderNumList
        this.initTimeChart()
        let arr = []
        res.data.map.timeList.forEach((item, index) => {
          arr.push({
            xdata: item,
            ydata: res.data.map.orderNumList[index]
          })
        })
        this.tableData = arr
        this.timeTotal = 0
        res.data.map.orderNumList.forEach((item) => {
          this.timeTotal += item
        })
      })
    },
    getTransportStatistics() {
      const userInfo = store.state.user.userInfo.user
      let params = {
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
      if (this.timeType == '1') {
        params.dateType = 'day'
      } else if (this.timeType == '6') {
        params.dateType = 'week'
      } else if (this.timeType == '2') {
        params.dateType = 'month'
      } else if (this.timeType == '4') {
        params.dateType = 'quarter'
      } else if (this.timeType == '3') {
        params.dateType = 'year'
      }

      if (this.pageType == '1') {
        params.statisticsType = 'person'
      } else if (this.pageType == '2') {
        params.statisticsType = 'dept'
      } else if (this.pageType == '4') {
        params.statisticsType = 'startLocal'
      } else if (this.pageType == '5') {
        params.statisticsType = 'endLocal'
      }
      this.$api.getTransportStatistics(params).then((res) => {
        if (res.data.list && res.data.list.length) {
          let biaobenArr = []
          let bingrenArr = []
          let songxueArr = []
          let yaopinArr = []
          let peijianArr = []
          let huowuArr = []
          let qixieArr = []
          let jiajuArr = []
          let jiachaungArr = []
          let qitaArr = []
          let nameList = []
          res.data.list.forEach((item) => {
            if (
              item.biaoben == 0 &&
              item.bingren == 0 &&
              item.songxue == 0 &&
              item.yaopin == 0 &&
              item.peijian == 0 &&
              item.huowu == 0 &&
              item.qixie == 0 &&
              item.jiaju == 0 &&
              item.jiachaung == 0 &&
              item.qita == 0 &&
              (this.pageType == '4' || this.pageType == '5')
            ) {
              return
            }
            biaobenArr.push(item.biaoben)
            bingrenArr.push(item.bingren)
            songxueArr.push(item.songxue)
            yaopinArr.push(item.yaopin)
            peijianArr.push(item.peijian)
            huowuArr.push(item.huowu)
            qixieArr.push(item.qixie)
            jiajuArr.push(item.jiaju)
            jiachaungArr.push(item.jiachaung)
            qitaArr.push(item.qita)
            if (this.pageType == '1') {
              nameList.push(item.designatePersonName)
            } else if (this.pageType == '2') {
              let parts = item.sourcesDeptName ? item.sourcesDeptName.split('  ') : ['无']
              nameList.push(parts.length > 1 ? parts[1] : parts[0])
            } else if (this.pageType == '4') {
              nameList.push(item.transportStartLocalName || '无')
            } else if (this.pageType == '5') {
              nameList.push(item.transportEndLocalName || '无')
            }
          })
          this.biaobenArr = biaobenArr
          this.bingrenArr = bingrenArr
          this.songxueArr = songxueArr
          this.yaopinArr = yaopinArr
          this.peijianArr = peijianArr
          this.huowuArr = huowuArr
          this.qixieArr = qixieArr
          this.jiajuArr = jiajuArr
          this.jiachaungArr = jiachaungArr
          this.qitaArr = qitaArr
          this.nameList = nameList
          if (this.pageType == '4' || this.pageType == '5') {
            this.tableData = res.data.list
          }
        } else {
          this.nameList = ['无数据']
          this.biaobenArr = [0]
          this.bingrenArr = [0]
          this.songxueArr = [0]
          this.yaopinArr = [0]
          this.peijianArr = [0]
          this.huowuArr = [0]
          this.qixieArr = [0]
          this.jiajuArr = [0]
          this.jiachaungArr = [0]
          this.qitaArr = [0]
        }
        this.initChart()
      })
    },
    getPersonDeptTransportStatistics() {
      const userInfo = store.state.user.userInfo.user
      let params = {
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
      if (this.timeType == '1') {
        params.dateType = 'day'
      } else if (this.timeType == '6') {
        params.dateType = 'week'
      } else if (this.timeType == '2') {
        params.dateType = 'month'
      } else if (this.timeType == '4') {
        params.dateType = 'quarter'
      } else if (this.timeType == '3') {
        params.dateType = 'year'
      }

      if (this.pageType == '1') {
        params.statisticsType = 'person'
      } else if (this.pageType == '2') {
        params.statisticsType = 'dept'
      }

      this.$api.getPersonDeptTransportStatistics(params).then((res) => {
        this.tableData = res.data.list
      })
    },
    getTransportTypeStatistics() {
      const userInfo = store.state.user.userInfo.user
      let params = {
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
      if (this.timeType == '1') {
        params.dateType = 'day'
      } else if (this.timeType == '6') {
        params.dateType = 'week'
      } else if (this.timeType == '2') {
        params.dateType = 'month'
      } else if (this.timeType == '4') {
        params.dateType = 'quarter'
      } else if (this.timeType == '3') {
        params.dateType = 'year'
      }
      this.$api.getTransportTypeStatistics(params).then((res) => {
        if (res.data.list && res.data.list.length) {
          let obj = res.data.list[0]
          let total = obj.biaoben + obj.bingren + obj.songxue + obj.yaopin + obj.peijian + obj.huowu + obj.yiliaoqixie + obj.bangongjiaju + obj.bingfangjiashouchuang + obj.qita
          this.circle1Arr = [
            { value: obj.biaoben, name: '标本' },
            { value: total, name: ' ' }
          ]
          this.circle2Arr = [
            { value: obj.bingren, name: '病人' },
            { value: total, name: ' ' }
          ]
          this.circle3Arr = [
            { value: obj.songxue, name: '送血' },
            { value: total, name: ' ' }
          ]
          this.circle4Arr = [
            { value: obj.yaopin, name: '药品' },
            { value: total, name: ' ' }
          ]
          this.circle5Arr = [
            { value: obj.peijian, name: '陪检' },
            { value: total, name: ' ' }
          ]
          this.circle6Arr = [
            { value: obj.huowu, name: '货物' },
            { value: total, name: ' ' }
          ]
          this.circle7Arr = [
            { value: obj.yiliaoqixie, name: '医疗器械' },
            { value: total, name: ' ' }
          ]
          this.circle8Arr = [
            { value: obj.bangongjiaju, name: '办公家具' },
            { value: total, name: ' ' }
          ]
          this.circle9Arr = [
            { value: obj.bingfangjiashouchuang, name: '病房加床' },
            { value: total, name: ' ' }
          ]
          this.circle10Arr = [
            { value: obj.qita, name: '其他' },
            { value: total, name: ' ' }
          ]

          let arr = []
          arr.push({
            name: '标本',
            value: obj.biaoben,
            rate: obj.biaobenRate
          })
          arr.push({
            name: '病人',
            value: obj.bingren,
            rate: obj.bingrenRate
          })
          arr.push({
            name: '送血',
            value: obj.songxue,
            rate: obj.songxueRate
          })
          arr.push({
            name: '药品',
            value: obj.yaopin,
            rate: obj.yaopinRate
          })
          arr.push({
            name: '陪检',
            value: obj.peijian,
            rate: obj.peijianRate
          })
          arr.push({
            name: '货物',
            value: obj.huowu,
            rate: obj.huowuRate
          })
          arr.push({
            name: '医疗器械',
            value: obj.yiliaoqixie,
            rate: obj.yiliaoqixieRate
          })
          arr.push({
            name: '办公家具',
            value: obj.bangongjiaju,
            rate: obj.bangongjiajuRate
          })
          arr.push({
            name: '病房加床',
            value: obj.bingfangjiashouchuang,
            rate: obj.bingfangjiashouchuangRate
          })
          arr.push({
            name: '其他',
            value: obj.qita,
            rate: obj.qitaRate
          })
          this.tableData = arr
        } else {
          this.tableData = []
        }
        // 如果数据是空的，就给默认值
        this.circle1Arr = this.circle1Arr[0].value
          ? this.circle1Arr
          : [
              { value: 0, name: '标本' },
              { value: 0, name: ' ' }
            ]
        this.circle2Arr = this.circle2Arr[0].value
          ? this.circle2Arr
          : [
              { value: 0, name: '病人' },
              { value: 0, name: ' ' }
            ]
        this.circle3Arr = this.circle3Arr[0].value
          ? this.circle3Arr
          : [
              { value: 0, name: '送血' },
              { value: 0, name: ' ' }
            ]
        this.circle4Arr = this.circle4Arr[0].value
          ? this.circle4Arr
          : [
              { value: 0, name: '药品' },
              { value: 0, name: ' ' }
            ]
        this.circle5Arr = this.circle5Arr[0].value
          ? this.circle5Arr
          : [
              { value: 0, name: '陪检' },
              { value: 0, name: ' ' }
            ]
        this.circle6Arr = this.circle6Arr[0].value
          ? this.circle6Arr
          : [
              { value: 0, name: '货物' },
              { value: 0, name: ' ' }
            ]
        this.circle7Arr = this.circle7Arr[0].value
          ? this.circle7Arr
          : [
              { value: 0, name: '医疗器械' },
              { value: 0, name: ' ' }
            ]
        this.circle8Arr = this.circle8Arr[0].value
          ? this.circle8Arr
          : [
              { value: 0, name: '办公家具' },
              { value: 0, name: ' ' }
            ]
        this.circle9Arr = this.circle9Arr[0].value
          ? this.circle9Arr
          : [
              { value: 0, name: '病房加床' },
              { value: 0, name: ' ' }
            ]
        this.circle10Arr = this.circle10Arr[0].value
          ? this.circle10Arr
          : [
              { value: 0, name: '其他' },
              { value: 0, name: ' ' }
            ]
        this.initCircleChart1()
        this.initCircleChart2()
        this.initCircleChart3()
        this.initCircleChart4()
        this.initCircleChart5()
        this.initCircleChart6()
        this.initCircleChart7()
        this.initCircleChart8()
        this.initCircleChart9()
        this.initCircleChart10()
      })
    },
    goDetails(type, row, transportTypeCode) {
      let query = {}
      if (type == 'dept') {
        query.sourcesDept = row.sources_dept
      } else if (type == 'deptfinish') {
        query.sourcesDept = row.sources_dept
        query.flowcode = '5'
      } else if (type == 'person') {
        query.designatePersonCode = row.designate_person_code
      } else if (type == 'personfinish') {
        query.designatePersonCode = row.designate_person_code
        query.flowcode = '5'
      } else if (type == 'start') {
        let arr = row.transportStartLocal.split(',')
        query.region = arr[0] || ''
        query.buliding = arr[1] || ''
        query.storey = arr[2] || ''
        query.room = arr[3] || ''
        query.roomSpace = arr[4] || ''
      } else if (type == 'end') {
        let arr = row.transportEndLocal.split(',')
        query.region = arr[0] || ''
        query.buliding = arr[1] || ''
        query.storey = arr[2] || ''
        query.room = arr[3] || ''
        query.roomSpace = arr[4] || ''
      } else if (type == 'transport') {
        if (row.name == '标本') {
          query.transportTypeCode = '10'
        }
        if (row.name == '病人') {
          query.transportTypeCode = '4'
        }
        if (row.name == '送血') {
          query.transportTypeCode = '9'
        }
        if (row.name == '药品') {
          query.transportTypeCode = '3'
        }
        if (row.name == '陪检') {
          query.transportTypeCode = '8'
        }
        if (row.name == '货物') {
          query.transportTypeCode = '1'
        }
        if (row.name == '医疗器械') {
          query.transportTypeCode = '2'
        }
        if (row.name == '办公家具') {
          query.transportTypeCode = '5'
        }
        if (row.name == '病房加床') {
          query.transportTypeCode = '6'
        }
        if (row.name == '其他') {
          query.transportTypeCode = '7'
        }
      }
      if (transportTypeCode) {
        query.transportTypeCode = transportTypeCode
      }
      query.showTimeType = this.timeType
      query.typeName = type
      this.$router.push({
        path: '/centralTransport/workOrderTable',
        query
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.time {
  display: flex;
  height: 50px;
  border-bottom: 1px solid #dcdfe6;

  span {
    line-height: 50px;
    font-size: 15px;
    color: #414653;
    margin: 0 16px;
    cursor: pointer;
  }
}

.active {
  color: #3562db !important;
  border-bottom: 2px solid #3562db;
}

.btns {
  display: flex;
}

.btns .btn {
  width: 100px;
  height: 35px;
  line-height: 35px;
  text-align: center;
  border: 1px solid #3562db;
  border-radius: 4px;
  margin: 10px;
  cursor: pointer;
  font-size: 14px;
  color: #3562db;
}

.btn-actived {
  background-color: #3562db;
  color: #fff !important;
}

::v-deep .container-content {
  margin-top: 16px;
  overflow: visible !important;
}

.container-content > div {
  display: flex;
  flex-wrap: wrap;
  height: 100%;
}

.container-content > div > div {
  width: 100%;
  background-color: #fff;
}

.cards {
  display: flex;
  justify-content: space-between;
  height: 16%;
  background-color: transparent !important;
}

.cards > div {
  background-color: #fff;
  width: 19%;
  height: 100%;
  border-radius: 4px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.cards > div > span {
  font-size: 15px;
  color: #121f3e;
  font-weight: 500;
}

.cards img {
  width: 40px;
  height: 38px;
}

.chart-box {
  height: 32%;
}

.table-box {
  height: 45%;
}

.cost-num {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cost-num > span > span:nth-child(1) {
  font-size: 28px;
  color: #121f3e;
  font-weight: 700;
  margin-right: 5px;
}

.cost-num > span > span:nth-child(2) {
  font-size: 14px;
  color: #ccced3;
  font-weight: 500;
}

.chart-box {
  padding: 12px;
}

.chart-box .title > span,
.table-box .title > span {
  font-size: 15px;
}

.table-box {
  padding: 12px;
}

#chart1 {
  width: 100%;
  height: 100%;
}

.circles-box {
  width: 100%;
  height: 92%;
  display: flex;
  justify-content: space-between;
}

.circles-box > div {
  width: 25%;
  height: 100%;
}

.el-table {
  margin-top: 14px;
}

#chart-time {
  width: 100%;
  height: 100%;
}

.jump-mark {
  color: #3562db;
  cursor: pointer;
}
</style>
