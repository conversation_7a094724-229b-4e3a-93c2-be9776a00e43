export const inventoryManagement = {
  tableHeader: [
    {
      prop: 'warehouseName',
      label: '仓库名称',
      width: ''
    },
    {
      prop: 'warehouseCode',
      label: '仓库编码',
      width: ''
    },
    {
      prop: 'materialName',
      label: '配件名称',
      width: ''
    },
    {
      prop: 'materialCode',
      label: '配件编码',
      width: ''
    },
    {
      prop: 'materialTypeName',
      label: '所属分类',
      width: ''
    },
    {
      prop: 'basicUnitName',
      label: '基本单位',
      width: ''
    },
    {
      prop: 'unitPrice',
      label: '单价(元)',
      width: ''
    },
    {
      prop: 'brandName',
      label: '品牌',
      width: ''
    },
    {
      prop: 'model',
      label: '规格型号',
      width: ''
    },
    {
      prop: 'supplierName',
      label: '供应商',
      width: ''
    },
    {
      prop: 'manufacturerName',
      label: '生产厂家',
      width: ''
    },
    {
      prop: 'inventory',
      label: '库存数量',
      width: ''
    },
    {
      prop: 'amount',
      label: '库存金额(元)',
      width: '100'
    }
  ]
}
export const inventoryDetails = {
  tableHeader: [
    {
      prop: 'orderNumber',
      label: '入库单号',
      width: ''
    },
    {
      prop: 'materialName',
      label: '配件名称',
      width: ''
    },
    {
      prop: 'materialCode',
      label: '配件编码',
      width: ''
    },
    {
      prop: 'basicUnitName',
      label: '基本单位',
      width: ''
    },
    {
      prop: 'materialTypeName',
      label: '所属分类',
      width: ''
    },
    {
      prop: 'brandName',
      label: '品牌',
      width: ''
    },
    {
      prop: 'model',
      label: '规格型号',
      width: ''
    },
    {
      prop: 'supplierName',
      label: '供应商',
      width: ''
    },
    {
      prop: 'manufacturerName',
      label: '生产厂家',
      width: ''
    },
    {
      prop: 'operateCount',
      label: '入库数量',
      width: ''
    },
    {
      prop: 'amount',
      label: '入库金额(元)',
      width: ''
    },
    {
      prop: 'warehouseName',
      label: '入库仓库',
      width: ''
    },
    {
      prop: 'createName',
      label: '入库人',
      width: ''
    },
    {
      prop: 'createTime',
      label: '入库时间',
      width: ''
    }
  ]
}
export const inventoryWarning = {
  tableHeader: [
    {
      prop: 'materialName',
      label: '配件名称',
      width: ''
    },
    {
      prop: 'materialCode',
      label: '配件编码',
      width: ''
    },
    {
      prop: 'materialTypeName',
      label: '所属分类',
      width: ''
    },
    {
      prop: 'basicUnitName',
      label: '基本单位',
      width: ''
    },
    {
      prop: 'unitPrice',
      label: '单价(元)',
      width: ''
    },
    {
      prop: 'brandName',
      label: '品牌',
      width: ''
    },
    {
      prop: 'model',
      label: '规格型号',
      width: ''
    },
    {
      prop: 'manufacturerName',
      label: '生产厂家',
      width: ''
    },
    {
      prop: 'inventoryStatus',
      label: '预警状态',
      width: '150'
    },
    {
      prop: 'inventory',
      label: '库存数量',
      width: ''
    },
    {
      prop: 'maxStock',
      label: '最高库存要求',
      width: ''
    },
    {
      prop: 'minStock',
      label: '最低库存要求',
      width: ''
    },
    {
      slot: 'operate',
      label: '操作',
      width: ''
    }
  ]
}
export const outboundDetails = {
  tableHeader: [
    {
      prop: 'orderNumber',
      label: '出库单号',
      width: ''
    },
    {
      prop: 'materialName',
      label: '配件名称',
      width: ''
    },
    {
      prop: 'materialCode',
      label: '配件编码',
      width: ''
    },
    {
      prop: 'basicUnitName',
      label: '基本单位',
      width: ''
    },
    {
      prop: 'materialTypeName',
      label: '所属分类',
      width: ''
    },
    {
      prop: 'brandName',
      label: '品牌',
      width: ''
    },
    {
      prop: 'model',
      label: '规格型号',
      width: ''
    },
    {
      prop: 'manufacturerName',
      label: '生产厂家',
      width: ''
    },
    {
      prop: 'operateCount',
      label: '出库数量',
      width: ''
    },
    {
      prop: 'amount',
      label: '出库金额(元)',
      width: ''
    },
    {
      prop: 'warehouseName',
      label: '出库仓库',
      width: ''
    },
    {
      prop: 'createName',
      label: '出库人',
      width: ''
    },
    {
      prop: 'createTime',
      label: '出库时间',
      width: ''
    }
  ]
}
