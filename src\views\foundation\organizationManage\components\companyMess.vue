<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <!-- <div class="whole">
    <div ref="sinoPanel" class="who"> -->
      <div class="whloe">
        <el-form
          ref="formInline"
          :model="formInline"
          :inline="true"
          :rules="rules"
          label-position="right"
          label-width="150px"
          :disabled="$route.query.type == 'View'"
          style="background: #fff; height: 100%"
        >
          <div style="margin-top: 25px">
            <el-form-item label="单位名称" prop="unitComName">
              <el-input v-model="formInline.unitComName" class="sino_form_input" placeholder="单位名称" show-word-limit> </el-input>
            </el-form-item>
            <el-form-item label="单位性质" prop="nature">
              <el-select v-model="formInline.nature" class="sino_form_input" placeholder="请选择单位性质">
                <el-option v-for="item in natureList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="单位类型" prop="unitType">
              <el-select v-model="formInline.unitType" class="sino_form_input" multiple clearable placeholder="请选择单位类型">
                <el-option v-for="item in unitTypeList" :key="item.id" :label="item.dictName" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="负责人姓名" prop="principalName">
              <el-input v-model="formInline.principalName" class="sino_form_input" placeholder="请输入负责人姓名" show-word-limit> </el-input>
            </el-form-item>
            <el-form-item label="负责人电话" prop="principalPhone">
              <el-input v-model="formInline.principalPhone" class="sino_form_input" placeholder="请输入负责人电话" show-word-limit> </el-input>
            </el-form-item>
            <el-form-item label="法人代表" prop="legalPersonName">
              <el-input v-model="formInline.legalPersonName" class="sino_form_input" placeholder="请输入法人代表" show-word-limit> </el-input>
            </el-form-item>
            <el-form-item label="公司地址" prop="companyAddress">
              <el-input v-model="formInline.companyAddress" class="sino_form_input" placeholder="请选择公司地址坐标" show-word-limit>
                <i v-show="$route.query.type != 'View'" slot="suffix" class="el-icon-location el-input__icon" style="cursor: pointer; font-size: 18px" @click="handleIconClick">
                </i>
              </el-input>
            </el-form-item>
            <el-form-item label="投诉电话" prop="complaintCall">
              <el-input v-model="formInline.complaintCall" class="sino_form_input" placeholder="请输入投诉电话" show-word-limit> </el-input>
            </el-form-item>
            <br />
            <el-form-item label="营业执照" prop="businessLicenseUrl">
              <sino-upload :fileList="businessLicenseList" :flieSizeType="1" modelName="hospitalBaseInfo" @input="uploadChange"></sino-upload>
            </el-form-item>
            <br />
            <el-form-item label="服务简介" prop="serviceProfile">
              <el-input v-model="formInline.serviceProfile" type="textarea" class="sino_form_textarea" placeholder="请输入服务简介" :rows="4" show-word-limit maxlength="200">
              </el-input>
            </el-form-item>
            <br />
            <el-form-item label="备注" prop="remark">
              <el-input v-model="formInline.remark" type="textarea" class="sino_form_textarea" placeholder="请输入备注" :rows="4" show-word-limit maxlength="200"> </el-input>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <BaiduPanel :closeState="closeState">
        <template slot="content">
          <el-input v-model="addressKeyword" class="map_search" placeholder="请输入"></el-input>
          <baidu-map class="bmView" :scroll-wheel-zoom="true" :center="location" :zoom="zoom" ak="EYOuQYCB2gnBzQsL7MYGxkyb1cAtOeNG" @click="getLocationPoint">
            <bm-view style="width: 100%; height: 100%; flex: 1"></bm-view>
            <bm-local-search :keyword="addressKeyword" :auto-viewport="true" style="display: none"></bm-local-search>
          </baidu-map>
        </template>
      </BaiduPanel>
    </div>
    <!-- </div>
  </div> -->
    <div slot="footer">
      <el-button v-if="$route.query.type == 'Edit'" type="primary" @click="complete">保存</el-button>
      <el-button type="primary" plain @click="$router.go(-1)">取消</el-button>
    </div>
  </PageContainer>
</template>
<script>
import sinoUpload from '../common/sinoUpload.vue'
import BaiduPanel from '../common/sinoSlidePanel.vue'
import BaiduMap from 'vue-baidu-map/components/map/Map.vue'
import BmView from 'vue-baidu-map/components/map/MapView.vue'
import BmLocalSearch from 'vue-baidu-map/components/search/LocalSearch.vue'
import { validateMobile, validatePhone } from '@/assets/common/validate'
const validateMobileNum = (rule, value, callback) => {
  if (!validateMobile(value)) {
    callback(new Error('请输入正确的手机号'))
  } else {
    callback()
  }
}
const validatePhoneNum = (rule, value, callback) => {
  if (validatePhone(value) || validateMobile(value)) {
    callback()
  } else {
    callback(new Error('请输入正确的电话号码'))
  }
}
export default {
  name: 'companyMess',
  components: {
    sinoUpload,
    BaiduPanel,
    BaiduMap,
    BmView,
    BmLocalSearch
  },
  async beforeRouteLeave(to, from, next) {
    if (!['unitManage'].includes(to.name)) {
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      // title: this.$route.query.type == 'Add' ? '新增单位' : this.$route.query.type == 'View' ? '查看单位' : '编辑单位',
      isDisable: false,
      formInline: {
        unitComName: '',
        nature: '',
        unitType: [],
        principalName: '',
        principalPhone: '',
        legalPersonName: '',
        companyAddress: '',
        businessLicenseId: '',
        businessLicenseUrl: '',
        serviceProfile: '',
        complaintCall: '',
        remark: ''
      },
      rules: {
        unitComName: {
          required: true,
          message: '请输入单位名称',
          trigger: 'blur'
        },
        nature: {
          required: true,
          message: '请选择单位性质',
          trigger: 'blur'
        },
        unitType: {
          required: true,
          message: '请选择单位类别',
          trigger: 'blur'
        },
        principalPhone: [
          { validator: validateMobileNum, trigger: 'blur' },
          { required: false, message: '请输入手机号码', trigger: 'blur' }
        ],
        complaintCall: [
          { validator: validatePhoneNum, trigger: 'blur' },
          { required: false, message: '请输入手机号码', trigger: 'blur' }
        ]
      },
      natureList: [],
      unitTypeList: [],
      closeState: true,
      location: {
        lng: 116.404,
        lat: 39.915
      },
      zoom: 12.8,
      addressKeyword: '',
      businessLicenseList: []
    }
  },
  activated() {
    this.initEvent()
  },
  created() {
    if (!this.$store.state.keepAlive.list.includes('unitManage')) {
      this.initEvent()
    }
  },
  methods: {
    initEvent() {
      Object.assign(this.$data, this.$options.data())
      this.$nextTick(() => {
        this.$refs.formInline.resetFields()
      })
      this.valveTypeListFn('DWXZ') // 字典-单位性质
      this.valveTypeListFn('DWLX') // 字典-单位类型
      if (this.$route.query.id) {
        this.getUnitByIdFn()
      }
    },
    //  根据单位ID获取单位信息详情
    getUnitByIdFn() {
      this.$api
        .getUnitById({
          id: this.$route.query.id
        })
        .then((res) => {
          if (res.code == 200) {
            this.formInline = res.data
            this.formInline.unitType = res.data.unitType.split(',')
            this.formInline.nature = res.data.nature.toString()
            this.formInline.businessLicenseUrl
              ? (this.businessLicenseList = [{ name: '', url: this.$tools.imgUrlTranslation(this.formInline.businessLicenseUrl) }])
              : (this.businessLicenseList = [])
          }
        })
    },
    //  获取字典列表Fn
    valveTypeListFn(typeValue) {
      this.$api
        .valveTypeList({
          typeValue: typeValue
        })
        .then((res) => {
          if (res.code == 200) {
            typeValue == 'DWLX' ? (this.unitTypeList = res.data) : (this.natureList = res.data)
          }
        })
    },
    // ------------------------------------------------百度地图
    /**
     * @description: 医院坐标选择方法
     * @param {*}
     * @return {*}
     * @note:
     * @Author: Allen
     */
    handleIconClick() {
      this.closeState = !this.closeState
    },
    getLocationPoint(e) {
      this.location.lng = e.point.lng
      this.location.lat = e.point.lat
      /* 创建地址解析器的实例 */
      let geoCoder = new BMap.Geocoder()
      /* 利用坐标获取地址的详细信息 */
      geoCoder.getLocation(e.point, (res) => {
        this.formInline.companyAddress = res.address
      })
    },
    // ------------------------------------------------ FormFn
    /**
     * @description: 新增单位_Fn
     * @param {*}
     * @return {*}
     * @note:
     * @Author: Allen
     */
    complete() {
      let fnName
      this.$route.query.type == 'Edit' ? (fnName = this.$api.updataUnit) : (fnName = this.$api.addUnit)
      let data = {
        id: this.$route.query.id,
        ...this.formInline
      }
      data.unitType = data.unitType.join(',')
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          let header = {}
          if (this.$route.query.type === 'Edit') {
            header = {
              'operation-type': 2,
              'operation-id': this.$route.query.id,
              'operation-name': data.unitComName
            }
          } else {
            header = {
              'operation-type': 1
            }
          }
          fnName(data, header).then((res) => {
            if (res.code == 200) {
              this.cancel()
              this.$message({
                message: res.msg,
                type: 'success'
              })
            }
          })
        }
      })
    },
    cancel() {
      this.$router.go(-1)
    },
    // ------------------------------------------------- OtherFn
    /**
     * @description: 图片上传
     * @param {*}
     * @return {*}
     * @note:
     * @Author: Allen
     */
    uploadChange(val, state) {
      if (state) {
        this.formInline.businessLicenseId = ''
        this.formInline.businessLicenseUrl = ''
      } else {
        this.formInline.businessLicenseId = val.uid
        this.formInline.businessLicenseUrl = val.fileHost + val.fileUrl
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
  padding: 10px;
  overflow-y: auto;
}
.whole {
  width: 100%;
  height: 100%;
  padding: 15px;
}
.map_search {
  padding-bottom: 10px;
}
.bmView {
  height: 100%;
  box-sizing: border-box;
}
.sino_form_input {
  width: 200px;
}
.sino_form_textarea {
  width: 500px;
}
</style>
