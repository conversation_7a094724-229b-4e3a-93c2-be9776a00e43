<template>
  <el-dialog
    v-loading="pageLoading"
    title="结果录入"
    :visible.sync="dialogVisible"
    append-to-body
    :close-on-click-modal="false"
    :modal-append-to-body="false"
    top="10vh"
    width="45%"
    :before-close="closed"
  >
    <div class="task-inner">
      <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
        <span class="green_line"></span>
        基础信息
      </div>
      <div class="content-group">
        <el-descriptions>
          <el-descriptions-item label="巡检点名称">{{ pointRelease.taskPointName }}</el-descriptions-item>
          <el-descriptions-item label="巡检人员">{{ $store.state.user.userInfo.user.staffName }}</el-descriptions-item>
          <el-descriptions-item label="巡检部门">{{ $store.state.user.userInfo.user.deptName }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <div v-if="pointRelease.maintainProjectRelease" class="toptip" style="padding: 0; border: 0; font-weight: bold">
        <span class="green_line"></span>
        巡检内容
      </div>
      <!-- 巡检项目 -->
      <div v-if="pointRelease.maintainProjectRelease" class="content-group" style="margin-bottom: 20px">
        <!-- 日常 -->
        <div v-if="type == '0'" class="projectContent">
          <div class="tableRow tableHeader">
            <div class="serialNumber">序号</div>
            <div>巡检内容</div>
            <div>标准要求</div>
            <div>巡检依据</div>
          </div>
          <div v-for="(item, index) in pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList" :key="index" class="tableRow">
            <div class="serialNumber">{{ index + 1 }}</div>
            <div>{{ item.detailName }}</div>
            <div>{{ item.maintainProjectdetails.standardRequirements }}</div>
            <div>{{ item.maintainProjectdetails.inspectionBasis }}</div>
          </div>
        </div>
        <!-- 专业 -->
        <el-collapse v-if="type == '1'" v-model="activeNames" :border="false">
          <el-collapse-item v-for="(item, index) in pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList" :key="item.id" :is-link="false" title="" :name="index">
            <template #title>
              <div class="inspectionConten">
                <img class="imgIcon" src="../../../../assets/images/inspectionCenter/ic-document.png" alt="" />
                <span class="serialNumber">{{ index + 1 }}</span>
                <span class="project">{{ item.detailName }}</span>
              </div>
            </template>
            <div v-for="(item2, inde) in item.maintainProjectdetailsTermReleaseList" :key="item2.id">
              <div v-if="item2.isNum == '1'" class="gist type1">
                <div class="titleContent">
                  <div class="projectItems">
                    <span class="typeTitle">巡检选项：</span>
                    <span class="typeContent">无</span>
                  </div>
                  <div class="projectItems">
                    <span class="typeTitle">巡检要点：</span>
                    <span class="typeContent">{{ item2.content ? item2.content : '' }}</span>
                  </div>
                </div>
              </div>
              <div v-if="item2.isNum == '2'" class="gist type2">
                <div class="titleContent">
                  <div class="projectItems">
                    <span class="typeTitle">巡检选项：</span>
                    <span class="typeContent">文本</span>
                  </div>
                  <div class="projectItems">
                    <span class="typeTitle">巡检要点：</span>
                    <span class="typeContent">{{ item2.content ? item2.content : '' }}</span>
                  </div>
                </div>
                <div class="fieldContent">
                  <span class="fieldTitle">请输入文本：</span>
                  <el-input v-model="item2.value" placeholder="请输入文本" maxlength="50" show-word-limit rows="2" type="textarea" autosize></el-input>
                </div>
              </div>
              <div v-if="item2.isNum == '0'" class="gist type3">
                <div class="titleContent">
                  <div class="projectItems">
                    <span class="typeTitle">巡检选项：</span>
                    <span class="typeContent">数值</span>
                  </div>
                  <div class="projectItems">
                    <span class="typeTitle">巡检要点：</span>
                    <span class="typeContent">{{ item2.content ? item2.content : '' }}</span>
                  </div>
                </div>
                <div class="fieldContent">
                  <span class="fieldTitle">
                    请输入数值：{{ '正常范围：' + item2.rangeStart + ' 至 ' + item2.rangeEnd + ' 单位：' + (item2.einheitName ? item2.einheitName : '无') }}
                  </span>
                  <el-input
                    v-model="item2.value"
                    placeholder="请输入数值"
                    type="number"
                    maxlength="20"
                    :class="item2.outRange ? 'verifyFail' : ''"
                    @blur="checkField(item2)"
                  ></el-input>
                  <div v-if="item2.outRange" style="color: red">输入范围异常</div>
                </div>
              </div>
              <div v-if="item2.isNum == '3'" class="gist type4">
                <div class="titleContent">
                  <div class="projectItems">
                    <span class="typeTitle">巡检选项：</span>
                    <span class="typeContent">单选</span>
                  </div>
                  <div class="projectItems">
                    <span class="typeTitle">巡检要点：</span>
                    <span class="typeContent">{{ item2.content ? item2.content : '' }}</span>
                  </div>
                </div>
                <div class="fieldContent">
                  <span class="fieldTitle">请选择：</span>
                  <el-radio-group v-model="activeChecked[index][inde]" text-color="#3562DB" fill="#3562DB">
                    <el-radio v-for="(item3, i) in item2.termJson" :key="item3.conText" :label="i">{{ item3.contText }}</el-radio>
                  </el-radio-group>
                </div>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
      <div class="content-group">
        <el-row>
          <el-col :span="22" class="bottom-item">
            <div class="item-title">巡检情况说明：</div>
            <el-input
              v-model="repairExplain"
              label="巡检情况说明"
              type="textarea"
              :rows="2"
              maxlength="200"
              show-word-limit
              placeholder="请输入巡检情况说明，字数限制200字以内"
            ></el-input>
          </el-col>
        </el-row>
        <el-row>
          <el-col v-loading="uploading" :span="22" class="bottom-item">
            <div class="item-title">附件：</div>
            <el-upload
              action=""
              list-type="picture-card"
              :file-list="fileList"
              multiple
              accept=".png,.jpg,.jpeg,.gif"
              :limit="9"
              :http-request="httpRequset"
              :on-preview="handlePictureCardPreview"
              :on-remove="handleRemove"
              :on-exceed="handleExceed"
            >
              <i class="el-icon-plus"></i>
            </el-upload>
          </el-col>
        </el-row>
      </div>
      <el-dialog v-dialogDrag :modal="false" append-to-body :visible.sync="dialogImageVisible" custom-class="img-dialog">
        <img height="100%" :src="dialogImageUrl" alt="" />
      </el-dialog>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button :disabled="pageLoading" @click="closed">取 消</el-button>
      <el-button :disabled="pageLoading" type="primary" @click="repairConfirm('3')">不合格提交</el-button>
      <el-button :disabled="pageLoading" type="primary" @click="repairConfirm('2')">合格提交</el-button>
    </span>
  </el-dialog>
</template>
<script>
import axios from 'axios'
export default {
  data() {
    return {
      pageLoading: false,
      dialogVisible: false,
      type: '',
      excute: {},
      result: {},
      pointRelease: {
        maintainProjectRelease: {
          maintainProjectdetailsReleaseList: []
        }
      },
      inspectionType: '',
      activeNames: [],
      activeChecked: [], // 单选默认选中
      repairExplain: '',
      fileList: [],
      dialogImageVisible: false,
      dialogImageUrl: '',
      attachmentUrl: [],
      uploading: false
    }
  },
  created() {},
  methods: {
    gtePointReleaseDetail(id) {
      this.$api.taskPointReleaseDetail({ id }).then((res) => {
        if (res.code == 200) {
          const { excute, result, pointRelease } = res.data
          this.excute = excute
          this.result = result
          this.pointRelease = pointRelease
          if (pointRelease.maintainProjectRelease) {
            this.type = pointRelease.maintainProjectRelease.equipmentTypeId
          }
          this.inspectionType = JSON.parse(pointRelease.particulars).taskPointTypeCode || JSON.parse(pointRelease.particulars).inspectionPointType
          if (pointRelease.maintainProjectRelease) {
            pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList.forEach((i, index) => {
              this.activeNames.push(index)
              if (i.maintainProjectdetailsTermReleaseList) {
                const iii = []
                i.maintainProjectdetailsTermReleaseList.forEach((j) => {
                  if (j.isNum == '3') {
                    j.termJson.forEach((k, ind) => {
                      if (k.isDefault == '0') {
                        iii.push(ind)
                      }
                    })
                  } else {
                    iii.push('')
                  }
                })
                this.activeChecked.push(iii)
              }
            })
          }
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 输入校验
    checkField(item) {
      if (!item.value || item.value.length == 0) {
        item.error = '内容不能为空'
      } else {
        item.error = ''
      }
      if (item.value && (item.value < parseInt(item.rangeStart) || item.value > parseInt(item.rangeEnd))) {
        item.outRange = true
      } else {
        item.outRange = false
      }
      console.log('item', item)
      this.$forceUpdate()
    },
    httpRequset(file) {
      const params = new FormData()
      params.append('file', file.file)
      this.uploading = true
      axios({
        method: 'post',
        url: __PATH.VUE_ICIS_API + 'file/upload',
        data: params,
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      })
        .then((res) => {
          if (res.data.code == 200) {
            this.attachmentUrl.push({
              fileKey: res.data.data.fileKey,
              name: file.file.name
            })
          }
          this.uploading = false
        })
        .catch(() => {
          this.fileList.forEach((i) => {
            return (i.status = 'failed')
          })
          this.$message.error('上传失败' || res.data.message)
        })
    },
    handleRemove(file, fileList) {
      this.attachmentUrl = this.attachmentUrl.filter((i) => i.name != file.name)
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogImageVisible = true
    },
    handleExceed() {
      this.$message.warning('最多上传 9 张图片！')
    },
    // 提交
    repairConfirm(type) {
      const arr = []
      let outRange = false
      if (this.pointRelease.maintainProjectRelease) {
        this.pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList.forEach((i, ind) => {
          if (this.type == 0) {
            // 日常
            const item = {}
            item.id = i.id
            item.normal = type
            arr.push(item)
          } else {
            i.maintainProjectdetailsTermReleaseList.forEach((item2, index) => {
              if (item2.isNum == '3') {
                let obj = {
                  id: item2.id,
                  value: item2.termJson[this.activeChecked[ind][index]].contText,
                  normal: ''
                }
                arr.push(obj)
              } else if (item2.isNum != '1') {
                if (!item2.value || item2.value.length == 0) {
                  item2.error = '内容不能为空'
                } else {
                  item2.error = ''
                }
                this.$forceUpdate()
                if (item2.outRange) {
                  outRange = true
                }
                let obj = {
                  id: item2.id,
                  value: item2.value,
                  normal: ''
                }
                arr.push(obj)
              }
            })
          }
        })
      }
      const isDevice = JSON.parse(this.pointRelease.particulars).assetsId ? JSON.parse(this.pointRelease.particulars).assetsId : false
      if (this.type == '0') {
        // 日常
        this.toSubmisPage(type, arr, isDevice)
      } else {
        // 专业
        if (type == '3') {
          // 不合格
          this.toSubmisPage(type, arr, isDevice)
        } else {
          // 校验所有填写项是否填写
          if (arr.some((i) => !i.value)) {
            this.$message.warning('请先完成任务书！')
          } else {
            if (outRange) {
              this.$message.warning('输入范围异常！')
            } else {
              this.toSubmisPage(type, arr, isDevice)
            }
          }
        }
      }
    },
    toSubmisPage(type, arr, isDevice) {
      let params = {
        unitCode: this.$store.state.user.userInfo.user.unitCode,
        hospitalCode: this.$store.state.user.userInfo.user.hospitalCode,
        staffId: this.$store.state.user.userInfo.user.staffId,
        staffName: this.$store.state.user.userInfo.user.staffName,
        taskPointReleaseId: this.pointRelease.id,
        taskId: this.pointRelease.taskId,
        spyScan: 1, // 定位状态
        details: this.repairExplain,
        attachmentUrl: this.attachmentUrl.length > 0 ? this.attachmentUrl.map((i) => i.fileKey).join(',') : '',
        submitLocation: '',
        officeId: this.$store.state.user.userInfo.user.deptId,
        officeName: this.$store.state.user.userInfo.user.deptName,
        callerTapeUrl: '', // 语音
        state: type
      }
      // 无任务书
      if (!arr.length) {
        params.isBookEmpty = true
        params.platformFlag = 2
        params.userName = this.$store.state.user.userInfo.user.staffName
        params.userId = this.$store.state.user.userInfo.user.staffId
      } else {
        params.answerMapList = JSON.stringify(arr)
      }
      this.pageLoading = true
      this.$api
        .taskSubmission(params)
        .then((res) => {
          if (res.code == '200') {
            if (isDevice) {
              // 设备巡检点
              let taskPointTypeCode = JSON.parse(this.pointRelease.particulars).taskPointTypeCode
              let ssmId = JSON.parse(this.pointRelease.particulars).ssmId
              let zdyId = JSON.parse(this.pointRelease.particulars).id
              const record = {
                unitCode: this.$store.state.user.userInfo.user.unitCode,
                hospitalCode: this.$store.state.user.userInfo.user.hospitalCode,
                assetsId: isDevice || (taskPointTypeCode === 'zdy' ? zdyId : ssmId),
                operationId: this.pointRelease.maintainProjectRelease.taskPointReleaseId,
                operationCode: '4', // 1:巡检 2:保养 3:报修/巡检 4.危化品
                operation: '巡检',
                record: '巡检单号：' + this.pointRelease.maintainProjectRelease.taskPointReleaseId
              }
              this.$api.saveOperateRecord(record).then(() => {})
            }
            this.pageLoading = false
            this.$message.success('执行成功！')
            this.dialogVisible = false
            Object.assign(this.$data, this.$options.data())
            this.$emit('refresh')
          } else {
            this.pageLoading = false
            this.$message.error(res.message || '执行失败！')
          }
        })
        .catch((err) => {
          this.$message.warning(err.message || '执行失败！')
        })
    },
    closed() {
      this.dialogVisible = false
      Object.assign(this.$data, this.$options.data())
    }
  }
}
</script>
<style lang="scss" scoped>
.task-inner {
  padding: 0 20px;
  max-height: 60vh;
  overflow-y: auto;
  .content-group {
    padding: 0 20px;
    .inspectionConten {
      display: flex;
      align-items: center;
      font-size: 14px;
      .imgIcon {
        width: 16px;
        margin-right: 10px;
      }
      .serialNumber {
        color: #3562db;
        margin-right: 10px;
      }
      .project {
        color: #1d2129;
      }
    }
    .gist {
      padding: 0 40px;
      .titleContent {
        display: flex;
        align-items: center;
        .projectItems {
          width: 50%;
          font-size: 14px;
          margin: 15px 0;
          .typeTitle {
            color: #96989a;
          }
          .typeContent {
            color: #333;
          }
        }
      }
      .fieldContent {
        padding: 0 40px;
        font-size: 14px;
        .fieldTitle {
          display: inline-block;
          margin: 15px 0;
        }
        .van-field {
          width: calc(75% - 100px);
          margin: 15px 0 15px 100px;
          border: 1px solid #e4e7ed;
          border-radius: 5px;
          box-shadow: 0 0 4px 0 rgba($color: #666, $alpha: 0.2);
        }
      }
    }
    .projectContent {
      .tableRow {
        display: flex;
        align-items: center;
        color: #333;
        font-size: 14px;
        min-height: 80px;
        border: 1px solid #f2f3f5;
        div {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 100%;
        }
        .serialNumber {
          width: 100px;
        }
        div:not(.serialNumber) {
          border-left: 1px solid #f2f3f5;
          width: calc((100% - 100px) / 3);
        }
      }
      .tableRow:nth-child(odd) {
        background-color: #f2f3f5;
      }
      .tableRow:nth-child(even) {
        background-color: #fff;
      }
      .tableRow:first-child {
        color: #666;
      }
    }
    .bottom-item {
      display: flex;
      margin-bottom: 20px;
      .item-title {
        width: 100px;
        text-align: right;
      }
      .el-textarea {
        width: calc(100% - 100px);
      }
    }
  }
}
:deep(.el-collapse) {
  border: none !important;
  .el-collapse-item__wrap {
    border: none !important;
  }
  .el-collapse-item__arrow {
    font-size: 16px;
  }
}
:deep(.verifyFail) {
  .el-input__inner {
    border: 1px solid red;
  }
}
:deep(.el-descriptions) {
  .el-descriptions-item__label {
    color: #96989a;
  }
  .el-descriptions-item__content {
    color: #333;
  }
}
:deep(.img-dialog) {
  .el-dialog__body {
    height: 70vh;
    display: flex;
    justify-content: center;
  }
}
</style>
