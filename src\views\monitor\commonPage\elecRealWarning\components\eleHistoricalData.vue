<template>
  <div class="echarts-wrapper">
    <div class="headBox">
      <div class="echart-title">{{ chartsName }}</div>
      <div v-loading style="height: 100%;">
        <el-form ref="formInline" :model="formInline" :rules="rules" :inline="true" class="advanced-search-form" label-position="right" label-width="110px">
          <el-date-picker
            v-model="formInline.daterange"
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="pickerOptions"
            style="margin-right: 20px;"
          >
          </el-date-picker>
          <el-button class="mini-button" @click="reset()">重置</el-button>
          <el-button class="mini-button" @click="search()">查询</el-button>
          <!-- <el-button class="mini-button" @click="down()">导出</el-button> -->
        </el-form>
      </div>
    </div>
    <div :id="'myLineChart' + index" v-loading="echartLoading" class="myLineChart" style="width: 100%;"></div>
    <!-- <div class="myLineChart" :id="'myLineChart'+index" @dblclick="dbclick()" v-loading="echartLoading" style="width: 100%"></div> -->

    <!-- <histoicalDataDetails
    ref="histoicalDataDetails"
    @dbclick="dbclick"
    :dialogVisibleRole="dialogVisibleRole"
    @closeDialog='closeDialog'
    @down="down"
    ></histoicalDataDetails> -->
  </div>
</template>

<script type=text/ecmascript-6>
import * as echarts from 'echarts'
import moment from 'moment'
moment.locale('zh-cn')
// import histoicalDataDetails from './histoicalDataDetails'
// 引入基本模板
// let echarts = require('echarts/lib/echarts')
// 引入柱状图和折线图组件
// require('echarts/lib/chart/line')
// 引入提示框和title组件
//  require('echarts/lib/component/tooltip')
export default {
  name: 'powerLineChart',
  components: {
    // histoicalDataDetails
  },
  props: {
    chartsData: {
      type: Object,
      default: () => {}
    },
    index: {
      type: Number,
      default: 0
    },
    requestHttp: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogVisibleRole: false,
      chartsName: '',
      echartLoading: false,
      xAxisData: [],
      seriesData: [],
      oldChartsData: {}, // 双击详情数据
      formInline: {
        daterange: []
      },
      rules: {},
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now() + 1000 * 60 * 60 || time.getTime() < Date.now() - 3600 * 1000 * 24 * 91
        },
        shortcuts: [
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      }
    }
  },
  created() {
    const end = new Date()
    const start = new Date()
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
    this.formInline.daterange = [moment(start).format('YYYY-MM-DD'), moment(end).format('YYYY-MM-DD')]
  },
  mounted() {
    this.drawLine(this.chartsData, this.index)
  },
  methods: {
    reset() {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      this.formInline.daterange = [moment(start).format('YYYY-MM-DD'), moment(end).format('YYYY-MM-DD')]
      this.search()
    },
    // 查询
    search() {
      this.$api
        .getDateDetails({
          // isHistory: 1,
          surveyCode: this.chartsData.surveyCode,
          parameterId: this.chartsData.parameterId,
          beginDate: this.formInline.daterange[0] || '',
          endDate: this.formInline.daterange[1] || ''
        })
        .then((res) => {
          this.drawLine(res.data, this.index)
        })
    },
    // 双击图表
    // dbclick(){
    //   this.dialogVisibleRole = true;
    //   console.log('双击');
    //   this.oldChartsData =[]
    //   this.$refs.histoicalDataDetails.echartLoading = true;
    //   this.$http.getDateDetails({
    //     isHistory:1,
    //     surveyCode:this.chartsData.surveyCode,
    //     parameterId:this.chartsData.parameterId,
    //     beginDate:this.formInline.daterange[0] || "",
    //     endDate:this.formInline.daterange[1] || "",
    //   }).then(res=>{
    //     this.oldChartsData = res.data;
    //     this.$refs.histoicalDataDetails.drawLine(this.oldChartsData)
    //   })
    // },
    // 导出
    down() {
      this.$http
        .realMonitoringExportReal({
          surveyCode: this.chartsData.surveyCode,
          parameterId: this.chartsData.parameterId,
          beginDate: this.formInline.daterange[0] || '',
          endDate: this.formInline.daterange[1] || ''
        })
        .then((res) => {
          this.$tools.downloadFile(res, this)
        })
    },
    // 关闭
    closeDialog() {
      this.dialogVisibleRole = false
    },
    drawLine(chartsData, index) {
      this.xAxisData = []
      this.seriesData = []
      // if(chartsData&&chartsData.parameterList.length>0){
      //   this.chartsName = chartsData.parameterName
      //   chartsData.parameterList.forEach((item, idx) => {
      //     this.xAxisData[idx] = item.parameterTime
      //     this.seriesData[idx] = item.parameterValue
      //   })
      //   this.echartLoading = false
      // }
      if (chartsData.parameterList) {
        this.chartsName = chartsData.parameterName
        chartsData.parameterList.forEach((item, idx) => {
          this.xAxisData[idx] = item.parameterTime
          this.seriesData[idx] = item.parameterValue
        })
      } else {
        this.xAxisData = []
        this.seriesData = []
      }
      this.echartLoading = false
      // 基于准备好的dom，初始化echarts实例
      let myChart = echarts.init(document.getElementById('myLineChart' + index))
      // 绘制图表
      myChart.setOption({
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '10.5%',
          right: '10%',
          bottom: '50',
          top: '50',
          containLabel: true
        },
        dataZoom: [
          {
            fillerColor: '#BBC3CE',
            backgroundColor: '#fff',
            height: 10,
            type: 'slider',
            bottom: 10,
            textStyle: {
              color: '#000'
            },
            start: 0,
            end: 85
          }
        ],
        xAxis: [
          {
            //  type: 'category',
            data: this.xAxisData,
            axisLabel: {
              show: true,
              // rotate:40,
              // interval:5,
              textStyle: {
                color: '#909399' // 更改坐标轴文字颜色
              }
            },
            axisLine: {
              lineStyle: {
                color: '#D8DEE7' // 更改坐标轴颜色
              }
            },
            // 去除x轴上的刻度线
            axisTick: {
              show: false
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            // name: this.chartsName,
            axisLabel: {
              formatter: '{value}',
              show: true,
              textStyle: {
                color: '#909399' // 更改坐标轴文字颜色
              }
            },
            // 控制y轴线是否显示
            axisLine: {
              lineStyle: {
                color: '#D8DEE7' // 更改坐标轴颜色
              }
            },
            // 网格样式
            splitLine: {
              show: true,
              lineStyle: {
                // color: ['rgba(255,255,255,.3)'],
                color: ['#f5f5f5'],
                width: 1,
                type: 'dashed'
              }
            },
            // 去除y轴上的刻度线
            axisTick: {
              show: false
            }
          }
        ],
        series: [
          {
            // symbol: "none", //折线的小圆点none不显示
            data: this.seriesData,
            type: 'line',
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                  {
                    // 颜色渐变函数 前四个参数分别表示四个位置依次为左、下、右、上
                    offset: 0,
                    color: '#F76B1C'
                  },
                  {
                    offset: 1,
                    color: '#FAD961'
                  }
                ])
              }
            }
          }
        ]
      })
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
.echarts-wrapper {
  background: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px 20px 0;
  box-sizing: border-box;

  .headBox {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .echart-title {
      height: 14px;
      border-left: 4px solid #5188fc;
      padding-left: 8px;
    }

    .mini-button {
      padding: 0 !important;
      width: 64px !important;
      height: 32px !important;
      font-size: 14px !important;
    }
  }

  .myLineChart {
    flex: 1;
    min-height: 100px;
  }
}
</style>
