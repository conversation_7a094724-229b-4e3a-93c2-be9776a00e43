<template>
  <el-dialog
    v-dialogDrag
    class="component dictionary-value-edit"
    :title="OperateType == 'add' ? '新增路线' : OperateType == 'edit' ? '编辑路线' : '路线详情'"
    width="750px"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :visible.sync="dialogShow"
    custom-class="model-dialog"
    :validate-on-rule-change="false"
    :before-close="closeRoadDialog"
  >
    <div class="content" style="padding: 10px; display: flex">
      <el-form ref="formRef" :model="formModel" :rules="rules" label-width="95px">
        <el-form-item label="路线编号" prop="code" style="width: 460px">
          <span v-if="OperateType == 'detail'">{{ formModel.code }}</span>
          <el-input v-else v-model="formModel.code" placeholder="请输入路线编号" maxlength="16" show-word-limit clearable></el-input>
        </el-form-item>
        <el-form-item label="路线名称" prop="name">
          <span v-if="OperateType == 'detail'">{{ formModel.name }}</span>
          <el-input v-else v-model="formModel.name" placeholder="请输入路线名称" maxlength="20" show-word-limit clearable></el-input>
        </el-form-item>
        <el-form-item label="始发地" prop="name">
          <span v-if="OperateType == 'detail'">{{ formModel.startStation }}</span>
          <el-input v-else v-model="formModel.startStation" placeholder="请输入始发地" maxlength="16" show-word-limit clearable></el-input>
        </el-form-item>
        <el-form-item label="目的地" prop="name">
          <span v-if="OperateType == 'detail'">{{ formModel.endStation }}</span>
          <el-input v-else v-model="formModel.endStation" placeholder="请输入目的地" maxlength="16" show-word-limit clearable></el-input>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <el-button type="primary" plain @click="closeRoadDialog">取消</el-button>
      <el-button v-if="OperateType != 'detail'" type="primary" @click="onSubmit">确认</el-button>
    </template>
  </el-dialog>
</template>
<script>
import { UsingStatusOptions } from '@/views/operationPort/constant'
import { uploadAcceptDict } from '@/util/dict.js'
export default {
  name: 'DictionaryValueEdit',
  props: {
    OperateType: {
      type: String,
      default: ''
    },
    dialogShow: {
      type: Boolean,
      default: false
    },
    operateId: {
      type: String,
      default: ''
    }
  },
  data: function () {
    return {
      uploadAcceptDict,
      formModel: {
        name: '',
        code: '',
        startStation: '',
        endStation: ''
      },
      rules: {
        name: [{ required: true, message: '请输入路线名称' }],
        code: [{ required: true, message: '请输入路线编码' }],
        startStation: [{ required: true, message: '请输入始发地' }],
        endStation: [{ required: true, message: '请输入目的地' }]
      },
      dictData: [],
      loadingStatus: false,
      statusOptions: UsingStatusOptions
    }
  },
  mounted() {
    this.$watch('dialogShow', () => {
      if (this.operateId) {
        if (this.OperateType == 'edit' || this.OperateType == 'detail') {
          this.getRoadmapDeatil(this.operateId)
        }
      }
    })
  },
  methods: {
    // dialog点击右上角关闭按钮，重置表单
    closeRoadDialog() {
      this.$emit('closeRoadDialog')
      this.resetForm()
    },
    // 获取路线详情
    getRoadmapDeatil(val) {
      this.loadingStatus = true
      this.$api.fileManagement.roadmapDetail({ id: val }).then((res) => {
        this.loadingStatus = false
        if (res.code == 200) {
          this.formModel = { ...res.data }
        }
      })
    },
    // 表单提交
    onSubmit() {
      this.$refs.formRef
        .validate()
        .catch(() => Promise.reject())
        .then(() => {
          this.loadingStatus = true
          const params = {
            code: this.formModel.code, // 	路线编码
            name: this.formModel.name, // 路线名称
            startStation: this.formModel.startStation, // 路线始发地
            endStation: this.formModel.endStation // 路线目的地
          }
          if (this.operateId) {
            params.id = this.operateId
            return this.$api.fileManagement.roadmapUpdate(params)
          } else {
            return this.$api.fileManagement.roadmapSave(params)
          }
        })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success(res.msg)
            this.$emit('success')
            this.closeRoadDialog()
          } else {
            throw res.msg || res.message
          }
        })
        .catch((msg) => {
          msg && this.$message.error(msg)
        })
        .finally(() => {
          this.loadingStatus = false
        })
    },
    resetForm() {
      this.formModel = {
        name: '',
        code: '',
        startStation: '',
        endStation: ''
      }
    }
  }
}
</script>
<style lang="scss">
.component.dictionary-value-edit {
  .el-form {
    background-color: #fff;
    padding: 10px 16px 0;
    .el-form-item {
      .el-cascader,
      .el-select {
        width: 100%;
      }
    }
    &.readonly {
      .el-form-item__label::before {
        display: none;
      }
      .el-form-item {
        .el-input__inner,
        .el-cascader,
        .el-select {
          background-color: transparent;
          border: none;
        }
        .el-input__suffix {
          display: none;
        }
        .el-upload-list__item-status-label {
          display: none;
        }
        .el-upload__tip {
          display: none;
        }
      }
    }
  }
  .content {
    width: 100%;
    max-height: 500px !important;
    overflow: auto;
    background-color: #fff !important;
  }
  .dictionary-value-edit {
    &__color {
      vertical-align: middle;
      .el-input-group__prepend {
        padding: 0;
        line-height: 0;
        border-radius: 4px 0 0 4px;
      }
      .el-color-picker {
        height: 30px;
      }
      .el-color-picker__trigger {
        border: none;
        height: 30px;
        width: 30px;
        padding: 2px;
      }
      .el-input__inner {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      }
    }
    &__upload {
      &.hide {
        .el-upload {
          opacity: 0;
          transition-duration: 0.5s;
          pointer-events: none;
        }
      }
    }
  }
}
</style>
