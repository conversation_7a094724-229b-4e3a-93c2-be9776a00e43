<template>
  <el-dialog title="监测参数配置" width="60%" :visible.sync="dialogShow" :close-on-click-modal="false" custom-class="model-dialog" :before-close="closeDialog">
    <div class="harvester_content">
      <div class="table_box">
        <div class="search_box">
          <div class="search_select">
            <span class="require-span"><span>*</span> 安防网关1:</span>
            <el-select v-model="searchForm.dataServerId" filterable placeholder="请选择安防网关" @change="formVerification">
              <el-option v-for="item in dataServerList" :key="item.applicationId" :label="item.serverId" :value="item.applicationId"></el-option>
            </el-select>
            <span class="require-span" style="margin-left: 30px"><span>*</span> 传感器类型:</span>
            <el-select v-model="searchForm.harvesterTypeId" filterable placeholder="请选择传感器类型" @change="formVerification('reset')">
              <el-option v-for="item in sensorTypeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </div>
          <div class="search_input">
            <p>传感器名称:</p>
            <el-input v-model="searchForm.name" suffix-icon="el-icon-search" placeholder="请输入传感器名称" @input="nameInputEvent"></el-input>
          </div>
        </div>
        <!-- <div class="table_div"> -->
        <TablePage
          ref="harvesterTable"
          v-loading="tableLoading"
          class="table_div"
          row-key="id"
          :showPage="false"
          :tableColumn="tableColumn"
          :data="tableData"
          :height="tableHeight"
          @selection-change="handleSelectionChange"
        >
        </TablePage>
        <!-- </div> -->
      </div>
      <div class="parameter_box">
        <span class="require-span"><span>*</span> 监测参数:</span>
        <div class="check_box_list">
          <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">全选</el-checkbox>
          <el-checkbox-group v-model="checkParameterList" @change="handleCheckedCitiesChange">
            <el-checkbox v-for="(item, index) in parameterList" :key="index" :label="item.parameterId">{{ item.parameterName }}</el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="groupSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script lang="jsx">
import $ from 'jquery'
import { debounce } from 'lodash/function'
export default {
  name: 'harvesterDialog',
  props: {
    dialogShow: {
      type: Boolean,
      default: false
    },
    dialogData: {
      type: Object,
      default: () => {}
    },
    // 传感器选中类型为 单选还是多选
    harvesterCheckType: {
      type: String,
      default: 'raido'
    }
  },
  data() {
    return {
      dataServerList: [], // 安防网关列表
      sensorTypeList: [], // 传感器类型列表
      parameterList: [], // 监测参数checkBox列表
      searchForm: {
        dataServerId: '',
        harvesterTypeId: '',
        name: ''
      },
      tableData: [],
      tableLoading: false,
      tableHeight: 'calc(100%)',
      checkParameterList: [],
      checkRadio: '',
      radioCheckData: {}, // 选中数据
      multipleCheckData: [], // 多选数据
      checkAll: false,
      isIndeterminate: true
    }
  },
  computed: {
    tableColumn() {
      let obj = [
        {
          label: '',
          width: 80,
          align: 'center',
          render: (h, row) => {
            return (
              <el-radio
                v-model={this.checkRadio}
                label={row.row.id}
                onChange={() => {
                  this.harvesterListRadioCheck(row.row)
                }}
              >
                &nbsp;
              </el-radio>
            )
          },
          hasJudge: this.harvesterCheckType == 'radio'
        },
        {
          type: 'selection',
          align: 'center',
          width: 80,
          hasJudge: this.harvesterCheckType == 'checkbox'
        }
      ]
      if (this.searchForm.dataServerId === 9999) {
        return [
          ...obj,
          {
            prop: 'name',
            label: '设备名称',
            required: true
          },
          {
            prop: 'ip',
            label: '设备IP'
          },
          {
            prop: 'no',
            label: '设备ID'
          }
        ]
      } else {
        return [
          ...obj,
          {
            prop: 'name',
            label: '传感器名称',
            required: true
          },
          {
            prop: 'no',
            label: '传感器编码'
          },
          {
            prop: 'manufacturerName',
            label: '厂家'
          },
          {
            prop: 'modelName',
            label: '型号'
          }
        ]
      }
    }
  },
  mounted() {
    const data = this.$options.data.call(this)
    for (const key in data) {
      this.$data[key] = data[key]
    }
    // 数据主机
    if (this.$route.query.monitortype && this.$route.query.monitortype === 'security') {
      this.dataServerList = JSON.parse(sessionStorage.getItem('securityDataServer'))
    } else {
      this.dataServerList = JSON.parse(sessionStorage.getItem('dataServer'))
    }
    // 编辑带参数及回显
    if (this.dialogData?.eventType == 'edit') {
      this.searchForm = {
        dataServerId: this.dialogData.dataServerId,
        harvesterTypeId: this.dialogData.harvesterTypeId
      }
      if (this.searchForm.dataServerId === 9999) {
        this.getSensorList('init', 'getOtherSensorList')
      } else {
        this.getSensorList('init', 'getSensorList')
      }
    } else {
      this.searchForm.dataServerId = this.dataServerList[0].applicationId
    }
    if (this.searchForm.dataServerId === 9999) {
      this.getSensorTypeList()
    } else {
      this.getDictionaryList()
    }
  },
  methods: {
    // 获取第三方数据类型
    getSensorTypeList() {
      this.$api.GetSensorTypeList().then((res) => {
        if (res.code == '200') {
          this.sensorTypeList = res.data || []
          // 初始化选中第一个安防网关和传感器类型
          if (this.dialogData?.eventType == 'add') {
            this.searchForm.harvesterTypeId = this.sensorTypeList[0].id
            this.getSensorList('search', 'getOtherSensorList')
          }
        }
      })
    },
    // 初始化获取字典项
    getDictionaryList() {
      // 传感器类型
      this.$api
        .getDictionaryList({
          dictType: 10
        })
        .then((res) => {
          this.sensorTypeList = res.data || []
          // 初始化选中第一个安防网关和传感器类型
          if (this.dialogData?.eventType == 'add') {
            this.searchForm.harvesterTypeId = this.sensorTypeList[0].id
            this.getSensorList('search', 'getSensorList')
          }
        })
    },
    // 传感器列表
    getSensorList(status = 'search', key) {
      this.$tools.cancelAjax()
      let data = {
        ...this.searchForm,
        batchOrSingle: this.harvesterCheckType == 'radio' ? 1 : 0,
        userId: this.$store.state.user.userInfo.user.staffId
      }
      this.tableLoading = true
      this.$api[key](data).then((res) => {
        this.tableLoading = false
        if (res.code == '200') {
          this.tableData = res.data.sensorList
          this.parameterList = res.data.allParamList || []
          // 单选的时候 判断选中的数据是否在列表中
          if (this.harvesterCheckType == 'radio') {
            const checkRadioIndex = this.tableData.findIndex((e) => e.id == this.checkRadio)
            if (checkRadioIndex !== -1) {
              // 将该元素移动至数组第一位
              this.tableData.unshift(this.tableData.splice(checkRadioIndex, 1)[0])
              this.parameterList = this.tableData[0]?.paramList || []
            }
          }
          // this.tableHeight = 300
          this.tableHeight = $('.table_div').height()
          // 回显(初始化渲染选中数据)
          if (this.dialogData?.eventType == 'edit' && status == 'init') {
            this.checkParameterList = this.dialogData.parameterIdsList
            if (this.harvesterCheckType == 'checkbox') {
              this.multipleCheckData = this.tableData.filter((e) => this.dialogData.harvesterRealIdList.includes(e.id))
              this.$nextTick(() => {
                this.multipleCheckData.forEach((e) => {
                  this.$refs.harvesterTable.toggleRowSelection(e)
                })
              })
            } else {
              this.radioCheckData = this.tableData.find((e) => e.id == this.dialogData.harvesterRealId)
              this.checkRadio = this.radioCheckData.id
              this.parameterList = this.radioCheckData?.paramList || []
            }
            this.handleCheckedCitiesChange(this.checkParameterList)
          }
        } else {
          this.tableData = []
          this.parameterList = []
        }
      })
    },
    nameInputEvent: debounce(function () {
      this.formVerification()
    }, 800),
    // 校验表单是否填写
    formVerification(type) {
      // 切换重置
      if (type == 'reset') {
        this.checkRadio = ''
        this.radioCheckData = {}
        this.multipleCheckData = []
        this.checkParameterList = []
        this.parameterList = []
      }
      if (type != 'reset' && type != undefined) {
        if (type == 9999) {
          this.getSensorTypeList()
        } else {
          this.getDictionaryList()
        }
      } else {
        if (this.searchForm.dataServerId && this.searchForm.harvesterTypeId) {
          if (this.searchForm.dataServerId === 9999) {
            this.getSensorList('search', 'getOtherSensorList')
          } else {
            this.getSensorList('search', 'getSensorList')
          }
        }
      }
    },
    // 传感器列表单选
    harvesterListRadioCheck(currentRow) {
      this.radioCheckData = currentRow
      // 监测参数列表使用单选时传感器的参数
      this.parameterList = currentRow.paramList
      this.checkParameterList = []
    },
    // 传感器列表多选
    handleSelectionChange(val) {
      this.multipleCheckData = val
    },
    closeDialog() {
      this.$emit('closeDialog')
    },
    groupSubmit() {
      if (Object.keys(this.radioCheckData).length || this.multipleCheckData.length) {
        if (this.checkParameterList.length) {
          let baseParams = {
            dataServerId: this.searchForm.dataServerId,
            dataServerName: this.dataServerList.find((e) => e.applicationId == this.searchForm.dataServerId).serverId,
            harvesterTypeId: this.searchForm.harvesterTypeId,
            harvesterType: this.sensorTypeList.find((e) => e.id == this.searchForm.harvesterTypeId).name,
            harvesterName: this.radioCheckData.name,
            harvesterId: this.radioCheckData.no,
            harvesterRealId: this.searchForm.dataServerId === 9999 ? "" : this.radioCheckData.id,
            state: this.radioCheckData.state,
            ip: this.radioCheckData.ip,
            port: this.radioCheckData.port
          }
          let parameterList = []
          if (this.harvesterCheckType == 'radio') {
            parameterList = [
              {
                ...baseParams,
                parameterList: this.parameterList.filter((e) => this.checkParameterList.includes(e.parameterId))
              }
            ]
          } else {
            parameterList = this.multipleCheckData.map((e) => {
              return {
                ...baseParams,
                harvesterName: e.name,
                harvesterId: e.no,
                harvesterRealId: this.searchForm.dataServerId === 9999 ? "" : e.id,
                state: e.state,
                ip: e.ip,
                port: e.port,
                parameterList: this.parameterList.filter((e) => this.checkParameterList.includes(e.parameterId))
              }
            })
          }
          this.$emit('submitDialog', parameterList)
        } else {
          this.$message({
            message: '请选择监测参数！',
            type: 'warning'
          })
        }
      } else {
        this.$message({
          message: '请选择传感器！',
          type: 'warning'
        })
      }
    },
    handleCheckAllChange(flag) {
      this.checkParameterList = flag ? Array.from(this.parameterList, ({ parameterId }) => parameterId) : []
      this.isIndeterminate = false
    },
    handleCheckedCitiesChange(value) {
      let checkedCount = value.length
      this.checkAll = checkedCount === this.parameterList.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.parameterList.length
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog__body {
  max-height: calc(78vh - 110px);
  height: calc(85vh - 110px);
  display: flex;
  padding: 15px 10px;
  overflow: auto;
}
.model-dialog {
  margin-top: 8vh !important;
  // .el-dialog__body {
  //   max-height: calc(78vh - 110px);
  //   height: calc(85vh - 110px);
  //   // overflow: auto!important;;
  // }
  .harvester_content {
    // flex: 1;
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .table_box,
    .parameter_box {
      padding: 10px 20px 10px 10px;
      background: #fff;
      .require-span {
        span {
          color: #fa403c;
          vertical-align: middle;
        }
      }
    }
    .parameter_box {
      margin-top: 15px;
      height: 120px;
      overflow: auto;
      .check_box_list {
        padding: 10px;
        .el-checkbox {
          margin-bottom: 5px;
        }
      }
    }
    .table_box {
      flex: 1;
      // height: calc(100% - 200px);
      height: 0;
      display: flex;
      flex-direction: column;
      .search_box {
        .search_select {
          margin-bottom: 30px;
          > span {
            margin-right: 10px;
          }
        }
        .search_input {
          display: flex;
          align-items: center;
          width: 500px;
          margin-bottom: 15px;
          p {
            width: 90px;
            margin: 0;
            margin-right: 15px;
          }
        }
      }
      .table_div {
        // height: calc(100%);
        flex: 1;
        height: 0;
      }
    }
  }
}
</style>
