/*
 * @Author: hedd <EMAIL>
 * @Date: 2025-02-19 15:45:12
 * @LastEditors: hedd <EMAIL>
 * @LastEditTime: 2025-05-08 18:11:07
 * @FilePath: \ihcrs_pc\src\api\module\supplierAssess.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/*
 * @Description:
 */
import { postFormData, postRequest, getRequest, downFile } from '../http.js'
// 服务前缀
const PrefixService = __PATH.VUE_SPACE_API
const PrefixSysService = __PATH.VUE_SYS_API
const SPACE_API = __PATH.SPACE_API
export default {
  // 岗位管理
  /** 查询证书字典启用 */
  getDictConfigUseData: postRequest('archive/dictConfig/queryDictConfigInUse', __PATH.SPACE_API),
  getDictData: getRequest('web/prDictionaryDetailsD/getSuperiorData', PrefixService), // 获取职称文化名称字典
  getUserInfoById: getRequest('userInfo/getUserByStaffId', PrefixSysService), // 查询用户详情
  getUnitListNew: getRequest('unitManager/unit-manager/getUnitTree', PrefixService), // 单位列表新
  getSelectedDept: getRequest('departmentManager/department-manager/getSelectedDept', PrefixService), // 根据所属单位回去部门列表
  // 证书成员
  saveUserCertificateInfo: postRequest('certificateDict/relationCertificateUser', SPACE_API), // 关联成员和证书
  certificateListByPage: postRequest('certificateDict/pageCertificateByUser', SPACE_API), // 根据人员信息查询证书列表
  updateUserCertificateInfo: postRequest('certificateDict/updateCertificateUser', SPACE_API), // 修改关联成员和证书信息
  getUserCertificateInfoById: getRequest('certificateDict/getCertificateUserOne', SPACE_API), // 查询成员的证书信息
  deleteUserCertificateInfoById: getRequest('certificateDict/deleteCertificateUser', SPACE_API), // 删除成员证书
  insertHospitalStaffList: postRequest('/hospitalStaff/hospital-staff/add', PrefixService), // 新增人员
  updateHospitalStaffList: postRequest('hospitalStaff/hospital-staff/update', PrefixService), // 修改人员
  getDeptTreeData: getRequest('dept/selectDeptTree', PrefixSysService), // 获取权限树
  queryUserInfoListByPage: postRequest('userInfo/getUserInfoListByDept', PrefixSysService), // 获取用户列表
  bathDeleteUserData: postRequest('userInfo/deleteUserByIds', PrefixSysService), // 批量删除人员
  // 班次
  queryShiftInfoByPage: postRequest('shiftManage/queryShiftManageByPage', SPACE_API), // 分页查询班次
  deleteShiftInfoById: getRequest('shiftManage/deleteShiftManage', SPACE_API), // 删除班次
  getShiftInfoById: getRequest('shiftManage/queryShiftManageById', SPACE_API), // 查看班次详情
  saveShiftInfo: postRequest('shiftManage/insertShiftManage', SPACE_API), // 保存班次
  updateShiftInfo: postRequest('shiftManage/updateShiftManage', SPACE_API), // 编辑班次
  // 数据看板
  queryDutyGroupRawDataByPage: postRequest('supSign/queryRawDataDutyGroup', SPACE_API), // 分页查询值班考勤组原始数据
  getPostCountData: postRequest('supSign/queryPostCount', SPACE_API), // 岗位分布（实际/要求）
  getSignGroupConutData: getRequest('supSign/querySignGroupConut', SPACE_API), // 查询当前值班考勤组情况
  getOnDutyCounData: getRequest('supSign/queryOnDutyCount', SPACE_API), // 查询当前在岗人数情况
  queryCurrentDutyGroupByPage: postRequest('supSign/queryCurrentDutyGroup', SPACE_API), // 分页查询当前值班考勤组
  getShiftGroupById: postRequest('supSign/countShiftData', SPACE_API), // 班组详情数据
  queryShiftDetailBypage: postRequest('supSign/queryShiftDetail', SPACE_API), // 班组详情数据分页
  getShiftData: getRequest('supSign/queryShiftData', SPACE_API), // 获取班次下拉框数据
  getDutyLOgList: postRequest('clientDutyHandOver/queryDutyHandOverByPage', SPACE_API), // 获取值班记录列表数据,
  // 值班岗
  getDutyPostList: getRequest('dutyPost/getDutyPostOne', SPACE_API), // 查询值班岗(单一)
  saveDutyPostInfo: postRequest('dutyPost/insertDutyPost', SPACE_API), // 新增值班岗
  addPersonToDutyPostData: getRequest('dutyPost/addPersonToDutyPost', SPACE_API), // 值班岗位添加人员
  addTerminalToDutyPostData: getRequest('dutyPost/relationTerminalToDutyPost', SPACE_API), // 值班岗位添加定位终端
  removePersonToDutyPostData: getRequest('dutyPost/removeDutyPostPerson', SPACE_API), // 岗位移除人员
  removeTerminalToDutyPostData: getRequest('dutyPost/removeTerminalToDutyPost', SPACE_API), // 岗位移除人员
  updateDutyPostInfo: postRequest('dutyPost/updateDutyPost', SPACE_API), // 编辑值班岗
  getDutyPostData: getRequest('dutyPost/getDutyPostList', SPACE_API), // 查询值班岗列表
  queryPersonByDutyPostByPage: postRequest('dutyPost/pagePersonByDutyPost', SPACE_API), // 根据值班岗查询人员
  queryTerminalByDutyPostByPage: postRequest('dutyPost/getTerminalListByDutyPost', SPACE_API), // 根据值班岗查询定位终端
  getDutyPostById: getRequest('dutyPost/getDutyPostOne', SPACE_API), // 查询值班岗位
  deleteDutyPostData: getRequest('dutyPost/deleteDutyPost', SPACE_API), // 删除值班岗位
  getPostListByLoginUserData: getRequest('post/getPostListByLoginUser', SPACE_API), // 岗位平铺展示
  submitOffDutyRuleData: postRequest('offlinePost/insertOfflineConfig', SPACE_API), // 新增离岗规则
  updateOffDutyRuleData: postRequest('offlinePost/updateOfflineConfig', SPACE_API), // 修改离岗规则
  queryTerminalByDutyPostData: postRequest('locateDevice/locateDevicePage', SPACE_API), // 根据值班岗查询定位设备列表
  removeLocateAssetsData: getRequest('locateDevice/removeLocateDevice', SPACE_API), // 移除定位设备
  insertLocateAssetsData: postRequest('locateDevice/insertLocateDevice', SPACE_API), // 新增定位设备
  getLocateAssetsData: postRequest('locateDevice/locateDevicePageByInsp', SPACE_API), // 定位终端列表获取
  bindLocateAssets: getRequest('locateDevice/bindLocateDevice', SPACE_API), // 绑定定位设备
  getRuleConfigDetails: getRequest('offlinePost/getOfflineConfig', SPACE_API), // 获取离岗规则详情
  unbindLocateAssets: getRequest('locateDevice/unBindLocateDevice', SPACE_API), // 解绑定位设备
  getLocatePointList: postRequest('asset/assetDetailsSync/syncBeaconDataPage', __PATH.VUE_ICIS_API), // 获取定位点列表
  getOfflineRecord: postRequest('offlinePost/offlineRecordPage', SPACE_API), // 获取离岗记录
  exportOfflineRecord: downFile('offlinePost/exportOfflineRecord', SPACE_API), // 离岗记录导出
  updateLeavePostLocatePoint: postRequest('offlinePost/updateOfflineLocate', SPACE_API), // 修改离岗定位点
  updateLeavePostMonitor: postRequest('offlinePost/updateOfflineMonitor', SPACE_API), // 修改离岗监测
  updateLeavePostRadius: postRequest('offlinePost/updateOfflineRadius', SPACE_API), // 修改离岗规则
  removeLeavePostLocatePoint: getRequest('offlinePost/removeOfflineLocate', SPACE_API), // 移除定位点
  batchAddLocatePoint: postRequest('locateDevice/batchInsertLocateDevice', SPACE_API), // 批量新增定位设备
  // 岗位
  getPostChildData: getRequest('post/getPostChild', SPACE_API), // 根据父级查询子级岗位
  queryPersonByPostByPage: postRequest('post/pagePersonByPost', SPACE_API), // 根据岗位查询人员
  removePersonToPostData: getRequest('post/removePostPerson', SPACE_API), // 岗位移除人员
  addPersonToPostData: getRequest('post/addPersonToPost', SPACE_API), // 岗位添加人员
  deletePostData: getRequest('post/deletePost', SPACE_API), // 删除岗位
  insertPostData: postRequest('post/insertPost', SPACE_API), // 新增岗位
  updatePostData: postRequest('post/updatePost', SPACE_API), // 修改岗位
  getPostById: getRequest('post/getPostOne', SPACE_API), // 查询岗位
  // 值班考勤配置
  insertDutyAttendance: postRequest('SupDutyAttendance/insertDutyAttendance', SPACE_API), // 新增值班考勤配置
  updateDutyAttendance: postRequest('SupDutyAttendance/updateDutyAttendance', SPACE_API), // 编辑值班考勤配置
  deleteDutyAttendanceById: getRequest('SupDutyAttendance/deleteDutyAttendanceById', SPACE_API), // 删除值班考勤配置
  queryDutyAttendanceById: getRequest('SupDutyAttendance/queryDutyAttendanceById', SPACE_API), // 查询值班考勤配置
  queryDutyAttendanceByPage: postRequest('SupDutyAttendance/queryDutyAttendanceByPage', SPACE_API), // 值班考勤配置分页列表
  // 考勤终端管理
  bandingAttendanceTerminal: postRequest('attendanceTerminal/bandingAttendanceTerminal', SPACE_API), // 绑定考勤终端
  updateAttendanceTerminalInfo: postRequest('attendanceTerminal/updateAttendanceTerminalInfo', SPACE_API), // 编辑绑定考勤终端
  removeAttendanceTerminal: getRequest('attendanceTerminal/removeAttendanceTerminal', SPACE_API), // 删除考勤终端
  pageAttendanceTerminal: postRequest('SupDutyAttendance/pageAttendanceTerminal', SPACE_API), // 考勤终端分页列表
  getAttendanceTerminalOne: getRequest('attendanceTerminal/getAttendanceTerminalOne', SPACE_API), // 考勤终端详情
  getAttendanceTerminalManagePassword: getRequest('attendanceTerminal/getAttendanceTerminalManagePassword', SPACE_API), // 查询考勤终端密码
  attendanceTerminalManagePassword: getRequest('attendanceTerminal/attendanceTerminalManagePassword', SPACE_API), // 考勤终端密码保存
  getActivationInfo: getRequest('attendanceTerminal/getActivationInfo', SPACE_API), // 查询考勤终端授权状态
  updateActivationInfo: postRequest('attendanceTerminal/saveActivationInfo', SPACE_API), // 保存考勤终端授权状态
  getFingerprintList: postRequest('fingerData/queryUserAndFingerByPage', SPACE_API), // 指纹分页列表
  // 定位终端
  queryAttendanceTerminalByPage: postRequest('attendanceTerminal/pageAttendanceTerminal', SPACE_API), // 分页查询考勤终端
  // 岗位SOP
  querySupPostSopByPage: postRequest('suPostSop/querySupPostSopByPage', SPACE_API), // 分页查询岗位SOP
  deleteSupPostSopById: getRequest('suPostSop/delete', SPACE_API), // 删除岗位SOP
  disOrUse: postRequest('suPostSop/disOrUse', SPACE_API), // 状态启用/停用
  saveOrUpdate: postRequest('suPostSop/saveOrUpdate', SPACE_API), // 保存岗位SOP
  getSupPostSopDetail: getRequest('suPostSop/getDetail', SPACE_API) // 获取岗位SOP详情
}
