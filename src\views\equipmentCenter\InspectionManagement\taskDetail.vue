<template>
  <div class="contentMain">
    <div class="topFilter">
      <div class="backBar">
        <span style="cursor: pointer" @click="$router.go(-1)">
          <i class="el-icon-arrow-left"></i>
          任务详情
        </span>
      </div>
    </div>
    <div v-loading="pageLoading" class="taskDetailContent">
      <div class="taskDetail">
        <!-- 基本信息 -->
        <div class="baseInfo">
          <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
            <span class="green_line"></span>
            基本信息
          </div>
          <el-form ref="form" :model="row" label-width="90px">
            <el-row :gutter="60">
              <el-col :span="8">
                <el-form-item label="任务名称:">
                  <el-tooltip :content="row.taskName" placement="top">
                    <span>{{ row.taskName }}</span>
                  </el-tooltip>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="周期类型:">
                  <span>{{ cycleTypeList.find((i) => i.cycleType == row.cycleType)?.label ?? '' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="systemType == '2' ? '应保养日期:' : systemType == '5' ? '应年检日期:' : '应巡日期:'">
                  <el-date-picker v-if="activeType == 'edit'" v-model="form.taskStartDate" type="date" placeholder="选择日期"> </el-date-picker>
                  <span v-else>{{ moment(row.taskStartTime).format('YYYY-MM-DD') }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="systemType == '2' ? '应保养时间:' : systemType == '5' ? '应年检时间:' : '应巡时间:'">
                  <el-time-picker
                    v-if="activeType == 'edit'"
                    v-model="form.timerange"
                    is-range
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    placeholder="选择时间范围"
                  >
                  </el-time-picker>
                  <template v-else>
                    <el-tooltip :content="moment(row.taskStartTime).format('YYYY-MM-DD HH:mm:ss') + '-' + moment(row.taskEndTime).format('YYYY-MM-DD HH:mm:ss')" placement="top">
                      <span>{{ moment(row.taskStartTime).format('YYYY-MM-DD HH:mm:ss') + '-' + moment(row.taskEndTime).format('YYYY-MM-DD HH:mm:ss') }}</span>
                    </el-tooltip>
                  </template>
                </el-form-item>
              </el-col>
              <el-col v-if="row.cycleType != '6' && row.cycleType != '7'" :span="8">
                <el-form-item label="完成期限:">
                  <span>{{ row.finalTime ? row.finalTime + '天' : '' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="systemType == '2' ? '保养部门:' : systemType == '5' ? '年检部门:' : '巡检部门:'">
                  <el-tooltip :content="row.distributionTeamName" placement="top">
                    <span>{{ row.distributionTeamName }}</span>
                  </el-tooltip>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="systemType == '2' ? '保养人员:' : systemType == '5' ? '年检人员:' : '巡检人员:'">
                  <el-select v-if="activeType == 'edit'" v-model="form.planPersonCode" multiple :multiple-limit="20" placeholder="请选择人员">
                    <el-option v-for="item in personList" :key="item.id" :label="item.staffName" :value="item.id"> </el-option>
                  </el-select>
                  <span v-else>{{ row.planPersonName || row.distributionTeamName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="systemType == '2' ? '实际保养时间:' : systemType == '5' ? '实际年检时间:' : '实际巡检时间:'" class="actualTime">
                  <el-tooltip v-if="pointList.length > 1" :content="(row.executeStartTime ?? '') + ' - ' + (row.executeEndTime ?? '')" placement="top">
                    <span>{{ (row.executeStartTime ?? '') + ' - ' + (row.executeEndTime ?? '') }}</span>
                  </el-tooltip>
                  <span v-else>{{ row.executeStartTime ?? '' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="完成状态:">
                  <span>{{ row.taskStatus == '2' ? '已完成' : '未完成' }}</span>
                </el-form-item>
              </el-col>
              <el-col v-if="$route.meta.type != 4" :span="8">
                <el-form-item label="超时状态:">
                  <span>{{ row.isOverTime == '2' ? '已超时' : '未超时' }}</span>
                </el-form-item>
              </el-col>
              <el-col v-if="$route.meta.type != 4" :span="24">
                <el-form-item label="超时情况说明:" label-width="120px">
                  <span>{{ row.timeoutDeclaration || '' }}</span>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <!-- 任务点 -->
        <div class="taskPoint">
          <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
            <span class="green_line"></span>
            {{ systemType == '2' ? '保养点' : systemType == '5' ? '年检点' : '巡检点' }}
          </div>
          <el-table :key="itemKey" :data="pointList" style="width: 80%">
            <el-table-column label="序号" type="index" width="80"> </el-table-column>
            <el-table-column :label="systemType == '2' ? '保养点名称' : systemType == '5' ? '年检点名称' : '巡检点名称'" show-overflow-tooltip>
              <template slot-scope="scope">
                <el-link type="primary" @click="detail(scope.row)">{{ scope.row.inspectionPointName }}</el-link>
              </template>
            </el-table-column>
            <el-table-column prop="assetsRemarks" label="备注说明" show-overflow-tooltip> </el-table-column>
            <el-table-column :label="systemType == '2' ? '保养结果' : systemType == '5' ? '年检结果' : '巡检结果'" align="center">
              <template slot-scope="scope">
                <span :style="{ color: scope.row.state == '3' || scope.row.state == '4' ? 'red' : '' }">{{
                  scope.row.state == '2' ? '合格' : scope.row.state == '3' ? '不合格' : scope.row.state == '4' ? '异常报修' : systemType == '5' ? '未年检' : '未巡检'
                }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="guaranteeCode" show-overflow-tooltip label="工单编号" align="center"></el-table-column>
            <el-table-column
              prop="excuteTime"
              show-overflow-tooltip
              :label="systemType == '2' ? '实际保养时间' : systemType == '5' ? '实际年检时间' : '实际巡检时间'"
              align="center"
            ></el-table-column>
            <el-table-column prop="implementPersonName" show-overflow-tooltip label="执行人员" align="center"></el-table-column>
            <el-table-column v-if="systemType !== '6'" prop="spyScan" show-overflow-tooltip label="定位状态" align="center"> </el-table-column>
            <el-table-column v-if="activeType == 'detail' && $route.meta.type == 4 && checkPerson() && checkDate()" label="操作" width="100" show-overflow-tooltip>
              <template v-if="!scope.row.state" slot-scope="scope">
                <div>
                  <el-link type="primary" @click="tasksInputEvent(scope.row)">录入</el-link>
                </div>
              </template>
            </el-table-column>
            <el-table-column v-if="activeType == 'edit'" label="操作" width="100" show-overflow-tooltip>
              <template slot-scope="scope">
                <div>
                  <el-link :disabled="pointList.length == 1" type="danger" @click="deleteRow(scope.row)">删除</el-link>
                  <el-link type="primary" :disabled="activeType == 'detail' || pointList.length == 1 || scope.row.sort == pointList.length" @click="pointMove(scope.row, 'down')">
                    下移
                  </el-link>
                  <el-link type="primary" :disabled="activeType == 'detail' || pointList.length == 1 || scope.row.sort == 1" @click="pointMove(scope.row, 'up')"> 上移 </el-link>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <div class="bottomBar">
      <div class="bottomWrap">
        <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db" @click="$router.go(-1)">取消</el-button>
        <el-button v-if="activeType == 'edit'" type="primary" @click="addTaskDetail">保存</el-button>
      </div>
    </div>
    <taskInput ref="taskInputDialog" @refresh="initEvent()"></taskInput>
  </div>
</template>
<script>
import moment from 'moment'
import taskInput from './components/taskInput.vue'
import qs from 'qs'
import axios from 'axios'
export default {
  name: 'taskDetail',
  components: { taskInput },
  beforeRouteEnter(to, from, next) {
    if (to.meta.title) {
      to.meta.title = to.query.type == 'edit' ? '任务编辑' : '任务详情'
    }
    next()
  },
  async beforeRouteLeave(to, from, next) {
    if (!['taskManagement', 'taskPointDetail'].includes(to.name)) {
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      systemType: '',
      moment,
      itemKey: '',
      activeType: '',
      taskId: '',
      row: {},
      // 周期类型
      cycleTypeList: [
        {
          cycleType: 8,
          label: '单次'
        },
        {
          cycleType: 6,
          label: '每日'
        },
        {
          cycleType: 0,
          label: '每周'
        },
        {
          cycleType: 2,
          label: '每月'
        },
        {
          cycleType: 3,
          label: '季度'
        },
        {
          cycleType: 5,
          label: '全年'
        },
        {
          cycleType: 7,
          label: '自定义'
        }
      ],
      // 巡检点列表
      pointList: [],
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      personList: [],
      form: {
        id: '',
        maintainPlanRegions: '',
        taskStartDate: '',
        taskStartTime: '',
        taskEndTime: '',
        timerange: [],
        planPersonCode: '',
        planPersonName: ''
      },
      pageLoading: false
    }
  },
  created() {
    if (!this.$store.state.keepAlive.list.includes('taskManagement')) {
      this.initEvent()
    }
  },
  activated() {
    this.initEvent()
  },
  methods: {
    // 校验危化品巡检录入权限
    checkPerson() {
      if (this.row.planPersonCode) {
        return this.row.planPersonCode.split(',').includes(this.$store.state.user.userInfo.user.staffId)
      } else if (this.row.distributionTeamId) {
        return this.row.distributionTeamId.split(',').includes(this.$store.state.user.userInfo.user.deptId)
      }
    },
    // 校验是否是超时任务
    checkDate() {
      return new Date(this.row.taskStartTime).getTime() <= new Date().getTime() && new Date(this.row.taskEndTime).getTime() >= new Date().getTime()
    },
    initEvent() {
      if (this.$route.path.indexOf('/InspectionManagement') != -1) {
        this.systemType = '1'
      } else if (this.$route.path.indexOf('/MaintenanceManagement') != -1) {
        this.systemType = '2'
      } else if (this.$route.path.indexOf('/comInspectionManagement') != -1) {
        this.systemType = '3'
      } else if (this.$route.path.indexOf('/hazardousChemicalGoods_inspection') != -1) {
        this.systemType = '4'
      } else if (this.$route.path.indexOf('/annualManagement') != -1) {
        this.systemType = '5'
      } else if (this.$route.path.indexOf('/vp_taskManagement') != -1) {
        this.systemType = '6'
      }
      if (this.$route.query.hasOwnProperty('systemType')) {
        this.systemType = this.$route.query.systemType
      }
      this.activeType = this.$route.query.type
      this.taskId = this.$route.query.taskId
      // this.row = this.$route.query.row.id ? this.$route.query.row : JSON.parse(sessionStorage.getItem('row'))
      this.getInspectionPointList()
      if (this.activeType == 'edit') {
        this.form.taskStartDate = moment(this.row.taskStartTime)
        this.form.taskEndTime = this.row.taskEndTime
        this.getLersonnelList(this.row.distributionTeamId)
        this.form.timerange = [moment(this.row.taskStartTime), moment(this.row.taskEndTime)]
        this.form.planPersonCode = this.row.planPersonCode.split(',')
        this.form.planPersonName = this.form.planPersonName
        this.form.taskStartTime = this.row.taskStartTime
      }
    },
    // 周期类型
    cycleTypeFn(row) {
      const item = this.cycleTypeList.filter((i) => i.cycleType == row.cycleType)
      return item[0].label
    },
    // 人员列表
    getLersonnelList(id) {
      let params = {
        current: 1,
        size: 20,
        sex: '',
        pmId: '',
        officeId: id,
        postId: '',
        stationStatus: ''
      }
      this.$api.staffList(params).then((res) => {
        if (res.code == 200) {
          this.personList = res.data.records
        }
      })
    },
    // 任务点列表
    getInspectionPointList() {
      const data = {
        taskId: this.taskId,
        pageNo: this.paginationData.currentPage,
        pageSize: 999
      }
      this.pageLoading = true
      this.$api.getInspectionPointList(data).then((res) => {
        if (res.code == '200') {
          if (res.code == '200') {
            this.row = res.data.taskMap
            const listArr = []
            res.data.list.forEach((i) => {
              let items = JSON.parse(i.particulars)
              items.pointId = i.id
              items.carryOutFlag = i.carryOutFlag
              items.excuteTime = i.excuteTime
              items.guaranteeCode = i.guaranteeCode
              items.spyScan = i.spyScan
              items.state = i.state
              items.implementPersonName = i.implementPersonName
              listArr.push(items)
            })
            listArr.sort((a, b) => a.sort - b.sort)
            this.pointList = listArr
          }
        }
        this.pageLoading = false
      })
    },
    // 巡检点详情
    detail(row) {
      this.$router.push({
        path: 'taskPointDetail',
        query: {
          row: {
            pointId: row.pointId,
            inspectionPointName: row.inspectionPointName
          },
          isTask: true,
          locationFlag: this.row.locationFlag
        }
      })
    },
    // 巡检点移动
    pointMove(row, type) {
      const indexNum = this.pointList.map((item) => item.id).indexOf(row.id)
      if (type == 'down') {
        row.sort = row.sort + 1
        this.pointList[indexNum + 1].sort = row.sort - 1
      } else {
        row.sort = row.sort - 1
        this.pointList[indexNum - 1].sort = row.sort + 1
      }
      this.pointList = this.pointList.sort((a, b) => a.sort - b.sort)
      this.itemKey = Math.random()
    },
    deleteRow(row) {
      this.$confirm('删除后将无法恢复，是否确定删除？', '信息提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(() => {
          this.$api
            .deleteInspectionPoint({
              id: row.pointId,
              planCount: this.pointList.filter((i) => i.id != row.id).length,
              taskId: this.taskId
            })
            .then((res) => {
              if (res.code == '200') {
                this.$message({
                  type: 'success',
                  message: `${this.systemType == '2' ? '保养点删除成功' : this.systemType == '5' ? '年检点删除成功' : '巡检点删除成功'}`
                })
                let dataList = this.pointList.filter((i) => i.id != row.id)
                if (dataList.length > 0) {
                  dataList.forEach((i, index) => {
                    i.sort = index + 1
                  })
                }
                this.pointList = dataList
                this.itemKey = Math.random()
              }
            })
        })
        .catch(() => {})
    },
    // 危化品录入
    tasksInputEvent(row) {
      this.taskExcute().then((res) => {
        const { code, data, message } = res.data
        if (code == '200') {
          const ele = this.$refs.taskInputDialog
          ele.dialogVisible = true
          ele.gtePointReleaseDetail(row.pointId)
        } else {
          this.$message.error(message || '任务不可执行')
        }
      })
    },
    // 校验任务是否可执行
    async taskExcute() {
      const params = {
        unitCode: this.$store.state.user.userInfo.user.unitCode,
        hospitalCode: this.$store.state.user.userInfo.user.hospitalCode,
        taskId: this.row.id,
        typeValue: '1'
      }
      const isExcute = await axios({
        method: 'post',
        url: __PATH.VUE_ICIS_API + '/planTaskNewApiController/executePlanTaskByTaskId',
        data: qs.stringify(params),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Authorization: 'Bearer ' + this.$store.state.user.token,
          hospitalSth: localStorage.getItem('hospitalSth') ? localStorage.getItem('hospitalSth') : ''
        }
      })
      return isExcute
    },
    // 任务编辑
    addTaskDetail() {
      const personNames = []
      this.personList.find((i) => {
        this.form.planPersonCode.forEach((j) => {
          if (i.id == j) {
            personNames.push(i.staffName)
          }
        })
      })
      const maintainPlanRegions = []
      this.pointList.forEach((i) => {
        const item = {
          id: i.pointId,
          particulars: i
        }
        maintainPlanRegions.push(item)
      })
      const params = {
        id: this.taskId,
        updateCode: this.$store.state.user.userInfo.userId,
        updateName: this.$store.state.user.userInfo.username,
        maintainPlanRegions: JSON.stringify(maintainPlanRegions),
        taskStartTime: moment(this.form.taskStartDate).format('YYYY-MM-DD') + ' ' + moment(this.form.timerange[0]).format('HH:mm:ss'),
        taskEndTime: moment(this.form.taskEndTime).format('YYYY-MM-DD') + ' ' + moment(this.form.timerange[1]).format('HH:mm:ss'),
        planPersonCode: this.form.planPersonCode.join(','),
        planPersonName: personNames.join(',')
      }
      this.$api.taskEdit(params).then((res) => {
        if (res.code == '200') {
          this.$message({
            type: 'success',
            message: '保存成功'
          })
          this.$router.go('-1')
        } else {
          this.$message({
            type: 'error',
            message: res.message || '保存失败'
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.contentMain {
  height: 100%;
  .topFilter {
    margin: 15px 15px 0;
    padding: 15px;
    width: calc(100% - 30px);
    height: 70px;
    background-color: #fff;
    .backBar {
      color: #121f3e;
      height: 30px;
      border-bottom: 1px solid #dcdfe6;
    }
  }
  .taskDetailContent {
    margin: 0 15px 15px;
    height: calc(100% - 152px);
    .taskDetail {
      overflow-y: scroll;
      overflow-x: hidden;
      padding: 0 15px 15px;
      width: 100%;
      height: 100%;
      background-color: #fff;
      .baseInfo {
        .el-col-8 {
          height: 45px;
        }
        :deep(.el-form-item) {
          .el-form-item__label {
            color: #414653;
          }
          .el-form-item__content {
            color: #121f3e;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
        :deep(.el-form-item__content) {
          width: 50%;
          .el-date-editor {
            width: 100%;
          }
        }
        .actualTime {
          :deep(.el-form-item__label) {
            width: 106px !important;
          }
        }
      }
    }
  }
  .bottomBar {
    height: 52px;
    width: 100%;
    background-color: #fff;
    display: flex;
    justify-content: right;
    align-items: center;
    .bottomWrap {
      padding: 0 16px;
    }
  }
}
</style>
