<template>
  <PageContainer>
    <div slot="content" class="role-content" style="height: 100%;">
      <div class="role-content-left">
        <div class="toptip">
          <span class="green_line"></span>
          管控小组
        </div>
        <div class="left_content">
          <el-input v-model.trim="filterText" clearable placeholder="输入关键字进行过滤"></el-input>
          <div class="tree">
            <el-tree
              ref="tree"
              v-loading
              style="margin-top: 10px;"
              :data="data"
              :props="defaultProps"
              :filter-node-method="filterNode"
              :default-expanded-keys="expanded"
              node-key="id"
              highlight-current
              @node-click="handleNodeClick"
              @check="treeChecked"
            ></el-tree>
          </div>
        </div>
      </div>
      <div class="role-content-right">
        <div style="height: 100%;">
          <div class="search-from">
            <el-input v-model.trim="filters.name" placeholder="人员姓名" style="width: 200px;"></el-input>
            <el-input v-model.trim="filters.mobilePhone" placeholder="手机号" style="width: 200px;"></el-input>
            <!-- <el-input
            v-model.trim="filters.controlTeamId"
            placeholder="所属部门"
            class="sino_sdcp_input mr15"
          ></el-input> -->
            <el-select v-model="filters.userType" class="sino_sdcp_input mr15" placeholder="人员类型" filterable>
              <el-option v-for="(item, index) in riskLevelList" :key="index" :label="item.name" :value="item.id" class="set_zindex"></el-option>
            </el-select>
            <el-button type="primary" @click="resetData">重置</el-button>
            <el-button type="primary" class="sino-button-sure-search" @click="selectList">查询</el-button>
          </div>
          <div class="middle_tools">
            <el-button type="primary" icon="el-icon-plus" @click="addDate">新增</el-button>
            <el-button type="primary" icon="el-icon-edit" :disabled="multipleSelection.length != 1" @click="upData">编辑</el-button>
            <el-button type="primary" icon="el-icon-delete" :disabled="multipleSelection.length != 1" @click="delData">删除</el-button>
            <el-button type="primary" icon="el-icon-download" @click="downLoad">导出</el-button>
            <el-button type="primary" icon="el-icon-lock" :disabled="multipleSelection.length < 1" @click="deletePerson">修改权限</el-button>
            <el-button type="primary" icon="el-icon-key" :disabled="multipleSelection.length < 1" @click="openSetPwd(1)">密码修改</el-button>
            <el-button type="primary" icon="el-icon-key" @click="openSetPwd(2)">一键重置密码</el-button>
          </div>
          <div class="contentTable">
            <div class="contentTable-main table-content">
              <el-table
                ref="multipleTable"
                v-loading="tableLoading"
                highlight-current-row
                :data="tableData"
                border
                stripe
                style="width: 100%;"
                :height="tableHeight"
                @row-click="rowHandleClick"
                @selection-change="handleSelectionChange"
                @row-dblclick="dblclick"
              >
                <!-- <template slot="empty">
              <p class="tableEmptyLabel">{{ $store.state.tableLabel }}</p>
            </template> -->
                <el-table-column type="selection" width="55" align="center"></el-table-column>
                <el-table-column type="index" width="80" label="序号" align="center">
                  <template slot-scope="scope">
                    <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="name" show-overflow-tooltip label="姓名"></el-table-column>
                <el-table-column prop="gender" show-overflow-tooltip label="性别" width="80">
                  <template slot-scope="scope">
                    <span>
                      {{ scope.row.gender == 1 ? '男' : scope.row.gender == 2 ? '女' : '' }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="mobilePhone" show-overflow-tooltip label="手机号"></el-table-column>
                <el-table-column prop="userType" show-overflow-tooltip label="人员类型">
                  <template slot-scope="scope">
                    <span>
                      {{ scope.row.userType == 1 ? '院内' : scope.row.userType == 2 ? '院外' : '' }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="controlTeamName" show-overflow-tooltip label="所属部门"></el-table-column>
                <el-table-column prop="teamTypeName" show-overflow-tooltip label="部门类别">
                  <!-- <template slot-scope="scope">
                <span>
                  {{ scope.row.teamType == 1 ? '院级' : scope.row.teamType == 2 ? '科级' : '' }}
                </span>
              </template> -->
                </el-table-column>
                <el-table-column prop="positionType" show-overflow-tooltip label="权限">
                  <template slot-scope="scope">
                    <span>
                      {{ scope.row.positionType == 1 ? '组长' : scope.row.positionType == 2 ? '组员' : '' }}
                    </span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="contentTable-footer">
              <el-pagination
                :current-page="paginationData.currentPage"
                :page-sizes="[15, 30, 50, 100]"
                :page-size="paginationData.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="paginationData.total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              ></el-pagination>
            </div>
          </div>
        </div>
      </div>
      <el-dialog v-loading="updateControlLoading" custom-class="model-dialog" title="修改权限" :visible.sync="dialogVisibleRole" @close="dialogVisibleRole = false">
        <div v-loading style="min-height: 200px; width: 100%; background-color: #fff; padding: 10px;">
          <el-radio v-model="radio" label="1">组长</el-radio>
          <el-radio v-model="radio" label="2">组员</el-radio>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="dialogVisibleRole = false">取 消</el-button>
          <el-button type="primary" class="sino-button-sure" @click="closeDialogRole">确定</el-button>
        </span>
      </el-dialog>
      <el-dialog v-loading="pwdLoading" custom-class="model-dialog" :title="pwdTitle" :visible.sync="dialogVisibleRole2" :before-close="cancelPwd" @close="closeDialog2">
        <div v-loading style="min-height: 200px; width: 100%; background-color: #fff; padding: 10px;">
          <div v-if="pwdtype == 2" style="margin-bottom: 20px; color: #f13e3e;">友好提醒：该操作将会对所有用户进行密码重置，请谨慎操作!</div>
          <el-form ref="formInline" :model="formInline" :rules="rules" :inline="true" class="advanced-search-form" label-position="right" label-width="110px">
            <el-form-item label="设置新密码" prop="NewPassWord">
              <el-input
                v-model="formInline.NewPassWord"
                placeholder="6~16位数字、字符、字母大小写"
                style="width: 350px;"
                autocomplete="off"
                :type="pwdType1"
                :maxlength="16"
                @change="loginError.errorStatus = true"
              >
                <i slot="suffix" class="el-icon-view" @click="showPwd('pwdType1')">
                  <span style="position: absolute; left: 4px;" :style="pwdType1 == '' ? 'display:none' : ''">\</span>
                </i>
              </el-input>
            </el-form-item>
            <br />
            <el-form-item label="确认登录密码" prop="confirmPassWord">
              <el-input
                v-model="formInline.confirmPassWord"
                placeholder="请确认密码"
                style="width: 350px;"
                autocomplete="off"
                :type="pwdType2"
                :maxlength="16"
                @change="loginError.errorStatus = true"
              >
                <i slot="suffix" class="el-icon-view" @click="showPwd('pwdType2')">
                  <span style="position: absolute; left: 4px;" :style="pwdType2 == '' ? 'display:none' : ''">\</span>
                </i>
              </el-input>
            </el-form-item>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="cancelPwd">取 消</el-button>
          <el-button type="primary" class="sino-button-sure" @click="setpwd">确定</el-button>
        </span>
      </el-dialog>
      <exportDialog
        ref="dialog-export"
        :exportType="exportType"
        :materRows="multipleSelection"
        :formInline="filters"
        :dialogVisibleExport="dialogVisibleExport"
        @closeDialog="closeDialog"
      ></exportDialog>
    </div>
  </PageContainer>
</template>

<script>
import tableListMixin from '@/mixins/tableListMixin.js'
import { transData } from '@/util'
import exportDialog from '@/components/recordExport/recordExport' // 导出
export default {
  name: 'userControlGroup',
  components: { exportDialog },
  mixins: [tableListMixin],
  data() {
    let validatePass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入密码'))
      } else {
        if (this.formInline.confirmPassWord !== '') {
          this.$refs.formInline.validateField('confirmPassWord')
        }
        callback()
      }
    }
    let validatePass2 = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.formInline.NewPassWord) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    return {
      dialogVisibleExport: false,
      loginError: {
        errorStatus: true,
        errorMessage: ''
      },
      pwdTitle: '修改密码',
      pwdtype: 1,
      formInline: {
        NewPassWord: '',
        confirmPassWord: ''
      },
      rules: {
        NewPassWord: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { validator: validatePass, trigger: 'blur' },
          { pattern: /[a-zA-Z0-9!@#$%^&*]{6,16}$/, message: '密码格式不正确' }
        ],
        confirmPassWord: [
          { required: true, message: '请确认新密码', trigger: 'blur' },
          { validator: validatePass2, trigger: 'blur' }
        ]
      },
      riskLevelList: [
        {
          name: '院内',
          id: 1
        },
        {
          name: '院外',
          id: 2
        }
      ],
      tableCode: '',
      filters: {
        name: '',
        mobilePhone: '',
        // controlTeamName: "",
        // controlTeamId: "",
        userType: ''
      },
      filterText: '',
      radio: '',
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      multipleSelection: [],
      tableData: [],
      advancClose: true,
      treeLoading: false,
      tableLoading: false,
      organizationTypeArr: [],
      defaultProps: {
        children: 'children',
        label: function (data, node) {
          return data.teamName
        },
        value: 'id'
      },
      data: [],
      checkedData: '',
      dialogVisibleRole: false,
      updateControlLoading: false,
      pwdLoading: false,
      dialogVisibleRole2: false,
      roleType: JSON.parse(sessionStorage.getItem('LOGINDATA')).moduleIdentity || '',
      exportType: 8,
      pwdType1: 'password',
      pwdType2: 'password',
      expanded: []
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    },
    screenHeight(n, o) {}
  },
  // activated() {
  //   this.init()
  // },
  mounted() {
    this.init()
  },
  methods: {
    rowHandleClick(row, column, event) {
      this.$refs.multipleTable.toggleRowSelection(row)
    },
    // 获取管控小组
    init() {
      this.treeLoading = true
      this.$api.ipsmGetControlGroupInfoList({}).then((res) => {
        this.treeLoading = false
        let planText = {
          id: '#',
          teamName: '安全管控部门',
          parentId: '',
          allParentIds: '',
          level: 0
        }
        let list = res.data.list
        list.push(planText)
        this.data = transData(list, 'id', 'parentId', 'children')
        this.$nextTick(() => {
          this.expanded = this.tableCode ? [this.tableCode] : [this.data[0].id]
          this.$refs.tree.setCurrentKey(this.tableCode ? this.tableCode : this.data[0].id)
        })
        let checkItem = list.find((item) => {
          return item.id == this.tableCode
        })
        this.checkedData = checkItem || this.data[0]
        this.tableCode = this.tableCode ? this.tableCode : this.data[0].id
        this.tableData = []
        this.getTableData()
      })
    },
    getTableData(val) {
      this.tableLoading = true
      let data = {
        currentPage: this.paginationData.currentPage,
        pageSize: this.paginationData.pageSize,
        controlTeamId: this.checkedData.id == '#' ? '' : this.checkedData.id, // 选第一级传空
        ...this.filters
      }
      this.$api
        .ipsmGetControlTeamUserList(data)
        .then((res) => {
          this.tableLoading = false
          if (res.code == 200) {
            this.tableLoading = false
            this.tableData = res.data.list
            this.paginationData.total = parseInt(res.data.sum)
          }
        })
        .catch(() => {
          this.tableLoading = this.$store.state.loadingShow
        })
    },
    // 查询
    selectList() {
      this.paginationData.currentPage = 1
      this.getTableData()
    },
    deletePerson(row) {
      this.radio = ''
      this.dialogVisibleRole = true
    },
    // 密码显示隐藏
    showPwd(type) {
      this[type] === 'password' ? (this[type] = '') : (this[type] = 'password')
    },
    // 修改权限
    closeDialogRole(val) {
      if (!this.radio) {
        return this.$message.error('请选择要分配的权限')
      }
      this.updateControlLoading = true
      let id = []
      this.multipleSelection.map((item) => {
        id.push(item.id)
      })
      let params = {
        ids: id.join(','),
        positionType: this.radio
      }
      if (this.multipleSelection.length == 1) {
        params = {
          ...params,
          ...this.multipleSelection[0],
          positionType: this.radio
        }
      }
      this.$api.ipsmUpdateControlTeamUser(params).then((res) => {
        this.updateControlLoading = false
        if (res.code == 200) {
          this.$message.success(res.message)
          this.dialogVisibleRole = false
        } else {
          this.$message.error(res.message)
        }
        this.getTableData(this.checkedData.id)
        // this.$refs.tree.setCurrentKey(this.data[0].id);
      })
    },
    // 查看
    dblclick(val) {
      this.$router.push({
        name: 'addControl',
        query: { type: 'check', id: val.id }
      })
    },
    // 新增
    addDate() {
      this.$router.push({
        name: 'addControl',
        query: { type: 'add', tableCode: this.checkedData }
      })
    },
    // 修改
    upData() {
      this.$router.push({
        name: 'addControl',
        query: { type: 'edit', id: this.multipleSelection[0].id }
      })
    },
    // 删除
    delData() {
      this.$confirm('确认删除?', '提醒', { type: 'warning' }).then((res) => {
        this.$api.ipsmDelControlTeamUser({ id: this.multipleSelection[0].id }).then((res) => {
          if (res.code == 200) {
            // 删除最后一页的最后一条数据时跳转回最后一页的上一页
            this.paginationData.currentPage = this.$tools.paginationData(this.paginationData.total, this.paginationData.pageSize, this.paginationData.currentPage)

            this.$message.success(res.message)
            this.getTableData()
          } else {
            this.$message.error(res.message)
          }
        })
      })
    },
    // 取消修改密码
    cancelPwd() {
      this.formInline.NewPassWord = ''
      this.formInline.confirmPassWord = ''
      this.$refs['formInline'].resetFields()
      this.dialogVisibleRole2 = false
    },
    // 修改密码
    openSetPwd(i) {
      if (i == 1) {
        this.pwdTitle = '密码修改'
      } else {
        this.pwdTitle = '一键重置密码'
      }
      this.pwdtype = i
      this.dialogVisibleRole2 = true
    },
    // 提交修改密码
    setpwd() {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          this.pwdLoading = true
          let mobilePhone = []
          this.multipleSelection.map((item) => {
            mobilePhone.push(item.mobilePhone)
          })
          let data = {
            password: this.formInline.NewPassWord,
            mobilePhone: mobilePhone.join(',')
          }
          this.$api.ipsmUpdatePassword(data).then((res) => {
            this.pwdLoading = false
            if (res.code == 200) {
              this.$message.success(res.message)
              this.cancelPwd()
            } else {
              this.$message.error(res.message)
            }
          })
        }
      })
    },
    filterNode(value, data) {
      if (!value) return true
      return data.teamName.indexOf(value) !== -1
    },
    // 导出
    downLoad(row) {
      this.dialogVisibleExport = true
    },
    closeDialog() {
      this.dialogVisibleExport = false
    },

    closeDialog2(val) {
      this.dialogVisible2 = false
    },
    treeChecked(data, checked) {
      this.checkedData = data
      this.$refs.tree.setCheckedNodes([data])
    },
    // 查询列表重置
    resetData() {
      this.filters.name = ''
      this.filters.mobilePhone = ''
      this.filters.controlTeamName = ''
      this.filters.userType = ''
      this.init()
    },

    //  ----------------------------------------------------分页相关------------------------------------------------------------
    handleSizeChange(val) {
      this.paginationData.currentPage = 1
      this.paginationData.pageSize = val
      this.getTableData(this.checkedData.id)
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.getTableData(this.checkedData.id)
    },
    //  ----------------------------------------------------树状结构相关---------------------------------------------------------
    /**
     * 树状图点击
     */
    handleNodeClick(data, checked) {
      this.paginationData.currentPage = 1
      // this.$store.commit('changeTableLabel', 'search')
      this.checkedData = data
      this.tableCode = data.id
      this.$refs.tree.setCheckedNodes([data])
      this.getTableData(data.id)
    },
    //  ------------------------------------------------操作表格中数据------------------------------------------------------------
    handleSelectionChange(val) {
      this.multipleSelection = val
    }
  }
}
</script>
<style lang="scss" scoped>
// el-tree-node is-expanded is-focusable
::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: linear-gradient(to right, #d9e1f8, #fff);
}

::v-deep .el-tree-node__content {
  height: 32px;
}

.role-content {
  height: 100%;
  display: flex;

  .role-content-left {
    width: 246px;
    height: 100%;
    padding: 10px;
    background: #fff;
    border-radius: 4px;
    margin-right: 12px;

    .left_content {
      height: calc(100% - 60px);
      padding: 10px;
      // overflow: auto;
      .tree {
        margin-top: 10px;
        height: calc(100% - 40px);
        overflow: auto;
      }
    }
  }

  .role-content-right {
    flex: 1;
    height: 100%;
    min-width: 0;
    padding: 10px;
    background: #fff;
    border-radius: 4px;

    .search-from {
      padding-bottom: 12px;

      & > div {
        margin-right: 10px;
      }

      & > button {
        margin-top: 12px;
      }
    }

    .middle_tools {
      margin-bottom: 10px;
    }

    .contentTable {
      height: calc(100% - 100px);
      display: flex;
      flex-direction: column;

      .contentTable-main {
        height: calc(100% - 40px);
        flex: 1;
        overflow: auto;
      }

      .contentTable-footer {
        padding: 10px 0 0;
      }
    }
  }
}
</style>
