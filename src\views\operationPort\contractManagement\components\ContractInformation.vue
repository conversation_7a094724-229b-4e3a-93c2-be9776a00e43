<!-- eslint-disable vue/no-mutating-props -->
<template>
  <div>
    <div v-if="!isEdit" class="details">
      <el-row :gutter="24">
        <el-col :span="9">
          <FormTextItem title="合同名称" :value="formData.archiveName" />
        </el-col>
        <el-col :span="9">
          <FormTextItem title="合同编号" :value="formData.archiveNumber" />
        </el-col>
        <el-col :span="9">
          <FormTextItem title="关联合同" :value="formData.relatedNames" />
        </el-col>
        <el-col :span="9">
          <FormTextItem title="合同分类" :value="archiveModel" />
        </el-col>
        <el-col :span="9">
          <FormTextItem title="合同类别" :value="category" />
        </el-col>
        <el-col :span="9">
          <FormTextItem title="供应商" :value="formData.supplier" />
        </el-col>
        <el-col :span="9">
          <FormTextItem title="签订部门" :value="formData.departmentName" />
        </el-col>
        <el-col :span="9">
          <FormTextItem title="经办人" :value="formData.handledBy" />
        </el-col>
        <el-col :span="9">
          <FormTextItem title="合同金额（元）" :value="formData.amount" />
        </el-col>
        <el-col :span="9">
          <FormTextItem title="保证金额（元）" :value="formData.bond" />
        </el-col>
        <el-col :span="9">
          <FormTextItem title="签订日期"
            :value="formData.archiveDate ? moment(formData.archiveDate).format('YYYY-MM-DD') : ''" />
        </el-col>
        <el-col :span="9">
          <FormTextItem title="合同有效期"
            :value="formData.effectiveDate ? moment(formData.effectiveDate).format('YYYY-MM-DD') : ''" />
        </el-col>
        <el-col :span="9">
          <FormTextItem title="合同生效日期"
            :value="formData.startDate ? moment(formData.startDate).format('YYYY-MM-DD') : ''" />
        </el-col>
        <el-col :span="9">
          <FormTextItem title="合同结束日期" :value="formData.endDate ? moment(formData.endDate).format('YYYY-MM-DD') : ''" />
        </el-col>
        <el-col :span="24">
          <FormTextItem title="合同存放位置" :value="formData.saveLocation" type="textarea" />
        </el-col>
        <el-col :span="24">
          <FormTextItem title="备注" :value="formData.remark" type="textarea" />
        </el-col>
        <el-col :span="24">
          <FormTextItem title="附件" type="slot">
            <el-table :data="formData.archiveFileList">
              <el-table-column label="序号" type="index" width="50"> </el-table-column>
              <el-table-column property="fileName" label="文件名"> </el-table-column>
              <el-table-column property="fileSize" label="大小（M）"> </el-table-column>
              <el-table-column property="option" label="操作" width="120">
                <template slot-scope="scope">
                  <el-button type="text" @click="handleView(scope.row)">查看</el-button>
                  <el-button type="text" @click="handleDownload(scope.row)"> 下载 </el-button>
                </template>
              </el-table-column>
            </el-table>
          </FormTextItem>
        </el-col>
      </el-row>
    </div>
    <div v-else>
      <el-form ref="form" :model="formData" label-width="140px" :rules="rules">
        <el-row :gutter="24">
          <el-col :md="9">
            <el-form-item label="合同名称" prop="archiveName">
              <el-input v-model="formData.archiveName" placeholder="请输入合同名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="9">
            <el-form-item label="合同编号" prop="archiveNumber">
              <el-input v-model="formData.archiveNumber" placeholder="请输入合同编号" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="9">
            <el-form-item label="关联合同">
              <VirtualListSelect v-model="formData.relatedIds" :options="contractList" collapse-tags multiple
                :propsOptions="propsOptions" />
            </el-form-item>
          </el-col>
          <el-col :md="9">
            <el-form-item label="合同分类" prop="archiveModel">
              <el-select v-model="formData.archiveModel">
                <el-option v-for="item in contractClassification" :key="item.value" :value="item.value"
                  :label="item.label" clearable></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="9">
            <el-form-item label="合同类别" prop="category">
              <el-select v-model="formData.category">
                <el-option v-for="item in categoryList" :key="item.value" :value="item.value" :label="item.label"
                  clearable></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="9">
            <el-form-item label="供应商" prop="supplier">
              <el-input v-model="formData.supplier" placeholder="请输入供应商" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="9">
            <el-form-item label="签订部门">
              <el-cascader v-model="formData.departmentId" placeholder="请选择签订部门" :options="deptList" :props="{
                  value: 'id',
                  label: 'deptName',
                  checkStrictly: true,
                  emitPath: false
                }" clearable filterable size="small" @change="selectDept"></el-cascader>
            </el-form-item>
          </el-col>
          <el-col :md="9">
            <el-form-item label="经办人">
              <el-input v-model="formData.handledBy" placeholder="请输入经办人" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="9">
            <el-form-item label="合同金额（元）" prop="amount">
              <el-input v-model="formData.amount" placeholder="请输入合同金额（元）" clearable
                @input="(value) => (formData.amount = value.replace(/[^\d.]/g, '').replace(/(\.\d{2}).+/, '$1'))"></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="9">
            <el-form-item label="保证金额（元）">
              <el-input v-model="formData.bond" placeholder="请输入保证金额（元）" clearable
                @input="(value) => (formData.bond = value.replace(/[^\d.]/g, '').replace(/(\.\d{2}).+/, '$1'))"></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="9">
            <el-form-item label="签订日期" prop="archiveDate">
              <el-date-picker v-model="formData.archiveDate" value-format="timestamp" placeholder="请选择签订日期"
                clearable></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="9">
            <el-form-item label="合同有效期">
              <el-date-picker v-model="formData.effectiveDate" value-format="timestamp" placeholder="请选择合同有效期"
                clearable></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="9">
            <el-form-item label="合同生效日期" prop="startDate">
              <el-date-picker v-model="formData.startDate" :pickerOptions="pickerOptions" value-format="timestamp"
                placeholder="请选择合同生效日期" clearable></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="9">
            <el-form-item label="合同结束日期">
              <el-date-picker v-model="formData.endDate" :pickerOptions="endPickerOptions" value-format="timestamp"
                placeholder="请选择合同生效日期" clearable></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="合同存放位置">
              <el-input v-model="formData.saveLocation" type="textarea" maxlength="200" show-word-limit
                placeholder="请输入存放位置"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input v-model="formData.remark" type="textarea" maxlength="200" show-word-limit
                placeholder="请输入备注"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="附件">
              <el-upload class="upload-demo" :action="''" :on-success="handleOnSuccess" :before-upload="beforeUpload"
                multiple :http-request="httpRequset" :show-file-list="false">
                <el-button size="small" type="secondary">
                  <em class="el-icon-upload2"></em>
                  点击上传
                </el-button>
                <div slot="tip" class="el-upload__tip">文件大小限制20M</div>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
        <div class="tableBox">
          <el-table :data="formData.archiveFileList" highlight-current-row>
            <el-table-column label="序号" type="index" width="50"> </el-table-column>
            <el-table-column property="fileName" label="文件名"> </el-table-column>
            <el-table-column property="fileSize" label="大小（M）"> </el-table-column>
            <el-table-column property="option" label="操作" width="120">
              <template slot-scope="scope">
                <el-button type="text" @click="handleDelete(scope)"> 删除 </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-form>
    </div>
  </div>
</template>
<script>
import FormTextItem from '@/views/operationPort/dossierManager/components/FormTextItem.vue'
import mixins from '@/views/operationPort/dossierManager/mixins/index.js'
import moment from 'moment'
import { transData } from '@/util'
import dictMixin from '../mixins/index.js'
export default {
  components: { FormTextItem },
  mixins: [mixins, dictMixin],
  props: {
    formData: {
      type: Object,
      default: () => ({})
    },
    tableData: {
      type: Array,
      default: () => []
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const that = this
    return {
      moment,
      propsOptions: {
        label: 'archiveName',
        value: 'archiveId'
      },
      rules: {
        archiveName: [
          {
            required: true,
            message: '请输入文档名称',
            trigger: ['change', 'blur']
          }
        ],
        archiveNumber: [{ required: true, message: '请输入文号', trigger: ['change', 'blur'] }],
        archiveModel: [
          {
            required: true,
            message: '请选择所属分类',
            trigger: ['change', 'blur']
          }
        ],
        category: [
          {
            required: true,
            message: '请选择合同类别',
            trigger: ['change', 'blur']
          }
        ],
        supplier: [
          {
            required: true,
            message: '请输入供应商',
            trigger: ['change', 'blur']
          }
        ],
        amount: [
          {
            required: true,
            message: '请输入合同金额',
            trigger: ['change', 'blur']
          }
        ],
        archiveDate: [
          {
            required: true,
            message: '请选择签订日期',
            trigger: ['change', 'blur']
          }
        ],
        startDate: [
          {
            required: true,
            message: '请选择合同生效日期',
            trigger: ['change', 'blur']
          }
        ]
      },
      pickerOptions: {
        disabledDate(date) {
          return that.formData.endDate && date.getTime() >= that.formData.endDate
        }
      },
      endPickerOptions: {
        disabledDate(date) {
          return that.formData.startDate && date.getTime() <= that.formData.startDate
        }
      },
      deptList: [],
      contractList: []
    }
  },
  computed: {
    archiveModel() {
      const item = this.contractClassification.find((item) => item.value === this.formData.archiveModel)
      return item ? item.label : ''
    },
    category() {
      const item = this.categoryList.find((item) => item.value === this.formData.category)
      return item ? item.label : ''
    }
  },
  created() {
    this.getDeptList()
    this.handleGetContractData()
  },
  methods: {
    handleGetContractData() {
      this.$api.fileManagement.queryAllContractPage({ current: 1, size: 99999999 }).then((res) => {
        this.contractList = res.data.records
      })
    },
    selectDept(e) {
      // eslint-disable-next-line vue/no-mutating-props
      this.formData.departmentName = this.findNameById(this.deptList, [e])
    },
    findNameById(tree, targetIds) {
      const names = []
      function search(node) {
        if (targetIds.includes(node.id)) {
          names.push(node.deptName)
        }
        if (node.children) {
          node.children.forEach((child) => search(child))
        }
      }
      tree.forEach((node) => search(node))
      return names.join(',')
    },
    // 获取部门列表
    getDeptList() {
      this.$api.getSelectedDept().then((res) => {
        if (res.code == '200') {
          this.deptList = transData(res.data, 'id', 'pid', 'children')
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-select,
::v-deep .el-date-editor.el-input {
  width: 100%;
}
::v-deep .upload-demo {
  display: flex;
  .el-upload__tip {
    margin-top: 0;
    margin-left: 8px;
    color: #8c8c8c;
  }
}
.tableBox {
  padding-left: 80px;
}
::v-deep .el-cascader {
  width: 100%;
}
</style>
