<!-- 运行监测 -->
<template>
  <ContentCard :title="item.componentTitleShow" :scrollbarHover="true" :cstyle="{ height: '100%' }" class="drag_class"
    :hasMoreOper="['edit']" @more-oper-event="(val) => $emit('all-more-Oper', val, 'operationMonitor')">
    <div slot="title-right" class="operation-list">
      <el-cascader style="margin-top:-5px;" ref="refHandle" :options="customGroupingList" :props="{
        checkStrictly: true,
        expandTrigger: 'click'
      }" clearable placeholder="自定义分组" :show-all-levels="false" v-model="handlerValue" @change="handleSelectChange">
      </el-cascader>
    </div>
  </ContentCard>
</template>
<script>
import { transData } from '@/util'
export default {
  name: 'assetsGroup',
  props: {
    item: {
      type: Object,
      default: () => { }
    },
    systemCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      customGroupingList: [],//自定义分组
      customDefaultProps: {
        label: 'groupName',
        isLeaf: 'leaf',
        children: 'children'
      },
      handlerValue: [],
      groupId: "",
    }
  },
  computed: {},
  watch: {
    handlerValue() {
      if (this.$refs.refHandle) {
        this.$refs.refHandle.dropDownVisible = false; //监听值发生变化就关闭它
      }
    },
  },
  created() {
    this.getAssetsGroup()//自定义分组树
  },
  methods: {
    handleSelectChange(val) {
      if (val == '123') {
        this.groupId = ''
      } else {
        this.groupId = val.length > 0 ? val[val.length - 1] : '';
      }
      this.$emit('select-changed', this.groupId);
    },
    // 自定义分组树
    getAssetsGroup() {
      this.customGroupingList = []
      let data = {
        dictionaryDetailsCode: this.systemCode,
        equipAttr: '2',
        groupName: "",
      }
      this.$api.getCustomGroupingTree(data).then((res) => {
        if (res.code == '200') {
          const newRoute = {
            children: null,
            createName: null,
            deleteFlag: 0,
            dictionaryDetailsCode: "PDXT",
            equipAttr: '2',
            groupName: '全部',
            label: '全部',
            id: "123",
            pid: "-1",
            value: "123",
          };
          res.data.unshift(newRoute);
          this.customGroupingList = transData(res.data, 'id', 'pid', 'children')
          if (this.customGroupingList.length > 0) {
            this.handlerValue = [this.customGroupingList[0].id]; // 默认选中第一条
          }
        }
      })
    },
  }
}
</script>
<style lang="scss" scoped>
.operation-list {
  height: 100%;
  display: flex;
  gap: 16px;
  flex-direction: row;
  margin-left: 50px;
}
</style>
