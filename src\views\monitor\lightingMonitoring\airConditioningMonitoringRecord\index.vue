<!-- 运行总览 -->
<template>
  <PageContainer>
    <div slot="header" class="control-btn-header">
      <el-select v-model="filters.operator" filterable clearable placeholder="操作人">
        <el-option v-for="item in operatorList" :key="item.value" :label="item.staffName" :value="item.staffName"></el-option>
      </el-select>
      <el-select v-model="filters.operatorType" placeholder="操作类型">
        <el-option v-for="item in operationTypeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
      </el-select>
      <el-input v-model.trim="filters.operatorObj" clearable placeholder="搜索设备对象名称"></el-input>
      <el-date-picker v-model="filters.date" type="date" value-format="yyyy-MM-dd" :picker-options="pickerOptions" :clearable="false" placeholder="选择日期"> </el-date-picker>
      <el-time-picker
        v-model="filters.timeRange"
        is-range
        value-format="HH:mm:ss"
        range-separator="-"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        style="margin-right: 20px;"
        :clearable="false"
        placeholder="选择时间范围"
      >
      </el-time-picker>
      <el-button type="primary" plain @click="resetData">重置</el-button>
      <el-button type="primary" @click="searchClick">查询</el-button>
    </div>
    <div slot="content" class="content">
      <div class="middle_tools">
        <div v-for="(vlaue, key) in timeObj" :key="key" class="hour-item">
          <div class="minuteBox">
            <div v-for="(v, k) in vlaue" :key="key + k" class="minute-item" :style="{ opacity: v.length ? 1 : 0 }">
              <div class="minute-hover">
                {{ (parseInt(key) >= 10 ? '' + key : '0' + key) + ':' + (parseInt(k) >= 10 ? '' + k : '0' + k) }}
                <br />
                {{ v.map((item) => item.operatorTypeText).join(',') }}
                <div class="triangle"></div>
              </div>
            </div>
          </div>
          <p class="label">{{ (parseInt(key) >= 10 ? '' + key : '0' + key) + ':00' }}</p>
        </div>
      </div>
      <div class="table-content">
        <TablePage
          ref="tablePage"
          v-loading="tableLoading"
          :showPage="true"
          :tableColumn="tableColumn"
          :data="tableData"
          row-key="id"
          border
          height="calc(100% - 40px)"
          :pageData="paginationData"
          @pagination="paginationChange"
        >
        </TablePage>
      </div>
    </div>
  </PageContainer>
</template>
<script lang="jsx">
import moment from 'moment'
moment.locale('zh-cn')
export default {
  name: 'airConditioningMonitoringRecord',
  components: {},
  data() {
    return {
      timeObj: {},
      operationTypeList: [
        {label: '全部', value: 'all'},
        {label: '人工控制', value: 5},
        {label: '定时控制', value: 6}
      ], // 操作类型列表
      operatorList: [], // 操作人列表
      pageProps: {
        page: 'currentPage',
        pageSize: 'pageSize',
        total: 'total'
      },
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      tableData: [],
      tableLoading: false,
      tableColumn: [
        {
          prop: 'serialNumber',
          label: '序号',
          width: 80,
          formatter: (scope) => {
            // 当前分页重置
            // return scope.$index + 1
            // 跨分页序号
            return (this.paginationData.currentPage - 1) * this.paginationData.pageSize + scope.$index + 1
          }
        },
        {
          prop: 'operatorDate',
          label: '操作时间',
          width: 150
        },
        {
          prop: 'timePoint',
          label: '时间点',
          width: 150
        },
        {
          prop: 'operator',
          label: '操作人',
          width: 150
        },
        {
          prop: 'operatorTypeText',
          label: '操作类型',
          width: 150
        },
        {
          prop: 'operatorObj',
          label: '对象'
        },
        {
          prop: 'details',
          label: '详情'
        }
      ],
      filters: {
        operator: '', // 操作人
        operatorType: 'all', // 操作类型
        operatorObj: '', // 详情关键字
        date: moment().format('YYYY-MM-DD'), // 日期
        timeRange: ['00:00:00', '23:59:59'] // 时间范围
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now() + 1000 * 60 * 60
        }
      }
    }
  },
  mounted() {
    this.initGetTime()
    this.getRoleStaffList()
  },
  created() {
    let self = this
    document.onkeydown = function (event) {
      let _key = event.keyCode
      if (_key == 13) {
        event.preventDefault()
        self.searchClick()
      }
    }
  },
  methods: {
    // 初始化获取时间格式
    initGetTime() {
      // let m = null
      for (let i = 0; i < 24; i++) {
        this.timeObj[i] = {}
        for (let j = 0; j < 60; j++) {
          // m = j < 10 ? '0' + j : '' + j
          this.timeObj[i][j] = []
        }
      }
      this.getTableData()
    },
    // 查询
    searchClick() {
      this.paginationData.currentPage = 1
      this.initGetTime()
    },
    // 获取用户列表
    getRoleStaffList() {
      let param = {
        page: 1,
        pageSize: 999,
        roleId: '', // 角色
        userName: '', // 人员姓名
        phone: '' // 手机号
      }
      this.$api.GetUserInfoList(param).then((res) => {
        if (res.code == 200) {
          this.operatorList = res.data.records
        } else if (res.message) {
          this.$message.error(res.message)
        }
      })
    },
    // 获取列表
    getTableData() {
      let { operator, operatorType, operatorObj, date, timeRange } = this.filters
      let params = {
        pageSize: this.paginationData.pageSize,
        page: this.paginationData.currentPage,
        operator,
        operatorType: operatorType == 'all' ? '' : operatorType,
        types: operatorType == 'all' ? [5, 6] : [],
        operatorObj,
        createTime: date + ' ' + timeRange[0],
        endTime: date + ' ' + timeRange[1]
      }
      this.tableLoading = true
      this.$api.GetOperationRecordList(params).then((res) => {
        this.tableLoading = this.$store.state.loadingShow
        if (res.code == 200) {
          res.data.list.forEach((item) => {
            item.operatorTypeText = this.operationTypeList.find((v) => item.operatorType == v.value).label
            this.timeObj[parseInt(item.timePoint.slice(0, 2))][parseInt(item.timePoint.slice(3, 5))].push(item)
          })
          this.tableData = res.data.list
          this.paginationData.total = parseInt(res.data.count)
        } else if (res.message) {
          this.$message.error(res.message)
        }
      })
    },
    resetData() {
      this.filters.iiPersonName = ''
      this.filters.operator = ''
      this.filters.operatorType = 'all'
      this.filters.operatorObj = ''
      this.filters.date = moment().format('YYYY-MM-DD')
      this.filters.timeRange = ['00:00:00', '23:59:59']
      this.paginationData.currentPage = 1
      this.initGetTime()
    },
    paginationChange(pagination) {
      Object.assign(this.paginationData, pagination)
      this.initGetTime()
    }
  }
}
</script>
<style lang="scss" scoped>
.control-btn-header {
  padding: 0 10px 10px !important;
  // padding: 0 200px 10px 10px !important;
  background: #fff;
  border-radius: 4px;

  & > div {
    margin-top: 12px;
    margin-right: 10px;
  }

  ::v-deep .el-input {
    width: 200px;
  }

  ::v-deep .el-range-editor.el-input__inner {
    padding: 0 10px;
  }

  & > div {
    margin-right: 10px;
    margin-top: 10px;
  }
}

.content {
  height: 100%;
  background: #fff;
  margin-top: 15px;
  padding: 10px;

  .middle_tools {
    width: 100%;
    height: 80px;
    padding: 10px;
    box-sizing: border-box;
    // margin-top: 20px;
    // margin-bottom: 20px;
    display: flex;

    .hour-item {
      text-align: center;
      width: calc(100% / 24);

      .label {
        font-size: 14px;
        color: #121f3e;
        font-family: "PingFang SC-Regular", "PingFang SC";
        // padding: 10px 0px 0px 0px;
      }
    }

    .minuteBox {
      width: 100%;
      display: flex;
      justify-content: center;
      cursor: pointer;
      padding: 5px 0 0;
      border-left: 1px solid #000;
      border-bottom: 1px solid #000;

      .minute-item {
        height: 10px;
        width: 1px;
        background: #000;
        position: relative;
      }

      .minute-hover {
        text-align: left;
        position: absolute;
        top: -65px;
        left: 50%;
        transform: translateX(-50%);
        display: none;
        background: #303133;
        color: #fff;
        border-radius: 4px;
        padding: 10px;
        z-index: 2000;
        font-size: 12px;
        line-height: 1.2;
        min-width: 10px;
        white-space: nowrap;
      }

      .triangle {
        width: 0;
        height: 0;
        border-left: 6px solid transparent;
        border-right: 6px solid transparent;
        border-bottom: 6px solid #303133;
        position: absolute;
        left: 50%;
        bottom: -6px;
        transform: translateX(-50%) rotate(180deg);
      }

      .minute-item:hover {
        .minute-hover {
          display: block;
        }
      }
    }

    .hour-item:last-child {
      .minuteBox {
        border-right: 1px solid #000;
      }
    }
  }

  .table-content {
    height: calc(100% - 100px);
  }
}
</style>
