<template>
  <PageContainer v-if="routeInfo.isFalg == 0">
    <div slot="header">
      <div class="search-header">
        <div class="search-from">
          <el-input v-model="formInline.name" placeholder="请输入任务名称" style="width: 200px"></el-input>
          <el-cascader v-model="formInline.deptId" placeholder="请选择所属部门" :options="deptList" :props="deptProps" clearable style="width: 280px"> </el-cascader>
          <el-select v-model="formInline.learnStatus" placeholder="请选择状态">
            <el-option v-for="item in statusList" :key="item.id" :label="item.label" :value="item.id"></el-option>
          </el-select>
          <el-input v-model="formInline.studentName" placeholder="请输入学员名称" style="width: 200px"></el-input>
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="search">查询</el-button>
        </div>
        <div>
          <el-button type="primary" @click="openTask('add')">创建学习任务</el-button>
          <el-button type="primary" :disabled="multipleSelection.length < 1" @click="exportFile">导出</el-button>
          <el-button type="primary" :disabled="multipleSelection.length < 1" @click="taskDelete('0')">批量删除</el-button>
        </div>
      </div>
    </div>
    <div slot="content" style="height: 100%; margin-top: 16px; background-color: #fff">
      <div class="table-content">
        <el-table
          v-loading="tableLoading"
          :data="tableData"
          tyle="width: 100%;"
          height="100%"
          border
          stripe
          title="双击查看详情"
          @row-dblclick="openDetails"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" fixed="left" align="center"></el-table-column>
          <el-table-column label="序号" width="55" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ (paginationData.pageNo - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="任务名称" width="120" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="deptName" label="所属部门" width="120" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="studentName" label="学习人员" width="120" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="createTime" label="完成时间" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ scope.row.startTime }}至{{ scope.row.endTime }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="courseName" label="课程内容" width="120" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="examName" label="考试内容" width="120" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="trainName" label="培训内容" width="120" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="任务状态" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <span v-if="scope.row.learnStatus == '0'" class="statusClass" style="color: #d25f00">草稿</span>
              <span v-if="scope.row.learnStatus == '1'" class="statusClass" style="color: #2749bf">学员执行中</span>
              <span v-if="scope.row.learnStatus == '2'" class="statusClass" style="color: #2749bf">已派发</span>
              <span v-if="scope.row.learnStatus == '3'" class="statusClass" style="color: #cb2634"><i class="el-icon-warning" style="margin-right: 3px"></i>超时未完成</span>
              <span v-if="scope.row.learnStatus == '4'" class="statusClass" style="color: #cb2634">超时部分完成</span>
              <span v-if="scope.row.learnStatus == '5'" class="statusClass" style="color: #009a29"><i class="el-icon-success" style="margin-right: 3px"></i>学员全部完成</span>
            </template>
          </el-table-column>
          <el-table-column prop="taskTeamName" label="操作" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <div class="bths">
                <span v-if="scope.row.learnStatus == '0'" @click="distribution(scope.row)">派发</span>
                <span v-if="scope.row.learnStatus == '0'" @click="openTask('edit', scope.row)">编辑</span>
                <span @click="openDetails(scope.row)">查看</span>
                <span @click="taskCopy(scope.row)">复制</span>
                <span v-if="scope.row.learnStatus != '1'" @click="taskDelete('1', scope.row)">删除</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="contentTable-footer">
          <el-pagination
            style="margin-top: 3px"
            :current-page="paginationData.pageNo"
            layout="total, sizes, prev, pager, next, jumper"
            :total="paginationData.total"
            :page-size="paginationData.pageSize"
            :page-sizes="[15, 30, 50, 100]"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
    </div>
  </PageContainer>
  <div v-else>
    <permissionPrompt></permissionPrompt>
  </div>
</template>
  
  <script>
import permissionPrompt from '@/views/safety/courseIndex/components/permissionPrompt.vue'
import axios from 'axios'
export default {
  components: { permissionPrompt },
  data() {
    return {
      formInline: {
        name: '',
        deptId: '',
        learnStatus: '',
        studentName: ''
      },
      statusList: [
        {
          id: '0',
          label: '草稿'
        },
        {
          id: '1',
          label: '学员执行中'
        },
        {
          id: '2',
          label: '任务已派发'
        },
        {
          id: '3',
          label: '超时未完成'
        },
        {
          id: '4',
          label: '超时部分完成'
        },
        {
          id: '5',
          label: '学员全部完成'
        }
      ],
      deptProps: {
        children: 'children',
        label: 'teamName',
        value: 'id',
        checkStrictly: true,
        emitPath: false
      },
      deptList: [], //部门
      tableData: [],
      tableLoading: false,
      paginationData: {
        pageNo: 1,
        pageSize: 15,
        total: 0
      },
      loginData: {},
      multipleSelection: [],
      routeInfo: {}
    }
  },
  computed: {},
  created() {
     this.routeInfo = JSON.parse(sessionStorage.getItem('routeInfo'))
    if (this.routeInfo.isFalg == 1) {
      return
    }
    this.getDeptList()
    this.getDataList() // 列表
  },
  methods: {
    // 组织列表
    getDeptList() {
      this.$api.getDeptListLaboratory({}).then((res) => {
        this.deptList = this.$tools.transData(res.data.list, 'id', 'parentId', 'children')
      })
    },
    // 查询列表
    getDataList() {
      this.tableLoading = true
      let data = {
        size: this.paginationData.pageSize,
        current: this.paginationData.pageNo,
        ...this.formInline
      }
      this.$api.taskListLaboratory(data).then((res) => {
        if (res.code == '200') {
          this.tableData = res.data.list
          this.paginationData.total = res.data.total
        }
        this.tableLoading = false
      })
    },
    // 派发
    distribution(row) {
      this.$confirm('是否确定派发？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.distribute({ id: row.id }).then((res) => {
          if (res.code == 200) {
            this.getDataList()
          } else {
            this.$message.error(res.msg)
          }
        })
      })
    },
    // 复制
    taskCopy(row) {
      this.$confirm('是否要复制该任务？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.copyTask({ id: row.id }).then((res) => {
          if (res.code == 200) {
            this.getDataList()
          } else {
            this.$message.error(res.msg)
          }
        })
      })
    },
    // 删除
    taskDelete(type, row) {
      this.$confirm('删除后将无法恢复，是否确定删除？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let ids = type == '0' ? this.multipleSelection.map((item) => item.id) : row.id.split(',')
        this.$api.deleteTaskLaboratory({ ids }).then((res) => {
          if (res.code == 200) {
            this.search()
          } else {
            this.$message.error(res.msg)
          }
        })
      })
    },
    // 打开创建学习任务页面
    openTask(type, row) {
      this.$router.push({
        name: 'addTask',
        query: {
          type,
          id: type == 'edit' ? row.id : ''
        }
      })
    },
    // 双击列表查看详情
    openDetails(row) {
      console.log('详情·1')
      this.$router.push({
        path: 'learnTaskDetails',
        query: {
          id: row.id
        }
      })
    },
    // 查询
    search() {
      this.paginationData.pageNo = 1
      this.getDataList()
    },
    // 重置
    resetForm() {
      this.formInline.name = ''
      this.formInline.deptId = ''
      this.formInline.learnStatus = ''
      this.formInline.studentName = ''
      this.paginationData.pageNo = 1
      this.getDataList()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 分页
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.getDataList()
    },
    handleCurrentChange(val) {
      this.paginationData.pageNo = val
      this.getDataList()
    },
    // 导出
    exportFile() {
      let httpname = 'learnTask/taskExport'
      let params = {
        recordIds: this.multipleSelection.map((item) => item.id),
        unitCode: this.routeInfo.unitCode,
        hospitalCode: this.routeInfo.hospitalCode
      }
      axios({
        method: 'post',
        url: __PATH.BASE_URL_LABORATORY + httpname,
        data: params,
        responseType: 'blob',
        headers: {
          'Content-Type': 'application/json',
          token: this.routeInfo.token || ''
        }
      })
        .then((res) => {
          let name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
          let blob = new Blob([res.data]) // { type: "application/vnd.ms-excel" }
          let url = window.URL.createObjectURL(blob) // 创建一个临时的url指向blob对象
          // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
          let a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
          // 释放这个临时的对象url
          window.URL.revokeObjectURL(url)
        })
        .catch((res) => {
          this.$message.error('导出失败！')
        })
    }
  }
}
</script>
  
  <style lang="scss" scoped>
.search-header {
  padding: 10px 16px;
  .search-from {
    padding-bottom: 12px;
    & > div {
      margin-top: 12px;
      margin-right: 10px;
    }
  }
}
.table-content {
  padding: 16px;
  height: calc(100% - 80px);
}
::v-deep .model-dialog .el-dialog__body {
  padding: 0%;
}
::v-deep .model-dialog .el-dialog__header {
  border: none;
}
.lookinform {
  color: #000000 !important;
}
.bths {
  > span {
    cursor: pointer;
    margin: 0px 5px;
  }
  > span:nth-child(1) {
    color: #558bf9;
    margin-right: 5px;
  }
  > span:nth-child(2) {
    color: #558bf9;
    margin-right: 5px;
  }
  > span:nth-child(3) {
    color: #558bf9;
    margin-right: 5px;
  }
  > span:nth-child(4) {
    color: #558bf9;
    margin-right: 5px;
  }
  > span:nth-child(5) {
    color: #606266;
  }
}
.contentTable-footer {
  padding: 10px 0 0 0;
}
.statusClass {
  font-size: 14px;
  background-color: #fff7e8;
  padding: 5px;
}
</style>
  