<template>
  <el-dialog
    v-dialogDrag
    custom-class="model-dialog"
    :modal="false"
    :close-on-click-modal="false"
    :title="handleType == 'edit' ? '编辑人员' : handleType == 'detail' ? '人员详情' : '新增人员'"
    :visible="visible"
    width="50%"
    :before-close="dialogClose"
  >
    <div class="dialog-content">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <div class="table-title">
          <span class="title"> <i></i>基本信息</span>
        </div>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="人员姓名" prop="name">
              <el-input v-if="handleType != 'detail'" v-model="form.name" placeholder="请输入人员姓名" maxlength="50"></el-input>
              <span v-else>{{ form.name }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="phone">
              <el-input v-if="handleType != 'detail'" v-model="form.phone" placeholder="请输入手机号" maxlength="11"></el-input>
              <span v-else>{{ form.phone }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="性别" prop="sex">
              <el-select v-if="handleType != 'detail'" v-model="form.sex" placeholder="请选择性别">
                <el-option label="男" value="1"></el-option>
                <el-option label="女" value="2"></el-option>
              </el-select>
              <span v-else>{{ form.sex == 1 ? '男' : '女' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="启用状态" prop="enableStatus">
              <el-radio-group v-if="handleType != 'detail'" v-model="form.enableStatus">
                <el-radio label="0">启用</el-radio>
                <el-radio label="1">禁用</el-radio>
              </el-radio-group>
              <span v-else>{{ form.enableStatus == '1' ? '禁用' : '启用' }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input v-if="handleType != 'detail'" v-model="form.remark" type="textarea" placeholder="请输入备注" show-word-limit maxlength="200"></el-input>
              <span v-else>{{ form.remark }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10" v-if="idCardImageInfo.isShow">
          <el-col :span="24">
            <el-form-item :label="idCardImageInfo.label" :required="idCardImageInfo.required">
              <el-upload
                v-if="handleType != 'detail'"
                action=""
                list-type="picture-card"
                :file-list="idCardImageList"
                :accept="uploadAcceptDict['picture'].type"
                :before-upload="beforeAvatarUpload"
                :http-request="(file) => httpRequset(file, 'idCardImage')"
                :on-remove="(file, fileList) => handleRemove(fileList, 'idCardImage')"
                :on-preview="handlePreview"
              >
                <i class="el-icon-plus"><br /></i>
                <div slot="tip" class="el-upload__tip">大小不超过20M</div>
              </el-upload>
              <div v-else>
                <img v-for="(item, index) in idCardImageList" :key="index" style="margin-right: 10px" :src="item.url" width="150" height="90" @click="showPic(item.url)" />
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10" v-if="certificationImageInfo.isShow">
          <el-col :span="24">
            <el-form-item :label="certificationImageInfo.label" :required="certificationImageInfo.required">
              <el-upload
                :disabled="handleType == 'detail'"
                action=""
                list-type="list"
                :file-list="certificationImageList"
                accept="*"
                :before-upload="beforeAvatarUpload"
                :http-request="(file) => httpRequset(file, 'certificationImage')"
                :on-remove="(file, fileList) => handleRemove(fileList, 'certificationImage')"
                :on-preview="handlePreview"
              >
                <div slot="tip" class="el-upload__tip">大小不超过20M</div>
                <el-button :disabled="handleType == 'detail'" size="small" type="primary">点击上传</el-button>
              </el-upload>
              <!-- <div v-else>
                <img v-for="(item, index) in certificationImageList" :key="index" style="margin-right: 10px" :src="item.url" width="150" height="90" @click="showPic(item.url)" />
              </div> -->
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10" v-if="PersonElseFileInfo.isShow">
          <el-col :span="24">
            <el-form-item :label="PersonElseFileInfo.label" :required="PersonElseFileInfo.required">
              <el-upload
                :disabled="handleType == 'detail'"
                action=""
                list-type="list"
                :file-list="elseFileList"
                accept="*"
                :before-upload="beforeAvatarUpload"
                :http-request="(file) => httpRequset(file, 'elseFile')"
                :on-remove="(file, fileList) => handleRemove(fileList, 'elseFile')"
                :on-preview="handlePreview"
              >
                <div slot="tip" class="el-upload__tip">大小不超过20M</div>
                <el-button :disabled="handleType == 'detail'" size="small" type="primary">点击上传</el-button>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="24" v-if="handleType != 'detail'"><el-button type="primary" plain icon="el-icon-plus" @click="addCertificate">添加证照</el-button></el-col>
          <el-col :span="24" v-if="certificateList.length">
            <div class="zzBox" v-for="(item, index) in certificateList" :key="index">
              <div class="zzBox-left">
                <el-form-item label="证照名称" style="margin-bottom: 0px" :required="filterIsWrited(item.fileCode)">
                  <el-select
                    v-model="item.fileCode"
                    placeholder="请选择活动区域"
                    v-if="licenseNameFilter(item)"
                    :disabled="handleType == 'detail'"
                    clearable
                    @change="changeKey($event, index)"
                  >
                    <el-option v-for="i in certificateOptions" :key="i.id" :label="i.fileName" :value="i.fileCode"></el-option>
                  </el-select>
                  <el-input v-else v-model="item.fileName" placeholder="" disabled></el-input>
                </el-form-item>
              </div>
              <div class="zzBox-right">
                <el-upload
                  :disabled="handleType == 'detail'"
                  action=""
                  list-type="list"
                  :file-list="item.url"
                  accept="*"
                  :before-upload="beforeAvatarUpload"
                  :http-request="(file) => httpCertificateRequset(file, index)"
                  :on-remove="(file, fileList) => handleCertificateRemove(fileList, index)"
                  :on-change="handleCertificateFileChange.bind(this, index)"
                  :on-preview="handlePreview"
                >
                  <div slot="tip" class="el-upload__tip">大小不超过20M</div>
                  <el-button :disabled="handleType == 'detail'" size="medium" type="primary">点击上传</el-button>
                </el-upload>
              </div>
              <div class="zzBox-del" @click="delCertificate(index)" v-if="handleType != 'detail'"><span class="el-icon-delete" style="color: #f53f3f"></span></div>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <Preview v-if="viewVisible" v-model="viewVisible" :list="viewFileList" />
    </div>
    <div slot="footer">
      <template v-if="handleType != 'detail'">
        <el-button @click="dialogClose">取 消</el-button>
        <el-button type="primary" @click="submit" :loading="buttonLoading">确 定</el-button>
      </template>
      <el-button v-else @click="dialogClose">关闭</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { uploadAcceptDict } from '@/util/dict.js'
import dayjs from 'dayjs'
export default {
  components: {
    Preview: () => import('../components/preview.vue')
  },
  model: {
    prop: 'visible',
    event: 'update:visible'
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    },
    companyName: {
      type: String,
      default: ''
    },
    handleType: {
      type: String,
      default: 'add'
    },
    activeCompany: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      uploadAcceptDict,
      buttonLoading: false,
      certificationImageList: [],
      idCardImageList: [],
      elseFileList: [],
      form: {
        id: this.id,
        companyId: '',
        companyName: '',
        idCard: '',
        name: '',
        phone: '',
        sex: '1',
        enableStatus: '0',
        idCardImage: '',
        remark: '',
        PersonElseFile: '',
        certificationImage: ''
      },
      rules: {
        name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
        phone: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
        sex: [{ required: true, message: '请选择性别', trigger: 'change' }]
      },
      idCardImageInfo: {
        label: '身份证件',
        isShow: true,
        required: false
      },
      PersonElseFileInfo: {
        label: '其他文件',
        isShow: true,
        required: false
      },
      certificationImageInfo: {
        label: '资质证书',
        isShow: true,
        required: false
      },
      viewVisible: false,
      viewFileList: [],
      certificateList: [], // 证照添加列表
      certificateOptions: [] // 证照列表信息
    }
  },
  mounted() {
    this.getCertificateList()
    Object.assign(this.form, this.activeCompany)
    if (this.id) {
      this.getDetail()
    }
  },
  methods: {
    // 过滤是否可编辑
    licenseNameFilter(item) {
      if (!item) return false
      let obj = this.certificateOptions.find((i) => i.fileCode === item.fileCode)
      if (obj && String(obj.enableStatus) === '1') return true
      if (!obj && !item.id) return true
      return false
    },
    // 过滤是否必填
    filterIsWrited(val) {
      if (!val) return false
      let obj = this.certificateOptions.find((item) => item.fileCode === val)
      if (obj && obj.writed == 1) return true
      return false
    },
    // 证照名称选择事件
    changeKey(val, index) {
      if (val) {
        let obj = this.certificateOptions.find((item) => item.fileCode === val)
        if (obj) {
          this.certificateList[index].writed = obj.writed
          this.certificateList[index].fileName = obj.fileName
        }
      } else {
        this.certificateList[index].writed = 0
        this.certificateList[index].fileName = ''
      }
    },
    // 添加证照
    addCertificate() {
      let obj = {
        fileCode: '',
        fileName: '',
        writed: 0,
        url: []
      }
      this.certificateList.push(obj)
    },
    // 删除证照
    delCertificate(index) {
      this.certificateList.splice(index, 1)
    },
    // 证照上传
    async httpCertificateRequset(file, index) {
      let res = await this.$api.constructionUpload(__PATH.SPACE_API, 'SecurityLedger/upload', file)
      if (res.code === '200') {
        this.$message({
          message: '上传成功',
          type: 'success'
        })
        const fileInfo = this.certificateList[index].url.find((it) => it.uid === file.file.uid)
        if (fileInfo) {
          fileInfo.uploadPath = res.data.fileKey
        }
      } else {
        this.$message.error(res.message)
      }
    },
    // 证照上传删除
    handleCertificateRemove(fileList, index) {
      this.certificateList[index].url = fileList
    },
    // 证照上传 失踪同步文件列表
    handleCertificateFileChange(idx, file) {
      if (file.status === 'fail') {
        // 失败的文件，500ms后删除，视觉效果会好一些
        window.clearTimeout(this.$timerId)
        this.$timerId = window.setTimeout(() => {
          let index = -1
          do {
            index = this.certificateList[idx].url.findIndex((it) => it.status === 'fail')
            if (index > -1) {
              this.certificateList[idx].url.splice(index, 1)
            }
          } while (index > -1)
          this.$timerId = -1
        }, 500)
      } else {
        const index = this.certificateList[idx].url.findIndex((it) => it.uid === file.uid)
        if (index > -1) {
          this.certificateList[idx].url.splice(index, 1, file)
        } else {
          this.certificateList[idx].url.push(file)
        }
      }
    },
    // 获取证照信息
    getCertificateList() {
      const params = {
        fileName: '',
        enableStatus: '',
        type: 2,
        page: 1,
        pageSize: 999999
      }
      this.$api
        .getConstructionCertificateList(params)
        .then((res) => {
          if (res.code === '200') {
            if (res.data.records && res.data.records.length) {
              let field = ['certificationImage', 'PersonElseFile', 'idCardImage']
              field.forEach((item) => {
                let findData = res.data.records.find((i) => i.fileCode === item)
                if (findData) {
                  this[`${item}Info`].label = findData.fileName
                  this[`${item}Info`].isShow = findData.enableStatus === 1 ? true : findData.enableStatus === 0 ? false : true
                  this[`${item}Info`].required = findData.writed === 1 ? true : findData.writed === 0 ? false : true
                }
              })
              this.certificateOptions = res.data.records.filter((item) => item.initialed == 2 && item.enableStatus == 1)
            }
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '查询失败'))
    },
    getDetail() {
      this.$api.detailConstrucitonUser({ id: this.id }).then((res) => {
        if (res.code == 200) {
          this.form = res.data
          let arr = ['idCardImage', 'certificationImage']
          arr.forEach((key) => {
            res.data[key] &&
              res.data[key].split(',').forEach((el) => {
                if (el) {
                  this[`${key}List`].push({
                    name: el.split('/')[el.split('/').length - 1],
                    url: this.$tools.imgUrlTranslation(el)
                  })
                }
              })
          })
          let fileArr = ['elseFile']
          fileArr.forEach((key) => {
            this[`${key}List`] = JSON.parse(res.data[key]) || []
          })
          if (res.data.fileAttachment) {
            let arr = JSON.parse(res.data.fileAttachment)
            arr.forEach((item) => {
              if (item.url) {
                item.url = JSON.parse(item.url).map((i) => {
                  return {
                    name: i.name,
                    url: this.$tools.imgUrlTranslation(i.url),
                    uploadPath: i.url
                  }
                })
              }
              item.id = Date.now()
            })
            this.certificateList = arr
          }
        }
      })
    },
    handlePreview(file) {
      window.open(file.uploadPath ? this.$tools.imgUrlTranslation(file.uploadPath) : file.url)
    },
    showPic(item) {
      this.viewVisible = true
      this.viewFileList = [item]
    },
    dialogClose() {
      this.buttonLoading = false
      this.$emit('update:visible')
    },
    beforeAvatarUpload(file) {
      const fileSize = file.size / 1024 / 1024 < 20
      if (!fileSize) {
        this.$message.error('上传图片大小不能超过 20MB!')
      }
      return fileSize
    },
    httpRequset(file, type) {
      this.$api.constructionUpload(__PATH.SPACE_API, 'SecurityLedger/upload', file).then((res) => {
        if (res.code == 200) {
          let obj = {
            attachment: res.data.name,
            name: res.data.name,
            createBy: this.$store.state.user.userInfo.user.staffName,
            createDate: dayjs().format('YYYY-MM-DD HH:mm:ss'),
            url: res.data.picUrl
          }
          this[`${type}List`].push(obj)
          if (type == 'idCardImage' || type == 'certificationImage') {
            this.form[type] = this[`${type}List`].map((v) => v.url).join(',')
          } else {
            this.form[type] = JSON.stringify(this[`${type}List`])
          }
        }
      })
    },
    handleRemove(fileList, key) {
      this[`${key}List`] = fileList
      if (key == 'idCardImage' || key == 'certificationImage') {
        this.form[key] = this[`${key}List`].map((v) => v.url).join(',') || ''
      } else {
        this.form[key] = JSON.stringify(this[`${key}List`]) || ''
      }
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          let fn = this.id ? 'updateConstrucitonUser' : 'addConstrucitonUser'
          let arr = []
          if (this.idCardImageInfo.required && this.idCardImageInfo.isShow && !this.idCardImageList.length) {
            return this.$message.error(`请上传${this.idCardImageInfo.label}`)
          }
          if (this.certificationImageInfo.required && this.certificationImageInfo.isShow && !this.certificationImageList.length) {
            return this.$message.error(`请上传${this.certificationImageInfo.label}`)
          }
          if (this.PersonElseFileInfo.required && this.PersonElseFileInfo.isShow && !this.elseFileList.length) {
            return this.$message.error(`请上传${this.PersonElseFileInfo.label}`)
          }
          if (this.certificateList.length) {
            let status = this.certificateList.filter((v) => v.writed == 1).every((item) => item.url.length)
            if (!status) {
              return this.$message.error('请完善证照信息')
            }
            this.certificateList.forEach((item) => {
              if (item.url.length) {
                arr.push({
                  fileCode: item.fileCode,
                  fileName: item.fileName,
                  url: JSON.stringify(
                    item.url.map((i) => {
                      return {
                        url: i.uploadPath,
                        name: i.name
                      }
                    })
                  )
                })
              }
            })
          }
          this.buttonLoading = true
          this.$api[fn]({ ...this.form, fileAttachment: arr.length ? JSON.stringify(arr) : '' })
            .then((res) => {
              if (res.code == 200) {
                this.$message.success('添加成功')
                this.$emit('success')
                this.dialogClose()
              } else {
                this.$message.error(res.msg)
              }
            })
            .catch(() => {})
            .finally(() => {
              this.buttonLoading = false
            })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 16px;
}
.el-form {
  .el-form-item {
    .el-form-item__content {
      .el-input,
      .el-select,
      .el-date-picker,
      .el-cascader {
        width: 100%;
      }
    }
  }
}
.zzBox {
  margin: 15px 0px;
  display: flex;
  .zzBox-left {
    width: 400px;
    flex-shrink: 0;
  }
  .zzBox-right {
    width: 350px;
    flex-shrink: 0;
    // display: flex;
    // align-items: center;
    margin-left: 20px;
  }
  .zzBox-del {
    margin-left: 20px;
    font-size: 26px;
    cursor: pointer;
  }
}
/* 隐藏提示 */
::v-deep .el-upload-list__item.is-success.focusing .el-icon-close-tip {
  display: none !important;
}
</style>
