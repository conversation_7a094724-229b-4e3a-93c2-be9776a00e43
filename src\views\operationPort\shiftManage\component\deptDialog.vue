<template>
  <el-dialog
    v-if="visible"
    ref="tablePage1"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    :title="title"
    width="40%"
    :visible.sync="visible"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <div class="dialog-content">
      <div class="content-left">
        <el-input v-model="treeFilter" placeholder="请输入关键字" clearable></el-input>
        <div v-loading="treeLoading" class="left-tree">
          <el-tree
            ref="tree"
            :data="deptTreeData"
            node-key="id"
            size="small"
            :default-expanded-keys="deptTreeData.map((v) => v.id)"
            :highlight-current="true"
            show-checkbox
            :props="treeProps"
            :filter-node-method="filterNode"
            @check="checkData"
          >
          </el-tree>
        </div>
      </div>
      <div class="content-right">
        <div class="right-title">
          <p class="title">
            已选：<span>{{ selectDept.length }}</span
            >个部门
          </p>
          <p class="clear" @click="clear">清空</p>
        </div>
        <div class="right-list">
          <div v-for="(item, index) in selectDept" :key="item.id" class="list-item">
            <span>{{ item.deptName }}</span>
            <i class="el-icon-close" @click="remove(item, index)"></i>
          </div>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="confirm('ruleForm')">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { transData } from '@/util'
export default {
  name: 'deptDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    checkedIds: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: '所属科室'
    },
    isRadio: {
      type: Boolean,
      default: false
    },
    nature: {
      type: String,
      default: '1'
    },
    isNotmultiSector: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      treeFilter: '',
      treeLoading: false,
      deptTreeData: [],
      treeProps: {
        label: 'deptName',
        children: 'children'
      },
      selectDept: [],
      lastTreeParentId: '',
      flatList: []
    }
  },
  watch: {
    treeFilter(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    this.getUnitListFn()
  },
  mounted() {
    // 在树加载完成后设置选中状态
    this.$nextTick(() => {
      this.setCheckedNodes()
    })
  },
  methods: {
    setCheckedNodes() {
      // 使用setCheckedKeys方法设置选中节点
      this.$refs.tree.setCheckedKeys(this.checkedIds.split(','))
    },
    // 移除
    remove(node, index) {
      this.$refs.tree.setChecked(node.id, false)
      this.selectDept.splice(index, 1)
    },
    // 清空
    clear() {
      this.selectDept = []
      this.$nextTick(() => {
        this.$refs.tree.setCheckedKeys([])
      })
    },
    filterNode(value, data) {
      if (!value) return true
      return data.deptName.indexOf(value) !== -1
    },
    checkData() {
      this.$nextTick(() => {
        this.selectDept = this.$refs.tree.getCheckedNodes()
      })
    },
    // 单位树
    async getUnitTree(unitList) {
      unitList.forEach((item) => {
        item.disabled = true
      })
      for (let i = 0; i < unitList.length; i++) {
        const v = unitList[i]
        let { data: deptList } = await this.$api.getDeptList({ unitId: v.umId })
        v.id = v.umId
        v.deptName = v.unitComName
        deptList.forEach((i) => {
          i.disabled = false
        })
        v.children = transData(deptList, 'id', 'pid', 'children')
        // this.setDisable(v.children)
      }
      this.deptTreeData = unitList
      this.treeLoading = false
      this.checkData()
    },
    setDisable(data) {
      data.forEach((v) => {
        if (v.children && v.children.length) {
          v.disabled = true
          this.setDisable(v.children) // 子级循环时把这一层数据的count传入
        }
      })
    },
    // 获取单位列表
    getUnitListFn() {
      this.treeLoading = true
      this.$api.getSelected({}).then((res) => {
        if (res.code == 200) {
          this.targetIds = res.data
          if (this.nature === '1') {
            this.getUnitTree(res.data.filter((item) => item.nature == 1))
          } else {
            // 查所有
            this.getUnitTree(res.data)
          }
        }
      })
    },
    confirm() {
      if (!this.selectDept.length) {
        this.$message({ message: '请选择打卡部门', type: 'error' })
        return
      }
      this.$emit('selectDept', this.selectDept)
      this.closeDialog()
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', !this.visible)
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .model-dialog {
  // min-height: 60vh;
}
.dialog-content {
  width: 100%;
  background: #fff;
  padding: 24px 0px;
  display: flex;
  p {
    margin: 0;
  }
  .content-left {
    height: 480px;
    flex: 1;
    padding: 0px 24px;
    border-right: 1px solid #e4e7ed;
    display: flex;
    flex-direction: column;
    .left-tree {
      flex: 1;
      margin-top: 16px;
      overflow: auto;
    }
    ::v-deep .el-tree-node {
      .is-leaf + .el-checkbox .el-checkbox__inner {
        display: inline-block;
      }
      .is-disabled {
        .el-checkbox__inner {
          display: none !important;
        }
      }
    }
  }
  .content-right {
    height: 480px;
    flex: 1;
    padding: 0px 24px;
    display: flex;
    flex-direction: column;
    .right-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 10px;
      .title {
        font-size: 14px;
        color: #7f848c;
        line-height: 22px;
        span {
          color: #333333;
          margin: 0 8px;
        }
      }
      .clear {
        cursor: pointer;
        font-size: 12px;
        color: #3562db;
      }
    }
    .right-list {
      flex: 1;
      overflow: auto;
      .list-item {
        transition: all 0.3s;
        padding: 8px 12px;
        border-radius: 4px;
        color: #333333;
        margin-top: 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        i {
          cursor: pointer;
          color: #666666;
          font-size: 16px;
        }
      }
      .list-item:hover {
        background: #e6effc;
      }
    }
  }
}
</style>
