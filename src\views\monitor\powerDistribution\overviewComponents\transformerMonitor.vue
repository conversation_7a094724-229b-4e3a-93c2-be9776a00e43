<!-- 变压器监测 -->
<template>
  <ContentCard
    :title="item.componentTitle"
    :scrollbarHover="true"
    :cstyle="{ height: '100%' }"
    class="drag_class"
    :hasMoreOper="['more', 'edit']"
    @more-oper-event="(val) => $emit('all-more-Oper', val, 'transformerMonitor')"
  >
    <div slot="title-right" class="data-btns">
      <el-dropdown trigger="click" @command="monitorEntitySelect">
        <span class="el-dropdown-link">{{ selectMonitorEntity.surveyEntityName }}<i class="el-icon-arrow-down el-icon--right"></i></span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item v-for="item in monitorEntityList" :key="item.surveyEntityCode" :command="item.surveyEntityCode">{{ item.surveyEntityName }}</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <div slot="content" class="transformer-main">
      <div class="main-left">
        <echarts ref="transformerChart" domId="transformerChart" />
      </div>
      <div v-if="selectMonitorEntity.parameterList.length" class="main-right">
        <div v-for="item in selectMonitorEntity.parameterList" :key="item.parameterName" class="right-item">
          <p class="item-name">{{ item.parameterName }}</p>
          <p class="item-value">{{ (item.parameterValue || '-') + (item.parameterUnit || '') }}</p>
        </div>
      </div>
      <div v-else style="width: 100%; margin-left: calc(30% + 10px);">
        <el-empty></el-empty>
      </div>
    </div>
  </ContentCard>
</template>

<script>
export default {
  name: 'transformerMonitor',
  props: {
    item: {
      type: Object,
      default: () => {}
    },
    requestInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      monitorEntityList: [],
      selectMonitorEntity: {
        parameterList: []
      }
    }
  },
  computed: {},
  watch: {
    '$store.state.settings.sidebarCollapse': {
      handler(val) {
        this.$nextTick(() => {
          this.echartsResize()
        })
      },
      deep: true
    },
    // dragSidebarCollapse
    '$store.state.settings.dragSidebarCollapse': {
      handler(val) {
        this.$nextTick(() => {
          this.echartsResize()
        })
      },
      deep: true
    },
    requestInfo: {
      handler(val, oldVal) {
        if (val.entityMenuCode != oldVal.entityMenuCode) {
          this.getDataList()
        }
      },
      deep: true
    }
  },
  mounted() {
    this.getDataList()
  },
  methods: {
    // 监测实体选择
    monitorEntitySelect(code) {
      if (!code) {
        this.selectMonitorEntity = { parameterList: [], schedule: 0 }
        this.$refs.transformerChart.init(this.transformerChartData(0))
        this.echartsResize()
        return
      }
      this.selectMonitorEntity = this.monitorEntityList.find(item => item.surveyEntityCode == code) || {parameterList: []}
      this.$nextTick(() => {
        this.$refs.transformerChart.init(this.transformerChartData(this.selectMonitorEntity.parameterList.find(item => item.parameterName.indexOf('负载') != -1)?.parameterValue ?? 0))
        this.echartsResize()
      })
    },
    getDataList() {
      let params = {
        ...this.requestInfo,
        page: 1,
        pageSize: 99
      }
      this.$api.GetRealMonitoringListSecurity(params).then((res) => {
        if (res.code == 200) {
          this.monitorEntityList = res.data.list
          this.monitorEntitySelect(res.data.list[0]?.surveyEntityCode ?? '')
        }
      })
    },
    echartsResize() {
      setTimeout(() => {
        if (this.$refs.transformerChart) {
          this.$refs.transformerChart.chartResize()
        }
      }, 200)
    },
    transformerChartData(value) {
      let option
      option = {
        backgroundColor: '#FAF9FC',
        series: [
          {
            type: 'gauge',
            startAngle: 225,
            endAngle: -45,
            min: 0,
            max: 100,
            radius: '99%',
            axisLine: {
              show: true,
              lineStyle: {
                color: '#999'
              }
            },
            title: {
              show: true,
              offsetCenter: [0, '105%'],
              textStyle: {
                color: '#414653',
                fontSize: 14
              }
            },
            detail: {
              show: true,
              offsetCenter: [0, '70%'],
              formatter: function (value) {
                return value + '%'
              },
              fontSize: 18,
              fontWeight: 600
            },
            axisTick: {
              show: false
            },
            splitLine: {
              length: 5,
              lineStyle: {
                width: 1,
                color: '#B9B8BB'
              }
            },
            axisLabel: {
              color: '#B9B8BB',
              fontSize: 10
            },
            pointer: {
              show: true,
              width: 3,
              length: '50%'
            },
            itemStyle: {
              color: '#3562DB'
            },
            markPoint: {
              animation: false,
              silent: true,
              data: [
                {
                  x: '50%',
                  y: '50%',
                  symbol: 'circle',
                  symbolSize: 12,
                  itemStyle: {
                    color: '#3562DB'
                  }
                },
                {
                  x: '50%',
                  y: '50%',
                  symbol: 'circle',
                  symbolSize: 3,
                  itemStyle: {
                    color: '#fff'
                  }
                }
              ]
            },
            data: [
              {
                name: '负载率',
                value: value
              }
            ]
          },
          {
            type: 'gauge',
            radius: '86%',
            startAngle: 225,
            endAngle: -45,
            min: 0,
            max: 100,
            detail: {
              show: false
            },
            axisLine: {
              show: true,
              lineStyle: {
                width: 5,
                color: [
                  [value / 100, '#3562DB'],
                  [1, 'RGBA(53, 98, 219, .1)']
                ]
              }
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false
            },
            axisLabel: {
              show: false
            },
            pointer: {
              show: false
            }
          }
        ]
      }
      return option
    }
  }
}
</script>

<style lang="scss" scoped>
.data-btns {
  position: absolute;
  right: 36px;

  :deep(.el-dropdown) {
    padding: 0 12px;
    background-color: #f6f5fa;
    border-radius: 4px;

    .el-dropdown-link {
      cursor: pointer;
      color: #7f848c;
    }
  }
}

.transformer-main {
  display: flex;
  height: 100%;
  width: 100%;

  p {
    margin: 0;
  }

  .main-left {
    width: calc(30% - 10px);
    height: calc(100% - 55px);
    position: absolute;
    bottom: 10px;
    left: 10px;
  }

  .main-right {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
    margin-left: calc(30% + 10px);

    .right-item {
      padding: 16px;
      width: calc(100% / 3);

      .item-name {
        font-size: 14px;
        font-weight: 400;
        color: #7f848c;
        line-height: 14px;
      }

      .item-value {
        margin-top: 10px;
        font-size: 14px;
        font-weight: 400;
        color: #121f3e;
        line-height: 14px;
      }
    }
  }
}
</style>
