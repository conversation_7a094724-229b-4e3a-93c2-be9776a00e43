<template>
  <div>
    <el-dialog
      v-loading="loading"
      class="sino_dialog"
      element-loading-text="文件导入中"
      title="文件导入"
      :visible.sync="importDialog"
      :before-close="cancel"
      custom-class="model-dialog"
    >
      <!-- <el-button
        @click="downLoad"
        class="sino-button-sure"
        style="margin-bottom: 15px"
        size="small"
        type="primary"
        >模板下载</el-button
      > -->
      <div class="upload-file dialog_from" style="background-color: #fff; width: 100%; padding-top: 10px;">
        <el-upload
          ref="uploadFile"
          action
          drag
          class="mterial_file"
          :limit="1"
          multiple
          :http-request="httpRequest"
          :beforeUpload="beforeAvatarUpload"
          :file-list="fileList"
          :on-exceed="handleExceed"
          :on-remove="handleRemove"
          accept=".Excel, .xlsx, .xls"
          list-type="text"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">
            将文件拖到此处，或
            <em>点击上传</em>
          </div>
          <div slot="tip" class="el-upload__tip">
            <p v-if="addFileName != 'netWork'" class="tip_1">提示：请按照下载模板导入</p>
            <p class="tip_2">可上传单个文件，小于50M，支持.Excel类型</p>
          </div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="sino-button-search" type="primary" plain @click="cancel">取 消</el-button>
        <el-button class="sino-button-sure-search" type="primary" @click="submit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import store from '@/store/index'
import axios from 'axios'
export default {
  props: {
    importDialog: {
      type: Boolean
    },
    addFileName: {
      type: String
    },
    param: {
      type: String
    }
  },
  data() {
    return {
      fileList: [],
      loading: false,
      currentTime: ''
    }
  },
  created: function () {
    let aData = new Date()
    // console.log(aData) //Wed Aug 21 2019 10:00:58 GMT+0800 (中国标准时间)

    this.currentTime = aData.getFullYear() + '-' + (aData.getMonth() + 1) + '-' + aData.getDate()
    // console.log(this.value) //2019-8-20
  },
  methods: {
    // 模板下载
    /* downLoad() {
      let baseInfo = JSON.parse(sessionStorage.getItem("USERMESSAGE"));
      let inType = "";
      let url = "";
      switch (this.addFileName) {
        case "staff": //人员管理
          url = "exportGridModel";
          inType = 1;
          break;
        case "beaconManagement": //Beacon管理
          url = "exportBeaComModel";
          inType = 1;
          break;
        case "locationMangement": //定位设备管理
          url = "nodeExportBeaComModel";
          inType = 2;
          break;
      }

      let data = {
        isUnitcode: baseInfo.isUnitcode,
        isHospitalcode: baseInfo.isHospitalcode,
        inType: inType,
      };
      this.$http[url](data).then(res=>{
        // 文件导出方法
        this.$tools.downloadFile(res, this);
      }).catch((res) => {
        console.log(res)
        this.$notify.error(res.message || '导出失败')
      });
    }, */
    // 限制文件大小
    beforeAvatarUpload(file) {
      const isLt50M = file.size / 1024 / 1024 < 50
      if (!isLt50M) {
        this.$message.error('上传图片大小不能超过 50MB!')
        return false
      }
      if (file.name.indexOf(',') != -1) {
        this.$message.error('非法的文件名')
        return false
      }
    },
    /**
     * 删除文件
     */
    handleRemove(file, fileList) {
      this.fileList = []
    },
    /**
     * 文件上传成功
     */
    handlePreview(file) {},
    httpRequest(item) {
      this.fileList.push(item.file)
    },
    handleExceed(files, fileList) {
      this.$message.warning('最多选择一个文件')
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`)
    },
    // 表单内容重置
    cancel() {
      this.fileList = []
      this.$emit('cancelFile')
    },
    /**
     * 导入文件
     */
    submit() {
      if (!this.fileList[0]) {
        return this.$message.error('请选择要上传的文件')
      }
      this.loading = true
      let url = ''
      let data = {
        file: this.fileList[0]
      }
      switch (this.addFileName) {
        case 'staff': // 人员管理
          url = 'hospitalStaff/hospital-staff/importHospitalStaffList'
          break
        case 'space': // 空间管理
          url = 'space/spaceInfo/importSpaceInfoByHandwork'
          break
        case 'company': // 单位管理
          url = 'importCompany'
          break
        case 'depet': // 部门管理
          url = 'importDepet'
          break
        case 'spacePlan': // 部门管理
          url = 'space/model/importSpaceModelInfo'
          break
        case 'dictype': // 字典管理
          url = 'importDicList'
          data.dictType = this.param
          break
        case 'netWork': // 管网信息
          url = 'importNetWorkList'
          data.modelType = this.param
          break
      }
      const userInfo = store.state.user.userInfo.user
      let formdata = new FormData()
      formdata.append('hospitalCode', userInfo.hospitalCode ?? 'BJSJTYY')
      formdata.append('unitCode', userInfo.hospitalCode ?? 'BJSYGJ')
      formdata.append('file', data.file)
      axios({
        method: 'post',
        url: __PATH.VUE_SPACE_API + url,
        data: formdata,
        headers: {
          'Content-Type': 'application/json;charset=utf-8',
          Authorization: 'Bearer ' + store.state.user.token,
          hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
          unitCode: userInfo.unitCode ?? 'BJSYGJ'
        }
      }).then((res) => {
        if (res.data.code == 200) {
          this.$message.success(res.data.msg)
          this.$emit('getTableData')
        } else {
          this.$message.error(res.data.msg)
        }
        this.loading = false
        this.cancel()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.upload-file {
  text-align: center;
  display: flex;
  justify-content: center;

  .el-upload__tip {
    p {
      line-height: 1.5rem;
    }

    .tip_1 {
      color: #5188fc;
    }
  }
}

.mterial_file > .el-upload-list {
  margin-top: 40px !important;
}
</style>
