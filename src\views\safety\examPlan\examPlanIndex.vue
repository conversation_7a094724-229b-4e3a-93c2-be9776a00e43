<template>
  <PageContainer v-if="routeInfo.isFalg == 0">
    <div slot="header">
      <div class="search-header">
        <div class="search-from">
          <el-input v-model="formInline.name" placeholder="请输入计划名称" style="width: 200px"></el-input>
          <el-cascader v-model="formInline.deptId" placeholder="请选择所属部门" :options="deptList" :props="deptProps" clearable style="width: 280px"> </el-cascader>
          <el-cascader
            v-model="formInline.subjectId"
            clearable
            class="sino_sdcp_input mr15"
            style="width: 280px"
            :options="subjectList"
            :props="subjectProps"
            placeholder="请选择所属科目"
          ></el-cascader>
          <el-select v-model="formInline.examStatus" placeholder="请选择试卷状态">
            <el-option v-for="item in examStatusList" :key="item.id" :label="item.label" :value="item.id"></el-option>
          </el-select>
          <el-date-picker v-model="timeLine" type="daterange" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
          </el-date-picker>
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="search">查询</el-button>
        </div>
        <div>
          <el-button type="primary" @click="addExam('0')">自动组卷</el-button>
          <el-button type="primary" @click="addExam('1')">手动组卷</el-button>
          <el-button type="primary" :disabled="multipleSelection.length == 0" @click="deletExam('all')">批量删除</el-button>
        </div>
      </div>
    </div>
    <div slot="content" style="height: 100%; margin-top: 16px; background-color: #fff">
      <div class="table-content">
        <el-table
          v-loading="tableLoading"
          :data="tableData"
          tyle="width: 100%;"
          height="100%"
          border
          stripe
          title="双击查看详情"
          @row-dblclick="openDetails"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" fixed="left" align="center"></el-table-column>
          <el-table-column label="序号" width="55" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ (paginationData.pageNo - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="试卷计划名称" width="120" align="center" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="deptName" label="所属部门" width="120" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="subjectName" label="所属科目" width="120" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="createTime" label="考试期限" width="200" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ moment(scope.row.startTime).format('YYYY-MM-DD') }}至{{ moment(scope.row.endTime).format('YYYY-MM-DD') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="时长" width="120" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ scope.row.duration || 0 }}分钟</span>
            </template>
          </el-table-column>
          <el-table-column prop="studentNum" label="考试学员" width="100" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ scope.row.studentNum || 0 }}人</span>
            </template>
          </el-table-column>
          <el-table-column prop="count" label="考题数量" width="80" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ scope.row.count || 0 }}题</span>
            </template>
          </el-table-column>
          <el-table-column prop="score" label="总分数" width="100" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ scope.row.score || 0 }}分</span>
            </template>
          </el-table-column>
          <el-table-column prop="passScore" label="通过分数" width="100" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ scope.row.passScore || 0 }}分</span>
            </template>
          </el-table-column>
          <el-table-column label="计划状态" width="140" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <div class="statusBtn">
                <!-- <span v-if="scope.row.state == '3'" class="inProgress"
                  >学员执行中</span
                > -->
                <span v-if="scope.row.examStatus == '0'" class="relwaseNo">未发布</span>
                <span v-if="scope.row.examStatus == '1'" class="auditIng">审核中</span>
                <span v-if="scope.row.examStatus == '2'" class="relwase">已发布</span>
                <span v-if="scope.row.examStatus == '3'" class="auditNo">
                  <img src="../../../assets/images/icon-wrapper.png" alt="" />
                  未通过
                </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="taskTeamName" label="操作" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <div class="operateBths">
                <el-link type="info" :disabled="scope.row.examStatus == '1'" @click="editPlan(scope.row)"> 编辑 </el-link>
                <el-link type="primary" @click="copyExam(scope.row)">复制</el-link>
                <el-link type="primary" @click="openDetails(scope.row)">查看</el-link>
                <el-link type="primary" :disabled="scope.row.examStatus == '1'" @click="deletExam('one', scope.row)">删除</el-link>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="contentTable-footer">
          <el-pagination
            style="margin-top: 3px"
            :current-page="paginationData.pageNo"
            layout="total, sizes, prev, pager, next, jumper"
            :total="paginationData.total"
            :page-size="paginationData.pageSize"
            :page-sizes="[15, 30, 50, 100]"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
    </div>
  </PageContainer>
  <div v-else>
  <permissionPrompt></permissionPrompt>
  </div>
</template>

<script>
import axios from 'axios'
import moment from 'moment'
import { servicesLoading } from '@/assets/js/temp.js'
import permissionPrompt from "@/views/safety/courseIndex/components/permissionPrompt.vue";
export default {
  components: { servicesLoading,permissionPrompt },
  data() {
    return {
      dialogVisible: true,
      moment,
      formInline: {
        name: '',
        deptId: '',
        subjectId: '',
        examStatus: '',
        startTime: '',
        endTime: ''
      },
      subjectProps: {
        children: 'childList',
        label: 'name',
        value: 'id',
        checkStrictly: true,
        emitPath: false
      },
      deptProps: {
        children: 'children',
        label: 'teamName',
        value: 'id',
        checkStrictly: true,
        emitPath: false
      },
      deptList: [], //部门
      subjectList: [], //科目分类
      examStatusList: [
        {
          id: '0',
          label: '草稿'
        },
        {
          id: '1',
          label: '审核中'
        },
        {
          id: '3',
          label: '未通过'
        },
        // {
        //   id:'3',
        //   label:'未发布'
        // },
        {
          id: '2',
          label: '已发布'
        }
      ],
      timeLine: [],
      hiddenLevelList: [], // 获取筛选类型数据
      tableData: [],
      tableLoading: false,
      paginationData: {
        pageNo: 1,
        pageSize: 15,
        total: 0
      },
      routeInfo: {},
      multipleSelection: []
    }
  },
  watch: {
    timeLine(val) {
      if (val.length > 0) {
        this.formInline.startTime = val[0]
        this.formInline.endTime = val[1]
      } else {
        this.formInline.startTime = ''
        this.formInline.endTime = ''
      }
    }
  },
  created() {
    // if (this.$route.query) {
    //   sessionStorage.setItem("routeInfo", JSON.stringify(this.$route.query));
    this.routeInfo = JSON.parse(sessionStorage.getItem('routeInfo'))
    if (this.routeInfo.isFalg == 1) {
      return
    }
    this.init()
    this.getDataList() // 列表
  },
  methods: {
    init() {
      // 获取科目分类
      let data = {
        pageNo: 1,
        pageSize: 999
      }
      this.$api.ipsmFicationlList(data).then((res) => {
        if (res.code == 200) {
          this.subjectList = res.data.records
        } else {
          this.$message.error(res.message)
        }
      })
      // 获取组织
      this.$api.getDeptListLaboratory({}).then((res) => {
        this.deptList = this.$tools.transData(res.data.list, 'id', 'parentId', 'children')
      })
    },
    // 查询列表
    getDataList() {
      this.tableLoading = true
      let params = {
        ...this.formInline,
        current: this.paginationData.pageNo,
        size: this.paginationData.pageSize
      }
      this.$api.getExamPlanList(params).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.list
          this.paginationData.total = res.data.total
        } else {
          this.$message.error(res.msg)
        }
        this.tableLoading = false
      })
    },
    // 删除
    deletExam(type, row) {
      if (type == 'all') {
        let showTabel = this.multipleSelection.some((item) => {
          const today = new Date()
          const startDate = new Date(item.startTime)
          const endDate = new Date(item.endTime)
          return today >= startDate && today <= endDate && item.examStatus == '2'
        })
        if (showTabel) {
          this.$message('选择的数据存在考试期内的计划，不支持删除')
          return
        }
      } else {
        const today = new Date()
        const startDate = new Date(row.startTime)
        const endDate = new Date(row.endTime)
        if (today >= startDate && today <= endDate && row.examStatus == '2') {
          this.$message('考试期内的计划不支持删除')
          return
        }
      }
      this.$confirm('删除后将无法恢复，是否确定删除？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let ids = type == 'all' ? this.multipleSelection.map((item) => item.id) : row.id.split(',')
        this.$api.deleteExamPlan({ ids }).then((res) => {
          if (res.code == '200') {
            this.getDataList()
          } else {
            this.$message.error(res.msg)
          }
        })
      })
    },
    // 双击列表查看详情
    openDetails(val) {
      this.$router.push({
        path: 'examDetails',
        query: {
          id: val.id
        }
      })
    },
    // 编辑
    editPlan(row) {
      const today = new Date()
      const startDate = new Date(row.startTime)
      const endDate = new Date(row.endTime)
      if (today >= startDate && today <= endDate && row.examStatus == '2') {
        this.$message('考试期内的计划不支持编辑')
      } else {
        this.$router.push({
          path: 'addExam',
          query: {
            id: row.id,
            type: row.type //0:自动组卷1:手动组卷
          }
        })
      }
    },
    // 查询
    search() {
      this.paginationData.pageNo = 1
      this.getDataList()
    },
    // 重置
    resetForm() {
      this.formInline.name = ''
      this.formInline.deptId = ''
      this.formInline.subjectId = ''
      this.formInline.startTime = ''
      this.formInline.endTime = ''
      this.formInline.examStatus = ''
      this.timeLine = []
      this.paginationData.pageNo = 1
      this.getDataList()
    },
    // 自动组卷/手动组卷
    addExam(type) {
      this.$router.push({
        path: 'addExam',
        query: {
          type //0:自动组卷1:手动组卷
        }
      })
    },
    // 复制
    copyExam(row) {
      this.$api.copyExam({ id: row.id }).then((res) => {
        if (res.code == '200') {
          this.$message.success('复制成功')
          this.getDataList()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 分页
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.getDataList()
    },
    handleCurrentChange(val) {
      this.paginationData.pageNo = val
      this.getDataList()
    }
  }
}
</script>

<style lang="scss" scoped>
.search-header {
  padding: 10px 16px;

  .search-from {
    padding-bottom: 12px;

    & > div {
      margin-top: 12px;
      margin-right: 10px;
    }
  }
}

.table-content {
  padding: 16px;
  height: calc(100% - 80px);

  .statusBtn {
    font-size: 14px;
    display: flex;
    justify-content: center;

    .auditIng {
      width: 58px;
      height: 24px;
      background-color: #fff7e8;
      border-radius: 4px;
      color: #d25f00;
    }

    .auditNo {
      width: 78px;
      height: 24px;
      background-color: #ffece8;
      border-radius: 4px;
      color: #cb2634;

      img {
        vertical-align: middle;
      }
    }

    .relwase {
      width: 58px;
      height: 24px;
      background-color: #e8ffea;
      border-radius: 4px;
      color: #009a29;
    }

    .inProgress {
      width: 86px;
      height: 24px;
      background-color: #e6effc;
      border-radius: 4px;
      color: #2749bf;
    }

    .relwaseNo {
      width: 58px;
      height: 24px;
      background-color: #f2f4f9;
      border-radius: 4px;
      color: #86909c;
    }
  }

  .operateBths {
    color: #3562db;

    .el-link {
      margin-right: 8px;
    }
  }
}

.contentTable-footer {
  padding: 10px 0 0 0;
}
</style>
