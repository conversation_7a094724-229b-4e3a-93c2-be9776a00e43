<template>
  <PageContainer>
    <div slot="header">
      <div class="searchForm">
        <div class="search-box">
          <el-input v-model="filterInfo.groupName" placeholder="请输入群组名称"></el-input>
          <div class="ml-16">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button type="primary" plain @click="reset">重置</el-button>
          </div>
        </div>
      </div>
    </div>
    <div slot="content" ref="contentRef" class="table-box">
      <div class="tableContainer">
        <el-button type="primary" style="margin-bottom: 12px" @click="handleListEvent('add')">新增</el-button>
        <el-button type="primary" :disabled="multipleSelection.length < 1" style="margin-bottom: 12px"
          @click="handleListEvent('delete')">删除</el-button>
        <el-table v-loading="tableLoading" height="calc(100% - 96px)" :data="tableData" border stripe
          @selection-change="selectionChange">
          <el-table-column type="selection" width="55"> </el-table-column>
          <el-table-column prop="groupHostextension" label="频道号" show-overflow-tooltip></el-table-column>
          <el-table-column prop="groupName" label="群组名称" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="groupType" label="群组类型" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ scope.row.groupType ===0?'平时组':'战时组'}}</span>
            </template>
          </el-table-column>
          <el-table-column prop="memberNums" label="组成员数量" show-overflow-tooltip></el-table-column>
          <el-table-column prop="groupDesc" label="备注" show-overflow-tooltip></el-table-column>
          <el-table-column label="操作" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-button type="text" @click="handleOperation('detail', scope.row)">详情</el-button>
              <el-button type="text" @click="handleOperation('edit',scope.row)">编辑</el-button>
              <el-button type="text" @click="handleOperation('del',scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination :current-page="pagination.page" :page-sizes="[15, 30, 50, 100]" :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper" :total="pageTotal" @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
        </el-pagination>
      </div>
      <el-dialog v-dialogDrag :visible.sync="dialogVisible" :before-close="dialogClosed" :modal="false"
        :close-on-click-modal="false" :title="diaTitle" width="50%" custom-class="model-dialog">
        <div class="diaContent" style="padding: 10px">
          <el-form ref="formInline" :model="formInline" label-width="100px" :rules="rules" label-position="right">
            <el-form-item label="频道类型" prop="callGrouptype" class="form-item">
              <el-select v-model="formInline.callGrouptype" filterable placeholder="请选择群组类型" clearable
                :disabled="diaTitle==='编辑群组'" @change="groupTypeChange">
                <el-option v-for="item in callGrouptypeList" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="频道号" prop="groupHostextension" class="form-item is-required">
              <el-input v-model.trim="formInline.groupHostextension"
                :disabled="formInline.callGrouptype!==1||formInline.callGrouptype!==4" placeholder="请输入频道号">
              </el-input>
            </el-form-item>
            <el-form-item label="群组名称" prop="groupHostextension" class="form-item">
              <el-input v-model="formInline.groupName" placeholder="请输入群组名称" class="form-item"></el-input>
            </el-form-item>
            <el-form-item label="备注" prop="groupDesc" class="inputWidth">
              <el-input v-model="formInline.groupDesc" placeholder="请输入备注信息，最多50字" type="textarea" maxlength="50"
                show-word-limit></el-input>
            </el-form-item>
            <el-form-item label="对应岗位">
              <el-button type="primary" @click="addPost">选择岗位</el-button>
              <span v-for="item in postList" :key="item.id" class="postItem">{{ item.postName }} <i
                  class="el-icon-close" @click="deletPost(item.id)"></i> </span>
            </el-form-item>
            <el-form-item prop="office" label="群组终端" class="is-required">
              <el-button type="primary" @click="addTerminal">选择终端</el-button>
              <el-table :data="terminalList" height="300" class="terminalTable" stripe>
                <el-table-column prop="usrName" show-overflow-tooltip label="名称"> </el-table-column>
                <el-table-column prop="extensionModel" label="型号" show-overflow-tooltip>
                </el-table-column>
                <el-table-column prop="extensionNum" label="号码" show-overflow-tooltip>
                </el-table-column>
                <el-table-column prop="usrType" label="类型" align="center" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span>{{ scope.row.usrType | filterType }}</span>
                  </template>
                </el-table-column>
                <!-- <el-table-column prop="groupOnlineState" label="状态">
                  <template slot-scope="scope">
                    <div v-if="scope.row.groupOnlineState=='0'" class="statusClass"><span class="online"></span>离线</div>
                    <div v-if="scope.row.groupOnlineState=='1'" class="statusClass"><span class="noJoined"></span>未进入群组
                    </div>
                    <div v-if="scope.row.groupOnlineState=='2'" class="statusClass"><span class="offOnline"></span>已进入群组
                    </div>
                  </template>
                </el-table-column> -->
                <el-table-column width="150" label="操作" align="center">
                  <template slot-scope="scope">
                    <el-button type="text" :disabled="scope.row.usrType=== 1"
                      @click="deleteTerminal(scope.row.id)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="dialogClosed">取 消</el-button>
          <el-button type="primary" @click="submit('formInline')">确 定</el-button>
        </span>
      </el-dialog>
      <!--选择终端 -->
      <template v-if="terminalDialogShow">
        <SelectTerminalDialog :terminalDialogShow="terminalDialogShow" @submitTerminalDialog="submitTerminalDialog"
          @closeTerminalDialog="closeTerminalDialog" />
      </template>
      <!--选择岗位 -->
      <template v-if="postDialogShow">
        <SelectPostDialog :postDialogShow="postDialogShow" @submitPostDialog="submitPostDialog"
          @closePostDialog="closePostDialog" />
      </template>
      <!--群组详情 -->
      <template v-if="groupDetailDialogShow">
        <groupDetaillDialog :groupDetailDialogShow="groupDetailDialogShow" :groupDetailId="groupDetailId"
          @closeGroupDetailDialog="closeGroupDetailDialog" />
      </template>
    </div>
  </PageContainer>
</template>
<script>
import SelectPostDialog from './components/SelectPost.vue'
import SelectTerminalDialog from './components/SelectTerminal.vue'
import groupDetaillDialog from './components/groupDetail.vue'
export default {
  name: 'groupManagement',
  components: {
    SelectPostDialog,
    SelectTerminalDialog,
    groupDetaillDialog
  },
  filters: {
    filterType(val) {
      return val == '2'
        ? '智能调度终端'
        : val == '3'
          ? '移动调度APP'
          : val == '1'
            ? '调度台'
            : val == '4'
              ? '数字对讲网关'
              : '电话网关'
    }
  },
  data() {
    return {
      tableLoading: false,
      dialogVisible: false,
      tableData: [],
      filterInfo: {
        groupName: ''
      },
      pagination: {
        pageSize: 15,
        pageNum: 1
      },
      callGrouptypeList: [
        {
          value: 2,
          label: '电话会议组'
        },
        {
          value: 4,
          label: '语音对讲组'
        }
      ],
      pageTotal: 0,
      formInline: {
        groupHostextension: '',
        groupName: '',
        groupDesc: '',
        callGrouptype: ''
      },
      rules: {
        callGrouptype: [
          { required: true, message: '请选择群组类型', trigger: 'blur' }
        ],
        groupName: [
          { required: true, message: '请输入群组名称', trigger: 'blur' }
        ]
      },
      diaTitle: '',
      postList: [], // 岗位
      terminalList: [], // 群组终端
      multipleSelection: [],
      groupDetailId: '',
      postDialogShow: false, // 岗位弹窗
      terminalDialogShow: false, // 终端弹窗
      groupDetailDialogShow: false// 详情弹窗
    }
  },
  mounted() {
    this.getTableData()
  },
  methods: {
    getTableData() {
      this.tableLoading = true
      let data = {
        ...this.pagination,
        ...this.filterInfo
      }
      this.$api
        .callgroupList(data)
        .then((res) => {
          this.tableLoading = false
          if (res.code == 200) {
            this.tableData = res.data ? res.data.records : []
            this.pageTotal = res.data ? res.data.total : 0
          } else if (res.message) {
            this.tableData = []
            this.pageTotal = 0
            this.$message.error(res.message)
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    search() {
      this.pagination.pageNum = 1
      this.getTableData()
    },
    reset() {
      this.filterInfo = {
        groupName: ''
      }
      this.pagination.pageNum = 1
      this.pageTotal = 0
      this.getTableData()
    },
    handleCurrentChange(val) {
      this.pagination.pageNum = val
      this.getTableData()
    },
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.pagination.pageNum = 1
      this.getTableData()
    },
    // 选中框
    selectionChange(val) {
      this.multipleSelection = val
    },
    handleListEvent(type) {
      if (type === 'add') {
        this.diaTitle = '新增群组'
        this.formInline = {
          groupHostextension: '',
          groupName: '',
          groupDesc: '',
          callGrouptype: ''
        }
        this.dialogVisible = true
      } else if (type === 'delete') {
        this.$confirm('是否删除所选群组？', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let ids = []
          this.multipleSelection.forEach(el => {
            ids.push(el.id)
          })
          this.$api.batchDeleteCallgroupList({ ids: ids }, {'operation-type': 3}).then((res) => {
            if (res.code === '200') {
              this.$message.success(res.msg)
              this.getTableData()
            } else {
              this.$message.error(res.msg)
            }
          })
        })
      }
    },
    handleOperation(type, row) {
      if (type == 'detail') {
        this.groupDetailDialogShow = true
        this.groupDetailId = row.id
      }
      if (type == 'edit') {
        this.diaTitle = '编辑群组'
        this.$api.callgroupDetails({ id: row.id }).then((res) => {
          if (res.code === '200') {
            this.postList = []
            this.terminalList = res.data.members
            this.formInline = res.data
            if (res.data.postId) {
              let arrId = res.data.postId.split(',')
              let arrName = res.data.postName.split(',')
              arrId.map((label, index) => {
                this.postList.push({
                  id: label,
                  postName: arrName[index]
                })
              })
            }
          } else {
            this.$message.error(res.msg)
          }
        })
        this.dialogVisible = true
      }
      if (type == 'del') {
        this.$confirm(`是否删除${row.groupName}群组?`, '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let params = {
            id: row.id
          }
          this.$api.deleteCallgroupList(params, { 'operation-type': 3, 'operation-id': row.id, 'operation-name': row.groupName}).then((res) => {
            if (res.code === '200') {
              this.$message.success(res.msg)
              this.getTableData()
            } else {
              this.$message.error(res.msg)
            }
          })
        })
      }
    },
    // 选择岗位
    addPost() {
      this.postDialogShow = true
    },
    // 删除岗位
    deletPost(id) {
      this.$confirm('是否将岗位移出群组？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.postList = this.postList.filter((item) => item.id != id)
      })
    },

    // 添加终端
    addTerminal() {
      this.terminalDialogShow = true
    },
    // 删除终端
    deleteTerminal(id) {
      this.$confirm('是否将终端移出群组？', '提示', {
        cancelButtonClass: 'el-button--primary is-plain',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.terminalList = this.terminalList.filter((item) => item.id != id)
      })
    },
    // 确定
    submit(formName) {
      if (!this.terminalList.length) {
        return this.$message.error('请添加终端！')
      }
      let newArr = []
      if (this.terminalList.length) {
        this.terminalList.map(function (item) {
          newArr.push({ usrNumber: item.extensionNum, usrName: item.usrName })
        })
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let data = {
            ...this.formInline,
            postId: this.postList.map(item => item.id).join(',') || '',
            postName: this.postList.map(item => item.postName).join(',') || '',
            members: newArr,
            isLevelOn: 0, // 在启用对讲组等级和自动邀请 只限于语音对讲使用，默认值为0
            maxTime: 0, // 最大发言时间，默认值为0(不限制)
            level: '5', // 组的级别，分五个级别，1-5，代表低到高最低：1, 低：2, 中：3, 高：4, 最高：5
            isDefault: 0// 如果是对讲组，指定此组是否为默认组，如果是默认组，app登录后会自动进入此组：1标识是默认组,0标识非默认组
          }
          if (data.id) {
            this.$api.editGroup(data, { 'operation-type': 2, 'operation-id': data.id, 'operation-name': data.groupName}).then((res) => {
              if (res.code === '200') {
                this.$message.success(res.msg)
                this.getTableData()
                this.dialogVisible = false
                this.formInline = {
                  groupHostextension: '',
                  groupName: '',
                  groupDesc: '',
                  callGrouptype: ''
                }
                this.postList = []
                this.terminalList = []
                this.$refs.formInline.resetFields()
              } else {
                this.$message.error(res.msg)
              }
            })
          } else {
            this.$api.addGroup(data, { 'operation-type': 1}).then((res) => {
              if (res.code === '200') {
                this.$message.success(res.msg)
                this.getTableData()
                this.dialogVisible = false
                this.formInline = {
                  groupHostextension: '',
                  groupName: '',
                  groupDesc: '',
                  callGrouptype: ''
                }
                this.postList = []
                this.terminalList = []
                this.$refs.formInline.resetFields()
              } else {
                this.$message.error(res.msg)
              }
            })
          }

        } else {
          return false
        }
      })
    },
    // 群组类型选择
    groupTypeChange(e) {
      this.getChannelNumber(e)
    },
    // 生成频道号
    getChannelNumber(e) {
      this.$api.groupNumber({ callGrouptype: e }).then((res) => {
        if (res.code == '200') {
          this.formInline.groupHostextension = res.data.groupHostextension
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 取消
    dialogClosed() {
      this.dialogVisible = false
      this.formInline = {
        groupHostextension: '',
        groupName: '',
        groupDesc: ''
      }
      this.postList = []
      this.terminalList = []
      this.$nextTick(() => {
        this.$refs['formInline'].clearValidate()
      })
    },
    // 取消终端弹窗
    closeTerminalDialog() {
      this.terminalDialogShow = false
    },
    // 选择终端弹窗
    submitTerminalDialog(list) {
      this.terminalList = JSON.parse(JSON.stringify(list))
      this.terminalDialogShow = false
    },
    // 部门弹窗
    closePostDialog() {
      this.postDialogShow = false
    },
    submitPostDialog(list) {
      this.postList = list
      this.postDialogShow = false
    },
    // 群组详情弹窗
    closeGroupDetailDialog() {
      this.groupDetailDialogShow = false
    }
  }
}
</script>
<style lang="scss" scoped>
.searchForm {
  display: flex;
  height: 80px;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  .el-input {
    width: 200px;
    margin-left: 16px;
  }
}

.search-box {
  display: flex;
  align-items: center;
}

.container-content > div {
  height: 100%;
  margin-top: 16px;
}
.el-table {
  margin-bottom: 12px;
}

::v-deep .el-table__row:hover {
  cursor: pointer;
}
.table-box {
  background-color: #fff;
  height: 100%;
  padding: 16px;
  .tableContainer {
    height: 100%;
    .el-table {
      height: calc(100% - 96px) !important;
    }
  }
}
.diaContent {
  width: 100%;
  max-height: 550px !important;
  overflow: auto;
  background-color: #fff !important;
  .el-input,
  .el-select,
  .el-cascader {
    width: 300px;
  }
}
.form-item {
  display: inline-block;
  margin-right: 20px;
}
.inputWidth {
  width: 820px;
}
.ml-16 {
  margin-left: 16px;
}
.terminalTable {
  margin-top: 10px;
}
.postItem {
  padding: 5px 10px;
  border-radius: 6px;
  text-align: center;
  color: #3562db;
  margin-left: 16px;
  background: rgba(53, 98, 219, 0.2);
}
.statusClass {
  display: flex;
  align-items: center;
}
.online {
  width: 6px;
  height: 6px;
  border-radius: 6px;
  background-color: #aaaaaa;
  margin-right: 6px;
}
.offOnline {
  width: 6px;
  height: 6px;
  border-radius: 6px;
  background-color: #33cc00;
  margin-right: 6px;
}
.noJoined {
  width: 6px;
  height: 6px;
  border-radius: 6px;
  background-color: #f59a23;
  margin-right: 6px;
}
</style>
