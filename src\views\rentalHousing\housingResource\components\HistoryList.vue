<script>
import RoomEventDetail from '@/views/rentalHousing/housingResource/components/RoomEventDetail.vue'
export default {
  name: 'HistoryList',
  components: { RoomEventDetail },
  props: {
    id: String
  },
  data: () => ({
    tableLoading: false,
    tableData: [],
    searchForm: {
      name: '',
      date: [],
      status: '',
      // 流程申请人
      proposer: ''
    },
    drawer: {
      show: false,
      id: ''
    }
  }),
  mounted() {
    this.onSearch()
  },
  methods: {
    // 查看流程详情
    onView(row) {
      this.drawer.id = row.id
      this.drawer.show = true
    },
    onSearch() {
      const params = {
        pageNum: 1,
        pageSize: 999,
        houseId: this.id,
        applyTimeStart: this.searchForm.date.length ? `${this.searchForm.date[0]} 00:00:00` : '',
        applyTimeEnd: this.searchForm.date.length ? `${this.searchForm.date[1]} 23:59:59` : '',
        eventName: this.searchForm.name,
        status: this.searchForm.status,
        proposer: this.searchForm.proposer
      }
      this.tableLoading = true
      this.$api.rentalHousingApi
        .moveInRecordByPage(params)
        .then((res) => {
          if (res.code === '200') {
            this.tableData = res.data.records
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取入住历史失败'))
        .finally(() => (this.tableLoading = false))
    },
    onReset() {
      this.$refs.formRef.resetFields()
      this.onSearch()
    }
  }
}
</script>
<template>
  <div class="history-list">
    <el-form ref="formRef" :model="searchForm" inline>
      <el-form-item prop="name">
        <el-input v-model="searchForm.name" placeholder="全部事件名称" clearable></el-input>
      </el-form-item>
      <el-form-item prop="proposer">
        <el-input v-model="searchForm.proposer" placeholder="全部承租人" clearable></el-input>
      </el-form-item>
      <el-form-item prop="status">
        <el-select v-model="searchForm.status" placeholder="全部状态" clearable>
          <el-option label="当前入住" value="1"></el-option>
          <el-option label="当前入住（已超时）" value="2"></el-option>
          <el-option label="已退租" value="3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="date">
        <el-date-picker v-model="searchForm.date" type="daterange" value-format="yyyy-MM-dd" start-placeholder="记录时间" end-placeholder="记录时间" clearable></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" plain @click="onReset">重置</el-button>
        <el-button type="primary" @click="onSearch">搜索</el-button>
      </el-form-item>
    </el-form>
    <div class="history-list__table">
      <el-table v-loading="tableLoading" height="100%" :data="tableData" border stripe table-layout="auto" class="tableAuto" row-key="id">
        <el-table-column prop="eventName" label="事件名称" />
        <el-table-column prop="typeName" label="类型" />
        <el-table-column prop="tenantName" label="承租人" />
        <el-table-column prop="createDate" label="记录时间" />
        <el-table-column prop="name" label="操作" width="120px">
          <template #default="{ row }">
            <el-button type="text" @click="onView(row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!--历史详情-->
    <RoomEventDetail :id="drawer.id" :visible.sync="drawer.show" />
  </div>
</template>
<style lang="scss" scoped>
.history-list {
  height: 100%;
  &__table {
    height: calc(100% - 62px);
  }
}
</style>
