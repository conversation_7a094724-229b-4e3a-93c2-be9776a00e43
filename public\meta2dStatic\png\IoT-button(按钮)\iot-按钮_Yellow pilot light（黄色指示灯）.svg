<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 150 150"><defs><style>.cls-1{fill:url(#未命名的渐变_74);}.cls-2{fill:#eee;}.cls-3{fill:#bdaf00;}.cls-4{fill:#eddd27;}.cls-5{fill:#cabb00;}.cls-6{fill:#e5d400;}.cls-7{fill:#e2e2e2;opacity:0.44;}.cls-8{opacity:0.3;fill:url(#未命名的渐变_49);}</style><linearGradient id="未命名的渐变_74" x1="-0.17" y1="75" x2="149.83" y2="75" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#6b6b6b"/><stop offset="0.01" stop-color="#767676"/><stop offset="0.03" stop-color="#959595"/><stop offset="0.04" stop-color="#aaa"/><stop offset="0.37" stop-color="#ccc"/><stop offset="0.74" stop-color="#eaeaea"/><stop offset="0.94" stop-color="#f6f6f6"/><stop offset="0.95" stop-color="#ededed"/><stop offset="0.96" stop-color="#d4d4d4"/><stop offset="0.97" stop-color="#ababab"/><stop offset="0.99" stop-color="#737373"/><stop offset="0.99" stop-color="#666"/></linearGradient><linearGradient id="未命名的渐变_49" x1="9.83" y1="-711.59" x2="9.83" y2="-738.96" gradientTransform="matrix(1.1, 0, 0, -0.77, 65.45, -437.94)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#fff"/><stop offset="1" stop-color="#fff"/></linearGradient></defs><title>iot-按钮</title><g id="图层_54" data-name="图层 54"><rect class="cls-1" x="-0.17" width="150" height="150" rx="5.65"/><circle class="cls-2" cx="74.83" cy="75" r="62.24"/><circle class="cls-3" cx="74.83" cy="75" r="57.4"/><circle class="cls-4" cx="74.83" cy="75" r="51.48"/><circle class="cls-5" cx="74.83" cy="75" r="26.24"/><circle class="cls-6" cx="74.83" cy="75" r="22.02"/><path class="cls-7" d="M132.23,75c0,31.7-25.7-10.8-57.4-10.8S17.42,106.7,17.42,75a57.41,57.41,0,0,1,114.81,0Z"/><ellipse class="cls-8" cx="76.23" cy="121.84" rx="33.6" ry="10.56"/></g></svg>