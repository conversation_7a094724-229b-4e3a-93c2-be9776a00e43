<template>
  <PageContainer v-loading="pageLoading" footer>
    <div slot="content" class="content">
      <div class="baseInfo">
        <!-- 基础信息 -->
        <div class="info-title">
          <div class="title">网格基础信息</div>
        </div>
        <div class="baseInfo-content">
          <el-form ref="form" :model="form" :rules="rules" label-width="140px" inline>
            <el-form-item label="网格名称" prop="areaName">
              <el-input v-model="form.areaName" maxlength="10" :disabled="$route.query.type === 'detail'" placeholder="请输入网格名称"></el-input>
            </el-form-item>
            <el-form-item label="责任部门" prop="officeId">
              <el-select
                v-model="form.officeId"
                :disabled="$route.query.type === 'detail'"
                style="width: 100%"
                popper-class="fireproof-popperClass"
                placeholder="请选择责任部门"
                filterable
                @change="deptChange"
              >
                <el-option v-for="item in deptList" :key="item.id" :label="item.deptName" :value="item.id"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="责任人" prop="principalId">
              <el-input v-if="$route.query.type === 'detail'" v-model="form.principalName" disabled></el-input>
              <el-select
                v-else
                v-model="form.principalId"
                popper-class="fireproof-popperClass"
                style="width: 100%"
                placeholder="请选择责任人"
                @change="(e) => userchange(e, 'phoneNo', 'principalName')"
              >
                <el-option v-for="item in userList" :key="item.id" :label="item.staffName" :value="item.id"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="责任人电话" prop="phoneNo">
              <el-input v-model="form.phoneNo" maxlength="11" :disabled="$route.query.type === 'detail'" placeholder="请输入责任人电话"></el-input>
            </el-form-item>
            <el-form-item label="安全责任人" prop="securityPrincipalId">
              <el-input v-if="$route.query.type === 'detail'" v-model="form.securityPrincipalName" disabled></el-input>
              <el-select
                v-else
                v-model="form.securityPrincipalId"
                popper-class="fireproof-popperClass"
                style="width: 100%"
                placeholder="请选择安全责任人"
                @change="(e) => userchange(e, 'securityPhoneNo', 'securityPrincipalName')"
              >
                <el-option v-for="item in userList" :key="item.id" :label="item.staffName" :value="item.id"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="安全责任人电话" prop="securityPhoneNo">
              <el-input v-model="form.securityPhoneNo" maxlength="11" :disabled="$route.query.type === 'detail'" placeholder="请输入安全责任人电话"></el-input>
            </el-form-item>
            <el-form-item label="最大通行人数" prop="">
              <el-input
                v-model="form.maxThroughCount"
                :disabled="$route.query.type === 'detail'"
                placeholder="请输入最大通行人数"
                @input="(value) => (form.maxThroughCount = value.replace(/[^\d]/g, ''))"
              ></el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="space">
        <!-- 包含空间 -->
        <div class="info-title hasBtn">
          <div class="title">包含空间</div>
          <el-button v-if="$route.query.type !== 'detail'" type="primary" icon="el-icon-plus" @click="showDeptDialog">添加空间</el-button>
        </div>
        <div class="space-content">
          <el-table v-loading="tableLoading" border :data="list" style="width: 100%" height="260">
            <el-table-column prop="spaceName" label="空间名称" />
            <el-table-column prop="regionName" label="所在位置" />
            <el-table-column v-if="$route.query.type !== 'detail'" label="操作" width="80">
              <template slot-scope="scope">
                <span style="color: #ff4d4f; cursor: pointer" @click="remove(scope.row, scope.$index)">移除</span>
              </template>
            </el-table-column>
          </el-table>
          <div class="bottom-pagination" style="height: 40px">
            <el-pagination
              :page-sizes="[15, 30, 20, 100]"
              :page-size="pagination.pageSize"
              :current-page="pagination.pageNo"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            >
            </el-pagination>
          </div>
        </div>
      </div>
      <div class="space">
        <!-- 网格通行监控 -->
        <div class="info-title hasBtn">
          <div class="title">网格通行监控 <span class="marsk">（非必填）</span></div>
          <el-button v-if="$route.query.type !== 'detail'" type="primary" icon="el-icon-plus" @click="showDeviceDialog">添加监控设备</el-button>
        </div>
        <div class="space-content">
          <el-table v-loading="tableLoading" border :data="selectDeviceList" style="width: 100%" height="260">
            <el-table-column prop="deviceName" label="设备名称" />
            <el-table-column prop="modelCode" label="最大通行量">
              <template slot-scope="scope">
                <el-input
                  v-if="$route.query.type !== 'detail'"
                  v-model="scope.row.maxThroughCount"
                  placeholder="请输入最大通行量"
                  style="width: 200px"
                  @input="(value) => (scope.row.maxThroughCount = value.replace(/[^\d]/g, ''))"
                ></el-input>
                <span v-else>{{ scope.row.maxThroughCount }}</span>
              </template>
            </el-table-column>
            <el-table-column v-if="$route.query.type !== 'detail'" label="操作" width="80">
              <template slot-scope="scope">
                <span style="color: #ff4d4f; cursor: pointer" @click="removeDevice(scope.row, scope.$index)">移除</span>
              </template>
            </el-table-column>
          </el-table>
          <div class="bottom-pagination" style="height: 40px">
            <el-pagination
              :page-sizes="[15, 30, 20, 100]"
              :page-size="pagination1.pageSize"
              :current-page="pagination1.pageNo"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total1"
              @size-change="handleSizeChange1"
              @current-change="handleCurrentChange1"
            >
            </el-pagination>
          </div>
        </div>
      </div>
      <!-- 选择空间 -->
      <spaceDialog v-if="isSpaceDialog" :id="$route.query.id" :visible.sync="isSpaceDialog" title="添加空间" :spaceList="allList" @selectSpace="selectSpace" />
      <!-- 选择设备 -->
      <deviceDialog v-if="isDeviceDialog" :visible.sync="isDeviceDialog" :deviceList="allDeviceList" @selectDevice="selectDevice" />
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="back">返回</el-button>
      <el-button v-if="$route.query.type !== 'detail'" type="primary" @click="submitForm">确认</el-button>
    </div>
  </PageContainer>
</template>
<script>
import { transData } from '@/util'
import spaceDialog from './spaceDialog.vue'
import deviceDialog from './deviceDialog.vue'
export default {
  name: 'addKeyAreas',
  components: { spaceDialog, deviceDialog },
  beforeRouteEnter(to, from, next) {
    if (to.meta.title) {
      const titleArr = {
        add: '新增',
        edit: '编辑',
        detail: '查看'
      }
      to.meta.title = titleArr[to.query.type] + '重点区域'
    }
    next()
  },
  data() {
    return {
      form: {
        areaName: '',
        officeId: '',
        officeName: '',
        principalId: '',
        principalName: '',
        phoneNo: '',
        securityPrincipalId: '',
        securityPrincipalName: '',
        securityPhoneNo: '',
        maxThroughCount: ''
      },
      rules: {
        areaName: [{ required: true, message: '请输入网格名称', trigger: 'blur' }],
        officeId: [{ required: true, message: '请选择责任部门', trigger: 'change' }],
        principalId: [{ required: true, message: '请选择责任人', trigger: 'change' }],
        phoneNo: [
          { required: true, message: '请输入责任人电话', trigger: 'change' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ],
        securityPrincipalId: [{ required: true, message: '请选择安全责任人', trigger: 'change' }],
        securityPhoneNo: [
          { required: true, message: '请输入安全责任人电话', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ]
      },
      userList: [],
      deptList: [],
      tableLoading: false,
      allList: [],
      list: [],
      isSpaceDialog: false,
      total: 0,
      total1: 0,
      pagination: {
        pageSize: 15,
        pageNo: 1
      },
      pagination1: {
        pageSize: 15,
        pageNo: 1
      },
      pageLoading: false,
      isDeviceDialog: false,
      allDeviceList: [],
      selectDeviceList: []
    }
  },
  watch: {
    'form.officeId': {
      handler(val) {
        if (val) {
          this.getUserList(val)
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.initEvent()
  },
  methods: {
    handleInput(e) {},
    initEvent() {
      this.getDept()
      // const data = this.$options.data()
      // delete data.rules
      // Object.assign(this.$data, data)
      if (this.$route.query.type !== 'add') {
        this.getDetail()
      } else {
        this.allList = []
        this.list = []
        this.allDeviceList = []
        this.selectDeviceList = []
      }
      this.$refs.form.clearValidate()
      this.$refs.form.resetFields()
    },
    // 获取重点区域详情
    getDetail() {
      const params = {
        id: this.$route.query.id
      }
      this.pageLoading = true
      this.$api.detailkeyAreas(params).then((res) => {
        if (res.code === '200') {
          this.form = res.data
          this.allList = res.data.spaceList || []
          this.allDeviceList = res.data.deviceList
          this.total = this.allList ? this.allList.length : 0
          this.total1 = this.allDeviceList ? this.allDeviceList.length : 0
          this.updateCurrentPageSpaceData()
          this.updateCurrentPageDeviceData()
          // this.list = this.allList.slice(0, this.pagination.pageSize)
          // this.selectDeviceList = this.allDeviceList.slice(0, this.pagination1.pageSize)
        } else {
          this.$message.error(res.message)
        }
        this.pageLoading = false
      })
    },
    updateCurrentPageSpaceData() {
      const start = (this.pagination.pageNo - 1) * this.pagination.pageSize
      const end = start + this.pagination.pageSize
      this.list = this.allList.slice(start, end)
    },
    updateCurrentPageDeviceData() {
      const start = (this.pagination1.pageNo - 1) * this.pagination1.pageSize
      const end = start + this.pagination1.pageSize
      this.selectDeviceList = this.allDeviceList.slice(start, end)
    },
    // 获取部门
    getDept() {
      this.$api.getSelectedDept({}).then((res) => {
        if (res.code == '200') {
          this.deptList = res.data
        }
      })
    },
    // 责任部门点击
    deptChange(val) {
      this.userList = []
      this.form.officeName = this.deptList.find((el) => el.id === val).deptName
      this.getUserList(val)
    },
    // 获取部门下人员list
    getUserList(dept) {
      let params = {
        current: 1,
        size: 9999,
        sex: '',
        pmId: '',
        postId: '', // 岗位
        stationStatus: '',
        officeId: dept
      }
      this.$api.staffList(params).then((res) => {
        if (res.code === 200) {
          this.userList = res.data.records
        }
      })
    },
    // 责任人选择
    userchange(val, phone, name) {
      this.form[phone] = this.userList.find((el) => el.id === val).mobile || ''
      this.form[name] = this.userList.find((el) => el.id === val).staffName
    },
    remove(row, $index) {
      // 移除
      // this.list.splice($index, 1)
      let index = this.allList.findIndex((el) => el.modelCode === row.modelCode)
      this.allList.splice(index, 1)
      // if (this.list.length == 0) {
      const pageNo = (this.pagination.pageNo - 1) * this.pagination.pageSize
      this.list = this.allList.slice(pageNo, pageNo + this.pagination.pageSize)
      this.total = this.allList ? this.allList.length : 0
      // 计算删除后的最大页码
      const maxPage = Math.ceil(this.allList.length / this.pagination.pageSize)
      // 如果当前页超过了最大页，且最大页>0，则跳转到最大页
      if (this.pagination.pageNo > maxPage && maxPage > 0) {
        this.pagination.pageNo = maxPage
        this.updateCurrentPageSpaceData()
      }
      // 如果数据为空，重置到第一页
      if (this.list.length === 0) {
        this.pagination.pageNo = 1
        this.updateCurrentPageSpaceData()
      }
    },
    removeDevice(row, $index) {
      // 移除
      this.selectDeviceList.splice($index, 1)
      let index = this.allDeviceList.findIndex((el) => el.id === row.id)
      this.allDeviceList.splice(index, 1)
      if (this.list.length == 0) {
        this.selectDeviceList = this.allDeviceList.slice(0, this.pagination1.pageSize)
      }
      this.total1 = this.allDeviceList ? this.allDeviceList.length : 0
      // 计算删除后的最大页码
      const maxPage = Math.ceil(this.allDeviceList.length / this.pagination1.pageSize)
      // 如果当前页超过了最大页，且最大页>0，则跳转到最大页
      if (this.pagination1.pageNo > maxPage && maxPage > 0) {
        this.pagination1.pageNo = maxPage
        this.updateCurrentPageDeviceData()
      }
      // 如果数据为空，重置到第一页
      if (this.selectDeviceList.length === 0) {
        this.pagination1.pageNo = 1
        this.updateCurrentPageDeviceData()
      }
    },
    // 选择空间
    selectSpace(value) {
      let newArr = value.map((item) => {
        return {
          regionCode: item.parentGridIds == null ? item.pid + ',' + item.id : item.parentGridIds + ',' + item.id, // 所在位置code
          regionName: item.allSpaceName, // 所在位置
          modelCode: item.modelCode, // 空间编码
          spaceName: item.ssmName, // 空间名称
          // localCode: item.localSpaceCode, // 本地编码
          spaceId: item.id // 空间id
        }
      })
      // 使用 Map 来存储唯一的 modelCode
      let map = new Map()
      // 将 allList 和 value 合并
      let arr = this.allList.concat(newArr)
      // 遍历合并后的数组，将每个对象的 modelCode 作为键存储在 Map 中
      arr.forEach((item) => {
        map.set(item.spaceId, item)
      })
      // 将 Map 的值转换回数组
      this.allList = Array.from(map.values())
      this.list = this.allList.slice(0, this.pagination.pageSize)
      this.total = this.allList.length
    },
    // 选择设备
    selectDevice(list) {
      let newArr = list.map((item) => {
        return {
          deviceId: item.assetsId,
          deviceName: item.assetName
        }
      })
      // 使用 Map 来存储唯一的 modelCode
      let map = new Map()
      // 将 allList 和 value 合并
      let arr = this.allDeviceList.concat(newArr)
      // 遍历合并后的数组，将每个对象的 modelCode 作为键存储在 Map 中
      arr.forEach((item) => {
        map.set(item.deviceId, item)
      })
      // 将 Map 的值转换回数组
      this.allDeviceList = Array.from(map.values())
      this.selectDeviceList = this.allDeviceList.slice(0, this.pagination1.pageSize)
      this.total1 = this.allDeviceList.length
    },
    showDeptDialog() {
      this.isSpaceDialog = true
    },
    handleSizeChange(pageSize) {
      this.pagination.pageNo = 1
      this.pagination.pageSize = pageSize
      // 改变每页条数后，可能需要调整当前页码
      const newTotalPage = Math.ceil(this.total / this.pagination.pageSize)
      if (this.pagination.pageNo > newTotalPage) {
        this.pagination.pageNo = newTotalPage > 0 ? newTotalPage : 1
      }
      this.updateCurrentPageSpaceData()
    },
    handleCurrentChange(current) {
      this.pagination.pageNo = current
      this.updateCurrentPageSpaceData()
    },
    handleSizeChange1(pageSize) {
      this.pagination1.pageNo = 1
      this.pagination1.pageSize = pageSize
      // 改变每页条数后，可能需要调整当前页码
      const newTotalPage = Math.ceil(this.total / this.pagination1.pageSize)
      if (this.pagination1.pageNo > newTotalPage) {
        this.pagination1.pageNo = newTotalPage > 0 ? newTotalPage : 1
      }
      this.updateCurrentPageDeviceData()
    },
    handleCurrentChange1(current) {
      this.pagination1.pageNo = current
      this.updateCurrentPageDeviceData()
    },
    // 提交
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          let params = {
            ...this.form,
            spaceList: this.allList,
            deviceList: this.allDeviceList
          }
          if (this.$route.query.type == 'detail') {
            params.id = this.$route.query.id
          }
          this.$api.addkeyAreas(params).then((res) => {
            if (res.code === '200') {
              this.$message.success('添加成功')
              this.back()
            } else {
              this.$message.error(res.message)
            }
          })
        }
      })
    },
    back() {
      this.$router.go(-1)
    },
    showDeviceDialog() {
      this.isDeviceDialog = true
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  background: #fff;
  overflow: auto;
  .info-title {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    .label_line {
      display: flex;
      align-items: center;
      .line {
        width: 8px;
        height: 14px;
        background: #3562db;
        border-top-right-radius: 14px;
        border-bottom-right-radius: 14px;
        margin-right: 4px;
      }
      .label {
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 15px;
        color: #333333;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
  }
  .hasBtn {
    justify-content: space-between;
  }
  .baseInfo {
    padding: 16px;
    ::v-deep .el-form--inline {
      width: 70%;
      .el-form-item {
        width: 45%;
      }
      .el-form-item__content {
        width: calc(100% - 140px);
      }
    }
  }
  .space {
    padding: 0 24px;
    margin-bottom: 16px;
  }
}
.title {
  display: flex;
  align-items: center;
  font-size: 16px;
  color: #333;
  margin: 16px 0;
  font-weight: 600;
  &::before {
    content: '';
    background: #3562db;
    width: 6px;
    height: 6px;
    margin-right: 6px;
  }
  .marsk {
    font-size: 14px;
    color: #96989a;
  }
}
</style>
<style lang="scss">
.fireproof-popperClass {
  width: 442px;
}
</style>
