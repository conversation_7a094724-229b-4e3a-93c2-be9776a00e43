<template>
  <el-dialog v-if="automaticChangeDialogShow" title="切换提示" :visible.sync="automaticChangeDialogShow" custom-class="automatic-dialog" :before-close="closeDialog">
    <div class="sceneForm_content" style="padding: 10px 20px 10px 10px;">
      <div class="change_box">
        <span>您正在执行以下操作</span>
        <div class="btn_view">
          <div>自动</div>
          <i class="el-icon-right"></i>
          <div :style="{ backgroundColor: dialogData.color }">{{ dialogData.name }}</div>
        </div>
      </div>
      <div class="reason_row">
        <span>请确认原因：</span>
        <div
          v-for="(item, index) in ressonTypeList"
          :key="index"
          class="weather_tag"
          :class="ressonForm.ressonType == item.dictId ? 'active_tag' : ''"
          @click="
            () => {
              ressonForm.ressonType = item.dictId
              ressonForm.remarks += item.dictName
            }
          "
        >
          {{ item.dictName }}
        </div>
      </div>
      <el-input v-model="ressonForm.remarks" type="textarea" :rows="3" placeholder="请输入内容"> </el-input>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button plain type="primary" @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="groupSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  name: 'automaticChangeDialog',
  props: {
    automaticChangeDialogShow: {
      type: Boolean,
      default: false
    },
    dialogData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      ressonTypeList: [
        {
          dictId: '1',
          dictName: '天气突变'
        },
        {
          dictId: '2',
          dictName: '检修'
        }
      ],
      ressonForm: {
        ressonType: '',
        remarks: ''
      }
    }
  },
  mounted() {
    console.log(this.dialogData)
  },
  methods: {
    closeDialog() {
      this.$emit('closeAutomaticDialog')
    },
    groupSubmit() {
      let baseInfo = this.$store.state.user.userInfo.user
      let operatorObj = ''
      if (this.dialogData.viewType == 1) {
        operatorObj = '回路' + this.dialogData.loopName + '自动切换为' + this.dialogData.name
      } else if (this.dialogData.viewType == 2) {
        operatorObj = '分组' + this.dialogData.groupName + '自动切换为' + this.dialogData.name
      } else if (this.dialogData.viewType == 3) {
        operatorObj = '回路' + this.dialogData.loopsName + '自动切换为' + this.dialogData.name
      }
      const params = {
        operator: baseInfo.staffName,
        operatorId: baseInfo.staffId,
        operatorObj: operatorObj,
        details: this.ressonForm.remarks
      }
      this.$api.addOperationRecord(params).then((res) => {
        // console.log(res);
        if (res.code == 200) {
          // this.$message.success('切换成功')
          this.$emit('automaticSubmit', this.dialogData)
        } else {
          this.$message.warning(res.message)
        }
      })
    }
  }
}
</script>
<style src="@/assets/styles/sinomis-ui.css" scoped></style>
<style lang="scss" scoped>
.automatic-dialog {
  width: 30% !important;
  height: 30% !important;
  min-width: 562px !important;
  min-height: 376px !important;

  .sceneForm_content {
    width: 86%;
    margin: 0 auto;

    .change_box {
      width: 100%;
      height: 110px;
      background: #f2f6ff;
      border: 1px dashed #5188fc;
      text-align: center;
      display: flex;
      flex-direction: column;
      justify-content: space-around;

      .btn_view {
        display: flex;
        justify-content: center;
        height: 32px;
        line-height: 32px;

        div {
          width: 80px;
          border-radius: 4px;
          color: #fff;
        }

        div:first-child {
          background: #8671de;
        }
        // div:last-child{
        //   background: #FFBB52;
        // }
        i {
          font-size: 25px;
          font-weight: 600;
          color: #5188fc;
          margin: auto 20px;
        }
      }
    }

    .reason_row {
      height: 50px;
      line-height: 50px;
      flex-wrap: wrap;

      > span {
        font-size: 14px;
        font-family: NotoSansHans-Medium, NotoSansHans;
        font-weight: 500;
        color: #606266;
      }
      // flex: 1;
      display: flex;

      .weather_tag {
        padding: 5px 10px;
        background: #f5f5fa;
        border-radius: 2px;
        font-size: 14px;
        color: #909399;
        height: 30px;
        line-height: 20px;
        margin: auto 10px;
        cursor: pointer;
        border: 1px solid #f5f5fa;
      }

      .active_tag {
        background: #f1f6ff;
        box-sizing: border-box;
        border-color: #5188fc;
        color: #5188fc;
      }
    }
  }
}
</style>
