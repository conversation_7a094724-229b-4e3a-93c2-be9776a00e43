<script>
import tableListMixin from '@/mixins/tableListMixin'
export default {
  name: 'requisitionRegistrationManage',
  mixins: [tableListMixin],
  data() {
    return {
      searchForm: {
        recordNumber: '',//申请单号
        applicationTime: [],//申请时间
      },
      hcsPagination: {
        current: 1,
        size: 15,
        total: 0
      },
      hcsTableData: [],
      multipleSelection: [],
      tableData: [],
      tableLoadingStatus: false,
      hcsTableLoadingStatus: false
    }
  },
  mounted() {
    this.getDataList()
  },
  methods: {
    //列表操作
    operating(type, row) {
      if (type === 'add') {
        this.$router.push({
          name: "requisitionRegistrationAdd",
          query: {
            type: 'add',
          }
        })
      } else if (type === 'details') {
        this.$router.push({
          name: "requisitionRegistrationView",
          query: {
            type: 'view',
            applyId: row.id
          }
        })
      } else if (type === 'edit') {
        this.$router.push({
          name: "requisitionRegistrationAdd",
          query: {
            type: 'edit',
            applyId: this.multipleSelection[0].id
          }
        })
      } else if (type === 'update') {
        this.getHcsInfoData(row.recordNumber)
      } else if (type === 'approval') {
        this.$confirm('确定提交领用吗?', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let params = {
            userId: this.$store.state.user.userInfo.user.staffId,
            userName: this.$store.state.user.userInfo.user.staffName,
            userType: 1,
            id: this.multipleSelection[0].id
          }
          this.$api.commitAuditReceiveApplyRecordData(params).then((res) => {
            if (res.code == 200) {
              this.onSearch()
              this.$message({
                message: res.message,
                type: 'success'
              })
            } else {
              this.$message.error(res.msg)
            }
          })
        })
      } else if (type === 'delete') {
        this.$confirm('确定删除该数据吗?', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let recordNumbers = this.multipleSelection.map((item) => {
            return item.recordNumber;
          });
          let ids = this.multipleSelection.map((item) => {
            return item.id;
          });
          let params = {
            userId: this.$store.state.user.userInfo.user.staffId,
            userName: this.$store.state.user.userInfo.user.staffName,
            userType: 1,
            recordNumber: recordNumbers.join(","),
            id: ids.join(","),
          }
          this.$api.deleteReceiveApplyRecordData(params).then((res) => {
            if (res.code == 200) {
              this.onSearch()
              this.$message({
                message: res.message,
                type: 'success'
              })
            } else {
              this.$message.error(res.msg)
            }
          })
        })
      } else if (type === 'withdraw') {
        this.$confirm('确认撤回所选计划吗?', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let params = {
            userId: this.$store.state.user.userInfo.user.staffId,
            userName: this.$store.state.user.userInfo.user.staffName,
            userType: 1,
            recordNumber: this.multipleSelection[0].recordNumber,
            id: this.multipleSelection[0].id,
          }
          this.$api.withdrawReceiveApplyRecordData(params).then((res) => {
            if (res.code == 200) {
              this.onSearch()
              this.$message({
                message: res.message,
                type: 'success'
              })
            } else {
              this.$message.error(res.msg)
            }
          })
        })
      }
    },
    //table选择
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    // 获取列表数据
    getDataList() {
      const userInfo = this.$store.state.user.userInfo.user
      this.tableData = []
      this.tableLoadingStatus = true
      const params = {
        pageSize: this.pagination.size,
        currentPage: this.pagination.current,
        ...this.searchForm,
        userId: userInfo.staffId,
        userName: userInfo.staffName,
      }
      params.beginDate = params.applicationTime.length ? params.applicationTime[0] : ''
      params.endDate = params.applicationTime.length ? params.applicationTime[1] : ''
      delete params.applicationTime
      this.$api.queryReceiveApplyRecordByPage(params)
        .then((res) => {
          this.tableLoadingStatus = false
          if (res.code === '200') {
            this.tableData = res.data.list
            this.pagination.total = res.data.sum
            if (this.tableData && this.tableData.length) {
              this.getHcsInfoData(this.tableData[0].recordNumber)
            } else {
              this.hcsTableData = []
              this.hcsPagination.total = 0
            }
          } else {
            this.tableData = []
            this.pagination.total = 0
          }
        })
        .catch((err) => {
          this.tableLoadingStatus = false
        })
    },
    // 表单搜索按钮点击
    onSearch() {
      this.pagination.current = 1
      this.getDataList()
    },
    // 表单重置按钮点击
    onReset() {
      this.$refs.formRef.resetFields()
      this.onSearch()
    },
    //获取危化品列表
    getHcsInfoData(recordNumber) {
      const userInfo = this.$store.state.user.userInfo.user
      this.hcsTableData = []
      this.hcsTableLoadingStatus = true
      const params = {
        pageSize: this.hcsPagination.size,
        currentPage: this.hcsPagination.current,
        orderNumber: recordNumber,
        userId: userInfo.staffId,
        userName: userInfo.staffName,
      }
      this.$api.getHcsRecordList(params)
        .then((res) => {
          this.hcsTableLoadingStatus = false
          if (res.code === '200') {
            this.hcsTableData = res.data.list
            this.hcsPagination.total = res.data.sum
          } else {
            this.hcsTableData = []
            this.hcsPagination.total = 0
          }
        })
        .catch((err) => {
          this.hcsTableLoadingStatus = false
        })
    },
    //危化品列表分页
    paginationHcsCurrentChange(val) {
      this.hcsPagination.page = val;
      this.getHcsInfoData(this.tableData[0].recordNumber)
    },
    paginationHcsSizeChange(val) {
      this.hcsPagination.size = val;
      this.getHcsInfoData(this.tableData[0].recordNumber)
    },
  }
}
</script>
<template>
  <PageContainer class="requisitionRegistrationManage">
    <template #content>
      <div class="requisitionRegistrationManage__header">
        <el-form ref="formRef" :model="searchForm" class="requisitionRegistrationManage__search" inline
          @submit.native.prevent="onSearch">
          <el-form-item prop="recordNumber">
            <el-input v-model="searchForm.recordNumber" clearable filterable placeholder="申请单号"></el-input>
          </el-form-item>
          <el-form-item prop="applicationTime">
            <el-date-picker v-model="searchForm.applicationTime" type="daterange" value-format="yyyy-MM-dd"
              range-separator="至" start-placeholder="申请时间" end-placeholder="申请时间"></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" plain @click="onReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="requisitionRegistrationManage__actions">
        <el-button type="primary" @click="operating('approval')"
          :disabled="multipleSelection.length != 1 || multipleSelection[0].status != '暂存'"> 提交领用 </el-button>
        <el-button type="primary" @click="operating('add')" icon="el-icon-plus"> 新增</el-button>
        <el-button type="primary" @click="operating('edit')"
          :disabled="multipleSelection.length != 1 || multipleSelection[0].status != '暂存'"> 修改</el-button>
        <el-button type="danger" plain @click="operating('delete')"
          :disabled="multipleSelection.length < 1 || multipleSelection[0].status != '暂存'">
          删除</el-button>
        <!-- <el-button type="primary" @click="operating('withdraw')"
          :disabled="multipleSelection.length != 1 || multipleSelection[0].status != '待审核'"> 撤回</el-button> -->
      </div>
      <div class="requisitionRegistrationManage__approveTable">
        <el-table v-loading="tableLoadingStatus" height="calc(100% - 50px)" :data="tableData" border stripe
          table-layout="auto" class="tableAuto" row-key="id" @selection-change="handleSelectionChange"
          @row-click="(row) => operating('update', row)" @row-dblclick="(row) => operating('details', row)"
          title="双击列表查看详情">
          <el-table-column type="selection" width="55" show-overflow-tooltip></el-table-column>
          <el-table-column label="状态" width="120" show-overflow-tooltip>
            <template #default="{ row }">
              <span :style="{
                      color:
                        row.status == '审核不通过'
                          ? '#F53F3F'
                          : row.status == '暂存'
                          ? '#FF9A2E'
                          : row.status == '待审核'
                          ? '#FF9A2E'
                          : '#08CB83'
                    }">{{ row.status }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="recordNumber" label="申请单号" show-overflow-tooltip></el-table-column>
          <el-table-column label="申请时间" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ scope.row.createTime }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="applyCount" label="申请数量" show-overflow-tooltip></el-table-column>
          <el-table-column prop="officesName" label="申请科室" show-overflow-tooltip></el-table-column>
          <el-table-column prop="createName" label="申请人" show-overflow-tooltip></el-table-column>
          <el-table-column prop="applyPeoplePhone" label="申请人电话" show-overflow-tooltip></el-table-column>
          <!-- <el-table-column prop="examinePerson" label="审核人" show-overflow-tooltip></el-table-column>
          <el-table-column prop="examineTime" label="审核时间" show-overflow-tooltip></el-table-column>
          <el-table-column prop="examineNotes" label="审核批注" show-overflow-tooltip></el-table-column> -->
        </el-table>
        <div class="requisitionRegistrationManage__pagination">
          <el-pagination :current-page="pagination.page" :page-sizes="pagination.pageSizeOptions"
            :page-size="pagination.size" :layout="pagination.layoutOptions" :total="pagination.total"
            @size-change="paginationSizeChange" @current-change="paginationCurrentChange">
          </el-pagination>
        </div>
      </div>
      <div class="requisitionRegistrationManage__assetsTable">
        <el-table v-loading="hcsTableLoadingStatus" height="calc(100% - 50px)" :data=" hcsTableData" border stripe
          table-layout="auto" class="tableAuto" row-key="id">
          <el-table-column label="序号" width="65" type="index" show-overflow-tooltip></el-table-column>
          <el-table-column prop="materialCode" show-overflow-tooltip label="危化品编码"></el-table-column>
          <el-table-column prop="materialName" show-overflow-tooltip label="危化品名称"></el-table-column>
          <el-table-column prop="model" show-overflow-tooltip label="规格型号"></el-table-column>
          <el-table-column prop="inventory" show-overflow-tooltip label="库存数量"></el-table-column>
          <el-table-column prop="operateCount" show-overflow-tooltip label="申请数量"></el-table-column>
          <el-table-column prop="basicUnitName" show-overflow-tooltip label="基础单位"></el-table-column>
          <!-- <el-table-column prop="trademark" show-overflow-tooltip label="品牌"></el-table-column> -->
          <el-table-column prop="supplierName" show-overflow-tooltip label="供应商"></el-table-column>
          <el-table-column prop="manufacturerName" show-overflow-tooltip label="生产厂家"></el-table-column>
        </el-table>
        <div class="requisitionRegistrationManage__pagination">
          <el-pagination :current-page="hcsPagination.page" :page-sizes="pagination.pageSizeOptions"
            :page-size="hcsPagination.size" :layout="pagination.layoutOptions" :total="hcsPagination.total"
            @size-change="paginationHcsSizeChange" @current-change="paginationHcsCurrentChange">
          </el-pagination>
        </div>
      </div>
    </template>
  </PageContainer>
</template>
<style scoped lang="scss">
.requisitionRegistrationManage {
  ::v-deep(> .container-content) {
    padding: 16px;
    height: 100%;
    width: 100%;
    background-color: #fff;
  }
  &__header {
    display: flex;
    justify-content: space-between;
  }
  &__actions {
    padding-bottom: 10px;
  }
  &__approveTable {
    height: calc(50%);
  }
  &__assetsTable {
    height: calc(40%);
  }
  &__pagination {
    margin: 10px;
  }
  &__tag {
    // 停用
    &--0 {
      --color: #f64646;
    }
    // 启用
    &--1 {
      --color: #00b42a;
    }
    &:before {
      content: '';
      display: inline-block;
      vertical-align: middle;
      margin-right: 4px;
      height: 4px;
      width: 4px;
      border-radius: 4px;
      background-color: var(--color);
    }
  }
  &__table__color {
    display: inline-block;
    height: 16px;
    width: 16px;
    vertical-align: text-top;
    margin-right: 4px;
  }
  .text-red {
    color: #ff1919;
  }
}
.el-form-item {
  margin-bottom: 8px !important;
}
</style>
