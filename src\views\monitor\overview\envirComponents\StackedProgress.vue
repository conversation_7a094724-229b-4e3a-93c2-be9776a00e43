<template>
  <div class="stacked-progress">
    <div class="progress_num">{{ newDataObj.endNum }}</div>
    <div class="stacked-left">
      <div v-for="index in 9" :key="index" class="left_scale" :style="{ top: index * 15 + 'px' }"></div>
      <div
        class="left_triangle"
        :style="{
          bottom: newColDataList.position - 6 + 'px'
        }"
      ></div>
      <div
        class="pos-box"
        :style="{
          bottom: `${newColDataList.position - 35}px`
        }"
      >
        <div>{{ newColDataList.name }}</div>
        <div style="margin-top: 5px;">
          <span class="stacked_font_color" :style="{ color: newDataObj.fontColor }">{{ newColDataList.val }}</span
          ><span>{{ newColDataList.unit }}</span
          ><span class="stacked_bg_color" :style="{ 'background-color': newDataObj.bgColor }">{{ newColDataList.title }}</span>
        </div>
      </div>
    </div>
    <div class="progress_num">{{ newDataObj.startNum }}</div>
  </div>
</template>
<script>
import $ from 'jquery'
export default {
  props: {
    colDataList: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      newDataObj: {},
      newColDataList: {}
    }
  },
  watch: {
    colDataList() {
      this.newColDataList = JSON.parse(JSON.stringify(this.colDataList))
      this.getGrogressData(this.newColDataList)
    }
  },
  mounted() {
    this.newColDataList = JSON.parse(JSON.stringify(this.colDataList))
    this.getGrogressData(this.newColDataList)
  },
  methods: {
    getGrogressData(data) {
      // const selectObj = commonData[data.type]
      Object.assign(data, {
        comval: data.val
      })
      // for (var i in selectObj) {
      //   if (selectObj[i].startNum == null && data.comval <= selectObj[i].endNum) {
      //     data.comval = selectObj[i].endNum - data.comval
      //     selectObj[i].total += data.comval
      //     data.typeTitle = selectObj[i].title
      //     data.color = selectObj[i].fontColor
      //     this.newDataObj = JSON.parse(JSON.stringify(selectObj[i]))
      //     break
      //   } else if (selectObj[i].endNum == null && data.comval > selectObj[i].startNum) {
      //     data.comval = data.comval - selectObj[i].startNum
      //     selectObj[i].total += data.comval
      //     data.typeTitle = selectObj[i].title
      //     data.color = selectObj[i].fontColor
      //     this.newDataObj = JSON.parse(JSON.stringify(selectObj[i]))
      //     break
      //   } else if (selectObj[i].startNum !== null && selectObj[i].endNum !== null && selectObj[i].startNum < data.comval && data.comval <= selectObj[i].endNum) {
      //     if (selectObj[i].startNum == '-0.0000000001') {
      //       Object.assign(selectObj[i], {
      //         startNum: selectObj[i].endNum,
      //         endNum: selectObj[i].afterStartNum
      //       })
      //     }
      //     data.comval = data.comval - selectObj[i].startNum
      //     selectObj[i].total = selectObj[i].endNum - selectObj[i].startNum
      //     data.typeTitle = selectObj[i].title
      //     data.color = selectObj[i].fontColor
      //     this.newDataObj = JSON.parse(JSON.stringify(selectObj[i]))
      //     break
      //   }
      // }
      data.comval = data.comval - data.startNum
      data.total = data.endNum - data.startNum
      // data.typeTitle = data.title
      data.color = data.fontColor
      this.newDataObj = JSON.parse(JSON.stringify(data))
      let percent = data.comval / this.newDataObj.total || 0.1
      data.position = Number($('.stacked-left').height() * percent)
    }
  }
}
</script>
<style lang="scss" scoped>
.stacked-progress {
  height: 220px;
  width: 200px;
  padding: 10px 5px 10px 30px;
  box-sizing: border-box;

  .progress_num {
    width: 25px;
    text-align: center;
  }

  .stacked-left {
    height: 150px;
    width: 1px;
    background-color: #d8d8d8;
    position: relative;
    margin: 10px 0 10px 12px;

    .left_scale {
      position: absolute;
      height: 1px;
      width: 5px;
      background-color: #d8d8d8;
      left: 0;
      z-index: 1;
    }

    .left_triangle {
      position: absolute;
      left: 1px;
      background-color: #d8d8d8;
      width: 0;
      height: 0;
      border-right: 10px solid #d8d8d8;
      border-top: 5px solid #fff;
      border-bottom: 5px solid #fff;
    }

    .pos-box {
      position: absolute;
      left: 20px;
      width: 150px;
      transform: translateY(-50%);

      .stacked_font_color {
        font-size: 20px;
        margin: 10px 8px 0 0;
      }

      .stacked_bg_color {
        display: inline-block;
        width: 40px;
        height: 22px;
        background: #34b253;
        border-radius: 2px;
        line-height: 22px;
        text-align: center;
        margin-left: 5px;
        color: #fff;
      }
    }

    &::after {
      content: "";
      position: absolute;
      top: -3px;
      left: -3px;
      width: 7px;
      height: 7px;
      background: #ff4848;
      border-radius: 50%;
      box-shadow: 0 0 2px 4px #ffdada;
      z-index: 1;
    }

    &::before {
      content: "";
      position: absolute;
      bottom: -3px;
      left: -3px;
      width: 7px;
      height: 7px;
      background: #5188fc;
      border-radius: 50%;
      box-shadow: 0 0 2px 4px #dce7fe;
      z-index: 1;
    }
  }
}
</style>
