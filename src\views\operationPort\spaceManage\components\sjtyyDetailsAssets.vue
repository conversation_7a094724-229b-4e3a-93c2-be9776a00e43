<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <el-tabs v-model="tabsActiveName" @tab-click="handleClick">
        <el-tab-pane label="设备概况" name="1">
          <equipment-situation :deviceId="deviceId" />
        </el-tab-pane>
        <el-tab-pane label="设备详情" name="2">
          <table-content :deviceId="deviceId"/>
        </el-tab-pane>
        <el-tab-pane label="设备档案" name="3">
          <equipment-archives :deviceId="deviceId" />
        </el-tab-pane>
      </el-tabs>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="revert">关闭</el-button>
    </div>
  </PageContainer>
</template>

<script>
import equipmentSituation from './equipmentSituation.vue'
import tableContent from './equipmentInfo.vue'
import equipmentArchives from './equipmentArchives.vue'
export default {
  name: 'detailsAssets',
  components: {
    equipmentSituation,
    tableContent,
    equipmentArchives
  },
  data() {
    return {
      tabsActiveName: '1',
      deviceId: ''
    }
  },
  created() {
    this.deviceId = this.$route.query.id || ''
  },
  methods: {
    handleClick(tab) {},
    revert() {
      // this.$router.go(-1)
      this.$router.push({
        path: '/policyasset/assetOperations',
        query: {
          pageModel: 'list'
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.table-content {
  background: transparent;
  border-radius: 4px;
  height: calc(100% - 0px);
  ::v-deep(.el-tabs) {
    height: 100%;
    .el-tabs__header{
      background: #fff;
      .el-tabs__nav {
        .el-tabs__item{
          padding: 0px 20px;
        }
      }
    }
    .el-tabs__content{
      height: calc(100% - 40px);
      overflow-y: auto;
      padding: 10px 0px 0px 0px;
      .el-tab-pane{
        height: 100%;
      }
    }
  }
}
</style>
