<template>
  <div v-loading="pageLoading" class="contentMain">
    <div class="inner">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <!-- 基本信息 -->
        <div class="baseInfo">
          <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
            <span class="green_line"></span>
            基本信息
          </div>
          <el-row :gutter="60">
            <el-col :span="8">
              <el-form-item label="计划名称" prop="planName">
                <el-input
                  v-if="activeType != 'detail'"
                  v-model="form.planName"
                  placeholder="请输入计划名称"
                  maxlength="50"
                  show-word-limit
                  :disabled="activeType == 'edit'"
                ></el-input>
                <span v-else>{{ form.planName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="计划编码" prop="planCode">
                <el-input v-if="activeType != 'detail'" v-model="form.planCode" placeholder="系统生成计划编码" disabled></el-input>
                <span v-else>{{ form.planCode }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8" style="height: 62px"></el-col>
            <el-col :span="8">
              <el-form-item label="计划类型" prop="template">
                <template v-if="activeType != 'detail'">
                  <el-select
                    v-if="$route.query.type == '0' || $route.query.type == '3' || $route.query.type == '4'"
                    v-model="form.template"
                    :disabled="activeType == 'detail' || activeType == 'edit' || $route.query.type == '3' || $route.query.type == '4'"
                    placeholder="请选择"
                  >
                    <el-option v-for="item in planTypeArr" :key="item.planTypeId" :label="item.planTypeName" :value="item.planTypeId"> </el-option>
                  </el-select>
                  <el-cascader
                    v-if="$route.query.type == '1'"
                    v-model="form.template"
                    :options="deviceType"
                    :props="cascaderProps"
                    :disabled="activeType == 'detail' || activeType == 'edit'"
                    clearable
                  >
                  </el-cascader>
                </template>
                <span v-else>{{ planDetailData.planTypeName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="计划说明" prop="remarks">
                <el-input
                  v-if="activeType != 'detail'"
                  v-model="form.remarks"
                  type="textarea"
                  placeholder="请输入计划说明，最多200字"
                  resize="none"
                  :autosize="{ minRows: 3, maxRows: 3 }"
                  maxlength="200"
                  :disabled="activeType == 'edit'"
                  show-word-limit
                >
                </el-input>
                <el-tooltip v-else class="item" effect="dark" :disabled="!form.remarks" :content="form.remarks" placement="top-start">
                  <span :style="{ width: '100%', display: 'inline-block', 'white-space': 'nowrap', overflow: 'hidden', 'text-overflow': 'ellipsis' }">{{
                    form.remarks || '暂无'
                  }}</span>
                </el-tooltip>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <!-- 执行信息 -->
        <div class="executeInfo">
          <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
            <span class="green_line"></span>
            执行信息
          </div>
          <el-row :gutter="60">
            <el-col :span="8">
              <el-form-item label="周期类型" prop="cycleType">
                <el-select v-if="activeType != 'detail'" v-model="form.cycleType" placeholder="请选择周期类型" :disabled="activeType == 'edit'" @change="changeCycleType">
                  <el-option v-for="item in typeOptions" :key="item.cycleType" :label="item.label" :value="item.cycleType"> </el-option>
                </el-select>
                <span v-else>{{ cycleTypeFn(form.cycleType) }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                v-if="form.cycleType == '6' || form.cycleType == '0' || form.cycleType == '2' || form.cycleType == '3' || form.cycleType == '7'"
                label="起止日期"
                prop="dateInterval"
              >
                <el-date-picker
                  v-if="activeType != 'detail'"
                  v-model="form.dateInterval"
                  :picker-options="pickerOptions"
                  type="daterange"
                  start-placeholder="开始日期"
                  range-separator="至"
                  end-placeholder="结束日期"
                  @change="changePlanRule"
                >
                </el-date-picker>
                <span v-else>{{ moment(planDetail.createStartTime).format('YYYY-MM-DD') + ' 至 ' + moment(planDetail.createEndTime).format('YYYY-MM-DD') }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item v-if="form.cycleType == '8' || form.cycleType == '5'" label="开始日期" prop="startDate">
                <el-date-picker
                  v-if="activeType != 'detail'"
                  v-model="form.startDate"
                  type="date"
                  :disabled="activeType == 'detail' || activeType == 'edit'"
                  placeholder="请选择开始日期"
                  @change="changePlanRule"
                >
                </el-date-picker>
                <span v-else>{{ planDetailData.startDate }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8" class="skipDays">
              <div>
                跳过周六
                <el-radio-group v-if="activeType != 'detail'" v-model="form.skipSaturday" @input="changePlanRule">
                  <el-radio label="0">是</el-radio>
                  <el-radio label="1">否</el-radio>
                </el-radio-group>
                <span v-else style="display: inline-block; margin-left: 15px">{{ form.skipSaturday == '0' ? '是' : '否' }}</span>
              </div>
              <div>
                跳过周日
                <el-radio-group v-if="activeType != 'detail'" v-model="form.skipSunday" @input="changePlanRule">
                  <el-radio label="0">是</el-radio>
                  <el-radio label="1">否</el-radio>
                </el-radio-group>
                <span v-else style="display: inline-block; margin-left: 15px">{{ form.skipSunday == '0' ? '是' : '否' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <el-form-item v-if="form.cycleType == '3'" label="开始月份" prop="startMonth">
                <el-select v-if="activeType != 'detail'" v-model="form.startMonth" placeholder="请选择" :disabled="activeType == 'edit'" @change="changePlanRule">
                  <el-option v-for="item in startMonthArr" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                </el-select>
                <span v-else>{{ startMonthArr.find((i) => i.id == form.startMonth).name }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item v-if="form.cycleType == '3'" label="开始日期" prop="startDay">
                <el-select v-if="activeType != 'detail'" v-model="form.startDay" placeholder="请选择" :disabled="activeType == 'edit'" @change="changePlanRule">
                  <el-option v-for="item in startDateArr" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                </el-select>
                <span v-else>{{ startDateArr.find((i) => i.id == form.startDay).name }}</span>
              </el-form-item>
              <el-form-item v-if="form.cycleType == '0' || form.cycleType == '2'" label="开始日期" prop="startDate">
                <el-select v-if="activeType != 'detail'" v-model="form.startDate" placeholder="请选择" :disabled="activeType == 'edit'" @change="changePlanRule">
                  <el-option v-for="item in startDateArr" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                </el-select>
                <span v-else>{{ startDateArr.find((i) => i.id == form.startDate).name }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="时间" prop="timeInterval">
                <el-time-picker
                  v-if="activeType != 'detail'"
                  v-model="form.timeInterval"
                  is-range
                  start-placeholder="开始日期"
                  range-separator="至"
                  end-placeholder="结束日期"
                  placeholder="选择时间范围"
                  @change="changePlanRule"
                >
                </el-time-picker>
                <span v-else>{{ moment(form.timeInterval[0]).format('HH:mm:ss') + ' 至 ' + moment(form.timeInterval[1]).format('HH:mm:ss') }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item v-if="form.cycleType != '8' && form.cycleType != '7'" label="频次" prop="cycleRole">
                <el-input
                  v-if="activeType != 'detail'"
                  v-model="form.cycleRole"
                  type="number"
                  style="width: 180px"
                  placeholder="请输入频次"
                  max="12"
                  min="1"
                  @input="changePlanRule"
                >
                  <template slot="append">次</template>
                </el-input>
                <span v-else>{{ form.cycleRole + '次' }}</span>
                <el-radio-group v-if="activeType != 'detail'" v-model="form.customTimeType" fill="#3a62d8" size="small" style="vertical-align: top; margin-left: 8px">
                  <el-radio-button v-if="checkFrequencyType() != false" label="2">自定义</el-radio-button>
                  <el-radio-button label="1">最小间隔</el-radio-button>
                </el-radio-group>
              </el-form-item>
              <el-form-item v-if="form.cycleType == '7'" label="频次类型" prop="customTimeType">
                <el-radio-group v-if="activeType != 'detail'" v-model="form.customTimeType" fill="#3a62d8" size="small" style="vertical-align: top; margin-left: 8px">
                  <el-radio-button label="1">最小间隔</el-radio-button>
                  <el-radio-button v-if="checkFrequencyType() != false" label="2">自定义时间</el-radio-button>
                </el-radio-group>
                <span v-else>{{ form.customTimeType == '1' ? '最小间隔' : '自定义时间' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item v-if="form.cycleType != '7'" label="跳过日期" prop="skipDate">
                <el-date-picker
                  v-if="activeType != 'detail'"
                  :key="skipKey"
                  v-model="form.skipDate"
                  :picker-options="pickerDates"
                  type="dates"
                  placeholder="请选择跳过日期"
                  @change="changePlanRule"
                >
                </el-date-picker>
                <span v-else>{{ planDetail.skipDate || '暂无' }}</span>
              </el-form-item>
            </el-col>
            <el-col v-if="form.cycleType != '8' && form.cycleType != '7'" :span="8">
              <el-form-item v-if="form.customTimeType == '1'" label="最小间隔" prop="minInterval">
                <el-input v-if="activeType != 'detail'" v-model="form.minInterval" type="number" placeholder="请输入最小间隔" min="1" @input="changePlanRule">
                  <template slot="append">{{ form.cycleType == '6' ? '分钟' : '天' }}</template>
                </el-input>
                <span v-else>{{ form.minInterval + (form.cycleType == '6' ? '分钟' : '天') }}</span>
              </el-form-item>
              <el-form-item v-if="form.customTimeType == '2'" label="自定义时间" label-width="100px" class="red-star">
                <el-button type="primary" @click="openCustomTimeDialog">{{ activeType != 'detail' ? '配置' : '查看配置' }}</el-button>
              </el-form-item>
            </el-col>
            <el-col v-if="form.cycleType == '7'" :span="8">
              <el-form-item v-if="form.customTimeType == '1'" label="最小间隔时间" label-width="120px" prop="minInterval">
                <el-input
                  v-if="activeType != 'detail'"
                  v-model="form.minInterval"
                  type="number"
                  placeholder="请输入最小间隔"
                  min="1"
                  class="minInterval"
                  oninput="value=value.replace(/^(0+)|[^\d]+/g,' ')"
                  @input="changePlanRule"
                >
                  <template slot="append">
                    <el-dropdown trigger="click" :tabindex="minTimeUnit" @command="changeMinInterval">
                      <span>{{ minTimeUnit == 1 ? '小时' : '天' }}<i class="el-icon-arrow-down el-icon--right"></i></span>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item :command="1">小时</el-dropdown-item>
                        <el-dropdown-item :command="2">天</el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                  </template>
                </el-input>
                <span v-else>{{ form.minInterval + (minTimeUnit == 1 ? ' 小时' : ' 天') }}</span>
              </el-form-item>
              <el-form-item v-if="form.customTimeType == '2'" label="自定义时间" label-width="100px" class="red-star">
                <el-button type="primary" @click="customTypeTime">{{ activeType != 'detail' ? '配置' : '查看配置' }}</el-button>
                <div style="width: 50%; float: right">
                  完整执行
                  <el-radio-group v-if="activeType != 'detail'" v-model="form.completeExecuteFlag" @input="changePlanRule">
                    <el-radio label="0">否</el-radio>
                    <el-radio label="1">是</el-radio>
                  </el-radio-group>
                  <span v-else style="display: inline-block; margin-left: 15px">{{ form.completeExecuteFlag == '0' ? '否' : '是' }}</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item v-if="form.cycleType != '6' && form.cycleType != '7'" label="完成期限" prop="finalTime">
                <el-input v-if="activeType != 'detail'" v-model="form.finalTime" type="number" min="1" placeholder="请输入完成期限" @input="changePlanRule">
                  <template slot="append">天</template>
                </el-input>
                <span v-else>{{ form.finalTime + '天' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="systemType == '2' ? '保养部门' : systemType == '5' ? '年检部门' : '巡检部门'" prop="dept">
                <el-cascader
                  v-if="activeType != 'detail'"
                  v-model="form.dept"
                  placeholder="请选择部门"
                  :options="deptList"
                  :props="deptTree"
                  :show-all-levels="false"
                  :disabled="activeType == 'edit'"
                  clearable
                  filterable
                  style="width: 100%"
                  @change="selectDept"
                >
                </el-cascader>
                <span v-else>{{ planDetail.distributionTeamName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="systemType == '2' ? '保养人员' : systemType == '5' ? '年检人员' : '巡检人员'" prop="person">
                <el-select v-if="activeType != 'detail'" v-model="form.person" multiple filterable :multiple-limit="20" placeholder="请选择人员" @change="changePlanRule">
                  <el-option v-for="item in personList" :key="item.id" :label="item.staffName" :value="item.id"> </el-option>
                </el-select>
                <span v-else>{{ planDetail.planPersonName || '暂无' }}</span>
              </el-form-item>
            </el-col>
            <el-col v-if="form.cycleType == '5'" :span="8" style="height: 62px"></el-col>
            <el-col v-if="form.cycleType == '8' || form.cycleType == '5'" :span="8" style="height: 62px"></el-col>
            <el-col :span="8">
              <el-form-item label="开启定位" prop="locationFlag">
                <el-radio-group v-if="activeType != 'detail'" v-model="form.locationFlag" @input="changePlanRule">
                  <el-radio label="0">是</el-radio>
                  <el-radio label="1">否</el-radio>
                </el-radio-group>
                <span v-else>{{ form.locationFlag == '0' ? '是' : '否' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8" class="titlePlus">
              <el-form-item label="任务执行方式" prop="scanFlag">
                <el-radio-group v-if="activeType != 'detail'" v-model="form.scanFlag" @input="changePlanRule">
                  <el-radio label="0">扫码执行</el-radio>
                  <el-radio label="1">列表执行</el-radio>
                </el-radio-group>
                <span v-else>{{ form.scanFlag == '0' ? '扫码执行' : '列表执行' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8" class="titlePlus">
              <el-form-item label="是否顺序执行" prop="sortFlag">
                <el-radio-group v-if="activeType != 'detail'" v-model="form.sortFlag" @input="changePlanRule">
                  <el-radio label="0">是</el-radio>
                  <el-radio label="1">否</el-radio>
                </el-radio-group>
                <span v-else>{{ form.sortFlag == '0' ? '是' : '否' }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <!-- 超时信息 -->
        <div class="overtimeInfo">
          <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
            <span class="green_line"></span>
            超时信息
          </div>
          <el-row :gutter="60">
            <el-col :span="24" class="titlePlus">
              <el-form-item label="超时提醒" prop="scanFlag">
                <el-radio-group v-if="activeType != 'detail'" v-model="isOverTime" @input="changePlanRule">
                  <el-radio label="0">开启</el-radio>
                  <el-radio label="1">关闭</el-radio>
                </el-radio-group>
                <span v-else>{{ isOverTime == '0' ? '开启' : '关闭' }}</span>
              </el-form-item>
            </el-col>
            <el-col v-if="isOverTime == '0'" :span="24">
              <div v-for="(item, index) in overTimeData" :key="index" class="overtimeItem">
                <span class="titleText">起始时间</span>
                <el-select v-if="activeType != 'detail'" v-model="item.remindType" placeholder="请选择" style="width: 140px; margin-right: 10px">
                  <el-option :label="`${systemType == '2' ? '保养' : systemType == '5' ? '年检' : '巡检'}开始时间`" value="1"></el-option>
                  <el-option :label="`${systemType == '2' ? '保养' : systemType == '5' ? '年检' : '巡检'}结束时间`" value="2"></el-option>
                </el-select>
                <span v-else style="margin-right: 10px">{{
                  (systemType == '2' ? '保养' : systemType == '5' ? '年检' : '巡检') + (item.remindType == '1' ? '开始时间' : '结束时间')
                }}</span>
                <span class="titleText">超时时长</span>
                <template v-if="activeType != 'detail'">
                  <el-input
                    v-model.number="item.duration"
                    :max="9999"
                    maxlength="4"
                    style="width: 60px"
                    class="inputName hour"
                    onkeyup="value=value.replace(/[^\d]/g,'')"
                    @change="checkZero(item.duration, index, 'duration')"
                  >
                  </el-input>
                </template>
                <template v-else>
                  <span>{{ item.duration }}</span>
                </template>
                <span style="margin-right: 10px">分钟</span>
                <span class="titleText">提醒部门</span>
                <el-cascader
                  v-if="activeType != 'detail'"
                  key="cascader2"
                  v-model="item.dept"
                  placeholder="请选择部门"
                  :options="deptList"
                  :props="deptTree"
                  :show-all-levels="false"
                  clearable
                  filterable
                  style="width: 180px; margin-right: 10px"
                  @change="(val) => overTimeSelectDept(val, index)"
                >
                </el-cascader>
                <template v-else>
                  <span style="width: 180px; margin-right: 10px">{{ timeOutData[index].remindDepartmentName }}</span>
                </template>
                <span class="titleText">提醒人员</span>
                <el-select
                  v-if="activeType != 'detail'"
                  v-model="item.person"
                  multiple
                  filterable
                  :multiple-limit="20"
                  class="inputName"
                  placeholder="请选择人员"
                  style="width: 180px; margin-right: 10px"
                >
                  <el-option v-for="i in item.personList" :key="i.id" :label="i.staffName" :value="i.id"> </el-option>
                </el-select>
                <template v-else>
                  <span style="width: 100px; margin-right: 10px">{{ timeOutData[index].remindPersonnelName }}</span>
                </template>
                <span class="titleText">提醒方式</span>
                <el-select
                  v-if="activeType != 'detail'"
                  v-model="item.method"
                  placeholder="请选择提醒方式"
                  class="inputName"
                  style="width: 120px; margin-right: 10px"
                  @change="(val) => changeMethod(val, index)"
                >
                  <el-option v-for="i in reminderMode" :key="i.id" :label="i.name" :value="i.id"> </el-option>
                </el-select>
                <template v-else>
                  <span style="width: 80px; margin-right: 10px">{{ reminderMode.find((i) => i.id == item.method).name }}</span>
                </template>
                <template v-if="item.method == '1'">
                  <template v-if="activeType != 'detail'">
                    <el-input
                      v-model.number="item.methodTime"
                      maxlength="4"
                      style="width: 60px"
                      class="inputName hour"
                      onkeyup="value=value.replace(/[^\d]/g,'')"
                      @change="checkZero(item.methodTime, index, 'methodTime')"
                    >
                    </el-input>
                  </template>
                  <template v-else>
                    <span v-if="item.methodTime">{{ item.methodTime }}</span>
                  </template>
                  <span style="margin-right: 10px">分钟</span>
                </template>
                <span class="titleText">提醒次数</span>
                <el-select v-if="activeType != 'detail'" v-model="item.reminderNum" placeholder="请选择提醒次数" class="inputName" style="width: 120px; margin-right: 10px">
                  <el-option v-for="i in item.reminderNumOption" :key="i.id" :label="i.name" :value="i.id"> </el-option>
                </el-select>
                <template v-else>
                  <span>{{ item.reminderNum == '0' ? '提醒一次' : '持续提醒' }}</span>
                </template>
                <template v-if="item.reminderNum == '1'">
                  <template v-if="activeType != 'detail'">
                    <el-input v-model.number="item.reminderTime" maxlength="4" style="width: 60px" class="inputName hour" onkeyup="value=value.replace(/[^\d]/g,'')"> </el-input>
                  </template>
                  <template v-else>
                    <span>{{ item.reminderTime }}</span>
                  </template>
                  <span>次</span>
                </template>
                <el-link v-if="activeType != 'detail'" type="primary" style="margin: 0 10px" @click="addOertimeData">添加</el-link>
                <el-link v-if="activeType != 'detail'" type="danger" :disabled="overTimeData.length == 1" @click="removeOertimeData(index)">删除</el-link>
              </div>
            </el-col>
          </el-row>
        </div>
        <!-- 调度终端管理 -->
        <div class="InspectionPointInfo">
          <div class="headerTop">
            <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
              <span class="green_line"></span>
              <span class="">调度终端管理</span>
              <el-tooltip placement="right">
                <i class="el-icon-warning"></i>
                <div slot="content" class="popper">配置调度终端后，默认开启NFC模式。此模式不校验巡检模板、不校验是否拍照、不校验巡检点执行顺序。</div>
              </el-tooltip>
            </div>
            <el-link v-if="activeType != 'detail'" type="primary" style="flex-shrink: 0" @click="openTerminalDialog()"> 添加调度终端 </el-link>
          </div>
          <div class="contentTable">
            <div class="listTable">
              <el-table key="id" ref="table" :data="terminalList" style="width: 100%">
                <el-table-column label="序号" type="index" width="100"> </el-table-column>
                <el-table-column prop="assetCode" label="调度终端卡号" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="assetName" label="调度终端卡号名称" show-overflow-tooltip> </el-table-column>
                <el-table-column label="操作" width="150">
                  <template slot-scope="scope">
                    <div class="operation">
                      <el-link v-if="activeType != 'detail'" type="danger" @click="deleteTerminalRow(scope.row)">删除</el-link>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
        <!-- 巡检点管理 -->
        <div class="InspectionPointInfo">
          <div class="headerTop">
            <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
              <span class="green_line"></span>
              <span class="requiredIcon">{{ systemType == '2' ? '保养点管理' : systemType == '5' ? '年检点管理' : '巡检点管理' }}</span>
              <el-button v-if="activeType != 'detail'" type="primary" style="margin-left: 20px" :disabled="pointList.length == 0" @click="batchAddTemplate">
                批量添加模板
              </el-button>
            </div>
            <div>
              <el-link v-if="activeType != 'detail'" type="primary" style="width: 70px" @click="addInspectionPoint">
                {{ systemType == '2' ? '添加保养点' : systemType == '5' ? '添加年检点' : '添加巡检点' }}
              </el-link>
            </div>
          </div>
          <div class="contentTable">
            <div class="listTable">
              <el-table :key="itemKey" ref="table" :data="pointList" style="width: 100%" @selection-change="handleSelectionChange">
                <el-table-column v-if="activeType != 'detail'" type="selection" width="55"></el-table-column>
                <el-table-column label="序号" type="index" width="100"> </el-table-column>
                <el-table-column
                  prop="inspectionPointName"
                  :label="systemType == '2' ? '保养点名称' : systemType == '5' ? '年检点名称' : '巡检点名称'"
                  width="200"
                  show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column prop="assetsRemarks" label="备注说明" width="200" show-overflow-tooltip> </el-table-column>
                <el-table-column :label="systemType == '2' ? '保养点类型' : systemType == '5' ? '年检点类型' : '巡检点类型'" width="200">
                  <template slot-scope="scope">
                    <span>{{ scope.row.inspectionPointType == '1' ? '空间' : scope.row.inspectionPointType == '2' ? '设备' : '自定义' }}</span>
                  </template>
                </el-table-column>
                <el-table-column :label="systemType == '2' ? '保养模板' : systemType == '5' ? '年检模板' : '巡检模板'">
                  <template slot-scope="scope">
                    <template v-if="activeType != 'detail'">
                      <el-link v-if="!scope.row.projectId" type="primary" @click="inspectionTemplate(scope.row, 'template')">
                        点击选择
                        <i class="el-icon-s-order"></i>
                      </el-link>
                      <span v-else style="cursor: pointer" @click="inspectionTemplate(scope.row, 'template')">{{ scope.row.projectName }}</span>
                    </template>
                    <span v-else>{{ scope.row.projectName || '暂无' }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="是否拍照">
                  <template slot-scope="scope">
                    <el-select v-if="activeType != 'detail'" v-model="scope.row.isPicture" @change="changeIsPicture(scope.row)">
                      <el-option v-for="item in pictureOption" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                    </el-select>
                    <span v-else>{{ pictureOption.find((i) => i.value == scope.row.isPicture).label }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="定位点">
                  <template slot-scope="scope">
                    <template v-if="activeType != 'detail'">
                      <el-link v-if="!scope.row.locationPointId" type="primary" :disabled="activeType == 'detail'" @click="inspectionTemplate(scope.row, 'location')">
                        点击选择
                        <i class="el-icon-s-order"></i>
                      </el-link>
                      <span v-else style="cursor: pointer" @click="inspectionTemplate(scope.row, 'location')">{{ scope.row.locationPointName }}</span>
                    </template>
                    <span v-else>{{ scope.row.locationPointName || '暂无' }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="150">
                  <template slot-scope="scope">
                    <div class="operation">
                      <el-link type="primary" @click="detail(scope.row)">详情</el-link>
                      <el-link v-if="activeType != 'detail'" type="danger" @click="deleteRow(scope.row)">删除</el-link>
                      <el-link
                        v-if="activeType != 'detail'"
                        type="primary"
                        :disabled="pointList.length == 1 || scope.row.sort == pointList.length"
                        @click="pointMove(scope.row, 'down')"
                      >
                        下移
                      </el-link>
                      <el-link v-if="activeType != 'detail'" type="primary" :disabled="pointList.length == 1 || scope.row.sort == 1" @click="pointMove(scope.row, 'up')">
                        上移
                      </el-link>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
        <!-- 任务预览 -->
        <div class="taskPreview">
          <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
            <span class="green_line"></span>
            任务预览
          </div>
          <template v-if="activeType != 'detail'">
            <el-button :loading="previewLoading" type="primary" @click="taskPreview"> 生成任务预览{{ allTableList.length > 0 ? '(' + allTableList.length + ')' : '' }} </el-button>
            <el-table v-if="allTableList.length > 0" :key="previewKey" :data="previewList" style="width: 100%; margin-top: 10px">
              <el-table-column label="序号" width="100">
                <template slot-scope="scope">
                  <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
                </template>
              </el-table-column>
              <el-table-column label="任务名称" show-overflow-tooltip prop="taskName"> </el-table-column>
              <el-table-column label="应执行日期" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span>{{ moment(scope.row.taskStartTime).format('YYYY-MM-DD') }}</span>
                </template>
              </el-table-column>
              <el-table-column label="日期类型" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span>{{ moment(scope.row.taskStartTime).weekday() === 6 || moment(scope.row.taskStartTime).weekday() === 0 ? '周末' : '工作日' }}</span>
                </template>
              </el-table-column>
              <el-table-column label="责任部门" show-overflow-tooltip prop="distributionTeamName"></el-table-column>
              <el-table-column :label="systemType == '2' ? '保养点数量' : systemType == '5' ? '年检点数量' : '巡检点数量'" show-overflow-tooltip prop="planCount">
              </el-table-column>
              <el-table-column label="应执行时间" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span v-if="form.cycleType == 7 && $route.query.type != '4' && $route.query.type != '5' && $route.query.type != '6'">
                    {{ moment(scope.row.taskStartTime).format('HH:mm:ss') + ' - ' + moment(scope.row.taskEndTime).format('YYYY-MM-DD HH:mm:ss') }}
                  </span>
                  <span v-else>{{ moment(scope.row.taskStartTime).format('HH:mm:ss') }}</span>
                </template>
              </el-table-column>
            </el-table>
            <div v-if="previewList.length > 0" class="" style="padding-top: 10px; text-align: right">
              <el-pagination
                layout="total, sizes, prev, pager, next, jumper"
                :current-page="paginationData.currentPage"
                :page-sizes="[10, 30, 50, 100]"
                :page-size="paginationData.pageSize"
                :total="paginationData.total"
                @size-change="sizeChange"
                @current-change="currentChange"
              ></el-pagination>
            </div>
          </template>
          <template v-else>
            <el-table :data="taskListDat" style="width: 100%; margin-top: 10px">
              <el-table-column label="序号" width="100">
                <template slot-scope="scope">
                  <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
                </template>
              </el-table-column>
              <el-table-column label="任务名称" show-overflow-tooltip prop="taskName"> </el-table-column>
              <el-table-column label="应执行日期" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span>{{ moment(scope.row.taskStartTime).format('YYYY-MM-DD') }}</span>
                </template>
              </el-table-column>
              <el-table-column label="日期类型" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span>{{ moment(scope.row.taskStartTime).weekday() === 6 || moment(scope.row.taskStartTime).weekday() === 0 ? '周末' : '工作日' }}</span>
                </template>
              </el-table-column>
              <el-table-column label="责任部门" show-overflow-tooltip prop="distributionTeamName"></el-table-column>
              <el-table-column :label="systemType == '2' ? '保养点数量' : systemType == '5' ? '年检点数量' : '巡检点数量'" show-overflow-tooltip prop="totalCount">
              </el-table-column>
              <el-table-column label="应执行时间" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span v-if="form.cycleType == 7 && $route.query.type != '4' && $route.query.type != '5' && $route.query.type != '6'">
                    {{ moment(scope.row.taskStartTime).format('HH:mm:ss') + ' - ' + moment(scope.row.taskEndTime).format('YYYY-MM-DD HH:mm:ss') }}
                  </span>
                  <span v-else>{{ moment(scope.row.taskStartTime).format('HH:mm:ss') }}</span>
                </template>
              </el-table-column>
            </el-table>
            <div style="padding-top: 10px; text-align: right">
              <el-pagination
                layout="total, sizes, prev, pager, next, jumper"
                :current-page="paginationData.currentPage"
                :page-sizes="[10, 30, 50, 100]"
                :page-size="paginationData.pageSize"
                :total="paginationData.total"
                @size-change="listSizeChange"
                @current-change="listCurrentChange"
              ></el-pagination>
            </div>
          </template>
        </div>
      </el-form>
    </div>
    <div class="bottomBar">
      <div class="bottomWrap">
        <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db" @click="goBack">取消</el-button>
        <el-button v-if="activeType != 'detail'" type="primary" @click="addPlans">保存</el-button>
      </div>
    </div>
    <div class="inspectionPoint">
      <addInspectionPoint v-if="inspectionPointVisible" ref="addInspectionPoint" v-model="inspectionPointVisible" @confirm="confirmInspectionPoint"></addInspectionPoint>
    </div>
    <template v-if="addDispatchTerminalShow">
      <addDispatchTerminal :visible.sync="addDispatchTerminalShow" :selectTags="terminalList" @submit="getDispatchTerminalData"></addDispatchTerminal>
    </template>
    <div v-if="dialogDateilVisible" class="inspectionPointDateil">
      <el-dialog :title="systemType == '2' ? '保养点详情' : systemType == '5' ? '年检点详情' : '巡检点详情'" :visible.sync="dialogDateilVisible" top="28vh" width="60%">
        <div v-if="detailDialogType == 'space'" class="detailDialog">
          <el-row :gutter="60">
            <el-col :span="12" class="cosRow">
              <div class="titleWrap">空间本地名称:</div>
              <div class="contenWrap">{{ spaceRowDetail.localSpaceName || '空' }}</div>
            </el-col>
            <el-col :span="12" class="cosRow">
              <div class="titleWrap">空间状态:</div>
              <div class="contenWrap">{{ spaceRowDetail.spaceState || '未知' }}</div>
            </el-col>
            <el-col :span="12" class="cosRow">
              <div class="titleWrap">位置:</div>
              <div class="contenWrap">{{ spaceRowDetail.simName || '未知' }}</div>
            </el-col>
            <el-col :span="12" class="cosRow">
              <div class="titleWrap">本地编码:</div>
              <div class="contenWrap">{{ spaceRowDetail.localSpaceCode || '空' }}</div>
            </el-col>
            <el-col :span="12" class="cosRow">
              <div class="titleWrap">模型编码:</div>
              <div class="contenWrap">{{ spaceRowDetail.modelCode || '空' }}</div>
            </el-col>
            <el-col :span="12" class="cosRow">
              <div class="titleWrap">功能类型:</div>
              <div class="contenWrap">{{ spaceRowDetail.functionDictName || '空' }}</div>
            </el-col>
            <el-col :span="12" class="cosRow">
              <div class="titleWrap">空间高度:</div>
              <div class="contenWrap">{{ spaceRowDetail.hight + 'm' || '未知' }}</div>
            </el-col>
            <el-col :span="12" class="cosRow">
              <div class="titleWrap">建筑面积:</div>
              <div class="contenWrap">{{ spaceRowDetail.area + 'm²' || '未知' }}</div>
            </el-col>
            <el-col :span="24" class="cosRow">
              <div class="titleWrap">备注说明:</div>
              <div class="contenWrap" style="width: 100%">
                <el-input v-model="spaceRowDetail.remark" type="textarea" resize="none" :autosize="{ minRows: 3, maxRows: 5 }" readonly></el-input>
              </div>
            </el-col>
          </el-row>
        </div>
        <div v-if="detailDialogType == 'equipment'">
          <div class="detailDialog">
            <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
              <span class="green_line"></span>
              基本信息
            </div>
            <el-row :gutter="60">
              <el-col :span="8" class="cosRow">
                <div class="titleWrap">设备名称:</div>
                <div class="contenWrap">{{ deviceRowDetail.assetName }}</div>
              </el-col>
              <el-col :span="8" class="cosRow">
                <div class="titleWrap">设备编码:</div>
                <div class="contenWrap">{{ deviceRowDetail.assetCode }}</div>
              </el-col>
              <el-col :span="8" class="cosRow">
                <div class="titleWrap">设备ID:</div>
                <div class="contenWrap">{{ deviceRowDetail.id }}</div>
              </el-col>
              <el-col :span="8" class="cosRow">
                <div class="titleWrap">品牌:</div>
                <div class="contenWrap">{{ deviceRowDetail.assetBrand }}</div>
              </el-col>
              <el-col :span="16" class="cosRow">
                <div class="titleWrap">型号:</div>
                <div class="contenWrap">{{ deviceRowDetail.assetModel }}</div>
              </el-col>
              <el-col :span="8" class="cosRow">
                <div class="titleWrap">出厂日期:</div>
                <div class="contenWrap">{{ moment(deviceRowDetail.dateOfManufacture).format('YYYY-MM-DD') }}</div>
              </el-col>
              <el-col :span="16" class="cosRow">
                <div class="titleWrap">SN码:</div>
                <div class="contenWrap">{{ deviceRowDetail.assetSn }}</div>
              </el-col>
              <el-col :span="8" class="cosRow">
                <div class="titleWrap">启用日期:</div>
                <div class="contenWrap">{{ moment(deviceRowDetail.startDate).format('YYYY-MM-DD') }}</div>
              </el-col>
              <el-col :span="16" class="cosRow">
                <div class="titleWrap">使用期限:</div>
                <div class="contenWrap">{{ deviceRowDetail.serviceLife + '月' }}</div>
              </el-col>
              <el-col :span="12" class="cosRow">
                <div class="titleWrap">所在区域:</div>
                <div class="contenWrap">{{ deviceRowDetail.regionName }}</div>
              </el-col>
              <el-col :span="12" class="cosRow">
                <div class="titleWrap">使用状态:</div>
                <div class="contenWrap">{{ deviceRowDetail.assetStatusName }}</div>
              </el-col>
              <el-col :span="24" class="cosRow">
                <div class="titleWrap">备注说明:</div>
                <div class="contenWrap" style="width: 100%">
                  <el-input v-model="deviceRowDetail.assetsRemarks" type="textarea" resize="none" :autosize="{ minRows: 3, maxRows: 5 }" readonly></el-input>
                </div>
              </el-col>
            </el-row>
          </div>
          <div class="detailDialog typeInfo">
            <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
              <span class="green_line"></span>
              类别信息
            </div>
            <el-row :gutter="60">
              <el-col :span="8" class="cosRow">
                <div class="titleWrap">归口部门:</div>
                <div class="contenWrap">{{ deviceRowDetail.centralizedDepartmentName }}</div>
              </el-col>
              <el-col :span="16" class="cosRow">
                <div class="titleWrap">归属系统:</div>
                <div class="contenWrap">{{ deviceRowDetail.systemCategoryName }}</div>
              </el-col>
            </el-row>
          </div>
        </div>
        <div v-if="detailDialogType == 'customize'" class="detailDialog">
          <el-row :gutter="60">
            <el-col :span="12" class="cosRow">
              <div class="titleWrap">{{ systemType == '2' ? '保养点名称:' : systemType == '5' ? '年检点名称' : '巡检点名称:' }}</div>
              <div class="contenWrap">{{ zdyRowDetail.inspectionPointName }}</div>
            </el-col>
            <el-col :span="12" class="cosRow">
              <div class="titleWrap">{{ systemType == '2' ? '保养点编码:' : systemType == '5' ? '年检点编码' : '巡检点编码:' }}</div>
              <div class="contenWrap">{{ zdyRowDetail.taskPointCode }}</div>
            </el-col>
            <el-col :span="24" class="cosRow">
              <div class="titleWrap">备注说明:</div>
              <div class="contenWrap" style="width: 100%">
                <el-input v-model="zdyRowDetail.remarks" type="textarea" resize="none" readonly :autosize="{ minRows: 3, maxRows: 5 }"></el-input>
              </div>
            </el-col>
          </el-row>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db" @click="dialogDateilVisible = false">关闭</el-button>
        </span>
      </el-dialog>
    </div>
    <div class="templateLocation">
      <templateLocation ref="templateLocation" @closed="closeDialog" @correlation="correlationTemplate"> </templateLocation>
    </div>
    <el-dialog title="自定义时间" :visible.sync="customDialogVisible" width="25%" :before-close="handleClose">
      <div v-for="(item, index) in customTimeArr" :key="index" class="time-picker-box">
        <span>{{ index + 1 }}</span>
        <el-time-picker
          v-model="item.customtime"
          value-format="HH:mm:ss"
          :disabled="activeType == 'detail'"
          is-range
          start-placeholder="开始时间"
          range-separator="至"
          end-placeholder="结束时间"
          placeholder="选择时间范围"
        >
        </el-time-picker>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button v-if="activeType != 'detail'" type="primary" plain @click="resetCustom">重 置</el-button>
        <el-button v-if="activeType != 'detail'" type="primary" @click="customDialogConfirm">确 定</el-button>
        <el-button v-if="activeType == 'detail'" type="primary" @click="handleClose">关 闭</el-button>
      </span>
    </el-dialog>
    <el-dialog title="自定义时间" :visible.sync="customTypeVisible" width="30%" :before-close="handleCustomTypeClose">
      <div v-for="(item, index) in customTimeTypeList" :key="index" class="time-picker-box customTimeType">
        <span>{{ index + 1 }}</span>
        <el-dropdown
          trigger="click"
          :disabled="activeType == 'detail'"
          :tabindex="item.timeStartValue"
          class="customDropdown"
          @command="(val) => changeCustomDay('first', index, val)"
        >
          <span>{{ item.timeStartValue == 0 ? item.timeStartValue + '日' : '+' + item.timeStartValue + '日' }}<i class="el-icon-arrow-down el-icon--right"></i></span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="(count, index) in 366" :key="count" :command="index">
              {{ index == 0 ? index + '日' : '+' + index + '日' }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-time-picker
          v-model="item.startTime"
          placeholder="开始时间"
          :disabled="activeType == 'detail'"
          @change="(val) => changeCustomTime('first', index, val)"
        ></el-time-picker>
        <span class="separator">至</span>
        <el-dropdown
          trigger="click"
          :disabled="activeType == 'detail'"
          :tabindex="item.timeEndValue"
          class="customDropdown"
          @command="(val) => changeCustomDay('second', index, val)"
        >
          <span>{{ item.timeEndValue == 0 ? item.timeEndValue + '日' : '+' + item.timeEndValue + '日' }}<i class="el-icon-arrow-down el-icon--right"></i></span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="(count, index) in 366" :key="count" :command="index">
              {{ index == 0 ? index + '日' : '+' + index + '日' }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-time-picker
          :key="secondKey"
          v-model="item.endTime"
          :disabled="!item.startTime || activeType == 'detail'"
          :picker-options="{ selectableRange: item.timeStartValue == item.timeEndValue ? moment(item.startTime).format('HH:mm:ss') + ' - 23:59:59' : '00:00:00 - 23:59:59' }"
          placeholder="结束时间"
        ></el-time-picker>
        <div v-if="activeType != 'detail'" class="icon-box">
          <i class="el-icon-plus" @click="appCustomTimeList"></i>
          <i v-if="customTimeTypeList.length > 1" class="el-icon-delete" @click="deletCustomTimeList(index)"></i>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button v-if="activeType != 'detail'" type="primary" plain @click="resetCustomType">重 置</el-button>
        <el-button v-if="activeType != 'detail'" type="primary" @click="customTypeConfirm">确 定</el-button>
        <el-button v-if="activeType == 'detail'" type="primary" @click="handleCustomTypeClose">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import addInspectionPoint from './components/addInspectionPoint.vue'
import templateLocation from './components/templateLocation.vue'
import moment from 'moment'
import { transData } from '@/util'
export default {
  name: 'addPlans',
  components: {
    addInspectionPoint,
    templateLocation,
    addDispatchTerminal: () => import('@/views/equipmentCenter/InspectionManagement/components/addDispatchTerminal.vue')
  },
  beforeRouteEnter(to, from, next) {
    if (to.meta.title) {
      to.meta.title = to.query.activeType == 'add' ? '新增计划' : to.query.activeType == 'edit' ? '编辑计划' : to.query.activeType == 'copy' ? '计划复制' : '计划详情'
    }
    next()
  },
  async beforeRouteLeave(to, from, next) {
    if (!['planManagement'].includes(to.name)) {
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      systemType: '',
      activeType: '',
      moment,
      itemKey: '',
      previewKey: '',
      pageLoading: false,
      dialogVisible: false,
      dialogDateilVisible: false,
      previewLoading: false,
      detailDialogType: 'space',
      cascaderProps: {
        label: 'baseName',
        value: 'id',
        children: 'children',
        checkStrictly: true
      },
      deptTree: {
        value: 'id',
        label: 'deptName',
        multiple: true,
        checkStrictly: true
      },
      // 计划类型,模板分类
      planTypeArr: [],
      // 计划类型,设备类别
      deviceType: [],
      allDeviceType: [],
      // 部门列表
      deptList: [],
      allDept: [],
      // 人员列表
      personList: [],
      // 第一次选中的时间
      pickerMinDate: '',
      // 时间跨度校验
      pickerOptions: {},
      // 跳过日期范围
      pickerDates: {},
      form: {
        id: '', // 计划id，修改时必传
        planName: '', // 计划名称
        planCode: '', // 计划编码
        remarks: '', // 计划说明
        template: '', // 计划类型
        cycleType: 8, // 周期类型
        startDate: '', // 开始日期
        skipSaturday: '1', // 跳过周六
        skipSunday: '1', // 跳过周日
        dateInterval: '', // 起止日期
        timeInterval: '', // 时间
        startMonth: '', // 开始月份
        startDay: '', // 开始日期
        cycleRole: '', // 频次
        minInterval: '', // 最小间隔
        finalTime: '', // 完成期限
        dept: [], // 巡检部门
        person: [], // 巡检人员
        skipDate: [], // 跳过日期
        locationFlag: '1', // 是否定位
        scanFlag: '1', // 执行方式
        sortFlag: '0', // 是否顺序
        customTimeType: '1', // 自定义或最小间隔
        customTimeJson: '', // 自定义时间
        completeExecuteFlag: '0'
      },
      rules: {
        planName: [{ required: true, message: '请输入计划名称', trigger: 'blur' }],
        cycleType: [{ required: true }],
        locationFlag: [{ required: true }],
        scanFlag: [{ required: true }],
        sortFlag: [{ required: true }],
        startDate: [{ required: true, message: '请选择开始时间', trigger: 'blur' }],
        dept: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (value.length > 0) {
                callback()
              } else {
                if (this.systemType == '2') {
                  callback(new Error('请选择保养部门'))
                } else if (this.systemType == '5') {
                  callback(new Error('请选择年检部门'))
                } else {
                  callback(new Error('请选择巡检部门'))
                }
              }
            },
            trigger: 'change'
          }
        ],
        location: [{ required: true, trigger: 'blur' }],
        template: [{ required: true, message: '请选择计划类型', trigger: 'blur' }],
        finalTime: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (value === '') {
                callback(new Error('请输入完成期限'))
              } else if (Number(value) < 0 || Number(value) == 0) {
                callback(new Error('完成期限最小为一天'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        minInterval: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (value === '') {
                callback(new Error('请输入最小间隔'))
              } else if (Number(value) < 0 || Number(value) == 0) {
                callback(new Error('最小间隔需大于1' + (this.form.cycleType == '6' ? '分钟' : '天')))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        cycleRole: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (value === '') {
                callback(new Error('请选择频次'))
              } else if (Number(value) <= 0 || Number(value) > 12) {
                callback(new Error('频次需大于0次,小于等于12次'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        timeInterval: [{ required: true, message: '请选择时间', trigger: 'blur' }],
        dateInterval: [{ required: true, message: '请选择起止日期', trigger: 'blur' }],
        startMonth: [{ required: true, message: '请选择开始月份', trigger: 'blur' }],
        startDay: [{ required: true, message: '请选择开始日期', trigger: 'blur' }],
        customTimeType: [{ required: true }]
      },
      // 周期类型
      typeOptions: [
        {
          cycleType: 8,
          label: '单次'
        },
        {
          cycleType: 6,
          label: '每日'
        },
        {
          cycleType: 0,
          label: '每周'
        },
        {
          cycleType: 2,
          label: '每月'
        },
        {
          cycleType: 3,
          label: '季度'
        },
        {
          cycleType: 5,
          label: '全年'
        }
      ],
      // 开始日期数组
      startDateArr: () => {
        if (this.form.cycleType == '0') {
          const dateName = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
          const dateArr = []
          for (let i = 0; i < 7; i++) {
            const item = {
              id: i + 1,
              name: '每' + dateName[i]
            }
            dateArr.push(item)
          }
          return dateArr
        } else if (this.form.cycleType == '2') {
          const dateArr = []
          for (let i = 0; i < 30; i++) {
            const item = {
              id: i + 1,
              name: '每月' + (i + 1) + '日'
            }
            dateArr.push(item)
          }
          return dateArr
        } else if (val == '3') {
          const dateArr = []
          for (let i = 0; i < 30; i++) {
            const item = {
              id: i + 1,
              name: '每月' + (i + 1) + '日'
            }
            dateArr.push(item)
          }
          return dateArr
        }
      },
      // 开始月份
      startMonthArr: [
        {
          id: 1,
          name: '第一个月'
        },
        {
          id: 2,
          name: '第二个月'
        },
        {
          id: 3,
          name: '第三个月'
        }
      ],
      pointList: [],
      pictureOption: [
        { label: '是', value: '0' },
        { label: '否', value: '1' }
      ],
      // 模板&定位点修改项
      modifyItem: '',
      // 空间巡检点详情
      spaceRowDetail: {},
      // 设备巡检点详情
      deviceRowDetail: {},
      // 自定义巡检点详情
      zdyRowDetail: {},
      allTableList: [],
      // 预览table
      previewList: [],
      // 预览分页
      paginationData: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      // 生成预览参数
      previewParmes: {},
      // 是否修改规则
      editRule: false,
      // 今日是否生成过任务
      isGeneration: 0,
      // 计划详情
      planDetailData: {},
      // 已生成的任务列表
      taskListDat: [],
      // 是否开启超时提醒
      isOverTime: '1',
      // 超时信息
      overTimeData: [
        {
          duration: '',
          dept: [],
          person: [],
          method: '',
          methodTime: '',
          reminderNum: '',
          reminderTime: '',
          personList: [],
          reminderNumOption: [],
          remindType: ''
        }
      ],
      // 提醒方式
      reminderMode: [
        {
          id: '0',
          name: '立即提醒'
        },
        {
          id: '1',
          name: '延期提醒'
        }
      ],
      timeOutData: [],
      checkInspectionRow: [],
      planDetail: JSON.parse(sessionStorage.getItem('row')) || {},
      terminalList: [], // 派工终端列表
      addDispatchTerminalShow: false, // 是否打开派工终端弹窗
      customDialogVisible: false, // 是否打开自定义弹窗
      customTimeArr: [],
      inspectionPointVisible: false, // 巡检点弹窗
      skipKey: 0,
      minTimeUnit: 1, // 1:小时  2:天
      customTypeVisible: false, // 自定义类型时间
      customTimeTypeList: [
        {
          timeStartValue: 0,
          startTime: '',
          timeEndValue: 0,
          endTime: ''
        }
      ],
      secondKey: Math.random() // 用于强制更新第二个时间选择器
    }
  },
  watch: {
    'form.dateInterval': {
      handler(val) {
        if (val.length == 2) {
          this.pickerMinDate = ''
        }
        this.skipKey = Math.random()
      }
    },
    overTimeData: {
      handler(newValue, oldValue) {
        for (let i = 0; i < newValue.length; i++) {
          if (newValue[i].dept.length > 5) {
            this.$message({
              message: '最多只支持选择5项',
              type: 'warning'
            })
            this.$nextTick(() => {
              this.overTimeData[i].dept = newValue[i].dept.slice(0, 5)
            })
          }
          if (newValue[i].person.length > 10) {
            this.$message({
              message: '最多只支持选择10项',
              type: 'warning'
            })
            this.$nextTick(() => {
              this.overTimeData[i].person = newValue[i].person.slice(0, 10)
            })
          }
        }
      },
      deep: true
    }
  },
  created() {
    if (!this.$store.state.keepAlive.list.includes('planManagement')) {
      this.initEvent()
    }
  },
  activated() {
    this.initEvent()
  },
  mounted() {},
  methods: {
    /** 周期类型 频次配置判断 */
    checkFrequencyType() {
      let arr = [0, 2, 3, 5]
      if (arr.includes(this.form.cycleType)) {
        this.form.customTimeType = '1'
        return false
      } else {
        return true
      }
    },
    initEvent() {
      this.$nextTick(() => {
        this.setDefaultDataEvent()
        this.getDeptList()
        if (this.$route.path.indexOf('/InspectionManagement') != -1) {
          this.systemType = '1'
        } else if (this.$route.path.indexOf('/MaintenanceManagement') != -1) {
          this.systemType = '2'
        } else if (this.$route.path.indexOf('/comInspectionManagement') != -1) {
          this.planTypeArr = []
          this.systemType = '3'
          let params = {
            pageSize: 999,
            pageNo: 1,
            dictType: 'lnspection_type',
            dictName: ''
          }
          this.$api.sysDictData(params).then((res) => {
            if (res.code == 200) {
              res.data.list.forEach((i) => {
                this.planTypeArr.push({
                  planTypeId: i.dictCode,
                  planTypeName: i.dictName
                })
              })
            } else {
              this.$message.error(res.message)
            }
          })
        } else if (this.$route.path.indexOf('/hazardousChemicalGoods_inspection') != -1) {
          this.systemType = '4'
          this.planTypeArr = []
          this.planTypeArr = [
            {
              planTypeId: '4',
              planTypeName: '安全巡查'
            }
          ]
        } else if (this.$route.path.indexOf('/annualManagement') != -1) {
          this.systemType = '5'
        }
        // 不为危化品时查分类
        if (this.systemType != '4') {
          if (this.$route.query.type == '0') {
            this.getTemplateClassification()
          } else {
            this.getDeviceType()
          }
        }
        // 不为危化品、年检、视频巡逻时增加自定义
        if (this.systemType != '4' && this.systemType != '5' && this.systemType != '6') {
          this.typeOptions.push({
            cycleType: 7,
            label: '自定义'
          })
        }
        this.activeType = this.$route.query.activeType
        this.form.template = this.$route.query.id
        if (this.activeType == 'edit' || this.activeType == 'detail' || this.activeType == 'copy') {
          if (this.activeType == 'detail') {
            this.taskListByPlanId()
          }
          // 不为单次和全年都得校验
          if (this.planDetail.cycleType != 5 && this.planDetail.cycleType != 8 && this.activeType != 'copy') {
            this.checkPlanCount()
          }
          this.getPlanDetail(this.planDetail.id)
          if (this.activeType != 'copy') {
            this.form.id = this.planDetail.id
            this.form.planName = this.planDetail.planName
          } else {
            this.form.planName = this.planDetail.planName + '副本'
          }
          this.form.planCode = this.planDetail.planCode
          this.form.cycleType = this.planDetail.cycleType
          this.changeCycleType(this.planDetail.cycleType)
          if (this.planDetail.cycleType == '8' || this.planDetail.cycleType == '5') {
            // 单次、全年
            this.form.startDate = moment(this.planDetail.startDate)
            this.form.finalTime = this.planDetail.finalTime
          } else if (this.planDetail.cycleType == '6' || this.planDetail.cycleType == '7') {
            // 每日
            this.form.dateInterval = []
            this.form.dateInterval[0] = moment(this.planDetail.createStartTime)
            this.form.dateInterval[1] = moment(this.planDetail.createEndTime)
          } else if (this.planDetail.cycleType == '2' || this.planDetail.cycleType == '0') {
            // 每周、每月
            this.form.dateInterval = []
            this.form.dateInterval[0] = moment(this.planDetail.createStartTime)
            this.form.dateInterval[1] = moment(this.planDetail.createEndTime)
            this.form.finalTime = this.planDetail.finalTime
            this.form.startDate = Number(this.planDetail.startDate)
          } else if (this.planDetail.cycleType == '3') {
            // 季度
            this.form.dateInterval = []
            this.form.dateInterval[0] = moment(this.planDetail.createStartTime)
            this.form.dateInterval[1] = moment(this.planDetail.createEndTime)
            this.form.finalTime = this.planDetail.finalTime
            this.form.startMonth = Number(this.planDetail.startDate.substring(0, 1))
            this.form.startDay = Number(this.planDetail.startDate.substring(2))
          }
          if (this.planDetail.cycleType != '8') {
            this.form.cycleRole = this.planDetail.cycleRole
            this.form.minInterval = this.planDetail.minInterval
          }
          this.form.timeInterval = []
          this.form.timeInterval[0] = moment('2023-01-01 ' + this.planDetail.startTime)
          this.form.timeInterval[1] = moment('2023-01-01 ' + this.planDetail.endTime)
          this.form.dept = JSON.parse(this.planDetail.free2)
          this.form.person = this.planDetail.planPersonCode ? this.planDetail.planPersonCode.split(',') : []
          const skipArr = this.planDetail.skipDate ? this.planDetail.skipDate.split(',') : ''
          if (skipArr) {
            skipArr.forEach((i) => this.form.skipDate.push(moment(i)._d))
          } else {
            this.form.skipDate = []
          }
          this.form.skipSaturday = this.planDetail.skipSaturday
          this.form.skipSunday = this.planDetail.skipSunday
          this.form.remarks = this.planDetail.remarks
          this.form.scanFlag = this.planDetail.scanFlag
          this.form.sortFlag = this.planDetail.sortFlag
          this.form.locationFlag = this.planDetail.locationFlag
          this.selectDept(this.form.dept)
        }
      })
    },
    setDefaultDataEvent() {
      const data = this.$options.data()
      delete data.rules
      Object.assign(this.$data, data)
      this.$set(this.$data, 'pickerOptions', {
        onPick: (obj) => {
          this.pickerMinDate = new Date(obj.minDate).getTime()
        },
        disabledDate: (time) => {
          if (this.pickerMinDate) {
            const day1 = 731 * 24 * 3600 * 1000
            let maxTime = this.pickerMinDate + day1
            let minTime = this.pickerMinDate
            return time.getTime() > maxTime || time.getTime() < minTime
          } else {
            if (this.$route.query.activeType == 'add') {
              return time.getTime() < moment().add(-1, 'd')
            } else {
              return time.getTime() + 1 <= new Date(new Date().toLocaleDateString()).getTime()
            }
          }
        }
      })
      this.$set(this.$data, 'pickerDates', {
        disabledDate: (time) => {
          if (this.form.cycleType != '5') {
            if (this.form.dateInterval) {
              if (this.activeType == 'edit' || this.activeType == 'copy') {
                return moment(time).unix() < this.form.dateInterval[0].unix() || moment(time).unix() > this.form.dateInterval[1].unix()
              } else {
                return time.getTime() < this.form.dateInterval[0].getTime() || time.getTime() > this.form.dateInterval[1].getTime()
              }
            }
          }
        }
      })
      // this.$refs.form.resetFields()
    },
    checkZero(val, index, param) {
      if (val == '0') {
        this.overTimeData[index][param] = ''
      }
    },
    handleClose() {
      this.customDialogVisible = false
    },
    resetCustom() {
      let customLength = this.customTimeArr.length
      let arr = []
      for (let i = 0; i < customLength; i++) {
        arr.push({ customtime: '' })
      }
      this.customTimeArr = arr
      this.form.customTimeJson = ''
    },
    customDialogConfirm() {
      let arr = []
      this.customTimeArr.forEach((item) => {
        if (item.customtime) {
          arr.push({
            startTime: item.customtime[0],
            endTime: item.customtime[1]
          })
        }
      })
      this.form.customTimeJson = arr
      this.customDialogVisible = false
    },
    openCustomTimeDialog() {
      if (!this.form.cycleRole || Number(this.form.cycleRole) > 12 || this.form.cycleRole == '0') {
        return this.$message.warning('请填写正确的频次')
      }
      if (Number(this.form.cycleRole) != this.customTimeArr.length) {
        this.customTimeArr = []
      }
      if (this.customTimeArr.length == 0) {
        let arr = []
        for (let i = 0; i < Number(this.form.cycleRole); i++) {
          arr.push({ customtime: '' })
        }
        this.customTimeArr = arr
      }
      this.customDialogVisible = true
    },
    goBack() {
      sessionStorage.removeItem('row')
      this.$router.go('-1')
    },
    deleteRow(row) {
      let filterData = this.pointList.filter((i) => i.id != row.id)
      filterData.forEach((i, index) => (i.sort = index + 1))
      this.pointList = filterData
    },
    // 获取模板分类
    getTemplateClassification() {
      this.$api
        .getTemplateType({
          systemIdentificationClassification: this.systemType
        })
        .then((res) => {
          if (res.code == '200') {
            this.planTypeArr = res.data
          }
        })
    },
    // 设备分类列表
    getDeviceType() {
      this.$api
        .getDeviceType({
          levelType: 3
        })
        .then((res) => {
          if (res.code == '200') {
            this.deviceType = transData(res.data, 'id', 'parentId', 'children')
            this.allDeviceType = res.data
          }
        })
    },
    // 获取部门列表
    getDeptList() {
      this.$api.getSelectedDept().then((res) => {
        if (res.code == '200') {
          this.allDept = res.data
          this.deptList = transData(res.data, 'id', 'parentId', 'children')
        }
      })
    },
    // 选择巡检部门
    selectDept(val) {
      const deptArr = []
      val.forEach((i) => deptArr.push(i[i.length - 1]))
      if (deptArr.length > 0) {
        this.getLersonnelList(deptArr)
      } else {
        this.personList = []
        this.form.person = []
      }
      this.editRule = true
    },
    // 获取人员列表
    getLersonnelList(deptIds) {
      let params = {
        current: 1,
        size: 9999,
        sex: '',
        pmId: '',
        postId: '', // 岗位
        stationStatus: '',
        officeId: deptIds.join(',')
      }
      this.$api.staffList(params).then((res) => {
        if (res.code == 200) {
          this.personList = res.data.records
          if (this.personList.length == 0) {
            this.form.person = []
          } else {
            if (this.form.person.length > 0) {
              const aaa = []
              this.form.person.forEach((i) => {
                this.personList.forEach((j) => {
                  if (i == j.id) {
                    aaa.push(i)
                  }
                })
              })
              this.form.person = aaa
            }
          }
        }
      })
    },
    overTimeSelectDept(val, index) {
      const deptArr = []
      val.forEach((i) => deptArr.push(i[i.length - 1]))
      if (deptArr.length > 0) {
        this.getOverTimePersonList(deptArr, index)
      } else {
        this.overTimeData[index].dept = []
        this.overTimeData[index].person = []
      }
    },
    getOverTimePersonList(deptIds, index) {
      let params = {
        current: 1,
        size: 9999,
        sex: '',
        pmId: '',
        postId: '', // 岗位
        stationStatus: '',
        officeId: deptIds.join(',')
      }
      this.$api.staffList(params).then((res) => {
        if (res.code == 200) {
          this.overTimeData[index].personList = res.data.records
        }
      })
    },
    changeMethod(val, index) {
      if (val == '0') {
        this.overTimeData[index].reminderNumOption = [
          {
            id: '0',
            name: '提醒一次'
          }
        ]
        this.overTimeData[index].reminderNum = '0'
      } else {
        this.overTimeData[index].reminderNumOption = [
          {
            id: '0',
            name: '提醒一次'
          },
          {
            id: '1',
            name: '持续提醒'
          }
        ]
      }
    },
    // 改变周期类型
    changeCycleType(val) {
      this.form.startDate = ''
      if (val == '0') {
        const dateName = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        this.startDateArr = []
        for (let i = 0; i < 7; i++) {
          const item = {
            id: i + 1,
            name: '每' + dateName[i]
          }
          this.startDateArr.push(item)
        }
      } else if (val == '2') {
        this.startDateArr = []
        for (let i = 0; i < 30; i++) {
          const item = {
            id: i + 1,
            name: '每月' + (i + 1) + '日'
          }
          this.startDateArr.push(item)
        }
      } else if (val == '3') {
        this.startDateArr = []
        for (let i = 0; i < 30; i++) {
          const item = {
            id: i + 1,
            name: '每月' + (i + 1) + '日'
          }
          this.startDateArr.push(item)
        }
      }
    },
    // 添加巡检点
    addInspectionPoint() {
      this.inspectionPointVisible = true
    },
    // 巡检点详情
    detail(row) {
      if (row.inspectionPointType == '1') {
        this.spaceRowDetail = row
        this.detailDialogType = 'space'
      } else if (row.inspectionPointType == '2') {
        this.deviceRowDetail = row
        this.detailDialogType = 'equipment'
      } else if (row.inspectionPointType == '3') {
        this.zdyRowDetail = row
        this.detailDialogType = 'customize'
      }
      this.dialogDateilVisible = true
    },
    // 选择模板&定位点
    inspectionTemplate(row, type) {
      this.$refs.templateLocation.batchAddStatus = false
      if (this.activeType != 'detail') {
        this.modifyItem = row.id
        this.$refs.templateLocation.type = type
        if (type == 'template') {
          this.$refs.templateLocation.getTemTree()
          this.$refs.templateLocation.getTemplateList()
        } else {
          this.$refs.templateLocation.getLocationList()
        }
        this.$refs.templateLocation.templateLocationVisible = true
      }
    },
    // 改变模板&定位点
    closeDialog(val) {
      if (val) {
        // 巡检模板
        if (val.projectName) {
          this.pointList = this.pointList.map((i) => {
            if (i.id == this.modifyItem) {
              i.projectId = val.id
              i.projectName = val.projectName
            }
            return i
          })
          // 定位点
        } else {
          this.pointList = this.pointList.map((i) => {
            if (i.id == this.modifyItem) {
              i.locationPointId = val.id
              i.locationPointName = val.locationPointName
              i.deviceMajor = val.deviceMajor || ''
              i.deviceMinor = val.deviceMinor || ''
            }
            return i
          })
        }
      }
    },
    // 更改是否拍照
    changeIsPicture(val) {
      this.pointList = this.pointList.map((i) => i)
      // this.itemKey = Math.random()
    },
    // 添加勾选的巡检点
    confirmInspectionPoint(msg) {
      if (msg[0].simCode) {
        msg.forEach((i) => {
          i.inspectionPointName = i.localSpaceName
          i.assetsRemarks = i.remark
          i.inspectionPointType = '1' // 1:空间，2:设备，3:自定义
        })
      } else if (msg[0].assetCode) {
        msg.forEach((i) => {
          i.inspectionPointName = i.assetName
          i.assetsRemarks = i.assetsRemarks
          i.inspectionPointType = '2' // 1:空间，2:设备，3:自定义
        })
      } else if (msg[0].taskPointTypeCode == 'zdy') {
        msg.forEach((i) => {
          i.inspectionPointName = i.taskPointName
          i.assetsRemarks = i.remarks
          i.inspectionPointType = '3' // 1:空间，2:设备，3:自定义
        })
      }
      msg.forEach((i, index) => {
        i.isPicture = '1' // 0:拍照，1:不拍照
        if (this.pointList.length > 0) {
          i.sort = this.pointList.length + 1
        } else {
          i.sort = index + 1 // 巡检点排序
        }
        if (this.pointList.find((j) => j.id == i.id)) {
          this.pointList = this.pointList.filter((j) => j.id != i.id)
          this.$message.info(`已经添加过的${this.systemType == '2' ? '保养' : systemType == '5' ? '年检' : '巡检'}点不可重复添加`)
        }
        this.editRule = true
        this.pointList.push(i)
      })
      // this.itemKey = Math.random()
    },
    // 巡检点移动
    pointMove(row, type) {
      const indexNum = this.pointList.map((item) => item.id).indexOf(row.id)
      if (type == 'down') {
        row.sort = row.sort + 1
        this.pointList[indexNum + 1].sort = row.sort - 1
      } else {
        row.sort = row.sort - 1
        this.pointList[indexNum - 1].sort = row.sort + 1
      }
      this.pointList = this.pointList.sort((a, b) => a.sort - b.sort)
      // this.itemKey = Math.random()
    },
    // 任务预览
    taskPreview() {
      // 必填项校验
      this.$refs.form.validate((valid) => {
        if (valid) {
          // 超时配置校验
          if (this.isOverTime == '0') {
            this.overTimeData.forEach((i) => {
              if (!i.remindType) {
                this.$message.error('请选择起始时间')
                return false
              }
              if (!i.duration) {
                this.$message.error('请填写超时时长')
                return false
              }
              if (i.dept.length == 0) {
                this.$message.error('请选择提醒部门')
                return false
              }
              if (i.person.length == 0) {
                this.$message.error('请选择提醒人员')
                return false
              }
              if (!i.method) {
                this.$message.error('请选择提醒方式')
                return false
              }
              if (i.method == '1' && !i.methodTime) {
                this.$message.error('请输入提醒时间')
                return false
              }
              if (!i.reminderNum) {
                this.$message.error('请选择提醒次数')
                return false
              }
              if (i.reminderNum == '1' && !i.reminderTime) {
                this.$message.error('请输入提醒次数')
                return false
              }
            })
          }
          if (this.form.cycleType != 7 && this.form.customTimeType == '2' && Number(this.form.cycleRole) != this.customTimeArr.length) {
            this.$message.error('请选择自定义时间')
            return false
          }
          if (this.form.cycleType == 7 && this.form.customTimeType == '2' && this.customTimeTypeList.some((i) => !i.startTime || !i.endTime)) {
            this.$message.error('请配置自定义时间')
            return false
          }
          // 巡检点校验
          if (this.pointList.length > 0) {
            this.parames()
          } else {
            this.$message({
              type: 'error',
              message: `${this.systemType == '2' ? '请先添加保养点' : this.systemType == '5' ? '请先添加年检点' : '请先添加巡检点'}`
            })
          }
        } else {
          this.$message({
            type: 'error',
            message: '请先完成必填项'
          })
          return false
        }
      })
    },
    // 预览/保存参数
    parames() {
      if (this.activeType == 'edit') {
        if (this.form.cycleType != '8' && this.form.cycleType != '5' && this.form.cycleType != '0') {
          if (this.form.cycleType == '2') {
            if (moment(this.form.startDate) > moment().date()) {
              this.$message({
                type: 'error',
                message: '开始日期必须大于今天'
              })
              return false
            }
          } else {
            if (moment(this.form.startDate) < moment()) {
              this.$message({
                type: 'error',
                message: '开始日期必须大于今天'
              })
              return false
            }
          }
        } else {
          if (moment(this.form.dateInterval[0]) < moment().format('YYYY-MM-DD')) {
            this.$message({
              type: 'error',
              message: '起止日期的开始日期必须大于今天'
            })
            return false
          }
        }
      }
      let planTypeName = ''
      let personName = []
      let distributionTeamName = []
      const deptArr = []
      this.form.dept.forEach((i) => deptArr.push(i[i.length - 1]))
      deptArr.forEach((i) => {
        this.allDept.forEach((j) => {
          if (i == j.id) {
            distributionTeamName.push(j.deptName)
          }
        })
      })
      if (this.form.person.length > 0) {
        this.form.person.forEach((i) => {
          this.personList.forEach((j) => {
            if (i == j.id) {
              personName.push(j.staffName)
            }
          })
        })
      }
      if (this.$route.query.type == '0' || this.$route.query.type == '3' || this.$route.query.type == '4') {
        planTypeName = this.planTypeArr.find((i) => i.planTypeId == this.form.template).planTypeName
      } else if (this.$route.query.type == '1') {
        for (let i = 0; i < this.form.template.length; i++) {
          planTypeName += this.allDeviceType.find((j) => j.id == this.form.template[i]).baseName + '/'
        }
        planTypeName = planTypeName.slice(0, planTypeName.length - 1)
      }
      let startDate = ''
      if (this.form.cycleType == '8' || this.form.cycleType == '5') {
        startDate = moment(this.form.startDate).format('YYYY-MM-DD')
      } else if (this.form.cycleType == '0' || this.form.cycleType == '2') {
        startDate = this.form.startDate
      } else if (this.form.cycleType == '3') {
        startDate = this.form.startMonth + '-' + this.form.startDay
      }
      let pointList = []
      this.pointList.forEach((i) => {
        const item = {
          taskPointId: i.id,
          taskPointName: i.inspectionPointName,
          taskBookId: i.projectId || '',
          taskBookName: i.projectName || '',
          locationPointId: i.locationPointId || '',
          locationPointName: i.locationPointName || '',
          isPhotograph: i.isPicture,
          particulars: JSON.stringify(i),
          taskPointTypeCode: i.inspectionPointType == '1' ? 'SPACE_001' : i.inspectionPointType == '2' ? 'DEVICE_001' : 'zdy',
          taskPointTypeName: i.inspectionPointType == '1' ? '空间' : i.inspectionPointType == '2' ? '设备' : '自定义',
          assetsRemarks: i.assetsRemarks // 设备的备注说明字段
        }
        pointList.push(item)
      })
      let skipDate = []
      if (this.form.skipDate) {
        this.form.skipDate.forEach((i) => skipDate.push(moment(i).format('YYYY-MM-DD')))
      }
      let planTeamPerson = []
      deptArr.forEach((i) => {
        const item = {
          distributionTeamId: i,
          distributionTeamName: this.allDept.find((j) => j.id == i).deptName || '',
          planPersonCode: '',
          planPersonName: ''
        }
        const personIds = []
        const personNames = []
        this.personList.forEach((j) => {
          this.form.person.forEach((k) => {
            if (j.id == k) {
              if (j.officeId.indexOf(i) != -1) {
                personIds.push(j.id)
                personNames.push(j.staffName)
              }
            }
          })
        })
        item.planPersonCode = personIds.join(',') || ''
        item.planPersonName = personNames.join(',') || ''
        planTeamPerson.push(item)
      })
      let params = {
        id: '',
        planName: this.form.planName,
        planTypeId: this.$route.query.type == '1' ? this.form.template.join(',') : this.form.template,
        planTypeName,
        distributionTeamId: deptArr.join(','),
        distributionTeamName: distributionTeamName.join(','),
        planPersonCode: this.form.person.join(','),
        planPersonName: personName.join(','),
        planPersonUserId: this.form.person.join(','),
        planPersonUserName: personName.join(','),
        cycleType: this.form.cycleType,
        locationFlag: this.form.locationFlag,
        scanFlag: this.form.scanFlag,
        sortFlag: this.form.sortFlag,
        startDate,
        endDate: '',
        startTime: moment(this.form.timeInterval[0]).format('HH:mm:ss'),
        endTime: moment(this.form.timeInterval[1]).format('HH:mm:ss'),
        finalTime: this.form.finalTime,
        cycleRole: this.form.cycleType == '8' ? '' : this.form.cycleRole,
        minInterval: this.form.minInterval,
        remarks: this.form.remarks,
        maintainPlanRegions: JSON.stringify(pointList),
        saveType: '1',
        maintainType: '1',
        createStartTime: this.isGeneration > 0 ? moment().add(1, 'd').format('YYYY-MM-DD') : moment(this.form.dateInterval[0]).format('YYYY-MM-DD'), // 如果今天生成任务则从今天开始
        createEndTime: moment(this.form.dateInterval[1]).format('YYYY-MM-DD'),
        planTeamPerson: JSON.stringify(planTeamPerson),
        skipSaturday: this.form.skipSaturday,
        skipSunday: this.form.skipSunday,
        skipDate: skipDate.join(','),
        executeMode: '1', // xxx
        systemCode: this.systemType, // 1:设备 2: 保养 3：巡检 4：危化品
        departmentCode: this.$store.state.user.userInfo.user.deptId,
        departmentName: this.$store.state.user.userInfo.user.deptName,
        free2: JSON.stringify(this.form.dept),
        timeoutReminder: '1', // 是否开启超时提醒 0：开启 1：关闭
        customTimeType: this.form.customTimeType
      }
      if (this.form.customTimeType != '1') {
        params.customTimeJson = JSON.stringify(this.form.customTimeJson)
        params.completeExecuteFlag = this.form.completeExecuteFlag
      }
      // 自定义类型增加间隔单位
      if (this.form.cycleType == 7 && this.form.customTimeType == '1') {
        params.minTimeUnit = this.minTimeUnit
      }
      // 是否配置超时信息
      if (this.isOverTime == '0') {
        params.timeoutReminder = this.isOverTime
        let overTimeParams = []
        this.overTimeData.forEach((i) => {
          let deptId = []
          if (typeof i.dept[0] == 'string') {
            deptId = i.dept
          } else {
            for (const arr of i.dept) {
              for (const value of arr) {
                deptId.push(value)
              }
            }
          }
          let deptName = []
          deptId.forEach((j) => {
            this.deptList.forEach((k) => {
              if (k.id == j) {
                deptName.push(k.deptName)
              }
            })
          })
          const personName = []
          i.person.forEach((j) => {
            i.personList.forEach((k) => {
              if (k.id == j) {
                personName.push(k.staffName)
              }
            })
          })
          const overTimeItem = {
            remindType: i.remindType,
            timeOutPeriod: i.duration * 60,
            remindDepartmentId: deptId.join(','),
            remindDepartmentName: deptName.join(','),
            remindPersonnelId: i.person.join(','),
            remindPersonnelName: personName.join(','),
            reminderMode: i.method,
            delayTime: i.method == '0' ? '' : i.methodTime * 60,
            remindAmountMode: i.reminderNum,
            remindAmount: i.reminderNum == '0' ? '1' : i.reminderTime
          }
          overTimeParams.push(overTimeItem)
        })
        params.messagePushConfigurations = JSON.stringify(overTimeParams)
      }
      console.log('params', params)
      this.previewLoading = true
      this.$api.getTaskPreview(params).then((res) => {
        if (res.code == '200') {
          if (res.data.list && res.data.list.length > 0) {
            res.data.list.forEach((i, index) => (i.index = index + 1))
            this.allTableList = res.data.list
            this.previewList = this.allTableList.slice(0, this.paginationData.pageSize)
            this.paginationData.total = res.data.list.length
          } else {
            this.previewList = []
            this.allTableList = []
          }
          this.editRule = false
        } else {
          this.$message({
            type: 'error',
            message: res.message || '生成任务预览失败'
          })
        }
        this.previewLoading = false
      })
      this.previewParmes = params
    },
    sizeChange(val) {
      this.paginationData.currentPage = 1
      this.previewList = this.allTableList.slice(
        (this.paginationData.currentPage - 1) * val,
        this.paginationData.currentPage * val > this.allTableList.length ? this.allTableList.length : this.paginationData.currentPage * val
      )
      this.previewKey = Math.random()
      this.paginationData.pageSize = val
    },
    currentChange(val) {
      if (val == '1') {
        this.previewList = this.allTableList.slice(0, this.paginationData.pageSize)
      } else {
        this.previewList = this.allTableList.slice(
          (val - 1) * this.paginationData.pageSize,
          val * this.paginationData.pageSize > this.allTableList.length ? this.allTableList.length : val * this.paginationData.pageSize
        )
      }
      this.previewKey = Math.random()
      this.paginationData.currentPage = val
    },
    // 保存计划
    addPlans() {
      if (this.editRule) {
        this.$message({
          type: 'error',
          message: '计划生成规则发生改变，请重新生成预览'
        })
      } else {
        if (this.previewList.length > 0) {
          this.previewParmes.id = this.form.id
          if (this.activeType == 'edit') {
            this.previewParmes.useState = this.planDetail.useState
          }
          if (this.systemType == '3' || this.systemType == '4') {
            this.previewParmes.menuType = this.form.template
          }
          if (this.systemType == '5') {
            this.previewParmes.systemCode = '5'
          }
          this.pageLoading = true
          // 获取调度终端管理选中的设备
          const terminalList = Array.from(this.terminalList, ({ id }) => id)
          const params = {
            ...this.previewParmes,
            terminalIds: terminalList.join(',') || ''
          }
          // 添加操作日志
          let header = {}
          if (this.activeType == 'edit') {
            header = {
              'operation-type': 2,
              'operation-id': this.form.id,
              'operation-name': this.form.planName
            }
          } else {
            header = {
              'operation-type': 1
            }
          }
          this.$api.addPlanTask(params, header).then((res) => {
            if (res.code == '200') {
              this.$message({
                type: 'success',
                message: res.message
              })
              this.pageLoading = false
              this.$router.go('-1')
            } else {
              this.$message({
                type: 'error',
                message: res.message
              })
            }
            this.pageLoading = false
          })
        } else {
          this.$message({
            type: 'error',
            message: '请先生成任务预览，或检查计划配置'
          })
        }
      }
    },
    // 计划详情
    getPlanDetail(id) {
      this.pageLoading = true
      this.$api.getPlanDetail({ id }).then((res) => {
        if (res.code == '200') {
          this.planDetailData = res.data.data
          this.timeOutData = res.data.messagePushConfiguration
          const pointList = []
          res.data.data.maintainPlanRegions.forEach((i) => {
            const items = JSON.parse(i.particulars)
            pointList.push(items)
          })
          this.pointList = pointList
          // 超时提醒
          this.isOverTime = this.planDetailData.timeoutReminder || '1'
          let arrData = []
          if (this.timeOutData.length > 0) {
            this.timeOutData.forEach((i, index) => {
              const item = {
                duration: Number(i.timeOutPeriod) / 60,
                dept: i.remindDepartmentId.split(','),
                person: i.remindPersonnelId.split(','),
                method: i.reminderMode,
                methodTime: Number(i.delayTime) / 60,
                reminderNum: i.remindAmountMode,
                reminderTime: i.remindAmount,
                personList: [],
                reminderNumOption: [],
                remindType: i.remindType
              }
              if (i.remindAmountMode == '0') {
                item.reminderNumOption = [
                  {
                    id: '0',
                    name: '提醒一次'
                  }
                ]
                item.reminderNum = '0'
              } else {
                item.reminderNumOption = [
                  {
                    id: '0',
                    name: '提醒一次'
                  },
                  {
                    id: '1',
                    name: '持续提醒'
                  }
                ]
              }
              this.getOverTimePersonList(i.remindDepartmentId.split(','), index)
              arrData.push(item)
            })
          } else {
            arrData = [
              {
                duration: '',
                dept: [],
                person: [],
                method: '',
                methodTime: '',
                reminderNum: '',
                reminderTime: '',
                personList: [],
                reminderNumOption: [],
                remindType: ''
              }
            ]
          }
          this.overTimeData = arrData
          this.terminalList = this.planDetailData.terminalList || []
          this.form.customTimeType = this.planDetailData.customTimeType
          this.form.customTimeJson = this.planDetailData.customTimeJson ? JSON.parse(this.planDetailData.customTimeJson) : []
          if (this.form.cycleType != '7') {
            let arr = []
            if (this.form.customTimeJson) {
              this.form.customTimeJson.forEach((i) => {
                arr.push({
                  customtime: [i.startTime, i.endTime]
                })
              })
            }
            this.customTimeArr = arr
          } else {
            this.form.finalTime = this.planDetailData.finalTime
            this.minTimeUnit = this.planDetailData.minTimeUnit
            this.form.completeExecuteFlag = this.planDetailData.completeExecuteFlag || '0'
            if (this.planDetailData.customTimeJson) {
              this.customTimeTypeList = JSON.parse(this.planDetailData.customTimeJson).map((i) => {
                return {
                  startTime: moment('2025-01-01 ' + i.startTime),
                  endTime: moment('2025-01-01 ' + i.endTime),
                  timeStartValue: i.timeStartValue,
                  timeEndValue: i.timeEndValue
                }
              })
            }
          }
          this.pageLoading = false
        }
      })
    },
    // 修改计划规则
    changePlanRule() {
      this.editRule = true
    },
    // 编辑计划时校验计划在今天是否生成过
    checkPlanCount() {
      this.$api.checkPlan({ planId: this.planDetail.id }).then((res) => {
        if (res.code == '200') {
          this.isGeneration = res.data
        }
      })
    },
    // 周期类型
    cycleTypeFn(typeId) {
      const item = this.typeOptions.filter((i) => i.cycleType == typeId)
      return item[0].label
    },
    // 根据计划id查任务列表
    taskListByPlanId() {
      const params = {
        pageNo: this.paginationData.currentPage,
        pageSize: this.paginationData.pageSize,
        planName: '',
        planPersonName: '',
        departmentName: '',
        planId: this.planDetail.id,
        systemCode: this.systemType, // 1:设备 2: 保养 3：巡检 3：危化品
        planTypeId: '',
        dateType: '',
        distributionTeamId: ''
      }
      this.$api.getTaskMaintaninList(params).then((res) => {
        if (res.code == '200') {
          this.taskListDat = res.data.list
          this.paginationData.total = res.data.sum
        }
      })
    },
    listSizeChange(val) {
      this.paginationData.currentPage = 1
      this.paginationData.pageSize = val
      this.taskListByPlanId()
    },
    listCurrentChange(val) {
      this.paginationData.currentPage = val
      this.taskListByPlanId()
    },
    addOertimeData() {
      this.overTimeData.push({
        duration: '',
        dept: [],
        person: [],
        method: '',
        methodTime: '',
        reminderNum: '',
        reminderTime: '',
        personList: [],
        reminderNumOption: []
      })
    },
    removeOertimeData(index) {
      this.overTimeData = this.overTimeData.filter((i, ind) => index != ind)
    },
    // 批量添加模板
    batchAddTemplate() {
      this.$refs.templateLocation.type = 'template'
      this.$refs.templateLocation.getTemTree()
      this.$refs.templateLocation.batchAdd()
      this.$refs.templateLocation.templateLocationVisible = true
    },
    handleSelectionChange(val) {
      this.checkInspectionRow = val
    },
    correlationTemplate(val) {
      this.pointList = this.pointList.map((i) => {
        if (this.checkInspectionRow.length > 0) {
          this.checkInspectionRow.forEach((j) => {
            if (i.id == j.id) {
              i.projectId = val.id
              i.projectName = val.projectName
            }
          })
        } else {
          i.projectId = val.id
          i.projectName = val.projectName
        }
        return i
      })
    },
    // ----------------------- 调度终端相关事件
    // 打开调度终端弹窗
    openTerminalDialog() {
      this.addDispatchTerminalShow = true
    },
    // 删除调度终端
    deleteTerminalRow(row) {
      this.terminalList = this.terminalList.filter((i) => i.id != row.id)
    },
    // 获取子组件传递的数据
    getDispatchTerminalData(data) {
      this.terminalList = data
    },
    changeMinInterval(val) {
      this.minTimeUnit = val
    },
    customTypeTime() {
      this.customTypeVisible = true
    },
    changeCustomDay(type, index, val) {
      if (type == 'first') {
        this.customTimeTypeList[index].timeStartValue = val
        this.customTimeTypeList[index].timeEndValue = val
      } else if (type == 'second') {
        this.customTimeTypeList[index].timeEndValue = val
        if (this.customTimeTypeList[index].timeStartValue == this.customTimeTypeList[index].timeEndValue) {
          this.customTimeTypeList[index].endTime = ''
        }
      }
    },
    appCustomTimeList() {
      this.customTimeTypeList.push({
        timeStartValue: Math.max(...this.customTimeTypeList.map((i) => i.timeEndValue)) || 0,
        startTime: '',
        timeEndValue: Math.max(...this.customTimeTypeList.map((i) => i.timeEndValue)) || 0,
        endTime: ''
      })
    },
    deletCustomTimeList(index) {
      this.customTimeTypeList.splice(index, 1)
    },
    handleCustomTypeClose() {
      this.customTypeVisible = false
    },
    resetCustomType() {
      this.customTimeTypeList = [
        {
          timeStartValue: 0,
          startTime: '',
          timeEndValue: 0,
          endTime: ''
        }
      ]
    },
    customTypeConfirm() {
      if (this.customTimeTypeList.some((i) => !i.startTime || !i.endTime)) {
        this.$message.error('请完整填写自定义时间')
        return
      } else {
        const arr = this.customTimeTypeList.map((i) => {
          return {
            timeStartValue: i.timeStartValue,
            timeEndValue: i.timeEndValue,
            startTime: moment(i.startTime, 'HH:mm:ss').format('HH:mm:ss'),
            endTime: moment(i.endTime, 'HH:mm:ss').format('HH:mm:ss')
          }
        })
        this.form.customTimeJson = arr
        this.customTypeVisible = false
      }
    },
    changeCustomTime(type, index, val) {
      if (type == 'first') {
        if (!val) {
          this.customTimeTypeList[index].endTime = null
        } else {
          this.secondKey = Math.random()
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.contentMain {
  height: 100%;
  .inner {
    overflow: auto;
    margin: 15px;
    padding: 15px 60px;
    width: calc(100% - 30px);
    height: calc(100% - 82px);
    background-color: #fff;
    .baseInfo {
      height: 210px;
      border-bottom: 3px solid #f6f5fa;
      :deep(.el-form-item) {
        .el-select {
          width: 100%;
        }
        .el-cascader {
          width: 100%;
        }
      }
    }
    .executeInfo {
      border-bottom: 3px solid #f6f5fa;
      .skipDays {
        padding-top: 10px;
        padding-left: 40px !important;
        height: 62px;
        display: flex;
        justify-content: space-between;
        font-size: 14px;
      }
      .el-select {
        width: 100%;
      }
      .el-date-editor.el-input {
        width: 100%;
      }
      .el-date-editor--daterange.el-input__inner {
        width: 100%;
      }
      .titlePlus {
        :deep(.el-form-item) {
          .el-form-item__label {
            width: 110px !important;
          }
        }
      }
      :deep(.el-date-editor) {
        width: 100%;
      }
      :deep(.el-input__inner) {
        line-height: 40px !important;
      }
    }
    .overtimeInfo {
      padding-bottom: 22px;
      border-bottom: 3px solid #f6f5fa;
      .overtimeItem {
        display: flex;
        align-items: center;
        padding: 5px 0;
        .titleText {
          width: 80px;
        }
        .titleText::before {
          content: '*';
          color: #f56c6c;
          margin-right: 4px;
        }
        .hour {
          :deep(.el-input__inner) {
            padding: 0 8px;
          }
        }
      }
    }
    .InspectionPointInfo {
      .headerTop {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .requiredIcon::after {
        content: '*';
        color: #f56c6c;
        margin-left: 4px;
      }
      .contentTable {
        margin-top: 10px;
        .allocationUser {
          width: 30px;
        }
        .listTable {
          width: calc(100% - 30px);
          .operation {
            display: flex;
            justify-content: space-between;
            align-items: center;
          }
          :deep(.el-input) {
            width: 150px;
          }
        }
      }
    }
    .taskPreview {
      width: 100%;
      :deep(.el-table__header) {
        width: 100% !important;
      }
      :deep(.el-table__body) {
        width: 100% !important;
      }
      :deep(.el-table__empty-block) {
        width: 100% !important;
      }
    }
  }
  .bottomBar {
    height: 52px;
    width: 100%;
    background-color: #fff;
    display: flex;
    justify-content: right;
    align-items: center;
    .bottomWrap {
      padding: 0 16px;
    }
  }
  .inspectionPointDateil {
    :deep(.el-dialog) {
      .el-dialog__header {
        border-bottom: 1px solid #dcdfe6;
      }
      .el-dialog__body {
        background-color: #f6f5fa;
        padding: 24px;
      }
      .el-dialog__footer {
        padding: 10px 20px;
      }
    }
    .detailDialog {
      background-color: #fff;
      padding: 0 16px;
      .cosRow {
        display: flex;
        align-items: center;
        padding: 12px 0;
        .titleWrap {
          color: #414653;
          width: 100px;
          text-align: right;
        }
        .contenWrap {
          color: #121f3e;
          margin-left: 14px;
        }
      }
    }
    .typeInfo {
      margin-top: 24px;
    }
  }
  .templateLocation {
    :deep(.el-dialog) {
      .el-dialog__header {
        border-bottom: 1px solid #dcdfe6;
      }
      .el-dialog__body {
        background-color: #f6f5fa;
        padding: 24px;
      }
      .el-dialog__footer {
        padding: 10px 20px;
      }
    }
  }
  /* 添加这段样式 */
  :deep(.red-star > .el-form-item__label::before) {
    content: '*';
    color: #f56c6c;
    margin-right: 4px;
  }
  .time-picker-box {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    > span {
      display: inline-block;
      width: 24px;
      font-weight: bold;
    }
  }
  .customTimeType {
    .el-input {
      width: 140px;
      :deep(.el-input__inner) {
        border-radius: 0 4px 4px 0 !important;
      }
    }
    .separator {
      display: inline-block;
      margin: 0 10px;
    }
    .icon-box {
      width: 70px;
      display: flex;
      align-items: center;
      i {
        margin-left: 10px;
        font-size: 18px;
        font-weight: bold;
      }
      .el-icon-delete {
        color: #f56c6c;
      }
    }
  }
  .minInterval {
    width: 80%;
    cursor: pointer;
    :deep(.el-input-group__append) {
      padding: 0;
      background-color: #3562db !important;
      border: 1px solid #3562db !important;
    }
  }
  :deep(.el-dropdown-selfdefine) {
    width: 90px;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: baseline;
    padding: 5px 10px;
  }
  .customDropdown {
    cursor: pointer;
    > span {
      width: 90px;
      color: #fff;
      display: flex;
      justify-content: center;
      align-items: baseline;
      padding: 5px 10px;
    }
    height: 32px;
    background-color: #3562db;
    border-radius: 4px 0 0 4px;
  }
}
</style>
<style>
.el-dropdown-menu {
  max-height: 300px;
  overflow: auto;
}
</style>
