<template>
  <PageContainer>
    <div slot="header">
      <div class="searchForm">
        <div class="search-box">
          <el-select v-model="filterInfo.alarmSystemCode" placeholder="请选择报警系统" class="ml-16" @change="alarmSystemChange">
            <el-option v-for="item in alarmSystemList" :key="item.imhMonitorCode" :label="item.imhMonitorName" :value="item.imhMonitorCode"></el-option>
          </el-select>
          <el-select v-model="filterInfo.alarmType" placeholder="请选择报警类型" :disabled="!filterInfo.alarmSystemCode" class="ml-16">
            <el-option v-for="item in alarmTypeList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
          <el-date-picker
            v-model="timeLine"
            type="daterange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            class="ml-16"
          ></el-date-picker>
          <el-input v-model="filterInfo.destinationNumber" placeholder="请输入消息接收终端号码" class="ml-16"></el-input>
          <div class="ml-16">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button type="primary" plain @click="reset">重置</el-button>
          </div>
        </div>
      </div>
    </div>
    <div slot="content" ref="contentRef" class="table-box">
      <div class="tableContainer">
        <el-table v-loading="tableLoading" height="calc(100% - 56px)" :data="tableData" border stripe>
          <el-table-column type="selection" width="55"> </el-table-column>
          <el-table-column prop="alarmTypeVal" label="报警类型" width="180" align="center"></el-table-column>
          <el-table-column prop="alarmTime" label="报警时间" align="center"></el-table-column>
          <el-table-column prop="destinationNumber" label="消息接收终端号码" align="center">
            <template slot-scope="scope">
              <span>{{ getRowDataByCallRecordList(scope.row.callRecordList, 'destinationNumber') }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="destinationNumberState" label="终端是否确认" align="center">
            <template slot-scope="scope">
              <span>{{ getRowDataByCallRecordList(scope.row.callRecordList, 'destinationNumberState') }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="startTime" label="确认时间" align="center">
            <template slot-scope="scope">
              <span>{{ getRowDataByCallRecordList(scope.row.callRecordList, 'startTime') }}</span>
            </template>
          </el-table-column>
          <el-table-column width="280" label="操作" align="center">
            <template slot-scope="scope">
              <div class="btns">
                <span @click="handleOperation('detail', scope.row)"> 详情 </span>
                <span @click="handleOperation('play', scope.row)"> 播放 </span>
                <span @click="handleOperation('download', scope.row)"> 下载 </span>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :current-page="pagination.pageNum"
          :page-sizes="[15, 30, 50, 100]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageTotal"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
      <!--报警记录 -->
      <template v-if="alarmRecordDialogShow">
        <SelectAlarmRecordDialog :alarmRecordDialogShow="alarmRecordDialogShow" :itemInfo="alarmInfo" @closeAlarmRecordDialog="closeAlarmRecordDialog" />
      </template>
      <!--文件 -->
      <template v-if="callFileDialogShow">
        <SelectCallFileDialog
          :callFileDialogShow="callFileDialogShow"
          :callFileList="fileList"
          :fileTitle="fileTitle"
          @closeFileDialog="closeFileDialog"
          @downLoadCallFile="downLoadCallFile"
        />
      </template>
      <!--播放 -->
      <template v-if="alarmPlayDialogShow">
        <SelectAlarmPlayDialog :alarmPlayDialogShow="alarmPlayDialogShow" :itemInfo="alarmInfo" @closeAlarmPlayDialog="closeAlarmPlayDialog" />
      </template>
    </div>
  </PageContainer>
</template>
<script>
import SelectAlarmRecordDialog from './components/alarmDetail.vue'
import SelectCallFileDialog from '../callRecords/components/callFile.vue'
import SelectAlarmPlayDialog from './components/playDialog.vue'
import store from '@/store/index'
import axios from 'axios'
export default {
  name: 'alarmRecord',
  components: {
    SelectAlarmRecordDialog,
    SelectCallFileDialog,
    SelectAlarmPlayDialog
  },
  data() {
    return {
      tableLoading: false,
      dialogVisible: false,
      alarmRecordDialogShow: false, // 报警纪录
      callFileDialogShow: false, // 文件列表
      alarmPlayDialogShow: false, // 播放
      tableData: [],
      filterInfo: {
        alarmType: '',
        startTime: '',
        endTime: '',
        destinationNumber: ''
      },
      alarmSystemList: [],
      alarmTypeList: [],
      timeLine: [],
      pagination: {
        pageSize: 15,
        pageNum: 1
      },
      pageTotal: 0,
      alarmInfo: {}, // 报警详情
      fileList: []
    }
  },
  mounted() {
    this.getTableData()
    this.getAlarmSystemList()
  },
  methods: {
    // 报警系统
    getAlarmSystemList() {
      let data = {
        projectCodes: ''
      }
      this.$api.getAlarmSystem(data, {}, __PATH.VUE_IEMC_API).then((res) => {
        if (res.code == 200) {
          this.alarmSystemList = res.data
        }
      })
    },
    // 报警系统改变
    alarmSystemChange(el) {
      this.getDictionaryList(el)
    },
    // 获取实体类型
    getDictionaryList(projectCode) {
      let params = {
        dictType: 1,
        projectCode: projectCode
      }
      this.$api.getDictionaryList(params).then((res) => {
        if (res.code == 200) {
          this.alarmTypeList = res.data
        }
      })
    },
    getTableData() {
      this.tableLoading = true
      let data = {
        ...this.pagination,
        ...this.searchForm
      }
      this.$api
        .getAlarmRecordData(data)
        .then((res) => {
          this.tableLoading = false
          if (res.code == 200) {
            this.tableData = res.data ? res.data.records : []
            this.pageTotal = res.data ? res.data.total : 0
          } else if (res.message) {
            this.tableData = []
            this.pageTotal = 0
            this.$message.error(res.message)
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    getRowDataByCallRecordList(callList, field) {
      if (callList && callList.length) {
        const callListMap = callList.map((item) => item[field])
        // 获取状态通过mapjosn特殊处理
        if (field == 'destinationNumberState') {
          return callList
            .map((item) => {
              const destinationNumberMap = JSON.parse(item.destinationNumberMap)
              const stateMap = destinationNumberMap.map((item) => item.state)
              // 有一个接通则视为接通，全部未接通则为未接通
              return stateMap.some((e) => e == 1) ? '接通' : '未接通'
            })
            .join('\n')
        }
        return callListMap.join('\n')
      } else {
        return '-'
      }
    },
    search() {
      this.pagination.pageNum = 1
      this.getTableData()
    },
    reset() {
      this.filterInfo = {
        type: '',
        startTime: '',
        endTime: '',
        destinationNumber: ''
      }
      this.timeLine = []
      this.pagination.pageNum = 1
      this.pageTotal = 0
      this.getTableData()
    },
    handleCurrentChange(val) {
      this.pagination.pageNum = val
      this.getTableData()
    },
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.pagination.pageNum = 1
      this.getTableData()
    },
    handleOperation(type, row) {
      if (type === 'detail') {
        this.alarmInfo = JSON.parse(JSON.stringify(row))
        this.alarmRecordDialogShow = true
      } else if (type === 'play') {
        this.alarmInfo = JSON.parse(JSON.stringify(row))
        this.alarmPlayDialogShow = true
      } else {
        if (row.callRecordList[0]) {
          if (row.callRecordList[0].callType == '7') {
            this.fileTitle = '录像'
          } else if (row.callRecordList[0].callType == '1' || row.callRecordList[0].callType == '2') {
            this.fileTitle = '录音'
          }
          let params = {
            uuid: row.callRecordList[0].uuid,
            callType: row.callRecordList[0].callType
          }
          this.$api.fileQuery(params).then((res) => {
            if (res.code == '200') {
              this.fileList = JSON.parse(res.data)
              this.callFileDialogShow = true
            }
          })
        } else {
          this.$message.info('暂无可下载文件')
        }
      }
    },
    // 通话纪录文件弹窗关闭
    closeFileDialog() {
      this.callFileDialogShow = false
    },
    // 下载通话文件
    downLoadCallFile(val) {
      let cacheInfo = JSON.parse(sessionStorage.getItem('LOGINDATA'))
      let params = {
        unitCode: cacheInfo.unitCode,
        hospitalCode: cacheInfo.hospitalCode,
        filePath: val.filePath
      }
      axios({
        method: 'post',
        url: __PATH.VUE_CONVERGED_COM_API + 'communicationController/callRecord/fileDownload',
        data: params,
        responseType: 'blob',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + store.state.user.token
        }
      })
        .then((res) => {
          let name = val.file
          let blob = new Blob([res.data]) // { type: "application/vnd.ms-excel" }
          let url = window.URL.createObjectURL(blob) // 创建一个临时的url指向blob对象
          // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
          let a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
          // 释放这个临时的对象url
          window.URL.revokeObjectURL(url)
        })
        .catch((res) => {
          this.$message.error('导出失败！')
        })
    },
    closeAlarmRecordDialog() {
      this.alarmRecordDialogShow = false
    },
    // 播放关闭
    closeAlarmPlayDialog() {
      this.alarmPlayDialogShow = false
    }
  }
}
</script>
<style lang="scss" scoped>
.searchForm {
  display: flex;
  height: 80px;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  .el-input {
    width: 200px;
    margin-left: 16px;
  }
}
.search-box {
  display: flex;
  align-items: center;
}
.container-content > div {
  height: 100%;
  margin-top: 16px;
}
.el-table {
  margin-bottom: 12px;
}
::v-deep .el-table__row:hover {
  cursor: pointer;
}
.table-box {
  background-color: #fff;
  height: 100%;
  padding: 16px;
  .tableContainer {
    height: 100%;
    .el-table {
      height: calc(100% - 56px) !important;
    }
  }
}
.diaContent {
  width: 100%;
  max-height: 500px !important;
  overflow: auto;
  background-color: #fff !important;
  .el-input,
  .el-select,
  .el-cascader {
    width: 300px;
  }
}
.ml-16 {
  margin-left: 16px;
}
.btns {
  span {
    color: #3562db;
    cursor: pointer;
    margin-right: 8px;
  }
}
.online {
  color: #33cc00;
}
.offOnline {
  color: #ff0000;
}
::v-deep .el-table {
  .cell {
    white-space: pre-line;
    text-align: center;
  }
}
</style>
