<!-- 环境监测弹窗 -->
<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    title="环境监测"
    width="65%"
    :visible.sync="visible"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <div class="content">
      <div class="content-heade">
        <el-date-picker
          v-model="searchFrom.dataRange"
          type="daterange"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          :picker-options="pickerOptions"
          :clearable="false"
        />
        <div style="display: inline-block;">
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="searchForm">查询</el-button>
        </div>
      </div>
      <div v-loading="loading" class="content-list">
        <div v-for="(item, index) in envirData" :key="item.paramName" class="list-item">
          <p class="item-title">{{ item.paramName }}</p>
          <p class="item-value">
            <span class="item-num" :style="{ color: item.name ? item.colour : '#121F3E' }">{{ (item.number || '-') + (item.paramUnit || '') }}</span>
            <span v-if="item.name" class="item-info" :style="{ backgroundColor: item.colour }">{{ item.name }}</span>
          </p>
          <div class="item-chart">
            <echarts :ref="'chart' + index" :domId="'chart' + index" />
          </div>
        </div>
      </div>
    </div>
    <span slot="footer">
      <el-button plain type="primary" @click="closeDialog">关 闭</el-button>
    </span>
  </el-dialog>
</template>
<script>
import moment from 'moment'
moment.locale('zh-cn')
export default {
  name: 'alarmStatisticsDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    requestInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      loading: false,
      searchFrom: {
        dataRange: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')] // 时间范围
      },
      pickerOptions: {
        shortcuts: [
          {
            text: '今日',
            onClick(picker) {
              picker.$emit('pick', [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')])
            }
          },
          {
            text: '本月',
            onClick(picker) {
              picker.$emit('pick', [moment().startOf('month').format('YYYY-MM-DD'), moment().endOf('month').format('YYYY-MM-DD')])
            }
          },
          {
            text: '本年',
            onClick(picker) {
              picker.$emit('pick', [moment().startOf('year').format('YYYY-MM-DD'), moment().endOf('year').format('YYYY-MM-DD')])
            }
          }
        ]
      },
      envirData: []
    }
  },
  computed: {},
  created() {
    this.getDataList()
  },
  methods: {
    // 查看详情
    getDataList() {
      let params = {
        startTime: this.searchFrom.dataRange[0],
        endTime: this.searchFrom.dataRange[1],
        easyOrInfo: 1,
        ...this.requestInfo
      }
      this.$api.GetEnvironmentMonitor(params).then((res) => {
        if (res.code == 200) {
          this.envirData = res.data
          this.$nextTick(() => {
            this.envirData.forEach((item, index) => {
              this.$refs['chart' + index][0].init(this.chartOptions(item))
            })
          })
        }
      })
    },
    chartOptions(item) {
      let option
      if (item.list && item.list.length) {
        option = {
          backgroundColor: '#fff',
          color: '#3562DB',
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              crossStyle: {
                color: '#999'
              },
              lineStyle: {
                type: 'dashed'
              }
            }
          },
          dataZoom: [
            {
              type: 'inside',
              start: 0,
              end: 100
            }
          ],
          grid: {
            left: '5',
            right: '5',
            bottom: '5',
            top: '10',
            containLabel: true
          },
          xAxis: {
            type: 'time',
            boundaryGap: false,
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            }
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              color: '#999',
              // rotate: 30,
              textStyle: {
                fontSize: 12
              }
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#F3F4F4'
              }
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            }
          },
          series: [
            {
              name: item.paramName + item.paramUnit,
              type: 'line',
              lineStyle: {
                width: 1
              },
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                  {
                    offset: 0,
                    color: 'hsla(225, 63%, 54%, 0)'
                  },
                  {
                    offset: 1,
                    color: 'hsla(225, 63%, 54%, .3)'
                  }
                ])
              },
              data: item.list.map((v) => [v.time, v.parameterValue])
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      return option
    },
    // 重置查询
    resetForm() {
      Object.assign(this.$data.searchFrom, this.$options.data().searchFrom)
      this.searchForm()
    },
    // 查询
    searchForm() {
      this.getDataList()
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', !this.visible)
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 0;
  max-height: calc(88vh - 110px);
}

.content {
  width: 100%;
  height: 100%;

  .content-heade {
    padding: 10px;
    background: #fff;

    & > div {
      margin-right: 10px;
    }
  }

  .content-list {
    width: 100%;
    padding: 8px 24px 24px 8px;
    display: flex;
    flex-wrap: wrap;

    p {
      margin: 0;
    }

    .list-item {
      margin: 16px 0 0 16px;
      width: calc(100% / 3 - 16px);
      background: #fff;
      border-radius: 4px;
      padding: 16px;

      .item-title {
        font-size: 14px;
        font-weight: 400;
        color: #414653;
        line-height: 14px;
      }

      .item-value {
        margin-top: 10px;
        padding-bottom: 24px;

        .item-num {
          font-size: 15px;
          font-weight: 500;
          line-height: 20px;
        }

        .item-info {
          display: inline-block;
          padding: 3px 6px;
          color: #fff;
          font-size: 14px;
          font-weight: 500;
          line-height: 14px;
          border-radius: 4px;
          margin-left: 10px;
        }
      }

      .item-chart {
        height: 230px;
      }
    }
  }
}

::v-deep .model-dialog {
  padding: 0 !important;
  margin-top: 8vh !important;
}
</style>
