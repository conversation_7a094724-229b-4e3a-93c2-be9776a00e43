<template>
  <div class="tableContainer">
    <el-button type="primary" @click="addCycle()">添加周期规则</el-button>
    <el-table v-loading="tableLoading" :data="list" height="calc(100% - 96px)" border stripe style="width: 100%; margin-top: 16px">
      <el-table-column prop="name" label="名称" show-overflow-tooltip :resizable="false"> </el-table-column>
      <el-table-column prop="defaultState" label="来源" show-overflow-tooltip :resizable="false">
        <template slot-scope="scope">
          <span>{{ scope.row.defaultState === 0 ? '默认' : '自定义' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="scopeContent" label="范围内容" show-overflow-tooltip :resizable="false"> </el-table-column>
      <el-table-column label="操作" show-overflow-tooltip :resizable="false">
        <template slot-scope="scope">
          <el-button type="text" @click="view(scope.row.id)">查看</el-button>
          <el-button type="text" :disabled="scope.row.defaultState === 0" @click="addCycle(scope.row)">编辑</el-button>
          <el-button type="text" :disabled="scope.row.defaultState === 0" @click="del(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      style="margin-top: 16px"
      :current-page="pagination.page"
      :page-sizes="[15, 30, 50, 100]"
      :page-size="pagination.size"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pagination.total"
      @size-change="(val) => paginatChange('pageSize', val)"
      @current-change="(val) => paginatChange('page', val)"
    >
    </el-pagination>
  </div>
</template>
<script>
export default {
  name: 'levelList',
  props: {
    type: {
      type: String,
      default: '0'
    }
  },
  data() {
    return {
      tableLoading: false,
      list: [],
      pagination: {
        pageSize: 15,
        page: 1,
        total: 0
      }
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    addCycle(row) {
      this.$router.push({ path: '/targetConfig/updateCycleRules', query: { id: row ? row.id : '', type: this.type } })
    },
    view(id) {
      this.$router.push({ path: '/targetConfig/cycleRulesDetail', query: { id: id, type: this.type } })
    },
    del(row) {
      this.$confirm('是否确认删除本条周期规则?', '提示', {
        cancelButtonClass: 'el-button--primary is-plain',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$api.delTargetConfig({ cycleLevel: this.type, id: row.id }, { 'operation-type': 3, 'operation-id': row.id, 'operation-name': row.name}).then((res) => {
            if (res.code === '200') {
              this.$message.success('删除成功')
              this.getList()
            }
          })
        })
        .catch(() => {})
    },
    getList() {
      this.$api
        .targetConfigList({
          cycleLevel: this.type,
          ...this.pagination
        })
        .then((res) => {
          if (res.code === '200') {
            this.list = res.data.records
            this.pagination.total = res.data.total
          }
        })
    },
    paginatChange(name, val) {
      this.pagination[name] = val
      if (name === 'pageSize') {
        this.pagination.page = 1
      }
      this.getList()
    }
  }
}
</script>
<style lang="scss" scoped>
.tableContainer {
  width: 100%;
  height: 100%;
  padding-top: 16px;
}
</style>
