<template>
  <!-- v-if="item.meta.sidebar !== false" -->
  <div
    :class="{
      'sidebar-item': true
    }"
  >
    <yx-submenu
      v-if="!hasPath"
      :title="generateI18nTitle(item.meta.i18n, item.meta.title)"
      :index="JSON.stringify(item)"
      :class="{
        'el-menu-title-zero': item.meta.arrangement == 'column' && !$store.state.settings.sidebarCollapse
      }"
      @opened-change="openedchanged"
    >
      <template slot="title">
        <div class="item">
          <template v-if="item.meta.icon?.includes('http') || item.meta.activeIcon?.includes('http')">
            <img v-if="item.meta.icon && item.index != $store.state.menu.headerActived" class="icon" :src="item.meta.icon" alt="" />
            <span v-if="item.index != $store.state.menu.headerActived">{{ item.meta.title }} </span>
            <img v-else :name="item.meta.activeIcon" class="icon" :src="item.meta.activeIcon" alt="" />
          </template>
          <template v-else>
            <svg-icon v-if="item.meta.icon && item.index != $store.state.menu.headerActived" :name="item.meta.icon" class="icon" />
            <span v-if="item.index != $store.state.menu.headerActived">{{ item.meta.title }} </span>
            <svg-icon v-else :name="item.meta.activeIcon" class="icon" style="font-size: 30px" />
          </template>
        </div>
      </template>
      <div v-for="route in item.children" :key="route.path" class="roll">
        <SidebarItem v-if="route.meta.sidebar !== false" :item="route" :base-path="resolvePath(route.path)" />
      </div>
    </yx-submenu>
    <template v-else>
      <router-link v-if="!hasChildren" v-slot="{ href, navigate, isActive, isExactActive }" custom :to="resolvePath(item.path)">
        <a
          :href="isExternal(resolvePath(item.path)) ? resolvePath(item.path) : href"
          :class="[isActive && 'router-link-active', isExactActive && 'router-link-exact-active']"
          :target="isExternal(resolvePath(item.path)) ? '_blank' : '_self'"
          @click="navigate"
        >
          <el-menu-item
            :title="generateI18nTitle(item.meta.i18n, item.meta.title)"
            :index="resolvePath(item.path)"
            :class="!$store.state.settings.sidebarCollapse ? item.meta.classType : ''"
            @click="goChildData"
          >
            <template v-if="item.meta.icon?.includes('http') || item.meta.activeIcon?.includes('http')">
              <img
                v-if="iconName(isActive || isExactActive, item.meta.icon, item.meta.activeIcon)"
                class="icon"
                :src="iconName(isActive || isExactActive, item.meta.icon, item.meta.activeIcon)"
                alt=""
              />
            </template>
            <template v-else>
              <svg-icon
                v-if="iconName(isActive || isExactActive, item.meta.icon, item.meta.activeIcon)"
                :name="iconName(isActive || isExactActive, item.meta.icon, item.meta.activeIcon)"
                class="icon"
              />
            </template>
            <!-- 收起的最后一级菜单 -->
            <span>{{ generateI18nTitle(item.meta.i18n, item.meta.title) }} </span>
            <span
              v-if="badge(item.meta.badge).visible"
              :class="{
                badge: true,
                'badge-dot': badge(item.meta.badge).type == 'dot',
                'badge-text': badge(item.meta.badge).type == 'text'
              }"
              >{{ badge(item.meta.badge).value }}</span
            >
          </el-menu-item>
        </a>
      </router-link>
      <router-link v-else v-slot="{ isActive, isExactActive }" custom :to="resolvePath(item.path)">
        <el-submenu
          :title="generateI18nTitle(item.meta.i18n, item.meta.title)"
          :index="resolvePath(item.path)"
          :class="{
            'el-menu-title-zero': item.meta.arrangement == 'column' && !$store.state.settings.sidebarCollapse
          }"
        >
          <template slot="title">
            <template v-if="item.meta.icon?.includes('http') || item.meta.activeIcon?.includes('http')">
              <img
                v-if="iconName(isActive || isExactActive, item.meta.icon, item.meta.activeIcon)"
                class="icon"
                :src="iconName(isActive || isExactActive, item.meta.icon, item.meta.activeIcon)"
                alt=""
              />
            </template>
            <template v-else>
              <svg-icon
                v-if="iconName(isActive || isExactActive, item.meta.icon, item.meta.activeIcon)"
                :name="iconName(isActive || isExactActive, item.meta.icon, item.meta.activeIcon)"
                class="icon"
              />
            </template>
            <span>{{ generateI18nTitle(item.meta.i18n, item.meta.title) }}</span>
            <span
              v-if="badge(item.meta.badge).visible"
              :class="{
                badge: true,
                'badge-text': badge(item.meta.badge).type == 'text',
                'badge-dot': badge(item.meta.badge).type == 'dot'
              }"
              >{{ badge(item.meta.badge).value }}</span
            >
          </template>
          <template v-for="route in item.children">
            <SidebarItem v-if="route.meta.sidebar !== false" :key="route.path" :item="route" :base-path="resolvePath(item.path)" />
          </template>
        </el-submenu>
      </router-link>
    </template>
  </div>
</template>
<script>
import path from 'path-browserify'
import { mapMutations } from 'vuex'
import { childAppWhite } from '@/util/dict.js'
import { deepClone } from '@/util'
export default {
  name: 'SidebarItem',
  inject: ['generateI18nTitle'],
  props: {
    item: {
      type: Object,
      required: true
    },
    basePath: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      childAppWhite,
      colActiveMenu: '',
      columnRoute: []
    }
  },
  components: {
    yxSubmenu: () => import('../yxSubmenu')
  },
  computed: {
    hasChildren() {
      let flag = true
      if (this.item.children) {
        if (this.item.children.every((item) => item.meta.sidebar === false)) {
          flag = false
        }
      } else {
        flag = false
      }
      return flag
    },
    hasPath() {
      return (this.item.path || this.item.name) ?? false
    }
  },
  created() {},
  mounted() {},
  methods: {
    openedchanged(val) {
      if (val && val.show) {
        let index = val.index ? JSON.parse(val.index) : ''
        if (index && index.meta.menuAuth === '/rentalHousing') {
          let oldRoutes = deepClone(this.$store.state.menu.routes)
          const routeModule = oldRoutes.find((route) => route.meta.menuAuth === index.meta.menuAuth)
          const route = routeModule.children.find((route) => route.name === 'rentalHousingApproveManage')
          if (route) {
            this.$api.rentalHousingApi.getApproveCount({ queryType: '0', userId: this.$store.state.user.userInfo.user.staffId }).then((res) => {
              if (res.code == 200) {
                route.meta.badge = res.data.todo ? Number(res.data.todo) : 0
                this.$store.commit('menu/setRoutes', oldRoutes)
              }
            })
          }
        }
      }
    },
    ...mapMutations(['childAppsData/setPath']),
    colMenuEvent(item, path, isActive, isExactActive) {
      this.colActiveMenu = path
      this.columnRoute = item.children ?? []
    },
    isExternal(path) {
      return /^(https?:|mailto:|tel:)/.test(path)
    },
    resolvePath(routePath) {
      if (this.isExternal(routePath)) {
        return routePath
      }
      if (this.isExternal(this.basePath)) {
        return this.basePath
      }
      return path.resolve(this.basePath, routePath)
    },
    iconName(isActive, icon, activeIcon) {
      let name = ''
      if ((!isActive && icon) || (isActive && !activeIcon)) {
        name = icon
      } else if (isActive && activeIcon) {
        name = activeIcon
      }
      return name
    },
    badge(badge) {
      let res = {
        type: '', // text or dot
        value: '',
        visible: false
      }
      if (badge) {
        res.visible = true
        res.value = typeof badge == 'function' ? badge() : badge
        if (typeof res.value == 'boolean') {
          res.type = 'dot'
          if (!res.value) {
            res.visible = false
          }
        } else if (typeof res.value == 'number') {
          res.type = 'text'
          if (res.value <= 0) {
            res.visible = false
          }
        } else {
          res.type = 'text'
          if (!res.value) {
            res.visible = false
          }
        }
      }
      return res
    },
    // 跳转子应用需要发送的数据
    goChildData() {
      let currentPath = ''
      if (this.childAppWhite.includes(this.item.path)) {
        currentPath = this.item.path
      } else if (this.childAppWhite.includes(this.item.name)) {
        currentPath = this.item.name
      }
      if (currentPath) {
        this.$store.commit('childAppsData/setChildAppInfo', { parentName: this.basePath, currentPath: currentPath })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.roll {
  ::v-deep .el-menu--vertical {
    max-height: 70vh;
    overflow-y: auto;
  }
}
.sidebar-item-inline {
  display: inline-block;
}
.el-menu-title-zero {
  ::v-deep .el-submenu__title {
    height: 0;
  }
}
::v-deep .el-menu-item,
::v-deep .el-menu-item span,
::v-deep .el-submenu__title,
::v-deep .el-submenu__title span {
  vertical-align: inherit;
  @include text-overflow;
}
::v-deep .el-menu-item,
::v-deep .el-submenu__title {
  display: flex;
  align-items: center;
}
::v-deep .el-submenu,
::v-deep .el-menu-item {
  .icon {
    width: 20px;
    font-size: 20px;
    margin-right: 10px;
    vertical-align: -0.25em;
    transition: transform 0.3s;
    color: unset;
    &[class^='el-icon-'] {
      vertical-align: middle;
    }
  }
  &:hover > .icon,
  .el-col:hover > .icon,
  .el-submenu__title:hover > .icon {
    transform: scale(1.2);
  }
}
a {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}
.badge {
  position: absolute;
  z-index: 1;
  @include themeify {
    background-color: themed('g-badge-bg');
    box-shadow: 0 0 0 1px themed('g-badge-border-color');
  }
  @include position-center(y);
  &-dot {
    right: 15px;
    text-indent: -9999px;
    border-radius: 50%;
    width: 6px;
    height: 6px;
  }
  &-text {
    right: 15px;
    border-radius: 10px;
    font-size: 12px;
    height: 18px;
    line-height: 18px;
    padding: 0 6px;
    text-align: center;
    white-space: nowrap;
    @include themeify {
      color: themed('g-badge-color');
    }
  }
}
::v-deep .el-submenu__title {
  padding-right: 45px;
  > .badge {
    &-dot {
      right: 40px;
    }
    &-text {
      right: 40px;
    }
  }
}
</style>
<style lang="scss">
.el-menu--vertical {
  max-height: calc(100vh - 8px);
  overflow-y: auto;
}
.el-menu--vertical::-webkit-scrollbar {
  display: none;
}
.el-menu--inline {
  @include themeify {
    background-color: themed('g-sub-sidebar-menu-bg') !important;
  }
  .el-menu-item,
  .el-submenu > .el-submenu__title {
    @include themeify {
      color: themed('g-sub-sidebar-menu-color');
      background-color: themed('g-sub-sidebar-menu-bg') !important;
    }
    &:hover {
      @include themeify {
        color: themed('g-sub-sidebar-menu-hover-color') !important;
        background-color: themed('g-sub-sidebar-menu-hover-bg') !important;
      }
    }
  }
}
.el-menu-item,
.el-submenu__title {
  @include themeify {
    color: themed('g-sub-sidebar-menu-color');
  }
  &:hover {
    @include themeify {
      color: themed('g-sub-sidebar-menu-hover-color') !important;
      background-color: themed('g-sub-sidebar-menu-hover-bg') !important;
    }
  }
}
.el-menu--popup {
  @include themeify {
    background-color: themed('g-sub-sidebar-bg');
  }
}
.el-menu-item.is-active,
.el-submenu .el-menu--inline .el-menu-item.is-active {
  @include themeify {
    color: themed('g-sub-sidebar-menu-active-color') !important;
    background-color: themed('g-sub-sidebar-menu-active-bg') !important;
  }
}
.el-menu--collapse .el-submenu.is-active > .el-submenu__title {
  @include themeify {
    color: themed('g-sub-sidebar-menu-active-color') !important;
    background-color: themed('g-sub-sidebar-menu-active-bg') !important;
  }
}
.menu-item-center {
  display: flex !important;
  justify-content: center !important;
  background: #f6f5fa;
  border-radius: 4px;
  width: 90%;
  margin: 0 auto;
}
.el-menu--vertical {
  .el-menu--popup {
    max-width: $g-sub-sidebar-width;
    @include themeify {
      background-color: #121f3e;
      border-radius: 4px;
    }
  }
  .el-menu-item,
  .el-submenu__title {
    @include themeify {
      color: #fff !important;
    }
    &:hover {
      @include themeify {
        color: #fff !important;
        background: #3562db !important;
      }
    }
  }
}
</style>
