<template>
  <el-dialog title="监测参数配置" width="60%" :visible.sync="dialogShow" :close-on-click-modal="false" custom-class="model-dialog" :before-close="closeDialog">
    <div class="dialog-content">
      <div class="table_box">
        <div class="search_box">
          <el-input v-model="searchForm.imsName" clearable placeholder="监测实体名称" style="width: 200px" @input="nameInputEvent"></el-input>
          <el-select v-model="searchForm.projectCode" filterable placeholder="监测系统" @change="nameInputEvent">
            <el-option v-for="item in sysList" :key="item.imhMonitorCode" :label="item.imhMonitorName" :value="item.imhMonitorCode"> </el-option>
          </el-select>
        </div>
        <!-- <div class="table_div"> -->
        <TablePage
          v-loading="tableLoading"
          class="table_div"
          row-key="id"
          :showPage="false"
          :tableColumn="tableColumn"
          :data="tableData"
          height="calc(100%)"
          @selection-change="handleSelectionChange"
        >
        </TablePage>
        <!-- </div> -->
      </div>
      <div class="parameter_box">
        <span class="require-span"><span>*</span> 监测参数:</span>
        <div class="check_box_list">
          <el-radio-group v-model="paramRadio">
            <el-radio v-for="(item, index) in parameterList" :key="index" :label="item.parameterId">{{ item.parameterName }}</el-radio>
          </el-radio-group>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="groupSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script lang="jsx">
import { debounce } from 'lodash/function'
export default {
  name: 'selectMonitorParmaDialog',
  props: {
    dialogShow: {
      type: Boolean,
      default: false
    },
    dialogData: {
      type: Object,
      default: () => {}
    },
    // 选中类型为 单选还是多选
    harvesterCheckType: {
      type: String,
      default: 'radio'
    }
  },
  data() {
    return {
      sysList: [], // 监测系统
      parameterList: [], // 监测参数checkBox列表
      searchForm: {
        projectCode: '',
        imsName: ''
      },
      tableData: [],
      tableLoading: false,
      checkRadio: '',
      radioCheckData: {}, // 选中数据
      multipleCheckData: [], // 多选数据
      paramRadio: '' // 监测参数单选
    }
  },
  computed: {
    tableColumn() {
      return [
        {
          label: '',
          width: 80,
          align: 'center',
          render: (h, row) => {
            return (
              <el-radio
                v-model={this.checkRadio}
                label={row.row.imsCode}
                onChange={() => {
                  this.harvesterListRadioCheck(row.row)
                }}
              >
                &nbsp;
              </el-radio>
            )
          },
          hasJudge: this.harvesterCheckType == 'radio'
        },
        {
          type: 'selection',
          align: 'center',
          width: 80,
          hasJudge: this.harvesterCheckType == 'checkbox'
        },
        {
          prop: 'imsName',
          label: '监测实体名称'
        },
        {
          prop: 'imsCode',
          label: '监测实体编码'
        }
      ]
    }
  },
  mounted() {
    this.getSystemList()
    // 编辑带参数及回显
    if (this.dialogData.projectCode) {
      this.searchForm.projectCode = this.dialogData.projectCode
      this.checkRadio = this.dialogData?.imsCode ?? ''
      this.paramRadio = this.dialogData?.paramId ?? ''
      this.getSurveryAndParam()
    }
  },
  methods: {
    nameInputEvent: debounce(function () {
      this.getSurveryAndParam()
    }, 800),
    // 列表单选
    harvesterListRadioCheck(currentRow) {
      this.radioCheckData = currentRow
      this.radioCheckData.paramList = []
      for (const key in this.radioCheckData.paramMap) {
        this.radioCheckData.paramList.push({
          parameterName: key,
          parameterId: currentRow.paramMap[key]
        })
      }
      this.parameterList = this.radioCheckData.paramList
    },
    // 列表多选
    handleSelectionChange(val) {
      this.multipleCheckData = val
    },
    closeDialog() {
      this.$emit('closeDialog')
    },
    groupSubmit() {
      if (Object.keys(this.radioCheckData).length || this.multipleCheckData.length) {
        if (this.paramRadio) {
          let paramsItem = this.radioCheckData.paramList.find((item) => item.parameterId == this.paramRadio)
          this.$emit('submitDialog', {
            imsCode: this.radioCheckData.imsCode,
            imsName: this.radioCheckData.imsName,
            imsNo: this.radioCheckData.imsNo,
            paramId: paramsItem.parameterId,
            paramName: paramsItem.parameterName
          })
        } else {
          this.$message({
            message: '请选择监测参数！',
            type: 'warning'
          })
        }
      } else {
        this.$message({
          message: '请选择监测实体！',
          type: 'warning'
        })
      }
    },
    getSurveryAndParam() {
      this.tableLoading = true
      this.$api
        .GetSurveryAndParamByProjectCode(this.searchForm)
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data
            if (this.checkRadio) {
              // 单选
              console.log(this.tableData.find((item) => item.imsCode == this.checkRadio))
              this.harvesterListRadioCheck(this.tableData.find((item) => item.imsCode == this.checkRadio))
            }
          }
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    // 获取监测系统
    getSystemList() {
      let data = {
        projectCodes: ''
      }
      this.$api.getAlarmSystem(data, {}).then((res) => {
        if (res.code == 200) {
          this.sysList = res.data
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog__body {
  max-height: calc(78vh - 110px);
  height: calc(85vh - 110px);
  display: flex;
  padding: 15px 10px;
  overflow: auto;
}
::v-deep .model-dialog {
  margin-top: 8vh !important;
  .dialog-content {
    // flex: 1;
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .table_box,
    .parameter_box {
      padding: 10px 20px 10px 10px;
      background: #fff;
      .require-span {
        span {
          color: #fa403c;
          vertical-align: middle;
        }
      }
    }
    .parameter_box {
      margin-top: 15px;
      height: 120px;
      overflow: auto;
      .check_box_list {
        padding: 10px;
        .el-checkbox {
          margin-bottom: 5px;
        }
      }
    }
    .table_box {
      flex: 1;
      // height: calc(100% - 200px);
      height: 0;
      display: flex;
      flex-direction: column;
      .search_box {
        & > div {
          margin-bottom: 10px;
          margin-right: 10px;
        }
      }
      .table_div {
        // height: calc(100%);
        flex: 1;
        height: 0;
      }
    }
  }
}
</style>
