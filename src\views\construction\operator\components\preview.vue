<template>
  <el-dialog v-dialogDrag custom-class="model-dialog" :modal="false" :close-on-click-modal="false" title="预览" :visible="visible" :diaWidth="diaWidth" :before-close="dialogClose">
    <div class="dialog-content">
      <div style="display: flex; justify-content: center">
        <img v-for="(item, index) in list" :key="index" :src="item" :width="width" :height="height" />
      </div>
    </div>
  </el-dialog>
</template>
<script>
export default {
  model: {
    prop: 'visible',
    event: 'update:visible'
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    list: {
      type: Array,
      default: () => []
    },
    width: {
      type: String,
      default: '500'
    },
    height: {
      type: String,
      default: '350'
    },
    diaWidth: {
      type: String,
      default: '600px'
    }
  },
  methods: {
    dialogClose() {
      this.$emit('update:visible', false)
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  background: #fff;
  padding: 16px;
  img {
    padding: 10px;
  }
}
</style>
