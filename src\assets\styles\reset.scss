::-webkit-scrollbar {
  width: 13px;
  height: 13px;
}

::-webkit-scrollbar-thumb {
  background-color: rgb(0 0 0 / 40%);
  background-clip: padding-box;
  border: 3px solid transparent;
  border-radius: 7px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgb(0 0 0 / 50%);
}

::-webkit-scrollbar-track {
  background-color: transparent;
}

::-webkit-scrollbar-track:hover {
  background-color: #f8fafc;
}

html,
body {
  height: 100%;
}

body {
  margin: 0;
  color: #333;
  background-color: $g-app-bg;
  box-sizing: border-box;
  font-family: Lato, "PingFang SC", "Microsoft YaHei", sans-serif;
  -webkit-tap-highlight-color: transparent;

  &.body-hidden {
    overflow: hidden;
  }
}

* {
  box-sizing: inherit;
}

@font-face {
  font-family: PingFangSC-Medium;
  src: url(~@/assets/fonts/PingFangMedium.ttf) format("truetype");
}

@font-face {
  font-family: PingFangSC-Regular;
  src: url(~@/assets/fonts/PingFangRegular.TTF) format("truetype");
}

@font-face {
  font-family: PingFangSC-Regular-Blod;
  src: url(~@/assets/fonts/pingfangscRegular.otf) format("opentype");
}

// 右侧内容区针对fixed元素，有横向铺满的需求，可在fixed元素上设置 [data-fixed-calc-width]
[data-fixed-calc-width] {
  position: fixed;
  left: 50%;
  right: 0;
}

[data-layout="adaption"] {
  [data-fixed-calc-width] {
    width: calc(100% - #{$g-main-sidebar-width} - #{$g-sub-sidebar-width});
  }

  &[data-sidebar-no-collapse] {
    [data-fixed-calc-width] {
      transform: translateX(-50%) translateX(calc($g-main-sidebar-width / 2)) translateX(calc($g-sub-sidebar-width / 2));
    }

    &[data-no-main-sidebar] {
      [data-fixed-calc-width] {
        width: calc(100% - #{$g-sub-sidebar-width});
        transform: translateX(-50%) translateX(calc($g-sub-sidebar-width / 2));
      }
    }
  }

  &[data-sidebar-collapse] {
    [data-fixed-calc-width] {
      width: calc(100% - #{$g-main-sidebar-width} - 0px);
      // width: calc(100% - #{$g-main-sidebar-width} - 64px);
      transform: translateX(-50%) translateX(calc($g-main-sidebar-width / 2)) translateX(0);
      // transform: translateX(-50%) translateX(calc($g-main-sidebar-width / 2)) translateX(32px);
    }

    &[data-no-main-sidebar] {
      [data-fixed-calc-width] {
        width: calc(100% - 64px);
        transform: translateX(-50%) translateX(32px);
      }
    }
  }
}

[data-layout="adaption-min-width"] {
  [data-fixed-calc-width] {
    width: calc(100% - #{$g-main-sidebar-width} - #{$g-sub-sidebar-width});
  }

  &[data-sidebar-no-collapse] {
    [data-fixed-calc-width] {
      transform: translateX(-50%) translateX(calc($g-main-sidebar-width / 2)) translateX(calc($g-sub-sidebar-width / 2));
    }

    &[data-no-main-sidebar] {
      [data-fixed-calc-width] {
        width: calc(100% - #{$g-sub-sidebar-width});
        transform: translateX(-50%) translateX(calc($g-sub-sidebar-width / 2));
      }
    }
  }

  &[data-sidebar-collapse] {
    [data-fixed-calc-width] {
      width: calc(100% - #{$g-main-sidebar-width} - 64px);
      transform: translateX(-50%) translateX(calc($g-main-sidebar-width / 2)) translateX(32px);
    }

    &[data-no-main-sidebar] {
      [data-fixed-calc-width] {
        width: calc(100% - 64px);
        transform: translateX(-50%) translateX(32px);
      }
    }
  }
}

[data-layout="center"] {
  [data-fixed-calc-width] {
    width: calc(#{$g-app-width} - #{$g-main-sidebar-width} - #{$g-sub-sidebar-width});
  }

  &[data-sidebar-no-collapse] {
    [data-fixed-calc-width] {
      transform: translateX(-50%) translateX(calc($g-main-sidebar-width / 2)) translateX(calc($g-sub-sidebar-width / 2));
    }

    &[data-no-main-sidebar] {
      [data-fixed-calc-width] {
        width: calc(#{$g-app-width} - #{$g-sub-sidebar-width});
        transform: translateX(-50%) translateX(calc($g-sub-sidebar-width / 2));
      }
    }
  }

  &[data-sidebar-collapse] {
    [data-fixed-calc-width] {
      width: calc(#{$g-app-width} - #{$g-main-sidebar-width} - 64px);
      transform: translateX(-50%) translateX(calc($g-main-sidebar-width / 2)) translateX(32px);
    }

    &[data-no-main-sidebar] {
      [data-fixed-calc-width] {
        width: calc(#{$g-app-width} - 64px);
        transform: translateX(-50%) translateX(32px);
      }
    }
  }
}

[data-layout="center-max-width"] {
  [data-fixed-calc-width] {
    width: calc(#{$g-app-width} - #{$g-main-sidebar-width} - #{$g-sub-sidebar-width});
  }

  &[data-sidebar-no-collapse] {
    [data-fixed-calc-width] {
      transform: translateX(-50%) translateX(calc($g-main-sidebar-width / 2)) translateX(calc($g-sub-sidebar-width / 2));
    }

    &[data-no-main-sidebar] {
      [data-fixed-calc-width] {
        width: calc(#{$g-app-width} - #{$g-sub-sidebar-width});
        transform: translateX(-50%) translateX(calc($g-sub-sidebar-width / 2));
      }
    }
  }

  &[data-sidebar-collapse] {
    [data-fixed-calc-width] {
      width: calc(#{$g-app-width} - #{$g-main-sidebar-width} - 64px);
      transform: translateX(-50%) translateX(calc($g-main-sidebar-width / 2)) translateX(32px);
    }

    &[data-no-main-sidebar] {
      [data-fixed-calc-width] {
        width: calc(#{$g-app-width} - 64px);
        transform: translateX(-50%) translateX(32px);
      }
    }
  }
}

[data-layout="center"],
[data-layout="center-max-width"] {
  @media screen and (max-width: $g-app-width) {
    [data-fixed-calc-width] {
      width: calc(100% - #{$g-main-sidebar-width} - #{$g-sub-sidebar-width});
    }

    &[data-sidebar-no-collapse] {
      [data-fixed-calc-width] {
        transform: translateX(-50%) translateX(calc($g-main-sidebar-width / 2)) translateX(calc($g-sub-sidebar-width / 2));
      }

      &[data-no-main-sidebar] {
        [data-fixed-calc-width] {
          width: calc(100% - #{$g-sub-sidebar-width});
          transform: translateX(-50%) translateX(calc($g-sub-sidebar-width / 2));
        }
      }
    }

    &[data-sidebar-collapse] {
      [data-fixed-calc-width] {
        width: calc(100% - #{$g-main-sidebar-width} - 64px);
      }

      &[data-no-main-sidebar] {
        [data-fixed-calc-width] {
          width: calc(100% - 64px);
        }
      }
    }
  }
}

[data-mode="mobile"] {
  [data-fixed-calc-width] {
    width: 100% !important;
    transform: translateX(-50%) !important;
  }
}

// remix icon
[class^="ri-"],
[class*=" ri-"] {
  display: inline-block;
  vertical-align: bottom;
  font-weight: 400;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
}

.tooltip-enter-active,
.tooltip-leave-active {
  transition: all 0.5s;
}

.tooltip-enter,
.tooltip-leave-to {
  opacity: 0;
  transform: translate(0, 0) scale(0.3);
}

.el-button [class*="ri-"]+span {
  margin-left: 5px;
}

.el-form-item {
  .el-form-item__label {
    color: $color-text;
  }
}

// textarea 字体跟随系统
textarea {
  font-family: inherit;
}

.el-date-editor .el-range-separator {
  width: auto;
  height: auto;
}

.el-date-editor--daterange.el-input__inner {
  width: 280px;
  height: 32px;
  line-height: 32px;

  .el-input__icon {
    height: 32px;
    line-height: 32px;
  }
}

.el-select {
  width: 200px;

  .el-input__icon {
    line-height: 32px;
  }
}

.el-select-dropdown__item {
  font-family: "PingFang SC-Regular", "PingFang SC";
  color: #121f3e;
}

.el-select-dropdown__item.selected {
  color: #121f3e;
  font-weight: 400;
}

.el-select-dropdown__item.hover,
.el-select-dropdown__item:hover {
  background: rgb(53 98 219 / 20%);
  border-radius: 4px 0 0 4px;
}

.el-input {
  .el-input__icon {
    height: 32px;
    line-height: 32px;
  }
}

.el-input__inner {
  height: 32px;
  line-height: 32px;
  color: $color-text;
}

.el-input.is-active .el-input__inner,
.el-input__inner:focus {
  border-color: $color-primary;
}

.el-tag {
  color: #fff;
}

// 单选复选框选中颜色
.el-radio,
.el-checkbox {
  color: $color-text;
}

.el-radio__input.is-checked .el-radio__inner,
.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  border-color: $color-primary;
  background-color: $color-primary;
}

.el-radio__input.is-checked+.el-radio__label,
.el-checkbox__input.is-checked+.el-checkbox__label {
  color: $color-primary;
}

.el-loading-mask {
  .el-loading-spinner .path {
    stroke: $color-primary;
  }
}

.el-table {
  border-color: $color-text-secondary;

  .el-table__header {
    .el-table__cell {
      color: #212121;
      font-size: 14px;
      font-weight: 400;
      border: none;
      background-color: #ededf5;

      .cell {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .ascending {
      .sort-caret.ascending {
        border-bottom-color: $color-primary;
      }
    }

    .descending {
      .sort-caret.descending {
        border-top-color: $color-primary;
      }
    }
  }

  .el-table__body-wrapper {
    td.el-table__cell {
      font-size: 14px;
      border-right: none;
    }
  }

  td.el-table__cell,
  th.el-table__cell.is-leaf {
    border-bottom: 1px solid #ededf5;
  }
}

.el-table--border::after,
.el-table--group::after,
.el-table::before {
  background-color: $color-text-secondary;
}

.el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell {
  background-color: rgb(53 98 219 / 5%);
}

.el-checkbox {
  .is-indeterminate {
    .el-checkbox__inner {
      background-color: $color-primary;
      border-color: $color-primary;
    }
  }

  .el-checkbox__input {
    .el-checkbox__inner:hover {
      border-color: $color-primary;
    }
  }

  .is-focus {
    .el-checkbox__inner {
      border-color: $color-primary;
    }
  }

  .is-checked {
    .el-checkbox__inner {
      background-color: $color-primary;
      border-color: $color-primary;
    }
  }

  .el-checkbox__label {
    line-height: inherit;
    width: calc(100% - 14px);
  }
}

.el-pagination {
  text-align: right;
  color: #414653;

  .el-pager li.active {
    color: $color-primary;
  }

  .el-pagination__total,
  .el-pagination__sizes {
    float: left;
  }
}

.el-message {
  border-radius: 4px;
  height: 40px;

  .el-message__title,
  .el-message__content {
    color: $color-text;
    font-size: 14px;
    font-family: "PingFang SC-Regular", "PingFang SC";
    font-weight: 400;
  }
}

// .el-message--warning {
//   background-color: #53516b;
//   border-color: #ebd2a0;
// }
.el-message--error {
  background-color: #ffece8;
  border-color: rgb(245 63 63 / 50%);
}

.el-message--success {
  background-color: #e8ffea;
  border-color: rgb(0 180 42 / 50%);
}

// el-tabs
.el-tabs__header {
  margin: 0;

  .el-tabs__nav-wrap {

    .el-tabs__nav-prev,
    .el-tabs__nav-next {
      line-height: 40px;
      width: 20px;
      text-align: center;
    }

    .el-tabs__item {
      font-size: 15px;
      font-weight: 500;

      &:hover {
        color: $color-primary;
      }
    }

    .is-active {
      color: $color-primary;
    }

    .el-tabs__active-bar {
      background-color: $color-primary;
    }

    &::after {
      height: 1px;
    }
  }
}

// el-tree
.el-tree {
  font-family: "PingFang SC-Regular", "PingFang SC";
  font-weight: 400;
  color: $color-text;

  .el-tree-node__content:hover,
  .el-upload-list__item:hover,
  .el-tree-node:focus>.el-tree-node__content {
    background: rgb(53 98 219 / 20%);
    border-radius: 4px 0 0 4px;
  }

  .el-tree-node__label {
    font-size: 15px;
  }
}

.table-state-span {
  .circle {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
  }
}

.operationBtn {
  span {
    cursor: pointer;
  }

  .operationBtn-span {
    margin-right: 10px;
    color: #3562db;
  }
  .operationBtn-del {
    color: #FF1919;
  }
}

// 全局处理el-button
.el-button {
  padding: 8px 22px;
}

// 主要按钮
.el-button--primary {
  color: #fff;
  border-color: $color-primary;
  background: $color-primary;
  font-weight: 400;
  font-size: 14px;
  padding: 8px 22px;
  font-family: PingFangSC-Regular;

  &:hover,
  &:focus {
    font-family: PingFangSC-Regular;
    border-color: $color-primary-hover;
    background-color: $color-primary-hover;
    font-weight: 500;
  }

  &.is-disabled {
    border-color: $color-primary-disabled;
    background-color: $color-primary-disabled;
    font-weight: 500;

    &:hover {
      border-color: $color-primary-disabled;
      background-color: $color-primary-disabled;
      font-weight: 500;
    }
  }
}

// 副要按钮
.el-button--primary.is-plain {
  color: $color-primary;
  border-color: $color-primary-pain;
  background: $color-primary-pain;
  font-size: 14px;
  font-family: PingFangSC-Regular;
  padding: 8px 22px;

  &:hover,
  &:focus {
    color: $color-primary;
    border-color: $color-primary-pain;
    background-color: $color-primary-pain;
    font-family: PingFangSC-Regular-Blod, "PingFang SC";
  }
}

// 文字按钮
.el-button.el-button--text {
  padding: 0;
  font-size: 14px;
  color: $color-primary;

  &:hover,
  &:focus {
    color: $color-primary;
  }

  &.text-red:not(.is-disabled) {
    // 红色文字按钮
    color: #FF1919;
  }

  &.is-disabled {
    color: $color-text-secondary;
  }
}

// 默认按钮
.el-button--default.is-plain {
  font-size: 14px;
  font-family: PingFangSC-Regular;
  padding: 8px 22px;

  &:hover,
  &:focus {
    color: $color-primary;
    border-color: $color-primary-pain;
    font-family: PingFangSC-Regular-Blod, "PingFang SC";
  }
}

// 单选组按钮
.el-radio-button {
  .el-radio-button__inner {
    &:hover {
      color: $color-primary;
    }

  }
}

.el-radio-button.is-active {
  .el-radio-button__inner {
    background: $color-primary;
    border-color: $color-primary;
    box-shadow: -1px 0 0 0 $color-primary;

    &:hover {
      color: #fff;
    }
  }
}

.tooltip {
  background: #fff !important;
  border: 1px solid #fff !important;
  border-radius: 2px;
  box-shadow: 0 0 10px 5px #eee;
  width: auto !important;
  font-size: 14px;
  padding: 5px 0;
  color: $color-text;

  .work-order-type>div {
    padding: 8px 10px;
    text-align: center;
    cursor: pointer;

    &:hover {
      background: #f6f5fa;
      color: $color-primary;
    }
  }

  .el-tooltip__popper {
    border-radius: 0;
  }
}

.el-tooltip__popper.is-light[x-placement^="bottom"] .popper__arrow {
  border-bottom-color: #fff;
}

.el-tooltip__popper {
  max-width: 600px;
}

// el-dialog
.model-dialog {
  background: #f6f5fa;
  border-radius: 4px;
  overflow: hidden;

  .el-dialog__header {
    height: 56px;
    padding: 15px 20px;
    background: #fff;
    border-bottom: 1px solid $color-text-secondary;
    box-shadow: 0 0 12px 3px rgb(0 0 0 / 10%);

    .el-dialog__title,
    .dialog-title {
      font-size: 18px;
      font-family: "PingFang SC-Medium", "PingFang SC";
      font-weight: 500;
      color: $color-text;
    }

    .el-dialog__close {
      color: $color-text;
      font-weight: 600;
      font-size: 18px;
    }
  }

  .el-dialog__body {
    padding: 15px;
    max-height: calc(78vh - 110px);
    overflow-y: auto;
    display: flex;

    .dialog-content {
      flex: 1;

      .el-row {
        height: 100%;
      }
    }

    // height: calc(100% - 110px);
  }

  .el-dialog__footer {
    max-height: 56px;
    padding: 10px 20px;
    background: rgb(255 255 255 / 80%);
    box-shadow: 0 0 12px 3px rgb(0 0 0 / 10%);
  }
}

// 下拉菜单 el-dropdown
.dropdownSelect {
  .el-dropdown-menu__item {
    color: #7f848c;
  }

  .el-dropdown-menu__item:hover {
    background: rgb(53 98 219 / 10%) !important;
    color: $color-primary !important;
  }
}

.toptip {
  box-sizing: border-box;
  height: 50px;
  width: 100%;
  line-height: 50px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid rgb(216 222 231 / 100%);
}

.green_line {
  display: inline-block;
  width: 8px;
  height: 16px;
  border-radius: 0 8px 8px 0;
  background: #3562db;
  margin-right: 10px;
  vertical-align: middle;
}

.el-select-dropdown {
  min-width: 150px !important;
}

.imgCarousel-dialog .el-dialog {
  margin-top: 20vh !important;
  box-shadow: 0 1px 3px rgb(0 0 0 / 0%);
  background-color: rgb(0 0 0 / 0%);
}

.imgCarousel-dialog .el-dialog__headerbtn .el-dialog__close {
  margin-top: -30px;
  color: #d8dee7;
}

.imgCarousel-dialog .el-dialog__header {
  border-bottom: none;
}

.imgCarousel-dialog .el-icon-close::before {
  content: "\E6DB";
  font-size: 34px;
}

.imgCarousel-dialog .el-carousel__item {
  display: flex;
  justify-content: center;
  align-items: center;
}

.icon-running-state {
  background: url("~@/assets/images/elevator/icon-running-state.png") no-repeat;
  background-size: 100% 100%;
}

.icon-current-floor {
  background: url("~@/assets/images/elevator/icon-current-floor.png") no-repeat;
  background-size: 100% 100%;
}

.icon-door-status {
  background: url("~@/assets/images/elevator/icon-door-status.png") no-repeat;
  background-size: 100% 100%;
}

.icon-indoor-temp {
  background: url("~@/assets/images/elevator/icon-indoor-temp.png") no-repeat;
  background-size: 100% 100%;
}

.icon-manned-not {
  background: url("~@/assets/images/elevator/icon-manned-not.png") no-repeat;
  background-size: 100% 100%;
}

.icon-indoor-humidity {
  background: url("~@/assets/images/elevator/icon-indoor-humidity.png") no-repeat;
  background-size: 100% 100%;
}

.el-picker-panel.no-atTheMoment {
  .el-button--text.el-picker-panel__link-btn {
    display: none;
  }
}

.el-dropdown-menu__item {
  &.text-red:not(.is-disabled) {
    // 红色文字按钮
    color: #FF1919;
  }
}

// 指令showtipPlus样式
.ellipsis_showtip_plus {
  white-space: nowrap;

  >div {
    display: inline-block;
    width: 100%;
    height: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.el-radio input[aria-hidden="true"] {
  display: none !important;
}

.el-radio:focus:not(.is-focus):not(:active):not(.is-disabled) .el-radio__inner {
  box-shadow: none !important;
}
