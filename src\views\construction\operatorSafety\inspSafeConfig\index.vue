<template>
  <PageContainer>
    <div slot="header" class="control-btn-header">
      <div class="search-from">
        <el-select v-model="searchFrom.workTypeId" placeholder="作业类型" clearable filterable>
          <el-option v-for="(item, index) in typeList" :key="index" :label="item.name" :value="item.id"> </el-option>
        </el-select>
        <div style="display: inline-block">
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="searchForm">查询</el-button>
        </div>
      </div>
    </div>
    <div slot="content" class="table-content">
      <div class="btn-group">
        <el-button type="primary" @click="handleListEvent('add')">新增配置</el-button>
        <span style="margin-left: 20px">
          说明：施工安全检查，指施工过程中，安保部和工程部按要求对现场进行施工安全检查。工程部检查每天一次、安保检查每2小时一次，配置可自定义修改
        </span>
      </div>
      <TablePage
        ref="table"
        v-loading="tableLoading"
        :showPage="true"
        :tableColumn="tableColumn"
        :data="tableData"
        height="calc(100% - 40px - 50px)"
        :pageData="pageData"
        @pagination="paginationChange"
      ></TablePage>
      <AddOrEdit v-if="visible" v-model="visible" :detail="dialogInfo" @success="getList" />
    </div>
  </PageContainer>
</template>
<script lang="jsx">
export default {
  name: 'inspSafeConfig',
  components: {
    AddOrEdit: () => import('./add.vue')
  },
  data() {
    return {
      tableLoading: false,
      typeList: [],
      searchFrom: {
        workTypeId: '',
      },
      tableColumn: [
        {
          prop: '',
          label: '序号',
          formatter: (scope) => {
            return (this.pageData.page - 1) * this.pageData.pageSize + scope.$index + 1
          },
          width: 80
        },
        {
          prop: 'maintainDeptName',
          label: '巡检部门',
          required: true
        },
        {
          prop: 'workTypeName',
          label: '业务类型'
        },
        {
          prop: 'templateName',
          label: '巡检模板'
        },
        {
          prop: 'createDate',
          label: '创建时间'
        },
        {
          prop: 'createByName',
          label: '创建人'
        },
        {
          prop: 'updateDate',
          label: '修改时间'
        },
        {
          prop: 'updateByName',
          label: '修改人'
        },
        {
          prop: 'operation',
          label: '操作',
          width: 120,
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span class="operationBtn-span" onClick={() => this.handleListEvent('edit', row.row)}>
                  编辑
                </span>
                <span class="operationBtn-span" style="color: #fa403c" onClick={() => this.handleListEvent('del', row.row)}>
                  删除
                </span>
              </div>
            )
          }
        }
      ],
      tableData: [],
      pageData: {
        page: 1,
        pageSize: 15,
        total: 0
      },
      visible: false,
      dialogInfo: {
        configId: '',
        handleType: ''
      }
    }
  },
  watch: {},
  mounted() {
    this.getBusinessFormList()
    this.getList()
  },
  methods: {
    getBusinessFormList() {
      this.$api
        .businessFormList({
          name: '',
          assignmentType: '0',
          page: 1,
          pageSize: 999
        })
        .then((res) => {
          if (res.code === '200') {
            this.typeList = res.data.records
          } else {
            throw res.message
          }
        })
    },
    handleListEvent(type, row) {
      switch (type) {
        case 'add':
        case 'edit':
          this.visible = true
          this.dialogInfo.handleType = type
          this.dialogInfo.configId = row ? row.id : ''
          break
        case 'del':
          this.$confirm('确定删除该数据吗？', '提示', {
            cancelButtonClass: 'el-button--primary is-plain',
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.$api.workMaintainConfigDel({ id: row.id }).then((res) => {
              if (res.code === '200') {
                this.$message.success('删除成功')
                this.getList()
              }
            })
          })
        default:
          break
      }
    },
    // 查询
    searchForm() {
      this.pageData.page = 1
      this.getList()
    },
    // 重置查询
    resetForm() {
      this.searchFrom = {
        workTypeId: '',
      }
      this.pageData.page = 1
      this.searchForm()
    },
    // 获取应用列表
    getList() {
      let param = {
        ...this.searchFrom,
        currentPage: this.pageData.page,
        size: this.pageData.pageSize
      }
      this.tableLoading = true
      this.$api
        .workMaintainConfigList(param)
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.list
            this.pageData.total = res.data.count
          }
          this.tableLoading = false
        })
        .catch(() => {
          this.tableLoading = false
        })
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getList()
    }
  }
}
</script>
<style lang="scss" scoped>
.hide {
  ::v-deep .el-upload--picture-card {
    display: none;
  }
}
.control-btn-header {
  padding: 10px !important;
  .search-from {
    & > div {
      margin-right: 10px;
    }
  }
}
.table-content {
  margin-top: 15px;
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 15px);
  padding: 10px;
  .btn-group {
    height: 50px;
  }
}
</style>
