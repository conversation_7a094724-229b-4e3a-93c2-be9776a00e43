<template>
  <div>
    <div class="quesName">
      <spanspan v-show="pathName[diff].isQuestionNum">{{ index + 1 }}、</spanspan>
      {{ radioOption.name }}
      <span v-show="pathName[diff].isShowSubjectType" class="chexMinMax">[单选题]</span>
      <span v-if="radioOption.isMust == 1" class="starRed">*</span>
    </div>
    <div v-for="(childitem, childindex) in radioOption.optionsData" :key="childindex" :class="getRadioStyle(radioOption.rowCount)" style="display: flex; align-items: center;">
      <el-radio v-model="radioValue" :label="childitem.id" :disabled="pathName[diff].isDisable" @change="handleRadioChange(childitem, childindex, $event)">{{
        childitem.name
      }}</el-radio>
      <el-input
        v-if="childitem.isInput == 1"
        v-model="answerInfo[childindex].textValue"
        class="isput"
        :disabled="pathName[diff].isDisable"
        @focus="handleRadioChange(childitem, childindex)"
      ></el-input>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PreviewRadio',
  props: {
    // 当前题目的所有相关信息
    previewOption: {
      type: Object
    },
    // 该题目在当前问卷中的序号
    index: {
      type: Number
    },
    // 是否显示题目序号，true为显示，false为不显示
    isQuestionNum: {
      type: Boolean
    },
    // 表示用于在哪个页面显示该题目时的相关设置信息
    pathName: {
      type: Object
    },
    diff: {
      type: String
    }
  },
  data() {
    return {
      currentPathName: '',
      storage: {},
      radioOption: {},
      answerVal: [], // 保存当前题目的答卷信息，父页面（答题页面）获取后用于构造答卷的请求参数
      answerInfo: [], // 保存表单数据
      radioValue: '',
      question: {
        1: 'quesOption',
        2: 'quesOption quesOptiontwo',
        3: 'quesOption quesOptionthree',
        4: 'quesOption quesOptionfour',
        5: 'quesOption quesOptionfive'
      }
      // pathName: {
      //   questionView: {
      //     isShowSubjectType: false,
      //     isDisable: true,
      //     isQuestionNum: true
      //   },
      //   quesstionScan: {
      //     isShowSubjectType: true,
      //     isDisable: false,
      //     isQuestionNum: true
      //   },
      //   questionDesign: {
      //     isShowSubjectType: true,
      //     isDisable: false,
      //     isQuestionNum: true
      //   }
      // }
    }
  },
  created() {
    console.log(this.diff, 'this.diff')
    this.radioOption = this.previewOption
    console.log(this.radioOption, 'this.radioOption')
    this.answerInfo = this.previewOption.optionsData.map((element) => {
      return {
        optionId: element.id, // 选项id
        optionName: '', // 选项label
        textValue: '' // 保存选项后的输入框的值
      }
    })
    if (this.pathName[this.diff].isSetDefaultValue) {
      if (this.previewOption.answers.length > 0) {
        this.radioValue = this.previewOption.answers[0].optionId
        this.answerInfo.forEach((item) => {
          this.previewOption.answers.forEach((answerOption) => {
            if (item.optionId === answerOption.optionId) {
              item.textValue = answerOption.textValue
            }
          })
        })
      }
    }
  },

  methods: {
    checkValided() {
      if (this.radioOption.isMust === 0) {
        return false
      }
      // 判断当前题目是否必填，必填时才需要判断是否已答
      if (this.radioOption.isMust !== 1) {
        return true
      }
      // 判断当前题目是否已答
      if (!this.isAnswer) {
        return true
      }
      return this.validedForm()
      return false
    },
    doValidedFlag() {
      return this.checkValided()
    },
    doValided() {
      this.isValided = this.checkValided()
      return this.isValided
    },
    // -----------------------
    getRadioStyle(val) {
      return this.question[val]
    },
    handleRadioChange(optionItem, optionIndex, val) {
      const value = this.radioValue
      // 清空其他选项输入框的值
      this.answerInfo.forEach((item) => {
        item.textValue = ''
        item.optionName = ''
      })
      // val为undefined表示输入框获取到焦点，此时默认选中当前行的radio
      if (val === undefined) {
        this.radioValue = optionItem.id
      }
      this.answerInfo[optionIndex].optionName = optionItem.name
      // 获取radio选中的值，当optionId和optionName同事存在当前的radio值才有效
      const answerArray = this.answerInfo.filter((item) => {
        return item.optionId && item.optionName
      })
      this.answerVal = answerArray.map((item) => {
        return {
          pvqId: localStorage.getItem('questId'), // 问卷id
          questionId: this.radioOption.id, // 题目id
          questionType: this.radioOption.type, // 题目类型
          ...item
        }
      })
      this.$parent.$parent.$parent.addAnswer &&
        this.$parent.$parent.$parent.addAnswer('selectFun', {
          old: this.storage,
          news: optionItem.id
        })
      this.storage = optionItem.id
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
.el-radio {
  margin-right: 10px;
  display: block;
  margin-bottom: 19px;
}

.quesName {
  font-size: 16px;
  margin-bottom: 10px;
}

.starRed {
  color: red;
}

.quesOption {
  padding: 13px 0;
  font-size: 16px;
  background-color: #fff;
  position: relative;
}

.quesOptiontwo {
  display: inline-block;
  width: 50%;
}

.quesOptionthree {
  display: inline-block;
  width: 33%;
}

.quesOptionfour {
  display: inline-block;
  width: 25%;
}

.quesOptionfive {
  display: inline-block;
  padding-right: 20px;
}

.isput {
  width: 200px;
  margin-left: 24px;
}

input[type="text"]:focus {
  border: 1px solid #2cc7c5;
  outline: none;
}

.chexMinMax {
  margin-left: 3px;
}
</style>
