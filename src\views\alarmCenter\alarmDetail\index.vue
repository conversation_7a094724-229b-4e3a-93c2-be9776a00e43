<template>
  <PageContainer footer>
    <div slot="header">
      <div class="header">
        <div class="header_left">
          <div>报警详情（{{ alarmId }}）</div>
          <div v-if="!isHide">
            <el-tag v-if="detail.alarmStatus === 0" size="small" type="danger">未处理</el-tag>
            <el-tag v-else-if="detail.alarmStatus === 1" size="small" type="warning">处理中</el-tag>
            <el-tag v-else size="small" type="success">已处理</el-tag>
            <el-tag v-if="detail.shield" size="small" type="info" style="margin-left: 10px">已屏蔽</el-tag>
          </div>
        </div>
        <div class="header_right">
          <template v-if="!isHide && detail.classic === 1">
            <i class="el-icon-star-on"></i>
            已收藏
          </template>
          <template v-else-if="!isHide && detail.classic != 1">
            <i class="el-icon-star-off"></i>
            收藏案例
          </template>
        </div>
      </div>
    </div>
    <div slot="content" class="detail_content">
      <div class="content_left">
        <div :class="['content_top', { 'topHeight': isHide }]">
          <div class="title">详细信息</div>
          <el-row type="flex">
            <el-col :span="12" class="detail_item">
              <span class="label">报警等级：</span>
              <el-tooltip class="item" effect="dark" :content="alarmLevel[detail.alarmLevel || 0]" placement="top">
                <span class="value" style="color: red">{{ alarmLevel[detail.alarmLevel || 0] }}</span>
              </el-tooltip>
            </el-col>
            <el-col :span="12" class="detail_item">
              <span class="label">报警时间：</span>
              <el-tooltip class="item" effect="dark" :content="detail.alarmStartTime" placement="top">
                <span class="value">{{ detail.alarmStartTime }}</span>
              </el-tooltip>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :span="12" class="detail_item">
              <span class="label">报警ID：</span>
              <el-tooltip class="item" effect="dark" :content="detail.alarmId" placement="top">
                <span class="value">{{ detail.alarmId }}</span>
              </el-tooltip>
            </el-col>
            <el-col :span="12" class="detail_item">
              <span class="label">报警系统：</span>
              <el-tooltip class="item" effect="dark" :content="detail.projectName" placement="top">
                <span class="value" :class="{ link: sysType.includes(detail.projectCode) }" @click="handleSysClick">{{
                  detail.projectName }}</span>
              </el-tooltip>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :span="12" class="detail_item">
              <span class="label">报警类型：</span>
              <el-tooltip class="item" effect="dark" :content="detail.alarmType" placement="top">
                <span class="value">{{ detail.alarmType }}</span>
              </el-tooltip>
            </el-col>
            <el-col :span="12" class="detail_item">
              <span class="label">报警位置：</span>
              <el-tooltip class="item" effect="dark" :content="detail.alarmSpaceName" placement="top">
                <span class="value">{{ detail.alarmSpaceName }}</span>
              </el-tooltip>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :span="12" class="detail_item">
              <span class="label">报警对象：</span>
              <el-tooltip class="item" effect="dark" :content="detail.alarmObjectName" placement="top">
                <span class="value" :class="{ link: sysType.includes(detail.projectCode) }"
                  @click="handleToDetailClick('2')">{{ detail.alarmObjectName }}</span>
              </el-tooltip>
            </el-col>
            <el-col :span="12" class="detail_item">
              <span class="label">报警数值：</span>
              <el-tooltip class="item" effect="dark" :content="detail.alarmValue" placement="top">
                <span class="value">{{ detail.alarmValue }}</span>
              </el-tooltip>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :span="24" class="detail_item">
              <span class="label">物联设备：</span>
              <el-tooltip class="item" effect="dark" :content="detail.iotDeviceName" placement="top">
                <span class="value" :class="{ link: sysType.includes(detail.projectCode) }"
                  @click="handleToDetailClick('1')">{{ detail.iotDeviceName }}</span>
              </el-tooltip>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :span="24" class="detail_item">
              <span class="label">报警描述：</span>
              <el-tooltip class="item" effect="dark" :content="detail.alarmDetails" placement="top">
                <span class="value">{{ detail.alarmDetails }}</span>
              </el-tooltip>
            </el-col>
          </el-row>
          <el-row type="flex" v-if='!isHide'>
            <el-col :span="12" class="detail_item">
              <span class="label">处理状态：</span>
              <el-tooltip class="item" effect="dark" :content="alarmStatusList[detail.alarmStatus]" placement="top">
                <span class="value"
                  :style="`color:${detail.alarmStatus == 0 ? '#F56C6C' : detail.alarmStatus == 1 ? '#ff9435' : '#67C23A'}`">
                  {{ alarmStatusList[detail.alarmStatus] }}
                </span>
              </el-tooltip>
            </el-col>
            <el-col :span="12" class="detail_item">
              <span class="label">警情类型：</span>
              <el-tooltip class="item" effect="dark" :content="alarmAffirmList[detail.alarmAffirm] || '未确认警情'"
                placement="top">
                <span class="value">{{ alarmAffirmList[detail.alarmAffirm] || '未确认警情' }}</span>
              </el-tooltip>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24" class="detail_item">
              <span class="label">影像信息：</span>
              <span class="value">
                <el-image v-for="(item, index) in detail.imageFileList" :key="index" style="width: 100px; height: 100px"
                  :src="item.fileUrl" fit="cover"
                  :preview-src-list="detail.imageFileList.map((el) => el.fileUrl)"></el-image>
              </span>
            </el-col>
          </el-row>
        </div>
        <div class="content_bottom" v-if='!isHide'>
          <el-tabs v-model="tabActive">
            <el-tab-pane label="处理过程" name="1">
              <HandleRecord v-if="tabActive === '1'" :data="handleProcessInfo" :detail="detail" />
            </el-tab-pane>
            <el-tab-pane label="预案流程" name="5">
              <template v-if="tabActive === '5'">
                <FlowChart v-if="isShow" type="view" :eventData="addEventData" />
                <div v-else class="no-data">
                  <img src="@/assets/images/null.png" alt="空数据" />
                  <span>暂无数据</span>
                </div>
              </template>
            </el-tab-pane>
            <el-tab-pane label="警情处理过程" name="2">
              <HandleProcess v-if="tabActive === '2'" :data="handleProcessList" />
            </el-tab-pane>
            <el-tab-pane :label="`工单信息(${workInfo.length || 0})`" name="3">
              <WorkOrderMsg v-if="tabActive === '3'" :workInfoList="workInfo" :alarmId="alarmId"
                :projectCode="detail.projectCode" :alarmSpaceId="detail.alarmSpaceId"
                @addWorkOrderSuccess="updateDetail" @closeWorkDetail="closeWorkDetail" />
            </el-tab-pane>
            <el-tab-pane v-if="detail.isSummary" label="总结分析" name="4">
              <SummaryAnalysis v-if="tabActive === '4'" :data="summaryInfo" />
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      <div class="content_right">
        <RightVideoContent :detail="detail" />
      </div>
      <!-- 开始处理 -->
      <HandleDrawer v-model="handleDrawerVisible" :selectItems="selectAlarmList" @close="closeHandleDrawer" />
      <!-- 屏蔽 -->
      <ScreenDialog v-if="scrDialog" :selectItems="selectAlarmList" :visible.sync="scrDialog"
        @update="closeScrDialog" />
      <!-- 添加说明 -->
      <RemarkDialog v-if="remarkVisible" :visible.sync="remarkVisible" :selectItems="selectAlarmList"
        @close="closeRemark">
      </RemarkDialog>
      <!-- 总结分析 -->
      <SummaryDrawer :visible.sync="summaryVisible" :selectItems="selectAlarmList" @close="closeSummary" />
    </div>
    <div slot="footer">
      <el-dropdown v-if='!isHide' trigger="click" @command="command">
        <span class="el-dropdown-link"> 更多操作<i class="el-icon-arrow-down el-icon--right"></i> </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item :disabled="!!detail.workNum"
            :command="beforeCommand('dispatch', detail)">派单</el-dropdown-item>
          <el-dropdown-item :disabled="detail.alarmStatus !== 0" :command="beforeCommand('shield', detail)">
            {{ detail.shield ? '取消屏蔽' : '屏蔽' }}
          </el-dropdown-item>
          <el-dropdown-item :command="beforeCommand('remark', detail)">添加说明</el-dropdown-item>
          <el-dropdown-item :command="beforeCommand('collect', detail)">{{ detail.classic == 1 ? '取消收藏' : '收藏案例'
          }}</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-button style="margin-left: 10px" plain @click="goBack">返回</el-button>
      <template v-if="!isHide && detail.alarmStatus == 2">
        <el-button v-if="detail.isSummary" type="primary" disabled>已总结</el-button>
        <el-button v-else type="primary" @click="handleSummary">总结</el-button>
      </template>
      <el-button v-else-if="!isHide && detail.alarmStatus != 2" type="primary"
        :disabled="detail.alarmStatus != 0 || (detail.disposalTerminal && detail.disposalTerminal.indexOf('2') === -1)"
        @click="handleAlarm">
        开始处理
        <el-tooltip class="item" effect="dark" :content="`此类报警仅可在${showClientName(detail.disposalTerminal)}端处理`"
          placement="top">
          <i v-if="detail.disposalTerminal && detail.disposalTerminal.indexOf('2') === -1" class="el-icon-info"></i>
        </el-tooltip>
      </el-button>
    </div>
  </PageContainer>
</template>
<script>
export default {
  components: {
    HandleRecord: () => import('./components/handleRecord'),
    HandleProcess: () => import('./components/handleProcess'),
    WorkOrderMsg: () => import('./components/workOrderMsg'),
    SummaryAnalysis: () => import('./components/summaryAnalysis'),
    HandleDrawer: () => import('./components/handleDrawer'),
    ScreenDialog: () => import('./components/screenDialog'),
    RemarkDialog: () => import('./components/remarkDialog'),
    RightVideoContent: () => import('./components/rightVideoContent'),
    SummaryDrawer: () => import('./components/summaryDrawer'),
    FlowChart: () => import('@/views/operationPort/planManage/components/flowChart.vue')
  },
  async beforeRouteLeave(to, from, next) {
    if (!['allAlarmIndex', 'noticeMyIndex', 'ClassicCasesIndex'].includes(to.name)) {
      this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      tabActive: '1',
      alarmId: '',
      handleDrawerVisible: false,
      alarmLevel: {
        0: '通知',
        1: '一般',
        2: '紧急',
        3: '重要'
      },
      alarmStatusList: {
        0: '未处理',
        1: '处理中',
        2: '已处理'
      },
      alarmAffirmList: {
        0: '未确认报警',
        1: '真实报警',
        2: '误报',
        3: '演练',
        4: '调试'
      },
      // 详情基础信息
      detail: {},
      // 处理过程
      handleProcessInfo: {},
      // 警情处理过程
      handleProcessList: [],
      // 工单信息
      workInfo: [],
      summaryInfo: {},
      // 屏蔽
      scrDialog: false,
      selectAlarmList: [],
      // 添加说明
      remarkVisible: false,
      // 总结分析
      summaryVisible: false,
      addEventData: {
        1: {}, // 演习
        2: {}, // 演习-启动预案
        3: {}, // 演习-自定义
        4: {}, // 确警
        5: {}, // 确警-启动预案
        6: {} // 确警-自定义
      },
      sysType: ['UPS', 'PDXT', 'SSSJCXT', 'AGVJQR', 'AIZNFX', 'WHPKFJC', 'DMJFJC', 'YLRQJC', 'DTXT'],
      isHide: false
    }
  },
  computed: {
    isShow() {
      return Object.keys(this.addEventData).some((key) => Object.keys(this.addEventData[key]).some((k) => this.addEventData[key][k].length))
    }
  },
  watch: {
    // 监听路由query变化
    $route: function (newVal, oldVal) {
      this.alarmId = newVal.query.alarmId
      this.getAlarmDetails(this.alarmId)
    }
  },
  mounted() {
    this.isHide = this.isSjyyy()
    this.tabActive = this.$route.query.tabType || '1'
    this.alarmId = this.$route.query.alarmId
    this.getAlarmDetails(this.alarmId)
  },
  methods: {
    // 获取模板数据
    getTemplateData(detail) {
      if (!detail.preplanId) return
      this.$api
        .GetPlanDetail({ id: detail.preplanId })
        .then((res) => {
          if (res.code == 200) {
            let { warnEventList, noticeEventList, confirmEventList, id } = res.data
            warnEventList.forEach((item) => {
              if (Object.keys(this.addEventData[item.stepType]).includes('warnEventList')) {
                this.addEventData[item.stepType].warnEventList.push(item)
              } else {
                this.addEventData[item.stepType].warnEventList = [item]
              }
            })
            noticeEventList.forEach((item) => {
              item.noticeWay = Number(item.noticeWay)
              item.noticeType = Number(item.noticeType)
              if (Object.keys(this.addEventData[item.stepType]).includes('noticeEventList')) {
                this.addEventData[item.stepType].noticeEventList.push(item)
              } else {
                this.addEventData[item.stepType].noticeEventList = [item]
              }
            })
            confirmEventList.forEach((item) => {
              if (Object.keys(this.addEventData[item.stepType]).includes('confirmEventList')) {
                this.addEventData[item.stepType].confirmEventList.push(item)
              } else {
                this.addEventData[item.stepType].confirmEventList = [item]
              }
            })
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 报警对象 物联设备 跳转 对应详情页
    handleToDetailClick(type) {
      if (!this.sysType.includes(this.detail.projectCode)) return
      // type 1 物联设备 2 监测设备 /UPS/operationalMonitoring/deviceDetails?id=xxx&assetsName=xxx&systemCode=UPS&equipAttr=2
      this.$router.push({
        path: `/${this.detail.projectCode}/operationalMonitoring/deviceDetails`,
        query: {
          id: type === '2' ? this.detail.alarmObjectId : this.detail.ispHarvesterId,
          assetsName: type === '2' ? this.detail.alarmObjectName : this.detail.ispHarvesterName,
          systemCode: this.detail.projectCode,
          equipAttr: type
        }
      })
    },
    // 报警系统跳转
    handleSysClick() {
      /**
       * UPS系统 UPS UPS/operationalOverview
       * 变配电系统 PDXT PDXT/operationalOverview
       * 手术室监测 SSSJCXT SSSJCXT/operationalOverview
       * AGV物流监测 AGVJQR /AGVJQR/operationalOverview
       * AI智能监测 AIZNFX /AIZNFX/operationalOverview
       * 危化品监测 WHPKFJC /WHPKFJC/operationalOverview
       * 毒麻精放监测 DMJFJC /DMJFJC
       * 压力容器监测 YLRQJC  YLRQJC
       * 电梯系统 DTXT DTXT /DTXT/operationalOverview
       */
      if (this.sysType.includes(this.detail.projectCode)) {
        this.$router.push(`/${this.detail.projectCode}/operationalOverview`)
      }
    },
    showClientName(val) {
      if (val === '0') {
        return 'app'
      } else if (val === '1') {
        return '专业客户'
      } else if (val === '0,1') {
        return 'app和专业客户'
      } else {
        return ''
      }
    },
    // 报警详情
    getAlarmDetails(alarmId) {
      this.workInfo = []
      this.$api.GetAlarmDetails({ alarmId: alarmId }).then((res) => {
        if (res.code == 200) {
          this.detail = res.data.record
          this.handleProcessInfo = res.data.handleProcessInfo
          this.handleProcessList = res.data.detail
          this.workInfo = res.data.workInfo
          this.summaryInfo = res.data.summaryInfo
          this.getTemplateData(res.data.record)
        }
      })
    },
    // 列表更多按钮
    beforeCommand(type, row) {
      return {
        type: type,
        row: row
      }
    },
    command(data) {
      // 派单
      if (data.type == 'dispatch') {
        this.$confirm('是否确认派发确警工单?', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.oneKeyDispatch(data.row)
        })
      }
      // 添加说明
      if (data.type == 'remark') {
        this.selectAlarmList = [data.row]
        this.remarkVisible = !this.remarkVisible
      }
      // 屏蔽
      if (data.type == 'shield') {
        if (data.row.shield == 1) {
          this.$confirm('是否取消屏蔽当前报警?', '提示', {
            cancelButtonClass: 'el-button--primary is-plain',
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.$api
              .shield({
                alarmId: data.row.alarmId,
                alarmObjectId: data.row.alarmObjectId,
                alarmType: data.row.alarmFleldsConfigId,
                shield: false,
                operationSource: 0
              })
              .then((res) => {
                if (res.code == 200) {
                  this.$message.success('已取消屏蔽')
                }
              })
          })
        } else {
          this.selectAlarmList = [data.row]
          this.scrDialog = !this.scrDialog
        }
      }
      // 存为收藏案例
      if (data.type == 'collect') {
        this.collectAlarmRecords(data.row)
      }
    },
    closeScrDialog() {
      this.scrDialog = false
      this.getAlarmDetails(this.alarmId)
    },
    closeRemark() {
      this.remarkVisible = false
      this.getAlarmDetails(this.alarmId)
    },
    // 一键派单
    oneKeyDispatch(row) {
      const userInfo = this.$store.state.user.userInfo.user
      let param = {
        alarmId: row.alarmId,
        alarmLevel: row.alarmLevel,
        alarmSourceName: row.alarmSource,
        incidentName: row.alarmType,
        spaceId: row.alarmSpaceId,
        userName: userInfo.staffName,
        userId: userInfo.staffId,
        projectCode: row.projectCode,
        operationSource: 0
      }
      this.$api.OneKeyDispatch(param).then((res) => {
        if (res.code == 200) {
          this.$message({
            message: '已派单',
            type: 'success'
          })
          this.getAlarmDetails(this.alarmId)
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 存为经典案例
    collectAlarmRecords(row) {
      let param = {
        alarmId: row.alarmId,
        classic: row.classic == 1 ? '0' : '1',
        operationSource: 0
      }
      this.$api.CollectAlarmRecords(param).then((res) => {
        if (res.code == 200) {
          if (param.classic == '1') {
            this.$alert('已存为经典案例，警情处理完毕后会在经典案例中显示', '提示', {
              confirmButtonText: '确定',
              type: 'warning',
              callback: (action) => {
                this.getAlarmDetails(this.alarmId)
              }
            })
          } else {
            this.$nextTick(() => {
              this.getAlarmDetails(this.alarmId)
            })
          }
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    handleAlarm() {
      this.selectAlarmList = [this.detail]
      this.handleDrawerVisible = true
    },
    closeHandleDrawer() {
      this.getAlarmDetails(this.alarmId)
    },
    handleSummary() {
      this.selectAlarmList = [this.detail]
      this.summaryVisible = true
    },
    closeSummary() {
      this.summaryVisible = false
      this.getAlarmDetails(this.alarmId)
    },
    updateDetail() {
      this.getAlarmDetails(this.alarmId)
      this.$nextTick(() => {
        this.$forceUpdate()
      })
    },
    closeWorkDetail() {
      this.getAlarmDetails(this.alarmId)
      this.$nextTick(() => {
        this.$forceUpdate()
      })
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>
<style lang="scss" scoped>
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  width: 100%;
  height: 56px;
  color: #333;
  font-weight: 500;

  >div {
    margin-right: 8px;
  }

  .header_left {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .header_right {
    color: #3562db;
  }
}

.detail_content {
  width: 100%;
  height: 100%;
  margin-top: 16px;
  display: flex;

  .content_left {
    width: calc(100% - 821px - 16px);
    margin-right: 16px;
    height: 100%;
    overflow: auto;

    .content_top {
      padding: 24px;
      background: #fff;
    }

    .content_bottom {
      min-height: calc(100% - 320px);
      padding: 24px;
      margin-top: 16px;
      background: #fff;
      padding-top: 0;
    }
  }

  .content_right {
    overflow: auto;
    width: 821px;
    height: 100%;
    background: #fff;
    padding: 24px;
  }

  .el-row {
    margin-top: 24px;
    margin-bottom: 24px;
    padding: 0 24px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.title {
  display: flex;
  align-items: center;
  font-size: 16px;
  color: #333;

  &::before {
    content: '';
    background: #3562db;
    width: 6px;
    height: 6px;
    margin-right: 6px;
  }
}

.detail_item {
  display: flex;
  align-items: center;

  .label {
    display: inline-block;
    width: 100px;
    color: #96989a;
    text-align: right;
  }

  .value {
    display: inline-block;
    max-width: calc(100% - 100px);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .link {
    color: #1890ff;
    cursor: pointer;
  }
}

.no-data {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;

  img {
    width: 50%;
    height: 50%;
  }

  span {
    color: #999;
  }
}

.topHeight {
  height: 100%;
}
</style>
