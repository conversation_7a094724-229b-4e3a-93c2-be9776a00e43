<template>
  <div class="temporary-page drag_class">
    <div v-if="item.id == 'mock1'" class="water-statistics">
      <ContentCard title="能源流向分析" :scrollbarHover="true" :cstyle="{ height: '100%' }" :hasMoreOper="['edit']" @more-oper-event="(val) => $emit('all-more-Oper', val)">
        <div slot="title-right" class="data-right-box">
          <div class="data-btns">
            <span v-for="item in energyFlowList" :key="item.type" :class="{ 'active-btn': selectEnergyFlow == item.type }" @click="changeDateType('selectEnergyFlow', item.type)">{{
              item.name
            }}</span>
          </div>
          <el-select v-model="temporarily.energyFlow">
            <el-option label="日" value="day"></el-option>
            <el-option label="周" value="week"></el-option>
            <el-option label="月" value="month"></el-option>
          </el-select>
        </div>
        <div slot="content" class="statistics-content">
          <div class="energy-flow-bg"></div>
          <div v-for="(flowData, index) in flowDataList" :key="index" :class="'flow-' + index">
            <div>{{ flowData.name }}</div>
            <div>
              {{ getFlowaValue(flowData.value[selectEnergyFlow]) }} {{ flowDataUnit[selectEnergyFlow] }}
              <span v-if="index != 0">{{ (flowData.value[selectEnergyFlow] * 100).toFixed(1) + '%' }}</span>
            </div>
          </div>
        </div>
      </ContentCard>
    </div>
    <div v-if="item.id == 'mock2'" class="gas-statistics">
      <ContentCard title="能耗指标分析" :scrollbarHover="true" :cstyle="{ height: '100%' }" :hasMoreOper="['edit']" @more-oper-event="(val) => $emit('all-more-Oper', val)">
        <div slot="title-right" class="data-right-box">
          <div class="data-btns">
            <span
              v-for="item in energyTargetList"
              :key="item.type"
              :class="{ 'active-btn': selectEnergyTarget == item.type }"
              @click="changeDateType('selectEnergyTarget', item.type)"
              >{{ item.name }}</span
            >
          </div>
          <el-select v-model="temporarily.energyTarget">
            <el-option label="2023" value="2023"></el-option>
            <el-option label="2024" value="2024"></el-option>
          </el-select>
        </div>
        <div slot="content" class="statistics-echarts">
          <div v-for="echartsData in echartsDataList" :key="echartsData.echartsId" class="echarts-box">
            <div :id="echartsData.echartsId"></div>
            <div class="echarts-text">
              <span class="echarts-text-num">{{ echartsData.num }}</span>
              <span class="echarts-text-unit">{{ echartsData.unit }}</span>
              <span class="echarts-text-title">{{ echartsDataTitle[echartsData.echartsId] }}</span>
            </div>
          </div>
        </div>
      </ContentCard>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
export default {
  name: 'temporaryPage',
  props: {
    item: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      // 临时数据 start
      energyFlowList: [
        {
          name: '用电',
          type: 'usePower'
        },
        {
          name: '用水',
          type: 'uesWater'
        },
        {
          name: '综合',
          type: 'useAll'
        }
      ],
      selectEnergyFlow: 'usePower',
      energyTargetList: [
        {
          name: 'tce',
          type: 'tce'
        },
        {
          name: '碳排放',
          type: 'carbon'
        },
        {
          name: '电',
          type: 'power'
        },
        {
          name: '水',
          type: 'water'
        }
      ],
      selectEnergyTarget: 'tce',
      temporarily: {
        energyFlow: 'day',
        energyTarget: '2023'
      },
      echartsDataTitle: {
        incomeEchart: '万元收入能耗',
        unitAreaEchart: '单位面积能耗',
        preCapitaEchart: '人均能耗'
      },
      echartsDataList: [],
      echartsTableData: {
        tce: [
          {
            echartsId: 'incomeEchart',
            num: 35,
            min: 0,
            max: 100,
            unit: 'tce/万元'
          },
          {
            echartsId: 'unitAreaEchart',
            num: 2.3,
            min: 0,
            max: 10,
            unit: 'tce/m²'
          },
          {
            echartsId: 'preCapitaEchart',
            num: 1.2,
            min: 0,
            max: 10,
            unit: 'tce/人'
          }
        ],
        carbon: [
          {
            echartsId: 'incomeEchart',
            num: 24.2,
            min: 0,
            max: 100,
            unit: 'kg/万元'
          },
          {
            echartsId: 'unitAreaEchart',
            num: 0.45,
            min: 0,
            max: 1,
            unit: 'kg/m²'
          },
          {
            echartsId: 'preCapitaEchart',
            num: 3.21,
            min: 0,
            max: 10,
            unit: 'kg/人'
          }
        ],
        power: [
          {
            echartsId: 'incomeEchart',
            num: 566,
            min: 0,
            max: 1000,
            unit: 'kwh/万元'
          },
          {
            echartsId: 'unitAreaEchart',
            num: 102,
            min: 0,
            max: 1000,
            unit: 'kwh/m²'
          },
          {
            echartsId: 'preCapitaEchart',
            num: 2200,
            min: 0,
            max: 10000,
            unit: 'kwh/人'
          }
        ],
        water: [
          {
            echartsId: 'incomeEchart',
            num: 89,
            min: 0,
            max: 100,
            unit: 'm³/万元'
          },
          {
            echartsId: 'unitAreaEchart',
            num: 2.4,
            min: 0,
            max: 10,
            unit: 'm³/m²'
          },
          {
            echartsId: 'preCapitaEchart',
            num: 46,
            min: 0,
            max: 100,
            unit: 'm³/人'
          }
        ]
      },
      flowDataUnit: {
        usePower: 'kwh',
        uesWater: 't',
        useAll: 'kwh'
      },
      flowDataList: [
        {
          name: '区域维度',
          value: {
            usePower: 1,
            uesWater: 1,
            useAll: 1
          }
        },
        {
          name: '四层',
          value: {
            usePower: 0.01,
            uesWater: 0.02,
            useAll: 0.015
          }
        },
        {
          name: '负一层',
          value: {
            usePower: 0.059,
            uesWater: 0.049,
            useAll: 0.044
          }
        },
        {
          name: '三层',
          value: {
            usePower: 0.352,
            uesWater: 0.351,
            useAll: 0.35
          }
        },
        {
          name: '负二层',
          value: {
            usePower: 0.496,
            uesWater: 0.497,
            useAll: 0.498
          }
        },
        {
          name: '四层',
          value: {
            usePower: 0.066,
            uesWater: 0.067,
            useAll: 0.068
          }
        },
        {
          name: '负一层',
          value: {
            usePower: 0.009,
            uesWater: 0.008,
            useAll: 0.007
          }
        },
        {
          name: '三层',
          value: {
            usePower: 0.008,
            uesWater: 0.008,
            useAll: 0.008
          }
        }
      ],
      flowTableData: {
        day: {
          usePower: 53189.76,
          uesWater: 7523.54,
          useAll: 9279.76
        },
        week: {
          usePower: 302683.54,
          uesWater: 48323.84,
          useAll: 62153.42
        },
        month: {
          usePower: 1125426.45,
          uesWater: 223518.62,
          useAll: 204235.84
        }
      }
      // 临时数据 end
    }
  },
  watch: {
    '$store.state.settings.sidebarCollapse': {
      handler(val) {
        this.$nextTick(() => {
          // 页面重载+插件缩放大概需要800ms
          let echartsDom = ['incomeEchart', 'unitAreaEchart', 'preCapitaEchart']
          setTimeout(() => {
            echartsDom.forEach((item) => {
              echarts.init(document.getElementById(item)).resize()
            })
          }, 900)
        })
      },
      deep: true
    },
    // dragSidebarCollapse
    '$store.state.settings.dragSidebarCollapse': {
      handler(val) {
        this.$nextTick(() => {
          // 页面重载+插件缩放大概需要800ms
          let echartsDom = ['incomeEchart', 'unitAreaEchart', 'preCapitaEchart']
          setTimeout(() => {
            echartsDom.forEach((item) => {
              echarts.init(document.getElementById(item)).resize()
            })
          }, 900)
        })
      },
      deep: true
    }
  },
  mounted() {
    this.initEchartsData()
  },
  methods: {
    initEchartsData() {
      this.echartsDataList = this.echartsTableData[this.selectEnergyTarget]
      this.$nextTick(() => {
        setTimeout(() => {
          this.setEcharts('incomeEchart', this.echartsDataList[0])
          this.setEcharts('unitAreaEchart', this.echartsDataList[1])
          this.setEcharts('preCapitaEchart', this.echartsDataList[2])
        }, 250)
      })
    },
    setEcharts(id, data) {
      let getchart = echarts.init(document.getElementById(id))
      let option = {
        backgroundColor: '#faf9fc',
        tooltip: {
          show: false
        },
        series: [
          {
            name: '123',
            type: 'gauge',
            z: 3,
            startAngle: 200,
            endAngle: -20,
            min: data.min,
            max: data.max,
            radius: '100%',
            center: ['50%', '65%'],
            splitNumber: 5,
            axisLine: {
              show: true,
              lineStyle: {
                width: 18,
                shadowBlur: 0,
                color: [
                  [data.num / data.max, '#3562DB'],
                  [1, '#E4E7ED']
                ]
              }
            },
            axisLabel: {
              show: true,
              distance: -40,
              textStyle: {
                color: '#7F848C',
                fontSize: 15,
                fontWeight: 500
              }
            },
            itemStyle: {
              normal: {
                shadowBlur: 0
              }
            },
            splitLine: {
              // 分隔线
              show: false
            },
            detail: {
              show: false
            },
            data: [{ value: data.num }]
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    getFlowaValue(per) {
      let all = this.flowTableData[this.temporarily.energyFlow][this.selectEnergyFlow]
      let data = per * all
      if (data > 10000) {
        data = (data / 10000).toFixed(2) + '万'
      } else {
        data = data.toFixed(2)
      }
      return data
    },
    // 切换选中类型 假数据假功能
    changeDateType(field, type) {
      this[field] = type
      if (field == 'selectEnergyFlow') {
      } else {
        this.initEchartsData()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.temporary-page {
  height: 100%;
  width: 100%;
  display: flex;
  .water-statistics {
    height: 100%;
    width: 100%;
    border-radius: 4px;
    overflow: hidden;
    margin-top: 1%;
  }
  .gas-statistics {
    height: 100%;
    width: 100%;
    border-radius: 4px;
    overflow: hidden;
  }
  ::v-deep .box-card {
    border-radius: 0;
  }
  .data-right-box {
    position: absolute;
    right: 3%;
    display: flex;
    // align-items: center;
    // justify-content: space-between;
    ::v-deep .el-select {
      width: 80px;
    }
  }
  .data-btns {
    display: flex;
    align-items: center;
    justify-content: space-between;
    & > span {
      width: fit-content;
      padding: 0 8px;
      height: 24px;
      line-height: 24px;
      text-align: center;
      margin: 0 4px;
      background-color: #faf9fc;
      font-size: 14px;
      font-family: 'PingFang SC-Medium', 'PingFang SC';
      border-radius: 4px;
      color: #414653;
      cursor: pointer;
    }
    .active-btn {
      background-color: #e6effc !important;
      color: #3562db !important;
    }
  }
  .statistics-content {
    width: 100%;
    height: 100%;
    background: #faf9fc;
    overflow: hidden;
    position: relative;
    .energy-flow-bg {
      position: absolute;
      top: 16%;
      left: 12%;
      width: 70%;
      height: 67%;
      background: url('~@/assets/images/energy-flow-bg.png') no-repeat;
      background-size: 100% 100%;
    }
    .flow-0,
    .flow-1,
    .flow-2,
    .flow-3,
    .flow-4,
    .flow-5,
    .flow-6,
    .flow-7,
    .flow-8 {
      position: absolute;
      display: flex;
      flex-direction: column;
      > div {
        text-align: center;
      }
    }
    .flow-0 {
      top: 50%;
      transform: translateY(-50%);
      left: 10px;
    }
    .flow-1 {
      top: 10%;
      left: 20%;
    }
    .flow-2 {
      top: 9%;
      left: 40%;
    }
    .flow-3 {
      top: 5%;
      left: 57%;
    }
    .flow-4 {
      top: 50%;
      transform: translateY(-50%);
      right: 2%;
    }
    .flow-5 {
      bottom: 5%;
      left: 53%;
    }
    .flow-6 {
      bottom: 10%;
      left: 30%;
    }
    .flow-7 {
      bottom: 14%;
      left: 13%;
    }
  }
  .statistics-echarts {
    width: 100%;
    height: 100%;
    background: #faf9fc;
    display: flex;
    justify-content: space-around;
    .echarts-box {
      width: 32%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
  }
  #incomeEchart,
  #preCapitaEchart,
  #unitAreaEchart {
    width: 100%;
    height: 50%;
    z-index: 2;
  }
  .echarts-text {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-evenly;
    .echarts-text-num {
      font-size: 24px;
      font-family: Arial, Arial;
      font-weight: bold;
      color: #333333;
    }
    .echarts-text-unit {
      font-size: 14px;
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      color: #7f848c;
    }
    .echarts-text-title {
      font-size: 14px;
      font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
      font-weight: bold;
      color: #333333;
      line-height: 30px;
    }
  }
}
</style>
