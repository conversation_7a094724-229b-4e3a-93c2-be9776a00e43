<template>
  <div class="search-aside">
    <div
      v-for="(item, index) in dateArr"
      :key="index"
      :class="{
        'search-aside-item': true,
        'search-aside-item-active': timeType === item.status
      }"
      @click="onTimeQuick(item)"
    >
      {{ item.name }}
    </div>
    <el-date-picker
      v-model="dateRange"
      type="daterange"
      range-separator="至"
      start-placeholder="开始月份"
      end-placeholder="结束月份"
      :picker-options="pickerOptions"
      :clearable="false"
      prefix-icon=0
      value-format="yyyy-MM-dd"
      @change="timeType=5"
    >
    </el-date-picker>
    <el-button type="primary" plain class="re_btn" @click="reset">重置</el-button>
    <el-button type="primary" @click="submit">查询</el-button>
  </div>
</template>

<script>
import moment from 'moment'
export default {
  props: {
    // 是否自动选择今天
    running: {
      type: Boolean,
      default: true
    },
    timeData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      // 时间类型(0:今日 1:近七日 2:近30天 3:近6个月 4:近12个月 5:自定义)
      // 1 7 30 6 12
      dateArr: [
        {name: '今天', type: 'days', num: 1, status: 0},
        {name: '近7日', type: 'days', num: 6, status: 1},
        {name: '近30日', type: 'days', num: 29, status: 2},
        {name: '近6个月', type: 'months', num: 6, status: 3},
        {name: '近12个月', type: 'months', num: 12, status: 4}
      ],
      // 快捷时间选择
      timeType: null,
      // 选择的时间范围  arr  【开始，结束】
      dateRange: null,
      // X轴数据  未用
      // daysList: [],
      diffDays: null,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      }
    }
  },
  watch: {
    // 时间范围数据变化
    dateRange: function() {
      this.diffTime()
    }
  },
  mounted() {
    if (this.running) {
      this.reset()
    } else {
      this.bringInTime()
    }
    this.toTheRightOfTime()
  },
  methods: {
    async bringInTime() {
      let {startTime, endTime, dateType } = this.timeData
      this.dateRange = [startTime, endTime]
      this.timeType = dateType
      await this.diffTime()
      this.submit()
    },
    submit() {
      let obj = this.dateArr.find(ele => ele.status == this.timeType)
      let data = {
        dateText: obj ? obj.name : '',
        dateType: this.timeType,
        startTime: this.dateRange[0],
        endTime: this.dateRange[1],
        dateRange: this.dateRange,
        diffDays: this.diffDays
      }
      this.$emit('submit', data)
    },
    //  重置到今天
    async reset() {
      await this.onTimeQuick(this.dateArr[0])
      this.submit()
    },
    // 点击快捷选择时间将时间添加到时间组件中
    async onTimeQuick(item) {
      this.timeType = item.status
      this.dateRange = await this.getDateRange(item)
    },
    /**
     * 获取时间差
    */
    diffTime() {
      if (!this.dateRange) return
      let startTime = moment(this.dateRange[0]) // 设置之前时间
      let endTime = moment(this.dateRange[1])   // 获取当前时间
      // let diffHours = endTime.diff(startTime, 'hours')  // 获取到相差小时
      this.diffDays = endTime.diff(startTime, 'days')  // 获取到相差日
      // let diffMonths = endTime.diff(startTime, 'months')  // 获取到相差月
      // if (diffMonths > 0) {   // 相差超过一个月
      //   this.getXAxisData(startTime, diffMonths, 'months', 'YYYY-MM')
      // } else if (diffDays > 0) {  // 相差超过一日
      //   this.getXAxisData(startTime, diffDays, 'days', 'YYYY-MM-DD')
      // } else {    //  相差不超过一日
      //   this.getXAxisData(startTime, diffHours, 'hours', 'HH')
      // }
    },
    /**
     * 获取X轴数据
     * @param {String} start 开始时间   moment格式化数据
     * @param {String} end 结束时间   moment格式化数据
     * @param {Number} num  计算相差数
     * @param {String} diffType 计算相差时间的类型    months|days|hours
     * @param {String} outputType 返回的类型  自选  YYYY-MM-DD HH:mm:ss
     */
    getXAxisData(startTime, num, diffType, outputType) {
      let daysList = []
      daysList.push(startTime.format(outputType))
      for (let i = 1; i <= num; i++) {
        daysList.push(startTime.add(1, diffType).format(outputType))
      }
      this.daysList =  daysList
    },
    /**
    * 获取之前时间-当前时间
    * 当选择今天时  返回今日0点-当前时间
    * @param {String} type 可选 day months 
    * @param {Number} num   向前推进的数量
    * @returns {Array} [之前时间,当前时间]
    */
    getDateRange({type, num}) {
      let start
      if (type == 'days' && num == 1) {
        start = moment().startOf('day').format('YYYY-MM-DD')
      } else {
        if (type == 'months') {
          num -= 1
        }
        start = moment(new Date()).subtract(num, type).startOf(type).format('YYYY-MM-DD')
      }
      let end = moment(new Date()).format('YYYY-MM-DD')
      return [start, end] || []
    },
    /**
     * 将时间选择器的icon放在右边
    */
    toTheRightOfTime() {
      let ElRangeCloseIcon = document.getElementsByClassName('el-range__close-icon')[0]
      ElRangeCloseIcon.innerHTML = '<i class="el-icon-date"></i>'
    }
  }
}
</script>

<style lang="scss" scoped>
.search-aside {
  height: 60px;
  line-height: 60px;
  background: #fff;
  border-radius: 4px;
  display: flex;
  align-items: center;

  .search-aside-item {
    display: inline-block;
    font-size: 14px;
    padding: 0 30px;
    height: 32px;
    line-height: 32px;
    font-family: PingFangSC-Regular;
    color: $color-primary;
    border: 1px solid $color-primary;
    background: #fff;
    margin-right: 20px;
    border-radius: 4px;
    cursor: pointer;

    &:hover,
    &:focus {
      color: #fff;
      font-family: PingFangSC-Regular;
      border-color: $color-primary;
      background-color: $color-primary;
      font-weight: 500;
    }
  }

  .search-aside-item-active {
    color: #fff;
    font-family: PingFangSC-Regular;
    border-color: $color-primary;
    background-color: $color-primary;
    font-weight: 500;
  }

  .re_btn {
    margin-left: 10px;
  }
}
</style>