<!-- 运行监测 -->
<template>
  <PageContainer>
    <div slot="content" class="monitor-content">
      <div class="monitor-content-left">
        <el-tree
          slot="content"
          ref="teamTree"
          v-loading="treeLoading"
          class="team-tree"
          :check-strictly="true"
          :data="teamTreeData"
          :props="defaultProps"
          node-key="id"
          :highlight-current="true"
          :default-expanded-keys="expandedTeam"
          @node-click="handleTeamClick"
        >
          <template #default="{ node, data }">
            <el-tooltip effect="dark" :disabled="showTooltip" :content="node.label" placement="top">
              <div class="nodeLabel" @mouseover="onMouseOver(data.id)">
                <span :ref="`nodeLabel${data.id}`">{{ node.label }}</span>
              </div>
            </el-tooltip>
          </template>
        </el-tree>
      </div>
      <div class="monitor-content-right">
        <div class="right-heade">
          <el-input v-model="searchFrom.surveyName" placeholder="监测项名称" suffix-icon="el-icon-search" clearable />
          <div v-for="item in sortList" :key="item.value" class="searchBtn" :style="{ color: searchFrom.orderBy == item.value ? '#3562db' : '#7f848c' }" @click="sortChange(item)">
            {{ item.label }}
            <span>
              <i class="el-icon-caret-top" :style="{ color: searchFrom.orderBy == item.value && searchFrom.descOrAsc == 0 ? '#3562db' : '#7f848c' }"></i>
              <i class="el-icon-caret-bottom" :style="{ color: searchFrom.orderBy == item.value && searchFrom.descOrAsc == 1 ? '#3562db' : '#7f848c' }"></i>
            </span>
          </div>
          <div style="display: inline-block">
            <el-button type="primary" plain @click="resetForm">重置</el-button>
            <el-button type="primary" @click="searchForm">查询</el-button>
          </div>
          <el-tooltip content="染色区间设置" placement="bottom-end">
            <svg-icon
              name="setup-icon"
              class="setup-icon"
              @click="
                () => {
                  $router.push('envirMonitor/dyeingConfig')
                }
              "
            />
          </el-tooltip>
        </div>
        <div v-loading="listLoading" class="right-content">
          <el-row>
            <el-col v-for="item in listData" :key="item.surveyEntityCode" :xs="24" :md="24" :lg="12" :xl="8">
              <div class="card-content" @click="goToDetails(item)">
                <div class="card-heade">
                  <p class="heade-name">{{ item.surveyEntityName }}</p>
                  <span v-for="alarm in item.policeHistoryGroup" :key="alarm.iphPoliceLevel" class="info-icon-box" @click.stop="viewAlarm(alarm)">
                    <svg-icon name="urgent_icon" class="info-icon" />
                  </span>
                  <span class="update-time">数据更新时间：{{ item.time }}</span>
                </div>
                <div class="card-main">
                  <div v-for="v in item.parameterList" :key="v.parameterId" class="param-item">
                    <div>
                      <svg-icon v-if="v.parameterIcon" :name="v.parameterIcon" class="item-icon" />
                    </div>
                    <div class="item-info">
                      <p class="info-label">{{ v.parameterName }}</p>
                      <p class="info-value">
                        <span class="value-num">{{ v.parameterValue || '-' }}</span>
                        <span class="value-unit">{{ v.parameterUnit || '' }}</span>
                        <span v-if="v.configName" class="configName" :style="{ background: v.colour }">{{ v.configName }}</span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
        <div class="right-footer">
          <el-pagination
            :current-page="pagination.current"
            :page-sizes="pagination.pageSizeOptions"
            :page-size="pagination.size"
            :layout="pagination.layoutOptions"
            :total="pagination.total"
            @size-change="paginationSizeChange"
            @current-change="paginationCurrentChange"
          />
          <alarm-dialog v-if="alarmDialog" :visible.sync="alarmDialog" :alarmTypeItem="alarmTypeItem" />
        </div>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin.js'
import alarmDialog from '../airMenu/components/alarmDialog'
import { monitorTypeList } from '@/util/dict.js'
export default {
  name: 'envirMonitor',
  components: {
    alarmDialog
  },
  mixins: [tableListMixin],
  beforeRouteEnter(to, from, next) {
    let names = []
    to.matched.map((v, i) => {
      if (i > 0) {
        v.components.default.name && names.push(v.components.default.name)
      }
    })
    next((vm) => {
      if (from.query.activeName) {
        vm.activeName = from.query.activeName
      }
      vm.$store.commit('keepAlive/add', names)
    })
  },
  async beforeRouteLeave(to, from, next) {
    // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
    if (!['monitorDetails'].includes(to.name)) {
      // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      projectCode: monitorTypeList.find((item) => item.projectName == '环境监测').projectCode,
      defaultProps: {
        children: 'children',
        label: 'name',
        value: 'code'
      },
      teamTreeData: [], // 树状数据
      expandedTeam: [], // 默认展开树
      checkedTeamData: {}, // 当前选中树
      searchFrom: {
        surveyName: '',
        orderBy: 0, // 排序项
        descOrAsc: '' // 升序: 0 降序: 1
      },
      sortList: [
        { label: '按名称', value: 1 },
        { label: '按温度', value: 2 },
        { label: '按湿度', value: 3 },
        { label: '按PM2.5浓度', value: 4 },
        { label: '按CO2浓度', value: 5 }
      ],
      listData: [],
      treeLoading: true,
      listLoading: true,
      alarmDialog: false,
      alarmTypeItem: {},
      showTooltip: true
    }
  },
  computed: {},
  created() {},
  mounted() {
    this.getTreelist()
  },
  activated() {
    this.getDataList()
  },
  methods: {
    goToDetails(item) {
      this.$router.push({
        path: '/envirMenu/envirMonitor/monitorDetails',
        query: {
          surveyCode: item.surveyEntityCode,
          surveyName: item.surveyEntityName,
          projectCode: this.projectCode,
          assetId: item.assetId
        }
      })
    },
    // 查看报警记录
    viewAlarm(item) {
      this.alarmTypeItem = item
      this.alarmDialog = true
    },
    // 获取检测项列表
    getDataList() {
      let params = {
        ...this.searchFrom,
        projectCode: this.projectCode,
        page: this.pagination.current,
        pageSize: this.pagination.size,
        entityMenuCode: this.checkedTeamData.code
      }
      this.listLoading = true
      this.listData = []
      this.$api
        .GetRealEnvMonitoringList(params)
        .then((res) => {
          this.listLoading = false
          if (res.code == 200) {
            this.listData = res.data.list ? res.data.list : []
            this.pagination.total = res.data.totalCount ? res.data.totalCount : 0
          }
        })
        .catch(() => {
          this.listLoading = false
        })
    },
    getTreelist() {
      this.$api
        .GetEntityMenuList({ projectId: this.projectCode })
        .then((res) => {
          this.treeLoading = false
          let list = this.$tools.transData(res.data, 'code', 'parentId', 'children')
          this.teamTreeData = list
          if (this.teamTreeData.length) {
            this.checkedTeamData = this.teamTreeData[0]
            this.$nextTick(() => {
              this.$refs.teamTree.setCurrentKey(this.teamTreeData[0])
            })
            this.getDataList()
          } else {
            this.pageData.total = 0
          }
        })
        .catch(() => {
          this.treeLoading = false
        })
    },
    // 排序
    sortChange(item) {
      if (item.value == this.searchFrom.orderBy) {
        this.searchFrom.orderBy = item.value
        if (this.searchFrom.descOrAsc == 0) {
          this.searchFrom.orderBy = 0
        }
        this.searchFrom.descOrAsc = this.searchFrom.descOrAsc == 1 ? 0 : ''
      } else {
        this.searchFrom.orderBy = item.value
        this.searchFrom.descOrAsc = 1
      }
      this.searchForm()
    },
    // 树状图点击
    handleTeamClick(data) {
      this.pagination.current = 1
      this.checkedTeamData = data
      this.$refs.teamTree.setCurrentKey(this.checkedTeamData)
      this.getDataList()
    },
    // 重置查询表单
    resetForm() {
      this.searchFrom = {
        surveyName: '',
        orderBy: 0,
        descOrAsc: ''
      }
      this.searchForm()
    },
    // 查询
    searchForm() {
      this.getDataList()
    },
    onMouseOver(id) {
      const parentWidth = this.$refs[`nodeLabel${id}`].parentNode.offsetWidth // 获取元素父级可视宽度
      const contentWidth = this.$refs[`nodeLabel${id}`].offsetWidth // 获取元素可视宽度
      this.showTooltip = contentWidth <= parentWidth
    }
  }
}
</script>
<style lang="scss" scoped>
.monitor-content {
  height: 100%;
  display: flex;
  ::v-deep .monitor-content-left {
    width: 246px;
    height: 100%;
    background: #fff;
    border-radius: 4px;
    padding: 16px;
    overflow: auto;
    .el-tree {
      height: 100%;
    }
    .el-tree-node {
      .el-tree-node__content {
        padding: 6px 0;
        height: auto;
      }
    }
    .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
      background-color: #d9e1f8;
    }
  }
  .monitor-content-right {
    height: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
    .right-heade {
      position: relative;
      padding: 0 40px 10px 10px !important;
      margin-left: 16px;
      background: #fff;
      border-radius: 4px;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      ::v-deep .el-input {
        width: 200px;
      }
      & > div {
        margin-right: 10px;
        margin-top: 10px;
      }
      .searchBtn {
        user-select: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 12px;
        height: 26px;
        background: #f6f5fa;
        border-radius: 4px;
        font-size: 14px;
        color: #7f848c;
        span {
          display: flex;
          flex-direction: column;
          i {
            width: 10px;
            height: 10px;
            font-size: 10px;
            margin-top: -2px;
            margin-bottom: -2px;
          }
        }
      }
      .setup-icon {
        font-size: 18px;
        cursor: pointer;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: 16px;
      }
    }
    .right-content {
      flex: 1;
      overflow: auto;
      p {
        margin: 0;
      }
      ::v-deep .el-loading-mask {
        left: 16px;
      }
      ::v-deep .el-row {
        width: 100%;
        .el-col {
          margin-top: 16px;
          padding-left: 16px;
        }
      }
      .card-content {
        background: #fff;
        border-radius: 4px;
        cursor: pointer;
        .card-heade {
          padding: 0px 16px;
          height: 80px;
          display: flex;
          justify-content: space-evenly;
          flex-direction: column;
          box-shadow: 0 0 5px 0 rgb(18 31 62 / 12%);
        }
        .heade-name {
          font-size: 15px;
          color: #121f3e;
          height: 20px;
        }
        .update-time {
          font-size: 14px;
          font-family: PingFang SC-Regular, PingFang SC;
          font-weight: 400;
          color: #666666;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
        }
        .info-icon-box {
          cursor: pointer;
          .info-icon {
            margin-left: 7px;
            font-size: 18px;
          }
        }
        .card-main {
          padding: 0 0 24px;
          display: flex;
          flex-wrap: wrap;
          align-items: flex-start;
          height: 212px;
          overflow: auto;
          .param-item {
            margin-top: 30px;
            width: 50%;
            padding: 8px 0 8px 16px;
            display: flex;
            align-items: center;
          }
          .item-icon {
            font-size: 30px;
            margin-right: 14px;
          }
          .info-label {
            font-size: 14px;
            color: #414653;
            line-height: 14px;
          }
          .info-value {
            margin-top: 8px;
            display: flex;
            align-items: flex-end;
            flex-wrap: wrap;
          }
          .value-num {
            font-size: 19px;
            font-weight: bold;
            color: #121f3e;
            line-height: 26px;
          }
          .value-unit {
            font-size: 13px;
            font-weight: 500;
            color: #ccced3;
            margin-left: 4px;
          }
          .configName {
            padding: 3px 6px;
            font-size: 13px;
            font-weight: 500;
            color: #fff;
            line-height: 14px;
            border-radius: 4px;
            margin-left: 14px;
            white-space: nowrap;
          }
        }
      }
    }
    .right-footer {
      padding: 10px 0 10px 16px;
      ::v-deep .el-pagination {
        .btn-next,
        .btn-prev {
          background: transparent;
        }
        .el-pager li {
          background: transparent;
        }
      }
    }
  }
}
.nodeLabel {
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
</style>
<style lang="scss">
.monitor-content {
}
</style>
