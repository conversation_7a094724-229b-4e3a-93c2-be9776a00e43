<template>
  <PageContainer>
    <div slot="content" class="role-content" style="height: 100%">
      <div v-loading="leftLoading" class="role-content-left">
        <div class="toptip">
          <span class="green_line"></span>
          <div>
            <span>区域</span>
            <span @click="handleAddRegion">新建</span>
          </div>
        </div>
        <div v-if="regionData.length" class="role-content-left-content">
          <div v-for="item in regionData" :key="item.id" class="role-content-left-content-item" :class="{ selected: selected === item.id }" @click="handleRegionClick(item)">
            <span v-showtipPlus="item.regionName"></span>
            <span @click="handleEditRegion(item)">编辑</span>
            <span @click="handleDeleteRegion(item)">删除</span>
          </div>
        </div>
        <div v-else class="seizeASeat">暂无数据</div>
      </div>
      <div v-loading="rightLoading" class="role-content-right">
        <div class="role-content-right-base">
          <div class="role-content-right-base-title">
            <span class="green_line"></span>
            基础信息
          </div>
          <div class="role-content-right-base-content">
            <el-row :gutter="24">
              <el-col :span="8">
                <span>区域名称：</span>
                <span v-showtipPlus="itemInfo.regionName"></span>
              </el-col>
              <el-col :span="8">
                <span>备注说明：</span>
                <span v-showtipPlus="itemInfo.regionComment"></span>
              </el-col>
            </el-row>
          </div>
        </div>
        <div class="role-content-right-inspectionStandards">
          <div class="role-content-right-inspectionStandards-title">
            <span class="green_line"></span>
            巡检标准
          </div>
          <div class="role-content-right-inspectionStandards-content">
            <el-table :data="itemInfo.planRegionDetailList" border style="width: 100%">
              <el-table-column prop="dictName" label="巡检类型"> </el-table-column>
              <el-table-column prop="dayNumber" label="日（次数）"> </el-table-column>
              <el-table-column prop="weekNumber" label="周（次数）"> </el-table-column>
              <el-table-column prop="monthNumber" label="月（次数）"> </el-table-column>
              <el-table-column prop="quarterNumber" label="季度（次数）"> </el-table-column>
              <el-table-column prop="yearNumber" label="年（次数）"> </el-table-column>
            </el-table>
          </div>
        </div>
        <div class="role-content-right-inspectionPoints">
          <div class="role-content-right-inspectionPoints-title">
            <span class="green_line"></span>
            巡检点信息
          </div>
          <div class="role-content-right-inspectionPoints-content">
            <el-form ref="formRef" :model="formData" class="form">
              <el-form-item prop="taskPointName">
                <el-input v-model="formData.taskPointName" placeholder="巡检点名称"></el-input>
              </el-form-item>
              <el-form-item prop="taskPointTypeId">
                <el-select v-model="formData.taskPointTypeId" placeholder="巡检点类型">
                  <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                </el-select>
              </el-form-item>
              <div class="btns">
                <div>
                  <el-button type="primary" plain @click="handleReset">重置</el-button>
                  <el-button type="primary" @click="handleSearch">查询</el-button>
                  <el-button type="danger" @click="handleBatchDelete">批量删除</el-button>
                </div>
                <el-button type="primary" :disabled="!selected" @click="handleAddInspectionPoints">添加巡检点</el-button>
              </div>
            </el-form>
            <el-table :data="tableData" border style="width: 100%; margin-bottom: 16px" @selection-change="handleSelectionChange">
              <el-table-column type="selection" width="55"></el-table-column>
              <el-table-column prop="taskPointName" label="巡检点名称"> </el-table-column>
              <el-table-column prop="location" label="位置"> </el-table-column>
              <el-table-column prop="taskPointTypeName" label="巡检点类型"> </el-table-column>
              <el-table-column prop="options" label="操作" width="100">
                <template slot-scope="scope">
                  <el-button type="text" size="small" @click="handleDelete(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination
              :current-page="pageInfo.pageNo"
              :page-sizes="[15, 30, 50, 100]"
              :page-size="pageInfo.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="pageInfo.total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            >
            </el-pagination>
          </div>
        </div>
      </div>
      <AddRegion v-if="visible" :current="current" :visible.sync="visible" @success="handleGetRegionData" />
      <addInspectionPoint v-if="pointVisible" v-model="pointVisible" @confirm="handleConfirm" />
    </div>
  </PageContainer>
</template>
<script>
import AddRegion from './components/AddRegion.vue'
import addInspectionPoint from '@/views/equipmentCenter/InspectionManagement/components/addInspectionPoint.vue'
import mixins from './mixins/index.js'
export default {
  components: { AddRegion, addInspectionPoint },
  mixins: [mixins],
  data() {
    return {
      options: [
        {
          label: '空间',
          value: 1
        },
        {
          label: '设备',
          value: 2
        },
        {
          label: '自定义',
          value: 3
        }
      ],
      tableData: [],
      formData: {
        taskPointName: '',
        taskPointTypeId: ''
      },
      pageInfo: {
        pageNo: 1,
        pageSize: 15,
        total: 0
      },
      visible: false,
      selected: '',
      selectdData: [],
      pointVisible: false,
      regionData: [],
      current: null,
      itemInfo: {
        regionName: '',
        regionComment: ''
      },
      leftLoading: false,
      rightLoading: false
    }
  },
  mounted() {
    this.handleGetRegionData()
    this.handleGetSelectData()
  },
  methods: {
    // 获取区域数据
    handleGetRegionData() {
      this.leftLoading = true
      const type = this.$route.path.includes('systemSettings') ? '2' : '1'
      this.$api
        .findPlanRegionList({ type })
        .then((res) => {
          if (res.code == 200) {
            this.regionData = res.data
            if (this.regionData.length) {
              this.selected = this.regionData[0].id
              this.handleRegionClick(this.regionData[0])
            } else {
              this.selected = ''
            }
          }
        })
        .finally(() => {
          this.leftLoading = false
        })
    },
    handleConfirm(e) {
      if (e.length) {
        const list = e
          .map((item) => {
            const { simCode, assetCode, taskPointTypeCode } = item
            const newItem = {}
            if (simCode) {
              newItem.taskPointName = item.localSpaceName
              newItem.taskPointCode = item.localSpaceCode
              newItem.taskPointId = item.id
              newItem.taskPointTypeId = '1' // 1:空间，2:设备，3:自定义
              newItem.taskPointTypeCode = item.id
              newItem.taskPointTypeName = '空间'
              newItem.locationCode = item.simCode
              newItem.location = item.simName
            } else if (assetCode) {
              newItem.taskPointName = item.assetName
              newItem.taskPointCode = item.assetCode
              newItem.taskPointId = item.id
              newItem.taskPointTypeId = '2' // 1:空间，2:设备，3:自定义
              newItem.taskPointTypeName = '设备'
              newItem.taskPointTypeCode = item.id
              newItem.locationCode = item.regionCode
              newItem.location = item.regionName
            } else if (taskPointTypeCode == 'zdy') {
              newItem.taskPointName = item.taskPointName
              newItem.taskPointCode = item.taskPointCode
              newItem.taskPointId = item.id
              newItem.taskPointTypeId = '3' // 1:空间，2:设备，3:自定义
              newItem.taskPointTypeCode = item.id
              newItem.taskPointTypeName = '自定义'
              newItem.locationCode = ''
              newItem.location = ''
            }
            return newItem
          })
          .filter((item) => this.itemInfo.planRegionPointList.findIndex((i) => i.taskPointTypeCode == item.taskPointTypeCode) == '-1')
        this.itemInfo.planRegionPointList.unshift(...list)
        const type = this.$route.path.includes('systemSettings') ? '2' : '1'
        this.$api.savePlanRegion({ ...this.itemInfo, type }).then((res) => {
          if (res.code == 200) {
            this.handleRegionClick({ id: this.selected })
          } else {
            this.$message({
              type: 'error',
              message: res.message
            })
          }
        })
        this.pointVisible = false
      }
    },
    // 计算分页数据
    handleSwitchData() {
      const { pageNo, pageSize } = this.pageInfo
      const index = pageNo * pageSize - pageSize
      this.tableData = this.itemInfo.planRegionPointList.slice(index, pageNo * pageSize)
      this.pageInfo.total = this.itemInfo.planRegionPointList.length
    },
    handleRegionClick(e) {
      this.selected = e.id
      this.rightLoading = true
      this.$api
        .getPlanRegionById({ id: e.id, ...this.formData })
        .then((res) => {
          if (res.code == '200') {
            this.itemInfo = res.data
            this.current = res.data
            this.pageInfo.total = res.data.sum
            this.handleSwitchData()
          } else {
            this.$message({
              type: 'error',
              message: res.message
            })
          }
        })
        .finally(() => {
          this.rightLoading = false
        })
    },
    // 重置
    handleReset() {
      this.$refs.formRef.resetFields()
      this.handleRegionClick({ id: this.selected })
    },
    // 查询
    handleSearch() {
      this.handleRegionClick({ id: this.selected })
    },
    // 批量删除
    handleBatchDelete() {
      if (!this.selectdData.length) {
        this.$message({
          type: 'warning',
          message: '请至少选择一条数据'
        })
        return
      }
      this.$confirm('是否批量删除选择数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const list = this.itemInfo.planRegionPointList.filter((item) => !this.selectdData.some((i) => item.id === i.id))
        this.itemInfo.planRegionPointList = list
        this.$api.savePlanRegion(this.itemInfo).then((res) => {
          if (res.code == 200) {
            this.handleRegionClick({ id: this.selected })
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
          } else {
            this.$message({
              type: 'error',
              message: res.message
            })
          }
        })
      })
    },
    // table 选择
    handleSelectionChange(e) {
      this.selectdData = e
    },
    /**
     * 删除
     */
    handleDelete(row) {
      this.$confirm('是否删除该条数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.itemInfo.planRegionPointList.findIndex((item) => item.id === row.id)
        this.itemInfo.planRegionPointList.splice(index, 1)
        this.$api.savePlanRegion(this.itemInfo).then((res) => {
          if (res.code == 200) {
            this.handleRegionClick({ id: this.selected })
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
          } else {
            this.$message({
              type: 'error',
              message: res.message
            })
          }
        })
      })
    },
    // size change
    handleSizeChange(val) {
      this.pageInfo.pageSize = val
      this.handleSwitchData()
    },
    // current change
    handleCurrentChange(val) {
      this.pageInfo.pageNo = val
      this.handleSwitchData()
    },
    // 添加巡检点
    handleAddInspectionPoints() {
      this.pointVisible = true
    },
    // 新建区域
    handleAddRegion() {
      this.current = null
      this.visible = true
    },
    // 编辑区域
    handleEditRegion() {
      this.visible = true
    },
    // 删除区域
    handleDeleteRegion(item) {
      this.$confirm('是否删除该区域？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.deletePlanRegionById({ id: item.id }).then((res) => {
          if (res.code == 200) {
            this.handleGetRegionData()
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
          }
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.role-content {
  height: 100%;
  display: flex;
  .role-content-left {
    width: 246px;
    height: 100%;
    padding: 10px;
    background: #fff;
    border-radius: 4px;
    margin-right: 12px;
    .toptip {
      div {
        flex: 1;
        display: flex;
        justify-content: space-between;
        span:last-child {
          color: #3562db;
          cursor: pointer;
        }
      }
    }
    .seizeASeat {
      margin: 20px;
      text-align: center;
      color: #666;
    }
    &-content {
      overflow: auto;
      height: calc(100vh - 146px);
      &-item {
        padding: 0 10px;
        display: flex;
        cursor: pointer;
        &:hover {
          background: #d7e0f8;
          > span:not(:first-child) {
            display: inline-block;
          }
          > span:first-child {
            width: 136px;
            display: inline-block;
          }
        }
        > span:first-child {
          width: 64%;
          display: inline-block;
        }
        span {
          height: 40px;
          line-height: 40px;
        }
        > span:not(:first-child) {
          display: none;
          cursor: pointer;
          color: #666;
          font-size: 12px;
          margin-left: 10px;
        }
        > span:last-child {
          color: #db001b;
        }
      }
      .selected {
        color: #3562db;
        background: -webkit-gradient(linear, left top, right top, from(#d9e1f8), to(#fff));
        background: linear-gradient(to right, #d9e1f8, #fff);
        font-weight: 500;
      }
    }
  }
  .role-content-right {
    overflow: auto;
    width: calc(100% - 258px);
    height: 100%;
    padding: 16px 30px;
    background: #fff;
    border-radius: 4px;
    &-base {
      &-content {
        ::v-deep .el-col {
          display: flex;
          height: 40px;
          align-items: center;
          > span:last-child {
            height: 20px;
            display: inline-block;
            width: calc(100% - 80px);
          }
        }
      }
    }
    &-inspectionStandards {
      &-content {
        margin: 16px 0;
      }
    }
    &-inspectionPoints {
      &-content {
        .form {
          display: flex;
          align-items: center;
          width: 100%;
          margin: 16px 0;
          .btns {
            flex: 1;
            display: flex;
            justify-content: space-between;
          }
          ::v-deep .el-form-item {
            margin-bottom: 0;
            margin-right: 10px;
          }
        }
      }
    }
  }
}
</style>
