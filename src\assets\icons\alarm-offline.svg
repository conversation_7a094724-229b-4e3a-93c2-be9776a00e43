<svg width="46" height="55" viewBox="0 0 46 55" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#231;&#131;&#159;" clip-path="url(#clip0_7_2199)">
<g id="&#230;&#164;&#173;&#229;&#156;&#134;&#229;&#189;&#162;&#229;&#164;&#135;&#228;&#187;&#189; 12" filter="url(#filter0_f_7_2199)">
<ellipse cx="23.6379" cy="23.6267" rx="12.0259" ry="12.007" fill="url(#paint0_linear_7_2199)"/>
</g>
<g id="&#231;&#155;&#190;&#231;&#137;&#140;" opacity="0.5">
<path id="&#232;&#183;&#175;&#229;&#190;&#132;" d="M23.25 0L44.04 6.93C45.0614 7.27018 45.7503 8.22598 45.75 9.3025V27.5C45.75 37.625 38.25 46.7925 23.25 55C8.25 46.7925 0.75 37.625 0.75 27.5V9.3C0.750817 8.2244 1.43952 7.26989 2.46 6.93L23.25 0Z" fill="url(#paint1_linear_7_2199)"/>
<mask id="mask0_7_2199" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="46" height="55">
<path id="&#232;&#183;&#175;&#229;&#190;&#132;_2" d="M23.25 0L44.04 6.93C45.0614 7.27018 45.7503 8.22598 45.75 9.3025V27.5C45.75 37.625 38.25 46.7925 23.25 55C8.25 46.7925 0.75 37.625 0.75 27.5V9.3C0.750817 8.2244 1.43952 7.26989 2.46 6.93L23.25 0Z" fill="white"/>
</mask>
<g mask="url(#mask0_7_2199)">
<rect id="&#231;&#159;&#169;&#229;&#189;&#162;" x="23.25" y="-4.375" width="16.875" height="71.25" fill="url(#paint2_linear_7_2199)"/>
</g>
</g>
<ellipse id="&#230;&#164;&#173;&#229;&#156;&#134;&#229;&#189;&#162;" opacity="0.2" cx="23.6379" cy="23.6267" rx="17.069" ry="17.0423" fill="url(#paint3_linear_7_2199)"/>
<ellipse id="&#230;&#164;&#173;&#229;&#156;&#134;&#229;&#189;&#162;&#229;&#164;&#135;&#228;&#187;&#189; 7" cx="23.6379" cy="23.6268" rx="13.9655" ry="13.9437" fill="url(#paint4_linear_7_2199)"/>
<g id="icon-wrapper">
<g id="tips/exclamation-circle-fill">
<path id="Vector" fill-rule="evenodd" clip-rule="evenodd" d="M16.25 23C16.25 18.8579 19.6079 15.5 23.75 15.5C27.8921 15.5 31.25 18.8579 31.25 23C31.25 27.1421 27.8921 30.5 23.75 30.5C19.6079 30.5 16.25 27.1421 16.25 23ZM23 25.25V26.75H24.5V25.25H23ZM24.5 24.5L24.5 19.25H23L23 24.5H24.5Z" fill="#0E1134"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_f_7_2199" x="-42.7536" y="-42.746" width="132.783" height="132.745" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="27.1828" result="effect1_foregroundBlur_7_2199"/>
</filter>
<linearGradient id="paint0_linear_7_2199" x1="11.6121" y1="11.6196" x2="11.6121" y2="35.6337" gradientUnits="userSpaceOnUse">
<stop offset="0.000818639" stop-color="#FF9D5A"/>
<stop offset="1" stop-color="#FF6A2E"/>
</linearGradient>
<linearGradient id="paint1_linear_7_2199" x1="45.75" y1="55.1013" x2="45.9977" y2="0.101713" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF9351" stop-opacity="0.01"/>
<stop offset="1" stop-color="#FF9351" stop-opacity="0.519846"/>
</linearGradient>
<linearGradient id="paint2_linear_7_2199" x1="40.125" y1="-9.52893" x2="23.25" y2="-9.52893" gradientUnits="userSpaceOnUse">
<stop offset="0.000907418" stop-color="#FF853B" stop-opacity="0.01"/>
<stop offset="1" stop-color="#FF853B" stop-opacity="0.22"/>
</linearGradient>
<linearGradient id="paint3_linear_7_2199" x1="6.56897" y1="6.58447" x2="6.56897" y2="40.669" gradientUnits="userSpaceOnUse">
<stop offset="0.000818639" stop-color="#FF9D5A"/>
<stop offset="1" stop-color="#FF6A2E"/>
</linearGradient>
<linearGradient id="paint4_linear_7_2199" x1="9.67236" y1="9.6831" x2="9.67236" y2="37.5704" gradientUnits="userSpaceOnUse">
<stop offset="0.000818639" stop-color="#FF9D5A"/>
<stop offset="1" stop-color="#FF6A2E"/>
</linearGradient>
<clipPath id="clip0_7_2199">
<rect width="45" height="55" fill="white" transform="translate(0.75)"/>
</clipPath>
</defs>
</svg>
