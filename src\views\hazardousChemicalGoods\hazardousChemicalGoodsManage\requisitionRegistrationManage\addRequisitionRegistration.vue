<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="requisitionRegistration-content-title"><i class="el-icon-arrow-left" @click="$router.go(-1)"></i> {{ applyId ? '编辑' : '新建' }}领用申请</div>
      <div class="content_box">
        <el-form ref="formRef" :model="formModel" :rules="rules" label-width="auto">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="领用科室" prop="officesId">
                <el-select filterable clearable v-model="formModel.officesId" placeholder="请选择领用科室" disabled>
                  <el-option v-for="item in officesOptions" :key="item.id" :label="item.deptName" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="申请单号" prop="recordNumber">
                <el-input v-model="formModel.recordNumber" placeholder="系统自动生成" disabled> </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="申请人" prop="createName">
                <el-input v-model="formModel.createName" placeholder="当前登录人" disabled> </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="申请时间:" prop="createTime">
                <el-date-picker
                  v-model="formModel.createTime"
                  type="datetime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  format="yyyy-MM-dd HH:mm:ss"
                  placeholder="选择日期时间"
                  disabled
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="申请人电话" prop="applyPeoplePhone">
                <el-input v-model="formModel.applyPeoplePhone" placeholder="当前登录人电话" disabled> </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="领用类型" prop="receiveType">
                <el-select v-model="formModel.receiveType" filterable placeholder="请选择">
                  <el-option v-for="item in receiveTypeOptions" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="15">
              <el-form-item label="备注" prop="remarks">
                <el-input v-model.trim="formModel.remarks" maxlength="500" show-word-limit type="textarea" placeholder="请输入"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <div class="hazardousChemicalInfo">
            <div class="toptip">
              <span class="green_line"></span>
              危化品信息
              <el-button type="primary" @click="onOperate('select')" icon="el-icon-plus"> 添加危化品 </el-button>
            </div>
          </div>
          <el-table v-loading="tableLoadingStatus" height="400" style="width: 100%" :data="tableData" border stripe class="tableAuto" row-key="id">
            <el-table-column type="index" width="70" label="序号"> </el-table-column>
            <el-table-column show-overflow-tooltip prop="materialCode" label="危化品编码"></el-table-column>
            <el-table-column show-overflow-tooltip prop="materialName" label="危化品名称"></el-table-column>
            <el-table-column show-overflow-tooltip prop="model" label="规格型号"></el-table-column>
            <el-table-column show-overflow-tooltip prop="inventory" label="库存数量"></el-table-column>
            <el-table-column show-overflow-tooltip prop="operateCount" label="领用申请数量" width="160">
              <template #default="{ row }">
                <el-input-number :min="0" :max="parseInt(row.inventory)" size="mini" v-model="row.operateCount"></el-input-number>
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip prop="basicUnitName" label="基础单位"></el-table-column>
            <el-table-column show-overflow-tooltip prop="minStock" label="最低库存"></el-table-column>
            <el-table-column show-overflow-tooltip prop="maxStock" label="最高库存"></el-table-column>
            <!-- <el-table-column show-overflow-tooltip prop="trademark" label="品牌"></el-table-column> -->
            <el-table-column show-overflow-tooltip prop="supplierName" label="供应商"></el-table-column>
            <el-table-column show-overflow-tooltip prop="manufacturerName" label="生产厂家"></el-table-column>
            <el-table-column label="操作">
              <template #default="{ row }">
                <el-button type="text" class="text-red" @click="onOperate('delete', row)">删除</el-button>
                <el-button type="text" @click="onOperate('img', row)">查看图片</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form>
      </div>
      <!--选择危化品 -->
      <template v-if="sectionDialogShow">
        <selectHazardousChemicalDialog
          :sectionDialogShow="sectionDialogShow"
          @submitSectionDialog="submitSectionDialog"
          @closeSectionDialog="closeSectionDialog"
          :selectHcsList="selectHcsList"
        />
      </template>
      <el-dialog :visible.sync="dialogImageVisible">
        <img width="100%" :src="dialogImageUrl" alt="" />
      </el-dialog>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">取消</el-button>
      <el-button type="primary" @click="submitForm('save')" :loading="formLoading">暂存</el-button>
      <el-button type="primary" @click="submitForm('submit')" :loading="formLoading">提交</el-button>
    </div>
  </PageContainer>
</template>
<script>
import moment from 'moment'
import selectHazardousChemicalDialog from './components/selectHazardousChemical.vue'
export default {
  name: 'addRequisitionRegistration',
  components: {
    selectHazardousChemicalDialog
  },
  data() {
    return {
      sectionDialogShow: false,
      // 正常表单
      formModel: {
        id: '',
        officesId: '', // 领用申请
        officesName: '', //领用申请aname
        recordNumber: '', //申请单号
        createName: '', //申请人
        createTime: '', //申请日期
        applyPeoplePhone: '', //申请人电话
        receiveType: '', //领用类型
        remarks: '' //备注
      },
      rules: {
        officesId: [{ required: true, message: '请选择领用申请', trigger: 'change' }],
        receiveType: [{ required: true, message: '请选择领用类型', trigger: 'change' }]
      },
      officesOptions: [], //领用申请
      receiveTypeOptions: [], //领用类型
      tableLoadingStatus: false,
      tableData: [],
      formLoading: false,
      applyId: '',
      dialogImageVisible: false, //图片
      dialogImageUrl: '',
      selectHcsList: []
    }
  },

  mounted() {
    this.init()
    const userInfo = this.$store.state.user.userInfo.user
    this.formModel.createTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
    this.formModel.createName = userInfo.staffName
    this.formModel.applyPeoplePhone = userInfo.phone
    if (this.$route.query.applyId) {
      this.applyId = this.$route.query.applyId
      this.getApplyInfoData()
    }
  },
  methods: {
    //初始化
    init() {
      this.getDeptList()
      this.getReceiveTypeDataFn()
    },
    //获取领用类型
    getReceiveTypeDataFn() {
      const userInfo = this.$store.state.user.userInfo.user
      let params = {
        typeCode: 'LYLX',
        status: '0',
        userId: userInfo.staffId,
        userName: userInfo.staffName
      }
      this.$api.getReceiveApplyRecordType(params).then((res) => {
        if (res.code == '200') {
          this.receiveTypeOptions = res.data
          this.formModel.receiveType = this.receiveTypeOptions[0].id
        }
      })
    },
    //  获取领用申请
    getDeptList() {
      this.$api.getSelectedDept().then((res) => {
        if (res.code == 200) {
          this.officesOptions = res.data
          this.formModel.officesId = this.$store.state.user.userInfo.user.deptId
          this.formModel.officesName = this.$store.state.user.userInfo.user.deptName
        }
      })
    },
    //获取领用申请name
    officesChange(val) {
      if (val) {
        this.formModel.officesName = this.officesOptions.find((item) => item.id == val).deptName
      }
    },
    //获取领用申请详情
    getApplyInfoData() {
      const userInfo = this.$store.state.user.userInfo.user
      let params = {
        id: this.applyId,
        userId: userInfo.staffId,
        userName: userInfo.staffName
      }
      this.$api.getReceiveApplyRecordById(params).then((res) => {
        this.tableLoading = false
        if (res.code == '200') {
          this.formModel = res.data
          this.tableData = res.data.materialRecordList
        }
      })
    },
    // 添加危化品弹窗
    closeSectionDialog() {
      this.sectionDialogShow = false
    },
    submitSectionDialog(list) {
      this.tableData = [...this.tableData, ...list]
      this.sectionDialogShow = false
    },
    onOperate(type, row) {
      if (type === 'select') {
        if (this.tableData && this.tableData.length) {
          this.selectHcsList = this.tableData
        }
        this.sectionDialogShow = true
      } else if (type === 'delete') {
        this.tableData = this.tableData.filter((obj) => obj.id !== row.id)
      } else if (type === 'img') {
        if (!row.materialPhoto) {
          this.$message.error('危化品暂无图片')
          return
        }
        this.dialogImageVisible = true
        this.dialogImageUrl = this.$tools.imgUrlTranslation(row.materialPhoto)
      }
    },
    // 点击确定
    submitForm(type) {
      this.$refs['formRef'].validate((valid) => {
        if (valid) {
          this.formLoading = true
          let params = {
            ...this.formModel,
            userId: this.$store.state.user.userInfo.user.staffId,
            userName: this.$store.state.user.userInfo.user.staffName,
            materialRecordArrayStr: JSON.stringify(this.tableData),
            status: type === 'save' ? '1' : '2',
            userType: 1
          }
          this.$api.saveReceiveApplyRecordData(params).then((res) => {
            this.formLoading = false
            if (res.code == '200') {
              this.$message.success(res.message)
              this.$router.go(-1)
            } else {
              this.$message.error(res.message)
            }
          })
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.container-content > div {
  background-color: #fff;
  height: 100%;
  position: relative;
  .requisitionRegistration-content-title {
    border-bottom: 1px solid #e4e7ed;
    padding: 12px 24px;
    cursor: pointer;
    margin-bottom: 24px;
  }
  .content_box {
    padding: 0 24px;
    background: #fff;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    height: 100%;
    .hazardousChemicalInfo {
      display: flex;
    }
    .toptip {
      box-sizing: border-box;
      height: 50px;
      width: 50% !important;
      line-height: 50px;
      display: flex;
      font-size: 16px;
      align-items: center;
      border: none;
      .el-button {
        margin-left: 20px !important;
      }
    }
    .green_line {
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 2px;
      background: #3562db;
      margin-right: 10px;
      vertical-align: middle;
    }
    .text-red {
      color: #ff1919;
    }
  }
  .el-input,
  .el-select,
  .el-cascader {
    width: 80%;
  }
}
</style>

