<template>
  <el-dialog v-if="visible" v-dialogDrag :title="pageTitle" width="60%" :visible.sync="dialogShow" custom-class="model-dialog" :before-close="closeDialog">
    <div class="sapce_content">
      <div class="search-from">
        <el-input v-model="searchForm.idOrName" placeholder="搜索班次名或创建人" clearable style="width: 200px"></el-input>
        <div style="display: inline-block; padding-left: 10px">
          <el-button type="primary" plain @click="resetEvent">重置</el-button>
          <el-button type="primary" @click="searchEvent">查询</el-button>
        </div>
      </div>
      <TablePage
        ref="tablePage"
        v-loading="tableLoading"
        class="tablePage"
        height="calc(100% - 80px)"
        :pageData="pageData"
        :pageProps="pageProps"
        border
        :tableColumn="tableColumn"
        :data="tableData"
        @pagination="paginationChange"
      ></TablePage>
      <selectShiftDetailDialog
        v-if="selectShiftDetailDialogShow"
        :visible.sync="selectShiftDetailDialogShow"
        :rowDetail="defaultCheckedShiftData"
        @closeDialog="() => (selectShiftDetailDialogShow = false)"
      />
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="submit">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script lang="jsx">
export default {
  name: 'selectShiftDialog',
  components: {
    selectShiftDetailDialog: () => import('../component/selectShiftDetailDialog.vue')
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'add'
    },
    defaultChecked: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      checkRadio: '',
      assetsCheckData: {},
      searchForm: {
        idOrName: ''
      },
      tableLoading: false,
      tableColumn: [
        {
          prop: 'select',
          label: '',
          align: 'center',
          width: 60,
          render: (h, row) => {
            return (
              <el-radio v-model={this.checkRadio} label={row.row.id} onChange={() => this.handlePrePlanEvent(row.row)}>
                <span></span>
              </el-radio>
            )
          }
        },
        {
          prop: 'name',
          label: '班次名称'
        },
        {
          prop: 'time',
          label: '考勤时间'
        },
        {
          prop: 'createdName',
          label: '创建人'
        },
        {
          prop: 'updateTime',
          label: '操作时间'
        },
        {
          prop: 'operation',
          label: '操作',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span class="operationBtn-span" onClick={() => this.handleTableEvent('detail', row.row)}>
                  查看
                </span>
              </div>
            )
          }
        }
      ],
      tableData: [],
      pageData: {
        current: 1,
        size: 15,
        total: 0
      },
      pageProps: {
        page: 'current',
        pageSize: 'size',
        total: 'total'
      },
      selectShiftDetailDialogShow: false,
      defaultCheckedShiftData: {}
    }
  },
  computed: {
    dialogShow() {
      return this.visible
    },
    pageTitle() {
      return '选择班次'
    }
  },
  mounted() {
    this.checkRadio = this.defaultChecked.id
    this.getDataList()
  },
  methods: {
    // 获取列表数据
    getDataList() {
      this.tableData = []
      this.tableLoading = true
      const params = {
        pageSize: this.pageData.size,
        page: this.pageData.current,
        ...this.searchForm
      }
      this.$api.supplierAssess
        .queryShiftInfoByPage(params)
        .then((res) => {
          this.tableLoading = false
          if (res.code === '200') {
            this.tableData = res.data.records
            this.pageData.total = res.data.total
          } else {
            this.tableData = []
            this.pageData.total = 0
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getDataList()
    },
    handleTableEvent(type, row) {
      this.defaultCheckedShiftData = {
        id: row.id
      }
      this.selectShiftDetailDialogShow = true
    },
    // 表单搜索按钮点击
    searchEvent() {
      this.getDataList()
    },
    // 表单重置按钮点击
    resetEvent() {
      this.pageData.current = 1
      this.searchForm = {
        idOrName: ''
      }
      this.searchEvent()
    },
    handlePrePlanEvent(currentRow) {
      this.assetsCheckData = currentRow ?? this.assetsCheckData
    },
    closeDialog() {
      this.$emit('closeDialog')
    },
    submit() {
      if (!Object.keys(this.assetsCheckData).length) {
        this.$message({
          message: '请选择班次',
          type: 'warning'
        })
      } else {
        this.$emit('submitDialog', {
          ...this.assetsCheckData,
          weekNum: this.defaultChecked.weekNum // 根据父页面传过来列表的排序或者批量配置的班次
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.model-dialog {
  width: 40% !important;
  .sapce_content {
    background: #fff;
    padding: 10px;
    border-radius: 4px;
    height: 400px;
    width: 100%;
    overflow: hidden;
    .search-from {
      height: 40px;
    }
  }
}
::v-deep .el-checkbox__input {
  display: inline-block !important;
}
</style>
