<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    title="角色分配"
    width="30%"
    :visible.sync="visible"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <div class="dialog-content">
      <TablePage
        ref="roleTable"
        v-loading="tableLoading"
        :showPage="true"
        :tableColumn="tableColumn"
        :data="tableData"
        height="calc(100% - 40px)"
        :pageData="pageData"
        @pagination="paginationChange"
        @selection-change="handleSelectionChange"
      />
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="confirm('ruleForm')">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  name: 'roleAssiDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    selectUserList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      tableLoading: false,
      tableData: [],
      multipleSelection: [],
      tableColumn: [
        {
          type: 'selection',
          align: 'center'
        },
        {
          prop: 'serialNumber',
          label: '序号',
          formatter: (scope) => {
            // 当前分页重置
            // return scope.$index + 1
            // 跨分页序号
            return (this.pageData.page - 1) * this.pageData.pageSize + scope.$index + 1
          }
        },
        {
          prop: 'roleName',
          label: '角色名称'
        },
        {
          prop: 'roleCode',
          label: '角色编码'
        }
      ],
      pageData: {
        page: 1,
        pageSize: 15,
        total: 0
      }
    }
  },
  mounted() {
    this.getRoleList()
  },
  methods: {
    confirm() {
      if (!this.multipleSelection.length) {
        this.$message({message: '请选择分配角色', type: 'error'})
        return
      }
      let param = {
        id: this.selectUserList.map(item => item.id).join(','),
        roleId: this.multipleSelection.map(item => item.id).join(',')
      }
      this.$api.UpdateUserAndRole(param).then(res => {
        if (res.code == '200') {
          this.$message({ message: '角色分配成功', type: 'success'})
          this.$emit('update:visible', !this.visible)
        }
      })
    },
    // 获取角色列表
    getRoleList() {
      let param = {
        pageSize: this.pageData.pageSize,
        page: this.pageData.page
      }
      let newArr = []
      this.$api.GetRoleList(param).then(res => {
        if (res.code == '200') {
          this.tableData = res.data.records
          this.pageData.total = res.data.total
          if (this.selectUserList.length == 1) {
            this.tableData.forEach(item => {
              if (this.selectUserList[0].roleId.split(',').map(v => Number(v)).includes(item.id)) {
                newArr.push(item)
              }
            })
            this.$nextTick(() => {
              newArr.forEach(item => {
                this.$refs.roleTable.toggleRowSelection(item)
              })
            })
          }
        }
      })
    },
    // 多选
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    paginationChange(pagination) {
      this.pageData.page = pagination.page
      this.pageData.pageSize = pagination.pageSize
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', !this.visible)
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .model-dialog {
  // min-height: 60vh;
}

.dialog-content {
  width: 100%;
  background: #fff;
  padding: 10px;
  min-height: 300px;
  max-height: 400px;
}
</style>
