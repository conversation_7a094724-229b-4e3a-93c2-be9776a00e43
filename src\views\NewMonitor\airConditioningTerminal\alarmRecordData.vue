<template>
    <PageContainer>
        <div slot="header">
            <div class="topalarm">
                <p v-for="(item, index) in dataList" :key="index">
                    <em v-if="item.level == 3"><img src="../../../assets/images/newMonitor/significance.png"
                            alt=""><span>
                            重要警情 <i>{{ item.count }}</i></span></em>
                    <em v-if="item.level == 2"><img src="../../../assets/images/newMonitor/urgency.png" alt=""><span>
                            紧急警情 <i>{{ item.count }}</i></span></em>
                    <em v-if="item.level == 1"><img src="../../../assets/images/newMonitor/ordinary.png" alt=""><span>
                            一般警情 <i>{{ item.count }}</i></span></em>
                    <em v-if="item.level == 0"><img src="../../../assets/images/newMonitor/inform.png" alt=""><span>
                            通知警情 <i>{{ item.count }}</i></span></em>
                </p>
            </div>
            <div class="searchForm">
                <div class="search-box">
                    <el-select v-model="searchFrom.alarmLevel" placeholder="请选择报警等级" class="ml-16">
                        <el-option v-for="item in policeLevelList" :key="item.value" :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                    <el-select v-model="searchFrom.alarmStatus" placeholder="请选择处理状态" class="ml-16">
                        <el-option v-for="item in handleList" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                    <el-date-picker v-model="dataRange" type="daterange" unlink-panels range-separator="至"
                        start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd" class="ml-16">
                    </el-date-picker>
                    <div class="ml-16">
                        <el-button type="primary" plain @click="reset">重置</el-button>
                        <el-button type="primary" @click="search">查询</el-button>
                    </div>
                </div>
            </div>
        </div>
        <div slot="content" ref="contentRef" class="table-box">
            <div class="tableContainer">
                <TablePage ref="table" v-loading="tableLoading" :showPage="true" :tableColumn="tableColumn" height="320"
                    :data="tableData" :pageData="pageData" @pagination="paginationChange" :pageProps="pageProps" />
            </div>
        </div>
    </PageContainer>
</template>
<script lang="jsx">
export default {
    name: 'alarmRecordData',
    data() {
        return {
            tableLoading: false,
            searchFrom: {
                objectId: '',//设备id
                alarmLevel: '', // 报警等级
                alarmStatus: '', // 报警状态
                startTime: "",
                endTime: "",
            }, // 搜索条件
            dataRange: [], // 时间范围
            policeLevelList: [{ value: 0, label: '通知' }, { value: 1, label: '一般' }, { value: 2, label: '紧急' }, { value: 3, label: '重要' },],
            handleList: [{ value: 0, label: '未处理' }, { value: 1, label: '已处理' }],
            tableColumn: [
                {
                    prop: '',
                    label: '序号',
                    formatter: (scope) => {
                        return (this.pageData.pageNo - 1) * this.pageData.pageSize + scope.$index + 1
                    },
                    width: 60
                },
                {
                    prop: 'alarmStartTime',
                    label: '报警时间'
                },
                {
                    prop: 'alarmObjectName',
                    label: '报警对象名称'
                },
                {
                    prop: 'alarmLevel',
                    label: '报警等级',
                    formatter: (row) => {
                        switch (row.row.alarmLevel) {
                            case 0:
                                return '通知';
                            case 1:
                                return '一般';
                            case 2:
                                return '紧急';
                            case 3:
                                return '重要';
                            default:
                                return '未知'; // 处理未定义的情况
                        }
                    }
                },
                {
                    prop: 'alarmType',
                    label: '报警类型',

                },
                {
                    prop: 'alarmSpaceName',
                    label: '位置'
                },
                {
                    prop: 'alarmStatus',
                    label: '处理状态',
                    formatter: (row) => {
                        switch (row.row.alarmStatus) {
                            case 0:
                                return '未处理';
                            case 1:
                                return '处理中';
                            case 2:
                                return '已关闭';
                            default:
                                return '未知'; // 处理未定义的情况
                        }
                    }
                },
                {
                    prop: 'operation',
                    label: '操作',
                    width: 100,
                    render: (h, row) => {
                        return (
                            <div class="operationBtn">
                                <span class="operationBtn-span" onClick={() => this.handleListEvent(row.row)}>
                                    详情
                                </span>
                            </div>
                        )
                    }
                }
            ],
            tableData: [],
            pageData: {
                pageNo: 1,
                pageSize: 15,
                total: 0
            },
            pageProps: {
                page: 'pageNo',
                pageSize: 'pageSize',
                total: 'total'
            },
            dataList: []
        }
    },
    mounted() {
        this.searchFrom.objectId = this.$route.query.id
        this.getApplicationList()
        this.bjxxData()
    },
    methods: {
        bjxxData() {
            this.$api.selectAlarmLevelByObjectId({ objectId: this.searchFrom.objectId }).then((res) => {
                if (res.code === "200") {
                    this.dataList = res.data
                } else {
                    this.hasChart = false
                }
            })
        },
        // 获取报警记录列表
        getApplicationList() {
            let param = {
                objectId: this.$route.query.id,
                alarmLevel: this.searchFrom.alarmLevel,
                alarmStatus: this.searchFrom.alarmStatus,
                startTime: this.dataRange[0],
                endTime: this.dataRange[1],
                pageSize: this.pageData.pageSize,
                pageNo: this.pageData.pageNo
            }
            this.tableLoading = true
            this.$api
                .getAssetsAlertPage(param)
                .then((res) => {
                    if (res.code == 200) {
                        this.tableData = res.data.records
                        this.pageData.total = res.data.total
                    }
                    this.tableLoading = false
                })
                .catch(() => {
                    this.tableLoading = false
                })
        },
        // 分页
        paginationChange(pagination) {
            Object.assign(this.pageData, pagination)
            this.getApplicationList()
        },
        // 重置查询
        reset() {
            Object.assign(this.searchFrom, {
                objectId: '',//设备id
                alarmLevel: '', // 报警等级
                alarmStatus: '', // 报警状态
                startTime: "",
                endTime: "",
            })
            this.dataRange = [] // 时间范围
            this.getApplicationList()
        },
        // 查询
        search() {
            this.searchFrom.startTime = this.dataRange[0]
            this.searchFrom.endTime = this.dataRange[1]
            this.getApplicationList()
        },
        // 查看
        handleListEvent(row) {
            this.$router.push({
                path: '/allAlarm/alarmDetail',
                query: {
                    alarmId: row.alarmId
                }
            })
        },
    }
}
</script>
<style lang="scss" scoped>
.topalarm {
    display: flex;
    justify-content: space-between;
    gap: 15px;
    margin-left: -10px;

    p {
        flex: 1;
        text-align: center;
        align-items: center;
        background: #faf9fc;
        color: #808081;
        padding: 10px 0;

        em {
            display: flex;
            justify-content: center;
            align-items: center;
            font-style: normal;
        }

        img {
            margin-right: 8px;
        }

        i {
            color: #333;
            display: block;
            font-weight: bold;
            font-size: 20px;
            font-style: normal;
        }

    }
}

.searchForm {
    display: flex;
    height: 50px;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin-left: -28px;

    .el-input {
        width: 200px;
        margin-left: 16px;
    }

    >div {
        margin-right: 20px;
    }
}

.search-box {
    display: flex;
    align-items: center;
}

.container-content>div {
    height: 100%;
    margin-top: 16px;
}

.el-table {
    margin-bottom: 12px;
}

::v-deep .el-table__row:hover {
    cursor: pointer;
}

.table-box {
    background-color: #fff;
    height: 100%;

    .tableContainer {
        height: 100%;

        .el-table {
            height: calc(100% - 96px) !important;
        }
    }
}

.diaContent {
    width: 100%;
    max-height: 550px !important;
    overflow: auto;
    background-color: #fff !important;

    .el-input,
    .el-select,
    .el-cascader {
        width: 300px;
    }
}

.form-item {
    display: inline-block;
    margin-right: 20px;
}

.inputWidth {
    width: 820px;
}

.ml-16 {
    margin-left: 16px;
}

.dialog .el-dialog {
    width: 60% !important;
}
</style>