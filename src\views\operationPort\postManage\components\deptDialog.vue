<template>
  <el-dialog v-if="visible" v-dialogDrag :modal="false" :close-on-click-modal="false" :title="title" width="40%"
    :visible.sync="visible" custom-class="model-dialog" :before-close="closeDialog">
    <div class="dialog-content">
      <div class="content-left">
        <el-input v-model="treeFilter" placeholder="请输入关键字" clearable></el-input>
        <div v-loading="treeLoading" class="left-tree">
          <el-tree ref="tree" :data="deptTreeData" node-key="id" size="small" default-expand-all
            :highlight-current="true" show-checkbox :check-strictly="true" :props="treeProps"
            :filter-node-method="filterNode" @check=" checkData" @check-change="handleCheckChange">
          </el-tree>
        </div>
      </div>
      <div class="content-right">
        <div class="right-title">
          <p class="title">已选：<span>{{selectDept.length}}</span>个科室</p>
          <p class="clear" @click="clear">清空</p>
        </div>
        <div class="right-list">
          <div v-for="(item, index) in selectDept" :key="item.id" class="list-item">
            <span>{{ item.deptName }}</span>
            <i class="el-icon-close" @click="remove(item, index)"></i>
          </div>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="confirm('ruleForm')">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { transData } from '@/util'
export default {
  name: "deptDialog",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "所属科室",
    },
    isRadio: {
      type: Boolean,
      default: false,
    },
    nature: {
      type: String,
      default: "1",
    },
    isNotmultiSector: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      treeFilter: '',
      treeLoading: false,
      deptTreeData: [],
      treeProps: {
        label: 'deptName',
        children: 'child'
      },
      selectDept: [],
      lastTreeParentId: '',
    };
  },
  watch: {
    treeFilter(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    this.getUnitListFn()
  },
  methods: {
    // 移除
    remove(node, index) {
      this.$refs.tree.setChecked(node.id, false)
      this.selectDept.splice(index, 1);
    },
    // 清空
    clear() {
      this.selectDept = []
      this.$nextTick(() => {
        this.$refs.tree.setCheckedKeys([])
      })
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.deptName.indexOf(value) !== -1;
    },
    checkData(data, checked, indeterminate) {
      if (this.isRadio) {
        this.selectDept = [data]
        this.$refs.tree.setCheckedKeys([data.id]);
      } else {
        this.selectDept = this.$refs.tree.getCheckedNodes()
      }
    },
    // 获取单位列表
    getUnitListFn() {
      this.treeLoading = true
      this.$api.supplierAssess.getDeptTreeData({ name: '' }).then((res) => {
        this.treeLoading = false
        if (res.code == 200) {
          let arr = res.data
          arr.forEach(v => {
            v.disabled = true
          })
          this.deptTreeData = arr
        }
      })
    },
    confirm() {
      if (!this.selectDept.length) {
        this.$message({ message: "请选择科室", type: "error" });
        return;
      }
      this.$emit("selectDept", this.selectDept);
      this.closeDialog()
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit("update:visible", !this.visible);
    },
    handleCheckChange(node, check, childCheck) {
      if (this.isNotmultiSector) {
        let checkedArr = this.$refs.tree.getCheckedNodes();
        if (check) {
          if (checkedArr.length > 1) {
            for (let i = 0; i < checkedArr.length; i++) {
              /*
            在已选节点中(包含最后次勾选不在原先同层节点的数据)判断当前勾选的节点是否跟原先的节点同层
            */
              if (node.id != checkedArr[i].id) {
                this.lastTreeParentId = checkedArr[i].umId;
              }
            }
            if (node.umId != this.lastTreeParentId) {
              this.$message.warning("选择的节点不在同一层级请重新选择");
              // 移除已选中的并且不在同一层的节点
              this.$refs.tree.setChecked(node.id, false, false);
              return;
            }
          }
        } else {
          if (checkedArr.length == 0) {
            this.lastTreeParentId = null;
          }
        }
      }
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .model-dialog {
  // min-height: 60vh;
}

.dialog-content {
  width: 100%;
  background: #fff;
  padding: 24px 0px;
  display: flex;
  p {
    margin: 0;
  }
  .content-left {
    height: 480px;
    flex: 1;
    padding: 0px 24px;
    border-right: 1px solid #e4e7ed;
    display: flex;
    flex-direction: column;
    .left-tree {
      flex: 1;
      margin-top: 16px;
      overflow: auto;
    }
  }
  .content-right {
    height: 480px;
    flex: 1;
    padding: 0px 24px;
    display: flex;
    flex-direction: column;
    .right-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 10px;
      .title {
        font-size: 14px;
        color: #7f848c;
        line-height: 22px;
        span {
          color: #333333;
          margin: 0 8px;
        }
      }
      .clear {
        cursor: pointer;
        font-size: 12px;
        color: #3562db;
      }
    }
    .right-list {
      flex: 1;
      overflow: auto;
      .list-item {
        transition: all 0.3s;
        padding: 8px 12px;
        border-radius: 4px;
        color: #333333;
        margin-top: 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        i {
          cursor: pointer;
          color: #666666;
          font-size: 16px;
        }
      }
      .list-item:hover {
        background: #e6effc;
      }
    }
  }
}
</style>
