<template>
  <PageContainer class="positioningTerminalManage-list">
    <template #content>
      <div class="positioningTerminalManage-list-content">
        <el-form ref="formRef" class="positioningTerminalManage-list__form" :model="searchForm" inline>
          <el-form-item prop="name">
            <el-input v-model="searchForm.name" placeholder="搜索设备名称、资产编码、SN" style="width: 250px" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" plain @click="onReset">重置</el-button>
            <el-button type="primary" @click="onSearch">查询</el-button>
          </el-form-item>
        </el-form>
        <div class="positioningTerminalManage-list__table_actions">
          <el-button type="primary" icon="el-icon-plus" @click="onOperate('addDevice')">添加设备</el-button>
        </div>
        <div class="positioningTerminalManage-list__table">
          <TablePage
            ref="table"
            v-loading="tableLoading"
            :showPage="true"
            height="calc(100% - 0px)"
            border
            stripe
            row-key="id"
            :tableColumn="tableColumn"
            :data="tableData"
            :pageData="pagination"
            @pagination="paginationChange"
          />
        </div>
      </div>
      <positioningTerminalDialog
        v-if="positioningTerminalShow"
        :isRadio="false"
        :visible="positioningTerminalShow"
        @close="closeDialog"
        @submitDialog="submitDialog"
      ></positioningTerminalDialog>
    </template>
  </PageContainer>
</template>
<script>
import positioningTerminalDialog from './positioningTerminalDialog.vue'
export default {
  components: { positioningTerminalDialog },
  data() {
    return {
      positioningTerminalShow: false,
      tableLoading: false,
      tableData: [],
      tableColumn: [
        {
          prop: 'name',
          label: '设备名称',
          showOverflowTooltip: true
        },
        {
          prop: 'code',
          label: '资产编码',
          showOverflowTooltip: true
        },
        {
          prop: 'sn',
          label: 'SN',
          showOverflowTooltip: true
        },
        {
          prop: 'sysTypeName',
          label: '专业类别',
          showOverflowTooltip: true
        },
        {
          prop: 'typeName',
          label: '系统类别',
          showOverflowTooltip: true
        },
        {
          prop: 'bindState',
          label: '绑定状态',
          render: (h, { row }) => {
            if (row.bindState === 1) {
              return h('el-tag', { props: { size: 'mini', type: 'success' } }, '已绑定')
            } else if (row.bindState === 0) {
              return h('el-tag', { props: { size: 'mini', type: 'danger' } }, '未绑定')
            }
            return ''
          }
        },
        {
          label: '操作',
          width: '130px',
          render: (h, { row }) => {
            return h('div', [
              h(
                'el-button',
                {
                  props: { type: 'text' },
                  on: { click: () => this.onOperate('details', row) }
                },
                '详情'
              ),
              h(
                'el-button',
                {
                  props: { type: 'text' },
                  class: 'text-red',
                  on: { click: () => this.onOperate('remove', row) }
                },
                '移除'
              )
            ])
          }
        }
      ],
      // 选中的行
      selectionList: [],
      pagination: {
        page: 1,
        pageSize: 15,
        total: 0
      },
      searchForm: { name: '' }
    }
  },
  mounted() {
    this.getDataList()
  },
  methods: {
    onReset() {
      this.searchForm = { name: '' }
      this.onSearch()
    },
    onSearch() {
      this.pagination.page = 1
      this.getDataList()
    },
    onOperate(type, row) {
      switch (type) {
        case 'details':
          this.$router.push({
            name: 'addDevice',
            query: {
              type: 'details',
              id: row.inspId,
              assetsId: row.assetsId
            }
          })
          break
        case 'remove':
          this.$confirm('移除设备会自动解绑，是否继续?', '操作确认', {
            cancelButtonClass: 'el-button--primary is-plain',
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.removeDevice(row)
          })
          break
        case 'addDevice':
          this.positioningTerminalShow = true
          break
      }
    },
    submitDialog() {
      this.positioningTerminalShow = false
      this.onSearch()
    },
    // 移除定位设备
    removeDevice(row) {
      let params = {
        id: row.id
      }
      this.$api.supplierAssess.removeLocateAssetsData(params).then((res) => {
        if (res.code == 200) {
          this.$message.success(res.msg)
          this.onReset()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    closeDialog() {
      this.positioningTerminalShow = false
    },
    getDataList() {
      let params = {
        name: this.searchForm.name,
        dutyPostCode: '',
        page: this.pagination.page,
        pageSize: this.pagination.pageSize
      }
      this.$api.supplierAssess.queryTerminalByDutyPostData(params).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.records
          this.pagination.total = res.data.total
        }
      })
    },
    paginationSizeChange(val) {
      this.pagination.pageSize = val
      this.getDataList()
    },
    paginationCurrentChange(val) {
      this.pagination.page = val
      this.getDataList()
    },
    paginationChange(pagination) {
      Object.assign(this.pagination, pagination)
      this.getDataList()
    }
  }
}
</script>
<style lang="scss" scoped>
.positioningTerminalManage-list {
  padding: 15px;
  box-sizing: border-box;
  overflow-y: auto;
  background-color: #fff;
  &-content {
    height: 100%;
  }
  &__table_actions {
    margin-bottom: 15px;
  }
  &__table {
    height: calc(100% - 160px);
    margin-bottom: 10px;
  }
}
</style>
