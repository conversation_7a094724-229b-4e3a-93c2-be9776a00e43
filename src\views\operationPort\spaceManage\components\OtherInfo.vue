<template>
  <div>
    <el-row>
      <el-col :span="12">
        <div class="item">
          <span class="item-title">配置</span>
          <span class="item-content">{{ detailsInfo.ratedPower || '--' }}</span>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="item">
          <span class="item-title">备注</span>
          <span class="item-content">{{ detailsInfo.remarks || '--' }}</span>
        </div>
      </el-col>
      <el-col :span="24">
        <div class="item">
          <span class="item-title">图片</span>
          <span v-if="imgArr.length === 0" class="item-content">--</span>
          <div v-else>
            <el-image
              v-for="(item, index) in imgArr"
              :key="index"
              style="width: 100px; height: 100px"
              :src="item"
              :preview-src-list="imgArr">
            </el-image>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
export default {
  name: 'OtherInfo',
  props: ['detailsInfo', 'imgArr'],
  data() {
    return {}
  },
  mounted() {},
  methods: {}
}
</script>

<style scoped lang="scss">
  .item {
    margin-bottom: 12px;
    font-size: 14px;
    display: flex;
    // height: 20px;
    // line-height: 20px;
    .item-title {
      color: #909399;
      min-width: 110px;
    }
    .item-content {
      color: #121f3e;
    }
  }
</style>
