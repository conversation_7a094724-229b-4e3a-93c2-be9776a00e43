<template>
  <PageContainer class="positionManage-list">
    <template #content>
      <div class="positionManage-list__left">
        <div class="positionManage-list__left__tree">
          <div class="space-tree__search">
            <el-input v-model="filterText" placeholder="输入关键字搜索" suffix-icon="el-icon-search"></el-input>
          </div>
          <el-tree
            ref="treeRef"
            v-loading="treeLoading"
            class="space-tree__tree"
            :data="treeData"
            node-key="id"
            :props="defaultProps"
            size="small"
            :highlight-current="true"
            :filter-node-method="filterNode"
            :expand-on-click-node="false"
            @node-click="nodeClick"
          >
            <template #default="{ node, data }">
              <div class="custom-tree-node">
                <div class="custom-tree-node-wrapper">
                  <el-tooltip class="item" effect="dark" :content="data.dutyPostName" placement="top-start" :disabled="data.dutyPostName.length < 10">
                    <span class="custom-tree-node-label">
                      {{ data.dutyPostName }}
                    </span>
                  </el-tooltip>
                  <el-dropdown trigger="click" class="custom-tree-menu" size="small">
                    <span class="more"><i class="el-icon-more rotate" /></span>
                    <el-dropdown-menu slot="dropdown" class="hide-arrow">
                      <el-dropdown-item divided>
                        <div v-auth="'shiftPostManage:edit'" style="padding: 0px 15px; text-align: center" @click.self="clickMenu('edit', data)">编辑</div>
                      </el-dropdown-item>
                      <el-dropdown-item divided>
                        <div v-auth="'shiftPostManage:delete'" style="padding: 0px 15px; text-align: center" class="removeNode" @click.self="clickMenu('remove', data)">删除</div>
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </div>
              </div>
            </template>
          </el-tree>
          <el-button v-auth="'shiftPostManage:add'" type="primary" icon="el-icon-plus" @click="addPost">新增值班岗 </el-button>
        </div>
      </div>
      <div class="positionManage-list__right">
        <el-tabs v-model="activeName" @tab-click="handleTabsClick">
          <el-tab-pane label="岗位人员" name="1"></el-tab-pane>
          <el-tab-pane label="定位设备" name="2"></el-tab-pane>
          <el-tab-pane label="离岗规则配置" name="3"></el-tab-pane>
        </el-tabs>
        <el-form v-if="activeName !== '3'" ref="formRef" class="positionManage-list__form" :model="searchForm" inline>
          <el-form-item prop="name">
            <el-input
              v-model="searchForm.name"
              :placeholder="activeName === '1' ? '搜索姓名、工号、手机号' : '搜索设备名称、编码'"
              suffix-icon="el-icon-search"
              clearable
            ></el-input>
          </el-form-item>
          <div>
            <el-button type="primary" plain @click="onReset">重置</el-button>
            <el-button type="primary" @click="onSearch">查询</el-button>
          </div>
        </el-form>
        <div v-if="activeName !== '3'" class="positionManage-list__table_actions">
          <el-button v-auth="'shiftPostManage:addPerson'" type="primary" icon="el-icon-plus" @click="onOperate(OperateType.ADD)">{{
            activeName === '1' ? '添加成员' : '添加设备'
          }}</el-button>
          <el-button v-if="activeName === '1'" type="danger" plain :disabled="!checkedIds.length" @click="onOperate(OperateType.BATCH_DELETE)">移除 </el-button>
        </div>
        <div class="positionManage-list__table" v-if="activeName === '1'">
          <el-table
            v-loading="tableLoading"
            height="100%"
            :data="tableData"
            border
            stripe
            class="tableAuto"
            ref="tableRef1"
            row-key="id"
            :key="1"
            @selection-change="(rows) => (selectionList = rows)"
          >
            <el-table-column type="selection" width="80"></el-table-column>
            <el-table-column label="序号" width="55">
              <template slot-scope="scope">
                <span>{{ (pagination.page - 1) * pagination.pageSize + scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="staffName" show-overflow-tooltip label="姓名"></el-table-column>
            <!-- <el-table-column prop="staffNum" show-overflow-tooltip label="工号"></el-table-column> -->
            <el-table-column prop="sex" label="性别" show-overflow-tooltip>
              <template slot-scope="scope">
                {{ scope.row.sex == 1 ? '男' : scope.row.sex == 0 ? '女' : '' }}
              </template>
            </el-table-column>
            <el-table-column prop="phone" label="手机号" show-overflow-tooltip></el-table-column>
            <!-- <el-table-column prop="phoneOffice" label="办公号码" show-overflow-tooltip></el-table-column>
            <el-table-column prop="workEntryDay" show-overflow-tooltip label="入职天数"></el-table-column> -->
            <el-table-column prop="age" show-overflow-tooltip label="年龄"></el-table-column>
            <el-table-column prop="educationName" show-overflow-tooltip label="学历"></el-table-column>
            <el-table-column prop="certDictName" label="证书" show-overflow-tooltip></el-table-column>
            <el-table-column prop="unit" show-overflow-tooltip label="归属单位"></el-table-column>
            <el-table-column label="操作" width="130px">
              <template #default="{ row }">
                <el-button type="text" @click="onOperate(OperateType.VIEW, row)">查看</el-button>
                <el-button type="text" class="text-red" @click="onOperate(OperateType.DELETE, row)">移除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="positionManage-list__table" v-if="activeName === '2'">
          <el-table v-loading="tableLoading" ref="tableRef2" height="100%" :key="2" :data="tableData" border stripe class="tableAuto" row-key="id">
            <!-- @selection-change="(rows) => (selectionList = rows)" <el-table-column type="selection" width="50"></el-table-column> -->
            <el-table-column label="序号" width="80">
              <template slot-scope="scope">
                <span>{{ (pagination.page - 1) * pagination.pageSize + scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="设备名称" show-overflow-tooltip></el-table-column>
            <el-table-column prop="code" label="资产编码" show-overflow-tooltip></el-table-column>
            <el-table-column prop="sn" label="SN" show-overflow-tooltip></el-table-column>
            <el-table-column prop="sysTypeName" label="专业类别" show-overflow-tooltip></el-table-column>
            <el-table-column prop="typeName" label="系统类别" show-overflow-tooltip></el-table-column>
            <el-table-column prop="state" label="在线状态" show-overflow-tooltip>
              <template slot-scope="scope">
                <el-tag v-if="scope.row.state == 1" size="mini" type="success">在线</el-tag>
                <el-tag v-else-if="scope.row.state == 0" size="mini" type="danger">离线</el-tag>
                <span v-else></span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="130px">
              <template #default="{ row }">
                <el-button type="text" class="text-red" @click="onOperate(OperateType.DELETE, row)">移除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination
          v-if="activeName !== '3'"
          :layout="pagination.layoutOptions"
          :current-page="pagination.page"
          :page-sizes="pagination.pageSizeOptions"
          :page-size="pagination.pageSize"
          :total="pagination.total"
          @size-change="paginationSizeChange"
          @current-change="paginationCurrentChange"
        ></el-pagination>
        <!-- 离岗规则配置 -->
        <div v-if="activeName === '3'" class="ruleConfigBox">
          <ruleConfig :dutyPostCode="checkedData.dutyPostCode"></ruleConfig>
        </div>
      </div>
      <memberDialog
        v-if="isMemberDialog"
        :memberShow="isMemberDialog"
        :treePostData="treePostData"
        :dutyPostCode="checkedData.dutyPostCode"
        @submitMemberDialog="submitMemberDialog"
        @closeMemberDialog="() => (isMemberDialog = false)"
      />
      <!-- 关联定位终端 -->
      <template v-if="positionTerminalShow">
        <positionTerminalDialog
          :positionTerminalShow="positionTerminalShow"
          :selectaDialogData="selectaDialogData"
          @submitTerminalDialog="submitTerminalDialog"
          @closeTerminalDialog="closeTerminalDialog"
        />
      </template>
    </template>
  </PageContainer>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin'
import memberDialog from './components/selectPostPersons.vue'
import positionTerminalDialog from './components/positionTerminalDialog.vue'
import ruleConfig from './ruleConfig.vue'
export default {
  name: 'shiftPostManage',
  components: {
    memberDialog,
    positionTerminalDialog,
    ruleConfig
  },
  mixins: [tableListMixin],
  data() {
    return {
      pagination: {
        page: 1,
        pageSize: 15,
        total: 0
      },
      // 树搜索关键字
      filterText: '',
      treeLoading: false, // tree loading
      treeData: [], // tree数据
      defaultProps: {
        children: 'child',
        label: 'dutyPostName'
      },
      tableLoading: false,
      tableData: [],
      // 选中的行
      selectionList: [],
      // 搜索表单
      searchForm: {
        // 搜索姓名、工号、手机号 设备名称id
        name: ''
      },
      checkedData: {}, // 选中数据
      activeName: '1',
      isMemberDialog: false, // 人员选择
      positionTerminalShow: false, // 定位终端
      selectaDialogData: '', // 定位终端数据回显
      treePostData: [] // 岗位左侧树数据
    }
  },
  computed: {
    OperateType() {
      return {
        // 增加
        ADD: 'add',
        // 批量删除
        BATCH_DELETE: 'batch_delete',
        // 删除
        DELETE: 'delete',
        // 查看
        VIEW: 'view'
      }
    },
    checkedIds() {
      return this.selectionList.map((it) => it.id)
    }
  },
  watch: {
    filterText(val) {
      this.$refs.treeRef.filter(val)
    }
  },
  mounted() {
    this.getTreeData()
  },
  methods: {
    // 获取值班岗数据
    getTreeData() {
      this.treeLoading = true
      this.$api.supplierAssess.getDutyPostData().then((res) => {
        this.treeLoading = false
        if (res.code == '200' && res.data.length > 0) {
          this.treeData = res.data
          this.checkedData = this.treeData[0]
          this.$nextTick(() => {
            this.$refs.treeRef.setCurrentKey(this.checkedData.id)
          })
          this.getListByTab()
        }
      })
      this.$api.supplierAssess.getDutyPostData().then((res) => {
        this.treeLoading = false
        if (res.code == '200' && res.data.length > 0) {
          this.treeData = res.data
          this.checkedData = this.treeData[0]
          this.$nextTick(() => {
            this.$refs.treeRef.setCurrentKey(this.checkedData.id)
          })
          this.getListByTab()
        }
      })
    },
    // 树节点过滤方法
    filterNode(value, data) {
      if (!value) return true
      return data.dutyPostName.includes(value)
    },
    // 岗位点击
    nodeClick(val) {
      this.checkedData = val
      this.onSearch()
    },
    // 点击搜索
    onSearch() {
      // 重置页码
      this.pagination.page = 1
      this.getListByTab()
    },
    // 点击重置，重置表单，然后触发搜索
    onReset() {
      this.$refs.formRef.resetFields()
      this.onSearch()
    },
    // 新增岗位
    addPost() {
      this.$router.push({
        name: 'shiftPostAdd',
        query: {
          type: 'add'
        }
      })
    },
    // 成员确认
    submitMemberDialog(list) {
      let ids = list.map((item) => {
        return item.staffId
      })
      let params = {
        staffId: ids.join(','),
        code: this.checkedData.dutyPostCode
      }
      this.$api.supplierAssess.addPersonToDutyPostData(params).then((res) => {
        if (res.code === '200') {
          this.$message.success(res.msg)
          this.isMemberDialog = false
          this.getDataList()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 定位终端确定
    submitTerminalDialog(row) {
      let params = {
        ids: '',
        code: this.checkedData.dutyPostCode
      }
      if (row && Array.isArray(row)) {
        params.ids = row.map((item) => item.id).join(',')
      } else {
        params.ids = row.id
      }

      this.$api.supplierAssess.bindLocateAssets(params).then((res) => {
        if (res.code === '200') {
          this.$message.success(res.msg)
          this.positionTerminalShow = false
          this.getTerminalListByPage()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 关闭定位终端
    closeTerminalDialog() {
      this.positionTerminalShow = false
    },
    // 树操作
    clickMenu(funcName, val) {
      if (funcName === 'edit') {
        this.$router.push({
          name: 'shiftPostAdd',
          query: {
            type: 'edit',
            id: val.id
          }
        })
      } else if (funcName === 'remove') {
        this.$confirm('是否删除该值班岗?', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$api.supplierAssess
            .deleteDutyPostData({ code: val.dutyPostCode }, { 'operation-type': 3, 'operation-name': val.dutyPostName, 'operation-id': val.dutyPostCode })
            .then((res) => {
              if (res.code === '200') {
                this.$message.success(res.msg)
                this.getTreeData()
              } else {
                this.$message.error(res.msg)
              }
            })
        })
      }
    },
    // 分页获取数据
    getDataList() {
      this.tableData = []
      this.tableLoading = true
      let data = {
        page: this.pagination.page,
        pageSize: this.pagination.pageSize,
        ...this.searchForm,
        postCode: this.checkedData.dutyPostCode
      }
      this.$api.supplierAssess
        .queryPersonByDutyPostByPage(data)
        .then((res) => {
          this.tableLoading = false
          if (res.code === '200') {
            if (res.data.records && res.data.records.length) {
              res.data.records.forEach((e) => {
                if (e.birthDate) {
                  e.age = this.getAge(new Date(e.birthDate))
                }
              })
            }
            this.tableData = res.data.records
            this.pagination.total = res.data.total
          } else {
            this.tableData = []
            this.pagination.total = 0
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    // 计算年龄
    getAge(birthDate) {
      // 创建一个 Date 对象表示当前日期
      const currentDate = new Date()
      // 获取当前年份
      const currentYear = currentDate.getFullYear()
      // 获取当前月份
      const currentMonth = currentDate.getMonth()
      // 获取当前日期
      const currentDay = currentDate.getDate()
      // 获取出生年份
      const birthYear = birthDate.getFullYear()
      // 获取出生月份
      const birthMonth = birthDate.getMonth()
      // 获取出生日期
      const birthDay = birthDate.getDate()
      // 计算年龄
      let age = currentYear - birthYear
      // 检查生日是否已过
      if (currentMonth < birthMonth || (currentMonth === birthMonth && currentDay < birthDay)) {
        age--
      }
      return age
    },
    // 分页相关
    paginationCurrentChange(val) {
      this.pagination.page = val
      this.getListByTab()
    },
    paginationSizeChange(val) {
      this.pagination.pageSize = val
      this.onSearch()
    },
    // 获取定位设备列表
    getTerminalListByPage() {
      this.tableLoading = true
      let data = {
        page: this.pagination.page,
        pageSize: this.pagination.pageSize,
        name: this.searchForm.name,
        dutyPostCode: this.checkedData.dutyPostCode
      }
      this.$api.supplierAssess
        .queryTerminalByDutyPostData(data)
        .then((res) => {
          this.tableLoading = false
          if (res.code === '200') {
            this.tableData = res.data.records
            this.pagination.total = res.data.total
          } else {
            this.tableData = []
            this.pagination.total = 0
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    onOperate(type, row) {
      switch (type) {
        case 'add':
          if (!this.checkedData.dutyPostCode) return this.$message.error('请先选择值班岗')
          if (this.activeName === '1') {
            this.isMemberDialog = true
          } else {
            this.positionTerminalShow = true
          }
          break
        case 'batch_delete':
          this.$confirm('是否确认删除所选内容？', '提示', {
            cancelButtonClass: 'el-button--primary is-plain',
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            let params = {}
            if (this.activeName === '1') {
              let staffIds = this.selectionList.map((it) => it.staffId)
              params.staffId = staffIds.join(',')
              params.code = this.checkedData.dutyPostCode
            } else {
              let ids = this.selectionList.map((it) => it.id)
              params.terminalId = ids.join(',')
              params.dutyPostCode = this.checkedData.dutyPostCode
            }
            let fn = this.activeName === '1' ? this.$api.supplierAssess.removePersonToDutyPostData : this.$api.supplierAssess.removeTerminalToDutyPostData
            fn(params).then((res) => {
              if (res.code === '200') {
                this.$message.success(res.msg)
                this.getListByTab()
              } else {
                this.$message.error(res.msg)
              }
            })
          })
          break
        case 'delete':
          this.$confirm(`是否移除该${this.activeName === '1' ? '人员' : '设备'}?`, '确认删除', {
            cancelButtonClass: 'el-button--primary is-plain',
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            let params = {}
            if (this.activeName === '1') {
              params.staffId = row.staffId
              params.code = this.checkedData.dutyPostCode
            } else {
              params.id = row ? row.id : ''
              params.code = this.checkedData.dutyPostCode
            }
            let fn = this.activeName === '1' ? this.$api.supplierAssess.removePersonToDutyPostData : this.$api.supplierAssess.unbindLocateAssets
            fn(params).then((res) => {
              if (res.code === '200') {
                this.$message.success(res.msg)
                this.getListByTab()
              } else {
                this.$message.error(res.msg)
              }
            })
          })
          break
        case 'view':
          this.$router.push({
            name: 'memberDetail',
            query: {
              type: 'view',
              staffId: row.staffId
            }
          })
          break
      }
    },
    // tab切换
    handleTabsClick(val) {
      this.activeName = val.name
      this.tableData = []
      this.onSearch()
      setTimeout(() => {
        if (this.activeName != '3') {
          this.$refs[`tableRef${this.activeName}`].doLayout()
        }
      }, 50)
    },
    getListByTab() {
      if (this.activeName === '1') {
        this.getDataList()
      } else if (this.activeName === '2') {
        this.getTerminalListByPage()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.positionManage-list {
  ::v-deep(.container-content) {
    display: flex;
    flex-flow: row nowrap;
    background-color: #fff;
  }
  &__left {
    width: 246px;
    height: 100%;
    padding: 16px;
    border-right: solid 1px #eee;
    text-align: center;
    &__tree {
      height: 100%;
      overflow: hidden;
    }
    ::v-deep(.el-tree) {
      position: relative;
      margin-top: 16px;
      height: calc(100% - 128px);
      margin-bottom: 16px;
      overflow: auto;
      .el-tree-node__content {
        line-height: 32px;
        height: 32px;
      }
      .el-tree-node.is-current > .el-tree-node__content {
        color: #3562db;
        background: #e6effc;
      }
      .space-tree {
        height: 100%;
        &__search {
          padding-right: 16px;
        }
      }
      .custom-tree-node {
        width: 100%;
        overflow: hidden;
        .custom-tree-node-wrapper {
          display: flex;
          width: 100%;
          justify-content: space-between;
          .custom-tree-node-label {
            display: inline-block;
            width: 70%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            text-align: left;
          }
        }
      }
      .rotate {
        cursor: pointer;
        margin-left: 5px;
        transform: rotate(90deg);
      }
    }
  }
  &__right {
    flex: 1;
    overflow: hidden;
    padding: 0 16px 16px 16px;
    background: #fff;
    display: flex;
    flex-direction: column;
    .ruleConfigBox {
      height: 100%;
      padding-top: 15px;
      box-sizing: border-box;
      overflow-y: auto;
    }
  }
  &__form {
    margin-top: 16px;
    display: flex;
    // justify-content: space-between;
    ::v-deep(.el-form-item) {
      margin-bottom: 0;
      .el-form-item__content {
        line-height: 1;
      }
    }
  }
  .text-red {
    color: #ff1919;
  }
  &__table_actions {
    margin: 16px 0;
  }
  &__table {
    flex: 1;
    overflow: hidden;
  }
  .el-pagination {
    margin-top: 10px;
  }
}
::v-deep .el-dropdown-menu__item--divided {
  border: 1px solid #ffffff !important;
}
::v-deep.el-dropdown-menu__item {
  color: #666666 !important;
  &:hover {
    background: #f6f5fa !important;
    color: #3562db !important;
  }
}
::v-deep .el-dropdown-menu--small .el-dropdown-menu__item.el-dropdown-menu__item--divided:before {
  display: none !important;
}
::v-deep .el-dropdown-menu__item--divided:before {
  display: none !important;
}
::v-deep .el-dropdown-menu--small .el-dropdown-menu__item,
::v-deep .el-dropdown-menu__item {
  padding: 0px !important;
}
.removeNode {
  color: #f53f3f !important;
  &:hover {
    background: #f6f5fa !important;
    color: #f53f3f !important;
  }
}
</style>
