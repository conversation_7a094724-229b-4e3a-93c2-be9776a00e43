<template>
    <PageContainer>
        <div slot="header">
            <div class="searchForm">
                <div class="search-box">
                    <el-input v-model="searchForm.dictionaryName" clearable filterable placeholder="请输入系统名称"
                        @blur="event => searchForm.dictionaryName = event.target.value.replace(/\s+/g, '')"></el-input>
                    <el-select v-model="searchForm.enable" class="ml-16" placeholder="请选择状态">
                        <el-option label="未启用" value="0" />
                        <el-option label="已启用" value="1" />
                    </el-select>
                    <div class="ml-16">
                        <el-button type="primary" @click="search">查询</el-button>
                        <el-button type="primary" plain @click="reset">重置</el-button>
                    </div>
                </div>
            </div>
        </div>
        <div slot="content" ref="contentRef" class="table-box">
            <div class="tableContainer">
                <el-button type="primary" style="margin-bottom: 12px" @click="handleListEvent('add')">新建</el-button>
                <el-table :data="tableData" style="width: 100%;margin-bottom: 20px;" border height="250">
                    <el-table-column prop="dictionaryDetailsName" label="系统名称">
                    </el-table-column>
                    <el-table-column prop="dictionaryDetailsCode" label="系统标识">
                    </el-table-column>
                    <el-table-column prop="enable" label="状态">
                        <template slot-scope="scope">
                            <span>
                                {{ scope.row.enable === 1 ? '已启用' : "未启用" }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="200">
                        <template slot-scope="scope">
                            <!-- <el-button type="text" @click="handleListEvent('look', scope.row)">查看</el-button> -->
                            <el-button type="text" @click="handleListEvent('edit', scope.row)">编辑</el-button>
                            <el-button type="text" v-if="scope.row.enable == 1"
                                @click="handleListEvent('forbidden', scope.row, 0)">禁用</el-button>
                            <el-button type="text" v-if="scope.row.enable == 0"
                                @click="handleListEvent('forbidden', scope.row, 1)">启用</el-button>
                            <el-button type="text" @click="handleListEvent('del', scope.row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination :current-page="pagination.page" :page-sizes="[15, 30, 50, 100]"
                    :page-size="pagination.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="pageTotal"
                    @size-change="handleSizeChange" @current-change="handleCurrentChange">
                </el-pagination>
            </div>
            <!-- 新增分类 -->
            <el-dialog v-dialogDrag :visible.sync="dialogVisible" :before-close="dialogClosed" :modal="false"
                :close-on-click-modal="false" :title="title" width="50%" custom-class="model-dialog">
                <div class="diaContent" style="padding: 10px">
                    <el-form ref="formInline" :model="formInline" label-width="100px" :rules="rules"
                        label-position="right">
                        <el-form-item label="系统名称：" prop="dictionaryDetailsName" class="form-item">
                            <el-input v-model.trim="formInline.dictionaryDetailsName" placeholder="请输入系统名称"
                                :maxlength="20"></el-input>
                        </el-form-item>
                        <el-form-item label="系统编码：" prop="dictionaryDetailsCode" class="form-item">
                            <el-input v-model.trim="formInline.dictionaryDetailsCode" :disabled="isEditing"
                                placeholder="请输入系统编码">
                            </el-input>
                        </el-form-item>
                    </el-form>
                </div>
                <span slot="footer" class="dialog-footer">
                    <el-button type="primary" plain @click="dialogClosed">取 消</el-button>
                    <el-button type="primary" @click="submit('formInline')">确 定</el-button>
                </span>
            </el-dialog>

        </div>
    </PageContainer>
</template>
<script>
export default {
    name: 'systemManagement',
    data() {
        return {
            searchForm: {
                dictionaryName: '',//模块名称
                enable: '',//	启用状态 0 未启用 1 已启用
            },
            pagination: {
                pageSize: 15,
                page: 1
            },
            tableData: [],
            title: '',
            checkedArr: [],
            checkedList: [],
            pageTotal: 0,
            formInline: {
                dictionaryDetailsId: '',
                dictionaryDetailsName: '', //名称
                dictionaryDetailsCode: '',  //类型标识
                dictionaryDetailsSort: '',  //排序
                dictionaryDetailsRemake: '', //备注
                level: 1,
                parentId: '-1',
            },
            rules: {
                dictionaryDetailsName: [{ required: true, message: '请输入名称', trigger: 'blur' }],
                dictionaryDetailsCode: [{ required: true, message: '请输入类型标识', trigger: 'blur' }],
                dictionaryDetailsSort: [{ required: true, message: '请输入排序', trigger: 'blur' }],
            },
            dialogVisible: false,
            parentMenu: null,
            isEditing: false
        }
    },
    mounted() {
        this.getTableData()
    },
    methods: {
        // 品类table列表
        getTableData() {
            this.tableLoading = true
            let data = {
                ...this.pagination,
                ...this.searchForm
            }
            this.$api
                .getCategoryManagementList(data)
                .then((res) => {
                    this.tableLoading = false
                    if (res.code == 200) {
                        this.tableData = res.data.records
                        this.pageTotal = res.data.total
                    } else if (res.message) {
                        this.tableData = []
                        this.pagination.total = 0
                        this.$message.error(res.message)
                    }
                })
                .catch((err) => {
                    this.tableLoading = false
                })
        },
        handleSizeChange(val) {
            this.pagination.pageSize = val
            this.pagination.page = 1
            this.getTableData()
        },
        search() {
            this.pagination.page = 1
            this.getTableData()
        },
        reset() {
            this.searchForm = {
                dictionaryName: '',//模块名称
                enable: '',//	启用状态 0 未启用 1 已启用
            }
            this.pagination.page = 1
            this.pageTotal = 0
            this.getTableData()
        },
        handleCurrentChange(val) {
            this.pagination.page = val
            this.getTableData()
        },
        handleListEvent(type, row, num) {
            if (type == 'add') {
                this.title = '新建'
                this.formInline = {
                    dictionaryDetailsId: '',
                    dictionaryDetailsName: '', //名称
                    dictionaryDetailsCode: '',  //类型标识
                    dictionaryDetailsSort: '',  //排序
                    dictionaryDetailsRemake: '', //备注
                    level: 1,
                    parentId: '-1',
                }
                this.isEditing = false
                this.dialogVisible = true
            }
            if (type == 'edit') {
                this.title = '编辑'
                this.isEditing = true
                let params = {
                    dictionaryId: row.dictionaryDetailsId
                }
                this.$api.getCategoryManagementDetailById(params).then((res) => {
                    if (res.code === '200') {
                        this.formInline = res.data
                    } else {
                        this.$message.error(res.message)
                    }
                })
                this.dialogVisible = true
            }
            if (type == 'forbidden') {
                let params = {
                    enable: num,
                    id: row.sysSettingsId
                }
                this.$api.getSystemManagementEnable(params).then((res) => {
                    if (res.code === '200') {
                        this.$message.success(res.message)
                        this.getTableData()
                    } else {
                        this.$message.error(res.message)
                    }
                })
            }
            if (type == 'del') {
                this.$confirm('此操作将永久删除, 是否继续?', '提示', {
                    cancelButtonClass: 'el-button--primary is-plain',
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    let params = {
                        id: row.sysSettingsId
                    }
                    this.$api.getSystemManagementDeleteById(params).then((res) => {
                        if (res.code === '200') {
                            this.$message.success(res.message)
                            this.getTableData()
                        } else {
                            this.$message.error(res.message)
                        }
                    })
                })
            }
        },
        // 确定
        submit(formName) {
            // 检查是否正在提交
            if (this.isSubmitting) {
                return; // 如果正在提交，则直接返回
            }
            this.isSubmitting = true; // 设置为正在提交
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    let data = {
                        ...this.formInline
                    }
                    if (data.dictionaryDetailsId) {
                        this.$api.getCategoryEditDetail(data).then((res) => {
                            this.isSubmitting = false; // 提交完成，重置状态
                            if (res.code === '200') {
                                this.$message.success(res.message)
                                this.getTableData()
                                this.dialogVisible = false
                                this.$refs.formInline.resetFields()
                            } else {
                                this.$message.error(res.message)
                            }
                        }).catch(() => {
                            this.isSubmitting = false; // 在请求失败的情况下也需要重置状态
                        });
                    } else {
                        this.$api.addDetailCategoryManagement(data).then((res) => {
                            this.isSubmitting = false; // 提交完成，重置状态
                            if (res.code === '200') {
                                this.$message.success(res.message)
                                this.getTableData()
                                this.dialogVisible = false
                                this.$refs.formInline.resetFields()
                            } else {
                                this.$message.error(res.message)
                            }
                        }).catch(() => {
                            this.isSubmitting = false; // 在请求失败的情况下也需要重置状态
                        });
                    }
                } else {
                    this.isSubmitting = false; // 表单验证失败也需要重置状态
                    return false
                }
            })
        },
        // 取消
        dialogClosed() {
            this.dialogVisible = false
            this.lookVisible = false
            if (this.$refs.formInline) {
                this.$refs.formInline.resetFields()
            }
        },
        handleClick() {
            this.personDialogShow = true
        },
    }
}
</script>
<style lang="scss" scoped>
/*修改展开按钮的样式 start*/
/*1.取消原本展开的旋转动效*/
::v-deep .el-table .el-table__expand-icon {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
    margin-right: 10px;
}

/*2.展开按钮未点击的样式是加号带边框*/
::v-deep .el-table__expand-icon .el-icon-arrow-right:before {
    content: "\e6d9";
    border: 1px solid #ccc;
    padding: 2px;
}

/*3.展开按钮点击后的样式是减号带边框*/
::v-deep .el-table__expand-icon--expanded .el-icon-arrow-right:before {
    content: "\e6d8";
}

.searchForm {
    display: flex;
    height: 80px;
    padding: 0 16px;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .el-input {
        width: 200px;
        margin-left: 16px;
    }

    >div {
        margin-right: 20px;
    }
}

.hide {
    ::v-deep .el-upload--picture-card {
        display: none;
    }
}

.span-plus {
    color: #3562db;
}

.span-reduce {
    color: #d9001b;
}

.span-plus,
.span-reduce {
    cursor: pointer;
}

.search-box {
    display: flex;
    align-items: center;
}

.container-content>div {
    height: 100%;
    margin-top: 16px;
}

.el-table {
    margin-bottom: 12px;

}

::v-deep .el-table__row:hover {
    cursor: pointer;
}

.table-box {
    background-color: #fff;
    height: 100%;
    padding: 16px;

    .tableContainer {
        height: 100%;

        .el-table {
            height: calc(100% - 96px) !important;
        }
    }
}

.diaContent {
    width: 100%;
    max-height: 550px !important;
    overflow: auto;
    background-color: #fff !important;

    .el-input,
    .el-select,
    .el-cascader {
        width: 300px;
    }
}

.form-item {
    display: inline-block;
    margin-right: 20px;
}

.inputWidth {
    width: 820px;
}

.ml-16 {
    margin-left: 16px;
}

.dialog .el-dialog {
    width: 60% !important;
}
</style>
