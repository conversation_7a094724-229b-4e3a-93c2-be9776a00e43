/*
 * @Description:
 */
import Layout from '@/layout'
import EmptyLayout from '@/layout/empty'
export default [
  {
    path: '/operator',
    component: Layout,
    redirect: '/operator/unitManagement',
    name: 'operator',
    meta: {
      title: '施工作业方管理',
      menuAuth: '/operator'
    },
    children: [
      {
        path: 'unitManagement',
        component: EmptyLayout,
        redirect: { name: 'unitManagement' },
        meta: {
          title: '单位管理',
          menuAuth: '/operator/unitManagement'
        },
        children: [
          {
            path: '',
            name: 'unitManagement',
            component: () => import('@/views/construction/operator/unitManagement/index.vue'),
            meta: {
              title: '单位管理',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'unitDetail',
            name: 'unitDetail',
            component: () => import('@/views/construction/operator/unitManagement/detail.vue'),
            meta: {
              title: '单位详情',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/operator/unitManagement'
            }
          }
        ]
      },
      {
        path: 'peopleManagement',
        component: EmptyLayout,
        redirect: '/operator/peopleManagement',
        meta: {
          title: '人员管理',
          menuAuth: '/operator/peopleManagement'
        },
        children: [
          {
            path: '',
            name: 'peopleManagement',
            component: () => import('@/views/construction/operator/peopleManagement/index.vue'),
            meta: {
              title: '人员管理',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      }
    ]
  },
  {
    path: '/operation',
    component: Layout,
    redirect: '/operation/operationLedger',
    name: 'operation',
    meta: {
      title: '施工作业管理',
      menuAuth: '/operation'
    },
    children: [
      {
        path: 'operationLedger',
        component: EmptyLayout,
        redirect: { name: 'operationLedger' },
        meta: {
          title: '作业台账',
          menuAuth: '/operation/operationLedger'
        },
        children: [
          {
            path: '',
            name: 'operationLedger',
            component: () => import('@/views/microApp/index.vue'),
            meta: {
              title: '作业台账',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'myApplication',
        component: EmptyLayout,
        redirect: { name: 'myApplication' },
        meta: {
          title: '我的申请',
          menuAuth: '/operation/myApplication'
        },
        children: [
          {
            path: '',
            name: 'myApplication',
            component: () => import('@/views/microApp/index.vue'),
            meta: {
              title: '我的申请',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'myApproval',
        component: EmptyLayout,
        redirect: '/operation/myApproval',
        meta: {
          title: '我的审批',
          menuAuth: '/operation/myApproval'
        },
        children: [
          {
            path: '',
            name: 'myApproval',
            component: () => import('@/views/microApp/index.vue'),
            meta: {
              title: '我的审批',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'constructionFileManage',
        component: EmptyLayout,
        redirect: '/operation/constructionFileManage',
        meta: {
          title: '文件管理',
          menuAuth: '/operation/constructionFileManage'
        },
        children: [
          {
            path: '',
            name: 'constructionFileManage',
            component: () => import('@/views/construction/constructionFileManage/index.vue'),
            meta: {
              title: '文件管理',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'constructionFileDocAdd',
            name: 'constructionFileDocAdd',
            component: () => import('@/views/construction/constructionFileManage/addDoc.vue'),
            meta: {
              title: '创建文档',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/operation/constructionFileManage'
            }
          },
          {
            path: 'constructionFileDocView',
            name: 'constructionFileDocView',
            component: () => import('@/views/construction/constructionFileManage/DocumentDetails.vue'),
            meta: {
              title: '文档详情',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/operation/constructionFileManage'
            }
          }
        ]
      },
      {
        path: ':page*',
        name: 'microApp',
        component: () => import('@/views/microApp/index.vue')
      }
    ]
  },
  {
    path: '/operatorSafety',
    component: Layout,
    redirect: '/operatorSafety/unitManagement',
    name: 'operatorSafety',
    meta: {
      title: '施工作业安全监督',
      menuAuth: '/operatorSafety'
    },
    children: [
      {
        path: 'inspSafeTemManagement',
        component: EmptyLayout,
        redirect: { name: 'inspSafeTemManagement' },
        meta: {
          title: '巡检模板管理',
          menuAuth: '/operatorSafety/inspSafeTemManagement'
        },
        children: [
          {
            path: '',
            name: 'inspSafeTemManagement',
            component: () => import('@/views/construction/operatorSafety/inspSafeTemManagement/index.vue'),
            meta: {
              title: '巡检模板管理',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'inspSafeTemManagementAdd',
            name: 'inspSafeTemManagementAdd',
            component: () => import('@/views/construction/operatorSafety/inspSafeTemManagement/add.vue'),
            meta: {
              title: '新增巡检模板',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'inspSafeTemManagementDetail',
            name: 'inspSafeTemManagementDetail',
            component: () => import('@/views/construction/operatorSafety/inspSafeTemManagement/detail.vue'),
            meta: {
              title: '巡检模板详情',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'inspSafeRecord',
        component: EmptyLayout,
        redirect: '/operatorSafety/inspSafeRecord',
        meta: {
          title: '巡检记录',
          menuAuth: '/operatorSafety/inspSafeRecord'
        },
        children: [
          {
            path: '',
            name: 'inspSafeRecord',
            component: () => import('@/views/construction/operatorSafety/inspSafeRecord/index.vue'),
            meta: {
              title: '巡检记录',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'inspSafeRecordPoint',
            name: 'inspSafeRecordPoint',
            component: () => import('@/views/construction/operatorSafety/inspSafeRecord/list.vue'),
            meta: {
              title: '巡检记录',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'maintainRecordDetail',
            name: 'maintainRecordDetail',
            component: () => import('@/views/construction/operatorSafety/inspSafeRecord/detail.vue'),
            meta: {
              title: '巡检记录详情',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'inspSafeConfig',
        component: EmptyLayout,
        redirect: '/operatorSafety/inspSafeConfig',
        meta: {
          title: '巡检配置',
          menuAuth: '/operatorSafety/inspSafeConfig'
        },
        children: [
          {
            path: '',
            name: 'inspSafeConfig',
            component: () => import('@/views/construction/operatorSafety/inspSafeConfig/index.vue'),
            meta: {
              title: '巡检配置',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'inspSafeConfigAdd',
            name: 'inspSafeConfigAdd',
            component: () => import('@/views/construction/operatorSafety/inspSafeConfig/add.vue'),
            meta: {
              title: '新增巡检配置',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      }
    ]
  },
  {
    path: '/operatorConfig',
    component: Layout,
    redirect: '/operatorConfig/functionConfig',
    name: 'operatorConfig',
    meta: {
      title: '施工作业配置',
      menuAuth: '/operatorConfig'
    },
    children: [
      {
        path: 'functionConfig',
        component: EmptyLayout,
        redirect: { name: 'functionConfig' },
        meta: {
          title: '功能配置',
          menuAuth: '/operatorConfig/functionConfig'
        },
        children: [
          {
            path: '',
            name: 'functionConfig',
            component: () => import('@/views/construction/operatorConfig/functionConfig.vue'),
            meta: {
              title: '功能配置',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      }
    ]
  }
]
