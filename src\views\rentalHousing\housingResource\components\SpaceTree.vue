<script>
const TreeRootId = '-1'
const TreeRoot = {
  spaceName: '所有小区',
  id: TreeRootId,
  spaceLevel: 0,
  children: []
}
export default {
  name: 'SpaceTree',
  components: {
    TreeOperate: () => import('./TreeOperate.vue'),
    CommunityEdit: () => import('./CommunityEdit.vue'),
    BuildingCreate: () => import('./BuildingCreate.vue'),
    BuildingEdit: () => import('./BuildingEdit.vue'),
    FloorEdit: () => import('./FloorEdit.vue')
  },
  model: {
    prop: 'spaceId',
    event: 'update:spaceId'
  },
  props: {
    roomCount: {
      type: Number,
      default: 0
    },
    spaceId: {
      type: String,
      default: TreeRootId
    },
    defaultExpandedIds: Array
  },
  emits: ['update:spaceId'],
  data: () => ({
    treeLoading: false,
    // 树搜索关键字
    treeSearchKeyWord: '',
    // 当前树选中的节点data-id
    currentTreeNodeId: TreeRootId,
    treeData: [TreeRoot],
    // 需要展开的节点
    expandedIds: [TreeRootId],
    hospitalMenuEvents: [{ label: '创建小区', funcName: 'addCourtyard' }],
    courtyardMenuEvents: [
      { label: '创建楼栋', funcName: 'addBuilding' },
      { label: '查看/编辑', funcName: 'editCourtyard' },
      { label: '删除小区', funcName: 'removeCourtyard' }
    ],
    buildingMenuEvents: [
      { label: '创建单元', funcName: 'addUnit' },
      { label: '查看/编辑', funcName: 'editBuilding' },
      { label: '删除建筑', funcName: 'removeBuilding' }
    ],
    unitMenuEvents: [
      { label: '创建楼层', funcName: 'addFloor' },
      { label: '查看/编辑', funcName: 'editUnit' },
      { label: '删除建筑', funcName: 'removeUnit' }
    ],
    floorMenuEvents: [
      { label: '编辑楼层', funcName: 'editFloor' },
      { label: '删除楼层', funcName: 'removeFloor' }
    ],
    // 小区弹窗
    dialogSpace: {
      show: false,
      id: ''
    },
    // 楼栋编辑弹窗
    dialogBuildingEdit: {
      show: false,
      id: '',
      // 是否从单元开始编辑
      isCell: false,
      // 单元/楼栋的父级ID
      parentId: ''
    },
    // 楼栋、单元新增弹窗
    dialogBuildingCreate: {
      show: false,
      // 是否从单元开始
      isCell: false
    },
    // 楼栋单元编辑弹窗
    dialogFloorEdit: {
      show: false,
      id: ''
    }
  }),
  watch: {
    treeSearchKeyWord(val) {
      this.$refs.treeRef.filter(val)
    },
    spaceId: {
      handler(val) {
        this.currentTreeNodeId = val || TreeRootId
        this.setTreeSelected()
      },
      immediate: true
    }
  },
  created() {
    if (this.defaultExpandedIds && this.defaultExpandedIds.length) {
      this.expandedIds = this.defaultExpandedIds
    }
    this.getTreeListData()
  },
  methods: {
    /**
     * 获取楼层ID链
     * expose
     * @returns {string[]}
     */
    getFloorIdChain() {
      const idChain = []
      let node = this.$refs.treeRef.getNode(this.currentTreeNodeId)
      while (node.parent) {
        const dataId = node.data.id
        if (dataId !== TreeRootId) {
          idChain.unshift(node.data.id)
          node = node.parent
        } else {
          break
        }
      }
      return idChain.length < 4 ? [] : idChain
    },
    // 设置树选中节点
    setTreeSelected() {
      if (!this.$refs.treeRef) return
      this.$refs.treeRef.setCurrentKey(this.currentTreeNodeId)
    },
    getTreeListData() {
      this.treeLoading = true
      this.$api.rentalHousingApi
        .allSpaceList()
        .then((res) => {
          if (res.code === '200') {
            const data = this.$tools.transData(res.data, 'id', 'parentId', 'children')
            this.treeData[0].children = data
            this.$nextTick(() => {
              // 选中节点
              this.setTreeSelected()
            })
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取空间信息失败'))
        .finally(() => (this.treeLoading = false))
    },
    // 当前选中节点发生变化
    onCurrentChange(nodeData, treeNode) {
      if (this.spaceId === nodeData.id) {
        return
      }
      // set key
      this.currentTreeNodeId = nodeData.id
      this.$emit('update:spaceId', nodeData.id)
      // 获取展开层
      const ids = []
      let node = treeNode
      while (node) {
        const nextNode = node.parent
        // 树会存在顶级节点。
        if (!nextNode) break
        const data = node.data
        ids.unshift(data.id)
        node = nextNode
      }
      this.expandedIds = ids
    },
    // 树节点过滤方法
    filterNode(value, data) {
      if (!value) return true
      return data.spaceName.includes(value)
    },
    // 拖拽时判定目标节点能否被放置
    allowDrop(draggingNode, dropNode, type) {
      // 不允许插入
      if (type === 'inner') {
        return false
      }
      // 不允许插入并且只能是同级
      const sameLevel = draggingNode.level === dropNode.level
      const sameParent = draggingNode.parent === dropNode.parent
      // 并且只能兄弟节点
      return sameLevel && sameParent
    },
    // 判断当前节点是否可被拖拽
    allowDrag(node) {
      const level = node.level
      // 根节点和楼层不允许拖动
      // return level > 1 && level < 5
      return level > 1
    },
    // 拖拽结束
    handleDrop(node) {
      // 获取最新排序
      const parentId = node.data.parentId
      const parentNode = this.$refs.treeRef.getNode(parentId)
      const sortedIds = parentNode.childNodes.map((childNode, index) => childNode.data.id)
      this.doSpaceSort(sortedIds)
    },
    // 点击根节点操作
    opertaionHospital(fun, data) {
      this.dialogSpace.id = ''
      this.dialogSpace.show = true
    },
    // 小区节点 操作回调
    opertaionCourtyard(fun, data) {
      const dataId = data.data.id
      // 删除小区
      if (fun === 'removeCourtyard') {
        this.$confirm('下级节点也将被删除，且无法恢复', {
          title: '是否要删除所选节点？',
          type: 'warning'
        }).then(() => this.doDeleteTreeNode(dataId, this.$api.rentalHousingApi.deleteSpaceEstateInfoById))
      } else if (fun === 'editCourtyard') {
        // 获取小区ID，进行编辑
        this.dialogSpace.id = data.data.id
        this.dialogSpace.show = true
      } else if (fun === 'addBuilding') {
        // 从楼栋开始创建
        this.dialogBuildingCreate.isCell = false
        this.dialogBuildingCreate.show = true
      }
    },
    // 楼栋节点 操作回调
    opertaionBuilding(fun, data) {
      const dataId = data.data.id
      // 删除楼栋
      if (fun === 'removeBuilding') {
        this.canDelete('下级节点也将被删除，且无法恢复')
          .then(() => this.doDeleteTreeNode(dataId, this.$api.rentalHousingApi.deleteBuildingById))
          .catch((msg) => msg && this.$message.error(msg))
      } else if (fun === 'editBuilding') {
        // 获取楼栋ID，进行编辑
        this.dialogBuildingEdit.parentId = this.expandedIds.slice(-2)[0]
        this.dialogBuildingEdit.id = dataId
        this.dialogBuildingEdit.isCell = false
        this.dialogBuildingEdit.show = true
      } else if (fun === 'addUnit') {
        // 从单元开始创建
        this.dialogBuildingCreate.isCell = true
        this.dialogBuildingCreate.show = true
      }
    },
    // 单元节点 操作回调
    opertaionUnit(fun, data) {
      const dataId = data.data.id
      // 删除楼栋
      if (fun === 'removeUnit') {
        this.canDelete('下级节点也将被删除，且无法恢复')
          .then(() => this.doDeleteTreeNode(dataId, this.$api.rentalHousingApi.deleteUnitById))
          .catch((msg) => msg && this.$message.error(msg))
      } else if (fun === 'editUnit') {
        this.dialogBuildingEdit.parentId = this.expandedIds.slice(-2)[0]
        // 获取楼栋ID，进行编辑
        this.dialogBuildingEdit.id = dataId
        this.dialogBuildingEdit.isCell = true
        this.dialogBuildingEdit.show = true
      } else {
        // 创建楼层
        this.dialogFloorEdit.id = ''
        this.dialogFloorEdit.show = true
      }
    },
    // 楼层节点 操作回调
    opertaionFloor(fun, data) {
      const dataId = data.data.id
      // 删除楼栋
      if (fun === 'removeFloor') {
        this.canDelete('节点将被删除，且无法恢复')
          .then(() => this.doDeleteTreeNode(dataId, this.$api.rentalHousingApi.deleteFloorById))
          .catch((msg) => msg && this.$message.error(msg))
      } else if (fun === 'editFloor') {
        // 获取楼栋ID，进行编辑
        this.dialogFloorEdit.id = dataId
        this.dialogFloorEdit.show = true
      }
    },
    // 节点删除前检查并询问
    async canDelete(question) {
      if (this.roomCount) {
        throw '所选节点下存在房间，无法删除'
      }
      const enquireResult = await this.$confirm(question, {
        title: '是否要删除所选节点？',
        type: 'warning'
      })
      if (!enquireResult) {
        throw ''
      }
    },
    /**
     * 删除小区、楼栋、单元、楼层数据
     * @param id
     * @param deleteFun
     */
    doDeleteTreeNode(id, deleteFun) {
      if (typeof deleteFun !== 'function') {
        return this.$message.error('删除失败')
      }
      deleteFun({ id })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('删除成功')
            this.expandedIds.pop()
            // 删除节点后，树的当前节点向上移动
            const treeDataId = this.expandedIds.slice(-1)[0]
            this.$emit('update:spaceId', treeDataId || TreeRootId)
            this.getTreeListData()
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg))
    },
    // 更新排序
    doSpaceSort(idList) {
      this.treeLoading = true
      this.$api.rentalHousingApi
        .updateSpaceSort({ idList })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('排序更新成功')
            this.getTreeListData()
          } else {
            throw res.message
          }
        })
        .catch((msg) => {
          this.$message.error(msg || '排序更新失败')
        })
        .finally(() => (this.treeLoading = false))
    }
  }
}
</script>
<template>
  <div class="component space-tree">
    <div class="space-tree__search">
      <el-input v-model="treeSearchKeyWord" placeholder="输入关键字搜索" suffix-icon="el-icon-search"></el-input>
    </div>
    <el-tree
      ref="treeRef"
      v-loading="treeLoading"
      class="space-tree__tree"
      :data="treeData"
      node-key="id"
      size="small"
      :highlight-current="true"
      :filter-node-method="filterNode"
      :default-expanded-keys="expandedIds"
      :expand-on-click-node="false"
      draggable
      :allow-drop="allowDrop"
      :allow-drag="allowDrag"
      @node-drop="handleDrop"
      @current-change="onCurrentChange"
    >
      <template #default="{ node, data }">
        <div class="custom-tree-node">
          <div class="custom-tree-node-wrapper">
            <span class="custom-tree-node-label">
              {{ data.spaceName }}
            </span>
            <span v-if="data.id === currentTreeNodeId" class="operate-btns">
              <TreeOperate v-if="data.spaceLevel == 0" key="root" :events="hospitalMenuEvents" :data="{ node, data }" @addNode="opertaionHospital"></TreeOperate>
              <TreeOperate v-if="data.spaceLevel == 1" key="area" :events="courtyardMenuEvents" :data="{ node, data }" @addNode="opertaionCourtyard"></TreeOperate>
              <TreeOperate v-if="data.spaceLevel == 2" key="building" :events="buildingMenuEvents" :data="{ node, data }" @addNode="opertaionBuilding"></TreeOperate>
              <TreeOperate v-if="data.spaceLevel == 3" key="unit" :events="unitMenuEvents" :data="{ node, data }" @addNode="opertaionUnit"></TreeOperate>
              <TreeOperate v-if="data.spaceLevel == 4" key="floor" :events="floorMenuEvents" :data="{ node, data }" @addNode="opertaionFloor"></TreeOperate>
            </span>
          </div>
        </div>
      </template>
    </el-tree>
    <!--创建、编辑小区-->
    <CommunityEdit v-bind="dialogSpace" :visible.sync="dialogSpace.show" @success="getTreeListData" />
    <!--创建楼栋、单元-->
    <BuildingCreate v-bind="dialogBuildingCreate" :visible.sync="dialogBuildingCreate.show" :treeNodeId="currentTreeNodeId" @success="getTreeListData" />
    <!--编辑楼栋,单元-->
    <BuildingEdit v-bind="dialogBuildingEdit" :visible.sync="dialogBuildingEdit.show" :room-count="roomCount" :treeNodeId="currentTreeNodeId" @success="getTreeListData" />
    <!--创建、编辑楼层-->
    <FloorEdit v-bind="dialogFloorEdit" :visible.sync="dialogFloorEdit.show" :room-count="roomCount" :treeNodeId="currentTreeNodeId" @success="getTreeListData" />
  </div>
</template>
<style scoped lang="scss">
.space-tree {
  height: 100%;
  &__search {
    padding-right: 16px;
  }
  ::v-deep(.el-tree) {
    position: relative;
    margin-top: 16px;
    padding-right: 16px;
    height: calc(100% - 48px);
    overflow: auto;
    .el-tree-node__content {
      line-height: 32px;
      height: 32px;
    }
    .el-tree-node.is-current > .el-tree-node__content {
      color: #3562db;
      background: #e6effc;
    }
  }
  .custom-tree-node {
    width: 100%;
    .custom-tree-node-wrapper {
      display: flex;
      justify-content: space-between;
    }
  }
}
</style>
