<template>
  <PageContainer>
    <div slot="content" class="role-content" style="height: 100%;">
      <div class="role-content-left">
        <div class="toptip">
          <span class="green_line"></span>
          风险四色分布图
        </div>
        <div class="left_content">
          <el-input v-model.trim="filterText" clearable placeholder="输入关键字进行过滤"></el-input>
          <div class="tree">
            <el-tree
              ref="tree"
              style="margin-top: 10px;"
              :check-strictly="true"
              :data="treeData"
              :props="defaultProps"
              :highlight-current="true"
              node-key="id"
              :filter-node-method="filterNode"
              :default-expanded-keys="expanded"
              @node-click="handleNodeClick"
            ></el-tree>
          </div>
        </div>
      </div>
      <div class="role-content-center">
        <div style="text-align: center; margin-top: 40px;">
          <div v-if="pictureUrl" class="demo-image__preview">
            <el-image id="images" style="width: 70%; height: calc(100% - 200px); margin-bottom: 20px; color: #fff;" :src="pictureUrl" :preview-src-list="srcList"> </el-image>
            <div style="margin-bottom: 20px;">(点击可放大查看)</div>
            <el-button style="margin: 0;" class="sino-button-sure" icon="el-icon-download" @click="imgDownlod">图片下载</el-button>
          </div>
          <div v-else style="margin-top: 20%;">
            <img style="width: 100px; height: 100px; margin-bottom: 20px;" src="@/assets/images/defaultimg.png" alt="" />
            <div style="color: #5188fc;">无四色图，请及时维护！</div>
          </div>
        </div>
      </div>
      <div v-if="type" class="role-content-right">
        <div class="toptip">
          <span class="green_line"></span>
          空间风险总览
        </div>
        <div style="padding: 10px 0 10px 26px;">
          <div v-for="(item, index) in riskList" :key="index" class="item">
            {{ item.lable }}
            <el-link type="primary" :disabled="riskData[item.isNum]==0?true:false" @click="getRiskList(item.dictValue)">{{ riskData[item.isNum] }}</el-link>
          </div>
        </div>
        <div class="toptip">
          <span class="green_line"></span>
          空间隐患总览
        </div>
        <div style="padding: 10px 0 10px 26px;">
          <div v-for="(item, index) in hiddenList" :key="index" class="item">
            {{ item.lable }}
            <el-link  type="primary" :disabled="questionData[item.isNum]==0||item.dictValue == 'zgl'?true:false" @click="getHiddenList(item.dictValue)">{{ questionData[item.isNum] }}</el-link>
          </div>
        </div>
        <div class="toptip" style="margin-bottom: 10px;">
          <span class="green_line"></span>
          部门风险隐患Top5
        </div>
        <el-table ref="multipleTable" v-loading="tableLoading" :data="tableData" height="320px" style="width: 100%;" element-loading-background="rgba(0, 0, 0, 0.2)">
          <el-table-column prop="teamName" show-overflow-tooltip label="部门"></el-table-column>
          <el-table-column prop="riskNum" show-overflow-tooltip label="风险数">
            <template slot-scope="scope">
              <el-link type="primary" :disabled="scope.row.riskNum==0?true:false" @click="getRiskList('', scope.row)">{{ scope.row.riskNum }}</el-link>
            </template>
          </el-table-column>
          <el-table-column prop="questionNum" show-overflow-tooltip label="隐患数">
            <template slot-scope="scope">
              <el-link type="primary" :disabled="scope.row.questionNum==0?true:false" @click="getHiddenList('', scope.row)">{{ scope.row.questionNum }}</el-link>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </PageContainer>
</template>

<script>
import { transData } from '@/util'
export default {
  name: 'RiskFourColorChart',
  components: {},
  data() {
    return {
      defaultProps: {
        children: 'children',
        label: 'gridName',
        value: 'id'
      },
      treeData: [],
      filterText: '',
      treeLoading: false,
      expanded: [],
      riskType: '',
      checkedData: [],
      pictureUrl: '', // 图片url
      srcList: [], // 放大查看的url
      type: '', // 是否从首页跳入
      riskList: [
        {
          lable: '重大风险',
          isNum: 'majorCount',
          dictValue: '1'
        },
        {
          lable: '较大风险',
          isNum: 'moreCount',
          dictValue: '2'
        },
        {
          lable: '一般风险',
          isNum: 'commonDangerCount',
          dictValue: '3'
        },
        {
          lable: '低风险',
          isNum: 'lowDangerCount',
          dictValue: '4'
        }
      ],
      hiddenList: [
        {
          lable: '重大隐患',
          isNum: 'majorDangerCount',
          dictValue: '2'
        },
        {
          lable: '一般隐患',
          isNum: 'commonDangerCount',
          dictValue: '1'
        },
        {
          lable: '未处理隐患',
          isNum: 'noRectificationCount',
          dictValue: 'wcl'
        },
        {
          lable: '隐患整改率',
          isNum: 'efficient',
          dictValue: 'zgl'
        },
        {
          lable: '本月新增隐患',
          isNum: 'currMonthTotal',
          dictValue: 'xz'
        }
      ],
      tableData: [],
      tableLoading: false,
      riskData: {}, // 空间风险总览
      questionData: {} // 空间隐患总览
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    },
    screenHeight(n, o) {}
  },
  created() {
    this.getDictValue()
    this.type = this.$route.query.type || ''
  },
  methods: {
    getDictValue() {
      this.treeLoading = true
      // 获取风险位置
      this.$api.ipsmGetGridList({ picture: 0 }).then((res) => {
        this.treeLoading = false
        let treeList = transData(res.data, 'id', 'parentId', 'children')
        this.treeData = treeList
        this.expanded = [this.treeData[0].id]
        this.riskType = [this.treeData[0].gridName]
        this.checkedData = this.treeData[0]
        this.$nextTick(() => {
          this.$refs.tree.setCurrentKey(this.checkedData)
        })
        this.handleNodeClick(this.checkedData)
      })
    },
    filterNode(value, data) {
      if (!value) return true
      return data.gridName.indexOf(value) !== -1
    },
    // 树状图点击
    handleNodeClick(data) {
      this.riskType = data.gridName
      this.tableCode = Number(data.gridName)
      this.checkedData = data
      this.$refs.tree.setCurrentKey(this.checkedData)
      this.pictureUrl = this.$tools.imgUrlTranslation(data.pictureUrl)
      this.srcList.push(this.$tools.imgUrlTranslation(data.pictureUrl))
      this.getetFourStatistics()
    },
    // 风险四色图统计
    getetFourStatistics() {
      this.tableLoading = true
      this.$api.ipsmGetFourStatistics({ gridId: this.checkedData.id }).then((res) => {
        if (res.code == 200) {
          this.riskData = res.data.riskData
          this.questionData = res.data.questionData
          this.tableData = res.data.riskQuestionList
        }
        this.tableLoading = false
      })
    },
    // 图片下载
    imgDownlod() {
      this.downloadIamge(this.pictureUrl, this.checkedData.gridName)
    },
    /**
     * @description: 下载图片到本地
     * @param {String} imgsrc 图片url
     * @param {String} name   图片名称
     */
    downloadIamge(imgsrc, name) {
      // 下载图片地址和图片名
      var image = new Image()
      // 解决跨域 Canvas 污染问题
      image.setAttribute('crossOrigin', 'anonymous')
      image.onload = function () {
        var canvas = document.createElement('canvas')
        canvas.width = image.width
        canvas.height = image.height
        var context = canvas.getContext('2d')
        context.drawImage(image, 0, 0, image.width, image.height)
        var url = canvas.toDataURL('image/png') // 得到图片的base64编码数据

        var a = document.createElement('a') // 生成一个a元素
        var event = new MouseEvent('click') // 创建一个单击事件
        a.download = name || 'photo' // 设置图片名称
        a.href = url // 将生成的URL设置为a.href属性
        a.dispatchEvent(event) // 触发a的单击事件
      }
      image.src = imgsrc
    },
    // 点击进入风险列表
    getRiskList(riskLevel, row) {
      this.$router.push({
        name: 'riskList',
        query: {
          riskLevel,
          gridId: this.checkedData.id,
          deptCode: row ? row.id : ''
        }
      })
    },
    // 点击进入隐患列表
    getHiddenList(val, row) {
      this.$router.push({
        name: 'dangerList',
        query: {
          riskCode: val == '1' || val == '2' ? val : '',
          flowCode: val == 'wcl' ? val : '',
          dateType: val == 'xz' ? 'month' : '',
          gridId: this.checkedData.id,
          deptCode: row ? row.id : ''
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: linear-gradient(to right, #d9e1f8, #fff);
}

::v-deep .el-tree-node__content {
  height: 32px;
}

.role-content {
  height: 100%;
  display: flex;

  .role-content-left {
    width: 246px;
    height: 100%;
    padding: 10px;
    background: #fff;
    border-radius: 4px;
    margin-right: 12px;

    .left_content {
      height: calc(100% - 60px);
      padding: 10px;
      // overflow: auto;
      .tree {
        margin-top: 20px;
        height: calc(100% - 40px);
        overflow: auto;
      }
    }
  }

  .role-content-center {
    flex: 1;
    height: 100%;
    min-width: 0;
    padding: 10px;
    background: #fff;
    border-radius: 4px;
    overflow: auto;
  }

  .role-content-right {
    width: 320px;
    background-color: #fff;
    margin-left: 12px;
    overflow: auto;
    padding: 10px;

    .item {
      font-size: 16px;
      margin-bottom: 10px;
      cursor: pointer;

      ::v-deep .el-link {
        font-size: 16px;
      }
    }
  }
}
</style>
