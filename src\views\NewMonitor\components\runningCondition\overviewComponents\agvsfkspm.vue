<!-- 运行监测 -->
<template>
  <ContentCard
    :title="item.componentTitleShow"
    :scrollbarHover="true"
    :cstyle="{ width: '100%', height: '100%' }"
    class="drag_class"
    :hasMoreOper="['edit']"
    @more-oper-event="(val) => $emit('all-more-Oper', val, item.componentName, item.chartType)"
  >
    <div slot="title-right" class="data-btns">
      <span :class="{ 'active-btn': totalCostDateType == 'all' }" @click="changeTime('all')">全部</span>
      <!-- <span :class="{ 'active-btn': totalCostDateType == 'day' }" @click="changeTime('day')">今日</span> -->
      <span :class="{ 'active-btn': totalCostDateType == 'week' }" @click="changeTime('week')">本周</span>
      <span :class="{ 'active-btn': totalCostDateType == 'month' }" @click="changeTime('month')">本月</span>
      <span :class="{ 'active-btn': totalCostDateType == 'year' }" @click="changeTime('year')">本年</span>
    </div>
    <div slot="content" class="operation-list">
      <echarts :ref="`agvsfkspm${item.componentDataType}`" :domId="`agvsfkspm${item.componentDataType}`" width="100%" height="100%" />
    </div>
  </ContentCard>
</template>
<script lang="jsx">
import moment from 'moment'
export default {
  name: 'agvsfkspm',
  props: {
    item: {
      type: Object,
      default: () => {}
    },
    systemCode: {
      type: String,
      default: ''
    },
    deviceId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      totalCostDateType: 'all',
      dateTime: {
        startTime: '',
        endTime: ''
      },
      chartType: '1'
    }
  },
  created() {},
  mounted() {
    setTimeout(() => {
      if (this.deviceId) {
        this.getDeviceAnalysisData()
      }
    }, 150)
  },
  methods: {
    changeTime(type) {
      this.totalCostDateType = type
      if (type == 'all') {
        this.dateTime.startTime = ''
        this.dateTime.endTime = ''
        this.getDeviceAnalysisData()
      } else if (type == 'day') {
        this.dateTime.startTime = moment().format('YYYY-MM-DD 00:00:00')
        this.dateTime.endTime = moment().format('YYYY-MM-DD 23:59:59')
        this.getDeviceAnalysisData()
      } else if (type == 'week') {
        this.dateTime.startTime = moment().startOf('isoWeek').format('YYYY-MM-DD 00:00:00')
        this.dateTime.endTime = moment().endOf('isoWeek').format('YYYY-MM-DD 23:59:59')
        this.getDeviceAnalysisData()
      } else if (type == 'month') {
        this.dateTime.startTime = moment().startOf('month').format('YYYY-MM-DD  00:00:00')
        this.dateTime.endTime = moment().endOf('month').format('YYYY-MM-DD  23:59:59')
        this.getDeviceAnalysisData()
      } else if (type == 'year') {
        this.dateTime.startTime = moment().startOf('year').format('YYYY-MM-DD  00:00:00')
        this.dateTime.endTime = moment().endOf('year').format('YYYY-MM-DD  23:59:59')
        this.getDeviceAnalysisData()
      }
    },
    getDeviceAnalysisData() {
      if (this.item.componentDataType === 'agvsfkspm') {
        this.$api.getQueryInstanceFunction(this.deviceId, 'startPositions', this.dateTime).then((res) => {
          if (res.status == 200) {
            this.appendEchartsData(res.result[0])
          }
        })
      } else if (this.item.componentDataType === 'agvmddkspm') {
        this.$api.getQueryInstanceFunction(this.deviceId, 'positions', this.dateTime).then((res) => {
          if (res.status == 200) {
            this.appendEchartsData(res.result[0])
          }
        })
      }
    },
    appendEchartsData(data) {
      const baseData = {
        color: this.item.componentDataType == 'agvsfkspm' ? '#3562DB' : '#08CB83',
        grid: {
          left: '-9%',
          right: '8%',
          bottom: '5%',
          top: '5%',
          containLabel: true
        },
        xAxis: {
          max: 'dataMax'
        },
        yAxis: {
          type: 'category',
          data: [], // 这里填充你的数据
          inverse: true,
          axisLabel: {
            margin: 90, // 刻度标签与轴线之间的距离。
            left: -2, // 整个echart位置
            textStyle: {
              align: 'left' //* *
            },
            formatter: function (value, index) {
              const indexNumber = index + 1 // 序号从1开始
              const truncatedValue = value.length > 5 ? value.substring(0, 5) + '...' : value // 截断文字
              const styleKey = index < 3 ? 'highlight' : 'normal'
              return `{${styleKey}|${indexNumber}} ${truncatedValue}`
            },
            rich: {
              highlight: {
                color: '#3562DB', // 前3个序号文字颜色
                backgroundColor: '#E6EFFC', // 背景颜色
                borderWidth: 2, // 边框宽度
                borderRadius: 4, // 圆角
                padding: [5, 5] // 内边距
              },
              normal: {
                color: '#666', // 其他序号颜色
                backgroundColor: '#F2F3F5', // 背景颜色
                borderWidth: 2, // 边框宽度
                borderRadius: 4, // 圆角
                padding: [4, 4] // 内边距
              }
            }
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{b} : {c}',
          confine: true
        },
        series: [
          {
            realtimeSort: true,
            name: 'X',
            type: 'bar',
            data: [],
            label: {
              show: true,
              position: 'right',
              valueAnimation: true
            }
          }
        ],
        dataZoom: [
          {
            type: 'inside',
            yAxisIndex: [0],
            start: 0, // 起始位置
            end: 100, // 结束位置
            width: 10
          },
          {
            type: 'slider',
            yAxisIndex: [0],
            start: 0,
            end: 100,
            width: 15
          }
        ]
      }
      if (data && data.length > 0) {
        data.length > 10 ? (baseData.dataZoom[0].end = 3) : (baseData.dataZoom[0].end = 100)
        data.forEach((item) => {
          baseData.yAxis.data.push(item.positionName)
          baseData.series[0].data.push(item.distributeJobCount)
        })
      }
      this.$refs[`agvsfkspm${this.item.componentDataType}`].init(baseData)
    }
  }
}
</script>
<style lang="scss" scoped>
.data-btns {
  position: absolute;
  right: 7%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  & > span {
    width: 44px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    margin: 0 4px;
    background-color: #f6f5fa;
    font-size: 14px;
    font-family: 'PingFang SC-Medium', 'PingFang SC';
    border: none;
    border-radius: 2px;
    color: #7f848c;
    cursor: pointer;
  }
  .active-btn {
    background-color: #e6effc !important;
    color: #3562db !important;
    border-color: #e6effc !important;
  }
}
.operation-list {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
}
</style>
