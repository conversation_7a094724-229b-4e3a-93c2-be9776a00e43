<template>
  <div>
    <el-dialog custom-class="model-dialog" title="修改风险等级配置" :visible="dialogVisible" @close="closeDialog">
      <div v-loading class="content" style="width: 100%; background-color: #fff; padding: 10px;">
        <el-form ref="formInline" :model="formInline" :inline="true" class="advanced-search-form" label-position="right" label-width="90px">
          <el-form-item label="风险等级：" prop="judgeExplain">
            <template>
              <span>{{ formInline.judgeExplain }}</span>
            </template>
          </el-form-item>
          <br />
          <el-form-item label="分值：" prop="score">
            <el-select v-model="formInline.min" placeholder="状态" @change="minChange">
              <el-option label="[" style="width: 100%;" value="0"></el-option>
              <el-option label="(" style="width: 100%;" value="1"></el-option>
            </el-select>
            <el-input
              v-model.number="formInline.minNum"
              placeholder="请输入最小分值"
              style="margin-left: 5px; width: 150px;"
              oninput="value=value.replace(/[^\d.]|(\.\d{3})|(\.\.)/g,'')"
              @input="handleInputMin"
            ></el-input>
            <span>~</span>
            <el-input
              v-model.number="formInline.maxNum"
              placeholder="请输入最大分值"
              oninput="value=value.replace(/[^\d.]|(\.\d{3})|(\.\.)/g,'')"
              style="width: 150px;"
              @input="handleInput"
            ></el-input>
            <el-select v-model="formInline.max" style="margin-left: 5px;" placeholder="状态" @change="maxChange">
              <el-option label="]" style="width: 100%;" value="0"></el-option>
              <el-option label=")" style="width: 100%;" value="1"></el-option>
            </el-select>
            <br />
            <span style="color: #cacdd4;">分值说明：英文"()"不包含此数值，"[]"包含此数值。</span>
          </el-form-item>
          <br />
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" plain @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="closeDialogRole">提 交</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script type="text/ecmascript-6">
export default {
  props: {
    dialogVisible: {
      type: Boolean
    },
    multipleSelection: {},
    dataList: {
      type: Array
    }
  },
  data() {
    return {
      formInline: {
        score: '',
        judgeExplain: '重大风险',
        min: '0',
        max: '1',
        minName: '[',
        maxName: ')',
        minNum: Number,
        maxNum: Number,
        riskJudgeId: '',
        a: Number,
        b: Number,
        c: Number,
        d: Number
      }
    }
  },
  watch: {
    multipleSelection() {
      if (this.multipleSelection.id) {
        this.setFormInline()
      }
    }
  },
  mounted() {},
  methods: {
    handleInputMin(value) {
      if (value != '') {
        if (value.indexOf('.') > -1) {
          this.formInline.minNum = value.slice(0, value.indexOf('.') + 3)
          this.formInline.minNum = value.replace(/[^\d.]|(\.\d{3})/g, '')
        } else {
          this.formInline.minNum = value
        }
      }
    },
    handleInput(value) {
      if (value != '') {
        if (value.indexOf('.') > -1) {
          this.formInline.maxNum = value.slice(0, value.indexOf('.') + 3)
        } else {
          this.formInline.maxNum = value
        }
      }
    },
    closeDialogRole() {
      if (!this.formInline.minNum.toString()) {
        return this.$message.error('请输入最小分值')
      }
      if (!this.formInline.maxNum) {
        return this.$message.error('请输入最大分值')
      }
      if (JSON.parse(this.formInline.maxNum) < JSON.parse(this.formInline.minNum)) {
        return this.$message.error('最大分值不得小于最小分值')
      }
      let riskLevel = this.multipleSelection.riskLevel

      if (riskLevel == 1) {
        if (JSON.parse(this.formInline.maxNum) > JSON.parse(this.dataList[0].LECMaxScore || this.dataList[0].LSRMaxScore)) {
          return this.$message.error('最大分值不得超过' + (this.dataList[0].LECMaxScore || this.dataList[0].LSRMaxScore))
        }
        this.a =  this.formInline.minName == '[' ? JSON.parse(this.formInline.minNum) + 0.01 : this.formInline.minNum
        this.b =  this.dataList[1].score.slice(-1) == ']' ? JSON.parse(this.dataList[1].maxScore) : JSON.parse(this.dataList[1].maxScore) - 0.01
      } else if (riskLevel == 2) {
        this.a =  this.formInline.minName == '[' ? JSON.parse(this.formInline.minNum) + 0.01 : this.formInline.minNum
        this.b =  this.dataList[2].score.slice(-1) == ']' ? JSON.parse(this.dataList[2].maxScore) : JSON.parse(this.dataList[2].maxScore) - 0.01
        this.c =  this.formInline.maxName == ']' ? JSON.parse(this.formInline.maxNum) - 0.01 : this.formInline.maxNum
        this.d =  this.dataList[0].score[0] == '[' ? JSON.parse(this.dataList[0].minScore) : JSON.parse(this.dataList[0].minScore) + 0.01
      } else if (riskLevel == 3) {
        this.a =  this.formInline.minName == '[' ? JSON.parse(this.formInline.minNum) + 0.01 : this.formInline.minNum
        this.b =  this.dataList[3].score.slice(-1) == ']' ? JSON.parse(this.dataList[3].maxScore) : JSON.parse(this.dataList[3].maxScore) - 0.01
        this.c =  this.formInline.maxName == ']' ? this.formInline.maxNum - 0.01 : this.formInline.maxNum
        this.d =  this.dataList[1].score[0] == '[' ? JSON.parse(this.dataList[1].minScore) : JSON.parse(this.dataList[1].minScore) + 0.01
      } else if (riskLevel == 4) {
        if (JSON.parse(this.formInline.minNum) < JSON.parse(this.dataList[3].LECMinScore || this.dataList[3].LSRMinScore)) {
          return this.$message.error('最小分值不得低于' + (this.dataList[3].LECMinScore || this.dataList[3].LSRMinScore))
        }
        this.c =  this.formInline.maxName == ']' ? JSON.parse(this.formInline.maxNum) - 0.01 : this.formInline.maxNum
        this.d =  this.dataList[2].score.slice(-1) == '[' ? JSON.parse(this.dataList[2].minScore) : JSON.parse(this.dataList[2].minScore) + 0.01
      }

      if (riskLevel != 4) {
        if (this.a < this.b) {
          return this.$message.error('最小分值不得小于' + this.b)
        }
      }
      if (riskLevel != 1) {
        if (this.c > this.d) {
          return this.$message.error('最大分值不得小于' + this.d)
        }
      }
      this.$confirm(
        '等级信息变更后，风险点相关等级信息不做变更，请确认提交，并关注等级信息！',
        '提醒',
        { type: 'warning' }
      ).then((res) => {
        let data = {}
        data.score = this.formInline.minName + this.formInline.minNum + '~' + this.formInline.maxNum + this.formInline.maxName
        data.judgeExplain = this.formInline.judgeExplain
        data.riskJudgeId = this.formInline.riskJudgeId
        this.$api.ipsmUpdateRiskJudge(data).then((res) => {
          if (res.code == 200) {
            this.$message.success(res.message)
            this.$emit('closeDialogRole')
          } else {
            this.$message.error(res.message)
          }
        })
      })
    },
    minChange(val) {
      if (val == 0) {
        this.formInline.minName = '['
      } else if (val == 1) {
        this.formInline.minName = '('
      }
    },
    maxChange(val) {
      if (val == 0) {
        this.formInline.maxName = ']'
      } else if (val == 1) {
        this.formInline.maxName = ')'
      }
    },
    closeDialog() {
      this.$emit('closeDialog')
    },
    setFormInline() {
      this.formInline.minNum = JSON.parse(this.multipleSelection.minScore)
      this.formInline.maxNum = JSON.parse(this.multipleSelection.maxScore)
      this.formInline.min = this.multipleSelection.score[0]
      this.formInline.max = this.multipleSelection.score.slice(-1)
      this.formInline.judgeExplain = this.multipleSelection.judgeExplain
      this.formInline.riskJudgeId = this.multipleSelection.id
    }
  }
}
</script>
