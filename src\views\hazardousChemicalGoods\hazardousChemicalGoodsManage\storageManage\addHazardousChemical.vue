<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="content_box">
        <el-form ref="formRef" :model="formModel" :rules="rules" label-width="auto">
          <el-row :gutter="20">
            <!-- <el-col :span="8">
              <el-form-item label="危化品编码" prop="materialId">
                <el-input v-model="formModel.materialId" placeholder="请输入危化品编码"></el-input>
              </el-form-item>
            </el-col> -->
            <el-col :span="8">
              <el-form-item label="危化品名称" prop="materialName">
                <el-input v-model="formModel.materialName" placeholder="请输入危化品名称"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="基本单位" prop="basicUnitCode">
                <el-select v-model="formModel.basicUnitCode" placeholder="请选择基本单位" @change="basicUnitChange">
                  <el-option v-for="item in basicUnitOptions" :key="item.dictValue" :label="item.dictName" :value="item.dictValue"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="所属分类" prop="materialTypeCode">
                <el-cascader
                  :options="classifyOptions"
                  ref="classifyCascader"
                  @change="classifyChange"
                  placeholder="请选择所属分类"
                  :show-all-levels="true"
                  v-model="formModel.materialTypeCode"
                  :props="classifyPropsType"
                ></el-cascader>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="规格型号" prop="model">
                <el-input v-model="formModel.model" placeholder="请输入规格型号"></el-input>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="8">
              <el-form-item label="数量" prop="unitPrice">
                <el-input v-model="formModel.unitPrice" placeholder="请输入数量"
                  @keyup.native="proving('number')"></el-input>
              </el-form-item>
            </el-col> -->
            <el-col :span="8">
              <el-form-item label="供应商" prop="supplierId">
                <el-select v-model="formModel.supplierId" placeholder="请选择供应商" @change="supplierChange" v-selectLoadmore="loadmore">
                  <el-option v-for="item in supplierOptions" :key="item.id" :label="item.unitsName" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="生产许可证" prop="manufacturingLicense">
                <el-input v-model="formModel.manufacturingLicense" placeholder="请输入生产许可证"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="商品编码" prop="goodsCode">
                <el-input v-model="formModel.goodsCode" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')" placeholder="请输入商品编码" maxlength="13"></el-input>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="8">
              <el-form-item label="品牌" prop="trademark">
                <el-input v-model="formModel.trademark" placeholder="请输入品牌"></el-input>
              </el-form-item>
            </el-col> -->
            <el-col :span="8">
              <el-form-item label="生产厂家" prop="manufacturer">
                <el-select v-model="formModel.manufacturer" placeholder="请选择生产厂家" @change="manufacturersChange">
                  <el-option v-for="item in manufacturerOptions" :key="item.id" :label="item.unitsName" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="状态" prop="status">
                <el-radio-group v-model="formModel.status">
                  <el-radio label="0">启用</el-radio>
                  <el-radio label="1">禁用</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="最低库存" prop="minStock">
                <el-input v-model="formModel.minStock" placeholder="请输入最低库存" maxlength="11" @keyup.native="proving('min')"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="最高库存" prop="maxStock">
                <el-input v-model="formModel.maxStock" placeholder="请输入最高库存" maxlength="11" @keyup.native="proving('max')"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24" style="margin: 20px 0">
              <el-form-item label="图片">
                <el-upload
                  drag
                  multiple
                  action="string"
                  class="mterial_file"
                  ref="uploadFile"
                  list-type="picture-card"
                  :file-list="fileEcho"
                  :http-request="httpRequest"
                  accept=".jpg, .jpeg, .png, .JPG, .JPEG, .bmp"
                  :limit="6"
                  :on-exceed="handleExceed"
                  :on-preview="handlePictureCardPreview"
                  :beforeUpload="beforeAvatarUpload"
                  :on-remove="handleRemove"
                >
                  <i class="el-icon-upload"></i>
                  <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                </el-upload>
                <el-dialog :visible.sync="dialogVisible" title="预览" style="z-index: 99999; text-align: center">
                  <img style="max-width: 70%; max-height: 70%" :src="dialogImageUrl" alt />
                </el-dialog>
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="说明" prop="explain">
                <el-input v-model.trim="formModel.explain" maxlength="500" show-word-limit type="textarea" placeholder="请输入"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="cancel">取消</el-button>
      <el-button type="primary" @click="submitForm" :loading="formLoading">提交</el-button>
    </div>
  </PageContainer>
</template>
<script>
import { transData } from '@/util'
import store from '@/store'
export default {
  name: 'addHazardousChemical',
  data() {
    return {
      // 正常表单
      formModel: {
        //materialId: '',//危化品编码
        materialName: '', // 危化品名称
        materialTypeCode: '', //所属分类code
        materialTypeName: '', //所属分类name
        basicUnitName: '', //基本单位name
        basicUnitCode: '', //基本单位code
        model: '', //规格型号
        //unitPrice: '',//数量
        supplierId: '', //供应商id
        supplierName: '', //供应商name
        manufacturer: '', //生产厂家id
        manufacturerName: '', //生产厂家name
        manufacturingLicense: '', //生产许可证
        goodsCode: '', //商品编码
        //trademark: '',//品牌
        status: '0', //状态
        minStock: '',
        maxStock: '',
        explain: ''
      },
      rules: {
        //materialId: [{ required: true, message: '请输入危化品编码', trigger: 'blur' }],
        materialName: [{ required: true, message: '请输入危化品名称', trigger: 'blur' }],
        model: [{ required: true, message: '请输入规格型号', trigger: 'blur' }],
        goodsCode: [
          {
            pattern: /^[0-9]{13}$/,
            message: '商品编码长度为13个字符',
            trigger: 'blur'
          }
        ]
      },
      basicUnitOptions: [], //基本单位下拉
      classifyOptions: [], //所属分类下拉
      supplierOptions: [], //供应商下拉
      supplierCurrentPage: 1, //供应商分页
      manufacturerOptions: [], //生产厂家下拉
      fileEcho: [],
      materialPhoto: [],
      dialogVisible: false,
      dialogImageUrl: '', //图片
      classifyPropsType: {
        label: 'name',
        value: 'code',
        children: 'children',
        emitPath: false,
        checkStrictly: true
      },
      formLoading: false //提交loading
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    //长度限制
    validateLength(rule, value, callback) {
      if (value.length !== 13) {
        callback(new Error('请输入13位长度的内容'))
      } else {
        callback()
      }
    },
    //初始化
    init() {
      this.getSelectByList('26', 'basicUnitOptions') // 基本单位
      this.getSupplierData()
      this.getManufacturerData()
      this.getClassifyData()
    },
    //获取分类数据
    getClassifyData() {
      const userInfo = this.$store.state.user.userInfo.user
      let params = {
        type: 'WZFL',
        userId: userInfo.staffId,
        userName: userInfo.staffName,
        status: 1
      }
      this.$api.materialTypeTreeKC(params).then((res) => {
        if (res.code === '200') {
          this.classifyOptions = transData(res.data, 'id', 'pid', 'children')
        }
      })
    },
    /** 供应商懒加载 */
    loadmore() {
      this.supplierCurrentPage++
      this.getSupplierData()
    },
    //获取供应商数据
    getSupplierData() {
      let params = {
        unitsTypeCode: 3,
        category: 2,
        userId: this.$store.state.user.userInfo.user.staffId,
        userName: this.$store.state.user.userInfo.user.staffName,
        status: '0',
        currentPage: this.supplierCurrentPage,
        pageSize: 15
      }
      this.$api.hscUnitManger(params).then((res) => {
        if (res.code == 200) {
          this.supplierOptions = [...this.supplierOptions, ...res.data.list]
        }
      })
    },
    //所属分类change
    classifyChange() {
      this.formModel.materialTypeName = this.$refs.classifyCascader.getCheckedNodes()[0].pathLabels.join(',') //获取选中name
    },
    //获取生产厂家数据
    getManufacturerData() {
      let params = {
        unitsTypeCode: 5,
        category: 2,
        userId: this.$store.state.user.userInfo.user.staffId,
        userName: this.$store.state.user.userInfo.user.staffName,
        status: '0'
      }
      this.$api.hscUnitManger(params).then((res) => {
        if (res.code == 200) {
          this.manufacturerOptions = res.data.list
        }
      })
    },
    //供应商change
    supplierChange(val) {
      if (val) {
        this.formModel.supplierName = this.supplierOptions.find((item) => item.id == val).unitsName
      }
    },
    //生产厂家change
    manufacturersChange(val) {
      if (val) {
        this.formModel.manufacturerName = this.manufacturerOptions.find((item) => item.id == val).unitsName
      }
    },
    //基本单位change
    basicUnitChange(val) {
      if (val) {
        this.formModel.basicUnitName = this.basicUnitOptions.find((item) => item.dictValue == val).dictName
      }
    },
    // 查询字典
    getSelectByList(str, getArr) {
      this.$api.selectByListAsset({ dictTypeId: str }).then((res) => {
        if (res.code == '200') {
          this[getArr] = res.data
        }
      })
    },
    proving(type) {
      if (type === 'min') {
        this.formModel.minStock = this.formModel.minStock.replace(/[^\.\d]/g, '')
        this.formModel.minStock = this.formModel.minStock.replace('.', '')
      } else if (type === 'max') {
        this.formModel.maxStock = this.formModel.maxStock.replace(/[^\.\d]/g, '')
        this.formModel.maxStock = this.formModel.maxStock.replace('.', '')
      } else if (type === 'number') {
        this.formModel.unitPrice = this.formModel.unitPrice.replace(/[^\.\d]/g, '')
        this.formModel.unitPrice = this.formModel.unitPrice.replace('.', '')
      }
    },
    //取消
    cancel() {
      this.$router.go(-1)
    },
    // 点击确定
    submitForm() {
      this.$refs['formRef'].validate((valid) => {
        if (valid) {
          this.formLoading = true
          const userInfo = this.$store.state.user.userInfo.user
          let params = {
            ...this.formModel,
            userId: userInfo.staffId,
            userName: userInfo.staffName
          }
          if (this.materialPhoto && this.materialPhoto.length) {
            params.materialPhoto = this.materialPhoto.join(',')
          }
          this.$api.saveHcsData(params).then((res) => {
            this.formLoading = false
            if (res.code == '200') {
              this.$message.success(res.message)
              this.$router.go(-1)
            } else {
              this.$message.error(res.message)
            }
          })
        }
      })
    },
    //  ------------------------------------------上传图片相关-----------------------------------------
    httpRequest(item) {
      const params = new FormData()
      params.append('file', item.file)
      this.uploadLoading = true
      this.$api.uploadIcon(params).then((res) => {
        this.uploadLoading = false
        if (res.code == 200) {
          this.fileEcho.push({ name: item.file.name, url: store.state.user.picPrefix + '/' + res.data, uid: item.file.uid })
          this.materialPhoto.push(store.state.user.picPrefix + '/' + res.data)
          this.$message({
            message: '上传成功',
            type: 'success'
          })
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },
    handleExceed() {
      this.$message.error('只允许传6份文件')
    },
    beforeAvatarUpload(file) {
      const isLt5M = file.size / 1024 / 1024 < 5
      if (!isLt5M) {
        this.$message.error('上传图片大小不能超过 5MB!')
        return false
      }
    },
    handleRemove(file, fileList) {
      let fileIndex = this.materialPhoto.findIndex((item) => {
        return item == file.name
      })
      this.materialPhoto.splice(fileIndex, 1)
      this.fileEcho.splice(fileIndex, 1)
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    }
  }
}
</script>
<style scoped lang="scss">
.container-content > div {
  background-color: #fff;
  height: 100%;
  padding: 24px;
  position: relative;
  .inventory-content-title {
    border-bottom: 1px solid #e4e7ed;
    padding: 12px 24px;
    cursor: pointer;
    margin-bottom: 24px;
  }
  .content_box {
    padding: 0 24px;
    background: #fff;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    height: 100%;
  }
  .el-input,
  .el-select,
  .el-cascader {
    width: 80%;
  }
}
// ::v-deep .el-upload {
//   display: block !important;
// }
::v-deep .mterial_file > .el-upload-list {
  position: absolute;
  top: 0;
  width: 500px;
  height: 180px;
  margin-left: 430px;
  overflow: auto;
}
::v-deep .el-upload--picture-card {
  border: none !important;
  width: 360px !important;
  height: 180px !important;
}
::v-deep .mterial_file .el-upload__text {
  position: absolute !important;
  left: 97px !important;
  top: 50px !important;
}
</style>

