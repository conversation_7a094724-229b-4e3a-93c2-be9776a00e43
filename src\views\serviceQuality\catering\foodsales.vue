<template>
  <PageContainer>
    <div slot="header" class="control-btn-header">
      <div class="search-from">
        <el-select v-model="searchFrom.foodType" placeholder="餐次" clearable>
          <el-option v-for="item in timeTypeList" :key="item.id" :label="item.label" :value="item.id"> </el-option>
        </el-select>
        <el-date-picker
          v-model="dataRange"
          type="datetimerange"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
        >
        </el-date-picker>

        <div style="display: flex">
          <el-button type="primary" @click="searchForm">查询</el-button>
          <el-button type="primary" plain @click="resetForm">重置</el-button>
        </div>
      </div>
    </div>
    <div slot="content" class="table-content">
      <div class="data-box">
        <div class="total-num">
          <span>订餐总数：</span>
          <span>{{ orderTotal }}</span>
        </div>
        <div class="total-amount">
          <span>菜品总价（元）：</span>
          <span>{{ orderTotalPrice }}</span>
        </div>
      </div>

      <TablePage
        ref="table"
        v-loading="tableLoading"
        :showPage="true"
        :tableColumn="tableColumn"
        :data="tableData"
        height="calc(100% - 110px)"
        :pageData="pageData"
        :pageProps="pageProps"
        @pagination="paginationChange"
      />
    </div>
  </PageContainer>
</template>
<script lang="jsx">
import moment from 'moment'

export default {
  name: 'entryRecordManage',
  components: {},
  data() {
    return {
      orderTotal: '',
      orderTotalPrice: '',
      dialogVisible: false,
      dataRange: [], // 时间范围
      searchFrom: {
        foodType: '' // 时间
      },
      timeTypeList: [
        {
          id: 1,
          label: '早餐'
        },
        {
          id: 2,
          label: '午餐'
        },
        {
          id: 3,
          label: '晚餐'
        },
        {
          id: 4,
          label: '夜宵'
        }
      ],
      tableLoading: false,
      tableColumn: [
        {
          prop: '',
          label: '序号',
          formatter: (scope) => {
            // 当前分页重置
            // return scope.$index + 1
            // 跨分页序号
            return (this.pageData.current - 1) * this.pageData.size + scope.$index + 1
          }
        },
        {
          prop: 'foodName',
          label: '菜品名称'
        },
        {
          prop: 'foodPrice',
          label: '菜品单价(元)'
        },
        {
          prop: 'foodCount',
          label: '订餐数量'
        },
        {
          prop: 'foodTotalPrice',
          label: '菜品总价(元)'
        }
        // {
        //   prop: 'score',
        //   label: '评价',
        //   render: (h, row) => {
        //     return (
        //       <div>
        //         <el-rate v-model={row.row.score} disabled></el-rate>
        //       </div>
        //     )
        //   }
        // }
      ],
      tableData: [],
      pageData: {
        current: 1,
        size: 15,
        total: 0
      },
      pageProps: {
        page: 'current',
        pageSize: 'size',
        total: 'total'
      }
    }
  },
  mounted() {
    this.getRecordList()
  },
  methods: {
    date() {
      const now = new Date()
      return moment(now).format('yyyy-MM-DD HH:mm:ss')
    },
    // 获取列表
    getRecordList() {
      let param = {
        ...this.searchFrom,
        startTime: this.dataRange[0] || '',
        endTime: this.dataRange[1] || this.date(),
        pageSize: this.pageData.size,
        pageNum: this.pageData.current
      }
      this.tableLoading = true
      this.$api
        .getDishesSaleCount(param)
        .then((res) => {
          if (res.code == 200) {
            this.orderTotal = res.data.orderTotal
            this.orderTotalPrice = res.data.orderTotalPrice

            this.tableData = res.data.list
            this.pageData.total = res.total
          }
          this.tableLoading = false
        })
        .catch(() => {
          this.tableLoading = false
        })
    },
    // 查询
    searchForm() {
      this.pageData.current = 1
      this.getRecordList()
    },
    // 重置查询
    resetForm() {
      Object.assign(this.$data.searchFrom, this.$options.data().searchFrom)
      this.dataRange = []
      this.searchForm()
    },

    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getRecordList()
    },
    // 判断当前页是否是最后一页
    isLastPage(deleteNum) {
      let deleteAfterPage = Math.ceil((this.pageData.total - deleteNum) / this.pageData.size)
      let currentPage = this.pageData.current > deleteAfterPage ? deleteAfterPage : this.pageData.current
      this.pageData.current = currentPage < 1 ? 1 : currentPage
    }
  }
}
</script>
<style lang="scss" scoped>
.control-btn-header {
  padding: 10px !important;
  .search-from {
    display: flex;
    & > div {
      margin-right: 10px;
    }
  }
}

.table-content {
  margin-top: 15px;
  border-radius: 4px;
  height: calc(100% - 15px);
  padding: 10px;
  background-color: #fff;
}
.dialogs {
  width: 100%;
  padding: 10px;
  display: flex;
  height: 725px;
  justify-content: space-between;
  > div {
    width: 49%;
    background-color: #fff;
  }
}
.entrance {
  display: flex;
  padding: 15px;
  background-color: #e6effc;
  > div:nth-child(1) {
    width: 30%;
    > p {
      width: 40px;
      text-align: center;
      margin: 54px auto;
    }
  }
  > div:nth-child(2) {
    width: 70%;
  }
}
.entranceTxt {
  display: flex;
  > p:nth-child(1) {
    width: 100px;
    font-size: 14px;
    color: #666666;
  }
  > p:nth-child(2) {
    text-align: left;
    font-size: 14px;
    color: #333333;
  }
}
.admissionPictures {
  width: 100%;
  height: 200px;
  margin-bottom: 15px;
  img {
    width: 100%;
    height: 200px;
  }
}
::v-deep .el-dialog {
  height: 800px;
}
::v-deep .model-dialog .el-dialog__body {
  overflow: hidden;
  max-height: 800px;
}
</style>
<style lang="scss">
.operationBtn-span {
  margin-right: 10px;
}
.count {
  width: 400px;
  display: flex;
  margin-bottom: 10px;
  justify-content: space-between;
  align-items: center;
  > div {
    border-radius: 5px;
    flex: 1;
    background-color: #f3f6fd;
    margin-right: 20px;
    padding: 8px 15px;
  }
}
.data-box {
  display: flex;
  margin: 8px 16px;
  margin-left: 0;
}
.data-box > div {
  background-color: rgba(53, 98, 219, 0.06);
  padding: 12px 24px;
  border-radius: 4px;
}
.data-box > div > span:nth-child(1) {
  font-size: 16px;
  color: #333;
}
.data-box > div > span:nth-child(2) {
  font-size: 16px;
  font-weight: bold;
  color: #3a62d8;
}
.data-box .total-num {
  margin-right: 24px;
}
</style>
