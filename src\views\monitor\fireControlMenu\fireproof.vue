<template>
  <PageContainer>
    <div slot="header" class="control-btn-header">
      <el-input v-model="search.gridName" placeholder="网格名称" clearable style="width: 200px"></el-input>
      <el-input v-model="search.responsibleDepartName" placeholder="责任部门" clearable style="width: 200px"></el-input>
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        unlink-panels
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="yyyy-MM-dd"
        @change="dateChange"
      >
      </el-date-picker>
      <div style="display: inline-block">
        <el-button type="primary" plain @click="resetForm">重置</el-button>
        <el-button type="primary" @click="searchForm">查询</el-button>
      </div>
    </div>
    <div slot="content" class="table-content">
      <div class="batch-control">
        <el-button type="primary" @click="add()">新增</el-button>
      </div>
      <TablePage
        ref="table"
        v-loading="tableLoading"
        class="table"
        :showPage="true"
        :tableColumn="tableColumn"
        :data="list"
        height="calc(100% - 40px - 42px)"
        :pageData="pageData"
        @pagination="paginationChange"
      />
    </div>
  </PageContainer>
</template>
<script lang="jsx">
import moment from 'moment'
moment.locale('zh-cn')
export default {
  name: 'fireproOf',
  beforeRouteEnter(to, from, next) {
    let names = []
    to.matched.map((v, i) => {
      if (i > 0) {
        v.components.default.name && names.push(v.components.default.name)
      }
    })
    next((vm) => {
      if (from.query.activeName) {
        vm.activeName = from.query.activeName
      }
      vm.$store.commit('keepAlive/add', names)
    })
  },
  async beforeRouteLeave(to, from, next) {
    // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
    if (!['addFireproof'].includes(to.name)) {
      // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      tableLoading: false,
      search: {
        endTime: '',
        startTime: '',
        gridName: '',
        responsibleDepartName: '' // 日志类型
      },
      dateRange: [],
      list: [],
      pageData: {
        page: 1,
        pageSize: 15,
        total: 0
      },
      tableColumn: [
        {
          prop: 'serialNumber',
          label: '序号',
          width: 80,
          formatter: (scope) => {
            return (this.pageData.page - 1) * this.pageData.pageSize + scope.$index + 1
          }
        },
        {
          prop: 'gridName',
          label: '网格名称'
        },
        {
          prop: 'responsibleDepartName',
          label: '责任部门'
        },
        {
          prop: 'responsiblePersonName',
          label: '责任人'
        },
        {
          prop: 'safePersonName',
          label: '安全责任人'
        },
        {
          width: 140,
          prop: 'operation',
          label: '操作',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span style="color: #3562db" onClick={() => this.goDetail(row.row.id,row.row.gridName, 'detail')}>查看</span>
                <span style="color: #3562db;margin-left:5px" onClick={() => this.goDetail(row.row.id,row.row.gridName, 'edit')}>编辑</span>
                <span style="color: #3562db;margin-left:5px" onClick={() => this.del(row.row)}>删除</span>
              </div>
            )
          }
        }
      ]
    }
  },
  created() {
    this.getList()
  },
  activated() {
    this.getList()
  },
  methods: {
    dateChange(val) {
      this.search.startTime = val[0]
      this.search.endTime = val[1]
    },
    getList() {
      let params = {...this.search, ...this.pageData}
      this.$api.fireproofList(params).then(res => {
        if (res.code === '200') {
          this.list = res.data.records
          this.pageData.total = res.data.total
        }
      })
    },
    // 重置查询表单
    resetForm() {
      this.pageData.page = 1
      this.search = {
        endTime: '',
        startTime: '',
        gridName: '',
        responsibleDepartName: '' // 日志类型
      }
      this.dateRange = []
      this.searchForm()
    },
    // 查询
    searchForm() {
      this.pageData.page = 1
      this.getList()
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getList()
    },
    add() {
      this.$router.push({name: 'addFireproof', query: {type: 'add'}})
    },
    goDetail(id,name, type) {
      this.$router.push({name: 'addFireproof', query: {id,name, type}})
    },
    del(row) {
      this.$confirm('删除后将无法恢复，是否确定删除？', '信息提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        this.$api.delFireprool({id: row.id}, { 'operation-type': 3, 'operation-name': row.gridName, 'operation-id': row.id}).then(res => {
          if (res.code === '200') {
            this.$message({
              type: 'success',
              message: '删除成功'
            })
            this.getList()
          }
        })
      }).catch(() => {})
    }
  }
}
</script>
<style lang="scss" scoped>
.control-btn-header {
  padding: 10px !important;
  & > div {
    margin-right: 10px;
  }
}
.table-content {
  margin-top: 15px;
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 15px);
  padding: 10px;
  .batch-control {
    margin-bottom: 10px;
  }
}
</style>
