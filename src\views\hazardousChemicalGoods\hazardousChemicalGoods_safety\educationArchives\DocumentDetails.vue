<template>
  <PageContainer :footer="true">
    <div slot="content" class="documentDetailsBox">
      <div class="documentDetails">
        <el-tabs v-model="activeName" :before-leave="handleBeforeLeave" @tab-click="handleTabsClick">
          <el-tab-pane label="文档信息" name="DocumentInformation"></el-tab-pane>
          <el-tab-pane label="基本信息" name="EssentialInformation"></el-tab-pane>
          <el-tab-pane label="操作记录" name="OperationRecord"></el-tab-pane>
          <el-tab-pane label="浏览记录" name="BrowsingHistory"></el-tab-pane>
        </el-tabs>
        <div class="content">
          <component :is="activeName" :ref="activeName" :isEdit="isEdit" :formData="formData" @fileDelete="fileDelete" />
        </div>
      </div>
    </div>
    <div slot="footer">
      <el-button v-if="isEdit" @click="handleCancel">取消</el-button>
      <el-button v-else @click="handleBack">返回</el-button>
      <template v-if="['DocumentInformation', 'EssentialInformation'].includes(activeName) && !$route.query.notShowEdit">
        <el-button v-if="!isEdit" type="primary" @click="handleEdit"> 编辑 </el-button>
        <el-button v-else type="primary" @click="handleSave">保存</el-button>
      </template>
    </div>
  </PageContainer>
</template>
<script>
import DocumentInformation from './components/DocumentInformation.vue'
import EssentialInformation from './components/EssentialInformation.vue'
import OperationRecord from '@/views/operationPort/dossierManager/components/OperationRecord.vue'
import BrowsingHistory from '@/views/operationPort/dossierManager/components/BrowsingHistory.vue'
import { cloneDeep } from 'lodash'
export default {
  name: 'DocumentDetails',
  components: {
    DocumentInformation,
    EssentialInformation,
    OperationRecord,
    BrowsingHistory
  },
  data() {
    return {
      activeName: 'DocumentInformation',
      isEdit: false,
      formData: { archiveFileList: [] },
      copyFormData: ''
    }
  },
  created() {
    this.handleGetData()
  },
  methods: {
    handleGetData() {
      const { id } = this.$route.query
      this.$api.fileManagement.getById({ id }).then((res) => {
        this.formData = res.data
      })
    },
    handleBeforeLeave() {
      if (this.isEdit) return false
    },
    handleTabsClick() {
      if (this.isEdit) {
        this.$confirm('确定保存修改信息', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          cancelButtonClass: 'el-button--primary is-plain',
          type: 'info'
        })
          .then(() => {
            this.handleSave()
          })
          .catch(() => {})
      }
    },
    handleSave() {
      const refs = this.$refs[this.activeName]
      refs.$refs.form.validate((res) => {
        if (res) {
          const { id: archiveId } = this.$route.query
          const {
            archiveDate,
            archiveFileList,
            archiveName,
            archiveNumber,
            archiveOwnerDeptId,
            archiveOwnerDeptName,
            archiveOwnerId,
            archiveOwnerName,
            folderId,
            remark,
            expireDay,
            archiveModel,
            supplier,
            saveLocation,
            trainingContent,
            trainingObjectives,
            training
          } = this.formData
          const params = {
            archiveDate,
            archiveFileList,
            archiveName,
            archiveNumber,
            archiveModel,
            archiveOwnerDeptId,
            archiveOwnerDeptName,
            archiveOwnerId,
            archiveOwnerName,
            expireDay,
            folderId,
            remark,
            supplier,
            saveLocation,
            trainingContent,
            trainingObjectives,
            training
          }
          this.$api.fileManagement.updateFile({ ...params, archiveId, archiveType: '3' }).then((res) => {
            if (res.code === '200') {
              this.$message.success('保存成功')
              this.isEdit = false
              this.handleGetData()
            }
          })
        }
      })
    },
    handleCancel() {
      this.formData = this.copyFormData
      this.isEdit = false
    },
    handleEdit() {
      this.copyFormData = cloneDeep(this.formData)
      this.isEdit = true
    },
    handleBack() {
      this.$router.go(-1)
    },
    fileDelete(index) {
      this.formData.archiveFileList.splice(index, 1)
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .container-content {
  overflow: auto !important;
}
.documentDetailsBox {
  .documentDetails {
    padding: 0 16px 16px;
    background: #ffffff;
    .content {
      width: 100%;
      padding: 20px 40px 0;
    }
  }
}
</style>
