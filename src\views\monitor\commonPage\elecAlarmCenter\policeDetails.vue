<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content" :style="{ padding: taskDetail.assetId ? '10px 26% 10px 10px' : '10px' }">
      <div v-loading="blockLoading" class="sino-content set_1366_height policeDetails">
        <div class="maintain-content-block" style="min-height: 150px">
          <div class="maintain-title">
            <span class="title-tag"></span>
            <span class="title-text">{{ header.titleOne }}</span>
          </div>
          <div class="maintain-list">
            <div
              v-for="(item, index) in option"
              :key="index"
              :class="[!item.remake ? 'maintain-list-item' : 'maintain-list-block']"
              :style="{
                display: item.inline || item.remake ? 'block' : 'inline-block'
              }"
            >
              <template>
                <label style="display: flex" class="list-item">
                  <span class="label">{{ item.label }}</span>
                  <span
                    class="value"
                    style="width: calc(100% - 100px); text-overflow: ellipsis; white-space: nowrap; overflow: hidden; display: inline-block"
                    :title="getTaskValue(taskDetail[item.value])"
                  >
                    {{ getTaskValue(taskDetail[item.value]) }}
                  </span>
                </label>
              </template>
            </div>
            <div class="maintain-list-block">
              <label style="display: flex" class="list-item">
                <span class="label">联动摄像头：</span>
                <span v-for="(item, index) in videoTabsList" :key="index" class="value" style="margin-right: 10px; cursor: pointer; color: #5188fc" @click="videoChange(item)">{{
                  item.vidiconName
                }}</span>
                <span v-if="!videoTabsList.length" style="color: rgb(144 147 153)">暂无关联摄像头</span>
              </label>
            </div>
          </div>
        </div>
        <div class="maintain-content-block">
          <div class="maintain-title">
            <span class="title-tag"></span>
            <span class="title-text">{{ header.titleTwo }}</span>
          </div>
          <div class="maintain-list">
            <div
              v-for="(item, index) in header.detailList"
              :key="index"
              :class="[!item.remake ? 'maintain-list-item' : 'maintain-list-block']"
              :style="{
                display: item.inline || item.remake ? 'block' : 'inline-block'
              }"
            >
              <template>
                <label style="display: flex" class="list-item">
                  <span class="label">{{ item.label }}</span>
                  <span
                    class="value"
                    style="width: calc(100% - 100px); text-overflow: ellipsis; white-space: nowrap; overflow: hidden; display: inline-block"
                    :title="getTaskValue(taskDetail[item.value])"
                  >
                    {{ getTaskValue(taskDetail[item.value]) }}
                  </span>
                </label>
              </template>
            </div>
            <div>
              <div>
                <el-form ref="formInline" :model="formInline" label-width="130px" :rules="rules">
                  <el-form-item label="处置结果：" prop="disposeResult">
                    <el-select v-if="$route.query.type != 'check'" v-model="formInline.disposeResult" class="sino_sdcp_input mr15" placeholder="请选择处置结果">
                      <el-option v-for="item in chuzhiList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                    </el-select>
                    <span v-else>
                      {{ taskDetail.disposeResultName }}
                    </span>
                  </el-form-item>
                  <el-form-item v-if="monitorData.projectName === '电梯监测'" label="处置时间：" prop="disposeTime">
                    <el-date-picker
                      v-if="$route.query.type != 'check'"
                      v-model="formInline.disposeTime"
                      type="datetime"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      placeholder="选择日期时间"
                    >
                    </el-date-picker>
                    <span v-else>{{ taskDetail.disposeTime || '无' }}</span>
                  </el-form-item>
                  <br />
                  <el-form-item label="处置结果说明：">
                    <el-input
                      v-if="$route.query.type != 'check'"
                      v-model="formInline.disposeText"
                      type="textarea"
                      style="width: 540px"
                      class="project-textarea"
                      placeholder="请输入处置结果说明"
                      show-word-limit
                      :autosize="{ minRows: 4, maxRows: 4 }"
                      maxlength="200"
                    ></el-input>
                    <span v-else>{{ taskDetail.disposeText || '无' }}</span>
                  </el-form-item>
                  <br />
                  <el-form-item label="相关附件：" prop="file">
                    <el-upload
                      v-if="$route.query.type != 'check'"
                      ref="uploadFile"
                      drag
                      multiple
                      class="mterial_file"
                      action="string"
                      :file-list="fileEcho"
                      :http-request="httpRequest"
                      accept=".jpg, .jpeg, .png, .JPG, .JPEG, .gif, .word, .WORD, .Excel, .pdf, .PDF, .doc, .docx, .DOCX, .xlsx, .xls, .zip, .zipx, .tar, .7z, .mp4, .mp3"
                      :limit="9"
                      :on-exceed="handleExceed"
                      :beforeUpload="beforeAvatarUpload"
                      :on-remove="handleRemove"
                    >
                      <i class="el-icon-upload"></i>
                      <!-- :on-preview="handlePictureCardPreview" -->
                      <div class="el-upload__text" style="top: 33px">
                        将文件拖到此处，或
                        <em>点击上传</em>
                      </div>
                      <div slot="tip" class="el-upload__tip">可上传单个文件，小于50M，支持音频、视频、office、图片、压缩包等类型</div>
                    </el-upload>
                    <div v-else style="color: #5188fc; cursor: pointer">
                      <div v-for="(item, i) of urlList" :key="i">
                        <div @click="downLoad(item)">{{ item.name }}</div>
                      </div>
                    </div>
                  </el-form-item>
                </el-form>
              </div>
            </div>
          </div>
        </div>
        <div v-show="electrical" class="maintain-content-block">
          <div class="maintain-title">
            <span class="title-tag"></span>
            <span class="title-text">实时监测</span>
          </div>
          <div class="maintain-list">
            <div v-for="(item, index) in option" :key="index" style="display: inline-block" :class="[!item.remake ? 'maintain-list-item' : 'maintain-list-block']">
              <img style="width: 187px" src="@/assets/images/monitor/null.png" alt="" />
            </div>
          </div>
        </div>
        <!-- <div class="maintain-content-block" v-if="taskDetail.imsVidicon">
          <div class="maintain-title">
            <span class="title-tag"></span>
            <span class="title-text">实时监测</span>
          </div>
          <div class="maintain-list video_height">
            <videoShow
              :videoArray="taskDetail.imsVidicon.split(',')"
              :videoNameArray="taskDetail.imsVidiconName.split(',')"
            ></videoShow>
          </div>
        </div> -->
      </div>
      <div v-if="taskDetail.assetId" class="deviceInfo">
        <div class="maintain-content-block">
          <div class="maintain-title">
            <span class="title-tag"></span>
            <span class="title-text">设备信息</span>
          </div>
          <div class="maintain-list">
            <p v-for="item in deviceInfoList" :key="item.label" class="info-item">
              <span class="label">{{ item.label }}</span>
              <span class="value">{{ deviceInfoData[item.value] || '-' }} {{ item.value == 'serviceLife' ? '月' : '' }}</span>
            </p>
          </div>
        </div>
        <div class="maintain-content-block" style="flex: 1; display: flex; flex-direction: column">
          <div class="maintain-title">
            <span class="title-tag"></span>
            <span class="title-text">历史报警分析</span>
            <el-radio-group v-model="dateType" size="mini" @input="dateTypeSwitch">
              <el-radio-button label="日"></el-radio-button>
              <el-radio-button label="周"></el-radio-button>
              <el-radio-button label="月"></el-radio-button>
              <el-radio-button label="全部"></el-radio-button>
            </el-radio-group>
          </div>
          <div class="maintain-list" style="flex: 1">
            <div class="alarm-count">
              <div v-for="item in policeStateCountList" :key="item.label" class="count-item">
                <p class="count-value">{{ item.value || 0 }}</p>
                <p class="count-label">{{ item.label }}</p>
              </div>
            </div>
            <div id="alarmTypeChaet" class="alarmTypeChaet"></div>
          </div>
        </div>
      </div>
      <template v-if="cameraTalkDialogShow">
        <el-dialog
          custom-class="camera-loader"
          :title="taskDetail.surveyName"
          :visible.sync="cameraTalkDialogShow"
          :close-on-click-modal="false"
          :close-on-press-escape="false"
          @before-close="() => (cameraTalkDialogShow = false)"
        >
          <div class="camera-talk-content">
            <div class="talk-content">
              <rtspCavas ref="rtspCavas" :rtspUrl="videoUrl" :videoName="videoName" :hasCavas="Boolean(videoTabsList.length)" class="videoflv"></rtspCavas>
            </div>
          </div>
        </el-dialog>
      </template>
    </div>
    <div slot="footer">
      <el-button
        type="primary"
        plain
        @click="
          () => {
            $router.go(-1)
          }
        "
      >
        关闭
      </el-button>
      <el-button type="primary" :disabled="type != 'add'" @click="complete">保存</el-button>
    </div>
  </PageContainer>
</template>
<script>
import moment from 'moment'
moment.locale('zh-cn')
import * as echarts from 'echarts'
import { monitorTypeList } from '@/util/dict.js'
export default {
  name: 'policeDetails',
  // 路由前置守卫动态修改meta中的title
  beforeRouteEnter(to, from, next) {
    if (to.meta.title) {
      const typeList = {
        add: '报警处置',
        check: '报警详情'
      }
      to.meta.title = typeList[to.query.type] ?? '报警详情'
    }
    next()
  },
  async beforeRouteLeave(to, from, next) {
    if (!['alarmCenter', 'elevatorAlarmCenter'].includes(to.name)) {
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      requestHttp: __PATH.VUE_IEMC_ELEVATOR_API,
      requestUrl: '',
      monitorData: {},
      type: '',
      title: '报警中心',
      blockLoading: false,
      titleOne: '',
      titleTwo: '',
      option: [],
      taskDetail: [],
      electrical: false,
      header: {
        title: '报警中心',
        titleOne: '报警基本信息',
        titleTwo: '报警处置',
        detailNode: [],
        detailList: [
          { label: '处置人员：', value: 'personSet', width: '200' },
          { label: '处置人员电话：', value: 'personPhone', width: '200' }
        ]
      },
      formInline: {
        disposeResult: '',
        disposeText: '',
        disposeTime: ''
      },
      rules: {
        disposeResult: {
          required: true,
          message: '请选择处置结果',
          trigger: 'change'
        },
        disposeTime: {
          required: true,
          message: '请选择处置时间',
          trigger: 'change'
        }
      },
      chuzhiList: [
        { id: 1, name: '已确认' },
        { id: 2, name: '误报' }
      ],
      fileEcho: [],
      urlList: [],
      videoUrl: '',
      videoName: '',
      videoTabsList: [],
      cameraTalkDialogShow: false,
      staffName: '',
      personPhone: '',
      attachmentUrl: '', // 旧的附件
      deviceInfoList: [
        { label: '资产名称：', value: 'assetName' },
        { label: '资产编码：', value: 'assetCode' },
        { label: '品牌：', value: 'assetBrand' },
        { label: '型号：', value: 'assetModel' },
        { label: '启用日期：', value: 'startDate' },
        { label: '使用期限：', value: 'serviceLife' }
      ],
      deviceInfoData: {},
      dateType: '', // 日期类型
      policeStateCountList: [
        { label: '历史报警总数', value: '' },
        { label: '已处置报警', value: '' },
        { label: '未处置报警', value: '' }
      ]
    }
  },
  computed: {},
  created() {
    const routerNameList = ['elevatorAlarmCenter', 'alarmCenter']
    if (!this.$store.state.keepAlive.list.some((e) => routerNameList.includes(e))) {
      this.initEvent()
    }
  },
  activated() {
    this.initEvent()
  },
  methods: {
    initEvent() {
      this.setDefaultDataEvent()
      this.type = this.$route.query.type
      if (this.$route.query.projectCode) {
        this.monitorData = monitorTypeList.find((item) => item.projectCode == this.$route.query.projectCode)
        this.requestUrl = this.monitorData.requestUrl
      }
      this.getDetails()
    },
    setDefaultDataEvent() {
      const data = this.$options.data()
      delete data.rules
      Object.assign(this.$data, data)
      this.$refs.formInline.resetFields()
      this.$set(this.$data.header, 'detailNode', [
        { label: (this.$route.query.entityConvertName || '') + '名称：', value: 'surveyName', remake: false },
        { label: '报警参数名称：', value: 'parameterName', remake: false },
        { label: '报警描述：', value: 'policeReason', remake: false },
        { label: '报警数值：', value: 'policeNumber', remake: false },
        { label: '报警上限：', value: 'upperThreshold', remake: false, notHasProject: '电梯监测' },
        { label: '报警下限：', value: 'downThreshold', remake: false, notHasProject: '电梯监测' },
        { label: '报警时间：', value: 'policeTime', remake: false },
        { label: '报警处置用时：', value: 'disposeUseTime', remake: false, notHasDeal: true },
        { label: '地理位置：', value: 'gridName', remake: true }
      ])
      this.staffName = this.$store.state.user.userInfo.user.staffName
      this.personPhone = this.$store.state.user.userInfo.user.phone || ''
    },
    getTaskValue(value) {
      if (!value && String(value) !== '0') {
        return '无'
      } else {
        return value
      }
    },
    getLegendLength() {
      let legendLength = 0
      let innerHeight = window.innerHeight
      if (innerHeight < 768) {
        legendLength = 2
      }
      if (innerHeight >= 768 && 900 > innerHeight) {
        legendLength = 3
      }
      if (innerHeight >= 900 && 1080 > innerHeight) {
        legendLength = 5
      }
      if (innerHeight >= 1080) {
        legendLength = 5
      }
      return legendLength
    },
    dateTypeSwitch(val) {
      this.dateType = val
      let newDate = {
        日: { startTime: moment().format('YYYY-MM-DD') + ' 00:00:00', endTime: moment().format('YYYY-MM-DD') + ' 23:59:59' },
        周: { startTime: moment().startOf('week').format('YYYY-MM-DD') + ' 00:00:00', endTime: moment().endOf('week').format('YYYY-MM-DD') + ' 23:59:59' },
        月: { startTime: moment().startOf('month').format('YYYY-MM-DD') + ' 00:00:00', endTime: moment().endOf('month').format('YYYY-MM-DD') + ' 23:59:59' },
        全部: { startTime: '', endTime: '' }
      }
      this.getPoliceStateCount(newDate[val])
      this.getWarnTypePieData(newDate[val])
    },
    // 获取报警类型统计数据
    getWarnTypePieData(param) {
      let params = {
        ...param,
        projectCode: this.monitorData.projectCode,
        surveyCode: this.taskDetail.surveyCode
      }
      this.$api.getReasonStatisticPie(params, this.requestHttp).then((res) => {
        if (res.code == 200) {
          this.$nextTick(() => {
            this.setWarnTypePieEcharts(res.data)
          })
        }
      })
    },
    // 报警类型统计Echarts
    setWarnTypePieEcharts(chartdata) {
      const getchart = echarts.init(document.getElementById('alarmTypeChaet'))
      const nameList = Array.from(chartdata, (item) => item.name)
      let sum = 0
      let warnData = []
      chartdata.forEach((item) => {
        sum += Number(item.value)
      })
      const colorIn = ['rgba(246, 180, 58, 1)', 'rgba(183, 187, 202, 1)', 'rgba(81, 136, 252, 1)', 'rgba(0, 221, 197, 1)', 'rgba(255, 86, 84, 1)']
      const colorOut = ['rgba(246, 180, 58, .4)', 'rgba(183, 187, 202, .4)', 'rgba(81, 136, 252, .4)', 'rgba(0, 221, 197, .4)', 'rgba(255, 86, 84, .4)']
      chartdata.forEach((item, index) => {
        const randomRgbColor = this.$tools.randomRgbColor('array')
        if (!colorIn[index] || !colorOut[index]) {
          colorIn.push(randomRgbColor[1])
          colorOut.push(randomRgbColor[0])
        }
        warnData.push(item)
        // warnData.push(item, {
        //   value: sum / 100,
        //   labelLine: {
        //     show: false,
        //     lineStyle: {
        //       color: 'transparent'
        //     }
        //   },
        //   itemStyle: {
        //     color: 'transparent'
        //   }
        // })
      })
      // colorIn数组每个元素后插入空字符
      colorIn.forEach((item, index) => {
        colorIn.splice(2 * index + 1, 0, '')
        colorOut.splice(2 * index + 1, 0, '')
      })
      const that = this
      let option
      if (chartdata.length) {
        option = {
          legend: {
            type: 'scroll',
            orient: 'vertical',
            top: 'center',
            right: '0%',
            data: nameList,
            itemWidth: 8,
            itemHeight: 8,
            itemGap: 16,
            textStyle: {
              //  fontSize: 18,//字体大小
              color: '#353535' //  字体颜色
            },
            tooltip: {
              show: true,
              confine: true // 限制tootip在容器内
            },
            formatter: function (name) {
              var oa = option.series[0].data
              var num = oa.reduce((sum, e) => sum + e.value, 0)
              for (var i = 0; i < option.series[0].data.length; i++) {
                if (name === oa[i].name) {
                  return (
                    ' ' +
                    (name.length > that.getLegendLength() ? name.substr(0, that.getLegendLength()) + '...' : name) +
                    ' (' +
                    oa[i].value +
                    ')   ' +
                    ((oa[i].value / num) * 100).toFixed(2) +
                    '%'
                  )
                }
              }
            }
          },
          tooltip: {
            trigger: 'item',
            formatter: '{b} : {c} ({d}%)',
            confine: true // 限制tootip在容器内
          },
          series: [
            {
              type: 'pie',
              radius: ['30%', '50%'],
              center: ['30%', '50%'],
              hoverAnimation: false,
              startAngle: 180,
              selectedMode: 'single',
              label: {
                show: false
              },
              itemStyle: {
                normal: {
                  borderWidth: 3,
                  borderColor: '#fff',
                  color: function (params) {
                    return colorOut[params.dataIndex]
                  }
                }
              },
              data: warnData,
              z: 666
            },
            {
              type: 'pie',
              radius: ['50%', '55%'],
              center: ['30%', '50%'],
              hoverAnimation: false,
              startAngle: 180,
              selectedMode: 'single',
              label: {
                show: false
              },
              itemStyle: {
                normal: {
                  borderWidth: 3,
                  borderColor: '#fff',
                  color: function (params) {
                    return colorIn[params.dataIndex]
                  }
                }
              },
              data: warnData,
              z: 1
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 获取设备报警统计
    getPoliceStateCount(param) {
      let params = {
        ...param,
        surveyCode: this.taskDetail.surveyCode,
        projectCode: this.monitorData.projectCode
      }
      this.$api.GetPoliceStateCount(params).then((res) => {
        if (res.code == '200') {
          this.policeStateCountList[0].value = res.data.total
          this.policeStateCountList[1].value = res.data.disposeCount
          this.policeStateCountList[2].value = res.data.unDisposeCount
        }
      })
    },
    // 获取资产详情
    getDeviceBasicInfo() {
      this.$api.getAssetDetailsByAssetIds({ assetsId: this.taskDetail.assetId }).then((res) => {
        if (res.code == '200') {
          this.deviceInfoData = res.data
        }
      })
    },
    /**
     * 获取数据详情
     */
    getDetails() {
      this.blockLoading = true
      let detailNode = []
      if (this.monitorData.projectName === '电梯监测') {
        detailNode = this.header.detailNode.filter((item) => item.notHasProject !== this.monitorData.projectName)
      } else {
        detailNode = this.header.detailNode
      }
      if (this.$route.query.type != 'check') {
        detailNode = detailNode.filter((item) => item.notHasDeal !== true)
      }
      this.option = detailNode
      //  接数据
      this.$api
        .getPoliceView(
          {
            id: this.$route.query.id
          },
          this.requestUrl
        )
        .then((res) => {
          this.blockLoading = false
          this.taskDetail = res.data
          this.getVideoList()
          // this.taskDetail.assetId = '1725123552660684806'
          if (this.taskDetail.assetId) {
            this.getDeviceBasicInfo()
            this.dateTypeSwitch('日')
          }
          this.fileEcho = this.urlList = res.data.attachmentUrl ? JSON.parse(res.data.attachmentUrl) : []
          if (this.$route.query.type == 'add') {
            this.taskDetail.personSet = this.staffName
            this.taskDetail.personPhone = this.personPhone
            if (res.data.disposeResult != 0) {
              this.formInline.disposeResult = res.data.disposeResult
              this.formInline.disposeText = res.data.disposeText
              this.attachmentUrl = res.data.attachmentUrl
            }
          }
        })
    },
    /**
     * 提交
     */
    complete() {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          this.blockLoading = true
          this.$api
            .disposePolice(
              {
                id: this.$route.query.id,
                ...this.formInline,
                disposePersonName: this.staffName,
                disposePersonPhone: this.personPhone,
                attachmentUrl: JSON.stringify(this.fileEcho)
              },
              {},
              this.requestUrl
            )
            .then((res) => {
              this.blockLoading = false
              if (res.code == '200') {
                this.$message.success(res.message)
                this.$router.go(-1)
              } else {
                this.$message.error(res.message)
              }
            })
        }
      })
    },
    // 文档下载
    downLoad(row) {
      // 获取后缀
      let index = row.name.lastIndexOf('.')
      let ext = row.name.substr(index + 1)
      if (ext == 'png' || ext == 'jpg' || ext == 'JPG' || ext == 'JPEG' || ext == 'gif' || ext == 'pdf' || ext == 'PDF') {
        window.open(this.$tools.imgUrlTranslation(row.url), '_top')
      } else {
        location.href = this.$tools.imgUrlTranslation(row.url)
      }
    },
    //  ------------------------------------------上传文件相关-----------------------------------------
    httpRequest(file) {
      const params = new FormData()
      params.append('file', file.file)
      this.$api.uploadIcon(params).then((res) => {
        if (res.code == 200) {
          this.fileEcho.push({ name: file.file.name, url: res.data })
          this.$message({
            message: '上传成功',
            type: 'success'
          })
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },
    handleExceed() {
      this.$message.error('最多上传九份文件')
    },
    beforeAvatarUpload(file) {
      const isLt5M = file.size / 1024 / 1024 < 5
      if (!isLt5M) {
        this.$message.error('上传文件大小不能超过 5MB!')
        return false
      }
      if (file.name.indexOf(',') != -1) {
        this.$message.error('非法的文件名')
        return false
      }
    },
    handleRemove(file, fileList) {
      this.fileEcho = this.fileEcho.filter((el) => {
        return el.name !== file.name
      })
    },
    // 获取摄像机列表
    // 摄像机列表
    getVideoList() {
      this.$api
        .getVideoList(
          {
            surveyCode: this.taskDetail.surveyCode
          },
          this.requestUrl
        )
        .then((res) => {
          if (res.code === '200') {
            this.videoTabsList = res.data
          }
        })
    },
    // 摄像机点击事件
    videoChange(item) {
      this.cameraTalkDialogShow = true
      const params = {
        cameraId: item.vidiconId
      }
      this.$api.getHlvAddress(params, this.requestUrl).then((res) => {
        if (res.code === '200') {
          this.videoName = item.vidiconName
          this.videoUrl = res.data
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.deviceInfo {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 25%;
  border-left: 1px solid #e6e6e6;
  padding: 10px;
  display: flex;
  flex-direction: column;
  .info-item {
    height: 30px;
    line-height: 30px;
    margin: 0;
    .label {
      display: inline-block;
      width: 100px;
      text-align: right;
      font-weight: 400;
      color: rgb(96 98 102 / 100%);
    }
    .value {
      display: inline-block;
      font-weight: 400;
      color: rgb(144 147 153 / 100%);
    }
  }
  .maintain-list {
    padding: 0px !important;
  }
  .alarm-count {
    display: flex;
    justify-content: space-between;
    padding: 0px 10px;
    p {
      margin: 0;
    }
    .count-item {
      padding: 20px 0px;
      text-align: center;
      .count-value {
        font-size: 16px;
      }
      .count-label {
        font-size: 14px;
      }
    }
  }
  .alarmTypeChaet {
    height: 80%;
    width: 100%;
  }
}
.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
  padding: 10px 26% 10px 10px;
  overflow-y: auto;
  position: relative;
}
.maintain-content-block {
  margin-bottom: 20px;
  .maintain-title {
    height: 40px;
    line-height: 40px;
    position: relative;
    .title-tag {
      display: inline-block;
      width: 4px;
      height: 16px;
      position: relative;
      top: 3px;
      background: #5188fc;
    }
    .title-text {
      padding-left: 5px;
      font-size: 14px;
      font-weight: 600;
      color: rgb(96 98 102 / 100%);
    }
    ::v-deep .el-radio-group {
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      .el-radio-button__inner {
        padding: 5px 12px;
      }
    }
  }
  .maintain-list {
    padding: 0 0 0 26px;
    .maintain-list-item {
      display: inline-block;
      width: 33%;
      overflow: hidden;
      text-overflow: ellipsis;
      .list-item {
        display: block;
        height: 40px;
        line-height: 40px;
        .label {
          display: inline-block;
          width: 130px;
          text-align: right;
          font-weight: 400;
          color: rgb(96 98 102 / 100%);
        }
        .value {
          font-weight: 400;
          color: rgb(144 147 153 / 100%);
        }
      }
    }
    .maintain-list-block {
      // height: 40px;
      line-height: 20px;
      .list-item {
        line-height: 40px;
      }
      .label {
        display: inline-block;
        width: 130px;
        vertical-align: top;
        text-align: right;
        font-weight: 400;
        color: rgb(96 98 102 / 100%);
      }
      .value {
        display: inline-block;
        // width: 880px;
        font-weight: 400;
        color: rgb(144 147 153 / 100%);
      }
    }
  }
}
.sino-content {
  height: calc(100% - 35px) !important;
}
::v-deep .el-dialog__body {
  background: #f6f5fa;
  padding: 15px;
  .camera-talk-content {
    background: #fff;
    padding: 16px;
  }
}
</style>
<style lang="scss">
.policeDetails {
  .mterial_file > .el-upload-list {
    margin-top: 40px !important;
  }
  .el-upload__text {
    top: 91px !important;
  }
}
.video_height {
  height: 200px;
  width: 600px;
}
</style>
