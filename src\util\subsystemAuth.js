/*
 * @Author: hedd
 * @Date: 2023-05-06 14:19:01
 * @LastEditTime: 2023-05-06 14:52:29
 * @FilePath: \ihcrs_pc\src\util\subsystemAuth.js
 * @Description:
 */
// import api from '@/api'
import store from '@/store/index'
import { Message } from 'element-ui'
import NProgress from 'nprogress'

export default {
  // 双预防
  ipsm (to, from, next) {
    if (Object.keys(store.state.user.ipsmUserInfo).length) {
      next()
    } else {
      Message.error('暂无权限，请联系管理员')
      store.state.settings.enableProgress && NProgress.done()
    }
    // api.getIpsmLoginInfo({
    //   userId: store.state.user.userInfo.user.staffId,
    //   platform: 1
    // }).then(res => {
    //   if (res.code == '200') {
    //     sessionStorage.setItem('LOGINDATA', JSON.stringify(res.data))
    //     next()
    //   } else {

    //   }
    // })
  }
}
