<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="whloe">
        <el-form ref="formInline" :model="formInline" :inline="true" :rules="rules" label-position="right" label-width="150px" :disabled="$route.query.type == 'View'">
          <!-- 基础信息 -->
          <div class="title">
            <svg-icon name="right-arrow" />
            <span>基础信息</span>
          </div>
          <div style="padding-top: 25px">
            <el-form-item label="人员姓名" prop="staffName">
              <el-input v-model="formInline.staffName" class="sino_form_input" placeholder="请输入人员姓名" show-word-limit> </el-input>
            </el-form-item>
            <el-form-item label="身份证号" prop="idCard">
              <el-input v-model="formInline.idCard" class="sino_form_input" placeholder="请输入身份证号" maxlength="18" show-word-limit> </el-input>
            </el-form-item>
            <el-form-item label="出生时间" prop="birthDate">
              <el-date-picker v-model="formInline.birthDate" class="sino_form_input" type="date" value-format="yyyy-MM-dd" placeholder="选择日期" clearable> </el-date-picker>
            </el-form-item>
            <el-form-item label="籍贯" prop="nativePlace">
              <el-input v-model="formInline.nativePlace" class="sino_form_input" placeholder="请输入籍贯" show-word-limit> </el-input>
            </el-form-item>
            <el-form-item label="民族" prop="nation">
              <el-select v-model="formInline.nation" class="sino_form_input" placeholder="请选择民族" clearable>
                <el-option v-for="item in nationList" :key="item.code" :label="item.nation" :value="item.code"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="性别" prop="sex">
              <el-select v-model="formInline.sex" class="sino_form_input" placeholder="请选择性别" clearable>
                <el-option v-for="item in sexList" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="手机号码" prop="mobile">
              <el-input v-model="formInline.mobile" class="sino_form_input" placeholder="请输入手机号码" show-word-limit maxlength="11"> </el-input>
            </el-form-item>
            <el-form-item label="办公电话" prop="phone">
              <el-input v-model="formInline.phone" class="sino_form_input" placeholder="请输入办公电话" show-word-limit> </el-input>
            </el-form-item>
            <el-form-item label="职工工号" prop="staffNumber">
              <el-input v-model="formInline.staffNumber" class="sino_form_input" placeholder="请输入职工工号" show-word-limit> </el-input>
            </el-form-item>
            <br />
            <el-form-item label="员工照片" prop="photoUrl">
              <sino-upload :fileList="photoList" modelName="hospitalBaseInfo" @input="uploadChangePhoto"></sino-upload>
            </el-form-item>
          </div>

          <!-- 工作信息 -->
          <div class="title">
            <svg-icon name="right-arrow" />
            <span>工作信息</span>
          </div>
          <div style="padding-top: 25px">
            <el-form-item label="在职状态" prop="stationStatus">
              <el-select v-model="formInline.stationStatus" class="sino_form_input" placeholder="请选择在职状态">
                <el-option v-for="item in stationStatusList" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="入职时间" prop="entryData">
              <el-date-picker v-model="formInline.entryData" class="sino_form_input" type="date" value-format="yyyy-MM-dd" clearable placeholder="选择日期"> </el-date-picker>
            </el-form-item>
            <el-form-item label="离职时间" prop="outData">
              <el-date-picker v-model="formInline.outData" class="sino_form_input" type="date" value-format="yyyy-MM-dd" clearable placeholder="选择日期"> </el-date-picker>
            </el-form-item>
            <br />
            <el-form-item label="所属单位" prop="pmId">
              <el-select v-model="formInline.pmId" class="sino_form_input" placeholder="请选择所属单位" clearable @change="selectedHandle">
                <el-option v-for="item in unitList" :key="item.umId" :label="item.unitComName" :value="item.umId"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="所属部门" prop="officeId">
              <el-select v-model="formInline.officeId" class="sino_form_input" placeholder="请选择所属部门" multiple clearable filterable>
                <el-option v-for="item in departList" :key="item.id" :label="item.deptName" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <br />
            <el-form-item label="岗位" prop="postId">
              <el-select v-model="formInline.postId" class="sino_form_input" placeholder="请选择岗位" multiple clearable>
                <el-option v-for="item in postList" :key="item.id" :label="item.postName" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="职务" prop="jobId">
              <el-select v-model="formInline.jobId" class="sino_form_input" placeholder="请选择职务" multiple clearable>
                <el-option v-for="item in jobList" :key="item.id" :label="item.jobName" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </div>

          <!-- 证书管理 -->
          <div class="title">
            <svg-icon name="right-arrow" />
            <span>证书管理 </span>
          </div>
          <el-form-item label=" " prop="pictureList" style="margin-top: 25px">
            <sino-upload :fileList="formInline.pictureList" :limit="9" :flieSizeType="2" modelName="staff" @input="uploadChange"></sino-upload>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">取消</el-button>
    </div>
  </PageContainer>
</template>

<script>
import sinoUpload from '../common/sinoUpload.vue'
import { validateMobile, validatePhone, validateIdCard } from '@/assets/common/validate'
const validateMobileNum = (rule, value, callback) => {
  if (!validateMobile(value)) {
    callback(new Error('请输入正确的手机号'))
  } else {
    callback()
  }
}
const validateIdCardNum = (rule, value, callback) => {
  if (!validateIdCard(value)) {
    callback(new Error('请输入正确的身份证号'))
  } else {
    callback()
  }
}
const validatePhoneNum = (rule, value, callback) => {
  if (validatePhone(value) || validateMobile(value)) {
    callback()
  } else {
    callback(new Error('请输入正确的电话号码'))
  }
}
export default {
  components: {
    sinoUpload
  },
  async beforeRouteLeave(to, from, next) {
    if (!['personnelManage'].includes(to.name)) {
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      // title: this.$route.query.type == 'Add' ? '新增人员' : this.$route.query.type == 'View' ? '查看人员' : '编辑人员',
      isDisable: false,
      formInline: {
        // ------------------------基础信息
        staffName: '',
        idCard: '',
        birthDate: '',
        nativePlace: '',
        nation: null,
        sex: '',
        mobile: '',
        phone: '',
        staffNumber: '',
        photoId: '',
        photoUrl: '',
        // ------------------------工作信息
        stationStatus: 0,
        entryData: '',
        outData: '',
        pmId: '',
        officeId: '',
        postId: '',
        jobId: '',
        // ------------------------证书管理
        pictureList: []
      },
      rules: {
        staffName: {
          required: true,
          message: '请输入人员姓名',
          trigger: 'change'
        },
        idCard: [
          { validator: validateIdCardNum, trigger: 'blur' },
          { required: false, message: '请输入正确的身份证号', trigger: 'blur' }
        ],
        sex: {
          required: true,
          message: '请选择性别',
          trigger: 'change'
        },
        mobile: [
          { validator: validateMobileNum, trigger: 'blur' },
          { required: true, message: '请输入手机号码', trigger: 'blur' }
        ],
        phone: [
          { validator: validatePhoneNum, trigger: 'blur' },
          { required: false, message: '请输入手机号码', trigger: 'blur' }
        ],
        pmId: {
          required: true,
          message: '请选择所属单位',
          trigger: 'change'
        },
        officeId: {
          required: true,
          message: '请选择所属部门',
          trigger: 'change'
        }
      },
      nationList: [],
      sexList: [
        { id: 1, name: '男' },
        { id: 2, name: '女' }
      ],
      stationStatusList: [
        { id: 0, name: '在职' },
        { id: 1, name: '离职' }
      ],
      unitList: [],
      departList: [],
      postList: [],
      jobList: [],
      photoList: []
    }
  },
  activated() {
    this.init()
  },
  created() {
    if (!this.$store.state.keepAlive.list.includes('personnelManage')) {
      this.init()
    }
  },
  methods: {
    init() {
      Object.assign(this.$data, this.$options.data())
      this.$nextTick(() => {
        this.$refs.formInline.resetFields()
      })
      this.getNationListFn()
      this.getUnitListFn()
      // this.getDeptListFn();
      this.postListFn()
      this.jobListByPageFn()
      if (this.$route.query.id) {
        this.getStaffDetailFn(this.$route.query.id)
      }
    },
    // ----------------------------------------------------------SelectData
    // 民族列表
    getNationListFn() {
      this.$api.getNationList({}).then((res) => {
        if (res.code == 200) {
          this.nationList = res.data
        }
      })
    },
    // 单位列表
    getUnitListFn() {
      this.$api.getUnitList({}).then((res) => {
        if (res.code == 200) {
          this.unitList = res.data
        }
      })
    },
    selectedHandle(val) {
      this.getDeptListFn(val)
    },
    // 部门列表
    getDeptListFn(unitId) {
      this.$api
        .getDeptList({
          unitId: unitId
        })
        .then((res) => {
          if (res.code == 200) {
            this.departList = res.data
          }
        })
    },
    //  获取岗位列表
    postListFn() {
      this.$api.selectByList({}).then((res) => {
        if (res.code == 200) {
          this.postList = res.data
        }
      })
    },
    //  获取岗位列表
    jobListByPageFn() {
      this.$api
        .jobManager({
          current: '1',
          size: '15'
        })
        .then((res) => {
          // this.treeLoading = false;
          if (res.code == 200) {
            this.jobList = res.data.records
          }
        })
    },
    // ----------------------------------------------------------FormFn
    /**
     * @description: 获取人员信息
     * @param {*}
     * @return {*}
     * @note:
     * @Author: Allen
     */
    getStaffDetailFn(id) {
      this.$api.getStaffDetail({ id: id }).then((res) => {
        if (res.code == 200) {
          this.formInline = res.data
          this.formInline.photoUrl ? (this.photoList = [{ name: '', url: this.$tools.imgUrlTranslation(this.formInline.photoUrl) }]) : (this.photoList = [])
          this.formInline.birthDate ? (this.formInline.birthDate = this.transDate(res.data.birthDate)) : (this.formInline.birthDate = '')
          this.formInline.entryData ? (this.formInline.entryData = this.transDate(res.data.entryData)) : (this.formInline.entryData = '')
          this.formInline.outData ? (this.formInline.outData = this.transDate(res.data.outData)) : (this.formInline.outData = '')
          this.formInline.jobId ? (this.formInline.jobId = res.data.jobId.split(',').map(Number)) : ''
          this.formInline.officeId ? (this.formInline.officeId = res.data.officeId.split(',')) : ''
          this.formInline.postId ? (this.formInline.postId = res.data.postId.split(',').map(Number)) : ''
          this.getDeptListFn(this.formInline.pmId)
          if (this.formInline.pictureList.some((val) => val.picUrl == '')) {
            this.formInline.pictureList = []
          } else {
            this.formInline.pictureList.map((list) => {
              list.url = this.$tools.imgUrlTranslatio(list.picUrl)
              list.name = ''
            })
          }
          if (this.$route.query.type === 'View') {
            this.$nextTick(() => {
              this.$refs.formInline.clearValidate()
            })
          }
        }
      })
    },
    /**
     * @description: 图片上传
     * @param {val}
     * @return {*}
     * @note:
     * @Author: Allen
     */
    transDate(data) {
      let date = new Date(data)
      let y = date.getFullYear()
      let m = date.getMonth() + 1
      let d = date.getDate()
      let H = date.getHours()
      let mm = date.getMinutes()
      let s = date.getSeconds()
      m = m < 10 ? '0' + m : m
      d = d < 10 ? '0' + d : d
      H = H < 10 ? '0' + H : H
      return y + '-' + m + '-' + d
    },
    uploadChangePhoto(val, state) {
      if (state) {
        this.formInline.photoId = ''
        this.formInline.photoUrl = ''
      } else {
        this.formInline.photoId = val.uid
        this.formInline.photoUrl = val.fileHost + val.fileUrl
      }
    },
    uploadChange(val, state) {
      let photoObj = {
        picId: val.picId ? val.picId : val.uid,
        picUrl: val.fileHost + val.fileUrl,
        url: val.fileHost + val.fileUrl
      }
      if (state) {
        this.formInline.pictureList.some((item, i) => {
          if (item.picId == photoObj.picId) {
            this.formInline.pictureList.splice(i, 1)
            return true
          }
        })
      } else {
        this.formInline.pictureList.push(photoObj)
      }
    },
    cancel() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
  padding: 10px;
  overflow-y: auto;
}

.whole {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 15px;
}

.sino_content {
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 300px;
  }

  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 150px;
    height: 150px;
    line-height: 150px;
    text-align: center;
  }

  .avatar {
    width: 150px;
    height: 150px;
    display: block;
  }
}

.title span {
  font-size: 15px;
}
</style>
