<template>
  <PageContainer>
    <div slot="content" ref="safetyDataCockpit" class="safetyDataCockpit">
      <div class="page-content-title">
        <div class="ability-icon">
          <i class="screen-icon" :class="isFullScreen ? 'exit-full-screen' : 'full-screen'" @click="fullScreen('safetyDataCockpit')"></i>
        </div>
        <div class="sion-login-header">
          <img src="@/assets/images/safetyDataCockpit/page-head-title.png" alt="" />
        </div>
        <div class="header-right-time">
          <span class="class-week">{{ timeData.weekDate }}</span>
          <span class="class-date">{{ timeData.dayDate }}</span>
          <span class="class-time">{{ timeData.timeDate }}</span>
        </div>
      </div>
      <div class="page-content-aside">
        <div class="content-aside-left">
          <SafetyCard title="设备管理" style="height: 74%">
            <DeviceManagement slot="content" />
          </SafetyCard>
          <SafetyCard title="隐患管理" :bodyBg="boxAsideHidden" style="height: 25%">
            <HiddenDangerManagement slot="content" />
          </SafetyCard>
        </div>
        <div class="content-aside-middel">
          <SafetyCard title="重点安全管理" :showTitle="false" style="height: 60%">
            <KeySafetyManagement slot="content" />
          </SafetyCard>
          <SafetyCard title="安全组织" :bodyBg="boxAsideKeysafety" style="height: 39%">
            <SecurityOrganization slot="content" />
          </SafetyCard>
        </div>
        <div class="content-aside-right">
          <SafetyCard title="保安工作" :bodyBg="boxAsideKeysafety" style="height: 60%">
            <SecurityWork slot="content" />
          </SafetyCard>
          <SafetyCard title="演练培训" :bodyBg="boxAsideKeysafety" style="height: 39%">
            <TrainingDrill slot="content" />
          </SafetyCard>
        </div>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import moment from 'moment'
moment.locale('zh-cn', {
  weekdays: '星期日_星期一_星期二_星期三_星期四_星期五_星期六'.split('_')
})
import boxAsideDevice from '@/assets/images/safetyDataCockpit/box-aside-device.png'
import boxAsideHidden from '@/assets/images/safetyDataCockpit/box-aside-hidden.png'
import boxAsideKeysafety from '@/assets/images/safetyDataCockpit/box-aside-keysafety.png'
export default {
  name: 'safetyDataCockpit',
  components: {
    SafetyCard: () => import('./components/SatetyCard.vue'),
    DeviceManagement: () => import('./deviceManagement/index.vue'),
    HiddenDangerManagement: () => import('./hiddenDangerManagement/index.vue'),
    KeySafetyManagement: () => import('./keySafetyManagement/index.vue'),
    SecurityOrganization: () => import('./securityOrganization/index.vue'),
    SecurityWork: () => import('./securityWork/index.vue'),
    TrainingDrill: () => import('./trainingDrill/index.vue')
  },
  data() {
    return {
      boxAsideDevice,
      boxAsideHidden,
      boxAsideKeysafety,
      isFullScreen: false, // 是否全屏
      timeData: {
        weekDate: moment().format('dddd'),
        dayDate: moment().format('YYYY年MM月DD日'),
        timeDate: moment().format('HH:mm:ss')
      }
    }
  },
  mounted() {
    this.timer = setInterval(() => {
      this.timeData = {
        weekDate: moment().format('dddd'),
        dayDate: moment().format('YYYY年MM月DD日'),
        timeDate: moment().format('HH:mm:ss')
      }
    }, 1000)
    this.$once('hook:beforeDestroy', () => {
      clearInterval(this.timer)
    })
    window.onresize = () => {
      // 可视区域的高度
      const clientHeight = document.documentElement.clientHeight || document.body.clientHeight
      // screen是window的属性方法，window.screen可省略window，指的是窗口
      this.isFullScreen = screen.height == clientHeight
    }
  },
  methods: {
    // 全屏事件
    fullScreen(dom) {
      this.isFullScreen = !this.isFullScreen
      const full = this.$refs[dom]
      if (this.isFullScreen) {
        if (full.RequestFullScreen) {
          full.RequestFullScreen()
          // 兼容Firefox
        } else if (full.mozRequestFullScreen) {
          full.mozRequestFullScreen()
          // 兼容Chrome, Safari and Opera等
        } else if (full.webkitRequestFullScreen) {
          full.webkitRequestFullScreen()
          // 兼容IE/Edge
        } else if (full.msRequestFullscreen) {
          full.msRequestFullscreen()
        }
      } else {
        if (document.exitFullscreen) {
          document.exitFullscreen()
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen()
        } else if (document.webkitCancelFullScreen) {
          document.webkitCancelFullScreen()
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen()
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.safetyDataCockpit {
  width: 100%;
  height: 100%;
  background-color: #020916;
  overflow: hidden;
  .page-content-title {
    height: 8.3%;
    width: 100%;
    display: flex;
    align-items: center;
    position: relative;
    .ability-icon {
      position: absolute;
      top: calc(50% - 8px);
      right: 15px;
      .screen-icon {
        display: inline-block;
        width: 15px;
        height: 15px;
        margin-left: 10px;
        cursor: pointer;
        color: #fff;
      }
      .full-screen {
        background: url('~@/assets/images/elevator/full-screen.png') no-repeat;
        background-size: 100% 100%;
      }
      .exit-full-screen {
        background: url('~@/assets/images/elevator/exit-full-screen.png') no-repeat;
        background-size: 100% 100%;
      }
    }
    .sion-login-header {
      width: 68%;
      margin: 0 auto;
      background: url('~@/assets/images/safetyDataCockpit/page-head.png') no-repeat;
      background-size: 100% 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      position: relative;
      img {
        position: absolute;
        top: 44%;
        transform: translateY(-50%);
        height: 50%;
      }
    }
    .header-right-time {
      position: absolute;
      right: 50px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .class-week,
      .class-date {
        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
        font-size: 14px;
        color: #ffffff;
        margin-right: 10px;
      }
      .class-time {
        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
        font-weight: 500;
        font-size: 28px;
        color: #94b7ff;
      }
    }
  }
  .page-content-aside {
    height: 91.7%;
    padding: 1% 2%;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    .content-aside-left,
    .content-aside-middel,
    .content-aside-right {
      width: calc(32%);
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }
    .content-aside-left {
      ::v-deep .safety-card-body {
        padding: 0;
      }
    }
    .content-aside-middel {
    }
    .content-aside-right {
    }
  }
}
</style>
