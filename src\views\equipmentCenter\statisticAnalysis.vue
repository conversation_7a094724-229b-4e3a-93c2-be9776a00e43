<template>
  <div v-loading="pageLoading" class="contentInner">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="巡检统计" name="1"></el-tab-pane>
      <el-tab-pane label="保养统计" name="2"></el-tab-pane>
    </el-tabs>
    <div class="typeWrap">
      <div class="typeItem" :class="activeType == '1' ? 'activeType' : ''" @click="changeType('1')">按系统</div>
      <div class="typeItem" :class="activeType == '2' ? 'activeType' : ''" @click="changeType('2')">按设备</div>
      <div class="typeItem" :class="activeType == '3' ? 'activeType' : ''" @click="changeType('3')">按部门</div>
      <div class="typeItem" :class="activeType == '4' ? 'activeType' : ''" @click="changeType('4')">按计划</div>
    </div>
    <div class="filterWrap">
      <el-input v-if="activeType == '2'" v-model="filterData.eqName" placeholder="设备名称" style="width: 200px; margin-right: 10px"></el-input>
      <el-select v-if="activeType == '1' || activeType == '2'" v-model="filterData.sysClassify" filterable clearable placeholder="请选择系统类别" style="margin-right: 10px">
        <el-option v-for="item in classifyOption" :key="item.id" :label="item.baseName" :value="item.id"> </el-option>
      </el-select>
      <el-select v-if="activeType == '3'" v-model="filterData.dept" filterable clearable placeholder="请选择部门" style="margin-right: 10px">
        <el-option v-for="item in deptList" :key="item.id" :label="item.deptName" :value="item.id"> </el-option>
      </el-select>
      <el-input v-if="activeType == '4'" v-model="filterData.planName" placeholder="计划名称" style="width: 200px; margin-right: 10px"></el-input>
      <el-select
        v-if="activeType == '4'"
        v-model="filterData.planTypeId"
        multiple
        collapse-tags
        filterable
        clearable
        placeholder="请选择模板"
        style="margin-right: 10px"
        @clear="removeTag"
      >
        <el-option v-for="item in planTypeTemplateArr" :key="item.planTypeId" :label="item.planTypeName" :value="item.planTypeId"> </el-option>
      </el-select>
      <el-date-picker v-model="filterData.dateInterval" type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" style="margin-right: 10px">
      </el-date-picker>
      <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db" @click="reset">重置</el-button>
      <el-button type="primary" style="font-size: 14px" @click="search">查询</el-button>
      <el-button :loading="exportLoading" type="primary" style="font-size: 14px" @click="exportExcel">导出</el-button>
    </div>
    <div v-if="activeType == '1' || activeType == '2'" class="tableChrat1">
      <div class="leftTable">
        <el-table :data="tableData" :height="activeType == '1' ? '100%' : 'calc(100% - 42px)'" style="width: 100%">
          <el-table-column label="序号" type="index" width="60">
            <template slot-scope="scope">
              <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="activeType == '2'" show-overflow-tooltip prop="taskPointName" label="设备名称"> </el-table-column>
          <el-table-column prop="professionalCategoryName" label="系统类型" width="120"> </el-table-column>
          <el-table-column v-if="activeType == '1'" prop="deviceNumber" :label="activeName == '1' ? '应巡设备数' : '应保养设备数'">
            <template slot-scope="scope">
              <el-link type="primary" :underline="false" @click="goList(scope.row, scope.row.deviceNumber, 1)">
                {{ scope.row.deviceNumber }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column prop="taskCount" label="任务总数">
            <template slot-scope="scope">
              <el-link type="primary" :underline="false" @click="goList(scope.row, scope.row.taskCount, 2)">
                {{ scope.row.taskCount }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column :prop="activeType == '1' ? 'accomplishCount' : 'hasCount'" :label="activeName == '1' ? '已巡检' : '已保养'">
            <template slot-scope="scope">
              <el-link type="primary" :underline="false" @click="goList(scope.row, activeType == '1' ? scope.row.accomplishCount : scope.row.hasCount, 3)">
                {{ activeType == '1' ? scope.row.accomplishCount : scope.row.hasCount }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column :prop="activeType == '1' ? 'unfinishedCount' : 'notCount'" :label="activeName == '1' ? '未巡检' : '未保养'">
            <template slot-scope="scope">
              <el-link type="primary" :underline="false" @click="goList(scope.row, activeType == '1' ? scope.row.unfinishedCount : scope.row.notCount, 4)">
                {{ activeType == '1' ? scope.row.unfinishedCount : scope.row.notCount }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column v-if="activeType == '2'" prop="anomalyCount" label="不合格数">
            <template slot-scope="scope">
              <el-link type="primary" :underline="false" @click="goList(scope.row, scope.row.anomalyCount, 5)">
                {{ scope.row.anomalyCount }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column v-if="activeType == '2'" prop="repairCount" label="报修数">
            <template slot-scope="scope">
              <el-link type="primary" :underline="false" @click="goList(scope.row, scope.row.repairCount, 0)">
                {{ scope.row.repairCount }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column prop="finishingRate" label="完成率"> </el-table-column>
        </el-table>
        <el-pagination
          v-if="activeType != '1'"
          style="padding-top: 10px"
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="paginationData.currentPage"
          :page-sizes="[15, 30, 50, 100]"
          :page-size="paginationData.pageSize"
          :total="paginationData.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
      <div class="rightChrat">
        <div ref="taskAnalysis" v-loading="chratsLoading1" class="chratItem"></div>
        <div ref="deviceAnalysis" v-loading="chratsLoading2" class="chratItem"></div>
      </div>
    </div>
    <div v-if="activeType == '3'" class="tableChrat2">
      <div class="topChrat">
        <div ref="deptAnalysis" v-loading="chratsLoading2" class="chratItem"></div>
        <div ref="staffAnalysis" v-loading="chratsLoading3" class="chratItem"></div>
      </div>
      <div v-loading="tableLoading" class="bottomTable">
        <el-table :data="tableData" height="calc(100% - 42px)" style="width: 100%">
          <el-table-column label="序号" type="index" width="60">
            <template slot-scope="scope">
              <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="distributionTeamName" label="部门名称"> </el-table-column>
          <el-table-column prop="taskCount" label="任务总数">
            <template slot-scope="scope">
              <el-link type="primary" :underline="false" @click="goList(scope.row, scope.row.taskCount, 2)">
                {{ scope.row.taskCount }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column prop="accomplishCount" :label="activeName == '1' ? '已巡检' : '已保养'">
            <template slot-scope="scope">
              <el-link type="primary" :underline="false" @click="goList(scope.row, scope.row.accomplishCount, 3)">
                {{ scope.row.accomplishCount }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column prop="unfinishedCount" :label="activeName == '1' ? '未巡检' : '未保养'">
            <template slot-scope="scope">
              <el-link type="primary" :underline="false" @click="goList(scope.row, scope.row.unfinishedCount, 4)">
                {{ scope.row.unfinishedCount }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column prop="anomalyCount" label="不合格数" width="180">
            <template slot-scope="scope">
              <el-link type="primary" :underline="false" @click="goList(scope.row, scope.row.anomalyCount, 5)">
                {{ scope.row.anomalyCount }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column prop="repairCount" label="报修数">
            <template slot-scope="scope">
              <el-link type="primary" :underline="false" @click="goList(scope.row, scope.row.repairCount, 0)">
                {{ scope.row.repairCount }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column prop="finishingRate" label="完成率"> </el-table-column>
        </el-table>
        <el-pagination
          style="padding-top: 10px"
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="paginationData.currentPage"
          :page-sizes="[15, 30, 50, 100]"
          :page-size="paginationData.pageSize"
          :total="paginationData.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
    <div v-if="activeType == '4'" class="tableChrat3">
      <el-table v-loading="tableLoading" :data="tableData" height="calc(100% - 42px)" style="width: 100%">
        <el-table-column label="序号" type="index" width="60">
          <template slot-scope="scope">
            <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="planName" show-overflow-tooltip label="计划名称"> </el-table-column>
        <el-table-column prop="planTypeName" label="计划类型" width="120"> </el-table-column>
        <el-table-column prop="totalTaskNum" label="任务总数">
          <template slot-scope="scope">
            <el-link type="primary" :underline="false" @click="goList(scope.row, scope.row.totalTaskNum, 2)">
              {{ scope.row.totalTaskNum }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column prop="completeTaskNum" label="完成任务数">
          <template slot-scope="scope">
            <el-link type="primary" :underline="false" @click="goList(scope.row, scope.row.completeTaskNum, 3)">
              {{ scope.row.completeTaskNum }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column prop="completeTaskRate" label="完成率"> </el-table-column>
        <el-table-column prop="totalTaskPointNum" label="点位总数"> </el-table-column>
        <el-table-column prop="completeTaskPointNum" label="完成点位数"> </el-table-column>
        <el-table-column prop="completeTaskPointRate" label="完成率"> </el-table-column>
      </el-table>
      <el-pagination
        v-if="activeType != '1'"
        style="padding-top: 10px"
        layout="total, sizes, prev, pager, next, jumper"
        :current-page="paginationData.currentPage"
        :page-sizes="[15, 30, 50, 100]"
        :page-size="paginationData.pageSize"
        :total="paginationData.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
  </div>
</template>
<script>
import moment from 'moment'
import * as echarts from 'echarts'
import axios from 'axios'
import { remove } from 'nprogress'
export default {
  data() {
    return {
      pageLoading: false,
      tableLoading: false,
      chratsLoading1: false,
      chratsLoading2: false,
      chratsLoading3: false,
      activeName: '1',
      activeType: '1',
      filterData: {
        dept: '',
        eqName: '',
        sysClassify: '',
        planName: '',
        planTypeId: '',
        dateInterval: [moment().format('YYYY-MM-01'), moment()]
      },
      classifyOption: [],
      deptList: [],
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      tableData: [],
      exportLoading: false,
      planTypeTemplateArr: []
    }
  },
  created() {
    if (this.$route.query.activeName) {
      this.activeName = this.$route.query.activeName
      this.activeType = this.$route.query.activeType
      if (this.activeType == '1') {
        this.getTaskListBySys()
      } else if (this.activeType == '2') {
        this.getDeviceChratsPie()
        this.getTaskListByDevice()
        this.getChratsDataByDevice()
      } else if (this.activeType == '3') {
        this.getTableByDept()
        this.getEchartsByDept()
        this.getEchartsBySttaf()
      } else if (this.activeType == '4') {
        this.getListDataByPlan()
      }
    } else {
      this.getTaskListBySys()
    }
    this.getDeptList()
    this.getClassifList()
    this.getTemplateClassification()
  },
  methods: {
    handleClick() {
      this.activeType = '1'
      this.$router.push({
        query: {
          activeName: this.activeName,
          activeType: this.activeType
        }
      })
      this.filterData = {
        dept: '',
        eqName: '',
        sysClassify: '',
        planTypeId: '',
        planName: '',
        dateInterval: [moment().format('YYYY-MM-01'), moment()]
      }
      this.getTemplateClassification()
      this.clearEcharts()
      this.getTaskListBySys()
    },
    changeType(val) {
      this.activeType = val
      this.paginationData = {
        currentPage: 1,
        pageSize: 15,
        total: 0
      }
      this.filterData = {
        dept: '',
        eqName: '',
        sysClassify: '',
        planTypeId: '',
        planName: '',
        dateInterval: [moment().format('YYYY-MM-01'), moment()]
      }
      this.clearEcharts()
      if (val == '1') {
        this.getTaskListBySys()
      } else if (val == '2') {
        this.getDeviceChratsPie()
        this.getTaskListByDevice()
        this.getChratsDataByDevice()
      } else if (val == '3') {
        this.getTableByDept()
        this.getEchartsByDept()
        this.getEchartsBySttaf()
      } else if (this.activeType == '4') {
        this.getListDataByPlan()
      }
      this.$router.push({
        query: {
          activeName: this.activeName,
          activeType: this.activeType
        }
      })
    },
    // 模板分类
    getTemplateClassification() {
      this.$api
        .getTemplateType({
          systemIdentificationClassification: this.activeName
        })
        .then((res) => {
          if (res.code == '200') {
            this.planTypeTemplateArr = res.data
          }
        })
    },
    // 获取系统分类
    getClassifList() {
      this.$api.getDeviceType({ levelType: 1 }).then((res) => {
        if (res.code == '200') {
          this.classifyOption = res.data
        }
      })
    },
    // 获取部门列表
    getDeptList() {
      this.$api.getSelectedDept().then((res) => {
        if (res.code == '200') {
          this.deptList = res.data
        }
      })
    },
    search() {
      this.clearEcharts()
      if (this.activeType == '1') {
        this.getTaskListBySys()
      } else if (this.activeType == '2') {
        this.getDeviceChratsPie()
        this.getTaskListByDevice()
        this.getChratsDataByDevice()
      } else if (this.activeType == '3') {
        this.getTableByDept()
        this.getEchartsByDept()
        this.getEchartsBySttaf()
      } else if (this.activeType == '4') {
        this.getListDataByPlan()
      }
    },
    reset() {
      this.paginationData = {
        currentPage: 1,
        pageSize: 15,
        total: 0
      }
      this.filterData = {
        dept: '',
        eqName: '',
        sysClassify: '',
        planName: '',
        dateInterval: [moment().format('YYYY-MM-01'), moment()]
      }
      this.clearEcharts()
      if (this.activeType == '1') {
        this.getTaskListBySys()
      } else if (this.activeType == '2') {
        this.getDeviceChratsPie()
        this.getTaskListByDevice()
        this.getChratsDataByDevice()
      } else if (this.activeType == '3') {
        this.getTableByDept()
        this.getEchartsByDept()
        this.getEchartsBySttaf()
      } else if (this.activeType == '4') {
        this.getListDataByPlan()
      }
    },
    // 按系统分类数据
    getTaskListBySys() {
      this.pageLoading = true
      const params = {
        systemCode: this.activeName,
        professionalCategoryCode: this.filterData.sysClassify,
        taskStartTime: this.filterData.dateInterval[0] ? moment(this.filterData.dateInterval[0]).format('YYYY-MM-DD') : '',
        taskEndTime: this.filterData.dateInterval[1] ? moment(this.filterData.dateInterval[1]).format('YYYY-MM-DD') : ''
      }
      this.$api.getTaskBySys(params).then((res) => {
        if (res.code == '200') {
          this.tableData = res.data
          const sysAnalysisData = []
          const deviceAnalysisData = []
          if (res.data.length) {
            res.data.forEach((i) => {
              sysAnalysisData.push({
                name: i.professionalCategoryName,
                value: i.taskCount,
                percentage: i.taskCountPercentage
              })
              deviceAnalysisData.push({
                name: i.professionalCategoryName,
                value: i.deviceNumber,
                percentage: i.deviceNumberPercentage
              })
            })
          }
          this.setPieEcharts('taskAnalysis', sysAnalysisData, '系统任务占比统计')
          this.setPieEcharts('deviceAnalysis', deviceAnalysisData, '系统设备占比统计')
        } else {
          this.$message.error(res.message || '请求失败')
        }
        this.pageLoading = false
      })
    },
    // 按设备分类列表
    getTaskListByDevice() {
      this.pageLoading = true
      const params = {
        systemCode: this.activeName,
        taskPointName: this.filterData.eqName,
        pageNo: this.paginationData.currentPage,
        pageSize: this.paginationData.pageSize,
        professionalCategoryCode: this.filterData.sysClassify,
        taskStartTime: this.filterData.dateInterval[0] ? moment(this.filterData.dateInterval[0]).format('YYYY-MM-DD') : '',
        taskEndTime: this.filterData.dateInterval[1] ? moment(this.filterData.dateInterval[1]).format('YYYY-MM-DD') : ''
      }
      this.$api.getTaskByDevice(params).then((res) => {
        if (res.code == '200') {
          this.tableData = res.data.list
          this.paginationData.total = res.data.sum
        } else {
          this.$message.error(res.message || '请求失败')
        }
        this.pageLoading = false
      })
    },
    // 按设备分类饼状图
    getDeviceChratsPie() {
      this.chratsLoading1 = true
      const params = {
        systemCode: this.activeName,
        taskPointName: this.filterData.eqName,
        professionalCategoryCode: this.filterData.sysClassify,
        taskStartTime: this.filterData.dateInterval[0] ? moment(this.filterData.dateInterval[0]).format('YYYY-MM-DD') : '',
        taskEndTime: this.filterData.dateInterval[1] ? moment(this.filterData.dateInterval[1]).format('YYYY-MM-DD') : ''
      }
      this.$api.getChratsByEquipment(params).then((res) => {
        if (res.code == '200') {
          const sysAnalysisData = []
          res.data.forEach((i) => {
            sysAnalysisData.push({
              id: i.professionalCategoryCode,
              name: i.professionalCategoryName,
              value: i.deviceNumber,
              percentage: i.deviceNumberPercentage
            })
          })
          this.setPieEcharts('taskAnalysis', sysAnalysisData, '系统设备占比统计')
        } else {
          this.$message.error(res.message || '请求失败')
        }
      })
    },
    // 按设备分类柱状图
    getChratsDataByDevice(typeId) {
      this.chratsLoading2 = true
      const params = {
        systemCode: this.activeName,
        taskPointName: this.filterData.eqName,
        pageNo: this.paginationData.currentPage,
        pageSize: this.paginationData.pageSize,
        professionalCategoryCode: typeId || this.filterData.sysClassify,
        taskStartTime: this.filterData.dateInterval[0] ? moment(this.filterData.dateInterval[0]).format('YYYY-MM-DD') : '',
        taskEndTime: this.filterData.dateInterval[1] ? moment(this.filterData.dateInterval[1]).format('YYYY-MM-DD') : ''
      }
      this.$api.getChratsByDevice(params).then((res) => {
        if (res.code == '200') {
          const barData = {
            xAxisArr: [],
            hasArr: [],
            notArr: []
          }
          if (res.data) {
            res.data.forEach((i) => {
              barData.xAxisArr.push(i.taskPointName)
              barData.hasArr.push(i.hasCount)
              barData.notArr.push(i.notCount)
            })
          }
          this.setBarEcharts('deviceAnalysis', barData)
        } else {
          this.$message.error(res.message || '请求失败')
        }
      })
    },
    // 按部门统计列表
    getTableByDept() {
      this.tableLoading = true
      const params = {
        systemCode: this.activeName,
        pageNo: this.paginationData.currentPage,
        pageSize: this.paginationData.pageSize,
        distributionTeamId: this.filterData.dept,
        taskStartTime: this.filterData.dateInterval[0] ? moment(this.filterData.dateInterval[0]).format('YYYY-MM-DD') : '',
        taskEndTime: this.filterData.dateInterval[1] ? moment(this.filterData.dateInterval[1]).format('YYYY-MM-DD') : ''
      }
      this.$api.getChratsByDepartment(params).then((res) => {
        if (res.code == '200') {
          this.tableData = res.data.list
          this.paginationData.total = res.data.sum
          const barData = {
            xAxisArr: [],
            hasArr: [],
            notArr: []
          }
          res.data.list.forEach((i) => {
            barData.xAxisArr.push(i.distributionTeamName)
            barData.hasArr.push(i.accomplishCount)
            barData.notArr.push(i.unfinishedCount)
          })
        } else {
          this.$message.error(res.message || '请求失败')
        }
        this.tableLoading = false
      })
    },
    // 按部门统计柱状图
    getEchartsByDept() {
      this.chratsLoading2 = true
      const params = {
        systemCode: this.activeName,
        distributionTeamId: this.filterData.dept,
        taskStartTime: this.filterData.dateInterval[0] ? moment(this.filterData.dateInterval[0]).format('YYYY-MM-DD') : '',
        taskEndTime: this.filterData.dateInterval[1] ? moment(this.filterData.dateInterval[1]).format('YYYY-MM-DD') : ''
      }
      this.$api.getChratsByDepartment(params).then((res) => {
        if (res.code == '200') {
          this.tableData = res.data.list
          this.paginationData.total = res.data.sum
          const barData = {
            xAxisArr: [],
            hasArr: [],
            notArr: []
          }
          if (res.data) {
            res.data.list.forEach((i) => {
              barData.xAxisArr.push(i.distributionTeamName)
              barData.hasArr.push(i.accomplishCount)
              barData.notArr.push(i.unfinishedCount)
            })
          }
          this.setBarEcharts('deptAnalysis', barData, '部门任务分析')
        } else {
          this.$message.error(res.message || '请求失败')
        }
      })
    },
    // 按人员统计柱状图
    getEchartsBySttaf() {
      this.chratsLoading3 = true
      const params = {
        systemCode: this.activeName,
        distributionTeamId: this.filterData.dept,
        taskStartTime: this.filterData.dateInterval[0] ? moment(this.filterData.dateInterval[0]).format('YYYY-MM-DD') : '',
        taskEndTime: this.filterData.dateInterval[1] ? moment(this.filterData.dateInterval[1]).format('YYYY-MM-DD') : ''
      }
      this.$api.getChratsBySttaf(params).then((res) => {
        if (res.code == '200') {
          const barData = []
          if (res.data) {
            res.data.forEach((i) => {
              barData.push({
                name: i.name,
                value: i.count
              })
            })
          }
          this.setSttafBraEcharts('staffAnalysis', barData, this.activeName == '1' ? '人员巡检分析' : '人员保养分析')
        } else {
          this.$message.error(res.message || '请求失败')
        }
      })
    },
    exportExcel() {
      this.exportLoading = true
      const userInfo = this.$store.state.user.userInfo.user
      const params = {
        unitCode: userInfo.unitCode ?? 'BJSYGJ',
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        systemCode: this.activeName,
        taskStartTime: this.filterData.dateInterval[0] ? moment(this.filterData.dateInterval[0]).format('YYYY-MM-DD') : '',
        taskEndTime: this.filterData.dateInterval[1] ? moment(this.filterData.dateInterval[1]).format('YYYY-MM-DD') : ''
      }
      let url = ''
      if (this.activeType == '1') {
        url = __PATH.VUE_ICIS_API + 'planTaskStatistics/taskTypeListExport'
        params.professionalCategoryCode = this.filterData.sysClassify
      } else if (this.activeType == '2') {
        url = __PATH.VUE_ICIS_API + 'planTaskStatistics/equipmentTaskExport'
        params.professionalCategoryCode = this.filterData.sysClassify
        params.taskPointName = this.filterData.eqName
      } else if (this.activeType == '3') {
        url = __PATH.VUE_ICIS_API + 'planTaskStatistics/departmentTaskExport'
        params.distributionTeamId = this.filterData.dept
      } else if (this.activeType == '4') {
        params.planName = this.filterData.planName
        params.planTypeId = this.filterData.planTypeId ? this.filterData.planTypeId.join(',') : ''
        url = __PATH.VUE_ICIS_API + 'planTaskNew/maintainPlanStatisticsExport'
      }
      const formData = new FormData()
      Object.keys(params).forEach((key) => {
        formData.append(key, params[key])
      })
      axios({
        method: 'post',
        url,
        data: formData,
        responseType: 'blob',
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token,
          'operation-type': 4
        }
      })
        .then((res) => {
          const name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
          const blob = new Blob([res.data])
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
          this.exportLoading = false
        })
        .catch(() => {
          this.$message.error(res.message || '导出失败')
          this.exportLoading = false
        })
    },
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.paginationData.currentPage = 1
      if (this.activeType == '2') {
        this.getTaskListByDevice()
      } else if (this.activeType == '3') {
        this.getTableByDept()
      } else if (this.activeType == '4') {
        this.getListDataByPlan()
      }
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      if (this.activeType == '2') {
        this.getTaskListByDevice()
      } else if (this.activeType == '3') {
        this.getTableByDept()
      } else if (this.activeType == '4') {
        this.getListDataByPlan()
      }
    },
    setPieEcharts(ref, data, title) {
      this.$nextTick(() => {
        const chratsEle = echarts.init(this.$refs[ref])
        let option = {}
        if (data.length) {
          option = {
            title: {
              text: title,
              left: '35%'
            },
            grid: {
              bottom: '50%'
            },
            tooltip: {
              trigger: 'item'
            },
            legend: {
              type: 'scroll',
              orient: 'vertical',
              right: '8%',
              top: '30%',
              formatter: (name) => {
                return name + `(${data.find((i) => i.name == name).value}条)         ` + data.find((i) => i.name == name).percentage
              }
            },
            series: [
              {
                type: 'pie',
                center: ['35%', '50%'],
                radius: '60%',
                data: data
              }
            ]
          }
        } else {
          option = {
            title: {
              text: '暂无数据',
              x: 'center',
              y: 'center',
              textStyle: {
                fontSize: 14,
                fontWeight: 'normal',
                color: '#999'
              }
            }
          }
        }
        chratsEle.setOption(option)
        this.chratsLoading1 = false
        if (this.activeType == '2') {
          chratsEle.off('click')
          chratsEle.on('click', (params) => {
            this.getChratsDataByDevice(params.data.id)
          })
        }
      })
    },
    setSttafBraEcharts(ref, data, title) {
      this.$nextTick(() => {
        const chratsEle = echarts.init(this.$refs[ref])
        let option = {}
        if (data.length) {
          option = {
            title: {
              text: title,
              left: '40%'
            },
            tooltip: {
              trigger: 'item'
            },
            legend: {
              itemHeight: 8,
              itemWidth: 8,
              icon: 'circle',
              top: '1%',
              left: '10%',
              textStyle: {
                color: '#666666'
              }
            },
            xAxis: {
              type: 'category',
              axisLine: {
                show: false
              },
              axisTick: {
                show: false
              },
              data: data.map((i) => i.name)
            },
            yAxis: {
              type: 'value'
            },
            grid: {
              top: '12%',
              left: '4%',
              right: '5%',
              bottom: '8%',
              containLabel: true
            },
            dataZoom: [
              {
                fillerColor: '#BBC3CE',
                backgroundColor: '#fff',
                height: 10,
                type: 'slider',
                bottom: 10,
                textStyle: {
                  color: '#000'
                },
                start: 0,
                end: (10 / data.length) * 100
              },
              {
                type: 'inside', // 支持内部鼠标滚动平移
                start: 0,
                end: (10 / data.length) * 100,
                zoomOnMouseWheel: false, // 关闭滚轮缩放
                moveOnMouseWheel: true, // 开启滚轮平移
                moveOnMouseMove: true // 鼠标移动能触发数据窗口平移
              }
            ],
            series: [
              {
                data: data.map((i) => i.value),
                barWidth: 15,
                type: 'bar'
              }
            ]
          }
        } else {
          option = {
            title: {
              text: '暂无数据',
              x: 'center',
              y: 'center',
              textStyle: {
                fontSize: 14,
                fontWeight: 'normal',
                color: '#999'
              }
            }
          }
        }
        chratsEle.setOption(option)
      })
    },
    setBarEcharts(ref, data, title) {
      this.$nextTick(() => {
        const chratsEle = echarts.init(this.$refs[ref])
        let option = {}
        if (data.xAxisArr.length) {
          option = {
            title: {
              show: title,
              text: title,
              left: '40%'
            },
            color: ['#3562DB', '#FF9435', '#00BC6D'],
            tooltip: {
              trigger: 'item',
              position: 'left'
            },
            legend: {
              itemHeight: 8,
              itemWidth: 8,
              icon: 'circle',
              top: '1%',
              left: '10%',
              textStyle: {
                color: '#666666'
              }
            },
            grid: {
              top: '12%',
              left: '4%',
              right: '5%',
              bottom: '8%',
              containLabel: true
            },
            dataZoom: [
              {
                fillerColor: '#BBC3CE',
                backgroundColor: '#fff',
                height: 10,
                type: 'slider',
                bottom: 10,
                textStyle: {
                  color: '#000'
                },
                start: 0,
                end: (10 / data.xAxisArr.length) * 100
              },
              {
                type: 'inside', // 支持内部鼠标滚动平移
                start: 0,
                end: (10 / data.xAxisArr.length) * 100,
                zoomOnMouseWheel: false, // 关闭滚轮缩放
                moveOnMouseWheel: true, // 开启滚轮平移
                moveOnMouseMove: true // 鼠标移动能触发数据窗口平移
              }
            ],
            xAxis: {
              type: 'category',
              data: data.xAxisArr,
              axisLine: {
                show: false
              },
              axisLabel: {
                rotate: 45,
                margin: 15,
                formatter: function (val) {
                  var strs = val.split('') // 字符串数组
                  var str = ''
                  // strs循环 forEach 每五个字符增加换行符
                  strs.forEach((item, index) => {
                    if (index % 6 === 0 && index !== 0) {
                      str += '\n'
                    }
                    str += item
                  })
                  return str
                }
              },
              axisTick: {
                show: false
              }
            },
            yAxis: [
              {
                type: 'value',
                axisLine: {
                  show: false
                },
                axisTick: {
                  show: false
                }
              }
            ],
            series: [
              {
                name: this.activeName == '1' ? '已巡检' : '已保养',
                type: 'bar',
                stack: 'Ad',
                barWidth: 15,
                emphasis: {
                  focus: 'series'
                },
                data: data.hasArr,
                itemStyle: {
                  borderColor: '#fff',
                  borderWidth: 1
                }
              },
              {
                name: this.activeName == '1' ? '未巡检' : '未保养',
                type: 'bar',
                stack: 'Ad',
                barWidth: 15,
                emphasis: {
                  focus: 'series'
                },
                data: data.notArr,
                itemStyle: {
                  borderColor: '#fff',
                  borderWidth: 1
                }
              }
            ]
          }
        } else {
          option = {
            title: {
              text: '暂无数据',
              x: 'center',
              y: 'center',
              textStyle: {
                fontSize: 14,
                fontWeight: 'normal',
                color: '#999'
              }
            }
          }
        }
        chratsEle.setOption(option)
        this.chratsLoading2 = false
      })
    },
    clearEcharts() {
      let chratsArr = ['taskAnalysis', 'deviceAnalysis', 'deptAnalysis', 'staffAnalysis']
      chratsArr.forEach((i) => {
        if (this.$refs[i]) {
          const chratsEle = echarts.init(this.$refs[i])
          chratsEle.clear()
        }
      })
    },
    goList(row, count, type) {
      if (count == 0) {
        this.$message.error('暂无更多')
      } else {
        const parames = {
          systemCode: this.activeName,
          activeType: this.activeType,
          sysClassify: this.filterData.sysClassify,
          taskStartTime: this.filterData.dateInterval[0] ? moment(this.filterData.dateInterval[0]).format('YYYY-MM-DD') : '',
          taskEndTime: this.filterData.dateInterval[1] ? moment(this.filterData.dateInterval[1]).format('YYYY-MM-DD') : '',
          listType: type // 0工单 1设备 2总数 3已巡检 4未巡检 5不合格
        }
        if (this.activeType == '1') {
          // 按系统
          parames.sysTypeId = row.professionalCategoryCode
        } else if (this.activeType == '2') {
          // 按设备
          ;(parames.eqName = this.filterData.eqName), (parames.pointCode = row.taskPointId)
        } else if (this.activeType == '3') {
          // 按部门
          parames.distributionTeamId = row.distributionTeamId
        } else if (this.activeType == '4') {
          // 计划
          parames.planId = row.planId
        }
        if (type == 3) {
          parames.taskStatus = 2
        } else if (type == 4) {
          parames.taskStatus = 1
        } else if (type == 5) {
          parames.state = 3
        }
        this.$router.push({
          path: 'taskListByStatistic',
          query: parames
        })
      }
    },
    getListDataByPlan() {
      const params = {
        systemCode: this.activeName,
        planTypeId: this.filterData.planTypeId ? this.filterData.planTypeId.join(',') : '',
        planName: this.filterData.planName,
        pageNo: this.paginationData.currentPage,
        pageSize: this.paginationData.pageSize,
        taskStartTime: this.filterData.dateInterval[0] ? moment(this.filterData.dateInterval[0]).format('YYYY-MM-DD') : '',
        taskEndTime: this.filterData.dateInterval[1] ? moment(this.filterData.dateInterval[1]).format('YYYY-MM-DD') : ''
      }
      this.tableLoading = true
      this.$api.getListStatisticsByPlan(params).then((res) => {
        if (res.code == '200') {
          this.tableData = res.data.list
          this.paginationData.total = res.data.sum
        }
        this.tableLoading = false
      })
    },
    removeTag() {
      this.getListDataByPlan()
    }
  }
}
</script>
<style lang="scss" scoped>
.contentInner {
  height: calc(100% - 30px);
  padding: 10px 20px;
  margin: 15px;
  border-radius: 5px;
  background-color: #fff;
  .typeWrap {
    padding: 20px 0;
    display: flex;
    align-items: center;
    .typeItem {
      padding: 8px 25px;
      margin-right: 20px;
      background-color: #ededf5;
      border-radius: 2px;
      cursor: pointer;
    }
    .activeType {
      background-color: #3562db;
      color: #fff;
    }
  }
  .tableChrat1 {
    margin-top: 20px;
    height: calc(100% - 168px);
    display: flex;
    .leftTable {
      margin-right: 10px;
      width: calc(55% - 10px);
      height: 100%;
    }
    .rightChrat {
      width: 45%;
      height: 100%;
      display: flex;
      flex-direction: column;
      .chratItem {
        width: 100%;
        height: 50%;
      }
    }
  }
  .tableChrat2 {
    margin-top: 20px;
    height: calc(100% - 168px);
    display: flex;
    flex-direction: column;
    .topChrat {
      height: 50%;
      width: 100%;
      display: flex;
      .chratItem {
        width: 50%;
        height: 100%;
      }
    }
    .bottomTable {
      height: 50%;
      width: 100%;
    }
  }
  .tableChrat3 {
    margin-top: 20px;
    height: calc(100% - 168px);
  }
}
</style>
