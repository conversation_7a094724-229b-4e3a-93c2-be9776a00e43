<template>
  <div ref="listMode" class="listMode">
    <div class="listMode-head">
      <el-input v-model="surveyName" placeholder="监测项名称" suffix-icon="el-icon-search" style="width: 200px" clearable />
      <div style="display: inline-block">
        <el-button type="primary" plain @click="resetForm">重置</el-button>
        <el-button type="primary" @click="getDataList">查询</el-button>
      </div>
    </div>
    <div v-loading="loading" class="listMode-main">
      <el-row>
        <!-- <el-col v-for="item in listData" :key="item.surveyCode" :xs="24" :sm="12" :md="24" :lg="12" :xl="8"> -->
        <el-col v-for="(item, index) in listData" :key="item.surveyCode" :xs="24" :md="24" :lg="12" :xl="8">
          <div class="card-content">
            <div class="card-heade">
              <span>{{ item.surveyName }}</span>
              <span class="incidentRecord" @click="toRecord(item)">事件纪录</span>
            </div>
            <div class="card-main">
              <div class="main-content">
                <div
                  v-for="v in item.paramList.slice((item.leftPaging.page - 1) * item.leftPaging.size, item.leftPaging.page * item.leftPaging.size)"
                  :key="v.parameterId" class="main-item">
                  <p class="item-label">
                    <span>{{ v.paramName }}</span>
                  </p>
                  <div class="item-value">
                    <p>
                      <span class="item-num"
                        :style="{ color: v.intervalName ? v.colour : '#121F3E' }">{{ v.value }}</span>
                      <span class="item-unit">{{ v.unitName }}</span>
                    </p>
                    <span v-if="v.intervalName" class="item-info"
                      :style="{ backgroundColor: v.intervalColor }">{{ v.intervalName }}</span>
                  </div>
                </div>
              </div>
              <div class="main-footer">
                <el-pagination layout="prev, pager, next" hide-on-single-page :page-size="item.leftPaging.size"
                  :current-page="item.leftPaging.page" :total="item.paramList.length" @current-change="
                    (val) => {
                      pageChange(val, index, 'left')
                    }
                  ">
                </el-pagination>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="listMode-footer">
      <el-pagination :current-page="pagination.current" :page-sizes="pagination.pageSizeOptions"
        :page-size="pagination.size" :layout="pagination.layoutOptions" :total="pagination.total"
        @size-change="paginationSizeChange" @current-change="paginationCurrentChange" />
    </div>
    <!--事件纪录 -->
    <template v-if="eventRecordDialogShow">
      <selectEventRecord :eventRecordDialogShow="eventRecordDialogShow" :objectId="objectId"
        @closeEventRecordDialog="closeEventRecordDialog" />
    </template>
  </div>
</template>

<script>
import tableListMixin from '@/mixins/tableListMixin.js'
import selectEventRecord from './selectEventRecord.vue'
export default {
  name: 'listMode',
  components: {
    selectEventRecord
  },
  mixins: [tableListMixin],
  props: {
    requestInfo: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      surveyName: '',
      alarmDialog: false,
      loading: false,
      eventRecordDialogShow: false, // 事件纪录弹窗
      listData: [],
      alarmType: {
        3: 'urgent_icon',
        2: 'serious_icon',
        1: 'commonly_icon',
        0: 'tips_icon'
      },
      objectId: ''
    }
  },
  computed: {},
  watch: {},
  created() {
    this.getDataList()
  },
  methods: {
    // 重置查询
    resetForm() {
      this.surveyName = ''
      this.getDataList()
    },
    // 获取检测项列表
    getDataList() {
      let params = {
        surveyName: this.surveyName,
        projectCode: this.requestInfo.projectCode,
        page: this.pagination.current,
        pageSize: this.pagination.size
      }
      this.loading = true
      this.listData = []
      this.$api.GetElectricFocusList(params).then((res) => {
        this.loading = false
        console.log(22222, res)
        if (res.code == 200) {
          res.data.list.forEach((item) => {
            if (!item.leftPaging) {
              item.leftPaging = { page: 1, size: 9 }
            }
          })
          this.listData = res.data.list ? res.data.list : []
          this.pagination.total = res.data.totalCount ? res.data.totalCount : 0
          console.log(1111, this.listData)
        }
      })
    },
    // 前端分页
    pageChange(page, index, type) {
      let newObj = JSON.parse(JSON.stringify(this.listData[index]))
      newObj[type + 'Paging'].page = page
      this.listData.splice(index, 1, newObj)
    },
    // 事件纪录弹窗开启
    toRecord(item) {
      this.eventRecordDialogShow = true
      this.objectId = item.surveyCode
    },
    // 事件记录弹窗关闭
    closeEventRecordDialog() {
      this.eventRecordDialogShow = false
    }
  }
}
</script>

<style lang="scss" scoped>
.listMode {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  .listMode-head {
    margin: 16px 10px 0px 16px;
    padding: 0 200px 10px 10px !important;
    background: #fff;
    border-radius: 4px;
    position: relative;

    & > div {
      margin-right: 10px;
      margin-top: 10px;
    }
  }

  .listMode-main {
    overflow: auto;
    width: 100%;
    flex: 1;
    display: flex;
    flex-wrap: wrap;

    ::v-deep .el-loading-mask {
      left: 16px;
    }

    ::v-deep .el-row {
      width: 100%;

      .el-col {
        margin-top: 16px;
        padding-left: 16px;
      }
    }

    .card-content {
      width: 100%;
      background: #fff;
      border-radius: 4px;
      position: relative;

      p {
        margin: 0;
      }

      .card-heade {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        box-shadow: 0 0 5px 0 rgb(18 31 62 / 12%);
        font-size: 15px;
        color: #121f3e;
        .incidentRecord {
          color: #3562db !important;
          cursor: pointer;
        }
      }

      .card-main {
        padding: 16px;
        height: 290px;
        width: 100%;
        .main-content {
          height: 90%;
          width: 100%;
          display: flex;
          flex-wrap: wrap;
        }

        .main-footer {
          height: 10%;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          ::v-deep .el-pagination {
            padding: 0;
          }
        }

        .main-item {
          width: calc(100% / 3);
        }

        .item-label {
          font-size: 14px;
          color: #414653;
          display: flex;
          align-items: center;

          span {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .item-value {
          margin-top: 9px;
          display: flex;
          font-weight: 500;
          align-items: flex-end;

          .item-num {
            color: #121f3e;
            line-height: 26px;
            font-weight: bold;
          }

          .item-unit {
            font-size: 12px;
            color: #ccced3;
            margin-left: 4px;
          }

          .item-info {
            height: 20px;
            display: inline-block;
            font-size: 14px;
            color: #fff;
            line-height: 20px;
            padding: 0 6px;
            border-radius: 4px;
            margin-bottom: 4px;
            margin-left: 16px;
          }
        }

        .item-icon {
          font-size: 20px;
          margin-right: 4px;
        }

        ::v-deep .el-pagination {
          .btn-next,
          .btn-prev {
            min-width: auto;
            height: 21px;
            line-height: 21px;

            .el-icon {
              line-height: 21px;
            }
          }

          .btn-prev {
            padding: 0 2px 0 0;
          }

          .btn-next {
            padding: 0 0 0 2px;
          }

          .el-pager > li {
            min-width: auto;
            height: auto;
            line-height: 21px;
            font-size: 13px;
            padding: 0 2px;
          }
        }
      }

      .openCard {
        display: flex;
        justify-content: center;
        padding-top: 10px;

        span {
          cursor: pointer;
          padding: 3px 10px;
          display: flex;
          align-items: center;
          background: rgb(53 98 219 / 20%);
          border-radius: 4px 4px 0 0;
          font-size: 14px;
          color: #3562db;
        }

        .open-icon {
          font-size: 10px;
          margin-left: 6px;
        }
      }
    }
  }

  .listMode-footer {
    padding: 10px 0 10px 16px;

    ::v-deep .el-pagination {
      .btn-next,
      .btn-prev {
        background: transparent;
      }

      .el-pager li {
        background: transparent;
      }
    }
  }
}
</style>
