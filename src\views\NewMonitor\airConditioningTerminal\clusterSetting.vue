<template>
  <PageContainer :footer="true">
    <div slot="content" v-loading="formLoading" class="form-content">
      <div class="form-title"><i class="el-icon-arrow-left" style="cursor:pointer" @click="() => {
        $router.go(-1)
      }
      "></i><span style="margin-left: 10px">配置</span></div>
      <el-form ref="formInline" :model="formInline">
        <ContentCard title="基础信息">
          <div slot="content" class="footer-role">
            <div class="heade-info">
              <p class="heade-p">
                <span>设备名称：</span>
                <span>{{ detailsData.assetsName }}</span>
              </p>
              <p class="heade-p">
                <span>设备编码：</span>
                <span>{{ detailsData.factoryCode }}</span>
              </p>
              <p class="heade-p">
                <span>通用名称：</span>
                <span>{{ detailsData.commonName }}</span>
              </p>
              <p class="heade-p">
                <span>所属品类：</span>
                <span>{{ detailsData.sysOf1Name }}</span>
              </p>
              <p class="heade-p">
                <span>模型ID：</span>
                <span>{{ detailsData.modelCode }}</span>
              </p>
              <p class="heade-p">
                <span>模型坐标：</span>
                <span
                  v-if="detailsData.modelPositionX !== null || detailsData.modelPositionY !== null || detailsData.modelPositionZ !== null">{{
                    detailsData.modelPositionX }},{{ detailsData.modelPositionY }},{{ detailsData.modelPositionZ
                  }}</span>
              </p>
              <p class="heade-p">
                <span>所属区域：</span>
                <span>{{ detailsData.spaceLocationName }}</span>
              </p>
            </div>
          </div>
        </ContentCard>
        <ContentCard title="关联信息">
          <div slot="content">
            <el-row :gutter="24" style="margin: 0 0 20px;">
              <el-col :md="24">
                <div>
                  <span class="form-btn-title">关联监控画面</span><el-button class="form-btn-btn" type="primary"
                    @click="associatedCamera()">选择</el-button><el-tag v-for="tag in imsVidiconList"
                    :key="tag.imsVidiconId" class="camera-tag" closable @close="tagHandleClose(tag.imsVidiconId)">
                    {{ tag.imsVidiconName }}
                  </el-tag>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="24" style="margin: 0 0 20px;">
              <el-col :md="24">
                <div>
                  <span class="form-btn-title">关联空间</span><el-button class="form-btn-btn" type="primary"
                    @click="changeServicesSpeace()">选择</el-button>
                  <el-tag v-for="tag in spaceNames" :key="tag.id" class="camera-tag" closable
                    @close="tagSpaceHandleClose(tag.id)">
                    {{ tag.gridName }}
                  </el-tag>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="24" style="margin: 0;">
              <el-col :md="24">
                <el-form-item label="系统图纸（scada）">
                  <el-select v-model="formInline.scaleFolderIdSystem" filterable clearable placeholder="请选择"
                    @change="changeEntity(1, formInline.scaleFolderIdSystem)">
                    <el-option v-for="(item, index) in systemDrawing" :key="index" :label="item.name" :value="item.id">
                    </el-option>
                  </el-select>
                  <el-select v-model="formInline.scaleImageIdSystem" filterable clearable placeholder="请选择"
                    style="margin-left: 10px;" @change="changeLevelEntity(1, formInline.scaleImageIdSystem)">
                    <el-option v-for="(item, index) in systemDrawingList" :key="index" :label="item.name"
                      :value="item.id"> </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :md="24">
                <el-form-item label="设备图纸（scada）">
                  <el-select v-model="formInline.scaleFolderIdEquipment" filterable clearable placeholder="请选择"
                    @change="changeEntity(2, formInline.scaleFolderIdEquipment)">
                    <el-option v-for="(item, index) in serviceDrawing" :key="index" :label="item.name" :value="item.id">
                    </el-option>
                  </el-select>
                  <el-select v-model="formInline.scaleImageIdEquipment" filterable clearable placeholder="请选择"
                    style="margin-left: 10px;" @change="changeLevelEntity(2, formInline.scaleImageIdEquipment)">
                    <el-option v-for="(item, index) in serviceDrawingList" :key="index" :label="item.name"
                      :value="item.id"> </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </ContentCard>
        <ContentCard title="物联信息">
          <el-button slot="title-right" class="form-btn-btn" type="primary" @click="addrow"
            v-if="$route.query.tabActive === '2'"><i class="el-icon-plus"></i>
            添加</el-button>
          <div slot="content">
            <TablePage ref="tablePage" v-loading="tableLoading" v-staticLoadMore="handelLoadmore"
              :tableColumn="tableColumn" :showPage="false" :data="tableData" border>
            </TablePage>
          </div>
        </ContentCard>
      </el-form>
      <!-- 弹窗事件 -->
      <template>
        <monitoringParamsGather :serviceSpaceDialogShow="serviceSpaceDialogShow" :cameraDialogShow="cameraDialogShow"
          :parameterAliasDialogShow="parameterAliasDialogShow" :spaceData="spaceNames"
          :cameraDialogData="cameraDialogData" :parameterData="parameterData"
          :equipmentAssetsDialogData="equipmentAssetsDialogData" :parameterAliasDialogData="parameterAliasDialogData"
          :assetsClickType="assetsClickType" @submitDialog="submitMonitoringParamsGather" />
      </template>
      <!-- 选择物联设备 -->
      <template v-if="harvesterDialogShow">
        <harvesterDialog :dialogShow.sync="harvesterDialogShow" :dialogData="selectHarvesterDialogData"
          :tableDataList="tableData" harvesterCheckType="radio" @submitDialog="submitHarvesterDialog"
          @closeDialog="closeHarvesterDialog" />
      </template>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="closePage">关闭</el-button>
      <el-button v-if="$route.query?.type != 'detail'" :disabled="isSubmitting" type="primary"
        @click="submitForm()">保存</el-button>
    </div>
  </PageContainer>
</template>
<script lang="jsx">
import monitoringParamsGather from './components/monitoringParamsGather'
import harvesterDialog from './components/harvesterDialog'
export default {
  name: 'clusterSetting',
  components: {
    monitoringParamsGather,
    harvesterDialog
  },
  data() {
    return {
      detailsData: {},
      // 物联信息
      harvesterDialogShow: false,
      selectHarvesterDialogData: {},
      currentStartIndex: 0,
      currentEndIndex: 50,
      tableLoading: false,
      tableData: [],
      valueType: null,
      selectedCount: 0,
      tableColumn: [
        {
          prop: 'assetsName',
          label: '设备名称',
        },
        {
          prop: 'factoryCode',
          label: '设备编码',
        },
        {
          prop: 'metadataName',
          label: '参数名称',
        },
        {
          prop: 'metadataType',
          label: '参数类型',
          render: (h, row) => {
            return (
              <div>{row.row.metadataType == 'properties' ? "属性" : row.row.metadataType == 'functions' ? "功能" : "事件"}</div>
            )
          },
        },
        {
          prop: 'metadataNameAlias',
          label: '参数别名',
          render: (h, row) => {
            return (
              <div>
                <el-input v-model={row.row.metadataNameAlias} clearable placeholder="" style="width:100%;"></el-input>
                {
                  (
                    (row.row.metadataType === 'properties' && (row.row.valueType?.type === 'boolean' || row.row.valueType?.type === 'enum')) ||
                    (row.row.metadataType === 'functions' && (row.row.valueType?.valueType?.type === 'boolean' || row.row.valueType?.valueType?.type === 'enum'))
                  ) ? (
                    <span class="el-icon-edit" onClick={(event) => this.handleInputChange(row.row, event)}>编辑</span>
                  ) : null
                }
              </div>
            )
          },
        },
        {
          prop: 'scadaParamEqu',
          label: '设备图纸参数',
          render: (h, row) => {
            return (
              <el-select v-model={row.row.scaleDataIdEquipment} clearable filterable collapse-tags class="monitor-select" style="width:100%;">
                {this.aliasDictList.map((item) => {
                  return <el-option key={item.id} label={item.name} value={item.id}></el-option>
                })}
              </el-select>
            )
          },

          hasJudge: true
        },
        {
          prop: 'scadaParamSystem',
          label: '系统图纸参数',
          render: (h, row) => {
            return (
              <el-select v-model={row.row.scaleDataIdSystem} clearable filterable collapse-tags class="monitor-select" style="width:100%;">
                {this.scadaParameterList.map((item) => {
                  return <el-option key={item.id} label={item.name} value={item.id}></el-option>
                })}
              </el-select>
            )
          },
          hasJudge: true
        },
        {
          prop: 'isShow',
          label: '列表展示',
          render: (h, row) => {
            let isChecked = row.row.isShow === 1; // 判断是否选中
            return (
              <el-checkbox
                v-model={isChecked}
                onChange={(checked) => this.handleCheckboxChange(row, checked)}
              />
            )
          },
          hasJudge: true
        },
        {
          prop: 'operation',
          label: '操作',
          render: (h, row) => {
            return (
              <div class="operationBtn" >
                <span class="operationBtn-span" onClick={() => this.handleParameterEvent(row.row)}>
                  删除
                </span>
              </div>
            )
          },
          hasJudge: this.$route.query.tabActive === '2' ? true : false
        }
      ],
      unselected: false,
      isChecked: true,
      equipmentAssetsDialogData: {},
      // 服务空间
      serviceSpaceDialogShow: false,
      spaceNames: [],
      // 摄像机
      cameraDialogShow: false,
      cameraDialogData: '',
      parameterData: {},
      imsVidiconList: [],
      // 参数别名
      parameterAliasDialogShow: false,
      parameterAliasDialogData: {},
      assetsClickType: '',
      formInline: {
        scaleFolderIdSystem: '',// 系统图纸ID
        scaleImageIdSystem: '',
        scaleFolderIdEquipment: '', // 设备图纸ID
        scaleImageIdEquipment: '',
      },
      systemDrawing: [],//系统图纸
      systemDrawingList: [],
      serviceDrawing: [],//设备图纸
      serviceDrawingList: [],
      formLoading: false,
      scadaParameterList: [],
      scadaProfileList: [],
      aliasDictList: [], // 监测项目别名列表
      isSubmitting: false,
    }
  },
  computed: {
    filteredData() {
      return this.tableData.filter((item, index) => {
        if (index < this.currentStartIndex) {
          return false
        } else if (index > this.currentEndIndex) {
          return false
        } else {
          return true
        }
      })
    }
  },
  watch: {
    $route: {
      handler(val) {
        this.tableData = []
      }
    },
  },
  activated() {
    this.initEvent()
    this.getCascaderListTwo()
  },
  mounted() {
    this.initEvent()
    this.getCascaderListTwo()
  },
  methods: {
    closePage() {
      this.$router.push(this.$route.meta.jumpAddress)
      this.formInline = {
        scaleFolderIdSystem: '',// 系统图纸ID
        scaleImageIdSystem: '',
        scaleFolderIdEquipment: '', // 设备图纸ID
        scaleImageIdEquipment: '',
      }
      this.aliasDictList = []
      this.scadaParameterList = []
    },
    handleInputChange(row, value) {
      this.parameterData = row
      this.parameterAliasDialogShow = true
    },
    // 获取详情初始化
    initEvent() {
      if (this.$route.query.id) {
        this.getSurveyByOne()
      }
    },
    handleCheckboxChange(row, checked) {
      if (checked) {
        if (this.selectedCount >= 4) {
          // 如果超过4个，取消选中
          this.$message.warning('最多只能展示4个选项');
          row.row.isShow = 0;
          return;
        }
        this.selectedCount++;
      } else {
        this.selectedCount--;
      }
      row.row.isShow = checked ? 1 : 0;
    },
    // 获取物联详情
    getSurveyByOne() {
      this.formLoading = true
      this.$api
        .getQueryDataById({
          id: this.$route.query.id
        })
        .then((res) => {
          this.$nextTick(() => {
            this.detailsData = res.data
            const promises = []
            this.tableData = []
            res.data.iotList?.forEach((item, i) => {
              if (item.valueType) {
                item.valueType = JSON.parse(item.valueType)
              }
            })
            this.formInline.scaleFolderIdSystem = res.data.assetsScale.scaleFolderIdSystem
            this.formInline.scaleImageIdSystem = res.data.assetsScale.scaleImageIdSystem
            this.formInline.scaleFolderIdEquipment = res.data.assetsScale.scaleFolderIdEquipment
            this.formInline.scaleImageIdEquipment = res.data.assetsScale.scaleImageIdEquipment
            if (res.data.assetsScale.scaleImageIdSystem) {
              this.getOperationalImage(1, res.data.assetsScale.scaleImageIdSystem)
            }
            if (res.data.assetsScale.scaleImageIdEquipment) {
              this.getOperationalImage(2, res.data.assetsScale.scaleImageIdEquipment)
            }
            if (res.data.assetsScale.scaleFolderIdSystem) {
              this.changeFolderId(1, this.formInline.scaleFolderIdSystem)
            }
            if (res.data.assetsScale.scaleFolderIdEquipment) {
              this.changeFolderId(2, this.formInline.scaleFolderIdEquipment)
            }
            this.tableData = res.data.iotList
            this.spaceNames = res.data.spaceList ?? []
            const imsVidiconId = res.data.cameraList
            const imsVidiconName = res.data.cameraTextList
            this.imsVidiconList =
              imsVidiconId?.map((item, index) => {
                return {
                  imsVidiconId: item,
                  imsVidiconName: imsVidiconName[index]
                }
              }) ?? []
            Promise.all(promises).then((results) => {
              setTimeout(() => {
                this.formLoading = false
              }, 1000)
              // 处理API调用的结果
            }).catch((error) => {
              setTimeout(() => {
                this.formLoading = false
              }, 1000)
              // 处理API调用过程中发生的错误
            })
          })
        })
    },
    // 添加参数
    addrow() {
      this.selectHarvesterDialogData = { eventType: 'add', systemCode: this.$route.query.systemCode }
      this.harvesterDialogShow = true
    },
    // 静态加载更多 table
    handelLoadmore() {
      this.currentStartIndex = currentStartIndex
      this.currentEndIndex = currentEndIndex
    },
    // 物联删除事件
    handleParameterEvent(row) {
      this.tableData = this.tableData.filter((e) => !(e.metadataTag === row.metadataTag && e.factoryCode === row.factoryCode))
      // 重新赋值
      this.tableData = [...this.tableData];
      this.$forceUpdate();
    },
    submitForm() {
      if (this.isSubmitting) {
        this.$message.warning("请等待 5 秒后再提交");
        return; // 如果冷却中，直接返回
      }
      this.$refs.formInline.validate((valid) => {
        if (valid) {
          if (this.tableData.length == 0) {
            this.$message.error('请添加物联信息')
            return
          }
          this.formLoading = true
          let obj = {
            id: this.$route.query.id,
            cameraAssetsIdList: this.imsVidiconList.map(item => {
              return item.imsVidiconId
            }),
            spaceLocationList: this.spaceNames.map(item => {
              return item.id
            }),
            scaleFolderIdSystem: this.formInline.scaleFolderIdSystem,
            scaleImageIdSystem: this.formInline.scaleImageIdSystem,
            scaleFolderIdEquipment: this.formInline.scaleFolderIdEquipment,
            scaleImageIdEquipment: this.formInline.scaleImageIdEquipment,
            iotPropertiesList: this.tableData.map(e => {
              return {
                factoryCode: e.factoryCode,//物联设备sn
                metadataName: e.metadataName,//名称
                metadataNameAlias: e.metadataNameAlias,//别名
                metadataTag: e.metadataTag,//tag
                metadataType: e.metadataType,//类型
                isShow: e.isShow,//是否展示 1 展示 0 不展示
                scaleDataIdEquipment: e.scaleDataIdEquipment, // SCADA图形参数IDS
                scaleDataIdSystem: e.scaleDataIdSystem, // SCADA系统参数IDS
                valueType: typeof e.valueType === 'string' ? e.valueType : JSON.stringify(e.valueType)
              }
            })
          }
          this.isSubmitting = true; // 开始冷却
          this.$api.getOperationalRelate(obj).then((res) => {
            this.formLoading = false
            if (res.code == 200) {
              this.$message.success("配置成功")
              this.$router.push(this.$route.meta.jumpAddress)
            } else {
              this.$message.error(res.message)
            }

          })
          // 设置冷却时间
          setTimeout(() => {
            this.isSubmitting = false; // 5秒后结束冷却
          }, 5000);
        } else {
          return false
        }
      })
    },
    getImageSelectById(id, params) {
      this.$api.getImageSelectById({ id: id }).then((res) => {
        if (res.code == 200) {
          this[params] = res.data
        }
      })
    },
    // 图纸级联数据
    changeFolderId(i, folderId) {
      this.$api.getOperationalFolderId(folderId).then((res) => {
        if (res.code == 200) {
          if (i == 1) {
            this.systemDrawingList = res.data
          } else {
            this.serviceDrawingList = res.data
          }
        }
      })
    },
    changeEntity(index, val) {
      if (index == 1) {
        this.formInline.scaleImageIdSystem = ''
        this.scadaParameterList = []
        this.changeFolderId(index, val)
      } else {
        this.formInline.scaleImageIdEquipment = ''
        this.aliasDictList = []
        this.changeFolderId(index, val)
      }
    },
    changeLevelEntity(index, val) {
      if (index == 1) {
        this.getOperationalImage(index, val)
      } else {
        this.getOperationalImage(index, val)
      }
    },
    // 图纸级联数据
    getCascaderListTwo() {
      let obj = {
        projection: {},
        query: {
          type: "le5le2d"
        },
        type: "",
        userId: 0
      }
      this.$api.getOperationalFolderList(obj).then((res) => {
        if (res.code == 200) {
          this.systemDrawing = res.data.list
          this.serviceDrawing = res.data.list
        }
      })
    },
    // table下图纸数据
    getOperationalImage(index, val) {
      let obj = {
        id: val
      }
      this.$api.getOperationalImageSelectById(obj).then((res) => {
        if (res.code == 200) {
          if (index == 1) {
            this.scadaParameterList = res.data
          } else {
            this.aliasDictList = res.data
          }
        }
      })
    },
    // 摄像机tag事件
    tagHandleClose(id) {
      // 根据id 删除imsVidiconList中的对应项
      const index = this.imsVidiconList.findIndex((e) => e.imsVidiconId === id)
      this.imsVidiconList.splice(index, 1)
    },
    // 服务空间tag事件
    tagSpaceHandleClose(id) {
      // 根据id 删除spaceNames中的对应项
      const index = this.spaceNames.findIndex((e) => e.id === id)
      this.spaceNames.splice(index, 1)
    },
    // 弹窗事件------------start
    // 关联空间
    changeServicesSpeace() {
      this.serviceSpaceDialogShow = true
    },
    // 摄像机
    associatedCamera() {
      this.cameraDialogData = Array.from(this.imsVidiconList, ({ imsVidiconId }) => imsVidiconId).toString()
      this.cameraDialogShow = true
    },
    submitMonitoringParamsGather(data) {
      this.parameterData = {}
      if (data.parameterSetting) {
        this.parameterData.valueType = data.valueType
      }
      this.equipmentAssetsDialogData = {}
      Object.assign(this, data)
    },
    submitHarvesterDialog(data) {
      let newSplitData = []
      let newSetTableData = this.tableData
      const filteredData1 = data.filter(item => item.factoryCode !== undefined);
      filteredData1.forEach((e) => {
        newSetTableData = newSetTableData.filter((item) => {
          const combinedId = `${item.factoryCode}${item.metadataTag}${item.metadataType}`;
          // 对比拼接后的值与 e.id
          return combinedId !== e.id;
        });
        newSplitData.push({
          ...e,
        })
      })
      // 将新的数据与旧的数据合并
      let concatData = [...newSplitData, ...newSetTableData]
      // 将合并后的数据赋值给tableData
      this.tableData = concatData
      this.closeHarvesterDialog()
    },
    closeHarvesterDialog() {
      this.harvesterDialogShow = false
    }
    // 弹窗事件------------end
  }
}
</script>
<style lang="scss" scoped>
.footer-role ::v-deep .el-input__inner {
  padding-right: 50px;
}

.form-content {
  height: calc(100% - 0px);
  overflow-y: auto;
  background: #fff;

  .heade-info {
    padding-left: 16px;

    .heade-p {
      display: inline-block;
      width: 25%;
      font-size: 14px;
      line-height: 35px;
    }
  }

  .form-title {
    height: 40px;
    line-height: 40px;
    background: #fff;
    border-bottom: 1px solid #dcdfe6;
    font-size: 15px;
    font-family: "PingFang SC-Medium", "PingFang SC";
    font-weight: 500;
    color: #121f3e;
    padding-left: 15px;
  }

  .box-card {
    padding-left: 3%;
    margin-bottom: 0px;
  }

  .form-btn-title {
    font-size: 14px;
    font-family: "PingFang SC-Regular", "PingFang SC";
    font-weight: 400;
    color: #121f3e;
    margin-right: 8px;
  }

  .form-btn-btn {
    padding: 4px 10px;
    margin: 0 8px;
  }

  .assets-info {
    font-size: 14px;
    font-family: "PingFang SC-Regular", "PingFang SC";
    font-weight: 400;
    color: #121f3e;
    margin-right: 15px;

    >span {
      color: #3562db;
    }
  }

  .assets-info-close {
    cursor: pointer;

    &:hover {
      color: #3562db;
      font-weight: 600;
    }
  }

  .parameter-box {
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    margin-bottom: 20px;

    .parameter-title {
      font-size: 14px;
      font-family: "PingFang SC-Regular", "PingFang SC";
      font-weight: 400;
      color: #121f3e;
      background: #faf9fc;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 48px;
      line-height: 48px;
      padding: 0 10px;

      &>span {
        &:first-child {
          font-size: 16px;
        }
      }
    }

    .unit-style {
      font-size: 14px;
      font-family: "PingFang SC-Regular", "PingFang SC";
      font-weight: 400;
      color: #121f3e;
      height: 40px;
      line-height: 40px;
    }
  }

  ::v-deep .el-select,
  .el-input {
    width: fit-content;
  }

  .camera-tag {
    background: #f6f5fa;
    border-radius: 4px;
    font-size: 14px;
    font-family: "PingFang SC-Regular", "PingFang SC";
    font-weight: 400;
    color: #121f3e;
    border: none;
    margin-right: 8px;

    ::v-deep .el-tag__close {
      color: #121f3e;

      &:hover {
        color: #fff;
        background-color: #3562db;
      }
    }
  }
}

.hidden {
  display: none
}
</style>
