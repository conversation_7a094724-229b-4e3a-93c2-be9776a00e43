import Layout from '@/layout'
import EmptyLayout from '@/layout/empty'
export default [
  {
    path: '/wasteManage',
    component: Layout,
    redirect: '/wasteManage/homePage',
    name: 'wasteManage',
    meta: {
      title: '医疗废物管理',
      menuAuth: '/wasteManage'
    },
    children: [
      {
        path: 'homePage',
        component: EmptyLayout,
        redirect: { name: 'homePage' },
        meta: {
          title: '首页',
          menuAuth: '/wasteManage/homePage'
        },
        children: [
          {
            path: '',
            name: 'homePage',
            component: () => import('@/views/operationPort/wasteManage/homePage.vue'),
            meta: {
              title: '首页',
              sidebar: false,
              breadcrumb: false,
              auth: []
            }
          }
        ]
      },
      {
        path: 'inoutManage',
        component: EmptyLayout,
        redirect: { name: 'collectionRecord' },
        meta: {
          title: '出入库管理',
          menuAuth: '/wasteManage/inoutManage'
        },
        children: [
          {
            path: 'collectionRecord',
            component: EmptyLayout,
            redirect: { name: 'collectionRecord' },
            meta: {
              title: '出入库管理',
              menuAuth: '/inoutManage/collectionRecord'
            },
            children: [
              {
                path: '',
                name: 'collectionRecord',
                component: () => import('@/views/operationPort/wasteManage/collectionRecord.vue'),
                meta: {
                  title: '收集记录',
                  sidebar: false,
                  breadcrumb: false,
                  auth: []
                }
              },
              {
                path: '/wasteManage/inoutManage/manualEntry',
                name: 'manualEntry',
                component: () => import('@/views/operationPort/wasteManage/manualEntry.vue'),
                meta: {
                  title: '手动录入',
                  sidebar: false,
                  breadcrumb: false,
                  activeMenu: '/wasteManage/inoutManage/collectionRecord'
                }
              },
              {
                path: '/wasteManage/inoutManage/batchImport',
                name: 'batchImport',
                component: () => import('@/views/operationPort/wasteManage/batchImport.vue'),
                meta: {
                  title: '批量导入',
                  sidebar: false,
                  breadcrumb: false,
                  activeMenu: '/wasteManage/inoutManage/collectionRecord'
                }
              }
            ]
          },
          {
            path: 'inboundRecord',
            component: EmptyLayout,
            redirect: { name: 'inboundRecord' },
            meta: {
              title: '入站记录',
              menuAuth: '/inoutManage/inboundRecord'
            },
            children: [
              {
                path: '',
                name: 'inboundRecord',
                component: () => import('@/views/operationPort/wasteManage/inboundRecord.vue'),
                meta: {
                  title: '入站记录',
                  sidebar: false,
                  breadcrumb: false,
                  auth: []
                }
              }
            ]
          },
          {
            path: 'outboundRecord',
            component: EmptyLayout,
            redirect: { name: 'outboundRecord' },
            meta: {
              title: '出站记录',
              menuAuth: '/inoutManage/outboundRecord'
            },
            children: [
              {
                path: '',
                name: 'outboundRecord',
                component: () => import('@/views/operationPort/wasteManage/outboundRecord.vue'),
                meta: {
                  title: '出站记录',
                  sidebar: false,
                  breadcrumb: false,
                  auth: []
                }
              }
            ]
          }
        ]
      },
      {
        path: 'statisticalAnalysisManagement',
        component: EmptyLayout,
        redirect: { name: 'statement' },
        meta: {
          title: '统计分析管理',
          menuAuth: '/wasteManage/statisticalAnalysisManagement'
        },
        children: [
          {
            path: 'statement',
            component: EmptyLayout,
            redirect: { name: 'statement' },
            meta: {
              title: '出入库管理',
              menuAuth: '/statisticalAnalysisManagement/statement'
            },
            children: [
              {
                path: '',
                name: 'statement',
                component: () => import('@/views/operationPort/wasteManage/statement.vue'),
                meta: {
                  title: '综合报表',
                  sidebar: false,
                  breadcrumb: false,
                  auth: []
                }
              }
            ]
          }
        ]
      }
    ]
  },
  {
    path: '/organizationManage',
    component: Layout,
    redirect: '/organizationManage/index',
    name: 'organizationManage',
    meta: {
      title: '组织管理',
      menuAuth: '/organizationManage'
    },
    children: [
      {
        path: 'unitManage',
        name: 'organizationManage/unitManage',
        component: EmptyLayout,
        redirect: { name: 'unitManage' },
        meta: {
          title: '单位管理',
          menuAuth: '/organizationManage/unitManage'
        },
        children: [
          {
            path: '',
            name: 'unitManage',
            component: () => import('@/views/foundation/organizationManage/unitManage.vue'),
            meta: {
              title: '单位管理',
              sidebar: false,
              breadcrumb: false,
              auth: []
            }
          },
          {
            path: '/companyMess',
            name: 'companyMess',
            component: () => import('@/views/foundation/organizationManage/components/companyMess.vue'),
            meta: {
              title: '单位详情',
              sidebar: false,
              activeMenu: '/organizationManage/unitManage'
            }
          },
          {
            path: '/detailedInformation',
            name: 'detailedInformation',
            component: () => import('@/views/foundation/organizationManage/components/detailedInformation.vue'),
            meta: {
              title: '下属列表',
              sidebar: false,
              activeMenu: '/organizationManage/unitManage'
            }
          }
        ]
      },
      {
        path: 'departmentManage',
        component: EmptyLayout,
        redirect: { name: 'departmentManage' },
        meta: {
          title: '部门管理',
          menuAuth: '/organizationManage/departmentManage'
        },
        children: [
          {
            path: '',
            name: 'departmentManage',
            component: () => import('@/views/foundation/organizationManage/departmentManage.vue'),
            meta: {
              title: '部门管理',
              sidebar: false,
              breadcrumb: false,
              auth: []
            }
          },
          {
            path: '/depetMess',
            name: 'depetMess',
            component: () => import('@/views/foundation/organizationManage/components/depetMess.vue'),
            meta: {
              title: '',
              sidebar: false,
              breadcrumb: false,
              auth: []
            }
          }
        ]
      },
      {
        path: 'personnelManage',
        component: EmptyLayout,
        redirect: { name: 'personnelManage' },
        meta: {
          title: '人员管理',
          menuAuth: '/organizationManage/personnelManage'
        },
        children: [
          {
            path: '',
            name: 'personnelManage',
            component: () => import('@/views/foundation/organizationManage/personnelManage.vue'),
            meta: {
              title: '人员管理',
              sidebar: false,
              breadcrumb: false,
              auth: []
            }
          },
          {
            path: '/staffMess',
            name: 'staffMess',
            component: () => import('@/views/foundation/organizationManage/components/staffMess.vue'),
            meta: {
              title: '',
              sidebar: false,
              breadcrumb: false,
              auth: []
            }
          }
        ]
      }
    ]
  },
  {
    path: '/parkingManage',
    component: Layout,
    // redirect: '/parkingManage/parkingOverview',
    name: 'parkingManage',
    meta: {
      title: '停车场管理',
      menuAuth: '/parkingManage'
    },
    children: [
      {
        path: 'parkingOverview',
        component: EmptyLayout,
        meta: {
          title: '运营总览',
          menuAuth: '/parkingManage/parkingOverview'
        },
        children: [
          {
            path: '',
            name: 'parkingOverview',
            component: () => import('@/views/microApp/index.vue'),
            // component: () => import('@/views/operationPort/parkingManage/parkingOverview.vue'),
            meta: {
              title: '运营总览',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'entryRecordManage',
        component: EmptyLayout,
        meta: {
          title: '入场记录管理',
          menuAuth: '/parkingManage/entryRecordManage'
        },
        children: [
          {
            path: '',
            name: 'entryRecordManage',
            component: () => import('@/views/microApp/index.vue'),
            // component: () => import('@/views/operationPort/parkingManage/entryRecordManage.vue'),
            meta: {
              title: '入场记录管理',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'exitRecordManage',
        component: EmptyLayout,
        meta: {
          title: '出场记录管理',
          menuAuth: '/parkingManage/exitRecordManage'
        },
        children: [
          {
            path: '',
            name: 'exitRecordManage',
            component: () => import('@/views/microApp/index.vue'),
            // component: () => import('@/views/operationPort/parkingManage/exitRecordManage.vue'),
            meta: {
              title: '出场记录管理',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'reverseSearch',
        component: EmptyLayout,
        meta: {
          title: '反向寻车',
          menuAuth: '/parkingManage/reverseSearch'
        },
        children: [
          {
            path: '',
            name: 'reverseSearch',
            component: () => import('@/views/microApp/index.vue'),
            // component: () => import('@/views/operationPort/parkingManage/exitRecordManage.vue'),
            meta: {
              title: '反向寻车',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'stallManage',
        component: EmptyLayout,
        meta: {
          title: '车位管理',
          menuAuth: '/parkingManage/stallManage'
        },
        children: [
          {
            path: '',
            name: 'stallManage',
            component: () => import('@/views/microApp/index.vue'),
            // component: () => import('@/views/operationPort/parkingManage/exitRecordManage.vue'),
            meta: {
              title: '车位管理',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      }
    ]
  },
  {
    path: '/planManage',
    component: Layout,
    redirect: '/planManage/planTemplate',
    name: 'planManage',
    meta: {
      title: '预案管理',
      menuAuth: '/planManage'
    },
    children: [
      {
        path: 'planTemplate',
        component: EmptyLayout,
        redirect: { name: 'planTemplate' },
        meta: {
          title: '预案模板',
          menuAuth: '/planManage/planTemplate'
        },
        children: [
          {
            path: '',
            name: 'planTemplate',
            component: () => import('@/views/operationPort/planManage/planTemplate/index.vue'),
            meta: {
              title: '预案模板',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'createTemplate',
            name: 'createTemplate',
            component: () => import('@/views/operationPort/planManage/planTemplate/createTemplate/index.vue'),
            meta: {
              title: '创建智能预案模版',
              sidebar: false,
              activeMenu: '/planManage/planTemplate'
            }
          },
          {
            path: 'templateDetails',
            name: 'templateDetails',
            component: () => import('@/views/operationPort/planManage/planTemplate/templateDetails/index.vue'),
            meta: {
              title: '预案模型详情',
              sidebar: false,
              activeMenu: '/planManage/planTemplate'
            }
          }
        ]
      },
      {
        path: 'planList',
        component: EmptyLayout,
        redirect: { name: 'planList' },
        meta: {
          title: '预案列表',
          menuAuth: '/planManage/planList'
        },
        children: [
          {
            path: '',
            name: 'planList',
            component: () => import('@/views/operationPort/planManage/planList/index.vue'),
            meta: {
              title: '预案列表',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'createPlan',
            name: 'createPlan',
            component: () => import('@/views/operationPort/planManage/planList/createPlan/index.vue'),
            meta: {
              title: '创建智能预案',
              sidebar: false,
              activeMenu: '/planManage/planList'
            }
          },
          {
            path: 'planDetails',
            name: 'planDetails',
            component: () => import('@/views/operationPort/planManage/planList/planDetails/index.vue'),
            meta: {
              title: '预案详情',
              sidebar: false,
              activeMenu: '/planManage/planList'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/exerciseManage',
    component: Layout,
    redirect: '/exerciseManage/exerciseStatistics',
    name: 'exerciseManage',
    meta: {
      title: '演习管理',
      menuAuth: '/exerciseManage'
    },
    children: [
      {
        path: 'exerciseStatistics',
        component: EmptyLayout,
        redirect: { name: 'exerciseStatistics' },
        meta: {
          title: '演练统计',
          menuAuth: '/exerciseManage/exerciseStatistics'
        },
        children: [
          {
            path: '',
            name: 'exerciseStatistics',
            component: () => import('@/views/operationPort/exerciseManage/exerciseStatistics/index.vue'),
            meta: {
              title: '演练统计',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/exerciseManage'
            }
          }
        ]
      },
      {
        path: 'exercisePlan',
        component: EmptyLayout,
        redirect: { name: 'exercisePlan' },
        meta: {
          title: '演练计划',
          menuAuth: '/exerciseManage/exercisePlan'
        },
        children: [
          {
            path: 'exercisePlan',
            name: 'exercisePlan',
            component: () => import('@/views/operationPort/exerciseManage/exercisePlan/index.vue'),
            meta: {
              title: '演练计划',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/exerciseManage'
            }
          },
          {
            path: 'addExercisePlan',
            name: 'addExercisePlan',
            component: () => import('@/views/operationPort/exerciseManage/exercisePlan/addExercisePlan.vue'),
            meta: {
              title: '新增计划',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/exerciseManage'
            }
          }
        ]
      },
      {
        path: 'exerciseTask',
        component: EmptyLayout,
        redirect: { name: 'exerciseTask' },
        meta: {
          title: '演练任务',
          menuAuth: '/exerciseManage/exerciseTask'
        },
        children: [
          {
            path: '',
            name: 'exerciseTask',
            component: () => import('@/views/operationPort/exerciseManage/exerciseTask/index.vue'),
            meta: {
              title: '演练任务',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/exerciseManage'
            }
          },
          {
            path: 'additionalRecording',
            name: 'additionalRecording',
            component: () => import('@/views/operationPort/exerciseManage/exerciseTask/additionalRecording.vue'),
            meta: {
              title: '任务补录',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/exerciseManage'
            }
          },
          {
            path: 'exerciseTaskDetail',
            name: 'exerciseTaskDetail',
            component: () => import('@/views/operationPort/exerciseManage/exerciseTask/taskDetail.vue'),
            meta: {
              title: '任务详情',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/exerciseManage'
            }
          },
          {
            path: 'exerciseTaskAssess',
            name: 'exerciseTaskAssess',
            component: () => import('@/views/operationPort/exerciseManage/exerciseTask/taskAssess.vue'),
            meta: {
              title: '任务评定',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/exerciseManage'
            }
          }
        ]
      },
      {
        path: 'exerciseType',
        component: EmptyLayout,
        redirect: { name: 'exerciseType' },
        meta: {
          title: '演练类型',
          menuAuth: '/exerciseManage/exerciseType'
        },
        children: [
          {
            path: '',
            name: 'exerciseType',
            component: () => import('@/views/operationPort/exerciseManage/exerciseType/index.vue'),
            meta: {
              title: '演练类型',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/exerciseManage'
            }
          }
        ]
      },
      {
        path: 'generalBranchManage',
        component: EmptyLayout,
        redirect: { name: 'generalBranchManage' },
        meta: {
          title: '总支管理',
          menuAuth: '/exerciseManage/generalBranchManage'
        },
        children: [
          {
            path: '',
            name: 'generalBranchManage',
            component: () => import('@/views/operationPort/exerciseManage/generalBranchManage/index.vue'),
            meta: {
              title: '总支管理',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/exerciseManage'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/supplierManage',
    component: Layout,
    redirect: { name: 'supplierList' },
    name: 'supplierManage',
    meta: {
      title: '供应商管理',
      menuAuth: '/supplierManage'
    },
    children: [
      {
        path: 'supplier',
        name: 'supplierList',
        component: () => import('@/views/operationPort/supplierManage/supplier/SupplierList.vue'),
        meta: {
          title: '供应商列表',
          menuAuth: '/supplierManage/supplier'
        }
      }
    ]
  },
  {
    path: '/projectManage',
    component: Layout,
    name: 'projectManage',
    redirect: '/projectManage/myProject',
    meta: {
      title: '工程管理',
      menuAuth: '/projectManage'
    },
    children: [
      {
        path: 'myProject',
        component: EmptyLayout,
        redirect: { name: 'myProject' },
        meta: {
          title: '我的项目',
          menuAuth: '/projectManage/myProject'
        },
        children: [
          {
            path: '',
            name: 'myProject',
            component: () => import('@/views/microApp/index.vue'),
            meta: {
              title: '我的项目',
              sidebar: false,
              breadcrumb: false,
              auth: ['myProject:create']
            }
          }
        ]
      },
      {
        path: 'allProject',
        component: EmptyLayout,
        redirect: { name: 'allProject' },
        meta: {
          title: '全部项目',
          menuAuth: '/projectManage/allProject'
        },
        children: [
          {
            path: '',
            name: 'allProject',
            component: () => import('@/views/microApp/index.vue'),
            meta: {
              title: '全部项目',
              sidebar: false,
              breadcrumb: false,
              auth: ['allProject:create', 'allProject:handle', 'allProject:aheadWork', 'allProject:addRecord', 'allProject:setTag', 'allProject:addRemark']
            }
          }
        ]
      },
      {
        path: 'addProject',
        component: EmptyLayout,
        redirect: { name: 'allProject' },
        meta: {
          title: '新建项目',
          menuAuth: '/projectManage/allProject-none' // 为了隐藏
        },
        children: [
          {
            path: '',
            name: 'allProject',
            component: () => import('@/views/microApp/index.vue'),
            meta: {
              title: '新建项目',
              sidebar: false,
              breadcrumb: false,
              auth: []
            }
          }
        ]
      },
      {
        path: 'projectDetail',
        component: EmptyLayout,
        // redirect: { name: 'allProject' },
        meta: {
          title: '项目详情',
          menuAuth: '/projectManage/allProject-none' // 为了隐藏
        },
        children: [
          {
            path: '',
            name: 'projectDetail',
            component: () => import('@/views/microApp/index.vue'),
            meta: {
              title: '项目详情',
              sidebar: false,
              breadcrumb: false,
              auth: []
            }
          }
        ]
      },
      {
        path: 'retentionMoneyManager',
        component: EmptyLayout,
        // redirect: { name: 'allProject' },
        meta: {
          title: '质保金管理',
          menuAuth: '/projectManage/retentionMoneyManager'
        },
        children: [
          {
            path: '',
            name: 'retentionMoneyManager',
            component: () => import('@/views/microApp/index.vue'),
            meta: {
              title: '质保金管理',
              sidebar: false,
              breadcrumb: false,
              auth: []
            }
          }
        ]
      },
      {
        path: 'paymentOrder',
        component: () => import('@/views/operationPort/projectManage/paymentOrder.vue'),
        meta: {
          title: '支付申请单',
          menuAuth: '/projectManage/paymentOrder'
        }
      },
      {
        path: 'statisticAnalysis',
        component: () => import('@/views/operationPort/projectManage/StatisticAnalysis.vue'),
        meta: {
          title: '统计分析',
          menuAuth: '/projectManage/statisticAnalysis'
        }
      },
      {
        path: 'projectConfig',
        component: () => import('@/views/operationPort/projectManage/ProjectConfig.vue'),
        meta: {
          title: '功能设置',
          menuAuth: '/projectManage/projectConfig'
        }
      },
      {
        path: 'dictionary',
        name: 'dictionaryList',
        component: () => import('@/views/operationPort/supplierManage/dictionary/DictionaryList.vue'),
        meta: {
          title: '字典管理',
          activeMenu: '/projectManage/dictionary'
        }
      },
      {
        path: ':page*',
        name: 'microApp',
        component: () => import('@/views/microApp/index.vue')
      }
    ]
  },
  // 配件管理
  {
    path: '/secondaryLibrary',
    component: Layout,
    name: 'secondaryLibrary',
    meta: {
      title: '配件管理',
      menuAuth: '/secondaryLibrary'
    },
    children: [
      {
        path: 'inventoryManagement',
        component: EmptyLayout,
        meta: {
          title: '库存管理',
          menuAuth: '/secondaryLibrary/inventoryManagement'
        },
        children: [
          {
            path: '',
            name: 'inventoryManagement',
            component: () => import('@/views/operationPort/secondaryLibrary/inventoryManagement.vue'),
            meta: {
              title: '库存管理',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'inventoryDetails',
        component: EmptyLayout,
        meta: {
          title: '入库明细',
          menuAuth: '/secondaryLibrary/inventoryDetails'
        },
        children: [
          {
            path: '',
            name: 'inventoryDetails',
            component: () => import('@/views/operationPort/secondaryLibrary/inventoryDetails.vue'),
            meta: {
              title: '入库明细',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'outboundDetails',
        component: EmptyLayout,
        meta: {
          title: '出库明细',
          menuAuth: '/secondaryLibrary/outboundDetails'
        },
        children: [
          {
            path: '',
            name: 'outboundDetails',
            component: () => import('@/views/operationPort/secondaryLibrary/outboundDetails.vue'),
            meta: {
              title: '出库明细',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'inventoryWarning',
        component: EmptyLayout,
        meta: {
          title: '库存预警',
          menuAuth: '/secondaryLibrary/inventoryWarning'
        },
        children: [
          {
            path: '',
            name: 'inventoryWarning',
            component: () => import('@/views/operationPort/secondaryLibrary/inventoryWarning.vue'),
            meta: {
              title: '库存预警',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'comprehensiveStatistics',
        component: EmptyLayout,
        meta: {
          title: '综合统计',
          menuAuth: '/secondaryLibrary/comprehensiveStatistics'
        },
        children: [
          {
            path: '',
            name: 'ComprehensiveStatistics',
            component: () => import('@/views/operationPort/secondaryLibrary/comprehensiveStatistics/index.vue'),
            meta: {
              title: '综合统计',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      }
    ]
  },
  // 业务管理
  {
    path: '/contractManagement',
    component: Layout,
    redirect: '/contractManagement/myContract',
    name: 'contractManagement',
    meta: {
      title: '合同管理',
      menuAuth: '/contractManagement'
    },
    children: [
      {
        path: 'myContract',
        component: EmptyLayout,
        redirect: { name: 'myContract' },
        meta: {
          title: '我的合同',
          menuAuth: '/contractManagement/myContract'
        },
        children: [
          {
            path: '',
            name: 'myContract',
            component: () => import('@/views/operationPort/contractManagement/myContract/index.vue'),
            meta: {
              sidebar: false,
              breadcrumb: false,
              auth: []
            }
          },
          {
            path: '/dossierManager/myCreateContract',
            name: 'myCreateContract',
            component: () => import('@/views/operationPort/contractManagement/myContract/add.vue'),
            meta: {
              sidebar: false,
              breadcrumb: false,
              auth: []
            }
          },
          {
            path: '/dossierManager/myContractDetails',
            name: 'myContractDetails',
            component: () => import('@/views/operationPort/contractManagement/myContract/details.vue'),
            meta: {
              sidebar: false,
              breadcrumb: false,
              auth: []
            }
          }
        ]
      },
      {
        path: 'allContract',
        component: EmptyLayout,
        redirect: { name: 'allContract' },
        meta: {
          title: '全部合同',
          menuAuth: '/contractManagement/allContract'
        },
        children: [
          {
            path: '',
            name: 'allContract',
            component: () => import('@/views/operationPort/contractManagement/allContract/index.vue'),
            meta: {
              sidebar: false,
              breadcrumb: false,
              auth: []
            }
          },
          {
            path: '/dossierManager/allCreateContract',
            name: 'allCreateContract',
            component: () => import('@/views/operationPort/contractManagement/allContract/add.vue'),
            meta: {
              sidebar: false,
              breadcrumb: false,
              auth: []
            }
          },
          {
            path: '/dossierManager/allContractDetails',
            name: 'allContractDetails',
            component: () => import('@/views/operationPort/contractManagement/allContract/details.vue'),
            meta: {
              sidebar: false,
              breadcrumb: false,
              auth: []
            }
          }
        ]
      },
      {
        path: 'dataStatistics',
        component: EmptyLayout,
        redirect: { name: 'dataStatistics' },
        meta: {
          title: '数据统计',
          menuAuth: '/contractManagement/dataStatistics'
        },
        children: [
          {
            path: '',
            name: 'dataStatistics',
            component: () => import('@/views/operationPort/contractManagement/dataStatistics/index.vue'),
            meta: {
              sidebar: false,
              breadcrumb: false,
              auth: []
            }
          }
        ]
      }
    ]
  },
  // 档案管理
  {
    path: '/dossierManager',
    component: Layout,
    redirect: '/dossierManager/myDossier',
    name: 'dossierManager',
    meta: {
      title: '档案管理',
      menuAuth: '/dossierManager'
    },
    children: [
      {
        path: 'myDossier',
        component: EmptyLayout,
        redirect: { name: 'myDossier' },
        meta: {
          title: '我的文档',
          menuAuth: '/dossierManager/myDossier'
        },
        children: [
          {
            path: '',
            name: 'myDossier',
            component: () => import('@/views/operationPort/dossierManager/myDossier/index.vue'),
            meta: {
              sidebar: false,
              breadcrumb: false,
              auth: []
            }
          },
          {
            path: '/dossierManager/myCreateDocuments',
            name: 'myCreateDocuments',
            component: () => import('@/views/operationPort/dossierManager/myDossier/createDocuments.vue'),
            meta: {
              sidebar: false,
              breadcrumb: false,
              auth: []
            }
          },
          {
            path: '/dossierManager/myDocumentDetails',
            name: 'myDocumentDetails',
            component: () => import('@/views/operationPort/dossierManager/myDossier/details.vue'),
            meta: {
              sidebar: false,
              breadcrumb: false,
              auth: []
            }
          }
        ]
      },
      {
        path: 'allDossier',
        component: EmptyLayout,
        redirect: { name: 'allDossier' },
        meta: {
          title: '全部文档',
          menuAuth: '/dossierManager/allDossier'
        },
        children: [
          {
            path: '',
            name: 'allDossier',
            component: () => import('@/views/operationPort/dossierManager/allDossier/index.vue'),
            meta: {
              sidebar: false,
              breadcrumb: false,
              auth: []
            }
          },
          {
            path: '/dossierManager/allCreateDocuments',
            name: 'allCreateDocuments',
            component: () => import('@/views/operationPort/dossierManager/allDossier/createDocuments.vue'),
            meta: {
              sidebar: false,
              breadcrumb: false,
              auth: []
            }
          },
          {
            path: '/dossierManager/allDocumentDetails',
            name: 'allDocumentDetails',
            component: () => import('@/views/operationPort/dossierManager/allDossier/details.vue'),
            meta: {
              sidebar: false,
              breadcrumb: false,
              auth: []
            }
          }
        ]
      }
    ]
  },
  {
    path: '/projectConfigManage',
    component: Layout,
    redirect: { name: 'projectConfigDictManage' },
    name: 'projectConfigManage',
    meta: {
      title: '配置管理',
      menuAuth: '/projectConfigManage'
    },
    children: [
      {
        path: 'projectConfigDictManage',
        name: 'projectConfigDictManage',
        component: () => import('@/views/operationPort/projectConfigManage/projectConfigDictManage/index.vue'),
        meta: {
          title: '字典管理',
          menuAuth: '/projectConfigManage/projectConfigDictManage'
        }
      }
    ]
  },
  // 班车管理
  {
    path: '/shuttleBusManage',
    component: Layout,
    redirect: { name: 'busRecord' },
    name: 'shuttleBusManage',
    meta: {
      title: '配置管理',
      menuAuth: '/shuttleBusManage'
    },
    children: [
      {
        path: 'busRecord',
        name: 'busRecord',
        component: () => import('@/views/operationPort/shuttleBusManage/busRecord/index.vue'),
        meta: {
          title: '班车记录',
          menuAuth: '/shuttleBusManage/busRecord'
        }
      },
      {
        path: 'trainConfiguration',
        name: 'trainConfiguration',
        component: () => import('@/views/operationPort/shuttleBusManage/trainConfiguration/index.vue'),
        meta: {
          title: '车次配置',
          menuAuth: '/shuttleBusManage/trainConfiguration'
        }
      },
      {
        path: 'busRoadmap',
        name: 'busRoadmap',
        component: () => import('@/views/operationPort/shuttleBusManage/busRoadmap/index.vue'),
        meta: {
          title: '路线管理',
          menuAuth: '/shuttleBusManage/busRoadmap'
        }
      }
    ]
  },
  // 岗位管理
  {
    path: '/postManage',
    component: Layout,
    name: 'postManage',
    redirect: '/postManage/memberInfoManage',
    meta: {
      title: '岗位管理',
      menuAuth: '/postManage'
    },
    children: [
      {
        path: 'memberInfoManage',
        component: EmptyLayout,
        redirect: { name: 'memberInfoManage' },
        meta: {
          title: '成员信息管理',
          menuAuth: '/postManage/memberInfoManage'
        },
        children: [
          {
            path: '',
            name: 'memberInfoManage',
            component: () => import('@/views/operationPort/postManage/memberInfoManage/index.vue'),
            meta: {
              title: '成员信息管理',
              sidebar: false,
              breadcrumb: false,
              menuAuth: '/postManage/memberInfoManage',
              auth: ['memberInfoManage:add', 'memberInfoManage:edit', 'memberInfoManage:delete', 'memberInfoManage:import', 'memberInfoManage:export']
            }
          },
          {
            path: 'memberAdd',
            name: 'memberAdd',
            component: () => import('@/views/operationPort/postManage/memberInfoManage/addMember.vue'),
            meta: {
              title: '新增成员',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/postManage/memberInfoManage'
            }
          },
          {
            path: 'memberDetail',
            name: 'memberDetail',
            component: () => import('@/views/operationPort/postManage/memberInfoManage/memberDetail.vue'),
            meta: {
              title: '成员详情',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/postManage/memberInfoManage'
            }
          }
        ]
      },
      {
        path: 'positionManage',
        component: EmptyLayout,
        redirect: { name: 'positionManage' },
        meta: {
          title: '岗位管理',
          menuAuth: '/postManage/positionManage'
        },
        children: [
          {
            path: '',
            name: 'positionManage',
            component: () => import('@/views/operationPort/postManage/positionManage/index.vue'),
            meta: {
              title: '岗位管理',
              sidebar: false,
              breadcrumb: false,
              menuAuth: '/postManage/positionManage',
              auth: ['positionManage:add', 'positionManage:edit', 'positionManage:delete', 'positionManage:addPerson']
            }
          },
          {
            path: 'positionAdd',
            name: 'positionAdd',
            component: () => import('@/views/operationPort/postManage/positionManage/addPosition.vue'),
            meta: {
              title: '新建岗位',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/postManage/positionManage'
            }
          }
        ]
      },
      {
        path: 'shiftPostManage',
        component: EmptyLayout,
        redirect: { name: 'shiftPostManage' },
        meta: {
          title: '值班岗管理',
          menuAuth: '/postManage/shiftPostManage'
        },
        children: [
          {
            path: '',
            name: 'shiftPostManage',
            component: () => import('@/views/operationPort/postManage/shiftPostManage/index.vue'),
            meta: {
              title: '值班岗管理',
              sidebar: false,
              breadcrumb: false,
              menuAuth: '/postManage/shiftPostManage',
              auth: ['shiftPostManage:add', 'shiftPostManage:edit', 'shiftPostManage:delete', 'shiftPostManage:addPerson']
            }
          },
          {
            path: 'shiftPostAdd',
            name: 'shiftPostAdd',
            component: () => import('@/views/operationPort/postManage/shiftPostManage/addShiftPost.vue'),
            meta: {
              title: '新建值班岗',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/postManage/shiftPostManage'
            }
          }
        ]
      },
      {
        path: 'leavePostRecord',
        component: EmptyLayout,
        redirect: { name: 'leavePostRecord' },
        meta: {
          title: '离岗记录',
          menuAuth: '/postManage/leavePostRecord'
        },
        children: [
          {
            path: '',
            name: 'leavePostRecord',
            component: () => import('@/views/operationPort/postManage/leavePostRecord/index.vue'),
            meta: {
              title: '离岗记录',
              sidebar: false,
              breadcrumb: false,
              menuAuth: '/postManage/leavePostRecord',
              auth: [
                // 'leavePostRecord:add',
                // 'leavePostRecord:edit',
                // 'leavePostRecord:delete',
                // 'leavePostRecord:addPerson',
              ]
            }
          }
          // {
          //   path: 'shiftPostAdd',
          //   name: 'shiftPostAdd',
          //   component: () => import('@/views/operationPort/postManage/shiftPostManage/addShiftPost.vue'),
          //   meta: {
          //     title: '新建值班岗',
          //     sidebar: false,
          //     breadcrumb: false,
          //     activeMenu: '/postManage/shiftPostManage'
          //   }
          // }
        ]
      },
      {
        path: 'positioningTerminalManage',
        component: EmptyLayout,
        redirect: { name: 'positioningTerminalManage' },
        meta: {
          title: '定位终端管理',
          menuAuth: '/postManage/positioningTerminalManage'
        },
        children: [
          {
            path: '',
            name: 'positioningTerminalManage',
            component: () => import('@/views/operationPort/postManage/positioningTerminalManage/index.vue'),
            meta: {
              title: '值班岗管理',
              sidebar: false,
              breadcrumb: false,
              menuAuth: '/postManage/positioningTerminalManage',
              auth: ['positioningTerminalManage:add', 'positioningTerminalManage:edit', 'positioningTerminalManage:delete', 'positioningTerminalManage:addPerson']
            }
          }
        ]
      },
      {
        path: 'postSOP',
        component: EmptyLayout,
        redirect: { name: 'postSOP' },
        meta: {
          title: '岗位SOP',
          menuAuth: '/postManage/postSOP'
        },
        children: [
          {
            path: '',
            name: 'postSOP',
            component: () => import('@/views/operationPort/postManage/postSOP/index.vue'),
            meta: {
              title: '岗位SOP',
              sidebar: false,
              breadcrumb: false,
              menuAuth: '/postManage/postSOP'
              // auth: ['postSOP:add', 'postSOP:edit', 'postSOP:delete', 'postSOP:view']
            }
          },
          {
            path: 'addPostSOP',
            name: 'addPostSOP',
            component: () => import('@/views/operationPort/postManage/postSOP/addPostSOP.vue'),
            meta: {
              title: '新建岗位SOP',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/postManage/postSOP'
            }
          },
          {
            path: 'postSOPDetail',
            name: 'postSOPDetail',
            component: () => import('@/views/operationPort/postManage/postSOP/postSOPDetail.vue'),
            meta: {
              title: '岗位SOP详情',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/postManage/postSOP'
            }
          }
        ]
      }
    ]
  },
  // 值班管理
  {
    path: '/shiftManage',
    component: Layout,
    name: 'shiftManage',
    redirect: '/shiftManage/dataBoard',
    meta: {
      title: '值班管理',
      menuAuth: '/shiftManage'
    },
    children: [
      {
        path: 'dataBoard',
        component: EmptyLayout,
        redirect: { name: 'dataBoard' },
        meta: {
          title: '数据看板',
          menuAuth: '/shiftManage/dataBoard'
        },
        children: [
          {
            path: '',
            name: 'memberInfoManage',
            component: () => import('@/views/operationPort/shiftManage/dataBoard/index.vue'),
            meta: {
              title: '数据看板',
              sidebar: false,
              breadcrumb: false,
              menuAuth: '/shiftManage/dataBoard'
            }
          },
          {
            path: 'postDetail',
            name: 'postDetail',
            component: () => import('@/views/operationPort/shiftManage/dataBoard/postDetail.vue'),
            meta: {
              title: '班次详情',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/shiftManage/dataBoard'
            }
          }
        ]
      },
      {
        path: 'classeseManage',
        component: EmptyLayout,
        redirect: { name: 'classeseManage' },
        meta: {
          title: '班次管理',
          menuAuth: '/shiftManage/classeseManage'
        },
        children: [
          {
            path: '',
            name: 'classeseManage',
            component: () => import('@/views/operationPort/shiftManage/classeseManage/index.vue'),
            meta: {
              title: '班次管理',
              sidebar: false,
              breadcrumb: false,
              menuAuth: '/shiftManage/classeseManage',
              auth: ['classeseManage:add', 'classeseManage:edit', 'classeseManage:delete', 'classeseManage:detail']
            }
          },
          {
            path: 'classeseAdd',
            name: 'classeseAdd',
            component: () => import('@/views/operationPort/shiftManage/classeseManage/addClassese.vue'),
            meta: {
              title: '新建班次',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/shiftManage/classeseManage'
            }
          }
        ]
      },
      {
        path: 'shiftAttendanceConfig',
        component: EmptyLayout,
        redirect: { name: 'shiftAttendanceConfig' },
        meta: {
          title: '值班考勤配置',
          menuAuth: '/shiftManage/shiftAttendanceConfig'
        },
        children: [
          {
            path: '',
            name: 'shiftAttendanceConfig',
            component: () => import('@/views/operationPort/shiftManage/shiftAttendanceConfig/index.vue'),
            meta: {
              title: '值班考勤配置',
              sidebar: false,
              breadcrumb: false,
              menuAuth: '/shiftManage/shiftAttendanceConfig'
            }
          },
          {
            path: 'addShiftAttendance',
            name: 'addShiftAttendance',
            component: () => import('@/views/operationPort/shiftManage/shiftAttendanceConfig/addShiftAttendance.vue'),
            meta: {
              title: '新建值班考勤组',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/shiftManage/shiftAttendanceConfig'
            }
          }
        ]
      },
      {
        path: 'attendanceTerminalManage',
        component: EmptyLayout,
        redirect: { name: 'attendanceTerminalManage' },
        meta: {
          title: '考勤终端管理',
          menuAuth: '/shiftManage/attendanceTerminalManage'
        },
        children: [
          {
            path: '',
            name: 'attendanceTerminalManage',
            component: () => import('@/views/operationPort/shiftManage/attendanceTerminalManage/index.vue'),
            meta: {
              title: '考勤终端管理',
              sidebar: false,
              breadcrumb: false,
              menuAuth: '/shiftManage/attendanceTerminalManage'
            }
          },
          {
            path: 'addAttendanceTerminalManage',
            name: 'addAttendanceTerminalManage',
            component: () => import('@/views/operationPort/shiftManage/attendanceTerminalManage/addAttendanceTerminalManage.vue'),
            meta: {
              title: '考勤终端管理',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/shiftManage/attendanceTerminalManage'
            }
          }
        ]
      },
      {
        path: 'assessIndicator',
        component: EmptyLayout,
        redirect: { name: 'assessIndicator' },
        meta: {
          title: '考核指标',
          menuAuth: '/shiftManage/assessIndicator'
        },
        children: [
          {
            path: '',
            name: 'assessIndicator',
            component: () => import('@/views/operationPort/shiftManage/assessIndicator/index.vue'),
            meta: {
              title: '考核指标',
              sidebar: false,
              breadcrumb: false,
              menuAuth: '/shiftManage/assessIndicator'
            }
          }
        ]
      }
    ]
  },
  // 号源管理
  {
    path: '/numSourceManagement',
    component: Layout,
    redirect: '/numSourceManagement/index',
    name: 'numSourceManagement',
    meta: {
      title: '号源管理',
      menuAuth: '/numSourceManagement/index'
      // icon: 'sidebar-breadcrumb'
    },
    children: [
      {
        path: 'index',
        name: 'numSourceManagement',
        component: () => import('@/views/operationPort/numSourceManagement/index.vue'),
        meta: {
          title: '号源管理',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/numSourceManagement'
        }
      },
      {
        path: 'registrationRecord',
        name: 'registrationRecord',
        component: () => import('@/views/operationPort/numSourceManagement/registrationRecord.vue'),
        meta: {
          title: '挂号记录',
          sidebar: false,
          activeMenu: '/numSourceManagement/index'
        }
      }
    ]
  },
  // 就诊管理
  {
    path: '/encounterMgmt',
    component: Layout,
    redirect: '/encounterMgmt/index',
    name: 'encounterMgmt',
    meta: {
      title: '就诊管理',
      menuAuth: '/encounterMgmt/index'
      // icon: 'sidebar-breadcrumb'
    },
    children: [
      {
        path: 'index',
        name: 'encounterMgmt',
        component: () => import('@/views/operationPort/encounterMgmt/index.vue'),
        meta: {
          title: '就诊管理',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/encounterMgmt'
        }
      }
    ]
  },
  // 重点区域管理
  {
    path: '/keyAreaManagement',
    component: Layout,
    redirect: '/keyAreaManagement/index',
    name: 'keyAreaManagement',
    meta: {
      title: '重点区域管理',
      menuAuth: '/keyAreaManagement/index'
      // icon: 'sidebar-breadcrumb'
    },
    children: [
      {
        path: 'index',
        name: 'keyAreaManagement',
        component: () => import('@/views/operationPort/keyAreaManagement/index.vue'),
        meta: {
          title: '重点区域管理',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/keyAreaManagement'
        }
      },
      {
        path: 'addKeyAreas',
        name: 'addKeyAreas',
        component: () => import('@/views/operationPort/keyAreaManagement/addKeyAreas.vue'),
        meta: {
          title: '新增重点区域',
          sidebar: false,
          activeMenu: '/keyAreaManagement/index'
        }
      }
    ]
  },
  // 人流量监测
  {
    path: '/peopleFlowMonitor',
    component: Layout,
    redirect: '/peopleFlowMonitor/statistics',
    name: 'peopleFlowMonitor',
    meta: {
      title: '人流量监测',
      menuAuth: '/peopleFlowMonitor'
    },
    children: [
      {
        path: 'statistics',
        component: EmptyLayout,
        redirect: { name: 'statistics' },
        meta: {
          title: '人流量监测统计',
          menuAuth: '/peopleFlowMonitor/statistics'
        },
        children: [
          {
            path: '',
            name: 'statistics',
            component: () => import('@/views/operationPort/peopleFlowMonitor/statistics.vue'),
            meta: {
              title: '人流量监测统计',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'config',
        component: EmptyLayout,
        redirect: { name: 'config' },
        meta: {
          title: '人流量监测配置',
          menuAuth: '/peopleFlowMonitor/config'
        },
        children: [
          {
            path: '',
            name: 'config',
            component: () => import('@/views/operationPort/peopleFlowMonitor/config/index.vue'),
            meta: {
              title: '人流量监测配置',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'add',
            name: 'add',
            component: () => import('@/views/operationPort/peopleFlowMonitor/config/add.vue'),
            meta: {
              title: '新增',
              sidebar: false,
              activeMenu: '/peopleFlowMonitor/config'
            }
          }
        ]
      }
    ]
  }
]
