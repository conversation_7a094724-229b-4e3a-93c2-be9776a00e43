<template>
  <el-dialog v-if="dialogVisible" custom-class="model-dialog" :title="ifType == 'add' ? '新增摄像机' : '修改摄像机'" :visible="dialogVisible" :before-close="closeDialog">
    <div class="camera-dialog-content" style="padding: 10px 20px 10px 10px">
      <el-form ref="addCameraManage" :model="addCameraManage" :inline="true" class="form-inline" label-width="100px" :rules="rules">
        <el-form-item label="摄像机名称" prop="icmName">
          <el-input v-model="addCameraManage.icmName" placeholder="请输入摄像机名称" show-word-limit></el-input>
        </el-form-item>
        <br />
        <el-form-item label="空间" prop="icmSpace">
          <el-cascader
            ref="icmSpace"
            v-model="addCameraManage.icmSpace"
            filterable
            :show-all-levels="false"
            placeholder="请选择空间"
            :options="icmSpaceArray"
            :props="props1"
            clearable
            @change="spaceChang(addCameraManage.icmSpace)"
          ></el-cascader>
        </el-form-item>
        <br />
        <el-form-item label="摄像机IP" prop="icmIp">
          <el-input v-model="addCameraManage.icmIp" placeholder="请输入摄像机IP" show-word-limit></el-input>
        </el-form-item>
        <br />
        <el-form-item label="类型" prop="icmType">
          <el-select ref="groupRis" v-model="addCameraManage.icmType" class="sino_sdcp_input mr15 parameter" filterable collapse-tags placeholder="请选择摄像机类型">
            <el-option v-for="item in icmType" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <br />
        <div>
          <el-form-item v-if="addCameraManage.icmType == '模拟'" label="通道号" prop="icmNumber">
            <el-input v-model="addCameraManage.icmNumber" placeholder="请输入通道号" show-word-limit></el-input>
          </el-form-item>
        </div>
        <el-form-item label="厂家" prop="icmManufacturer">
          <el-select ref="groupRis" v-model="addCameraManage.icmManufacturer" class="sino_sdcp_input mr15 parameter" filterable collapse-tags placeholder="请选择厂家">
            <el-option v-for="item in icmManufacturer" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <br />
        <div v-if="addCameraManage.icmManufacturer != '其他'">
          <el-form-item label="用户名" prop="icmUserName">
            <el-input v-model="addCameraManage.icmUserName" placeholder="请输入用户名" show-word-limit></el-input>
          </el-form-item>
          <br />
          <el-form-item label="密码" prop="icmPassword">
            <el-input v-model="addCameraManage.icmPassword" placeholder="请输入密码" show-word-limit></el-input>
          </el-form-item>
        </div>
        <el-form-item v-if="addCameraManage.icmManufacturer == '其他' || (addCameraManage.icmUserName && addCameraManage.icmPassword)" label="RTSP" prop="icmRtsp">
          <el-input v-model="addCameraManage.icmRtsp" placeholder="请输入RTSP" show-word-limit></el-input>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer" style="margin-top: 50px">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="editSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    checkedData: {
      type: Object,
      default: () => {}
    },
    ifType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      spaceList: [],
      loginError: {
        errorStatus: true,
        errorMessage: ''
      },
      loading: false,
      rules: {
        icmName: [{ required: true, message: '请输入摄像机名称', trigger: 'change' }],
        icmSpace: [{ required: true, message: '请选择空间', trigger: 'change' }],
        icmIp: [{ required: true, message: '请输入摄像机IP', trigger: 'change' }],
        icmType: [{ required: true, message: '请选择类型', trigger: 'change' }],
        icmNumber: [{ required: true, message: '请输入通道号', trigger: 'change' }],
        icmManufacturer: [{ required: true, message: '请选择厂家', trigger: 'change' }],
        icmUserName: [{ required: true, message: '请输入用户名', trigger: 'change' }],
        icmPassword: [{ required: true, message: '请输入密码', trigger: 'change' }],
        icmRtsp: [{ required: true, message: '请输入RTSP', trigger: 'change' }]
      },
      addCameraManage: {
        icmName: '', // 摄像机名称
        icmSpace: '', // 空间id
        icmSpaceName: '', // 空间名字
        icmIp: '', // 摄像机IP
        icmType: '', // 类型
        icmNumber: '', // 通道号
        icmManufacturer: '', // 厂家
        icmUserName: '', // 用户名
        icmPassword: '', // 密码
        icmRtsp: '' // RTSP
      },
      icmManufacturer: [
        {
          label: '宇视',
          value: '宇视'
        },
        {
          label: '海康',
          value: '海康'
        },
        {
          label: '大华',
          value: '大华'
        },
        {
          label: '华为',
          value: '华为'
        },
        {
          label: '其他',
          value: '其他'
        }
      ],
      icmType: [
        {
          label: '数字',
          value: '数字'
        },
        {
          label: '模拟',
          value: '模拟'
        }
      ],
      icmSpaceArray: [],
      props1: {
        checkStrictly: true,
        emitPath: false,
        value: 'id',
        label: 'ssmName',
        children: 'list'
      }
    }
  },
  mounted() {
    this.getData()
    this.treeDataForStaff() // 空间
  },
  methods: {
    closeDialog() {
      this.$emit('update:dialogVisible', false)
    },
    getData() {
      this.reset()
      if (this.checkedData) {
        this.addCameraManage.icmName = this.checkedData.icmName
        this.addCameraManage.icmSpace = this.checkedData.icmSpace
        this.addCameraManage.icmSpaceName = this.checkedData.icmSpaceName
        this.addCameraManage.icmIp = this.checkedData.icmIp
        this.addCameraManage.icmType = this.checkedData.icmType
        this.addCameraManage.icmNumber = this.checkedData.icmNumber
        this.addCameraManage.icmManufacturer = this.checkedData.icmManufacturer
        this.addCameraManage.icmUserName = this.checkedData.icmUserName
        this.addCameraManage.icmPassword = this.checkedData.icmPassword
        this.addCameraManage.icmRtsp = this.checkedData.icmRtsp
      }
    },
    // 重置
    reset() {
      this.addCameraManage.icmName = ''
      this.addCameraManage.icmSpace = ''
      this.addCameraManage.icmSpaceName = ''
      this.addCameraManage.icmIp = ''
      this.addCameraManage.icmType = ''
      this.addCameraManage.icmNumber = ''
      this.addCameraManage.icmManufacturer = ''
      this.addCameraManage.icmUserName = ''
      this.addCameraManage.icmPassword = ''
      this.addCameraManage.icmRtsp = ''
    },
    // 保存新增/修改的人员
    editSubmit() {
      this.$refs['addCameraManage'].validate((valid) => {
        if (valid) {
          let data = {
            ...this.addCameraManage
          }
          if (this.ifType == 'edit') {
            data.icmId = this.checkedData.icmId
          }
          let header = {}
          if (this.ifType == 'edit') {
            header = {
              'operation-type': 2,
              'operation-id': data.icmId,
              'operation-name': data.icmName
            }
          } else {
            header = {
              'operation-type': 1
            }
          }
          this.$api.saveCamera(data, header).then((res) => {
            if (res.code == 200) {
              this.$message.success(res.message)
              this.$emit('sure')
            } else {
              this.$message.error(res.message)
            }
          })
        }
      })
    },
    // 空间数据
    treeDataForStaff() {
      this.$api.getAllSpaceTree().then((res) => {
        if (res.code === 200) {
          this.spaceList = res.data
          this.icmSpaceArray = this.$tools.transData(res.data, 'id', 'pid', 'list')
        }
      })
    },
    addicmSpace(val) {
      this.addCameraManage.icmSpaceName = []
      this.$refs.icmSpace.getCheckedNodes().map((item) => {
        this.addCameraManage.icmSpaceName.push(item.label)
      })
    },
    spaceChang(val) {
      this.spaceList.forEach((item) => {
        if (item.id == val) {
          this.addCameraManage.icmSpaceName = item.ssmName || item.id
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .model-dialog {
  width: 562px !important;

  .camera-dialog-content {
    width: 100%;
    height: 100%;
    margin: 0 auto;
    background: #fff;
    border-radius: 4px;

    .el-form-item {
      width: 100%;

      .el-form-item__content {
        width: calc(100% - 150px) !important;
      }

      .el-input,
      .el-select,
      .el-cascader {
        width: 100% !important;
      }
    }
  }
}
</style>
