<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-06-06 18:13:06
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-06-19 15:39:21
 * @FilePath: \ihcrs_pc\src\views\operationPort\spaceManage\operationsManage\iomsOperations.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <template v-for="(item, index) in dlayoutItems">
      <dash-item v-if="item.id == 'serviceOverview'" v-bind.sync="dlayoutItems[index]" :key="item.id" @moveEnd="moveEnd"
        @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)">
        <ContentCard title="服务概况" :scrollbarHover="true" :cstyle="{ height: '100%' }" class="drag_class"
          :hasMoreOper="['edit']" @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')">
          <div slot="content" style="height: 100%">
            <div id="serviceOverviewChart" class="full-style"></div>
          </div>
        </ContentCard>
      </dash-item>
      <dash-item v-if="item.id == 'last6months'" v-bind.sync="dlayoutItems[index]" :key="item.id"
        @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)">
        <ContentCard title="近6个月工单走势" :scrollbarHover="true" :cstyle="{ height: '100%' }" class="drag_class"
          :hasMoreOper="['edit']" @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')">
          <div slot="content" style="height: 100%">
            <div id="last6monthChart" class="full-style"></div>
          </div>
        </ContentCard>
      </dash-item>
      <dash-item v-if="item.id == 'orderOverview'" v-bind.sync="dlayoutItems[index]" :key="item.id"
        @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)">
        <ContentCard title="工单概览" :scrollbarHover="true" :cstyle="{ height: '100%' }" class="drag_class"
          :hasMoreOper="['edit']" @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')">
          <div slot="content" style="height: 100%">
            <div class="num-box">
              <div class="left-num-box pointer-style" @click="changeFlowCode('')">
                <div>
                  <span>服务总量</span>
                  <span style="font-size: 26px">{{ countData.all }}</span>
                </div>
                <img src="@/assets/images/statistics.png" />
              </div>
              <div class="right-num-box">
                <div class="pointer-style" @click="changeFlowCode('5')">
                  <span>已完工</span>
                  <span style="color: #3562db">{{ countData.completed }}</span>
                </div>
                <div class="pointer-style" @click="changeFlowCode('30')">
                  <span>未完工</span>
                  <span style="color: #ff6461">{{ countData.unfinishedwork }}</span>
                </div>
                <div class="pointer-style" @click="changeFlowCode('6')">
                  <span>已取消</span>
                  <span style="color: #ff9435">{{ countData.cancelled }}</span>
                </div>
              </div>
            </div>
            <div class="rate-box">
              <div>
                <div>完工率</div>
                <div>
                  <span class="rate-val">{{ countData && countData.completionRate ? countData.completionRate.slice(0,
                    -1) : '' }}</span>
                  <span class="unit">%</span>
                </div>
              </div>
              <div>
                <div>返修率</div>
                <div>
                  <span class="rate-val">{{ countData && countData.repairWork ? countData.repairWork.slice(0, -1) : ''
                    }}</span>
                  <span class="unit">%</span>
                </div>
              </div>
              <div>
                <div>综合评价</div>
                <div>
                  <span class="rate-val">{{ countData && countData.evaluate ? countData.evaluate.slice(0, -1) : ''
                    }}</span>
                  <span class="unit">分</span>
                </div>
              </div>
              <div>
                <div>回访率</div>
                <div>
                  <span class="rate-val">{{ countData && countData.callBack ? countData.callBack.slice(0, -1) : ''
                    }}</span>
                  <span class="unit">%</span>
                </div>
              </div>
            </div>
            <!-- <div class="statistics">
              <div :class="['item', 'pointer-style']" @click="changeFlowCode('')">
                <span>服务总量</span>
                <span>{{ countData.all }}</span>
              </div>
              <div :class="['item', 'pointer-style']" @click="changeFlowCode('5')">
                <span>已完工</span>
                <span>{{ countData.completed }}</span>
              </div>
              <div :class="['item', 'pointer-style']" @click="changeFlowCode('30')">
                <span>未完工</span>
                <span>{{ countData.unfinishedwork }}</span>
              </div>
              <div :class="['item', 'pointer-style']" @click="changeFlowCode('6')">
                <span>已取消</span>
                <span>{{ countData.cancelled }}</span>
              </div>
              <div class="item pure">
                <span>完工率</span>
                <span>{{ countData.completionRate }}</span>
              </div>
              <div class="item pure">
                <span>平均响应</span>
                <span>{{ countData.response }}</span>
              </div>
              <div class="item pure">
                <span>平均完工</span>
                <span>{{ countData.finishTime }}</span>
              </div>
              <div class="item pure">
                <span>返修率</span>
                <span>{{ countData.repairWork }}</span>
              </div>
              <div class="item pure">
                <span>综合评价</span>
                <span>{{ countData.evaluate }}</span>
              </div>
              <div class="item pure">
                <span>回访率</span>
                <span>{{ countData.callBack }}</span>
              </div>
            </div> -->
          </div>
        </ContentCard>
      </dash-item>
      <dash-item v-if="item.id == 'annualOrderTrend'" v-bind.sync="dlayoutItems[index]" :key="item.id"
        @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)">
        <ContentCard title="年度工单走势" :scrollbarHover="true" :cstyle="{ height: '100%' }" class="drag_class"
          :hasMoreOper="['edit']" @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')">
          <div slot="content" style="height: 100%">
            <div id="annualOrderTrendChart" class="full-style"></div>
          </div>
        </ContentCard>
      </dash-item>
      <dash-item v-if="item.id == 'itemsTop10'" v-bind.sync="dlayoutItems[index]" :key="item.id"
        @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)">
        <ContentCard title="报修事项Top10" :scrollbarHover="true" :cstyle="{ height: '100%' }" class="drag_class"
          :hasMoreOper="['edit']" @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')">
          <div slot="content" style="height: 100%">
            <div id="itemsTop10Chart" class="full-style"></div>
          </div>
        </ContentCard>
      </dash-item>
      <dash-item v-if="item.id == 'departTop5'" v-bind.sync="dlayoutItems[index]" :key="item.id"
        @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)">
        <ContentCard title="本月报修科室Top5" :scrollbarHover="true" :cstyle="{ height: '100%' }" class="drag_class"
          :hasMoreOper="['edit']" @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')">
          <div slot="content" style="height: 100%">
            <div id="departTop5" class="full-style"></div>
          </div>
        </ContentCard>
      </dash-item>
      <dash-item v-if="item.id == 'reportBuildingTop5'" v-bind.sync="dlayoutItems[index]" :key="item.id"
        @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)">
        <ContentCard title="本月报修楼宇Top5" :scrollbarHover="true" :cstyle="{ height: '100%' }" class="drag_class"
          :hasMoreOper="['edit']" @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')">
          <div slot="content" style="height: 100%">
            <div id="reportBuildingTop5" class="full-style"></div>
          </div>
        </ContentCard>
      </dash-item>
      <dash-item v-if="item.id == 'workMonthComparison'" v-bind.sync="dlayoutItems[index]" :key="item.id"
        @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)">
        <ContentCard title="工单月对比" :scrollbarHover="true" :cstyle="{ height: '100%' }" class="drag_class"
          :hasMoreOper="['edit']" @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')">
          <div slot="content" style="height: 100%; position: relative">
            <div class="total-text">
              <img src="@/assets/images/operationPort/waste-icon5.png" />
              <span style="color: #666">本年累计工单总量&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
              <span style="color: #333333; font-size: 18px; font-weight: bold">{{ totalWorkOrders }}&nbsp;</span>
              <span style="font-size: 12px; color: #414653">单</span>
            </div>
            <div id="workMonthComparisonChart" class="full-style" style="height: 94%"></div>
          </div>
        </ContentCard>
      </dash-item>
      <dash-item v-if="item.id == 'groupWorkOrder'" v-bind.sync="dlayoutItems[index]" :key="item.id"
        @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)">
        <ContentCard title="班组工单量统计分析" :scrollbarHover="true" :cstyle="{ height: '100%' }" class="drag_class"
          :hasMoreOper="['edit']" @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')">
          <div slot="title-right" class="data-btns">
            <span :class="{ 'active-btn': groupDateType == 'day' }" @click="changeGroupDateType('day')">日</span>
            <span :class="{ 'active-btn': groupDateType == 'week' }" @click="changeGroupDateType('week')">周</span>
            <span :class="{ 'active-btn': groupDateType == 'month' }" @click="changeGroupDateType('month')">月</span>
            <span :class="{ 'active-btn': groupDateType == 'year' }" @click="changeGroupDateType('year')">年</span>
          </div>
          <div slot="content" style="height: 100%">
            <div class="full-style" style="position: relative">
              <el-table v-loading="tableLoading" v-el-table-infinite-scroll="loadMore" stripe :data="tableData"
                :border="true" height="100%" :header-cell-style="{ backgroundColor: '#F9F9F9', color: '#333333' }">
                <el-table-column type="index" label="序号" width="88"></el-table-column>
                <el-table-column prop="deptName" label="班组名称" width="120" show-overflow-tooltip></el-table-column>
                <el-table-column prop="allCount" label="服务总量" show-overflow-tooltip width="140">
                  <template slot-scope="scope">
                    <span style="color: #3a62d8; cursor: pointer" @click="handleJumpAll(scope.row)">{{
                      scope.row.allCount }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="finish" label="已完工" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span style="color: #3a62d8; cursor: pointer" @click="handleJumpFinish(scope.row)">{{
                      scope.row.finish }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="finishRate" label="完工率" show-overflow-tooltip></el-table-column>
                <el-table-column prop="returnRate" label="返修率" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="finishTime" label="平均完工时间（小时）" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="evaluate" label="满意度" show-overflow-tooltip> </el-table-column>
              </el-table>
            </div>
          </div>
        </ContentCard>
      </dash-item>
      <dash-item v-if="item.id == 'costOfConsumables'" v-bind.sync="dlayoutItems[index]" :key="item.id"
        @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)">
        <ContentCard title="维修耗材费用" :scrollbarHover="true" :cstyle="{ height: '100%' }" class="drag_class"
          :hasMoreOper="['edit']" @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')">
          <div slot="title-right" class="data-btns">
            <span :class="{ 'active-btn': totalCostDateType == 'week' }" @click="changeDateType('week')">周</span>
            <span :class="{ 'active-btn': totalCostDateType == 'month' }" @click="changeDateType('month')">月</span>
            <span :class="{ 'active-btn': totalCostDateType == 'year' }" @click="changeDateType('year')">年</span>
          </div>
          <div slot="content" style="height: 100%; position: relative">
            <div class="cost-box">
              <span class="cost-title">
                <span>维修耗材费用</span>
                <i class="el-icon-arrow-right"></i>
              </span>
              <div class="cost-num">
                <span>
                  <span style="cursor: pointer" @click="goMaintenanceCost">{{ totalPrice || 0 }}</span>
                  <span>万元</span>
                </span>
                <img src="@/assets/images/service/icon2.png" />
              </div>
            </div>
          </div>
        </ContentCard>
      </dash-item>
      <dash-item v-if="item.id == 'serviceEfficiency'" v-bind.sync="dlayoutItems[index]" :key="item.id"
        @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)">
        <ContentCard title="服务效率分析" :scrollbarHover="true" :cstyle="{ height: '100%' }" class="drag_class"
          :hasMoreOper="['edit']" @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')">
          <div slot="content" style="height: 100%; position: relative">
            <div class="count-box">
              <div class="response">
                <div>
                  <!-- <img src="@/assets/images/service/time1.png" /> -->
                  <div class="val-box">
                    <span class="desc">工单平均响应时间</span>
                    <span class="time">{{ countData.response }}</span>
                  </div>
                </div>
                <div class="average">
                  <span>同比去年平均值</span>
                  <span>
                    <span :class="[countData.responseRate.charAt(0) == '-' ? 'green-text' : 'red-text']">{{
                      countData.responseRate }}</span>
                    <img v-if="countData.responseRate.charAt(0) == '-'" src="@/assets/images/service/down.png" />
                    <img v-else src="@/assets/images/service/up.png" />
                  </span>
                </div>
              </div>
              <div class="finish response">
                <div>
                  <!-- <img src="@/assets/images/service/time2.png" /> -->
                  <div class="val-box">
                    <span class="desc">工单平均完工时长</span>
                    <span class="time">{{ countData.finishTime }}</span>
                  </div>
                </div>
                <div class="average">
                  <span>同比去年平均值</span>
                  <span>
                    <span :class="[countData.responseRate.charAt(0) == '-' ? 'green-text' : 'red-text']">{{
                      countData.finishTimeRate }}</span>
                    <img v-if="countData.finishTimeRate.charAt(0) == '-'" src="@/assets/images/service/down.png" />
                    <img v-else src="@/assets/images/service/up.png" />
                  </span>
                </div>
              </div>
            </div>
            <div class="chart-box">
              <div class="full-style" style="position: relative">
                <div id="monthSatisfyChart" class="full-style"></div>
                <div v-if="workOrderInfo && workOrderInfo.satisfiedRate" class="chart-legend">
                  本月服务满意度<span class="legend-text">{{ Number(workOrderInfo.satisfiedRate.substring(0,
                    workOrderInfo.satisfiedRate.length - 1)) }}</span>%
                </div>
              </div>
              <div class="full-style" style="position: relative">
                <div id="callbackChart" class="full-style"></div>
                <div v-if="workOrderInfo && workOrderInfo.callBackRate" class="chart-legend">
                  近7天回访率<span class="legend-text">{{ Number(workOrderInfo.callBackRate.substring(0,
                    workOrderInfo.callBackRate.length - 1)) }}</span>%
                </div>
              </div>
            </div>
            <div class="order-num">
              <div class="content">
                <div class="left-cont">
                  <span style="margin-left: 24px" class="square-icon">本日超时工单&nbsp;&nbsp;</span>
                  <span>
                    <span style="cursor: pointer; color: #3562db" @click="goTimeout">{{ countOverTimes }}</span>
                    <!-- <span>单</span> -->
                  </span>
                </div>
                <div class="right-cont">
                  <span>本日总工单&nbsp;&nbsp;</span>
                  <span>
                    <span style="cursor: pointer; margin-left: 12px" @click="goToday">{{ workOrderInfo.todayNum
                      }}</span>
                    <!-- <span>单</span> -->
                  </span>
                </div>
              </div>
              <el-progress :percentage="percentage" color="#ff6461" :stroke-width="12" define-back-color="#ccced4"
                :show-text="false"></el-progress>
            </div>
          </div>
        </ContentCard>
      </dash-item>
    </template>
  </div>
</template>
<script>
import { DashItem } from 'vue-responsive-dash'
import * as echarts from 'echarts'
export default {
  name: 'iomsOperations',
  components: {
    DashItem
  },
  props: {
    dlayoutItems: {
      type: Array,
      default: () => []
    },
    componentData: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      echartsDom: [
        'serviceOverviewChart',
        'last6monthChart',
        'orderOverviewChart',
        'annualOrderTrendChart',
        'itemsTop10Chart',
        'departTop5Chart',
        'monthSatisfyChart',
        'callbackChart'
      ],
      // dlayoutItems: [
      //   { id: 'serviceOverview', x: 0, y: 5, width: 8, height: 5, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
      //   { id: 'last6months', x: 8, y: 0, width: 9, height: 5, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
      //   { id: 'orderOverview', x: 8, y: 5, width: 16, height: 5, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
      //   { id: 'annualOrderTrend', x: 8, y: 10, width: 16, height: 5, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
      //   { id: 'itemsTop10', x: 17, y: 0, width: 7, height: 5, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
      //   { id: 'departTop5', x: 0, y: 10, width: 8, height: 5, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
      //   { id: 'reportBuildingTop5', x: 0, y: 15, width: 8, height: 5, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
      //   { id: 'workMonthComparison', x: 8, y: 15, width: 8, height: 5, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
      //   { id: 'groupWorkOrder', x: 16, y: 15, width: 8, height: 5, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
      //   { id: 'costOfConsumables', x: 0, y: 20, width: 8, height: 5, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
      //   { id: 'serviceEfficiency', x: 0, y: 0, width: 8, height: 5, dragAllowFrom: '.drag_class', resizable: true, draggable: true }
      // ],
      countData: {
        responseRate: '',
        finishTimeRate: ''
      },
      free1: '',
      iomsWorkOrderParams: {
        typeSources: '',
        contrastType: '',
        free1: '',
        free2: '',
        workTypeCode: '',
        urgencyDegree: '',
        disDegree: '',
        disDegreeNew: '',
        workSources: '',
        feedbackFlag: '',
        responseTime: '',
        responseTimeType: '',
        transportTypeCode: '',
        isPack: '',
        releaseType: '',
        statementFlagCode: '',
        showTimeType: 'all',
        hqfwsj: 'hqfwsj',
        startTime: '',
        endTime: '',
        sectionStartDate: '',
        sectionEndDate: '',
        sectionStartTime: '',
        sectionEndTime: '',
        sourcesDept: '',
        region: '',
        buliding: '',
        storey: '',
        room: '',
        localtionName: '',
        replyToCode: '',
        sourcesPhone: '',
        itemDetailCode: '',
        itemDetailName: '',
        itemTypeCode: '',
        itemTypeName: '',
        itemServiceCode: '',
        itemServiceName: '',
        itemList: '',
        questionDescription: '',
        workNum: '',
        restaurantId: '',
        haveStartTime: '',
        haveEndTime: '',
        orderBy: ''
      },
      flowCode: '',
      groupDateType: 'month',
      tableLoading: false,
      tableData: [],
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      totalCostDateType: 'year',
      totalPrice: 0,
      percentage: 0,
      countOverTimes: 0,
      workOrderInfo: {},
      workMonthComparisonChartData: []
    }
  },
  computed: {
    totalWorkOrders() {
      // let year = new Date().getFullYear()
      // let total = 0
      // this.workMonthComparisonChartData.forEach((item) => {
      //   if (item.year == year) {
      //     total += Number(item.value)
      //   }
      // })
      // return total

      let total = 0
      if (this.workMonthComparisonChartData && this.workMonthComparisonChartData.currYear) {
        this.workMonthComparisonChartData.currYear.forEach((item) => {
          total += Number(item.value)
        })
      }
      return total
    }
  },
  watch: {
    '$store.state.settings.sidebarCollapse': {
      handler(val) {
        this.$nextTick(() => {
          let echartsDom = this.echartsDom
          setTimeout(() => {
            echartsDom.forEach((item) => {
              echarts.init(document.getElementById(item)).resize()
            })
          }, 250)
        })
      },
      deep: true
    }
  },
  mounted() {
    setTimeout(() => {
      this.getServiceOverviewChartData()
      this.getLast6MonthsChartData()
      this.getReckonCount()
      this.getAnnualOrderTrendChartData()
      this.getItemsTop10ChartData()
      this.getDepartTop5ChartData()
      this.getReportBuildingTop5Data()
      this.getWorkMonthComparisonChartData()
      this.getGroupWorkOrderData()
      this.getCostOfConsumablesData()
      this.getServiceEfficiencyData()
      this.getComprehensiveWorkOrderInfo()
    }, 300)
  },
  methods: {
    getComprehensiveWorkOrderInfo() {
      this.$api.getComprehensiveWorkOrderInfo({}).then((res) => {
        if (res.code == '200') {
          this.workOrderInfo = res.data.resultMap
          this.initPercentageChart()
          this.initPercentageChart2()
          if (this.workOrderInfo.todayNum == 0) {
            this.percentage = 0
          } else {
            setTimeout(() => {
              this.percentage = Math.round((this.countOverTimes / this.workOrderInfo.todayNum) * 100)
            }, 100)
          }
        }
      })
    },
    goTimeout() { },
    goToday() { },
    getServiceEfficiencyData() { },
    getCostOfConsumablesData() {
      let params = {
        dateType: this.totalCostDateType
      }
      this.$api.getConsumableMaterialTotalExpenses(params).then((res) => {
        if (res.code == '200') {
          this.totalPrice = res.data.map.totalPrice
          if (this.totalPrice) {
            this.totalPrice = this.totalPrice.toFixed(2)
          }
        }
      })
    },
    initPercentageChart() {
      let satisfiedRate = this.workOrderInfo.satisfiedRate.substring(0, this.workOrderInfo.satisfiedRate.length - 1)
      let satisfiedRateNum = Number(satisfiedRate)
      const getchart = echarts.init(document.getElementById('monthSatisfyChart'))
      const option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          show: false
        },
        series: [
          {
            type: 'pie',
            radius: ['30%', '45%'],
            center: ['50%', '40%'],
            avoidLabelOverlap: false,
            color: ['#3562DB', '#E4E7ED'],
            clockwise: false,
            tooltip: {
              show: false
            },
            label: {
              show: false
            },
            emphasis: {
              disabled: true
            },
            labelLine: {
              show: false
            },
            data: [
              {
                value: satisfiedRateNum,
                name: '本月服务满意度'
              },
              {
                value: 100 - satisfiedRateNum,
                name: '本月服务不满意度'
              }
            ]
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    initPercentageChart2() {
      let callBackRate = this.workOrderInfo.callBackRate.substring(0, this.workOrderInfo.callBackRate.length - 1)
      let callBackRateNum = Number(callBackRate)
      const getchart = echarts.init(document.getElementById('callbackChart'))
      const option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          show: false
        },
        series: [
          {
            type: 'pie',
            radius: ['30%', '45%'],
            center: ['50%', '40%'],
            avoidLabelOverlap: false,
            color: ['#3562DB', '#E4E7ED'],
            clockwise: false,
            tooltip: {
              show: false
            },
            label: {
              show: false
            },
            emphasis: {
              disabled: true
            },
            labelLine: {
              show: false
            },
            data: [
              {
                value: callBackRateNum,
                name: '近七天回访率'
              },
              {
                value: 100 - callBackRateNum,
                name: '近七天不回访率'
              }
            ]
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    goMaintenanceCost() {
      this.$router.push({
        path: '/maintenanceService/workOrderTable',
        query: {
          typeName: 'MaintenanceCost',
          totalCostDateType: this.totalCostDateType
        }
      })
    },
    changeDateType(dateType) {
      this.totalCostDateType = dateType
      this.getCostOfConsumablesData()
    },
    changeGroupDateType(dateType) {
      this.groupDateType = dateType
      this.getGroupWorkOrderData()
    },
    loadMore() {
      if (this.tableData.length < this.paginationData.total && this.tableData.length) {
        this.paginationData.currentPage += 1
        this.getGroupWorkOrderData()
      }
    },
    getGroupWorkOrderData() {
      this.tableLoading = true
      let params = {}
      if (this.groupDateType == 'day') {
        params.btnType = '1'
      }
      if (this.groupDateType == 'week') {
        params.btnType = '2'
      }
      if (this.groupDateType == 'month') {
        params.btnType = '3'
      }
      if (this.groupDateType == 'year') {
        params.btnType = '4'
      }
      this.$api
        .getTeamTasks(params)
        .then((res) => {
          if (res.code == '200') {
            this.tableLoading = false
            this.tableData = res.data
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    moveEnd() {
      console.log(this.dlayoutItems)
    },
    getWorkMonthComparisonChartData() {
      this.$api.getMonthComparison({}).then((res) => {
        if (res.code == '200') {
          this.workMonthComparisonChartData = res.data
          this.initWorkMonthComparisonChart(res.data)
        }
      })
    },
    initWorkMonthComparisonChart(chartData) {
      let chartData1 = chartData.lastYear
      let chartData2 = chartData.currYear
      // chartData.forEach((item) => {
      //   let firstItem = chartData[0].year
      //   if (item.year == firstItem) {
      //     chartData1.push({
      //       name: item.month,
      //       value: item.value,
      //       year: item.year
      //     })
      //   } else {
      //     chartData2.push({
      //       name: item.month,
      //       value: item.value,
      //       year: item.year
      //     })
      //   }
      // })
      chartData1.forEach((item) => {
        // 把year改成字符串格式
        item.year = item.year.toString()
      })
      chartData2.forEach((item) => {
        // 把year改成字符串格式
        item.year = item.year.toString()
      })
      const nameList = chartData1.map((item) => item.month)
      const valueList = chartData1.map((item) => item.value)
      const valueList2 = chartData2.map((item) => item.value)
      const getchart = echarts.init(document.getElementById('workMonthComparisonChart'))
      const option = {
        tooltip: {
          trigger: 'axis'
          // formatter: function (params) {
          //   let str = '第' + params[0].axisValue + '周<br/>'
          //   params.forEach((item) => {
          //     str += item.marker + item.seriesName + '第' + params[0].axisValue + '周:' + item.value + '单<br/>'
          //   })
          //   return str
          // }
        },
        grid: {
          top: '20%',
          left: '1%',
          right: '1%',
          bottom: '6%',
          containLabel: true
        },
        legend: {
          show: true,
          // 修改位置
          right: 0,
          top: '8%',
          data: [chartData1[0].year ? chartData1[0].year : '', chartData2[0].year ? chartData2[0].year : '']
        },
        xAxis: {
          type: 'category',
          data: nameList,
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            interval: 1
          }
        },
        yAxis: [
          {
            type: 'value',
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            }
          }
        ],
        series: [
          {
            name: chartData1[0].year ? chartData1[0].year : '',
            data: valueList,
            type: 'line',
            symbol: 'emptyCircle',
            symbolSize: 6,
            itemStyle: {
              color: '#FF9435'
            },
            lineStyle: {
              color: '#FF9435'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: '#F0984B36' }, // 起始颜色
                  { offset: 1, color: '#fff' } // 结束颜色
                ]
              }
            }
          },
          {
            name: chartData2[0].year ? chartData2[0].year : '',
            data: valueList2,
            type: 'line',
            symbol: 'emptyCircle',
            symbolSize: 6,
            itemStyle: {
              color: '#4764CC'
            },
            lineStyle: {
              color: '#4764CC'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: '#3F63D336' }, // 起始颜色
                  { offset: 1, color: '#fff' } // 结束颜色
                ]
              }
            }
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    getReportBuildingTop5Data() {
      this.$api.taskByBuildingTop5({}).then((res) => {
        if (res.code == '200') {
          this.initReportBuildingTop5Chart(res.data)
        }
      })
    },
    initReportBuildingTop5Chart(chartData) {
      console.log('楼宇', chartData)
      chartData.sort((a, b) => {
        return a.value - b.value
      })
      const nameList = chartData.map((item) => {
        return item.buildingName
      })
      const getchart = echarts.init(document.getElementById('reportBuildingTop5'))
      let option
      let colors = ['#3562DB', '#FF9435']
      if (chartData.length) {
        option = {
          tooltip: {
            trigger: 'axis'
          },
          label: {
            show: true,
            position: 'right',
            color: '#121F3E',
            fontSize: '12px'
          },
          grid: {
            top: '4%',
            left: '4%',
            right: '8%',
            bottom: '6%',
            containLabel: true
          },
          xAxis: {
            type: 'value',
            name: ''
          },
          yAxis: [
            {
              type: 'category',
              data: nameList,
              axisLabel: {
                formatter: function (val) {
                  var strs = val.split('') // 字符串数组
                  var str = ''
                  // strs循环 forEach 每五个字符增加换行符
                  strs.forEach((item, index) => {
                    if (index % 7 === 0 && index !== 0) {
                      str += '\n'
                    }
                    str += item
                  })
                  return str
                }
              },
              axisLine: {
                show: false
              },
              axisTick: {
                show: false
              }
            }
          ],
          series: [
            {
              type: 'bar',
              barWidth: 10,
              data: chartData,
              itemStyle: {
                color: function (params) {
                  // 使用函数来设置颜色，实现循环使用两种颜色
                  return colors[params.dataIndex % colors.length]
                },
                barBorderRadius: [0, 2, 2, 0]
              }
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    getDepartTop5ChartData() {
      this.$api.taskByOfficeTop5({}).then((res) => {
        if (res.code == '200') {
          this.initDepartTop5Chart(res.data)
        }
      })
    },
    initDepartTop5Chart(data) {
      data.sort((a, b) => {
        return a.value - b.value
      })
      const nameList = data.map((item) => {
        return item.officeName
      })
      const getchart = echarts.init(document.getElementById('departTop5'))
      let colors = ['#FF9435', '#3562DB']
      let option
      if (data.length) {
        option = {
          tooltip: {
            trigger: 'axis'
          },
          label: {
            show: true,
            position: 'right',
            color: '#121F3E',
            fontSize: '12px'
          },
          grid: {
            top: '4%',
            left: '4%',
            right: '8%',
            bottom: '6%',
            containLabel: true
          },
          xAxis: {
            type: 'value',
            name: ''
          },
          yAxis: [
            {
              type: 'category',
              data: nameList,
              axisLabel: {
                formatter: function (val) {
                  var strs = val.split('') // 字符串数组
                  var str = ''
                  // strs循环 forEach 每五个字符增加换行符
                  strs.forEach((item, index) => {
                    if (index % 7 === 0 && index !== 0) {
                      str += '\n'
                    }
                    str += item
                  })
                  return str
                }
              },
              axisLine: {
                show: false
              },
              axisTick: {
                show: false
              }
            }
          ],
          series: [
            {
              type: 'bar',
              barWidth: 10,
              data: data,
              itemStyle: {
                color: function (params) {
                  // 使用函数来设置颜色，实现循环使用两种颜色
                  return colors[params.dataIndex % colors.length]
                },
                barBorderRadius: [0, 2, 2, 0]
              }
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    getItemsTop10ChartData() {
      this.$api.taskByServiceItemTop10({}).then((res) => {
        if (res.code == '200') {
          this.initItemsTop10Chart(res.data)
        }
      })
    },
    initItemsTop10Chart(chartData) {
      const getchart = echarts.init(document.getElementById('itemsTop10Chart'))
      chartData.sort((a, b) => {
        return a.value - b.value
      })
      const nameList = chartData.map((item) => {
        return item.itemName
      })
      let valList = chartData.map((item) => {
        return item.value
      })
      let colors = ['#00BC6D', '#3562DB']
      const option = {
        tooltip: {
          trigger: 'item'
          // position: 'left'
        },
        grid: {
          top: '5%',
          left: '5%',
          right: '5%',
          bottom: '5%',
          containLabel: true
        },
        yAxis: {
          type: 'category',
          data: nameList,
          axisLabel: {
            formatter: function (val) {
              var strs = val.split('') // 字符串数组
              var str = ''
              // strs循环 forEach 每五个字符增加换行符
              strs.forEach((item, index) => {
                if (index % 6 === 0 && index !== 0) {
                  str += '\n'
                }
                str += item
              })
              return str
            }
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        xAxis: [
          {
            type: 'value',
            name: '',
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#e3e7ec'
              }
            }
          }
        ],
        series: [
          {
            type: 'bar',
            barWidth: 10,
            data: valList,
            itemStyle: {
              color: function (params) {
                // 使用函数来设置颜色，实现循环使用两种颜色
                return colors[params.dataIndex % colors.length]
              },
              barBorderRadius: [0, 2, 2, 0]
            },
            label: {
              show: true,
              position: 'right',
              formatter: '{c}'
            }
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    getAnnualOrderTrendChartData() {
      this.$api.getWorkOrderNumByWeek({}).then((res) => {
        if (res.code == '200') {
          console.log(res.data.map.list)
          this.initAnnualOrderTrendChart(res.data.map.list)
        }
      })
    },
    initAnnualOrderTrendChart(chartData) {
      let arr = []
      chartData.forEach((item) => {
        arr.push({
          name: item.weeks,
          value: item.workNum
        })
      })
      const nameList = arr.map((item) => item.name)
      const valueList = arr.map((item) => item.value)
      const getchart = echarts.init(document.getElementById('annualOrderTrendChart'))
      const option = {
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            let str = '第' + params[0].axisValue + '周<br/>'
            params.forEach((item) => {
              str += item.marker + item.seriesName + '第' + params[0].axisValue + '周:' + item.value + '单<br/>'
            })
            return str
          }
        },
        grid: {
          top: '15%',
          left: '1%',
          right: '1%',
          bottom: '6%',
          containLabel: true
        },
        legend: {
          show: false,
          data: [chartData[0].years]
        },
        xAxis: {
          type: 'category',
          data: nameList,
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            interval: 1
          }
        },
        yAxis: [
          {
            type: 'value',
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            }
          }
        ],
        series: [
          {
            name: chartData[0].years,
            data: valueList,
            type: 'line',
            symbol: 'emptyCircle',
            symbolSize: 6,
            itemStyle: {
              color: '#3F63D3'
            },
            lineStyle: {
              color: '#3F63D3'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: '#3F63D336' }, // 起始颜色
                  { offset: 1, color: '#fff' } // 结束颜色
                ]
              }
            }
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    getReckonCount() {
      let params = {
        region: '',
        buliding: '',
        storey: '',
        room: ''
      }
      this.$api.reckonCount({ ...this.iomsWorkOrderParams, ...params }).then((res) => {
        this.countData = res.body.data
      })
    },
    changeFlowCode(val) {
      if (val == '30') {
        this.free1 = val
        this.flowCode = null
      } else {
        this.flowCode = val
        this.free1 = ''
      }
      this.$router.push({
        path: '/maintenanceService/workOrderTable',
        query: {
          typeName: 'byFlowCode',
          showTimeType: this.groupDateType,
          flowcode: this.flowCode,
          free1: this.free1
        }
      })
    },
    getLast6MonthsChartData() {
      let params = {
        region: '',
        buliding: '',
        storey: '',
        room: ''
      }
      this.$api.getWorkOrderTypeStatisticsBySpaceRuiAn(params).then((res) => {
        if (res.code == '200') {
          const arr = res.data.list
          const dateArr = []
          const echartsData = []
          // 获取日期 以及 获取工单类型每日对应数据
          for (let i = 0; i < arr.length; i++) {
            if (dateArr.indexOf(arr[i].createDate) === -1) {
              dateArr.push(arr[i].createDate)
            }
            if (!echartsData.length || !echartsData.some((e) => e.name === arr[i].workTypeName)) {
              echartsData.push({
                name: arr[i].workTypeName,
                value: [arr[i].workOrderNum]
              })
            } else {
              for (let j = 0; j < echartsData.length; j++) {
                if (echartsData[j].name === arr[i].workTypeName) {
                  echartsData[j].value.push(arr[i].workOrderNum)
                  break
                }
              }
            }
          }
          this.initlast6monthChart(dateArr, echartsData)
        }
      })
    },
    initlast6monthChart(dateArr, echartsData) {
      const getchart = echarts.init(document.getElementById('last6monthChart'))
      var color = ['rgba(31, 250, 255, 0.3)', 'rgba(255, 227, 166, 0.28)', 'rgba(147, 130, 255, 0.26)', 'rgba(0, 248, 114, 0.24)', 'rgba(46, 119, 251, 0.3)']
      var borderColor = ['rgba(31, 250, 255, 1)', 'rgba(255, 227, 166, 1)', 'rgba(147, 130, 255, 1)', 'rgba(0, 248, 114, 1)', 'rgba(46, 119, 251, 1)']
      let barcolor = ['#3562DB', '#08CB83', '#FF6461', '#FF9435', '#0CA6ED']
      const seriesObj = []
      echartsData.forEach((item, index) => {
        console.log(item.value, '3333')
        const randomRgbColor = this.$tools.randomRgbColor('array')
        seriesObj.push({
          name: item.name,
          type: 'bar',
          // stack: 'total',
          barWidth: '8%',
          smooth: true,
          showAllSymbol: true,
          symbol: 'circle',
          symbolSize: 8,
          itemStyle: {
            color: barcolor[index],
            // borderColor: '#f1f1f1',
            borderWidth: 1
          },
          lineStyle: {
            normal: {
              width: 2
              // color: borderColor[index] ?? randomRgbColor[1]
            }
          },
          areaStyle: {
            // 区域填充样式
            normal: {
              color: new echarts.graphic.LinearGradient(
                0,
                0,
                0,
                1,
                [
                  {
                    offset: 0,
                    color: borderColor[index] ?? randomRgbColor[1]
                  },
                  {
                    offset: 1,
                    color: color[index] ?? randomRgbColor[0]
                  }
                ],
                false
              )
            }
          },
          data: item.value
          // markLine: {
          //   label: {
          //     show: false
          //   },
          //   emphasis: {
          //     label: {
          //       show: true,
          //       position: 'right',
          //       color: '#3a62d8',
          //       backgroundColor: '#fff',
          //       fontSize: 16
          //     }
          //   },
          //   data: [{ type: 'average', name: 'Avg' }]
          // }
        })
      })
      const option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: '#fff',
          textStyle: {
            color: '#000'
          },
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        legend: {
          data: Array.from(echartsData, ({ name }) => ({
            name: name,
            icon: 'rect' // 这里使用 'circle' 作为图标类型示例
          })),
          type: 'scroll',
          orient: 'horizontal',
          right: '50',
          top: '0',
          itemGap: 16,
          itemWidth: 15,
          itemHeight: 15,
          textStyle: {
            color: '#000'
          }
        },
        grid: {
          left: '2%',
          right: '0%',
          bottom: '3%',
          top: '18%',
          width: '90%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: dateArr
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            show: true,
            textStyle: {
              color: '#000'
            }
          },
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: '#fff',
              width: '1'
            }
          },
          splitLine: {
            lineStyle: {
              color: '#eee',
              width: 1,
              type: 'solid'
            }
          },
          axisTick: {
            show: false
          }
        },
        series: seriesObj
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    getServiceOverviewChartData() {
      let params = {
        region: '',
        buliding: '',
        storey: '',
        room: ''
      }
      this.$api.getWorkOrder(params).then((res) => {
        if (res.code == '200') {
          this.initServiceOverviewChart(res.data.list)
        }
      })
    },
    initServiceOverviewChart(chartData) {
      console.log('chartData', chartData)
      let myChart = echarts.init(document.getElementById('serviceOverviewChart'))
      let pieData = []
      chartData.forEach((i) => {
        const item = {}
        item.value = i.workNum || 0
        item.name = i.workTypeName
        item.workTypeCode = i.workTypeCode
        item.label = {}
        pieData.push(item)
      })
      pieData.sort((a, b) => a.value - b.name)
      // if (pieData.length > 0) {
      //   pieData[0].label = {
      //     show: false,
      //     position: 'center',
      //     fontSize: 14,
      //     color: '#989898',
      //     formatter(params) {
      //       let text = params.data.name
      //       let value_format = params.data.value
      //       let proportion = params.percent
      //       return (text = `{time|${text}}\n ${proportion}%`)
      //     },
      //     rich: {
      //       time: {
      //         fontSize: 30,
      //         color: '#666',
      //         lineHeight: 35
      //       }
      //     }
      //   }
      // }
      myChart.setOption({
        tooltip: {
          trigger: 'item'
        },
        legend: {
          show: false,
          type: 'scroll',
          orient: 'vertical',
          top: '2%',
          right: '8%',
          selected: {},
          formatter: function (name) {
            var oa = pieData
            var num = oa.reduce((sum, e) => sum + e.value, 0)
            for (var i = 0; i < pieData.length; i++) {
              if (name === oa[i].name) {
                return ' ' + name + '(' + oa[i].value + '单' + ')' + '    ' + ((oa[i].value / num) * 100).toFixed(2) + '%'
              }
            }
          }
        },
        series: [
          {
            name: '',
            type: 'pie',
            radius: '60%',
            center: ['50%', '50%'],
            avoidLabelOverlap: false,
            label: {
              normal: {
                show: true,
                position: 'outside',
                formatter: function (params) {
                  console.log('params', params)
                  return params.name + ' ' + params.percent + '%' + ' ' + params.value + '件'
                }
              }
            },
            emphasis: {},
            labelLine: {
              show: true
            },
            data: pieData
          }
        ]
      })
    },
    echartsResize() {
      let echartsDom = this.echartsDom
      this.$nextTick(() => {
        setTimeout(() => {
          echartsDom.forEach((item) => {
            echarts.init(document.getElementById(item)).resize()
          })
        }, 100)
      })
    },
    handleJumpAll(row) {
      console.log(row)
      let showTimeTypeDict = {
        day: '1',
        week: '6',
        month: '2',
        year: '3'
      }
      this.$router.push({
        path: '/maintenanceService/workOrderTable',
        query: {
          typeName: 'byteam',
          showTimeType: showTimeTypeDict[this.groupDateType],
          designateDeptCode: row.deptCode
        }
      })
    },
    handleJumpFinish(row) {
      let showTimeTypeDict = {
        day: '1',
        week: '6',
        month: '2',
        year: '3'
      }
      this.$router.push({
        path: '/maintenanceService/workOrderTable',
        query: {
          typeName: 'byteam',
          showTimeType: showTimeTypeDict[this.groupDateType],
          designateDeptCode: row.deptCode,
          flowcode: '5'
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.full-style {
  width: 100%;
  height: 100%;
}

.statistics {
  display: flex;
  justify-content: space-between;
  height: 48%;
  flex-wrap: wrap;
}

.statistics .item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-around;
  width: 24%;
  height: 100%;
  background-color: rgb(53 98 219 / 6%);
  margin-right: 8px;
  border-radius: 4px;
}

.statistics .item span:nth-child(2) {
  font-size: 18px;
  color: #3562db;
  font-weight: 700;
}

.statistics .hover-style {
  // background-color: #fff;
  // box-shadow: 0 0 10px 0 rgba(32, 56, 114, 0.13);
  // border-bottom: 3px solid #3562db;
}

.pointer-style {
  cursor: pointer;
}

.statistics .pure {
  background-color: #fff;
}

.statistics .pure span:nth-child(2) {
  color: #121f3e;
}

.data-btns {
  position: absolute;
  right: 7%;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &>span {
    width: 44px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    margin: 0 4px;
    background-color: #f6f5fa;
    font-size: 14px;
    font-family: 'PingFang SC-Medium', 'PingFang SC';
    border: none;
    border-radius: 2px;
    color: #7f848c;
    cursor: pointer;
  }

  .active-btn {
    background-color: #e6effc !important;
    color: #3562db !important;
    border-color: #e6effc !important;
  }
}

.btns {
  position: absolute;
  width: 140px;
  height: 24px;
  right: 3%;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 99999;
}

.btns>span {
  width: 30%;
  background-color: #f6f5fa;
  font-size: 12px;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #ededf5;
  color: #7f848c;
  cursor: pointer;
}

.active-btn {
  background-color: #e6effc !important;
  color: #3562db !important;
  border-color: #e6effc !important;
}

.cost-title>span {
  font-size: 15px;
  color: #666666;
}

.top-right .content img {
  width: 40px;
  height: 38px;
}

.cost-num {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 24px;
}

.cost-num>span>span:nth-child(1) {
  font-size: 28px;
  color: #3562db;
  font-weight: 700;
  margin-right: 5px;
}

.cost-num>span>span:nth-child(2) {
  font-size: 14px;
  color: #ccced3;
  font-weight: 500;
}

.cost-box {
  max-width: 80%;
  margin: 0 auto;
  padding: 26px;
  background-color: #faf9fc;
  border-radius: 4px;
  transform: translateY(40px);
}

.cost-box img {
  width: 40px;
}

.count-box {
  width: 100%;
  min-height: 120px;
  display: flex;
  justify-content: space-between;
  padding: 0 8px;
  box-sizing: border-box;
  // align-items: center;
  // flex-wrap: wrap;
}

.response {
  background-color: #faf9fc;
  width: 48%;
  // display: flex;
  // flex-wrap: nowrap;
  padding: 16px;
  // align-items: center;
}

.response img {
  width: 60px;
  height: 60px;
  margin-right: 8px;
}

.response>div:nth-child(1) {
  // width: 60%;
  // height: 73%;
  display: flex;
  align-items: center;
  // justify-content: space-between;
}

.response>div:nth-child(2) {
  // width: 30%;
}

.response .desc {
  font-size: 14px;
}

.response .time {
  margin-top: 6px;
  margin-bottom: 8px;
  font-size: 20px;
  font-weight: 700;
}

.val-box {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}

.average {
  display: flex;
  justify-content: space-between;
  align-items: center;
  // height: 58%;
}

.average img {
  width: 20px;
  height: 20px;
}

.average>span:nth-child(1) {
  font-size: 12px;
  color: #414653;
}

.average>span:nth-child(2) {
  font-size: 16px;
  display: flex;
  align-items: center;
}

.average>span:nth-child(2)>span:nth-child(1) {
  margin-right: 8px;
}

.order-num {
  width: 98%;
  padding: 16px;
  box-sizing: border-box;
  background-color: #faf9fc;
}

.order-num .content {
  width: 100%;
  display: flex;
  margin-bottom: 16px;
  justify-content: space-between;
}

.order-num .content .left-cont {
  margin-right: 30%;
}

.order-num .content .left-cont>span:nth-child(1),
.order-num .content .right-cont>span:nth-child(1) {
  font-size: 14px;
  color: #666666;
  font-weight: 500;
  margin-bottom: 5px;
}

.order-num .content .left-cont>span:nth-child(2)>span:nth-child(1),
.order-num .content .right-cont>span:nth-child(2)>span:nth-child(1) {
  font-size: 24px;
  font-weight: 700;
  margin-right: 5px;
}

.order-num .content .left-cont>span:nth-child(2)>span:nth-child(2),
.order-num .content .right-cont>span:nth-child(2)>span:nth-child(2) {
  font-size: 14px;
  color: #ccced3;
  font-weight: 500;
}

.el-progress {
  margin-bottom: 8px;
}

.chart-box {
  display: flex;
  justify-content: space-between;
  width: 100%;
  min-height: 100px;
  height: 160px;
  box-sizing: border-box;
  margin: 8px 0;
  padding: 0 8px;
}

.total-text {
  // text-align: center;
  display: inline-block;
  background-color: #faf9fc;
  padding: 6px 16px;
  position: absolute;
  top: 0;
  left: 0;
}

.total-text img {
  width: 14px;
  height: 18px;
  margin-right: 3px;
}

.total-text>span:nth-child(2) {
  color: #3e62d5;
}

.red-text {
  color: #fa403c;
}

.green-text {
  color: #00bc6d;
}

.chart-box>div {
  width: 48%;
  background-color: #faf9fc;
}

.chart-legend {
  width: 100%;
  position: absolute;
  bottom: 12%;
  left: 0;
  text-align: center;
}

.legend-text {
  color: #3562db;
  font-size: 20px;
  margin: 0 5px;
  font-weight: bold;
}

.square-icon {
  position: relative;
}

.square-icon::before {
  content: '';
  position: absolute;
  width: 10px;
  height: 10px;
  background-color: #3562db;
  left: -15px;
  top: 5px;
}

::v-deep .el-progress-bar__outer {
  border-radius: 0;
}

.num-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  height: 40%;
  min-height: 200px;
}

.num-box>div {
  width: 49%;
  height: 100%;
  padding: 0 20px;
}

.left-num-box {
  background-color: #3562db;
  border-radius: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #fff;
}

.left-num-box img {
  width: 40px;
  height: 40px;
}

.left-num-box>div {
  display: flex;
  flex-direction: column;
}

.right-num-box {
  height: 40%;
  padding: 0 !important;
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  justify-content: space-between;
}

.right-num-box>div {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 32%;
  padding: 0 16px;
}

.right-num-box>div>span:nth-child(1) {
  color: #121f3e;
  font-size: 15px;
}

.right-num-box>div>span:nth-child(2) {
  font-size: 18px;
  font-weight: bold;
}

.right-num-box>div:nth-child(1) {
  background: rgba(53, 98, 219, 0.1);
}

.right-num-box>div:nth-child(2) {
  background: rgba(255, 100, 97, 0.1);
}

.right-num-box>div:nth-child(3) {
  background: rgba(255, 148, 53, 0.1);
}

.rate-box {
  width: 100%;
  height: 40%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

.rate-box>div {
  width: 24%;
  height: 100%;
  background-color: #faf9fc;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.rate-box>div>div:nth-child(1) {
  color: #666;
  margin-bottom: 8px;
}

.unit {
  color: #666;
  margin-left: 3px;
}

.rate-val {
  font-size: 20px;
  color: #121f3e;
  font-weight: bold;
}

::v-deep .el-table__cell {
  border-bottom: none !important;
}
</style>
