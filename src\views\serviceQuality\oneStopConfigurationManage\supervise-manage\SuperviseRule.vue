<script>
import tableListMixin from '@/mixins/tableListMixin'
import { SuperviseDictType } from './constant'
export default {
  name: 'SuperviseRule',
  components: {
    SuperviseRuleConfig: () => import('./components/SuperviseRuleConfig.vue')
  },
  mixins: [tableListMixin],
  data() {
    return {
      loadingStatus: false,
      tableData: [],
      // 基础表单数据
      baseFormModel: {
        // 服务事项
        serviceType: [],
        // 工单类型
        workType: ''
      },
      baseFormRules: {
        workType: [{ required: true, message: '请选择工单类型' }],
        serviceType: [{ required: true, type: 'array', message: '请选择服务事项', trigger: 'change' }]
      },
      // 动态表单数据
      dynamicFormModel: {
        // 超时类型
        overTimeType: []
      },
      dynamicFormRules: {
        overTimeType: [{ required: true, type: 'array', message: '请选择超时类型' }],
        overTime: [{ required: true, message: '请输入超时时间' }],
        notifyWays: [{ required: true, type: 'array', message: '请选择通知方式' }],
        userName: [{ required: true, message: '请选择通知人员' }],
        userPhone: [
          { required: true, message: '请输入联系方式' },
          { required: true, message: '请输入联系方式' }
        ]
      },
      // 表单选项
      options: {
        // 项目类型
        projectList: [],
        // 超时类型选项
        overTimeList: [],
        // 通知方式
        notifyList: [],
        // 超时时间单位选项
        unitList: [],
        // 服务类型
        serviceList: [],
        // 工单类型
        workTypeList: []
      },
      // 是否可以编辑
      editable: false,
      // 记录ID
      $id: ''
    }
  },
  computed: {
    // 当前使用的规则
    ruleList() {
      // 按照默认的超时类型顺序返回选中的类型
      return this.options.overTimeList.filter((it) => this.dynamicFormModel.overTimeType.includes(it.dictCode))
    }
  },
  created() {
    this.getOptions()
  },
  mounted() {
    this.editable = this.$route.query.readonly === false
    this.$id = this.$route.query.id
    if (this.$id) {
      this.getDetail()
    }
  },
  methods: {
    getOptions() {
      // 获取项目类型
      // this.$api.getProjectList({ page: 1, pageSize: 9999 }).then((res) => {
      //   if (res.code === '200') {
      //     this.options.projectList = res.data.records
      //   }
      // })
      // 获取超时类型、超时时间单位、通知类型字典
      const types = [SuperviseDictType.NotifyType, SuperviseDictType.OverTimeType, SuperviseDictType.TimeUnit]
      this.$api.dictQueryByTypes({ dictTypeList: types }).then((res) => {
        if (res.code === '200') {
          res.data.forEach((it) => {
            if (it.dictType === SuperviseDictType.NotifyType) {
              this.options.notifyList = it.dictList
            } else if (it.dictType === SuperviseDictType.TimeUnit) {
              this.options.unitList = it.dictList
            } else if (it.dictType === SuperviseDictType.OverTimeType) {
              this.options.overTimeList = it.dictList
            }
          })
        }
      })

      // 获取工单类型选项
      this.$api.workOderType({ page: 1, pageSize: 999, ssmType: 2 }).then((res) => {
        if (res.code === '200') {
          this.options.workTypeList = res.data
        }
      })
    },
    // 服务事项
    getItemList(id) {
      let params = {
        id: id,
        nodeLevel: 1
      }
      this.$api.getItemList(params).then((res) => {
        if (res.code === '200') {
          this.options.serviceList = res.data.list.filter((it) => it.parent === '#')
        }
      })
    },
    // 获取分页列表数据
    getDetail() {
      this.loadingStatus = true
      this.$api
        .superviseGetById({ id: this.$id })
        .then((res) => {
          if (res.code === '200') {
            this.baseFormModel.workType = res.data.workTypeCode
            this.getItemList(this.baseFormModel.workType)
            this.baseFormModel.serviceType = res.data.itemTypeCode.split(',')
            this.dynamicFormModel.overTimeType = res.data.timeoutType.split(',')
            res.data.pushConfigDetailsList.forEach((it) => this.onAppendRule(it.timeoutType, it))
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取详情失败'))
        .finally(() => (this.loadingStatus = false))
    },
    onSubmit() {
      this.$refs.fromBaseRef
        .validate()
        .then(this.$refs.formDynamicRef.validate)
        .then(this.getConfigData)
        .catch(() => Promise.reject())
        .then((res) => {
          this.loadingStatus = true
          const params = {
            userId: this.$store.state.user.userInfo.user.staffId,
            userName: this.$store.state.user.userInfo.user.staffName,
            workTypeCode: this.baseFormModel.workType,
            workTypeName: this.options.workTypeList.find((it) => it.workTypeCode === this.baseFormModel.workType).workTypeName,
            itemTypeCode: this.baseFormModel.serviceType.join(),
            itemTypeName: this.options.serviceList
              .filter((it) => this.baseFormModel.serviceType.includes(it.id))
              .map((it) => it.text)
              .join(),
            timeoutType: this.dynamicFormModel.overTimeType.join(),
            timeoutTypeName: this.ruleList.map((it) => it.dictName).join(),
            pushConfigDetailsList: res.map((it) => it.data)
          }
          // 根据路由参数判断是否编辑
          if (this.$id) {
            params.id = this.$id
            return this.$api.superviseUpdate(params)
          } else {
            return this.$api.superviseSave(params)
          }
        })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('操作成功')
            this.$router.back()
          } else {
            throw res.message || '操作失败'
          }
        })
        .catch((err) => err && this.$message.error(err))
        .finally(() => (this.loadingStatus = false))
    },
    // 获取配置的数据
    getConfigData() {
      const taskList = this.$refs.formDynamicRef.$children
        // 查找是督办规则配置的组件
        .filter((it) => it.$options?.name === 'SuperviseRuleConfig')
        // 调用配置项的验证方法，并获取转换数据格式 {type：超时类型，data：配置项的数据}
        .map((instance) => instance.validate().then((data) => ({ type: instance.type, data })))
      return Promise.all(taskList)
    },
    onBack() {
      this.$router.back()
    },
    /**
     * 增加一组超时规则
     * @param overTimeType 超时类型
     * @param rule 规则,默认为空规则
     */
    onAppendRule(overTimeType, rule = {}) {
      const config = this.dynamicFormModel[overTimeType] || []
      config.push(rule)
      this.$set(this.dynamicFormModel, overTimeType, config)
    },
    onRemove(type, index) {
      if (index < 1) return
      this.dynamicFormModel[type].splice(index, 1)
    },
    // 超时类型改变时处理
    onOverTimeTypeChange(type, checked) {
      if (checked) {
        this.onAppendRule(type)
      } else {
        this.dynamicFormModel[type] = []
      }
    },
    // 当基础信息变更，检查规则是否存在
    onBaseInfoChange(val, type) {
      if (type) {
        this.options.serviceList = []
        this.getItemList(val)
      }
      if (!this.baseFormModel.workType || !this.baseFormModel.serviceType.length) {
        return
      }
      const params = {
        workTypeName: this.options.workTypeList.find((it) => it.workTypeCode === this.baseFormModel.workType).workTypeName,
        itemTypeCode: this.baseFormModel.serviceType.join(),
        itemTypeName: this.options.serviceList
          .filter((it) => this.baseFormModel.serviceType.includes(it.id))
          .map((it) => it.text)
          .join()
      }
      this.$api
        .superviseIsExist(params)
        .then((res) => {
          if (res.code !== '200') {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg))
    }
  }
}
</script>
<template>
  <PageContainer v-loading="loadingStatus" footer class="supervise-rule" :class="{ readonly: !editable }">
    <template #content>
      <div class="supervise-rule__title">
        <div>
          <svg-icon name="right-arrow" />
          基本信息
        </div>
      </div>
      <el-form ref="fromBaseRef" :model="baseFormModel" :disabled="!editable" :rules="baseFormRules" label-width="90px" style="margin-top: 10px">
        <el-row>
          <el-col :span="8"> </el-col>
          <el-col :span="8">
            <el-form-item prop="workType" label="工单类型">
              <el-select v-model="baseFormModel.workType" @change="onBaseInfoChange($event, '1')">
                <el-option v-for="item in options.workTypeList" :key="item.workTypeCode" :label="item.workTypeName" :value="item.workTypeCode"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="serviceType" label="服务事项">
              <el-select v-model="baseFormModel.serviceType" multiple :collapse-tags="editable" @change="onBaseInfoChange" :disabled="baseFormModel.workType == ''">
                <el-option v-for="item in options.serviceList" :key="item.id" :label="item.text" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-form ref="formDynamicRef" :model="dynamicFormModel" :rules="dynamicFormRules" :disabled="!editable" label-width="90px">
        <el-form-item prop="overTimeType" label="超时类型">
          <el-checkbox-group v-model="dynamicFormModel.overTimeType">
            <el-checkbox v-for="item in options.overTimeList" :key="item.dictCode" :label="item.dictCode" @change="onOverTimeTypeChange(item.dictCode, $event)">
              {{ item.dictName }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <template v-if="ruleList.length">
          <div v-for="{ dictCode, dictName } in ruleList" :key="dictCode" class="supervise-rule__item">
            <div class="supervise-rule__title">
              <div class="supervise-rule__title__info">
                <svg-icon name="right-arrow" />
                {{ dictName }}
              </div>
              <el-button v-if="editable" type="primary" plain icon="el-icon-plus" @click="onAppendRule(dictCode)">增加 </el-button>
            </div>
            <div v-for="(rule, index) in dynamicFormModel[dictCode]" :key="index" class="supervise-rule__content">
              <SuperviseRuleConfig
                :readonly="!editable"
                :data="rule"
                :type="dictCode"
                :option-ways="options.notifyList"
                :option-unit="options.unitList"
                class="supervise-rule__config"
              />
              <el-button v-if="editable" type="text" :disabled="index === 0" class="supervise-rule__delete text-red" @click="() => dynamicFormModel[dictCode].splice(index, 1)">
                <i class="el-icon-delete" />
                删除
              </el-button>
            </div>
          </div>
        </template>
        <el-empty v-else style="margin-top: 10vh" description="请先选择超时类型"></el-empty>
      </el-form>
    </template>
    <template #footer>
      <el-button type="primary" plain @click="onBack">取消</el-button>
      <el-button v-if="editable" type="primary" @click="onSubmit">保存</el-button>
    </template>
  </PageContainer>
</template>
<style scoped lang="scss">
.supervise-rule {
  ::v-deep(.container-content) {
    padding: 16px;
    height: 100%;
    overflow: auto;
    background-color: #fff;
  }
  &__item {
    margin-bottom: 10px;
  }
  &__title {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  &__content {
    margin-top: 10px;
    padding-top: 16px;
    position: relative;
    background: #faf9fc;
    border-radius: 4px;
    overflow: hidden;
  }
  &__delete {
    position: absolute;
    bottom: 32px;
    right: 16px;
  }
  &.readonly {
    ::v-deep(.el-form-item) {
      margin-bottom: 10px;
      .el-form-item__label::before {
        display: none;
      }
      .el-input__inner {
        background: transparent;
        border: none;
        color: inherit;
      }
      .el-input__suffix {
        display: none;
      }
      .el-input.el-input-group {
        width: auto;
        .el-input__inner {
          width: 60px;
          padding-right: 5px;
        }
        .el-input-group__append {
          padding-left: 5px;
          border: none;
          background: transparent;
        }
      }
      .el-checkbox.is-checked .el-checkbox__label {
        color: inherit;
      }
      input,
      .el-checkbox {
        pointer-events: none;
      }
    }
  }
}
</style>
