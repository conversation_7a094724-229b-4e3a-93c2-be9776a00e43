<template>
  <div>
    <div class="quesName">
      <span v-show="pathName[diff].isQuestionNum">{{ index + 1 }}、</span>
      {{ checkboxOption.name }}
      <span v-show="pathName[diff].isShowSubjectType" class="chexMinMax">
        [多选题]
        <span v-if="checkboxOption.minSelect > 0">最少选{{ checkboxOption.minSelect }}个</span>
        <span v-if="checkboxOption.maxSelect > 0">最多选{{ checkboxOption.maxSelect }}个</span>
      </span>
      <span v-if="checkboxOption.isMust == 1" class="starRed">*</span>
    </div>
    <div v-for="(childitem, childindex) in checkboxOption.optionsData" :key="childindex" :class="getStyle(checkboxOption.rowCount)">
      <el-checkbox
        v-model="checkedOption"
        :label="childitem.id"
        :disabled="pathName[diff].isDisable"
        style="display: block; margin-bottom: 19px;"
        @change="handleCheckboxChange(childitem, childindex, $event)">{{ childitem.name }}</el-checkbox
      >
      <el-input
        v-if="childitem.isInput == 1"
        v-model="answerInfo[childindex].textValue"
        :disabled="pathName[diff].isDisable"
        class="isput"
        @focus="handleCheckboxChange(childitem, childindex)"
      ></el-input>
    </div>
    <!-- <div class="belowTips">最少选{{checkboxOption.minSelect}}个</div>
    <div class="belowTips">最多选{{checkboxOption.maxSelect}}个</div>-->
  </div>
</template>

<script>
export default {
  props: {
    // 当前题目的所有相关信息
    previewOption: {
      type: Object
    },
    // 该题目在当前问卷中的序号
    index: {
      type: Number
    },
    // 是否显示题目序号，true为显示，false为不显示
    isQuestionNum: {
      type: Boolean
    },
    // 表示用于在哪个页面显示该题目时的相关设置信息
    pathName: {
      type: Object
    },
    diff: {
      type: String
    }
  },
  data() {
    return {
      currentPathName: '',
      checkboxOption: {},
      answerVal: [],
      answerInfo: [], // 保存表单数据，用于显示答题结果
      checkedOption: [], // 保存checkbox的选中项
      checkboxOption1: {
        minSelect: 2,
        maxSelect: 3,
        name: '2.多选题击一为ID哦iwejdooiadio',
        optionsData: [
          {
            name: '还是啥',
            isInput: 1
          },
          {
            name: '看到啥',
            isInput: 1
          },
          {
            name: '参加反馈啥',
            isInput: 0
          },
          {
            name: '发出发啥',
            isInput: 0
          }
        ],
        isMust: 0,
        rowCount: 5 // 排序方式 5.横排 1.竖排 2.一排2个 3.一排3个 4.一排4个
      },
      question: {
        1: 'quesOption',
        2: 'quesOption quesOptiontwo',
        3: 'quesOption quesOptionthree',
        4: 'quesOption quesOptionfour',
        5: 'quesOption quesOptionfive'
      }
    }
  },
  mounted() {
    this.answerInfo = this.previewOption.optionsData.map((element) => {
      return {
        optionId: element.id, // 选项id
        optionName: '', // 选项label
        textValue: '' // 保存选项后的输入框的值
      }
    })
    if (this.pathName[this.diff].isSetDefaultValue) {
      if (this.previewOption.answers.length > 0) {
        this.checkedOption = this.previewOption.answers.map((item) => item.optionId)
        this.answerInfo.forEach((item) => {
          this.previewOption.answers.forEach((answerOption) => {
            if (item.optionId === answerOption.optionId) {
              item.textValue = answerOption.textValue
            }
          })
        })
      }
    }
  },
  created() {
    this.checkboxOption = this.previewOption
  },

  methods: {
    validedForm() {
      let validedResult = true
      this.$refs['checkboxForm'].validate((result) => {
        if (result) {
          validedResult = false
        } else {
          validedResult = true
        }
      })
      return validedResult
    },
    checkValided() {
      if (this.checkboxOption.isMust === 0) {
        return false
      }
      // 判断当前题目是否必填，必填时才需要判断是否已答
      if (this.checkboxOption.isMust !== 1) {
        return true
      }
      // 判断当前题目是否已答,true为已做答，false为未做答
      if (!this.isAnswer) {
        return true
      }
      return this.validedForm()
      // 判断选中的选项格式是否满足设置的最多选择和最少选择
      if (
        (this.checkboxOption.maxSelect !== this.checkboxOption.minSelect && this.checkedOption.length > this.checkboxOption.maxSelect) ||
        this.checkedOption.length < this.checkboxOption.minSelect
      ) {
        return true
      }
      return false
    },
    doValidedFlag() {
      return this.checkValided()
    },
    doValided() {
      this.isValided = this.checkValided()
      return this.isValided
    },
    getStyle(val) {
      return this.question[val]
    },
    handleCheckboxChange(optionItem, optionIndex, val) {
      const value = this.checkedOption
      // val为undefined表示输入框获取到焦点，此时默认选中当前行的checkbox
      if (val === undefined) {
        const indexValue = this.checkedOption.indexOf(optionItem.id)
        indexValue === -1 && this.checkedOption.push(optionItem.id)
        this.answerInfo[optionIndex].optionName = optionItem.name
      } else {
        this.answerInfo[optionIndex].optionName = val ? optionItem.name : ''
      }
      // 获取checkbox选中的值，当optionId和optionName同事存在当前的checkbox值才有效
      const answerArray = this.answerInfo.filter((item) => {
        return item.optionId && item.optionName
      })
      this.answerVal = answerArray.map((item) => {
        return {
          pvqId: localStorage.getItem('questId'), // 问卷id
          questionId: this.checkboxOption.id, // 题目id
          questionType: this.checkboxOption.type, // 题目类型
          ...item
        }
      })
      this.$parent.$parent.$parent.addAnswer && this.$parent.$parent.$parent.addAnswer('checkBoxFun', optionItem, val)
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
.quesName {
  font-size: 16px;
  margin-bottom: 10px;
}

.starRed {
  color: red;
}

.quesOption {
  padding: 13px 0;
  font-size: 16px;
  background-color: #fff;
  position: relative;
}

.quesOptiontwo {
  display: inline-block;
  width: 50%;
}

.quesOptionthree {
  display: inline-block;
  width: 33%;
}

.quesOptionfour {
  display: inline-block;
  width: 25%;
}

.quesOptionfive {
  display: inline-block;
  padding-right: 30px;
}

input[type="text"]:focus {
  border: 1px solid #2cc7c5;
  outline: none;
}

.chexMinMax {
  margin-left: 3px;
}

.belowTips {
  color: red;
  font-size: 14px;
}

.isput {
  width: 200px;
  margin-left: 24px;
}

div ::v-deep .el-checkbox__label {
  white-space: normal;
  word-break: break-all;
}
</style>
