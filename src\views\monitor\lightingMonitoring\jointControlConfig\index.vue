<template>
  <!-- 联控配置 -->
  <PageContainer>
    <div slot="header" class="control-btn-header">
      <p class="title">联控配置</p>
      <el-button type="primary" @click="() => {addJointControlStrategy = true; configId= ''}">新建联控策略</el-button>
    </div>
    <div slot="content" class="table-content">
      <TablePage
        ref="table"
        v-loading="tableLoading"
        :showPage="true"
        :tableColumn="tableColumn"
        :data="tableData"
        height="calc(100% - 40px)"
        :pageData="pageData"
        @pagination="paginationChange"
      />
      <addJointControlStrategyDialog v-if="addJointControlStrategy" :id="configId" :visible.sync="addJointControlStrategy" />
    </div>
  </PageContainer>
</template>

<script lang="jsx">
export default {
  name: 'jointControlConfig',
  components: {
    addJointControlStrategyDialog: () => import('../components/addJointControlStrategyDialog')
  },
  data() {
    return {
      configId: '',
      addJointControlStrategy: false, // 新建联控策略
      tableLoading: false,
      tableData: [],
      pageData: {
        page: 1,
        pageSize: 15,
        total: 0
      },
      tableColumn: [
        {
          prop: 'serialNumber',
          label: '序号',
          width: 80,
          formatter: (scope) => {
            return (this.pageData.page - 1) * this.pageData.pageSize + scope.$index + 1
          }
        },
        {
          prop: 'controlName',
          label: '规则名称'
        },
        {
          prop: '',
          label: '执行时间',
          formatter: (scope) => {
            return scope.row.startTime + '-' + scope.row.endTime
          }
        },
        {
          prop: 'illuminanceHarvesterName',
          label: '传感器设备'
        },
        {
          prop: 'loopCount',
          label: '回路数量',
          formatter: (scope) => {
            return scope.row.startTime + '-' + scope.row.endTime
          }
        },
        {
          prop: '',
          label: '联控规则',
          formatter: (scope) => {
            return scope.row.linkageRuleName + scope.row.compare + scope.row.value + 'lx'
          }
        },
        {
          prop: '',
          label: '控制设定',
          formatter: (scope) => {
            return (scope.row.controlSetting == 0 ? '亮度控制：' : '开关控制：') + scope.row.controlSettingName
          }
        },
        {
          width: 100,
          prop: 'operation',
          label: '控制',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span class="operationBtn-span" style="color: #121F3E;  margin-right: 10px;" onClick={() => this.configEdit('edit', row.row)}>编辑</span>
                <span class="operationBtn-span" style="color: #fa403c" onClick={() => this.configEdit('del', row.row)}>删除</span>
              </div>
            )
          }
        }
      ]
    }
  },
  watch: {
    addJointControlStrategy(val) {
      if (!val) {
        this.getListData()
      }
    }
  },
  created() {
    this.getListData()
  },
  methods: {
    configEdit(type, row) {
      if (type == 'edit') {
        this.addJointControlStrategy = true
        this.configId = row.id
      } else {
        this.$api.DeleteLightingControl({id: row.id, jsonId: row.jsonId}, {'operation-type': 3}).then(res => {
          if (res.code == 200) {
            this.getListData()
          }
        })
      }
    },
    getListData() {
      this.tableLoading = true
      this.$api.GetLightingControlList({page: this.pageData.page, pageSize: this.pageData.pageSize}).then(res => {
        let list = []
        this.tableLoading = false
        if (res.code == 200) {
          res.data.list.forEach(item => {
            item.list.forEach(v => {
              list.push({
                ...v,
                ...item
              })
            })
          })
          this.tableData = list
          this.pageData.total = res.data.total
        }
      })
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getListData()
    }
  }
}

</script>

<style lang="scss" scoped>
.control-btn-header {
  border-radius: 4px;
  height: 56px;
  background-color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 17px;
  color: #333333;
  .title {
    margin: 0;
    font-size: 18px;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
  }
}
.table-content {
  margin-top: 15px;
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 15px);
  padding: 10px;
}
</style>
