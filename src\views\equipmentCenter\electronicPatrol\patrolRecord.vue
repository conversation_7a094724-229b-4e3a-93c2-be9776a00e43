<template>
  <PageContainer>
    <div slot="content" class="role-content" style="height: 100%">
      <div class="role-content-right">
        <div style="height: 100%">
          <div class="search-from">
            <div>
              <el-input v-model.trim="routesName" placeholder="请输入线路名称" style="width: 240px;margin-right: 10px;"
                clearable maxlength="25" onkeyup="if(value.length>25)value=value.slice(0,25)"></el-input>
              <el-date-picker v-model="dataRange" type="daterange" unlink-panels range-separator="至"
                start-placeholder="开始时间" end-placeholder="结束时间" value-format="yyyy-MM-dd" suffix-icon="el-icon-date"
                :picker-options="pickerOptions" style="width: 240px;margin-right: 10px;">
              </el-date-picker>
              <el-button type="primary" plain @click="resetForm">重置</el-button>
              <el-button type="primary" @click="searchForm">查询</el-button>
            </div>
            <div style="display: flex;">
              <el-button type="primary" class="rightBtn" icon="el-icon-download" @click="handleOperation()"> 导入
              </el-button>
            </div>
          </div>
          <div class="contentTable">
            <div class="contentTable-main table-content">
              <el-table v-loading="tableLoading" border style="width: 100%" :data="tableData" height="100%" stripe>
                <el-table-column prop="startTime" label="开始时间" show-overflow-tooltip></el-table-column>
                <el-table-column prop="endTime" label="结束时间" show-overflow-tooltip></el-table-column>
                <el-table-column prop="name" label="线路名称" show-overflow-tooltip></el-table-column>
                <el-table-column prop="place" label="地点" show-overflow-tooltip></el-table-column>
                <el-table-column prop="patrolTime" label="巡检时间" show-overflow-tooltip></el-table-column>
                <el-table-column prop="userName" label="人员" show-overflow-tooltip></el-table-column>
                <el-table-column prop="assetName" label="设备名称" show-overflow-tooltip></el-table-column>
                <el-table-column prop="deptName" label="部门" show-overflow-tooltip></el-table-column>
                <el-table-column prop="status" label="考核状态" show-overflow-tooltip></el-table-column>
                <el-table-column prop="planType" label="计划类别" show-overflow-tooltip></el-table-column>
                <el-table-column prop="planMode" label="计划模式" show-overflow-tooltip></el-table-column>
              </el-table>
            </div>
            <div class="contentTable-footer">
              <el-pagination :current-page="pagination.current" :page-sizes="pagination.pageSizeOptions"
                :page-size="pagination.size" :layout="pagination.layoutOptions" :total="pagination.total"
                @size-change="paginationSizeChange" @current-change="paginationCurrentChange">
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
      <!-- 导入数据信息 -->
      <el-dialog v-dialogDrag title="数据导入" :visible.sync="assetsImportdialogVisible" width="40%"
        :before-close="handleAssetsImportClose" class="classify-dialog" custom-class="model-dialog"
        :close-on-click-modal="false">
        <div class="content" style="padding: 10px; display: flex">
          <div>上传附件：</div>
          <div class="upload-file">
            <el-upload ref="uploadFile" action="string" :limit="1" :http-request="httpRequest"
              :beforeUpload="beforeAvatarUpload" :file-list="assetsFileList" :on-exceed="handleExceed"
              :on-remove="handleRemove" accept=".xlsx">
              <div class="leadFile">
                <el-button size="small" type="primary">点击上传</el-button>
              </div>
              <div slot="tip" class="el-upload__tip">只能上传 .xlsx格式</div>
            </el-upload>
          </div>
          <div class="leadFile_item" @click="getTemplateExport">导入模板下载</div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="handleAssetsImportClose">取 消</el-button>
          <el-button type="primary" @click="submitAssetsImport">确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </PageContainer>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin.js'
import store from '@/store/index'
import axios from 'axios'
import qs from 'qs'
export default {
  name: 'patroLine',
  mixins: [tableListMixin,],
  data() {
    return {
      routesName: '', // 路线名称,
      dataRange: [],
      assetsImportdialogVisible: false, // 传资产弹窗
      assetsFileList: [],
      tableLoading: false,
      tableData: [],
      synDialog: false, // 同步弹窗显示
      activities: [],
      drawerPageNo: 1,
      drawerPageSize: 10,
      drawerTotal: 0,
    }
  },
  created() {
    this.getDataList()
  },
  methods: {
    getDataList() {
      let data = {
        currentPage: this.pagination.current,
        size: this.pagination.size,
        routesName: this.routesName,
        startTime: this.dataRange.length ? this.dataRange[0] : '',
        endTime: this.dataRange.length ? this.dataRange[1] : '',
      }
      this.tableLoading = true
      this.$api.patrolRecordList(data).then((res) => {
        if (res.code == '200') {
          this.tableData = res.data.list
          this.pagination.total = parseInt(res.data.total)
        }
      })
      this.tableLoading = false
    },
    // 重置
    resetForm() {
      this.routesName = '',
        this.dataRange = []
      this.assetsFileList = []
      this.pagination.size = 15
      this.pagination.current = 1
      this.getDataList()
    },
    // 查询
    searchForm() {
      this.pagination.current = 1
      this.getDataList()
    },
    // 操作记录分页
    pagingLoad() {
      if (this.activities.length < this.drawerTotal) {
        this.drawerPageNo += 1
      }
    },
    // 导入导出操作
    handleOperation() {
      this.assetsImportdialogVisible = true
    },
    /**
     * 文件
     */
    httpRequest(item) {
      this.assetsFileList.push(item.file)
    },
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
    },
    handleRemove(file, fileList) {
      this.assetsFileList = []
    },
    beforeAvatarUpload(file) {
      const isLt50M = file.size / 1024 / 1024 < 50
      if (!isLt50M) {
        this.$message.error('上传文件大小不能超过 50MB!')
        return false
      }
      if (file.name.indexOf(',') != -1) {
        this.$message.error('非法的文件名')
        return false
      }
    },
    // 模板导出
    getTemplateExport() {
      const userInfo = store.state.user.userInfo.user
      let params = {
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
      axios({
        method: 'post',
        url: __PATH.VUE_ICIS_API + '/electronic_patrols/records_temp_download',
        data: qs.stringify(params),
        responseType: 'blob',
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token,
          'operation-type': 4
        }
      })
        .then((res) => {
          let name = '数据模板.xlsx'
          let blob = new Blob([res.data]) // { type: "application/vnd.ms-excel" }
          let url = window.URL.createObjectURL(blob) // 创建一个临时的url指向blob对象
          // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
          let a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
          // 释放这个临时的对象url
          window.URL.revokeObjectURL(url)
        })
        .catch((res) => {
          this.$message.error('导出失败！')
        })
    },
    // 记录数据导入
    getAssetsImport() {
      let formData = new FormData()
      const userInfo = store.state.user.userInfo.user
      if (this.assetsFileList && this.assetsFileList.length) {
        formData.append('file', this.assetsFileList[0])
      }
      if (this.assetsFileList.length <= 0) {
        this.$message.error('请上传文件')
        return
      }
      this.tableLoading = true
      axios
        .post(__PATH.VUE_ICIS_API + '/electronic_patrols/import_records', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
            Authorization: 'Bearer ' + this.$store.state.user.token
          }
        })
        .then((res) => {
          if (res.data.code == 200) {
            this.resetForm()
            this.$message.success(res.data.message)
          } else {
            this.$message.error(res.data.message)
          }
        })
        .catch((error) => {
          console.log(error, 'errorerrorerror');
        })
      this.assetsImportdialogVisible = false
      this.tableLoading = false
    },
    // 关闭资产上传弹窗
    handleAssetsImportClose() {
      this.assetsImportdialogVisible = false
      this.assetsFileList = []
    },
    // 确定资产上传弹窗
    submitAssetsImport() {
      this.getAssetsImport()
    },
  }
}
</script>
<style lang="scss" scoped>
.el-tree-node__content {
  width: 100%;

  .custom-tree-node {
    display: inline-block;
    width: 100%;
    overflow: hidden;

    .item {
      display: inline-block;
      width: calc(100%);
      // width: calc(100% - 10px);
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
  }
}

::v-deep .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
  color: #3562db;
  background: linear-gradient(to right, #d9e1f8, #fff);
}

::v-deep .el-tree-node__content {
  height: 32px;
}

.role-content {
  height: 100%;
  display: flex;

  .role-content-right {
    height: 100%;
    min-width: 0;
    padding: 20px;
    background: #fff;
    border-radius: 4px;
    flex: 1;


    .search-from {
      padding-bottom: 12px;
      display: flex;
      justify-content: space-between;

      &>div {
        margin-right: 10px;
      }

      &>button {
        margin-top: 12px;
      }
    }

    .contentTable {
      height: calc(100% - 110px);
      display: flex;
      flex-direction: column;

      .contentTable-main {
        flex: 1;
        overflow: auto;
      }

      .contentTable-footer {
        padding: 10px 0 0;
      }
    }
  }

  .content {
    width: 100%;
    max-height: 500px !important;
    overflow: auto;
    background-color: #fff !important;
  }
}

::v-deep .el-tabs__nav-scroll {
  width: 80%;
  margin: 0 auto;
}

::v-deep .el-drawer__open .el-drawer.rtl {
  background: #f6f5fa;
  border-radius: 4px;
}

::v-deep .el-drawer__header {
  height: 56px;
  padding: 15px 20px;
  margin-bottom: 0;
  background: #fff;
  border-bottom: 1px solid $color-text-secondary;
  box-shadow: 0 0 12px 3px rgb(0 0 0 / 10%);

  &>span {
    font-size: 18px;
    font-family: 'PingFang SC-Medium', 'PingFang SC';
    font-weight: 500;
    color: $color-text;
  }

  .el-dialog__close {
    color: $color-text;
    font-weight: 600;
    font-size: 18px;
  }
}

::v-deep .el-drawer__body {
  background-color: #fff;
  margin: 20px;
  padding: 20px 10px;
  height: calc(100% - 80px);
  overflow-y: auto;
}

::v-deep .el-timeline-item__timestamp.is-bottom {
  font-size: 14px;
  position: absolute;
  left: -100px;
  top: -5px;
  font-weight: 600;
  color: #121f3e;
}

::v-deep .el-timeline {
  padding-left: 120px;
}

.timeContent {
  height: 100%;
  overflow: auto;

  .time {
    font-size: 14px;
    font-family: 'PingFang SC-Regular', 'PingFang SC';
    color: #414653;
  }

  .continer {
    display: flex;
    // justify-content: space-between;
    flex-wrap: wrap;

    .item {
      height: 32px;
      flex: 1;
      padding: 0 16px;
      background-color: #faf9fc;
      line-height: 32px;
      white-space: nowrap;
      margin-bottom: 20px;
      margin-right: 10px;
    }

    .itemContent {
      height: 32px;
      width: 220px;
      padding: 0 16px;
      background-color: #faf9fc;
      line-height: 32px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      cursor: pointer;
      margin-right: 10px;
    }
  }
}

::v-deep .el-timeline-item__node--normal {
  background: #fff;
  border: 2px solid #3562db;
}

.noData {
  display: inline-block;
  padding-bottom: 10px;
  width: 100%;
  margin: 0;
  font-size: 14px;
  color: #999;
  text-align: center;
}

.record {
  color: #66b1ff !important;
}

.delete {
  color: red !important;
}

::v-deep .el-tree-node {
  white-space: normal;
  // outline: 0;
}

.rightBtn {
  height: 36px;
  margin: 0;
  margin-right: 10px;
}

.leadFile {
  display: flex;
}

.leadFile_item {
  margin: 10px 35px;
  color: #66b1ff;
  cursor: pointer;
}

::v-deep .el-message-box.no-close-btn .el-message-box__headerbtn {
  display: none;
}
</style>
