<template>
  <div style="padding-top: 20px">
    <p class="title">访问统计</p>
    <div class="statistics">
      <div>
        <span>访问人数</span>
        <span>{{ stasticsData.personTimes || 0 }}</span>
      </div>
      <div>
        <span>访问次数</span>
        <span>{{ stasticsData.viewTimes || 0 }}</span>
      </div>
    </div>
    <p class="title">访问记录</p>
    <div class="right-heade">
      <el-input v-model="formData.operInfo" placeholder="访问者" suffix-icon="el-icon-search" clearable />
      <el-cascader
        v-model="formData.logPersonDeptId"
        placeholder="所属部门"
        :options="deptList"
        :props="{
          value: 'id',
          label: 'deptName',
          checkStrictly: true,
          emitPath: false
        }"
        clearable
        filterable
        size="small"
      >
      </el-cascader>
      <el-date-picker
        v-model="formData.datetimerange"
        value-format="timestamp"
        type="datetimerange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="handleDateChange"
      >
      </el-date-picker>
      <div>
        <el-button type="primary" @click="search">查询</el-button>
        <el-button type="primary" plain @click="reset">重置</el-button>
      </div>
    </div>
    <div>
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="logPersonName" label="访问者"> </el-table-column>
        <el-table-column prop="logPersonDeptName" label="所属部门"> </el-table-column>
        <el-table-column prop="logDate" label="操作时间">
          <template slot-scope="scope">
            {{ moment(scope.row.logDate).format('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        class="pagination"
        :current-page="pageData.current"
        :page-sizes="[15, 30, 45, 60]"
        :page-size="pageData.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
      </el-pagination>
    </div>
  </div>
</template>
<script>
import moment from 'moment'
import { transData } from '@/util'
export default {
  data() {
    return {
      moment,
      formData: {
        operInfo: '',
        logPersonDeptId: '',
        logDateStart: '',
        logDateEnd: '',
        datetimerange: []
      },
      optionList: [
        {
          label: '创建',
          value: '0'
        },
        {
          label: '删除',
          value: '1'
        },
        {
          label: '编辑',
          value: '2'
        },
        {
          label: '共享',
          value: '3'
        },
        {
          label: '恢复',
          value: '4'
        },
        {
          label: '取消共享',
          value: '5'
        }
      ],
      tableLoading: false,
      tableData: [],
      pageData: {
        current: 1,
        size: 15,
        total: 0
      },
      deptList: [],
      stasticsData: {}
    }
  },
  created() {
    this.handleQueryTablelist()
    this.getDeptList()
    this.handleGetStastics()
  },
  methods: {
    handleGetStastics() {
      const { id } = this.$route.query
      this.$api.fileManagement.queryStastics({ archiveId: id }).then((res) => {
        this.stasticsData = res.data
      })
    },
    handleQueryTablelist() {
      const { id: archiveId } = this.$route.query
      const { logPersonDeptId } = this.formData
      const params = {
        archiveId,
        ...this.formData,
        ...this.pageData,
        logType: '1' // 0：操作记录，1：浏览记录
      }
      delete params.datetimerange
      this.$api.fileManagement.recordQueryByPage(params).then((res) => {
        this.tableData = res.data.records
        this.pageData.total = res.data.total
      })
    },
    handleDateChange(e) {
      this.formData.logDateStart = e[0]
      this.formData.logDateEnd = e[1]
    },
    // 获取部门列表
    getDeptList() {
      this.$api.getSelectedDept().then((res) => {
        if (res.code == '200') {
          this.deptList = transData(res.data, 'id', 'pid', 'children')
        }
      })
    },
    search() {
      this.pageData.current = 1
      this.handleQueryTablelist()
    },
    reset() {
      Object.keys(this.formData).forEach((key) => {
        if (key === 'datetimerange') {
          this.formData[key] = []
        } else {
          this.formData[key] = ''
        }
      })
      this.search()
    },
    handleSizeChange(size) {
      this.pageData.size = size
      this.handleQueryTablelist()
    },
    handleCurrentChange(current) {
      this.pageData.current = current
      this.handleQueryTablelist()
    }
  }
}
</script>
<style lang="scss" scoped>
.right-heade {
  display: flex;
  margin: 16px 0;
  > div {
    flex: 1;
    margin-right: 20px;
  }
}
.title {
  font-weight: bold;
  font-size: 16px;
}
.statistics {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  > div {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 49.5%;
    height: 60px;
    line-height: 50px;
    background: #f8f9fa;
    span {
      font-size: 15px;
      font-weight: 500;
    }
    span:last-child {
      margin-left: 10px;
      font-size: 24px;
      font-weight: bold;
    }
  }
}
.pagination {
  margin-top: 16px;
}
</style>
