<template>
  <PageContainer :footer="true">
    <div slot="content" v-loading="formLoading" class="form-content">
      <div class="form-title">监测实体配置</div>
      <el-form ref="formInline" :model="formInline" :rules="rules">
        <ContentCard title="基本信息">
          <div slot="content" class="footer-role">
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="7">
                <el-form-item label="监测实体名称" prop="sensorName" label-width="110px">
                  <el-input v-model="formInline.sensorName" maxlength="25" placeholder="请输入监测实体名称"></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="7">
                <el-form-item label="监测实体编号" prop="sensorNo" label-width="110px">
                  <el-input v-model="formInline.sensorNo" show-word-limit maxlength="20"></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="7">
                <el-form-item label="序号" prop="sort" label-width="110px">
                  <el-input v-model="formInline.sort" type="number" placeholder="请输入序号"> </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="7">
                <el-form-item label="实体类型" prop="entityTypeId" label-width="110px">
                  <el-select v-model="formInline.entityTypeId" filterable clearable @change="entityChang">
                    <el-option v-for="item in entityTypeList" :key="item.id" :label="item.name" :value="item.id + ''"> </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </ContentCard>
        <ContentCard title="关联信息">
          <div slot="content">
            <el-row :gutter="24" style="margin: 0 0 20px">
              <el-col :md="24">
                <div>
                  <span class="form-btn-title">关联监控画面</span><el-button class="form-btn-btn" type="primary" @click="associatedCamera()">选择</el-button
                  ><el-tag v-for="tag in imsVidiconList" :key="tag.imsVidiconId" class="camera-tag" closable @close="tagHandleClose(tag.imsVidiconId)">
                    {{ tag.imsVidiconName }}
                  </el-tag>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="24" style="margin: 0 0 20px">
              <el-col :md="24">
                <div>
                  <span class="form-btn-title">关联设备资产</span><el-button class="form-btn-btn" type="primary" @click="chooseAssets('relation')">选择</el-button>
                  <el-tag v-if="assetsData.assetsId" closable class="camera-tag" @close="delAssetsData()">
                    <span class="assets-info"
                      >资产名称：<span>{{ assetsData.assetName }}</span></span
                    >
                    <span class="assets-info"
                      >专业类别：<span>{{ assetsData.assetCategoryName }}</span></span
                    >
                    <span class="assets-info"
                      >系统类别：<span>{{ assetsData.assetTypeName }}</span></span
                    >
                    <span class="assets-info"
                      >资产编码：<span>{{ assetsData.assetsNumber }}</span></span
                    >
                    <span class="assets-info">安装位置：<span v-html="assetsData.regionName"></span></span>
                    <span class="assets-info"
                      >模型ID：<span>{{ assetsData.modelCode }}</span></span
                    >
                  </el-tag>
                </div>
              </el-col>
            </el-row>
            <el-row v-if="$route.meta.activeMenu === '/monitoringConfig/upsConfiguration'" :gutter="24" style="margin: 0 0 20px">
              <el-col :md="24">
                <el-col :md="7">
                  <el-form-item label="上游监测实体">
                    <el-select v-model="formInline.topTntity" filterable clearable placeholder="请选择或输入关键字" @change="changeEntity">
                      <el-option v-for="(item, index) in topTntityList" :key="index" :label="item.imsName" :value="item.imsCode"> </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-col>
            </el-row>
          </div>
        </ContentCard>
        <ContentCard title="监测参数配置">
          <el-button slot="title-right" class="form-btn-btn" type="primary" @click="addrow"><i class="el-icon-plus"></i> 添加</el-button>
          <div slot="content">
            <el-table v-loading="tableLoading" :data="tableData" style="width: 100%" border>
              <el-table-column prop="dataServerName" show-overflow-tooltip label="数据主机"></el-table-column>
              <el-table-column prop="harvesterType" show-overflow-tooltip label="传感器类型"></el-table-column>
              <el-table-column prop="harvesterName" show-overflow-tooltip label="传感器名称" width="150"></el-table-column>
              <el-table-column prop="harvesterId" show-overflow-tooltip label="传感器编码" width="150"></el-table-column>
              <!-- <el-table-column prop="analysis" show-overflow-tooltip label="数据解析表格" width="200">
                <template slot-scope="scope">
                  <el-select v-model="scope.row.analysis" clearable filterable placeholder="请选择数据解析表格">
                    <el-option v-for="item in dataParserList" :key="item.value" :label="item.name"
                      :value="item.value"></el-option>
                  </el-select>
                </template>
              </el-table-column> -->
              <el-table-column prop="parameterName" show-overflow-tooltip label="监测参数"></el-table-column>
              <el-table-column prop="unitName" show-overflow-tooltip label="单位"> </el-table-column>
              <el-table-column prop="istId" show-overflow-tooltip label="图纸参数" width="200">
                <template slot-scope="scope">
                  <el-select v-model="scope.row.istId" filterable clearable placeholder="请选择图纸参数">
                    <el-option v-for="item in scadaParameterList" :key="item.istId" :label="item.dataTag" :value="item.istId + ''"> </el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column prop="Visible" show-overflow-tooltip label="是否展示" width="150" align="center">
                <template slot-scope="scope">
                  <el-checkbox v-model="scope.row.Visible" style="width: 50px" @change="VisibleChange(scope.row.Visible, scope.$index)"></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column prop="dictAliasId" show-overflow-tooltip label="监测项目别名" width="200">
                <template slot-scope="scope">
                  <el-select v-model="scope.row.dictAliasId" filterable clearable placeholder="请选择监测项目别名">
                    <el-option v-for="item in aliasDictList" :key="item.id" :label="item.paramAlias + '(' + item.paramName + ')'" :value="item.id"> </el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template slot-scope="scope">
                  <el-button type="text" @click="handleParameterEvent('edit', scope.row)">修改</el-button>
                  <el-button type="text" @click="handleParameterEvent('del', scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </ContentCard>
      </el-form>
      <!-- 弹窗事件 -->
      <template>
        <monitoringParamsGather
          :cameraDialogShow="cameraDialogShow"
          :cameraDialogData="cameraDialogData"
          :equipmentAssetsDialogShow="equipmentAssetsDialogShow"
          :assetsClickType="assetsClickType"
          @submitDialog="submitDialog"
        />
      </template>
      <!-- 选择传感器弹窗 -->
      <template v-if="harvesterDialogShow">
        <harvesterDialog
          :dialogShow.sync="harvesterDialogShow"
          :dialogData="selectHarvesterDialogData"
          harvesterCheckType="radio"
          @submitDialog="submitHarvesterDialog"
          @closeDialog="closeHarvesterDialog"
        />
      </template>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">关闭</el-button>
      <el-button v-if="$route.query?.type != 'detail'" type="primary" @click="submitForm()">保存</el-button>
    </div>
  </PageContainer>
</template>
<script lang="jsx">
import monitoringParamsGather from '../components/monitoringParamsGather'
import harvesterDialog from '../components/harvesterDialog'
import { monitorTypeList } from '@/util/dict.js'
export default {
  name: 'medicalGasMonitorForm',
  components: {
    monitoringParamsGather,
    harvesterDialog
  },
  async beforeRouteLeave(to, from, next) {
    if (!this.routerNameList.includes(to.name)) {
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    const routerNameList = ['medicalGasConfiguration', 'upsConfiguration', 'sewageConfiguration']
    return {
      routerNameList: Object.freeze(routerNameList),
      cameraDialogShow: false,
      cameraDialogData: '',
      imsVidiconList: [],
      requestHttp: __PATH.VUE_IEMC_API,
      // 设备资产
      equipmentAssetsDialogShow: false,
      equipmentAssetsDialogData: {},
      assetsClickType: '',
      assetsData: {
        assetsId: '', // 资产ID
        assetName: '', // 资产名称
        regionName: '', // 区域名称
        regionCode: '', // 区域编码
        assetsNumber: '', // 资产编码
        assetTypeName: '', // 系统类别
        assetTypeId: '', // 系统类别ID
        assetCategoryName: '', // 专业类别
        assetCategoryCode: '', // 专业类别ID
        modelCode: '' // 模型ID
      },
      formInline: {
        topTntity: '', // 上游实体
        sensorName: '', // 监测项实体名称
        sort: null, // 序号
        sensorNo: '', // 监测项编号
        length: '', // 监测项长度
        width: '', // 监测项宽度
        height: '', // 监测项高度
        entityTypeId: '' // 实体类型ID
      },
      tableLoading: false,
      tableData: [],
      scadaParameterList: [],
      aliasDictList: [],
      entityTypeName: '',
      upstreamEntityName: '', // 上游实体名称
      formLoading: false,
      disabled: false,
      rules: {
        sensorName: [
          {
            required: true,
            message: '请输入监测实体名称',
            trigger: 'change'
          }
          // { max: 16, message: '长度不大于16位', trigger: 'change' }
        ],
        sort: {
          required: true,
          message: '请输入序号',
          trigger: 'change'
        },
        sensorNo: [
          {
            required: true,
            message: '请输入监测项编号',
            trigger: 'change'
          },
          { min: 6, message: '长度不低于6位', trigger: 'blur' }
        ],
        entityTypeId: {
          required: true,
          message: '请选择实体类型',
          trigger: 'change'
        }
      },
      policeMaxNumberList: [
        { id: 5, name: '5' },
        { id: 10, name: '10' },
        { id: 15, name: '15' },
        { id: 20, name: '20' }
      ],
      leixingList: [],
      jibielist: [], // 报警级别list
      entityTypeList: [], // 实体类型list
      dataParserList: [], // 数据解析表格列表
      dataServer: [], // 数据主机list
      AllPoliceOffList: [{ policeOffList: [] }],
      AllOllectorList: [{ ollectorList: [] }], // 传感器名称list
      ollectorListLoading: [false], // 传感器名称的loading
      parameterId: '',
      parameterList: [],
      setType: '',
      isUps: false, // 是否是ups配置
      currentRouterName: '',
      topTntityList: [],
      harvesterDialogShow: false,
      selectHarvesterDialogData: {}
    }
  },
  activated() {
    this.initEvent()
  },
  mounted() {
    this.currentRouterName = this.$route.name
    if (!this.$store.state.keepAlive.list.some((e) => this.routerNameList.includes(e))) {
      this.initEvent()
    }
  },
  methods: {
    // 设备资产
    chooseAssets(type) {
      // 关联
      this.assetsClickType = type
      if (type === 'relation') {
        this.equipmentAssetsDialogData = JSON.parse(JSON.stringify(this.assetsData))
      } else if (type === 'service') {
        // 服务
        this.equipmentAssetsDialogData = JSON.parse(JSON.stringify(this.assetsServiceData))
      }
      this.equipmentAssetsDialogShow = true
    },
    // 设备资产tag事件
    delAssetsData() {
      Object.assign(this.assetsData, {
        assetsId: '', // 资产ID
        assetName: '', // 资产名称
        regionName: '', // 区域名称
        regionCode: '', // 区域编码
        assetsNumber: '', // 资产编码
        assetTypeName: '', // 系统类别
        assetTypeId: '', // 系统类别ID
        assetCategoryName: '', // 专业类别
        assetCategoryCode: '', // 专业类别ID
        modelCode: '' // 模型ID
      })
    },
    submitDialog(data) {
      this.equipmentAssetsDialogData = {}
      Object.assign(this, data)
    },
    // 摄像机tag事件
    tagHandleClose(id) {
      // 根据id 删除imsVidiconList中的对应项
      const index = this.imsVidiconList.findIndex((e) => e.imsVidiconId === id)
      this.imsVidiconList.splice(index, 1)
    },
    // 摄像机
    associatedCamera() {
      this.cameraDialogData = Array.from(this.imsVidiconList, ({ imsVidiconId }) => imsVidiconId).toString()
      this.cameraDialogShow = true
    },
    // 接口数据初始化
    initEvent() {
      // 重置form表单
      const data = this.$options.data()
      delete data.rules
      this.$route.query.isiId ? this.getImageSelectById(this.$route.query.isiId, 'scadaParameterList') : ''
      this.$nextTick(() => {
        Object.assign(this.$data, data)
        this.$refs.formInline.resetFields()
        this.setType = this.$route.query.type
        this.getDictionaryList()
        this.isUps = monitorTypeList.find((item) => item.projectCode == this.$route.query.projectCode).projectName === 'UPS监测'
      })
      if (this.$route.meta.activeMenu === '/monitoringConfig/upsConfiguration') {
        this.getTopTntityList()
      }
    },
    // 获取上游监测实体
    getTopTntityList() {
      let data = {
        isUpdate: 0,
        projectCode: this.$route.query.projectCode
      }
      this.$api.getupStreamList(data).then((res) => {
        if (res.code == 200) {
          this.topTntityList = res.data
        }
      })
    },
    // 上游监测实体改变
    changeEntity() {
      // 根据code取出name
      this.upstreamEntityName = this.topTntityList.find((item) => item.imsCode == this.formInline.topTntity)?.imsName ?? ''
    },
    getDictionaryList() {
      // 数据主机
      this.dataServer = JSON.parse(sessionStorage.getItem('dataServer'))
      // 报警级别
      this.$api
        .getDictionaryList({
          dictType: 0
        })
        .then((res) => {
          this.jibielist = res.data
        })
      // 传感器参数
      this.$api
        .getDictionaryList({
          dictType: 1,
          projectCode: this.$route.query.projectCode
        })
        .then((res) => {
          this.parameterList = res.data
          this.setType != 'add' ? this.getSurveyByOne() : ''
        })
      // 报警类型
      this.$api
        .getDictionaryList({
          dictType: 5
        })
        .then((res) => {
          this.leixingList = res.data
        })
      // 数据解析表格列表
      this.$api
        .getDictionaryList({
          dictType: 9
        })
        .then((res) => {
          this.dataParserList = res.data
        })
      // 获取监测项目别名列表
      this.$api.getAliasDictList().then((res) => {
        this.aliasDictList = res.data
      })
      // 获取实体类型数据
      this.$api
        .getDictionaryList({
          dictType: 12
        })
        .then((res) => {
          // 获取实体类型数据
          if (res.code == 200) {
            this.entityTypeList = res.data
          }
        })
    },
    // 实体类型改变
    entityChang() {
      this.entityTypeName = this.entityTypeList.find((item) => item.id == this.formInline.entityTypeId)?.name ?? ''
    },
    // 获取监测项详情
    getSurveyByOne() {
      this.formLoading = true
      const userInfo = this.$store.state.user.userInfo.user
      this.$api
        .getSurveyParameterOne(
          {
            surveyCode: this.$route.query.sensorCode,
            userId: userInfo.staffId,
            userName: userInfo.staffName
          },
          {},
          this.requestHttp
        )
        .then((res) => {
          const promises = []
          res.data.parameterList.forEach((item, i) => {
            item.Visible = item.policeShow != '1'
            // item.analysis = item.analysis ? item.analysis.toString() : item.analysis
          })
          this.tableData = res.data.parameterList
          this.formInline.sensorName = res.data.sensorName + (this.setType == 'copy' ? '副本' : '')
          this.formInline.sensorNo = res.data.sensorNo
          this.formInline.sort = res.data.sort ? Number(res.data.sort) : null
          this.formInline.entityTypeId = res.data.entityTypeId
          this.entityTypeName = res.data.entityTypeName
          this.formInline.length = res.data.length
          this.formInline.width = res.data.width
          this.formInline.height = res.data.height
          this.formInline.topTntity = res.data.upstreamEntityId
          this.upstreamEntityName = res.data.upstreamEntityName
          this.assetsData.assetsId = res.data.assetId
          this.assetsData.assetName = res.data.assetName
          this.assetsData.regionName = res.data.regionName
          this.assetsData.regionCode = res.data.regionCode
          this.assetsData.assetsNumber = res.data.assetsNumber
          this.assetsData.assetTypeName = res.data.assetTypeName
          this.assetsData.assetTypeId = res.data.assetTypeId
          this.assetsData.assetCategoryName = res.data.assetCategoryName
          this.assetsData.assetCategoryCode = res.data.assetCategoryCode
          this.assetsData.modelCode = res.data.modelCode
          const imsVidiconId = res.data.imsVidiconId ? res.data.imsVidiconId.split(',') : []
          const imsVidiconName = res.data.imsVidiconName ? res.data.imsVidiconName.split(',') : []
          this.imsVidiconList =
            imsVidiconId?.map((item, index) => {
              return {
                imsVidiconId: item,
                imsVidiconName: imsVidiconName[index]
              }
            }) ?? []
          this.formLoading = false
        })
    },
    submitForm() {
      this.$refs.formInline.validate((valid) => {
        if (valid) {
          if (this.tableData.length == 0) {
            this.$message.error('请添加监测参数配置')
            return
          }
          this.formLoading = true
          const userInfo = this.$store.state.user.userInfo.user
          let reqInsertSurvey = {
            userId: userInfo.staffId,
            userName: userInfo.staffName,
            entityMenuCode: this.$route.query.entityMenuCode,
            projectCode: this.$route.query.projectCode, // 项目编号
            sensorName: this.formInline.sensorName, // 监测实体名称
            sensorNo: this.formInline.sensorNo, // 监测项编号
            length: this.formInline.length, // 监测实体长
            width: this.formInline.width, // 监测实体宽
            height: this.formInline.height, // 监测实体高
            sort: this.formInline.sort, // 序号
            entityTypeName: this.entityTypeName, // 实体类型名称
            entityTypeId: this.formInline.entityTypeId, // 实体类型ID
            upstreamEntityId: this.formInline.topTntity, // 上游实体ID
            upstreamEntityName: this.upstreamEntityName, // 上游实体名称
            imsVidiconId: Array.from(this.imsVidiconList, (e) => e.imsVidiconId).toString(), // 关联摄像头
            imsVidiconName: Array.from(this.imsVidiconList, (e) => e.imsVidiconName).toString(), // 关联摄像头
            ...this.assetsData, // 关联资产设备
            assetId: this.assetsData.assetsId, // 资产设备ID
            parameterList: this.tableData.map((e) => {
              return {
                harvesterName: e.harvesterName,
                harvesterId: e.harvesterId,
                harvesterRealId: e.harvesterRealId,
                dataServerId: e.dataServerId,
                dataServerName: e.dataServerName,
                harvesterTypeId: e.harvesterTypeId,
                harvesterType: e.harvesterType,
                parameterId: e.parameterId,
                parameterName: e.parameterName,
                dictAliasId: e.dictAliasId,
                unitName: e.unitName,
                unitId: e.unitId,
                // analysis:e.analysis,
                policeShow: e.policeShow,
                istId: e.istId, // SCADA图形参数IDS
                state: e.state,
                ip: e.ip,
                port: e.port,
                paramSource: e.paramSource,
                projectCode: this.$route.query.projectCode // 项目编号
              }
            })
          }
          if (this.setType == 'add' || this.setType == 'copy') {
            // 新增或复制
            this.$api.insertSurveyAndParameter(reqInsertSurvey, { 'operation-type': 1 }, this.requestHttp).then((res) => {
              this.formLoading = false
              if (res.code == '200') {
                this.$message.success(res.message)
                this.$refs.formInline.resetFields()
                this.$router.go(-1)
              } else {
                this.$message.error(res.message)
              }
            })
          } else {
            reqInsertSurvey.sensorCode = this.$route.query.sensorCode
            this.$api
              .updateSurveyAndParameter(
                reqInsertSurvey,
                { 'operation-type': 2, 'operation-id': reqInsertSurvey.sensorCode, 'operation-name': reqInsertSurvey.sensorName },
                this.requestHttp
              )
              .then((res) => {
                this.formLoading = false
                if (res.code == 200) {
                  this.$message.success(res.message)
                  this.$refs.formInline.resetFields()
                  this.$router.go(-1)
                } else {
                  this.$message.error(res.message)
                }
              })
          }
        }
      })
    },
    // 添加参数
    addrow() {
      this.selectHarvesterDialogData = { eventType: 'add' }
      this.harvesterDialogShow = true
    },
    // 监测项事件
    handleParameterEvent(type, row) {
      if (type === 'del') {
        // const index = this.tableData.findIndex((e) => ((e.id === row.id) && (e.parameterId === row.parameterId)))
        // this.tableData.splice(index, 1)
        const filterData = this.tableData.filter((e) => !(e.harvesterRealId === row.harvesterRealId && e.parameterId === row.parameterId))
        this.tableData = filterData
      } else {
        // radio 选中的参数id
        const parameterIdsList = Array.from(
          this.tableData.filter((e) => e.harvesterRealId === row.harvesterRealId),
          (e) => e.parameterId
        )
        this.selectHarvesterDialogData = Object.assign(row, { eventType: 'edit', parameterIdsList })
        this.harvesterDialogShow = true
      }
    },
    VisibleChange(val, i) {
      this.$forceUpdate()
      if (val) {
        this.tableData[i].policeShow = 0 // 展示
      } else {
        this.tableData[i].policeShow = 1 // 不展示
      }
    },
    submitHarvesterDialog(data) {
      // 将监测参数抽出赋值在传感器数据上
      let newSplitData = []
      // tableData中与data中相同的传感器id数据过滤掉
      let newSetTableData = this.tableData
      data.forEach((e) => {
        newSetTableData = newSetTableData.filter((item) => item.harvesterRealId !== e.harvesterRealId && this.selectHarvesterDialogData.harvesterRealId !== item.harvesterRealId)
        e.parameterList.forEach((item) => {
          newSplitData.push({
            ...e,
            ...item,
            parameterList: null,
            istId: '',
            policeShow: '',
            dictAliasId: ''
          })
        })
      })
      // 将新的数据与旧的数据合并
      let concatData = [...newSplitData, ...newSetTableData]
      // 将合并后的数据赋值给tableData
      // this.tableData = concatData
      this.tableData = concatData
      this.tableData.forEach((el) => {
        el.VisibleTrendChart = true
        el.Visible = true
      })
      this.harvesterDialogShow = false
    },
    closeHarvesterDialog() {
      this.harvesterDialogShow = false
    },
    getImageSelectById(id, params) {
      this.$api.getImageSelectById({ id: id }, {}, this.requestHttp).then((res) => {
        if (res.code == 200) {
          this[params] = res.data
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.footer-role ::v-deep .el-input__inner {
  padding-right: 50px;
}
.form-content {
  height: calc(100% - 0px);
  overflow-y: auto;
  .form-title {
    height: 40px;
    line-height: 40px;
    background: #fff;
    border-bottom: 1px solid #dcdfe6;
    font-size: 15px;
    font-family: 'PingFang SC-Medium', 'PingFang SC';
    font-weight: 500;
    color: #121f3e;
    padding-left: 15px;
  }
  .box-card {
    padding-left: 3%;
    margin-bottom: 3px;
  }
  .form-btn-title {
    font-size: 14px;
    font-family: 'PingFang SC-Regular', 'PingFang SC';
    font-weight: 400;
    color: #121f3e;
    margin-right: 8px;
  }
  .form-btn-btn {
    padding: 4px 10px;
    margin: 0 8px;
  }
  .assets-info {
    font-size: 14px;
    font-family: 'PingFang SC-Regular', 'PingFang SC';
    font-weight: 400;
    color: #121f3e;
    margin-right: 15px;
    > span {
      color: #3562db;
    }
  }
  .assets-info-close {
    cursor: pointer;
    &:hover {
      color: #3562db;
      font-weight: 600;
    }
  }
  .parameter-box {
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    margin-bottom: 20px;
    .parameter-title {
      font-size: 14px;
      font-family: 'PingFang SC-Regular', 'PingFang SC';
      font-weight: 400;
      color: #121f3e;
      background: #faf9fc;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 48px;
      line-height: 48px;
      padding: 0 10px;
      & > span {
        &:first-child {
          font-size: 16px;
        }
      }
    }
    .unit-style {
      font-size: 14px;
      font-family: 'PingFang SC-Regular', 'PingFang SC';
      font-weight: 400;
      color: #121f3e;
      height: 40px;
      line-height: 40px;
    }
  }
  ::v-deep .el-select,
  .el-input {
    width: fit-content;
  }
  .camera-tag {
    background: #f6f5fa;
    border-radius: 4px;
    font-size: 14px;
    font-family: 'PingFang SC-Regular', 'PingFang SC';
    font-weight: 400;
    color: #121f3e;
    border: none;
    margin-right: 8px;
    ::v-deep .el-tag__close {
      color: #121f3e;
      &:hover {
        color: #fff;
        background-color: #3562db;
      }
    }
  }
}
</style>
