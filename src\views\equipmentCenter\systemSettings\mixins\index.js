export default {
  computed: {
    selectData() {
      if (this.typeList.length) {
        const obj = { id: '-1', dictName: '全部', disabled: this.tableData.length !== 1 }
        this.typeList.splice(0, 1, obj)
      }
      return this.typeList
    }
  },
  data() {
    return {
      typeList: []
    }
  },
  methods: {
    handleGetSelectData() {
      const isEquipment = this.$route.path.includes('systemSettings')
      const params = isEquipment ? '1' : '2'
      this.handleGetDictList(params)
        .then((res) => {
          const list = res.flat().filter((e) => ['patrol_template_classification_type', 'maintain_template_type', 'lnspection_type'].includes(e.dictType))
          const promiseData = list.map((item) => this.handleGetDataList(item.dictType))
          Promise.all(promiseData)
            .then((res) => {
              this.typeList = [{ id: '-1', dictName: '全部' }, ...res.flat()]
            })
            .catch((err) => {
              new Error(err)
            })
        })
        .catch((err) => {
          this.$message.error(err)
        })
    },
    handleGetDictList(val) {
      return new Promise((resolve, reject) => {
        let data = {
          systemIdentificationClassification: val, // 1（设施设备巡检）2（综合巡检系统）
          pageNo: 1,
          pageSize: 99999
        }
        this.$api.sysDictType(data).then((res) => {
          if (res.code == 200) {
            resolve(res.data.list)
          } else {
            reject(res.message)
          }
        })
      })
    },
    handleGetDataList(e) {
      return new Promise((resolve, reject) => {
        const data = {
          pageSize: 99999,
          pageNo: 1,
          dictType: e
        }
        this.$api.sysDictData(data).then((res) => {
          if (res.code == 200) {
            resolve(res.data.list)
          } else {
            reject(res.message)
          }
        })
      })
    }
  }
}
