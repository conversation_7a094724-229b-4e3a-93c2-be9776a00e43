<template>
  <PageContainer>
    <div slot="content" class="role-content" style="height: 100%;">
      <div class="role-content-left">
        <div class="toptip">
          <span class="green_line"></span>
          数据字典
        </div>
        <div class="left_content">
          <el-input v-model.trim="filterText" clearable placeholder="输入关键字进行过滤"></el-input>
          <div class="tree">
            <el-tree
              ref="tree"
              style="margin-top: 10px;"
              :check-strictly="true"
              :data="treeData"
              :props="defaultProps"
              :highlight-current="true"
              :filter-node-method="$tools.filterNode"
              :default-expanded-keys="expanded"
              node-key="dictValue"
              @node-click="handleNodeClick"
            ></el-tree>
          </div>
        </div>
      </div>
      <div class="role-content-right">
        <div v-if="dictionaryType != '1' && dictionaryType != 'manage_type' && dictionaryType != 'hidden_trouble_type'" style="height: 100%;">
          <div class="search-from">
            <el-input v-model.trim="filters.dictName" placeholder="字典名称" style="width: 200px;" clearable></el-input>
            <el-input v-model.trim="filters.dictCode" placeholder="字典编码" style="width: 200px;" clearable></el-input>
            <el-button type="primary" plain @click="resetData">重置</el-button>
            <el-button type="primary" @click="searchClick">查询</el-button>
          </div>
          <div class="middle_tools">
            <el-button type="primary" icon="el-icon-plus" @click="openDialog('add')">新增</el-button>
            <el-button type="primary" icon="el-icon-edit" :disabled="multipleSelection.length != 1" @click="openDialog('edit')">编辑</el-button>
          </div>
          <div class="contentTable">
            <div class="contentTable-main table-content">
              <el-table
                v-loading="tableLoading"
                :data="tableData"
                :height="tableHeight"
                title="双击查看详情"
                border
                stripe
                @selection-change="handleSelectionChange"
                @row-dblclick="dblclick"
              >
                <el-table-column type="selection" width="55" align="center" fixed="left"></el-table-column>
                <el-table-column type="index" label="序号" width="55">
                  <template slot-scope="scope">
                    <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="dictName" label="字典名称" width="180" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="dictCode" label="字典编码" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="sort" label="排序号" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="updateDate" label="更新时间" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="description" label="备注信息" show-overflow-tooltip> </el-table-column>
                <el-table-column label="操作" width="100">
                  <template slot-scope="scope">
                    <el-button type="danger" size="mini" @click="remove(scope.row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="contentTable-footer">
              <el-pagination
                class="pagination"
                :current-page="paginationData.currentPage"
                :page-sizes="[15, 30, 50, 100]"
                :page-size="paginationData.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="paginationData.total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              ></el-pagination>
            </div>
          </div>
          <el-dialog v-dialogDrag custom-class="model-dialog" :title="dialogTitle" :visible.sync="dialogVisible" :before-close="dialogClosed">
            <div style="height: 100%; background-color: #fff; width: 100%; padding: 10px;">
              <el-form ref="formInline" :model="formInline" :rules="rules" :inline="true" class="advanced-search-form" label-position="right" label-width="110px">
                <el-form-item label="字典名称：" prop="dictName">
                  <el-input
                    v-if="!disabled"
                    v-model="formInline.dictName"
                    placeholder="请输入字典名称"
                    autocomplete="off"
                    :maxlength="50"
                    show-word-limit
                    style="width: 260px;"
                  ></el-input>
                  <span v-else style="width: 260px; display: inline-block;">{{ formInline.dictName }}</span>
                </el-form-item>
                <el-form-item label="字典编码：" prop="dictCode">
                  <el-input
                    v-if="!disabled"
                    v-model.number="formInline.dictCode"
                    placeholder="请输入字典编码"
                    autocomplete="off"
                    show-word-limit
                    maxlength="10"
                    style="width: 260px;"
                  ></el-input>
                  <span v-else style="width: 260px; display: inline-block;">{{ formInline.dictCode }}</span>
                </el-form-item>
                <br />
                <el-form-item label="排序号：" prop="sort">
                  <el-input v-if="!disabled" v-model.number="formInline.sort" placeholder="请输入排序号" maxlength="6" autocomplete="off" style="width: 260px;"></el-input>
                  <span v-else style="width: 260px; display: inline-block;">{{ formInline.sort }}</span>
                </el-form-item>
                <br />
                <el-form-item label="备注信息：" prop="description" class="sino-form-textarea">
                  <el-input
                    v-if="!disabled"
                    v-model.trim="formInline.description"
                    show-word-limit
                    placeholder="请输入备注信息"
                    maxlength="200"
                    type="textarea"
                    :autosize="{ minRows: 4, maxRows: 6 }"
                  ></el-input>
                  <span v-else style="width: 500px; display: inline-block;">{{ formInline.description }}</span>
                </el-form-item>
              </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
              <span>
                <el-button type="primary" plain @click="dialogClosed">取 消</el-button>
                <el-button v-if="!disabled" type="primary" class="sino-button-sure" @click="submit">确定</el-button>
              </span>
            </span>
          </el-dialog>
        </div>
        <!-- 事故类型 -->
        <accidentType v-if="dictionaryType == '1'"></accidentType>
        <!-- 设备字典 -->
        <Equipment-dict v-if="dictionaryType == 'manage_type'"></Equipment-dict>
        <!-- 隐患分类字典 -->
        <Classify-dict v-if="dictionaryType == 'hidden_trouble_type'"></Classify-dict>
      </div>
    </div>
  </PageContainer>
</template>

<script>
import tableListMixin from '@/mixins/tableListMixin.js'
import accidentType from './components/accidentType.vue'
import EquipmentDict from './components/EquipmentDict.vue'
import ClassifyDict from './components/ClassifyDict.vue'
export default {
  name: 'DataDictionary',
  components: {
    accidentType,
    // eslint-disable-next-line vue/no-unused-components
    EquipmentDict,
    // eslint-disable-next-line vue/no-unused-components
    ClassifyDict
  },
  mixins: [tableListMixin],
  data() {
    return {
      disabled: false,
      setType: '',
      dialogVisible: false,
      treeData: [],
      defaultProps: {
        label: 'dictLabel',
        value: 'dictValue'
      },
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      tableData: [],
      treeLoading: false,
      tableLoading: false,
      filters: {
        dictName: '',
        dictCode: ''
      },
      multipleSelection: [],
      checkedData: {},
      dictionaryType: '',
      filterText: '',
      expanded: [],
      rules: {
        dictName: [{ required: true, message: '请输入字典名称', trigger: 'blur' }],
        dictCode: [{ required: true, message: '请输入字典编码', trigger: 'blur' }]
      },
      formInline: {
        dictName: '',
        dictCode: '',
        sort: '',
        description: ''
      },
      dialogTitle: ''
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    this.getTreeData()
  },
  methods: {
    searchClick() {
      this.paginationData.currentPage = 1
      this.getTableData()
    },
    closeDialog() {
      // this.dialogVisible = false;
    },
    // 勾选
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 获取数据字典
    getTreeData() {
      let params = {}
      this.treeLoading = true
      this.$api
        .ipsmGetDictValue({
          dictType: 'data_dict_type',
          isShowParent: 0
        })
        .then((res) => {
          this.treeLoading = this.$store.state.loadingShow
          if (res.code == '200' && res.data.length > 0) {
            this.treeData = res.data
            this.findDictionaryType() // 获取巡检字典类型
            this.expanded = [this.treeData[0].dictValue]
            this.handleNodeClick(this.treeData[0])
            this.$nextTick(() => {
              this.$refs.tree.setCurrentKey(this.treeData[0])
            })
          }
        })
    },
    findDictionaryType() {
      let data = {
        pageNo: 1,
        pageSize: 99999
      }
      this.$api.ipsmFindDictionaryType(data).then((res) => {
        if (res.code == 200) {
          var dictionaryArr = res.data.list
          if (dictionaryArr && dictionaryArr.length) {
            dictionaryArr.forEach((v, i) => {
              let sums = new Object()
              sums.dictLabel = v.dictName
              sums.dictValue = v.dictType
              this.treeData.push(sums)
            })
            // console.log("treeDatas", this.treeData);
          }
        } else {
        }
      })
    },
    // 树状图点击
    handleNodeClick(data) {
      // console.log(data)
      this.paginationData.currentPage = 1
      this.checkedData = data
      this.$nextTick(() => {
        this.dictionaryType = this.checkedData.dictValue
      })
      this.$refs.tree.setCurrentKey(this.checkedData)
      // //如果数据字典类型dictValue为：1（事故类型）时，直接获取接口，否则根据this.dictionaryType的值调取对应的组件
      if (this.checkedData.dictValue !== '1' && this.checkedData.dictValue !== 'manage_type' && this.checkedData.dictValue !== 'hidden_trouble_type') {
        this.getTableData()
      }
    },
    // 获取列表
    getTableData() {
      let data = {
        pageSize: this.paginationData.pageSize,
        pageNo: this.paginationData.currentPage,
        dictType: this.checkedData.dictValue,
        ...this.filters
      }
      this.tableLoading = true
      this.$api.ipsmFindDictionaryTableList(data).then((res) => {
        this.tableLoading = this.$store.state.loadingShow
        if (res.code == 200) {
          this.tableData = res.data.list
          this.paginationData.total = parseInt(res.data.count)
        }
      })
    },
    dblclick(val) {
      console.log(1)
      this.openDialog('view', val.id)
    },
    openDialog(type, id) {
      this.dialogTitle = type == 'add' ? '新增字典' : type == 'edit' ? '编辑字典' : '查看详情'
      this.disabled = type == 'view'
      if (type != 'add') {
        let data = {
          id: id || this.multipleSelection[0].id,
          dictType: this.activeType
        }
        this.$api.ipsmDictionaryDetails(data).then((res) => {
          const { code, data, message } = res
          if (code == 200) {
            this.formInline = data
          }
        })
      }
      this.dialogVisible = true
    },
    dialogClosed() {
      this.dialogVisible = false
      this.$refs.formInline.resetFields()
      this.formInline.dictName = ''
      this.formInline.dictCode = ''
      this.formInline.sort = ''
      this.formInline.description = ''
    },
    submit() {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          if (this.dialogTitle == '新增字典') {
            let { dictName, dictCode, sort, description } = this.formInline
            let params = {
              dictName,
              dictCode,
              sort,
              description,
              dictType: this.checkedData.dictValue
            }
            this.$api.ipsmSaveDictionary(params).then((res) => {
              if (res.code == 200) {
                this.$message.success('新增成功!')
                this.getTableData()
              } else {
                this.$message.error(res.message || '新增失败!')
              }
              this.dialogClosed()
            })
          } else {
            let { dictName, dictCode, sort, description } = this.formInline
            let params = {
              dictName,
              dictCode,
              sort,
              description,
              dictType: this.checkedData.dictValue,
              id: this.multipleSelection[0].id
            }
            this.$api.ipsmUpdateDictionary(params).then((res) => {
              if (res.code == 200) {
                this.$message.success('修改成功!')
                this.getTableData()
              } else {
                this.$message.error('修改失败!')
              }
              this.dialogClosed()
            })
          }
        }
      })
    },
    remove(row) {
      let data = {
        id: row.id,
        dictType: this.dictionaryType
      }
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$api.ipsmDeleteDictionary(data).then((res) => {
            if (res.code == 200) {
              this.$message.success('删除成功!')
              this.getTableData()
            } else {
              this.$message.error(res.message)
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    resetData() {
      this.paginationData = {
        currentPage: 1,
        pageSize: 15,
        total: 0
      }
      this.filters.dictCode = ''
      this.filters.dictName = ''
      this.getTableData()
    },
    handleSizeChange(val) {
      this.paginationData.currentPage = 1
      this.paginationData.pageSize = val
      this.getTableData()
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.getTableData()
    }
  }
}
</script>
<style lang="scss" scoped>
// el-tree-node is-expanded is-focusable
::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: linear-gradient(to right, #d9e1f8, #fff);
}

::v-deep .el-tree-node__content {
  height: 32px;
}

.role-content {
  height: 100%;
  display: flex;

  .role-content-left {
    width: 246px;
    height: 100%;
    padding: 10px;
    background: #fff;
    border-radius: 4px;
    margin-right: 12px;

    .left_content {
      height: calc(100% - 60px);
      padding: 10px;
      // overflow: auto;
      .tree {
        height: calc(100% - 40px);
        overflow: auto;
      }
    }
  }

  .role-content-right {
    flex: 1;
    height: 100%;
    min-width: 0;
    padding: 10px;
    background: #fff;
    border-radius: 4px;

    .search-from {
      padding-bottom: 12px;

      & > div {
        margin-right: 10px;
      }

      & > button {
        margin-top: 12px;
      }
    }

    .middle_tools {
      // margin-top: 20px;
      margin-bottom: 10px;
    }

    .contentTable {
      height: calc(100% - 80px);
      display: flex;
      flex-direction: column;

      .contentTable-main {
        height: calc(100% - 40px);
        flex: 1;
        overflow: auto;
      }

      .contentTable-footer {
        padding: 10px 0;
      }
    }
  }
}
</style>
