<template>
  <PageContainer>
    <div slot="header">
      <span>报告类型</span>
      <el-select v-model="reportType" placeholder="请选择">
        <el-option v-for="item in reportTypeList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
      </el-select>
      <span>报告文件</span>
      <div class="file-name">{{ fileName }}</div>
      <el-upload ref="upload" action="" :file-list="fileList" :auto-upload="false" :on-change="fileChanged" :show-file-list="false" :limit="1">
        <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
        <el-button style="margin-left: 10px;" size="small" type="success" @click="uploadFile">上传文件</el-button>
      </el-upload>
    </div>
    <div slot="content" ref="contentRef">
      <el-table :data="tableData" border style="width: 100%;" height="90%">
        <el-table-column label="序号" type="index" width="50" align="center" :resizable="false"></el-table-column>
        <el-table-column prop="reportType" label="报告类型" show-overflow-tooltip :resizable="false" align="center"> </el-table-column>
        <el-table-column prop="name" label="模板名称" show-overflow-tooltip :resizable="false" align="center"> </el-table-column>
        <el-table-column prop="" label="操作人" show-overflow-tooltip :resizable="false" align="center"> </el-table-column>
        <el-table-column prop="createDate" label="更新时间" show-overflow-tooltip :resizable="false" align="center"> </el-table-column>
        <el-table-column label="操作" show-overflow-tooltip :resizable="false" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="downloadFile(scope.row)">下载</el-button>
            <el-button type="text" size="small" @click="openEditDialog(scope.row)">编辑</el-button>
            <el-button class="delbtn" type="text" size="small" @click="delTemplate(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="pageNo"
        :page-sizes="[15, 30, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
      </el-pagination>
      <el-dialog title="修改名称" :visible.sync="dialogVisible" width="30%" :before-close="dialogClosed">
        <div class="dialog-contet">
          <span>文件名称</span>
          <el-input v-model="rename" placeholder="请输入内容"></el-input>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="editTemplate">确 定</el-button>
          <el-button @click="dialogClosed">取 消</el-button>
        </span>
      </el-dialog>
    </div>
  </PageContainer>
</template>
<script>
import axios from 'axios'
import store from '@/store/index'
export default {
  name: 'templateManagement',
  data() {
    return {
      reportTypeList: [
        // {
        //   label: '日报',
        //   value: '0'
        // },
        {
          label: '周报',
          value: '1'
        },
        // {
        //   label: '半月报',
        //   value: '2'
        // },
        {
          label: '月报',
          value: '3'
        },
        {
          label: '季报',
          value: '4'
        },
        {
          label: '年报',
          value: '5'
        }
      ],
      reportType: '',
      fileList: [],
      fileName: '',
      pageNo: 1,
      pageSize: 15,
      total: 0,
      tableData: [],
      editId: '',
      dialogVisible: false,
      rename: ''
    }
  },
  created() {},
  mounted() {
    this.getList()
  },
  methods: {
    getList() {
      let params = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        type: 1
      }
      this.$api.getTemplateList(params).then((res) => {
        this.tableData = res.rows
        this.total = res.total
      })
    },
    uploadFile() {
      if (!this.reportType) {
        this.$message.error('请选择报告类型')
        return
      }
      let formData = new FormData()
      formData.append('file', this.fileList[0].raw)
      formData.append('reportType', this.reportType)
      formData.append('action', 'UploadVMKImagePath')
      axios
        .post(__PATH.VUE_IOMS_API + 'newReportExport/import', formData, {
          headers: { 'Content-Type': 'multipart/form-data', Authorization: 'Bearer ' + this.$store.state.user.token }
        })
        .then((res) => {
          if (res.data.success) {
            this.$message.success('上传成功')
            this.getList()
            this.fileName = ''
            this.fileList = []
          } else {
            this.$message.error(res.data.msg)
          }
        })
    },
    fileChanged(file, fileList) {
      this.fileList = fileList
      this.fileName = file.name
    },
    handleSizeChange(val) {},
    handleCurrentChange(val) {},
    downloadFile(row) {
      // const userInfo = store.state.user.userInfo.user
      // let params = {
      //   id: row.id
      // }
      // this.$api.downLoadTemplate(params).then((res) => {
      // })
      window.location.href = this.$tools.imgUrlTranslation(row.minioUrl)
    },
    editTemplate() {
      if (this.rename === '') {
        this.$message.error('请输入模板名称')
        return
      }
      let params = {
        id: this.editId,
        name: this.rename
      }
      this.$api.renameTemplate(params, { 'operation-type': 2, 'operation-id': params.id, 'operation-name': params.name}).then((res) => {
        if (res.success) {
          this.dialogVisible = false
          this.$message.success('修改成功')
          this.getList()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    openEditDialog(row) {
      this.editId = row.id
      this.rename = row.name
      this.dialogVisible = true
    },
    dialogClosed() {
      this.rename = ''
      this.dialogVisible = false
    },
    async delTemplate(row) {
      const confirmRes = await this.$confirm('此操作将永久删除该模板, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).catch((err) => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
      if (confirmRes === 'confirm') {
        this.$api.delTemplate({ id: row.id }, { 'operation-type': 3, 'operation-id': row.id, 'operation-name': row.name }).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.getList()
          } else {
            this.$message.error(res.msg)
          }
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
@font-face {
  font-family: PingFangSC-Regular;
  src: url(~@/assets/fonts/PingFangRegular.TTF) format("truetype");
}
.container-header > div {
  display: flex;
  height: 80px;
  align-items: center;
}
.container-header > div > span {
  margin: 0 16px;
}
.el-upload {
  display: flex;
}
.file-name {
  width: 200px;
  height: 32px;
  border: 1px solid #ccced3;
  border-radius: 4px;
  margin-right: 16px;
  display: flex;
  align-items: center;
  font-size: 14px;
  padding-left: 8px;
}
.el-select {
  margin-right: 34px;
}
.el-button--success {
  font-size: 14px;
  font-family: PingFangSC-Regular;
}
.container-content > div {
  height: 100%;
  background-color: #fff;
  padding: 16px;
  margin-top: 16px;
}
.el-pagination {
  margin-top: 16px;
}
.dialog-contet {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}
.dialog-contet > span {
  width: 16%;
}
.el-dialog__footer {
  display: flex;
  align-items: center;
}
.el-button--default {
  padding: 8px 22px;
}
.delbtn {
  color: #fa403c !important;
}
</style>
