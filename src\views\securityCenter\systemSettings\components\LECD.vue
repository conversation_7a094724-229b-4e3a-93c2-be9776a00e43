<template>
  <div v-loading="tableLoading">
    <template>
      <div style="margin-bottom: 20px;">
        <div class="toptip">
          <span class="green_line"></span>
          事故可能造成的后果（C）
        </div>
        <div class="inspection-content">
          <el-table :data="tableL" border>
            <el-table-column prop="score" label="分值" show-overflow-tooltip width="120"></el-table-column>
            <el-table-column prop="judgeExplain" label="说明" show-overflow-tooltip></el-table-column>
            <el-table-column prop="judgeExplain" label="操作" show-overflow-tooltip width="80">
              <template slot-scope="scope">
                <span style="color: #5188fc; cursor: pointer;" @click="look(scope.row)">编辑</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </template>
    <template>
      <div style="margin-bottom: 20px;">
        <div class="toptip">
          <span class="green_line"></span>
          暴露于危险环境的频繁程度（E）
        </div>
        <div class="inspection-content">
          <el-table :data="tableE" border>
            <el-table-column prop="score" label="分值" show-overflow-tooltip width="120"></el-table-column>
            <el-table-column prop="judgeExplain" label="说明" show-overflow-tooltip></el-table-column>
            <el-table-column prop="judgeExplain" label="操作" show-overflow-tooltip width="80">
              <template slot-scope="scope">
                <span style="color: #5188fc; cursor: pointer;" @click="look(scope.row)">编辑</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </template>
    <template>
      <div style="margin-bottom: 20px;">
        <div class="toptip">
          <span class="green_line"></span>
          发生故事的可能性（L）
        </div>
        <div class="inspection-content">
          <el-table :data="tableC" border>
            <el-table-column prop="score" label="分值" show-overflow-tooltip width="120"></el-table-column>
            <el-table-column prop="judgeExplain" label="说明" show-overflow-tooltip></el-table-column>
            <el-table-column prop="judgeExplain" label="操作" show-overflow-tooltip width="80">
              <template slot-scope="scope">
                <span style="color: #5188fc; cursor: pointer;" @click="look(scope.row)">编辑</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </template>
    <template>
      <div style="margin-bottom: 60px;">
        <div class="toptip">
          <span class="green_line"></span>
          风险等级配置（说明：英文"()"不包含此数值，"[]"包含此数值。）
        </div>
        <div class="inspection-content" style="width: 500px;">
          <el-table :data="tableD" border>
            <el-table-column prop="score" label="D分值" show-overflow-tooltip width="120"></el-table-column>
            <el-table-column prop="judgeExplain" label="风险等级" show-overflow-tooltip>
              <template slot-scope="scope">
                <span v-if="scope.row.judgeExplain == '重大风险'" style="color: rgb(255 0 0);">重大风险</span>
                <span v-if="scope.row.judgeExplain == '较大风险'" style="color: rgb(255 97 0);">较大风险</span>
                <span v-if="scope.row.judgeExplain == '一般风险'" style="color: rgb(255 255 0);">一般风险</span>
                <span v-if="scope.row.judgeExplain == '低风险'" style="color: rgb(0 0 255);">低风险</span>
              </template>
            </el-table-column>
            <el-table-column prop="judgeExplain" label="操作" show-overflow-tooltip width="80">
              <template slot-scope="scope">
                <span style="color: #5188fc; cursor: pointer;" @click="look2(scope.row)">编辑</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </template>
    <gradeDetails :multipleSelection="multipleSelection" :dialogVisible="dialogVisible" @closeDialog="closeDialog" @closeDialogRole="closeDialogRole"></gradeDetails>
    <scoreDetails
      :multipleSelection="multipleSelection2"
      :dataList="tableD"
      :dialogVisible="dialogVisible2"
      @closeDialog="closeDialog2"
      @closeDialogRole="closeDialogRole2"
    ></scoreDetails>
  </div>
</template>

<script type="text/ecmascript-6">
import gradeDetails from './gradeDetails'
import scoreDetails from './scoreDetails'

export default {
  components: { gradeDetails, scoreDetails},
  data() {
    return {
      dialogVisible: false,
      dialogVisible2: false,
      tableL: [],
      tableE: [],
      tableC: [],
      tableD: [],
      tableLoading: false,
      multipleSelection: [],
      multipleSelection2: []
    }
  },
  created() {
    // this.getTableData();
  },
  mounted() {
    this.getTableData()

  },
  methods: {
    getTableData() {
      this.tableLoading = true
      this.$api.ipsmRiskJudgeFindList({}).then(res => {
        if (res.code == 200) {
          this.tableL = res.data.LECDLInfo
          this.tableE = res.data.LECDEInfo
          this.tableC = res.data.LECDCInfo
          this.tableD = res.data.LECDRiskLevelInfo
        } else {
          this.$message.error(res.message)
        }
        this.tableLoading = false
      })
    },
    look(val) {
      this.multipleSelection = val
      this.dialogVisible = true
    },
    look2(val) {
      this.multipleSelection2 = val
      this.dialogVisible2 = true
    },
    closeDialog() {
      this.dialogVisible = false
    },
    closeDialog2() {
      this.multipleSelection2 = []
      this.dialogVisible2 = false
    },
    closeDialogRole() {
      this.dialogVisible = false
      this.getTableData()
    },
    closeDialogRole2() {
      this.dialogVisible2 = false
      this.getTableData()
    }
  }
}
</script>
<style lang="scss" scoped>
.toptip {
  margin-bottom: 16px;
}
</style>
