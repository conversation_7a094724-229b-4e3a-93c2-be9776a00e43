<template>
  <PageContainer v-loading="contentLoading">
    <div slot="header" class="table-content">
      <div class="topFilter">
        <div class="backBar">
          <span style="cursor: pointer" @click="$router.go(-1)">
            <i class="el-icon-arrow-left"></i>
            课程详情
          </span>
        </div>
      </div>
      <div class="baseInfo">
        <h1>基础信息</h1>
        <div class="contenter">
          <el-image style="width: 100px; height: 100px" :src="formInfo.coverUrl"
            :preview-src-list="[formInfo.coverUrl]">
          </el-image>
          <!-- <img
            :src="formInfo.coverUrl"
            alt=""
          /> -->
          <div class="center">
            <div class="item">
              <span class="key">课程名称：</span>
              <span class="label">{{ formInfo.courseName }}</span>
            </div>
            <div class="item">
              <span class="key">所属科目：</span>
              <span class="label">{{ formInfo.subjectName }}</span>
            </div>
            <div class="item">
              <span class="key">课程中认证学员：</span>
              <span class="label">{{ formInfo.isAuth == 0 ? '认证' : '不认证' }}</span>
            </div>
          </div>
          <div class="right">
            <div class="item">
              <span class="key">课程公开范围：</span>
              <span class="label">{{ formInfo.deptName }}</span>
            </div>
            <div class="item">
              <span class="key">课程介绍：</span>
              <span class="label">
                <p>{{ formInfo.comments }}</p>
              </span>
            </div>
            <div class="item">
              <span class="key">审批状态：</span>
              <!-- <span class="label">{{ formInfo.approvalState == '2' ? '审核中' : formInfo.approvalState == '3' ? '未通过' :
    formInfo.approvalState == '4' ? '已通过' : '' }} -->
              <span v-if="processDataList.length < 1" class="label">
                <p>暂无记录</p>
              </span>
              <el-tooltip v-else placement="bottom" effect="light" popper-class="process-tooltip">
                <div slot="content">
                  <div ref="hiddenRealTime" class="hiddenRealTime">
                    <div v-for="(item, index) in processDataList" :key="index" class="itemWrap">
                      <div class="dateTime">
                        {{ moment(item.createTime).format("YYYY-MM-DD") }}
                        <span class="statusIcon">
                          <span v-if="index > 0" class="whiteIcon"></span>
                        </span>
                        <span>{{ moment(item.createTime).format("HH:MM:ss") }}</span>
                        <!-- <span class="line"></span> -->
                      </div>
                      <div class="matterConten">
                        <div class="processsName">
                          <span>实验室审核人：{{ item.approveName }}</span>
                          <div class="processType">
                            <img v-if="item.approveResult == 1" src="../../../../assets/images/pass.png" alt="">
                            <span>{{ item.approveResult == 1 ? '通过' : '未通过' }}</span>
                          </div>
                        </div>
                        <div class="processEvaluate">
                          <span class="key">审核评价：</span>
                          <span class="value">{{ item.evaluate }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <span class="questionsNum">审核记录
                  <img class="questionImg" src="../../../../assets/images/process.png" alt="" />
                </span>
              </el-tooltip>
              <!-- </span> -->
            </div>
          </div>
        </div>
      </div>
    </div>
    <div slot="content" class="courseContent">
      <h1>课程目录</h1>
      <div class="table_content">
        <div class="item" v-for="(item, index) in formInfo.coursePeriodDTOList" :key="index">
          <div>
            <img src="../../../../assets/images/doc.png" alt="" />
            <span>{{ index + 1 }}.{{ item.periodName }}</span>
          </div>
          <div>课后习题:
            <span class="questionsNum" @click="seeQuestions(item)">{{ item.questionCount || 0 }}题
              <img class="questionImg" src="../../../../assets/images/question.png" alt="" />
            </span>
          </div>
          <!-- <div
            >已录入题目:
            <span class="questionsNum"
              >----题
              <img
                class="questionImg"
                src="../../../assets/images/question.png"
                alt=""
              />
            </span>
          </div> -->
          <div class="questionsNum" @click="seeFile(item, formInfo.coursePeriodDTOList)">查看课件</div>
        </div>
      </div>
      <!-- 课后习题列表 -->
      <answer-info ref="answerInfo" :seeType="seeType" :drawerDialog="drawerDialog" @closeDrawer="closeDrawer">
      </answer-info>
    </div>

  </PageContainer>
</template>
<script>
  import moment from 'moment'
  import answerInfo from './answerInfo.vue'
  export default {
    components: {
      answerInfo
    },
    data() {
      return {
        contentLoading: false,
        routeInfo: '',
        moment,
        paginationData: {
          startSize: 1,
          pageSize: 15,
          total: 0,
        },
        processDataList: [{
            createTime: '2023-12-12',
            name: '赵鑫',
            processEvaluate: '内容'
          },
          {
            createTime: '2023-12-12',
            title: 'title',
            processEvaluate: '内容2'
          }
        ],
        formInfo: {
          courseName: '',
          coverUrl: '',
          subjectName: '',
          isAuth: '',
          deptId: '',
          comments: '',
          approvalState: '',
          coursePeriodDTOList: []
        },
        id: '',
        drawerDialog: false,
        seeType: 'public'
      };
    },
    created() {
      this.routeInfo = JSON.parse(sessionStorage.getItem('routeInfo'))
      this.id = this.$route.query.id
      if (this.id) {
        this.getDetails();
        // this.getCourseDetList()
      }
    },
    methods: {
      // 获取详情
      getDetails() {
        this.contentLoading = true
        this.$api.getCourseDetails({
          id: this.id
        }).then((res) => {
          if (res.code == '200') {
            if (res.data.coursePeriodDTOList) {
              let newArr = []
              newArr = res.data.coursePeriodDTOList
              newArr && newArr.forEach(i => {
                i.coursePeriodQuestionList && i.coursePeriodQuestionList.forEach(k => {
                  k.isExpand = true
                  k.options = JSON.parse(k.options)
                  if (k.type == '2') {
                    k.answer = k.answer.split(',')
                  }
                })
              })
              res.data.coursePeriodDTOList = newArr
            } else {
              res.data.coursePeriodDTOList = []
            }
            this.formInfo = res.data
            this.formInfo.deptId = res.data.deptId.split(',')
          } else {
            this.$message.error(res.msg)
          }
          this.contentLoading = false
        });
      },
      // 查看课件
      seeFile(item, list) {
        console.log(item, '1111111111111111111111');

        if (item.type == 1) {
          this.$router.push({
            path: 'videoPlay',
            query: {
              courseId: item.courseId,
              id: item.id,
              type: 'see'
            }
          })
        } else {
          this.$router.push({
            path: 'seeFile',
            query: {
              itemInfo: JSON.stringify(item),
              type: 'see'
            }
          })
        }
      },
      // 查看试题
      seeQuestions(item) {
        if (!item.questionCount) {
          return this.$message.warning('没有课后习题哦！')
        }
        this.drawerDialog = true
        this.$refs.answerInfo.questionsList(item)
      },
      // 关闭弹窗
      closeDrawer() {
        this.drawerDialog = false
      },
      // // 获取课程审批记录
      // getCourseDetList() {
      //   let params = {
      //     busId: this.id,
      //     current: 1,
      //     size: 999,
      //   }
      //   this.$http.approveRecordPage(params).then(res => {
      //     if (res.code == 200) {
      //       this.processDataList = res.data.list
      //     }
      //   })
      // },
    },
  };

</script>
<style>
  .process-tooltip {
    width: 712px;
    height: 528px;
  }

</style>
<style lang="scss" scoped>
  .table-content {
    background: #fff;
    border-radius: 4px;
    height: calc(100% - 0px);

    .baseInfo {
      padding: 24px;

      .contenter {
        padding-top: 24px;
        display: flex;
        font-size: 14px;

        .center {
          flex: 1;
          margin: 0 24px;
        }

        .right {
          flex: 1;
        }

        .item {
          display: flex;
          align-items: center;

          .key {
            color: #666;
            width: 120px;
            text-align: left;
          }

          .label {
            flex: 1;
          }
        }

        .item:nth-child(2) {
          margin: 24px 0;
        }
      }
    }
  }

  .topFilter {
    padding: 15px;
    height: 60px;
    background-color: #fff;

    .backBar {
      color: #121f3e;
      height: 30px;
      border-bottom: 1px solid #dcdfe6;
    }
  }

  .courseContent {
    height: 100%;
    margin-top: 16px;
    background-color: #fff;
    padding: 24px;

    .table_content {
      height: calc(100% - 70px);
      margin-top: 16px;
      overflow: auto;

      .item {
        height: 56px;
        font-size: 14px;
        border-bottom: 1px solid #e5e6eb;
        display: flex;
        align-items: center;
        justify-content: space-between;

        >div:nth-child(1) {
          width: 50%;
        }

        >div:nth-child(2) {
          width: 25%;
        }

        >div:nth-child(3) {
          width: 25%;
        }

        img {
          margin-right: 10px;
          vertical-align: middle;
        }

        span {
          color: #7f848c;
        }
      }
    }
  }

  .questionsNum {
    color: #3562db !important;
    margin-left: 10px;
    cursor: pointer;

    .questionImg {
      width: 16px !important;
      height: 16px !important;
      vertical-align: middle;
    }
  }

  //鼠标悬浮提示边框
  .process-tooltip .el-tooltip__popper .tooltip-inner {
    border: 1px solid transparent;
    /* 自定义边框颜色 */
    background-color: #fff;
    border-radius: 4px;
    /* 自定义边框圆角 */
  }

  .hiddenRealTime {
    width: 100%;
    height: 500px;
    overflow-y: auto;

    .itemWrap {
      div {
        min-height: 30px;
        line-height: 30px;
      }

      .dateTime {
        display: flex;
        align-items: center;

        .statusIcon {
          margin: 0 14px 0 6px;
          display: inline-block;
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background-color: #3562db;
          position: relative;

          .whiteIcon {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 8px;
            height: 8px;
            background-color: #fff;
            border-radius: 50%;
          }
        }
      }

      .matterConten {
        padding-left: 100px;

        .processsName {
          display: flex;
          padding: 8px 16px;

          .processType {
            width: 65px;
            height: 24px;
            margin-left: 20px;
            background: #E8FFEA;
            border-radius: 4px;
            color: #009A29;
            text-align: center;

            img {
              vertical-align: middle;
              margin-right: 8px;
            }
          }
        }

        .processEvaluate {
          padding: 8px 16px;
          background-color: #FAF9FC;
          display: flex;

          .key {
            width: 70px;
          }

          .value {
            flex: 1;
          }
        }

      }
    }
  }

</style>
