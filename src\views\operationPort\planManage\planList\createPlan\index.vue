<template>
  <PageContainer :footer="true">
    <div slot="content" class="form-content">
      <div class="form-title"><i class="el-icon-arrow-left" @click="$router.go(-1)"></i>{{ pageTitle }}</div>
      <div class="content-main">
        <el-steps v-show="$route.query?.planType != 1 && !isPreview" :active="stepsCurrent" align-center class="main-steps">
          <el-step title="基础配置"></el-step>
          <el-step title="预案执行配置"></el-step>
        </el-steps>
        <p v-if="$route.query?.type == 'preview'" class="previewTips">
          <i class="el-icon-warning"></i>
          <span>请操作右侧弹窗面板，此时预览环境中，根据流程图生成相关操作界面（不会对真实工作环境有影响）</span>
        </p>
        <div class="main-central">
          <el-form v-show="stepsCurrent == 1" ref="formInline" :model="formInline" :rules="rules">
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="10">
                <el-form-item label="预案名称" prop="planName" label-width="150px">
                  <el-input v-model="formInline.planName" type="text" placeholder="请输入预案名称，最多输入20个字" show-word-limit maxlength="20"></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="10">
                <el-form-item label="关联预案类型" prop="planType" label-width="150px">
                  <el-select v-model="formInline.planType" filterable placeholder="请选择预案类型" style="width: 100%" @change="planTypeChange">
                    <el-option v-for="(item, index) in planTypeList" :key="index" :label="item.thirdSystemName" :value="item.thirdSystemCode"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="10">
                <el-form-item label="关联报警类型" prop="alarmType" label-width="150px">
                  <el-select v-model="formInline.alarmType" filterable placeholder="请选择报警类型" style="width: 100%" :disabled="!formInline.planType">
                    <el-option v-for="item in alarmTypeList" :key="item.id" :label="item.alarmDictName" :value="item.id"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :md="10">
                <el-form-item label="关联空间类型" prop="spaceType" label-width="150px">
                  <el-cascader
                    v-model="formInline.spaceType"
                    filterable
                    :options="spaceTypeList"
                    placeholder="请选择空间类型"
                    :props="{ value: 'id', label: 'name' }"
                    collapse-tags
                    clearable
                  ></el-cascader>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="20">
                <el-form-item label="说明" label-width="150px">
                  <el-input v-model="formInline.regulationsDesc" type="textarea" placeholder="请输入预案说明，最多输入50个字" show-word-limit maxlength="50"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="10">
                <el-form-item label="法规流程图" label-width="150px" class="upload-item">
                  <el-upload
                    :drag="true"
                    :file-list="imgFileList"
                    action="string"
                    :before-upload="(file) => beforeAvatarUpload(file, 'img')"
                    :http-request="(file) => httpRequset(file, 'img')"
                    :on-remove="(file, fileList) => handleRemove(file, fileList, 'img')"
                  >
                    <i class="el-icon-upload"></i>
                    <div class="el-upload__text">
                      将文件拖到此处，或<em>{{ imgFileList.length ? '重新上传' : '点击上传' }}</em>
                    </div>
                    <div class="el-upload__tip" style="color: #cbced3">支持JPG、PNG，大小不超过20M</div>
                  </el-upload>
                </el-form-item>
              </el-col>
              <el-col :md="10">
                <el-form-item label="法规文档" label-width="150px" class="upload-item">
                  <el-upload
                    :drag="true"
                    :file-list="textFileList"
                    action="string"
                    :before-upload="(file) => beforeAvatarUpload(file, 'text')"
                    :http-request="(file) => httpRequset(file, 'text')"
                    :on-remove="(file, fileList) => handleRemove(file, fileList, 'text')"
                  >
                    <i class="el-icon-upload"></i>
                    <div class="el-upload__text">
                      将文件拖到此处，或<em>{{ textFileList.length ? '重新上传' : '点击上传' }}</em>
                    </div>
                    <div class="el-upload__tip" style="color: #cbced3">支持PDF、DOC，大小不超过20M</div>
                  </el-upload>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="20">
                <el-form-item label="法规文案" label-width="150px">
                  <Editor ref="myTextEditor" v-model="formInline.regulationsText" class="myTextEditor"></Editor>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <el-button v-if="stepsCurrent == 2 && !isPreview" type="primary" plain icon="el-icon-picture" class="viewImg" @click="isViewImgFile = true">法规流程图</el-button>
          <flowChart v-if="stepsCurrent == 2" :eventData="addEventData" :isPreview="isPreview" :currentStepId="currentStepId" @eventFinish="eventFinish" />
          <previewProcess v-if="isPreview" :eventData="addEventData" :isPreview="isPreview" :currentStepId="currentStepId" @previewChange="previewChange" />
        </div>
      </div>
      <div v-if="isViewImgFile" draggable="true" class="viewImgFile" :style="{ left: elLeft + 'px', top: elTop + 'px' }" @dragstart="dragstart($event)" @dragend="dragend($event)">
        <i class="el-icon-close" @click="isViewImgFile = false"></i>
        <img :src="$tools.imgUrlTranslation(formInline.regulationsFlow.length ? formInline.regulationsFlow[0].url : '')" alt="法规流程图" />
      </div>
    </div>
    <div slot="footer">
      <el-button v-show="stepsCurrent == 1 && $route.query?.planType != 1" type="primary" @click="nextStep()">下一步</el-button>
      <el-button v-show="stepsCurrent == 2 && !isPreview" style="padding: 8px 22px" @click="previousStep()">上一步</el-button>
      <el-button v-show="stepsCurrent == 2 && !isPreview" type="primary" plain @click="previewPlan">预览</el-button>
      <el-button v-show="isPreview" type="primary" plain @click="previewPlan">重新预览</el-button>
      <el-button v-show="stepsCurrent == 2 || $route.query?.planType == 1 || isPreview" type="primary" @click="submitForm">保存</el-button>
    </div>
  </PageContainer>
</template>
<script>
import { treeToList } from '@/util'
import flowChart from '../../components/flowChart'
import previewProcess from '../../components/previewProcess'
var pageTitle
export default {
  name: 'createPlan',
  components: {
    flowChart,
    previewProcess
  },
  // 路由前置守卫动态修改meta中的title
  beforeRouteEnter(to, from, next) {
    if (to.meta.title) {
      const typeList = {
        add: '创建智能预案',
        edit: '编辑智能预案',
        copy: '创建智能预案',
        preview: '预览智能预案'
      }
      pageTitle = typeList[to.query.type] ?? '创建智能预案'
      to.meta.title = pageTitle
    }
    next()
  },
  async beforeRouteLeave(to, from, next) {
    if (!['planList'].includes(to.name)) {
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      pageTitle,
      stepsCurrent: 1, // 步骤
      isViewImgFile: false,
      isPreview: false, // 预览
      currentStepId: '', // 预览当前步骤id
      formInline: {
        planName: '', // 预案名称
        planType: '', // 预案类型
        alarmType: '', // 报警类型
        alarmTypeName: '', // 报警类型名称
        planSource: '', // 预案来源
        spaceType: [], // 空间类型
        spaceTypeName: '', // 空间类型名称
        regulationsDesc: '', // 说明
        regulationsFlow: [], // 法规流程图
        regulationsDoc: [], // 法规文档
        regulationsText: '', // 法规文案
        warnEventList: [], // 提示列表
        noticeEventList: [], // 通知列表
        confirmEventList: [] // 确认列表
      },
      rules: {},
      planTypeList: [], // 预案类型列表
      alarmTypeList: [], // 报警类型列表
      spaceTypeList: [], // 空间类型列表
      spaceTypeData: {},
      imgFileList: [], // 法规流程图
      textFileList: [], // 法规文档
      addEventData: {
        1: {}, // 演习
        2: {}, // 演习-启动预案
        3: {}, // 演习-自定义
        4: {}, // 确警
        5: {}, // 确警-启动预案
        6: {} // 确警-自定义
      },
      startclientX: 0, // 元素拖拽前距离浏览器的X轴位置
      startclientY: 0, // 元素拖拽前距离浏览器的Y轴位置
      elLeft: 0, // 元素的左偏移量
      elTop: 0 // 元素的右偏移量
    }
  },
  computed: {},
  watch: {
    'formInline.planType'(val) {
      if (val != '' && this.planTypeList.length) {
        this.$refs.formInline.validateField('planType')
      }
    },
    'formInline.alarmType'(val) {
      if (val != '' && this.alarmTypeList.length) {
        this.$refs.formInline.validateField('alarmType')
      }
    }
  },
  activated() {
    this.initData()
  },
  created() {
    if (!this.$store.state.keepAlive.list.includes('planList')) {
      this.initData()
    }
  },
  methods: {
    initData() {
      let planNameValidate = (rule, value, callback) => {
        let params = {
          planSource: this.formInline?.planSource ?? '',
          id: this.formInline?.id ?? '',
          planCategory: this.$route.query?.planType ?? '',
          planName: value
        }
        this.$api.PlanNameExist(params).then((res) => {
          if (res.code == 200) {
            if (res.data) {
              callback()
            } else {
              callback('预案名称已存在')
            }
          }
        })
      }
      Object.assign(this.$data, this.$options.data())
      this.$set(this.$data, 'rules', {
        planName: [
          { required: true, message: '请输入预案名称', trigger: 'blur' },
          { max: 20, message: '预案名称不得超出20个字符', trigger: 'blur' },
          { validator: planNameValidate, trigger: 'blur' }
        ],
        alarmType: [{ required: true, message: '请选择报警类型', trigger: 'blur' }],
        planType: [{ required: true, message: '请选择预案类型', trigger: 'blur' }]
      })
      // 编辑 预览
      if (['edit', 'preview'].includes(this.$route.query?.type) && this.$route.query?.id) {
        this.getPlanDetail()
      } else if (this.$route.query?.type == 'copy' && this.$route.query?.id) {
        // 复制预案模板信息
        this.getTemplateData()
      } else {
        this.getAlarmSystem()
      }
      this.$nextTick(() => {
        this.$refs.formInline.resetFields()
        this.$refs.myTextEditor.tinymceInit()
        if (this.$route.query?.type == 'preview') {
          this.stepsCurrent = 2
          this.previewPlan()
        }
        this.getSuperiorData()
      })
    },
    // 拖拽开始事件
    dragstart(e) {
      this.startclientX = e.clientX // 记录拖拽元素初始位置
      this.startclientY = e.clientY
    },
    // 拖拽完成事件
    dragend(e) {
      let x = e.clientX - this.startclientX // 计算偏移量
      let y = e.clientY - this.startclientY
      this.elLeft += x // 实现拖拽元素随偏移量移动
      this.elTop += y
    },
    previewChange(val) {
      if (val == 'close') {
        this.isPreview = false
        this.currentStepId = ''
        return
      }
      this.currentStepId = val
      console.log(val)
    },
    // 预览
    previewPlan() {
      this.isPreview = true
      this.currentStepId = '0'
    },
    // 获取预案详情
    getPlanDetail() {
      this.$api.GetPlanDetail({ id: this.$route.query?.id }).then((res) => {
        if (res.code == 200) {
          let {
            planName,
            planType,
            regulationsDesc,
            regulationsFlow,
            regulationsDoc,
            regulationsText,
            warnEventList,
            noticeEventList,
            confirmEventList,
            alarmType,
            spaceType,
            id,
            planSource
          } = res.data
          this.formInline = {
            id,
            planSource,
            planName,
            planType: planType,
            alarmType,
            spaceType: spaceType ? spaceType.split(',') : [],
            regulationsDesc,
            regulationsFlow: JSON.parse(regulationsFlow),
            regulationsDoc: JSON.parse(regulationsDoc),
            regulationsText,
            warnEventList: [],
            noticeEventList: [],
            confirmEventList: []
          }
          this.imgFileList = JSON.parse(regulationsFlow)
          this.textFileList = JSON.parse(regulationsDoc)
          this.getAlarmSystem()
          if (this.formInline.planType) {
            this.getDictionaryList(this.formInline.planType)
          }
          if (this.$route.query?.planType == 1) return
          warnEventList.forEach((item) => {
            if (Object.keys(this.addEventData[item.stepType]).includes('warnEventList')) {
              this.addEventData[item.stepType].warnEventList.push(item)
            } else {
              this.addEventData[item.stepType].warnEventList = [item]
            }
          })
          noticeEventList.forEach((item) => {
            item.noticeWay = Number(item.noticeWay)
            item.noticeType = Number(item.noticeType)
            if (Object.keys(this.addEventData[item.stepType]).includes('noticeEventList')) {
              this.addEventData[item.stepType].noticeEventList.push(item)
            } else {
              this.addEventData[item.stepType].noticeEventList = [item]
            }
          })
          confirmEventList.forEach((item) => {
            if (Object.keys(this.addEventData[item.stepType]).includes('confirmEventList')) {
              this.addEventData[item.stepType].confirmEventList.push(item)
            } else {
              this.addEventData[item.stepType].confirmEventList = [item]
            }
          })
        }
      })
    },
    // 获取模板数据
    getTemplateData() {
      this.$api.GetBasicConfigDetail({ id: this.$route.query?.id }).then((res) => {
        if (res.code == 200) {
          let { planName, planType, regulationsDesc, regulationsFlow, regulationsDoc, regulationsText, warnEventList, noticeEventList, confirmEventList } = res.data
          this.formInline = {
            planName,
            planType: planType,
            alarmType: '',
            spaceType: [],
            regulationsDesc,
            regulationsFlow: JSON.parse(regulationsFlow),
            regulationsDoc: JSON.parse(regulationsDoc),
            regulationsText,
            warnEventList: [],
            noticeEventList: [],
            confirmEventList: []
          }
          this.imgFileList = JSON.parse(regulationsFlow)
          this.textFileList = JSON.parse(regulationsDoc)
          this.getAlarmSystem()
          if (this.formInline.planType) {
            this.getDictionaryList(this.formInline.planType)
          }
          if (this.$route.query?.planType != 0) return
          warnEventList.forEach((item) => {
            if (Object.keys(this.addEventData[item.stepType]).includes('warnEventList')) {
              this.addEventData[item.stepType].warnEventList.push(item)
            } else {
              this.addEventData[item.stepType].warnEventList = [item]
            }
          })
          noticeEventList.forEach((item) => {
            item.noticeWay = Number(item.noticeWay)
            item.noticeType = Number(item.noticeType)
            if (Object.keys(this.addEventData[item.stepType]).includes('noticeEventList')) {
              this.addEventData[item.stepType].noticeEventList.push(item)
            } else {
              this.addEventData[item.stepType].noticeEventList = [item]
            }
          })
          confirmEventList.forEach((item) => {
            if (Object.keys(this.addEventData[item.stepType]).includes('confirmEventList')) {
              this.addEventData[item.stepType].confirmEventList.push(item)
            } else {
              this.addEventData[item.stepType].confirmEventList = [item]
            }
          })
        }
      })
    },
    // 保存
    submitForm() {
      let drillKeys = Object.keys(this.addEventData[3])
      let alarmKeys = Object.keys(this.addEventData[6])
      if (this.$route.query?.planType == 0) {
        if (
          !(drillKeys.includes('noticeEventList') && this.addEventData[3].noticeEventList.length) &&
          !(drillKeys.includes('warnEventList') && this.addEventData[3].warnEventList.length) &&
          !(drillKeys.includes('confirmEventList') && this.addEventData[3].confirmEventList.length)
        ) {
          return this.$message.warning('演习节点事件不能为空')
        }
        if (
          !(alarmKeys.includes('noticeEventList') && this.addEventData[6].noticeEventList.length) &&
          !(alarmKeys.includes('warnEventList') && this.addEventData[6].warnEventList.length) &&
          !(alarmKeys.includes('confirmEventList') && this.addEventData[6].confirmEventList.length)
        ) {
          return this.$message.warning('确警节点事件不能为空')
        }
      }
      this.$api.GetSaveVersion({ planSource: this.formInline?.planSource ?? '' }).then((res) => {
        if (res.code == 200) {
          this.$confirm('确定保存当前预案' + res.data + ' 版本吗', '提示', {
            cancelButtonClass: 'el-button--primary is-plain',
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
            .then(() => {
              if (this.$route.query?.planType == 0) {
                Object.values(this.addEventData).forEach((item) => {
                  if (item.warnEventList && item.warnEventList.length) {
                    this.formInline.warnEventList = [...this.formInline.warnEventList, ...item.warnEventList]
                  }
                  if (item.noticeEventList && item.noticeEventList.length) {
                    this.formInline.noticeEventList = [...this.formInline.noticeEventList, ...item.noticeEventList]
                  }
                  if (item.confirmEventList && item.confirmEventList.length) {
                    this.formInline.confirmEventList = [...this.formInline.confirmEventList, ...item.confirmEventList]
                  }
                })
              }
              let userInfo = this.$store.state.user.userInfo.user
              let params = { ...this.formInline, createName: userInfo.staffName, updateName: userInfo.staffName, planCategory: this.$route.query?.planType }
              params.regulationsDoc = JSON.stringify(params.regulationsDoc)
              params.regulationsFlow = JSON.stringify(params.regulationsFlow)
              params.spaceTypeName = params.spaceType.map((v) => this.spaceTypeData[v]).join(' / ')
              params.alarmTypeName = this.alarmTypeList.find((v) => v.id == params.alarmType)?.alarmDictName
              params.spaceType = params.spaceType.join(',')
              this.savePlanConfig(params)
            })
            .catch(() => {
              console.log('取消保存')
            })
        }
      })
    },
    // 预案新增
    savePlanConfig(params) {
      let header = {}
      if (this.$route.query?.type === 'edit') {
        header = { 'operation-type': 2, 'operation-id': this.$route.query?.id, 'operation-name': this.formInline.planName }
      } else {
        header = { 'operation-type': 1 }
      }
      this.$api.SavePlanConfig(params, header).then((res) => {
        if (res.code == 200) {
          this.$message.success('保存成功')
          this.$router.go(-1)
        }
      })
    },
    // 弹窗选择完成
    eventFinish(type, data) {
      this.addEventData[type] = data
      console.log(type, data, JSON.stringify(this.addEventData))
    },
    // 上一步
    previousStep() {
      this.stepsCurrent = 1
    },
    // 下一步
    nextStep() {
      this.$refs.formInline.validate((valid) => {
        if (valid) {
          this.stepsCurrent = 2
        }
      })
    },
    handleRemove(file, fileList, type) {
      if (type == 'img') {
        this.formInline.regulationsFlow = ''
        this.imgFileList = []
      } else {
        this.formInline.regulationsDoc = ''
        this.textFileList = []
      }
    },
    httpRequset(file, type) {
      const params = new FormData()
      params.append('file', file.file)
      this.$api.uploadIcon(params).then((res) => {
        if (res.code == 200) {
          if (type == 'img') {
            this.formInline.regulationsFlow = [{ name: file.file.name, url: res.data }]
            this.imgFileList = [{ name: file.file.name, url: res.data }]
          } else {
            this.formInline.regulationsDoc = [{ name: file.file.name, url: res.data }]
            this.textFileList = [{ name: file.file.name, url: res.data }]
          }
          this.$message({
            message: '上传成功',
            type: 'success'
          })
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },
    beforeAvatarUpload(file, type) {
      const isPNG = file.type === 'image/png'
      const isJPG = file.type === 'image/jpeg'
      const isDOC = file.type === 'application/msword' || file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      const isPDF = file.type === 'application/pdf' || file.type === 'application/vnd.ms-powerpoint'
      const isLt20M = file.size / 1024 / 1024 < 20
      if (!isLt20M) {
        this.$message.error('文件大小不能超过20MB!')
      }
      if (type == 'img') {
        if (!isPNG && !isJPG) {
          this.$message.error('仅支持JPG、PNG格式!')
        }
        return (isPNG || isJPG) && isLt20M
      } else {
        if (!isDOC && !isPDF) {
          this.$message.error('仅支持PDF、DOC格式!')
        }
        return (isDOC || isPDF) && isLt20M
      }
    },
    planTypeChange(val) {
      this.formInline.alarmType = ''
      this.getDictionaryList(val)
    },
    // 获取报警类型
    getDictionaryList(val) {
      this.$api.getAlarmThirdTypeData({ thirdSystemCode: val }).then((res) => {
        if (res.code == 200) {
          this.alarmTypeList = res.data
        }
      })
    },
    // 获取预案类型
    getAlarmSystem() {
      this.$api.getAlarmThirdSystemData().then((res) => {
        if (res.code == 200) {
          this.planTypeList = res.data
        }
      })
    },
    // 获取空间类型字典
    getSuperiorData() {
      this.$api.GetSuperiorData({ dictionaryCategoryId: 'SPACE_FUNCTION' }).then((res) => {
        if (res.code == 200) {
          this.spaceTypeList = this.handleTreeList(res.data[0].children)
          this.spaceTypeData = {}
          treeToList(this.spaceTypeList).forEach((v) => {
            this.spaceTypeData[v.id] = v.name
          })
        }
      })
    },
    handleTreeList(list) {
      for (var i = 0; i < list.length; i++) {
        if (list[i].children.length < 1) {
          list[i].children = undefined
        } else {
          this.handleTreeList(list[i].children)
        }
      }
      return list
    }
  }
}
</script>
<style lang="scss" scoped>
.form-content {
  height: 100%;
  overflow-y: auto;
  .form-title {
    height: 40px;
    line-height: 40px;
    background: #fff;
    border-bottom: 1px solid #dcdfe6;
    font-size: 15px;
    font-family: 'PingFang SC-Medium', 'PingFang SC';
    font-weight: 500;
    color: #121f3e;
    padding-left: 15px;
    i {
      width: 20px;
      cursor: pointer;
    }
  }
  .content-main {
    height: calc(100% - 40px);
    width: 100%;
    background: #fff;
    padding: 16px 0px 0px 0px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    ::v-deep(.main-steps) {
      padding: 0px 0px 24px 0px;
      .el-step__head {
        .el-step__line {
          height: 1px;
          background: #e5e6eb;
          .el-step__line-inner {
            border-color: #3562db;
            border-bottom: none;
          }
        }
        .el-step__icon {
          border: none;
          width: 18px;
          height: 18px;
        }
      }
      .el-step__main {
        .is-finish {
          color: #3562db !important;
        }
        .el-step__title {
          margin-top: 5px;
          font-size: 14px;
          line-height: 20px;
          color: #86909c;
          font-weight: 500;
        }
      }
      .el-step__head {
        .el-step__icon {
          background: #f2f3f5;
        }
        .el-step__icon-inner {
          font-weight: 500;
          font-size: 12px;
          color: #86909c;
          line-height: 18px;
        }
      }
      .is-finish {
        .el-step__icon {
          background: #3562db;
        }
        .el-step__icon-inner {
          font-weight: 500;
          font-size: 12px;
          color: #ffffff;
          line-height: 18px;
        }
      }
    }
    .previewTips {
      display: flex;
      align-items: center;
      justify-content: center;
      i {
        font-size: 18px;
        color: #fa8c2b;
      }
      span {
        margin-left: 8px;
        font-weight: 300;
        font-size: 14px;
        color: #666666;
      }
    }
    .main-central {
      flex: 1;
      overflow: auto;
      position: relative;
      .viewImg {
        position: absolute;
        right: 16px;
        top: 0;
      }
      ::v-deep(.el-cascader) {
        line-height: 32px;
        width: 100%;
        .el-input__inner {
          height: 32px !important;
        }
      }
    }
    ::v-deep(.upload-item) {
      .el-form-item__content {
        line-height: 16px;
      }
    }
    .myTextEditor {
      height: auto;
    }
  }
  .viewImgFile {
    width: 360px;
    height: 640px;
    position: absolute;
    left: 10px;
    right: 10px;
    cursor: move;
    z-index: 1000;
    user-select: none;
    background: #fff;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #dcdfe6;
    .el-icon-close {
      background: #bdbdbf;
      border-radius: 4px;
      position: absolute;
      right: 0;
      top: 0;
      font-size: 20px;
      color: #fff;
      cursor: pointer;
    }
    img {
      pointer-events: none;
      object-fit: scale-down;
      width: 100%;
      height: 100%;
    }
  }
}
</style>
