<template>
  <el-dialog
    v-dialogDrag
    class="component housing-edit"
    :title="title"
    width="30%"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    custom-class="model-dialog"
    destroy-on-close
    @close="onDialogClosed"
  >
    <div class="dialog-submit">
      <el-form ref="formRef" class="housing-list__form" :model="formInfo" :rules="rules" inline>
        <el-form-item prop="reason" label="弃选原因">
          <el-input
            type="textarea"
            v-model="formInfo.reason"
            style="width: 380px"
            placeholder="弃选原因"
            :rows="6"
            :disabled="disabled"
            resize="none"
            maxlength="500"
            show-word-limit
            clearable
          ></el-input>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <el-button type="primary" plain @click="onDialogClosed">取消</el-button>
      <el-button type="primary" v-if="!disabled" @click="onSubmit">确认</el-button>
    </template>
  </el-dialog>
</template>
<script>
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    extendReason: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      title: '弃选确认',
      formInfo: {
        reason: ''
      },
      rules: {
        reason: [{ required: true, message: '请输入弃选原因', trigger: 'blur' }]
      }
    }
  },
  mounted() {
    this.formInfo.reason = this.extendReason
    this.title = this.id ? '弃选确认' : '弃选原因'
  },
  methods: {
    onDialogClosed() {
      this.$emit('qxDialogclose')
    },
    onSubmit() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.confirmAgain()
        }
      })
    },
    confirmAgain() {
      let html = `<div style="display: flex;align-items: center;">
            <span class="el-icon-warning" style="color: #f59a23;font-size: 22px;margin-right: 5px;"></span><span style="font-weight: 600;">弃选确认</span>
        </div>
        <div style="padding-left: 28px;">弃选后，当前成员3个月内将无法申请公租房</div>`
      this.$alert(html, '', {
        dangerouslyUseHTMLString: true,
        showClose: false,
        showCancelButton: true
      })
        .then(() => {
          this.submitFn()
        })
        .catch(() => {})
    },
    submitFn() {
      let params = {
        reason: this.formInfo.reason,
        projectAllocationId: this.id
      }
      this.$api.rentalHousingApi.abandonSelectionSubmit(params).then((res) => {
        if (res.code == 200) {
          this.$message({
            type: 'success',
            message: res.message
          })
          this.$emit('qxSubmitDialog')
        } else {
          this.$message({
            type: 'error',
            message: res.message
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.model-dialog .el-dialog__body {
  background: #fff;
  .dialog-submit {
    padding: 20px;
    box-sizing: border-box;
    width: 100%;
    background: #fff;
  }
  .el-form-item {
    margin-bottom: 0;
  }
}
</style>