<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    :title="title"
    width="60%"
    :visible.sync="visible"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <div class="detail_content">
      <div class="content_left">
        <div class="peoplePicture video_group">
          <div class="video_Carousel_item">
            <rtspCavas
              ref="rtspCavas"
              style="width: 100%; height: 100%"
              :hasCavas="peopleVideoList[0] ? !!peopleVideoList[0].rtspUrl : false"
              :rtspUrl="peopleVideoList[0] ? peopleVideoList[0].rtspUrl : ''"
              :videoName="peopleVideoList[0] ? peopleVideoList[0].positionDeviceName : ''"
              :hasControl="false"
              :isLight="false"
              @toHiddenOperation="(e) => toHiddenRightBox(e, videoItem)"
            >
            </rtspCavas>
          </div>
        </div>
        <div class="video_group">
          <div class="thingPicture">
            <div class="video_Card_item">
              <rtspCavas
                ref="rtspCavas"
                style="width: 100%; height: 100%"
                :hasCavas="thingVideoList[0] ? !!thingVideoList[0].rtspUrl : false"
                :rtspUrl="thingVideoList[0] ? thingVideoList[0].rtspUrl : ''"
                :videoName="thingVideoList[0] ? thingVideoList[0].positionDeviceName : ''"
                :hasControl="false"
                :isLight="false"
                @toHiddenOperation="(e) => toHiddenRightBox(e, videoItem)"
              >
              </rtspCavas>
            </div>
            <div class="video_Card_item">
              <rtspCavas
                ref="rtspCavas"
                style="width: 100%; height: 100%"
                :hasCavas="thingVideoList[1] ? !!thingVideoList[1].rtspUrl : false"
                :rtspUrl="thingVideoList[1] ? thingVideoList[1].rtspUrl : ''"
                :videoName="thingVideoList[1] ? thingVideoList[1].positionDeviceName : ''"
                :hasControl="false"
                :isLight="false"
                @toHiddenOperation="(e) => toHiddenRightBox(e, videoItem)"
              >
              </rtspCavas>
            </div>
          </div>
        </div>
      </div>
      <div class="content_right">
        <div class="data_statistics">
          <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
            <span class="green_line"></span>
            数据统计
          </div>
          <div class="data_num">
            <div v-for="(item, index) in dataNumList" :key="index" class="data_num_item">
              <span class="label">{{ item.label }}</span>
              <span class="num">{{ item.num }}</span>
            </div>
          </div>
        </div>
        <div class="personnel_snapshot">
          <div class="snapshot_header">
            <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
              <span class="green_line"></span>
              人员抓拍
            </div>
            <div class="deatils" @click="CaptureList('people')">详情></div>
          </div>
          <div v-if="peopleImgList.length" class="people_img">
            <div v-for="(item, index) in peopleImgList" :key="index" class="img_item">
              <div class="img_item_content">
                <img :src="item.snapUrl" alt="" :style="{ height: item.abnormal ? 'calc(100% - 12px)' : '100%' }" />
                <div v-if="!item.empty" class="position" :style="{ bottom: item.abnormal ? '12px' : '0' }">{{ item.alarmPosition }}</div>
                <div v-if="item.abnormal" class="tips">异常报警</div>
              </div>
            </div>
          </div>
        </div>
        <div class="personnel_snapshot">
          <div class="snapshot_header">
            <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
              <span class="green_line"></span>
              安检照片
            </div>
            <div class="deatils" @click="CaptureList('thing')">详情></div>
          </div>
          <div class="people_img">
            <div v-for="(item, index) in thingImgList" :key="index" class="img_item">
              <div class="img_item_content">
                <img :src="item.snapUrl" alt="" :style="{ height: item.abnormal ? 'calc(100% - 12px)' : '100%' }" />
                <div v-if="!item.empty" class="position" :style="{ bottom: item.abnormal ? '12px' : '0' }">{{ item.alarmPosition }}</div>
                <div v-if="item.abnormal" class="tips">异常报警</div>
              </div>
            </div>
          </div>
        </div>
        <div class="traffic_statistics">
          <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
            <span class="green_line"></span>
            人流量统计
          </div>
          <div id="Echart" class="charts" style="width: 100%; height: 100%"></div>
        </div>
      </div>
    </div>
    <!-- 抓拍列表 -->
    <capture-list v-if="listDialog" ref="scuritySafety" :visible.sync="listDialog" :title="title" :typeNum="typeNum" :positionId="positionId"></capture-list>
  </el-dialog>
</template>
<script>
import * as echarts from 'echarts'
import moment from 'moment'
import captureList from './captureList.vue'
import snocPic from '@/assets/images/monitor/no_capture .png'
export default {
  components: { captureList },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    positionId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      snocPic,
      peopleVideoList: [],
      thingVideoList: [],
      dataNumList: [
        {
          label: '安检人数',
          value: 'securityCheckCount',
          num: '0'
        },
        {
          label: '安检包数',
          value: 'securityPackageCount',
          num: '0'
        },
        {
          label: '体温报警',
          value: 'temperatureAlarmCount',
          num: '0'
        },
        {
          label: '安检门报警',
          value: 'securityCheckAlarmCount',
          num: '0'
        },
        {
          label: '安检机报警',
          value: 'securityPackageAlarmCount',
          num: '0'
        }
      ],
      peopleImgList: [],
      thingImgList: [],
      listDialog: false,
      typeNum: ''
    }
  },
  mounted() {
    this.getDataCount()
    this.getCameraList()
    this.getSnopList(1)
    this.getSnopList(2)
    this.getLineCharts()
  },
  methods: {
    // 获取视频流
    getCameraList() {
      let params = {
        positionId: this.positionId
      }
      this.$api.getCameraList(params).then((res) => {
        if (res.code == 200) {
          this.peopleVideoList = res.data.filter((item) => {
            return item.deviceType == 1
          })
          // if(!this.peopleVideoList.length){
          //   this.peopleVideoList = [
          //     {
          //       rtspUrl:'',
          //       positionDeviceName:''
          //     }
          //   ]
          // }
          this.thingVideoList = res.data.filter((item) => {
            return item.deviceType == 2
          })
          // if(!this.thingVideoList.length){
          //   this.thingVideoList = [
          //     {
          //       rtspUrl:'',
          //       positionDeviceName:''
          //     },
          //     {
          //       rtspUrl:'',
          //       positionDeviceName:''
          //     }
          //   ]
          // }else if(this.thingVideoList.length==1){
          //   this.thingVideoList.push(
          //     {
          //       rtspUrl:'',
          //       positionDeviceName:''
          //     }
          //   )
          // }
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 获取数据统计
    getDataCount() {
      let params = {
        positionId: this.positionId
      }
      this.$api.getTodyCount(params).then((res) => {
        if (res.code == 200) {
          this.dataNumList.forEach((el) => {
            el.num = res.data[el.value]
          })
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 获取抓拍列表
    getSnopList(type) {
      let params = {
        beginTime: moment().format('YYYY-MM-DD') + ' 00:00:00',
        endTime: moment().format('YYYY-MM-DD') + ' 23:59:59',
        currentPage: 1,
        devicePositionId: this.positionId,
        pageSize: 6,
        type
      }
      this.$api.getSnopList(params).then((res) => {
        if (res.code == 200) {
          if (res.code == 200) {
            let imgList = type == 1 ? 'peopleImgList' : 'thingImgList'
            this[imgList] = []
            this[imgList] = res.data.records
            if (this[imgList].length < 6) {
              let obj = {
                snapUrl: this.snocPic,
                alarmPosition: '',
                abnormal: 0,
                empty: true
              }
              let arr = new Array(6 - this[imgList].length).fill(obj)
              this[imgList] = this[imgList].concat(arr)
            }
          }
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 获取人流量折线图
    getLineCharts() {
      let params = {
        positionId: this.positionId
      }
      this.$api.getLineCharts(params).then((res) => {
        if (res.code == 200) {
          setTimeout(() => {
            this.getChartData(res.data)
          }, 1000)
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 控制右侧显示隐藏
    toHiddenRightBox(type, item) {
      if (type == 'screen') {
        this.toggleFullscreen()
      } else {
        item.isLight = !item.isLight
      }
    },
    getChartData(data) {
      var chartDom = document.getElementById('Echart')
      var myChart = echarts.init(chartDom)
      var hours = data.map((item) => item.hour)
      var option
      option = {
        grid: {
          top: '12%',
          bottom: '8%'
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: hours,
          axisTick: {
            show: false // 隐藏 X 轴刻度线
          }
        },
        yAxis: {
          name: '单位：人',
          type: 'value'
        },
        series: this.dataArr(data)
      }
      myChart.resize()
      myChart.clear()
      option && myChart.setOption(option)
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
    closeDialog() {
      this.$emit('update:visible', !this.visible)
    },
    CaptureList(type) {
      this.typeNum = type
      this.listDialog = true
    },
    dataArr(data) {
      let arr = [
        {
          name: '全部',
          type: 'line',
          data: [],
          symbol: 'circle', // 设置圆点形状为圆形
          itemStyle: {
            borderWidth: 2, // 设置圆点边框宽度
            color: '#3562db'
          },
          lineStyle: {
            color: '#3562db' // 设置线条颜色为红色
          }
        }
      ]
      data.forEach((v) => {
        v.columnList?.forEach((sonV, index) => {
          let obj = arr.find((ele) => ele.name == sonV.key)
          var value = sonV.value
          if (obj) {
            obj.data.push(value)
          } else {
            let dataObj = {
              name: sonV.key,
              type: 'line',
              data: [value],
              symbol: 'circle', // 设置圆点形状为圆形
              itemStyle: {
                borderWidth: 2, // 设置圆点边框宽度
                color: '#3562db'
              },
              lineStyle: {
                color: '#3562db' // 设置线条颜色为红色
              }
            }
            arr.push(dataObj)
          }
        })
      })
      return arr
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .model-dialog {
  width: 80% !important;
  margin-top: 0 !important;
  // height: 1000px !important;
}
::v-deep .model-dialog .el-dialog__body {
  max-height: 100%;
  height: 800px;
}
.detail_content {
  width: 100%;
  height: 100%;
  display: flex;
  .content_left {
    width: calc(100% - 640px - 16px);
    margin-right: 16px;
    height: 100%;
    background: #fff;
    overflow: auto;
    padding: 24px;
    .peoplePicture {
      height: 350px;
      margin-bottom: 10px;
      background: #f6f5fa;
    }
    .thingPicture {
      width: 100%;
      height: 350px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      > div {
        width: 50%;
        height: 100%;
        background: #f6f5fa;
        &:nth-child(2) {
          margin-left: 10px;
        }
      }
    }
    .video_group {
      padding-top: 0;
      padding-left: 0;
      display: flex;
    }
    .video_Carousel_item {
      margin-right: 0;
      width: 100%;
      height: 100%;
    }
    .video_Carousel_item_empty {
      margin-right: 0;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #fff;
    }
    .video_Card_item {
      width: calc(100% - 16px);
      height: calc(100% - 0px);
    }
    .video_Card_item_empty {
      width: calc(100% - 16px);
      height: calc(100% - 16px);
      display: flex;
      align-items: center;
      justify-content: center;
      background: #fff;
    }
  }
  .content_right {
    overflow: auto;
    width: 640px;
    height: 100%;
    background: #fff;
    padding: 16px;
    .data_num {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .data_num_item {
        width: 125px;
        height: 82px;
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: center;
        background-color: #faf9fc;
        span {
          display: block;
        }
        .label {
          color: #666;
          font-size: 14px;
        }
        .num {
          font-weight: 600;
          font-size: 18px;
        }
      }
    }
    .personnel_snapshot {
      .snapshot_header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .deatils {
          cursor: pointer;
          width: 60px;
          color: #96989a;
        }
      }
    }
    .people_img {
      display: flex;
      align-items: center;
      .img_item {
        width: 100px;
        height: 100px;
        margin-right: 10px;
        &:last-child {
          margin-right: 0;
        }
        &_content {
          width: 100%;
          height: 100%;
          position: relative;
          img {
            width: 100%;
          }
          .tips {
            width: 100%;
            height: 18px;
            background: rgba(255, 55, 55, 0.2);
            color: #ff2d55;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            &::before {
              content: '';
              width: 14px;
              height: 14px;
              display: inline-block;
              background: url('@/assets/images/monitor/ic_sos.png') no-repeat;
              background-size: 100% 100%;
            }
          }
          &:hover {
            .position {
              position: absolute;
              bottom: 12px;
              width: 100%;
              height: 40px;
              background: rgba(0, 0, 0, 0.6);
              color: #fff;
              text-overflow: unset;
              overflow: visible;
              white-space: normal;
            }
          }
          .position {
            position: absolute;
            bottom: 12px;
            width: 100%;
            height: 20px;
            line-height: 20px;
            background: rgba(0, 0, 0, 0.6);
            color: #fff;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
        }
      }
    }
    .traffic_statistics {
      width: 100%;
      height: 260px;
    }
  }
}
.el-icon-arrow-left {
  cursor: pointer;
}
</style>
