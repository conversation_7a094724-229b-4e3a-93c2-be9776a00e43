// 导入封装好的axios实例
import request from './request'
import qs from 'qs'
import store from '@/store/index'
import router from '@/router/index'
/**
 * methods: 请求
 * @param url 请求地址
 * @param params 请求参数
 */
function getUserCode() {
  let userCode = {}
  if (store.getters['user/isLogin']) {
    const userInfo = store.state.user.userInfo.user
    userCode = {
      hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
      unitCode: userInfo.unitCode ?? 'BJSYGJ'
    }
  }
  return userCode
}
export function postFile(url, http = __PATH.VUE_MONITOR_API, contentType = 'json') {
  return function (params = {}, headers = {}) {
    const config = {
      method: 'post',
      url: http + url,
      headers: {
        'Content-Type': contentType == 'json' ? 'application/json' : 'application/x-www-form-urlencoded;charset=utf8'
      }
    }
    config.data = params
    return request(config)
  }
}
export function getRequest(url, http = __PATH.VUE_MONITOR_API, contentType = 'json') {
  return function (params, headers = {}, newHttp = http) {
    let userCode = getUserCode()
    const config = {
      method: 'get',
      url: newHttp + url,
      headers: {
        'Content-Type': contentType == 'json' ? 'application/json' : 'application/x-www-form-urlencoded;charset=utf8',
        ...headers,
        ...userCode
      }
    }
    config.params = { ...params }
    return request(config)
  }
}
export function postRequest(url, http = __PATH.VUE_MONITOR_API, contentType = 'json') {
  return function (params = {}, headers = {}, newHttp = http) {
    let userCode = {}
    if (store.getters['user/isLogin']) {
      const userInfo = store.state.user.userInfo.user
      userCode = {
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
    }
    const config = {
      method: 'post',
      url: newHttp + url,
      headers: {
        'Content-Type': contentType == 'json' ? 'application/json' : 'application/x-www-form-urlencoded;charset=utf8',
        ...headers,
        ...userCode
      }
    }
    config.data = { ...params, ...userCode }
    return request(config)
  }
}
export function dataExport(url, http = __PATH.VUE_MONITOR_API, contentType = 'json') {
  return function (params = {}, headers = {}, newHttp = http) {
    let userCode = {}
    if (store.getters['user/isLogin']) {
      const userInfo = store.state.user.userInfo.user
      userCode = {
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }

    }
    const config = {
      method: 'post',
      url: newHttp + url,
      responseType: 'blob',
      headers: {
        'Content-Type': contentType == 'json' ? 'application/json' : 'application/x-www-form-urlencoded;charset=utf8',
        ...headers,
        ...userCode
      }
    }
    config.data = { ...params, ...userCode }
    return request(config)
  }
}
// 针对jetlink物联特殊处理
export function postRequest1(url, http = __PATH.VUE_MONITOR_API, body = {}, contentType = 'json') {
  return function (params = {}, headers = {}, newHttp = http) {
    if (store.getters['user/isLogin']) {
      // 在这里添加登录检查逻辑
    }

    const config = {
      method: 'post',
      url: `${newHttp}${url}?${new URLSearchParams(params).toString()}`,
      headers: {
        'Content-Type': contentType === 'json' ? 'application/json' : 'application/x-www-form-urlencoded;charset=utf8',
        ...headers,
      },
      data: body,
    };

    return request(config);
  };
}
// 导出文件
export function postRequest2(url, http = __PATH.VUE_MONITOR_API, body = {}, contentType = 'json') {
  return function (params = {}, headers = {}, newHttp = http) {
    if (store.getters['user/isLogin']) {
      // 在这里添加登录检查逻辑
    }
    const config = {
      method: 'post',
      url: `${newHttp}${url}?${new URLSearchParams(params).toString()}`,
      responseType: 'blob',
      headers: {
        'Content-Type': contentType === 'json' ? 'application/json' : 'application/x-www-form-urlencoded;charset=utf8',
        ...headers,
      },
      data: body,
    };

    return request(config);
  };
}
export function postQs(url, http = __PATH.VUE_MONITOR_API, contentType = 'form') {
  return function (params, headers = {}) {
    const config = {
      method: 'post',
      url: http + url,
      headers: {
        'Content-Type': contentType == 'json' ? 'application/json' : 'application/x-www-form-urlencoded;charset=utf8',
        ...headers
      }
    }
    config.data = qs.stringify(params)
    return request(config)
  }
}

