<template>
  <PageContainer class="videoMonitor">
    <div slot="header" class="videoMonitor-heade">
      <i class="el-icon-arrow-left" @click="() => {$router.go(-1)}"/>
      <span>视频监控</span>
    </div>
    <div slot="content" class="videoMonitor-main">
      <div v-loading="leftLoading" class="main-left">
        <ContentCard title="设备" :cstyle="{ height: '100%' }">
          <div slot="content" class="left-content">
            <el-input v-model="searchValue" placeholder="设备名称搜索" suffix-icon="el-icon-search" clearable @input="searchForm" />
            <div class="content-main">
              <el-checkbox-group v-model="selectDeviceList" :max="4" @change="selectDeviceChange">
                <el-checkbox v-for="item in deviceList" :key="item.icmId" :label="item.icmId">{{ item.icmName }}</el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
        </ContentCard>
      </div>
      <div class="main-right">

      </div>
    </div>
  </PageContainer>
</template>

<script>
import { debounce } from 'lodash/function'
export default {
  name: 'videoMonitor',
  data() {
    return {
      leftLoading: false,
      searchValue: '',
      deviceList: [], // 设备列表
      selectDeviceList: []
    }
  },
  computed: {

  },
  created() {
    this.getCameraListByProjectCode()
  },
  methods: {
    selectDeviceChange(val) {
      console.log(val)
    },
    // 查询
    searchForm: debounce(function() {
      this.getCameraListByProjectCode()
    }, 500),
    getCameraListByProjectCode() {
      this.leftLoading = true
      this.$api.GetCameraListByProjectCode({...this.$route.query, icmName: this.searchValue}).then((res) => {
        this.leftLoading = false
        if (res.code == 200) {
          this.deviceList = res.data
        }
      })
    }
  }
}

</script>

<style lang="scss" scoped>
.videoMonitor {
  p {
    margin: 0;
  }

  ::v-deep .container-header {
    // margin-left: 16px;
    border-radius: 4px;
  }

  .videoMonitor-heade {
    display: flex;
    align-items: center;
    padding: 13px 16px;
    font-size: 14px;
    font-weight: 500;
    color: #121f3e;
    line-height: 14px;

    .el-icon-arrow-left {
      font-size: 13px;
      margin-right: 8px;
      cursor: pointer;
      color: #7f848c;
    }
  }

  .videoMonitor-main {
    height: 100%;
    overflow: auto;
    display: flex;
    padding-top: 16px;

    .main-left {
      width: 230px;
      margin-right: 16px;
      background: #fff;
      border-radius: 4px;

      .left-content {
        height: 100%;
        display: flex;
        flex-direction: column;

        .content-main {
          flex: 1;
          overflow: auto;
          padding-top: 16px;
        }

        :deep(.el-checkbox-group) {
          .el-checkbox {
            padding: 8px 12px;
            display: block;
            margin: 4px 0 0;

            .el-checkbox__label {
              color: #121f3e !important;
            }
          }

          .is-checked {
            background: rgb(53 98 219 / 20%);
          }
        }
      }
    }

    .main-right {
      flex: 1;
      background: #fff;
      border-radius: 4px;
    }
  }
}
</style>
