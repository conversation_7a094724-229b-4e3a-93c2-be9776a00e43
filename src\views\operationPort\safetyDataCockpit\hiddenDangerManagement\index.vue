<!--
 * @Description: 隐患管理
-->
<template>
  <div class="hiddenDangerManagement">
    <div class="hidden-header">
      <div class="hidden-header-right">
        <svg-icon name="cockpit-static" />
        <span class="right-text">隐患总数</span>
        <span class="right-num">{{ hiddenData.total }}</span>
      </div>
    </div>
    <div class="middle-progress">
      <div class="chart" :style="{ 'grid-template-columns': getLineWidth(progressList, hiddenData.total, 97, 1) }">
        <div v-for="(v, i) in progressList" :key="i" class="chart_line">
          <div class="line" :style="{ background: v.color }"></div>
        </div>
      </div>
      <div class="chart" :style="{ 'grid-template-columns': getLineWidth(progressList, hiddenData.total, 46, 18) }">
        <div v-for="(v, i) in progressList" :key="i" class="chart_line">
          <div class="line_text">
            <span :style="{ color: v.color, marginRight: '5px' }">{{ v.name }}</span>
            <span>{{ v.num }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="footer-box">
      <div class="box-left-icon"></div>
      <div class="box-right-text">
        <div class="text-box"><span class="text-title">三天以上</span><span class="text-num">{{hiddenData.moreThreeDays}}</span></div>
        <div class="text-box"><span class="text-title">三天以内</span><span class="text-num">{{hiddenData.lessThreeDays}}</span></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'hiddenDangerManagement',
  data() {
    return {
      hiddenData: {
        total: 0,
        moreThreeDays: 0,
        lessThreeDays: 0
      },
      progressList: [
        { name: '已派工', sort: 1, num: 0, color: '#09EDFF' },
        { name: '已挂单', sort: 2, num: 0, color: '#FFAE72' },
        { name: '已超时', sort: 3, num: 0, color: '#FF6068' }
      ]
    }
  },
  mounted() {
    this.getHiddenManageData()
  },
  methods: {
    // 获取隐患工单数量
    getHiddenManageData() {
      this.$api.getHiddenManageCount().then(res => {
        console.log(res)
        if (res.code == '200') {
          const data = res.data
          this.progressList[0].num = data.disWorkCount || 0
          this.progressList[1].num = data.hangUpCount || 0
          this.progressList[2].num = data.overTimeCount || 0
          this.hiddenData.total = data.workCount || 0
          this.hiddenData.moreThreeDays = data.DisExceedThreeDayCount || 0
          this.hiddenData.lessThreeDays = data.DisWithinThreeDayCount || 0
        }
      })
    },
    getLineWidth(eqTypeArr, total, cMax, cMin) {
      let newEqTypeArr = JSON.parse(JSON.stringify(eqTypeArr)).sort((a, b) => a.num - b.num)
      total = eqTypeArr.reduce((a, b) => a + b.num, 0)
      let lessThanlessNum = 0
      newEqTypeArr.forEach((ele) => {
        const numPercent = (ele.num / total) * 100
        if (numPercent > cMin) {
          lessThanlessNum++
        }
      })
      let deductNum = 0
      newEqTypeArr.map((ele) => {
        let num = (ele.num / total) * 100
        // 最大值不得超过cMax   为其他元素留位置   最小值不得小于cMin
        if (num < cMin) {
          deductNum += cMin - num
          num = cMin
        } else {
          if (num - deductNum > cMin) {
            num = Math.min(cMax, num - deductNum / lessThanlessNum)
          } else {
            lessThanlessNum = lessThanlessNum - 1 || 1
          }
        }
        ele.occupancy = `${num.toFixed(2)}% `
      })
      const orthodox = newEqTypeArr.sort((a, b) => a.sort - b.sort)
      return orthodox.map((ele) => `${ele.occupancy} `).join(' ')
    }
  }
}
</script>

<style lang="scss" scoped>
.hiddenDangerManagement {
  height: 100%;
  width: 100%;
  padding: 10px 10px 0 10px;
  overflow: hidden;
  .hidden-header {
    height: 40px;
    line-height: 40px;
    width: 100%;
    padding: 0 10px;
    display: flex;
    justify-content: flex-end;
    .hidden-header-right {
      .right-text {
        font-size: 14px;
        color: #1ffaff;
        margin: 0 4px;
      }
      .right-num {
        font-size: 16px;
        color: #ffffff;
      }
    }
  }
  .middle-progress {
    height: calc(66% - 40px);
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    .chart {
      display: grid;
      grid-gap: 0px;
      grid-template-columns: auto auto auto auto;
      .chart_line {
        width: calc(100% - 4px);
        .line {
          width: 100%;
          height: 10px;
          margin: 10px 0;
        }
        .line_text {
          span {
            font-size: 14px;
            font-weight: bold;
          }
        }
      }
    }
  }
  .footer-box {
    height: calc(34%);
    display: flex;
    align-items: flex-end;
    .box-left-icon {
      height: 100%;
      aspect-ratio: 3/5;
      background: url('@/assets/images/safetyDataCockpit/hidden-group.png') no-repeat;
      background-size: 100% 100%;
    }
    .box-right-text {
      width: 50%;
      height: 50%;
      background: linear-gradient(270deg, rgba(88, 135, 255, 0) 0%, rgba(88, 135, 255, 0.07) 29%, rgba(88, 135, 255, 0.12) 100%);
      display: flex;
      align-items: center;
      .text-box {
        margin-left: 20px;
        display: flex;
        align-items: center;
        .text-title {
          font-size: 14px;
          color: #ffffff;
          margin-right: 6px;
        }
        .text-num {
          font-size: 20px;
          color: #09edff;
        }
      }
    }
  }
}
</style>
