<template>
  <GroupConfigVue :projectCode="projectCode" />
</template>

<script>
import GroupConfigVue from '../airAndLightingCom/groupConfig.vue'
import { monitorTypeList } from '@/util/dict.js'
export default {
  components: {
    GroupConfigVue
  },
  data() {
    return {
      projectCode: monitorTypeList.find((item) => item.projectName == '照明监测').projectCode
    }
  },
  mounted() {
    
  },

  methods: {
    
  }
}
</script>
<style lang="scss" scoped>

</style>
