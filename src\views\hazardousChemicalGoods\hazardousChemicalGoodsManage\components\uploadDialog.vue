<template>
  <el-dialog title="资料添加" width="45%" :visible.sync="uploadDialogShow" v-dialogDrag custom-class="model-dialog"
    :before-close="closeDialog">
    <div class="content">
      <el-form ref="formRef" :model="formModel" label-width="140px">
        <el-row :gutter="20">
          <el-col :span="16">
            <el-form-item label="资料名称" prop="fileName">
              <el-input v-model="formModel.fileName"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="资料文件" prop="fileList">
              <el-upload :drag="true" :file-list="fileList" action="string"
                :before-upload="(file) => beforeAvatarUpload(file, 'text')"
                :http-request="(file) => httpRequest(file, 'text')"
                accept=".jpg, .jpeg, .png, .JPG, .JPEG,.word, .WORD, .Excel, .pdf, .PDF, .doc, .docx, .DOCX, .xlsx, .xls"
                :on-remove="(file, fileList) => handleRemove(file, fileList, 'text')">
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">
                  将文件拖到此处，或<em>{{ fileList.length ? '重新上传' : '点击上传' }}</em>
                </div>
                <div class="el-upload__tip" style="color: #cbced3">图片、word、excel、pdf 单个文件大小不允许超过5M</div>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="submitDialog">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'uploadDialog',
  components: {},
  props: {
    uploadDialogShow: {
      type: Boolean,
      default: false
    },
    fileArr: {
      type: Array,
      default: []
    },
    fileName: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      formModel: {
        fileName: ''
      },
      limitData: 6,
      fileList: [],
    }
  },
  mounted() {
    if (this.fileArr && this.fileArr.length) {
      this.fileList = this.fileArr
      this.formModel.fileName = this.fileName
    }
  },
  methods: {
    //  上传相关
    handleExceed(files, fileList) {
      this.$message.error(`最多传${this.limitData}份文件`);
    },
    handleRemove(files, fileList) {
      this.fileList = fileList
    },
    beforeAvatarUpload(file) {
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isLt5M) {
        this.$message.error("上传文件大小不能超过 5MB!");
        return false;
      }
    },
    httpRequest(item) {
      const params = new FormData()
      params.append('file', item.file)
      this.$api.uploadIcon(params).then((res) => {
        if (res.code == 200) {
          this.fileList.push({ name: item.file.name, url: res.data, uid: item.file.uid })
          this.$message({
            message: '上传成功',
            type: 'success'
          })
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },
    closeDialog() {
      this.$emit('closeUploadDialog')
    },
    submitDialog() {
      let fileInfo = {
        fileList: this.fileList,
        fileName: this.formModel.fileName
      }
      this.$emit('submitUploadDialog', fileInfo)
    }
  }
}
</script>
<style lang="scss" scoped>
.model-dialog {
  .content {
    margin: 0 auto;
    background: #fff;
    border-radius: 4px;
    width: 100%;
    height: 100%;
    padding: 16px;
    .el-input {
      width: 100%;
    }
  }
}
::v-deep .el-upload-dragger .el-icon-upload {
  margin: 20px 0 16px !important;
}
</style>

