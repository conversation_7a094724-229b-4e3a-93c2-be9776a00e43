<!--
 * @Author: hedd
 * @Date: 2023-08-16 16:11:06
 * @LastEditTime: 2025-03-18 15:46:27
 * @FilePath: \ihcrs_pc\src\views\monitor\securityMenu\securityMonitor.vue
 * @Description:
-->
<template>
  <security-operation-monitor :tabs="tabsList" />
</template>

<script>
import { monitorTypeList } from '@/util/dict.js'
import securityOperationMonitor from '../components/securityOperationMonitor/index.vue'
import { auth } from '@/util'
export default {
  name: 'securityMonitor',
  components: {
    securityOperationMonitor
  },
  beforeRouteEnter(to, from, next) {
    let names = []
    to.matched.map((v, i) => {
      if (i > 0) {
        v.components.default.name && names.push(v.components.default.name)
      }
    })
    next((vm) => {
      if (from.query.activeName) {
        vm.activeName = from.query.activeName
      }
      vm.$store.commit('keepAlive/add', names)
    })
  },
  async beforeRouteLeave(to, from, next) {
    // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
    if (!['monitorDetails'].includes(to.name)) {
      // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      tabsList: []
    }
  },
  computed: {

  },
  created() {
    const tabs = monitorTypeList.filter((item) => item.parentCode == monitorTypeList.find((item) => item.projectName == '安防系统监测').projectCode)
    this.tabsList = tabs.filter((item) => auth(item.menuAuth || ''))
    console.log(this.tabsList,'tabsListds');
    
  },
  methods: {

  }
}

</script>

<style lang="scss" scoped>

</style>
