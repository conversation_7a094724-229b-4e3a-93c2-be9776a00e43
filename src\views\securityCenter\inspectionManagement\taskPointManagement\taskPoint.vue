<template>
  <div class="content_box">
    <div class="top_content">
      <div style="margin-bottom: 20px;">
        <el-input
          v-model="taskPointName"
          style="width: 217px; margin-right: 20px;"
          placeholder="巡检点名称"
          maxlength="25"
          onkeyup="if(value.length>25)value=value.slice(0,25)"
        ></el-input>
        <el-input
          v-model="taskPointCode"
          style="width: 217px; margin-right: 20px;"
          placeholder="巡检点编号"
          maxlength="25"
          onkeyup="if(value.length>25)value=value.slice(0,25)"
        ></el-input>
        <el-button type="primary" plain style="margin-left: 30px;" @click="_resetCondition">重置</el-button>
        <el-button type="primary" @click="_searchByCondition">查询</el-button>
        <el-button
          type="primary"
          :disabled="selectedTableList.length == 0"
          :loading="isdownload"
          style="max-width: 120px; min-width: 80px; width: auto;"
          @click="download">
          巡检码下载
        </el-button>
      </div>
      <div>
        <el-button type="primary" icon="el-icon-plus" @click="addFn('add')">新增</el-button>
        <el-button type="primary" icon="el-icon-edit" :disabled="selectedTableList.length !== 1" @click="updateFn('edit')">编辑</el-button>
        <el-button style="padding: 8px 20px;" type="danger" icon="el-icon-delete" :disabled="selectedTableList.length !== 1" @click="deleteLocationPoint">删除</el-button>
      </div>
    </div>
    <div class="table_list" style="text-align: right; height: 87%;">
      <el-table
        ref="materialTable"
        v-loading="tableLoading"
        :data="tableData"
        height="calc(100% - 75px)"
        border
        style="width: 100%;"
        :cell-style="{ padding: '8px' }"
        stripe
        :header-cell-style="{ background: '#f2f4fbd1' }"
        highlight-current-row
        :empty-text="emptyText"
        :row-key="getRowKeys"
        @row-dblclick="toDetails"
        @selection-change="handleSelectionChange"
      >
        <el-table-column :reserve-selection="true" type="selection" width="55" align="center"></el-table-column>
        <el-table-column label="序号" type="index" width="80" align="center">
          <template slot-scope="scope">
            <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="taskPointCode" show-overflow-tooltip label="巡检点编号"></el-table-column>
        <el-table-column align="center" prop="taskPointName" show-overflow-tooltip label="巡检点名称"></el-table-column>
        <el-table-column align="center" show-overflow-tooltip label="所属区域">
          <template slot-scope="scope">
            <span>{{ formatting(scope.row) }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="engineerBrand" show-overflow-tooltip label="所属设备类型"></el-table-column>
        <el-table-column align="center" prop="locationPointName" show-overflow-tooltip label="关联定位点"></el-table-column>
        <el-table-column align="center" prop="remarks" show-overflow-tooltip label="描述"></el-table-column>
        <el-table-column align="center" prop="createDate" show-overflow-tooltip label="创建时间"></el-table-column>
      </el-table>
      <el-pagination
        class="table-page pagination"
        style="margin-top: 15px;"
        :current-page="paginationData.currentPage"
        :page-sizes="[15, 30, 50, 100]"
        :page-size="paginationData.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="paginationData.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
  </div>
</template>
<script type="text/ecmascript-6">
import qs from 'qs'
export default {
  name: 'taskPoint',
  components: {},
  data() {
    return {
      isdownload: false,
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      LOGINDATA: '',
      taskBookTypeArr: [],
      taskPointName: '', // 定位点名称
      taskPointCode: '', // 定位点编号
      searchDataObj: {
        workTypeCode: '',
        endDate: '',
        startDate: '',
        dateLine: '',
        unionSel: ''
      },
      tableData: [],
      selectedTableList: [],
      emptyText: '暂无数据',
      tableLoading: false,
      pageSize: '',
      currentPage: '',
      listRole: '',
      userIds: '',
      taskBookTypeName: '',
      downLoadId: '',
      downLoadIdArr: '',
      downLoadIdde: '',
      downLoadIdsp: '',
      downLoadIddeArr: []
    }
  },
  created() {
    // 获取登录信息
    this.LOGINDATA = JSON.parse(sessionStorage.getItem('LOGINDATA'))
  },
  mounted() {
    // 获取任务点列表
    this._findTaskPointList()
  },
  methods: {
    // 新增
    addFn(type) {
      this.$router.push({
        path: 'taskPointManagement/addTaskPoint',
        query: {
          type: type
        }
      })
    },
    updateFn(type) {
      console.log(type)
      this.$router.push({
        path: 'taskPointManagement/addTaskPoint',
        query: {
          type: type,
          id: this.selectedTableList[0].id
        }
      })
    },
    // 巡检码下载
    download() {
      var type = []
      this.selectedTableList &&
        this.selectedTableList.length &&
        this.selectedTableList.forEach((val, index) => {
          type.push(val.taskPointTypeCode)
        })
      // 下载
      this._downLoadQrCode(type)
    },

    _downLoadQrCode(types) {
      //  types:  代表新增字典时的  字典编码类型：设备、空间、自定义
      types.some((item, index) => {
        if (item == 'DEVICE_001') {
          // 设备下载
          this.selectedTableList &&
            this.selectedTableList.length &&
            this.selectedTableList.map((val, index) => {
              val.engineerCodes = val.engineerCode
            })
        }
        if (item == 'SPACE_001') {
          // 空间下载
          this.selectedTableList &&
            this.selectedTableList.length &&
            this.selectedTableList.map((val, index) => {
              val.engineerCodes1 = val.id
            })
        }
        if (item !== 'SPACE_001' && item !== 'DEVICE_001') {
          // 自定义下载
          this.selectedTableList &&
            this.selectedTableList.length &&
            this.selectedTableList.map((val, index) => {
              val.engineerCodes2 = val.sourceId
            })
        }
      })
      var idsArr = []
      this.selectedTableList &&
        this.selectedTableList.length &&
        this.selectedTableList.forEach((val, index) => {
          if (val.taskPointTypeCode == 'DEVICE_001') {
            idsArr.push(val.engineerCodes)
          } else if (val.taskPointTypeCode == 'SPACE_001') {
            idsArr.push(val.engineerCodes1)
          } else {
            idsArr.push(val.engineerCodes2)
          }
        })
      let taskCode =
        this.selectedTableList &&
        this.selectedTableList.length &&
        this.selectedTableList.map((val, index) => {
          return val.taskPointTypeCode
        })
      let taskName =
        this.selectedTableList &&
        this.selectedTableList.length &&
        this.selectedTableList.map((val, index) => {
          return val.taskPointName.indexOf('#') == -1 ? val.taskPointName : val.taskPointName.replace('#', '＃')
        })
      this.isdownload = true
      const params = {
        ids: idsArr.join(',') || '',
        taskPointTypeCode: taskCode.join(',') || '',
        taskPointName: taskName.join(',') || '',
        unitCode: this.LOGINDATA.unitCode || '',
        hospitalCode: this.LOGINDATA.hospitalCode || ''
      }
      fetch(`${__PATH.VUE_AQ_URL}/taskPoint/exportQrCode`, {
        method: 'POST',
        responseType: 'blob',
        body: qs.stringify(params),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      })
        .then((res) => {
          res.blob().then((blob) => {
            this.isdownload = false
            this.saveBlobAs(blob)
          })
        })
        .catch((err) => {
          console.log(err)
          this.isdownload = false
        })
    },
    // 保存blob的方法
    saveBlobAs(blob) {
      let _self = this
      if (window.navigator.msSaveOrOpenBlob) {
        navigator.msSaveBlob(blob)
      } else {
        const anchor = document.createElement('a')
        const body = document.querySelector('body')
        anchor.href = window.URL.createObjectURL(blob)
        anchor.download = '巡检码.zip'

        anchor.style.display = 'none'
        body.appendChild(anchor)
        anchor.click()
        body.removeChild(anchor)
        window.URL.revokeObjectURL(anchor.href)
        this.exportLoading = false
      }
    },
    // 查询表格
    _findTaskPointList() {
      this.tableLoading = true
      let obj = this.LOGINDATA
      const { taskPointName, taskPointCode, paginationData } = this
      let data = {
        taskPointName: taskPointName,
        taskPointCode: taskPointCode,
        pageNo: paginationData.currentPage,
        pageSize: paginationData.pageSize
      }
      this.$api.findTaskPointList(data).then((res) => {
        this.tableLoading = false
        if (res.code == 200) {
          this.tableData = res.data.list
          this.paginationData.total = parseInt(res.data.count)
          if (res.data.list.length == 0) {
            this.emptyText = '未查到任何记录，请放大查询条件试试！'
          }
        }
        this.tableLoading = false
      })
    },

    // 条件查询
    _searchByCondition() {
      this.paginationData.currentPage = 1
      this._findTaskPointList()
    },
    // 重置查询条件
    _resetCondition() {
      this.paginationData = {
        currentPage: 1,
        pageSize: 15,
        total: 0
      }
      this.taskPointCode = ''
      this.taskPointName = ''
      this._findTaskPointList()
    },
    // 删除列表
    deleteLocationPoint() {
      const { selectedTableList } = this
      let id =
        selectedTableList &&
        selectedTableList.length &&
        selectedTableList.map((val) => {
          return val.id
        })
      let poitName =
        selectedTableList &&
        selectedTableList.length &&
        selectedTableList.map((val) => {
          return val.taskPointName
        })
      console.log(id)
      this.$confirm('此操作将永久删除该巡检点, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let data = {
          id: id.join(','),
          taskPointName: poitName.join(',')
        }
        this.$api.deleteTaskList(data).then((res) => {
          const { code, data, message } = res
          if (code == 200) {
            this.$message.success(message)
            this._findTaskPointList()
            this.$refs.materialTable.clearSelection() // 清空表格选中状态
          } else {
            this.$message.error(message)
          }
        })
      })
    },
    // 双击查看详情
    toDetails(row) {},

    // 表格,勾选表格，对表格进行操作
    handleSelectionChange(val) {
      this.selectedTableList = val
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this._findTaskPointList()
      this.$refs.materialTable.clearSelection() // 清空表格选中状态
    },
    // 每页的条数
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.paginationData.currentPage = 1
      this._findTaskPointList()
      this.$refs.materialTable.clearSelection() // 清空表格选中状态
    },
    getRowKeys(row) {
      return row.id
    },
    formatting(row) {
      if (row.location) {
        return row.location.replace(/&gt;/g, '>')
      }
      return ''
    }
  }
}
</script>
<style lang="scss" scoped>
.content_box {
  height: 100%;
  margin-top: 10px;
  padding: 20px 25px 25px;
  background: #fff;
  display: flex;
  flex-direction: column;
}

.top_content {
  width: 100%;
  display: flex;
  flex-direction: column;
  margin-bottom: 25px;
}

.el-input {
  width: 200px;
}

.viewButton {
  color: #5188fc;
  cursor: pointer;
}

.green_line {
  display: inline-block;
  width: 4px;
  height: 16px;
  background: #5188fc;
  margin-right: 10px;
  vertical-align: middle;
}

.toptip {
  box-sizing: border-box;
  padding-left: 26px;
  height: 50px;
  width: 100%;
  line-height: 50px;
  text-align: left;
  border-bottom: 1px solid rgb(216 222 231 / 100%);
}

.left_content {
  margin: 20px 20px 0 35px;
}

.left {
  float: left;
  width: 268px;
  min-width: 14%;
  height: 97%;
  border-radius: 10px;
  background-color: #fff;
  margin: 10px;
  margin-top: 0;

  .middle_tools {
    margin-top: 10px !important;
  }
}

.right {
  position: relative;
  width: calc(100% - 300px);
  // flex: 1;
  // width: 56%;
  height: 97%;
  text-align: left;
  background-color: #fff;
  padding-left: 30%;
  // float: left;
  box-sizing: border-box;
  padding: 20px 10px 10px;
  margin: 10px 10px 10px 0;
  margin-left: 288px;
  border-radius: 10px;

  .top_filters {
    input,
    select {
      margin-right: 15px;
      width: 200px;
      height: 40px;
    }

    .search_button {
      background-color: #5188fc;
      color: #fff;
    }

    .search_button:hover {
      opacity: 0.7;
    }
  }

  .middle_tools {
    // margin-top: 20px;
    margin-bottom: 10px;
  }
}

.deleteButton {
  color: #f43530;
  cursor: pointer;
}

@media screen and (max-width: 1600px) {
  .pagination {
    position: absolute;
    bottom: 0;
    right: 15px;
  }

  .personDialog .el-dialog {
    height: 545px;
  }

  .toptip {
    height: 35px;
    line-height: 35px;
  }

  .left_content {
    height: 415px;
  }

  .left .middle_tools {
    line-height: 36px;
    box-shadow: 0 -4px 4px 0 #f3f4f8;
    box-shadow: 0 -4px 4px 0 #f3f4f8;
  }
}
</style>