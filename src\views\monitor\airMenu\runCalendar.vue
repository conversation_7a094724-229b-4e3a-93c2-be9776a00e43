<template>
  <RunCalendar :projectCode="projectCode" />
</template>

<script>
import RunCalendar from '@/views/monitor/lightingMonitoring/airAndLightingCom/runCalendar.vue'
import { monitorTypeList } from '@/util/dict.js'
export default {
  components: {
    RunCalendar
  },
  data() {
    return {
      projectCode: monitorTypeList.find((item) => item.projectName == '空调监测').projectCode
    }
  },
  mounted() {
    
  },

  methods: {
    
  }
}
</script>
<style lang="scss" scoped>

</style>
