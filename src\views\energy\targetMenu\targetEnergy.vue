<template>
  <PageContainer>
    <div slot="content" class="role-content" style="height: 100%">
      <div class="form-box">
        <el-form :inline="true" :model="formInline" class="demo-form-inline">
          <el-form-item>
            <el-date-picker v-model="formInline.inspectDate" type="month" placeholder="选择日期" value-format="timestamp"> </el-date-picker>
          </el-form-item>
          <el-form-item>
            <!-- 重置 -->
            <el-button type="primary" plain @click="onReset">重置</el-button>
            <el-button type="primary" @click="onSubmit">查询</el-button>
            <el-button type="primary" @click="onDeletes()">批量删除</el-button>
          </el-form-item>
        </el-form>
        <div>
          <el-button type="primary" class="form-btn" @click="onOpen('add')">新建</el-button>
        </div>
      </div>
      <div class="table-box">
        <el-table v-loading="tableLoading" border :data="tableData" style="width: 100%" stripe @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="65" align="center"> </el-table-column>
          <el-table-column prop="inspectDate" label="指标日期" width="180">
            <template slot-scope="{ row }"> {{ moment(row.inspectDate).format('YYYY-MM') }} </template>
          </el-table-column>
          <el-table-column prop="inspectEnergy" label="万元收入能耗支出（元）" width="180"> </el-table-column>
          <el-table-column prop="inspectElectricity" label="单位面积用电量（kwh/㎡）"> </el-table-column>
          <el-table-column prop="inspectWater" label="单位面积生活用水量（t/㎡）"> </el-table-column>
          <el-table-column prop="inspectBed" label="单位床位能耗支出（元/床）"> </el-table-column>
          <el-table-column prop="operation" label="操作">
            <template slot-scope="{ row }">
              <el-button type="text" @click="onOpen('edit', row)">编辑</el-button>
              <el-button type="text" class="delet" @click="onDeletes(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="contentTable-footer">
          <el-pagination
            :current-page="pagination.current"
            :page-sizes="pagination.pageSizeOptions"
            :page-size="pagination.size"
            :layout="pagination.layoutOptions"
            :total="pagination.total"
            @size-change="paginationSizeChange"
            @current-change="paginationCurrentChange"
          >
          </el-pagination>
        </div>
      </div>
      <!-- 弹窗 -->
      <addTarget ref="addTarget" :dialogTitle="dialogTitle" @onAddTarget="onAddTarget"></addTarget>
    </div>
  </PageContainer>
</template>
<script>
import moment from 'moment'
import addTarget from './component/addTarget.vue'
export default {
  name: 'targetEnergy',
  components: { addTarget },
  props: {},
  data() {
    return {
      moment,
      dialogTitle: '新增',
      tableLoading: false,
      tableData: [],
      multipleSelection: [],
      formInline: {
        inspectDate: ''
      },
      //   分页
      pagination: {
        current: 1,
        size: 15,
        total: 0,
        pageSizeOptions: [15, 25, 50, 100],
        layoutOptions: 'total, sizes, prev, pager, next, jumper'
      }
    }
  },
  created() {
    this.init()
  },
  mounted() {},
  methods: {
    // 新增
    onAddTarget(obj, val) {
      this.tableLoading = true
      let apiName = val === '0' ? this.$api.setTargetEnergy : val === '1' ? this.$api.updateTargetEnergy : ''
      if (!apiName) {
        this.tableLoading = false
        this.$message.error('新增失败！')
        return
      }
      // 新建添加
      apiName(obj).then((res) => {
        const { code, msg } = res
        if (code === '200') {
          this.$message.success(msg || '新增成功！')
        } else {
          this.$message.error(msg || '新增失败！')
        }
        this.init()
      })
    },
    // 查询
    onSubmit() {
      this.init()
    },
    // 重置
    onReset() {
      this.formInline.inspectDate = ''
      this.pagination.size = 15
      this.pagination.current = 1
      this.init()
    },
    // 编辑
    onOpen(type, row) {
      this.$refs.addTarget.resetForm() // 重置
      if (type === 'add') {
        this.dialogTitle = '新增'
      } else {
        this.dialogTitle = '编辑'
        this.$refs.addTarget.edit(row.id)
      }
    },
    // 删除
    onDeletes(row = {}) {
      let ids = ''
      if (Object.keys(row).length > 0) {
        ids = [row.id]
      } else {
        if (this.multipleSelection.length === 0) {
          this.$message.error('请选择要删除的数据！')
          return
        }
        ids = this.multipleSelection
          .map((item) => item.id)
          .join(',')
          .split(',')
      }
      this.tableLoading = true
      const params = {
        ids
      }
      this.$api.delTargetEnergy(params).then((res) => {
        console.log(res)
        const { code, msg } = res
        if (code === '200') {
          this.$message.success(msg || '删除成功！')
        } else {
          this.$message.error(msg || '删除失败！')
        }
        this.tableLoading = false
        this.init()
      })
    },
    // 多选
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    //
    paginationSizeChange(val) {
      this.pagination.size = val
      this.init()
    },
    paginationCurrentChange(val) {
      this.pagination.current = val
      this.init()
    },
    init() {
      this.tableLoading = true
      const { current, size, total } = this.pagination
      const { inspectDate } = this.formInline
      let params = {
        inspectDate,
        page: {
          current,
          size,
          total,
          orders: [
            {
              asc: true,
              column: ''
            }
          ]
        }
      }
      this.$api
        .getTargetEnergyList(params)
        .then((res) => {
          const {
            data: { list, totalCount },
            msg,
            code
          } = res
          if (code === '200') {
            this.tableData = list
            this.pagination.total = totalCount
          } else {
            this.$message.error(msg || '查询失败！')
          }
          this.tableLoading = false
        })
        .finally(() => {
          this.tableLoading = false
        })
    }
  }
  //   computed: {},
  //   watch: {}
}
</script>
<style lang="scss" scoped>
.role-content {
  background: #fff;
  padding: 15px;
  .form-box {
    display: flex;
    justify-content: space-between;
    .form-btn {
      margin-top: 4px;
    }
  }
  .table-box {
    display: flex;
    flex-direction: column;
    height: calc(100% - 62px);
    .contentTable-footer {
      padding-top: 10px;
    }
    .delet {
      color: red !important;
    }
    ::v-deep .el-table__body-wrapper {
      height: calc(100% - 50px);
      overflow-y: auto;
    }
  }
}
</style>
