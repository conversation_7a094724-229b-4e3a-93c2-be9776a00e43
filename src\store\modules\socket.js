/*
 * @Author: hedd heding<PERSON>@sinomis.com
 * @Date: 2023-05-18 15:20:12
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2024-05-08 16:02:29
 * @FilePath: \ihcrs_pc\src\store\modules\socket.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const state = {
  receiveMsg: null,
  iemcMsgs: null,
  elevatorMsgs: null,
  noticeMsgs: null
}

const getters = {
  socketMsgs: (state) => state.receiveMsg,
  socketIemcMsgs: (state) => state.iemcMsgs,
  socketelevatorMsgs: (state) => state.elevatorMsgs,
  socketNoticeMsgs: (state) => state.noticeMsgs
}

const actions = {}

const mutations = {
  setSocketMsgs(state, data) {
    state.receiveMsg = data
  },
  setSocketIemcMsgs(state, data) {
    state.iemcMsgs = data
  },
  setSocketElevatorMsgs(state, data) {
    state.elevatorMsgs = data
  },
  setNoticeSocketMsgs(state, data) {
    state.noticeMsgs = data
  }
}

export default {
  namespaced: true,
  state,
  actions,
  getters,
  mutations
}
