<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="taskPlanDetail-content-title" @click="goBack"><i class="el-icon-arrow-left"></i><span
          style="margin-left: 10px">返回</span></div>
      <div class="content_box">
        <div class="detaiBox">
          <div v-for="(item, index) in taskStatisticsData" :key="index" class="list-item">
            <div class="list-label">{{ item.name }}</div>
            <div v-if="taskInfo[item.key]" class="list-value">
              <el-tooltip class="item" effect="dark" :content="taskInfo[item.key]" placement="bottom-start"
                :disabled="taskInfo[item.key].length<22">
                <span>{{ taskInfo[item.key] || '--' }}</span>
              </el-tooltip>
            </div>
            <div v-else class="list-value">
              <span>--</span>
            </div>
          </div>
          <div class="list-item1">
            <div class="list-label">参与人员:</div>
            <div class="list-value1">{{ taskInfo.actorName || '--' }}</div>
          </div>
        </div>
        <div class="taskBox">
          <div class="detaiTitle">演练任务</div>
          <div class="taskList">
            {{taskInfo.drillDesc||"--"}}
          </div>
        </div>
        <div class="taskBox">
          <div class="detaiTitle">预案法规文档</div>
          <div class="regulationsDocBox" @click="openFile1(regulationsDoc)">
            <span class="list-label">预案法规文档:</span>
            <span class="list-value">{{regulationsDoc.length?regulationsDoc[0].name:""}}</span>
            <span v-if="regulationsDoc.length" class="list-upLoad">
              <i class="el-icon-download"></i>
            </span>
          </div>
        </div>
        <div class="taskBox">
          <div class="detaiTitle">法规文案</div>
          <div class="copywritinBox" @click="toDrawer">
            <span class="list-copywriting" v-html="regulationsText">
            </span>
          </div>
        </div>
        <div v-if="taskInfo.taskState===1">
          <div class="assessBox">
            <div class="detaiTitle">演练效果评定</div>
            <div class="assessList">
              <span class="list-label">演练效果评定:</span><span
                class="list-state state-color1">{{taskEffect.divideRadio?divideRadioText[taskEffect.divideRadio].text:''}}</span><span
                class="list-state state-color2">{{taskEffect.dutyRadio?dutyRadioText[taskEffect.dutyRadio].text : ''}}</span><span
                class="list-state state-color3">{{taskEffect.effectRadio?effectRadioText[taskEffect.effectRadio].text : ''}}</span>
            </div>
          </div>
          <div class="attachmentsBox">
            <div class="detaiTitle">演练附件 <span class="total">共</span><span
                class="number">{{taskFilelist.length?taskFilelist.length:0}}</span><span class="total">个附件</span></div>
            <div class="attachmentsLists">
              <div v-for="(item,index) in taskFilelist" :key="index" class="attachmentsLists-item"
                @click="openFile(item.url)">
                <div class="images"> <img src="@/assets/images/operationPort/fileText.png" />
                </div>
                <div class="content">{{item.name}}</div>
              </div>
            </div>
          </div>
          <div class="remarkBox">
            <div class="detaiTitle">演练备注</div>
            <div class="remarkList">
              <span class="list-label">演练备注:</span>
              <span v-if="taskRemark.remarkCheckList.length&&taskRemark.remarkCheckList[0]==='0'">
                有领导参加 ,</span>
              <span v-if="taskRemark.remarkCheckList.length&&taskRemark.remarkCheckList[0]==='1'">有安全事故,</span>
              <span v-if="taskRemark.remarkCheckList.length&&taskRemark.remarkCheckList.length===2">
                有领导参加,有安全事故,</span>
              <span class="list-value">{{taskRemark.remark||''}}</span>
            </div>
            <div>
              <span class="list-label">操作人员:</span><span class="list-value">{{taskInfo.updateName||""}}</span><span
                class="list-value">{{ moment(taskInfo.updateTime).format('YYYY-MM-DD')||"" }}</span>
            </div>
          </div>
        </div>
        <el-drawer title="法规文案" :visible.sync="drawer" :direction="direction" :before-close="handleClose">
          <div class="drawerBox" v-html="regulationsText"></div>
        </el-drawer>
      </div>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">关闭</el-button>
    </div>
  </PageContainer>
</template>
<script>
import moment from 'moment'
export default {
  name: 'taskPlanDetail',
  data() {
    return {
      moment,
      taskStatisticsData: [
        {
          name: '演练任务名称:',
          key: 'drillName'
        },
        {
          name: '演练类型:',
          key: 'drillTypeName'
        },
        {
          name: '演练部门:',
          key: 'deptNames'
        },
        {
          name: '周期类型:',
          key: 'cycleTypeName'
        },
        {
          name: '完成状态:',
          key: 'taskStateName'
        },
        {
          name: '演练时间:',
          key: 'exerciseTime'
        },
        {
          name: '演练地点:',
          key: 'drillPlace'
        },
        {
          name: '演练负责人:',
          key: 'headNames'
        },
        {
          name: '演练组织人:',
          key: 'organizerName'
        }
      ],
      regulationsDoc: [], // 法规文档
      regulationsText: '', // 法规文案
      taskInfo: {
        drillName: '', // 任务名称
        drillType: '', // 演练类型
        drillTypeName: '', // 类型名称
        deptNames: '', // 演练部门
        cycleTypeName: '', // 周期类型
        taskStateName: '', // 完成状态Name
        exerciseTime: '', // 演练时间
        drillPlace: '', // 演练地点
        headNames: '', // 演练负责人
        organizerName: '', // 演练组织人
        actorName: '', // 参演人员,
        drillDesc: '', // 演练任务描述
        updateName: '', // 更新人员
        updateTime: '', // 更新时间
        taskState: 0 // 任务状态
      },
      taskFilelist: [],
      cycleTypeList: [
        {
          cycleType: 0,
          label: '单次'
        },
        {
          cycleType: 1,
          label: '每周'
        },
        {
          cycleType: 2,
          label: '每月'
        },
        {
          cycleType: 3,
          label: '季度'
        },
        {
          cycleType: 4,
          label: '半年'
        },
        {
          cycleType: 5,
          label: '全年'
        }
      ],
      drillTypeList: [],
      taskEffect: {
        dutyRadio: '',
        divideRadio: '',
        effectRadio: ''
      },
      taskRemark: {
        remark: '',
        remarkCheckList: ''
      },
      divideRadioText: {
        '0': {
          text: '分工明确'
        },
        '1': {
          text: '分工不明确'
        }
      },
      dutyRadioText: {
        '0': {
          text: '职责清晰'
        },
        '1': {
          text: '职责不清晰'
        }
      },
      effectRadioText: {
        '0': {
          text: '达到预期效果'
        },
        '1': {
          text: '基本达到预期效果'
        },
        '2': {
          text: '未达到预期效果'
        }
      },
      drawer: false,
      direction: 'rtl'
    }
  },
  mounted() {
    this.getTypeList()
    setTimeout(() => {
      this.getTaskData()
    }, 500)
  },
  methods: {
    // 打开抽屉
    toDrawer() {
      this.drawer = true
    },
    // 关闭文案抽屉
    handleClose(dne) {
      this.drawer = false
    },
    // 获取演练类型列表
    getTypeList() {
      let data = {
        page: {
          current: 1,
          size: 9999
        }
      }
      this.$api
        .getPreplanDrillTypeData(data)
        .then((res) => {
          this.drillTypeList = res.data ? res.data.list : []
        })

    },
    getTaskData() {
      const taskId = this.$route.query.taskId
      this.$api.getTaskDataById({ id: taskId }).then((res) => {
        if (res.code == 200) {
          this.taskInfo = res.data
          this.taskInfo.cycleTypeName = this.cycleTypeFn(res.data.cycleType) || ''
          this.taskInfo.taskStateName = res.data.taskState === 0 ? '未完成' : '已完成'
          this.taskInfo.exerciseTime = moment(res.data.taskStartTime).format('YYYY-MM-DD') + '-' + moment(res.data.taskEndTime).format('YYYY-MM-DD')
          this.taskInfo.actorName = res.data.deptPersonList ? res.data.deptPersonList.join(',') : ''
          this.taskFilelist = res.data.taskUrl ? JSON.parse(res.data.taskUrl) : []
          this.taskEffect = res.data.taskEffect ? JSON.parse(res.data.taskEffect) : {}
          this.taskRemark = res.data.taskRemark ? JSON.parse(res.data.taskRemark) : {}
          this.regulationsDoc = res.data.regulationsDoc ? JSON.parse(res.data.regulationsDoc) : []
          this.regulationsText = res.data.regulationsText || ''
        } else if (res.message) {
          this.$message.error(res.message)
        }
      })
    },
    // 周期类型
    cycleTypeFn(e) {
      const item = this.cycleTypeList.filter((i) => i.cycleType === e)
      if (item)
        return item[0].label || ''
    },
    // 打开文件
    openFile(e) {
      const url = e
      window.open(url)
    },
    openFile1(list) {
      let url = this.$tools.imgUrlTranslation(list[0].url)
      window.open(url)
    },
    // 返回
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.container-content > div {
  background-color: #fff;
  height: 100%;
  position: relative;
  .taskPlanDetail-content-title {
    border-bottom: 1px solid #e4e7ed;
    padding: 13px 24px;
    cursor: pointer;
  }
  .content_box {
    padding: 16px 24px;
    background: #fff;
    display: flex;
    height: calc(100% - 50px);
    flex-direction: column;
    overflow-y: auto;
    .detaiTitle {
      font-weight: 500;
      font-size: 16px;
      color: #333333;
      padding: 10px 0px;
    }
    .detaiBox {
      display: flex;
      flex-wrap: wrap;
      font-size: 14px;
      .list-item {
        width: calc(100% / 3) !important;
        margin-bottom: 16px;
        display: flex;
      }
      .list-item1 {
        width: 100%;
        margin-bottom: 16px;
        display: flex;
      }
      .list-value {
        flex: 1;
        color: #666666;
        font-weight: 400;
        margin-left: 10px;
        white-space: nowrap; /* 不换行 */
        overflow: hidden; /* 隐藏溢出部分 */
        text-overflow: ellipsis; /* 使用省略号表示被截断的内容 */
      }
      .list-value1 {
        flex: 1;
        color: #666666;
        font-weight: 400;
        margin-left: 10px;
      }
    }
    .list-label {
      display: inline-block;
      width: 100px;
      color: #666666;
      font-weight: 400;
      text-align: right;
    }
    .list-value {
      display: inline-block;
      color: #666666;
      font-weight: 400;
      margin-left: 10px;
    }
    .list-state {
      padding: 2px 4px;
      display: inline-block;
      border-radius: 2px;
      margin-left: 10px;
    }
    .state-color1 {
      color: #f53f3f;
      background: #ffece8;
    }
    .state-color2 {
      color: #00b42a;
      background: #e8ffea;
    }
    .state-color3 {
      color: #ff7d00;
      background: #fff7e8;
    }
    .remarkBox {
      width: 100%;
      padding: 0 8px;
      .remarkList {
        margin: 8px 0 8px 0;
      }
    }
    .assessBox {
      width: 100%;
      padding: 0 8px;
      .assessList {
        margin: 8px 0 8px 0;
      }
    }
    .taskBox {
      width: 100%;
      padding: 0 8px;
      .regulationsDocBox {
        cursor: pointer;
        display: flex;
        .list-upLoad {
          margin-left: 30px;
          color: #3562db;
          font-size: 16px;
          font-weight: bold;
        }
      }
      .copywritinBox {
        display: flex;
        .list-copywriting {
          display: inline-block;
          flex: 1;
          // height: 100px;
          // overflow: scroll;
          margin-left: 10px;
          cursor: pointer;
        }
      }
      .taskList {
        padding: 2px 0 16px 8px;
      }
    }
    .attachmentsBox {
      width: 100%;
      padding: 0 8px;
      .attachmentsLists {
        cursor: pointer;
        margin: 8px 0 8px 0;
        display: flex;
        flex-wrap: wrap;
        .attachmentsLists-item {
          width: calc((100% - 32px) / 3);
          display: flex;
          margin: 5px 5px 5px 5px;
          .imgage {
            vertical-align: middle;
          }
          .content {
            margin-left: 8px;
          }
        }
      }
      .total {
        font-size: 14px;
        color: #7f848c;
      }
      .total {
        font-size: 14px;
        color: #333333;
        margin: 0 5px;
      }
    }
  }
}
.drawerBox {
  padding: 0 20px;
  word-break: break-all !important;
}
</style>
