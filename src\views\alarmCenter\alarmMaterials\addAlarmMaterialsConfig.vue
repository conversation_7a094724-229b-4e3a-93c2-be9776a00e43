<template>
  <PageContainer :footer="true">
    <div slot="content">
      <div class="addAlarmConfig-content-title" @click="goBack">
        <i class="el-icon-arrow-left"></i>
        <span style="margin-left: 10px">
          <span>{{ $route.query.type == 'add' ? '新增' : $route.query.type == 'edit' ? '编辑' : '查看' }}报警附近物资</span>
        </span>
      </div>
      <div class="content_box">
        <div class="contentBox-left">
          <el-form ref="formInline" :model="formInline" :inline="true" class="form-inline" :rules="addRules" label-width="150px">
            <span class="iconClass">*</span
            ><el-form-item label="通知范围">
              <div class="iconPlus">
                <el-tooltip popper-class="tps1" effect="dark" content="根据报警类型的报警产生时，在客户端地图上的报警点附近关联的显示内容" placement="top">
                  <span><i class="el-icon-question"></i></span>
                </el-tooltip>
                <div class="scope-from" v-if="$route.query.type == 'detail'">
                  <i class="el-icon-plus" style="margin-right: 5px"></i>
                  <span style="iconLable">添加报警类型</span>
                </div>
                <div v-else class="scope-from" @click.stop="selectAlarmType('0')">
                  <i class="el-icon-plus" style="margin-right: 5px"></i>
                  <span style="iconLable">添加报警类型</span>
                </div>
              </div>
            </el-form-item>
            <template>
              <div class="scopeFrom">
                <el-tag v-for="(tag, index) in alarmTypeTags" :key="index" closable :type="tag.type" @close="removeArarmTypeTag(tag, index)">
                  <span v-if="tag.parentName">{{ tag.parentName ? tag.parentName : '' }} / {{ tag.name }} </span> <span v-else>{{ tag.name }}</span>
                </el-tag>
              </div>
            </template>
            <br />
            <span class="iconItem">*</span
            ><el-form-item label="选择显示内容" style="margin-top: -10px">
              <div class="iconPlus">
                <div class="scope-from" v-if="$route.query.type == 'detail'">
                  <i class="el-icon-plus" style="margin-right: 5px"></i>
                  <span style="iconLable">添加显示内容</span>
                </div>
                <div v-else class="scope-from" @click.stop="selectAlarmConter('1')">
                  <i class="el-icon-plus" style="margin-right: 5px"></i>
                  <span style="iconLable">添加显示内容</span>
                </div>
              </div>
            </el-form-item>
            <template>
              <div class="scopeFrom">
                <el-tag v-for="(tag, index) in alarmContentTags" :key="tag.name" closable :type="tag.type" @close="removeArarmContenTag(tag, index)">
                  <span v-if="tag.parentName">{{ tag.parentName ? tag.parentName : '' }} / {{ tag.name }} </span>
                  <span v-else>{{ tag.name }}</span>
                </el-tag>
              </div>
            </template>
            <br />
            <el-form-item label="附近物资距离" prop="distance">
              <el-tooltip popper-class="tps1" effect="dark" content="显示距离报警点直径范围内的物资" placement="top">
                <span><i class="el-icon-question"></i></span>
              </el-tooltip>
              <div>
                <span class="nearbyLable">显示距离报警点</span
                ><el-input
                  placeholder="请输入报警物资距离"
                  min="1"
                  :disabled="$route.query.type == 'detail'"
                  type="number"
                  v-model.number="formInline.distance"
                  @input="handleInput"
                >
                  <template slot="append">m</template>
                </el-input>
                <span class="nearbyLable">附近的物资</span>
              </div>
            </el-form-item>
          </el-form>
        </div>
        <template v-if="shiftAlarmTypeShow">
          <shiftAlarmTypeDialog
            :shiftAlarmTypeShow="shiftAlarmTypeShow"
            @closeTypeDialog="closeTypeDialog"
            :typeString="typeString"
            :defaultChecked="defaultChecked"
            @submitAlarmTypeDialog="submitAlarmTypeDialog"
          />
        </template>
        <template v-if="shiftContentDialogShow">
          <shiftAlarmConterDialog
            :shiftContentDialogShow="shiftContentDialogShow"
            @closeContenDialog="closeContenDialog"
            :typeString="typeString"
            :defaultCheckedConter="defaultCheckedConter"
            :emergencyId="emergencyId"
            :lineId="lineId"
            :contenList="contenList"
            @submitAlarmConterDialog="submitAlarmConterDialog"
          />
        </template>
      </div>
    </div>

    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">取消</el-button>
      <el-button type="primary" :disabled="activeType == 'detail'" @click.stop="submitForm()">保存</el-button>
    </div>
  </PageContainer>
</template>
<script>
export default {
  name: 'addAlarmConfig',
  components: {
    shiftAlarmTypeDialog: () => import('./components/shiftAlarmTypeDialog.vue'),
    shiftAlarmConterDialog: () => import('./components/shiftAlarmConterDialog.vue')
  },
  async beforeRouteLeave(to, from, next) {
    if (!['alarmConfigIndex'].includes(to.name)) {
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      shiftAlarmTypeShow: false,
      shiftContentDialogShow: false,
      formInline: {
        distance: '15',
        alarmTypeList: [],
        showTypeList: []
      },
      activeType: '',
      addRules: {
        distance: [{ required: true, message: '请输入显示附近物资距离', trigger: 'blur' }]
      },
      alarmTypeTags: [], // 选中的报警类型
      alarmContentTags: [], // 选中的显示内容
      typeString: '',
      defaultChecked: [], // 选中的树id
      defaultCheckedConter: [],
      emergencyId: [],
      lineId: [],
      contenList: []
    }
  },
  mounted() {
    if (this.$route.query.baseId) {
      this.getAlarmDetail()
    }
  },
  methods: {
    getAlarmDetail() {
      this.loadingStatus = true
      this.$api.getAlarmMaterialsDetail({ id: this.$route.query.baseId }).then((res) => {
        this.loadingStatus = true
        if (res.code == '200') {
          this.formInline.distance = res.data.distance
          this.formInline.alarmTypeList = res.data.alarmTypeList
          this.alarmTypeTags = this.formInline.alarmTypeList
          this.formInline.showTypeList = res.data.showTypeList
          this.alarmContentTags = this.formInline.showTypeList
        }
      })
    },
    handleInput(value) {
      let val = value.replace(/[^\d]/g, '')
      // 确保大于0
      if (val && parseInt(val) <= 0) val = ''
      this.formInline.distance = val // 更新绑定的值
    },
    // 打开报警类型
    selectAlarmType(type) {
      this.shiftAlarmTypeShow = true
      this.typeString = '0'
      if (this.$route.query.type == 'edit') {
        this.defaultChecked = this.formInline.alarmTypeList.map((item) => item.id)
      } else if (type == '0' && this.$route.query.type == 'add') {
        this.$nextTick(() => {
          this.defaultChecked = this.alarmTypeTags.map((item) => item.code)
        })
      }
    },
    closeTypeDialog() {
      this.shiftAlarmTypeShow = false
    },
    // 点击tag删除
    removeArarmTypeTag(val, index) {
      console.log(val, 'val')
      this.alarmTypeTags.splice(index, 1)
    },
    // 打开添加内容
    selectAlarmConter(type) {
      this.shiftContentDialogShow = true
      this.typeString = '1'
      if (this.$route.query.type == 'edit') {
        this.contenList = this.formInline.showTypeList
        this.defaultCheckedConter = this.formInline.showTypeList.filter((item) => item.type == 0)
        this.defaultCheckedConter = this.defaultCheckedConter.map((e) => e.id)
        this.emergencyId = this.formInline.showTypeList.filter((item) => item.type == 1)
        this.emergencyId = this.emergencyId.map((e) => e.id)
        this.lineId = this.formInline.showTypeList.filter((item) => item.type == 2)
        this.lineId = this.lineId.map((e) => e.id)
      } else if (type == '1' && this.$route.query.type == 'add') {
        this.contenList = this.formInline.showTypeList.filter((el) => el.parentId !== '-1')
        this.defaultCheckedConter = this.alarmContentTags.filter((item) => item.type == 0)
        this.defaultCheckedConter = this.defaultCheckedConter.map((e) => e.id)
        this.emergencyId = this.alarmContentTags.filter((item) => item.type == 1)
        this.emergencyId = this.emergencyId.map((e) => e.id)
        this.lineId = this.alarmContentTags.filter((item) => item.type == 2)
        this.lineId = this.lineId.map((e) => e.id)
      }
    },
    closeContenDialog() {
      this.shiftContentDialogShow = false
    },
    // 点击显示内容tag删除
    removeArarmContenTag(val, index) {
      console.log(val)

      this.alarmContentTags.splice(index, 1)
    },
    // 选择报警类型
    submitAlarmTypeDialog(val) {
      let arry = []
      this.alarmTypeTags = val.filter((item) => item.parentCode !== null)
      val.forEach((el) => {
        const params = {
          id: el.code,
          name: el.name,
          parentId: el.parentCode,
          parentName: el.parentName
        }
        arry.push(params)
      })
      this.formInline.alarmTypeList = arry
    },
    // // 选中的内容
    submitAlarmConterDialog(data) {
      let arry1 = []
      this.alarmContentTags = data.filter((el) => el.parentId !== '-1')
      data.forEach((item) => {
        const params = {
          id: item.id,
          name: item.name,
          parentId: item.parentId,
          parentName: item.parentName,
          type: item.type
        }
        arry1.push(params)
      })
      this.formInline.showTypeList = arry1
      console.log(this.alarmContentTags, 'this.alarmContentTags-----------------')
    },
    // 提交表单
    submitForm() {
      this.$refs.formInline
        .validate()
        .catch(() => Promise.reject())
        .then(() => {
          this.loadingStatus = true
          const params = {
            alarmTypeList: this.formInline.alarmTypeList, // 报警系统类型
            showTypeList: this.formInline.showTypeList, // 设备类型
            distance: this.formInline.distance // 距离
          }

          if (params.alarmTypeList.length == 0) {
            this.$message.error('请选择报警类型')
            return
          } else if (params.showTypeList.length == 0) {
            this.$message.error('请选择显示内容')
            return
          }
          if (params.alarmTypeList.length != 0) {
            params.alarmTypeList = params.alarmTypeList.filter((item) => item.parentId !== null)
          }
          if (params.showTypeList.length != 0) {
            params.showTypeList = params.showTypeList.filter((el) => el.parentId !== '-1')
          }
          if (this.$route.query.baseId) {
            params.id = this.$route.query.baseId
            return this.$api.getAlarmMaterialsUpdate(params)
          } else {
            return this.$api.getAlarmMaterialsSave(params)
          }
        })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success(res.msg)
            this.$router.go('-1')
          } else {
            throw res.msg || res.message
          }
        })
        .catch((msg) => {
          // msg && this.$message.error(msg)
        })
        .finally(() => {
          this.loadingStatus = false
        })
    },
    // 返回
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>
<style lang="scss" scoped>
.container-content > div {
  background-color: #fff;
  height: 100%;
  position: relative;
  .addAlarmConfig-content-title {
    padding: 13px 24px;
    cursor: pointer;
    color: #333333;
  }
  .content_box {
    height: calc(100% - 60px);
    display: flex;
    .content-title {
      font-weight: bold;
      font-size: 16px;
      color: #333333;
    }
    .contentBox-left {
      height: 100%;
      width: 100%;
      overflow-y: auto;
      margin-top: 20px;
      padding: 0px 10px;
      position: relative;
    }
    .contentBox-right {
      flex: 1;
      > div {
        cursor: pointer;
        padding: 8px;
        font-size: 16px;
        color: #1d2129;
      }
      .isLight {
        color: #3562db;
      }
      .lineBox {
        display: inline-block;
        width: 3px;
        height: 24px;
        border-radius: 2px;
        margin-right: 10px;
        vertical-align: middle;
      }
      .lineStyle {
        background: #3562db;
      }
    }
  }
  .form-inline {
    .el-input,
    .el-select,
    .el-cascader {
      width: 340px;
    }
  }
  .scopeFrom {
    // height: 100px;
    // max-height: 240px;
    // overflow-y: auto;
    width: 85%;
    display: flex;
    flex-wrap: wrap;
    margin-left: 11%;
  }
  .scope-from {
    text-align: center;
    width: 138px;
    height: 32px;
    line-height: 32px;
    background: #e6effc;
    border-radius: 4px 4px 4px 4px;
    color: #3562db;
    margin: 5px 0px 0px 10px;
    cursor: pointer;
  }
  .fromBox {
    display: flex;
    flex-direction: column;
  }
  .iconPlus {
    display: flex;
    flex-direction: row;
  }
  .nearbyLable {
    font-size: 14px;
    color: #333333;
    margin: 0px 8px;
  }
}
::v-deep .el-tag {
  margin: 10px 5px;
  background-color: #f6f5fa;
  border-color: #f6f5fa;
  color: #333333;
}
::v-deep .el-tag .el-tag__close {
  color: #333333;
}
.iconClass {
  position: absolute;
  top: 1.5%;
  left: 4.5%;
  color: red;
}
.iconItem {
  position: relative;
  left: 3%;
  color: red;
}
</style>
