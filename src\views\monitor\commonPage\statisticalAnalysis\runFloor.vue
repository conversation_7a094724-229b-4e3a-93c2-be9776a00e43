<template>
  <div class="monitor-content-right">
    <div class="right-heade">
      <div class="search-from">
        <el-select v-model="search.buildCode" placeholder="请选择楼栋" @change="buildChange">
          <el-option v-for="(item, index) in buildList" :key="index" :value="item.value" :label="item.name"></el-option>
        </el-select>
        <el-select v-model="search.elecCode" placeholder="请选择电梯" multiple collapse-tags style="margin-left: 10px">
          <el-option v-for="(item, index) in elecList" :key="index" :value="item.value" :label="item.name"></el-option>
        </el-select>
        <el-date-picker v-model="search.dateTime" :picker-options="{ firstDayOfWeek: 1 }" type="daterange" style="margin: 10px" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
        </el-date-picker>
        <span>
          <el-button size="small" type="primary" plain @click="reset">重置</el-button>
          <el-button size="small" type="primary" @click="searchBtn">查询</el-button>
          <el-button size="small" type="primary" @click="exportData">导出</el-button>
        </span>
      </div>
    </div>
    <div id="contentPage" v-loading="loading" class="right-content">
      <div class="content-top">
        <ContentCard title="总停靠楼层次数">
          <div slot="content" class="left">
            <div class="staticsisticItem">
              <img src="@/assets/images/parkingLot/count-two.png" alt="" />
              <div class="staticsisticItemData">
                <div>总停靠楼层次数</div>
                <div class="staticsisticItemDataNum">
                  {{ workStopCount }}
                  <span>次</span>
                </div>
              </div>
            </div>
            <div>
              <span style="color: #96989a">统计日期：</span>
              {{ search.dateTime[0] }} - {{ search.dateTime[1] }}
            </div>
          </div>
        </ContentCard>
        <ContentCard title="电梯停靠楼层总次数占比">
          <div id="workStopChart" slot="content"></div>
        </ContentCard>
        <ContentCard title="停靠楼层次数占比">
          <div id="workStopTotalChart" slot="content"></div>
        </ContentCard>
      </div>
      <div class="content-bottom">
        <div class="table_header">
          <div class="headerItem" :style="`width: calc(100% / ${tableCloumns.length + 1})`">楼层</div>
          <div v-for="(item, index) in tableCloumns" :key="index" class="headerItem" :style="`width: calc(100% / ${tableCloumns.length + 1})`">{{ item }}</div>
        </div>
        <div v-for="(item, index) in tableData" :key="index" class="table_tr">
          <div class="bodyItem" :style="`width: calc(100% / ${tableCloumns.length + 1})`">{{ item.floor }}</div>
          <div v-for="td in item.data" :key="td.name" :style="`width: calc(100% / ${tableCloumns.length + 1})`" class="bodyItem">{{ td.value }}</div>
        </div>
        <div class="table_tr">
          <div class="bodyItem" :style="`width: calc(100% / ${tableCloumns.length + 1})`">总次数</div>
          <div v-for="(item, index) in tableTotal" :key="index" class="bodyItem" :style="`width: calc(100% / ${tableCloumns.length + 1})`">{{ item }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import dayjs from 'dayjs'
import * as echarts from 'echarts'
import { downloadPDF } from '@/util/htmlToPdf.js'
export default {
  props: {
    projectCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      elecList: [],
      buildList: [],
      loading: false,
      search: {
        buildCode: '',
        elecCode: [],
        dateTime: [dayjs().startOf('month').format('YYYY-MM-DD'), dayjs().endOf('month').format('YYYY-MM-DD')]
      },
      workStopCount: 0,
      floorList: [],
      tableData: [],
      tableCloumns: [],
      tableTotal: [],
      topFloorList: []
    }
  },
  mounted() {
    this.getBuildList()
    this.getWorkStopFloor()
  },
  methods: {
    // 获取楼层
    getBuildList() {
      this.$api.buildList().then((res) => {
        if (res.code == 200) {
          this.buildList = res.data
          this.search.buildCode = this.buildList[0].value
          this.getEmptyBuildFn(this.search.buildCode)
          this.getElecList(this.search.buildCode)
        }
      })
    },
    // 楼层更改
    buildChange(val) {
      this.getEmptyBuildFn(val)
      this.getElecList(val)
    },
    // 获取电梯列表
    getElecList(val) {
      let params = {
        projectCode: this.projectCode,
        regionCode: val
      }
      this.$api.elecList(params).then((res) => {
        if (res.code == 200) {
          this.elecList = res.data
          if (res.data.length) {
            let arr = []
            res.data.forEach((el) => {
              arr.push(el.value)
            })
            this.search.elecCode = arr
          }
        }
      })
    },
    searchBtn() {
      // 第一个饼图
      this.getWorkStopFloor()
      // 第二个饼图
      this.getStopFloorData()
      // 列表
      this.getRunFloorList()
    },
    reset() {
      this.search.dateTime = [dayjs().startOf('month').format('YYYY-MM-DD'), dayjs().endOf('month').format('YYYY-MM-DD')]
      this.getBuildList()
      setTimeout(() => {
        this.getWorkStopFloor()
      }, 200)
    },
    // 楼层字典
    getEmptyBuildFn(val) {
      this.$api.getEmptyBuild({ buildId: val }).then((res) => {
        if (res.code == 200) {
          this.floorList = res.data.selectedFloorIdList
          setTimeout(() => {
            this.getWorkStopFloor()
          }, 200)
          this.getStopFloorData()
          this.getRunFloorList()
        }
      })
    },
    // 获取电梯停靠楼层次数及占比
    getWorkStopFloor() {
      let params = {
        regionCode: this.search.buildCode,
        surveyCode: this.search.elecCode.join(','),
        startTime: this.search.dateTime[0],
        endTime: this.search.dateTime[1]
      }
      this.$api.workStopFloor(params).then((res) => {
        if (res.code == 200) {
          this.workStopCount = res.data.sum
          this.setPieChart(res.data.list, 'workStopChart')
        }
      })
    },
    // 停靠楼层占比
    getStopFloorData() {
      let params = {
        regionCode: this.search.buildCode,
        surveyCode: this.search.elecCode.join(','),
        floor: this.floorList,
        timeType: 2,
        order: 1,
        orderByWitch: 2,
        startTime: this.search.dateTime[0],
        endTime: this.search.dateTime[1]
      }
      this.$api.stopFloorChart(params).then((res) => {
        if (res.code == 200) {
          this.setPieChart(res.data, 'workStopTotalChart')
        }
      })
    },
    // 设置饼图
    setPieChart(chartData, domId) {
      console.log(domId)
      let options
      let data = []
      const getchart = echarts.init(document.getElementById(domId))
      if (domId == 'workStopChart') {
        chartData.map((item) => {
          data.push({ name: item.surveyName, value: item.value, ratio: item.ratio })
        })
      } else {
        chartData.map((item) => {
          data.push({ name: item.floor + '层', value: item.count, ratio: item.ratio })
        })
      }
      if (chartData.length) {
        options = {
          tooltip: {
            trigger: 'item'
          },
          legend: {
            orient: 'vertical',
            left: '60%',
            top: 'center',
            icon: 'circle',
            align: 'left',
            pageIconColor: '#5188fc', // 激活的分页按钮颜色
            pageIconInactiveColor: '#c2c6ce', // 没激活的分页按钮颜色
            type: 'scroll',
            tooltip: {
              show: true
            }
          },
          color: ['#37BBF0', '#FF796D', '#F787D3', '#21CAB5', '#FCD442', '#B27BEF', '#5E7BE1', '#F5666D'],
          series: [
            {
              name: '',
              type: 'pie',
              radius: '70%',
              center: ['30%', '50%'],
              data: data,
              label: {
                show: false,
                position: 'center'
              },
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        }
      } else {
        options = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      getchart.clear()
      getchart.setOption(options)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 列表数据
    getRunFloorList() {
      let params = {
        floors: this.floorList.join(','),
        surveyCodes: this.search.elecCode.join(','),
        startTime: this.search.dateTime[0] + ' 00:00:00',
        endTime: this.search.dateTime[1] + ' 23:59:59'
      }
      this.$api.runFloorList(params).then((res) => {
        if (res.code == 200) {
          this.tableCloumns = res.data.header
          this.tableData = res.data.floorDataList
          this.tableTotal = res.data.total
        }
      })
    },
    exportData() {
      //  1报警统计分析
      this.$api
        .runFloorExportList(
          {
            floors: this.floorList.join(','),
            surveyCodes: this.search.elecCode.join(','),
            startTime: this.search.dateTime[0] + ' 00:00:00',
            endTime: this.search.dateTime[1] + ' 23:59:59'
          },
          this.requestHttp
        )
        .then((res) => {
          this.$tools.downloadFile(res, '运行楼层分析')
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.monitor-content-right {
  height: 100%;
  flex: 1;
  flex-direction: column;
  overflow: auto;
  margin-left: 16px;
  display: flex;
  .right-heade {
    border-radius: 4px;
    background: #fff;
    padding: 10px !important;
    .search-from {
      .btnGroup {
        .el-button {
          width: 72px;
        }
      }
    }
    .batch-control {
      & > button {
        margin-top: 12px;
        margin-right: 10px;
        margin-left: 0;
      }
    }
  }
  .right-content {
    padding: 16px;
    flex: 1;
    overflow: auto;
    margin-top: 16px;
    background: #fff;
    .content-top {
      width: 100%;
      height: 263px;
      display: flex;
      justify-content: space-between;
      ::v-deep .box-card {
        margin: 0 8px;
        height: 100%;
        width: 33%;
        background: #faf9fc;
        &:first-child {
          margin-left: 0;
        }
        &:last-child {
          margin-right: 0;
        }
        #workStopChart,
        #workStopTotalChart {
          width: 100%;
          height: 100%;
          padding: 10px;
          overflow: hidden;
        }
        .left {
          height: 100%;
          .staticsisticItem {
            width: 100%;
            height: calc(100% - 40px);
            background: #faf9fc;
            border-radius: 4px 4px 4px 4px;
            padding: 16px;
            display: flex;
            align-items: center;
            &:first-child {
              margin-bottom: 16px;
            }
            img {
              margin-right: 22px;
            }
            .staticsisticItemData {
              color: #96989a;
              .staticsisticItemDataNum {
                color: #333;
                font-size: 24px;
                & > span {
                  font-size: 14px;
                  color: #96989a;
                }
              }
            }
          }
        }
      }
    }
    .content-bottom {
      width: 100%;
      height: calc(100% - 263px - 16px);
      margin-top: 16px;
      overflow: auto;
      .table_header {
        width: 100%;
        display: flex;
        justify-content: space-between;
        .headerItem {
          border-right: 1px solid #f2f3f5;
          border-bottom: 1px solid #f2f3f5;
          background: #f2f3f5;
          color: #96989a;
          padding: 12px 14px;
          &:last-child {
            border-right: none;
          }
        }
      }
      .table_tr {
        width: 100%;
        display: flex;
        justify-content: space-between;
        border-bottom: 1px solid #f2f3f5;
        &:nth-child(odd) {
          background-color: #f7f8fa;
        }
        .bodyItem {
          border-right: 1px solid #f2f3f5;
          color: #96989a;
          padding: 12px 14px;
          &:first-child {
            border-left: 1px solid #f2f3f5;
          }
        }
      }
    }
  }
}
</style>
