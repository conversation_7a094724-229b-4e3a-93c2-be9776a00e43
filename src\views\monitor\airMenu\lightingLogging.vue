<template>
  <LightingLogging :projectCode="projectCode" />
</template>

<script>
import LightingLogging from '@/views/monitor/lightingMonitoring/airAndLightingCom/lightingLogging.vue'
import { monitorTypeList } from '@/util/dict.js'
export default {
  components: {
    LightingLogging
  },
  data() {
    return {
      projectCode: monitorTypeList.find((item) => item.projectName == '空调监测').projectCode
    }
  },
  mounted() {
    
  },

  methods: {
    
  }
}
</script>
<style lang="scss" scoped>

</style>
