<template>
  <div class="main_content" :class="{ close: isCloseChild }">
    <header>
      <span>{{ cardData.surveyEntityName }}</span>
      <div class="tools">
        <i class="el-icon-close" style="font-size: 18px; vertical-align: middle; cursor: pointer" @click="close"></i>
      </div>
    </header>
    <div class="content">
      <div v-for="(item, index) in cardData.parameterList" :key="index" class="text item card_options">
        <div class="info-list">
          <span class="info_list_icon iconfont" :class="item.parameterIcon ? item.parameterIcon : ''" :style="{ color: item.parameterColor || '#fd9a5e' }"></span>
          <div style="margin: auto 10px; max-width: 50%; flex: 1">
            <div class="parameterName" :title="item.parameterName">
              {{ item.parameterName || '-' }}
            </div>
            <div class="parameterUnit" :title="item.parameterValue + item.parameterUnit">
              {{ (item.parameterValue ? (item.parameterValue === 'null' ? '-' : item.parameterValue) : '') + (item.parameterUnit ? o.parameterUnit : '') || '-' }}
            </div>
          </div>
          <div style="margin: auto 0" :class="item.parameterException == '0' ? '' : 'font-red'">
            {{ item.parameterException == '2' ? '离线' : item.parameterException == '1' ? '异常' : '' }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    closeParameterState: Boolean,
    cardData: {
      type: Object,
      default: () => {}
    }
  },
  data: function () {
    return {
      isCloseChild: this.closeParameterState
    }
  },
  watch: {
    closeParameterState: {
      handler(val) {
        // debugger
        this.isCloseChild = val
        this.$emit('isCloseParameterState', this.isCloseChild)
      },
      deep: true
    }
  },
  mounted() {},
  methods: {
    close() {
      this.isCloseChild = !this.isCloseChild
      this.$emit('isCloseParameterState', this.isCloseChild)
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  box-sizing: border-box;
  padding-bottom: 16px;
  overflow: auto;
  height: calc(100% - 56px);
  background: #f2f2f2;

  .card_options {
    display: inline-block;
    width: calc(50% - 8px);
    background: #fff;
    margin-top: 16px;
    border-radius: 8px;
    &:nth-of-type(odd) {
      margin-right: 16px;
    }
  }

  .info-list {
    display: flex;
    // justify-content: space-between;
    justify-content: flex-start;
    padding: 0 20px;
    padding-right: 0;
    max-height: 66px;
    min-height: 66px;
    overflow: hidden;
    .info_list_icon {
      width: 48px;
      height: 48px;
      display: inline-block;
      margin: auto 0;
      color: #fd9a5e;
      font-size: 24px;
      text-align: center;
    }

    .parameterUnit {
      color: #353535;
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 2px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .parameterName {
      color: #929cb3;
      font-size: 14px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .font-red {
      color: #fa4764;
    }
  }
}

.main_content {
  position: absolute;
  top: 0;
  right: 0;
  transition: all 0.5s;
  z-index: 999;
  text-align: left;
  width: 540px;
  height: 100%;
  box-shadow: 0 6px 18px 0 rgb(18 31 62 / 20%);
  border-radius: 4px;
  float: right;
  header {
    height: 62px;
    border-radius: 0 10px 0 0;
    background: #fff;
    line-height: 62px;
    padding: 0 20px;
    font-size: 20px;
    font-family: NotoSansHans-Medium;
    font-weight: 500;
    color: rgb(96 98 102 / 100%);
    box-sizing: border-box;
    border-bottom: 1px solid #d8dee7;
  }

  .tools {
    float: right;
    box-sizing: border-box;
  }
}

.close {
  margin-right: -680px;
}
</style>
