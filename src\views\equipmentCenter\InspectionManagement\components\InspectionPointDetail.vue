<!-- eslint-disable vue/html-indent -->
<template>
  <div class="contentMain">
    <div class="topFilter">
      <div class="backBar">
        <span style="cursor: pointer" @click="$router.go(-1)">
          <i class="el-icon-arrow-left"></i>
          {{ systemType == '2' ? '保养点详情' : '巡检点详情' }}
        </span>
      </div>
    </div>
    <div v-loading="pageLoading" class="taskDetailContent">
      <div class="taskDetail">
        <!-- 基本信息 -->
        <div class="baseInfo">
          <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
            <span class="green_line"></span>
            基本信息
          </div>
          <div class="pointNamr">
            <el-tooltip class="item" effect="dark" :disabled="!particulars.inspectionPointName" :content="particulars.inspectionPointName" placement="top-start">
              <div class="pointItem">
                <span>{{ systemType == '2' ? '保养点名称:' : systemType == '5' ? '年检点名称' : '巡检点名称:' }}</span>
                <span>{{ row.inspectionPointName }}</span>
              </div>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" :content="showAreaContent(particulars)" placement="top-start">
              <div class="pointItem">
                <span>所属区域:</span>
                <span>{{ particulars.inspectionPointType == '1' ? particulars.simName : particulars.inspectionPointType == '2' ? particulars.regionName : '' }}</span>
              </div>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" :disabled="!particulars.assetsRemarks" :content="particulars.assetsRemarks" placement="top-start">
              <div class="pointItem" style="max-width: 400px">
                <span>备注说明:</span>
                <span>{{ particulars.assetsRemarks }}</span>
              </div>
            </el-tooltip>
          </div>
        </div>
        <!-- 巡检内容 -->
        <div class="inspectionInfo">
          <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
            <span class="green_line"></span>
            {{ systemType == '2' ? '保养内容' : systemType == '5' ? '年检内容' : '巡检内容' }}
          </div>
          <el-table :data="projectList" :style="{ width: isTask ? '80%' : '60%' }">
            <el-table-column label="序号" type="index" width="80"> </el-table-column>
            <el-table-column
              :prop="projectType == '1' ? 'detailName' : 'detailName'"
              :label="
                systemType == '2'
                  ? projectType == '1'
                    ? '保养项目'
                    : '保养内容'
                  : systemType == '5'
                  ? projectType == '1'
                    ? '年检项目'
                    : '年检内容'
                  : projectType == '1'
                  ? '巡检项目'
                  : '巡检内容'
              "
              show-overflow-tooltip
            >
            </el-table-column>
            <el-table-column
              :prop="projectType == '1' ? 'content' : 'standardRequirements'"
              :label="
                systemType == '2'
                  ? projectType == '1'
                    ? '保养要点'
                    : '保养要求'
                  : systemType == '5'
                  ? projectType == '1'
                    ? '年检要点'
                    : '年检要求'
                  : projectType == '1'
                  ? '巡检要点'
                  : '标准要求'
              "
              show-overflow-tooltip
            >
            </el-table-column>
            <el-table-column
              :label="
                systemType == '2'
                  ? projectType == '1'
                    ? '保养内容'
                    : '保养依据'
                  : systemType == '5'
                  ? projectType == '1'
                    ? '年检内容'
                    : '年检依据'
                  : projectType == '1'
                  ? '巡检内容'
                  : '巡检依据'
              "
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <span>{{ formate(scope.row) }}</span>
              </template>
            </el-table-column>
            <el-table-column
              v-if="isTask && projectType == '1'"
              show-overflow-tooltip
              :label="systemType == '2' ? '保养结果' : systemType == '5' ? '年检结果' : '巡检结果'"
              prop="contentStandard"
            >
              <template slot-scope="scope">
                <div v-if="scope.row.isNum == '4'" style="white-space: normal">
                  <span v-for="item in scope.row.contentStandard" :key="item.fileKey" class="link" @click="handleDownload(item)">{{ item.sourceName }}</span>
                </div>
                <span v-else>{{ scope.row.contentStandard }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <!-- 巡检执行 -->
        <div v-if="isTask" class="executeInfo">
          <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
            <span class="green_line"></span>
            {{ systemType == '2' ? '保养执行' : systemType == '5' ? '年检执行:' : '巡检执行' }}
          </div>
          <el-form ref="form" label-width="80px">
            <el-row :gutter="60">
              <el-col :span="8">
                <el-form-item :label="systemType == '2' ? '保养情况' : systemType == '5' ? '年检情况:' : '巡检情况'">
                  <span>{{ systemType == '2' ? (excute.carryOutFlag == '1' ? '已保养' : '未保养') : excute.carryOutFlag == '1' ? '已巡' : '未巡' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="执行人员:">
                  <span>{{ excute.implementPersonName || '-' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="systemType == '2' ? '实际保养时间:' : systemType == '5' ? '实际年检时间:' : '实际巡检时间:'" class="actualTime">
                  <span>{{ excute.excuteTime || '-' }}</span>
                </el-form-item>
              </el-col>
              <el-col v-if="systemType !== '6'" :span="8">
                <el-form-item label="定位状态:">
                  <span>{{ excute.spyScan }}</span>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <!-- 巡检结果 -->
        <div v-if="isTask" class="result">
          <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
            <span class="green_line"></span>
            {{ systemType == '2' ? '保养结果' : systemType == '5' ? '年检结果:' : '巡检结果' }}
          </div>
          <el-form ref="form" label-position="left" label-width="100px">
            <el-row :gutter="60">
              <el-col :span="24">
                <el-form-item :label="systemType == '2' ? '保养结果:' : systemType == '5' ? '年检结果:' : '巡检结果:'">
                  <span>{{ result.state == '2' ? '合格' : result.state == '3' ? '不合格' : result.state == '4' ? '异常报修' : systemType == '2' ? '未保养' : '未巡' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item :label="systemType == '2' ? '保养情况说明:' : systemType == '5' ? '年检情况说明:' : '巡检情况说明:'">
                  <span>{{ result.desc }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="图片:">
                  <div v-if="result.attachmentUrlList.length > 0" class="resultImgBox">
                    <div v-for="(item, index) in result.attachmentUrlList" :key="index">
                      <el-image style="max-height: 120px; max-width: 100px; margin-right: 5px" :src="item" fit="scale-down" :preview-src-list="[item]"></el-image>
                    </div>
                  </div>
                  <div v-else>暂无</div>
                </el-form-item>
              </el-col>
              <el-col v-if="systemType !== '6'" :span="24">
                <el-form-item label="语音:">
                  <span v-if="!result.callerTapeUrl">暂无</span>
                  <div v-else>
                    <audio ref="player" style="height: 40px" :src="result.callerTapeUrl" preload="true" controls="controls"></audio>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
    </div>
    <div class="bottomBar">
      <div class="bottomWrap">
        <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db" @click="$router.go(-1)">取消</el-button>
      </div>
    </div>
  </div>
</template>
<script>
import axios from 'axios'
export default {
  name: 'InspectionPointDetail',
  components: {},
  async beforeRouteLeave(to, from, next) {
    if (!['planProgressDetail', 'taskDetail'].includes(to.name)) {
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      systemType: '',
      isTask: false,
      row: {},
      projectList: [],
      projectType: '', // 1:专业 0:日常
      excute: {},
      result: {
        attachmentUrlList: []
      },
      pageLoading: false,
      locationFlag: '',
      particulars: []
    }
  },
  computed: {},
  created() {
    if (!this.$store.state.keepAlive.list.includes('planManagement')) {
      this.initEvent()
    }
  },
  activated() {
    this.initEvent()
  },
  methods: {
    handleDownload(item) {
      const formData = new FormData()
      const params = { fileKey: item.fileKey, sourceName: item.sourceName }
      Object.keys(params).forEach((key) => {
        formData.append(key, params[key])
      })
      axios({
        method: 'post',
        url: __PATH.VUE_ICIS_API + 'file/download',
        data: formData,
        responseType: 'blob',
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      })
        .then((res) => {
          const name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
          const blob = new Blob([res.data])
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
        })
        .catch(() => {
          this.$message.error('下载失败')
        })
    },
    showAreaContent(val) {
      return val.inspectionPointType == '1' ? val.simName : val.inspectionPointType == '2' ? val.regionName : ''
    },
    initEvent() {
      Object.assign(this.$data, this.$options.data())
      if (this.$route.path.indexOf('/InspectionManagement') != -1) {
        this.systemType = '1'
      } else if (this.$route.path.indexOf('/MaintenanceManagement') != -1) {
        this.systemType = '2'
      } else if (this.$route.path.indexOf('/comInspectionManagement') != -1) {
        this.systemType = '3'
      } else if (this.$route.path.indexOf('/hazardousChemicalGoods_inspection') != -1) {
        this.systemType = '4'
      } else if (this.$route.path.indexOf('/annualManagement') != -1) {
        this.systemType = '5'
      } else if (this.$route.path.indexOf('/vp_taskManagement') != -1) {
        this.systemType = '6'
      }
      this.row = this.$route.query.row
      this.isTask = this.$route.query.isTask
      this.locationFlag = this.$route.query.locationFlag
      this.getInspectionPointDetail()
    },
    getInspectionPointDetail() {
      this.pageLoading = true
      this.$api.getInspectionPointDetail({ id: this.row.pointId }).then((res) => {
        if (res.code == '200') {
          if (res.data.project.projectdetailsReleaseList) {
            this.projectType = res.data.project.equipmentTypeId
            res.data.project.projectdetailsReleaseList.forEach((item) => {
              item.contentStandard = item.isNum === '4' ? (item.contentStandard ? JSON.parse(item.contentStandard) : []) : item.contentStandard
            })
            this.projectList = res.data.project.projectdetailsReleaseList
          }
          this.excute = res.data.excute
          res.data.result.attachmentUrlList = res.data.result.attachmentUrlList.length > 0 ? res.data.result.attachmentUrlList.map((i) => this.$tools.imgUrlTranslation(i)) : []
          this.result = res.data.result
          this.particulars = JSON.parse(res.data.taskPoint.particulars)
        }
        this.pageLoading = false
      })
    },
    formate(row) {
      if (this.projectType == '1') {
        // 数值
        if (row.isNum == '0') {
          return row.rangeStart + '-' + row.rangeEnd + (row.einheitName || '')
        } else if (['1', '2', '4'].includes(row.isNum)) {
          // 无 || 文本 || 文件上传
          return '无'
        } else if (row.isNum == '3') {
          // 选项
          const option = JSON.parse(row.termJson)
          const contTexts = option.map((i) => i.contText)
          return contTexts.join('、')
        }
      } else {
        return row.inspectionBasis
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.contentMain {
  height: 100%;
  .topFilter {
    margin: 15px 15px 0;
    padding: 15px;
    width: calc(100% - 30px);
    height: 70px;
    background-color: #fff;
    .backBar {
      color: #121f3e;
      height: 30px;
      border-bottom: 1px solid #dcdfe6;
    }
  }
  .taskDetailContent {
    margin: 0 15px 15px;
    height: calc(100% - 152px);
    .taskDetail {
      padding: 0 15px 15px;
      overflow-y: scroll;
      overflow-x: hidden;
      width: 100%;
      height: 100%;
      background-color: #fff;
      .baseInfo {
        border-bottom: 3px solid #f6f5fa;
        .pointNamr {
          padding: 16px;
          font-size: 15px;
          display: flex;
          .pointItem {
            width: 33%;
            margin-right: 60px;
            display: flex;
            align-items: center;
            span:first-child {
              color: #414653;
              padding-right: 12px;
            }
            span:last-child {
              width: calc(100% - 100px);
              display: inline-block;
              color: #121f3e;
              white-space: nowrap;
              /* 防止换行 */
              overflow: hidden;
              /* 隐藏超出部分 */
              text-overflow: ellipsis;
            }
          }
        }
      }
    }
  }
  .executeInfo {
    .actualTime {
      :deep(.el-form-item__label) {
        width: 106px !important;
      }
    }
  }
  .result {
    .resultImgBox {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      img {
        width: 80px;
        height: 80px;
        margin-left: 10px;
      }
    }
  }
  .bottomBar {
    height: 52px;
    width: 100%;
    background-color: #fff;
    display: flex;
    justify-content: right;
    align-items: center;
    .bottomWrap {
      padding: 0 16px;
    }
  }
  .link {
    color: #1890ff;
    cursor: pointer;
    margin-right: 20px;
  }
}
</style>
