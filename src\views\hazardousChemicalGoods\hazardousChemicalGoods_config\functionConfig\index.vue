<template>
  <PageContainer>
    <div slot="content" class="space-content" style="height: 100%">
      <div class="space-content-left">
        <div class="left_content">
          <el-tree
            ref="tree"
            v-loading="treeLoading"
            :check-strictly="true"
            :data="treeData"
            node-key="id"
            :highlight-current="true"
            :default-expanded-keys="idArr"
            :expand-on-click-node="false"
            @node-click="nodeClick"
          >
            <template #default="{ node, data }">
              <el-tooltip effect="dark" :disabled="showTooltip" :content="node.label" placement="top">
                <div class="nodeLabel" @mouseover="onMouseOver(data.id)">
                  <span :ref="`nodeLabel${data.id}`">{{ node.label }}</span>
                </div>
              </el-tooltip>
            </template>
          </el-tree>
        </div>
      </div>
      <div class="space-content-right">
        <div style="height: 100%; display: flex; flex-direction: column">
          <div class="search-from">
            <template v-if="selectType != 5">
              <el-input
                v-model.trim="searchFrom.keywords"
                style="width: 300px"
                suffix-icon="el-icon-search"
                clearable
                maxlength="25"
                placeholder="请输入"
                onkeyup="if(value.length>25)value=value.slice(0,25)"
              ></el-input>
              <el-select v-model="searchFrom.status" placeholder="全部状态" clearable>
                <el-option v-for="(item, index) in statusArr" :key="index" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </template>
            <template v-else>
              <el-input
                v-model.trim="searchFrom.keywords"
                style="width: 300px"
                suffix-icon="el-icon-search"
                clearable
                maxlength="25"
                placeholder="名称"
                onkeyup="if(value.length>25)value=value.slice(0,25)"
              ></el-input>
            </template>
            <el-button type="primary" plain style="margin-right: 10px" @click="resetForm">重置</el-button>
            <el-button type="primary" @click="searchForm">查询</el-button>
          </div>
          <div class="search-control">
            <el-button type="primary" icon="el-icon-plus" @click="tableControl('add')">新增</el-button>
          </div>
          <div class="contentTable-main table-content">
            <el-table
              ref="table"
              :key="tableKey"
              v-loading="tableLoading"
              border
              style="width: 100%; height: 100%"
              :data="tableData"
              :height="tableHeight"
              default-expand-all
              stripe
              row-key="id"
              :tree-props="{ children: 'children' }"
            >
              <el-table-column label="序号" type="index" width="100"> </el-table-column>
              <template v-if="selectType == 1">
                <el-table-column label="分类名称" prop="name" show-overflow-tooltip></el-table-column>
                <el-table-column label="分类编码" prop="code" show-overflow-tooltip> </el-table-column>
                <el-table-column label="说明" prop="dictionaryDetailsRemake" show-overflow-tooltip> </el-table-column>
                <el-table-column label="启用状态" show-overflow-tooltip>
                  <span slot-scope="scope">{{ scope.row.dictionaryDetailsStatus == '1' ? '启用' : '禁用' }}</span>
                </el-table-column>
              </template>
              <template v-if="selectType == 2">
                <el-table-column label="危化品名称" prop="materialName" width="150" show-overflow-tooltip></el-table-column>
                <el-table-column label="危化品编码" prop="materialCode" show-overflow-tooltip> </el-table-column>
                <el-table-column label="基本单位" prop="basicUnitName" show-overflow-tooltip> </el-table-column>
                <el-table-column label="规格型号" prop="model" show-overflow-tooltip> </el-table-column>
                <el-table-column label="供应商" prop="supplierName" show-overflow-tooltip> </el-table-column>
                <el-table-column label="生产许可证" prop="manufacturingLicense" show-overflow-tooltip> </el-table-column>
                <el-table-column label="生产厂家" prop="manufacturerName" show-overflow-tooltip> </el-table-column>
                <el-table-column label="物资图片" prop="remark" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <el-button type="text" @click="viewImg(scope.row.materialPhoto)">查看图片</el-button>
                  </template>
                </el-table-column>
                <el-table-column label="启用状态" prop="status" show-overflow-tooltip> </el-table-column>
              </template>
              <template v-if="selectType == 3">
                <el-table-column label="名称" prop="name" show-overflow-tooltip></el-table-column>
                <el-table-column label="编码" prop="typeCode" show-overflow-tooltip> </el-table-column>
                <el-table-column label="参数类型" prop="type" show-overflow-tooltip>
                  <span slot-scope="scope">{{ paramsType.find((i) => i.id == scope.row.parentId) ? paramsType.find((i) => i.id == scope.row.parentId).name : '' }}</span>
                </el-table-column>
                <el-table-column label="说明" prop="explain" show-overflow-tooltip> </el-table-column>
                <el-table-column label="启用状态" prop="status" show-overflow-tooltip> </el-table-column>
              </template>
              <template v-if="selectType == 4">
                <el-table-column label="名称" prop="name" width="150" show-overflow-tooltip></el-table-column>
                <el-table-column label="编码" prop="typeCode" show-overflow-tooltip> </el-table-column>
                <el-table-column label="说明" prop="explain" show-overflow-tooltip> </el-table-column>
                <el-table-column label="启用状态" prop="status" show-overflow-tooltip> </el-table-column>
              </template>
              <template v-if="selectType == 5">
                <el-table-column label="终端" prop="endpoint" width="150" show-overflow-tooltip></el-table-column>
                <el-table-column label="说明" prop="description" show-overflow-tooltip> </el-table-column>
                <el-table-column label="监测设备" prop="monitoringDevice" show-overflow-tooltip> </el-table-column>
                <el-table-column label="启用状态" prop="status" show-overflow-tooltip> </el-table-column>
              </template>
              <el-table-column label="操作" width="140" fixed="right">
                <template slot-scope="scope">
                  <el-button type="text" @click="tableControl('view', scope.row)">查看</el-button>
                  <el-button type="text" @click="tableControl('edit', scope.row)">编辑</el-button>
                  <el-button type="text" style="color: #fa403c" @click="tableControl('del', scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div v-if="selectType != 1" class="contentTable-footer">
            <el-pagination
              layout="total, sizes, prev, pager, next, jumper"
              :current-page="pagination.currentPage"
              :page-size="pagination.pageSize"
              :page-sizes="[15, 20, 30, 40]"
              :total="pagination.total"
              :hide-on-single-page="isShowPage"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            >
            </el-pagination>
          </div>
        </div>
      </div>
      <el-dialog :visible.sync="dialogVisible" top="5vh" style="z-index: 99999">
        <img width="100%" :src="dialogImageUrl" alt />
      </el-dialog>
    </div>
  </PageContainer>
</template>
<script>
import { transData } from '@/util'
import tableListMixin from '@/mixins/tableListMixin.js'
import axios from 'axios'
export default {
  name: 'functionConfig',
  components: {},
  mixins: [tableListMixin],
  data() {
    return {
      showTooltip: false,
      treeLoading: false,
      selectType: 1,
      treeData: [
        { id: 1, label: '危化品分类', placeholder: '危化品分类' },
        { id: 2, label: '危化品字典库', placeholder: '危化品名称' },
        { id: 3, label: '类型设置', placeholder: '分类名称' },
        { id: 4, label: '风险等级', placeholder: '等级名称' },
        { id: 5, label: '危化品监测配置', placeholder: '名称' }
      ],
      idArr: [1],
      searchFrom: {
        keywords: '',
        status: ''
      },
      statusArr: [
        {
          id: '0',
          name: '启用'
        },
        {
          id: '1',
          name: '禁用'
        }
      ],
      tableLoading: false,
      tableData: [],
      pagination: {
        currentPage: 1,
        pageSize: 15,
        total: 0,
        pageSizeOptions: [15, 30, 45, 60]
      },
      isShowPage: false,
      paramsType: [],
      dialogImageUrl: '',
      dialogVisible: false,
      tableKey: Math.random()
    }
  },
  computed: {},
  mounted() {
    if (this.$route.query.type) {
      this.selectType = this.$route.query.type
    } else {
      this.selectType = this.treeData[0].id
    }
    if (this.selectType == 3) {
      this.getParamsTypeData()
    }
    this.$refs.tree.setCurrentKey(this.selectType)
    this.getTableDataList()
  },
  methods: {
    tableControl(key, data) {
      console.log(data)
      if (key == 'del') {
        // 删除
        this.$confirm('确认删除当前数据吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            const params = {
              userName: this.$store.state.user.userInfo.user.staffName,
              userId: this.$store.state.user.userInfo.user.staffId
            }
            let url = ''
            if (this.selectType == 1) {
              params.dictionaryDetailsId = data.dictionaryDetailsId
              params.parentId = data.parentId
              url = 'deleteHscParentType'
            } else if (this.selectType == 2) {
              url = 'hscDictionariesDelete'
              params.materialCode = data.materialCode
            } else if (this.selectType == 3) {
              url = 'deleteTypeSetting'
              params.ids = data.id
            } else if (this.selectType == 4) {
              url = 'deleteHscRiskLeve'
              params.ids = data.id
            } else if (this.selectType == 5) {
              url = 'deleteHcsConfig'
              params.id = data.id
            }
            this.$api[url](params).then((res) => {
              if (res.code == 200) {
                this.$message({ message: '删除成功', type: 'success' })
                this.getTableDataList()
                this.isLastPage(1)
              } else {
                this.$message.error(res.message || '删除失败！')
              }
            })
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            })
          })
      } else {
        let queryData = {
          parentId: this.selectType,
          type: key
        }
        // 编辑查看
        if (data) {
          queryData.rowData = data
        }
        this.$router.push({
          name: 'addFunctionConfig',
          query: queryData
        })
      }
    },
    nodeClick(data) {
      this.$router.push({
        query: {
          type: data.id
        }
      })
      this.selectType = data.id
      this.pagination.currentPage = 1
      this.pagination.pageSize = 15
      // this.tableData = []
      if (this.selectType == 3) {
        this.getParamsTypeData()
      }
      this.tableKey = Math.random()
      this.getTableDataList()
    },
    // 判断当前页是否是最后一页
    isLastPage(deleteNum) {
      let deleteAfterPage = Math.ceil((this.pagination.total - deleteNum) / this.pagination.pageSize)
      let currentPage = this.pagination.currentPage > deleteAfterPage ? deleteAfterPage : this.pagination.currentPage
      this.pagination.currentPage = currentPage < 1 ? 1 : currentPage
    },
    // 重置
    resetForm() {
      this.pagination.pageSize = 15
      this.pagination.currentPage = 1
      this.searchFrom = {
        keywords: '',
        status: ''
      }
      this.getTableDataList()
    },
    // 查询
    searchForm() {
      this.pagination.pageSize = 15
      this.pagination.currentPage = 1
      this.getTableDataList()
    },
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.getTableDataList()
    },
    handleCurrentChange(val) {
      this.pagination.currentPage = val
      this.getTableDataList()
    },
    // 树划入划出效果
    onMouseOver(id) {
      const parentWidth = this.$refs[`nodeLabel${id}`].parentNode.offsetWidth // 获取元素父级可视宽度
      const contentWidth = this.$refs[`nodeLabel${id}`].offsetWidth // 获取元素可视宽度
      this.showTooltip = contentWidth <= parentWidth
    },
    getTableDataList() {
      const params = {
        currentPage: this.pagination.currentPage,
        pageSize: this.pagination.pageSize,
        userType: 1,
        userName: this.$store.state.user.userInfo.user.staffName,
        userId: this.$store.state.user.userInfo.user.staffId,
        status: this.searchFrom.status
      }
      let url = ''
      if (this.selectType == '1') {
        url = 'hscParentTypeData'
        params.materialName = this.searchFrom.keywords
        delete params.status
        if (this.searchFrom.status) {
          params.dictionaryDetailsStatus = this.searchFrom.status == '0' ? '1' : '0'
        }
        this.isShowPage = true
      } else if (this.selectType == '2') {
        url = 'hscDictionariesList'
        params.materialName = this.searchFrom.keywords
        this.isShowPage = false
      } else if (this.selectType == '3') {
        url = 'hscTypeSettingList'
        this.isShowPage = false
        params.typeCode = ''
        params.parentId = ''
        params.name = this.searchFrom.keywords
      } else if (this.selectType == '4') {
        url = 'hscRiskLevelList'
        params.name = this.searchFrom.keywords
        this.isShowPage = false
      } else if (this.selectType == '5') {
        url = 'getHcsConfigDataList'
        params.name = this.searchFrom.keywords
        this.isShowPage = false
      }
      this.tableLoading = true
      this.$api[url](params).then((res) => {
        if (res.code == '200') {
          if (this.selectType == '1') {
            this.tableData = transData(res.data, 'id', 'pid', 'children')
          } else {
            this.tableData = res.data.list
            this.pagination.total = res.data.sum
          }
          this.tableKey = Math.random()
        } else {
          this.$message.error(res.message || '获取失败！')
        }
        this.tableLoading = false
      })
    },
    // 类型设置 参数类型
    getParamsTypeData() {
      const params = {
        parentId: '#',
        userName: this.$store.state.user.userInfo.user.staffName,
        userId: this.$store.state.user.userInfo.user.staffId
      }
      this.$api.hscTypeSettingList(params).then((res) => {
        if (res.code == '200') {
          this.paramsType = res.data.list
          if (this.tableData.length) {
            this.tableKey = Math.random()
          }
        }
      })
    },
    // 查看图片
    viewImg(url) {
      if (!url) return this.$message.error('暂无更多！')
      // this.dialogImageUrl = sessionStorage.getItem('ihcrs_picPrefix') + '/' + url
      this.dialogImageUrl = this.$tools.imgUrlTranslation(url)
      this.dialogVisible = true
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: #e6effc !important;
}
::v-deep .el-tree-node__content {
  height: 36px !important;
  position: relative;
}
.space-content {
  height: 100%;
  display: flex;
  .space-content-left {
    width: 280px;
    height: 100%;
    padding: 0px 10px 10px 10px;
    background: #fff;
    border-radius: 4px 0px 0px 4px;
    border-right: 1px solid #e4e7ed;
    .left_content {
      width: 100%;
      height: calc(100% - 95px);
      overflow: auto;
      padding-top: 16px;
    }
    .left_title {
      font-weight: bold;
      font-size: 16px;
      color: #333333;
      line-height: 22px;
      padding: 16px 14px;
    }
  }
  .space-content-right {
    height: 100%;
    min-width: 0;
    padding: 10px 20px 20px 20px;
    background: #fff;
    border-radius: 0px 4px 4px 0px;
    flex: 1;
    .search-from {
      & > div,
      .el-button {
        margin-right: 10px;
        margin-top: 10px;
      }
      & > .el-button:last-child {
        margin: 0px;
      }
    }
    .search-control {
      margin-bottom: 10px;
      & > .el-button {
        margin-top: 10px;
        margin-right: 10px;
        margin-left: 0px;
      }
      // & > .el-button:last-child {
      //   margin: 0px;
      // }
    }
    .contentTable-main {
      flex: 1;
      overflow: hidden;
    }
    .contentTable-footer {
      padding: 10px 0 0;
    }
  }
  .content {
    width: 100%;
    max-height: 500px !important;
    overflow: auto;
    background-color: #fff !important;
  }
}
::v-deep .el-tree-node {
  white-space: normal;
  // outline: 0;
}
</style>
