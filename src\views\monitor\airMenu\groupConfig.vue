<template>
  <GroupConfigVue :projectCode="projectCode" :type="'1'" />
</template>

<script>
import GroupConfigVue from '@/views/monitor/lightingMonitoring/airAndLightingCom/groupConfig.vue'
import { monitorTypeList } from '@/util/dict.js'
export default {
  components: {
    GroupConfigVue
  },
  data() {
    return {
      projectCode: monitorTypeList.find((item) => item.projectName == '空调监测').projectCode
    }
  },
  mounted() {
    
  },

  methods: {
    
  }
}
</script>
<style lang="scss" scoped>

</style>
