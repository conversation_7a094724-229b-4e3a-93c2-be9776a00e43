<template>
  <PageContainer>
    <div slot="content" class="elevator-content">
      <div ref="largeScreenMonitoring" class="largeScreenMonitoring">
        <div class="largeScreen_content">
          <div class="largeScreen_top">
            <div class="largeScreen_top_item">
              <div class="card_box_title card_box_long_bg">燃气监测</div>
              <div class="monitor_spot">
                <div v-for="(item, index) in gasMonitoringList" :key="index + 'gas'" class="monitor_spot_item">
                  <div class="monitor_spot_item_left">
                    <img src="@/assets/images/monitor/monitoring_points.png" alt="">
                    <P>{{ item.monitorName }}</P>
                  </div>
                  <div class="monitor_spot_item_right">
                    <p :style="{color: item.isNormal? '#FA403C': ''}">{{ item.monitorValue || '-' }}</p>
                    <span :style="{color: item.isNormal? '#FA403C': ''}">PPM</span>
                    <div class="monitor_spot_btn" :style="{backgroundColor: item.isNormal? '#FA403C': ''}">{{ item.isNormal ? '报警':'正常'}}</div>
                  </div>
                </div>
                <div v-if="!gasMonitoringList.length" class="echart-null">
                  <img src="@/assets/images/null.png" alt="" />
                  <div>暂无数据~</div>
                </div>
              </div>
            </div>
            <div class="largeScreen_top_item">
              <div class="card_box_title card_box_long_bg">锅炉监测</div>
              <div class="boiler_monitoring_top">
                <img src="@/assets/images/monitor/boiler_img.png" alt="">
                <div class="boiler_monitoring_totle">
                  <p>总数</p>
                  <b>{{ totalNumberOfBoilers.total || '-' }}<span>台</span></b>
                </div>
                <div class="boiler_monitoring_totle">
                  <p>启动</p>
                  <b>{{ totalNumberOfBoilers.startTotal || '-' }}<span>台</span></b>
                </div>
              </div>
              <el-carousel v-if="boilerMonitoringList.length" ref="elevatorCarousel" class="elevator_carousel" :autoplay="false" arrow="always">
                <el-carousel-item v-for="item in boilerMonitoringList" :key="item.surveyCode">
                  <div class="elevator_carousel_list">
                    <div v-for="v in item" :key="v.paramId" class="elevator_carousel_item">
                      <div class="elevator_carousel_title">
                        <h3>{{v.surveyName}}</h3>
                        <span @click="goToEntityDetails(v)" >详情</span>
                      </div>
                      <div class="elevator_carousel_content">
                        <div v-for="objItem in v.paramObj" :key="objItem.paramId" class="elevator_carousel_content_item">
                          <p>{{ objItem.paramName }}</p>
                          <div class="elevator_carousel_content_item_status">
                            <h3 v-if="objItem.paramId == '30265'" :style="{color: objItem.paramValue ? '' : '#A2B7D9'}" >{{ objItem.paramValue ? '启动' : '停止'}}</h3>
                            <el-tooltip v-else class="item" :disabled="showTooltip" effect="dark" :content="(objItem.paramValue || '-') + objItem.unit" placement="top">
                              <h3 @mouseenter="visibilityChange">{{ objItem.paramValue || '-' }}{{ objItem.unit }}</h3>
                            </el-tooltip>
                            <div v-if="objItem.warnColor" :style="{backgroundColor: objItem.warnColor}" class="monitor_spot_btn">{{ objItem.warnName }}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-carousel-item>
              </el-carousel>
              <div v-if="!boilerMonitoringList.length" class="echart-null">
                <img src="@/assets/images/null.png" alt="" />
                <div>暂无数据~</div>
              </div>
            </div>
            <div class="largeScreen_top_item">
              <div class="card_box_title card_box_long_bg">环境监测</div>
              <div v-if="environmentalMonitoringList.length" class="environmental_monitoring">
                <div v-for="(item, index) in environmentalMonitoringList" :key="index + 'environmental'" class="environmental_monitoring_item" @click="environmentDetails(item)">
                  <el-tooltip class="item" effect="dark" :disabled="showTooltip" :content="item.paramName" placement="top">
                    <p @mouseenter="visibilityChange">{{ item.paramName }}</p>
                  </el-tooltip>
                  <b>{{ item.paramValue || '-' }}<span>{{ item.unit }}</span></b>
                  <div style="height: 20px;">
                    <div v-if="item.warnId" class="monitor_spot_btn" :style="{ backgroundColor:item.warnColor }">{{ item.warnName }}</div>
                  </div>
                </div>
              </div>
              <div v-if="!environmentalMonitoringList.length" class="echart-null">
                <img src="@/assets/images/null.png" alt="" />
                <div>暂无数据~</div>
              </div>
            </div>
            <div class="video_surveillance" @click="toVideo()">
              <img src="@/assets/images/monitor/video_surveillance.png" alt="">
              <p>视频监控</p>
            </div>
          </div>
          <div class="largeScreen_bottom">
            <div class="largeScreen_bottom_item">
              <div class="card_box_title card_box_long_bg">
                <div class="card_box_title_left">
                  <span>报警统计</span>
                  <div @click="goToAlarmList">
                    <el-badge class="mark" :value="alarmSum" :max="99" />
                  </div>
                </div>
                <div class="details" @click="alarmDetails">
                  详情
                </div>
              </div>
              <div class="alarm_statistics">
                <img src="@/assets/images/monitor/boiler-alarm.png" alt="">
                <div class="alarm_statistics_total">
                  <p>报警总数</p>
                  <b>{{ alarmStatistics.total }}<span>个</span></b>
                </div>
                <div class="alarm_statistics_total">
                  <p>未处理</p>
                  <b>{{ alarmStatistics.noDealCount }}<span>个</span></b>
                </div>
                <div class="alarm_statistics_total">
                  <p>处理中</p>
                  <b>{{ alarmStatistics.isDealCount }}<span>个</span></b>
                </div>
              </div>
              <div v-if="!alarmTypeStatisticsShow" class="echart-null">
                <img src="@/assets/images/null.png" alt="" />
                <div>暂无数据~</div>
              </div>
              <div v-else style="width: 100%; height: calc(100% - 110px);">
                <div id="alarmTypeStatistics"></div>
              </div>
            </div>
            <div class="largeScreen_bottom_item">
              <div class="card_box_title card_box_special_long_bg">
                <div class="card_box_title_left">
                  <span>能耗分析</span>
                </div>
                <div class="card_box_title_right">
                  <div class="please_select">
                    <p>{{ elevatorSelectId ? energyConsumptionOptions.find(ele => ele.id == elevatorSelectId).name : '请选择'}}</p>
                    <div class="select_arrow"></div>
                    <el-select v-model="elevatorSelectId" class="custom_select" :popper-append-to-body="false" placeholder="请选择" @change="selectChange">
                      <el-option v-for="item in energyConsumptionOptions" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                    </el-select>
                  </div>
                  <div class="vertical_bar">
                    <div></div>
                  </div>
                  <div class="time_date">
                    <div
                    v-for="item in dateTime"
                    :key="item.type"
                    class="time_date_item"
                    :class="item.type == dateTimeValue ? 'time_date_item_active' : ''"
                    @click="onDateTime(item)"
                    >
                      {{item.name}}
                    </div>
                    <el-date-picker
                      ref="datePicker"
                      v-model="assemblyDateRange"
                      class="my-date-picker"
                      popper-class="diabloPopperClass"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="开始月份"
                      end-placeholder="结束月份"
                      :picker-options="pickerOptions"
                      :clearable="false"
                      prefix-icon=0
                      value-format="yyyy-MM-dd"
                      @blur="onBlur"
                    >
                    </el-date-picker>
                  </div>
                  <div v-if="dateTimeValue == 4" class="custom_date">
                    ({{ assemblyDateRange[0] }} 至 {{ assemblyDateRange[1] }})
                  </div>
                </div>
              </div>
              <div class="energy_consumption_analysis">
                <div class="energy_consumption_analysis_left">
                  <div class="tabs">
                    <div
                    v-for="(item, index) in tabsList"
                    :key="item.type"
                    class="tabs_item"
                    :class="tabIndex == index ? 'tabs_item_active' : ''"
                    @click="onDataType(index)">
                      <span>{{item.name}}</span>
                    </div>
                  </div>
                  <div v-if="!energyConsumptionAnalysisPieShow" class="echart-null">
                    <img src="@/assets/images/null.png" alt="" />
                    <div>暂无数据~</div>
                  </div>
                  <div v-else style="width: 100%; height: calc(100% - 110px);">
                    <div id="energyConsumptionAnalysisPie"></div>
                  </div>
                  <div class="yoy_monthOnMonthRatio">
                    <div v-if="EnergyConsumptionPie.yoy">
                      <p>同比</p>
                      <span>{{ EnergyConsumptionPie.yoy }}</span>
                      <img v-if="EnergyConsumptionPie.yoyStatus=='1'" src="@/assets/images/monitor/up.png" alt="">
                      <img v-else-if="EnergyConsumptionPie.yoyStatus=='0'" src="@/assets/images/monitor/down.png" alt="">
                    </div>
                    <div v-if="EnergyConsumptionPie.qoq">
                      <p>环比</p>
                      <span>{{ EnergyConsumptionPie.qoq }}</span>
                      <img v-if="EnergyConsumptionPie.qoqStatus == '1'" src="@/assets/images/monitor/up.png" alt="">
                      <img v-else-if="EnergyConsumptionPie.qoqStatus == '0'" src="@/assets/images/monitor/down.png" alt="">
                    </div>
                  </div>
                </div>
                <div class="energy_consumption_analysis_right">
                  <div class="bar_chart_title">
                    <!-- <h3>供气量</h3> -->
                    <h4>供汽总量：<span>{{steamSupplyObj.total}} {{ steamSupplyObj.unit }}</span></h4>
                    <div v-if="steamSupplyObj.yoy">
                      <p>同比</p>
                      <span>{{ steamSupplyObj.yoy }}</span>
                      <img v-if="steamSupplyObj.yoyStatus=='1'" src="@/assets/images/monitor/up.png" alt="">
                      <img v-else-if="steamSupplyObj.yoyStatus=='0'" src="@/assets/images/monitor/down.png" alt="">
                    </div>
                    <div v-if="steamSupplyObj.qoq">
                      <p>环比</p>
                      <span>{{ steamSupplyObj.qoq }}</span>
                      <img v-if="steamSupplyObj.qoqStatus == '1'" src="@/assets/images/monitor/up.png" alt="">
                      <img v-else-if="steamSupplyObj.qoqStatus == '0'" src="@/assets/images/monitor/down.png" alt="">
                    </div>
                  </div>
                  <div v-if="!energyConsumptionAnalysisColumnShow" class="echart-null">
                    <img src="@/assets/images/null.png" alt="" />
                    <div>暂无数据~</div>
                  </div>
                  <div v-else style="width: 100%; height: calc(100% - 100px);">
                    <echarts ref="energyConsumptionAnalysisColumn" domId="energyConsumptionAnalysisColumn" width="100%" height="100%" isTrigger :xyType="'xAxis'" :showWord="4" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <AlarmDialog ref="alarmDialog" />
      <EnvironmentDialog ref="environmentDialog" />
      <AlarmListDialog ref="alarmListDialog" />
      <AlarmAnalysisDialog ref="alarmAnalysisDialog" />
    </div>

  </PageContainer>
</template>

<script>
import EnvironmentDialog from './components/environmentDialog.vue'
import AlarmListDialog from './components/alarmListDialog.vue'
import AlarmAnalysisDialog from './components/alarmAnalysisDialog.vue'
import { monitorTypeList } from '@/util/dict.js'
import moment from 'moment'
import mixin from './mixin/mixin.js'
import * as echarts from 'echarts'
export default {
  components: {
    EnvironmentDialog,
    AlarmListDialog,
    AlarmAnalysisDialog
  },
  mixins: [mixin],
  data() {
    return {
      showTooltip: false,
      projectCode: monitorTypeList.find((item) => item.projectName == '锅炉监测').projectCode,
      energyConsumptionOptions: [
        {
          name: '能耗',
          id: '1'
        },
        {
          name: '标煤',
          id: '0'
        }
      ],
      elevatorSelectId: '1',
      tabIndex: 0,
      tabsList: [
        {
          name: '燃气',
          type: 1
        },
        {
          name: '用水',
          type: 2
        },
        {
          name: '用电',
          type: 3
        }
      ],
      alarmTypeStatisticsShow: true,
      energyConsumptionAnalysisPieShow: true,
      energyConsumptionAnalysisColumnShow: true,
      dateTime: [
        {
          name: '全部',
          type: 1
        },
        {
          name: '本月',
          type: 2
        },
        {
          name: '本年',
          type: 3
        },
        {
          name: '自定义',
          type: 4
        }
      ],
      dateTimeValue: 1,
      assemblyDateRange: [],
      dateRange: ['', moment().format('YYYY-MM-DD')],  // 选择的日期
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      },
      totalNumberOfBoilers: {},   // 锅炉检测总数
      boilerMonitoringList: [],    //  锅炉检测设备list
      gasMonitoringList: [],   // 燃气监测
      environmentalMonitoringList: [],  // 环境监测
      steamSupplyObj: {},      // 供汽总量
      alarmStatistics: {},        // 报警统计
      EnergyConsumptionPie: {},   // 能耗分析 饼图
      alarmSum: 0
    }
  },
  mounted() {
    // 测试报警数据
    // let a = '{"data":{"alarmData":{"alarmDetails":"燃气泄漏","alarmEndTime":1692126490000,"alarmEntityTypeId":30263,"alarmEntityTypeName":"可燃气体探测器","alarmId":"BJ2023818179501692349790124","alarmLevel":3,"alarmObjectId":"27746fafdcb9401b925f6f93fb8f8bff","alarmObjectName":"1#燃气监测点","alarmRule":"10.0ppm<浓度测量值<76.0ppm且持续时间>10s","alarmSource":"锅炉监测","alarmSpaceId":"1574997197546598402","alarmSpaceName":"4F主院区>综合急诊急救楼","alarmSpaceStr":"null,1574997197546598402","alarmStartTime":1692126480000,"alarmValue":"74","continueTime":10,"createdTime":1692349790165,"hospitalCode":"BJSJTYY","id":"1182","incidentName":"浓度测量值","incidentParam":30296,"incidentParamColour":"","incidentType":"0","menuCode":"a14a3272340141c7ab2a303446577e20","menuName":"燃气探测器","projectCode":"IEMC-Boiler","projectName":"锅炉监测","remark":"","unitCode":"BJSYGJ"},"broadcastNum":1,"alarmCode":200,"broadcastMsg":"4F主院区>综合急诊急救楼1#燃气监测点燃气泄漏，请及时处理"},"type":"alarm"}'
    // setTimeout(() => {
    //   this.$store.commit('socket/setSocketMsgs', a)
    // }, 5000)
    this.getBoilerCount()
    this.getEnergyConsumptionAnalysisPieEcharts()
    this.getEnergyConsumptionAnalysisColumnEcharts()
  },
  methods: {
    visibilityChange(e) {
      const item = e.target
      const itemWidth = item.offsetWidth
      const itemScrollWidth = item.scrollWidth
      this.showTooltip = !(itemWidth < itemScrollWidth)
    },
    goToEntityDetails({surveyCode}) {
      console.log(surveyCode)
      this.$router.push({
        path: '/boilerMenu/boilerMonitor',
        query: {
          surveyCode
        }
      })
    },
    goToAlarmList() {
      this.$refs.alarmListDialog.getOpenDialog(this.projectCode)
    },
    alarmDetails() {
      this.$refs.alarmAnalysisDialog.getOpenDialog(this.projectCode)
    },
    // 环境监测点击弹框
    environmentDetails(item) {
      console.log(item)
      this.$refs.environmentDialog.getData({
        ...item,
        projectCode: this.projectCode
      })
    },
    getBoilerCount() {
      let data = {
        projectCode: this.projectCode,
        currentDate: moment().format('YYYY-MM-DD')
        // currentDate: '2023-08-14'
      }
      // 燃气监测
      this.$api.queryGasMonitoring(data).then(res => {
        if (res.code == 200) {
          this.gasMonitoringList = res.data
        }
      })
      // 锅炉检测总数
      this.$api.boilerCount(data).then(res => {
        if (res.code == 200) {
          this.totalNumberOfBoilers = res.data
        }
      })
      // 锅炉检测设备list
      this.$api.queryBoilerMonitor(data).then(res => {
        if (res.code == 200) {
          this.boilerMonitoringList = this.formatArray(res.data, 2)
        }
      })
      // 环境监测
      this.$api.queryEnvMonitor(data).then(res => {
        if (res.code == 200) {
          this.environmentalMonitoringList = res.data.map(ele => {
            return ele.paramObj.map(item => {
              return {
                ...item,
                paramName: ele.surveyName + item.paramName,
                surveyName: ele.surveyName
              }
            })
          }).flat()
        }
      })
      // 报警统计
      this.$api.getBoilerByProjectCode(data).then((res) => {
        if (res.code == 200) {
          this.alarmStatistics = res.data
          let arr = res.data.policeList.map(ele => {
            return {
              name: ele.entityTypeName,
              value: ele.count
            }
          })
          this.$nextTick(() => {
            this.setElevatorBrandEcharts(arr)
          })
        }
      })
      let selectData = {
        projectCode: this.projectCode,
        startTime: moment().format('YYYY-MM-DD') + ' 00:00:00',
        endTime: moment().format('YYYY-MM-DD HH:mm:ss'),
        alarmStatus: 0
      }
      this.$api.selectBoilerAlarmRecord(selectData).then(res => {
        this.alarmSum = res.data || 0
      })
    },
    formatArray(data, num) {
      var dataList2 = []
      for (var i = 0, len = data.length; i < len; i += num) {
        dataList2.push(data.slice(i, i + num))
      }
      return dataList2
    },
    selectChange() {
      this.getEnergyConsumptionAnalysisPieEcharts()
      this.getEnergyConsumptionAnalysisColumnEcharts()
    },
    onDataType(index) {
      this.tabIndex = index
      this.getEnergyConsumptionAnalysisPieEcharts()
    },
    onBlur() {
      if (this.assemblyDateRange.length) {  // 若选择了自定义日期则赋值
        this.dateRange = this.assemblyDateRange
        this.getEnergyConsumptionAnalysisPieEcharts()
        this.getEnergyConsumptionAnalysisColumnEcharts()
      } else {  // 如果自定义未选择  则回退到全部
        this.onDateTime({type: 1})
      }
    },
    onDateTime(item) {
      this.dateTimeValue = item.type
      if (item.type == 4) {  // 自定义
        this.$refs.datePicker.focus()
      } else if (item.type == 1) {   // 全部
        this.dateRange = ['', moment().format('YYYY-MM-DD')]
      } else if (item.type == 2) {   // 本月
        this.dateRange = [moment().format('YYYY-MM-01'), moment().format('YYYY-MM-DD')]
      } else if (item.type == 3) {   // 本年
        this.dateRange = [moment().format('YYYY-01-01'), moment().format('YYYY-MM-DD')]
      }
      if (item.type != 4) {
        this.getEnergyConsumptionAnalysisPieEcharts()
        this.getEnergyConsumptionAnalysisColumnEcharts()
      }
    },
    // 能耗分析折线图
    getEnergyConsumptionAnalysisColumnEcharts() {
      let param = {
        paramType: 4,
        flag: this.elevatorSelectId,
        projectCode: this.projectCode,
        queryTimeType: this.dateTimeValue,
        startTIme: this.dateRange[0],
        endTime: this.dateRange[1]
      }
      this.$api.queryEnergyAnalyse(param).then(res => {
        this.steamSupplyObj = res.data
        this.energyConsumptionAnalysisColumnShow = !!res.data?.param?.length
        if (!res.data.param?.length) return
        let data = res.data.param.map(ele => {
          return {
            name: ele.surveyName,
            value: ele.paramValue,
            percentage: ele.percentage
          }
        })
        this.$nextTick(() => {
          this.$refs.energyConsumptionAnalysisColumn.init(this.setEnergyConsumptionAnalysisColumnEcharts(data, res.data.unit))
        })
      })
    },
    // 能耗分析柱状图
    setEnergyConsumptionAnalysisColumnEcharts(data, unit) {
      if (!data.length) return
      let textColor = '#86909C'
      let option = {
        color: ['#3562DB'],
        xAxis: {
          type: 'category',
          data: data.map(x => x.name),
          triggerEvent: true,
          axisLine: {
            show: false
          },
          axisLabel: {
            color: textColor,
            fontSize: 12,
            formatter: function (params) {
              var val = ''
              if (params.length > 4) {
                val = params.substr(0, 4) + '...'
                return val
              } else {
                return params
              }
            }
          }
        },
        yAxis: {
          type: 'value',
          name: `单位：${unit}`,
          nameTextStyle: {
            color: textColor
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: textColor,
            fontSize: 13
          },
          // y轴轴线颜色
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed',
              color: 'rgba(255,255,255,0.2)'
            }
          }
        },
        dataZoom: [
          {
            type: 'slider',
            realtime: true,
            start: 0,
            end: 20,  // 数据窗口范围的结束百分比。范围是：0 ~ 100。
            height: 5, // 组件高度
            left: 5, // 左边的距离
            right: 5, // 右边的距离
            bottom: 10, // 下边的距离
            show: data.length > 6,  // 是否展示
            showDetail: false, // 拖拽时是否展示滚动条两侧的文字
            zoomLock: true,         // 是否只平移不缩放
            moveOnMouseMove: false, // 鼠标移动能触发数据窗口平移
            // zoomOnMouseWheel: false, // 鼠标移动能触发数据窗口缩放
            // 下面是自己发现的一个问题，当点击滚动条横向拖拽拉长滚动条时，会出现文字重叠，导致效果很不好，以此用下面四个属性进行设置，当拖拽时，始终保持显示六个柱状图，可结合自己情况进行设置。添加这个属性前后的对比见**图二**
            startValue: 0, // 从头开始。
            endValue: 6,  // 最多六个
            minValueSpan: 6,  // 放大到最少几个
            maxValueSpan: 6  //  缩小到最多几个
          },
          {
            type: 'inside',  // 支持内部鼠标滚动平移
            start: 0,
            end: 20,
            zoomOnMouseWheel: false,  // 关闭滚轮缩放
            moveOnMouseWheel: true, // 开启滚轮平移
            moveOnMouseMove: true  // 鼠标移动能触发数据窗口平移
          }
        ],
        series: [
          {
            data: data.map(x => x.value),
            type: 'bar',
            barWidth: 10,
            itemStyle: {
              color: '#F4DB67'
            },
            label: {
              show: true, // 显示数值标签
              position: 'top', // 数值标签显示在柱状图上方
              color: '#D4DEEC', // 设置标签文本颜色
              fontSize: 12, // 设置标签文本大小
              backgroundColor: 'rgba(255,255,255,0.1)',
              padding: 5
            }
          }
        ],
        grid: { // 让图表占满容器
          top: '30px',
          left: '16px',
          right: '16px',
          bottom: '20px',
          containLabel: true
        }
      }
      return option
    },
    // 能耗分析饼图
    getEnergyConsumptionAnalysisPieEcharts() {
      console.log(this.dateRange)
      let param = {
        paramType: this.tabsList[this.tabIndex].type,
        flag: this.elevatorSelectId,
        projectCode: this.projectCode,
        queryTimeType: this.dateTimeValue,
        startTIme: this.dateRange[0],
        endTime: this.dateRange[1]
      }
      this.$api.queryPowerAnalyseByPie(param).then(res => {
        this.EnergyConsumptionPie = res.data
        this.energyConsumptionAnalysisPieShow = !!res.data?.param?.length
        let data = res.data?.param?.map(ele => {
          return {
            name: ele.surveyName,
            value: ele.paramValue,
            percentage: ele.percentage
          }
        })
        this.$nextTick(() => {
          this.setEnergyConsumptionAnalysisPieEcharts(data, res.data.unit, res.data.total)
        })
      })
    },
    // 能耗分析饼图echarts
    setEnergyConsumptionAnalysisPieEcharts(data, unit, sum) {
      if (!data?.length) return
      const getchart = echarts.init(document.getElementById('energyConsumptionAnalysisPie'))
      const nameList = Array.from(data, (item) => item.name)
      // const sum = data.reduce((per, cur) => per + cur.value, 0)
      var objData = this.array2obj(data, 'name')
      let option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          type: 'scroll',
          orient: 'vertical',
          top: '30',
          x: '50%',
          pageIconColor: '#5188fc', // 激活的分页按钮颜色
          pageIconInactiveColor: '#c2c6ce', // 没激活的分页按钮颜色
          data: nameList,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 16,
          formatter: function (name) {
            return '{a|' + name + '}{c|' + objData[name].value + unit + '}'
          },
          textStyle: {
            rich: {
              a: {
                align: 'center',
                fontSize: 13,
                color: 'rgba(255,255,255,1)'
              },
              c: {
                align: 'center',
                width: 100,
                fontSize: 15,
                color: '#00C2FF'
              }
            }
          }
        },
        title: [
          {
            text: sum,
            left: '25%',
            top: '35%',
            textAlign: 'center',
            textStyle: {
              fontSize: '18',
              fontWeight: '600',
              color: '#FFF',
              textAlign: 'center'
            }
          },
          {
            text: `总量/${unit}`,
            x: '25%',
            y: '50%',
            textAlign: 'center',
            textStyle: {
              fontSize: '16',
              fontWeight: '400',
              color: '#A3A9AD',
              textAlign: 'center'
            }
          }
        ],
        series: [
          {
            type: 'pie',
            roundCap: true,
            radius: ['56%', '72%'],
            center: ['25%', '50%'],
            hoverAnimation: true,
            label: {
              normal: {
                show: false,
                position: 'center',
                // formatter: '{value|{d}' + '%' + '}\n{label|{b}}',
                formatter: function (data) {
                  // return '{value|' + objData[data.name].value + '}\n{label|' + data.name + '}'
                  return ''
                },
                rich: {
                  value: {
                    padding: 5,
                    align: 'center',
                    verticalAlign: 'middle',
                    fontSize: 24,
                    fontWeight: '600'
                  },
                  label: {
                    align: 'center',
                    verticalAlign: 'middle',
                    color: '#A3A9AD',
                    fontWeight: '400',
                    fontSize: 14
                  }
                }
              },
              emphasis: {
                show: true,
                textStyle: {
                  fontSize: '12',
                  color: '#fff'
                }
              }
            },
            itemStyle: {
              borderWidth: 3,
              borderColor: '#09141e'
            },
            data: data
          },
          {
            name: '',
            type: 'pie',
            startAngle: 80,
            radius: ['54%'],
            hoverAnimation: false,
            center: ['25%', '50%'],
            tooltip: {
              show: false
            },
            itemStyle: {
              color: 'rgba(10, 25, 40, 1)',
              borderWidth: 1,
              borderColor: '#1E2C39'
            },
            data: [sum]
          },
          {
            name: '',
            type: 'pie',
            startAngle: 80,
            radius: ['48%'],
            hoverAnimation: false,
            center: ['25%', '50%'],
            tooltip: {
              show: false
            },
            itemStyle: {
              color: 'rgba(21, 36, 50, 1)'
            },
            data: [sum]
          },
          {
            name: '',
            type: 'pie',
            startAngle: 80,
            radius: ['77%', '78%'],
            hoverAnimation: false,
            center: ['25%', '50%'],
            tooltip: {
              show: false
            },
            itemStyle: {
              normal: {
                color: 'rgba(10, 25, 40, .1)',
                borderColor: '#1E2C39',
                borderWidth: 1
              }
            },
            data: [sum]
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
      getchart.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: 0
      })
      getchart.on('mouseover', (e) => {
        // 取消默认高亮
        getchart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: 0
        })
      })
      // 鼠标移出后默认高亮
      getchart.on('mouseout', (e) => {
        getchart.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: 0
        })
      })
    },
    array2obj(array, key) {
      var resObj = {}
      for (var i = 0; i < array.length; i++) {
        resObj[array[i][key]] = array[i]
      }
      return resObj
    },
    // 报警统计echarts
    setElevatorBrandEcharts(data) {
      const getchart = echarts.init(document.getElementById('alarmTypeStatistics'))
      const sum = data.reduce((a, b) => a + b.value, 0)
      var objData = this.array2obj(data, 'name')
      // const gap = (1 * sum) / 100
      // const pieData = []
      // const gapData = {
      //   name: '',
      //   value: gap,
      //   itemStyle: {
      //     color: 'transparent'
      //   }
      // }
      // for (let i = 0; i < data.brandPic.length; i++) {
      //   pieData.push({
      //     name: data.brandPic[i].assetBrand,
      //     value: data.brandPic[i].brandNum,
      //     itemStyle: {
      //       normal: {
      //         // borderRadius: 5
      //       }
      //     }
      //   })
      //   pieData.push(gapData)
      // }
      let option = {
        tooltip: {
          // show: false,
          trigger: 'item',
          // backgroundColor: "rgba(0, 0, 0, 0.1)",
          formatter: function (params) {
            if (params.name) {
              return params.name + ' : ' + params.value + '    (' + ((params.value / sum) * 100).toFixed(2) + '%)'
            }
          }
        },
        legend: {
          type: 'scroll',
          orient: 'vertical',
          top: '30',
          x: '40%',
          pageIconColor: '#5188fc', // 激活的分页按钮颜色
          pageIconInactiveColor: '#c2c6ce', // 没激活的分页按钮颜色
          data: data,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 16,
          formatter: function (name) {
            return '{a|' + name + '}{c|' + objData[name].value + '}'
          },
          textStyle: {
            rich: {
              a: {
                align: 'center',
                fontSize: 13,
                color: 'rgba(255,255,255,1)'
              },
              c: {
                align: 'center',
                width: 70,
                fontSize: 15,
                color: '#00C2FF'
              }
            }
          }
        },
        title: [
          // {
          //   text: sum,
          //   left: '48%',
          //   top: '37%',
          //   textAlign: 'center',
          //   textStyle: {
          //     fontSize: '24',
          //     fontWeight: '600',
          //     color: '#FFF',
          //     textAlign: 'center'
          //   }
          // },
          {
            text: '报警类型统计',
            x: '20%',
            y: '45%',
            textAlign: 'center',
            textStyle: {
              fontSize: '16',
              fontWeight: '400',
              color: '#A3A9AD',
              textAlign: 'center'
            }
          }
        ],
        series: [
          {
            type: 'pie',
            roundCap: true,
            radius: ['60%', '72%'],
            center: ['20%', '50%'],
            color: ['RGBA(100, 210, 255, 1)', 'RGBA(10, 132, 255, 1)', 'RGBA(244, 220, 110, 1)', 'RGBA(212, 222, 236, 1)', 'RGBA(94, 92, 230, 1)'],
            label: {
              show: false
            },
            labelLine: {
              show: false
            },
            data: data
          },
          {
            name: '',
            type: 'pie',
            startAngle: 80,
            radius: ['54%'],
            hoverAnimation: false,
            center: ['20%', '50%'],
            tooltip: {
              show: false
            },
            itemStyle: {
              color: 'rgba(10, 25, 40, 1)',
              borderWidth: 1,
              borderColor: '#1E2C39'
            },
            data: [sum]
          },
          {
            name: '',
            type: 'pie',
            startAngle: 80,
            radius: ['48%'],
            hoverAnimation: false,
            center: ['20%', '50%'],
            tooltip: {
              show: false
            },
            itemStyle: {
              color: 'rgba(21, 36, 50, 1)'
            },
            data: [sum]
          },
          {
            name: '',
            type: 'pie',
            startAngle: 80,
            radius: ['77%', '78%'],
            hoverAnimation: false,
            center: ['20%', '50%'],
            tooltip: {
              show: false
            },
            itemStyle: {
              normal: {
                color: 'rgba(10, 25, 40, .1)',
                borderColor: '#1E2C39',
                borderWidth: 1
              }
            },
            data: [sum]
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    toVideo() {
      this.$router.push({
        path: '/boilerMenu/boilerOverview/videoSurveillance',
        query: {
          projectCode: this.projectCode
        }
      })
    }
  }
}
</script>
<style lang="scss">
@import './css/index.scss';
</style>
<style lang="scss" scoped>

#alarmTypeStatistics,
#energyConsumptionAnalysisPie,
#energyConsumptionAnalysisColumn {
  width: 100%;
  height: 100%;
  z-index: 2;
}
.elevator-content {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  .largeScreenMonitoring {
    width: 100%;
    height: 100%;
    overflow: hidden;
    .largeScreen_content {
      height: calc(100% - 0px);
      width: 100%;
      padding: 16px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      background: url('~@/assets/images/elevator/elevator-bg.png') no-repeat;
      background-size: 100% 100%;
      .largeScreen_bottom{
        width: calc(100%);
        height: 45%;
        display: grid;
        grid-gap: 16px;
        grid-template-columns: 28% 71%;
        margin-top: 16px;
        .largeScreen_bottom_item{
          min-height: calc(100%);
          max-height: calc(100%);
          background: linear-gradient(180deg, #101D29 0%, #081A2B 100%);
          border: 1px solid #3B4C5B;
          padding: 20px;
          .energy_consumption_analysis{
            width: 100%;
            height: calc(100% - 31px);
            display: grid;
            grid-gap: 1%;
            grid-template-columns: 38% 61%;
            .energy_consumption_analysis_right{
              height: 100%;
              .bar_chart_title{
                width: 85%;
                display: flex;
                align-items: center;
                background: rgba(75,100,157,0.1);
                padding: 10px 16px;
                margin: 25px 0;
                h3{
                  font-size: 20px;
                  font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
                  font-weight: 400;
                  color: #FFFFFF;
                  line-height: 20px;
                  margin: 0;
                  margin-right: 30px;
                }
                h4{
                  font-size: 14px;
                  font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
                  font-weight: 400;
                  color: #FFFFFF;
                  line-height: 20px;
                  margin: 0;
                  margin-right: 25px;
                  span{
                    font-size: 16px;
                    font-family: HarmonyOS Sans SC-Medium, HarmonyOS Sans SC;
                    font-weight: 500;
                    color: #F4DB67;
                    line-height: 16px;
                    margin-left: 10px;
                  }
                }
                p{
                  font-size: 12px;
                  font-family: PingFang SC-Regular, PingFang SC;
                  font-weight: 400;
                  line-height: 14px;
                  color: #A2B7D9;
                  margin: 0;
                }
                span{
                  font-size: 16px;
                  font-family: Arial-Regular, Arial;
                  font-weight: 400;
                  color: #00BC6D;
                  line-height: 19px;
                  margin: 0 8px;
                }
                img{
                  margin-right: 16px;
                }
              }
            }
            .energy_consumption_analysis_left{
              height: 100%;
              .yoy_monthOnMonthRatio{
                display: flex;
                align-items: center;
                p{
                  font-size: 12px;
                  font-family: PingFang SC-Regular, PingFang SC;
                  font-weight: 400;
                  line-height: 14px;
                  color: #A2B7D9;
                  margin: 0;
                }
                span{
                  font-size: 16px;
                  font-family: Arial-Regular, Arial;
                  font-weight: 400;
                  color: #00BC6D;
                  line-height: 19px;
                  margin: 0 8px;
                }
                img{
                  margin-right: 16px;
                }
              }
              .tabs{
                display: flex;
                justify-content: center;
                margin-top: 40px;
                .tabs_item{
                  cursor: pointer;
                  width: 90px;
                  height: 30px;
                  background: linear-gradient(180deg, rgba(72,117,188,0) 80%, rgba(72,117,188,0.2) 180%),
                  linear-gradient(90deg, rgba(72,117,188,0) 80%, rgba(240, 240, 241, 0.6) 300%),
                  linear-gradient(-180deg, rgba(72,117,188,0) 60%, rgba(72,117,188,0.2) 110%);
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  span{
                    font-size: 16px;
                    font-weight: normal;
                    color: #99B1DD;
                  }
                }
                .tabs_item_active{
                  background: linear-gradient(180deg, rgba(72,117,188,0) 0%, rgba(8, 65, 156, 0.6) 100%),
                  linear-gradient(90deg, rgba(72,117,188,0) 80%, rgba(240, 240, 241, 0.8) 300%),
                  linear-gradient(-180deg, rgba(72,117,188,0) 0%, rgba(8, 65, 156,0.6) 100%);
                  span{
                    color: #FFFFFF;
                  }
                }
              }
            }
          }
          .alarm_statistics{
            display: flex;
            margin-top: 20px;
            .alarm_statistics_total{
              flex: 1;
              display: flex;
              flex-direction: column;
              align-items: center;
              p{
                font-size: 16px;
                font-family: Alibaba PuHuiTi-Regular, Alibaba PuHuiTi;
                font-weight: 400;
                color: #A2B7D9;
                margin: 0;
              }
              b{
                font-size: 28px;
                font-family: DIN-Bold, DIN;
                font-weight: bold;
                color: #EEFBFE;
                span{
                  font-size: 14px;
                  font-family: PingFang SC-Regular, PingFang SC;
                  font-weight: 400;
                  color: #CCCED3;
                  margin-left: 4px;
                }
              }
            }
          }
        }
      }
      .largeScreen_top{
        width: calc(100%);
        height: 55%;
        display: grid;
        grid-gap: 16px;
        grid-template-columns: 28% 37% 28% calc(7% - 48px);
        .largeScreen_top_item{
          min-height: calc(100%);
          max-height: calc(100%);
          background: linear-gradient(180deg, #101D29 0%, #081A2B 100%);
          border: 1px solid #3B4C5B;
          padding: 20px;
          .environmental_monitoring{
            height: calc(100% - 30px);
            display: grid;
            grid-gap: 16px;
            grid-template-columns: calc(33.33% - 11px) calc(33.33% - 11px) calc(33.33% - 11px);
            overflow-y: auto;
            &_item{
              cursor: pointer;
              display: flex;
              // justify-content: center;
              align-items: center;
              flex-direction: column;
              p{
                max-width: 90%;
                overflow:hidden;
                text-overflow:ellipsis;
                white-space:nowrap;
                font-size: 16px;
                font-family: Alibaba PuHuiTi-Regular, Alibaba PuHuiTi;
                font-weight: 400;
                color: #A2B7D9;
              }
              b{
                font-size: 28px;
                font-family: DIN-Bold, DIN;
                font-weight: bold;
                color: #EEFBFE;
                span{
                  font-size: 14px;
                  font-family: PingFang SC-Regular, PingFang SC;
                  font-weight: 400;
                  color: #CCCED3;
                  margin-left: 4px;
                }
              }
            }
          }
          .elevator_carousel_list{
            height: 100%;
            width: 100%;
            display: grid;
            grid-gap: 10px;
            grid-template-columns: calc(50% - 10px) calc(50% - 10px);
          }
          .elevator_carousel_item{
            min-height: calc(100%);
            max-height: calc(100%);
            width: 100%;
            background: url('~@/assets/images/monitor/boiler_min_bg.png') no-repeat;
            background-size: 100% 100%;
            .elevator_carousel_content{
              padding: 0 10px;
              width: 100%;
              height: calc(100% - 65px);
              display: grid;
              grid-template-columns: 50% 50%;
              overflow: auto;
              .elevator_carousel_content_item{
                margin-bottom: 15px;
                p{
                  font-size: 12px;
                  font-family: OPPOSans-Regular, OPPOSans;
                  font-weight: 400;
                  color: #DFEEFE;
                  margin: 0;
                  margin-bottom: 6px;
                }
                .elevator_carousel_content_item_status{
                  display: flex;
                  h3{
                    max-width: 60px;
                    overflow:hidden;
                    text-overflow:ellipsis;
                    white-space:nowrap;
                    font-size: 16px;
                    font-family: HarmonyOS Sans SC-Medium, HarmonyOS Sans SC;
                    font-weight: 500;
                    color: #F4DB67;
                    margin: 0;
                    margin-right: 10px;
                  }
                }
              }
            }
            .elevator_carousel_title{
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 0 10px;
              h3{
                font-size: 16px;
                font-family: PingFang SC-Medium, PingFang SC;
                font-weight: 500;
                color: #FFFFFF;
                text-indent: 15px;
              }
              span{
                cursor: pointer;
                font-size: 14px;
                font-family: PingFang SC-Medium, PingFang SC;
                font-weight: 500;
                color: #2181F4;
              }
            }
          }
          .elevator_carousel {
            height: calc(100% - 160px);
            margin-bottom: 10px;
            display: flex;
            justify-content: center;
            ::v-deep .el-carousel__container {
              height: 100%;
              width: 90%;
              .el-carousel__arrow--left{
                left: -22px;
              }
              .el-carousel__arrow--right{
                right: -22px;
              }
              .el-carousel__arrow i{
                font-size: 26px;
                color: #F4DB67;
              }
              .el-carousel__item {
                display: flex;
                .elevator_img {
                  margin: auto;
                }
              }
              .el-carousel__arrow {
                width: 24px;
                height: 44px;
                background-color: transparent;
                border-radius: unset;
              }
              .el-carousel__arrow:hover {
                background-color: rgba(255, 255, 255, 0.1);
              }
            }
            ::v-deep .el-carousel__indicators {
              display: none;
            }
          }
          .boiler_monitoring_top{
            display: flex;
            margin: 20px 0;
            img{
              height: 90px;
              margin-right: 60px;
            }
            .boiler_monitoring_totle{
              flex: 1;
              display: flex;
              flex-direction: column;
              p{
                font-size: 16px;
                font-family: Alibaba PuHuiTi-Regular, Alibaba PuHuiTi;
                font-weight: 400;
                color: #A2B7D9;
              }
              b{
                font-size: 28px;
                font-family: DIN-Bold, DIN;
                font-weight: bold;
                color: #EEFBFE;
                span{
                  font-size: 14px;
                  font-family: PingFang SC-Regular, PingFang SC;
                  font-weight: 400;
                  color: #CCCED3;
                  margin-left: 5px;
                }
              }
            }
          }
          .monitor_spot{
            height: calc(100% - 31px);
            overflow-y: auto;
            .monitor_spot_item{
              height: 50px;
              width: 100%;
              padding: 0 20px;
              display: flex;
              justify-content: space-between;
              margin-top: 16px;
              background: linear-gradient(to left, #F4DB67, #F4DB67) left top no-repeat,
              linear-gradient(to bottom, #F4DB67, #F4DB67) left top no-repeat,
              linear-gradient(to left, #F4DB67, #F4DB67) right top no-repeat,
              linear-gradient(to bottom, #F4DB67, #F4DB67) right top no-repeat,
              linear-gradient(to left, #F4DB67, #F4DB67) left bottom no-repeat,
              linear-gradient(to bottom, #F4DB67, #F4DB67) left bottom no-repeat,
              linear-gradient(to left, #F4DB67, #F4DB67) right bottom no-repeat,
              linear-gradient(to left, #F4DB67, #F4DB67) right bottom no-repeat;
              background-size: 2px 8px, 8px 2px, 2px 8px, 8px 2px;
              background-color: #051634;
              .monitor_spot_item_left{
                display: flex;
                align-items: center;
                img{
                  height: 100%;
                }
                p{
                  margin: 0;
                  font-size: 16px;
                  font-weight: 400;
                  color: #A2B7D9;
                }
              }
              .monitor_spot_item_right{
                display: flex;
                align-items: flex-end;
                padding: 15px 0;
                p{
                  font-size: 24px;
                  font-weight: bold;
                  color: #EEFBFE;
                  margin: 0;
                  line-height: 24px;
                  margin-right: 5px;
                }
                span{
                  font-size: 14px;
                  font-weight: 600;
                  color: #CCCED3;
                  margin-right: 20px;
                }

              }
            }
          }
        }
        .video_surveillance{
          cursor: pointer;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          background: #062A52;
          img{
            width: 50px;
          }
          p{
            font-size: 20px;
            font-family: PingFang SC-Semibold, PingFang SC;
            font-weight: 600;
            color: #FFFFFF;
            writing-mode: vertical-lr;
            margin-top: 24px;
            letter-spacing:10px;
          }
        }
      }
    }
  }
}
.monitor_spot_btn{
  width: 32px;
  height: 20px;
  // overflow:hidden;
  // text-overflow:ellipsis;
  // white-space:nowrap;
  background: #00B42A;
  border-radius: 2px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  font-weight: 500;
  color: #FFFFFF;
}
.card_box_title {
  height: 31px;
  width: 100%;
  line-height: 31px;
  padding-left: 45px;
  padding-right: 10px;
  font-size: 15px;
  font-family: Alibaba PuHuiTi-Medium, Alibaba PuHuiTi;
  font-weight: 500;
  color: #b7cfff;
  display: flex;
  justify-content: space-between;
  .card_box_title_left{
    display: flex;
    align-items: center;
    .mark{
      cursor: pointer;
      display: flex;
      align-items: center;
      margin-left: 10px;
      ::v-deep .el-badge__content{
        margin-top: 12px;
      }
    }
  }
  .card_box_title_right{
    display: flex;
    .custom_date{
      font-size: 14px;
      font-family: PingFang SC-Medium, PingFang SC;
      font-weight: 500;
      color: #F4DB67;
    }
    .time_date{
      display: flex;
      margin-left: 20px;
      &_item{
        cursor: pointer;
        margin-right: 10px;
        color: #2181F4;
      }
      &_item_active{
        color: #F4DB67;
      }
      .my-date-picker{
        width: 10px;
        opacity: 0;
        position: absolute;
        right: 0;
      }
    }
    .vertical_bar{
      width: 1px;
      padding: 5px 0;
      div{
        background: rgba(255,255,255,0.2);
        height: 100%;
      }
    }
    .please_select{
      width: 80px;
      display: flex;
      position: relative;
      p{
        font-size: 14px;
        color: #FFFFFF;
        margin: 0;
      }
      .select_arrow {
        width: 32px;
        height: 32px;
        margin: auto 0;
        background: url("~@/assets/images/elevator/chevron-down.png") no-repeat;
        background-size: 100% 100%;
      }
    }
    ::v-deep .custom_select {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;

      .el-input {
        visibility: hidden;
      }

      .el-select-dropdown {
        position: absolute !important;
        width: 100% !important;
        min-width: 100px !important;
        left: 0 !important;
        // background-color: #122c41;
        background: url("~@/assets/images/elevator/select-bg.png") no-repeat;
        background-size: 100% 100%;
        border: none;
        box-shadow: none;

        .el-select-dropdown__list {
          padding: 6px 16px !important;
        }

        .el-select-dropdown__item {
          font-size: 14px;
          font-family: "Alibaba PuHuiTi-Regular", "Alibaba PuHuiTi";
          font-weight: 400;
          color: #a2b7d9;
        }

        .el-select-dropdown__item.hover,
        .el-select-dropdown__item:hover {
          background: url("~@/assets/images/elevator/select-li-bg.png") no-repeat;
          background-size: 100% 100%;
          font-size: 14px;
          font-family: "HarmonyOS Sans SC-Regular", "HarmonyOS Sans SC";
          font-weight: 400;
          color: #fff;
        }

        .popper__arrow {
          display: none;
        }
      }
    }
  }
  .details{
    color: #2181F4;
    font-size: 14px;
    cursor: pointer;
  }
}
.card_box_long_bg {
  background: url('~@/assets/images/elevator/card-title-long-bg.png') no-repeat;
  background-size: 100% 100%;
}
.card_box_special_long_bg {
  background: url('~@/assets/images/elevator/card-title-special-long-bg.png') no-repeat;
  background-size: 100% 100%;
}
.echart-null {
  margin: 0 auto;
  height: calc(100% - 31px);
  width: 50%;
  text-align: center;
  color: #8a8c8f;

  img {
    max-width: 100%;
    max-height: calc(100% - 20px);
  }

  div {
    font-size: 14px;
  }
}
</style>
