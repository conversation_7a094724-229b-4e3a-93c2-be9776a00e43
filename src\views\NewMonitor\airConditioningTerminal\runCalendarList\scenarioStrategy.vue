<template>
    <PageContainer>
        <div slot="header">
            <div class="searchForm">
                <div class="search-box">
                    <el-input v-model="searchForm.name" clearable filterable placeholder="请输入场景名称" class="ml-16"
                        @blur="event => searchForm.name = event.target.value.replace(/\s+/g, '')"></el-input>
                    <el-select v-model="searchForm.weatherId" placeholder="请选择天气类型" class="ml-16">
                        <el-option v-for="item in weatherList" :key="item.id" :label="item.weatherName"
                            :value="item.id">
                        </el-option>
                    </el-select>
                    <el-select v-model="searchForm.periodType" placeholder="请选择周期类型" class="ml-16">
                        <el-option v-for="item in periodType" :key="item.value" :label="item.name" :value="item.value">
                        </el-option>
                    </el-select>
                    <el-select v-model="searchForm.status" placeholder="请选择启用状态" class="ml-16">
                        <el-option v-for="item in startAndStop" :key="item.value" :label="item.name"
                            :value="item.value">
                        </el-option>
                    </el-select>
                    <div class="ml-16">
                        <el-button type="primary" @click="search">查询</el-button>
                        <el-button type="primary" plain @click="reset">重置</el-button>
                    </div>
                </div>
            </div>
        </div>
        <div slot="content" ref="contentRef" class="table-box">
            <div class="tableContainer">
                <el-button type="primary" style="margin-bottom: 12px;" @click="handleListEvent('add')">新建场景</el-button>
                <el-button :disabled="selectionList.length == 0" type="primary" style="font-size: 14px;"
                    @click="batchDel">批量删除</el-button>
                <el-button :disabled="selectionList.length == 0" type="primary" style="font-size: 14px;"
                    @click="batchStatus('startUsing')">批量启用</el-button>
                <el-button :disabled="selectionList.length == 0" type="primary" style="font-size: 14px;"
                    @click="batchStatus('blockUp')">批量停用</el-button>
                <el-table v-loading="tableLoading" height="calc(100% - 96px)" :data="tableData" border
                    row-key="cronTimeId" @selection-change="handleSelectionChange" :span-method="objectSpanMethod">
                    <el-table-column type="selection" width="55">
                    </el-table-column>
                    <el-table-column label="场景名称" prop="name" show-overflow-tooltip align="center">
                    </el-table-column>
                    <el-table-column label="天气类型" prop="weatherName" show-overflow-tooltip align="center">
                    </el-table-column>
                    <el-table-column label="周期类型" prop="periodType" show-overflow-tooltip align="center">
                        <div slot-scope="scope">{{ scope.row.periodType === "fixed" ? "固定" : "自定义" }}</div>
                    </el-table-column>
                    <el-table-column label="状态" prop="status" show-overflow-tooltip align="center">
                        <div slot-scope="scope">{{ scope.row.status === 0 ? "禁用" : "启用" }}</div>
                    </el-table-column>
                    <el-table-column label="时间表" prop="cronTimeName" show-overflow-tooltip
                        align="center"></el-table-column>
                    <el-table-column label="操作" show-overflow-tooltip align="center">
                        <template slot-scope="scope">
                            <el-button type="text" @click="handleListEvent('equipment', scope.row)">设备</el-button>
                            <el-button type="text" @click="handleListEvent('edit', scope.row)">编辑</el-button>
                            <el-button type="text" @click="handleListEvent('del', scope.row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination :current-page="pagination.page" :page-sizes="[15, 30, 50, 100]"
                    :page-size="pagination.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="pageTotal"
                    @size-change="handleSizeChange" @current-change="handleCurrentChange">
                </el-pagination>
            </div>
            <!-- 新建弹窗 -->
            <el-dialog v-dialogDrag :visible.sync="dialogVisible" :before-close="dialogClosed" :modal="true"
                :close-on-click-modal="false" title="新建场景" width="50%" custom-class="model-dialog">
                <div class="diaContent" style="padding: 10px">
                    <el-form ref="formInline" :model="formInline" label-width="100px" :rules="rules"
                        label-position="right">
                        <el-form-item label="场景名称：" prop="name" class="form-item">
                            <el-input v-model.trim="formInline.name" placeholder="请输入场景名称" :maxlength="20"></el-input>
                        </el-form-item>
                        <el-form-item label="天气类型：" prop="weather" class="form-item">
                            <el-select v-model="formInline.weather" placeholder="请选择天气类型" @change="weatherListChange">
                                <el-option v-for="item in weatherList" :key="item.id" :label="item.weatherName"
                                    :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="时间区间：" prop="startDate" class="form-item">
                            <el-date-picker v-model="timeList" type="daterange" unlink-panels range-separator="至"
                                start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd"
                                style="width:300px;" @change="time1" :picker-options="pickerOptions">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item label="周期类型：" prop="periodType" class="form-item">
                            <el-select ref="troopDeviceCode" v-model="formInline.periodType" placeholder="请选择周期类型"
                                clearable filterable @change="selectChange">
                                <el-option v-for="item in periodType" :key="item.value" :label="item.name"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                            <el-checkbox-group v-model="formInline.fixedPeriod.weekDaySelectionList"
                                @change="weekDayChange" v-if="formInline.periodType === 'fixed'">
                                <el-checkbox v-for="item in alarmLevelList " :key="item.identifier"
                                    :label="item.identifier">{{
                                        item.chineseName }}</el-checkbox>
                            </el-checkbox-group>
                            <el-date-picker v-if="formInline.periodType === 'custom'"
                                v-model="formInline.customPeriodDateList" :picker-options="pickerDates" type="dates"
                                placeholder="请选择自定义日期" value-format="yyyy-MM-dd" style="width:100%">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item label="时间表：" prop="timeScheduleList" class="inputWidth"
                            style="margin-bottom: 100px;">
                            <el-button type="primary" @click="addNewRow">+ 新增</el-button>
                            <div class="contentTable">
                                <div class="listTable">
                                    <el-table ref="materialTable" :data="formInline.timeScheduleList" border
                                        style="overflow: auto; margin-top: 10px" :cell-style="{ padding: '6px' }">
                                        <el-table-column label="时间类型" align="center">
                                            <template slot-scope="scope">
                                                <el-select v-model="scope.row.timeType" style="width: 100%;">
                                                    <el-option v-for="item in timeType" :key="item.value"
                                                        :label="item.label" :value="item.value"> </el-option>
                                                </el-select>
                                            </template>
                                        </el-table-column>
                                        <el-table-column label="时间点" align="center" width="350">
                                            <template slot-scope="scope">
                                                <el-time-picker v-if="scope.row.timeType === 'absolute'"
                                                    v-model="scope.row.timePoint" placeholder="请选择时间点"
                                                    value-format="HH:mm:ss" :popper-class="'datePicker'"
                                                    @change="timePickerChange" @blur="handleBlur">
                                                </el-time-picker>
                                                <div v-if="scope.row.timeType === 'relative'">
                                                    <el-select v-model.trim="scope.row.relativeTime" class='mr-5'
                                                        style="width:110px;">
                                                        <el-option v-for="item in relativeTime" :key="item.value"
                                                            :label="item.label" :value="item.value"> </el-option>
                                                    </el-select>
                                                    <el-select v-model.trim="scope.row.beforeOrAfter" class='mr-5'
                                                        style="width: 110px;">
                                                        <el-option v-for="item in beforeOrAfter" :key="item.value"
                                                            :label="item.label" :value="item.value"> </el-option>
                                                    </el-select>
                                                    <el-input placeholder="请输入" style="width: 80px;"
                                                        v-model.trim="scope.row.offset" type="number">
                                                        <span slot="suffix" class="el-input__icon ">分钟</span>
                                                    </el-input>
                                                </div>
                                            </template>
                                        </el-table-column>
                                        <el-table-column label="执行类型" align="center">
                                            <template slot-scope="scope">
                                                <el-select v-model="scope.row.executionType" style="width: 100%;">
                                                    <el-option v-for="item in executionType" :key="item.value"
                                                        :label="item.label" :value="item.value"> </el-option>
                                                </el-select>
                                            </template>
                                        </el-table-column>
                                        <el-table-column label="操作" width="70" align="center">
                                            <template slot-scope="scope">
                                                <el-button type="text" size="small"
                                                    @click="deleteNewRow(scope.$index, scope.row)"> 删除 </el-button>
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                </div>
                            </div>
                        </el-form-item>
                    </el-form>
                </div>
                <span slot="footer" class="dialog-footer">
                    <el-button type="primary" plain @click="dialogClosed">取 消</el-button>
                    <el-button type="primary" @click="submit('formInline')">确 定</el-button>
                </span>
            </el-dialog>
        </div>
    </PageContainer>
</template>
<script>
import { periodType, startAndStop, timeType, relativeTime, beforeOrAfter, executionType } from '@/util/newDict.js'
import moment from 'moment'
export default {
    name: 'scenarioStrategy',
    components: {
    },
    props: {
        systemCode: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            tableLoading: false,
            dialogVisible: false,
            tableData: [],
            timeListData: [],//选择时间
            selectionList: [], // 所有勾选项
            searchForm: {
                name: '', //场景名称
                weatherId: '',//天气类型
                periodType: '',//周期类型
                status: '',//状态
            },
            pagination: {
                pageSize: 15,
                page: 1
            },
            pageTotal: 0,
            formInline: {
                id: '',
                name: '',//场景名称
                weather: [],//天气类型
                startDate: '',//开始时间
                endDate: '',//结束时间
                periodType: "",//周期类型
                fixedPeriod: {
                    id: "",
                    sceneId: "",
                    fixedPeriodType: "week",
                    weekDaySelectionList: []
                }
                ,//固定周期
                customPeriodDateList: [],//自定义周期
                timeScheduleList: [],//时间表
            },
            timeList: [],
            pickerOptions: {
                disabledDate(time) {
                    const today = new Date();
                    today.setHours(0, 0, 0, 0); // 设置为当天的开始时间
                    return time.getTime() <= today.getTime(); // 禁用今天及之前的日期
                }
            },
            pickerDates: {
                disabledDate: (time) => {
                    return time.getTime() < new Date(this.timeList[0]).getTime() || time.getTime() > new Date(this.timeList[1]).getTime();
                }
            },
            startAndStop: startAndStop,
            timeType: timeType,
            periodType: periodType,
            relativeTime: relativeTime,
            beforeOrAfter: beforeOrAfter,
            executionType: executionType,
            weatherList: [],//天气
            weatherListData: null,//天气
            alarmLevelList: [],//固定周期
            weekDayList: [],//固定周期
            rules: {
                name: [{ required: true, message: '请输入场景名称', trigger: 'blur' }],
                weather: [{ required: true, message: '请选择天气', trigger: 'blur' }],
                startDate: [{ required: true, message: '请选择时间区间', trigger: 'blur' }],
                periodType: [{ required: true, message: '请选择周期类型', trigger: 'change' }],
                timeScheduleList: [{ required: true, message: '请增加时间表', trigger: 'change' }],
            },
        }
    },
    computed: {
        tableDataColumn() {
            const dataObject = {}
            this.tableData.forEach((item, index) => {
                const id = item.id
                if (dataObject[id]) {
                    dataObject[id].push(index)
                } else {
                    dataObject[id] = []
                    dataObject[id].push(index)
                }
            })
            return dataObject
        }
    },
    mounted() {
        this.getTableData()
        this.weatherList1()//天气字典
        this.cycleList()//周期字典
    },
    methods: {
        handleBlur() {
            console.log('失去焦点');
            if (document.activeElement instanceof HTMLElement) {
                document.activeElement.blur();
            }
        },
        timePickerChange(val) {
            console.log(val, 'vall');
            if (document.activeElement instanceof HTMLElement) {
                document.activeElement.blur();
            }
        },
        // 批量删除
        batchDel() {
            const idArr = this.selectionList.map(i => i.id)
            let obj = {
                ids: idArr
            }
            this.$confirm('删除后将无法恢复，是否确定删除？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消'
            })
                .then(() => {
                    this.tableLoading = true
                    this.$api.getQueryBatchDelete(obj).then((res) => {
                        if (res.code == '200') {
                            this.$message({
                                type: 'success',
                                message: '删除成功'
                            })
                            this.getTableData()
                            this.tableLoading = false
                        }
                    })
                })
                .catch(() => { })
        },
        // 批量启停用
        batchStatus(type) {
            const idArr = this.selectionList.map(i => i.id)
            let obj = {
                ids: idArr,
                enableStatus: null,//0停用1启用
            }
            if (type === 'startUsing') {
                obj.enableStatus = 1
            } else if (type === 'blockUp') {
                obj.enableStatus = 0
            }
            this.$api.getQueryBatchUpdateStatus(obj).then(res => {
                if (res.code == '200') {
                    this.$message.success(res.message)
                    this.getTableData()
                    this.tableLoading = false
                } else {
                    this.$message.error(res.message)
                }
            })
        },
        // 查询天气字典
        weatherList1() {
            this.$api.getSceneWeatherTypes().then(res => {
                if (res.code === '200') {
                    this.weatherList = res.data
                }
            })
        },
        weatherListChange(val) {
            console.log(this.formInline.weather, 'this.formInline.weather');

            this.weatherListData = this.weatherList.find(item => {
                return item.id === val
            })
        },
        // 查询周期字典
        cycleList() {
            this.$api.getSceneByIdOrCreateNew().then(res => {
                if (res.code === '200') {
                    this.alarmLevelList = res.data.fixedPeriod.weekDaySelectionList
                }

            })
        },
        weekDayChange(val) {
            this.weekDayList = this.alarmLevelList.filter(item => {
                return val.includes(item.identifier)
            })
        },
        selectChange(val) {
            this.formInline.fixedPeriod.weekDaySelectionList = []
            this.formInline.customPeriodDateList = []
        },
        // 新建场景时间表新增一行
        addNewRow() {
            this.formInline.timeScheduleList.push({
                timeType: 'relative',
                // timePoint: moment(new Date).format('HH:mm:ss'),
                timePoint: '',
                relativeTime: 'sunrise',
                beforeOrAfter: 'before',
                offset: '0',
                executionType: 'SIM',
            })
        },
        // 新建场删除行
        deleteNewRow(index, row) {
            if (index == 0) return
            this.formInline.timeScheduleList.splice(index, 1)
        },
        time1(val) {
            this.timeList = val
            this.formInline.startDate = this.timeList[0]
            this.formInline.endDate = this.timeList[1]
        },
        // 列表
        getTableData() {
            this.tableLoading = true
            let data = {
                ...this.pagination,
                ...this.searchForm,
                systemCode: this.systemCode
            }
            this.$api
                .getScenarioStrategyList(data)
                .then((res) => {
                    this.tableLoading = false
                    if (res.code == 200) {
                        this.tableData = res.data ? res.data.records : []
                        this.pageTotal = res.data ? res.data.total : 0
                    } else if (res.message) {
                        this.tableData = []
                        this.pagination.total = 0
                        this.$message.error(res.message)
                    }
                })
                .catch((err) => {
                    this.tableLoading = false
                })
        },
        // 列合并
        objectSpanMethod({ row, column, rowIndex, columnIndex }) {
            if (columnIndex === 0 || columnIndex === 1 || columnIndex === 2 || columnIndex === 3 || columnIndex === 4) { // 前4列相同数据合并
                if (rowIndex > 0 && row.id === this.tableData[rowIndex - 1].id) {
                    return {
                        rowspan: 0,
                        colspan: 0
                    }
                } else {
                    const id = row.id
                    const rows = this.tableDataColumn[id]
                    const length = rows.length
                    return {
                        rowspan: length,
                        colspan: 1
                    }
                }
            }
        },
        // table多选
        handleSelectionChange(val) {
            this.selectionList = val
        },
        handleSizeChange(val) {
            this.pagination.pageSize = val
            this.pagination.page = 1
            this.getTableData()
        },
        search() {
            this.pagination.page = 1
            this.getTableData()
        },
        reset() {
            this.searchForm = {
                name: '', //场景名称
                weatherId: '',//天气类型
                periodType: '',//周期类型
                status: '',//状态
            }
            this.pagination.page = 1
            this.pageTotal = 0
            this.getTableData()
        },
        handleCurrentChange(val) {
            this.pagination.page = val
            this.getTableData()
        },
        handleListEvent(type, row) {
            if (type == 'add') {
                this.formInline = {
                    id: '',
                    name: '',//场景名称
                    weather: [],//天气类型
                    startDate: '',//开始时间
                    endDate: '',//结束时间
                    periodType: "",//周期类型
                    fixedPeriod: {
                        id: "",
                        sceneId: "",
                        fixedPeriodType: "week",
                        weekDaySelectionList: []
                    }
                    ,//固定周期
                    customPeriodDateList: [],//自定义周期
                    timeScheduleList: [],//时间表
                }
                this.dialogVisible = true
                this.timeList = []
            }
            if (type == 'equipment') {
                let path = `${this.$route.meta.jumpAddress}/airScenarioStrategyDetails`
                this.$router.push({
                    path: path,
                    query: {
                        cronTimeId: row.cronTimeId,
                        systemCode: this.systemCode,
                    }
                })
            }
            if (type == 'edit') {
                let params = {
                    sceneId: row.id
                }
                this.timeList = []
                this.$api.getSceneByIdOrCreateNew(params).then((res) => {
                    if (res.code === '200') {
                        this.formInline = res.data
                        this.timeList.push(this.formInline.startDate, this.formInline.endDate)
                        this.formInline.weather = this.formInline.weather.id
                        this.weekDayList = this.formInline.fixedPeriod.weekDaySelectionList
                        this.weatherListData = this.formInline.weatherList[0]
                        if (this.formInline.fixedPeriod) {
                            this.formInline.fixedPeriod.weekDaySelectionList = this.formInline.fixedPeriod.weekDaySelectionList.map(item => {
                                return item.identifier
                            })
                        }
                    } else {
                        this.$message.error(res.msg)
                    }
                })
                this.dialogVisible = true
            }
            if (type == 'del') {
                this.$confirm('此操作将永久删除, 是否继续?', '提示', {
                    cancelButtonClass: 'el-button--primary is-plain',
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    let params = {
                        id: row.id
                    }
                    this.$api.getQueryDeleteScene(params).then((res) => {
                        if (res.code === '200') {
                            this.$message.success(res.message)
                            this.getTableData()
                        } else {
                            this.$message.error(res.message)
                        }
                    })
                })
            }
        },
        // 确定
        submit(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.formInline.weather = this.weatherListData
                    this.formInline.fixedPeriod.fixedPeriodType = 'week'
                    this.formInline.fixedPeriod.weekDaySelectionList = this.weekDayList
                    for (let item of this.formInline.timeScheduleList) {
                        if (item.timeType === 'absolute' && (item.timePoint === '' || item.timePoint === null)) {
                            this.$message.error('请选择时间点');
                            return; // 直接返回
                        }
                        if (item.timeType === 'relative') {
                            item.timePoint = ''
                        }
                    }
                    let data = {
                        ...this.formInline,
                    }
                    const isIdValid = this.formInline.id && this.formInline.id.toString().trim() !== '';
                    const apiMethod = isIdValid ? this.$api.getupdateScene : this.$api.getaddScene;
                    apiMethod(data).then((res) => {
                        if (res.code === '200') {
                            this.dialogVisible = false
                            this.$refs.formInline.resetFields()
                            this.$message.success(res.message)
                            this.getTableData()
                        } else {
                            this.$message.error(res.message)
                        }
                    })

                } else {
                    return false
                }
            })

        },
        // 取消
        dialogClosed() {
            this.dialogVisible = false
            this.$refs.formInline.resetFields()
        },


    }
}
</script>
<style>
.el-time-panel__footer {
    display: none !important;
}
</style>
<style lang="scss" scoped>
.searchForm {
    display: flex;
    height: 80px;
    padding: 0 16px;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .el-input {
        width: 200px;
        margin-left: 16px;
    }

    >div {
        margin-right: 20px;
    }
}

.search-box {
    display: flex;
    align-items: center;
}

.container-content>div {
    height: 100%;
    margin-top: 16px;
}

.el-table {
    margin-bottom: 12px;
}

::v-deep .el-table__row:hover {
    cursor: pointer;
}

.table-box {
    background-color: #fff;
    height: 100%;
    padding: 16px;

    .tableContainer {
        height: 100%;

        ::v-deep .el-table .el-table__body-wrapper td.el-table__cell {
            font-size: 14px;
            border-right: 1px solid #EBEEF5 !important;
        }

        .el-table {
            height: calc(100% - 96px) !important;

        }
    }
}

.diaContent {
    width: 100%;
    max-height: 550px !important;
    overflow: auto;
    background-color: #fff !important;

    .el-input,
    .el-select,
    .el-cascader {
        width: 300px;
    }
}

.form-item {
    display: inline-block;
    margin-right: 20px;
}

.inputWidth {
    width: 820px;
}

.ml-16 {
    margin-left: 16px;
}

.mr-5 {
    margin-right: 5px;
}

.dialog .el-dialog {
    width: 60% !important;
}
</style>
