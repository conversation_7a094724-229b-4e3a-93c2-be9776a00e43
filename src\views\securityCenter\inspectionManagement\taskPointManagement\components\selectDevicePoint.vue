<template>
  <el-dialog class="sino-dialog-device" :title="title" :visible.sync="dialogVisibleWz" @close="closeDialog">
    <div class="top-search" style="margin-bottom: 10px;">
      分类：
      <el-cascader
        ref="cascaderArea"
        v-model="classificationId"
        style="height: 30px; margin-bottom: 10px; margin-top: 10px;"
        placeholder="请选择分类"
        :props="classArea"
        :options="classificationList"
        clearable
        @change="selectChange"
      ></el-cascader>
      <el-input v-model="filters.id" placeholder="编码/名称/型号" class="sino_sdcp_input" style="margin: 0 10px; width: 200px; height: 30px;" maxlength="50"></el-input>
      <el-select v-model="filters.supplierId" placeholder="供应商" style="width: 200px; height: 30px; margin-right: 10px;">
        <el-option v-for="item in supplierIdArr" :key="item.id" :label="item.unitsName" :value="item.id"></el-option>
      </el-select>
      <el-button type="primary" plain @click="_resetCondition">重置</el-button>
      <el-button type="primary" @click="_searchByCondition">查询</el-button>
    </div>
    <div class="hasSelected">
      <div style="color: #5b5b5b; font-weight: 600;">已选择：</div>
      <span v-if="!multipleSelection.length">暂无</span>
      <div v-for="(item, index) in multipleSelection" v-else :key="index" style="color: #5188fc;">
        <span>{{ item.assetsName }}</span>
        <span v-show="multipleSelection.length > 1 && index !== multipleSelection.length - 1">、</span>
      </div>
    </div>
    <div class="table-list">
      <el-table
        ref="materialTable"
        v-loading="dialogtableLoading"
        :data="tableWz"
        :border="true"
        height="320px"
        
        stripe
        row-key="id"
        :cell-style="{ padding: '8px' }"
        style="overflow: auto;"
        :header-cell-style="$tools.setHeaderCell(3)"
        class="table"
        :header-cell-class-name="cellClass"
        @selection-change="handleSelectionChangeDialog"
      >
        <el-table-column :reserve-selection="true" type="selection" width="55"></el-table-column>
        <el-table-column type="index" label="序号" width="65">
          <template slot-scope="scope">
            <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="assetsNumber" show-overflow-tooltip label="资产编码" width="120"></el-table-column>
        <el-table-column prop="assetsName" show-overflow-tooltip label="资产名称" width="120"></el-table-column>
        <el-table-column prop="model" show-overflow-tooltip label="规格型号" width="120"></el-table-column>
        <el-table-column prop="useDepartmentName" show-overflow-tooltip label="使用科室" width="120"></el-table-column>
        <el-table-column prop="storageLocation" show-overflow-tooltip label="存放位置" width="120"></el-table-column>
        <el-table-column prop="buyDate" show-overflow-tooltip label="启用日期" width="120"></el-table-column>
        <el-table-column prop="money" show-overflow-tooltip label="资产原值(元)" width="160"></el-table-column>
        <el-table-column prop="serviceLife" show-overflow-tooltip label="使用年限（月）" width="160"></el-table-column>
        <el-table-column prop="warranties" show-overflow-tooltip label="维保到期" width="120"></el-table-column>
        <el-table-column prop="supplierName" show-overflow-tooltip label="供应商" width="120"></el-table-column>
        <el-table-column prop="manufacturerName" show-overflow-tooltip label="生产厂家" width="120"></el-table-column>
        <el-table-column prop="brandName" show-overflow-tooltip label="品牌" width="120"></el-table-column>
        <el-table-column prop="remarks" show-overflow-tooltip label="备注" width="120"></el-table-column>
      </el-table>
      <el-pagination
        :current-page="paginationData.currentPage"
        :page-sizes="[15, 20, 30, 50]"
        :page-size="paginationData.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="paginationData.total"
        class="selectLocation"
        style="margin-bottom: 20px;"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
    <div class="table-page my_page"></div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="sureWz">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { transData } from '@/util'
export default {
  name: '',
  props: {
    title: {
      type: String,
      default: '选择设备'
    },
    dialogVisibleWz: {
      type: Boolean,
      default: false
    },
    flag: {
      type: String,
      default: 'maintainFlag'
    },
    deviceId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogFiltersSelectArr: [],
      workTypeCodeList: [],
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      props: {
        label: 'gridName',
        value: 'id',
        children: 'children'
      },
      multipleSelection: [],
      tableWz: [],
      dialogtableLoading: false,
      dialogFilters: {
        locationPointCode: '',
        locationPointName: ''
      },
      buttonLoading: false,
      tableSort: '',
      tableOrderBy: '',
      LOGINDATA: '',
      classificationId: [],
      filters: {
        id: '',
        supplierId: ''
      },
      classArea: {
        label: 'className',
        value: 'id',
        children: 'children',
        checkStrictly: true
      },
      classificationList: [],
      supplierIdArr: [],
      deviceTypeId: ''
    }
  },
  watch: {
    deviceId(newVal) {
      console.log(newVal)
      this.deviceTypeId = newVal
    }
  },
  created() {
    // 获取登录信息
    this.LOGINDATA = JSON.parse(sessionStorage.getItem('LOGINDATA'))
    // 获取设备列表
    this._findEquipmentList()
  },
  mounted() {
    this.init()
  },
  methods: {
    cellClass(row) {
      if (row.columnIndex === 0) {
        return 'DisableSelection'
      }
    },
    init() {
      // 资产分类
      this.$api
        .assetsClassListData({
          currentPage: 1,
          pageSize: 10000
        })
        .then((res) => {
          if (res.code == '200') {
            this.classificationList = transData(res.data.list, 'id', 'parentId', 'children')
            this.classificationList.forEach((item) => {
              item.disabled = true
            })
          }
        })
      // 供应商列表
      this.$api
        .getSupplierList({
          type: 1,
          dataType: 1
        })
        .then((res) => {
          this.tableLoading = false
          if (res.code == 200) {
            this.supplierIdArr = res.data.supplierList
          } else {
          }
        })
    },
    selectChange() {
      this._findEquipmentList()
    },
    // 查询表格
    _findEquipmentList() {
      this.tableLoading = true
      let obj = this.LOGINDATA
      let _self = this
      const { dialogFilters, paginationData, filters } = this
      let data = {
        compound: filters.id,
        supplierId: filters.supplierId,
        competentDeptTypeCode: this.classificationId[this.classificationId.length - 1],
        pageSize: paginationData.pageSize,
        currentPage: paginationData.currentPage,
        from: 'blsj',
        orderBy: '',
        sort: 'desc' // 排序
      }
      if (this.$route.query.isMaintenance == 0) {
        data.isMaintenance = 1
      } else if (this.$route.query.isAntiepidemic == 0) {
        data.isAntiepidemic = 1
      } else if (this.$route.query.isMetering == 0) {
        data.isMetering = 1
      }
      // 设备列表
      this.$api.findEquipmentList(data).then((res) => {
        this.tableLoading = false
        if (res.code == 200) {
          this.tableWz = res.data.list
          if (this.tableWz) {
            this.tableWz.forEach((val, index) => {
              if (val.id == this.deviceId) {
                _self.$refs.materialTable.toggleRowSelection(val, true)
              }
            })
          }
          this.paginationData.total = parseInt(res.data.sum)
          if (res.data.list.length == 0) {
            this.emptyText = '未查到任何记录，请放大查询条件试试！'
          }
        }
        this.tableLoading = false
      })
    },
    // 条件查询
    _searchByCondition() {
      this.paginationData.currentPage = 1
      this._findEquipmentList()
    },
    // 重置查询条件
    _resetCondition() {
      this.paginationData = {
        currentPage: 1,
        pageSize: 15,
        total: 0
      }
      this.filters = {
        id: '',
        supplierId: ''
      }
      this.classificationId = ''
      this._findEquipmentList()
    },
    handleSelectionChangeDialog(val) {
      this.multipleSelection = val
      if (val.length > 1) {
        // 将复选框多选改为单选
        this.$refs.materialTable.clearSelection()
        this.$refs.materialTable.toggleRowSelection(val.pop())
      }
    },
    closeDialog() {
      this.dialogFilters = {
        locationPointCode: '',
        locationPointName: ''
      }
      this.$emit('closeDialog')
    },
    // 点击确定
    sureWz() {
      if (this.multipleSelection.length > 1) {
        this.$message.error('最多只能选择一个设备!')
        return
      }
      this.$emit('multipleSelection', this.multipleSelection)
      this.closeDialog()
    },
    handleSizeChange(val) {
      this.paginationData.currentPage = 1
      this.paginationData.pageSize = val
      this._findEquipmentList()
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this._findEquipmentList()
    }
  }
}
</script>
<style lang="scss" scoped>
.sino-dialog-device {
  .hasSelected {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    min-height: 20px;
    max-height: 80px;
    height: auto;
    overflow-y: auto;
  }

  thead :deep(.el-checkbox__inner) {
    display: none !important;
  }

  .table :deep(.DisableSelection > .cell) {
    display: none !important;
  }

  :deep(.el-dialog) {
    width: 1000px !important;
    height: 600px !important;

    .el-dialog__footer {
      padding: 30px 20px 20px;
    }
  }

  .selectLocation :deep(.el-input__inner) {
    height: 25px !important;
    line-height: 25px !important;
  }

  :deep(.el-dialog__body) {
    height: 470px;

    .el-dialog__footer {
      padding: 0 20px;
    }

    .el-input__inner {
      height: 30px !important;
    }
  }
}
</style>