<template>
  <div>
    <el-table
      v-if="type == '1' && isActivities"
      :data="tableData"
      style="width: 100%;"
      border
      :header-cell-style="{
        textAlign: 'center',
        lineHeight: '35px',
        padding: '0'
      }"
    >
      <el-table-column type="index" width="50" label="序号"></el-table-column>
      <el-table-column label="作业步骤" width="180" property="name"> </el-table-column>
      <el-table-column label="危险源或潜事件" width="180" property="date"> </el-table-column>
      <el-table-column label="可能发生的事故类型及后果" width="190" property="address"> </el-table-column>
      <el-table-column label="现有控制措施">
        <el-table-column label="工程技术措施" width="150"> </el-table-column>
        <el-table-column label="管理措施" width="150"> </el-table-column>
        <el-table-column label="培训教育措施" width="150"> </el-table-column>
        <el-table-column label="个体防护措施" width="150"> </el-table-column>
        <el-table-column label="应急措施" width="150"> </el-table-column>
      </el-table-column>
      <el-table-column label="建议改进(新增)措施" width="150"> </el-table-column>
      <el-table-column label="操作" width="80">
        <template slot-scope="">
          <el-button type="danger" size="mini">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-table
      v-if="type == '1' && !isActivities"
      :data="tableData"
      style="width: 100%;"
      border
      :header-cell-style="{
        textAlign: 'center',
        lineHeight: '35px',
        padding: '0'
      }"
    >
      <el-table-column type="index" width="50" label="序号"></el-table-column>
      <el-table-column label="检查项目" width="180" property="name"> </el-table-column>
      <el-table-column label="标准" width="180" property="date"> </el-table-column>
      <el-table-column label="不符合标准情况及后果" width="190" property="address"> </el-table-column>
      <el-table-column label="现有控制措施">
        <el-table-column label="工程技术措施" width="150"> </el-table-column>
        <el-table-column label="管理措施" width="150"> </el-table-column>
        <el-table-column label="培训教育措施" width="150"> </el-table-column>
        <el-table-column label="个体防护措施" width="150"> </el-table-column>
        <el-table-column label="应急措施" width="150"> </el-table-column>
      </el-table-column>
      <el-table-column label="建议改进(新增)措施" width="150"> </el-table-column>
      <el-table-column label="操作" width="80">
        <template slot-scope="">
          <el-button type="danger" size="mini">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-table
      v-if="type == '2'"
      :data="tableData"
      style="width: 100%;"
      border
      :header-cell-style="{
        textAlign: 'center',
        lineHeight: '35px',
        padding: '0'
      }"
    >
      <el-table-column type="index" width="50" label="序号"></el-table-column>
      <el-table-column label="主要风险因素" width="300" property="name"> </el-table-column>
      <el-table-column label="安全操作要点" width="300" property="date"> </el-table-column>
      <el-table-column label="主要风险管控措施" width="300" property="address"> </el-table-column>
      <el-table-column label="应急处置措施" width="300"> </el-table-column>
      <el-table-column label="操作" width="80">
        <template slot-scope="">
          <el-button type="danger" size="mini">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'TableTemplate',
  props: ['type', 'isActivities'],
  data() {
    return {
      tableData: []
    }
  }
}
</script>

<style lang="scss" scoped>
.el-table {
  margin: 12px 0;
}
</style>
