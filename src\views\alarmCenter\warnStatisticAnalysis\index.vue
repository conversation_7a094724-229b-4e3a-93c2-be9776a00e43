<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-06-01 10:25:46
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2024-03-23 16:20:38
 * @FilePath: \ihcrs_pc\src\views\alarmCenter\warnStatisticAnalysis\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <PageContainer>
    <div slot="content" ref="contentBox" class="warn-statistic-content" style="height: 100%">
      <!-- <scaleScreen :width="parentBox.width" :height="parentBox.height"> -->
      <el-row :gutter="24" style="height: 100%">
        <el-col :xs="24" :md="24" :lg="12">
          <ContentCard v-loading="loading.alarmStatistics" title="报警统计（单）">
            <p slot="title-right" class="viewDetails" @click="viewDetailsDialog('alarmStatisticsDialogShow')">查看</p>
            <div slot="content" style="width: 100%; height: 100%; display: flex; padding-top: 20px">
              <div class="cardContent-left">
                <div v-for="item in alarmStatisticsList" :key="item.title" class="left-item">
                  <p class="item-title item-btn" :class="{ other: item.title === '报警统计' }" @click="goAlarmDetail(item.alarmState)">
                    <span>{{ item.title }}</span>
                    <span class="check" v-if="item.title != '报警统计'"><i class="el-icon-arrow-right"></i></span>
                  </p>
                  <p class="item-value">{{ item.value || 0 }}<span>单</span></p>
                  <img class="item-icon" v-if="item.title === '报警统计'" :src="item.icon" :alt="item.title" />
                </div>
              </div>
              <echarts ref="alarmStatistics" domId="alarmStatistics" width="40%" height="100%" />
            </div>
          </ContentCard>
          <ContentCard v-loading="loading.alarmAnalysis" title="报警新增趋势（单）">
            <p slot="title-right" class="viewDetails" @click="viewDetailsDialog('alarmAnalysisDialogShow')">查看</p>
            <div slot="content" style="width: 100%; height: 100%">
              <div class="add-analysis">
                <div v-if="activeDataFilter == 1">
                  <el-date-picker
                    v-model="dataRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    :clearable="false"
                    @change="changeDateRange"
                  />
                </div>
                <div v-if="activeDataFilter == 2" class="datePickerClass">
                  <el-date-picker v-model="startDate" type="month" placeholder="选择月" format="yyyy-MM" value-format="yyyy-MM" @change="changeDateRange"> </el-date-picker>
                  至
                  <el-date-picker v-model="endDate" type="month" placeholder="选择月" format="yyyy-MM" value-format="yyyy-MM" @change="changeDateRange"> </el-date-picker>
                </div>
                <div v-if="activeDataFilter == 3" class="datePickerClass">
                  <el-date-picker v-model="startDate" type="year" placeholder="选择年" format="yyyy" value-format="yyyy" @change="changeDateRange"> </el-date-picker>
                  至
                  <el-date-picker v-model="endDate" type="year" placeholder="选择年" format="yyyy" value-format="yyyy" @change="changeDateRange"> </el-date-picker>
                </div>
                <div class="date-type-btn">
                  <div
                    v-for="(item, index) in dataFilterList"
                    :key="index"
                    :class="{ 'data-filter': true, 'data-filter-active': item.value === activeDataFilter }"
                    @click="changeDateType(item)"
                  >
                    {{ item.label }}
                  </div>
                </div>
              </div>
              <echarts ref="alarmAnalysis" domId="alarmAnalysis" width="100%" height="calc(100% - 40px)" />
            </div>
          </ContentCard>
        </el-col>
        <el-col :xs="24" :md="24" :lg="12" style="padding-left: 0 !important">
          <div class="add-warn">
            <div class="added-alarms-tabs">
              <div
                v-for="(item, index) in alarmsTabsData"
                :key="index"
                :class="{ 'alarms-tab': true, 'alarms-tab-active': activeAlarmsTab === item.type }"
                @click="changeAlarmsTab(item.type)"
              >
                <div class="tabs-top-title">{{ item.title }}</div>
                <div class="tabs-bottom-div">
                  <div class="tabs-bottom-div-count">{{ item.count }}</div>
                  <div v-if="item.contrastType || item.contrastType == 0" class="tabs-bottom-div-contrst">
                    <div>{{ item.contrastTitle }}</div>
                    <div>
                      <span>{{ item.contrastPercent }}</span>
                      <img v-if="item.contrastType == '1'" src="@/assets/images/service/down.png" />
                      <img v-else src="@/assets/images/service/up.png" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="added-alarms-charts">
              <div class="flex-box" @click="viewDetailsDialog('alarmTableDialogShow')">
                <ContentCard v-loading="loading.alarmUrgency" title="报警紧急程度" :cstyle="{ width: '30%', height: '100%', background: '#FAF9FC', 'border-radius': '4px' }">
                  <div slot="content" style="width: 100%; height: 100%">
                    <echarts ref="alarmUrgency" domId="alarmUrgency" width="100%" height="100%" />
                  </div>
                </ContentCard>
                <ContentCard
                  v-loading="loading.alarmEventType"
                  title="报警事件类型Top10"
                  :cstyle="{ width: 'calc(70% - 16px)', height: '100%', background: '#FAF9FC', 'border-radius': '4px' }"
                >
                  <div slot="content" style="width: 100%; height: 100%">
                    <echarts ref="alarmEventType" domId="alarmEventType" :isTrigger="true" :xyType="'yAxis'" width="100%" height="100%" />
                  </div>
                </ContentCard>
              </div>
              <div class="flex-box">
                <ContentCard v-loading="loading.alarmFault" title="故障数" :cstyle="{ width: '30%', height: '100%', background: '#FAF9FC', 'border-radius': '4px' }">
                  <div slot="content" style="width: 100%; height: 100%" @click="viewDetailsDialog('alarmTableDialogShow')">
                    <echarts ref="alarmFault" domId="alarmFault" width="100%" height="100%" />
                  </div>
                </ContentCard>
                <ContentCard
                  v-loading="loading.alarmSource"
                  title="报警来源"
                  :cstyle="{ width: 'calc(70% - 16px)', height: '100%', background: '#FAF9FC', 'border-radius': '4px' }"
                >
                  <div slot="content" style="width: 100%; height: 100%">
                    <echarts
                      ref="alarmSource"
                      domId="alarmSource"
                      width="100%"
                      height="100%"
                      :isTrigger="true"
                      :xyType="'xAxis'"
                      :showWord="4"
                      @onClickChart="viewDetailsDialog('alarmTableDialogShow')"
                    />
                  </div>
                </ContentCard>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
      <alarmStatisticsDialog v-if="alarmStatisticsDialogShow" projectCode="" :visible.sync="alarmStatisticsDialogShow" />
      <alarmAnalysisDialog v-if="alarmAnalysisDialogShow" :visible.sync="alarmAnalysisDialogShow" :activeDataFilter="activeDataFilter" :dataRange="dataRange" />
      <alarmTableDialog v-if="alarmTableDialogShow" :visible.sync="alarmTableDialogShow" :dateType="activeAlarmsTab" />
      <!-- </scaleScreen> -->
    </div>
  </PageContainer>
</template>
<script>
import moment from 'moment'
import * as echarts from 'echarts'
import alarmStatisticsDialog from '../components/alarmStatisticsDialog/index.vue'
import alarmAnalysisDialog from '../components/alarmAnalysisDialog/index.vue'
import alarmTableDialog from '../components/alarmTableDialog/index.vue'
import alarmStatistics from '@/assets/images/monitor/alarm-statistics.png'
import alarmAlarm from '@/assets/images/monitor/alarm-pending.png'
import alarmDoing from '@/assets/images/monitor/alarm-doing.png'
import alarmEnd from '@/assets/images/monitor/alarmEnd.png'
export default {
  name: 'warnStatisticAnalysis',
  components: {
    alarmStatisticsDialog,
    alarmAnalysisDialog,
    alarmTableDialog
  },
  data() {
    return {
      parentBox: {
        width: 1920,
        height: 1080
      },
      alarmTableDialogShow: false, // 报警列表弹窗
      alarmAnalysisDialogShow: false, // 报警趋势弹窗
      alarmStatisticsDialogShow: false, // 报警统计弹窗
      alarmStatisticsList: [
        // 报警统计
        {
          title: '报警统计',
          icon: alarmStatistics,
          value: 0
        },
        {
          title: '未处理',
          icon: alarmAlarm,
          value: 0,
          alarmState: '0'
        },
        {
          title: '处理中',
          icon: alarmDoing,
          value: 0,
          alarmState: '1'
        },
        {
          title: '已处理',
          icon: alarmEnd,
          value: 0,
          alarmState: '2'
        }
      ],
      dataRange: [], // 报警新增趋势时间选择器,
      startDate: '', //开始时间
      endDate: '', //结束时间
      activeDataFilter: '1', // 报警新增趋势日期类型选择期
      dataFilterList: [
        // 日期类型选取列表
        {
          label: '按日',
          value: '1'
        },
        {
          label: '按月',
          value: '2'
        },
        {
          label: '按年',
          value: '3'
        }
      ],
      activeAlarmsTab: '',
      alarmsTabsData: [
        // 新增报警数量tabs
        {
          title: '本日新增',
          type: '0',
          count: 0,
          contrastTitle: '环比',
          contrastPercent: '',
          contrastType: ''
        },
        {
          title: '本周新增',
          type: '1',
          count: 0,
          contrastTitle: '环比',
          contrastPercent: '',
          contrastType: ''
        },
        {
          title: '本月新增',
          type: '2',
          count: 0,
          contrastTitle: '环比',
          contrastPercent: '',
          contrastType: ''
        },
        {
          title: '本年新增',
          type: '3',
          count: 0
        }
      ],
      loading: {
        alarmStatistics: false,
        alarmAnalysis: false,
        alarmUrgency: false,
        alarmEventType: false,
        alarmFault: false,
        alarmSource: false
      }
    }
  },
  mounted() {
    this.parentBox.width = this.$refs.contentBox.offsetWidth
    this.parentBox.height = this.$refs.contentBox.offsetHeight
    this.$nextTick(() => {
      // this.$refs.contentBox.style.height = '100%'
      this.init()
    })
  },
  methods: {
    init() {
      this.dataRange = [moment().subtract(5, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
      this.changeAlarmsTab('0')
      this.getWarnAddAnalysis()
      this.getPoliceInfo()
      this.getAirRunPolice()
    },
    // 改变选择日期
    changeDateRange() {
      let startDateTime = new Date(this.startDate).getTime()
      let endDateTime = new Date(this.endDate).getTime()
      if (startDateTime > endDateTime) {
        this.$message.error('开始时间不能大于结束时间')
        this.startDate = ''
        return false
      }
      if (this.activeDataFilter == '2') {
        this.dataRange = [moment(this.startDate).startOf('month').format('YYYY-MM-DD'), moment(this.endDate).endOf('month').format('YYYY-MM-DD')]
      } else if (this.activeDataFilter == '3') {
        this.dataRange = [moment(this.startDate).startOf('year').format('YYYY-MM-DD'), moment(this.endDate).endOf('year').format('YYYY-MM-DD')]
      }
      this.getWarnAddAnalysis()
    },
    // 改变选择日期类型
    changeDateType(item) {
      this.activeDataFilter = item.value
      if (this.activeDataFilter == '2') {
        this.startDate = moment().subtract(5, 'months').format('YYYY-MM-DD')
        this.endDate = moment().format('YYYY-MM-DD')
        this.dataRange = [moment().subtract(5, 'months').startOf('month').format('YYYY-MM-DD'), moment().endOf('month').format('YYYY-MM-DD')]
      } else if (this.activeDataFilter == '3') {
        this.startDate = moment().subtract(2, 'years').format('YYYY-MM-DD')
        this.endDate = moment().format('YYYY-MM-DD')
        this.dataRange = [moment().subtract(2, 'years').startOf('year').format('YYYY-MM-DD'), moment().endOf('year').format('YYYY-MM-DD')]
      } else {
        this.dataRange = [moment().subtract(5, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
      }
      this.getWarnAddAnalysis()
    },
    // 改变选择报警日期类型
    changeAlarmsTab(type) {
      this.activeAlarmsTab = type
      const params = {
        timeType: type
      }
      this.getPoliceUrgency(params)
      this.getAlarmTopCount(params)
      this.getAlarmSourceCount(params)
      this.getAlarmFaultsCount(params)
    },
    // 获取报警数量数据
    getPoliceInfo() {
      this.$api.getPoliceInfoByApp().then((res) => {
        this.policeInfo = res.data
        this.alarmsTabsData.map((e) => {
          const countInfo = res.data.countInfo
          let findData = countInfo.find((item) => item.timeType == e.type)
          e.count = findData.count
          e.contrastPercent = findData.ringRatio
          e.contrastType = findData.ringRatioType
        })
      })
    },
    // 获取报警紧急程度
    getPoliceUrgency(params) {
      this.$api.getPolicePieByApp(params).then((res) => {
        const resData = res.data
        // 	报警级别(0:通知 1:一般 2:紧急 3:重要)
        let polLevelList = {
          3: { name: '重要' },
          2: { name: '紧急' },
          1: { name: '一般' },
          0: { name: '通知' }
        }
        let sortData = resData.sort((a, b) => b.alarmLevel - a.alarmLevel)
        let data = []
        sortData.forEach((ele) => {
          let obj = {
            name: polLevelList[ele.alarmLevel].name,
            value: ele.alarmCount
          }
          data.push(obj)
        })
        this.$refs.alarmUrgency.init(this.getPoliceUrgencyEchats(data))
      })
    },
    // 获取报警事件类型top10
    getAlarmTopCount(params) {
      this.$api.getAlarmTopCount(params).then((res) => {
        const resData = res.data
        let topData = resData.sort((a, b) => a.count - b.count)
        this.$refs.alarmEventType.init(this.getAlarmTopCountEchats(topData))
      })
    },
    // 获取报警来源
    getAlarmSourceCount(params) {
      this.$api.getAlarmSourceCount(params).then((res) => {
        const resData = res.data
        // const resData = [...res.data, ...res.data, ...res.data]
        this.$refs.alarmSource.init(this.getAlarmSourceCountEchats(resData))
      })
    },
    // 获取报警故障数
    getAlarmFaultsCount(params) {
      this.$api.selectFaultPie(params).then((res) => {
        const resData = res.data?.map((e) => {
          return {
            name: e.projectName,
            value: e.count
          }
        })
        this.$refs.alarmFault.init(this.getPoliceFaultCountEchats(resData))
      })
      // const resData = []
    },
    // 报警新增趋势统计
    getWarnAddAnalysis() {
      const params = {
        startTime: this.dataRange[0],
        endTime: this.dataRange[1],
        hourDayOrMouth: this.activeDataFilter
      }
      this.loading.alarmAnalysis = true
      this.$api.getAlarmTrendPc(params).then((res) => {
        this.loading.alarmAnalysis = false
        this.$refs.alarmAnalysis.init(this.setLineChart(res.data || []))
      })
    },
    // 查看详情
    viewDetailsDialog(type) {
      this[type] = true
    },
    // 报警统计
    getAirRunPolice() {
      let params = {
        startTime: '',
        endTime: '',
        projectCode: '',
        timeType: ''
      }
      let newArr = []
      this.loading.alarmStatistics = true
      this.$api
        .selectDiffProjectCodeAlarm(params)
        .then((res) => {
          this.loading.alarmStatistics = false
          if (res.code == 200) {
            ;(res.data?.policeList ?? []).forEach((item) => {
              newArr.push({
                name: item.entityTypeName,
                value: item.count
              })
            })
            this.alarmStatisticsList[0].value = res.data?.total ?? 0
            this.alarmStatisticsList[1].value = res.data?.noDealCount ?? 0
            this.alarmStatisticsList[2].value = res.data?.isDealCount ?? 0
            this.alarmStatisticsList[3].value = res.data?.dealCount ?? 0
            this.$refs.alarmStatistics.init(this.setPieChart(newArr))
          } else {
            this.$refs.alarmStatistics.init(this.setPieChart())
          }
        })
        .catch(() => {
          this.loading.alarmStatistics = false
          this.$refs.alarmStatistics.init(this.setPieChart())
        })
    },
    setPieChart(data) {
      let option
      var colors = ['#5e81ec', '#ffc855', '#98e79b', '#00d695', '#00b29a', '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc']
      var total = data.reduce((p, v) => {
        return p + v.value
      }, 0)
      if (data.length) {
        option = {
          title: {
            text: '监测项类型报警占比',
            left: 'center',
            textStyle: {
              fontSize: 15,
              fontWeight: 500
            }
          },
          legend: {
            type: 'scroll',
            bottom: 20,
            left: 'center',
            orient: 'vertical',
            height: '35%',
            itemHeight: 8,
            itemWidth: 8,
            textStyle: {
              fontSize: 14,
              color: '#000'
            },
            data: data.map((item) => item.name),
            formatter: (name) => {
              let item = data.find((v) => v.name == name)
              return `${name}  ${((item.value / total) * 100).toFixed(2)}%  ${item.value}单`
            }
          },
          tooltip: {
            trigger: 'item',
            formatter: '{b} : {c} ({d}%)',
            confine: true
          },
          color: colors,
          calculable: true,
          series: [
            {
              type: 'pie',
              radius: ['30%', '45%'],
              center: ['50%', '30%'],
              roseType: 'radius',
              label: {
                show: false
              },
              labelLine: {
                length: 1,
                length2: 20,
                smooth: true
              },
              data: data
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      return option
    },
    // 折线图
    setLineChart(data = []) {
      function colorAdd(transparency) {
        return `rgba(63, 99, 211, ${transparency})`
      }
      let colorHalf = colorAdd('.5')
      let colorZero = colorAdd('0')
      let option
      if (data.length) {
        option = {
          color: colorHalf,
          tooltip: {
            trigger: 'axis'
          },
          legend: {
            show: false
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: data.map((x) => {
              return x.time
            })
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: '{value}'
            }
          },
          series: this.dataArr(data, colorHalf, colorZero),
          dataZoom: [
            {
              height: 20, // 时间滚动条的高度
              type: 'inside', // type的作用是指定数据缩放的类型，slider表示使用滑动条进行缩放，inside表示使用鼠标滚轮进行缩放。
              xAxisIndex: 0, // 作用在x轴的下标（因为x轴可以有多个）
              filterMode: 'filter', // 间滚动条的过滤模式,'filter'表示滑动时间条时会直接过滤掉不在时间范围内的数据，'weakFilter'表示滑动时间条时会逐渐过滤掉不在时间范围内的数据。
              start: 0, // 默认开始位置（百分比）
              end: 100 // 默认结束位置（百分比）
            },
            {
              type: 'inside',
              xAxisIndex: 0,
              filterMode: 'filter',
              start: 0,
              end: 100
            }
          ],
          grid: {
            // 让图表占满容器
            top: '5%',
            left: '30px',
            right: '50px',
            bottom: '0px',
            containLabel: true
          }
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      return option
    },
    dataArr(data, colorHalf, colorZero) {
      let arr = [
        {
          name: '全部',
          type: 'line',
          data: [],
          areaStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: colorHalf
                },
                {
                  offset: 1,
                  color: colorZero
                }
              ])
            }
          }
        }
      ]
      data.forEach((v) => {
        let total =
          v.list.length &&
          v.list.reduce((arr, size) => {
            return arr + size.count
          }, 0)
        arr[0].data.push(total)
      })
      return arr
    },
    // 报警紧急程度echarts
    getPoliceUrgencyEchats(data = []) {
      let option = {}
      let total = data.reduce((a, b) => a + b.value, 0)
      if (data.length) {
        option = {
          backgroundColor: '#fff',
          color: ['#FA403C', '#FF9435', '#FFBE00', '#3562DB'],
          tooltip: {
            trigger: 'item',
            axisPointer: {
              type: 'shadow'
            },
            confine: true
          },
          legend: {
            bottom: 20,
            left: 'center',
            orient: 'vertical',
            itemHeight: 8,
            itemWidth: 8,
            formatter: (name) => {
              let value
              for (let i = 0; i < data.length; i++) {
                if (data[i].name == name) {
                  value = data[i].value
                }
              }
              // console.log('(value / total)*100===========',(value / total)*100);
              let proportion = total ? ((value / total || 0) * 100).toFixed(2) : 0
              let arr = `{name| ${name} : ${proportion}%   ${value}个}`
              return arr
            },
            textStyle: {
              rich: {
                name: {
                  fontSize: 14,
                  color: '#121F3E',
                  padding: [0, 20, 0, 0]
                },
                proportion: {
                  fontSize: 14,
                  color: '#121F3E'
                }
              }
            }
          },
          series: [
            {
              type: 'pie',
              radius: '45%',
              center: ['50%', '30%'],
              data: data,
              itemStyle: {
                normal: {
                  borderWidth: 2,
                  borderColor: '#ffffff'
                },
                emphasis: {
                  borderWidth: 0,
                  shadowBlur: 0,
                  shadowOffsetX: 0
                }
              },
              labelLine: {
                show: false
              },
              label: {
                show: false
              }
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      return option
    },
    // 获取报警事件类型top10echarts
    getAlarmTopCountEchats(data = []) {
      let option = {}
      if (data.length) {
        option = {
          backgroundColor: '#ffffff',
          color: ['#3562DB'],
          textStyle: {
            color: '#333'
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          grid: {
            // 让图表占满容器
            top: '30px',
            left: '16px',
            right: '16px',
            bottom: '20px',
            containLabel: true
          },
          xAxis: {
            type: 'value',
            minInterval: 1,
            gridIndex: 0,
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              color: '#121F3E',
              fontSize: 12
            }
          },
          yAxis: {
            data: data.map((x) => x.incidentName),
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            triggerEvent: true,
            axisLabel: {
              color: '#121F3E',
              fontSize: 13,
              margin: 6,
              formatter: function (params) {
                var val = ''
                if (params.length > 4) {
                  val = params.substr(0, 5) + '...'
                  return val
                } else {
                  return params
                }
              }
            }
          },
          series: [
            {
              type: 'bar',
              barCategoryGap: '10px',
              data: data.map((x) => x.count)
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      return option
    },
    // 获取报警来源echarts
    getAlarmSourceCountEchats(data = []) {
      let option = {}
      if (data.length) {
        option = {
          backgroundColor: '#ffffff',
          color: ['#FF9435'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          xAxis: {
            type: 'category',
            data: data.map((x) => x.projectName),
            triggerEvent: true,
            axisLine: {
              lineStyle: {
                color: '#E5E6EB'
              }
            },
            axisLabel: {
              interval: 0,
              color: '#121F3E',
              fontSize: 12,
              formatter: function (params) {
                var val = ''
                if (params.length > 4) {
                  val = params.substr(0, 4) + '...'
                  return val
                } else {
                  return params
                }
              }
            }
          },
          dataZoom: [
            {
              type: 'slider',
              realtime: true,
              start: 0,
              end: 20, // 数据窗口范围的结束百分比。范围是：0 ~ 100。
              height: 5, // 组件高度
              left: 5, // 左边的距离
              right: 5, // 右边的距离
              bottom: 10, // 下边的距离
              show: data.length > 6, // 是否展示
              showDetail: false, // 拖拽时是否展示滚动条两侧的文字
              zoomLock: true, // 是否只平移不缩放
              moveOnMouseMove: false, // 鼠标移动能触发数据窗口平移
              // zoomOnMouseWheel: false, // 鼠标移动能触发数据窗口缩放
              // 下面是自己发现的一个问题，当点击滚动条横向拖拽拉长滚动条时，会出现文字重叠，导致效果很不好，以此用下面四个属性进行设置，当拖拽时，始终保持显示六个柱状图，可结合自己情况进行设置。添加这个属性前后的对比见**图二**
              startValue: 0, // 从头开始。
              endValue: 6, // 最多六个
              minValueSpan: 6, // 放大到最少几个
              maxValueSpan: 6 //  缩小到最多几个
            },
            {
              type: 'inside', // 支持内部鼠标滚动平移
              start: 0,
              end: 20,
              zoomOnMouseWheel: false, // 关闭滚轮缩放
              moveOnMouseWheel: true, // 开启滚轮平移
              moveOnMouseMove: true // 鼠标移动能触发数据窗口平移
            }
          ],
          yAxis: {
            type: 'value',
            // name: '单位：%',
            minInterval: 1,
            gridIndex: 0,
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              color: '#121F3E',
              fontSize: 13
            },
            // y轴轴线颜色
            splitLine: {
              show: true,
              lineStyle: {
                color: '#E5E6EB'
              }
            }
          },
          series: [
            {
              data: data.map((x) => x.count),
              type: 'bar',
              barWidth: 16
            }
          ],
          grid: {
            // 让图表占满容器
            top: '30px',
            left: '16px',
            right: '16px',
            bottom: '20px',
            containLabel: true
          }
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      return option
    },
    // 查看报警列表
    goAlarmDetail(val) {
      this.$router.push({
        path: '/allAlarm',
        query: {
          alarmStatus: val
        }
      })
    },
    // 报警故障数echarts
    getPoliceFaultCountEchats(data = []) {
      let option = {}
      let total = data.reduce((a, b) => a + b.value, 0)
      if (data.length) {
        option = {
          backgroundColor: '#fff',
          tooltip: {
            trigger: 'item',
            confine: true,
            axisPointer: {
              type: 'shadow'
            }
          },
          legend: {
            type: 'scroll',
            height: '30%',
            bottom: 20,
            left: 'center',
            orient: 'vertical',
            itemHeight: 8,
            itemWidth: 8,
            formatter: (name) => {
              let value
              for (let i = 0; i < data.length; i++) {
                if (data[i].name == name) {
                  value = data[i].value
                }
              }
              let proportion = total ? Math.round((value / total) * 100) : 0
              let arr = `{name| ${name} :   ${proportion}%     ${value}单}`
              return arr
            },
            textStyle: {
              rich: {
                name: {
                  fontSize: 14,
                  color: '#121F3E',
                  padding: [0, 20, 0, 0]
                },
                proportion: {
                  fontSize: 14,
                  color: '#121F3E'
                }
              }
            }
          },
          series: [
            {
              type: 'pie',
              radius: '45%',
              center: ['50%', '30%'],
              data: data,
              itemStyle: {
                normal: {
                  borderWidth: 2,
                  borderColor: '#ffffff'
                },
                emphasis: {
                  borderWidth: 0,
                  shadowBlur: 0,
                  shadowOffsetX: 0
                }
              },
              labelLine: {
                show: false
              },
              label: {
                show: false
              }
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      return option
    }
  }
}
</script>
<style lang="scss" scoped>
.page-container {
  margin: 8px;
  height: calc(100% - 16px);
  width: calc(100% - 16px);
}

.warn-statistic-content {
  height: 100%;

  ::v-deep .el-row {
    margin: 0 !important;
    height: 100%;
    overflow-y: auto;

    .el-col {
      margin: 0 !important;
      padding: 8px !important;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }
  }

  .box-card {
    // margin-top: 16px;
    height: calc(50% - 8px);
    width: calc(100% - 8px);

    .card-body {
      height: calc(100% - 25px);
      margin: 0;
    }

    .viewDetails {
      user-select: none;
      cursor: pointer;
      margin: 0 !important;
      font-size: 14px;
      color: #3562db;
      position: absolute;
      right: 15px;
      top: 0;
    }

    .add-analysis {
      height: 40px;
      display: flex;
      align-items: center;
      justify-items: center;
      justify-content: space-between;
      padding: 0 10px;

      .date-type-btn {
        display: flex;
      }

      .data-filter {
        width: 44px;
        height: 20px;
        line-height: 20px;
        text-align: center;
        font-size: 14px;
        font-family: 'PingFang SC-Medium', 'PingFang SC';
        color: #7f848c;
        background-color: #f6f5fa;
        border-radius: 2px;
        border: 1px solid #ededf5;
        cursor: pointer;
      }

      .data-filter-active {
        background: #3562db;
        color: #fff;
      }

      .data-filter + .data-filter {
        margin-left: 8px;
      }
    }
  }

  .cardContent-left {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    width: 60%;
    height: 100%;

    p {
      margin: 0;
    }

    .left-item {
      width: 100%;
      // width: calc(100% / 2 - 16px);
      height: calc(50% - 10px);
      margin-right: 16px;
      padding: 24px 24px 30px;
      background: #faf9fc;
      border-radius: 4px;
      margin-bottom: 7px;
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .item-title {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        line-height: 22px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      .item-btn {
        display: flex;
        align-items: center;
        cursor: pointer;
      }
      .other {
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 15px;
        color: #333333;
        line-height: 18px;
        text-align: center;
        font-style: normal;
        text-transform: none;
        .item-value {
          height: 32px;
          font-family: Arial;
          font-weight: bold;
          font-size: 40px;
          color: #333333;
          line-height: 36px;
          font-style: normal;
          text-transform: none;

          & > span {
            margin-left: 4px;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 15px;
            color: #c2c4c8;
            line-height: 18px;
            font-style: normal;
            text-transform: none;
          }
        }
      }

      .item-value {
        height: 32px;
        font-family: HarmonyOS Sans SC;
        font-weight: bold;
        font-size: 24px;
        color: #333333;
        line-height: 32px;
        text-align: left;
        font-style: normal;
        text-transform: none;

        & > span {
          margin-left: 4px;
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 14px;
          color: #96989a;
          line-height: 16px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
      }

      .item-icon {
        position: absolute;
        right: 24px;
        bottom: 24px;
        width: 40px;
        height: 40px;
      }
    }

    .left-item + .left-item {
      width: calc(100% / 3 - 16px);
    }

    // & :last-child {
    //   margin-bottom: 0;
    // }
  }

  .add-warn {
    background-color: #fff;
    border-radius: 4px;
    height: 100%;
    padding: 16px;

    .added-alarms-tabs {
      width: 100%;
      height: 120px;
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid #dcdfe6;

      .alarms-tab {
        // width: 13%;
        // width: calc(100% / 4 - 10px);
        height: 100%;
        box-sizing: border-box;
        padding: 0 8px;
        cursor: pointer;

        .tabs-top-title {
          height: 40px;
          line-height: 40px;
          font-size: 15px;
          font-family: 'PingFang SC-Medium', 'PingFang SC';
          color: #121f3e;
        }

        .tabs-bottom-div {
          height: 60px;
          display: flex;

          .tabs-bottom-div-count {
            flex: 1;
            font-size: 24px;
            line-height: 60px;
            font-family: Arial-Bold, Arial;
            font-weight: bold;
            color: #121f3e;
          }

          .tabs-bottom-div-contrst {
            width: 60%;

            div:first-child {
              height: 30px;
              line-height: 30px;
              font-size: 12px;
              font-family: 'PingFang SC-Regular', 'PingFang SC';
              color: #414653;
            }

            div:last-child {
              height: 30px;
              line-height: 30px;
              font-size: 16px;
              font-family: Arial-Regular, Arial;

              span {
                margin-right: 8px;
              }

              img {
                width: 20px;
                height: 20px;
                vertical-align: middle;
              }
            }
          }
        }
      }

      .alarms-tab:nth-child(-n + 4) {
        width: calc(29.5% - 10px);
      }

      .alarms-tab:nth-child(n + 4) {
        width: 15%;
      }

      .alarms-tab-active {
        background: rgb(53 98 219 / 6%);
        border-radius: 4px 4px 0 0;
        border-bottom: 2px solid #3562db;
      }
    }

    .added-alarms-charts {
      height: calc(100% - 120px);
      padding-top: 15px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      cursor: pointer;

      .flex-box {
        height: calc(50% - 8px);
        display: flex;
        justify-content: space-between;
      }
    }
  }
}
::v-deep .datePickerClass .el-date-editor {
  width: 150px !important;
}
</style>
