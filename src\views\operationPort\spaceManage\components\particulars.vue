<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="sino_content" style="padding: 20px 100px;">
        <div style="line-height: 40px;">位置&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;：{{ formInline.simName }}</div>
        <div class="sty">
          <div class="sino_form_input">空间名称：{{ formInline.localSpaceName }}</div>
          <div class="sino_form_input">空间状态：{{ formInline.spaceState }}</div>
        </div>
        <div class="sty">
          <div class="sino_form_input">本地编码：{{ formInline.localSpaceCode }}</div>
          <div class="sino_form_input">模型编码：{{ formInline.modelCode }}</div>
        </div>
        <div class="sty">
          <div class="sino_form_input">功能类型：{{ formInline.functionDictName }}</div>
          <div class="sino_form_input">空间高度：{{ formInline.hight }}</div>
        </div>
        <div class="sty">
          <div class="sino_form_input">空间长度：{{ formInline.length }}</div>
          <div class="sino_form_input">空间宽度：{{ formInline.width }}</div>
        </div>
        <div class="sty">
          <div class="sino_form_input">建筑面积：{{ formInline.area }}</div>
          <div class="sino_form_input">使用面积：{{ formInline.useArea }}</div>
        </div>
        <div style="line-height: 40px;">备注&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;：{{ formInline.remark }}</div>
        <div class="sty">
          <div class="sino_form_input">归属部门：{{ formInline.dmName }}</div>
          <div class="sino_form_input">部门负责人：{{ formInline.principalName }}</div>
        </div>
        <div class="sty">
          <div class="sino_form_input">空间电话：{{ formInline.spacePhone }}</div>
          <div class="sino_form_input" style="display: flex;">
            <div>空间图片：</div>
            <div><img v-if="formInline.spacePicUrl" :src="formInline.spacePicUrl" alt="" style="width: 200px; height: 200px;" /></div>
          </div>
        </div>
        <div class="sty">
          <div class="sino_form_input">竣工日期：{{ formInline.complitionDate }}</div>
          <div class="sino_form_input">投入使用日期：{{ formInline.useDate }}</div>
        </div>
        <div class="sty">
          <div class="sino_form_input">资产原值：{{ formInline.complitionDate }}</div>
          <div class="sino_form_input">房产证：{{ formInline.useDate == 1 ? '有' : '无' }}</div>
        </div>
        <div class="sty">
          <div class="sino_form_input">不动产登记号：{{ formInline.registratioNo }}</div>
          <div class="sino_form_input">
            附件：
            <br>
            <span v-for="(item, index) in fileList" :key="index" class="files" style="margin-right: 20px;">
              {{ item.name }}
              <i style="display: inline-block; width: 20px;"></i>
              <span style="cursor: pointer; color: #409eff;" @click="downloadIamge(item.url, item.name)">下载</span>
              <i style="display: inline-block; width: 10px;"></i>
            </span>
          </div>
        </div>
        <div style="display: flex; line-height: 40px;">
          <div>空间二维码：</div>
          <div>
            <el-image :src="'data:image/png;base64,' + formInline.qrcodeBase64">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </div>
          <p style="cursor: pointer; color: #409eff;" @click="imgDownlod">下载二维码</p>
        </div>
      </div>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">返回</el-button>
    </div>
  </PageContainer>
</template>

<script>
export default {
  data() {
    return {
      title: this.$route.query.type == 'Add' ? '添加空间' : this.$route.query.type == 'View' ? '查看空间' : '编辑空间',
      fileList: [],
      formInline: {
        simName: '',
        localSpaceName: '',
        localSpaceCode: '',
        spaceStateId: '',
        functionDictId: '',
        hight: '',
        area: '',
        remark: '',
        modelCode: '',
        dmId: '',
        dmName: '',
        principalId: '',
        principalName: '',
        useArea: ''
      }
    }
  },
  created() {},
  mounted() {
    if (this.$route.query.type != 'Add') {
      this.getSpaceDetailFn()
    }
  },
  methods: {
    imgDownlod() {
      this.downloadIamge('data:image/png;base64,' + this.formInline.qrcodeBase64, '空间二维码')
    },
    /**
    //  * @description: 下载图片到本地
    //  * @param {String} imgsrc 图片url
    //  * @param {String} name   图片名称
     */
    downloadIamge(urL, name) {
      const a = document.createElement('a')
      const url = urL
      // 这里是将url转成blob地址，
      fetch(url)
        .then((res) => res.blob())
        .then((blob) => {
          // 将链接地址字符内容转变成blob地址
          a.href = URL.createObjectURL(blob)
          a.download = name || '' // 下载文件的名字
          document.body.appendChild(a)
          a.click()
          // 在资源下载完成后 清除 占用的缓存资源
          window.URL.revokeObjectURL(a.href)
          document.body.removeChild(a)
        })
    },
    //  根据空间ID获取空间信息详情
    getSpaceDetailFn() {
      this.$api
        .getSpaceDetail({
          id: this.$route.query.id
        })
        .then((res) => {
          if (res.code == 200) {
            this.formInline = res.data
            if (this.formInline.spaceFileIds) {
              this.$api.getFilesByIds({ ids: this.formInline.spaceFileIds }).then((res) => {
                console.log(res, 'uiduiduid')
                res.data.forEach((i) => {
                  let obj = {
                    name: i.fileOriginName,
                    url: this.$tools.imgUrlTranslation(i.fileUrl)
                  }
                  this.fileList.push(obj)
                })
              })
            } else {
              this.formInline.spaceFileUrl = []
              this.formInline.spaceFileIds = []
            }
            if (this.$route.query.type != 'View') {
              this.$refs.tree.setCurrentKey(res.data.ssmId)
            }
          }
        })
    },

    filterNode(value, data) {
      if (!value) return true
      return data.ssmName.indexOf(value) !== -1
    },
    nodeClick(val) {
      this.checkedNodeId = val.id
      const { tree } = this.$refs
      this.spaceIds = []
      this.simNames = []
      let checkedNode = tree.getNode(val.id)
      if (checkedNode.data.ssmType == 4) {
        this.getTreeNode(checkedNode)
      } else {
        this.$message({
          message: '请选择楼层 ！',
          type: 'warning'
        })
      }
      this.formInline.simName = this.simNames.join('>')
    },
    getTreeNode(node) {
      if (node.level >= 1) {
        this.spaceIds.unshift(node.data.id)
        this.simNames.unshift(node.data.ssmName)
        this.getTreeNode(node.parent)
      } else {
        this.spaceIds.unshift('#')
      }
    },
    cancel() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
  padding: 10px;
  overflow-y: auto;
}

.sino_form_input {
  width: 300px;
}

.sty {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  width: 60%;
  line-height: 40px;
}
</style>
