<template>
  <el-dialog
    v-dialogDrag
    class="component dictionary-value-edit"
    :title="'班车记录数据导入'"
    width="750px"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :visible.sync="roadmapVisible"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <div class="content" style="padding: 10px; display: flex">
      <div>上传附件：</div>
      <div class="upload-file">
        <el-upload
          ref="uploadFile"
          action="string"
          :limit="1"
          :http-request="httpRequest"
          :beforeUpload="beforeAvatarUpload"
          :file-list="assetsFileList"
          :on-exceed="handleExceed"
          :on-remove="handleRemove"
          accept=".xlsx"
        >
          <div class="leadFile">
            <el-button size="small" type="primary">点击上传</el-button>
          </div>
          <div slot="tip" class="el-upload__tip">只能上传 .xlsx格式</div>
        </el-upload>
      </div>
      <div class="leadFile_item" @click="getTemplateExport">导入模板下载</div>
    </div>

    <template #footer>
      <el-button type="primary" plain @click="closeDialog">取消</el-button>
      <el-button type="primary" @click="onSubmit">确认</el-button>
    </template>
  </el-dialog>
</template>
<script>
import { UsingStatusOptions } from '@/views/operationPort/constant'
import { uploadAcceptDict } from '@/util/dict.js'
import { debounce } from 'lodash/function'
import store from '@/store/index'
import axios from 'axios'
export default {
  name: 'DictionaryValueEdit',
  props: {
    roadmapVisible: {
      type: Boolean,
      default: false
    }
  },
  events: ['update:visible', 'success'],
  data: function () {
    return {
      uploadAcceptDict,
      assetsFileList: [],
      dictData: [],
      loadingStatus: false,
      statusOptions: UsingStatusOptions
    }
  },
  watch: {},
  methods: {
    // dialog点击右上角关闭按钮，重置表单
    httpRequest(item) {
      this.assetsFileList.push(item.file)
    },
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
    },
    handleRemove(file, fileList) {
      this.assetsFileList = []
    },
    beforeAvatarUpload(file) {
      const isLt50M = file.size / 1024 / 1024 < 50
      if (!isLt50M) {
        this.$message.error('上传文件大小不能超过 50MB!')
        return false
      }
      if (file.name.indexOf(',') != -1) {
        this.$message.error('非法的文件名')
        return false
      }
    },
    // 表单提交
    onSubmit: debounce(function () {
      if (this.assetsFileList.length <= 0) {
        return this.$message.error('请上传文件！')
      }
      this.getAssetsImport()
    }, 500),
    // 车次模板导出
    getTemplateExport() {
      this.downLoading = true
      axios({
        method: 'post',
        url: __PATH.SPACE_API + 'busTravelRecords/exportExcelTemplate',
        responseType: 'blob',
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      })
        .then((res) => {
          this.downLoading = false
          let name = '班车记录模板.xlsx'
          let blob = new Blob([res.data]) // { type: "application/vnd.ms-excel" }
          let url = window.URL.createObjectURL(blob) // 创建一个临时的url指向blob对象
          // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
          let a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
          // 释放这个临时的对象url
          window.URL.revokeObjectURL(url)
        })
        .catch((res) => {
          this.downLoading = false
          this.$message.error('导出失败！')
        })
    },
    // 路线导入
    getAssetsImport() {
      let formData = new FormData()
      this.downLoading = true
      if (this.assetsFileList && this.assetsFileList.length) {
        formData.append('file', this.assetsFileList[0])
      }
      this.tableLoading = true
      this.subLoading = true
      axios
        .post(__PATH.SPACE_API + 'busTravelRecords/importExcel', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
            Authorization: 'Bearer ' + this.$store.state.user.token
          }
        })
        .then((res) => {
          console.log(res, 'RES')
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.$emit('getDataList')
          } else {
            this.$message.error(res.data.msg)
          }
          this.subLoading = false
          this.assetsFileList = []
          this.closeDialog()
        })
        .catch((error) => {
          console.log(error, '123---------------------')
        })
      this.assetsImportdialogVisible = false
      this.downLoading = false
      this.tableLoading = false
    },
    closeDialog() {
      this.$emit('closeDialog')
    }
  }
}
</script>
<style lang="scss">
.component.dictionary-value-edit {
  .el-form {
    background-color: #fff;
    padding: 10px 16px 0;
    .el-form-item {
      .el-cascader,
      .el-select {
        width: 100%;
      }
    }
  }
  .content {
    width: 100%;
    max-height: 500px !important;
    overflow: auto;
    background-color: #fff !important;
  }
  .leadFile_item {
    margin: 10px 35px;
    color: #66b1ff;
    cursor: pointer;
  }
  .dictionary-value-edit {
    &__color {
      vertical-align: middle;
      .el-input-group__prepend {
        padding: 0;
        line-height: 0;
        border-radius: 4px 0 0 4px;
      }
      .el-color-picker {
        height: 30px;
      }
      .el-color-picker__trigger {
        border: none;
        height: 30px;
        width: 30px;
        padding: 2px;
      }
      .el-input__inner {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      }
    }
    &__upload {
      &.hide {
        .el-upload {
          opacity: 0;
          transition-duration: 0.5s;
          pointer-events: none;
        }
      }
    }
  }
}
</style>
