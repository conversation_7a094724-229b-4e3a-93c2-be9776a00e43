module.exports = {
  root: true,
  env: {
    browser: true,
    node: true,
    es6: true
  },
  globals: {
    process: true,
    require: true,
    module: true,
    __PATH: true,
    AMap: true
  },
  extends: ['plugin:vue/essential', '@vue/eslint-config-prettier'],
  parserOptions: {
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true,
      tsx: true
    }
  },
  parser: 'vue-eslint-parser',
  rules: {
    // 代码风格
    'block-spacing': [2, 'always'],
    'brace-style': [2, '1tbs', { allowSingleLine: true }],
    'comma-spacing': [2, { before: false, after: true }],
    'comma-dangle': [2, 'never'],
    'comma-style': [2, 'last'],
    'computed-property-spacing': [2, 'never'],
    indent: ['error', 2, { SwitchCase: 1 }],
    'key-spacing': [
      2,
      {
        beforeColon: false,
        afterColon: true
      }
    ],
    'keyword-spacing': [
      2,
      {
        before: true,
        after: true
      }
    ],
    'linebreak-style': 0,
    'multiline-ternary': [0, 'always-multiline'],
    'no-multiple-empty-lines': [2, { max: 0 }],
    'no-unneeded-ternary': [
      2,
      {
        defaultAssignment: false
      }
    ],
    quotes: [2, 'single'],
    semi: [2, 'never'],
    'space-before-blocks': [2, 'always'],
    // 'space-before-function-paren': [2, 'never'],
    'space-in-parens': [2, 'never'],
    'space-infix-ops': 2,
    'space-unary-ops': [
      2,
      {
        words: true,
        nonwords: false
      }
    ],
    'spaced-comment': [
      2,
      'always',
      {
        markers: ['global', 'globals', 'eslint', 'eslint-disable', '*package', '!', ',']
      }
    ],
    'switch-colon-spacing': [
      2,
      {
        after: true,
        before: false
      }
    ],
    // ES6
    // 'arrow-parens': [2, 'as-needed'],
    'arrow-spacing': [
      2,
      {
        before: true,
        after: true
      }
    ],
    'vue/multi-word-component-names': 'off',
    'vue/html-indent': [2, 2],
    'vue/max-attributes-per-line': 0,
    'vue/require-default-prop': 0,
    'vue/singleline-html-element-content-newline': 0,
    'vue/attributes-order': 2,
    'vue/order-in-components': 2,
    'vue/this-in-template': 2,
    'vue/no-parsing-error': [2, { 'x-invalid-end-tag': false }], // 关闭对iview end-tag 检查
    'vue/script-indent': [
      'error',
      2,
      {
        switchCase: 1
      }
    ],
    'no-dupe-keys': 'error',
    'prettier/prettier': ['error', { endOfLine: 'auto' }]
  }
}
