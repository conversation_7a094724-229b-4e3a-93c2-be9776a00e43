<template>
  <div v-loading="pageLoading" class="equipment-status">
    <div class="equipment-top">
      <div class="top-left">
        <div v-for="(item, index) in maintenance" :key="index" class="item" :class="item.color">
          <span class="item-text">{{ item.text }}</span>
          <span class="item-text"
            >{{ item.count | toThousands }}<span class="unit">{{ item.unit }}</span></span
          >
        </div>
      </div>
      <div class="top-right">
        <div class="tag-title">设备标签</div>
        <div class="tag-content">
          <div class="tag-left">
            <div class="tagInfo">
              <span class="itemTitle">设备编码:</span>
              <span class="itemInfo">{{ detailsInfo.assetsCode }}</span>
            </div>
            <div class="tagInfo">
              <span class="itemTitle">设备名称:</span>
              <span class="itemInfo">{{ detailsInfo.assetsName }}</span>
            </div>
            <div class="tagInfo">
              <span class="itemTitle">使用状态:</span>
              <span class="status"> <span></span>{{ detailsInfo.useStatusName }} </span>
            </div>
          </div>
          <div class="tag-right">
            <el-image style="width: 98px; height: 98px" :src="scanImg"> </el-image>
            <div class="scanText">设备二维码</div>
          </div>
        </div>
      </div>
    </div>
    <div class="equipment-content">
      <div class="content-left">
        <div id="line-chart" class="charts"></div>
      </div>
      <div class="content-right">
        <div class="swiper">
          <div class="swiper-desc">
            此设备共有图片<span class="desc-num">{{ swiperInfos.length }}</span
            >张
          </div>
          <div class="swiper-content">
            <template>
              <el-carousel v-if="swiperInfos.length > 0" ref="carousel" :autoplay="false" indicator-position="none" arrow="never" @change="changeImage">
                <el-carousel-item v-for="item in swiperInfos" :key="item.id" :name="item.id + ''">
                  <el-image style="width: 100%; height: 100%" :src="item.url" :preview-src-list="srcList"> </el-image>
                </el-carousel-item>
              </el-carousel>
              <div class="page-num">
                <span>{{ swiperInfos.length > 0 ? cuurentPage : 0 }}</span
                >/<span>{{ swiperInfos.length }}</span>
              </div>
              <div v-if="swiperInfos.length === 0" class="image-warp">
                <el-image style="width: 100%; height: 100%" src=""> </el-image>
                <!-- <img :src="defaultImg" alt="" /> -->
              </div>
              <div class="left-btn" @click="switchImage('prev')">
                <img src="@/assets/images/header/btn／right.png" alt="上一张" />
              </div>
              <div class="right-btn" @click="switchImage('next')">
                <img src="@/assets/images/header/btn／right.png" alt="下一张" />
              </div>
            </template>
          </div>
        </div>
        <div class="maintenance">
          <span class="maintenance-text">保修信息</span>
          <span class="maintenance-logo">出保</span>
          <span class="maintenance-day"
            >已出保<span class="day">{{ Number(detailsInfo.outWarrDateNum) > 0 ? detailsInfo.outWarrDateNum : 0 }}</span
            >天</span
          >
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { iomsUserInfon } from '@/util/dict.js'
import * as echarts from 'echarts'
import defaultImg from '@/assets/images/header/default.png'
export default {
  name: 'EquipmentSituation',
  filters: {
    toThousands(num) {
      let result = ''
      let counter = 0
      num = (num || 0).toString()
      for (let i = num.length - 1; i >= 0; i--) {
        counter++
        result = num.charAt(i) + result
        if (!(counter % 3) && i !== 0) {
          result = ',' + result
        }
      }
      return result
    }
  },
  props: {
    deviceId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      defaultImg,
      maintenance: [
        {
          text: '维修总次数',
          unit: '次',
          count: 0,
          color: 'green'
        },
        {
          text: '维修总费用',
          unit: '元',
          count: 0,
          color: 'blue'
        },
        {
          text: '维修停工总时长',
          unit: '分钟',
          count: 0,
          color: 'orange'
        }
      ],
      swiperInfos: [],
      srcList: [],
      axisData: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
      seriesData: [],
      echartsOption: {
        lineEcharts: null
      },
      cuurentPage: 1,
      detailsInfo: {}, // 复制和编辑时详情信息
      pageLoading: false,
      legendData: [],
      scanImg: ''
    }
  },
  mounted() {
    window.onresize = () => {
      this.echartsOption.lineEcharts.resize()
    }
  },
  created() {
    // const { id } = this.$route.query
    const id = this.deviceId
    this.getInfo(id)
    this.getTag(id)
    this.getAssetsInfoView(id)
  },
  methods: {
    // 使用状态和图片展示详情
    getAssetsInfoView(id) {
      // this.pageLoading = true
      this.$api.getEquimentInfo({ id: id, ...iomsUserInfon }).then((res) => {
        // this.pageLoading = false
        console.log(111, res)
        if (res.code === '200') {
          this.detailsInfo = res.data[0]
          if (this.detailsInfo.pictureView) {
            const arr = this.detailsInfo.pictureView.split(',')
            const newArr = []
            arr.length &&
              arr.forEach((item, index) => {
                newArr.push({
                  id: index,
                  url: item
                })
                this.srcList.push(item)
              })
            this.swiperInfos = newArr
            console.log(11111, newArr)
          }
        }
      })
    },
    // 维修总次数使用图表
    getInfo(id) {
      this.$api.getEquimentMaintenance({
        deviceId: id,
        ...iomsUserInfon
      }).then((res) => {
        const { code, data } = res
        console.log(code)
        if (code === '200') {
          const { cost, count, time, rate } = data
          this.maintenance[0].count = count
          this.maintenance[1].count = cost
          this.maintenance[2].count = time
          const date = new Date()
          const dateYear = date.getFullYear()
          const obj = {}
          rate.length &&
            rate.forEach((item, index) => {
              const date = item.month
              const year = date.split('-')[0]
              const month = date.substring(date.indexOf('-') + 1)
              const o = {
                [month - 1]: item.rate
              }
              if (!obj[year]) {
                obj[year] = [o]
              } else {
                obj[year].push(o)
              }
            })
          const seriesData = []
          const legendData = []
          for (const key in obj) {
            const seriesDataItem = {
              name: key + '年',
              type: 'line',
              data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
            }
            obj[key].forEach((item) => {
              const key = Object.keys(item)[0]
              seriesDataItem.data[key] = Number(item[key] * 100).toFixed(2)
            })
            seriesData.push(seriesDataItem)
            legendData.push(key + '年')
          }
          if (legendData.length) {
            for (let i = 0; i < 3; i++) {
              if (!legendData[i]) {
                seriesData[i] = { name: dateYear - i + '年', type: 'line', data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0] }
                legendData[i] = dateYear - i + '年'
              }
            }
          }
          if (!rate.length) {
            for (let i = 0; i < 3; i++) {
              seriesData[i] = { name: dateYear - i + '年', type: 'line', data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0] }
              legendData[i] = dateYear - i + '年'
            }
          }
          this.legendData = legendData
          this.seriesData = seriesData
          this.initEcharts()
        } else if (res.data.code === '600') {
          this.maintenance[0].count = 0
          this.maintenance[1].count = 0
          this.maintenance[2].count = 0
        }
      })
    },
    // 设备标签信息
    getTag(id) {
      const a = {
        // modelId: id,
        // id: '7b1bc599b171473ca21cbcfa7aadccc9'
        assetsIds: id,
        ...iomsUserInfon
      }
      this.$api.getEquimentTag(a).then((res) => {
        if (res.code === '200') {
          this.scanImg = res.data[0].url.temporaryUrl
          this.detailsInfo.assetsCode = res.data[0].assetsCode
          this.detailsInfo.assetsName = res.data[0].assetsName
        }
      })
    },
    // 初始化echarts
    initEcharts() {
      const date = new Date()
      const dateYear = date.getFullYear()
      this.echartsOption.lineEcharts = echarts.init(document.getElementById('line-chart'))
      const options = {
        color: ['#29B79F', '#5C90FF', '#FA7600'],
        title: {
          text: `${dateYear - 2}-${dateYear}设备正常使用率`,
          left: 'left',
          textStyle: {
            color: '#121f3e',
            fontSize: '16px',
            fontWeight: 'normal'
          }
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: this.legendData,
          top: '10%',
          itemGap: 30,
          textStyle: {
            color: '#121f3e'
          }
        },
        grid: {
          top: '30%',
          left: '3%',
          right: '4%',
          bottom: '2%',
          containLabel: true
        },
        xAxis: {
          show: true,
          type: 'category',
          boundaryGap: false,
          data: this.axisData,
          axisLine: {
            show: false,
            lineStyle: {
              color: '#121f3e'
            }
          }
        },
        yAxis: {
          show: true,
          name: '正常使用率',
          type: 'value',
          nameGap: 30,
          axisLabel: {
            formatter: '{value} %'
          },
          splitLine: {
            // 网格线
            lineStyle: {
              type: 'solid', // 设置网格线类型 dotted：虚线   solid:实线
              color: '#D8DEE7'
            },
            show: true // 隐藏或显示
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: '#121f3e'
            }
          }
        },
        series: this.seriesData
      }
      this.echartsOption.lineEcharts.setOption(options)
    },
    switchImage(type) {
      if (type === 'next') this.$refs.carousel.next()
      if (type === 'prev') this.$refs.carousel.prev()
    },
    changeImage(target, old) {
      this.cuurentPage = target + 1
    }
  }
}
</script>
<style lang="scss" scoped>
.equipment-status {
  width: 100%;
  height: 100%;
  .equipment-top {
    height: 210px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .top-left {
      width: calc(100% - 350px - 14px);
      height: 100%;
      background: #fff;
      // border: 1px solid #5996f9;
      border-radius: 10px;
      display: flex;
      justify-content: space-around;
      align-items: center;
      padding: 0 20px;
      box-sizing: border-box;
      .item {
        width: 318px;
        height: 130px;
        border-radius: 10px;
        padding: 10px 0 10px 10px;
        box-sizing: border-box;
        text-align: center;
        user-select: none;
      }
      .item-text {
        display: flex;
        width: 93%;
        height: 45%;
        font-size: 17px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #ffffff;
        letter-spacing: 1px;
        .unit {
          font-size: 15px;
          margin: 5px 0 0 8px;
        }
      }

      .item-text:first-child {
        justify-content: flex-start;
        align-items: center;
        padding-left: 20px;
        box-sizing: border-box;
      }
      .item-text:last-child {
        justify-content: flex-end;
        align-items: center;
        padding-right: 30px;
        box-sizing: border-box;
        font-size: 26px;
      }
      .green {
        background: url('~@/assets/images/header/green.png') no-repeat 100% 100%;
      }
      .blue {
        background: url('~@/assets/images/header/blue.png') no-repeat 100% 100%;
      }
      .orange {
        background: url('~@/assets/images/header/orange.png') no-repeat 100% 100%;
      }
    }
    .top-right {
      width: 350px;
      height: 100%;
      background: #fff;
      border-radius: 10px;
      .tag-title {
        margin: 30px 0 0 20px;
        font-size: 16px;
        color: #121f3e;
      }
      .tag-content {
        margin-top: 8px;
        display: flex;
        justify-content: space-between;
        .tag-left {
          margin-left: 20px;
          padding-top: 14px;
          width: 60%;
          .tagInfo {
            margin: 15px 0 0 0;
            font-size: 14px;
            color: #909399;
            display: flex;
            .itemTitle {
              width: 70px;
            }
            .itemInfo {
              width: calc(100% - 70px);
              color: #121f3e;
            }
            .status {
              display: flex;
              align-items: center;
              color: #7bffc1;
              span {
                margin: 0 5px 0 0;
                display: block;
                width: 8px;
                height: 8px;
                border-radius: 50%;
                background-color: #7bffc1;
              }
            }
          }
        }
        .tag-right {
          margin-right: 18px;
          width: 40%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          text-align: center;
          .scanText {
            margin-top: 14px;
            font-size: 12px;
            color: #121f3e;
          }
        }
      }
    }
  }
  .equipment-content {
    width: 100%;
    margin-top: 16px;
    height: 510px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .content-left {
      width: calc(100% - 350px - 14px);
      height: inherit;
      background: #fff;
      border-radius: 10px;
      padding: 20px;
      padding-bottom: 0;
      box-sizing: border-box;
      .charts {
        width: 100%;
        height: calc(100% - 40px);
        // min-height: 28rem;
      }
    }
    .content-right {
      width: 350px;
      height: 100%;
      background: #fff;
      border-radius: 10px;
      overflow: auto;
      .swiper {
        width: 80%;
        height: 249px;
        margin: 20px auto 0 auto;
        .swiper-content {
          width: 215px;
          height: 215px;
          display: flex;
          justify-content: center;
          align-items: center;
          margin: 16px auto;
          background-color: #F5F7FA;
          position: relative;
          .el-carousel {
            width: 100%;
            height: 100%;
          }
          ::v-deep .el-carousel__container {
            width: 100%;
            height: 100%;
          }
          .page-num {
            position: absolute;
            bottom: -25px;
          }
          .left-btn {
            position: absolute;
            transform: rotate(180deg);
            left: -26px;
          }
          .right-btn {
            position: absolute;
            right: -26px;
          }
        }
        .swiper-desc {
          height: 15px;
          font-size: 14px;
          font-weight: 400;
          color: #121f3e;
          margin: 0 auto;
          text-align: center;
          .desc-num {
            color: #5c90ff;
            margin: 0 5px;
          }
        }
      }
      .maintenance {
        width: 85px;
        height: 150px;
        margin: 70px auto 0 auto;
        text-align: center;
        .maintenance-text {
          display: block;
          font-size: 17px;
          font-family: NotoSansHans-Medium, NotoSansHans;
          font-weight: normal;
          color: #121f3e;
        }
        .maintenance-logo {
          display: block;
          width: 67px;
          height: 67px;
          text-align: center;
          line-height: 55px;
          font-size: 16px;
          font-family: NotoSansHans-Bold, NotoSansHans;
          font-weight: bold;
          color: #5c90ff;
          background: url('~@/assets/images/header/dp.png');
          margin: 15px auto;
        }
        .maintenance-day {
          display: block;
          font-size: 14px;
          font-family: NotoSansHans-Regular, NotoSansHans;
          font-weight: 400;
          color: #121f3e;
          .day {
            color: #3562db;
            margin: 0 5px;
          }
        }
      }
    }
  }
  ::v-deep .el-carousel__container {
    position: relative;
    height: 187px;

    img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
