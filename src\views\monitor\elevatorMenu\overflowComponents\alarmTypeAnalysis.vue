<template>
  <div style="width: 100%; height: 100%; padding: 10px" class="drag_class">
    <div class="card_box_title card_box_long_bg">
      <div class="title_left">报警类型分析</div>
      <div class="title_right">
        <span :class="{ active: dateType === 3 }" @click="dateTypeClick(3)">本日</span>
        <span :class="{ active: dateType === 2 }" @click="dateTypeClick(2)">本周</span>
        <span :class="{ active: dateType === 1 }" @click="dateTypeClick(1)">本月</span>
      </div>
    </div>
    <div v-if="!alarmTypeAnalysisShow" class="echart-null">
      <img src="@/assets/images/null.png" alt="" />
      <div>暂无数据~</div>
    </div>
    <div v-else style="width: 100%; height: calc(100% - 31px); position: relative">
      <!-- <div class="pie-decoration"></div> -->
      <div id="alarmTypeAnalysis" ref="alarmTypeAnalysis"></div>
    </div>
  </div>
</template>
<script>
import { monitorTypeList } from '@/util/dict.js'
import * as echarts from 'echarts'
import moment from 'moment'
moment.locale('zh-cn')
export default {
  data() {
    return {
      requestHttp: __PATH.VUE_IEMC_ELEVATOR_API,
      projectCode: monitorTypeList.find((item) => item.projectName == '电梯监测').projectCode, // 电梯code
      alarmTypeAnalysisShow: false,
      dateType: 1,
      timer: null
    }
  },
  mounted() {
    console.log(document.getElementById('alarmTypeAnalysis'))
    this.getAlarmTypeAnalysisData()
    this.scheduledTasks()
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
  },
  methods: {
    scheduledTasks() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
      this.timer = setInterval(() => {
        this.getAlarmTypeAnalysisData()
      }, 30000)
    },
    echartsResize() {
      this.$nextTick(() => {
        setTimeout(() => {
          if (document.getElementById('alarmTypeAnalysis')) {
            echarts.init(document.getElementById('alarmTypeAnalysis')).resize()
          }
        }, 50)
      })
    },
    dateTypeClick(val) {
      this.dateType = val
      this.$emit('dataChange', { type: val })
      this.getAlarmTypeAnalysisData()
    },
    array2obj(array, key) {
      var resObj = {}
      for (var i = 0; i < array.length; i++) {
        resObj[array[i][key]] = array[i]
      }
      return resObj
    },
    //  moment().date(1).format('YYYY-MM-DD')
    // 获取报警类型数据
    getAlarmTypeAnalysisData() {
      let startTime = ''
      let endTime = ''
      switch (this.dateType) {
        case 1:
          startTime = moment().startOf('month').format('YYYY-MM-DD')
          endTime = moment().endOf('month').format('YYYY-MM-DD')
          break
        case 2:
          startTime = moment().isoWeekday(1).format('YYYY-MM-DD')
          endTime = moment().isoWeekday(7).format('YYYY-MM-DD')
          break
        case 3:
          startTime = moment().format('YYYY-MM-DD')
          endTime = startTime
          break
        default:
          break
      }
      this.$api
        .getReasonStatisticPie(
          {
            projectCode: this.projectCode,
            startTime: startTime,
            endTime: endTime
          },
          this.requestHttp
        )
        .then((res) => {
          if (res.code == 200) {
            if (res.data.length > 0) {
              this.alarmTypeAnalysisShow = true
              let that = this
              setTimeout(() => {
                that.setAlarmTypeAnalysisEcharts(res.data)
              }, 50)
            } else {
              this.alarmTypeAnalysisShow = false
            }
          }
        })
    },
    // 报警类型数据echarts
    setAlarmTypeAnalysisEcharts(data) {
      const getchart = echarts.init(this.$refs.alarmTypeAnalysis)
      const nameList = Array.from(data, (item) => item.name)
      const sum = data.reduce((per, cur) => per + cur.value, 0)
      var objData = this.array2obj(data, 'name')
      let option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          type: 'scroll',
          orient: 'vertical',
          top: '10',
          x: '55%',
          hoverLink: true, // 启用图例项和相应系列项的hover联动
          pageIconColor: '#5188fc', // 激活的分页按钮颜色
          pageIconInactiveColor: '#c2c6ce', // 没激活的分页按钮颜色
          pageTextStyle: {
            color: '#FFF' // 设置图例数字的颜色
          },
          data: nameList,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 16,
          formatter: function (name) {
            return '{c|' + objData[name].percent + '}{a|' + name + '}'
          },
          textStyle: {
            rich: {
              a: {
                align: 'center',
                fontSize: 13,
                color: 'rgba(255,255,255,1)'
              },
              c: {
                align: 'center',
                width: 70,
                fontSize: 15,
                color: '#00C2FF'
              }
            }
          }
        },
        series: [
          {
            type: 'pie',
            roundCap: true,
            radius: ['56%', '72%'],
            center: ['30%', '50%'],
            hoverAnimation: true,
            label: {
              normal: {
                show: false,
                position: 'center',
                // formatter: '{value|{d}' + '%' + '}\n{label|{b}}',
                formatter: function (data) {
                  return '{value|' + objData[data.name].percent + '}\n{label|' + data.name + '}'
                },
                rich: {
                  value: {
                    padding: 5,
                    align: 'center',
                    verticalAlign: 'middle',
                    fontSize: 24,
                    fontWeight: '600'
                  },
                  label: {
                    align: 'center',
                    verticalAlign: 'middle',
                    color: '#A3A9AD',
                    fontWeight: '400',
                    fontSize: 14
                  }
                }
              },
              emphasis: {
                show: true,
                textStyle: {
                  fontSize: '12',
                  color: '#fff'
                }
              }
            },
            itemStyle: {
              borderWidth: 3,
              borderColor: '#09141e'
            },
            data: data
          },
          {
            name: '',
            type: 'pie',
            silent: true,
            startAngle: 80,
            radius: ['54%'],
            hoverAnimation: false,
            center: ['30%', '50%'],
            tooltip: {
              show: false
            },
            itemStyle: {
              color: 'rgba(10, 25, 40, 1)',
              borderWidth: 1,
              borderColor: '#1E2C39'
            },
            data: [sum]
          },
          {
            name: '',
            type: 'pie',
            silent: true,
            startAngle: 80,
            radius: ['48%'],
            hoverAnimation: false,
            center: ['30%', '50%'],
            tooltip: {
              show: false
            },
            itemStyle: {
              color: 'rgba(21, 36, 50, 1)'
            },
            data: [sum]
          },
          {
            name: '',
            type: 'pie',
            startAngle: 80,
            silent: true,
            radius: ['77%', '78%'],
            hoverAnimation: false,
            center: ['30%', '50%'],
            tooltip: {
              show: false
            },
            itemStyle: {
              normal: {
                color: 'rgba(10, 25, 40, .1)',
                borderColor: '#1E2C39',
                borderWidth: 1
              }
            },
            data: [sum]
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 先移除点击事件 解决点击事件重复绑定
      getchart.off('click')
      // 点击事件
      getchart.on('click', (params) => {
        this.$emit('barClick', { type: 1, ...params.data })
      })
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
      // getchart.dispatchAction({
      //   type: 'highlight',
      //   seriesIndex: 0,
      //   dataIndex: 0
      // })
      // getchart.on('mouseover', (e) => {
      //   // 取消默认高亮
      //   getchart.dispatchAction({
      //     type: 'downplay',
      //     seriesIndex: 0,
      //     dataIndex: 0
      //   })
      // })
      // 鼠标移出后默认高亮
      // getchart.on('mouseout', (e) => {
      //   getchart.dispatchAction({
      //     type: 'highlight',
      //     seriesIndex: 0,
      //     dataIndex: 0
      //   })
      // })
    }
  }
}
</script>
<style lang="scss" scoped>
.card_box_long_bg {
  background: url('~@/assets/images/elevator/module-bg.png') no-repeat;
  background-size: 100% 100%;
}
.card_box_title {
  height: 31px;
  width: 100%;
  line-height: 31px;
  padding-left: 10px;
  font-size: 15px;
  font-family: Alibaba PuHuiTi-Medium, Alibaba PuHuiTi;
  font-weight: 500;
  color: #b7cfff;
  display: flex;
  justify-content: space-between;
  .title_left {
  }
  .title_right {
    padding: 0 10px;
    cursor: pointer;
    span {
      margin: 0 5px;
      font-size: 14px;
    }
    .active {
      color: orange;
    }
  }
}
.echart-null {
  margin: 0 auto;
  height: calc(100% - 31px);
  width: 50%;
  text-align: center;
  color: #8a8c8f;
  img {
    max-width: 100%;
    max-height: calc(100% - 20px);
  }
  div {
    font-size: 14px;
  }
}
#alarmTypeAnalysis {
  width: 100%;
  height: 100%;
  z-index: 2;
}
</style>
