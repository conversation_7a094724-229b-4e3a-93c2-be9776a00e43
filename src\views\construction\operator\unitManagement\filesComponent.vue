<template>
  <div class="file-component" v-loading="pageLoading">
    <div class="file-upload">
      <el-upload action="" list-type="list" :file-list="filesList" :show-file-list="false" accept="*" :http-request="httpRequset" :on-remove="handleRemove">
        <el-button size="small" type="primary">上传</el-button>
      </el-upload>
    </div>
    <div class="file-table">
      <el-table v-loading="tableLoading" height="100%" :data="tableData" border stripe table-layout="auto" class="tableAuto" row-key="id">
        <el-table-column label="序号" type="index" width="55"> </el-table-column>
        <el-table-column label="附件" prop="name" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-button type="text" @click="downFile(scope.row)">{{ scope.row.name }}</el-button>
          </template>
        </el-table-column>
        <el-table-column label="添加人" prop="createBy" show-overflow-tooltip width="120px"></el-table-column>
        <el-table-column label="添加时间" prop="createDate" show-overflow-tooltip width="180px"> </el-table-column>
        <el-table-column label="操作" width="150px">
          <template #default="{ row }">
            <el-button type="text" style="color: #ff1919" @click="handleListEvent(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script lang="jsx">
import dayjs from 'dayjs'
import _ from 'lodash'
export default {
  props: {
    fieldName: {
      type: String,
      default: ''
    },
    id: {
        type: String,
        default: ''
    }
  },
  data() {
    return {
      pageLoading: false,
      tableLoading: false,
      tableData: [],
      filesList: []
    }
  },
  mounted() {
    this.getDataList()
  },
  methods: {
    // 删除
    handleListEvent(row) {
      let params = {
        id: row.fileId,
      }
        this.pageLoading = false
          this.$api.delFileUnit(params).then(res=>{
            if(res.code=='200'){
                this.filesList = []
                this.$message.success(res.msg)
                this.getDataList()
            } else {
              this.$message.error(res.msg)
            }
          }).catch(()=>{
          }).finally(()=>{
            this.pageLoading = false
          })
    },
    // 获取列表数据
    getDataList() {
        let obj = {
            attachmentInfo: 1,
            fireControlInfo: 2,
            elseFile: 3,
        }
        let params = {
            type: obj[this.fieldName],
            id: this.id
        }
        this.$api.getFileListUnit(params).then(res => { 
            if(res.code == 200){
                this.tableData = res.data
            }
        })
    },
    // 上传
    httpRequset(file) {
      this.$api.constructionUpload(__PATH.SPACE_API, 'SecurityLedger/upload', file).then((res) => {
        if (res.code == 200) {
          let obj = {
            attachment: res.data.name,
            name: res.data.name,
            createBy: this.$store.state.user.userInfo.user.staffName,
            createDate: dayjs().format('YYYY-MM-DD HH:mm:ss'),
            url: res.data.fileKey
          }
          let fieldObj = {
            attachmentInfo: 1,
            fireControlInfo: 2,
            elseFile: 3,
        }
        let params = {
            compyId: this.id,
            ...obj,
            fileType: fieldObj[this.fieldName]
        }
        // let arr = [obj]
        // params[this.fieldName] = JSON.stringify(arr)
        this.pageLoading = false
          this.$api.updateFileUnit(params).then(res=>{
            if(res.code=='200'){
                this.filesList = []
                this.$message.success(res.msg)
                this.getDataList()
            } else {
              this.$message.error(res.msg)
            }
          }).catch(()=>{
          }).finally(()=>{
            this.pageLoading = false
          })
        }
      })
    },
    // 移除
    handleRemove(fileList) {
      this.filesList = fileList
    },
    // 下载文件
    downFile(row) {
      window.open(this.$tools.imgUrlTranslation(row.url))
    },
  }
}
</script>
<style lang="scss" scoped>
.file-component {
  height: 100%;
  .file-upload {
    margin-bottom: 10px;
  }
  .file-table {
    height: calc(100% - 42px);
  }
}
</style>