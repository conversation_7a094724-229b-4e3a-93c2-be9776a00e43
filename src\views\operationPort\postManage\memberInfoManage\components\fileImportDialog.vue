<template>
  <el-dialog v-if="fileImportShow" v-dialogDrag title="上传文件" width="50%" :visible.sync="fileImportShow" custom-class="model-dialog" :before-close="closeDialog">
    <div class="upload-content">
      <el-upload
        ref="uploadFile"
        action
        drag
        class="mterial_file"
        :limit="1"
        multiple
        :http-request="httpRequest"
        :beforeUpload="beforeAvatarUpload"
        :file-list="fileList"
        :on-exceed="handleExceed"
        :on-remove="handleRemove"
        accept=".Excel, .xlsx, .xls"
        list-type="text"
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div slot="tip" class="el-upload__tip">
          <p class="tip_2">可上传单个文件，小于50M，支持.Excel类型</p>
        </div>
      </el-upload>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="submit">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import axios from 'axios'
export default {
  name: 'fileImportDialog',
  props: {
    fileImportShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      fileList: []
    }
  },
  mounted() {},
  methods: {
    // 限制文件大小
    beforeAvatarUpload(file) {
      const isLt50M = file.size / 1024 / 1024 < 50
      if (!isLt50M) {
        this.$message.error('上传图片大小不能超过 50MB!')
        return false
      }
      if (file.name.indexOf(',') != -1) {
        this.$message.error('非法的文件名')
        return false
      }
    },
    /**
     * 文件上传成功
     */
    handlePreview(file) {},
    httpRequest(item) {
      this.fileList.push(item.file)
    },
    handleExceed(files, fileList) {
      this.$message.warning('最多选择一个文件')
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`)
    },
    /**
     * 删除文件
     */
    handleRemove(file, fileList) {
      this.fileList = []
    },
    closeDialog() {
      this.$emit('closefileImportDialog')
    },
    submit() {
      if (this.fileList.length !== 1) {
        return this.$message({
          message: '请上传文件',
          type: 'warning'
        })
      }
      let data = {
        file: this.fileList[0]
      }
      const userInfo = this.$store.state.user.userInfo.user
      let formdata = new FormData()
      formdata.append('hospitalCode', userInfo.hospitalCode ?? 'BJSJTYY')
      formdata.append('unitCode', userInfo.hospitalCode ?? 'BJSYGJ')
      formdata.append('file', data.file)
      // console.log(userInfo, 'userInfo')
      axios({
        method: 'post',
        url: `${__PATH.VUE_SPACE_API}` + 'hospitalStaff/hospital-staff/importHospitalStaffList',
        data: formdata,
        responseType: '',
        headers: {
          'Content-Type': 'application/json;charset=utf-8',
          Authorization: this.$store.state.user.token,
          hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
          unitCode: userInfo.unitCode ?? 'BJSYGJ'
        }
      })
        .then((res) => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            setTimeout(() => {
              this.$emit('submitfileImportDialog', this.fileList)
            }, 800)
          } else {
            this.$message.error(res.data.msg || '导入失败')
          }
        })
        .catch((res) => {
          this.$message.error(res.msg || '导入失败')
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.model-dialog {
  .upload-content {
    margin: 0 auto;
    background: #fff;
    text-align: center;
    padding: 10px 0;
    border-radius: 4px;
    max-height: 400px;
    width: 100%;
    .el-upload__tip {
      p {
        line-height: 1.5rem;
      }
      .tip_1 {
        color: #5188fc;
        cursor: pointer;
      }
    }
  }
}
</style>
