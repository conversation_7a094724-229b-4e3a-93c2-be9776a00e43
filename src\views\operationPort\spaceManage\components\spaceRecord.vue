<template>
  <PageContainer :footer="true">
    <div slot="content" class="whole">
      <div style="height: 97%;">
        <el-table :data="tableData" height="calc(100% - 10px)" stripe border>
          <el-table-column type="index" label="序号" width="50" align="center">
            <template slot-scope="scope">
              <span>{{ (pagination.current - 1) * pagination.size + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="空间编码" min-width="150" prop="modelCode" show-overflow-tooltip> </el-table-column>
          <el-table-column label="空间本地名称" min-width="150" prop="localSpaceName" show-overflow-tooltip> </el-table-column>
          <el-table-column label="本地编码" min-width="150" prop="localSpaceCode" show-overflow-tooltip> </el-table-column>
          <el-table-column label="功能类型" min-width="150" prop="functionDictName" show-overflow-tooltip> </el-table-column>
          <el-table-column label="归属部门" min-width="150" prop="dmName" show-overflow-tooltip> </el-table-column>
          <el-table-column label="空间责任人" width="150" prop="principalName" show-overflow-tooltip> </el-table-column>
          <el-table-column label="操作人" width="150" prop="opretionName" show-overflow-tooltip> </el-table-column>
          <el-table-column label="时间" width="150" prop="opretionTime" show-overflow-tooltip> </el-table-column>
        </el-table>
      </div>
      <el-pagination
        :current-page="pagination.current"
        :page-sizes="[15, 30, 50, 100]"
        :page-size="pagination.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>

    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">取消</el-button>
    </div>
  </PageContainer>
</template>

<script>
export default {
  name: 'spaceRecord',
  data() {
    return {
      tableData: [],
      pagination: {
        current: 1,
        size: 15
      }, // 分页数据
      total: 0
    }
  },
  mounted() {
    this.unitListFn()
  },
  methods: {
    //  获取部门列表
    unitListFn() {
      let params = {
        ...this.pagination,
        spaceIds: this.$route.query.id
      }
      this.$api.getPageList(params).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.records
          this.total = res.data.total
        }
      })
    },
    handleSizeChange(val) {
      this.pagination.current = 1
      this.pagination.size = val
      this.unitListFn()
    },
    handleCurrentChange(val) {
      this.pagination.current = val
      this.unitListFn()
    }
  }
}
</script>

<style lang="scss" scoped>
.whole {
  width: 100%;
  height: 100%;
  padding: 15px;
  background: #fff;
}

.color_blue {
  color: #5188fc;
  cursor: pointer;
}
</style>
