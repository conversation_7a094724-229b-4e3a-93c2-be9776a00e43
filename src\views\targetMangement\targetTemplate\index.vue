<template>
  <PageContainer>
    <div slot="header">
      <div class="searchForm">
        <div class="search-box">
          <el-input v-model="searchForm.name" clearable filterable placeholder="请输入模板名称"></el-input>
          <el-select v-model="searchForm.type" placeholder="考察对象类型" style="margin-left: 16px">
            <el-option v-for="item in typeList" :key="item.node" :label="item.nodeName" :value="item.node"> </el-option>
          </el-select>
          <el-select v-model="searchForm.state" placeholder="引用状态" style="margin-left: 16px">
            <el-option label="未引用" :value="0"> </el-option>
            <el-option label="已引用" :value="1"> </el-option>
          </el-select>
          <div style="margin-left: 16px">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button type="primary" plain @click="reset">重置</el-button>
          </div>
        </div>
      </div>
    </div>
    <div slot="content" ref="contentRef" class="table-box">
      <div class="tableContainer">
        <el-button type="primary" style="margin-bottom: 12px" @click="update()">添加模版</el-button>
        <el-table v-loading="tableLoading" height="calc(100% - 96px)" :data="list" border stripe style="width: 100%">
          <el-table-column label="模板名称" prop="name" :resizable="false" align="center"></el-table-column>
          <el-table-column label="考察对象类型" prop="typeName" :resizable="false" align="center"></el-table-column>
          <el-table-column label="引用状态" prop="state" :resizable="false" align="center">
            <template slot-scope="scope">
              {{ scope.row.state === 0 ? '未引用' : '已引用' }}
            </template>
          </el-table-column>
          <el-table-column label="考察说明" prop="remark" :resizable="false" align="center"
            show-overflow-tooltip></el-table-column>
          <el-table-column label="操作" :resizable="false" align="center">
            <template slot-scope="scope">
              <el-button type="text" @click="viewDetail(scope.row)">查看</el-button>
              <el-button type="text" :disabled="scope.row.state === 1" @click="update(scope.row)">编辑</el-button>
              <el-button type="text" :disabled="scope.row.state === 1" @click="del(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination :current-page="pagination.page" :page-sizes="[15, 30, 50, 100]" :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper" :total="pagination.total" @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
        </el-pagination>
      </div>
    </div>
  </PageContainer>
</template>
<script>
export default {
  name: 'targetTemplate',
  data() {
    return {
      tableLoading: false,
      list: [],
      searchForm: {
        name: '',
        type: '',
        state: ''
      },
      typeList: [],
      pagination: {
        pageSize: 15,
        page: 1,
        total: 0
      }
    }
  },
  mounted() {
    this.getTypeList()
    this.getList()
  },
  methods: {
    getTypeList() {
      this.$api.templateType().then((res) => {
        if (res.code === '200') {
          this.typeList = res.data
        }
      })
    },
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.pagination.page = 1
      this.getList()
    },
    getList() {
      this.$api.templateManageList({ ...this.searchForm, ...this.pagination }).then((res) => {
        if (res.code === '200') {
          this.list = res.data.records
          this.pagination.total = res.data.total
        }
      })
    },
    handleCurrentChange(val) {
      this.pagination.page = val
      this.getList()
    },

    search() {
      this.pagination.page = 1
      this.getList()
    },
    reset() {
      this.searchForm = {
        name: '',
        type: '',
        state: ''
      }
      this.pagination.page = 1
      this.pagination.total = 0
      this.getList()
    },
    viewDetail(row) {
      this.$router.push({ path: '/targetTemplate/updateTemplate', query: { id: row.id, type: 'detail' } })
    },
    // 新增编辑
    update(row) {
      if (row && row.state === 1) {
        this.$alert('该模板管理已被引用，不可以编辑', '提示', {
          cancelButtonText: '确定',
          callback: (action) => { }
        })
      } else {
        this.$router.push({ path: '/targetTemplate/updateTemplate', query: row ? { id: row.id } : {} })
      }
    },
    del(row) {
      if (row.state === 1) {
        this.$alert('该模板管理已被引用，不可以删除', '提示', {
          cancelButtonText: '确定',
          callback: (action) => { }
        })
      } else {
        this.$confirm('是否确定删除该模板？', '提示', {
          cancelButtonText: '取消',
          confirmButtonText: '确定',
          type: 'warning'
        })
          .then(() => {
            this.$api.delTemplateById({ id: row.id }, { 'operation-type': 3, 'operation-name': row.name, 'operation-id': row.id }).then((res) => {
              if (res.code === '200') {
                this.$message.success('删除成功')
                this.getList()
              }
            })
          })
          .catch(() => { })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.searchForm {
  display: flex;
  height: 80px;
  padding: 0 16px;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .el-input {
    width: 200px;
    margin-left: 16px;
  }

  > div {
    margin-right: 20px;
  }
}

.search-box {
  display: flex;
  align-items: center;
}

.container-content > div {
  height: 100%;
  margin-top: 16px;
}

.el-table {
  margin-bottom: 12px;
}

::v-deep .el-table__row:hover {
  cursor: pointer;
}

.table-box {
  background-color: #fff;
  height: 100%;
  padding: 16px;
  .tableContainer {
    height: 100%;
    .el-table {
      height: calc(100% - 96px) !important;
    }
  }
}
</style>
