<template>
  <PageContainer>
    <div slot="content" class="elevator-content">
      <div v-if="!elevatorDetailShow" ref="largeScreenMonitoring" class="largeScreenMonitoring">
        <div class="screen_btn">
          <el-button type="primary" plain @click="fullScreen('largeScreenMonitoring')">{{ isFullScreen ? '退出全屏' : '全屏' }}</el-button>
        </div>
        <div class="largeScreen_content">
          <div class="content_left">
            <div class="left_top">
              <div class="top_count">
                <div class="vertical_count">
                  <div class="count_title">直梯数量</div>
                  <div class="count_num">
                    <img src='@/assets/images/monitor/ic-vertical.png' alt="" /><span
                      ><span>{{ elevatorList[0].connectNum }}</span
                      >/{{ elevatorList[0].totalNum }}</span
                    >
                  </div>
                </div>
                <div class="escalator_count">
                  <div class="count_title">扶梯数量</div>
                  <div class="count_num">
                    <img src='@/assets/images/monitor/ic-escalator.png' alt="" /><span
                      ><span>{{ elevatorList[1].connectNum }}</span
                      >/{{ elevatorList[1].totalNum }}</span
                    >
                  </div>
                </div>
                <div class="warn_count">
                  <div class="count_title">报警数量</div>
                  <div class="count_num">
                    {{ elevatorOthersData.alarmCount }}
                  </div>
                </div>
              </div>
              <div class="top_hour">
                <div class="run_time">
                  <p>{{ elevatorOthersData.runTime ?? 0 }}</p>
                  <p>运行时长(小时)</p>
                </div>
                <div class="run_distance">
                  <p>{{ elevatorOthersData.runDistance ?? 0 }}</p>
                  <p>运行距离(米)</p>
                </div>
              </div>
              <div class="top_num">
                <div class="run_time">
                  <p>{{ elevatorOthersData.runNum ?? 0 }}</p>
                  <p>运行次数(次)</p>
                </div>
                <div class="run_distance">
                  <p>{{ elevatorOthersData.openCount ?? 0 }}</p>
                  <p>开门次数(次)</p>
                </div>
              </div>
            </div>
            <div class="left_center">
              <div>
                <div class="echarts_title">电梯品牌分布</div>
                <div v-if="!elevatorBrandShow" class="echart-null">
                  <img src="@/assets/images/null.png" alt="" />
                  <div>暂无数据~</div>
                </div>
                <div v-else style="width: 100%; height: calc(100% - 20px);">
                  <div id="elevatorBrandPieEcharts"></div>
                </div>
              </div>
              <div>
                <div class="echarts_title">使用年限分布</div>
                <div v-if="!serviceLifeShow" class="echart-null">
                  <img src="@/assets/images/null.png" alt="" />
                  <div>暂无数据~</div>
                </div>
                <div v-else style="width: 100%; height: calc(100% - 20px);">
                  <div id="serviceLifeBarEcharts"></div>
                </div>
              </div>
            </div>
            <div class="left_bottom">
              <div class="echarts_title" style="margin-left: 23%; text-align: left;">报警类型分析</div>
              <div v-if="!alarmTypeAnalysisShow" class="echart-null">
                <img src="@/assets/images/null.png" alt="" />
                <div>暂无数据~</div>
              </div>
              <div v-else style="width: 100%; height: calc(100% - 20px);">
                <div id="alarmTypeAnalysisPieEcharts"></div>
              </div>
            </div>
          </div>
          <div class="content_center">
            <div ref="centerU3d" class="center_u3d">
              <iframe ref="unityWebglIframe" style="width: 100%; height: 100%; z-index: 9;" :src="ElevatorWebURL" frameborder="0"></iframe>
              <div v-show="paramsBoxShow" ref="paramsBox" class="params_box" @mouseenter="paramsBoxMouseEnter" @mouseleave="paramsBoxMouseLeave">
                <div class="params_box_title">
                  <span>{{ paramsBoxData.doorName }}</span>
                  <span v-if="hasParamsBoxData.show" @click="elevatorDetail(paramsBoxData.surveyEntityCode)">更多</span>
                </div>
                <div v-if="hasParamsBoxData.show" class="params_box_content">
                  <div>
                    <span>运行状态：</span>
                    <span>{{ paramsBoxData.runningState }}</span>
                  </div>
                  <div>
                    <span>当前楼层：</span>
                    <span>{{ paramsBoxData.currentFloor }}</span>
                  </div>
                  <div>
                    <span>运行速度：</span>
                    <span>{{ paramsBoxData.currentSpeed }}</span>
                  </div>
                  <div>
                    <span>是否载人：</span>
                    <span>{{ paramsBoxData.hasManned }}</span>
                  </div>
                  <div>
                    <span>梯门状态：</span>
                    <span>{{ paramsBoxData.doorStatus }}</span>
                  </div>
                </div>
                <div v-else class="params_box_content">
                  <div>
                    <span>{{ hasParamsBoxData.message }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="center_table">
              <el-table
                v-loading="tableLoading"
                class="table-center-transfer"
                :data="tableData"
                height="100%"
                :cell-style="$tools.setCell(3)"
                :header-cell-style="$tools.setHeaderCell(3)"
                style="width: 100%;"
              >
                <el-table-column fixed prop="iphPoliceTime" show-overflow-tooltip label="报警时间"></el-table-column>
                <el-table-column fixed prop="iphSurveyName" show-overflow-tooltip label="设备"></el-table-column>
                <el-table-column fixed prop="iphParameterName" show-overflow-tooltip label="报警类型"></el-table-column>
                <el-table-column fixed prop="disposeResultName" show-overflow-tooltip label="处置状态">
                  <template slot-scope="scope">
                    <div :style="scope.row.iphDisposeResult == '0' ? 'color:#5188fc' : ''">
                      {{ scope.row.disposeResultName }}
                    </div>
                  </template></el-table-column
                >
              </el-table>
            </div>
          </div>
          <div class="content_right">
            <div class="all_bar_box">
              <div v-for="item in keyList" :key="item.dom">
                <div class="echarts_title">
                  {{ item.title }}
                </div>
                <div v-if="!item.show" class="echart-null">
                  <img src="@/assets/images/null.png" alt="" />
                  <div>暂无数据~</div>
                </div>
                <div v-else style="width: 100%; height: calc(100% - 20px);">
                  <div :id="item.dom"></div>
                </div>
              </div>
            </div>
            <div class="elevator_health">
              <div class="echarts_title" style="margin-left: 10%; text-align: left;">电梯健康分布</div>
              <div class="health_level_box">
                <div
                  v-for="(item, index) in healthLevelData"
                  :key="index"
                  class="health_level"
                  :style="{
                    background: item.bgColor,
                    color: item.color,
                    border: `1px solid ${item.border}`
                  }"
                >
                  <div class="health_level_title">{{ item.levelNum }}</div>
                  <div class="health_level_num">{{ item.levelName }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <template v-else>
        <elevatorDetail :elevatorDetailShow="elevatorDetailShow" :dialogData="elevatorData" @closeElevator="closeElevator" />
      </template>
    </div>
  </PageContainer>
</template>
<script>
import elevatorDetail from './components/elevatorDetail'
import { monitorTypeList } from '@/util/dict.js'
import * as echarts from 'echarts'
export default {
  name: 'elevatorOverview',
  components: {
    elevatorDetail
  },
  data() {
    return {
      requestHttp: __PATH.VUE_IEMC_ELEVATOR_API,
      // ElevatorWebURL: import.meta.env + 'ElevatorWeb/index.html',
      ElevatorWebURL: '',
      elevatorData: {}, // 电梯详情数据
      elevatorDetailShow: false, // 电梯详情页面
      elevatorBrandShow: false,
      serviceLifeShow: false,
      alarmTypeAnalysisShow: false,
      isFullScreen: false, // 是否全屏
      keyList: [
        {
          dom: 'timeRankingBarEcharts',
          show: true,
          title: '运行时间排行',
          type: '电梯运行时间',
          color: '#5188FC'
        },
        {
          dom: 'floorRankingBarEcharts',
          show: true,
          title: '运行楼层排行',
          type: '运行楼层',
          color: '#F6B43A'
        },
        {
          dom: 'heightRankingBarEcharts',
          show: true,
          title: '运行距离排行',
          type: '电梯运行距离',
          color: '#29BEBC'
        },
        {
          dom: 'numsRankingBarEcharts',
          show: true,
          title: '开门次数排行',
          type: '电梯开门次数',
          color: '#F18080'
        }
      ],
      healthLevelData: [
        {
          levelNum: 0,
          levelName: 'A级',
          levelType: 'A',
          bgColor: '#FFEFEF',
          color: '#D20101',
          border: '#FFDBDB'
        },
        {
          levelNum: 0,
          levelName: 'B级',
          levelType: 'B',
          bgColor: '#FFF3E7',
          color: '#FF8A26',
          border: '#FFDDBA'
        },
        {
          levelNum: 0,
          levelName: 'C级',
          levelType: 'C',
          bgColor: '#F5F8FF',
          color: '#5188FC',
          border: '#D9E6FF'
        },
        {
          levelNum: 0,
          levelName: 'D级',
          levelType: 'D',
          bgColor: '#F2FFF9',
          color: '#0B854D',
          border: '#C8EEDC'
        }
      ],
      tableData: [],
      tableLoading: false,
      elevatorList: [
        {
          type: '1',
          connectNum: 0,
          totalNum: 0
        },
        {
          type: '2',
          connectNum: 0,
          totalNum: 0
        }
        // {
        //   type: "warnCount",
        //   countNum: 0,
        // },
      ],
      elevatorOthersData: {
        openCount: '0',
        runDistance: '0',
        runNum: '0',
        runTime: '0',
        alarmCount: '0'
      },
      u3dMouse: {
        x: 0,
        y: 0
      },
      projectCode: monitorTypeList.find(item => item.projectName == '电梯监测').projectCode, // 天梯code
      paramsBoxShow: false,
      paramsBoxData: {}, // 模型返回数据
      boxDataTimer: null,
      paramsBoxMouseFocus: false,
      hasParamsBoxData: {
        show: false,
        message: null
      } // 是否有该监测项
    }
  },
  // 监听侧边栏缩放 动态改变echarts图表大小
  watch: {
    '$store.state.settings.sidebarCollapse': {
      handler(val) {
        this.$nextTick(() => {
          let echartsDom = [
            'elevatorBrandPieEcharts',
            'serviceLifeBarEcharts',
            'alarmTypeAnalysisPieEcharts',
            'timeRankingBarEcharts',
            'floorRankingBarEcharts',
            'heightRankingBarEcharts',
            'numsRankingBarEcharts'
          ]
          setTimeout(() => {
            echartsDom.forEach((item) => {
              if (document.getElementById(item)) {
                echarts.init(document.getElementById(item)).resize()
              }
            })
          }, 250)
        })
      },
      deep: true
    }
  },
  mounted() {
    // this.ElevatorWebURL = import.meta.env.BASE_URL + 'ElevatorWeb/index.html'
    this.mountendInit()
    window.addEventListener('message', this.receiveMessage)
    window.onresize = () => {
      // 可视区域的高度
      const clientHeight = document.documentElement.clientHeight || document.body.clientHeight
      // screen是window的属性方法，window.screen可省略window，指的是窗口
      this.isFullScreen = screen.height == clientHeight
    }
    // setTimeout(() => {
    //   this.setWebsocket()
    // }, 10000)
  },
  methods: {
    // 初始化渲染
    mountendInit() {
      this.getIaasStatistics()
      this.getAlarmTypeAnalysisData()
      this.getTimeRankingData()
      this.getElevatorStatistics()
      this.getElevatorFloorList()
      this.getData()
    },
    // 获取电梯类型数据 左上角统计 及电梯健康分布数据
    getElevatorStatistics() {
      const params = {
        projectCode: this.projectCode
      }
      this.$api.getElevatorStatistics(params, this.requestHttp).then((res) => {
        if (res.code === '200') {
          this.elevatorStatisticsData = res.data
          const { list, recordList, ...othersData } = res.data
          // 直梯扶梯数量渲染
          this.elevatorList.map((e) => {
            const filterData = list.find((item) => item.elevatorType === e.type)
            if (filterData) {
              e.connectNum = filterData.connectNum
              e.totalNum = filterData.totalNum
            }
          })
          // 其他统计项渲染
          this.elevatorOthersData = othersData
          // 电梯健康分布渲染
          this.healthLevelData.map((e) => {
            const filterData = recordList?.find((item) => item.record === e.levelType) ?? {}
            e.levelNum = filterData?.num ?? 0
          })
        }
      })
    },
    getIaasStatistics() {
      this.$api.getIaasStatistics({}, this.requestHttp).then((res) => {
        if (res.code === '200') {
          // 获取电梯品牌数据
          if (res.data.hasOwnProperty('brand')) {
            this.elevatorBrandShow = true
            this.$nextTick(() => {
              this.setElevatorBrandEcharts(res.data.brand)
            })
          } else {
            this.elevatorBrandShow = false
          }
          // 获取使用年限数据
          if (res.data.hasOwnProperty('life')) {
            this.serviceLifeShow = true
            this.$nextTick(() => {
              this.setServiceLifeEcharts(res.data.life)
            })
          } else {
            this.serviceLifeShow = false
          }
        }
      })
    },
    // 获取报警类型数据
    getAlarmTypeAnalysisData() {
      this.$api
        .getReasonStatisticPie({
          projectCode: this.projectCode
        }, this.requestHttp)
        .then((res) => {
          this.alarmTypeAnalysisShow = true
          if (res.code == 200) {
            this.$nextTick(() => {
              this.setAlarmTypeAnalysisEcharts(res.data)
            })
            if (res.data.length > 0) {
              this.alarmTypeAnalysisShow = true
            } else {
              this.alarmTypeAnalysisShow = false
            }
          }
        })
    },
    // 获取运行时间 楼层 距离 次数 排行
    getTimeRankingData() {
      const data = {
        // entityMenuCode: "",
        projectCode: this.projectCode,
        parameterIds: '2639,2634,2641',
        timeType: null
      }
      this.$api.getElevatorMonitoringList(data, this.requestHttp).then((res) => {
        if (res.code === '200') {
          this.keyList.forEach((item) => {
            // 楼层接口 单独执行
            if (item.dom == 'floorRankingBarEcharts') {
              return
            }
            this.$nextTick(() => {
              const chartdata = res.data?.find((e) => e.type == item.type)?.map
              item.show = (chartdata && Object.keys(chartdata).length > 0) ?? false
              this.setRankingBarEcharts(item.dom, chartdata?.yAxisData ?? [], chartdata?.seriesData ?? [], item.color)
            })
          })
        }
      })
      // const nameList = chartdata.map((item) => item.name);
    },
    getElevatorFloorList() {
      let data = {
        projectCode: this.projectCode,
        parameterIds: '2700'
      }
      this.$api.getElevatorFloorList(data, this.requestHttp).then((res) => {
        if (res.code === '200') {
          this.keyList.forEach((item) => {
            if (item.dom == 'floorRankingBarEcharts') {
              this.$nextTick(() => {
                const chartdata = res.data.columnAsCount
                item.show = (chartdata && Object.keys(chartdata).length > 0) ?? false
                this.setRankingBarEcharts(item.dom, chartdata?.yAxisData ?? [], chartdata?.seriesData ?? [], item.color)
                // this.setRankingBarEcharts(
                //   item.dom,
                //   ['1号电梯', '2号电梯', '3号电梯', '4号电梯', '5号电梯', '6号电梯', '7号电梯', '8号电梯', '9号电梯', '10号电梯', '11号电梯', '12号电梯', '13号电梯', '14号电梯', '15号电梯', '16号电梯', '17号电梯', '18号电梯', '19号电梯', '20号电梯', '21号电梯', '22号电梯', '23号电梯', '24号电梯', '25号电梯', '26号电梯', '27号电梯', '28号电梯', '29号电梯', '30号电梯', '31号电梯', '32号电梯', '33号电梯', '34号电梯', '35号电梯'],
                //   ['3152', '4152', '5152', '6152', '7152', '8152', '9152', '13152', '12152', '13152', '33152', '23152', '53152', '63152', '13152', '3152', '3152', '3152', '3152', '3152', '3152', '3152', '3152', '3152', '3152', '3152', '3152', '3152', '3152', '3152', '3152', '3152', '3152'],
                //   item.color
                // );
              })
            }
          })
        }
      })
    },
    // 电梯品牌数据echarts
    setElevatorBrandEcharts(data) {
      const getchart = echarts.init(document.getElementById('elevatorBrandPieEcharts'))
      const sum = data.totalNum
      const gap = (1 * sum) / 100
      const pieData = []
      const gapData = {
        name: '',
        value: gap,
        itemStyle: {
          color: 'transparent'
        }
      }
      for (let i = 0; i < data.brandPic.length; i++) {
        pieData.push({
          name: data.brandPic[i].name,
          value: data.brandPic[i].value,
          itemStyle: {
            normal: {
              borderRadius: 5
            }
          }
        })
        pieData.push(gapData)
      }
      let option = {
        tooltip: {
          trigger: 'item',
          // backgroundColor: "rgba(0, 0, 0, 0.1)",
          formatter: function (params) {
            return params.marker + params.name + ' : ' + params.value + '    (' + ((params.value / sum) * 100).toFixed(2) + '%)'
          }
        },
        title: [
          {
            text: sum,
            x: '47%',
            y: '50%',
            textAlign: 'center',
            textStyle: {
              fontSize: '24',
              fontWeight: '500',
              color: '#353535',
              textAlign: 'center'
            }
          },
          {
            text: '总数',
            left: '48%',
            top: '37%',
            textAlign: 'center',
            textStyle: {
              fontSize: '16',
              fontWeight: '400',
              color: '#A1A9BC',
              textAlign: 'center'
            }
          }
        ],
        series: [
          {
            type: 'pie',
            roundCap: true,
            radius: ['60%', '72%'],
            center: ['50%', '50%'],
            label: {
              show: false
            },
            labelLine: {
              show: false
            },
            data: pieData
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 使用年限数据echarts
    setServiceLifeEcharts(data) {
      const getchart = echarts.init(document.getElementById('serviceLifeBarEcharts'))
      const nameList = data.xserial
      const valueList = data.yserial
      // 数组求和
      const total = eval(valueList.join('+'))
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '5%',
          right: '15%',
          top: '10%',
          bottom: '5%',
          containLabel: true
        },
        xAxis: [
          {
            name: '年',
            type: 'category',
            data: nameList,
            axisTick: { show: false }
          }
        ],
        yAxis: [
          {
            type: 'value'
          }
        ],
        series: [
          {
            type: 'bar',
            barWidth: 20,
            data: valueList,
            itemStyle: {
              normal: {
                color: function (params) {
                  var colorList = ['#F18080', '#5188FC', '#FFC050', '#29BEBC']
                  return colorList[params.dataIndex]
                },
                label: {
                  show: true,
                  position: 'top',
                  formatter: (params) => {
                    var text
                    text = ((params.data * 100) / total).toFixed(2) + '%'
                    return text
                  }
                }
              }
            }
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 报警类型数据echarts
    setAlarmTypeAnalysisEcharts(data) {
      const getchart = echarts.init(document.getElementById('alarmTypeAnalysisPieEcharts'))
      const nameList = Array.from(data, (item) => item.name)
      const sum = data.reduce((per, cur) => per + cur.value, 0)
      const gap = (1 * sum) / 100
      const pieData = []
      const gapData = {
        name: '',
        value: gap,
        itemStyle: {
          color: 'transparent'
        }
      }
      for (let i = 0; i < data.length; i++) {
        pieData.push({
          ...data[i],
          itemStyle: {
            normal: {
              borderRadius: 5
            }
          }
        })
        pieData.push(gapData)
      }
      let that = this
      let option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          type: 'scroll',
          orient: 'vertical',
          top: 'center',
          left: 'right',
          pageIconColor: '#5188fc', // 激活的分页按钮颜色
          pageIconInactiveColor: '#c2c6ce', // 没激活的分页按钮颜色
          data: nameList,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 16,
          textStyle: {
            color: '#353535' //  字体颜色
          },
          formatter: function (name) {
            var oa = option.series[0].data
            var num = oa.reduce((sum, e) => sum + e.value, 0)
            for (var i = 0; i < option.series[0].data.length; i++) {
              if (name === oa[i].name) {
                return (
                  ' ' +
                  (name.length > that.getLegendLength() ? name.substr(0, that.getLegendLength()) + '...' : name) +
                  ' (' +
                  oa[i].value +
                  ')   ' +
                  ((oa[i].value / num) * 100).toFixed(2) +
                  '%'
                )
              }
            }
          }
        },
        series: [
          {
            type: 'pie',
            roundCap: true,
            center: ['30%', '50%'],
            label: {
              show: false
            },
            labelLine: {
              show: false
            },
            data: pieData
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 电梯4个状态数据echarts
    setRankingBarEcharts(dom, nameList, valueList, color) {
      const getchart = echarts.init(document.getElementById(dom))
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '6%',
          right: '12%',
          top: '6%',
          bottom: '5%',
          containLabel: true
        },
        yAxis: [
          {
            type: 'category',
            data: nameList,
            axisTick: { show: false },
            axisLabel: {
              margin: 8,
              interval: 0,
              hideOverlap: true, // 隐藏互相遮挡的文本标签
              formatter: function (val) {
                var strs = val.split('') // 字符串数组
                var str = ''
                // strs循环 forEach 每五个字符增加换行符
                strs.forEach((item, index) => {
                  if (index % 6 === 0 && index !== 0) {
                    str += '\n'
                  }
                  str += item
                })
                return str
              }
            }
          }
        ],
        xAxis: [
          {
            type: 'value',
            position: 'top',
            splitNumber: 3,
            axisLabel: {
              fontSize: 11,
              interval: 30000,
              hideOverlap: true // 隐藏互相遮挡的文本标签
              //   margin: 8,
              //   interval: 0,
              //   rotate: -20,
            }
          }
        ],
        series: [
          {
            type: 'bar',
            barWidth: 8,
            // barGap: 0,
            data: valueList,
            itemStyle: {
              normal: {
                color: color
              }
            }
          }
        ],
        dataZoom: [
          {
            type: 'slider',
            show: true,
            // 设置组件控制的y轴
            yAxisIndex: 0,
            right: 4,
            start: 0,
            end: 20,
            width: 10,
            borderRadius: 0,
            borderColor: '#D7DEE8',
            fillerColor: '#CDD5E1', // 滑动块的颜色
            // backgroundColor: "#33384b", //两边未选中的滑动条区域的颜色
            // 是否显示detail，即拖拽时候显示详细数值信息
            showDetail: false,
            zoomLock: false,
            brushSelect: false,
            // 控制手柄的尺寸
            handleSize: 12,
            // 是否在 dataZoom-silder 组件中显示数据阴影。数据阴影可以简单地反应数据走势。
            showDataShadow: false,
            // filterMode: 'filter',
            handleIcon: 'M0,0 v11h3 v-11h-3 Z',
            handleStyle: {
              color: '#FFF',
              shadowOffsetX: 0, // 阴影偏移x轴多少
              shadowOffsetY: 0 // 阴影偏移y轴多少
              // borderCap: 'square',
              // borderColor: '#D8DFE9',
              // borderType: [15, 20],
            }
          },
          {
            type: 'inside',
            // show: false,
            yAxisIndex: [0],
            zoomOnMouseWheel: false, // 关闭滚轮缩放
            moveOnMouseWheel: true, // 开启滚轮平移
            moveOnMouseMove: true // 鼠标移动能触发数据窗口平移
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 报警列表
    getData() {
      this.tableLoading = true
      this.$api
        .getStaticPoliceList({
          projectCode: this.projectCode,
          pageNo: 1,
          pageSize: 30,
          startTime: '',
          endTime: ''
        }, this.requestHttp)
        .then((res) => {
          this.tableLoading = false
          if (res.code == 200) {
            this.tableData = res.data.list
          }
        })
    },
    // 根据选中模型获取电梯数据
    getParamsBoxData(data) {
      this.paramsBoxData = {
        surveyEntityCode: '',
        doorName: '',
        runningState: '',
        currentFloor: null,
        currentSpeed: '',
        hasManned: '',
        doorStatus: ''
      }
      if (this.boxDataTimer) {
        clearTimeout(this.boxDataTimer)
      }
      this.hasParamsBoxData = {
        show: false,
        message: null
      }
      this.paramsBoxShow = true
      this.boxDataTimer = setTimeout(() => {
        const params = {
          projectCode: this.projectCode,
          sensorNo: data.ElevatorID
        }
        this.$api.getElevatorParticulars(params, this.requestHttp).then((res) => {
          if (res.code === '200') {
            this.boxDataTimer = null
            const data = res.data?.resRealTable ?? false
            if (!data) {
              this.hasParamsBoxData = {
                show: false,
                message: res.message
              }
            } else {
              this.hasParamsBoxData = {
                show: true,
                message: null
              }
              this.paramsBoxData = {
                surveyEntityCode: data.surveyEntityCode,
                doorName: data.surveyEntityName,
                runningState: this.getParameterDataById(data.parameterList, '2741', ['parameterValue']) ?? '停止',
                currentFloor: this.getParameterDataById(data.parameterList, '2700', ['parameterValue']) ?? '1',
                currentSpeed: this.getParameterDataById(data.parameterList, '2711', ['parameterValue', 'parameterUnit']) ?? '0m/s',
                hasManned: this.getParameterDataById(data.parameterList, '2745', ['parameterValue']) ?? '',
                doorStatus: this.getParameterDataById(data.parameterList, '2713', ['parameterValue']) ?? ''
              }
            }
          }
        })
      }, 500)
    },
    getParameterDataById(data, id, field) {
      const findData = data?.find((e) => e.parameterId == id) ?? {}
      let str = ''
      field.forEach((item) => {
        str += findData[item] ?? ''
      })
      return str
    },
    // 查看电梯详情
    elevatorDetail(val) {
      // 跳转详情重置 选中状态
      this.isFullScreen = false
      this.paramsBoxShow = false
      this.boxDataTimer = null
      // 跳转传参
      this.elevatorData = {
        projectCode: this.projectCode,
        surveyEntityCode: val
      }
      this.elevatorDetailShow = true
    },
    // 初始化获取所有电梯状态数据
    getElevatorParamsList() {
      this.$api
        .getRealMonitoringListOld({
          projectCode: this.projectCode,
          isHistory: 0,
          page: 1,
          pageSize: 999
        }, this.requestHttp)
        .then((res) => {
          if (res.code == 200) {
            if (res.data.list) {
              // 增加延时 等待模型加载完成后再渲染
              setTimeout(() => {
                this.sendMessage(res.data.list)
              }, 4000)
            }
          }
        })
    },
    // 发送指令
    sendMessage(data) {
      const stateList = [
        {
          name: '停止',
          value: 0
        },
        {
          name: '上行',
          value: 1
        },
        {
          name: '下行',
          value: 2
        }
      ]
      const params = data.map((e) => {
        const state = e.parameterList?.find((e) => e.parameterId == '2741')?.parameterValue ?? '停止'
        return {
          elevatorName: e.surveyEntityName,
          elevatorCode: e.surveyEntityNo,
          elevatorId: e.surveyEntityCode,
          currentFloor: e.parameterList?.find((e) => e.parameterId == '2700')?.parameterValue ?? 1,
          runningState: stateList.find((item) => item.name === state).value
        }
      })
      console.log(params, '发送的数据')
      const message = [
        {
          elevatorName: null, // 监测实体名称
          elevatorId: null, // 电梯ID
          elevatorCode: '01_01', // 电梯模型编号
          currentFloor: -1, // 当前楼层 负楼层则为-1 -2 -3
          runningState: 1 // 运行状态 0 停止 1 上行 2 下行
        },
        {
          elevatorName: null,
          elevatorId: null,
          elevatorCode: '01_07',
          currentFloor: -2,
          runningState: 2
        }
        // {
        //   elevatorName: null,
        //   elevatorId: null,
        //   elevatorCode: "01_28",
        //   currentFloor: 8,
        //   runningState: null,
        // }
      ]
      // console.log(params)
      this.$nextTick(() => {
        try {
          this.$refs.unityWebglIframe.contentWindow.SendData(JSON.stringify(params))
        } catch (error) {
          console.log(error, 'unityWebglIframe')
        }
      })
    },
    // 接收消息
    receiveMessage(msg) {
      // 鼠标移入电梯井返回数据
      if (msg.data && msg.data.cmd === 'webglMessage') {
        const data = JSON.parse(msg.data.params)
        console.log(data, '接收的数据')
        // 为1表示移入电梯选中
        if (data.IsSelected == 1) {
          // 根据返回值获取选中电梯数据
          this.getParamsBoxData(data)
          this.$nextTick(() => {
            this.$refs.paramsBox.style.left = this.u3dMouse.X + 1 + 'px'
            this.$refs.paramsBox.style.top = this.u3dMouse.Y + 1 + 'px'
          })
        } else {
          // 为0表示移出电梯取消 如果移出电梯 但是鼠标悬停在 box上 则不取消
          if (!this.paramsBoxMouseFocus) {
            this.paramsBoxShow = false
            if (this.boxDataTimer) {
              clearTimeout(this.boxDataTimer)
            }
          }
        }
        // u3d是否渲染完成
      } else if (msg.data && msg.data.cmd === 'webglMessageFlag') {
        this.getElevatorParamsList()
        // 鼠标xy轴坐标
      } else if (msg.data && msg.data.cmd === 'coordinate') {
        this.u3dMouse = JSON.parse(msg.data.params)
        if (this.paramsBoxShow) {
          this.$nextTick(() => {
            this.$refs.paramsBox.style.left = this.u3dMouse.X + 1 + 'px'
            this.$refs.paramsBox.style.top = this.u3dMouse.Y + 1 + 'px'
          })
        }
      } else if (msg.data && msg.data.cmd === 'webglMessageScreen') {
        if (msg.data.params) {
          this.fullScreen('centerU3d')
        }
      }
    },
    // 全屏事件
    fullScreen(dom) {
      this.isFullScreen = !this.isFullScreen
      const full = this.$refs[dom]
      if (this.isFullScreen) {
        if (full.RequestFullScreen) {
          full.RequestFullScreen()
          // 兼容Firefox
        } else if (full.mozRequestFullScreen) {
          full.mozRequestFullScreen()
          // 兼容Chrome, Safari and Opera等
        } else if (full.webkitRequestFullScreen) {
          full.webkitRequestFullScreen()
          // 兼容IE/Edge
        } else if (full.msRequestFullscreen) {
          full.msRequestFullscreen()
        }
      } else {
        if (document.exitFullscreen) {
          document.exitFullscreen()
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen()
        } else if (document.webkitCancelFullScreen) {
          document.webkitCancelFullScreen()
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen()
        }
      }
    },
    // 电梯关闭事件
    closeElevator() {
      this.elevatorDetailShow = false
      this.mountendInit()
    },
    // 参数box 鼠标事件
    paramsBoxMouseEnter() {
      this.paramsBoxMouseFocus = true
    },
    paramsBoxMouseLeave() {
      this.paramsBoxMouseFocus = false
      this.paramsBoxShow = false
    },
    setWebsocket() {
      let websocketData = sessionStorage.getItem('websocket') ? JSON.parse(sessionStorage.getItem('websocket')) : {}
      // 电梯来消息执行操作
      if (websocketData.projectCode == this.projectCode) {
        console.log(websocketData)
        this.sendMessage([websocketData])
        // 收到消息刷新页面
      }
    },
    getLegendLength() {
      let legendLength = 0
      let innerHeight = window.innerHeight
      if (innerHeight < 768) {
        legendLength = 2
      }
      if (innerHeight >= 768 && 900 > innerHeight) {
        legendLength = 3
      }
      if (innerHeight >= 900 && 1080 > innerHeight) {
        legendLength = 5
      }
      if (innerHeight >= 1080) {
        legendLength = 5
      }
      return legendLength
    }
  }
}
</script>
<style lang="scss" scoped>
.elevator-content {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;

  .largeScreenMonitoring {
    width: 100%;
    height: 100%;
    background: #fff;
    overflow: hidden;

    .screen_btn {
      height: 50px;
      text-align: right;
      padding-top: 8px;
      padding-right: 8px;

      .el-button {
        min-width: 55px;
        height: 32px;
      }
    }

    .largeScreen_content {
      height: calc(100% - 50px);
      min-height: 728px - 50px;
      padding: 0 20px;
      display: flex;
      justify-content: space-between;

      .content_left {
        width: 27%;
        height: 100%;

        .left_top {
          height: 32%;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .top_count {
            display: flex;
            justify-content: space-between;
            height: calc(40% - 15px);

            .vertical_count,
            .escalator_count {
              width: calc(40% - 5px);
              height: 100%;
              background: #5188fc;
              border-radius: 10px;
              display: flex;
              flex-direction: column;
              justify-content: space-evenly;
              padding: 0 20px;

              .count_title {
                font-size: 14px;
                font-family: PingFang-SC-Medium, PingFang-SC;
                font-weight: 500;
                color: #fff;
              }

              .count_num {
                display: flex;
                justify-content: space-between;
                align-items: center;

                img {
                  width: 30px;
                  height: 30px;
                }

                span {
                  font-size: 20px;
                  font-family: PingFang-SC-Medium, PingFang-SC;
                  font-weight: 500;
                  color: #fff;

                  span {
                    color: #ffd661;
                  }
                }
              }
            }

            .warn_count {
              width: calc(20% - 5px);
              height: 100%;
              background: #f26d6d;
              border-radius: 10px;
              display: flex;
              flex-direction: column;
              justify-content: space-evenly;
              text-align: center;
              color: #fff;

              .count_title {
                font-size: 14px;
              }

              .count_num {
                font-size: 20px;
              }
            }
          }

          .top_hour,
          .top_num {
            height: calc(30% - 5px);
            box-shadow: 0 1px 5px 0 rgb(190 196 204 / 50%);
            border-radius: 4px;
            display: flex;
            justify-content: space-evenly;

            .run_time,
            .run_distance {
              display: flex;
              flex-direction: column;
              justify-content: space-evenly;

              p {
                text-align: center;
                font-size: 14px;
                font-family: PingFangSC-Regular, "PingFang SC";
                font-weight: 400;
                color: #888;
                padding: 0;
                margin: 0;
              }

              :first-child {
                font-size: 26px;
                font-family: "HarmonyOS_Sans_SC_Medium";
                color: #5188fc;
              }
            }
          }
        }

        .left_center {
          height: 33%;
          display: flex;
          padding-top: 15px;

          > div {
            // flex: 1;
            width: 50%;
          }
        }

        .left_bottom {
          height: 35%;
          width: 100%;
        }
      }

      .content_center {
        width: 44%;
        height: 100%;

        .center_u3d {
          height: 65%;
          // background: #5188fc;
          position: relative;
          overflow: hidden;

          .fullscreen_box {
            // pointer-events: none;
            position: absolute;
            right: 0;
            bottom: 0;
            width: 45px;
            height: 45px;
            // background: #5188fc;
            display: flex;
            cursor: pointer;

            img {
              margin: auto;
              width: 40px;
              height: 40px;
            }

            z-index: 2;
          }

          .params_box {
            // pointer-events: none;
            position: absolute;
            // left: 10px;
            // top: 10px;
            width: 244px;
            height: 212px;
            background: url("~@/assets/images/monitor/ic-u3d-bg.png") no-repeat;
            background-size: 100% 100%;
            z-index: 999;

            .params_box_title {
              height: 35px;
              line-height: 35px;
              font-size: 14px;
              font-weight: 400;
              color: #fff;
              display: flex;
              justify-content: space-between;
              padding: 0 10px;

              > span:first-child {
                width: 70%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }

              > span:last-child {
                color: #1ffaff;
                cursor: pointer;
              }
            }

            .params_box_content {
              height: calc(100% - 35px);
              padding: 20px;
              display: flex;
              justify-content: space-between;
              flex-direction: column;

              > div {
                color: #fff;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
          }

          .u3d_mask {
            width: 100%;
            height: 100%;
            position: absolute;
            left: 0;
            top: 0;
            z-index: 10;
          }
        }

        .center_table {
          height: calc(35% - 15px);
          padding-top: 15px;
        }
      }

      .content_right {
        width: 27%;
        height: 100%;

        .all_bar_box {
          height: 65%;
          display: flex;
          flex-wrap: wrap;

          > div {
            width: 50%;
            height: 50%;
          }
        }

        .elevator_health {
          height: calc(35% - 15px);
          padding-top: 15px;

          .health_level_box {
            height: calc(100% - 20px);
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            align-content: space-between;
            padding: 20px 10px 0;

            .health_level {
              width: calc(50% - 10px);
              height: calc(50% - 6px);
              display: flex;
              flex-direction: column;
              border-radius: 4px;
              justify-content: space-evenly;
              box-shadow: 0 2px 12px -2px rgb(79 84 98 / 12%);

              .health_level_title {
                font-size: 24px;
                font-family: DIN-Bold, DIN;
                font-weight: bold;
                text-align: center;
              }

              .health_level_num {
                font-size: 16px;
                font-family: NotoSansHans-Regular, NotoSansHans;
                font-weight: 400;
                text-align: center;
              }
            }
          }
        }
      }

      .echarts_title {
        height: 20px;
        font-size: 15px;
        font-family: NotoSansHans-Medium, NotoSansHans;
        font-weight: 600;
        color: #393a3d;
        line-height: 20px;
        text-align: center;
      }

      .echart-null {
        margin: 0 auto;
        height: calc(100% - 20px);
        width: 50%;
        text-align: center;
        color: #8a8c8f;

        img {
          max-width: 100%;
          max-height: 100%;
        }

        div {
          font-size: 14px;
        }
      }

      #elevatorBrandPieEcharts,
      #serviceLifeBarEcharts,
      #alarmTypeAnalysisPieEcharts,
      #timeRankingBarEcharts,
      #floorRankingBarEcharts,
      #heightRankingBarEcharts,
      #numsRankingBarEcharts {
        width: 100%;
        height: 100%;
        z-index: 2;
      }
    }
  }
}
</style>
<style lang="scss">
.largeScreenMonitoring {
  .table-center-transfer {
    .el-table__fixed-header-wrapper .el-table__header thead {
      tr {
        background: center !important;

        .el-table__cell {
          border-right: none !important;

          .cell {
            font-size: 16px;
            // font-family: NotoSansHans-Medium, NotoSansHans;
            // font-weight: 500;
            // color: #647181;
          }
        }
      }
    }

    table.el-table__header thead th {
      background: transparent !important;
    }

    td.el-table__cell {
      border-bottom: 1px solid #d8dee7 !important;
    }

    .el-table__fixed-body-wrapper {
      border-left: 1px solid #d8dee7;
      border-right: 1px solid #d8dee7;
      width: calc(100% - 1px);
    }
  }
}
</style>
