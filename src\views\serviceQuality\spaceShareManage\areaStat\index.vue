<!-- 面积统计 -->
<template>
  <PageContainer class="area_stat">
    <template #content>
      <el-tabs v-model="currentTab" class="area_stat__nav" @tab-click="onTabClick">
        <el-tab-pane label="建筑" name="1"></el-tab-pane>
        <el-tab-pane label="科室" name="2"></el-tab-pane>
      </el-tabs>
      <div class="area_stat__search">
        <el-select v-if="currentTab == 2" ref="treeSelect" v-model="queryForm.baseId" placeholder="请选择" clearable @clear="handleClear">
          <el-option hidden :value="queryForm.baseId" :label="areaName"> </el-option>
          <el-tree :data="spaceList" :props="serverDefaultProps" :expand-on-click-node="false" :check-on-click-node="true" @node-click="handleNodeClick"> </el-tree>
        </el-select>
        <el-button style="float: right" type="primary" :loading="exportLoading" @click="exportExcel">导出</el-button>
      </div>
      <div class="area_stat__content">
        <div v-loading="tableLoading" class="content-table">
          <table v-if="currentTab == 1" border="1px splid #333" cellspacing="5" cellpadding="5">
            <thead>
              <tr style="height: 40px">
                <th style="width: 150px" colspan="18">龙华中心医院-建筑面积统计</th>
              </tr>
              <tr style="height: 40px">
                <th>院区</th>
                <th>楼栋</th>
                <th>楼层</th>
                <th>公摊面积（㎡）</th>
                <th>建筑面积（㎡）</th>
                <th>总面积（㎡）</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, index) in tableData" :key="index">
                <td>{{ item.hospital }}</td>
                <td>{{ item.building }}</td>
                <td>{{ item.floor }}</td>
                <td>{{ item.sharedArea }}</td>
                <td>{{ item.constructionArea }}</td>
                <td>{{ item.totalArea }}</td>
              </tr>
            </tbody>
          </table>
          <table v-else border="1px splid #333" cellspacing="5" cellpadding="5">
            <thead>
              <tr style="height: 40px">
                <th style="width: 150px" colspan="18">龙华中心医院-科室空间类型面积统计</th>
              </tr>
              <tr style="height: 40px">
                <th rowspan="2" style="width: 100px">楼栋</th>
                <th rowspan="2" style="width: 100px">楼层</th>
                <th rowspan="2" style="width: 150px">科室</th>
                <template v-if="tableData[0]">
                  <th v-for="(item, index) in tableData[0].details" :key="'th' + index" colspan="3" style="height: 40px">{{ item.paceType }}</th>
                </template>
              </tr>
              <tr v-if="tableData[0]">
                <template v-for="i in tableData[0].details">
                  <th style="height: 40px">公摊面积</th>
                  <th style="height: 40px">建筑面积</th>
                  <th style="height: 40px">总面积</th>
                </template>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, index) in tableData" :key="'tbody' + index">
                <td>{{ item.building }}</td>
                <td>{{ item.floor }}</td>
                <td>{{ item.department }}</td>
                <template v-for="v in item.details">
                  <td>{{ v.sharedArea }}</td>
                  <td>{{ v.constructionArea }}</td>
                  <td>{{ v.totalArea }}</td>
                </template>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </template>
  </PageContainer>
</template>
<script>
import { transData } from '@/util'
export default {
  name: 'areaStat',
  data() {
    return {
      currentTab: '1',
      queryForm: {
        baseId: ''
      },
      tableLoading: false,
      exportLoading: false,
      tableData: [],
      spaceList: [],
      serverDefaultProps: {
        label: 'ssmName',
        children: 'children'
      },
      areaName: ''
    }
  },
  computed: {},
  mounted() {
    this.spaceTreeListFn()
    this.getTableList()
  },
  methods: {
    // 选择下拉树 数据
    handleNodeClick(data) {
      this.queryForm.baseId = data.id
      this.areaName = data.ssmName
      this.$refs.treeSelect.blur()
      this.getTableList()
    },
    onTabClick(e) {
      this.currentTab = e.name
      this.getTableList()
    },
    exportExcel() {
      this.exportLoading = true
      let params = this.currentTab == 1 ? {} : { baseId: this.queryForm.baseId }
      this.$api[this.currentTab == 1 ? 'ExportShareAreaStatisticsExcel' : 'ExportDepartmentStatisticsExcel'](params)
        .then((res) => {
          let url = window.URL.createObjectURL(new Blob([res]), { type: 'application/vnd.ms-excel;charset=UTF-8' })
          let link = document.createElement('a')
          link.style.display = 'none'
          link.href = url
          link.setAttribute('download', `${this.currentTab == 1 ? '建筑面积统计' : '科室空间类型面积统计'}.xlsx`)
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link) // 下载完成移除元素
          window.URL.revokeObjectURL(url) // 释放掉blob对象
        })
        .finally(() => {
          this.exportLoading = false
        })
    },
    getTableList() {
      this.tableLoading = true
      let params = this.currentTab == 1 ? {} : { baseId: this.queryForm.baseId }
      this.$api[this.currentTab == 1 ? 'GetShareAreaStatisticsList' : 'GetDepartmentStatisticsList'](params)
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data
          }
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    // 获取空间树
    spaceTreeListFn() {
      this.$api.getStructureTree().then((res) => {
        if (res.code == 200) {
          this.spaceList = transData(
            res.data.filter((item) => item.ssmType == 2 || item.ssmType == 3),
            'id',
            'pid',
            'children'
          )
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.page-container.area_stat {
  ::v-deep(.container-content) {
    background: #fff;
    display: flex;
    flex-direction: column;
    .area_stat__nav {
      .el-tabs__nav-scroll {
        padding: 0 32px;
      }
    }
    .area_stat__search {
      padding: 15px;
    }
    .area_stat__content {
      flex: 1;
      overflow: hidden;
      font-family: '宋体';
      font-size: 16px;
      padding: 0px 15px 15px;
      .content-table {
        color: #333;
        background: rgba(103, 103, 103, 0.1);
        height: 100%;
        overflow: auto;
        table {
          border-collapse: collapse;
          width: 100%;
          text-align: center;
        }
      }
    }
  }
}
</style>
