<!-- 运行监测 -->
<template>
  <ContentCard :title="item.componentTitleShow" :scrollbarHover="true" :cstyle="{ width: '100%', height: '100%' }"
    class="drag_class" :hasMoreOper="['edit']"
    @more-oper-event="(val) => $emit('all-more-Oper', val, item.componentName, item.chartType)">
    <div slot="content" style="width: 100%; height: 100%;">
      <div v-if="statisticsList.length" class="content_top">
        <p v-for="(item, index) in statisticsList" :key="index">{{ item.metadataName }}<br>{{ item.valueText }}</p>
      </div>
      <div class="main-footer">
        <el-pagination :current-page="pagination.current" :page-sizes="[10]" :page-size="pagination.size"
          layout="total, sizes, prev, pager, next, jumper" :total="pagination.total" @size-change="paginationSizeChange"
          @current-change="paginationCurrentChange" />
      </div>
      <div v-if="statisticsList.length == 0"
        style="width: 100%; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center; padding: 10px">
        <!-- <img src="@/assets/images/newMonitor/no-chat.png" /> -->
        <span>暂无数据</span>
      </div>
    </div>
  </ContentCard>
</template>
<script>
export default {
  name: 'jcxx',
  props: {
    item: {
      type: Object,
      default: () => { }
    },
    systemCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      statisticsList: [],
      pagination: {
        size: 10,
        current: 1,
        total: 0
      },
      selectedId: null,
    }
  },
  mounted() {
    // this.jcxxDataList()
  },
  methods: {
    paginationSizeChange(val) {
      this.pagination.size = val
      this.jcxxDataList(this.selectedId);
    },
    paginationCurrentChange(val) {
      this.pagination.current = val
      this.jcxxDataList(this.selectedId);
    },
    jcxxDataList(id) {
      this.selectedId = id;
      let data = {
        pageSize: this.pagination.size,
        page: this.pagination.current,
        id: id || "",
      }
      this.$api.getRealtimeDataByPage(data).then((res) => {
        if (res.code == "200") {
          this.statisticsList = res.data.records
          this.pagination.total = res.data.total
        }
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.operation-list {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
}

.main-footer {
  position: absolute;
  left: 20px;
  bottom: 20px;
}

.content_top {
  display: flex;
  flex-wrap: wrap;

  p {
    width: 20%;
    text-align: center;
    line-height: 25px;
    margin-bottom: 30px;
  }

}
</style>
