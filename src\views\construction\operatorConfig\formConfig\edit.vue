<template>
  <el-dialog
    v-dialogDrag
    class="component form-field-edit"
    title="编辑字段"
    width="750px"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    custom-class="model-dialog"
    :modal="false"
    @closed="onDialogClosed"
  >
    <el-form ref="formRef" :model="formModel" :rules="rules" label-width="95px">
      <el-row :gutter="12">
        <el-col :span="12">
          <el-form-item label="字段名称" prop="name">
            <el-input v-model="formModel.name" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="字段编码" prop="code">
            <el-input :value="formModel.code" disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="数据来源" prop="dataSource">
            <el-select v-model="formModel.dataSource" placeholder="请选择" disabled>
              <el-option v-for="item of dataSourceOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="流程表单" prop="flowCode">
            <el-select v-model="formModel.flowCode" placeholder="请选择" filterable>
              <el-option v-for="item of flowFieldList" :key="item.code" :value="item.code" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <!-- 定制字段不可选择填写方式，也没有数据规则 -->
        <template v-if="!!formModel.dataSource">
          <el-col :span="12">
            <el-form-item label="填写方式" prop="dataType">
              <el-select v-model="formModel.dataType" placeholder="请选择">
                <el-option v-for="item of dataFillOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="extendLabel" prop="extend">
              <el-input v-if="formModel.dataSource === 3" v-model="formModel.extend" placeholder="请输入"></el-input>
              <!--字典和空间信息-->
              <el-select v-else v-model="formModel.extend" placeholder="请选择">
                <el-option v-for="item of selectOption" :key="item.value" :value="item.value" :label="item.label"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </template>
        <el-col :span="12">
          <el-form-item label="必填" prop="required">
            <el-select v-model="formModel.required" placeholder="请选择">
              <el-option v-for="item of yesNoOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select v-model="formModel.status" placeholder="请选择" :disabled="isPreset">
              <el-option v-for="item of statusOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button type="primary" plain @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="onSubmit">确认</el-button>
    </template>
  </el-dialog>
</template>
<script>
// 字典类型
import { FormConfigDataFillOptions, FormConfigDataSourceOptions, FormItemOptions } from './constant'
import { UsingStatusOptions, YesNoOptions } from '@/views/operationPort/constant'
const DICT_TYPE = 2
export default {
  name: 'FormFieldEdit',
  props: {
    visible: Boolean,
    fieldData: Object,
    formKey: String
  },
  events: ['update:visible', 'update'],
  data: function () {
    // 扩展字段自定义验证
    const extendValidator = (_, value, callback) => {
      if (value === undefined || value === null || !value.length) {
        const msg = this.itemConfig?.errMessage ?? '规则内容不能为空'
        callback(new Error(msg))
      } else {
        callback()
      }
    }
    return {
      formModel: {
        name: '',
        code: '',
        flowCode: '',
        status: 1,
        required: 1,
        extend: '',
        dataSource: 0,
        dataType: 0
      },
      rules: {
        name: [{ required: true, message: '请输入表单名称' }],
        code: [{ required: true, message: '请输入表单编码' }],
        flowCode: [{ required: true, message: '请选择流程表单' }],
        dataSource: [{ required: true, message: '请选择数据来源' }],
        dataType: [{ required: true, message: '请选择填写方式' }],
        extend: [{ required: true, validator: extendValidator }]
      },
      // 流程表单字段列表
      flowFieldList: [],
      // 状态选项
      statusOptions: UsingStatusOptions,
      // 数据源选项
      dataSourceOptions: FormConfigDataSourceOptions,
      // 填写方式选项
      dataFillOptions: FormConfigDataFillOptions,
      // 是否选项
      yesNoOptions: YesNoOptions
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    },
    // 扩展字段字段
    itemConfig() {
      return FormItemOptions.find((it) => it.dataSource === this.formModel.dataSource)
    },
    extendLabel() {
      return this.itemConfig?.label ?? '-'
    },
    // 选择器的数据源
    selectOption() {
      if (this.formModel.dataSource === 1) {
        return this.floorList
      } else if (this.formModel.dataSource === 2) {
        return this.dictList
      } else {
        return []
      }
    },
    // 是否可以还原数据
    canRevertData() {
      return this.dialogVisible && this.fieldData && typeof this.fieldData === 'object'
    },
    // 是否预设字段
    isPreset() {
      return this.fieldData && +this.fieldData.presets === 1
    },
    // 字典列表
    dictList() {
      return [{ value: '2', label: '工程类型' }]
    },
    // 楼层列表
    floorList() {
      return [{ value: '3', label: '楼层' }]
    }
  },
  watch: {
    // 监听是否反显数据
    canRevertData(value) {
      if (!value) return
      this.formModel.dataSource = +this.fieldData.dataSource
      this.formModel.dataType = +this.fieldData.dataType
      this.formModel.name = this.fieldData.operationName
      this.formModel.code = this.fieldData.operationCode
      this.formModel.required = +(this.fieldData.required ?? '1')
      this.formModel.status = +(this.fieldData.state ?? '1')
      this.formModel.extend = this.fieldData.extend ?? ''
      this.formModel.flowCode = this.fieldData.flowCode ?? ''
      if (!this.formModel.flowCode) {
        // 尝试匹配流程表单字段
        this.tryMatchField()
      }
    },
    dialogVisible(value) {
      // 打开时如果没有流程表单数据则获取一次
      if (value && !this.flowFieldList.length) {
        this.getFlowFieldList()
      }
    }
  },
  methods: {
    onDialogClosed() {
      this.$refs.formRef.resetFields()
    },
    // 获取流程表单字段列表
    getFlowFieldList() {
      const params = { formKey: this.formKey }
      this.$api.SporadicProject.getFormNodeListByFormKey(params).then((res) => {
        if (res.code === '200') {
          this.flowFieldList = res.data
          if (!this.formModel.flowCode) {
            // 尝试匹配流程表单字段
            this.tryMatchField()
          }
        }
      })
    },
    // 尝试匹配表单字段
    tryMatchField() {
      const matchedField = this.flowFieldList.find((it) => it.name === this.formModel.name)
      if (matchedField) {
        this.formModel.flowCode = matchedField.code
      }
    },
    // 点击提交
    onSubmit() {
      this.$refs.formRef.validate().then(() => {
        const flowInfo = this.flowFieldList.find((it) => it.code === this.formModel.flowCode) ?? {}
        // 要更新的内容
        const params = {
          operationName: this.formModel.name,
          operationCode: this.formModel.code,
          dataSource: this.formModel.dataSource,
          dataType: this.formModel.dataType,
          state: this.formModel.status,
          required: this.formModel.required,
          extend: this.formModel.extend,
          // 对应的流程表单项
          flowCode: this.formModel.flowCode,
          flowName: flowInfo.name
        }
        if (this.formModel.dataSource === 3) {
          params.dataRule = params.extend + '字'
        } else {
          const findItem = this.selectOption.find((it) => it.value === this.formModel.extend)
          params.dataRule = findItem?.label ?? ''
        }
        // 与原始行数据合并后为最新数据
        const newData = Object.assign({}, this.fieldData, params)
        this.$emit('update', newData)
      })
    }
  }
}
</script>
<style lang="scss">
.component.form-field-edit {
  .el-form {
    background-color: #fff;
    padding: 10px 16px 0;
    .el-form-item {
      .el-cascader,
      .el-select {
        width: 100%;
      }
    }
  }
}
</style>
