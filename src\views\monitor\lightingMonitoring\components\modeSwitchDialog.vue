<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    title="切换提示"
    width="40%"
    :visible.sync="visible"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <div class="content" style="padding: 10px;">
      <div style="display: flex; align-items: center;padding-bottom: 20px;">
        <p style="color: #121f3e; font-size: 15px;">您当前正在执行一下操作：</p>
        <p class="status">
          <span style="margin:0 10px;">{{ type == 1 ? '手动' : '自动' }}</span>
          <i class="el-icon-right"></i>
          <span style="margin-left: 10px;">{{ type == 1 ? '自动' : '手动' }}</span>
        </p>
      </div>
      <el-form ref="ruleForm" :model="form" :rules="rules">
        <el-form-item label="请确认原因" prop="details">
          <el-radio-group v-model="form.radio" size="mini" @input="radioInput">
            <el-radio-button label="1">天气突变</el-radio-button>
            <el-radio-button label="2">检修</el-radio-button>
          </el-radio-group>
          <el-input v-model.trim="form.details" type="textarea" :rows="4" placeholder="请输入原因" maxlength="100" show-word-limit style="width: 70%;" class="ipt"> </el-input>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="confirm('ruleForm')">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  name: 'modeSwitch',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: '1'
    }
  },
  data() {
    return {
      form: {
        radio: '1',
        details: '天气突变'
      },
      rules: {
        details: [{ required: true, message: '请输入原因', trigger: 'change' }]
      }
    }
  },
  mounted() {},
  methods: {
    // 关闭弹窗
    closeDialog() {
      this.form.radio = '1'
      this.form.details = '天气突变'
      this.$emit('update:visible', !this.visible)
      this.$emit('modeSwitch', false)
    },
    radioInput() {
      if (this.form.radio == '1') {
        this.form.details += '天气突变'
      } else {
        this.form.details += '检修'
      }
    },
    confirm(formName) {
      const userInfo = this.$store.state.user.userInfo.user
      let params = {
        userId: userInfo.staffId,
        userName: userInfo.staffName,
        operatorType: 7,
        details: this.form.details
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$api.modeSwitching(params).then((res) => {
            if (res.code == 200) {
              this.$emit('modeSwitch', true)
              this.$emit('update:visible', !this.visible)
            } else {
              this.$message.error(res.msg)
            }
          })
        } else {
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  padding: 20px !important;
  background-color: #fff !important;
  p {
    margin: 0;
  }
  .status {
    font-size: 20px;
    .el-icon-right{
      color: #3562db;
    }
  }
}

.model-dialog {
  padding: 0 !important;
}

.ipt {
  margin-top: 15px;
  margin-left: 80px;
}

::v-deep .el-form-item__error {
  height: 20px;
  width: 300px;
  color: #f56c6c;
  font-size: 12px;
  line-height: 20px;
  padding-top: 4px;
  position: absolute;
  left: 78px;
}

::v-deep .el-radio-button__orig-radio:checked + .el-radio-button__inner {
  background-color: #3562db !important;
}

::v-deep .el-dialog__body {
  padding: 0 !important;
}

::v-deep .el-textarea {
  .el-textarea__inner {
    padding-bottom: 20px;
  }

  .el-input__count {
    line-height: 16px;
  }
}
</style>
