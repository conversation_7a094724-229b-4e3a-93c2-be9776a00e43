<template>
  <el-dialog v-if="positionTerminalShow" title="选择定位终端" width="60%" :visible.sync="positionTerminalShow" custom-class="model-dialog" :before-close="closeDialog">
    <div class="camera_content" style="padding: 10px 20px 10px 10px">
      <div class="header_operation">
        <div class="search_box">
          <div class="search_select">
            <el-input v-model="filter.name" style="width: 200px" placeholder="搜索设备名称、编码、SN"></el-input>
            <el-select v-model="filter.type" style="margin: 0px 15px" placeholder="系统类别">
              <el-option v-for="item in systemCategoryOptions" :key="item.id" :label="item.baseName" :value="item.id"> </el-option>
            </el-select>
            <el-select v-model="filter.bindState" placeholder="状态" clearable>
              <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
          </div>
        </div>
        <div class="header_btn">
          <el-button type="primary" plain @click="userReset">重置</el-button>
          <el-button type="primary" @click="userQuery">查询</el-button>
        </div>
      </div>
      <div class="table_div">
        <el-table ref="cameraTable" row-key="id" :data="tableData" tooltip-effect="dark" style="width: 100%" height="310px" border @selection-change="userSelectChange">
          <el-table-column prop="radio" width="55" v-if="isRadio">
            <template slot-scope="scope">
              <el-radio v-model="selectedRow" :label="scope.row.id" name="" @change="handleRadioChange(scope.row)"></el-radio>
            </template>
          </el-table-column>
          <el-table-column type="selection" :reserve-selection="true" width="55" align="center" v-else> </el-table-column>
          <el-table-column type="index" label="序号" width="55" align="center">
            <template slot-scope="scope">
              <span>{{ (pageinationData.page - 1) * pageinationData.pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="设备名称" align="center" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="code" label="资产编码" align="center" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="sn" label="SN" align="center" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="sysTypeName" label="专业类别" align="center" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="typeName" label="系统类别" align="center" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="bindState" label="状态" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-tag v-if="scope.row.bindState == 1" size="mini" type="success">已绑定</el-tag>
              <el-tag v-else-if="scope.row.bindState == 0" size="mini" type="danger">未绑定</el-tag>
              <span v-else></span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div style="padding: 6px">
        <el-pagination
          class="pagination"
          :current-page="pageinationData.page"
          :page-sizes="[15, 30, 50, 100]"
          :page-size="pageinationData.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageinationData.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="groupSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  name: 'positionTerminalDialog',
  props: {
    positionTerminalShow: {
      type: Boolean,
      default: false
    },
    selectaDialogData: {
      type: String,
      default: ''
    },
    // 列表选择方式 是否单选
    isRadio: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      filter: {
        name: '', // 名称
        type: '',
        bindState: 0
      },
      selectedRow: '',
      systemCategoryOptions: [],
      statusOptions: [
        { value: 1, label: '已绑定' },
        { value: 0, label: '未绑定' }
      ],
      checkedData: {},
      tableData: [], // 数据
      pageinationData: {
        page: 1,
        pageSize: 15,
        total: 0
      },
      multipleSelection: []
    }
  },
  mounted() {
    this.getProfessionalCategory()
    this.getStaffListByPage()
  },
  methods: {
    handleRadioChange(row) {
      this.checkedData = row
    },
    // 获取列表
    getStaffListByPage() {
      let data = {
        ...this.pageinationData,
        ...this.filter
      }
      if (data.bindState === '') {
        data.bindState = null
      }
      delete data.total
      this.$api.supplierAssess.queryTerminalByDutyPostData(data).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.records
          this.pageinationData.total = res.data.total
          let terminalId = this.selectaDialogData?.split(',') ?? []
          if (terminalId.length && this.tableData.length) {
            terminalId.forEach((el) => {
              let selectRow = this.tableData.find((e) => e.id == el)
              if (selectRow) {
                this.$refs.cameraTable.toggleRowSelection(selectRow)
              }
            })
          }
          this.tableData.forEach((row) => {})
        }
      })
    },
    // 获取系统类别
    getProfessionalCategory() {
      this.$api.getProfessionalCategory().then((res) => {
        if (res.code == '200') {
          this.systemCategoryOptions = res.data
        }
      })
    },
    // 弹框分页
    handleSizeChange(val) {
      this.pageinationData.pageSize = val
      this.getStaffListByPage()
    },
    handleCurrentChange(val) {
      this.pageinationData.page = val
      this.getStaffListByPage()
    },
    // 重置
    userReset() {
      this.filter = {
        name: '', // 名称
        type: '',
        bindState: 0
      }
      this.userQuery()
    },
    // 点击查询
    userQuery() {
      this.pageinationData.page = 1
      this.getStaffListByPage()
    },
    userSelectChange(rows) {
      this.multipleSelection = rows
    },
    closeDialog() {
      this.$emit('closeTerminalDialog')
    },
    groupSubmit() {
      if (JSON.stringify(this.checkedData) !== '{}' && this.isRadio) {
        if (this.checkedData.bindState === 1) {
          this.$confirm('已绑定设备将被绑定至所选值班岗', '操作确认', {
            cancelButtonClass: 'el-button--primary is-plain',
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.$emit('submitTerminalDialog', this.checkedData)
          })
        } else {
          this.$emit('submitTerminalDialog', this.checkedData)
        }
      } else if (this.multipleSelection.length && !this.isRadio) {
        let bindState = this.multipleSelection.some((el) => el.bindState === 1)
        if (bindState) {
          this.$confirm('已绑定设备将被绑定至所选值班岗', '操作确认', {
            cancelButtonClass: 'el-button--primary is-plain',
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.$emit('submitTerminalDialog', this.multipleSelection)
          })
        } else {
          this.$emit('submitTerminalDialog', this.multipleSelection)
        }
      } else {
        this.$message({
          message: '请选择至少一项',
          type: 'warning'
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.model-dialog {
  .camera_content {
    margin: 0 auto;
    background: #fff;
    border-radius: 4px;
    // display: flex;
    width: 100%;
    height: 100%;
    .header_operation {
      // padding: 24px;
      margin-bottom: 10px;
      display: flex;
      .search_box {
        flex: 1;
        > div {
          margin-right: 16px;
        }
      }
    }
    .table_div {
      height: calc(100% - 100px);
    }
    .paging_box {
      display: flex;
      justify-content: flex-end;
      padding: 6px;
    }
  }
}
::v-deep .el-radio__label {
  display: none;
}
</style>
