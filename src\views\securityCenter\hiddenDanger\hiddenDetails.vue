<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="sino-content">
        <div class="detail-box">
          <div v-for="item in itemArr" :key="item.label" class="item">
            <span>{{ item.label }}:</span>
            <span>{{ detailData[item.value] }}</span>
          </div>
          <div class="description">
            <span>问题描述:</span>
            <span>{{ detailData.questionContent }}</span>
          </div>
          <div class="voice">
            <span>语音:</span>
            <audio v-if="detailData.questionVoiceUrl" style="height: 30px" :src="detailData.questionVoiceUrl" controls="controls"></audio>
            <span v-else class="empty-voice">当前任务未上传录音</span>
          </div>
          <div class="pic">
            <span>图片:</span>
            <div class="pic-list">
              <el-image
                v-for="item in detailData.questionAttachmentList"
                :key="item"
                style="width: 100px; height: 100px"
                :src="item"
                :preview-src-list="detailData.questionAttachmentList"
              >
              </el-image>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">取消</el-button>
    </div>
  </PageContainer>
</template>

<script>
export default {
  data() {
    return {
      loading: true,
      itemArr: [
        {
          label: '检查类型',
          value: 'checkName'
        },
        {
          label: '隐患区域',
          value: 'questionAddress'
        },
        {
          label: '隐患分类',
          value: 'questionDetailType'
        },
        {
          label: '隐患等级',
          value: 'riskName'
        },
        {
          label: '责任部门',
          value: 'dutyDeptName'
        },
        {
          label: '反馈部门',
          value: 'createByDeptName'
        },
        {
          label: '反馈人',
          value: 'createPersonName'
        },
        {
          label: '电话',
          value: 'createPersonPhone'
        },
        {
          label: '反馈时间',
          value: 'createTime'
        },
        {
          label: '要求整改完成时间',
          value: 'rectificationPlanTime'
        }
      ],
      id: '',
      detailData: {}
    }
  },
  computed: {},
  mounted() {
    this.id = this.$route.query.id
    this.getData()
  },
  methods: {
    getData() {
      this.$api.ipsmGetHiddenInfoById({ id: this.id }).then((res) => {
        this.loading = false
        if (res.code == 200) {
          this.detailData = res.data
          let arr = []
          this.detailData.questionAttachmentList.forEach((item) => {
            arr.push(this.$tools.imgUrlTranslation(item))
          })
          this.detailData.questionAttachmentList = arr
        } else {
          this.$message.error(res.message)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.table-content {
  width: 100%;
  background: #fff;
  border-radius: 4px;
  height: 100%;
  padding: 10px;
  overflow: auto;
}

.detail-box {
  // width: 100%;
  display: flex;
  flex-wrap: wrap;
  margin: 24px;

  > .item {
    display: flex;
    width: 40%;
    height: 40px;
    align-items: center;

    span:nth-child(1) {
      width: 150px;
      text-align: right;
    }

    span:nth-child(2) {
      padding-left: 24px;
      color: #909399;
    }
  }
}

.description {
  display: flex;
  width: 100%;
  margin-top: 24px;

  span:nth-child(1) {
    width: 150px;
    text-align: right;
    line-height: 32px;
  }

  span:nth-child(2) {
    width: 40%;
    padding-left: 24px;
    color: #909399;
  }
}

.voice {
  display: flex;
  width: 100%;
  margin-top: 24px;

  span:nth-child(1) {
    width: 150px;
    text-align: right;
    line-height: 32px;
  }

  audio:nth-child(2) {
    padding-left: 24px;
    color: #909399;
  }
}

.pic {
  display: flex;
  width: 100%;
  margin-top: 24px;

  span:nth-child(1) {
    width: 150px;
    text-align: right;
    line-height: 32px;
  }

  .pic-list {
    padding-left: 24px;
  }
}

.el-image {
  margin-right: 8px;
}

.empty-voice {
  margin-left: 24px;
  display: flex;
  align-items: center;
  color: #909399;
}
</style>
