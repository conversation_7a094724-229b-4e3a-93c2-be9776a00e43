<template>
  <div style="height: 100%">
    <div class="searchForm">
      <div class="search-box">
        <div class="btns">
          <el-button type="primary" style="margin-right: 16px" @click="goBack()">返回</el-button>
          <div :class="['date-btn', listType == 'out' ? 'active-btn' : '']" @click="changeListType('out')">出站记录</div>
          <div :class="['date-btn', listType == 'timeoutT' ? 'active-btn' : '']" @click="changeListType('timeoutT')">超时记录</div>
          <el-cascader
            v-model="officeId"
            :options="deptOptions"
            :props="{ expandTrigger: 'hover', checkStrictly: true, label: 'deptName', value: 'id' }"
            placeholder="所属科室"
            clearable
            filterable
          >
          </el-cascader>
        </div>
        <div style="margin-left: 16px">
          <el-button type="primary" @click="listSearch">查询</el-button>
          <el-button type="primary" plain @click="listReset">重置</el-button>
        </div>
      </div>
    </div>
    <div class="listWrap">
      <div class="leftList">
        <el-table v-if="listType == 'out'" v-loading="outTableLoading" :data="outTableData" border style="width: 100%" height="90%">
          <el-table-column label="所属科室" prop="officeName" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="医疗废物数量" prop="count" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="收集时间" prop="gatherTime" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="收集重量" prop="gatherWeigh" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="出站时间" prop="outTime" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="出站人员签字" prop="hospitalSignature" align="center">
            <template slot-scope="scope">
              <span style="color: #5188fc; cursor: pointer" @click="showPicture(scope.row, 'hospitalSignature')">查看图片</span>
            </template>
          </el-table-column>
          <el-table-column label="医废公司人员签字" prop="companySignature" align="center">
            <template slot-scope="scope">
              <span style="color: #5188fc; cursor: pointer" @click="showPicture(scope.row, 'companySignature')">查看图片</span>
            </template>
          </el-table-column>
        </el-table>
        <el-table v-else v-loading="timeoutTableLoading" :data="timeoutTableData" border style="width: 100%" height="90%">
          <el-table-column label="医废编码" prop="barCode" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="所属科室" prop="officeName" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="医废类型" prop="wasteType" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="收集时间" prop="gathertime" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="转运箱编码" prop="rfidCode" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="收集人员签字" prop="receivedSignature" align="center">
            <template slot-scope="scope">
              <span style="color: #5188fc; cursor: pointer" @click="showPicture(scope.row, 'receivedSignature')">查看图片</span>
            </template>
          </el-table-column>
          <el-table-column label="科室人员签字" prop="officeSignature" align="center">
            <template slot-scope="scope">
              <span style="color: #5188fc; cursor: pointer" @click="showPicture(scope.row, 'officeSignature')">查看图片</span>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :current-page="pageNo"
          :page-sizes="[15, 30, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
        <imgCarousel :dialogVisibleImg="dialogVisibleImg" :imgArr="imgArr" @closeDialog="closeDialogImg"></imgCarousel>
      </div>
      <div class="rightImg">
        <div class="rightHeader">今日运出</div>
        <div class="content">
          <div class="count">
            <div class="leftCount">{{ boxCount || '0' }}</div>
            <div class="rightUnit">箱</div>
          </div>
          <div class="count">
            <div class="leftCount">{{ detailInfo.buyWeigh || '0.00' }}</div>
            <div class="rightUnit">kg</div>
          </div>
          <div class="upload">
            <el-upload
              class="upload-demo"
              action="string"
              accept="image/bmp,image/jpeg,image/png"
              :on-exceed="handleExceed"
              :on-change="fileChange"
              :file-list="fileList"
              :show-file-list="false"
              :http-request="handleUpload"
            >
              <img v-if="fileList.length == 0" class="uplodaImg" :src="detailInfo.buyPicture ? detailInfo.ossFilePrefix + detailInfo.buyPicture : uploadBgImg" alt="今日运出图片" />
              <img v-else class="uplodaImg" :src="baseUrl" />
            </el-upload>
          </div>
        </div>
        <div class="footer">
          <el-button type="primary" plain style="margin-right: 10px" @click="toView">查看</el-button>
          <el-button v-loading="subLoading" type="primary" @click="save">保存</el-button>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import imgCarousel from '../../spaceManage/common/imgCarousel.vue'
import uploadBgImg from '../../../../assets/images/uploadBgImg.png'
import { transData } from '@/util'
import axios from 'axios'
import moment from 'moment'
export default {
  name: 'tableList',
  components: {
    imgCarousel
  },
  props: ['date', 'detailInfo'],
  data() {
    return {
      subLoading: false,
      uploadBgImg,
      listType: 'out',
      officeId: '',
      pageNo: 1,
      pageSize: 15,
      total: 0,
      deptOptions: [],
      outTableLoading: false,
      outTableData: [],
      dialogVisibleImg: false,
      imgArr: [],
      timeoutTableLoading: false,
      timeoutTableData: [],
      fileList: [],
      baseUrl: '',
      boxCount: 0
    }
  },
  watch: {
    listType(val) {
      this.pageNo = 1
      this.pageSize = 15
      if (val == 'timeoutT') {
        this.getTimeoutTTableList()
      } else {
        this.getOutTableList()
      }
    }
  },
  created() {
    this.getDeptList()
    this.getOutTableList()
    this.getDailyBoxCount()
  },
  methods: {
    goBack() {
      this.$emit('close')
    },
    changeListType(type) {
      this.listType = type
    },
    listSearch() {
      this.pageNo = 1
      if (this.listType == 'out') {
        this.getOutTableList()
      } else {
        this.getTimeoutTTableList()
      }
    },
    listReset() {
      this.officeId = ''
      if (this.listType == 'out') {
        this.getOutTableList()
      } else {
        this.getTimeoutTTableList()
      }
    },
    showPicture(row, label) {
      if (row[label]) {
        this.imgArr.push(this.$tools.imgUrlTranslation(row[label]))
        this.dialogVisibleImg = true
      } else {
        this.$message.error('暂无签名')
      }
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.pageNo = 1
      if (this.listType == 'out') {
        this.getOutTableList()
      } else {
        this.getTimeoutTTableList()
      }
    },
    handleCurrentChange(val) {
      this.pageNo = val
      if (this.listType == 'out') {
        this.getOutTableList()
      } else {
        this.getTimeoutTTableList()
      }
    },
    // 获取部门列表
    getDeptList() {
      this.$api.getSelectedDept({}).then((res) => {
        if (res.code == 200) {
          this.deptOptions = transData(res.data, 'id', 'pid', 'children')
        }
      })
    },
    getOutTableList() {
      const params = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        date: this.date,
        officeId: this.officeId[this.officeId.length - 1]
      }
      this.$api.outTableList(params).then((res) => {
        if (res.code == '200') {
          this.total = res.data.total
          this.outTableData = res.data.rows
        } else {
          this.$message.error(res.message)
        }
      })
    },
    getTimeoutTTableList() {
      const params = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        date: this.date,
        officeId: this.officeId[this.officeId.length - 1]
      }
      this.$api.timeoutTableList(params).then((res) => {
        if (res.code == '200') {
          this.total = res.data.total
          this.timeoutTableData = res.data.rows
        } else {
          this.$message.error(res.message)
        }
      })
    },
    closeDialogImg() {
      this.dialogVisibleImg = false
      this.imgArr = []
    },
    handleExceed() {},
    fileChange(file, fileList) {
      console.log(file)
      this.fileList = fileList
      this.filetoBase64(file.raw)
    },
    handleUpload() {},
    toView() {
      if (this.fileList.length > 0) {
        this.imgArr.push(this.baseUrl)
        this.dialogVisibleImg = true
      } else {
        if (this.detailInfo.buyPicture) {
          let str = this.detailInfo.ossFilePrefix + this.detailInfo.buyPicture
          this.imgArr.push(str)
          this.dialogVisibleImg = true
        } else {
          this.$message.error('暂无图片')
        }
      }
    },
    // 图片链接转base64
    imageUrlToBase64(imageUrl) {
      let image = new Image() // 一定要设置为let，不然图片不显示
      image.setAttribute('crossOrigin', 'anonymous') // 解决跨域问题
      image.src = imageUrl
      image.onload = () => {
        var canvas = document.createElement('canvas')
        canvas.width = image.width
        canvas.height = image.height
        var context = canvas.getContext('2d')
        context.drawImage(image, 0, 0, image.width, image.height)
        var quality = 0.8
        var dataURL = canvas.toDataURL('image/jpeg', quality) // 使用toDataUrl将图片转换成jpeg的格式,不要把图片压缩成png，因为压缩成png后base64的字符串可能比不转换前的长！
        this.base64toFile(dataURL)
      }
    },
    // base64转文件流
    base64toFile(dataurl, filename = 'file') {
      let arr = dataurl.split(',')
      let mime = arr[0].match(/:(.*?);/)[1]
      let suffix = mime.split('/')[1]
      let bstr = atob(arr[1])
      let n = bstr.length
      let u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      let file = new File([u8arr], `${filename}.${suffix}`, {
        type: mime
      })
      this.upload(file)
    },
    // 文件流转base64
    filetoBase64(file) {
      let reader = new FileReader() // 实例化文件读取对象
      reader.readAsDataURL(file) // 将文件读取为 DataURL,也就是base64编码
      reader.onload = (e) => {
        // 文件读取成功完成时触发
        let dataURL = e.target.result // 获得文件读取成功后的DataURL,也就是base64编码
        this.baseUrl = dataURL
      }
    },
    save() {
      this.subLoading = true
      if (this.fileList.length > 0) {
        this.base64toFile(this.baseUrl)
      } else {
        if (this.detailInfo.ossFilePrefix) {
          this.imageUrlToBase64(this.detailInfo.ossFilePrefix + this.detailInfo.buyPicture)
        } else {
          this.$message.error('请选择图片')
        }
      }
    },
    upload(file) {
      const userInfo = this.$store.state.user.userInfo.user
      let formData = new FormData()
      formData.append('hospitalCode', userInfo.hospitalCode ?? 'BJSJTYY')
      formData.append('unitCode', userInfo.unitCode ?? 'BJSYGJ')
      formData.append('id', this.detailInfo.id)
      formData.append('createTime', moment(this.detailInfo.createTime).format('YYYY-MM-DD'))
      formData.append('upload-file', file)
      axios({
        method: 'post',
        url: __PATH.VUE_APP_IMWS_API + 'ihcrsInterfaceController/saveDeliveryRecord',
        data: formData,
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      })
        .then((res) => {
          if (res.data.code == 200) {
            this.$message.success(res.data.message || '保存成功')
          } else {
            this.$message.error(res.data.message || '保存失败')
          }
          this.subLoading = false
        })
        .catch(() => {
          this.$message.error('保存失败')
          this.subLoading = false
        })
    },
    getDailyBoxCount() {
      const params = {
        selectDate: this.date
      }
      this.$api.getDailyBoxCount(params).then((res) => {
        if (res.code == '200') {
          this.boxCount = res.data.boxCount
        } else {
          this.$message.error(res.message)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.searchForm {
  background-color: #fff;
  margin-bottom: 16px;
  display: flex;
  height: 60px;
  padding: 0 16px;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .el-input {
    width: 300px;
  }

  > div {
    margin-right: 20px;
  }

  .search-box {
    display: flex;
    align-items: center;
  }

  .btns {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .btns .date-btn {
    width: 100px;
    height: 32px;
    border: 1px solid #3562db;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    color: #3562db;
    margin-right: 12px;
    cursor: pointer;
  }

  .active-btn {
    background-color: #3562db;
    color: #fff !important;
  }
}

.listWrap {
  width: 100%;
  height: calc(100% - 76px);
  display: flex;

  .leftList {
    padding: 0 20px;
    width: 75%;
  }

  .rightImg {
    position: relative;
    width: calc(25% - 20px);
    height: 455px;
    border: 1px solid #ddd;

    .rightHeader {
      padding-left: 20px;
      height: 40px;
      line-height: 40px;
      border-bottom: 1px solid #ddd;
      font-size: 14px;
    }

    .content {
      padding: 20px;

      .count {
        width: 100%;
        display: flex;
        font-size: 50px;
        font-weight: bold;

        .leftCount {
          width: 80%;
          text-align: center;
        }

        .rightUnit {
          color: #ccced3;
        }
      }

      .upload {
        height: 180px;
        text-align: center;

        :deep(.upload-demo) {
          height: 100%;

          .el-upload--text {
            height: 100%;
            width: 100%;
            line-height: 180px;

            .uplodaImg {
              max-width: 100%;
              max-height: 100%;
            }
          }
        }
      }
    }

    .footer {
      position: absolute;
      bottom: 20px;
      right: 40px;
    }
  }
}
</style>
