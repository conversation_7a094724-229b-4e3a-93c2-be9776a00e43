<template>
  <div class="wing-container">
    <div class="table-search">
      <div class="table-search-left">
        <el-input v-model="searchInfo.userNameOrEmployeeId" style="width: 200px; margin-right: 8px" placeholder="姓名、工号" maxlength="60"></el-input>
        <el-select v-model="searchInfo.deptCode" filterable clearable placeholder="所属科室" style="margin-right: 8px" @change="changeDeptName">
          <el-option v-for="item in deptList" :key="item.id" :label="item.deptName" :value="item.id"> </el-option>
        </el-select>
        <el-input v-model="searchInfo.spaceName" style="width: 200px; margin-right: 8px" placeholder="小区名称" maxlength="60"></el-input>
        <el-select v-model="searchInfo.houseTypeCode" placeholder="申请房型" style="width: 110px; margin-right: 8px">
          <el-option v-for="item of houseTypeOptions" :key="item.dictCode" :label="item.dictName" :value="item.dictCode"></el-option>
        </el-select>
        <el-select v-model="searchInfo.applicationTypeId" placeholder="申请类型" style="width: 200px; margin-right: 8px">
          <el-option v-for="item in applicationTypeOptions" :key="item.dictCode" :label="item.dictName" :value="item.dictCode"> </el-option>
        </el-select>
        <el-date-picker
          v-model="searchInfo.date"
          type="datetimerange"
          start-placeholder="申请时间"
          range-separator="至"
          style="width: 380px; margin-right: 8px"
          value-format="yyyy-MM-dd HH:mm:ss"
          end-placeholder="申请时间"
          :default-time="['00:00:00', '23:59:59']"
        >
        </el-date-picker>
      </div>
      <div class="table-search-right">
        <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db; margin-left: 20px" @click="resetCondition">重置</el-button>
        <el-button type="primary" style="font-size: 14px" @click="searchByCondition">查询</el-button>
      </div>
    </div>
    <div class="btn-box">
      <el-button type="primary" @click="sendNotice" :disabled="multipleSelection.length == 0">发送通知</el-button>
      <el-button type="primary" style="font-size: 14px; background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db" @click="handExport">导出</el-button>
    </div>
    <div class="table-box">
      <div class="table-list">
        <el-table
          ref="materialTable"
          v-loading="tableLoading"
          :data="tableData"
          height="calc(100% - 10px)"
          border
          :header-cell-style="{ background: '#F6F5FA' }"
          style="width: 100%"
          :cell-style="{ padding: '8px 0 8px 0' }"
          stripe
          highlight-current-row
          :empty-text="emptyText"
          row-key="id"
          @row-dblclick="handRowDblclick"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" :reserve-selection="true"> </el-table-column>
          <el-table-column label="序号" type="index" width="70">
            <template slot-scope="scope">
              <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="userName" label="姓名"></el-table-column>
          <el-table-column show-overflow-tooltip prop="userEmployeeId" label="工号"></el-table-column>
          <el-table-column show-overflow-tooltip prop="userGender" label="性别">
            <template slot-scope="scope">
              {{ scope.row.userGender | sexFilter }}
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="userIdCard" label="身份证号"></el-table-column>
          <el-table-column show-overflow-tooltip prop="userEntryTime" label="入职日期"></el-table-column>
          <el-table-column show-overflow-tooltip prop="userDepartment" label="所属科室"></el-table-column>
          <el-table-column show-overflow-tooltip prop="processName" label="申请类型" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="applyCommunityName" label="申请小区" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="houseTypeName" label="申请房型" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="acceptAdjustment" label="是否接受调剂" align="center">
            <template slot-scope="scope">
              {{ scope.row.acceptAdjustment | acceptAdjustmentFilter }}
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="processTime" label="申请时间" align="center"></el-table-column>
          <el-table-column label="操作" align="center" width="200">
            <template slot-scope="scope">
              <el-link type="primary" @click="openDetails(scope.row, 'details')"> 详情 </el-link>
              <el-link type="primary" style="margin-left: 10px" @click="openDetails(scope.row, 'fp')"> 分配 </el-link>
              <el-link type="primary" style="margin-left: 10px" @click="openDetails(scope.row, 'lh')"> 轮候 </el-link>
              <el-link type="primary" style="margin-left: 10px" @click="openDetails(scope.row, 'qx')"> 弃选 </el-link>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="table-pagination" style="padding-top: 10px; text-align: right">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="paginationData.currentPage"
          :page-sizes="[15, 30, 50, 100]"
          :page-size="paginationData.pageSize"
          :total="paginationData.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
    <!-- 分配房间 -->
    <div class="dialog-all" v-if="allocationDialogVisible">
      <allocationDialog :dialogVisible="allocationDialogVisible" :roomName="roomName" @close="closeAllocationDialog" @verify="verifyAllocationRoom"></allocationDialog>
    </div>
    <!-- 分配房屋提交 -->
    <div class="dialog-submit" v-if="submitDialogVisible" v-loading="submitLoading">
      <allocationRoomSubmitDialog
        ref="allocationRoomSubmitDialog"
        :reselectRoomInfo="reselectRoomInfo"
        :echoInfo="echoInfo"
        :dialogVisible="submitDialogVisible"
        @submitDialogclose="submitDialogclose"
        @submitDialog="submitDialog"
        @reselect="roomReselect"
      ></allocationRoomSubmitDialog>
    </div>
    <!-- 发送通知 -->
    <houseSelectionNoticeDialog
      v-if="houseSelectionNoticeVisible"
      :dialogVisible="houseSelectionNoticeVisible"
      :multipleSelection="multipleSelection"
      @xfDialogclose="houseSelectionNoticeVisible = false"
      @xfDialogOpen="xfDialogOpen"
    ></houseSelectionNoticeDialog>
    <!-- 弃选 -->
    <abandonSelectionDialog
      v-if="abandonSelectionDialogVisible"
      :id="qxId"
      :dialogVisible="abandonSelectionDialogVisible"
      @qxSubmitDialog="qxSubmitDialog"
      @qxDialogclose="qxDialogclose"
    ></abandonSelectionDialog>
  </div>
</template>
<script>
import axios from 'axios'
export default {
  components: {
    allocationDialog: () => import('./allocationDialog.vue'),
    allocationRoomSubmitDialog: () => import('./allocationRoomSubmitDialog.vue'),
    houseSelectionNoticeDialog: () => import('./houseSelectionNoticeDialog.vue'),
    abandonSelectionDialog: () => import('./abandonSelectionDialog.vue')
  },
  data() {
    return {
      abandonSelectionDialogVisible: false,
      allocationDialogVisible: false,
      houseSelectionNoticeVisible: false,
      submitDialogVisible: false,
      tableLoading: false,
      submitLoading: false,
      emptyText: '暂无数据',
      searchInfo: {
        userNameOrEmployeeId: '', // 姓名或工号
        spaceName: '', // 小区名称
        houseTypeCode: '', // 申请房型
        applicationTypeId: '', // 申请类型
        deptCode: '', // 部门
        deptName: '',
        date: [] // 申请时间
      },
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      deptList: [],
      applicationTypeOptions: [],
      tableData: [],
      reselectRoomInfo: {},
      echoInfo: {
        id: '',
        name: '',
        processName: '',
        processType: '',
        userEmployeeId: ''
      },
      roomName: '',
      multipleSelection: [],
      houseTypeOptions: [],
      qxId: ''
    }
  },
  filters: {
    sexFilter(val) {
      let sex = {
        1: '男',
        2: '女'
      }
      if (!val) return ''
      if (val === '男') return val
      if (val === '女') return val
      return sex[val]
    },
    acceptAdjustmentFilter(val) {
      if (!val) return ''
      return val == 1 ? '是' : '否'
    }
  },
  mounted() {
    this.getDeptDataList()
    this.getTypeDataList()
    this.searchByCondition()
  },
  methods: {
    // 双击进详情
    handRowDblclick(row) {
      this.$router.push({
        path: 'allocationDetail',
        query: {
          id: row.id || '',
          activeName: '0'
        }
      })
    },
    // 弃选 关闭
    qxDialogclose() {
      this.abandonSelectionDialogVisible = false
    },
    // 弃选成功 事件
    qxSubmitDialog() {
      this.abandonSelectionDialogVisible = false
      this.$emit('updateStatistics')
      this.searchByCondition()
    },
    xfDialogOpen() {
      this.houseSelectionNoticeVisible = true
    },
    // 打开选房通知弹窗
    sendNotice() {
      this.houseSelectionNoticeVisible = true
    },
    // 表格多选
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 导出
    handExport() {
      const userInfo = this.$store.state.user.userInfo.user
      const params = {
        pageSize: this.paginationData.pageSize,
        pageNum: this.paginationData.currentPage,
        queryType: '1',
        userNameOrEmployeeId: this.searchInfo.userNameOrEmployeeId,
        userDepartmentId: this.searchInfo.deptCode,
        userDepartment: this.searchInfo.deptName,
        userName: userInfo.staffName,
        userId: userInfo.staffId,
        processType: this.searchInfo.applicationTypeId,
        startTime: this.searchInfo.date.length ? this.searchInfo.date[0] : '',
        endTime: this.searchInfo.date.length ? this.searchInfo.date[1] : '',
        spaceName: this.searchInfo.spaceName, // 小区名称
        houseTypeCode: this.searchInfo.houseTypeCode, // 申请房型
        unitCode: userInfo.unitCode ?? 'BJSYGJ',
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY'
      }
      axios({
        method: 'post',
        url: __PATH.VUE_RHMS_API + 'allocation/houseAllocationExport',
        data: params,
        responseType: 'blob',
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token,
          unitCode: userInfo.unitCode ?? 'BJSYGJ',
          hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY'
        }
      })
        .then((res) => {
          const name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
          const blob = new Blob([res.data])
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
        })
        .catch(() => {
          this.$message.error('下载失败')
        })
    },
    changeDeptName(val) {
      if (val) {
        this.searchInfo.deptName = this.deptList.find((i) => i.id === val).deptName
      } else {
        this.searchInfo.deptName = ''
      }
    },
    /** 重置 */
    resetCondition() {
      this.paginationData = {
        currentPage: 1,
        pageSize: 15,
        total: 0
      }
      this.searchInfo = {
        userNameOrEmployeeId: '',
        applicantId: '',
        deptCode: '',
        deptName: '',
        date: [],
        spaceName: '', // 小区名称
        houseTypeCode: '' // 申请房型
      }
      this.getTableData()
    },
    /** 查询 */
    searchByCondition() {
      this.paginationData.currentPage = 1
      this.getTableData()
    },
    /** 获取列表 */
    getTableData() {
      const userInfo = this.$store.state.user.userInfo.user
      let params = {
        pageSize: this.paginationData.pageSize,
        pageNum: this.paginationData.currentPage,
        queryType: '1',
        userNameOrEmployeeId: this.searchInfo.userNameOrEmployeeId,
        userDepartmentId: this.searchInfo.deptCode,
        userDepartment: this.searchInfo.deptName,
        userName: userInfo.staffName,
        userId: userInfo.staffId,
        processType: this.searchInfo.applicationTypeId,
        startTime: this.searchInfo.date.length ? this.searchInfo.date[0] : '',
        endTime: this.searchInfo.date.length ? this.searchInfo.date[1] : '',
        spaceName: this.searchInfo.spaceName, // 小区名称
        houseTypeCode: this.searchInfo.houseTypeCode // 申请房型
      }
      this.tableLoading = true
      this.$api.rentalHousingApi
        .getRoomAllocationList(params)
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.records
            this.paginationData.total = res.data.total
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取失败'))
        .finally(() => (this.tableLoading = false))
    },
    /** 打开分配房源选择弹窗 */
    openDetails(row, type) {
      switch (type) {
        case 'details':
          this.$router.push({
            path: 'allocationDetail',
            query: {
              id: row.id || '',
              activeName: '0'
            }
          })
          break
        case 'fp':
          this.echoInfo.id = row.id || ''
          this.echoInfo.name = row.userName || ''
          this.echoInfo.processName = row.processName || ''
          this.echoInfo.processType = row.processType || ''
          this.echoInfo.userEmployeeId = row.userEmployeeId || ''
          if (row.desiredRoomName) {
            let roomName = row.desiredRoomName.split(' ')
            this.roomName = roomName.length ? roomName[roomName.length - 1] : ''
          }
          this.allocationDialogVisible = true
          break
        case 'lh':
          let html = `<div style="display: flex;align-items: center;">
            <span class="el-icon-warning" style="color: #f59a23;font-size: 22px;margin-right: 5px;"></span><span style="font-weight: 600;">轮候确认</span>
        </div>
        <div style="padding-left: 28px;">是否将所选人员移入轮候列表</div>`
          this.$alert(html, '', {
            dangerouslyUseHTMLString: true,
            showClose: false,
            showCancelButton: true
          })
            .then(() => {
              let params = { projectAllocationId: row.id }
              this.$api.rentalHousingApi.moveToWaiting(params).then((res) => {
                if (res.code == 200) {
                  this.$message.success(res.message)
                  this.$emit('updateStatistics')
                  this.searchByCondition()
                } else {
                  this.$message.error(res.message)
                }
              })
            })
            .catch(() => {})
          break
        case 'qx':
          this.qxId = row.id
          this.abandonSelectionDialogVisible = true
          break
        default:
          break
      }
    },
    /** 确认分配房间 */
    verifyAllocationRoom(row) {
      this.reselectRoomInfo = row
      this.closeAllocationDialog()
      setTimeout(() => {
        this.submitDialogVisible = true
      }, 200)
    },
    /** 关闭分配房间弹窗 */
    closeAllocationDialog() {
      this.allocationDialogVisible = false
    },
    /** 关闭提交房间弹窗 */
    submitDialogclose() {
      this.submitDialogVisible = false
    },
    /** 配房提交 */
    submitDialog() {
      let params = {
        id: this.echoInfo.id,
        allocationRoomId: this.reselectRoomInfo.id,
        allocationRoomName: this.reselectRoomInfo.houseName
      }
      this.submitLoading = true
      this.$api.rentalHousingApi
        .submitAllocation(params)
        .then((res) => {
          if (res.code == 200) {
            this.roomName = ''
            this.$message.success('提交成功')
            this.$emit('updateStatistics')
            this.submitDialogclose()
            this.resetCondition()
          } else {
            throw res.message
          }
        })
        .catch((msg) => msg && this.$message.error(msg))
        .finally(() => (this.submitLoading = false))
    },
    /** 房屋重新选择 */
    roomReselect() {
      this.submitDialogclose()
      setTimeout(() => {
        this.allocationDialogVisible = true
      }, 200)
    },
    /** 催办 */
    handRushDoClick(row) {
      this.$confirm('确认发送催办短信？', '发送通知', { type: 'warning' }).then(() => {
        let params = {
          id: row.id || ''
        }
        this.$api.rentalHousingApi
          .submitUrge(params)
          .then((res) => {
            if (res.code === '200') {
              this.$message.success('催办成功')
              this.getTableData()
            } else {
              throw res.message
            }
          })
          .catch((msg) => msg && this.$message.error(msg || '催办失败'))
      })
    },
    /** 获取科室 */
    getDeptDataList() {
      this.$api.getSelectedDept().then((res) => {
        if (res.code == '200') {
          this.deptList = res.data
        }
      })
    },
    /** 获取申请类型 */
    getTypeDataList() {
      const params = {
        pageSize: 99999,
        pageNum: 1
      }
      this.$api.rentalHousingApi
        .queryDictPage(params)
        .then((res) => {
          if (res.code === '200') {
            res.data.records.forEach((it) => {
              // 只取启用的
              if (it.dictState !== '1') return
              if (it.dictType === 'fangxing') {
                this.houseTypeOptions.push(it)
              } else if (it.dictType === 'shenqingleixing') {
                this.applicationTypeOptions.push(it)
              }
            })
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取列表数据失败'))
        .finally(() => {})
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.getTableData()
    },
    // 每页的条数
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.paginationData.currentPage = 1
      this.getTableData()
    }
  }
}
</script>
<style lang="scss" scoped>
.wing-container {
  height: 100%;
  .table-search {
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    .table-search-left,
    .table-search-right {
      flex-shrink: 0;
    }
    .table-search-left {
      display: flex;
      align-items: center;
    }
  }
  .btn-box {
    margin-bottom: 10px;
  }
  .table-box {
    height: calc(100% - 82px);
    .table-list {
      height: calc(100% - 82px);
    }
  }
}
</style>