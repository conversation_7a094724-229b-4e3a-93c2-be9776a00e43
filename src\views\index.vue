<template>
  <!-- <div @click="changeReplaceRouter">123456</div> -->
  <div></div>
</template>

<script>
export default {
  data() {
    return {}
  },
  mounted() {
    console.log(this.$router.currentRoute.path, 'this.$router.currentRoute.path')
  },
  methods: {
    changeReplaceRouter() {
      this.$router.replace({
        path: this.$router.currentRoute.path,
        query: {
          name: '123'
        }
      })
    }
  }
}
</script>
