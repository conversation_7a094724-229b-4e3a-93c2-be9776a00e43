<!--
 * @Author: hedd
 * @Date: 2023-02-27 14:35:19
 * @LastEditTime: 2024-12-10 19:08:16
 * @LastEditTime: 2023-03-11 14:22:21
 * @LastEditTime: 2023-11-22 14:31:41
 * @FilePath: \ihcrs_pc\src\components\Echarts\index.vue
 * @Description:
-->
<template>
  <div :id="domId" class="echarts" :style="{ width: width, height: height }"></div>
</template>
<script>
import * as echarts from 'echarts'
import { throttle } from 'lodash/function'
export default {
  name: 'echarts',
  props: {
    domId: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    onTimeLineChange: {
      type: Boolean,
      default: false
    },
    // 是否点击图例自定义事件
    onLegendClickBool: {
      type: Boolean,
      default: false
    },
    // 是否监听可视页面大小改变图表
    isMonitor: {
      type: Boolean,
      default: true
    },
    /**
     * 是否需要xy轴名称省略后展示triggerEvent
     * 在xAxis或yAxis添加  triggerEvent: true 属性
     */
    isTrigger: {
      type: Boolean,
      default: false
    },
    // 展示那个轴的triggerEvent
    xyType: {
      type: String,
      default: 'xAxis'
    },
    // 超过多少个字展示  默认5个字
    showWord: {
      type: Number,
      default: 5
    }
  },
  computed: {
    // 图表DOM
    getDom() {
      const dom = document.getElementById(this.domId)
      return dom && echarts.init(dom)
      // const dom = document.getElementById(this.domId)
      // let myCharts = echarts.getInstanceByDom(dom)
      // if (myCharts) {
      //   myCharts.dispose()
      // }
      // myCharts = echarts.init(dom)
      // return myCharts
    },
    // 图表resize节流
    chartResize() {
      return throttle(() => {
        setTimeout(() => {
          this.getDom.resize()
        }, 100)
      }, 400)
    }
  },
  mounted() {
    if (this.isMonitor) {
      window.addEventListener('resize', this.chartResize)
    }
    if (this.onTimeLineChange) {
      this.onTimeLineChanged()
    }
    if (this.onLegendClickBool) {
      this.onLegendselectchanged()
    }
    if (this.isTrigger) {
      this.extension()
    }
    this.onClickChart()
  },
  destroyed() {
    window.removeEventListener('resize', this.chartResize)
  },
  methods: {
    // 图表初始化
    init(options) {
      this.getDom.resize()
      this.getDom.clear()
      this.getDom.setOption(options, true)
    },
    onTimeLineChanged() {
      this.getDom.on('timelinechanged', (data) => {
        this.$emit('timelinechanged', data)
      })
    },
    onClickChart() {
      // this.getDom.on('dataZoom', (params) => {
      //   params.event.stopPropagation()
      // })
      this.getDom.on('click', (data) => {
        this.$emit('onClickChart', data)
      })
    },
    onLegendselectchanged() {
      // customData 为自定义的data数据  用来查询点击获取更多数据  如后期其他组件需要此方法  再优化
      this.getDom.on('legendselectchanged', (data) => {
        let option = this.getDom.getOption()
        let obj = option.customData.find((ele) => ele.name == data.name)
        // 处理自己的自定义事件
        this.$emit('onClickChartLegend', { data: obj })
        // 阻止默认事件（原理就是将点击的图例重新选中）
        this.getDom.dispatchAction({
          type: 'legendSelect',
          name: data.name
        })
      })
    },
    /*
     * echart X轴文字太长悬浮
     * 在xAxis或yAxis添加  triggerEvent: true 属性
     */
    extension() {
      // 判断是否创建过div框,如果创建过就不再创建了
      // 该div用来盛放文本显示内容的，方便对其悬浮位置进行处理
      var html = document.querySelector('html')
      if (!document.getElementById('extension')) {
        var div = document.createElement('div')
        div.setAttribute('id', 'extension')
        div.style.display = 'block'
        html.appendChild(div)
      }
      var extension = document.getElementById('extension')
      this.getDom.on('mouseover', (params) => {
        if (params.componentType == this.xyType && params.value.length > this.showWord) {
          // 设置悬浮文本的位置以及样式
          var elementStyle =
            'position: absolute;z-index: 99999;color: #fff;font-size: 12px;padding: 5px;display: inline;border-radius: 4px;background-color: #303133;box-shadow: rgba(0, 0, 0, 0.3) 2px 2px 8px'
          extension.style.cssText = elementStyle
          extension.innerHTML = params.value
          html.onmousemove = function (event) {
            var xx = event.pageX - 10
            var yy = event.pageY + 15
            extension.style.top = yy + 'px'
            extension.style.left = xx + 'px'
          }
        }
      })
      this.getDom.on('mouseout', (params) => {
        if (params.componentType == this.xyType) {
          extension.style.display = 'none'
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.echarts {
  z-index: 2;
  overflow: hidden;
}
</style>
