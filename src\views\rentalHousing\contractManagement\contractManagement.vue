<template>
  <div style="height: 100%">
    <div class="special_box">
      <div class="content_box">
        <div class="tabsMenu">
          <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="履约中" name="first"></el-tab-pane>
            <el-tab-pane :label="`即将超期(${expiringCount})`" name="second"></el-tab-pane>
            <el-tab-pane label="未开始" name="zero"></el-tab-pane>
            <el-tab-pane label="已超期" name="third"></el-tab-pane>
            <el-tab-pane label="已结束" name="finish"></el-tab-pane>
          </el-tabs>
        </div>
        <div class="top_content">
          <el-input
            v-model="filterData.contractNumOrUserName"
            style="width: 200px; margin-right: 8px"
            placeholder="搜索合同号、承租人姓名"
            maxlength="60"
            onkeyup="if(value.length>25)value=value.slice(0,60)"
            @keyup.enter.native="searchByCondition"
          ></el-input>
          <el-input
            v-model="filterData.houseName"
            style="width: 150px; margin-right: 8px"
            placeholder="搜索房间名称"
            maxlength="60"
            onkeyup="if(value.length>25)value=value.slice(0,60)"
            @keyup.enter.native="searchByCondition"
          ></el-input>
          <el-cascader
            ref="spaceIds"
            v-model="filterData.spaceIds"
            filterable
            :show-all-levels="false"
            placeholder="请选择空间"
            :options="icmSpaceArray"
            :props="propsSpace"
            clearable
            style="margin-right: 8px"
            @change="spaceChang(filterData.spaceIds)"
          ></el-cascader>
          <el-date-picker
            v-model="filterData.date"
            type="datetimerange"
            start-placeholder="租期开始时间"
            range-separator="至"
            style="width: 380px; margin-right: 8px"
            end-placeholder="租期开始时间"
            :default-time="['00:00:00', '23:59:59']"
          >
          </el-date-picker>
          <el-date-picker
            v-model="filterData.dateEnd"
            type="datetimerange"
            start-placeholder="租期结束时间"
            range-separator="至"
            style="width: 380px"
            end-placeholder="租期结束时间"
            :default-time="['00:00:00', '23:59:59']"
          >
          </el-date-picker>
          <div class="button-group">
            <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db; margin-left: 20px" @click="resetCondition">重置</el-button>
            <el-button type="primary" style="font-size: 14px" @click="searchByCondition">查询</el-button>
          </div>
        </div>
        <div class="top_content">
          <el-button type="primary" style="font-size: 14px" @click="downloadFile('batch')">合同下载</el-button>
        </div>
        <div class="table_list" style="text-align: right; height: 100%">
          <el-table
            ref="materialTable"
            v-loading="tableLoading"
            :data="tableData"
            height="calc(100% - 10px)"
            border
            :header-cell-style="{ background: '#F6F5FA' }"
            style="width: 100%"
            :cell-style="{ padding: '8px 0 8px 0' }"
            stripe
            highlight-current-row
            :empty-text="emptyText"
            row-key="id"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column label="序号" type="index" width="70">
              <template slot-scope="scope">
                <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip prop="contractNum" label="合同编号">
              <template slot-scope="scope">
                <el-link type="primary" :underline="false" @click="operation(scope.row, 'details')">{{ scope.row.contractNum }}</el-link>
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip prop="secondPartyName" label="承租人"></el-table-column>
            <el-table-column show-overflow-tooltip prop="houseName" label="房间名称"></el-table-column>
            <el-table-column prop="spaceName" show-overflow-tooltip label="空间位置"></el-table-column>
            <el-table-column prop="rentingUnitPrice" show-overflow-tooltip label="租金单价(元/㎡)"></el-table-column>
            <el-table-column prop="rentingMoney" label="出租租金(元)" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="租期开始日期">
              <template slot-scope="scope">
                <span>{{ moment(scope.row.rentingStartTime).format('YYYY-MM-DD') }}</span>
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip label="租期结束日期">
              <template slot-scope="scope">
                <span>{{ moment(scope.row.rentingEndTime).format('YYYY-MM-DD') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="165">
              <template slot-scope="scope">
                <div style="display: flex; gap: 10px">
                  <el-link type="primary" @click="operation(scope.row, 'details')"> 详情 </el-link>
                  <el-link type="primary" :disabled="!scope.row.attachmentUrl" @click="operation(scope.row, 'download')"> 下载 </el-link>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="" style="padding-top: 10px; text-align: right">
          <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="paginationData.currentPage"
            :page-sizes="[15, 30, 50, 100]"
            :page-size="paginationData.pageSize"
            :total="paginationData.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { transData } from '@/util'
import moment from 'moment'
import axios from 'axios'
export default {
  name: 'punishmentSystemList',
  beforeRouteEnter(to, from, next) {
    let names = []
    to.matched.map((v, i) => {
      if (i > 0) {
        v.components.default.name && names.push(v.components.default.name)
      }
    })
    next((vm) => {
      console.log(names, 'names')
      vm.$store.commit('keepAlive/add', names)
    })
  },
  async beforeRouteLeave(to, from, next) {
    // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
    if (!['rentalHousingContractManagementDetail'].includes(to.name)) {
      // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      moment,
      activeName: 'first',
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      tableData: [],
      // 列表过滤条件
      filterData: {
        contractStatus: '',
        houseName: '',
        contractNumOrUserName: '',
        date: [],
        filterData: '',
        spaceIds: '',
        dateEnd: []
      },
      emptyText: '暂无数据',
      tableLoading: false,
      pageSize: '',
      currentPage: '',
      propsSpace: {
        checkStrictly: true,
        emitPath: false,
        value: 'id',
        label: 'spaceName',
        children: 'list'
      },
      icmSpaceArray: [],
      spaceList: [],
      expiringCount: 0
    }
  },
  computed: {},
  created() {},
  mounted() {
    // 页面加载时调用 handleClick，并传递默认选中的 tab
    this.handleClick({ name: this.activeName })
    this.spaceTreeListFn()
    this.expiringSoonCount()
  },
  activated() {
    // 页面加载时调用 handleClick，并传递默认选中的 tab
    this.handleClick({ name: this.activeName })
    this.spaceTreeListFn()
    this.expiringSoonCount()
  },
  methods: {
    // 获取合同列表
    getContractList(typeId) {
      const params = {
        pageNum: this.paginationData.currentPage, // 页码
        pageSize: this.paginationData.pageSize, //	页大小
        houseName: this.filterData.houseName, // 房源名称
        contractNumOrUserName: this.filterData.contractNumOrUserName, // 合同号，承租人姓名
        spaceIds: this.filterData.spaceIds, // 空间code
        contractStatus: { first: '1', finish: '2', second: '3', third: '4', zero: '0' }[typeId] || '1',
        rentingStartTime1: this.filterData.date && this.filterData.date.length > 0 ? moment(this.filterData.date[0]).format('YYYY-MM-DD HH:mm:ss') : '',
        rentingStartTime2: this.filterData.date && this.filterData.date.length > 0 ? moment(this.filterData.date[1]).format('YYYY-MM-DD HH:mm:ss') : '',
        rentingEndTime1: this.filterData.dateEnd && this.filterData.dateEnd.length > 0 ? moment(this.filterData.dateEnd[0]).format('YYYY-MM-DD HH:mm:ss') : '',
        rentingEndTime2: this.filterData.dateEnd && this.filterData.dateEnd.length > 0 ? moment(this.filterData.dateEnd[1]).format('YYYY-MM-DD HH:mm:ss') : ''
      }
      this.tableLoading = true
      this.$api.contractManagementList(params).then((res) => {
        if (res.code == '200') {
          this.tableData = res.data.records
          this.paginationData.total = res.data.total
        } else {
          this.$message.error(res.message)
        }
        this.tableLoading = false
      })
    },
    // 切换tabs
    handleClick(tab, event) {
      this.paginationData.currentPage = 1
      this.paginationData.pageSize = 15
      this.getContractList(tab.name)
      this.expiringSoonCount()
    },
    // 条件查询
    searchByCondition() {
      this.paginationData.currentPage = 1
      this.getContractList(this.activeName)
    },
    // 重置查询条件
    resetCondition() {
      this.paginationData = {
        currentPage: 1,
        pageSize: 15,
        total: 0
      }
      this.filterData = {
        planName: '',
        dept: '',
        personName: '',
        status: '0',
        date: [],
        dateEnd: [],
        acceptance: ''
      }
      this.getContractList(this.activeName)
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.getContractList(this.activeName)
    },
    // 每页的条数
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.paginationData.currentPage = 1
      this.getContractList(this.activeName)
    },
    // 列表操作
    operation(row, type) {
      if (type == 'details') {
        this.$router.push({
          name: 'rentalHousingContractManagementDetail',
          query: {
            id: row.id
          }
        })
      } else if (type == 'download') {
        this.downloadFile('one', row.id)
      }
    },
    // 批量下载
    downloadFile(type, id) {
      const params = {
        pageNum: this.paginationData.currentPage, // 页码
        pageSize: this.paginationData.pageSize, //	页大小
        houseName: this.filterData.houseName, // 房源名称
        contractNumOrUserName: this.filterData.contractNumOrUserName, // 合同号，承租人姓名
        spaceIds: this.filterData.spaceIds, // 空间code
        contractStatus: { first: '1', finish: '2', second: '3', third: '4', zero: '0' }[this.activeName] || '1',
        rentingStartTime1: this.filterData.date && this.filterData.date.length > 0 ? moment(this.filterData.date[0]).format('YYYY-MM-DD HH:mm:ss') : '',
        rentingStartTime2: this.filterData.date && this.filterData.date.length > 0 ? moment(this.filterData.date[1]).format('YYYY-MM-DD HH:mm:ss') : '',
        rentingEndTime1: this.filterData.dateEnd && this.filterData.dateEnd.length > 0 ? moment(this.filterData.dateEnd[0]).format('YYYY-MM-DD HH:mm:ss') : '',
        rentingEndTime2: this.filterData.dateEnd && this.filterData.dateEnd.length > 0 ? moment(this.filterData.dateEnd[1]).format('YYYY-MM-DD HH:mm:ss') : ''
      }
      if (type == 'batch') {
        if (!this.selectionList) {
          const ids = this.tableData.map((i) => i.id)
          params.ids = ids.join(',')
        } else {
          const ids = this.selectionList.map((i) => i.id)
          params.ids = ids.join(',')
        }
      } else {
        params.ids = id
      }
      this.downloadPort(params)
    },
    downloadPort(params) {
      axios
        .post(__PATH.VUE_RHMS_API + 'houseContract/downloadContractBatch', params, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer ' + this.$store.state.user.token,
            hospitalCode: this.$store.state.user.userInfo.user.hospitalCode,
            unitCode: this.$store.state.user.userInfo.user.unitCode
          },
          responseType: 'blob'
        })
        .then((res) => {
          this.download(res)
        })
    },
    download(res) {
      const downloadElement = document.createElement('a')
      const href = window.URL.createObjectURL(res.data) // 创建下载的链接
      downloadElement.style.display = 'none'
      downloadElement.href = href
      downloadElement.download = this.$tools.getFileName(res) // 下载后文件名
      document.body.appendChild(downloadElement)
      downloadElement.click() // 点击下载
      document.body.removeChild(downloadElement) // 下载完成移除元素
      window.URL.revokeObjectURL(href) // 释放掉blob对象
    },
    handleSelectionChange(val) {
      this.selectionList = val
    },
    //  ------------------------------获取空间结构 Tree And fn
    spaceTreeListFn() {
      this.$api.spatialInformationTree().then((res) => {
        if (res.code == 200) {
          this.spaceList = res.data
          this.icmSpaceArray = transData(res.data, 'id', 'parentId', 'list')
        }
      })
    },
    spaceChang(val) {
      if (val) {
        this.spaceList.forEach((item) => {
          if (item.id == val) {
            this.filterData.spaceName = item.spaceName || item.id
          }
        })
      } else {
        this.filterData.spaceName = ''
      }
    },
    // 即将过期数量
    expiringSoonCount() {
      this.$api.expiringSoonCount().then((res) => {
        if (res.code == '200') {
          this.expiringCount = res.data
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.tabsItem {
  padding: 8px 0;
  width: 200px;
  margin: 0 auto;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #5b5b5b;
  cursor: pointer;
  .item {
    width: 200px !important;
  }
  > span {
    width: 200px !important;
    margin: 0 auto !important;
  }
}
.tabsItem:hover {
  color: #5188fc;
}
:deep(.el-tabs--left) {
  width: 200px;
  margin: 20px auto 0;
}
:deep(.el-table__row) {
  :last-child > .cell {
    display: flex;
    justify-content: space-between;
  }
}
.special_box {
  height: 100%;
  display: flex;
  .content_box {
    width: calc(100% - 30px);
    margin: 15px;
    border-radius: 10px;
    padding: 20px 25px 25px;
    background: #fff;
    display: flex;
    flex-direction: column;
    .tabsMenu {
      margin-bottom: 15px;
      :deep(.el-tabs__item) {
        width: 100px;
      }
    }
    .top_content {
      width: 100%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin-bottom: 15px;
      // // gap: 10px;
      // > :deep(.el-input) {
      //   width: 20% !important;
      // }
      > :deep(.el-select) {
        margin-right: 20px;
      }
    }
    .table_list {
      .disable {
        color: #414653;
        span {
          display: inline-block;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: #ccced3;
        }
      }
      .enable {
        color: #08cb83;
        span {
          display: inline-block;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: #08cb83;
        }
      }
      .acceptanceText {
        cursor: pointer;
      }
      .acceptanceColor0 {
        color: #414653;
      }
      .acceptanceColor1 {
        color: #08cb83;
      }
      .acceptanceColor2 {
        color: #f56c6c;
      }
    }
  }
}
:deep(.el-tabs__nav) {
  width: 100%;
  .el-tabs__item {
    padding: 0 10px;
    width: 50%;
    text-align: center;
  }
}
</style>
<style lang="scss">
.messageIndex {
  z-index: 3000 !important;
}
.recordContent {
  .recordWrap {
    display: flex;
    margin: 5px 0;
    .recordTitle {
      width: 70px;
      text-align: right;
      display: block;
    }
    .textRecord {
      width: calc(100% - 70px);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .recordWrap:nth-child(n + 5) {
    text-align: right;
  }
  //   .button-group {
  //   display: flex;
  //   margin-left: auto; /* 将整个按钮组推到右侧 */
  //   gap: 10px; /* 按钮之间的间距为 10px */
  // }
}
</style>
