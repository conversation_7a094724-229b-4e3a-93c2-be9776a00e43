<template>
  <PageContainer>
    <div slot="content" class="who">
      <div style="display: flex; margin-bottom: 10px">
        <el-select v-model="filters.unitType" placeholder="请选择单位类型" clearable class="ipt">
          <el-option v-for="item in unitTypeList" :key="item.id" :label="item.dictName" :value="item.id"></el-option>
        </el-select>
        <el-button type="primary" @click="inquiry">查询</el-button>
        <el-button type="primary" plain @click="reset">重置</el-button>
        <!-- <el-button size="medium" type="primary" @click="AddFn()">新增</el-button> -->
      </div>
      <div slot="content" style="height: 100%">
        <div class="contentTable">
          <div class="contentTable-main table-content">
            <el-table :data="tableData" :height="tableHeight" stripe border>
              <el-table-column type="index" label="序号" width="50" align="center">
                <template slot-scope="scope">
                  <span>{{ (pagination.current - 1) * pagination.size + scope.$index + 1 }}</span>
                </template>
              </el-table-column>
              <el-table-column v-for="item in tableHeaders" :key="item.porp" :prop="item.porp" :label="item.label" :width="item.width" show-overflow-tooltip align="center">
                <template slot-scope="scope">
                  <span v-if="item.porp == 'unitComName'" class="color_blue" @click="ViewFn(scope.row)">
                    {{ scope.row.unitComName }}
                  </span>
                  <span v-else-if="item.porp == 'nature'">{{ scope.row.nature == 1 ? '院内' : '院外' }}</span>
                  <span v-else-if="item.porp == 'unitManagerCount'" class="color_blue" @click="details(scope.row, 1)">{{ scope.row.unitManagerCount }}</span>
                  <span v-else-if="item.porp == 'unitUserCount'" class="color_blue" @click="details(scope.row, 2)">{{ scope.row.unitUserCount }}</span>
                  <span v-else>&nbsp;{{ scope.row[item.porp] }}</span>
                </template>
              </el-table-column>
              <el-table-column fixed="right" label="操作" width="150" align="center">
                <template slot-scope="scope">
                  <span style="color: #5188fc; margin-right: 10px; cursor: pointer" @click="EditFn(scope.row)">编辑</span>
                  <span style="color: red; cursor: pointer" @click="DeleteFn(scope.row)">删除</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div class="contentTable-footer">
          <el-pagination
            :current-page="pagination.current"
            :page-sizes="[15, 30, 50, 100]"
            :page-size="pagination.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin.js'
export default {
  name: 'unitManage',
  mixins: [tableListMixin],
  beforeRouteEnter(to, from, next) {
    let names = []
    to.matched.map((v, i) => {
      if (i > 0) {
        v.components.default.name && names.push(v.components.default.name)
      }
    })
    // 进入页面时，先将当前页面的 name 信息存入 keep-alive 全局状态
    setTimeout(() => {
      next((vm) => {
        vm.$store.commit('keepAlive/add', names)
      })
    }, 0)
  },
  async beforeRouteLeave(to, from, next) {
    // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
    if (!['companyMess', 'detailedInformation'].includes(to.name)) {
      // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      filters: {
        unitType: ''
      },
      unitTypeList: [],
      tableData: [],
      tableHeaders: [
        // {
        //   porp: "unitComCode",
        //   label: "单位ID",
        // },
        {
          porp: 'unitComName',
          label: '单位名称'
        },
        {
          porp: 'nature',
          label: '单位性质'
        },
        {
          porp: 'unitType',
          label: '单位类别'
        },
        {
          porp: 'principalName',
          label: '负责人姓名',
          width: 120
        },
        {
          porp: 'principalPhone',
          label: '负责人电话',
          width: 120
        },
        {
          porp: 'serviceProfile',
          label: '服务简介'
        },
        {
          porp: 'unitManagerCount',
          label: '下属部门数量'
        },
        {
          porp: 'unitUserCount',
          label: '下属人员数量'
        },
        {
          porp: 'complaintCall',
          label: '投诉电话'
        },
        {
          porp: 'remark',
          label: '备注'
        }
      ],
      pagination: {
        current: 1,
        size: 15
      }, // 分页数据
      total: 0, // 数据总条数
      // -------------------Dialog_导入
      addFileName: 'company'
    }
  },
  created() {},
  activated() {
    this.unitListFn()
  },
  mounted() {
    this.unitListFn()
    this.valveTypeListFn()
  },
  methods: {
    //  获取单位列表（分页）
    unitListFn() {
      this.$api
        .unitList({
          ...this.filters,
          ...this.pagination
        })
        .then((res) => {
          // this.treeLoading = false;
          if (res.code == 200) {
            this.tableData = res.data.records
            this.pagination.current = res.data.current
            this.pagination.size = res.data.size
            this.total = res.data.total
          }
        })
    },
    details(row, type) {
      this.$router.push({
        path: '/detailedInformation',
        query: { row: row, type: type }
      })
    },
    handleSizeChange(val) {
      this.pagination.current = 1
      this.pagination.size = val
      this.unitListFn()
    },
    handleCurrentChange(val) {
      this.pagination.current = val
      this.unitListFn()
    },
    //  获取单位类型字典列表
    valveTypeListFn() {
      this.$api
        .valveTypeList({
          typeValue: 'DWLX'
        })
        .then((res) => {
          if (res.code == 200) {
            this.unitTypeList = res.data
          }
        })
    },
    inquiry() {
      this.filters.unitType = this.filters.unitType
      this.unitListFn()
    },
    reset() {
      this.filters.unitType = ''
      this.unitListFn()
    },
    // -----------------------------------Table-Fn
    // AddFn() {
    //   this.$router.push({
    //     path: '/companyMess',
    //     query: { type: 'Add' }
    //   })
    // },
    EditFn(row) {
      this.$router.push({
        name: 'companyMess',
        // path: '/organizationManage/unitManage/companyMess',
        query: { type: 'Edit', id: row.id }
      })
    },
    DeleteFn(val) {
      this.$confirm('确认删除选中的单位吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$api.deleteUnit({ id: val.id }, { 'operation-type': 3, 'operation-id': val.id, 'operation-name': val.unitComName }).then((res) => {
            if (res.code == 200) {
              this.unitListFn()
              this.$message({
                message: res.msg,
                type: 'success'
              })
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    ViewFn(row) {
      this.$router.push({
        // path: '/organizationManage/unitManage/companyMess',
        name: 'companyMess',
        query: { type: 'View', id: row.id }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.who {
  height: 100%;
  background-color: #fff;
  border-radius: 5px;
  padding: 10px;
}
.ipt {
  width: 200px;
  margin-right: 10px;
}
.color_blue {
  color: #5188fc;
  cursor: pointer;
}
.contentTable {
  height: calc(100% - 70px);
  background: #fff;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  overflow: auto;
  .contentTable-main {
    flex: 1;
    overflow: auto;
  }
  .contentTable-footer {
    padding: 10px 0 0;
  }
  .alarmLevel {
    padding: 3px 6px;
    border-radius: 4px;
    color: #fff;
    line-height: 14px;
  }
  .alarmStatus {
    position: relative;
    display: inline-block;
    padding-left: 12px;
    .alarmStatusIcon {
      display: inline-block;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 8px;
      height: 8px;
      border-radius: 100%;
    }
  }
  .collectIcon {
    font-size: 16px;
    margin-right: 4px;
  }
}
</style>
