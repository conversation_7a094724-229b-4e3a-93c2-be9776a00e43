/*
 * @Author: hedd
 * @Date: 2023-10-23 16:41:16
 * @LastEditTime: 2025-04-11 17:09:57
 * @LastEditTime: 2025-01-21 11:31:26
 * @FilePath: \ihcrs_pc\src\util\dict.js
 * @Description:
 */
import on_sxt from '@/assets/images/monitor/on_sxt.png'
import off_sxt from '@/assets/images/monitor/off_sxt.png'
import on_mjsb from '@/assets/images/monitor/on_mjsb.png'
import off_mjsb from '@/assets/images/monitor/off_mjsb.png'
import on_rqsb from '@/assets/images/monitor/on_rqsb.png'
import off_rqsb from '@/assets/images/monitor/off_rqsb.png'
import on_oneKey from '@/assets/images/monitor/on_oneKey.png'
import off_oneKey from '@/assets/images/monitor/off_oneKey.png'
import on_ygwg from '@/assets/images/monitor/on_ygwg.png'
import off_ygwg from '@/assets/images/monitor/off_ygwg.png'
import on_sb from '@/assets/images/monitor/on_sb.png'
import off_sb from '@/assets/images/monitor/off_sb.png'
import on_xfsx from '@/assets/images/monitor/on_xfsx.png'
import off_xfsx from '@/assets/images/monitor/off_xfsx.png'
import on_xfsb from '@/assets/images/monitor/on_xfsb.png'
import off_xfsb from '@/assets/images/monitor/off_xfsb.png'
import on_pyfj from '@/assets/images/monitor/on_pyfj.png'
import off_pyfj from '@/assets/images/monitor/off_pyfj.png'
import on_fhm from '@/assets/images/monitor/on_fhm.png'
import off_fhm from '@/assets/images/monitor/off_fhm.png'
import on_mdss from '@/assets/images/monitor/on_mdss.png'
import off_mdss from '@/assets/images/monitor/off_mdss.png'
import on_gbzd from '@/assets/images/monitor/on_gbzd.png'
import off_gbzd from '@/assets/images/monitor/off_gbzd.png'
import on_led from '@/assets/images/monitor/on_led.png'
import off_led from '@/assets/images/monitor/off_led.png'
import on_dmtxsp from '@/assets/images/monitor/on_dmtxsp.png'
import off_dmtxsp from '@/assets/images/monitor/off_dmtxsp.png'
// import on_es from '@/assets/images/monitor/on_es.png'
import on_ydaq from '@/assets/images/monitor/on_ydaq.png'
import off_ydaq from '@/assets/images/monitor/off_ydaq.png'
import on_flrb from '@/assets/images/monitor/on_flrb.png'
import off_flrb from '@/assets/images/monitor/off_flrb.png'
import off_sec from '@/assets/images/monitor/off_sec.png'
import off_ent from '@/assets/images/monitor/off_ent.png'
import off_sos from '@/assets/images/monitor/off_sos.png'
import on_sec from '@/assets/images/monitor/on_sec.png'
import on_ent from '@/assets/images/monitor/on_ent.png'
import on_sos from '@/assets/images/monitor/on_sos.png'
import on_ipqz from '@/assets/images/monitor/ipqz.png'
import off_ipqz from '@/assets/images/monitor/ipqz.png'
import badgeoff from '@/assets/images/monitor/badgeoff.png'
import badgeon from '@/assets/images/monitor/badgeon.png'
export const monitorTypeList = [
  {
    projectName: '污水监测',
    projectCode: 'IEMC-Sewage',
    formPath: 'sewageConfiguration/medicalGasMonitorForm',
    hasScada: true,
    hasImportBtn: true
  },
  {
    projectName: '环境监测',
    projectCode: '0918f947fec94b6ea6c8041dc63b3134',
    defaultJumpPath: '/envirMenu/envirOverview',
    formPath: 'envirConfiguration/envirMonitorForm',
    hasImportBtn: true
  },
  {
    projectName: '空调监测',
    projectCode: 'fb78bc9c7e5311ec8e4f000c2912d8ca',
    defaultJumpPath: '/airMenu/airOverview',
    formPath: 'airConfiguration/airMonitorForm',
    hasScada: true,
    hasImportBtn: true
  },
  {
    projectName: '给排水监测',
    defaultJumpPath: '/sewerageMenu/sewerageOverview',
    projectCode: 'b5587fbbe453422ebc937cc0b7c69wsx',
    formPath: 'sewerConfiguration/sewerMonitorForm',
    hasScada: true,
    hasImportBtn: true
  },
  {
    projectName: '冷热源监测',
    projectCode: 'b5587fbbe453422ebc937cc0b7c69a3c',
    defaultJumpPath: '/coldHeat/coldOverview',
    formPath: 'coldHotConfiguration/coldHotMonitorForm',
    hasScada: true,
    hasImportBtn: true
  },
  {
    projectName: '锅炉监测',
    projectCode: 'IEMC-Boiler',
    formPath: 'boilerConfiguration/boilerMonitorForm',
    hasScada: true,
    hasImportBtn: true
  },
  {
    projectName: '电梯监测',
    projectCode: '713e24b03094410499db0b08a2eccbcc',
    formPath: 'elevatorConfiguration/elevatorMonitorForm',
    policeDetailsPath: 'allAlarm/alarmDetail',
    alarmDetailsPath: 'managementConfig/elevatorAlarmConfiguration/alarmConfigForm',
    tableColumn: 'elevatorTableColumn',
    requestUrl: __PATH.VUE_IEMC_ELEVATOR_API,
    hasScada: true
  },
  {
    projectName: '医用气体',
    projectCode: '73e7aab447b34971b9ae6d8dae034aa3',
    defaultJumpPath: '/medicalGas/realtimeMonitor',
    formPath: 'medicalGasConfiguration/medicalGasMonitorForm',
    policeDetailsPath: 'allAlarm/alarmDetail',
    tableColumn: 'medicalGasTableColumn',
    requestUrl: __PATH.VUE_IEMC_API,
    hasScada: true,
    hasImportBtn: true
  },
  {
    projectName: 'UPS监测',
    projectCode: '01f4bd80e5c060809aae72c7470e8be30',
    formPath: 'upsConfiguration/medicalGasMonitorForm',
    policeDetailsPath: 'allAlarm/alarmDetail',
    tableColumn: 'medicalGasTableColumn',
    hasScada: true,
    hasImportBtn: true
  },
  {
    projectName: '配电监测',
    projectCode: 'IEMC-Electricity',
    formPath: 'powerDistributionConfiguration/powerDistributionMonitorForm',
    hasScada: true,
    hasImportBtn: true
  },
  {
    projectName: '风冷热泵监测',
    projectCode: 'IEMC-AirCooledHeatPump',
    formPath: 'airCooledConfiguration/airCooledMonitorForm',
    hasScada: true,
    hasImportBtn: true
  },
  {
    projectName: '照明监测',
    projectCode: '00f4ad80e5c04809aae72c7470e8be28',
    formPath: 'lightConfiguration/lightMonitorForm',
    hasScada: true
  },
  {
    projectName: '能耗计量监测',
    projectCode: 'IEMC-EnergyMetering',
    formPath: 'energyConfiguration/energyMonitorForm',
    hasImportBtn: true
  },
  {
    projectName: '安防系统监测',
    projectCode: 'IEMC-SecuritySystem',
    defaultJumpPath: '/securityMenu/securityOverview'
  },
  {
    projectName: '消防系统监测',
    projectCode: 'IEMC-FireAlarmSystem',
    defaultJumpPath: '/fireControlMenu/fireControlOverview'
  },
  {
    projectName: '多媒体系统监测',
    projectCode: 'IEMC-AudioAndVideoSystem',
    defaultJumpPath: '/multiMediaMenu/multiMediaOverview'
  },
  {
    projectName: '摄像头设备',
    projectCode: 'IEMC-CameraEquipment',
    formPath: 'cameraConfiguration/securityMonitorForm',
    formBatchPath: 'cameraConfiguration/securityBatchMonitorForm',
    menuAuth: '/monitoringConfig/cameraConfiguration',
    type: 'security',
    hasImportBtn: true,
    parentCode: 'IEMC-SecuritySystem'
  },
  {
    projectName: '门禁设备',
    projectCode: 'IEMC-AccessControlEquipment',
    formPath: 'guardConfiguration/securityMonitorForm',
    formBatchPath: 'guardConfiguration/securityBatchMonitorForm',
    menuAuth: '/monitoringConfig/guardConfiguration',
    type: 'security',
    hasImportBtn: true,
    parentCode: 'IEMC-SecuritySystem'
  },
  {
    projectName: '入侵设备',
    projectCode: 'IEMC-IntrusionEquipment',
    formPath: 'invadeConfiguration/securityMonitorForm',
    formBatchPath: 'invadeConfiguration/securityBatchMonitorForm',
    menuAuth: '/monitoringConfig/invadeConfiguration',
    type: 'security',
    hasImportBtn: true,
    parentCode: 'IEMC-SecuritySystem'
  },
  {
    projectName: '一键报警',
    projectCode: 'IEMC-OneKeyAlarm',
    formPath: 'oneKeyConfiguration/securityMonitorForm',
    formBatchPath: 'oneKeyConfiguration/securityBatchMonitorForm',
    menuAuth: '/monitoringConfig/oneKeyConfiguration',
    type: 'security',
    hasImportBtn: true,
    parentCode: 'IEMC-SecuritySystem'
  },
  {
    projectName: '报警定位卡',
    projectCode: 'IEMC_AlarmCard',
    formPath: 'alarmPositioningConfiguration/securityMonitorForm',
    formBatchPath: 'alarmPositioningConfiguration/securityBatchMonitorForm',
    menuAuth: '/monitoringConfig/alarmPositioningConfiguration',
    type: 'security',
    hasImportBtn: true,
    parentCode: 'IEMC-SecuritySystem'
  },
  {
    projectName: '保安定位卡',
    projectCode: 'IEMC_PositioningCard',
    formPath: 'securityPositioningConfiguration/securityMonitorForm',
    formBatchPath: 'securityPositioningConfiguration/securityBatchMonitorForm',
    menuAuth: '/monitoringConfig/securityPositioningConfiguration',
    type: 'security',
    hasImportBtn: true,
    parentCode: 'IEMC-SecuritySystem'
  },
  {
    projectName: '智能安检筛查',
    projectCode: 'IEMC-SecurityDoor',
    formPath: 'ecurityScreeningConfiguration/securityMonitorForm',
    formBatchPath: 'ecurityScreeningConfiguration/securityBatchMonitorForm',
    menuAuth: '/monitoringConfig/ecurityScreeningConfiguration',
    type: 'security',
    hasImportBtn: true,
    parentCode: 'IEMC-SecuritySystem'
  },
  {
    projectName: '固定报警按钮',
    projectCode: 'IEMC-FixedAlarmBtn',
    formPath: 'fixedButtonConfiguration/securityMonitorForm',
    formBatchPath: 'fixedButtonConfiguration/securityBatchMonitorForm',
    menuAuth: '/monitoringConfig/fixedButtonConfiguration',
    type: 'security',
    hasImportBtn: true,
    parentCode: 'IEMC-SecuritySystem'
  },
  {
    projectName: 'ip求助',
    projectCode: 'IEMC-ipSeekHelp',
    formPath: 'helpConfiguration/securityMonitorForm',
    formBatchPath: 'helpConfiguration/securityBatchMonitorForm',
    menuAuth: '/monitoringConfig/helpConfiguration',
    type: 'security',
    hasImportBtn: true,
    parentCode: 'IEMC-SecuritySystem'
  },
  {
    projectName: '报警定位胸卡',
    projectCode: 'IEMC_AlarmChestCard',
    formPath: 'positioningChestCard/securityMonitorForm',
    formBatchPath: 'positioningChestCard/securityBatchMonitorForm',
    menuAuth: '/monitoringConfig/positioningChestCard',
    type: 'security',
    hasImportBtn: true,
    parentCode: 'IEMC-SecuritySystem'
  },
  {
    projectName: '烟感温感',
    projectCode: 'IEMC-SmokeAndTemperatureSensing',
    formPath: 'sensationConfiguration/securityMonitorForm',
    formBatchPath: 'sensationConfiguration/securityBatchMonitorForm',
    menuAuth: '/monitoringConfig/sensationConfiguration',
    type: 'security',
    hasImportBtn: true,
    parentCode: 'IEMC-FireAlarmSystem'
  },
  {
    projectName: '用电安全',
    projectCode: 'IEMC-ElectricitySafe',
    formPath: 'electricitySafetyConfiguration/securityMonitorForm',
    formBatchPath: 'electricitySafetyConfiguration/securityBatchMonitorForm',
    menuAuth: '/monitoringConfig/electricitySafetyConfiguration',
    type: 'security',
    hasImportBtn: true,
    parentCode: 'IEMC-FireAlarmSystem'
  },
  {
    projectName: '手报',
    projectCode: 'IEMC-HandNewspaper',
    formPath: 'manualConfiguration/securityMonitorForm',
    formBatchPath: 'manualConfiguration/securityBatchMonitorForm',
    menuAuth: '/monitoringConfig/manualConfiguration',
    type: 'security',
    hasImportBtn: true,
    parentCode: 'IEMC-FireAlarmSystem'
  },
  {
    projectName: '消防水箱',
    projectCode: 'IEMC-FireWaterTank',
    formPath: 'fireTankConfiguration/securityMonitorForm',
    formBatchPath: 'fireTankConfiguration/securityBatchMonitorForm',
    menuAuth: '/monitoringConfig/fireTankConfiguration',
    type: 'security',
    hasScada: true,
    hasImportBtn: true,
    parentCode: 'IEMC-FireAlarmSystem'
  },
  {
    projectName: '消防水泵',
    projectCode: 'IEMC-FirePump',
    formPath: 'firePumpConfiguration/securityMonitorForm',
    formBatchPath: 'firePumpConfiguration/securityBatchMonitorForm',
    menuAuth: '/monitoringConfig/firePumpConfiguration',
    type: 'security',
    hasScada: true,
    hasImportBtn: true,
    parentCode: 'IEMC-FireAlarmSystem'
  },
  {
    projectName: '防排烟风机',
    projectCode: 'IEMC-Anti-exhaustFan',
    formPath: 'smokeFanConfiguration/securityMonitorForm',
    formBatchPath: 'smokeFanConfiguration/securityBatchMonitorForm',
    menuAuth: '/monitoringConfig/smokeFanConfiguration',
    type: 'security',
    hasImportBtn: true,
    parentCode: 'IEMC-FireAlarmSystem'
  },
  {
    projectName: '防火门',
    projectCode: 'IEMC-FireDoor',
    formPath: 'fireProofConfiguration/securityMonitorForm',
    formBatchPath: 'fireProofConfiguration/securityBatchMonitorForm',
    menuAuth: '/monitoringConfig/fireProofConfiguration',
    type: 'security',
    hasImportBtn: true,
    parentCode: 'IEMC-FireAlarmSystem'
  },
  {
    projectName: '末端试水',
    projectCode: 'IEMC-EndTestWater',
    formPath: 'endWaterConfiguration/securityMonitorForm',
    formBatchPath: 'endWaterConfiguration/securityBatchMonitorForm',
    menuAuth: '/monitoringConfig/endWaterConfiguration',
    type: 'security',
    hasScada: true,
    hasImportBtn: true,
    parentCode: 'IEMC-FireAlarmSystem'
  },
  {
    projectName: '广播终端',
    projectCode: 'IEMC-BroadcastTerminal',
    formPath: 'broadcastConfiguration/securityMonitorForm',
    formBatchPath: 'broadcastConfiguration/securityBatchMonitorForm',
    menuAuth: '/monitoringConfig/broadcastConfiguration',
    type: 'security',
    hasImportBtn: true,
    parentCode: 'IEMC-AudioAndVideoSystem'
  },
  {
    projectName: 'LED屏幕',
    projectCode: 'IEMC-LEDScreen',
    formPath: 'LEDConfiguration/securityMonitorForm',
    formBatchPath: 'LEDConfiguration/securityBatchMonitorForm',
    menuAuth: '/monitoringConfig/LEDConfiguration',
    type: 'security',
    hasImportBtn: true,
    parentCode: 'IEMC-AudioAndVideoSystem'
  },
  {
    projectName: '多媒体屏幕',
    projectCode: 'IEMC-MultimediaScreen',
    formPath: 'mediaConfiguration/securityMonitorForm',
    formBatchPath: 'mediaConfiguration/securityBatchMonitorForm',
    menuAuth: '/monitoringConfig/mediaConfiguration',
    type: 'security',
    hasImportBtn: true,
    parentCode: 'IEMC-AudioAndVideoSystem'
  }
]
export const monitorItemImg = {
  'IEMC-AirCooledHeatPump': {
    // 风冷热泵
    on: on_flrb,
    off: off_flrb
  },
  'IEMC-CameraEquipment': {
    // 摄像头设备
    on: on_sxt,
    off: off_sxt
  },
  'IEMC-AccessControlEquipment': {
    // 门禁设备
    on: on_mjsb,
    off: off_mjsb
  },
  'IEMC-IntrusionEquipment': {
    // 入侵设备
    on: on_rqsb,
    off: off_rqsb
  },
  'IEMC-OneKeyAlarm': {
    // 一键报警
    on: on_oneKey,
    off: off_oneKey
  },
  IEMC_AlarmCard: {
    // 报警定位卡
    on: on_sos,
    off: off_sos
  },
  IEMC_PositioningCard: {
    // 保安定位卡
    on: on_sec,
    off: off_sec
  },
  'IEMC-SecurityDoor': {
    // 智能安检筛查
    on: on_ent,
    off: off_ent
  },
  'IEMC-FixedAlarmBtn': {
    // 固定报警按钮
    on: on_oneKey,
    off: off_oneKey
  },
  'IEMC-SmokeAndTemperatureSensing': {
    // 烟感温感
    on: on_ygwg,
    off: off_ygwg
  },
  'IEMC-HandNewspaper': {
    // 手报
    on: on_sb,
    off: off_sb
  },
  'IEMC-ElectricitySafe': {
    // 用电安全
    on: on_ydaq,
    off: off_ydaq
  },
  'IEMC-FireWaterTank': {
    // 消防水箱
    on: on_xfsx,
    off: off_xfsx
  },
  'IEMC-FirePump': {
    // 消防水泵
    on: on_xfsb,
    off: off_xfsb
  },
  'IEMC-Anti-exhaustFan': {
    // 防排烟风机
    on: on_pyfj,
    off: off_pyfj
  },
  'IEMC-FireDoor': {
    // 防火门
    on: on_fhm,
    off: off_fhm
  },
  'IEMC-EndTestWater': {
    // 末端试水
    on: on_mdss,
    off: off_mdss
  },
  'IEMC-BroadcastTerminal': {
    // 广播终端
    on: on_gbzd,
    off: off_gbzd
  },
  'IEMC-LEDScreen': {
    // LED屏幕
    on: on_led,
    off: off_led
  },
  'IEMC-MultimediaScreen': {
    // 多媒体屏幕
    on: on_dmtxsp,
    off: off_dmtxsp
  },
  'IEMC-ipSeekHelp': {
    // ip求助
    on: on_ipqz,
    off: off_ipqz
  },
  IEMC_AlarmChestCard: {
    on: badgeon,
    off: badgeoff
  }
}
export const stagingConfig = [
  {
    componentName: 'personalInfo',
    componentTitle: '用户信息'
  },
  {
    componentName: 'workOderType',
    componentTitle: '服务工单类型统计'
  },
  {
    componentName: 'warnManageTable',
    componentTitle: '报警管理',
    formPath: '/drag/warnRecord'
  },
  {
    componentName: 'msgReminder',
    componentTitle: '消息提醒',
    formPath: '/drag/todoAndmsgRecord?page=msg'
  },
  {
    componentName: 'todoItems',
    componentTitle: '待办事项',
    formPath: '/drag/todoAndmsgRecord?page=todo'
  },
  {
    componentName: 'quickNavigation',
    componentTitle: '快捷导航',
    formPath: '/apply/index'
  },
  {
    componentName: 'equipmentPatrolTask',
    componentTitle: '后勤设备巡检任务统计'
  },
  {
    componentName: 'logisticsService',
    componentTitle: '后勤服务数据统计'
  },
  {
    componentName: 'wasteDeliveryRecord',
    componentTitle: '医废出库记录统计'
  }
]
export const dataTypeList = [
  {
    type: 'day',
    name: '日'
  },
  {
    type: 'week',
    name: '周'
  },
  {
    type: 'month',
    name: '月'
  }
]
export const alarmLevelItem = {
  0: { text: '通知', color: '#3562DB' },
  1: { text: '一般', color: '#3562DB' },
  2: { text: '紧急', color: '#FF9435' },
  3: { text: '重要', color: '#FA403C' }
}
export const alarmAffirmItem = {
  0: '未确认',
  1: '真实报警',
  2: '误报',
  3: '演练',
  4: '调试'
}
export const alarmStatusList = {
  0: { text: '未处理', color: '#FA403C' },
  1: { text: '处理中', color: '#fd9434' },
  2: { text: '已关闭', color: '#999' }
}
export const alarmLevelOptions = [
  {
    value: '0',
    label: '通知'
  },
  {
    value: '1',
    label: '一般'
  },
  {
    value: '2',
    label: '紧急'
  },
  {
    value: '3',
    label: '重要'
  }
]
export const alarmStatusOptions = [
  {
    value: '0',
    label: '未处理'
  },
  {
    value: '1',
    label: '处理中'
  },
  {
    value: '2',
    label: '已关闭'
  }
]
function getCustomTypeByType(type) {
  return ['system', 'ioms', 'approve', 'icis', 'warn', 'drill', 'drillSure', 'life'][type]
}
/*
  字典与数据库表关系对应
  Objece.key-id
  name-lable
  type-system 0:系统消息 1:工单消息 2:审批消息 3:任务提箱 4:报警消息
  typeCode-type
*/
// icon为页面渲染所需要的对应icon component为一站式跳转对应渲染组件 elements是详情所需字段
export const msgIconList = {
  1: {
    name: '综合维修工单',
    type: getCustomTypeByType(1),
    typeName: '综合维修',
    typeCode: 1,
    component: 'workOrderDetailWX',
    elements: 'workNum,flowType,type',
    icon: 'questionnaire'
  },
  2: {
    name: '应急保洁工单',
    type: getCustomTypeByType(1),
    typeName: '应急保洁',
    typeCode: 2,
    component: 'workOrderDetailBJ',
    elements: 'workNum,flowType,type',
    icon: 'questionnaire'
  },
  3: {
    name: '运输工单',
    type: getCustomTypeByType(1),
    typeName: '中央运送',
    typeCode: 3,
    component: 'workOrderDetailYS',
    elements: 'workNum,flowType,type',
    icon: 'questionnaire'
  },
  4: {
    name: '订餐工单',
    type: getCustomTypeByType(1),
    typeCode: 4,
    elements: 'workNum,flowType,type',
    icon: 'questionnaire'
  },
  5: {
    name: '后勤投诉工单',
    type: getCustomTypeByType(1),
    typeName: '后勤投诉',
    typeCode: 5,
    component: 'workOrderDetailWX',
    elements: 'workNum,flowType,type',
    icon: 'questionnaire'
  },
  6: {
    name: '综合服务工单',
    type: getCustomTypeByType(1),
    typeName: '综合服务',
    typeCode: 6,
    elements: 'workNum,flowType,type',
    icon: 'questionnaire'
  },
  7: {
    name: '医疗设备工单',
    type: getCustomTypeByType(1),
    typeName: '医疗设备',
    typeCode: 7,
    elements: 'workNum,flowType,type',
    icon: 'questionnaire'
  },
  8: {
    name: '巡检自修工单',
    type: getCustomTypeByType(1),
    typeCode: 8,
    elements: 'workNum,flowType,type',
    icon: 'questionnaire'
  },
  9: {
    name: '巡检整改工单',
    type: getCustomTypeByType(1),
    typeCode: 9,
    elements: 'workNum,flowType,type',
    icon: 'questionnaire'
  },
  10: {
    name: '巡检报修工单',
    type: getCustomTypeByType(1),
    typeCode: 10,
    elements: 'workNum,flowType,type',
    icon: 'questionnaire'
  },
  11: {
    name: '随手拍工单',
    type: getCustomTypeByType(1),
    typeName: '随手拍',
    typeCode: 11,
    elements: 'workNum,flowType,type',
    component: 'workOrderDetailSSP',
    icon: 'questionnaire'
  },
  12: {
    name: '公车预订工单',
    type: getCustomTypeByType(1),
    typeName: '公车预订',
    typeCode: 15,
    elements: 'workNum,flowType,type',
    icon: 'questionnaire'
  },
  13: {
    name: '确警工单',
    type: getCustomTypeByType(1),
    typeCode: 16,
    elements: 'workNum,flowType,type',
    icon: 'questionnaire'
  },
  14: {
    name: '安全隐患',
    type: getCustomTypeByType(2),
    typeCode: 1,
    icon: 'report'
  },
  15: {
    name: '设备巡检任务',
    type: getCustomTypeByType(3),
    typeCode: 1,
    icon: 'inspection',
    elements: 'id'
  },
  16: {
    name: '设备保养任务',
    type: getCustomTypeByType(3),
    typeCode: 2,
    icon: 'inspection',
    elements: 'id'
  },
  17: {
    name: '综合巡检任务',
    type: getCustomTypeByType(3),
    typeCode: 3,
    icon: 'inspection',
    elements: 'id'
  },
  18: {
    name: '报警消息',
    type: getCustomTypeByType(4),
    typeCode: 1,
    icon: 'report-emergency',
    elements: 'alarmId'
  },
  19: {
    name: '会议通知',
    type: getCustomTypeByType(0),
    typeCode: 1,
    icon: 'announcement'
  },
  20: {
    name: '通知公告',
    type: getCustomTypeByType(0),
    typeCode: 2,
    icon: 'announcement'
  },
  21: {
    name: '院内新闻',
    type: getCustomTypeByType(0),
    typeCode: 3,
    icon: 'announcement'
  },
  22: {
    name: '问卷调查',
    type: 'question',
    typeCode: 4,
    icon: 'announcement'
  },
  23: {
    name: '后勤设备工单',
    type: getCustomTypeByType(1),
    typeCode: 17,
    elements: 'workNum,flowType,type',
    icon: 'questionnaire'
  },
  24: {
    name: '审批消息',
    type: getCustomTypeByType(2),
    typeCode: 2,
    icon: 'report'
  },
  25: {
    name: '设备巡检超时提醒',
    type: getCustomTypeByType(3),
    typeCode: 4,
    icon: 'inspection',
    elements: 'id'
  },
  26: {
    name: '设备保养超时提醒',
    type: getCustomTypeByType(3),
    typeCode: 5,
    icon: 'inspection',
    elements: 'id'
  },
  27: {
    name: '综合巡检超时提醒',
    type: getCustomTypeByType(3),
    typeCode: 6,
    icon: 'inspection',
    elements: 'id'
  },
  // type为3，演练暂时特殊处理
  29: {
    name: '演练确认通知',
    type: getCustomTypeByType(6),
    typeCode: 3,
    icon: 'questionnaire',
    elements: 'id'
  },
  // type为3，演练暂时特殊处理
  30: {
    name: '演练通知',
    type: getCustomTypeByType(5),
    typeCode: 3,
    icon: 'questionnaire',
    elements: 'id'
  },
  // type为2，当前版本不做消息处理，暂时走通知公告
  31: {
    name: '工程审批',
    type: getCustomTypeByType(0),
    typeCode: 3,
    elements: 'id'
  },
  // type为2，当前版本不做消息处理，暂时走通知公告
  32: {
    name: '会议超时提醒',
    type: getCustomTypeByType(0),
    typeCode: 9,
    elements: 'id'
  },
  // 寿命预警提醒
  34: {
    name: '寿命预警提醒',
    type: getCustomTypeByType(7),
    typeName: '寿命预警',
    typeCode: 34,
    component: '',
    elements: 'configType,id,msgSysType,assetsId',
    icon: 'questionnaire'
  },
  // 值班打卡提醒
  35: {
    name: '值班任务提醒',
    type: getCustomTypeByType(0),
    typeCode: 5,
    icon: 'announcement'
  },
  // 施工审批消息
  36: {
    name: '施工审批消息',
    type: getCustomTypeByType(2),
    typeCode: 4,
    icon: 'report',
    elements: 'projectCode,processInstanceId,status'
  }
}
export const levelIconList = [
  {
    name: '高优先级',
    type: 3,
    icon: 'red-flag-icon'
  },
  {
    name: '中优先级',
    type: 2,
    icon: 'orange-flag-icon'
  },
  {
    name: '低优先级',
    type: 1,
    icon: 'blue-flag-icon'
  },
  {
    name: '无优先级',
    type: 0,
    icon: 'green-flag-icon'
  }
]
export const taskStatusList = {
  0: { text: '进行中', color: '#FF9435' },
  1: { text: '未完成', color: '#FA403C' },
  2: { text: '已完成', color: '#08CB83' }
}
export const energyConsumptionList = [
  {
    name: '电',
    energyId: 'SU035',
    child: [
      {
        name: '照明用',
        energyId: 'SU061'
      },
      {
        name: '照明用',
        energyId: 'SU061'
      },
      {
        name: '照明用',
        energyId: 'SU061'
      }
    ]
  }
]
export const iomsUserInfon = {
  unitCode: 'BJSYYGLJ',
  hospitalCode: 'BJSJTY',
  userId: 'd6aeca8128fa400c8f60f9c69402d0b3',
  staffId: 'd6aeca8128fa400c8f60f9c69402d0b3',
  userName: '系统管理员',
  roleCode: 'BJSJTY_systemAdminCode_IMES_CORE',
  officeCode: '8afe0f36c9ea4e46a35f2c00bc2929cb',
  sysIdentity: 'systemAdminCode',
  departmentAssetAdminCode: '8afe0f36c9ea4e46a35f2c00bc2929cb',
  moduleIdentity: 'IMES_CORE',
  sysCome: 2,
  userType: 1,
  sysFlag: 'imes',
  permissionsType: 1,
  onOff: 1
}
export const childAppWhite = [
  // 基础信息
  'memberArchitecture',
  'hospitalManage',
  'postManage',
  'spaceList',
  'ichnography',
  'assetManage',
  // 餐饮
  'overview',
  'comprehensive',
  'foodsales',
  'complaint',
  'merchantConfiguration',
  'bedConfiguration',
  // 停车场
  'parkingOverview',
  'entryRecordManage',
  'exitRecordManage',
  'reverseSearch',
  'stallManage',
  // 工作流子工程
  'allProject',
  'addProject',
  'projectDetail',
  'myProject',
  'retentionMoneyManager',
  'maintenanceDispatch',
  // 施工管理
  'operationLedger',
  'myApplication',
  'myApproval',
  // 公租房
  'rentalHousingApproveManage'
]
const uploadAcceptList = {
  picture: '.jpg,.jpeg,.png,.gif,.bmp,.svg,.webp,.heic',
  video: '.mp4,.flv,.avi,.wmv,.mov,.webm,.mpeg4,.ts,.mpg,.rm,.rmvb,.mkv,.m4v',
  audio: '.wav,.mp3,.aac,.flac',
  annex: '.pdf,.xls,.xlsx,.doc,.docx,.txt,.ppt,.pptx',
  drawing: '.dwg'
}
export const uploadAcceptDict = {
  // 图片
  picture: {
    type: uploadAcceptList['picture'],
    fileSize: 20
  },
  // 视频
  video: {
    type: uploadAcceptList['video'],
    fileSize: 100
  },
  // 音频
  audio: {
    type: uploadAcceptList['audio'],
    fileSize: 20
  },
  // 富媒体（视频+图片+音频）
  richMedia: {
    type: uploadAcceptList['picture'] + ',' + uploadAcceptList['video'] + ',' + uploadAcceptList['audio'],
    fileSize: 100
  },
  // 课件（视频+文档附件）
  courseware: {
    type: uploadAcceptList['video'] + ',' + uploadAcceptList['annex'],
    fileSize: 100
  },
  // 文档附件
  annex: {
    type: uploadAcceptList['annex'],
    fileSize: 100
  },
  // 附件（广义）（所有附件种类的全集,rar,zip）
  annexGeneralized: {
    type:
      uploadAcceptList['picture'] +
      ',' +
      uploadAcceptList['video'] +
      ',' +
      uploadAcceptList['audio'] +
      ',' +
      uploadAcceptList['annex'] +
      ',' +
      uploadAcceptList['drawing'] +
      ',' +
      '.rar,.zip',
    fileSize: 100
  },
  // 图纸文件（特殊）
  drawing: {
    type: uploadAcceptList['drawing'],
    fileSize: 100
  }
}
