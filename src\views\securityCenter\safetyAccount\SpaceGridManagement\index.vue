<template>
  <PageContainer>
    <div slot="content" class="role-content" style="height: 100%">
      <div class="role-content-left">
        <div class="toptip">
          <span class="green_line"></span>
          建筑空间
        </div>
        <div class="left_content">
          <div class="tree">
            <el-tree ref="tree" :default-expand-all="true" style="margin-top: 10px" :check-strictly="true"
              :data="treeData" :props="defaultProps" node-key="id" :highlight-current="true"
              @node-click="handleNodeClick"></el-tree>
          </div>
        </div>
      </div>
      <div class="role-content-right">
        <div style="height: 100%">
          <div class="search-from">
            <span>空间功能类型: </span>
            <!-- <el-select v-model="value1" filterable @change="changeSpaceType">
              <el-option v-for="item in options1" :key="item.id" :label="item.dictName" :value="item.id"> </el-option>
            </el-select> -->
            <el-cascader v-model="value1" :options="options1" :props="props" clearable @change="changeSpaceType">
            </el-cascader>
            <span style="margin-left: 16px">部门: </span>
            <el-select v-model="value2" filterable @change="changeSpaceType">
              <el-option v-for="item in teamList" :key="item.id" :label="item.officeName" :value="item.id"> </el-option>
            </el-select>
          </div>
          <div class="contentTable">
            <div class="contentTable-main table-content">
              <el-table ref="multipleTable" v-loading="tableLoading" highlight-current-row :data="tableData" border
                stripe style="width: 100%" :height="tableHeight">
                <!-- <template slot="empty">
              <p class="tableEmptyLabel">{{ $store.state.tableLabel }}</p>
            </template> -->
                <el-table-column type="index" width="60" label="序号" align="center">
                  <template slot-scope="scope">
                    <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="id" label="空间ID" min-width="200" show-overflow-tooltip
                  align="center"></el-table-column>
                <el-table-column prop="localSpaceName" label="本地名称" show-overflow-tooltip width="100"
                  align="center"></el-table-column>
                <el-table-column prop="localSpaceCode" show-overflow-tooltip label="本地编码" width="100"
                  align="center"></el-table-column>
                <el-table-column prop="modelCode" show-overflow-tooltip label="模型编码" align="center"></el-table-column>
                <el-table-column prop="simName" show-overflow-tooltip label="位置" min-width="130"
                  align="center"></el-table-column>
                <el-table-column prop="functionDictName" show-overflow-tooltip label="功能类型" min-width="120"
                  align="center"></el-table-column>
                <el-table-column prop="dmName" show-overflow-tooltip label="归属科室" min-width="120"
                  align="center"></el-table-column>
                <el-table-column prop="principalName" show-overflow-tooltip label="空间负责人" min-width="120"
                  align="center"></el-table-column>
                <el-table-column prop="remark" show-overflow-tooltip label="备注" min-width="120"
                  align="center"></el-table-column>
              </el-table>
            </div>
            <div class="contentTable-footer">
              <el-pagination :current-page="paginationData.currentPage" :page-sizes="[15, 30, 50, 100]"
                :page-size="paginationData.pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="paginationData.total" @size-change="handleSizeChange"
                @current-change="handleCurrentChange"></el-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin.js'
import { transData } from '@/util'
import axios from 'axios'
export default {
  name: 'SpaceGridManagement',
  mixins: [tableListMixin],
  data() {
    return {
      loading: false,
      importFileDialog: false,
      confirmDialog: false,
      id: '',
      type: 'edit',
      treeData: [],
      tableCode: 1,
      defaultProps: {
        children: 'children',
        label: 'ssmName',
        value: 'id'
      },
      value1: '',
      options1: [],
      props: {
        label: 'name',
        value: 'id',
        leaf: 'leaf'
      },
      value2: '',
      defaultProps4: {
        children: 'children',
        label: 'text',
        value: 'id'
      },
      riskLevelList: [],
      guankongList: [
        {
          name: '启用',
          id: 0
        },
        {
          name: '禁用',
          id: 1
        }
      ],
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      tableData: [],
      treeLoading: false,
      tableLoading: false,
      filters: {
        eqName: '',
        eqType: ''
      },
      multipleSelection: [],
      checkedData: {},
      filterText: '',
      riskType: '',
      dialogVisibleExport: false,
      dataList: [],
      groupRiskArrData: [],
      teamList: [],
      imgArr: [],
      dialogVisibleImg: false,
      // eslint-disable-next-line vue/no-dupe-keys
      confirmDialog: false,
      img: '',
      ledgerId: '',
      allTreeData: [],
      prnetIds: []
    }
  },
  mounted() { },
  created() {
    this.getSpaceType()
    this.getDataValue()
    this.getTableData()
    this.getTreeData()
  },
  methods: {
    searchClick() {
      this.paginationData.currentPage = 1
      this.getTableData()
    },
    // 获取左侧树
    getTreeData() {
      this.treeLoading = true
      this.$api.ipsmGetSpaceGridTree({ spaceFlag: 1 }).then((res) => {
        this.treeLoading = false
        if (res.code == '200' && res.data.length > 0) {
          this.allTreeData = res.data
          let treeList = transData(res.data, 'id', 'pid', 'children')
          this.treeData = treeList
          this.$nextTick(() => {
            this.$refs.tree.setCurrentKey(this.treeData[0])
          })
          //     this.getTableData()
        }
      })
    },
    changeSpaceType(val) {
      this.getTableData()
    },
    // 树状图点击
    handleNodeClick(data) {
      this.paginationData.currentPage = 1
      this.$refs.tree.setCurrentKey(this.checkedData)
      let prentIds = []
      let aimId = data.pid
      for (let i = 0; i < data.ssmType; i++) {
        const prent = this.allTreeData.find((i) => i.id == aimId)
        if (prent) {
          prentIds.push(prent.id)
          aimId = prent.pid
        } else {
          prentIds.push(data.id)
        }
      }
      if (prentIds.length == 3) {
        const id = prentIds[0]
        prentIds[0] = prentIds[1]
        prentIds[1] = id
      } else if (prentIds.length == 4) {
        const id1 = prentIds[0]
        const id2 = prentIds[2]
        prentIds[0] = id2
        prentIds[2] = id1
      }
      prentIds.unshift('#')
      this.prnetIds = prentIds
      this.getTableData()
    },
    getSpaceType() {
      // axios({
      //   method: 'get',
      //   url: __PATH.VUE_SPACE_API + 'web/prDictionaryDetailsD/getSuperiorData',
      //   params: {
      //     dictionaryCategoryId: 'SPACE_FUNCTION'
      //   },
      //   headers: {
      //     'Content-Type': 'application/json',
      //     Authorization: 'Bearer ' + this.$store.state.user.token,
      //     unitCode: this.$store.state.user.userInfo.user.unitCode,
      //     hospitalCode: this.$store.state.user.userInfo.user.hospitalCode
      //   }
      // }).then(res => {
      //   if (res.data.code) {
      //     this.options1 = this.findChild(res.data.data[0].children)
      //   }
      // })
      this.$api.GetSuperiorData({ dictionaryCategoryId: 'SPACE_FUNCTION' }).then((res) => {
        if (res.code == 200) {
          this.options1 = this.findChild(res.data[0].children)
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    // 递归处理最末级数据
    findChild(treeData) {
      return findItem(treeData)
      function findItem(data) {
        data.forEach((i) => {
          if (!i.children.length) {
            delete i.children
            i.leaf = false
          } else {
            findItem(i.children)
          }
        })
        return treeData
      }
    },
    getTableData() {
      let data = {
        simCode: this.prnetIds.join(',') || '#',
        functionDictId: this.value1[this.value1.length - 1] || '',
        current: this.paginationData.currentPage,
        size: this.paginationData.pageSize,
        deptCode: this.value2 || ''
      }
      this.tableLoading = true
      this.$api.ipsmGetSpaceGridTable(data).then((res) => {
        this.tableLoading = this.$store.state.loadingShow
        if (res.code == 200) {
          this.tableData = res.data.records
          this.paginationData.total = res.data.total
        }
      })
    },
    getDataValue() {
      this.tableLoading = true
      this.$api.ipsmOutsourcedTreeData({ type: '' }).then((res) => {
        this.treeLoading1 = this.$store.state.loadingShow
        if (res.code == '200') {
          const item = {
            id: '',
            officeName: '全部'
          }
          res.data.unshift(item)
          this.teamList = res.data
        }
      })
    },
    handleSizeChange(val) {
      this.paginationData.currentPage = 1
      this.paginationData.pageSize = val
      this.getTableData()
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.getTableData()
    }
  }
}
</script>
<style lang="scss" scoped>
// el-tree-node is-expanded is-focusable
::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: linear-gradient(to right, #d9e1f8, #fff);
}
::v-deep .el-tree-node__content {
  height: 32px;
}
.role-content {
  height: 100%;
  display: flex;
  .role-content-left {
    width: 246px;
    height: 100%;
    padding: 10px;
    background: #fff;
    border-radius: 4px;
    margin-right: 12px;
    .left_content {
      height: calc(100% - 60px);
      padding: 10px;
      // overflow: auto;
      .tree {
        margin-top: 10px;
        height: calc(100% - 40px);
        overflow: auto;
      }
    }
  }
    padding: 20px;
    background: #fff;
    border-radius: 4px;
    .search-from {
      padding-bottom: 12px;
      & > div {
        margin-right: 10px;
      }
      & > button {
        margin-top: 12px;
      }
    }
    .contentTable {
      height: calc(100% - 50px);
      display: flex;
      flex-direction: column;
      .contentTable-main {
        height: calc(100% - 40px);
        flex: 1;
        overflow: auto;
      }
      .contentTable-footer {
        padding: 10px 0 0;
      }
    }
  }
</style>
