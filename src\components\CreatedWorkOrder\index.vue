<template>
  <el-dialog
    v-dialogDrag
    :modal="false"
    custom-class="model-dialog"
    width="50%"
    append-to-body
    :visible.sync="workOrderDealShow"
    :close-on-click-modal="false"
    :before-close="closeDialog"
  >
    <template slot="title">
      <span class="dialog-title">{{ (dealType === 'add' ? '新建' : '') + workTypeName + '工单' + (dealType === 'deal' ? '（ 处理 ）' : '') }}</span>
      <div class="right-tips"><span class="v-img"></span>{{ workOrderDetail.taskWorkHint }}</div>
    </template>
    <olgMaintenance
      v-if="workOrderDetail?.olgTaskManagement?.workTypeCode === '1' || workOrderDetail?.olgTaskManagement?.workTypeCode === '16'"
      ref="olgMaintenance"
      :projectCode="projectCode"
      :dealType="dealType"
      :alarmId="alarmId"
      :spaceId="spaceId"
      routerFrom="local"
      :workOrderDetail="workOrderDetail"
      @save="getSaveCallback"
    />
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="savePeople">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import olgMaintenance from './components/olgMaintenance.vue'
export default {
  name: 'CreatedWorkOrder',
  components: {
    olgMaintenance
  },
  props: {
    workOrderDealShow: {
      type: Boolean,
      default: false
    },
    dealType: {
      type: String,
      default: 'add'
    },
    alarmId: {
      type: String,
      default: ''
    },
    spaceId: {
      type: String,
      default: ''
    },
    projectCode: {
      type: String,
      default: ''
    },
    workTypeCode: {
      type: String,
      default: ''
    },
    workTypeName: {
      type: String,
      default: ''
    },
    workTypeId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      workOrderDetail: {}
    }
  },
  watch: {},
  created() {
    if (this.dealType === 'deal') {
      this.getWorkOrderOper()
    } else {
      this.getWorkOrderToAdd()
    }
  },
  mounted() {},
  methods: {
    getWorkOrderOper() {
      const params = {
        id: this.workTypeId,
        operType: 'placeOrder'
        // ...__PATH.USER_CODE
      }
      this.$api.getWorkOrderOper(params).then((res) => {
        this.workOrderDetail = res
      })
    },
    getWorkOrderToAdd() {
      const params = {
        type: 1,
        workTypeCode: this.workTypeCode,
        workTypeName: this.getWorkTypeLetter(this.workTypeCode)
      }
      this.$api.getWorkOrderToAdd(params).then((res) => {
        this.workOrderDetail = res
      })
    },
    getWorkTypeLetter(code) {
      switch (code) {
        case '1':
          return 'WX'
        case '16':
          return 'QJ'
      }
    },
    // 取消按钮
    closeDialog() {
      this.$emit('update:workOrderDealShow', !this.workOrderDealShow)
    },
    // 确认按钮
    savePeople() {
      this.$refs.olgMaintenance.saveForm()
    },
    getSaveCallback(item) {
      // 关联工单返回数据 保存关联报警
      if (item) {
        this.$api.limWorkInfo(item).then((res) => {
          console.log(res)
        })
      }
      this.$emit('workOrderSure', item)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .model-dialog {
  .el-dialog__header {
    display: flex;

    .dialog-title {
      display: inline-block;
      width: 38%;
    }

    .right-tips {
      width: 50%;
      background: linear-gradient(90deg, rgb(255 36 36 / 0%) 0%, rgb(255 43 43 / 40%) 50%, rgb(255 49 49 / 0%) 100%);
      height: 30px;
      line-height: 30px;
      text-align: center;
      color: #fff;
      display: flex;
      justify-content: center;

      .v-img {
        margin: auto 0;
        margin-right: 10px;
        display: inline-block;
        height: 20px;
        width: 20px;
        background: center url("~@/assets/images/workOrder/volume.png") no-repeat;
        background-size: 100% 100%;
      }
    }
  }

  .el-dialog__body {
    padding: 0 0 15px;
  }
}

.el-table-column--selection .cell {
  padding-left: 0.875rem;
  padding-right: 0.875rem;
  text-overflow: initial;
}
</style>
