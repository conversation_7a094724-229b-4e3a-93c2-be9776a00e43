<script>
import { UsingStatusOptions } from '@/views/operationPort/constant'
import tableListMixin from '@/mixins/tableListMixin'
import ColumnBase from './components/ColumnBase.vue'
import ColumnDetail from './components/ColumnDetail.vue'
export default {
  name: 'TableColumnConfig',
  components: {
    ColumnDetail,
    ColumnBase
  },
  mixins: [tableListMixin],
  data: () => ({
    searchForm: {
      name: ''
    },
    tableData: [],
    loadingStatus: false,
    dialog: {
      // 基础表单
      baseVisible: false,
      detailVisible: false,
      detailReadonly: false,
      id: 0
    }
  }),
  computed: {
    OperateType() {
      return {
        View: 'view',
        Edit: 'edit',
        Delete: 'delete',
        Create: 'create',
        Config: 'config'
      }
    }
  },
  mounted() {
    this.getDataList()
  },
  methods: {
    visibleFormatter(value) {
      return value == '1' ? '显示' : '隐藏'
    },
    presetFormatter(value) {
      return value == '1' ? '是' : '否'
    },
    getDataList() {
      this.loadingStatus = true
      const params = {
        name: this.searchForm.name,
        pageSize: this.pagination.size,
        pageNo: this.pagination.current
      }
      this.$api.SporadicProject.queryProjectColumnByPage(params)
        .then((res) => {
          if (res.code === '200') {
            this.tableData = res.data.records
            this.pagination.total = res.data.total
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '查询失败'))
        .finally(() => (this.loadingStatus = false))
    },
    onSearch() {
      this.pagination.current = 1
      this.getDataList()
    },
    onReset() {
      this.$refs.formRef.resetFields()
      this.onSearch()
    },
    onOperate(row, type) {
      switch (type) {
        case this.OperateType.Delete:
          if (+row.presets === 1) {
            this.$message.error('预设的列不允许删除！')
          } else if (+row.state === 1) {
            this.$message.error('显示的列不允许删除！')
          } else {
            this.$confirm('删除后将无法恢复，是否确定删除？', '信息提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消'
            }).then(() => this.doDelete(row.id))
          }
          break
        case this.OperateType.Create:
          this.dialog.baseVisible = true
          break
        default:
          this.dialog.detailVisible = true
          this.dialog.id = row.id
          this.dialog.detailReadonly = type === this.OperateType.View
          break
      }
    },
    doDelete(id) {
      this.$api.SporadicProject.deleteProjectColumnById({ id })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('删除成功')
            this.getDataList()
          } else {
            throw res.message
          }
        })
        .catch((msg) => msg && this.$message.error(msg))
        .finally(() => (this.loadingStatus = false))
    }
  }
}
</script>
<template>
  <div class="table-column-config">
    <div class="table-column-config__top">
      <el-form ref="formRef" :model="searchForm" class="table-column-config__search" inline @submit.native.prevent="onSearch">
        <el-form-item prop="name">
          <el-input v-model="searchForm.name" clearable filterable placeholder="列名称"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" plain @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
      <div class="table-column-config__actions">
        <el-button type="primary" icon="el-icon-plus" @click="onOperate(undefined, OperateType.Create)">新增列 </el-button>
      </div>
    </div>
    <div class="table-column-config__table">
      <el-table v-loading="loadingStatus" height="100%" :data="tableData" border stripe table-layout="auto" class="tableAuto" row-key="id">
        <el-table-column label="序号" type="index" width="55" />
        <el-table-column label="列表列名称" prop="name" show-overflow-tooltip></el-table-column>
        <el-table-column label="编码" prop="code" show-overflow-tooltip></el-table-column>
        <el-table-column label="排序" prop="shortNo" width="100px"></el-table-column>
        <el-table-column label="显隐状态" prop="state" width="100px" :formatter="(row) => visibleFormatter(row.state)"></el-table-column>
        <el-table-column label="预设" prop="preset" width="100px" :formatter="(row) => presetFormatter(row.presets)"></el-table-column>
        <el-table-column label="操作" width="150px">
          <template #default="{ row }">
            <el-button type="text" @click="onOperate(row, OperateType.View)">查看</el-button>
            <el-button type="text" @click="onOperate(row, OperateType.Edit)">编辑</el-button>
            <el-button v-if="row.presets !== '1'" type="text" class="text-red" @click="onOperate(row, OperateType.Delete)"> 删除 </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-pagination
      class="table-column-config__pagination"
      :current-page="pagination.page"
      :page-sizes="pagination.pageSizeOptions"
      :page-size="pagination.size"
      :layout="pagination.layoutOptions"
      :total="pagination.total"
      @size-change="paginationSizeChange"
      @current-change="paginationCurrentChange"
    >
    </el-pagination>
    <!--基础信息-->
    <ColumnBase :visible.sync="dialog.baseVisible" @success="getDataList" @config="(id) => onOperate({ id }, OperateType.Config)"></ColumnBase>
    <!--表单详情 查看或者编辑-->
    <ColumnDetail :id="dialog.id" :visible.sync="dialog.detailVisible" :readonly="dialog.detailReadonly" @success="getDataList"></ColumnDetail>
  </div>
</template>
<style scoped lang="scss">
.table-column-config {
  display: flex;
  flex-flow: column nowrap;
  padding: 16px;
  &__top {
    display: flex;
    justify-content: space-between;
  }
  &__actions {
    line-height: 40px;
  }
  &__pagination {
    margin-top: 10px;
  }
  &__table {
    margin-top: -10px;
    flex: 1;
  }
  &__tag {
    // 停用
    &--0 {
      --color: #f64646;
    }
    // 启用
    &--1 {
      --color: #00b42a;
    }
    &:before {
      content: '';
      display: inline-block;
      vertical-align: middle;
      margin-right: 4px;
      height: 4px;
      width: 4px;
      border-radius: 4px;
      background-color: var(--color);
    }
  }
}
</style>
