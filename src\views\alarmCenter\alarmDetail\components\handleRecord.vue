<template>
  <div class="tabs_content">
    <div class="tabs_content_item">
      <div class="title">关联人员信息</div>
      <el-row type="flex">
        <el-col :span="12" class="detail_item">
          <div class="label">区域责任部门：</div>
          <el-tooltip class="item" effect="dark" :content="data.liabilityDeptName" :disabled="!data.liabilityDeptName" placement="top">
            <div class="value">{{ data.liabilityDeptName || '-' }}</div>
          </el-tooltip>
        </el-col>
        <el-col :span="12" class="detail_item">
          <div class="label">区域负责人：</div>
          <el-tooltip class="item" effect="dark" :content="data.liabilityPersonName" :disabled="!data.liabilityPersonName" placement="top">
            <div class="value">{{ data.liabilityPersonName || '-' }}</div>
          </el-tooltip>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12" class="detail_item">
          <div class="label">受理警情队伍：</div>
          <el-tooltip class="item" effect="dark" :content="data.acceptAlarmTeamName" :disabled="!data.acceptAlarmTeamName" placement="top">
            <div class="value">{{ data.acceptAlarmTeamName || '-' }}</div>
          </el-tooltip>
        </el-col>
        <el-col :span="12" class="detail_item">
          <div class="label">值班人员：</div>
          <el-tooltip class="item" effect="dark" :content="data.operatorOnDuty" :disabled="!data.operatorOnDuty" placement="top">
            <div class="value">{{ data.operatorOnDuty || '-' }}</div>
          </el-tooltip>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12" class="detail_item">
          <div class="label">应急队员：</div>
          <el-tooltip class="item" effect="dark" :content="data.emergencyTeamMembers" :disabled="!data.emergencyTeamMembers" placement="top">
            <div class="value">{{ data.emergencyTeamMembers || '-' }}</div>
          </el-tooltip>
        </el-col>
      </el-row>
      <el-row>
        <el-table :data="taskList" border>
          <el-table-column prop="taskType" label="任务类型"></el-table-column>
          <el-table-column prop="taskName" label="任务名称" show-overflow-tooltip></el-table-column>
          <el-table-column prop="executionPersonName" label="执行人员" show-overflow-tooltip></el-table-column>
          <el-table-column prop="executionTime" label="执行时间" show-overflow-tooltip></el-table-column>
          <el-table-column prop="taskStatus" label="任务状态">
            <template slot-scope="scope">
              <span>{{ scope.row.taskStatus == 1 ? '未执行' : '已执行' }}</span>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          style="margin-top: 10px"
          :current-page="page.pageNo"
          :page-sizes="[10, 20, 30, 40, 50]"
          :page-size="page.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="page.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </el-row>
    </div>
    <div class="tabs_content_item">
      <div class="title">现场文件</div>
      <el-row>
        <el-table :data="data.fileVoList" border>
          <el-table-column prop="name" label="文件" show-overflow-tooltip></el-table-column>
          <el-table-column prop="createdName" label="上传人"></el-table-column>
          <el-table-column prop="createdTime" label="上传时间"></el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button type="text" @click="downloadFile(scope.row.url)">下载</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
    </div>
    <div class="tabs_content_item">
      <div class="title">通话记录</div>
      <el-row>
        <el-table :data="data.clientOperationLogVoList" border>
          <el-table-column prop="communicateTypeName" label="呼叫类型"></el-table-column>
          <el-table-column prop="operationStartTime" label="通话开始时间" show-overflow-tooltip></el-table-column>
          <el-table-column prop="callDuration" label="通话时长"></el-table-column>
          <el-table-column prop="callPersonNum" label="通话人数"></el-table-column>
          <el-table-column prop="" label="操作">
            <template slot-scope="scope">
              <el-button type="text" @click="showDetail(scope.row)">详情</el-button>
              <el-button type="text" @click="downloadFile(scope.row.operationUrl)">下载</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
    </div>
    <el-dialog
      v-if="callLogVisible"
      v-dialogDrag
      :modal="false"
      :close-on-click-modal="false"
      title="通话记录"
      width="40%"
      :visible="callLogVisible"
      custom-class="model-dialog"
      :before-close="closeDialog"
    >
      <div class="callLog_content" style="padding: 10px">
        <el-row type="flex">
          <el-col :span="12" class="detail_item">
            <div class="label">呼叫类型：</div>
            <div class="value">{{ callLog.communicateTypeName }}</div>
          </el-col>
          <el-col :span="12" class="detail_item">
            <div class="label">通话时长：</div>
            <div>{{ callLog.callDuration }}</div>
          </el-col>
        </el-row>
        <el-row type="flex">
          <el-col :span="12" class="detail_item">
            <div class="label">通话开始时间：</div>
            <div class="value">{{ callLog.operationStartTime }}</div>
          </el-col>
          <el-col :span="12" class="detail_item">
            <div class="label">通话结束时间：</div>
            <div class="value">{{ callLog.operationEndTime }}</div>
          </el-col>
        </el-row>
        <el-row type="flex">
          <el-col :span="24" class="detail_item">
            <div class="label">被呼叫人：</div>
            <div class="value">
              <span v-for="(item, index) in callLog.callInfoList" :key="index">
                <span>{{ item.callPhone }}</span>
                <span :style="`color: ${item.callStatus == '接通' ? '#67c23a' : '#FA403C'}`">({{ item.callStatus }})</span>
                <span v-if="index !== callLog.callInfoList.length - 1">,</span>
              </span>
            </div>
          </el-col>
        </el-row>
        <el-row type="flex">
          <el-col :span="24" class="detail_item">
            <div class="label">通话人数：</div>
            <div class="value">{{ callLog.callPersonNum }}</div>
          </el-col>
        </el-row>
        <el-row type="flex">
          <el-col :span="24" class="detail_item">
            <div class="label">通话录音：</div>
            <div class="value">
              <audio id="" :src="callLog.operationUrl" controls />
            </div>
          </el-col>
        </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" plain @click="closeDialog">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
export default {
  props: {
    data: {
      type: Object,
      default: () => {}
    },
    detail: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      page: {
        pageNo: 1,
        pageSize: 10,
        total: 0
      },
      taskList: [],
      callLogVisible: false,
      callLog: {}
    }
  },
  watch: {
    detail: {
      handler(val) {
        if (Object.keys(val).length > 0 && val.alarmDeviceId && val.alarmStartTime) {
          this.getTaskList()
        }
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {},
  methods: {
    getTaskList() {
      let params = {
        alarmDeviceId: this.detail.alarmDeviceId,
        alarmStartTime: this.detail.alarmStartTime,
        pageNo: this.page.pageNo,
        pageSize: this.page.pageSize
      }
      this.$api.taskList(params).then((res) => {
        if (res.code == 200) {
          this.taskList = res.data.records
          this.page.total = res.data.total
        }
      })
    },
    handleSizeChange(val) {
      this.page.pageNo = 1
      this.page.pageSize = val
      this.getTaskList()
    },
    handleCurrentChange(val) {
      this.page.pageNo = val
      this.getTaskList()
    },
    downloadFile(url) {
      window.open(url)
    },
    showDetail(row) {
      this.callLog = row
      this.callLogVisible = true
    },
    closeDialog() {
      this.callLogVisible = false
    }
  }
}
</script>
<style lang="scss" scoped>
.tabs_content {
  width: 100%;
  height: 100%;
  overflow: auto;
  .tabs_content_item {
    width: 100%;
  }
}
.el-row {
  margin-top: 12px;
  margin-bottom: 12px;
  padding: 0 24px;
  &:first-child {
    margin-top: 24px;
  }
  &:last-child {
    margin-bottom: 24px;
  }
  .el-col {
    display: flex;
  }
}
.title {
  display: flex;
  align-items: center;
  font-size: 16px;
  color: #333;
  margin: 16px 0;
  &::before {
    content: '';
    background: #3562db;
    width: 6px;
    height: 6px;
    margin-right: 6px;
  }
}
.callLog_content {
  width: 100%;
  background: #fff;
}
.detail_item {
  display: flex;
  align-items: center;
}
.label {
  display: inline-block;
  width: 100px;
  color: #96989a;
  text-align: right;
}
.value {
  max-width: calc(100% - 100px);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
