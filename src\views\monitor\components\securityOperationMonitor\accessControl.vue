<template>
  <div class="securityOperationMonitor-content">
    <div class="content-main">
      <div class="search-from">
        <el-input v-model="searchFrom.surveyName" placeholder="请输入设备名称" clearable style="width: 200px" />
        <el-select v-model="searchFrom.doorStatus" placeholder="请选择状态" clearable style="width: 200px">
          <el-option v-for="(item, index) in monitorStatList" :key="index" :label="item.value" :value="item.id"></el-option>
        </el-select>
        <el-select v-model="searchFrom.entityMenuCode" :placeholder="'请选择门禁分组'" clearable style="width: 200px">
          <el-option v-for="item in entityMenuList" :key="item.code" :label="item.name" :value="item.code"></el-option>
        </el-select>
        <div style="display: inline-block">
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="searchForm">查询</el-button>
        </div>
      </div>
      <!-- {{ list }} -->
      <div v-loading="listLoading" class="main-content">
        <div class="grouping">
          <el-row :gutter="16">
            <el-col v-for="item in list" :key="item.menuCode" :xs="12" :sm="12" :md="8" :xl="6">
              <div class="entity_box">
                <div class="entity_header">
                  <div class="entity_type_title">
                    <div class="entity_type entity_type_no">
                      <img v-if="item.status == '6'" src="@/assets/images/monitor/offLine.png" alt="" />
                      <img v-else src="@/assets/images/monitor/onLine.png" alt="" />
                      <p>{{ item.status == 0 ? '在线' : item.status == 6 ? '离线' : '-' }}</p>
                    </div>
                    <span class="navText" @click.stop="jumpMonitorDetail(item)">
                      <el-tooltip :content="item.surveyEntityName" placement="top">
                        <span class="navText">{{ item.surveyEntityName }}</span>
                      </el-tooltip>
                    </span>
                  </div>
                  <div v-if="item.unDisposePolice > 0" class="dangerImg"><img src="../../../../assets/images/danger2.png" alt="" /></div>
                  <span class="buTton" @click.stop="openPaeameter(item)">更多</span>
                </div>
                <div v-scrollMove class="entity_parameter">
                  <div v-for="(el, index) in item.parameterList.slice(0, item.parameterList.length > 4 ? 4 : item.parameterList.length)" :key="index" class="entity_parameter_item">
                    <div class="entity_parameter_item_lable">
                      <p>{{ el.parameterName }}</p>
                      <!-- 映射 1=开，0=关，-1=离线 -->
                      <span class="entity_lable">{{ mapParameterValue(el.parameterValue) }}</span>
                    </div>
                  </div>
                </div>
                <div class="alarm">
                  <div class="alarm_item" @click="goToAlarm({ objectName: item.surveyEntityName, dataRange: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')] })">
                    <p>今日报警：</p>
                    <span>{{ item.allPolice }}</span>
                  </div>
                  <div class="line"></div>
                  <div class="alarm_item" @click="goToAlarm({ objectName: item.surveyEntityName, alarmStatus: '0' })">
                    <p>未处理报警：</p>
                    <span>{{ item.unDisposePolice }}</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
        <parameterConfiguration ref="parameterConfiguration" :title="title" :monitorData="monitorData" />
      </div>
      <div class="main-footer">
        <el-pagination
          :current-page="pagination.current"
          :page-sizes="pagination.pageSizeOptions"
          :page-size="pagination.size"
          :layout="pagination.layoutOptions"
          :total="pagination.total"
          @size-change="paginationSizeChange"
          @current-change="paginationCurrentChange"
        />
      </div>
    </div>
  </div>
</template>
<script>
import moment from 'moment'
import tableListMixin from '@/mixins/tableListMixin.js'
import parameterConfiguration from './parameterConfiguration.vue'
export default {
  components: { parameterConfiguration },
  mixins: [tableListMixin],
  props: {
    projectCode: {
      type: String,
      default: ''
    },
    currentStatus: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      moment,
      listLoading: false,
      title: '',
      searchFrom: {
        surveyName: '',
        entityMenuCode: '',
        doorStatus: '' // 状态
      },
      list: [],
      monitorData: {},
      entityMenuList: [], // 分组列表
      monitorStatList: [
        {
          value: '在线',
          id: '0'
        },
        {
          value: '离线',
          id: '1'
        },
        {
          value: '报警',
          id: '2'
        }
      ]
      // monitorList: [],
    }
  },
  watch: {
    currentStatus(newVal) {
      if (newVal) {
        this.searchFrom.doorStatus = newVal
        this.searchForm()
      }
    }
  },
  mounted() {
    this.pagination.size = 15
    this.getDataList()
    this.getEntityMenuList()
  },
  methods: {
    jumpMonitorDetail(v) {
      const item = {
        ...v,
        surveyEntityCode: v.surveyEntityCode,
        surveyEntityName: v.surveyEntityName
      }
      this.$emit('jumpDetail', item)
    },
    openPaeameter(el) {
      this.monitorData = el
      this.title = el.surveyEntityName
      this.$refs.parameterConfiguration.openDialog()
    },
    // 获取分组列表
    getEntityMenuList() {
      this.$api.GetEntityMenuList({ projectId: this.projectCode }).then((res) => {
        if (res.code == 200) {
          this.entityMenuList = res.data
        }
      })
    },
    // 跳转报警
    goToAlarm(params) {
      this.$router.push({
        name: 'allAlarmIndex',
        params: {
          projectCode: this.projectCode,
          alarmStatus: '3',
          ...params
        }
      })
    },
    getDataList(item) {
      let data = {
        projectCode: this.projectCode,
        page: this.pagination.current,
        pageSize: this.pagination.size,
        ...this.searchFrom
      }
      if (item) {
        if (item == '0') {
          data.doorStatus = '0'
        } else if (item == '6') {
          data.doorStatus = '1'
        } else {
          data.doorStatus = this.searchFrom.doorStatus
        }
      }
      this.listLoading = true
      this.$api.GetSecurityOtherSysList(data).then((res) => {
        this.pagination.total = res.data.totalCount ? res.data.totalCount : 0
        this.list = res.data.list ? this.setData(res.data.list) : []
        this.listLoading = false
      })
    },
    setData(list) {
      list.forEach((item) => {
        item.parameterObj = {}
        item.parameterList.forEach((v) => {
          if (!item.parameterObj[v.harvesterName]) {
            item.parameterObj[v.harvesterName] = []
          }
          item.parameterObj[v.harvesterName].push(v)
        })
      })
      return list
    },
    // 重置查询表单
    resetForm() {
      this.searchFrom = {
        surveyName: '',
        entityMenuCode: '',
        doorStatus: '' // 状态
      }
      this.pagination.size = 15
      this.pagination.current = 1
      this.searchForm()
    },
    // 查询
    searchForm() {
      this.pagination.current = 1
      this.getDataList()
    },
    mapParameterValue(value) {
      // value转一下字符串 (兼容)
      value = value.toString()
      switch (value) {
        case '1':
          return '开'
        case '0':
          return '关'
        case '-1':
          return '离线'
        default:
          return value // 如果没有匹配，返回原值
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.securityOperationMonitor-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  overflow-x: hidden;
  .statistics-item:first-child {
    margin-left: 0;
  }
  .content-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin-top: 16px;
    background: #fff;
    overflow: hidden;
    .search-from {
      padding: 0 0 16px 16px;
      & > div {
        margin-top: 0px;
        margin-right: 16px;
      }
    }
    .main-content {
      flex: 1;
      padding: 0 16px;
      overflow: auto;
      .grouping {
        display: flex;
        flex-direction: column;
        // background: #faf9fc;
        // padding: 19px 16px;
        margin-bottom: 24px;
        .entity_box_alarm {
          background: linear-gradient(180deg, #ffffff 55%, #ffe2e2 100%) !important;
          border-color: #f53f3f !important;
          .alarm {
            background: transparent !important;
          }
        }
        .entity_box {
          padding: 16px;
          background: #faf9fc;
          margin-bottom: 16px;
          border-radius: 4px;
          // border: 1px solid #fff;
          .entity_header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            .entity_type {
              display: flex;
              align-items: center;
              padding: 8px;
              background: #e8ffea;
              p {
                margin: 0;
                font-size: 14px;
                line-height: 14px;
                color: #009a29;
              }
              img {
                margin-right: 7px;
              }
            }
            .entity_type_no {
              background: #f2f4f9;
              p {
                color: #86909c;
              }
            }
          }
          .entity_parameter {
            width: 100%;
            display: flex;
            height: 140px;
            margin: 15px 0px 0px 0px;
            flex-wrap: wrap;
            padding: 0px 25px;
            text-align: center;
            .entity_parameter_item {
              width: 50%;
              margin-bottom: 15px;
            }
            .entity_lable {
              font-size: 13px;
              font-weight: bolder;
            }
          }
          .alarm {
            display: flex;
            padding: 8px 0;
            justify-content: space-around;
            background: #faf9fc;
            border-top: 1px solid #dcdfe6;
            .line {
              height: 20px;
              width: 1px;
              background: #dcdfe6;
            }
            .alarm_item {
              display: flex;
              align-items: center;
              cursor: pointer;
              p {
                margin: 0;
                color: #666666;
              }
              span {
                font-size: 18px;
                line-height: 18px;
                font-family: Arial-Regular, Arial;
                font-weight: 400;
                color: #333333;
              }
            }
          }
        }
        &_header {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;
          &_left {
            display: flex;
            align-items: center;
            img {
              width: 18px;
              height: 18px;
            }
            p {
              font-size: 15px;
              font-family: PingFang SC-Medium, PingFang SC;
              font-weight: 500;
              color: #333333;
              margin: 0 10px;
            }
            &_badge {
              background: #f53f3f;
              border-radius: 100px;
              display: flex;
              align-items: center;
              padding: 3px 12px;
              img {
                width: 12px;
                height: 12px;
              }
              span {
                font-size: 12px;
                color: #ffffff;
                margin-left: 5px;
              }
            }
          }
          &_right {
            display: flex;
            .grouping_information:last-child {
              margin: 0;
              div {
                padding: 0;
                border: 0;
              }
            }
            .grouping_information {
              margin-right: 11px;
              padding: 5px 0;
              div {
                padding-right: 11px;
                display: flex;
                align-items: center;
                border-right: 1px solid #dcdfe6;
              }
              p {
                margin: 0;
                font-size: 14px;
                font-family: PingFang SC-Regular, PingFang SC;
                font-weight: 400;
                color: #666666;
                line-height: 14px;
              }
              span {
                font-size: 18px;
                font-family: Arial-Regular, Arial;
                font-weight: 400;
                color: #333333;
                line-height: 18px;
              }
            }
          }
        }
      }
    }
    .entity_type_title {
      display: flex;
      align-items: center;
    }
    .main-footer {
      padding: 10px 16px;
    }
    .navText {
      width: 200px;
      height: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .buTton {
      color: #3562db;
    }
    .dangerImg img {
      width: 20px;
      height: 20px;
    }
    // .entity_parameter_item_lable {
    //   width: 100%;
    //   display: flex;
    //   justify-content: space-between;
    // }
  }
}
</style>
