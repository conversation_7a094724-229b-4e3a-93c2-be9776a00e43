<template>
  <div class="sino_page">
    <el-container>
      <el-main>
        <div class="search-box">
          <el-select v-model="searchForm.troopType" placeholder="请选择队伍类型">
            <el-option v-for="item in typeList" :key="item.dict_code" :label="item.description" :value="item.dict_code">
            </el-option>
          </el-select>
          <el-input v-model="searchForm.troopCaptainName" clearable filterable placeholder="请输入队伍负责人"
            class="ml-16"></el-input>
          <div class="ml-16">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button type="primary" plain @click="reset">重置</el-button>
          </div>
        </div>
        <div class="sino_table">
          <el-table ref="sinoTable" v-loading="tableLoading" :data="tableData" :row-key="getRowKeys" height="300px"
            stripe border @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="50" align="center" :reserve-selection="true"></el-table-column>
            <el-table-column v-for="(column, index) in tableColumn" :key="index" :prop="column.prop"
              :label="column.label" :min-width="column.minWidth" show-overflow-tooltip>
              <template slot-scope="scope">
                <span>{{ scope.row[column.prop] }}</span>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination :current-page="pagination.current" :page-sizes="[15, 30, 50, 100]" :page-size="pagination.size"
            :total="pageTotal" layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
            @current-change="handleCurrentChange"></el-pagination>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script>
export default {
  name: 'SelectGroup',
  components: {},
  props: {
    groupList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      searchForm: {
        troopCaptainName: '',
        troopType: ''
      },
      typeList: [],
      tableLoading: false,
      tableHeight: '',
      tableData: [],
      multipleSelection: [],
      // -----------------------------Pagination
      pagination: {
        pageSize: 15,
        page: 1
      }, // 分页数据
      pageTotal: 0, // 数据总条数
      selectList: [],
      tableColumn: [
        {
          prop: 'troopName',
          label: '队伍名称'
        },
        {
          prop: 'troopTypeName',
          label: '队伍类型'
        },
        {
          prop: 'troopCaptainName',
          label: '队伍队长'
        },
        {
          prop: 'troopPersonnelName',
          label: '队伍人员'
        },
        {
          prop: 'troopResponsibility',
          label: '队伍职责'
        },
        {
          prop: 'troopDescribe',
          label: '队伍描述'
        }
      ]
    }
  },
  watch: {
    groupList: {
      handler(newValue, oldValue) {
        this.getTableData()
      },
      immediate: false,
      deep: true
    }
  },
  mounted() {
    this.getTypeList()
    this.getTableData()
  },
  methods: {
    getTypeList() {
      let data = {
        dictType: 'troopTypeCode'
      }
      this.$api.getEmergencyTroopTypeList(data).then((res) => {
        if (res.code === '200') {
          this.typeList = res.data
        }
      })
    },
    getRowKeys(row) {
      return row.id
    },
    search() {
      this.pagination.page = 1
      this.getTableData()
    },
    reset() {
      this.searchForm = {
        troopCaptainName: '',
        troopType: ''
      }
      this.pagination.page = 1
      this.pageTotal = 0
      this.getTableData()
    },
    getTableData() {
      this.tableLoading = true
      let data = {
        ...this.pagination,
        ...this.searchForm
      }
      this.$api
        .getEmergencyTroopManageList(data)
        .then((res) => {
          this.tableLoading = false
          if (res.code == 200) {
            this.tableData = res.data ? res.data.records : []
            this.pageTotal = res.data ? res.data.total : 0
          } else if (res.message) {
            this.tableData = []
            this.pagination.total = 0
            this.$message.error(res.message)
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
      if (this.groupList && this.groupList.length) {
        this.$nextTick(() => {
          this.groupList.forEach(i => {
            this.$refs.sinoTable.toggleRowSelection(i)
          })
        })
      } else {
        this.$refs.sinoTable.clearSelection()
      }
    },
    // ---------------------------------------------------------- TabelFn
    handleSizeChange(val) {
      this.pagination.page = 1
      this.pagination.pageSize = val
      this.getTableData()
    },
    handleCurrentChange(val) {
      this.pagination.page = val
      this.getTableData()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
      this.selectList = JSON.parse(JSON.stringify(this.multipleSelection))
    }
  }
}
</script>
<style lang="scss" scoped>
.el-main {
  padding: 6px 24px 8px;
  overflow: hidden;

  .sino_panel {
    height: 100%;
    border-radius: 10px;
    background: #fff;
    position: relative;
    font-size: 14px;
  }

  .sino_page {
    height: 100%;
    position: relative;

    .el-main {
      // height: 100%;
      background: #fff;
      border-radius: 10px;
      padding: 0;
    }
  }
}
.search-box {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  .el-input,
  .el-select {
    width: 200px;
  }
}
.ml-16 {
  margin-left: 16px;
}
.el-pagination {
  margin-top: 10px;
}
</style>
