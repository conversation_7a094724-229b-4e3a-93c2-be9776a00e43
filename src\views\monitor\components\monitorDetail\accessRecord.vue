<!--
 * @Description:
-->
<template>
  <div class="warn-history">
    <div class="wholeTable-main table-content">
      <el-table ref="table" v-loading="loading" :resizable="false" border :data="tableData" height="100%"
        style="width: 100%">
        <el-table-column prop="alarmStartTime" label="流水号"></el-table-column>
        <el-table-column prop="incidentName" label="场所名称" show-overflow-tooltip></el-table-column>
        <el-table-column prop="alarmLevel" label="对应门号">
          <span slot-scope="scope" class="alarmLevel"
            :style="{ background: alarmLevelItem[scope.row.alarmLevel].color }">
            {{ alarmLevelItem[scope.row.alarmLevel].text }}
          </span>
        </el-table-column>
        <el-table-column prop="alarmStatus" label="设备名称">
          <div slot-scope="scope" class="alarmStatus"
            :style="{ color: scope.row.alarmStatus == 0 ? '#FA403C' : scope.row.alarmStatus == 1 ? '#fd9434' : '#999' }">
            <span class="alarmStatusIcon"
              :style="{ background: scope.row.alarmStatus == 0 ? '#FA403C' : scope.row.alarmStatus == 1 ? '#ff9435' : '#999' }"></span>
            {{ scope.row.alarmStatus == 0 ? '未处理' : scope.row.alarmStatus == 1 ? '处理中' : '已关闭' }}
          </div>
        </el-table-column>
        <el-table-column prop="alarmAffirm" label="工号">
          <template slot-scope="scope">{{ alarmAffirmItem[scope.row.alarmAffirm] || '-' }}</template>
        </el-table-column>
        <el-table-column prop="alarmAffirm" label="用户姓名">
          <template slot-scope="scope">{{ alarmAffirmItem[scope.row.alarmAffirm] || '-' }}</template>
        </el-table-column>
        <el-table-column prop="alarmAffirm" label="部门名称">
          <template slot-scope="scope">{{ alarmAffirmItem[scope.row.alarmAffirm] || '-' }}</template>
        </el-table-column>
        <el-table-column prop="alarmAffirm" label="卡号">
          <template slot-scope="scope">{{ alarmAffirmItem[scope.row.alarmAffirm] || '-' }}</template>
        </el-table-column>
        <el-table-column prop="alarmAffirm" label="门方向">
          <template slot-scope="scope">{{ alarmAffirmItem[scope.row.alarmAffirm] || '-' }}</template>
        </el-table-column>
        <el-table-column prop="alarmAffirm" label="记录状态">
          <template slot-scope="scope">{{ alarmAffirmItem[scope.row.alarmAffirm] || '-' }}</template>
        </el-table-column>
        <el-table-column prop="alarmAffirm" label="记录时间">
          <template slot-scope="scope">{{ alarmAffirmItem[scope.row.alarmAffirm] || '-' }}</template>
        </el-table-column>
        <!-- <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button type="text" @click="getWarnDetail(scope.row)">查看</el-button>
          </template>
        </el-table-column> -->
      </el-table>
    </div>
    <div class="wholeTable-footer">
      <el-pagination :current-page="pagination.current" :page-sizes="pagination.pageSizeOptions"
        :page-size="pagination.size" :layout="pagination.layoutOptions" :total="pagination.total"
        @size-change="paginationSizeChange" @current-change="paginationCurrentChange">
      </el-pagination>
    </div>
    <template v-if="alarmDetailShow">
      <AlarmDetailDialog ref="alarmDetail" :alarmId="alarmDetail.alarmId" :visible.sync="alarmDetailShow"
        @closeAlarmDialog="closeAlarmDialog" />
    </template>
  </div>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin.js'
export default {
  name: 'accessRecord',
  mixins: [tableListMixin],
  props: {
    searchForm: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      loading: false,
      alarmLevelItem: {
        0: { text: '通知', color: '#3562DB' },
        1: { text: '一般', color: '#3562DB' },
        2: { text: '紧急', color: '#FF9435' },
        3: { text: '重要', color: '#FA403C' }
      },
      alarmAffirmItem: { 0: '未确认', 1: '真实报警', 2: '误报', 3: '演练', 4: '调试' },
      tableData: [],
      alarmDetailShow: false,
      alarmDetail: {}
    }
  },
  mounted() {
    // this.getDetails()
  },
  methods: {
    // 初始化组件数据
    // getDetails() {
    //   let { projectCode, alarmStatus, alarmLevel, surveyCode, dataRange } = this.searchForm
    //   let params = {
    //     pageNo: this.pagination.current,
    //     pageSize: this.pagination.size,
    //     projectCode: projectCode,
    //     alarmLevel,
    //     alarmStatus,
    //     objectId: surveyCode,
    //     startTime: dataRange[0],
    //     endTime: dataRange[1]
    //   }
    //   this.loading = true
    //   this.$api
    //     .GetAllAlarmRecord(params)
    //     .then((res) => {
    //       this.loading = false
    //       if (res.code == 200) {
    //         this.tableData = res.data ? res.data.records : []
    //         this.pagination.total = res.data ? res.data.total : 0
    //       } else {
    //         this.tableData = []
    //         this.pagination.total = 0
    //       }
    //     })
    //     .catch((err) => {
    //       this.loading = false
    //     })
    // },
    // 查看详情
    // getWarnDetail(row) {
    //   // this.alarmDetailShow = true
    //   this.alarmDetail = row
    //   this.$router.push({
    //     path: '/allAlarm/alarmDetail',
    //     query: {
    //       alarmId: row.alarmId
    //     }
    //   })
    // },
    // 关闭弹窗刷新当前页数据
    closeAlarmDialog() {
      this.getDetails()
    }
  }
}
</script>
<style lang="scss" scoped>
.warn-history {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 0 16px 10px 16px;

  .wholeTable-main {
    height: calc(100% - 40px);
    overflow: auto;
  }

  .wholeTable-footer {
    padding: 10px 0;
  }

  .alarmLevel {
    padding: 3px 6px;
    border-radius: 4px;
    color: #fff;
    line-height: 14px;
  }

  .alarmStatus {
    position: relative;
    display: inline-block;
    padding-left: 12px;

    .alarmStatusIcon {
      display: inline-block;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 8px;
      height: 8px;
      border-radius: 100%;
    }
  }

  .collectIcon {
    font-size: 16px;
    margin-right: 4px;
  }
}
</style>
