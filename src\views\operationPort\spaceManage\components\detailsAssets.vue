<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div><svg-icon name="right-arrow" /> 基本信息</div>
      <div class="item-row">
        <div class="width33">
          <span class="li-first-span">资产名称：</span><span class="li-last-span">{{ formInline.assetName }}</span>
        </div>
        <div class="width33">
          <span class="li-first-span">资产编码：</span><span class="li-last-span">{{ formInline.assetCode }}</span>
        </div>
        <div class="width33">
          <span class="li-first-span">ID：</span><span class="li-last-span">{{ formInline.assetsId }}</span>
        </div>
      </div>
      <div class="item-row">
        <div class="width33">
          <span class="li-first-span">品牌：</span><span class="li-last-span">{{ formInline.assetBrand }}</span>
        </div>
        <div class="width33">
          <span class="li-first-span">型号：</span><span class="li-last-span">{{ formInline.assetModel }}</span>
        </div>
      </div>
      <div class="item-row">
        <div class="width33">
          <span class="li-first-span">生产日期：</span><span class="li-last-span">{{ formInline.dateOfManufacture }}</span>
        </div>
        <div class="width33">
          <span class="li-first-span">SN码：</span><span class="li-last-span">{{ formInline.assetSn }}</span>
        </div>
      </div>
      <div class="item-row">
        <div class="width33">
          <span class="li-first-span">所在区域：</span><span class="li-last-span">{{ formInline.regionName }}</span>
        </div>
      </div>
      <div class="item-row">
        <div class="width33">
          <span class="li-first-span">计量单位：</span><span class="li-last-span">{{ formInline.unitOfMeasurement }}</span>
        </div>
        <div class="width33">
          <span class="li-first-span">启用日期：</span><span class="li-last-span">{{ formInline.startDate }}</span>
        </div>
      </div>
      <div class="item-row">
        <div class="width33">
          <span class="li-first-span">使用期限：</span><span class="li-last-span">{{ formInline.serviceLife }}&nbsp;月</span>
        </div>
        <div class="width33">
          <span class="li-first-span">资产状态：</span><span class="li-last-span">{{ formInline.assetStatusName }}</span>
        </div>
      </div>
      <div><svg-icon name="right-arrow" /> 资产归口部门</div>
      <div class="item-row">
        <div class="width33">
          <span class="li-first-span">归口部门：</span><span class="li-last-span">{{ formInline.centralizedDepartmentName }}</span>
        </div>
      </div>
      <div><svg-icon name="right-arrow" /> 资产国标分类：</div>
      <div class="item-row">
        <div class="width33">
          <span class="li-first-span">资产大类：</span><span class="li-last-span">{{ formInline.assetCategoryName }}</span>
        </div>
        <div class="width33">
          <span class="li-first-span">资产小类：</span><span class="li-last-span">{{ formInline.assetSubcategoryName }}</span>
        </div>
      </div>
      <div class="item-row">
        <div class="width33">
          <span class="li-first-span">专业类别：</span><span class="li-last-span">{{ formInline.professionalCategoryName }}</span>
        </div>
        <div class="width33">
          <span class="li-first-span">系统类别：</span><span class="li-last-span">{{ formInline.systemCategoryName }}</span>
        </div>
      </div>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="revert">关闭</el-button>
    </div>
  </PageContainer>
</template>

<script>
export default {
  name: 'detailsAssets',
  data() {
    return {
      formInline: {}
    }
  },
  mounted() {
    this.getAssetDetails()
  },
  methods: {
    revert() {
      this.$router.push({
        path: '/policyasset/assetOperations',
        query: {
          pageModel: 'list'
        }
      })
    },
    getAssetDetails() {
      this.$api.getAssetDetails({ id: this.$route.query.row.id }).then((res) => {
        if (res.code == 200) {
          this.formInline = res.data
        } else {
          this.$message.error(res.message)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
  padding: 10px;
  overflow-y: auto;
}

.item-row {
  width: 100%;
  display: flex;
  padding: 20px 0 20px 30px;
  box-sizing: border-box;

  .width33 {
    width: 33%;
    display: flex !important;

    .li-first-span {
      display: inline-block;
      width: 100px;
      // margin-right: 20px;
      font-size: 14px;
      font-family: PingFang-SC-Medium, PingFang-SC;
      font-weight: 500;
      color: #000;
    }

    .li-last-span {
      display: inline-block;
      width: calc(100% - 120px);
      font-size: 14px;
      font-family: PingFang-SC-Medium, PingFang-SC;
      font-weight: 500;
      color: #000;

      // align-items: center;
    }
  }
}
</style>
