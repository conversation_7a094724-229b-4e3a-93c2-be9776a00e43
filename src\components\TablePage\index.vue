<template>
  <div>
    <div
      class="el-table"
      :class="[
        {
          'el-table--fit': fit,
          'el-table--striped': stripe,
          'el-table--border': border || isGroup,
          'el-table--hidden': isHidden,
          'el-table--group': isGroup,
          'el-table--fluid-height': maxHeight,
          'el-table--scrollable-x': layout.scrollX,
          'el-table--scrollable-y': layout.scrollY,
          'el-table--enable-row-hover': !store.states.isComplex,
          'el-table--enable-row-transition': (store.states.data || []).length !== 0 && (store.states.data || []).length < 100
        },
        tableSize ? `el-table--${tableSize}` : ''
      ]"
      @mouseleave="handleMouseLeave($event)"
    >
      <div v-if="$slots.default" ref="hiddenColumns" class="hidden-columns"><slot></slot></div>
      <div v-else ref="hiddenColumns" class="hidden-columns">
        <template v-for="(column, index) in tableColumn.filter((e) => e.hasJudge !== false)">
          <el-table-column
            v-if="column.type === 'selection'"
            :key="index"
            type="selection"
            v-bind="column"
            :min-width="column.minWidth"
            :reserve-selection="column.reserveSelection"
          >
          </el-table-column>
          <!-- hasJudge: false 时，不显示该列 -->
          <TableColumn v-else :key="index" :column="column" :index="index">
            <template v-if="column.slot" #[column.slot]="slotData">
              <slot :name="column.slot" v-bind="slotData"></slot>
            </template>
          </TableColumn>
        </template>
      </div>
      <div v-if="showHeader" ref="headerWrapper" v-mousewheel="handleHeaderFooterMousewheel" class="el-table__header-wrapper">
        <table-header
          ref="tableHeader"
          :store="store"
          :border="border"
          :default-sort="defaultSort"
          :style="{
            width: layout.bodyWidth ? layout.bodyWidth + 'px' : ''
          }"
        >
        </table-header>
      </div>
      <div ref="bodyWrapper" class="el-table__body-wrapper" :class="[layout.scrollX ? `is-scrolling-${scrollPosition}` : 'is-scrolling-none']" :style="[bodyHeight]">
        <table-body
          :context="context"
          :store="store"
          :stripe="stripe"
          :row-class-name="rowClassName"
          :row-style="rowStyle"
          :highlight="highlightCurrentRow"
          :style="{
            width: bodyWidth
          }"
        >
        </table-body>
        <div v-if="!data || data.length === 0" ref="emptyBlock" class="el-table__empty-block" :style="emptyBlockStyle">
          <span class="el-table__empty-text">
            <slot name="empty">{{ emptyText || t('el.table.emptyText') }}</slot>
          </span>
        </div>
        <div v-if="$slots.append" ref="appendWrapper" class="el-table__append-wrapper">
          <slot name="append"></slot>
        </div>
      </div>
      <div v-if="showSummary" v-show="data && data.length > 0" ref="footerWrapper" v-mousewheel="handleHeaderFooterMousewheel" class="el-table__footer-wrapper">
        <table-footer
          :store="store"
          :border="border"
          :sum-text="sumText || t('el.table.sumText')"
          :summary-method="summaryMethod"
          :default-sort="defaultSort"
          :style="{
            width: layout.bodyWidth ? layout.bodyWidth + 'px' : ''
          }"
        >
        </table-footer>
      </div>
      <div
        v-if="fixedColumns.length > 0"
        ref="fixedWrapper"
        v-mousewheel="handleFixedMousewheel"
        class="el-table__fixed"
        :style="[
          {
            width: layout.fixedWidth ? layout.fixedWidth + 'px' : ''
          },
          fixedHeight
        ]"
      >
        <div v-if="showHeader" ref="fixedHeaderWrapper" class="el-table__fixed-header-wrapper">
          <table-header
            ref="fixedTableHeader"
            fixed="left"
            :border="border"
            :store="store"
            :style="{
              width: bodyWidth
            }"
          ></table-header>
        </div>
        <div
          ref="fixedBodyWrapper"
          class="el-table__fixed-body-wrapper"
          :style="[
            {
              top: layout.headerHeight + 'px'
            },
            fixedBodyHeight
          ]"
        >
          <table-body
            fixed="left"
            :store="store"
            :stripe="stripe"
            :highlight="highlightCurrentRow"
            :row-class-name="rowClassName"
            :row-style="rowStyle"
            :style="{
              width: bodyWidth
            }"
          >
          </table-body>
          <div v-if="$slots.append" class="el-table__append-gutter" :style="{ height: layout.appendHeight + 'px' }"></div>
        </div>
        <div v-if="showSummary" v-show="data && data.length > 0" ref="fixedFooterWrapper" class="el-table__fixed-footer-wrapper">
          <table-footer
            fixed="left"
            :border="border"
            :sum-text="sumText || t('el.table.sumText')"
            :summary-method="summaryMethod"
            :store="store"
            :style="{
              width: bodyWidth
            }"
          ></table-footer>
        </div>
      </div>
      <div
        v-if="rightFixedColumns.length > 0"
        ref="rightFixedWrapper"
        v-mousewheel="handleFixedMousewheel"
        class="el-table__fixed-right"
        :style="[
          {
            width: layout.rightFixedWidth ? layout.rightFixedWidth + 'px' : '',
            right: layout.scrollY ? (border ? layout.gutterWidth : layout.gutterWidth || 0) + 'px' : ''
          },
          fixedHeight
        ]"
      >
        <div v-if="showHeader" ref="rightFixedHeaderWrapper" class="el-table__fixed-header-wrapper">
          <table-header
            ref="rightFixedTableHeader"
            fixed="right"
            :border="border"
            :store="store"
            :style="{
              width: bodyWidth
            }"
          ></table-header>
        </div>
        <div
          ref="rightFixedBodyWrapper"
          class="el-table__fixed-body-wrapper"
          :style="[
            {
              top: layout.headerHeight + 'px'
            },
            fixedBodyHeight
          ]"
        >
          <table-body
            fixed="right"
            :store="store"
            :stripe="stripe"
            :row-class-name="rowClassName"
            :row-style="rowStyle"
            :highlight="highlightCurrentRow"
            :style="{
              width: bodyWidth
            }"
          >
          </table-body>
          <div v-if="$slots.append" class="el-table__append-gutter" :style="{ height: layout.appendHeight + 'px' }"></div>
        </div>
        <div v-if="showSummary" v-show="data && data.length > 0" ref="rightFixedFooterWrapper" class="el-table__fixed-footer-wrapper">
          <table-footer
            fixed="right"
            :border="border"
            :sum-text="sumText || t('el.table.sumText')"
            :summary-method="summaryMethod"
            :store="store"
            :style="{
              width: bodyWidth
            }"
          ></table-footer>
        </div>
      </div>
      <div
        v-if="rightFixedColumns.length > 0"
        ref="rightFixedPatch"
        class="el-table__fixed-right-patch"
        :style="{
          width: layout.scrollY ? layout.gutterWidth + 'px' : '0',
          height: layout.headerHeight + 'px'
        }"
      ></div>
      <div v-show="resizeProxyVisible" ref="resizeProxy" class="el-table__column-resize-proxy"></div>
    </div>
    <div v-if="showPage" style="padding-top: 8px">
      <el-pagination
        :current-page="$data.pagination.page"
        :page-sizes="$data.pagination.pageSizeOptions"
        :page-size="$data.pagination.size"
        :layout="$data.pagination.layoutOptions"
        :total="$data.pagination.total"
        @size-change="paginationSizeChange"
        @current-change="paginationCurrentChange"
      >
      </el-pagination>
    </div>
  </div>
</template>
<script lang="jsx">
import { Table } from 'element-ui'
import TableColumn from '@/components/TablePage/components/tableColumn'
import Vue from 'vue'
// 自定义渲染列 引入render函数组件
Vue.component('TableRender', {
  functional: true,
  props: {
    row: {
      type: Object,
      required: true
    },
    render: {
      type: Function,
      required: true
    },
    sc: {
      type: Object,
      required: true
    }
  },
  render: function (h, ctx) {
    const arr = []
    const params = {
      row: ctx.props.row,
      index: ctx.props.sc.$index
    }
    // console.log('params========', params)
    const VNode = ctx.props.render(h, params)
    arr.push(VNode)
    return h('div', arr, '')
  }
})
export default {
  name: 'TablePage',
  components: { TableColumn },
  extends: Table,
  props: {
    // 是否显示分页组件
    showPage: {
      type: Boolean,
      default: true
    },
    // 分页参数 暂支持下面五个参数
    pageData: {
      type: Object,
      default: () => {
        return {
          page: 1,
          pageSize: 15,
          total: 0,
          pageSizeOptions: [15, 30, 45, 60],
          layoutOptions: 'total, sizes, prev, pager, next, jumper'
        }
      }
    },
    // pageData对应的可调整字段
    pageProps: {
      default() {
        return {
          page: 'page',
          pageSize: 'pageSize',
          total: 'total'
        }
      }
    },
    // table列数据用于自定义列 可不穿继续使用el-table-column进行列配置
    // 支持 type（序号、多选）、width、min-width、label、prop、align等el-table-coulumn基础参数
    // 提供 render（自定义渲染函数）参数 及formatter（格式化函数）参数
    tableColumn: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  computed: {},
  watch: {
    pageData: {
      handler(val) {
        const data = Object.assign(this.$data.pagination, val)
        let pagination = this.$data.pagination
        this.setDataPagination(pagination, data)
      },
      deep: true
    }
  },
  // 使用$data代替data函数完成双向绑定 解决el-ta1ble组件data函数数据量过大导致的性能溢出问题
  created() {
    const pagination = {
      page: 1,
      pageSize: 15,
      total: 0,
      pageSizeOptions: [15, 30, 45, 60],
      layoutOptions: 'total, sizes, prev, pager, next, jumper'
    }
    this.setDataPagination(pagination, this.pageData)
  },
  mounted() {},
  methods: {
    // this.pageData 与 this.pageProps key匹配赋值
    setDataPagination(pagination = {}, data = this.pageData) {
      Object.keys(this.pageProps).forEach((key) => {
        if (data.hasOwnProperty(this.pageProps[key])) {
          pagination[key] = data[this.pageProps[key]]
        }
      })
      Object.assign(this.$data, { pagination })
    },
    // 分页数量变化事件
    paginationSizeChange(size) {
      this.$data.pagination.pageSize = size
      this.emitPagination()
    },
    // 分页页码变化事件
    paginationCurrentChange(current) {
      this.$data.pagination.page = current
      this.emitPagination()
    },
    // 将pageSize和page赋值给this.pageProps对应的key
    emitPagination() {
      let pagination = {}
      pagination[this.pageProps.page] = this.$data.pagination.page
      pagination[this.pageProps.pageSize] = this.$data.pagination.pageSize
      this.$emit('pagination', pagination)
    }
  }
}
</script>
<style scoped>
.required::before {
  content: '*';
  color: #f40;
  margin-right: 5px;
}
</style>
