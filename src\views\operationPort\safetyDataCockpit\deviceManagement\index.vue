<!--
 * @Description: 设备管理
-->
<template>
  <div class="deviceManagement">
    <div v-for="(device, dIndex) in deviceDataList" :key="dIndex" class="security-fireControl-device">
      <div class="device-header">
        <span class="device-header-title">{{ device.systemName }}</span>
        <div class="device-header-right">
          <svg-icon name="cockpit-static" />
          <span class="right-text">设备总数</span>
          <span class="right-num">{{ device.deviceTotal }}</span>
        </div>
      </div>
      <div class="device-content">
        <div class="tag-bg">概览</div>
        <div class="device-carousel">
          <el-carousel ref="carousel" class="carousel" :interval="1000000" arrow="always">
            <el-carousel-item v-for="(item, pIndex) in deviceGroupedArray[dIndex]" :key="pIndex" :loop="false">
              <div class="gas-group">
                <div v-for="(data, index) in item" :key="index" class="gas-group-item">
                  <span class="item-value">{{ data.value }} </span>
                  <span class="item-name">{{ data.name }}</span>
                </div>
              </div>
            </el-carousel-item>
          </el-carousel>
        </div>
      </div>
      <div class="alarm-content">
        <div class="tag-bg">状态</div>
        <div class="alarm-list">
          <div v-for="(item, i) in alarmList[dIndex]" :key="i" class="alarm-box">
            <svg-icon :name="item.icon" />
            <div class="alarm-box-content">
              <span class="alarm-name">{{ item.name }}</span>
              <span class="alarm-value">{{ item.value }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { monitorTypeList } from '@/util/dict.js'
export default {
  name: 'deviceManagement',
  data() {
    return {
      deviceGroupedArray: [[], []],
      alarmList: [
        [
          {
            icon: 'alarm-normal',
            name: '正常',
            value: 0
          },
          {
            icon: 'alarm-offline',
            name: '离线',
            value: 0
          },
          {
            icon: 'alarm-warning',
            name: '报警',
            value: 0
          },
          {
            icon: 'alarm-warning',
            name: '未处理报警数',
            value: 0
          }
        ],
        [
          {
            icon: 'alarm-normal',
            name: '正常',
            value: 0
          },
          {
            icon: 'alarm-offline',
            name: '离线',
            value: 0
          },
          {
            icon: 'alarm-warning',
            name: '报警',
            value: 0
          },
          {
            icon: 'alarm-warning',
            name: '未处理报警数',
            value: 0
          }
        ]
      ],
      deviceDataList: [
        {
          systemName: '安防设备',
          deviceTotal: 0
        },
        {
          systemName: '消防设备',
          deviceTotal: 0
        }
      ]
    }
  },
  mounted() {
    const categoryIdList = [
      {
        categoryId: '1730794228292841472',
        projectCode: monitorTypeList.find((item) => item.projectName == '安防系统监测').projectCode
      },
      {
        categoryId: '1752721557371301888',
        projectCode: monitorTypeList.find((item) => item.projectName == '消防系统监测').projectCode
      }
    ]
    categoryIdList.forEach((item, index) => {
      this.getAssetsCountByCategory(item.categoryId, index)
      this.getStatisticsOverview(item.projectCode, index)
      // this.getCountMonitoringNum(item.projectCode, index)
    })
  },
  methods: {
    // 获取资产类别及其设备数量
    getAssetsCountByCategory(categoryId, index) {
      this.$api.getAssetsCountByCategory({ categoryId: categoryId }).then((res) => {
        if (res.code == '200') {
          const data = res.data
          const deviceArray = data.map((item) => {
            return {
              name: item.categoryName,
              value: item.assetCount || 0
            }
          })
          this.$set(this.deviceGroupedArray, index, this.group(deviceArray, 6))
          this.deviceDataList[index].deviceTotal = deviceArray.reduce((a, b) => a + b.value, 0)
        }
      })
    },
    // 获取设备统计数量
    getStatisticsOverview(projectCode, index) {
      this.$api.getStatisticsOverview({ projectCode: projectCode, isCockpit: true }).then((res) => {
        if (res.code == '200') {
          const data = res.data
          const stateList = ['normalCount', 'offLineCount', 'policeCount', 'unDisposedCount']
          this.alarmList[index].forEach((item, i) => {
            item.value = data[stateList[i]] || 0
          })
        }
      })
    },
    // 获取设备各类型报警数量
    // getCountMonitoringNum(projectCode, index) {
    //   this.$api.CountMonitoringNum({ projectCode: projectCode }).then((res) => {
    //     console.log(res)
    //   })
    // },
    // 轮播图数据分组
    group(array, subGroupLength) {
      let index = 0
      const newArray = []
      while (index < array.length) {
        newArray.push(array.slice(index, (index += subGroupLength)))
      }
      return newArray
    },
    viewRealisticScenery() {}
  }
}
</script>

<style lang="scss" scoped>
.deviceManagement {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
  overflow: hidden;
  .security-fireControl-device {
    width: 100%;
    height: 49%;
    background: url('@/assets/images/safetyDataCockpit/box-aside-device.png') no-repeat;
    background-size: 100%;
    padding: 10px 0 0 0;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .device-header {
      height: 40px;
      line-height: 40px;
      width: 100%;
      padding: 0 10px;
      text-align: center;
      position: relative;
      .device-header-title {
        font-weight: bold;
        font-size: 16px;
        color: #cfe1f7;
      }
      .device-header-right {
        position: absolute;
        height: inherit;
        right: 10px;
        top: 0;
        .right-text {
          font-size: 14px;
          color: #1ffaff;
          margin: 0 4px;
        }
        .right-num {
          font-size: 16px;
          color: #ffffff;
        }
      }
    }
    .tag-bg {
      width: 54px;
      margin: 0 10px 8px 10px;
      height: 28px;
      line-height: 28px;
      text-align: center;
      background: url('@/assets/images/safetyDataCockpit/device-title.png') no-repeat;
      background-size: 100% 100%;
    }
    .device-content {
      height: calc(52% - 20px);

      .device-carousel {
        height: calc(100% - 36px);
        .gas-group {
          height: calc(100%);
          padding: 0 10px;
          box-sizing: border-box;
          display: flex;
          justify-content: space-around;
          .gas-group-item {
            // padding: 0 20px;
            width: 13%;
            display: flex;
            flex-direction: column;
            justify-content: space-evenly;
            align-items: center;
            background: url('@/assets/images/safetyDataCockpit/device-group.png') no-repeat;
            background-size: 100% 100%;
            .item-value {
              font-size: 20px;
              font-weight: bold;
              color: #fff;
            }
            .item-name {
              font-size: 14px;
              color: #cfe1f7;
              white-space: nowrap;
            }
          }
        }
        ::v-deep .carousel {
          width: 100%;
          height: 100%;
          .el-carousel__indicators {
            display: none;
          }
          .el-carousel__container {
            height: 100%;
          }
          .el-carousel__arrow {
            width: 16px;
            height: 34px;
            background-color: #163274;
            color: #5996f9;
            border-radius: 0;
            font-size: 1.1rem;
          }
          .el-carousel__arrow:hover {
            background-color: #373e5f;
            color: #eed6a0;
          }
          .el-carousel__arrow--left {
            left: -1px;
          }
          .el-carousel__arrow--right {
            right: -1px;
          }
        }
      }
    }
    .alarm-content {
      height: calc(42% - 20px);
      .alarm-list {
        height: calc(100% - 36px);
        display: flex;
        justify-content: space-around;
        .alarm-box {
          width: fit-content;
          white-space: nowrap;
          display: flex;
          align-items: center;
          svg {
            width: 55px;
            height: 45px;
          }
          .alarm-box-content {
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            text-align: center;
            .alarm-name {
              font-size: 14px;
              color: #cfe1f7;
              font-family: PingFang HK, PingFang HK;
            }
            .alarm-value {
              font-size: 18px;
              color: #fff;
            }
          }
        }
      }
    }
  }
}
</style>
