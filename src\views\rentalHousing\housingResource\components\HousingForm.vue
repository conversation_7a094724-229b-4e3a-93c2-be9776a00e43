<script>
const iconPdf = '/images/icon_pdf.jpg'
export default {
  name: 'HousingForm',
  props: {
    readonly: Boolean,
    large: Boolean
  },
  data: function() {
    return {
      // 正常表单
      formModel: {
        name: '',
        // 房型
        roomType: '',
        // 类型
        roomMode: '',
        // 空间地址
        spaceIds: [],
        // 所属地址
        addressBase: '',
        // 详细地址
        addressDetail: '',
        // 建筑面积
        area: '',
        // 配套设备
        devices: [],
        // 房屋图片
        photos: [],
        // 房产证
        certificates: [],
        // ar地址
        ARUrl: '',
        remark: '',
        // 成本单价
        costPrice: '',
        // 出租单价
        rentPrice: '',
        // 是否自定义单价
        isCustom: false,
        // 物业费
        serviceFee: ''
      },
      rules: {
        name: [{ required: true, message: '请输入房间名称' }],
        roomType: [{ required: true, message: '请选择房型' }],
        roomMode: [{ required: true, message: '请选择类型' }],
        area: [{ required: true, message: '请输入建筑面积' }],
        spaceIds: [{ required: true, type: 'array', message: '请选择空间位置' }]
      },
      // 当前上传组件点击后对应的文件列表key
      currentUploadProp: '',
      // 计时器ID
      $timerId: -1,
      // 表单选项
      formOptions: {
        // 房屋类型
        roomType: [],
        // 房屋类型
        roomMode: [],
        // 配套设置
        devices: [],
        // 空间位置
        space: []
      },
      // 空间级联选择器配置
      cascaderProps: {
        label: 'spaceName',
        value: 'id'
      },
      // 扩展字段源
      extendProperties: [],
      // 扩展数据缓冲存储，用于回填
      $tmpExtendInfo: '',
      // 空间信息原始成本单价
      $costPrice: '',
      // 空间信息原始出租单价
      $rentPrice: '',
      // 所在地址省市区code
      $addressCode: '',
      // 上次选择的小区ID
      $lastSpaceId: '',
      // 预览图片的表单项类型
      // 预览图片的URL
      previewUrl: '',
      // 全部预览图片的URL
      previewImages: []
    }
  },
  computed: {
    fileAcceptPhoto() {
      return '.jpg,.jpeg,.png'
    },
    fileAcceptCertificate() {
      return '.jpg,.jpeg,.png,.pdf'
    },
    span() {
      return this.large ? 8 : 12
    },
    maxValue() {
      return 99999.99
    },
    spaceId() {
      return this.formModel.spaceIds[0]
    },
    // 成本租金
    costAmount() {
      const price = Number(this.formModel.costPrice)
      const area = Number(this.formModel.area)
      const result = price * area
      return result === 0 ? '' : result.toFixed(2)
    },
    // 出租租金
    rentAmount() {
      const price = Number(this.formModel.rentPrice)
      const area = Number(this.formModel.area)
      const result = price * area
      return result === 0 ? '' : result.toFixed(2)
    },
    photoImageUrls() {
      return this.formModel.photos.map(it => it.url)
    },
    certificateImageUrls() {
      return this.formModel.certificates.map(it => it.url)
    },
    // 上传组件可用的数据源key
    uploadProp() {
      return {
        certificates: 'certificates',
        photos: 'photos'
      }
    }
  },
  created() {
    this.getExtendInfo()
    this.getDictData()
    this.getSpaceData()
  },
  beforeDestroy() {
    clearTimeout(this.$timerId)
  },
  methods: {
    // 获取数据字典
    getDictData() {
      const params = {
        pageNo: 1,
        pageSize: 9999
      }
      this.$api.rentalHousingApi.queryDictPage(params).then((res) => {
        if (res.code === '200') {
          res.data.records.forEach((it) => {
            if (it.dictState !== '1') return
            if (it.dictType === 'fangxing') {
              this.formOptions.roomType.push(it)
            } else if (it.dictType === 'leixing') {
              this.formOptions.roomMode.push(it)
            } else if (it.dictType === 'peitaosheshi') {
              this.formOptions.devices.push(it)
            }
          })
        }
      })
    },
    // 根据key，获取上传文件对应的数据源
    getFileList(key) {
      if (key === this.uploadProp.photos) {
        return this.formModel.photos
      } else if (key === this.uploadProp.certificates) {
        return this.formModel.certificates
      } else {
        return []
      }
    },
    // 获取空间数据
    getSpaceData() {
      this.$api.rentalHousingApi.allSpaceList().then((res) => {
        if (res.code === '200') {
          this.formOptions.space = this.$tools.transData(res.data, 'id', 'parentId', 'children')
        }
      })
    },
    // 获取扩展字段数据
    getExtendInfo() {
      this.$api.rentalHousingApi.queryExtendFieldList().then((res) => {
        if (res.code === '200') {
          res.data.forEach((it) => {
            if (it.fieldStatus === '1') {
              // 重命名表单字段名称，防止冲突
              const formField = this.prefix + it.fieldId
              it.formField = formField
              this.extendProperties.push(it)
            }
          })
          this.revertExtendData()
        }
      })
    },
    // 空间地址改变
    onSpaceChange() {
      // 如果小区地址发生变化再获取所在位置
      if (this.$lastSpaceId === this.spaceId) return
      this.getSpaceDetail().then(() => {
        this.$lastSpaceId = this.spaceId
      })
    },
    // 获取空间信息数据
    getSpaceDetail() {
      return this.$api.rentalHousingApi
        .getSpaceEstateInfoById({ id: this.spaceId })
        .then((res) => {
          if (res.code === '200') {
            this.$addressCode = res.data.addressCode
            this.formModel.addressBase = res.data.addressName
            this.formModel.addressDetail = res.data.addressDetails || ''
            this.formModel.costPrice = res.data.costPrice || ''
            this.formModel.rentPrice = res.data.hirePrice || ''
            // 缓存空间信息原始的成本价和出租价
            this.$costPrice = this.formModel.costPrice
            this.$rentPrice = this.formModel.rentPrice
            this.$tmpExtendInfo = res.data.extendInfo || ''
            this.revertExtendData()
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取空间所在地址失败'))
    },
    /**
     * 设置空间ID
     * expose
     */
    setSpaceIds(spaceIds) {
      this.formModel.spaceIds = spaceIds
      this.onSpaceChange()
    },
    /**
     * 反显数据
     * expose
     */
    // 获取详情信息
    revertData(data) {
      if (!data) return
      const getNumberStr = val => {
        const num = Number.parseFloat(val ?? '')
        return isNaN(num) ? '' : num.toFixed(2)
      }
      // 获取详情信息
      this.formModel.name = data.houseName || ''
      this.formModel.roomType = data.houseTypeCode || ''
      this.formModel.roomMode = data.houseNatureCode || ''
      // 房间的空间ID还原
      if (data.spaceIds) {
        this.formModel.spaceIds = data.spaceIds.split(',')
      }
      this.formModel.addressBase = data.addressName || ''
      this.formModel.addressDetail = data.addressDetails || ''
      this.formModel.area = getNumberStr(data.floorArea)
      // 配套设施还原为数组
      if (data.ancillaryFacilityCode) {
        this.formModel.devices = data.ancillaryFacilityCode.split(',')
      }
      // 房屋照片
      if (data.housePictureUrl) {
        // 分割后和还原
        this.formModel.photos = data.housePictureUrl.split(',').map(this.covertPathToUploadFile.bind(this, this.uploadProp.photos))
      }
      // 房产证照片
      if (data.houseCertificateUrl) {
        this.formModel.certificates = data.houseCertificateUrl.split(',').map(this.covertPathToUploadFile.bind(this, this.uploadProp.certificates))
      }
      this.formModel.ARUrl = data.housePanoramaUrl || ''
      this.formModel.remark = data.remark || ''
      this.formModel.isCustom = data.customPrice === '1'
      this.formModel.costPrice = getNumberStr(data.costPrice)
      this.formModel.rentPrice = getNumberStr(data.hirePrice)
      this.formModel.propertyFee = getNumberStr(data.propertyFee)
      // 缓存地址code
      this.$addressCode = data.addressCode || ''
      // 缓存单价
      this.$costPrice = this.formModel.costPrice
      this.$rentPrice = this.formModel.rentPrice
      // 缓存小区ID
      this.$lastSpaceId = this.spaceId
      // 扩展信息
      this.$tmpExtendInfo = data.extendInfo || ''
      this.revertExtendData()
      this.$refs.formRef.clearValidate()
    },
    /**
     * 重置表单数据
     * expose
     */
    resetFields() {
      clearTimeout(this.$timerId)
      this.$refs.formRef.resetFields()
      this.currentUploadProp = ''
      this.$rentPrice = ''
      this.$costPrice = ''
      this.$addressCode = ''
      this.$tmpExtendInfo = ''
    },
    /**
     * expose
     * 验证表单数据，并返回数据
     * @return { Promise<Object> } 表单数据
     */
    validate() {
      return this.$refs.formRef
        .validate()
        .catch(() => Promise.reject())
        .then(() => {
          let uploaded = this.formModel.photos.every((it) => it.status === 'success')
          if (!uploaded) {
            throw '房屋照片上传中'
          }
          uploaded = this.formModel.certificates.every((it) => it.status === 'success')
          if (!uploaded) {
            throw '房产证照片上传中'
          }
          const params = {
            // 房屋名称
            houseName: this.formModel.name,
            // 房屋类型
            houseTypeCode: this.formModel.roomType,
            // 房屋性质
            houseNatureCode: this.formModel.roomMode,
            // 所在地址省市区code
            addressCode: this.$addressCode,
            // 所在地址省市区名称
            addressName: this.formModel.addressBase,
            // 地址详情
            addressDetails: this.formModel.addressDetail,
            // 配套设施
            ancillaryFacilityCode: this.formModel.devices.join(),
            // 面积
            floorArea: this.formModel.area,
            // 出租单价
            costPrice: this.formModel.costPrice,
            // 出租价格
            costRent: this.costAmount,
            // 出租成本
            hirePrice: this.formModel.rentPrice,
            // 出租租金
            hireRent: this.rentAmount,
            // 是否为自定义价格
            customPrice: this.formModel.isCustom ? '1' : '0',
            // 空间地址ID串
            spaceIds: this.formModel.spaceIds.join(),
            // 空间地址文本 小区/楼/单元/层
            spaceNames: this.$refs.cascaderRef.presentText,
            // 房屋照片
            housePictureUrl: this.formModel.photos.map((it) => it.uploadPath).join(),
            // 房产证照片
            houseCertificateUrl: this.formModel.certificates.map((it) => it.uploadPath).join(),
            // AR地址
            housePanoramaUrl: this.formModel.ARUrl,
            // 备注信息
            remark: this.formModel.remark,
            // 物业服务费
            propertyFee: this.formModel.serviceFee,
            // 拓展字段信息
            extendInfo: this.$tmpExtendInfo
          }
          if (!params.spaceNames) {
            throw '请选择空间位置'
          }
          params.houseTypeName = this.formOptions.roomType.find((d) => d.dictCode === this.formModel.roomType)?.dictName ?? ''
          params.ancillaryFacilityName = this.formModel.devices.map((it) => this.formOptions.roomMode.find((d) => d.dictCode === it)?.dictName).join(',')
          params.spaceId = this.formModel.spaceIds.slice(-1)[0]
          params.spaceName = params.spaceNames.split(' / ').pop()
          return params
        })
    },
    /**
     * 只读模式点击上传的图片，进行预览
     * @param fileItem
     */
    onImageClick(fileItem) {
      if (/\.pdf/i.test(fileItem.uploadPath)) {
        // 还原出下载路径，然后使用window.open方式打开文件
        const url = this.$tools.imgUrlTranslation(fileItem.uploadPath)
        window.open(url, 'house_certificate', 'noreferrer=true')
      }
    },
    // 填充扩展信息
    revertExtendData() {
      if (!this.extendProperties.length || !this.$tmpExtendInfo) return
      try {
        const extendData = JSON.parse(this.$tmpExtendInfo)
        this.extendProperties.forEach((it) => {
          const value = extendData[it.fieldId] ?? ''
          // 扩展字段需要和表单字段映射
          this.$set(this.formModel, it.formField, value)
        })
      } catch {
        this.$message.error('扩展信息数据格式错误')
        console.error(this.$tmpExtendInfo)
      }
    },
    // 转换路径为已上传的文件
    covertPathToUploadFile(prop, path) {
      const uid = Date.now() + (Math.random() * 1000).toFixed(0)
      let url
      if (/.pdf$/i.test(path)) {
        url = iconPdf
      } else {
        url = this.$tools.imgUrlTranslation(path)
      }
      return {
        uid,
        url,
        prop,
        uploadPath: path,
        status: 'success'
      }
    },
    // 文件上传代理
    async handleHttpRequest(propKey, request) {
      const fileList = this.getFileList(propKey)
      await this.checkFile(request.file, fileList)
      const params = new FormData()
      params.append('file', request.file)
      let res = await this.$api.uploadFile(params)
      if (res.code === '200') {
        this.$message({
          message: '上传成功',
          type: 'success'
        })
        const fileInfo = fileList.find((it) => it.uid === request.file.uid)
        if (fileInfo) {
          fileInfo.uploadPath = res.data.fileKey
        }
      } else {
        throw res.message
      }
    },
    // 检测文件是否可以上传
    async checkFile(file, fileList) {
      if (/\s/.test(file.name)) {
        throw '文件名不能存在空格'
      }
      if (fileList.filter((x) => x.name === file.name).length > 1) {
        throw '已存在此文件，请重新选择'
      }
      const extension = file.name.substring(file.name.lastIndexOf('.'))
      // 根据key判断是否文件上传模式
      const isPhotoUpload = this.currentUploadProp === this.uploadProp.photos
      // 根据类型验证
      const accept = isPhotoUpload ? this.fileAcceptPhoto : this.fileAcceptCertificate
      if (!accept.includes(extension)) {
        throw '请选择正确的文件类型'
      }
      const size = isPhotoUpload ? 10 : 5
      if (file.size > size * 1024 * 1024) {
        throw `文件大小不能超过${size}MB`
      }
    },
    // 上传失败提示
    handleUploadError(err) {
      let errMsg = '上传失败'
      if (typeof err === 'string') {
        errMsg = err
      }
      this.$message.error(errMsg)
    },
    // 失踪同步文件列表
    handleFileChange(file) {
      if (file.status === 'ready') {
        // 给文件绑定上传组件对应的数据源key
        file.prop = this.currentUploadProp
        // 对刚选择的pdf文件进行图片替换
        if (/.pdf$/i.test(file.name)) {
          file.url = iconPdf
        }
      }
      const fileList = this.getFileList(file.prop)
      if (file.status === 'fail') {
        // 失败的文件，500ms后删除，视觉效果会好一些
        window.clearTimeout(this.$timerId)
        this.$timerId = window.setTimeout(() => {
          let index = -1
          do {
            index = fileList.findIndex((it) => it.status === 'fail')
            if (index > -1) {
              fileList.splice(index, 1)
            }
          } while (index > -1)
          this.$timerId = -1
        }, 500)
      } else {
        const index = fileList.findIndex((it) => it.uid === file.uid)
        if (index > -1) {
          fileList.splice(index, 1, file)
        } else {
          fileList.push(file)
        }
      }
    },
    // 文件移除时
    handleFileRemove(file) {
      const fileList = this.getFileList(file.prop)
      const index = fileList.findIndex((it) => it.uid === file.uid)
      if (index > -1) {
        fileList.splice(index, 1)
      }
    },
    // 金额发生变化
    onAreaChange(field, val) {
      if (!val) return
      const fixedValue = Math.min(Number(val), this.maxValue).toFixed(2)
      this.$set(this.formModel, field, fixedValue)
    },
    // 切换自定义单价
    onCustomChange(value) {
      // 如果不是使用自定单价，尝试还原原始价格
      if (!value && this.$costPrice) {
        this.formModel.costPrice = this.$costPrice
      }
      if (!value && this.$rentPrice) {
        this.formModel.rentPrice = this.$rentPrice
      }
    }
  }
}
</script>
<template>
  <el-form ref="formRef" class="housing-form" :model="formModel" :rules="rules" :disabled="readonly" :class="{ readonly }" label-width="120px">
    <div class="housing-form__title">
      <svg-icon name="right-arrow" />
      基本信息
    </div>
    <el-row>
      <el-col :span="span">
        <el-form-item label="房间名称" prop="name">
          <el-input v-model="formModel.name" placeholder="请输入" clearable maxlength="50"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="span">
        <el-form-item label="房型" prop="roomType">
          <el-select v-model="formModel.roomType" placeholder="请选择">
            <el-option v-for="item of formOptions.roomType" :key="item.dictCode" :label="item.dictName" :value="item.dictCode"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="span">
        <el-form-item label="类型" prop="roomMode">
          <el-select v-model="formModel.roomMode" placeholder="请选择">
            <el-option v-for="item of formOptions.roomMode" :key="item.dictCode" :label="item.dictName" :value="item.dictCode"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="span">
        <el-form-item label="空间位置" prop="spaceIds">
          <el-cascader
            ref="cascaderRef"
            v-model="formModel.spaceIds"
            :props="cascaderProps"
            :options="formOptions.space"
            placeholder="请选择"
            @change="onSpaceChange"
          ></el-cascader>
        </el-form-item>
      </el-col>
      <el-col :span="span">
        <el-form-item label="地址信息" prop="addressBase">
          <el-input :value="formModel.addressBase" placeholder="请选择空间位置" disabled></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="span">
        <el-form-item label="详细地址" prop="addressDetail">
          <el-input :value="formModel.addressDetail" placeholder="请选择空间位置" disabled></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="span">
        <el-form-item label="建筑面积" prop="area">
          <el-input v-model="formModel.area" type="number" :min="1" :max="maxValue" :step="0.01" placeholder="请输入" :maxlength="7" @change="onAreaChange('area', $event)">
            <template #append>m²</template>
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="span">
        <el-form-item label="全景地址" prop="ARUrl">
          <el-input v-model="formModel.ARUrl" clearable maxlength="100" :placeholder="readonly?'-':'请输入'"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="配套设置" prop="devices">
          <el-checkbox-group v-model="formModel.devices">
            <el-checkbox v-for="item of formOptions.devices" :key="item.dictCode" :label="item.dictCode">
              {{ item.dictName }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-col>
      <el-col :span="readonly?16:12">
        <el-form-item label="房屋图片" prop="photos">
          <template v-if="readonly">
            <ul v-if="photoImageUrls.length" class="el-upload-list el-upload-list--picture-card">
              <li v-for="item of formModel.photos" :key="item.uid" class="el-upload-list__item">
                <el-image :src="item.url" alt="" class="el-upload-list__item-thumbnail" :preview-src-list="photoImageUrls"></el-image>
              </li>
            </ul>
            <div v-else style="padding-left: 16px">无</div>
          </template>
          <template v-else>
            <el-upload
              action=""
              list-type="picture-card"
              class="housing-form__upload"
              data-type="photo"
              :file-list="formModel.photos"
              :accept="fileAcceptPhoto"
              :limit="10"
              multiple
              :http-request="handleHttpRequest.bind(this,uploadProp.photos)"
              :on-change="handleFileChange"
              :on-error="handleUploadError"
              :on-remove="handleFileRemove"
              @click.native="currentUploadProp = uploadProp.photos"
            >
              <i class="el-icon-plus"></i>
              <div slot="tip" class="el-upload__tip">支持JPG/JPEG/PNG,且大小不超过10M</div>
            </el-upload>
          </template>
        </el-form-item>
      </el-col>
      <el-col :span="span">
        <el-form-item label="房产证" prop="certificates">
          <template v-if="readonly">
            <ul v-if="certificateImageUrls.length" class="el-upload-list el-upload-list--picture-card">
              <li v-for="item of formModel.certificates" :key="item.uid" class="el-upload-list__item">
                <el-image :src="item.url" alt="" class="el-upload-list__item-thumbnail" :preview-src-list="certificateImageUrls" @click="onImageClick(item)"></el-image>
              </li>
            </ul>
            <div v-else style="padding-left: 16px">无</div>
          </template>
          <template v-else>
            <el-upload
              action=""
              list-type="picture-card"
              class="housing-form__upload"
              data-type="certificate"
              :file-list="formModel.certificates"
              :accept="fileAcceptCertificate"
              :limit="10"
              :http-request="handleHttpRequest.bind(this,uploadProp.certificates)"
              :on-change="handleFileChange"
              :on-error="handleUploadError"
              :on-remove="handleFileRemove"
              @click.native="currentUploadProp = uploadProp.certificates"
            >
              <i class="el-icon-plus"></i>
              <div slot="tip" class="el-upload__tip">支持JPG/JPEG/PNG/PDF,且大小不超过5M</div>
            </el-upload>
          </template>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="备注" prop="remark">
          <el-input v-model="formModel.remark" type="textarea" :rows="3" clearable maxlength="500" :placeholder="readonly?'-':'请输入'"></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <div class="housing-form__title housing-form__title--extend">
      <svg-icon name="right-arrow" />
      <span>租金信息</span>
      <el-form-item prop="isCustom" label-width="50px">
        <el-checkbox v-model="formModel.isCustom" label="自定义" @change="onCustomChange"></el-checkbox>
      </el-form-item>
    </div>
    <el-row>
      <el-col :span="span">
        <el-row>
          <el-col :span="14">
            <el-form-item label="成本单价" prop="costPrice">
              <el-input
                v-model="formModel.costPrice"
                :disabled="!formModel.isCustom"
                type="number"
                :min="1"
                :max="maxValue"
                :step="0.01"
                :placeholder="formModel.isCustom ? '请输入' : ''"
                :maxlength="7"
                @change="onAreaChange('costPrice', $event)"
              >
                <template #suffix>元/m²/月</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="成本租金" label-width="90px">
              <el-input :value="costAmount" disabled :title="`${costAmount||0}元`">
                <template #suffix>元</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-col>
      <el-col :span="span">
        <el-row>
          <el-col :span="14">
            <el-form-item label="出租单价" prop="rentPrice">
              <el-input
                v-model="formModel.rentPrice"
                :disabled="!formModel.isCustom"
                type="number"
                :min="1"
                :max="maxValue"
                :step="0.01"
                :placeholder="formModel.isCustom ? '请输入' : ''"
                :maxlength="7"
                @change="onAreaChange('rentPrice', $event)"
              >
                <template #suffix>元/m²/月</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="出租租金" label-width="90px">
              <el-input :value="rentAmount" disabled :title="`${costAmount||0}元`">
                <template #suffix>元</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-col>
      <el-col :span="span">
        <el-form-item label="物业费" prop="serviceFee">
          <el-input v-model="formModel.serviceFee" type="number" disabled>
            <template #suffix>元</template>
          </el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <template v-if="extendProperties.length">
      <div class="housing-form__title">
        <svg-icon name="right-arrow" />
        扩展信息
      </div>
      <el-row>
        <el-col v-for="item of extendProperties" :key="item.id" :span="span">
          <el-form-item :label="item.fieldName" :prop="item.formField">
            <el-input v-if="item.fieldType === '1'" :value="formModel[item.formField]" :placeholder="readonly?'-':''" disabled></el-input>
            <el-input v-if="item.fieldType === '2'" :value="formModel[item.formField]" type="textarea" :rows="1" :placeholder="readonly?'-':''" disabled></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </template>
  </el-form>
</template>
<style lang="scss" scoped>
.el-form {
  width: 100%;
  background-color: #fff;
  padding: 10px 16px 0;
  height: 100%;
  .el-cascader,
  .el-select {
    width: 100%;
  }
  ::v-deep(.el-input) {
    .el-input__inner[type=number] {
      &::-webkit-inner-spin-button {
        display: none;
      }
    }
  }
  &.disabled {
    .el-upload__tip {
      display: none;
    }
  }
  &.readonly {
    ::v-deep(.el-form-item) {
      margin-bottom: 10px;
      .el-form-item__label {
        &::before {
          display: none;
        }
      }
      .el-form-item__content {
        .el-input {
          &.el-input-group--append {
            width: auto;
            .el-input__inner {
              height: 40px;
              line-height: 40px;
            }
          }
        }
        .el-textarea__inner,
        .el-input__inner {
          background: transparent;
          border: none;
          color: inherit;
          cursor: default;
          padding-right: 0;
          &::placeholder {
            color: inherit;
          }
        }
        .el-textarea__inner {
          resize: none;
          padding-top: 10px;
        }
        .el-input-group__append {
          background: transparent;
          border: none;
          padding-left: 0;
        }
        .el-select,
        .el-cascader {
          .el-input__suffix {
            display: none;
          }
          .el-input {
            width: 100%;
          }
        }
        .el-checkbox {
          .el-checkbox__label {
            color: inherit;
          }
        }
      }
    }
  }
}
.housing-form__title--extend {
  .el-form-item {
    display: inline-block;
    margin-bottom: 0;
  }
}
</style>
