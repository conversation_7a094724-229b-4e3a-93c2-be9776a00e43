<template>
  <PageContainer>
    <div slot="content" class="monitor-setting">
      <div class="monitor-setting-left" :class="activeName === 'energy' || pageModel === 'grap' ? 'left-width-zero' : ''">
        <div class="title_box"><svg-icon name="right-arrow" /> 空间结构</div>
        <el-input v-model="filterText" style="width: 246px; padding-top: 10px" placeholder="请输入关键字"></el-input>
        <div v-loading="treeLoading" class="sino_tree_box">
          <div class="tree_div">
            <el-tree
              ref="tree"
              class="filter-tree"
              :data="treeData"
              :props="defaultProps"
              :default-expanded-keys="idArr"
              :filter-node-method="filterNode"
              :highlight-current="true"
              node-key="id"
              @node-click="nodeClick"
            ></el-tree>
          </div>
        </div>
      </div>
      <div class="monitor-setting-right">
        <div class="right-heade" style="display: none">
          <el-tabs v-model="activeName" @tab-click="handleClick($event.name)">
            <!-- <el-tab-pane v-for="item in componentFilter" :key="item.outputComponent" :label="item.componentName" :name="item.outputComponent"></el-tab-pane> -->
            <el-tab-pane label="空间" name="space"></el-tab-pane>
            <el-tab-pane label="工单" name="work"></el-tab-pane>
            <el-tab-pane label="资产" name="property"></el-tab-pane>
            <el-tab-pane label="医废" name="waste"></el-tab-pane>
            <el-tab-pane label="隐患" name="troubl"></el-tab-pane>
            <el-tab-pane label="风险" name="risk"></el-tab-pane>
            <el-tab-pane label="能耗" name="energy"></el-tab-pane>
          </el-tabs>
          <div v-if="activeName === 'energy'" class="energy-tabs">
            <div v-for="(item, index) in energyTabList" :key="index" :class="{ 'active-energy-tab': item.value === energyModel }" @click="changeEnergyTabs(item)">
              {{ item.label }}
            </div>
          </div>
          <div v-else>
            <div v-if="editDashExample" class="batch-control">
              <el-button type="primary" plain @click="cancelStaging">取消</el-button>
              <el-button type="primary" @click="saveStaging">保存</el-button>
            </div>
            <div v-else>
              <el-radio-group v-model="pageModel" size="mini" @change="changePageModel">
                <el-radio-button label="grap">图形</el-radio-button>
                <el-radio-button label="list">列表</el-radio-button>
              </el-radio-group>
            </div>
          </div>
        </div>
        <div class="right-content" :class="[pageModel === 'list' ? 'normal-margin' : 'reduce-margin']">
          <div v-if="activeName === 'energy'" style="width: 100%; height: 100%">
            <energyOperations :energyModel="energyModel"></energyOperations>
          </div>
          <div v-else style="width: 100%; height: 100%">
            <dashboard v-if="pageModel === 'grap' && dashExampleShow" id="dashExample">
              <dash-layout v-bind="dlayout" :debug="false">
                <component
                  :is="componentFilter.inputComponent"
                  :ref="componentFilter.inputComponent"
                  :componentData="componentFilter"
                  :dlayoutItems="dlayout.items"
                  @resizeEnd="resizeEnd"
                  @all-more-Oper="allMoreOper"
                ></component>
              </dash-layout>
            </dashboard>
            <div v-if="pageModel === 'list'" class="data-Manage">
              <!-- <dataManage :componentData="componentFilter"></dataManage> -->
              <space v-if="activeName == 'space'" ref="space" :modelCode="modelCode"></space>
              <work v-if="activeName == 'work'" ref="work" :roomData="roomData"></work>
              <sjtyyProperty v-if="activeName == 'sjtyyProperty'" ref="property" :placeIds="placeIds" :assetParmas="assetParmas"></sjtyyProperty>
              <property v-if="activeName == 'property'" ref="property" :placeIds="placeIds"></property>
              <waste v-if="activeName == 'waste'" ref="waste" :treeList="treeList"></waste>
              <troubl v-if="activeName == 'troubl'" ref="troubl" :placeIds="placeIds"></troubl>
              <risk v-if="activeName == 'risk'" ref="risk" :placeIds="placeIds"></risk>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PageContainer>
</template>

<script>
import { transData } from '@/util'
import space from './components/space.vue'
import work from './components/work.vue'
import sjtyyProperty from './components/sjtyyProperty.vue'
import property from './components/property.vue'
import waste from './components/waste.vue'
import troubl from './components/troubl.vue'
import risk from './components/risk.vue'
import { Dashboard, DashLayout } from 'vue-responsive-dash'
export default {
  name: 'dataManage',
  components: {
    space,
    work,
    property,
    sjtyyProperty,
    waste,
    troubl,
    risk,
    Dashboard,
    DashLayout,
    spaceOperations: () => import('./operationsManage/spaceOperations'),
    iomsOperations: () => import('./operationsManage/iomsOperations'),
    // dataManage: () => import('../dataManage'),
    dangerOperations: () => import('./operationsManage/dangerOperations'),
    wasteOperations: () => import('./operationsManage/wasteOperations'),
    riskOperations: () => import('./operationsManage/riskOperations'),
    assetOperations: () => import('./operationsManage/assetOperations'),
    energyOperations: () => import('./operationsManage/energyOperations')
  },
  props: {
    componentData: {
      type: Object,
      default: () => {}
    },
    assetParmas: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      pageModel: this.$route.query.pageModel || 'list',
      energyModel: 'energyOverview',
      energyTabList: [
        {
          label: '综合能耗',
          value: 'energyOverview'
        },
        {
          label: '电能耗',
          value: 'powerOverview'
        },
        {
          label: '水能耗',
          value: 'waterOverview'
        }
      ],
      componentsList: Object.freeze([
        {
          inputComponent: 'spaceOperations',
          outputComponent: 'space',
          componentName: '空间',
          menuType: 1,
          dlayoutItems: [
            { id: 'personalInfo', x: 0, y: 0, width: 8, height: 6 },
            { id: 'msgReminder', x: 8, y: 0, width: 8, height: 6 },
            { id: 'todoItems', x: 16, y: 0, width: 8, height: 6 },
            { id: 'workOderType', x: 0, y: 6, width: 8, height: 6 },
            { id: 'warnManageTable', x: 8, y: 6, width: 16, height: 6 }
          ]
        },
        {
          inputComponent: 'iomsOperations',
          outputComponent: 'work',
          componentName: '工单',
          menuType: 2,
          dlayoutItems: [
            { id: 'serviceOverview', x: 0, y: 5, width: 8, height: 5 },
            { id: 'last6months', x: 8, y: 0, width: 9, height: 5 },
            { id: 'orderOverview', x: 8, y: 5, width: 16, height: 5 },
            { id: 'annualOrderTrend', x: 8, y: 10, width: 16, height: 5 },
            { id: 'itemsTop10', x: 17, y: 0, width: 7, height: 5 },
            { id: 'departTop5', x: 0, y: 10, width: 8, height: 5 },
            { id: 'reportBuildingTop5', x: 0, y: 15, width: 8, height: 5 },
            { id: 'workMonthComparison', x: 8, y: 15, width: 8, height: 5 },
            { id: 'groupWorkOrder', x: 16, y: 15, width: 8, height: 5 },
            { id: 'costOfConsumables', x: 0, y: 20, width: 8, height: 5 },
            { id: 'serviceEfficiency', x: 0, y: 0, width: 8, height: 5 }
          ]
        },
        {
          inputComponent: 'assetOperations',
          outputComponent: 'property',
          componentName: '资产',
          menuType: 5,
          dlayoutItems: [
            { id: '1', name: '资产总览', x: 0, y: 0, width: 8, height: 7, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
            { id: '2', name: '工作日历', x: 8, y: 0, width: 16, height: 7, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
            { id: '3', name: '专业类别分析', x: 0, y: 7, width: 8, height: 5, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
            { id: '4', name: '资产使用年限分析', x: 8, y: 7, width: 8, height: 5, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
            { id: '5', name: '资产巡检分析', x: 16, y: 7, width: 8, height: 5, dragAllowFrom: '.drag_class', resizable: true, draggable: true }
          ]
        },
        {
          inputComponent: 'assetOperationsDM',
          outputComponent: 'sjtyyProperty',
          componentName: '资产',
          menuType: 8,
          dlayoutItems: [
            { id: '1', name: '资产总览', x: 0, y: 0, width: 16, height: 4, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
            { id: '2', name: '资产使用年限分析', x: 16, y: 0, width: 8, height: 4, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
            { id: '3', name: '资产状态分析', x: 0, y: 4, width: 16, height: 4, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
            { id: '4', name: '资产归口部门分析', x: 16, y: 4, width: 8, height: 4, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
            { id: '5', name: '资产巡检分析', x: 0, y: 8, width: 24, height: 4, dragAllowFrom: '.drag_class', resizable: true, draggable: true }
          ]
        },
        {
          inputComponent: 'wasteOperations',
          outputComponent: 'waste',
          componentName: '医废',
          menuType: 6,
          dlayoutItems: [
            { id: 'wasteTopLeft', x: 0, y: 0, width: 12, height: 6 },
            { id: 'wasteTopCenter', x: 12, y: 0, width: 6, height: 6 },
            { id: 'wasteTopRight', x: 18, y: 0, width: 6, height: 6 },
            { id: 'wasteBottomLeft', x: 0, y: 6, width: 12, height: 6 },
            { id: 'wasteBottomRight', x: 12, y: 6, width: 12, height: 6 }
          ]
        },
        {
          inputComponent: 'dangerOperations',
          outputComponent: 'troubl',
          componentName: '隐患',
          menuType: 3,
          dlayoutItems: [
            { id: 'dangerStatus', x: 0, y: 0, width: 8, height: 5, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
            { id: 'dangerType', x: 8, y: 0, width: 9, height: 5, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
            { id: 'dangerTrend', x: 8, y: 5, width: 16, height: 5, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
            { id: 'realTime', x: 0, y: 10, width: 24, height: 6, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
            { id: 'overview', x: 17, y: 0, width: 7, height: 5, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
            { id: 'departmentDanger', x: 0, y: 5, width: 8, height: 5, dragAllowFrom: '.drag_class', resizable: true, draggable: true }
          ]
        },
        {
          inputComponent: 'riskOperations',
          outputComponent: 'risk',
          componentName: '风险',
          menuType: 4,
          dlayoutItems: [
            { id: '1', name: '风险等级分析', x: 0, y: 0, width: 8, height: 6, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
            { id: '2', name: '风险类型分析', x: 8, y: 0, width: 8, height: 6, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
            { id: '3', name: '风险巡查任务分析', x: 16, y: 0, width: 8, height: 6, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
            { id: '4', name: '风险部门分布', x: 0, y: 6, width: 16, height: 6, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
            { id: '5', name: '科室任务巡检分析', x: 16, y: 6, width: 8, height: 6, dragAllowFrom: '.drag_class', resizable: true, draggable: true }
          ]
        },
        {
          componentName: '能耗',
          outputComponent: 'energy',
          componentRouter: 'energyOperations'
        }
      ]),
      dlayout:
        // x=> 4 8 12 小中大
        {
          breakpoint: 'xs',
          numberOfCols: 24,
          items: []
        },
      editDashExample: false,
      dashExampleShow: true,
      treeLoading: true,
      activeName: '',
      treeData: [],
      defaultProps: {
        label: 'ssmName',
        children: 'list'
      },
      idArr: [],
      filterText: '',
      modelCode: '',
      placeIds: '',
      spaces: [],
      roomData: [],
      treeList: {},
      isRouter: this.$route.name === 'dataManage'
    }
  },
  computed: {
    componentFilter() {
      // if (this.isRouter) {
      // return this.componentsList
      // } else {
      console.log(this.activeName)
      return this.componentsList.find((e) => e.outputComponent === this.activeName)
      // }
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  mounted() {
    this.spaceTreeListFn()
    // const componentFilter =
    // if (this.isRouter) {
    if (localStorage.getItem('activeName')) {
      this.activeName = localStorage.getItem('activeName')
      this.$nextTick(() => {
        localStorage.removeItem('activeName')
      })
    } else {
      this.activeName = 'space'
    }
    if (this.componentData.componentRouter == 'iomsOperations') {
      this.activeName = 'work'
    }
    if (this.componentData.componentRouter == 'wasteOperations') {
      this.activeName = 'waste'
    }
    if (this.componentData.componentRouter == 'dangerOperations') {
      this.activeName = 'troubl'
    }
    if (this.componentData.componentRouter == 'riskOperations') {
      this.activeName = 'risk'
    }
    if (this.componentData.componentRouter == 'assetOperations') {
      this.activeName = 'property'
    }
    if (this.componentData.componentRouter == 'assetOperationsDM') {
      this.activeName = 'sjtyyProperty'
    }
    this.getDragManageList()
    // } else {
    //   console.log('555555555', this.componentFilter[0].outputComponent)
    //   this.handleClick(this.componentFilter[0].outputComponent)
    // }
  },
  methods: {
    // 获取可拖拽列表
    getDragManageList() {
      const params = {
        userId: this.$store.state.user.userInfo.user.staffId,
        menuType: this.componentFilter.menuType
      }
      this.$api.getWorktopManageList(params).then((res) => {
        if (res.code == 200) {
          const data = res.data
          let componentItems = []
          // 有接口数据取接口数据 无接口数据取字典默认配置数据
          if (data.length) {
            componentItems = data
          } else {
            componentItems = this.componentFilter.dlayoutItems
          }
          // 遍历增加dragAllowFrom、resizable、draggable字段
          const items = componentItems.map((item) => {
            return {
              id: item.id,
              x: item.x,
              y: item.y,
              width: item.width,
              height: item.height,
              dragAllowFrom: '.drag_class',
              resizable: false,
              draggable: false
            }
          })
          this.dashExampleShow = false
          this.$nextTick(() => {
            this.dashExampleShow = true
            this.dlayout.items = items
          })
        }
      })
    },
    // 缩放结束事件
    resizeEnd(val, item) {
      console.log(this.$refs[item])
      this.$refs[item].echartsResize()
    },
    changeEnergyTabs(item) {
      this.energyModel = item.value
    },
    // 取消编辑工作台模块
    cancelStaging() {
      this.editDashExample = false
      this.getDragManageList()
    },
    // 保存工作台模块
    saveStaging() {
      console.log(this.dlayout.items)
      const items = this.dlayout.items
      if (!items.length) {
        return this.$message.warning('无可配置模块')
      }
      const jsonList = items.map((e) => {
        return {
          id: e.id,
          x: e.x,
          y: e.y,
          width: e.width,
          height: e.height
        }
      })
      const params = {
        jsonList: JSON.stringify(jsonList),
        userId: this.$store.state.user.userInfo.user.staffId,
        menuType: this.componentFilter.menuType
      }
      console.log('save提交', params)
      this.$api.saveWorktopManage(params).then((res) => {
        if (res.code == 200) {
          this.$message.success('保存成功')
          this.editDashExample = false
          this.getDragManageList()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 更多操作事件
    allMoreOper(type, component) {
      this.editDashExample = true
      this.dlayout.items.map((e) => {
        e.resizable = true
        e.draggable = true
      })
    },
    // 改变图形列表事件
    changePageModel() {
      this.handleClick(this.componentFilter.outputComponent)
    },
    ListTree(list, rootid) {
      var arr = []
      list.forEach((item) => {
        if (item.id === rootid) {
          arr.push(item.id)
          const newArr = this.ListTree(list, item.pid)
          if (newArr) {
            arr.unshift(...newArr)
          }
        }
      })
      return arr
    },
    handleClick(name) {
      this.activeName = name
      localStorage.setItem('activeName', this.activeName)
      if (name != 'energy') {
        if (this.pageModel === 'grap') {
          this.getDragManageList()
        } else {
          if (this.activeName == 'space') {
            this.$nextTick(() => {
              this.$refs.space.pagination.current = 1
              this.$refs.space.getRoomCountAndArea()
              // this.$refs.space.changeType()
              this.$refs.space.active()
            })
          } else if (this.activeName == 'troubl') {
            this.$nextTick(() => {
              this.$refs.troubl.pagination.current = 1
              this.$refs.troubl.getStateStatistics()
              this.$refs.troubl.getHiddenDangerTypeAnalysis()
              this.$refs.troubl.getTableList()
            })
          } else if (this.activeName == 'risk') {
            this.$nextTick(() => {
              this.$refs.risk.pagination.current = 1
              this.$refs.risk.getRiskLevelStatistics()
              this.$refs.risk.getRiskDeptStatistics()
              this.$refs.risk.getTableList()
            })
          } else if (this.activeName == 'work') {
            this.$nextTick(() => {
              this.$refs.work.pagination.current = 1
              this.$refs.work.getReckonCount()
              this.$refs.work.getWorkOderType()
              this.$refs.work.getWorkOderTrend()
              this.$refs.work.getTableList()
            })
          } else if (this.activeName == 'waste') {
            this.$nextTick(() => {
              this.$refs.waste.pagination.current = 1
              this.$refs.waste.getWorkOderType()
              this.$refs.waste.getReckonCount()
              this.$refs.waste.getTableList()
              this.$refs.waste.getWorkOderTrend()
            })
          } else if (this.activeName == 'property') {
            this.$nextTick(() => {
              this.$refs.property.pagination.current = 1
              this.$refs.property.getReckonCount()
              this.$refs.property.getWorkOderType()
              this.$refs.property.getWorkOderTrend()
              this.$refs.property.getTableList()
            })
          }
        }
      }
      console.log(this.componentFilter)
    },
    spaceTreeListFn() {
      this.$api.spaceTreeList().then((res) => {
        this.treeLoading = false
        if (res.code == 200) {
          res.data.map((tree) => {
            tree.ssmType == 1 || tree.ssmType == 2 ? this.idArr.push(tree.id) : ''
            return this.idArr
          })
          this.spaces = res.data
          this.treeData = transData(res.data, 'id', 'pid', 'list')
          let treeLtst = JSON.parse(localStorage.getItem('treeRow'))
          this.$nextTick(function () {
            if (treeLtst) {
              this.$refs.tree.setCurrentKey(treeLtst.id)
              this.modelCode = treeLtst.id
              this.placeIds = treeLtst.id
              this.roomData.push(treeLtst.id)
              // this.treeList = treeLtst
              this.nodeClick(treeLtst)
              localStorage.removeItem('treeRow')
            } else {
              this.treeList = res.data[0]
              this.$refs.tree.setCurrentKey(res.data[0].id)
              this.modelCode = res.data[0].modelCode
              if (this.activeName == 'troubl' || this.activeName == 'risk') {
                this.placeIds = ''
              } else if (this.activeName == 'sjtyyProperty') { // 资产默认查全部
                this.placeIds = 'all'
              } else {
                this.placeIds = res.data[0].id
              }

              this.roomData.push(res.data[0].id)
            }
          })
        }
      })
    },
    filterNode(value, data) {
      if (!value) return true
      return data.ssmName.indexOf(value) !== -1
    },
    nodeClick(val) {
      if ((this.activeName == 'troubl' || this.activeName == 'risk') && val.ssmType == '1') {
        this.placeIds = ''
      } else {
        this.placeIds = val.id
      }
      this.modelCode = val.modelCode

      this.treeList = val
      localStorage.setItem('treeRow', JSON.stringify(val))
      const child = this.ListTree(this.spaces, val.pid)
      child.push(val.id)
      this.roomData = child
      if (this.activeName == 'space') {
        this.$nextTick(() => {
          this.$refs.space.pagination.current = 1
          this.$refs.space.getRoomCountAndArea()
          // this.$refs.space.changeType()
          this.$refs.space.active()
        })
      } else if (this.activeName == 'troubl') {
        this.$nextTick(() => {
          this.$refs.troubl.pagination.current = 1
          this.$refs.troubl.getStateStatistics()
          this.$refs.troubl.getHiddenDangerTypeAnalysis()
          this.$refs.troubl.getTableList()
        })
      } else if (this.activeName == 'risk') {
        this.$nextTick(() => {
          this.$refs.risk.pagination.current = 1
          this.$refs.risk.getRiskLevelStatistics()
          this.$refs.risk.getRiskDeptStatistics()
          this.$refs.risk.getTableList()
        })
      } else if (this.activeName == 'work') {
        this.$nextTick(() => {
          this.$refs.work.pagination.current = 1
          this.$refs.work.getReckonCount()
          this.$refs.work.getWorkOderType()
          this.$refs.work.getWorkOderTrend()
          this.$refs.work.getTableList()
        })
      } else if (this.activeName == 'waste') {
        this.$nextTick(() => {
          this.$refs.waste.pagination.current = 1
          this.$refs.waste.getWorkOderType()
          this.$refs.waste.getReckonCount()
          this.$refs.waste.getTableList()
          this.$refs.waste.getWorkOderTrend()
        })
      } else if (this.activeName == 'property' || this.activeName == 'sjtyyProperty') {
        this.$nextTick(() => {
          this.$refs.property.pagination.current = 1
          this.$refs.property.getReckonCount()
          this.$refs.property.getWorkOderType()
          this.$refs.property.getWorkOderTrend()
          this.$refs.property.getTableList()
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.monitor-setting {
  height: 100%;
  display: flex;

  .monitor-setting-left {
    text-align: center;
    width: 246px;
    height: 100%;
    margin-right: 16px;
    // transition: width 0.2s;
    background: #fff;
    border-radius: 4px;

    ::v-deep .box-card {
      .card-title {
        position: relative;

        .select-servive {
          position: absolute;
          right: 0;
          top: 2px;
          cursor: pointer;
          font-family: PingFangSC-Regular, 'PingFang SC';
          font-size: 14px;
          font-weight: 600;
          color: $color-primary;
          padding-left: 15px;

          i {
            font-weight: 600;
            font-size: 13px;
            margin-right: 2px;
          }
        }
      }

      .card-body {
        border-top: 1px solid #dcdfe6;
        padding-top: 10px;

        .custom-tree-node {
          flex: 1;
          display: flex;
          justify-content: space-between;
          padding-right: 10px;

          & > span {
            &:first-child {
              flex: 1;
              display: flex;
              align-items: center;
              text-overflow: ellipsis;
              white-space: nowrap;
              overflow: hidden;
              color: #121f3e;
              font-size: 14px;
              font-family: 'PingFang SC-Regular', 'PingFang SC';

              .el-tree-node__expand-icon {
                margin-right: 10px;
              }
            }

            &:last-child {
              display: flex;
              align-items: center;

              i {
                margin-left: 6px;
                cursor: pointer;
                color: #3562db;
              }
            }
          }
        }

        .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
          background-color: #d9e1f8;
        }
      }
    }
  }

  .left-width-zero {
    width: 0;
    overflow: hidden;
    margin-right: 0;
  }

  .monitor-setting-right {
    width: calc(100% - 262px);
    height: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    // overflow: auto;
    .right-heade {
      padding: 0 10px 10px !important;
      // margin-left: 16px;
      background: #fff;
      border-radius: 4px;
      display: flex;
      justify-content: space-between;

      ::v-deep .el-input {
        width: 200px;
      }

      & > div {
        // margin-right: 10px;
        margin-top: 10px;
      }
    }

    .right-content {
      // margin-left: 16px;
      // margin-top: 10px;
      // background-color: #fff;
      // padding: 10px;
      height: calc(100% - 70px);
      border-radius: 4px;
      flex: 1;

      .btns-group {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;

        & > div {
          display: flex;
        }
      }

      .table-content {
        height: calc(100% - 45px);

        ::v-deep .el-table .el-table__body-wrapper td.el-table__cell {
          border-right: 1px solid #ededf5;
        }
      }
    }

    .normal-margin {
      // margin-left: 16px;
      // margin-top: 10px;
    }

    .reduce-margin {
      // margin-left: 6px;
      margin-top: 8px;
    }
  }
}

.sino_tree_box {
  margin: 10px;
  height: calc(100% - 100px) !important;
  overflow: auto;

  .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
    background-color: #f5f6fb;
  }

  .el-tree-node__content {
    height: 38px;

    .el-tree-node__label {
      font-size: 15px;
    }
  }
}

.title_box {
  box-sizing: border-box;
  padding-left: 24px;
  height: 50px;
  width: 100%;
  line-height: 50px;
  text-align: left;
  border-bottom: 1px solid #d8dee7;

  .title-tip {
    display: inline-block;
    width: 4px;
    height: 16px;
    margin-right: 8px;
    position: relative;
    top: 2px;
    background: #5188fc;
  }

  .title_name {
    font-size: 16px;
    font-weight: bold;
  }
}

#dashExample {
  height: 100%;
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;

  :deep(.placeholder) {
    background: #e2e6eb !important;
    border-radius: 10px;
    opacity: 1;
  }
}

.data-Manage {
  height: calc(100% - 5px);
  width: 100%;
  // padding: 5px 10px 10px 10px;
  .page-container {
    width: 100%;
    margin: 0;
  }
  // overflow-y: ahuto;
}

.energy-tabs {
  display: flex;

  & > div {
    margin: auto 0;
    border-radius: 4px;
    // width: 66px;
    padding: 0 10px;
    height: 26px;
    line-height: 26px;
    text-align: center;
    background: #f6f5fa;
    color: #7f848c;
    font-size: 14px;
    margin-right: 10px;
    cursor: pointer;
  }

  .active-energy-tab {
    background: rgb(53 98 219 / 10%);
    color: #3562db;
  }
}

::v-deep .energy-content {
  .page-container {
    height: calc(100% - 0px);
    width: calc(100% - 0px);
    margin: 0;
  }
}
::-webkit-scrollbar-thumb:hover, ::-webkit-scrollbar-track:hover {
  background: transparent;
}
::-webkit-scrollbar-thumb {
  background-color: transparent;
}
</style>
