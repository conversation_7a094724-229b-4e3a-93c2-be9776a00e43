<template>
  <RunCalendar :projectCode="projectCode" />
</template>

<script>
import RunCalendar from '../airAndLightingCom/runCalendar.vue'
import { monitorTypeList } from '@/util/dict.js'
export default {
  components: {
    RunCalendar
  },
  data() {
    return {
      projectCode: monitorTypeList.find((item) => item.projectName == '照明监测').projectCode
    }
  },
  mounted() {
    
  },

  methods: {
    
  }
}
</script>
<style lang="scss" scoped>

</style>
