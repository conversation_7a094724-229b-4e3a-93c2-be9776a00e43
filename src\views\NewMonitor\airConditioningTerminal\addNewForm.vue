<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="addAlarmConfig-content-title"><i class="el-icon-arrow-left" @click="goBack"></i><span
          style="margin-left: 10px"><span v-if="!$route.query.activeType">{{ $route.query.id ? '编辑' : '新建'
          }}</span>设备</span></div>
      <div class="content_box">
        <el-form ref="formInline" :model="formInline" :inline="true" class="form-inline" label-width="120px"
          :rules="rules">
          <el-form-item label="资产编码：" prop="assetsCode">
            <el-input v-model.trim="formInline.assetsCode" maxlength="30" show-word-limit :readonly="readonly"
              type="text" style="width:240px;" placeholder="资产编码" :disabled="activeType == 'details'"></el-input>
            <el-button v-if="activeType != 'details'" class="form-btn-btn" type="primary"
              @click="generate">自动生成</el-button>
          </el-form-item>
          <el-form-item label="资产名称：" prop="assetsName">
            <el-input v-model.trim="formInline.assetsName" maxlength="40" show-word-limit :readonly="readonly"
              type="text" placeholder="资产名称" :disabled="activeType == 'details'"></el-input>
          </el-form-item>

          <el-form-item label="通用名：" prop="commonName">
            <el-input v-model.trim="formInline.commonName" maxlength="25" show-word-limit :readonly="readonly"
              type="text" placeholder="通用名" :disabled="activeType == 'details'"></el-input>
          </el-form-item>
          <el-form-item label="规格型号：" prop="deviceModel">
            <el-input v-model.trim="formInline.deviceModel" maxlength="64" show-word-limit :readonly="readonly"
              type="text" placeholder="规格型号" :disabled="activeType == 'details'"></el-input>
          </el-form-item>
          <el-form-item label="计量单位：">
            <el-select v-model.trim="formInline.deviceUnitOfMeasurement" :readonly="readonly" filterable
              placeholder="计量单位" :disabled="activeType == 'details'">
              <el-option v-for="item in companyList" :key="item.dictValue" :label="item.dictName"
                :value="item.dictValue">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="SN/出厂编码:" prop="factoryCode">
            <el-input v-model.trim="formInline.factoryCode" maxlength="64" show-word-limit type="text"
              :readonly="readonly" placeholder="SN/出厂编码" :disabled="activeType == 'details'"></el-input>
          </el-form-item>
          <el-form-item label="系统类型：" prop="deviceTypeCode" v-if="formInline.equipAttr === '2'">
            <el-select v-model.trim="formInline.deviceTypeCode" :readonly="readonly" filterable placeholder="系统类型"
              :disabled="activeType == 'details'">
              <el-option v-for="item in systemCodeList" :key="item.dictionaryDetailsCode"
                :label="item.dictionaryDetailsName" :value="item.dictionaryDetailsCode">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="所属产品：" prop="product" v-if="formInline.equipAttr === '1'">
            <el-select v-model.trim="formInline.product" :readonly="readonly" filterable placeholder="所属产品"
              :disabled="activeType == 'details'" @change="productChange">
              <el-option v-for="item in productCodeList" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="设备属性：" prop="equipAttr">
            <el-radio-group v-model="formInline.equipAttr" :disabled="activeType == 'details'"
              @change="formInline.deviceTypeCode = ''">
              <el-radio label="1">物联设备</el-radio>
              <el-radio label="2">被监测设备</el-radio>
            </el-radio-group>
          </el-form-item>
          <br />
          <el-form-item label="RFID：" prop="rfid">
            <el-input v-model.trim="formInline.rfid" :readonly="readonly" type="text" placeholder="RFID" maxlength="64"
              show-word-limit :disabled="activeType == 'details'"></el-input>
          </el-form-item>
          <el-form-item label="模型编码：" prop="modelCode">
            <el-input v-model.trim="formInline.modelCode" maxlength="64" show-word-limit :readonly="readonly"
              type="text" placeholder="模型编码" :disabled="activeType == 'details'"></el-input>
          </el-form-item>
          <el-form-item label="NFC：" prop="nfc">
            <el-input v-model.trim="formInline.nfc" maxlength="64" show-word-limit :readonly="readonly" type="text"
              placeholder="NFC" :disabled="activeType == 'details'"></el-input>
          </el-form-item>
          <el-form-item label="归口部门：">
            <el-select v-model.trim="formInline.relevantDept" :readonly="readonly" filterable placeholder="归口部门"
              :disabled="activeType == 'details'">
              <el-option v-for="item in relevantList" :key="item.id" :label="item.deptName" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="所在空间：" prop="spaceLocation">
            <el-cascader v-model="formInline.spaceLocation" :props="riskPropsType" :options="regionCodeList"
              :collapse-tags="true" placeholder="请选择所在空间" class="cascaderWid" :show-all-levels="true"
              @change="hangdleChange" :disabled="activeType == 'details'"></el-cascader>
          </el-form-item>
          <br />
          <el-form-item label="上传图片：" prop="pictureUrl">
            <template v-if="activeType == 'details'">
              <el-image v-if="fileList.length > 0" style="width: 100px; height: 100px" :src="fileList[0].url"
                :preview-src-list="fileList.map(i => i.url)">
              </el-image>
              <span v-else>暂无</span>
            </template>
            <template v-else>
              <el-upload action="" list-type="picture-card" :file-list="fileList" :on-change="fileChange"
                :on-preview="handlePictureCardPreview" :on-remove="deletImg" :http-request="handleUpload"
                :before-upload="beforeUpload" :class="{ hide: hideUpload }" accept=".jpg,.jpeg,.png" :limit="1">
                <i class="el-icon-plus"></i>
                <div slot="tip" class="el-upload__tip">只能上传png、jpg、jpeg、bmp、gif、tif格式，且不超过10MB</div>
              </el-upload>
            </template>
            <el-dialog :visible.sync="imgVisible" :append-to-body="true">
              <img width="100%" :src="formInline.pictureUrl" alt="" />
            </el-dialog>
          </el-form-item>
          <br />
          <el-form-item label="X坐标：" prop="modelPositionX">
            <el-input v-model.trim="formInline.modelPositionX" :readonly="readonly" type="number" placeholder="X坐标"
              show-word-limit maxlength="20" :disabled="activeType == 'details'"></el-input>
          </el-form-item>
          <el-form-item label="Y坐标：" prop="modelPositionY">
            <el-input v-model.trim="formInline.modelPositionY" :readonly="readonly" type="number" placeholder="Y坐标"
              show-word-limit maxlength="20" :disabled="activeType == 'details'"></el-input>
          </el-form-item>
          <el-form-item label="Z坐标：" prop="modelPositionZ">
            <el-input v-model.trim="formInline.modelPositionZ" :readonly="readonly" type="number" placeholder="Z坐标"
              show-word-limit maxlength="20" :disabled="activeType == 'details'"></el-input>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="CloseEvent">关闭</el-button>
      <el-button v-if="activeType != 'details'" type="primary" :disabled="isSubmitting"
        @click="submitForm()">保存</el-button>
    </div>
  </PageContainer>
</template>

<script>
import { transData } from '@/util'
import axios from 'axios'
export default {
  name: 'addNewForm',
  beforeRouteEnter(to, from, next) {
    if (to.meta.title) {
      const typeList = {
        add: '新建设备',
        edit: '编辑设备',
        details: '设备详情'
      }
      to.meta.title = typeList[to.query.type] ?? '设备详情'
    }
    next(vm => {
      // 根据需要初始化数据
      vm.initializeData();
    });
  },
  data() {
    return {
      formInline: {
        id: "",
        assetsName: '', //资产名称
        assetsCode: '', //资产编码
        commonName: '',//通用名
        deviceModel: '', //规格型号
        deviceUnitOfMeasurement: '', //计量单位
        factoryCode: '',    //SN/出厂编码
        spaceLocation: [], //所在空间
        equipAttr: this.$route.query.equipAttr, // 设备属性
        dictionaryDetailsParentCode: this.$route.query.systemCode,//系统类别编码
        product: '',  //产品id
        deviceTypeCode: '', //品类编码
        rfid: '', // RFID
        modelCode: '',// 模型编码
        nfc: '',// nfc
        relevantDept: '',// 归口部门
        pictureUrl: '',//图片地址
        modelPositionX: null, // X坐标
        modelPositionY: null, // Y坐标
        modelPositionZ: null, // Z坐标
      },
      riskPropsType: {
        children: 'children',
        label: 'ssmName',
        value: 'id',
        checkStrictly: true,
        multiple: false
      },
      imgVisible: false,
      treeData: [],
      query: '', // 路由传递参数
      readonly: false, // 是否只读
      fileList: [],//上传图片
      dockingMethodArr: [], //  对接方式list
      limitSelectLength: 4, // 限制长度
      rules: {
        assetsName: [{ required: true, message: '请输入资产名称', trigger: 'blur' }],
        assetsCode: [{ required: true, message: '请输入资产编码', trigger: 'change' }],
        deviceUnitOfMeasurement: [{ required: true, message: '请选择计量单位', trigger: 'change' }],
        factoryCode: [{ required: true, message: '请输入SN/出厂编码', trigger: 'blur' }],
        deviceTypeCode: [{ required: true, message: '请选择系统类型', trigger: 'change' }],
        product: [{ required: true, message: '请选择产品类型', trigger: 'change' }],
        equipAttr: [{ required: true, message: '请选择设备属性', trigger: 'change' }],
        rfid: [{ required: false, message: '请输入RFID', trigger: 'blur' }, { validator: this.validateInput, trigger: 'blur' }],
        nfc: [{ required: false, message: '请输入NFC', trigger: 'blur' }, { validator: this.validateInput, trigger: 'blur' }],
        modelCode: [{ required: false, message: '请输入模型编码', trigger: 'blur' }, { validator: this.validateInput1, trigger: 'blur' }],
      },
      regionCodeList: [], // 所属空间列表
      regionCodeAll: [], // 所属空间全部
      companyList: [], // 计量单位列表
      systemCodeList: [], // 系统类型
      productCodeList: [], // 所属产品
      systemAllList: [], // 系统类别全部
      relevantList: [],//归口部门列表
      hideUpload: false,
      activeType: "",
      isSubmitting: false,
    }
  },
  watch: {
    $route: {
      handler: function (route) {
        this.$nextTick(() => {
          if (route.query.id && this.$route.query.id) {
            this.getDetails(route.query.id)
          }
        })
      },
    }
  },
  created() {
    this.formInline.pictureUrl = ''
    this.query = this.$route.query
  },
  mounted() {
    this.init()
    this.$nextTick(() => {
      if (this.$route.query.id) {
        this.getDetails(this.$route.query.id)
      }
    })
    if (this.query.type == 'details') {
      this.readonly = true
    }
  },
  methods: {
    validateInput(rule, value, callback) {
      const regex = /^[a-zA-Z0-9]*$/; // 正则表达式：只允许英文和数字
      if (value && !regex.test(value)) {
        callback(new Error('只能输入英文和数字'));
      } else {
        callback(); // 验证通过
      }
    },
    validateInput1(rule, value, callback) {
      const regex = /^[a-zA-Z0-9!@#$%^&*()_+={}\[\]:;"'<>,.?\/\\|-]*$/;// 正则表达式：只允许英文和数字和特殊字符
      if (value && !regex.test(value)) {
        callback(new Error('只能输入英文数字和特殊字符'));
      } else {
        callback(); // 验证通过
      }
    },
    initializeData() {
      this.formInline = {
        id: "",
        assetsName: '', //资产名称
        assetsCode: '', //资产编码
        commonName: '',//通用名
        deviceModel: '', //规格型号
        deviceUnitOfMeasurement: '', //计量单位
        factoryCode: '',    //SN/出厂编码
        spaceLocation: [], //所在空间
        equipAttr: this.$route.query.equipAttr, // 设备属性
        dictionaryDetailsParentCode: this.$route.query.systemCode,//系统类别编码
        product: '',  //产品id
        deviceTypeCode: '', //品类编码
        rfid: '', // RFID
        modelCode: '',// 模型编码
        nfc: '',// nfc
        relevantDept: '',// 归口部门
        pictureUrl: '',//图片地址
        modelPositionX: null, // X坐标
        modelPositionY: null, // Y坐标
        modelPositionZ: null, // Z坐标
      }
      this.fileList = []
      this.$refs['formInline'].resetFields();
    },
    // 返回
    goBack() {
      this.CloseEvent()
    },
    init() {
      this.activeType = this.$route.query.activeType
      this.systemType()//系统类型
      this.getDept()//系统类型
      this.productType()//所属产品
      this.getSelectByList('26', 'companyList') // 计量单位
      // 所在空间
      this.$api.getSpaceTreeList().then((res) => {
        this.isTreeData = true
        if (res.code == '200') {
          this.regionCodeList = transData(res.data, 'id', 'pid', 'children')
          this.regionCodeAll = res.data
        }
      })
    },
    //获取归口部门
    getDept() {
      this.relevantList = []
      this.$api.getSelectedDeptNew().then((res) => {
        if (res.code == '200') {
          this.relevantList = res.data
        }
      })
    },
    // 系统类型
    systemType() {
      this.systemCodeList = []
      let data = {
        dictionaryCode: this.$route.query.systemCode
      }
      this.$api.getQueryRootSubCategoryDetails(data).then((res) => {
        if (res.code == '200') {
          this.systemCodeList = res.data
        }
      })
    },
    // 所属产品
    productType() {
      this.productCodeList = []
      let data = {
        where: "state=1%20and%20systemTypeCode=" + this.$route.query.systemCode
      }
      this.$api.getDeviceProduct(data).then((res) => {
        if (res.status == '200') {
          this.productCodeList = res.result
        }
      })
    },
    productChange(val) {
      this.formInline.deviceTypeCode = this.productCodeList.find((item) => {
        return item.id === val
      }).deviceTypeCode
    },
    // 查询资产小类
    onAssetCategory(id) {
      this.subcategoryList = []
      let data = {
        current: 1,
        size: 1000000,
        dictTypeId: '27',
        pids: id
      }
      this.$api.selectByPage(data).then((res) => {
        if (res.code == '200') {
          this.subcategoryList = transData(res.data.records, 'id', 'pid', 'children')
          this.subcategoryAll = res.data.records
          ++this.assetIndex
        }
      })
    },
    // 选择资产小类
    handleAssetClick(type) {
      const names = []
      type.forEach((i) => {
        this.subcategoryAll.find((j) => {
          if (i == j.id) {
            names.push(j.dictName)
          }
        })
      })
      this.assetSubcategoryName = names.join(',')
    },
    // 选择下拉树 数据
    handleSystemClick(type) {
      const names = []
      type.forEach((i) => {
        this.systemAllList.find((j) => {
          if (i == j.id) {
            names.push(j.baseName)
          }
        })
      })
      this.systemCategoryName = names.join(',')
    },
    // 查询资产字典
    getSelectByList(str, getArr) {
      this.$api.selectByListAsset({ dictTypeId: str }).then((res) => {
        if (res.code == '200') {
          this[getArr] = res.data
        }
      })
    },
    // 所在区域选择
    hangdleChange(type) {
      const names = []
      type.forEach((i) => {
        this.regionCodeAll.find((j) => {
          if (i == j.id) {
            names.push(j.ssmName)
          }
        })
      })
      this.regionNameList = names.join('>')
    },
    // 根据最后一级选中的值回显整个选中的数组  即通过最后一级的id 获取整个路径的数组
    getPath(treeData, targetId) {
      let path = []
      let toggle = false
      for (let i = 0; i < treeData.length; i++) {
        let item = treeData[i]
        path.push(item.id)
        if (item.id === targetId) {
          toggle = true
          break
        } else {
          if (item.children) {
            toggle = this.findPath(item.children, targetId, path, toggle)
            if (!toggle) {
              path.pop()
            } else {
              break
            }
          } else {
            path.pop()
          }
        }
      }

      if (toggle) {
        return path
      } else {
        return []
      }
    },
    findPath(nodes, targetId, path, toggle) {
      for (let i = 0; i < nodes.length; i++) {
        let item = nodes[i]
        path.push(item.id)
        if (item.id === targetId) {
          return true
        } else {
          if (item.children) {
            toggle = this.findPath(item.children, targetId, path, toggle)
            if (toggle) {
              return toggle
            } else {
              path.pop()
            }
          } else {
            path.pop()
          }
        }
      }
      return toggle
    },
    // 获取详情
    getDetails(id) {
      this.$api.getQueryByIdDisplay({ id: id }).then((res) => {
        if (res.code == '200') {
          // this.formInline = res.data
          const keysToMap = Object.keys(this.formInline);
          keysToMap.forEach(key => {
            if (res.data.hasOwnProperty(key)) {
              this.formInline[key] = res.data[key] !== undefined ? res.data[key] : this.formInline[key];
            }
          });
          // 处理图片回显
          if (res.data.pictureUrl) {
            this.fileList = [];
            this.fileList = [
              {
                url: this.$tools.imgUrlTranslation(res.data.pictureUrl)
              }
            ]
          }
          this.hideUpload = !!res.data.pictureUrl
        }
      })
    },
    submitForm() {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          this.isSubmitting = true;
          let data = {
            ...this.formInline,
            spaceLocation: typeof this.formInline.spaceLocation === 'string'
              ? this.formInline.spaceLocation
              : Array.isArray(this.formInline.spaceLocation) && this.formInline.spaceLocation.length > 0
                ? this.formInline.spaceLocation[this.formInline.spaceLocation.length - 1]
                : '',//空间id
            modelPositionX: Number(this.formInline.modelPositionX),
            modelPositionY: Number(this.formInline.modelPositionY),
            modelPositionZ: Number(this.formInline.modelPositionZ),
          }
          this.$api.addOperationalMonitoring(data).then((res) => {
            this.isSubmitting = false;
            if (res.code == '200') {
              this.$message.success('保存成功')
              this.$router.go(-1)
              this.$refs['formInline'].resetFields();
              this.deletImg(this.fileList, this.fileList)
              // 强制更新组件
              this.$forceUpdate();
            } else {
              this.$message.error(res.message)
            }
          })
        }
      })
    },

    CloseEvent() {
      this.$router.go(-1)
      this.fileList = []
      this.deletImg(this.fileList, this.fileList)
      this.$refs['formInline'].resetFields();
      // 强制更新组件
      this.$forceUpdate();
    },
    // 生成资产编码
    generate() {
      this.$api.getQueryGenAssetsCode().then((res) => {
        if (res.code === '200') {
          this.formInline.assetsCode = res.data
        }
      })
    },
    // 新增编辑上传
    beforeUpload(file) {
      let fileName = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()
      if (fileName != 'png' && fileName != 'jpg' && fileName != 'bmp' && fileName != 'tif' && fileName != 'gif' && fileName != 'jpeg') {
        this.$message({
          message: '上传图片只能是png、jpg、jpeg、bmp、gif、tif格式',
          type: 'warning'
        })
        return false
      }
      let sizes = 0
      this.fileList.forEach((i) => {
        sizes += i.raw.size
      })
      if (sizes / 1024 > 10000) {
        this.$message({
          type: 'warning',
          message: '上传文件大小不能超过10MB'
        })
        return false
      }
    },
    // 图片上传
    handleUpload() {
      this.fileList.forEach((i) => {
        return (i.status = 'uploading')
      })
      const urlData = new FormData()
      this.fileList.forEach((item) => {
        urlData.append('file', item.raw)
        urlData.append('platform', 1)
      })
      axios({
        method: 'post',
        url: __PATH.VUE_MONITOR_API + 'fileController/upload',
        data: urlData,
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      })
        .then((res) => {
          this.hideUpload = true
          this.fileList.forEach((i) => {
            return (i.status = 'done')
          })
          this.formInline.pictureUrl = res.data.data
        })
        .catch(() => {
          this.$message.error(res.data.message)
        })
    },
    deletImg(file, fileList) {
      this.hideUpload = false
      this.formInline.pictureUrl = ''
      this.formInline.pictureUrl = ''
      this.fileList = fileList
    },
    handlePictureCardPreview(file) {
      this.formInline.pictureUrl = file.url
      this.imgVisible = true
    },
    fileChange(file, fileList) {
      this.fileList = fileList
    }
  }
}
</script>

<style lang="scss" scoped>
.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
  padding: 10px;
}

.addAlarmConfig-content-title {
  border-bottom: 1px solid #e4e7ed;
  padding: 13px 24px;
  color: #333333;

  .el-icon-arrow-left {
    cursor: pointer;
  }
}

.content_box {
  height: calc(100% - 30px);
  margin-top: 10px;
  padding: 20px 25px 25px;
  background: #fff;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.form-btn-btn {
  padding: 8px 3px;
  margin: -1px;
  border-radius: 0;
  border: 1px solid #DCDFE6;
  background: #F6F5FA;
  color: #3562db;
}

.camera-tag {
  background: #f6f5fa;
  border-radius: 4px;
  font-size: 14px;
  font-family: 'PingFang SC-Regular', 'PingFang SC';
  font-weight: 400;
  color: #121f3e;
  border: none;
  margin-right: 8px;

  ::v-deep .el-tag__close {
    color: #121f3e;

    &:hover {
      color: #fff;
      background-color: #3562db;
    }
  }
}

.hide {
  ::v-deep .el-upload--picture-card {
    display: none;
  }
}

.form-inline {

  .el-input,
  .el-select,
  .el-cascader {
    width: 300px;
  }
}

.form-inline .width_lengthen {
  width: 300px;
}

.form-inline .cascaderWid {
  width: 730px;
}

.detailClass ::v-deep .el-input__inner {
  border: none !important;
}

.detailClass ::v-deep .el-input__suffix-inner {
  display: none !important;
}

.detailClass ::v-deep .el-input-group__append {
  display: none !important;
}
</style>
