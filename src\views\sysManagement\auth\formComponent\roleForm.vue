<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <el-form ref="formInline" :model="formInline" :rules="rules">
        <ContentCard title="新增角色">
          <div slot="content" class="footer-role">
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="7">
                <el-form-item label="角色名称" prop="roleName" label-width="100px">
                  <el-input v-model="formInline.roleName" :disabled="updatedType == 'detail'" placeholder="请输入角色名称"></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="7">
                <el-form-item label="角色编码" prop="roleCode" label-width="100px">
                  <el-input v-model="formInline.roleCode" :disabled="updatedType == 'detail'" placeholder="请输入角色编码"></el-input>
                  <!-- onkeyup="this.value=this.value.replace(/[^u4e00-u9fa5w]/g,'')" -->
                </el-form-item>
              </el-col>
              <el-col :md="7">
                <el-form-item label="角色排序" prop="roleSort" label-width="100px">
                  <el-input
                    v-model.number="formInline.roleSort"
                    :disabled="updatedType == 'detail'"
                    onkeyup="value=Number(value.replace(/[^\d]/g,''));"
                    placeholder="请输入角色排序"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="21">
                <el-form-item label="备注" prop="remark" label-width="100px">
                  <el-input
                    v-model="formInline.remark"
                    :disabled="updatedType == 'detail'"
                    type="textarea"
                    :autosize="{ minRows: 4 }"
                    maxlength="200"
                    show-word-limit
                    placeholder="请输入备注"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </ContentCard>
        <ContentCard title="菜单权限">
          <div slot="content" class="footer-menu">
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="7" class="menu-col">
                <div class="menu-col-title">PC权限</div>
                <treeCard
                  ref="menuPCdata"
                  refPname="menuPCdata"
                  :defaultExpandAll="true"
                  :checkStrictly="true"
                  :treeData="menuTreeData"
                  :defaultExpandedKeys="menuDefaultExpandedKeys"
                  :defaultCheckedKeys="menuDefaultCheckedKeys"
                  :defaultProps="menuDefaultProps"
                  nodeKey="menuId"
                  @nodeCheckChange="menuNodeCheckChange"
                ></treeCard>
              </el-col>
              <el-col :md="7" class="menu-col">
                <div class="menu-col-title">APP权限</div>
                <treeCard
                  ref="menuAPPdata"
                  refPname="menuAPPdata"
                  :defaultExpandAll="true"
                  :checkStrictly="true"
                  :treeData="appMenuTreeData"
                  :defaultExpandedKeys="[]"
                  :defaultCheckedKeys="appMenuDefaultCheckedKeys"
                  :defaultProps="menuDefaultProps"
                  nodeKey="menuId"
                  @nodeCheckChange="menuNodeCheckChange"
                ></treeCard>
              </el-col>
            </el-row>
          </div>
        </ContentCard>
        <ContentCard title="数据权限">
          <div slot="content" class="footer-data">
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="21">
                <el-form-item label="数据权限" prop="dataScope" label-width="100px">
                  <el-radio-group v-model="formInline.dataScope" :disabled="updatedType == 'detail'" @input="roleScopeChange">
                    <el-radio :label="1">全部数据</el-radio>
                    <el-radio :label="2">本部门数据</el-radio>
                    <el-radio :label="3">自定义</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row v-if="deptTreeShow" :gutter="24" style="margin: 0">
              <el-col :md="7" class="menu-col">
                <div class="menu-col-title">数据范围</div>
                <treeCard
                  ref="deptTree"
                  refPname="deptTree"
                  :treeData="deptTreeList"
                  :defaultExpandedKeys="deptDefaultExpandedKeys"
                  :defaultCheckedKeys="deptDefaultCheckedKeys"
                  :defaultProps="deptDefaultProps"
                ></treeCard>
              </el-col>
            </el-row>
          </div>
        </ContentCard>
      </el-form>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">关闭</el-button>
      <el-button v-if="$route.query?.type != 'detail'" type="primary" @click="submitForm()">保存</el-button>
    </div>
  </PageContainer>
</template>
<script>
import treeCard from './treeCard.vue'
import { transData, treeToList } from '@/util'
export default {
  name: 'roleForm',
  components: {
    treeCard
  },
  async beforeRouteLeave(to, from, next) {
    if (!['roleManagement'].includes(to.name)) {
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  // 路由前置守卫动态修改meta中的title
  beforeRouteEnter(to, from, next) {
    if (to.meta.title) {
      console.log(to.query.type)
      const typeList = {
        add: '新增角色',
        edit: '编辑角色',
        detail: '角色详情'
      }
      to.meta.title = typeList[to.query.type] ?? '角色详情'
    }
    next()
  },
  data() {
    return {
      updatedType: '',
      // updatedType: this.$route.query.type,
      deptTreeShow: false,
      formInline: {
        roleName: '',
        roleCode: '',
        roleSort: 0,
        remark: '',
        dataScope: 2
      },
      // rules: {},
      rules: {
        // roleName: [{ required: true, validator: checkSpecialChar, trigger: 'blur' }],
        // roleCode: [{ required: true, validator: checkSpecialChar, trigger: 'blur' }]
      },
      // 菜单树
      menuTreeData: [],
      appMenuTreeData: [],
      menuDefaultExpandedKeys: [],
      menuDefaultCheckedKeys: [],
      appMenuDefaultCheckedKeys: [],
      menuDefaultProps: {
        children: 'children',
        label: 'menuName'
      },
      // 部门树
      deptTreeList: [],
      deptDefaultExpandedKeys: [],
      deptDefaultCheckedKeys: [],
      deptDefaultProps: {
        children: 'children',
        label: 'deptName'
      }
    }
  },
  activated() {
    this.init()
  },
  created() {
    if (!this.$store.state.keepAlive.list.includes('departmentManage')) {
      this.init()
    }
  },
  methods: {
    init() {
      const checkSpecialChar = (rule, value, callback) => {
        const rexg = /[^\a-\z\A-\Z0-9\u4E00-\u9FA5]/g
        if (!value) {
          callback(new Error('请输入信息'))
        } else if (rexg.test(value)) {
          callback(new Error('请勿输入特殊字符'))
        } else {
          callback()
        }
      }
      Object.assign(this.$data, this.$options.data())
      this.updatedType = this.$route.query.type
      this.$set(this.$data, 'rules', {
        roleName: [{ required: true, validator: checkSpecialChar, trigger: 'blur' }],
        roleCode: [{ required: true, validator: checkSpecialChar, trigger: 'blur' }]
      })
      this.$nextTick(() => {
        this.$refs.formInline.resetFields()
      })
      this.getDeptTreeList()
      this.getMenuTreeList()
      this.getAppMenuTreeList()
      if (Object.hasOwn(this.$route.query, 'id')) {
        this.getRoleInfo(this.$route.query.id)
      }
    },
    // 树选中返回数据
    menuNodeCheckChange([node]) {
      if (node.checked) {
        // 如果当前是选中checkbox,则递归设置父节点和父父节点++选中
        this.setParentNode(node)
        this.setChildenNode(node, true)
      } else {
        // 当前是取消选中,将所有子节点都取消选中
        this.setChildenNode(node)
      }
    },
    // 树选中返回数据
    // menuNodeCheckChange(data) {
    //   const node = this.$refs.menuPCdata?.getNode(data.menuId)
    //   if (node.checked) {
    //     // 如果当前是选中checkbox,则递归设置父节点和父父节点++选中
    //     this.setParentNode(node)
    //   } else {
    //     // 当前是取消选中,将所有子节点都取消选中
    //     this.setChildenNode(node)
    //   }
    // },
    setParentNode(node) {
      if (node.parent) {
        for (const key in node) {
          if (key === 'parent') {
            node[key].checked = true
            this.setParentNode(node[key])
          }
        }
      }
    },
    setChildenNode(node, flag = false) {
      let len = node.childNodes.length
      for (let i = 0; i < len; i++) {
        node.childNodes[i].checked = flag
        this.setChildenNode(node.childNodes[i], flag)
      }
    },
    getRoleInfo(id) {
      this.$api.getSysRoleInfoByCode({ roleId: id }).then((res) => {
        if (res.code == 200) {
          const roleInfo = res.data.roleInfo
          // 遍历roleInfo 给formInline赋值
          for (const key in roleInfo) {
            if (Object.hasOwnProperty.call(this.formInline, key)) {
              const element = roleInfo[key]
              this.formInline[key] = element
            }
          }
          this.deptTreeShow = res.data.roleInfo.dataScope === 3
          this.deptDefaultCheckedKeys = res.data.deptId?.split(',') ?? []
          this.menuDefaultCheckedKeys = res.data.menuId?.split(',') ?? []
          this.appMenuDefaultCheckedKeys = res.data.menuIdApp?.split(',') ?? []
          // this.$refs.menuPCdata.$refs.tree.setCheckedKeys(res.data.menuId?.split(',') ?? [], true)
        }
      })
    },
    // 切换数据权限
    roleScopeChange(val) {
      this.deptTreeShow = val === 3
    },
    // 获取App菜单列表
    getAppMenuTreeList() {
      const params = {
        state: 0,
        userId: this.$store.state.user.userInfo.userId
      }
      this.$api.SysAppMenu(params).then((res) => {
        if (this.updatedType === 'detail') {
          const menuList = treeToList(res.data) ?? []
          menuList.map((e) => (e.disabled = true))
          this.appMenuTreeData = transData(menuList, 'menuId', 'parentId', 'children')
        } else {
          this.appMenuTreeData = res.data ?? []
        }
      })
    },
    // 获取菜单列表
    getMenuTreeList() {
      const params = {
        state: 0,
        userId: this.$store.state.user.userInfo.userId
      }
      this.$api.getMenuTreeData(params).then((res) => {
        if (res.code === '200') {
          if (this.updatedType === 'detail') {
            const menuList = treeToList(res.data) ?? []
            menuList.map((e) => (e.disabled = true))
            this.menuTreeData = transData(menuList, 'menuId', 'parentId', 'children')
          } else {
            this.menuTreeData = res.data ?? []
          }
        }
      })
    },
    // 获取科室树数据
    getDeptTreeList() {
      // this.$api.getSelectedDept({}, __PATH.USER_CODE).then((res) => {
      this.$api.getSelectedDept({}).then((res) => {
        if (res.code == 200) {
          const deptList = res.data
          if (this.updatedType === 'detail') {
            deptList.map((e) => (e.disabled = true))
          }
          this.deptTreeList = transData(deptList, 'id', 'pid', 'children')
        }
      })
    },
    submitForm() {
      const menuTreeList = this.$refs.menuPCdata.getCheckedNodes(false, false)
      const AppMenuTreeList = this.$refs.menuAPPdata.getCheckedNodes()
      const deptTreeList = this.$refs.deptTree?.getCheckedNodes() ?? []
      this.$refs.formInline.validate((valid) => {
        if (valid) {
          const params = {
            ...this.formInline
          }
          // 数据权限自定义时填充科室id
          if (params.dataScope === 3) {
            params.deptId = deptTreeList?.map((item) => item.id).join(',') ?? ''
          } else {
            params.deptId = ''
          }
          params.menuId = menuTreeList.map((item) => item.menuId).join(',') ?? ''
          params.menuIdApp = AppMenuTreeList.map((item) => item.menuId).join(',') ?? ''
          let submitPromise = null
          let message = ''
          // 新增或修改
          if (this.updatedType === 'add') {
            submitPromise = this.$api.insertRoleInfo(params, { 'operation-type': 1 })
            message = '新增成功'
          } else if (this.updatedType === 'edit') {
            Object.assign(params, { id: this.$route.query.id })
            submitPromise = this.$api.updateRoleInfo(params, { 'operation-type': 2, 'operation-id': params.id, 'operation-name': params.roleName })
            message = '修改成功'
          }
          submitPromise.then((res) => {
            if (res.code == 200) {
              this.$message({
                message: message,
                type: 'success'
              })
              this.$router.push({ name: 'roleManagement' })
            } else {
              this.$message({
                message: res.msg,
                type: 'error'
              })
            }
          })
        } else {
          return false
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
  padding: 10px;
  overflow-y: auto;
  .footer-menu,
  .footer-data {
    .menu-col {
      display: flex;
      .menu-col-title {
        font-size: 14px;
        font-family: 'PingFang SC-Regular', 'PingFang SC';
        color: #121f3e;
        width: 100px;
        padding-right: 12px;
        text-align: right;
        // margin-top: 10px;
      }
    }
  }
}
</style>
