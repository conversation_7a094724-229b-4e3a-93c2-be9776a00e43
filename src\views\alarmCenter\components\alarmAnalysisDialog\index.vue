<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-06-02 15:17:04
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2024-09-07 11:20:08
 * @FilePath: \ihcrs_pc\src\views\alarmCenter\components\alarmAnalysisDialog\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-dialog v-if="visible" v-dialogDrag :modal="false" :close-on-click-modal="false" title="报警新增趋势" width="60%"
    append-to-body :visible="visible" custom-class="model-dialog" :before-close="closeDialog">
    <div class="dialog-content">
      <div class="content-heade">
        <div class="filterClass">
          <div v-if="searchForm.hourDayOrMouth==1">
            <el-date-picker v-model="searchForm.dataRange" type="daterange" range-separator="至" start-placeholder="开始日期"
              unlink-panels end-placeholder="结束日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd" :clearable="false"
              @change="changeDate()" />
          </div>
          <div v-if="searchForm.hourDayOrMouth==2" class="datePickerClass">
            <el-date-picker v-model="startDate" type="month" placeholder="选择月" format="yyyy-MM" value-format="yyyy-MM"
              @change="changeDate()">
            </el-date-picker>
            至
            <el-date-picker v-model="endDate" type="month" placeholder="选择月" format="yyyy-MM" value-format="yyyy-MM"
              @change="changeDate()">
            </el-date-picker>
          </div>
          <div v-if="searchForm.hourDayOrMouth==3" class="datePickerClass">
            <el-date-picker v-model="startDate" type="year" placeholder="选择年" format="yyyy" value-format="yyyy"
              @change="changeDate()">
            </el-date-picker>
            至
            <el-date-picker v-model="endDate" type="year" placeholder="选择年" format="yyyy" value-format="yyyy"
              @change="changeDate()">
            </el-date-picker>
          </div>
          <!-- <el-date-picker v-model="searchForm.dataRange" class="style-interval" type="daterange" unlink-panels
          range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" format="yyyy-MM-dd"
          value-format="yyyy-MM-dd" :clearable="false" @change="changeDate()" /> -->
          <el-select v-model="searchForm.projectCode" class="style-interval" filterable clearable placeholder="请选择报警来源"
            @change="getDictionaryList()">
            <el-option v-for="item in alarmSourceOptions" :key="item.projectCode" :label="item.projectName"
              :value="item.projectCode"> </el-option>
          </el-select>
          <el-select v-model="searchForm.entityTypeId" class="style-interval" filterable clearable placeholder="请选择设备类型"
            @change="selectIncidentParam()">
            <el-option v-for="item in entityTypeList" :key="item.entityTypeId" :label="item.entityTypeName"
              :value="item.entityTypeId"> </el-option>
          </el-select>
        </div>
        <div class="control-btn-header">
          <el-button class="btn-item" :class="{ 'btn-active': searchForm.hourDayOrMouth == 1 }" plain
            @click="timeTypeChange(1)">按日</el-button>
          <el-button class="btn-item" :class="{ 'btn-active': searchForm.hourDayOrMouth == 2 }" plain
            @click="timeTypeChange(2)">按月</el-button>
          <el-button class="btn-item" :class="{ 'btn-active': searchForm.hourDayOrMouth == 3 }" plain
            @click="timeTypeChange(3)">按年</el-button>
        </div>
        <div class="incident-checkbox">
          <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" style="margin-right: 30px"
            @change="handleCheckAllChange">全选</el-checkbox>
          <el-checkbox-group v-model="searchForm.checkedincidents" @change="handleCheckedCitiesChange">
            <el-checkbox v-for="item in incidentList" :key="item.incidentParam"
              :label="item.incidentParam">{{ item.incidentName }}</el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
      <div class="content-aside">
        <echarts ref="alarmAnalysisTrend" domId="alarmAnalysisTrend" width="100%" height="100%" />
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button plain type="primary" @click="closeDialog">关 闭</el-button>
    </span>
  </el-dialog>
</template>
<script>
import * as echarts from 'echarts'
import moment from 'moment'
export default {
  name: 'alarmStatisticsDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    dataRange: {
      type: Array,
      default: () => []
    },
    activeDataFilter: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      searchForm: {
        dataRange: [],
        projectCode: '',
        entityTypeId: '',
        checkedincidents: [],
        hourDayOrMouth: 1
      },
      alarmSourceOptions: [],
      entityTypeList: [],
      checkAll: false,
      isIndeterminate: false,
      incidentList: [],
      startDate: '',//开始时间
      endDate: '',//结束时间
    }
  },
  mounted() {
    this.searchForm.hourDayOrMouth = Number(this.activeDataFilter)
    if (this.searchForm.hourDayOrMouth == 2 || this.searchForm.hourDayOrMouth == 3) {
      this.startDate = this.dataRange[0]
      this.endDate = this.dataRange[1]
    }
    this.searchForm.dataRange = this.dataRange
    this.getAlarmSource()
    this.selectIncidentParam()
    // this.getDictionaryList('init')
  },
  methods: {
    // 获取报警来源
    getAlarmSource() {
      this.$api.getSourceByEmpty().then((res) => {
        if (res.code == 200) {
          this.alarmSourceOptions = res.data
        }
      })
    },
    // 获取实体类型数据
    getDictionaryList() {
      const params = {
        projectCode: this.searchForm.projectCode
      }
      this.$api.selectEntityType(params).then((res) => {
        if (res.code == 200) {
          this.entityTypeList = res.data || []
          this.searchForm.entityTypeId = ''
          this.selectIncidentParam()
        }
      })
    },
    // 获取事件类型数据
    selectIncidentParam() {
      const params = {
        projectCode: this.searchForm.projectCode,
        entityTypeId: this.searchForm.entityTypeId
      }
      this.incidentList = []
      this.$api.selectIncidentParam(params).then((res) => {
        this.incidentList = res.data
        this.checkAll = true
        this.handleCheckAllChange(true)
      })
    },
    // 改变日期
    changeDate() {
      let startDateTime = new Date(this.startDate).getTime()
      let endDateTime = new Date(this.endDate).getTime()
      if (startDateTime > endDateTime) {
        this.$message.error('开始时间不能大于结束时间')
        this.startDate = ""
        return false
      }
      if (this.searchForm.hourDayOrMouth == 2) {
        this.startDate = moment().subtract(5, 'months').format("YYYY-MM-DD")
        this.endDate = moment().format('YYYY-MM-DD')
        this.searchForm.dataRange = [
          moment().subtract(5, 'months').startOf("month").format("YYYY-MM-DD"),
          moment().endOf('month').format("YYYY-MM-DD")
        ]
      } else if (this.searchForm.hourDayOrMouth == 3) {
        this.startDate = moment().subtract(2, 'years').format('YYYY-MM-DD')
        this.endDate = moment().format('YYYY-MM-DD')
        this.searchForm.dataRange = [
          moment().subtract(2, 'years').startOf('year').format('YYYY-MM-DD'),
          moment().endOf('year').format('YYYY-MM-DD')
        ]
      }
      this.selectIncidentParam()
      // this.getAlarmAnalysisTrendData()
    },
    // 日月年 数据切换
    timeTypeChange(type) {
      this.searchForm.hourDayOrMouth = type
      if (this.searchForm.hourDayOrMouth == 2) {
        this.startDate = moment().subtract(5, 'months').format("YYYY-MM-DD")
        this.endDate = moment().format('YYYY-MM-DD')
        this.searchForm.dataRange = [
          moment().subtract(5, 'months').startOf("month").format("YYYY-MM-DD"),
          moment().endOf('month').format("YYYY-MM-DD")
        ]
      } else if (this.searchForm.hourDayOrMouth == 3) {
        this.startDate = moment().subtract(2, 'years').format('YYYY-MM-DD')
        this.endDate = moment().format('YYYY-MM-DD')
        this.searchForm.dataRange = [
          moment().subtract(2, 'years').startOf('year').format('YYYY-MM-DD'),
          moment().endOf('year').format('YYYY-MM-DD')
        ]
      } else {
        this.searchForm.dataRange = [moment().subtract(5, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
      }
      this.getAlarmAnalysisTrendData()
    },
    // 获取新增报警趋势数据
    getAlarmAnalysisTrendData() {
      const params = {
        projectCode: this.searchForm.projectCode,
        entityTypeId: this.searchForm.entityTypeId,
        incidentParam: this.searchForm.checkedincidents.toString(),
        hourDayOrMouth: this.searchForm.hourDayOrMouth,
        startTime: this.searchForm.dataRange[0],
        endTime: this.searchForm.dataRange[1]
      }
      const incidentParamInfo = this.searchForm.checkedincidents.map((e) => {
        return {
          name: e,
          value: this.incidentList.find((a) => a.incidentParam == e).alarmTypeId
        }
      })
      params.incidentParamInfo = incidentParamInfo
      if (this.searchForm.checkedincidents.length) {
        this.$api.getAlarmTrendPcByParam(params).then((res) => {
          this.$refs.alarmAnalysisTrend.init(this.getAlarmTrendEchats(res.data))
        })
      } else {
        this.$refs.alarmAnalysisTrend.init(this.getAlarmTrendEchats())
      }
    },
    // 报警趋势图
    getAlarmTrendEchats(data = []) {
      function colorAdd(transparency) {
        return [
          `rgba(255, 100, 97, ${transparency})`,
          `rgba(255, 148, 53, ${transparency})`,
          `rgba(53, 98, 219, ${transparency})`,
          `rgba(0, 188, 109, ${transparency})`,
          `rgba(115, 192, 222, ${transparency})`,
          `rgba(154, 96, 180, ${transparency})`,
          `rgba(250, 200, 88, ${transparency})`,
          `rgba(9, 205, 143, ${transparency})`
        ]
      }
      let colorHalf = colorAdd('.5')
      let colorZero = colorAdd('0')
      let option
      // var a = Array.from(new Array(10).keys()).slice(1)
      if (data.length) {
        option = {
          color: colorHalf,
          tooltip: {
            trigger: 'axis'
          },
          legend: {
            // icon: 'rect',
            show: true,
            top: '6%'
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: data.map((x) => {
              return x.time
            })
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: '{value}'
            }
          },
          series: this.dataArr(data, colorHalf, colorZero),
          dataZoom: [
            {
              height: 20, // 时间滚动条的高度
              type: 'inside', // type的作用是指定数据缩放的类型，slider表示使用滑动条进行缩放，inside表示使用鼠标滚轮进行缩放。
              xAxisIndex: 0, // 作用在x轴的下标（因为x轴可以有多个）
              filterMode: 'filter', // 间滚动条的过滤模式,'filter'表示滑动时间条时会直接过滤掉不在时间范围内的数据，'weakFilter'表示滑动时间条时会逐渐过滤掉不在时间范围内的数据。
              start: 0, // 默认开始位置（百分比）
              end: 100 // 默认结束位置（百分比）
            },
            {
              type: 'inside',
              xAxisIndex: 0,
              filterMode: 'filter',
              start: 0,
              end: 100
            }
          ],
          grid: {
            left: '50px',
            right: '50px',
            bottom: '0px',
            containLabel: true
          }
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      return option
    },
    hexToRgb(hex) {
      // 去除#号
      let color = hex.replace('#', '')
      // 分割成红、绿、蓝三部分的16进制字符串
      let red = parseInt(color.substring(0, 2), 16)
      let green = parseInt(color.substring(2, 4), 16)
      let blue = parseInt(color.substring(4, 6), 16)
      return `${red},${green},${blue}`
    },
    dataArr(data) {
      let arr = []
      data.forEach((v) => {
        v.list.length &&
          v.list.forEach((sonV, index) => {
            let obj = arr.find((ele) => ele.name == sonV.incidentName)
            if (obj) {
              obj.data.push(sonV.count)
            } else {
              const randomColor = Math.floor(Math.random() * 256)
              const color = sonV.incidentParamColour ? this.hexToRgb(sonV.incidentParamColour) : randomColor + ',' + randomColor + ',' + randomColor
              // const color = randomColor + ',' + randomColor + ',' + randomColor
              let dataObj = {
                name: sonV.incidentName,
                type: 'line',
                data: [sonV.count],
                areaStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: `rgba(${color}, .5)`
                      },
                      {
                        offset: 1,
                        color: `rgba(${color}, 0)`
                      }
                    ])
                  }
                }
              }
              arr.push(dataObj)
            }
          })
      })
      return arr
    },
    handleCheckAllChange(val) {
      this.searchForm.checkedincidents = val ? Array.from(this.incidentList, ({ incidentParam }) => incidentParam) : []
      this.isIndeterminate = false
      this.getAlarmAnalysisTrendData()
    },
    handleCheckedCitiesChange(value) {
      let checkedCount = value.length
      this.checkAll = checkedCount === this.incidentList.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.incidentList.length
      this.getAlarmAnalysisTrendData()
    },
    closeDialog() {
      this.$emit('update:visible', !this.visible)
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog__body {
  height: calc(78vh - 110px);
}
.dialog-content {
  background: #fff;
  padding: 15px;
  display: flex;
  flex-direction: column;

  .filterClass {
    height: 40px;
    display: flex;
    align-items: center;
    justify-items: center;
    .style-interval {
      margin-left: 10px;
    }
  }

  .control-btn-header {
    // padding: 6px 6px 16px 16px;
    margin: 15px 0;
    & > div {
      margin-right: 10px;
      margin-top: 10px;
    }
    .btn-item {
      border: 1px solid #3562db;
      color: #3562db;
      font-family: none;
    }
    .btn-active {
      color: #fff;
      background: #3562db;
    }
  }
  .incident-checkbox {
    max-height: 100px;
    overflow-y: auto;
    ::v-deep .el-checkbox {
      width: 100px;
      margin-bottom: 10px;
      .el-checkbox__label {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: bottom;
      }
    }
    ::v-deep .el-checkbox-group {
      display: contents;
    }
  }
  .content-aside {
    flex: 1;
  }
}
::v-deep .datePickerClass .el-date-editor {
  width: 150px !important;
}
</style>
