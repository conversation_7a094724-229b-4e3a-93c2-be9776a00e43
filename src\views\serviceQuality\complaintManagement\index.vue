<template>
  <PageContainer>
    <div slot="header">
      <div class="searchForm">
        <el-select v-model="signatureFlag" placeholder="匿名状态">
          <el-option v-for="item in signatureFlagOption" :key="item.label" :label="item.label" :value="item.value"> </el-option>
        </el-select>
        <!-- <el-input v-model="sourcesDeptName" placeholder="投诉部门"></el-input> -->
        <el-select v-model="showTimeType" placeholder="申报时间">
          <el-option v-for="item in showTimeTypeOption" :key="item.value" :label="item.label" :value="item.value"> </el-option>
        </el-select>
        <el-date-picker
          v-show="showTimeType == '4'"
          v-model="dateVal"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd+HH:mm:ss"
        >
        </el-date-picker>
        <!-- <el-select v-model="flowtype" placeholder="工单状态">
          <el-option v-for="item in flowtypeOption" :key="item.label" :label="item.label" :value="item.value"> </el-option>
        </el-select> -->
        <el-button type="primary" plain @click="resetForm">重置</el-button>
        <el-button type="primary" @click="searchForm">查询</el-button>
      </div>
    </div>
    <div slot="content" ref="contentRef">
      <div class="statistics">
        <div :class="['item', 'pointer-style', { 'hover-style': flowtype == '' }]" @click="changeFlowCode('')">
          <span>投诉总数</span>
          <span>{{ countData.all }}</span>
        </div>
        <div class="item pure">
          <span>综合维修</span>
          <span>{{ countData.wx }}</span>
        </div>
        <div class="item pure">
          <span>应急保洁</span>
          <span>{{ countData.bj }}</span>
        </div>
        <div class="item pure">
          <span>运送服务</span>
          <span>{{ countData.ys }}</span>
        </div>
        <div class="item pure">
          <span>综合服务</span>
          <span>{{ countData.bm }}</span>
        </div>
        <div class="item pure">
          <span>其他</span>
          <span>{{ countData.other }}</span>
        </div>
      </div>
      <el-table v-loading="tableLoading" :data="tableData" border style="width: 100%;" height="77%" @row-click="handleClick">
        <el-table-column label="序号" type="index" width="50" align="center" :resizable="false">
          <template slot-scope="scope">
            {{ (pageNo - 1) * pageSize + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="关联工单" width="180" :resizable="false">
          <template slot-scope="scope">
            <span style="color: #3562db;">{{ scope.row.relevanceWorknum }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="createDate" label="开单时间" show-overflow-tooltip :resizable="false"> </el-table-column>
        <el-table-column prop="callerName" label="投诉人" width="180" show-overflow-tooltip :resizable="false"> </el-table-column>
        <el-table-column prop="complaintTitle" label="投诉标题" show-overflow-tooltip :resizable="false"></el-table-column>
        <el-table-column prop="questionDescription" label="投诉描述" show-overflow-tooltip :resizable="false"> </el-table-column>
        <el-table-column label="状态" :resizable="false">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.flowtype" type="danger">{{ scope.row.flowtype }}</el-tag>
            <el-tag v-else type="success">已完成</el-tag>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="pageNo"
        :page-sizes="[15, 30, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
      </el-pagination>
      <template v-if="workOrderDetailCenterShow">
        <el-dialog :visible.sync="workOrderDetailCenterShow" custom-class="detailDialog main" :close-on-click-modal="false" :before-close="workOrderDetailCloseDialog">
          <template slot="title">
            <span class="dialog-title">{{ dialogTitle }}</span>
          </template>
          <workOrderDetailList :rowData="detailObj" />
        </el-dialog>
      </template>
    </div>
  </PageContainer>
</template>
<script>
import store from '@/store/index'
import workOrderDetailList from './workOrderDetailList.vue'
export default {
  name: 'WorkOrder',
  components: {
    workOrderDetailList
  },
  data() {
    return {
      workNum: '', // 工单号
      flowtype: '', // 工单状态
      signatureFlag: '', // 跟踪状态
      showTimeType: '1', // 申报时间
      dateVal: '', // 自定义时间
      tableData: [], // 表格数据
      total: 0, // 总条数
      pageNo: 1, // 当前页
      pageSize: 15, // 每页条数
      tableLoading: false, // 表格加载状态
      flowtypeOption: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '已完成',
          value: '1'
        },
        {
          label: '未处理',
          value: '0'
        }
      ],
      signatureFlagOption: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '署名投诉',
          value: '0'
        },
        {
          label: '匿名投诉',
          value: '1'
        }
      ],
      showTimeTypeOption: [
        {
          label: '本年',
          value: '3'
        },
        {
          label: '昨天',
          value: '5'
        },
        {
          label: '今天',
          value: '1'
        },
        {
          label: '本周',
          value: '6'
        },
        {
          label: '本月',
          value: '2'
        },
        {
          label: '其他',
          value: '4'
        }
      ],
      countData: {},
      workOrderDetailCenterShow: false,
      detailObj: {},
      dialogTitle: '',
      sourcesDeptName: ''
    }
  },
  created() {},
  mounted() {
    this.getList()
    this.getReckonCount()
  },
  methods: {
    resetForm() {
      this.workNum = ''
      this.flowtype = ''
      this.signatureFlag = ''
      this.showTimeType = '1'
      this.dateVal = ''
      this.getList()
      this.getReckonCount()
    },
    searchForm() {
      this.getList()
      this.getReckonCount()
    },
    getList() {
      const userInfo = store.state.user.userInfo.user
      let params = {
        id: '',
        contrastType: '',
        free2: '',
        workTypeCode: 5,
        workTypeName: 'TS',
        urgencyDegree: '',
        disDegree: '',
        disDegreeNew: '',
        workSources: '',
        signatureFlag: this.signatureFlag,
        responseTime: '',
        responseTimeType: '',
        transportTypeCode: '',
        showTimeType: this.showTimeType,
        typeSources: '',
        startTime: this.dateVal[0],
        endTime: this.dateVal[1],
        sectionStartDate: '',
        sectionEndDate: '',
        sectionStartTime: '',
        sectionEndTime: '',
        sourcesDept: '',
        // sourcesDeptName: this.sourcesDeptName,
        region: '',
        buliding: '',
        storey: '',
        room: '',
        localtionName: '',
        companyCode: '',
        designateDeptCode: '',
        designatePersonCode: '',
        replyToCode: '',
        replyToPeople: '',
        callerName: '',
        sourcesPhone: '',
        itemDetailCode: '',
        itemDetailName: '',
        itemTypeCode: '',
        itemTypeName: '',
        itemServiceCode: '',
        itemServiceName: '',
        itemList: '',
        questionDescription: '',
        workNum: this.workNum,
        restaurantId: '',
        restaurantName: '',
        haveStartTime: '',
        haveEndTime: '',
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        orderBy: '',
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
      this.tableLoading = true
      this.$api.getWorkOrderDataList(params).then((res) => {
        this.tableLoading = false
        this.tableData = res.rows
        this.total = res.total
      })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.pageNo = val
      this.getList()
    },
    getReckonCount() {
      const userInfo = store.state.user.userInfo.user
      let params = {
        workNum: this.workNum,
        workTypeCode: 5,
        workTypeName: 'TS',
        signatureFlag: this.signatureFlag,
        showTimeType: this.showTimeType,
        startTime: this.dateVal[0],
        endTime: this.dateVal[1],
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
      this.$api.getReckonCount(params).then((res) => {
        if (res.success) {
          this.countData = res.body.data
        }
      })
    },
    handleClick(row) {
      this.detailObj = row
      this.dialogTitle = '后勤投诉'
      this.workOrderDetailCenterShow = true
    },
    workOrderDetailCloseDialog() {
      this.workOrderDetailCenterShow = false
    },
    changeFlowCode(val) {
      return
      this.flowtype = val
      this.getList()
      this.getReckonCount()
    }
  }
}
</script>
<style lang="scss" scoped>
.searchForm {
  display: flex;
  height: 80px;
  padding: 0 16px;
  align-items: center;

  .el-input {
    width: 200px;
  }

  > div {
    margin-right: 20px;
  }
}

.container-content > div {
  height: 100%;
  background-color: #fff;
  padding: 16px;
  margin-top: 16px;
}

.el-table {
  margin-bottom: 12px;
}

::v-deep .el-date-editor .el-range__icon {
  line-height: 24px;
}

.statistics {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
  height: 78px;
  width: 65%;
}

.statistics .item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-around;
  flex: 1;
  background-color: rgb(53 98 219 / 6%);
  margin-right: 8px;
  border-radius: 4px;
}

.statistics .item span:nth-child(2) {
  font-size: 18px;
  color: #3562db;
  font-weight: 700;
}

.statistics .hover-style {
  background-color: #fff;
  box-shadow: 0 0 10px 0 rgb(32 56 114 / 13%);
  border-bottom: 3px solid #3562db;
}

.statistics .pointer-style {
  cursor: pointer;
}

.statistics .pure {
  background-color: #fff;
}

.statistics .pure span:nth-child(2) {
  color: #121f3e;
}

::v-deep .el-dialog {
  height: 80vh;
  margin-top: 10vh;
}

::v-deep .el-dialog__wrapper {
  overflow: hidden;
}

::v-deep .el-dialog__body {
  height: 93%;
}

::v-deep .el-table__row:hover {
  cursor: pointer;
}
</style>
