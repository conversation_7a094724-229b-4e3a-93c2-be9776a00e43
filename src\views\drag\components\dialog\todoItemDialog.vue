<!--
 * @Author: hedd
 * @Date: 2023-03-08 17:09:35
 * @LastEditTime: 2023-04-10 18:57:30
 * @FilePath: \ihcrs_pc\src\views\drag\components\dialog\todoItemDialog.vue
 * @Description:
-->
<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    width="560px"
    append-to-body
    :visible.sync="visible"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <template slot="title">
      <span class="dialog-title" @click="showDatePicker"
        ><i class="el-icon-date" style="font-size: 18px;"></i>{{ nowTime }}
      </span>
      <div class="date-paicker-box">
        <el-date-picker
          ref="datePicker"
          v-model="nowDateTime"
          popper-class="timePicker"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetime"
          placeholder="选择日期时间"
          @change="changeDataTime"
        >
        </el-date-picker>
      </div>
    </template>
    <div class="dialog-content">
      <div class="left-input">
        <el-input v-model="formInline.title" placeholder="准备做什么"></el-input>
        <el-input v-model="formInline.remark" type="textarea" :autosize="{ minRows: 5, maxRows: 8 }" placeholder="描述"> </el-input>
      </div>
      <div class="right-level">
        <div v-for="item in levelIconList" :key="item.type" class="level-item" @click="activeLevel = item.type">
          <svg-icon :name="item.icon" />
          <span :class="{ 'level-name': true, 'active-name': activeLevel == item.type }">{{ item.name }}</span>
          <svg-icon v-if="activeLevel == item.type" name="round-tick" />
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog()">取消</el-button>
      <el-button type="primary" @click="submitForm()">保存</el-button>
    </span>
  </el-dialog>
</template>
<script>
import moment from 'moment'
import { levelIconList } from '@/util/dict.js'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    itemData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      nowTime: '',
      nowDateTime: '',
      formInline: {
        title: '',
        remark: ''
      },
      activeLevel: 3,
      levelIconList: levelIconList
    }
  },
  mounted() {
    if (this.itemData.msgId) {
      this.nowDateTime = moment(this.itemData.publishTime).format('YYYY-MM-DD HH:mm:ss')
      this.nowTime = moment(this.nowDateTime).format('MM月DD日 HH:mm')
    } else {
      this.nowDateTime = moment().format('YYYY-MM-DD HH:mm:ss')
      this.nowTime = moment().format('MM月DD日 HH:mm')
    }
    this.formInline.title = this.itemData.msgTitle || ''
    this.formInline.remark = this.itemData.msgRemark || ''
    this.activeLevel = this.itemData.msgLevel || 3
  },
  methods: {
    closeDialog() {
      this.$emit('update:visible', false)
    },
    submitForm() {
      let params = {
        msgTitle: this.formInline.title,
        msgRemark: this.formInline.remark,
        msgLevel: this.activeLevel,
        publishTime: this.nowDateTime,
        msgSysId: 0,
        msgType: 2,
        userId: this.$store.state.user.userInfo.user.staffId
      }
      if (this.itemData.msgId) {
        params.msgId = this.itemData.msgId
        this.updateToDoList(params)
      } else {
        params.userName = this.$store.state.user.userInfo.user.staffName
        this.insertToDoList(params)
      }
    },
    insertToDoList(params) {
      this.$api.insertToDoList(params).then((res) => {
        if (res.code == 200) {
          this.$emit('update:visible', false)
          this.$emit('closeTodoDialog')
        } else {
          this.$message.error(res.message)
        }
      })
    },
    updateToDoList(params) {
      this.$api.updateToDoList(params).then((res) => {
        if (res.code == 200) {
          this.$emit('update:visible', false)
          this.$emit('closeTodoDialog')
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 显示日期选择器
    showDatePicker() {
      this.$refs.datePicker.focus()
    },
    // 选择日期
    changeDataTime(val) {
      this.nowDateTime = moment(val).format('YYYY-MM-DD HH:mm:ss')
      this.nowTime = moment(val).format('MM月DD日 HH:mm')
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .model-dialog {
  background: #fff;

  .dialog-title {
    font-size: 17px;
    cursor: pointer;

    i {
      font-size: 20px;
      margin-right: 8px;
    }
  }

  .date-paicker-box {
    position: relative;

    .el-date-editor {
      width: 0;
      position: absolute;
      top: -20px;
      left: 0;
      opacity: 0;
    }
  }
}

.dialog-content {
  background: #fff;
  padding: 16px;
  width: 100%;
  display: flex;

  .left-input {
    width: 65%;

    :first-child {
      width: 70%;
      margin-bottom: 8px;
    }

    :last-child {
      width: 95%;
    }
  }

  .right-level {
    width: 35%;
    padding: 10px 15px;
    cursor: pointer;

    .level-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      svg {
        width: 24px;
        height: 24px;
      }

      .level-name {
        font-size: 14px;
        font-family: "PingFang SC-Regular", "PingFang SC";
        color: #414653;
        margin: 4px 6px;
      }

      .active-name {
        color: #3562db;
      }
    }
  }
}
</style>
