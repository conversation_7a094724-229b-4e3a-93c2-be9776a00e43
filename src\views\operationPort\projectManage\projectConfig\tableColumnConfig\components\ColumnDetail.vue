<script>
import { UsingStatusOptions } from '@/views/operationPort/constant'
import ContentCard from '@/components/ContentCard/index.vue'
import ColumnDataConfig from './ColumnDataConfig.vue'
export default {
  name: 'ColumnDetail',
  components: { ColumnDataConfig, ContentCard },
  events: ['update:visible', 'success'],
  props: {
    id: Number,
    visible: Boolean,
    readonly: <PERSON>olean
  },
  data: function() {
    return {
      formModel: {
        name: '',
        code: '',
        visible: 1,
        sortNo: ''
      },
      rules: {
        name: [{ required: true, message: '请输入状态名称' }],
        sortNo: [{ required: true, message: '请输入列排序号' }]
      },
      tableData: [],
      // 编辑配置dialog
      dialog: {
        show: false,
        rowIndex: -1
      },
      loadingStatus: false,
      statusOptions: UsingStatusOptions
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    },
    toFetch() {
      return this.dialogVisible && this.id
    },
    // 当前要编辑的行
    currentRowData() {
      const row = this.tableData[this.dialog.rowIndex]
      return row ? Object.assign({}, row) : null
    }
  },
  watch: {
    toFetch(val) {
      val && this.getDetail()
    }
  },
  methods: {
    onDrawerClosed() {
      this.$refs.formRef.resetFields()
    },
    getDetail() {
      this.loadingStatus = true
      this.$api.SporadicProject.queryProjectColumnDetail({ id: this.id })
        .then((res) => {
          if (res.code === '200') {
            // form data
            this.formModel.name = res.data.name
            this.formModel.visible = +res.data.state
            this.formModel.code = res.data.code
            this.formModel.sortNo = res.data.shortNo
            // 数据配置
            this.tableData = res.data.detailList
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取状态详情失败'))
        .finally(() => (this.loadingStatus = false))
    },
    onSubmit() {
      this.$refs.formRef.validate().catch(() => Promise.reject())
        .then(() => {
          this.loadingStatus = true
          const params = {
            id: this.id,
            name: this.formModel.name,
            shortNo: this.formModel.sortNo,
            state: this.formModel.visible,
            detailList: this.tableData
          }
          return this.$api.SporadicProject.updateProjectColumn(params)
        })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('操作成功')
            this.$emit('success')
            this.dialogVisible = false
          } else {
            throw res.message
          }
        })
        .catch((msg) => {
          msg && this.$message.error(msg)
        })
        .finally(() => {
          this.loadingStatus = false
        })
    },
    // 点击编辑列配置
    onEditColumnConfig(index) {
      this.dialog.rowIndex = index
      this.dialog.show = true
    },
    // 编辑了表单的这个字段数据来源时进行表格更新
    onColumnDataUpdate(data) {
      this.$set(this.tableData, this.dialog.rowIndex, data)
    }
  }
}
</script>
<template>
  <el-drawer class="component column-detail" title="表单列详情" size="960px" :visible.sync="dialogVisible" :close-on-press-escape="false" :wrapper-closable="false" @closed="onDrawerClosed">
    <ContentCard title="基本信息">
      <template #content>
        <el-form ref="formRef" :disabled="readonly" :model="formModel" :rules="rules" label-width="95px">
          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="列名称" prop="name">
                <el-input v-model="formModel.name" placeholder="请输入"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="列编码" prop="formName">
                <el-input :value="formModel.code" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="显隐状态" prop="visible">
                <el-select v-model="formModel.visible" placeholder="请选择">
                  <el-option :value="0" label="隐藏"></el-option>
                  <el-option :value="1" label="显示"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="排序" prop="sortNo">
                <el-input v-model="formModel.sortNo" placeholder="请输入" onkeyup="value=value.replace(/^0|\D/,'')"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </template>
    </ContentCard>
    <ContentCard title="数据配置" class="column-detail__table">
      <template #content>
        <el-table height="100%" :data="tableData" border stripe table-layout="auto" class="tableAuto" row-key="id">
          <el-table-column label="项目类型" prop="businessFormName"></el-table-column>
          <el-table-column label="项目编码" prop="businessFormCode"></el-table-column>
          <el-table-column label="表单字段名称" prop="name"></el-table-column>
          <el-table-column label="表单字段编码" prop="code"></el-table-column>
          <el-table-column label="操作" width="80px">
            <template v-if="!readonly" #default="scope">
              <el-button type="text" @click="onEditColumnConfig(scope.$index)">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </ContentCard>
    <div class="column-detail__footer">
      <el-button type="primary" plain @click="dialogVisible = false">取消</el-button>
      <el-button v-if="!readonly" type="primary" @click="onSubmit">确认</el-button>
    </div>
    <!--给项目配置该列的数据来源-->
    <ColumnDataConfig :visible.sync="dialog.show" :field-data="currentRowData" @update="onColumnDataUpdate"></ColumnDataConfig>
  </el-drawer>
</template>
<style lang="scss">
.component.column-detail {
  .el-drawer {
    .el-drawer__header {
      color: #333;
      padding: 9px 16px;
      border-bottom: solid 1px #eee;
      margin-bottom: 0px;
    }
    .el-drawer__body {
      display: flex;
      flex-flow: column nowrap;
      overflow: hidden;
    }
  }
  .box-card {
    padding: 16px;
    .card-body {
      overflow: hidden;
    }
  }
  @mixin normal-text {
    cursor: default;
    color: #666;
    border: none;
    background-color: transparent;
  }
  .el-form {
    .el-form-item {
      .el-select {
        width: 100%;
      }
      .el-textarea.is-disabled .el-textarea__inner {
        @include normal-text;
        resize: none;
        padding-top: 9px;
      }
      .el-input.is-disabled {
        .el-input__inner {
          @include normal-text;
        }
        .el-input__icon {
          display: none;
        }
      }
    }
  }
  .column-detail {
    &__table {
      flex: 1;
      margin-top: -30px;
    }
    &__footer {
      padding: 16px;
      text-align: right;
    }
  }
}
</style>
