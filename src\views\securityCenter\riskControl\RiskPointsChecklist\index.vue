<template>
  <PageContainer>
    <div slot="content" class="role-content" style="height: 100%;">
      <div class="role-content-left">
        <div class="toptip">
          <span class="green_line"></span>
          风险点清单
        </div>
        <div class="left_content">
          <el-input v-model.trim="filterText" style="margin-top: 10px;" clearable placeholder="输入关键字进行过滤"></el-input>
          <div class="tree">
            <el-tree
              ref="tree"
              style="margin-top: 10px;"
              :check-strictly="true"
              :data="treeData"
              :props="defaultProps"
              node-key="dictCode"
              :highlight-current="true"
              :filter-node-method="$tools.filterNode"
              :default-expanded-keys="expanded"
              @node-click="handleNodeClick"
            ></el-tree>
          </div>
        </div>
      </div>
      <div class="role-content-right">
        <div style="height: 100%;">
          <div class="search-from">
            <el-select v-model="filters.riskLevel" class="sino_sdcp_input mr15" placeholder="风险等级" filterable clearable>
              <el-option v-for="(item, index) in riskLevelList" :key="index" :label="item.dictLabel" :value="item.dictValue" class="set_zindex"></el-option>
            </el-select>
            <el-select v-model="filters.status" class="sino_sdcp_input mr15" placeholder="管控状态" filterable clearable>
              <el-option v-for="(item, index) in guankongList" :key="index" :label="item.name" :value="item.id" class="set_zindex"></el-option>
            </el-select>
            <el-input v-model.trim="filters.riskName" clearable placeholder="风险点名称 " style="width: 200px;"></el-input>
            <el-select v-model="filters.controlGroupIds" class="sino_sdcp_input mr15" placeholder="责任部门" filterable clearable>
              <el-option v-for="(item, index) in groupRiskArrData" :key="index" :label="item.teamName" :value="item.id" class="set_zindex"></el-option>
            </el-select>
            <el-input v-model.trim="filters.responsiblePersonName" clearable placeholder="责任人" style="width: 200px;"></el-input>
            <el-button type="primary" @click="resetData">重置</el-button>
            <el-button type="primary" class="sino-button-sure-search" @click="searchClick()">查询</el-button>
          </div>
          <div class="middle_tools">
            <el-button type="primary" icon="el-icon-plus" :disabled="multipleSelection.length != 1" @click="operation('add')">新增巡查清单</el-button>
            <el-button type="primary" icon="el-icon-edit" :disabled="multipleSelection.length != 1" @click="operation('edit')">编辑</el-button>
            <el-button type="primary" icon="el-icon-picture-outline" :disabled="multipleSelection.length != 1" @click="confirmDialogShow">查看二维码</el-button>
          </div>
          <div class="contentTable">
            <div class="contentTable-main table-content">
              <el-table v-loading="tableLoading" :data="tableData" :height="tableHeight" border stripe @selection-change="handleSelectionChange" @row-dblclick="dblclick">
                <el-table-column type="selection" width="55" fixed></el-table-column>
                <el-table-column type="index" width="60" label="序号">
                  <template slot-scope="scope">
                    <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="riskName" label="风险点名称" min-width="100" show-overflow-tooltip></el-table-column>
                <el-table-column prop="riskPlace" label="风险点位点(区域)或系统" show-overflow-tooltip width="180"></el-table-column>
                <el-table-column prop="riskLevel" label="风险等级" min-width="80" show-overflow-tooltip width="120">
                  <template slot-scope="scope">
                    <div v-if="scope.row.riskLevel == 1" class="table-block-text" style="background: rgb(255 0 0);">重大风险</div>
                    <div v-if="scope.row.riskLevel == 2" class="table-block-text" style="background: rgb(255 97 0);">较大风险</div>
                    <div v-if="scope.row.riskLevel == 3" class="table-block-text" style="background: rgb(255 255 0); color: #606266;">一般风险</div>
                    <div v-if="scope.row.riskLevel == 4" class="table-block-text" style="background: rgb(0 0 255);">低风险</div>
                    <div v-if="scope.row.riskLevel == 5" class="table-block-text" style="color: #606266;">未研判</div>
                  </template>
                </el-table-column>
                <el-table-column prop="taskTeamName" show-overflow-tooltip label="责任部门" width="100"></el-table-column>
                <el-table-column prop="responsiblePersonName" show-overflow-tooltip label="责任人"></el-table-column>
                <el-table-column prop="urgentContactPhone" show-overflow-tooltip label="应急联系电话" min-width="130"></el-table-column>
                <!-- <el-table-column prop="urgentPhone" show-overflow-tooltip label="应急电话" min-width="100"></el-table-column> -->
                <el-table-column prop="attachmentUrl" show-overflow-tooltip label="图片" min-width="100">
                  <template slot-scope="scope">
                    <span style="color: #5188fc; cursor: pointer;" @click="showPicture(scope.row, 'attachmentUrl')">查看图片</span>
                  </template>
                </el-table-column>
                <el-table-column prop="processUrl" show-overflow-tooltip label="预案流程图" min-width="100">
                  <template slot-scope="scope">
                    <span style="color: #5188fc; cursor: pointer;" @click="showPicture(scope.row, 'processUrl')">查看图片</span>
                  </template>
                </el-table-column>
                <el-table-column prop="registerPerson" show-overflow-tooltip label="登记人"></el-table-column>
                <el-table-column prop="registerTime" show-overflow-tooltip label="登记时间"></el-table-column>
                <el-table-column prop="status" show-overflow-tooltip label="是否管控" min-width="60">
                  <template slot-scope="scope">
                    <span>{{ scope.row.status == 0 ? '启用' : '禁用' }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="100">
                  <template slot-scope="scope">
                    <el-switch
                      v-model="scope.row.status"
                      active-value="0"
                      inactive-value="1"
                      active-color="#5188fc"
                      inactive-color="#DCDFE6"
                      @change="switchChange(scope.row)"
                    ></el-switch>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="contentTable-footer">
              <el-pagination
                class="pagination"
                :current-page="paginationData.currentPage"
                :page-sizes="[15, 30, 50, 100]"
                :page-size="paginationData.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="paginationData.total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              ></el-pagination>
            </div>
          </div>
        </div>
      </div>
      <el-dialog title="查看二维码" custom-class="model-dialog" style="text-align: left;" :visible.sync="confirmDialog" width="50%">
        <div class="content" style="width: 100%; padding: 10px; background-color: #fff;">
          <div class="contentItem" style="text-align: center; margin: 0 auto;">
            <img style="width: 300px;" :src="'data:image/png;base64' + img" alt />
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="confirmDialog = false">关闭</el-button>
        </span>
      </el-dialog>
      <imgCarousel :dialogVisibleImg="dialogVisibleImg" :imgArr="imgArr" @closeDialog="closeDialogImg"></imgCarousel>
    </div>
  </PageContainer>
</template>

<script>
import tableListMixin from '@/mixins/tableListMixin.js'
import imgCarousel from '@/components/imgCarousel/imgCarousel'
export default {
  name: 'RiskPointsChecklist',
  components: { imgCarousel },
  mixins: [tableListMixin],
  data() {
    return {
      loading: false,
      importFileDialog: false,
      confirmDialog: false,
      id: '',
      type: 'edit',
      treeData: [],
      tableCode: 1,
      defaultProps: {
        children: 'children',
        label: 'dictLabel',
        value: 'dictValue'
      },
      defaultProps2: {
        children: 'children',
        label: 'gridName',
        value: 'id'
      },
      defaultProps4: {
        children: 'children',
        label: 'text',
        value: 'id'
      },
      riskLevelList: [],
      guankongList: [
        {
          name: '启用',
          id: 0
        },
        {
          name: '禁用',
          id: 1
        }
      ],
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      tableData: [],
      treeLoading: false,
      tableLoading: false,
      filters: {
        riskName: '',
        riskLevel: '',
        status: '',
        controlGroupIds: '',
        responsiblePersonName: '',
        riskName: ''
      },
      multipleSelection: [],
      checkedData: {},
      filterText: '',
      expanded: [],
      riskType: '',
      dialogVisibleExport: false,
      dataList: [],
      groupRiskArrData: [],
      teamList: [],
      imgArr: [],
      dialogVisibleImg: false,
      // eslint-disable-next-line vue/no-dupe-keys
      confirmDialog: false,
      img: ''
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  mounted() {},
  created() {
    this.getTreeData()
    this.getDictValue()
  },
  methods: {
    getDictValue() {
      // 获取风险等级
      this.$api
        .ipsmGetDictValue({
          dictType: 'risk_level_judge',
          isShowParent: 0
        })
        .then((res) => {
          if (res.code == 200) {
            this.riskLevelList = res.data
          }
        })
      // 获取分配班组
      this.groupRiskArrData = []
      this.$api.ipsmGetControlGroupInfoList({}).then((res) => {
        res.data.list.map((v) => {
          if (v.parent != '#') {
            this.groupRiskArrData.push(v)
          }
        })
      })
    },
    searchClick() {
      // this.$store.commit("changeImasTableLabel");
      // this.$store.commit("changeNetworkError", "查询失败，请检查您的网络…");
      this.paginationData.currentPage = 1
      this.getTableData(true)
    },
    // --------------新增，修改
    operation(type) {
      this.$api
        .ipsmCheckRiskInvestigation({ riskId: this.multipleSelection[0].id })
        .then((res) => {
          sessionStorage.setItem('RiskPointsChecklistRow', JSON.stringify(this.multipleSelection[0]))
          if (type == 'add') {
            if (res.code == 200) {
              this.$message.error(res.message)
            } else {
              this.$router.push({
                name: 'ipsmRiskDetail',
                query: { type: type }
              })
            }
          } else {
            if (res.code == 200) {
              this.$router.push({
                name: 'ipsmRiskDetail',
                query: { type: type }
              })
            } else {
              this.$message.error('该风险点未添加巡查清单')
            }
          }
        })
    },
    // --------------详情
    dblclick(val) {
      this.$api.ipsmCheckRiskInvestigation({riskId: val.id}).then(res => {
        let list = val
        list.riskInvestigationId = res.data.riskInvestigationId || ''
        sessionStorage.setItem('RiskPointsChecklistRow', JSON.stringify(list))
        sessionStorage.setItem('riskDetailType', 2)
        this.$router.push({
          name: 'ipsmRiskDetail',
          query: {type: 'check', id: val.id}
        })
      })
    },
    // 查看二维码
    confirmDialogShow() {
      this.confirmDialog = true
      this.$api.ipsmQueryQRCode({
        riskId: this.multipleSelection[0].id,
        riskName: this.multipleSelection[0].riskName
      }).then(res => {
        this.img = res.data.imgUrl
      })
    },
    // 状态修改
    switchChange(val) {
      this.tableLoading = true
      this.$api
        .ipsmRiskManageRiskUpdateStatus({
          id: val.id,
          status: val.status
        })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success(res.message)
          } else {
            this.$message.warning(res.message)
          }
          this.getTableData()
        })
    },
    closeDialogImg() {
      this.dialogVisibleImg = false
      this.imgArr = []
    },
    showPicture(row, type) {
      if (row.attachmentUrl.length > 0) {
        this.$api
          .ipsmGetPictureUrls({
            repairAttachmentUrl: type == 'attachmentUrl' ? row.attachmentUrl : row.processUrl
          })
          .then((res) => {
            res.data.repairAttachmentUrl.forEach(i => {
              this.imgArr.push(this.$tools.imgUrlTranslation(i))
            })
          })
        this.dialogVisibleImg = true
      } else {
        this.$message.error(type == 'attachmentUrl' ? '该记录无图片' : '该记录无预案流程图')
      }
    },
    // 勾选
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 获取风险分类
    getTreeData() {
      this.treeLoading = true
      this.$api
        .ipsmGetDictValue({
          dictType: 'risk_order_type',
          isShowParent: 0
        })
        .then((res) => {
          this.treeLoading = this.$store.state.loadingShow
          if (res.code == '200' && res.data.length > 0) {
            let arr = this.$tools.transData(res.data, 'dictCode', 'parentCode', 'children')
            this.treeData = arr
            this.expanded = [this.treeData[0].dictCode]
            this.riskType = this.treeData[0].dictValue
            this.checkedData = this.treeData[0]
            this.$nextTick(() => {
              this.$refs.tree.setCurrentKey(this.checkedData)
            })
            this.getTableData()
          }
        })
    },
    // 树状图点击
    handleNodeClick(data) {
      this.riskType = data.dictValue
      this.tableCode = Number(data.dictValue)
      this.paginationData.currentPage = 1
      this.checkedData = data
      this.$refs.tree.setCurrentKey(this.checkedData)
      this.getTableData()
    },
    getTableData(type) {
      let data = {
        queryType: 1,
        pageSize: this.paginationData.pageSize,
        currentPage: this.paginationData.currentPage,
        ...this.filters,
        riskType: this.riskType
      }
      this.tableLoading = true
      this.$api.ipsmRiskManageFindList(data).then((res) => {
        this.tableLoading = this.$store.state.loadingShow
        if (res.code == 200) {
          this.tableDataList = res.data
          this.tableData = res.data.list
          this.paginationData.total = parseInt(res.data.sum)
        } else if (res.message) {
          this.$message.error(res.message)
        }
      })
    },
    resetData() {
      this.filters.riskName = ''
      this.filters.riskLevel = ''
      this.filters.status = ''
      this.filters.controlGroupIds = ''
      this.filters.responsiblePersonName = ''
      this.filters.riskName = ''
      this.paginationData.currentPage = 1
      this.getTableData()
    },
    handleSizeChange(val) {
      this.paginationData.currentPage = 1
      this.paginationData.pageSize = val
      this.getTableData()
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.getTableData()
    },
    exportClickExport() {
      this.dialogVisibleExport = true
    }
  }
}
</script>
<style lang="scss" scoped>
// el-tree-node is-expanded is-focusable
::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: linear-gradient(to right, #d9e1f8, #fff);
}

::v-deep .el-tree-node__content {
  height: 32px;
}

.role-content {
  height: 100%;
  display: flex;

  .role-content-left {
    width: 246px;
    height: 100%;
    padding: 10px;
    background: #fff;
    border-radius: 4px;
    margin-right: 12px;

    .left_content {
      height: calc(100% - 60px);
      padding: 10px;
      // overflow: auto;
      .tree {
        height: calc(100% - 40px);
        overflow: auto;
      }
    }
  }

  .role-content-right {
    flex: 1;
    height: 100%;
    min-width: 0;
    padding: 10px;
    background: #fff;
    border-radius: 4px;

    .search-from {
      padding-bottom: 12px;

      & > div {
        margin-right: 10px;
      }

      & > button {
        margin-top: 12px;
      }
    }

    .middle_tools {
      // margin-top: 20px;
      margin-bottom: 10px;

      .print-template {
        display: inline-block;
        cursor: pointer;
        font-size: 22px;
        font-family: DIN-Bold;
        font-weight: 500;
        color: #6b9dff;
        margin-right: 40px;
      }
    }

    .contentTable {
      height: calc(100% - 100px);
      display: flex;
      flex-direction: column;

      .contentTable-main {
        height: calc(100% - 40px);
        overflow: auto;
      }

      .contentTable-footer {
        padding: 10px 0 0;
      }
    }
  }
}

.table-block-text {
  width: 80px;
  line-height: 24px;
  text-align: center;
  // cursor: pointer;
  color: #fff;
}
</style>
