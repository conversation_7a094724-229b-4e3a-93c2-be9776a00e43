<template>
  <PageContainer>
    <div slot="content" class="alarm-configuration">
      <div class="alarm-configuration-left">
        <ContentCard title="报警规则配置" :cstyle="{ height: '100%' }">
          <el-tree slot="content" :data="departs" :props="defaultProps" :default-expand-all="true" :highlight-current="true" @node-click="handleNodeClick"></el-tree>
        </ContentCard>
      </div>
      <div class="alarm-configuration-right">
        <div class="right-heade">
          <!-- <el-input v-model="alarmQueryParam.alarmSystemType" placeholder="报警系统" suffix-icon="el-icon-search" clearable /> -->
          <!-- <el-input v-model="searchFrom.parameterName" placeholder="监测参数" suffix-icon="el-icon-search" clearable /> -->
          <!-- <el-input v-model="searchFrom.harvesterName" placeholder="传感器名称" suffix-icon="el-icon-search" clearable /> -->
          <el-select v-model="alarmQueryParam.alarmSystemType" clearable placeholder="请选择报警系统">
            <el-option v-for="item in alarmSystemList" :key="item.imhId" :label="item.imhMonitorName" :value="item.imhId"></el-option>
          </el-select>
          <el-select v-model="alarmQueryParam.entityType" placeholder="请选择实体类型">
            <el-option v-for="item in entityTypeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
          <el-input v-model="alarmQueryParam.entityObject" placeholder="请输入实体对象" clearable> </el-input>
          <el-input v-model="alarmQueryParam.alarmRule" placeholder="请输入报警规则" clearable></el-input>
          <div style="display: inline-block;">
            <el-button type="primary" plain @click="resetData">重置</el-button>
            <el-button type="primary" @click="searchClick">查询</el-button>
            <el-button type="primary" @click="addfn('add')">新增</el-button>
          </div>
        </div>
        <div class="right-content">
          <div class="table-content">
            <TablePage
              ref="tablePage"
              v-loading="tableLoading"
              :tableColumn="tableColumn"
              :data="alarmRuleList"
              border
              height="calc(100% - 40px)"
              :showPage="true"
              :pageData="paginationData"
              @pagination="paginationChange"
            >
            </TablePage>
          </div>
        </div>
      </div>
    </div>
  </PageContainer>
</template>
<script lang="jsx">
export default {
  name: 'commonAlarmConfiguration',
  props: {
    requestHttp: {
      type: String,
      default: __PATH.VUE_IEMC_API
    },
    alarmDetailsPath: {
      type: String,
      default: 'managementConfig/alarmConfiguration/alarmConfigForm'
    }
  },
  data() {
    return {
      selectTreeData: {},
      paginationData: {
        page: 1,
        pageSize: 15,
        total: 0
      },
      detailsId: '', // 双击行的ID
      departs: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      alarmQueryParam: {
        alarmType: '',
        alarmSystemType: '', // 报警系统
        entityType: '', // 实体类型
        alarmRule: '', // 报警规则
        entityObject: '' // 实体对象
      },
      alarmSystemList: [],
      entityTypeList: [],
      alarmRuleList: [],
      tableLoading: false,
      tableColumn: [
        {
          prop: 'sensorName',
          label: '序号',
          type: 'index',
          width: '80',
          formatter: (scope) => {
            // 当前分页重置
            // return scope.$index + 1
            // 跨分页序号
            return (this.paginationData.page - 1) * this.paginationData.pageSize + scope.$index + 1
          }
        },
        {
          label: '报警类型',
          prop: 'alarmType'
        },
        {
          label: '报警名称',
          prop: 'alarmName'
        },
        {
          label: '报警系统',
          prop: 'alarmSys'
        },
        {
          label: '实体类型',
          prop: 'surverType',
          hasJudge: this.alarmDetailsPath == 'managementConfig/alarmConfiguration/alarmConfigForm'
        },
        {
          label: '实体对象',
          prop: 'surverName'
        },
        {
          label: '报警规则',
          prop: 'ruleText'
        },
        {
          prop: '',
          label: '操作',
          render: (h, scope) => {
            return (
              <div class="operationBtn">
                <span class="operationBtn-span" onClick={() => this.addfn('edit', scope)}>
                  修改
                </span>
                <span class="operationBtn-span" onClick={() => this.delData(scope.row)}>
                  删除
                </span>
              </div>
            )
          }
        }
      ]
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    this.getalarmentitylist()
    this.getTreeList()
    this.getAlarmSystemList()
    this.getEntityList()
  },
  activated() {
    this.getalarmentitylist()
  },
  mounted() {
  },
  methods: {
    addfn(type, scope) {
      const queryData = {
        requestHttp: this.requestHttp
      }
      if (type != 'add') {
        queryData.id = scope.row.id
      }
      this.$router.push({
        path: '/' + this.alarmDetailsPath,
        query: queryData
      })
    },
    // 树形结构
    getTreeList() {
      let data = {}
      this.$api.getAlarmConfigTree(data, {}, this.requestHttp).then((res) => {
        if (res.code == 200) {
          this.departs = res.data.children
        }
      })
    },
    handleNodeClick(val) {
      this.selectTreeData = val
      this.searchClick()
    },
    getAlarmSystemList() {
      let data = {
        projectCodes: ''
      }
      this.$api.getAlarmSystem(data, {}, this.requestHttp).then((res) => {
        if (res.code == 200) {
          this.alarmSystemList = res.data
        }
      })
    },
    getEntityList() {
      let data = {
        dictType: 12
      }
      this.$api.getDictionaryList(data).then((res) => {
        // h获取实体类型数据
        if (res.code == 200) {
          this.entityTypeList = res.data
        }
      })
    },
    resetData() {
      // 将查询条件置为空
      this.alarmQueryParam = {
        alarmSystemType: '',
        alarmType: '',
        entityType: '',
        alarmRule: '',
        entityObject: ''
      }
      this.searchClick()
    },
    getalarmentitylist() {
      // 查询
      let data = {
        page: this.paginationData.page,
        pageSize: this.paginationData.pageSize,
        alarmSysId: this.alarmQueryParam.alarmSystemType, // 报警系统
        // typeid: this.alarmQueryParam.alarmType,//报警系统ID
        surverTypeId: this.alarmQueryParam.entityType, // 报警实体类型ID
        surverName: this.alarmQueryParam.entityObject, // 报警实体对象
        jsonText: this.alarmQueryParam.alarmRule, // 报警规则
        alarmDictId: '' // 树形结构子节点
      }
      if (Object.keys(this.selectTreeData)) {
        if (this.selectTreeData.level == 1) {
          data.alarmTypeId = this.selectTreeData.id
        } else if (this.selectTreeData.level == 2) {
          data.alarmNameId = this.selectTreeData.id
        }
      }
      this.$api.getAlarmConfigByPage(data, {}, this.requestHttp).then((res) => {
        if (res.code == 200) {
          this.alarmRuleList = res.data.list
          this.paginationData.total = res.data.totalCount
        }
      })
    },
    searchClick() {
      this.paginationData.page = 1
      this.getalarmentitylist()
    },
    // 删除
    delData(row) {
      this.$confirm('确定删除?', '消息', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api
          .policeDeleteById({
            id: row.id
          }, { 'operation-type': 3, 'operation-id': row.id, 'operation-name': row.alarmName}, this.requestHttp)
          .then((res) => {
            if (res.code == 200) {
              this.$message.success(res.message)
              this.getalarmentitylist()
            }
          })
      })
    },
    // 分页事件
    paginationChange(pagination) {
      Object.assign(this.paginationData, pagination)
      this.getalarmentitylist()
    }
  }
}
</script>
<style lang="scss" scoped>
.alarm-configuration {
  height: 100%;
  display: flex;
  .alarm-configuration-left {
    width: 246px;
    height: 100%;
    // padding: 8px 16px 16px 16px;
    background: #fff;
    border-radius: 4px;
    ::v-deep .box-card {
      .card-title {
        position: relative;
        .select-servive {
          position: absolute;
          right: 0;
          top: 2px;
          cursor: pointer;
          font-family: PingFangSC-Regular, "PingFang SC";
          font-size: 14px;
          color: $color-primary;
          font-weight: 600;
          padding-left: 15px;
          i {
            font-weight: 600;
            font-size: 13px;
            margin-right: 2px;
          }
        }
      }
      .card-body {
        border-top: 1px solid #dcdfe6;
        padding-top: 10px;
        .custom-tree-node {
          flex: 1;
          display: flex;
          justify-content: space-between;
          padding-right: 10px;
          & > span {
            &:first-child {
              flex: 1;
              display: block;
              align-items: center;
              text-overflow: ellipsis;
              white-space: nowrap;
              overflow: hidden;
              color: #121f3e;
              font-size: 14px;
              font-family: "PingFang SC-Regular", "PingFang SC";
            }
            &:last-child {
              display: flex;
              align-items: center;
              i {
                margin-left: 6px;
                cursor: pointer;
                color: #3562db;
              }
            }
          }
        }
        .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
          background-color: #d9e1f8;
        }
      }
    }
  }
  .alarm-configuration-right {
    height: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
    .right-heade {
      padding: 0 200px 10px 10px !important;
      margin-left: 16px;
      background: #fff;
      border-radius: 4px;
      ::v-deep .el-input {
        width: 200px;
      }
      & > div {
        margin-right: 10px;
        margin-top: 10px;
      }
    }
    .right-content {
      margin-left: 16px;
      margin-top: 16px;
      background: #fff;
      padding: 10px;
      border-radius: 4px;
      flex: 1;
      overflow: hidden;
      .table-content {
        height: calc(100% - 0px);
        ::v-deep .el-table .el-table__body-wrapper td.el-table__cell {
          border-right: 1px solid #ededf5;
        }
      }
    }
  }
}
</style>
<style lang="scss">
.alarm-configuration {
  .operationBtn-span {
    margin-right: 10px;
    color: #3562db;
  }
}
</style>
