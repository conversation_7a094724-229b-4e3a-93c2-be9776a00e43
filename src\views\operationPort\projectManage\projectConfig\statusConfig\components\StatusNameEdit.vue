<script>
export default {
  name: 'StatusNameEdit',
  props: {
    visible: <PERSON><PERSON><PERSON>,
    name: String
  },
  events: ['update:visible', 'update'],
  data: function() {
    return {
      formModel: {
        name: ''
      },
      rules: {
        name: [{ required: true, message: '请输入状态机名称' }]
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    }
  },
  watch: {
    dialogVisible(value) {
      if (value) {
        this.formModel.name = this.name
      }
    }
  },
  methods: {
    onDialogClosed() {
      this.$refs.formRef.resetFields()
    },
    // 点击提交
    onSubmit() {
      this.$refs.formRef.validate().then(() => {
        this.$emit('update', this.formModel.name)
      })
    }
  }
}
</script>
<template>
  <el-dialog
    v-dialogDrag
    class="component status-name-edit"
    title="批量编辑"
    width="750px"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    custom-class="model-dialog"
    :modal="false"
    @closed="onDialogClosed"
  >
    <el-form ref="formRef" :model="formModel" :rules="rules" label-width="95px">
      <el-form-item label="状态机名称" prop="name">
        <el-input v-model="formModel.name" placeholder="请输入"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" plain @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="onSubmit">确认</el-button>
    </template>
  </el-dialog>
</template>
<style lang="scss">
.component.status-name-edit {
  .el-form {
    width: 100%;
    background-color: #fff;
    padding: 10px 16px 0;
  }
}
</style>
