<!-- 数据报表 -->
<template>
  <PageContainer>
    <div slot="header" class="pow_header">
      <div class="title">
        <div class="title_p">数据报表</div>
      </div>
      <div class="tabs">
        <el-tabs v-model="queryParams.menuCode" @tab-click="selectEntityGroup()">
          <el-tab-pane
          v-for="item in tabs"
          :key="item.code"
          :label="item.name"
          :name="item.code"
          >
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <div slot="content" class="pow_content">
      <div class="content_left">
        <div class="all_tree">
          <el-tabs v-model="isKeyDevice">
            <el-tab-pane label="配电设备" name="1"> </el-tab-pane>
            <el-tab-pane label="重点设备" name="0"> </el-tab-pane>
          </el-tabs>
          <el-input
            v-model="filterText"
            suffix-icon="el-icon-search"
            style="margin-bottom: 16px;"
            placeholder="输入关键字进行过滤"
          >
          </el-input>
          <div class="my_tree">
            <el-tree
              ref="tree"
              class="filter-tree"
              :data="treeData"
              node-key="code"
              show-checkbox
              :props="{
                children: 'children',
                label: 'name',
              }"
              :render-content="renderContent"
              default-expand-all
              :default-checked-keys="defaultChecked"
              :filter-node-method="filterNode"
              @check="handleCheck"
            >
              <span slot-scope="{ node }" class="custom-tree-node">
                <el-tooltip class="item" effect="dark" :content="node.label" placement="top-start">
                  <span>{{ node.label }}</span>
                </el-tooltip>
              </span>
            </el-tree>
          </div>
        </div>
      </div>
      <div class="content_right">
        <div class="typeTabs">
          <el-tabs v-model="choiceTypeTab">
            <el-tab-pane
            v-for="item in typeTabs"
            :key="item.type"
            style="height: calc(100%);"
            :label="item.name"
            :name="item.type"
            >
              <div class="query_row">
                <div class="query_left">
                  <div v-if="item.dateTypeArr.length > 1">
                    <div
                      v-for="(v, i) in item.dateTypeArr"
                      :key="i"
                      :class="{
                        'search-aside-item': true,
                        'search-aside-item-active': selectedTimeType.dateType === v.dateType
                      }"
                      @click="onDateType(v)"
                    >
                      {{ v.name }}
                    </div>
                  </div>
                  <!-- 日选择 -->
                  <div v-if="selectedTimeType.dateType == 'day'">
                    <el-date-picker
                      key="day"
                      v-model="dateTime.day"
                      type="date"
                      placeholder="请选择日期"
                      :picker-options="pickerOptions"
                      :clearable="false"
                      value-format="yyyy-MM-dd"
                    >
                    </el-date-picker>
                  </div>
                  <!-- 月选择 -->
                  <div v-else-if="selectedTimeType.dateType == 'month'">
                    <el-date-picker
                      key="month"
                      v-model="dateTime.month"
                      type="month"
                      placeholder="请选择日期"
                      :picker-options="pickerOptions"
                      :clearable="false"
                      value-format="yyyy-MM-dd"
                    >
                    </el-date-picker>
                  </div>
                  <!-- 年选择 -->
                  <div v-else-if="selectedTimeType.dateType == 'year'">
                    <el-date-picker
                      key="year"
                      v-model="dateTime.year"
                      type="year"
                      placeholder="请选择日期"
                      :picker-options="pickerOptions"
                      :clearable="false"
                      value-format="yyyy-MM-dd"
                    >
                    </el-date-picker>
                  </div>
                  <!-- 自定义时间范围选择 -->
                  <div v-else>
                    <el-date-picker
                      key="daterange"
                      v-model="dateTime.custom"
                      type="daterange"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      :picker-options="pickerOptions"
                      :clearable="false"
                      value-format="yyyy-MM-dd"
                    >
                    </el-date-picker>
                  </div>
                  <div v-if="selectedTimeType.dateType != 'custom'" class="up_down">
                    <span class="jump_date" @click="jumpDate('subtract')">上一{{ selectedTimeType.unit }}</span>
                    <span class="jump_date" :class="{ 'no_clicl': isNext }" @click="jumpDate('add')">下一{{ selectedTimeType.unit }}</span>
                  </div>
                  <el-button type="primary" plain class="re_btn" @click="reset">重置</el-button>
                  <el-button type="primary" @click="submit">查询</el-button>
                </div>
                <div class="query_right">
                  <el-button type="primary" icon="el-icon-upload2" @click="exportExcel()">导出</el-button>
                  <el-button v-if="choiceTypeTab == 3" type="primary" icon="el-icon-setting" @click="openTimeSlot">分时段配置</el-button>
                </div>
              </div>
              <div v-if="item.typeArr" class="btn_row">
                <div
                  v-for="(v, i) in item.typeArr"
                  :key="i"
                  :class="{
                    'search-aside-item': true,
                    'search-aside-item-active': queryParams.paramIds === v.paramIds
                  }"
                  @click="onTypeQuick(v)"
                >
                  {{ v.name }}
                </div>
              </div>
              <div class="my_tabel">
                <TablePage
                  id="my-table"
                  ref="table"
                  v-loading="tabelLoading"
                  :showPage="false"
                  :tableColumn="tableColumn"
                  :span-method="objectSpanMethod"
                  :data="tableData"
                  height="100%"
                />
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      <TimeSlotConfigurationVue ref="timeSlotConfiguration" />
    </div>
  </PageContainer>
</template>

<script lang="jsx">
import moment from 'moment'
import FileSaver from 'file-saver'
import * as XLSX from 'xlsx'
import reportForms from './mixin/reportForms'
import TimeSlotConfigurationVue from './components/timeSlotConfiguration.vue'
export default {
  name: 'intervalState',
  components: {
    TimeSlotConfigurationVue
  },
  mixins: [ reportForms ],
  data() {
    return {
      isKeyDevice: '1', // 是否早重点设备
      tableColumn: [],
      tableData: [],
      tabelLoading: false,
      selectedTimeType: {},   // 选择的筛选时间对象
      activeTab: '0',    // 图形列表切换   0 图形  1 列表
      queryParams: {
        dateType: null,       //  时间类型
        endTime: null,
        menuCode: null,      //  顶部菜单code
        startTime: null,
        surveyCodes: '',      //  左侧实体code
        paramIds: null,   //  选择的类型
        paramNames: null
      },
      dateTime: {
        day: null,
        month: null,
        year: null,
        custom: []
      },  // 查询的日期时间
      choiceTypeTab: null,    // 类型  电力数据表、电力极值报表、用能报表、分时段用电
      tabs: [],
      filterText: '',    //  分组input搜索
      treeData: [],   // 左侧tree
      defaultChecked: [],  // 左侧tree 默认
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      },
      isShowTooltip: false
    }
  },
  computed: {
    isNext() {
      if (this.selectedTimeType.dateType == 'day' && this.dateTime.day) {
        return moment(this.dateTime.day).add(1, 'days').valueOf() > Date.now()
      } else if (this.selectedTimeType.dateType == 'month' && this.dateTime.month) {
        return moment(this.dateTime.month).add(1, 'month').valueOf() > Date.now()
      } else if (this.selectedTimeType.dateType == 'year' && this.dateTime.year) {
        return moment(this.dateTime.year).add(1, 'year').valueOf() > Date.now()
      } else {
        return false
      }
    }
  },
  watch: {
    'choiceTypeTab': function(val) {
      let obj = this.typeTabs.find(ele => ele.type == val)
      this.onDateType(obj.dateTypeArr[0])
      this.onTypeQuick(obj.typeArr[0])
    },
    'filterText': function(val) {
      this.$refs.tree.filter(val)
    },
    isKeyDevice(val) {
      this.selectEntityGroup()
    }
  },
  mounted() {
    // this.toTheRightOfTime()
    this.GetEntityMenuList()
    this.getSelectExtremumTree()
    this.choiceTypeTab = this.typeTabs[0].type
  },
  methods: {
    renderContent(h, { node, data, store }) {
      const notTeamNodeRole = data.notTeamNodeRole?.split(',') ?? []
      // const nowProjectName = monitorTypeList.find(item => item.projectCode == this.projectCode).projectName
      // 空调监测为非树形结构
      // <el-tooltip class="item" effect="dark" content={ node.label } placement="top-start" disabled={ !this.isShowTooltip }>
      //   <span onMouseenter={(e) => { this.visibilityChange(e) }}>{node.label}</span>
      // </el-tooltip>
      return (
        <span class="custom-tree-node">
          <el-tooltip class="item" effect="dark" content={node.label} placement="top-start" disabled={!this.isShowTooltip}>
            <span
              onMouseenter={(e) => {
                this.visibilityChange(e)
              }}
            >
              {node.label}
            </span>
          </el-tooltip>
        </span>
      )
    },
    visibilityChange(e) {
      const item = e.target
      const itemWidth = item.offsetWidth
      const itemScrollWidth = item.scrollWidth
      if (itemWidth < itemScrollWidth) {
        this.isShowTooltip = true
      } else {
        this.isShowTooltip = false
      }
    },
    // 电力数据表
    handlePowerDataSurface(data) {
      // 表头
      this.tableColumn = [
        {
          label: '设备名称',
          prop: 'surveyName',
          width: 160
        },
        {
          label: '参数名称',
          prop: 'paramName',
          width: 160
        }
      ]
      data[0].date.forEach(ele => {
        this.tableColumn = [
          ...this.tableColumn,
          {
            label: ele.time.substring(ele.time.length - 2) + '时',
            prop: ele.time
          }
        ]
      })
      // 表数据
      this.tableData = data.reduce((arr, item) => {
        return [
          ...arr,
          {
            surveyName: item.surveyName,
            surveyCode: item.surveyCode,
            paramName: item.paramName,
            ...item.date.reduce((obj, ele) => {
              obj[ele.time] = ele.date
              return obj
            }, {})
          }
        ]
      }, [])
    },
    // 电力极值报表
    handleExtremeValueOfElectricity(data) {
      console.log(111111, data)
      // 表头
      this.tableColumn = [
        {
          label: '设备名称',
          prop: 'surveyName',
          width: 160
        },
        {
          label: '日期',
          prop: 'time',
          width: 160
        }
      ]
      data[0].list.forEach(ele => {
        let obj = {
          label: ele.paramName,
          align: 'center',
          subMerge: []
        }
        // 最大值、发生时间、最小值、发生时间、平均值
        obj.subMerge = [
          {
            label: '最大值',
            prop: `${ ele.paramId }maxValue`
          },
          {
            label: '发生时间',
            prop: `${ ele.paramId }maxTime`
          },
          {
            label: '最小值',
            prop: `${ ele.paramId }minValue`
          },
          {
            label: '发生时间',
            prop: `${ ele.paramId }minTime`
          },
          {
            label: '平均值',
            prop: `${ ele.paramId }avg`
          }
        ]
        this.tableColumn.push(obj)
      })
      // 表数据
      this.tableData = data.reduce((arr, item) => {
        return [
          ...arr,
          {
            surveyName: item.surveyName,
            surveyCode: item.surveyCode,
            time: item.time,
            ...item.list.reduce((obj, ele) => {
              obj[`${ele.paramId}maxValue`] = ele.maxValue
              obj[`${ele.paramId}maxTime`] = ele.maxTime
              obj[`${ele.paramId}minValue`] = ele.minValue
              obj[`${ele.paramId}minTime`] = ele.minTime
              obj[`${ele.paramId}avg`] = ele.avg
              return obj
            }, {})
          }
        ]
      }, [])
    },
    // 用能报表
    handleEnergyUsageSurface(data, dateType) {
      // 表头
      this.tableColumn = [
        {
          label: '设备名称',
          prop: 'surveyName',
          width: 160
        }
      ]
      data[0].list.forEach(ele => {
        this.tableColumn = [
          ...this.tableColumn,
          {
            label: ele.time + (dateType == 0 ? '时' : ''),
            minWidth: 100,
            prop: ele.time
          }
        ]
      })
      // 表数据
      this.tableData = data.reduce((arr, item) => {
        return [
          ...arr,
          {
            surveyName: item.surveyName,
            surveyCode: item.surveyCode,
            ...item.list.reduce((obj, ele) => {
              obj[ele.time] = ele.date
              return obj
            }, {})
          }
        ]
      }, [])
    },
    // 分时段用电
    handleDayparting(data) {
      // 表头
      this.tableColumn = [
        {
          label: '设备名称',
          prop: 'surveyName',
          width: 160
        },
        {
          label: '尖峰',
          prop: 'peakValue'
        },
        {
          label: '高峰',
          prop: 'highValue'
        },
        {
          label: '平时',
          prop: 'flatValue'
        },
        {
          label: '谷峰',
          prop: 'valleyValue'
        },
        {
          label: '用电总量',
          prop: 'total'
        }
      ]
      // 表数据
      this.tableData = data
    },
    getList() {
      this.tabelLoading = true
      this.handleDateChange()
      const typeTab = this.typeTabs.find(ele => ele.type == this.choiceTypeTab)
      const data = {
        ...this.queryParams,
        projectCode: this.projectCode
      }
      this.$api[typeTab.api](data).then(res => {
        this.tableColumn = []
        this.tableData = []
        // console.log('this.$refs.table==========', this.$refs)
        // this.$refs.table[typeTab.type].doLayout()
        this.$nextTick(() => {
          // 未找到更好解决  暂用setTimeout解决列表错位问题  滚动条划至右侧，切换上一日，表头和下面数据没对齐
          setTimeout(() => {
            if (res.data.length) {
              if (typeTab.type == '0') { // 电力数据表
                this.handlePowerDataSurface(res.data)
              } else if (typeTab.type == '1') {   // 电力极值报表
                this.handleExtremeValueOfElectricity(res.data)
              } else if (typeTab.type == '2') {   // 用能报表
                this.handleEnergyUsageSurface(res.data, data.dateType)
              } else if (typeTab.type == '3') {    // 分时段用电
                this.handleDayparting(res.data, data.dateType)
              }
            }
            this.tabelLoading = false
          }, 100)
        })
      })
    },
    getSelectExtremumTree() {
      this.$api.selectExtremumTree().then(res => {
        this.typeTabs[1].typeArr = res.data
      })
    },
    // 监测实体列合并
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      let arr = [0]
      if (arr.indexOf(columnIndex) != -1) {
        // 判断当前列是否为第一列
        if (rowIndex === 0 || row.surveyCode !== this.tableData[rowIndex - 1].surveyCode) {
          // 判断当前行是否为第一行或者与上一行的name属性不相同
          const rowspan = this.tableData.filter(
            item => item.surveyCode === row.surveyCode
          ).length
          // 计算需要合并的行数
          return {
            rowspan,
            colspan: 1
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
    },
    handleDateChange() {
      // 时间类型(0:日 1:月 2:年 3:自定义)
      let type = this.queryParams.dateType
      if (type == 0) {
        this.queryParams.startTime = this.dateTime.day
        this.queryParams.endTime = this.dateTime.day
      } else if (type == 1) {
        this.queryParams.startTime = moment(this.dateTime.month).startOf('month').format('YYYY-MM-DD')
        this.queryParams.endTime = moment(this.dateTime.month).endOf('month').format('YYYY-MM-DD')
      } else if (type == 2) {
        this.queryParams.startTime = moment(this.dateTime.year).startOf('year').format('YYYY-MM-DD')
        this.queryParams.endTime = moment(this.dateTime.year).endOf('year').format('YYYY-MM-DD')
      } else if (type == 3) {
        this.queryParams.startTime = this.dateTime.custom[0]
        this.queryParams.endTime = this.dateTime.custom[1]
      }
    },
    handleCheck(data, checked) {
      console.log('checked==========', checked)
      this.queryParams.surveyCodes = checked.checkedNodes.filter(ele => ele.lv == '2').map(item => item.code).toString()
      this.getList()
    },
    selectEntityGroup() {
      this.queryParams.surveyCodes = ''
      let data = {
        entityMenuCode: this.queryParams.menuCode,
        projectCode: this.projectCode
      }
      if (this.isKeyDevice == 1) {
        this.$api.selectEntityGroup(data).then(res => {
          this.treeData = res.data.reduce((arr, item) => {
            let obj = {
              name: item.entityTypeName,
              code: item.entityTypeId,
              lv: '1',
              children: [...item.list.map(ele => {
                return {
                  name: ele.imsName,
                  code: ele.imsCode,
                  lv: '2'
                }
              })]
            }
            return [...arr, obj]
          }, [])
          if (this.treeData.length && this.treeData[0].children.length) {
            this.defaultChecked = [this.treeData[0].children[0].code]
            this.queryParams.surveyCodes = this.treeData[0].children[0].code
          }
          this.getList()
        })
      } else {
        this.$api.GetElectricityKeyDevice({...data, keyDevice: this.isKeyDevice}).then(res => {
          this.treeData = res.data.map(item => {
            return {
              name: item.imsName,
              code: item.imsCode,
              lv: '2'
            }
          })
          if (this.treeData.length) {
            this.defaultChecked = [this.treeData[0].code]
            this.queryParams.surveyCodes = this.treeData[0].code
          }
          this.getList()
        })
      }
    },
    GetEntityMenuList() {
      this.$api.GetEntityMenuList({ projectId: this.projectCode }).then(res => {
        this.tabs = res.data.filter(ele => ele.level == 1)
        this.queryParams.menuCode = this.tabs[0].code
        this.selectEntityGroup()
      })
    },
    reset() {
      this.resetDateTime()
      this.getList()
    },
    submit() {
      this.getList()
    },
    jumpDate(type) {
      if (this.selectedTimeType.dateType == 'day' && this.dateTime.day) {
        this.dateTime.day = moment(this.dateTime.day)[type](1, 'days').format('YYYY-MM-DD')
      } else if (this.selectedTimeType.dateType == 'month' && this.dateTime.month) {
        this.dateTime.month = moment(this.dateTime.month)[type](1, 'month').format('YYYY-MM-DD')
      } else if (this.selectedTimeType.dateType == 'year' && this.dateTime.year) {
        this.dateTime.year = moment(this.dateTime.year)[type](1, 'year').format('YYYY-MM-DD')
      }
      this.getList()
    },
    onDateType(v) {
      this.selectedTimeType = v
      this.queryParams.dateType = v.status
      this.resetDateTime()
    },
    resetDateTime() {
      let current = moment().format('YYYY-MM-DD')
      this.dateTime = {
        day: current,
        month: current,
        year: current,
        custom: [current, current]
      }
    },
    openTimeSlot() {
      console.log('this.$refs==========', this.$refs)
      this.$refs.timeSlotConfiguration.getData()
    },
    exportExcel() {
      if (!this.tableData.length) return this.$message.error('暂无导出数据')

      /* 从表生成工作簿对象 */
      var wb = XLSX.utils.table_to_book(document.querySelector('#my-table'))
      /* 获取二进制字符串作为输出 */
      var wbout = XLSX.write(wb, {
        bookType: 'xlsx',
        bookSST: true,
        type: 'array'
      })
      try {
        // Blob 对象表示一个不可变、原始数据的类文件对象。
        // Blob 表示的不一定是JavaScript原生格式的数据。
        // File 接口基于Blob，继承了 blob 的功能并将其扩展使其支持用户系统上的文件。
        // 返回一个新创建的 Blob 对象，其内容由参数中给定的数组串联组成。
        FileSaver.saveAs(
          new Blob([wbout], { type: 'application/octet-stream' }),
          // 设置导出文件名称
          '数据报表.xlsx')
      } catch (e) {
        if (typeof console !== 'undefined') console.log(e, wbout)
      } return wbout
    },
    onTypeQuick(item) {
      if (item) {
        this.queryParams.paramIds = item.paramIds
        this.queryParams.paramNames = item.paramNames
      }
      this.getList()
    },
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    /**
     * 将时间选择器的icon放在右边
    */
    toTheRightOfTime() {
      let ElRangeCloseIcon = document.getElementsByClassName('el-range__close-icon')[0]
      ElRangeCloseIcon.innerHTML = '<i class="el-icon-date"></i>'
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-tab-pane {
  height: calc(100%) !important;
}

::v-deep .el-tabs {
  height: calc(100%) !important;
}

::v-deep .el-tabs__content {
  height: calc(100%) !important;
}

::v-deep .el-table thead.is-group th.el-table__cell {
  background-color: #ededf5;
}

.search-aside-item {
  display: inline-block;
  font-size: 14px;
  padding: 0 30px;
  height: 32px;
  line-height: 32px;
  font-family: PingFangSC-Regular;
  color: $color-primary;
  border: 1px solid $color-primary;
  background: #fff;
  margin-right: 20px;
  border-radius: 4px;
  cursor: pointer;

  &:hover,
  &:focus {
    color: #fff;
    font-family: PingFangSC-Regular;
    border-color: $color-primary;
    background-color: $color-primary;
    font-weight: 500;
  }
}

.search-aside-item-active {
  color: #fff;
  font-family: PingFangSC-Regular;
  border-color: $color-primary;
  background-color: $color-primary;
  font-weight: 500;
}

::v-deep .el-tabs__header .el-tabs__nav-wrap::after {
  height: 1px !important;
  background-color: #dcdfe6 !important;
}

.pow_header {
  background: #fff;
  border-radius: 4px;

  .title {
    border-bottom: 1px solid #dcdfe6;

    &_p {
      padding: 19px 16px;
    }
  }

  .tabs {
    padding: 0 16px;
  }
}

.pow_content {
  margin-top: 16px;
  // background: #fff;
  height: 100%;
  display: flex;

  .content_left {
    background: #fff;
    width: 220px;
    height: 100%;
    margin-right: 16px;

    .all_tree {
      height: 100%;
      padding: 16px;
      ::v-deep .el-tabs {
        height: auto !important;
        margin-bottom: 16px;
        .el-tabs__content {
          display: none;
        }
      }
      .my_tree {
        height: calc(100% - 75px - 32px);
        overflow-y: auto;
        overflow-x: hidden;
        ::v-deep .el-tree-node__content {
          width: 100%;

          .custom-tree-node {
            display: inline-block;
            width: 100%;
            height: 21px;
            .item {
              display: inline-block;
              width: calc(100% - 40px);
              text-overflow: ellipsis;
              white-space: nowrap;
              overflow: hidden;
            }
          }
        }
      }
    }
  }

  .content_right {
    background: #fff;
    width: 0;
    flex: 1;

    .typeTabs {
      padding: 0 16px;
      height: 100%;
      // border-bottom: 1px solid #dcdfe6;
    }

    .query_row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 16px 0;
      padding: 0 16px;

      .query_left {
        display: flex;
        align-items: center;

        .up_down {
          display: flex;
          margin-right: 6px;

          .jump_date {
            cursor: pointer;
            margin-left: 16px;
            font-size: 14px;
            color: #3562db;
          }

          .no_clicl {
            cursor: not-allowed;
            opacity: 0.5;
            pointer-events: none;
            color: #414653;
          }
        }

        .re_btn {
          margin-left: 10px;
        }
      }

      .query_right {
        display: flex;
      }
    }

    .btn_row {
      padding: 0 16px;
      background: #fff;
      border-radius: 4px;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      margin-bottom: 16px;

      .harmonicOrder {
        display: flex;
        align-items: center;

        span {
          font-size: 14px;
        }
      }
    }

    .my_tabel {
      padding: 0 16px;
      height: calc(100% - 180px);
    }
  }
}
</style>
