import Layout from '@/layout'
import EmptyLayout from '@/layout/empty'
export default [
  {
    path: '/homePage',
    component: Layout,
    redirect: '/homePage/index',
    name: 'homePage',
    meta: {
      title: '首页',
      menuAuth: '/homePage/index'
    },
    children: [
      {
        path: 'index',
        name: 'homePage',
        component: () => import('@/views/securityCenter/homePage/index.vue'),
        meta: {
          title: '首页',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/homePage'
        }
      },
      {
        path: 'RiskFourColorChart',
        name: 'RiskFourColorHome',
        component: () => import('@/views/securityCenter/riskControl/RiskFourColorChart/index.vue'),
        meta: {
          title: '风险四色图',
          sidebar: false,
          activeMenu: '/homePage/index'
        }
      },
      {
        path: 'riskList',
        name: 'riskList',
        component: () => import('@/views/securityCenter/riskControl/riskPointManagementList/riskList.vue'),
        meta: {
          title: '风险点列表',
          sidebar: false,
          activeMenu: '/homePage/index'
        }
      },
      {
        path: 'dangerList',
        name: 'dangerList',
        component: () => import('@/views/securityCenter/hiddenDanger/dangerList.vue'),
        meta: {
          title: '隐患列表',
          sidebar: false,
          activeMenu: '/homePage/index'
        }
      },
      {
        path: 'hiddenManagementDetails',
        name: 'hiddenManagementDetails3',
        component: () => import('@/views/securityCenter/hiddenDanger/hiddenManagementDetails.vue'),
        meta: {
          title: '隐患详情',
          sidebar: false,
          activeMenu: '/homePage/index'
        }
      },
      {
        path: 'ipsmAddRisk',
        name: 'ipsmAddRisk1',
        component: () => import('@/views/securityCenter/riskControl/riskPointManagementList/addRisk.vue'),
        meta: {
          title: '新增风险点',
          sidebar: false,
          activeMenu: '/homePage/index'
        }
      }
    ]
  },
  {
    path: '/riskControl',
    component: Layout,
    redirect: '/riskControl/riskPointManagementList',
    name: 'riskControl',
    meta: {
      title: '风险管控',
      menuAuth: '/riskControl'
    },
    children: [
      {
        path: 'riskPointManagementList',
        component: EmptyLayout,
        redirect: { name: 'riskPointManagementList' },
        meta: {
          title: '风险点管理',
          menuAuth: '/riskControl/riskPointManagementList'
        },
        children: [
          {
            path: '',
            name: 'riskPointManagementList',
            component: () => import('@/views/securityCenter/riskControl/riskPointManagementList/index.vue'),
            meta: {
              title: '风险点管理',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'ipsmAddRisk',
            name: 'ipsmAddRisk',
            component: () => import('@/views/securityCenter/riskControl/riskPointManagementList/addRisk.vue'),
            meta: {
              title: '新增风险点',
              sidebar: false,
              activeMenu: '/riskControl/riskPointManagementList'
            }
          }
        ]
      },
      {
        path: 'RiskFourColorChart',
        component: EmptyLayout,
        redirect: { name: 'RiskFourColorChart' },
        meta: {
          title: '风险四色图',
          menuAuth: '/riskControl/RiskFourColorChart'
        },
        children: [
          {
            path: '',
            name: 'RiskFourColorChart',
            component: () => import('@/views/securityCenter/riskControl/RiskFourColorChart/index.vue'),
            meta: {
              title: '风险四色图',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'RiskPointsChecklist',
        component: EmptyLayout,
        redirect: { name: 'RiskPointsChecklist' },
        meta: {
          title: '风险巡查',
          menuAuth: '/riskControl/RiskPointsChecklist'
        },
        children: [
          {
            path: '',
            name: 'RiskPointsChecklist',
            component: () => import('@/views/securityCenter/riskControl/RiskPointsChecklist/index.vue'),
            meta: {
              title: '风险巡查',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'riskDetail',
            name: 'ipsmRiskDetail',
            component: () => import('@/views/securityCenter/riskControl/RiskPointsChecklist/riskDetail.vue'),
            meta: {
              title: '新增巡查清单',
              sidebar: false,
              activeMenu: '/riskControl/RiskPointsChecklist'
            }
          },
          {
            path: 'hiddenManagementDetails',
            name: 'hiddenManagementDetails2',
            component: () => import('@/views/securityCenter/hiddenDanger/hiddenManagementDetails.vue'),
            meta: {
              title: '详情',
              sidebar: false,
              activeMenu: '/riskControl/RiskPointsChecklist'
            }
          },
          {
            path: 'ipsmListsDetail',
            name: 'ipsmListsDetail',
            component: () => import('@/views/securityCenter/riskControl/RiskPointsChecklist/listsDetail.vue'),
            meta: {
              title: '详情',
              sidebar: false,
              activeMenu: '/riskControl/RiskPointsChecklist'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/safetyAccount',
    component: Layout,
    redirect: '/safetyAccount/securityControl',
    name: 'safetyAccount',
    meta: {
      title: '安全台账管理',
      menuAuth: '/safetyAccount'
    },
    children: [
      {
        path: 'UnitBasicInformation',
        component: EmptyLayout,
        redirect: { name: 'UnitBasicInformation' },
        meta: {
          title: '单位基础信息',
          menuAuth: '/safetyAccount/UnitBasicInformation'
        },
        children: [
          {
            path: '',
            name: 'UnitBasicInformation',
            component: () => import('@/views/securityCenter/safetyAccount/UnitBasicInformation/index.vue'),
            meta: {
              title: '单位基础信息',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'KeyLedgeManagement',
        component: EmptyLayout,
        redirect: { name: 'KeyLedgeManagement' },
        meta: {
          title: '重点台账管理',
          menuAuth: '/safetyAccount/KeyLedgeManagement'
        },
        children: [
          {
            path: '',
            name: 'KeyLedgeManagement',
            component: () => import('@/views/securityCenter/safetyAccount/KeyLedgeManagement/index.vue'),
            meta: {
              title: '重点台账管理',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'addKeyLedge',
            name: 'addKeyLedge',
            component: () => import('@/views/securityCenter/safetyAccount/KeyLedgeManagement/addKeyLedge.vue'),
            meta: {
              title: '新增',
              sidebar: false,
              activeMenu: '/safetyAccount/KeyLedgeManagement'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/safetySystem',
    component: Layout,
    redirect: '/safetySystem/SpaceGridManagement',
    name: 'safetySystem',
    meta: {
      title: '安全体系管理',
      menuAuth: '/safetySystem'
    },
    children: [
      {
        path: 'SpaceGridManagement',
        component: EmptyLayout,
        redirect: { name: 'SpaceGridManagement' },
        meta: {
          title: '空间网格管理',
          menuAuth: '/safetySystem/SpaceGridManagement'
        },
        children: [
          {
            path: '',
            name: 'SpaceGridManagement',
            component: () => import('@/views/securityCenter/safetyAccount/SpaceGridManagement/index.vue'),
            meta: {
              title: '空间网格管理',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'securityControl',
        component: EmptyLayout,
        redirect: { name: 'securityControl' },
        meta: {
          title: '安全管控组织',
          menuAuth: '/safetySystem/securityControl'
        },
        children: [
          {
            path: '',
            name: 'securityControl',
            component: () => import('@/views/securityCenter/safetyAccount/securityControl/index.vue'),
            meta: {
              title: '安全管控组织',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'addSecurity',
            name: 'addSecurity',
            component: () => import('@/views/securityCenter/safetyAccount/securityControl/addSecurity.vue'),
            meta: {
              title: '新增',
              sidebar: false,
              activeMenu: '/safetySystem/securityControl'
            }
          }
        ]
      },
      {
        path: 'userControlGroup',
        component: EmptyLayout,
        redirect: { name: 'userControlGroup' },
        meta: {
          title: '安全人员管理',
          menuAuth: '/safetySystem/userControlGroup'
        },
        children: [
          {
            path: '',
            name: 'userControlGroup',
            component: () => import('@/views/securityCenter/safetyAccount/userControlGroup/index.vue'),
            meta: {
              title: '安全人员管理',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'addControl',
            name: 'addControl',
            component: () => import('@/views/securityCenter/safetyAccount/userControlGroup/addControl.vue'),
            meta: {
              title: '新增',
              sidebar: false,
              activeMenu: '/safetySystem/userControlGroup'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/newInspectionManagement',
    component: Layout,
    redirect: '/newInspectionManagement/assignmentManagement',
    name: 'assignmentManagement',
    meta: {
      title: '巡检管理',
      menuAuth: '/newInspectionManagement'
    },
    children: [
      {
        path: 'assignmentManagement',
        component: EmptyLayout,
        redirect: { name: 'assignmentManagement' },
        meta: {
          title: '任务书管理',
          menuAuth: '/newInspectionManagement/assignmentManagement'
        },
        children: [
          {
            path: '',
            name: 'assignmentManagement',
            component: () => import('@/views/securityCenter/inspectionManagement/assignmentManagement/assignmentManagement.vue'),
            meta: {
              title: '任务书管理',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'addAssignment',
            name: 'addAssignment',
            component: () => import('@/views/securityCenter/inspectionManagement/assignmentManagement/addAssignment.vue'),
            meta: {
              title: '新增任务书',
              sidebar: false,
              activeMenu: '/newInspectionManagement/assignmentManagement'
            }
          },
          {
            path: 'assignmentDetail',
            name: 'assignmentDetail',
            component: () => import('@/views/securityCenter/inspectionManagement/assignmentManagement/assignmentDetail.vue'),
            meta: {
              title: '任务书详情',
              sidebar: false,
              activeMenu: '/newInspectionManagement/assignmentManagement'
            }
          }
        ]
      },
      {
        path: 'planManagement',
        component: EmptyLayout,
        redirect: { name: 'planManagement' },
        meta: {
          title: '计划管理',
          menuAuth: '/newInspectionManagement/planManagement'
        },
        children: [
          {
            path: '',
            name: 'planManagement',
            component: () => import('@/views/securityCenter/inspectionManagement/planManagement/planManagement.vue'),
            meta: {
              title: '计划管理',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'addPlans',
            name: 'addPlans',
            component: () => import('@/views/securityCenter/inspectionManagement/planManagement/addPlans.vue'),
            meta: {
              title: '新增计划',
              sidebar: false,
              activeMenu: '/newInspectionManagement/planManagement'
            }
          },
          {
            path: 'planDetail',
            name: 'planDetail',
            component: () => import('@/views/securityCenter/inspectionManagement/planManagement/planDetails.vue'),
            meta: {
              title: '计划详情',
              sidebar: false,
              activeMenu: '/newInspectionManagement/planManagement'
            }
          }
        ]
      },
      {
        path: 'newTaskManagement',
        component: EmptyLayout,
        redirect: { name: 'newTaskManagement' },
        meta: {
          title: '任务管理',
          menuAuth: '/newInspectionManagement/newTaskManagement'
        },
        children: [
          {
            path: '',
            name: 'newTaskManagement',
            component: () => import('@/views/securityCenter/inspectionManagement/taskManagement/taskManagement.vue'),
            meta: {
              title: '任务管理',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'scheduleDetails',
            name: 'scheduleDetails',
            component: () => import('@/views/securityCenter/inspectionManagement/taskManagement/scheduleDetails.vue'),
            meta: {
              title: '任务详情',
              sidebar: false,
              activeMenu: '/newInspectionManagement/newTaskManagement'
            }
          },
          {
            path: 'recordDetails',
            name: 'recordDetails',
            component: () => import('@/views/securityCenter/inspectionManagement/taskManagement/recordDetails.vue'),
            meta: {
              title: '巡检点详情',
              sidebar: false,
              activeMenu: '/newInspectionManagement/newTaskManagement'
            }
          }
        ]
      },
      {
        path: 'locationPoint',
        component: EmptyLayout,
        redirect: { name: 'locationPoint' },
        meta: {
          title: '定位点管理',
          menuAuth: '/newInspectionManagement/locationPoint'
        },
        children: [
          {
            path: '',
            name: 'locationPoint',
            component: () => import('@/views/securityCenter/inspectionManagement/locationPointManagement/locationPoint.vue'),
            meta: {
              title: '定位点管理',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'addLocaltionPoint',
            name: 'addLocaltionPoint',
            component: () => import('@/views/securityCenter/inspectionManagement/locationPointManagement/addLocaltionPoint.vue'),
            meta: {
              title: '新增定位点',
              sidebar: false,
              activeMenu: '/newInspectionManagement/locationPoint'
            }
          }
        ]
      },
      {
        path: 'taskPointManagement',
        component: EmptyLayout,
        redirect: { name: 'taskPointManagement' },
        meta: {
          title: '巡检点管理',
          menuAuth: '/newInspectionManagement/taskPointManagement'
        },
        children: [
          {
            path: '',
            name: 'taskPointManagement',
            component: () => import('@/views/securityCenter/inspectionManagement/taskPointManagement/taskPoint.vue'),
            meta: {
              title: '巡检点管理',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'addTaskPoint',
            name: 'addTaskPoint',
            component: () => import('@/views/securityCenter/inspectionManagement/taskPointManagement/addTaskPoint.vue'),
            meta: {
              title: '新增巡检点',
              sidebar: false,
              activeMenu: '/newInspectionManagement/taskPointManagement'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/hiddenDanger',
    component: Layout,
    redirect: '/hiddenDanger/hiddenPool',
    name: 'hiddenPool',
    meta: {
      title: '隐患管理',
      menuAuth: '/hiddenDanger'
    },
    children: [
      {
        path: 'hiddenPool',
        component: EmptyLayout,
        redirect: { name: 'hiddenPool' },
        meta: {
          title: '隐患池',
          menuAuth: '/hiddenDanger/hiddenPool'
        },
        children: [
          {
            path: '',
            name: 'hiddenPool',
            component: () => import('@/views/securityCenter/hiddenDanger/hiddenPool.vue'),
            meta: {
              title: '隐患池',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'hiddenDetails',
            name: 'hiddenDetails',
            component: () => import('@/views/securityCenter/hiddenDanger/hiddenDetails.vue'),
            meta: {
              title: '详情',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'auditManagement',
        component: EmptyLayout,
        redirect: { name: 'auditManagement' },
        meta: {
          title: '审核管理',
          menuAuth: '/hiddenDanger/auditManagement'
        },
        children: [
          {
            path: '',
            name: 'auditManagement',
            component: () => import('@/views/securityCenter/hiddenDanger/auditManagement.vue'),
            meta: {
              title: '审核管理',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'hiddenManagementDetails',
            name: 'hiddenManagementDetails',
            component: () => import('@/views/securityCenter/hiddenDanger/hiddenManagementDetails.vue'),
            meta: {
              title: '详情',
              sidebar: false,
              activeMenu: '/hiddenDanger/auditManagement'
            }
          }
        ]
      },
      {
        path: 'dangerManagement',
        component: EmptyLayout,
        redirect: { name: 'dangerManagement' },
        meta: {
          title: '隐患管理',
          menuAuth: '/hiddenDanger/dangerManagement'
        },
        children: [
          {
            path: '',
            name: 'dangerManagement',
            component: () => import('@/views/securityCenter/hiddenDanger/dangerManagement.vue'),
            meta: {
              title: '隐患管理',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'hiddenManagementDetails',
            name: 'hiddenManagementDetails1',
            component: () => import('@/views/securityCenter/hiddenDanger/hiddenManagementDetails.vue'),
            meta: {
              title: '隐患详情',
              sidebar: false,
              activeMenu: '/hiddenDanger/dangerManagement',
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'dangerReporting',
        component: EmptyLayout,
        redirect: { name: 'dangerReporting' },
        meta: {
          title: '隐患上报',
          menuAuth: '/hiddenDanger/dangerReporting'
        },
        children: [
          {
            path: '',
            name: 'dangerReporting',
            component: () => import('@/views/securityCenter/hiddenDanger/dangerReporting.vue'),
            meta: {
              title: '隐患上报',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      }
    ]
  },
  {
    path: '/assessmentManagement',
    component: Layout,
    redirect: '/assessmentManagement/punishmentSystemList',
    name: 'punishmentSystemList',
    meta: {
      title: '考核管理',
      menuAuth: '/assessmentManagement'
    },
    children: [
      {
        path: 'punishmentSystemList',
        component: EmptyLayout,
        redirect: { name: 'punishmentSystemList' },
        meta: {
          title: '考核制度',
          menuAuth: '/assessmentManagement/punishmentSystemList'
        },
        children: [
          {
            path: '',
            name: 'punishmentSystemList',
            component: () => import('@/views/securityCenter/assessmentManagement/punishmentSystemList.vue'),
            meta: {
              title: '考核制度',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      }
    ]
  },
  {
    path: '/messageManagement',
    component: Layout,
    redirect: '/messageManagement/announcements',
    name: 'announcements',
    meta: {
      title: '消息管理',
      menuAuth: '/messageManagement'
    },
    children: [
      {
        path: 'announcements',
        component: EmptyLayout,
        redirect: { name: 'announcements' },
        meta: {
          title: '公告管理',
          menuAuth: '/messageManagement/announcements'
        },
        children: [
          {
            path: '',
            name: 'announcements',
            component: () => import('@/views/securityCenter/messageManagement/announcements.vue'),
            meta: {
              title: '公告管理',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'addNotice',
            name: 'addNotice',
            component: () => import('@/views/securityCenter/messageManagement/addNotice.vue'),
            meta: {
              title: '新建公告',
              sidebar: false,
              activeMenu: '/messageManagement/announcements'
            }
          }
        ]
      },
      {
        path: 'notifyList',
        component: EmptyLayout,
        redirect: { name: 'notifyList' },
        meta: {
          title: '知会管理',
          menuAuth: '/messageManagement/notifyList'
        },
        children: [
          {
            path: '',
            name: 'notifyList',
            component: () => import('@/views/securityCenter/messageManagement/notifyList.vue'),
            meta: {
              title: '知会管理',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'hiddenManagementDetails',
            name: 'hiddenManagementDetails3',
            component: () => import('@/views/securityCenter/hiddenDanger/hiddenManagementDetails.vue'),
            meta: {
              title: '详情',
              sidebar: false,
              activeMenu: '/messageManagement/notifyList'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/education',
    component: Layout,
    redirect: '/education/knowledgeList',
    name: 'knowledgeList',
    meta: {
      title: '教育培训',
      menuAuth: '/education'
    },
    children: [
      {
        path: 'knowledgeList',
        component: EmptyLayout,
        redirect: { name: 'knowledgeList' },
        meta: {
          title: '知识库管理',
          menuAuth: '/education/knowledgeList'
        },
        children: [
          {
            path: '',
            name: 'knowledgeList',
            component: () => import('@/views/securityCenter/education/knowledgeList.vue'),
            meta: {
              title: '知识库管理',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'newAttachment',
            name: 'newAttachment',
            component: () => import('@/views/securityCenter/education/newAttachment.vue'),
            meta: {
              title: '新增文档',
              sidebar: false,
              activeMenu: '/education/knowledgeList'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/systemSettingsAQ',
    component: Layout,
    redirect: '/systemSettingsAQ/configureManagement',
    name: 'configureManagement',
    meta: {
      title: '系统管理',
      menuAuth: '/systemSettingsAQ'
    },
    children: [
      {
        path: 'configureManagement',
        component: EmptyLayout,
        redirect: { name: 'configureManagement' },
        meta: {
          title: '用户配置',
          menuAuth: '/systemSettingsAQ/configureManagement'
        },
        children: [
          {
            path: '',
            name: 'configureManagement',
            component: () => import('@/views/securityCenter/systemSettings/configureManagement.vue'),
            meta: {
              title: '用户配置',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'operationRole',
        component: EmptyLayout,
        redirect: { name: 'operationRole' },
        meta: {
          title: '角色管理',
          menuAuth: '/systemSettingsAQ/operationRole'
        },
        children: [
          {
            path: '',
            name: 'operationRole',
            component: () => import('@/views/securityCenter/systemSettings/operationRole.vue'),
            meta: {
              title: '角色管理',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'controlGroup',
        component: EmptyLayout,
        redirect: { name: 'controlGroup' },
        meta: {
          title: '管控小组',
          menuAuth: '/systemSettingsAQ/controlGroup'
        },
        children: [
          {
            path: '',
            name: 'controlGroup',
            component: () => import('@/views/securityCenter/systemSettings/controlGroup.vue'),
            meta: {
              title: '管控小组',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'JudgedMethodManagement',
        component: EmptyLayout,
        redirect: { name: 'JudgedMethodManagement' },
        meta: {
          title: '研判方法管理',
          menuAuth: '/systemSettingsAQ/JudgedMethodManagement'
        },
        children: [
          {
            path: '',
            name: 'JudgedMethodManagement',
            component: () => import('@/views/securityCenter/systemSettings/JudgedMethodManagement.vue'),
            meta: {
              title: '研判方法管理',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'incentiveManagement',
        component: EmptyLayout,
        redirect: { name: 'incentiveManagement' },
        meta: {
          title: '奖惩配置',
          menuAuth: '/systemSettingsAQ/incentiveManagement'
        },
        children: [
          {
            path: '',
            name: 'incentiveManagement',
            component: () => import('@/views/securityCenter/systemSettings/incentiveManagement.vue'),
            meta: {
              title: '奖惩配置',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'DataDictionary',
        component: EmptyLayout,
        redirect: { name: 'DataDictionary' },
        meta: {
          title: '数据字典管理',
          menuAuth: '/systemSettingsAQ/DataDictionary'
        },
        children: [
          {
            path: '',
            name: 'DataDictionary',
            component: () => import('@/views/securityCenter/systemSettings/DataDictionary/index.vue'),
            meta: {
              title: '数据字典管理',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'parameterConfiguration',
        component: EmptyLayout,
        redirect: { name: 'parameterConfiguration' },
        meta: {
          title: '参数配置',
          menuAuth: '/systemSettingsAQ/parameterConfiguration'
        },
        children: [
          {
            path: '',
            name: 'parameterConfiguration',
            component: () => import('@/views/securityCenter/systemSettings/parameterConfiguration.vue'),
            meta: {
              title: '参数配置',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'RiskFourColorChartMangement',
        component: EmptyLayout,
        redirect: { name: 'RiskFourColorChartMangement' },
        meta: {
          title: '风险四色图管理',
          menuAuth: '/systemSettingsAQ/RiskFourColorChartMangement'
        },
        children: [
          {
            path: '',
            name: 'RiskFourColorChartMangement',
            component: () => import('@/views/securityCenter/systemSettings/RiskFourColorChartMangement.vue'),
            meta: {
              title: '风险四色图管理',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      }
    ]
  },
  {
    path: '/dataStatisticsList',
    component: Layout,
    redirect: '/dataStatisticsList/index',
    name: 'dataStatisticsList',
    meta: {
      title: '数据统计',
      menuAuth: '/dataStatisticsList/index'
    },
    children: [
      {
        path: 'index',
        name: 'dataStatisticsList',
        component: () => import('@/views/securityCenter/dataStatisticsList/index.vue'),
        meta: {
          title: '数据统计',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/dataStatisticsList'
        }
      },
      {
        path: 'hiddenManagementDetails',
        name: 'hiddenManagementDetails3',
        component: () => import('@/views/securityCenter/hiddenDanger/hiddenManagementDetails.vue'),
        meta: {
          title: '隐患详情',
          sidebar: false,
          activeMenu: '/dataStatisticsList/index'
        }
      },
      {
        path: 'taskDataStatistics',
        name: 'taskDataStatistics',
        component: () => import('@/views/securityCenter/dataStatisticsList/taskDataStatistics.vue'),
        meta: {
          title: '巡检统计详情',
          sidebar: false,
          activeMenu: '/dataStatisticsList/index'
        }
      },
      {
        path: 'scheduleDetails',
        name: 'scheduleDetailsStatistics',
        component: () => import('@/views/securityCenter/inspectionManagement/taskManagement/scheduleDetails.vue'),
        meta: {
          title: '任务详情',
          sidebar: false,
          activeMenu: '/dataStatisticsList/index'
        }
      },
      {
        path: 'recordDetails',
        name: 'recordDetailsStatistics',
        component: () => import('@/views/securityCenter/inspectionManagement/taskManagement/recordDetails.vue'),
        meta: {
          title: '巡检点详情',
          sidebar: false,
          activeMenu: '/dataStatisticsList/index'
        }
      }
    ]
  }
]
