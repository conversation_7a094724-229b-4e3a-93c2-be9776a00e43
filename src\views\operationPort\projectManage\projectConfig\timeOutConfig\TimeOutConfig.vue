<script>
import { UsingStatusOptions } from '@/views/operationPort/constant'
import tableListMixin from '@/mixins/tableListMixin'
export default {
  name: 'TimeOutConfig',
  components: {
    TimeOutConfigEdit: () => import('./components/TimeOutConfigEdit.vue')
  },
  filters: {
    statusFilter(status) {
      return UsingStatusOptions.find((it) => it.value == status)?.label ?? '-'
    }
  },
  mixins: [tableListMixin],
  data: () => ({
    searchForm: {
      name: ''
    },
    tableData: [],
    loadingStatus: false,
    dialog: {
      show: false,
      id: ''
    }
  }),
  mounted() {
    this.getDataList()
  },
  methods: {
    getDataList() {
      this.loadingStatus = true
      const params = {
        name: this.searchForm.name,
        pageSize: this.pagination.size,
        pageNo: this.pagination.current
      }
      this.$api.SporadicProject.pageReminderConfig(params)
        .then((res) => {
          if (res.code === '200') {
            this.tableData = res.data.records
            this.pagination.total = res.data.total
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '查询失败'))
        .finally(() => (this.loadingStatus = false))
    },
    handleSizeChange(pageSize) {
      this.pagination.pageSize = pageSize
      this.pagination.page = 1
      this.getDataList()
    },
    handleCurrentChange(page) {
      this.pagination.page = page
      this.getDataList()
    },
    reset() {
      this.$refs.formRef.resetFields()
    },
    onView(row) {
      this.$confirm('删除后将无法恢复，是否确定删除？', '信息提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => this.doDelete(row.id))
    },
    doDelete(id) {
      this.$api.SporadicProject.deleteReminderConfig({ id })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('删除成功')
            this.getDataList()
          } else {
            throw res.message
          }
        })
        .catch((msg) => msg && this.$message.error(msg))
        .finally(() => (this.loadingStatus = false))
    },
    onCreate() {
      this.dialog.id = ''
      this.dialog.show = true
    },
    onEdit(row) {
      this.dialog.id = row.id
      this.dialog.show = true
    }
  }
}
</script>
<template>
  <div class="time-out-config">
    <div class="time-out-config__top">
      <el-form ref="formRef" :model="searchForm" class="time-out-config__search" inline>
        <el-form-item prop="name">
          <el-input v-model="searchForm.name" clearable placeholder="搜索规则名称"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getDataList">查询</el-button>
          <el-button type="primary" plain @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
      <div class="time-out-config__actions">
        <el-button type="primary" icon="el-icon-plus" @click="onCreate">创建规则</el-button>
      </div>
    </div>
    <div class="time-out-config__table">
      <el-table v-loading="loadingStatus" height="100%" :data="tableData" border stripe table-layout="auto" class="tableAuto" row-key="id">
        <el-table-column label="序号" type="index" width="55" />
        <el-table-column label="规则名称" prop="ruleName" width="180px" show-overflow-tooltip></el-table-column>
        <el-table-column label="生效项目类型" prop="businessFormName"></el-table-column>
        <el-table-column label="超时时间" prop="ruleValue" width="150px">
          <template #default="{ row }">
            <span>{{ row.ruleValue ? `${row.ruleValue} 天` : '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status" width="100px">
          <template #default="{ row }">
            <span class="time-out-config__tag" :class="`time-out-config__tag--${row.status}`">{{ row.status | statusFilter }}</span>
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" show-overflow-tooltip></el-table-column>
        <el-table-column label="操作" width="100px">
          <template #default="{ row }">
            <el-button type="text" @click="onEdit(row)">编辑</el-button>
            <el-button type="text" class="text-red" @click="onView(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-pagination
      class="time-out-config__pagination"
      :current-page="pagination.page"
      :page-sizes="pagination.pageSizeOptions"
      :page-size="pagination.size"
      :layout="pagination.layoutOptions"
      :total="pagination.total"
      @size-change="paginationSizeChange"
      @current-change="paginationCurrentChange"
    >
    </el-pagination>
    <TimeOutConfigEdit v-bind="dialog" :visible.sync="dialog.show" @success="getDataList" />
  </div>
</template>
<style scoped lang="scss">
.time-out-config {
  display: flex;
  flex-flow: column nowrap;
  padding: 16px;
  &__top {
    display: flex;
    justify-content: space-between;
  }
  &__actions {
    line-height: 40px;
  }
  &__pagination {
    margin-top: 10px;
  }
  &__table {
    margin-top: -10px;
    flex: 1;
  }
  &__tag {
    // 停用
    &--0 {
      --color: #f64646;
    }
    // 启用
    &--1 {
      --color: #00b42a;
    }
    &:before {
      content: '';
      display: inline-block;
      vertical-align: middle;
      margin-right: 4px;
      height: 4px;
      width: 4px;
      border-radius: 4px;
      background-color: var(--color);
    }
  }
}
</style>
