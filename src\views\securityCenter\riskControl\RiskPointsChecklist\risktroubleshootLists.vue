<template>
  <div style="height: 100%;">
    <div class="content_box">
    <div class="content-top">
      <div class="search-from">
        <el-select v-model="formInline.investigationType" clearable placeholder="用户类别">
          <el-option v-for="(item, index) in typeList" :key="index" :label="item.name" :value="item.id"></el-option>
        </el-select>
        <el-date-picker v-model="formInline.createtTmeStart" type="date" clearable format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="创建日期"> </el-date-picker>
        <el-date-picker v-model="formInline.createtTmeEnd" type="date" clearable format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="结束日期"> </el-date-picker>

        <el-button type="primary" @click="reset">重置</el-button>
        <el-button type="primary" @click="searchClick()">查询</el-button>
      </div>
    </div>
    <div class="content-table">
      <div class="table-list">
        <el-table v-loading="tableLoading" :data="tableData" :height="tableHeight" border style="width: 100%;" :cell-style="{ padding: '8px' }" stripe>
          <!-- <template slot="empty">
                <p class="tableEmptyLabel">{{ $store.state.tableLabel }}</p>
              </template> -->
          <el-table-column type="index" label="序号" width="65">
            <template slot-scope="scope">
              <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="createPersonName" label="排查人" show-overflow-tooltip width="180"></el-table-column>
          <el-table-column prop="teamName" label="部门" show-overflow-tooltip width="180"></el-table-column>
          <el-table-column prop="createPersonPhone" label="联系方式" show-overflow-tooltip width="100"></el-table-column>
          <el-table-column prop="investigationType" show-overflow-tooltip label="用户类别" width="180">
            <template slot-scope="scope">
              <span>{{ scope.row.investigationType == 1 ? '匿名用户' : scope.row.investigationType == 2 ? '系统用户' : '' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="riskPlace" show-overflow-tooltip label="排查记录" width="180">
            <template slot-scope="scope">
              <span style="color: #5188fc; cursor: pointer;" @click="lookDetail(scope.row)">查看详情</span>
            </template>
          </el-table-column>
          <el-table-column prop="recordState" show-overflow-tooltip label="排查结果">
            <template slot-scope="scope">
              <span>{{ scope.row.recordState == 1 ? '合格' : scope.row.recordState == 2 ? '异常' : '' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="workOrderCode" show-overflow-tooltip label="隐患记录" width="130">
            <template slot-scope="scope">
              <span v-if="scope.row.workOrderCode" style="color: #5188fc; cursor: pointer;" @click="workOrderDetails(scope.row)">{{ scope.row.workOrderCode }}</span>
              <span v-else>无</span>
            </template>
          </el-table-column>
          <el-table-column prop="investigationTime" show-overflow-tooltip width="180" label="排查时间"></el-table-column>
        </el-table>
        <div class="table-page">
          <el-pagination
            class="pagination imas_page"
            :current-page="paginationData.currentPage"
            :page-sizes="[15, 30, 50, 100]"
            :page-size="paginationData.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="paginationData.total"
            @size-change="firstSizeNumChange"
            @current-change="firstCurrentPageChange"
          ></el-pagination>
        </div>
      </div>
    </div>
  </div>
  <div class="bottomBar">
      <div class="bottomWrap">
        <el-button type="primary" @click="complete">确定</el-button>
        <el-button type="primary" plain @click="$router.go('-1')">关闭</el-button>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      loading: false,
      tableLoading: false,
      formInline: {
        investigationType: '',
        createtTmeStart: ''
      },
      tableData: [],
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      typeList: [
        {
          name: '匿名用户',
          id: 1
        },
        {
          name: '系统用户',
          id: 2
        }
      ],
      title: '',
      disabled: false,
      row: {}
    }
  },
  computed: {
    tableHeight() {
      return document.body.clientHeight - 360
    }
  },
  //   activated() {
  //     this.row = JSON.parse(sessionStorage.getItem('RiskPointsChecklistRow'))
  //     this.requestList()
  //     this.disabled = this.$route.query.type == 'check'
  //   },
  mounted() {
    this.row = JSON.parse(sessionStorage.getItem('RiskPointsChecklistRow'))
    // this.row = this.$route.query.row

    this.requestList()
    this.disabled = this.$route.query.type == 'check'
  },
  methods: {
    complete() {},
    requestList() {
      this.$api
        .ipsmGetRiskInvestigationRecord({
          riskInvestigationId: this.row.riskInvestigationId || '',
          ...this.paginationData,
          ...this.formInline
        })
        .then((res) => {
          this.paginationData.total = res.data.sum
          this.tableData = res.data.list
        })
    },
    // 点击查询
    searchClick() {
      this.paginationData.currentPage = 1
      this.requestList()
    },
    lookDetail(row) {
      this.$router.push({
        name: 'ipsmListsDetail',
        query: {
          type: 'HiddenDangerProgress',
          id: row.id
        }
      })
    },
    workOrderDetails(row) {
      this.$router.push({
        name: 'hiddenManagementDetails2',
        query: {
          type: 'risktroubleshootLists',
          id: row.workOrderId
        }
      })
    },

    // 查询重置
    reset() {
      this.formInline.investigationType = ''
      this.formInline.createtTmeStart = ''
      this.formInline.createtTmeEnd = ''
      this.paginationData.currentPage = 1
      this.requestList()
    },
    firstSizeNumChange(val) {
      this.paginationData.currentPage = 1
      this.paginationData.pageSize = val
      this.requestList()
    },
    firstCurrentPageChange(val) {
      this.paginationData.currentPage = val
      this.requestList()
    }
  }
}
</script>
<style lang="scss" scoped>
.bottomBar {
  height: 52px;
  width: 100%;
  background-color: #fff;
  display: flex;
  justify-content: right;
  align-items: center;

  .bottomWrap {
    padding: 0 16px;
  }
}

.content_box {
  height: calc(100% - 70px);
  overflow-y: auto;
  margin: 15px;
  padding: 20px;
  background: #fff;
  display: flex;
  flex-direction: column;
}

.search-from {
  padding-bottom: 12px;

  & > div {
    margin-right: 10px;
  }

  & > button {
    margin-top: 12px;
  }
}
</style>
