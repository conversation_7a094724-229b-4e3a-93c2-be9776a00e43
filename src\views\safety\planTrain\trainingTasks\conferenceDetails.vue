<template>
  <PageContainer v-loading="contentLoading">
    <div slot="header" class="table-content">
      <div class="topFilter">
        <div class="backBar">
          <span style="cursor: pointer" @click="$router.go(-1)">
            <i class="el-icon-arrow-left"></i>
            会议详情
          </span>
        </div>
      </div>
      <div class="tabsList">
        <el-tabs @tab-click="handleClick">
          <el-tab-pane v-for="(item, index) in tabsList" :key="index" :label="item.name"
            :class="{ active: activeIndex == index }"></el-tab-pane>
        </el-tabs>
        <el-button v-if="activeIndex=='1'" type="primary" class="btn" :disabled="multipleSelection.length<1"
          @click="exportClickExport">导出</el-button>
      </div>
      <!-- 会议信息 -->
      <div class="baseInfo" v-if="activeIndex == 0">
        <h1>基础信息</h1>
        <div class="contenter">
          <div class="itemInfo">
            <div class="title">会议名称：</div>
            <div class="value">{{ dataList.name }}</div>
          </div>
          <div class="itemInfo">
            <div class="title">所属部门：</div>
            <div class="value">{{ dataList.subjectName }}</div>
          </div>
          <div class="itemInfo">
            <div class="title">会议时间：</div>
            <div class="value">{{ dataList.startTime }}-{{dataList.endTime}}</div>
          </div>
          <div class="itemInfo">
            <div class="title">会议负责人:</div>
            <div class="value">{{ dataList.principalName }}
            </div>
          </div>
          <div class="itemInfo">
            <div class="title">会议方式：</div>
            <div class="value">{{ dataList.type == 0 ? '线上会议' : '线下会议' }}</div>
          </div>
          <div class="itemInfo">
            <div class="title">会议地址：</div>
            <div class="value">{{ dataList.address }}</div>
          </div>
          <div class="itemInfo">
            <div class="title">参会人员：</div>
            <div>{{ dataList.students }}</div>
          </div>
        </div>
        <div class="statusClass" v-if="activeFlag=='3'">
          <img v-if="dataList.taskStatus=='0'" src="../../../../assets/images/signOk.png" alt="">
          <img v-if="dataList.taskStatus=='4'" src="../../../../assets/images/signNo.png" alt="">
          <img v-if="dataList.taskStatus=='5'" src="../../../../assets/images/finish.png" alt="">
        </div>
      </div>
      <div class="baseInfoSy" v-if="activeIndex == 1">
        <div class="contenter">
          <el-table :data="dataList.signIns" border style="width: 100%" height="100%"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" fixed="left" align="center"></el-table-column>
            <el-table-column prop="name" label="姓名" width="180">
            </el-table-column>
            <el-table-column prop="sex" label="性别" width="180">
              <template slot-scope="scope">
                <span>{{ scope.row.sex == '1'?'女':'男' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="phone" label="联系电话">
            </el-table-column>
            <el-table-column prop="deptName" label="所属部门" width="180">
            </el-table-column>
            <el-table-column prop="signInStatus" label="签到状态" width="180">
              <template slot-scope="scope">
                <span>{{ scope.row.signInStatus == '0'?'未签到':'已签到' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="signTime" label="签到时间" width="180">
            </el-table-column>
            <el-table-column label="签到内容" width="180">
              <template slot-scope="scope">
                <img class="imgClass" v-for="(item,index) in scope.row.fileList" :key="index" :src="item.viewAddress"
                  alt="">
              </template>
            </el-table-column>
            <el-table-column prop="type" label="培训方式" width="180">
              <template slot-scope="scope">
                <span>{{ scope.row.type == '0'?'线上会议':'线下培训' }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <div slot="content" class="courseContent" v-if="activeIndex == 0&&activeFlag=='1'">
      <div class="top">
        <h1>培训课件</h1>
      </div>
      <div class="courseware_content">
        <div v-for="(k, index) in dataList.files" :key="k.id" class="item">
          <div>
            <img src="../../../../assets/images/doc.png" alt="" />
            <span>{{ index + 1 }}.{{ k.originalFilename }}</span>
          </div>
          <div class="operate">
            <span @click="ckeckFile(k, 'examine')">查看</span>
            <!-- <span @click="download(k)">下载</span> -->
          </div>
        </div>
        <div class="top">
          <h1>会议记录</h1>
        </div>
        <div class="recored_content">
          <div v-for="(k, index) in dataList.conferenceRecordList" :key="k.id" class="item">
            <div>
              <img src="../../../../assets/images/doc.png" alt="" />
              <span>{{ index + 1 }}.{{ k.originalFilename }}</span>
            </div>
            <div class="operate">
              <span @click="ckeckFile(k, 'examine')">查看</span>
              <!-- <span @click="download(k)">下载</span> -->
            </div>
          </div>
        </div>
        <div class="top">
          <h1>会议备注：</h1>
        </div>
        <p>{{dataList.remark}}</p>
      </div>

    </div>
  </PageContainer>
</template>
<script>
  import moment from "moment";
  import axios from 'axios'
  export default {
    data() {
      return {
        activeName: 'second',
        tabsList: [{
            name: '会议信息',
            value: '0',
          },
          {
            name: '签到信息',
            value: '1',
          }
        ],
        tableData: [],
        contentLoading: false,
        routeInfo: "",
        moment,
        id: "",
        examine: '', // 查看文件状态
        dataList: {}, // 模板信息
        trainList: [], // 模板课件
        coursewareList: [{
          id: '0',
          name: '课件名称'
        }],
        drawerDialog: false,
        paginationData: {
          pageNo: 1,
          pageSize: 15,
          total: 0
        },
        idsy: [],
        activeIndex: 0,
        activeFlag: '1',
        multipleSelection: []
      };
    },
    created() {
      this.routeInfo = JSON.parse(sessionStorage.getItem("routeInfo"));
      this.id = this.$route.query.id
      this.activeFlag = this.$route.query.activeFlag
      if (this.activeFlag == '3') {
        this.tabsList = [{
          name: '会议信息',
          value: '0',
        }]
      }
      if (this.id) {
        this.getDetails();
      }
    },
    methods: {
      // 切换tabs
      handleClick(tab, event) {
        this.activeIndex = tab.index
      },
      // 获取详情
      getDetails() {
        this.contentLoading = true;
        this.$api.conferencePlanDetail({
          id: this.id
        }).then(res => {
          this.dataList = res.data
          this.trainList = res.data.fileUploadRecords
        })
        this.contentLoading = false;
      },
      // 查看文件
      ckeckFile(k, examine) {
        k.url = JSON.stringify([{
          url: k.viewAddress
        }])
        this.$router.push({
          path: '/seeFile',
          query: {
            itemInfo: JSON.stringify(k),
            type: 'see'
          }
        })

      },
      // 下载试题类型模板
      download(k) {
        this.idsy = []
        this.idsy.push(k.id)
        let params = {
          ids: this.idsy
        }
        console.log(params, 'PARAMS789');
        let httpname = '/minio/downloadBatch'
        axios({
            method: 'post',
            url: __PATH.BASE_URL_LABORATORY + httpname,
            data: params,
            responseType: 'blob',
            headers: {
              "Content-Type": "application/json",
            }
          })
          .then((res) => {
            console.log(JSON.stringify(res.data));
            let name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
            let blob = new Blob([res.data]) // { type: "application/vnd.ms-excel" }
            let url = window.URL.createObjectURL(blob) // 创建一个临时的url指向blob对象
            // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
            let a = document.createElement('a')
            a.href = url
            a.download = decodeURI(name)
            a.click()
            // 释放这个临时的对象url
            window.URL.revokeObjectURL(url)
          })
          .catch((res) => {
            this.$message.error('下载失败！')
          })
      },
      // 全部下载
      allDownload() {
        let httpname = 'trainTmp/downloadCoursewares'
        let params = {
          id: this.dataList.id,
        }
        axios({
            method: 'post',
            url: __PATH.BASE_URL_LABORATORY + httpname,
            data: params,
            responseType: 'blob',
            headers: {
              "Content-Type": "application/json",
            }
          })
          .then((res) => {
            console.log(JSON.stringify(res.data));
            let name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
            let blob = new Blob([res.data]) // { type: "application/vnd.ms-excel" }
            let url = window.URL.createObjectURL(blob) // 创建一个临时的url指向blob对象
            // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
            let a = document.createElement('a')
            a.href = url
            a.download = decodeURI(name)
            a.click()
            // 释放这个临时的对象url
            window.URL.revokeObjectURL(url)
          })
          .catch((res) => {
            this.$message.error('下载失败！')
          })
      },
      // 展开试题
      isExpandBtn(item, index) {
        item.isExpand = !item.isExpand;
      },
      //勾选
      handleSelectionChange(val) {
        this.multipleSelection = val;
      },
      exportClickExport() {
        let params = {
          signIds: this.multipleSelection.map(k => k.id)
        }
        axios({
          method: "post",
          url: __PATH.BASE_URL + '/conferencePlan/taskInfoSignExport',
          data: params,
          headers: {
            "Content-Type": "application/json",
            token: this.routeInfo.token,
          },
          responseType: 'blob',
        }).then((res) => {
          console.log(res, 'res');
          if (res.status == 200 && !res.data.code) {
            let name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
            let blob = new Blob([res.data]) // { type: "application/vnd.ms-excel" }
            let url = window.URL.createObjectURL(blob) // 创建一个临时的url指向blob对象
            // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
            let a = document.createElement('a')
            a.href = url
            a.download = decodeURI(name)
            a.click()
            // 释放这个临时的对象url
            window.URL.revokeObjectURL(url)
          } else {
            this.$message.error('导出失败')
          }
        })
      }
    },
  };

</script>
<style lang="scss" scoped>
  .table-content {
    background: #fff;
    border-radius: 4px;
    height: calc(100% - 0px);

    .baseInfo {
      padding: 0 24px 24px 24px;
      position: relative;

      .contenter {
        padding-top: 24px;
        font-size: 14px;
        display: flex;
        flex-wrap: wrap;

        .itemInfo {
          width: 33.3%;
          margin-bottom: 16px;
          display: flex;

          .title {
            width: 100px;
            color: #666;
            margin-top: 3px;
          }

          .value {
            width: 200px;
            line-height: 20px;
          }
        }
      }

      .statusClass {
        position: absolute;
        right: 20px;
        top: 20px;

        img {
          width: 100px;
          height: 100px;
        }
      }
    }
  }

  .topFilter {
    padding: 15px;
    height: 60px;
    background-color: #fff;

    .backBar {
      color: #121f3e;
      height: 30px;
      border-bottom: 1px solid #dcdfe6;
    }
  }

  .courseContent {
    height: 100%;
    margin-top: 16px;
    background-color: #fff;
    padding: 16px;

    .top {
      margin-bottom: 20px;
      display: flex;
      justify-content: space-between;

      span {
        font-size: 14px;
        color: #3562DB;
      }
    }

    .table_content {
      height: calc(100% - 70px);
      overflow: auto;

      .item {
        height: 56px;
        font-size: 14px;
        line-height: 56px;
        display: flex;
        justify-content: space-between;

        img {
          vertical-align: middle;
          margin-right: 12px;
        }

        .operate {
          span {
            cursor: pointer;
            color: #3562DB;
            margin-right: 16px;
          }
        }

      }
    }
  }

  .itemInfo {
    display: flex;
    justify-content: flex-start;
  }

  .itemInfoName {
    display: flex;
    margin-right: 25px;
  }

  .title {
    width: 115px;
  }

  .value {
    width: 60%;
    white-space: nowrap;
    /* 不换行 */
    overflow: hidden;
    /* 超出部分隐藏 */
    text-overflow: ellipsis;
  }

  .itemIndex {
    width: 730px;
    white-space: nowrap;
    /* 不换行 */
    overflow: hidden;
    /* 超出部分隐藏 */
    text-overflow: ellipsis;
  }

  .tabsList {
    margin-bottom: 10px;
    padding-left: 15px;
    width: 100%;
    height: 50px;
    position: relative;

    .btn {
      position: absolute;
      right: 10px;
      top: 0px;
    }
  }

  ::v-deep .el-radio {
    display: block;
    margin: 10px 0;
  }

  ::v-deep .el-checkbox {
    display: block;
    margin: 10px 0;
  }

  ::v-deep .el-checkbox-group {
    margin-left: 38px;
  }

  .baseInfoSy {
    padding: 0 24px 24px 24px;
    width: 100%;
    height: 650px;

    .contenter {
      // padding-top: 24px;
      font-size: 14px;


      .itemInfo {
        margin-bottom: 16px;
        display: flex;

        .title {
          width: 120px;
          color: #666;
          margin-top: 3px;
        }

        .value {
          flex: 1;
          line-height: 20px;
        }
      }
    }
  }

  .courseware_content {
    .item {
      height: 56px;
      font-size: 14px;
      line-height: 56px;
      display: flex;
      justify-content: space-between;

      img {
        vertical-align: middle;
        margin-right: 12px;
      }

      .operate {
        span {
          cursor: pointer;
          color: #3562DB;
          margin-right: 16px;
        }
      }

    }
  }

  .recored_content {
    .item {
      height: 56px;
      font-size: 14px;
      line-height: 56px;
      display: flex;
      justify-content: space-between;

      img {
        vertical-align: middle;
        margin-right: 12px;
      }

      .operate {
        span {
          cursor: pointer;
          color: #3562DB;
          margin-right: 16px;
        }
      }

    }
  }

  .imgClass {
    width: 80px;
    height: 80px;
  }

</style>
