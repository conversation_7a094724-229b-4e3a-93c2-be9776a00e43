<template>
  <div class="sino-matera-dialog">
    <el-dialog class="imgCarousel-dialog" :imgArr="imgArr" :visible.sync="dialogVisibleImg" :before-close="closeDialog" append-to-body>
      <el-carousel ref="imgCarousel" indicator-position="outside" :autoplay="false" class="imgCarousel" :initial-index="index">
        <el-carousel-item v-for="item in imgArr" :key="item" style="text-align: center;">
          <img :src="item" alt style="max-width: 100%; max-height: 100%;" />
        </el-carousel-item>
      </el-carousel>
    </el-dialog>
  </div>
</template>
<script>
export default {
  props: {
    imgArr: {
      type: Array,
      default: () => {
        return []
      }
    },
    dialogVisibleImg: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      index: 0
    }
  },
  watch: {
    index(n, o) {
      this.index = n
      this.$refs.imgCarousel.setActiveItem(n)
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {},
    closeDialog() {
      this.$emit('closeDialog')
    }
  }
}
</script>
<style lang="stylus" type="stylesheet/stylus"></style>
