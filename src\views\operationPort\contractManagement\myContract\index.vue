<template>
  <PageContainer>
    <div slot="content" class="page_containner">
      <el-tabs v-model="active">
        <el-tab-pane v-for="(item, index) in tabList" :key="index" :label="item.label" :name="item.value"></el-tab-pane>
      </el-tabs>
      <MyDoc v-if="active == 1" key="1" />
      <MyDoc v-if="active == 2" key="2" type="2" />
      <ContractBorrowing v-if="active == 3" />
      <LendingManagement v-if="active == 4" />
      <Deled v-if="active == 5" />
    </div>
  </PageContainer>
</template>
<script lang="jsx">
export default {
  components: {
    MyDoc: () => import('../components/MyDoc.vue'),
    ContractBorrowing: () => import('../components/ContractBorrowing.vue'),
    LendingManagement: () => import('../components/LendingManagement.vue'),
    Deled: () => import('../components/deled.vue')
  },
  data() {
    return {
      active: '1',
      tabList: [
        {
          label: '我的合同',
          value: '1'
        },
        {
          label: '已结束合同',
          value: '2'
        },
        {
          label: '合同借阅',
          value: '3'
        },
        {
          label: '借出管理',
          value: '4'
        },
        {
          label: '已删除合同',
          value: '5'
        }
      ]
    }
  }
}
</script>
<style lang="scss" scoped>
.page_containner {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 16px;
}
</style>
