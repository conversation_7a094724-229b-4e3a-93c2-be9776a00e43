<!-- eslint-disable vue/no-mutating-props -->
<template>
  <div>
    <div v-if="!isEdit" class="details">
      <el-row :gutter="24">
        <el-col :span="9">
          <FormTextItem title="文档名称" :value="formData.archiveName" />
        </el-col>
        <el-col :span="9">
          <FormTextItem title="文号" :value="formData.archiveNumber" />
        </el-col>
        <el-col :span="9">
          <FormTextItem title="所属分类" :value="archiveModel" />
        </el-col>
        <el-col :span="9">
          <FormTextItem title="成文日期"
            :value="formData.archiveDate ? moment(formData.archiveDate).format('YYYY-MM-DD') : ''" />
        </el-col>
        <el-col :span="24">
          <FormTextItem title="存放位置" :value="formData.saveLocation" type="textarea" />
        </el-col>
        <el-col :span="24">
          <FormTextItem title="摘要信息" :value="formData.remark" type="textarea" />
        </el-col>
        <el-col :span="24">
          <FormTextItem title="附件资料" type="slot">
            <el-table :data="formData.archiveFileList">
              <el-table-column label="序号" type="index" width="50"> </el-table-column>
              <el-table-column property="fileName" label="文件名"> </el-table-column>
              <el-table-column property="fileSize" label="大小（M）"> </el-table-column>
              <el-table-column property="option" label="操作" width="120">
                <template slot-scope="scope">
                  <el-button type="text" @click="handleView(scope.row)">查看</el-button>
                  <el-button type="text" @click="handleDownload(scope.row)"> 下载 </el-button>
                </template>
              </el-table-column>
            </el-table>
          </FormTextItem>
        </el-col>
      </el-row>
    </div>
    <div v-else>
      <el-form ref="form" :model="formData" label-width="80px" :rules="rules">
        <el-row :gutter="24">
          <el-col :span="9">
            <el-form-item label="文档名称" prop="archiveName">
              <el-input v-model="formData.archiveName"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item label="文号" prop="archiveNumber">
              <el-input v-model="formData.archiveNumber"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item label="所属分类" prop="archiveModel">
              <el-select v-model="formData.archiveModel">
                <el-option v-for="item in classification" :key="item.value" :value="item.value" :label="item.label"
                  clearable></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item label="成文日期">
              <el-date-picker v-model="formData.archiveDate" value-format="timestamp" type="date" placeholder="请选择成文日期"
                clearable> </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="存放位置">
              <el-input v-model="formData.saveLocation" type="textarea" maxlength="200" show-word-limit
                placeholder="请输入存放位置"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="摘要信息">
              <el-input v-model="formData.remark" type="textarea" maxlength="200" show-word-limit
                placeholder="请输入摘要信息"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="附件资料">
              <el-upload class="upload-demo" :action="''" :on-success="handleOnSuccess" :before-upload="beforeUpload"
                multiple :http-request="httpRequset" :show-file-list="false">
                <el-button size="small" type="secondary">
                  <em class="el-icon-upload2"></em>
                  点击上传
                </el-button>
                <div slot="tip" class="el-upload__tip">文件大小限制20M</div>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
        <div class="tableBox">
          <el-table :data="formData.archiveFileList" highlight-current-row>
            <el-table-column label="序号" type="index" width="50"> </el-table-column>
            <el-table-column property="fileName" label="文件名"> </el-table-column>
            <el-table-column property="fileSize" label="大小（M）"> </el-table-column>
            <el-table-column property="option" label="操作" width="120">
              <template slot-scope="scope">
                <el-button type="text" @click="handleDelete(scope)"> 删除 </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-form>
    </div>
  </div>
</template>
<script>
import FormTextItem from './FormTextItem.vue'
import mixins from '../mixins/index.js'
import moment from 'moment'
import dictMixin from '@/views/operationPort/contractManagement/mixins/index.js'
export default {
  components: { FormTextItem },
  mixins: [mixins, dictMixin],
  props: {
    formData: {
      type: Object,
      default: () => ({})
    },
    tableData: {
      type: Array,
      default: () => []
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      moment,
      rules: {
        archiveName: [{ required: true, message: '请输入文档名称', trigger: ['blur', 'change'] }],
        archiveNumber: [{ required: true, message: '请输入文号', trigger: ['blur', 'change'] }],
        archiveModel: [{ required: true, message: '请选择所属分类', trigger: ['blur', 'change'] }]
      }
    }
  },
  computed: {
    archiveModel() {
      const item = this.classification.find((item) => item.value === this.formData.archiveModel)
      return item ? item.label : ''
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-select,
::v-deep .el-date-editor.el-input {
  width: 100%;
}
::v-deep .upload-demo {
  display: flex;
  .el-upload__tip {
    margin-top: 0;
    margin-left: 8px;
    color: #8c8c8c;
  }
}
.tableBox {
  padding-left: 80px;
}
</style>
