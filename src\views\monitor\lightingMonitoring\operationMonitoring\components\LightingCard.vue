<script>
export default {
  name: 'LightingCard',
  props: {
    data: Object
  },
  emits: ['update'],
  data: () => ({
    isLighting: false,
    paramState: {},
    isChanged: false
  }),
  computed: {
    entityCode() {
      let code = this.data.surveyEntityCode
      if (!code) {
        return Date.now() + (Math.random() * 1000).toFixed(0)
      }
      return code
    },
    title() {
      return this.data?.surveyEntityName || '设备位置'
    },
    // 参数值详情，包含参数的选项
    parameterList() {
      const list = this.data.parameterList || []
      // 发现参数顺序不一致，统一进行排序
      list.sort((a, b) => a.parameterId - b.parameterId)
      return list
    },
    // 参数值
    parameterValues() {
      return this.parameterList.map((item) => ({ paramId: item.parameterId, value: item.parameterValue }))
    }
  },
  mounted() {
    // // 获取开关状态参数
    // const parameter = this.parameterList.find((item) => item.parameterId === 100209)
    // // 获取参数值 默认关闭
    // const value = parameter?.parameterValue ?? '0'
    // // 如果是1或者开启，则认为是开启状态
    // this.isLighting = value === '1' || value === '开启'
    // 1开启 0关闭
    this.isLighting = this.data.lightingStatus === '1'
    // 存储原始状态
    this.parameterList.forEach((it) => {
      this.paramState[it.parameterId] = it.parameterValue
    })
  },
  methods: {
    // 发送时间给父级，请求父级调用接口改变设备参数状态
    emitUpdate() {
      this.$emit('update', {
        imsCode: this.entityCode,
        paramList: this.parameterValues
      })
    },
    // 每次更改参数值，判断是否有变化
    onParameterValueChange() {
      this.isChanged = this.parameterValues.some((it) => this.paramState[it.paramId] !== it.value)
    }
  }
}
</script>
<template>
  <div class="component LightingCard">
    <div class="LightingCard__header">
      <div :title="title" class="LightingCard__title">{{ title }}</div>
      <el-button v-if="isChanged" class="LightingCard__apply" type="primary" @click="emitUpdate">应用</el-button>
    </div>
    <div class="LightingCard__body">
      <div class="LightingCard__symbol">
        <img v-if="isLighting" key="light-on" class="LightingCard__symbol__logo" src="@/assets/images/lightingMonitoring/onlight.png" alt="" />
        <img v-else key="light-off" class="LightingCard__symbol__logo" src="@/assets/images/lightingMonitoring/offlight.png" alt="" />
      </div>
      <el-form class="LightingCard__form" label-position="top">
        <el-form-item v-for="parameter of parameterList" :key="entityCode + parameter.parameterId" :label="parameter.parameterName">
          <el-select v-model="parameter.parameterValue" @change="onParameterValueChange">
            <el-option v-for="(option, index) in parameter.child || []" :key="`value-${index}`" :label="option.paramName" :value="option.value" />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<style scoped lang="scss">
.LightingCard {
  border: solid 1px #eee;
  display: inline-block;
  &__header {
    padding: 12px;
    box-shadow: 0px 2px 4px #eee;
    display: flex;
    overflow: hidden;
  }
  &__title {
    flex: 1;
    max-width: 200px;
    padding-right: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  &__apply {
    padding: 2px 8px;
  }
  &__body {
    display: flex;
    padding: 10px 2px 0 12px;
    flex-flow: row nowrap;
  }
  &__symbol {
    margin: 12px 12px 0 0;
    padding: 35px;
    text-align: center;
    &__logo {
      scale: 1.5;
    }
  }
  &__form {
    flex: 1;
    display: flex;
    flex-flow: column wrap;
    ::v-deep(.el-form-item) {
      margin-right: 10px;
      margin-bottom: 10px;
      width: 120px;
      .el-form-item__label {
        padding-bottom: 0;
        line-height: 22px;
      }
      .el-select {
        width: auto;
      }
    }
  }
}
</style>
