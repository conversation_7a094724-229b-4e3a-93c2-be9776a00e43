<template>
  <div slot="content" class="whole">
    <!-- <div class="who_t">
      <div class="space">
        <div>空间总数</div>
        <div>
          <div>
            <span style="font-weight: 700; font-size: 20px;">{{ spaceData.totalCount }}</span
            >&nbsp;<span style="color: #ccced3;">个</span>
          </div>
          <div><img src="../../../../assets/images/operationPort/空间总数.png" alt="" /></div>
        </div>
      </div>
      <div class="space">
        <div>闲置空间</div>
        <div>
          <div>
            <span style="font-weight: 700; font-size: 20px;">{{ spaceData.idleCount }}</span
            >&nbsp;<span style="color: #ccced3;">个</span>
          </div>
          <div><img src="../../../../assets/images/operationPort/闲置空间.png" alt="" /></div>
        </div>
      </div>
      <div class="space">
        <div>建筑面积</div>
        <div>
          <div>
            <span style="font-weight: 700; font-size: 20px;">{{ spaceData.totalArea }}</span
            >&nbsp;<span style="color: #ccced3;">㎡</span>
          </div>
          <div><img src="../../../../assets/images/operationPort/面积.png" alt="" /></div>
        </div>
      </div>
      <div class="space">
        <div>使用面积</div>
        <div>
          <div>
            <span style="font-weight: 700; font-size: 20px;">{{ spaceData.useArea }}</span
            >&nbsp;<span style="color: #ccced3;">㎡</span>
          </div>
          <div><img src="../../../../assets/images/operationPort/面积.png" alt="" /></div>
        </div>
      </div>
      <div class="space">
        <div>公区面积</div>
        <div>
          <div>
            <span style="font-weight: 700; font-size: 20px;">{{ spaceData.publicArea }}</span
            >&nbsp;<span style="color: #ccced3;">㎡</span>
          </div>
          <div><img src="../../../../assets/images/operationPort/面积.png" alt="" /></div>
        </div>
      </div>
    </div> -->
    <div class="who_c">
      <el-tabs v-model="activeName" @tab-click="handleClick($event)">
        <el-tab-pane label="功能类型" name="function"> </el-tab-pane>
        <el-tab-pane label="使用部门" name="dept"> </el-tab-pane>
      </el-tabs>
      <div v-if="activeName == 'function'" slot="content" style="height: 100%;">
        <div class="contentTable">
          <div class="contentTable-main table-content">
            <el-table :data="tableData" :height="tableHeight" :default-sort="{ prop: 'dataName', order: 'descending' }" @sort-change="sortchange">
              <el-table-column type="index" label="序号" width="50" align="center">
                <template slot-scope="scope">
                  <span>{{ (pagination.current - 1) * pagination.size + scope.$index + 1 }}</span>
                </template>
              </el-table-column>
              <el-table-column label="功能类型" prop="dataName" show-overflow-tooltip sortable="custom"></el-table-column>
              <el-table-column label="空间数量" prop="roomAllCount" sortable>
                <template slot-scope="scope">
                  <span class="color_blue" @click="ViewFn(scope.row,)">
                    {{ scope.row.roomAllCount }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="闲置空间数量" prop="unUseRoomCount" sortable>
                <template slot-scope="scope">
                  <span class="color_blue" @click="ViewFn(scope.row,3)">
                    {{ scope.row.unUseRoomCount }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="空间占比" prop="proportion" sortable></el-table-column>
              <el-table-column label="建筑面积（㎡）" prop="totalArea" sortable></el-table-column>
              <el-table-column label="使用面积（㎡）" prop="totalUseArea" sortable></el-table-column>
            </el-table>
          </div>
        </div>
        <div class="contentTable-footer">
          <el-pagination
            :current-page="pagination.current"
            :page-sizes="[15, 30, 50, 100]"
            :page-size="pagination.size"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
      <div v-if="activeName == 'dept'" style="height: 100%;">
        <div class="contentTable">
          <div class="contentTable-main table-content">
            <el-table :data="tableData" :height="tableHeight" :default-sort="{ prop: 'dataName', order: 'descending' }" @sort-change="sortchange">
              <el-table-column type="index" label="序号" width="50" align="center">
                <template slot-scope="scope">
                  <span>{{ (pagination.current - 1) * pagination.size + scope.$index + 1 }}</span>
                </template>
              </el-table-column>
              <el-table-column label="部门名称" prop="dataName" custom show-overflow-tooltip sortable="custom"></el-table-column>
              <el-table-column label="空间数量" prop="roomAllCount" sortable>
                <template slot-scope="scope">
                  <span class="color_blue" @click="ViewFn(scope.row, 1)">
                    {{ scope.row.roomAllCount }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="闲置空间数量" prop="unUseRoomCount" sortable>
                <template slot-scope="scope">
                  <span class="color_blue" @click="ViewFn(scope.row, 2)">
                    {{ scope.row.unUseRoomCount }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="空间占比" prop="proportion" sortable></el-table-column>
              <el-table-column label="建筑面积（㎡）" prop="totalArea" sortable></el-table-column>
              <el-table-column label="使用面积（㎡）" prop="totalUseArea" sortable></el-table-column>
            </el-table>
          </div>
        </div>
        <div class="contentTable-footer">
          <el-pagination
            :current-page="pagination.current"
            :page-sizes="[15, 30, 50, 100]"
            :page-size="pagination.size"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin.js'
export default {
  mixins: [tableListMixin],

  props: {
    modelCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      param1: '',
      param2: '',
      tableData: [],
      pagination: {
        current: 1,
        size: 15
      }, // 分页数据
      total: 0, // 数据总条数
      activeName: 'function',
      spaceData: {
        idleCount: 0,
        publicArea: 0,
        totalArea: 0,
        totalCount: 0,
        useArea: 0
      }
    }
  },
  // computed: {
  //   tableHeight() {
  //     return document.body.clientHeight - 410
  //   }
  // },
  mounted() {
    this.getRoomCountAndArea()
  },
  methods: {
    active() {
      this.activeName = 'function'
    },
    ViewFn(row, type) {
      this.$router.push({
        path: '/spaceLedger',
        query: {
          row: row,
          type: type,
          modelCode: this.modelCode
        }
      })
    },
    sortchange(column, prop, order) {
      if (column.prop == 'dataName') {
        if (column.order == 'ascending') {
          this.param1 = column.prop
          this.param2 = ''
        } else {
          this.param1 = ''
          this.param2 = column.prop
        }
      }
      this.getSpaceMessageList(this.activeName)
    },
    getRoomCountAndArea() {
      const params = {
        modelCode: this.modelCode,
        haveModel: '0'
      }
      this.$api.getRoomCountAndArea(params).then((res) => {
        if (res.code === 200) {
          Object.assign(this.spaceData, res.data)
          // 计算公共区域面积 = 建筑面积 - 使用面积
          this.spaceData.publicArea = this.spaceData.publicArea ? this.spaceData.publicArea : this.spaceData.totalArea - this.spaceData.useArea
          console.log(this.spaceData)
        }
        this.changeType()
      })
    },
    handleClick(event) {
      this.activeName = event.name
      this.pagination.current = 1
      this.getSpaceMessageList(this.activeName)
    },
    changeType() {
      const params = {
        modelCode: this.modelCode,
        queryType: 'function',
        ascColumn: 'dataName',
        descColumn: '',
        current: this.pagination.current,
        size: this.pagination.size,
        haveModel: '0'
      }
      this.$api.getRoomCountAndAreaPageList(params).then((res) => {
        if (res.code == 200) {
          res.data.records.forEach((item) => {
            item.proportion = ((item.roomAllCount / this.spaceData.totalCount) * 100).toFixed(2) + '%'
          })
          this.tableData = res.data.records
          this.total = res.data.total
        }
      })
    },
    getSpaceMessageList(type) {
      const params = {
        modelCode: this.modelCode,
        queryType: type,
        ascColumn: this.param1,
        descColumn: this.param2,
        current: this.pagination.current,
        size: this.pagination.size,
        haveModel: '0'
      }
      this.$api.getRoomCountAndAreaPageList(params).then((res) => {
        if (res.code == 200) {
          res.data.records.forEach((item) => {
            item.proportion = ((item.roomAllCount / this.spaceData.totalCount) * 100).toFixed(2) + '%'
            if (params.queryType == 'dept') {
              if (item.dataName == null) {
                item.dataName = '未知'
              }
            }
          })
          this.tableData = res.data.records
          this.total = res.data.total
        }
      })
    },
    handleSizeChange(val) {
      this.pagination.size = val
      this.getSpaceMessageList(this.activeName)
    },
    handleCurrentChange(val) {
      this.pagination.current = val
      this.getSpaceMessageList(this.activeName)
    }
  }
}
</script>

<style lang="scss" scoped>
.whole {
  width: 100%;
  height: 100%;

  .who_t {
    display: flex;
    height: 100px;
    width: 100%;
    margin-bottom: 10px;

    > div:nth-child(1) {
      margin-right: 10px;
    }

    > div:nth-child(2) {
      margin-right: 10px;
    }

    > div:nth-child(3) {
      margin-right: 10px;
    }

    > div:nth-child(4) {
      margin-right: 10px;
    }

    .space {
      border-radius: 5px;
      background-color: #fff;
      width: 20%;
      height: 100%;
      padding: 10px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      > div {
        height: 50%;
      }

      > div:nth-child(2) {
        display: flex;
        align-items: center;
        justify-content: space-between;

        img {
          width: 40px;
          height: 100%;
        }
      }
    }
  }

  .who_c {
    padding: 10px;
    background-color: #fff;
    height: 100%;
    // .contentTable {
    //   background: #fff;
    //   border-radius: 4px;
    //   padding: 16px 0 0 0;
    //   display: flex;
    //   flex-direction: column;
    //   overflow: auto;
    //   .contentTable-main {
    //     flex: 1;
    //     overflow: auto;
    //   }
    //   .contentTable-footer {
    //     padding: 10px 0 0 0;
    //   }
    //   .alarmLevel {
    //     padding: 3px 6px;
    //     border-radius: 4px 4px 4px 4px;
    //     color: #fff;
    //     line-height: 14px;
    //   }
    //   .alarmStatus {
    //     position: relative;
    //     display: inline-block;
    //     padding-left: 12px;
    //     .alarmStatusIcon {
    //       display: inline-block;
    //       position: absolute;
    //       left: 0;
    //       top: 50%;
    //       transform: translateY(-50%);
    //       width: 8px;
    //       height: 8px;
    //       border-radius: 100%;
    //     }
    //   }
    //   .collectIcon {
    //     font-size: 16px;
    //     margin-right: 4px;
    //   }
    // }
  }
}

.color_blue {
  color: #5188fc;
  cursor: pointer;
}

.contentTable {
  height: calc(100% - 70px);
  background: #fff;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  overflow: auto;

  .contentTable-main {
    flex: 1;
    overflow: auto;
  }

  .contentTable-footer {
    padding: 10px 0 0;
  }

  .alarmLevel {
    padding: 3px 6px;
    border-radius: 4px;
    color: #fff;
    line-height: 14px;
  }

  .alarmStatus {
    position: relative;
    display: inline-block;
    padding-left: 12px;

    .alarmStatusIcon {
      display: inline-block;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 8px;
      height: 8px;
      border-radius: 100%;
    }
  }

  .collectIcon {
    font-size: 16px;
    margin-right: 4px;
  }
}
</style>
