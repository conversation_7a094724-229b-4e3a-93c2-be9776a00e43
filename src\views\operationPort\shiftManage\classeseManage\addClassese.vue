<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="classese-content-title" @click="$router.go(-1)">
        <i class="el-icon-arrow-left"></i> {{ queryParams.type === 'add' ? '新建班次' : queryParams.type === 'view' ? '班次详情' : '编辑班次' }}
      </div>
      <div class="content_box">
        <classConfig ref="classConfig" :parameter="$route.query" />
      </div>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="onClose">取消</el-button>
      <el-button v-show="queryParams.type !== 'view'" type="primary" :loading="formLoading" @click="onPreservation">保存</el-button>
      <el-button v-show="queryParams.type === 'view'" type="primary" @click="onEdit">编辑</el-button>
    </div>
  </PageContainer>
</template>
<script>
import classConfig from './components/classConfig.vue'
export default {
  name: 'addClassese',
  components: {
    classConfig
  },
  data() {
    return {
      queryParams: {},
      formLoading: false
    }
  },
  mounted() {
    this.queryParams = this.$route.query
  },
  methods: {
    // 关闭
    onClose() {
      this.$router.go(-1)
    },
    // 编辑
    onEdit() {
      this.queryParams.type = 'edit'
    },
    // 保存
    onPreservation() {
      const refs = this.$refs.classConfig
      let totalWorkHours = refs._computedWatchers.totalWorkHours.value || ''
      // totalWorkHours不能为负数
      if (totalWorkHours < 0) {
        this.$message.error('总工作时长异常!')
        return
      }
      if (totalWorkHours > 24) {
        this.$message.error('工作时长不能超过24小时!')
        return
      }
      refs.$refs.form.validate((valid) => {
        if (!valid) return
        const { name, tableData } = refs.form
        const { id, attendanceTimeId } = this.queryParams
        const params = {
          name,
          rule: tableData,
          id: id || '',
          totalTime: this.totalWorkHourSum
        }
        if (attendanceTimeId) {
          params.attendanceTimeId = attendanceTimeId
        }
        let fn = id ? this.$api.supplierAssess.updateShiftInfo : this.$api.supplierAssess.saveShiftInfo
        this.formLoading = true
        fn(params)
          .then((res) => {
            const { code } = res
            if (code == 200) {
              this.$message.success('保存成功!')
              // if (id) {
              //   this.$confirm('立即生效则考勤结果按新规计算?', '生效时间', {
              //     cancelButtonClass: 'el-button--primary is-plain',
              //     confirmButtonText: '立即生效',
              //     cancelButtonText: '明日生效',
              //     type: 'warning'
              //   }).then(() => {
              //   })
              // }
              this.$router.go(-1)
            } else {
              this.$message.error(res.msg || '保存失败!')
            }
          })
          .catch((err) => {
            this.$message.error(err.msg || '保存失败!')
          })
          .finally(() => {
            this.formLoading = false
          })
      })
    }
  }
}
</script>
<style scoped lang="scss">
.container-content > div {
  background-color: #fff;
  height: 100%;
  position: relative;
  .classese-content-title {
    border-bottom: 1px solid #e4e7ed;
    padding: 12px 24px;
    cursor: pointer;
    margin-bottom: 24px;
  }
  .content_box {
    padding: 0 24px;
    background: #fff;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    height: calc(100% - 100px);
  }
}
</style>
