<template>
  <div class="inner">
    <div class="content-left">
      <div v-for="(item, index) in typeList" :key="index" :class="['archive-bar', active === item.dictValue ? 'active' : '']" @click="changeType(item.dictValue)">
        <div class="conten-text">
          <span>{{ item.dictLabel }}</span>
          <span>({{ item.total }})</span>
        </div>
      </div>
    </div>
    <div class="content-right">
      <div class="archives-title">
        <span class="title-text">资产档案</span>
      </div>
      <div class="table-content">
        <el-table
          ref="singleTable"
          :data="tableData"
          height="500"
          stripe
          highlight-current-row
        >
          <el-table-column align="center" type="index" label="序号" width="80"></el-table-column>
          <el-table-column align="center" property="fileName" label="档案名称"></el-table-column>
          <el-table-column align="center" property="fileType" label="档案类型"></el-table-column>
          <el-table-column align="center" property="fileSize" label="档案大小"></el-table-column>
        </el-table>
      </div>
      <!-- <div class="content-footer">
        <el-pagination
          :current-page="pageParams.currentPage"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="pageParams.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageParams.total"
          class="pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div> -->
    </div>
  </div>
</template>
<script>
import { iomsUserInfon } from '@/util/dict.js'
export default {
  name: 'equipmentArchives',
  components: {},
  props: {
    deviceId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      typeList: [
      ],
      active: '',
      tableData: [],
      pageParams: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      }
    }
  },
  computed: {},
  watch: {},
  created() {
    const id = this.deviceId
    this.getArchivesType(id)
  },
  mounted() {},
  methods: {
    getArchivesType(id) {
      this.$api.getArchivesType({ assetsId: id, ...iomsUserInfon }).then((res) => {
        if (res.code === '200') {
          this.typeList = res.data
          this.active = res.data[0].dictValue
          this.getArchivesList(id, this.active)
        }
      })
    },
    getArchivesList(id, type) {
      this.$api.getArchivesList({ assetsId: id, fileCatalogCode: type, ...iomsUserInfon }).then((res) => {
        if (res.code === '200') {
          this.tableData = res.data
        }
      })
    },
    changeType(type) {
      this.active = type
      const id = this.deviceId
      this.getArchivesList(id, type)
    },
    handleSizeChange(val) {
      this.pageParams.pageSize = val
      // 在此处请求接口
    },
    handleCurrentChange(val) {
      this.pageParams.currentPage = val
      // 在此处请求接口
    }
  }
}
</script>
<style lang="scss" scoped>
.inner {
  width: 100%;
  // padding: 0 120px;
  height: 100%;
  display: flex;
  .content-left {
    width: 15%;
    height: 100%;
    background: #fff;
    border-radius: 4px;
    padding: 16px;
    overflow: auto;
    .archive-bar {
      cursor: pointer;
      width: 100%;
      height: 36px;
      color: #121f3e;
      font-size: 15px;
      display: flex;
      align-items: center;
      padding-left: 24px;
      .conten-text {
        span {
          display: inline-block;
        }
        :last-child {
          margin-left: 6px;
        }
      }
    }
    .active {
      background-color: #d9e1f8;
    }
  }
  .content-right {
    width: 84%;
    margin-left: 1%;
    height: 100%;
    .archives-title {
      background: #fff;
      margin-bottom: 16px;
      width: 100%;
      padding-left: 12px;
      height: 32px;
      line-height: 32px;
      border-radius: 4px;
      .title-text {
        font-size: 14px;
        color: #121f3e;
      }
    }
    .content-footer{
      margin-top: 10px;
      background: #fff;
      padding: 5px;
    }
  }
}
</style>
