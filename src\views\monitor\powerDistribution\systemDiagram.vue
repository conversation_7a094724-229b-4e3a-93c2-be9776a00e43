<template>
  <PageContainer class="systemDiagram">
    <div slot="header" class="systemDiagram-heade">
      <i class="el-icon-arrow-left" @click="() => {$router.go(-1)}"/>
      <span>配电系统图</span>
    </div>
    <div slot="content" class="content-details">
      <graphics-mode ref="scadaShow" :entityMenuCode="$route.query.entityMenuCode" :projectId="$route.query.projectCode" />
    </div>
  </PageContainer>
</template>

<script>
import graphicsMode from '../airMenu/components/graphicsMode.vue'
export default {
  name: 'systemDiagram',
  components: { graphicsMode },
  data() {
    return {

    }
  },
  computed: {

  },
  created() {

  },
  methods: {

  }
}

</script>

<style lang="scss" scoped>
.systemDiagram {
  p {
    margin: 0;
  }

  ::v-deep .container-header {
    // margin-left: 16px;
    border-radius: 4px 4px 0 0;
    border-bottom: 1px solid #dcdfe6;
  }

  .systemDiagram-heade {
    display: flex;
    align-items: center;
    padding: 13px 16px;
    font-size: 14px;
    font-weight: 500;
    color: #121f3e;
    line-height: 14px;

    .el-icon-arrow-left {
      font-size: 13px;
      margin-right: 8px;
      cursor: pointer;
      color: #7f848c;
    }
  }

  .content-details {
    height: 100%;
    background: #fff;
    padding: 16px;

    .scada-preview {
      padding: 0;
    }
  }
}
</style>
