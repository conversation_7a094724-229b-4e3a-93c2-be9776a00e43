<template>
  <el-dialog
    v-dialogDrag
    title="文件预览"
    :modal="false"
    custom-class="model-dialog"
    width="60%"
    append-to-body
    :visible="visible"
    :close-on-click-modal="false"
    :before-close="close"
  >
    <!-- <div v-if="['.docx', '.xlsx', '.pdf'].includes(row.fileExtension)" id="main_content" class="main-content"></div>
    <div v-else-if="['.png', '.jpg', '.jpeg', '.bmp'].includes(row.fileExtension)" class="main-content">
      <div style="text-align: center">
        <img :src="fileUrl" alt="" style="width: 500px; height: 500px" />
      </div>
    </div>
    <div v-else-if="['.mp4'].includes(row.fileExtension)" class="main-content">
      <video ref="videoPlayer" style="width: 100%; height: 500px" :src="fileUrl" controls></video>
    </div>
    <div v-else class="main-content">
      <audio ref="audioPlayer" style="width: 100%; height: 500px; background-color: #f1f3f4" :src="fileUrl" autoplay muted loop controls></audio>
    </div> -->
    <!-- iframe -->
    <div class="main-content">
      <iframe :src="url" style="width: 100%; height: 100%" frameborder="0"></iframe>
    </div>
  </el-dialog>
</template>
<script>
import jsPreviewDocx from '@js-preview/docx'
import '@js-preview/docx/lib/index.css'
import jsPreviewExcel from '@js-preview/excel'
import '@js-preview/excel/lib/index.css'
import jsPreviewPdf from '@js-preview/pdf'
export default {
  name: 'PreView',
  model: {
    prop: 'visible',
    event: 'close'
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      url: ''
    }
  },
  computed: {
    fileUrl() {
      return this.$tools.imgUrlTranslation(this.row.fileUrl)
    }
  },
  mounted() {
    this.previewInit()
    // if (['.docx', '.xlsx', '.pdf'].includes(this.row.fileExtension)) {
    //   this.$nextTick(() => {
    //     this.init()
    //   })
    // }
  },
  methods: {
    previewInit() {
      const baseUrl = __PATH.VUE_PREVIEW_URL
      const encode = new TextEncoder()
      const fileUrl = encode.encode(this.fileUrl)
      const str = btoa(String.fromCharCode.apply(null, fileUrl))
      this.url = baseUrl + str.replace(/\+/g, '%2B')
    },
    // init() {
    //   if (this.row.fileExtension == '.pdf') {
    //     const myPdfPreviewer = jsPreviewPdf.init(document.getElementById('main_content'), {
    //       onError: (e) => {
    //         console.log('发生错误', e)
    //       },
    //       onRendered: () => {
    //         console.log('渲染完成')
    //       }
    //     })
    //     myPdfPreviewer.preview(this.fileUrl)
    //   } else if (this.row.fileExtension == '.xlsx') {
    //     const myExcelPreviewer = jsPreviewExcel.init(document.getElementById('main_content'))
    //     myExcelPreviewer
    //       .preview(this.fileUrl)
    //       .then((res) => {
    //         console.log('预览完成')
    //       })
    //       .catch((e) => {
    //         console.log('预览失败', e)
    //       })
    //   } else {
    //     const myDocxPreviewer = jsPreviewDocx.init(document.getElementById('main_content'))
    //     myDocxPreviewer
    //       .preview(this.fileUrl)
    //       .then((res) => {
    //         console.log('预览完成')
    //       })
    //       .catch((e) => {
    //         console.log('预览失败', e)
    //       })
    //   }
    // },
    close() {
      this.$emit('close')
    }
  }
}
</script>
<style lang="scss" scoped>
.model-dialog {
  width: 100%;
  height: 100%;
  .el-dialog__body {
    height: 100%;
  }
}
.main-content {
  width: 100%;
  // height: 100%;
  min-height: 550px;
  ::v-deep .vue-office-excel {
    height: 500px;
    .vue-office-excel-main {
      height: 100%;
      .x-spreadsheet {
        height: 100%;
      }
    }
  }
}
.notPreview {
  width: 100%;
  height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
</style>
