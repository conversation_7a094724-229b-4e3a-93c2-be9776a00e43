<!--
 * @Description:
-->
<template>
  <PageContainer>
    <div slot="content">
      <el-tabs v-model="activeName">
        <el-tab-pane label="维修工单" name="first">
          <!-- <maintenanceOrder v-if="activeName == 'first'"></maintenanceOrder> -->
        </el-tab-pane>
        <el-tab-pane label="我的报修" name="second">
          <!-- <myReport v-if="activeName == 'second'"></myReport> -->
        </el-tab-pane>
      </el-tabs>
      <div class="tool-box">
        <el-select v-model="searchFrom.flowCode" placeholder="工单状态">
          <el-option v-for="item in flowcodeOption" :key="item.label" :label="item.label" :value="item.value"> </el-option>
        </el-select>
        <el-date-picker v-model="searchFrom.dateVal" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd+HH:mm:ss">
        </el-date-picker>
        <el-button type="primary" plain @click="resetForm">重置</el-button>
        <el-button type="primary" @click="searchForm">查询</el-button>
      </div>
      <div class="require-content">
        <maintenanceOrder v-if="activeName == 'first'" ref="customComponent" :searchFrom="searchFrom"></maintenanceOrder>
        <myReport v-if="activeName == 'second'" ref="customComponent" :searchFrom="searchFrom"></myReport>
      </div>
    </div>
  </PageContainer>
</template>
<script>
// import store from '@/store/index'
import myReport from './myReport.vue'
import maintenanceOrder from './maintenanceOrder.vue'
export default {
  name: 'MaintenanceWorkOrderManagement',
  components: {
    myReport,
    maintenanceOrder
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.$store.commit('keepAlive/add', 'MaintenanceWorkOrderManagement')
    })
  },
  async beforeRouteLeave(to, from, next) {
    // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
    if (!['addDevice'].includes(to.name)) {
      // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      activeName: 'first',
      searchFrom: {
        flowCode: '',
        dateVal: '',
        pageNo: 1
      },
      flowcodeOption: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '未受理',
          value: '1'
        },
        {
          label: '暂存',
          value: '7'
        },
        {
          label: '未派工',
          value: '2'
        },
        {
          label: '已派工',
          value: '3'
        },
        {
          label: '已挂单',
          value: '4'
        },
        {
          label: '已完工',
          value: '5'
        },
        {
          label: '已取消',
          value: '6'
        }
      ]
    }
  },
  mounted() {},
  methods: {
    resetForm() {
      this.searchFrom.flowCode = ''
      this.searchFrom.dateVal = ''
      this.searchFrom.pageNo = 1
      this.$refs.customComponent.getTableData()
    },
    searchForm() {
      this.$refs.customComponent.getTableData()
    }
  }
}
</script>
<style lang="scss" scoped>
.container-content > div {
  width: 100%;
  height: 100%;
  background-color: #fff;
  padding: 16px;
  display: flex;
  flex-direction: column;
  ::v-deep .el-tabs {
    height: auto;
  }
  .require-content {
    flex: 1;
    overflow: auto;
  }
}
.tool-box {
  display: flex;
  align-items: center;
  margin: 24px 0;
}

.el-range-editor {
  margin: 0 24px;
}

::v-deep .el-date-editor .el-range__icon {
  line-height: 24px;
}
.el-tabs {
  height: 100%;
}

::v-deep .el-tabs__content {
  height: 95%;
}

::v-deep .el-tab-pane {
  height: 96%;
}
</style>
