<!-- 运行率占比弹窗 -->
<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    width="50%"
    :visible.sync="visible"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <div class="content">
      <el-table ref="table" border :data="tableData" :height="350" style="width: 100%;">
        <el-table-column label="序号" type="index" width="50"> </el-table-column>
        <el-table-column label="监测项名称" prop="paramName" show-overflow-tooltip></el-table-column>
        <el-table-column :label="requestInfo.parameterName + requestInfo.unit" prop="paramValue" show-overflow-tooltip></el-table-column>
        <el-table-column label="时间" prop="paramTime" show-overflow-tooltip></el-table-column>
        <el-table-column label="区间" prop="configName" show-overflow-tooltip>
          <template slot-scope="scope">
            <span :style="{color: scope.row.colour}">{{scope.row.configName}}</span>
          </template>
        </el-table-column>
        <el-table-column label="服务空间" prop="spaceName" show-overflow-tooltip></el-table-column>
      </el-table>
    </div>
  </el-dialog>
</template>
<script>
export default {
  name: 'monitoringListDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    requestInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      loading: false,
      tableData: []
    }
  },
  mounted() {
    this.getMonitoringList()
  },
  methods: {
    // 获取环境监测详情列表
    getMonitoringList() {
      this.loading = true
      this.$api.GetMonitoringList(this.requestInfo).then((res) => {
        this.loading = false
        if (res.code == 200) {
          this.tableData = res.data
        }
      })
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', !this.visible)
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog {
  background: #fff;

  .el-dialog__header {
    padding: 0;
    height: 0;

    .el-dialog__headerbtn {
      top: 5px;
      right: 5px;
    }
  }

  .el-dialog__body {
    padding: 30px 10px 20px;
  }
}

.content {
  width: 100%;
  height: 100%;
}

.model-dialog {
  padding: 0 !important;
}

::v-deep .el-pagination {
  .btn-next,
  .btn-prev {
    background: transparent;
  }

  .el-pager li {
    background: transparent;
  }
}
</style>
