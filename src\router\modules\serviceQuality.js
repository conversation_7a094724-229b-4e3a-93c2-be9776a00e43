import Layout from '@/layout'
import EmptyLayout from '@/layout/empty'
export default [
  {
    path: '/serviceOverview',
    component: Layout,
    redirect: '/serviceOverview/index',
    name: 'ServiceOverview',
    meta: {
      title: '服务总览',
      menuAuth: '/serviceOverview/index'
      // icon: 'sidebar-breadcrumb'
    },
    children: [
      {
        path: 'index',
        name: 'ServiceOverview',
        component: () => import('@/views/serviceQuality/overview/overview.vue'),
        meta: {
          title: '服务总览',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/serviceOverview'
        }
      }
    ]
  },
  {
    path: '/fileManager',
    component: Layout,
    redirect: '/fileManager/ledgerFile',
    name: 'fileManager',
    meta: {
      title: '安全台账管理',
      menuAuth: '/fileManager'
      // icon: 'sidebar-breadcrumb'
    },
    children: [
      {
        path: 'ledgerFile',
        component: EmptyLayout,
        redirect: { name: 'ledgerFile' },
        name: 'ledgerFile',
        meta: {
          title: '台账文件管理',
          menuAuth: '/fileManager/ledgerFile'
        },
        children: [
          {
            path: '',
            name: 'ledgerFile',
            component: () => import('@/views/serviceQuality/fileManager/ledgerFile.vue'),
            meta: {
              title: '台账文件管理',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'recycleBin',
        component: EmptyLayout,
        redirect: { name: 'recycleBin' },
        name: 'recycleBin',
        meta: {
          title: '回收站',
          menuAuth: '/fileManager/recycleBin'
        },
        children: [
          {
            path: '',
            name: 'recycleBin',
            component: () => import('@/views/serviceQuality/fileManager/recycleBin.vue'),
            meta: {
              title: '回收站',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      }
    ]
  },
  {
    path: '/maintenanceService',
    component: Layout,
    redirect: '/maintenanceService/workOrder',
    name: 'maintenanceService',
    meta: {
      title: '维修服务管理',
      menuAuth: '/maintenanceService'
    },
    children: [
      {
        path: 'workOrder',
        component: EmptyLayout,
        redirect: {
          name: 'workOrder'
        },
        meta: {
          title: '工单管理',
          menuAuth: '/maintenanceService/workOrder'
        },
        children: [
          {
            path: '',
            name: 'WorkOrder',
            component: () => import('@/views/serviceQuality/maintenance/workOrder.vue'),
            meta: {
              title: '工单管理',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'workOrderTable',
        name: 'WorkOrderTable',
        component: () => import('@/views/serviceQuality/maintenance/workOrderTable.vue'),
        meta: {
          title: '工单管理',
          sidebar: false,
          breadcrumb: false
        }
      },
      {
        path: 'statisticalAnalysis',
        component: EmptyLayout,
        redirect: {
          name: 'statisticalAnalysis'
        },
        meta: {
          title: '统计分析',
          menuAuth: '/maintenanceService/statisticalAnalysis'
        },
        children: [
          {
            path: '',
            name: 'WorkOrder',
            component: () => import('@/views/serviceQuality/maintenance/statisticalAnalysis.vue'),
            meta: {
              title: '统计分析',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      }
    ]
  },
  {
    path: '/cleaningService',
    component: Layout,
    redirect: '/cleaningService/workOrder',
    name: 'cleaningService',
    meta: {
      title: '保洁服务管理',
      menuAuth: '/cleaningService'
    },
    children: [
      {
        path: 'workOrder',
        component: EmptyLayout,
        redirect: {
          name: 'workOrder'
        },
        meta: {
          title: '工单管理',
          menuAuth: '/cleaningService/workOrder'
        },
        children: [
          {
            path: '',
            name: 'WorkOrder',
            component: () => import('@/views/serviceQuality/cleaning/workOrder.vue'),
            meta: {
              title: '工单管理',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'inspectionTaskList',
        name: 'InspectionTaskList',
        component: () => import('@/views/serviceQuality/cleaning/inspectionTaskList.vue'),
        meta: {
          title: '巡检任务列表',
          sidebar: false,
          breadcrumb: false
        }
      },
      {
        path: 'inspectionPointList',
        name: 'InspectionPointList',
        component: () => import('@/views/serviceQuality/cleaning/inspectionPointList.vue'),
        meta: {
          title: '巡检点列表',
          sidebar: false,
          breadcrumb: false
        }
      },
      {
        path: 'taskManagement',
        component: EmptyLayout,
        redirect: {
          name: 'taskManagement'
        },
        meta: {
          title: '任务管理',
          menuAuth: '/cleaningService/taskManagement'
        },
        children: [
          {
            path: '',
            name: 'taskManagement',
            component: () => import('@/views/serviceQuality/cleaning/statisticalAnalysis.vue'),
            meta: {
              title: '任务管理',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      }
    ]
  },
  {
    path: '/centralTransport',
    component: Layout,
    redirect: '/centralTransport/workOrder',
    name: 'centralTransport',
    meta: {
      title: '中央运送管理',
      menuAuth: '/centralTransport'
    },
    children: [
      {
        path: 'workOrder',
        component: EmptyLayout,
        redirect: {
          name: 'workOrder'
        },
        meta: {
          title: '工单管理',
          menuAuth: '/centralTransport/workOrder'
        },
        children: [
          {
            path: '',
            name: 'WorkOrder',
            component: () => import('@/views/serviceQuality/transport/workOrder.vue'),
            meta: {
              title: '工单管理',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'workOrderTable',
        name: 'WorkOrderTable',
        component: () => import('@/views/serviceQuality/transport/workOrderTable.vue'),
        meta: {
          title: '工单管理',
          sidebar: false,
          breadcrumb: false
        }
      },
      {
        path: 'statisticalAnalysis',
        component: EmptyLayout,
        redirect: {
          name: 'statisticalAnalysis'
        },
        meta: {
          title: '统计分析',
          menuAuth: '/centralTransport/statisticalAnalysis'
        },
        children: [
          {
            path: '',
            name: 'WorkOrder',
            component: () => import('@/views/serviceQuality/transport/statisticalAnalysis.vue'),
            meta: {
              title: '统计分析',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      }
    ]
  },
  {
    path: '/complaintManagement',
    component: Layout,
    redirect: '/complaintManagement/index',
    name: 'complaintManagement',
    meta: {
      title: '投诉管理',
      menuAuth: '/complaintManagement'
    },
    children: [
      {
        path: 'index',
        component: EmptyLayout,
        redirect: {
          name: 'index'
        },
        meta: {
          title: '投诉管理',
          menuAuth: '/complaintManagement/index'
        },
        children: [
          {
            path: '',
            name: 'ComplaintManagement',
            component: () => import('@/views/serviceQuality/complaintManagement/index.vue'),
            meta: {
              title: '投诉管理',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      }
    ]
  },
  {
    path: '/degreeSatisfaction',
    component: Layout,
    redirect: '/degreeSatisfaction/questTemplate',
    name: 'degreeSatisfaction',
    meta: {
      title: '满意度调查',
      menuAuth: '/degreeSatisfaction'
    },
    children: [
      {
        path: 'questTemplate',
        component: EmptyLayout,
        redirect: {
          name: 'questTemplate'
        },
        meta: {
          title: '问卷模板',
          menuAuth: '/degreeSatisfaction/questTemplate'
        },
        children: [
          {
            path: '',
            name: 'questTemplate',
            component: () => import('@/views/serviceQuality/degreeSatisfaction/questTemplate.vue'),
            meta: {
              title: '问卷模板',
              sidebar: false,
              breadcrumb: false,
              auth: []
            }
          },
          {
            path: 'newQuestionnaire',
            name: 'newQuestionnaire',
            component: () => import('@/views/serviceQuality/degreeSatisfaction/questionnaire/newQuestionnaire.vue'),
            meta: {
              title: '编辑问卷模板',
              sidebar: false,
              activeMenu: '/degreeSatisfaction/questTemplate'
            }
          },
          {
            path: 'templatePreview',
            name: 'templatePreview',
            component: () => import('@/views/serviceQuality/degreeSatisfaction/preview/templatePreview.vue'),
            meta: {
              title: '预览',
              sidebar: false,
              activeMenu: '/degreeSatisfaction/questTemplate'
            }
          }
        ]
      },
      {
        path: 'questManagement',
        component: EmptyLayout,
        redirect: '/degreeSatisfaction/questManagement',
        meta: {
          title: '问卷管理',
          menuAuth: '/degreeSatisfaction/questManagement'
        },
        children: [
          {
            path: '',
            name: 'questManagement',
            component: () => import('@/views/serviceQuality/degreeSatisfaction/questManagement.vue'),
            meta: {
              title: '问卷管理',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'questionCreate',
            name: 'questionCreate',
            component: () => import('@/views/serviceQuality/degreeSatisfaction/questionCreate/questionCreate.vue'),
            meta: {
              title: '问卷名称',
              sidebar: false,
              activeMenu: '/degreeSatisfaction/questManagement'
            }
          },
          {
            path: 'QuestionDesign',
            name: 'QuestionDesign',
            component: () => import('@/views/serviceQuality/degreeSatisfaction/questionDesign/questionDesign.vue'),
            meta: {
              title: '问卷题目',
              sidebar: false,
              activeMenu: '/degreeSatisfaction/questManagement'
            }
          },
          {
            path: 'questionSetting',
            name: 'questionSetting',
            component: () => import('@/views/serviceQuality/degreeSatisfaction/questionSetting/questionSetting.vue'),
            meta: {
              title: '问卷设置',
              sidebar: false,
              activeMenu: '/degreeSatisfaction/questManagement'
            }
          },
          {
            path: 'propagation',
            name: 'propagation',
            component: () => import('@/views/serviceQuality/degreeSatisfaction/propagation/propagation.vue'),
            meta: {
              title: '问卷传播',
              sidebar: false,
              activeMenu: '/degreeSatisfaction/questManagement'
            }
          },
          {
            path: 'recoveryQues',
            name: 'recoveryQues',
            component: () => import('@/views/serviceQuality/degreeSatisfaction/recoveryQues/recoveryQues.vue'),
            meta: {
              title: '问卷回收',
              sidebar: false,
              activeMenu: '/degreeSatisfaction/questManagement'
            }
          },
          {
            path: 'questionAnalysis',
            name: 'questionAnalysis',
            component: () => import('@/views/serviceQuality/degreeSatisfaction/questionAnalysis/questionAnalysis.vue'),
            meta: {
              title: '单提分析',
              sidebar: false,
              activeMenu: '/degreeSatisfaction/questManagement'
            }
          },
          {
            path: 'questionTotalAnalysis',
            name: 'questionTotalAnalysis',
            component: () => import('@/views/serviceQuality/degreeSatisfaction/questionTotalAnalysis/questionTotalAnalysis.vue'),
            meta: {
              title: '交叉分析',
              sidebar: false,
              activeMenu: '/degreeSatisfaction/questManagement'
            }
          },
          {
            path: 'preview',
            name: 'preview',
            component: () => import('@/views/serviceQuality/degreeSatisfaction/preview/preview.vue'),
            meta: {
              title: '预览',
              sidebar: false,
              activeMenu: '/degreeSatisfaction/newQuestionnaire'
            }
          },
          {
            path: 'QuestionView',
            name: 'QuestionView',
            component: () => import('@/views/serviceQuality/degreeSatisfaction/QuestionView/QuestionView.vue'),
            meta: {
              title: '查看答卷',
              sidebar: false,
              activeMenu: '/degreeSatisfaction/newQuestionnaire'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/reportManagement',
    component: Layout,
    redirect: '/reportManagement/templateManagement',
    name: 'reportManagement',
    meta: {
      title: '报告管理',
      menuAuth: '/reportManagement'
    },
    children: [
      {
        path: 'templateManagement',
        component: EmptyLayout,
        redirect: {
          name: 'templateManagement'
        },
        meta: {
          title: '模板管理',
          menuAuth: '/reportManagement/templateManagement'
        },
        children: [
          {
            path: '',
            name: 'WorkOrder',
            component: () => import('@/views/serviceQuality/reportManagement/template.vue'),
            meta: {
              title: '模板管理',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'index',
        component: EmptyLayout,
        redirect: {
          name: 'index'
        },
        meta: {
          title: '报告管理',
          menuAuth: '/reportManagement/index'
        },
        children: [
          {
            path: '',
            name: 'WorkOrder',
            component: () => import('@/views/serviceQuality/reportManagement/index.vue'),
            meta: {
              title: '报告管理',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      }
    ]
  },
  {
    path: '/linenManagement',
    component: Layout,
    redirect: '/linenManagement/linenLedger',
    name: 'linenManagement',
    meta: {
      title: '布草管理',
      menuAuth: '/linenManagement'
    },
    children: [
      {
        path: 'linenLedger',
        component: EmptyLayout,
        redirect: {
          name: 'linenLedger'
        },
        meta: {
          title: '布草台账',
          menuAuth: '/linenManagement/linenLedger'
        },
        children: [
          {
            path: '',
            name: 'linenLedger',
            component: () => import('@/views/serviceQuality/linenManagement/linenLedger.vue'),
            meta: {
              title: '布草台账',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'receive',
        component: EmptyLayout,
        redirect: {
          name: 'receive'
        },
        meta: {
          title: '收发数据',
          menuAuth: '/linenManagement/receive'
        },
        children: [
          {
            path: '',
            name: 'receive',
            component: () => import('@/views/serviceQuality/linenManagement/receive.vue'),
            meta: {
              title: '收发数据',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'statisticAnalysis',
        component: EmptyLayout,
        redirect: {
          name: 'statisticAnalysis'
        },
        meta: {
          title: '统计分析',
          menuAuth: '/linenManagement/statisticAnalysis'
        },
        children: [
          {
            path: '',
            name: 'statisticAnalysis',
            component: () => import('@/views/serviceQuality/linenManagement/statisticAnalysis.vue'),
            meta: {
              title: '统计分析',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      }
    ]
  },
  {
    path: '/cateringManagement',
    component: Layout,
    name: 'cateringManagement',
    meta: {
      title: '餐饮管理',
      menuAuth: '/cateringManagement'
    },
    children: [
      {
        path: 'overview',
        component: EmptyLayout,
        meta: {
          title: '运营总览',
          menuAuth: '/cateringManagement/overview'
        },
        children: [
          {
            path: '',
            name: 'overview',
            component: () => import('@/views/microApp/index.vue'),
            meta: {
              title: '运营总览',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'comprehensive',
        component: EmptyLayout,
        meta: {
          title: '订餐综合查询',
          menuAuth: '/cateringManagement/comprehensive'
        },
        children: [
          {
            path: '',
            name: 'cateringComprehensive',
            component: () => import('@/views/microApp/index.vue'),
            meta: {
              title: '订餐综合查询',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'foodsales',
        component: EmptyLayout,
        meta: {
          title: '菜品销售统计',
          menuAuth: '/cateringManagement/foodsales'
        },
        children: [
          {
            path: '',
            name: 'cateringFoodsales',
            component: () => import('@/views/microApp/index.vue'),
            meta: {
              title: '菜品销售统计',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'complaint',
        component: EmptyLayout,
        meta: {
          title: '投诉管理',
          menuAuth: '/cateringManagement/complaint'
        },
        children: [
          {
            path: '',
            name: 'cateringComplaint',
            component: () => import('@/views/microApp/index.vue'),
            meta: {
              title: '投诉管理',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'merchantConfiguration',
        component: EmptyLayout,
        meta: {
          title: '商家配置',
          menuAuth: '/cateringManagement/merchantConfiguration'
        },
        children: [
          {
            path: '',
            name: 'cateringMerchant',
            component: () => import('@/views/microApp/index.vue'),
            meta: {
              title: '商家配置',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'bedConfiguration',
        component: EmptyLayout,
        meta: {
          title: '床位管理',
          menuAuth: '/cateringManagement/bedConfiguration'
        },
        children: [
          {
            path: '',
            name: 'cateringBed',
            component: () => import('@/views/microApp/index.vue'),
            meta: {
              title: '床位管理',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      }
    ]
  },
  {
    path: '/oneStopConfigurationManage',
    component: Layout,
    redirect: '/oneStopConfigurationManage/teamManage',
    name: 'oneStopConfigurationManage',
    meta: {
      title: '配置管理',
      menuAuth: '/oneStopConfigurationManage'
    },
    children: [
      {
        path: 'superviseManage',
        component: EmptyLayout,
        redirect: { name: 'superviseManage' },
        meta: {
          title: '督办管理',
          menuAuth: '/oneStopConfigurationManage/superviseManage'
        },
        children: [
          {
            path: '',
            name: 'superviseManage',
            component: () => import('@/views/serviceQuality/oneStopConfigurationManage/supervise-manage/SuperviseList.vue'),
            meta: {
              title: '督办管理',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/oneStopConfigurationManage/superviseManage'
            }
          },
          {
            path: 'rule',
            name: 'superviseRule',
            component: () => import('@/views/serviceQuality/oneStopConfigurationManage/supervise-manage/SuperviseRule.vue'),
            meta: {
              title: '督办规则',
              sidebar: false,
              activeMenu: '/oneStopConfigurationManage/superviseManage'
            }
          },
          {
            path: 'record',
            name: 'superviseRecord',
            component: () => import('@/views/serviceQuality/oneStopConfigurationManage/supervise-manage/SuperviseRecord.vue'),
            meta: {
              title: '督办记录',
              sidebar: false,
              activeMenu: '/oneStopConfigurationManage/superviseManage'
            }
          },
          {
            path: 'detail',
            name: 'superviseDetail',
            component: () => import('@/views/serviceQuality/oneStopConfigurationManage/supervise-manage/SuperviseDetail.vue'),
            meta: {
              title: '督办详情',
              sidebar: false,
              activeMenu: '/oneStopConfigurationManage/superviseManage'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/dictionary',
    component: Layout,
    redirect: '/dictionary/DictionaryList',
    name: 'dictionary',
    meta: {
      title: '系统管理',
      menuAuth: '/dictionary'
    },
    children: [
      {
        path: 'DictionaryList',
        component: EmptyLayout,
        redirect: { name: 'DictionaryList' },
        meta: {
          title: '字典管理',
          menuAuth: '/dictionary/DictionaryList'
        },
        children: [
          {
            path: '',
            name: 'DictionaryList',
            component: () => import('@/views/serviceQuality/dictionary/DictionaryList.vue'),
            meta: {
              title: '字典管理',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/dictionary/DictionaryList'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/spaceShareManage',
    component: Layout,
    name: 'spaceShareManage',
    redirect: '/spaceShareManage/spaceShare',
    meta: {
      title: '空间分摊管理',
      menuAuth: '/spaceShareManage'
    },
    children: [
      {
        path: 'spaceShare',
        component: EmptyLayout,
        meta: {
          title: '空间分摊',
          menuAuth: '/spaceShareManage/spaceShare'
        },
        children: [
          {
            path: '',
            name: 'spaceShare',
            component: () => import('@/views/serviceQuality/spaceShareManage/spaceShare/index.vue'),
            meta: {
              title: '空间分摊',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'operateRecord',
            name: 'operateRecord',
            component: () => import('@/views/serviceQuality/spaceShareManage/spaceShare/operateRecord.vue'),
            meta: {
              title: '操作记录',
              sidebar: false,
              activeMenu: '/spaceShareManage/spaceShare'
            }
          }
        ]
      },
      {
        path: 'areaStat',
        component: EmptyLayout,
        meta: {
          title: '面积统计',
          menuAuth: '/spaceShareManage/areaStat'
        },
        children: [
          {
            path: '',
            name: 'areaStat',
            component: () => import('@/views/serviceQuality/spaceShareManage/areaStat/index.vue'),
            meta: {
              title: '面积统计',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      }
    ]
  }
]
