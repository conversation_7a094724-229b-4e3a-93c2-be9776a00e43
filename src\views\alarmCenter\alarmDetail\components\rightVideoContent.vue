<template>
  <div class="content_page">
    <div class="title">
      报警画面
      <el-tooltip class="item" effect="dark" content="实时监控可在专业客户端查看" placement="top">
        <i style="color: #96989a; margin-left: 5px" class="el-icon-info"></i>
      </el-tooltip>
    </div>
    <div v-for="(item, i) in videoContentList" :key="i" class="video_content">
      <div class="video_title">
        <div @click="collapse(item)"><i :class="`el-icon-caret-${item.show ? 'bottom' : 'right'}`" style="color: #96989a; margin-right: 5px"></i>{{ item.title }}</div>
        <el-radio-group v-model="item.type" size="mini" @change="(e) => videoTypeChange(item, e)">
          <el-radio-button label="1">四屏</el-radio-button>
          <el-radio-button label="2">一屏</el-radio-button>
        </el-radio-group>
      </div>
      <template v-if="item.show">
        <div v-if="item.type == '1'" class="video_group">
          <template v-if="item.videoList.length">
            <div v-for="(videoItem, index) in item.videoList" :key="index" class="video_Card_item">
              <alarmRtspCavas
                ref="rtspCavas"
                style="width: 100%; height: 100%"
                functionId="previewStreaming"
                :cameraId="videoItem.id"
                :rtspUrl="videoItem.url"
                :videoName="videoItem.name"
                :hasCavas="Boolean(item.videoList)"
              ></alarmRtspCavas>
            </div>
          </template>
          <div v-else class="video_Carousel_item_empty">
            <img src="@/assets/images/elevator/no-camera-list.png" alt="" />
            <div>{{ '暂无关联' + (i == 0 ? '监控' : '录像') }}</div>
          </div>
        </div>
        <div v-else class="video_group">
          <el-carousel v-if="item.videoList.length" height="100%" class="video_Carousel_item">
            <el-carousel-item v-for="(videoItem, index) in item.videoList" :key="index">
              <alarmRtspCavas
                ref="rtspCavas"
                style="width: 100%; height: 100%"
                functionId="playbackStreaming"
                :cameraId="videoItem.id"
                :rtspUrl="videoItem.url"
                :videoName="videoItem.name"
              ></alarmRtspCavas>
            </el-carousel-item>
          </el-carousel>
          <div v-else class="video_Carousel_item_empty">
            <img src="@/assets/images/elevator/no-camera-list.png" alt="" />
            <div>{{ '暂无关联' + (i == 0 ? '监控' : '录像') }}</div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>
<script>
export default {
  components: {
    alarmRtspCavas: () => import('./alarmRtspCavas.vue')
  },
  props: {
    detail: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      activeNames: ['1'],
      radio1: '四屏',
      videoContentList: [
        {
          title: '关联监控',
          show: true,
          type: '1',
          videoList: []
        },
        {
          title: '报警录像',
          show: true,
          type: '1',
          videoList: []
        }
      ],
      playerFullscreen: false
    }
  },
  watch: {
    detail: {
      handler(val) {
        this.getVideoList(val.alarmObjectId)
        this.getAlarmPicRecording()
      },
      deep: true
    }
  },
  methods: {
    getVideoList(id) {
      this.$api.videoList({ imsCode: id, alarmStartTime: this.detail.alarmStartTime }).then((res) => {
        if (res.code == 200) {
          let arr = []
          // 老监测
          if (res.data.length) {
            arr = res.data.map((item) => {
              return {
                url: item.icmRtsp,
                id: item.icmId,
                name: item.icmName || ''
              }
            })
          } else {
            arr = this.getNewMonitorCameraData()
          }
          this.videoContentList[0].videoList = arr
        }
      })
    },
    getAlarmPicRecording() {
      this.$api.picRecording({ alarmId: this.detail.alarmId, fileType: 2 }).then((res) => {
        if (res.code == 200) {
          let arr = []
          // 老监测
          if (res.data.length) {
            arr = res.data.map((item) => {
              return {
                url: item.fileUrl,
                id: item.id,
                name: item.name || ''
              }
            })
          } else {
            arr = this.getNewMonitorCameraData()
          }
          this.videoContentList[1].videoList = arr
        }
      })
    },
    getNewMonitorCameraData() {
      if (this.detail.indexCode && this.detail.indexCode.length > 0 && !res.data.length) {
        return this.detail.indexCode.split(',').map((item) => {
          return {
            url: '',
            id: item,
            name: ''
          }
        })
      } else {
        return []
      }
    },
    collapse(item) {
      item.show = !item.show
    },
    videoTypeChange(item, type) {
      item.type = type
    }
  }
}
</script>
<style lang="scss" scoped>
.content_page {
  width: 100%;
}
.title {
  display: flex;
  align-items: center;
  font-size: 16px;
  color: #333;
  &::before {
    content: '';
    background: #3562db;
    width: 6px;
    height: 6px;
    margin-right: 6px;
  }
}
.video_content {
  width: 100%;
  margin-top: 16px;
  .video_title {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .video_group {
    margin-top: 6px;
    width: 100%;
    height: 472px;
    background: #f6f5fa;
    padding: 16px;
    padding-top: 0;
    padding-left: 0;
    display: flex;
    flex-wrap: wrap;
    .video_Card_item {
      margin-left: 16px;
      margin-top: 16px;
      width: calc(50% - 16px);
      height: calc(50% - 16px);
    }
    .video_Card_item_empty {
      margin-left: 16px;
      margin-top: 16px;
      width: calc(50% - 16px);
      height: calc(50% - 16px);
      display: flex;
      align-items: center;
      justify-content: center;
      background: #fff;
    }
    .video_Carousel_item {
      margin: 16px;
      margin-right: 0;
      width: 100%;
      height: calc(100% - 16px);
    }
    .video_Carousel_item_empty {
      margin: 16px;
      margin-right: 0;
      width: 100%;
      height: calc(100% - 16px);
      background: #fff;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      img {
        height: calc(60%);
      }
      div {
        margin-top: 16px;
        font-size: 14px;
        color: #96989a;
        flex-shrink: 0;
      }
    }
  }
}
</style>
