<template>
  <div style="width: 100%;height: 100%;padding: 10px;" class="drag_class">
    <div class="card_box_title card_box_short_bg">温度报警排行（近30天）</div>
    <div v-if="!chartInfo.show" class="echart-null">
      <img src="@/assets/images/null.png" alt="" />
      <div>恭喜，近期无该类型报警，继续努力!</div>
    </div>
    <div v-else style="width: 100%; height: calc(100% - 31px)">
      <div id="heatAlarm" ref="heatAlarm"></div>
    </div>
  </div>
</template>
<script>
import moment from 'moment'
import { monitorTypeList } from '@/util/dict.js'
import * as echarts from 'echarts'
export default {
  data() {
    return {
      requestHttp: __PATH.VUE_IEMC_ELEVATOR_API,
      projectCode: monitorTypeList.find((item) => item.projectName == '电梯监测').projectCode, // 电梯code
      chartInfo: {
        dom: 'heatAlarm',
        show: true,
        title: '温度报警排行',
        type: '温度报警',
        color: ['#F4DB67', 'rgba(244,219,103,0)'],
        unit: '层',
        parameterIds: '2748'
      },
      timer: null
    }
  },
  mounted() {
    this.getElevatorFloorList()
    this.scheduledTasks()
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
  },
  methods: {
    scheduledTasks() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
      this.timer = setInterval(() => {
        this.getElevatorFloorList()
      }, 30000)
    },
    echartsResize() {
      this.$nextTick(() => {
        setTimeout(() => {
          if (document.getElementById('heatAlarm')) {
            echarts.init(document.getElementById('heatAlarm')).resize()
          }
        }, 50)
      })
    },
    // 获取卡层
    getElevatorFloorList() {
      let data = {
        projectCode: this.projectCode,
        parameterIds: this.chartInfo.parameterIds,
        startTime: moment().date(1).format('YYYY-MM-DD'),
        endTime: moment().format('YYYY-MM-DD')
      }
      this.$api.getElevatorFaultData(data, this.requestHttp).then((res) => {
        if (res.code === '200') {
          let nameList = []
          let valueList = []
          res.data[0][this.chartInfo.parameterIds].forEach(item => {
            nameList.push(item.surveyName)
            valueList.push({
              name: item.surveyName,
              value: item.count,
              code: item.surveyCode,
              id: item.parameterId,
              title: item.parameterName
            })
          })
          this.chartInfo.show = !!nameList.length
          setTimeout(() => {
            this.setRankingBarEcharts(this.chartInfo.dom, nameList, valueList, this.chartInfo.color, this.chartInfo.unit)
          }, 50)
        }
      })
    },
    // 电梯4个状态数据echarts
    setRankingBarEcharts(dom, nameList, valueList, color, unit) {
      valueList = valueList.reverse()
      nameList = nameList.reverse()
      const getchart = echarts.init(this.$refs[dom])
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '6%',
          right: '15%',
          top: '6%',
          bottom: '5%',
          containLabel: true
        },
        xAxis: [
          {
            name: unit,
            type: 'value',
            position: 'top',
            splitNumber: 3,
            splitLine: {
              lineStyle: {
                color: '#283849',
                type: 'dashed'
              }
            },
            axisLabel: {
              textStyle: {
                color: '#6A737C'
              },
              fontSize: 11,
              interval: 30000,
              hideOverlap: true // 隐藏互相遮挡的文本标签
            }
          }
        ],
        yAxis: [
          {
            type: 'category',
            data: nameList,
            axisTick: { show: false },
            axisLine: {
              lineStyle: {
                color: '#283849',
                type: 'dashed'
              }
            },
            axisLabel: {
              textStyle: {
                color: '#6A737C'
              },
              margin: 8,
              interval: 0,
              hideOverlap: true, // 隐藏互相遮挡的文本标签
              formatter: function (val) {
                var strs = val.split('') // 字符串数组
                var str = ''
                // strs循环 forEach 每五个字符增加换行符
                strs.forEach((item, index) => {
                  if (index % 6 === 0 && index !== 0) {
                    str += '\n'
                  }
                  str += item
                })
                return str
              }
            }
          }
        ],
        series: [
          {
            type: 'bar',
            barWidth: 8,
            sort: false,
            data: valueList,
            itemStyle: {
              normal: {
                color: color[0]
                // color: function (params) {
                //   // 首先定义一个数组
                //   var colorList = ['#2784E9', '#F4DB67']
                //   if (params.dataIndex % 2 == 0) {
                //     return colorList[0]
                //   } else {
                //     return colorList[1]
                //   }
                // }
                // color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                //   {
                //     offset: 0,
                //     color: color[0]
                //   },
                //   {
                //     offset: 1,
                //     color: color[1]
                //   }
                // ])
              }
            }
          }
        ],
        dataZoom: [
          {
            type: 'slider',
            show: true,
            orient: 'vertical',
            // 设置组件控制的y轴
            yAxisIndex: 0,
            right: 4,
            start: 100,
            // end: 0,
            width: 8,
            // borderRadius: 0,
            borderColor: 'transparent',
            fillerColor: '#6580b8', // 滑动块的颜色
            backgroundColor: 'transparent', // 两边未选中的滑动条区域的颜色
            // 是否显示detail，即拖拽时候显示详细数值信息
            showDataShadow: false,
            showDetail: false,
            zoomLock: true,
            // 控制手柄的尺寸
            // handleSize: 12,
            // 是否在 dataZoom-silder 组件中显示数据阴影。数据阴影可以简单地反应数据走势。
            filterMode: 'filter',
            handleIcon: 'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
            handleStyle: {
              color: '#6580b8',
              borderColor: '#6580b8'
            },
            maxValueSpan: 6,
            minValueSpan: 6,
            brushSelect: false
          },
          {
            type: 'inside',
            // show: false,
            yAxisIndex: [0],
            zoomOnMouseWheel: false, // 关闭滚轮缩放
            moveOnMouseWheel: true, // 开启滚轮平移
            moveOnMouseMove: true // 鼠标移动能触发数据窗口平移
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 先移除点击事件 解决点击事件重复绑定
      getchart.off('click')
      // 点击事件
      getchart.on('click', (params) => {
        this.$emit('barClick', params.data)
      })
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.card_box_short_bg {
  background: url('~@/assets/images/elevator/module-bg.png') no-repeat;
  background-size: 100% 100%;
}
.card_box_title {
  height: 31px;
  width: 100%;
  line-height: 31px;
  padding-left: 10px;
  font-size: 15px;
  font-family: Alibaba PuHuiTi-Medium, Alibaba PuHuiTi;
  font-weight: 500;
  color: #b7cfff;
}
.card_box_bg {
  background: url('~@/assets/images/elevator/card-title-bg.png') no-repeat;
  background-size: 100% 100%;
}
.echarts_title {
  height: 20px;
  font-size: 15px;
  font-family: NotoSansHans-Medium, NotoSansHans;
  font-weight: 600;
  color: #393a3d;
  line-height: 20px;
  text-align: center;
}
.echart-null {
  margin: 0 auto;
  height: calc(100% - 31px);
  width: 50%;
  text-align: center;
  color: #8a8c8f;
  img {
    max-width: 100%;
    max-height: calc(100% - 20px);
  }
  div {
    font-size: 14px;
  }
}
#heatAlarm {
  width: 100%;
  height: 100%;
  z-index: 2;
}
</style>
