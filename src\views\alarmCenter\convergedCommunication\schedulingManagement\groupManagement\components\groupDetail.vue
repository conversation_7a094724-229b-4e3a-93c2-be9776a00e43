<template>
  <el-dialog title="群组详情" width="55%" :visible.sync="groupDetailDialogShow" custom-class="model-dialog"
    :before-close="closeDialog">
    <div class="content">
      <el-form ref="formInline" :model="itemInfo" label-width="100px" label-position="right">
        <el-form-item label="频道类型" prop="callGrouptype" class="form-item">
          <div class="input_width">
            <span>{{itemInfo.callGrouptype===1?'电话会议组':itemInfo.callGrouptype===4?'语音对讲组':''}}</span>
          </div>
        </el-form-item>
        <el-form-item label="频道号" prop="groupHostextension" class="form-item">
          <div class="input_width">
            <span>{{itemInfo.groupHostextension}}</span>
          </div>
        </el-form-item>
        <el-form-item label="群组名称" prop="groupHostextension" class="form-item">
          <div class="input_width">
            <span>{{itemInfo.groupName}}</span>
          </div>
        </el-form-item>
        <el-form-item label="备注" prop="groupDesc" class="inputWidth">
          <div class="input_width">
            <span>{{itemInfo.groupDesc}}</span>
          </div>
        </el-form-item>
        <el-form-item label="对应岗位">
          <span v-for="item in postList" :key="item.id" class="postItem">{{ item.postName }} </span>
        </el-form-item>
        <el-form-item prop="office" label="群组终端">
          <el-table :data="terminalList" height="250" class="terminalTable" stripe>
            <el-table-column prop="usrName" show-overflow-tooltip label="名称"> </el-table-column>
            <el-table-column prop="extensionModel" label="型号" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="extensionNum" label="号码" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="usrType" label="类型" align="center" show-overflow-tooltip>
              <template slot-scope="scope">
                <span>{{ scope.row.usrType | filterType }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="groupOnlineState" label="状态" show-overflow-tooltip>
              <template slot-scope="scope">
                <div v-if="scope.row.groupOnlineState=='0'" class="statusClass"><span class="online"></span>离线</div>
                <div v-if="scope.row.groupOnlineState=='1'" class="statusClass"><span class="noJoined"></span>未进入群组
                </div>
                <div v-if="scope.row.groupOnlineState=='2'" class="statusClass"><span class="offOnline"></span>已进入群组
                </div>
              </template>
            </el-table-column>
          </el-table>
          <!-- <el-pagination :current-page="parameter.pageNo" :page-sizes="[10, 30, 50, 100]"
            :page-size="parameter.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="parameter.total"
            @size-change="handleTerminalSizeChange" @current-change="handleTerminalCurrentChange"></el-pagination> -->
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'groupDetail',
  components: {},
  filters: {
    filterType(val) {
      return val == '2'
        ? '智能调度终端'
        : val == '3'
          ? '移动调度APP'
          : val == '1'
            ? '调度台'
            : val == '4'
              ? '数字对讲网关'
              : '电话网关'
    }
  },
  props: {
    groupDetailDialogShow: {
      type: Boolean,
      default: false
    },
    groupDetailId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      terminalList: [],
      postList: [],
      itemInfo: {}
    }
  },
  mounted() {
    this.getById()
  },
  methods: {
    getById() {
      this.$api.callgroupDetails({ id: this.groupDetailId }).then((res) => {
        if (res.code === '200') {
          this.postList = []
          this.terminalList = res.data.members
          this.itemInfo = res.data
          if (res.data.postId) {
            let arrId = res.data.postId.split(',')
            let arrName = res.data.postName.split(',')
            arrId.map((label, index) => {
              this.postList.push({
                id: label,
                postName: arrName[index]
              })
            })
          }
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    closeDialog() {
      this.$emit('closeGroupDetailDialog')
    }
  }
}
</script>
<style lang="scss" scoped>
.model-dialog {
  .content {
    width: 100%;
    max-height: 480px !important;
    overflow: auto;
    background-color: #fff !important;
  }
  .terminalTable {
    margin-top: 10px;
  }
  .postItem {
    padding: 5px 10px;
    border-radius: 6px;
    text-align: center;
    color: #3562db;
    margin-right: 10px;
    background: rgba(53, 98, 219, 0.2);
  }
  .input_width {
    width: 300px;
    .el-select,
    .el-date-editor.el-input,
    .el-cascader {
      width: 100%;
    }
  }
}
.el-form-item {
  margin-bottom: 8px !important;
}
.statusClass {
  display: flex;
  align-items: center;
}
.online {
  width: 6px;
  height: 6px;
  border-radius: 6px;
  background-color: #aaaaaa;
  margin-right: 6px;
}
.offOnline {
  width: 6px;
  height: 6px;
  border-radius: 6px;
  background-color: #33cc00;
  margin-right: 6px;
}
.noJoined {
  width: 6px;
  height: 6px;
  border-radius: 6px;
  background-color: #f59a23;
  margin-right: 6px;
}
</style>

