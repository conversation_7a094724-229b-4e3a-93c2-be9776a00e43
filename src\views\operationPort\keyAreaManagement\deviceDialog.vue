<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    title="选择设备"
    width="52%"
    :visible.sync="visible"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <div class="dialog-content">
      <div style="margin-bottom: 20px">
        <el-input v-model="searchFrom.name" style="width: 180px; margin-right: 10px" placeholder="请输入名称/编码"></el-input>
        <el-select v-model.trim="searchFrom.professionalCategoryCode" filterable placeholder="设备分类" style="width: 180px; margin-right: 10px" @change="onMajorType">
          <el-option v-for="item in majorList" :key="item.id" :label="item.baseName" :value="item.id"> </el-option>
        </el-select>
        <el-cascader
          ref="systemCategoryCode"
          :key="systemIndex"
          v-model="searchFrom.systemCategoryCode"
          :props="systemPropsType"
          :options="systemCodeList"
          :collapse-tags="true"
          placeholder="设备类型"
          style="width: 180px; margin-right: 10px"
        ></el-cascader>
        <el-cascader
          ref="regionCode"
          v-model="searchFrom.regionCode"
          :props="riskPropsType"
          :options="regionCodeList"
          :collapse-tags="true"
          placeholder="设备位置"
          class="cascaderWid"
          :show-all-levels="true"
          style="width: 180px; margin-right: 10px"
        ></el-cascader>
        <el-button plain type="primary" @click="reset">重置</el-button>
        <el-button type="primary" @click="search">查询</el-button>
      </div>
      <el-table v-loading="tableLoading" :data="tableData" height="300" style="width: 100%" element-loading-background="rgba(0, 0, 0, 0.2)" @selection-change="selectionChange">
        <el-table-column fixed align="center" type="selection" width="45px"></el-table-column>
        <el-table-column fixed prop="assetName" show-overflow-tooltip label="设备名称"></el-table-column>
        <el-table-column fixed prop="assetCode" show-overflow-tooltip label="设备编码"></el-table-column>
        <el-table-column fixed prop="professionalCategoryName" show-overflow-tooltip label="设备分类"></el-table-column>
        <el-table-column fixed prop="systemCategoryName" show-overflow-tooltip label="设备类型"></el-table-column>
        <el-table-column fixed prop="regionReverseName" show-overflow-tooltip label="所在位置"></el-table-column>
        <el-table-column fixed prop="centralizedDepartmentName" show-overflow-tooltip label="所属科室"></el-table-column>
        <el-table-column fixed prop="assetModel" show-overflow-tooltip label="型号"></el-table-column>
      </el-table>
      <el-pagination
        :current-page="pageNo"
        :page-sizes="[15, 20, 30, 40]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="confirm('ruleForm')">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { transData } from '@/util'
import * as forEach from 'lodash/forEach'
export default {
  name: 'spaceDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      searchFrom: {
        name: '',
        professionalCategoryCode: '',
        systemCategoryCode: '',
        regionCode: ''
      },
      tableLoading: false,
      tableData: [],
      pageNo: 1,
      pageSize: 15,
      total: 0,
      majorList: [],
      systemCodeList: [], // 设备类型
      regionCodeList: [], // 设备位置
      systemPropsType: {
        children: 'children',
        label: 'baseName',
        value: 'id',
        checkStrictly: true,
        multiple: false
      },
      riskPropsType: {
        children: 'children',
        label: 'ssmName',
        value: 'id',
        checkStrictly: true,
        multiple: false
      },
      selectDeviceData: [],
      systemIndex: 0
    }
  },
  watch: {},
  created() {
    this.init()
    this.getTableData()
  },
  methods: {
    init() {
      // 设备分类
      this.$api
        .getDeviceType({
          levelType: 1
        })
        .then((res) => {
          if (res.code == '200') {
            this.majorList = res.data
          }
        })
      // 设备位置
      this.$api.spaceTree().then((res) => {
        this.isTreeData = true
        if (res.code == '200') {
          this.regionCodeList = transData(res.data, 'id', 'pid', 'children')
        }
      })
    },
    confirm() {
      // if (!this.selectSpace.length) {
      //   this.$message({ message: '请选择空间', type: 'error' })
      //   return
      // }
      this.$emit('selectDevice', this.selectDeviceData)
      this.closeDialog()
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', !this.visible)
    },
    // 查询二级以下设备字典
    onMajorType(val) {
      this.systemCodeList = []
      let data = {
        startLevel: '2',
        parentId: val,
        levelType: '5'
      }
      this.$api.getDeviceType(data).then((res) => {
        if (res.code == '200') {
          this.systemCodeList = transData(res.data, 'id', 'parentId', 'children')
          ++this.systemIndex
        }
      })
    },
    getTableData() {
      let params = {
        pageSize: this.pageSize,
        currentPage: this.pageNo,
        assetName: this.searchFrom.name, // 设备名称
        professionalCategoryCode: this.searchFrom.professionalCategoryCode, // 设备分类
        systemCategoryCode: this.searchFrom.systemCategoryCode[0], // 设备类别
        regionCode: this.searchFrom.regionCode[this.searchFrom.regionCode.length - 1] // 设备位置
      }
      this.tableLoading = true
      this.$api.getAssetList(params).then((res) => {
        if (res.code == '200') {
          this.tableData = res.data.assetDetailsList
          this.total = parseInt(res.data.sum)
        }
      })
      this.tableLoading = false
    },
    search() {
      this.pageNo = 1
      this.getTableData()
    },
    reset() {
      this.pageNo = 1
      this.pageSize = 15
      this.searchFrom.name = ''
      this.searchFrom.professionalCategoryCode = ''
      this.searchFrom.systemCategoryCode = ''
      this.searchFrom.regionCode = ''
      this.getTableData()
    },
    // 选中框
    selectionChange(val) {
      this.selectDeviceData = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getTableData()
    },
    handleCurrentChange(val) {
      this.pageNo = val
      this.getTableData()
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .model-dialog {
  .dialog-content {
    width: 100%;
    background: #fff;
    padding: 10px;
  }
}
.el-table-column--selection .cell {
  padding-left: 0.875rem;
  padding-right: 0.875rem;
  text-overflow: initial;
}
.redColor {
  color: red;
}
</style>
