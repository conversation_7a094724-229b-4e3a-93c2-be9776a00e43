<template>
  <PageContainer>
    <div slot="content" class="page_content">
      <div class="top">
        <div class="top_left">
          <div class="title">合同总数</div>
          <div class="block_content">
            <div class="block_top">
              <img src="@/assets/images/parkingLot/count-four.png" alt="" />
              <div class="tips">
                <div>合同总数</div>
                <div>
                  <span class="num">{{ statisticsInfo.sumCount }}</span>
                  <span>份</span>
                </div>
              </div>
            </div>
            <div class="block_bottom">
              <div class="title">状态统计</div>
              <div class="statusStatistics">
                <div class="statusStatistics_item">
                  <div class="num">未开始</div>
                  <div>
                    <span class="tips">{{ statisticsInfo.notStartedCount }}</span>
                    <span style="color: #96989a">份</span>
                  </div>
                </div>
                <div class="statusStatistics_item">
                  <div class="num">履约中</div>
                  <div>
                    <span class="tips">{{ statisticsInfo.inProgressCount }}</span>
                    <span style="color: #96989a">份</span>
                  </div>
                </div>
                <div class="statusStatistics_item">
                  <div class="num">已结束</div>
                  <div>
                    <span class="tips">{{ statisticsInfo.endedCount }}</span>
                    <span style="color: #96989a">份</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="top_center">
          <div class="title">合同类型分布</div>
          <div id="pieChart" class="pieChart"></div>
        </div>
        <div class="top_right">
          <div class="title">合同金额</div>
          <div class="block_content">
            <img class="icon" src="@/assets/images/operationPort/money.png" />
            <div class="tips">合同金额</div>
            <div class="num">{{ archiveAmount }}</div>
          </div>
        </div>
      </div>
      <div class="bottom">
        <div class="bottom_left">
          <div class="title">签署部门排行/份</div>
          <div id="barChart" class="barChart"></div>
        </div>
        <div class="bottom_right">
          <div class="title">更多排名</div>
          <div style="height: calc(100% - 24px)">
            <TablePage
              ref="table"
              :showPage="true"
              :tableColumn="tableColumn"
              :data="tableData"
              height="calc(100% - 40px)"
              :pageData="pageData"
              :pageProps="pageProps"
              @pagination="paginationChange"
            >
            </TablePage>
          </div>
        </div>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import * as echarts from 'echarts'
export default {
  data() {
    return {
      statisticsInfo: {
        sumCount: 0,
        notStartedCount: 0,
        inProgressCount: 0,
        endedCount: 0
      },
      archiveAmount: 0,
      tableData: [],
      tableColumn: [
        {
          prop: '',
          label: '序号',
          formatter: (scope) => {
            return (this.pageData.current - 1) * this.pageData.size + scope.$index + 1
          },
          width: 80
        },
        {
          prop: 'depName',
          label: '部门'
        },
        {
          prop: 'num',
          label: '数量（份）'
        }
      ],
      pageData: {
        current: 1,
        size: 15,
        total: 0
      },
      pageProps: {
        page: 'current',
        pageSize: 'size',
        total: 'total',
        layoutOptions: 'prev, pager, next'
      }
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.handleGetStatistics()
      this.getArchiveTypeStatistics()
      this.getAmountStatistics()
      this.getSignerStatistics()
      this.getArchiveDepStatisticsMorePage()
    },
    // 获取合同总数及状态统计
    handleGetStatistics() {
      this.$api.fileManagement.getArchiveStatistics({ archiveType: '0', isMine: false }).then((res) => {
        this.statisticsInfo = res.data
      })
    },
    // 获取合同类型分布
    getArchiveTypeStatistics() {
      this.$api.fileManagement.getArchiveTypeStatistics().then((res) => {
        if (res.code == 200) {
          const data = res.data.map((e) => {
            return {
              name: e.typeName,
              value: e.num
            }
          })
          this.pieChartData(data)
        } else {
          this.$message.warning(res.msg || res.message)
        }
      })
    },
    // 获取合同金额
    getAmountStatistics() {
      const params = {
        archiveType: '0',
        isMine: false
      }
      this.$api.fileManagement.getAmountStatistics(params).then((res) => {
        if (res.code == 200) {
          this.archiveAmount = res.data
        } else {
          this.$message.warning(res.msg || res.message)
        }
      })
    },
    // 获取签署部门
    getSignerStatistics() {
      const params = {
        archiveType: '0',
        isMine: false
      }
      this.$api.fileManagement.getArchiveDepStatistics(params).then((res) => {
        if (res.code == 200) {
          const nameArr = []
          const valueArr = []
          res.data
            .sort((a, b) => b.top - a.top)
            .map((e) => {
              nameArr.push(e.depName)
              valueArr.push(e.num)
            })
          this.barChartData(nameArr, valueArr)
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    // 获取更多排行
    getArchiveDepStatisticsMorePage() {
      const params = {
        archiveType: '0',
        isRecycle: '0',
        statisticsFlag: '1',
        isMine: false
      }
      Object.assign(params, this.pageData)
      this.$api.fileManagement.getArchiveDepStatisticsMorePage(params).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.records
          this.pageData.total = res.data.total
        } else {
          this.$message.warning(res.msg || res.message)
        }
      })
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getArchiveDepStatisticsMorePage()
    },
    barChartData(nameArr, valueArr) {
      let option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {},
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01]
        },
        yAxis: {
          type: 'category',
          data: nameArr
        },
        series: [
          {
            type: 'bar',
            data: valueArr
          }
        ]
      }
      let chartDom = document.getElementById('barChart')
      let myChart = echarts.init(chartDom)
      myChart.setOption(option)
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
    pieChartData(data) {
      let option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          left: 'center',
          top: '10%'
        },
        series: [
          {
            type: 'pie',
            radius: '50%',
            data: data,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      let chartDom = document.getElementById('pieChart')
      let myChart = echarts.init(chartDom)
      myChart.setOption(option)
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.page_content {
  width: 100%;
  height: 100%;
  .top,
  .bottom {
    display: flex;
    & > div {
      flex: 1;
      background: #fff;
      margin-left: 16px;
      padding: 24px;
      &:first-child {
        margin-left: 0;
      }
      .title {
        color: #333333;
        font-size: 16px;
        font-weight: bold;
      }
    }
  }
  .top {
    width: 100%;
    height: 496px;
    .top_left {
      .block_top {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 50px;
        margin-top: 13px;
        background: #faf9fc;
        img {
          width: 66px;
          height: 66px;
          margin-right: 24px;
        }
        .tips {
          color: #666666;
          text-align: center;
          .num {
            color: #333333;
            font-size: 32px;
            font-weight: bold;
          }
        }
      }
      .block_bottom {
        width: 100%;
        margin-top: 24px;
        .statusStatistics {
          margin-top: 24px;
          height: 170px;
          display: flex;
          & > div {
            flex: 1;
            display: flex;
            justify-content: center;
            flex-direction: column;
            margin-left: 10px;
            background: #faf9fc;
            color: #333333;
            padding: 16px;
            &:first-child {
              margin-left: 0;
            }
            .num {
              margin-bottom: 24px;
            }
            .tips {
              font-size: 24px;
              font-weight: bold;
            }
          }
        }
      }
    }
    .top_right {
      .block_content {
        margin-top: 24px;
        height: calc(100% - 48px);
        background: #faf9fc;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        img {
          width: 100px;
          height: 100px;
        }
        .tips {
          font-size: 24px;
          margin: 24px 0;
        }
        .num {
          font-size: 50px;
          color: #333333;
          font-weight: bold;
        }
      }
    }
  }
  .bottom {
    width: 100%;
    height: calc(100% - 496px);
    margin-top: 16px;
  }
}
.pieChart,
.barChart {
  width: 100%;
  height: calc(100% - 24px);
}
</style>
