<template>
  <PageContainer>
    <div slot="content" class="form-content">
      <div class="form-title"><i class="el-icon-arrow-left" @click="$router.go(-1)"></i>预案模板详情</div>
      <div class="content-main">
        <el-tabs v-model="activeTabs">
          <el-tab-pane label="基础信息" name="1">
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="11">
                <p class="info-item">
                  <span class="info-label">预案名称：</span>
                  <span class="info-value">{{ templateData.planName }}</span>
                </p>
              </el-col>
              <el-col :md="11">
                <p class="info-item">
                  <span class="info-label">关联预案类型：</span>
                  <span class="info-value">{{ templateData.planType ? planTypeList.find((v) => v.imhMonitorCode == templateData.planType)?.imhMonitorName : '' }}</span>
                </p>
              </el-col>
            </el-row>
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="22">
                <p class="info-item">
                  <span class="info-label">说明：</span>
                  <span class="info-value">{{ templateData.regulationsDesc }}</span>
                </p>
              </el-col>
            </el-row>
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="11">
                <p class="info-item">
                  <span class="info-label">法规流程图：</span>
                  <span class="info-value">
                    <div v-if="templateData.regulationsFlow?.length > 0">
                      <img :src="$tools.imgUrlTranslation(templateData.regulationsFlow[0].url)" @click="viewImage" />
                    </div>
                    <el-image-viewer v-if="showViewer" :on-close="() => (showViewer = false)" :url-list="iconPathList" />
                  </span>
                </p>
              </el-col>
              <el-col :md="11">
                <p class="info-item">
                  <span class="info-label">法规文档：</span>
                  <span class="info-value">
                    <div style="cursor: pointer; color: #3562db" @click="viewFile">
                      {{ templateData.regulationsDoc?.length > 0 ? templateData.regulationsDoc[0].name : '' }}
                    </div>
                  </span>
                </p>
              </el-col>
            </el-row>
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="22">
                <p class="info-item">
                  <span class="info-label">法规文案：</span>
                  <span class="info-value" v-html="templateData.regulationsText"></span>
                </p>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="预案流程" name="2">
            <flowChart v-if="activeTabs == 2" type="view" :eventData="addEventData" />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <div slot="footer"></div>
  </PageContainer>
</template>
<script>
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import flowChart from '../../components/flowChart'
export default {
  name: 'templateDetails',
  components: {
    ElImageViewer,
    flowChart
  },
  async beforeRouteLeave(to, from, next) {
    if (!['planTemplate'].includes(to.name)) {
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      activeTabs: '1',
      templateData: {},
      planTypeList: [],
      showViewer: false,
      iconPathList: [], // 图片列表
      addEventData: {
        1: {}, // 演习
        2: {}, // 演习-启动预案
        3: {}, // 演习-自定义
        4: {}, // 确警
        5: {}, // 确警-启动预案
        6: {} // 确警-自定义
      }
    }
  },
  computed: {},
  activated() {
    this.init()
  },
  created() {
    if (!this.$store.state.keepAlive.list.includes('planTemplate')) {
      this.init()
    }
  },
  methods: {
    init() {
      Object.assign(this.$data, this.$options.data())
      this.getAlarmSystem()
      this.getTemplateData()
    },
    viewFile() {
      window.open(this.$tools.imgUrlTranslation(this.templateData.regulationsDoc[0].url), '_blank')
    },
    // 查看图片
    viewImage() {
      let img = this.templateData.regulationsFlow
      if (!img.length) {
        this.$message.info('当前没有可预览的图片')
        return
      }
      this.iconPathList = [this.$tools.imgUrlTranslation(img[0].url)]
      this.showViewer = true
    },
    // 获取预案类型
    getAlarmSystem() {
      this.$api.getAlarmSystem({ projectCodes: '' }).then((res) => {
        if (res.code == 200) {
          this.planTypeList = res.data
        }
      })
    },
    // 获取模板数据
    getTemplateData() {
      this.$api.GetBasicConfigDetail({ id: this.$route.query?.id }).then((res) => {
        if (res.code == 200) {
          let { regulationsFlow, regulationsDoc, warnEventList, noticeEventList, confirmEventList } = res.data
          res.data.regulationsFlow = JSON.parse(regulationsFlow)
          res.data.regulationsDoc = JSON.parse(regulationsDoc)
          this.templateData = res.data
          warnEventList.forEach((item) => {
            if (Object.keys(this.addEventData[item.stepType]).includes('warnEventList')) {
              this.addEventData[item.stepType].warnEventList.push(item)
            } else {
              this.addEventData[item.stepType].warnEventList = [item]
            }
          })
          noticeEventList.forEach((item) => {
            item.noticeWay = Number(item.noticeWay)
            item.noticeType = Number(item.noticeType)
            if (Object.keys(this.addEventData[item.stepType]).includes('noticeEventList')) {
              this.addEventData[item.stepType].noticeEventList.push(item)
            } else {
              this.addEventData[item.stepType].noticeEventList = [item]
            }
          })
          confirmEventList.forEach((item) => {
            if (Object.keys(this.addEventData[item.stepType]).includes('confirmEventList')) {
              this.addEventData[item.stepType].confirmEventList.push(item)
            } else {
              this.addEventData[item.stepType].confirmEventList = [item]
            }
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.form-content {
  height: 100%;
  overflow-y: auto;
  p {
    margin: 0px;
  }
  .form-title {
    height: 40px;
    line-height: 40px;
    background: #fff;
    border-bottom: 1px solid #dcdfe6;
    font-size: 15px;
    font-family: 'PingFang SC-Medium', 'PingFang SC';
    font-weight: 500;
    color: #121f3e;
    padding-left: 15px;
    i {
      width: 20px;
      cursor: pointer;
    }
  }
  .content-main {
    height: calc(100% - 40px);
    width: 100%;
    background: #fff;
    padding: 24px 0px 0px 0px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    ::v-deep(.el-tabs) {
      height: 100%;
      .el-tabs__nav-wrap {
        padding-left: 60px;
        &::after {
          display: none;
        }
      }
      .el-tabs__content {
        height: calc(100% - 40px);
        .el-tab-pane {
          height: 100%;
          overflow: auto;
        }
      }
    }
    .info-item {
      margin-top: 24px;
      display: flex;
      span {
        display: inline-block;
        font-size: 14px;
        line-height: 14px;
      }
      .info-label {
        text-align: right;
        min-width: 140px;
        display: inline-block;
        font-weight: 400;
        color: #666666;
      }
      .info-value {
        display: inline-block;
        font-weight: 500;
        color: #333333;
        flex: 1;
        word-wrap: break-word;
        word-break: break-all;
        img {
          width: 100px;
          height: 100px;
          cursor: pointer;
        }
      }
    }
  }
}
</style>
