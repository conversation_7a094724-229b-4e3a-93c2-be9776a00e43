<template>
  <PageContainer :footer="true" v-loading="blockLoading">
    <div slot="content" class="table-content">
      <div class="content_box">
        <h1>基础信息</h1>
        <el-form ref="formInline" :model="formInline" class="form-inline" label-width="125px" :rules="rules">
          <el-form-item label="会议计划名称:" prop="name">
            <el-input v-model.trim="formInline.name" placeholder="请输入会议名称，最多输入30个字" maxlength="30" show-word-limit
              style="width: 360px;"></el-input>
          </el-form-item>
          <el-form-item label="组会部门:">
            <el-cascader ref="myCascader" v-model="formInline.organizeDept" placeholder="请选择部门" :options="deptList"
              :props="deptTree" :show-all-levels="false" clearable filterable collapse-tags style="width: 360px;">
            </el-cascader>
          </el-form-item>
          <br />
          <el-form-item label="会议频率:" prop="frequency">
            <el-select v-model="formInline.frequency" placeholder="请选择会议频率方式" style="width: 360px"
              @change="changeCycleType">
              <el-option v-for="item in frequencyList" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="会议方式:" prop="type">
            <el-select v-model="formInline.type" placeholder="请选择会议方式" style="width: 360px;">
              <el-option v-for="item in trainList" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <br />
          <el-form-item label="会议开始日期:" prop="templateName" v-if="formInline.frequency == 0">
            <el-date-picker v-model="formInline.startDay" type="date" value-format="yyyy-MM-dd" placeholder="选择日期" :picker-options="pickerOptions" style="width: 360px;">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="培训开始日期:" prop="templateName" v-if="formInline.frequency == 1">
            <el-date-picker v-model="formInline.startDay" type="date" value-format="yyyy-MM-dd" placeholder="选择日期" :picker-options="pickerOptions" style="width: 360px;">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="培训开始日期:" prop="templateName" v-if="formInline.frequency == 2">
            <el-select v-model="formInline.startDay" placeholder="请选择开始周" style="width: 360px;">
              <el-option v-for="item in startDateArr" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="培训开始日期:" prop="templateName" v-if="formInline.frequency == 3">
            <el-select v-model="formInline.startMonth" placeholder="请选择月" style="width: 360px;">
              <el-option v-for="item in startDateArr" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="培训开始日期:" prop="templateName" v-if="formInline.frequency == 4">
            <el-select v-model="formInline.startMonth" placeholder="请选择季度" style="width: 360px;">
              <el-option v-for="item in startMonthArr" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="培训开始日期:" prop="templateName" v-if="formInline.frequency == 5">
            <el-date-picker v-model="formInline.startDay" type="date" value-format="yyyy-MM-dd" placeholder="选择日期" style="width: 360px;">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="会议时间:" prop="courseTimeLine">
            <el-time-picker is-range v-model="formInline.courseTimeLine" range-separator="至" start-placeholder="开始时间"
              end-placeholder="结束时间" placeholder="选择时间范围" value-format="HH:mm:ss" style="width: 360px;"
              @change="dataPinkTime">
            </el-time-picker>
          </el-form-item>
          <br />
          <el-form-item label="会议地址:" prop="templateName">
            <el-input v-model.trim="formInline.address" placeholder="请输入会议地址" maxlength="200" show-word-limit
              style="width: 360px;"></el-input>
          </el-form-item>
          <el-form-item label="会议负责人:" prop="principal">
            <el-input v-model.trim="formInline.principal" show-word-limit style="width: 360px;"></el-input>
          </el-form-item>
          <br />
          <el-form-item label="通知培训消息" prop="duration">
            <el-input type="number" placeholder="请输入" v-model="formInline.notice" class="input-with-select" style="width: 360px;">
                <el-select v-model="select" slot="prepend" style="width: 90px; position: relative;left: 0;">
                  <el-option label="分钟" value="1"></el-option>
                  <el-option label="小时" value="2"></el-option>
                  <el-option label="天" value="3"></el-option>
                </el-select>
              </el-input>
          </el-form-item>
          <el-form-item label="参会部门及人员" prop="stuOrOrg">
            <el-select v-model="formInline.stuOrOrg" placeholder="请选择分配方式" style="width: 360px">
              <el-option v-for="item in stuOrOrgList" :key="item.id" :label="item.label" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <br />
          <el-form-item label="计划开始日期:" prop="timeLine" v-if="formInline.frequency != 1 && formInline.frequency != 0" >
            <el-date-picker v-model="formInline.timeLine" type="daterange" range-separator="至" start-placeholder="开始日期" value-format="yyyy-MM-dd" :picker-options="pickerOptions"
                end-placeholder="结束日期"  @change="dataPink" style="width: 360px;">
            </el-date-picker>
          </el-form-item>
          
          <el-form-item label="考试学员" class="" v-if="formInline.stuOrOrg == '0'">
            <span style="color: red; margin-left: -80px">*</span>
            <div class="set_select_width" @click="showUserDialog('xueyuan')">
              <template v-if="userNewList.length">
                <span v-for="(item, index) in userNewList" :key="index">
                  {{ item }}
                  <i class="el-icon-error" @click.stop="deleteTag(index, 'user')"></i>
                </span>
              </template>
              <p v-else>请选择人员</p>
            </div>
          </el-form-item>
          <el-form-item label="组织" prop="orgId" v-if="formInline.stuOrOrg == '1'">
            <el-select placeholder="请选择组织" style="width: 360px" multiple v-model="formInline.orgId">
              <el-option v-for="item in deptAllList" :key="item.id" :label="item.teamName" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <br />
          <el-form-item label="会议记录上传">
            <el-upload ref="uploadFile" drag multiple class="mterial_file" action="string" :file-list="fileEcho"
              :http-request="httpRequest"
              accept=".jpg,.png,.pdf,.JPG,.PBG,.GIF,.BMP,.PDF,.mp4,.avi,.wimv,.mpeg,.pdf,.doc,.docx,pptx" :limit="30"
              :on-exceed="handleExceed" :before-upload="beforeAvatarUpload" :on-remove="handleRemove"
              :on-change="fileChange">
              <i class="el-icon-upload"></i>
              <div class="el-upload__text" style="top: 100px">
                将文件拖到此处，或
                <em>点击上传</em>
              </div>
            </el-upload>
          </el-form-item>
          <br />
        </el-form>
      </div>
      <tasksDialog ref="userDialogRef" :opty="opty" :peopleDialog="peopleDialog" @closeDialog="closeDialog"
        @sureDialogUser="sureDialogUser"></tasksDialog>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go('-1')">取消</el-button>
      <el-button type="primary" @click="complete('0')">保存草稿</el-button>
      <el-button type="primary" @click="complete('2')">发布会议</el-button>
    </div>
  </PageContainer>
</template>
<script>
import axios from 'axios'
import moment from "moment";
import tasksDialog from '../trainingTasks/components/tasksDialog.vue';
export default {
  name: "addLocationPoint",
  components: { tasksDialog },
  data() {
    return {
      select: '1',
      moment,
      opty: '', // 学员弹窗状态
      subjectData: [], // 学科科目分类
      subjectDataType: {
        children: 'childList',
        label: 'name',
        value: 'id',
        checkStrictly: true,
        emitPath: true,
        disabled: 'disabledStatus'
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7;
        }
      },
      deptList: [], // 部门列表
      deptTree: {
        children: "children",
        label: "teamName",
        value: "id",
        multiple: true,
        checkStrictly: true
      },
      type: '', // 点击状态
      peopleDialog: false, // 学员弹窗
      stuOrOrgList: [
        {
          id: '0',
          label: '学员'
        },
        {
          id: '1',
          label: '组织'
        }
      ],
      frequencyList: [  // 计划频率
      {
          value: '0',
          label: '全年'
        }, {
          value: '1',
          label: '单次'
        }, {
          value: '2',
          label: '每周'
        }, {
          value: '3',
          label: '每月'
        }, {
          value: '4',
          label: '季度'
        },
        {
          value: '5',
          label: '每半年'
        }
      ],
      // 开始日期数组
      startDateArr: () => {
        if (this.formInline.frequency == 2) {
          const dateName = [
            "周一",
            "周二",
            "周三",
            "周四",
            "周五",
            "周六",
            "周日",
          ];
          const dateArr = [];
          for (let i = 0; i < 7; i++) {
            const item = {
              id: i + 1,
              name: "每" + dateName[i],
            };
            dateArr.push(item);
          }
          return dateArr;
        } else if (this.formInline.frequency == "3") {
          const dateArr = [];
          for (let i = 0; i < 30; i++) {
            const item = {
              id: i + 1,
              name: "每月" + (i + 1) + "日",
            };
            dateArr.push(item);
          }
          return dateArr;
        } else if (val == "3") {
          const dateArr = [];
          for (let i = 0; i < 30; i++) {
            const item = {
              id: i + 1,
              name: "每月" + (i + 1) + "日",
            };
            dateArr.push(item);
          }
          return dateArr;
        }
      },
      // 开始月份
      startMonthArr: [
      {
          id: 1,
          name: "第一季度",
        },
        {
          id: 2,
          name: "第二季度",
        },
        {
          id: 3,
          name: "第三季度",
        },
        {
          id: 4,
          name: "第四季度",
        },
      ],
      userNewList: [],
      trainTeacher: [], // 选择培训老师
      trainTeacherName: '',
      trainUser: [], // 到会人员
      trainUserName: '',
      deptAllList: [],
      value1: [new Date(2000, 10, 10, 10, 10), new Date(2000, 10, 11, 10, 10)],
      trainList: [{
        value: '0',
        label: '线上会议'
      }, {
        value: '1',
        label: '线下会议'
      }],
      value1: '',
      value: '',
      readonly: false,
      formInline: {
        name: "",
        address: '', // 地址
        teacherIds: '', // 
        studentIds: '',
        organizeDept: '',
        disc: '',
        startDay: '', // 计划开始日
        startMonth: '', // 计划开始月
        courseTimeLine: "", // 开始时间
        timeLine: [], // 计划开始日期
        stuOrOrg: '',
        orgId: '',
        startTime: '', //计划生效开始日期
        endTime: '', //计划生效结束日期
        material: '', // 上传计划资料id
        frequency: '0', // 频率
        conferenceStartTime: '', // 计划生效开始时间
        conferenceEndTime: '', // 计划结束时间
        principal: '', // 负责人名称
      },
      id: "",
      blockLoading: false,
      fileEcho: [],
      indextype: '',
      rules: {
        name: [
          { required: true, message: "请输入会议计划名称", trigger: "change" },
        ],
        frequency: [
          { required: true, message: "请选择会议频率", trigger: "change" },
        ],
        type: [
          { required: true, message: "请选择会议方式", trigger: "change" },
        ],
        timeLine: [
          { required: true, message: "请选择会议开始日期", trigger: "change" },
        ],
        stuOrOrg: [
          { required: true, message: "请选择参会部门及人员", trigger: "change" },
        ],
        courseTimeLine: [
          { required: true, message: "请选择会议时间", trigger: "change" },
        ],
      },
      fileQuestions: [],
      routeInfo: '',
      userAllList: [],
    };
  },
  created() {
    this.getTableList()
    this.routeInfo = JSON.parse(sessionStorage.getItem("routeInfo"));
    this.formInline.principal = this.routeInfo.createName
    this.getDeptList() // 获取所属单位
    this.type = this.$route.query.type

    this.id = this.$route.query.id
    if (this.id) {
      this.getConferenDetail()
    }
    // this.getTrainDetail()
  },

  methods: {
    changeCycleType(val) {
      this.formInline.frequency = val
      this.formInline.startDay = "";
      if (val == "2") {
        const dateName = [
          "周一",
          "周二",
          "周三",
          "周四",
          "周五",
          "周六",
          "周日",
        ];
        this.startDateArr = [];
        for (let i = 0; i < 7; i++) {
          const item = {
            id: i + 1,
            name: "每" + dateName[i],
          };
          this.startDateArr.push(item);
        }
      } else if (val == "2") {
        this.startDateArr = [];
        for (let i = 0; i < 30; i++) {
          const item = {
            id: i + 1,
            name: "每月" + (i + 1) + "日",
          };
          this.startDateArr.push(item);
        }
      } else if (val == "3") {
        this.startDateArr = [];
        for (let i = 0; i < 30; i++) {
          const item = {
            id: i + 1,
            name: "每月" + (i + 1) + "日",
          };
          this.startDateArr.push(item);
        }
      }
    },
    // 获取人员列表
    getTableList() {
      let data = {
        currentPage: 1,
        pageSize: 9999,
        controlTeamId: ''
      }
      this.$api.getControlTeamUserListLaboratory(data).then((res) => {
        if (res.code == 200) {
          this.userAllList = res.data.list
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 获取所属单位
    getDeptList() {
      this.$api.getDeptListLaboratory({}).then((res) => {
        this.allDept = res.data.list;
        this.deptList = this.$tools.transData(
          res.data.list,
          "id",
          "parentId",
          "children"
        );
        this.deptAllList = res.data.list
      });
    },
    // 学员选择弹窗-------------------------------
    showUserDialog(opty) {
      this.opty = opty
      this.peopleDialog = true;
      this.$nextTick(() => {
        // this.$refs.userDialogRef.userSelectData = this.userList;
        this.$refs.userDialogRef.getTableList(true);
      });
    },
    //关闭弹窗
    closeDialog() {
      this.peopleDialog = false
    },
    sureDialogUser() {
      this.peopleDialog = false;
      if (this.opty == 'xueyuan') {
        this.userList = this.$refs.userDialogRef.userSelectData;
        const userNewList = this.userList.map(item => item.name)
        this.userNewList = userNewList
      }
      if (this.opty == 'teacher') {
        this.userList = this.$refs.userDialogRef.userSelectData;
        this.formInline.teacherIds = this.userList.map(item => item.id)
        this.formInline.teacherIds = this.formInline.teacherIds.join(',')
        this.trainTeacher = this.userList.map(item => item.name)
        this.trainTeacherName = this.trainTeacher.join(',')
      }
      if (this.opty == 'user') {
        this.userList = this.$refs.userDialogRef.userSelectData;
        this.formInline.studentIds = this.userList.map(item => item.id)
        this.formInline.studentIds = this.formInline.studentIds.join(',')
        this.trainUser = this.userList.map(item => item.name)
        this.trainUserName = this.trainUser.join(',')
      }
    },
    deleteTag(index) {
      this.userNewList.splice(index, 1);
      this.userList.splice(index, 1);
    },
    onCancel() {
      this.$router.go(-1)
    },
    // 选择时间
    dataPinkTime(data) {
      this.formInline.conferenceStartTime = data[0]
      this.formInline.conferenceEndTime = data[1]
    },
    // 计划开始日期
    dataPink(data) {
      this.formInline.startTime = data[0]
      this.formInline.endTime = data[1]
    },
    addPersonShow(opty) {
      this.opty = opty
      this.peopleDialog = true;
      this.$nextTick(() => {
        // this.$refs.userDialogRef.userSelectData = this.userList;
        this.$refs.userDialogRef.getTableList(true);
      });
    },
    // 获取会议计划详情
    getConferenDetail() {
      this.$api.confereceDetail({ id: this.id }).then(res => {
        if (res.code == 200) {
          this.formInline = { ...res.data }
          this.formInline.organizeDept = res.data.organizeDept ? res.data.organizeDept.split(',') : [];
          this.formInline.timeLine = [res.data.startTime, res.data.endTime];
          this.formInline.courseTimeLine = [res.data.conferenceStartTime, res.data.conferenceEndTime];
          this.formInline.orgId = res.data.orgId.split(',')
          // 考试人员回显
          let studentIdList = res.data.studentIds.split(',')
          this.userList = []
          studentIdList.forEach(i => {
            this.userAllList.forEach(k => {
              if (i == k.id) {
                this.userList.push(k)
              }
            })
          })
          const userNewList = this.userList.map(item => item.name)
          this.userNewList = userNewList
        }
      })
    },
    // 点击确定
    complete(typesy) {
      this.$refs.formInline.validate((valid) => {
        if (valid) {
          let data = {
            ...this.formInline,
            id: this.formInline.id,
            studentIds: this.formInline.stuOrOrg == '0' ? this.userList.map(i => i.id).join(',') : '',
            organizeDept: this.formInline.organizeDept.join(','),
            principal: this.routeInfo.userId,
            conferenceStatus: 0,
          }
          if (typesy == 0) {
            data.conferenceStatus = 0
          } else if (typesy == 2) {
            data.conferenceStatus = 2
          }
          if(this.formInline.orgId) {
            data.orgId = this.formInline.orgId.join(',')
          }
          if (this.type == 'addCon') {
            delete data.id
            this.$api.confereceSave(data).then(res => {
              if (res.code == 200) {
                this.$message.success(res.msg)
                this.$router.go(-1)
              } else {
                this.$message.error(res.msg)
              }
            })
          } else if (this.type == 'edit') {
            this.$api.confereceEdit(data).then(res => {
              if (res.code == 200) {
                this.$message.success(res.msg)
                this.$router.go(-1)
              } else {
                this.$message.error(res.msg)
              }
            })
          }

        }
      })
    },
    async fileChange(file, fileList) {
      const isLt40M = file.size / 1024 / 1024 < 40
      if (!isLt40M) {
        this.$message.error('上传图片大小不能超过 40MB!')
        fileList.splice(-1, 1); //移除选中图片
        return false
      }
      this.fileEcho = fileList
    },
    httpRequest() {
      this.formData = new FormData()
      this.fileEcho.forEach((item) => {
        this.formData.append('file', item.raw)
      })
      this.formData.append('hospitalCode', this.routeInfo.hospitalCode)
      axios({
        method: 'post',
        url: __PATH.BASE_URL + "minio/upload",
        data: this.formData,
        headers: {
          "Content-Type": "application/json",
          token: this.routeInfo.token,
        },
      }).then(res => {
        this.formInline.material = res.data.data.fileRecordIds
        this.submit()
      }).catch(() => {
        this.$message.error(res.data.message)
      })
    },
    handleExceed() {
      this.$message.error('最多上传三张图片')
    },
    beforeAvatarUpload(file) {
      const isLt40M = file.size / 1024 / 1024 < 40
      if (!isLt40M) {
        this.$message.error('上传图片大小不能超过 40MB!')
        return false
      }
      if (file.name.indexOf(',') != -1) {
        this.$message.error('非法的文件名')
        return false
      }
    },
    handleRemove(file, fileList) {
      this.fileEcho = fileList
    },
  },
};
</script>
<style lang="scss" scoped>
.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
}

.content_box {
  height: 100%;
  padding: 30px 25px 20px 25px;
  background: #fff;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

:deep(.el-input.is-disabled .el-input__inner) {
  background: #f5f7fa !important;
}

.detailClass :deep(.el-input__inner) {
  border: none !important;
}

.detailClass :deep(.el-textarea__inner) {
  border: none;
  resize: none;
}

.project-textarea textarea {
  height: 120px;
}

.form-inline {
  margin-top: 20px;
}

/deep/ .el-form-item {
  display: inline-block;
}

/deep/ .el-date-editor .el-range__icon {
  margin-top: -5px;
}

/deep/ .el-date-editor .el-range__close-icon {
  margin-top: -5px;
}

.set_select_width {
  width: 300px;
  min-height: 80px;
  margin-top: -30px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  display: flex;
  flex-wrap: wrap;

  span {
    height: 20px;
    line-height: 20px;
    background-color: #f4f4f5;
    border-color: #e9e9eb;
    color: #909399;
    padding: 3px 8px;
    font-size: 12px;
    border-radius: 4px;
    margin: 2px 0 2px 6px;
    cursor: pointer;
  }

  p {
    padding-left: 15px;
    color: rgb(191, 196, 204);
    font-size: inherit;
  }
}

/deep/ .el-input__inner {
  height: 32px !important;
  line-height: 32px !important;
}
</style>
  