<template>
  <div style="height: 100%;">
    <div class="special_box">
      <div class="left">
        <div class="leftCon">
          <div class="toptip">
            <span class="green_line"></span>
            计划类型
          </div>
          <template>
            <div class="tabsBox">
              <el-tree
                ref="tree"
                :data="dictionaryArr"
                :props="treeProps"
                node-key="id"
                :highlight-current="true"
                :check-strictly="true"
                @node-click="handleClick"
              >
              </el-tree>
            </div>
          </template>
        </div>
      </div>
      <div class="content_box">
        <div class="top_content">
          <div style="margin-bottom: 20px;">
            <el-input
              v-model="searchDataObj.planName"
              style="width: 217px; margin-right: 20px;"
              placeholder="计划名称"
              maxlength="25"
              onkeyup="if(value.length>25)value=value.slice(0,25)"
              @keyup.enter.native="_searchByCondition"
            ></el-input>
            <el-select v-model="searchDataObj.useState" filterable placeholder="请选择状态" @keyup.enter.native="_searchByCondition">
              <el-option v-for="(item, index) in statusList" :key="index" :label="item.label" :value="item.id"> </el-option>
            </el-select>
            <el-button type="primary" plain style="margin-left: 30px;" @click="_resetCondition">重置</el-button>
            <el-button type="primary" style="font-size: 14px;" @click="_searchByCondition">查询</el-button>
          </div>
          <div>
            <el-button type="primary" icon="el-icon-plus" style="font-size: 14px;" @click="addFn('add')">新增</el-button>
            <!-- <el-button
              class="sino-button-sure-search"
              icon="el-icon-edit"
              @click="updateFn('edit')"
              :disabled="
                tableClickArry.length != 1 ||
                tableClickArry[0].approvalStateDict !== '暂存'
              "
              >编辑</el-button
            > -->
            <!-- <el-button
              class="sino-button-sure-search"
              icon="el-icon-delete"
              @click="deleteFn"
              :disabled="
                tableClickArry.length == 0 ||
                tableClickArry[0].approvalStateDict !== '暂存'
              "
              >删除</el-button
            > -->
            <el-button
              type="primary"
              :disabled="tableClickArry.length == 0"
              style="width: 120px; font-size: 14px;"
              :loading="isdownload"
              @click="downLoadQrCode"
            >巡检码下载</el-button
            >
          </div>
        </div>
        <div class="table_list" style="text-align: right; height: 100%;">
          <el-table
            ref="materialTable"
            v-loading="tableLoading"
            :data="tableData"
            title="双击列表查看详情"
            height="calc(100% - 10px)"
            border
            style="width: 100%;"
            :cell-style="{ padding: '8px' }"
            stripe
            :header-cell-style="{ background: '#f2f4fbd1' }"
            highlight-current-row
            :empty-text="emptyText"
            :row-key="getRowKeys"
            @cell-dblclick="viewDetails"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" align="center"></el-table-column>
            <el-table-column label="序号" type="index" width="80" align="center">
              <template slot-scope="scope">
                <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column prop show-overflow-tooltip label="启用/禁用" width="120" align="center">
              <template slot-scope="scope">
                <el-switch
                  v-model="scope.row.showState"
                  active-color="#5188FC"
                  inactive-color="#d8dee6"
                  @change="updateMaintainPlanUseStateFn(scope.row.id, scope.row.useState)"
                ></el-switch>
              </template>
            </el-table-column>
            <el-table-column prop="planName" show-overflow-tooltip label="计划名称" align="center"></el-table-column>
            <el-table-column prop="planTypeName" show-overflow-tooltip label="计划类型" align="center"></el-table-column>
            <el-table-column prop show-overflow-tooltip label="周期类型" align="center">
              <template slot-scope="scope">
                <span>{{ cycleTypeFn(scope.row) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="cycleRole" show-overflow-tooltip label="执行频次" align="center"></el-table-column>
            <el-table-column prop show-overflow-tooltip label="执行方式" align="center">
              <template slot-scope="scope">
                <span>{{ executeMode(scope.row.executeMode) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop show-overflow-tooltip label="小组/人员" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.planPersonName || scope.row.distributionTeamName }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="useState" width="90" show-overflow-tooltip label="状态" align="center">
              <template slot-scope="scope">
                <span
                  class="table_forbidden"
                  :class="{
                    table_using: scope.row.useState == 0,
                    scrap_color: scope.row.useState == 1
                  }"
                  :style="{ color: checkState('color', scope.row) }"
                >
                  <!-- {{ scope.row.useState == 0 ? "启用" : "停用" }} -->
                  {{ checkState('text', scope.row) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
              <template slot-scope="scope">
                <!-- <el-button
                  v-if="scope.row.useState==1"
                  size="mini"
                  type="danger"
                  disabled
                  >停用</el-button>
                <el-button
                  v-else
                  size="mini"
                  type="danger"
                  @click="deleteFn(scope.$index, scope.row)"
                  :disabled="
                  scope.row.approvalStateDict !== '暂存'
                  "
                  >删除</el-button>
                <el-button
                  size="mini"
                  type="primary"
                  @click="updateFn('edit',scope.row)"
                  :disabled="
                    scope.row.approvalStateDict !== '暂存'
                  "
                  >编辑</el-button> -->
                <el-link
                  :underline="false"
                  style="margin-right: 10px;"
                  :type="scope.row.free2 === '1' ? 'danger' : 'info'"
                  :disabled="scope.row.free2 !== '1'"
                  @click="deleteFn(scope.$index, scope.row)"
                >删除</el-link
                >
                <el-link :underline="false" :type="scope.row.free2 === '1' ? 'primary' : 'info'" :disabled="scope.row.free2 !== '1'" @click="updateFn('edit', scope.row)"
                >编辑</el-link
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="" style="padding-top: 10px; text-align: right;">
          <el-pagination
            :current-page="paginationData.currentPage"
            :page-sizes="[15, 30, 50, 100]"
            :page-size="paginationData.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="paginationData.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
    </div>
  </div>
    
</template>

<script>
export default {
  name: 'feedbackRecord',
  components: {},
  data() {
    return {
      isdownload: false,
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      LOGINDATA: '',
      treeProps: {
        children: 'children',
        label: 'dictName',
        isLeaf: 'leaf'
      },
      statusList: [
        {
          id: 0,
          label: '启用'
        },
        {
          id: 1,
          label: '禁用'
        }
      ], // 状态
      searchDataObj: {
        planName: '',
        useState: ''
      },
      // 执行方式
      executeMode: (model) => (model == '1' ? '按小组' : '按人员'),
      pickerOptions: {
        shortcuts: [this.$tools.getThisWeek, this.$tools.getThisMonth]
      },
      cycleTypeList: [
        {
          cycleType: 8,
          label: '单次'
        },
        {
          cycleType: 6,
          label: '每日'
        },
        {
          cycleType: 0,
          label: '每周'
        },
        {
          cycleType: 2,
          label: '每月'
        },
        {
          cycleType: 3,
          label: '季度'
        },
        {
          cycleType: 5,
          label: '全年'
        }
      ],
      tableData: [],
      tableList: [],
      emptyText: '暂无数据',
      tableLoading: false,
      pageSize: '',
      currentPage: '',
      listRole: '',
      userIds: '',
      activeType: '',
      tableClickArry: [],
      dictionaryArr: [],
      useState: '',
      downLoadId: '',
      staging: null
    }
  },
  created() {
    // 获取登录信息
    this.LOGINDATA = JSON.parse(sessionStorage.getItem('LOGINDATA'))
    // 获取计划类型
    this._findDictionaryTableList()
  },
  mounted() {},
  methods: {
    // 获取字典列表 findPageLists
    _findDictionaryTableList() {
      let data = {
        dictCode: '',
        dictName: '',
        pageNo: 1,
        pageSize: 99999,
        dictType: 'plan_type'
      }
      this.$api.findDictionaryTableList(data).then((res) => {
        this.tableLoading = false
        const { code, data, message } = res
        if (code == 200) {
          this.dictionaryArr = data.list
          this.activeType = this.dictionaryArr[0].id
          this.dictionaryArr[0].avtiveItem = true
          this.$nextTick(() => {
            // 获取计划列表
            this._findPageLists(this.activeType)
            this.$refs.tree.setCurrentKey(this.activeType)
          })
        } else {
          this.$message.error(message)
        }
      })
    },
    // 新增
    addFn(type) {
      console.log(type)
      this.$router.push({
        path: 'planManagement/addPlans',
        query: {
          type: type
        }
      })
    },
    // 编辑
    updateFn(type, row) {
      console.log(type, row)
      this.$router.push({
        path: 'planManagement/addPlans',
        query: {
          type: type,
          id: row.id
        }
      })
    },
    /**
     * 查看、修改详情
     */
    viewDetails(row) {
      this.$router.push({
        path: 'planManagement/planDetail',
        query: {
          id: row.id,
          type: 'view'
        }
      })
    },
    // 删除计划
    deleteFn(index, row) {
      let id =
        this.tableClickArry &&
        this.tableClickArry.length &&
        this.tableClickArry.map((item, index) => {
          return item.id
        })
      this.$confirm('此操作将永久删除该计划, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api
          .deleteMaintainPlan({
            ids: row.id,
            type: 1
          })
          .then((res) => {
            if (res.code == 200) {
              this.$refs.materialTable.clearSelection() // 清空表格选中状态
              let val = res.data.success
              let val2 = res.data.fail
              let msg = ''

              if (res.data.statusfail || res.data.nopowfail) {
                msg += '<div style="color:#333">选择项中有计划不能删除！</div>'
              }
              if (res.data.success) {
                msg +=
                  '<div class="dialog_box"><div class="dele_success" style="margin-top:15px;max-width:500px;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;"><span class="iconfont" style="margin-right:5px">&#xe649;</span>' +
                  res.data.success +
                  '</div>'
              }
              if (res.data.statusfail) {
                msg +=
                  '<div class="dele_err" style="color:red;margin-top:15px;max-width:500px;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;"><span class="iconfont" style="margin-right:5px">&#xe648;</span>' +
                  res.data.statusfail +
                  '</div></div>'
              }
              if (res.data.nopowfail) {
                msg +=
                  '<div class="dele_err" style="color:red;margin-top:15px;max-width:500px;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;"><span class="iconfont" style="margin-right:5px">&#xe648;</span>' +
                  res.data.nopowfail +
                  '</div></div>'
              }
              this.$message({
                dangerouslyUseHTMLString: true,
                type: 'success',
                customClass: 'my_dialog',
                message: msg,
                duration: 1500,
                showClose: true
              })
              this.paginationData.currentPage = this.paginationData.currentPage == 1 ? 1 : this.paginationData.currentPage - 1
              this._findPageLists(this.activeType)
            }
          })
      })
    },
    // 巡检码下载
    downLoadQrCode() {
      this.downLoadId =
        this.tableClickArry &&
        this.tableClickArry.length &&
        this.tableClickArry.map((item, index) => {
          return item.id
        })
      this.isdownload = true
      fetch(
        `${__PATH.VUE_AQ_URL}/taskPoint/exportQrCode?planIds=${
          this.downLoadId.join(',') || ''
        }&unitCode=${this.LOGINDATA.unitCode || ''}&hospitalCode=${this.LOGINDATA.hospitalCode || ''}`,
        {
          method: 'POST',
          responseType: 'blob',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            Authorization: 'Bearer ' + this.$store.state.user.token
          }
        }
      )
        .then((res) => {
          res.blob().then((blob) => {
            this.saveBlobAs(blob)
            this.isdownload = false
          })
        })
        .catch((err) => {
          console.log(err)
          this.isdownload = false
        })
    },
    // 保存blob的方法
    saveBlobAs(blob) {
      let _self = this
      if (window.navigator.msSaveOrOpenBlob) {
        navigator.msSaveBlob(blob)
      } else {
        const anchor = document.createElement('a')
        const body = document.querySelector('body')
        anchor.href = window.URL.createObjectURL(blob)
        anchor.download = '巡检码.zip'
        anchor.style.display = 'none'
        body.appendChild(anchor)
        anchor.click()
        body.removeChild(anchor)
        window.URL.revokeObjectURL(anchor.href)
        this.exportLoading = false
      }
    },
    /**
     * 修改计划启用禁用状态
     */
    updateMaintainPlanUseStateFn(id, useState) {
      this.amendPlanStateFn(id, useState)
    },
    /**
     * 修改计划状态
     */
    amendPlanStateFn(id, useState) {
      this.tableLoading = true
      this.$api
        .updateMaintainPlanUseState({
          id: id,
          useState: useState == 0 ? 1 : 0, // 0:启用，1：禁用
          companyCode: this.LOGINDATA.companyCode ? this.LOGINDATA.companyCode : this.LOGINDATA.hospitalCode,
          companyName: this.LOGINDATA.companyName ? this.LOGINDATA.companyName : this.LOGINDATA.hospitalName,
          unitCode: this.LOGINDATA.unitCode,
          // hospitalCode: this.LOGINDATA.hospitalCode,
          userId: this.LOGINDATA.id,
          userName: this.LOGINDATA.name
          // staffId: '8984ffe1bb704fffba1fa8eb8d4e6cfb'
        })
        .then((res) => {
          this.tableLoading = false
          if (res.code == 200) {
            this.$message({
              type: 'success',
              message: res.message
            })
          } else {
            this.$message({
              type: 'error',
              message: res.message
            })
          }
          this._findPageLists(this.activeType)
        })
    },
    /**
     * 点击table表格
     */
    handleSelectionChange(val) {
      this.tableClickArry = val
    },
    // 点击切换计划类型
    handleClick(val, node, event) {
      this.activeType = val.id
      if (this.dictionaryArr.length) {
        this.dictionaryArr.forEach((item, index) => {
          if (item.id == val.id) {
            item.avtiveItem = true
          } else {
            item.avtiveItem = false
          }
        })
      }
      this._findPageLists(this.activeType)
    },
    // 查询表格
    _findPageLists(types) {
      this.tableLoading = true
      let obj = this.LOGINDATA
      const { paginationData, searchDataObj } = this
      let data = {
        pageNo: paginationData.currentPage,
        pageSize: paginationData.pageSize,
        planName: searchDataObj.planName,
        useState: searchDataObj.useState,
        planTypeId: types
      }
      this.$api.findPageLists(data).then((res) => {
        this.tableLoading = false
        const { data, message, code } = res
        if (code == 200) {
          this.tableData = data.list.map(i => {
            if (i.useState == 0) {
              i.showState = true
            } else {
              i.showState = false
            }
            return i
          })
          this.paginationData.total = data.count
        }
      })
    },
    // 条件查询
    _searchByCondition() {
      console.log(this.paginationData)
      this.paginationData.currentPage = 1
      this._findPageLists(this.activeType)
    },
    // 重置查询条件
    _resetCondition() {
      this.paginationData = {
        currentPage: 1,
        pageSize: 15,
        total: 0
      }
      this.searchDataObj = {
        planName: '',
        useState: ''
      }
      this._findPageLists(this.activeType)
    },

    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this._findPageLists(this.activeType)
    },
    // 每页的条数
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.paginationData.currentPage = 1
      this._findPageLists(this.activeType)
    },
    getRowKeys(row) {
      return row.staffId
    },
    // 校验启用、禁用、暂存
    checkState(type, state) {
      if (type == 'text') {
        if (state.free2 == '1') {
          return '暂存'
        } else if (state.useState == 0) {
          return '启用'
        } else if (state.useState == 1) {
          return '禁用'
        }
      } else {
        if (state.free2 == '1') {
          return '#d1d1d1'
        } else if (state.useState == 0) {
          return '#5188FC'
        } else if (state.useState == 1) {
          return '#ff3a3a'
        }
      }
    },
    // 周期类型
    cycleTypeFn(row) {
      const item = this.cycleTypeList.filter((i) => i.cycleType == row.cycleType)
      return item[0].label
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: linear-gradient(to right, #d9e1f8, #fff);
}

::v-deep .el-tree-node__content {
  height: 32px;
}

.tabsItem {
  padding: 8px 0;
  width: 200px;
  margin: 0 auto;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #5b5b5b;
  cursor: pointer;

  .item {
    width: 200px !important;
  }

  >span {
    width: 200px !important;
    margin: 0 auto !important;
  }
}

.tabsItem:hover {
  color: #5188fc;
}

:deep(.el-tabs--left) {
  width: 200px;
  margin: 20px auto 0;
}

:deep(.el-table__row) {
  :last-child > .cell {
    display: flex;
    justify-content: space-between;
  }
}

.special_box {
  height: 100%;
  display: flex;

  .left {
    width: 246px;
    min-width: 14%;
    height: calc(100% - 30px);
    border-radius: 10px;
    background-color: #fff;
    margin: 15px;

    .leftCon {
      padding: 10px;
      height: 100%;

      .toptip {
        box-sizing: border-box;
        padding-left: 26px;
        height: 50px;
        width: 100%;
        line-height: 50px;
        text-align: left;
        border-bottom: none;
        font-weight: 600;
      }

      :deep(.el-tabs) {
        height: calc(100% - 50px);

        .el-tabs__content {
          height: calc(100% - 40px);
          overflow: scroll;
        }
      }

      .tabsBox {
        text-align: center;
        margin-top: 20px;
        margin-bottom: 30px;
      }
    }
  }

  .content_box {
    width: calc(100% - 276px);
    margin: 15px 0;
    border-radius: 10px;
    padding: 20px 25px 25px;
    background: #fff;
    display: flex;
    flex-direction: column;

    .top_content {
      width: 100%;
      display: flex;
      justify-content: space-between;
      margin-bottom: 25px;
    }

    .table_list {
      .disable {
        color: #414653;

        span {
          display: inline-block;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: #ccced3;
        }
      }

      .enable {
        color: #08cb83;

        span {
          display: inline-block;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: #08cb83;
        }
      }
    }
  }
}

@media screen and (max-width: 1600px) {
  .pagination {
    position: absolute;
    bottom: 0;
    right: 15px;
  }

  .personDialog .el-dialog {
    height: 545px;
  }

  .left_content {
    height: 415px;
  }

  .left .middle_tools {
    line-height: 36px;
    box-shadow: 0 -4px 4px 0 #f3f4f8;
    box-shadow: 0 -4px 4px 0 #f3f4f8;
  }
}
</style>