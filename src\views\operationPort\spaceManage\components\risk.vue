<template>
  <div style="width: 100%; height: 100%;">
    <div class="control-btn-header">
      <div style="display: flex; padding: 10px 5px;">
        <el-input v-model="riskName" placeholder="请输入风险点名称" class="ipt"></el-input>
        <el-input v-model="responsiblePersonName" placeholder="请输入责任人" class="ipt"></el-input>
        <el-button type="primary" @click="inquiry">查询</el-button>
        <el-button type="primary" plain @click="reset">重置</el-button>
      </div>
    </div>
    <div style=" height: 100%;">
      <div class="content">
        <!-- <el-row :gutter="16">
          <el-col :xs="24" :md="24" :lg="12" style="padding: 10px 5px 10px 0;">
            <div class="col" style="height: 100%;">
              <div class="title">
                <svg-icon name="right-arrow" />
                <span>风险等级分析</span>
              </div>
              <div id="orderTypeCharts"></div>
            </div>
          </el-col>
          <el-col :xs="24" :md="24" :lg="12" style="padding: 10px 0 10px 5px;">
            <div class="col">
              <div class="title">
                <svg-icon name="right-arrow" />
                <span>风险类型分析</span>
              </div>
              <div id="powerMonthTypeNoEchart"></div>
            </div>
          </el-col>
        </el-row> -->
        <!-- <el-row>
          <el-col :xs="24" :md="24" :lg="12" style="width: 100%;"> -->

        <div class="col" style="height: 100%; width: 100%;">
          <div class="contentTable">
            <div class="contentTable-main table-content">
              <el-table :data="tableData" :height="tableHeight">
                <el-table-column type="index" label="序号" width="50" align="center">
                  <template slot-scope="scope">
                    <span>{{ (pagination.current - 1) * pagination.size + scope.$index + 1 }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="" label="风险点名称" min-width="100" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span class="color_blue" @click="ViewFn(scope.row)">
                      {{ scope.row.riskName }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="riskPlace" label="风险点位(区域)或系统" show-overflow-tooltip width="180"></el-table-column>
                <el-table-column prop="riskLevel" label="风险等级" min-width="80" show-overflow-tooltip width="120">
                  <template slot-scope="scope">
                    <div v-if="scope.row.riskLevel == 1" class="table-block-text" style="background: rgb(255 0 0);">重大风险</div>
                    <div v-if="scope.row.riskLevel == 2" class="table-block-text" style="background: rgb(255 97 0);">较大风险</div>
                    <div v-if="scope.row.riskLevel == 3" class="table-block-text" style="background: rgb(255 255 0); color: #606266;">一般风险</div>
                    <div v-if="scope.row.riskLevel == 4" class="table-block-text" style="background: rgb(0 0 255);">低风险</div>
                    <div v-if="scope.row.riskLevel == 5" class="table-block-text" style="color: #606266;">未研判</div>
                  </template>
                </el-table-column>
                <el-table-column prop="taskTeamName" show-overflow-tooltip label="责任部门" width="100"></el-table-column>
                <el-table-column prop="responsiblePersonName" show-overflow-tooltip label="责任人"></el-table-column>
                <el-table-column prop="urgentContactPhone" show-overflow-tooltip label="应急联系电话" min-width="130"></el-table-column>
                <el-table-column prop="attachmentUrl" show-overflow-tooltip label="图片" min-width="100">
                  <template slot-scope="scope">
                    <span style="color: #5188fc; cursor: pointer;" @click="showPicture(scope.row)">查看图片</span>
                  </template>
                </el-table-column>
                <el-table-column prop="processUrlList" show-overflow-tooltip label="预案流程图" min-width="100">
                  <template slot-scope="scope">
                    <span style="color: #5188fc; cursor: pointer;" @click="showPicture2(scope.row)">查看图片</span>
                  </template>
                </el-table-column>
                <el-table-column prop="registerPerson" show-overflow-tooltip label="登记人"></el-table-column>
                <el-table-column prop="registerTime" show-overflow-tooltip label="登记时间"></el-table-column>
              </el-table>
            </div>
          </div>
          <div class="contentTable-footer">
            <el-pagination
              style="margin-top: 3px;"
              :current-page="pagination.current"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              :page-size="pagination.size"
              :page-sizes="[15, 30, 50]"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            ></el-pagination>
          </div>
        </div>
        <!-- </el-col>
        </el-row> -->
      </div>
      <imgCarousel :dialogVisibleImg="dialogVisibleImg" :imgArr="imgArr" @closeDialog="closeDialogImg"></imgCarousel>
    </div>
  </div>
</template>

<script>
import tableListMixin from '@/mixins/tableListMixin.js'

import store from '@/store/index'
import * as echarts from 'echarts'
import imgCarousel from '../common/imgCarousel.vue'

export default {
  name: 'risk',
  components: {
    imgCarousel
  },
  mixins: [tableListMixin],
  props: {
    placeIds: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      imgArr: [],
      dialogVisibleImg: false,
      riskLevel: '',
      riskName: '',
      responsiblePersonName: '',
      orderStatisticsName: [],
      orderType: 'year',
      seriesData: [],
      tableData: [],
      pagination: {
        current: 1,
        size: 15
      },
      total: 0
    }
  },
  // computed: {
  //   tableHeight() {
  //     return document.body.clientHeight - 630
  //   }
  // },
  watch: {
    '$store.state.settings.sidebarCollapse': {
      handler(val) {
        this.$nextTick(() => {
          let echartsDom = ['orderTypeCharts', 'powerMonthTypeNoEchart']
          setTimeout(() => {
            echartsDom.forEach((item) => {
              echarts.init(document.getElementById(item)).resize()
            })
          }, 250)
        })
      },
      deep: true
    }
  },
  created() {
    // this.getRiskLevelStatistics()
    // this.getRiskDeptStatistics()
    this.getTableList()
  },
  methods: {
    inquiry() {
      this.getRiskLevelStatistics()
      this.getRiskDeptStatistics()
      this.getTableList()
    },
    reset() {
      this.riskLevel = ''
      this.responsiblePersonName = ''
      this.riskName = ''
      this.getRiskLevelStatistics()
      this.getRiskDeptStatistics()
      this.getTableList()
    },
    closeDialogImg() {
      this.dialogVisibleImg = false
      this.imgArr = []
    },
    ViewFn(row) {
      this.$router.push({
        path: '/addRisk',
        query: { row: row }
      })
    },
    showPicture(row) {
      if (row.attachmentUrlList.length > 0) {
        this.imgArr = row.attachmentUrlList

        this.dialogVisibleImg = true
      } else {
        this.$message.error('该记录无图片')
      }
    },
    showPicture2(row) {
      if (row.processUrlList.length > 0) {
        this.imgArr = row.processUrlList
        this.dialogVisibleImg = true
      } else {
        this.$message.error('该记录无预案流程图')
      }
    },
    getTableList() {
      let params = {
        pageNo: this.pagination.current,
        pageSize: this.pagination.size,
        placeIds: this.placeIds,
        riskName: this.riskName,
        responsiblePersonName: this.responsiblePersonName,
        riskLevel: this.riskLevel
      }
      this.$api.getRiskPageList(params).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.list
          this.total = res.data.sum
        }
      })
    },
    getRiskLevelStatistics() {
      let params = {
        placeIds: this.placeIds,
        riskName: this.riskName,
        responsiblePersonName: this.responsiblePersonName
      }
      this.$api.getRiskLevelStatistics(params).then((res) => {
        if (res.code == '200') {
          this.orderStatisticsName = res.data.array
          this.initOrderTypeCharts()
        }
      })
    },
    initOrderTypeCharts() {
      let arr = []
      this.orderStatisticsName.forEach((item) => {
        let obj = {
          name: item.name,
          value: item.value
        }
        arr.push(obj)
      })
      // this.orderStatisticsValue.forEach((item, i) => {
      //   arr.forEach((item2, n) => {
      //     if (i == n) {
      //       item2.value = item
      //     }
      //   })
      // })
      const nameList = Array.from(arr, (item) => item.name)
      const getchart = echarts.init(document.getElementById('orderTypeCharts'))
      const option = {
        tooltip: {
          trigger: 'item',
          axisPointer: {
            lineStyle: {
              color: '#FFE3A6'
            }
          },
          backgroundColor: '#fff',
          borderColor: '#fff',
          textStyle: {
            color: '#000'
          },
          formatter: function (name) {
            var oa = option.series[0].data
            for (var i = 0; i < option.series[0].data.length; i++) {
              if (name.name === oa[i].name) {
                return ' ' + name.name + '(' + oa[i].value + '件' + ')' + '' + name.percent + '%' + ''
              }
            }
          }
        },
        legend: {
          orient: 'vertical',
          type: 'scroll',
          right: 30,
          top: 60,
          bottom: 20,
          itemWidth: 15,
          itemHeight: 15,
          itemGap: 15,
          data: nameList,
          formatter: function (name) {
            var oa = option.series[0].data
            var num = oa.reduce((sum, e) => sum + e.value, 0)
            for (var i = 0; i < option.series[0].data.length; i++) {
              if (name === oa[i].name) {
                if (isNaN(oa[i].value / num)) {
                  return ' ' + name + '(' + oa[i].value + '件' + ')' + '    ' + 0 + '%'
                } else {
                  return ' ' + name + '(' + oa[i].value + '件' + ')' + '    ' + ((oa[i].value / num) * 100).toFixed(2) + '%'
                }
              }
            }
          }
        },
        series: [
          {
            type: 'pie',
            roseType: 'radius',
            radius: ['45%', '75%'],
            center: ['35%', '45%'],
            data: arr,
            label: {
              normal: {
                show: false
              }
            }
          }
        ]
      }
      getchart.on('click', (params) => {
        // 此处的value值为饼状图里 data的name 值
        this.orderStatisticsName.forEach((item) => {
          if (params.name == item.name) {
            this.riskLevel = item.level
          }
        })
        this.getTableList()
      })

      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    getRiskDeptStatistics() {
      let params = {
        placeIds: this.placeIds,
        riskName: this.riskName,
        responsiblePersonName: this.responsiblePersonName
      }
      this.$api.getRiskDeptStatistics(params).then((res) => {
        if (res.code == '200') {
          this.seriesData = res.data.array
          this.getPowerMonthTypeNoEchart()
        }
      })
    },
    getPowerMonthTypeNoEchart() {
      const getchart = echarts.init(document.getElementById('powerMonthTypeNoEchart'))
      let arr = []
      this.seriesData.forEach((item) => {
        let obj = {
          name: item.name,
          value: item.value
        }
        arr.push(obj)
      })
      const data = arr
      data.sort((a, b) => {
        return a.value - b.value
      })
      const nameList = Array.from(data, ({ name }) => name)
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          top: '4%',
          left: '4%',
          right: '5%',
          bottom: '5%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: nameList,
          axisLabel: {
            formatter: function (val) {
              var strs = val.split('') // 字符串数组
              var str = ''
              // strs循环 forEach 每五个字符增加换行符
              strs.forEach((item, index) => {
                if (index % 6 === 0 && index !== 0) {
                  str += '\n'
                }
                str += item
              })
              return str
            }
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        yAxis: [
          {
            type: 'value'
          }
        ],
        series: [
          {
            type: 'bar',
            barWidth: 10,
            data: data,
            itemStyle: {
              color: '#3562db'
            }
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    handleSizeChange(val) {
      this.pagination.size = val
      this.getTableList()
    },
    handleCurrentChange(val) {
      this.pagination.current = val
      this.getTableList()
    }
  }
}
</script>

<style lang="scss" scoped>
.control-btn-header {
  background-color: #fff;

  & > div {
    display: flex;
    padding: 10px 5px;
  }
}

.content {
  height: 100%;

  ::v-deep .el-row {
    height: 100%;

    .el-col {
      height: 100%;
    }
  }
}

.buttom {
  height: 50%;
  background-color: #fff;
  border-radius: 5px;
}

.ipt {
  width: 200px;
  margin-right: 10px;
}

.col {
  background-color: #fff;
  height: 100%;
  border-radius: 5px;
  padding: 10px;
}

#orderTypeCharts,
#powerMonthTypeNoEchart {
  width: 100%;
  height: 95%;
}

.title span {
  font-size: 15px;
}

.table-block-text {
  width: 80px;
  line-height: 24px;
  text-align: center;
  cursor: pointer;
  color: #fff;
}

.color_blue {
  color: #5188fc;
  cursor: pointer;
}

.contentTable {
  height: calc(100% - 90px);
  background: #fff;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  overflow: auto;

  .contentTable-main {
    flex: 1;
    // overflow: auto;
  }

  .contentTable-footer {
    padding: 10px 0 0;
  }

  .alarmLevel {
    padding: 3px 6px;
    border-radius: 4px;
    color: #fff;
    line-height: 14px;
  }

  .alarmStatus {
    position: relative;
    display: inline-block;
    padding-left: 12px;

    .alarmStatusIcon {
      display: inline-block;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 8px;
      height: 8px;
      border-radius: 100%;
    }
  }

  .collectIcon {
    font-size: 16px;
    margin-right: 4px;
  }
}
</style>
