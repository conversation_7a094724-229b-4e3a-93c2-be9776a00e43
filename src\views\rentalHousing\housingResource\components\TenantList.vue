<script>
export default {
  name: 'TenantList',
  components: {
    RoomRefund: () => import('./RoomRefund.vue'),
    RoomRenew: () => import('./RoomRenew.vue')
  },
  props: {
    id: String,
    baseInfo: Object
  },
  data: () => ({
    tableLoading: false,
    tableData: [],
    dialogRefund: {
      show: false,
      id: '',
      tenantName: '',
      startDate: '',
      endDate: ''
    },
    dialogRenew: {
      show: false,
      id: '',
      tenantName: '',
      endDate: '',
      spaceName: '',
      roomName: '',
      price: '',
      amount: ''
    }
  }),
  computed: {
    OperateType() {
      return {
        // 退租
        REFUND: 'refund',
        // 续租
        RELET: 'relet'
      }
    }
  },
  filters: {
    sexFilter(val) {
      let sex = {
        1: '男',
        2: '女'
      }
      if (!val) return ''
      if (val === '男') return val
      if (val === '女') return val
      return sex[val]
    }
  },
  mounted() {
    this.getDataList()
  },
  methods: {
    getDataList() {
      const params = {
        pageNum: 1,
        pageSize: 999,
        houseId: this.id
      }
      this.tableLoading = true
      this.$api.rentalHousingApi
        .queryTenantByPage(params)
        .then((res) => {
          if (res.code === '200') {
            this.tableData = res.data.records
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取租户列表失败'))
        .finally(() => (this.tableLoading = false))
    },
    // 表格相关操作总线处理
    onOperate(type, row) {
      switch (type) {
        case this.OperateType.REFUND:
          this.dialogRefund.id = row.houseHistoryRecordId
          this.dialogRefund.tenantName = row.tenantName
          this.dialogRefund.startDate = row.tenancyStartDate
          this.dialogRefund.endDate = row.tenancyEndDate
          this.dialogRefund.show = true
          break
        case this.OperateType.RELET:
          this.dialogRenew.id = row.houseHistoryRecordId
          this.dialogRenew.tenantName = row.tenantName
          this.dialogRenew.endDate = row.tenancyEndDate
          Object.assign(this.dialogRenew, this.baseInfo)
          this.dialogRenew.show = true
          break
      }
    },
    getDateStr(dateStr) {
      if (!dateStr) return '-'
      return dateStr.substring(0, 10)
    }
  }
}
</script>
<template>
  <div class="tenant-list">
    <el-table v-loading="tableLoading" height="100%" :data="tableData" border stripe table-layout="auto" class="tableAuto" row-key="id">
      <el-table-column prop="tenantName" label="姓名" />
      <el-table-column prop="tenantNumber" label="工号" />
      <el-table-column prop="tenantSex" label="性别">
        <template #default="{ row }">
          {{ row.tenantSex | sexFilter }}
        </template>
      </el-table-column>
      <el-table-column prop="tenantIdNumber" label="身份证号" width="180px" />
      <el-table-column prop="tenantDeptName" label="所属科室" show-overflow-tooltip />
      <el-table-column prop="statusDesc" label="状态" />
      <el-table-column prop="liveDate" label="入住时间" width="160px" />
      <el-table-column prop="leaveDate" label="退租日期" width="160px" :formatter="(row) => getDateStr(row.leaveDate)" />
      <el-table-column prop="tenancyStartDate" label="租期开始日期" width="110px" :formatter="(row) => getDateStr(row.tenancyStartDate)" />
      <el-table-column prop="tenancyEndDate" label="租期结束日期" width="110px" :formatter="(row) => getDateStr(row.tenancyEndDate)">
        <template #default="{ row }">
          <span :style="{ color: row.status == 2 ? '#f53f3f' : '' }">{{ getDateStr(row.tenancyEndDate) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="residueRentingDay" label="剩余租期（天）">
        <template #default="{ row }">
          <span :style="{ color: row.status == 2 ? '#f53f3f' : '' }">{{ row.residueRentingDay }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120px">
        <template #default="{ row }">
          <template v-if="row.status !== '3' && !row.leaveDate">
            <el-button type="text" @click="onOperate(OperateType.REFUND, row)">退租</el-button>
            <el-button type="text" @click="onOperate(OperateType.RELET, row)">续租</el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>
    <!--退租-->
    <RoomRefund v-bind="dialogRefund" :visible.sync="dialogRefund.show" @success="getDataList"></RoomRefund>
    <RoomRenew v-bind="dialogRenew" :visible.sync="dialogRenew.show" @success="getDataList"></RoomRenew>
  </div>
</template>
<style lang="scss" scoped>
.tenant-list {
  height: 100%;
}
</style>
