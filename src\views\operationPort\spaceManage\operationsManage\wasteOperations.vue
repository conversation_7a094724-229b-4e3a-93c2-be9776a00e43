<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-06-06 18:13:06
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-06-06 19:00:12
 * @FilePath: \ihcrs_pc\src\views\operationPort\spaceManage\operationsManage\spaceComponent.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="">
    <template v-for="(item, index) in dlayoutItems">
      <dash-item v-if="item.id == 'wasteTopLeft'" v-bind.sync="dlayoutItems[index]" :key="item.id" @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)">
        <ContentCard
          title="医废收集类型分析"
          :scrollbarHover="true"
          :cstyle="{ height: '100%' }"
          class="drag_class"
          :hasMoreOper="['edit']"
          @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')"
        >
          <div slot="title-right" class="dateType">
            <div class="btns">
              <span :class="{ 'active-btn': collectType == 'day' }" @click="changeCompletionRateType('day')">日</span>
              <span :class="{ 'active-btn': collectType == 'week' }" @click="changeCompletionRateType('week')">周</span>
              <span :class="{ 'active-btn': collectType == 'month' }" @click="changeCompletionRateType('month')">月</span>
              <span :class="{ 'active-btn': collectType == 'year' }" @click="changeCompletionRateType('year')">年</span>
            </div>
          </div>
          <div slot="content" style="height: 100%">
            <div class="collectionType">
              <div id="wasteTypeEcharts"></div>
            </div>
          </div>
        </ContentCard>
      </dash-item>
      <dash-item v-if="item.id == 'wasteTopCenter'" v-bind.sync="dlayoutItems[index]" :key="item.id" @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)">
        <ContentCard
          title="医废收集情况"
          :scrollbarHover="true"
          :cstyle="{ height: '100%' }"
          class="drag_class"
          :hasMoreOper="['edit']"
          @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')"
        >
          <div slot="content" style="height: 100%">
            <div class="collect">
              <div class="card-box card-box2">
                <span>今日</span>
                <div class="num-value">
                  <span>{{ wasteSituationData.todaySum }}</span>
                  <span>袋/桶</span>
                </div>
                <img src="@/assets/images/operationPort/waste-icon1.png" />
              </div>
              <div v-for="(item, index) in collectList" :key="index" class="card-box">
                <span>{{ item.label }}</span>
                <div class="num-value">
                  <span>{{ wasteSituationData[item.key] > 1000 ? (wasteSituationData[item.key] / 1000).toFixed(2) : wasteSituationData[item.key] }}</span>
                  <span>{{ wasteSituationData[item.key] > 1000 ? 't' : 'kg' }}</span>
                </div>
                <img src="@/assets/images/operationPort/waste-icon2.png" />
              </div>
            </div>
          </div>
        </ContentCard>
      </dash-item>
      <dash-item v-if="item.id == 'wasteTopRight'" v-bind.sync="dlayoutItems[index]" :key="item.id" @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)">
        <ContentCard
          title="出入站概况情况"
          :scrollbarHover="true"
          :cstyle="{ height: '100%' }"
          class="drag_class"
          :hasMoreOper="['edit']"
          @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')"
        >
          <div slot="content" style="height: 100%">
            <div class="access">
              <div class="card-box card-box3">
                <span>超时未处理</span>
                <div class="num-value">
                  <span>{{ inoutData.expiredCount }}</span>
                  <span>箱</span>
                </div>
                <img src="@/assets/images/operationPort/waste-icon4.png" />
              </div>
              <div class="card-box card-box3">
                <span>库存箱数</span>
                <div class="num-value">
                  <span>{{ inoutData.amount }}</span>
                  <span>箱</span>
                </div>
                <img src="@/assets/images/operationPort/waste-icon5.png" />
              </div>
              <div class="card-box card-box3">
                <span>今日入站箱数</span>
                <div class="num-value">
                  <span>{{ inoutData.gatherCount }}</span>
                  <span>箱</span>
                </div>
                <img src="@/assets/images/operationPort/waste-icon6.png" />
              </div>
              <div class="card-box card-box3">
                <span>今日出站箱数</span>
                <div class="num-value">
                  <span>{{ inoutData.buyCount }}</span>
                  <span>箱</span>
                </div>
                <img src="@/assets/images/operationPort/waste-icon7.png" />
              </div>
            </div>
          </div>
        </ContentCard>
      </dash-item>
      <dash-item v-if="item.id == 'wasteBottomLeft'" v-bind.sync="dlayoutItems[index]" :key="item.id" @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)">
        <ContentCard
          title="医废类型分析"
          :scrollbarHover="true"
          :cstyle="{ height: '100%' }"
          class="drag_class"
          :hasMoreOper="['edit']"
          @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')"
        >
          <div slot="title-right" class="dateType">
            <div class="btns">
              <span :class="{ 'active-btn': typeAnalysisType == 'day' }" @click="changeTypeAnalysisType('day')">日</span>
              <span :class="{ 'active-btn': typeAnalysisType == 'week' }" @click="changeTypeAnalysisType('week')">周</span>
              <span :class="{ 'active-btn': typeAnalysisType == 'month' }" @click="changeTypeAnalysisType('month')">月</span>
              <span :class="{ 'active-btn': typeAnalysisType == 'year' }" @click="changeTypeAnalysisType('year')">年</span>
            </div>
          </div>
          <div slot="content" style="height: 100%">
            <div class="typeAnalysis">
              <div id="typeAnalysisEcharts"></div>
            </div>
          </div>
        </ContentCard>
      </dash-item>
      <dash-item v-if="item.id == 'wasteBottomRight'" v-bind.sync="dlayoutItems[index]" :key="item.id" @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)">
        <ContentCard
          title="医废收集处置趋势分析"
          :scrollbarHover="true"
          :cstyle="{ height: '100%' }"
          class="drag_class"
          :hasMoreOper="['edit']"
          @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')"
        >
          <div slot="title-right" class="dateType">
            <div class="btns btns2">
              <span :class="{ 'active-btn': chartDateType == 'month' }" @click="changeChartDateType('month')">月度对比</span>
              <span :class="{ 'active-btn': chartDateType == 'year' }" @click="changeChartDateType('year')">年度对比</span>
            </div>
          </div>
          <div slot="content" style="height: 100%">
            <div class="middle-bottom-box">
              <div v-show="chartDateType == 'month'" id="monthChart"></div>
              <div v-show="chartDateType == 'year'" id="yearChart"></div>
            </div>
          </div>
        </ContentCard>
      </dash-item>
    </template>
  </div>
</template>
<script>
import { DashItem } from 'vue-responsive-dash'
import * as echarts from 'echarts'
export default {
  name: 'wasteOperations',
  components: {
    DashItem
  },
  props: {
    dlayoutItems: {
      type: Array,
      default: () => []
    },
    componentData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      collectList: [
        {
          label: '今日',
          key: 'today'
        },
        {
          label: '本月',
          key: 'months'
        },
        {
          label: '本年',
          key: 'years'
        }
      ],
      chartDateType: 'month',
      wasteSituationData: {},
      inoutData: {},
      collectType: 'day',
      typeAnalysisType: 'day',
      echartsDom: ['monthChart', 'yearChart', 'wasteTypeEcharts', 'typeAnalysisEcharts']
      // dlayoutItems: [
      //   { id: 'wasteTopLeft', x: 0, y: 0, width: 12, height: 6, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
      //   { id: 'wasteTopCenter', x: 12, y: 0, width: 6, height: 6, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
      //   { id: 'wasteTopRight', x: 18, y: 0, width: 6, height: 6, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
      //   { id: 'wasteBottomLeft', x: 0, y: 6, width: 12, height: 6, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
      //   { id: 'wasteBottomRight', x: 12, y: 6, width: 12, height: 6, dragAllowFrom: '.drag_class', resizable: true, draggable: true }
      // ]
    }
  },
  watch: {
    '$store.state.settings.sidebarCollapse': {
      handler(val) {
        this.$nextTick(() => {
          let echartsDom = this.echartsDom
          setTimeout(() => {
            echartsDom.forEach((item) => {
              echarts.init(document.getElementById(item)).resize()
            })
          }, 250)
        })
      },
      deep: true
    }
  },
  mounted() {
    setTimeout(() => {
      this.getMonthChartData()
      this.getWasteType()
      this.getTypeAnalysisType()
      this.getData()
    }, 300)
  },
  methods: {
    echartsResize() {
      let echartsDom = this.echartsDom
      this.$nextTick(() => {
        setTimeout(() => {
          echartsDom.forEach((item) => {
            echarts.init(document.getElementById(item)).resize()
          })
        }, 100)
      })
    },
    getMonthChartData() {
      this.$api.getHomeMonthChart({}).then((res) => {
        if (res.code == '200') {
          this.initMonthChart(res.data)
        }
      })
    },
    getYearChartData() {
      this.$api.getHomeYearChart({}).then((res) => {
        if (res.code == '200') {
          this.initYearChart(res.data)
        }
      })
    },
    getData() {
      this.$api.getHomeOfficeWasteState({ columnName: 'gather_weigh' }).then((res) => {
        if (res.code == '200') {
          this.wasteSituationData = res.data
        }
      })
      this.$api.getTodayStationInfo({ columnName: 'gather_weigh' }).then((res) => {
        if (res.code == '200') {
          this.inoutData = res.data
        }
      })
    },
    changeChartDateType(type) {
      this.chartDateType = type
      if (type == 'month') {
        this.getMonthChartData()
      } else {
        this.getYearChartData()
      }
    },
    initMonthChart(data) {
      const getchart = echarts.init(document.getElementById('monthChart'))
      let colors = ['#3562DB', '#FF9435']
      let option = {}
      if (JSON.stringify(data) != '{}') {
        option = {
          color: colors,
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross'
            }
          },
          grid: {
            right: '15%',
            left: '12%'
          },
          legend: {
            data: [
              {
                name: '收集重量',
                icon: 'circle'
              },
              {
                name: '运出重量'
              }
            ],
            textStyle: {
              color: '#333' // 图例文字颜色
            }
          },
          xAxis: [
            {
              type: 'category',
              axisTick: {
                alignWithLabel: true
              },
              data: data.monthList
            }
          ],
          yAxis: [
            {},
            {
              type: 'value',
              name: '',
              min: 0,
              max: Math.max.apply(null, data.gatherWeighList),
              position: 'left',
              axisLine: {
                lineStyle: {
                  color: '#414653'
                }
              },
              axisLabel: {
                formatter: '{value}'
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: '#e3e7ec'
                }
              }
            },
            {
              type: 'value',
              name: '',
              min: 0,
              max: Math.max.apply(null, data.buyWeighList),
              offset: 22,
              position: 'right',
              axisLine: {
                lineStyle: {
                  color: '#414653'
                }
              },
              axisLabel: {
                formatter: '{value}'
              },
              splitLine: {
                show: false
              }
            }
          ],
          series: [
            {
              name: '收集重量',
              type: 'bar',
              yAxisIndex: 1,
              barWidth: 10,
              data: data.gatherWeighList,
              itemStyle: {
                borderRadius: 2
              }
            },
            {
              name: '运出重量',
              type: 'line',
              yAxisIndex: 2,
              data: data.buyWeighList,
              symbol: 'circle',
              itemStyle: {
                borderType: 'solid',
                borderColor: '#fff',
                borderWidth: 1
              }
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    initYearChart(data) {
      const getchart = echarts.init(document.getElementById('yearChart'))
      let colors = ['#3562DB', '#FF9435']
      let option = {}
      if (JSON.stringify(data) != '{}') {
        option = {
          color: colors,
          tooltip: {
            trigger: 'none',
            axisPointer: {
              type: 'cross'
            }
          },
          legend: {
            data: [data.lastYearMonth[0].split('-', 1) + ' 收集重量', data.thisYearMonth[0].split('-', 1) + ' 收集重量']
          },
          grid: {
            top: 70,
            bottom: 50
          },
          xAxis: [
            {
              type: 'category',
              axisTick: {
                alignWithLabel: true
              },
              axisLine: {
                onZero: false,
                lineStyle: {
                  color: colors[1]
                }
              },
              axisPointer: {
                label: {
                  formatter: function (params) {
                    return '收集重量  ' + params.value + (params.seriesData.length ? '：' + params.seriesData[0].data : '')
                  }
                }
              },
              data: data.thisYearMonth
            },
            {
              type: 'category',
              axisTick: {
                alignWithLabel: true
              },
              axisLine: {
                onZero: false,
                lineStyle: {
                  color: colors[0]
                }
              },
              axisPointer: {
                label: {
                  formatter: function (params) {
                    return '收集重量  ' + params.value + (params.seriesData.length ? '：' + params.seriesData[0].data : '')
                  }
                }
              },
              data: data.lastYearMonth
            }
          ],
          yAxis: [
            {
              type: 'value'
            }
          ],
          series: [
            {
              name: data.lastYearMonth[0].split('-', 1) + ' 收集重量',
              type: 'line',
              xAxisIndex: 1,
              smooth: true,
              data: data.lastYearGatherWeigh
            },
            {
              name: data.thisYearMonth[0].split('-', 1) + ' 收集重量',
              type: 'line',
              smooth: true,
              data: data.thisYearGatherWeigh
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }

      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    changeCompletionRateType(type) {
      this.collectType = type
      this.getWasteType()
    },
    getWasteType() {
      let baseInfo = JSON.parse(sessionStorage.getItem('LOGINDATA'))
      let params = {
        ssmType: '1',
        // spatialId: baseInfo.hospitalCode,
        spatialId: '',

        dateType: this.collectType
      }
      this.$api.getRuiAnTypeAnalysisInfo(params).then((res) => {
        if (res.code === '200') {
          // this.workOdertTypeShow = false
          const arr = res.data.list
          const total = res.data.totalPrice ? res.data.totalPrice : 0
          this.$nextTick(() => {
            this.wasteTypeEchart(arr, total)
          })
        }
      })
    },
    wasteTypeEchart(arr, total) {
      const getchart = echarts.init(document.getElementById('wasteTypeEcharts'))
      let data = []
      const xdata = Array.from(arr, ({ wasteType }) => wasteType)
      let option = {}
      if (arr.length) {
        for (var i = 0; i < arr.length; i++) {
          data.push({
            name: arr[i].wasteType,
            value: arr[i].totalweigh,
            itemStyle: {
              normal: {
                borderWidth: 1,
                label: {
                  normal: { show: true }
                }
              }
            }
          })
        }
        var seriesObj = [
          {
            type: 'pie',
            clockWise: false,
            radius: '55%',
            center: ['50%', '50%'],
            // hoverAnimation: false,
            itemStyle: {
              normal: {
                borderColor: '#FFF',
                borderWidth: 2
              }
            },
            labelLine: {
              normal: {
                length: 30,
                length2: 25
                // lineStyle: {
                //   color: '#237CEC'
                // }
              }
            },
            label: {
              normal: {
                formatter: '{b}:\n{c}' + 'kg' + '\n{d}%',
                // padding: [0, -100, 25, -100],
                rich: {
                  name: {
                    fontSize: 14,
                    padding: [0, 10, 0, 0],
                    color: '#121f3e'
                  },
                  value: {
                    fontSize: 18,
                    fontWeight: 'bold',
                    color: '#121f3e'
                  }
                }
              }
            },
            data: data
          }
        ]
        option = {
          color: ['#1eb977', '#f6be2c', '#3562db', '#2aa5dc', '#bc9dd9', '#f88650', '#8998af'],
          tooltip: {
            trigger: 'item',
            // axisPointer: {
            //   lineStyle: {
            //     color: '#FFE3A6'
            //   }
            // },
            // backgroundColor: '#fff',
            // borderColor: '#fff',
            // textStyle: {
            //   color: '#000'
            // },
            formatter: '{b}:\n{c}' + 'kg' + '\n{d}%'
          },
          series: seriesObj
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    changeTypeAnalysisType(type) {
      this.typeAnalysisType = type
      this.getTypeAnalysisType()
    },
    getTypeAnalysisType() {
      let params = {
        ssmType: '1',
        spatialId: '',
        dateType: this.typeAnalysisType
      }
      this.$api.getAreaDepartmentMedicalWasteInfo(params).then((res) => {
        if (res.code === '200') {
          const arr = res.data.list
          const sortArr = arr.sort((a, b) => a.totalweigh - b.totalweigh)
          this.deptTopShow = false
          this.$nextTick(() => {
            this.getDeptProduceEcharts(sortArr)
          })
        }
      })
    },
    getDeptProduceEcharts(arr) {
      const getchart = echarts.init(document.getElementById('typeAnalysisEcharts'))
      const name = Array.from(arr, ({ officeName }) => officeName)
      const Infectedweigh = Array.from(arr, ({ Infectedweigh }) => Infectedweigh)
      const chemistryweigh = Array.from(arr, ({ chemistryweigh }) => chemistryweigh)
      const damageweigh = Array.from(arr, ({ damageweigh }) => damageweigh)
      const epidemicweigh = Array.from(arr, ({ epidemicweigh }) => epidemicweigh)
      const medicineweigh = Array.from(arr, ({ medicineweigh }) => medicineweigh)
      const pathoweigh = Array.from(arr, ({ pathoweigh }) => pathoweigh)
      const otherweigh = Array.from(arr, ({ otherweigh }) => otherweigh)
      let option = {}
      if (arr.length) {
        option = {
          backgroundColor: '',
          tooltip: {
            trigger: 'axis',
            backgroundColor: '#fff',
            textStyle: {
              color: '#000'
            },
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
            },
            formatter: function (params) {
              var relVal = params[0].name
              for (var i = 0, l = params.length; i < l; i++) {
                relVal += '<br/>' + params[i].marker + params[i].seriesName + ' : ' + params[i].value.toLocaleString() + 'kg'
              }
              return relVal
            }
          },
          grid: {
            x: '12%',
            width: '75%',
            // y: '22%'
            bottom: '18%'
          },
          legend: {
            icon: 'circle',
            orient: 'horizontal',
            left: 'center',
            top: '0',
            // data: xdata,
            itemWidth: 8,
            itemHeight: 8,
            itemGap: 16,
            textStyle: {
              color: '#000' //  字体颜色
            }
          },
          xAxis: {
            type: 'category',
            axisLine: {
              show: true,
              lineStyle: {
                color: '#e3e7ec',
                width: 1,
                type: 'solid'
              }
            },
            // boundaryGap: true,
            axisTick: {
              show: false
            },
            axisLabel: {
              color: '#414653',
              margin: 8,
              interval: 0,
              formatter: function (val) {
                var strs = val.split('') // 字符串数组
                var str = ''
                for (var i = 0, s; (s = strs[i++]); ) {
                  str += s
                  if (!(i % 10)) str += '\n' // 按需要求余
                }
                return str
              }
            },
            data: name
          },
          yAxis: {
            name: '单位：kg',
            type: 'value',
            axisLabel: {
              show: true,
              textStyle: {
                color: '#878EA9'
              }
            },
            axisLine: {
              lineStyle: {
                type: 'solid',
                color: '#666',
                width: '1'
              }
            },
            splitLine: {
              lineStyle: {
                color: '#e3e7ec',
                width: 1,
                type: 'solid'
              }
            },
            axisTick: {
              show: false
            }
          },
          series: [
            {
              name: '损伤类',
              data: damageweigh,
              type: 'bar',
              stack: 'one',
              barWidth: '6',
              itemStyle: {
                color: '#bc9dd9'
              }
            },
            {
              name: '病理类',
              data: pathoweigh,
              type: 'bar',
              stack: 'one',
              barWidth: '6',
              itemStyle: {
                color: '#2aa5dc'
              }
            },
            {
              name: '化学类',
              data: chemistryweigh,
              type: 'bar',
              stack: 'one',
              barWidth: '6',
              itemStyle: {
                color: '#1eb977'
              }
            },
            {
              name: '感染类',
              data: Infectedweigh,
              type: 'bar',
              stack: 'one',
              barWidth: '6',
              itemStyle: {
                color: '#3562db'
              }
            },
            {
              name: '药物类',
              data: medicineweigh,
              type: 'bar',
              stack: 'one',
              barWidth: '6',
              itemStyle: {
                color: '#f6be2c'
              }
            },
            {
              name: '涉疫类',
              data: epidemicweigh,
              type: 'bar',
              stack: 'one',
              barWidth: '6',
              itemStyle: {
                color: '#f88650'
              }
            },
            {
              name: '其他类',
              data: otherweigh,
              type: 'bar',
              stack: 'one',
              barWidth: '6',
              itemStyle: {
                color: '#8998af'
              }
            }
          ],
          dataZoom: [
            {
              type: 'slider',
              realtime: true,
              startValue: 0,
              endValue: 5,
              height: 4,
              fillerColor: 'rgba(17, 100, 210, 0.42)', // 滚动条颜色
              borderColor: 'rgba(17, 100, 210, 0.12)',
              handleSize: 0, // 两边手柄尺寸
              showDetail: false, // 拖拽时是否展示滚动条两侧的文字
              top: '96%',
              zoomLock: true // 是否只平移不缩放
              // moveOnMouseMove:true, //鼠标移动能触发数据窗口平移
              // zoomOnMouseWheel :true, //鼠标移动能触发数据窗口缩放
            },
            {
              type: 'inside', // 支持内部鼠标滚动平移
              start: 0,
              end: 40,
              zoomOnMouseWheel: false, // 关闭滚轮缩放
              moveOnMouseWheel: true, // 开启滚轮平移
              moveOnMouseMove: true // 鼠标移动能触发数据窗口平移
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }

      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.collect,
.access {
  height: 100%;
  padding: 20px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;

  .card-box {
    width: 48%;
    height: 40%;
    background-color: rgb(255 148 53 / 10%);
    border-radius: 4px;
    font-size: 15px;
    padding: 16px;
    position: relative;

    .num-value {
      position: absolute;
      bottom: 12%;
    }

    .num-value span:nth-child(1) {
      font-size: 24px;
      font-weight: 700;
      margin-right: 5px;
    }

    .num-value span:nth-child(2) {
      font-size: 14px;
      color: #ccced3;
    }

    img {
      position: absolute;
      bottom: 10%;
      right: 8%;
      width: 34px;
      height: 34px;
    }

    span {
      color: #121f3e;
    }
  }

  .card-box2 {
    background: rgba(255, 148, 53, 0.1);
  }

  .card-box3 {
    background: rgba(53, 98, 219, 0.1);
  }
}

.middle-bottom-box {
  width: 100%;
  height: 100%;

  #monthChart {
    width: 100%;
    height: 100%;
  }

  #yearChart {
    width: 100%;
    height: 100%;
  }
}

.btns {
  width: 220px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.btns > span {
  width: 22%;
  background-color: #f6f5fa;
  font-size: 14px;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  border: none;
  border-radius: 4px;
  color: #414653;
  cursor: pointer;
}

.btns2 {
  width: 200px;

  > span {
    width: 48%;
  }
}

.active-btn {
  background-color: #e6effc !important;
  color: #3562db !important;
  border: none !important;
}

.collectionType {
  width: 100%;
  height: 100%;

  #wasteTypeEcharts {
    width: 100%;
    height: 100%;
  }
}

.typeAnalysis {
  width: 100%;
  height: 100%;

  #typeAnalysisEcharts {
    width: 100%;
    height: 100%;
  }
}

.dateType {
  position: absolute;
  right: 50px;
  top: 0;
}
</style>
