<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <el-container style="height: 100%">
        <el-aside v-if="$route.query.type != 'View'" style="border-right: 1px solid #ddd; height: 100%" class="left">
          <ContentCard title="空间结构"></ContentCard>
          <el-input v-model="filterText" style="width: 280px" placeholder="请输入关键字"></el-input>
          <div v-loading="treeLoading" class="sino_tree_box" style="height: calc(100% - 100px) !important">
            <div class="tree_div">
              <el-tree
                ref="tree"
                class="filter-tree"
                :data="treeData"
                :props="defaultProps"
                :default-expanded-keys="idArr"
                :filter-node-method="filterNode"
                :highlight-current="true"
                node-key="id"
                @node-click="nodeClick"
              ></el-tree>
            </div>
          </div>
        </el-aside>
        <el-main>
          <div class="sino_content">
            <el-form
              ref="formInline"
              class="sino_form"
              :model="formInline"
              :inline="true"
              :rules="rules"
              :disabled="$route.query.type == 'View'"
              label-position="right"
              label-width="150px"
            >
              <ContentCard title="基础信息"></ContentCard>
              <div style="margin-top: 25px">
                <el-form-item label="位置" prop="simName">
                  <el-input v-model="formInline.simName" style="width: 350px" placeholder="请选择空间位置" show-word-limit> </el-input>
                </el-form-item>
                <br />
                <el-form-item label="空间名称" prop="localSpaceName">
                  <el-input v-model="formInline.localSpaceName" class="sino_form_input" placeholder="请输入空间名称" show-word-limit> </el-input>
                </el-form-item>
                <el-form-item label="空间状态" prop="spaceStateId">
                  <el-select v-model="formInline.spaceStateId" class="sino_form_input" placeholder="请选择空间用途" filterable clearable>
                    <el-option v-for="item in spaceStateList" :key="item.id" :label="item.dictName" :value="item.id"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item v-if="status == 1" label="本地编码" prop="localSpaceCode">
                  <el-input v-model="formInline.localSpaceCode" maxlength="20" class="sino_form_input" placeholder="请输入本地编码" show-word-limit> </el-input>
                </el-form-item>
                <el-form-item v-else label="本地编码" prop="">
                  <el-input v-model="formInline.localSpaceCode" disabled class="sino_form_input" placeholder="系统生成" show-word-limit> </el-input>
                </el-form-item>
                <el-form-item label="模型编码" prop="modelCode">
                  <el-input v-model="formInline.modelCode" class="sino_form_input" placeholder="系统生成" show-word-limit disabled> </el-input>
                </el-form-item>
                <el-form-item label="功能类型" prop="functionDictId">
                  <el-select v-model="formInline.functionDictId" class="sino_form_input" placeholder="请选择功能类型" filterable clearable>
                    <el-option v-for="item in spaceTypeList" :key="item.id" :label="item.dictName" :value="item.id"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="空间高度" prop="hight">
                  <el-input
                    v-model="formInline.hight"
                    class="sino_form_input"
                    placeholder="请输入空间高度"
                    show-word-limit
                    maxlength="15"
                    onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')"
                    ><template slot="append">m</template>
                  </el-input>
                </el-form-item>
                <el-form-item label="空间长度" prop="length">
                  <el-input
                    v-model="formInline.length"
                    class="sino_form_input"
                    placeholder="请输入空间长度"
                    maxlength="15"
                    show-word-limit
                    onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')"
                  >
                    <template slot="append">m</template>
                  </el-input>
                </el-form-item>
                <el-form-item label="空间宽度" prop="width">
                  <el-input
                    v-model="formInline.width"
                    class="sino_form_input"
                    placeholder="请输入空间宽度"
                    maxlength="15"
                    show-word-limit
                    onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')"
                  >
                    <template slot="append">m</template>
                  </el-input>
                </el-form-item>
                <el-form-item label="建筑面积" prop="area">
                  <el-input
                    v-model="formInline.area"
                    class="sino_form_input"
                    placeholder="请输入建筑面积"
                    maxlength="15"
                    show-word-limit
                    onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')"
                    ><template slot="append">㎡</template>
                  </el-input>
                </el-form-item>
                <el-form-item label="使用面积" prop="useArea">
                  <el-input
                    v-model="formInline.useArea"
                    class="sino_form_input"
                    placeholder="请输入使用面积"
                    maxlength="15"
                    show-word-limit
                    onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')"
                    ><template slot="append">㎡</template>
                  </el-input>
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                  <el-input v-model="formInline.remark" class="sino_form_input" placeholder="请输入备注" show-word-limit> </el-input>
                </el-form-item>
                <ContentCard title="使用管理"></ContentCard>
                <el-form-item label="归属部门" prop="remark">
                  <el-input v-model="formInline.dmName" :disabled="$route.query.type == 'View'" placeholder="请选择归属部门" class="sino_form_input" @focus="deptfocus"> </el-input>
                </el-form-item>
                <el-form-item label="责任人" prop="remark">
                  <el-input v-model="formInline.principalName" :disabled="$route.query.type == 'View'" placeholder="请选择安全责任人" class="sino_form_input" @focus="personfocus">
                  </el-input>
                </el-form-item>
                <div v-for="(item, i) in formInline.spacePhone" :key="i">
                  <el-form-item label="空间电话" :prop="'spacePhone.' + i">
                    <!-- :rules="rules.deptPhone" -->
                    <el-input
                      v-model.trim="formInline.spacePhone[i]"
                      class="sino_form_input"
                      placeholder="请输入空间电话"
                      show-word-limit
                      maxlength="11"
                      onkeyup="value=value.replace(/[^\d.]/g,'')"
                    >
                    </el-input>
                    <i class="el-icon-plus" style="color: blue" @click="AddRow"></i>
                    <i v-if="i > 0" class="el-icon-minus" style="color: red" @click="DelRow(i)"></i>
                  </el-form-item>
                </div>
                <el-form-item label="空间图片" prop="spacePicUrl">
                  <sino-upload :fileList="photoList" modelName="spacePic" @input="uploadChangePhoto"></sino-upload>
                </el-form-item>
                <ContentCard title="产权管理"></ContentCard>
                <el-form-item label="竣工日期" prop="complitionDate">
                  <el-date-picker v-model="formInline.complitionDate" class="sino_form_input" type="date" value-format="yyyy-MM-dd" placeholder="选择竣工日期" clearable>
                  </el-date-picker>
                </el-form-item>
                <el-form-item label="投入使用日期" prop="useDate">
                  <el-date-picker v-model="formInline.useDate" class="sino_form_input" type="date" value-format="yyyy-MM-dd" placeholder="选择投入使用日期" clearable>
                  </el-date-picker>
                </el-form-item>
                <el-form-item label="资产原值" prop="assetsCost">
                  <el-input
                    v-model="formInline.assetsCost"
                    class="sino_form_input"
                    placeholder="请输入资产原值"
                    show-word-limit
                    onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')"
                  >
                    <template slot="append">元</template>
                  </el-input>
                </el-form-item>
                <el-form-item label="房产证" prop="houseOwnership">
                  <el-radio v-model="formInline.houseOwnership" :label="1">有</el-radio>
                  <el-radio v-model="formInline.houseOwnership" :label="0">无</el-radio>
                </el-form-item>
                <br />
                <el-form-item v-if="formInline.houseOwnership == '1'" label="不动产登记号" prop="registratioNo">
                  <el-input v-model="formInline.registratioNo" class="sino_form_input" placeholder="请输入不动产登记号" show-word-limit> </el-input>
                </el-form-item>
                <br />
                <el-form-item label="附件" prop="spaceFile">
                  <el-upload
                    ref="uploadFile"
                    :disabled="$route.query.type == 'View'"
                    action
                    drag
                    class="mterial_file"
                    :limit="3"
                    multiple
                    :http-request="httpRequest"
                    :beforeUpload="beforeAvatarUpload"
                    :file-list="fileList"
                    :on-exceed="handleExceed"
                    :on-remove="handleRemove"
                    list-type="text"
                  >
                    <i class="el-icon-upload"></i>
                    <div class="el-upload__text">
                      将文件拖到此处，或
                      <em>点击上传</em>
                    </div>
                    <div slot="tip" class="el-upload__tip">
                      <p class="tip_2">最多可上传三个文件，大小不超过50M，文件格式不限</p>
                    </div>
                  </el-upload>
                </el-form-item>
                <br />
                <el-form-item v-show="$route.query.type == 'View'" label="空间二维码" prop="qrcodeBase64">
                  <el-image :src="'data:image/png;base64,' + formInline.qrcodeBase64">
                    <div slot="error" class="image-slot">
                      <i class="el-icon-picture-outline"></i>
                    </div>
                  </el-image>
                </el-form-item>
              </div>
            </el-form>
          </div>
          <sinoDialog ref="dialogDepart" title="设置归属部门" @sureDialog="sureDialog(2)" @closeDialog="closeDialog">
            <div class="title_box" style="border: none; padding: 0"><svg-icon name="right-arrow" />责任部门</div>
            <el-collapse v-model="activeName" style="padding: 0 10px 0 20px" accordion @change="handelChangeCom">
              <el-collapse-item v-for="(list, index) in collapseData" :key="index" :title="list.unitComName" :name="list.umId">
                <div class="sino_tree_box" style="margin: 0; overflow: auto">
                  <el-tree
                    ref="departTree"
                    class="filter-tree"
                    style="overflow: hidden"
                    :data="departTreeData"
                    :props="departProps"
                    node-key="id"
                    show-checkbox
                    :check-strictly="true"
                    highlight-current
                    @check-change="(data, checked, self) => departNodeClickRad(data, checked, self, index)"
                  ></el-tree>
                </div>
              </el-collapse-item>
            </el-collapse>
          </sinoDialog>
          <sinoDialog ref="dialogUsers" title="设置责任人" :customStyle="true" @sureDialog="sureDialog(3)" @closeDialog="closeDialog">
            <StaffManagement ref="StaffManagement" :isDialog="true" :checkbox="false" :nature="true"></StaffManagement>
          </sinoDialog>
        </el-main>
      </el-container>
    </div>
    <div slot="footer">
      <el-button v-if="$route.query.type != 'View'" type="primary" @click="complete">保存</el-button>
      <el-button type="primary" plain @click="$router.go(-1)">取消</el-button>
    </div>
  </PageContainer>
</template>

<script>
import store from '@/store/index'
import { transData } from '@/util'
import StaffManagement from '../components/staffManagement.vue'
import sinoUpload from '../common/sinoUpload.vue'
import sinoDialog from '../common/sinoDialog.vue'

export default {
  components: {
    sinoUpload,
    sinoDialog,
    StaffManagement
  },
  data() {
    return {
      fileList: [],
      status: 1, // 1手动 2自动
      activeName: '',
      departTreeData: [],
      checkedDepartId: '',
      collapseData: [],
      checkedDepartName: '',
      departProps: {
        label: 'deptName',
        children: 'list'
      },
      photoList: [],

      title: this.$route.query.type == 'Add' ? '添加空间' : this.$route.query.type == 'View' ? '查看空间' : '编辑空间',
      formInline: {
        simName: '',
        localSpaceName: '',
        localSpaceCode: '',
        spaceStateId: '',
        functionDictId: '',
        hight: '',
        length: '',
        width: '',
        area: '',
        useArea: '',
        remark: '',
        modelCode: '',
        dmId: '',
        dmName: '',
        principalId: '',
        principalName: '',
        spacePhone: [''],
        spacePicUrl: '',
        spaceFileUrl: [],
        spaceFileIds: [],
        houseOwnership: 0,
        registratioNo: '',
        assetsCost: '',
        complitionDate: '',
        useDate: '',
        personId: '',
        deptId: ''
      },
      functionDictId: '',
      rules: {
        simName: {
          required: true,
          message: '请选择空间位置',
          trigger: 'change'
        },
        localSpaceName: {
          required: true,
          message: '请输入空间名称',
          trigger: 'change'
        },
        localSpaceCode: {
          required: true,
          message: '请输入本地编码',
          trigger: 'change'
        },
        spaceStateId: {
          required: true,
          message: '请选择空间状态',
          trigger: 'blur'
        }
      },
      spaceStateList: [],
      spaceTypeList: [],
      treeLoading: true,
      treeData: [],
      filterText: '',
      idArr: [],
      spaceIds: [],
      simNames: [],
      checkedNodeId: '',
      defaultProps: {
        label: 'ssmName',
        children: 'list'
      },
      filters: {
        functionDictId: ''
      }
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    this.getSettingItemByKey()
  },
  mounted() {
    this.valveTypeListFn(['SP', 'SPZT']) // 空间用途、功能类型字典列表
    this.spaceTreeListFn()
    if (this.$route.query.type != 'Add') {
      this.getSpaceDetailFn()
    }
  },
  methods: {
    handelChangeCom(val) {
      this.activeName = val
      this.getDeptListFn(val)
    },
    getDeptListFn(unitId) {
      this.departTreeData = []
      this.$api
        .getDeptList({
          unitId: unitId
        })
        .then((res) => {
          if (res.code == 200) {
            this.departTreeData = transData(res.data, 'id', 'pid', 'list')
            // this.$nextTick(() => {
            this.setDisable(this.departTreeData)
            // })
          }
        })
    },
    setDisable(data) {
      data.forEach((v) => {
        if (v.list && v.list.length) {
          v.disabled = true
          this.setDisable(v.list) // 子级循环时把这一层数据的count传入
        }
      })
    },
    sureDialog(type) {
      if (type == 1) {
      } else if (type == 2) {
        this.formInline.deptId = this.checkedDepartId
        this.formInline.dmName = this.checkedDepartName

        this.$refs.dialogDepart.dialogTableVisible = false
      } else {
        this.$refs.dialogUsers.dialogTableVisible = false
        let selected = this.$refs.StaffManagement.radioObj
        this.formInline.personId = selected.id
        this.formInline.principalName = selected.staffName
      }
    },
    closeDialog() {
      this.reset()
      this.$refs.dialogDepart.dialogTableVisible = false
      this.$refs.dialogUsers.dialogTableVisible = false
    },
    // 重置
    reset() {
      this.checkedDepartId = ''
      this.checkedDepartName = ''

      this.activeName = ''
      this.departTreeData = []
    },
    personfocus() {
      this.$refs.dialogUsers.dialogTableVisible = true
    },
    deptfocus() {
      this.$refs.dialogDepart.dialogTableVisible = true
      this.departNodeClickRad()
      this.getUnitListFn()
    },
    departNodeClickRad(data, checked, self, index) {
      if (checked) {
        let num = this.collapseData.length - 1
        for (let i = 0; i <= num; i++) {
          if (i != index) {
            this.$refs.departTree[i].setCheckedKeys([])
          } else {
            this.$refs.departTree[i].setCheckedKeys([data.id])
            this.checkedDepartId = data.id
            this.checkedDepartName = data.deptName
          }
        }
      }
    },
    getUnitListFn() {
      this.$api.getUnitList({}).then((res) => {
        this.treeLoading = false
        if (res.code == 200) {
          // 单位仅为本院
          this.collapseData = []
          res.data.map((list) => {
            list.nature == 1 ? this.collapseData.push(list) : ''
          })
        }
      })
    },
    beforeAvatarUpload(file) {
      const isLt50M = file.size / 1024 / 1024 < 50
      if (!isLt50M) {
        this.$message.error('上传图片大小不能超过 50MB!')
        return false
      }
      if (file.name.indexOf(',') != -1) {
        this.$message.error('非法的文件名')
        return false
      }
    },
    /**
     * 删除文件
     */
    handleRemove(file, fileList) {
      this.fileList.forEach((i) => {
        if (i.url == this.$tools.imgUrlTranslation(file.url)) {
          this.fileList.splice(i, 1)
        }
      })
      this.formInline.spaceFileUrl = this.fileList.map((a) => a.url)
      this.formInline.spaceFileIds = this.fileList.map((a) => a.uid)
    },
    /**
     * 文件上传成功
     */
    handlePreview(file) {},
    httpRequest(item) {
      this.fileList.push(item.file)
      let data = {
        file: item.file,
        modelName: 'spaceFile',
        storageType: 'minio',
        persistenceType: 'forever',
        specifyFileName: new Date().getTime(),
        folderPath: 'hospital',
        serviceName: 'ihbs'
      }
      this.$api.getUploadOne(data).then((res) => {
        if (res.code == 200) {
          this.formInline.spaceFileUrl.push(res.data.fileUrl)
          this.formInline.spaceFileIds.push(res.data.uid)
          this.$message({
            message: res.msg,
            type: 'success'
          })
        }
      })
    },
    handleExceed(files, fileList) {
      this.$message.warning('最多选择三个文件')
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`)
    },
    downloadIamge(urL, name) {
      const a = document.createElement('a')
      const url = urL
      // 这里是将url转成blob地址，
      fetch(url)
        .then((res) => res.blob())
        .then((blob) => {
          // 将链接地址字符内容转变成blob地址
          a.href = URL.createObjectURL(blob)
          a.download = name || '' // 下载文件的名字
          document.body.appendChild(a)
          a.click()
          // 在资源下载完成后 清除 占用的缓存资源
          window.URL.revokeObjectURL(a.href)
          document.body.removeChild(a)
        })
    },
    getSettingItemByKey() {
      let params = {
        key: 'localSpaceCode',
        isUsed: '0'
      }
      this.$api.getSettingItemByKey(params).then((res) => {
        if (res.code == 200) {
          if (res.data.length > 0) {
            res.data.forEach((i) => {
              i.itemVal = JSON.parse(i.itemVal)
            })
            res.data.forEach((e) => {
              if (e.itemVal.model == 1) {
                this.status = 2
              } else {
                this.status = 1
              }
            })
          } else {
            this.status = 1
          }
        }
      })
    },
    uploadChangePhoto(val, state) {
      if (state) {
        this.formInline.spacePicUrl = ''
      } else {
        this.formInline.spacePicUrl = val.fileUrl
      }
    },
    AddRow() {
      this.formInline.spacePhone.push('')
    },
    DelRow(index) {
      this.formInline.spacePhone.splice(index, 1)
    },

    //  根据空间ID获取空间信息详情
    getSpaceDetailFn() {
      this.$api
        .getSpaceDetail({
          id: this.$route.query.id
        })
        .then((res) => {
          if (res.code == 200) {
            this.formInline = res.data
            this.formInline.spacePicUrl ? (this.photoList = [{ name: '', url: this.$tools.imgUrlTranslation(this.formInline.spacePicUrl) }]) : (this.photoList = [])

            if (this.formInline.spaceFileIds) {
              this.formInline.spaceFileUrl = this.formInline.spaceFileUrl.split(',')

              this.$api.getFilesByIds({ ids: this.formInline.spaceFileIds }).then((res) => {
                res.data.forEach((i) => {
                  let obj = {
                    name: i.fileOriginName,
                    url: this.$tools.imgUrlTranslation(i.fileUrl),
                    uid: i.uid
                  }
                  this.fileList.push(obj)
                })
              })
              this.formInline.spaceFileIds = this.formInline.spaceFileIds.split(',')
            } else {
              this.formInline.spaceFileUrl = []
              this.formInline.spaceFileIds = []
            }
            if (this.formInline.spacePhone) {
              this.formInline.spacePhone = this.formInline.spacePhone.split(',')
            } else {
              this.formInline.spacePhone = ['']
            }

            if (this.$route.query.type != 'View') {
              this.$refs.tree.setCurrentKey(res.data.ssmId)
            }
          }
        })
    },
    // ---------------------------------- Tree
    // 空间结构Tree
    spaceTreeListFn() {
      this.$api.spaceTreeList().then((res) => {
        this.treeLoading = false
        if (res.code == 200) {
          res.data.map((tree) => {
            tree.ssmType == 1 || tree.ssmType == 2 || tree.ssmType == 3 ? this.idArr.push(tree.id) : ''
            return this.idArr
          })
          this.treeData = this.$tools.transData(res.data, 'id', 'pid', 'list')
        }
      })
    },
    filterNode(value, data) {
      if (!value) return true
      return data.ssmName.indexOf(value) !== -1
    },
    nodeClick(val) {
      this.checkedNodeId = val.id
      const { tree } = this.$refs
      this.spaceIds = []
      this.simNames = []
      let checkedNode = tree.getNode(val.id)
      if (checkedNode.data.ssmType == 4) {
        this.getTreeNode(checkedNode)
      } else {
        this.$message({
          message: '请选择楼层 ！',
          type: 'warning'
        })
      }
      this.formInline.simName = this.simNames.join('>')
    },
    getTreeNode(node) {
      if (node.level >= 1) {
        this.spaceIds.unshift(node.data.id)
        this.simNames.unshift(node.data.ssmName)
        this.getTreeNode(node.parent)
      } else {
        this.spaceIds.unshift('#')
      }
    },
    // ---------------------------------- 下拉框Data
    //  获取功能类型字典列表
    valveTypeListFn(val) {
      val.forEach((item) => {
        this.$api
          .valveTypeList({
            typeValue: item
          })
          .then((res) => {
            if (res.code == 200) {
              item == 'SP' ? (this.spaceTypeList = res.data) : (this.spaceStateList = res.data)
              this.spaceStateList.forEach((i) => {
                if (i.dictName == '使用中') {
                  this.formInline.spaceStateId = i.id
                }
              })
              this.other()
            }
          })
      })
    },
    other() {
      this.spaceTypeList.forEach((item) => {
        if (item.dictName == '其他') {
          this.functionDictId = item.id
        }
      })
    },
    // -------------------------------------FormFn
    complete() {
      let fnName
      this.$route.query.type == 'Edit' ? (fnName = this.$api.updateSpace) : (fnName = this.$api.addSpace)

      if (this.formInline.functionDictId == '' || this.formInline.functionDictId == null) {
        this.formInline.functionDictId = this.functionDictId
      }
      let data = {
        id: this.$route.query.id,
        ...this.formInline
      }
      if (data.spacePhone) {
        data.spacePhone = data.spacePhone.toString()
      }

      if (data.spaceFileUrl && data.spaceFileIds) {
        data.spaceFileUrl = data.spaceFileUrl.toString()
        data.spaceFileIds = data.spaceFileIds.toString()
      }
      if (this.status == 2 && this.$route.query.type == 'Add') {
        data.localSpaceCode = ''
      }
      if (this.$route.query.type == 'Edit') {
        const userInfo = store.state.user.userInfo.user
        data.staffName = userInfo.staffName || ''
        if (data.localSpaceCode) {
          let str = data.localSpaceCode.substring(0, 3)
          if (str != 'KJ,') {
            data.localSpaceCode = 'KJ,' + data.localSpaceCode
          }
        }
      }
      delete data.dmId
      delete data.dmName
      delete data.principalId
      delete data.principalName
      delete data.functionDictName
      delete data.qrcodeBase64
      data.simCode = this.spaceIds == true ? this.spaceIds.join() : this.formInline.simCode
      data.ssmId = this.checkedNodeId ? this.checkedNodeId : this.formInline.ssmId
      if (data.useArea && data.area == '') return this.$message.error('请填写建筑面积')
      // if (data.useArea && data.area && data.useArea - data.area > 0) return this.$message.error('使用面积不能大于建筑面积')
      if (data.useArea && data.area && Number(data.useArea) > Number(data.area)) return this.$message.error('使用面积不能大于建筑面积')
      this.$refs['formInline'].validate((valid) => {
        let header = {}
        if (this.$route.query.type === 'Edit') {
          header = {
            'operation-type': 2,
            'operation-id': data.id,
            'operation-name': data.localSpaceName
          }
        } else {
          header = {
            'operation-type': 1
          }
        }
        if (valid) {
          fnName(data, header).then((res) => {
            if (res.code == 200) {
              this.cancel()
              this.$message({
                message: res.msg,
                type: 'success'
              })
            }
          })
        }
      })
    },
    cancel() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
  padding: 10px;
  // overflow-y: auto;
}

.sino_form_input {
  width: 300px;
}

.sino_tree_box {
  margin: 10px;
  overflow: auto;

  .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
    background-color: #f5f6fb;
  }

  .el-tree-node__content {
    height: 38px;

    .el-tree-node__label {
      font-size: 15px;
    }
  }
}

.el-aside {
  text-align: center;
  height: 100%;
  background-color: #fff;
  overflow: hidden;
}
</style>
