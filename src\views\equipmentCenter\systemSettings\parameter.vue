<template>
  <PageContainer>
    <div slot="content" class="role-content" style="height: 100%">
      <div class="role-content-left">
        <div class="toptip">
          <span class="green_line"></span>
          参数类别
        </div>
        <div class="left_content">
          <ul>
            <li :class="{ pitchOn: setType == 1 }" @click="setType = 1">消息提醒设置</li>
            <li v-if="systemType == '2'" :class="{ pitchOn: setType == 2 }" @click="setType = 2">巡检任务提醒</li>
            <li
              v-if="systemType == '1'"
              :class="{ pitchOn: setType == 3 }"
              @click="
                setType = 3
                getConfigList()
              "
            >
              设备设施到期提醒配置
            </li>
            <li :class="{ pitchOn: setType == 4 }" @click="setType = 4">上传图片水印设置</li>
          </ul>
        </div>
      </div>
      <div class="role-content-right">
        <!-- 设备 -->
        <div v-if="systemType == '1' && setType == 1 && form.checkList.length" class="message_set">
          <div v-if="setType == 1">
            <div class="title_h">设备巡检消息提醒</div>
            <div class="parameter_box">
              <el-form ref="form" label-width="120px">
                <div class="parameter_row">
                  <el-form-item label="提醒方式">
                    <el-checkbox-group v-model="form.checkList[7].value">
                      <el-checkbox label="1">App</el-checkbox>
                      <el-checkbox label="2" disabled>短信</el-checkbox>
                      <el-checkbox label="3" disabled>微信</el-checkbox>
                    </el-checkbox-group>
                  </el-form-item>
                </div>
                <div class="parameter_row">
                  <el-form-item label="即时提醒">
                    <el-radio-group v-model="form.checkList[6].value" @change="riskChange($event, '6')">
                      <el-radio label="1">开启</el-radio>
                      <el-radio label="2">关闭</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </div>
                <div class="parameter_row">
                  <el-form-item label="提前提醒">
                    <el-radio-group v-model="form.checkList[5].value" @change="riskChange($event, '5')">
                      <el-radio label="1">开启</el-radio>
                      <el-radio label="2">关闭</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <div class="parameter_time">
                    任务开始前
                    <div class="tiem_input">
                      <el-input v-model="form.checkList[5].reminderInAdvance" oninput="value=value.replace(/[^\d]/g,'')" class="input_param" placeholder=""></el-input>
                    </div>
                    分钟开启提醒，每隔
                    <div class="tiem_input">
                      <el-input v-model="form.checkList[5].advanceReminderInterval" oninput="value=value.replace(/[^\d]/g,'')" class="input_param" placeholder=""></el-input>
                    </div>
                    分钟提醒
                    <div class="tiem_input">
                      <el-input class="input_param" oninput="value=value.replace(/[^\d]/g,'')" value="1" placeholder="" disabled></el-input>
                    </div>
                    次，
                  </div>
                </div>
                <!-- <div class="parameter_row">
                  <el-form-item label="信息接收人">
                    <el-radio v-model="form.checkList[4].value" label="1">部门</el-radio>
                    <el-radio v-model="form.checkList[4].value" label="2">责任人</el-radio>
                  </el-form-item>
                </div> -->
              </el-form>
            </div>
            <div class="title_h">设备保养消息提醒</div>
            <div class="parameter_box">
              <el-form ref="form" label-width="120px">
                <div class="parameter_row">
                  <el-form-item label="提醒方式">
                    <el-checkbox-group v-model="form.checkList[3].value">
                      <el-checkbox label="1">App</el-checkbox>
                      <el-checkbox label="2" disabled>短信</el-checkbox>
                      <el-checkbox label="3" disabled>微信</el-checkbox>
                    </el-checkbox-group>
                  </el-form-item>
                </div>
                <div class="parameter_row">
                  <el-form-item label="即时提醒">
                    <el-radio-group v-model="form.checkList[2].value" @change="riskChange($event, '2')">
                      <el-radio label="1">开启</el-radio>
                      <el-radio label="2">关闭</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </div>
                <div class="parameter_row">
                  <el-form-item label="提前提醒">
                    <el-radio-group v-model="form.checkList[1].value" @change="riskChange($event, '1')">
                      <el-radio label="1">开启</el-radio>
                      <el-radio label="2">关闭</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <div class="parameter_time">
                    任务开始前
                    <div class="tiem_input">
                      <el-input v-model="form.checkList[1].reminderInAdvance" oninput="value=value.replace(/[^\d]/g,'')" class="input_param" placeholder=""></el-input>
                    </div>
                    分钟开启提醒，每隔
                    <div class="tiem_input">
                      <el-input v-model="form.checkList[1].advanceReminderInterval" oninput="value=value.replace(/[^\d]/g,'')" class="input_param" placeholder=""></el-input>
                    </div>
                    分钟提醒
                    <div class="tiem_input">
                      <el-input class="input_param" value="1" oninput="value=value.replace(/[^\d]/g,'')" placeholder="" disabled></el-input>
                    </div>
                    次，
                  </div>
                </div>
                <!-- <div class="parameter_row">
                  <el-form-item label="信息接收人">
                    <el-radio v-model="form.checkList[0].value" label="1">部门</el-radio>
                    <el-radio v-model="form.checkList[0].value" label="2">责任人</el-radio>
                  </el-form-item>
                </div> -->
              </el-form>
            </div>
            <!-- 处理按钮 -->
            <div class="btn">
              <el-button @click="reset">重置</el-button>
              <el-button @click="submit">保存</el-button>
            </div>
          </div>
        </div>
        <!-- 综合巡检 -->
        <div v-if="systemType == '2' && setType != 4" class="message_set">
          <div v-show="setType == 1">
            <div class="title_h">巡检消息提醒</div>
            <div class="parameter_box">
              <el-form ref="form" label-width="120px">
                <div class="parameter_row">
                  <el-form-item label="提醒方式">
                    <el-checkbox-group v-model="form.checkList[5].value">
                      <el-checkbox label="1">App</el-checkbox>
                      <el-checkbox label="2" disabled>短信</el-checkbox>
                      <el-checkbox label="3" disabled>微信</el-checkbox>
                    </el-checkbox-group>
                  </el-form-item>
                </div>
                <div class="parameter_row">
                  <el-form-item label="即时提醒">
                    <el-radio-group v-model="form.checkList[4].value" @change="troubleshootChange($event, '4')">
                      <el-radio label="1">开启</el-radio>
                      <el-radio label="2">关闭</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </div>
                <div class="parameter_row">
                  <el-form-item label="提前提醒">
                    <el-radio-group v-model="form.checkList[3].value" @change="troubleshootChange($event, '3')">
                      <el-radio label="1">开启</el-radio>
                      <el-radio label="2">关闭</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <div class="parameter_time">
                    任务开始前
                    <div class="tiem_input">
                      <el-input v-model="form.checkList[3].reminderInAdvance" oninput="value=value.replace(/[^\d]/g,'')" class="input_param" placeholder=""></el-input>
                    </div>
                    分钟开启提醒，每隔
                    <div class="tiem_input">
                      <el-input v-model="form.checkList[3].advanceReminderInterval" oninput="value=value.replace(/[^\d]/g,'')" class="input_param" placeholder=""></el-input>
                    </div>
                    分钟提醒
                    <div class="tiem_input">
                      <el-input class="input_param" value="1" placeholder="" oninput="value=value.replace(/[^\d]/g,'')" disabled></el-input>
                    </div>
                    次，
                  </div>
                </div>
              </el-form>
            </div>
            <!-- 处理按钮 -->
            <div class="btn">
              <el-button @click="reset">重置</el-button>
              <el-button @click="submit">保存</el-button>
            </div>
          </div>
          <div v-show="setType == 2" class="message_set">
            <div style="padding: 16px">
              <el-form ref="form" label-width="140px">
                <div class="parameter_row">
                  <el-form-item label="空间巡检点显示层级">
                    <el-checkbox-group v-model="form.checkList[2].value">
                      <el-checkbox label="1">一级</el-checkbox>
                      <el-checkbox label="2">二级</el-checkbox>
                      <el-checkbox label="3">三级</el-checkbox>
                      <el-checkbox label="4">四级</el-checkbox>
                    </el-checkbox-group>
                  </el-form-item>
                </div>
                <div class="parameter_row">
                  <el-form-item label="网格地址显示顺序">
                    <el-radio v-model="form.checkList[1].value" label="1">正序</el-radio>
                    <el-radio v-model="form.checkList[1].value" label="2">倒序</el-radio>
                  </el-form-item>
                </div>
                <div class="parameter_row">
                  <el-form-item label="任务计时">
                    <el-radio v-model="form.checkList[0].value" label="1">开启</el-radio>
                    <el-radio v-model="form.checkList[0].value" label="2">关闭</el-radio>
                  </el-form-item>
                </div>
              </el-form>
            </div>
            <!-- 处理按钮 -->
            <div class="btn">
              <el-button @click="reset">重置</el-button>
              <el-button @click="submit">保存</el-button>
            </div>
          </div>
        </div>
        <!-- 设备设施到期提醒配置 -->
        <div v-if="systemType == '1' && setType == 3" style="height: 100%">
          <div style="height: 100%">
            <div class="search-from">
              <div style="display: flex; margin-top: 12px">
                <el-button type="primary" class="rightBtn" icon="el-icon-plus" @click="onOpen('add')">新增</el-button>
              </div>
            </div>
            <div class="contentTable">
              <div class="contentTable-main table-content">
                <el-table v-loading="tableLoading" title="双击查看详情" border style="width: 100%" :data="tableData" height="100%" stripe>
                  <el-table-column type="index" label="序号" width="70">
                    <template slot-scope="scope">
                      <span>{{ (pagination.current - 1) * pagination.size + scope.$index + 1 }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="notificationScope" label="通知范围" show-overflow-tooltip>
                    <template slot-scope="{ row }"> {{ row.notificationScope == '0' ? '全部' : '自定义' }} </template>
                  </el-table-column>
                  <el-table-column prop="notificationType" label="通知类型" show-overflow-tooltip>
                    <template slot-scope="{ row }"> {{ row.notificationType == '0' ? '使用期限' : '' }} </template>
                  </el-table-column>
                  <el-table-column prop="expireNotificationType" :formatter="formatType" label="提醒终端" show-overflow-tooltip>
                    <!-- <template slot-scope="scope">
                      <span v-for="(tag, index) in scope.row.expireNotificationType.split(',')" :key="tag">{{ getTagName(tag) }}</span>
                    </template> -->
                  </el-table-column>
                  <el-table-column prop="remark" show-overflow-tooltip label="备注"></el-table-column>
                  <el-table-column prop="status" show-overflow-tooltip label="状态">
                    <template slot-scope="{ row }"> {{ row.status == '0' ? '启用' : '关闭' }} </template>
                  </el-table-column>
                  <el-table-column label="操作" fixed="right">
                    <template slot-scope="scope">
                      <el-button type="text" class="record" @click="onOpen('detail', scope.row)">查看</el-button>
                      <el-button type="text" @click="onOpen('edit', scope.row)">编辑</el-button>
                      <el-button type="text" class="delete" @click="deleteFn(scope.row)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div class="contentTable-footer">
                <el-pagination
                  :current-page="pageNo"
                  :page-sizes="[15, 20, 30, 40]"
                  :page-size="pageSize"
                  layout="total, sizes, prev, pager, next"
                  :total="total"
                  class="pagination"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                ></el-pagination>
              </div>
            </div>
          </div>
        </div>
        <!-- 上传图片水印设置 -->
        <div v-if="setType == 4" class="message_set">
          <div class="title_h">{{ systemType == '1' ? '设备巡检保养' : '巡查' }}上传图片水印设置</div>
          <div class="parameter_box">
            <el-form ref="form" label-width="120px">
              <div class="parameter_row">
                <el-form-item label="图片水印">
                  <el-radio-group v-model="watermarkFlag" @change="troubleshootChange($event, '3')">
                    <el-radio label="0">关闭</el-radio>
                    <el-radio label="1">开启</el-radio>
                  </el-radio-group>
                </el-form-item>
              </div>
            </el-form>
          </div>
          <!-- 处理按钮 -->
          <div class="btn">
            <el-button @click="reset">重置</el-button>
            <el-button @click="submit">保存</el-button>
          </div>
        </div>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin.js'
export default {
  mixins: [tableListMixin],
  // 前置路由守卫
  beforeRouteEnter(to, from, next) {
    const { path } = from
    if (path == '/systemSettings/parameter/addEquipment') {
      next((vm) => {
        vm.setType = '3'
      })
    } else {
      next()
    }
  },
  data() {
    return {
      systemType: this.$route.meta.type, // 系统标识,
      setType: '1',
      form: {
        checkList: ''
      },
      loading: false,
      data: [],
      tableLoading: false,
      tableData: [],
      pageNo: 1,
      pageSize: 15,
      total: 0,
      disabledFlag: '',
      informOptions: [
        {
          label: 'PC',
          value: '0'
        },
        {
          label: 'APP',
          value: '1'
        },
        {
          label: '短信',
          value: '2'
        },
        {
          label: '微信',
          value: '3'
        }
      ],
      annualInspectionList: [],
      watermarkFlag: '0'
    }
  },
  mounted() {
    this.getData()
    this.getConfigList()
  },
  methods: {
    formatType(row, column, cellValue) {
      let payTypeList = row.expireNotificationType.split(',')
      for (var i = 0; i < payTypeList.length; i++) {
        switch (payTypeList.length - i) {
          case 1: {
            payTypeList[i] = 'PC '
            break
          }
          case 2: {
            payTypeList[i] = 'APP '
            break
          }
          case 3: {
            payTypeList[i] = '短信 '
            break
          }
          case 4: {
            payTypeList[i] = '微信 '
            break
          }
        }
      }
      return payTypeList
    },
    getData() {
      this.loading = true
      this.form.checkList = ''
      this.data = []
      this.$api.getConfigurationListData({ systemIdentificationClassification: this.systemType }).then((res) => {
        this.loading = false
        if (res.code == 200) {
          this.form.checkList = res.data
          this.watermarkFlag = res.data[0].value || '0'
          // 综合巡检
          if (this.systemType == '2') {
            this.form.checkList[2].value = res.data[2].value.split(',') // 巡检任务设置空间巡检点显示层级
            // this.form.checkList[3].value = res.data[3].value.split(',') // 巡检消息提前提醒开关
            this.form.checkList[5].value = res.data[5].value.split(',') // 巡检消息提醒方式
          } else if (this.systemType == '1') {
            // 设备巡检
            // this.form.checkList[1].value = res.data[1].value.split(',') // 服务巡检消息提前提醒开关
            this.form.checkList[3].value = res.data[3].value.split(',') // 服务巡检消息信息提醒方式
            // this.form.checkList[5].value = res.data[5].value.split(',') // 设备巡检消息提前提醒开关
            this.form.checkList[7].value = res.data[7].value.split(',') // 设备巡检消息提醒方式
          }
        }
      })
    },
    troubleshootChange(val, str) {
      if (str == '3') {
        // 巡检及时提醒
        if (val == 2) {
          this.form.checkList[3].reminderInAdvance = 0
          this.form.checkList[3].advanceReminderInterval = 0
        } else {
          this.form.checkList[4].value = '2'
          this.form.checkList[3].reminderInAdvance = 10
          this.form.checkList[3].advanceReminderInterval = 1
        }
      } else if (str == '4') {
        // 设备巡检消息 及时提醒
        if ((val = 1)) {
          this.form.checkList[3].value = '2'
        }
      }
    },
    riskChange(val, str) {
      if (str == '5') {
        // 设备巡检消息 提前提醒
        if (val == 2) {
          this.form.checkList[5].reminderInAdvance = 0
          this.form.checkList[5].advanceReminderInterval = 0
        } else {
          this.form.checkList[6].value = '2'
          this.form.checkList[5].reminderInAdvance = 10
          this.form.checkList[5].advanceReminderInterval = 1
        }
      } else if (str == '6') {
        // 设备巡检消息 及时提醒
        if ((val = 1)) {
          this.form.checkList[5].value = '2'
        }
      } else if (str == '1') {
        // 设备保养消息 提前提醒
        if (val == 2) {
          this.form.checkList[1].reminderInAdvance = 0
          this.form.checkList[1].advanceReminderInterval = 0
        } else {
          this.form.checkList[2].value = '2'
          this.form.checkList[1].reminderInAdvance = 10
          this.form.checkList[1].advanceReminderInterval = 1
        }
      } else if (str == '2') {
        // 设备保养消息 及时提醒
        if ((val = 1)) {
          this.form.checkList[1].value = '2'
        }
      } else if (str == 'a1') {
        console.log(val, 'val')
        // 年检消息 提前提醒
        if (val == 2) {
          this.annualInspectionList[1].reminderInAdvance = 0
          this.annualInspectionList[1].advanceReminderInterval = 0
        } else {
          this.annualInspectionList[2].value = '2'
          this.annualInspectionList[1].reminderInAdvance = 10
          this.annualInspectionList[1].advanceReminderInterval = 1
        }
      } else if (str == 'a2') {
        // 年检消息 及时提醒
        if ((val = 1)) {
          this.annualInspectionList[1].value = '2'
          this.annualInspectionList[1].reminderInAdvance = 0
          this.annualInspectionList[1].advanceReminderInterval = 0
        }
      }
    },
    reset() {
      this.getData()
    },
    submit() {
      this.data = []
      const userInfo = this.$store.state.user.userInfo.user
      this.form.checkList.forEach((item) => {
        this.data.push(item)
      })
      if (this.setType == 4) {
        this.data[0].value = this.watermarkFlag
      } else {
        if (this.systemType == '2') {
          // 综合巡检
          this.data[2].value = this.data[2].value.join(',') // 巡检任务设置空间巡检点显示层级
          this.data[5].value = this.data[5].value.join(',') // 巡检消息提醒方式
        } else if (this.systemType == '1') {
          // 设备巡检
          this.data[3].value = this.data[3].value.join(',')
          this.data[7].value = this.data[7].value.join(',')
        }
      }
      this.data.forEach((item, i) => {
        this.data[i].unitCode = userInfo.unitCode ?? 'BJSYGJ'
        this.data[i].hospitalCode = userInfo.hospitalCode ?? 'BJSJTYY'
        this.data[i].systemIdentificationClassification = this.systemType
        this.data[i].reminderInAdvance = this.data[i].reminderInAdvance // 提前提醒
        this.data[i].advanceReminderInterval = this.data[i].advanceReminderInterval // 间隔时间
        if (item.value && typeof item.value == 'object') {
          this.data[i].value = item.value.join(',')
        }
      })
      this.$api.setConfigurationSave(JSON.stringify(this.data), { 'operation-id': 1 }).then((res) => {
        if (res.code == 200) {
          this.$message.success(res.message)
          this.getData()
          // location.reload();
        } else {
          this.$message.error(res.message)
          location.reload()
        }
      })
    },
    onOpen(type, val) {
      // 判断列表有没有全部通知范围类型
      if (type == 'add') {
        let flag = this.tableData.some((item) => item.notificationScope == 0)
        this.disabledFlag = this.tableData.some((item) => item.notificationType == 0)
        console.log(this.disabledFlag, 'this.disabledFlag')
        if (flag) return this.$message.error('请先删除全部通知范围类型!')
      }
      this.$router.push({
        name: 'addEquipment',
        query: {
          type,
          id: val ? val.id : '',
          disabledFlag: this.disabledFlag
        }
      })
    },
    // 删除
    deleteFn(row) {
      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$api.getDelConfig({ ids: row.id }).then((res) => {
            const { code } = res
            if (code == 200) {
              this.$message.success(res.message || '删除成功')
              this.getConfigList()
            } else {
              this.$message.error(res.message || '删除失败')
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    getConfigList() {
      let params = {
        current: this.pageNo,
        size: this.pageSize
      }
      this.$api.getConfigList(params).then((res) => {
        const { code } = res
        if (code == 200) {
          const { list, total } = res.data
          this.tableData = list
          this.total = total
        }
      })
    },
    handleSizeChange(val) {
      this.pageNo = 1
      this.pageSize = val
      this.getConfigList()
    },
    handleCurrentChange(val) {
      this.pageNo = val
      this.getConfigList()
    }
  }
}
</script>
<style lang="scss" scoped>
.role-content {
  height: 100%;
  display: flex;
  .role-content-left {
    width: 246px;
    height: 100%;
    padding: 10px;
    background: #fff;
    border-radius: 4px;
    margin-right: 12px;
    .left_content {
      height: calc(100% - 60px);
      overflow: auto;
      ul {
        padding: 0;
        li {
          height: 38px;
          width: 100%;
          font-size: 15px;
          font-family: 'PingFang SC-Regular', 'PingFang SC';
          font-weight: 400;
          color: #606266;
          line-height: 38px;
          list-style-type: none;
          box-sizing: border-box;
          cursor: pointer;
          padding-left: 27px;
          &:hover {
            background: rgb(53 98 219 / 20%);
            border-radius: 4px 0 0 4px;
          }
        }
      }
      .pitchOn {
        color: #3562db;
        background: linear-gradient(to right, #d9e1f8, #fff);
        font-weight: 500;
      }
    }
  }
  .role-content-right {
    width: calc(100% - 258px);
    height: 100%;
    padding: 10px 10px 20px 30px;
    background: #fff;
    border-radius: 4px;
    // flex: 1;
    .message_set {
      height: calc(100% - 40px);
      width: 100%;
      overflow: auto;
      .parameter_box {
        box-sizing: border-box;
        padding-left: 50px;
        .parameter_row {
          width: 100%;
          display: flex;
          align-items: center;
          .parameter_time {
            margin-left: 25px;
            display: flex;
            align-items: center;
            font-size: 14px;
            vertical-align: baseline;
          }
          .tiem_input {
            width: 50px;
          }
        }
      }
      .btn {
        position: absolute;
        bottom: 16px;
      }
    }
    .title_h {
      font-size: 16px;
      font-weight: 500;
      color: #121f3e;
      display: flex;
      align-items: center;
      margin: 15px 0;
    }
  }
}
.el-form-item {
  margin-bottom: 0 !important;
}
.search-from {
  padding-bottom: 12px;
  // display: flex;
  // justify-content: space-between;
  & > div {
    margin-right: 10px;
  }
  & > button {
    margin-top: 12px;
  }
}
.contentTable {
  height: calc(100% - 52px);
  display: flex;
  flex-direction: column;
  .contentTable-main {
    flex: 1;
    overflow: auto;
  }
  .contentTable-footer {
    padding: 10px 0 0;
  }
}
::v-deep .el-radio__original {
  display: none !important;
  /* 隐藏原生 radio 输入，但仍然允许交互 */
}
::v-deep.el-radio:focus:not(.is-focus):not(:active):not(.is-disabled) .el-radio__inner {
  box-shadow: none !important;
}
</style>
