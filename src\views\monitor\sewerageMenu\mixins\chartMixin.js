export default {
  methods: {
    // 横向柱状图
    transverseColumnChart(color, data = {}, title) {
      let { dataAll = [], yData = [] } = data
      let option
      if (yData && yData.length) {
        option = {
          backgroundColor: '#fff',
          grid: {
            top: '5%',
            left: '3%',
            bottom: '3%',
            right: '30',
            containLabel: true
          },
          // tooltip: {
          //   formatter: '{b} ({c})'
          // },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              label: {
                color: '#fff'
              },
              crossStyle: {
                color: '#999'
              }
            }
          },
          dataZoom: [
            {
              type: 'slider',
              show: true,
              // 设置组件控制的y轴
              yAxisIndex: 0,
              right: 4,
              start: 0,
              end: 20,
              width: 6,
              borderRadius: 0,
              borderColor: '#D7DEE8',
              fillerColor: '#C4C4C4', // 滑动块的颜色
              // backgroundColor: "#33384b", //两边未选中的滑动条区域的颜色
              // 是否显示detail，即拖拽时候显示详细数值信息
              showDetail: false,
              zoomLock: false,
              brushSelect: false,
              // 控制手柄的尺寸
              handleSize: 8,
              // 是否在 dataZoom-silder 组件中显示数据阴影。数据阴影可以简单地反应数据走势。
              showDataShadow: false,
              // filterMode: 'filter',
              handleIcon: 'M0,0 v11h3 v-11h-3 Z',
              handleStyle: {
                color: '#FFF',
                shadowOffsetX: 0, // 阴影偏移x轴多少
                shadowOffsetY: 0 // 阴影偏移y轴多少
                // borderCap: 'square',
                // borderColor: '#D8DFE9',
                // borderType: [15, 20],
              }
            },
            {
              type: 'inside',
              // show: false,
              yAxisIndex: [0],
              zoomOnMouseWheel: false, // 关闭滚轮缩放
              moveOnMouseWheel: true, // 开启滚轮平移
              moveOnMouseMove: true // 鼠标移动能触发数据窗口平移
            }
          ],
          xAxis: [
            {
              gridIndex: 0,
              axisTick: {
                show: false
              },
              axisLabel: {
                show: true,
                color: '#414653'
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: '#e4e9ed'
                }
              },
              axisLine: {
                show: false
              }
            }
          ],
          yAxis: [
            {
              axisLine: {
                show: false
              },
              gridIndex: 0,
              interval: 0,
              data: yData,
              axisTick: {
                show: false
              },
              axisLabel: {
                show: true,
                color: '#414653',
                textStyle: {
                  fontSize: 12
                },
                formatter: function (name) {
                  return name.length > 3 ? name.slice(0, 3) + '...' : name
                }
              },
              splitLine: {
                show: false
              }
            }
          ],
          series: [
            {
              name: title,
              type: 'bar',
              barWidth: '45%',
              itemStyle: {
                color: color
              },
              data: dataAll
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      return option
    },
    // 折柱混合图
    setBrokenLineColumnMixingChart(data = {}) {
      let { xData = [], barData = [], lineData = [], legendData = [] } = data
      let option
      if (xData && xData.length) {
        option = {
          backgroundColor: '#fff',
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              label: {
                color: '#fff'
              },
              crossStyle: {
                color: '#999'
              }
            }
          },
          grid: {
            top: '10%',
            left: '3%',
            right: '3%',
            bottom: '20',
            containLabel: true
          },
          legend: {
            top: '1%',
            itemGap: 50,
            data: legendData,
            textStyle: {
              color: '#121F3E'
            }
          },
          dataZoom: [
            {
              type: 'slider',
              show: true,
              xAxisIndex: [0],
              bottom: 5,
              start: 0,
              end: 20,
              height: 6,
              borderRadius: 0,
              borderColor: '#D7DEE8',
              fillerColor: '#C4C4C4', // 滑动块的颜色
              // backgroundColor: "#33384b", //两边未选中的滑动条区域的颜色
              // 是否显示detail，即拖拽时候显示详细数值信息
              showDetail: false,
              zoomLock: false,
              brushSelect: false,
              // 控制手柄的尺寸
              handleSize: 8,
              // 是否在 dataZoom-silder 组件中显示数据阴影。数据阴影可以简单地反应数据走势。
              showDataShadow: false,
              // filterMode: 'filter',
              handleIcon: 'M0,0 v11h3 v-11h-3 Z',
              handleStyle: {
                color: '#FFF',
                shadowOffsetX: 0, // 阴影偏移x轴多少
                shadowOffsetY: 0 // 阴影偏移y轴多少
                // borderCap: 'square',
                // borderColor: '#D8DFE9',
                // borderType: [15, 20],
              }
            },
            {
              type: 'inside',
              // show: false,
              xAxisIndex: [0],
              zoomOnMouseWheel: false, // 关闭滚轮缩放
              moveOnMouseWheel: true, // 开启滚轮平移
              moveOnMouseMove: true // 鼠标移动能触发数据窗口平移
            }
          ],
          xAxis: [
            {
              type: 'category',
              boundaryGap: true,
              axisLine: {
                show: false
              },
              axisLabel: {
                color: '#414653',
                textStyle: {
                  fontSize: 12
                },
                formatter: function (name) {
                  return name.length > 3 ? name.slice(0, 3) + '...' : name
                }
              },
              axisTick: {
                show: false
              },
              data: xData
            }
          ],
          yAxis: [
            {
              type: 'value',
              // name: '监测项数量',
              min: 0,
              max: Math.max(...barData, ...lineData),
              splitNumber: 7,
              splitLine: {
                show: true,
                lineStyle: {
                  color: '#eceff3'
                }
              },
              axisLine: {
                show: false
              },
              axisLabel: {
                margin: 20,
                formatter: '{value}',
                color: '#414653'
              },
              axisTick: {
                show: false
              }
            },
            {
              type: 'value',
              // name: '平均运行率',
              min: 0,
              max: Math.max(...barData, ...lineData),
              splitNumber: 7,
              splitLine: {
                show: true,
                lineStyle: {
                  color: '#eceff3'
                }
              },
              axisLine: {
                show: false
              },
              axisLabel: {
                formatter: '{value} %',
                margin: 20,
                color: '#414653'
              },
              axisTick: {
                show: false
              }
            }
          ],
          series: [
            {
              name: '监测项数量',
              type: 'bar',
              // barMinWidth: 10,
              // barMaxWidth: 20,
              barWidth: '45%',
              tooltip: {
                show: true,
                valueFormatter: (value) => value + ''
              },
              // label: {
              //   show: true,
              //   position: 'top',
              //   color: '#3562DB'
              // },
              itemStyle: {
                color: '#3562DB'
              },
              data: barData
            },
            {
              name: '平均运行率',
              type: 'line',
              // smooth: true, //是否平滑曲线显示
              symbol: 'circle', // 默认是空心圆（中间是白色的），改成实心圆
              showAllSymbol: true,
              symbolSize: 6,
              lineStyle: {
                color: '#FF9435', // 线条颜色
                borderColor: '#f0f'
              },
              // label: {
              //   show: true,
              //   position: 'top',
              //   color: '#FF9435',
              // },
              itemStyle: {
                color: '#FF9435'
              },
              tooltip: {
                show: true,
                valueFormatter: (value) => value + '%'
              },
              data: lineData
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      return option
    },
    // 饼图
    setPieChart(unit, data = [], title = []) {
      let color = ['#0E7CE2', '#FF8352', '#E271DE', '#F8456B', '#00FFFF', '#4AEAB0']
      let option
      if (data.length) {
        option = {
          backgroundColor: '#fff',
          color: color,
          // tooltip: {
          //   trigger: 'item'
          // },
          title: [],
          series: [
            {
              type: 'pie',
              radius: ['45%', '60%'],
              center: ['50%', '50%'],
              data: data,
              itemStyle: {
                borderColor: '#fff',
                borderWidth: 2
              },
              labelLine: {
                length: 20,
                length2: 30
              },
              label: {
                formatter: `{name|{b}}{value|{c}${unit}}{value|{d}%}`,
                // formatter: params => {
                //     return (
                //         '{name|' + params.name + '}{value|' + params.value + 'h}{value|'+ params.value +'%}'
                //     );
                // },
                padding: [0, -30, 15, -30],
                rich: {
                  name: {
                    fontSize: 12,
                    color: '#414653'
                  },
                  value: {
                    fontSize: 12,
                    padding: [0, 0, 0, 4],
                    color: '#414653'
                  }
                }
              }
            }
          ]
        }
        title.forEach((item, index) => {
          option.title.push({
            text: item,
            top: index == 0 ? '44%' : '50%',
            textAlign: 'center',
            left: '49%',
            textStyle: {
              color: '#121F3E',
              fontSize: 14,
              fontWeight: '500'
            }
          })
        })
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      return option
    }
  }
}
