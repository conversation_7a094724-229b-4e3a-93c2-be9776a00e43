<template>
    <PageContainer>
        <div slot="content" class="role-content" style="height: 100%">
            <div class="role-content-right">
                <div style="height: 100%">
                    <div class="search-from">
                        <div>
                            <el-input v-model.trim="seachFrom.nameorCodeorRoom" placeholder="网格名称"
                                style="width: 200px;margin-right: 10px;" clearable></el-input>
                            <el-select v-model="seachFrom.officeId" style="width: 200px;margin-right: 10px;"
                                placeholder="责任部门" filterable>
                                <el-option v-for="item in deptList" :key="item.id" :label="item.deptName"
                                    :value="item.id"> </el-option>
                            </el-select>
                            <el-date-picker v-model="seachFrom.datatime" type="daterange" range-separator="至"
                                start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd"
                                style="width: 240px;margin-right: 10px;">
                            </el-date-picker>
                            <el-button type="primary" plain @click="resetForm">重置</el-button>
                            <el-button type="primary" @click="searchForm">查询</el-button>
                        </div>
                        <div>
                            <el-button type="primary" @click="addKeyAreas('add')">新增重点区域</el-button>
                        </div>
                    </div>
                    <div class="contentTable">
                        <div class="contentTable-main table-content">
                            <el-table v-loading="tableLoading" border style="width: 100%" :data="tableData"
                                height="100%" stripe>
                                <el-table-column prop="areaName" label="网格名称" show-overflow-tooltip></el-table-column>
                                <el-table-column prop="throughCount" label="人员流量"
                                    show-overflow-tooltip></el-table-column>
                                <el-table-column prop="" label="设备状态" show-overflow-tooltip>
                                    <template slot-scope="scope">
                                        <span style="color: red;">{{ scope.row.abnormalCount }}</span>/
                                        <span>{{ scope.row.allCount }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="alarmCount" label="区域报警" show-overflow-tooltip></el-table-column>
                                <el-table-column prop="officeName" label="责任部门" show-overflow-tooltip></el-table-column>
                                <el-table-column prop="principalName" label="责任人"
                                    show-overflow-tooltip></el-table-column>
                                <el-table-column prop="phoneNo" label="责任人电话" show-overflow-tooltip></el-table-column>
                                <el-table-column prop="securityPrincipalName" label="安全责任人"
                                    show-overflow-tooltip></el-table-column>
                                <el-table-column prop="securityPhoneNo" label="安全责任人电话"
                                    show-overflow-tooltip></el-table-column>
                                <el-table-column prop="" label="人流监测设备" show-overflow-tooltip>
                                    <template slot-scope="scope">
                                        <el-tooltip v-if="scope.row.deviceNameList.length" effect="dark"
                                            placement="right">
                                            <div slot="content" class="custom-tooltip">
                                                <div v-for="(item, index) in scope.row.deviceNameList" :key="index">
                                                    {{ item }}</div>
                                            </div>
                                            <span>{{ scope.row.deviceNameList.length }}</span>
                                        </el-tooltip>
                                        <span v-else>0</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="num" label="包含空间" show-overflow-tooltip>
                                    <template slot-scope="scope">
                                        <span>{{ scope.row.spaceNameList ? scope.row.spaceNameList.join(',') : ''
                                            }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="num" label="操作" width="180">
                                    <template slot-scope="scope">
                                        <el-button type="text" class="record"
                                            @click="addKeyAreas('detail', scope.row.id)">查看</el-button>
                                        <el-button type="text" class="record"
                                            @click="addKeyAreas('edit', scope.row.id)">编辑</el-button>
                                        <el-button type="text" class="record"
                                            @click="deleteKeyAears(scope.row)">删除</el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                        <div class="contentTable-footer">
                            <el-pagination :current-page="pagination.current" :page-sizes="pagination.pageSizeOptions"
                                :page-size="pagination.size" :layout="pagination.layoutOptions"
                                :total="pagination.total" @size-change="paginationSizeChange"
                                @current-change="paginationCurrentChange">
                            </el-pagination>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </PageContainer>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin.js'
export default {
    name: 'keyAreaManagement',
    mixins: [tableListMixin,],
    beforeRouteEnter(to, from, next) {
        // 进入页面时，先将当前页面的 name 信息存入 keep-alive 全局状态（当前页面的name需要和路由的name一致）
        next((vm) => {
            // 二级页面存储当前级，多级页面存储多级
            vm.$store.commit('keepAlive/add', 'keyAreaManagement')
        })
    },
    async beforeRouteLeave(to, from, next) {
        // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
        if (!['addKeyAreas'].includes(to.name)) {
            // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
            await this.$store.commit('keepAlive/clean')
        }
        next()
    },
    data() {
        return {
            seachFrom: {
                nameorCodeorRoom: '', // 名称/编码/诊室,
                datatime: [],//日期
                officeId: '',//部门
            },
            deptList: [],
            tableLoading: false,
            tableData: [],
        }
    },
    mounted() {
        this.getDept()
        this.getDataList()
    },
    activated() {
        this.getDataList()
    },
    methods: {
        // 获取部门
        getDept() {
            this.$api.getSelectedDept({}).then((res) => {
                if (res.code == '200') {
                    this.deptList = res.data
                }
            })
        },
        getDataList() {
            let params = {
                areaName: this.seachFrom.nameorCodeorRoom,
                officeId: this.seachFrom.officeId,
                beginDate: this.seachFrom.datatime[0],
                endDate: this.seachFrom.datatime[1],
                pageNo: this.pagination.current,
                pageSize: this.pagination.size
            }
            this.tableLoading = true
            this.$api.keyAreasList(params).then((res) => {
                if (res.code == '200') {
                    this.tableData = res.data.list
                    this.pagination.total = parseInt(res.data.totalCount)
                } else {
                    this.$message.error(res.message)
                }
            })
            this.tableLoading = false
        },
        // 重置
        resetForm() {
            this.seachFrom.nameorCodeorRoom = ''
            this.seachFrom.datatime = []
            this.seachFrom.officeId = ''
            this.pagination.size = 15
            this.pagination.current = 1
            this.getDataList()
        },
        // 查询
        searchForm() {
            this.pagination.current = 1
            this.getDataList()
        },
        // 操作记录分页
        pagingLoad() {
            if (this.activities.length < this.drawerTotal) {
                this.drawerPageNo += 1
            }
        },
        deleteKeyAears(row) {
            this.$confirm('是否删除所选重点区域？', '提示', {
                cancelButtonClass: 'el-button--primary is-plain',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$api.deletekeyAreas({ id: row.id }).then((res) => {
                    if (res.code === '200') {
                        this.$message.success(res.message)
                        this.getDataList()
                    } else {
                        this.$message.error(res.message)
                    }
                })
            })

        },
        // 新增重点区域
        addKeyAreas(type, id) {
            this.$router.push(
                {
                    path: 'addKeyAreas',
                    query: {
                        type,
                        id,
                    }
                }
            )
        }
    }
}
</script>
<style lang="scss" scoped>
.el-tree-node__content {
    width: 100%;

    .custom-tree-node {
        display: inline-block;
        width: 100%;
        overflow: hidden;

        .item {
            display: inline-block;
            width: calc(100%);
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
        }
    }
}

::v-deep .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
    color: #3562db;
    background: linear-gradient(to right, #d9e1f8, #fff);
}

::v-deep .el-tree-node__content {
    height: 32px;
}

.role-content {
    height: 100%;
    display: flex;

    .role-content-right {
        height: 100%;
        min-width: 0;
        padding: 20px;
        background: #fff;
        border-radius: 4px;
        flex: 1;


        .search-from {
            padding-bottom: 12px;
            display: flex;
            justify-content: space-between;

            &>div {
                margin-right: 10px;
            }

            &>button {
                margin-top: 12px;
            }
        }

        .contentTable {
            height: calc(100% - 40px);
            display: flex;
            flex-direction: column;

            .contentTable-main {
                flex: 1;
                overflow: auto;
            }

            .contentTable-footer {
                padding: 10px 0 0;
            }
        }
    }

    .content {
        width: 100%;
        max-height: 500px !important;
        overflow: auto;
        background-color: #fff !important;
    }
}

::v-deep .el-drawer__body {
    background-color: #fff;
    margin: 20px;
    padding: 20px 10px;
    height: calc(100% - 80px);
    overflow-y: auto;
}

::v-deep .el-timeline-item__timestamp.is-bottom {
    font-size: 14px;
    position: absolute;
    left: -100px;
    top: -5px;
    font-weight: 600;
    color: #121f3e;
}

::v-deep .el-timeline {
    padding-left: 120px;
}

.timeContent {
    height: 100%;
    overflow: auto;

    .time {
        font-size: 14px;
        font-family: 'PingFang SC-Regular', 'PingFang SC';
        color: #414653;
    }

    .continer {
        display: flex;
        flex-wrap: wrap;

        .item {
            height: 32px;
            flex: 1;
            padding: 0 16px;
            background-color: #faf9fc;
            line-height: 32px;
            white-space: nowrap;
            margin-bottom: 20px;
            margin-right: 10px;
        }

        .itemContent {
            height: 32px;
            width: 220px;
            padding: 0 16px;
            background-color: #faf9fc;
            line-height: 32px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            cursor: pointer;
            margin-right: 10px;
        }
    }
}

::v-deep .el-timeline-item__node--normal {
    background: #fff;
    border: 2px solid #3562db;
}

.noData {
    display: inline-block;
    padding-bottom: 10px;
    width: 100%;
    margin: 0;
    font-size: 14px;
    color: #999;
    text-align: center;
}

.record {
    color: #66b1ff !important;
}

.delete {
    color: red !important;
}

::v-deep .el-tree-node {
    white-space: normal;
}

.rightBtn {
    height: 36px;
    margin: 0;
    margin-right: 10px;
}

.leadFile {
    display: flex;
}

.leadFile_item {
    margin: 10px 35px;
    color: #66b1ff;
    cursor: pointer;
}

::v-deep .el-message-box.no-close-btn .el-message-box__headerbtn {
    display: none;
}
</style>