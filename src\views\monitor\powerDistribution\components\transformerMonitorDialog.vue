<!-- 变压器监测弹窗 -->
<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    title="变压器监测"
    width="60%"
    :visible.sync="visible"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <div class="content">
      <el-tabs v-model="tabsActive" class="heade-tabs" @tab-click="tabClick">
        <el-tab-pane v-for="item in monitorEntityList" :key="item.surveyEntityCode" :label="item.surveyEntityName" :name="item.surveyEntityCode"></el-tab-pane>
      </el-tabs>
      <div class="transformer-main">
        <div class="main-left">
          <echarts ref="leftChart" domId="leftChart" />
        </div>
        <div v-if="selectmonitorEntity.parameterList.length" class="main-right">
          <div v-for="item in selectmonitorEntity.parameterList" :key="item.parameterName" class="right-item">
            <p class="item-name">{{ item.parameterName }}</p>
            <p class="item-value">{{ (item.parameterValue || '-') + (item.parameterUnit || '') }}</p>
          </div>
        </div>
        <div v-else style="width: 100%; margin-left: calc(30% + 10px);">
          <el-empty></el-empty>
        </div>
      </div>
      <div v-loading="loading" class="content-main">
        <ContentCard class="content-right" >
          <div slot="content" class="card-content">
            <div class="card-content-head">
              <!-- <el-select v-model="searchFrom.typeCode" placeholder="请选择设备" clearable @change="getDataList"> -->
              <el-select v-model="searchFrom.typeCode" placeholder="请选择" @change="getDataList">
                <el-option v-for="item in typeList" :key="item.paramId" :label="item.paramName" :value="item.paramId"> </el-option>
              </el-select>
              <el-date-picker
                v-model="searchFrom.date"
                type="date"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                :picker-options="pickerOptions"
                :clearable="false"
              />
              <div style="display: inline-block;">
                <el-button type="primary" plain @click="resetForm">重置</el-button>
                <el-button type="primary" @click="searchForm">查询</el-button>
              </div>
              <div class="heade-pattern">
                <div class="pattern-item" @click="switchPattern(1)">
                  <svg-icon :name="currentPattern == 1 ? 'chartModeActive' : 'chartMode'" class="pattern-icon" />
                  <span :style="{ color: currentPattern == 1 ? '#3562DB' : '#414653' }">图形模式</span>
                </div>
                <div class="pattern-item" @click="switchPattern(2)">
                  <svg-icon :name="currentPattern == 2 ? 'listModeActive' : 'listMode'" class="pattern-icon" />
                  <span :style="{ color: currentPattern == 2 ? '#3562DB' : '#414653' }">列表模式</span>
                </div>
              </div>
            </div>
            <div v-show="currentPattern == 2" class="card-content-table table-content">
              <el-table ref="table" :resizable="false" border :data="tableData" :height="tableHeight" style="width: 100%;">
                <el-table-column v-for="item in tableColumn" :key="item.prop" :prop="item.prop" :label="item.label" show-overflow-tooltip></el-table-column>
              </el-table>
            </div>
            <div v-show="currentPattern == 1" class="card-content-chart">
              <echarts ref="rightChart" domId="rightChart" />
            </div>
          </div>
        </ContentCard>
      </div>
    </div>
    <span slot="footer">
      <el-button plain type="primary" @click="closeDialog">关 闭</el-button>
    </span>
  </el-dialog>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin.js'
import moment from 'moment'
moment.locale('zh-cn')
export default {
  name: 'alarmStatisticsDialog',
  mixins: [tableListMixin],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    requestInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      tabsActive: '',
      currentPattern: 2, // 当前模式
      loading: false,
      tableData: [],
      tableColumn: [],
      searchFrom: {
        typeCode: '', // 类型
        date: moment().format('YYYY-MM-DD') // 时间
      },
      pickerOptions: {
        shortcuts: [
          {
            text: '今日',
            onClick(picker) {
              picker.$emit('pick', moment().format('YYYY-MM-DD'))
            }
          }
        //   {
        //     text: '本月',
        //     onClick(picker) {
        //       picker.$emit('pick', [moment().startOf('month').format('YYYY-MM-DD'), moment().endOf('month').format('YYYY-MM-DD')])
        //     }
        //   },
        //   {
        //     text: '本年',
        //     onClick(picker) {
        //       picker.$emit('pick', [moment().startOf('year').format('YYYY-MM-DD'), moment().endOf('year').format('YYYY-MM-DD')])
        //     }
        //   }
        ]
      },
      monitorEntityList: [],
      selectmonitorEntity: {
        parameterList: []
      },
      typeList: [] // 类型列表
    }
  },
  computed: {},
  created() {
    this.$nextTick(() => {
      this.getMonitorEntity()
    })
  },
  mounted() {},
  methods: {
    echartsResize() {
      setTimeout(() => {
        if (this.$refs.rightChart) {
          this.$refs.rightChart.chartResize()
        }
      }, 200)
    },
    // 图形模式
    lineChartData(data, paramList) {
      let option
      let colorArr = [
        '255, 100, 97',
        '255, 148, 53',
        '53, 98, 219',
        '74, 206, 40',
        '12, 166, 237',
        '255, 190, 0'
      ]
      if (data.length) {
        option = {
          tooltip: {
            trigger: 'axis'
          },
          legend: {
            x: 'center',
            y: '20px',
            textStyle: {
              color: '#414653',
              fontSize: 12
            },
            data: paramList.map(item => item.paramName)
          },
          grid: {
            right: '3%',
            bottom: '3%',
            left: '3%',
            top: '60px',
            containLabel: true
          },
          dataZoom: [
            {
              type: 'inside',
              start: 0,
              end: 100
            }
          ],
          xAxis: [
            {
              type: 'time',
              axisLine: {
                lineStyle: {
                  color: '#E7EAEE'
                }
              },
              axisTick: {
                show: false
              },
              axisLabel: {
                show: true,
                textStyle: {
                  color: '#414653',
                  fontSize: 12
                }
              }
            }
          ],
          yAxis: [
            {
              type: 'value',
              position: 'left',
              axisLine: {
                show: false
              },
              splitLine: {
                lineStyle: {
                  color: '#E7EAEE'
                }
              },
              axisTick: {
                show: false
              },
              axisLabel: {
                color: '#414653',
                fontSize: 12
              }
            }
          ],
          series: []
        }
        let newArr = []
        paramList.forEach((item, index) => {
          newArr.push(
            {
              name: item.paramName,
              type: 'line',
              yAxisIndex: 0,
              symbolSize: 5,
              itemStyle: {
                normal: {
                  color: `rgba(${colorArr[index]}, 1)`
                }
              },
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                  {
                    offset: 0,
                    color: `rgba(${colorArr[index]}, 0)`
                  },
                  {
                    offset: 1,
                    color: `rgba(${colorArr[index]}, .3)`
                  }
                ])
              },
              data: data.map((v) => [v.collectTime, v[item.paramId]])
            }
          )
        })
        option.series = newArr
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      return option
    },
    queryElectMonitoCondition() {
      this.$api.queryParamListBySurveyCode({surveyCode: this.tabsActive}).then(res => {
        if (res.code == 200) {
          this.typeList = res.data
          this.searchFrom.typeCode = res.data[0].paramId
          this.getDataList()
        }
      })
    },
    // 查看详情
    getDataList() {
      let paramArr = this.typeList.find(item => item.paramId == this.searchFrom.typeCode).paramObj
      let params = {
        startTime: this.searchFrom.date,
        surveyCode: this.tabsActive,
        list: paramArr
      }
      let newArr = [
        {
          label: '设备名称',
          prop: 'surveyName'
        },
        {
          label: '采集时间',
          prop: 'collectTime'
        }
      ]
      this.$api.SelectEntityDateList(params).then((res) => {
        if (res.code == 200) {
          res.data.forEach(item => {
            item.list.forEach(v => {
              item[v.paramId] = v.avg
            })
          })
          if (this.currentPattern == 1) { // 图形模式
            this.$refs.rightChart.init(this.lineChartData(res.data, paramArr))
            this.echartsResize()
          } else if (this.currentPattern == 2) { // 列表模式
            paramArr.forEach(item => {
              newArr.push({
                label: item.paramName + (item.paramUnit ? '（' + item.paramUnit + '）' : ''),
                prop: item.paramId.toString()
              })
            })
            this.tableColumn = newArr
            this.tableData = res.data
          }
        }
      })
    },
    // 模式切换
    switchPattern(type) {
      if (this.currentPattern != type) {
        this.currentPattern = type
        this.getDataList()
      }
    },
    // tab切换
    tabClick(tab) {
      this.selectmonitorEntity = this.monitorEntityList.find(item => item.surveyEntityCode == tab.name) || {parameterList: []}
      this.$refs.leftChart.init(this.transformerChartData(this.selectmonitorEntity.parameterList.find(item => item.parameterName.indexOf('负载') != -1)?.parameterValue ?? 0))
      this.$refs.leftChart.chartResize()
      this.queryElectMonitoCondition()
    },
    // 获取检测实体列表
    getMonitorEntity() {
      let params = {
        ...this.requestInfo,
        page: 1,
        pageSize: 99
      }
      this.$api.GetRealMonitoringListSecurity(params).then((res) => {
        if (res.code == 200) {
          this.monitorEntityList = res.data.list
          this.tabsActive = res.data.list[0]?.surveyEntityCode
          this.tabClick({name: res.data.list[0]?.surveyEntityCode ?? ''})
        }
      })
    },
    transformerChartData(value) {
      let option
      option = {
        backgroundColor: '#FAF9FC',
        series: [
          {
            type: 'gauge',
            startAngle: 225,
            endAngle: -45,
            min: 0,
            max: 100,
            radius: '99%',
            axisLine: {
              show: true,
              lineStyle: {
                color: '#999'
              }
            },
            title: {
              show: true,
              offsetCenter: [0, '105%'],
              textStyle: {
                color: '#414653',
                fontSize: 14
              }
            },
            detail: {
              show: true,
              offsetCenter: [0, '70%'],
              formatter: function (value) {
                return value + '%'
              },
              fontSize: 18,
              fontWeight: 600
            },
            axisTick: {
              show: false
            },
            splitLine: {
              length: 5,
              lineStyle: {
                width: 1,
                color: '#B9B8BB'
              }
            },
            axisLabel: {
              color: '#B9B8BB',
              fontSize: 10
            },
            pointer: {
              show: true,
              width: 3,
              length: '50%'
            },
            itemStyle: {
              color: '#3562DB'
            },
            markPoint: {
              animation: false,
              silent: true,
              data: [
                {
                  x: '50%',
                  y: '50%',
                  symbol: 'circle',
                  symbolSize: 12,
                  itemStyle: {
                    color: '#3562DB'
                  }
                },
                {
                  x: '50%',
                  y: '50%',
                  symbol: 'circle',
                  symbolSize: 3,
                  itemStyle: {
                    color: '#fff'
                  }
                }
              ]
            },
            data: [
              {
                name: '负载率',
                value: value
              }
            ]
          },
          {
            type: 'gauge',
            radius: '86%',
            startAngle: 225,
            endAngle: -45,
            min: 0,
            max: 100,
            detail: {
              show: false
            },
            axisLine: {
              show: true,
              lineStyle: {
                width: 5,
                color: [
                  [value / 100, '#3562DB'],
                  [1, 'RGBA(53, 98, 219, .1)']
                ]
              }
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false
            },
            axisLabel: {
              show: false
            },
            pointer: {
              show: false
            }
          }
        ]
      }
      return option
    },
    // 重置查询
    resetForm() {
      Object.assign(this.$data.searchFrom, this.$options.data().searchFrom)
      this.searchFrom.typeCode = this.typeList[0].paramId
      this.searchForm()
    },
    // 查询
    searchForm() {
      this.getDataList()
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', !this.visible)
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 0;
  max-height: calc(88vh - 110px);
}

.content {
  width: 100%;
  height: 100%;

  :deep(.heade-tabs) {
    background: #fff;
    border-radius: 4px;

    .el-tabs__header .el-tabs__nav-wrap::after {
      display: none;
    }

    .el-tabs__active-bar {
      bottom: 1px;
    }

    .el-tabs__item {
      padding: 0 20px !important;
    }

    .el-tabs__content {
      display: none;
    }
  }

  .transformer-main {
    display: flex;
    height: 300px;
    // width: calc(100% - 48px);
    background: #fff;
    margin: 24px;
    padding: 16px;
    position: relative;

    p {
      margin: 0;
    }

    .main-left {
      width: calc(20% - 10px);
      height: calc(100% - 32px);
      position: absolute;
      bottom: 16px;
      left: 16px;
    }

    .main-right {
      width: 100%;
      height: 100%;
      overflow: auto;
      display: flex;
      flex-wrap: wrap;
      align-content: flex-start;
      margin-left: calc(20% + 10px);

      .right-item {
        padding: 16px;
        width: calc(100% / 4);

        .item-name {
          font-size: 14px;
          font-weight: 400;
          color: #7f848c;
          line-height: 14px;
        }

        .item-value {
          margin-top: 10px;
          font-size: 14px;
          font-weight: 400;
          color: #121f3e;
          line-height: 14px;
        }
      }
    }
  }

  .content-main {
    width: 100%;
    padding: 0 24px 24px;

    :deep(.box-card) {
      .card-title {
        display: none;
      }
    }
  }

  .content-left {
    height: 415px;
    width: 100%;

    .cardContent-left {
      display: flex;
      flex-direction: column;
      justify-content: center;
      width: 35%;
      height: 100%;
      padding-right: 16px;

      p {
        margin: 0;
      }

      .left-item {
        padding: 14px 22px;
        background: #faf9fc;
        border-radius: 4px;
        margin-bottom: 7px;
        position: relative;

        .item-title {
          font-size: 15px;
          font-weight: 500;
          color: #121f3e;
        }

        .item-value {
          margin-top: 4px;
          font-size: 30px;

          & > span {
            font-size: 15px;
            font-weight: 500;
            color: #ccced3;
          }
        }

        .item-icon {
          position: absolute;
          right: 22px;
          bottom: 14px;
          width: 40px;
          height: 40px;
        }
      }

      & :last-child {
        margin-bottom: 0;
      }
    }
  }

  .content-right {
    height: 500px;
    width: 100%;
    margin-top: 16px;

    .card-content {
      height: 100%;
      padding: 0 0 0 4px;
      display: flex;
      flex-direction: column;

      .card-content-head {
        position: relative;
        padding-right: 200px;

        & > div {
          margin-right: 10px;
        }

        .heade-pattern {
          display: flex;
          position: absolute;
          right: 16px;
          top: 50%;
          margin: 0 !important;
          transform: translateY(-50%);

          .pattern-item {
            cursor: pointer;
            font-size: 15px;

            .pattern-icon {
              font-size: 16px;
              margin-right: 6px;
            }
          }

          .pattern-item:last-child {
            margin-left: 16px;
          }
        }
      }

      .card-content-table {
        flex: 1;
        overflow: auto;
        margin-top: 16px;

        ::v-deep .el-progress {
          .el-progress-bar__outer {
            border-radius: 0;
            height: 8px !important;
          }

          .el-progress-bar__inner {
            border-radius: 0;
          }
        }

        .alarmLevel {
          padding: 3px 6px;
          border-radius: 4px;
          color: #fff;
          line-height: 14px;
        }

        .alarmStatus {
          position: relative;
          display: inline-block;
          padding-left: 12px;

          .alarmStatusIcon {
            display: inline-block;
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            border-radius: 100%;
          }
        }
      }

      .card-content-footer {
        padding: 10px 0 0;
      }

      .card-content-chart {
        flex: 1;
      }
    }
  }
}

::v-deep .model-dialog {
  padding: 0 !important;
  margin-top: 8vh !important;
}
</style>
