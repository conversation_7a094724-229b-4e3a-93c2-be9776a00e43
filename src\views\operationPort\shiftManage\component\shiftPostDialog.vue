<template>
  <el-dialog v-if="visible" v-dialogDrag title="选择值班岗" width="40%" :visible.sync="dialogShow"
    custom-class="model-dialog" :before-close="closeDialog">
    <div class="sapce_content">
      <div v-loading="treeLoading" class="center">
        <el-input v-model.trim="searchForm.keyWord" placeholder="请输入关键字" clearable suffix-icon="el-icon-search"
          @input="nameInput"></el-input>
        <el-checkbox-group v-model="shiftPostSelect" @change="changegroup">
          <el-checkbox v-for="item in shiftPostBakData" :key="item.dutyPostCode" :label="item.dutyPostCode"
            style="display: block" @change="(val) => checkboxchange(item, val)">
            <div class="personCheck">
              <div class="info">
                <div class="name">{{ item.dutyPostName }}</div>
              </div>
            </div>
          </el-checkbox>
        </el-checkbox-group>
      </div>
      <div class="right">
        <div class="top">
          <span>
            <span class="label">已选:</span>
            <span class="num">{{ selectData.length ? selectData.length : 0 }}</span>
            <span class="dept">个</span>
          </span>
          <span class="clear" @click="clear">清空</span>
        </div>
        <div v-for="(item, index) in selectData" :key="index" class="item-list">
          <div style="display: flex">
            <div class="info">
              <div class="name">{{ item.dutyPostName }}</div>
            </div>
          </div>
          <div class="remove" @click="remove(item, index)"><i class="el-icon-close"></i></div>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="submit">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  name: 'shiftPostDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    /*
      是否为单选
      checkbox 多选
      radio 单选
    */
    checkType: {
      type: String,
      default: 'checkbox'
    },
    defaultChecked: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      defaultProps: {
        label: 'deptName',
        children: 'list'
      },
      treeLoading: false,
      selectData: [],
      shiftPostData: [], // 人员列表
      shiftPostBakData: [],
      shiftPostSelect: [],
      searchForm: {
        keyWord: ''
      }
    }
  },
  computed: {
    dialogShow() {
      return this.visible
    }
  },
  mounted() {
    this.getShiftPostList('init')
  },
  methods: {
    //  获取值班岗列表
    getShiftPostList(type) {
      this.treeLoading = true
      this.$api.supplierAssess.getDutyPostData().then((res) => {
        this.treeLoading = false
        if (res.code == '200' && res.data.length > 0) {
          this.shiftPostData = res.data
          this.shiftPostBakData = JSON.parse(JSON.stringify(res.data))
        }
        if (this.defaultChecked.length && type == 'init') {
          this.shiftPostSelect = this.defaultChecked
          this.selectData = this.shiftPostData.filter((item) => {
            return this.defaultChecked.includes(item.dutyPostCode)
          })
        }
      })
    },
    nameInput(val) {
      if (val) {
        this.shiftPostBakData = this.shiftPostData.filter((e) => e.dutyPostName.includes(val))
      } else {
        this.shiftPostBakData = JSON.parse(JSON.stringify(this.shiftPostData))
      }
    },
    // 控制单选
    changegroup(list) {
      if (this.checkType == 'radio') {
        this.shiftPostSelect = [list[list.length - 1]]
        this.selectData = this.shiftPostData.filter((el) => el.dutyPostCode == this.shiftPostSelect[0])
      }
    },
    // 选择
    checkboxchange(item, val) {
      if (!val) {
        this.selectData = this.selectData.filter((el) => {
          return el.dutyPostCode !== item.dutyPostCode
        })
      } else {
        if (this.selectData.includes(item)) {
          return
        } else {
          this.selectData.push(item)
        }
      }
    },
    // 移除
    remove(list, index) {
      this.selectData.splice(index, 1)
      this.shiftPostSelect = this.selectData.map((item) => {
        return item.dutyPostCode
      })
    },
    // 清空
    clear() {
      this.selectData = []
      this.shiftPostSelect = []
    },
    closeDialog() {
      this.$emit('closeDialog')
    },
    submit() {
      if (this.selectData.length < 1) {
        this.$message({
          message: '请选择至少一条数据',
          type: 'warning'
        })
      } else {
        this.$emit('submitDialog', this.selectData)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.model-dialog {
  width: 40% !important;

  .sapce_content {
    margin: 0 auto;
    background: #fff;
    padding: 10px 0;
    border-radius: 4px;
    height: 400px;
    display: flex;
    width: 100%;

    .center {
      width: 300px;
      background-color: #fff;
      height: 100%;
      margin-left: 10px;
      border-radius: 5px;
      padding-left: 16px;
      overflow: scroll;

      .personCheck {
        width: 100%;
        display: flex;
        padding: 10px 0;

        img {
          vertical-align: middle;
        }
      }
    }

    .right {
      width: calc(100% - 320px);
      border-left: 1px solid #dcdfe6;
      background-color: #fff;
      height: 100%;
      margin-left: 10px;
      border-radius: 5px;
      padding-left: 16px;
      overflow: scroll;

      .top {
        display: flex;
        padding: 0 12px;
        justify-content: space-between;

        .label,
        .dept {
          font-size: 14px;
          color: #7f848c;
        }

        .num {
          font-size: 14px;
          color: #333333;
          margin-left: 10px;
        }

        .dept {
          margin-left: 10px;
        }

        .clear {
          font-size: 12px;
          color: #3562db;
          cursor: pointer;
        }
      }

      .item-list {
        display: flex;
        cursor: pointer;
        padding: 0 12px;
        justify-content: space-between;
        margin-top: 8px;

        .remove {
          margin: auto 0;
        }
      }

      .item-list:hover {
        background: #e6effc;
      }
    }

    .info {
      margin-left: 8px;

      .name {
        font-weight: 500;
        color: #333333;
      }

      .mobile {
        font-size: 12px;
        color: #7f848c;
      }
    }
  }
}

::v-deep .el-checkbox__input {
  display: inline-block !important;
}
</style>
