<template>
  <div class="alarmList">
    <el-checkbox-group v-if="listData.length" v-model="selectList" @change="change">
      <el-row v-for="(list, index) in getListData" :key="index" :gutter="type == 'shielded' ? 16 : 0">
        <el-col v-for="item in list" :key="item.id" :xs="24" :lg="type == 'shielded' ? 12 : 24">
          <div :id="type + item.alarmId" class="alarm-item" :style="{ background: selectList.includes(item.id) ? 'rgba(53,98,219,0.1)' : '#faf9fc' }">
            <el-checkbox :label="item.id">
              <div class="alarm-heade">
                <div class="heade-left">
                  <p class="alarm-name">{{ item.alarmObjectName }}</p>
                  <p class="alarm-type" :style="{ background: alarmLevelItem[item.alarmLevel].color }">
                    {{ alarmLevelItem[item.alarmLevel].text }}
                  </p>
                  <p class="alarm-message" :style="{ color: alarmLevelItem[item.alarmLevel].color }">{{ item.alarmDetails || '-' }}</p>
                </div>
                <div v-if="type != 'unhandled'" class="heade-right">
                  <p v-if="type == 'shielded'" class="shieldedTime">屏蔽至 {{ item.shieldTime || '-' }}</p>
                  <p v-if="type == 'shielded'" class="cancelShielded" @click.stop.prevent="$emit('operating', 'shielded', item)">取消屏蔽</p>
                  <p v-if="type == 'processing'" class="shieldedTime">关联工单：{{ item.workCount || '-' }}</p>
                  <p v-if="type == 'processing'" class="cancelShielded" @click.stop.prevent="handleCommand('workOrderDetails', item)">
                    class="el-icon-arrow-right"></i></p>
                  <p v-if="item.alarmAffirm != 0" class="alarmType">
                    <img v-if="item.alarmAffirm == 2" src="@/assets/images/alarmCenter/wubao_icon.png" alt="误报" />
                    <img v-else-if="item.alarmAffirm == 4" src="@/assets/images/alarmCenter/tiaoshi_icon.png" alt="调试" />
                    <img v-else-if="item.alarmAffirm == 3" src="@/assets/images/alarmCenter/yanlian_icon.png" alt="演练" />
                    <img v-else-if="item.alarmAffirm == 1" src="@/assets/images/alarmCenter/zhenshi_icon.png" alt="真实报警·" />
                  </p>
                </div>
              </div>
              <p class="alarm-info" style="margin-top: 12px">
                <svg-icon name="clock_icon" class="clockIcon" />
                <span>{{ item.alarmStartTime || '-' }}</span>
              </p>
              <p class="alarm-info" style="margin-top: 8px">
                <svg-icon name="place_icon" class="clockIcon" />
                <span>{{ item.alarmSpaceName || '-' }}</span>
              </p>
              <div class="btnBox">
                <!-- unhandled 未处理 processing 处理中 -->
                <p v-if="!item.workNum" class="clickBtn" @click.stop.prevent="handleCommand('dispatch', item)">派单</p>
                <p v-if="![1, 2, 3, 4].includes(item.alarmAffirm)" class="clickBtn" @click.stop.prevent="handleCommand('confirmAlarm', item)">确警</p>
                <el-dropdown trigger="click" @command="(val) => handleCommand(val, item)">
                  <p class="clickBtn moreBtn" @click.stop.prevent>···</p>
                  <el-dropdown-menu slot="dropdown" class="dropdownSelect">
                    <el-dropdown-item command="remark">备注</el-dropdown-item>
                    <el-dropdown-item v-if="type != 'shielded'" command="shielded">屏蔽</el-dropdown-item>
                    <el-dropdown-item v-if="item.alarmStatus != 2" command="close">关闭</el-dropdown-item>
                    <el-dropdown-item command="alarmDetails">查看</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </el-checkbox>
          </div>
        </el-col>
      </el-row>
    </el-checkbox-group>
    <p v-else class="noData">暂无数据</p>
  </div>
</template>
<script>
import { arrTrans } from '@/util'
export default {
  name: 'alarmList',
  props: {
    type: {
      type: String,
      default: 'shielded' // shielded（已屏蔽） unhandled（未处理）processing（处理中）
    },
    listData: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      selectList: [],
      alarmLevelItem: {
        0: { text: '通知', color: '#3562DB' },
        1: { text: '一般', color: '#3562DB' },
        2: { text: '紧急', color: '#FF9435' },
        3: { text: '重要', color: '#FA403C' }
      }
    }
  },
  computed: {
    getListData() {
      return arrTrans(2, this.listData)
    }
  },
  created() {},
  methods: {
    handleCommand(command, item) {
      // dispatch(派单) confirmAlarm(确警) remark(备注) shielded(屏蔽) close(关闭) viewDetails(查看详情)
      this.$emit('operating', command, item)
    },
    change(data) {
      this.$emit('selectChange', this.type, data)
    }
  }
}
</script>
<style lang="scss" scoped>
.alarmList {
  width: 100%;
  height: 100%;
  position: relative;
  .noData {
    margin: 0;
    font-size: 14px;
    color: #999;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .alarm-item {
    position: relative;
    padding: 16px;
    border-radius: 4px;
    margin-bottom: 12px;
    overflow: hidden;
    border: 2px solid #fff;
    p {
      margin: 0;
    }
    .alarm-heade {
      display: flex;
      justify-content: space-between;
      .heade-left {
        display: flex;
        align-items: center;
        font-size: 14px;
        font-weight: 500;
        overflow: hidden;
        .alarm-name {
          font-size: 16px;
          color: #121f3e;
          line-height: 16px;
        }
        .alarm-type {
          margin-left: 4px;
          padding: 3px 6px;
          border-radius: 4px;
          color: #fff;
          line-height: 14px;
        }
        .alarm-message {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin-left: 4px;
          line-height: 14px;
        }
      }
      .heade-right {
        display: flex;
        flex-shrink: 0;
        align-items: center;
        line-height: 14px;
        font-size: 14px;
        .shieldedTime {
          color: #121f3e;
        }
        .cancelShielded {
          color: #3562db;
          margin-left: 4px;
          cursor: pointer;
        }
        .alarmType {
          width: 18px;
          height: 18px;
          margin-left: 8px;
          img {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
    .alarm-info {
      display: flex;
      align-items: center;
      color: #414653;
      span {
        display: inline-block;
        margin-left: 5px;
        /* line-height: 16px; */
      }
      .clockIcon {
        font-size: 15px;
      }
    }
    .btnBox {
      position: absolute;
      display: flex;
      right: 0;
      bottom: 0;
      font-size: 14px;
      .clickBtn {
        color: #3562db;
        width: 48px;
        text-align: center;
        line-height: 24px;
        background: rgb(53 98 219 / 20%);
        border-radius: 4px;
        margin-left: 8px;
      }
      .moreBtn {
        letter-spacing: 2px;
        font-size: 25px;
        padding-left: 2px;
      }
    }
  }
  .alarmShine {
    animation: alarmShine 1.7s linear infinite;
  }
  @keyframes alarmShine {
    50% {
      border-color: #ff6969;
      background-color: #ffdada;
    }
  }
}
</style>
