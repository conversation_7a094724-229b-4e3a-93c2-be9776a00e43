<template>
  <div style="height: 100%;">
    <div class="inner">
      <div class="content_box" :class="{ info: readonly }">
        <div class="topList">
          <div v-if="query.type !== 'approval' && query.type !== 'button'" class="topTitle">
            <div class="line"></div>
            <span>基本信息</span>
          </div>
          <el-form ref="formInline" :model="formInline" :inline="true" :rules="rules" label-width="100px">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="计划名称:">
                  <span style="color: #606266;">{{ formInline.planName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="计划类型:">
                  <span>{{ detailsInfo.planTypeName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="执行方式:" prop="executeMode">
                  <span>{{ executeModeList.find(i => i.executeMode == formInline.executeMode).name }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <!-- 查看部门/班组时无按钮 -->
                <el-form-item
                  v-if="formInline.executeMode == '1' && (query.type == 'view' || query.type == 'approval' || query.type == 'button')"
                  label="小组:"
                  prop="equipmentTypeId"
                  class="itemLabel executeModes"
                  style="height: 50px;"
                >
                  <div v-if="formInline.id && formInline.executeMode == '1'" style="display: flex; margin-bottom: 20px;">
                    <el-tooltip class="item" effect="dark" placement="top-start">
                      <div slot="content">{{ distributionTeamName }}</div>
                      <div style="white-space: nowrap; overflow: hidden; cursor: pointer;">
                        {{ distributionTeamName }}
                      </div>
                    </el-tooltip>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <!-- 查看人员时无按钮 light dark-->
                <el-form-item
                  v-if="formInline.executeMode !== '1' && (query.type == 'view' || query.type == 'approval' || query.type == 'button')"
                  label="人员:"
                  prop="equipmentTypeId"
                  class="itemLabel executeModes"
                  style="width: 100%;"
                >
                  <div v-if="formInline.id" style=" color: #606266; width: 100%;">
                    <!-- {{distributionPeopleName}} -->
                    <el-tooltip class="item" effect="dark" placement="top-start">
                      <div slot="content">{{ distributionPeopleName }}</div>
                      <div style="white-space: nowrap; overflow: hidden; cursor: pointer;">
                        {{ distributionPeopleName }}
                      </div>
                    </el-tooltip>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="周期类型:" prop="cycleType" class="itemLabel">
                  <span>{{ cycleTypeList.find(i => i.cycleType == formInline.cycleType).label }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="是否定位:" prop="locationFlag" class="itemLabel">
                  <span>{{ formInline.locationFlag == '0' ? '是' : '否' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="是否允许扫码:" prop="scanFlag" class="itemLabel">
                  <span>{{ formInline.scanFlag == '0' ? '是' : '否' }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <!-- 每周 -->
                <el-form-item v-if="formInline.cycleType == 0" label="开始日期:" prop="startDate" class="dateNum itemLabel">
                  <span>{{ singleTimeList.find(i => i.startTime == formInline.startDate).label }}</span>
                </el-form-item>
                <!-- 季度 -->
                <el-form-item v-if="formInline.cycleType == 3" label="开始月份:" prop="startTime" class="dateNum itemLabel">
                  <span>{{ startMonthList.find(i => i.value == formInline.startMonth).label }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <!-- 每月 -->
                <el-form-item v-if="formInline.cycleType == 2 || formInline.cycleType == 3" label="开始日期:" prop="startDate" class="dateNum itemLabel">
                  <span>{{ setMonths().find(i => formInline.startDate == i.value).value }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <!-- 全年.、单次 -->
                <el-form-item v-if="formInline.cycleType == 5 || formInline.cycleType == 8" label="开始日期:" prop="startDate" class="dateNum itemLabel">
                  <span>{{ formInline.startDate }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="时间:" prop="timeLine">
                  <span>{{ formInline.timeLine[0] + '-' + formInline.timeLine[1] }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <!-- 查看时回显 -->
                <el-form-item
                  v-if="(formInline.cycleType == 8 || formInline.cycleType == 0 || formInline.cycleType == 2 || formInline.cycleType == 3 || formInline.cycleType == 5) && readonly"
                  label="完成期限:"
                  prop="finalTime"
                  class=""
                  
                >
                  <span style="color: #606266; display: inline-block; width: 300px;">{{ formInline.finalTime }}天</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col v-if="readonly" :span="8">
                <el-form-item v-if="formInline.cycleType == 6" label="频次:" prop="cycleRole" style="width: 370px; text-align: left;">
                  <span>{{ formInline.cycleRole }}次</span>
                </el-form-item>
              </el-col>
              <el-col v-if="readonly" :span="8">
                <el-form-item v-if="formInline.cycleType == 6" label="最小间隔:" prop="minInterval" style="width: 370px; margin-left: 30px;">
                  <span>{{ formInline.minInterval }}分钟</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="是否按顺序执行：" label-width="130px">
                  <span>{{ formInline.sortFlag == '0' ? '是' : '否' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="16">
                <el-form-item label="计划说明:">
                  <span>{{ detailsInfo.remarks }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <!-- 每日
            <div v-if="!readonly" style="display: flex; align-items: center;">
              <el-form-item v-if="formInline.cycleType == 6" label="频次:" prop="cycleRole" style="margin-left: 30px;" class="dayNum itemLabel">
                <el-input v-model="formInline.cycleRole" placeholder="默认：0" type="number">
                  <template slot="append">天</template>
                </el-input>
              </el-form-item>
              <el-form-item v-if="formInline.cycleType == 6" label="最小间隔:" prop="minInterval" class="minNum itemLabel">
                <el-input v-model="formInline.minInterval" placeholder="默认：0" type="number">
                  <template slot="append">分钟</template>
                </el-input>
              </el-form-item>
            </div>
            <div v-else style="display: flex; align-items: center;">
              <el-form-item v-if="formInline.cycleType == 6" label="频次:" prop="cycleRole" style="width: 370px; text-align: left;">
                <span>{{ formInline.cycleRole }}次</span>
              </el-form-item>
              <el-form-item v-if="formInline.cycleType == 6" label="最小间隔:" prop="minInterval" style="width: 370px; margin-left: 30px;">
                <span>{{ formInline.minInterval }}分钟</span>
              </el-form-item>
            </div> -->
          </el-form>
        </div>
        <div class="topList" style="margin-top: 30px;">
          <div class="topTitle">
            <div style="display: flex; align-items: center;">
              <div class="line"></div>
              <span style="width: 80px;">巡检点管理</span>
            </div>
          </div>
          <div>
            <div>
              <el-table
                class="taskPointTable"
                :data="tableData"
                border
                style="width: 100%; margin-bottom: 10px; height: 300px;"
                :cell-style="{ padding: '8px' }"
                stripe
                height="280px;"
                :header-cell-style="{ background: '#f2f4fbd1' }"
                @selection-change="handleSelectionChangeDialog"
              >
                <el-table-column type="index" label="序号" width="80" align="center"></el-table-column>
                <el-table-column prop="taskPointName" show-overflow-tooltip label="巡检点名称" align="center"></el-table-column>
                <el-table-column prop="taskPointTypeName" show-overflow-tooltip label="巡检点类型" align="center"></el-table-column>
                <el-table-column prop="projectName" show-overflow-tooltip label="任务书" align="center">
                  <template slot-scope="scope">
                    <span v-if="!scope.row.projectName || scope.row.projectName == ''" style="color: #ff1919;">无</span>
                    <span v-else style="color: #5188fc;">{{ scope.row.projectName }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="locationPointName" show-overflow-tooltip label="定位点名称" align="center"> </el-table-column>
                <el-table-column prop show-overflow-tooltip label="操作" align="center">
                  <template slot-scope="scope">
                    <span v-if="!scope.row.projectName || scope.row.projectName == ''" style="color: #5188fc; cursor: pointer;" @click="addRowTask(scope.row)">添加任务书</span>
                    <span v-else style="color: #ccc; cursor: not-allowed;">添加任务书</span>
                  </template>
                </el-table-column>
              </el-table>
              <el-pagination
                class="pagination"
                style="margin-top: 10px; bottom: 90px; display: none;"
                :current-page="paginationData.currentPage"
                :page-sizes="[15, 30, 50, 100]"
                :page-size="paginationData.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="paginationData.total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              ></el-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="bottomBar">
      <div class="bottomWrap">
        <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db;" @click="$router.go('-1')">关闭</el-button>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'addPlans',
  data() {
    return {
      multipleSelection: [],
      title: '',
      pageOrigin: '', // 保存页面来源，是日常保养，还是日常巡检
      readonly: false,
      saveLoading: false,
      completeLoading: false,
      formInline: {
        executeMode: '1', // 执行方式
        planTypeId: '', // 计划类型
        cycleType: 8, // 周期类型
        finalTime: '', // 完成期限
        planName: '', // 计划名称
        planTypeName: '',
        startDate: '', // 开始日期
        projectExplain: '',
        timeLine: '', // 时间
        cycleRole: '', // 频次
        minInterval: '', // 最小间隔
        remarks: '', // 计划说明
        startMonth: '', // 开始月份
        locationFlag: '0', // 是否定位
        scanFlag: '0', // 是否扫码
        id: '' // 模板id，修改必传
      },
      distributionTeamId: '', // 按班组/部门
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      typeList: [],
      workTypeCodeList: [],
      unitOptions: [],
      termTypeOptions: [],
      query: {},
      id: '',
      blockLoading: false,
      typeArr: [],
      typeTreeData: [],
      propsType: {
        children: 'children',
        label: 'typeName',
        value: 'typeId',
        checkStrictly: true
      },
      rules: {
        planName: [],
        planTypeId: [],
        finalTime: [],
        executeMode: [],
        timeLine: [{ required: true, message: '请选择时间', trigger: 'change' }],
        startDate: [{ required: true, message: '请选择开始日期', trigger: 'change' }]
      },
      currentPage: 1,
      total: 0,
      fileList: [],

      tableData: [],
      dialogVisibleWz: false,
      dialogVisibleSk: false,
      dialogVisibleDp: false,
      dialogVisiblePo: false,
      executeModeList: [
        {
          executeMode: '1',
          name: '按小组'
        },
        {
          executeMode: '2',
          name: '按人员'
        }
      ], // 执行方式
      cycleTypeList: [
        {
          cycleType: 8,
          label: '单次'
        },
        {
          cycleType: 6,
          label: '每日'
        },
        {
          cycleType: 0,
          label: '每周'
        },
        {
          cycleType: 2,
          label: '每月'
        },
        {
          cycleType: 3,
          label: '季度'
        },
        {
          cycleType: 5,
          label: '全年'
        }
      ], // 周期类型
      planTypeList: [], // 计划类型
      singleTimeList: [
        {
          startTime: '1',
          label: '每周一'
        },
        {
          startTime: '2',
          label: '每周二'
        },
        {
          startTime: '3',
          label: '每周三'
        },
        {
          startTime: '4',
          label: '每周四'
        },
        {
          startTime: '5',
          label: '每周五'
        },
        {
          startTime: '6',
          label: '每周六'
        },
        {
          startTime: '7',
          label: '每周日'
        }
      ], // 单次开始日期
      startMonthList: [
        // 季度开始月份
        {
          value: 1,
          label: '第一个月'
        },
        {
          value: 2,
          label: '第二个月'
        },
        {
          value: 3,
          label: '第三个月'
        }
      ],
      selectTaskRow: '',
      seelctTaskArr: [],
      saveType: '', // 暂存或提交
      deptArr: '', // 班组
      deptArrs: '',
      deptArrAll: [],
      distributionTeamName: '',
      peopleArr: [],
      distributionPeopleName: '',
      distributionPeopleId: '',
      detailsInfo: {}
    }
  },
  mounted() {
    this._findDictionaryTableList() // 获取计划类型
    this.query = this.$route.query
    this.title = '计划查看'
    this.formInline.id = this.query.id
    this.readonly = true
    this._getMaintainPlanDetail() // 查询详情
  },
  methods: {
    // 查询详情
    _getMaintainPlanDetail() {
      let data = {
        id: this.query.id
      }
      this.$api.getMaintainPlanDetail(data).then((res) => {
        const { code, data, message } = res
        var _self = this
        if (code == '200') {
          this.detailsInfo = data.data
          this.formInline.executeMode = data.data.executeMode
          this.formInline.planTypeId = data.data.planTypeId
          this.formInline.cycleType = data.data.cycleType
          this.formInline.sortFlag = data.data.sortFlag
          if (data.data.cycleType == '3') {
            this.formInline.startMonth = parseInt(data.data.startDate.substring(0, 1))
            this.formInline.startDate = data.data.startDate.split('-')[1]
          } else {
            this.formInline.startDate = data.data.startDate
          }
          this.formInline.finalTime = data.data.finalTime
          if (this.title == '计划复制') {
            this.formInline.planName = ''
          } else {
            this.formInline.planName = data.data.planName
          }
          this.formInline.planTypeName = data.data.planTypeName

          this.formInline.cycleRole = data.data.cycleRole
          this.formInline.minInterval = data.data.minInterval
          this.formInline.remarks = data.data.remarks
          this.formInline.locationFlag = data.data.locationFlag
          this.formInline.scanFlag = data.data.scanFlag
          this.distributionTeamName = data.data.distributionTeamName
          this.distributionTeamId = data.data.distributionTeamId
          this.distributionPeopleName = data.data.planPersonName
          this.distributionPeopleId = data.data.planPersonCode
          this.formInline.timeLine = [data.data.startTime, data.data.endTime]
          this.tableData = data.data.maintainPlanRegions
          if (this.tableData) {
            this.tableData.forEach((val, index) => {
              data.data.maintainPlanRegions.forEach((v, i) => {
                if (val.taskPointId == v.taskPointId) {
                  val.projectName = v.taskBookName
                }
              })
            })
          }
        }
      })
    },
    // 选择
    handleSelectionChangeDialog(val) {
      console.log(val)
      this.multipleSelection = val
    },
    setMonths() {
      let months = []
      for (let i = 1; i <= 30; i++) {
        months.push({
          value: i
        })
      }
      return months
    },
    // 计划类型
    _findDictionaryTableList() {
      let data = {
        dictCode: '',
        dictName: '',
        pageNo: 1,
        pageSize: 99999,
        dictType: 'plan_type'
      }
      this.$api.findDictionaryTableList(data).then((res) => {
        const { code, data, message } = res
        if (code == 200) {
          this.planTypeList = data.list
        } else {
          this.$message.error(message)
        }
      })
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
    },
    // 每页的条数
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.paginationData.currentPage = 1
    },
    // 保存
    saveFn() {
      console.log('保存')
    }
  }
}
</script>
<style lang="scss" scoped>
.inner {
  overflow: auto;
  margin: 15px;
  padding: 15px 60px;
  width: calc(100% - 30px);
  height: calc(100% - 82px);
  background-color: #fff;

  .topTitle {
    display: flex;
    align-items: center;
  }
}

.bottomBar {
  height: 52px;
  width: 100%;
  background-color: #fff;
  display: flex;
  justify-content: left;
  align-items: center;

  .bottomWrap {
    padding: 0 16px;
  }
}

.content_box {
  height: 100%;
  padding: 20px 25px 25px;
  background: #fff;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.isScan {
  margin-left: 20px;
  width: 250px;

  .el-form-item__label {
    width: 120px !important;
  }
}

.el-form--inline .el-form-item {
  margin-right: 50px;
  // margin-bottom: 0;
  margin-bottom: 10px;
}

.topTitle {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-weight: 600;
  color: #5b5b5b;
}

.line {
  width: 5px;
  height: 20px;
  background: #5188fc;
  margin-right: 8px;
}

.form-inline .dayNum {
  :deep(.el-form-item__label) {
    width: 70px !important;
  }
}

.dateNum {
  margin-right: 80px;
}

.isScan {
  :deep(.el-form-item__label) {
    width: 115px !important;
  }
}

.project-textarea textarea {
  height: 120px;
}

.sion-icon {
  cursor: pointer;
  padding-left: 3px;
  color: #5188fc;
}

.icon-disabled {
  cursor: not-allowed;
}

.form-inline {
  .el-input,
  .el-select,
  .el-cascader {
    width: 300px;
  }
}

.form-inline {
  width: 100%;
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  .itemLabel {
    display: flex;
  }
}

.form-inline .width_lengthen {
  width: 300px;
}

:deep(.el-upload-dragger) {
  width: 255px;
  height: 164px;
}

:deep(.sino-vertical-file > .el-upload__tip) {
  top: -43px;
  left: 57px;
  color: #ccc;
}

.executeModes {
  :deep(.el-form-item__content) {
    width: calc(100% - 100px);
  }
}
</style>
                