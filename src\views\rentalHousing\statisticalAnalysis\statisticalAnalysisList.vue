<template>
  <div class="container">
    <div class="box-container">
      <div class="box">
        <div class="box-title">
          <span>按房型统计</span>
        </div>
        <div class="box-content">
          <el-table
            ref="materialTable"
            v-loading="roomStatisticsTableLoading"
            :data="roomStatisticsTableData"
            height="100%"
            border
            :header-cell-style="{ background: '#F6F5FA' }"
            style="width: 100%"
            :cell-style="{ padding: '8px 0 8px 0' }"
            stripe
            highlight-current-row
            :empty-text="emptyText"
            row-key="id"
          >
            <el-table-column show-overflow-tooltip prop="groupName" label="房型"></el-table-column>
            <el-table-column show-overflow-tooltip prop="totalCount" label="总数"></el-table-column>
            <el-table-column show-overflow-tooltip prop="usingCount" label="使用中"></el-table-column>
            <el-table-column show-overflow-tooltip prop="awaitCount" label="待入住"></el-table-column>
            <el-table-column show-overflow-tooltip prop="idleCount" label="闲置中"></el-table-column>
          </el-table>
        </div>
      </div>
      <div class="box">
        <div class="box-title">
          <span>按小区统计</span>
        </div>
        <div class="box-content">
          <div class="table-div">
            <el-table
              ref="materialTable"
              v-loading="communityStatisticsTableLoading"
              :data="communityStatisticsTableData"
              height="100%"
              border
              :header-cell-style="{ background: '#F6F5FA' }"
              style="width: 100%"
              :cell-style="{ padding: '8px 0 8px 0' }"
              stripe
              highlight-current-row
              :empty-text="emptyText"
              row-key="id"
            >
              <el-table-column show-overflow-tooltip prop="groupName" label="小区名称"></el-table-column>
              <el-table-column show-overflow-tooltip prop="totalCount" label="总数"></el-table-column>
              <el-table-column show-overflow-tooltip prop="usingCount" label="使用中"></el-table-column>
              <el-table-column show-overflow-tooltip prop="awaitCount" label="待入住"></el-table-column>
              <el-table-column show-overflow-tooltip prop="idleCount" label="闲置中"></el-table-column>
            </el-table>
          </div>
          <div class="table-pagination">
            <el-pagination layout="prev, pager, next" @current-change="handleCurrentChange" :total="communityPagination.total"> </el-pagination>
          </div>
        </div>
      </div>
      <div class="box">
        <div class="box-title">
          <span>科室成员入住分布</span>
        </div>
        <div class="box-content">
          <div id="pie" style="width: 100%; height: 100%"></div>
        </div>
      </div>
      <div class="box">
        <div class="box-title">
          <span>房租租金统计</span>
          <el-date-picker
            v-model="rentTime"
            size="mini"
            type="monthrange"
            range-separator="至"
            start-placeholder="开始日期"
            value-format="yyyy-MM"
            end-placeholder="结束日期"
            @change="handleRentTime"
          >
          </el-date-picker>
          <el-select v-model="communityValue" size="mini" clearable placeholder="全部小区" @change="handleCommunityValue">
            <el-option v-for="item in communityOptions" :key="item.id" :label="item.spaceName" :value="item.id"> </el-option>
          </el-select>
          <el-select v-model="houseValue" size="mini" clearable placeholder="全部房型" @change="handleHouseValue">
            <el-option v-for="item in roomTypeOptions" :key="item.dictCode" :label="item.dictName" :value="item.dictCode"> </el-option>
          </el-select>
        </div>
        <div class="box-content">
          <div class="rent-box">
            <div class="img-div">
              <img src="@/assets/images/rent.png" alt="" />
            </div>
            <div class="text-div">租金收入</div>
            <div class="rent-div">
              <el-statistic group-separator="," :precision="2" :value="rent" :title="''">
                <template slot="suffix">
                  <span style="font-size: 30px; margin-left: 3px">元</span>
                </template>
              </el-statistic>
            </div>
          </div>
        </div>
      </div>
      <div class="box">
        <div class="box-title">
          <span>成本收入统计</span>
          <el-date-picker
            v-model="incomeTime"
            size="mini"
            type="monthrange"
            range-separator="至"
            start-placeholder="开始日期"
            value-format="yyyy-MM"
            end-placeholder="结束日期"
            @change="handleIncomeTime"
          >
          </el-date-picker>
        </div>
        <div class="box-content">
          <div id="bar" style="width: 100%; height: 100%"></div>
        </div>
      </div>
      <div class="box">
        <div class="box-title">
          <span>房屋空置率统计</span>
          <el-date-picker
            v-model="vacancyRateTime"
            size="mini"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            value-format="yyyy-MM-dd"
            style="width: 240px"
            end-placeholder="结束日期"
            @change="handleVacancyRateTime"
          >
          </el-date-picker>
        </div>
        <div class="box-content">
          <div id="gauge" style="width: 100%; height: 100%"></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
import moment from 'moment'
export default {
  name: 'statisticalAnalysisListIndex',
  data() {
    return {
      pickerOptions: {
        // 包含当月
        disabledDate: (time) => {
          const sixMonth = moment().subtract(5, 'months').startOf('month').valueOf()
          return time.getTime() > Date.now() || time.getTime() < sixMonth
        }
      },
      roomStatisticsTableLoading: false,
      communityStatisticsTableLoading: false,
      emptyText: '暂无数据',
      communityPagination: {
        pageSize: 15,
        currentPage: 1,
        total: 0
      },
      rentTime: '',
      communityValue: '',
      houseValue: '',
      incomeTime: '',
      vacancyRateTime: '',
      communityOptions: [], // 小区下拉数据
      roomTypeOptions: [], // 房型下拉数据
      roomStatisticsTableData: [], // 房型统计数据
      communityStatisticsTableData: [], // 小区统计数据
      deptMembersCheckInStatisticsData: [], // 科室成员入住统计
      rent: 0,
      costStatisticsData: [],
      vacancyRateValue: 0
    }
  },
  created() {
    this.getDictData()
    this.getTreeListData()
  },
  mounted() {
    const now = new Date()
    const year = now.getFullYear()
    const month = now.getMonth() + 1
    if (month == 12) {
      let date = new Date(year, now.getMonth(), 1)
      date.setMonth(date.getMonth() + 1)
      this.rentTime = [moment(`${year}-${month}`).format('YYYY-MM'), moment(`${date.getFullYear()}-${date.getMonth() + 1}`).format('YYYY-MM')]
    } else {
      this.rentTime = [moment(`${year}-${month}`).format('YYYY-MM'), moment(`${year}-${month + 1}`).format('YYYY-MM')]
    }
    if (month == 1) {
      let date = new Date(year, now.getMonth(), 1)
      date.setMonth(date.getMonth() - 2)
      this.incomeTime = [moment(`${date.getFullYear()}-${date.getMonth() + 1}`).format('YYYY-MM'), moment(`${year}-${month}`).format('YYYY-MM')]
    } else {
      this.incomeTime = [moment(`${year}-${month - 2}`).format('YYYY-MM'), moment(`${year}-${month}`).format('YYYY-MM')]
    }
    let vacancyRateTime = this.getPreviousMonthRange()
    this.vacancyRateTime = [vacancyRateTime.startDate, vacancyRateTime.endDate]
    this.getRoomStatisticsList()
    this.getCommunityStatisticsList()
    this.getDeptMemberCheckInPie()
    this.getRentStatisticsList()
    this.getCostIncomeStatistics()
    this.getRentVacancyStatistics()
  },
  methods: {
    // 当前日期到上个月+1天
    getPreviousMonthRange() {
      const today = new Date()
      const currentYear = today.getFullYear()
      const currentMonth = today.getMonth()
      const currentDate = today.getDate()

      // Calculate the last day of the previous month
      const lastDayOfPreviousMonth = new Date(currentYear, currentMonth, 0).getDate()

      // Calculate the date in the previous month
      let previousMonthDate
      if (currentDate > lastDayOfPreviousMonth) {
        previousMonthDate = new Date(currentYear, currentMonth - 1, lastDayOfPreviousMonth)
      } else {
        previousMonthDate = new Date(currentYear, currentMonth - 1, currentDate)
      }

      // Calculate the date one day before the previous month date
      const startDate = new Date(previousMonthDate)
      startDate.setDate(previousMonthDate.getDate() + 1)

      // Format the dates as YYYY-MM-DD
      const formatDate = (date) => `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`

      return {
        startDate: formatDate(startDate),
        endDate: formatDate(today)
      }
    },
    /** 小区统计页数变化 事件 */
    handleCurrentChange(val) {
      this.communityPagination.currentPage = val
      this.getCommunityStatisticsList()
    },
    /** 房型统计 */
    getRoomStatisticsList() {
      let params = {}
      this.roomStatisticsTableLoading = true
      this.$api.rentalHousingApi.getRoomTypeStatisticsList(params).then((res) => {
        this.roomStatisticsTableLoading = false
        if (res.code == 200) {
          this.roomStatisticsTableData = res.data
        } else {
          this.$message.error(res.message)
        }
      })
    },
    /** 小区统计 */
    getCommunityStatisticsList() {
      let params = {
        pageNum: this.communityPagination.currentPage
      }
      this.communityStatisticsTableLoading = true
      this.$api.rentalHousingApi.getCommunityStatisticsList(params).then((res) => {
        this.communityStatisticsTableLoading = false
        if (res.code == 200) {
          this.communityStatisticsTableData = res.data.records
          this.communityPagination.total = res.data.total
        } else {
          this.$message.error(res.message)
        }
      })
    },
    /** 科室成员入住 */
    getDeptMemberCheckInPie() {
      let params = {}
      this.$api.rentalHousingApi.getDeptMemberCheckInPie(params).then((res) => {
        if (res.code == 200) {
          this.deptMembersCheckInStatisticsData = res.data || []
          this.$nextTick(() => {
            this.initPie()
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    /** 租金日期查询 */
    handleRentTime() {
      this.getRentStatisticsList()
    },
    /** 租金小区查询 */
    handleCommunityValue() {
      this.getRentStatisticsList()
    },
    /** 租金房型查询 */
    handleHouseValue() {
      this.getRentStatisticsList()
    },
    /** 房租租金统计 */
    getRentStatisticsList() {
      let params = {
        startMonth: this.rentTime && this.rentTime.length ? this.rentTime[0] : '',
        endMonth: this.rentTime && this.rentTime.length ? this.rentTime[1] : '',
        estateId: this.communityValue,
        houseTypeCode: this.houseValue
      }
      this.$api.rentalHousingApi.getRentStatisticsList(params).then((res) => {
        if (res.code == 200) {
          this.rent = res.data ? res.data : 0
        } else {
          this.$message.error(res.message)
        }
      })
    },
    /** 成本日期查询 */
    handleIncomeTime() {
      if (this.incomeTime.length === 2) {
        const startDate = new Date(this.incomeTime[0])
        const endDate = new Date(this.incomeTime[1])
        const monthsDiff = (endDate.getFullYear() - startDate.getFullYear()) * 12 + (endDate.getMonth() - startDate.getMonth())
        if (monthsDiff > 6) {
          const now = new Date()
          const year = now.getFullYear()
          const month = now.getMonth() + 1
          this.$message.warning('选择的月份跨度不能超过6个月')
          if (month == 1) {
            let date = new Date(year, now.getMonth(), 1)
            date.setMonth(date.getMonth() - 2)
            this.incomeTime = [moment(`${date.getFullYear()}-${date.getMonth() + 1}`).format('YYYY-MM'), moment(`${year}-${month}`).format('YYYY-MM')]
          } else {
            this.incomeTime = [moment(`${year}-${month - 2}`).format('YYYY-MM'), moment(`${year}-${month}`).format('YYYY-MM')]
          }
        }
      }
      this.getCostIncomeStatistics()
    },
    /** 成本收入统计 */
    getCostIncomeStatistics() {
      let params = {
        startMonth: this.incomeTime && this.incomeTime.length ? this.incomeTime[0] : '',
        endMonth: this.incomeTime && this.incomeTime.length ? this.incomeTime[1] : ''
      }
      this.$api.rentalHousingApi.getCostIncomeStatisticsBar(params).then((res) => {
        if (res.code == 200) {
          this.costStatisticsData = res.data || []
          this.$nextTick(() => {
            this.initBar()
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    /** 空置率日期查询 */
    handleVacancyRateTime() {
      this.getRentVacancyStatistics()
    },
    /** 房租空置率统计 */
    getRentVacancyStatistics() {
      let params = {
        start: this.vacancyRateTime && this.vacancyRateTime.length ? this.vacancyRateTime[0] : '',
        end: this.vacancyRateTime && this.vacancyRateTime.length ? this.vacancyRateTime[1] : ''
      }
      this.$api.rentalHousingApi.getRentVacancyStatistics(params).then((res) => {
        if (res.code == 200) {
          this.vacancyRateValue = res.data || 0
          this.$nextTick(() => {
            this.initGauge()
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 获取数据字典
    getDictData() {
      const params = {
        pageNo: 1,
        pageSize: 9999
      }
      this.$api.rentalHousingApi.queryDictPage(params).then((res) => {
        if (res.code === '200') {
          res.data.records.forEach((it) => {
            // 只取启用的
            if (it.dictState !== '1') return
            if (it.dictType === 'fangxing') {
              this.roomTypeOptions.push(it)
            }
          })
        }
      })
    },
    /** 获取全部小区数据 */
    getTreeListData() {
      this.$api.rentalHousingApi
        .getCommunityList({ parentId: '' })
        .then((res) => {
          if (res.code === '200') {
            this.communityOptions = res.data
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取小区信息失败'))
    },
    /** 初始化饼图 */
    initPie() {
      var chartDom = document.getElementById('pie')
      var myChart = echarts.init(chartDom)
      var option
      option = {
        tooltip: {
          trigger: 'item',
          formatter: function (params) {
            let marker = params.marker || '',
              name = params.name || '',
              value = params.value || '',
              percent = params.percent || '0'
            let spanVal = `<span style="padding: 0px 10px 0 5px;font-weight: bolder;color: #333;">${value}</span>`
            return `${marker}${name}${spanVal}${percent}%`
          }
        },
        // color: ['rgb(53, 99, 222)', 'rgb(255, 148, 52)', 'rgb(7, 203, 130)'],
        legend: {
          top: '5%',
          left: 'center'
        },
        series: [
          {
            name: '',
            type: 'pie',
            center: ['50%', '60%'],
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: this.deptMembersCheckInStatisticsData.length
              ? this.deptMembersCheckInStatisticsData.map((item) => {
                  return {
                    value: item.totalCount,
                    name: item.groupName
                  }
                })
              : []
          },
          {
            type: 'gauge',
            center: ['50%', '60%'], // 饼状图的位置
            radius: '58%',
            // 如何是版本是4 这里是359.999；不能是360；否则圆环不能正常显示
            // 如果是版本是5，这里可以是360
            startAngle: 360,
            endAngle: 0,
            splitNumber: 40,
            zlevel: 10,
            axisLine: {
              show: false
            },
            splitLine: {
              show: true, // 是否显示分隔线。 如果设置为true,外层虚线才能看见
              length: 1, // 分隔线与轴线的距离。这里表现是虚线的宽度
              lineStyle: {
                width: 5, // 分隔线线长。支持相对半径的百分比。
                color: '#E5E6E8' // 线的颜色
              }
            },
            axisLabel: {
              //刻度标签
              show: false
            },
            axisTick: {
              //刻度样式
              show: false
            },
            detail: {
              show: false
            },
            //仪表盘指针。
            pointer: {
              // 不显示仪表盘中的指针
              show: false
            },
            data: []
          }
        ]
      }
      myChart.resize()
      myChart.clear()
      option && myChart.setOption(option)
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
    /** 初始化柱状图 */
    initBar() {
      var chartDom = document.getElementById('bar')
      var myChart = echarts.init(chartDom)
      var option

      option = {
        legend: {
          left: '75%', //图例的位置，可以用像素，可以用百分比，也可以用center，right等
          top: 0, //图例的位置
          itemWidth: 15, //图例图标的宽
          itemHeight: 10 //图例图标的高
        },
        grid: {
          top: '15%',
          right: '5%',
          bottom: '10%'
        },
        xAxis: {
          type: 'category',
          data: this.costStatisticsData.length ? this.costStatisticsData.map((item) => item.groupName) : [],
          axisLine: {
            show: true,
            lineStyle: {
              color: ['rgb(233, 233, 233)'], //分割线的颜色
              type: 'dashed'
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: ['rgb(233, 233, 233)'], //分割线的颜色
              type: 'dashed'
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '单位：元',
          axisLine: {
            show: true,
            lineStyle: {
              color: ['rgb(233, 233, 233)'], //分割线的颜色
              type: 'dashed'
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: ['rgb(233, 233, 233)'], //分割线的颜色
              type: 'dashed'
            }
          }
        },
        series: [
          {
            data: this.costStatisticsData.length ? this.costStatisticsData.map((item) => item.costAmount * 1) : [],
            type: 'bar',
            name: '成本',
            showBackground: true,
            backgroundStyle: {
              color: 'rgb(249, 249, 249)'
            },
            itemStyle: {
              normal: {
                color: 'rgb(52, 100, 219)'
              }
            },
            barWidth: 10
          },
          {
            data: this.costStatisticsData.length ? this.costStatisticsData.map((item) => item.hireRentAmount * 1) : [],
            type: 'bar',
            name: '收入',
            showBackground: true,
            backgroundStyle: {
              color: 'rgb(249, 249, 249)'
            },
            itemStyle: {
              normal: {
                color: 'rgb(251, 147, 54)'
              }
            },
            barWidth: 10
          }
        ]
      }
      myChart.resize()
      myChart.clear()
      option && myChart.setOption(option)
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
    /** 初始化仪表盘 */
    initGauge() {
      var chartDom = document.getElementById('gauge')
      var myChart = echarts.init(chartDom)
      var option

      option = {
        series: [
          {
            type: 'gauge',
            progress: {
              show: true,
              width: 18
            },
            axisLine: {
              lineStyle: {
                width: 18
              }
            },
            axisTick: {
              show: false
            },
            splitLine: {
              length: 15,
              lineStyle: {
                width: 2,
                color: '#999'
              }
            },
            axisLabel: {
              distance: 25,
              color: '#999'
            },
            anchor: {
              show: true,
              showAbove: true,
              itemStyle: {
                borderWidth: 10
              }
            },
            title: {
              show: false
            },
            detail: {
              valueAnimation: true,
              offsetCenter: [0, '70%']
            },
            data: [
              {
                value: this.vacancyRateValue
              }
            ]
          }
        ]
      }
      myChart.resize()
      myChart.clear()
      option && myChart.setOption(option)
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  height: 100%;
  padding: 20px;
  box-sizing: border-box;
}
.box-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  .box {
    width: calc((100% / 3) - 10px);
    height: calc((100% / 2) - 5px);
    padding: 0px 15px 15px 15px;
    box-sizing: border-box;
    background: #ffffff;
    border-radius: 4px 4px 4px 4px;
    .box-title {
      padding: 20px 0px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      span {
        font-weight: bolder;
      }
    }
    .box-content {
      height: calc(100% - 68px);
      .table-div {
        height: calc(100% - 35px);
      }
      .table-pagination {
        margin-top: 6px;
      }
      .rent-box {
        height: 100%;
        background: #faf9fc;
        border-radius: 5px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color: #333;
        font-weight: bolder;
        img {
          width: 75px;
          height: 75px;
        }
        .text-div {
          margin: 24px 0px;
          font-size: 20px;
        }
        .rent-div {
          margin-bottom: 15px;
        }
      }
    }
  }
  .box:nth-child(-n + 3) {
    margin-bottom: 10px;
  }
  .box:nth-child(3n + 2) {
    margin: 0px 15px;
  }
  .el-date-editor {
    width: 180px;
    margin-left: 5px;
  }
  .el-select {
    width: 105px;
    margin-left: 5px;
  }
}
::v-deep .el-statistic .con .number {
  font-size: 45px;
}
::v-deep .el-range__close-icon {
  display: none;
}
</style>