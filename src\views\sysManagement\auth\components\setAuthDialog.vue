<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    title="角色分配"
    width="40%"
    :visible.sync="visible"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <div class="dialog-content">
      <el-form ref="formInline">
        <el-row :gutter="24" style="margin: 0;">
          <el-col :md="12">
            <el-form-item label="登录账号" label-width="80px">
              <el-input :value="selectItem.roleName" disabled/>
            </el-form-item>
          </el-col>
          <el-col :md="12">
            <el-form-item label="人员姓名" label-width="80px">
              <el-input :value="selectItem.roleName" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24" style="margin: 0;">
          <el-col :md="12">
            <el-form-item label="角色" label-width="80px">
              <el-input :value="selectItem.roleName" disabled/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24" style="margin: 0;">
          <el-col :md="24">
            <el-form-item label="数据权限" label-width="80px">
              <el-radio-group v-model="selectAuth">
                <el-radio :label="1">全部数据</el-radio>
                <el-radio :label="2">本部门数据</el-radio>
                <el-radio :label="3">自定义</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="selectAuth == 3" :gutter="24" style="margin: 0;">
          <el-col :md="12">
            <el-form-item label="数据范围" label-width="80px">
              <treeCard ref="authTree" :treeData="treeData" :defaultExpandedKeys="[1]" :defaultCheckedKeys="[3]" treeHeight="280px"></treeCard>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="confirm('ruleForm')">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import treeCard from '../formComponent/treeCard.vue'
export default {
  name: 'setAuthDialog',
  components: {
    treeCard
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    selectItem: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      selectAuth: 1,
      treeData: [
        {
          id: 1,
          label: '一级 1',
          children: [
            {
              id: 4,
              label: '二级 1-1',
              children: [
                {
                  id: 9,
                  label: '三级 1-1-1'
                },
                {
                  id: 10,
                  label: '三级 1-1-2'
                }
              ]
            }
          ]
        },
        {
          id: 2,
          label: '一级 2',
          children: [
            {
              id: 5,
              label: '二级 2-1'
            },
            {
              id: 6,
              label: '二级 2-2'
            }
          ]
        },
        {
          id: 3,
          label: '一级 3',
          children: [
            {
              id: 7,
              label: '二级 3-1'
            },
            {
              id: 8,
              label: '二级 3-2'
            }
          ]
        },
        {
          id: 11,
          label: '一级 4',
          children: [
            {
              id: 12,
              label: '二级 4-1'
            },
            {
              id: 13,
              label: '二级 4-2'
            },
            {
              id: 14,
              label: '二级 4-1'
            },
            {
              id: 15,
              label: '二级 4-2'
            },
            {
              id: 16,
              label: '二级 4-1'
            },
            {
              id: 17,
              label: '二级 4-2'
            }
          ]
        }
      ]
    }
  },
  mounted() {

  },
  methods: {
    confirm() {
      const authTreeData = this.$refs.authTree.getCheckedNodes()
      console.log(authTreeData)
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', !this.visible)
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .model-dialog {
  // min-height: 60vh;
}

.dialog-content {
  width: 100%;
  background: #fff;
  padding: 10px;
  max-height: 500px;
}
</style>
