<template>
  <el-dialog
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    :title="title"
    width="60%"
    :visible="visible"
    custom-class="model-dialog"
    :before-close="close"
    z-index="99999"
  >
    <Open v-if="type === 1" ref="openTargetTemplate" :step="showStep" :selectedId="selectedId" @choose="next"></Open>
    <Close v-else :list="formList" :selectedId="selectedId" @choose="closeSelectedChange"></Close>
    <!-- 不开启权重的按钮 -->
    <div v-if="type === 0" slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submit">确 定</el-button>
      <el-button @click="close">取 消</el-button>
    </div>
    <!-- 开启权重的按钮 -->
    <div v-else slot="footer" class="dialog-footer">
      <div v-if="showStep">
        <el-button plain @click="close">取 消</el-button>
      </div>
      <div v-else>
        <el-button v-if="title === '添加指标'" plain @click="showStep = true">上一步</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import Open from './dialogComponents/open'
import Close from './dialogComponents/close'
export default {
  components: {
    Open,
    Close
  },
  model: {
    prop: 'visible',
    event: 'close'
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '添加指标'
    },
    type: {
      type: Number,
      default: 0
    },
    selectedId: {
      type: Array,
      default: () => {}
    }
  },
  data() {
    return {
      formList: [],
      showStep: true,
      first: true
    }
  },
  mounted() {
    if (this.title === '编辑指标') {
      this.showStep = false
    }
  },
  methods: {
    // 不开启权重表格选择变化
    closeSelectedChange(val) {
      console.log(val)
      this.formList = val
    },
    next() {
      this.showStep = false
    },
    close() {
      this.$emit('close', false)
    },
    submit() {
      if (this.type) {
        let obj = this.$refs.openTargetTemplate.selectList
        if (obj.dataType === 2) {
          obj.calculationMethodList.forEach((el) => {
            el.min = el.minTime[0].hours * 60 + el.minTime[0].minute * 1
            el.max = el.maxTime[0].hours * 60 + el.maxTime[0].minute * 1
          })
        } else if (obj.dataType == 0) {
          obj.scoreMechanism = this.$refs.openTargetTemplate.$data.numberType
          obj.intervalNumber = this.$refs.openTargetTemplate.$data.intervalType
        }
        if (!obj.calculationMethodList.length) {
          this.$message.error('请完善指标配置')
          return
        }
        // 判断是否所有值都已经填写
        for (let index = 0; index < obj.calculationMethodList.length; index++) {
          const el = obj.calculationMethodList[index]
          // 区间
          if (obj.intervalNumber == 0) {
            if (!el.min || !el.max) {
              this.$message.error('请完善指标配置')
              return
            }
          } else {
            if (!el.number) {
              this.$message.error('请完善指标配置')
              return
            }
          }
          // 扣分
          if (obj.scoreMechanism == 1) {
            if (!el.scoreValue) {
              this.$message.error('请完善指标配置')
              return
            }
          } else {
            if (!el.score) {
              this.$message.error('请完善指标配置')
              return
            }
          }
        }
        this.formList.push(obj)
      }
      this.$emit('success', this.formList)
    }
  }
}
</script>
