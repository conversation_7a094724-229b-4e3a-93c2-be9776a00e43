<template>
  <div class="promptEvent">
    <p v-show="!prompt_isEditEventName" class="notify-title" @click="editTitleFun('prompt_isEditEventName')">
      {{ warnData.eventName || '请输入' }}
      <i class="el-icon-edit" style="color: #CCCED3;"></i>
    </p>
    <el-input v-show="prompt_isEditEventName" ref="prompt_isEditEventName" v-model="warnData.eventName" placeholder="请输入提示事件名称" :maxlength="10" style="width: 200px;" @blur="() => prompt_isEditEventName = false" />
    <div v-for="(item, index) in warnEventList" :key="index" class="item-form">
      <el-form>
        <el-row :gutter="24" style="margin: 0">
          <el-col :md="22">
            <el-form-item label="提示文案" label-width="130px">
              <template slot="label">
                <el-checkbox v-model="item.checkFlag">提示文案</el-checkbox>
              </template>
              <el-input v-model="item.warnContent" type="textarea" rows="3" resize="none" placeholder="请输入提示文案内容" show-word-limit maxlength="50"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24" style="margin: 0">
          <el-col :md="22">
            <el-form-item label="提示角色" label-width="130px">
              <el-input v-model="item.warnRoles" type="text" clearable placeholder="请输入角色或人员" show-word-limit maxlength="30"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <p v-if="index" class="item-delete" @click="control('delete', index)">
        <i class="el-icon-delete"></i>
        删除
      </p>
    </div>
    <el-button type="primary" icon="el-icon-plus" plain style="margin-top: 8px;" @click="control('add')">添加提示文案</el-button>
  </div>
</template>

<script>
export default {
  name: 'promptEvent',
  data() {
    return {
      prompt_isEditEventName: false, // 修改title
      warnData: {
        stepName: '提示事件',
        eventName: '提示事件',
        showFlag: false // 是否显示操作按钮
      },
      warnEventList: [
        {
          checkFlag: true, // 是否通知 0未勾选 1已勾选
          warnContent: '', // 提示文案
          warnRoles: '' // 提示角色
        }
      ] // 提示列表
    }
  },
  computed: {

  },
  created() {

  },
  methods: {
    control(type, index = 0) {
      if (type == 'add') {
        this.warnEventList.push({
          checkFlag: true,
          warnContent: '',
          warnRoles: ''
        })
      } else if (type == 'delete') {
        this.warnEventList.splice(index, 1)
      }
    },
    // 编辑标题
    editTitleFun(key) {
      this[key] = true
      this.$nextTick(() => {
        this.$refs[key].focus()
      })
    }
  }
}

</script>

<style lang="scss" scoped>
.promptEvent {
  padding: 16px;
  background: #FAF9FC;
  border-radius: 4px;
  margin-bottom: 16px;
  p{
    margin: 0px;
  }
  .notify-title {
    margin-bottom: 16px;
    cursor: pointer;
    font-weight: bold;
    font-size: 16px;
    color: #333333;
    line-height: 32px;
    width: 200px;
  }
  .item-form {
    border-bottom: 1px solid #E4E7ED;
    position: relative;
    margin-bottom: 16px;
    .item-delete {
      color: #FF6461;
      position: absolute;
      top: 50%;
      right: 16px;
      transform: translateY(-50%);
      cursor: pointer;
    }
  }
}
</style>
