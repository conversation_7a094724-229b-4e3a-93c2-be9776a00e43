<template>
  <div class="contentMain">
    <div class="topFilter">
      <div class="backBar">
        <span style="cursor: pointer" @click="$router.go(-1)">
          <i class="el-icon-arrow-left"></i>
          统计分析
        </span>
      </div>
    </div>
    <div class="tableWrap">
      <el-table v-if="activeType == 1 && type == 1" v-loading="tableLoading" :data="taskList" style="width: 100%" height="calc(100% - 40px)" border>
        <el-table-column label="序号" type="index" width="100">
          <template slot-scope="scope">
            <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="assetName" show-overflow-tooltip label="设备名称">
          <template slot-scope="scope">
            <el-link type="primary" :underline="false" @click="goDeviceDetail(scope.row)">{{ scope.row.assetName }}</el-link>
          </template>
        </el-table-column>
        <el-table-column prop="assetCode" show-overflow-tooltip label="设备编码"> </el-table-column>
        <el-table-column prop="assetModel" show-overflow-tooltip label="品牌"></el-table-column>
        <el-table-column prop="regionName" show-overflow-tooltip label="所在区域" width="500"></el-table-column>
        <el-table-column prop="professionalCategoryName" show-overflow-tooltip label="专业列表"></el-table-column>
      </el-table>
      <el-table v-if="type == 0" v-loading="tableLoading" :data="taskList" style="width: 100%" height="calc(100% - 40px)" border>
        <el-table-column label="序号" type="index" width="50" align="center" :resizable="false">
          <template slot-scope="scope">
            <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="workNum" label="工单号" width="180" :resizable="false"></el-table-column>
        <el-table-column prop="workTypeName" label="工单类型" width="180" show-overflow-tooltip :resizable="false"> </el-table-column>
        <el-table-column prop="sourcesDeptName" label="所属科室" show-overflow-tooltip :resizable="false"> </el-table-column>
        <el-table-column prop="localtionNames" label="服务地点" show-overflow-tooltip :resizable="false"> </el-table-column>
        <el-table-column label="服务事项" show-overflow-tooltip :resizable="false">
          <template slot-scope="scope">
            <span>{{ scope.row.itemTypeName }}-{{ scope.row.itemDetailName }}-{{ scope.row.itemServiceName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="designateDeptName" label="服务部门" show-overflow-tooltip :resizable="false"> </el-table-column>
        <el-table-column prop="designatePersonName" label="服务人员" show-overflow-tooltip :resizable="false"> </el-table-column>
        <el-table-column prop="designatePersonPhone" label="联系方式" show-overflow-tooltip :resizable="false"> </el-table-column>
        <el-table-column prop="questionDescription" label="说明" show-overflow-tooltip :resizable="false"> </el-table-column>
        <el-table-column label="状态" :resizable="false">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.flowcode == '1'" type="danger">未受理</el-tag>
            <el-tag v-else-if="scope.row.flowcode == '2'" type="danger">未派工</el-tag>
            <el-tag v-else-if="scope.row.flowcode == '3'" type="success">已派工</el-tag>
            <el-tag v-else-if="scope.row.flowcode == '4'" type="warning">已挂单</el-tag>
            <el-tag v-else-if="scope.row.flowcode == '5'" type="success">已完工</el-tag>
            <el-tag v-else-if="scope.row.flowcode == '6'" type="danger">已取消</el-tag>
            <el-tag v-else-if="scope.row.flowcode == '7'" type="info">暂存</el-tag>
          </template>
        </el-table-column>
      </el-table>
      <el-table v-if="!(activeType == 1 && type == 1) && type != 0" v-loading="tableLoading" :data="taskList" style="width: 100%" height="calc(100% - 40px)" border>
        <el-table-column label="序号" type="index" width="100">
          <template slot-scope="scope">
            <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="planName" show-overflow-tooltip label="任务名称">
          <template slot-scope="scope">
            <el-link type="primary" :underline="false" @click="toTaskDetail(scope.row)">
              {{ scope.row.planName }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column prop="planTypeName" show-overflow-tooltip label="所属计划"> </el-table-column>
        <el-table-column show-overflow-tooltip label="周期类型">
          <template slot-scope="scope">
            <span>{{ cycleTypeFn(scope.row) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="regionName" show-overflow-tooltip :label="systemCode == '1' ? '应巡日期' : '应保养日期'">
          <template slot-scope="scope">
            <span>{{ moment(scope.row.taskStartTime).format('YYYY-MM-DD') }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="distributionTeamName" show-overflow-tooltip :label="systemCode == '1' ? '巡检部门' : '保养部门'"></el-table-column>
        <el-table-column prop="totalCount" show-overflow-tooltip :label="systemCode == '1' ? '应巡点数' : '应保养点数'"></el-table-column>
        <el-table-column prop="hasCount" show-overflow-tooltip :label="systemCode == '1' ? '实巡点数' : '实保养点数'"></el-table-column>
        <el-table-column prop="taskStartTime" show-overflow-tooltip label="完成状态">
          <template slot-scope="scope">
            <div>
              <span v-if="scope.row.taskStatus == '1'" class="disable">
                <span></span>
                未完成
              </span>
              <span v-if="scope.row.taskStatus == '2'" class="enable">
                <span></span>
                已完成
              </span>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="" style="padding-top: 10px; text-align: right">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="paginationData.currentPage"
          :page-sizes="[15, 30, 50, 100]"
          :page-size="paginationData.pageSize"
          :total="paginationData.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
import moment from 'moment'
import axios from 'axios'
export default {
  components: {},
  beforeRouteEnter(to, from, next) {
    if (to.meta.title) {
      to.meta.title = to.query.activeType == 1 && to.query.type == 1 ? '设备列表' : to.query.type == 0 ? '工单列表' : '任务列表'
    }
    next()
  },
  data() {
    return {
      moment,
      type: 2,
      listType: 0,
      systemCode: '',
      activeType: '',
      planId: '',
      filterData: {
        dept: '',
        person: '',
        status: '0',
        date: ''
      },
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      // 周期类型
      cycleTypeList: [
        {
          cycleType: 8,
          label: '单次'
        },
        {
          cycleType: 6,
          label: '每日'
        },
        {
          cycleType: 0,
          label: '每周'
        },
        {
          cycleType: 2,
          label: '每月'
        },
        {
          cycleType: 3,
          label: '季度'
        },
        {
          cycleType: 5,
          label: '全年'
        },
        {
          cycleType: 7,
          label: '自定义'
        }
      ],
      statusOption: [
        {
          id: '2',
          label: '已完成'
        },
        {
          id: '1',
          label: '未完成'
        },
        {
          id: '0',
          label: '全部'
        }
      ],
      taskList: [],
      tableLoading: false,
      dateInterval: [], // 时间区间
      sysTypeId: '', // 系统类型id
      pointCode: '', // 巡检点id
      taskPointName: '', // 点name
      distributionTeamId: '', // 部门id
      taskStatus: '', // 任务状态（1.未完成 2.已完成）
      state: '' // 执行状态
    }
  },
  created() {
    this.systemCode = this.$route.query.systemCode // 巡检/保养
    this.activeType = this.$route.query.activeType // 系统/设备/部门
    this.type = this.$route.query.listType // 0工单 1设备 2总数 3已巡检 4未巡检 5不合格
    this.dateInterval = this.$route.query.taskStartTime?.concat(',' + this.$route.query.taskEndTime).split(',') || []
    if (this.activeType == '1') {
      // 按系统
      this.sysTypeId = this.$route.query.sysTypeId
    } else if (this.activeType == '2') {
      // 按设备
      this.pointCode = this.$route.query.pointCode
      this.taskPointName = this.$route.query.eqName
    } else if (this.activeType == '3') {
      // 按部门
      this.distributionTeamId = this.$route.query.distributionTeamId
    } else if (this.activeType == '4') {
      // 按计划
      this.planId = this.$route.query.planId
    }
    if (this.type == 0) {
    } else if (this.type == 3) {
      this.taskStatus = 2
    } else if (this.type == 4) {
      this.taskStatus = 1
    } else if (this.type == 5) {
      this.state = 3
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      if (this.activeType == 1 && this.type == 1) {
        // 设备
        this.getListDataByAsset()
      } else if (this.type == 0) {
        this.getWorkOrderDataByTask()
      } else {
        this.getListDataByPoint()
      }
    },
    getListDataByAsset() {
      this.tableLoading = true
      const params = {
        currentPage: this.paginationData.currentPage,
        pageSize: this.paginationData.pageSize,
        professionalCategoryCode: this.sysTypeId,
        taskStartTime: this.dateInterval[0] ? this.dateInterval[0] : '',
        taskEndTime: this.dateInterval[1] ? this.dateInterval[1] : '',
        systemCode: this.systemCode
      }
      this.$api.getTaskByAsset(params).then((res) => {
        if (res.code == '200') {
          this.taskList = res.data.assetDetailsList
          this.paginationData.total = res.data.sum
        }
        this.tableLoading = false
      })
    },
    getListDataByPoint() {
      this.tableLoading = true
      const params = {
        currentPage: this.paginationData.currentPage,
        pageSize: this.paginationData.pageSize,
        professionalCategoryCode: this.sysTypeId,
        taskStartTime: this.dateInterval[0] ? this.dateInterval[0] : '',
        taskEndTime: this.dateInterval[1] ? this.dateInterval[1] : '',
        systemCode: this.systemCode,
        distributionTeamId: this.distributionTeamId,
        pointCode: this.pointCode,
        taskPointName: this.taskPointName,
        state: this.state
      }
      if (this.activeType == '2') {
        params.carryOutFlag = this.type == 3 ? 1 : this.type == 4 ? 0 : ''
        params.engineerCode = 1
      } else {
        params.planId = this.planId
        params.taskStatus = this.taskStatus
      }
      this.$api.getTaskListByPoint(params).then((res) => {
        if (res.code == '200') {
          this.taskList = res.data.list
          this.paginationData.total = res.data.sum
        }
        this.tableLoading = false
      })
    },
    getWorkOrderDataByTask() {
      this.tableLoading = true
      const params = {
        currentPage: this.paginationData.currentPage,
        pageSize: this.paginationData.pageSize,
        professionalCategoryCode: this.sysTypeId,
        taskStartTime: this.dateInterval[0] ? this.dateInterval[0] : '',
        taskEndTime: this.dateInterval[1] ? this.dateInterval[1] : '',
        systemCode: this.systemCode,
        distributionTeamId: this.distributionTeamId,
        pointCode: this.pointCode,
        taskPointName: this.taskPointName,
        state: this.state
      }
      this.$api.getWorkOrderByTask(params).then((res) => {
        if (res.code == '200') {
          this.taskList = res.data.list
          this.paginationData.total = res.data.sum
        }
        this.tableLoading = false
      })
    },
    // 周期类型
    cycleTypeFn(row) {
      const item = this.cycleTypeList.filter((i) => i.cycleType == row.cycleType)
      return item[0].label
    },
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.paginationData.currentPage = 1
      this.init()
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.init()
    },
    goDeviceDetail(row) {
      this.$router.push({
        name: 'addDevice',
        query: {
          type: 'details',
          id: row.id,
          assetsId: row.assetsId
        }
      })
    },
    toTaskDetail(row) {
      this.$router.push({
        path: 'taskDetail',
        query: {
          taskId: row.id,
          type: 'detail',
          systemType: this.systemCode
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.contentMain {
  height: 100%;
  .topFilter {
    margin: 15px 15px 0 15px;
    padding: 15px;
    width: calc(100% - 30px);
    height: 50px;
    background-color: #fff;
    .backBar {
      color: #121f3e;
      height: 30px;
    }
  }
  .tableWrap {
    margin: 0 15px 15px 15px;
    padding: 15px;
    height: calc(100% - 85px);
    background-color: #fff;
    .disable {
      color: #414653;
      span {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: #ccced3;
      }
    }
    .enable {
      color: #08cb83;
      span {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: #08cb83;
      }
    }
  }
}
</style>
