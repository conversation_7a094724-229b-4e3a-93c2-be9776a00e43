<template>
  <PageContainer :footer="true">
    <div slot="content" v-loading="formLoading" class="form-content">
      <div class="form-title">区间状态配置</div>
      <ContentCard title="基本信息">
        <div slot="content" class="footer-role">
          <el-form ref="formInline" :model="formInline" :rules="rules">
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="7">
                <el-form-item label="区间监测名称" prop="name" label-width="110px">
                  <el-input v-model="formInline.name" maxlength="16" placeholder="请输入区间监测名称"></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="7">
                <el-form-item label="区间监测编码" prop="code" label-width="110px">
                  <el-input v-model="formInline.code" show-word-limit maxlength="16" placeholder="请输入区间监测编码"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </ContentCard>
      <ContentCard title="区间配置">
        <div slot="content">
          <el-form ref="sonFormInline" :model="sonFormInline" :rules="rules2" :validate-on-rule-change="false">
            <el-row :gutter="24" style="margin: 0 0 20px">
              <el-col :md="7">
                <el-form-item label="区间状态名称" prop="stateName" label-width="110px">
                  <el-input v-model="sonFormInline.stateName" maxlength="16" placeholder="请输入区间状态名称"></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="7">
                <el-form-item label="区间状态颜色" prop="colour" label-width="110px">
                  <el-color-picker v-model="sonFormInline.colour" :predefine="predefineColors"> </el-color-picker>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24" style="margin: 0 0 20px">
              <el-col>
                <el-form-item label="区间类型" label-width="110px">
                  <div class="btn_row">
                    <div
                      v-for="(item, index) in intervalTypeArr"
                      :key="index"
                      :class="{
                        'search-aside-item': true,
                        'search-aside-item-active': sonFormInline.section === item.status
                      }"
                      @click="sonFormInline.section = item.status"
                    >
                      {{ item.name }}
                    </div>
                  </div>
                  <!-- <div v-for="(item, index) in rangeArr" :key="index" class="on_range">
                    <div v-if="sonFormInline.section == 0 || (sonFormInline.section == 1 && index != 1)">
                      <el-select v-model="item.symbol" placeholder="请选择" @change="(e) => symbolChange(e, item, index)">
                        <el-option v-for="item in symbol" :key="item" :label="item" :value="item"> </el-option>
                      </el-select>
                      <el-input v-model="item.value" style="margin-left: 16px" type="number"></el-input>
                    </div>
                  </div> -->
                  <div class="on_range">
                    <div>
                      <el-select v-model="rangeArr[0].symbol" placeholder="请选择">
                        <el-option  label=">" value=">"> </el-option>
                        <el-option  label=">=" value=">="> </el-option>
                        <el-option  label="=" value="="> </el-option>
                        <el-option v-if="sonFormInline.section === 1"  label="<=" value="<="> </el-option>
                        <el-option  v-if="sonFormInline.section === 1" label="<" value="<"> </el-option>
                      </el-select>
                      <el-input v-model="rangeArr[0].value" style="margin-left: 16px" type="number"></el-input>
                    </div>
                  </div>
                  <div class="on_range">
                    <div v-if="sonFormInline.section !== 1">
                      <el-select v-model="rangeArr[1].symbol" placeholder="请选择">
                        <el-option  label="<=" value="<="> </el-option>
                        <el-option  label="<" value="<"> </el-option>
                      </el-select>
                      <el-input v-model="rangeArr[1].value" style="margin-left: 16px" type="number"></el-input>
                    </div>
                  </div>
                  <el-button type="primary" style="margin-left: 150px" @click="addSonFrom">添加</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <el-table ref="table" :resizable="false" border :data="formInline.list" height="250px" style="width: 50%">
            <el-table-column prop="stateName" label="区间状态名称" show-overflow-tooltip />
            <el-table-column prop="section" label="区间类型" show-overflow-tooltip>
              <template slot-scope="scope">
                <span>{{ intervalTypeArr.find((ele) => ele.status == scope.row.section).name }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="maxAndMinStr" label="区间值" show-overflow-tooltip />
            <el-table-column prop="colour" label="状态颜色" show-overflow-tooltip>
              <template slot-scope="scope">
                <span :style="{ color: scope.row.colour }">{{ scope.row.colour }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template slot-scope="scope">
                <el-button type="text" icon="el-icon-delete" @click="delSonRow(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </ContentCard>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">关闭</el-button>
      <el-button v-if="$route.query?.type != 'detail'" type="primary" @click="submitForm()">保存</el-button>
    </div>
  </PageContainer>
</template>
<script>
export default {
  name: 'intervalMonitorForm',
  async beforeRouteLeave(to, from, next) {
    if (!['intervalState'].includes(to.name)) {
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      tableData: [
        {
          stateName: null,
          colour: null
        }
      ],
      rangeArr: [
        { symbol: '>', value: null },
        { symbol: '<', value: null }
      ],
      symbol: ['>', '>=', '=', '<=', '<'],
      intervalTypeArr: [
        { name: '区间', status: 0 },
        { name: '半开区间', status: 1 }
      ],
      // 预定颜色
      predefineColors: [],
      formInline: {
        sectionMenuId: null,
        name: '',
        code: '',
        list: []
      },
      sonFormInline: {
        stateName: '',
        colour: '',
        section: 0
      },
      formLoading: false,
      rules: {
        name: {
          required: true,
          message: '请输入区间监测名称',
          trigger: 'change'
        },
        code: {
          required: true,
          message: '请输入区间监测编码',
          trigger: 'change'
        }
      },
      rules2: {
        stateName: {
          required: true,
          message: '请输入区间状态名称',
          trigger: 'change'
        },
        colour: {
          required: true,
          message: '请选择区间状态颜色',
          trigger: 'change'
        }
      },
      setType: null
    }
  },
  // watch: {
  //   'sonFormInline.section': function(old, val) {
  //     const obj = { symbol: '>', value: null }
  //     if (old == 0) {
  //       this.rangeArr = [obj, obj]
  //     } else {
  //       this.rangeArr = [obj]
  //     }
  //   }
  // },
  activated() {
    this.initEvent()
  },
  mounted() {
    if (!this.$store.state.keepAlive.list.includes('intervalState')) {
      this.initEvent()
    }
  },
  methods: {
    delSonRow(i) {
      this.formInline.list.splice(i, 1)
    },
    sonFormReset() {
      this.sonFormInline = {
        stateName: '',
        colour: '',
        section: 0
      }
      this.rangeArr = [
        { symbol: '>', value: null },
        { symbol: '>', value: null }
      ]
      this.$nextTick(() => {
        this.$refs.sonFormInline.resetFields()
      })
    },
    addSonFrom() {
      this.$refs.sonFormInline.validate((valid) => {
        if (valid) {
          let obj = {
            maxCompare: null,
            maxValue: null,
            minCompare: null,
            minValue: null,
            maxAndMinStr: null,
            ...this.sonFormInline
          }
          if (this.sonFormInline.section == 0) {
            if (!this.rangeArr[0].value || !this.rangeArr[1].value) return this.$message('区间范围值为必填')
            let arr = [...this.rangeArr].sort((a, b) => b.value - a.value)
            obj.maxCompare = arr[0].symbol
            obj.maxValue = arr[0].value
            obj.minCompare = arr[1].symbol
            obj.minValue = arr[1].value
            obj.maxAndMinStr = `${arr[0].symbol}${arr[0].value},${arr[1].symbol}${arr[1].value}`
          } else {
            if (!this.rangeArr[0].value) return this.$message.error('区间范围值为必填')
            obj.minCompare = this.rangeArr[0].symbol
            obj.minValue = this.rangeArr[0].value
            obj.maxAndMinStr = `${this.rangeArr[0].symbol}${this.rangeArr[0].value}`
          }
          try {
            this.formInline.list.push(obj)
            this.sonFormReset()
          } catch {}
        }
      })
    },
    initEvent() {
      // 重置form表单
      const data = this.$options.data()
      delete data.rules
      // this.$route.query.isiId ? this.getImageSelectById(this.$route.query.isiId, 'scadaParameterList') : ''
      this.$nextTick(() => {
        Object.assign(this.$data, data)
        this.$refs.formInline.resetFields()
        this.formInline.sectionMenuId = this.$route.query.entityMenuCode
        this.setType = this.$route.query.type
        this.setType != 'add' ? this.getSurveyByOne() : ''
      })
    },
    // 获取监测项详情
    getSurveyByOne() {
      this.formLoading = true
      this.$api
        .selectSectionOne({
          id: this.$route.query.id
        })
        .then((res) => {
          this.formInline = res.data
          this.formLoading = false
        })
    },
    submitForm() {
      this.$refs.formInline.validate((valid) => {
        if (valid) {
          if (!this.formInline.list.length) return this.$message.error('至少添加一条区间配置')
          this.formLoading = true
          if (this.setType == 'add') {
            // 新增
            this.$api.saveSection(this.formInline, { 'operation-type': 1}).then((res) => {
              this.formLoading = false
              if (res.code == '200') {
                this.$message.success(res.message)
                this.$router.go(-1)
              } else {
                this.$message.error(res.message)
              }
            })
          } else {
            this.$api.updateSection(this.formInline, { 'operation-type': 2, 'operation-id': this.$route.query.id, 'operation-name': this.formInline.name }).then((res) => {
              this.formLoading = false
              if (res.code == 200) {
                this.$message.success(res.message)
                this.$router.go(-1)
              } else {
                this.$message.error(res.message)
              }
            })
          }
        } else {
          return false
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.on_range {
  display: flex;
  margin-top: 10px;
  ::v-deep .el-select,
  .el-input {
    width: 100px !important;
  }
}
::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
  appearance: none !important;
}
::v-deep input[type='\2018number\2019'] {
  appearance: textfield !important;
}
.btn_row {
  border-radius: 4px;
  display: flex;
  align-items: center;
  .search-aside-item {
    display: inline-block;
    font-size: 14px;
    padding: 0 30px;
    height: 32px;
    line-height: 32px;
    font-family: PingFangSC-Regular;
    color: $color-primary;
    border: 1px solid $color-primary;
    background: #fff;
    margin-right: 20px;
    border-radius: 4px;
    cursor: pointer;
    &:hover,
    &:focus {
      color: #fff;
      font-family: PingFangSC-Regular;
      border-color: $color-primary;
      background-color: $color-primary;
      font-weight: 500;
    }
  }
  .search-aside-item-active {
    color: #fff;
    font-family: PingFangSC-Regular;
    border-color: $color-primary;
    background-color: $color-primary;
    font-weight: 500;
  }
}
.form-content {
  height: calc(100% - 0px);
  overflow-y: auto;
  .form-title {
    height: 40px;
    line-height: 40px;
    background: #fff;
    border-bottom: 1px solid #dcdfe6;
    font-size: 15px;
    font-family: 'PingFang SC-Medium', 'PingFang SC';
    font-weight: 500;
    color: #121f3e;
    padding-left: 15px;
  }
  .box-card {
    padding-left: 3%;
    margin-bottom: 3px;
  }
  .form-btn-title {
    font-size: 14px;
    font-family: 'PingFang SC-Regular', 'PingFang SC';
    font-weight: 400;
    color: #121f3e;
    margin-right: 8px;
  }
  .form-btn-btn {
    padding: 4px 10px;
    margin: 0 8px;
  }
  .assets-info {
    font-size: 14px;
    font-family: 'PingFang SC-Regular', 'PingFang SC';
    font-weight: 400;
    color: #121f3e;
    margin-right: 15px;
    > span {
      color: #3562db;
    }
  }
  .assets-info-close {
    cursor: pointer;
    &:hover {
      color: #3562db;
      font-weight: 600;
    }
  }
  .parameter-box {
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    margin-bottom: 20px;
    .parameter-title {
      font-size: 14px;
      font-family: 'PingFang SC-Regular', 'PingFang SC';
      font-weight: 400;
      color: #121f3e;
      background: #faf9fc;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 48px;
      line-height: 48px;
      padding: 0 10px;
      & > span {
        &:first-child {
          font-size: 16px;
        }
      }
    }
    .unit-style {
      font-size: 14px;
      font-family: 'PingFang SC-Regular', 'PingFang SC';
      font-weight: 400;
      color: #121f3e;
      height: 40px;
      line-height: 40px;
    }
  }
  ::v-deep .el-select,
  .el-input {
    width: fit-content;
  }
  .camera-tag {
    background: #f6f5fa;
    border-radius: 4px;
    font-size: 14px;
    font-family: 'PingFang SC-Regular', 'PingFang SC';
    font-weight: 400;
    color: #121f3e;
    border: none;
    margin-right: 8px;
    ::v-deep .el-tag__close {
      color: #121f3e;
      &:hover {
        color: #fff;
        background-color: #3562db;
      }
    }
  }
}
</style>
