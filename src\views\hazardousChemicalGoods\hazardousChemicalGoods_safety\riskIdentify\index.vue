<script>
import tableListMixin from '@/mixins/tableListMixin'
export default {
  name: 'riskIdentify',
  mixins: [tableListMixin],
  data() {
    return {
      tableData: [],
      multipleSelection: [],
      tableLoadingStatus: false
    }
  },
  mounted() {
    this.getDataList()
  },
  methods: {
    // 获取列表数据
    getDataList() {
      this.tableData = []
      this.tableLoadingStatus = true
      const params = {
        pageSize: this.pagination.size,
        currentPage: this.pagination.current
      }
      this.$api
        .queryRiskHouseByPage(params)
        .then((res) => {
          this.tableLoadingStatus = false
          if (res.code === '200') {
            this.tableData = res.data.list
            this.pagination.total = res.data.sum
          } else {
            this.tableData = []
            this.pagination.total = 0
          }
        })
        .catch((err) => {
          this.tableLoadingStatus = false
        })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 下载文件
    downLoad(val) {
      let url = this.$tools.imgUrlTranslation(val)
      window.open(url)
    },
    //操作
    onOperate(type, row) {
      if (type === 'add') {
        this.$router.push({
          name: 'riskIdentifyRecordAdd',
          query: {
            type: 'add',
            warehouseId: row.id
          }
        })
      } else if (type === 'details') {
        this.$router.push({
          name: 'riskIdentifyRecordAdd',
          query: {
            type: 'view',
            warehouseId: row.id
          }
        })
      }
    }
  }
}
</script>
<template>
  <PageContainer class="riskIdentify">
    <template #content>
      <div class="riskIdentify__table">
        <el-table
          v-loading="tableLoadingStatus"
          height="100%"
          :data="tableData"
          border
          stripe
          table-layout="auto"
          class="tableAuto"
          row-key="id"
          @row-dblclick="(row) => onOperate('details', row)"
          title="双击列表查看详情"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="index" width="70" label="序号"> </el-table-column>
          <el-table-column prop="warehouseName" show-overflow-tooltip label="仓库名称"></el-table-column>
          <el-table-column prop="gridName" show-overflow-tooltip label="位置"></el-table-column>
          <el-table-column prop="riskName" show-overflow-tooltip label="风险等级"></el-table-column>
          <el-table-column prop="manageName" show-overflow-tooltip label="责任人"></el-table-column>
          <el-table-column prop="remark" show-overflow-tooltip label="说明"></el-table-column>
          <el-table-column prop="file" label="文件" show-overflow-tooltip>
            <template #default="{ row }">
              <el-popover placement="top" width="400" trigger="click" v-if="row.files && row.files.length">
                <div class="filePopover" v-for="(f1, index) in row.files" :key="index">
                  <span> {{ f1.name }}</span>
                  <span class="profile ml-16" @click="downLoad(f1.url)">下载</span>
                </div>
                <span slot="reference" class="profile">点击查看</span>
              </el-popover>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150px">
            <template #default="{ row }">
              <el-button type="text" v-auth="'riskIdentificationAndAnalysis:check'" @click="onOperate('details', row)">查看</el-button>
              <el-button type="text" v-auth="'riskIdentificationAndAnalysis:add'" @click="onOperate('add', row)">新增</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        class="riskIdentify__pagination"
        :current-page="pagination.page"
        :page-sizes="pagination.pageSizeOptions"
        :page-size="pagination.size"
        :layout="pagination.layoutOptions"
        :total="pagination.total"
        @size-change="paginationSizeChange"
        @current-change="paginationCurrentChange"
      >
      </el-pagination>
    </template>
  </PageContainer>
</template>
<style scoped lang="scss">
.riskIdentify {
  ::v-deep(> .container-content) {
    padding: 16px;
    height: 100%;
    width: 100%;
    background-color: #fff;
  }
  &__table {
    height: calc(100% - 60px);
  }
  &__pagination {
    margin-top: 10px;
  }
}
::v-deep .ml-16 {
  margin-left: 16px;
}
::v-deep .profile {
  color: #008af4;
  cursor: pointer;
}
::v-deep .filePopover {
  max-height: 100px;
  overflow-y: auto;
  display: flex;
}
</style>
