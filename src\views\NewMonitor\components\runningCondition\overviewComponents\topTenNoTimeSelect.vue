<!-- 运行监测 -->
<template>
  <ContentCard :title="item.componentTitleShow" :scrollbarHover="true" :cstyle="{ width: '100%', height: '100%' }"
    class="drag_class" :hasMoreOper="['edit']"
    @more-oper-event="(val) => $emit('all-more-Oper', val, item.componentName, item.chartType)">
    <div slot="content" style="width: 100%; height: 100%; display: flex">
      <echarts :ref="`topTenNoTimeSelectEchars${item.componentDataType}`"
        :domId="`topTenNoTimeSelectEchars${item.componentDataType}`" width="100%" height="100%" />

      <!-- <div style="width: 100%; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center; padding: 50px">
        <img src="@/assets/images/newMonitor/no-chat.png" />
        <span>暂无数据</span>
      </div> -->
    </div>
  </ContentCard>
</template>

<script>
import moment from 'moment'
import * as echarts from 'echarts'
export default {
  name: 'topTenNoTimeSelect',
  props: {
    item: {
      type: Object,
      default: () => { }
    },
    systemCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {}
  },
  mounted() {
    setTimeout(() => {
      this.getTopTenData()
    }, 100)
  },
  methods: {
    getTopTenData(id) {
      const payload = {
        componentDataType: this.item.componentDataType,
        timePeriod: 'THIS_MONTH',
        systemCode: this.systemCode,
        groupId: id || ""
      }
      this.$api.getElevatorRunTimeData(payload).then((res) => {
        if (res.code === "200") {
          this.appendEchartsData(res.data)
        } else {
          this.hasChart = false
        }
      })

    },
    appendEchartsData(data) {
      const baseData = {
        grid: {
          left: '0%',
          right: '50px',
          bottom: '5%',
          top: '5%',
          containLabel: true
        },
        xAxis: {
          max: 'dataMax',
          show: false
        },
        yAxis: {
          type: 'category',
          data: [],
          inverse: true,
          axisLabel: {
            formatter: function (value) {
              return value.length > 5 ? value.substring(0, 5) + '...' : value; // 截断文字
            }
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{b} : {c}',
          confine: true
        },
        series: [
          {
            realtimeSort: true,
            name: 'X',
            type: 'bar',
            data: [],
            label: {
              show: true,
              position: 'right',
              valueAnimation: true
            }
          }
        ]
      }
      if (data && data.offlineDevices && data.offlineDevices.length > 0) {
        data.offlineDevices.forEach((item) => {
          baseData.yAxis.data.push(item.assetsName)
          baseData.series[0].data.push(item.value)
        })
      }
      this.$refs[`topTenNoTimeSelectEchars${this.item.componentDataType}`].init(baseData)
    }
  }
}
</script>

<style lang="scss" scoped>
.data-btns {
  position: absolute;
  right: 7%;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &>span {
    width: 44px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    margin: 0 4px;
    background-color: #f6f5fa;
    font-size: 14px;
    font-family: 'PingFang SC-Medium', 'PingFang SC';
    border: none;
    border-radius: 2px;
    color: #7f848c;
    cursor: pointer;
  }

  .active-btn {
    background-color: #e6effc !important;
    color: #3562db !important;
    border-color: #e6effc !important;
  }
}

.operation-list {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
}
</style>
