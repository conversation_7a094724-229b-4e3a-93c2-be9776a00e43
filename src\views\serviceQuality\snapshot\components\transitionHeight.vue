<template>
  <div :ref="heigtRef" class="container">
    <slot></slot>
  </div>
</template>

<script>
export default {
  props: {
    // show: {
    //   type: Boolean,
    //   default: false
    // },
    heigtRef: {
      type: String,
      default: 'container'
    }
  },
  data() {
    return {
      height: 0,
      show: false
    }
  },
  watch: {
    show(newVal) {
      // console.log(newVal)
      this.$refs[this.heigtRef].style.height = newVal ? `${this.height}px` : 0
    }
  },
  mounted() {
    // console.log(this.heigtRef)
    this.$nextTick().then(() => {
      this.height = this.$refs[this.heigtRef].offsetHeight
      this.$refs[this.heigtRef].style.height = this.show ? `${this.height}px` : 0
    })
  }
}
</script>

<style scoped>
.container {
  transition: height 0.3s linear;
  overflow: hidden;
}
</style>
