<!-- 运行监测 -->
<template>
  <ContentCard :title="item.componentTitleShow" :scrollbarHover="true" :cstyle="{ width: '100%', height: '100%' }"
    class="drag_class" :hasMoreOper="['edit']"
    @more-oper-event="(val) => $emit('all-more-Oper', val, item.componentName, item.chartType)">
    <div slot="title-right" class="data-btns">
      <el-select v-model="devicename" placeholder="设备名称" style="margin-top: 5px" filterable clearable
        @change="selectCategory">
        <el-option v-for="(item, index) in deviceTypeList" :key="index" :label="item.assetsName"
          :value="item.id"></el-option>
      </el-select>
      <span @click="videoClick()"><img src="../../../../../assets/images/newMonitor/fullscreen.png" alt="" /></span>
    </div>
    <div slot="content" style="width: 100%; height: 100%">
      <el-carousel v-if="videoData.length" ref="elevatorCarousel" class="elevator_carousel" arrow="always"
        :autoplay="false" @change="changeCarousel">
        <el-carousel-item v-for="item in videoData" :key="item.id" :name="item.name">
          <rtspCavas ref="rtspCavas" :rtspUrl="item.url" :videoName="item.name" :hasCavas="Boolean(item.url)"
            class="video_preview"></rtspCavas>
        </el-carousel-item>
      </el-carousel>
      <div v-else><img src="../../../../../assets/images/alarmCenter/nopic.png" alt=""
          style="margin-left: 22px; width: 92%" /></div>
      <template v-if="videoDialgoShow">
        <yssVideoDialgo :videoDialgoShow="videoDialgoShow" :title="title1" :factoryCode="factoryCode"
          :systemCode="systemCode" @submitDialog="submitParamsGather" @closeVideoDialog="closeVideoDialog" />
      </template>
    </div>
  </ContentCard>
</template>
<script>
import yssVideoDialgo from '../components/yssVideoDialgo.vue'
export default {
  name: 'jkhm',
  components: {
    yssVideoDialgo
  },
  props: {
    item: {
      type: Object,
      default: () => { }
    },
    systemCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      devicename: '', // 设备名称
      deviceTypeList: [],
      videoDialgoShow: false,
      title1: '监控画面',
      elevatorOptions: [],
      factoryCode: '',
      videoData: [],
      videoId: ''
    }
  },
  mounted() {
    this.getDataList()
    this.jkhmElevatorList()
  },
  methods: {
    // 设备名称
    getDataList() {
      let data = {
        sysOfCode: this.systemCode,
        sysOf1Code: '',
        groupId: ''
      }
      this.deviceTypeList = []
      this.$api
        .getMasterOrMonitoredByCode(data)
        .then((res) => {
          if (res.code === '200' || res.code === 200) {
            this.deviceTypeList = res.data
            this.jkhmElevatorList()
          }
        })
        .catch(() => { })
    },
    selectCategory(id) {
      this.jkhmElevatorList(id)
    },
    // 初始化获取所有监控状态数据
    jkhmElevatorList(id) {
      this.videoId = id
      let data = {
        assetsId: id || '',
        spaceId: '',
        sysOf1Code: '',
        sysOfCode: this.systemCode
      }
      this.$api
        .getRelatedCameraList(data)
        .then((res) => {
          if (res.code == 200 || res.code == '200') {
            this.elevatorOptions = res.data
            this.playVideoByElevatorId(this.elevatorOptions[0])
            // if (this.elevatorOptions.length) {
            //   this.elevatorOptions.forEach(option => {
            //     this.playVideoByElevatorId(option);
            //   });
            // } else {

            // }
          } else {
            this.playVideoByElevatorId()
          }
        })
    },
    // 点击视频出大图
    videoClick() {
      const currentIndex = this.$refs.elevatorCarousel.activeIndex
      const currentVideo = this.videoData[currentIndex]
      this.factoryCode = currentVideo
      this.videoDialgoShow = true
    },
    // 通过轮播图改变选中电梯
    changeCarousel(val) {
      const selectData = this.elevatorOptions[val]
      this.playVideoByElevatorId(selectData); // 选择对应的视频
    },
    // 播放电梯监控
    playVideoByElevatorId(selectData) {
      if (selectData) {
        this.videoData = []
        this.$api.getQueryInstanceFunction(selectData.factoryCode, 'previewStreaming', {}).then((res) => {
          if (res.status === 200) {
            if (res.result.length && res.result[0].url) {
              this.videoData.push({
                id: selectData.factoryCode,
                name: selectData.assetsName,
                url: res.result[0].url
              });
            } else {
              this.videoData = []
            }
          } else {
            // this.$message.error(res.message)
          }
        })
      } else {
        this.videoData = []
      }
    },
    submitParamsGather() {
      this.videoDialgoShow = false
    },
    closeVideoDialog() {
      this.videoDialgoShow = false
    }
  }
}
</script>
<style lang="scss" scoped>
.data-btns {
  position: absolute;
  right: 7%;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &>span {
    width: 44px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    margin: 0 4px;
    background-color: #f6f5fa;
    font-size: 14px;
    font-family: 'PingFang SC-Medium', 'PingFang SC';
    border: none;
    border-radius: 2px;
    color: #7f848c;
    cursor: pointer;
  }

  .active-btn {
    background-color: #e6effc !important;
    color: #3562db !important;
    border-color: #e6effc !important;
  }
}

.operation-list {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
}

.elevator_carousel {
  height: calc(100% - 5px);
  margin-bottom: 10px;
  display: flex;
  justify-content: center;

  ::v-deep .el-carousel__container {
    height: 100%;
    width: 90%;

    .el-carousel__arrow--left {
      left: -22px;
    }

    .el-carousel__arrow--right {
      right: -22px;
    }

    .el-carousel__arrow i {
      font-size: 26px;
      color: #3562db;
    }

    .el-carousel__item {
      display: flex;

      .elevator_img {
        margin: auto;
      }
    }

    .el-carousel__arrow {
      width: 24px;
      height: 44px;
      background-color: transparent;
      border-radius: unset;
    }

    .el-carousel__arrow:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }

  ::v-deep .el-carousel__indicators {
    display: none;
  }
}
</style>
