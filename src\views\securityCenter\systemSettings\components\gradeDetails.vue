<template>
  <div>
    <el-dialog custom-class="model-dialog" title="配置修改" :visible="dialogVisible" @close="closeDialog">
      <div v-loading class="content" style=" width: 100%; background-color: #fff; padding: 10px;">
        <el-form ref="formInline" :model="formInline" :inline="true" :rules="rules" class="advanced-search-form" label-position="right" label-width="90px">
          <el-form-item label="分值：" prop="score">
            <el-input
              v-model="formInline.score"
              placeholder="请输入分值"
              style="width: 350px;"
              oninput="value=value.replace(/[^\d.]|(\.\d{3})|(\.\.)/g,'')"
              @input="handleInputMin"
            ></el-input>
          </el-form-item>
          <br />
          <el-form-item label="说明：" prop="judgeExplain">
            <el-input
              v-model="formInline.judgeExplain"
              type="textarea"
              :rows="3"
              style="width: 350px;"
              placeholder="请输入说明"
              show-word-limit
              maxlength="200"
              resize="none"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" plain @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="closeDialogRole">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script type="text/ecmascript-6">
export default {
  props: {
    dialogVisible: {
      type: Boolean
    },
    multipleSelection: {}
  },
  data() {
    return {
      formInline: {
        score: '',
        judgeExplain: '',
        riskJudgeId: ''
      },
      rules: {
        score: [{ required: true, message: '请输入分值', trigger: 'change' }],
        judgeExplain: [
          { required: true, message: '请输入说明', trigger: 'change' }
        ]
      }
    }
  },
  watch: {
    multipleSelection() {
      if (this.multipleSelection) {
        this.formInline.score = this.multipleSelection.score
        this.formInline.judgeExplain = this.multipleSelection.judgeExplain
        this.formInline.riskJudgeId = this.multipleSelection.id
      }
    }
  },
  mounted() {},
  methods: {
    handleInputMin(value) {
      if (value != '') {
        if (value.indexOf('.') > -1) {
          this.formInline.score = value.slice(0, value.indexOf('.') + 3)
        } else {
          this.formInline.score = value
        }
      }
    },
    closeDialogRole() {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          let data = { ...this.formInline }
          this.$api.ipsmUpdateRiskJudge(data).then((res) => {
            if (res.code == 200) {
              this.$message.success(res.message)
              this.$emit('closeDialogRole')
            } else {
              this.$message.error(res.message)
            }
          })
        }
      })
    },
    closeDialog() {
      this.$emit('closeDialog')
    }
  }
}
</script>
