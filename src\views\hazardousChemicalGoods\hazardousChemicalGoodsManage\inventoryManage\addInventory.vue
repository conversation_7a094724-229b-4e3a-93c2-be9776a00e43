<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="inventory-content-title"><i class="el-icon-arrow-left" @click="$router.go(-1)"></i> {{ inventoryType === 'add' ? '新建盘点' : '盘点详情' }}</div>
      <div class="content_box">
        <el-form ref="formRef" :model="formModel" :rules="rules" label-width="auto" class="formRef">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="选择仓库" prop="warehouseId">
                <el-select v-if="inventoryType === 'add'" v-model="formModel.warehouseId" filterable placeholder="默认选择第一个" @change="warehouseChange">
                  <el-option v-for="item of warehouseOptions" :key="item.id" :label="item.warehouseName" :value="item.id"></el-option>
                </el-select>
                <span v-else class="detailContent">{{ formModel.warehouseName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="月份" prop="month">
                <el-date-picker
                  v-if="inventoryType === 'add'"
                  v-model="formModel.month"
                  value-format="yyyy-MM"
                  format="yyyy-MM"
                  @change="monthChange"
                  type="month"
                  placeholder="年月"
                >
                </el-date-picker>
                <span v-else class="detailContent">{{ formModel.month }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="地址" prop="warehouseAddress">
                <el-input v-if="inventoryType === 'add'" v-model="formModel.warehouseAddress" placeholder="带出仓库地址" disabled> </el-input>
                <span v-else class="detailContent">{{ formModel.warehouseAddress }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="责任人" prop="contacts">
                <el-input v-if="inventoryType === 'add'" v-model="formModel.contacts" placeholder="带出当前仓库责任人" disabled> </el-input>
                <span v-else class="detailContent">{{ formModel.contacts }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="责任人电话" prop="contactsPhone">
                <el-input v-if="inventoryType === 'add'" v-model="formModel.contactsPhone" placeholder="带出当前仓库责任人电话" disabled> </el-input>
                <span v-else class="detailContent">{{ formModel.contactsPhone }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="检查人" prop="checkName">
                <el-input v-if="inventoryType === 'add'" v-model="formModel.checkName" placeholder="当前登录人" disabled> </el-input>
                <span v-else class="detailContent">{{ formModel.checkName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="检查人电话" prop="checkPhone">
                <el-input v-if="inventoryType === 'add'" v-model="formModel.checkPhone" placeholder="当前登录人电话" disabled> </el-input>
                <span v-else class="detailContent">{{ formModel.checkPhone }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <div class="inventoryInfo">
            <div class="toptip">
              <span class="green_line"></span>
              盘点危化品信息
            </div>
            <div class="tips" v-if="inventoryType === 'add'">
              <el-button type="primary" @click="onOperate('check')" :disabled="isStartCheck"> 开始盘点 </el-button>
              <span>盘点截至时间：点击开始盘点的时间 年月日时分秒</span>
            </div>
          </div>
          <div class="inventory_table" v-if="inventoryType === 'add'">
            <el-table v-loading="tableLoadingStatus" height="100%" style="width: 100%" :data="tableData" border stripe class="tableAuto" row-key="id">
              <el-table-column prop="materialName" show-overflow-tooltip label="危化品名称"></el-table-column>
              <el-table-column prop="model" show-overflow-tooltip label="规格型号"></el-table-column>
              <el-table-column prop="basicUnitName" show-overflow-tooltip label="基础单位"></el-table-column>
              <el-table-column v-if="isShow" prop="lastRemainder" show-overflow-tooltip label="上月结存"></el-table-column>
              <el-table-column v-if="isShow" prop="currentIn" show-overflow-tooltip label="本月入库"></el-table-column>
              <el-table-column v-if="isShow" prop="currentOut" show-overflow-tooltip label="本月出库"> </el-table-column>
              <el-table-column v-if="isShow" prop="remainder" show-overflow-tooltip label="结存"></el-table-column>
              <el-table-column prop="discrepancy" show-overflow-tooltip label="差异" v-if="isShow">
                <template #default="{ row }">
                  <el-input v-model="row.discrepancy" size="small" placeholder="输入" clearable maxlength="20" show-word-limit></el-input>
                </template>
              </el-table-column>
              <el-table-column prop="remark" show-overflow-tooltip label="备注" v-if="isShow">
                <template #default="{ row }">
                  <el-input v-model="row.remark" size="small" placeholder="输入" clearable maxlength="20" show-word-limit></el-input>
                </template>
              </el-table-column>
              <el-table-column label="操作" v-if="isShow">
                <template #default="{ row }">
                  <el-button type="text" class="text-red" @click="onOperate('delete', row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="inventory_table" v-else>
            <el-table v-loading="tableLoadingStatus" height="100%" style="width: 100%" :data="tableData" border stripe class="tableAuto" row-key="id">
              <el-table-column prop="materialName" show-overflow-tooltip label="危化品名称"></el-table-column>
              <el-table-column prop="model" show-overflow-tooltip label="规格型号"></el-table-column>
              <el-table-column prop="basicUnitName" show-overflow-tooltip label="基础单位"></el-table-column>
              <el-table-column prop="lastRemainder" show-overflow-tooltip label="上月结存"></el-table-column>
              <el-table-column prop="currentIn" show-overflow-tooltip label="本月入库"></el-table-column>
              <el-table-column prop="currentOut" show-overflow-tooltip label="本月出库"> </el-table-column>
              <el-table-column prop="remainder" show-overflow-tooltip label="结存"></el-table-column>
              <el-table-column prop="discrepancy" show-overflow-tooltip label="差异"></el-table-column>
              <el-table-column prop="remark" show-overflow-tooltip label="备注"> </el-table-column>
            </el-table>
          </div>
        </el-form>
      </div>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">取消</el-button>
      <el-button type="primary" :disabled="!isCheck" :loading="checkLoading" @click="submitForm" v-if="inventoryType === 'add'">盘点确认</el-button>
    </div>
  </PageContainer>
</template>
<script>
import moment from 'moment'
export default {
  name: 'addInventory',
  data() {
    return {
      isSelectPers: false, // 选择人员
      selectType: '', //选择人员类型
      // 正常表单
      formModel: {
        warehouseName: '', // 仓库名称
        warehouseId: '', //仓库id
        warehouseAddress: '', //仓库地址
        month: '', //年月
        contacts: '', //责任人
        contactsPhone: '', //联系方式
        checkId: '', //检查人id
        checkName: '', //检查人名称
        checkPhone: '', //检查人电话
        takeStockTime: '' //盘点时间
      },
      rules: {
        warehouseId: [{ required: true, message: '请选择仓库', trigger: 'change' }],
        month: [{ required: true, message: '请选择年月', trigger: 'change' }]
      },
      warehouseOptions: [], //上级库房
      inventoryId: '', //盘点id
      tableLoadingStatus: false,
      tableData: [],
      isShow: false, //列显示
      checkLoading: false, //盘点loading
      isCheck: false, //是否开始盘点
      isStartCheck: false, //是否可以点击盘点
      inventoryId: '', //盘点id
      inventoryType: '', //类型 add新增 edit编辑
      takeStockList: []
    }
  },

  mounted() {
    this.inventoryType = this.$route.query.type
    const userInfo = this.$store.state.user.userInfo.user
    this.formModel.checkId = userInfo.staffId
    this.formModel.checkName = userInfo.staffName
    this.formModel.checkPhone = userInfo.phone
    if (this.$route.query.inventoryId) {
      this.inventoryId = this.$route.query.inventoryId
      this.getInventoryInfo()
    } else {
      this.getWarehouseListFn()
    }
  },
  methods: {
    //获取盘点详情
    getInventoryInfo() {
      this.$api.getTakeStockById({ id: this.inventoryId }).then((res) => {
        if (res.code == '200') {
          this.formModel = res.data
          this.tableData = res.data.takeStockSlaveList
        }
      })
    },
    // 获取库房下拉
    getWarehouseListFn() {
      this.$api.getWarehouseList({ status: '0' }).then((res) => {
        if (res.code == '200') {
          this.warehouseOptions = res.data.list
          this.formModel.warehouseId = this.warehouseOptions[0].id
          this.formModel.warehouseName = this.warehouseOptions[0].warehouseName
          this.getWarehouseDetailData(this.formModel.warehouseId)
          this.getHCSData()
        }
      })
    },
    //获取库房详情
    getWarehouseDetailData(e) {
      const userInfo = this.$store.state.user.userInfo.user
      let params = {
        userId: userInfo.staffId,
        userName: userInfo.staffName,
        id: e
      }
      this.$api.getWarehouseById(params).then((res) => {
        if (res.code == '200') {
          this.formModel.warehouseAddress = res.data.warehouseAddress
          this.formModel.contacts = res.data.manageName
          this.formModel.contactsPhone = res.data.managePhone
        }
      })
    },
    //仓库选择取仓库信息
    warehouseChange(val) {
      if (val) {
        this.isStartCheck = false
        //切换仓库时，table有数据就更新数据，没有数据把当前最后一条数据清空
        if (
          !this.takeStockList[this.takeStockList.length - 1] ||
          !this.takeStockList[this.takeStockList.length - 1].takeStockSlaveList ||
          !this.takeStockList[this.takeStockList.length - 1].takeStockSlaveList.length
        ) {
          this.takeStockList[this.takeStockList.length - 1] = []
          this.tableData = []
        } else {
          //切换时更新table数据
          if (this.takeStockList && this.takeStockList.length) {
            this.takeStockList[this.takeStockList.length - 1].takeStockSlaveList = this.tableData
          }
        }
        this.formModel.warehouseName = this.warehouseOptions.find((item) => item.id == val).warehouseName
        this.getWarehouseDetailData(val)
        this.getHCSData()
      }
    },
    getHCSData() {
      let params = {
        selectSign: 0,
        warehouseId: this.formModel.warehouseId
      }
      this.$api.getTakeStockData(params).then((res) => {
        if (res.code == '200') {
          this.tableData = res.data.materialInfoList
          this.isShow = false
        }
      })
    },
    //月份改变获取列表
    monthChange(e) {
      this.isStartCheck = false
      //切换月份时，table有数据就更新数据，没有数据把当前最后一条数据清空
      if (
        !this.takeStockList[this.takeStockList.length - 1] ||
        !this.takeStockList[this.takeStockList.length - 1].takeStockSlaveList ||
        !this.takeStockList[this.takeStockList.length - 1].takeStockSlaveList.length
      ) {
        this.takeStockList[this.takeStockList.length - 1] = []
        this.tableData = []
      } else {
        //切换时更新table数据
        if (this.takeStockList && this.takeStockList.length) {
          this.takeStockList[this.takeStockList.length - 1].takeStockSlaveList = this.tableData
        }
      }
      this.getHCSData()
    },
    onOperate(type, row) {
      if (type === 'check') {
        this.formModel.takeStockTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
        this.$refs['formRef'].validate((valid) => {
          if (valid) {
            let params = {
              selectSign: 1,
              month: this.formModel.month,
              warehouseId: this.formModel.warehouseId
            }
            this.$api.getTakeStockData(params).then((res) => {
              if (res.code == '200') {
                this.tableData = res.data
                this.isShow = true
                //每次盘点保存一遍数据,只有table有数据才会给数组push数据
                if (this.tableData && this.tableData.length) {
                  this.takeStockList.push({
                    ...this.formModel,
                    takeStockSlaveList: this.tableData
                  })
                }
                this.isCheck = true
                this.isStartCheck = true
              }
            })
          }
        })
      } else if (type === 'delete') {
        this.tableData = this.tableData.filter((obj) => obj.materialCode !== row.materialCode)
      }
    },
    // 点击确定
    submitForm() {
      //不切换时更新最后一次table数据
      if (this.tableData && this.tableData.length && this.takeStockList.length) {
        this.takeStockList[this.takeStockList.length - 1].takeStockSlaveList = this.tableData
      }
      this.$refs['formRef'].validate((valid) => {
        if (valid) {
          this.checkLoading = true
          let params = {
            id: '',
            takeStockList: this.takeStockList
          }
          this.$api.saveTakeStockData(params).then((res) => {
            this.checkLoading = false
            if (res.code == '200') {
              this.$message.success(res.message)
              this.$router.go(-1)
            } else {
              this.$message.error(res.message)
            }
          })
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.container-content > div {
  background-color: #fff;
  height: 100%;
  position: relative;
  .formRef {
    width: 100%;
    height: 100%;
  }
  .inventory-content-title {
    border-bottom: 1px solid #e4e7ed;
    padding: 12px 24px;
    cursor: pointer;
    margin-bottom: 24px;
  }
  .inventory_table {
    height: calc(60% - 20px);
  }
  .content_box {
    padding: 0 24px;
    background: #fff;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    height: 100%;
    .inventoryInfo {
      display: flex;
      justify-content: space-between;
    }

    .tips {
      span {
        margin-left: 10px;
      }
    }
    .toptip {
      box-sizing: border-box;
      height: 50px;
      width: 50% !important;
      line-height: 50px;
      display: flex;
      font-size: 16px;
      align-items: center;
      border: none;
    }
    .green_line {
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 2px;
      background: #3562db;
      margin-right: 10px;
      vertical-align: middle;
    }
    .text-red {
      color: #ff1919;
    }
  }
  .el-input,
  .el-select,
  .el-cascader {
    width: 80%;
  }
}
.detailContent {
  display: inline-block;
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>

