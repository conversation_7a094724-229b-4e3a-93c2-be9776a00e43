<template>
    <!-- 图形绘制 -->
    <PageContainer>
        <div slot="content" class="monitor-content">

            图形绘制
        </div>
    </PageContainer>
</template>
  
<script>

export default {
    name: '',
    components: {

    },
    data() {
        return {
            treeLoading: true,

        }
    },
    computed: {},
    created() {

    },
    methods: {

    }
}
</script>
  
<style lang="scss" scoped>
.monitor-content {
    height: 100%;
    display: flex;

}
</style>
  