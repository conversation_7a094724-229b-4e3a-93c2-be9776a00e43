<template>
  <PageContainer>
    <div slot="content" class="role-content" style="height: 100%;">
      <div class="role-content-left">
        <div class="toptip">
          <span class="green_line"></span>
          数据字典
        </div>
        <div v-loading class="left_content">
          <el-tree ref="tree" style="margin-top: 10px;" :check-strictly="true" :data="treeData" :props="defaultProps"
            node-key="dictType" :highlight-current="true" :default-expanded-keys="expanded"
            @node-click="handleNodeClick"></el-tree>
        </div>
      </div>
      <div class="role-content-right">
        <div v-if="checkedData.dictType != 'equipment_category_type'" style="height: 100%;">
          <div class="search-from">
            <el-input v-model.trim="filters.dictName" placeholder="字典名称" style="width: 200px;" clearable></el-input>
            <el-button type="primary" plain @click="resetForm">重置</el-button>
            <el-button type="primary" @click="searchForm">查询</el-button>
            <el-button type="primary" icon="el-icon-plus" @click="openDialog('add')">新增</el-button>
          </div>
          <div class="contentTable">
            <div class="contentTable-main table-content">
              <el-table v-loading="tableLoading" title="双击查看详情" border style="width: 100%;" :data="tableData"
                :height="tableHeight" stripe @row-dblclick="dblclick">
                <el-table-column type="index" label="序号" width="70">
                  <template slot-scope="scope">
                    <span>{{ (pagination.current - 1) * pagination.size + scope.$index + 1 }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="dictName" label="字典名称" width="150" show-overflow-tooltip></el-table-column>
                <el-table-column prop="dictCode" label="字典编码" width="140" show-overflow-tooltip></el-table-column>
                <el-table-column prop="sort" label="排序号" width="140" show-overflow-tooltip></el-table-column>
                <el-table-column prop="updateDate" show-overflow-tooltip label="更新时间"></el-table-column>
                <el-table-column prop="description" show-overflow-tooltip label="备注信息"></el-table-column>
                <el-table-column show-overflow-tooltip label="操作">
                  <template slot-scope="scope">
                    <el-button type="text" @click="openDialog('edit', scope.row)">编辑</el-button>
                    <el-button type="text" :disabled="scope.row.isDefault==='0'"
                      :class="[scope.row.isDefault==='0'?' noDelete':'delete'  ]"
                      @click="deleteFn(scope.row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="contentTable-footer">
              <el-pagination :current-page="pagination.current" :page-sizes="pagination.pageSizeOptions"
                :page-size="pagination.size" :layout="pagination.layoutOptions" :total="pagination.total"
                @size-change="paginationSizeChange" @current-change="paginationCurrentChange">
              </el-pagination>
            </div>
          </div>
        </div>
        <!-- 设备类别列表 -->
        <equipment-category v-else></equipment-category>
      </div>
      <el-dialog v-dialogDrag :visible.sync="dialogVisible" :before-close="dialogClosed" :modal="false"
        :close-on-click-modal="false" :title="dialogTitle" width="40%" custom-class="model-dialog">
        <div class="content" style="padding: 10px;">
          <el-form ref="formInline" :model="formInline" :rules="rules" :inline="true" label-position="right"
            label-width="110px">
            <el-form-item label="字典名称：" prop="dictName">
              <el-input v-model="formInline.dictName" :disabled="disabled" placeholder="请输入字典名称" autocomplete="off"
                :maxlength="50" show-word-limit style="width: 260px;"></el-input>
            </el-form-item>
            <el-form-item label="字典编码：" prop="dictCode">
              <el-input v-model.number="formInline.dictCode" :disabled="disabled" placeholder="请输入字典编码"
                autocomplete="off" show-word-limit :maxlength="checkedData.dictType == 'lnspection_type' ? 20 : 6"
                style="width: 260px;"></el-input>
            </el-form-item>
            <br />
            <el-form-item label="排序号：" prop="sort">
              <el-input v-model.number="formInline.sort" :disabled="disabled" placeholder="请输入排序号" maxlength="6"
                autocomplete="off" style="width: 260px;"></el-input>
            </el-form-item>
            <br />
            <el-form-item label="备注信息：" prop="description" class="sino-form-textarea">
              <el-input v-model.trim="formInline.description" show-word-limit placeholder="请输入备注信息"
                class="sino-textarea" style="width: 400px;" maxlength="200" type="textarea" :disabled="disabled"
                :autosize="{ minRows: 4, maxRows: 6 }"></el-input>
            </el-form-item>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="dialogClosed">取 消</el-button>
          <el-button v-if="!disabled" type="primary" @click="submit">确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </PageContainer>
</template>

<script>
import tableListMixin from '@/mixins/tableListMixin.js'
// import { timingSafeEqual } from 'crypto'
import equipmentCategory from './components/equipmentCategory.vue'
export default {
  name: 'dictionaryManagement',
  components: {
    equipmentCategory
  },
  mixins: [tableListMixin],
  data() {
    return {
      treeData: [],
      checkedData: [], // 选中tree数据
      defaultProps: {
        label: 'dictName',
        value: 'dictType'
      },
      expanded: [],
      filters: {
        dictName: ''
      },
      tableLoading: false,
      tableData: [],
      dialogVisible: false,
      disabled: false,
      riskLevelList: [], // 标签类别列表
      dialogTitle: '',
      formInline: {
        dictName: '',
        dictCode: '',
        sort: '',
        description: ''
      },
      rules: {
        dictName: [{ required: true, message: '请输入字典名称', trigger: 'blur' }],
        dictCode: [{ required: true, message: '请输入字典编码', trigger: 'blur' }]
      },
      dictData: []
    }
  },
  created() {
    this.sysDictType()
  },
  methods: {
    // 获取字典参数
    sysDictType() {
      let data = {
        systemIdentificationClassification: this.$route.meta.type, // 1（设施设备巡检）2（综合巡检系统）
        pageNo: 1,
        pageSize: 99999
      }
      this.$api.sysDictType(data).then((res) => {
        if (res.code == 200) {
          this.treeData = res.data.list
          this.checkedData = this.treeData[0]
          this.getDataList()
          this.$nextTick(() => {
            // selectId：绑定的 node-key
            this.$refs.tree.setCurrentKey(this.checkedData.dictType)
          })
        }
      })
    },
    getDataList() {
      let data = {
        pageSize: this.pagination.size,
        pageNo: this.pagination.current,
        dictType: this.checkedData.dictType,
        dictName: this.filters.dictName
      }
      this.tableLoading = true
      this.$api.sysDictData(data).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.list
          this.pagination.total = parseInt(res.data.count)
        } else {
          this.$message.error(res.message)
        }
      })
      this.tableLoading = false
    },
    // 树状图点击
    handleNodeClick(data) {
      this.pagination.current = 1
      this.checkedData = data
      this.$refs.tree.setCurrentKey(this.checkedData.dictType)
      if (this.checkedData.dictType != 'equipment_category_type') {
        this.getDataList()
      }
    },
    // 重置
    resetForm() {
      this.filters.dictName = ''
      this.pagination.size = 15
      this.pagination.current = 1
      this.getDataList()
    },
    // 查询
    searchForm() {
      this.getDataList()
    },
    // 新增、查看、编辑
    openDialog(type, val) {
      this.dialogTitle = type == 'add' ? '新增字典' : type == 'edit' ? '编辑字典' : '查看详情'
      this.disabled = type == 'view'
      if (type != 'add') {
        this.dictData = val
        let data = {
          id: this.dictData.id,
          dictType: this.checkedData.dictType
        }
        this.$api.dictionaryDetails(data).then((res) => {
          const { code, data, message } = res
          if (code == 200) {
            this.formInline = data
          }
        })
      }
      this.dialogVisible = true
    },
    // 双击查看详情
    dblclick(val) {
      this.openDialog('view', val)
    },
    // 弹窗取消
    dialogClosed() {
      this.$refs.formInline.resetFields()
      // this.formInline.dictName = ''
      // this.formInline.dictCode = ''
      // this.formInline.sort = ''
      // this.formInline.description = ''
      this.dialogVisible = false
    },
    // 新增/编辑字典
    submit() {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          if (this.dialogTitle == '新增字典') {
            let { dictName, dictCode, sort, description } = this.formInline
            let params = {
              dictName,
              dictCode,
              sort,
              description,
              dictType: this.checkedData.dictType,
              parentId: this.checkedData.id
            }
            this.$api.saveDictionary(params, { 'operation-type': 1 }).then((res) => {
              if (res.code == 200) {
                this.$message.success('新增成功')
                this.getDataList()
              } else {
                this.$message.error(res.message || '新增失败!')
              }
              this.dialogClosed()
            })
          } else {
            let { dictName, dictCode, sort, description } = this.formInline
            let params = {
              dictName,
              dictCode,
              sort,
              description,
              dictType: this.checkedData.dictType,
              id: this.dictData.id
            }
            this.$api.updateDictionary(params, { 'operation-type': 2, 'operation-id': params.id, 'operation-name': params.dictName }).then((res) => {
              if (res.code == 200) {
                this.$message.success(res.message)
                this.getDataList()
              } else {
                this.$message.error(res.message)
              }
              this.dialogClosed()
            })
          }
        }
      })
    },
    deleteFn(row) {
      this.$api.deleteDictData({
        dictCode: row.id,
        type: '1'
      }).then((res) => {
        if (res.code == 200) {
          if (res.data) {
            this.$confirm('此操作将永久删除该字典项, 是否继续?', '提示', {
              cancelButtonClass: 'el-button--primary is-plain',
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              let data = {
                id: row.id,
                dictType: this.checkedData.dictType
              }
              this.$api.deleteDictionary(data, { 'operation-type': 3, 'operation-name': row.dictName, 'operation-id': row.id }).then((res) => {
                const { code, data, message } = res
                if (code == 200) {
                  this.$message.success(message)
                  this.getDataList()
                } else {
                  this.$message.error(res.message)
                }
              })
            })
          } else {
            this.$alert('无法删除正在使用的字典值', '提示', {
              confirmButtonText: '确定'
            })
          }
        } else {
          this.$message.error(res.message)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-textarea {
  textarea {
    height: 100px;
    resize: none;
    // overflow: hidden;
    padding-bottom: 13px;
  }

  .el-input__count {
    height: 12px;
    line-height: 12px;
    background: rgb(255 255 255 / 50%);
  }
}

::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: linear-gradient(to right, #d9e1f8, #fff);
}

::v-deep .el-tree-node__content {
  height: 32px;
}

.role-content {
  height: 100%;
  display: flex;

  .role-content-left {
    width: 246px;
    height: 100%;
    padding: 10px;
    background: #fff;
    border-radius: 4px;
    margin-right: 12px;

    .left_content {
      height: calc(100% - 60px);
      overflow: auto;
    }
  }

  .role-content-right {
    height: 100%;
    min-width: 0;
    padding: 10px;
    background: #fff;
    border-radius: 4px;
    flex: 1;

    .search-from {
      padding-bottom: 12px;

      & > div {
        margin-right: 10px;
      }

      & > button {
        margin-top: 12px;
      }
    }

    .contentTable {
      height: calc(100% - 50px);
      display: flex;
      flex-direction: column;

      .contentTable-main {
        flex: 1;
        overflow: auto;
      }

      .contentTable-footer {
        padding: 10px 0 0;
      }
    }
  }

  .content {
    width: 100%;
    max-height: 500px !important;
    overflow: auto;
    background-color: #fff !important;
  }
}

.delete {
  color: red !important;
}
.noDelete {
  color: #bbb !important;
}
</style>
