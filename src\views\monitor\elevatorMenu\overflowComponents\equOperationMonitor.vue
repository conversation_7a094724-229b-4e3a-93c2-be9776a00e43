<template>
  <div class="content_left drag_class">
    <div class="left_title" @click="getElevatorList">电梯运行监测</div>
    <div class="left_content">
      <div class="select_elevator">
        <div class="select_icon"></div>
        <div class="select_name">{{ elevatorSelectName }}</div>
        <div class="select_arrow"></div>
        <el-select v-model="elevatorSelectId" class="custom_select" :popper-append-to-body="false" placeholder="请选择"
          @change="changeSelectElevator">
          <el-option v-for="item in elevatorOptions" :key="item.id" :label="item.name" :value="item.id"> </el-option>
        </el-select>
      </div>
      <el-carousel ref="elevatorCarousel" class="elevator_carousel" :interval="180000" arrow="always"
        @change="changeCarousel">
        <el-carousel-item v-for="item in elevatorOptions" :key="item.id" :name="item.id">
          <rtspCavas v-if="elevatorSelectId == item.id" ref="rtspCavas" :rtspUrl="videoUrl" :videoName="videoName"
            :hasCavas="Boolean(videoUrl)" class="video_preview"></rtspCavas>
        </el-carousel-item>
      </el-carousel>
      <div class="operating_data operating_common">
        <div v-for="item in elevatorServerData" :key="item.field" class="operating_box">
          <div class="box_icon duration-icon"></div>
          <div class="box_content">
            <span>{{ item.name }}</span>
            <span><span class="big_num">{{ item.value }}</span> {{ item.unit || '' }}</span>
          </div>
        </div>
      </div>
      <div class="actual_time_box">
        <div class="card_box_title card_box_bg">电梯运行实时参数</div>
        <div class="card_box_body">
          <div v-for="item in serverData" :key="item.field" class="body_li">
            <span class="li_label">{{ item.name }}</span>
            <span class="li_value">{{ item.value + (item.unit || '') }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { monitorTypeList } from '@/util/dict.js'
import moment from 'moment'
export default {
  props: {
    item: {
      type: Object,
      default: () => { }
    },
    socketData: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      requestHttp: __PATH.VUE_IEMC_ELEVATOR_API,
      projectCode: monitorTypeList.find((item) => item.projectName == '电梯监测').projectCode, // 电梯code
      elevatorSelectId: '',
      elevatorSelectName: '',
      elevatorOptions: [],
      elevatorServerData: [
        {
          name: '今日运行时长',
          value: '',
          field: '2641',
          unit: ''
        },
        {
          name: '今日运行距离',
          value: '',
          field: '2639',
          unit: ''
        },
        {
          name: '今日运行次数',
          value: '',
          field: '2637',
          unit: ''
        },
        {
          name: '今日开门次数',
          value: '',
          field: '2634',
          unit: ''
        }
      ],
      serverData: [
        {
          name: '运行状态',
          value: null,
          field: '2741',
          unit: ''
        },
        {
          name: '当前楼层',
          value: null,
          field: '2700',
          unit: ''
        },
        {
          name: '当前速度',
          value: '',
          field: '2711',
          unit: ''
        },
        {
          name: '是否载人',
          value: null,
          field: '2745',
          unit: ''
        },
        {
          name: '梯门状态',
          value: null,
          field: '2713',
          unit: ''
        },
        {
          name: '温度',
          value: '',
          field: '2748',
          unit: ''
        },
        {
          name: '湿度',
          value: '',
          field: '2750',
          unit: ''
        },
        {
          name: '运行振幅',
          value: '',
          field: '2694',
          unit: ''
        },
        {
          name: '左右偏移',
          value: '',
          field: '2698'
        },
        {
          name: '前后偏移',
          value: '',
          field: '2696',
          unit: ''
        }
      ], // 电梯运行数据
      videoUrl: '',
      videoName: ''
    }
  },
  watch: {
    socketData(data) {
      let itemData = JSON.parse(data)
      // console.log(itemData, this.serverData, this.elevatorSelectId)
      if (itemData.surveyEntityCode === this.elevatorSelectId) {
        this.serverData.map(e => {
          itemData.parameterList.forEach(item => {
            if (e.field === item.parameterId) {
              e.value = !this.isNumber(item.parameterValue) ? Number(item.parameterValue) : item.parameterValue
              e.unit = item.parameterUnit
            }
          })
        })
      }
      // let newList = JSON.parse(JSON.stringify(this.listData))
      // newList.forEach((item) => {
      //   if (item.surveyEntityCode == itemData.surveyEntityCode) {
      //     item.leftPaging = { page: 1, size: 6 }
      //     item.rightPaging = { page: 1, size: 3 }
      //     Object.assign(item, itemData)
      //   }
      // })
      // this.listData = this.setData(newList)
    }
  },
  mounted() {
    this.getElevatorList()
  },
  methods: {
    // 初始化获取所有电梯状态数据
    getElevatorList() {
      this.$api
        .getElevatorPullDownList(
          {
            projectCode: this.projectCode
          },
          this.requestHttp
        )
        .then((res) => {
          if (res.code == 200) {
            this.elevatorOptions = res.data
            if (this.elevatorOptions.length) {
              this.elevatorSelectId = this.elevatorOptions[0].id
              this.elevatorSelectName = this.elevatorOptions[0].name
              this.getSelectElevatorDetail()
              // this.getVideoList()
            }
          }
        })
    },
    // 通过下拉改变选中电梯
    changeSelectElevator(val) {
      this.elevatorSelectName = this.elevatorOptions.find((e) => e.id == val).name
      this.$refs.elevatorCarousel.setActiveItem(val)
    },
    // 获取选中电梯的实时数据
    getSelectElevatorDetail() {
      // 初始化查询id对应的视频
      const selectData = this.elevatorOptions.find((e) => e.id == this.elevatorSelectId)
      this.playVideoByElevatorId(selectData)
      this.serverData.map((e) => {
        e.value = ''
        e.unit ? (e.unit = '') : ''
      })
      this.elevatorServerData.map((e) => {
        e.value = null
        e.unit = ''
      })
      const params = {
        projectCode: this.projectCode,
        surveyCode: this.elevatorSelectId,
        startTime: moment().format('YYYY-MM-DD'),
        endTime: moment().format('YYYY-MM-DD')
      }
      this.$api.getElevatorParticulars(params, this.requestHttp).then((res) => {
        if (res.code === '200') {
          const data = res.data?.resRealTable ?? false
          // 无值清空当前数据
          if (!data) return
          data.parameterList?.forEach((item) => {
            this.serverData.forEach((e) => {
              if (e.field === item.parameterId) {
                e.value = !this.isNumber(item.parameterValue) ? Number(item.parameterValue) : item.parameterValue
                e.unit = item.parameterUnit
              }
            })
            this.elevatorServerData.forEach((e) => {
              if (e.field === item.parameterId) {
                e.value = !this.isNumber(item.parameterValue) ? Number(item.parameterValue) : item.parameterValue
                e.unit = item.parameterUnit
              }
            })
          })
        }
      })
    },
    isNumber(str) {
      return parseFloat(str).toString() === 'NaN'
    },
    // 通过轮播图改变选中电梯
    changeCarousel(val) {
      const selectData = this.elevatorOptions[val]
      this.elevatorSelectId = selectData.id
      this.elevatorSelectName = selectData.name
      this.getSelectElevatorDetail()
    },
    // 播放电梯监控
    playVideoByElevatorId(selectData) {
      this.videoUrl = selectData.url
      this.videoName = selectData.name
    }
  }
}
</script>
<style lang="scss" scoped>
.content_left {
  width: 100%;
  height: 100%;

  .left_title {
    height: 40px;
    line-height: 40px;
    background: url('~@/assets/images/elevator/left-title-bg.png') no-repeat;
    background-size: 100% 100%;
    font-size: 22px;
    font-family: YouSheBiaoTiHei-Regular, YouSheBiaoTiHei;
    font-weight: 400;
    color: #c7e0ff;
    // text-align: center;
    padding-left: 25%;
  }

  .left_content {
    width: 100%;
    margin-top: 16px;
    height: calc(100% - 56px);
    padding: 16px;
    background: linear-gradient(180deg, #0a1e31 0%, #09141e 100%);
    border: 1px solid #3b4c5b;

    .select_elevator {
      width: 80%;
      margin: 5px 10% 16px;
      padding: 0 24px;
      height: 38px;
      background: #122c41;
      border-radius: 8px;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      position: relative;

      .select_icon {
        width: 25px;
        height: 25px;
        margin: auto 0;
        background: url('~@/assets/images/elevator/select-icon.png') no-repeat;
        background-size: 100% 100%;
      }

      .select_name {
        flex: 1;
        font-size: 16px;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        color: #fff;
        padding: 0 10px;
        margin: auto 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .select_arrow {
        width: 32px;
        height: 32px;
        margin: auto 0;
        background: url('~@/assets/images/elevator/chevron-down.png') no-repeat;
        background-size: 100% 100%;
      }

      ::v-deep .custom_select {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;

        .el-input {
          visibility: hidden;
        }

        .el-select-dropdown {
          position: absolute !important;
          width: 100% !important;
          left: 0 !important;
          // background-color: #122c41;
          background: url('~@/assets/images/elevator/select-bg.png') no-repeat;
          background-size: 100% 100%;
          border: none;
          box-shadow: none;

          .el-select-dropdown__list {
            padding: 6px 16px !important;
          }

          .el-select-dropdown__item {
            font-size: 14px;
            font-family: Alibaba PuHuiTi-Regular, Alibaba PuHuiTi;
            font-weight: 400;
            color: #a2b7d9;
          }

          .el-select-dropdown__item.hover,
          .el-select-dropdown__item:hover {
            background: url('~@/assets/images/elevator/select-li-bg.png') no-repeat;
            background-size: 100% 100%;
            font-size: 14px;
            font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
            font-weight: 400;
            color: #fff;
          }

          .popper__arrow {
            display: none;
          }
        }
      }
    }

    .elevator_carousel {
      height: calc(25% - 30px);
      margin-bottom: 10px;

      ::v-deep .el-carousel__container {
        height: 100%;

        .el-carousel__item {
          display: flex;

          .video_preview {
            width: 100%;
            height: 100%;
            margin-bottom: 10px;
          }
        }

        .el-carousel__arrow {
          width: 32px;
          height: 32px;
          background-color: transparent;
        }

        .el-carousel__arrow:hover {
          background-color: rgba(255, 255, 255, 0.1);
        }
      }

      ::v-deep .el-carousel__indicators {
        display: none;
      }
    }

    .operating_data {
      height: calc(25% - 30px);
    }

    .operating_common {
      // height: calc(25% - 20px);
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;

      // align-items: ;
      .operating_box {
        width: calc(50% - 8px);
        height: calc(50% - 16px);
        display: flex;

        .box_icon {
          margin: auto 0;
          height: 80%;
          aspect-ratio: 1;
        }

        .duration-icon {
          background: url('~@/assets/images/elevator/duration-icon.png') no-repeat;
          background-size: 100% 100%;
        }

        .distance-icon {
          background: url('~@/assets/images/elevator/distance-icon.png') no-repeat;
          background-size: 100% 100%;
        }

        .numbers-icon {
          background: url('~@/assets/images/elevator/numbers-icon.png') no-repeat;
          background-size: 100% 100%;
        }

        .box_content {
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          padding-left: 10px;

          >span {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          >span:first-child {
            font-size: 15px;
            font-family: Alibaba PuHuiTi-Regular, Alibaba PuHuiTi;
            color: #a2b7d9;
          }

          >span:last-child {
            font-size: 12px;
            font-family: PingFang SC-Medium, PingFang SC;
            font-weight: 500;
            color: #ccced3;
          }

          .big_num {
            font-size: 20px;
            font-family: Arial-Bold, Arial;
            font-weight: bold;
            color: #c9dfff;
          }
        }
      }
    }

    .actual_time_box {
      width: 100%;
      height: calc(50% - 20px);

      // background: #fff;
      .card_box_title {
        height: 31px;
        width: 100%;
        line-height: 31px;
        padding-left: 10px;
        font-size: 15px;
        font-family: Alibaba PuHuiTi-Medium, Alibaba PuHuiTi;
        font-weight: 500;
        color: #b7cfff;
      }

      .card_box_bg {
        background: url('~@/assets/images/elevator/module-bg.png') no-repeat;
        background-size: 100% 100%;
      }

      .card_box_body {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        height: calc(100% - 31px);

        .body_li {
          width: calc(50% - 8px);
          height: calc(20% - 12px);
          margin-top: 12px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0 10px 0 40px;
          background: url('~@/assets/images/elevator/real-time-bg.png') no-repeat;
          background-size: 100% 100%;

          .li_label {
            font-size: 14px;
            font-family: OPPOSans-Regular, OPPOSans;
            font-weight: 400;
            color: #dfeefe;
          }

          .li_value {
            font-size: 18px;
            font-family: Arial-Bold, Arial;
            font-weight: bold;
            color: #f4db67;
          }
        }
      }
    }
  }
}
</style>
