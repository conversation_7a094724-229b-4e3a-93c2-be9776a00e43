<template>
  <!-- 时间紧张来不及维护组件了，后面可与控制策略管理页面 封装公共使用 -->
  <div class="updateOperationMode">
    <div class="content-left">
      <div class="toptip">
        <span class="green_line"></span>
        场景配置
        <div class="title_btn_icon">
          <i title="新增" class="el-icon-plus" @click="sceneEvent('add', null)"></i>
        </div>
      </div>
      <div class="left_content">
        <div v-for="(item, index) in sceneList" :key="index" class="scene_list">
          <div class="scene_list_name">
            <span>{{ item.timeTableName }}</span>
            <span>
              <i class="el-icon-edit" @click="sceneEvent('edit', item, index)"></i>
              <i class="el-icon-delete" @click="sceneEvent('del', item, index)"></i>
            </span>
          </div>
          <div class="scene_list_time">
            <div v-for="(table, idx) in item.timeTableDataList" :key="idx">{{ table.timePoint }}（{{ table.switchControl == 1 ? '开启' : '关闭' }}）</div>
          </div>
        </div>
      </div>
    </div>
    <div class="content-right">
      <div class="content-top" style="margin: 12px;">
        <el-select v-model="searchForm.timeTableId" class="sino_sdcp_input mr15" style="width: 15%;" clearable placeholder="场景选择">
          <el-option v-for="item in sceneList" :key="item.id" :label="item.timeTableName" :value="item.id"></el-option>
        </el-select>
        <el-input v-model="searchForm.dictName" placeholder="分组名称" style="width: 15%; margin-right: 15px;"></el-input>
        <el-button plain type="primary" @click="reset()">重置</el-button>
        <el-button type="primary" @click="search()">查询</el-button>
      </div>
      <div class="table tableDataConfig">
        <el-table v-loading="tableLoading" :data="tableData" :border="true" stripe max-height="calc(100%)" :cell-style="{ padding: '0px' }">
          <el-table-column v-for="(item, index) in tableTitle" :key="index" :prop="item.value" :label="item.label" show-overflow-tooltip :width="item.width">
            <template slot-scope="scope">
              <el-select
                v-if="scope.column.property == 'timeTableId'"
                class="no-border"
                :value="scope.row.timeTableId"
                placeholder="请选择"
                @change="changeSceneTable($event, scope.row, scope.$index)"
              >
                <el-option v-for="item in sceneList" :key="item.id" :label="item.timeTableName" :value="item.id"></el-option>
              </el-select>
              <span v-else>{{ scope.row[scope.column.property] }}</span>
            </template>
          </el-table-column>
        </el-table>
        <!-- <div class="table-page">
          <el-pagination
            class="pagination"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="paginationData.currentPage"
            :page-sizes="[15, 30, 50, 100]"
            :page-size="paginationData.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="paginationData.total"
          ></el-pagination>
        </div> -->
      </div>
    </div>
    <template v-if="sceneDialogShow">
      <sceneDialog
        :page="2"
        :sceneDialogShow="sceneDialogShow"
        :sceneDialogType="sceneDialogType"
        :dialogData="sceneDialogData"
        :patternId="selectId"
        @closeSceneDialog="closeSceneDialog"
        @sceneSubmit="sceneSubmit"
      ></sceneDialog>
    </template>
  </div>
</template>
<script>
import sceneDialog from './sceneDialog.vue'
export default {
  name: 'updateOperationMode',
  components: {
    sceneDialog
  },
  data() {
    return {
      sceneDialogType: 'add', // 场景弹窗类型
      sceneDialogShow: false, // 场景弹窗显示
      sceneDialogData: {}, // 场景弹窗数据
      selectId: '', // 选中 模式的type
      sceneList: [],
      areaOption: [], // 场景选择
      searchForm: {
        timeTableId: '',
        dictName: ''
      },
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      tableTitle: [
        { label: '分组名称', value: 'dictName' },
        { label: '回路数量', value: 'count' },
        { label: '回路名称', value: 'circuitName' },
        { label: '场景', value: 'timeTableId' },
        { label: '时刻表预览', value: 'timeTable' }
      ],
      tableData: [],
      tableLoading: false,
      editIndex: null
    }
  },
  created() {},
  mounted() {
    this.setToDayDataByRedis()
  },
  methods: {
    // 获取场景列表
    setToDayDataByRedis() {
      this.tableLoading = true
      // let newArr = []
      this.$api.setToDayDataByRedis(this.searchForm).then((res) => {
        this.tableLoading = false
        if (res.code == 200) {
          // if(this.sceneList.length) newArr = this.sceneList.filter(item => !item.id)
          this.selectId = res.data.timeTable[0]?.patternId
          this.sceneList = res.data.timeTable
          // this.sceneList = res.data.timeTable.concat(newArr)
          this.tableData = res.data.toDayLightingGroup
        }
      })
    },
    // 获取列表
    // getTableData() {
    //   let data = {
    //     ...this.searchForm,
    //     patternId: this.selectId,
    //     pageSize: this.paginationData.pageSize,
    //     page: this.paginationData.currentPage,
    //   };
    //   this.tableLoading = true;
    //   this.$api.getLightingGroupByPattern(data).then((res) => {
    //     this.tableLoading = false;
    //     if (res.code == 200) {
    //       this.tableData = res.data.list;
    //       this.paginationData.total = parseInt(res.data.count);
    //     } else {
    //       this.$message.error(res.message);
    //     }
    //   });
    // },
    // handleSizeChange(val) {
    //   this.paginationData.currentPage = 1;
    //   this.paginationData.pageSize = val;
    //   this.search();
    // },
    // handleCurrentChange(val) {
    //   this.paginationData.currentPage = val;
    //   this.search();
    // },
    changeSceneTable(val, row, index) {
      const params = {
        lightingDictId: row.id,
        id: row.controlStrategyId,
        patternId: this.selectId,
        lightingDictId: row.id
      }
      if (row.timeTableId) {
        Object.assign(params, {
          newTimeTableId: val,
          timeTableId: row.timeTableId
        })
      } else {
        Object.assign(params, {
          timeTableId: val
        })
      }
      this.$api.updateConnectionRelation({ relation: JSON.stringify(params) }).then((res) => {
        if (res.code == 200) {
          this.tableData[index].timeTableId = val
          this.tableData[index].timeTable = this.sceneList.find((item) => item.id == val).timeTable
          this.$message.success('关联成功')
        }
      })
      // this.search()
    },
    reset() {
      // this.paginationData.currentPage = 1;
      Object.assign(this.searchForm, {
        timeTableId: '',
        dictName: ''
      })
      this.search()
    },
    search() {
      this.setToDayDataByRedis()
    },
    sceneEvent(type, selectRow, index) {
      if (type !== 'del') {
        this.editIndex = index
        this.sceneDialogType = type
        this.sceneDialogData = selectRow
        this.sceneDialogShow = true
      } else {
        this.$confirm('删除后，相关数据将自动解绑，确认删除？', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          if (selectRow.id) {
            this.$api.delTimetableToRedis({ id: selectRow.id }).then((res) => {
              if (res.code == 200) {
                this.sceneList.splice(index, 1)
                this.$message({
                  message: '删除成功',
                  type: 'success'
                })
              }
            })
          } else {
            this.sceneList.splice(index, 1)
            this.$message({
              message: '删除成功',
              type: 'success'
            })
          }
        })
      }
    },
    closeSceneDialog() {
      this.sceneDialogShow = false
    },
    sceneSubmit(data) {
      if (data.id && this.sceneDialogType == 'edit') {
        this.sceneDialogShow = false
        this.sceneList.splice(this.editIndex, 1, {
          ...data,
          timeTable: data.timeTableDataList
            .map((item) => {
              return item.timePoint + `（${item.switchControl == 1 ? '开启' : '关闭'}）`
            })
            .join()
        // timeTableDataList: JSON.parse(data.timeTable)
        })
      } else {
        this.sceneDialogShow = false
        this.sceneList.push({
          ...data,
          timeTable: data.timeTableDataList
            .map((item) => {
              return item.timePoint + `（${item.switchControl == 1 ? '开启' : '关闭'}）`
            })
            .join()
        // timeTableDataList: JSON.parse(data.timeTable)
        })
      }
      console.log(this.sceneList)
    }
  }
}
</script>
<style lang="scss" type="text/css">
.updateOperationMode {
  width: 100%;
  height: 100%;
  height: calc(100% - 66px);
  margin-top: 10px;
  display: flex;

  .content-left {
    width: calc(18% - 10px);
    height: 100%;
    background-color: #fff;
    border-radius: 10px;
    display: block;

    .title_btn_icon {
      float: right;
      cursor: pointer;
      margin-right: 20px;
      color: #5188fc;

      i {
        font-size: 18px;
      }
    }

    .left_content {
      height: calc(100% - 50px) !important;
      padding: 0;

      .scene_list {
        .scene_list_name {
          width: 100%;
          height: 38px;
          line-height: 38px;
          background: #f5f6fb;
          display: flex;
          justify-content: space-between;
          padding: 0 20px;

          span {
            font-size: 15px;
            color: #5188fc;
          }

          i {
            line-height: 38px;
            font-size: 22px;
            margin-left: 20px;
            color: #848da1;
            cursor: pointer;
          }
        }

        .scene_list_time {
          width: 100%;
          padding: 6px 20px;

          div {
            height: 25px;
            line-height: 25px;
            padding-left: 10px;
            font-size: 14px;
            color: #606266;
          }
        }
      }
    }
  }

  .content-right {
    width: 82%;
    margin-left: 10px;
    background-color: #fff;
    border-radius: 10px;
  }
}

.pattern_popper {
  width: 99px;
  height: 30px;
  background: #7b7b7d !important;
  border-radius: 15px !important;
  padding: 0 6px !important;

  .popper__arrow {
    display: none !important;
  }

  .popper_icon_box {
    display: flex;
    justify-content: space-evenly;

    i {
      width: 28px;
      height: 28px;
      line-height: 28px !important;
      border-radius: 50%;
      font-size: 18px;
      text-align: center;
      cursor: pointer;

      &:hover {
        background: #979799;
      }
    }
  }
}
</style>
