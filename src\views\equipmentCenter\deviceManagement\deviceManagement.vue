<template>
  <PageContainer>
    <div slot="content" class="role-content" style="height: 100%">
      <div class="role-content-left">
        <div class="toptip">
          <span class="green_line"></span>
          资产管理
        </div>
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="专业类别" :name="tabType.ZY" />
          <el-tab-pane label="资产分类" :name="tabType.ZC" />
        </el-tabs>
        <div v-loading class="left_content">
          <ZkRenderTree
            ref="tree"
            v-loading="treeLoading"
            :check-strictly="true"
            :data="treeData"
            :props="defaultProps"
            style="margin-top: 10px"
            node-key="id"
            :highlight-current="true"
            :default-expanded-keys="expanded"
            @node-click="handleNodeClick"
          >
          </ZkRenderTree>
        </div>
      </div>
      <div class="role-content-right">
        <div style="height: 100%">
          <div class="search-from">
            <div>
              <el-input
                v-model.trim="taskBookName"
                placeholder="请输入资产名称"
                style="width: 200px"
                clearable
                maxlength="25"
                onkeyup="if(value.length>25)value=value.slice(0,25)"
              ></el-input>
              <el-input
                v-model.trim="assetCode"
                placeholder="请输入资产编码"
                style="width: 200px; margin: 0 10px"
                clearable
                maxlength="25"
                onkeyup="if(value.length>25)value=value.slice(0,25)"
              ></el-input>
              <el-cascader
                ref="regionCode"
                v-model="regionCode"
                :props="riskPropsType"
                :options="regionCodeList"
                placeholder="请选择所在区域"
                class="cascaderWid"
                :show-all-levels="false"
                style="margin-right: 10px"
              ></el-cascader>
              <el-input
                v-model.trim="assetModel"
                placeholder="请输入型号"
                style="width: 200px; margin: 0 10px"
                clearable
                maxlength="25"
                onkeyup="if(value.length>25)value=value.slice(0,25)"
              ></el-input>
              <el-cascader
                v-if="activeName === 'zhuanye'"
                ref="childrenDeviceRef"
                v-model="childrenDevice"
                :props="childrenDevicPropsType"
                :options="childrenDeviceOptions"
                placeholder="请选择系统类别"
                class="cascaderWid"
                filterable
                clearable
                :show-all-levels="false"
                style="margin-right: 10px"
              ></el-cascader>
              <el-input v-model.trim="useDuration" style="width: 200px; margin: 0 10px" placeholder="使用时长" @keyup.native="proving">
                <template slot="append">月</template>
              </el-input>
              <el-button type="primary" plain style="margin-right: 10px" @click="resetForm">重置</el-button>
              <el-button type="primary" style="margin-right: 20px" @click="searchForm">查询</el-button>
            </div>
            <div style="display: flex; margin-top: 5px">
              <el-button type="primary" class="rightBtn" icon="el-icon-upload2" :loading="downLoading" @click="handleOperation('export')"> 导出资产 </el-button>
              <el-button type="primary" class="rightBtn" icon="el-icon-download" :loading="downLoading" @click="handleOperation('Import')"> 导入资产 </el-button>
              <el-button type="primary" class="rightBtn" icon="el-icon-plus" @click="onOpen('add')">新增</el-button>
              <el-button type="primary" class="rightBtn" icon="el-icon-download" @click="onSynchronization()">同步 </el-button>
              <el-button type="primary" class="rightBtn" icon="el-icon-download" :disabled="tableClickArry.length == 0" :loading="isdownload" @click="downLoadQrCode">
                设备码下载
              </el-button>
              <el-button type="primary" :disabled="tableClickArry.length == 0" style="margin: 0" @click="batchDelete"> 批量删除 </el-button>
            </div>
          </div>
          <div class="contentTable">
            <div class="contentTable-main table-content">
              <el-table
                v-loading="tableLoading"
                title="双击查看详情"
                border
                style="width: 100%"
                :data="tableData"
                height="100%"
                stripe
                @row-dblclick="dblclick"
                @selection-change="handleSelectionChange"
              >
                <el-table-column type="selection" width="60" align="center"></el-table-column>
                <el-table-column type="index" label="序号" width="70">
                  <template slot-scope="scope">
                    <span>{{ (pagination.current - 1) * pagination.size + scope.$index + 1 }}</span>
                  </template>
                </el-table-column>
                <el-table-column v-for="col in tableList" :key="isUpdate" :prop="col.column" :label="col.fieldName" :width="col.width" show-overflow-tooltip> </el-table-column>
                <!-- <el-table-column prop="assetName" label="资产名称" width="150" show-overflow-tooltip></el-table-column>
                <el-table-column prop="assetCode" label="资产编码" width="140" show-overflow-tooltip></el-table-column>
                <el-table-column prop="assetBrand" label="品牌" width="140" show-overflow-tooltip></el-table-column>
                <el-table-column prop="assetModel" show-overflow-tooltip label="型号"></el-table-column>
                <el-table-column prop="regionName" width="240" show-overflow-tooltip label="所在区域">
                  <template slot-scope="scope">
                    <span v-html="scope.row.regionReverseName"></span>
                  </template>
                </el-table-column>
                <el-table-column prop="professionalCategoryName" show-overflow-tooltip label="专业类别"></el-table-column>
                <el-table-column prop="updateTime" label="更新时间" width="180"></el-table-column> -->
                <el-table-column label="操作" width="180" fixed="right" :render-header="renderHeader">
                  <template slot-scope="scope">
                    <el-button type="text" class="record" @click="onRecord(scope.row)">操作记录</el-button>
                    <el-button type="text" @click="onOpen('edit', scope.row)">编辑</el-button>
                    <el-button type="text" class="delete" @click="deleteFn(scope.row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="contentTable-footer">
              <el-pagination
                :current-page="pagination.current"
                :page-sizes="pagination.pageSizeOptions"
                :page-size="pagination.size"
                :layout="pagination.layoutOptions"
                :total="pagination.total"
                @size-change="paginationSizeChange"
                @current-change="paginationCurrentChange"
              >
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
      <!-- 导入资产信息 -->
      <el-dialog
        v-dialogDrag
        title="资产导入"
        :visible.sync="assetsImportdialogVisible"
        width="40%"
        :before-close="handleAssetsImportClose"
        class="classify-dialog"
        custom-class="model-dialog"
        :close-on-click-modal="false"
      >
        <div class="content" style="padding: 10px; display: flex">
          <div>上传附件：</div>
          <div class="upload-file">
            <el-upload
              ref="uploadFile"
              action="string"
              :limit="1"
              :http-request="httpRequest"
              :beforeUpload="beforeAvatarUpload"
              :file-list="assetsFileList"
              :on-exceed="handleExceed"
              :on-remove="handleRemove"
              accept=".xlsx"
            >
              <div class="leadFile">
                <el-button size="small" type="primary">点击上传</el-button>
              </div>
              <div slot="tip" class="el-upload__tip">只能上传 .xlsx格式</div>
            </el-upload>
          </div>
          <div class="leadFile_item" @click="getTemplateExport">导入模板下载</div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="handleAssetsImportClose">取 消</el-button>
          <el-button type="primary" :loading="subLoading" @click="submitAssetsImport">确 定</el-button>
        </span>
      </el-dialog>
      <!-- 同步弹窗 -->
      <synDialog v-if="synDialog" ref="synDialog" :visible.sync="synDialog" @synOK="synOK"></synDialog>
      <!-- 操作记录 -->
      <!-- 自定义表头弹窗 -->
      <tablecolumsdialog v-if="dialogVisible" :dialogVisible="dialogVisible" @handleCloseChange="handleCloseChange" @handleSubmitChange="handleSubmitChange"></tablecolumsdialog>
      <el-drawer title="操作记录" :visible.sync="drawer" size="35%">
        <div
          v-infinite-scroll="
            () => {
              pagingLoad()
            }
          "
          class="timeContent"
        >
          <el-timeline>
            <el-timeline-item
              v-for="(activity, index) in activities"
              :key="index"
              :icon="index == 0 ? 'el-icon-more' : ''"
              :size="index == 0 ? 'large' : 'normal'"
              type="primary"
              :color="activity.color"
              :timestamp="activity.createTime.split(' ')[0]"
              :hide-timestamp="index > 0 && activity.createTime.split(' ')[0] == activities[index - 1].createTime.split(' ')[0]"
            >
              <p class="time">{{ activity.createTime.split(' ')[1] }}</p>
              <div class="continer">
                <span class="item">操作人 : {{ activity.createName }}</span>
                <span class="item">操作 : {{ activity.operation }}</span>
                <el-tooltip effect="dark" :content="activity.record" placement="bottom">
                  <span v-if="activity.record" class="itemContent">内容 : {{ activity.record }}</span>
                </el-tooltip>
              </div>
            </el-timeline-item>
          </el-timeline>
          <span v-if="activities.length >= drawerTotal && activities.length" class="noData">没有更多了</span>
        </div>
      </el-drawer>
    </div>
  </PageContainer>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin.js'
import store from '@/store/index'
import axios from 'axios'
import qs from 'qs'
import { transData } from '@/util'
export default {
  name: 'DeviceManagementIndex',
  components: {
    synDialog: () => import('./components/synDialog.vue'),
    tablecolumsdialog: () => import('./components/tableColumsDialog.vue')
  },
  mixins: [tableListMixin],
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.$store.commit('keepAlive/add', 'DeviceManagementIndex')
    })
  },
  async beforeRouteLeave(to, from, next) {
    // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
    if (!['addDevice'].includes(to.name)) {
      // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      activeName: 'zhuanye',
      subLoading: false,
      downLoading: false, // 下载权限
      taskBookName: '', // 模板名称,
      assetCode: '', // 资产编码
      assetModel: '', // 资产型号
      useDuration: '', // 使用时长
      regionCode: [], // 所在区域
      assetsImportdialogVisible: false, // 传资产弹窗
      assetsFileList: [],
      dialogVisible: false, // 自定义表头弹窗
      treeData: [],
      tableList: [
        {
          unChecked: 'false',
          fieldName: '资产名称',
          column: 'assetName',
          sort: '1',
          isChecked: '1',
          width: '150'
        },
        {
          unChecked: 'true',
          fieldName: '资产编码',
          column: 'assetCode',
          sort: '2',
          isChecked: '1',
          width: '150'
        },
        {
          unChecked: 'true',
          fieldName: '品牌',
          column: 'assetBrand',
          sort: '3',
          isChecked: '1',
          width: '150'
        },
        {
          unChecked: 'true',
          fieldName: '型号',
          column: 'assetModel',
          sort: '4',
          isChecked: '1',
          width: '150'
        },
        {
          unChecked: 'true',
          fieldName: '所在区域',
          column: 'regionReverseName',
          sort: '5',
          isChecked: '1',
          width: '150'
        },
        {
          unChecked: 'true',
          fieldName: '专业类别',
          column: 'professionalCategoryName',
          sort: '6',
          isChecked: '1',
          width: '150'
        },
        {
          unChecked: 'true',
          fieldName: '更新时间',
          column: 'updateTime',
          sort: '7',
          isChecked: '1',
          width: '150'
        }
      ],
      checkedData: [], // 选中tree数据
      defaultProps: {
        label(data, node) {
          return (data.baseName || data.dictName) + (data.dataSum ? `  (${data.dataSum})` : '')
        },
        value: 'dictCode'
      },
      expanded: [],
      filters: {
        dictName: ''
      },
      tableLoading: false,
      tableData: [],
      dictData: [],
      drawer: false, // 操作记录弹窗
      synDialog: false, // 同步弹窗显示
      activities: [],
      drawerPageNo: 1,
      drawerPageSize: 10,
      drawerTotal: 0,
      checkRecordData: '', // 操作记录数据
      treeLoading: false,
      tableClickArry: [],
      isdownload: false,
      riskPropsType: {
        children: 'children',
        label: 'ssmName',
        value: 'id',
        checkStrictly: true,
        multiple: false
      },
      newList: [],
      isUpdate: 1,
      loginTable: '',
      userInfos: '',
      regionCodeList: [], // 所在区域列表
      isShowTooltip: false,
      // 子设备列表
      childrenDeviceOptions: [],
      // 子设备级联菜单配置
      childrenDevicPropsType: {
        children: 'children',
        label: 'baseName',
        value: 'id',
        checkStrictly: true,
        multiple: false
      },
      // 选择的子设备
      childrenDevice: []
    }
  },
  computed: {
    tabType() {
      return {
        // 专业
        ZY: 'zhuanye',
        // 资产
        ZC: 'zichan'
      }
    }
  },
  created() {
    this.init()
  },
  mounted() {
    this.initEvent()
  },
  activated() {
    this.initEvent()
  },
  methods: {
    init() {
      this.userInfos = this.$store.state.user.userInfo.user
      if (localStorage.getItem('configParamsList')) {
        this.loginTable = JSON.parse(localStorage.getItem('configParamsList'))
        this.newList = this.loginTable.filter((item) => item.id == this.userInfos.staffId)
        if (this.newList && this.userInfos.staffId == this.newList[0].id) {
          let Arry = this.newList[0].list.filter((item) => item.isChecked == 1)
          this.tableList = Arry
          this.isUpdate = this.isUpdate++
        } else if (this.newList != []) {
          this.tableList = this.tableList
        }
      }
      this.$forceUpdate()
    },
    initEvent() {
      this.getRegionList()
      if (this.$route.query.activeName) {
        this.activeName = this.$route.query.activeName
        if (this.activeName === this.tabType.ZY) {
          this.getProfessionalCategory()
        } else {
          this.getAssetList()
        }
      } else {
        this.getProfessionalCategory()
      }
    },
    visibilityChange(e) {
      const item = e.target
      const itemWidth = item.offsetWidth
      const itemScrollWidth = item.scrollWidth
      if (itemWidth < itemScrollWidth) {
        this.isShowTooltip = true
      } else {
        this.isShowTooltip = false
      }
    },
    // 获取资产分类
    getAssetList() {
      this.treeLoading = true
      this.$api.selectByListAsset({ dictTypeId: '27' }).then((res) => {
        if (res.code == '200') {
          this.treeData = transData(res.data, 'id', 'pid', 'children')
          this.checkedData = this.$route.query.id ? { id: this.$route.query.id } : this.treeData[0]
          if (this.$route.query.pids) {
            this.checkedData.pids = this.$route.query.pids
          }
          this.getDataList()
          this.$nextTick(() => {
            // selectId：绑定的 node-key
            this.$refs.tree.getTreeRef().setCurrentKey(this.checkedData.id)
            this.expanded = [this.checkedData.id]
          })
        }
        this.treeLoading = false
      })
    },
    getProfessionalCategory() {
      this.treeLoading = true
      this.$api.getProfessionalCategory().then((res) => {
        if (res.code == '200') {
          this.treeData = res.data
          this.checkedData = this.$route.query.id ? { id: this.$route.query.id } : this.treeData[0]
          this.getDataList()
          this.$nextTick(() => {
            // selectId：绑定的 node-key
            this.$refs.tree.getTreeRef().setCurrentKey(this.checkedData.id)
            // 获取子设备
            this.loadSubDevice(this.checkedData.id)
          })
        }
        this.treeLoading = false
      })
    },
    handleClick(tab, event) {
      this.$router.push({
        query: {
          activeName: this.activeName,
          id: '',
          pids: ''
        }
      })
      this.treeData = []
      this.tableData = []
      if (tab.index == '0') {
        this.getProfessionalCategory()
      } else {
        this.getAssetList()
      }
    },
    getDataList() {
      var strBig = ''
      let strSmall = ''
      if (this.activeName === this.tabType.ZC) {
        if (this.checkedData.pids == '#') {
          strBig = this.checkedData.id
        } else {
          strSmall = this.checkedData.id
        }
      }
      let data = {
        currentPage: this.pagination.current,
        pageSize: this.pagination.size,
        assetName: this.taskBookName,
        assetCode: this.assetCode,
        assetModel: this.assetModel,
        useDuration: this.useDuration,
        regionCode: this.regionCode[this.regionCode.length - 1],
        professionalCategoryCode: this.activeName === this.tabType.ZY ? this.checkedData.id : '', // 专业类别
        assetCategoryCode: this.activeName === this.tabType.ZC ? strBig : '', // 资产大类
        assetSubcategoryCode: this.activeName === this.tabType.ZC ? strSmall : '' // 资产小类
      }
      // 如果是专业，并且有子分类查询数据，则插入查询条件
      if (this.activeName === this.tabType.ZY && this.childrenDevice.length > 0) {
        data.systemCategoryCode = this.childrenDevice.slice(-1)[0]
      }
      this.tableLoading = true
      this.$api.getAssetList(data).then((res) => {
        if (res.code == '200') {
          this.tableData = res.data.assetDetailsList
          this.pagination.total = parseInt(res.data.sum)
        }
      })
      this.tableLoading = false
    },
    // 树状图点击
    handleNodeClick(data) {
      this.$router.push({
        query: {
          activeName: this.activeName,
          id: data.id,
          pids: data.pids || ''
        }
      })
      this.$route.query
      this.pagination.current = 1
      this.checkedData = data
      this.$refs.tree.getTreeRef().setCurrentKey(this.checkedData.id)
      // 始终重置子设备选中值
      this.childrenDevice = []
      this.getDataList()
      // 专业设备获取获取子集数据
      if (this.activeName === this.tabType.ZY) {
        this.loadSubDevice(this.checkedData.id)
      }
    },
    /** 输入框输入限制 */
    proving(e) {
      if (e.target.value && e.target.value.length == 1) {
        e.target.value = e.target.value.toString().replace(/[^1-9]/g, '') // 只能输入正整数.replace(/[^1-9]/g, '')
      } else {
        e.target.value = e.target.value.toString().replace(/[^0-9]/g, '') // 只能输入正整数.replace(/[^1-9]/g, '')
      }
    },
    // 重置
    resetForm() {
      this.taskBookName = ''
      this.assetCode = ''
      this.useDuration = ''
      this.assetModel = ''
      this.regionCode = []
      this.childrenDevice = []
      this.pagination.size = 15
      this.pagination.current = 1
      this.getDataList()
    },
    // 查询
    searchForm() {
      this.pagination.current = 1
      this.getDataList()
    },
    // 双击查看详情
    dblclick(val) {
      this.$router.push({
        name: 'addDevice',
        query: {
          type: 'details',
          id: val.id,
          assetsId: val.assetsId
        }
      })
    },
    deleteFn(row) {
      this.$confirm('此操作将永久删除该资产, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api
          .getDelAsset(
            { assetsId: row.assetsId },
            {
              'operation-type': 3,
              'operation-name': row.assetName,
              'operation-id': row.id
            }
          )
          .then((res) => {
            if (res.code == '200') {
              this.searchForm()
              this.getProfessionalCategory()
            }
          })
      })
    },
    onOpen(type, val) {
      if (type == 'add') {
        this.$router.push({
          name: 'addDevice',
          query: {
            type
          }
        })
      } else {
        this.$router.push({
          name: 'addDevice',
          query: {
            type,
            id: val.id,
            assetsId: val.assetsId
          }
        })
      }
    },
    //
    renderHeader(h, { column }) {
      // h即为cerateElement的简写，具体可看vue官方文档
      return h('div', [
        h('span', column.label),
        h('i', {
          class: 'el-icon-setting',
          style: 'color:#000000;margin-left:50px;cursor: pointer;',
          on: {
            click: () => {
              this.dialogVisible = true
              // 在这里处理点击事件
            }
          }
        })
      ])
    },
    handleCloseChange() {
      this.$nextTick(() => {
        this.init()
      })
      this.getDataList()
      this.dialogVisible = false
    },
    handleSubmitChange() {
      this.dialogVisible = false
    },
    // 点击操作记录
    onRecord(data) {
      this.activities = []
      this.drawerTotal = 0
      this.drawerPageNo = 1
      this.checkRecordData = data
      this.drawer = true
      this.getRecordList()
    },
    // 操作记录数据
    getRecordList() {
      let data = {
        assetsIds: this.checkRecordData.assetsId,
        current: this.drawerPageNo,
        size: this.drawerPageSize
      }
      this.$api.recordList(data).then((res) => {
        if (res.code == '200') {
          this.activities = this.activities.concat(res.data.records)
          this.drawerTotal = res.data.total
        } else {
          this.activities = []
          this.drawerTotal = 0
        }
      })
    },
    // 操作记录分页
    pagingLoad() {
      if (this.activities.length < this.drawerTotal) {
        this.drawerPageNo += 1
        this.getRecordList()
      }
    },
    // 点击同步
    onSynchronization() {
      this.activities = []
      this.synDialog = true
    },
    // 导入导出操作
    handleOperation(type) {
      if (type == 'export') {
        this.getAssetsExport()
      } else {
        this.assetsFileList = []
        this.assetsImportdialogVisible = true
      }
    },
    /**
     * 文件
     */
    httpRequest(item) {
      this.assetsFileList.push(item.file)
    },
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
    },
    handleRemove(file, fileList) {
      this.assetsFileList = []
    },
    beforeAvatarUpload(file) {
      const isLt50M = file.size / 1024 / 1024 < 50
      if (!isLt50M) {
        this.$message.error('上传文件大小不能超过 50MB!')
        return false
      }
      if (file.name.indexOf(',') != -1) {
        this.$message.error('非法的文件名')
        return false
      }
    },
    // 资产导出
    getAssetsExport() {
      const userInfo = this.$store.state.user.userInfo.user
      const params = {
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ',
        assetName: this.taskBookName,
        assetCode: this.assetCode,
        assetModel: this.assetModel,
        useDuration: this.useDuration,
        regionCode: this.regionCode[this.regionCode.length - 1],
        professionalCategoryCode: this.activeName === this.tabType.ZY ? this.checkedData.id : '', // 专业类别
        assetCategoryCode: this.activeName === this.tabType.ZC ? strBig : '', // 资产大类
        assetSubcategoryCode: this.activeName === this.tabType.ZC ? strSmall : '' // 资产小类
      }
      if (this.tableClickArry.length > 0) {
        params.deviceIds = this.tableClickArry.map((i) => i.assetsId).join(',')
      } else {
        params.deviceIds = ''
      }
      axios({
        method: 'post',
        url: __PATH.VUE_ICIS_API + 'asset/assetDetails/asset_export',
        data: params,
        responseType: 'blob',
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token,
          'Content-Type': 'application/json',
          'operation-type': 4
        }
      })
        .then((res) => {
          let name = '资产.xlsx'
          const blob = new Blob([res.data])
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
        })
        .catch(() => {
          this.$message.error('导出失败')
        })
    },
    // 模板导出
    getTemplateExport() {
      this.downLoading = true
      const userInfo = store.state.user.userInfo.user
      let params = {
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
      axios({
        method: 'post',
        url: __PATH.VUE_ICIS_API + '/asset/assetDetails/exportAssetTemplate',
        data: qs.stringify(params),
        responseType: 'blob',
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token,
          'operation-type': 4
        }
      })
        .then((res) => {
          this.downLoading = false
          let name = '资产模板.xlsx'
          let blob = new Blob([res.data]) // { type: "application/vnd.ms-excel" }
          let url = window.URL.createObjectURL(blob) // 创建一个临时的url指向blob对象
          // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
          let a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
          // 释放这个临时的对象url
          window.URL.revokeObjectURL(url)
        })
        .catch((res) => {
          this.downLoading = false
          this.$message.error('导出失败！')
        })
    },
    // 资产导入
    getAssetsImport() {
      let formData = new FormData()
      const userInfo = store.state.user.userInfo.user
      this.downLoading = true
      formData.append('unitCode', userInfo.unitCode)
      formData.append('hospitalCode', userInfo.hospitalCode)
      formData.append('userId', userInfo.staffId)
      formData.append('userName', userInfo.staffName)
      formData.append('importSign', '0')
      if (this.assetsFileList && this.assetsFileList.length) {
        formData.append('excelFile', this.assetsFileList[0])
      }
      this.tableLoading = true
      this.subLoading = true
      axios
        .post(__PATH.VUE_ICIS_API + '/asset/assetDetails/importAssets', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
            Authorization: 'Bearer ' + this.$store.state.user.token
          }
        })
        .then((res) => {
          if (res.data.code != 200) {
            this.subLoading = false
            return this.$message.error(res.data.message)
          }
          console.log(res.data.data, 'res.data.datares.data.datares.data.data')
          if (res.data.code == 410) {
            this.$confirm(res.data.message, '提示', {
              closeOnClickModal: false, // 是否点击遮罩（点击空白处）关闭
              showClose: false, // 是否显示右上角的x
              confirmButtonText: '是',
              cancelButtonText: '否',
              type: 'warning'
            })
              .then(() => {
                this.getExcode('1')
              })
              .catch(() => {
                // 取消操作的逻辑
                this.getExcode('2')
              })
          } else {
            if (res.data.code != 200) {
              this.subLoading = false
              return this.$message.error(res.data.message)
            }
            this.activeName = this.tabType.ZY
            this.getProfessionalCategory()
            this.resetForm()
            console.log(res.data.data, 'res.data.data000000000000000000000000000')
            if (res.data.data.fileKey) {
              const dwonLoadDevice = () =>
                axios({
                  url: res.data.data.fileKey,
                  method: 'GET',
                  responseType: 'blob'
                })
                  .then((response) => {
                    const url = window.URL.createObjectURL(new Blob([response.data]))
                    const link = document.createElement('a')
                    link.href = url
                    link.download = res.data.data.fileName
                    document.body.appendChild(link)
                    link.click()
                  })
                  .catch((error) => {
                    console.error('文件下载失败', error)
                  })
              const h = this.$createElement
              this.$message({
                type: 'error',
                duration: 5000,
                showClose: true,
                dangerouslyUseHTMLString: true,
                message: h('p', null, [
                  h('strong', null, `${res.data.message}`),
                  h(
                    'span',
                    {
                      style: 'color: #3562db; cursor: pointer',
                      on: {
                        click: dwonLoadDevice
                      }
                    },
                    '去下载'
                  )
                ])
              })
            } else {
              this.$message.success(res.data.message)
            }
          }
          this.subLoading = false
        })
        .catch((error) => {
          console.log(error, '123---------------------')
        })
      this.assetsImportdialogVisible = false
      this.downLoading = false
      this.tableLoading = false
    },
    getExcode(type) {
      let formData = new FormData()
      const userInfo = store.state.user.userInfo.user
      this.downLoading = true
      formData.append('unitCode', userInfo.unitCode)
      formData.append('hospitalCode', userInfo.hospitalCode)
      formData.append('userId', userInfo.staffId)
      formData.append('userName', userInfo.staffName)
      if (type == 1) {
        formData.append('importSign', '1')
      } else if (type == 2) {
        formData.append('importSign', '2')
      }
      if (this.assetsFileList && this.assetsFileList.length) {
        formData.append('excelFile', this.assetsFileList[0])
      }
      this.tableLoading = true
      this.subLoading = true
      axios
        .post(__PATH.VUE_ICIS_API + '/asset/assetDetails/importAssets', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
            Authorization: 'Bearer ' + this.$store.state.user.token
          }
        })
        .then((res) => {
          if (res.data.code == 200) {
            if (res.data.data.fileKey) {
              const dwonLoadDevice = () =>
                axios({
                  url: res.data.data.fileKey,
                  method: 'GET',
                  responseType: 'blob'
                })
                  .then((response) => {
                    const url = window.URL.createObjectURL(new Blob([response.data]))
                    const link = document.createElement('a')
                    link.href = url
                    link.download = res.data.data.fileName
                    document.body.appendChild(link)
                    link.click()
                  })
                  .catch((error) => {
                    console.error('文件下载失败', error)
                  })
              const h = this.$createElement
              this.$message({
                type: 'error',
                duration: 5000,
                showClose: true,
                dangerouslyUseHTMLString: true,
                message: h('p', null, [
                  h('strong', null, `${res.data.message}`),
                  h(
                    'span',
                    {
                      style: 'color: #3562db; cursor: pointer',
                      on: {
                        click: dwonLoadDevice
                      }
                    },
                    '去下载'
                  )
                ])
              })
            } else {
              this.$message.success(res.data.message)
            }
            this.downLoading = false
            this.tableLoading = false
            this.subLoading = false
            this.getProfessionalCategory()
          }
        })
    },
    // 同步刷新
    synOK() {
      this.activeName = this.tabType.ZY
      this.getProfessionalCategory()
      this.resetForm()
    },
    handleSelectionChange(val) {
      this.tableClickArry = val
    },
    // 巡检码下载
    downLoadQrCode() {
      let downLoadId = []
      downLoadId =
        this.tableClickArry &&
        this.tableClickArry.length &&
        this.tableClickArry.map((item, index) => {
          return item.id
        })
      const userInfo = this.$store.state.user.userInfo.user
      const params = {
        ids: downLoadId.join(',') || '',
        unitCode: userInfo.unitCode || 'BJSYGJ',
        hospitalCode: userInfo.hospitalCode || 'BJSJTYY'
      }
      // const qs = require('qs')
      this.isdownload = true
      fetch(`${__PATH.VUE_ICIS_API}/operation/record/exportAssetsQrCode`, {
        method: 'POST',
        responseType: 'blob',
        body: qs.stringify(params),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      })
        .then((res) => {
          res.blob().then((blob) => {
            this.saveBlobAs(blob)
            this.isdownload = false
          })
        })
        .catch((err) => {
          console.log(err)
          this.isdownload = false
        })
    },
    // 保存blob的方法
    saveBlobAs(blob) {
      if (window.navigator.msSaveOrOpenBlob) {
        navigator.msSaveBlob(blob)
      } else {
        const anchor = document.createElement('a')
        const body = document.querySelector('body')
        anchor.href = window.URL.createObjectURL(blob)
        anchor.download = '设备码.zip'
        anchor.style.display = 'none'
        body.appendChild(anchor)
        anchor.click()
        body.removeChild(anchor)
        window.URL.revokeObjectURL(anchor.href)
      }
    },
    // 关闭资产上传弹窗
    handleAssetsImportClose() {
      this.assetsImportdialogVisible = false
      this.assetsFileList = []
    },
    // 确定资产上传弹窗
    submitAssetsImport() {
      this.getAssetsImport()
    },
    getRegionList() {
      // 所在区域
      this.$api.spaceTree().then((res) => {
        if (res.code == '200') {
          this.regionCodeList = transData(res.data, 'id', 'pid', 'children')
        }
      })
    },
    // 批量删除
    batchDelete() {
      const assetsIds = this.tableClickArry.map((i) => i.assetsId)
      this.$confirm('此操作将永久删除该资产, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.getDelAsset({ assetsId: assetsIds.join(',') }, { 'operation-type': 3 }).then((res) => {
          if (res.code == '200') {
            this.searchForm()
            this.getProfessionalCategory()
          }
        })
      })
    },
    // 根据父节点加载子设备数据数据
    loadSubDevice(parentId) {
      this.childrenDeviceOptions = []
      const param = {
        startLevel: '2',
        parentId,
        levelType: '5'
      }
      return this.$api.getDeviceType(param).then((res) => {
        if (res.code === '200') {
          this.childrenDeviceOptions = transData(res.data, 'id', 'parentId', 'children')
        } else {
          return Promise.reject()
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.el-tree-node__content {
  width: 100%;
  .custom-tree-node {
    display: inline-block;
    width: 100%;
    overflow: hidden;
    .item {
      display: inline-block;
      width: calc(100%);
      // width: calc(100% - 10px);
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
  }
}
::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: linear-gradient(to right, #d9e1f8, #fff);
}
::v-deep .el-tree-node__content {
  height: 32px;
}
.role-content {
  height: 100%;
  display: flex;
  .role-content-left {
    width: 246px;
    height: 100%;
    padding: 10px;
    background: #fff;
    border-radius: 4px;
    margin-right: 12px;
    .left_content {
      width: 100%;
      height: calc(100% - 100px);
      overflow: scroll;
    }
  }
  .role-content-right {
    height: 100%;
    min-width: 0;
    padding: 20px;
    background: #fff;
    border-radius: 4px;
    flex: 1;
    .search-from {
      padding-bottom: 12px;
      // display: flex;
      // justify-content: space-between;
      & > div {
        margin-right: 10px;
      }
      & > button {
        margin-top: 12px;
      }
    }
    .contentTable {
      height: calc(100% - 110px);
      display: flex;
      flex-direction: column;
      .contentTable-main {
        flex: 1;
        overflow: auto;
      }
      .contentTable-footer {
        padding: 10px 0 0;
      }
    }
  }
  .content {
    width: 100%;
    max-height: 500px !important;
    overflow: auto;
    background-color: #fff !important;
  }
}
::v-deep .el-tabs__nav-scroll {
  width: 80%;
  margin: 0 auto;
}
::v-deep .el-drawer__open .el-drawer.rtl {
  background: #f6f5fa;
  border-radius: 4px;
}
::v-deep .el-drawer__header {
  height: 56px;
  padding: 15px 20px;
  margin-bottom: 0;
  background: #fff;
  border-bottom: 1px solid $color-text-secondary;
  box-shadow: 0 0 12px 3px rgb(0 0 0 / 10%);
  & > span {
    font-size: 18px;
    font-family: 'PingFang SC-Medium', 'PingFang SC';
    font-weight: 500;
    color: $color-text;
  }
  .el-dialog__close {
    color: $color-text;
    font-weight: 600;
    font-size: 18px;
  }
}
::v-deep .el-drawer__body {
  background-color: #fff;
  margin: 20px;
  padding: 20px 10px;
  height: calc(100% - 80px);
  overflow-y: auto;
}
::v-deep .el-timeline-item__timestamp.is-bottom {
  font-size: 14px;
  position: absolute;
  left: -100px;
  top: -5px;
  font-weight: 600;
  color: #121f3e;
}
::v-deep .el-timeline {
  padding-left: 120px;
}
.timeContent {
  height: 100%;
  overflow: auto;
  .time {
    font-size: 14px;
    font-family: 'PingFang SC-Regular', 'PingFang SC';
    color: #414653;
  }
  .continer {
    display: flex;
    // justify-content: space-between;
    flex-wrap: wrap;
    .item {
      height: 32px;
      flex: 1;
      padding: 0 16px;
      background-color: #faf9fc;
      line-height: 32px;
      white-space: nowrap;
      margin-bottom: 20px;
      margin-right: 10px;
    }
    .itemContent {
      height: 32px;
      width: 220px;
      padding: 0 16px;
      background-color: #faf9fc;
      line-height: 32px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      cursor: pointer;
      margin-right: 10px;
    }
  }
}
::v-deep .el-timeline-item__node--normal {
  background: #fff;
  border: 2px solid #3562db;
}
.noData {
  display: inline-block;
  padding-bottom: 10px;
  width: 100%;
  margin: 0;
  font-size: 14px;
  color: #999;
  text-align: center;
}
.record {
  color: #66b1ff !important;
}
.delete {
  color: red !important;
}
::v-deep .el-tree-node {
  white-space: normal;
  // outline: 0;
}
.rightBtn {
  height: 36px;
  margin: 0;
  margin-right: 10px;
}
.leadFile {
  display: flex;
}
.leadFile_item {
  margin: 10px 35px;
  color: #66b1ff;
  cursor: pointer;
}
::v-deep .el-message-box.no-close-btn .el-message-box__headerbtn {
  display: none;
}
</style>
