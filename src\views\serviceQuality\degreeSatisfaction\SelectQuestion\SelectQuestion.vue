<template>
  <div class="main-container">
    <el-form ref="selectForm" :model="formItem" :rules="rules" label-width="100px">
      <div style="background-color: #fff;">
        <el-row style="display: flex;">
          <el-col :span="1.5" class="table-label">
            <i class="is-require">*</i>
            <span>标题 :</span>&nbsp;
          </el-col>
          <el-col :span="21">
            <el-form-item prop="name" label-width="0">
              <el-input v-model="formItem.name" type="textarea" rows="1" style="width: 40%;"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <div style="display: flex; padding-right: 10px;">
          <div class="table-label">
            <span class="cgangeStyle">选项：</span>
          </div>
          <el-table :data="formItem.optionsData">
            <el-table-column label="操作" width="100" align="center">
              <template slot-scope="scope">
                <span style="cursor: pointer; padding: 0 5px; color: #3562db;" @click="addOption">新增</span>
                <span v-if="scope.$index != 0" style="cursor: pointer; color: red; padding: 0 5px;" @click.prevent="removeOption(scope.row)">删除</span>
              </template>
            </el-table-column>
            <el-table-column label="选项" align="center">
              <template slot-scope="scope">
                <el-input v-model.trim="scope.row.name" show-word-limit placeholder="请输入选项" :maxlength="50" :readonly="readonly"></el-input>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <el-row style="display: flex; margin-top: 10px; background-color: #fff; align-items: center; padding-left: 10px;">
        <el-col :span="3">
          <el-form-item label-width="0" style="margin: 0;">
            <el-checkbox v-model="isSetMust">是否必填</el-checkbox>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import notice from '../utils/notice'
import utils from '../utils/utils'
// import axiosConter from "../../../../axios/centralControl";
import checkCentralControl from '../check/checkCentralControl'
export default {
  props: {
    questionSubject: {
      type: Object
    }
  },
  data() {
    return {
      readonly: false,
      arr: [],
      formItem: {
        name: '',
        optionsData: [{ name: '', key: this.guid() }],
        isMust: 0 // 是否必填
      },
      rules: checkCentralControl.getCheckConfig('select') // 获取表单校验对象  (下拉菜单校验)
    }
  },
  computed: {
    isSetSore: {
      set(value) {
        this.formItem.score = value ? 1 : 0
        this.resetoptionsDatacore()
      },
      get() {
        return this.formItem.score === 1
      }
    },
    isSetMust: {
      set(value) {
        this.formItem.isMust = value ? 1 : 0
      },
      get() {
        return this.formItem.isMust === 1
      }
    }
  },
  mounted() {
    if (this.questionSubject.id) {
      this.formItem = JSON.parse(JSON.stringify(this.questionSubject))
    }
    // 添加copy数组
    this.arr = JSON.parse(JSON.stringify(this.formItem.optionsData))
    notice.$emit('initChildComponent', this, '下拉菜单')
    notice.$on('handleSubmit', (component) => {
      if (this === component) {
        this.submitForm()
      }
    })
  },
  methods: {
    guid() {
      return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1)
    },
    submitForm() {
      this.$refs['selectForm'].validate((valid) => {
        if (valid) {
          this.saveQuestionSubject()
        } else {
          return false
        }
      })
    },
    deleteArrKey(arrMain, arrFrom) {
      for (var a = 0; a < arrMain.length; a++) {
        for (var b = 0; b < arrFrom.length; b++) {
          if (arrMain[a].id == arrFrom[b].id || (arrMain[a].key && arrMain[a].key == arrFrom[b].key)) {
            arrFrom.splice(b, 1)
          }
        }
      }
    },
    saveQuestionSubject(callBack) {
      this.deleteArrKey(this.formItem.optionsData, this.arr)
      // 合并数组
      let len = this.formItem.optionsData.filter((item) => item.name == '')
      if (len.length) {
        return this.$message.error('请检查选项是否填写完整')
      }
      var arrCopy = this.arr.concat(this.formItem.optionsData)
      const params = {
        id: this.questionSubject.id ? this.questionSubject.id : '',
        type: 'select',
        name: this.formItem.name,
        isMust: this.formItem.isMust,
        pvqId: localStorage.getItem('questId'),
        options: JSON.stringify(arrCopy)
      }
      if (params.id == '') {
        this.$api.saveQuestion(params).then((res) => {
          if (res.status == 200) {
            this.$message({
              message: res.message,
              type: 'success'
            })
            notice.$emit('closeQuestionDialog', this)
          }
        })
      } else {
        this.$api.updateQuestion(params).then((res) => {
          if (res.status == 200) {
            this.$message({
              message: res.message,
              type: 'success'
            })
            notice.$emit('closeQuestionDialog', this)
          }
        })
      }
    },
    duplicateRemoval(arr) {
      for (let index = 0; index < arr.length; index++) {
        const element = arr[index]
        for (var a = 0; a < arr.length; a++) {
          if (element.id && element.id == arr[a].id && index != a) {
            if (element.name == '') {
              arr.splice(index, 1)
              index--
              a--
            } else {
              arr.splice(a, 1)
              a--
            }
          }
        }
      }
    },
    // 删除存储数组
    arrCopyCheck(item) {
      for (var a = 0; a < this.arr.length; a++) {
        if (this.arr[a].id === item.id) {
          this.arr[a].isDelete = 1
        }
      }
    },
    // 删除数组中的项
    deleteArr(item, arrFlag, itemFlag) {
      for (var a = 0; a < this.formItem.optionsData.length; a++) {
        if (this.formItem.optionsData[a][arrFlag] === item[itemFlag]) {
          this.formItem.optionsData.splice(a, 1)
        }
      }
    },
    // 删除选项成功之后重置表单数据
    resetOptionData(data) {
      this.formItem = { ...data, rowCount: this.questionSubject.rowCount + '' }
    },
    removeOption(item) {
      // 编辑时删除选项
      if (this.questionSubject.id) {
        if (item.id) {
          this.arrCopyCheck(item)
          this.deleteArr(item, 'id', 'id')
        } else {
          this.deleteArr(item, 'key', 'key')
        }
      } else {
        // 新建时删除选项
        var index = this.formItem.optionsData.indexOf(item)
        if (index !== -1) {
          this.formItem.optionsData.splice(index, 1)
        }
      }
    },
    addOption() {
      this.formItem.optionsData.push({
        name: '',
        key: this.guid()
      })
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
.main-container {
  width: 100%;

  textarea {
    resize: none;
  }

  .el-input__inner {
    width: 100%;
  }

  .el-dialog__body {
    padding-bottom: 20px;
  }

  .table-label {
    text-align: left;
    box-sizing: border-box;
    padding-left: 15px;
    margin-top: 10px;
  }

  .is-require {
    position: absolute;
    top: 0;
    left: 0%;
    color: red;
    padding-top: 12px;
  }

  .operator-container {
    text-align: center;
    line-height: 40px;

    .row-option {
      cursor: pointer;
      display: inline-block;
      width: 16px;
      height: 16px;
      vertical-align: middle;
      background-size: 100% 100%;
    }

    .add-option {
      background-image: url(../../../../assets/images/ic-add.png);
    }

    .remove-option {
      background-image: url(../../../../assets/images/<EMAIL>);
    }
  }
}
</style>
