import Layout from '@/layout'
import EmptyLayout from '@/layout/empty'
export default [
  {
    path: '/tissueArchitecture',
    component: Layout,
    name: 'tissueArchitecture',
    // redirect: {name: 'microApp'},
    meta: {
      title: '组织架构',
      menuAuth: '/tissueArchitecture'
    },
    children: [
      {
        path: 'memberArchitecture',
        component: EmptyLayout,
        meta: {
          title: '成员与架构',
          menuAuth: '/tissueArchitecture/memberArchitecture'
        },
        children: [
          {
            path: '',
            name: 'memberArchitecture',
            component: () => import('@/views/microApp/index.vue'),
            // component: () => import('@/views/operationPort/parkingManage/entryRecordManage.vue'),
            meta: {
              title: '成员与架构',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'hospitalManage',
        component: EmptyLayout,
        meta: {
          title: '院区管理',
          menuAuth: '/tissueArchitecture/hospitalManage'
        },
        children: [
          {
            path: '',
            name: 'hospitalManage',
            component: () => import('@/views/microApp/index.vue'),
            // component: () => import('@/views/operationPort/parkingManage/entryRecordManage.vue'),
            meta: {
              title: '院区管理',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'postManage',
        component: EmptyLayout,
        meta: {
          title: '岗位管理',
          menuAuth: '/tissueArchitecture/postManage'
        },
        children: [
          {
            path: '',
            name: 'hospitalManage',
            component: () => import('@/views/microApp/index.vue'),
            // component: () => import('@/views/operationPort/parkingManage/entryRecordManage.vue'),
            meta: {
              title: '岗位管理',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: ':page*',
        name: 'microApp',
        component: () => import('@/views/microApp/index.vue')
      }
    ]
  },
  {
    path: '/baseAsset',
    component: Layout,
    name: 'baseAsset',
    meta: {
      title: '资产管理',
      menuAuth: '/baseAsset'
    },
    children: [
      {
        path: 'assetManage',
        component: EmptyLayout,
        meta: {
          title: '资产管理',
          menuAuth: '/baseAsset/assetManage'
        },
        children: [
          {
            path: '',
            name: 'assetManage',
            component: () => import('@/views/microApp/index.vue'),
            meta: {
              title: '资产管理',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: ':page*',
        name: 'microApp',
        component: () => import('@/views/microApp/index.vue')
      }
    ]
  },
  {
    path: '/baseSpace',
    component: Layout,
    name: 'baseSpace',
    meta: {
      title: '空间管理',
      menuAuth: '/baseSpace'
    },
    children: [
      {
        path: 'spaceList',
        component: EmptyLayout,
        meta: {
          title: '空间列表',
          menuAuth: '/baseSpace/spaceList'
        },
        children: [
          {
            path: '',
            name: 'spaceList',
            component: () => import('@/views/microApp/index.vue'),
            meta: {
              title: '空间列表',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'ichnography',
        component: EmptyLayout,
        meta: {
          title: '空间平面图',
          menuAuth: '/baseSpace/ichnography'
        },
        children: [
          {
            path: '',
            name: '空间平面图',
            component: () => import('@/views/microApp/index.vue'),
            meta: {
              title: '空间平面图',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: ':page*',
        name: 'microApp',
        component: () => import('@/views/microApp/index.vue')
      }
    ]
  },
  {
    path: '/spaceSystemSetUp',
    component: Layout,
    name: 'spaceSystemSetUp',
    meta: {
      title: '系统设置',
      menuAuth: '/spaceSystemSetUp'
    },
    children: [
      {
        path: 'spaceFnType',
        component: EmptyLayout,
        meta: {
          title: '空间功能类型',
          menuAuth: '/spaceSystemSetUp/spaceFnType'
        },
        children: [
          {
            path: '',
            name: 'spaceFnType',
            component: () => import('@/views/baseInfo/spaceFnType/index.vue'),
            meta: {
              title: '空间功能类型',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      }
    ]
  }
]
