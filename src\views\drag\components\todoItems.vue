<!--
 * @Author: hedd
 * @Date: 2023-03-07 15:27:01
 * @LastEditTime: 2025-03-11 17:27:58
 * @FilePath: \ihcrs_pc\src\views\drag\components\todoItems.vue
 * @Description:
 *
-->
<template>
  <ContentCard
    :title="item.componentTitle"
    :scrollbarHover="true"
    :cstyle="{ height: '100%' }"
    class="drag_class"
    :hasMoreOper="['more', 'edit']"
    @more-oper-event="(val) => $emit('all-more-Oper', val, 'todoItems')"
  >
    <div id="getNeedScrollTop" slot="content" class="infinite-list-wrapper items-content">
      <ul v-infinite-scroll="recordLoad" class="list" infinite-scroll-disabled="disabled" @dblclick.stop="addTodoDialog()">
        <li v-for="(item, i) in msgDataList" :key="i" :class="{ 'todoItem-check': item.check && item.msgType == 2, 'list-item': true }">
          <div v-if="item.msgType == 1" class="msgBox">
            <div class="msg-main">
              <!-- <svg-icon :name="msgIconList[item.msgCatId].icon" /> -->
              <div class="msg-type" :class="{ 'red-round': item.readStatus == 0 }">{{ item.msgSysTypeName }}</div>
              <div class="msg-title" @click="navigationTo(item)">
                <div class="msg-text">{{ item.msgTitle }}</div>
              </div>
            </div>
            <div class="msg-time">{{ item.publishTime }}</div>
          </div>
          <div v-else-if="item.msgType == 2" class="tipBox">
            <el-checkbox v-model="item.check" @change="checkChange($event, item)"></el-checkbox>
            <div class="msg-type">【工作提醒】</div>
            <div class="msg-title" @click="navigationTo(item)">
              <div class="msg-text">{{ item.msgTitle }}</div>
            </div>
            <div class="msg-time">{{ item.publishTime }}</div>
          </div>
        </li>
      </ul>
      <p v-if="loading">加载中...</p>
      <p v-if="noMore">没有更多了</p>
      <div class="empty-seat" @dblclick.stop="addTodoDialog()"></div>
      <todoItemDialog v-if="todoItemDialogShow" :visible.sync="todoItemDialogShow" :itemData="itemData" @closeTodoDialog="closeTodoDialog" />
      <template v-if="workOrderDetailCenterShow">
        <el-dialog :visible.sync="workOrderDetailCenterShow" custom-class="detailDialog main" :close-on-click-modal="false" :before-close="workOrderDetailCloseDialog">
          <template slot="title">
            <span class="dialog-title">{{ dialogTitle }}</span>
          </template>
          <component :is="iomsComponent" :rowData="detailObj" />
        </el-dialog>
      </template>
      <template v-if="alarmDetailShow">
        <AlarmDetailDialog ref="alarmDetail" :alarmId="alarmDetail.alarmId" :visible.sync="alarmDetailShow" @update:visible="closeAlarmDialog" />
      </template>
      <template v-if="workOrderDetailShow">
        <WorkOrderDetailDialog sourceType="alarm" :visible.sync="workOrderDetailShow" :alarmDetail="alarmDetail" :dialogDetail="workOderDialogData" />
      </template>
    </div>
  </ContentCard>
</template>
<script>
import { msgIconList } from '@/util/dict.js'
import { mapGetters } from 'vuex'
export default {
  name: 'todoItems',
  components: {
    todoItemDialog: () => import('./dialog/todoItemDialog.vue'),
    workOrderDetailYS: () => import('@/views/serviceQuality/transport/components/workOrderDetailList.vue'),
    workOrderDetailWX: () => import('@/views/serviceQuality/maintenance/components/workOrderDetailList.vue'),
    workOrderDetailBJ: () => import('@/views/serviceQuality/cleaning/components/workOrderDetailList.vue'),
    workOrderDetailSSP: () => import('@/views/serviceQuality/snapshot/components/workOrderDetailList.vue')
  },
  props: {
    item: {
      type: Object,
      default: () => {}
    }
    // setSocketMsgs: {
    //   type: Object,
    //   default: () => { }
    // }
  },
  data() {
    return {
      alarmDetailShow: false,
      alarmDetail: {},
      workOrderDetailShow: false, // 工单详情弹窗
      workOderDialogData: [], // 工单详情传递数据
      total: 20,
      page: 1,
      loading: false,
      msgIconList: Object.freeze(msgIconList),
      msgDataList: [],
      todoItemDialogShow: false, // todolist对应弹窗
      itemData: {},
      workOrderDetailCenterShow: false, // 工单详情弹窗
      dialogTitle: '', // 弹窗标题
      detailObj: {}, // 工单详情传递数据
      iomsComponent: 'workOrderDetailWX', // 工单详情组件(维修)
      pageSize: 10
    }
  },
  computed: {
    noMore() {
      return this.msgDataList.length >= this.total
    },
    disabled() {
      return this.loading || this.noMore
    },
    ...mapGetters({
      socketNoticeMsgs: 'socket/socketNoticeMsgs'
    })
  },
  watch: {
    socketNoticeMsgs: {
      handler(item) {
        const msg = JSON.parse(item)
        if (msg) {
          if (msg.object.msgType == 1 || msg.object.msgType == 2) {
            this.todoMessageInit()
          }
        }
      },
      deep: true // 深度监听
    }
  },
  mounted() {
    this.todoMessageInit()
  },
  methods: {
    todoMessageInit() {
      // 查看sessionStorage是否有存储的分页数据 与 滚动条位置
      var needDataPosition = JSON.parse(sessionStorage.getItem('needDataPosition'))
      if (needDataPosition) {
        this.page = 1
        this.total = 0
        this.msgDataList = []
        this.getMessageByUserId(needDataPosition)
      } else {
        this.page = 1
        this.total = 0
        this.msgDataList = []
        // this.loading = true
        this.getMessageByUserId()
      }
    },
    // 获取消息列表
    getMessageByUserId(needDataPosition) {
      const params = {
        userId: this.$store.state.user.userInfo.user.staffId,
        msgType: 1, // 0:消息 1:待办 2:todolist
        msgSysId: 0,
        page: this.page,
        // 有存储的分页数据则先请求之前历史数据
        pageSize: needDataPosition ? needDataPosition.page * needDataPosition.pageSize : this.pageSize
      }
      this.$api
        .getMessageByUserId(params)
        .then((res) => {
          this.loading = false
          if (res.code == 200) {
            res.data.list =
              (res.data.list?.length &&
                res.data.list?.map((item) => {
                  return {
                    ...item,
                    check: item.readStatus == 1
                  }
                })) ??
              []
            this.msgDataList = res.data.list ? this.msgDataList.concat(res.data.list) : this.msgDataList
            this.total = res.data.total
            if (needDataPosition) {
              // 历史数据渲染加载完成 将历史的分页页数放入当前的分页页数中  下次请求接着走之前的分页  将滚动条跳至上次位置
              this.$nextTick(() => {
                this.page = needDataPosition.page
                document.querySelector('#getNeedScrollTop').parentNode.scrollTop = needDataPosition.scrollTop
              })
              // 清除缓存的位置  之后不在加载历史数据
              sessionStorage.removeItem('needDataPosition')
            }
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
    recordLoad() {
      this.page++
      this.loading = true
      this.getMessageByUserId()
    },
    // 待办事项选中修改
    checkChange(flag, item) {
      const params = {
        msgId: item.msgId,
        readStatus: flag ? 1 : 0,
        userId: this.$store.state.user.userInfo.user.staffId
      }
      this.$api.finishToDoListLevel(params).then((res) => {
        if (res.code == 200) {
          // this.$message.success('修改成功')
        } else {
          this.$message.error(res.msg)
          this.todoMessageInit()
        }
      })
    },
    // 跳转查看详情
    navigationTo(item) {
      // 跳转前记录当前分页页数 与请求每页数量  记录滚动条位置  存在sessionStorage
      var scrollTop = document.querySelector('#getNeedScrollTop').parentNode.scrollTop
      let needDataPosition = {
        page: this.page,
        pageSize: this.pageSize,
        scrollTop
      }
      sessionStorage.setItem('needDataPosition', JSON.stringify(needDataPosition))
      if (item.msgType == 2) {
        this.itemData = item
        // todolist对应弹窗
        this.todoItemDialogShow = true
      } else {
        const selectTypeData = msgIconList[item.msgCatId]
        // 通知公告
        if (selectTypeData.type == 'system') {
          this.$router.push({
            path: '/drag/noticeDetail',
            query: {
              msgId: item.msgId,
              msgSysId: item.msgSysId
            }
          })
          return
        }
        if (selectTypeData.type == 'drillSure') {
          this.$message.error('请前往App确认')
          return
        }
        const params = {
          userId: this.$store.state.user.userInfo.user.staffId,
          msgId: item.msgId,
          msgSysId: item.msgSysId
        }
        this.$api.getImserverMsgInfo(params).then((res) => {
          if (res.code == 200) {
            const data = res.data
            let msgBody = {}
            try {
              msgBody = JSON.parse(data.msgBody)
            } catch (error) {
              return this.$message.error('消息体解析失败，请联系管理员')
            }
            if (!Object.keys(msgBody).length) {
              return this.$message.warning('此消息暂无详情数据')
            }
            const selectTypeData = msgIconList[data.msgCatId]
            const elements = selectTypeData?.elements?.split(',') ?? []
            // 遍历msgBody对象保留elements中的属性
            for (const key in msgBody) {
              if (!elements.includes(key)) {
                delete msgBody[key]
              }
            }
            // msgBody的元素数量不等于elements的元素数量，说明有查询属性缺失
            if (Object.keys(msgBody).length != elements.length) {
              return this.$message.warning('消息数据缺失，请联系管理员')
            }
            if (selectTypeData.type == 'ioms') {
              // 确警工单
              if (selectTypeData.typeCode == 16) {
                this.workOderDialogData = [
                  {
                    workTypeName: msgBody.type,
                    id: msgBody.workNum,
                    active: true
                  }
                ]
                this.workOrderDetailShow = !this.workOrderDetailShow
              } else {
                this.detailObj = {
                  id: msgBody.workNum
                }
                this.dialogTitle = `${msgBody.type}（${msgBody.flowType}）`
                if (selectTypeData?.component) {
                  this.iomsComponent = selectTypeData?.component
                  this.workOrderDetailCenterShow = true
                } else {
                  this.$message.warning('该工单类型暂未开放')
                }
              }
            } else if (selectTypeData.type == 'icis') {
              this.$router.push({
                path: '/InspectionManagement/taskManagement/taskDetail',
                query: {
                  taskId: msgBody.id,
                  type: 'detail',
                  systemType: selectTypeData.typeCode
                }
              })
            } else if (selectTypeData.type == 'warn') {
              this.alarmDetail = msgBody
              // this.alarmDetailShow = true
              this.$router.push({
                path: '/allAlarm/alarmDetail',
                query: {
                  alarmId: msgBody.alarmId
                }
              })
            } else if (selectTypeData.type == 'drill') {
              this.$router.push({
                path: '/exerciseManage/exerciseTask/exerciseTaskDetail',
                query: {
                  taskId: msgBody.id
                }
              })
            } else if (selectTypeData.type == 'life') {
              // 寿命提醒功能 跳转设备详情
              const { id, assetsId, configType } = msgBody
              this.$router.push({
                name: 'addDevice',
                query: {
                  alarmId: msgBody.alarmId,
                  type: 'details',
                  configType,
                  id,
                  assetsId
                }
              })
            } else if (selectTypeData.type == 'approve') {
              // 审批消息 跳转审批详情
              const { projectCode, processInstanceId, status } = msgBody
              this.$router.push({
                path: '/operation/flowFormDetail',
                query: {
                  detailId: projectCode,
                  flowId: processInstanceId,
                  handleType: 'detail',
                  // handleType: status == 'finished' ? 'detail' : 'handle',
                  status
                }
              })
            } else {
              this.$message.warning('该消息类型暂未开放')
            }
          }
        })
      }
    },
    // 双击todo弹窗
    addTodoDialog() {
      this.itemData = {}
      this.todoItemDialogShow = true
    },
    closeTodoDialog() {
      // this.todoItemDialogShow = false
      this.todoMessageInit()
    },
    // 工单详情弹窗关闭
    workOrderDetailCloseDialog() {
      this.workOrderDetailCenterShow = false
      this.todoMessageInit()
    },
    // 报警详情弹窗关闭
    closeAlarmDialog() {
      this.todoMessageInit()
    }
  }
}
</script>
<style lang="scss" scoped>
.items-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  ul {
    list-style-type: none;
    padding: 10px 0;
    margin: 0;
    .msg-title {
      flex: 1;
      display: flex;
      align-items: center;
      cursor: pointer;
      font-size: 14px;
      font-family: 'PingFang SC-Regular', 'PingFang SC';
      font-weight: 400;
      color: #666666;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      .msg-text {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .msg-time {
      font-size: 13px;
      font-family: 'PingFang SC-Regular', 'PingFang SC';
      color: #7f848c;
      white-space: nowrap;
    }
    .msgBox {
      margin: 3px 0;
      padding: 5px 5px 15px 5px;
      .msg-main {
        display: flex;
        justify-content: space-between;
        overflow: hidden;
        margin-bottom: 6px;
      }
      svg {
        width: 18px;
        height: 18px;
      }
      .msg-type {
        min-width: 20px;
        padding: 1px 6px;
        white-space: nowrap;
        background: rgba(53, 98, 219, 0.1);
        border-radius: 4px 4px 4px 4px;
        color: #3562db;
        font-size: 12px;
        margin-right: 4px;
        position: relative;
      }
      .red-round::after {
        content: '';
        position: absolute;
        width: 5px;
        height: 5px;
        background: #fa403c;
        border-radius: 50%;
        box-shadow: 0 0 2px 3px rgba($color: #fff, $alpha: 100%);
        right: 0px;
        top: 0px;
      }
      .red-round {
      }
    }
    .tipBox {
      margin: 3px 0;
      padding: 5px;
      display: flex;
      justify-content: space-between;
      overflow: hidden;
      position: relative;
      .msg-type {
        white-space: nowrap;
      }
    }
    .todoItem-check {
      background: rgb(53 98 219 / 10%);
    }
  }
  & > p {
    text-align: center;
    padding: 0 0 5px;
    margin: 0 0 10px;
    font-size: 14px;
    color: #999;
    font-family: PingFangSC-Regular;
  }
  .empty-seat {
    flex: 1;
  }
}
</style>
