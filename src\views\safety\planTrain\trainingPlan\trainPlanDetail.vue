<template>
  <PageContainer v-loading="contentLoading">
    <div slot="header" class="table-content">
      <div class="topFilter">
        <div class="backBar">
          <span style="cursor: pointer" @click="$router.go(-1)">
            <i class="el-icon-arrow-left"></i>
            培训计划详情
          </span>
        </div>
      </div>
      <div class="tabsList">
        <el-tabs @tab-click="handleClick">
          <el-tab-pane v-for="(item, index) in tabsList" :key="index" :label="item.name"
            :class="{ active: activeIndex == index }"></el-tab-pane>
        </el-tabs>
      </div>
      <!-- 培训信息 -->
      <div class="baseInfo" v-if="activeIndex == 0">
        <h1>基础信息</h1>
        <div class="contenter">
          <div class="itemInfo">
            <div class="itemInfoName">
              <div class="itemLable">培训名称：</div>
              <div class="itemInfoIndex">{{ dataList.name }}</div>
            </div>
            <div class="itemInfoName">
              <div class="itemLable">培训类型：</div>
              <div class="itemInfoIndex">{{ dataList.subjectName }}</div>
            </div>
            <div class="itemInfoName">
              <div class="itemLable">所属单位：</div>
              <div class="itemInfoIndex">{{ dataList.deptName }}</div>
            </div>
            <div class="itemInfoName">
              <div class="itemLable">培训老师:</div>
              <div class="itemIndex">{{ dataList.teacherName }}</div>
            </div>
          </div>
          <div class="itemInfo">
            <div class="itemInfoName">
              <div class="itemLable">培训时间：</div>
              <div class="itemInfoIndex">{{ dataList.startTime }}</div>
            </div>
            <div class="itemInfoName">
              <div class="itemLable" style="width: 100px;">培训方式：</div>
              <div class="itemInfoIndex">{{ dataList.type == 0 ? '线上会议' : '线下会议' }}</div>
            </div>
            <div class="itemInfoName">
              <div class="itemLable">培训地址：</div>
              <div class="itemInfoIndex">{{ dataList.address }}</div>
            </div>
          </div>
          <div class="itemInfo">
            <div class="itemLable">必修学员：</div>
            <div>{{ dataList.teacherName }}</div>
          </div>
        </div>
      </div>
      <!-- 考试信息 -->
      <div class="baseInfo" v-if="activeIndex == 1">
        <h1>考试信息</h1>
        <div class="contenter">
          <div class="itemInfo">
            <div class="itemInfoName">
              <div class="itemLable">试卷名称：</div>
              <div class="itemInfoIndex">{{ examinDetail.name }}</div>
            </div>
            <div class="itemInfoName">
              <div class="itemLable">所属单位：</div>
              <div class="itemInfoIndex">{{ examinDetail.deptName }}</div>
            </div>
            <div class="itemInfoName">
              <div class="itemLable">所属科目：</div>
              <div class="itemInfoIndex">{{ examinDetail.subjectName }}</div>
            </div>
            <div class="itemInfoName">
              <div class="itemLable">考试期限:</div>
              <div class="itemIndex">{{ examinDetail.startTime }} - {{ examinDetail.endTime }}</div>
            </div>
            <div class="itemInfoName">
              <div class="itemLable">答题时长:</div>
              <div class="itemIndex">{{ examinDetail.duration }} 分钟</div>
            </div>
          </div>
          <div class="itemInfo">
            <div class="itemInfoName">
              <div class="itemLable">试题总分：</div>
              <div class="itemInfoIndex">{{ examinDetail.score }} 分</div>
            </div>
            <div class="itemInfoName">
              <div class="itemLable" style="width: 100px;">通过分数：</div>
              <div class="itemInfoIndex">{{ examinDetail.passScore }}</div>
            </div>
            <div class="itemInfoName">
              <div class="itemLable">考试不通过：</div>
              <!-- <div class="itemInfoIndex">{{ examinDetail.rejectDeal == 1 ? '不补考' : examinDetail.rejectDeal == 2 ?
    '自动组卷补考' : '手动派发补考试卷' }}</div> -->
            </div>
            <div class="itemInfoName">
              <div class="itemLable">考试通过发放证书：</div>
              <div class="itemInfoIndex">{{ examinDetail.credentialName }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div slot="content" class="courseContent" v-if="activeIndex == 0">
      <div>
        <div class="top">
          <h1>培训课件</h1>
        </div>
        <div class="table_content">
          <div v-for="(k, index) in trainList" :key="k.id" class="item">
            <div>
              <img src="../../../../assets/images/doc.png" alt="" />
              <span>{{ index + 1 }}.{{ k.originalFilename }}</span>
            </div>
            <div class="operate">
              <span @click="ckeckFile(k)">查看</span>
              <span @click="download(k)">下载</span>
            </div>
          </div>
        </div>
      </div>
      <div style="margin: 15px 0px;">
        <div class="top">
          <h1>模板课件</h1>
        </div>
        <div class="table_content">
          <div v-for="(k, index) in tmpsList" :key="k.id" class="item">
            <div>
              <img src="../../../../assets/images/doc.png" alt="" />
              <span>{{ index + 1 }}.{{ k.originalFilename }}</span>
            </div>
            <div class="operate">
              <span @click="ckeckFile(k)">查看</span>
              <span @click="download(k)">下载</span>
            </div>
          </div>
        </div>
      </div>
      <div>
        <div class="top">
          <h1>关联课程资源</h1>
        </div>
        <div class="courseKjBox" v-if="coursePlanList ? coursePlanList.length > 0 : []">
          <div class="courseMbnav" v-for="(item, index) in coursePlanList" :key="index">
            <div class="courseMbtitie">
              <div class="courseMbBox">
                <div>名称:</div>
                <div>{{ item.courseName }}</div>
              </div>
              <div class="courseMbBox">
                <div>课程类型：</div>
                <div>{{ item.subjectName }}</div>
              </div>
              <div class="courseMbBox">
                <div>课时数：</div>
                <div>{{ item.periodCount }}</div>
              </div>
              <div class="courseMbBox">
                <div>创建人：</div>
                <div>{{ item.createName }}</div>
              </div>
              <div class="ck">
                <div @click="courseDetils(item)">查看</div>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>
    <div slot="content" class="courseContent" v-if="activeIndex == 1">
      <div class="table_content">
        <div v-for="(k, ind) in dataList.questions" :key="k.id" :name="k.id"
          :class="['exercisesItem', k.isExpand ? 'expand' : '']" :ref="'exercisesItem' + ind">
          <div class="exercisesTop">
            <div class="left">
              <div class="exercisesType">
                {{ k.type == "1" ? "单选题" : k.type == "2" ? "多选题" : "判断题" }}
              </div>
              <span>分数：{{ k.score }}分</span>
            </div>
            <div class="right">
              <el-input type="number" placeholder="小题分数" min="1" style="width: 160px" v-model="k.score">
                <template slot="append">分</template>
              </el-input>
              <span @click="isExpandBtn(k, index)">{{ k.isExpand ? "折叠" : "展开" }}</span>
            </div>
          </div>
          <div :class="['exercisesName', k.isExpand ? '' : 'title']">
            {{ k.topic }}
          </div>
          <el-radio-group v-if="k.type == '1'" class="radio" v-model="k.answer" disabled>
            <el-radio v-for="(j, index) in k.options" :key="index" :label="j.id">{{ j.id }}. {{ j.label }}</el-radio>
          </el-radio-group>
          <el-checkbox-group v-if="k.type == '2'" v-model="k.answer" class="radio" disabled>
            <el-checkbox v-for="(j, index) in k.options" :key="index" :label="j.id">{{ j.id }}. {{ j.label
              }}</el-checkbox>
          </el-checkbox-group>
          <p>答案：{{ k.answer }}</p>
          <p>
            解析：
            {{ k.analysis }}
          </p>
        </div>
      </div>
    </div>

  </PageContainer>
</template>
<script>
  import moment from "moment";
  import axios from 'axios'
  export default {
    data() {
      return {
        tabsList: [{
            name: '培训信息',
            value: '0',
          },
          {
            name: '考试信息',
            value: '1',
          },
        ],
        contentLoading: false,
        routeInfo: "",
        moment,
        id: "",
        examine: '', // 查看文件状态
        dataList: {}, // 模板信息
        trainList: [], // 模板课件
        coursewareList: [{
          id: '0',
          name: '课件名称'
        }],
        drawerDialog: false,
        paginationData: {
          pageNo: 1,
          pageSize: 15,
          total: 0
        },
        idsy: [],
        activeIndex: 0,
        questionsList: [],
        examinDetail: '',
        tmpsList: [],
        coursePlanList: [],
      };
    },
    created() {
      this.routeInfo = JSON.parse(sessionStorage.getItem("routeInfo"));
      this.id = this.$route.query.id
      if (this.id) {
        this.getDetails();
      }
    },
    methods: {
      // 切换tabs
      handleClick(tab, event) {
        this.activeIndex = tab.index
        console.log(this.activeIndex, 'this.activeIndexthis.activeIndexthis.activeIndexthis.activeIndex');
      },
      // 获取详情
      getDetails() {
        this.contentLoading = true;
        this.$api.trainPlanDetal({
          id: this.id
        }).then(res => {
          if (res.data.questions != null) {
            res.data.questions.forEach(item => {
              item.isExpand = false
              item.options = JSON.parse(item.options)
              if (item.type == '2') {
                item.answer = item.answer
              }
            });
          }
          this.dataList = res.data;
          if (res.data.materials) {
            this.trainList = res.data.materials
          }
          if (res.data.examInfos) {
            res.data.examInfos.forEach(el => {
              this.examinDetail = el;

            })
          }
          if (res.data.tmps) {
            res.data.tmps.forEach(el => {
              this.tmpsList = el.fileUploadRecords
            })
          }
          if (res.data.courses) {
            this.coursePlanList = res.data.courses
          }
        })
        this.contentLoading = false;
      },
      // 查看文件
      ckeckFile(k) {
        this.$router.push({
          path: 'seeTrainFile',
          query: {
            url: k.viewAddress,
            name: k.originalFilename
          }
        })
      },
      // 查看详情
      courseDetils(item) {
        this.$router.push({
          path: "courseInfo",
          query: {
            id: item.id
          }
        });
      },
      // 下载试题类型模板
      download(k) {
        this.idsy = []
        this.idsy.push(k.id)
        let params = {
          ids: this.idsy
        }
        let httpname = '/minio/downloadBatch'
        axios({
            method: 'post',
            url: __PATH.BASE_URL_LABORATORY + httpname,
            data: params,
            responseType: 'blob',
            headers: {
              "Content-Type": "application/json",
            }
          })
          .then((res) => {
            let name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
            let blob = new Blob([res.data]) // { type: "application/vnd.ms-excel" }
            let url = window.URL.createObjectURL(blob) // 创建一个临时的url指向blob对象
            // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
            let a = document.createElement('a')
            a.href = url
            a.download = decodeURI(name)
            a.click()
            // 释放这个临时的对象url
            window.URL.revokeObjectURL(url)
          })
          .catch((res) => {
            this.$message.error('下载失败！')
          })
      },
      // 全部下载
      allDownload() {
        let httpname = 'trainTmp/downloadCoursewares'
        let params = {
          id: this.dataList.id,
        }
        axios({
            method: 'post',
            url: __PATH.BASE_URL_LABORATORY + httpname,
            data: params,
            responseType: 'blob',
            headers: {
              "Content-Type": "application/json",
            }
          })
          .then((res) => {
            let name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
            let blob = new Blob([res.data]) // { type: "application/vnd.ms-excel" }
            let url = window.URL.createObjectURL(blob) // 创建一个临时的url指向blob对象
            // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
            let a = document.createElement('a')
            a.href = url
            a.download = decodeURI(name)
            a.click()
            // 释放这个临时的对象url
            window.URL.revokeObjectURL(url)
          })
          .catch((res) => {
            this.$message.error('下载失败！')
          })
      },
      // 展开试题
      isExpandBtn(item, index) {
        console.log(item, 'item');
        item.isExpand = !item.isExpand
      },
      // 上移选择项
      moveUp(index) {
        if (index != 0) {
          let nums = this.questionsList;
          [nums[index], nums[index - 1]] = [nums[index - 1], nums[index]];
          this.questionsList = [...nums];
        } else {
          this.$message.error('禁止上移')
        }
      },
      //下移选择项
      moveDown(index) {
        if (this.questionsList.length && index != this.questionsList.length - 1) {
          let nums = this.questionsList;
          [nums[index], nums[index + 1]] = [nums[index + 1], nums[index]];
          this.questionsList = [...nums];
        } else {
          this.$message.error('禁止下移')
        }
      },
      deleteQuestions(index) {
        this.questionsList.splice(index, 1);
      },
      handleSizeChange() {},
      handleCurrentChange() {}
    },
  };

</script>
<style lang="scss" scoped>
  .table-content {
    background: #fff;
    border-radius: 4px;
    height: calc(100% - 0px);

    .baseInfo {
      padding: 0 24px 24px 24px;

      .contenter {
        padding-top: 24px;
        font-size: 14px;

        .itemInfo {
          margin-bottom: 16px;
          display: flex;

          .title {
            width: 120px;
            color: #666;
            margin-top: 3px;
          }

          .value {
            flex: 1;
            line-height: 20px;
          }
        }
      }
    }
  }

  .courseKjBox {
    width: 100%;
    height: 180px;
    overflow-y: auto;
    padding: 0px 10px;
    background-color: #F6F5FA;
  }

  .courseMbnav {
    width: 100%;
    height: 64px;
    border-radius: 4px;
    opacity: 1;
    background-color: #fff;
    margin: 10px 0px;
    color: #333333;
    font-size: 14px;

  }

  .topFilter {
    padding: 15px;
    height: 60px;
    background-color: #fff;

    .backBar {
      color: #121f3e;
      height: 30px;
      border-bottom: 1px solid #dcdfe6;
    }
  }

  .courseMbtitie {
    height: 100%;
    display: flex;
    justify-content: space-between;
  }

  .table-content {
    background: #fff;
    border-radius: 4px;
    height: calc(100% - 0px);
  }

  .courseBoxw {
    padding: 16px;
    height: 550px;
  }

  .courseMbBox {
    display: flex;
    align-items: center;
    padding: 0px 10px;
    width: 20%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    .courseMbBoxLable {
      margin-right: 5px;
    }

    .courseMbBoxItem {
      width: 60%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .ck {
    width: 50px;
    display: flex;
    align-items: center;
    color: #3562DB;
    cursor: pointer;
  }

  .courseContent {
    height: 100%;
    margin-top: 16px;
    background-color: #fff;
    padding: 16px;
    overflow: auto;

    .top {
      margin-bottom: 20px;
      display: flex;
      justify-content: space-between;

      span {
        font-size: 14px;
        color: #3562DB;
      }
    }

    .table_content {
      height: calc(100% - 70px);
      overflow: auto;

      .item {
        height: 56px;
        font-size: 14px;
        line-height: 56px;
        display: flex;
        justify-content: space-between;

        img {
          vertical-align: middle;
          margin-right: 12px;
        }

        .operate {
          span {
            cursor: pointer;
            color: #3562DB;
            margin-right: 16px;
          }
        }

      }
    }
  }

  .itemInfo {
    display: flex;
    justify-content: flex-start;
  }

  .itemInfoName {
    width: 20%;
    display: flex;
    align-items: flex-start;
    white-space: nowrap;
    /* 不换行 */
    overflow: hidden;
    /* 超出部分隐藏 */
    text-overflow: ellipsis;
  }

  .itemInfoIndex {
    width: 60%;
    white-space: nowrap;
    /* 不换行 */
    overflow: hidden;
    /* 超出部分隐藏 */
    text-overflow: ellipsis;
  }

  .itemIndex {
    width: 730px;
    white-space: nowrap;
    /* 不换行 */
    overflow: hidden;
    /* 超出部分隐藏 */
    text-overflow: ellipsis;
  }

  .tabsList {
    margin-bottom: 10px;
    padding-left: 15px;
    width: 100%;
    height: 50px;
  }

  ::v-deep .el-radio {
    display: block;
    margin: 10px 0;
  }

  ::v-deep .el-checkbox {
    display: block;
    margin: 10px 0;
  }

  ::v-deep .el-checkbox-group {
    margin-left: 38px;
  }

  .courseContent {
    height: 100%;
    margin-top: 16px;
    background-color: #fff;
    padding: 16px;

    .table_content {
      height: calc(100% - 70px);
      overflow: auto;

      .exercisesItem {
        height: 90px;
        overflow: hidden;
        background-color: #fff;
        margin-bottom: 16px;
        padding: 16px;
        border-bottom: 4px solid #faf9fc;
        font-size: 14px;

        .exercisesTop {
          display: flex;
          justify-content: space-between;
          margin-bottom: 10px;

          .left {
            display: flex;
            align-items: center;
            justify-content: center;

            span {
              color: #7f848c;
            }
          }

          .right {
            color: #ccced3;
            display: flex;
            align-items: center;

            .line {
              width: 2px;
              height: 14px;
              margin: 0 10px 0 26px;
              background-color: #dcdfe6;
            }

            span {
              color: #3562db;
              margin-left: 16px;
              cursor: pointer;
            }

            i {
              color: #3562db;
              cursor: pointer;
              margin-left: 16px;
            }
          }

          .exercisesType {
            width: 58px;
            height: 22px;
            line-height: 22px;
            text-align: center;
            border-radius: 4px;
            color: #86909c;
            background-color: #ededf5;
            margin: 0 10px;
          }
        }

        .exercisesName {
          line-height: 20px;
          margin-bottom: 16px;
        }

        .title {
          overflow: hidden;
          -webkit-line-clamp: 2;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
        }

        .el-radio {
          margin-left: 38px;
          font-size: 14px;
          color: #7f848c !important;
          line-height: 30px;
        }

        p {
          font-size: 14px;
          color: #7f848c !important;
          line-height: 20px;
          margin-bottom: 16px;
        }
      }

      .expand {
        height: auto;
      }
    }
  }

</style>
