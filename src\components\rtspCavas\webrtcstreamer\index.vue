<!--
 * @Author: hedd
 * @Date: 2023-11-07 20:47:20
 * @LastEditTime: 2023-11-15 10:40:12
 * @FilePath: \ihcrs_pc\src\components\rtspCavas\webrtcstreamer\index.vue
 * @Description:
-->
<template>
  <video id="video" ref="video" muted autoplay width="100%" height="100%" controls></video>
</template>

<script>
import './webrtcstreamer.js'
import './adapter.min.js'
export default {
  name: 'rtspCavasStr',
  props: {
    rtspUrl: {
      type: String,
      default: ''
    },
    videoName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      webRtcServer: null // webRtcServer上下文
      // webRtcServer: null // webRtcServer上下文
    }
  },
  watch: {
    rtspUrl(val) {
      if (val) {
        console.log('RTSP = ', __PATH.VUE_APP_RTSP_LIVE_WS_SERVER, val)
        this.$nextTick(() => {
          this.webRtcServer.connect(val)
        })
      }
    }
  },
  mounted() {
    this.webRtcServer = new WebRtcStreamer('video', __PATH.VUE_APP_RTSP_LIVE_WS_SERVER)
    this.$once('hook:beforeDestroy', () => {
      this.webRtcServer.disconnect()
    })
  },
  methods: {
  }
}

</script>

<style lang="scss" scoped>

</style>
