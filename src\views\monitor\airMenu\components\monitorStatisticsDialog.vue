<!-- 监测项目统计弹窗 -->
<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    :title="getTitle"
    width="30%"
    :visible.sync="visible"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <div class="content">
      <div class="content-heade">
        <el-date-picker
          v-model="searchFrom.dataRange"
          type="daterange"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          :picker-options="pickerOptions"
          :clearable="false"
        />
        <div style="display: inline-block;">
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="searchForm">查询</el-button>
        </div>
      </div>
      <div v-loading="loading" class="content-main">
        <ContentCard class="content-right" :showTitle="false" style="height:100%">
          <div slot="content" class="card-content">
            <el-select v-model="searchFrom.menuCode" placeholder="请选择设备" clearable @change="getDataList">
              <el-option label="全部" value=""> </el-option>
              <el-option v-for="item in entityList" :key="item.code" :label="item.name" :value="item.code"></el-option>
            </el-select>
            <div class="card-content-table table-content">
              <el-table ref="table" :resizable="false" border :data="tableData" height="100%" style="width: 100%;">
                <el-table-column v-for="item in getColumn" :key="item.prop" :prop="item.prop" :label="item.label" :width="item.width" show-overflow-tooltip>
                  <!-- <template slot-scope="scope">{{ scope.row.alarmStartTime }}</template> -->
                </el-table-column>
              </el-table>
            </div>
            <div class="card-content-footer">
              <el-pagination
                :current-page="pagination.current"
                :page-sizes="pagination.pageSizeOptions"
                :page-size="pagination.size"
                layout="total, prev, pager, next"
                :total="pagination.total"
                @size-change="paginationSizeChange"
                @current-change="paginationCurrentChange"
              >
              </el-pagination>
            </div>
          </div>
        </ContentCard>
      </div>
    </div>
    <span slot="footer">
      <el-button plain @click="closeDialog">关 闭</el-button>
    </span>
  </el-dialog>
</template>
<script>
import chartMixin from '../mixins/chartMixin'
import tableListMixin from '@/mixins/tableListMixin.js'
import moment from 'moment'
moment.locale('zh-cn')
export default {
  name: 'monitorStatisticsDialog',
  mixins: [chartMixin, tableListMixin],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    type: {
      type: Number,
      default: 1 // 1运行时长统计 2故障统计 3离线统计
    },
    projectCode: {
      type: String,
      default: ''
    },
    entityList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      searchFrom: {
        menuCode: '', // 设备
        // dataRange: [moment(new Date().setTime(new Date().getTime() - 3600 * 1000 * 24 * 30)).format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')] // 时间范围
        dataRange: [moment().startOf('month').format('YYYY-MM-DD'), moment().endOf('month').format('YYYY-MM-DD')] // 时间范围
      },
      pickerOptions: {
        shortcuts: [
          {
            text: '今日',
            onClick(picker) {
              picker.$emit('pick', [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')])
            }
          },
          {
            text: '本月',
            onClick(picker) {
              picker.$emit('pick', [moment().startOf('month').format('YYYY-MM-DD'), moment().endOf('month').format('YYYY-MM-DD')])
            }
          },
          {
            text: '本年',
            onClick(picker) {
              picker.$emit('pick', [moment().startOf('year').format('YYYY-MM-DD'), moment().endOf('year').format('YYYY-MM-DD')])
            }
          }
        ]
      }
    }
  },
  computed: {
    getTitle() {
      return this.type == 4 ? '故障统计' : this.type == 5 ? '离线统计' : '运行时长统计'
    },
    getColumn() {
      if (this.type == 1) {
        return [
          {
            label: '名称',
            prop: 'menuName',
            width: ''
          },
          {
            label: '运行时长统计（h）',
            prop: 'runTime',
            width: 150
          },
          {
            label: '排行',
            prop: 'sort',
            width: ''
          }
        ]
      } else if (this.type == 4) {
        return [
          {
            label: '名称',
            prop: 'menuName',
            width: ''
          },
          {
            label: '故障次数统计',
            prop: 'breakCount',
            width: 150
          },
          {
            label: '排行',
            prop: 'sort',
            width: ''
          }
        ]
      } else {
        return [
          {
            label: '名称',
            prop: 'menuName',
            width: ''
          },
          {
            label: '离线次数统计',
            prop: 'breakCount',
            width: 150
          },
          {
            label: '排行',
            prop: 'sort',
            width: ''
          }
        ]
      }
    }
  },
  created() {
    this.$nextTick(() => {
      this.getDataList()
    })
  },
  mounted() {},
  methods: {
    // 查看详情
    getDataList() {
      let params = {
        type: this.type,
        menuCode: this.searchFrom.menuCode,
        startTime: this.searchFrom.dataRange[0],
        endTime: this.searchFrom.dataRange[1],
        projectCode: this.projectCode,
        page: this.pagination.current,
        pageSize: this.pagination.size
      }
      let newArr = []
      this.loading = true
      this.$api.GetNewAirViewDetail(params).then((res) => {
        this.loading = false
        if (res.code == 200) {
          if (this.type == 1) {
            (res.data?.airRunTimeDetail ?? []).forEach((item) => {
              newArr.push({
                name: item.menuName,
                value: item.runTime
              })
            })
            this.tableData = res.data?.airRunTimeDetailList?.list ?? []
            this.pagination.total = res.data?.airRunTimeDetailList?.count ?? 0
          } else if (this.type == 4) {
            (res.data?.airConditionBreakList.list ?? []).forEach((item) => {
              newArr.push({
                name: item.menuName,
                value: item.breakCount
              })
            })
            this.tableData = res.data?.airConditionBreakList?.list ?? []
            this.pagination.total = res.data?.airConditionBreakList?.count ?? 0
          } else {
            (res.data?.airConditionOffList.list ?? []).forEach((item) => {
              newArr.push({
                name: item.menuName,
                value: item.breakCount
              })
            })
            this.tableData = res.data?.airConditionOffList?.list ?? []
            this.pagination.total = res.data?.airConditionOffList?.count ?? 0
          }
          this.$refs.deviceType.init(this.setPieChart(this.type == 1 ? 'h' : '次', newArr))
        } else {
          this.$refs.deviceType.init(this.setPieChart())
        }
      }).catch(() => {
        this.loading = false
        this.$refs.deviceType.init(this.setPieChart())
      })
    },
    // 重置查询
    resetForm() {
      Object.assign(this.$data.searchFrom, this.$options.data().searchFrom)
      this.searchForm()
    },
    // 查询
    searchForm() {
      this.getDataList()
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', !this.visible)
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 0;
  height: calc(78vh - 110px);
}

.content {
  width: 100%;
  height: 100%;

  .content-heade {
    padding: 10px;
    background: #fff;

    & > div {
      margin-right: 10px;
    }
  }

  .content-main {
    width: 100%;
    height: calc(100% - 52px);
    padding: 15px 10px;
    display: flex;
    justify-content: space-between;
  }

  .content-right {
    height: 100%;
    width: 100%;
    margin-left: 8px;

    .card-content {
      height: 100%;
      padding: 0 0 0 4px;
      display: flex;
      flex-direction: column;

      .card-content-table {
        flex: 1;
        overflow: auto;
        margin-top: 16px;

        ::v-deep .el-progress {
          .el-progress-bar__outer {
            border-radius: 0;
            height: 8px !important;
          }

          .el-progress-bar__inner {
            border-radius: 0;
          }
        }
      }

      .card-content-footer {
        padding: 10px 0 0;
      }
    }
  }
}

.model-dialog {
  padding: 0 !important;
}
</style>
