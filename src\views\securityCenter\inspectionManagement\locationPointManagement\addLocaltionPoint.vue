<template>
  <div style="height: 100%;">
    <div class="content_box">
      <el-form ref="formInline" :model="formInline" :inline="true" class="form-inline" label-width="100px" :rules="rules">
        <el-form-item label="定位点名称" prop="locationPointName">
          <el-input
            v-model.trim="formInline.locationPointName"
            :readonly="readonly"
            placeholder="请输入定位点名称"
            maxlength="50"
            :class="[query.type == 'details' ? 'detailClass' : 'width_lengthen']"
          ></el-input>
        </el-form-item>
        <el-form-item label="定位点编码" prop="locationPointCode">
          <el-input
            v-model.trim="formInline.locationPointCode"
            :readonly="readonly"
            placeholder="请输入定位点编码"
            maxlength="50"
            :class="[query.type == 'details' ? 'detailClass' : 'width_lengthen']"
          ></el-input>
        </el-form-item>
        <el-form-item label="设备UUID" prop="deviceUuid">
          <el-input
            v-model.trim="formInline.deviceUuid"
            :disabled="formInline.id !== '' ? true : false"
            placeholder="请输出设备UUID"
            maxlength="50"
            :class="[query.type == 'details' ? 'detailClass' : 'width_lengthen']"
          ></el-input>
        </el-form-item>
        <el-form-item label="设备minor" prop="deviceMinor">
          <el-input
            v-model.trim="formInline.deviceMinor"
            :readonly="readonly"
            placeholder="请输出设备minor"
            maxlength="50"
            :class="[query.type == 'details' ? 'detailClass' : 'width_lengthen']"
          ></el-input>
        </el-form-item>
        <el-form-item label="设备major" prop="deviceMajor">
          <el-input
            v-model.trim="formInline.deviceMajor"
            :readonly="readonly"
            placeholder="请输出设备major"
            maxlength="50"
            :class="[query.type == 'details' ? 'detailClass' : 'width_lengthen']"
          ></el-input>
        </el-form-item>
        <div style="width: 100%; display: flex;">
          <el-form-item label="备注" class="itemLabel">
            <el-input
              v-if="query.type !== 'details'"
              v-model.trim="formInline.remarks"
              style="width: 400px;"
              type="textarea"
              :class="[query.type == 'details' ? 'detailClass' : 'project-textarea']"
              placeholder="请输入备注，限制200字以内"
              show-word-limit
              :autosize="{ minRows: 4, maxRows: 6 }"
              maxlength="200"
            ></el-input>
            <div v-else style="width: 400px; height: 96px; color: #606266;">{{ formInline.remarks || '暂无' }}</div>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div class="bottomBar">
      <div class="bottomWrap">
        <el-button v-if="query.type != 'details'" type="primary" @click="complete">确定</el-button>
        <el-button type="primary" plain @click="$router.go('-1')">关闭</el-button>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'addLocationPoint',
  beforeRouteEnter(to, from, next) {
    if (to.meta.title) {
      to.meta.title = to.query.type == 'add' ? '新增定位点' : to.query.type == 'edit' ? '编辑定位点' : '定位点详情'
    }
    next()
  },
  data() {
    return {
      readonly: false,
      formInline: {
        locationPointName: '',
        locationPointCode: '',
        deviceUuid: '',
        deviceMinor: '',
        deviceMajor: '',
        remarks: '',
        id: '' // 模板id，修改必传
      },
      parentDictList: [],
      unitOptions: [],
      termTypeOptions: [],
      query: {},
      id: '',
      blockLoading: false,
      rules: {
        locationPointName: [{ required: true, message: '请输入定位点名称', trigger: 'change' }],
        locationPointCode: [{ required: true, message: '请输入定位点编码', trigger: 'change' }],
        deviceUuid: [
          { required: true, message: '请输入设备UUID', trigger: 'change' },
          // {
          //   validator: (rule, value, callback) => {
          //     if (this.query.type == 'add') {
          //       if (this.$route.query.uuids.find((i) => i == value)) {
          //         callback('uuid已存在')
          //       } else {
          //         callback()
          //       }
          //     }
          //     callback()
          //   },
          //   trigger: 'change'
          // }
        ],
        deviceMinor: [{ required: true, message: '请输入设备minor', trigger: 'change' }],
        deviceMajor: [{ required: true, message: '请输入设备major', trigger: 'change' }]
      },
      currentPage: 1,
      total: 0,
      fileList: [],
      parentName: '',
      updateId: '',
      title: '新增定位点'
    }
  },
  mounted() {
    this.query = this.$route.query
    console.log(this.query)
    if (this.query.id) {
      this.detailId = this.query.id
      this.formInline.id = this.query.id
      this.title = '编辑定位点'
      this._getLocationPointDetails() // 获取定位点管理详情
    } else if (this.query.type == 'details') {
      this.formInline = JSON.parse(this.query.data)
      console.log(this.formInline)
      this.title = '查看定位点详情'
      this.readonly = true
    }
  },
  methods: {
    _getLocationPointDetails() {
      let data = {
        id: this.detailId
      }
      this.$api.ipsmGetLocationPointDetails(data).then((res) => {
        const { code, data, message } = res
        if (code == 200) {
          this.formInline = data
        }
      })
    },
    // 点击确定
    complete() {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          const { formInline } = this
          let data = {
            locationPointName: formInline.locationPointName,
            locationPointCode: formInline.locationPointCode,
            deviceUuid: formInline.deviceUuid,
            deviceMinor: formInline.deviceMinor,
            deviceMajor: formInline.deviceMajor,
            description: formInline.description,
            remarks: formInline.remarks,
            id: this.formInline.id
          }
          this.blockLoading = true
          this.$store.commit('changeNetworkError', this.$route.query.type == 'edit' ? '修改失败，请检查您的网络…' : '新增失败，请检查您的网络…')
          this.$api.ipsmAddLocationPoint(data).then((res) => {
            this.blockLoading = false
            if (res.code == 200) {
              this.$message.success(res.message)
              this.$store.commit('keepAliveChange', false)
              this.$router.go(-1)
            }
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.sion-paenl-footer) {
  bottom: -4px !important;
}

.sino-content {
  height: calc(100% + 60px);
}

.content_box {
  height: calc(100% - 82px);
  margin: 15px;
  padding: 20px 25px 25px;
  background: #fff;
  display: flex;
  flex-direction: column;
}

.bottomBar {
  height: 52px;
  width: 100%;
  background-color: #fff;
  display: flex;
  justify-content: left;
  align-items: center;

  .bottomWrap {
    padding: 0 16px;
  }
}

:deep(.el-input.is-disabled .el-input__inner) {
  background: #f5f7fa !important;
}

.detailClass :deep(.el-input__inner) {
  border: none !important;
}

.detailClass :deep(.el-textarea__inner) {
  border: none;
  resize: none;
}

.project-textarea textarea {
  height: 120px;
}

.sion-icon {
  cursor: pointer;
  padding-left: 3px;
  color: #5188fc;
}

.icon-disabled {
  cursor: not-allowed;
}

.form-inline {
  .el-input,
  .el-select,
  .el-cascader {
    width: 300px;
  }
}

.inspection-content {
  width: 1200px;
  padding-left: 42px;
  font-size: 14px;
  font-weight: 400;
  color: rgb(96 98 102 / 100%);

  .content-block {
    margin-bottom: 20px;

    .porject-name {
      .porject {
        display: inline-block;
      }

      .porject-index {
        width: 50px;

        .index-icon {
          position: relative;
          right: 8px;
          top: 1px;
        }
      }

      .porject-input {
        width: 795px;

        .el-input {
          display: inline-block;
          width: 90%;
        }
      }

      .porject-button {
        width: 200px;
        padding-left: 30px;
      }
    }

    .termContent {
      padding-left: 52px;
      margin-top: 20px;

      .termContent-input {
        display: inline-block;
        width: auto;
        margin-right: 23px;

        .el-input {
          display: inline-block;
          width: 400px;
        }

        .termContent-button {
          height: 42px;
          line-height: 42px;
          color: #5188fc;
          margin-right: 20px;
          cursor: pointer;
        }

        .button-detele {
          color: rgb(252 44 97 / 100%);
        }
      }

      .termContent-tools {
        padding-left: 75px;
        margin-top: 20px;

        .termContent-number {
          display: inline-block;
          margin-right: 20px;

          .el-input,
          .el-select {
            display: inline-block;
            width: 100px;
            margin-left: 10px;
          }
        }
      }

      .termContent-radio {
        .radio-text {
          .el-input {
            display: inline-block;
            width: 300px;
          }
        }
      }
    }
  }
}

.form-inline {
  width: 1200px;

  .itemLabel {
    display: flex;
  }
}

.form-inline .width_lengthen {
  width: 400px;
}

:deep(.el-upload-dragger) {
  width: 255px;
  height: 164px;
}

:deep(.sino-vertical-file>.el-upload__tip) {
  top: -43px;
  left: 57px;
  color: #ccc;
}

:deep(.el-input__inner) {
  height: 40px !important;
  line-height: 40px !important;
}
</style>
                