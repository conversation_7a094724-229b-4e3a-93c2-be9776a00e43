/*
 * @Author: hedd
 * @Date: 2023-02-27 14:35:21
 * @LastEditTime: 2025-05-09 15:48:55
 * @FilePath: \ihcrs_pc\src\main.js
 * @Description:
 */
import Vue from 'vue'
import App from './App.vue'
import router from './router/index'
import store from './store/index'
import i18n from './lang'
import Tool from '@/assets/common/utils'
import '@/util/jsmpeg.min.js'
import meta2d from 'meta2d-vue/meta2d-vue.es.js'
import 'meta2d-vue/style.css'
// 文件预览polyfills配置
import './assets/common/polyfills.js'
Vue.use(meta2d)
import { VueJsonp } from 'vue-jsonp'
Vue.use(VueJsonp)
import api from './api'
Vue.prototype.$api = api
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
dayjs.locale('zh-cn')
Vue.prototype.$dayjs = dayjs
import auth from './util/auth'
Vue.use(auth)
import tabbar from './util/tabbar'
Vue.use(tabbar)
import cookies from 'vue-cookies'
Vue.use(cookies)
// import VueMeta from 'vue-meta'
// Vue.use(VueMeta)
import elTableInfiniteScroll from 'el-table-infinite-scroll'
Vue.use(elTableInfiniteScroll)

import VueVideoPlayer from 'vue-video-player';

// 引入 video.js 样式
import 'video.js/dist/video-js.css';

// 引入 vue-video-player 所需要的样式
import 'vue-video-player/src/custom-theme.css';

Vue.use(VueVideoPlayer);
import './util/dialogDrag'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
Vue.prototype.$ELEMENT = ElementUI
Vue.use(ElementUI, {
  size: store.state.settings.elementSize,
  i18n: (key, value) => i18n.t(key, value)
})
Vue.prototype.$tools = Tool
import hotkeys from 'hotkeys-js'
Vue.prototype.$hotkeys = hotkeys
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'quill/dist/quill.bubble.css'
// import Contextmenu from 'vue-contextmenujs'
// Vue.use(Contextmenu)
// 全局组件自动注册
import './components/autoRegister'
import 'remixicon/fonts/remixicon.css'
import './assets/styles/reset.scss'
import 'virtual:svg-icons-register'
// 错误日志
import './util/error.log'
// 升级成主应用
import microApp from '@micro-zoe/micro-app'
microApp.start()
Vue.config.productionTip = false
fetch('/build-time.txt')
  .then((response) => {
    if (!response.ok) {
      throw new Error('Network response was not ok')
    }
    return response.text()
  })
  .then((buildTime) => {
    // 输出构建时间到控制台
    console.log(`Build Time: ${buildTime}`)
  })
  .catch((error) => {
    console.error('Error reading build time:', error)
  })
Vue.prototype.$eventBus = new Vue({
  router,
  store,
  i18n,
  render: (h) => h(App)
}).$mount('#app')

function handleBackButton (e) {
  // 在这里编写处理逻辑，如检查用户数据是否已保存、显示提示等
  console.log('Browser back button was pressed:', e.state)
}
// 注册全局popstate事件监听器
window.addEventListener('popstate', handleBackButton)
// 如果需要在Vue实例销毁时移除监听器，可以在Vue原型上扩展一个销毁钩子
Vue.prototype.$removeBackButtonListener = function () {
  window.removeEventListener('popstate', handleBackButton)
}
Vue.mixin({
  methods: {
    isEnv(env) {
      return process.env.VUE_APP_HOSPITAL_NODE_ENV === env
    },
    isSjyyy() {
      return this.isEnv('sjyyy')
    }
  }
})
