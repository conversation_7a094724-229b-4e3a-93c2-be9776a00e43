<template>
  <PageContainer>
    <div slot="header" class="control-btn-header">
      <div class="search-from">
        <!-- <el-input v-model="searchFrom.order" placeholder="订单号" clearable style="width: 200px"></el-input> -->
        <!-- <el-select v-model="searchFrom.querySection" placeholder="时间范围" clearable>
          <el-option v-for="item in timeTypeList" :key="item.id" :label="item.label" :value="item.id"> </el-option>
        </el-select> -->
        <el-date-picker
          v-model="dataRange"
          type="datetimerange"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
        >
        </el-date-picker>
        <!-- <el-input v-model="searchFrom.proofNum" placeholder="凭证号" clearable style="width: 200px"></el-input>
        <el-input v-model="searchFrom.userName" placeholder="用户名称" clearable style="width: 200px"></el-input> -->
        <div style="display: inline-block">
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="searchForm">查询</el-button>
        </div>
      </div>
      <el-dialog title="订单详情" :visible.sync="dialogVisible" width="900px" :before-close="handleClose" custom-class="model-dialog">
        <div class="dialogs">
          <div class="order-num">点餐单号： {{ currentData.orderNo }}</div>
          <div class="table-content-new">
            <table>
              <tr>
                <td colspan="1" style="max-width: 80px; width: 80px">订餐人</td>
                <td colspan="2" style="max-width: 160px; width: 160px">{{ currentData.personName }}</td>
                <td colspan="1" style="max-width: 80px; width: 80px">订餐人工号</td>
                <td colspan="5" style="max-width: 160px; width: 160px">{{ currentData.jobNumber }}</td>
              </tr>
              <tr>
                <td colspan="1" style="max-width: 80px; width: 80px">点餐科室</td>
                <td colspan="2" style="max-width: 160px; width: 160px">{{ currentData.deptName }}</td>
                <td colspan="1" style="max-width: 80px; width: 160px">地点</td>
                <td colspan="5" style="max-width: 400px; width: 400px">{{ currentData.address }}</td>
              </tr>
              <tr>
                <td colspan="1" style="max-width: 80px; width: 80px">订餐时间</td>
                <td colspan="2" style="max-width: 480px; width: 480px">{{ currentData.createTime }}</td>
                <td colspan="1" style="max-width: 80px; width: 80px">订餐电话</td>
                <td colspan="5" style="max-width: 80px; width: 80px">{{ currentData.personMobile }}</td>
              </tr>
              <tr>
                <td colspan="1" style="max-width: 80px; width: 80px">备注</td>
                <td colspan="8" style="max-width: 640px; width: 640px">{{ currentData.remark }}</td>
              </tr>
              <tr>
                <td colspan="2" style="max-width: 160px; width: 160px">菜品名称</td>
                <td colspan="2" style="max-width: 80px; width: 80px">单价</td>
                <td colspan="2" style="max-width: 80px; width: 80px">数量</td>
                <td colspan="3" style="max-width: 80px; width: 80px">总价</td>
              </tr>
              <tr v-for="(item, index) in currentData.mealList" :key="index">
                <td colspan="2">{{ item.mealName }}</td>
                <td colspan="2">{{ item.unitPrice }}</td>
                <td colspan="2">{{ item.mealNum }}</td>
                <td colspan="3">{{ item.totalPrice }}</td>
              </tr>
              <tr>
                <td colspan="6" style="text-align: right">总计金额：</td>
                <td colspan="3">&yen;{{ currentData.orderPrice }}</td>
              </tr>
            </table>
          </div>
        </div>
        <!-- <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="handleClose">取 消</el-button>
        </span> -->
      </el-dialog>
    </div>
    <div slot="content" class="table-content">
      <div class="data-box">
        <div class="total-num">
          <span>投诉总数：</span>
          <span>{{ complaintTotal }}</span>
        </div>
        <div class="total-amount">
          <span>已处置：</span>
          <span>{{ handle }}</span>
        </div>
        <div class="total-amount">
          <span>处置率：</span>
          <span>{{ handleRate }}%</span>
        </div>
      </div>
      <TablePage
        ref="table"
        v-loading="tableLoading"
        :showPage="true"
        :tableColumn="tableColumn"
        :data="tableData"
        height="calc(100% - 95px)"
        :pageData="pageData"
        :pageProps="pageProps"
        @pagination="paginationChange"
        @row-click="handleClick"
      />
    </div>
  </PageContainer>
</template>
<script lang="jsx">
import moment from 'moment'

import empty from '@/assets/images/parkingLot/parking-null.png'

export default {
  name: 'cateringComprehensive',
  components: {},
  data() {
    return {
      empty,
      dialogVisible: false,
      rowList: {},
      dataRange: [], // 时间范围
      searchFrom: {
        // querySection: '', // 时间
        // order: ''
        // proofNum: '', // 凭证号
        // userName: '' // 用户名称
      },
      timeTypeList: [
        {
          id: 1,
          label: '今天'
        },
        {
          id: 2,
          label: '昨天'
        },
        {
          id: 3,
          label: '上周'
        },
        {
          id: 4,
          label: '上个月'
        },
        {
          id: 5,
          label: '最近60天'
        }
      ],
      tableLoading: false,
      tableColumn: [
        // {
        //   type: 'selection',
        //   align: 'center',
        //   selectable: (row) => {
        //     return row.id !== 1
        //   }
        // },
        {
          prop: '',
          label: '序号',
          width: '60',
          formatter: (scope) => {
            // 当前分页重置
            // return scope.$index + 1
            // 跨分页序号
            return (this.pageData.current - 1) * this.pageData.size + scope.$index + 1
          }
        },
        {
          prop: 'orderNo',
          label: '订单号'
        },
        {
          prop: 'orderNum',
          label: '数量',
          width: '90'
        },
        {
          prop: 'orderPrice',
          label: '总价'
        },
        {
          prop: 'createTime',
          label: '订餐时间'
        },
        {
          prop: 'feedbackName',
          label: '反馈人姓名'
        },
        // {
        //   prop: '',
        //   label: '视频图片',
        //   width: '90',
        //   render: (h, row) => {
        //     return (
        //       <div class="operationBtn">
        //         <span class="operationBtn-span" style="color: #5482ee" onClick={() => this.handleDetailEvent(row.row)}>
        //           查看
        //         </span>
        //       </div>
        //     )
        //   }
        // },
        {
          prop: 'contactNumber',
          label: '联系方式'
        },
        {
          prop: 'classify',
          label: '分类',
          width: '90'
        },
        {
          prop: 'content',
          label: '投诉内容'
        },
        {
          prop: 'stateName',
          label: '状态'
        },
        {
          prop: 'handleDesc',
          label: '处理说明',
          width: '90'
        }
        // {
        //   prop: 'operationModel',
        //   label: '操作方式'
        // }
      ],
      tableData: [],
      complaintTotal: 0,
      handle: 0,
      handleRate: 0,
      pageData: {
        current: 1,
        size: 15,
        total: 0
      },
      pageProps: {
        page: 'current',
        pageSize: 'size',
        total: 'total'
      },
      currentData: ''
    }
  },
  mounted() {
    this.getRecordList()
  },
  methods: {
    date() {
      const now = new Date()
      return moment(now).format('yyyy-MM-DD HH:mm:ss')
    },
    // 获取人员列表
    getRecordList() {
      let param = {
        startTime: this.dataRange[0] || '',
        endTime: this.dataRange[1] || this.date(),
        pageSize: this.pageData.size,
        pageNum: this.pageData.current
      }
      this.tableLoading = true
      this.$api
        .getComplainList(param)
        .then((res) => {
          console.log(res, 'sssss')
          if (res.code == 200) {
            // res.data.forEach((i) => {
            //   i.whether = '否'
            // })
            this.tableData = res.data.complainVoList
            this.complaintTotal = res.data.complaintTotal
            this.handle = res.data.handle
            this.handleRate = res.data.handleRate

            this.pageData.total = res.total
          }
          this.tableLoading = false
        })
        .catch(() => {
          this.tableLoading = false
        })
    },
    // 查询
    searchForm() {
      this.pageData.current = 1
      this.getRecordList()
    },
    // 重置查询
    resetForm() {
      // Object.assign(this.$data.searchFrom, this.$options.data().searchFrom)
      this.dataRange = []
      this.searchForm()
    },
    // 查看详情
    handleDetailEvent(row) {
      console.log(row, 'sssssss')
      this.rowList = row
      this.dialogVisible = true
    },
    handleClose() {
      this.dialogVisible = false
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getRecordList()
    },
    // 判断当前页是否是最后一页
    isLastPage(deleteNum) {
      let deleteAfterPage = Math.ceil((this.pageData.total - deleteNum) / this.pageData.size)
      let currentPage = this.pageData.current > deleteAfterPage ? deleteAfterPage : this.pageData.current
      this.pageData.current = currentPage < 1 ? 1 : currentPage
    },
    handleClick(row) {
      this.dialogVisible = true
       this.currentData = row
      // this.$api.getComplainDetails({}).then((res) => {
      //   if (res.code == 200) {
      //     this.currentData = res.data
      //   }
      // })
    }
  }
}
</script>
<style lang="scss" scoped>
.control-btn-header {
  padding: 10px !important;
  .search-from {
    display: flex;
    & > div {
      margin-right: 10px;
    }
  }
}

.table-content {
  margin-top: 15px;
  border-radius: 4px;
  height: calc(100% - 15px);
  padding: 10px;
  background-color: #fff;
}
.dialogs {
  width: 100%;
  padding: 10px;
  height: 725px;
}
.entrance {
  display: flex;
  padding: 15px;
  background-color: #e6effc;
  > div:nth-child(1) {
    width: 30%;
    > p {
      width: 40px;
      text-align: center;
      margin: 54px auto;
    }
  }
  > div:nth-child(2) {
    width: 70%;
  }
}
.entranceTxt {
  display: flex;
  > p:nth-child(1) {
    width: 100px;
    font-size: 14px;
    color: #666666;
  }
  > p:nth-child(2) {
    text-align: left;
    font-size: 14px;
    color: #333333;
  }
}
.admissionPictures {
  width: 100%;
  height: 200px;
  margin-bottom: 15px;
  img {
    width: 100%;
    height: 200px;
  }
}
::v-deep .el-dialog {
  // height: 800px;
  background-color: #fff;
}
::v-deep .model-dialog .el-dialog__body {
  overflow: hidden;
  // max-height: 800px;
}
::v-deep .el-date-editor .el-icon-time {
  transform: translateY(-3px);
}
.data-box {
  display: flex;
  margin: 8px 16px;
  margin-left: 0;
}
.data-box > div {
  background-color: rgba(53, 98, 219, 0.06);
  padding: 12px 24px;
  border-radius: 4px;
}
.data-box > div > span:nth-child(1) {
  font-size: 16px;
}
.data-box > div > span:nth-child(2) {
  font-size: 16px;
  font-weight: bold;
  color: #3a62d8;
}
.data-box .total-num {
  margin-right: 24px;
}
.data-box .total-amount {
  margin-right: 24px;
}
.table-title {
  text-align: center;
}
.table-content-new {
  padding-top: 8px;
}
.table-content-new table {
  width: 100%;
}
.title-1 {
  font-size: 30px;
}
.title-2 {
  font-size: 24px;
  letter-spacing: 3px;
}
.table-up-box {
  display: flex;
  justify-content: center;
}
.table-up-box > div {
  margin: 0 5px;
}
.remark {
  width: 70%;
  margin: 0 auto;
}
.repairNum {
  padding-left: 15vw;
}
table,
td {
  border: 1px solid #000;
  border-collapse: collapse;
}
td {
  padding: 6px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
}
tr {
  height: 36px;
}
table {
  margin: 0 auto;
  margin-top: 5px;
  table-layout: fixed;
}
</style>
<style lang="scss">
.operationBtn-span {
  margin-right: 10px;
}
</style>
