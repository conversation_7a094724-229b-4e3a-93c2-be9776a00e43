<template>
  <div id="operOverview">
    <!-- <div @click="changeReplaceRouter">123456</div> -->
    <div v-if="saveAndCancelBtnShow" class="batch-control">
      <el-button type="primary" plain @click="cancelStaging">取消</el-button>
      <el-button type="primary" @click="saveStaging">保存</el-button>
    </div>
    <dashboard v-if="dashExampleShow" id="dashExample">
      <dash-layout v-bind="dlayout" :debug="false">
        <dash-item v-for="(item, index) in dlayout.items" v-bind.sync="dlayout.items[index]" :key="item.id" @moveEnd="moveEnd" @resizeEnd="resizeEnd">
          <div class="card-item">
            <div class="card-empty">
              <header class="header drag_class airconditioning">
                <div v-if="saveAndCancelBtnShow" class="legend-title move-pointer">{{ item.name }}</div>
                <div v-else class="legend-title" @click="jumpMointorMenu(item)">{{ item.name }}</div>
                <div v-if="item.hasCount" class="legend-right-box notlinkBg">
                  <span class="notlink"></span><i>{{ item.offLineCount }}</i>
                </div>
                <div v-if="item.hasFault" class="legend-right-box faultBg">
                  <span class="fault"></span><i>{{ item.faultCount }}</i>
                </div>
                <div v-if="item.hasCount" class="legend-right-box warnBg">
                  <span class="warn"></span><i>{{ item.policeCount }}</i>
                </div>
                <span v-if="saveAndCancelBtnShow" class="more-operations move-pointer">· · ·</span>
                <el-dropdown v-else class="dropdown-btn" trigger="click" @command="dropDownCommand">
                  <span class="more-operations" @click.stop.prevent>· · ·</span>
                  <el-dropdown-menu slot="dropdown" class="dropdownSelect">
                    <el-dropdown-item>编辑</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </header>
              <!-- 医用气体监测 -->
              <div v-if="item.id == getProjectDataByName('医用气体').projectCode" class="card-content">
                <div v-for="(list, idx) in yyqtData ?? []" :key="idx" class="air_conditioner">
                  <p>
                    <span>{{ list.workCount }}</span> / {{ list.totalCount }}
                  </p>
                  <p>{{ list.menuName }}</p>
                </div>
              </div>
              <!-- 空调 -->
              <div v-if="item.id == getProjectDataByName('空调监测').projectCode" class="card-content">
                <div v-for="(list, idx) in airData ?? []" :key="idx" class="air_conditioner">
                  <p>
                    <span>{{ list.workCount }}</span> / {{ list.totalCount }}
                  </p>
                  <p>{{ list.menuName }}</p>
                </div>
              </div>
              <!-- 环境 -->
              <div v-else-if="item.id == getProjectDataByName('环境监测').projectCode" class="card-content">
                <div v-for="(list, idx) in envirData" :key="idx" class="environment">
                  <StackedProgress :ref="'StackedProgress' + idx" :colDataList="list" />
                </div>
              </div>
              <!-- 给排水 -->
              <div v-else-if="item.id == getProjectDataByName('给排水监测').projectCode" class="card-content">
                <div v-for="(list, idx) in sewerData ?? []" :key="idx" class="air_conditioner">
                  <p v-if="list.workCount == 0 || list.workCount">
                    <span>{{ list.workCount }}</span> / {{ list.totalCount }}
                  </p>
                  <p v-else>
                    <span>{{ list.totalCount }}</span>
                  </p>
                  <p>{{ list.entityTypeName }}</p>
                </div>
              </div>
              <!-- 冷热源 -->
              <div v-else-if="item.id == getProjectDataByName('冷热源监测').projectCode" class="card-content">
                <div v-for="(list, idx) in codeHotData ?? []" :key="idx" class="air_conditioner">
                  <p v-if="list.workCount == 0 || list.workCount">
                    <span>{{ list.workCount }}</span> / {{ list.totalCount }}
                  </p>
                  <p v-else>
                    <span>{{ list.totalCount }}</span>
                  </p>
                  <p>{{ list.menuName }}</p>
                </div>
              </div>
              <!-- getColdHostStatistics -->
              <!-- 安防、火灾、音视频 -->
              <template v-else-if="item.id == 'IEMC-FireAlarmSystem' || item.id == 'IEMC-SecuritySystem' || item.id == 'IEMC-AudioAndVideoSystem'">
                <div class="card-content">
                  <div v-for="(list, idx) in safeData[item.id] ?? []" :key="idx" class="air_conditioner safe_conditioner" @click="jumpHandle('/allAlarm/allAlarmIndex', item, idx)">
                    <p>
                      <span>{{ list.workCount }}</span>
                    </p>
                    <p>{{ list.menuName }}</p>
                  </div>
                </div>
              </template>
            </div>
          </div>
        </dash-item>
      </dash-layout>
    </dashboard>
  </div>
</template>
<script>
import { Dashboard, DashLayout, DashItem } from 'vue-responsive-dash'
import StackedProgress from './envirComponents/StackedProgress.vue'
import { monitorTypeList } from '@/util/dict.js'
export default {
  name: 'operOverview',
  components: {
    Dashboard,
    DashLayout,
    DashItem,
    StackedProgress
  },
  data() {
    return {
      hospitalEvn: __PATH.VUE_APP_HOSPITAL_NODE_ENV,
      dlayout: {
        breakpoint: 'xs',
        numberOfCols: 24,
        items: []
      },
      yyqtData: [],
      airData: [],
      envirData: [],
      sewerData: [],
      codeHotData: [],
      safeData: {
        'IEMC-FireAlarmSystem': [
          {
            workCount: 0,
            menuName: '设备总数'
          },
          {
            workCount: 0,
            menuName: '正常设备'
          },
          {
            workCount: 0,
            menuName: '离线设备'
          },
          {
            workCount: 0,
            menuName: '今日报警数'
          },
          {
            workCount: 0,
            menuName: '未处理报警'
          }
        ],
        'IEMC-SecuritySystem': [
          {
            workCount: 0,
            menuName: '设备总数'
          },
          {
            workCount: 0,
            menuName: '正常设备'
          },
          {
            workCount: 0,
            menuName: '离线设备'
          },
          {
            workCount: 0,
            menuName: '今日报警数'
          },
          {
            workCount: 0,
            menuName: '未处理报警'
          }
        ],
        'IEMC-AudioAndVideoSystem': [
          {
            workCount: 0,
            menuName: '设备总数'
          },
          {
            workCount: 0,
            menuName: '正常设备'
          },
          {
            workCount: 0,
            menuName: '离线设备'
          },
          {
            workCount: 0,
            menuName: '今日报警数'
          },
          {
            workCount: 0,
            menuName: '未处理报警'
          }
        ]
      },
      defaultItems: [],
      dashExampleShow: false,
      saveAndCancelBtnShow: false // 保存按钮组显示
    }
  },
  mounted() {
    let items = []
    if (this.hospitalEvn === 'bjsjtyy') {
      items = [
        { id: 'fb78bc9c7e5311ec8e4f000c2912d8ca', name: '暖通空调系统', x: 0, y: 0, width: 10, height: 3 },
        { id: '0918f947fec94b6ea6c8041dc63b3134', name: '环境监测', x: 10, y: 3, width: 14, height: 5 }
      ]
    } else {
      items = [
        { id: '73e7aab447b34971b9ae6d8dae034aa3', name: '医用气体监测', x: 0, y: 0, width: 10, height: 3 },
        { id: 'fb78bc9c7e5311ec8e4f000c2912d8ca', name: '暖通空调系统', x: 0, y: 3, width: 10, height: 3 },
        { id: '0918f947fec94b6ea6c8041dc63b3134', name: '环境监测', x: 10, y: 3, width: 14, height: 5 },
        { id: 'b5587fbbe453422ebc937cc0b7c69wsx', name: '给排水监测', x: 10, y: 0, width: 11, height: 3 },
        { id: 'b5587fbbe453422ebc937cc0b7c69a3c', name: '冷热源监测', x: 10, y: 8, width: 14, height: 3 },
        { id: 'IEMC-FireAlarmSystem', name: '消防系统', x: 0, y: 6, width: 10, height: 3 },
        { id: 'IEMC-SecuritySystem', name: '安防系统', x: 0, y: 9, width: 10, height: 3 }
        // { id: 'IEMC-AudioAndVideoSystem', name: '多媒体', x: 0, y: 9, width: 10, height: 3 }
      ]
    }
    this.defaultItems = items
    this.getDragManageList()
  },
  methods: {
    init() {
      this.getWarnCount()
      this.getAirOverview()
      this.getYyqtOverview()
      this.getEnvirOverview()
      if (this.hospitalEvn !== 'bjsjtyy') {
        this.getSewerOverview()
        this.getColdHostStatistics()
        this.getStatisticsOverview()
      }
    },
    // 获取可拖拽列表
    getDragManageList() {
      const params = {
        userId: this.$store.state.user.userInfo.user.staffId,
        menuType: 9
      }
      this.$api.getWorktopManageList(params).then((res) => {
        if (res.code == 200) {
          const data = res.data
          let componentItems = []
          // 有接口数据取接口数据 无接口数据取字典默认配置数据
          if (data.length) {
            componentItems = data
          } else {
            componentItems = this.defaultItems
          }
          // 遍历增加dragAllowFrom、resizable、draggable字段
          const items = componentItems.map((item) => {
            return {
              id: item.id,
              name: item.name || this.defaultItems.find((e) => e.id == item.id).name,
              x: item.x,
              y: item.y,
              width: item.width,
              height: item.height,
              dragAllowFrom: '.drag_class',
              resizable: false,
              draggable: false
            }
          })
          this.dashExampleShow = false
          this.$nextTick(() => {
            this.dashExampleShow = true
            this.dlayout.items = items
            this.init()
          })
        }
      })
    },
    // dropdown
    dropDownCommand() {
      this.saveAndCancelBtnShow = true
      this.dlayout.items.map((e) => {
        e.resizable = true
        e.draggable = true
      })
      // console.log(val)
    },
    jumpMointorMenu(item) {
      console.log(this.getProjectDataByCode(item.id))
      const projectData = this.getProjectDataByCode(item.id)
      if (projectData.defaultJumpPath) {
        this.$router.push({
          path: projectData.defaultJumpPath
        })
      }
    },
    // 取消编辑工作台模块
    cancelStaging() {
      this.saveAndCancelBtnShow = false
      this.getDragManageList()
    },
    // 保存工作台模块
    saveStaging() {
      const items = this.dlayout.items
      if (!items.length) {
        return this.$message.warning('无可配置模块')
      }
      const jsonList = items.map((e) => {
        return {
          id: e.id,
          name: e.name,
          x: e.x,
          y: e.y,
          width: e.width,
          height: e.height
        }
      })
      const params = {
        jsonList: JSON.stringify(jsonList),
        userId: this.$store.state.user.userInfo.user.staffId,
        menuType: 9
      }
      console.log('save提交', params)
      this.$api.saveWorktopManage(params).then((res) => {
        if (res.code == 200) {
          this.$message.success('保存成功')
          this.saveAndCancelBtnShow = false
          this.getDragManageList()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    changeReplaceRouter() {
      this.$router.replace({
        path: this.$router.currentRoute.path,
        query: {
          name: Math.random()
        }
      })
    },
    // 获取空调统计数据
    getAirOverview() {
      const params = {
        projectCode: this.getProjectDataByName('空调监测').projectCode
      }
      this.$api.airCountOverview(params).then((res) => {
        if (res.code === '200') {
          this.dlayout.items.map((e) => {
            if (e.id == params.projectCode) {
              e.offLineCount = res.data.offLineCount ?? 0
              e.faultCount = res.data.faultCount ?? 0
              e.hasFault = true
            }
          })
          this.airData = res.data.airView ?? []
        }
      })
    },
    // 获取医用气体统计数据
    getYyqtOverview() {
      const params = {
        projectCode: this.getProjectDataByName('医用气体').projectCode
      }
      this.$api.airCountOverview(params).then((res) => {
        if (res.code === '200') {
          this.dlayout.items.map((e) => {
            if (e.id == params.projectCode) {
              console.log(e.id, params.projectCode, res.data.offLineCount, res.data.faultCount)
              e.offLineCount = res.data.offLineCount ?? 0
              e.faultCount = res.data.faultCount ?? 0
              e.hasFault = true
              e.hasCount = true
            }
          })
          this.yyqtData = res.data.airView ?? []
        }
      })
    },
    // 获取环境监测统计数据
    getEnvirOverview() {
      const params = {
        projectCode: monitorTypeList.find((item) => item.projectName == '环境监测').projectCode
      }
      this.$api.getEnvironmentOverview(params).then((res) => {
        if (res.code === '200') {
          this.dlayout.items.map((e) => {
            if (e.id == this.getProjectDataByName('环境监测').projectCode) {
              e.offLineCount = res.data.offLineCount ?? 0
            }
          })
          this.envirData =
            res.data.environmentConfigInfoList.map((e) => {
              return {
                endNum: e.maxNumber,
                startNum: e.minNumber,
                title: e.config,
                bgColor: e.colour,
                fontColor: e.colour ?? 'black',
                name: e.parameterName,
                val: this.convertToInteger(e.avgValue),
                unit: e.paramUnit
              }
            }) ?? []
        }
      })
    },
    // 获取给排水统计数据
    getSewerOverview() {
      const params = {
        projectCode: this.getProjectDataByName('给排水监测').projectCode,
        allData: 1 // 0全部数据  其他返回当天
      }
      this.$api.getWaterOverview(params).then((res) => {
        if (res.code === '200') {
          this.dlayout.items.map((e) => {
            if (e.id == params.projectCode) {
              e.offLineCount = res.data.offLineCount ?? 0
              e.faultCount = res.data.faultCount ?? 0
              e.hasFault = true
            }
          })
          this.sewerData = res.data.data ?? []
          this.sewerData.push(
            {
              entityTypeName: '高液位',
              totalCount: res.data.high ?? 0
            },
            {
              entityTypeName: '低液位',
              totalCount: res.data.low ?? 0
            }
          )
        }
      })
    },
    // 获取冷热源统计数据
    getColdHostStatistics() {
      const params = {
        projectCode: this.getProjectDataByName('冷热源监测').projectCode,
        allData: 1 // 0全部数据  其他返回当天
      }
      this.$api.getColdHostStatistics(params).then((res) => {
        if (res.code === '200') {
          this.dlayout.items.map((e) => {
            if (e.id == params.projectCode) {
              e.offLineCount = res.data.offLineCount ?? 0
              e.faultCount = res.data.faultCount ?? 0
              e.hasFault = true
            }
          })
          const dataList = []
          // res.data.dataList?.map((e) => {
          //   console.log(e.capacity, this.convertToInteger(e.capacity))
          //   return {
          //     menuName: e.paramName + '（' + e.unit + '）',
          //     totalCount: this.convertToInteger(e.capacity)
          //   }
          // }) ?? []
          console.log(dataList)
          const list = res.data.list.map((e) => {
            console.log(e.totalCount, e.workCount)
            return {
              menuName: e.entityTypeName,
              totalCount: e.totalCount,
              workCount: e.workCount
            }
          })
          this.codeHotData = list.concat(dataList)
        }
      })
    },
    // 获取安防消防多媒体统计数据
    getStatisticsOverview() {
      const names = ['安防系统监测', '消防系统监测', '多媒体系统监测']
      const params = this.getChildCodeByName(names)
      this.$api.getSecurityOverview(params).then((res) => {
        if (res.code === '200') {
          // res.data = [{"deviceCount":0,"normalCount":0,"offLineCount":0,"policeCount":0,"unDisposedCount":0,"projectCode":"IEMC-SecuritySystem","projectName":"安防系统监测"},{"deviceCount":0,"normalCount":0,"offLineCount":0,"policeCount":0,"unDisposedCount":0,"projectCode":"IEMC-FireAlarmSystem","projectName":"火灾报警系统"},{"deviceCount":38,"normalCount":0,"offLineCount":38,"policeCount":0,"unDisposedCount":0,"projectCode":"IEMC-AudioAndVideoSystem","projectName":"音视频系统监测"}]
          // 根据res.data的数据，生成this.safeData类型的数据
          res.data.map((e) => {
            this.safeData[e.projectCode] = [
              {
                workCount: e.deviceCount ?? 0,
                menuName: '设备总数'
              },
              {
                workCount: e.normalCount ?? 0,
                menuName: '正常设备'
              },
              {
                workCount: e.offLineCount ?? 0,
                menuName: '离线设备'
              },
              {
                workCount: e.policeCount ?? 0,
                menuName: '今日报警数'
              },
              {
                workCount: e.unDisposedCount ?? 0,
                menuName: '未处理报警'
              }
            ]
          })
        }
      })
    },
    getChildCodeByName(names) {
      const codeList = {}
      names.map((name) => {
        let parentCode = this.getProjectDataByName(name).projectCode
        codeList[parentCode] =
          monitorTypeList
            .filter((item) => item.parentCode == parentCode)
            ?.map((e) => e.projectCode)
            ?.join(',') ?? ''
      })
      return {
        codeList
      }
    },
    // 获取报警数量
    getWarnCount() {
      const projectNameList = ['医用气体', '空调监测', '环境监测', '给排水监测', '冷热源监测']
      const params = {
        projectCode: projectNameList.map((e) => monitorTypeList.find((item) => item.projectName == e).projectCode).join(',')
      }
      this.$api.getWarnCount(params).then((res) => {
        if (res.code === '200') {
          res.data.length &&
            res.data.map((item) => {
              this.dlayout.items.map((e) => {
                projectNameList.map((name) => {
                  if (
                    e.id == monitorTypeList.find((it) => it.projectName == name).projectCode &&
                    item.projectCode == monitorTypeList.find((it) => it.projectName == name).projectCode
                  ) {
                    e.policeCount = item.policeCount ?? 0
                    e.hasCount = true
                  }
                })
              })
            })
          this.$forceUpdate()
        }
      })
    },
    // 获取项目data通过项目name
    getProjectDataByName(name) {
      return monitorTypeList.find((item) => item.projectName == name)
    },
    // 获取项目data通过项目code
    getProjectDataByCode(code) {
      return monitorTypeList.find((item) => item.projectCode == code)
    },
    // 跳转页面
    jumpHandle(path, row, index) {
      if (path == '/allAlarm/allAlarmIndex') {
        const projectCode =
          monitorTypeList
            .filter((item) => item.parentCode == row.id)
            ?.map((e) => e.projectCode)
            ?.join(',') ?? ''
        let queryData = {
          activeWarnType: 'all',
          projectCode: projectCode,
          alarmStatus: 0
        }
        if (index == 3) {
          Object.assign(queryData, {
            dataRange: 'today'
          })
        } else if (index == 4) {
          Object.assign(queryData, {
            alarmStatus: 0
          })
        } else {
          return
        }
        this.$router.push({
          path: path,
          query: queryData
        })
      }
    },
    // 清除小数点后末尾为0的
    convertToInteger(num) {
      const numStr = num.toString()
      const decimalIndex = numStr.indexOf('.')
      if (decimalIndex === -1) {
        return num
      } else {
        const decimalPart = numStr.slice(decimalIndex + 1)
        if (decimalPart === '0') {
          return parseInt(numStr)
        } else {
          return num
        }
      }
    },
    moveEnd() {
      console.log('moveEnd', this.dlayout)
    },
    resizeEnd() {
      console.log('resizeEnd', this.dlayout)
    }
  }
}
</script>
<style lang="scss" scoped>
#operOverview {
  background: #fff;
  height: calc(100% - 30px);
  width: calc(100% - 30px);
  margin: 15px;
  display: flex;
  flex-direction: column;
  .batch-control {
    text-align: right;
    height: 44px;
    padding: 6px 10px;
  }
}
#dashExample {
  flex: 1;
  max-height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  ::v-deep(.placeholder) {
    background: #e2e6eb !important;
    border-radius: 10px;
    opacity: 1;
  }
}
.dropdownSelect {
  margin: 0;
  padding: 3px 0;
  .el-dropdown-menu__item {
    line-height: 30px;
    padding: 0 15px;
  }
}
.card-item {
  height: 100%;
  width: 100%;
  background: #fff;
  box-sizing: border-box;
  padding: 10px;
  box-shadow: 0 2px 7px 0 #e4e4ec !important;
  border-radius: 10px 4px 4px !important;
  .card-empty {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  header {
    align-items: center;
    height: 48px;
    // .dropdown-btn {
    //   position: absolute;
    //   right: 0;
    // }
    .more-operations {
      cursor: pointer;
      color: #3562db;
      user-select: none;
      font-size: 14px;
      font-weight: 500;
      height: auto;
    }
    .move-pointer {
      cursor: move !important;
    }
  }
  // .mover {
  //   cursor: move;
  // }
  .airconditioning {
    display: flex;
    padding: 0 15px;
    color: #606266;
    font-weight: 600;
    border-bottom: 1px solid #d8dee7;
    .legend-title {
      font-size: 16px;
      color: #606266;
      padding-left: 15px;
      position: relative;
      line-height: 48px;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      height: 100%;
      cursor: pointer;
      &::before {
        content: '';
        position: absolute;
        top: 16px;
        left: 2px;
        width: 4px;
        height: 16px;
        background: #5089fc;
      }
    }
    .notlinkBg {
      background-color: #f2f2f2;
      color: #606266;
    }
    .faultBg {
      background-color: #fff4ea;
      color: #f39038;
    }
    .warnBg {
      background-color: #ffebeb;
      color: #ff4848;
    }
    .legend-right-box {
      width: 49px;
      height: 22px;
      border-radius: 2px;
      margin: auto;
      display: flex;
      margin-right: 15px;
      span {
        display: inline-block;
        width: 22px;
        height: 22px;
        margin-right: 5px;
        line-height: 22px;
      }
      i {
        flex: 1;
        line-height: 22px;
      }
      .notlink {
        background: url('~@/assets/images/monitor/notlink.png') no-repeat;
        background-size: 100% 100%;
      }
      .fault {
        background: url('~@/assets/images/monitor/fault.png') no-repeat;
        background-size: 100% 100%;
      }
      .warn {
        background: url('~@/assets/images/monitor/warn.png') no-repeat;
        background-size: 100% 100%;
      }
    }
    & > span {
      // flex: 1;
      text-align: center;
    }
  }
  .card-content {
    display: flex;
    flex-wrap: wrap;
    padding: 10px;
    height: calc(100% - 48px);
    overflow: auto;
    .air_conditioner {
      width: 120px;
      height: 100px;
      text-align: center;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      font-size: 13px;
      padding: 6px 0;
      span {
        font-size: 22px;
        color: #5188fc;
      }
      p {
        padding: 0;
        margin: 0;
        align-items: center;
      }
      p:first-child {
        height: 55px;
        line-height: 55px;
      }
      p {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0;
      }
    }
    .safe_conditioner {
      &:nth-child(4),
      &:nth-child(5) {
        cursor: pointer;
        &:hover {
          box-shadow: 2px 3px 5px 3px #e4e4ec !important;
        }
      }
    }
  }
}
::-webkit-scrollbar-thumb:hover,
::-webkit-scrollbar-track:hover {
  background: transparent;
}
::-webkit-scrollbar-thumb {
  background-color: transparent;
}
</style>
