export default {
  data() {
    return {
      contractClassification: [],
      categoryList: [],
      classification: []
    }
  },
  created() {
    this.handleQueryDictConfigAll()
  },
  methods: {
    handleQueryDictConfigAll() {
      this.$api.fileManagement.queryDictConfigAll().then((res) => {
        this.contractClassification = []
        this.categoryList = []
        this.classification = []
        const obj = new Map([
          [1, 'contractClassification'],
          [2, 'categoryList'],
          [3, 'classification'],
        ])
        res.data.forEach((item) => {
          const key = obj.get(item.type)
          if (key) {
            const { code, name } = item
            this[key].push({ label: name, value: code })
          }
        })
      })
    }
  }
}
