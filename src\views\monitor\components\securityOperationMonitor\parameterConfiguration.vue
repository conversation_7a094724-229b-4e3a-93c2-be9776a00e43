<template>
  <el-dialog v-if="dialogShow" :title="title" width="40%" :visible.sync="dialogShow" custom-class="model-dialog">
    <div style="width: 100%; height: 100%">
      <el-tabs type="border-card">
        <el-tab-pane label="实时数据">
          <div class="realBox">
            <div v-for="item in monitorData.parameterList" class="reaslNav">
              <div>{{ item.parameterName }}</div>
              <!-- 映射 1=开，0=关，-1=离线 -->
              <!-- <div class="reaslNav_lable">{{ item.parameterValue }}</div> -->
              <div class="reaslNav_lable">{{ mapParameterValue(item.parameterValue) }}</div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane v-if="monitorData.controlList.length > 0" label="控制参数">
          <div class="realBox">
            <div v-for="item in monitorData.controlList" class="reaslNav">
              <div>{{ item.parameterName }}</div>
              <div class="reaslNav_lable"><el-switch v-model="item.parameterValue" active-value="0" inactive-value="1">
                </el-switch></div>
            </div>
          </div>
          <div class="controlBtn"><el-button style="width: 120px" type="primary" @click="application()">应用</el-button>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-dialog>
</template>
<script>
export default {
  name: 'normalDialog',
  props: {
    title: {
      type: String,
      default: ''
    },
    monitorData: {
      type: Object,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      dialogShow: false,
      equipmentData: {}
    }
  },
  mounted() { },
  methods: {
    openDialog() {
      this.dialogShow = true
    },
    application() {
      let data = {
        imsCode: this.monitorData.surveyEntityCode,
        paramList: this.monitorData.controlList.map((ele) => {
          return {
            paramId: ele.parameterId,
            value: ele.parameterValue
          }
        })
      }
      this.$api.setControl(data).then((res) => {
        this.dialogShow = false
        this.$emit('getDataList')
      })
    },
    mapParameterValue(value) {
      // value转一下字符串 (兼容)
      value = value.toString()
      switch (value) {
        case '1':
          return '开'
        case '0':
          return '关'
        case '-1':
          return '离线'
        default:
          return value // 如果没有匹配，返回原值
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.realBox {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  width: 100%;
  padding: 0px 15px;
  text-align: center;
}

.reaslNav {
  display: flex;
  flex-direction: column;
  width: 33%;
  margin: 20px 0px;
}

.reaslNav_lable {
  margin: 10px 0px;
  font-size: 15px;
  font-weight: bolder;
}

.controlBtn {
  float: right;
  margin: 5px;
}

::v-deep .model-dialog .el-dialog__body {
  background: #ffffff;
}

::v-deep .el-tabs--border-card>.el-tabs__header .el-tabs__item {
  width: 50% !important;
  text-align: center;
}

::v-deep .el-tabs__nav {
  width: 100%;
  text-align: left;
}

::v-deep .el-tabs--border-card>.el-tabs__header .el-tabs__item.is-active {
  text-align: center;
}
</style>
