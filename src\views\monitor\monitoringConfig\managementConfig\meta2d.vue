<!--
 * @Author: hedd
 * @Date: 2023-08-04 10:34:01
 * @LastEditTime: 2024-07-19 16:48:01
 * @FilePath: \ihcrs_pc\src\views\monitor\monitoringConfig\managementConfig\meta2d.vue
 * @Description:
-->
<template>
  <!-- <div v-loading="loading" class="scada-edit"> -->
  <!-- 使用topology组件 -->
  <meta2d-vue ref="meta2dVue" :userInfo="userInfo" :baseUrl="baseUrl" />
  <!-- </div> -->
</template>
<script>
export default {
  name: 'meta-2d',
  data() {
    return {
      requestHttp: __PATH.VUE_IEMC_API,
      loading: false,
      baseUrl: __PATH.VUE_IEMC_API,
      userInfo: {
        avatarUrl: '',
        captcha: '',
        createdAt: '',
        deletedAt: '',
        email: '',
        id: '1',
        phone: '',
        updatedAt: '',
        username: 'admin',
        vip: 1,
        vipExpiry: '',
        isVip: true // 该属性是计算出来的，不是数据库中的
      }
    }
  },
  created() {
    // 网关需要token 组件获取token通过localStorage
    window.localStorage.setItem('token', 'Bearer ' + this.$store.state.user.token)
  },
  mounted() {
    console.log(this.$refs.meta2dVue)
    console.log(this.$router.currentRoute.path)
  },
  methods: {}
}
</script>
<style lang="scss" scoped>
.scada-edit {
  height: 100%;
  overflow: hidden;
}
</style>
