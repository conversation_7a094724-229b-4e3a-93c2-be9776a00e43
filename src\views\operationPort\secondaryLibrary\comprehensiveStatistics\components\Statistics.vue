<template>
  <div class="statistics">
    <div class="item_box">
      <div class="item">
        <img src="@/assets/images/comprehensiveStatistics/statisticalIcon2x.png" alt="..." />
        <span class="label">库存总数量</span>
        <div class="item_info">
          <span v-showtipPlus="info.totalInventory" class="num"></span>
          <span class="unit">台</span>
        </div>
        <span class="label">库存总金额</span>
        <div class="item_info">
          <span v-showtipPlus="info.totalValue" class="num"></span>
          <span class="unit">元</span>
        </div>
      </div>
    </div>
    <div class="item_box">
      <div class="item">
        <img src="@/assets/images/comprehensiveStatistics/statisticalIcon2x1.png" alt="..." />
        <span class="label">高库存数量</span>
        <div class="item_info">
          <span v-showtipPlus="info.aboveMaxStockCount" class="num"></span>
          <span class="unit">台</span>
        </div>
        <span class="label warn"
          >低库存数量
          <img src="@/assets/images/comprehensiveStatistics/faultStatus2x.png" alt="..." />
        </span>
        <div class="item_info">
          <span v-showtipPlus="info.belowMinStockCount" class="num" style="color: #fa403c"></span>
          <span class="unit">台</span>
        </div>
      </div>
    </div>
    <div class="item_box">
      <div class="item">
        <img src="@/assets/images/comprehensiveStatistics/statisticalIcon2x2.png" alt="..." />
        <span class="label">入库总数量</span>
        <div class="item_info">
          <span v-showtipPlus="info.totalInCount" class="num"></span>
          <span class="unit">台</span>
        </div>
        <span class="label">入库总金额</span>
        <div class="item_info">
          <span v-showtipPlus="info.totalInValue" class="num"></span>
          <span class="unit">元</span>
        </div>
      </div>
    </div>
    <div class="item_box">
      <div class="item">
        <img src="@/assets/images/comprehensiveStatistics/statisticalIcon2x3.png" alt="..." />
        <span class="label">出库总数量</span>
        <div class="item_info">
          <span v-showtipPlus="info.totalOutCount" class="num"></span>
          <span class="unit">台</span>
        </div>
        <span class="label">出库总金额</span>
        <div class="item_info">
          <span v-showtipPlus="info.totalOutValue" class="num"></span>
          <span class="unit">元</span>
        </div>
      </div>
    </div>
    <div class="item_box">
      <div class="item">
        <div>
          <img src="@/assets/images/comprehensiveStatistics/statisticalIcon2x4.png" alt="..." />
          <div>
            <span class="label">仓库数量</span>
            <div class="item_info">
              <span v-showtipPlus="info.warehouseCount" class="num"></span>
              <span class="unit">台</span>
            </div>
          </div>
        </div>
        <div>
          <img src="@/assets/images/comprehensiveStatistics/statisticalIcon2x5.png" alt="..." />
          <div>
            <span class="label">分类数量</span>
            <div class="item_info">
              <span v-showtipPlus="info.materialTypeCount" class="num"></span>
              <span class="unit">台</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import api from '@/api/index.js'
export default {
  name: 'StatisticsIndex',
  data() {
    return {
      info: {}
    }
  },
  mounted() {
    this.handleGetData()
  },
  methods: {
    handleGetData() {
      const { username, userId } = this.$store.state.user.userInfo
      api.getInventoryStatistics({ username, userId }).then((res) => {
        this.info = res.data
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.statistics {
  display: flex;
  margin: 16px 0px;
  .item_box {
    width: calc(100% / 5);
    .item {
      display: flex;
      flex-direction: column;
      color: #333333;
      background: #fff;
      margin-right: 16px;
      padding: 24px;
      border-radius: 4px 4px 4px 4px;
    }
    &:last-child {
      .item {
        padding: 0;
        justify-content: space-between;
        background: transparent;
        margin-right: 0;
        height: 100%;
        > div {
          padding: 36px 24px;
          background: #fff;
          display: flex;
          align-items: center;
          border-radius: 4px 4px 4px 4px;
          img {
            margin-bottom: 0;
            margin-right: 24px;
          }
        }
      }
    }
    img {
      width: 66px;
      height: 66px;
      margin-bottom: 24px;
    }
    .warn {
      img {
        width: 20px;
        height: 20px;
        margin-bottom: 0;
      }
    }
    .label {
      font-size: 15px;
      font-weight: 600;
    }
    .item_info {
      margin-bottom: 24px;
      height: 45px;
      &:last-child {
        margin-bottom: 0;
      }
      .num {
        font-size: 30px;
        font-weight: bold;
        max-width: 80%;
        display: inline-block;
        height: 36px;
      }
      .unit {
        font-weight: 500;
        font-size: 15px;
        color: #96989a;
        margin-left: 4px;
      }
    }
  }
}
</style>
