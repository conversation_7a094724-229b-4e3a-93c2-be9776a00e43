<template>
  <PageContainer>
    <div slot="content" class="role-content">
      <div class="role-content-left">
        <div class="control-btn-header">
          <el-button v-auth="'roleManagement:add'" type="primary" icon="el-icon-plus" @click="handleRoleEvent('add')">新增</el-button>
          <el-button
            v-auth="'roleManagement:edit'"
            type="primary"
            :disabled="multipleSelection.length != 1"
            icon="el-icon-edit"
            @click="handleRoleEvent('edit', multipleSelection[0])"
          >编辑</el-button
          >
          <el-button v-auth="'roleManagement:start'" type="primary" :disabled="multipleSelection.length == 0" @click="handleRoleEvent('start', multipleSelection, true)"
          >角色启用</el-button
          >
          <el-button v-auth="'roleManagement:stop'" type="primary" :disabled="multipleSelection.length == 0" @click="handleRoleEvent('stop', multipleSelection, true)"
          >角色禁用</el-button
          >
        </div>
        <div class="table-content">
          <TablePage
            ref="tablePage"
            v-loading="tableLoading"
            :showPage="true"
            :tableColumn="tableColumn"
            :data="tableData"
            row-key="id"
            height="calc(100% - 40px)"
            :pageData="pageData"
            @pagination="paginationChange"
            @row-dblclick="handleRoleEvent('detail', $event)"
            @selection-change="handleSelectionChange"
          >
          </TablePage>
        </div>
      </div>
      <div v-if="configMenuShow" class="role-content-right">
        <ContentCard title="PC权限">
          <el-checkbox slot="title-right" v-model="menuChecked" class="title-right-check" @change="menuCheckedChange('menuChecked', 'menuPCdata')">全选</el-checkbox>
          <div slot="content" class="footer-menu">
            <treeCard
              ref="menuPCdata"
              treeHeight="100%"
              refPname="menuPCdata"
              :defaultExpandAll="true"
              :treeData="menuTreeData"
              :checkStrictly="true"
              :defaultExpandedKeys="menuDefaultExpandedKeys"
              :defaultCheckedKeys="menuDefaultCheckedKeys"
              :defaultProps="menuDefaultProps"
              nodeKey="menuId"
              @nodeCheckChange="menuNodeCheckChange"
            ></treeCard>
          </div>
        </ContentCard>
        <ContentCard title="APP权限">
          <el-checkbox slot="title-right" v-model="appMenuChecked" class="title-right-check" @change="menuCheckedChange('appMenuChecked', 'menuAPPdata')">全选</el-checkbox>
          <div slot="content" class="footer-menu">
            <treeCard
              ref="menuAPPdata"
              treeHeight="100%"
              refPname="menuAPPdata"
              :defaultExpandAll="true"
              :treeData="appMenuTreeData"
              :checkStrictly="true"
              :defaultExpandedKeys="[]"
              :defaultCheckedKeys="appMenuDefaultCheckedKeys"
              :defaultProps="menuDefaultProps"
              nodeKey="menuId"
              @nodeCheckChange="menuNodeCheckChange"
            ></treeCard>
          </div>
        </ContentCard>
        <footer class="footer-btn">
          <el-button type="primary" plain @click="changeMenuShow('configMenuShow', false)">关闭</el-button>
          <el-button type="primary" @click="submitTreeData('menu')">保存</el-button>
        </footer>
      </div>
      <div v-if="configAuthShow" class="role-content-right">
        <ContentCard title="数据权限" :cstyle="{ height: 'auto' }">
          <div slot="content" class="footer-menu">
            <el-radio-group v-model="roleForm.dataScope" @input="(val) => (deptTreeShow = val === 3)">
              <el-radio :label="1">全部数据</el-radio>
              <el-radio :label="2">本部门数据</el-radio>
              <el-radio :label="3">自定义</el-radio>
            </el-radio-group>
          </div>
        </ContentCard>
        <ContentCard v-if="deptTreeShow" title="数据范围" :cstyle="{ height: '80%' }">
          <div slot="content" class="footer-menu">
            <treeCard
              ref="deptTree"
              treeHeight="100%"
              refPname="deptTree"
              :treeData="deptTreeList"
              :defaultExpandedKeys="deptDefaultExpandedKeys"
              :defaultCheckedKeys="deptDefaultCheckedKeys"
              :defaultProps="deptDefaultProps"
            ></treeCard>
          </div>
        </ContentCard>
        <footer class="footer-btn">
          <el-button type="primary" plain @click="changeMenuShow('configAuthShow', false)">关闭</el-button>
          <el-button type="primary" @click="submitTreeData('auth')">保存</el-button>
        </footer>
      </div>
    </div>
  </PageContainer>
</template>
<script lang="jsx">
import $ from 'jquery'
import treeCard from './formComponent/treeCard.vue'
import { transData, treeToList, deepClone } from '@/util'
export default {
  name: 'roleManagement',
  components: {
    treeCard
  },
  beforeRouteEnter(to, from, next) {
    let names = []
    to.matched.map((v, i) => {
      if (i > 0) {
        v.components.default.name && names.push(v.components.default.name)
      }
    })
    // 进入页面时，先将当前页面的 name 信息存入 keep-alive 全局状态
    setTimeout(() => {
      next((vm) => {
        vm.$store.commit('keepAlive/add', names)
      })
    }, 0)
  },
  async beforeRouteLeave(to, from, next) {
    // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
    if (!['roleForm'].includes(to.name)) {
      // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      checked: false,
      configMenuShow: false,
      configAuthShow: false,
      tableLoading: false,
      keepAliveFlag: false,
      tableColumn: [
        {
          type: 'selection',
          align: 'center',
          width: 50,
          selectable: (row) => {
            return row.id !== 1
          }
        },
        {
          prop: 'serialNumber',
          label: '序号',
          width: 80,
          formatter: (scope) => {
            // 当前分页重置
            // return scope.$index + 1
            // 跨分页序号
            return (this.pageData.page - 1) * this.pageData.pageSize + scope.$index + 1
          }
        },
        {
          prop: 'roleName',
          label: '角色名称'
        },
        {
          prop: 'roleCode',
          label: '角色编码'
        },
        {
          prop: 'roleSort',
          label: '排序',
          width: 80
        },
        {
          prop: 'updateTime',
          label: '更新时间'
        },
        {
          prop: 'state',
          label: '状态',
          render: (h, row) => {
            return (
              <div>
                {row.row.state === 0
                  ? (
                    <div class="table-state-span" style="color: #08CB83">
                      <span class="circle" style="background-color: #08CB83"></span>
                      <span>已启用</span>
                    </div>
                  )
                  : (
                    <div class="table-state-span" style="color: #414653">
                      <span class="circle" style="background-color: #414653"></span>
                      <span>已停用</span>
                    </div>
                  )}
              </div>
            )
          }
        },
        {
          prop: 'operation',
          label: '操作',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                {this.$auth('roleManagement:del') && row.row.id != 1
                  ? (
                    <span class="operationBtn-span" style="color: #fa403c" onClick={() => this.handleRoleEvent('del', row.row)}>
                    删除
                    </span>
                  )
                  : (
                    ''
                  )}
                {row.row.state === 0
                  ? (
                    this.$auth('roleManagement:stop') && row.row.id != 1
                      ? (
                        <span class="operationBtn-span" style="color: #3562db" onClick={() => this.handleRoleEvent('stop', row.row)}>
                      停用
                        </span>
                      )
                      : (
                        ''
                      )
                  )
                  : this.$auth('roleManagement:start') && row.row.id != 1
                    ? (
                      <span class="operationBtn-span" style="color: #3562db" onClick={() => this.handleRoleEvent('start', row.row)}>
                    启用
                      </span>
                    )
                    : (
                      ''
                    )}
                {this.$auth('roleManagement:role') && row.row.id != 1
                  ? (
                    <span class="operationBtn-span" style="color: #08CB83" onClick={() => this.changeMenuShow('configAuthShow', true, row.row)}>
                    权限
                    </span>
                  )
                  : (
                    ''
                  )}
                {this.$auth('roleManagement:menu') && row.row.id != 1
                  ? (
                    <span class="operationBtn-span" style="color: #121F3E" onClick={() => this.changeMenuShow('configMenuShow', true, row.row)}>
                    菜单
                    </span>
                  )
                  : (
                    ''
                  )}
              </div>
            )
          }
        }
      ],
      tableData: [],
      pageData: {
        page: 1,
        pageSize: 15,
        total: 0
      },
      multipleSelection: [],
      selectRowId: '', // 权限菜单行id
      roleForm: {
        dataScope: ''
      }, // 权限菜单行详情数据
      // 菜单树
      menuChecked: false,
      appMenuChecked: false,
      allMenuKeys: [],
      appAllMenuKeys: [],
      menuTreeData: [],
      appMenuTreeData: [],
      menuDefaultExpandedKeys: [],
      menuDefaultCheckedKeys: [],
      appMenuDefaultCheckedKeys: [],
      menuDefaultProps: {
        children: 'children',
        label: 'menuName'
      },
      // 权限树
      deptTreeShow: false,
      deptTreeList: [],
      deptDefaultExpandedKeys: [],
      deptDefaultCheckedKeys: [],
      deptDefaultProps: {
        children: 'children',
        label: 'deptName'
      }
    }
  },
  activated() {
    this.getSysRoleList()
  },
  mounted() {
    this.getSysRoleList()
    this.getMenuTreeList()
    this.getAppMenuTreeList()
    this.getDeptTreeList()
  },
  methods: {
    // 获取角色列表
    getSysRoleList() {
      const params = {
        page: this.pageData.page,
        pageSize: this.pageData.pageSize
      }
      this.tableLoading = true
      this.$api.getSysRoleInfo(params).then((res) => {
        if (res.code === '200') {
          this.tableData = res.data.records
          this.pageData.total = res.data.total
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },
    // 获取App菜单列表
    getAppMenuTreeList() {
      const params = {
        state: 0,
        userId: this.$store.state.user.userInfo.userId
      }
      this.$api.SysAppMenu(params).then((res) => {
        if (res.code === '200') {
          const menuList = treeToList(res.data) ?? []
          this.appMenuTreeData = this.recursionTree(res.data ?? [])
          this.appAllMenuKeys = Array.from(menuList, (item) => item.menuId) ?? []
        }
      })
    },
    // 获取菜单列表
    getMenuTreeList() {
      const params = {
        state: 0,
        userId: this.$store.state.user.userInfo.userId
      }
      this.$api.getMenuTreeData(params).then((res) => {
        if (res.code === '200') {
          const menuList = treeToList(res.data) ?? []
          let asfas = menuList.filter(e => e.menuId == 1082 || e.menuId == 1083)
          this.menuTreeData = res.data ?? []
          this.allMenuKeys = Array.from(menuList, (item) => item.menuId) ?? []
        }
      })
    },
    recursionTree(tree) {
      const newTree = deepClone(tree)
      const loop = (data) => {
        data.forEach((item) => {
          item.disabled = item.appFix == 0
          if (item.children) {
            loop(item.children)
          }
        })
      }
      loop(newTree)
      return newTree
    },
    // 获取科室树数据
    getDeptTreeList() {
      this.$api.getSelectedDept({}).then((res) => {
        if (res.code == 200) {
          const deptList = res.data
          this.deptTreeList = transData(deptList, 'id', 'pid', 'children')
        }
      })
    },
    // 控制菜单树全选 反选
    menuCheckedChange(selectAllKey, treeKey) {
      const keys = treeKey == 'menuPCdata' ? this.allMenuKeys : this.appAllMenuKeys
      if (this[selectAllKey]) {
        // setCheckedKeys 设置全选
        this.$nextTick(() => {
          this.$refs[treeKey]?.setCheckedKeys(keys)
        })
      } else {
        // setCheckedKeys 设置全不选
        this.$nextTick(() => {
          this.$refs[treeKey]?.setCheckedKeys([])
        })
      }
    },
    // 控制菜单显示隐藏
    changeMenuShow(config, flag, row) {
      this.menuChecked = false
      this.appMenuChecked = false
      this.configMenuShow = false
      this.configAuthShow = false
      this[config] = flag
      if (flag) {
        // 同一个角色不重复请求
        if (this.selectRowId !== row.id) {
          this.selectRowId = row.id
          this.getRoleInfoById()
        }
        $('.role-content-left').removeClass('width100')
        $('.role-content-left').addClass('width75')
        setTimeout(function () {
          $('.role-content-right').fadeIn(310)
        }, 300)
      } else {
        this.selectRowId = ''
        $('.role-content-right').fadeOut(310)
        setTimeout(function () {
          $('.role-content-left').removeClass('width75')
          $('.role-content-left').addClass('width100')
        }, 300)
      }
    },
    // 获取角色信息
    getRoleInfoById() {
      this.$api.getSysRoleInfoByCode({ roleId: this.selectRowId }).then((res) => {
        if (res.code == 200) {
          const roleInfo = res.data.roleInfo
          Object.assign(this.roleForm, roleInfo)
          this.deptTreeShow = res.data.roleInfo.dataScope === 3
          this.deptDefaultCheckedKeys = res.data.deptId?.split(',') ?? []
          this.menuDefaultCheckedKeys = res.data.menuId?.split(',') ?? []
          this.appMenuDefaultCheckedKeys = res.data.menuIdApp?.split(',') ?? []
          this.$nextTick(() => {
            this.$refs['deptTree']?.setCheckedKeys(this.deptDefaultCheckedKeys)
            this.$refs['menuPCdata']?.setCheckedKeys(this.menuDefaultCheckedKeys)
            this.$refs['menuAPPdata']?.setCheckedKeys(this.appMenuDefaultCheckedKeys)
          })
          this.menuChecked = this.menuDefaultCheckedKeys.length === this.allMenuKeys.length
          this.appMenuChecked = this.appMenuDefaultCheckedKeys.length === this.appAllMenuKeys.length
        }
      })
    },
    handleRoleEvent(type, row, isArray) {
      this.keepAliveFlag = type === 'detail'
      if (type === 'add' || type === 'edit' || type === 'detail') {
        this.$router.push({
          name: 'roleForm',
          query: {
            type,
            id: row?.id ?? ''
          }
        })
      } else if (type === 'del') {
        const params = {
          id: row.id,
          delFlag: 1
        }
        const headerParams = {
          'operation-type': 3,
          'operation-id': params.id,
          'operation-name': row.roleName
        }
        this.delOrStateRole(params, type, headerParams)
      } else if (type === 'stop' || type === 'start') {
        const params = {
          id: isArray ? Array.from(row, ({ id }) => id).toString() : row.id,
          state: type === 'stop' ? 1 : 0
        }
        this.delOrStateRole(params, type)
      }
    },
    // 删除或停用角色
    delOrStateRole(params, type, headerParams = {}) {
      const msg = {
        del: '删除',
        stop: '停用',
        start: '启用'
      }
      this.$confirm('是否' + msg[type] + '该角色?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.removeOrDisuseSysRole(params, headerParams).then((res) => {
          if (res.code === '200') {
            this.$message.success(msg[type] + '成功')
            this.getSysRoleList()
          } else {
            this.$message.warning(res.msg)
          }
        })
      })
    },
    // 树选中返回数据
    menuNodeCheckChange([node, refPname]) {
      const checkModel = refPname === 'menuPCdata' ? 'menuChecked' : 'appMenuChecked'
      const keys = refPname == 'menuPCdata' ? this.allMenuKeys : this.appAllMenuKeys
      if (node.checked) {
        // 如果当前是选中checkbox,则递归设置父节点和父父节点++选中
        this.setParentNode(node)
        this.setChildenNode(node, true)
        // this.$nextTick(() => {
        // this[checkModel] = this.$refs[refPname].$refs.tree?.getCheckedKeys().length === keys.length
        // })
      } else {
        // this[checkModel] = false
        // 当前是取消选中,将所有子节点都取消选中
        this.setChildenNode(node)
      }
    },
    setParentNode(node) {
      if (node.parent) {
        for (const key in node) {
          if (key === 'parent') {
            node[key].checked = true
            this.setParentNode(node[key])
          }
        }
      }
    },
    setChildenNode(node, flag = false) {
      let len = node.childNodes.length
      for (let i = 0; i < len; i++) {
        node.childNodes[i].checked = flag
        this.setChildenNode(node.childNodes[i], flag)
      }
    },
    // 提交树数据
    submitTreeData(type) {
      let params = {
        id: this.selectRowId
      }
      // 菜单权限
      if (type === 'menu') {
        const selectNodes = this.$refs.menuPCdata?.getCheckedNodes(false, false) ?? []
        const selectAppNodes = this.$refs.menuAPPdata?.getCheckedNodes(false, false) ?? []
        Object.assign(params, {
          deptId: this.deptDefaultCheckedKeys.toString(),
          dataScope: this.roleForm.dataScope,
          menuId: selectNodes?.map((item) => item.menuId).join(',') ?? '',
          menuIdApp: selectAppNodes?.map((item) => item.menuId).join(',') ?? ''
        })
        this.updateRolePower(params, 'configMenuShow')
      } else {
        // 数据权限
        Object.assign(params, {
          dataScope: this.roleForm.dataScope,
          deptId: '',
          menuId: this.menuDefaultCheckedKeys.toString(),
          menuIdApp: this.appMenuDefaultCheckedKeys.toString()
        })
        if (params.dataScope === 3) {
          const selectNodes = this.$refs.deptTree?.getCheckedNodes() ?? []
          params.deptId = selectNodes?.map((item) => item.id).join(',') ?? ''
        }
        this.updateRolePower(params, 'configAuthShow')
      }
    },
    updateRolePower(params, type) {
      this.$api.updateRolePower(params).then((res) => {
        if (res.code === '200') {
          this.$message.success('保存成功')
          this.changeMenuShow(type, false)
        }
      })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getSysRoleList()
    }
  }
}
</script>
<style lang="scss" scoped>
.role-content {
  height: 100%;
  display: flex;

  .role-content-left {
    height: 100%;
    padding: 10px;
    background: #fff;
    border-radius: 4px;
    width: 100%;

    .control-btn-header {
      display: flex;
      justify-content: flex-start;
      padding-bottom: 15px;
    }

    .table-content {
      height: calc(100% - 45px);
      // flex: 1;
    }
  }

  .role-content-right {
    flex: 1;
    height: 100%;
    // padding: 10px;
    background: #fff;
    border-radius: 4px;
    width: 0%;
    margin-left: 15px;
    position: relative;

    ::v-deep .box-card {
      height: calc(50% - 26px);

      .title-right-check {
        position: absolute;
        right: 15px;
        top: 0;
      }
    }

    .footer-menu {
      height: 100%;
    }

    .footer-btn {
      position: absolute;
      bottom: 0;
      height: 50px;
      line-height: 50px;
      padding: 0 17px;
      text-align: right;
      width: 100%;
      background: #fff;
      flex: 0 0 auto;
      box-shadow: 0 0 12px 3px rgb(0 0 0 / 10%);
    }
  }

  .width75 {
    width: 75% !important;
    transition: width 0.8s;
  }

  .width100 {
    width: 100% !important;
    transition: width 0.8s;
  }
}
</style>
<style lang="scss">
.role-content {
  .operationBtn-span {
    margin-right: 10px;
  }
}
</style>
