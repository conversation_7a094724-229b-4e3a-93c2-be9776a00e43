<template>
  <div class="main">
    <el-dialog title="分配人员" :visible.sync="dialogVisible" :before-close="closeDialog" append-to-body custom-class="model-dialog">
      <div class="outermost">
        <div class="left">
          <div style="display: flex; justify-content: space-around;">
            <div :class="[activeTitle == '1' ? 'tree-title-active' : '']" class="tree-title" @click="asveActiveTitle(1)">院內职工</div>
            <div :class="[activeTitle == '2' ? 'tree-title-active' : '']" class="tree-title" @click="asveActiveTitle(2)">外委人员</div>
          </div>
          <el-input v-model.trim="filterText" style="margin: 5px 0; width: 200px;" clearable placeholder="输入关键字进行过滤"></el-input>
          <!-- <el-input v-else style="margin:5px 0" clearable placeholder="输入关键字进行过滤" v-model.trim="filterText2"></el-input> -->
          <div class="block">
            <el-tree ref="tree" v-loading="treeLoading" :data="data" :props="defaultProps" :filter-node-method="filterNode" @node-click="handleNodeClick"></el-tree>
          </div>
        </div>
        <div v-loading="loading" class="content">
          <div class="topTools">
            <el-input v-model.trim="name" placeholder="用户姓名" style="width: 150px; margin-right: 10px;"></el-input>
            <el-input v-model.trim="phone" placeholder="手机号码" style="width: 150px; margin-right: 10px;"></el-input>
            <el-button type="primary" @click="reset">重 置</el-button>
            <el-button type="primary" @click="search">查 询</el-button>
          </div>
          <div style="max-height: 70px; margin-bottom: 5px; overflow: auto;">
            <span>已选择：</span>
            <span v-for="item of multipleSelection" :key="item.index" style="color: #5188fc;">{{ item.name + '、' }}</span>
          </div>
          <el-table
            ref="personTable"
            v-loading="tableLoading"
            :data="tableData"
            border
            :height="tableHeight"
            stripe
            :row-key="getRowKeys"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" align="center" :reserve-selection="true"></el-table-column>
            <el-table-column type="index" label="序号" width="75">
              <template slot-scope="scope">
                <span>{{ (currentPage - 1) * pageSize + scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="name" show-overflow-tooltip label="用户姓名" width="180"></el-table-column>
            <el-table-column prop="mobile" show-overflow-tooltip label="手机号" width="180"></el-table-column>
            <el-table-column prop="officeName" show-overflow-tooltip label="所属部门"></el-table-column>
          </el-table>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-pagination
          class="user-pagination"
          style
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 40]"
          :pager-count="5"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
        <el-button type="primary" @click="closeDialog">取 消</el-button>
        <el-button type="primary" :disabled="addDis" class="sino-button-sure" @click="editSubmit">确认</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { transData } from '@/util'
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    tableDate: {}
  },
  data() {
    return {
      filterText: '',
      filterText2: '',
      activeTitle: 1,
      rules: {
        roleName: [{ required: true, message: '请输入角色名', trigger: 'change' }]
      },
      filters: {
        roleName: ''
      },

      name: '',
      phone: '',
      total: 0,
      currentPage: 1,
      treeDataTop: '',
      treeDataTopType: '',
      treeType: '',
      treeDataBottom: '',
      treeDataBottomLevel: '',
      treeDataBottomParent: '',
      personArr: [],
      data: [],
      data1: [],
      pageSize: 10,
      tableLoading: false,
      treeLoading: false,
      treeLoading1: false,
      multipleSelection: [],
      defaultProps: {
        value: 'id',
        label: 'officeName',
        children: 'children'
      },
      defaultProps1: {
        value: 'id',
        label: 'text',
        children: 'children'
      },
      tableData: [],
      loading: false,
      addDis: false
    }
  },
  computed: {
    tableHeight() {
      return document.body.clientHeight - 525
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    },
    filterText2(val) {
      this.$refs.tree2.filter(val)
    }
  },
  mounted() {
    this.init()
    this.search()
  },
  methods: {
    getRowKeys(row) {
      return row.id
    },
    closeDialog() {
      this.$emit('closeDialog')
    },
    editSubmit() {
      if (this.multipleSelection.length > 0) {
        this.$emit('sure', this.multipleSelection)
      } else {
        this.$message.error('请先选择人员')
      }
    },
    init() {
      this.treeLoading = true
      const params = {
        type: this.activeTitle
      }
      this.$api.outsourcedTreeDataIpsm(params).then((res) => {
        this.treeLoading = false
        this.data = transData(res.data, 'id', 'parentId', 'children')
      })
    },
    asveActiveTitle(i) {
      this.activeTitle = i
      this.treeDataTop = ''
      this.init()
      this.search()
    },
    handleSizeChange(val) {
      this.currentPage = 1
      this.pageSize = val
      this.search()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.search()
    },
    handleNodeClick(data) {
      this.currentPage = 1
      this.treeType = this.activeTitle
      this.treeDataBottom = ''
      this.treeDataBottomLevel = ''
      this.treeDataBottomParent = ''
      this.treeDataTop = data.id
      this.search()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    search() {
      let data = {
        isActivate: 1,
        pageSize: this.pageSize,
        currentPage: this.currentPage,
        name: this.name,
        mobile: this.phone,
        id: this.treeDataTop,
        type: this.activeTitle,
        outsourcedLevel: this.treeDataBottomLevel,
        outsourcedValue: this.treeDataBottom,
        outsourcedParent: this.treeDataBottomParent
      }
      this.tableLoading = true
      this.$api.getStaffListByOfficeOrTeamIpsm(data).then((res) => {
        this.tableLoading = false
        this.tableData = res.data.staffList
        this.total = parseInt(res.data.sum)
      })
    },
    reset() {
      this.currentPage = 1
      this.treeType = ''
      this.treeDataBottom = ''
      this.treeDataBottomLevel = ''
      this.treeDataBottomParent = ''
      this.treeDataTop = ''
      this.name = ''
      this.phone = ''
      this.search()
    },
    filterNode(value, data) {
      if (!value) return true
      return data.officeName.indexOf(value) !== -1
    },
    filterNode2(value, data) {
      if (!value) return true
      return data.text.indexOf(value) !== -1
    }
  }
}
</script>

<style lang="scss" scoped>
.outermost {
  display: flex;
  width: 930px;
  height: 500px;
  border: 1px solid #eee;
  padding: 10px;
}

.left {
  padding: 10px;
  width: 268px;
  margin-right: 10px;
  height: 100%;
  background-color: #fff;

  .left_content {
    height: calc(100% - 60px);
    overflow: auto;
  }
}

.content {
  background-color: #fff;
  padding: 10px;
  width: calc(100% - 278px);
  height: 100%;
}

.topTools {
  margin-bottom: 5px;
}

.user-pagination {
  text-align: right;
  position: absolute;
  right: 250px;
  bottom: 10px;
}
// @media screen and(max-width: 1600px) {
//   .user-pagination {
//     right: 210px;
//     bottom: 10px;
//   }
// }
.tree-title {
  border: 1px solid #ebeef5;
  width: 40%;
  margin-bottom: 5px;
  line-height: 30px;
  border-radius: 4px;
  text-align: center;
  color: #5188fc;
  cursor: pointer;
}

.tree-title-active {
  background: #5188fc;
  border-color: #5188fc;
  color: #fff;
}

.block {
  height: calc(100% - 80px);
  overflow: auto;
}

::v-deep .el-tree-node__label {
  display: inline-block;
  white-space: normal;
  text-align: left;
}

::v-deep .el-tree-node__content {
  height: auto;
}
</style>
