<!--
 * @Author: hedd
 * @Date: 2023-10-23 17:26:51
 * @LastEditTime: 2025-03-13 18:07:21
 * @FilePath: \ihcrs_pc\src\views\monitor\commonPage\elecRealWarning\components\parameterListDialog.vue
 * @Description:
-->
<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    :title="cardData.surveyEntityName"
    :visible.sync="visibleDialog"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <div class="content">
      <div v-for="(parameter, index) in cardData.parameterList" :key="index" class="parameter-data">
        <!-- <img src="@/assets/images/monitor/battery-voltage.png" class="info_list_icon" alt="" /> -->
        <img v-if="parameter.icon" :src="$tools.imgUrlTranslation(parameter.icon)" class="info_list_icon" alt="" />
        <span v-else class="info_list_icon"></span>
        <div class="list-param">
          <div class="parameterName" :title="parameter.parameterName">
            {{ parameter.dictAlias || parameter.parameterName || '-' }}
          </div>
          <!-- parameterException 2 离线 1异常 -->
          <div
            class="parameterUnit"
            :title="parameter.parameterValue + parameter.parameterUnit"
            :class="{ 'font-red': parameter.parameterException == '2' || parameter.parameterException == '1' }"
          >
            <svg-icon v-if="parameter.parameterException == '1'" name="general-alarm-red" />
            {{ getParameterValue(parameter) + (parameter.parameterUnit ? parameter.parameterUnit : '') || '-' }}
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>
<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    cardData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      message: 'Hello, world!',
      visibleDialog: this.visible
    }
  },
  mounted() {
    console.log('mounted', this.cardData)
  },
  methods: {
    getParameterValue(o) {
      if (o.parameterValue && o.parameterValue !== 'null') {
        if ([100026, 100028, 100038, 100040, 100048, 100050, 100052, 100056].includes(o.parameterId)) {
          return o.parameterValue < 0 ? '-' : o.parameterValue
        } else {
          return o.parameterValue
        }
      }
      return '-'
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', !this.visibleDialog)
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .model-dialog {
  width: fit-content !important;
  height: fit-content;
  .content {
    background: #fff;
    width: 630px;
    height: 100%;
    display: flex;
    flex-wrap: wrap;
    padding: 15px;
    .parameter-data {
      width: 200px;
      height: 74px;
      display: flex;
      justify-content: flex-start;
      padding: 0 20px;
      padding-right: 0;
      overflow: hidden;
      .info_list_icon {
        width: 40px;
        height: 40px;
        display: inline-block;
        margin: auto 0;
        text-align: center;
      }
      .list-param {
        margin: 6px 10px;
        max-width: 70%;
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        .parameterUnit {
          color: #333;
          font-size: 18px;
          font-weight: bold;
          margin-bottom: 2px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .parameterName {
          color: #666;
          font-size: 14px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .font-red {
          color: #f53f3f;
        }
      }
    }
  }
}
/* Your styles here */
</style>
