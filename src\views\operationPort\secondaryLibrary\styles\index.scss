.content-box {
  height: 100%;
  width: 100%;
  padding: 15px;
  display: flex;
  justify-content: space-between;
}

.content-left {
  height: 100%;
  width: 18%;
  margin-right: 15px;
  background: #fff;
  border-radius: 10px;

  .left-top-title {
    height: 50px;
    border-bottom: 1px solid #ccc;
    display: flex;
    align-items: center;

    .top-icon {
      width: 5px;
      height: 20px;
      background: #5188fc;
      margin: 0 15px;
    }
  }

  .left-bottom-box {
    height: calc(100% - 50px);
    overflow-y: auto;
    padding: 15px;
  }
}

.content-right {
  height: 100%;
  width: 81%;
  background: #fff;
  border-radius: 10px;
  padding: 10px 15px 15px;
  
  .right-bottom-box {
    height: calc(100% - 55px);

    .right-bottom-table {
      height: calc(100% - 55px - 47px);
      margin-top: 15px;
    }
  }
}
.content-right-width{
  width: 100%;
}