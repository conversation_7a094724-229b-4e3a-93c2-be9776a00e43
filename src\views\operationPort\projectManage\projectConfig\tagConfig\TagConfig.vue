<script>
import { UsingStatusOptions } from '@/views/operationPort/constant'
import tableListMixin from '@/mixins/tableListMixin'
export default {
  name: 'TagConfig',
  components: {
    TagEdit: () => import('./components/TagEdit.vue')
  },
  filters: {
    statusFilter(status) {
      return UsingStatusOptions.find((it) => it.value === +status)?.label ?? '-'
    },
    operateStatusFilter(status) {
      const value = +status === 1 ? 0 : 1
      return UsingStatusOptions.find((it) => it.value === value).label
    }
  },
  mixins: [tableListMixin],
  data: () => ({
    searchForm: {
      name: ''
    },
    tableData: [],
    loadingStatus: false,
    dialog: {
      show: false,
      id: 0
    }
  }),
  computed: {
    OperateType() {
      return {
        Edit: 'edit',
        Delete: 'delete',
        Create: 'create',
        Status: 'status'
      }
    }
  },
  mounted() {
    this.getDataList()
  },
  methods: {
    /** 获取表格数据 */
    getDataList() {
      this.loadingStatus = true
      const params = {
        name: this.searchForm.name,
        pageSize: this.pagination.size,
        pageNo: this.pagination.current
      }
      this.$api.SporadicProject.pageProjectTagInfo(params)
        .then((res) => {
          if (res.code === '200') {
            this.tableData = res.data.records
            this.pagination.total = res.data.total
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '查询失败'))
        .finally(() => (this.loadingStatus = false))
    },
    /** 点击查询 */
    onSearch() {
      this.pagination.current = 1
      this.getDataList()
    },
    /** 点击重置 */
    onReset() {
      this.$refs.formRef.resetFields()
      this.onSearch()
    },
    /** 表格相关按钮操作回调 */
    onOperate(row, command) {
      switch (command) {
        case this.OperateType.Delete:
          if (+row.state === 1) {
            this.$message.error('启用的标签不允许删除！')
          } else {
            this.$confirm('删除后将无法恢复，是否确定删除？', '信息提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消'
            }).then(() => this.doDelete(row.id))
          }
          break
        case this.OperateType.Status:
          const value = +row.state === 1 ? 0 : 1
          this.doUpdateStatus(row.id, value)
          break
        default:
          this.dialog.show = true
          this.dialog.id = row?.id ?? 0
          break
      }
    },
    /** 删除标签 */
    doDelete(id) {
      this.$api.SporadicProject.deleteProjectTagInfo({ id })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('删除成功')
            this.getDataList()
          } else {
            throw res.message
          }
        })
        .catch((msg) => msg && this.$message.error(msg))
        .finally(() => (this.loadingStatus = false))
    },
    /** 修改标签状态 */
    doUpdateStatus(id, status) {
      const param = { id, state: status }
      this.loadingStatus = true
      this.$api.SporadicProject.disOrUseProjectTagInfo(param)
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('修改状态成功')
            this.getDataList()
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg))
        .finally(() => (this.loadingStatus = false))
    }
  }
}
</script>
<template>
  <div class="tag-config">
    <div class="tag-config__top">
      <el-form ref="formRef" :model="searchForm" class="tag-config__search" inline>
        <el-form-item prop="name">
          <el-input v-model="searchForm.name" clearable filterable placeholder="请输入标签名称"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" plain @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
      <div class="tag-config__actions">
        <el-button type="primary" icon="el-icon-plus" @click="onOperate(undefined, OperateType.Create)"> 新增标签
        </el-button>
      </div>
    </div>
    <div class="tag-config__table">
      <el-table v-loading="loadingStatus" height="100%" :data="tableData" border stripe table-layout="auto" class="tableAuto" row-key="id">
        <el-table-column type="index" width="55" label="序号" />
        <el-table-column label="标签名称" prop="name" show-overflow-tooltip></el-table-column>
        <el-table-column label="编码" prop="code" width="100px"></el-table-column>
        <el-table-column label="状态" prop="status" width="100px">
          <template #default="{ row }">
            <span class="tag-config__tag" :class="`tag-config__tag--${row.state}`">
              {{ row.state | statusFilter }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" show-overflow-tooltip :formatter="row => row.remark||'-'"></el-table-column>
        <el-table-column label="操作" width="150px">
          <template #default="{ row }">
            <el-button type="text" @click="onOperate(row, OperateType.Edit)">编辑</el-button>
            <el-dropdown @command="(command) => onOperate(row, command)">
              <el-button type="text" style="margin-left: 10px">更多</el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="OperateType.Status">
                    {{ row.state | operateStatusFilter }}
                  </el-dropdown-item>
                  <el-dropdown-item style="color: #ff1919" :command="OperateType.Delete">删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-pagination
      class="tag-config__pagination"
      :current-page="pagination.page"
      :page-sizes="pagination.pageSizeOptions"
      :page-size="pagination.size"
      :layout="pagination.layoutOptions"
      :total="pagination.total"
      @size-change="paginationSizeChange"
      @current-change="paginationCurrentChange"
    >
    </el-pagination>
    <!--标签弹窗-->
    <TagEdit v-bind="dialog" :visible.sync="dialog.show" @success="getDataList" />
  </div>
</template>
<style scoped lang="scss">
.tag-config {
  display: flex;
  flex-flow: column nowrap;
  padding: 16px;
  &__top {
    display: flex;
    justify-content: space-between;
  }
  &__actions {
    line-height: 40px;
  }
  &__pagination {
    margin-top: 10px;
  }
  &__table {
    margin-top: -10px;
    flex: 1;
  }
  &__tag {
    // 停用
    &--0 {
      --color: #f64646;
    }
    // 启用
    &--1 {
      --color: #00b42a;
    }
    &:before {
      content: '';
      display: inline-block;
      vertical-align: middle;
      margin-right: 4px;
      height: 4px;
      width: 4px;
      border-radius: 4px;
      background-color: var(--color);
    }
  }
}
</style>
