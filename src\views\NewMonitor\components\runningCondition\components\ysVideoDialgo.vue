<template>
    <el-dialog :title="title" width="1000px" :visible.sync="videoDialgoShow" custom-class="model-dialog"
        :before-close="closeDialog" :close-on-click-modal="false">
        <div class="content_left video_content">
            <div class="top_content">
                <el-radio-group v-model="type" size="mini" @change="(e) => videoTypeChange(e)">
                    <el-radio-button label="1">一屏</el-radio-button>
                    <el-radio-button label="2">四屏</el-radio-button>
                </el-radio-group>
            </div>
            <template>
                <div v-if="type == '1'" class="video_group">
                    <el-carousel v-if="videoList.length" height="100%" class="video_Carousel_item" :interval="180000"
                        arrow="always">
                        <el-carousel-item v-for="(videoItem, index) in videoList" :key="index">
                            <rtspCavas ref="rtspCavas" style="width: 100%; height: 100%" :rtspUrl="videoItem.url"
                                :videoName="videoItem.name" :hasCavas="Boolean(videoItem.url)" :hasControl="false"
                                :isLight="false" @toHiddenOperation="(e) => toHiddenRightBox(e, videoItem)">
                            </rtspCavas>
                        </el-carousel-item>
                    </el-carousel>
                    <template v-else class="video_Carousel_item_empty">
                        <img src="@/assets/images/alarmCenter/nopic.png" width="100%" height="96%" alt=""
                            style="margin-top:16px;" />
                    </template>
                </div>
                <div v-else class="video_group">
                    <el-carousel v-if="videoGroups.length" :interval="180000" arrow="always" class="video_Carousel_item"
                        height="100%" style="margin: 10px 0;">
                        <el-carousel-item v-for="(group, groupIndex) in videoGroups" :key="groupIndex">
                            <div class="video_group ">
                                <rtspCavas v-for="(videoItem, index) in group" :key="index" ref="rtspCavas"
                                    class="video_item video_Card_item" :rtspUrl="videoItem.url"
                                    :videoName="videoItem.name" :hasCavas="Boolean(videoItem.url)" :hasControl="false"
                                    :isLight="false" @toHiddenOperation="(e) => toHiddenRightBox(e, videoItem)" />
                            </div>
                        </el-carousel-item>
                    </el-carousel>
                    <div v-else class="video_Carousel_item_empty">
                        <div v-for="videoItem in 4" :key="videoItem" class="video_Carditemempty">
                            <img src="@/assets/images/alarmCenter/nopic.png" width="100%" height="100%" alt="" />
                        </div>

                    </div>
                </div>
            </template>

        </div>
    </el-dialog>
</template>
<script>
export default {
    name: 'monitorExport',
    props: {
        title: {
            type: String,
            default: ''
        },
        videoId: {
            type: String,
            default: ''
        },
        factoryCode: {
            type: Object,
            default: () => ({})
        },
        videoDialgoShow: {
            type: Boolean,
            default: false
        },
    },
    data() {
        return {
            type: '1',
            videoList: [],
            elevatorSelectId: '',
            elevatorSelectName: '',
            elevatorOptions: [],
            videoUrl: '',
            videoName: '',
        }
    },
    computed: {
        videoGroups() {
            const groups = [];
            for (let i = 0; i < this.videoList.length; i += 4) {
                groups.push(this.videoList.slice(i, i + 4)); // 每组 4 个视频
            }
            return groups;
        },
    },
    mounted() {
        this.jkhmTCElevatorList()
    },
    methods: {
        // 通过轮播图改变选中电梯
        changeCarousel(val) {
            const selectData = this.elevatorOptions[val]
            this.elevatorSelectId = selectData.id
            this.elevatorSelectName = selectData.name
        },
        videoTypeChange(type) {
            this.type = type
        },
        closeDialog() {
            this.$emit('closeVideoDialog')
        },
        // 初始化获取所有监控状态数据
        jkhmTCElevatorList() {
            this.$api
                .getCustomGroupingQueryList(this.videoId)
                .then((res) => {
                    if (res.code == 200 || res.code == '200') {
                        this.elevatorOptions = res.data
                        if (this.elevatorOptions.length) {
                            this.elevatorOptions.forEach(option => {
                                this.playVideoByElevatorId(option);
                            });
                        } else {
                            this.playVideoByElevatorId()
                        }
                    } else {
                        this.$message.error(res.message)
                    }
                })
        },
        // 播放电梯监控
        playVideoByElevatorId(selectData) {
            if (selectData) {
                this.$api.getQueryInstanceFunction(selectData.factoryCode, 'previewStreaming', {}).then(res => {
                    if (res.status === 200) {
                        if (res.result.length && res.result[0].url) {
                            const videoItem = {
                                id: selectData.factoryCode,
                                name: selectData.assetsName,
                                url: res.result[0].url
                            };
                            if (videoItem.id === this.factoryCode.id) {
                                this.videoList.unshift(videoItem);
                            } else {
                                this.videoList.push(videoItem);
                            }
                        } else {
                            this.videoList = []
                            this.$message.error('暂无内容')
                        }
                    } else {
                        this.$message.error(res.message)
                    }
                })
            } else {
                this.videoList = []
            }
        },
    }
}
</script>
<style lang="scss" scoped>
.content_left {
    width: 100%;
    height: 100%;
}

::v-deep .video_Carousel_item .el-carousel__indicator {
    display: none;
    /* 隐藏指示条 */
}

.video_content {
    width: 100%;

    .video_title {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .video_group {
        margin-top: 6px;
        width: 100%;
        height: 482px;
        background: #f6f5fa;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;

        .video_Card_item {
            margin-right: 16px;
            margin-bottom: 10px;
            width: calc(50% - 8px);
            height: calc(50% - 15px);
        }

        .video_Card_item:nth-child(2n) {
            margin-right: 0;
        }

        .video_Card_item_empty {
            margin-left: 16px;
            margin-top: 16px;
            width: calc(50% - 16px);
            height: calc(50% - 16px);
            display: flex;
            align-items: center;
            justify-content: center;
            background: #fff;
        }

        .video_Carousel_item {
            margin: 16px 0;
            width: 100%;
            height: calc(100% - 10px);
        }

        ::v-deep .el-carousel__container {
            height: 100%;
            width: 100%;

            .el-carousel__arrow i {
                font-size: 26px;
                color: #3562db;
            }

            .el-carousel__arrow {
                width: 24px;
                height: 44px;
                background-color: transparent;
                border-radius: unset;
            }

            .el-carousel__arrow:hover {
                background-color: rgba(255, 255, 255, 0.1);
            }
        }

        .video_Carousel_item_empty {
            width: 100%;
            height: calc(100% - 16px);
            display: flex;
            flex-wrap: wrap;
            justify-content: center;

            .video_Carditemempty {
                margin-right: 16px;
                margin-top: 16px;
                width: calc(50% - 16px);
                height: calc(50% - 10px);
                display: flex;
                align-items: center;
                justify-content: center;
                background: #fff;
            }
        }
    }
}

::v-deep .el-radio-button__orig-radio:checked+.el-radio-button__inner {
    background-color: $color-primary;
    border-color: $color-primary;
}

.elevator_carousel {
    height: calc(100% - 5px);
    margin-bottom: 10px;
    display: flex;
    justify-content: center;

    ::v-deep .el-carousel__container {
        height: 100%;
        width: 90%;

        .el-carousel__arrow--left {
            left: -22px;
        }

        .el-carousel__arrow--right {
            right: -22px;
        }

        .el-carousel__arrow i {
            font-size: 26px;
            color: #3562db;
        }

        .el-carousel__item {
            display: flex;

            .elevator_img {
                margin: auto;
            }
        }

        .el-carousel__arrow {
            width: 24px;
            height: 44px;
            background-color: transparent;
            border-radius: unset;
        }

        .el-carousel__arrow:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }
    }

    ::v-deep .el-carousel__indicators {
        display: none;
    }
}
</style>