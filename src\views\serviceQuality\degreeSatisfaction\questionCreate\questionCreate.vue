<template>
  <div class="main-container">
    <div class="whole">
      <div class="page-title">问卷名称</div>
      <el-row style="border-bottom: 1px solid #d8dee7;">
        <el-col :md="16" :lg="16" :xl="14" style="height: auto;">
          <BreadcrumbNavBar />
        </el-col>
        <el-col :md="8" :lg="8" :xl="10">
          <HeaderButton />
        </el-col>
      </el-row>
      <div class="bom">
        <el-form ref="creatQuestionForm" :model="createQuestionForm" label-width="80px" :rules="rules">
          <el-form-item label="问卷名称" prop="questionName">
            <el-input v-model="createQuestionForm.questionName" class="ipt"></el-input>
          </el-form-item>
          <el-form-item label="前置说明" prop="startText">
            <el-input v-model="createQuestionForm.startText" type="textarea" rows="8" style="width: 50%;"></el-input>
          </el-form-item>
          <el-form-item label="后置说明" prop="endText">
            <el-input v-model="createQuestionForm.endText" type="textarea" rows="8" style="width: 50%;"></el-input>
          </el-form-item>
          <el-row class="submit-container">
            <el-button type="primary" @click="submitForm">确定</el-button>
          </el-row>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import BreadcrumbNavBar from '../component/BreadcrumbNavBar/BreadcrumbNavBar.vue'
import HeaderButton from '../questionDesign/component/HeaderButton/HeaderButton.vue'
export default {
  components: {
    BreadcrumbNavBar,
    HeaderButton
  },
  data() {
    return {
      createQuestionForm: {
        questionName: '', // 创建问卷的名称
        startText: '', // 前置说明
        endText: '' // 后置说明
      },
      rules: {
        questionName: [
          { required: true, message: '请输入问卷名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        startText: [{ min: 0, max: 200, message: '长度大于200个字符', trigger: 'blur' }],
        endText: [{ min: 0, max: 200, message: '长度大于200个字符', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getfindPaper()
  },
  methods: {
    getfindPaper() {
      let params = {
        questionId: localStorage.getItem('questId')
      }
      this.$api.findPaper(params).then((res) => {
        if (res.status == 200) {
          this.createQuestionForm.questionName = res.data.name
          this.createQuestionForm.startText = res.data.startText
          this.createQuestionForm.endText = res.data.endText
        }
      })
    },
    submitForm() {
      let params = {
        userId: this.$store.state.user.userInfo.userId,
        userName: this.$store.state.user.userInfo.username,
        id: localStorage.getItem('questId'),
        name: this.createQuestionForm.questionName,
        startText: this.createQuestionForm.startText,
        endText: this.createQuestionForm.endText
      }
      this.$refs['creatQuestionForm'].validate((valid) => {
        if (valid) {
          this.$api.updatePvq(params).then((res) => {
            if (res.status == 200) {
              this.$message({
                message: '修改问卷模板成功',
                type: 'success'
              })
            } else {
              this.$message.error(res.message)
            }
          })
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.main-container {
  height: 100%;
  padding: 15px;
  margin: 0 !important;

  .whole {
    width: 100%;
    height: 100%;
    border-radius: 5px;
    background-color: #fff !important;

    .page-title {
      height: 50px;
      line-height: 50px;
      color: #606266;
      font-size: 14px;
      padding-left: 25px;
      border-bottom: 1px solid #d8dee7;
    }

    .submit-container {
      display: flex;
      justify-content: center;
      padding-bottom: 20px;
    }

    .el-button--text {
      color: #fff;
      padding: 12px 10px;
    }

    .el-button--text:hover,
    .el-button:hover {
      color: #fff;
      background-color: #5188fc;
      border-radius: 0;
      border-color: transparent;
    }
  }
}

@media screen and (max-width: 1366px) {
  .main-container .el-button--text {
    color: #fff;
    padding: 0 15px;
  }
}

.ipt {
  width: 200px;
}

.bom {
  padding: 0 15px;
  margin-left: 25%;
}
</style>
