<template>
  <el-dialog v-if="parameterAliasDialogShow" title="参数别名" width="60%" :visible.sync="parameterAliasDialogShow"
    custom-class="model-dialog" :before-close="closeDialog">
    <div class="camera_content" style="padding: 10px 20px 10px 10px;">
      <el-form ref="assetsData" :model="assetsData">
        <div v-if="parameterData.valueType.valueType">
          <div v-if="parameterData.valueType.valueType.type === 'enum'">
            <el-form-item label="" label-width="0px" v-for="(item, index) in parameterData.valueType.valueType.elements"
              :key="index">
              <el-input readonly v-model="item.value" placeholder=""></el-input>-<el-input readonly v-model="item.text"
                placeholder=""></el-input>
              <el-input v-model="item.textAlias" maxlength="25" placeholder="请输入别名" style="margin-left:15px"></el-input>
            </el-form-item>
          </div>
          <div v-if="parameterData.valueType.valueType.type === 'boolean'">
            <el-form-item label="" label-width="0px">
              <el-input readonly v-model="parameterData.valueType.valueType.falseValue"
                placeholder=""></el-input>-<el-input readonly v-model="parameterData.valueType.valueType.falseText"
                placeholder=""></el-input>
              <el-input v-model="parameterData.valueType.valueType.falseTextAlias" maxlength="25" placeholder="请输入别名"
                style="margin-left:15px"></el-input>
            </el-form-item>
            <el-form-item label="" label-width="0px">
              <el-input readonly v-model="parameterData.valueType.valueType.trueValue"
                placeholder=""></el-input>-<el-input readonly v-model="parameterData.valueType.valueType.trueText"
                placeholder=""></el-input>
              <el-input v-model="parameterData.valueType.valueType.trueTextAlias" maxlength="25" placeholder="请输入别名"
                style="margin-left:15px"></el-input>
            </el-form-item>
          </div>
        </div>
        <div v-if="parameterData.valueType !== undefined">
          <div v-if="parameterData.valueType.type === 'boolean'">
            <el-form-item label="" label-width="0px">
              <el-input readonly v-model="parameterData.valueType.falseValue" placeholder=""></el-input>-<el-input
                readonly v-model="parameterData.valueType.falseText" placeholder=""></el-input>
              <el-input v-model="parameterData.valueType.falseTextAlias" maxlength="25" placeholder="请输入别名"
                style="margin-left:15px"></el-input>
            </el-form-item>
            <el-form-item label="" label-width="0px">
              <el-input readonly v-model="parameterData.valueType.trueValue" placeholder=""></el-input>-<el-input readonly
                v-model="parameterData.valueType.trueText" placeholder=""></el-input>
              <el-input v-model="parameterData.valueType.trueTextAlias" maxlength="25" placeholder="请输入别名"
                style="margin-left:15px"></el-input>
            </el-form-item>
          </div>
          <div v-if="parameterData.valueType.type === 'enum'">
            <el-form-item label="" label-width="0px" v-for="(item, index) in parameterData.valueType.elements"
              :key="index">
              <el-input readonly v-model="item.value" placeholder=""></el-input>-<el-input readonly v-model="item.text"
                placeholder=""></el-input>
              <el-input v-model="item.textAlias" maxlength="25" placeholder="请输入别名" style="margin-left:15px"></el-input>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="groupSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script lang="jsx">
export default {
  name: 'equipmentAssetsDialog',
  props: {
    parameterAliasDialogShow: {
      type: Boolean,
      default: false
    },
    parameterData: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      assetsData: {},
    }
  },
  mounted() {
    console.log(this.parameterData, 'this.this.parameterData');
    Object.assign(this.assetsData, this.parameterData)
  },
  methods: {
    // 关联资产
    // getAssetsListData() {
    //   let data = {
    //     ...this.assetsData,
    //     currentPage: this.pageinationData.page,
    //     pageSize: this.pageinationData.pageSize,
    //     professionalCategoryCode: '', // 专业类别
    //     assetCategoryCode: '', // 资产大类
    //     assetSubcategoryCode: '' // 资产小类
    //   }
    //   this.tableLoading = true
    //   this.$api.getAssetList(data).then((res) => {
    //     this.tableLoading = false
    //     if (res.code == '200') {
    //       this.tableData = res.data.assetDetailsList
    //       this.pageinationData.total = parseInt(res.data.sum)
    //       // 选中数据回显
    //       if (this.assetsCheckData?.assetsId) {
    //         let selectRow = this.tableData.find((e) => e.assetsId == this.assetsCheckData.assetsId)
    //         if (selectRow) {
    //           this.$refs.multipleAssetsTable.setCurrentRow(selectRow)
    //         }
    //       }
    //     }
    //   })
    // },
    closeDialog() {
      console.log('参数别名关闭');
      this.$emit('closeParameterDialog')
    },
    groupSubmit() {
      if (this.parameterData.inputs) {
        this.$emit('submitParameterDialog', this.parameterData.inputs)
      } else if (this.parameterData.valueType) {
        this.$emit('submitParameterDialog', this.parameterData.valueType)
      } else {
        this.$message({
          message: '请输入相关内容！',
          type: 'warning'
        })
      }
    }
  }
}
</script>
<style lang="scss" scope>
.model-dialog {

  // width: 60%;
  .camera_content {
    width: 100%;
    margin: 0 auto;
    background: #fff;
    border-radius: 4px;
    // display: flex;
    width: 100%;
    height: 100%;

    .el-form-item__content {
      display: flex;
    }

  }
}
</style>
