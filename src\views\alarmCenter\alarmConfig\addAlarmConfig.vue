<template>
  <PageContainer :footer="true">
    <div slot="content">
      <div class="addAlarmConfig-content-title" @click="goBack">
        <i class="el-icon-arrow-left"></i>
        <span style="margin-left: 10px">
          <span v-if="!$route.query.activeType">{{ $route.query.baseId ? '编辑' : '新增' }}报警配置</span>
        </span>
      </div>
      <div class="content_box">
        <div class="contentBox-left" v-on:scroll="scrollHandler">
          <div class="basicBox">
            <div class="content-title">基础信息</div>
            <el-form ref="formInline" :model="addForm" :inline="true" class="form-inline" label-width="auto" :rules="rules">
              <el-form-item label="报警系统" prop="alarmSystemCode">
                <el-select v-model="addForm.alarmSystemCode" :disabled="activeType == 'detail'" placeholder="请选择报警系统" filterable clearable @change="alarmSystemChange">
                  <el-option v-for="item in alarmSystemList" :key="item.thirdSystemCode" :label="item.thirdSystemName" :value="item.thirdSystemCode"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="报警类型" prop="alarmTypeCode">
                <el-select
                  v-model="addForm.alarmTypeCode"
                  filterable
                  :disabled="activeType == 'detail'"
                  placeholder="请选择报警类型"
                  class="ml-16"
                  clearable
                  @change="alarmTypeChange"
                >
                  <el-option v-for="item in alarmTypeList" :key="item.id" :label="item.alarmDictName" :value="item.id"> </el-option>
                </el-select>
              </el-form-item>
              <br />
              <el-form-item label="产生报警等级" prop="alarmLevel">
                <el-checkbox-group v-model="addForm.alarmLevel" :disabled="activeType == 'detail'" @change="gradeChange">
                  <el-checkbox v-for="item in alarmLevelList" :key="item.value" :label="item.value">{{ item.label }}</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <br />
            </el-form>
          </div>
          <div v-if="importantShow" id="importantBox" class="importantBox">
            <div class="content-title">重要警情</div>
            <Config ref="importantInfo" :projectCode="projectCode" :isPoliceAlarm="true"></Config>
          </div>
          <div v-if="exigencyShow" id="exigencyBox" class="exigencyBox">
            <div class="content-title">紧急警情</div>
            <Config ref="exigencyInfo" :projectCode="projectCode" :policeAlarmType="'2'"></Config>
          </div>
          <div v-if="normalShow" id="normalBox" class="normalBox">
            <div class="content-title">一般警情</div>
            <Config ref="normalInfo" :projectCode="projectCode" :policeAlarmType="'1'"></Config>
          </div>
          <div v-if="informShow" id="informBox" class="informBox">
            <div class="content-title">通知警情</div>
            <Config ref="informInfo" :projectCode="projectCode" :policeAlarmType="'0'"></Config>
          </div>
        </div>
        <div class="contentBox-right">
          <div v-if="importantShow" :class="{ isLight: btnType == '0' }" @click="anchor('0')">
            <span :class="['lineBox', btnType == '0' ? 'lineStyle' : '']"></span><span>重要警情</span>
          </div>
          <div v-if="exigencyShow" :class="{ isLight: btnType == '1' }" @click="anchor('1')">
            <span :class="['lineBox', btnType == '1' ? 'lineStyle' : '']"></span><span>紧急警情</span>
          </div>
          <div v-if="normalShow" :class="{ isLight: btnType == '2' }" @click="anchor('2')">
            <span :class="['lineBox', btnType == '2' ? 'lineStyle' : '']"></span><span>一般警情</span>
          </div>
          <div v-if="informShow" :class="{ isLight: btnType == '3' }" @click="anchor('3')">
            <span :class="['lineBox', btnType == '3' ? 'lineStyle' : '']"></span><span>通知警情</span>
          </div>
        </div>
      </div>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">取消</el-button>
      <el-button type="primary" :disabled="activeType == 'detail'" @click="submitForm()">保存</el-button>
    </div>
    <template v-if="contingencyVisible">
      <contingencyChoose v-if="contingencyVisible" :visible.sync="contingencyVisible" />
    </template>
  </PageContainer>
</template>
<script>
import Config from './components/config.vue'
import { cloneDeep } from 'lodash'
import { listToTree } from '@/util'
import axios from 'axios'
export default {
  name: 'addAlarmConfig',
  components: {
    Config
  },
  async beforeRouteLeave(to, from, next) {
    if (!['alarmConfigIndex'].includes(to.name)) {
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      alarmSystemList: [],
      alarmTypeList: [],
      planList: [], // 预案list
      taskTeamList: [], // 班组
      contingencyVisible: false,
      workOrderItemList: [], // 工单事项
      btnType: '0',
      alarmLevelList: [
        {
          label: '重要警情',
          value: '3'
        },
        {
          label: '紧急警情',
          value: '2'
        },
        {
          label: '一般警情',
          value: '1'
        },
        {
          label: '通知警情',
          value: '0'
        }
      ],
      addForm: {
        alarmSystemCode: '', // 报警系统
        alarmSystemName: '', // 报警系统名称
        alarmTypeCode: '', // 报警系统
        alarmTypeName: '', // 报警类型名称
        alarmLevel: ['3'] // 报警类型
      },
      rules: {
        alarmSystemCode: { required: true, message: '请选择报警系统', trigger: 'change' },
        alarmTypeCode: { required: true, message: '请选择报警类型', trigger: 'change' },
        alarmLevel: { required: true, message: '请至少选择一个报警等级', trigger: 'change' }
      },
      termTypeOptions: [],
      emergencyTeamList: [],
      importantShow: true,
      exigencyShow: false,
      normalShow: false,
      informShow: false,
      projectCode: '',
      activeType: '',
      free1: ''
    }
  },
  mounted() {
    this.initEvent()
  },
  methods: {
    initEvent() {
      this.getAlarmSystemList()
      const baseId = this.$route.query.baseId
      this.activeType = this.$route.query.activeType
      if (baseId) {
        setTimeout(() => {
          this.getDetailData()
        }, 300)
      }
    },
    // 获取详情
    getDetailData() {
      const baseId = this.$route.query.baseId
      this.$api.getAlarmConfigListById({ baseId: baseId }).then((res) => {
        if (res.code == 200) {
          this.$emit('getWorkOrderList')
          this.addForm.alarmSystemCode = res.data[0].alarmSystemCode
          this.addForm.alarmSystemName = res.data[0].alarmSystemName
          this.addForm.alarmTypeCode = res.data[0].alarmTypeCode
          this.addForm.alarmTypeName = res.data[0].alarmTypeName
          let alarmLevelArr = []
          res.data.forEach((item) => {
            alarmLevelArr.push(item.alarmLevel)
          })
          this.addForm.alarmLevel = alarmLevelArr
          this.gradeChange(this.addForm.alarmLevel)
          this.getAlarmTypeData(this.addForm.alarmSystemCode)
          // 重要回显
          if (this.importantShow) {
            this.$nextTick(() => {
              let importantObj = res.data.find((item) => item.alarmLevel === '3') // 重要三
              if (importantObj.alarmModel === '0') {
                this.$refs.importantInfo.formInline.alarmModel = true // 是否进入战时模式，0-是，1-否
              } else {
                this.$refs.importantInfo.formInline.alarmModel = false
              }
              this.$refs.importantInfo.formInline.disposalErminal = importantObj.disposalErminal.split(',') // 处置终端，0-专业客户端，1-手机端，2-web端
              this.$refs.importantInfo.formInline.id = importantObj.id // 编辑
              if (importantObj.alarmModel === '0') {
                this.$refs.importantInfo.formInline.alarmResponseWay = importantObj.alarmResponseWay.split(',')
                this.$refs.importantInfo.formInline.alarmResponseNum = importantObj.alarmResponseNum // 声光报警次数
                this.$refs.importantInfo.formInline.preplanId = importantObj.preplanId
                this.$refs.importantInfo.formInline.preplanName = importantObj.preplanName
                this.$refs.importantInfo.groupTableData = importantObj.emergencys || [] // 应急队伍
                this.$refs.importantInfo.formInline.emergencyTeamId = importantObj.emergencyTeamId
              } else {
                this.$refs.importantInfo.formInline.alarmSecureWay = importantObj.alarmSecureWay.split(',')
              }
              if (importantObj.preplanId) {
                this.$refs.importantInfo.generationContents = importantObj.occurAlarmNotices || [
                  {
                    alarmConfigType: '0', // 报警处理方式，0-报警产生时，1-确警时
                    noticeWay: '0', // 通知方式，0-短信，1-app，2-企业微信，3-微信服务号，4-语音电话
                    noticeType: '1', // 0-部门，1-人员
                    personId: '', // 人员编码
                    personName: '', // 人员名称
                    tel: '', // 联系电话
                    departName: '', // 部门名称
                    departCodes: [], // 部门code
                    departCode: '', // 部门code（逗号分割提交）
                    noticePersonList: [], // 人员列表
                    selectDeptList: [] // 部门 人员归类（不提交）
                  }
                ]
                this.$refs.importantInfo.generationContents.forEach((item) => {
                  if (item.noticeType === '0') {
                    item.departCodes = item.departCode ? item.departCode.split(',') : []
                    let arr = JSON.parse(item.noticePersonStr)
                    item.noticePersonList = []
                    if (arr && arr.length) {
                      arr.forEach((el) => {
                        item.noticePersonList = item.noticePersonList.concat(el.userList)
                      })
                    }
                    item.selectDeptList = JSON.parse(item.noticePersonStr)
                  }
                })
              } else {
                this.$refs.importantInfo.generationContents = importantObj.occurAlarmNotices || [
                  {
                    alarmConfigType: '0', // 报警处理方式，0-报警产生时，1-确警时
                    noticeWay: '0', // 通知方式，0-短信，1-app，2-企业微信，3-微信服务号，4-语音电话
                    noticeType: '1', // 0-部门，1-人员
                    personId: '', // 人员编码
                    personName: '', // 人员名称
                    tel: '', // 联系电话
                    departName: '', // 部门名称
                    departCodes: [], // 部门code
                    departCode: '', // 部门code（逗号分割提交）
                    noticePersonList: [], // 人员列表
                    selectDeptList: [] // 部门 人员归类（不提交）
                  }
                ]
                this.$refs.importantInfo.generationSureContents = importantObj.confirmedAlarmNotices || [
                  {
                    alarmConfigType: '1', // 报警处理方式，0-报警产生时，1-确警时
                    noticeWay: '0', // 通知方式，0-短信，1-app，2-企业微信，3-微信服务号，4-语音电话
                    noticeType: '1', // 0-部门，1-人员
                    personId: '', // 人员编码
                    personName: '', // 人员名称
                    tel: '', // 联系电话
                    departName: '', // 部门名称
                    departCodes: [], // 部门code
                    departCode: '', // 部门code（逗号分割提交）
                    noticePersonList: [], // 人员列表
                    selectDeptList: [] // 部门 人员归类（不提交）
                  }
                ]
                this.$refs.importantInfo.generationContents.forEach((item) => {
                  if (item.noticeType === '0') {
                    item.departCodes = item.departCode ? item.departCode.split(',') : []
                    let arr = JSON.parse(item.noticePersonStr)
                    item.noticePersonList = []
                    if (arr && arr.length) {
                      arr.forEach((el) => {
                        item.noticePersonList = item.noticePersonList.concat(el.userList)
                      })
                    }
                    item.selectDeptList = JSON.parse(item.noticePersonStr)
                  }
                })
                this.$refs.importantInfo.generationSureContents.forEach((item) => {
                  if (item.noticeType === '0') {
                    item.departCodes = item.departCode ? item.departCode.split(',') : []
                    let arr = JSON.parse(item.noticePersonStr)
                    item.noticePersonList = []
                    if (arr && arr.length) {
                      arr.forEach((el) => {
                        item.noticePersonList = item.noticePersonList.concat(el.userList)
                      })
                    }
                    item.selectDeptList = JSON.parse(item.noticePersonStr)
                  }
                })
              }
              this.$refs.importantInfo.formInline.webAlarmResponseWay = importantObj?.webAlarmResponseWay?.split(',') ?? []
              this.$refs.importantInfo.formInline.webAlarmResponseNum = importantObj.webAlarmResponseNum // 声光报警次数
              this.$refs.importantInfo.formInline.autoSendOrder = importantObj.autoSendOrder
              this.$refs.importantInfo.formInline.workOrderName = importantObj.workOrderName
              this.$refs.importantInfo.formInline.workOrderCodes = importantObj.workOrderCode
              this.$refs.importantInfo.formInline.teamCode = importantObj.teamCode
              this.$refs.importantInfo.formInline.teamName = importantObj.teamName
              this.$refs.importantInfo.formInline.workTypeCode = importantObj.workTypeCode
              if (importantObj.workTypeCode == '6' || importantObj.workTypeCode == '3') {
                this.$refs.importantInfo.formInline.workOrderCode = importantObj.workOrderCode
              } else {
                this.$refs.importantInfo.formInline.workOrderCode = importantObj.workOrderCode ? importantObj.workOrderCode.split(',') : []
              }
              let arr = this.$refs.importantInfo.formInline.workOrderCode
              if (arr && arr.length) {
                if (importantObj.workTypeCode == '6' || importantObj.workTypeCode == '3') {
                  this.$refs.importantInfo.getTeamsByWorkTypeCode()
                  this.$refs.importantInfo.getWorkMatter(importantObj.workTypeCode)
                } else {
                  this.$refs.importantInfo.getTeamsByWorkTypeCode(arr[0])
                  this.$refs.importantInfo.getItemTreeData(importantObj.workTypeCode)
                }
              }
            })
          }
          // 紧急回显
          if (this.exigencyShow) {
            // 紧要2
            this.$nextTick(() => {
              let exigencyObj = res.data.find((item) => item.alarmLevel === '2')
              if (exigencyObj.alarmModel === '0') {
                this.$refs.exigencyInfo.formInline.alarmModel = true
              } else {
                this.$refs.exigencyInfo.formInline.alarmModel = false
              }
              if (exigencyObj.upgraded === '0') {
                this.$refs.exigencyInfo.formInline.upgraded = true
              } else {
                this.$refs.exigencyInfo.formInline.upgraded = false
              }
              this.$refs.exigencyInfo.formInline.alarmWaitTime = exigencyObj.alarmWaitTime // 报警等待时间
              this.$refs.exigencyInfo.formInline.alarmWaitTimeUnit = exigencyObj.alarmWaitTimeUnit // 报警升级时间单位
              this.$refs.exigencyInfo.formInline.alarmUpgradedLevel = exigencyObj.alarmUpgradedLevel // 报警升级级别
              this.$refs.exigencyInfo.formInline.disposalErminal = exigencyObj.disposalErminal.split(',') // 处置终端，0-专业客户端，1-手机端，2-web端
              this.$refs.exigencyInfo.formInline.id = exigencyObj.id // 编辑
              if (exigencyObj.alarmModel === '0') {
                this.$refs.exigencyInfo.formInline.alarmResponseWay = exigencyObj.alarmResponseWay.split(',')
                this.$refs.exigencyInfo.formInline.alarmResponseNum = exigencyObj.alarmResponseNum // 声光报警次数
                this.$refs.exigencyInfo.formInline.preplanId = exigencyObj.preplanId
                this.$refs.exigencyInfo.formInline.preplanName = exigencyObj.preplanName
                this.$refs.exigencyInfo.groupTableData = exigencyObj.emergencys || [] // 应急队伍
                this.$refs.exigencyInfo.formInline.emergencyTeamId = exigencyObj.emergencyTeamId
              } else {
                this.$refs.exigencyInfo.formInline.alarmSecureWay = exigencyObj.alarmSecureWay.split(',')
              }
              if (exigencyObj.preplanId) {
                this.$refs.exigencyInfo.generationContents = exigencyObj.occurAlarmNotices || [
                  {
                    alarmConfigType: '0', // 报警处理方式，0-报警产生时，1-确警时
                    noticeWay: '0', // 通知方式，0-短信，1-app，2-企业微信，3-微信服务号，4-语音电话
                    noticeType: '1', // 0-部门，1-人员
                    personId: '', // 人员编码
                    personName: '', // 人员名称
                    tel: '', // 联系电话
                    departName: '', // 部门名称
                    departCodes: [], // 部门code
                    departCode: '', // 部门code（逗号分割提交）
                    noticePersonList: [], // 人员列表
                    selectDeptList: [] // 部门 人员归类（不提交）
                  }
                ]
                this.$refs.exigencyInfo.generationContents.forEach((item) => {
                  if (item.noticeType === '0') {
                    item.departCodes = item.departCode ? item.departCode.split(',') : []
                    let arr = JSON.parse(item.noticePersonStr)
                    item.noticePersonList = []
                    if (arr && arr.length) {
                      arr.forEach((el) => {
                        item.noticePersonList = item.noticePersonList.concat(el.userList)
                      })
                    }
                    item.selectDeptList = JSON.parse(item.noticePersonStr)
                  }
                })
              } else {
                this.$refs.exigencyInfo.generationContents = exigencyObj.occurAlarmNotices || [
                  {
                    alarmConfigType: '0', // 报警处理方式，0-报警产生时，1-确警时
                    noticeWay: '0', // 通知方式，0-短信，1-app，2-企业微信，3-微信服务号，4-语音电话
                    noticeType: '1', // 0-部门，1-人员
                    personId: '', // 人员编码
                    personName: '', // 人员名称
                    tel: '', // 联系电话
                    departName: '', // 部门名称
                    departCodes: [], // 部门code
                    departCode: '', // 部门code（逗号分割提交）
                    noticePersonList: [], // 人员列表
                    selectDeptList: [] // 部门 人员归类（不提交）
                  }
                ]
                this.$refs.exigencyInfo.generationSureContents = exigencyObj.confirmedAlarmNotices || [
                  {
                    alarmConfigType: '1', // 报警处理方式，0-报警产生时，1-确警时
                    noticeWay: '0', // 通知方式，0-短信，1-app，2-企业微信，3-微信服务号，4-语音电话
                    noticeType: '1', // 0-部门，1-人员
                    personId: '', // 人员编码
                    personName: '', // 人员名称
                    tel: '', // 联系电话
                    departName: '', // 部门名称
                    departCodes: [], // 部门code
                    departCode: '', // 部门code（逗号分割提交）
                    noticePersonList: [], // 人员列表
                    selectDeptList: [] // 部门 人员归类（不提交）
                  }
                ]
                this.$refs.exigencyInfo.generationContents.forEach((item) => {
                  if (item.noticeType === '0') {
                    item.departCodes = item.departCode ? item.departCode.split(',') : []
                    let arr = JSON.parse(item.noticePersonStr)
                    item.noticePersonList = []
                    if (arr && arr.length) {
                      arr.forEach((el) => {
                        item.noticePersonList = item.noticePersonList.concat(el.userList)
                      })
                    }
                    item.selectDeptList = JSON.parse(item.noticePersonStr)
                  }
                })
                this.$refs.exigencyInfo.generationSureContents.forEach((item) => {
                  if (item.noticeType === '0') {
                    item.departCodes = item.departCode ? item.departCode.split(',') : []
                    let arr = JSON.parse(item.noticePersonStr)
                    item.noticePersonList = []
                    if (arr && arr.length) {
                      arr.forEach((el) => {
                        item.noticePersonList = item.noticePersonList.concat(el.userList)
                      })
                    }
                    item.selectDeptList = JSON.parse(item.noticePersonStr)
                  }
                })
              }
              this.$refs.exigencyInfo.formInline.webAlarmResponseWay = exigencyObj?.webAlarmResponseWay?.split(',') ?? []
              this.$refs.exigencyInfo.formInline.webAlarmResponseNum = exigencyObj.webAlarmResponseNum // 声光报警次数
              this.$refs.exigencyInfo.formInline.autoSendOrder = exigencyObj.autoSendOrder
              this.$refs.exigencyInfo.formInline.workOrderName = exigencyObj.workOrderName
              this.$refs.exigencyInfo.formInline.workOrderCodes = exigencyObj.workOrderCode
              this.$refs.exigencyInfo.formInline.teamCode = exigencyObj.teamCode
              this.$refs.exigencyInfo.formInline.teamName = exigencyObj.teamName
              if (exigencyObj.workTypeCode == '6' || exigencyObj.workTypeCode == '3') {
                this.$refs.exigencyInfo.formInline.workOrderCode = exigencyObj.workOrderCode
                this.$refs.exigencyInfo.getWorkMatter(exigencyObj.workTypeCode)
              } else {
                this.$refs.exigencyInfo.formInline.workOrderCode = exigencyObj.workOrderCode ? exigencyObj.workOrderCode.split(',') : []
                this.$refs.exigencyInfo.getItemTreeData(exigencyObj.workTypeCode)
              }
              this.$refs.exigencyInfo.formInline.workTypeCode = exigencyObj.workTypeCode
              let arr = this.$refs.exigencyInfo.formInline.workOrderCode
              if (arr && arr.length) {
                if (exigencyObj.workTypeCode == '6' || exigencyObj.workTypeCode == '3') {
                  this.$refs.exigencyInfo.getTeamsByWorkTypeCode()
                } else {
                  this.$refs.exigencyInfo.getTeamsByWorkTypeCode(arr[0])
                }
              }
            })
          }
          // 一般回显
          if (this.normalShow) {
            // 一般1
            this.$nextTick(() => {
              let normalObj = res.data.find((item) => item.alarmLevel === '1')
              if (normalObj.alarmModel === '0') {
                this.$refs.normalInfo.formInline.alarmModel = true
              } else {
                this.$refs.normalInfo.formInline.alarmModel = false
              }
              if (normalObj.upgraded === '0') {
                this.$refs.normalInfo.formInline.upgraded = true
              } else {
                this.$refs.normalInfo.formInline.upgraded = false
              }
              this.$refs.normalInfo.formInline.alarmWaitTime = normalObj.alarmWaitTime // 报警等待时间
              this.$refs.normalInfo.formInline.alarmWaitTimeUnit = normalObj.alarmWaitTimeUnit // 报警升级时间单位
              this.$refs.normalInfo.formInline.alarmUpgradedLevel = normalObj.alarmUpgradedLevel // 报警升级级别
              this.$refs.normalInfo.formInline.disposalErminal = normalObj.disposalErminal.split(',') // 处置终端，0-专业客户端，1-手机端，2-web端
              this.$refs.normalInfo.formInline.id = normalObj.id // 编辑
              if (normalObj.alarmModel === '0') {
                this.$refs.normalInfo.formInline.alarmResponseWay = normalObj.alarmResponseWay.split(',')
                this.$refs.normalInfo.formInline.alarmResponseNum = normalObj.alarmResponseNum // 声光报警次数
                this.$refs.normalInfo.formInline.preplanId = normalObj.preplanId
                this.$refs.normalInfo.formInline.preplanName = normalObj.preplanName
                this.$refs.normalInfo.groupTableData = normalObj.emergencys || [] // 应急队伍
                this.$refs.normalInfo.formInline.emergencyTeamId = normalObj.emergencyTeamId
              } else {
                this.$refs.normalInfo.formInline.alarmSecureWay = normalObj.alarmSecureWay.split(',')
              }
              if (normalObj.preplanId) {
                this.$refs.normalInfo.generationContents = normalObj.occurAlarmNotices || [
                  {
                    alarmConfigType: '0', // 报警处理方式，0-报警产生时，1-确警时
                    noticeWay: '0', // 通知方式，0-短信，1-app，2-企业微信，3-微信服务号，4-语音电话
                    noticeType: '1', // 0-部门，1-人员
                    personId: '', // 人员编码
                    personName: '', // 人员名称
                    tel: '', // 联系电话
                    departName: '', // 部门名称
                    departCodes: [], // 部门code
                    departCode: '', // 部门code（逗号分割提交）
                    noticePersonList: [], // 人员列表
                    selectDeptList: [] // 部门 人员归类（不提交）
                  }
                ]
                this.$refs.normalInfo.generationContents.forEach((item) => {
                  if (item.noticeType === '0') {
                    item.departCodes = item.departCode ? item.departCode.split(',') : []
                    let arr = JSON.parse(item.noticePersonStr)
                    item.noticePersonList = []
                    if (arr && arr.length) {
                      arr.forEach((el) => {
                        item.noticePersonList = item.noticePersonList.concat(el.userList)
                      })
                    }
                    item.selectDeptList = JSON.parse(item.noticePersonStr)
                  }
                })
              } else {
                this.$refs.normalInfo.generationContents = normalObj.occurAlarmNotices || [
                  {
                    alarmConfigType: '0', // 报警处理方式，0-报警产生时，1-确警时
                    noticeWay: '0', // 通知方式，0-短信，1-app，2-企业微信，3-微信服务号，4-语音电话
                    noticeType: '1', // 0-部门，1-人员
                    personId: '', // 人员编码
                    personName: '', // 人员名称
                    tel: '', // 联系电话
                    departName: '', // 部门名称
                    departCodes: [], // 部门code
                    departCode: '', // 部门code（逗号分割提交）
                    noticePersonList: [], // 人员列表
                    selectDeptList: [] // 部门 人员归类（不提交）
                  }
                ]
                this.$refs.normalInfo.generationSureContents = normalObj.confirmedAlarmNotices || [
                  {
                    alarmConfigType: '1', // 报警处理方式，0-报警产生时，1-确警时
                    noticeWay: '0', // 通知方式，0-短信，1-app，2-企业微信，3-微信服务号，4-语音电话
                    noticeType: '1', // 0-部门，1-人员
                    personId: '', // 人员编码
                    personName: '', // 人员名称
                    tel: '', // 联系电话
                    departName: '', // 部门名称
                    departCodes: [], // 部门code
                    departCode: '', // 部门code（逗号分割提交）
                    noticePersonList: [], // 人员列表
                    selectDeptList: [] // 部门 人员归类（不提交）
                  }
                ]
                this.$refs.normalInfo.generationContents.forEach((item) => {
                  if (item.noticeType === '0') {
                    item.departCodes = item.departCode ? item.departCode.split(',') : []
                    let arr = JSON.parse(item.noticePersonStr)
                    item.noticePersonList = []
                    if (arr && arr.length) {
                      arr.forEach((el) => {
                        item.noticePersonList = item.noticePersonList.concat(el.userList)
                      })
                    }
                    item.selectDeptList = JSON.parse(item.noticePersonStr)
                  }
                })
                this.$refs.normalInfo.generationSureContents.forEach((item) => {
                  if (item.noticeType === '0') {
                    item.departCodes = item.departCode ? item.departCode.split(',') : []
                    let arr = JSON.parse(item.noticePersonStr)
                    item.noticePersonList = []
                    if (arr && arr.length) {
                      arr.forEach((el) => {
                        item.noticePersonList = item.noticePersonList.concat(el.userList)
                      })
                    }
                    item.selectDeptList = JSON.parse(item.noticePersonStr)
                  }
                })
              }
              this.$refs.normalInfo.formInline.webAlarmResponseWay = normalObj?.webAlarmResponseWay?.split(',') ?? []
              this.$refs.normalInfo.formInline.webAlarmResponseNum = normalObj.webAlarmResponseNum // 声光报警次数
              this.$refs.normalInfo.formInline.autoSendOrder = normalObj.autoSendOrder
              this.$refs.normalInfo.formInline.workOrderName = normalObj.workOrderName
              this.$refs.normalInfo.formInline.workOrderCodes = normalObj.workOrderCode
              this.$refs.normalInfo.formInline.teamCode = normalObj.teamCode
              this.$refs.normalInfo.formInline.teamName = normalObj.teamName
              if (normalObj.workTypeCode == '6' || normalObj.workTypeCode == '3') {
                this.$refs.normalInfo.formInline.workOrderCode = normalObj.workOrderCode
                this.$refs.normalInfo.getWorkMatter(normalObj.workTypeCode)
              } else {
                this.$refs.normalInfo.formInline.workOrderCode = normalObj.workOrderCode ? normalObj.workOrderCode.split(',') : []
                this.$refs.normalInfo.getItemTreeData(normalObj.workTypeCode)
              }
              this.$refs.normalInfo.formInline.workTypeCode = normalObj.workTypeCode
              let arr = this.$refs.normalInfo.formInline.workOrderCode
              if (arr && arr.length) {
                if (normalObj.workTypeCode == '6' || normalObj.workTypeCode == '3') {
                  this.$refs.normalInfo.getTeamsByWorkTypeCode()
                } else {
                  this.$refs.normalInfo.getTeamsByWorkTypeCode(arr[0])
                }
              }
            })
          }
          // 通知回显
          if (this.informShow) {
            // 通知4
            this.$nextTick(() => {
              let informObj = res.data.find((item) => item.alarmLevel === '0')
              if (informObj.alarmModel === '0') {
                this.$refs.informInfo.formInline.alarmModel = true
              } else {
                this.$refs.informInfo.formInline.alarmModel = false
              }
              if (informObj.upgraded === '0') {
                this.$refs.informInfo.formInline.upgraded = true
              } else {
                this.$refs.informInfo.formInline.upgraded = false
              }
              this.$refs.informInfo.formInline.alarmWaitTime = informObj.alarmWaitTime // 报警等待时间
              this.$refs.informInfo.formInline.alarmWaitTimeUnit = informObj.alarmWaitTimeUnit // 报警升级时间单位
              this.$refs.informInfo.formInline.alarmUpgradedLevel = informObj.alarmUpgradedLevel // 报警升级级别
              this.$refs.informInfo.formInline.disposalErminal = informObj.disposalErminal.split(',') // 处置终端，0-专业客户端，1-手机端，2-web端
              this.$refs.informInfo.formInline.id = informObj.id // 编辑
              if (informObj.alarmModel === '0') {
                this.$refs.informInfo.formInline.alarmResponseWay = informObj.alarmResponseWay.split(',')
                this.$refs.informInfo.formInline.alarmResponseNum = informObj.alarmResponseNum // 声光报警次数
                this.$refs.informInfo.formInline.preplanId = informObj.preplanId
                this.$refs.informInfo.formInline.preplanName = informObj.preplanName
                this.$refs.informInfo.groupTableData = informObj.emergencys || [] // 应急队伍
                this.$refs.informInfo.formInline.emergencyTeamId = informObj.emergencyTeamId
              } else {
                this.$refs.informInfo.formInline.alarmSecureWay = informObj.alarmSecureWay.split(',')
              }
              if (informObj.preplanId) {
                this.$refs.informInfo.generationContents = informObj.occurAlarmNotices || [
                  {
                    alarmConfigType: '0', // 报警处理方式，0-报警产生时，1-确警时
                    noticeWay: '0', // 通知方式，0-短信，1-app，2-企业微信，3-微信服务号，4-语音电话
                    noticeType: '1', // 0-部门，1-人员
                    personId: '', // 人员编码
                    personName: '', // 人员名称
                    tel: '', // 联系电话
                    departName: '', // 部门名称
                    departCodes: [], // 部门code
                    departCode: '', // 部门code（逗号分割提交）
                    noticePersonList: [], // 人员列表
                    selectDeptList: [] // 部门 人员归类（不提交）
                  }
                ]
                this.$refs.informInfo.generationContents.forEach((item) => {
                  if (item.noticeType === '0') {
                    item.departCodes = item.departCode ? item.departCode.split(',') : []
                    let arr = JSON.parse(item.noticePersonStr)
                    item.noticePersonList = []
                    if (arr && arr.length) {
                      arr.forEach((el) => {
                        item.noticePersonList = item.noticePersonList.concat(el.userList)
                      })
                    }
                    item.selectDeptList = JSON.parse(item.noticePersonStr)
                  }
                })
              } else {
                this.$refs.informInfo.generationContents = informObj.occurAlarmNotices || [
                  {
                    alarmConfigType: '0', // 报警处理方式，0-报警产生时，1-确警时
                    noticeWay: '0', // 通知方式，0-短信，1-app，2-企业微信，3-微信服务号，4-语音电话
                    noticeType: '1', // 0-部门，1-人员
                    personId: '', // 人员编码
                    personName: '', // 人员名称
                    tel: '', // 联系电话
                    departName: '', // 部门名称
                    departCodes: [], // 部门code
                    departCode: '', // 部门code（逗号分割提交）
                    noticePersonList: [], // 人员列表
                    selectDeptList: [] // 部门 人员归类（不提交）
                  }
                ]
                this.$refs.informInfo.generationSureContents = informObj.confirmedAlarmNotices || [
                  {
                    alarmConfigType: '1', // 报警处理方式，0-报警产生时，1-确警时
                    noticeWay: '0', // 通知方式，0-短信，1-app，2-企业微信，3-微信服务号，4-语音电话
                    noticeType: '1', // 0-部门，1-人员
                    personId: '', // 人员编码
                    personName: '', // 人员名称
                    tel: '', // 联系电话
                    departName: '', // 部门名称
                    departCodes: [], // 部门code
                    departCode: '', // 部门code（逗号分割提交）
                    noticePersonList: [], // 人员列表
                    selectDeptList: [] // 部门 人员归类（不提交）
                  }
                ]
                this.$refs.informInfo.generationContents.forEach((item) => {
                  if (item.noticeType === '0') {
                    item.departCodes = item.departCode ? item.departCode.split(',') : []
                    let arr = JSON.parse(item.noticePersonStr)
                    item.noticePersonList = []
                    if (arr && arr.length) {
                      arr.forEach((el) => {
                        item.noticePersonList = item.noticePersonList.concat(el.userList)
                      })
                    }
                    item.selectDeptList = JSON.parse(item.noticePersonStr)
                  }
                })
                this.$refs.informInfo.generationSureContents.forEach((item) => {
                  if (item.noticeType === '0') {
                    item.departCodes = item.departCode ? item.departCode.split(',') : []
                    let arr = JSON.parse(item.noticePersonStr)
                    item.noticePersonList = []
                    if (arr && arr.length) {
                      arr.forEach((el) => {
                        item.noticePersonList = item.noticePersonList.concat(el.userList)
                      })
                    }
                    item.selectDeptList = JSON.parse(item.noticePersonStr)
                  }
                })
              }
              this.$refs.informInfo.formInline.webAlarmResponseWay = informObj?.webAlarmResponseWay?.split(',') ?? []
              this.$refs.informInfo.formInline.webAlarmResponseNum = informObj.webAlarmResponseNum // 声光报警次数
              this.$refs.informInfo.formInline.autoSendOrder = informObj.autoSendOrder
              this.$refs.informInfo.formInline.workOrderName = informObj.workOrderName
              this.$refs.informInfo.formInline.workOrderCodes = informObj.workOrderCode
              this.$refs.informInfo.formInline.teamCode = informObj.teamCode
              this.$refs.informInfo.formInline.teamName = informObj.teamName
              if (informObj.workTypeCode == '6' || informObj.workTypeCode == '3') {
                this.$refs.informInfo.formInline.workOrderCode = informObj.workOrderCode
                this.$refs.informInfo.getWorkMatter(informObj.workTypeCode)
              } else {
                this.$refs.informInfo.formInline.workOrderCode = informObj.workOrderCode ? informObj.workOrderCode.split(',') : []
                this.$refs.informInfo.getItemTreeData(informObj.workTypeCode)
              }
              this.$refs.informInfo.formInline.workTypeCode = informObj.workTypeCode
              let arr = this.$refs.informInfo.formInline.workOrderCode
              if (arr && arr.length) {
                if (informObj.workTypeCode == '6' || informObj.workTypeCode == '3') {
                  this.$refs.informInfo.getTeamsByWorkTypeCode()
                } else {
                  this.$refs.informInfo.getTeamsByWorkTypeCode(arr[0])
                }
              }
            })
          }
        } else if (res.message) {
          this.$message.error(res.message)
        }
      })
    },
    // 报警系统
    getAlarmSystemList() {
      this.$api.getAlarmThirdSystemData().then((res) => {
        if (res.code == 200) {
          this.alarmSystemList = res.data
        }
      })
    },
    // 获取实体类型
    getAlarmTypeData(thirdSystemCode) {
      let params = {
        thirdSystemCode: thirdSystemCode
      }
      this.$api.getAlarmThirdTypeData(params).then((res) => {
        if (res.code == 200) {
          this.alarmTypeList = res.data
        }
      })
    },
    // 报警系统改变
    alarmSystemChange(el) {
      this.addForm.alarmSystemName = this.alarmSystemList.find((ele) => ele.thirdSystemCode === el).thirdSystemName
      this.$set(this.addForm, 'alarmTypeCode', '')
      this.$set(this.addForm, 'alarmTypeName', '')
      this.getAlarmTypeData(el)
      this.projectCode = el
    },
    // 报警类型改变
    alarmTypeChange(el) {
      this.addForm.alarmTypeName = this.alarmTypeList.find((ele) => ele.id === el).alarmDictName
    },
    scrollHandler(event) {
      var scrollTop = event.target.scrollTop
      if (scrollTop < 600) {
        this.btnType = '0'
      } else if (scrollTop < 1600) {
        this.btnType = '1'
      } else if (scrollTop < 2500) {
        this.btnType = '2'
      } else if (scrollTop > 2500) {
        this.btnType = '3'
      }
    },
    // 锚点定位
    anchor(e) {
      this.btnType = e
      if (this.btnType === '0') {
        var target = document.getElementById('importantBox')
        console.log(target.offsetTop - 80, 'dassssssssss')
        target.parentNode.scrollTop = target.offsetTop - 80
      } else if (this.btnType === '1') {
        var target = document.getElementById('exigencyBox')
        target.parentNode.scrollTop = target.offsetTop - 80
      } else if (this.btnType === '2') {
        var target = document.getElementById('normalBox')
        target.parentNode.scrollTop = target.offsetTop - 80
      } else {
        var target = document.getElementById('informBox')
        target.parentNode.scrollTop = target.offsetTop - 80
      }
    },
    // 等级change
    gradeChange(val) {
      if (val && val.length) {
        let str = val.join(',')
        if (str.indexOf('3') !== -1) {
          this.importantShow = true
        } else {
          this.importantShow = false
        }
        if (str.indexOf('2') !== -1) {
          this.exigencyShow = true
        } else {
          this.exigencyShow = false
        }
        if (str.indexOf('1') !== -1) {
          this.normalShow = true
        } else {
          this.normalShow = false
        }
        if (str.indexOf('0') !== -1) {
          this.informShow = true
        } else {
          this.informShow = false
        }
      } else {
        this.importantShow = false
        this.exigencyShow = false
        this.normalShow = false
        this.informShow = false
      }
    },
    getFormPromise(form) {
      return new Promise((resolve) => {
        form.validate((res) => {
          resolve(res)
        })
      })
    },
    submitForm() {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          const baseId = this.$route.query.baseId
          var paramsData = {
            alarmConfigs: []
          }
          var rulesForm = []
          if (this.importantShow) {
            const importantForm = this.$refs.importantInfo.$refs.formInline
            rulesForm.push(importantForm)
            // 重要
            let importantObj = {
              alarmSystemCode: this.addForm.alarmSystemCode, // 报警系统
              alarmSystemName: this.addForm.alarmSystemName, // 报警系统名称
              alarmTypeCode: this.addForm.alarmTypeCode, // 报警类型
              alarmTypeName: this.addForm.alarmTypeName, // 报警类型名称
              alarmLevel: '3', // 报警等级
              alarmModel: this.$refs.importantInfo.formInline.alarmModel ? '0' : '1', // 是否弹出警单
              disposalErminal: this.$refs.importantInfo.formInline.disposalErminal.join(','), // 处置终端
              autoSendOrder: this.$refs.importantInfo.formInline.autoSendOrder, // 派发工单
              workOrderCode: this.$refs.importantInfo.formInline.workOrderCodes || null, // 工单事项
              workOrderName: this.$refs.importantInfo.formInline.workOrderName || null, // 工单事项名称
              workTypeCode: this.$refs.importantInfo.formInline.workTypeCode, // 工单类型
              teamCode: this.$refs.importantInfo.formInline.teamCode || null, // 班组code
              teamName: this.$refs.importantInfo.formInline.teamName || null, // 班组名称
              id: baseId ? this.$refs.importantInfo.formInline.id : '', // id
              notices: [],
              upgraded: this.$refs.importantInfo.formInline.upgraded ? '0' : '1', // 报警是否升级
              alarmWaitTime: this.$refs.importantInfo.formInline.alarmWaitTime, // 报警等待时间
              alarmWaitTimeUnit: this.$refs.importantInfo.formInline.alarmWaitTimeUnit, // 报警升级时间单位
              alarmUpgradedLevel: this.$refs.importantInfo.formInline.alarmUpgradedLevel, // 报警升级级别
              webAlarmResponseWay: this.$refs.importantInfo.formInline.webAlarmResponseWay.join(',') || null, // 响应方式
              webAlarmResponseNum: this.$refs.importantInfo.formInline.webAlarmResponseNum || null // 声光报警次数
            }
            if (importantObj.alarmModel === '0') {
              importantObj.alarmResponseWay = this.$refs.importantInfo.formInline.alarmResponseWay.join(',') || null // 响应方式
              importantObj.alarmResponseNum = this.$refs.importantInfo.formInline.alarmResponseNum || null // 声光报警次数
              if (this.$refs.importantInfo.formInline.emergencyTeamId == '' || !this.$refs.importantInfo.formInline.emergencyTeamId) {
                this.$message.error('应急队伍不能为空')
                return
              }
              importantObj.preplanId = this.$refs.importantInfo.formInline.preplanId || null // 预案id
              importantObj.preplanName = this.$refs.importantInfo.formInline.preplanName || null // 预案name
              importantObj.emergencyTeamId = this.$refs.importantInfo.formInline.emergencyTeamId || null // 应急队伍
            } else {
              importantObj.alarmSecureWay = this.$refs.importantInfo.formInline.alarmSecureWay.join(',') || null // 解除方式
            }
            if (importantObj.preplanId) {
              let arr = cloneDeep(this.$refs.importantInfo.generationContents)
              arr.forEach((item) => {
                delete item.departCodes
                delete item.noticePersonList
                item.noticePersonStr = JSON.stringify(item.selectDeptList)
                delete item.selectDeptList
              })
              importantObj.notices = arr
            } else {
              let arr1 = cloneDeep(this.$refs.importantInfo.generationContents.concat(this.$refs.importantInfo.generationSureContents))
              arr1.forEach((item) => {
                delete item.departCodes
                delete item.noticePersonList
                item.noticePersonStr = JSON.stringify(item.selectDeptList)
                delete item.selectDeptList
              })
              importantObj.notices = arr1
            }
            paramsData.alarmConfigs.push(importantObj)
          }
          if (this.exigencyShow) {
            const exigencyForm = this.$refs.exigencyInfo.$refs.formInline
            rulesForm.push(exigencyForm)
            // 紧急
            let exigencyObj = {
              alarmSystemCode: this.addForm.alarmSystemCode, // 报警系统
              alarmSystemName: this.addForm.alarmSystemName, // 报警系统名称
              alarmTypeCode: this.addForm.alarmTypeCode, // 报警类型
              alarmTypeName: this.addForm.alarmTypeName, // 报警类型名称
              alarmLevel: '2', // 报警等级
              alarmModel: this.$refs.exigencyInfo.formInline.alarmModel ? '0' : '1', // 是否弹出警单
              disposalErminal: this.$refs.exigencyInfo.formInline.disposalErminal.join(','), // 处置终端
              autoSendOrder: this.$refs.exigencyInfo.formInline.autoSendOrder || null, // 派发工单
              workOrderCode: this.$refs.exigencyInfo.formInline.workOrderCodes || null, // 工单事项
              workOrderName: this.$refs.exigencyInfo.formInline.workOrderName || null, // 工单事项名称
              workTypeCode: this.$refs.exigencyInfo.formInline.workTypeCode, // 工单类型
              teamCode: this.$refs.exigencyInfo.formInline.teamCode || null, // 班组code
              teamName: this.$refs.exigencyInfo.formInline.teamName || null, // 班组名称
              notices: [],
              id: baseId ? this.$refs.exigencyInfo.formInline.id : '', // id
              upgraded: this.$refs.exigencyInfo.formInline.upgraded ? '0' : '1', // 报警是否升级
              alarmWaitTime: this.$refs.exigencyInfo.formInline.alarmWaitTime, // 报警等待时间
              alarmWaitTimeUnit: this.$refs.exigencyInfo.formInline.alarmWaitTimeUnit, // 报警升级时间单位
              alarmUpgradedLevel: this.$refs.exigencyInfo.formInline.alarmUpgradedLevel, // 报警升级级别
              webAlarmResponseWay: this.$refs.exigencyInfo.formInline.webAlarmResponseWay.join(',') || null, // 响应方式
              webAlarmResponseNum: this.$refs.exigencyInfo.formInline.webAlarmResponseNum || null // 声光报警次数
            }
            if (exigencyObj.alarmModel === '0') {
              exigencyObj.alarmResponseWay = this.$refs.exigencyInfo.formInline.alarmResponseWay.join(',') // 响应方式
              exigencyObj.alarmResponseNum = this.$refs.exigencyInfo.formInline.alarmResponseNum || null // 声光报警次数
              if (this.$refs.exigencyInfo.formInline.emergencyTeamId == '' || !this.$refs.exigencyInfo.formInline.emergencyTeamId) {
                this.$message.error('应急队伍不能为空')
                return
              }
              exigencyObj.preplanId = this.$refs.exigencyInfo.formInline.preplanId || null // 预案id
              exigencyObj.preplanName = this.$refs.exigencyInfo.formInline.preplanName || null // 预案name
              exigencyObj.emergencyTeamId = this.$refs.exigencyInfo.formInline.emergencyTeamId || null // 应急队伍
            } else {
              exigencyObj.alarmSecureWay = this.$refs.exigencyInfo.formInline.alarmSecureWay.join(',') // 解除方式
            }
            if (exigencyObj.preplanId) {
              let arr = cloneDeep(this.$refs.exigencyInfo.generationContents)
              arr.forEach((item) => {
                delete item.departCodes
                delete item.noticePersonList
                item.noticePersonStr = JSON.stringify(item.selectDeptList)
                delete item.selectDeptList
              })
              exigencyObj.notices = arr
            } else {
              let arr1 = cloneDeep(this.$refs.exigencyInfo.generationContents.concat(this.$refs.exigencyInfo.generationSureContents))
              arr1.forEach((item) => {
                delete item.departCodes
                delete item.noticePersonList
                item.noticePersonStr = JSON.stringify(item.selectDeptList)
                delete item.selectDeptList
              })
              exigencyObj.notices = arr1
            }
            paramsData.alarmConfigs.push(exigencyObj)
          }
          if (this.normalShow) {
            const normalForm = this.$refs.normalInfo.$refs.formInline
            rulesForm.push(normalForm)
            // 一般
            let normalObj = {
              alarmSystemCode: this.addForm.alarmSystemCode, // 报警系统
              alarmSystemName: this.addForm.alarmSystemName, // 报警系统名称
              alarmTypeCode: this.addForm.alarmTypeCode, // 报警类型
              alarmTypeName: this.addForm.alarmTypeName, // 报警类型名称
              alarmLevel: '1', // 报警等级
              alarmModel: this.$refs.normalInfo.formInline.alarmModel ? '0' : '1', // 是否弹出警单
              disposalErminal: this.$refs.normalInfo.formInline.disposalErminal.join(','), // 处置终端
              autoSendOrder: this.$refs.normalInfo.formInline.autoSendOrder || null, // 派发工单
              workOrderCode: this.$refs.normalInfo.formInline.workOrderCodes || null, // 工单事项
              workOrderName: this.$refs.normalInfo.formInline.workOrderName || null, // 工单事项名称
              workTypeCode: this.$refs.normalInfo.formInline.workTypeCode, // 工单类型
              teamCode: this.$refs.normalInfo.formInline.teamCode || null, // 班组code
              teamName: this.$refs.normalInfo.formInline.teamName || null, // 班组名称
              notices: [],
              id: baseId ? this.$refs.normalInfo.formInline.id : '', // id
              upgraded: this.$refs.normalInfo.formInline.upgraded ? '0' : '1', // 报警是否升级
              alarmWaitTime: this.$refs.normalInfo.formInline.alarmWaitTime, // 报警等待时间
              alarmWaitTimeUnit: this.$refs.normalInfo.formInline.alarmWaitTimeUnit, // 报警升级时间单位
              alarmUpgradedLevel: this.$refs.normalInfo.formInline.alarmUpgradedLevel, // 报警升级级别
              webAlarmResponseWay: this.$refs.normalInfo.formInline.webAlarmResponseWay.join(',') || null, // 响应方式
              webAlarmResponseNum: this.$refs.normalInfo.formInline.webAlarmResponseNum || null // 声光报警次数
            }
            if (normalObj.alarmModel === '0') {
              normalObj.alarmResponseWay = this.$refs.normalInfo.formInline.alarmResponseWay.join(',') // 响应方式
              normalObj.alarmResponseNum = this.$refs.normalInfo.formInline.alarmResponseNum || null // 声光报警次数
              if (this.$refs.normalInfo.formInline.emergencyTeamId == '' || !this.$refs.normalInfo.formInline.emergencyTeamId) {
                this.$message.error('应急队伍不能为空')
                return
              }
              normalObj.preplanId = this.$refs.normalInfo.formInline.preplanId || null // 预案id
              normalObj.preplanName = this.$refs.normalInfo.formInline.preplanName || null // 预案name
              normalObj.emergencyTeamId = this.$refs.normalInfo.formInline.emergencyTeamId || null // 应急队伍
            } else {
              normalObj.alarmSecureWay = this.$refs.normalInfo.formInline.alarmSecureWay.join(',') // 解除方式
            }
            if (normalObj.preplanId) {
              let arr = cloneDeep(this.$refs.normalInfo.generationContents)
              arr.forEach((item) => {
                delete item.departCodes
                delete item.noticePersonList
                item.noticePersonStr = JSON.stringify(item.selectDeptList)
                delete item.selectDeptList
              })
              normalObj.notices = arr
            } else {
              let arr1 = cloneDeep(this.$refs.normalInfo.generationContents.concat(this.$refs.normalInfo.generationSureContents))
              arr1.forEach((item) => {
                delete item.departCodes
                delete item.noticePersonList
                item.noticePersonStr = JSON.stringify(item.selectDeptList)
                delete item.selectDeptList
              })
              normalObj.notices = arr1
            }
            paramsData.alarmConfigs.push(normalObj)
          }
          if (this.informShow) {
            const informForm = this.$refs.informInfo.$refs.formInline
            rulesForm.push(informForm)
            // 通知
            let informObj = {
              alarmSystemCode: this.addForm.alarmSystemCode, // 报警系统
              alarmSystemName: this.addForm.alarmSystemName, // 报警系统名称
              alarmTypeCode: this.addForm.alarmTypeCode, // 报警类型
              alarmTypeName: this.addForm.alarmTypeName, // 报警类型名称
              alarmLevel: '0', // 报警等级
              alarmModel: this.$refs.informInfo.formInline.alarmModel ? '0' : '1', // 是否弹出警单
              disposalErminal: this.$refs.informInfo.formInline.disposalErminal.join(','), // 处置终端
              autoSendOrder: this.$refs.informInfo.formInline.autoSendOrder, // 派发工单
              workOrderCode: this.$refs.informInfo.formInline.workOrderCodes || null, // 工单事项
              workOrderName: this.$refs.informInfo.formInline.workOrderName || null, // 工单事项名称
              workTypeCode: this.$refs.informInfo.formInline.workTypeCode, // 工单类型
              teamCode: this.$refs.informInfo.formInline.teamCode || null, // 班组code
              teamName: this.$refs.informInfo.formInline.teamName || null, // 班组名称
              notices: [],
              id: baseId ? this.$refs.informInfo.formInline.id : '', // id
              upgraded: this.$refs.informInfo.formInline.upgraded ? '0' : '1', // 报警是否升级
              alarmWaitTime: this.$refs.informInfo.formInline.alarmWaitTime, // 报警等待时间
              alarmWaitTimeUnit: this.$refs.informInfo.formInline.alarmWaitTimeUnit, // 报警升级时间单位
              alarmUpgradedLevel: this.$refs.informInfo.formInline.alarmUpgradedLevel, // 报警升级级别
              webAlarmResponseWay: this.$refs.informInfo.formInline.webAlarmResponseWay.join(',') || null, // 响应方式
              webAlarmResponseNum: this.$refs.informInfo.formInline.webAlarmResponseNum || null // 声光报警次数
            }
            if (informObj.alarmModel === '0') {
              informObj.alarmResponseWay = this.$refs.informInfo.formInline.alarmResponseWay.join(',') // 响应方式
              informObj.alarmResponseNum = this.$refs.informInfo.formInline.alarmResponseNum || null // 声光报警次数
              if (this.$refs.informInfo.formInline.emergencyTeamId == '' || !this.$refs.informInfo.formInline.emergencyTeamId) {
                this.$message.error('应急队伍不能为空')
                return
              }
              informObj.preplanId = this.$refs.informInfo.formInline.preplanId || null // 预案id
              informObj.preplanName = this.$refs.informInfo.formInline.preplanName || null // 预案name
              informObj.emergencyTeamId = this.$refs.informInfo.formInline.emergencyTeamId || null // 应急队伍
            } else {
              informObj.alarmSecureWay = this.$refs.informInfo.formInline.alarmSecureWay.join(',') // 解除方式
            }
            if (informObj.preplanId) {
              let arr = cloneDeep(this.$refs.informInfo.generationContents)
              arr.forEach((item) => {
                delete item.departCodes
                delete item.noticePersonList
                item.noticePersonStr = JSON.stringify(item.selectDeptList)
                delete item.selectDeptList
              })
              informObj.notices = arr
            } else {
              let arr1 = cloneDeep(this.$refs.informInfo.generationContents.concat(this.$refs.informInfo.generationSureContents))
              arr1.forEach((item) => {
                delete item.departCodes
                delete item.noticePersonList
                item.noticePersonStr = JSON.stringify(item.selectDeptList)
                delete item.selectDeptList
              })
              informObj.notices = arr1
            }
            paramsData.alarmConfigs.push(informObj)
          }
          Promise.all(rulesForm.map(this.getFormPromise)).then((res) => {
            const validateResult = res.every((item) => !!item)
            if (validateResult) {
              if (baseId) {
                paramsData.alarmConfigs.forEach((item) => {
                  item.baseId = baseId
                })
                this.$api
                  .updateAlarmConfigList(paramsData)
                  .then((res) => {
                    if (res.code == 200) {
                      this.$message({
                        type: 'success',
                        message: '保存成功'
                      })
                      this.$router.go('-1')
                    } else {
                      this.$message({
                        type: 'error',
                        message: res.message || '保存失败'
                      })
                    }
                  })
                  .catch(() => {})
              } else {
                this.$api
                  .addAlarmConfigList(paramsData)
                  .then((res) => {
                    if (res.code == 200) {
                      this.$message({
                        type: 'success',
                        message: '保存成功'
                      })
                      this.$router.go('-1')
                    } else {
                      this.$message({
                        type: 'error',
                        message: res.message || '保存失败'
                      })
                    }
                  })
                  .catch(() => {})
              }
            } else {
              console.log('表单未校验通过')
            }
          })
        }
      })
    },
    // 返回
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>
<style lang="scss" scoped>
.container-content > div {
  background-color: #fff;
  height: 100%;
  position: relative;
  .addAlarmConfig-content-title {
    border-bottom: 1px solid #e4e7ed;
    padding: 13px 24px;
    cursor: pointer;
    color: #333333;
  }
  .content_box {
    height: calc(100% - 60px);
    display: flex;
    .content-title {
      font-weight: bold;
      font-size: 16px;
      color: #333333;
    }
    .contentBox-left {
      height: 100%;
      width: 90%;
      overflow-y: auto;
    }
    .contentBox-right {
      flex: 1;
      > div {
        cursor: pointer;
        padding: 8px;
        font-size: 16px;
        color: #1d2129;
      }
      .isLight {
        color: #3562db;
      }
      .lineBox {
        display: inline-block;
        width: 3px;
        height: 24px;
        border-radius: 2px;
        margin-right: 10px;
        vertical-align: middle;
      }
      .lineStyle {
        background: #3562db;
      }
    }
  }
  .form-inline {
    .el-input,
    .el-select,
    .el-cascader {
      width: 340px;
    }
  }
}
.basicBox {
  padding: 10px 24px 0 24px;
}
.importantBox {
  padding: 10px 24px 10px 24px;
}
.exigencyBox,
.normalBox,
.informBox {
  padding: 10px 24px 10px 24px;
  border-top: 1px solid #ddd;
}
</style>
