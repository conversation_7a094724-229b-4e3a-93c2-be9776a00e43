<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    title="添加说明"
    width="30%"
    :visible="visible"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <div class="content" style="padding: 10px">
      <el-form ref="ruleForm" :model="form" :rules="rules">
        <el-form-item prop="remark">
          <el-input v-model.trim="form.remark" type="textarea" :rows="4" placeholder="请输入添加说明" maxlength="100" show-word-limit class="ipt"> </el-input>
        </el-form-item>
      </el-form>
      <div>
        <div v-for="(item, index) in remaksData" :key="index" class="txt">
          <div style="color: #414653">{{ item.createdTime }}</div>
          <div style="color: #121f3e">
            <span>添加人 :</span>
            <span style="padding-left: 10px">{{ item.operationPersonName + '（' + item.operationPersonId + '）' }}</span>
          </div>
          <div style="color: #121f3e">
            <span>说明 :</span>
            <span style="padding-left: 10px">{{ item.remark || '-' }}</span>
          </div>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="confirm('ruleForm')">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  name: 'remarkDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    selectItems: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      form: {
        remark: ''
      },
      rules: {
        remark: [{ required: true, message: '请输入说明', trigger: 'change' }]
      },
      remaksData: []
    }
  },
  mounted() {
    this.getRemaksList()
  },
  methods: {
    getRemaksList() {
      let params = {
        isRemark: '1',
        operationId: this.selectItems.map((item) => item.alarmId).toString()
      }
      this.$api.selectAlarmOperationOrRemarkById(params).then((res) => {
        if (res.code == 200) {
          this.remaksData = res.data
        }
      })
    },
    // 关闭弹窗
    closeDialog() {
      this.form.remark = ''
      this.$emit('close')
    },
    confirm(formName) {
      let params = {
        alarmId: this.selectItems.map((item) => item.alarmId).toString(),
        remark: this.form.remark,
        operationSource: 0
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$api.insertRemarkById(params).then((res) => {
            if (res.code == 200) {
              this.$message.success('添加说明成功')
              this.$emit('close')
            } else {
              this.$message.error(res.msg)
              this.$emit('close')
            }
          })
        } else {
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  max-height: 300px !important;
  overflow: auto;
  background-color: #fff !important;
}
.model-dialog {
  padding: 0 !important;
}
.ipt {
  margin: 10px auto 0;
  width: 100% !important;
}
::v-deep .el-form-item__error {
  height: 20px;
  width: 300px;
  color: #f56c6c;
  font-size: 12px;
  line-height: 20px;
  padding-top: 4px;
  position: absolute;
  left: 0;
}
::v-deep .el-dialog__body {
  padding: 0 !important;
}
.txt {
  border-bottom: 1px solid #dcdfe6;
  width: 100%;
  padding: 5px 10px;
  background-color: #faf9fc;
  border-radius: 1px;
  div {
    line-height: 25px;
  }
}
</style>
