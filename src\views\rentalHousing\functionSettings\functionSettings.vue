<template>
  <PageContainer>
    <div slot="content" class="role-content" style="height: 100%">
      <div class="role-content-left">
        <div class="left_content">
          <ul>
            <li :class="{ pitchOn: setType == 1 }" @click="commonConfig">通用配置</li>
            <li :class="{ pitchOn: setType == 2 }" @click="reminderSettings">提醒设置</li>
            <li :class="{ pitchOn: setType == 3 }" @click="houseExtensionInfo">房源扩展信息</li>
            <li :class="{ pitchOn: setType == 4 }" @click="protocolConfig">协议配置</li>
          </ul>
        </div>
      </div>
      <div class="role-content-right">
        <common-config v-if="setType === 1"></common-config>
        <reminder-settings v-if="setType === 2"></reminder-settings>
        <house-extension-info v-if="setType === 3"></house-extension-info>
        <protocol-config v-if="setType === 4"></protocol-config>
      </div>
    </div>
  </PageContainer>
</template>

<script>
import CommonConfig from './CommonConfig.vue'
import reminderSettings from './reminderSettings.vue'
import HouseExtensionInfo from './houseExtensionInfo.vue'
import ProtocolConfig from './protocolConfig.vue'

export default {
  components: {
    CommonConfig,
    reminderSettings,
    HouseExtensionInfo,
    ProtocolConfig
  },
  data() {
    return {
      setType: 1 // 默认选中第一个
    }
  },
  mounted() {},
  methods: {
    commonConfig() {
      this.setType = 1
      // 通用配置的逻辑
    },
    reminderSettings() {
      this.setType = 2
      // 提醒设置的逻辑
    },
    houseExtensionInfo() {
      this.setType = 3
      // 房源扩展信息的逻辑
    },
    protocolConfig() {
      this.setType = 4
      // 协议配置的逻辑
    }
  }
}
</script>

<style lang="scss" scoped>
.role-content {
  height: 100%;
  display: flex;

  .role-content-left {
    width: 246px;
    height: 100%;
    padding: 10px;
    background: #fff;
    border-radius: 4px;
    margin-right: 12px;
    .left_content {
      height: calc(100% - 60px);
      overflow: auto;

      ul {
        padding: 0;

        li {
          height: 38px;
          width: 100%;
          font-size: 15px;
          font-family: 'PingFang SC-Regular', 'PingFang SC';
          font-weight: 400;
          color: #606266;
          line-height: 38px;
          list-style-type: none;
          box-sizing: border-box;
          cursor: pointer;
          padding-left: 27px;

          &:hover {
            background: rgb(53 98 219 / 20%);
            border-radius: 4px 0 0 4px;
          }
        }
      }

      .pitchOn {
        color: #3562db;
        background: linear-gradient(to right, #d9e1f8, #fff);
        font-weight: 500;
      }
    }
  }

  .role-content-right {
    width: calc(100% - 258px);
    height: 100%;
    padding: 10px 10px 20px 30px;
    background: #fff;
    border-radius: 4px;
  }
}

.el-form-item {
  margin-bottom: 0 !important;
}
</style>
