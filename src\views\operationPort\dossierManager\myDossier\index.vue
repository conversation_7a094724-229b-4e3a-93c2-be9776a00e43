<template>
  <PageContainer>
    <div slot="content" class="page_containner">
      <el-tabs v-model="active">
        <el-tab-pane v-for="(item, index) in tabList" :key="index" :label="item.label" :name="item.value"></el-tab-pane>
      </el-tabs>
      <MyDoc v-if="active == 1" />
      <Share v-if="active == 2" />
      <ShareManager v-if="active == 3" />
      <Deled v-if="active == 4" />
    </div>
  </PageContainer>
</template>
<script lang="jsx">
export default {
  components: {
    MyDoc: () => import('../components/MyDoc.vue'),
    Share: () => import('../components/share.vue'),
    ShareManager: () => import('../components/shareManager.vue'),
    Deled: () => import('../components/deled.vue')
  },
  data() {
    return {
      active: '1',
      tabList: [
        {
          label: '我的文档',
          value: '1'
        },
        {
          label: '共享文档',
          value: '2'
        },
        {
          label: '共享管理',
          value: '3'
        },
        {
          label: '回收站',
          value: '4'
        }
      ]
    }
  }
}
</script>
<style lang="scss" scoped>
.page_containner {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 16px;
}
</style>
