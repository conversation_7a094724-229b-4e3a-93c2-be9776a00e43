<template>
  <div ref="topology" class="scada-preview">
    <div v-loading="scadaLoading" customClass="custom-loading" element-loading-text="组态图加载中"
      style="width: 100%; height:100%">
      <meta2d-vue-preview v-if="JSON.stringify(scadaData) != '{}'" ref="meta2dVuePreview" :onlyPreview="true"
        :queryData="scadaData" :userInfo="userInfo" :baseUrl="baseUrl" @initCompleted="initCompleted" />
    </div>
  </div>
</template>

<script>
// onlyPreview 是否只是预览 不需要查看其他图纸及弹窗 区别与绘制页面的预览
// queryData 预览的图纸id及时间戳
// userInfo 用户信息
// baseUrl 接口ip地址

import { monitorTypeList } from '@/util/dict.js'
export default {
  name: 'scadaShow',
  props: {
    checkedTeamData: {
      type: Number,
      default: 0
    },
    entityMenuCode: {
      type: String,
      default: ''
    },
    requestHttp: {
      type: String,
      default: __PATH.VUE_MONITOR_API
    }
  },
  data() {
    return {
      monitorData: monitorTypeList.find((item) => item.projectCode == this.projectId),
      scadaLoading: false,
      scadaTimer: null,
      scadaData: {},
      baseUrl: this.requestHttp,
      userInfo: {
        avatarUrl: '',
        captcha: '',
        createdAt: '',
        deletedAt: '',
        email: '',
        id: '1',
        phone: '',
        updatedAt: '',
        username: 'admin',
        vip: 1,
        vipExpiry: '',
        isVip: true // 该属性是计算出来的，不是数据库中的
      }
    }
  },
  watch: {
    '$store.state.settings.sidebarCollapse': {
      handler(val) {
        this.$nextTick(() => {
          setTimeout(() => {
            if (JSON.stringify(this.scadaData) != '{}') {
              meta2d.resize()
            }
          }, 250)
        })
      },
      deep: true
    }
  },
  created() {
    // 网关需要token 组件获取token通过localStorage
    window.localStorage.setItem('token', 'Bearer ' + this.$store.state.user.token)
  },
  mounted() {
    this.scadaData = {}
    // this.getScadaList()
  },
  beforeDestroy() {
    // console.log('销毁')
    clearInterval(this.scadaTimer)
  },
  methods: {
    initCompleted() {
      this.scadaLoading = false
      console.log('initCompleted666')
    },
    // 实时监测--scada
    getScadaList(id) {
      this.scadaLoading = true
      this.scadaData = {}
      this.$api
        .getScadaData(
          {
            isiId: id
          }
        )
        .then((res) => {
          if (res.data.length > 0) {
            const scadaJsonData = res.data[0]?.isiJsonStr || ''
            this.handleScadaChange(scadaJsonData, { isiId: res.data[0]?.isiId })
          } else {
            this.scadaData = {}
            this.$message.warning('暂无数据')
            this.scadaLoading = false
          }
        })
    },

    // 选择scada
    handleScadaChange(val, assignData = {}) {
      let updataList = JSON.parse(val)
      Object.assign(updataList, assignData)
      this.data = updataList
      this.scadaData = {
        r: Date.now() + '',
        id: assignData.isiId
      }
      // this.setH5ScadaData()
    },
    // 单图纸
    oneScadaChange(id) {
      this.scadaData = {
        r: Date.now() + '',
        id: id
      }
    },

  }
}
</script>
<style lang="scss">
.scada-preview {
  position: relative;
  height: calc(100% - 0px);
  width: 100%;
  padding: 16px 0 0 16px;

  .tools {
    position: absolute;
    top: 16px;
    right: 10px;

    &>div {
      flex-grow: 1;
    }

    button {
      margin: 12px 16px;
      color: #fff;
      font-size: 14px;
      font-family: PingFangSC-Regular;
      line-height: 1;
    }
  }

  .menus-list {
    position: absolute;
    top: 28px;
    left: 20px;
  }
}
</style>
