<template>
  <div class="main">
    <el-dialog
      v-dialogDrag
      :modal="false"
      :visible.sync="dialogShow"
      custom-class="mainDialog main"
      append-to-body
      :close-on-click-modal="false"
      :before-close="closeDialog"
      class="personDialog"
    >
      <template slot="title">
        <span class="dialog-title">追溯详情</span>
      </template>
      <div class="dialog-content">
        <div class="reserve-scorll">
          <div class="reserve-plan">
            <div class="plan-title">
              <div class="color-box"><i class="el-icon-time"></i></div>
              <div class="linear-g">
                <span class="linear-g-span1">医废收集</span>
                <span>{{ taskRecordObj.updateTime }}</span>
                <!-- <i style="display: inline-block" :ref="'PGright' + taskIndex" class="el-icon-arrow-right title-icon"></i> -->
                <!-- <i v-show="false" :ref="'PGdown' + taskIndex" class="el-icon-arrow-down title-icon"></i> -->
              </div>
            </div>
            <div class="plan-content plan-content-line">
              <div class="item-row">
                <span class="li-first-span">收集人员</span><span class="li-last-span">{{ taskRecordObj.receivedPersonName }}</span>
              </div>
              <div class="item-row">
                <span class="li-first-span">医废类型</span><span class="li-last-span">{{ taskRecordObj.wasteType }}</span>
              </div>
              <div class="item-row">
                <span class="li-first-span">所属科室</span><span class="li-last-span">{{ taskRecordObj.officeName }}</span>
              </div>
              <div class="item-row">
                <span class="li-first-span">收集重量</span><span class="li-last-span">{{ taskRecordObj.gatherWeigh }}kg</span>
              </div>
              <div class="item-row">
                <span class="li-first-span">医废数量</span><span class="li-last-span">{{ taskRecordObj.count }}袋（件）</span>
              </div>
              <div class="item-row">
                <span class="li-first-span">医废编码</span
                ><span class="li-last-span">
                  <div v-for="item in taskRecordBarCodeList" :key="item" style="margin-bottom: 7px;">{{ item }}</div>
                </span>
              </div>
            </div>
          </div>
          <div class="reserve-plan">
            <div class="plan-title">
              <div class="color-box"><i class="el-icon-time"></i></div>
              <div class="linear-g">
                <span class="linear-g-span1">装箱(桶)入站</span>
                <!-- <span>{{ taskRecordObj.updateTime }}</span> -->
                <!-- <i style="display: inline-block" :ref="'PGright' + taskIndex" class="el-icon-arrow-right title-icon"></i> -->
                <!-- <i v-show="false" :ref="'PGdown' + taskIndex" class="el-icon-arrow-down title-icon"></i> -->
              </div>
            </div>
            <div class="plan-content plan-content-line">
              <div v-if="!inboundArr.length" class="nodata-center">无记录</div>
              <div v-for="(item, index) in inboundArr" :key="index" class="plan-border">
                <div class="item-row">
                  <span class="li-first-span">入站时间</span><span class="li-last-span">{{ item.inTime }}</span>
                </div>
                <div class="item-row">
                  <span class="li-first-span">入站人员</span><span class="li-last-span">{{ item.stationCode }}</span>
                </div>
                <div class="item-row">
                  <span class="li-first-span">转运箱(桶)编码</span><span class="li-last-span">{{ item.rfidCode }}</span>
                </div>
                <div class="item-row">
                  <span class="li-first-span">是否超时</span><span class="li-last-span">{{ item.expiredFlag }}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="reserve-plan">
            <div class="plan-title">
              <div class="color-box"><i class="el-icon-time"></i></div>
              <div class="linear-g">
                <span class="linear-g-span1">出站交接</span>
                <!-- <span>{{ taskRecordObj.updateTime }}</span> -->
                <!-- <i style="display: inline-block" :ref="'PGright' + taskIndex" class="el-icon-arrow-right title-icon"></i> -->
                <!-- <i v-show="false" :ref="'PGdown' + taskIndex" class="el-icon-arrow-down title-icon"></i> -->
              </div>
            </div>
            <div class="plan-content">
              <div v-if="!outboundArr.length" class="nodata-center">无记录</div>
              <div v-for="(item, index) in outboundArr" :key="index" class="plan-border">
                <div class="item-row">
                  <span class="li-first-span">出站时间</span><span class="li-last-span">{{ item.outTime }}</span>
                </div>
                <div class="item-row">
                  <span class="li-first-span">出站人员</span><span class="li-last-span">{{ item.stationCode }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: 'retrospect',
  props: {
    dialogShow: {
      type: Boolean,
      default: false
    },
    detailId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tableData: [],
      retrospectShow: false,
      taskRecordObj: {},
      inboundArr: [],
      outboundArr: []
    }
  },
  computed: {
    taskRecordBarCodeList: function () {
      return this.taskRecordObj.barCode ? this.taskRecordObj.barCode.split(',') : {}
    }
  },
  watch: {},
  created() {},
  mounted() {
    this.getDetail()
  },
  methods: {
    // 获取列表
    getDetail() {
      this.$api.gatherRetrospect({ id: this.detailId }).then((res) => {

        if (res.code === '200') {
          this.taskRecordObj = res.data.gather
          this.inboundArr = res.data.inbound
          this.outboundArr = res.data.outbound
        }
      })
    },
    // 取消按钮
    closeDialog() {
      this.$emit('retrospectCloseDialog')
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
::v-deep .mainDialog {
  width: 28%;
  height: 83vh;
  margin-top: 10vh !important;
  background: #fff;
  pointer-events: auto;
  .dialog-title {
    display: inline-block;
    width: 38%;
  }
  .el-dialog__body {
    padding: 0;
    height: calc(100% - 60px);
    max-height: 82vh;
    .dialog-content {
      width: 100%;
      height: 100%;
      .el-table {
        border: none !important;
        .redBg {
          background: #fff;
        }
      }
    }
  }
}
.reserve-scorll {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  box-sizing: border-box;
  padding: 10px 55px;
}
.reserve-plan {
  width: 100%;
  position: relative;
  .plan-title {
    display: flex;
    width: 100%;
    height: 30px;
    .color-box {
      height: 1.25rem;
      width: 1.25rem;
      color: #000;
      line-height: 1.25rem;
      text-align: center;
      border-radius: 3px;
      font-size: 16px;
      margin: auto;
    }
    .linear-g {
      margin-left: 2px;
      width: calc(100% - 22px);
      height: 100%;
      background: #fff;
      font-size: 13px;
      line-height: 30px;
      font-family: PingFangSC-Medium;
      color: #000;
      border-radius: 6px;
      // cursor: pointer;
      .linear-g-span1 {
        margin: 0 15px;
        font-weight: 600;
      }
      .title-icon {
        float: right;
        line-height: 30px;
        margin-right: 5px;
      }
    }
  }
  .plan-content {
    width: calc(100% - 33px);
    margin-left: 11px;
    color: #000;
    font-size: 13px;
    .plan-border {
      width: calc(100% - 20px);
      // margin-left: 20px;
      border: 1px solid #eee;
      margin: 10px 0 5px 20px;
      padding-bottom: 10px;
      box-sizing: border-box;
      .item-row {
        padding: 12px 0 5px 10px;
      }
    }
    .item-row {
      // width: 100%;
      width: 90%;
      display: flex;
      padding: 12px 0 5px 30px;
      box-sizing: border-box;
      .li-first-span {
        display: inline-block;
        width: 120px;
        font-size: 14px;
        font-family: PingFang-SC-Medium, PingFang-SC;
        font-weight: 500;
        color: #000;
      }
      .li-last-span {
        font-size: 14px;
        font-family: PingFang-SC-Medium, PingFang-SC;
        font-weight: 500;
        color: #000;
      }
    }
    .nodata-center {
      text-align: center;
      padding: 15px;
      font-size: 15px;
    }
  }
  .plan-content-line {
    border-left: 1px solid #303758;
  }
}
</style>
