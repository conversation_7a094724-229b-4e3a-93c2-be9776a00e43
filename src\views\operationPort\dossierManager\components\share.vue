<template>
  <div class="tab_content">
    <div class="right-heade">
      <el-input v-model="formData.archiveInfo" placeholder="文件名/文号/摘要" suffix-icon="el-icon-search" clearable />
      <el-cascader
        v-model="formData.ownerDeptId"
        placeholder="所属部门"
        :options="deptList"
        :props="{
          value: 'id',
          label: 'deptName',
          checkStrictly: true,
          emitPath: false
        }"
        clearable
        filterable
        size="small"
      >
      </el-cascader>
      <VirtualListSelect
        v-model="formData.ownerId"
        placeholder="所有者"
        clearable
        :options="ownerList"
        :propsOptions="{
          label: 'staffName',
          value: 'id'
        }"
      />
      <el-button type="primary" @click="search">查询</el-button>
      <el-button type="primary" plain @click="reset">重置</el-button>
    </div>
    <div class="right-content">
      <div class="table-content">
        <TablePage
          ref="tablePage"
          v-loading="tableLoading"
          v-scrollHideTooltip
          :tableColumn="tableColumn"
          :data="tableData"
          border
          height="100%"
          :showPage="true"
          :pageData="pageData"
          :pageProps="{
            page: 'current',
            pageSize: 'size',
            total: 'total'
          }"
          @pagination="paginationChange"
        >
        </TablePage>
      </div>
    </div>
    <ViewAttachments :id="currentId" :visible.sync="visible" :isShare="true" />
  </div>
</template>
<script lang="jsx">
import tableListMixin from '@/mixins/tableListMixin.js'
import ViewAttachments from './ViewAttachments.vue'
import { transData } from '@/util'
import dictMixin from '@/views/operationPort/contractManagement/mixins/index.js'
export default {
  components: { ViewAttachments },
  mixins: [tableListMixin, dictMixin],
  data() {
    return {
      tableColumn: [
        {
          prop: 'archiveName',
          label: '文件名称',
          align: 'center'
        },
        {
          prop: 'archiveNumber',
          label: '文号',
          align: 'center'
        },
        {
          prop: 'folderName',
          label: '文件夹',
          align: 'center'
        },
        {
          prop: 'archiveModel',
          label: '分类',
          align: 'center',
          render: (h, row) => {
            console.log(this.classification)
            const item = this.classification.find((item) => item.value === row.row.archiveModel)
            return item ? item.label : ''
          }
        },
        {
          prop: 'archiveDate',
          label: '成文日期',
          align: 'center'
        },
        {
          prop: 'archiveOwnerName',
          label: '所有者',
          align: 'center'
        },
        {
          prop: 'archiveOwnerDeptName',
          label: '所有者部门',
          align: 'center'
        },
        {
          prop: 'remark',
          label: '摘要信息',
          align: 'center'
        },
        {
          prop: 'shareEndDate',
          label: '共享结束日期',
          align: 'center'
        },
        {
          prop: 'handler',
          label: '操作',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span class="operationBtn-span" style="color: #3562DB" onClick={() => this.handleViewAtta(row.row)}>
                  查看附件
                </span>
                <span class="operationBtn-span" style="color: #3562DB" onClick={() => this.handleGotoDetail(row.row)}>
                  详情
                </span>
              </div>
            )
          }
        }
      ],
      formData: {
        archiveInfo: '',
        ownerId: '',
        ownerDeptId: ''
      },
      tableLoading: false,
      tableData: [],
      pageData: {
        current: 1,
        size: 15,
        total: 0
      },
      currentId: '',
      visible: false,
      ownerList: [],
      deptList: []
    }
  },
  created() {
    this.handleQueryTablelist()
    this.getDeptList()
    this.getLersonnelList()
  },
  methods: {
    // 获取部门列表
    getDeptList() {
      this.$api.getSelectedDept().then((res) => {
        if (res.code == '200') {
          this.deptList = transData(res.data, 'id', 'pid', 'children')
        }
      })
    },
    // 获取人员列表
    getLersonnelList() {
      let params = {
        current: 1,
        size: 99999
      }
      this.$api.staffList(params).then((res) => {
        if (res.code == 200) {
          this.ownerList = res.data.records
        }
      })
    },
    handleGotoDetail(row) {
      this.$router.push(`/dossierManager/myDocumentDetails?id=${row.archiveId}&notShowEdit=true`)
    },
    handleViewAtta(row) {
      this.currentId = row.archiveShareId
      this.visible = true
    },
    handleQueryTablelist() {
      const parmas = {
        ...this.formData,
        ...this.pageData,
        archiveType: '1'
      }
      this.$api.fileManagement.shareListByPage(parmas).then((res) => {
        this.tableData = res.data.records
        this.pageData.total = res.data.total
      })
    },
    // 分页事件
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getTableList()
    },
    search() {
      this.pageData.current = 1
      this.handleQueryTablelist()
    },
    reset() {
      Object.keys(this.formData).forEach((key) => {
        this.formData[key] = ''
      })
      this.search()
    }
  }
}
</script>
<style lang="scss" scoped>
.tab_content {
  width: 100%;
  height: calc(100% - 40px);
  .right-heade {
    border-radius: 4px;
    ::v-deep .el-input {
      width: 200px;
    }
    & > div {
      margin-right: 10px;
      margin-top: 10px;
    }
  }
  .right-content {
    background: #fff;
    border-radius: 4px;
    overflow: hidden;
    width: 100%;
    height: calc(100% - 50px);
    margin-top: 16px;
    .btns-group {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
      & > div {
        display: flex;
      }
      .btns-group-control {
        > div {
          margin-left: 10px;
        }
        // & > div, & > button {
        //   margin-right: 10px;
        // }
      }
    }
    .table-content {
      height: calc(100% - 45px);
      ::v-deep .el-table .el-table__body-wrapper td.el-table__cell {
        border-right: 1px solid #ededf5;
        .tooltip-over-td {
          display: block;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
}
</style>
