<template>
  <div>
    <ContentCard :title="type == 'new' ? '新建应用' : type == 'edit' ? '编辑应用' : type == 'details' ? '查看菜单' : ''"></ContentCard>
    <el-form ref="ruleForm" style="padding-left: 10px; padding-right: 10px" :model="form" :rules="rules">
      <div style="display: flex; justify-content: space-between; width: 50%">
        <el-form-item label="应用简称" label-width="85px" prop="appReferred">
          <el-input v-model="form.appReferred" placeholder="应用简称" class="ipt" :disabled="type == 'details'"></el-input>
        </el-form-item>
        <el-form-item label="应用全称" label-width="85px" prop="appName">
          <el-input v-model="form.appName" placeholder="请输入应用全称" class="ipt" :disabled="type == 'details'"></el-input>
        </el-form-item>
      </div>
      <div style="display: flex; justify-content: space-between; width: 50%">
        <el-form-item label="应用地址" label-width="85px" prop="appAddress">
          <el-input v-model="form.appAddress" placeholder="请输入应用地址" style="width: 655px"></el-input>
        </el-form-item>
      </div>
      <el-form-item label="应用类型" label-width="85px" prop="appType">
        <el-select v-model="form.appType" placeholder="请选择应用类型" class="ipt">
          <el-option></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="应用图标" label-width="85px">
        <span style="font-size: 10px; color: #7f848c">支持{{ uploadAcceptDict['picture'].type }},且大小不超过{{ uploadAcceptDict['picture'].fileSize }}M</span>
        <div style="display: flex">
          <el-upload
            action=""
            :headers="headers"
            list-type="picture-card"
            :class="{ hide: hideUpload }"
            :file-list="fileList"
            :accept="uploadAcceptDict['picture'].type"
            :limit="1"
            :before-upload="beforeAvatarUpload"
            :http-request="httpRequset"
            :on-remove="handleRemove"
            :on-change="fileChange"
            :disabled="type == 'details'"
          >
            <i class="el-icon-circle-plus-outline" style="color: #3562db"
            ><br /><span style="font-size: 10px; color: #7f848c">默认图标</span><span style="font-size: 10px; color: #7f848c">(20*20)</span></i
            >
          </el-upload>
          &nbsp;
          <el-upload
            action=""
            :headers="headers"
            list-type="picture-card"
            :class="{ hide2: hideUpload2 }"
            :file-list="fileList2"
            :accept="uploadAcceptDict['picture'].type"
            :limit="1"
            :before-upload="beforeAvatarUpload"
            :http-request="httpRequset2"
            :on-remove="handleRemove2"
            :on-change="fileChange2"
            :disabled="type == 'details'"
          >
            <!-- <img v-if="imageUrl" :src="imageUrl" class="avatar" /> -->
            <i class="el-icon-circle-plus-outline" style="color: #3562db"
            ><br /><span style="font-size: 10px; color: #7f848c">选中图标</span><span style="font-size: 10px; color: #7f848c">(32*32)</span></i
            >
          </el-upload>
        </div>
      </el-form-item>
      <el-form-item label="简介" label-width="85px">
        <el-input
          v-model="form.remarks"
          style="width: 45%"
          placeholder="请输入简介"
          type="textarea"
          maxlength="200"
          show-word-limit
          :autosize="{ minRows: 4, maxRows: 4 }"
          :disabled="type == 'details'"
        ></el-input>
      </el-form-item>
    </el-form>
    <div class="buttom">
      <el-button v-if="type != 'details'" type="primary" @click="confirm('ruleForm')">保存</el-button>
      <el-button type="primary" plain @click="cancel">取消</el-button>
    </div>
  </div>
</template>
<script>
import store from '@/store/index'
import axios from 'axios'
import { uploadAcceptDict } from '@/util/dict.js'
export default {
  props: {
    type: String,
    menuId: Number
  },
  data() {
    return {
      form: {
        uploadAcceptDict,
        appReferred: '', //  应用简称
        appName: '', //  应用全称
        appAddress: '', //  应用地址
        appType: '', //  应用类型
        remarks: ''
      },
      icon: {},
      formData: '',
      formData2: '',
      imageUrl: '',
      fileList: [],
      fileList2: [],
      hideUpload: false,
      hideUpload2: false,
      areaName: '',
      rules: {
        appReferred: [{ required: true, message: '请输入应用简称', trigger: 'blur' }],
        appName: [{ required: true, message: '请输入应用全称', trigger: 'blur' }],
        appAddress: [{ required: true, message: '请输入应用地址', trigger: 'blur' }],
        appType: [{ required: true, message: '请选择应用类型', trigger: 'blur' }]
      }
    }
  },
  computed: {
    headers() {
      return {
        Authorization: 'Bearer ' + store.state.user.token
      }
    }
  },
  created() {
    if (this.type != 'new') {
      // this.$api.getMenuInfo({ menuId: this.menuId }).then((res) => {
      //   if (res.code == 200) {
      //     this.form.superiorMenu = res.data.menuInfo.parentId
      //     this.form.radio = JSON.stringify(res.data.menuInfo.menuType)
      //     this.form.menuName = res.data.menuInfo.menuName
      //     this.form.chainedAddress = res.data.menuInfo.pathUrl
      //     this.form.sortNumber = res.data.menuInfo.orderNum
      //     this.form.remarks = res.data.menuInfo.remark
      //     this.areaName = res.data.menuInfo.parentName
      //     let url = JSON.parse(res.data.menuInfo.icon)
      //     if (url.approve != undefined) {
      //       this.fileList = [
      //         {
      //           url: url.approve
      //         }
      //       ]
      //       this.hideUpload = true
      //       this.icon.approve = url.approve
      //     } else {
      //       this.hideUpload = false
      //     }
      //     if (url.pitch != undefined) {
      //       this.fileList2 = [
      //         {
      //           url: url.pitch
      //         }
      //       ]
      //       this.hideUpload2 = true
      //       this.icon.pitch = url.pitch
      //     } else {
      //       this.hideUpload2 = false
      //     }
      //   }
      // })
    }
  },
  methods: {
    confirm(formName) {
      let params = {
        remark: this.form.remarks
      }
      if (this.icon.approve == undefined && this.icon.pitch == undefined) {
        params.icon = ''
      } else {
        params.icon = JSON.stringify(this.icon)
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.type == 'new') {
            params.userId = this.$store.state.user.userInfo.userId
            // this.$api.menuAdd(params).then((res) => {
            //   if (res.code == 200) {
            //     this.$message({
            //       message: res.data,
            //       type: 'success'
            //     })
            //     this.$emit('menuDiscreteness')
            //     this.$emit('child-event', false)
            //   } else {
            //     this.$message.error(res.data)
            //   }
            // })
          } else {
            params.menuId = this.menuId
            // this.$api.edit(params).then((res) => {
            //   if (res.code == 200) {
            //     this.$message({
            //       message: res.data,
            //       type: 'success'
            //     })
            //     this.$emit('menuDiscreteness')
            //     this.$emit('child-event', false)
            //   } else {
            //     this.$message.error(res.data)
            //   }
            // })
          }
        } else {
          // console.log('error submit!!')
          return false
        }
      })
    },
    cancel() {
      this.$emit('child-event', false)
    }
    // fileChange(file, fileList) {
    //   this.fileList = fileList
    // },
    // beforeAvatarUpload(file) {
    //   const isJPG = file.type === 'image/png'
    //   const isLt2M = file.size / 1024 / 1024 < 5
    //   if (!isJPG) {
    //     this.$message.error('上传头像图片只能是 png 格式!')
    //   }
    //   if (!isLt2M) {
    //     this.$message.error('上传头像图片大小不能超过 5MB!')
    //   }
    //   return isJPG && isLt2M
    // },
    // handleRemove(file, fileList) {
    //   this.icon.approve = undefined
    //   this.hideUpload = false
    // },
    // httpRequset() {
    //   this.formData = new FormData()
    //   this.fileList.forEach((item) => {
    //     console.log(item)
    //     this.formData.append('file', item.raw)
    //   })
    //   axios
    //     .post(__PATH.VUE_SYS_API + 'SysMenu/upload', this.formData, {
    //       headers: {
    //         Authorization: 'Bearer ' + store.state.user.token
    //       }
    //     })
    //     .then((res) => {
    //       console.log(res, 'res')
    //       if (res.data.code == 200) {
    //         this.hideUpload = true
    //         this.icon.approve = res.data.data
    //         this.$message({
    //           showClose: true,
    //           message: res.data.msg,
    //           type: 'success'
    //         })
    //       } else {
    //         this.$message.error(res.data.msg)
    //       }
    //     })
    // },
    // fileChange2(file, fileList) {
    //   console.log(fileList, 'fileListfileListfileList')
    //   this.fileList2 = fileList
    // },
    // handleRemove2(file, fileList) {
    //   this.icon.pitch = undefined
    //   this.hideUpload2 = false
    // },
    // httpRequset2() {
    //   this.formData2 = new FormData()
    //   this.fileList2.forEach((item) => {
    //     console.log(item)
    //     this.formData2.append('file', item.raw)
    //   })
    //   axios
    //     .post(__PATH.VUE_SYS_API + 'SysMenu/upload', this.formData2, {
    //       headers: {
    //         Authorization: 'Bearer ' + store.state.user.token
    //       }
    //     })
    //     .then((res) => {
    //       console.log(res, 'ress')
    //       if (res.data.code == 200) {
    //         this.icon.pitch = res.data.data
    //         this.hideUpload2 = true
    //         this.$message({
    //           showClose: true,
    //           message: res.data.msg,
    //           type: 'success'
    //         })
    //       } else {
    //         this.$message.error(res.data.msg)
    //       }
    //     })
    // }
  }
}
</script>
<style lang="scss" scoped>
.top {
  border-left: 3px solid #759bfa;
  color: #759bfa;
  font-weight: 700;
  padding-left: 15px;
}
.ipt {
  width: 200px;
}
.buttom {
  position: absolute;
  bottom: 1.8%;
  height: 50px;
  line-height: 50px;
  width: calc(100% - 30px);
  background: rgb(255 255 255 / 80%);
  box-shadow: 0 0 12px 3px rgb(0 0 0 / 10%);
  text-align: right;
  padding-right: 10px;
}
::v-deep .el-upload--picture-card {
  border: 1px dashed #409eff !important;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.hide {
  ::v-deep .el-upload--picture-card {
    display: none;
  }
}
.hide2 {
  ::v-deep .el-upload--picture-card {
    display: none;
  }
}
</style>
