<template>
  <pageContainer v-loading="pageLoading" footer>
    <div slot="header" class="header_content">
      <div @click="goBack">
        <i class="el-icon-arrow-left"></i>
        <span class="title_text">巡检记录</span>
      </div>
    </div>
    <div slot="content" class="page_content">
      <div class="content_bottom">
        <el-table :data="recordsList" style="width: 100%">
          <el-table-column label="操作" width="120">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="view(scope.row)">查看</el-button>
            </template>
          </el-table-column>
          <el-table-column prop="taskName" label="任务名称"> </el-table-column>
          <el-table-column prop="actualExecutionTime" label="巡查日期"> </el-table-column>
          <el-table-column prop="state" label="巡查结果">
            <template slot-scope="scope">
              {{ scope.row.state ? (scope.row.state == '2' ? '合格' : scope.row.state == '3' ? '不合格' : '报修') : '' }}
            </template>
          </el-table-column>
          <el-table-column prop="personName" label="执行人员"> </el-table-column>
        </el-table>
      </div>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="goBack">关闭</el-button>
    </div>
  </pageContainer>
</template>
<script>
import dayjs from 'dayjs'
export default {
  data() {
    return {
      recordsList: [],
      pageLoading: false
    }
  },
  computed: {
    cycleTypeName() {
      if (this.detail.taskPoint.cycleType) {
        return this.cycleList.find((el) => el.cycleType == this.detail.taskPoint.cycleType).label
      } else {
        return '-'
      }
    }
  },
  mounted() {
    this.getDetail()
  },
  methods: {
    dayjs,
    getDetail() {
      const params = {
        planId: this.$route.query.id,
        currentPage: 1,
        size: 999
      }
      this.pageLoading = true
      this.$api.maintainRecordPointList(params).then((res) => {
        if (res.code == 200) {
          this.recordsList = res.data.list
        }
        this.pageLoading = false
      })
    },
    handleClick() {},
    goBack() {
      this.$router.go(-1)
    },
    view(row) {
      this.$router.push({
        path: 'maintainRecordDetail',
        query: {
          id: row.recordId
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.header_content {
  width: 100%;
  height: 60px;
  line-height: 60px;
  padding: 0 16px;
  font-size: 18px;
  > div {
    width: 100px;
    cursor: pointer;
  }
}
.page_content {
  height: 100%;
  width: 100%;
  background: #fff;
  padding: 16px 32px;
}
.fileContent {
  padding: 10px;
  .fileItem {
    width: 700px;
    padding: 0 10px;
    &:hover {
      background: #4476ff11;
    }
    height: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
