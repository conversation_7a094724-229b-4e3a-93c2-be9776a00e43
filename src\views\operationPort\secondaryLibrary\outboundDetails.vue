<template>
  <div class="content-box">
    <div class="content-right content-right-width">
      <div class="right-top-form">
        <el-form ref="formInline" :inline="true" :model="formInline" class="demo-form-inline">
          <el-form-item label="" prop="unionSel">
            <el-input v-model="formInline.unionSel" placeholder="出库单号/配件名称/配件编码" style="width: 300px"></el-input>
          </el-form-item>
          <el-form-item label="" prop="warehouseId">
            <el-select v-model="formInline.warehouseId" placeholder="请选择仓库" filterable clearable>
              <el-option v-for="item in warehouseList" :key="item.id" :label="item.warehouseName" :value="item.id"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="" prop="startAndEnd">
            <el-date-picker
              v-model="formInline.startAndEnd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSubmit">查询</el-button>
            <!-- 重置 -->
            <el-button type="primary" plain @click="onReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="right-bottom-box">
        <!-- 导出 -->
        <el-button type="primary" @click="exportExcel">导出</el-button>
        <div class="right-bottom-table">
          <el-table
            v-loading="tableLoading"
            :data="tableData"
            border
            height="calc(100%)"
            style="width: 100%; overflow: auto"
            :cell-style="{ padding: '8px 0' }"
            stripe
            :header-cell-style="{ background: '#f2f4fbd1' }"
            :empty-text="emptyText"
            highlight-current-row
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="60"> </el-table-column>
            <el-table-column v-for="(item, index) in outboundDetails.tableHeader" :key="index" :prop="item.prop" :label="item.label" :width="item.width" show-overflow-tooltip>
            </el-table-column>
          </el-table>
        </div>
        <div class="" style="padding-top: 10px; text-align: right">
          <el-pagination
            :current-page="paginationData.currentPage"
            :page-sizes="[15, 30, 50, 100]"
            :page-size="paginationData.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="paginationData.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import axios from 'axios'
import { outboundDetails } from './json/index.js'
export default {
  name: 'outboundDetails',
  components: {},
  mixins: [],
  props: {},
  data() {
    return {
      outboundDetails,
      tableData: [],
      warehouseList: [],
      multipleSelection: [],
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      formInline: {
        unionSel: '',
        warehouseId: '',
        startAndEnd: []
      },
      emptyText: '暂无数据',
      userInfo: {},
      tableLoading: false
    }
  },
  computed: {},
  watch: {
    'formInline.startAndEnd'(val) {
      if (val === null) {
        this.formInline.startAndEnd = []
      }
    }
  },
  created() {
    this.userInfo = this.$store.state.user.userInfo.user
    this.formInline.unionSel = this.$route.query?.materialCode
    this.init()
    this.onSubmit()
  },
  mounted() {},
  methods: {
    // 查询
    onSubmit() {
      const {
        paginationData: { currentPage, pageSize },
        formInline: { unionSel, warehouseId, supplierId, startAndEnd },
        userInfo: { staffId, staffName }
      } = this
      let params = {
        userId: staffId,
        userName: staffName,
        currentPage,
        pageSize,
        supplierId, // 供应商
        unionSel, // 入库信息
        warehouseId, // 仓库
        beginDate: '',
        endDate: ''
      }
      if (startAndEnd.length > 0) {
        params.beginDate = startAndEnd[0]
        params.endDate = startAndEnd[1]
      }
      this.tableLoading = true
      this.$api
        .getOutwarehouseDetailList(params)
        .then((res) => {
          const { code, message } = res
          if (code === '200') {
            const { list, sum } = res.data
            this.tableData = list
            this.paginationData.total = sum
          } else {
            this.$message.error(message)
          }
        })
        .catch((err) => {
          this.$message.error('查询失败！')
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    // 重置
    onReset() {
      this.$refs.formInline.resetFields()
      this.formInline.unionSel = ''
      this.onSubmit()
    },
    // 导出
    exportExcel() {
      const {
        userInfo: { staffId, staffName, unitCode, hospitalCode },
        multipleSelection
      } = this
      if (!multipleSelection.length) {
        this.$message.error('请选择要导出的数据')
        return
      }
      const ids = multipleSelection.map((item) => item.id).join(',')
      const formData = {
        ids,
        userId: staffId,
        userName: staffName,
        unitCode,
        hospitalCode
      }
      axios({
        method: 'get',
        url: __PATH.VUE_APP_PARTS_LIBRARY + 'outwarehouseRecord/materialRecordExport2',
        params: formData,
        responseType: 'blob',
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      })
        .then((res) => {
          const name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
          const blob = new Blob([res.data])
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
        })
        .catch(() => {
          this.$message.error('导出失败')
        })
    },
    // 条数
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.onSubmit()
    },
    // 页数
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.onSubmit()
    },
    // 多选
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 初始化
    init() {
      const { staffId, staffName } = this.userInfo
      let params = {
        userId: staffId,
        userName: staffName,
        warehouseType: '2',
        status: '0',
        userType: '1'
      }
      this.$api.getWarehouseList(params).then((res) => {
        const { code } = res
        if (code === '200') {
          this.warehouseList = res.data.list
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import './styles/index.scss';
</style>
