<template>
  <PageContainer :footer="true">
    <div slot="content" v-loading="pageLoading" class="addApp-content">
      <el-form ref="formInline" :model="formInline" :rules="rules" :disabled="$route.query.type == 'detail'">
        <ContentCard title="新增租户">
          <div slot="content" style="overflow: hidden">
            <el-row :gutter="24">
              <el-col :md="7">
                <el-form-item label="租户名称" prop="tenantName" label-width="100px">
                  <el-input v-model="formInline.tenantName" maxlength="20" placeholder="请输入租户名称" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :md="7">
                <el-form-item label="联系人" prop="contacts" label-width="100px">
                  <el-input v-model="formInline.contacts" maxlength="10" placeholder="请输入联系人"></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="7">
                <el-form-item label="联系电话" prop="tel" label-width="100px">
                  <el-input v-model="formInline.tel" maxlength="11" placeholder="请输入联系电话" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :md="14">
                <el-form-item label="营业执照" prop="businessLicenseUrl" label-width="100px">
                  <span style="font-size: 10px; color: #7f848c">支持{{ uploadAcceptDict['picture'].type }},且大小不超过{{ uploadAcceptDict['picture'].fileSize }}M</span>
                  <div style="display: flex">
                    <el-upload
                      action=""
                      :class="{ hide: iconFileList.length }"
                      list-type="picture-card"
                      :file-list="iconFileList"
                      :accept="uploadAcceptDict['picture'].type"
                      :limit="1"
                      :before-upload="beforeAvatarUpload"
                      :http-request="(file) => httpRequset(file)"
                      :on-remove="(file, fileList) => handleRemove(file, fileList)"
                      :on-change="(file, fileList) => fileChange(file, fileList)"
                      :disabled="$route.query.type == 'detail'"
                    >
                      <i class="el-icon-circle-plus-outline" style="color: #3562db"></i>
                    </el-upload>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :md="10">
                <el-form-item label="备注" prop="remark" label-width="100px">
                  <el-input v-model="formInline.remark" placeholder="请输入备注" type="textarea" maxlength="200" :autosize="{ minRows: 4 }" show-word-limit></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </ContentCard>
      </el-form>
    </div>
    <div slot="footer">
      <el-button
        type="primary"
        plain
        @click="
          () => {
            $router.go(-1)
          }
        "
      >关闭</el-button
      >
      <el-button type="primary" :disabled="$route.query.type == 'detail'" @click="submitForm('formInline')">保存</el-button>
    </div>
  </PageContainer>
</template>
<script>
import { uploadAcceptDict } from '@/util/dict.js'
export default {
  name: 'addApp',
  // 路由前置守卫动态修改meta中的title
  beforeRouteEnter(to, from, next) {
    if (to.meta.title) {
      const typeList = {
        add: '新增租户',
        edit: '编辑租户',
        detail: '租户详情'
      }
      to.meta.title = typeList[to.query.type] ?? '租户详情'
    }
    next()
  },
  async beforeRouteLeave(to, from, next) {
    if (!['tenantManagement'].includes(to.name)) {
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      uploadAcceptDict,
      pageLoading: false,
      iconFileList: [],
      formInline: {
        tenantName: '', // 租户名称
        contacts: '', // 联系人
        tel: '', // 联系电话
        remark: '', // 备注
        businessLicenseUrl: '' // 营业执照url
      },
      rules: {
        tenantName: [{ required: true, message: '请输入应用简称', trigger: ['blur', 'change'] }],
        contacts: [{ required: true, message: '请输入联系人', trigger: ['blur', 'change'] }],
        tel: [{ required: true, message: '请输入联系电话', trigger: ['blur', 'change'] }],
        businessLicenseUrl: [{ required: true, message: '请上传营业执照', trigger: ['blur', 'change'] }]
      }
    }
  },
  watch: {
    'formInline.businessLicenseUrl'(val) {
      if (val != '' && this.$route.query.type == 'add') {
        this.$refs.formInline.validateField('businessLicenseUrl')
      }
    }
  },
  activated() {
    this.init()
  },
  created() {
    if (!this.$store.state.keepAlive.list.includes('tenantManagement')) {
      this.init()
    }
  },
  methods: {
    init() {
      Object.assign(this.$data, this.$options.data())
      this.$nextTick(() => {
        this.$refs.formInline.resetFields()
      })
      if (this.$route.query.type != 'add') {
        this.getAppInfo()
      }
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.formInline.id) {
            this.$api.updateTenant(this.formInline, { 'operation-type': 2, 'operation-name': this.formInline.tenantName, 'operation-id': this.formInline.id }).then((res) => {
              if (res.code == 200) {
                this.$message({ message: '修改成功', type: 'success' })
                this.$router.go(-1)
              } else {
                this.$message({ message: res.msg, type: 'error' })
              }
            })
          } else {
            this.$api.insertTenant(this.formInline, { 'operation-type': 1 }).then((res) => {
              if (res.code == 200) {
                this.$message({ message: '提交成功', type: 'success' })
                this.$router.go(-1)
              } else {
                this.$message({ message: res.msg, type: 'error' })
              }
            })
          }
        }
      })
    },
    // 获取应用详情
    getAppInfo() {
      this.pageLoading = true
      this.$api
        .getTenantById({ id: this.$route.query.id })
        .then((res) => {
          this.pageLoading = false
          if (res.code == 200) {
            this.formInline = res.data
            this.iconFileList = [{ url: this.$tools.imgUrlTranslation(res.data.businessLicenseUrl) }]
            // let { tenantName, contacts, tel, remark, businessLicenseUrl, id} = res.data
            // let icons = JSON.parse(businessLicenseUrl)
            // this.formInline = {
            //   tenantName, contacts, tel, remark, id,
            //   businessLicenseUrl
            // }
          }
        })
        .catch(() => {
          this.pageLoading = false
        })
    },
    fileChange(file, fileList) {
      this.fileList = fileList
      this.iconFileList = fileList
    },
    handleRemove(file, fileList) {
      this.formInline.businessLicenseUrl = ''
      this.iconFileList = []
    },
    httpRequset(file) {
      const params = new FormData()
      params.append('file', file.file)
      this.$api.spaceaUpload(params).then((res) => {
        if (res.code == 200) {
          this.formInline.businessLicenseUrl = res.data
          this.$message({
            message: '上传成功',
            type: 'success'
          })
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },
    beforeAvatarUpload(file) {
      const fileSize = this.uploadAcceptDict['picture'].fileSize
      const isLt2M = file.size / 1024 / 1024 < fileSize
      if (!isLt2M) {
        this.$message.error(`上传文件大小不能超过 ${fileSize}MB!`)
        return false
      }
      const extension = file.name.substring(file.name.lastIndexOf('.'))
      const isFormat = this.uploadAcceptDict['picture'].type.includes(extension)
      if (!isFormat) {
        this.$message.error(`上传图片只能是 ${this.uploadAcceptDict['picture'].type}格式!`)
        return false
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.addApp-content {
  background: #fff;
  border-radius: 4px;
  height: 100%;
  padding: 10px;
  overflow-y: auto;
  ::v-deep .el-upload {
    width: 130px;
    height: 130px;
  }
  ::v-deep .el-upload-list {
    .el-upload-list__item {
      width: 130px;
      height: 130px;
    }
  }
  .hide {
    ::v-deep .el-upload--picture-card {
      display: none;
    }
  }
}
</style>
