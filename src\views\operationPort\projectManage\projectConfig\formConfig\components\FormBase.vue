<script>
import { UsingStatusOptions } from '@/views/operationPort/constant'
export default {
  name: 'FormBase',
  props: {
    visible: Boolean
  },
  events: ['update:visible', 'success', 'config'],
  data: function() {
    return {
      formModel: {
        name: '',
        code: '',
        flowKey: '',
        status: 1,
        remark: ''
      },
      rules: {
        name: [{ required: true, message: '请输入表单名称' }],
        code: [{ required: true, message: '请输入表单编码' }],
        flowKey: [{ required: true, message: '请选择适用流程' }]
      },
      // 流程列表
      flowList: [],
      loadingStatus: false,
      statusOptions: UsingStatusOptions
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    }
  },
  watch: {
    dialogVisible(value) {
      if (value && !this.flowList.length) {
        this.$api.SporadicProject.getworkFlowDesignModeList().then((res) => {
          if (res.code === '200') {
            this.flowList = res.data
          }
        })
      }
    }
  },
  methods: {
    onDialogClosed() {
      this.$refs.formRef.resetFields()
    },
    onSubmit() {
      this.$refs.formRef.validate()
        .catch(() => Promise.reject())
        .then(() => {
          this.loadingStatus = true
          const flowInfo = this.flowList.find(it => it.modelKey === this.formModel.flowKey) ?? {}
          const params = {
            code: this.formModel.code,
            name: this.formModel.name,
            remark: this.formModel.remark,
            status: this.formModel.status,
            // 工作流程
            flowKey: this.formModel.flowKey,
            flowDefinitionId: flowInfo.id,
            flowCategoryId: flowInfo.categoryId,
            flowModelName: flowInfo.name,
            formKey: flowInfo.formKey
          }
          return this.$api.SporadicProject.createProjectBusinessForm(params)
        })
        .then(res => {
          if (res.code === '200') {
            this.onSuccess(res.data.id)
          } else {
            throw res.msg
          }
        })
        .catch((msg) => {
          msg && this.$message.error(msg)
        })
        .finally(() => {
          this.loadingStatus = false
        })
    },
    onSuccess(id) {
      this.$emit('success')
      this.$confirm('是否开始进行表单数据配置？', '创建成功', {
        cancelButtonText: '稍后配置',
        confirmButtonText: '确定',
        type: 'success'
      })
        .then(() => {
          this.$emit('config', id)
        })
        .finally(() => {
          this.dialogVisible = false
        })
    }
  }
}
</script>
<template>
  <el-dialog
    v-dialogDrag
    class="component form-base"
    title="新增表单"
    width="750px"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    custom-class="model-dialog"
    @closed="onDialogClosed"
  >
    <el-form ref="formRef" :model="formModel" :rules="rules" label-width="95px">
      <el-row :gutter="12">
        <el-col :span="12">
          <el-form-item label="表单名称" prop="name">
            <el-input v-model="formModel.name" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="表单编码" prop="code">
            <el-input v-model="formModel.code" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="适用流程" prop="flowKey">
            <el-select v-model="formModel.flowKey" placeholder="请选择">
              <el-option v-for="item of flowList" :key="item.id" :value="item.modelKey" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select v-model="formModel.status" placeholder="请选择">
              <el-option v-for="item of statusOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="formModel.remark" type="textarea" :rows="4" placeholder="请输入" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button type="primary" plain @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="onSubmit">确认</el-button>
    </template>
  </el-dialog>
</template>
<style lang="scss">
.component.form-base {
  .el-form {
    background-color: #fff;
    padding: 10px 16px 0;
    .el-form-item {
      .el-select {
        width: 100%;
      }
    }
  }
}
</style>
