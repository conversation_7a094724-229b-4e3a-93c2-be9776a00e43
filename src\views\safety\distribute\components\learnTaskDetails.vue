<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="topFilter">
        <div class="backBar">
          <span style="cursor: pointer" @click="$router.go(-1)">
            <i class="el-icon-arrow-left"></i>
            学习任务详情
          </span>
        </div>
      </div>
      <div class="distribute_Detail">
        <div class="detail_heard">
          <div class="detail_heard_left">
            <div class="detail_title">{{ formInfo.name }}</div>
            <div class="detail_km">所属科目：<span class="detail_item">{{ formInfo.subjectName }}</span></div>
            <div class="detail_km">任务完成时间: <span class="detail_item">{{ formInfo.startTime }} 至
                {{ formInfo.endTime }}</span></div>
            <div class="detail_km">所属部门: <span class="detail_item">{{ formInfo.deptName }}</span></div>
          </div>
          <div class="detail_heard_right">
            <div class="detailxi">学习人员: <div class="detail_user">{{ studentNames }}</div>
            </div>
          </div>
        </div>
        <div class="sign_detail">
          <div style="display: flex; justify-content: space-between;">
            <div>学习课程进度: <span class="sign_lable">{{ formInfo.middle.courseSchedule.completedNum }} 人完成 </span> <span
                class="sign_item">{{ formInfo.middle.courseSchedule.uncompletedNum }} </span> 人未完成</div>
            <div>培训签到: <span class="sign_lable">{{ formInfo.middle.train.signInNum }} 人签到 </span> <span
                class="sign_item">{{ formInfo.middle.train.noSignInNum }}</span> 人未签到 </div>
            <div>考试记录: <span class="sign_lable">{{ formInfo.middle.examination.attendNum }} 人参与考试 </span> <span
                class="sign_item">{{ formInfo.middle.examination.noAttendNum }}</span> 人未参与考试 <span
                class="sign_lable">{{ formInfo.middle.examination.passNum }} 人通过考试 </span> <span class="sign_item">{{
                  formInfo.middle.examination.noPassNum }}</span> 人未通过考试 </div>
          </div>
        </div>
      </div>
      <div class="distribute_content">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="课程任务" name="0"></el-tab-pane>
          <el-tab-pane label="学习课程进度" name="1"></el-tab-pane>
          <el-tab-pane label="培训任务" name="2"></el-tab-pane>
          <el-tab-pane label="签到详情" name="3"></el-tab-pane>
          <el-tab-pane label="考试任务" name="4"></el-tab-pane>
          <el-tab-pane label="参考信息" name="5"></el-tab-pane>
        </el-tabs>
        <div class="tabs_title">
          <div class="course_title">
            <div>
              <span>{{ activeName == '0' ? '课程任务' : activeName == '1' ? '学习课程进度' : activeName == '2' ? '培训任务' : activeName == '3' ? '签到详情' : activeName == '4' ? '考试任务' : '参考信息' }}</span>
              <el-select v-model="courseId" v-if="activeName == '1'" clearable @change="selectCourse"
                style="margin-left: 12px;">
                <el-option v-for="(item, index) in courseList" :key="index" :label="item.courseName"
                  :value="item.id"></el-option>
              </el-select>
              <el-select v-model="trainId" v-if="activeName == '3'" clearable @change="selectTrain"
                style="margin-left: 12px;">
                <el-option v-for="(item, index) in trainList" :key="index" :label="item.name"
                  :value="item.taskId"></el-option>
              </el-select>
              <el-select v-model="examId" v-if="activeName == '5'" clearable @change="selectExam"
                style="margin-left: 12px;">
                <el-option v-for="(item, index) in examList" :key="index" :label="item.name"
                  :value="item.id"></el-option>
              </el-select>
            </div>
            <el-button type="primary" :disabled="multipleSelection.length < 1" @click="exportClickExport">导出</el-button>
          </div>
          <div class="course_content" v-if="activeName == '0'">
            <el-table :data="courseList" @selection-change="selectionChange" border ref="userTable"
              :row-key="getRowKeys" height="100%" width="100%">
              <el-table-column type="selection">
              </el-table-column>
              <el-table-column type="index" label="序号" width="70">
              </el-table-column>
              <el-table-column prop="courseName" label="课程名称">
              </el-table-column>
              <el-table-column prop="subjectName" label="课程类型">
              </el-table-column>
              <el-table-column prop="periodCount" label="课时数">
              </el-table-column>
              <el-table-column prop="createName" label="创建人">
              </el-table-column>
              <el-table-column prop="controlTeamName" label="操作">
                <template slot-scope="scope">
                  <div class="operateBths">
                    <el-link type="primary" @click="openDetails(scope.row)">查看</el-link>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="course_content" v-if="activeName == '1'">
            <el-table :data="userPeriodList" @selection-change="selectionChange" border ref="userTable"
              :row-key="getRowKeys" height="100%" width="100%">
              <el-table-column type="selection">
              </el-table-column>
              <el-table-column type="index" label="序号" width="70">
              </el-table-column>
              <el-table-column prop="userName" label="姓名">
              </el-table-column>
              <el-table-column prop="gender" label="性别">
                <template slot-scope="scope">
                  <span>{{ scope.row.gender == '0' ? '男' : '女' }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="mobilePhone" label="联系电话">
              </el-table-column>
              <el-table-column prop="controlTeamName" label="所属部门">
              </el-table-column>
              <el-table-column prop="periodNum" label="课程课时">
                <template slot-scope="scope">
                  <span>{{ scope.row.periodNum || 0 }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="schedule" label="学习进度">
                <template slot-scope="scope">
                  <span>{{ scope.row.schedule == '3' ? '已完成' : '学习中' }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="finishTime" label="学习完成时间">
                <template slot-scope="scope">
                  <span>{{ moment(scope.row.finishTime).format("YYYY-MM-DD") }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="controlTeamName" label="操作">
                <template slot-scope="scope">
                  <div class="operateBths">
                    <el-link type="primary" @click="openDetails(scope.row)">查看</el-link>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="course_content" v-if="activeName == '2'">
            <el-table :data="trainList" @selection-change="selectionChange" border ref="userTable" :row-key="getRowKeys"
              height="100%" width="100%">
              <el-table-column type="selection">
              </el-table-column>
              <el-table-column type="index" label="序号" width="70">
              </el-table-column>
              <el-table-column prop="name" label="培训名称">
              </el-table-column>
              <el-table-column prop="deptName" label="所属单位">
              </el-table-column>
              <el-table-column prop="subjectName" label="所属科目">
              </el-table-column>
              <el-table-column prop="principal" label="负责人员">
              </el-table-column>
              <el-table-column prop="controlTeamName" label="操作">
                <template slot-scope="scope">
                  <div class="operateBths">
                    <el-link type="primary" @click="openDetails(scope.row)">查看</el-link>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="course_content" v-if="activeName == '3'">
            <el-table :data="signsList" @selection-change="selectionChange" border ref="userTable" :row-key="getRowKeys"
              height="100%" width="100%">
              <el-table-column type="selection">
              </el-table-column>
              <el-table-column type="index" label="序号" width="70">
              </el-table-column>
              <el-table-column prop="name" label="姓名">
              </el-table-column>
              <el-table-column prop="gender" label="性别">
                <template slot-scope="scope">
                  <span>{{ scope.row.gender == '0' ? '男' : '女' }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="phone" label="联系电话">
              </el-table-column>
              <el-table-column prop="deptName" label="所属部门">
              </el-table-column>
              <el-table-column prop="signInstatus" label="签到状态">
                <template slot-scope="scope">
                  <span>{{ scope.row.signInstatus == '0' ? '未签到' : '已签到' }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="signTime" label="签到时间"></el-table-column>
              <el-table-column prop="controlTeamName" label="签到图片">
                <template slot-scope="scope">
                  <img class="imgClass" v-for="(item, index) in scope.row.fileList" :key="index" :src="item.viewAddress"
                    alt="">
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="course_content" v-if="activeName == '4'">
            <el-table :data="examList" @selection-change="selectionChange" border ref="userTable" :row-key="getRowKeys"
              height="100%" width="100%">
              <el-table-column type="selection">
              </el-table-column>
              <el-table-column type="index" label="序号" width="70">
              </el-table-column>
              <el-table-column prop="name" label="试卷名称">
              </el-table-column>
              <el-table-column prop="deptName" label="所属单位">
              </el-table-column>
              <el-table-column prop="subjectName" label="所属科目">
              </el-table-column>
              <el-table-column prop="duration" label="时长">
              </el-table-column>
              <el-table-column prop="taskStatusPc" label="考试状态">
                <template slot-scope="scope">
                  <span>{{ scope.row.taskStatusPc == '0' ? '未开始' : scope.row.taskStatusPc == '1' ? '进行中' : '已结束' }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="controlTeamName" label="操作">
                <template slot-scope="scope">
                  <div class="operateBths">
                    <el-link type="primary" @click="openDetails(scope.row)">查看</el-link>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="course_content" v-if="activeName == '5'">
            <el-table :data="referList" @selection-change="selectionChange" border ref="userTable" :row-key="getRowKeys"
              height="100%" width="100%">
              <el-table-column type="selection">
              </el-table-column>
              <el-table-column type="index" label="序号" width="70">
              </el-table-column>
              <el-table-column prop="name" label="姓名">
              </el-table-column>
              <el-table-column prop="" label="性别" align="center" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span>{{ scope.row.gender == '0' ? '男' : '女' }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="mobilePhone" label="联系电话" align="center" show-overflow-tooltip></el-table-column>
              <el-table-column prop="laboratoryName" label="所属部门" align="center"
                show-overflow-tooltip></el-table-column>
              <el-table-column prop="actualDuration" label="答题时长" align="center"
                show-overflow-tooltip></el-table-column>
              <el-table-column prop="actualScore" label="考试得分">
              </el-table-column>
              <el-table-column prop="controlTeamName" label="考试状态">
                <template slot-scope="scope">
                  <div class="statusBtn">
                    <span v-if="scope.row.pass == '1'" class="auditNo">
                      <img src="../../../../assets/images/icon-wrapper.png" alt="" />
                      未通过
                    </span>
                    <span v-if="scope.row.pass == '0'" class="relwase">
                      <img src="../../../../assets/images/pass.png" alt="">
                      通过</span>
                    <span v-if="scope.row.pass == '2'" class="relwaseNo">未参与</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="controlTeamName" label="操作">
                <template slot-scope="scope">
                  <div class="operateBths">
                    <el-link type="primary" @click="openDetails(scope.row)">查看</el-link>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>
    <div slot="footer">
      <el-button type="primary" @click="$router.go(-1)">取消</el-button>
    </div>
  </PageContainer>
</template>
<script>
import moment from "moment";
import axios from 'axios'
export default {
  beforeRouteEnter(to, from, next) {
    if (to.meta.title) {
      to.meta.title = to.query.type == "edit" ? "任务编辑" : "任务详情";
    }
    next();
  },
  data() {
    return {
      moment,
      activeName: '0',
      formInfo: {},
      courseList: [],
      examList: [],
      trainList: [],
      userPeriodAllList: [],
      userPeriodList: [],
      signsList: [],
      referList: [],
      id: '',
      multipleSelection: [],
      studentNames: '',
      courseId: '',
      trainId: '',
      examId: '',
      signsAllList: [],
      routeInfo: {}
    };
  },
  created() {
    this.routeInfo = JSON.parse(sessionStorage.getItem('routeInfo'))
    this.id = this.$route.query.id
    if (this.id) {
      this.getDetails()
    }
  },
  methods: {
    getDetails() {
      this.$api.getTaskInfo({ id: this.id }).then((res) => {
        if (res.code == '200') {
          this.formInfo = res.data
          this.studentNames = res.data.studentList.map(item => item.name).join(',')
          this.courseList = res.data.below.course || []
          this.trainList = res.data.below.trainTasks || []
          this.userPeriodAllList = res.data.below.userPeriodList || []
          this.userPeriodList = res.data.below.userPeriodList || []
          this.signsAllList = res.data.below.signs || []
          this.signsList = res.data.below.signs || []
          this.examList = res.data.below.examTasks || []
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    handleClick(tab, event) {
      this.multipleSelection = []
    },
    getRowKeys(row) {
      return row.id;
    },
    selectCourse(val) {
      if (val) {
        this.userPeriodList = this.userPeriodAllList.filter(item => item.courseId == val)
      } else {
        this.userPeriodList = this.userPeriodAllList
      }
    },
    selectTrain(val) {
      if (val) {
        this.signsList = this.signsAllList.filter(item => item.taskId == val)
      } else {
        this.signsList = this.signsAllList
      }
    },
    selectExam(val) {
      this.$api.getRecordInfo({ id: val }).then(res => {
        this.referList = res.data.examRecord
      })
    },
    selectionChange(val) {
      this.multipleSelection = val
    },
    openDetails(row) {
      if (this.activeName == '0') {
        this.$router.push({
          path: 'courseInfo',
          query: {
            id: row.id
          }
        })
      } else if (this.activeName == '1') {
        this.$router.push({
          path: 'courseSchedule',
          query: {
            id: row.courseId,
            userId: row.userId
          }
        })
      } else if (this.activeName == '2') {
        this.$router.push({
          path: 'trainPlanDetail',
          query: {
            id: row.planId,
          }
        })
      } else if (this.activeName == '4') {
        this.$router.push({
          path: "examDetails",
          query: {
            id: row.planId,
          },
        });
      } else if (this.activeName == '5') { //参考信息
        this.$router.push({
          path: "answerInfo",
          query: {
            respondent: row.respondent,
            recordId: this.examId //任务id
          },
        });
      }
    },
    exportClickExport() {
      let baseInfo = {
        hospitalCode: this.routeInfo.hospitalCode,
        unitCode: this.routeInfo.unitCode,
      }
      let url = ''
      let params = {}
      if (this.activeName == '0') {
        url = '/learnTask/courseExport'
        params = {
          courseIds: this.multipleSelection.map(item => item.id)
        }
      } else if (this.activeName == '1') {
        url = '/learnTask/courseScheduleExport'
        params = {
          ids: this.multipleSelection.map(item => item.id),
          learnTaskId: this.id
        }
      } else if (this.activeName == '2') {
        url = '/learnTask/trainTaskExport'
        params = {
          ids: this.multipleSelection.map(item => item.taskId),
          learnTaskId: this.id
        }
      } else if (this.activeName == '3') {
        url = '/learnTask/signInfoExport'
        params = {
          signIds: this.multipleSelection.map(item => item.id)
        }
      } else if (this.activeName == '4') {
        url = '/learnTask/examTaskExport'
        params = {
          ids: this.multipleSelection.map(item => item.id),
          learnTaskId: this.id
        }
      } else if (this.activeName == '5') {
        url = '/learnTask/referenceExport'
        params = {
          recordIds: this.multipleSelection.map(item => item.id)
        }
      }
      axios({
        method: "post",
        url: __PATH.BASE_URL_LABORATORY + url,
        data: { ...params, ...baseInfo },
        headers: {
          "Content-Type": "application/json",
          token: this.routeInfo.token,
        },
        responseType: 'blob',
      }).then((res) => {
        if (res.status == 200 && !res.data.code) {
          let name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
          let blob = new Blob([res.data]) // { type: "application/vnd.ms-excel" }
          let url = window.URL.createObjectURL(blob) // 创建一个临时的url指向blob对象
          // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
          let a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
          // 释放这个临时的对象url
          window.URL.revokeObjectURL(url)
        } else {
          this.$message.error('导出失败')
        }
      })
    }
  },
};
</script>
<style lang="scss" scoped>
.table-content {
  // background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
}

.topFilter {
  padding: 15px;
  height: 40px;
  background-color: #fff;

  .backBar {
    color: #333333;
    height: 100%;
    line-height: 100%;
    font-size: 15px;
    font-weight: 500;
  }
}

.distribute_Detail {
  height: 200px;
  margin-top: 2px;
  padding: 0 16px;
  background: #fff !important;
  border-radius: 4px;

  .detail_heard {
    display: flex;

    .detail_heard_left {
      width: 50%;
      margin-top: 15px;
    }

    .detail_heard_right {
      width: 50%;
      margin-top: 35px;
      line-height: 25px;
    }
  }
}

.sign_detail {
  height: 46px;
  background: #FAF9FC;
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
  line-height: 46px;
  font-size: 14px;
  padding: 0px 15px;
  box-sizing: border-box;
}

.sign_lable {
  color: #333333;
  font-weight: 400;
  font-size: 14px;
  margin-left: 15px;
}

.sign_item {
  margin-left: 15px;
  color: #FF6461;
}

.detail_title {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  margin: 10px 0px;
}

.detail_km {
  font-size: 14px;
  font-weight: 400;
  color: #666666;
  margin: 10px 0px;
}

.detail_item {
  font-size: 14px;
  color: #333333;
  font-weight: 400;
}

.detailxi {
  display: flex;
  font-size: 14px;
  font-weight: 400;
  color: #666666;
}

.detail_user {
  width: 90%;
  margin-left: 10px;
  height: 80px;
  overflow-y: auto;
  font-weight: 400;
  color: #333333;
  font-size: 14px;
}

.detail_user::-webkit-scrollbar {
  display: none
}

//隐藏滚动条
.detail_user::-webkit-scrollbar {
  //滚动条样式
  width: 5px;
  background: rgb(177, 188, 191);
}

.distribute_content {
  height: 490px;
  background-color: #3562DB;
  margin-top: 20px;
  padding: 0 16px;
  background: #fff !important;
  border-radius: 4px;
}

.tabs_title {
  height: calc(100% - 70px);
  margin-top: 15px;
}

.course_content {
  height: calc(100% - 60px);
  overflow: auto;
  margin-top: 16px;
}

.course_title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #333333;
  font-size: 16px;
}

.operateBths {
  color: #3562DB;

  .el-link {
    margin-right: 8px;
  }
}

.imgClass {
  width: 80px;
  height: 80px;
}

.statusBtn {
  text-align: center;
  font-size: 14px;
  display: flex;
  justify-content: center;

  .auditIng {
    width: 58px;
    height: 24px;
    background-color: #fff7e8;
    border-radius: 4px;
    color: #d25f00;
  }

  .auditNo {
    width: 78px;
    height: 24px;
    background-color: #ffece8;
    border-radius: 4px;
    color: #cb2634;

    img {
      vertical-align: middle;
    }
  }

  .relwase {
    width: 58px;
    height: 24px;
    background-color: #e8ffea;
    border-radius: 4px;
    color: #009a29;

    img {
      vertical-align: middle;
    }
  }

  .inProgress {
    width: 86px;
    height: 24px;
    background-color: #E6EFFC;
    border-radius: 4px;
    color: #2749BF;
  }

  .relwaseNo {
    width: 58px;
    height: 24px;
    background-color: #F2F4F9;
    border-radius: 4px;
    color: #86909C;
  }
}
</style>