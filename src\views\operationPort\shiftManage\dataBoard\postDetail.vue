<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="classese-content-title" @click="$router.go(-1)"><i class="el-icon-arrow-left"></i> {{ detailInfo.dutyAttendanceConfigName }}</div>
      <div class="content_box">
        <div class="postDetail__search">
          <el-form ref="formRef" :model="filter" inline>
            <el-form-item prop="queryTime" label="选择日期">
              <el-date-picker v-model="filter.queryTime" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" @change="queryTimeChange" placeholder="日期"> </el-date-picker>
            </el-form-item>
            <el-form-item prop="shiftStr" label="选择班次">
              <el-select v-model="filter.shiftStr" placeholder="全部班次" @change="shiftChange">
                <el-option v-for="item in shifteOptions" :key="item.signTimeStrKey" :label="item.signTimeStrKey" :value="item.signTimeStrValue"> </el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <div class="postDetail__statistics">
          <div>考勤组类型：{{ detailInfo.type ? attendanceGroupTypeList.find((v) => v.value == detailInfo.type)?.label : '' }}</div>
          <div class="postDetail__card">
            <div class="card_box" v-for="(item, index) in statisticalList" :key="index">
              <p>{{ item.name }}</p>
              <div class="infoBox">
                <div class="count">
                  <span>{{ detailInfo[item.value] }}</span
                  ><span>{{ item.unit }}</span>
                </div>
                <img :src="item.img" alt="" />
              </div>
            </div>
          </div>
        </div>
        <div class="postDetail__search">
          <el-form ref="formRef" :model="searchForm" inline @submit.native.prevent="name">
            <el-form-item prop="personName">
              <el-input placeholder="搜索成员姓名" suffix-icon="el-icon-search" v-model="searchForm.personName"> </el-input>
            </el-form-item>
            <el-form-item prop="status">
              <el-select v-model="searchForm.status" placeholder="全部状态">
                <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <div>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" plain @click="onReset">重置</el-button>
          </div>
        </div>
        <div class="postDetail__table">
          <el-table v-loading="tableLoadingStatus" height="380" :data="tableData" border stripe table-layout="auto" class="tableAuto" row-key="id">
            <el-table-column prop="userName" label="姓名" show-overflow-tooltip></el-table-column>
            <el-table-column prop="postName" label="岗位" show-overflow-tooltip></el-table-column>
            <el-table-column prop="dutyPostName" label="所在值班岗" show-overflow-tooltip></el-table-column>
            <el-table-column prop="departmentName" label="所属部门" show-overflow-tooltip></el-table-column>
            <el-table-column prop="workTime" label="实际工时（小时）" show-overflow-tooltip></el-table-column>
            <el-table-column prop="shiftName" label="出勤状态" show-overflow-toolti>
              <template #default="{ row }">
                <span class="postDetail__tag" :class="`postDetail__tag--${row.attendanceStatus}`">
                  {{ statusOptions.find((it) => it.value === row.attendanceStatus).label }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="dutyAttendanceConfigName" label="当前出勤值班考勤组" show-overflow-tooltip></el-table-column>
          </el-table>
          <el-pagination
            class="postDetail__pagination"
            :current-page="pagination.page"
            :page-sizes="pagination.pageSizeOptions"
            :page-size="pagination.size"
            :layout="pagination.layoutOptions"
            :total="pagination.total"
            @size-change="paginationSizeChange"
            @current-change="paginationCurrentChange"
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <div slot="footer"></div>
  </PageContainer>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin'
import satffImg from '@/assets/images/operationPort/dataBoard_crew.png'
import statisticsImg from '@/assets/images/operationPort/dataBoard_statistics.png'
import timeImg from '@/assets/images/operationPort/dataBoard_time.png'
import moment from 'moment'
import { attendanceGroupTypeList } from '../component/dict.js'

export default {
  name: 'postDetail',
  mixins: [tableListMixin],
  data() {
    return {
      attendanceGroupTypeList,
      searchForm: {
        personName: '', // 人员名称
        status: '' //状态
      },
      filter: {
        queryTime: '', //日期
        shiftStr: '' //班次
      },
      tableData: [],
      shifteOptions: [], //班次下拉
      //统计数据
      statisticalList: [
        {
          name: '当前在岗人数',
          value: 'realTimeCount',
          unit: '人',
          img: satffImg
        },
        {
          name: '峰值在岗人数',
          value: 'maxCount',
          unit: '人',
          img: satffImg
        },
        {
          name: '要求在岗人数',
          value: 'requiredCount',
          unit: '人',
          img: satffImg
        },
        {
          name: '实际人时/要求人时',
          value: 'personCount',
          img: statisticsImg
        },
        {
          name: '平均工时',
          value: 'avgWorkTime',
          unit: '小时',
          img: timeImg
        }
      ],
      statusOptions: [
        {
          label: '上班打卡成功',
          value: 0
        },
        {
          label: '下班打卡成功',
          value: 1
        },
        {
          label: '未打卡',
          value: 2
        },
        {
          label: '未在本组出勤',
          value: 3
        }
      ],
      tableLoadingStatus: false,
      detailInfo: {}, //详情数据
      queryParams: {}
    }
  },
  mounted() {
    this.filter.queryTime = moment(new Date()).format('YYYY-MM-DD')
    this.queryParams = this.$route.query
    this.init()
  },
  methods: {
    //初始化
    init() {
      this.getDetailData()
      this.getShiftList()
    },
    //改变日期和班次查询
    shiftChange() {
      this.getDetailData()
      this.getDataList()
    },
    queryTimeChange() {
      this.init()
      this.getDataList()
    },
    //获取班次下拉
    getShiftList() {
      this.$api.supplierAssess.getShiftData({ queryTime: this.filter.queryTime, id: this.queryParams.id }).then((res) => {
        if (res.code === '200') {
          this.shifteOptions = res.data
          this.filter.shiftStr = res.data[0] ? res.data[0].signTimeStrValue : []
          this.getDataList()
        }
      })
    },
    //获取详情数据
    getDetailData() {
      let params = {
        id: this.queryParams.id
      }
      this.$api.supplierAssess.getShiftGroupById(params).then((res) => {
        if (res.code === '200') {
          this.detailInfo = res.data
        }
      })
    },
    // 获取列表数据
    getDataList() {
      this.tableData = []
      this.tableLoadingStatus = true
      const params = {
        pageSize: this.pagination.size,
        page: this.pagination.current,
        id: this.queryParams.id,
        ...this.searchForm,
        ...this.filter
      }
      this.$api.supplierAssess
        .queryShiftDetailBypage(params)
        .then((res) => {
          this.tableLoadingStatus = false
          if (res.code === '200') {
            this.tableData = res.data.records
            this.pagination.total = res.data.total
          } else {
            this.tableData = []
            this.pagination.total = 0
          }
        })
        .catch((err) => {
          this.tableLoadingStatus = false
        })
    },
    // 表单搜索按钮点击
    onSearch() {
      this.pagination.current = 1
      this.getDataList()
    },
    // 表单重置按钮点击
    onReset() {
      this.$refs.formRef.resetFields()
      this.onSearch()
    }
  }
}
</script>
<style scoped lang="scss">
.container-content > div {
  background-color: #fff;
  height: 100%;
  position: relative;
  .classese-content-title {
    border-bottom: 1px solid #e4e7ed;
    padding: 12px 24px;
    cursor: pointer;
  }
  .content_box {
    padding: 16px 24px 24px 24px;
    background: #fff;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    height: calc(100% - 80px);
  }
  .postDetail {
    ::v-deep(> .container-content) {
      height: 100%;
      width: 100%;
      background-color: #fff;
    }
    &__card {
      display: flex;
      width: 100%;
      align-items: center;
      margin: 16px 0;
      .card_box {
        margin-right: 16px;
        width: calc((100% / 5) - 12px);
        height: 100%;
        background: rgba(53, 98, 219, 0.1);
        border-radius: 4px;
        font-weight: 500;
        padding: 16px 24px 18px 24px;
        font-size: 15px;
        color: #333333;
        .infoBox {
          display: flex;
          justify-content: space-between;
          .count {
            font-weight: bold;
            font-size: 24px;
            .unit {
              color: #c2c4c8 !important;
              font-weight: 500 !important;
              margin-left: 6px;
            }
          }
        }
      }
      .card_box:last-child {
        margin-right: 0px;
      }
    }
    &__tag {
      // 上班打卡成功
      &--0 {
        --color: #08cb83;
      }
      // 下班打卡成功
      &--1 {
        --color: #00b42a;
      }
      //未打卡
      &--2 {
        --color: #f64646;
      }
      // 未在本组出勤
      &--3 {
        --color: #f64646;
      }
      &:before {
        content: '';
        display: inline-block;
        vertical-align: middle;
        margin-right: 4px;
        height: 4px;
        width: 4px;
        border-radius: 4px;
        background-color: var(--color);
      }
    }
    &__pagination {
      margin-top: 10px;
    }
    &__table__color {
      display: inline-block;
      height: 16px;
      width: 16px;
      vertical-align: text-top;
      margin-right: 4px;
    }
    &__search {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  .el-form-item {
    margin-bottom: 8px !important;
  }
}
</style>

