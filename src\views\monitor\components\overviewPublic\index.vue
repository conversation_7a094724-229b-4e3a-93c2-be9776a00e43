<template>
  <PageContainer>
    <div v-if="rows.length" slot="content" class="container">
      <div class="title">监测总览</div>
      <!-- 监测总览 -->
      <el-row v-if="rows.length" v-loading="loading.overview" :gutter="16" class="overview">
        <el-col v-for="(item, index) in rows" :key="index" :xl="rows.length < 4 ? 24 / rows.length : 6" :lg="8" :md="12" :sm="12" :xs="24">
          <div class="item">
            <div class="semicircle">{{ item.sysName }}</div>
            <div class="chart_date" :style="{ 'justify-content': rows.length < 4 ? 'center' : 'space-between' }">
              <div class="chart_date_left">
                <!-- <span>{{[item.projectCode]}}</span> -->
                <img v-if="monitorItemImg[item.projectCode]" :src="monitorItemImg[item.projectCode]?.['on']" :alt="item.projectName" />
                <div class="eq_data">
                  <span>设备总数</span>
                  <div class="eq_data_p">{{ item.devicesNum }}</div>
                </div>
              </div>
              <div class="chart_date_right">
                <echarts
                  :ref="`overviewStatistics${index}`"
                  :domId="`overviewStatistics${index}`"
                  :onLegendClickBool="true"
                  @onClickChart="(data) => onClickChart(data)"
                  @onClickChartLegend="(data) => onClickChart(data)"
                />
              </div>
            </div>
            <div v-if="fireControl" class="distribution">
              <span>分布防区数</span>
              <div class="distribution_p">{{ item.defenceAreaNum }}</div>
            </div>
          </div>
        </el-col>
      </el-row>
      <div class="call_the_police">
        <div class="semicircle">报警统计（件）</div>
        <TimeQuery ref="timeQuery" @submit="submit" />
        <!-- :xs="24" :md="24" :lg="12" :xl="8" -->
        <el-row class="display_graphics">
          <el-col v-loading="loading.PoliceInfoLoading" class="display_graphics_left" :xs="12" :md="12" :lg="10" :xl="8">
            <div class="police">
              <div class="police_l" :class="gridTemplateRows ? 'twoLines' : 'aLine'">
                <div class="police_num">
                  <img src="@/assets/images/monitor/error.png" alt="" />
                  <div class="today_police">
                    <div>{{ dateText }}报警总数</div>
                    <span class="piece">{{ policeInfo.policeCount }}</span>
                  </div>
                </div>
                <div class="police_proportion">
                  <div class="police_proportion_box">
                    <span>同比</span>
                    <div class="data_float" :class="gridTemplateRows ? 'mart5' : 'marl5'">
                      <div :style="{ color: colorType(policeInfo.yoyStatus) }">{{ policeInfo.yoy }}</div>
                      <img v-if="policeInfo.yoyStatus == 0" src="@/assets/images/monitor/up.png" alt="" />
                      <img v-if="policeInfo.yoyStatus == 1" src="@/assets/images/monitor/down.png" alt="" />
                    </div>
                  </div>
                  <div class="police_proportion_box">
                    <span>环比</span>
                    <div class="data_float" :class="gridTemplateRows ? 'mart5' : 'marl5'">
                      <div :style="{ color: colorType(policeInfo.ringRatioStatus) }">{{ policeInfo.ringRatio }}</div>
                      <img v-if="policeInfo.ringRatioStatus == 0" src="@/assets/images/monitor/up.png" alt="" />
                      <img v-if="policeInfo.ringRatioStatus == 1" src="@/assets/images/monitor/down.png" alt="" />
                    </div>
                  </div>
                </div>
              </div>
              <div style="display: flex">
                <div class="police_add" @click="jumpAlarmRecord(projectSplic)">
                  <p>未处理报警</p>
                  <span class="piece">{{ policeInfo.unDisposedCount }}</span>
                </div>
                <!-- <div class="police_add">
                  <p>新增报警</p>
                  <span class="piece">4</span>
                </div> -->
              </div>
            </div>
            <div class="callThePolice">
              <echarts
                :ref="`callThePolice`"
                :domId="`callThePolice`"
                :onLegendClickBool="true"
                :isMonitor="false"
                @onClickChartLegend="(data) => jumpAlarmRecord(data.data.projectCode, queryParams.dateRange)"
                @onClickChart="(data) => jumpAlarmRecord(data.data.projectCode, queryParams.dateRange)"
              />
            </div>
          </el-col>
          <el-col v-loading="loading.PoliceTrendLoading" class="display_graphics_right" :xs="24" :md="24" :lg="14" :xl="16">
            <echarts :ref="`alarmTrend`" :domId="`alarmTrend`" :isMonitor="false" @onClickChart="(data) => jumpAlarmRecord(projectSplic, queryParams.dateRange)" />
            <div class="location_sele">
              <el-select v-model="hourOrMouth" size="mini" placeholder="时间" @change="onPoliceTrend">
                <el-option v-for="item in Xdisplay" :key="item.value" :disabled="!item.disabled" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </div>
          </el-col>
        </el-row>
      </div>
      <OffLineDialog ref="offLineDialog"></OffLineDialog>
      <NormalDialog ref="normalDialog"></NormalDialog>
    </div>
  </PageContainer>
</template>
<script>
import mixins from './mixins/chartMixin.js'
import OffLineDialog from './offLineDialog.vue'
import NormalDialog from './normalDialog.vue'
import TimeQuery from './timeQuery.vue'
import { monitorItemImg, monitorTypeList } from '@/util/dict.js'
import { auth } from '@/util'
export default {
  name: 'overviewPublic',
  components: {
    OffLineDialog, // 详情弹窗
    TimeQuery, // 时间选择
    NormalDialog // 正常
  },
  mixins: [mixins],
  props: {
    fireControl: {
      type: Number,
      default: 0
    },
    projectName: {
      type: String,
      default: () => {
        throw '必须参数projectName'
      }
    }
  },
  data() {
    return {
      monitorItemImg,
      // 设备
      rows: [],
      Xdisplay: [
        { value: 0, label: '24小时', disabled: true, fn: (diffDays) => diffDays <= 0 },
        { value: 1, label: '日', disabled: false, fn: (diffDays) => diffDays > 0 },
        { value: 2, label: '月', disabled: false, fn: (diffDays) => diffDays > 0 }
        // {value: 1, label: '日', disabled: false, fn: (diffDays) => diffDays > 0 && diffDays < 30 * 7 },
        // {value: 2, label: '月', disabled: false, fn: (diffDays) => diffDays > 30 }
      ],
      hourOrMouth: 0,
      // 报警统计 需要的projectCode拼接字段
      projectSplic: '',
      // diffDays: null
      // 报警数
      policeInfo: {},
      // 报警数日期描述
      dateText: null,
      // 入参
      queryParams: {},
      loading: {
        overview: false,
        PoliceInfoLoading: false,
        PoliceTrendLoading: false
      }
    }
  },
  computed: {
    // 显示一行还是两行
    gridTemplateRows() {
      let rowsNum = Math.ceil(this.rows.length / 4)
      return rowsNum
    }
  },
  created() {
    this.getProject()
  },
  mounted() {
    // 查询设备 projectCode
  },
  methods: {
    // 跳转报警中心
    jumpAlarmRecord(projectSplic, dateRange) {
      this.$router.push({
        path: '/allAlarm/allAlarmIndex',
        query: {
          projectCode: projectSplic,
          dataRange: dateRange
        }
      })
    },
    // 颜色样式
    colorType(type) {
      let colorObj = {
        0: '#FA403C',
        1: '#00BC6D',
        2: ''
      }
      return colorObj[type]
    },
    getCountMonitoringNum() {
      this.loading.overview = true
      this.$api.CountMonitoringNum({ projectCode: this.projectSplic }).then((res) => {
        if (res.code == 200) {
          this.rows = res.data
          this.$nextTick(() => {
            this.getEchartsData()
            this.loading.overview = false
          })
        }
      })
    },
    /**
     * 查找所有设备的projectCode
     */
    getProject() {
      let projectObj = monitorTypeList.find((ele) => ele.projectName == this.projectName)
      let arr = monitorTypeList.filter((ele) => ele.parentCode == projectObj.projectCode && auth(ele.menuAuth || ''))
      this.projectSplic = arr.map((x) => x.projectCode).join(',')
      this.getCountMonitoringNum()
    },
    /**
     * 批量pie图点击
     */
    onClickChart(data) {
      // console.log('data==========', data)
      if (data.data.name == '正常') {
        this.$refs.normalDialog.getEchartData(data.data)
      } else {
        this.$refs.offLineDialog.getEchartData(data.data, this.queryParams)
      }
    },
    getEcharPieBig(arr) {
      let obj
      let data = []
      let total = 0
      arr.forEach((ele) => {
        // 测试数据
        // let a = Math.round(Math.random() * 99)
        total += ele.policeCount
        // 测试数据
        // total += a
        obj = {
          name: ele.projectName,
          projectCode: ele.projectCode,
          value: ele.policeCount,
          // 测试数据
          // value: a,
          proportion: '',
          ringRatio: ele.ringRatio,
          ringRatioStatus: ele.ringRatioStatus
        }
        data.push(obj)
      })
      this.$refs.callThePolice.init(this.setPieChart('个', data, total))
    },
    // 报警属
    onPoliceInfo() {
      this.loading.PoliceInfoLoading = true
      this.$api.GetPoliceInfo(this.queryParams).then((res) => {
        if (res.code == 200) {
          this.policeInfo = res.data
          this.getEcharPieBig(res.data.projectList)
          this.loading.PoliceInfoLoading = false
        }
      })
    },
    // 报警趋势
    onPoliceTrend() {
      this.loading.PoliceTrendLoading = true
      var data = { ...this.queryParams, hourDayOrMouth: this.hourOrMouth }
      this.$api.GetPoliceTrend(data).then((res) => {
        if (res.code == 200) {
          this.getEcharLineBig(res.data)
          this.loading.PoliceTrendLoading = false
        }
      })
    },
    //
    getEcharLineBig(data) {
      this.$refs.alarmTrend.init(this.setLineChart(data, []))
    },
    /**
     * 查询 | 调接口
     */
    submit(data) {
      this.dateText = data.dateText
      // this.diffDays = data.diffDays
      // 控制右侧时间选择下拉
      let bool
      this.Xdisplay.forEach((ele, index) => {
        bool = ele.fn(data.diffDays)
        ele.disabled = bool
        bool ? (this.hourOrMouth = ele.value) : ''
      })
      data['projectCode'] = this.projectSplic
      this.queryParams = data
      this.onPoliceInfo()
      this.onPoliceTrend()
    },
    getEchartsData() {
      this.rows.forEach((ele, index) => {
        let data = [
          { name: '正常', value: ele.onLineNum, projectCode: ele.projectCode, sysName: ele.sysName },
          { name: '离线', value: ele.offLineNum, projectCode: ele.projectCode, sysName: ele.sysName }
        ]
        if (ele.projectCode == 'IEMC-ElectricitySafe') {
          // 用电安全
          data.push({ name: '异常', value: ele.abnormalCount, projectCode: ele.projectCode, sysName: ele.sysName })
        }
        this.$nextTick(() => {
          // console.log(this.$refs[`overviewStatistics${index}`])
          this.$refs[`overviewStatistics${index}`][0].init(this.setBatchPieChart('个', data, [], ['#3562DB', '#CBCED3', '#FF6461']))
        })
      })
    }
  }
}
</script>
<style scoped lang="scss">
.aLine {
  flex-direction: column;
  .police_proportion_box {
    margin-top: 20px;
    align-items: center;
  }
}
.twoLines {
  align-items: center;
  .police_proportion_box {
    flex-direction: column;
  }
}
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: scroll;
  overflow-x: hidden;
}
.semicircle::before {
  content: ' ';
  display: inline-block;
  width: 7px;
  height: 13px;
  background: #3562db;
  border-radius: 0 7px 7px 0;
  opacity: 1;
  margin-right: 4px;
}
.title {
  font-size: 14px;
  font-family: 'PingFang SC-Medium', 'PingFang SC';
  font-weight: 500;
  color: #121f3e;
  line-height: 14px;
  margin-bottom: 16px;
}
.item,
.call_the_police {
  background: white;
  padding: 15px;
}
.overview {
  .item {
    background: white;
    padding: 15px;
    margin-bottom: 10px;
    .semicircle {
      font-size: 15px;
      display: flex;
      align-items: center;
    }
    .chart_date {
      display: flex;
      justify-content: space-between;
      margin: 10px 0;
      height: 80px;
      &_left {
        display: flex;
        height: 100%;
        margin-right: 20px;
        img {
          height: 60px;
          width: 60px;
          margin: auto;
          margin-right: 15px;
        }
        .eq_data {
          display: flex;
          flex-direction: column;
          justify-content: center;
          span {
            font-size: 15px;
            line-height: 18px;
          }
          &_p {
            width: 90px;
            font-size: 30px;
            font-weight: bold;
            line-height: 36px;
          }
          &_p::after {
            content: '个';
            font-size: 15px;
            font-weight: 400;
            color: #ccced3;
            line-height: 18px;
          }
        }
      }
      &_right {
        width: 170px;
        height: 100%;
      }
    }
    .distribution {
      display: flex;
      justify-content: center;
      align-items: center;
      background: #faf9fc;
      border-radius: 4px;
      padding: 10px 0;
      span {
        font-size: 15px;
        line-height: 15px;
        color: #121f3e;
        margin-right: 8px;
      }
      .distribution_p {
        font-size: 18px;
        font-family: Arial-Bold, Arial;
        font-weight: bold;
        color: #121f3e;
      }
      .distribution_p::after {
        content: '个';
        font-size: 14px;
        font-weight: 400;
        margin-left: 2px;
        color: #ccced3;
      }
    }
  }
}
.call_the_police {
  // margin-top: 5px;
  flex: 1;
  display: flex;
  flex-direction: column;
  .display_graphics {
    flex: 1;
    // overflow: auto;
    // display: flex;
    &_left {
      // width: 28%;
      // margin-right: 1%;
      height: 100%;
      // flex: 1;
      display: flex;
      flex-direction: column;
      .police {
        width: 100%;
        padding: 20px;
        background: #faf9fc;
        border-radius: 4px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        &_l {
          // height: 60px;
          display: flex;
          // align-items: center;
          .police_num {
            display: flex;
            margin-right: 20px;
            img {
              height: 100%;
              width: 60px;
              margin-right: 5px;
            }
            .today_police {
              div {
                font-size: 14px;
              }
              span {
                font-size: 30px;
                font-weight: bold;
              }
            }
          }
          .police_proportion {
            display: flex;
            &_box {
              display: flex;
              margin-right: 20px;
              span {
                font-size: 12px;
                color: #414653;
              }
              .marl5 {
                margin-left: 5px;
              }
              .mart5 {
                margin-top: 5px;
              }
              .data_float {
                display: flex;
                div {
                  // color: #00bc6d;
                  margin-right: 8px;
                }
                img {
                  width: 20px;
                  height: 20px;
                }
              }
            }
          }
        }
        .police_add {
          cursor: pointer;
          p {
            margin: 0;
            font-size: 12px;
            color: #414653;
          }
          span {
            font-size: 18px;
            font-weight: bold;
          }
        }
      }
      .callThePolice {
        flex: 1;
        width: 100%;
        margin-top: 20px;
      }
    }
    &_right {
      position: relative;
      height: 100%;
      // width: 72%;
      .location_sele {
        position: absolute;
        right: 0;
        top: 0;
        z-index: 50;
        .el-select {
          width: 90px;
        }
      }
    }
  }
}
.piece::after {
  content: '件';
  font-size: 14px;
  font-family: 'PingFang SC-Medium', 'PingFang SC';
  font-weight: 500;
  margin-left: 5px;
  color: #ccced3;
}
// 不知名问题  疑似被其他组件css冲掉  后期解决
::v-deep .el-input__icon {
  height: unset !important;
}
</style>
