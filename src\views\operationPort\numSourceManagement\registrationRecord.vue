<template>
    <PageContainer :footer="true">
        <div slot="content" class="role-content" style="height: 100%">
            <div class="role-content-right">
                <div style="height: 100%">
                    <div class="search-from">
                        <div>
                            <el-input v-model.trim="seachFrom.nameorCode" placeholder="患者名称/编码"
                                style="width: 200px; margin-right: 10px" clearable></el-input>
                            <el-date-picker v-model="seachFrom.datatime" type="date" value-format="yyyy-MM-dd"
                                popper-class="timePicker" style="width: 200px; margin-right: 10px" placeholder="选择日期">
                            </el-date-picker>
                            <el-cascader ref="icmSpace" v-model="seachFrom.positionId" :show-all-levels="false"
                                placeholder="就诊位置" :options="positionList" :props="props1" clearable collapse-tags
                                style="width: 200px; margin-right: 10px"></el-cascader>
                            <el-select v-model="seachFrom.regMethodId" placeholder="挂号方式"
                                style="width: 200px; margin-right: 10px">
                                <el-option v-for="item in regMethodList" :key="item.value" :label="item.label"
                                    :value="item.value"> </el-option>
                            </el-select>
                            <el-select v-model="seachFrom.regStatus" placeholder="挂号状态"
                                style="width: 200px; margin-right: 10px">
                                <el-option v-for="item in regStatusList" :key="item.value" :label="item.label"
                                    :value="item.value"> </el-option>
                            </el-select>
                            <el-button type="primary" plain @click="resetForm">重置</el-button>
                            <el-button type="primary" @click="search">查询</el-button>
                        </div>
                    </div>
                    <div class="contentTable">
                        <div class="contentTable-main table-content">
                            <el-table v-loading="tableLoading" border style="width: 100%" :data="tableData"
                                height="100%" stripe>
                                <el-table-column prop="caseCode" label="病例编码" show-overflow-tooltip></el-table-column>
                                <el-table-column prop="patientName" label="患者名称"
                                    show-overflow-tooltip></el-table-column>
                                <el-table-column prop="registType" label="挂号方式" show-overflow-tooltip>
                                    <template slot-scope="scope">
                                        <span>{{ scope.row.registType == 0 ? '现场号' : '网约号' }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="visitStatus" label="初复诊" show-overflow-tooltip>
                                    <template slot-scope="scope">
                                        <span>{{ scope.row.visitStatus == '1' ? '初诊' : '复诊' }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="doctorName" label="主治医生" show-overflow-tooltip></el-table-column>
                                <el-table-column prop="positionName" label="就诊位置"
                                    show-overflow-tooltip></el-table-column>
                                <el-table-column prop="chairName" label="就诊椅位" show-overflow-tooltip></el-table-column>
                                <!-- <el-table-column prop="" label="门诊号" show-overflow-tooltip></el-table-column> -->
                                <el-table-column prop="" label="预约时段" show-overflow-tooltip>
                                    <template slot-scope="scope">
                                        <span>{{ scope.row.appointmentDate }}&nbsp;{{ scope.row.appointmentBeginTime
                                            }}-{{ scope.row.appointmentEndTime }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="status" label="挂号状态" show-overflow-tooltip>
                                    <template slot-scope="scope">
                                        <span>{{ scope.row.status | filterStatus }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="createTime" label="挂号时间" show-overflow-tooltip></el-table-column>
                            </el-table>
                        </div>
                        <div class="contentTable-footer">
                            <el-pagination :current-page="pagination.current" :page-sizes="pagination.pageSizeOptions"
                                :page-size="pagination.size" :layout="pagination.layoutOptions"
                                :total="pagination.total" @size-change="paginationSizeChange"
                                @current-change="paginationCurrentChange">
                            </el-pagination>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div slot="footer">
            <el-button type="primary" @click="$router.go(-1)">取消</el-button>
        </div>
    </PageContainer>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin.js'
import moment from 'moment'
export default {
    name: 'registrationRecord',
    filters: {
        filterStatus(val) {
            return val == '0' ? '已挂号' : val == '-1' ? '已退号' : val == '1' ? '候诊' : val == '2' ? '就诊中' : '已结束'
        }
    },
    mixins: [tableListMixin],
    data() {
        return {
            seachFrom: {
                nameorCode: '', // 名称/编码/诊室,
                datatime: moment().format('YYYY-MM-DD'), // 日期
                positionId: '', // 位置
                regMethodId: '', // 挂号方式
                regStatus: '' // 挂号状态
            },
            positionList: [],
            regMethodList: [
                {
                    label: '现场号',
                    value: '0'
                },
                {
                    label: '网约号',
                    value: '1'
                }
            ],
            regStatusList: [
                {
                    label: '已挂号',
                    value: '0'
                },
                {
                    label: '已退号',
                    value: '-1'
                },
                {
                    label: '候诊',
                    value: '1'
                },
                {
                    label: '就诊中',
                    value: '2'
                },
                {
                    label: '已结束',
                    value: '3'
                }
            ],
            tableLoading: false,
            tableData: [],
            doctorCode: '',
            props1: {
                checkStrictly: false,
                emitPath: false,
                multiple: true,
                value: 'id',
                label: 'ssmName',
                children: 'list'
            }
        }
    },
    created() {
        this.doctorCode = this.$route.query.doctorCode
        this.seachFrom.datatime = this.$route.query.datatime
        this.treeDataForStaff()
        this.getDataList()
    },
    methods: {
        treeDataForStaff() {
            this.$api.getAllSpaceTree().then((res) => {
                if (res.code === 200) {
                    this.positionList = this.$tools.transData(res.data, 'id', 'pid', 'list')
                }
            })
        },
        getDataList() {
            let params = {
                doctorCode: this.doctorCode,
                searchKeyword: this.seachFrom.nameorCode,
                appointmentDate: this.seachFrom.datatime,
                positionId: this.seachFrom.positionId ? this.seachFrom.positionId.join(',') : '',
                registType: this.seachFrom.regMethodId,
                status: this.seachFrom.regStatus,
                page: this.pagination.current,
                pageSize: this.pagination.size
            }
            this.tableLoading = true
            this.$api.getPatientPage(params).then((res) => {
                if (res.code == '200') {
                    this.tableData = res.data.records
                    this.pagination.total = parseInt(res.data.total)
                } else {
                    this.$message.error(res.message)
                }
            })
            this.tableLoading = false
        },
        // 重置
        resetForm() {
            this.seachFrom.nameorCode = ''
            this.seachFrom.datatime = moment().format('YYYY-MM-DD')
            this.seachFrom.positionId = ''
            this.seachFrom.regMethodId = ''
            this.seachFrom.regStatus = ''
            this.pagination.size = 15
            this.pagination.current = 1
            this.getDataList()
        },
        // 查询
        search() {
            this.pagination.current = 1
            this.getDataList()
        },
        // 操作记录分页
        pagingLoad() {
            if (this.activities.length < this.drawerTotal) {
                this.drawerPageNo += 1
            }
        }
    }
}
</script>
<style lang="scss" scoped>
.el-tree-node__content {
    width: 100%;

    .custom-tree-node {
        display: inline-block;
        width: 100%;
        overflow: hidden;

        .item {
            display: inline-block;
            width: calc(100%);
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
        }
    }
}

::v-deep .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
    color: #3562db;
    background: linear-gradient(to right, #d9e1f8, #fff);
}

::v-deep .el-tree-node__content {
    height: 32px;
}

.role-content {
    height: 100%;
    display: flex;

    .role-content-right {
        height: 100%;
        min-width: 0;
        padding: 20px;
        background: #fff;
        border-radius: 4px;
        flex: 1;

        .search-from {
            padding-bottom: 12px;

            &>div {
                margin-right: 10px;
            }

            &>button {
                margin-top: 12px;
            }
        }

        .contentTable {
            height: calc(100% - 40px);
            display: flex;
            flex-direction: column;

            .contentTable-main {
                flex: 1;
                overflow: auto;
            }

            .contentTable-footer {
                padding: 10px 0 0;
            }
        }
    }

    .content {
        width: 100%;
        max-height: 500px !important;
        overflow: auto;
        background-color: #fff !important;
    }
}

::v-deep .el-drawer__body {
    background-color: #fff;
    margin: 20px;
    padding: 20px 10px;
    height: calc(100% - 80px);
    overflow-y: auto;
}

::v-deep .el-timeline-item__timestamp.is-bottom {
    font-size: 14px;
    position: absolute;
    left: -100px;
    top: -5px;
    font-weight: 600;
    color: #121f3e;
}

::v-deep .el-timeline {
    padding-left: 120px;
}

.timeContent {
    height: 100%;
    overflow: auto;

    .time {
        font-size: 14px;
        font-family: 'PingFang SC-Regular', 'PingFang SC';
        color: #414653;
    }

    .continer {
        display: flex;
        flex-wrap: wrap;

        .item {
            height: 32px;
            flex: 1;
            padding: 0 16px;
            background-color: #faf9fc;
            line-height: 32px;
            white-space: nowrap;
            margin-bottom: 20px;
            margin-right: 10px;
        }

        .itemContent {
            height: 32px;
            width: 220px;
            padding: 0 16px;
            background-color: #faf9fc;
            line-height: 32px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            cursor: pointer;
            margin-right: 10px;
        }
    }
}

::v-deep .el-timeline-item__node--normal {
    background: #fff;
    border: 2px solid #3562db;
}

.noData {
    display: inline-block;
    padding-bottom: 10px;
    width: 100%;
    margin: 0;
    font-size: 14px;
    color: #999;
    text-align: center;
}

.record {
    color: #66b1ff !important;
}

.delete {
    color: red !important;
}

::v-deep .el-tree-node {
    white-space: normal;
}

.rightBtn {
    height: 36px;
    margin: 0;
    margin-right: 10px;
}

.leadFile {
    display: flex;
}

.leadFile_item {
    margin: 10px 35px;
    color: #66b1ff;
    cursor: pointer;
}

::v-deep .el-message-box.no-close-btn .el-message-box__headerbtn {
    display: none;
}

::v-deep .el-cascader {
    line-height: 32px;

    .el-input__inner {
        height: 32px !important;
    }
}
</style>
