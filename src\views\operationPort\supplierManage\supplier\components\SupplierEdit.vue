<script>
import { UsingStatusOptions } from '@/views/operationPort/constant'
// 工程类型字典类型
const DICT_TYPE = 2
export default {
  name: 'SupplierEdit',
  components: {
    SelectPeople: () => import('../../../exerciseManage/exercisePlan/components/SelectPeople')
  },
  props: {
    id: Number,
    visible: <PERSON><PERSON><PERSON>,
    readonly: <PERSON><PERSON><PERSON>
  },
  events: ['update:visible', 'success'],
  data: function() {
    return {
      formModel: {
        fullName: '',
        simpleName: '',
        code: '',
        principal: '',
        principalId: '',
        guaranteeYears: '',
        status: 1,
        remark: '',
        scopeIds: []
      },
      rules: {
        fullName: [{ required: true, message: '请输入供应商名称' }],
        simpleName: [{ required: true, message: '请输入供应商简称' }],
        code: [{ required: true, message: '请输入供应商编码' }],
        scopeIds: [{ required: true, message: '请选择业务范围' }],
        guaranteeYears: [{ required: true, message: '请输入维保年限' }],
        principal: [{ required: true, message: '请输入供应商负责人' }]
      },
      dictData: [],
      loadingStatus: false,
      statusOptions: UsingStatusOptions,
      // 人员选择dialog显示
      userDialog: false,
      // timerId
      $timerId: -1
    }
  },
  computed: {
    title: function() {
      if (this.readonly) {
        return '查看供应商'
      } else {
        return this.id ? '编辑供应商' : '新建供应商'
      }
    },
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    },
    toFetch() {
      return this.dialogVisible && this.id
    }
  },
  watch: {
    toFetch(val) {
      val && this.getDetail()
    },
    dialogVisible(value) {
      if (value && !this.dictData.length) {
        this.getBusinessDictData()
      }
    }
  },
  methods: {
    // 获取数据字典
    getBusinessDictData() {
      this.$api.SporadicProject.getDictConfigData({ type: DICT_TYPE }).then((res) => {
        if (res.code === '200') {
          const data = res.data || []
          // 递归转换内容，设置禁用项
          function transItem(item) {
            item.disabled = item.status === 0
            if (item.children && item.children.length > 0) {
              item.children.forEach(transItem)
            }
          }
          data.forEach(transItem)
          this.dictData = data
        }
      })
    },
    getDetail() {
      this.loadingStatus = true
      this.$api.SporadicProject.selectProjectSupplierOne({ id: this.id })
        .then((res) => {
          if (res.code === '200') {
            this.formModel.fullName = res.data.name
            this.formModel.simpleName = res.data.shortName
            this.formModel.code = res.data.code
            this.formModel.principal = res.data.leader
            this.formModel.principalId = res.data.leaderId
            if (res.data.businessIds) {
              this.formModel.scopeIds = res.data.businessIds.split(',').map(Number)
            }
            this.formModel.status = +res.data.state
            this.formModel.guaranteeYears = res.data.serviceDate
            this.formModel.remark = res.data.remark
          } else {
            throw res.msg
          }
        })
        .catch((msg) => this.$message.error(msg || '获取供应商详情失败'))
        .finally(() => (this.loadingStatus = false))
    },
    onDialogClosed() {
      // dialog关闭时重置form表单
      this.$refs.formRef.resetFields()
    },
    onSubmit() {
      this.$refs.formRef
        .validate()
        .catch(() => Promise.reject())
        .then(() => {
          // config request data
          const params = {
            businessIds: this.formModel.scopeIds.join(),
            bussiness: this.formModel.scopeIds.slice(-1)[0],
            code: this.formModel.code,
            leader: this.formModel.principal,
            leaderId: this.formModel.principalId,
            name: this.formModel.fullName,
            remark: this.formModel.remark,
            serviceDate: this.formModel.guaranteeYears,
            shortName: this.formModel.simpleName,
            state: this.formModel.status
          }
          this.loadingStatus = true
          // do request
          if (this.id) {
            params.id = this.id
            return this.$api.SporadicProject.updateProjectSupplier(params)
          } else {
            return this.$api.SporadicProject.saveProjectSupplier(params)
          }
        })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('操作成功')
            this.$emit('success')
            this.dialogVisible = false
          } else {
            throw res.msg || '操作失败'
          }
        })
        .catch((msg) => {
          msg && this.$message.error(msg)
        })
        .finally(() => {
          this.loadingStatus = false
        })
    },
    // 选择人员
    onSelectUser() {
      this.userDialog = true
      // fix 选择人员的 modal 没有禁用，会影响视觉
      this.$timerId = setTimeout(() => {
        // 从文档根元素查找modal节点，如果有则模拟触发点击
        const modalEl = document.querySelector('div.v-modal')
        if (modalEl) {
          modalEl.click()
        }
      }, 100)
    },
    // 人员弹窗点击确定
    onSelectUserSubmit(list) {
      if (!list.length) {
        this.$message.error('请选择负责人')
      } else if (list.length > 1) {
        this.$message.error('负责人只能选择一个')
      } else {
        const user = list[0]
        this.formModel.principalId = user.id
        this.formModel.principal = user.staffName
        this.userDialog = false
      }
    },
    // 人员弹窗点击取消
    onSelectUserCancel() {
      this.userDialog = false
    }
  }
}
</script>
<template>
  <el-dialog
    v-dialogDrag
    class="component supplier-edit"
    :title="title"
    width="750px"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    custom-class="model-dialog"
    destroy-on-close
    @close="onDialogClosed"
  >
    <el-form ref="formRef" :model="formModel" :rules="rules" label-width="95px" :disabled="readonly" :class="{ readonly }">
      <el-form-item label="供应商名称" prop="fullName">
        <el-input v-model="formModel.fullName" placeholder="请输入"></el-input>
      </el-form-item>
      <el-row :gutter="12">
        <el-col :span="12">
          <el-form-item label="简称" prop="simpleName">
            <el-input v-model="formModel.simpleName" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="编码" prop="code">
            <el-input v-model="formModel.code" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="业务范围" prop="scopeIds">
            <el-cascader
              ref="cascaderRef"
              v-model="formModel.scopeIds"
              :options="dictData"
              :props="{ label: 'name', checkStrictly: true, value: 'id' }"
              :placeholder="readonly ? '-' : '请选择'"
              :show-all-levels="false"
              clearable
            >
            </el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="负责人" prop="principal">
            <el-input :value="formModel.principal" readonly placeholder="请选择" suffix-icon="el-icon-arrow-down" @focus="onSelectUser"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="维保年限" prop="guaranteeYears">
            <el-input v-model="formModel.guaranteeYears" placeholder="请输入" :maxlength="2" onkeyup="value=value.replace(/^0|\D/,'')">
              <template #append>年</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select v-model="formModel.status" placeholder="请选择">
              <el-option v-for="item of statusOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="formModel.remark" type="textarea" :rows="4" :placeholder="readonly ? '-' : '请输入'"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!--选择人员 -->
    <SelectPeople class="select-people" :person-dialog-show.sync="userDialog" @submitPersonDialog="onSelectUserSubmit" @closePersonDialog="onSelectUserCancel" />
    <template #footer>
      <el-button type="primary" plain @click="dialogVisible = false">取消</el-button>
      <el-button v-if="!readonly" type="primary" @click="onSubmit">确认</el-button>
    </template>
  </el-dialog>
</template>
<style lang="scss">
.component.supplier-edit {
  .el-form {
    background-color: #fff;
    padding: 10px 16px 0;
    .el-form-item {
      .el-cascader,
      .el-select {
        width: 100%;
      }
      .el-form-item__content .el-input-group {
        vertical-align: middle;
      }
    }
    &.readonly {
      .el-form-item {
        .el-form-item__label::before {
          display: none;
        }
        .el-input__inner,
        .el-cascader,
        .el-select {
          background-color: transparent;
          border: none;
        }
        .el-input__suffix {
          display: none;
        }
        .el-upload-list__item-status-label {
          display: none;
        }
        .el-upload__tip {
          display: none;
        }
        .el-input-group {
          width: auto;
          .el-input__inner {
            width: 50px;
          }
          .el-input-group__append {
            padding: 0;
            border: none;
            background-color: transparent;
          }
        }
        .el-textarea__inner {
          background-color: transparent;
          border: none;
          padding-top: 10px;
        }
      }
    }
  }
  // fix select people content layout
  .select-people {
    .el-dialog__body {
      .content {
        padding: 16px;
        overflow: hidden;
        .leftContent {
          .topSearch {
            padding: 0;
          }
          .treeBox {
            margin-top: 16px;
          }
        }
        .rightcontent {
          flex: 1;
          overflow: auto;
          .sino_table {
            width: 100%;
            overflow: hidden;
          }
        }
      }
    }
  }
}
</style>
