<!-- 抄表记录 -->
<template>
  <PageContainer>
    <div slot="content" class="form-content">
      <div class="form-title"><i class="el-icon-arrow-left" @click="$router.go(-1)"></i>抄表记录</div>
      <div class="asset-info">
        <el-row :gutter="24" style="margin: 0">
          <el-col :md="8">
            <p class="info-item">
              <span class="info-label">设备名称：</span>
              <span class="info-value">{{ deviceInfo.deviceName || '-' }}</span>
            </p>
          </el-col>
          <el-col :md="8">
            <p class="info-item">
              <span class="info-label">设备编码：</span>
              <span class="info-value">{{ deviceInfo.deviceCode || '-' }}</span>
            </p>
          </el-col>
          <el-col :md="8">
            <p class="info-item">
              <span class="info-label">设备类型：</span>
              <span class="info-value">{{ deviceInfo.deviceTypeName || '-' }}</span>
            </p>
          </el-col>
        </el-row>
        <el-row :gutter="24" style="margin: 0">
          <el-col :md="8">
            <p class="info-item">
              <span class="info-label">监测系统：</span>
              <span class="info-value">{{ deviceInfo.projectName || '-' }}</span>
            </p>
          </el-col>
          <el-col :md="8">
            <p class="info-item">
              <span class="info-label">安装位置：</span>
              <span class="info-value">{{ deviceInfo.reginonName || '-' }}</span>
            </p>
          </el-col>
          <el-col :md="8">
            <p class="info-item">
              <span class="info-label">服务区域：</span>
              <span class="info-value">{{ deviceInfo.serviceReginonName || '-' }}</span>
            </p>
          </el-col>
        </el-row>
        <el-row :gutter="24" style="margin: 0">
          <el-col :md="8">
            <p class="info-item">
              <span class="info-label">监测科室：</span>
              <span class="info-value">{{ deviceInfo.departmentName || '-' }}</span>
            </p>
          </el-col>
          <el-col :md="8">
            <p class="info-item">
              <span class="info-label">抄表类型：</span>
              <span class="info-value">{{ deviceInfo.recordType == 1 ? '手动' : '自动' || '-' }}</span>
            </p>
          </el-col>
        </el-row>
      </div>
      <div class="content-main">
        <div class="search-from">
          <el-date-picker
            v-model="queryForm.dataRange"
            type="datetimerange"
            unlink-panels
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
          >
          </el-date-picker>
          <div style="display: inline-block">
            <el-button type="primary" plain @click="resetForm">重置</el-button>
            <el-button type="primary" @click="searchForm">查询</el-button>
            <el-button type="primary" :disabled="deviceInfo.recordType == 0" icon="el-icon-plus" @click="handleRoleEvent('add')">录入抄表</el-button>
          </div>
        </div>
        <TablePage
          ref="table"
          v-loading="tableLoading"
          :showPage="true"
          :tableColumn="tableColumn"
          :data="tableData"
          height="calc(100% - 115px)"
          :pageData="pageData"
          @pagination="paginationChange"
        >
        </TablePage>
      </div>
      <recordMeterDialog v-if="isRecordMeterDialog" :visible.sync="isRecordMeterDialog" :deviceData="deviceInfo" :selectItem="activeItem" />
    </div>
  </PageContainer>
</template>
<script lang="jsx">
export default {
  name: 'meterRecord',
  components: {
    recordMeterDialog: () => import('../components/recordMeterDialog.vue')
  },
  data() {
    return {
      isRecordMeterDialog: false,
      queryForm: {
        dataRange: []
      },
      deviceInfo: {},
      tableLoading: false,
      tableData: [],
      tableColumn: [
        {
          prop: 'recordTime',
          label: '抄表时间'
        },
        {
          prop: 'numericalValue',
          label: '表计读数'
        },
        {
          width: 110,
          prop: 'operation',
          label: '操作',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <el-button type="text" disabled={this.deviceInfo.recordType == 0} style="color: #121F3E" onClick={() => this.handleRoleEvent('edit', row.row)}>
                  编辑
                </el-button>
                <el-button type="text" disabled={this.deviceInfo.recordType == 0} style="color: #fa403c" onClick={() => this.handleRoleEvent('del', row.row)}>
                  删除
                </el-button>
              </div>
            )
          }
        }
      ],
      pageData: {
        page: 1,
        pageSize: 15,
        total: 0
      },
      activeItem: {}
    }
  },
  computed: {},
  watch: {
    isRecordMeterDialog(val) {
      if (!val) this.getMeterRecordPage()
    }
  },
  created() {
    this.getMeterDeviceById()
    this.getMeterRecordPage()
  },
  methods: {
    handleRoleEvent(type, row = {}) {
      this.activeItem = row
      if (type == 'add' || type === 'edit') {
        // 新增/修改
        this.isRecordMeterDialog = true
      } else if (type == 'del') {
        // 删除
        this.$confirm('删除后将无法恢复，是否确定删除？', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$api.meterDeleteRecord({ id: row.id }).then((res) => {
            if (res.code == 200) {
              this.$message.success('删除成功')
              this.getMeterRecordPage()
            } else {
              this.$message.error('删除失败')
            }
          })
        })
      }
    },
    getMeterDeviceById() {
      this.$api.GetMeterDeviceById({ id: this.$route.query.id }).then((res) => {
        if (res.code == 200) {
          this.deviceInfo = res.data
        }
      })
    },
    getMeterRecordPage() {
      let params = {
        deviceId: this.$route.query.id,
        startDate: this.queryForm.dataRange[0],
        endDate: this.queryForm.dataRange[1],
        page: this.pageData.page,
        pageSize: this.pageData.pageSize
      }
      this.$api.GetMeterRecordPage(params).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.records
          this.pageData.total = res.data.total
        }
      })
    },
    // 查询
    searchForm() {
      this.pageData.page = 1
      this.getMeterRecordPage()
    },
    // 重置查询
    resetForm() {
      Object.assign(this.$data.searchFrom, this.$options.data().searchFrom)
      this.searchForm()
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getMeterRecordPage()
    }
  }
}
</script>
<style lang="scss" scoped>
.form-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: hidden;
  p {
    margin: 0px;
  }
  .form-title {
    height: 40px;
    line-height: 40px;
    background: #fff;
    border-bottom: 1px solid #dcdfe6;
    font-size: 15px;
    font-family: 'PingFang SC-Medium', 'PingFang SC';
    font-weight: 500;
    color: #121f3e;
    padding-left: 15px;
    i {
      width: 20px;
      cursor: pointer;
    }
  }
  .asset-info {
    padding: 24px 0px 0px;
    background: #fff;
    margin-bottom: 16px;
    border-radius: 0px 0px 4px 4px;
  }
  .content-main {
    flex: 1;
    width: 100%;
    background: #fff;
    padding: 14px 24px 0px;
    ::v-deep .search-from {
      margin-bottom: 24px;
      & > div {
        margin-top: 10px;
        margin-right: 10px;
      }
      .el-date-editor {
        .el-range__icon,
        .el-range__close-icon {
          line-height: 25px !important;
        }
      }
    }
  }
  .info-item {
    margin-bottom: 24px;
    display: flex;
    span {
      display: inline-block;
      font-size: 14px;
      line-height: 14px;
    }
    .info-label {
      text-align: right;
      min-width: 100px;
      display: inline-block;
      font-weight: 400;
      color: #7f848c;
    }
    .info-value {
      display: inline-block;
      font-weight: 500;
      color: #333333;
      flex: 1;
      word-wrap: break-word;
      word-break: break-all;
      img {
        width: 100px;
        height: 100px;
        cursor: pointer;
      }
      span {
        display: block;
        margin-bottom: 16px;
        color: #3562db;
        cursor: pointer;
      }
    }
  }
}
</style>
<style lang="scss">
.operationBtn {
  .is-disabled {
    color: #dcdfe6 !important;
  }
}
</style>
