<script>
import { UsingStatusOptions } from '@/views/operationPort/constant'
import tableListMixin from '@/mixins/tableListMixin'
import { DocumentType } from '@/views/operationPort/projectManage/projectConfig/billConfig/constant'
export default {
  name: 'BillConfig',
  components: {
    BillEdit: () => import('./components/BillEdit.vue')
  },
  filters: {
    statusFilter(status) {
      return UsingStatusOptions.find((it) => it.value === +status)?.label ?? '-'
    }
  },
  mixins: [tableListMixin],
  data: () => ({
    searchForm: {
      name: ''
    },
    tableData: [],
    loadingStatus: false,
    dialog: {
      show: false,
      editData: null,
      readonly: false
    }
  }),
  computed: {
    OperateType() {
      return {
        Edit: 'edit',
        View: 'view',
        Config: 'config',
        Download: 'download',
        Delete: 'delete',
        Create: 'create'
      }
    }
  },
  mounted() {
    this.getDataList()
  },
  methods: {
    getDataList() {
      this.loadingStatus = true
      const params = {
        name: this.searchForm.name,
        pageSize: this.pagination.size,
        pageNo: this.pagination.current
      }
      this.$api.SporadicProject.queryProjectDocumentConfigByPage(params)
        .then((res) => {
          if (res.code === '200') {
            this.tableData = res.data.records
            this.pagination.total = res.data.total
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '查询失败'))
        .finally(() => (this.loadingStatus = false))
    },
    onSearch() {
      this.pagination.current = 1
      this.getDataList()
    },
    onReset() {
      this.$refs.formRef.resetFields()
      this.onSearch()
    },
    onOperate(row, type) {
      switch (type) {
        case this.OperateType.Delete:
          if (+row.state === 1) {
            this.$message.error('启用的单据不允许删除！')
          } else {
            this.$confirm('删除后将无法恢复，是否确定删除？', '信息提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消'
            }).then(() => this.doDelete(row.id))
          }
          break
        case this.OperateType.Download:
          const url = row.viewUrl
          if (!url) {
            this.$message.error('缺少模板文件！')
            return
          }
          // 预览单据模板，当前阶段使用链接的形式打开
          window.open(url, 'template', 'noreferrer')
          break
        default:
          this.dialog.show = true
          this.dialog.editData = row
          this.dialog.readonly = type === this.OperateType.View
          break
      }
    },
    // 删除单据
    doDelete(id) {
      this.loadingStatus = true
      this.$api.SporadicProject.deleteProjectDocumentConfigById({ id })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('删除成功')
            this.getDataList()
          } else {
            throw res.message
          }
        })
        .catch((msg) => msg && this.$message.error(msg))
        .finally(() => (this.loadingStatus = false))
    },
    // 单据类型格式化
    documentTypeFormatter(row) {
      const { documentType } = row
      const item = DocumentType.find(it => it.value === documentType)
      return item?.label ?? '-'
    }
  }
}
</script>
<template>
  <div class="bill-config">
    <div class="bill-config__top">
      <el-form ref="formRef" :model="searchForm" class="bill-config__search" inline @submit.native.prevent="onSearch">
        <el-form-item prop="name">
          <el-input v-model="searchForm.name" clearable filterable placeholder="单据名称"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" plain @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
      <div class="bill-config__actions">
        <el-button type="primary" icon="el-icon-plus" @click="onOperate(null, OperateType.Create)">新增单据
        </el-button>
      </div>
    </div>
    <div class="bill-config__table">
      <el-table v-loading="loadingStatus" height="100%" :data="tableData" border stripe table-layout="auto" class="tableAuto" row-key="id">
        <el-table-column label="序号" type="index" width="55" />
        <el-table-column label="单据名称" prop="name" show-overflow-tooltip></el-table-column>
        <el-table-column label="编码" prop="code" show-overflow-tooltip></el-table-column>
        <el-table-column label="单据类型" prop="documentType" width="120px" :formatter="documentTypeFormatter"></el-table-column>
        <el-table-column label="项目类型" prop="businessFormName" width="150px"></el-table-column>
        <el-table-column label="生成方式" prop="dataType" width="150px" :formatter="row => row.createType === '1'?'手动':'自动'"></el-table-column>
        <el-table-column label="生成节点" prop="workNodeName" width="150px"></el-table-column>
        <el-table-column label="模板" prop="templateFile" width="100px">
          <template #default="{ row }">
            <el-button type="text" @click="onOperate(row, OperateType.Download)">查看</el-button>
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="statu" width="100px">
          <template #default="{ row }">
            <span class="bill-config__tag" :class="`bill-config__tag--${row.state}`">
              {{ row.state | statusFilter }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" show-overflow-tooltip></el-table-column>
        <el-table-column label="操作" width="150px">
          <template #default="{ row }">
            <el-button type="text" @click="onOperate(row, OperateType.View)">查看</el-button>
            <el-button type="text" @click="onOperate(row, OperateType.Edit)">编辑</el-button>
            <el-button type="text" class="text-red" @click="onOperate(row, OperateType.Delete)">删除</el-button>
            <!--            <el-dropdown @command="(command) => onOperate(row, command)">-->
            <!--              <el-button type="text" style="margin-left: 10px">更多</el-button>-->
            <!--              <template #dropdown>-->
            <!--                <el-dropdown-menu>-->
            <!--                  <el-dropdown-item :command="OperateType.Config">模板配置</el-dropdown-item>-->
            <!--                  <el-dropdown-item style="color: #ff1919" :command="OperateType.Delete">删除</el-dropdown-item>-->
            <!--                </el-dropdown-menu>-->
            <!--              </template>-->
            <!--            </el-dropdown>-->
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-pagination
      class="bill-config__pagination"
      :current-page="pagination.page"
      :page-sizes="pagination.pageSizeOptions"
      :page-size="pagination.size"
      :layout="pagination.layoutOptions"
      :total="pagination.total"
      @size-change="paginationSizeChange"
      @current-change="paginationCurrentChange"
    >
    </el-pagination>
    <BillEdit v-bind="dialog" :visible.sync="dialog.show" @success="getDataList" />
  </div>
</template>
<style scoped lang="scss">
.bill-config {
  display: flex;
  flex-flow: column nowrap;
  padding: 16px;
  &__top {
    display: flex;
    justify-content: space-between;
  }
  &__actions {
    line-height: 40px;
  }
  &__pagination {
    margin-top: 10px;
  }
  &__table {
    margin-top: -10px;
    flex: 1;
  }
  &__tag {
    // 停用
    &--0 {
      --color: #f64646;
    }
    // 启用
    &--1 {
      --color: #00b42a;
    }
    &:before {
      content: '';
      display: inline-block;
      vertical-align: middle;
      margin-right: 4px;
      height: 4px;
      width: 4px;
      border-radius: 4px;
      background-color: var(--color);
    }
  }
}
</style>
