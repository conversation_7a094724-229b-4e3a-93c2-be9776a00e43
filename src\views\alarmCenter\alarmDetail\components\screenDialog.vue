<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    title="屏蔽报警"
    width="40%"
    :visible="visible"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <div class="content" style="padding: 10px">
      <el-form ref="ruleForm" :model="form" :rules="rules">
        <el-form-item label="屏蔽时长" prop="shieldTime">
          <el-input v-model.trim="form.shieldTime" maxlength="6" placeholder="请输入" style="width: 200px" onkeyup="value=value.replace(/[^\d]/g,'')">
            <template slot="append">分钟</template>
          </el-input>
        </el-form-item>
        <el-form-item label="屏蔽原因" prop="alarmAffirm">
          <el-radio-group v-model="form.alarmAffirm" size="mini" @input="radioInput">
            <el-radio-button label="2">误报</el-radio-button>
            <el-radio-button label="4">调试</el-radio-button>
            <el-radio-button label="3">其他</el-radio-button>
          </el-radio-group>
          <el-input
            v-model.trim="form.remarks"
            :disabled="form.alarmAffirm !== '3'"
            type="textarea"
            :rows="4"
            placeholder="请输入备注"
            maxlength="100"
            show-word-limit
            style="width: 70%"
            class="ipt"
          >
          </el-input>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="confirm('ruleForm')">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  name: 'screenDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    selectItems: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      form: {
        shieldTime: '15',
        alarmAffirm: '2',
        remarks: '误报'
      },
      rules: {
        shieldTime: [{ required: true, message: '请输入分钟', trigger: 'change' }],
        alarmAffirm: [{ required: true, message: '请选择屏蔽原因', trigger: 'change' }]
      }
    }
  },
  mounted() {},
  methods: {
    // 关闭弹窗
    closeDialog() {
      this.form.shieldTime = '15'
      this.form.alarmAffirm = '2'
      this.form.remarks = ''
      this.$emit('update:visible', !this.visible)
    },
    radioInput() {
      if (this.form.remarks.length > 100) {
        return
      }
      if (this.form.alarmAffirm == '2') {
        this.form.remarks = '误报'
      } else if (this.form.alarmAffirm == '4') {
        this.form.remarks = '调试'
      } else {
        this.form.remarks = ''
      }
    },
    confirm(formName) {
      let alarmObjectId = []
      let alarmType = []
      let alarmId = []
      let params = {
        shield: true,
        alarmAffirm: this.form.alarmAffirm,
        shieldTime: this.form.shieldTime,
        remarks: this.form.remarks,
        operationSource: 0
      }
      this.selectItems.forEach((item) => {
        alarmObjectId.push(item.alarmObjectId)
        alarmType.push(item.alarmFleldsConfigId)
        alarmId.push(item.alarmId)
      })
      params.alarmObjectId = alarmObjectId.toString()
      params.alarmType = alarmType.toString()
      params.alarmId = alarmId.toString()
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$api.shield(params).then((res) => {
            if (res.code == 200) {
              this.$message({
                message: '屏蔽成功',
                type: 'success'
              })
              this.form.remarks = ''
              this.$emit('update')
            } else {
              this.$message.error(res.msg)
            }
          })
        } else {
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  padding: 20px !important;
  background-color: #fff !important;
}
.model-dialog {
  padding: 0 !important;
}
.ipt {
  margin-top: 15px;
  margin-left: 80px;
}
::v-deep .el-form-item__error {
  height: 20px;
  width: 300px;
  color: #f56c6c;
  font-size: 12px;
  line-height: 20px;
  padding-top: 4px;
  position: absolute;
  left: 78px;
}
::v-deep .el-radio-button__orig-radio:checked + .el-radio-button__inner {
  background-color: #3562db !important;
}
::v-deep .el-dialog__body {
  padding: 0 !important;
}
::v-deep .el-textarea {
  .el-textarea__inner {
    padding-bottom: 20px;
  }
  .el-input__count {
    line-height: 16px;
  }
}
</style>
