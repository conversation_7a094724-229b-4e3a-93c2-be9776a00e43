<template>
  <PageContainer>
    <div slot="content" class="role-content">
      <div class="role-content-left">
        <div class="toptip">
          <span class="green_line"></span>
          模板管理
        </div>
        <div v-loading class="left_content">
          <!-- <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="模板分类" name="moban" />
            <el-tab-pane label="设备类别" name="shebei" />
          </el-tabs> -->
          <div class="tree">
            <el-tree
              ref="tree"
              v-loading="treeLoading"
              style="margin-top: 10px"
              :check-strictly="true"
              :data="treeData"
              :props="defaultProps"
              node-key="id"
              :highlight-current="true"
              :default-expanded-keys="expanded"
              @node-click="handleNodeClick"
            >
              <span slot-scope="{ node }" class="custom-tree-node" style="width: 100%">
                <el-tooltip v-if="node.label.length > 17" class="item" effect="dark" :content="node.label" placement="top-start">
                  <span class="treeLabel">{{ node.label }}</span>
                </el-tooltip>
                <span v-else class="treeLabel">{{ node.label }}</span>
              </span>
            </el-tree>
          </div>
        </div>
      </div>
      <div class="role-content-right">
        <div style="height: 100%">
          <div class="search-from">
            <el-input
              v-model.trim="taskBookName"
              placeholder="模板名称"
              style="width: 200px"
              clearable
              maxlength="25"
              onkeyup="if(value.length>25)value=value.slice(0,25)"
            ></el-input>
            <el-button type="primary" plain @click="resetForm">重置</el-button>
            <el-button type="primary" @click="searchForm">查询</el-button>
            <el-button type="primary" icon="el-icon-plus" @click="onOpen('add')">新增</el-button>
          </div>
          <div class="contentTable">
            <div class="contentTable-main table-content">
              <el-table v-loading="tableLoading" title="双击查看详情" border style="width: 100%" :data="tableData" height="100%" stripe @row-dblclick="dblclick">
                <el-table-column type="index" label="序号" width="80">
                  <template slot-scope="scope">
                    <span>{{ (pagination.current - 1) * pagination.size + scope.$index + 1 }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="projectName" label="模板名称" show-overflow-tooltip></el-table-column>
                <el-table-column prop="equipmentTypeName" label="模板分类" show-overflow-tooltip></el-table-column>
                <el-table-column prop="projectExplain" label="模板说明" show-overflow-tooltip></el-table-column>
                <el-table-column prop="createPersonName" show-overflow-tooltip label="添加人"></el-table-column>
                <el-table-column prop="updateTime" show-overflow-tooltip label="更新时间"></el-table-column>
                <el-table-column show-overflow-tooltip width="250" label="操作">
                  <template slot-scope="scope">
                    <el-button type="text" class="copy" @click="onOpen('copy', scope.row)">复制</el-button>
                    <el-button type="text" @click="onOpen('edit', scope.row)">编辑</el-button>
                    <el-button type="text" class="delet" @click="deleteFn(scope.row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="contentTable-footer">
              <el-pagination
                :current-page="pagination.current"
                :page-sizes="pagination.pageSizeOptions"
                :page-size="pagination.size"
                :layout="pagination.layoutOptions"
                :total="pagination.total"
                @size-change="paginationSizeChange"
                @current-change="paginationCurrentChange"
              >
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin.js'
export default {
  name: 'videoPatrolTemplateManagement',
  components: {},
  mixins: [tableListMixin],
  beforeRouteEnter(to, from, next) {
    let names = []
    to.matched.map((v, i) => {
      if (i > 0) {
        v.components.default.name && names.push(v.components.default.name)
      }
    })
    next((vm) => {
      if (from.query.activeName) {
        vm.activeName = from.query.activeName
      }
      vm.$store.commit('keepAlive/add', names)
    })
  },
  async beforeRouteLeave(to, from, next) {
    // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
    if (
      ![
        'maintenanceAddTemplate',
        'maintenanceTemplateDetail',
        'addTemplate',
        'templateDetail',
        'comInsAddTemplate',
        'comInsTemplateDetail',
        'hsc_comInsTemplateDetail',
        'hsc_comInsAddTemplate',
        'AddVpTemplate',
        'vp_templateDetail'
      ].includes(to.name)
    ) {
      // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      treeLoading: false,
      systemType: this.$route.meta.type, // 系统标识
      activeName: 'moban',
      taskBookName: '', // 模板名称
      treeData: [],
      checkedData: [], // 选中tree数据
      defaultProps: {
        label(data, node) {
          return (data.dictName || data.baseName) + `  (${data.dataSum})`
        },
        children: 'children' || ''
      },
      expanded: [],
      filters: {
        dictName: ''
      },
      tableLoading: false,
      tableData: [],
      dictData: [],
      allTreeList: []
    }
  },
  watch: {
    activeName(e, l) {
      this.treeData = []
      this.tableData = []
      this.getTemTree()
    }
  },
  activated() {
    this.initData()
  },
  created() {
    this.initData()
  },
  methods: {
    initData() {
      this.getTemTree()
    },
    // 获取模板树形结构
    getTemTree() {
      this.treeLoading = true
      // moduleDifferentiation 1.设施设备巡检 2.设施保养 3.综合巡检 4.危化品安全巡查 5:视频巡逻
      this.$api.getTemTree({ moduleDifferentiation: this.systemType }).then((res) => {
        if (res.code == 200) {
          this.treeData = res.data
          if (this.$route.query.typeId) {
            this.checkedData.id = this.$route.query.typeId
          } else {
            this.checkedData = this.treeData[0]
          }
          this.getDataList()
          this.$nextTick(() => {
            // id：绑定的 node-key
            this.$refs.tree.setCurrentKey(this.checkedData.id)
          })
        }
        this.treeLoading = false
      })
    },
    handleClick(tab) {
      this.$router.push({ query: { activeName: this.activeName } })
    },
    getDataList() {
      let userInfo = this.$store.state.user.userInfo.user
      let data = {
        systemIdentificationClassification: this.systemType,
        pageNo: this.pagination.current,
        pageSize: this.pagination.size,
        projectName: this.taskBookName,
        dictTypeId: this.checkedData ? this.checkedData.id : '',
        staffId: userInfo.staffId,
        userName: userInfo.staffName
      }
      this.tableLoading = true
      this.$api.getTemList(data).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.list
          this.pagination.total = res.data.count
        }
        this.tableLoading = false
      })
    },
    // 树状图点击
    handleNodeClick(data) {
      this.pagination.current = 1
      this.checkedData = data
      this.tableData = []
      this.$refs.tree.setCurrentKey(this.checkedData.id)
      this.getDataList()
      this.$router.push({
        query: {
          activeName: this.activeName,
          typeId: this.checkedData.id
        }
      })
    },
    // 重置
    resetForm() {
      this.taskBookName = ''
      this.pagination.size = 15
      this.pagination.current = 1
      this.getDataList()
    },
    // 查询
    searchForm() {
      this.pagination.current = 1
      this.getDataList()
    },
    // 双击查看详情
    dblclick(val) {
      let pathName = ''
      if (this.systemType == '6') pathName = 'vp_templateDetail'
      this.$router.push({
        name: pathName,
        query: {
          rowId: val.id,
          activeName: this.activeName
        }
      })
    },
    deleteFn(row) {
      this.$confirm('此操作将永久删除该模板, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let userInfo = this.$store.state.user.userInfo.user
        let data = {
          staffId: userInfo.staffId,
          userName: userInfo.staffName,
          ids: row.id
        }
        this.$api.icisDeleteTaskBookList(data, { 'operation-type': 3, 'operation-name': row.projectName, 'operation-id': row.id }).then((res) => {
          const { code, data, message } = res
          if (code == 200) {
            this.$message.success(message)
            this.getTemTree()
            this.searchForm()
          } else {
            this.$message.error(message)
          }
        })
      })
    },
    onOpen(type, val) {
      let pathName = ''
      if (this.systemType == '6') pathName = 'AddVpTemplate'
      if (type == 'add') {
        this.$router.push({
          name: pathName,
          query: {
            type,
            checkedData: this.checkedData,
            activeName: this.activeName
          }
        })
      } else {
        this.$router.push({
          name: pathName,
          query: {
            id: val.id,
            type,
            checkedData: this.checkedData,
            activeName: this.activeName
          }
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
// el-tree-node is-expanded is-focusable
::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: linear-gradient(to right, #d9e1f8, #fff);
}
::v-deep .el-tree-node__content {
  height: 32px;
}
.role-content {
  height: 100%;
  display: flex;
  .role-content-left {
    width: 246px;
    height: 100%;
    padding: 10px;
    background: #fff;
    border-radius: 4px;
    margin-right: 12px;
    .left_content {
      height: calc(100% - 60px);
      // overflow: auto;
      .tree {
        height: calc(100% - 40px);
        overflow: auto;
        ::v-deep .el-tree-node__content {
          width: 100%;
          text-align: left;
          .custom-tree-node {
            width: calc(100% - 24px) !important;
            .treeLabel {
              display: inline-block;
              width: 100%;
              text-overflow: ellipsis;
              white-space: nowrap;
              overflow: hidden;
            }
          }
        }
      }
      ul {
        padding: 0;
        li {
          height: 38px;
          width: 100%;
          font-size: 14px;
          font-family: 'PingFang SC-Regular', 'PingFang SC';
          font-weight: 400;
          color: #606266;
          line-height: 38px;
          list-style-type: none;
          box-sizing: border-box;
          cursor: pointer;
          padding-left: 27px;
          &:hover {
            background: rgb(53 98 219 / 20%);
            border-radius: 4px 0 0 4px;
          }
        }
      }
    }
  }
  .role-content-right {
    flex: 1;
    height: 100%;
    min-width: 0;
    padding: 10px;
    background: #fff;
    border-radius: 4px;
    .search-from {
      padding-bottom: 12px;
      & > div {
        margin-right: 10px;
      }
      & > button {
        margin-top: 12px;
      }
    }
    .contentTable {
      height: calc(100% - 80px);
      display: flex;
      flex-direction: column;
      .contentTable-main {
        flex: 1;
        overflow: auto;
      }
      .contentTable-footer {
        padding: 10px 0 0;
      }
    }
  }
  .content {
    width: 100%;
    max-height: 500px !important;
    overflow: auto;
    background-color: #fff !important;
  }
}
.delet {
  color: red !important;
}
.copy {
  color: #606266 !important;
}
::v-deep .el-tabs__nav-scroll {
  width: 80%;
  margin: 0 auto;
}
</style>
