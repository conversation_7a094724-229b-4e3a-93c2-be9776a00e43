import CryptoJS from 'crypto-js'
export default {
  /**
   * @description: 加密
   * @param {*} word
   * @param {*} keyStr
   */
  set(word, keyStr) {
    keyStr = keyStr || 'A615324747421311' // 判断是否存在ksy，不存在就用定义好的key
    var key = CryptoJS.enc.Utf8.parse(keyStr)
    var srcs = CryptoJS.enc.Utf8.parse(word)
    var iv = CryptoJS.enc.Utf8.parse('A615324747421311')
    var encrypted = CryptoJS.AES.encrypt(srcs, key, {
      iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    })
    return encrypted.toString()
  }
}
