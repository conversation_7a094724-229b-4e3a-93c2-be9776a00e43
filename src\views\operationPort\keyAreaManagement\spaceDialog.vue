<template>
  <el-dialog v-if="visible" v-dialogDrag :modal="false" :close-on-click-modal="false" :title="title" width="40%"
    :visible.sync="visible" custom-class="model-dialog" :before-close="closeDialog">
    <div class="dialog-content">
      <div class="content-left">
        <el-input v-model="treeFilter" placeholder="请输入关键字" clearable></el-input>
        <div v-loading="treeLoading" class="left-tree">
          <el-tree ref="tree" :data="spaceTreeData" node-key="id" size="small" class="wrap-tree"
            :default-expanded-keys="spaceTreeData.map((v) => v.id)" :highlight-current="true" show-checkbox
            :props="treeProps" :filter-node-method="filterNode" @check="checkData" @check-change="handleCheckChange">
          </el-tree>
        </div>
      </div>
      <div class="content-right">
        <div class="right-title">
          <p class="title">
            已选：
            <span>{{ selectSpace.length }}</span>
            个空间
          </p>
          <p class="clear" @click="clear">清空</p>
        </div>
        <div class="right-list">
          <div v-for="(item, index) in selectSpace" :key="item.id" class="list-item">
            <span>{{ item.allSpaceName }}</span>
            <i class="el-icon-close" @click="remove(item, index)"></i>
          </div>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="confirm('ruleForm')">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { transData } from '@/util'
import * as forEach from 'lodash/forEach'
export default {
  name: 'spaceDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '所属科室'
    },
    id: {
      type: String,
      default: ''
    },
    isRadio: {
      type: Boolean,
      default: false
    },
    isNotmultiSector: {
      type: Boolean,
      default: false
    },
    spaceList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      treeFilter: '',
      treeLoading: false,
      spaceTreeData: [],
      treeProps: {
        // checkStrictly: true,
        // emitPath: false,
        // value: 'id',
        label: 'ssmName',
        children: 'list'
      },
      selectSpace: [],
      lastTreeParentId: '',
      disabledNode: []
    }
  },
  watch: {
    treeFilter(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    this.getDisabledTreeNode()
    this.getTreelist()
  },
  methods: {
    // 移除
    remove(node, index) {
      this.$refs.tree.setChecked(node.id, false)
      this.selectSpace.splice(index, 1)
      if (!this.selectSpace.length) {
        this.clear()
      }
    },
    // 清空
    clear() {
      this.selectSpace = []
      this.$nextTick(() => {
        this.$refs.tree.setCheckedKeys([])
      })
    },
    filterNode(value, data) {
      if (!value) return true
      return data.ssmName.indexOf(value) !== -1 || data.allSpaceName.indexOf(value) !== -1
    },
    checkData(data, checked, indeterminate) {
      if (this.isRadio) {
        this.selectSpace = [data]
        this.$refs.tree.setCheckedKeys([data.id])
      } else {
        const checkedArr = this.$refs.tree.getCheckedNodes()
        // this.selectSpace = checkedArr.filter((i) => i.ssmType == 5)
        this.selectSpace = checkedArr
      }
      if (!this.selectSpace.length) {
        this.$message.warning('当前空间下暂无房间')
        this.$refs.tree.getCheckedNodes().forEach((item) => {
          // if (item.ssmType != 5) {
          this.$refs.tree.setChecked(item.id, false)
          // }
        })
      }
    },
    // 获取单位列表
    getTreelist() {
      this.treeLoading = true
      this.$api.getAllSpaceTree().then((res) => {
        if (res.code === 200) {
          res.data = res.data.filter(item => item.ssmName !== '' && item.ssmName !== null)
          this.spaceList = res.data
          let list = res.data
          for (let i = 0; i < list.length; i++) {
            if (this.disabledNode.includes(list[i].id)) {
              list[i].disabled = true
            } else {
              list[i].disabled = false
            }
          }
          this.spaceTreeData = this.$tools.transData(res.data, 'id', 'pid', 'list')
        } else {
          this.$message.error(res.message)
        }
        this.treeLoading = false
      })
    },
    getDisabledTreeNode() {
      let params = {}
      if (this.id) {
        params.notId = this.id
      }
      this.$api.findAllSpaceIdList(params).then((res) => {
        this.disabledNode = res.data
      })
    },
    confirm() {
      if (!this.selectSpace.length) {
        this.$message({ message: '请选择空间', type: 'error' })
        return
      }
      this.$emit('selectSpace', this.selectSpace)
      this.closeDialog()
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', !this.visible)
    },
    handleCheckChange(node, check, childCheck) {
      if (this.isNotmultiSector) {
        let checkedArr = this.$refs.tree.getCheckedNodes()
        if (check) {
          if (checkedArr.length > 1) {
            for (let i = 0; i < checkedArr.length; i++) {
              /*
            在已选节点中(包含最后次勾选不在原先同层节点的数据)判断当前勾选的节点是否跟原先的节点同层
            */
              if (node.id != checkedArr[i].id) {
                this.lastTreeParentId = checkedArr[i].umId
              }
            }
            if (node.umId != this.lastTreeParentId) {
              this.$message.warning('选择的节点不在同一层级请重新选择')
              // 移除已选中的并且不在同一层的节点
              this.$refs.tree.setChecked(node.id, false, false)
              return
            }
          }
        } else {
          if (checkedArr.length == 0) {
            this.lastTreeParentId = null
          }
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .model-dialog {
  // min-height: 60vh;
}

.dialog-content {
  width: 100%;
  background: #fff;
  padding: 24px 0px;
  display: flex;

  p {
    margin: 0;
  }

  .content-left {
    height: 480px;
    flex: 1;
    padding: 0px 24px;
    border-right: 1px solid #e4e7ed;
    display: flex;
    flex-direction: column;

    .left-tree {
      flex: 1;
      margin-top: 16px;
      overflow: auto;
    }

    // ::v-deep .el-tree-node {
    //   .is-leaf+.el-checkbox .el-checkbox__inner {
    //     display: inline-block;
    //   }

    //   .el-checkbox .el-checkbox__inner {
    //     // display: none;
    //   }
    // }
  }

  .content-right {
    height: 480px;
    flex: 1;
    padding: 0px 24px;
    display: flex;
    flex-direction: column;

    .right-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 10px;

      .title {
        font-size: 14px;
        color: #7f848c;
        line-height: 22px;

        span {
          color: #333333;
          margin: 0 8px;
        }
      }

      .clear {
        cursor: pointer;
        font-size: 12px;
        color: #3562db;
      }
    }

    .right-list {
      flex: 1;
      overflow: auto;

      .list-item {
        transition: all 0.3s;
        padding: 8px 12px;
        border-radius: 4px;
        color: #333333;
        margin-top: 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        i {
          cursor: pointer;
          color: #666666;
          font-size: 16px;
        }
      }

      .list-item:hover {
        background: #e6effc;
      }
    }
  }
}
</style>
