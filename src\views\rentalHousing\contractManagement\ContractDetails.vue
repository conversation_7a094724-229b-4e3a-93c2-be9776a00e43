<template>
  <PageContainer :footer="true">
    <div slot="content" class="role-content">
      <div class="container-details">
        <div class="container-header">
          <!-- 合同基本信息 -->
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>合同详情</span>
            </div>
            <div class="contract-header">
              <div class="left-header">
                <img src="@/assets/images/contract_icon.png" alt="" />
              </div>
              <div class="right-header">
                <div class="top-section">
                  <p>合同编号：{{ contractInfo.contractNum }}</p>
                </div>
                <div class="bottom-section">
                  <el-col :span="6">
                    <p>承租人：{{ contractInfo.secondPartyName || '' }}</p>
                    <p>租赁房间：{{ houseInfo.houseName }}</p>
                  </el-col>
                  <el-col :span="8">
                    <p>状态：{{ contractInfo.statusName }}</p>
                    <p>空间位置：{{ contractInfo.spaceName }}</p>
                  </el-col>
                </div>
              </div>
            </div>
          </el-card>
        </div>
        <div class="container-info-box">
          <!-- Tab 切换面板 -->
          <el-card class="box-card">
            <el-tabs v-model="activeTab" @tab-click="handleClick">
              <el-tab-pane label="合同信息" name="contractInfo">
                <div class="tab-pane-box">
                  <div class="baseInfo" style="margin-top: 20px">
                    <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
                      <span class="green_line"></span>
                      基本信息
                    </div>
                  </div>
                  <!-- 合同基本信息 -->
                  <div class="contract-info">
                    <el-row>
                      <el-col :span="8" class="column-spacing">
                        <p>合同编号：{{ contractInfo.contractNum }}</p>
                        <p>空间位置：{{ contractInfo.spaceName }}</p>
                        <p>租期开始时间：{{ contractInfo.rentingStartTime }}</p>
                        <p>剩余租期：{{ contractInfo.residueRentingDay ? `${contractInfo.residueRentingDay} 天` : '' }}</p>
                      </el-col>
                      <el-col :span="8" class="column-spacing">
                        <p>承租人：{{ contractInfo.secondPartyName }}</p>
                        <p>租赁单价(元/㎡)：{{ contractInfo.rentingUnitPrice }}</p>
                        <p>租期结束日期：{{ contractInfo.rentingEndTime }}</p>
                        <p class="contractFile">
                          <span>合同附件：</span>
                          <span v-if="contractInfo.attachmentUrl" class="span">
                            <div v-for="(item, index) in JSON.parse(contractInfo.attachmentUrl)" :key="index">
                              <span @click="downloadContractFile(item.url)">{{ item.url | urlFilters }}</span>
                            </div>
                          </span>
                          <span v-else></span>
                        </p>
                      </el-col>
                      <el-col :span="8" class="column-spacing">
                        <p>房间名称：{{ houseInfo.houseName }}</p>
                        <p>出租租金(元)：{{ contractInfo.rentingMoney }}</p>
                        <p>实际结束日期：{{ contractInfo.actualEndTime }}</p>
                      </el-col>
                    </el-row>
                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane label="账单信息" name="billingInfo">
                <!-- 账单信息 -->
                <div class="bill_content">
                  <div class="top_content">
                    <el-input
                      v-model="filterData.billName"
                      style="width: 180px; margin-right: 8px"
                      placeholder="账单名称"
                      maxlength="60"
                      onkeyup="if(value.length>25)value=value.slice(0,60)"
                      @keyup.enter="searchByCondition"
                    ></el-input>
                    <el-date-picker
                      v-model="filterData.date"
                      type="datetimerange"
                      start-placeholder="开始日期"
                      range-separator="至"
                      end-placeholder="结束日期"
                      :default-time="['00:00:00', '23:59:59']"
                    >
                    </el-date-picker>
                    <div class="button-group">
                      <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db; margin-left: 20px" @click="resetCondition">重置</el-button>
                      <el-button type="primary" style="font-size: 14px" @click="getBillList(contractId)">查询</el-button>
                    </div>
                  </div>
                  <div class="table_list" style="text-align: right; height: 350px">
                    <el-table
                      ref="materialTable"
                      v-loading="tableLoading"
                      :data="tableData"
                      height="100%"
                      border
                      :header-cell-style="{ background: '#F6F5FA' }"
                      style="width: 100%"
                      :cell-style="{ padding: '8px 0 8px 0' }"
                      stripe
                      highlight-current-row
                      :empty-text="emptyText"
                      row-key="id"
                    >
                      <el-table-column label="序号" type="index" width="70">
                        <template slot-scope="scope">
                          <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column show-overflow-tooltip prop="billNum" label="账单编号"></el-table-column>
                      <el-table-column show-overflow-tooltip prop="billName" label="账单名称"></el-table-column>
                      <el-table-column prop="billCycle" show-overflow-tooltip label="账单周期"></el-table-column>
                      <el-table-column prop="billMoney" show-overflow-tooltip label="账单金额(元)"></el-table-column>
                    </el-table>
                  </div>
                  <div class="" style="padding-top: 10px; text-align: right">
                    <el-pagination
                      layout="total, sizes, prev, pager, next, jumper"
                      :current-page="paginationData.currentPage"
                      :page-sizes="[15, 30, 50, 100]"
                      :page-size="paginationData.pageSize"
                      :total="paginationData.total"
                      @size-change="handleSizeChange"
                      @current-change="handleCurrentChange"
                    ></el-pagination>
                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane label="房间信息" name="roomInfo">
                <div class="room-info-box">
                  <HousingForm v-if="activeTab == 'roomInfo'" :id="houseInfo.id" ref="contentRef" :readonly="true" large></HousingForm>
                </div>
              </el-tab-pane>
            </el-tabs>
          </el-card>
        </div>
      </div>
    </div>
    <div slot="footer">
      <el-button type="primary" @click="close">关闭</el-button>
    </div>
  </PageContainer>
</template>
<script>
import moment from 'moment'
import HousingForm from '../housingResource/components/HousingForm'
export default {
  components: {
    HousingForm
  },
  filters: {
    urlFilters(val) {
      if (!val) return ''
      let url = val.split(',')
      let urlStr = ''
      url.forEach((element, idx) => {
        let index = element.lastIndexOf('/')
        if (index != -1) {
          urlStr += idx == url.length - 1 ? element.substring(index + 1, element.length) : `${element.substring(index + 1, element.length)},`
        }
      })
      return urlStr
    }
  },
  data() {
    return {
      dialogVisible: false,
      showImage: false, // 控制图片显示的布尔值
      currentImageUrl: '', // 当前查看的大图链接
      housePictureUrls: [], // 直接存储图片地址数组
      moment,
      form: {
        taskName: 'name',
        cycleType: ''
      },
      contractId: '',
      activeTab: 'contractInfo',
      contractInfo: {
        userName: '',
        userId: '',
        houseName: '',
        status: '',
        spaceName: '',
        contractNum: '',
        rentingStartTime: '',
        rentingEndTime: '',
        rentingUnitPrice: '',
        attachmentUrl: '',
        rentingMoney: ''
      },
      houseInfo: {
        houseName: '', // 房间名称
        spaceNames: '', // 空间位置
        floorArea: '', // 建筑面积
        housePictureUrl: '', // 房屋图片
        houseTypeName: '', // 房型
        addressName: '', // 地址信息
        ancillaryFacilityName: '', // 配套设施
        remark: '', // 备注
        houseNatureName: '', // 类型
        addressDetails: '', // 详细地址
        houseCertificateUrl: '', // 房产证
        costRent: '', // 租金成本
        hirePrice: '', // 单价出租
        hireRent: '' // 出租租金
      },
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      tableData: [],
      // 列表过滤条件
      filterData: {
        billName: '',
        billStartCycle: '',
        billEndCycle: '',
        date: []
      },
      emptyText: '暂无数据',
      tableLoading: false,
      pageSize: '',
      currentPage: ''
    }
  },
  mounted() {
    this.contractId = this.$route.query.id
    this.fetchContractDetails(this.$route.query.id) // 获取 id 并调用详情查询接口
    this.getBillList(this.$route.query.id) // 查询账单列表
  },
  methods: {
    /** 合同附件下载 */
    async downloadContractFile(val) {
      const fileUrl = this.$tools.imgUrlTranslation(val)
      try {
        const response = await fetch(fileUrl)
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
        const blob = await response.blob() // 将响应体转换为 Blob 对象
        // 创建一个 URL 对象
        const url = window.URL.createObjectURL(blob)
        // 创建一个隐藏的 <a> 元素
        const a = document.createElement('a')
        a.style.display = 'none'
        a.href = url
        // 设置下载的文件名
        const fileName = fileUrl.substring(fileUrl.lastIndexOf('/') + 1)
        a.download = fileName
        // 触发点击事件
        document.body.appendChild(a)
        a.click()
        // 释放 URL 对象
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      } catch (error) {
        console.error('下载文件时出错:', error)
      }
    },
    fetchContractDetails(id) {
      if (!id) return // 如果没有 id，则不调用接口
      this.$api.houseContractDetails({ id: id }).then((res) => {
        if (res.code == '200') {
          this.contractInfo = res.data.houseContract
          let statusMap = {
            0: '未开始',
            1: '履约中',
            2: '已结束',
            3: '即将过期',
            4: '已过期'
          }
          this.contractInfo.statusName = statusMap[this.contractInfo.status] || '未知状态'
          this.houseInfo = res.data.houseInfo
        }
      })
    },
    // 查询账单列表
    getBillList(id) {
      if (!id) return // 如果没有 id，则不调用接口
      const params = {
        pageNum: this.paginationData.currentPage, // 页码
        pageSize: this.paginationData.pageSize, //	页大小
        contractId: id,
        billName: this.filterData.billName,
        billStartCycle: this.filterData.date && this.filterData.date.length > 0 ? moment(this.filterData.date[0]).format('YYYY-MM-DD HH:mm:ss') : '',
        billEndCycle: this.filterData.date && this.filterData.date.length > 0 ? moment(this.filterData.date[1]).format('YYYY-MM-DD HH:mm:ss') : ''
      }
      this.tableLoading = true
      this.$api.billListByContractId(params).then((res) => {
        if (res.code == '200') {
          this.tableData = res.data.records
          this.paginationData.total = res.data.total
        }
        this.tableLoading = false
      })
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.getBillList(this.contractId)
    },
    // 每页的条数
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.paginationData.currentPage = 1
      this.getBillList(this.contractId)
    },
    // 切换tabs
    handleClick(tab, event) {
      if (tab.name == 'billingInfo') {
        this.getBillList(this.contractId)
      }
      if (tab.name == 'roomInfo') {
        this.$nextTick(() => {
          this.$refs.contentRef?.revertData(this.houseInfo)
        })
      }
    },
    // 重置查询条件
    resetCondition() {
      this.paginationData = {
        currentPage: 1,
        pageSize: 15,
        total: 0
      }
      this.filterData = {
        billName: '',
        billStartCycle: '',
        billEndCycle: '',
        date: []
      }
      this.getBillList(this.contractId)
    },
    /** 返回上一页 */
    close() {
      this.$router.go(-1)
    }
  }
}
</script>
<style lang="scss" scoped>
.role-content {
  height: 100%;
  box-sizing: border-box;
}
.container-details {
  background-color: #f9f9f9;
  display: flex;
  flex-direction: column;
  height: 100%; /* 父容器高度占满 */
}
.container-header {
  width: 100%;
  margin-bottom: 10px;
}
.contract-header {
  width: 100%;
  display: flex;
  .left-header {
    flex-shrink: 0;
    width: 120px;
    height: 130px;
    margin-right: 10px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .right-header {
    width: calc(100% - 140px);
    .top-section {
      font-size: 20px;
      font-weight: bolder;
    }
  }
}
.container-info-box {
  height: calc(100% - 240px);
  .el-card {
    height: 100%;
  }
  .bill_content {
    height: 475px;
    padding-top: 15px;
    box-sizing: border-box;
    overflow-y: auto;
    .top_content {
      display: flex;
      margin: 0px 0px 15px;
    }
  }
  .tab-pane-box {
    .column-spacing p {
      margin-bottom: 45px;
    }
  }
  .room-info-box {
    height: 475px;
    padding-bottom: 15px;
    box-sizing: border-box;
    overflow-y: auto;
    .column-spacing p {
      margin-bottom: 25px;
    }
    .image-gallery {
      white-space: pre-wrap;
    }
  }
}
.contractFile {
  display: flex;
  .span {
    flex-shrink: 0;
    flex-wrap: wrap;
    color: #3562db;
    cursor: pointer;
  }
}
</style>
