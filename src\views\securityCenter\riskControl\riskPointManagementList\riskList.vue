<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="content_box">
        <el-table v-loading="tableLoading" :data="tableData" height="cacl(100% - 30px)" border stripe @row-dblclick="dblclick">
          <!-- <el-table-column type="selection" width="55" fixed></el-table-column> -->
          <el-table-column type="index" width="80" label="序号" align="center">
            <template slot-scope="scope">
              <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="riskName" label="风险点名称" min-width="100" show-overflow-tooltip></el-table-column>
          <el-table-column prop="riskPlace" label="风险点位点(区域)或系统" show-overflow-tooltip width="180"></el-table-column>
          <el-table-column prop="riskLevel" label="风险等级" min-width="80" show-overflow-tooltip width="120">
            <template slot-scope="scope">
              <div v-if="scope.row.riskLevel == 1" class="table-block-text" style="background: rgb(255 0 0)">重大风险</div>
              <div v-if="scope.row.riskLevel == 2" class="table-block-text" style="background: rgb(255 97 0)">较大风险</div>
              <div v-if="scope.row.riskLevel == 3" style="background: rgb(255 255 0); color: #606266" class="table-block-text">一般风险</div>
              <div v-if="scope.row.riskLevel == 4" style="background: rgb(0 0 255)" class="table-block-text">低风险</div>
              <div v-if="scope.row.riskLevel == 5" style="color: #606266" class="table-block-text">未研判</div>
            </template>
          </el-table-column>
          <el-table-column prop="taskTeamName" show-overflow-tooltip label="责任部门" width="100"></el-table-column>
          <el-table-column prop="responsiblePersonName" show-overflow-tooltip label="责任人"></el-table-column>
          <el-table-column prop="urgentContactPhone" show-overflow-tooltip label="应急联系电话" min-width="130"></el-table-column>
          <el-table-column prop="attachmentUrl" show-overflow-tooltip label="图片" min-width="100">
            <template slot-scope="scope">
              <span style="color: #5188fc; cursor: pointer" @click="showPicture(scope.row, 'attachmentUrl')">查看图片</span>
            </template>
          </el-table-column>
          <el-table-column prop="processUrl" show-overflow-tooltip label="预案流程图" min-width="100">
            <template slot-scope="scope">
              <span style="color: #5188fc; cursor: pointer" @click="showPicture(scope.row, 'processUrl')">查看图片</span>
            </template>
          </el-table-column>
          <el-table-column prop="registerPerson" show-overflow-tooltip label="登记人"></el-table-column>
          <el-table-column prop="registerTime" label="登记时间" width="180"></el-table-column>
        </el-table>
        <div class="contentTable-footer">
          <el-pagination
            :current-page="paginationData.currentPage"
            :page-sizes="[15, 30, 50, 100]"
            :page-size="paginationData.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="paginationData.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
      <imgCarousel :dialogVisibleImg="dialogVisibleImg" :imgArr="imgArr" @closeDialog="closeDialogImg"></imgCarousel>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">关闭</el-button>
    </div>
  </PageContainer>
</template>

<script>
import imgCarousel from '@/components/imgCarousel/imgCarousel'
export default {
  name: 'riskList',
  components: {
    imgCarousel
  },
  data() {
    return {
      tableLoading: false,
      tableData: [],
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      riskLevel: '', // 风险等级
      riskType: '', // 风险类型
      gridId: '', // 网格id
      deptCode: '', // 部门id
      dialogVisibleImg: false,
      imgArr: []
    }
  },
  created() {
    this.riskLevel = this.$route.query.riskLevel || ''
    this.riskType = this.$route.query.riskType || ''
    this.gridId = this.$route.query.gridId || ''
    this.deptCode = this.$route.query.deptCode || ''
    this.getTableData()
  },
  methods: {
    getTableData() {
      let data = {
        pageSize: this.paginationData.pageSize,
        pageNo: this.paginationData.currentPage,
        riskLevel: this.riskLevel,
        riskType: this.riskType,
        gridId: this.gridId,
        deptCode: this.deptCode
      }
      this.$api.ipsmGetRiskList(data).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.list
          this.paginationData.total = parseInt(res.data.total)
        }
      })
    },
    dblclick(val) {
      this.$router.push({
        name: 'ipsmAddRisk1',
        query: { type: 'check', id: val.id }
      })
    },
    showPicture(row, type) {
      if (type == 'attachmentUrl' && row.attachmentUrl && row.attachmentUrl.length > 0) {
        this.$api
          .ipsmGetPictureUrls({
            repairAttachmentUrl: row.attachmentUrl
          })
          .then((res) => {
            res.data.repairAttachmentUrl.forEach((item) => {
              this.imgArr.push(this.$tools.imgUrlTranslation(item))
            })
          })
        this.dialogVisibleImg = true
      } else if (type == 'processUrl' && row.processUrl && row.processUrl.length > 0) {
        this.$api
          .ipsmGetPictureUrls({
            repairAttachmentUrl: row.processUrl
          })
          .then((res) => {
            // this.imgArr = res.data.repairAttachmentUrl
            res.data.repairAttachmentUrl.forEach((item) => {
              this.imgArr.push(this.$tools.imgUrlTranslation(item))
            })
          })
        this.dialogVisibleImg = true
      } else {
        this.$message.error('该记录无图片')
      }
    },
    handleSizeChange(val) {
      this.paginationData.currentPage = 1
      this.paginationData.pageSize = val
      this.getTableData()
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.getTableData()
    },
    closeDialogImg() {
      this.dialogVisibleImg = false
      this.imgArr = []
    }
  }
}
</script>

<style lang="scss" scoped>
.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
  padding: 10px;
  overflow-y: auto;
}

.content_box {
  height: calc(100% - 30px);
  margin-top: 10px;
  //   padding: 20px 25px 25px 25px;
  background: #fff;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.contentTable-footer {
  padding: 10px 0 0;
}

.table-block-text {
  width: 80px;
  line-height: 24px;
  text-align: center;
  cursor: pointer;
  color: #fff;
}
</style>
