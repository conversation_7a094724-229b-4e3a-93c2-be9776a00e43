import { downFile, getRequest, postFile, postFormData, postRequest, post_json } from '../http.js'
// 服务前缀
const PrefixService = __PATH.VUE_RHMS_API
/**
 * 公租房API
 */
const apiConfig = {
  // ---------------------------------字典配置--------------------------------------------------
  /** 分页获取字典 */
  queryDictPage: postRequest('dictData/selectDictDataList', PrefixService),
  /** 删除字典 */
  deleteDictById: postRequest('dictData/deleteById', PrefixService),
  /** 启用停用字典 */
  enableOrDisable: postRequest('dictData/enableOrDisable', PrefixService),
  /** 字典详情 */
  getDictById: getRequest('dictData/getById', PrefixService),
  /** 查询字典类型列表 */
  selectDictTypeList: postRequest('dictType/selectDictTypeList', PrefixService),
  /** 保存字典 */
  dictSave: postRequest('dictData/save', PrefixService),
  /** 更新字典 */
  dictUpdate: postRequest('dictData/update', PrefixService),
  // ---------------------------------空间----------------------------------------------------
  // 查询省市区街道行政数据列表
  selectAdministrativeList: getRequest('space-entity/selectAdministrativeList', PrefixService),
  // 更新空间位置排序
  updateSpaceSort: postRequest('space-entity/updateSort', PrefixService),
  // 所有空间节点
  allSpaceList: postRequest('space-entity/queryAll', PrefixService),
  // ---------------------------------小区----------------------------------------------------
  // 删除小区信息
  deleteSpaceEstateInfoById: getRequest('space-estate-info-entity/deleteSpaceEstateInfoById', PrefixService),
  // 新增小区
  saveSpaceEstateInfo: postRequest('space-estate-info-entity/saveSpaceEstateInfo', PrefixService),
  // 更新小区
  updateSpaceEstateInfo: postRequest('space-estate-info-entity/updateSpaceEstateInfo', PrefixService),
  // 获取小区信息
  getSpaceEstateInfoById: getRequest('space-estate-info-entity/getById', PrefixService),
  // ---------------------------------楼栋----------------------------------------------------
  // 删除楼栋信息
  deleteBuildingById: getRequest('space-building-info-entity/deleteById', PrefixService),
  // 获取楼栋信息
  getBuildingById: getRequest('space-building-info-entity/getById', PrefixService),
  // 更新楼栋信息
  updateBuildingInfo: postRequest('space-building-info-entity/updateSpaceBuildingInfo', PrefixService),
  // 新增楼栋信息
  saveBuildingInfo: postRequest('space-building-info-entity/saveSpaceBuildingInfo', PrefixService),
  // ---------------------------------单元----------------------------------------------------
  // 删除单元信息
  deleteUnitById: getRequest('space-unit-info-entity/deleteById', PrefixService),
  // 获取单元信息
  getUnitById: getRequest('space-unit-info-entity/getById', PrefixService),
  // 更新单元信息
  updateUnitInfo: postRequest('space-unit-info-entity/updateSpaceUnitInfo', PrefixService),
  // 新增单元信息
  saveUnitInfo: postRequest('space-unit-info-entity/saveSpaceUnitInfo', PrefixService),
  // ---------------------------------楼层----------------------------------------------------
  // 删除楼层信息
  deleteFloorById: getRequest('space-floor-info-entity/deleteById', PrefixService),
  // 获取楼层信息
  getFloorById: getRequest('space-floor-info-entity/getById', PrefixService),
  // 新增、更新楼层信息
  saveFloorInfo: postRequest('space-floor-info-entity/saveOrUpdateSpaceFloorInfo', PrefixService),
  // ---------------------------------房源----------------------------------------------------
  // 删除房源信息
  deleteHouseById: postRequest('house-info-entity/deleteById', PrefixService),
  // 获取房源信息
  getHouseById: getRequest('house-info-entity/getById', PrefixService),
  // 新增、更新房源信息
  saveHouseInfo: postRequest('house-info-entity/saveOrUpdate', PrefixService),
  // 房源分页查询
  queryHouseByPage: postRequest('house-info-entity/selectPageList', PrefixService),
  // 更改房源状态
  updateHouseStatus: postRequest('house-info-entity/updateBusProc', PrefixService),
  // 房屋下架
  updateBusProc: postRequest('house-info-entity/updateBusProc', PrefixService),
  // 下载房源模板
  downloadHouseResourceTemplate: downFile('house-info-entity/downloadTemplate', PrefixService),
  // 房源导入
  importHouseFile: postFormData('house-info-entity/importExcel', PrefixService),
  // ---------------------------------房源扩展字段----------------------------------------------------
  // 获取房源扩展字段详情信息
  getExtendFieldInfoId: getRequest('fieldInfoEntity/getById', PrefixService),
  // 分页获取房源扩展字段
  queryExtendFieldByPage: postRequest('fieldInfoEntity/selectFieldInfoList', PrefixService),
  // 获取房源扩展字段列表
  queryExtendFieldList: postRequest('fieldInfoEntity/selectList', PrefixService),
  // 新增房源扩展字段
  saveExtendFieldInfo: postRequest('fieldInfoEntity/save', PrefixService),
  // 更新房源扩展字段
  updateExtendFieldInfo: postRequest('fieldInfoEntity/update', PrefixService),
  // 删除房源扩展字段
  deleteExtendFieldById: postRequest('fieldInfoEntity/deleteById', PrefixService),
  // 更改房源扩展字段状态
  updateExtendFieldStatus: postRequest('fieldInfoEntity/enableOrDisable', PrefixService),
  // ---------------------------------合同----------------------------------------------------
  // 分页获取合同
  queryHouseContractByPage: postRequest('houseContract/selectPageList', PrefixService),
  // ---------------------------------账单----------------------------------------------------
  // 分页获取合同
  queryHouseBillByPage: postRequest('bill/selectPageListByContractId', PrefixService),
  // ---------------------------------租户----------------------------------------------------
  // 分页获取租户
  queryTenantByPage: postRequest('houseUser/checkPage', PrefixService),
  // ---------------------------------抄表----------------------------------------------------
  // 分页获取抄表信息
  queryMeterRecordByPage: postRequest('waterPowerRecord/pageList', PrefixService),
  // ---------------------------------历史----------------------------------------------------
  // 分页获取历史事件记录
  moveInRecordByPage: postRequest('houseHistoryRecord/pageList', PrefixService),
  // 获取事件详情
  historyEventDetail: getRequest('houseHistoryRecord/details', PrefixService),
  // ---------------------------------通用设置----------------------------------------------------
  // 获取通用设置
  getCommonSetting: getRequest('configSettings/get', PrefixService),
  // 更新通用设置
  saveOrUpdateCommonSetting: postRequest('configSettings/saveOrUpdate', PrefixService),
  // 获取房源详情
  // ---------------------------------提醒设置----------------------------------------------------
  // 获取提醒设置详情信息
  getReminderSettingsDetails: getRequest('reminderSettings/get', PrefixService),
  // 保存或修改提醒设置
  saveOrUpdateReminderSettings: postRequest('reminderSettings/saveOrUpdate', PrefixService),
  // ---------------------------------协议配置----------------------------------------------------
  // 获取协议列表
  getAgreementList: postRequest('agreementInfo/selectAgreementInfoList', __PATH.VUE_RHMS_API),
  // 协议 更新
  updateAgreement: postRequest('agreementInfo/update', __PATH.VUE_RHMS_API),
  // 协议 停用
  stopAgreement: postRequest('agreementInfo/enableOrDisable', __PATH.VUE_RHMS_API),
  // ---------------------------------办理入住----------------------------------------------------
  // 校验租户是否入住或待入住
  checkTenantMoveInto: getRequest('houseHistoryRecord/alreadyExisted', PrefixService),
  // 办理入住提交
  saveMoveInto: postRequest('houseHistoryRecord/checkIn', PrefixService),
  // 退租
  checkOut: postRequest('houseHistoryRecord/checkOut', PrefixService),
  // 续租
  renewed: postRequest('houseHistoryRecord/renewed', PrefixService),
  // ---------------------------------统计分析----------------------------------------------------
  // 获取房型统计列表数据
  getRoomTypeStatisticsList: getRequest('statistics/byHouseType', PrefixService),
  // 获取小区统计列表数据
  getCommunityStatisticsList: postRequest('statistics/byEstate', PrefixService),
  // 获取科室成员入住分部 饼图数据
  getDeptMemberCheckInPie: getRequest('statistics/byDept', PrefixService),
  // 获取房租租金统计
  getRentStatisticsList: postRequest('statistics/hireRent', PrefixService),
  // 获取成本收入统计
  getCostIncomeStatisticsBar: postRequest('statistics/costHireRent', PrefixService),
  // 获取房租空置率统计
  getRentVacancyStatistics: postRequest('statistics/idleRate', PrefixService),
  // 获取小区下拉数据
  getCommunityList: getRequest('space-entity/selectTree', PrefixService),
  // --------------------------------配房管理---------------------------------------------------
  // 获取配房列表
  getRoomAllocationList: postRequest('allocation/selectAllocationPage', PrefixService),
  // 获取配房详情
  getRoomAllocationDetails: getRequest('allocation/selectApproveDetail', PrefixService),
  // 获取待配房数量
  getRoomAllocationCount: postRequest('allocation/selectAllocationCnt', PrefixService),
  // 催办
  submitUrge: postRequest('allocation/pressMessage', PrefixService),
  // 房源分配
  submitAllocation: postRequest('allocation/updateAllocation', PrefixService),
  // 获取审批管理 我的待办统计
  getApproveCount: postRequest('allocation/selectApproveCnt', PrefixService),
  // 获取房源统计
  getRoomStatics: postRequest('house-info-entity/selectHouseStatics', PrefixService),
  // 发送配房通知
  sendAllocationNotice: postRequest('allocation/sendHouseSelectNotice', PrefixService),
  // 移入待配房列表
  moveToWaitAllocation: postRequest('allocation/moveHouseAllocation', PrefixService),
  // 弃选解冻
  cancelAllocation: postRequest('allocation/houseUnFreeze', PrefixService),
  // 弃选
  abandonSelectionSubmit: postRequest('allocation/putHouseToAbandon', PrefixService),
  // 移入轮候
  moveToWaiting: postRequest('allocation/putHouseToQueue', PrefixService),
  // 文件管理列表
  fileManagementList: postRequest('fileInfo/selectFileInfoList', PrefixService),
  // 文件管理保存、更新
  fileSaveUpdata: postRequest('fileInfo/saveOrUpdateFile', PrefixService),
  // 文件批量下载
  fileBatchDownload: post_json('fileInfo/downloadFileBatch', PrefixService),
  // 文件批量删除
  fileBatchDelete: postRequest('fileInfo/deleteById', PrefixService),
  // 文件详情
  fileDeatil: getRequest('fileInfo/getById', PrefixService),
  // 查询是否有文件创建
  checkExistFile: getRequest('fileInfo/selectExistFile', PrefixService),
  // 公示管理列表
  noticeDataList: postRequest('noticeInfo/selectInfoList', PrefixService),
  // 公示管理保存、更新
  noticeSaveUpdata: postRequest('noticeInfo/saveOrUpdate', PrefixService),
  // 公示管理撤回
  noticeWithdraw: getRequest('noticeInfo/revokeById', PrefixService),
  // 公示管理发布
  noticePublish: getRequest('noticeInfo/publishById', PrefixService),
  // 公示管理获取展示的公示信息
  noticePublishShowPage: postRequest('noticeInfo/getPublishShowPage', PrefixService),
  // 公示管理公示详情
  noticeDetail: getRequest('noticeInfo/getById', PrefixService),
  // 公示管理删除
  noticeDelete: getRequest('noticeInfo/deleteById', PrefixService),
  // 获取minio前缀
  getMinioBaseUrl: getRequest('noticeInfo/getMinioBaseUrl', PrefixService),
  // 合同管理批量下载
  contractBatchDownload: postRequest('houseContract/downloadContractBatch', PrefixService),
  // 获取账单列表
  getBillList: postRequest('bill/getDeductionTable', PrefixService),
  // 获取配房详情
  getApproveDetail: postRequest('allocation/selectApproveDetail', PrefixService),
}
export default apiConfig
