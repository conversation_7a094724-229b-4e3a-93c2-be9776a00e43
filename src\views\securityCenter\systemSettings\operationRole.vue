<template>
  <PageContainer>
    <div slot="content" class="role-content" style="height: 100%;">
      <div class="role-content-left">
        <div class="middle_tools">
          <el-button type="primary" @click="addRoleShow(0)">新增</el-button>
          <el-button type="primary" :disabled="multipleSelection.length != 1" @click="addRoleShow(1)">编辑</el-button>
          <el-button type="primary" :disabled="multipleSelection.length != 1" @click="deletePerson">删除</el-button>
        </div>
        <div class="table-list">
          <el-table
            ref="tableList"
            v-loading="tableLoading"
            :data="tableData"
            :height="tableHeight"
            border
            stripe
            highlight-current-row
            @selection-change="handleSelectionChange"
            @row-click="getMenuList"
          >
            <el-table-column type="selection" width="55" fixed="left" align="center"></el-table-column>
            <el-table-column type="index" label="序号" width="65">
              <template slot-scope="scope">
                <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="roleName" width="120" show-overflow-tooltip label="角色名称"></el-table-column>
            <el-table-column prop="roleCode" width="120" show-overflow-tooltip label="角色编码"></el-table-column>
            <el-table-column prop="roleSort" width="80" show-overflow-tooltip label="排序号"></el-table-column>
            <el-table-column prop="remarks" show-overflow-tooltip label="备注"></el-table-column>
            <el-table-column prop="updateDate" width="120" show-overflow-tooltip label="更新时间"></el-table-column>
            <el-table-column prop="status" width="80" show-overflow-tooltip label="状态">
              <template slot-scope="scope">
                <div :class="'status' && scope.row.status == 0 ? 'colorGreen' : 'colorGray'">{{ scope.row.status == 0 ? '启用' : '禁用' }}</div>
              </template>
            </el-table-column>
            <el-table-column label="启用/禁用" width="120" show-overflow-tooltip>
              <template slot-scope="scope">
                <el-switch v-model="scope.row.status" active-value="0" inactive-value="1" active-color="#5188fc" @change="switchHandle(scope.row)"></el-switch>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <!-- <div class="contentTable-footer">
          <el-pagination
            class="pagination"
            :current-page="paginationData.currentPage"
            :page-sizes="[15, 30, 50, 100]"
            :page-size="paginationData.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="paginationData.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div> -->
      </div>
      <div class="role-content-right">
        <div class="toptip">
          <span class="green_line"></span>
          权限设置
        </div>
        <div class="left_content" style="overflow: auto; padding: 20px 0 0 20px; height: calc(100% - 100px);">
          <div style="display: inline-block; width: 100%; text-align: left; margin-bottom: 10px;">PC权限：</div>
          <div style="display: flex;">
            <el-tree
              ref="tree"
              style="width: calc(100% - 80px); margin-left: 30px;"
              :data="treeData"
              show-checkbox
              :props="defaultProps"
              :default-checked-keys="idArr"
              node-key="menuId"
              default-expand-all
            ></el-tree>
          </div>
        </div>
        <div class="footer">
          <el-button style="flex: 1; font-size: 14px;" type="primary" :loading="treeLoading" icon="el-icon-plus" @click="sure">保存</el-button>
          <el-button style="flex: 1; font-size: 14px;" type="primary" :loading="treeLoading" icon="el-icon-edit" @click="chooseAll(1)">全选</el-button>
          <el-button style="flex: 1; font-size: 14px;" type="primary" :loading="treeLoading" icon="el-icon-delete" @click="chooseAll(0)">全不选</el-button>
        </div>
      </div>
      <template v-if="dialogVisible">
        <addRole :ifType="ifType" :roleData="roleData" :dialogVisible="dialogVisible" :roleType="roleType" @closeDialog="closeDialog" @init="init"></addRole>
      </template>
    </div>
  </PageContainer>
</template>

<script>
import addRole from './components/addRoleNew'
import { transData } from '@/util'
export default {
  name: 'operationRole',
  components: { addRole },
  data() {
    return {
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      multipleSelection: [],
      tableData: [],
      roleData: {},
      roleType: JSON.parse(sessionStorage.getItem('LOGINDATA')).moduleIdentity || '',
      advancClose: true,
      idArr: [],
      treeLoading: false,
      tableLoading: false,
      blockLoading: false,
      organizationTypeArr: [],
      dialogVisible: false,
      treeData: [],
      tempTreeData: [],
      // treeDataMobile: [],
      defaultProps: {
        children: 'children',
        label: 'menuName',
        value: 'menuId',
        checkStrictly: true
      },
      data: '',
      clickRow: '',
      ifType: ''
    }
  },
  computed: {
    tableHeight() {
      return document.body.clientHeight - 210
    }
  },
  mounted() {
    // let baseInfo = JSON.parse(sessionStorage.getItem("LOGINDATA"));
    // if (
    //   baseInfo.isBoss == "1" &&
    //   (baseInfo.departLevel == "1" || baseInfo.departLevel == "2")
    // ) {
    //   console.log("无访问权限");
    //   return;
    // }
    this.init()
  },
  methods: {
    sure() {
      let list = this.$refs.tree.getCheckedNodes().concat(this.$refs.tree.getHalfCheckedNodes())
      let temp = []
      list.forEach((item) => {
        temp.push(item.menuId)
      })
      if (list.length == 0) {
        this.$message.error('请先为该角色分配菜单')
        return
      }
      let data = {
        roleCode: this.clickRow.id,
        menuCodes: temp.join(',')
      }
      this.$api.ipsmAddAssociated(data).then((res) => {
        if (res.code == 200) {
          this.$message.success(res.message)
          this.init()
        } else if (res.message) {
          this.$message.error(res.message)
        }
      })
    },
    deletePerson() {
      this.$confirm('确定删除该角色?', '消息', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (this.multipleSelection[0].sysIdentity == 'systemAdminCode') {
          this.$message.error('当前角色不允许删除')
        } else {
          let data = {
            roleCode: this.multipleSelection[0].id,
            sysIdentity: this.multipleSelection[0].sysIdentity
          }
          this.$api.ipsmDelRole(data).then((res) => {
            if (res.code == 200) {
              this.$message.success(res.message)
              this.init()
            } else {
              this.$message.error(res.message)
            }
          })
        }
      })
    },
    getMenuList(row, column, event) {
      this.$refs['tableList'].toggleRowSelection(row)
      if (column.label != '启用/禁用') {
        this.clickRow = row
        this.treeLoading = true
        let data = {
          roleCode: this.clickRow.id
        }
        data.sysFlag = 'IDPS_CORE'
        this.$api.ipsmUserMenuControllerGetMenuList(data).then((res) => {
          this.treeLoading = false
          if (res.code == 200) {
            this.tempTreeData = res.data.pcPlatform
            this.treeData = transData(res.data.pcPlatform, 'menuId', 'parentId', 'children')
            // 菜单选中
            let list = new Array()
            let listNo = new Array()
            res.data.pcPlatform.forEach((item) => {
              if (item.isShow == 0) {
                list.push(item.menuId)
              } else {
                listNo.push(item.menuId)
              }
            })
            this.$nextTick(() => {
              list.forEach((item) => {
                this.$refs.tree.setChecked(item, true, false)
              })
              listNo.forEach((item) => {
                this.$refs.tree.setChecked(item, false, false)
              })
            })
          }
        })
      }
    },
    chooseAll(state) {
      if (state == 1) {
        this.idArr = this.tempTreeData.map((item) => {
          return item.menuId
        })
      } else {
        this.$refs.tree.store._getAllNodes().map((item) => {
          item.checked = false
        })
      }
    },
    // 启用禁用
    switchHandle(row) {
      console.log(row.id, 'row')
      let data = {
        roleType: this.roleType,
        status: row.status,
        roleCode: row.id,
        personName: JSON.parse(sessionStorage.getItem('LOGINDATA')).name,
        roleName: row.roleName
      }
      this.$api.ipsmUpdateStatus(data).then((res) => {
        if (res.code == 200) {
          this.$message.success(res.message)
          this.init()
        } else if (res.message) {
          this.$message.error(res.message)
        }
      })
    },
    addRoleShow(type) {
      this.ifType = type
      if (type == 1) {
        this.roleData = this.multipleSelection[0]
        this.data = this.multipleSelection[0]
      } else {
        this.roleData = {}
      }
      this.dialogVisible = true
    },
    closeDialog() {
      this.dialogVisible = false
    },
    init() {
      this.tableLoading = true
      this.$api
        .ipsmUserRoleGetRoleList({
          roleType: this.roleType,
          isShowSystemAdmin: 0,
          pageSize: this.paginationData.pageSize,
          currentPage: this.paginationData.currentPage
        })
        .then((res) => {
          this.tableLoading = false
          this.paginationData.total = res.data.sum
          this.tableData = res.data
          if (this.tableData.length > 0) {
            this.getMenuList(this.tableData[0], { label: 'init' })
          }
        })
        .catch(() => {
          this.tableLoading = false
        })
    },

    //  ----------------------------------------------------分页相关------------------------------------------------------------
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.init()
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.init()
    },
    //  ----------------------------------------------------树状结构相关---------------------------------------------------------

    //  ------------------------------------------------操作表格中数据------------------------------------------------------------
    handleSelectionChange(val) {
      this.multipleSelection = val
      // if(this.multipleSelection.length == 1){
      //   this.roleData = this.multipleSelection[0]
      // } else {
      //   this.roleData = {}
      // }
    }
  }
}
</script>
<style lang="scss" scoped>
// el-tree-node is-expanded is-focusable
::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: linear-gradient(to right, #d9e1f8, #fff);
}

::v-deep .el-tree-node__content {
  height: 32px;
}

.role-content {
  height: 100%;
  display: flex;

  .role-content-left {
    flex: 1;
    height: 100%;
    padding: 10px;
    background: #fff;
    border-radius: 4px;
    margin-right: 12px;
    overflow: auto;

    .middle_tools {
      margin: 20px 0;
    }
  }

  .role-content-right {
    width: 330px;
    height: 100%;
    min-width: 0;
    padding: 10px;
    background: #fff;
    border-radius: 4px;
  }
}

.colorGray {
  color: #909399;
}

.colorGreen {
  color: #5188fc;
}

.footer {
  height: 60px;
  box-shadow: 0 -4px 4px 0 #f3f4f8;
  width: 100%;
  padding: 0 20px;
  box-sizing: border-box;
  line-height: 60px;
  display: flex;
  z-index: 100;
  position: relative;
  justify-content: center;
  align-items: center;
  font-size: 14px;
}

.contentTable-footer {
  padding: 5px 0 0;
}
</style>
