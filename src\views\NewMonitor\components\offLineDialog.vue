<template>
  <el-dialog
    v-if="dialogShow"
    :title="title"
    width="85%"
    :visible.sync="dialogShow"
    custom-class="model-dialog"
  >
    <div style=" width: 100%; height: 100%;">
      <div class="time_search">
        <TimeQuery ref="timeQuery" :running="false" :timeData="timeData" @submit="submit"/>
      </div>
      <div class="off_line">
        <div class="off_line_left">
          <div v-loading="loading.distributionLoading" style="margin-bottom: 10px; height: calc(50% - 5px);">
            <ContentCard title="离线运行分布" :style="{height:'100%'}">
              <div slot="title-right" class="viewDetails">
                <el-select v-model="hourOrMouth" size="mini" placeholder="时间" @change="retCountOffLineDistribute">
                  <el-option
                    v-for="item in Xdisplay"
                    :key="item.value"
                    :disabled="!item.disabled"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </div>

              <echarts slot="content" ref="offlineDistribution" domId="offlineDistribution" @onClickChart="(data) => onClickeDistribution(data)" />
            </ContentCard>
          </div>
          <div v-loading="loading.offlineRankingLoading" style="height: calc(50% - 5px);">
            <ContentCard title="离线设备排行" :style="{height:'100%'}">
              <div slot="title-right" class="viewDetails">
                <el-select slot="title-right" v-model="rankingType" size="mini" placeholder="时间" @change="retCountMonitoringOffLineData">
                  <el-option
                    v-for="item in rankingTypeArr"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </div>
              <echarts slot="content" ref="offlineRanking" domId="offlineRanking" @onClickChart="(data) => onClickeRanking(data)" />
            </ContentCard>
          </div>
        </div>
        <div class="off_line_right">
          <ContentCard title="设备离线记录" :style="{height:'100%'}">
            <div slot="content" class="card-content">
              <div class="query_criteria">
                <el-form ref="listForm" :model="queryParams" :inline="true">
                  <el-form-item prop="harvester">
                    <el-input
                      v-model="queryParams.harvester"
                      placeholder="请输入设备名称/ID"
                      clearable
                      size="small"
                    />
                  </el-form-item>
                  <el-form-item prop="spaceId">
                    <el-select ref="treeSelect" v-model="queryParams.spaceId" clearable placeholder="请选择设备位置" @clear="handleClear">
                      <el-option hidden :value="queryParams.spaceId" :label="areaName"> </el-option>
                      <el-tree
                        :data="serverSpaces"
                        :props="serverDefaultProps"
                        :load="serverLoadNode"
                        lazy
                        :expand-on-click-node="false"
                        :check-on-click-node="true"
                        @node-click="handleNodeClick"
                      >
                      </el-tree>
                    </el-select>
                  </el-form-item>
                  <el-form-item prop="dateRange">
                    <el-date-picker
                      v-model="dateRange"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="开始月份"
                      end-placeholder="结束月份"
                      :picker-options="pickerOptions"
                      :clearable="false"
                      prefix-icon=0
                      value-format="yyyy-MM-dd"
                      @change="changeDeadline"
                    >
                    </el-date-picker>
                  </el-form-item>
                  <el-form-item prop="deviceStatus">
                    <el-select
                      v-model="queryParams.deviceStatus"
                      clearable
                      size="small"
                      placeholder="设备状态"
                    >
                      <el-option
                        v-for="item in deviceArr"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" plain @click="resetQuery">重置</el-button>
                    <el-button type="primary" @click="handleQuery">查询</el-button>
                  </el-form-item>
                </el-form>
              </div>

              <div class="card-content-table table-content">
                <el-table ref="table" v-loading="loading.tableLoading" :resizable="false" border :data="tableData" height="100%" style="width: 99%;">
                  <el-table-column prop="imsName" label="设备名称" show-overflow-tooltip />
                  <el-table-column prop="spaceIds" label="使用位置" show-overflow-tooltip />
                  <el-table-column prop="offLineTime" label="离线时间" show-overflow-tooltip />
                  <el-table-column prop="resumeTime" label="恢复时间" show-overflow-tooltip />
                  <el-table-column prop="status" label="设备状态">
                    <template slot-scope="scope">
                      <span :class="scope.row.status=='6'?'offLine':'onLine'">{{scope.row.status=='6'?'离线':'在线'}}</span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div class="card-content-footer">
                <el-pagination
                  :current-page="queryParams.page"
                  :page-sizes="[10, 20, 30, 50]"
                  :page-size="queryParams.pageSize"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="total"
                  @size-change="paginationSizeChange"
                  @current-change="paginationCurrentChange"
                />
              </div>
            </div>
          </ContentCard>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { transData, ListTree } from '@/util'
import mixins from './mixins/chartMixin.js'
import TimeQuery from './timeQuery.vue'
import moment from 'moment'
export default {
  name: 'offLineDialog',
  components: { TimeQuery },
  mixins: [mixins],
  data() {
    return {
      dialogShow: false,
      Xdisplay: [
        {value: 0, label: '24小时统计分布', disabled: true, fn: (diffDays) => diffDays <= 0},
        {value: 1, label: '日分布', disabled: false, fn: (diffDays) => diffDays > 0 },
        {value: 2, label: '月分布', disabled: false, fn: (diffDays) => diffDays > 0 }
        // {value: 1, label: '日分布', disabled: false, fn: (diffDays) => diffDays > 0 && diffDays < 30 * 7 },
        // {value: 2, label: '月分布', disabled: false, fn: (diffDays) => diffDays > 30 }
      ],
      hourOrMouth: 0,
      rankingTypeArr: [
        {value: 0, label: '设备名称排行' },
        {value: 1, label: '使用区域排行' }
      ],
      rankingType: 0,
      deviceArr: [
        {value: '0,1', label: '正常' },
        {value: '2', label: '离线' }
      ],
      // 顶部查询条件
      topQuery: {},
      // 列表查询条件
      queryParams: {},
      dateRange: null,
      projectCode: null,
      total: 0,
      // 列表内容
      tableData: [],
      loading: {
        tableLoading: false,
        distributionLoading: false,   // 分布
        offlineRankingLoading: false   // 排行
      },
      title: '',
      serverSpaces: [], // 空间位置
      spaces: [], // 空间位置
      serverDefaultProps: {
        label: 'ssmName',
        isLeaf: 'leaf',
        children: 'children'
      },
      areaName: '', // 选中 下拉树的name
      // 传入时间组件的携带参数   从一级页面传入的时间
      timeData: {},
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      }
    }
  },
  mounted() {
    this.getTreelist()
  },
  methods: {
    // 离线设备排行点击
    onClickeRanking(data) {
      console.log('onClickeRanking============', data)
      var newData = {
        harvester: data.name
      }
      this.resetQuery(newData)
    },
    getDateStart(val) {
      let month = val.slice(5)
      // 当前日期
      let time = new Date()
      // 选取的月份
      let monthNum = month
      // 某月第一天
      let startTime = moment(time).month(monthNum - 1).date(1).startOf('month').format('YYYY-MM-DD')
      // 某月最后一天
      let endTime = moment(time).month(monthNum - 1).date(1).endOf('month').format('YYYY-MM-DD')
      return [startTime, endTime]
    },
    // 离线运行分布点击
    onClickeDistribution(data) {
      console.log('onClickeDistribution============', data)
      let dateRange
      if (this.hourOrMouth == 2) {
        dateRange = this.getDateStart(data.name)
      } else {
        let time = moment(data.name).format('YYYY-MM-DD')
        dateRange = [time, time]
      }
      var newData = {
        dateRange,
        startTime: dateRange[0],
        endTime: dateRange[1]
      }
      this.resetQuery(newData)
    },
    // 统计监测项离线分布
    retCountOffLineDistribute() {
      this.loading.distributionLoading = true
      let newData = {
        distributionType: this.hourOrMouth,
        dateType: this.topQuery.dateType,
        startTime: this.topQuery.startTime,
        endTime: this.topQuery.endTime,
        projectCode: this.topQuery.projectCode
      }
      this.$api.CountOffLineDistribute(newData).then(res => {
        if (res.code == 200) {
          this.$refs.offlineDistribution.init(this.dialogSetLineChart(res.data, []))
          this.loading.distributionLoading = false
        }
      })
    },
    // 统计离线排行
    retCountMonitoringOffLineData() {
      let newData = {
        rankingType: this.rankingType,
        dateType: this.topQuery.dateType,
        startTime: this.topQuery.startTime,
        endTime: this.topQuery.endTime,
        projectCode: this.topQuery.projectCode
      }
      this.$api.CountMonitoringOffLineData(newData).then(res => {
        if (res.code == 200) {
          this.$refs.offlineRanking.init(this.dialogSetBarChart(res.data, []))
          this.loading.offlineRankingLoading = false
        }
      })
    },
    changeDeadline(e) {
      // 实际交稿时间
      this.queryParams.startTime = e ? e[0] : null
      this.queryParams.endTime = e ? e[1] : null
    },
    // 列表查询
    getList() {
      this.loading.tableLoading = true
      let data = {...this.queryParams, projectCode: this.projectCode}
      this.$api.OnOrOfflineRecord(data).then(res => {
        if (res.code == 200) {
          this.tableData = res.data.list
          this.total = res.data.count
          setTimeout(() => {
            this.loading.tableLoading = false
          }, 1000)
        }
      })
    },
    /**
     * 查询 | 调接口
     */
    submit(data) {
      // 控制右侧时间选择下拉
      let bool
      this.Xdisplay.forEach((ele, index) => {
        bool = ele.fn(data.diffDays)
        ele.disabled = bool
        bool ? this.hourOrMouth = ele.value : ''
      })
      data['projectCode'] = this.projectCode
      this.topQuery = data
      this.retCountOffLineDistribute()
      this.retCountMonitoringOffLineData()
      this.resetQuery(data)
    },
    // 一级页面打开弹窗  赋值title  将一级页面时间带入弹框
    getEchartData(data, timeData) {
      if (timeData) {
        this.timeData = timeData
      }
      this.title = `${data.sysName}离线统计`
      this.dialogShow = true
      // console.log('data.projectCode===============', data.projectCode)
      this.projectCode = data.projectCode
      // this.projectCode = 'IEMC-BroadcastTerminal'
    },
    // 重置列表查询
    resetQuery(data) {
      this.queryParams = {
        harvester: data.harvester ? data.harvester : null,
        page: 1,
        pageSize: 10,
        startTime: data.startTime ? data.startTime : null,
        endTime: data.endTime ? data.endTime : null,
        spaceId: null,
        deviceStatus: null
      },
      this.dateRange = data.dateRange
      this.handleQuery()
    },
    // 列表查询
    handleQuery() {
      this.getList()
    },
    paginationSizeChange(val) {
      this.queryParams.pageSize = val
      this.queryParams.page = 1
      this.getList()
    },
    paginationCurrentChange(val) {
      this.queryParams.page = val
      this.getList()
    },
    getLineChart() {
      let data = [
        {
          name: '烟感',
          value: 423,
          proportion: 34,
          ring: 11.22
        },
        {
          name: '温度',
          value: 233,
          proportion: 33,
          ring: 11.22
        },
        {
          name: '手报',
          value: 543,
          proportion: 33,
          ring: 11.22
        }
      ]
      // this.$refs.offlineDistribution.init(this.dialogSetLineChart(data, []))
      // this.$refs.offlineRanking.init(this.dialogSetBarChart(data, []))
    },
    // 选择下拉树 数据
    handleNodeClick(data) {
      this.queryParams.spaceId = data.id
      this.areaName = data.ssmName
      this.$refs.treeSelect.blur()
    },
    // 空间数据清除
    handleClear() {
      this.queryParams.spaceId = ''
      this.areaName = ''
    },
    // 下拉树懒加载
    serverLoadNode(node, resolve) {
      if (node.level == 0) {
        return resolve(node.data)
      } else if (node.level > 0 && node.level < 4) {
        return resolve(node.data.children)
      } else {
        return this.spaceTreeChange(node.data, resolve)
      }
    },
    // 获取服务空间树形结构
    getTreelist() {
      // this.$api.getStructureTree({}, __PATH.USER_CODE).then((res) => {
      this.$api.getStructureTree({}).then((res) => {
        if (res.code == 200) {
          this.spaces = res.data
          // 增加 懒加载节点
          res.data.map((e) => {
            e.leaf = false
          })
          // 转换为树形结构数据
          this.serverSpaces = transData(res.data, 'id', 'pid', 'children')
        }
      })
    },
    spaceTreeChange(value, resolve) {
      const child = ListTree(this.spaces, value.pid)
      child.push(value.id)
      let treeId = child.toString()
      let data = {
        current: 1,
        functionDictId: '',
        simCode: treeId,
        size: 999
      }
      // this.$api.getSpaceInfoList(data, __PATH.USER_CODE).then((res) => {
      this.$api.getSpaceInfoList(data).then((res) => {
        if (res.code == 200) {
          if (typeof resolve === 'function') {
            let treeNodeData = JSON.parse(JSON.stringify(res.data.records))
            treeNodeData.map((e) => {
              e.leaf = true
            })
            resolve(treeNodeData)
          }
        }
      })
    }
  }
}
</script>

<style scoped>
.viewDetails /deep/ .el-input--mini .el-input__inner {
  height: 20px;
  background: #f6f5fa;
  color: #7f848c;
  font-size: 12px;
  border: none;
}

.viewDetails /deep/ .el-input .el-input__icon {
  line-height: 28px;
}

</style>
<style lang="scss" scoped>
@mixin spot {
  content: "";
  display: inline-block;
  width: 8px;
  height: 8px;
  background: #00bc6d;
  border-radius: 50%;
  margin-right: 5px;
}

.onLine::before {
  @include spot;

  background: #00bc6d;
}

.offLine::before {
  @include spot;

  background: #ccced3;
}

.onLine {
  color: #00bc6d;
}
// .offLine {
//   color: #ccced3;
// }
.time_search {
  width: 100%;
  padding: 0 20px;
  background: #fff;
}

.off_line {
  display: flex;
  margin-top: 20px;
  height: calc(78vh - 220px);

  &_left {
    height: 100%;
    margin-right: 10px;
    width: 25%;

    .el-select {
      width: 140px;
    }

    .viewDetails {
      user-select: none;
      cursor: pointer;
      margin: 0 !important;
      font-size: 14px;
      color: #3562db;
      position: absolute;
      right: 15px;
      top: 0;
    }
  }

  &_right {
    height: 100%;
    flex: 1;

    .card-content {
      height: 100%;
      padding: 0 0 0 4px;
      display: flex;
      flex-direction: column;

      .query_criteria {
        display: flex;
      }

      .card-content-table {
        flex: 1;
        overflow: auto;
        margin-top: 16px;

        ::v-deep .el-progress {
          .el-progress-bar__outer {
            border-radius: 0;
            height: 8px !important;
          }

          .el-progress-bar__inner {
            border-radius: 0;
          }
        }
      }

      .card-content-footer {
        padding: 10px 0 0;
      }
    }
  }
}
</style>
