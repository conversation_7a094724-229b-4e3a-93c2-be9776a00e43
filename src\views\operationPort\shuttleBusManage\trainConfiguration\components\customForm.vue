<template>
  <el-dialog
    v-dialogDrag
    class="component dictionary-value-edit"
    :title="'车次列表自定义'"
    width="750px"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :visible.sync="customVisible"
    custom-class="model-dialog"
    :before-close="customClose"
  >
    <div class="content" style="padding: 10px; display: flex">
      <div>
        <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
        <el-checkbox-group v-model="list" @change="handleCheckedCitiesChange">
          <el-checkbox v-for="(item, index) in tableColumn" :label="item.code" :key="index">{{ item.name }}</el-checkbox>
        </el-checkbox-group>
      </div>
    </div>

    <template #footer>
      <el-button type="primary" plain @click="customClose">取消</el-button>
      <el-button type="primary" @click="onSubmit">确认</el-button>
    </template>
  </el-dialog>
</template>
<script>
import { uploadAcceptDict } from '@/util/dict.js'
export default {
  props: {
    customVisible: {
      type: Boolean,
      default: false
    },
    tableColumn: {
      type: Array,
      default: () => []
    },
    columnCode: {
      type: Array,
      default: () => []
    }
  },
  data: function () {
    return {
      list: [],
      uploadAcceptDict,
      loadingStatus: false,
      isIndeterminate: false,
      checkAll: false,
      trainType: 'bus_train_number'
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.list = this.columnCode
      this.updateCheckAllState()
    })
  },
  methods: {
    handleCheckAllChange(val) {
      this.list = val ? this.tableColumn.map((item) => item.code) : []
      this.isIndeterminate = false
    },
    handleCheckedCitiesChange(value) {
      let checkedCount = value.length
      this.checkAll = checkedCount === this.tableColumn.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.tableColumn.length
    },
    updateCheckAllState() {
      const checkedCount = this.list.length
      this.checkAll = checkedCount === this.tableColumn.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.items.length
    },
    customClose() {
      this.$emit('customClose')
    },
    // 表单提交
    onSubmit() {
      const result = this.tableColumn.map((item) => {
        const isMatched = this.list.find((el) => el === item.code)
        return {
          ...item,
          checked: isMatched ? 1 : 0
        }
      })
      console.log(result, 'result')

      this.loadingStatus = true
      let params = {
        list: result.filter((item) => item.checked === 1),
        type: this.trainType
      }
      if (params.list.length <= 0) {
        return this.$message.error('请至少选择一种')
      }
      this.$api.fileManagement.customSave(params).then((res) => {
        this.loadingStatus = false
        if (res.code == 200) {
          this.$message.success(res.msg)
          this.customClose()
          this.$emit('getDataList')
          this.$emit('getCustomData')
        } else {
          this.$message.error(res.msg)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.component.dictionary-value-edit {
  .el-form {
    background-color: #fff;
    padding: 10px 16px 0;
    .el-form-item {
      .el-cascader,
      .el-select {
        width: 100%;
      }
    }
  }
  .content {
    width: 100%;
    max-height: 500px !important;
    overflow: auto;
    background-color: #fff !important;
  }
  ::v-deep .el-checkbox {
    margin: 8px;
  }
  .dictionary-value-edit {
    &__color {
      vertical-align: middle;
      .el-input-group__prepend {
        padding: 0;
        line-height: 0;
        border-radius: 4px 0 0 4px;
      }
      .el-color-picker {
        height: 30px;
      }
      .el-color-picker__trigger {
        border: none;
        height: 30px;
        width: 30px;
        padding: 2px;
      }
      .el-input__inner {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      }
    }
    &__upload {
      &.hide {
        .el-upload {
          opacity: 0;
          transition-duration: 0.5s;
          pointer-events: none;
        }
      }
    }
  }
}
</style>
