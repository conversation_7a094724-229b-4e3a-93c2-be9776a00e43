import AlarmDialog from '@/views/monitor/boilerMenu/components/alarmDialog.vue'
import { mapGetters } from 'vuex'
export default {
  components: {
    AlarmDialog
  },
  computed: {
    ...mapGetters({
      socketMsgs: 'socket/socketMsgs'
    })
  },
  watch: {
    socketMsgs(data) {
      console.log('接收到了报警信息-------------', data)
      this.getAlarm(JSON.parse(data))
    }
  },
  mounted() {
    this.checkIsAlarm() 
  },
  methods: {
    checkIsAlarm() {
      let uesrId = this.$store.state.user.userInfo.userId
      let alarmLocData = JSON.parse(window.localStorage.getItem(`boilerAlarm${uesrId}`))
      if (alarmLocData) {
        let alarmTime = alarmLocData.time
        let currentTime = new Date().getTime()
        let diff = alarmTime - currentTime
        if (diff <= 0) {
          this.$refs.alarmDialog.getData(alarmLocData)
        } else {
          setTimeout(() => {
            this.$refs.alarmDialog.getData(alarmLocData)
          }, diff)
        }
      }
    },
    getAlarm(data) {
      if (data.type == 'boilerAlarm') {  // 获取总览的报警数量
        this.getAlarmNum(data)
      } else if (data.type == 'alarm')  {   // 弹出报警的弹框
        this.getAlarmDialog(data)
      }
    },
    getAlarmDialog(data) {
      if (data.data.alarmData.projectCode == this.projectCode) {
        let uesrId = this.$store.state.user.userInfo.userId
        let alarmLocData = JSON.parse(window.localStorage.getItem(`boilerAlarm${uesrId}`))?.data.alarmData
        if (alarmLocData && alarmLocData.alarmObjectName == data.data.alarmData.alarmObjectName && alarmLocData.alarmStartTime == data.data.alarmData.alarmStartTime) return
        this.$refs.alarmDialog.getData(data)
      }
    },
    getAlarmNum(data) {
      try {
        this.alarmSum = data.data.alarmTotal
      } catch {

      }
    }
  }
}