<!--  -->
<!-- 模板 -->
<template>
  <!-- 页面的主体结构 -->
<div>
  <el-empty class="empty" description="暂无实验室权限" :image-size="500"></el-empty>
</div>
</template>

<script>
export default {
  // 组件注册
  components: {},
  data() {
    // 数据定义
    return {
    }
  },
  // 计算属性定义
  computed: {},
  // 监视器，用于监听数据的变化
  watch: {},
  // 方法定义，包括事件处理函数
  methods: {},
  // 生命周期钩子：创建后
  created() {},
  // 生命周期钩子：载入后
  mounted() {},

}
</script>
<style lang='scss' scoped>
::v-deep .empty {
.el-empty__description p  {
  font-size: 24px;
}
}

//@import url(); 引入公共css类
</style>