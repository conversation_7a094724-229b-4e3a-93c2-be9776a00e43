<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    title="空间审核"
    width="35%"
    :visible.sync="visible"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <div class="content" style="padding: 10px;">
      <el-form ref="ruleForm" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="审核结论" prop="radio">
          <el-radio-group v-model="form.radio" size="mini">
            <el-radio-button :label="2">通过</el-radio-button>
            <el-radio-button :label="3">驳回</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核意见">
          <el-input v-model.trim="form.textarea" type="textarea" :rows="4" placeholder="请输入审核意见" maxlength="200" show-word-limit style="width: 90%;" class="ipt"> </el-input>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="confirm('ruleForm')">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  name: 'spaceAuditDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    item: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      form: {
        radio: 2,
        textarea: ''
      },
      rules: {
        radio: [{ required: true, message: '请选择审核结论', trigger: 'change' }]
      }
    }
  },
  mounted() {
  },
  methods: {
    // 审核
    updateSpaceApplyStatus() {
      let userInfo = this.$store.state.user.userInfo.user
      let params = {
        spaceApplyStatus: this.form.radio,
        reason: this.form.textarea,
        applyId: this.item.id,
        reviewDept: userInfo.deptName,
        userName: userInfo.staffName
      }
      this.$api.UpdateSpaceApplyStatus(params).then((res) => {
        if (res.code == 200) {
          this.closeDialog()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', !this.visible)
    },
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.updateSpaceApplyStatus()
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.content {
  width: 100%;
  height: 100%;
  padding: 20px !important;
  background-color: #fff !important;
}

.model-dialog {
  padding: 0 !important;
}

.ipt {
  margin-top: 12px;
}

::v-deep .el-radio-group {
  .el-radio-button {
    margin-right: 8px;

    .el-radio-button__inner {
      background: #f6f5fa;
      border-radius: 4px;
      border: 1px solid #dcdfe6;
      // width: 72px;
      height: 28px;
    }
  }
}

::v-deep .el-radio-button__orig-radio:checked + .el-radio-button__inner {
  background-color: #3562db !important;
  border-color: #3562db;
}

::v-deep .el-dialog__body {
  padding: 0 !important;
}
</style>
