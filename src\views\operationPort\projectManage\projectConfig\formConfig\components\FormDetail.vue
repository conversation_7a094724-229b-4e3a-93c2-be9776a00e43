<script>
import { UsingStatusOptions } from '@/views/operationPort/constant'
import ContentCard from '@/components/ContentCard/index.vue'
import FormFieldEdit from './FormFieldEdit.vue'
import { FormConfigDataFillOptions, FormConfigDataSourceOptions } from '../constant'
export default {
  name: 'FormDetail',
  components: { ContentCard, FormFieldEdit },
  filters: {},
  events: ['update:visible', 'success'],
  props: {
    id: Number,
    visible: Boolean,
    readonly: Boolean
  },
  data: function() {
    return {
      formModel: {
        name: '',
        status: 1,
        remark: '',
        code: '',
        flowModelName: ''
      },
      rules: {
        name: [{ required: true, message: '请输入表单名称' }]
      },
      formKey: '',
      tableData: [],
      loadingStatus: false,
      statusOptions: UsingStatusOptions,
      // 表单编辑弹窗
      dialog: {
        show: false,
        rowIndex: -1
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    },
    toFetch() {
      return this.dialogVisible && this.id
    },
    // 当前要编辑的行
    currentRowData() {
      const row = this.tableData[this.dialog.rowIndex]
      return row ? Object.assign({}, row) : null
    }
  },
  watch: {
    toFetch(val) {
      val && this.getDetail()
    }
  },
  methods: {
    onDrawerClosed() {
      this.$refs.formRef.resetFields()
    },
    formatYesOrNo(value) {
      return +value === 1 ? '是' : '否'
    },
    formatDataSource(value) {
      return FormConfigDataSourceOptions.find((it) => it.value === +value)?.label ?? '-'
    },
    formatDataFill(value) {
      return FormConfigDataFillOptions.find((it) => it.value === +value)?.label ?? '-'
    },
    /** 获取表单配置详情 */
    getDetail() {
      this.loadingStatus = true
      this.$api.SporadicProject.queryProjectBusinessFormDetail({ id: this.id })
        .then((res) => {
          if (res.code === '200') {
            // form data
            this.formModel.name = res.data.name
            this.formModel.status = +res.data.status
            this.formModel.remark = res.data.remark
            this.formModel.flowModelName = res.data.flowModelName
            this.formModel.code = res.data.code
            this.tableData = res.data.detailList
            // readonly data
            this.formKey = res.data.formKey
            // 如果没有配置的表单项，则获取一次默认配置
            if (!this.tableData.length) {
              return this.$api.SporadicProject.getFormConfigDefaultList()
            }
          } else {
            throw res.msg
          }
        })
        .then((res) => {
          if (!res) return
          if (res.code === '200') {
            const data = res.data
            data.forEach(it => {
              it.presets = 1
            })
            this.tableData = data
          } else {
            throw res.msg || '获取默认表单项配置失败'
          }
        })
        .catch((msg) => this.$message.error(msg || '获取表单配置详情失败'))
        .finally(() => (this.loadingStatus = false))
    },
    // 提交字段
    onSubmit() {
      this.$refs.formRef
        .validate()
        .catch(() => Promise.reject())
        .then(() => {
          this.loadingStatus = true
          const params = {
            id: this.id,
            name: this.formModel.name,
            status: this.formModel.status,
            remark: this.formModel.remark,
            detailList: this.tableData
          }
          return this.$api.SporadicProject.updateProjectBusinessForm(params)
        })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('操作成功')
            this.$emit('success')
            this.dialogVisible = false
          } else {
            throw res.message
          }
        })
        .catch((msg) => {
          msg && this.$message.error(msg)
        })
        .finally(() => {
          this.loadingStatus = false
        })
    },
    // 点击编辑字段
    editField(index) {
      this.dialog.rowIndex = index
      this.dialog.show = true
    },
    // 当字段被修改时
    onFieldUpdate(data) {
      // 更新表格
      this.$set(this.tableData, this.dialog.rowIndex, data)
      this.dialog.show = false
    },
    // 格式化流程字段名称
    formatterFlowName(row) {
      const { flowName, flowCode } = row
      if (!flowName) return '-'
      return `${flowName}(${flowCode})`
    }
  }
}
</script>
<template>
  <el-drawer
    v-loading="loadingStatus"
    class="component form-detail"
    title="业务表单详情"
    size="960px"
    :visible.sync="dialogVisible"
    :close-on-press-escape="false"
    :wrapper-closable="false"
    @closed="onDrawerClosed"
  >
    <ContentCard title="基本信息">
      <template #content>
        <el-form ref="formRef" :disabled="readonly" :model="formModel" :rules="rules" label-width="95px">
          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="表单名称" prop="name">
                <el-input v-model="formModel.name" placeholder="请输入"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="编码" prop="code">
                <el-input :value="formModel.code" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="适用流程" prop="flowModelName">
                <el-input :value="formModel.flowModelName" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="状态" prop="status">
                <el-select v-model="formModel.status" placeholder="请选择">
                  <el-option v-for="item of statusOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="备注" prop="remark" class="full-line">
            <el-input v-model="formModel.remark" type="textarea" :rows="2" placeholder="请输入"></el-input>
          </el-form-item>
        </el-form>
      </template>
    </ContentCard>
    <ContentCard title="表单信息" class="form-detail__table">
      <template #content>
        <el-table height="100%" :data="tableData" border stripe table-layout="auto" class="tableAuto" row-key="id">
          <el-table-column label="业务表单" prop="operationName" width="120px" show-overflow-tooltip></el-table-column>
          <el-table-column label="编码" prop="operationCode" show-overflow-tooltip></el-table-column>
          <el-table-column label="数据来源" prop="dataSource" width="90px" show-overflow-tooltip :formatter="(row) => formatDataSource(row.dataSource)"></el-table-column>
          <el-table-column label="填充方式" prop="dataType" width="90px" :formatter="(row) => formatDataFill(row.dataType)"></el-table-column>
          <el-table-column label="数据规则" prop="dataRule" width="90px" show-overflow-tooltip :formatter="(row) => row.dataRule||'-'"></el-table-column>
          <el-table-column label="流程表单" prop="flowName" show-overflow-tooltip :formatter="formatterFlowName"></el-table-column>
          <el-table-column label="必填" prop="required" width="55px" :formatter="(row) => formatYesOrNo(row.required)"></el-table-column>
          <el-table-column label="预设" prop="presets" width="55px" :formatter="(row) => formatYesOrNo(row.presets)"></el-table-column>
          <el-table-column label="操作" width="55px">
            <template v-if="!readonly" #default="scope">
              <el-button type="text" @click="editField(scope.$index)">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </ContentCard>
    <div class="form-detail__footer">
      <el-button type="primary" plain @click="dialogVisible = false">取消</el-button>
      <el-button v-if="!readonly" type="primary" @click="onSubmit">确认</el-button>
    </div>
    <!--表单字段编辑-->
    <FormFieldEdit :visible.sync="dialog.show" :field-data="currentRowData" :form-key="formKey" @update="onFieldUpdate"></FormFieldEdit>
  </el-drawer>
</template>
<style lang="scss">
.component.form-detail {
  .el-drawer {
    .el-drawer__header {
      color: #333;
      padding: 9px 16px;
      border-bottom: solid 1px #eee;
      margin-bottom: 0;
    }
    .el-drawer__body {
      display: flex;
      flex-flow: column nowrap;
      overflow: hidden;
    }
  }
  .box-card {
    padding: 16px;
    .card-body {
      overflow: hidden;
    }
  }
  @mixin normal-text {
    cursor: default;
    color: #666;
    border: none;
    background-color: transparent;
  }
  .el-form {
    .el-form-item {
      .el-select {
        width: 100%;
      }
      .el-textarea.is-disabled .el-textarea__inner {
        @include normal-text;
        resize: none;
        padding-top: 9px;
      }
      .el-input.is-disabled {
        .el-input__inner {
          @include normal-text;
        }
        .el-input__icon {
          display: none;
        }
      }
    }
  }
  .form-detail {
    &__table {
      flex: 1;
      margin-top: -30px;
      padding-bottom: 0;
      overflow: hidden;
    }
    &__footer {
      padding: 16px;
      text-align: right;
    }
  }
}
</style>
