<template>
  <el-dropdown trigger="click" class="custom-tree-menu" size="small">
    <span class="more"><i class="el-icon-more rotate" /></span>
    <el-dropdown-menu slot="dropdown" class="hide-arrow">
      <el-dropdown-item v-for="(item, index) in events" v-show="!inputText" :key="index" :divided="index > 0" :class="[item.funcName]">
        <div style="padding: 0px 15px;text-align: center;" @click.self="clickMenu(item)">{{ item.label }}</div>
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>
<script>
export default {
  name: 'TreeOperate',
  props: {
    events: {
      type: Array,
      default: function() {
        return []
      }
    },
    // 注入数据
    data: {
      type: Object
    },
    inputText: {
      type: String,
      default: ''
    }
  },
  methods: {
    clickMenu(item) {
      this.$emit('addNode', item.funcName, this.data)
    }
  }
}
</script>
<style lang="scss" scoped>
.el-icon-more:before {
  content: '\E794';
  color: #666666;
  font-size: 16px;
}
.rotate {
  cursor: pointer;
  margin-left: 5px;
  transform: rotate(90deg);
}
.rotate:focus {
  width: 20px;
  height: 20px;
  border-radius: 4em;
  background-color: rgba(130, 132, 138, 0.2);
}
::v-deep .el-dropdown-menu__item--divided {
  border: 1px solid #ffffff !important;
}
::v-deep.el-dropdown-menu__item {
  color: #666666 !important;
  &:hover {
    background: #f6f5fa !important;
    color: #3562db !important;
  }
}
::v-deep .el-dropdown-menu--small .el-dropdown-menu__item.el-dropdown-menu__item--divided:before{
  display: none !important;
}
::v-deep .el-dropdown-menu__item--divided:before{
  display: none !important;
}
::v-deep .el-dropdown-menu--small .el-dropdown-menu__item,::v-deep .el-dropdown-menu__item{
  padding: 0px !important;
}
.removeNode {
  color: #f53f3f !important;
  &:hover {
    background: #f6f5fa !important;
    color: #f53f3f !important;
  }
}
</style>
