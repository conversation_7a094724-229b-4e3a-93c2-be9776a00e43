<template>
  <el-dialog :title="title" width="1400px" :visible.sync="videoDialgoShow" custom-class="model-dialog"
    :before-close="closeDialog" :close-on-click-modal="false">
    <div class="content_left">
      <div class="left_content">
        <div v-if="!videoUrl" style="height:300px"></div>
        <rtspCavas v-else ref="rtspCavas" :rtspUrl="videoUrl" :videoName="videoName" :hasCavas="Boolean(videoUrl)"
          class="video_preview"></rtspCavas>
      </div>
      <div class="right_content">
        <div class="select_elevator">
          视频回放
          <div class="searchForm">
            <div class="search-box">
              <el-date-picker v-model="startTime" type="datetime" value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="选择开始日期时间">
              </el-date-picker>
              <el-date-picker v-model="endTime" type="datetime" placeholder="选择结束日期时间" class="mt-5">
              </el-date-picker>
              <div class="mt-5">
                <el-button type="primary" @click="search">查询</el-button>
                <el-button type="primary" style="background: transparent;" @click="reset">重置</el-button>
              </div>
            </div>
          </div>
          <div class="mt-5">
            云台控制
            <div class="bg-img">
              <p class="bg-img-top top1" @mousedown="changeSelectElevator('UP')" @mouseup="changeSelectElevator1('UP')">
              </p>
              <p class="bg-img-top top2" @mousedown="changeSelectElevator('LEFT_UP')"
                @mouseup="changeSelectElevator1('LEFT_UP')"></p>
              <p class="bg-img-top top3" @mousedown="changeSelectElevator('RIGHT_UP')"
                @mouseup="changeSelectElevator1('RIGHT_UP')"></p>
              <p class="bg-img-top center1" @click="voiceIntercom"></p>
              <p class="bg-img-top left1" @mousedown="changeSelectElevator('LEFT')"
                @mouseup="changeSelectElevator1('LEFT')"></p>
              <p class="bg-img-top left2" @mousedown="changeSelectElevator('LEFT_DOWN')"
                @mouseup="changeSelectElevator1('LEFT_DOWN')"></p>
              <p class="bg-img-top left3" @mousedown="changeSelectElevator('DOWN')"
                @mouseup="changeSelectElevator1('DOWN')"></p>
              <p class="bg-img-top right1" @mousedown="changeSelectElevator('RIGHT')"
                @mouseup="changeSelectElevator1('RIGHT')"></p>
              <p class="bg-img-top right2" @mousedown="changeSelectElevator('RIGHT_DOWN')"
                @mouseup="changeSelectElevator1('RIGHT_DOWN')"></p>
            </div>
            <div class="parameter">
              光圈 <span :class="{ 'active': diaphragm == 'IRIS_ENLARGE' }"
                @mousedown="changeSelectElevator('IRIS_ENLARGE')"
                @mouseup="changeSelectElevator1('IRIS_ENLARGE')">扩大</span>
              <span :class="{ 'active': diaphragm == 'IRIS_REDUCE' }" @mousedown="changeSelectElevator('IRIS_REDUCE')"
                @mouseup="changeSelectElevator1('IRIS_REDUCE')">缩小</span>
            </div>
            <div class="parameter">
              焦点 <span :class="{ 'active': focus == 'FOCUS_NEAR' }" @mousedown="changeSelectElevator('FOCUS_NEAR')"
                @mouseup="changeSelectElevator1('FOCUS_NEAR')">前移</span>
              <span :class="{ 'active': focus == 'FOCUS_FAR' }" @mousedown="changeSelectElevator('FOCUS_FAR')"
                @mouseup="changeSelectElevator1('FOCUS_FAR')">后移</span>
            </div>
            <div class="parameter">
              焦距 <span :class="{ 'active': focalDistance == 'ZOOM_IN' }" @mousedown="changeSelectElevator('ZOOM_IN')"
                @mouseup="changeSelectElevator1('ZOOM_IN')">变大</span>
              <span :class="{ 'active': focalDistance == 'ZOOM_OUT' }" @mousedown="changeSelectElevator('ZOOM_OUT')"
                @mouseup="changeSelectElevator1('ZOOM_OUT')">变小</span>
            </div>
          </div>
          <div class="mt-5">
            云台速度
            <el-slider v-model="speed" @change="sudu"></el-slider>
          </div>
          <div class="parameter1 mt-5">
            <span @click="takeScreenshot"><img src="../../../../assets/images/newMonitor/screenshot.png" alt="">
              视频截图</span>
            <!-- <span @click="startRecording" v-if="!pictureRecording"><img
                src="../../../../assets/images/newMonitor/startRecording.png" alt="">
              开始录像</span>
            <span @click="endRecording" v-if="pictureRecording" style="color:#f53f3f"><img
                src="../../../../assets/images/newMonitor/endRecording.png" alt="">
              停止录像</span> -->
          </div>
          <div class="mt-5" style="display: flex;justify-content: space-between;padding-bottom:20px;margin-top:16px;">
            <!-- <span>录像时长：{{ formattedTime }}</span>
            <span style="color: #1ffaff;" @click="downloadVideo">下载</span> -->
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import moment from 'moment'
export default {
  name: 'monitorExport',
  props: {
    title: {
      type: String,
      default: ''
    },
    factoryCode: {
      type: String,
      default: ''
    },
    videoDialgoShow: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      startTime: moment().format('YYYY-MM-DD 00:00:00'),
      endTime: moment().format('YYYY-MM-DD 23:59:59'),
      diaphragm: '',
      focus: '',
      focalDistance: '',
      speed: 50,
      elevatorSelectId: '',
      elevatorSelectName: '',
      elevatorOptions: [],
      videoUrl: '',
      videoName: '',
      pictureRecording: false,
      recordingStartTime: null,
      recordingTime: 0,
      taskID: '',
      intervalId: null,
      canvasElement: null,
      ctx: null,
    }
  },
  computed: {
    formattedTime() {
      const totalSeconds = this.recordingTime;
      const hours = Math.floor(totalSeconds / 3600);
      const minutes = Math.floor((totalSeconds % 3600) / 60);
      const seconds = totalSeconds % 60;
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    },
  },
  mounted() {
    this.routeData = this.$route.query
    console.log("factoryCode", this.factoryCode)
    this.getElevatorList(this.factoryCode, 'previewStreaming', {}, 'init')
    this.getRecordStatus()
  },
  methods: {
    // 下载
    async downloadVideo() {
      console.log('下载');
      // const videoUrl = 'https://example.com/path/to/video.mp4'; // 替换为实际视频地址
      // // 使用 fetch 获取视频数据
      // const response = await fetch(videoUrl);
      // const blob = await response.blob();

      // // 创建一个链接并下载
      // const url = window.URL.createObjectURL(blob);
      // const a = document.createElement('a');
      // a.href = url;
      // a.download = 'video.mp4'; // 设置下载的文件名
      // document.body.appendChild(a);
      // a.click();
      // a.remove();
      // window.URL.revokeObjectURL(url); // 释放内存

    },
    getRecordStatus() {
      this.$api.getRecordStatus(this.factoryCode, {}).then(res => {
        if (res.code === "200") {
          this.pictureRecording = res.data.recordStatus === 1
        }
      })
    },
    // 开始录像
    startRecording() {
      this.$api.startRecording(this.factoryCode, {}).then(res => {
        console.log(res, '开始录像');
        if (res.status === 200) {
          this.pictureRecording = true
          this.recordingStartTime = new Date().getTime();
          this.intervalId = setInterval(() => {
            this.recordingTime = Math.floor((new Date().getTime() - this.recordingStartTime) / 1000);
          }, 1000);
          let obj = {
            recordType: 6,
            type: 0
          }
        } else {
          this.$message.error(res.message)
        }
      })

      // this.getElevatorList(this.factoryCode, 'startRecording', obj, 'recording')
      // 单独使用新接口
    },
    // 停止录像
    endRecording() {
      this.$api.stopRecording(this.factoryCode, {}).then(res => {
        console.log(res, '停止录像');
        if (res.status === 200) {
          this.pictureRecording = false
          clearInterval(this.intervalId)
          let obj = {
            taskID: this.taskID,
            type: 0
          }
        } else {
          this.$message.error(res.message)
        }
      })

      // this.getElevatorList(this.factoryCode, 'stopRecording', obj, 'recording')
      // 单独使用新接口
    },
    // 视频截图
    downloadImage(picUrl) {
      const link = document.createElement('a');
      link.href = picUrl;
      link.download = 'image.jpg'; // 设置下载文件名
      link.target = '_blank'; // 在新页面或新标签页中打开
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    takeScreenshot() {
      const obj = {
        taskID: this.taskID,
        type: 0
      }
      this.getElevatorList(this.factoryCode, 'manualCapture', obj, 'takeScreenshot')
    },
    closeDialog() {
      this.$emit('closeVideoDialog')
    },
    // 初始化获取所有监控状态数据
    getElevatorList(factoryCode, operation, obj, params) {
      this.$api.getQueryInstanceFunction(factoryCode, operation, obj).then(res => {
        if (res.status === 200) {
          if (params == 'init') {
            this.elevatorOptions = res.result[0]
            if (res.result.length && res.result[0].url) {
              this.playVideoByElevatorId(this.elevatorOptions)
            } else {
              this.$message.error('暂无内容')
            }
          } else if (params == 'recording') {
            this.taskID = res.result[0].taskID
          } else if (params == 'takeScreenshot') {
            const picUrl = res.result[0].picUrl
            this.downloadImage(picUrl)
          } else if (params == 'cloudPlatform') {
          }
        } else {
          this.$message.error(res.message)
        }
      })
    },
    sudu(val) {
      this.speed = val
    },
    // 鼠标按下
    changeSelectElevator(val) {
      this.diaphragm = val
      this.focus = val
      this.focalDistance = val
      let obj = {
        action: "0",
        command: val,
        speed: this.speed
      }
      this.getElevatorList(this.factoryCode, 'PTZ', obj, 'cloudPlatform')
    },
    // 鼠标放开
    changeSelectElevator1(val) {
      let obj = {
        action: "1",
        command: val,
        speed: this.speed
      }
      this.getElevatorList(this.factoryCode, 'PTZ', obj, 'cloudPlatform')
    },
    // 语音对讲
    voiceIntercom() {
      let obj = {
        transmode: 1
      }
      this.getElevatorList(this.factoryCode, 'voiceIntercom', obj, 'init')
    },
    // 播放视频监控
    playVideoByElevatorId(selectData) {
      // console.log(selectData.url, 'urlllll');
      // // 找到 '?' 的索引位置
      // const questionIndex = selectData.url.indexOf('?');
      // console.log(questionIndex, 'questionIndexquestionIndex');
      // // 如果找到 '?'，则截取 '?' 前面的部分
      // if (questionIndex !== -1) {
      //   const baseUrl = selectData.url.slice(0, questionIndex);
      //   selectData.url = baseUrl
      //   console.log(baseUrl, 'baseUrl');
      // } else {
      //   console.log(selectData.url, 'selectData.url');
      // }
      console.log(selectData, 'selectData');

      this.videoUrl = selectData.url
      this.videoName = '监控'
    },
    // 回放
    search() {
      let obj = {
        protocol: 'rtsp',
        transmode: '1',
        beginTime: moment(this.startTime).toISOString(true),
        endTime: moment(this.endTime).toISOString(true)
      }
      this.getElevatorList(this.factoryCode, 'playbackStreaming', obj, 'init')
    },
    // 重置
    reset() {
      this.getElevatorList(this.factoryCode, 'previewStreaming', {}, 'init')
    },
  }
}
</script>
<style lang="scss" scoped>
.content_left {
  width: 100%;
  height: 100%;
  /* 设置父容器高度为视口高度 */
  display: flex;
  flex-direction: row;

  .left_content {
    width: 75%;
    height: auto;
    background: #F7F8FA;
  }

  .right_content {
    width: 25%;
    background: #121F3E;
    color: #fff;
    overflow-y: scroll;
    overflow-x: hidden;
    padding-bottom: 30px;

    .select_elevator {
      width: 93%;
      margin: 10px;
      height: 38px;
      cursor: pointer;
    }

    .searchForm {
      margin-top: 10px;
    }

    .mt-5 {
      margin-top: 5px;
    }

    .bg-img {
      width: 139px;
      height: 138px;
      background: center url("~@/assets/images/newMonitor/tripodHead.png") no-repeat;
      background-size: 100% 100%;
      margin: 0 auto;
      margin-top: 5px;
      position: relative;

      .bg-img-top {
        position: absolute;
        width: 18px;
        height: 18px;
      }

      .top1 {
        top: 16px;
        left: 60px;
      }

      .top2 {
        top: 32px;
        left: 34px;
      }

      .top3 {
        top: 32px;
        left: 88px;
      }

      .center1 {
        width: 30px;
        height: 30px;
        top: 53px;
        left: 55px;
      }

      .left1 {
        top: 58px;
        left: 19px;
      }

      .left2 {
        top: 87px;
        left: 34px;
      }

      .left3 {
        top: 101px;
        left: 61px;
      }

      .right1 {
        top: 58px;
        right: 19px;
      }

      .right2 {
        top: 87px;
        right: 34px;
      }
    }

    .parameter {
      margin: 10px 17px;

      span {
        display: inline-block;
        width: 60px;
        height: 30px;
        line-height: 27px;
        border: 1px solid;
        background: linear-gradient(180deg, #081F3B 0%, #021122 100%);
        border-image: linear-gradient(180deg, rgba(2, 12, 29, 1), rgba(22, 44, 76, 1)) 1 1;
        margin-left: 8px;
        text-align: center;
      }

      img {
        width: 16px;
        height: 16px;
      }

      span.active {
        background: linear-gradient(180deg, #105B79 0%, #032A3C 100%);
        border: 1px solid #1FFAFF;
      }
    }

    .parameter1 {
      display: flex;
      justify-content: space-between;

      span {
        display: inline-block;
        width: 100px;
        height: 30px;
        line-height: 27px;
        border: 1px solid;
        background: linear-gradient(180deg, #081F3B 0%, #021122 100%);
        border-image: linear-gradient(180deg, rgba(2, 12, 29, 1), rgba(22, 44, 76, 1)) 1 1;
        text-align: center;
      }

      img {
        width: 16px;
        height: 16px;
      }
    }
  }
}

::v-deep .el-slider__bar {
  background-color: #1ffaff;
}

::v-deep .el-slider__button {
  border: 2px solid #1ffaff
}

::v-deep .el-input__inner {
  background: #121F3E;
  border: 1px solid #193382;
  color: #fff;
  width: 235px;
}
</style>
