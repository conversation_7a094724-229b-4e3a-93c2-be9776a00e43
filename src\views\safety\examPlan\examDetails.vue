<template>
  <PageContainer v-loading="contentLoading">
    <div slot="header" class="table-content">
      <div class="topFilter">
        <div class="backBar">
          <span style="cursor: pointer" @click="$router.go(-1)">
            <i class="el-icon-arrow-left"></i>
            考试计划详情
          </span>
        </div>
      </div>
      <div class="baseInfo">
        <h1>基础信息</h1>
        <div class="contenter">
          <div class="itemInfo">
            <span class="title">试卷名称：</span>
            <span class="value">{{formInfo.name}}</span>
          </div>
          <div class="itemInfo">
            <span class="title">所属单位：</span>
            <span class="value">{{formInfo.deptName}}</span>
          </div>
          <div class="itemInfo">
            <span class="title">所属科目：</span>
            <span class="value">{{formInfo.subjectName}}</span>
          </div>
          <div class="itemInfo">
            <span class="title">考试期限：</span>
            <span class="value">{{formInfo.startTime}}至{{formInfo.endTime}}</span>
          </div>
          <div class="itemInfo">
            <span class="title">答题时长：</span>
            <span class="value">{{formInfo.duration||'-'}}分钟</span>
          </div>
          <div class="itemInfo">
            <span class="title">试卷总分：</span>
            <span class="value">{{formInfo.score||'-'}}分</span>
          </div>
          <div class="itemInfo">
            <span class="title">通过分数：</span>
            <span class="value">{{ formInfo.passScore ||'-'}}分</span>
          </div>
          <div class="itemInfo">
            <span class="title">考试不通过：</span>
            <span class="value">{{formInfo.rejectDeal=='1'?'不补考':formInfo.rejectDeal=='2'?'自动组卷补考':'手动派发补考'}}</span>
          </div>
          <!-- <div class="itemInfo">
            <span class="title">考试通过自动发放证书：</span>
            <span class="value">{{formInfo.credentialName||'-'}}</span>
          </div> -->
        </div>
      </div>
    </div>
    <div slot="content" class="courseContent">
      <h1>试题内容</h1>
      <div class="table_content">
        <div
          v-for="(k, ind) in formInfo.questions"
          :key="k.id"
          :name="k.id"
          :class="['exercisesItem', k.isExpand ? 'expand' : '']"
          :ref="'exercisesItem' + ind"
        >
          <div class="exercisesTop">
            <div class="left">
              <div class="exercisesType">
                {{
                  k.type == "1" ? "单选题" : k.type == "2" ? "多选题" : "判断题"
                }}
              </div>
              <span>得分：{{k.score}}分</span>
            </div>
            <div class="right">
              <span @click="isExpandBtn(k, )">{{
                k.isExpand ? "折叠" : "展开"
              }}</span>
            </div>
          </div>
          <div :class="['exercisesName', k.isExpand ? '' : 'title']">
            {{ k.topic }}
          </div>
          <el-radio-group
            v-if="k.type == '1'"
            class="radio"
            v-model="k.answer"
            disabled
          >
            <el-radio v-for="(j, index) in k.options" :key="index" :label="j.id"
              >{{ j.id }}. {{ j.label }}</el-radio
            >
          </el-radio-group>
          <el-checkbox-group
            v-if="k.type == '2'"
            v-model="k.answer"
            class="radio"
            disabled
          >
          <el-checkbox
            v-for="(j, index) in k.options"
            :key="index"
            :label="j.id"
            >{{ j.id }}. {{ j.label }}</el-checkbox
          >
        </el-checkbox-group>
          <p>答案：{{ k | getAnswer }}</p>
          <p>
            解析：
            {{ k.analysis }}
          </p>
        </div>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import moment from "moment";
export default {
  data() {
    return {
      contentLoading: false,
      routeInfo: "",
      moment,
      formInfo: {},
      id: "",
      drawerDialog: false,
      seeType: "public",
    };
  },
  created() {
    this.routeInfo = JSON.parse(sessionStorage.getItem("routeInfo"));
    this.id = this.$route.query.id;
    if (this.id) {
      this.getDetails();
    }
  },
  filters:{
    getAnswer(val){
      if(val.type=='3'){
        return val.answer=='1'?'正确':'错误'
      }else if(val.type=='2'){
        return val.answer.toString()
      }else{
        return val.answer
      }
    }
  },
  methods: {
    // 获取详情
    getDetails() {
      this.contentLoading = true;
      this.$api.examPlanDetails({id:this.id}).then(res=>{
        if(res.code=='200'){
          res.data.questions.forEach(i=>{
            i.options = JSON.parse(i.options)
            i.isExpand = false
            if(i.type=='2'){        
              i.answer = i.answer.split(',')
            }
          })
          this.formInfo=res.data
          console.log(this.formInfo,'this.formInfo');
        }else{
          this.$message.error(res.msg)
        }
      })
      this.contentLoading = false;
    },
    // 展开试题
    isExpandBtn(item, ) {
      item.isExpand = !item.isExpand;
    },
  },
};
</script>
<style lang="scss" scoped>
.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
  .baseInfo {
    padding: 0 24px 24px 24px;
    .contenter {
      padding-top: 24px;
      display: flex;
      font-size: 14px;
      flex-wrap: wrap;
      .itemInfo {
        width: 50%;
        margin-bottom: 16px;
        .title {
          color: #666;
          margin-right: 10px;
        }
      }
    }
  }
}
.topFilter {
  padding: 15px;
  height: 60px;
  background-color: #fff;
  .backBar {
    color: #121f3e;
    height: 30px;
    border-bottom: 1px solid #dcdfe6;
  }
}
.courseContent {
  height: 100%;
  margin-top: 16px;
  background-color: #fff;
  padding: 16px;
  .table_content {
    height: calc(100% - 70px);
    overflow: auto;
    .exercisesItem {
      height: 90px;
      overflow: hidden;
      background-color: #fff;
      margin-bottom: 16px;
      padding: 16px;
      border-bottom: 4px solid #faf9fc;
      font-size: 14px;
      .exercisesTop {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        .left {
          display: flex;
          align-items: center;
          justify-content: center;
          span {
            color: #7f848c;
          }
        }
        .right {
          color: #ccced3;
          display: flex;
          align-items: center;
          .line {
            width: 2px;
            height: 14px;
            margin: 0 10px 0 26px;
            background-color: #dcdfe6;
          }
          span {
            color: #3562db;
            margin-left: 16px;
            cursor: pointer;
          }
          i {
            color: #3562db;
            cursor: pointer;
            margin-left: 16px;
          }
        }
        .exercisesType {
          width: 58px;
          height: 22px;
          line-height: 22px;
          text-align: center;
          border-radius: 4px;
          color: #86909c;
          background-color: #ededf5;
          margin: 0 10px;
        }
      }
      .exercisesName {
        line-height: 20px;
        margin-bottom: 16px;
      }
      .title {
        overflow: hidden;
        -webkit-line-clamp: 2;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
      }
      .el-radio {
        margin-left: 38px;
        font-size: 14px;
        color: #7f848c !important;
        line-height: 30px;
      }
      p {
        font-size: 14px;
        color: #7f848c !important;
        line-height: 20px;
        margin-bottom: 16px;
      }
    }
    .expand {
      height: auto;
    }
  }
}
::v-deep .el-radio {
  display: block;
  margin: 10px 0;
}

::v-deep .el-checkbox {
  display: block;
  margin: 10px 0;
}
::v-deep .el-checkbox-group {
  margin:30px 0  0 38px;
}
</style>
