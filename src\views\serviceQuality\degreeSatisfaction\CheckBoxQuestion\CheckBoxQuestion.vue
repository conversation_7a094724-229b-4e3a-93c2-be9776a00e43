key
<template>
  <div class="main-container">
    <el-form ref="checkboxForm" :model="formItem" :rules="rules" label-width="100px">
      <div style="background-color: #fff;">
        <el-row>
          <el-col :span="1.5" class="table-label">
            <i class="is-require">*</i>
            <span>标题 :</span>&nbsp;
          </el-col>
          <el-col :span="21">
            <el-form-item prop="name" label-width="0">
              <el-input v-model="formItem.name" type="textarea" rows="1" style="width: 40%;"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <div style="display: flex; padding-right: 10px;">
          <div class="table-label">
            <span class="cgangeStyle">选项：</span>
          </div>
          <el-table :data="formItem.optionsData">
            <el-table-column label="操作" width="100" align="center">
              <template slot-scope="scope">
                <span style="cursor: pointer; padding: 0 5px; color: #3562db;" @click="addOption">新增</span>
                <span v-if="scope.$index != 0" style="cursor: pointer; color: red; padding: 0 5px;" @click.prevent="removeOption(scope.row)">删除</span>
              </template>
            </el-table-column>
            <el-table-column label="选项" align="center">
              <template slot-scope="scope">
                <el-input v-model.trim="scope.row.name" show-word-limit placeholder="请输入选项" :maxlength="50" :readonly="readonly"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="可输入" align="center" width="120px">
              <template slot-scope="scope">
                <el-checkbox v-model="scope.row.isInput" :true-label="isInputTrue" :false-label="isInputFalse">可输入</el-checkbox>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <!-- <el-row v-for="(option, index) in formItem.optionsData" :key="option.key">
        <el-col :span="3" class="table-label">
          <i class="is-require">*</i>
          <span>{{ '选项' + (index + 1) + ' :' }}</span>
        </el-col>
        <el-col :span="isSetSore ? 14 : 16">
          <el-form-item label-width="0" :prop="'optionsData.' + index + '.name'" :rules="rules.test">
            <el-input v-model="option.name"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="isSetSore ? 2 : 0">
          <el-form-item label-width="0">
            <el-input v-model="option.score" type="number" min="-1" @mousewheel.native.prevent></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="3" class="operator-container">
          <el-checkbox v-model="option.isInput" :true-label="isInputTrue" :false-label="isInputFalse">可输入</el-checkbox>
        </el-col>
        <el-col :span="1" class="operator-container">
          <div class="row-option add-option" @click="addOption"></div>
        </el-col>
        <el-col :span="1" class="operator-container">
          <div v-if="index !== 0" class="row-option remove-option" @click.prevent="removeOption(option)"></div>
        </el-col>
      </el-row> -->
      <el-row style="display: flex; margin-top: 10px; background-color: #fff; align-items: center; padding-left: 10px;">
        <!-- <el-col :span="3">
          <el-form-item label-width="0">
            <el-checkbox v-model="isSetSore" >设置分数</el-checkbox>
          </el-form-item>
        </el-col>-->
        <el-col :span="3">
          <el-form-item label-width="0" style="margin: 0;">
            <el-checkbox v-model="isSetMust">是否必填</el-checkbox>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="6">
          <el-form-item label="选项排序">
            <el-select v-model="formItem.rowCount" placeholder="请选择排序方式">
              <el-option label="横排" value="5"></el-option>
              <el-option label="竖排" value="1"></el-option>
              <el-option label="每行2个" value="2"></el-option>
              <el-option label="每行3个" value="3"></el-option>
              <el-option label="每行4个" value="4"></el-option>
            </el-select>
          </el-form-item>
        </el-col>-->
        <el-col :span="8">
          <el-form-item label="最少选择" style="margin: 0;">
            <!-- 最少选择 -->
            <el-select v-model="formItem.minSelect" placeholder="请选择" @change="minSelectChange">
              <el-option v-for="item in minSelectOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
            <!-- 最少选择结束 -->

            <!-- <el-input
              type="number"
              v-model="formItem.minSelect"
              @mousewheel.native.prevent
              min="-1"
            ></el-input>-->
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="最多选择" style="margin: 0;">
            <!-- 最多选择 -->
            <el-select v-model="formItem.maxSelect" placeholder="请选择">
              <el-option v-for="item in maxSelectOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
            <!-- 最多选择结束 -->

            <!-- <el-input type="number" @mousewheel.native.prevent min="1" v-model="formItem.maxSelect"></el-input> -->
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import notice from '../utils/notice'
import utils from '../utils/utils'
import checkCentralControl from '../check/checkCentralControl'
export default {
  props: {
    questionSubject: {
      type: Object
    }
  },
  data() {
    return {
      readonly: false,
      arr: [],
      isInputTrue: 1, // 默认选中
      isInputFalse: 0, // 默认选中
      maxSelectOptions: [], // 最大框选择
      minSelectOptions: [], // 最小框选择
      formItem: {
        name: '',
        optionsData: [
          { name: '', key: this.guid(), score: -1, isInput: 0, isDelete: 0 } // isDelete是否删除  0 否 1是; isInput 是否可输入 0否 1是
        ],
        isMust: 0, // 是否必填
        score: 0, // 是否设置分数  1是 0否
        rowCount: '1', // 排序方式 一行显示几个  1.竖排 2.一排2个 3.一排3个 4.一排4个 5.横排
        minSelect: '',
        maxSelect: ''
      },
      rules: checkCentralControl.getCheckConfig('checkBox') // 获取表单校验对象  (多选校验)
    }
  },
  computed: {
    isSetSore: {
      set(value) {
        this.formItem.score = value ? 1 : 0
        this.resetOptionScore()
      },
      get() {
        return this.formItem.score === 1
      }
    },
    isSetMust: {
      set(value) {
        this.formItem.isMust = value ? 1 : 0
      },
      get() {
        return this.formItem.isMust === 1
      }
    }
  },
  mounted() {
    if (this.questionSubject.id) {
      const formData = {
        ...this.questionSubject,
        rowCount: this.questionSubject.rowCount + ''
      }
      this.formItem = JSON.parse(JSON.stringify(formData))
    }
    // 添加copy数组
    this.arr = JSON.parse(JSON.stringify(this.formItem.optionsData))
    notice.$emit('initChildComponent', this, '多选题')
    notice.$on('handleSubmit', (component) => {
      if (this === component) {
        this.submitForm()
      }
    })
    for (var a = 0; a < this.formItem.optionsData.length; a++) {
      this.addMaxAndMinOption(this.formItem.optionsData.length != 1 ? this.formItem.minSelect : 1)
    }
    // this.minSelectChange()
  },
  methods: {
    guid() {
      return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1)
    },
    resetOptionScore() {
      this.formItem.optionsData.forEach((item) => {
        item.score = 0
      })
    },
    submitForm() {
      this.$refs['checkboxForm'].validate((valid) => {
        if (valid) {
          this.saveQuestionSubject()
        } else {
          return false
        }
      })
    },
    deleteArrKey(arrMain, arrFrom) {
      for (var a = 0; a < arrMain.length; a++) {
        for (var b = 0; b < arrFrom.length; b++) {
          if (arrMain[a].id == arrFrom[b].id || (arrMain[a].key && arrMain[a].key == arrFrom[b].key)) {
            arrFrom.splice(b, 1)
          }
        }
      }
    },

    saveQuestionSubject(callBack) {
      //
      this.deleteArrKey(this.formItem.optionsData, this.arr)
      let len = this.formItem.optionsData.filter((item) => item.name == '')
      if (len.length) {
        return this.$message.error('请检查选项是否填写完整')
      }
      // 合并数组
      var arrCopy = this.arr.concat(this.formItem.optionsData)
      const params = {
        id: this.questionSubject.id ? this.questionSubject.id : '',
        type: 'checkbox',
        name: this.formItem.name,
        isMust: this.formItem.isMust,
        score: this.formItem.score,
        rowCount: this.formItem.rowCount,
        pvqId: localStorage.getItem('questId'),
        options: JSON.stringify(arrCopy),
        minSelect: this.formItem.minSelect,
        maxSelect: this.formItem.maxSelect
      }
      if (params.id == '') {
        this.$api.saveQuestion(params).then((res) => {
          if (res.status == 200) {
            this.$message({
              message: res.message,
              type: 'success'
            })
            notice.$emit('closeQuestionDialog', this)
          }
        })
      } else {
        this.$api.updateQuestion(params).then((res) => {
          if (res.status == 200) {
            this.$message({
              message: res.message,
              type: 'success'
            })
            notice.$emit('closeQuestionDialog', this)
          }
        })
      }
    },
    duplicateRemoval(arr) {
      for (let index = 0; index < arr.length; index++) {
        const element = arr[index]
        for (var a = 0; a < arr.length; a++) {
          if (element.id && element.id == arr[a].id && index != a) {
            if (element.name == '') {
              arr.splice(index, 1)
              index--
              a--
            } else {
              arr.splice(a, 1)
              a--
            }
          }
        }
      }
    },
    // 删除存储数组
    arrCopyCheck(item) {
      for (var a = 0; a < this.arr.length; a++) {
        if (this.arr[a].id === item.id) {
          this.arr[a].isDelete = 1
        }
      }
    },
    // 删除数组中的项
    deleteArr(item, arrFlag, itemFlag) {
      for (var a = 0; a < this.formItem.optionsData.length; a++) {
        if (this.formItem.optionsData[a][arrFlag] === item[itemFlag]) {
          this.formItem.optionsData.splice(a, 1)
        }
      }
    },
    // 一级选择切换
    minSelectChange() {
      var count = this.formItem.minSelect
      this.maxSelectOptions = this.minSelectOptions.slice(count - 1, this.minSelectOptions.length)
      this.formItem.maxSelect = this.minSelectOptions.length
    },
    // 删除选项成功之后重置表单数据
    resetOptionData(data) {
      this.formItem = { ...data, rowCount: this.questionSubject.rowCount + '' }
    },
    removeOption(item) {
      // 编辑时删除选项
      if (this.questionSubject.id) {
        if (item.id) {
          this.arrCopyCheck(item)
          this.deleteArr(item, 'id', 'id')
        } else {
          this.deleteArr(item, 'key', 'key')
        }
      } else {
        // 新建时删除选项
        var index = this.formItem.optionsData.indexOf(item)
        if (index !== -1) {
          this.formItem.optionsData.splice(index, 1)
        }
      }
      this.deleteMaxAndMinOption()
    },
    // 删除多选框中的数据
    deleteMaxAndMinOption() {
      this.minSelectOptions.pop()
      var maxSelectOptions = JSON.parse(JSON.stringify(this.minSelectOptions))
      this.maxSelectOptions = maxSelectOptions // 最大框选择
      this.formItem.minSelect = 1
      var count = this.maxSelectOptions.length
      this.formItem.maxSelect = count
    },
    // 给下拉节点中赋值
    addMaxAndMinOption(size = 1) {
      // 取当前值
      var count = this.minSelectOptions.length + 1
      var obj_min = {
        value: count,
        label: count
      }
      this.minSelectOptions.push(obj_min)
      var maxSelectOptions = JSON.parse(JSON.stringify(this.minSelectOptions))
      this.maxSelectOptions = maxSelectOptions // 最大框选择
      this.formItem.minSelect = size
      this.formItem.maxSelect = count
    },
    addOption() {
      this.formItem.optionsData.push({
        name: '',
        key: this.guid(),
        score: this.isSetSore ? 0 : -1,
        isInput: 0,
        isDelete: 0
      })
      this.addMaxAndMinOption()
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
.main-container {
  width: 100%;

  textarea {
    resize: none;
  }

  .el-input__inner {
    width: 100%;
  }

  .el-dialog__body {
    padding-bottom: 20px;
  }

  .table-label {
    text-align: left;
    box-sizing: border-box;
    padding-left: 15px;
    margin-top: 10px;
  }

  .is-require {
    position: absolute;
    top: 0;
    left: 0%;
    color: red;
    padding-top: 12px;
  }

  .operator-container {
    text-align: center;
    line-height: 40px;

    .row-option {
      cursor: pointer;
      display: inline-block;
      width: 16px;
      height: 16px;
      vertical-align: middle;
      background-size: 100% 100%;
    }

    .add-option {
      background-image: url(../../../../assets/images/ic-add.png);
    }

    .remove-option {
      background-image: url(../../../../assets/images/<EMAIL>);
    }
  }

  div .el-input__inner {
    padding: 0 0 0 10px;
  }
}

input {
  width: 200px;
}
</style>
