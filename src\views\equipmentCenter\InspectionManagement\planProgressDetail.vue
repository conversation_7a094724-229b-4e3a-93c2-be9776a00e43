<template>
  <div class="contentMain">
    <div class="topFilter">
      <div class="backBar">
        <span style="cursor: pointer" @click="$router.go(-1)">
          <i class="el-icon-arrow-left"></i>
          {{ title }}
        </span>
      </div>
    </div>
    <div class="taskDetailContent">
      <div class="taskDetail">
        <!-- 基本信息 -->
        <div class="baseInfo">
          <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
            <span class="green_line"></span>
            基本信息
          </div>
          <el-form ref="form" :model="row" label-width="90px">
            <el-row :gutter="60">
              <el-col :span="8">
                <el-form-item label="任务名称:">
                  <el-tooltip :content="row.taskName" placement="top">
                    <span>{{ row.taskName }}</span>
                  </el-tooltip>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="周期类型:">
                  <span>{{ cycleTypeFn(row) }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="systemType == '2' ? '应保养日期:' : '应巡日期:'">
                  <el-date-picker v-if="activeType == 'edit'" v-model="form.taskStartDate" type="date" placeholder="选择日期"> </el-date-picker>
                  <span v-else>{{ moment(row.taskStartTime).format('YYYY-MM-DD') }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="systemType == '2' ? '应保养时间:' : '应巡时间:'">
                  <el-time-picker
                    v-if="activeType == 'edit'"
                    v-model="form.timerange"
                    is-range
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    placeholder="选择时间范围"
                  >
                  </el-time-picker>
                  <template v-else>
                    <el-tooltip :content="moment(row.taskStartTime).format('YYYY-MM-DD HH:mm:ss') + '-' + moment(row.taskEndTime).format('YYYY-MM-DD HH:mm:ss')" placement="top">
                      <span>{{ moment(row.taskStartTime).format('YYYY-MM-DD HH:mm:ss') + '-' + moment(row.taskEndTime).format('YYYY-MM-DD HH:mm:ss') }}</span>
                    </el-tooltip>
                  </template>
                </el-form-item>
              </el-col>
              <el-col v-if="row.cycleType != '6'" :span="8">
                <el-form-item label="完成期限:">
                  <span>{{ row.finalTime ? row.finalTime + '天' : '' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="systemType == '2' ? '保养部门:' : '巡检部门:'">
                  <el-tooltip :content="row.distributionTeamName" placement="top">
                    <span>{{ row.distributionTeamName }}</span>
                  </el-tooltip>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="systemType == '2' ? '保养人员:' : '巡检人员:'">
                  <el-select v-if="activeType == 'edit'" v-model="form.planPersonCode" multiple :multiple-limit="20" placeholder="请选择人员">
                    <el-option v-for="item in personList" :key="item.id" :label="item.staffName" :value="item.id"> </el-option>
                  </el-select>
                  <span v-else>{{ row.planPersonName }}</span>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <!-- 任务点 -->
        <div class="taskPoint">
          <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
            <span class="green_line"></span>
            {{ systemType == '2' ? '保养点' : '巡检点' }}
          </div>
          <el-table :key="itemKey" :data="pointList" style="width: 60%">
            <el-table-column label="序号" type="index" width="80"> </el-table-column>
            <el-table-column prop="inspectionPointName" :label="systemType == '2' ? '保养点名称' : '巡检点名称'" show-overflow-tooltip> </el-table-column>
            <el-table-column v-if="activeType == 'edit'" label="设备点类型" show-overflow-tooltip>
              <template slot-scope="scope">
                <span>{{ scope.row.inspectionPointType == '1' ? '空间' : scope.row.inspectionPointType == '2' ? '设备' : '自定义' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100" show-overflow-tooltip>
              <template slot-scope="scope">
                <div v-if="activeType == 'edit'">
                  <el-link :disabled="pointList.length == 1" type="danger" @click="deleteRow(scope.row)">删除</el-link>
                  <el-link type="primary" :disabled="activeType == 'detail' || pointList.length == 1 || scope.row.sort == pointList.length" @click="pointMove(scope.row, 'down')">
                    下移
                  </el-link>
                  <el-link type="primary" :disabled="activeType == 'detail' || pointList.length == 1 || scope.row.sort == 1" @click="pointMove(scope.row, 'up')"> 上移 </el-link>
                </div>
                <el-link v-else type="primary" @click="detail(scope.row)">详情</el-link>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <div class="bottomBar">
      <div class="bottomWrap">
        <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db" @click="$router.go(-1)">取消</el-button>
        <el-button v-if="activeType == 'edit'" type="primary" @click="addTaskDetail">保存</el-button>
      </div>
    </div>
  </div>
</template>
<script>
import moment from 'moment'
export default {
  name: 'planProgressDetail',
  beforeRouteEnter(to, from, next) {
    if (to.meta.title) {
      to.meta.title = to.query.type == 'edit' ? '任务编辑' : '任务详情'
    }
    next()
  },
  async beforeRouteLeave(to, from, next) {
    if (!['progressDetail', 'InspectionPointDetail', 'taskManagement'].includes(to.name)) {
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      systemType: '',
      title: '',
      moment,
      itemKey: '',
      activeType: '',
      taskId: '',
      row: {},
      // 周期类型
      cycleTypeList: [
        {
          cycleType: 8,
          label: '单次'
        },
        {
          cycleType: 6,
          label: '每日'
        },
        {
          cycleType: 0,
          label: '每周'
        },
        {
          cycleType: 2,
          label: '每月'
        },
        {
          cycleType: 3,
          label: '季度'
        },
        {
          cycleType: 5,
          label: '全年'
        },
        {
          cycleType: 7,
          label: '自定义'
        }
      ],
      // 巡检点列表
      pointList: [],
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      personList: [],
      form: {
        id: '',
        maintainPlanRegions: '',
        taskStartDate: '',
        taskStartTime: '',
        taskEndTime: '',
        timerange: [],
        planPersonCode: '',
        planPersonName: ''
      }
    }
  },
  created() {
    if (!this.$store.state.keepAlive.list.includes('planManagement')) {
      this.initEvent()
    }
  },
  activated() {
    this.initEvent()
  },
  methods: {
    initEvent() {
      Object.assign(this.$data, this.$options.data())
      if (this.$route.path.indexOf('/InspectionManagement') != -1) {
        this.systemType = '1'
      } else if (this.$route.path.indexOf('/MaintenanceManagement') != -1) {
        this.systemType = '2'
      } else if (this.$route.path.indexOf('/comInspectionManagement') != -1) {
        this.systemType = '3'
      } else if (this.$route.path.indexOf('/hazardousChemicalGoods_inspection') != -1) {
        this.systemType = '4'
      } else if (this.$route.path.indexOf('/vp_planManagement') != -1) {
        this.systemType = '6'
      }
      this.activeType = this.$route.query.type
      this.taskId = this.$route.query.taskId
      this.row = this.$route.query.row.id ? this.$route.query.row : JSON.parse(sessionStorage.getItem('row'))
      this.getInspectionPointList()
      if (this.activeType == 'edit') {
        this.title = '任务编辑'
        this.form.taskStartDate = moment(this.row.taskStartTime)
        this.form.taskEndTime = this.row.taskEndTime
        this.getLersonnelList(this.row.distributionTeamId)
        this.form.timerange = [moment(this.row.taskStartTime), moment(this.row.taskEndTime)]
        this.form.planPersonCode = this.row.planPersonCode ? this.row.planPersonCode.split(',') : ''
        this.form.planPersonName = this.form.planPersonName
        this.form.taskStartTime = this.row.taskStartTime
      } else {
        this.title = '任务详情'
      }
    },
    // 周期类型
    cycleTypeFn(row) {
      const item = this.cycleTypeList.filter((i) => i.cycleType == row.cycleType)
      return item[0].label
    },
    // 人员列表
    getLersonnelList(id) {
      let params = {
        current: 1,
        size: 999,
        sex: '',
        pmId: '',
        officeId: id,
        postId: '',
        stationStatus: ''
      }
      this.$api.staffList(params).then((res) => {
        if (res.code == 200) {
          this.personList = res.data.records
        }
      })
    },
    // 任务点列表
    getInspectionPointList() {
      const data = {
        taskId: this.taskId,
        pageNo: this.paginationData.currentPage,
        pageSize: this.paginationData.pageSize
      }
      this.$api.getInspectionPointList(data).then((res) => {
        if (res.code == '200') {
          if (res.code == '200') {
            const listArr = []
            res.data.list.forEach((i) => {
              let items = JSON.parse(i.particulars)
              items.pointId = i.id
              listArr.push(items)
            })
            listArr.sort((a, b) => a.sort - b.sort)
            this.pointList = listArr
          }
        }
      })
    },
    // 巡检点详情
    detail(row) {
      this.$router.push({
        path: 'InspectionPointDetail',
        query: {
          row: {
            pointId: row.pointId,
            inspectionPointName: row.inspectionPointName
          }
        }
      })
    },
    // 巡检点移动
    pointMove(row, type) {
      const indexNum = this.pointList.map((item) => item.id).indexOf(row.id)
      if (type == 'down') {
        row.sort = row.sort + 1
        this.pointList[indexNum + 1].sort = row.sort - 1
      } else {
        row.sort = row.sort - 1
        this.pointList[indexNum - 1].sort = row.sort + 1
      }
      this.pointList = this.pointList.sort((a, b) => a.sort - b.sort)
      this.itemKey = Math.random()
    },
    deleteRow(row) {
      this.$confirm('删除后将无法恢复，是否确定删除？', '信息提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(() => {
          this.$api
            .deleteInspectionPoint({
              id: row.pointId,
              planCount: this.pointList.filter((i) => i.id != row.id).length,
              taskId: this.taskId
            })
            .then((res) => {
              if (res.code == '200') {
                if (this.systemType) {
                  this.$message({
                    type: 'success',
                    message: '保养点删除成功'
                  })
                } else {
                  this.$message({
                    type: 'success',
                    message: '巡检点删除成功'
                  })
                }
                let dataList = this.pointList.filter((i) => i.id != row.id)
                if (dataList.length > 0) {
                  dataList.forEach((i, index) => {
                    i.sort = index + 1
                  })
                }
                this.pointList = dataList
                this.itemKey = Math.random()
              }
            })
        })
        .catch(() => {})
    },
    // 任务编辑
    addTaskDetail() {
      const personNames = []
      this.personList.find((i) => {
        this.form.planPersonCode.forEach((j) => {
          if (i.id == j) {
            personNames.push(i.staffName)
          }
        })
      })
      const maintainPlanRegions = []
      this.pointList.forEach((i) => {
        const item = {
          id: i.pointId,
          particulars: i
        }
        maintainPlanRegions.push(item)
      })
      const params = {
        id: this.taskId,
        updateCode: this.$store.state.user.userInfo.userId,
        updateName: this.$store.state.user.userInfo.username,
        maintainPlanRegions: JSON.stringify(maintainPlanRegions),
        taskStartTime: moment(this.form.taskStartDate).format('YYYY-MM-DD') + ' ' + moment(this.form.timerange[0]).format('HH:mm:ss'),
        taskEndTime: moment(this.form.taskEndTime).format('YYYY-MM-DD') + ' ' + moment(this.form.timerange[1]).format('HH:mm:ss'),
        planPersonCode: this.form.planPersonCode.join(','),
        planPersonName: personNames.join(',')
      }
      this.$api.taskEdit(params).then((res) => {
        if (res.code == '200') {
          this.$message({
            type: 'success',
            message: '保存成功'
          })
          this.$router.go('-1')
        } else {
          this.$message({
            type: 'error',
            message: res.message || '保存失败'
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.contentMain {
  height: 100%;
  .topFilter {
    margin: 15px 15px 0;
    padding: 15px;
    width: calc(100% - 30px);
    height: 70px;
    background-color: #fff;
    .backBar {
      color: #121f3e;
      height: 30px;
      border-bottom: 1px solid #dcdfe6;
    }
  }
  .taskDetailContent {
    margin: 0 15px 15px;
    height: calc(100% - 152px);
    .taskDetail {
      overflow-y: scroll;
      overflow-x: hidden;
      padding: 0 15px 15px;
      width: 100%;
      height: 100%;
      background-color: #fff;
      .baseInfo {
        .el-col-8 {
          height: 45px;
        }
        :deep(.el-form-item) {
          .el-form-item__label {
            color: #414653;
          }
          .el-form-item__content {
            color: #121f3e;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
        :deep(.el-form-item__content) {
          width: 50%;
          .el-date-editor {
            width: 100%;
          }
        }
      }
    }
  }
  .bottomBar {
    height: 52px;
    width: 100%;
    background-color: #fff;
    display: flex;
    justify-content: right;
    align-items: center;
    .bottomWrap {
      padding: 0 16px;
    }
  }
}
</style>
