<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="addDept-content-title" @click="goBack">
        <i class="el-icon-arrow-left"></i><span style="margin-left: 10px">{{ $route.query.staffId ? '编辑成员' : '新增成员' }}</span>
      </div>
      <div class="content_box">
        <div class="top-title">基础信息</div>
        <el-form ref="formInline" :model="formInline" :inline="true" class="form-inline" :rules="rules" label-width="100px">
          <el-form-item label="姓名" prop="staffName">
            <el-input v-model.trim="formInline.staffName" placeholder="请输入姓名"></el-input>
          </el-form-item>
          <el-form-item label="身份证号" prop="idCard" class="ml-14">
            <el-input v-model="formInline.idCard" placeholder="请输入身份证号" maxlength="18"> </el-input>
          </el-form-item>
          <el-form-item label="出生日期" prop="birthDate" class="ml-14">
            <el-date-picker v-model="formInline.birthDate" type="date" value-format="yyyy-MM-dd" placeholder="选择日期" clearable> </el-date-picker>
          </el-form-item>
          <br />
          <el-form-item label="籍贯" prop="nativePlace">
            <el-input v-model="formInline.nativePlace" placeholder="请输入籍贯"> </el-input>
          </el-form-item>
          <el-form-item label="民族" prop="nation" class="ml-14">
            <el-select v-model="formInline.nation" placeholder="请选择民族" clearable filterable>
              <el-option v-for="item in nationList" :key="item.code" :label="item.nation" :value="item.code"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="性别" prop="sex" class="ml-14">
            <el-select v-model="formInline.sex" placeholder="请选择性别" clearable>
              <el-option v-for="item in sexList" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <br />
          <el-form-item label="手机号码" prop="mobile">
            <el-input v-model="formInline.mobile" placeholder="请输入手机号码" show-word-limit maxlength="11"> </el-input>
            <el-tooltip popper-class="tps" effect="dark" content="手机号将作为登录凭证" placement="top" style="margin-left: 2px">
              <span style="margin: auto"> <i class="el-icon-warning-outline"></i></span>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="办公电话" prop="phone">
            <el-input v-model="formInline.phone" placeholder="请输入办公电话" oninput="value=value.replace(/\D/g, '')" show-word-limit> </el-input>
          </el-form-item>
          <el-form-item label="职工工号" prop="staffNumber" class="ml-14">
            <el-input v-model="formInline.staffNumber" placeholder="请输入职工工号" show-word-limit> </el-input>
          </el-form-item>
          <br />
          <el-form-item label="文化程度" prop="educationId">
            <el-select v-model="formInline.educationId" placeholder="请选择文化程度" clearable @change="educationChange">
              <el-option v-for="item in educationalLevelList" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <br />
          <el-form-item label="员工照片" prop="avatar" style="width: 31%">
            <upload :fileList="photoList" modelName="hospitalBaseInfo" @input="uploadChangePhoto" @uploadRemove="uploadPhotoRemove"></upload>
          </el-form-item>
          <div class="top-title">工作信息</div>
          <el-form-item label="入职时间" prop="entryData">
            <el-date-picker v-model="formInline.entryData" type="date" value-format="yyyy-MM-dd" clearable placeholder="选择日期"> </el-date-picker>
          </el-form-item>
          <el-form-item label="所属科室" prop="officeName" class="ml-14">
            <el-input v-model.trim="formInline.officeName" placeholder="请选择所属科室" readonly clearable suffix-icon="el-icon-arrow-down" @focus="control"></el-input>
          </el-form-item>
          <el-form-item label="所属单位" prop="pmName" class="ml-14">
            <el-input v-model.trim="formInline.pmName" placeholder="请选择所所属单位" disabled suffix-icon="el-icon-arrow-down"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <deptDialog v-if="isDeptDialog" :visible.sync="isDeptDialog" :title="dialogTitle" :nature="'2'" :isNotmultiSector="true" @selectDept="selectDept" />
    </div>
    <div slot="footer">
      <el-button type="primary" @click="submitForm()">保存</el-button>
      <el-button type="primary" plain @click="$router.go(-1)">取消</el-button>
    </div>
  </PageContainer>
</template>
<script>
import moment from 'moment'
import deptDialog from '../components/deptDialog.vue'
import upload from '../components/upload.vue'
import { validateMobile, validatePhone, validateIdCard } from '@/assets/common/validate'
const validateMobileNum = (rule, value, callback) => {
  if (!validateMobile(value)) {
    callback(new Error('请输入正确的手机号'))
  } else {
    callback()
  }
}
const validateIdCardNum = (rule, value, callback) => {
  if (!validateIdCard(value)) {
    callback(new Error('请输入正确的身份证号'))
  } else {
    callback()
  }
}
const validatePhoneNum = (rule, value, callback) => {
  if (validatePhone(value) || validateMobile(value)) {
    callback()
  } else {
    callback(new Error('请输入正确的电话号码'))
  }
}
export default {
  name: 'addMember',
  components: {
    upload,
    deptDialog
  },
  async beforeRouteLeave(to, from, next) {
    if (!['memberInfoManage'].includes(to.name)) {
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      moment,
      formInline: {
        // ------------------------基础信息
        staffName: '',
        idCard: '',
        birthDate: '',
        nativePlace: '',
        nation: null,
        sex: '',
        mobile: '',
        phone: '',
        staffNumber: '',
        photoId: '',
        avatar: '',
        signUrl: '', // 签名
        sealUrl: '', // 印章
        education: '', // 学历名称
        educationId: '', //学历
        entryData: '',
        pmId: '',
        pmName: '',
        officeId: '',
        officeName: ''
      },
      dialogTitle: '选择科室',
      rules: {
        staffName: {
          required: true,
          message: '请输入人员姓名',
          trigger: 'change'
        },
        idCard: [
          { validator: validateIdCardNum, trigger: 'blur' },
          { required: false, message: '请输入正确的身份证号', trigger: 'blur' }
        ],
        mobile: [
          { validator: validateMobileNum, trigger: 'blur' },
          { required: true, message: '请输入手机号码', trigger: 'blur' }
        ],
        phone: [
          // { validator: validatePhoneNum, trigger: "blur" },
          // { required: false, message: "请输入手机号码", trigger: "blur" },
        ]
      },
      nationList: [],
      sexList: [
        { id: 1, name: '男' },
        { id: 2, name: '女' }
      ],
      postList: [],
      jobList: [],
      photoList: [], // 照片
      isDeptDialog: false, //科室选择
      educationalLevelList: [] // 文化程度
    }
  },
  mounted() {
    this.init()
    if (this.$route.query.staffId) {
      this.getStaffDetailFn(this.$route.query.staffId)
    }
  },
  methods: {
    init() {
      this.getNationListFn()
      this.getDictListFn()
    },
    // 民族列表
    getNationListFn() {
      this.$api.getNationList({}).then((res) => {
        if (res.code == 200) {
          this.nationList = res.data
          this.nationList.forEach((item) => {
            item.code = String(item.code)
          })
        }
      })
    },
    getDictListFn() {
      // 文化程度
      this.$api.supplierAssess
        .getDictData({
          dictionaryCategoryId: 'EDUCATION_LEVEL_CATEGORY'
        })
        .then((res) => {
          if (res.code == 200) {
            this.educationalLevelList = res.data[0].children
          }
        })
    },
    goBack() {
      this.$router.go(-1)
    },
    getStaffDetailFn(id) {
      this.$api.getStaffDetail({ id: id }).then((res) => {
        if (res.code == 200) {
          this.formInline = res.data
          this.formInline.role = Number(res.data.role)
          res.data.avatar
            ? (this.photoList = [
                {
                  name: '',
                  url: this.$tools.imgUrlTranslation(res.data.avatar)
                }
              ])
            : (this.photoList = [])
        }
      })
    },
    uploadChangePhoto(val, state) {
      if (state) {
        this.formInline.photoId = ''
        this.formInline.avatar = ''
      } else {
        this.formInline.photoId = val.uid
        this.formInline.avatar = val.fileHost + val.fileUrl
      }
    },
    uploadPhotoRemove() {
      this.photoList = []
      this.formInline.photoId = ''
      this.formInline.avatar = ''
    },
    // 科室弹窗
    control() {
      this.isDeptDialog = true
    },
    // 选择科室
    selectDept(list) {
      if (list && list.length) {
        this.formInline.pmId = list[0].umId
        this.formInline.pmName = list[0].umName
        this.formInline.officeId = list.map((item) => item.id).join(',')
        this.formInline.officeName = list.map((item) => item.deptName).join(',')
      }
    },
    submitForm() {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          const staffId = this.$route.query.staffId
          const userInfo = this.$store.state.user.userInfo.user
          if (staffId) {
            let data = {
              ...this.formInline,
              id: this.$route.query.staffId,
              operatorName: userInfo.staffName
            }
            data.nation = Number(data.nation)
            this.$api.supplierAssess
              .updateHospitalStaffList(data)
              .then((res) => {
                if (res.code == 200) {
                  this.$message({
                    type: 'success',
                    message: '保存成功'
                  })
                  setTimeout(() => {
                    this.$router.go(-1)
                  }, 500)
                } else {
                  this.$message({
                    type: 'error',
                    message: res.message || '保存失败'
                  })
                }
              })
              .catch(() => {})
          } else {
            const userInfo = this.$store.state.user.userInfo.user
            let data = {
              ...this.formInline,
              operatorName: userInfo.staffName
            }
            data.nation = Number(data.nation)
            this.$api.supplierAssess
              .insertHospitalStaffList(data)
              .then((res) => {
                if (res.code == 200) {
                  this.$message({
                    type: 'success',
                    message: '保存成功'
                  })
                  setTimeout(() => {
                    this.$router.go(-1)
                  }, 500)
                } else {
                  this.$message({
                    type: 'error',
                    message: res.msg || '保存失败'
                  })
                }
              })
              .catch(() => {})
          }
        }
      })
    },
    uploadSighRemove() {
      this.signList = []
      this.formInline.signUrl = ''
    },
    // 文化程度改变
    educationChange(e) {
      this.formInline.education = this.educationalLevelList.find((ele) => ele.id == e).name
    },
    cancel() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.container-content > div {
  background-color: #fff;
  height: 100%;
  position: relative;
  .addDept-content-title {
    border-bottom: 1px solid #e4e7ed;
    padding: 13px 24px;
    cursor: pointer;
    font-weight: 500;
    color: #333333;
  }
  .content_box {
    padding: 16px 24px;
    background: #fff;
    display: flex;
    flex-direction: column;
    height: calc(100% - 50px);
    overflow-y: scroll;
    .top-title {
      font-weight: bold;
      font-size: 16px;
      color: #333333;
    }
  }
  .form-inline {
    margin-top: 24px;
    .el-input,
    .el-select,
    .el-cascader {
      width: 361px;
    }
  }
  .el-form-item {
    margin-right: 20px;
  }
}
.ml-14 {
  margin-left: 14px;
}
::v-deep .el-cascader {
  height: 32px !important;
}
::v-deep .el-input__inner {
  height: 32px !important;
}
::v-deep .el-cascader__search-input {
  margin: 1px 0 0px 15px !important;
}
::v-deep .el-cascader__tags {
  top: 60% !important;
}
</style>
