<template>
  <PageContainer :footer="true" v-loading="blockLoading">
    <div slot="content" class="table-content">
      <div class="content_box">
        <h1>基础信息</h1>
        <el-form ref="formInline" :model="formInline" class="form-inline" label-width="120px" :rules="rules">
          <el-form-item label="培训计划名称:" prop="name">
            <el-input v-model.trim="formInline.name" placeholder="请输入培训计划名称，最多输入30个字" maxlength="30" show-word-limit
              style="width: 360px;"></el-input>
          </el-form-item>
          <el-form-item label="所属科目:" prop="templateName">
            <el-cascader ref="allParent" v-model="formInline.subjectId" style="width: 360px;" :options="subjectData"
              :props="subjectDataType" placeholder="请选择科目" @change="handleChange"></el-cascader>
          </el-form-item>
          <br />
          <el-form-item label="所属单位:" prop="templateName">
            <el-cascader v-model="formInline.deptIds" placeholder="请选择部门" :options="deptList" :props="deptTree"
              :show-all-levels="false" clearable filterable collapse-tags style="width: 360px;">
            </el-cascader>
          </el-form-item>
          <el-form-item>
            <span slot="label"> <span style="color: red;">*</span> 培训老师: </span>
            <template>
              <div @click="addPersonShow('teacher')">
                <el-input v-model="teacherNames" placeholder="请选择培训老师" style="width:360px"> </el-input>
              </div>
            </template>
          </el-form-item>
          <br />
          <el-form-item label="培训时间:" prop="timeLine">
            <el-date-picker v-model="formInline.timeLine" type="datetimerange" range-separator="至" start-placeholder="开始日期"
              end-placeholder="结束日期" style="width: 360px;" value-format="yyyy-MM-dd HH:mm:ss" @change = "dataPink">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="培训方式:" prop="type">
            <el-select v-model="formInline.type" placeholder="请选择培训方式" style="width: 360px;" >
              <el-option v-for="item in trainList" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <br />
          <el-form-item label="培训地址:" prop="templateName">
            <el-input v-model.trim="formInline.address" placeholder="请输入培训地址" maxlength="30" show-word-limit
              style="width: 360px;"></el-input>
          </el-form-item>
          <el-form-item label="到会培训人员:" prop="templateName">
            <template>
              <div @click="addPersonShow('user')">
                <el-input v-model="trainUserNames" placeholder="请选择人员" style="width:360px"> </el-input>
              </div>
            </template>
          </el-form-item>
          <br />

          <el-form-item label="分配方式" prop="stuOrOrg">
            <el-select v-model="formInline.stuOrOrg" placeholder="请选择分配方式" style="width: 360px" @change="btnType">
              <el-option v-for="item in stuOrOrgList" :key="item.id" :label="item.label" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="考试学员" class="" v-if="formInline.stuOrOrg == '0'">
            <span style="color: red; margin-left: -80px">*</span>
            <div class="set_select_width" @click="showUserDialog('xueyuan')">
              <template v-if="userNewList.length">
                <span v-for="(item, index) in userNewList" :key="index">
                  {{ item }}
                  <i class="el-icon-error" @click.stop="deleteTag(index, 'user')"></i>
                </span>
              </template>
              <p v-else>请选择人员</p>
            </div>
          </el-form-item>
          <el-form-item  prop="orgId" v-if="formInline.stuOrOrg == '1'">
            <span slot="label"> <span style="color: red;">*</span> 组织 </span>
            <el-select v-model="formInline.orgId" placeholder="请选择组织" style="width: 360px" multiple>
              <el-option v-for="item in deptAllList" :key="item.id" :label="item.teamName" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <br />
          <el-form-item label="培训记录上传" prop="fileEcho">
            <el-upload 
              ref="uploadFile" 
              drag 
              multiple 
              class="mterial_file" 
              action="string" 
              :file-list="formInline.fileEcho"
              :http-request="httpRequest"
              accept="" 
              :limit="30"
              :on-exceed="handleExceed" 
              :before-upload="beforeAvatarUpload" 
              :on-remove="handleRemove"
              :on-change="fileChange"
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text" style="top: 100px">
                将文件拖到此处，或
                <em>点击上传</em>
              </div>
            </el-upload>
          </el-form-item>
          <br />
          <el-form-item label="培训备注：" prop="describe">
            <el-input v-model.trim="formInline.trainRemark" type="textarea" :autosize="{ minRows: 2, maxRows: 4 }"
              placeholder="请输入培训描述" maxlength="200" show-word-limit style="width: 840px;"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <tasksDialog ref="userDialogRef" :opty="opty" :peopleDialog="peopleDialog" @closeDialog="closeDialog"
        @sureDialogUser="sureDialogUser"></tasksDialog>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go('-1')">取消</el-button>
      <el-button type="primary" @click="complete">确认</el-button>
    </div>
  </PageContainer>
</template>
<script>
import axios from 'axios'
import tasksDialog from '../trainingTasks/components/tasksDialog.vue';
export default {
  name: "addLocationPoint",
  components: { tasksDialog },
  data() {
    return {
      opty: '', // 学员弹窗状态
      subjectData: [], // 学科科目分类
      subjectDataType: {
        children: 'childList',
        label: 'name',
        value: 'id',
        checkStrictly: true,
        emitPath: false
      },
      deptList: [], // 部门列表
      deptTree: {
        children: "children",
        label: "teamName",
        value: "id",
        multiple: true,
        emitPath: false,
      },
      peopleDialog: false, // 学员弹窗
      stuOrOrgList: [
        {
          id: '0',
          label: '学员'
        },
        {
          id: '1',
          label: '组织'
        }
      ],
      userNewList: [],
      deptAllList: [],
      value1: [new Date(2000, 10, 10, 10, 10), new Date(2000, 10, 11, 10, 10)],
      trainList: [{
        value: '0',
        label: '线上会议'
      }, {
        value: '1',
        label: '线下会议'
      }],
      value: '',
      readonly: false,
      formInline: {
        name: "",
        subjectId:'',
        deptIds: '',
        teacherIds: '',
        timeLine: [],
        startTime:'',
        endTime:'',
        meetingIds:'',//到会人员ids
        type:'',
        orgId:[],
        address: '', // 地址
        stuOrOrg: '', 
        fileEcho:[],
        trainRecord:'',
        trainRemark: ''//备注
      },
      id: "",
      blockLoading: false,
      indextype: '',
      rules: {
        name: [
          { required: true, message: "请输入培训计划名称", trigger: "blur" },
        ],
        timeLine: [
        { required: true, message: "请选择培训时间", trigger: "change" },
        ],
        teacherIds:[
        { required: true, message: "请选择培训老师", trigger: "change" },
        ],
        type: [
        { required: true, message: "请选择培训方式", trigger: "change" },
        ],
        fileEcho: [
          { required: true, message: "请选择培训课件", trigger: "change" },
        ],
      },
      routeInfo: '',
      teacherList:[],//选择培训老师
      teacherNames:'',
      meetUserList:[],//到会人员
      trainUserNames:'',
      userList:[],
      userNewList:[]
    };
  },
  created() {
    this.routeInfo = JSON.parse(sessionStorage.getItem("routeInfo"));
    this.getSubjectList() // 获取科目分类列表
    this.getDeptList() // 获取所属单位
  },
  methods: {
    // 获取科目分类
    getSubjectList() {
      let params = {
        pageNo: 1,
        pageSize: 999,
      }
      this.$api.subjectList(params).then(res => {
        this.subjectData = this.$tools.transData(res.data, 'id', 'parentId', 'childList')
      })
    },
    // 获取所属单位
    getDeptList() {
      this.$api.getDeptListLaboratory({}).then((res) => {
        this.allDept = res.data.list;
        this.deptList = this.$tools.transData(
          res.data.list,
          "id",
          "parentId",
          "children"
        );
        this.deptAllList = res.data.list
      });
    },
    // 学员选择弹窗-------------------------------
    showUserDialog(opty) {
      this.opty = opty
      this.peopleDialog = true;
      this.$nextTick(() => {
        // this.$refs.userDialogRef.userSelectData = this.userList;
        this.$refs.userDialogRef.getTableList(true);
      });
    },
    //关闭弹窗
    closeDialog() {
      this.peopleDialog = false
    },
    sureDialogUser() {
      this.peopleDialog = false;
      if (this.opty == 'xueyuan') {
        this.userList = this.$refs.userDialogRef.userSelectData;
        const userNewList = this.userList.map(item => item.name)
        this.userNewList = userNewList
      }
      // 培训老师
      if(this.opty == 'teacher') {
        this.teacherList = this.$refs.userDialogRef.userSelectData;
        this.formInline.teacherIds = this.teacherList.map(item=>item.id).join(',')
        this.teacherNames = this.teacherList.map(item=>item.name).join('、')
      }
      // 参会人员
      if(this.opty == 'user') {
        this.meetUserList = this.$refs.userDialogRef.userSelectData;
        this.formInline.meetingIds = this.meetUserList.map(item => item.id).join(',')
        this.trainUserNames = this.meetUserList.map(item => item.name).join('、')
      }
    },
    btnType(val){
      if(val=='0'){
        this.userList = []
        this.userNewList = []
      }else{
        this.formInline.orgId = []
      }
    },
    deleteTag(index) {
      this.userNewList.splice(index, 1);
      this.userList.splice(index, 1);
    },
    onCancel() {
      this.$router.go(-1)
    },
     // 选中上一级学科科目
     handleChange(val) {
    },
    // 选择时间
    dataPink(data) {
      this.formInline.startTime = data[0]
      this.formInline.endTime = data[1]
    },
    addPersonShow(opty) {
      this.opty = opty
      this.peopleDialog = true;
      this.$nextTick(() => {
        this.$refs.userDialogRef.getTableList(true);
      });
    },
    // 点击确定
    complete() {
      this.$refs.formInline.validate((valid) => {
        if (valid) {
          if(this.formInline.stuOrOrg=='0'&&!this.userList.length){
            return this.$message.error('请先选择学员')
          }else if(this.formInline.stuOrOrg=='1'&&!this.formInline.orgId.length){
            return this.$message.error('请先选择组织')
          }
          let data = {
            ...this.formInline
          }
          data.deptIds = data.deptIds.join(',')
          delete data.fileEcho
          delete data.timeLine
          data.studentIds = this.userList.length?this.userList.map(item=>item.id).join(','):''
          data.orgId = this.formInline.orgId.length?this.formInline.orgId.join(','):''
          this.$api.trainPlanRecordEnter(data).then( res => {
            if(res.code == 200) {
              this.$router.go(-1)
            } else {
              this.$message.error(res.msg)
            }
          })
        }
      })
    },
    async fileChange(file, fileList) {
      const isLt40M = file.size / 1024 / 1024 < 40
      if (!isLt40M) {
        this.$message.error('上传图片大小不能超过 40MB!')
        fileList.splice(-1, 1); //移除选中图片
        return false
      }
      this.formInline.fileEcho = fileList
    },
    httpRequest() {
      this.formData = new FormData()
      this.formInline.fileEcho.forEach((item) => {
        this.formData.append('file', item.raw)
      })
      this.formData.append('hospitalCode', this.routeInfo.hospitalCode)
      axios({
        method: 'post',
        url: __PATH.BASE_URL_LABORATORY + "minio/upload",
        data: this.formData,
        headers: {
          "Content-Type": "application/json",
          token: this.routeInfo.token,
        },
      }).then(res => {
        this.formInline.trainRecord = res.data.data.fileRecordIds
      }).catch(() => {
        this.$message.error('上传失败')
      })
    },
    handleExceed() {
      this.$message.error('最多上传三张图片')
    },
    beforeAvatarUpload(file) {
      const isLt40M = file.size / 1024 / 1024 < 40
      if (!isLt40M) {
        this.$message.error('上传图片大小不能超过 40MB!')
        return false
      }
      if (file.name.indexOf(',') != -1) {
        this.$message.error('非法的文件名')
        return false
      }
    },
    handleRemove(file, fileList) {
      this.formInline.fileEcho = fileList
    },
  },
};
</script>
<style lang="scss" scoped>
.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
}

.content_box {
  height: 100%;
  padding: 30px 25px 20px 25px;
  background: #fff;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

:deep(.el-input.is-disabled .el-input__inner) {
  background: #f5f7fa !important;
}

.detailClass :deep(.el-input__inner) {
  border: none !important;
}

.detailClass :deep(.el-textarea__inner) {
  border: none;
  resize: none;
}

.project-textarea textarea {
  height: 120px;
}

.form-inline {
  margin-top: 20px;
}

/deep/ .el-form-item {
  display: inline-block;
}

/deep/ .el-date-editor .el-range__icon {
  margin-top: -5px;
}

/deep/ .el-date-editor .el-range__close-icon {
  margin-top: -5px;
}

.set_select_width {
  width: 300px;
  min-height: 80px;
  margin-top: -30px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  display: flex;
  flex-wrap: wrap;

  span {
    height: 20px;
    line-height: 20px;
    background-color: #f4f4f5;
    border-color: #e9e9eb;
    color: #909399;
    padding: 3px 8px;
    font-size: 12px;
    border-radius: 4px;
    margin: 2px 0 2px 6px;
    cursor: pointer;
  }

  p {
    padding-left: 15px;
    color: rgb(191, 196, 204);
    font-size: inherit;
  }
}

/deep/ .el-input__inner {
  height: 32px !important;
  line-height: 32px !important;
}
</style>
  