<script>
export default {
  name: 'CommunityEdit',
  props: {
    id: String,
    visible: Boolean
  },
  events: ['update:visible', 'success'],
  data: function () {
    return {
      formModel: {
        spaceName: '',
        addressCode: [],
        addressDetails: '',
        costPrice: '',
        hirePrice: ''
      },
      rules: {
        spaceName: [{ required: true, message: '请输入小区名称' }],
        costPrice: [{ required: true, message: '请输入成本单价' }],
        hirePrice: [{ required: true, message: '请输入出租单价' }]
      },
      loadingStatus: false,
      // 人员选择dialog显示
      userDialog: false,
      addressOptions: {
        lazy: true,
        label: 'name',
        value: 'code',
        lazyLoad: this.getAreaData
      },
      // 扩展信息字典
      extendInfoData: [],
      // 临时存储的扩展信息
      $tmpExtendInfo: null,
      readonly: false
    }
  },
  computed: {
    title: function () {
      return this.id ? '小区详情' : '创建小区'
    },
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    },
    maxValue() {
      return 99999.99
    },
    prefix() {
      return 'EXT_'
    }
  },
  watch: {
    dialogVisible(value) {
      if (!value) return
      if (!this.extendInfoData.length) {
        this.getExtendInfo()
      }
      this.readonly = !!this.id
      this.id && this.getDetail()
    }
  },
  methods: {
    getAreaData(node, resolve) {
      const parentCode = node.root ? '000000' : node.value
      this.$api.rentalHousingApi.selectAdministrativeList({ parentCode }).then((res) => {
        if (res.code === '200') {
          res.data.forEach((i) => {
            i.leaf = node.level >= 3
          })
          if (res.data.length) {
            resolve(res.data)
          } else {
            resolve([{ code: '', name: '无', leaf: true }])
          }
        } else {
          this.$message.error(res.message || '请求失败')
        }
      })
    },
    getDetail() {
      this.loadingStatus = true
      this.$api.rentalHousingApi
        .getSpaceEstateInfoById({ id: this.id })
        .then((res) => {
          if (res.code === '200') {
            this.formModel.spaceName = res.data.spaceName
            this.formModel.addressCode = res.data.addressCode.split(',')
            this.formModel.addressDetails = res.data.addressDetails || ''
            this.formModel.costPrice = res.data.costPrice || ''
            this.formModel.hirePrice = res.data.hirePrice || ''
            const extendInfo = JSON.parse(res.data.extendInfo || '{}')
            // 如果有扩展字段，将扩展字段数据设置到表单中
            if (this.extendInfoData.length) {
              this.extendInfoData.forEach((it) => {
                const value = extendInfo[it.fieldId] ?? ''
                // 扩展字段需要和表单字段映射
                this.$set(this.formModel, it.formField, value)
              })
            } else {
              this.$tmpExtendInfo = extendInfo
            }
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取供应商详情失败'))
        .finally(() => (this.loadingStatus = false))
    },
    onDialogClosed() {
      // dialog关闭时重置form表单
      this.$refs.formRef.resetFields()
    },
    onSubmit() {
      this.$refs.formRef
        .validate()
        .catch(() => Promise.reject())
        .then(() => {
          // config request data
          const params = {
            spaceName: this.formModel.spaceName,
            addressCode: this.formModel.addressCode.join(),
            addressName: this.$refs.cascaderRef.presentText,
            addressDetails: this.formModel.addressDetails,
            costPrice: this.formModel.costPrice,
            hirePrice: this.formModel.hirePrice,
            userId: this.$store.getters.userId, // 用户id
            userName: this.$store.getters.userName // 用户名
          }
          // 扩展字段数据
          const extendInfo = {}
          this.extendInfoData.forEach((it) => {
            extendInfo[it.fieldId] = this.formModel[it.formField]
          })
          params.extendInfo = JSON.stringify(extendInfo)
          this.loadingStatus = true
          // do request
          if (this.id) {
            params.id = this.id
            return this.$api.rentalHousingApi.updateSpaceEstateInfo(params)
          } else {
            return this.$api.rentalHousingApi.saveSpaceEstateInfo(params)
          }
        })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('操作成功')
            this.$emit('success')
            this.dialogVisible = false
          } else {
            throw res.message || '操作失败'
          }
        })
        .catch((msg) => {
          msg && this.$message.error(msg)
        })
        .finally(() => {
          this.loadingStatus = false
        })
    },
    // 获取扩展字段数据
    getExtendInfo() {
      this.$api.rentalHousingApi.queryExtendFieldList().then((res) => {
        if (res.code === '200') {
          res.data.forEach((it) => {
            if (it.fieldStatus === '1') {
              // 重命名表单字段名称，防止冲突
              const formField = this.prefix + it.fieldId
              let value = ''
              // 如果存在临时值，需要默认填充
              if (this.$tmpExtendInfo) {
                value = this.$tmpExtendInfo[formField]
              }
              // 设置扩展字段的默认值
              this.$set(this.formModel, formField, value)
              it.formField = formField
              this.extendInfoData.push(it)
            }
          })
          // 清除临时值
          this.$tmpExtendInfo = null
        }
      })
    },
    // 金额发生变化
    onPriceChange(field, val) {
      if (!val) return
      const price = Math.min(Number(val), this.maxValue).toFixed(2)
      this.$set(this.formModel, field, price)
    }
  }
}
</script>
<template>
  <el-dialog
    v-dialogDrag
    class="component community-edit"
    :title="title"
    width="550px"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    custom-class="model-dialog"
    destroy-on-close
    @close="onDialogClosed"
  >
    <el-form ref="formRef" v-loading="loadingStatus" :model="formModel" :rules="rules" label-width="95px" :disabled="readonly" :class="{ readonly }">
      <div class="form-title">
        <svg-icon name="right-arrow" />
        基础信息
      </div>
      <el-form-item label="小区名称" prop="spaceName">
        <el-input v-model="formModel.spaceName" placeholder="请输入小区名称" maxlength="50"></el-input>
      </el-form-item>
      <el-form-item label="地址信息" prop="addressCode">
        <el-cascader v-if="dialogVisible" ref="cascaderRef" v-model="formModel.addressCode" :props="addressOptions" placeholder="请选择省/市/区/街道"></el-cascader>
      </el-form-item>
      <el-form-item label="详细地址" prop="addressDetails">
        <el-input v-model="formModel.addressDetails" type="textarea" placeholder="详细地址" maxlength="200"></el-input>
      </el-form-item>
      <div class="form-title">
        <svg-icon name="right-arrow" />
        租金信息
      </div>
      <el-form-item label="成本单价" prop="costPrice">
        <el-input
          v-model="formModel.costPrice"
          type="number"
          :min="0"
          :max="maxValue"
          :step="0.01"
          placeholder="请输入成本单价"
          :maxlength="7"
          @change="onPriceChange('costPrice', $event)"
        >
          <span slot="append">元/m²/月</span>
        </el-input>
      </el-form-item>
      <el-form-item label="出租单价" prop="hirePrice">
        <el-input
          v-model="formModel.hirePrice"
          type="number"
          :min="0"
          :max="maxValue"
          :step="0.01"
          placeholder="请输入出租单价"
          :maxlength="7"
          @change="onPriceChange('hirePrice', $event)"
        >
          <span slot="append">元/m²/月</span>
        </el-input>
      </el-form-item>
      <template v-if="extendInfoData.length">
        <div class="form-title">
          <svg-icon name="right-arrow" />
          扩展信息
        </div>
        <el-form-item v-for="item of extendInfoData" :key="item.id" :label="item.fieldName" :prop="item.formField">
          <el-input v-if="item.fieldType === '1'" v-model="formModel[item.formField]" :placeholder="'请输入' + item.fieldName" maxlength="50"></el-input>
          <el-input v-if="item.fieldType === '2'" v-model="formModel[item.formField]" type="textarea" :rows="2" :placeholder="'请输入' + item.fieldName" maxlength="200"></el-input>
        </el-form-item>
      </template>
    </el-form>
    <template #footer>
      <template v-if="readonly">
        <el-button type="primary" plain @click="dialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="readonly = false">编辑</el-button>
      </template>
      <template v-else>
        <el-button type="primary" plain @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="loadingStatus" @click="onSubmit">确认</el-button>
      </template>
    </template>
  </el-dialog>
</template>
<style lang="scss" scoped>
.el-form {
  width: 100%;
  height: 100%;
  background-color: #fff;
  padding: 10px 16px 0;
  .el-form-item {
    .el-cascader,
    .el-select {
      width: 100%;
    }
    ::v-deep(.el-form-item__content) {
      .el-input-group {
        vertical-align: baseline;
      }
    }
  }
  .form-title {
    margin-bottom: 10px;
  }
  &.readonly {
    ::v-deep(.el-form-item) {
      margin-bottom: 10px;
      .el-form-item__label {
        &::before {
          display: none;
        }
      }
      .el-form-item__content {
        //.el-input {
        //  width: auto;
        //}
        .el-textarea__inner,
        .el-input__inner {
          background: transparent;
          border: none;
          color: inherit;
          width: auto;
          cursor: default;
          &::placeholder {
            color: transparent;
          }
        }
        .el-textarea__inner {
          resize: none;
          padding-top: 10px;
        }
        .el-input-group__append {
          background: transparent;
          border: none;
          padding-left: 0;
        }
        .el-input__suffix {
          display: none;
        }
      }
    }
  }
}
</style>
