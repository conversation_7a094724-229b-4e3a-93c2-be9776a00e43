<template>
  <div ref="topology"  class="scada-preview">
    <div v-loading="scadaLoading" customClass="custom-loading" element-loading-text="组态图加载中" style="width: 100%; height:100%">
      <meta2d-vue-preview v-if="JSON.stringify(scadaData) != '{}'" ref="meta2dVuePreview" :onlyPreview="true" :queryData="scadaData" :userInfo="userInfo" :baseUrl="baseUrl" @initCompleted="initCompleted" />
    </div>
  </div>
</template>

<script>
// onlyPreview 是否只是预览 不需要查看其他图纸及弹窗 区别与绘制页面的预览
// queryData 预览的图纸id及时间戳
// userInfo 用户信息
// baseUrl 接口ip地址

import { monitorTypeList } from '@/util/dict.js'
export default {
  name: 'scadaShow',
  props: {
    projectId: {
      type: String,
      default: ''
    },
    entityMenuCode: {
      type: String,
      default: ''
    },
    surveyEntityCode: {
      type: String,
      default: ''
    },
    scadaType: {
      type: String,
      default: ''
    },
    requestHttp: {
      type: String,
      default: __PATH.VUE_IEMC_API
    }
  },
  data() {
    return {
      monitorData: monitorTypeList.find((item) => item.projectCode == this.projectId),
      scadaLoading: false,
      scadaTimer: null,
      scadaData: {},
      baseUrl: this.requestHttp,
      userInfo: {
        avatarUrl: '',
        captcha: '',
        createdAt: '',
        deletedAt: '',
        email: '',
        id: '1',
        phone: '',
        updatedAt: '',
        username: 'admin',
        vip: 1,
        vipExpiry: '',
        isVip: true // 该属性是计算出来的，不是数据库中的
      }
    }
  },
  watch: {
    '$store.state.settings.sidebarCollapse': {
      handler(val) {
        this.$nextTick(() => {
          setTimeout(() => {
            if (JSON.stringify(this.scadaData) != '{}') {
              meta2d.resize()
            }
          }, 250)
        })
      },
      deep: true
    }
  },
  created() {
    // 网关需要token 组件获取token通过localStorage
    window.localStorage.setItem('token', 'Bearer ' + this.$store.state.user.token)
  },
  mounted() {
    // 图纸类型 剖面图还是普通图纸
    if (this.projectId && (this.entityMenuCode || this.surveyEntityCode)) {
      if (this.scadaType == 'profile') {
        this.getSurveyImgByCode()
      } else {
        this.getScadaList()
      }
    } else {
      this.scadaData = {}
    }
  },
  beforeDestroy() {
    // console.log('销毁')
    clearInterval(this.scadaTimer)
  },
  methods: {
    initCompleted() {
      this.scadaLoading = false
      console.log('initCompleted')
    },
    // 实时监测--scada
    getScadaList() {
      this.scadaLoading = true
      this.scadaData = {}
      this.$api
        .getScadaList(
          {
            projectId: this.projectId,
            menuCode: this.entityMenuCode
          },
          this.requestHttp
        )
        .then((res) => {
          if (res.data.length > 0) {
            const scadaJsonData = res.data[0]?.isiJsonStr || ''
            this.handleScadaChange(scadaJsonData, { isiId: res.data[0]?.isiId })
          } else {
            this.scadaData = {}
            this.$message.warning('暂无数据')
            this.scadaLoading = false
          }
        })
    },
    // 剖面图数据
    getSurveyImgByCode() {
      this.scadaLoading = true
      this.scadaData = {}
      this.$api
        .getSurveyImgByCode({
          projectCode: this.projectId,
          surveyCode: this.surveyEntityCode
        })
        .then((res) => {
          if (Object.keys(res.data).length > 0) {
            const scadaJsonData = res.data.isiJsonStr
            this.handleScadaChange(scadaJsonData, { isiId: res.data.isiId })
          } else {
            this.scadaData = {}
            this.$message.warning('暂无数据')
            this.scadaLoading = false
          }
        })
    },
    // 消防父级code换取图纸id(通过消防直接调用)
    setScadaData(isiId) {
      // 先重置再赋值，重新加载dom
      this.scadaData = {}
      this.$nextTick(() => {
        this.scadaData = {
          r: Date.now() + '',
          id: isiId
        }
      })
    },
    // 选择scada
    handleScadaChange(val, assignData = {}) {
      let updataList = JSON.parse(val)
      Object.assign(updataList, assignData)
      this.data = updataList
      this.scadaData = {
        r: Date.now() + '',
        id: assignData.isiId
      }
      this.setH5ScadaData()
    },
    // 设置scada数据 医用气体 定时保存数据存库，提供给移动端使用
    setH5ScadaData() {
      if (this.monitorData.projectName === '医用气体') {
        const scadaData = this.data
        scadaData ? this.$api.setScadaToRedis({ scadaStrJson: JSON.stringify(scadaData), isiId: this.data.isiId }) : ''
        this.scadaTimer = setInterval(() => {
          scadaData ? this.$api.setScadaToRedis({ scadaStrJson: JSON.stringify(scadaData), isiId: this.data.isiId }) : ''
        }, 180000)
      }
    }
  }
}
</script>
<style lang="scss">
.scada-preview {
  position: relative;
  height: calc(100% - 0px);
  width: 100%;
  padding: 16px 0 0 16px;

  .tools {
    position: absolute;
    top: 16px;
    right: 10px;

    & > div {
      flex-grow: 1;
    }

    button {
      margin: 12px 16px;
      color: #fff;
      font-size: 14px;
      font-family: PingFangSC-Regular;
      line-height: 1;
    }
  }
  .custom-loading {

  }
  .menus-list {
    position: absolute;
    top: 28px;
    left: 20px;
  }
}
</style>
