{"name": "back-stage", "version": "1.0.0", "private": true, "scripts": {"dev": "vite --host 0.0.0.0", "dev:test": "vite --host 0.0.0.0 --mode test", "build:test": "vite build --mode test && node version.js test", "build:bjsjtyy": "vite build --mode bjsjtyy && node version.js bjsjtyy", "build:gqgjzyy": "vite build --mode gqgjzyy && node version.js gqgjzyy", "build:jxsdeyy": "vite build --mode jxsdeyy && node version.js jxsdeyy", "build:bjazyy": "vite build --mode bjazyy && node version.js bjazyy", "build:bjsyzyyy": "vite build --mode bjsyzyyy && node version.js bjsyzyyy", "build:ljxyy": "vite build --mode ljxyy && node version.js ljxyy", "build:dxrmyy": "vite build --mode dxrmyy && node version.js dxrmyy", "build:szzlyy": "vite build --mode szzlyy && node version.js szzlyy", "build:szdeyy": "vite build --mode szdeyy && node version.js szdeyy", "build:syfyyy": "vite build --mode syfyyy && node version.js syfyyy", "build:syzxyy": "vite build --mode syzxyy && node version.js syzxyy", "build:szlhyy": "vite build --mode szlhyy && node version.js szlhyy", "build:fjslyy": "vite build --mode fjslyy && node version.js fjslyy", "build:tjzlyy": "vite build --mode tjzlyy && node version.js tjzlyy", "build:schxyy": "vite build --mode schxyy && node version.js schxyy", "build:sjyyy": "vite build --mode sjyyy && node version.js sjyyy", "build:216": "vite build --mode 216 && node version.js 216", "build:outernet": "vite build --mode outernet && node version.js outernet", "build:uat": "vite build --mode uat && node version.js uat", "build:mhzyy": "vite build --mode mhzyy && node version.js mhzyy", "build:szrmyy": "vite build --mode szrmyy && node version.js szrmyy", "build": "vite build && node version.js", "build:mac": "node --max_old_space_size=4096 ./node_modules/vite/bin/vite.js build", "fix-memory-limit": "cross-env LIMIT=16384  increase-memory-limit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "stylelint": "stylelint src/**/*.{css,scss,vue} --fix", "svgo": "svgo -f src/assets/icons", "new": "plop"}, "dependencies": {"@js-preview/docx": "^1.6.2", "@js-preview/excel": "^1.7.8", "@js-preview/pdf": "^2.0.2", "@micro-zoe/micro-app": "^1.0.0-rc.4", "@tinymce/tinymce-vue": "^3.2.6", "animate.css": "^4.1.1", "autoprefixer": "^10.4.15", "axios": "^0.21.0", "core-js": "^3.6.4", "crypto-js": "^4.1.1", "dayjs": "^1.9.4", "dplayer": "^1.27.1", "echarts": "^5.4.2", "el-table-infinite-scroll": "^1.0.11", "element-ui": "^2.14.0", "enquire.js": "^2.1.6", "file-saver": "^2.0.5", "hotkeys-js": "^3.8.1", "html2canvas": "^1.4.1", "indexof": "^0.0.1", "jquery": "^3.6.3", "js-base64": "^2.6.4", "jspdf": "^2.5.1", "jutils-src": "^1.0.0-beta2", "lodash": "^4.17.21", "meta2d-vue": "^1.0.36", "moment": "^2.29.4", "mutationobserver-shim": "^0.3.7", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "path-to-regexp": "^6.2.0", "postcss-html": "^1.5.0", "qrcodejs2": "^0.0.2", "qrcodejs2-fix": "0.0.1", "qs": "^6.11.2", "quill": "^1.3.7", "remixicon": "^2.5.0", "screenfull": "^5.0.2", "sortablejs": "^1.15.6", "spinkit": "^2.0.1", "tinymce": "^5.5.1", "v-charts": "^1.19.0", "video.js": "^8.21.0", "vue": "^2.7.14", "vue-baidu-map": "^0.21.22", "vue-contextmenujs": "^1.3.13", "vue-cookies": "^1.7.4", "vue-drag-resize": "^1.5.4", "vue-esign": "^1.1.4", "vue-i18n": "^8.22.1", "vue-jsonp": "^2.0.0", "vue-meta": "^2.4.0", "vue-quill-editor": "^3.0.6", "vue-responsive-dash": "^0.5.0", "vue-router": "^3.6.4", "vue-slider-component": "^3.2.24", "vue-video-player": "^5.0.1", "vue2-org-tree": "^1.3.6", "vuedraggable": "^2.24.3", "vuex": "^3.5.1", "xlsx": "^0.18.5", "xlsx-js-style": "^1.2.0"}, "devDependencies": {"@kazupon/vue-i18n-loader": "^0.5.0", "@vitejs/plugin-legacy": "^4.0.5", "@vitejs/plugin-vue2": "^2.2.0", "@vitejs/plugin-vue2-jsx": "^1.0.2", "@vue/eslint-config-prettier": "^7.0.0", "cross-env": "^7.0.3", "eslint": "^8.5.0", "eslint-plugin-vue": "^9.0.0", "formidable": "^2.0.1", "increase-memory-limit": "^1.0.7", "less": "^3.13.1", "less-loader": "^4.1.0", "lint-staged": "^11.0.0", "plop": "^2.7.4", "prettier": "^2.5.1", "sass": "^1.28.0", "sass-loader": "^10.0.4", "sass-resources-loader": "^2.1.1", "stylelint": "^14.14.1", "stylelint-config-standard": "^29.0.0", "stylelint-config-standard-scss": "^6.1.0", "stylelint-config-standard-vue": "^1.0.0", "stylelint-scss": "^4.3.0", "stylus": "^0.59.0", "stylus-loader": "^7.1.0", "svg-sprite-loader": "^6.0.6", "svgo": "^2.3.0", "vite": "^4.3.9", "vite-plugin-compression": "^0.5.1", "vite-plugin-ejs": "^1.6.4", "vite-plugin-html": "^3.2.0", "vite-plugin-optimize-persist": "^0.1.2", "vite-plugin-package-config": "^0.1.1", "vite-plugin-require-transform": "^1.0.9", "vite-plugin-svg-icons": "^2.0.1"}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}