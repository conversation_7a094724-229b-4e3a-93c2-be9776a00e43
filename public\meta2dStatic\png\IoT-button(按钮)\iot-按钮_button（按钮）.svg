<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 150 150"><defs><style>.cls-1{fill:url(#未命名的渐变_70);}.cls-2{fill:#fdbf34;}.cls-3{fill:#e2e2e2;opacity:0.52;}</style><linearGradient id="未命名的渐变_70" y1="75" x2="150" y2="75" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#e2e2e2"/><stop offset="0.01" stop-color="#dadada"/><stop offset="0.04" stop-color="#b9b9b9"/><stop offset="0.07" stop-color="#a5a5a5"/><stop offset="0.09" stop-color="#9e9e9e"/><stop offset="0.11" stop-color="#a3a3a3"/><stop offset="0.49" stop-color="#f6f6f6"/><stop offset="0.63" stop-color="#f3f3f3"/><stop offset="0.73" stop-color="#eaeaea"/><stop offset="0.82" stop-color="#dbdbdb"/><stop offset="0.9" stop-color="#c6c6c6"/><stop offset="0.97" stop-color="#aaa"/><stop offset="1" stop-color="#9b9b9b"/></linearGradient></defs><title>iot-按钮</title><g id="图层_39" data-name="图层 39"><circle class="cls-1" cx="75" cy="75" r="75"/><circle class="cls-2" cx="75" cy="75" r="55.17"/><path class="cls-3" d="M75.17,130.17c-30.47,0,10.38-24.7,10.38-55.17S44.7,19.83,75.17,19.83a55.17,55.17,0,0,1,0,110.34Z"/></g></svg>