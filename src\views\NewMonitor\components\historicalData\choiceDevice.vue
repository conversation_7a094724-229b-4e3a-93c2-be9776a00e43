<template>
    <el-dialog v-if="dialogShow" title="选择批量导出设备" width="60%" :visible.sync="dialogShow" custom-class="model-dialog"
        :before-close="closeDialog">
        <div class="camera_content" style="padding: 10px 20px 10px 10px;">
            <div class="header_operation">
                <div class="search_box">
                    <div class="search_select">
                        <el-input v-model="assetsData.assetsNameCode" placeholder="设备名称/编码"
                            style="width: 170px;margin-right:10px;"></el-input>
                        <el-select v-model="assetsData.sysOf1" placeholder="所属品类" filterable clearable
                            style="width: 170px;margin-right:10px;">
                            <el-option v-for="(item, index) in deviceTypeList" :key="index"
                                :label="item.dictionaryDetailsName" :value="item.dictionaryDetailsId"></el-option>
                        </el-select>
                        <el-cascader ref="refHandle" :options="customGroupingList" style="width: 170px;"
                            :props="{ checkStrictly: true, expandTrigger: 'hover' }" clearable placeholder="所属分组"
                            :show-all-levels="false" v-model="handlerValue" @change="handleChange"></el-cascader>
                    </div>
                </div>
                <div class="header_btn" style="margin-top: 3px;">
                    <el-button type="primary" plain @click="assetsResetting">重置</el-button>
                    <el-button type="primary" @click="getAssetsListData">查询</el-button>
                </div>
            </div>
            <div class="table_div">
                <TablePage ref="multipleAssetsTable" v-loading="tableLoading" row-key="id" tooltip-effect="dark"
                    :tableColumn="tableColumn" :data="tableData" height="300px" :pageData="pageinationData"
                    @pagination="paginationChange" :selectable="selectableRow" @select="handleSelectionChange"
                    @select-all="toggleSelectAll">
                </TablePage>
                <el-alert v-if="multipleSelection.length > 5" title="最多只能选择 5 条数据" type="warning" />
            </div>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button type="primary" plain @click="closeDialog">取 消</el-button>
            <el-button type="primary" @click="groupSubmit">确 定</el-button>
        </span>
    </el-dialog>
</template>
<script lang="jsx">
import { transData } from '@/util'
export default {
    name: '',
    props: {
        dialogShow: {
            type: Boolean,
            default: false
        },
        dialogData: {
            type: Object,
            default: () => ({
                assetsNameCode: '',
                sysOf1: '',
                groupId: ''
            })
        },
        systemCode: {
            type: String,
            default: () => {
                throw ''
            }
        },
        isKeyDevice: {
            type: String,
            default: () => "0"
        },
        handlerArr: {
            type: Array,
            default: () => [] // 设置默认值为一个空数组
        }
    },
    data() {
        return {
            assetsData: {
                assetsNameCode: "",
                sysOf1: "",
                groupId: "",
            },
            handlerValue: [],
            deviceTypeList: [],//所属品类
            customGroupingList: [],//所属分组
            multipleSelection: [],//选中的数据
            tableColumn: [
                {
                    type: 'selection',
                    align: 'center',
                    width: 80,
                    reserveSelection: true
                },
                {
                    prop: 'assetsName',
                    label: '设备名称'
                },
                {
                    prop: 'factoryCode',
                    label: '物联设备',
                    render: (h, row) => {
                        return (
                            <el-select v-model={row.row.factoryCode} filterable collapse-tags class="monitor-select" style="width:100%;" onChange={async (value) => {
                                // 清空监测参数和自定义属性
                                row.row.property = '';
                                row.row.customProperties = []; // 清空自定义属性

                                // 调用接口以重新获取可选参数
                                const properties = await this.getCustomIotAssetsProperties(row.row.id, value, false);
                                this.$set(row.row, 'customProperties', properties); // 更新自定义属性
                                if (this.isKeyDevice === '0') {
                                    row.row.property = null
                                } else {
                                    // 如果有可选的属性，设置默认值
                                    if (properties.length > 0) {
                                        row.row.property = properties[0].metadataTag; // 取第一个属性作为默认值
                                    }
                                }
                            }}>
                                {row.row.bindingIotAssets.length > 0 ? (
                                    row.row.bindingIotAssets.map((item) => (
                                        <el-option key={item.factoryCode} label={item.assetsName} value={item.factoryCode}></el-option>
                                    ))
                                ) : (
                                    <el-option label="无可选项" value=""></el-option>
                                )}
                            </el-select>
                        );
                    },
                    hasJudge: true
                },
                {
                    prop: 'property',
                    label: '监测参数',
                    render: (h, row) => {
                        return (
                            <el-select v-model={row.row.property}
                                clearable={this.isKeyDevice === '0'} filterable collapse-tags class="monitor-select" style="width:100%;" placeholder="全部">
                                {row.row.customProperties.length > 0 ? (
                                    row.row.customProperties.map((item) => (
                                        <el-option key={item.metadataTag} label={item.metadataName} value={item.metadataTag}></el-option>
                                    ))
                                ) : (
                                    <el-option label="无可选项" value=""></el-option>
                                )}
                            </el-select>
                        );
                    },
                    hasJudge: true
                },
            ],
            tableData: [],
            tableLoading: false,
            pageinationData: {
                page: 1,
                pageSize: 15,
                total: 0
            },
        }
    },
    watch: {
        handlerValue() {
            if (this.$refs.refHandle) {
                this.$refs.refHandle.dropDownVisible = false; //监听值发生变化就关闭它
            }
        },
    },
    mounted() {
        console.log(this.handlerArr, 'handlerArr');

        this.assetsData.assetsNameCode = this.dialogData.assetsNameCode || ''
        this.assetsData.sysOf1 = this.dialogData.sysOf1 || ''
        this.assetsData.groupId = this.dialogData.groupId || ''
        this.handlerValue = this.handlerArr || []
        this.getAssetsListData()
        this.getDeviceType()
        this.getAssetsGroup()
    },
    methods: {
        //  所属品类
        getDeviceType() {
            this.deviceTypeList = []
            let data = {
                dictionaryCode: this.systemCode,
                equipAttr: '2',
            }
            this.$api.querySubCategoryDetailsRelatedAssets(data).then((res) => {
                if (res.code == '200') {
                    this.deviceTypeList = res.data
                }
            })
        },
        // 所属分组
        getAssetsGroup() {
            this.customGroupingList = []
            let data = {
                dictionaryDetailsCode: this.systemCode,
                equipAttr: '2',
                groupName: "",
            }
            this.$api.getCustomGroupingTree(data).then((res) => {
                if (res.code == '200') {
                    this.customGroupingList = transData(res.data, 'id', 'pid', 'children')
                }
            })
        },
        // 分组选择
        handleChange(value) {
            this.assetsData.groupId = value.length > 0 ? value[value.length - 1] : '';
        },
        // 选中某一项
        handleSelectionChange(val) {
            if (val.length > 5) {
                this.$message.warning("最多只能选择 5 条数据");
                this.multipleSelection = val.slice(0, 5);
                this.$refs.multipleAssetsTable.clearSelection();
                this.multipleSelection.forEach(item => {
                    this.$refs.multipleAssetsTable.toggleRowSelection(item, true);
                });
            } else {
                this.multipleSelection = val;
            }
        },
        // 全选
        toggleSelectAll() {
            const allData = this.tableData;
            const shouldSelect = this.multipleSelection.length < 5;
            if (shouldSelect) {
                this.multipleSelection = allData.slice(0, 5);
                this.$refs.multipleAssetsTable.clearSelection();
                this.multipleSelection.forEach(item => {
                    this.$refs.multipleAssetsTable.toggleRowSelection(item, true);
                });
            } else {
                this.$refs.multipleAssetsTable.clearSelection();
                this.multipleSelection = [];
            }
        },
        selectableRow(row, index) {
            // 只有当选择的数量小于5时，行才可选
            return this.multipleSelection.length < 5 || this.multipleSelection.includes(row);
        },
        // 重置
        assetsResetting() {
            Object.assign(this.assetsData, {
                assetsNameCode: "",
                sysOf1: "",
                groupId: "",
            })
            this.handlerValue = []
            this.getAssetsListData()
        },
        // 获取被监测设备绑定列表
        async getCustomIotAssets(id) {
            try {
                const response = await this.$api.getCustomIotAssets(id); // 假设你在这里调用 API
                return response.data || []; // 确保返回数据，或返回空数组
            } catch (error) {
                return []; // 处理错误，返回空数组
            }
        },
        // 获取参数
        async getCustomIotAssetsProperties(id, code, filter) {
            try {
                const response = await this.$api.getCustomIotAssetsProperties(id, code, filter); // 假设你在这里调用 API
                return response.data || []; // 确保返回数据，或返回空数组
            } catch (error) {
                return []; // 处理错误，返回空数组
            }
        },
        // 关联设备（查询）
        getAssetsListData() {
            let params = {
                assetsNameCode: this.assetsData.assetsNameCode,
                dictionaryDetailsCode: this.systemCode,
                equipAttr: '2',
                groupId: this.assetsData.groupId,
                page: this.pageinationData.page,
                pageSize: this.pageinationData.pageSize,
                sysOf1: this.assetsData.sysOf1,
            };
            this.tableData = [];
            this.tableLoading = true;
            this.$api.getOperationalMonitoringList(params).then(async (res) => {
                this.tableLoading = false;
                if (res.code === '200') {
                    this.tableData = res.data.records.map(item => ({
                        ...item,
                        bindingIotAssets: [],
                        customProperties: [],
                        property: null // 初始化为 null
                    }));
                    this.pageinationData.total = res.data.total;
                    const customAssetsPromises = this.tableData.map(async (item) => {
                        const customAssets = await this.getCustomIotAssets(item.id);
                        if (customAssets && customAssets.length > 0) {
                            item.bindingIotAssets = Array.isArray(customAssets) ? customAssets : [customAssets];
                            const defaultAsset = item.bindingIotAssets[0];
                            item.factoryCode = defaultAsset.factoryCode;
                            const properties = await this.getCustomIotAssetsProperties(item.id, defaultAsset.factoryCode, false);
                            this.$set(item, 'customProperties', properties);
                            // 设置默认值
                            if (this.isKeyDevice === '0') {
                                item.property = null
                            } else {
                                if (properties.length > 0) {
                                    item.property = properties[0].metadataTag; // 取第一个属性作为默认值
                                }
                            }

                        } else {
                            item.bindingIotAssets = [];
                            item.customProperties = [];
                        }
                    });

                    // 等待所有请求完成
                    await Promise.all(customAssetsPromises);
                }
            });
        },

        paginationChange(pagination) {
            Object.assign(this.pageinationData, pagination)
            this.getAssetsListData()
        },
        closeDialog() {
            this.$emit('closeDialog')
        },
        groupSubmit() {
            let dataList = {}
            if (this.multipleSelection.length) {
                if (this.isKeyDevice === '0') {
                    dataList = {
                        deviceBatch: this.multipleSelection.map((item) => ({
                            factoryCode: item.factoryCode,
                            property: item.property,
                            belongAssetsName: item.assetsName
                        }))
                    };
                } else if (this.isKeyDevice === '1') {
                    dataList = {
                        deviceBatch: this.multipleSelection.map((item) => ({
                            deviceId: item.factoryCode,
                            metadataTag: item.customProperties.find(obj => {
                                return obj.metadataTag === item.property
                            })?.metadataTag,
                            productId: item.customProperties.find(obj => {
                                return obj.metadataTag === item.property
                            }).product,
                            assetsName: item.bindingIotAssets.find(obj => {
                                return obj.factoryCode === item.factoryCode
                            }).assetsName,

                        }))
                    };
                }

                this.$emit('submitDialog', dataList)
            } else {
                this.$message({
                    message: '请选择关联设备！',
                    type: 'warning'
                })
            }
        }
    }
}
</script>
<style lang="scss" scope>
.model-dialog {

    // width: 60%;
    .camera_content {
        width: 100%;
        margin: 0 auto;
        background: #fff;
        border-radius: 4px;
        // display: flex;
        width: 100%;
        height: 100%;

        .header_operation {
            // padding: 24px;
            margin-bottom: 10px;
            display: flex;

            .search_box {
                display: flex;

                >div {
                    margin-right: 16px;
                }

                .search_input {
                    width: 200px;
                }
            }
        }

        .table_div {
            height: calc(100% - 100px);
        }

        .paging_box {
            display: flex;
            justify-content: flex-end;
            padding: 6px;
        }
    }
}
</style>