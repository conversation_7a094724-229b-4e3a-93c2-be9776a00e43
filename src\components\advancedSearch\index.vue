<template>
  <div class="main_content" :class="{ close: isCloseChild }">
    <header>
      <i class="el-icon-close" style="font-size: 28px; vertical-align: middle; cursor: pointer;" @click="close"></i>
      高级搜索
      <div class="tools">
        <el-button type="primary" class="sino-button-sure" @click="searchList">立即搜索</el-button>
        <el-button type="primary" class="sino-button-sure" @click="resetSearch">清空查询</el-button>
        <el-button type="primary" plain @click="close">关闭</el-button>
      </div>
    </header>
    <div class="content">
      <slot name="content"></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'advancedSearch',
  props: ['closeState'],
  data: function () {
    return {
      isCloseChild: this.closeState
    }
  },
  watch: {
    closeState: {
      handler(val) {
        this.isCloseChild = val
        this.$emit('isCloseState', this.isCloseChild)
      },
      deep: true
    }
  },
  mounted() {},
  methods: {
    close() {
      this.isCloseChild = !this.isCloseChild
      this.$emit('isCloseState', this.isCloseChild)
    },
    resetSearch() {
      this.$emit('resetSearch')
    },
    searchList() {
      this.isCloseChild = !this.isCloseChild
      this.$emit('searchList')
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  box-sizing: border-box;
  padding: 20px 14px;
  padding-bottom: 80px;
  overflow: auto;
  height: calc(100%);
}

.content input {
  width: 200px;
  height: 40px;
}

.content .sino_sdcp_input {
  margin: 20px 10px 0 0;

  /* line-height: 40px; */
}

.main_content {
  position: absolute;
  top: 0;
  right: 0;
  background: #fff;
  transition: all 0.5s;
  z-index: 999;
  text-align: left;
  width: 640px;
  height: calc(100%);
  box-shadow: -4px 0 4px 0 rgb(203 205 220 / 24%);
  float: right;
}

.close {
  margin-right: -680px;
}

.primary {
  margin-top: 5px;
  background-color: #2cc7c5;
  color: #fff;
  width: 94px;
  vertical-align: top;
}

footer {
  width: 100%;
  height: 60px;
  line-height: 60px;
  box-sizing: border-box;
  background-color: #fff;
  text-align: right;
  box-shadow: 0 -4px 4px 0 rgb(243 244 248 / 100%);
  padding-right: 18px;
  position: absolute;
  bottom: 0;
}

header {
  height: 51px;
  border-radius: 0 10px 0 0;
  background: rgb(242 244 251 / 100%);
  line-height: 51px;
  padding: 0 20px;
  font-size: 20px;
  font-family: NotoSansHans-Medium;
  font-weight: 500;
  color: rgb(96 98 102 / 100%);
  box-sizing: border-box;
  border-bottom: 1px solid #d8dee7;
}

.tools {
  float: right;
  box-sizing: border-box;
}

.header-close {
  cursor: pointer;
  color: #606266;
  border: 1px solid rgb(220 223 229 / 100%);
  vertical-align: top;
  margin-top: 5px;
}

.header-close:hover {
  color: rgb(44 199 197 / 100%);
}

.header-close:focus {
  color: rgb(44 199 197 / 100%);
  background-color: #eaf9f9;
}

@media screen and (max-width: 1600px) {
  .content .sino_sdcp_input {
    margin: 14px 10px 0 0;

    /* line-height: 40px; */
  }

  .main_content {
    width: 510px;
  }

  header {
    height: 40px;
    line-height: 40px;
  }

  .content {
    padding: 10px 0 0 10px;
    height: 100%;
  }

  footer {
    height: 40px;
    line-height: 40px;
  }
}
</style>
