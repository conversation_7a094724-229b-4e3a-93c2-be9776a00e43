<template>
  <PageContainer :footer="true">
    <div slot="header">
      <div class="searchForm">
        <div class="search-box">
          <el-input v-model="searchForm.assetsCodeName" clearable filterable placeholder="请输入设备名称/编码" class="ml-16"
            @blur="event => searchForm.assetsCodeName = event.target.value.replace(/\s+/g, '')"></el-input>
          <div class="ml-16">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button type="primary" plain @click="reset">重置</el-button>
          </div>
        </div>
      </div>
    </div>
    <div slot="content" ref="contentRef" class="table-box">
      <div class="tableContainer">
        <el-button type="primary" style="margin-bottom: 12px" @click="handleListEvent('add')">绑定设备</el-button>
        <el-button :disabled="selectionList.length == 0" type="primary" style="font-size: 14px"
          @click="batchDel">批量删除</el-button>
        <el-button :disabled="selectionList.length == 0" type="primary" style="font-size: 14px"
          @click="batchStatus('quarantine')">批量隔离</el-button>
        <el-button :disabled="selectionList.length == 0" type="primary" style="font-size: 14px"
          @click="batchStatus('noQuarantine')">取消隔离</el-button>
        <el-button :disabled="selectionList.length == 0" type="primary" style="font-size: 14px"
          @click="batchExecute">批量执行</el-button>
        <el-button :disabled="selectionList.length == 0" type="primary" style="font-size: 14px"
          @click="batchSetting">批量设定</el-button>
        <div class="teamOperation" style="height: 100%;">
          <div class="emergencyTeamTable" style="height:calc( 100% - 0px);">
            <el-table :data="tableData" border @selection-change="handleSelectionChange" height="100%">
              <el-table-column type="selection" width="45px"></el-table-column>
              <el-table-column prop="assetsName" label="设备名称" show-overflow-tooltip align="center"> </el-table-column>
              <el-table-column prop="assetsCode" label="设备编码" show-overflow-tooltip align="center"> </el-table-column>
              <el-table-column prop="operateParameter" label="控制参数" show-overflow-tooltip align="center">
              </el-table-column>
              <el-table-column prop="isIsolated" label="是否隔离" show-overflow-tooltip align="center">
                <template slot-scope="scope">
                  <span>{{ scope.row.isIsolated === true ? '是' : '否' }}</span>
                </template>
              </el-table-column>
              <el-table-column label="排序" show-overflow-tooltip align="center">
                <template slot-scope="scope">
                  <el-input type="text" v-model="scope.row.sortOrder" @focus="originalValue = scope.row.sortOrder"
                    @blur="onSortOrderBlur(scope.row.settingId, scope.row.sortOrder, originalValue)"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template slot-scope="scope">
                  <el-button type="text"
                    @click="handleListEvent('controlSetting', scope.row.settingId)">控制设定</el-button>
                  <el-button type="text" @click="handleListEvent('execute', scope.row.settingId)">执行</el-button>
                  <el-button type="text" @click="handleListEvent('del', scope.row.settingId)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination :current-page="pagination.pageNo" :page-sizes="[15, 30, 50, 100]"
              :page-size="pagination.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="pageTotal"
              @size-change="handleSizeChange" @current-change="handleCurrentChange">
            </el-pagination>
          </div>
        </div>
      </div>

      <!-- 控制设定 -->
      <el-dialog v-dialogDrag :visible.sync="dialogVisible" :before-close="dialogClosed" :modal="false"
        :close-on-click-modal="false" title="控制设定" width="50%" custom-class="model-dialog">
        <div class="diaContent" style="padding: 10px">
          <el-form ref="formInline" :model="formInline" label-width="100px" :rules="rules" label-position="right">
            <el-form-item label="设定参数：" prop="groupHostextension" class="form-item">
              <el-button type="primary" v-if="formInline.parameterList.length < filterItem.length"
                @click="controlSure('add')"><i class="el-icon-plus"></i> 新增</el-button>
            </el-form-item>
            <div>
              <div v-for="(item, index) in formInline.parameterList" :key="index" :gutter="20">
                <el-form-item label="控制参数：" prop="groupHostextension" class="form-item">
                  <el-select ref="troopDeviceCode" v-model="item.metadataNameAlias" placeholder="请选择控制参数" clearable
                    @change="selectChange(item.metadataNameAlias, index)" filterable>
                    <el-option v-for="(obj, i) in filterItem" :key="i" :label="obj.metadataNameAlias"
                      :value="obj.identifier">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="设定值：" prop="groupHostextension" class="form-item">
                  <el-select @change="handleSelectSetValue(item.parameter, index)" ref="troopDeviceCode"
                    v-model="item.parameter" placeholder="请选择设定值" clearable filterable
                    v-if="item.valueType.valueType.type == 'enum'">
                    <el-option v-for="(obj, i) in item.valueType.valueType.elements" :key="i"
                      :label="obj.textAlias ? obj.textAlias : obj.text" :value="obj.value"> </el-option>
                  </el-select>
                  <el-select @change="handleSelectSetValue(item.parameter, index)" ref="troopDeviceCode"
                    v-model="item.parameter" placeholder="请选择设定值" clearable filterable
                    v-else-if="item.valueType.valueType.type == 'boolean'">
                    <el-option
                      :label="item.valueType.valueType.falseTextAlias ? item.valueType.valueType.falseTextAlias : item.valueType.valueType.falseText"
                      :value="item.valueType.valueType.falseValue">
                    </el-option>
                    <el-option
                      :label="item.valueType.valueType.trueTextAlias ? item.valueType.valueType.trueTextAlias : item.valueType.valueType.trueText"
                      :value="item.valueType.valueType.trueValue">
                    </el-option>
                  </el-select>
                  <el-input @change="handleSelectSetValue(item.parameter, index)" v-else v-model.trim="item.parameter"
                    placeholder="请输入"></el-input>
                </el-form-item>
                <span v-if="index != 0" @click="controlSure('delete', index)" style="cursor: pointer"> <i
                    class="el-icon-delete"></i> </span>
              </div>
            </div>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="dialogClosed">取 消</el-button>
          <el-button type="primary" @click="submit('formInline')">确 定</el-button>
        </span>
      </el-dialog>
      <!-- 绑定设备 -->
      <template>
        <sinoDialog ref="dialogGroup" title="绑定设备" @sureDialog="sureGroupDialog" @closeDialog="closeGroupDialog">
          <BindDeviceDialog ref="groupChoice" :groupList="tableData">
          </BindDeviceDialog>
        </sinoDialog>
      </template>
    </div>
    <div slot="footer">
      <el-button type="primary" @click="$router.go(-1)">返回</el-button>
    </div>
  </PageContainer>
</template>
<script>
import BindDeviceDialog from './components/BindDevice.vue'
import sinoDialog from '../../operationPort/spaceManage/common/sinoDialog.vue'
export default {
  name: 'emergencyTeam',
  components: {
    sinoDialog,
    BindDeviceDialog
  },
  data() {
    return {
      tableLoading: false,
      dialogVisible: false,
      tableData: [],
      selectionList: [], // 所有勾选项
      searchForm: {
        assetsCodeName: ''
      },
      formInline: {
        parameterList: [
          {
            identifier: '',
            metadataNameAlias: '',
            metadataTag: '',
            metadataType: '',
            parameter: '',
            product: '',
            valueType: { valueType: { type: '' } }
          }
        ],
        settingId: '',
        settingIdList: []
      },
      typeList: [],
      pagination: {
        pageSize: 15,
        pageNo: 1
      },
      pageTotal: 0,
      filterItem: [],
      troopDeviceCodeArr: [],
      tableDataTime: [
        {
          index: 0,
          content: '',
          standardRequirements: '',
          inspectionBasis: ''
        }
      ],
      // 自定义日期范围
      timeList: [],
      pickerDates: {
        disabledDate: (time) => {
          return time.getTime() < new Date(this.timeList[0]).getTime() || time.getTime() > new Date(this.timeList[1]).getTime()
        }
      },

      rules: {
        troopTypeCode: [{ required: true, message: '请选择队伍类型', trigger: 'change' }]
      },
      troopTypeList: [],
      isShow: false,
      originalValue: null,
    }
  },
  mounted() {

    this.getTableData()
  },
  methods: {
    // 批量删除
    batchDel() {
      const idArr = this.selectionList.map((i) => i.settingId)
      let obj = {
        ids: idArr
      }
      this.$confirm('删除后将无法恢复，是否确定删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(() => {
          this.tableLoading = true
          this.$api.getQueryDeleteBindAssets(obj).then((res) => {
            if (res.code == '200') {
              this.$message({
                type: 'success',
                message: '删除成功'
              })
              this.getTableData()
            }
          })
        })
        .catch(() => { })
    },
    // 设定值选择
    handleSelectSetValue(value, index) {
      const item = this.formInline.parameterList[index]
      item.parameterJson = JSON.stringify({ [item.valueType.id]: value })
    },
    // 批量隔离
    batchStatus(type) {
      const idArr = this.selectionList.map((i) => i.settingId)
      let obj = {
        ids: idArr,
        enableStatus: null //0取消1隔离
      }
      if (type === 'quarantine') {
        obj.enableStatus = 1
      } else if (type === 'noQuarantine') {
        obj.enableStatus = 0
      }
      this.$api.getupdateBindAssetsStatus(obj).then((res) => {
        if (res.code == '200') {
          this.$message.success(res.message)
          this.getTableData()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 批量执行
    batchExecute() {
      const idArr = this.selectionList.map((i) => i.settingId)
      let obj = {
        ids: idArr
      }
      this.$api.getQueryAssetsExecuteBatch(obj).then((res) => {
        if (res.code == '200') {
          this.$message.success(res.message)
          this.getTableData()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 批量设定
    batchSetting() {
      this.formInline = {
        parameterList: [
          {
            identifier: '',
            metadataNameAlias: '',
            metadataTag: '',
            metadataType: '',
            parameter: '',
            product: '',
            valueType: { valueType: { type: '' } }
          }
        ],
        settingId: '',
        settingIdList: []
      }
      const idArr = this.selectionList.map((i) => i.settingId)
      let hasDifferentSysOf1 = true
      idArr.forEach((item) => {
        const matchingItem = this.selectionList.find((selection) => selection.settingId === item)
        if (matchingItem && matchingItem.sysOf1 !== this.selectionList[0].sysOf1) {
          this.$message.error('请选择相同类型的设备进行批量设定')
          hasDifferentSysOf1 = false
          return
        } else {
          hasDifferentSysOf1 = true
        }
      })
      if (hasDifferentSysOf1) {
        let obj = {
          ids: idArr
        }
        this.formInline.settingIdList = idArr
        this.$api.getQueryAssetsFunctionsList(obj).then((res) => {
          if (res.code == '200') {
            this.dialogVisible = true
            res.data.map((item) => {
              item.valueType = JSON.parse(item.valueType)
            })
            this.filterItem = res.data
          } else {
            this.$message.error(res.message)
          }
        })
      }
    },
    onSortOrderBlur(id, newValue, originalValue) {
      // 仅在值更改时调用 inputClick
      if (newValue !== originalValue) {
        this.inputClick(id, newValue);
      }
    },
    // table排序编辑
    inputClick(id, value) {
      let obj = {
        settingId: id,
        sortOrder: value
      }
      this.$api.getQueryUpdateSortOrder(obj).then((res) => {
        if (res.code == '200') {
          this.$message.success(res.message)
          this.getTableData()
          this.tableLoading = false
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 控制设定参数
    getQueryAssetsByIdFunctionsList(id) {
      this.$api.getQueryAssetsByIdFunctionsList({ settingId: id }).then((res) => {
        if (res.code === '200') {
          const filteredItems = res.data.filter((item) => {
            try {
              if (typeof item.valueType === 'string') {
                const parsedValueType = JSON.parse(item.valueType);
                return typeof parsedValueType === 'object' && parsedValueType !== null && Object.keys(parsedValueType).length > 0;
              } else {
                return false;
              }
            } catch (e) {
              return false;
            }
          });
          const jsonFilteredItems = filteredItems.map(item => {
            return {
              ...item,
              valueType: JSON.parse(item.valueType) // 将 valueType 解析成对象后再转换为 JSON 字符串
            };
          });
          this.filterItem = jsonFilteredItems; // 保存转换后的数据
        }
      });
    },
    getAssetParameters(id) {
      this.$api.getAssetParameters({ settingId: id }).then((res) => {
        if (res.code === '200') {
          res.data.map((item) => {
            item.valueType = JSON.parse(item.valueType)
          })

          this.formInline.parameterList = res.data
          if (this.formInline.parameterList.length) {
            this.isShow = true
          } else {
            this.isShow = false
          }

        }
      })
    },
    selectChange(val, index) {
      const selectedItem = this.filterItem.find((item) => item.identifier == val);
      if (!selectedItem) {
        console.error('未找到匹配项:', val);
        return;
      }
      this.formInline.parameterList[index].metadataNameAlias = selectedItem.metadataNameAlias;
      this.formInline.parameterList[index].factoryCode = selectedItem.factoryCode;
      this.formInline.parameterList[index].identifier = selectedItem.identifier;
      this.formInline.parameterList[index].assetsInfoId = selectedItem.assetsInfoId;
      this.formInline.parameterList[index].parameter = '';
      this.formInline.parameterList[index].parameterJson = '';
      if (selectedItem.valueType && selectedItem.valueType.valueType) {
        this.formInline.parameterList[index].valueType = selectedItem.valueType;
      }
    },
    controlSure(type, index) {
      if (type === 'add') {
        this.formInline.parameterList.push({
          identifier: '',
          metadataNameAlias: '',
          metadataTag: '',
          factoryCode: '',
          metadataType: '',
          parameter: '',
          product: '',
          valueType: { valueType: { type: '' } }
        });
      } else if (type == 'delete') {
        this.formInline.parameterList.splice(index, 1)
      }
    },
    // table多选
    handleSelectionChange(val) {
      this.selectionList = val
    },

    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.pagination.pageNo = 1
      this.getTableData()
    },
    getTableData() {
      this.tableLoading = true
      let data = {
        ...this.pagination,
        ...this.searchForm,
        systemCode: this.$route.query.systemCode,
        cronTimeId: this.$route.query.cronTimeId
      }
      this.tableData = []
      this.$api
        .getQueryAssetsLinked(data)
        .then((res) => {
          this.tableLoading = false
          if (res.code == 200) {
            this.tableData = res.data ? res.data.records : []
            this.pageTotal = res.data ? res.data.total : 0
          } else if (res.message) {
            this.pagination.total = 0
            this.$message.error(res.message)
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },

    search() {
      this.pagination.pageNo = 1
      this.getTableData()
    },
    reset() {
      this.searchForm = {
        assetsCodeName: ''
      }
      this.pagination.pageNo = 1
      this.pageTotal = 0
      this.getTableData()
    },
    handleCurrentChange(val) {
      this.pagination.pageNo = val
      this.getTableData()
    },
    closeGroupDialog() {
      this.$refs.dialogGroup.dialogTableVisible = false
    },
    sureGroupDialog() {
      let obj = {
        assetsIds: [],
        systemCode: this.$route.query.systemCode,
        cronTimeId: this.$route.query.cronTimeId
      }
      const selectedIds = this.$refs.groupChoice.multipleSelection.map((item) => item.id)
      obj.assetsIds = selectedIds
      this.$api.getQueryAssetsBindAssets(obj).then((res) => {
        if (res.code === '200') {
          this.$refs.dialogGroup.dialogTableVisible = false
          this.getTableData()
        }
      })
    },
    handleListEvent(type, id) {
      if (type == 'add') {
        this.$refs.dialogGroup.dialogTableVisible = true
        this.$refs.dialogGroup.getSelectData()
      }
      if (type == 'controlSetting') {
        //控制设定
        this.dialogVisible = true
        this.formInline.settingId = id
        this.getAssetParameters(id)
        this.getQueryAssetsByIdFunctionsList(id)
      }
      if (type === 'execute') {
        //执行
        this.tableLoading = true
        this.$api.getQueryExecuteById({ id: id }).then((res) => {
          if (res.code == '200') {
            this.$message({
              type: 'success',
              message: '执行成功'
            })
            this.getTableData()
            this.tableLoading = false
          }
        })
      }
      if (type == 'del') {
        this.$confirm('删除后将无法恢复，是否确定删除？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        })
          .then(() => {
            this.tableLoading = true
            this.$api.getQueryDeleteBindAssetById({ id: id }).then((res) => {
              if (res.code == '200') {
                this.$message({
                  type: 'success',
                  message: '删除成功'
                })
                this.getTableData()
                this.tableLoading = false
              }
            })
          })
          .catch(() => { })
      }
    },
    // 确定
    submit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.formInline.parameterList.map((item) => {
            item.metadataTag = this.filterItem.find((j) => {
              return j.identifier == item.identifier
            }).metadataTag
            item.factoryCode = this.filterItem.find((j) => {
              return j.identifier == item.identifier
            }).factoryCode
            item.metadataType = this.filterItem.find((j) => {
              return j.identifier == item.identifier
            }).metadataType
            item.product = this.filterItem.find((j) => {
              return j.identifier == item.identifier
            }).product
            item.valueType = JSON.stringify(item.valueType)
          })
          this.$api.getQuerySaveFunctionsList(this.formInline).then((res) => {
            if (res.code === '200') {
              this.$message.success(res.message)
              this.getTableData()
            } else {
              this.$message.error(res.message)
            }
          })
          this.dialogVisible = false
          this.formInline = {
            parameterList: [
              {
                identifier: '',
                metadataNameAlias: '',
                metadataTag: '',
                metadataType: '',
                parameter: '',
                product: '',
                valueType: { valueType: { type: '' } }
              }
            ],
            settingId: '',
            settingIdList: []
          }
        } else {
          return false
        }
      })
    },
    // 取消
    dialogClosed() {
      this.dialogVisible = false
      this.$refs.formInline.resetFields()
    }
  }
}
</script>
<style lang="scss" scoped>
.searchForm {
  display: flex;
  height: 80px;
  padding: 0 16px;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .el-input {
    width: 200px;
    margin-left: 16px;
  }

  >div {
    margin-right: 20px;
  }
}

.search-box {
  display: flex;
  align-items: center;
}

.container-content>div {
  height: 100%;
  margin-top: 16px;
}

.el-table {
  margin-bottom: 12px;
}

::v-deep .el-table__row:hover {
  cursor: pointer;
}

.table-box {
  background-color: #fff;
  height: 100%;
  padding: 16px;

  .tableContainer {
    height: 100%;

    ::v-deep .el-table .el-table__body-wrapper td.el-table__cell {
      font-size: 14px;
      border-right: 1px solid #ebeef5 !important;
    }

    .el-table {
      height: calc(100% - 96px) !important;
    }
  }
}

.diaContent {
  width: 100%;
  max-height: 550px !important;
  overflow: auto;
  background-color: #fff !important;

  .el-input,
  .el-select,
  .el-cascader {
    width: 300px;
  }
}

.form-item {
  display: inline-block;
  margin-right: 20px;
}

.inputWidth {
  width: 820px;
}

.ml-16 {
  margin-left: 16px;
}

.dialog .el-dialog {
  width: 60% !important;
}
</style>
