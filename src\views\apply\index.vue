<template>
  <PageContainer class="applyCenter">
    <div slot="header" class="control-btn-header">
      <div class="search-from clear">
        <p class="fl">应用中心</p>
        <el-input v-model="searchFrom.shortName" class="fl" placeholder="搜索应用" suffix-icon="el-icon-search" :disabled="isEdit" @input="searchForm" />
        <div class="search-btn fr">
          <el-button v-if="!isEdit" type="primary" @click="configApply">配置</el-button>
          <el-button
            v-if="isEdit"
            type="primary"
            plain
            @click="
              () => {
                isEdit = false
                getMyApplyList()
              }
            "
          >取消</el-button
          >
          <el-button v-if="isEdit" type="primary" @click="configMyApply">完成</el-button>
        </div>
      </div>
      <p v-if="isEdit" class="control-tips">长按可拖动调整顺序</p>
      <applyAddDialog v-if="isApplyAddDialog" :visible.sync="isApplyAddDialog" :selectAppList="applyList" @selectAppFinish="selectAppFinish" />
    </div>
    <draggable slot="content" v-model="applyList" v-loading="loading" v-bind="dragOptions" handle=".apply-item" class="applyCenter-main" @update="sortUpdate">
      <transition-group class="apply-list">
        <div
          v-for="item in applyList"
          :key="item.id"
          class="apply-item"
          :class="{ 'apply-acitve': !isEdit }"
          :style="{ cursor: isEdit ? 'move' : 'pointer' }"
          @click="openApply(item)"
        >
          <div class="apply-iconBox">
            <img class="apply-defaultIcon" :src="urlTransEvent(item.icon, 'defaultIcon')" :alt="item.fullName" :onerror="defaultImg" />
            <img class="apply-activeIcon" :src="urlTransEvent(item.icon, 'activeIcon')" :alt="item.fullName" :onerror="defaultImg" />
          </div>
          <p class="apply-name">{{ item.shortName }}</p>
        </div>
        <div
          v-if="isEdit"
          key="add"
          class="apply-add"
          @click="
            () => {
              isApplyAddDialog = true
            }
          "
        >
          <svg-icon name="addApply" class="add-apply-icon" />
          <p class="apply-add-text">添加</p>
        </div>
      </transition-group>
    </draggable>
  </PageContainer>
</template>
<script>
import defaultImgIm from '@/assets/images/staging/space-manage.png'
import qs from 'qs'
import { debounce } from 'lodash/function'
import { sortBy } from 'lodash/collection'
import draggable from 'vuedraggable'
import applyAddDialog from './components/applyAddDialog.vue'
import { mapMutations } from 'vuex'
import { childAppWhite } from '@/util/dict.js'
export default {
  name: 'applyCenter',
  components: {
    draggable,
    applyAddDialog
  },
  data() {
    return {
      childAppWhite,
      defaultImg: 'this.src="' + defaultImgIm + '"',
      loading: false,
      isApplyAddDialog: false,
      isEdit: false, // 是否配置
      applyList: [],
      searchFrom: {
        shortName: '' // 应用名称
      }
    }
  },
  computed: {
    dragOptions() {
      return {
        animation: 200,
        disabled: false,
        scroll: true,
        ScrollSensitivity: 100, //
        sort: this.isEdit
      }
    }
  },
  created() {
    this.getMyApplyList()
  },
  methods: {
    ...mapMutations(['childAppsData/setPath']),
    urlTransEvent(srcObjStr, field) {
      try {
        return this.$tools.imgUrlTranslation(JSON.parse(srcObjStr)[field])
      } catch {
        return ''
      }
    },
    openApply(item) {
      if (this.isEdit) return
      // 子模块
      if (item.type == 2) {
        if (this.$auth(item.path)) {
          // 子应用地址白名单，如果跳转目标是子应用的话，需要走这里
          let pathArr = item.path.split('/')
          if (this.childAppWhite.includes(pathArr[2])) {
            this.$store.commit('childAppsData/setChildAppInfo', { parentName: `/${pathArr[1]}`, currentPath: pathArr[2] })
          }
          this.$router.push(item.path)
        } else {
          this.$message({ message: '无操作权限，请于管理员联系。', type: 'warning' })
        }
        // 能耗 oauth单独处理
      } else if (item.path.includes(process.env.VUE_APP_ENERGY_API) || process.env.VUE_APP_ENERGY_API.includes(item.path)) {
        // http://*************:29011/api/oauth/authorize?response_type=code&client_id=szzlyy&scope=&state=&redirect_url=http://localhost:9000/oauth
        const queryData = {
          response_type: 'code',
          client_id: oAuthData.clientId,
          scope: '',
          redirect_url: window.location.origin + '/oauth'
        }
        const path = __PATH.VUE_ENERGY_API + 'oauth/authorize?' + qs.stringify(queryData)
        window.open(path, '_blank', 'toolbar=no,location=no,status=no,menubar=no,scrollbars=yes,resizable=yes,width=600,height=500,right=24,top=24')
      } else {
        // 子应用或第三方系统
        const sessionId = 'sessionId=' + (this.$store.state.user.userInfo.user.sessionId || '')
        const path = item.path.includes('?') ? item.path + '&' + sessionId : item.path + '?' + sessionId
        window.open(path)
      }
    },
    configApply() {
      this.isEdit = true
      this.searchFrom.shortName = ''
      this.getMyApplyList()
    },
    // 保存
    configMyApply() {
      let appConfig = []
      let staffId = this.$store.state.user.userInfo.user.staffId
      this.applyList.forEach((item) => {
        appConfig.push({
          appId: item.id,
          sortNum: item.sortNum,
          userId: staffId
        })
      })
      this.$api.ConfigMyApply({ appConfig }).then((res) => {
        if (res.code == 200) {
          this.$message({ message: '配置成功', type: 'success' })
          this.isEdit = false
          this.getMyApplyList()
        } else {
          this.$message({ message: res.msg, type: 'error' })
        }
      })
    },
    // 排序变更
    sortUpdate(e) {
      if (e.newIndex == this.applyList.length) return
      this.applyList[e.newIndex].sortNum = e.newIndex
      this.applyList[e.oldIndex].sortNum = e.oldIndex
    },
    // 查询
    searchForm: debounce(function () {
      this.getMyApplyList()
    }, 500),
    // 选择应用完成
    selectAppFinish(selectAppList) {
      let oldSort = {}
      let soltApply = []
      let newApply = []
      this.applyList.forEach((item) => {
        oldSort[item.id] = item.sortNum
      })
      selectAppList.forEach((item) => {
        if (oldSort[item.id]) {
          item.sortNum = oldSort[item.id]
        }
        soltApply.push(item)
      })
      newApply = sortBy([...soltApply, ...newApply], 'sortNum')
      newApply.forEach((item, index) => {
        item.sortNum = index
      })
      this.applyList = newApply
    },
    // 获取我的应用列表
    getMyApplyList() {
      this.loading = true
      this.$api
        .GetMyApplyList({ userId: this.$store.state.user.userInfo.user.staffId, ...this.searchFrom })
        .then((res) => {
          this.loading = false
          if (res.code == 200) {
            this.applyList = res.data ? res.data : []
          } else {
            this.$message({ message: res.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.loading = false
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.applyCenter {
  background: #fff;
  border-radius: 4px;
  p {
    margin: 0;
  }
}
.control-btn-header {
  width: 100%;
  padding: 2.4% 3% 0;
  .search-from {
    width: 100%;
    p {
      font-size: 34px;
      color: #121f3e;
      line-height: 40px;
      margin-right: 40px;
    }
    ::v-deep .el-input {
      width: 27%;
      .el-input__inner {
        height: 40px;
      }
      .el-input__icon {
        height: 40px;
        line-height: 40px;
        font-size: 16px;
      }
    }
    .search-btn {
      height: 40px;
      display: flex;
      align-items: center;
    }
  }
  .control-tips {
    font-size: 14px;
    color: #414653;
    padding: 10px 0 0;
  }
  .fl {
    float: left;
  }
  .fr {
    float: right;
  }
  .clear::after {
    content: '.';
    display: block;
    clear: both;
    height: 0;
    overflow: hidden;
    visibility: hidden;
  }
}
.applyCenter-main {
  height: 100%;
  padding: 2.4% 3% 2.4% calc(3% - 32px);
  .apply-list {
    /* overflow-x: hidden;
    overflow-y: auto; */
    overflow: auto;
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    height: 100%;
    .apply-item,
    .apply-add {
      margin: 0 0 32px 32px;
      width: calc(100% / 5 - 32px);
      height: calc(100% / 3 - 32px);
      min-width: 200px;
      min-height: 150px;
      background: #faf9fc;
      border-radius: 4px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
    .apply-add {
      cursor: pointer;
    }
    .add-apply-icon {
      font-size: 72px;
    }
    .apply-add-text {
      font-size: 18px;
      color: #ccced3;
      margin-top: 16px;
    }
    .apply-iconBox {
      position: relative;
      width: 72px;
      height: 72px;
      img {
        width: 100%;
        height: 100%;
        position: absolute;
      }
    }
    .apply-defaultIcon {
      transition: opacity 0.3s;
      opacity: 1;
    }
    .apply-activeIcon {
      transition: opacity 0.3s;
      opacity: 0;
    }
    .apply-name {
      margin-top: 16px;
    }
    .apply-acitve {
      transition: all 0.3s;
      &:hover {
        background-color: #fff;
        box-shadow: 0 4px 12px 0 rgb(18 31 62 / 12%);
        .apply-defaultIcon {
          opacity: 0;
        }
        .apply-activeIcon {
          opacity: 1;
        }
      }
    }
  }
}
</style>
