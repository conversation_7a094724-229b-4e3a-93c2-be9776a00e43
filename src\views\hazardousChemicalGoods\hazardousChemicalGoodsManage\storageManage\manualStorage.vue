<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="content_box">
        <el-form ref="formRef" :model="formModel" :rules="rules" label-width="auto" :disabled="isDisabled" class="formRef">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="入库单号" prop="recordNumber">
                <el-input v-model="formModel.recordNumber" placeholder="系统自动生成" disabled> </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="入库类型" prop="inwarehouseType">
                <el-select v-model="formModel.inwarehouseType" filterable placeholder="请选择">
                  <el-option v-for="item in inwarehouseTypeOptions" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="入库仓库" prop="warehouseId">
                <el-select v-model="formModel.warehouseId" filterable placeholder="请选择入库仓库" @change="warehouseChange">
                  <el-option v-for="item of warehouseOptions" :key="item.id" :label="item.warehouseName" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="入库时间" prop="createTime">
                <el-date-picker
                  v-model="formModel.createTime"
                  type="datetime"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="系统自动生成"
                  :picker-options="pickerOptions"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="入库人" prop="person">
                <el-input v-model="formModel.person" placeholder="默认库房管理员" filterable disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="验收人" prop="acceptorId">
                <el-select v-model="formModel.acceptorId" placeholder="验收人" clearable filterable @change="acceptorChange">
                  <el-option v-for="item of acceptorOptions" :key="item.id" :label="item.staffName" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="供应商">
                <el-select v-model="formModel.supplierId" placeholder="请选择供应商" @change="supplierChange" filterable v-selectLoadmore="loadmore">
                  <el-option v-for="item in supplierOptions" :key="item.id" :label="item.unitsName" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="上传">
                <el-button el-button type="primary" @click="uploadDialogShow = true">资料</el-button>
                <span style="color: #3b89f8; cursor: pointer; margin-left: 20px">已传{{ fileEcho.length || 0 }} 个文件</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="备注" prop="remarks">
                <el-input v-model="formModel.remarks" placeholder="请输入备注"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <div class="inventoryInfo">
            <div class="inventoryInfo_title">
              <span class="green_line"></span>
              危化品明细
              <el-button type="primary" icon="el-icon-plus" @click="onOperate('add')"> 添加危化品 </el-button>
              <el-button type="primary" @click="onOperate('select')"> 选择危化品 </el-button>
            </div>
          </div>
          <div class="manualStorage_table">
            <el-table v-loading="tableLoadingStatus" height="100%" style="width: 100%" :data="tableData" border stripe class="tableAuto" row-key="id">
              <el-table-column prop="materialTypeName" label="危化品分类" show-overflow-tooltip> </el-table-column>
              <!-- <el-table-column prop="materialCode" label="危化品编码" show-overflow-tooltip></el-table-column> -->
              <el-table-column prop="materialName" label="危化品名称" show-overflow-tooltip></el-table-column>
              <el-table-column prop="model" label="规格型号" show-overflow-tooltip></el-table-column>
              <el-table-column prop="basicUnitName" label="基础单位" show-overflow-tooltip></el-table-column>
              <el-table-column prop="operateCount" label="入库数量" width="180">
                <template #default="{ row }">
                  <el-input-number v-model="row.operateCount" :min="0" :precision="0" size="small"> </el-input-number>
                </template>
              </el-table-column>
              <!-- <el-table-column prop="unitPrice" label="进货单价" width="180">
                <template #default="{ row }">
                  <el-input v-model="row.unitPrice" placeholder="进货单价" oninput="if(isNaN(value)) { value =
               null } if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)}"> </el-input>
                </template>
              </el-table-column> -->
              <el-table-column prop="serviceLife" label="有效期" width="180">
                <template #default="{ row }">
                  <el-date-picker v-model="row.serviceLife" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="选择日期"> </el-date-picker>
                </template>
              </el-table-column>
              <!-- <el-table-column prop="trademark" label="品牌" show-overflow-tooltip></el-table-column> -->
              <!-- <el-table-column prop="supplierName" label="供应商" show-overflow-tooltip></el-table-column> -->
              <el-table-column prop="manufacturerName" label="生产厂家" show-overflow-tooltip></el-table-column>
              <el-table-column label="操作">
                <template #default="{ row }">
                  <el-button type="text" :class="[isDisabled ? '' : 'text-red']" @click="deleteData(row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-form>
      </div>
      <!--选择危化品 -->
      <template v-if="sectionDialogShow">
        <selectHazardousChemicalDialog
          :sectionDialogShow="sectionDialogShow"
          @submitSectionDialog="submitSectionDialog"
          :warehouseType="'1'"
          :warehouseId="formModel.warehouseId"
          @closeSectionDialog="closeSectionDialog"
          :selectHcsList="selectHcsList"
        />
      </template>
      <!--上传 -->
      <template v-if="uploadDialogShow">
        <uploadDialog
          :uploadDialogShow="uploadDialogShow"
          :fileArr="fileEcho"
          :fileName="formModel.fileName"
          @submitUploadDialog="submitUploadDialog"
          @closeUploadDialog="
            () => {
              uploadDialogShow = false
            }
          "
        />
      </template>
    </div>
    <div slot="footer">
      <el-button type="primary" :loading="formLoading" :disabled="!isOperation" @click="submitForm(1)">暂存</el-button>
      <el-button type="primary" :loading="formLoading" :disabled="!isOperation" @click="submitForm(2)">入库单确认</el-button>
      <el-button type="primary" plain :disabled="isOperation" @click="operating('print')">打印入库单</el-button>
      <el-button type="primary" :disabled="isOperation" @click="operating('again')">再次入库</el-button>
    </div>
  </PageContainer>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin'
import moment from 'moment'
export default {
  name: 'manualStorage',
  mxins: [tableListMixin],
  components: {
    selectHazardousChemicalDialog: () => import('../components/selectHazardousChemicalDialog.vue'),
    uploadDialog: () => import('../components/uploadDialog.vue')
  },
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      },
      sectionDialogShow: false,
      // 正常表单
      formModel: {
        recordNumber: '', // 入库单号
        inwarehouseType: '', // 入库类型
        warehouseId: '', // 入库仓库id
        warehouseName: '', // 入库仓库name
        createTime: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'), // 入库时间
        person: '', // 入库人
        acceptorId: '', // 验收人id
        acceptorName: '', // 验收人name
        supplierId: '', // 供应商
        supplierName: '', // 供应商name
        remarks: '', // 备注
        file: [], //文件
        fileName: '' //文件名称
      },
      rules: {
        inwarehouseType: [{ required: true, message: '请选择入库类型', trigger: 'change' }],
        warehouseId: [{ required: true, message: '请选择入库仓库', trigger: 'change' }],
        acceptorId: [{ required: true, message: '请选择验收人', trigger: 'change' }]
      },
      inwarehouseTypeOptions: [], // 入库类型下拉
      supplierOptions: [], // 供应商下拉
      supplierCurrentPage: 1, //供应商分页
      warehouseOptions: [], // 入库仓库下拉
      tableLoadingStatus: false,
      acceptorOptions: [], // 验收人下拉
      nodataFlag: false,
      dataBackFile: '',
      tableData: [],
      isDisabled: false,
      uploadDialogShow: false,
      fileEcho: [],
      isOperation: true, // 操作
      formLoading: false, // 保存loading
      selectHcsList: [] //选中危化品数据
    }
  },
  mounted() {
    this.init()
    if (sessionStorage.getItem('hazardousChemicalBaseInfo')) {
      this.formModel = JSON.parse(sessionStorage.getItem('hazardousChemicalBaseInfo'))
      this.fileEcho = this.formModel.file
    }
    this.formModel.person = this.$store.state.user.userInfo.user.staffName
    if (this.$route.query.type === 'warehouseWarrant') {
      this.getInWarehouseData()
    }
  },
  methods: {
    // 获取入库单详情
    getInWarehouseData() {
      const userInfo = this.$store.state.user.userInfo.user
      let params = {
        id: this.$route.query.id,
        recordNumber: this.$route.query.recordNumber,
        userId: userInfo.staffId,
        userName: userInfo.staffName
      }
      this.$api.getInwarehouseRecordById(params).then((res) => {
        if (res.code == '200') {
          this.formModel = res.data
          if (res.data.file) {
            this.fileEcho = JSON.parse(res.data.file)
            this.formModel.fileName = res.data.name
          }
          this.getWarehouseDetailData(this.formModel.warehouseId)
          this.formModel.person = this.$store.state.user.userInfo.user.staffName
          this.tableData = res.data.materialRecordList
        }
      })
    },
    // 初始化
    init() {
      // this.getPersonListFn()
      this.getSupplierData()
      this.getWarehouseTypeDataFn()
      this.getWarehouseListFn()
    },
    // 获取库房下拉
    getWarehouseListFn() {
      this.$api.getWarehouseList({ status: '0' }).then((res) => {
        if (res.code == '200') {
          this.warehouseOptions = res.data.list
          if (res.data.list.length > 0) {
            this.formModel.warehouseId = res.data.list[0].id
            this.formModel.warehouseName = res.data.list[0].warehouseName
            this.getWarehouseDetailData(res.data.list[0].id)
          }
        }
      })
    },
    // 获取供应商数据
    getSupplierData() {
      let params = {
        unitsTypeCode: 3,
        category: 2,
        userId: this.$store.state.user.userInfo.user.staffId,
        userName: this.$store.state.user.userInfo.user.staffName,
        status: '0',
        currentPage: this.supplierCurrentPage,
        pageSize: 15
      }
      this.$api.hscUnitManger(params).then((res) => {
        if (res.code == 200) {
          this.supplierOptions = [...this.supplierOptions, ...res.data.list]
          if (this.supplierOptions.length) {
            this.formModel.supplierId = this.supplierOptions[0].id
            this.formModel.supplierName = this.supplierOptions[0].unitsName
          }
        }
      })
    },
    /** 供应商懒加载 */
    loadmore() {
      this.supplierCurrentPage++
      this.getSupplierData()
    },
    // 供应商change
    supplierChange(val) {
      if (val) {
        this.formModel.supplierName = this.supplierOptions.find((item) => item.id == val).unitsName
      }
    },
    // 仓库选择取仓库信息
    warehouseChange(val) {
      if (val) {
        this.formModel.warehouseName = this.warehouseOptions.find((item) => item.id == val).warehouseName
        this.getWarehouseDetailData(val)
      }
    },
    //获取库房详情
    getWarehouseDetailData(e) {
      const userInfo = this.$store.state.user.userInfo.user
      let params = {
        userId: userInfo.staffId,
        userName: userInfo.staffName,
        id: e
      }
      this.$api.getWarehouseById(params).then((res) => {
        if (res.code == '200') {
          let { manageUserArrayStr } = res.data
          if (manageUserArrayStr) {
            this.acceptorOptions = JSON.parse(manageUserArrayStr).map((item) => {
              return {
                staffName: item.manageName,
                id: item.manageCode
              }
            })
            this.formModel.acceptorId = this.acceptorOptions[0].id
            this.formModel.acceptorName = this.acceptorOptions[0].staffName
          }
        }
      })
    },
    // 获取仓库类型
    getWarehouseTypeDataFn() {
      let params = {
        userId: this.$store.state.user.userInfo.user.staffId,
        userName: this.$store.state.user.userInfo.user.staffName,
        typeCode: 'RKLX',
        status: '0'
      }
      this.$api.getWarehouseType(params).then((res) => {
        this.inwarehouseTypeOptions = res.data
        this.formModel.inwarehouseType = this.inwarehouseTypeOptions[0].id
      })
    },
    // // 获取人员列表
    // getPersonListFn() {
    //   let params = {
    //     current: 1,
    //     size: 9999
    //   }
    //   this.$api.staffList(params).then((res) => {
    //     if (res.code == 200) {
    //       this.acceptorOptions = res.data.records
    //     }
    //   })
    // },
    // 验收人获取name
    acceptorChange(val) {
      if (val) {
        this.formModel.acceptorName = this.acceptorOptions.find((item) => item.id == val).staffName
      }
    },
    deleteData(val) {
      this.tableData = this.tableData.filter((obj) => obj.id !== val.id)
    },
    // 添加危化品弹窗
    closeSectionDialog() {
      this.sectionDialogShow = false
    },
    submitSectionDialog(list) {
      this.tableData = [...this.tableData, ...list]
      this.sectionDialogShow = false
    },
    onOperate(type, row) {
      if (type === 'add') {
        this.formModel.file = this.fileEcho
        sessionStorage.setItem('hazardousChemicalBaseInfo', JSON.stringify(this.formModel))
        this.$router.push({
          name: 'hazardousChemicalAdd',
          query: {
            currentType: 'manual'
          }
        })
      } else if (type === 'select') {
        this.selectHcsList = this.tableData
        this.sectionDialogShow = true
      }
    },
    // 点击确定
    submitForm(type) {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.formLoading = true
          const userInfo = this.$store.state.user.userInfo.user
          this.tableData.forEach((el) => {
            el.amount = 1
          })
          let params = {
            ...this.formModel,
            userId: userInfo.staffId,
            userName: userInfo.staffName,
            materialRecordArrayStr: JSON.stringify(this.tableData),
            status: type || 1,
            amount: 1,
            name: this.formModel.fileName
          }
          if (this.fileEcho && this.fileEcho.length) {
            params.file = JSON.stringify(this.fileEcho)
          } else {
            params.file = ''
          }
          delete params.materialRecordList
          delete params.fileName
          this.$api.saveInwarehouse(params).then((res) => {
            this.formLoading = false
            if (res.code == '200') {
              this.isOperation = false
              this.isDisabled = true
              this.formModel.recordNumber = res.data.recordNumber
              this.$message.success(res.message)
            } else {
              this.$message.error(res.message)
            }
          })
        }
      })
    },
    // 打印和再次上传
    operating(type) {
      if (type === 'print') {
        this.$router.push({
          name: 'warehousingEntry',
          query: {
            currentType: 'manual',
            inWarehouseId: this.formModel.recordNumber
          }
        })
      } else if (type === 'again') {
        this.isOperation = true
        this.isDisabled = false
        this.$refs.formRef.resetFields()
      }
    },
    // 上传文件
    submitUploadDialog(value) {
      if (value.fileList && value.fileList.length) {
        this.fileEcho = value.fileList
      }
      this.formModel.fileName = value.fileName
      this.uploadDialogShow = false
    }
  }
}
</script>
<style scoped lang="scss">
.container-content > div {
  background-color: #fff;
  height: 100%;
  position: relative;

  .content_box {
    padding: 0 24px;
    background: #fff;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    height: 100%;

    .formRef {
      height: 100%;
      width: 100%;
    }

    .inventoryInfo {
      display: flex;
      height: 40px;
      line-height: 40px;
      margin-bottom: 8px;
      align-items: center !important;

      .inventoryInfo_title {
        font-size: 16px;

        .el-button {
          margin-left: 20px;
        }

        .green_line {
          display: inline-block;
          width: 8px;
          height: 8px;
          border-radius: 2px;
          background: #3562db;
          margin-right: 10px;
          vertical-align: middle;
        }
      }
    }

    .manualStorage_table {
      margin-top: 10px;
      height: calc(100% - 280px) !important;
    }

    .text-red {
      color: #ff1919;
    }
  }

  .el-input,
  .el-select,
  .el-cascader {
    width: 80%;
  }
}
</style>
