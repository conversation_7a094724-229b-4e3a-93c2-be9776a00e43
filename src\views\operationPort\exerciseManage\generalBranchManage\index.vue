<template>
  <PageContainer>
    <div slot="content" class="generalBranchManage-content">
      <div class="generalBranchManage-content-left">
        <div class="toptip">
          <div> <span class="green_line"></span>
            总支名称</div>
          <div> <el-button type="text" size="mini" @click="() => handleBranch('add')">
              添加
            </el-button></div>
        </div>
        <div v-loading class="left_content">
          <el-tree ref="tree" v-loading="treeLoading" style="margin-top: 10px;" :data="treeData" :props="defaultProps"
            node-key="id" :highlight-current="true" @node-click="handleNodeClick">
            <span slot-scope="{ node, data }" class="custom-tree-node">
              <el-tooltip :content="node.label+'（'+data.pidCount+'）'" placement="top-start" effect="dark">
                <span class="nodeLabel">{{ node.label }}（{{data.pidCount}}）</span>
              </el-tooltip>
              <span class="nodeOperation">
                <el-button type="text" size="mini" @click="() => handleBranch('edit',node)">
                  编辑
                </el-button>
                <el-button type="text" size="mini" @click="() => handleBranch('del',node)">
                  删除
                </el-button>
              </span>
            </span></el-tree>
        </div>
      </div>
      <div class="generalBranchManage-content-right">
        <div style="height: 100%;">
          <div class="search-from">
            <div>
              <el-input v-model.trim="filters.orgName" placeholder="请输入部门名称" style="width: 200px" maxlength="20"
                clearable></el-input>
              <el-button type="primary" plain class="ml-16" @click="resetForm">重置</el-button>
              <el-button type="primary" @click="searchForm">查询</el-button>
            </div>
            <div>
              <el-button type="primary" icon="el-icon-plus" @click="handleListEvent('add')">添加科室</el-button>
              <el-button type="primary" :disabled="multipleSelection.length<1" @click="moveBranch">移动到总支</el-button>
              <el-button type="primary" :disabled="multipleSelection.length<1" @click="batchDelete">批量删除</el-button>
            </div>
          </div>
          <div class="contentTable">
            <div class="contentTable-main table-content">
              <el-table v-loading="tableLoading" border style="width: 100%;" height="calc(100% - 10px)"
                :data="tableData" stripe @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55">
                </el-table-column>
                <el-table-column prop="orgName" label="部门名称" show-overflow-tooltip></el-table-column>
                <el-table-column show-overflow-tooltip label="操作" width="200" align="center">
                  <template slot-scope="scope">
                    <el-button type="text" @click="handleListEvent('edit', scope.row)">编辑</el-button>
                    <el-button type="text" @click="handleListEvent('del',scope.row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="contentTable-footer">
              <el-pagination :current-page="pagination.current" :page-sizes="pagination.pageSizeOptions"
                :page-size="pagination.size" :layout="pagination.layoutOptions" :total="pageTotal"
                @size-change="paginationSizeChange" @current-change="paginationCurrentChange">
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
      <el-dialog v-dialogDrag :visible.sync="dialogVisible" :before-close="dialogClosed" :modal="false"
        :close-on-click-modal="false" :title="dialogTitle" width="30%" custom-class="model-dialog">
        <div class="diaContent" style="padding: 10px">
          <el-form ref="formInline" :model="formInline" label-width="100px" :rules="rules" label-position="right">
            <el-form-item v-if="condition==='branch'" key="orgName" label="总支名称" prop="orgName">
              <el-input v-model.trim="formInline.orgName" placeholder="请输入总支名称" :maxlength="20"
                style="width: 260px"></el-input>
            </el-form-item>
            <el-form-item v-if="condition==='dept'" key="dept" label="选择科室" prop="userDepartmentId">
              <el-select ref="userDepartmentId" v-model="formInline.userDepartmentId" placeholder="请选择科室" clearable
                :multiple="!selectBtn" style="width: 260px" filterable @change="selectDept">
                <el-option v-for="item in useDeptList" :key="item.id" :label="item.deptName" :disabled="item.disabled"
                  :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item v-if="condition==='moveBranch'" key="moveBranch" label="移动到总支" prop="branch">
              <el-select ref="userDepartmentId" v-model="formInline.branch" placeholder="选择总支" clearable
                style="width: 260px" filterable>
                <el-option v-for="item in branchList" :key="item.id" :label="item.orgName" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="dialogClosed">取 消</el-button>
          <el-button type="primary" @click="submit('formInline')">确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </PageContainer>
</template>
<script lang="jsx">
import tableListMixin from '@/mixins/tableListMixin.js'
import { transData } from '@/util'
export default {
  name: 'generalBranchManage',
  mixins: [tableListMixin],
  data() {
    return {
      defaultProps: {
        label: 'orgName',
        value: 'id'
      },
      checkedData: {},
      treeData: [],
      useDeptList: [],
      branchList: [],
      dialogTitle: '',
      tableLoading: false,
      treeLoading: false,
      dialogVisible: false,
      filters: {
        orgName: ''
      },
      formInline: {
        orgName: '',
        userDepartmentId: '',
        userDepartmentName: '',
        branch: ''
      },
      pagination: {
        current: 1,
        size: 15
      },
      pageTotal: 0,
      rules: {
        orgName: [{ required: true, message: '请输入总支名称', trigger: 'blur' }],
        userDepartmentId: [{ required: true, message: '请选择科室', trigger: 'blur' }],
        branch: [{ required: true, message: '请选择总支', trigger: 'blur' }]
      },
      tableData: [],
      multipleSelection: [],
      condition: '',
      allDept: [],
      selectedDeptData: [],
      personData: [],
      deptId: '',
      rowId: '',
      selectBtn: false,
      selectId: ''
    }
  },
  mounted() {
    this.getTreeData()
  },
  methods: {
    getTreeData() {
      this.treeLoading = true
      this.$api.getGeneralist({}).then((res) => {
        this.treeLoading = false
        if (res.code == 200) {
          this.treeData = res.data
          this.branchList = res.data
          if (this.selectId) {
            this.checkedData.id = this.selectId
            this.getDataList()
            this.$nextTick(() => {
              // selectId：绑定的 node-key
              this.$refs.tree.setCurrentKey(this.selectId)
            })
          } else {
            this.checkedData =  this.treeData[0]
            this.getDataList()
            this.$nextTick(() => {
              // selectId：绑定的 node-key
              this.$refs.tree.setCurrentKey(this.checkedData.id)
            })
          }

        } else if (res.message) {
          this.$message.error(res.message)
        }
      })
    },
    getDataList() {
      let data = {
        id: this.checkedData?this.checkedData.id:"",
        page: {
          current: this.pagination.current,
          size: this.pagination.size
        },
        orgName: this.filters.orgName
      }
      this.tableLoading = true
      this.$api.getGeneraDeptData(data).then((res) => {
        if (res.code == '200') {
          this.tableData = res.data.list
          this.pageTotal = res.data.totalCount
        }
      })
      this.tableLoading = false
    },
    paginationSizeChange(val) {
      this.pagination.size = val
      this.getDataList()
    },
    paginationCurrentChange(val) {
      this.pagination.current = val
      this.getDataList()
    },
    // 查询已被选择的科室
    getSelectDept() {
      this.$api.getSelectedDeptData().then((res) => {
        if (res.code == '200') {
          this.selectedDeptData = res.data
        }
      })
    },
    // 使用科室
    getUseDeptList() {
      this.$api.getSelectedDept().then((res) => {
        if (res.code == '200') {
          this.allDept = res.data
          let deptIds = this.selectedDeptData
          deptIds.forEach(item => {
            this.allDept.forEach(el => {
              if (item.deptId === el.id) {
                el.disabled = true
              }
            })
          })
          this.useDeptList = this.allDept
        }
      })
    },
    selectDept(val) {
      if (this.selectBtn) {
        this.formInline.userDepartmentName = this.allDept.find((item) => {
          return item.id == val
        }).deptName
        this.deptId = val
      } else {
        this.personData = []
        if (val && val.length) {
          val.forEach((item) => {
            this.allDept.forEach((item2) => {
              if (item === item2.id) {
                this.personData.push(item2.deptName)
              }
            })
          })
        }
        this.deptId = val.join(',')
        this.formInline.userDepartmentName = this.personData.join(',')
      }
    },
    handleNodeClick(data) {
      this.pagination.current = 1
      this.checkedData = data
      this.$refs.tree.setCurrentKey(this.checkedData.id)
      this.getDataList()
      this.selectId = this.checkedData.id
    },
    resetForm() {
      this.filters.orgName = '',
      this.getDataList()
    },
    searchForm() {
      this.getDataList()
    },
    // 弹窗取消
    dialogClosed() {
      this.dialogVisible = false
      this.condition = ''
    },
    submit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.condition === 'branch') {
            let data = {
              orgName: this.formInline.orgName,
              orgType: '0',
              id: ''
            }
            if (this.rowId) {
              data.id = this.rowId
              this.updateData(data)
            } else {
              this.insert(data)
            }
          } else if (this.condition === 'dept') {
            let data = {
              orgName: this.formInline.userDepartmentName,
              orgType: '1',
              deptId: this.deptId,
              id: '',
              pid:this.checkedData? this.checkedData.id:""
            }
            if (this.rowId) {
              data.id = this.rowId
              this.updateData(data)
            } else {
              this.insert(data)
            }
          } else {
            let arr = this.multipleSelection
            let data = {
              id: this.formInline.branch,
              deptList: arr
            }
            this.$api.moveGeneraDeptData(data).then((res) => {
              if (res.code === '200') {
                this.$message.success(res.msg)
                this.getTreeData()
                this.dialogClosed()
              } else {
                this.$message.error(res.msg)
              }
            })
          }
        } else {
          return false
        }
      })
    },
    // 新增数据
    insert(params) {
      this.$api.insertGeneraDeptData(params, { 'operation-type': 1}).then((res) => {
        if (res.code == 200) {
          this.$message.success(res.msg)
          this.getTreeData()
          this.dialogClosed()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 更新数据
    updateData(params) {
      let header = {
        'operation-type': 2,
        'operation-id': this.rowId,
        'operation-name': this.formInline.orgName
      }
      this.$api.updateGeneraDeptData(params, header).then((res) => {
        if (res.code == 200) {
          this.$message.success(res.msg)
          this.getTreeData()
          this.dialogClosed()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    handleBranch(type, row) {
      this.condition = 'branch'
      if (type == 'add') {
        this.dialogVisible = true
        this.dialogTitle = '添加总支'
        this.formInline.orgName = ''
        this.selectId = ''
        this.rowId = ''
      }
      if (type == 'edit') {
        this.dialogTitle = '编辑总支'
        this.dialogVisible = true
        this.formInline.orgName = row.data.orgName
        this.rowId = row.data.id
      }
      if (type == 'del') {
        this.$confirm('是否删除该总支?', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let ids = []
          this.selectId = ''
          ids.push(row.data.id)
          this.$api.deleteGeneraDeptData({ids: ids}, { 'operation-type': 3, 'operation-id': row.data.id, 'operation-name': row.data.orgName }).then((res) => {
            if (res.code === '200') {
              this.$message.success('删除成功')
              this.getTreeData()
            } else {
              this.$message.warning(res.msg)
            }
          })
        })
      }
    },
    handleListEvent(type, row) {
      this.condition = 'dept'
      if (type == 'add') {
        this.getSelectDept()
        this.getUseDeptList()
        this.dialogVisible = true
        this.dialogTitle = '添加科室'
        this.formInline.userDepartmentId = ''
        this.formInline.userDepartmentName = ''
        this.rowId = '',
        this.selectBtn = false
      }
      if (type == 'edit') {
        this.getSelectDept()
        this.getUseDeptList()
        this.dialogVisible = true
        this.dialogTitle = '编辑科室'
        this.rowId = row.id
        this.selectBtn = true,
        this.formInline.userDepartmentId = row.deptId
        this.formInline.userDepartmentName = row.orgName
      }
      if (type == 'del') {
        this.$confirm('是否删除该科室?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let ids = []
          ids.push(row.id)
          this.$api.deleteGeneraDeptData({ids: ids}, { 'operation-type': 3, 'operation-id': row.id, 'operation-name': row.orgName }).then((res) => {
            if (res.code === '200') {
              this.$message.success('删除成功')
              this.getTreeData()
            } else {
              this.$message.warning(res.msg)
            }
          })
        })
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 移动到总支
    moveBranch() {
      this.dialogVisible = true
      this.condition = 'moveBranch'
      this.dialogTitle = '移动总支'
    },
    // 批量删除
    batchDelete() {
      this.$confirm('是否批量删除科室?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let ids = []
        this.multipleSelection.forEach(item => {
          ids.push(item.id)
        })
        this.$api.deleteGeneraDeptData({ids: ids}, { 'operation-type': 3}).then((res) => {
          if (res.code === '200') {
            this.$message.success('删除成功')
            this.getDataList()
          } else {
            this.$message.warning(res.msg)
          }
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: linear-gradient(to right, #d9e1f8, #fff);
}

::v-deep .el-tree-node__content {
  height: 32px;
}
.generalBranchManage-content {
  height: 100%;
  display: flex;
  .generalBranchManage-content-left {
    width: 246px;
    height: 100%;
    padding: 10px;
    background: #fff;
    border-radius: 4px;
    margin-right: 12px;
    .toptip {
      width: 100%;
      display: flex;
      justify-content: space-between;
    }
    .custom-tree-node {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 14px;
      padding-right: 8px;
      width: 100%;
      .nodeLabel {
        width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .nodeOperation {
        flex: 1;
        text-align: right;
      }
    }

    .left_content {
      height: calc(100% - 60px);
      overflow: auto;
    }
  }
  .generalBranchManage-content-right {
    height: 100%;
    min-width: 0;
    padding: 10px;
    background: #fff;
    border-radius: 4px;
    flex: 1;

    .search-from {
      display: flex;
      justify-content: space-between;
      padding-bottom: 12px;

      & > div {
        margin-right: 10px;
      }

      & > button {
        margin-top: 12px;
      }
    }

    .contentTable {
      height: calc(100% - 50px);
      display: flex;
      flex-direction: column;

      .contentTable-main {
        flex: 1;
        overflow: auto;
      }

      .contentTable-footer {
        padding: 10px 0 0;
      }
    }
  }

  .diaContent {
    width: 100%;
    max-height: 500px !important;
    overflow: auto;
    background-color: #fff !important;
  }
  .ml-16 {
    margin-left: 16px;
  }
}
</style>
