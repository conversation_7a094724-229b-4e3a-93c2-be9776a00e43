<template>
  <el-dialog v-if="dialogShow" :title="title" width="50%" :visible.sync="dialogShow" custom-class="model-dialog">
    <div style=" width: 100%; height: 100%;">
      <div slot="content" class="card-content">
        <div class="card-content-table table-content">
          <el-table ref="table" v-loading="loading" :resizable="false" border :data="tableData" height="530px"
            style="width: 100%;">
            <el-table-column prop="imsName" label="设备名称11" show-overflow-tooltip />
            <el-table-column prop="entityTypeName" label="设备类型" show-overflow-tooltip />
            <el-table-column prop="imsNo" label="设备ID" show-overflow-tooltip />
            <el-table-column prop="regionName" label="设备位置" show-overflow-tooltip />
            <el-table-column prop="status" label="设备状态">
              <template slot-scope="scope">
                <span :class="scope.row.status == '6' ? 'offLine' : 'onLine'">{{ scope.row.status == '6' ? '离线' : '在线'
                  }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="card-content-footer">
          <el-pagination :current-page="queryParams.page" :page-sizes="[10, 20, 30, 50]"
            :page-size="queryParams.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total"
            @size-change="paginationSizeChange" @current-change="paginationCurrentChange" />
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'normalDialog',
  props: {

  },
  data() {
    return {
      dialogShow: false,
      queryParams: {
        page: 1,
        pageSize: 10,
        projectCode: null,
        deviceStatus: '0,1'
      },
      total: 0,
      tableData: [],
      loading: false,
      title: ''
    }
  },
  mounted() {

  },
  methods: {
    getEchartData(data) {
      this.title = `${data.sysName}(正常)`
      this.dialogShow = true
      this.queryParams.projectCode = data.projectCode
      // this.queryParams.projectCode = 'IEMC-BroadcastTerminal'
      this.getList()
    },
    getList() {
      this.loading = true
      this.$api.OnOrOfflineRecord(this.queryParams).then(res => {
        if (res.code == 200) {
          this.tableData = res.data.list
          this.total = res.data.count
          this.loading = false
        }
      })
    },
    paginationSizeChange(val) {
      this.queryParams.pageSize = val
      this.queryParams.page = 1
      this.getList()
    },
    paginationCurrentChange(val) {
      this.queryParams.page = val
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
@mixin spot {
  content: "";
  display: inline-block;
  width: 8px;
  height: 8px;
  background: #00bc6d;
  border-radius: 50%;
  margin-right: 5px;
}

.onLine::before {
  @include spot;

  background: #00bc6d;
}

.offLine::before {
  @include spot;

  background: #ccced3;
}

.onLine {
  color: #00bc6d;
}

// .offLine {
//   color: #ccced3;
// }
.card-content {
  height: 100%;
  padding: 0 0 0 4px;
  display: flex;
  flex-direction: column;

  .query_criteria {
    display: flex;
  }

  .card-content-table {
    flex: 1;
    overflow: auto;
    margin-top: 16px;

    ::v-deep .el-progress {
      .el-progress-bar__outer {
        border-radius: 0;
        height: 8px !important;
      }

      .el-progress-bar__inner {
        border-radius: 0;
      }
    }
  }

  .card-content-footer {
    padding: 10px 0 0;
  }
}
</style>
