<!--
 * @Author: hedd
 * @Date: 2023-06-30 11:21:40
 * @LastEditTime: 2023-10-08 10:51:19
 * @FilePath: \ihcrs_pc\src\views\monitor\elevatorMenu\components\cameraTalkDialog.vue
 * @Description:
-->
<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    title="对讲"
    width="60%"
    :visible.sync="visible"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <div class="content">
      <rtspCavas ref="rtspCavas" :rtspUrl="videoUrl" :videoName="videoName" :hasCavas="hasCavas" class="video_preview"></rtspCavas>
      <div class="talk-box"></div>
      <playAudio ref="playAudio" />
    </div>
    <span slot="footer">
      <el-button class="stop-btn" type="primary" @click="closeDialog"><svg-icon name="phone-stop" /> 关闭对讲</el-button>
    </span>
  </el-dialog>
</template>
<script>
import playAudio from './playAudio.vue'
export default {
  name: 'cameraTalkDialog',
  components: {
    playAudio
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    cameraInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      requestHttp: __PATH.VUE_IEMC_ELEVATOR_API,
      videoUrl: '',
      videoName: '',
      hasCavas: false
    }
  },
  mounted() {
    this.getHlvAddress()
    this.$nextTick(() => {
      this.$refs.playAudio.startCall()
    })
  },
  methods: {
    // 获取摄像机地址
    getHlvAddress() {
      console.log(this.cameraInfo)
      const params = {
        cameraId: this.cameraInfo.vidiconId
      }
      this.$api.getHlvAddress(params, this.requestHttp).then((res) => {
        if (res.code == 200) {
          this.videoName = this.cameraInfo.vidiconName
          this.videoUrl = res.data
          this.hasCavas = true
        }
      })
    },
    closeDialog() {
      try {
        this.$refs.playAudio.stopCall()
      } catch (error) {}
      this.$emit('update:visible', false)
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog__body {
  height: 450px;

  .content {
    background: #fff;
    width: 100%;
    height: 100%;
    padding: 10px;
    border-radius: 4px;
    display: flex;
    justify-content: space-between;

    .video_preview,
    .talk-box {
      width: calc(50% - 5px);
      height: 100%;
    }

    .talk-box {
      background: url('~@/assets/images/elevator/camera-null.png') no-repeat;
      background-size: 100% 100%;
    }
  }
}

.stop-btn {
  border-color: #fa403c;
  background-color: #fa403c;
}
</style>
