<!--
 * @Description:
-->
<template>
  <div class="safety-card" :style="$attrs.style || {}">
    <div v-if="showTitle" class="safety-card-title">
      <div class="card-name">{{ title }}</div>
    </div>
    <div ref="cardBody" v-scrollbarHover class="safety-card-body" :style="{ height: showTitle ? 'calc(100% - 38px)' : '100%', background: `url(${bodyBg}) no-repeat`, backgroundSize: '100%' }">
      <slot name="content" />
    </div>
  </div>
</template>
<script>
export default {
  name: 'SafetyCard',
  props: {
    // 标题
    title: {
      type: String,
      default: ''
    },
    showTitle: {
      type: Boolean,
      default: true
    },
    bodyBg: {
      type: String,
      default: 'center'
    }
  },
  data() {
    return {
    }
  },
  methods: {}
}
</script>
<style lang="scss" scoped>
.safety-card {
  background: center;
  height: 100%;
  .safety-card-title {
    height: 38px;
    line-height: 30px;
    width: 68%;
    margin: 0 auto;
    text-align: center;
    background: url('~@/assets/images/safetyDataCockpit/box-title-bg.png') no-repeat;
    background-size: 100% 100%;
    .card-name {
      font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
      font-weight: bold;
      font-size: 15px;
      color: #cfe1f7;
    }
  }

  .safety-card-body{
    padding: 10px;
    box-sizing: border-box;
    color: #fff;
    background: red;
    // height: calc(100% - 38px);
  }
}
</style>
