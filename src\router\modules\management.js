import Layout from '@/layout'
import EmptyLayout from '@/layout/empty'
export default [
  {
    path: '/auth',
    component: Layout,
    redirect: '/auth/userManagement',
    name: 'auth',
    meta: {
      title: '权限管理',
      menuAuth: '/auth'
      // icon: 'sidebar-breadcrumb'
    },
    children: [
      {
        path: 'userManagement',
        component: EmptyLayout,
        redirect: { name: 'userManagement' },
        meta: {
          title: '用户管理',
          menuAuth: '/auth/userManagement'
        },
        children: [
          {
            path: '',
            name: 'userManagement',
            component: () => import('@/views/sysManagement/auth/userManagement.vue'),
            meta: {
              title: '用户管理',
              sidebar: false,
              breadcrumb: false,
              auth: [
                'userManagement:reset',
                'userManagement:search',
                'userManagement:add',
                'userManagement:import',
                'userManagement:export',
                'userManagement:accountsEnable',
                'userManagement:accountsDisable',
                'userManagement:roleAssignment',
                'userManagement:resetPassword'
              ]
            }
          },
          {
            path: 'userForm',
            name: 'userForm',
            component: () => import('@/views/sysManagement/auth/formComponent/userForm.vue'),
            meta: {
              title: '新增用户',
              sidebar: false,
              activeMenu: '/auth/userManagement'
            }
          }
        ]
      },
      {
        path: 'roleManagement',
        component: EmptyLayout,
        redirect: '/auth/roleManagement',
        meta: {
          title: '角色管理',
          menuAuth: '/auth/roleManagement'
        },
        children: [
          {
            path: '',
            name: 'roleManagement',
            component: () => import('@/views/sysManagement/auth/roleManagement.vue'),
            meta: {
              title: '角色管理',
              sidebar: false,
              breadcrumb: false,
              auth: ['roleManagement:add', 'roleManagement:edit', 'roleManagement:del', 'roleManagement:stop', 'roleManagement:start', 'roleManagement:role', 'roleManagement:menu']
            }
          },
          {
            path: 'roleForm',
            name: 'roleForm',
            component: () => import('@/views/sysManagement/auth/formComponent/roleForm.vue'),
            meta: {
              title: '角色详情',
              sidebar: false,
              activeMenu: '/auth/roleManagement'
            }
          }
        ]
      },
      {
        path: 'menuManagement',
        component: EmptyLayout,
        redirect: '/auth/menuManagement',
        meta: {
          title: '菜单管理',
          menuAuth: '/auth/menuManagement'
        },
        children: [
          {
            path: '',
            name: 'menuManagement',
            component: () => import('@/views/sysManagement/auth/menuManagement.vue'),
            meta: {
              title: '菜单管理',
              sidebar: false,
              breadcrumb: false,
              auth: ['menuManagement:add', 'menuManagement:delete', 'menuManagement:edit', 'menuManagement:start', 'menuManagement:stop']
            }
          }
        ]
      },
      {
        path: 'unifiedPermissions',
        component: EmptyLayout,
        redirect: '/auth/unifiedPermissions',
        meta: {
          title: '统一权限管理',
          menuAuth: '/auth/unifiedPermissions'
        },
        children: [
          {
            path: '',
            name: 'unifiedPermissions',
            component: () => import('@/views/sysManagement/auth/unifiedPermissions/index.vue'),
            meta: {
              title: '统一权限管理',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'addControlRow',
            name: 'addControlRow',
            component: () => import('@/views/sysManagement/auth/unifiedPermissions/addControlRow.vue'),
            meta: {
              title: '新增',
              sidebar: false,
              activeMenu: '/auth/unifiedPermissions'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/locationPoint',
    component: Layout,
    redirect: '/locationPoint/index',
    name: 'locationPoint',
    meta: {
      title: '定位点管理',
      menuAuth: '/locationPoint/index'
      // icon: 'sidebar-breadcrumb'
    },
    children: [
      {
        path: 'index',
        name: 'locationPoint',
        component: () => import('@/views/sysManagement/auth/locationPoint/locationPoint.vue'),
        meta: {
          title: '定位点管理',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/locationPoint/index'
        }
      },
      {
        path: 'addLocation',
        name: 'addLocation',
        component: () => import('@/views/sysManagement/auth/locationPoint/addLocation.vue'),
        meta: {
          title: '新增定位点',
          sidebar: false,
          activeMenu: '/locationPoint/index'
        }
      }
    ]
  },
  {
    path: '/dictionaryManagements',
    component: Layout,
    redirect: '/dictionaryManagements',
    name: 'dictionaryManagements',
    meta: {
      title: '字典管理',
      menuAuth: '/dictionaryManagements'
    },
    children: [
      {
        path: '',
        name: 'dictionaryManagements',
        component: () => import('@/views/sysManagement/auth/dictionaryManagement/dictionaryManagements.vue'),
        meta: {
          title: '字典管理',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/dictionaryManagements'
        }
      }
    ]
  },
  {
    path: '/applyManager',
    component: Layout,
    redirect: { name: 'ApplyManager' },
    meta: {
      title: '应用中心管理',
      menuAuth: '/applyManager'
    },
    children: [
      {
        path: '',
        name: 'ApplyManager',
        component: () => import('@/views/sysManagement/applyManager/applyManager.vue'),
        meta: {
          title: '应用中心管理',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/applyManager'
        }
      },
      {
        path: 'addApp',
        name: 'AddApp',
        component: () => import('@/views/sysManagement/applyManager/addApp.vue'),
        meta: {
          title: '新增应用',
          sidebar: false,
          activeMenu: '/applyManager'
        }
      }
    ]
  },
  {
    path: '/appManager',
    component: Layout,
    name: 'AppManager',
    redirect: '/AppManager/messageRelease',
    meta: {
      title: 'App应用管理',
      menuAuth: '/appManager'
    },
    children: [
      {
        path: 'messageRelease',
        component: EmptyLayout,
        redirect: { name: 'MessageRelease' },
        meta: {
          title: '消息发布',
          menuAuth: '/appManager/messageRelease'
        },
        children: [
          {
            path: '',
            name: 'MessageRelease',
            component: () => import('@/views/sysManagement/appManager/messageRelease/index.vue'),
            meta: {
              title: '消息发布',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'addMessage',
            name: 'AddMessage',
            component: () => import('@/views/sysManagement/appManager/messageRelease/addMessage.vue'),
            meta: {
              title: '新增消息',
              sidebar: false,
              activeMenu: '/appManager/messageRelease'
            }
          }
        ]
      },
      {
        path: 'appMenu',
        component: EmptyLayout,
        redirect: { name: 'AppMenu' },
        meta: {
          title: '应用管理',
          menuAuth: '/appManager/appMenu'
        },
        children: [
          {
            path: '',
            name: 'AppMenu',
            component: () => import('@/views/sysManagement/appManager/appMenu/index.vue'),
            meta: {
              title: '应用管理',
              sidebar: false,
              breadcrumb: false
            }
          }
          // {
          //   path: 'addMessage',
          //   name: 'AddMessage',
          //   component: () => import('@/views/sysManagement/appManager/appMenu/addMenu.vue'),
          //   meta: {
          //     title: '新建菜单',
          //     sidebar: false,
          //     activeMenu: '/appManager/appMenu'
          //   }
          // }
        ]
      }
    ]
  },
  {
    path: '/log',
    component: Layout,
    redirect: '/log/loginLog',
    name: 'log',
    meta: {
      title: '日志管理'
      // menuAuth: '/log'
      // icon: 'sidebar-breadcrumb'
    },
    children: [
      {
        path: 'loginLog',
        component: EmptyLayout,
        redirect: { name: 'loginLog' },
        meta: {
          title: '登录日志'
          // menuAuth: '/log/loginLog'
        },
        children: [
          {
            path: '',
            name: 'loginLog',
            component: () => import('@/views/sysManagement/log/loginLog/index.vue'),
            meta: {
              title: '登录日志',
              sidebar: false,
              breadcrumb: false
              // auth: [
              //   'loginLog:reset',
              //   'loginLog:search'
              // ]
            }
          }
        ]
      },
      {
        path: 'operationLog',
        component: EmptyLayout,
        redirect: { name: 'operationLog' },
        meta: {
          title: '操作日志'
          // menuAuth: '/log/operationLog'
        },
        children: [
          {
            path: '',
            name: 'operationLog',
            meta: {
              title: '操作日志',
              sidebar: false,
              breadcrumb: false
            },
            component: () => import('@/views/sysManagement/log/operationLog/index.vue')
          }
        ]
      }
    ]
  },
  {
    path: '/processManagement',
    component: Layout,
    redirect: '/processManagement/maintenanceDispatch',
    name: 'processManagement',
    meta: {
      title: '流程管理'
      // menuAuth: '/log'
      // icon: 'sidebar-breadcrumb'
    },
    children: [
      {
        path: 'maintenanceDispatch',
        component: EmptyLayout,
        // redirect: { name: 'allProject' },
        meta: {
          title: '运维调度',
          menuAuth: '/processManagement/maintenanceDispatch' // 为了隐藏
        },
        children: [
          {
            path: '',
            name: 'maintenanceDispatch',
            component: () => import('@/views/microApp/index.vue'),
            meta: {
              title: '运维调度',
              sidebar: false,
              breadcrumb: false,
              auth: []
            }
          }
        ]
      }
    ]
  }
]
