<template>
  <PageContainer :footer="true">
    <div slot="content" class="role-content">
      <el-descriptions :column="3">
        <el-descriptions-item width="33%" labelClassName="labelItemName" label="申请人">{{ form.createByName || '' }}</el-descriptions-item>
        <el-descriptions-item width="33%" labelClassName="labelItemName" label="性别">{{ sexFilter(form.userGender) }}</el-descriptions-item>
        <el-descriptions-item width="33%" labelClassName="labelItemName" label="工号">{{ form.userEmployeeId || '' }}</el-descriptions-item>
        <el-descriptions-item width="33%" labelClassName="labelItemName" label="身份证号">{{ form.userIdCard || '' }}</el-descriptions-item>
        <el-descriptions-item width="33%" labelClassName="labelItemName" label="所属科室">{{ form.userDepartment || '' }}</el-descriptions-item>
        <el-descriptions-item width="33%" labelClassName="labelItemName" label="岗位">{{ form.userPost || '' }}</el-descriptions-item>
        <el-descriptions-item width="33%" labelClassName="labelItemName" label="入职日期">{{ form.userEntryTime || '' }}</el-descriptions-item>
        <el-descriptions-item width="33%" labelClassName="labelItemName" label="联系电话">{{ form.userPhone || '' }}</el-descriptions-item>
        <el-descriptions-item width="33%" labelClassName="labelItemName" label="申请日期">{{ form.processTime || '' }}</el-descriptions-item>
        <el-descriptions-item width="33%" labelClassName="labelItemName" label="申请小区" v-if="['1', '4'].includes(form.processType)">{{
          form.applyCommunityName || ''
        }}</el-descriptions-item>
        <el-descriptions-item width="33%" labelClassName="labelItemName" label="申请户型" v-if="['1', '4'].includes(form.processType)">{{
          form.houseTypeName || ''
        }}</el-descriptions-item>
        <el-descriptions-item width="33%" labelClassName="labelItemName" label="是否服从调剂">
          <el-radio-group v-model="form.acceptAdjustment">
            <el-radio :label="'1'" disabled>是</el-radio>
            <el-radio :label="'0'" disabled>否</el-radio>
          </el-radio-group>
        </el-descriptions-item>
        <el-descriptions-item width="33%" labelClassName="labelItemName" label="婚姻状况" v-if="['1', '4'].includes(form.processType)">
          <el-radio-group v-model="form.marriage">
            <el-radio :label="'0'" disabled>未婚</el-radio>
            <el-radio :label="'1'" disabled>已婚</el-radio>
          </el-radio-group>
        </el-descriptions-item>
      </el-descriptions>
      <el-descriptions :column="3">
        <el-descriptions-item width="50%" labelClassName="labelClassName">
          <div class="file-box" style="width: calc(100% - 200px)">
            <div class="file-title">身份证复印件:</div>
            <div class="file">
              <div
                class="file"
                style="color: #3562db; cursor: pointer; margin-bottom: 10px"
                v-for="(item, index) in fileFormat(form.copyOfIdCard)"
                :key="index"
                @click="goToViewFile(item)"
              >
                {{ urlFilters(item) }}
              </div>
            </div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item width="50%" labelClassName="labelClassName">
          <div class="file-box" style="width: calc(100% - 200px)">
            <div class="file-title">学历学位复印件:</div>
            <div class="file">
              <div
                class="file"
                style="color: #3562db; cursor: pointer; margin-bottom: 10px"
                v-for="(item, index) in fileFormat(form.copyOfDiploma)"
                :key="index"
                @click="goToViewFile(item)"
              >
                {{ urlFilters(item) }}
              </div>
            </div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item width="50%" labelClassName="labelClassName">
          <div class="file-box" style="width: calc(100% - 200px)">
            <div class="file-title">职称复印件:</div>
            <div class="file">
              <div
                class="file"
                style="color: #3562db; cursor: pointer; margin-bottom: 10px"
                v-for="(item, index) in fileFormat(form.copyOfProfessionalTitle)"
                :key="index"
                @click="goToViewFile(item)"
              >
                {{ urlFilters(item) }}
              </div>
            </div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item width="50%" labelClassName="labelClassName" v-if="form.marriage === '0'">
          <div class="file-box" style="width: calc(100% - 200px)">
            <div class="file-title">深圳居住证:</div>
            <div class="file">
              <div
                class="file"
                style="color: #3562db; cursor: pointer; margin-bottom: 10px"
                v-for="(item, index) in fileFormat(form.copyOfResidencePermit)"
                :key="index"
                @click="goToViewFile(item)"
              >
                {{ urlFilters(item) }}
              </div>
            </div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item width="50%" labelClassName="labelClassName" v-if="form.marriage === '0'">
          <div class="file-box" style="width: calc(100% - 200px)">
            <div class="file-title">父母身份证复印件:</div>
            <div class="file">
              <div
                class="file"
                style="color: #3562db; cursor: pointer; margin-bottom: 10px"
                v-for="(item, index) in fileFormat(form.copyOfParentsIdCard)"
                :key="index"
                @click="goToViewFile(item)"
              >
                {{ urlFilters(item) }}
              </div>
            </div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item width="50%" labelClassName="labelClassName">
          <div class="file-box" style="width: calc(100% - 200px)">
            <div class="file-title">深圳无房证明:</div>
            <div class="file">
              <div
                class="file"
                style="color: #3562db; cursor: pointer; margin-bottom: 10px"
                v-for="(item, index) in fileFormat(form.noHouse)"
                :key="index"
                @click="goToViewFile(item)"
              >
                {{ urlFilters(item) }}
              </div>
            </div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item width="50%" labelClassName="labelClassName" v-if="form.marriage === '1'">
          <div class="file-box" style="width: calc(100% - 200px)">
            <div class="file-title">结婚证复印件:</div>
            <div class="file">
              <div
                class="file"
                style="color: #3562db; cursor: pointer; margin-bottom: 10px"
                v-for="(item, index) in fileFormat(form.copyOfMarriageCertificate)"
                :key="index"
                @click="goToViewFile(item)"
              >
                {{ urlFilters(item) }}
              </div>
            </div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item width="50%" labelClassName="labelClassName" v-if="form.marriage === '1'">
          <div class="file-box" style="width: calc(100% - 200px)">
            <div class="file-title">配偶身份证复印件:</div>
            <div class="file">
              <div
                class="file"
                style="color: #3562db; cursor: pointer; margin-bottom: 10px"
                v-for="(item, index) in fileFormat(form.copyOfSpouseIdCard)"
                :key="index"
                @click="goToViewFile(item)"
              >
                {{ urlFilters(item) }}
              </div>
            </div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item width="50%" labelClassName="labelClassName">
          <div class="file-box" style="width: calc(100% - 200px)">
            <div class="file-title">同住人资料:</div>
            <div class="file">
              <div
                class="file"
                style="color: #3562db; cursor: pointer; margin-bottom: 10px"
                v-for="(item, index) in fileFormat(form.cohabitant)"
                :key="index"
                @click="goToViewFile(item)"
              >
                {{ urlFilters(item) }}
              </div>
            </div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item width="50%" labelClassName="labelClassName">
          <div class="file-box" style="width: calc(100% - 200px)">
            <div class="file-title">其他资料:</div>
            <div class="file">
              <div
                class="file"
                style="color: #3562db; cursor: pointer; margin-bottom: 10px"
                v-for="(item, index) in fileFormat(form.otherInfo)"
                :key="index"
                @click="goToViewFile(item)"
              >
                {{ urlFilters(item) }}
              </div>
            </div>
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <div slot="footer">
      <el-button type="primary" @click="close">关闭</el-button>
    </div>
  </PageContainer>
</template>
  <script>
export default {
  data() {
    return {
      form: {},
      projectContractEntity: {},
      projectWaterPowerEntity: {},
      projectHistoryEntity: {}
    }
  },
  mounted() {
    this.getDetails()
  },
  methods: {
    goToViewFile(item) {
      fetch(this.$tools.imgUrlTranslation(item.url))
        .then((res) => res.blob())
        .then((blob) => {
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = item.name
          a.click()
          window.URL.revokeObjectURL(url)
        })
        .catch(() => this.$message.error('下载失败'))
    },
    sexFilter(val) {
      let sex = {
        1: '男',
        2: '女'
      }
      if (!val) return ''
      if (val === '男') return val
      if (val === '女') return val
      return sex[val]
    },
    fileFormat(val) {
      if (!val) return []
      return JSON.parse(val)
    },
    urlFilters(item) {
      if (!item) return
      return item.name
    },
    getDetails() {
      let params = {
        id: this.$route.query.id || ''
      }
      this.$api.rentalHousingApi
        .getApproveDetail(params)
        .then((res) => {
          if (res.code == 200) {
            const { allocationEntity, projectContractEntity, projectWaterPowerEntity, projectHistoryEntity } = res.data
            if (allocationEntity) {
              this.form = allocationEntity
            }
            this.projectContractEntity = projectContractEntity || {}
            this.projectWaterPowerEntity = projectWaterPowerEntity || {}
            this.projectHistoryEntity = projectHistoryEntity || {}
          } else {
            throw res.message
          }
        })
        .catch((msg) => {
          this.$message.error(msg)
        })
        .finally(() => {})
    },
    close() {
      this.$router.go(-1)
    }
  }
}
</script>
  <style lang="scss" scoped>
.role-content {
  height: 100%;
  box-sizing: border-box;
  padding: 16px;
  overflow-y: auto;
  background: #fff;
}
.file-box {
  display: flex;
  .file-title {
    width: 130px;
    text-align: right;
    margin-right: 10px;
  }
}
.title-box {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  .content_title {
    width: 100px;
    text-align: right;
    margin-right: 16px;
  }
}
.head-title {
  font-weight: bolder;
  color: #333;
  font-size: 20px;
  padding-left: 15px;
  margin-bottom: 16px;
}
::v-deep .el-descriptions__cell {
  vertical-align: top !important;
}
::v-deep .labelClassName {
  display: none;
}
::v-deep .labelItemName {
  width: 130px;
  text-align: right;
  display: inline-block;
}
</style>