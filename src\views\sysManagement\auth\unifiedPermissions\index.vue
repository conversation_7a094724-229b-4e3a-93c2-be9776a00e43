<template>
  <PageContainer>
    <div slot="content" class="role-content" style="height: 100%;">
      <div class="role-content-left">
        <div class="toptip">
          <span class="green_line"></span>
          系统列表
        </div>
        <div class="left_content">
          <ul>
            <li class="pitchOn">双重预防管理系统</li>
          </ul>
        </div>
      </div>
      <div class="role-content-center">
        <div style="height: 100%;">
          <div class="contentTable-main table-content">
            <el-table ref="tableList" v-loading="tableLoading" :data="tableData" :height="tableHeight" border stripe highlight-current-row>
              <!-- <el-table-column type="selection" width="55" fixed="left" align="center"></el-table-column> -->
              <el-table-column type="index" label="序号" width="65">
                <template slot-scope="scope">
                  <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="roleName" show-overflow-tooltip label="角色名称"></el-table-column>
              <el-table-column prop="roleCode" show-overflow-tooltip label="角色编码"></el-table-column>
              <!-- <el-table-column prop="roleSort" width="80" show-overflow-tooltip label="排序号"></el-table-column> -->
              <el-table-column prop="remarks" show-overflow-tooltip label="备注"></el-table-column>
              <!-- <el-table-column prop="updateDate" width="120" show-overflow-tooltip label="更新时间"></el-table-column> -->
              <el-table-column prop="status" show-overflow-tooltip label="状态">
                <template slot-scope="scope">
                  <div :class="'status' && scope.row.status == 0 ? 'colorGreen' : 'colorGray'">{{ scope.row.status == 0 ? '启用' : '禁用' }}</div>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="180" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span style="color: rgb(8 203 131); margin-right: 10px;" class="operation" @click="operation('1', scope.row)">权限管理</span>
                  <span style="color: rgb(53 98 219);" class="operation" @click="operation('2', scope.row)">用户管理</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>

      <div v-if="jurisdictionShow" class="role-content-right" style="width: 330px;">
        <div class="toptip">
          <span class="green_line"></span>
          权限设置
        </div>
        <div class="left_content" style="overflow: auto; padding: 20px 0 0 20px; height: calc(100% - 100px);">
          <div style="display: inline-block; width: 100%; text-align: left; margin-bottom: 10px;">PC权限：</div>
          <div style="display: flex;">
            <el-tree
              ref="tree"
              style="width: calc(100% - 80px); margin-left: 30px;"
              :data="treeData"
              show-checkbox
              :props="defaultProps"
              :default-checked-keys="idArr"
              node-key="menuId"
              default-expand-all
            ></el-tree>
          </div>
        </div>
        <div class="footer">
          <el-button style="flex: 1; font-size: 14px;" type="primary" :loading="treeLoading" icon="el-icon-plus" @click="sure">保存</el-button>
          <el-button style="flex: 1; font-size: 14px;" type="primary" :loading="treeLoading" icon="el-icon-edit" @click="chooseAll(1)">全选</el-button>
          <el-button style="flex: 1; font-size: 14px;" type="primary" :loading="treeLoading" icon="el-icon-delete" @click="chooseAll(0)">全不选</el-button>
        </div>
      </div>
      <div v-if="userShow" class="role-content-right" style="width: 500px;">
        <div class="toptip">
          <span class="green_line"></span>
          用户管理
        </div>
        <el-button size="small" type="primary" style="margin: 10px 0;" @click="addUser('add')">新增</el-button>
        <div class="tableContent">
          <el-table v-loading="tableLoading1" v-el-table-infinite-scroll="loadMore" :data="tableData1" height="calc(100% - 50px)" border stripe @row-dblclick="dblclick">
            <!-- <el-table-column type="selection" width="55" align="center" fixed="left"></el-table-column> -->
            <el-table-column type="index" label="序号" width="77">
              <template slot-scope="scope">
                <span class="spanStyle">{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="name" show-overflow-tooltip width="100" label="姓名"></el-table-column>
            <el-table-column prop="controlTeamName" show-overflow-tooltip label="所属部门"></el-table-column>
            <el-table-column prop="positionType" show-overflow-tooltip label="权限">
              <template slot-scope="scope">
                <span>
                  {{ scope.row.positionType == '1' ? '组长' : scope.row.positionType == '2' ? '组员' : '' }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="180" show-overflow-tooltip>
              <template slot-scope="scope">
                <span style="color: red; margin-right: 10px;" class="operation" @click="delData(scope.row.id)">删除</span>
                <span style="color: rgb(53 98 219);" class="operation" @click="addUser('edit', scope.row.id)">编辑</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <!-- 添加人员 -->
      <addControlRow ref="addControlRow" :outerVisible="outerVisible" @closeDialog="closeDialog" @getUserList="getUserList"></addControlRow>
    </div>
  </PageContainer>
</template>

<script>
import { transData } from '@/util'
import axios from 'axios'
import addControlRow from './addControlRow.vue'
export default {
  name: 'unifiedPermissions',
  components: { addControlRow },
  data() {
    return {
      tableLoading: false,
      tableData: [],
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'menuName',
        value: 'menuId',
        checkStrictly: true
      },
      idArr: [],
      jurisdictionShow: false,
      userShow: false,
      clickRow: '',
      tableLoading1: false,
      tableData1: [],
      paginationData1: {
        currentPage1: 1,
        pageSize1: 15,
        total1: 0
      },
      outerVisible: false,
      saveType: '',
      checkUserId: ''
    }
  },
  computed: {
    tableHeight() {
      return document.body.clientHeight - 160
    }
  },
  mounted() {},
  created() {
    this.getTable()
  },
  methods: {
    getTable() {
      this.tableLoading = true
      this.$api
        .userRoleGetRoleListIpsm({
          roleType: '',
          isShowSystemAdmin: 0,
          pageSize: this.paginationData.pageSize,
          currentPage: this.paginationData.currentPage
        })
        .then((res) => {
          this.tableLoading = false
          this.paginationData.total = res.data.sum
          this.tableData = res.data
        })
        .catch(() => {
          this.tableLoading = false
        })
    },
    operation(type, row) {
      this.paginationData.currentPage = 1
      this.paginationData1.currentPage1 = 1
      this.clickRow = row
      if (type == '1') {
        this.jurisdictionShow = true
        this.userShow = false
        this.getMenuList()
      } else {
        this.tableData1 = []
        this.jurisdictionShow = false
        this.userShow = true
        this.getStaffList()
      }
    },
    // .........权限........
    getMenuList() {
      this.treeLoading = true
      let data = {
        roleCode: this.clickRow.id
      }
      data.sysFlag = 'IDPS_CORE'
      this.$api.userMenuControllerGetMenuListIpsm(data).then((res) => {
        this.treeLoading = false
        if (res.code == 200) {
          this.tempTreeData = res.data.pcPlatform
          this.treeData = transData(res.data.pcPlatform, 'menuId', 'parentId', 'children')
          // 菜单选中
          let list = new Array()
          let listNo = new Array()
          res.data.pcPlatform.forEach((item) => {
            if (item.isShow == 0) {
              list.push(item.menuId)
            } else {
              listNo.push(item.menuId)
            }
          })
          this.$nextTick(() => {
            list.forEach((item) => {
              this.$refs.tree.setChecked(item, true, false)
            })
            listNo.forEach((item) => {
              this.$refs.tree.setChecked(item, false, false)
            })
          })
        }
      })
    },
    sure() {
      let list = this.$refs.tree.getCheckedNodes().concat(this.$refs.tree.getHalfCheckedNodes())
      let temp = []
      list.forEach((item) => {
        temp.push(item.menuId)
      })
      if (list.length == 0) {
        this.$message.error('请先为该角色分配菜单')
        return
      }
      let data = {
        roleCode: this.clickRow.id,
        menuCodes: temp.join(',')
      }
      this.$api.addAssociatedIpsm(data).then((res) => {
        if (res.code == 200) {
          this.$message.success(res.message)
          //   this.init()
        } else if (res.message) {
          this.$message.error(res.message)
        }
      })
    },
    chooseAll(state) {
      if (state == 1) {
        this.idArr = this.tempTreeData.map((item) => {
          return item.menuId
        })
      } else {
        this.$refs.tree.store._getAllNodes().map((item) => {
          item.checked = false
        })
      }
    },
    // ......用户........
    loadMore() {
      if (this.tableData1.length < this.paginationData1.total1 && this.tableData1.length) {
        this.paginationData1.currentPage1 += 1
        this.getStaffList()
      }
    },
    getStaffList() {
      let data = {
        currentPage: this.paginationData1.currentPage1,
        pageSize: this.paginationData1.pageSize1,
        webRoleCode: this.clickRow.id,
        positionType: ''
      }
      this.$api.getUserListByWebRoleCodeIpsm(data).then((res) => {
        this.tableLoading1 = true
        if (res.code == 200) {
          this.tableData1 = this.tableData1.concat(res.data.list)
          this.paginationData1.total1 = parseInt(res.data.sum)
        } else {
          this.$message.error(res.message)
        }
        this.tableLoading1 = false
      })
    },
    addUser(val, userId) {
      this.$refs.addControlRow.getSaveType(val, this.clickRow.id, userId)
      // this.saveType = val
      // this.checkUserId = userId || ''
      this.outerVisible = true
    },
    dblclick(row) {
      this.$refs.addControlRow.getSaveType('check', this.clickRow.id, row.id)
      this.outerVisible = true
    },
    closeDialog() {
      this.outerVisible = false
    },
    getUserList() {
      this.tableData1 = []
      this.paginationData1.currentPage1 = 1
      this.getStaffList()
    },
    // 删除
    delData(id) {
      this.$confirm('确认删除?', '提醒', { type: 'warning' }).then((res) => {
        this.$api.delControlTeamUserIpsm({ id: id }).then((res) => {
          if (res.code == 200) {
            this.$message.success(res.message)
            this.tableData1 = []
            this.paginationData1.currentPage1 = 1
            this.getStaffList()
          } else {
            this.$message.error(res.message)
          }
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
// el-tree-node is-expanded is-focusable
::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: linear-gradient(to right, #d9e1f8, #fff);
}

::v-deep .el-tree-node__content {
  height: 32px;
}

.role-content {
  height: 100%;
  display: flex;

  .role-content-left {
    width: 246px;
    height: 100%;
    padding: 10px;
    background: #fff;
    border-radius: 4px;
    margin-right: 12px;

    .left_content {
      height: calc(100% - 60px);
      padding: 10px;
      // overflow: auto;
      ul {
        padding: 0;

        li {
          height: 38px;
          width: 100%;
          font-size: 15px;
          font-family: "PingFang SC-Regular", "PingFang SC";
          font-weight: 400;
          color: #606266;
          line-height: 38px;
          list-style-type: none;
          box-sizing: border-box;
          cursor: pointer;
          padding-left: 27px;

          &:hover {
            background: rgb(53 98 219 / 20%);
            border-radius: 4px 0 0 4px;
          }
        }
      }

      .pitchOn {
        color: #3562db;
        background: linear-gradient(to right, #d9e1f8, #fff);
        font-weight: 500;
      }
    }
  }

  .role-content-center {
    flex: 1;
    height: 100%;
    min-width: 0;
    padding: 20px;
    background: #fff;
    border-radius: 4px;

    .contentTable-main {
      height: 100%;
      overflow: auto;
    }
  }

  .role-content-right {
    // width: 350px;
    height: 100%;
    margin-left: 12px;
    background: #fff;
    border-radius: 4px;
    padding: 10px;
  }
}

.footer {
  height: 60px;
  box-shadow: 0 -4px 4px 0 #f3f4f8;
  width: 100%;
  padding: 0 20px;
  box-sizing: border-box;
  line-height: 60px;
  display: flex;
  z-index: 100;
  position: relative;
  justify-content: center;
  align-items: center;
  font-size: 14px;
}

.operation {
  cursor: pointer;
  font-size: 14px;
}

.tableContent {
  height: calc(100% - 100px);
  // overflow: auto;
}
</style>
