<template>
  <div>
    <ContentCard :title="type == 'new' ? '新建菜单' : type == 'edit' ? '编辑菜单' : type == 'details' ? '查看菜单' : ''"></ContentCard>
    <el-form ref="ruleForm" class="rule-form" style="padding-left: 10px; padding-right: 10px" :model="form" :rules="rules">
      <div style="display: flex">
        <el-form-item label="菜单类型" label-width="85px">
          <el-radio-group v-model="form.menuType" style="width: 200px" :disabled="type == 'details'">
            <el-radio :label="1">菜单</el-radio>
            <el-radio :label="2">按钮</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="所属模块" label-width="85px" prop="superiorMenu">
          <el-select ref="treeSelect" v-model="form.superiorMenu" placeholder="请选择所属模块" :disabled="type == 'details'" @focus="getMenu" @clear="handleClear">
            <el-option hidden :value="form.superiorMenu" :label="areaName"> </el-option>
            <el-tree :data="menuList" :props="defaultProps" :expand-on-click-node="false" :check-on-click-node="true" @node-click="handleNodeClick"> </el-tree>
          </el-select>
        </el-form-item>
        <el-form-item label="菜单名称" label-width="85px" prop="menuName">
          <el-input v-model="form.menuName" placeholder="请输入菜单名称" class="ipt" :disabled="type == 'details'"></el-input>
        </el-form-item>
      </div>
      <div style="display: flex">
        <el-form-item label="应用分类" label-width="85px" prop="appCategory">
          <el-select v-model="form.appCategory" clearable placeholder="请选择应用分类" :disabled="type == 'details' || disableCategory">
            <el-option v-for="item in appClassifyList" :key="item" :value="item" :label="item"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="链接地址" label-width="85px">
          <el-input v-model="form.chainedAddress" placeholder="请输入链接地址" class="ipt" :disabled="type == 'details'"></el-input>
        </el-form-item>
        <el-form-item label="排序号" label-width="85px" prop="sortNumber">
          <el-input v-model="form.sortNumber" placeholder="请输入排序号" class="ipt" :disabled="type == 'details'"></el-input>
        </el-form-item>
      </div>
      <el-form-item label="菜单图标" label-width="85px">
        <span style="font-size: 10px; color: #7f848c">支持{{ uploadAcceptDict['picture'].type }},且大小不超过{{ uploadAcceptDict['picture'].fileSize }}M</span>
        <div style="display: flex">
          <el-upload
            action=""
            :headers="headers"
            list-type="picture-card"
            :class="{ hide: hideUpload }"
            :file-list="fileList"
            :accept="uploadAcceptDict['picture'].type"
            :limit="1"
            :before-upload="beforeAvatarUpload"
            :http-request="httpRequset"
            :on-remove="handleRemove"
            :on-change="fileChange"
            :disabled="type == 'details'"
          >
            <i class="el-icon-circle-plus-outline" style="color: #3562db"
            ><br /><span style="font-size: 10px; color: #7f848c">菜单默认图标</span><span style="font-size: 10px; color: #7f848c">(20*20)</span></i
            >
          </el-upload>
          <!-- &nbsp;
          <el-upload
            action=""
            :headers="headers"
            list-type="picture-card"
            :class="{ hide2: hideUpload2 }"
            :file-list="fileList2"
            accept=".png"
            :limit="1"
            :before-upload="beforeAvatarUpload"
            :http-request="httpRequset2"
            :on-remove="handleRemove2"
            :on-change="fileChange2"
            :disabled="type == 'details'"
          >
            <i class="el-icon-circle-plus-outline" style="color: #3562db;"><br /><span style="font-size: 10px; color: #7f848c;">菜单选中图标</span><span style="font-size: 10px; color: #7f848c;">(32*32)</span></i
            >
          </el-upload> -->
        </div>
      </el-form-item>
      <el-form-item label="备注" label-width="85px">
        <el-input
          v-model="form.remarks"
          style="width: 60%"
          placeholder="请输入备注"
          type="textarea"
          maxlength="200"
          show-word-limit
          :autosize="{ minRows: 4, maxRows: 4 }"
          :disabled="type == 'details'"
        ></el-input>
      </el-form-item>
    </el-form>
    <div class="buttom">
      <el-button v-if="type != 'details'" type="primary" @click="confirm('ruleForm')">保存</el-button>
      <el-button type="primary" plain @click="cancel">取消</el-button>
    </div>
  </div>
</template>
<script>
import { uploadAcceptDict } from '@/util/dict.js'
import { transData, treeToList, deepClone } from '@/util'
import store from '@/store/index'
import axios from 'axios'
export default {
  props: {
    type: String,
    menuId: Number
  },
  data() {
    return {
      uploadAcceptDict,
      form: {
        superiorMenu: '',
        menuType: 1,
        menuName: '',
        pageTitle: '',
        chainedAddress: '',
        sortNumber: '',
        remarks: '',
        appCategory: ''
      },
      icon: {},
      formData: '',
      formData2: '',
      imageUrl: '',
      menuList: [],
      fileList: [],
      fileList2: [],
      hideUpload: false,
      hideUpload2: false,
      areaName: '',
      rules: {
        superiorMenu: [{ required: true, message: '请选择所属模块', trigger: 'blur' }],
        menuName: [{ required: true, message: '请输入菜单名称', trigger: 'blur' }],
        appCategory: [{ required: false, message: '请选择应用分类', trigger: 'blur' }],
        sortNumber: [{ required: true, message: '请输入排序号', trigger: 'blur' }]
      },
      defaultProps: {
        children: 'children',
        label: 'menuName',
        value: 'menuId'
      },
      appClassifyList: [],
      disableCategory: true
    }
  },
  computed: {
    headers() {
      return {
        Authorization: 'Bearer ' + store.state.user.token
      }
    }
  },
  created() {
    if (this.type != 'new') {
      this.$api.getMenuInfo({ menuId: this.menuId }).then((res) => {
        if (res.code == 200) {
          this.form.superiorMenu = res.data.menuInfo.parentId
          this.form.menuType = res.data.menuInfo.menuType
          this.form.menuName = res.data.menuInfo.menuName
          this.form.chainedAddress = res.data.menuInfo.pathUrl
          this.form.sortNumber = res.data.menuInfo.orderNum
          this.form.remarks = res.data.menuInfo.remark
          this.form.appCategory = res.data.menuInfo.appCategory
          this.areaName = res.data.menuInfo.parentName
          let url = JSON.parse(res.data.menuInfo.icon)
          if (url.approve != undefined) {
            this.fileList = [
              {
                url: this.$tools.imgUrlTranslation(url.approve)
              }
            ]
            this.hideUpload = true
            this.icon.approve = url.approve
          } else {
            this.hideUpload = false
          }
          if (url.pitch != undefined) {
            this.fileList2 = [
              {
                url: this.$tools.imgUrlTranslation(url.pitch)
              }
            ]
            this.hideUpload2 = true
            this.icon.pitch = url.pitch
          } else {
            this.hideUpload2 = false
          }
        }
      })
    }
    this.getAppCategoryList()
  },
  methods: {
    // 获取应用分类
    getAppCategoryList() {
      this.$api.GetAppCategoryList({ menuId: this.menuId }).then((res) => {
        if (res.code == 200) {
          this.appClassifyList = res.data
        }
      })
    },
    handleClear() {
      this.form.superiorMenu = ''
      this.areaName = ''
    },
    handleNodeClick(data, Node, self) {
      this.form.superiorMenu = data.menuId
      const parentMenuIds = this.recursionTree(Node)
      // 数据中心才有应用分类
      this.disableCategory = !parentMenuIds.includes(1005)
      if (!this.disableCategory) {
        this.rules['appCategory'] = { required: true, message: '请选择应用分类', trigger: 'blur' }
      } else {
        this.rules['appCategory'] = { required: false }
        this.form.appCategory = ''
      }
      this.areaName = data.menuName
      this.$refs.treeSelect.blur()
    },
    recursionTree(tree) {
      const newTree = tree
      // const newTree = deepClone(tree)
      let menuIds = []
      const loop = (data) => {
        if (data.parent && data.parent != null) {
          menuIds.push(data.data.menuId)
          loop(data.parent)
        }
      }
      loop(newTree)
      return menuIds
    },
    getMenu() {
      // this.$api.GetAppMenuParent({ }).then((res) => {
      //   if (res.code == 200) {
      //     this.menuList = res.data
      //   }
      // })
      const params = {
        state: 0,
        userId: this.$store.state.user.userInfo.userId
      }
      this.$api.SysAppMenu(params).then((res) => {
        if (res.code === '200') {
          let menuList = treeToList(res.data ?? [])
          const menuFixIds = [1001, 1002]
          menuList = menuList.filter((item) => !(item.appFix == 0 && menuFixIds.includes(item.menuId)))
          this.menuList = transData(menuList, 'menuId', 'parentId', 'children')
        }
      })
    },
    confirm(formName) {
      let params = {
        menuType: this.form.menuType, // 菜单类型
        parentId: this.form.superiorMenu, // 上级菜单
        menuName: this.form.menuName, // 菜单名称
        pathUrl: this.form.chainedAddress, // 链接地址
        orderNum: this.form.sortNumber, // 排序号
        state: 0,
        remark: this.form.remarks,
        appCategory: this.form.appCategory,
        menuCategory: 1
      }
      if (this.icon.approve == undefined && this.icon.pitch == undefined) {
        params.icon = ''
      } else {
        params.icon = JSON.stringify(this.icon)
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.type == 'new') {
            params.userId = this.$store.state.user.userInfo.userId
            this.$api.menuAdd(params, { 'operation-type': 1 }).then((res) => {
              if (res.code == 200) {
                this.$message({
                  message: res.data,
                  type: 'success'
                })
                this.$emit('menuDiscreteness')
                this.$emit('child-event', false)
              } else {
                this.$message.error(res.data)
              }
            })
          } else {
            params.menuId = this.menuId
            this.$api.edit(params, { 'operation-type': 2 }).then((res) => {
              if (res.code == 200) {
                this.$message({
                  message: res.data,
                  type: 'success'
                })
                this.$emit('menuDiscreteness')
                this.$emit('child-event', false)
              } else {
                this.$message.error(res.data)
              }
            })
          }
        } else {
          return false
        }
      })
    },
    cancel() {
      this.$emit('child-event', false)
    },
    fileChange(file, fileList) {
      this.fileList = fileList
    },
    beforeAvatarUpload(file) {
      const extension = file.name.substring(file.name.lastIndexOf('.'))
      const isFormat = this.uploadAcceptDict['picture'].type.includes(extension)
      if (!isFormat) {
        this.$message.error(`上传图片只能是 ${this.uploadAcceptDict['picture'].type}格式!`)
        return false
      }
      const fileSize = this.uploadAcceptDict['picture'].fileSize
      const isLt2M = file.size / 1024 / 1024 < fileSize
      if (!isLt2M) {
        this.$message.error(`上传文件大小不能超过 ${fileSize}MB!`)
        return false
      }
    },
    handleRemove(file, fileList) {
      this.icon.approve = undefined
      // this.fileList = fileList
      this.hideUpload = false
    },
    httpRequset() {
      this.formData = new FormData()
      this.fileList.forEach((item) => {
        this.formData.append('file', item.raw)
      })
      axios
        .post(__PATH.VUE_SYS_API + 'SysMenu/upload', this.formData, {
          headers: {
            Authorization: 'Bearer ' + store.state.user.token
          }
        })
        .then((res) => {
          if (res.data.code == 200) {
            this.hideUpload = true
            this.icon.approve = this.$tools.imgUrlTranslation(res.data.data)
            this.$message({
              showClose: true,
              message: res.data.msg,
              type: 'success'
            })
          } else {
            this.$message.error(res.data.msg)
          }
        })
    },
    fileChange2(file, fileList) {
      this.fileList2 = fileList
    },
    handleRemove2(file, fileList) {
      this.icon.pitch = undefined
      this.hideUpload2 = false
    },
    httpRequset2() {
      this.formData2 = new FormData()
      this.fileList2.forEach((item) => {
        this.formData2.append('file', item.raw)
      })
      axios
        .post(__PATH.VUE_SYS_API + 'SysMenu/upload', this.formData2, {
          headers: {
            Authorization: 'Bearer ' + store.state.user.token
          }
        })
        .then((res) => {
          if (res.data.code == 200) {
            this.icon.pitch = this.$tools.imgUrlTranslation(res.data.data)
            this.hideUpload2 = true
            this.$message({
              showClose: true,
              message: res.data.msg,
              type: 'success'
            })
          } else {
            this.$message.error(res.data.msg)
          }
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.rule-form {
  ::v-deep .el-form-item {
    margin-right: 50px;
  }
}
.top {
  border-left: 3px solid #759bfa;
  color: #759bfa;
  font-weight: 700;
  padding-left: 15px;
}
.ipt {
  width: 200px;
}
.buttom {
  position: absolute;
  bottom: 1.8%;
  height: 50px;
  line-height: 50px;
  width: calc(100% - 290px);
  background: rgb(255 255 255 / 80%);
  box-shadow: 0 0 12px 3px rgb(0 0 0 / 10%);
  text-align: right;
  padding-right: 10px;
}
::v-deep .el-upload--picture-card {
  border: 1px dashed #409eff !important;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.hide {
  ::v-deep .el-upload--picture-card {
    display: none;
  }
}
.hide2 {
  ::v-deep .el-upload--picture-card {
    display: none;
  }
}
</style>
