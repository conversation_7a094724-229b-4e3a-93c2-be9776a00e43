<template>
  <PageContainer :footer="true">
    <div slot="content">
      <div class="content_box">
        <div class="contentBox-left">
          <div class="basicBox">
            <div class="content-title">基础信息</div>
            <el-form ref="formInline" :model="formInline" :inline="true" class="form-inline" label-width="auto" :rules="rules">
              <el-form-item prop="notificationScope">
                <template slot="label">
                  <el-tooltip :content="filterText(formInline.notificationScope)" :disabled="!formInline.notificationScope" placement="top-start">
                    <span>通知范围 <i class="el-icon-warning-outline"></i></span>
                  </el-tooltip>
                </template>
                <el-select
                  v-model="formInline.notificationScope"
                  :disabled="activeType == 'detail' || activeType == 'edit'"
                  placeholder="请选择通知范围"
                  filterable
                  clearable
                  @change="changeScope"
                >
                  <el-option
                    v-for="item in alarmSystemList"
                    :key="item.thirdSystemCode"
                    :label="item.thirdSystemName"
                    :value="item.thirdSystemCode"
                    :disabled="item.flag"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="通知类型" prop="notificationType">
                <el-select v-model="formInline.notificationType" filterable :disabled="activeType == 'detail'" placeholder="请选择通知类型" class="ml-16" clearable>
                  <el-option v-for="item in alarmTypeList" :key="item.id" :label="item.alarmDictName" :value="item.id"> </el-option>
                </el-select>
              </el-form-item>
              <br />
              <div v-show="!!formInline.notificationScope" class="tagInfor">
                <div style="margin-left: 23px" @click="addType">
                  <el-button style="background-color: #e6effc; color: #3562db; border: #e6effc" type="primary" :disabled="activeType == 'detail'"
                    ><i class="el-icon-plus"></i> {{ formInline.notificationScope == '0' ? '选择不通知设备' : '选择通知设备' }}</el-button
                  >
                </div>
                <div>
                  <el-tag v-for="(item, index) in tagsList" :key="index" style="margin: 0px 5px" :closable="activeType != 'detail'" effect="plain" @close="handleClose(item)">
                    {{ item.assetName }}</el-tag
                  >
                </div>
              </div>
              <br />
              <el-form-item label="状态" style="margin-top: -15px">
                <el-radio v-model="formInline.status" label="0" :disabled="activeType == 'detail'">开启</el-radio>
                <el-radio v-model="formInline.status" label="1" :disabled="activeType == 'detail'">关闭</el-radio>
              </el-form-item>
              <br />
              <el-form-item label="备注" prop="remark">
                <el-input v-model="formInline.remark" type="textarea" style="width: 785px" :disabled="activeType == 'detail'"></el-input>
              </el-form-item>
              <br />
              <div class="content-title">设备设施提醒配置</div>
              <div class="configTitle">
                <img src="@/assets/images/parkingLot/warpper.png" alt="" />
                <!-- <span>使用期限到期时提醒配置 <img style="width: 16px; height: 16px" src="@/assets/images/parkingLot/info-circle.png" alt="" /></span> -->
                <span>使用期限到期时提醒配置 </span>
                <el-tooltip content="注：设备到期时提醒，即到设备设施寿命到期时间进行提醒，如2024.11.21到期，那么2024.11.21进行提醒即可" placement="top-start">
                  <span> <i class="el-icon-warning-outline"></i></span>
                </el-tooltip>
              </div>
              <br />
              <el-form-item label="是否开启提醒" style="margin-left: 15px">
                <el-radio v-model="formInline.enabled1" label="1" :disabled="activeType == 'detail'">关闭 </el-radio>
                <el-radio v-model="formInline.enabled1" label="0" :disabled="activeType == 'detail'">开启</el-radio>
              </el-form-item>
              <br />
              <div v-show="formInline.enabled1 == '0'" class="append_lable">
                <ConfigInfor ref="importantInfo" :atExpirationObj="atExpirationObj"></ConfigInfor>
              </div>
              <div class="configTitle">
                <img src="@/assets/images/parkingLot/warpper.png" alt="" />
                <span>使用期限到期前提醒配置</span>
                <el-tooltip
                  content="注：提前提醒，在备设施寿命到期前进行消息提醒，如：设备设施寿命到期前(  20   )分钟开启提醒，则在设备设施寿命到期前20分钟提醒一次。"
                  placement="top-start"
                >
                  <span> <i class="el-icon-warning-outline"></i></span>
                </el-tooltip>
              </div>
              <br />
              <el-form-item label="是否开启提醒" style="margin-left: 15px">
                <el-radio v-model="formInline.enabled2" label="1" :disabled="activeType == 'detail'">关闭</el-radio>
                <el-radio v-model="formInline.enabled2" label="0" :disabled="activeType == 'detail'">开启</el-radio>
              </el-form-item>
              <br />
              <div v-show="formInline.enabled2 == '0'" class="append_lable">
                <configInforQian ref="configInforQian" :beforeExpirationObj="beforeExpirationObj"></configInforQian>
              </div>
            </el-form>
          </div>
        </div>
        <!-- 设备弹窗 -->
        <addDispatForm v-if="Visible" ref="addDispatForm" :Visible="Visible" :projectIds="projectIds" @confirm="confirmInspectionPoint" @closePinot="closePinot"></addDispatForm>
      </div>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">取消</el-button>
      <el-button type="primary" :disabled="activeType == 'detail'" @click="submitForm()">保存</el-button>
    </div>
  </PageContainer>
</template>
<script>
import ConfigInfor from './configInfor.vue' // 使用期限到期时提醒配置
import configInforQian from './configInforQian.vue' // 使用期限到期前提醒配置
import addDispatForm from './addDispatForm.vue'
export default {
  name: 'addAlarmConfig',
  components: {
    ConfigInfor,
    addDispatForm,
    configInforQian
  },
  beforeRouteEnter(to, from, next) {
    if (to.meta.title) {
      const typeList = {
        add: '新增',
        edit: '编辑',
        detail: '详情'
      }
      to.meta.title = typeList[to.query.type] ?? '详情'
    }
    next()
  },
  data() {
    return {
      tagsList: [], // 选中的全部设备设施列表
      configList: [], // 选中的自定义设备列表
      alarmSystemList: [
        {
          thirdSystemName: '全部设备设施',
          thirdSystemCode: '0',
          flag: false
        },
        {
          thirdSystemName: '自定义设备',
          thirdSystemCode: '1',
          flag: false
        }
      ], // 通知范围
      alarmTypeList: [
        // 通知类型
        {
          alarmDictName: '寿命提醒',
          id: '0'
        }
      ],
      btnType: '0',
      formInline: {
        notificationScope: '', // 通知范围(0全部,1自定义)
        notificationType: '0', // 通知类型(0使用期限,)
        devices: '', // 设备ids(通知范围为0时是排除设备,为1时是自定义设备)
        status: '0', // 状态(0开启,1关闭)
        enabled1: '1', // 是否开启提醒(0关闭,1开启)
        enabled2: '1', // 是否开启提醒(0关闭,1开启)
        remark: '',
        typeList: []
      },
      rules: {
        notificationScope: { required: true, message: '请选择通知范围', trigger: 'change' },
        alarmSystemCode: { required: true, message: '请选择报警系统', trigger: 'change' },
        notificationType: { required: true, message: '请选择报警类型', trigger: 'change' },
        alarmLevel: { required: true, message: '请至少选择一个报警等级', trigger: 'change' }
      },
      activeType: '',
      Visible: false, // 设备弹窗
      byIds: '',
      atExpirationObj: {},
      beforeExpirationObj: {},
      projectIds: ''
    }
  },
  created() {
    this.getIds()
  },
  mounted() {
    const { type, id } = this.$route.query
    let flagSy = this.$route.query.disabledFlag
    if (flagSy) {
      this.alarmSystemList.forEach((el, index) => {
        if (flagSy == true) {
          if (index == 0) {
            el.flag = true
          }
        }
      })
    }
    this.activeType = type
    this.byIds = id
    id && this.getDetailData()
  },
  methods: {
    // 过滤提示
    filterText(val) {
      if (val) {
        return val == 0
          ? '全部设备设施（可选择不提醒设备），选择全部设备设施后不可在新增其它设置，通知范围选项禁用'
          : '自定义设备（选中的设备不可重复选中），选择自定义设备设施后不可在新增其它设置，通知范围选项只能选择自定义设备'
      }
      return ''
    },
    getIds() {
      this.$api.getDetids().then((res) => {
        const { code, data } = res
        if (code == 200) {
          this.projectIds = data
        }
      })
    },
    // 打开设备弹窗
    addType() {
      this.Visible = true
    },
    changeScope() {
      this.formInline.devices = ''
      this.tagsList = []
    },
    // 关闭设备弹窗
    closePinot() {
      this.Visible = false
    },
    // 确定选择设备
    confirmInspectionPoint(val) {
      if (val) {
        // 添加页面设备
        this.tagsList = [...this.tagsList, ...val]
        let list = val.map((item) => item.id)
        // let arr = this.formInline.devices.split(',')
        let newArr = [...new Set([...list])]
        this.formInline.devices = newArr.join(',')
        // 增加选择设备ids
        let arrList = this.projectIds.split(',')
        this.projectIds = [...new Set([...arrList, ...list])].join(',') || ''
      }
    },
    // 删除选择的设备
    handleClose(val) {
      const { id } = val
      let list = this.formInline.devices.split(',').filter((item) => item != id)
      this.formInline.devices = list.length > 0 ? list.join(',') : ''
      this.tagsList = this.tagsList.filter((item) => item.id != id)
    },
    // --------------------------------------------------------------
    // 获取详情
    getDetailData() {
      this.$api.getDetails({ id: this.byIds }).then((res) => {
        const { code, data } = res
        if (code == 200) {
          const { notificationScope, notificationType, devices, status, remark, typeList } = data
          this.formInline.notificationScope = notificationScope
          this.formInline.notificationType = notificationType
          this.formInline.devices = devices
          this.formInline.status = status
          this.formInline.remark = remark
          this.getDeviceList()
          // atExpirationObj // 到期时
          // beforeExpirationObj // 到期前
          typeList.forEach((item) => {
            if (item.expireNotificationType) {
              item.expireNotificationType = item.expireNotificationType.split(',')
            }
            item.customReceiverList = item.customReceiverList.map((receiver) => {
              receiver.departCodes = receiver.ids.split(',')
              return receiver
            })
            switch (item.configType) {
              case '0':
                this.atExpirationObj = { ...item }
                this.formInline.enabled1 = item.enabled
                break
              case '1':
                this.beforeExpirationObj = { ...item }
                this.formInline.enabled2 = item.enabled
                break
            }
          })
        }
      })
    },
    submitForm() {
      const importantForm = this.$refs.importantInfo
      const configInforQian = this.$refs.configInforQian
      let arr = []
      if (importantForm) {
        let list = JSON.parse(JSON.stringify(importantForm.generationContents))
        list.forEach((item) => {
          if (item.expireNotificationType) {
            item.expireNotificationType = item.expireNotificationType.join(',')
          }
          item.enabled = this.formInline.enabled1
        })
        arr.push(...list)
      }
      if (configInforQian) {
        let list = JSON.parse(JSON.stringify(configInforQian.generationContents))
        list.forEach((item) => {
          if (item.expireNotificationType) {
            item.expireNotificationType = item.expireNotificationType.join(',')
          }
          item.enabled = this.formInline.enabled2
        })
        arr.push(...list)
      }
      this.formInline.typeList = arr
      let params = {
        ...this.formInline,
        id: this.activeType === 'edit' ? this.byIds : ''
      }
      this.$refs['formInline'].validate((valid) => {
        if (!valid) return
        let fn = this.activeType === 'edit' ? 'getDetailsSave' : 'postConfigSave'
        this.$api[fn](params)
          .then((res) => {
            const { code } = res
            if (code == 200) {
              this.$message.success(res.message || '保存成功')
              this.$router.go(-1)
            } else {
              this.$message.error(res.message || '保存失败')
            }
          })
          .finally(() => {
            this.formInline.typeList = []
          })
      })
    },
    // 查询设备详情
    getDeviceList() {
      // this.spaceLoading = true
      const { devices } = this.formInline
      if (!devices) return
      this.$api
        .getDeviceList({
          containIds: devices, // 查询设备ids
          pageSize: 9999999,
          pageNo: 1
        })
        .then((res) => {
          const { code } = res
          if (code == '200') {
            const { list } = res.data
            this.tagsList = list
            // this.spaceLoading = false
          } else {
            // this.spaceLoading = false
            this.$message.error(res.message || '查询失败')
          }
        })
        .catch(() => {
          this.$message.error(res.message || '查询失败')
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.container-content > div {
  background-color: #fff;
  height: 100%;
  position: relative;
  .addAlarmConfig-content-title {
    border-bottom: 1px solid #e4e7ed;
    padding: 13px 24px;
    cursor: pointer;
    color: #333333;
  }
  .content_box {
    height: calc(100% - 60px);
    display: flex;
    .content-title {
      margin: 15px 0px;
      font-weight: bold;
      font-size: 16px;
      color: #333333;
    }
    .contentBox-left {
      height: 100%;
      width: 90%;
      overflow-y: auto;
    }
    .contentBox-right {
      flex: 1;
      > div {
        cursor: pointer;
        padding: 8px;
        font-size: 16px;
        color: #1d2129;
      }
      .isLight {
        color: #3562db;
      }
      .lineBox {
        display: inline-block;
        width: 3px;
        height: 24px;
        border-radius: 2px;
        margin-right: 10px;
        vertical-align: middle;
      }
      .lineStyle {
        background: #3562db;
      }
    }
  }
  .form-inline {
    .el-input,
    .el-select,
    .el-cascader {
      width: 340px;
    }
  }
}
.basicBox {
  width: 100%;
  padding: 10px 24px 0 24px;
}
.importantBox {
  padding: 10px 24px 10px 24px;
}
.exigencyBox,
.normalBox,
.informBox {
  padding: 10px 24px 10px 24px;
  border-top: 1px solid #ddd;
}
.configTitle {
  display: inline-block;
  background: #e6effc;
  margin: 10px 0px 10px 15px;
  padding: 3px 16px 3px 10px;
  border-radius: 0px 99px 99px 0px;
  font-size: 14px;
  color: #3562db;
  span {
    margin-left: 5px;
    img {
      vertical-align: text-bottom;
    }
  }
}
.alarmTitle {
  width: 128px;
  height: 22px;
  font-family: PingFang SC, PingFang SC;
  font-weight: bold;
  font-size: 16px;
  color: #333333;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.append_item {
  font-size: 14px;
  color: #333333;
  margin: 0px 0px 0px 10px;
}
::v-deep .el-tag--plain {
  background-color: #f6f5fa;
  border: #f6f5fa;
}
::v-deep .el-tag {
  color: #333333;
}
::v-deep .el-tag--plain .el-tag__close {
  color: #333333;
}
.tagInfor {
  display: flex;
}
</style>
