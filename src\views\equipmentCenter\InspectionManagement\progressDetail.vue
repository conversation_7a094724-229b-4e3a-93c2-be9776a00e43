<template>
  <div class="contentMain">
    <div class="topFilter">
      <div class="backBar">
        <span style="cursor: pointer" @click="$router.go(-1)">
          <i class="el-icon-arrow-left"></i>
          计划进度详情
        </span>
      </div>
      <!-- 任务状态 -->
      <div class="tabChange">
        <span v-for="(item, index) in tabList" :key="item.value" :class="btnTabType == index ? 'timeBtn active' : 'timeBtn'" @click="onTypeBtn(item)">{{ item.label }}</span>
      </div>
      <!-- 任务率 -->
      <div class="tasksNumber">
        <div class="item">
          <span style="margin-bottom: 5px">任务总数</span>
          <span style="color: #121f3e">{{ totalTasks || 0 }}</span>
        </div>
        <div class="item">
          <span style="margin-bottom: 5px">已完成</span>
          <span style="color: #08cb83">{{ completedQuantity || 0 }}</span>
        </div>
        <div class="item">
          <span style="margin-bottom: 5px">完成率</span>
          <span style="color: #ff9435">{{ completionRate || 0 }}%</span>
        </div>
      </div>
      <div v-if="tabType === 'task'" class="filterOption">
        <div class="leftInputs">
          <el-input v-model="filterData.dept" :placeholder="systemType == '2' ? '请输入保养部门' : '请输入巡检部门'"></el-input>
          <el-input v-model="filterData.person" :placeholder="systemType == '2' ? '请输入保养人员' : '请输入巡检人员'"></el-input>
          <el-select v-model="filterData.status" placeholder="请选择任务状态">
            <el-option v-for="item in statusOption" :key="item.id" :label="item.label" :value="item.id"> </el-option>
          </el-select>
          <el-date-picker v-model="filterData.date" value-format="yyyy-MM-dd" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
          </el-date-picker>
          <el-button type="primary" style="margin-left: 15px; background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db" @click="reset">重置</el-button>
          <el-button type="primary" style="font-size: 14px" @click="search">查询</el-button>
        </div>
        <el-button type="primary" icon="el-icon-upload2" @click="exportExcel">导出Excel</el-button>
      </div>
    </div>
    <div v-if="tabType === 'task'" class="tableWrap">
      <el-table v-loading="tableLoading" :data="taskList" style="width: 100%" height="calc(100% - 40px)" border>
        <el-table-column label="序号" type="index" width="50"> </el-table-column>
        <el-table-column prop="taskName" show-overflow-tooltip label="任务名称" width="200"> </el-table-column>
        <el-table-column prop="planName" show-overflow-tooltip label="所属计划" width="150"> </el-table-column>
        <el-table-column label="周期类型" width="80" align="center">
          <template slot-scope="scope">
            <span>{{ cycleTypeFn(scope.row.cycleType) }}</span>
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip :label="systemType == '2' ? '应保养日期' : '应巡日期'">
          <template slot-scope="scope">
            <span>{{ moment(scope.row.taskStartTime).format('YYYY-MM-DD') }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="distributionTeamName" show-overflow-tooltip :label="systemType == '2' ? '保养部门' : '巡检部门'"></el-table-column>
        <el-table-column prop="planPersonName" show-overflow-tooltip :label="systemType == '2' ? '保养人员' : '巡检人员'"></el-table-column>
        <el-table-column prop="totalCount" :label="systemType == '2' ? '应保养点数' : '应巡点数'" width="90" align="center"></el-table-column>
        <el-table-column prop="hasCount" :label="systemType == '2' ? '实保养点数' : '实巡点数'" width="90" align="center"></el-table-column>
        <el-table-column label="完成状态" width="90" align="center">
          <template slot-scope="scope">
            <div>
              <span v-if="scope.row.taskStatus == '1'" class="disable">
                <span></span>
                未完成
              </span>
              <span v-if="scope.row.taskStatus == '2'" class="enable">
                <span></span>
                已完成
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template slot-scope="scope">
            <div class="operation">
              <el-link type="primary" style="margin-right: 10px" @click="detail(scope.row, 'detail')">详情</el-link>
              <el-link
                v-if="!$route.query.taskStartTime"
                :disabled="scope.row.taskStatus != '1'"
                :type="scope.row.taskStatus == '1' ? 'primary' : 'info'"
                @click="editRow(scope.row, 'edit')"
              >编辑</el-link
              >
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="" style="padding-top: 10px; text-align: right">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="paginationData.currentPage"
          :page-sizes="[15, 30, 50, 100]"
          :page-size="paginationData.pageSize"
          :total="paginationData.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
    <div v-if="tabType === 'team'" class="tableWrap tableWrap1">
      <el-table v-loading="tableLoading" :data="taskList" style="width: 100%" height="calc(100% - 40px)" border>
        <el-table-column label="序号" type="index" width="100"> </el-table-column>
        <el-table-column prop="distributionTeamName" label="部门名称" min-width="200" show-overflow-tooltip></el-table-column>
        <el-table-column prop="taskCount" label="任务总数" min-width="160" show-overflow-tooltip></el-table-column>
        <el-table-column prop="accomplishCount" label="已完成" min-width="160" show-overflow-tooltip></el-table-column>
        <el-table-column prop="unfinishedCount" label="未完成" min-width="160" show-overflow-tooltip></el-table-column>
        <el-table-column prop="finishingRate" label="完成率" min-width="160" show-overflow-tooltip></el-table-column>
      </el-table>
      <div class="" style="padding-top: 10px; text-align: right">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="paginationData.currentPage"
          :page-sizes="[15, 30, 50, 100]"
          :page-size="paginationData.pageSize"
          :total="paginationData.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
import moment from 'moment'
import axios from 'axios'
export default {
  name: 'progressDetail',
  components: {},
  async beforeRouteLeave(to, from, next) {
    if (!['planManagement', 'planProgressDetail'].includes(to.name)) {
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      moment,
      systemType: '',
      planId: '',
      filterData: {
        dept: '',
        person: '',
        status: '0',
        date: ''
      },
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      // 周期类型
      cycleTypeList: [
        {
          cycleType: 8,
          label: '单次'
        },
        {
          cycleType: 6,
          label: '每日'
        },
        {
          cycleType: 0,
          label: '每周'
        },
        {
          cycleType: 2,
          label: '每月'
        },
        {
          cycleType: 3,
          label: '季度'
        },
        {
          cycleType: 5,
          label: '全年'
        },
        {
          cycleType: 7,
          label: '自定义'
        }
      ],
      statusOption: [
        {
          id: '2',
          label: '已完成'
        },
        {
          id: '1',
          label: '未完成'
        },
        {
          id: '0',
          label: '全部'
        }
      ],
      taskList: [],
      tableLoading: false,
      tabList: [
        {
          value: '0',
          label: '任务',
          type: 'task'
        },
        {
          value: '1',
          label: '班组',
          type: 'team'
        }
      ],
      tabType: 'task',
      btnTabType: '0',
      totalTasks: 0, // 任务总数
      completedQuantity: 0, // 已完成
      completionRate: 0 // 完成率
    }
  },
  created() {
    if (!this.$store.state.keepAlive.list.includes('planManagement')) {
      this.initEvent()
    }
  },
  activated() {
    this.initEvent()
  },
  methods: {
    initEvent() {
      Object.assign(this.$data, this.$options.data())
      if (this.$route.path.indexOf('/InspectionManagement') != -1) {
        this.systemType = '1'
      } else if (this.$route.path.indexOf('/MaintenanceManagement') != -1) {
        this.systemType = '2'
      } else if (this.$route.path.indexOf('/comInspectionManagement') != -1) {
        this.systemType = '3'
      } else if (this.$route.path.indexOf('/hazardousChemicalGoods_inspection') != -1) {
        this.systemType = '4'
      }
      this.planId = this.$route.query.id || ''
      this.planTypeId = this.$route.query.menuType || ''
      this.filterData.date = this.$route.query.taskStartTime?.concat(',' + this.$route.query.taskEndTime).split(',') || []
      this.systemType = this.systemType || this.$route.query.type
      this.getProgressList()
    },
    // 按任务
    getProgressList() {
      this.tableLoading = true
      const params = {
        pageNo: this.paginationData.currentPage,
        pageSize: this.paginationData.pageSize,
        planName: '',
        planPersonName: this.filterData.person,
        departmentName: this.filterData.dept,
        taskStatus: this.filterData.status == '0' ? '' : this.filterData.status,
        planId: this.planId,
        taskStartTime: this.filterData.date[0] ? this.filterData.date[0] + ' 00:00:00' : '',
        taskEndTime: this.filterData.date[1] ? this.filterData.date[1] + ' 23:59:59' : '',
        systemCode: this.systemType, // 1:设备 2: 保养 3：巡检
        planTypeId: this.planTypeId
      }
      // 判断是否从工作日历进
      if (this.$route.query.type) {
        params.type = '0'
      }
      this.tableLoading = true
      this.$api.getTaskMaintaninList(params).then((res) => {
        if (res.code == '200') {
          this.taskList = res.data.list
          this.paginationData.total = res.data.sum
          this.totalTasks = res.data.sum
          this.completedQuantity = res.data.finishedCount
          this.completionRate = res.data.sum === 0 ? 0 : ((this.completedQuantity / this.totalTasks) * 100).toFixed(2)
        }
        this.tableLoading = false
      })
    },
    // 按部门统计列表
    getTableByDept() {
      this.tableLoading = true
      const params = {
        systemCode: this.systemType, // 1:设备 2: 保养 3：巡检
        pageNo: this.paginationData.currentPage,
        pageSize: this.paginationData.pageSize,
        taskStartTime: this.filterData.date[0] ? this.filterData.date[0] : '',
        taskEndTime: this.filterData.date[1] ? this.filterData.date[1] : '',
        planId: this.planId
      }
      this.$api.getChratsByDepartment(params).then((res) => {
        if (res.code == '200') {
          this.taskList = res.data.list
          this.paginationData.total = parseInt(res.data.sum)
          this.totalTasks = res.data.allCount
          this.completedQuantity = res.data.finishedCount
          this.completionRate = res.data.sum === 0 ? 0 : ((this.completedQuantity / this.totalTasks) * 100).toFixed(2)
        } else {
          this.$message.error(res.message || '请求失败')
        }
        this.tableLoading = false
      })
    },
    // 周期类型
    cycleTypeFn(cycleType) {
      const item = this.cycleTypeList.find((i) => i.cycleType == cycleType)
      return item.label
    },
    detail(row, type) {
      sessionStorage.setItem('row', JSON.stringify(row))
      this.$router.push({
        path: 'planProgressDetail',
        query: {
          taskId: row.id,
          row,
          type
        }
      })
    },
    editRow(row, type) {
      this.$router.push({
        path: 'planProgressDetail',
        query: {
          taskId: row.id,
          row,
          type
        }
      })
    },
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.paginationData.currentPage = 1
      this.getProgressList()
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.getProgressList()
    },
    reset() {
      this.initEvent()
    },
    search() {
      this.getProgressList()
    },
    // 任务类型切换
    onTypeBtn(val) {
      this.paginationData = {
        currentPage: 1,
        pageSize: 15,
        total: 0
      }
      this.btnTabType = val.value
      this.tabType = val.type
      if (this.tabType === 'task') {
        this.getProgressList()
      } else if (this.tabType === 'team') {
        this.getTableByDept()
      }
    },
    exportExcel() {
      const userInfo = this.$store.state.user.userInfo.user
      const params = {
        unitCode: userInfo.unitCode ?? 'BJSYGJ',
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        platformFlag: 1,
        planId: this.planId,
        taskName: '',
        planPersonName: this.filterData.person,
        departmentName: this.filterData.dept,
        taskStatus: this.filterData.status == '0' ? '' : this.filterData.status,
        pageNo: this.paginationData.currentPage,
        pageSize: this.paginationData.pageSize,
        taskStartTime: this.filterData.date[0] ? this.filterData.date[0] + ' 00:00:00' : '',
        taskEndTime: this.filterData.date[0] ? this.filterData.date[1] + ' 23:59:59' : '',
        systemCode: this.systemType // 1:设备 2: 保养 3：巡检
      }
      // 判断是否从工作日历进
      if (this.$route.query.type) {
        params.type = '0'
      }
      const formData = new FormData()
      Object.keys(params).forEach((key) => {
        formData.append(key, params[key])
      })
      axios({
        method: 'post',
        url: __PATH.VUE_ICIS_API + 'planTaskNew/taskExport',
        data: formData,
        responseType: 'blob',
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      })
        .then((res) => {
          const name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
          const blob = new Blob([res.data])
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
        })
        .catch(() => {
          this.$message.error('导出失败')
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.contentMain {
  height: 100%;
  .topFilter {
    margin: 15px 15px 0 15px;
    padding: 15px;
    width: calc(100% - 30px);
    background-color: #fff;
    .backBar {
      color: #121f3e;
      height: 30px;
    }
    .filterOption {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .leftInputs {
        display: flex;
        align-items: center;
        :deep(.el-input) {
          width: 200px;
          margin-right: 15px;
        }
        :deep(.el-select) {
          margin-right: 15px;
        }
      }
    }
  }
  .tableWrap {
    margin: 0 15px;
    padding: 15px;
    height: calc(100% - 290px);
    background-color: #fff;
    .completeSattus {
      .statusPoint {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
      }
    }
    .incomplete {
      .statusPoint {
        background-color: #fa403c;
      }
      color: #fa403c;
    }
    .complete {
      .statusPoint {
        background-color: #08cb83;
      }
      color: #08cb83;
    }
  }
}
.tableWrap1 {
  height: calc(100% - 258px) !important;
}
.tabChange {
  margin-top: 10px;
  text-align: center;
  .timeBtn {
    display: inline-block;
    width: 80px;
    height: 30px;
    line-height: 30px;
    margin-right: 0;
    background-color: #ededf5;
    border-radius: 2px;
    font-size: 14px;
    cursor: pointer;
  }
  .active {
    background-color: #3562db;
    color: #fff;
  }
}
.tasksNumber {
  display: flex;
  justify-content: space-between;
  margin: 10px 30px;
  .item {
    padding: 20px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    span {
      font-size: 20px;
      font-weight: 700;
    }
  }
}
</style>
