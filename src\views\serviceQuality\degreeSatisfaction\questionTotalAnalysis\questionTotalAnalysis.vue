<template>
  <div class="main-container">
    <div class="whole">
      <div class="page-title">交叉分析</div>
      <el-row style="border-bottom: 1px solid #d8dee7;">
        <el-col :md="16" :lg="16" :xl="14" style="height: auto;">
          <BreadcrumbNavBar />
        </el-col>
        <el-col :md="8" :lg="8" :xl="10">
          <HeaderButton />
        </el-col>
      </el-row>
      <div class="main-container">
        <!-- 条件选择框 -->
        <div class="conditionBox">
          <el-card class="box-card">
            <!-- 条件外层div -->
            <div>
              <span v-for="(item, index) in arr" :key="index" class="circularArray">
                <span>{{ item.title }}:</span>
                <el-select v-model="item.result" class="selectBox" size="mini" placeholder="请选择">
                  <el-option v-for="item in options" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
              </span>
              <el-button type="primary" size="mini" @click="querySelecrt">生成报表</el-button>
            </div>
          </el-card>
        </div>
        <!-- 条件选择框  结束 -->
        <div v-if="seeFlag">
          <!-- 表格盒子 -->
          <div v-if="count != 0" class="tableBox">
            <table class="tableCss" style="width: 100%;" cellspacing="0" cellpadding="0" border="1">
              <thead>
                <!-- 表头 -->
                <tr>
                  <th rowspan="3" colspan="2">问题A/问题B</th>
                  <th :colspan="dataCks.optionBList.length * tabltBasic.length">{{ dataCks.optionBName }}</th>
                  <th rowspan="3" colspan="2">横向合计</th>
                </tr>
                <!-- 循环选项 -->
                <tr>
                  <th v-for="(item, index) in dataCks.optionBList" :key="index" colspan="3">{{ item.name }}</th>
                </tr>
                <!-- 循环选项结束 -->
                <!-- 循环选项数量和交叉比 -->
                <tr>
                  <th v-for="index of dataCks.optionBList.length * tabltBasic.length" :key="index">{{ returnName(index) }}</th>
                </tr>
                <!-- 循环选项数量和交叉比 结束 -->
                <!-- 表头结束 -->
              </thead>
              <tbody>
                <!-- 循环展示问题A的内容 -->
                <tr v-for="(item, index) in dataCks.tableList" :key="index">
                  <th v-if="index == 0" :rowspan="dataCks.tableList.length">{{ dataCks.optionAName }}</th>
                  <td>{{ item.optionAName }}</td>
                  <td v-for="indexs of item.crossAnalyses.length * tabltBasic.length" :key="indexs">{{ returnValue(indexs, item.crossAnalyses) }}</td>
                  <td>{{ item.total }}</td>
                  <td>{{ strAddSymbol(item.totalRowProp) }}</td>
                </tr>
                <!-- 循环展示问题A的内容结束 -->
                <!-- 纵向合计 -->
                <tr>
                  <th colspan="2">纵向合计</th>
                  <th v-for="(item, index) in dataCks.rowTotal.list" :key="index" colspan="3">{{ item.total }}({{ strAddSymbol(item.totalRowProp) }})</th>
                  <th>{{ dataCks.rowTotal.total }}</th>
                  <th>{{ strAddSymbol(dataCks.rowTotal.totalRowProp) }}</th>
                </tr>
                <!-- 纵向合计结束 -->
              </tbody>
            </table>

            <!-- 表格盒子 结束 -->
            <!-- 饼状图盒子 -->
            <div class="pieBox">
              <div class="pieBoxTitle">交叉项占总比统计图</div>
              <ve-pie :settings="pieChartSettings" :legend-visible="false" :data="pieChart"></ve-pie>
            </div>
            <!--  饼状图盒子 结束 -->
            <hr style="height: 1px; border: none; border-top: 1px solid #555;" />
            <!-- 柱状图盒子 -->
            <div>
              <div class="histogramBoxTitle">横向分类统计图</div>
              <ve-histogram :toolbox="toolbox" :data="chartData" :settings="chartSettings"></ve-histogram>
            </div>
          </div>
          <div v-else class="tableBox">
            <div class="countBox">
              <span>请先收集答卷</span>
            </div>
          </div>
          <!-- 柱状图盒子 结束 -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import utils from '../utils/utils'
import VePie from 'v-charts/lib/pie'
import VeHistogram from 'v-charts/lib/histogram'
import BreadcrumbNavBar from '../component/BreadcrumbNavBar/BreadcrumbNavBar.vue'
import HeaderButton from '../questionDesign/component/HeaderButton/HeaderButton.vue'
export default {
  components: {
    BreadcrumbNavBar,
    HeaderButton,
    VePie,
    VeHistogram
  },
  data() {
    this.toolbox = {
      // 设置工具箱
      feature: {
        // 自定义工具栏
        saveAsImage: {
          name: '横向分类统计图'
        } // 设置导出
      }
      // show: true,
    }
    // 饼状图
    this.pieChartSettings = {
      radius: 130, // 谁知饼状图员的 半径
      offsetY: 195 // 设置饼状图距离上边的距离
    }
    this.chartSettings = {
      stack: { demo: [] }
    }
    return {
      pvqid: localStorage.getItem('questId'), // 获取问卷id
      seeFlag: false, // 表格和饼状图展示隐藏标识
      dataCks: {}, // 表格展示数据结果集
      count: 0, // 答卷数量
      tabltBasic: [
        // 每个选项 展示 三列 配置对象  title展示标题名  key要取的健值  percentConversion 是否拼接%号
        { title: '横向比率', key: 'rowProp', percentConversion: true },
        { title: '数量', key: 'answerCnt' },
        { title: '交叉项占总比', key: 'crossProp', percentConversion: true }
      ],
      pieChart: {
        // 饼状图数据
        columns: ['name', 'value'],
        rows: []
      },
      chartData: {
        // 柱状图数据
        columns: [],
        rows: []
      },
      options: [], // 选项数组
      arr: [
        // 配置数组
        {
          key: 'questionIda',
          title: '问题A',
          result: ''
        },
        {
          key: 'questionIdb',
          title: '问题B',
          result: ''
        }
      ]
    }
  },
  created() {
    var date = {
      questionId: this.pvqid,
      userId: this.$store.state.user.userInfo.userId,
      userName: this.$store.state.user.userInfo.username
    }
    this.$api.getCrossQuestion(date).then((res) => {
      if (res.status == 200) {
        this.options = res.data
      } else {
        this.$message.error(res.message)
      }
    })
  },
  methods: {
    // 字符串拼接%
    strAddSymbol(str) {
      return `${str}%`
    },
    // 返回选项名称
    returnName(idnex) {
      var indexFlaf = idnex % 3
      return this.tabltBasic[indexFlaf].title
    },

    // 返回选项内容
    returnValue(idnex, arr) {
      var obj = arr[Math.floor((idnex - 1) / 3)]
      var indexFlaf = idnex % 3
      if (this.tabltBasic[indexFlaf].percentConversion) {
        return this.strAddSymbol(obj[this.tabltBasic[indexFlaf].key])
      } else {
        return obj[this.tabltBasic[indexFlaf].key]
      }
    },
    // 校验
    checkquerySelecrt() {
      return utils.verificationIsNotEmpty(this.arr, [''], 'result', 'title')
    },
    getQuestionInfo(arr, result, key) {
      var obj = {}
      for (var a = 0; a < arr.length; a++) {
        obj[arr[a][key]] = arr[a][result]
      }
      return obj
    },
    // 封装请求
    // request(str, fun, date) {
    //   axiosConter.centralControl(
    //     str,
    //     (data) => {
    //       fun(data)
    //     },
    //     date
    //   )
    // },
    // 查询 问题结果
    querySelecrt() {
      var obj = this.checkquerySelecrt()
      if (obj['flag']) {
        this.$message({ type: 'warning', message: '请选择' + obj['message'] })
      } else {
        var data = this.getQuestionInfo(this.arr, 'result', 'key')
        // 发起查询--表格
        this.$api.crossList(data).then((res) => {
          if (res.status == 200) {
            this.dataCks = res.data
            this.count = res.data.answerCnt.answerCount
            this.seeFlag = true
            this.$api.crossTotalCharts(data).then((res) => {
              if (res.status == 200) {
                this.pieChart.rows = res.data['valueList']
                this.seeFlag = true
              } else {
                this.$message.error(res.message)
              }
            })
            this.$api.crossColCharts(data).then((res) => {
              if (res.status == 200) {
                this.chartSettings['stack']['demo'] = res.data.stackList
                this.chartData.rows = res.data.rows
                this.chartData.columns = res.data.columns
                this.seeFlag = true
              } else {
                this.$message.error(res.message)
              }
            })
          } else {
            this.$message.error(res.message)
          }
        })

        // this.request(
        //   'getCrossList',
        //   (data) => {
        //     if (data.code == 200) {
        //       this.dataCks = data.data
        //       this.count = data.data.answerCnt.answerCount
        //       this.seeFlag = true
        //       //发起查询--饼状图
        //       this.request(
        //         'pieChart',
        //         (data) => {
        //           if (data.code == 200) {
        //             this.pieChart.rows = data.data['valueList']
        //             this.seeFlag = true
        //           } else {
        //             this.$message.error(data.message)
        //           }
        //         },
        //         date
        //       )
        //       //发起查询--柱状图
        //       this.request(
        //         'histogram',
        //         (data) => {
        //           if (data.code == 200) {
        //             this.chartSettings['stack']['demo'] = data.data.stackList
        //             this.chartData.rows = data.data.rows
        //             this.chartData.columns = data.data.columns
        //             this.seeFlag = true
        //           } else {
        //             this.$message.error(data.message)
        //           }
        //         },
        //         date
        //       )
        //     } else {
        //       this.$message.error(data.message)
        //     }
        //   },
        //   date
        // )
      }
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
.main-container {
  font-size: 14px;
  height: calc(100vh - 200px);
  overflow-y: scroll;
  padding: 20px;
  margin: 0 !important;

  .whole {
    width: 100%;
    height: 100%;
    border-radius: 5px;
    background-color: #fff !important;

    .page-title {
      height: 50px;
      line-height: 50px;
      color: #606266;
      font-size: 14px;
      padding-left: 25px;
      border-bottom: 1px solid #d8dee7;
    }

    .submit-container {
      display: flex;
      justify-content: center;
      padding-bottom: 20px;
    }

    .el-button--text {
      color: #fff;
      padding: 12px 10px;
    }

    .el-button--text:hover,
    .el-button:hover {
      color: #fff;
      background-color: #5188fc;
      border-radius: 0;
      border-color: transparent;
    }
  }
}

@media screen and (max-width: 1366px) {
  .main-container .el-button--text {
    color: #fff;
    padding: 0 15px;
  }
}

.quesM-title {
  height: 50px;
  line-height: 50px;
  color: #606266;
  font-size: 14px;
  padding-left: 25px;
  border-bottom: 1px solid #d8dee7;
}

.tableCss {
  margin-top: 20px;
}

table,
td {
  border: 1px solid #ccc;
  border-collapse: collapse;
  font-weight: normal;
  vertical-align: middle;
  height: 35px;
  text-align: center;
}

table,
th {
  border: 1px solid #ccc;
  border-collapse: collapse;
  font-weight: 600;
  height: 35px;
  vertical-align: middle;
}

.conditionBox {
  min-width: 50px;
}

.box-card {
  height: 60px;
}

.selectBox {
  width: 300px;
  height: 10px;
}

.circularArray {
  margin-right: 20px;
}

.pieBoxTitle {
  text-align: center;
  color: #2cc7c5;
  font-size: 25px;
  margin-top: 20px;
}

.histogramBoxTitle {
  color: #2cc7c5;
  font-size: 25px;
  margin-top: 20px;
}

.el-table td {
  padding: 0 !important;

  .cell {
    line-height: 40px;
  }

  .cell span {
    border-right: 1px solid #ebeef5;
    padding: 10px 7px;
    width: 100px;
    display: inline-block;
  }

  .cell span:last-child {
    border: none;
  }
}

.countBox {
  font-size: 20px;
  font-weight: 300;
}

.el-button--success {
  color: #fff;
  background-color: #5188fc;
  border-color: #5188fc;
}

::v-deep .el-card__body {
  padding: 15px 20px !important;
}
</style>
