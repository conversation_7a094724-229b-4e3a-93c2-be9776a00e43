<template>
  <PageContainer>
    <div slot="content" class="role-content" style="height: 100%">
      <div class="approve-manage-list-view">
        <div class="header-box">
          <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane :label="`待配房（${unallocatedRoomNum}）`" name="wingList"></el-tab-pane>
            <el-tab-pane :label="`待入住（${stayCheckInNum}）`" name="treatCheckInList"></el-tab-pane>
            <el-tab-pane :label="`已入住（${checkedInNum}）`" name="alreadyCheckedInList"></el-tab-pane>
            <el-tab-pane :label="`轮候（${waitQueue}）`" name="waitingList"></el-tab-pane>
            <el-tab-pane :label="`弃选（${abandon}）`" name="abandonSelectionList"></el-tab-pane>
          </el-tabs>
        </div>
        <div class="table-content">
          <component :is="activeName" ref="contentRef" style="padding: 0 16px 0 0" large @updateStatistics="updateStatistics"></component>
        </div>
      </div>
    </div>
  </PageContainer>
</template>
  <script>
import wingList from './wingList.vue'
import treatCheckInList from './treatCheckInList.vue'
import alreadyCheckedInList from './alreadyCheckedInList.vue'
import waitingList from './waitingList.vue'
import abandonSelectionList from './abandonSelectionList.vue'
export default {
  components: {
    wingList,
    treatCheckInList,
    alreadyCheckedInList,
    waitingList,
    abandonSelectionList
  },
  data() {
    return {
      activeName: 'wingList',
      unallocatedRoomNum: '0',
      stayCheckInNum: '0',
      checkedInNum: '0',
      abandon: '0',
      waitQueue: '0'
    }
  },
  mounted() {
    this.getStatisticsCount()
  },
  methods: {
    updateStatistics() {
      this.getStatisticsCount()
    },
    handleClick() {
      this.getStatisticsCount()
    },
    /** 获取统计数量 */
    getStatisticsCount() {
      const userInfo = this.$store.state.user.userInfo.user
      let params = {
        userName: userInfo.staffName,
        userId: userInfo.staffId
      }
      this.$api.rentalHousingApi
        .getRoomAllocationCount(params)
        .then((res) => {
          if (res.code == 200) {
            this.unallocatedRoomNum = res.data.toBeAssigned || 0
            this.stayCheckInNum = res.data.toBeChecked || 0
            this.checkedInNum = res.data.inUse || 0
            this.abandon = res.data.abandon || 0
            this.waitQueue = res.data.waitQueue || 0
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取数量失败'))
        .finally(() => {})
    }
  }
}
</script>
  <style lang="scss" scoped>
.approve-manage-list-view {
  padding: 10px 15px 15px;
  box-sizing: border-box;
  height: 100%;
  background: #fff;
  .table-content {
    height: calc(100% - 50px);
    margin-top: 10px;
  }
}
::v-deep .el-icon-time {
  position: relative;
}
::v-deep .el-icon-time::before {
  position: absolute;
  top: -3px;
  left: 4px;
}
</style>