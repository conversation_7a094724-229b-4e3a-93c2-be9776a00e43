<!-- 运行监测 -->
<template>
  <ContentCard :title="item.componentTitle" :scrollbarHover="true" :hasIcon="false" :cstyle="{ height: '100%' }"
    class="drag_class" :hasMoreOper="['edit']"
    @more-oper-event="(val) => $emit('all-more-Oper', val, 'operationMonitor')">
    <div slot="content" class="operation-list">
      <div v-for="item in operationList" :key="item.id" class="list-item">
        <div class="item-left">
          <!-- <img class="left-icon" :src="iconType[item.entityTypeId]"> -->
          <img class="left-icon" src="@/assets/images/monitor/air_icon.png">
          <div class="left-info">
            <p class="info-name">{{ item.entityTypeName }}</p>
            <p style="margin-top: 8px;">
              <span class="info-num">{{ item.allCount }}</span>
              <span class="info-unit">个</span>
            </p>
          </div>
        </div>
        <div class="item-right">
          <el-progress type="circle" :width="67" :stroke-width="10" strokeLinecap="" color="#3562DB"
            define-back-color="#CCCED3" :show-text="false" :percentage="percentage(item.normalCount, item.allCount)" />
          <div class="right-info">
            <p class="info-box">
              <span class="info-chart" style="background-color: #3562db;"></span>
              <span class="info-name">开启</span>
              <span class="info-num" style="color: #3562db;">{{ item.normalCount }}</span>
              <span class="info-unit">个</span>
            </p>
            <p class="info-box" style="margin-top: 14px;">
              <span class="info-chart" style="background-color: #CCCED3;"></span>
              <span class="info-name">关闭</span>
              <span class="info-num">{{ item.offLineCount }}</span>
              <span class="info-unit">个</span>
            </p>
          </div>
        </div>
      </div>
    </div>
  </ContentCard>
</template>

<script>
import gykgg_icon from '@/assets/images/monitor/gykgg_icon.png'
import dykgg_icon from '@/assets/images/monitor/dykgg_icon.png'
import byq_icon from '@/assets/images/monitor/byq_icon.png'
import zlp_icon from '@/assets/images/monitor/zlp_icon.png'
export default {
  name: 'deviceOperationMonitor',
  props: {
    item: {
      type: Object,
      default: () => { }
    },
    projectCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      iconType: {
        30028: gykgg_icon,
        30029: dykgg_icon,
        30030: byq_icon,
        30405: zlp_icon
      },
      operationList: []
    }
  },
  computed: {

  },
  watch: {
    projectCode: {
      handler(val, oldVal) {
        this.getEquipmentOperationMonitor()
      },
      deep: true
    }
  },
  created() {
    this.getEquipmentOperationMonitor()
  },
  methods: {
    percentage(num, denom) {
      if (num && denom && denom !== 0) {
        return (num / denom) * 100
      } else {
        return 0
      }
    },
    getEquipmentOperationMonitor() {
      this.$api.airConditionerOnOrOff({ projectCode: this.projectCode }).then((res) => {
        if (res.code == 200) {
          this.operationList = res.data.OnOrOff
        }
      })
    }
  }
}

</script>

<style lang="scss" scoped>
.operation-list {
  height: 100%;
  display: flex;
  flex-direction: column;

  p {
    margin: 0;
  }

  .list-item {
    background-color: #faf9fc;
    border-radius: 4px;
    padding: 16px;
    margin-top: 16px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    .item-left {
      display: flex;
      align-items: center;

      .left-icon {
        width: 60px;
        height: 60px;
      }

      .left-info {
        margin-left: 16px;

        .info-name {
          font-size: 15px;
          font-weight: 500;
          color: #121f3e;
          line-height: 18px;
        }

        .info-num {
          font-size: 30px;
          font-weight: bold;
          color: #121f3e;
          line-height: 36px;
        }

        .info-unit {
          font-size: 15px;
          font-weight: 500;
          color: #ccced3;
          line-height: 18px;
          margin-left: 4px;
        }
      }
    }

    .item-right {
      display: flex;
      align-items: center;

      .info-box {
        display: flex;
        align-items: center;
        margin-left: 20px;
      }

      .info-chart {
        display: inline-block;
        width: 8px;
        height: 8px;
      }

      .info-name {
        font-size: 14px;
        font-weight: 500;
        color: #121f3e;
        margin-left: 6px;
      }

      .info-num {
        font-size: 18px;
        font-weight: bold;
        margin-left: 10px;
        color: #414653;
      }

      .info-unit {
        font-size: 15px;
        font-weight: 500;
        color: #ccced3;
        margin-left: 4px;
      }
    }
  }

  .list-item:first-child {
    margin: 0;
  }
}
</style>
