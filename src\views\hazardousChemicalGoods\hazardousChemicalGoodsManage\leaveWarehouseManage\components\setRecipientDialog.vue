<template>
  <div class="dialog-continer">
    <el-dialog title="设置常用领用人" width="50%" v-dialogDrag :visible.sync="isSelectPers" custom-class="model-dialog" :before-close="closeDialog">
      <div class="content">
        <div class="search-container">
          <el-input v-model="searchStaffName" placeholder="请输入姓名" clearable style="width: 150px; margin-right: 10px"></el-input>
          <el-input v-model="searchMobile" placeholder="请输入手机号" clearable style="width: 150px; margin-right: 10px"></el-input>
          <el-button type="primary" plain @click="reset">重置</el-button>
          <el-button type="primary" @click="inquiry">查询</el-button>
        </div>
        <div class="table-container">
          <el-table
            v-loading="tableLoadingStatus"
            height="100%"
            @selection-change="handleSelectionChange"
            style="width: 100%"
            :data="tableData"
            :border="true"
            stripe
            class="tableAuto"
            row-key="id"
            ref="table"
          >
            <el-table-column type="selection" width="55" align="center" :reserve-selection="true"> </el-table-column>
            <el-table-column type="index" label="序号" prop="" width="50" align="center">
              <template slot-scope="scope">
                <span>{{ (currentPage - 1) * pageSize + scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="staffName" label="人员姓名" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="mobile" label="手机号" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="officeName" label="所属部门" show-overflow-tooltip> </el-table-column>
          </el-table>
        </div>
        <div class="pagination-container">
          <el-pagination
            :current-page="currentPage"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            :page-size="pageSize"
            :page-sizes="[5, 10, 15, 20]"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" plain @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="submitDialog">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
export default {
  props: {
    isSelectPers: {
      type: Boolean,
      default: false
    },
    officeId: {
      type: String,
      default: ''
    },
    defaultSelectedUser: {
      // 默认选中数据
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      tableLoadingStatus: false,
      searchStaffName: '',
      searchMobile: '',
      currentPage: 1,
      total: 0,
      pageSize: 20,
      tableData: [],
      multipleSelection: []
    }
  },
  computed: {
    showDialog: {
      get() {
        return this.isSelectPers
      },
      set(val) {
        this.$emit('updateVisible', val)
      }
    }
  },
  mounted() {
    this.getLersonnelList()
  },
  methods: {
    reset() {
      this.searchMobile = ''
      this.searchStaffName = ''
      this.currentPage = 1
      this.getLersonnelList()
    },
    inquiry() {
      this.currentPage = 1
      this.getLersonnelList()
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getLersonnelList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getLersonnelList()
    },
    // table多选
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 获取全部人员信息
    getLersonnelList() {
      let params = {
        current: this.currentPage,
        size: this.pageSize,
        staffName: this.searchStaffName, // 姓名
        mobile: this.searchMobile, // 手机号
        officeId: this.officeId // 领用部门ID
      }
      this.$api.staffList(params).then((res) => {
        if (res.code == 200) {
          res.data.records.forEach((item) => {
            if (item.sex == 2) {
              item.sex = '女'
            } else if (item.sex == 1) {
              item.sex = '男'
            }
            if (item.stationStatus == 0) {
              item.stationStatus = '在职'
            } else {
              item.stationStatus = '离职'
            }
          })
          this.tableData = res.data.records
          this.total = res.data.total
          this.defaultSelectedUser.length &&
            this.defaultSelectedUser.forEach((item) => {
              this.tableData.forEach((v, i) => {
                if (item == v.id) {
                  this.$refs.table.toggleRowSelection(v)
                }
              })
            })
        }
      })
    },
    // 取消弹窗
    closeDialog() {
      this.$refs.table.clearSelection()
      this.$emit('updateVisible', false)
    },
    // 确定
    submitDialog() {
      if (this.multipleSelection.length == 0) {
        this.$message({
          showClose: true,
          message: '至少选择一个人员',
          type: 'warning'
        })
      } else {
        this.$emit('advancedSearchFn', this.multipleSelection)
        this.$refs.table.clearSelection()
        this.$emit('updateVisible', !this.isSelectPers)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.model-dialog .content {
  background: #fff;
  border-radius: 4px;
  width: 100%;
  height: 530px;
  padding: 16px;
  .search-container {
    margin-top: 10px;
  }
  .table-container {
    height: calc(100% - 100px);
    margin: 10px 0px;
  }
}
</style>