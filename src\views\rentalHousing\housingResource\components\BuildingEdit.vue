<script>
import { FloorNameOptions, FloorNameType } from '@/views/rentalHousing/housingResource/constant'
export default {
  name: 'BuildingEdit',
  props: {
    visible: <PERSON>olean,
    id: String,
    roomCount: Number,
    // 是否楼栋单元操作
    isCell: Boolean,
    parentId: String
  },
  events: ['update:visible', 'success'],
  data: () => ({
    formModel: {
      // 楼栋名称
      buildingName: '',
      // 单元名称
      cellName: '',
      // 单元数量
      cellCount: 1,
      // 地上层数
      upGroundCount: 1,
      // 地下层数
      underGroundCount: 0,
      // 楼层命名方式
      floorNameType: '1',
      // 楼层名称
      floorNames: []
    },
    rules: {
      buildingName: [{ required: true, message: '请输入楼栋编号' }],
      cellName: [{ required: true, message: '请输入单元名称' }]
    },
    dictData: [],
    loadingStatus: false,
    // 人员选择dialog显示
    userDialog: false,
    // timerId
    $timerId: -1,
    // 只读模式
    readonly: false,
    // 上次选中的命名规则
    $lastNameType: FloorNameType.L,
    // 备份的自定义命名
    $floorNames: [],
    // 备份的L格式的楼层
    $floorNamesL: [],
    // 备份的F格式的楼层
    $floorNamesF: [],
    // 含有ID的楼层数据元，用于编辑单元时回传
    $floorIdState: []
  }),
  computed: {
    title: function () {
      return this.isCell ? '单元详情' : '楼栋详情'
    },
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    },
    // 是否自定义名称
    isCustom() {
      return this.formModel.floorNameType === FloorNameType.ZH || this.formModel.floorNameType === FloorNameType.F || this.formModel.floorNameType === FloorNameType.L
    },
    floorNameTypeOptions() {
      return FloorNameOptions
    }
  },
  watch: {
    dialogVisible(val) {
      if (!val) return
      this.readonly = true
      this.getDetail()
    }
  },
  methods: {
    getDetail() {
      this.loadingStatus = true
      const apiFun = this.isCell ? this.$api.rentalHousingApi.getUnitById : this.$api.rentalHousingApi.getBuildingById
      apiFun({ id: this.id })
        .then((res) => {
          if (res.code === '200') {
            if (this.isCell) {
              this.formModel.cellName = res.data.spaceName || ''
              this.formModel.floorNameType = res.data.floorNameRule || FloorNameType.L
              this.$lastNameType = this.formModel.floorNameType
              this.formModel.floorNames = res.data.floorList || []
              // 如果是自定义名称，备份一次
              if (this.formModel.floorNameType === FloorNameType.ZH) {
                this.$floorNames = this.formModel.floorNames.map((it) => Object.assign({}, it))
              }
              // 如果是L格式名称，备份一次
              if (this.formModel.floorNameType === FloorNameType.L) {
                this.$floorNamesL = this.formModel.floorNames.map((it) => Object.assign({}, it))
              }
              // 如果是F格式名称，备份一次
              if (this.formModel.floorNameType === FloorNameType.F) {
                this.$floorNamesF = this.formModel.floorNames.map((it) => Object.assign({}, it))
              }
            } else {
              this.formModel.buildingName = res.data.spaceName || ''
            }
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取详情信息失败'))
        .finally(() => (this.loadingStatus = false))
    },
    onDialogClosed() {
      // dialog关闭时重置form表单
      this.$refs.formRef.resetFields()
      // 重置自定义命名
      this.$floorNames = []
      this.$floorNamesL = []
      this.$floorNamesF = []
      this.$lastNameType = this.formModel.floorNameType
    },
    onSubmit() {
      this.$refs.formRef
        .validate()
        .catch(() => Promise.reject())
        .then(() => {
          // 如果是自定义命名，需要校验自定义命名是否为空
          if (this.isCustom) {
            const hasEmpty = this.formModel.floorNames.some((it) => !it.displayName.trim())
            if (hasEmpty) {
              throw '请填写所有楼层名称'
            }
          }
        })
        .then(() => {
          // loading
          this.loadingStatus = true
          // config request data
          const params = {
            id: this.id,
            userId: this.$store.getters.userId, // 用户id
            userName: this.$store.getters.userName // 用户名
          }
          if (!this.isCell) {
            params.estateId = this.parentId
            params.spaceName = this.formModel.buildingName
            return this.$api.rentalHousingApi.updateBuildingInfo(params)
          } else {
            params.buildingId = this.parentId
            params.spaceName = this.formModel.cellName
            params.floorNameRule = this.formModel.floorNameType
            params.floorList = this.formModel.floorNames
            return this.$api.rentalHousingApi.updateUnitInfo(params)
          }
        })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('操作成功')
            this.$emit('success')
            this.dialogVisible = false
          } else {
            throw res.message || '操作失败'
          }
        })
        .catch((msg) => {
          msg && this.$message.error(msg)
        })
        .finally(() => {
          this.loadingStatus = false
        })
    },
    // 重新生成楼层名称
    reGenerateNames() {
      const type = this.formModel.floorNameType
      const bakNames = this.$floorNames || []
      const bakNamesL = this.$floorNamesL || []
      const bakNamesF = this.$floorNamesF || []
      // 生成楼层名称
      function getFloorName(number, under = false) {
        if (type === FloorNameType.L) {
          // 尝试从源自定义命名中找回名称
          const label = bakNamesL.find((it) => {
            const nameStr = under ? `-${number}` : `${number}`
            return it.actualNumber === nameStr
          })?.displayName
          if (label) {
            return label
          } else {
            return under ? `-L${number}` : `L${number}`
          }
        } else if (type === FloorNameType.F) {
          // 尝试从源自定义命名中找回名称
          const label = bakNamesF.find((it) => {
            const nameStr = under ? `-${number}` : `${number}`
            return it.actualNumber === nameStr
          })?.displayName
          if (label) {
            return label
          } else {
            return under ? `-${number}F` : `${number}F`
          }
        } else if (type === FloorNameType.ZH) {
          // 尝试从源自定义命名中找回名称
          const label = bakNames.find((it) => {
            const nameStr = under ? `-${number}` : `${number}`
            return it.actualNumber === nameStr
          })?.displayName
          if (label) {
            return label
          } else {
            return under ? `负${number}层` : `${number}层`
          }
        }
      }
      this.formModel.floorNames.forEach((it) => {
        const number = Number(it.actualNumber)
        it.displayName = getFloorName(Math.abs(number), number < 0)
      })
      this.formModel.floorNames = [].concat(this.formModel.floorNames)
    },
    // 楼层命名规则发生变化时候
    onGenTypeChange(type) {
      if (this.$lastNameType === FloorNameType.ZH) {
        // 如果是从自定义切出，备份一次自定义名称
        this.$floorNames = this.formModel.floorNames.map((it) => Object.assign({}, it))
      }
      // 如果是L格式名称，备份一次
      if (this.$lastNameType === FloorNameType.L) {
        this.$floorNamesL = this.formModel.floorNames.map((it) => Object.assign({}, it))
      }
      // 如果是F格式名称，备份一次
      if (this.$lastNameType === FloorNameType.F) {
        this.$floorNamesF = this.formModel.floorNames.map((it) => Object.assign({}, it))
      }
      this.reGenerateNames()
      this.$lastNameType = type
    }
  }
}
</script>
<template>
  <el-dialog
    v-dialogDrag
    class="component building-edit"
    :title="title"
    width="550px"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    custom-class="model-dialog"
    destroy-on-close
    @close="onDialogClosed"
  >
    <el-form ref="formRef" v-loading="loadingStatus" :model="formModel" :rules="rules" label-width="95px" :disabled="readonly" :class="{ readonly }">
      <el-form-item v-if="!isCell" label="楼栋名称" prop="buildingName">
        <el-input v-model="formModel.buildingName" placeholder="请输入楼栋名称" maxlength="50"></el-input>
      </el-form-item>
      <el-form-item v-if="isCell" label="单元名称" prop="cellName">
        <el-input v-model="formModel.cellName" placeholder="请输入单元名称" maxlength="50"></el-input>
      </el-form-item>
      <el-form-item label="在管房间" prop="roomCount">
        <span style="padding-left: 16px">{{ roomCount }}间</span>
      </el-form-item>
      <template v-if="isCell">
        <el-form-item label="楼层命名" prop="floorNameType" style="margin-bottom: 0">
          <el-radio-group v-model="formModel.floorNameType" @change="onGenTypeChange">
            <el-radio v-for="item of floorNameTypeOptions" :key="item.value" :label="item.value">{{ item.label }} </el-radio>
          </el-radio-group>
        </el-form-item>
        <div class="building-edit__table">
          <el-table size="mini" :data="formModel.floorNames" max-height="200px">
            <el-table-column label="实际层数" prop="actualNumber"></el-table-column>
            <el-table-column label="显示名称" prop="displayName">
              <template v-if="isCustom" #default="{ row }">
                <el-input v-model="row.displayName" placeholder="请输入" maxlength="10" class="building-edit__input--custom"></el-input>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </template>
    </el-form>
    <template #footer>
      <template v-if="readonly">
        <el-button type="primary" plain @click="dialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="readonly = false">编辑</el-button>
      </template>
      <template v-else>
        <el-button type="primary" plain @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="loadingStatus" @click="onSubmit">确认</el-button>
      </template>
    </template>
  </el-dialog>
</template>
<style lang="scss" scoped>
.building-edit {
  .el-form {
    background: #fff;
    width: 100%;
    padding: 16px;
    height: 100%;
    &.readonly {
      ::v-deep(.el-form-item) {
        margin-bottom: 10px;
        .el-form-item__label {
          &::before {
            display: none;
          }
        }
        .el-form-item__content {
          .el-textarea__inner,
          .el-input__inner {
            background: transparent;
            border: none;
            color: inherit;
            cursor: default;
            &::placeholder {
              color: transparent;
            }
          }
          .el-textarea__inner {
            resize: none;
            padding-top: 10px;
          }
          .el-input-group__append {
            background: transparent;
            border: none;
            padding-left: 0;
          }
          .el-input__suffix {
            display: none;
          }
          .el-input-number {
            width: auto;
            .el-input__inner {
              padding: 16px;
              text-align: left;
            }
            & > span {
              display: none;
            }
          }
          .el-radio__input {
            display: none;
          }
          .el-radio__label {
            padding-left: 16px;
            color: #606266;
          }
          .el-radio:not(.is-checked) {
            display: none;
          }
        }
      }
    }
  }
  .el-input-number {
    line-height: 30px;
  }
  &__table {
    padding: 0 0 0 90px;
  }
  &__input--custom {
    ::v-deep(.el-input__inner) {
      line-height: inherit;
      height: auto;
    }
  }
}
</style>
