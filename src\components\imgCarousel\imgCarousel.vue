<!--
 * @Author: hedd
 * @Date: 2023-02-27 14:35:19
 * @LastEditTime: 2023-03-10 17:50:49
 * @FilePath: \IHCRS_alarm\src\components\imgCarousel\imgCarousel.vue
 * @Description:
-->
<template>
  <div>
    <el-dialog class="imgCarousel-dialog" style="background-color: transparent;" :imgArr="imgArr" :visible.sync="dialogVisibleImg" :before-close="closeDialog" append-to-body>
      <el-carousel ref="imgCarousel" indicator-position="outside" :autoplay="false" class="imgCarousel" :initial-index="index">
        <el-carousel-item v-for="item in imgArr" :key="item" style="text-align: center;">
          <img :src="item" alt style="max-width: 100%; max-height: 100%;" :onerror="defaultLogo" />
        </el-carousel-item>
      </el-carousel>
      <!-- <span slot="footer" class="dialog-footer">
          <el-button @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="sureWz">确 定</el-button>
        </span>-->
    </el-dialog>
  </div>
</template>
<script type="text/ecmascript-6">
import imageUrl from '@/assets/images/imgError.png'
export default {
  name: '',
  props: {
    imgArr: {
      type: Array,
      default: () => {
        return []
      }
    },
    dialogVisibleImg: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      index: 0,
      defaultLogo:
        'this.src=" ' + imageUrl + ' "'
    }
  },
  watch: {
    index(n, o) {
      this.index = n
      this.$refs.imgCarousel.setActiveItem(n)
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {},
    closeDialog() {
      this.$emit('closeDialog')
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
