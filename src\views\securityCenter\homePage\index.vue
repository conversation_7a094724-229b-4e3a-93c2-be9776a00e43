<template>
  <div id="content" class="inner">
    <div class="content">
      <div class="header">
        <div class="hospital">{{ hospitalName }}</div>
        <div class="sysTitle">安全生产双重预防信息化公示平台</div>
        <div class="days">
          <span>安全生产</span>
          <span class="dayBar">{{ riskLevelList.runDay }}</span>
          <span>天</span>
          <span @click="fullScreen">
            <img v-if="!isFullFlag" height="24px" src="../../../assets/images/securityCenter/Full <EMAIL>" alt="全屏" title="全屏" />
            <img v-if="isFullFlag" src="../../../assets/images/securityCenter/fullCancle.png" alt="全屏" title="退出全屏" />
          </span>
        </div>
      </div>
      <div class="pageContent">
        <div class="topInner">
          <div class="aside">
            <!-- 风险数量 -->
            <div class="children">
              <div class="toptip" style="padding: 0; border: 0; font-weight: bold;">
                <span class="green_line"></span>
                风险数量分析
              </div>
              <div class="childrenCenter">
                <div class="totalCount" @click="getRiskList('', '', riskLevelList.totalCount)">
                  <img src="../../../assets/images/securityCenter/<EMAIL>" alt="" />
                  <div class="countFont">{{ riskLevelList.totalCount }}</div>
                  <div class="typeFont">风险总数</div>
                </div>
                <div class="otherCenter">
                  <div v-for="(item, index) in riskNumList" :key="index" class="otherChild" @click="getRiskList(item.dictValue, '', riskLevelList[item.value])">
                    <img :src="item.src" alt="" />
                    <div class="countFont" :class="item.color">{{ riskLevelList[item.value] }}</div>
                    <div class="typeFont">{{ item.lable }}</div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 风险类型 -->
            <div class="children">
              <div class="toptip" style="padding: 0; border: 0; font-weight: bold;">
                <span class="green_line"></span>
                风险类型分析
              </div>
              <div class="childrenCenter">
                <barCharts :barData="barData" @getBarType="getBarType"></barCharts>
              </div>
            </div>
          </div>
          <!-- 四色图 -->
          <div class="center">
            <img :src="$tools.imgUrlTranslation(riskLevelList.pictureUrl)" alt="" style="width: 100%; height: 100%;" />
            <img height="24px" src="../../../assets/images/securityCenter/Full <EMAIL>" alt="全屏" title="全屏" class="imgClass" @click="fourColor" />
          </div>
          <div class="aside">
            <!-- 隐患等级 -->
            <div class="children">
              <div class="toptip" style="padding: 0; border: 0; font-weight: bold; justify-content: space-between;">
                <div style="display: flex; align-items: center;">
                  <span class="green_line"></span>
                  隐患等级分析
                </div>
                <div>
                  <el-radio-group v-model="hiddenGrade" style="margin-bottom: 30px;">
                    <el-radio-button label="year">本年</el-radio-button>
                    <el-radio-button label="month">本月</el-radio-button>
                  </el-radio-group>
                </div>
              </div>
              <div class="hiddenStatusWarp">
                <div
                  v-for="(item, index) in hiddenLevelList"
                  :key="index"
                  class="hiddenGrade"
                  style="cursor: pointer;"
                  @click="getHiddenList(item.dictValue, WorkOrderStatistics[item.value])"
                >
                  <img :src="item.src" alt="" />
                  <div class="countFont" :class="item.color">{{ WorkOrderStatistics[item.value] }}</div>
                  <div class="typeFont">{{ item.lable }}</div>
                </div>
                <div class="hiddenGrade gaugeCharts">
                  <div style="width: 100%; height: 86%;"><gaugeCharts :chartsData="WorkOrderStatistics.efficient"></gaugeCharts></div>
                  <div class="typeFont">隐患整改率</div>
                </div>
              </div>
            </div>
            <!-- 隐患状态 -->
            <div class="children">
              <div class="toptip" style="padding: 0; border: 0; font-weight: bold; justify-content: space-between;">
                <div style="display: flex; align-items: center;">
                  <span class="green_line"></span>
                  隐患状态分析
                </div>
                <div>
                  <el-radio-group v-model="hiddenStatus" style="margin-bottom: 30px;">
                    <el-radio-button label="year">本年</el-radio-button>
                    <el-radio-button label="month">本月</el-radio-button>
                  </el-radio-group>
                </div>
              </div>
              <div class="childrenCenter">
                <pieCharts :pieData="pieData"></pieCharts>
              </div>
            </div>
          </div>
        </div>
        <div class="bottomInner">
          <div>
            <div class="toptip" style="padding: 0; border: 0; font-weight: bold;">
              <span class="green_line"></span>
              近12个月隐患走势
            </div>
            <div class="riskNum">
              <line-riskNum :lineData="lineData" @getHiddenDate="getHiddenDate"></line-riskNum>
            </div>
          </div>
          <div>
            <div class="toptip" style="padding: 0; border: 0; font-weight: bold;">
              <span class="green_line"></span>
              隐患实时动态
            </div>
            <div class="riskNum">
              <el-table v-loading="tableLoading" v-el-table-infinite-scroll="loadMore" stripe :data="tableData" :border="true" height="100%">
                <el-table-column prop="createTime" label="反馈时间" show-overflow-tooltip width="160"></el-table-column>
                <el-table-column prop="createPersonName" label="反馈人" show-overflow-tooltip></el-table-column>
                <el-table-column prop="questionCode" label="隐患编号" show-overflow-tooltip width="140">
                  <template slot-scope="scope">
                    <el-link type="primary" @click="hiddenDetail(scope.row)">{{ scope.row.questionCode }}</el-link>
                  </template>
                </el-table-column>
                <el-table-column prop="riskName" label="隐患等级" show-overflow-tooltip></el-table-column>
                <el-table-column prop="questionDetailType" label="隐患分类" show-overflow-tooltip></el-table-column>
                <el-table-column prop="flowType" label="处理状态" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span class="enable"><span></span>{{ scope.row.flowType }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import lineRiskNum from './components/lineRiskNum.vue'
import pieCharts from './components/pieCharts.vue'
import barCharts from './components/barCharts.vue'
import gaugeCharts from './components/gaugeCharts.vue'
import screenfull from 'screenfull'
import majorRiskImg from '@/assets/images/securityCenter/<EMAIL>'
import majorRiskImg1 from '@/assets/images/securityCenter/risk-major@2x(1).png'
import riskLow from '@/assets/images/securityCenter/<EMAIL>'
import hiddendanger from '@/assets/images/securityCenter/hidden <EMAIL>'
import hiddendanger1 from '@/assets/images/securityCenter/hidden <EMAIL>'
import hiddendanger2 from '@/assets/images/securityCenter/hidden <EMAIL>'
export default {
  components: {
    pieCharts,
    barCharts,
    lineRiskNum,
    gaugeCharts
  },
  data() {
    return {
      hospitalName: sessionStorage.getItem('LOGINDATA') ? JSON.parse(sessionStorage.getItem('LOGINDATA')).hospitalName : '',
      isFullFlag: false,
      hiddenGrade: 'year',
      hiddenStatus: 'year',
      riskTypeBarIsShow: true, // 风险类型
      dangerStatePieIsShow: true, // 隐患状态
      tableLoading: false,
      tableData: [],
      WorkOrderStatistics: [], // 获取隐患数量
      pieData: {}, // 隐患状态统计
      barData: {}, // 风险类型
      riskLevelList: [], // 风险等级数量
      lineData: {}, // 走势图
      riskNumList: [
        {
          src: majorRiskImg,
          value: 'majorRisk',
          lable: '重大风险',
          dictValue: '1',
          color: 'majorClass'
        },
        {
          src: majorRiskImg1,
          value: 'moreRisk',
          lable: '较大风险',
          dictValue: '2',
          color: 'moreClass'
        },
        {
          src: majorRiskImg1,
          value: 'commonDangerCount',
          lable: '一般风险',
          dictValue: '3',
          color: 'commonClass'
        },
        {
          src: riskLow,
          value: 'lowDangerCount',
          lable: '低风险',
          dictValue: '4',
          color: 'lowClass'
        }
      ],
      hiddenLevelList: [
        {
          src: hiddendanger,
          value: 'majorDangerCount',
          lable: '重大隐患',
          dictValue: '2',
          color: 'majorClass'
        },
        {
          src: hiddendanger1,
          value: 'commonDangerCount',
          lable: '一般隐患',
          dictValue: '1',
          color: 'commonClass'
        },
        {
          src: hiddendanger2,
          value: 'noRectificationCount',
          lable: '未整改隐患',
          dictValue: 'wzg',
          color: 'moreClass'
        }
      ],
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      }
    }
  },
  watch: {
    hiddenGrade() {
      this.getWorkOrderStatisticsHomePage()
    },
    hiddenStatus() {
      this.getDangerState()
    }
  },
  created() {
    this.getWorkOrderStatisticsHomePage()
    this.getDangerState()
    this.getRiskType()
    this.getWorkOrderCount()
    this.getRiskNum()
    this.getHiddenDangerList() // 隐患实时列表
  },
  mounted() {
    // 监听页面全屏
    window.addEventListener('fullscreenchange', (e) => {
      if (screenfull.isFullscreen) {
        this.isFullFlag = true
      } else {
        this.isFullFlag = false
      }
    })
  },
  methods: {
    // 隐患实时列表
    getHiddenDangerList() {
      let data = {
        pageNo: this.paginationData.currentPage,
        pageSize: this.paginationData.pageSize
      }
      this.$api.ipsmGetHiddenDangerList(data).then((res) => {
        if (res.code == 200) {
          this.tableData = this.tableData.concat(res.data.list)
          this.paginationData.total = parseInt(res.data.total)
        }
      })
    },
    loadMore() {
      if (this.tableData.length < this.paginationData.total && this.tableData.length) {
        this.paginationData.currentPage += 1
        this.getHiddenDangerList()
      }
    },
    // 获取风险等级分析
    getWorkOrderCount() {
      this.$api.ipsmWorkOrderCount({ isPanel: 1 }).then((res) => {
        this.riskLevelList = res.data
      })
    },
    // 获取日常隐患分析
    getWorkOrderStatisticsHomePage() {
      this.$api.ipsmGetTasksAnalysis({ dateType: this.hiddenGrade }).then((res) => {
        this.WorkOrderStatistics = res.data
      })
    },
    // 隐患状态分析
    getDangerState() {
      this.$api.ipsmGetWorkOrderStatistics({ dateType: this.hiddenStatus }).then((res) => {
        if (res.code == 200) {
          this.pieData = res.data
        }
      })
    },
    // 风险类型
    getRiskType() {
      this.$api.ipsmGetRiskStatsByType({}).then((res) => {
        if (res.code == 200) {
          this.barData = res.data
        }
      })
    },
    // 隐患走势图
    getRiskNum() {
      this.$api.ipsmGetHiddenDangerTrend({}).then((res) => {
        if (res.code == 200) {
          this.lineData = res.data
        }
      })
    },
    // 全屏||收缩
    fullScreen() {
      this.isFullFlag = !this.isFullFlag
      const element = document.getElementById('content') // 指定全屏区域元素
      if (this.isFullFlag) {
        // screenfull.request(element);
        element.requestFullscreen()
      } else {
        document.exitFullscreen()
      }
    },
    // 进入四色图
    fourColor() {
      this.$router.push({
        name: 'RiskFourColorHome',
        query: {
          type: 'home'
        }
      })
    },
    hiddenDetail(row) {
      this.$router.push({
        name: 'hiddenManagementDetails3',
        query: { id: row.id }
      })
    },
    getBarType(val) {
      this.getRiskList('', val)
    },
    // 点击进入风险列表
    getRiskList(riskLevel, riskType, num) {
      if (num == 0) {
        return this.$message.error('风险数为0')
      }
      this.$router.push({
        name: 'riskList',
        query: {
          riskLevel,
          riskType
        }
      })
    },
    // 点击进入隐患列表
    getHiddenList(val, num) {
      if (num == 0) {
        return this.$message.error('隐患数为0')
      }
      this.$router.push({
        name: 'dangerList',
        query: {
          dateType: this.hiddenGrade,
          riskCode: val != 'wzg' ? val : '',
          flowCode: val != 'wzg' ? '' : 'wzg'
        }
      })
    },
    // 获取月份
    getHiddenDate(val) {
      this.$router.push({
        name: 'dangerList',
        query: {
          currMonth: val
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.inner {
  margin: 16px;
  background-color: #fff;
  height: calc(100% - 32px);

  .content {
    height: 100%;
    padding: 16px;

    .header {
      height: 50px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 18px;

      .hospital {
        color: #414653;
        font-weight: bold;
      }

      .sysTitle {
        font-size: 24px;
        color: #121f3e;
        font-weight: bold;
      }

      .days {
        display: flex;
        align-items: center;

        .dayBar {
          color: #fff;
          height: 25px;
          padding: 0 4px;
          background-color: #08cb83;
        }

        img {
          margin-left: 24px;
        }
      }
    }

    .pageContent {
      margin: 16px 16px 0;
      height: calc(100% - 64px);

      .toptip {
        height: 30px;
        line-height: 30px;
      }

      .topInner {
        margin-bottom: 15px;
        height: calc(70% - 15px);
        display: flex;

        .aside {
          width: 32%;
          height: 100%;

          > div:nth-child(1) {
            margin-bottom: 10px;
          }

          .children {
            padding: 10px;
            width: 100%;
            height: 49%;
            background-color: #faf9fc;

            .childrenCenter {
              width: 100%;
              display: flex;
              height: calc(100% - 30px);
              font-size: 20px;
              font-weight: bold;
              margin-top: 10px;

              .totalCount {
                width: 30%;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                background-color: #fff;
                margin-right: 10px;

                img {
                  width: 100%;
                }
              }

              .otherCenter {
                width: 70%;
                display: flex;
                flex-wrap: wrap;

                > div:nth-child(1) {
                  margin: 0 10px 10px 0;
                }

                > div:nth-child(2) {
                  margin-bottom: 10px;
                }

                > div:nth-child(3) {
                  margin-right: 10px;
                }

                .otherChild {
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  box-sizing: border-box;
                  width: 45%;
                  height: 47%;
                  cursor: pointer;
                  background-color: #fff;

                  img {
                    width: 50%;
                  }

                  div {
                    padding: 0;
                  }

                  > :nth-child(2) {
                    font-size: 18px;
                  }
                }
              }

              .countFont {
                padding: 5px;
              }

              .typeFont {
                padding: 5px;
                font-size: 12px;
                font-weight: normal;
                color: #414653;
              }
            }

            .hiddenStatusWarp {
              width: 100%;
              height: 85%;
              display: flex;
              flex-wrap: wrap;

              > div:nth-child(1) {
                margin-right: 10px;
              }

              > div:nth-child(3) {
                margin-right: 10px;
              }

              .hiddenGrade {
                width: 48%;
                height: 47%;
                display: flex;
                flex-direction: column;
                align-items: center;
                box-sizing: border-box;
                background-color: #fff;
                padding: 10px;
                margin-top: 10px;

                img {
                  width: 28%;
                  height: 50%;
                }

                .countFont {
                  padding: 2px;
                  font-size: 18;
                  font-weight: bold;
                }

                .typeFont {
                  padding: 2px;
                  font-size: 12px;
                  color: #414653;
                }
              }
            }
          }
        }

        .center {
          margin: 0 15px;
          width: calc(50% - 30px);
          height: 100%;
          position: relative;

          .imgClass {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 2;
          }
        }
      }

      .bottomInner {
        height: 30%;
        display: flex;

        > div {
          width: 50%;
          padding: 10px;
          background-color: #faf9fc;
        }

        > div:nth-child(1) {
          margin-right: 10px;
        }

        .riskNum {
          height: calc(100% - 30px);
          width: 100%;
        }
      }
    }

    :deep(.el-radio-group) {
      margin: 0 0 2px !important;

      .el-radio-button__inner {
        padding: 5px 10px;
      }
    }
  }
}

.echartsClass {
  height: calc(100% - 30px);
  width: 100%;
}

.enable {
  color: #ff9435;

  span {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 2px;
    background-color: #ff9435;
  }
}

.riskStatus {
  display: flex;
  height: 50px;
  // justify-content: space-between;
}

.majorClass {
  color: #fa403c;
}

.moreClass {
  color: #ff9435;
}

.commonClass {
  color: #ffbe00;
}

.lowClass {
  color: #406ce1;
}

.gaugeCharts {
  padding: 0 !important;
}
</style>
