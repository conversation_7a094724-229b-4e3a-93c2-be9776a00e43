<template>
  <!-- 日志记录 -->
  <PageContainer>
    <div slot="header" class="control-btn-header">
      <el-date-picker
        v-model="searchFrom.dataRange"
        type="daterange"
        unlink-panels
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="yyyy-MM-dd"
      >
      </el-date-picker>
      <el-select v-model="searchFrom.type" placeholder="日志类型" clearable>
        <el-option label="全部" :value="0"> </el-option>
        <el-option label="通信状态" :value="1"> </el-option>
        <el-option label="运行状态" :value="2"> </el-option>
      </el-select>
      <div style="display: inline-block;">
        <el-button type="primary" plain @click="resetForm">重置</el-button>
        <el-button type="primary" @click="searchForm">查询</el-button>
      </div>
    </div>
    <div slot="content" class="table-content">
      <TablePage
        ref="table"
        v-loading="tableLoading"
        :showPage="true"
        :tableColumn="tableColumn"
        :data="tableData"
        height="calc(100% - 40px)"
        :pageData="pageData"
        @pagination="paginationChange"
      />
    </div>
  </PageContainer>
</template>

<script>
import moment from 'moment'
moment.locale('zh-cn')
export default {
  name: 'lightingLogging',
  props: {
    projectCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tableLoading: false,
      searchFrom: {
        type: 0, // 日志类型
        dataRange: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')] // 时间范围
      },
      tableData: [],
      pageData: {
        page: 1,
        pageSize: 15,
        total: 0
      },
      tableColumn: [
        {
          prop: 'serialNumber',
          label: '序号',
          width: 80,
          formatter: (scope) => {
            return (this.pageData.page - 1) * this.pageData.pageSize + scope.$index + 1
          }
        },
        {
          prop: 'operatorDate',
          label: '日期时间',
          width: 150
        },
        {
          prop: 'operatorType',
          label: '日志类型',
          width: 150,
          formatter: (scope) => {
            return scope.row.operatorType == 1 ? '通信状态' : (scope.row.operatorType == 2 ? '运行状态' : '-')
          }
        },
        {
          prop: 'details',
          label: '日志详情'
        }
      ]
    }
  },
  computed: {

  },
  created() {
    this.getLoggingList()
  },
  methods: {
    getLoggingList() {
      let params = {
        projectCode: this.projectCode,
        pageSize: this.pageData.pageSize,
        page: this.pageData.page,
        operatorType: this.searchFrom.type,
        // types: [1, 2, 3, 4],
        createTime: this.searchFrom.dataRange[0] + ' 00:00:00',
        endTime: this.searchFrom.dataRange[0] + ' 23:59:59'
      }
      this.tableLoading = true
      this.$api.GetOperationRecordList(params).then((res) => {
        this.tableLoading = false
        if (res.code == 200) {
          this.tableData = res.data.list
          this.pageData.total = parseInt(res.data.count)
        } else if (res.message) {
          this.$message.error(res.message)
        }
      })
    },
    // 重置查询表单
    resetForm() {
      Object.assign(this.$data.searchFrom, this.$options.data().searchFrom)
      this.searchForm()
    },
    // 查询
    searchForm() {
      this.pageData.page = 1
      this.getLoggingList()
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getLoggingList()
    }
  }
}

</script>

<style lang="scss" scoped>
.control-btn-header {
  padding: 10px !important;
  & > div {
    margin-right: 10px;
  }
}

.table-content {
  margin-top: 15px;
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 15px);
  padding: 10px;
}
</style>
