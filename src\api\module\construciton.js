/*
 * @Description:
 */
import { getRequest, postRequest, uploadCommon, downFile } from '../http.js'
let SPACE_API = __PATH.SPACE_API
// let SPACE_API = 'http://************:9799/'
export default {
  getConstructionUnitList: postRequest('constructionUnitController/selectConstructionUnit', SPACE_API), // 单位列表
  unitConstructionDetail: postRequest('constructionUnitController/getOperationStaffDetails', SPACE_API), // 单位详情
  addConstructionUnit: postRequest('constructionUnitController/addConstructionUnit', SPACE_API), // 新增单位
  editConstructionUnit: postRequest('constructionUnitController/updateConstructionUnit', SPACE_API), // 编辑单位
  delConstructionUnit: postRequest('constructionUnitController/delConstructionUnit', SPACE_API), // 删除单位
  getConstructionUnitListNoParams: postRequest('constructionUnitController/getCompanyList', SPACE_API), // 单位列表无入参
  updateFileUnit: postRequest('constructionUnitController/updateFileConstructionUnit', SPACE_API), // 单位管理详情附件修改
  delFileUnit: getRequest('constructionUnitController/deleteUnitFileById', SPACE_API), // 单位管理详情附件删除
  getFileListUnit: getRequest('constructionUnitController/getUnitFileDetails', SPACE_API), // 获取单位详情附件列表
  // 人员管理==========================================
  getConstructionUserList: postRequest('fieldOperationStaffController/selectOperationStaff', SPACE_API),
  // 通用上传，实际上只是用的是 SPACE_API，只不过是因为需要本地联调，所以直接从环境变量文件中取地址
  constructionUpload: uploadCommon(SPACE_API),
  // 下载人员
  constructionDownloadUser: downFile('fieldOperationStaffController/exportStaff', SPACE_API),
  // 下载单位
  constructionDownloadUnit: downFile('constructionUnitController/exporCompany', SPACE_API),
  // 新增人员    SPACE_API
  addConstrucitonUser: postRequest('fieldOperationStaffController/addOperationStaff', SPACE_API),
  // 编辑
  updateConstrucitonUser: postRequest('fieldOperationStaffController/updateOperationStaff', SPACE_API),
  // 删除
  delConstrucitonUser: postRequest('fieldOperationStaffController/delOperationStaff', SPACE_API),
  // 详情
  detailConstrucitonUser: postRequest('fieldOperationStaffController/getOperationStaffDetails', SPACE_API),
  // 导入
  // 功能配置==========================================
  // 新增业务表单配置
  addBusinessForm: postRequest('assignmentBusinessForm/createAssignmentBusinessForm', SPACE_API),
  // 编辑业务表单配置
  editBusinessForm: postRequest('assignmentBusinessForm/updateAssignmentBusinessForm', SPACE_API),
  flowList: postRequest('assignmentBusinessForm/getworkFlowDesignModeList', SPACE_API),
  // 业务表单配置删除
  delBusinessForm: postRequest('assignmentBusinessForm/deleteAssignmentBusinessFormById', SPACE_API),
  // 业务表单配置分页列表
  businessFormList: postRequest('assignmentBusinessForm/queryAssignmentBusinessFormByPage', SPACE_API),
  // 业务表单配置详情
  businessFormDetail: postRequest('assignmentBusinessForm/queryAssignmentBusinessFormDetail', SPACE_API),
  // 同步工作流
  businessFormSync: postRequest('assignmentBusinessForm/synchronousWorkFlow', SPACE_API),
  // 全部类型
  allBusinessForm: postRequest('assignmentBusinessForm/queryAssignmentBusinessFormAll', SPACE_API),
  // 单据管理-----------------------------------------
  // 列表
  receiptList: postRequest('projectDocumentConfig/queryAssignmentDocumentConfigByPage', SPACE_API),
  // 删除
  receiptDel: postRequest('projectDocumentConfig/deleteAssignmentDocumentConfigById', SPACE_API),
  // 流程节点列表
  workNodeListByFlowKey: postRequest('assignmentBusinessForm/getWorkNodeListByFlowKey', SPACE_API),
  // 新增
  receiptAdd: postRequest('projectDocumentConfig/createAssignmentDocumentConfig', SPACE_API),
  // 编辑
  receiptUpdate: postRequest('projectDocumentConfig/updateAssignmentDocumentConfig', SPACE_API),
  // 作业管理==========================================
  // 列表
  allAssignmentList: postRequest('assignmentInfo/queryAssignmentInfoByPage', SPACE_API),
  // 巡检作业==========================================
  // 列表
  workMaintainList: postRequest('maintain/template/pageList', SPACE_API),
  // 详情
  workMaintainDetail: postRequest('maintain/template/getDetail', SPACE_API),
  // 删除
  workMaintainDel: postRequest('maintain/template/deleteById', SPACE_API),
  // 保存
  workMaintainSave: postRequest('maintain/template/save', SPACE_API),
  // 模版配置===========================================
  // 列表
  workMaintainConfigList: postRequest('maintain/config/pageList', SPACE_API),
  // 详情
  workMaintainConfigDetail: postRequest('maintain/config/detail', SPACE_API),
  // 新增编辑
  workMaintainConfigInsert: postRequest('maintain/config/save', SPACE_API),
  // 删除
  workMaintainConfigDel: postRequest('maintain/config/delete', SPACE_API),
  // 巡检记录
  // 列表
  maintainRecordList: postRequest('maintain/plan/pageListNoPermission', SPACE_API),
  // 点列表
  maintainRecordPointList: postRequest('maintain/plan/recordList', SPACE_API),
  // 详情
  maintainRecordDetail: postRequest('maintain/record/detail', SPACE_API),
  // 施工审批提醒设置-保存
  saveConstructionApprovalConfig: postRequest('messageConfig/saveSendMessageConfig', SPACE_API),
  // 施工审批提醒设置-详情
  getConstructionApprovalConfigById: getRequest('messageConfig/getSendMessageConfig', SPACE_API),
  // 施工 单位/人员证照信息字典==================================
  // 获取证照列表
  getConstructionCertificateList: postRequest('CertificateConfig/queryByPage', SPACE_API),
  // 新增证照信息
  addConstructionCertificate: postRequest('CertificateConfig/addCertificateConfig', SPACE_API),
  // 编辑证照信息
  editConstructionCertificate: postRequest('CertificateConfig/updateCertificateConfig', SPACE_API),
  // 删除证照信息
  deleteConstructionCertificate: getRequest('CertificateConfig/deleteById', SPACE_API)
}
