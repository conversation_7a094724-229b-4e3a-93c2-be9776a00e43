<template>
  <!-- 医用气体统计分析 -->
  <commonStatisticalAnalysis :projectCode="projectCode" :requestHttp="requestHttp" />
</template>

<script>
import commonStatisticalAnalysis from '../commonPage/statisticalAnalysis/index'
import { monitorTypeList } from '@/util/dict.js'
export default {
  name: 'statisticalAnalysis',
  components: {commonStatisticalAnalysis},
  data() {
    return {
      requestHttp: __PATH.VUE_IEMC_API,
      projectCode: monitorTypeList.find(item => item.projectName == '医用气体').projectCode
    }
  },
  computed: {

  },
  created() {

  },
  methods: {

  }
}

</script>

<style lang="scss" scoped>

</style>
