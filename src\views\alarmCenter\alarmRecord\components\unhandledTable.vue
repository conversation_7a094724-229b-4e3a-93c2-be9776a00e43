<template>
  <div class="unhandledTable">
    <div v-if="list.shielded.length" class="heade-shielded">
      <div v-show="!showShielded" class="shielded-hide">
        <span class="shielded-info">
          <svg-icon name="info_fill" class="shielded-hide-icon" />
          已屏蔽{{ pagingInfo.shielded.total }}条数据
        </span>
        <span class="shielded-open" style="margin-left: 30px;" @click="() => {showShielded = true}">
          展开
          <svg-icon name="down_icon" class="shielded-open-icon" />
        </span>
      </div>
      <div v-show="showShielded" v-loading="loading.processing" class="shielded-show">
        <div class="shielded-show-heade">
          <span class="shielded-info">
            <svg-icon name="info_fill" class="shielded-hide-icon" />
            已展开{{ list.shielded.length }}条屏蔽数据
          </span>
          <span class="shielded-open" style="margin-left: 10px;" @click="() => {showShielded = false}">
            收起
            <svg-icon name="up_icon" class="shielded-open-icon" />
          </span>
        </div>
        <div v-infinite-scroll="() => {pagingLoad('shielded')}" style="margin-top: 8px; height: calc(100% - 28px); overflow-x: hidden; overflow-y: auto;">
          <alarmList ref="shielded" :listData="list.shielded" @selectChange="selectChange" v-on="$listeners" />
          <!-- <span v-if="loading.shielded" class="noData">加载中...</span> -->
          <span v-if="(list.shielded.length >= pagingInfo.shielded.total) && !loading.shielded && list.shielded.length" class="noData">没有更多了</span>
        </div>
      </div>
    </div>
    <el-row :gutter="16" style="flex: 1; overflow: auto;">
      <el-col :xs="24" :lg="12" style="height: 100%; padding-top: 16px;">
        <div v-loading="loading.unhandled" class="contentList unhandledList">
          <div class="heade-search">
            <div class="searchBtn" :style="{color: sort.unhandled === 2 || sort.unhandled === 3 ? '#3562db' : '#7f848c'}" @click="sortChange('unhandled', [0, 1, ''].includes(sort.unhandled) ? 3 : (sort.unhandled == 3 ? 2 : ''))">
              时间
              <span>
                <i class="el-icon-caret-top" :style="{color: sort.unhandled === 2 ? '#3562db' : '#7f848c'}"></i>
                <i class="el-icon-caret-bottom" :style="{color: sort.unhandled === 3 ? '#3562db' : '#7f848c'}"></i>
              </span>
            </div>
            <div class="searchBtn" :style="{color: sort.unhandled === 0 || sort.unhandled === 1 ? '#3562db' : '#7f848c'}" @click="sortChange('unhandled', [2, 3, ''].includes(sort.unhandled) ? 0 : (sort.unhandled === 0 ? 1 : ''))">
              紧急
              <span>
                <i class="el-icon-caret-top" :style="{color: sort.unhandled === 1 ? '#3562db' : '#7f848c'}"></i>
                <i class="el-icon-caret-bottom" :style="{color: sort.unhandled === 0 ? '#3562db' : '#7f848c'}"></i>
              </span>
            </div>
          </div>
          <div class="heade-selectAll">
            <el-checkbox v-model="selectAll.unhandled" :disabled="!list.unhandled.length" style="width: 70px;" :indeterminate="indeterminate.unhandled" @change="(val) => slelectAll('unhandled', val)">全选</el-checkbox>
          </div>
          <div v-scrollbarHover v-infinite-scroll="() => {pagingLoad('unhandled')}" class="contentList-main">
            <alarmList ref="unhandled" type="unhandled" :listData="list.unhandled" @selectChange="selectChange" v-on="$listeners" />
            <!-- <span v-if="loading.unhandled" class="noData">加载中...</span> -->
            <span v-if="(list.unhandled.length >= pagingInfo.unhandled.total) && !loading.unhandled && list.unhandled.length" class="noData">没有更多了</span>
          </div>
        </div>
      </el-col>
      <el-col :xs="24" :lg="12" style="height: 100%; padding-top: 16px;">
        <div v-loading="loading.processing" class="contentList processingList">
          <div class="heade-search">
            <div class="searchBtn" :style="{color: sort.processing === 2 || sort.processing === 3 ? '#3562db' : '#7f848c'}" @click="sortChange('processing', [0, 1, ''].includes(sort.processing)? 3 : (sort.processing == 3 ? 2 : ''))">
              时间
              <span>
                <i class="el-icon-caret-top" :style="{color: sort.processing === 2 ? '#3562db' : '#7f848c'}"></i>
                <i class="el-icon-caret-bottom" :style="{color: sort.processing === 3 ? '#3562db' : '#7f848c'}"></i>
              </span>
            </div>
            <div class="searchBtn" :style="{color: sort.processing === 0 || sort.processing === 1 ? '#3562db' : '#7f848c'}" @click="sortChange('processing', [2, 3, ''].includes(sort.processing) ? 0 : (sort.processing === 0 ? 1 : ''))">
              紧急
              <span>
                <i class="el-icon-caret-top" :style="{color: sort.processing === 1 ? '#3562db' : '#7f848c'}"></i>
                <i class="el-icon-caret-bottom" :style="{color: sort.processing === 0 ? '#3562db' : '#7f848c'}"></i>
              </span>
            </div>
          </div>
          <div class="heade-selectAll">
            <el-checkbox v-model="selectAll.processing" :disabled="!list.processing.length" style="width: 70px;" :indeterminate="indeterminate.processing" @change="(val) => slelectAll('processing', val)">全选</el-checkbox>
          </div>
          <div v-scrollbarHover v-infinite-scroll="() => {pagingLoad('processing')}" class="contentList-main">
            <alarmList ref="processing" type="processing" :listData="list.processing" @selectChange="selectChange" v-on="$listeners" />
            <!-- <span v-if="loading.processing" class="noData">加载中...</span> -->
            <span v-if="(list.processing.length >= pagingInfo.processing.total) && !loading.processing && list.processing.length" class="noData">没有更多了</span>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import alarmList from './alarmList'
import { mapGetters } from 'vuex'
export default {
  name: 'unhandledTable',
  components: { alarmList },
  props: {
    params: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      showShielded: false, // 是否显示屏蔽
      sort: {
        unhandled: '', // 未处理排序 0.按紧急降序  1.按紧急升序  2.按时间降序  3.按时间升序
        processing: '' // 处理中排序 0.按紧急降序  1.按紧急升序  2.按时间降序  3.按时间升序
      },
      list: {
        shielded: [], // 已屏蔽列表
        unhandled: [], // 未处理列表
        processing: [] // 处理中列表
      },
      loading: {
        shielded: false,
        unhandled: false,
        processing: false
      },
      selectAll: {
        unhandled: false,
        processing: false
      }, // 是否全选
      indeterminate: {
        unhandled: false,
        processing: false
      },
      selectList: {
        shielded: [],
        unhandled: [],
        processing: []
      }, // 已选择id
      pagingInfo: {
        shielded: {
          total: 0,
          pageSize: 10,
          pageNo: 1
        },
        unhandled: {
          total: 0,
          pageSize: 10,
          pageNo: 1
        },
        processing: {
          total: 0,
          pageSize: 10,
          pageNo: 1
        }
      } // 分页信息
    }
  },
  // computed: {
  //   scrollDisabled() {
  //     return (key) => {
  //       return (this.list[key].length >= this.pagingInfo[key].total) || this.loading[key]
  //     }
  //   }
  // },
  computed: {
    ...mapGetters({
      socketMsgs: 'socket/socketMsgs'
    })
  },
  created() {
    this.getAlarmRecord('shielded')
    this.getAlarmRecord('unhandled')
    this.getAlarmRecord('processing')
  },
  methods: {
    clearSelect() {
      this.selectList = { shielded: [], unhandled: [], processing: [] }
      if (this.list.shielded.length) {
        this.$refs.shielded.selectList = []
      }
      if (this.list.unhandled.length) {
        this.$refs.unhandled.selectList = []
      }
      if (this.list.processing.length) {
        this.$refs.processing.selectList = []
      }
      this.selectAll = { unhandled: false, processing: false}
      this.indeterminate = { unhandled: false, processing: false}
    },
    // 获取报警记录
    getAlarmRecord(key, page = this.pagingInfo[key].pageNo) {
      let { projectCode, incidentType, alarmLevel, alarmSpaceId, dataRange, objectId } = this.params
      let params = {
        pageSize: this.pagingInfo[key].pageSize,
        pageNo: page,
        alarmStatus: key == 'shielded' ? 2 : (key == 'unhandled' ? 0 : 1),
        timeOrType: this.sort[key],
        projectCode: projectCode.toString(),
        objectId, incidentType, alarmLevel, alarmSpaceId,
        startTime: dataRange ? dataRange[0] : '',
        endTime: dataRange ? dataRange[1] : ''
      }
      this.loading[key] = true
      if (page == 1) {
        this.list[key] = []
        this.pagingInfo[key].total = 0
        this.pagingInfo[key].pageNo = page
      }
      this.$api.GetAlarmRecord(params).then((res) => {
        this.loading[key] = false
        if (res.code == 200) {
          this.list[key] = this.list[key].concat(res.data.records)
          this.pagingInfo[key].total = res.data.total
          // 请求到新数据更新全选选中状态
          this.indeterminate[key] = this.selectList[key].length > 0 && this.selectList[key].length < this.list[key].length
          this.selectAll[key] = this.selectList[key].length === this.list[key].length
          if (key == 'unhandled') {
            let alarm = JSON.parse(this.socketMsgs)
            if (alarm.type && alarm.type == 'alarm') {
              this.$nextTick(() => {
                try {
                  document.getElementById('unhandled' + alarm.data.alarmData.alarmId).classList.add('alarmShine')
                } catch (error) {}
              })
            }
          }
        } else {
          this.list[key] = []
          this.pagingInfo[key].total = 0
        }
      }).catch((err) => {
        this.loading[key] = false
      })
    },
    // 排序查询
    sortChange(key, val) {
      console.log(key, val)
      this.sort[key] = val
      this.slelectAll(key, false)
      this.getAlarmRecord(key, 1)
    },
    // 分页加载
    pagingLoad(key) {
      if (this.list[key].length < this.pagingInfo[key].total) {
        this.pagingInfo[key].pageNo += 1
        this.getAlarmRecord(key)
      }
    },
    // 全选
    slelectAll(key, val) {
      this.$refs[key].selectList = val ? this.list[key].map(item => item.id) : []
      this.selectList[key] = val ? this.list[key].map(item => item.id) : []
      this.indeterminate[key] = false
      this.selectAll[key] = val
      // this.$emit('getSelectList', [...this.selectList.shielded, ...this.selectList.unhandled, ...this.selectList.processing])
      this.$emit('getSelectList', [
        ...this.list.shielded.filter(item => this.selectList.shielded.includes(item.id)),
        ...this.list.unhandled.filter(item => this.selectList.unhandled.includes(item.id)),
        ...this.list.processing.filter(item => this.selectList.processing.includes(item.id))
      ])
    },
    // 复选框选择
    selectChange(key, val) {
      let checkedCount = val.length
      this.selectList[key] = val
      this.selectAll[key] = checkedCount === this.list[key].length
      this.indeterminate[key] = checkedCount > 0 && checkedCount < this.list[key].length
      this.$emit('getSelectList', [
        ...this.list.shielded.filter(item => this.selectList.shielded.includes(item.id)),
        ...this.list.unhandled.filter(item => this.selectList.unhandled.includes(item.id)),
        ...this.list.processing.filter(item => this.selectList.processing.includes(item.id))
      ])
    }
  }
}
</script>
<style lang="scss" scoped>
.unhandledTable {
  height: 100%;
  display: flex;
  flex-direction: column;
  .heade-shielded {
    min-height: 46px;
    max-height: 32%;
    padding: 16px 0 0;
    position: relative;
    .shielded-hide {
      padding: 5px 12px;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      background: rgb(53 98 219 / 20%);
      border-radius: 100px;
      display: flex;
      align-items: center;
      color: #121f3e;
      font-size: 14px;
    }
    .shielded-info {
      display: flex;
      align-items: center;
    }
    .shielded-hide-icon {
      font-size: 16px;
    }
    .shielded-open {
      user-select: none;
      cursor: pointer;
      color: #3562db;
      display: flex;
      align-items: center;
    }
    .shielded-open-icon {
      font-size: 10px;
      margin-left: 5px;
    }
    .shielded-show {
      height: 100%;
      background: #fff;
      padding: 16px 16px 4px;
      border-radius: 4px;
      overflow: hidden;
      .shielded-show-heade {
        display: flex;
        font-size: 14px;
      }
      .noData {
        display: inline-block;
        padding-bottom: 10px;
        width: 100%;
        margin: 0;
        font-size: 14px;
        color: #999;
        text-align: center;
      }
    }
  }
  .contentList {
    height: 100%;
    border-radius: 6px;
    padding: 13px 16px;
    display: flex;
    flex-direction: column;
    .heade-search {
      display: flex;
      justify-content: flex-end;
      .searchBtn {
        user-select: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 66px;
        height: 26px;
        background: #f6f5fa;
        border-radius: 4px;
        margin-left: 8px;
        font-size: 14px;
        color: #7f848c;
        span {
          display: flex;
          flex-direction: column;
          i {
            width: 10px;
            height: 10px;
            font-size: 10px;
            margin-top: -2px;
            margin-bottom: -2px;
          }
        }
      }
    }
    .heade-selectAll {
      padding: 20px 0 20px 12px;
    }
    .contentList-main {
      flex: 1;
      overflow: auto;
      .noData {
        display: inline-block;
        width: 100%;
        margin: 0;
        font-size: 14px;
        color: #999;
        text-align: center;
      }
    }
  }
  .unhandledList {
    background: #fff url("~@/assets/images/alarmCenter/weichuli_img.png") no-repeat 0 12px / 86px 28px;
  }
  .processingList {
    background: #fff url("~@/assets/images/alarmCenter/chulizhon_img.png") no-repeat 0 12px / 86px 28px;
  }
  ::v-deep .is-checked {
    .el-checkbox__label {
      color: #3562db;
    }
  }
  ::v-deep .el-checkbox {
    width: 100%;
    .el-checkbox__input {
      vertical-align: top;
      .el-checkbox__inner {
        margin-top: 3px;
      }
    }
  }
}
</style>
