<template>
  <PageContainer class="dictionary-list">
    <template #content>
      <div class="dictionary-list__right">
        <div class="dictionary-list__right__header">
          <div>
            <el-input v-model="searchForm.code" clearable filterable placeholder="请输入路线编号" style="width: 200px; margin-right: 10px"></el-input>
            <el-input v-model="searchForm.name" clearable filterable placeholder="请输入路线名称" style="width: 200px; margin-right: 10px"></el-input>
            <el-input v-model="searchForm.startStation" clearable filterable placeholder="请输入始发地" style="width: 200px; margin-right: 10px"></el-input>
            <el-input v-model="searchForm.endStation" clearable filterable placeholder="请输入目的地" style="width: 200px; margin-right: 10px"></el-input>
            <el-select v-model="searchForm.status" placeholder="请选择状态" style="width: 200px; margin-right: 10px">
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" plain @click="onReset">重置</el-button>
          </div>
          <div class="dictionary-list__right__actions">
            <el-button type="primary" :disabled="tableClickArry.length <= 1" @click="doDelete('0')">批量删除</el-button>
            <el-button type="primary" :disabled="tableClickArry.length < 1" @click="updateState('0')">批量启用</el-button>
            <el-button type="primary" :disabled="tableClickArry.length < 1" @click="updateState('1')">批量禁用</el-button>
            <el-button type="primary" @click="roadmapExport()">导出</el-button>
            <el-button type="primary" @click="roadmapImport">导入</el-button>
            <el-button type="primary" @click="onOperate('add')">新建</el-button>
          </div>
        </div>

        <div class="dictionary-list__table">
          <el-table
            ref="multipleTable"
            v-loading="tableLoadingStatus"
            height="100%"
            :data="tableData"
            border
            stripe
            table-layout="auto"
            @selection-change="handleSelectionChange"
            class="tableAuto"
            row-key="id"
          >
            <el-table-column type="selection" width="60" :reserve-selection="true" align="center"></el-table-column>
            <el-table-column label="路线编号" prop="code" show-overflow-tooltip></el-table-column>
            <el-table-column label="路线名称" prop="name" show-overflow-tooltip></el-table-column>
            <el-table-column label="始发地" prop="startStation" show-overflow-tooltip></el-table-column>
            <el-table-column label="目的地" prop="endStation" show-overflow-tooltip></el-table-column>
            <el-table-column label="启用状态" prop="status" show-overflow-tooltip>
              <template slot-scope="scope">
                <span>{{ scope.row.status == '0' ? '启用' : '禁用' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template #default="{ row }">
                <el-button type="text" @click="onOperate('detail', row)">详情</el-button>
                <el-button type="text" @click="onOperate('edit', row)"> 编辑 </el-button>
                <el-button type="text" class="delete" @click="doDelete('1', row)"> 删除 </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination
          :current-page="pagination.current"
          :page-sizes="pagination.pageSizeOptions"
          :page-size="pagination.size"
          :layout="pagination.layoutOptions"
          :total="pagination.total"
          @size-change="paginationSizeChange"
          @current-change="paginationCurrentChange"
        >
        </el-pagination>
      </div>
      <!--路线新增-->
      <busRoadmapForm ref="showRoadmap" :dialogShow="dialogShow" :OperateType="OperateType" :operateId="operateId" @success="getDataList" @closeRoadDialog="closeRoadDialog" />
      <!-- 导入 -->
      <roadmapImportForm ref="organization" :roadmapVisible="roadmapVisible" @success="getDataList" @closeDialog="closeDialog" />
    </template>
  </PageContainer>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin'
import { UsingStatusOptions } from '@/views/operationPort/constant'
import axios from 'axios'
export default {
  name: 'DictionaryList',
  components: {
    busRoadmapForm: () => import('./components/busRoadmapForm'),
    roadmapImportForm: () => import('./components/roadmapImportForm.vue')
  },
  filters: {
    statusFilter(status) {
      return UsingStatusOptions.find((it) => it.value == status)?.label ?? '-'
    }
  },
  mixins: [tableListMixin],
  data() {
    return {
      currentKey: -1,
      treeSearchKeyWord: '',
      searchForm: {
        code: '',
        name: '',
        startStation: '',
        endStation: '',
        status: ''
      },
      treeData: [],
      treeLoadingStatus: false,
      tableData: [],
      tableLoadingStatus: false,
      dialogShow: false,
      currentImage: '', // 当前预览的图片
      roadmapVisible: false,
      tableClickArry: [],
      OperateType: '',
      operateId: '',
      options: [
        {
          value: '0',
          label: '启用'
        },
        {
          value: '1',
          label: '禁用'
        }
      ]
    }
  },
  mounted() {
    this.getDataList()
  },
  methods: {
    handleSelectionChange(val) {
      this.tableClickArry = val
      console.log(this.tableClickArry, 'this.tableClickArry')
    },
    // 获取列表数据
    getDataList() {
      this.tableLoadingStatus = true
      const params = {
        pageSize: this.pagination.size,
        pageNo: this.pagination.current,
        name: this.searchForm.name,
        code: this.searchForm.code,
        startStation: this.searchForm.startStation,
        endStation: this.searchForm.endStation,
        status: this.searchForm.status // 固定参数，工程类型
      }
      this.$api.fileManagement
        .roadmapList(params)
        .then((res) => {
          if (res.code === '200') {
            this.tableData = res.data.records
            this.pagination.total = res.data.total
          } else {
            throw res.msg || res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取列表数据失败'))
        .finally(() => {
          this.tableLoadingStatus = false
        })
    },
    // 表单搜索按钮点击
    onSearch() {
      this.pagination.current = 1
      this.getDataList()
    },
    // 表单重置按钮点击
    onReset() {
      this.searchForm = {
        code: '',
        name: '',
        startStation: '',
        endStation: '',
        status: ''
      }
      this.pagination.size = 15
      this.pagination.current = 1
      this.getDataList()
    },
    // 列表操作相关的事件绑定
    onOperate(type, row) {
      this.$refs.showRoadmap.resetForm()
      this.dialogShow = true
      this.OperateType = type
      if (this.OperateType != 'add') {
        this.operateId = row.id
      }
      this.$nextTick(() => {
        this.$refs.showRoadmap.$refs.formRef.clearValidate()
      })
    },
    closeRoadDialog() {
      this.dialogShow = false
      this.$refs.showRoadmap.resetForm()
    },
    // 删除一行数据
    doDelete(type, row) {
      let paramsId = {}
      if (type == '0') {
        paramsId.id = this.tableClickArry.map((item) => item.id).join(',')
      } else {
        paramsId.id = row.id
      }
      this.$confirm('次操作将删除选中路线信息，是否继续？', '信息提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        this.$api.fileManagement
          .roadmapDelete(paramsId)
          .then((res) => {
            if (res.code === '200') {
              this.$refs.multipleTable.clearSelection()
              this.$message.success('已删除')
              this.getDataList()
            } else {
              throw res.message || '删除失败'
            }
          })
          .catch((msg) => msg && this.$message.error(msg))
          .finally(() => (this.tableLoadingStatus = false))
      })
    },
    // 导出
    roadmapExport() {
      const userInfo = this.$store.state.user.userInfo.user
      const params = {
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ',
        pageSize: this.pagination.size,
        pageNo: this.pagination.current,
        name: this.searchForm.name,
        code: this.searchForm.code,
        startStation: this.searchForm.startStation,
        endStation: this.searchForm.endStation,
        status: this.searchForm.status // 固定参数，工程类型
      }
      if (this.tableClickArry.length > 0) {
        params.ids = this.tableClickArry.map((i) => i.id).join(',')
      } else {
        params.ids = ''
      }
      axios({
        method: 'post',
        url: __PATH.SPACE_API + 'busLine/exportExcel',
        data: params,
        responseType: 'blob',
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token,
          'Content-Type': 'application/json'
        }
      })
        .then((res) => {
          let name = '路线.xlsx'
          const blob = new Blob([res.data])
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
        })
        .catch(() => {
          this.$message.error('导出失败')
        })
    },
    // 导入
    roadmapImport() {
      this.roadmapVisible = true
    },
    closeDialog() {
      this.roadmapVisible = false
    },
    // 批量修改状态
    updateState(val) {
      let paramsIds = {
        id: this.tableClickArry.map((item) => item.id).join(',')
      }
      if (val == '0') {
        paramsIds.status = '0'
      } else {
        paramsIds.status = '1'
      }
      this.tableLoadingStatus = true
      this.$api.fileManagement.batchState(paramsIds).then((res) => {
        this.tableLoadingStatus = false
        if (res.code == 200) {
          this.$message.success(res.msg)
          this.getDataList()
          this.$refs.multipleTable.clearSelection()
          console.log(this.tableClickArry, 'this.tableClickArry')
        } else {
          this.$message.error(res.msg)
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.dictionary-list {
  ::v-deep(> .container-content) {
    display: flex;
    background-color: #fff;
  }
  &__right {
    flex: 1;
    overflow: hidden;
    padding: 16px;
    display: flex;
    flex-flow: column nowrap;
  }
  &__right__header {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  &__right__actions {
    margin: 20px 0px;
  }
  &__table {
    height: calc(100% - 150px);
    overflow: auto;
  }
  &__pagination {
    margin-top: 10px;
  }
  &__tag {
    // 停用
    &--0 {
      --color: #f64646;
    }
    // 启用
    &--1 {
      --color: #00b42a;
    }
    &:before {
      content: '';
      display: inline-block;
      vertical-align: middle;
      margin-right: 4px;
      height: 4px;
      width: 4px;
      border-radius: 4px;
      background-color: var(--color);
    }
  }
  &__table__color {
    display: inline-block;
    height: 16px;
    width: 16px;
    vertical-align: text-top;
    margin-right: 4px;
  }
  ::v-deep(.dictionary-list__table__col-img) {
    padding: 0;
    .cell {
      padding: 4px;
    }
    .el-image {
      height: 38px;
      .el-image__error {
        background-color: transparent;
      }
    }
  }
  .delete {
    color: red !important;
  }
}
</style>
