<template>
  <el-dialog v-if="visible" v-dialogDrag :modal="false" :close-on-click-modal="false"
    :title="typeNum == 'people' ? title + '/人员抓拍' : title + '/安检照片'" width="60%" :visible.sync="visible"
    custom-class="model-dialog" :before-close="closeDialog">
    <div class="header">
      <div class="searchForm">
        <div class="search-box">
          <el-date-picker v-model="searchFrom.dataRange" type="daterange" unlink-panels range-separator="至"
            start-placeholder="拍摄开始日期" end-placeholder="拍摄结束日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
            :clearable="false" :picker-options="pickerOptions" @change="timeListData">
          </el-date-picker>
          <el-input v-model="searchFrom.devicePosition" placeholder="设备名称"
            style="width: 200px; margin-right: 20px"></el-input>
          <el-select v-model="searchFrom.onlineState" filterable placeholder="分析结果"
            style="width: 200px; margin-right: 20px">
            <el-option v-for="item in resultList" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
          <div class="ml-16">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button type="primary" plain @click="reset">重置</el-button>
          </div>
        </div>
      </div>
      <div ref="contentRef" class="table-box">
        <div class="tableContainer">
          <el-table v-loading="tableLoading" height="calc(100% - 96px)" :data="tableData" border stripe>
            <el-table-column fixed type="index" width="70" label="序号"> </el-table-column>
            <el-table-column prop="devicePosition" label="设备名称" width="180" align="center"></el-table-column>
            <el-table-column prop="snapTime" label="拍摄时间" align="center"></el-table-column>
            <el-table-column v-if="typeNum == 'people'" prop="temperature" label="人员体温(度)"
              align="center"></el-table-column>
            <el-table-column v-if="typeNum == 'people'" prop="alarmPosition" label="报警位置" align="center">
              <template slot-scope="scope">
                <span>--</span>
              </template>
            </el-table-column>
            <el-table-column prop="abnormal" label="分析结果" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.abnormal == '0' ? '正常' : '异常' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="异常描述" align="center" prop="alarmGoods"></el-table-column>
            <el-table-column label="照片" align="center">
              <template slot-scope="scope">
                <div class="demo-image__preview">
                  <el-image style="width: 100px; height: 100px" :src="scope.row.snapUrl"
                    :preview-src-list="[scope.row.snapUrl]">
                  </el-image>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination :current-page="pagination.pageNum" :page-sizes="[15, 30, 50, 100]"
            :page-size="pagination.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="pageTotal"
            @size-change="handleSizeChange" @current-change="handleCurrentChange">
          </el-pagination>
        </div>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import moment from 'moment'
export default {
  name: 'captureList',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    positionId: {
      type: String,
      default: ''
    },
    typeNum: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      searchFrom: {
        dataRange: [moment().format('YYYY-MM-DD') + ' 00:00:00', moment().format('YYYY-MM-DD') + ' 23:59:59'],
        devicePosition: '',
        alarmSpaceId: ''
      },
      pickerOptions: {},
      resultList: [
        {
          value: '0',
          label: '正常'
        },
        {
          value: '1',
          label: '异常'
        },
      ],
      tableData: [],
      tableLoading: false,
      pagination: {
        pageNum: 1,
        pageSize: 15
      },
      pageTotal: 0,
    }
  },
  created() {

  },
  mounted() {
    this.getTableData()
  },
  methods: {
    timeListData(val) {
      this.searchFrom.dataRange[0] = val[0] + ' 00:00:00'
      this.searchFrom.dataRange[1] = val[1] + ' 23:59:59'
    },
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.pagination.pageNum = 1
      this.getTableData()
    },
    getTableData() {
      this.tableLoading = true
      let params = {
        abnormal: this.searchFrom.onlineState,
        //   alarmPosition: "string",
        beginTime: this.searchFrom.dataRange[0],
        currentPage: this.pagination.pageNum,
        devicePosition: this.searchFrom.devicePosition,//设备名称
        devicePositionId: this.positionId,
        endTime: this.searchFrom.dataRange[1],
        pageSize: this.pagination.pageSize,
        type: this.typeNum == 'people' ? '1' : '2'
      }
      this.$api.getSnopList(params).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.records
          this.pageTotal = res.data.total
        } else {
          this.$message.error(res.msg)
        }
        this.tableLoading = false
      })
    },
    search() {
      this.pagination.pageNum = 1
      this.getTableData()
    },
    reset() {
      this.searchFrom = {
        dataRange: [moment().format('YYYY-MM-DD') + ' 00:00:00', moment().format('YYYY-MM-DD') + ' 23:59:59'],
        devicePosition: '',
        alarmSpaceId: ''
      },
        this.pagination.pageNum = 1
      this.pageTotal = 0
      this.getTableData()
    },
    handleCurrentChange(val) {
      this.pagination.pageNum = val
      this.getTableData()
    },
    closeDialog() {
      this.$emit('update:visible', !this.visible)
    },
  }
}
</script>
<style lang="scss" scoped>
.header {
  width: 100%;
}

.searchForm {
  display: flex;
  height: 80px;
  justify-content: space-between;
  background-color: #fff;
  align-items: center;
  width: 100%;

  .el-input {
    width: 200px;
    margin-left: 16px;
  }
}

.search-box {
  display: flex;
  align-items: center;
  padding: 24px
}

.container-content>div {
  height: 100%;
  margin-top: 16px;
}

.el-table {
  margin-bottom: 12px;
}

::v-deep .el-table__row:hover {
  cursor: pointer;
}

.table-box {
  background-color: #fff;
  height: 700px;
  padding: 16px;

  .tableContainer {
    height: 100%;

    .el-table {
      height: calc(100% - 96px) !important;
    }
  }
}

.diaContent {
  width: 100%;
  max-height: 500px !important;
  overflow: auto;
  background-color: #fff !important;

  .el-input,
  .el-select,
  .el-cascader {
    width: 300px;
  }
}

.ml-16 {
  margin-left: 16px;
}

.dialog .el-dialog {
  width: 60% !important;
}

.online {
  color: #33cc00;
}

.offOnline {
  color: #ff0000;
}

.exhale {
  color: #ff9900;
}
</style>