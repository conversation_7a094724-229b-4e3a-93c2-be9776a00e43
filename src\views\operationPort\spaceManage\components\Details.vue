<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="sino-content heigth-1366" style="height: calc(100% - 5px) !important; padding-top: 0; padding-bottom: 25px">
        <div class="content">
          <el-timeline>
            <template v-for="(item, index) in detailData">
              <!-- 基本信息 -->
              <el-timeline-item v-if="item.optType == '0'" :key="index" :timestamp="item.createRecordTime" placement="top" size="large" icon="el-icon-time">
                <el-collapse accordion value="1">
                  <el-collapse-item name="1">
                    <template slot="title">{{ item.title }}</template>
                    <div class="content-box">
                      <div class="item">
                        <span>检查类型:</span>
                        <span>{{ item.checkName }}</span>
                      </div>
                      <div class="item">
                        <span>隐患等级:</span>
                        <span>{{ item.riskName }}</span>
                      </div>
                      <div class="item">
                        <span>隐患分类:</span>
                        <span>{{ item.questionDetailType }}</span>
                      </div>
                      <div class="item">
                        <span>反馈部门:</span>
                        <span>{{ item.createByDeptName }}</span>
                      </div>
                      <div class="item">
                        <span>反馈人:</span>
                        <span>{{ item.createPersonName }}</span>
                      </div>
                      <div class="item">
                        <span>电话:</span>
                        <span>{{ item.createPersonPhone }}</span>
                      </div>
                      <div class="item">
                        <span>隐患区域:</span>
                        <span>{{ item.questionAddress }}</span>
                      </div>
                      <div class="item">
                        <span>反馈时间:</span>
                        <span>{{ item.createTime }}</span>
                      </div>
                      <div class="item">
                        <span>要求整改完成时间:</span>
                        <span>{{ item.rectificationPlanTime }}</span>
                      </div>
                      <div class="item desc">
                        <span>隐患描述:</span>
                        <span>{{ item.questionContent }}</span>
                      </div>
                      <div class="item desc">
                        <span>语音:</span>
                        <audio v-if="item.questionVoiceUrl" style="height: 30px" :src="item.questionVoiceUrl" controls="controls"></audio>
                        <span v-else>当前任务未上传录音</span>
                      </div>
                      <div class="item desc">
                        <span>附件:</span>
                        <el-image
                          v-for="item2 in item.questionAttachmentList"
                          :key="item2"
                          style="width: 100px; height: 100px"
                          :src="item2"
                          :preview-src-list="item.questionAttachmentList"
                        >
                        </el-image>
                      </div>
                    </div>
                  </el-collapse-item>
                </el-collapse>
              </el-timeline-item>
              <!--  指派信息 -->
              <el-timeline-item v-if="item.optType == '1'" :key="index" :timestamp="item.createRecordTime" placement="top" size="large" icon="el-icon-time">
                <el-collapse accordion value="2">
                  <el-collapse-item name="2">
                    <template slot="title">{{ item.title }}</template>
                    <div class="content-box">
                      <div class="item">
                        <span>部门:</span>
                        <span>{{ item.dutyDeptName }}</span>
                      </div>
                      <div class="item">
                        <span>人员:</span>
                        <span>{{ item.claimPersonName }}</span>
                      </div>
                      <div class="item">
                        <span>电话:</span>
                        <span>{{ item.claimPersonPhone }}</span>
                      </div>
                      <div class="item">
                        <span>时间:</span>
                        <span>{{ item.claimTime }}</span>
                      </div>
                      <div class="item">
                        <span>超时状态:</span>
                        <span>{{ item.overTimeState }}</span>
                      </div>
                      <div class="item">
                        <span>超时时长:</span>
                        <span>{{ item.overTimeOften }}</span>
                      </div>
                    </div>
                  </el-collapse-item>
                </el-collapse>
              </el-timeline-item>
              <!-- 整改信息 -->
              <el-timeline-item v-if="item.optType == '2'" :key="index" :timestamp="item.createRecordTime" placement="top" size="large" icon="el-icon-time">
                <el-collapse accordion value="3">
                  <el-collapse-item name="3">
                    <template slot="title">{{ item.title }}</template>
                    <div class="content-box">
                      <div class="item">
                        <span>部门:</span>
                        <span>{{ item.handleDeptName }}</span>
                      </div>
                      <div class="item">
                        <span>人员:</span>
                        <span>{{ item.handlePersonName }}</span>
                      </div>
                      <div class="item">
                        <span>电话:</span>
                        <span>{{ item.handlePersonPhone }}</span>
                      </div>
                      <div class="item">
                        <span>时间:</span>
                        <span>{{ item.handleTime }}</span>
                      </div>
                      <div class="item desc">
                        <span>隐患描述:</span>
                        <span>{{ item.finishContent }}</span>
                      </div>
                      <div class="item desc">
                        <span>语音:</span>
                        <audio v-if="item.finishVoiceUrl" style="height: 30px" :src="item.finishVoiceUrl" controls="controls"></audio>
                        <span v-else>当前任务未上传录音</span>
                      </div>
                      <div class="item desc">
                        <span>附件:</span>
                        <el-image v-for="item2 in item.finishPicUrlList" :key="item2" style="width: 100px; height: 100px" :src="item2" :preview-src-list="item.finishPicUrlList">
                        </el-image>
                      </div>
                    </div>
                  </el-collapse-item>
                </el-collapse>
              </el-timeline-item>
              <!-- 挂帐信息 -->
              <el-timeline-item v-if="item.optType == '3'" :key="index" :timestamp="item.createRecordTime" placement="top" size="large" icon="el-icon-time">
                <el-collapse accordion value="4">
                  <el-collapse-item name="4">
                    <template slot="title">{{ item.title }}</template>
                    <div class="content-box">
                      <div class="item">
                        <span>挂帐时间:</span>
                        <span>{{ item.paymentTime }}</span>
                      </div>
                      <div class="item">
                        <span>预计解决时间:</span>
                        <span>{{ item.estimateSolutionTime }}</span>
                      </div>
                      <div class="item">
                        <span>管控部门:</span>
                        <span>{{ item.recordDeptName }}</span>
                      </div>
                      <div class="item desc">
                        <span>挂帐原因描述:</span>
                        <span>{{ item.paymentReason }}</span>
                      </div>
                      <div class="item desc">
                        <span>现阶段防控措施:</span>
                        <span>{{ item.riskControl }}</span>
                      </div>
                      <div class="item desc">
                        <span>整改计划:</span>
                        <span>{{ item.rectificationPlan }}</span>
                      </div>
                    </div>
                  </el-collapse-item>
                </el-collapse>
              </el-timeline-item>
              <!-- 审核信息 -->
              <el-timeline-item v-if="item.optType == '14'" :key="index" :timestamp="item.createRecordTime" placement="top" size="large" icon="el-icon-time">
                <el-collapse accordion value="5">
                  <el-collapse-item name="5">
                    <template slot="title">{{ item.title }}</template>
                    <div class="content-box">
                      <div class="item">
                        <span>部门:</span>
                        <span>{{ item.recordDeptName }}</span>
                      </div>
                      <div class="item">
                        <span>人员:</span>
                        <span>{{ item.currentAuditPersonName }}</span>
                      </div>
                      <div class="item">
                        <span>电话:</span>
                        <span>{{ item.currentAuditPersonPhone }}</span>
                      </div>
                      <div class="item desc">
                        <span>时间:</span>
                        <span>{{ item.checkTime }}</span>
                      </div>
                      <div class="item" style="width: 30%">
                        <span>审核结论:</span>
                        <span>{{ item.auditResult }}</span>
                      </div>
                      <div class="item" style="width: 50%">
                        <span>满意度:</span>
                        <el-rate v-model="item.starLevel" disabled text-color="#ff9900"> </el-rate>
                      </div>
                      <div class="item desc">
                        <span>审核评价:</span>
                        <span>{{ item.currentAuditEvaluate }}</span>
                      </div>
                    </div>
                  </el-collapse-item>
                </el-collapse>
              </el-timeline-item>
            </template>
          </el-timeline>
        </div>
      </div>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="revert">取消</el-button>
    </div>
  </PageContainer>
</template>
<script>
export default {
  name: 'Details',
  data() {
    return {
      loading: true,
      id: '',
      detailData: {}
    }
  },
  created() {
    this.id = this.$route.query.id
  },
  mounted() {
    this.getData()
  },
  methods: {
    revert() {
      this.$router.go(-1)
      // this.$router.push({
      //   path: '/spaceManage/dataManage',
      //   query: {
      //     pageModel: 'list'
      //   }
      // })
    },
    getData() {
      this.$api.getHistoryFollow({ id: this.id }).then((res) => {
        if (res.code == 200) {
          this.detailData = res.data
        } else {
          this.$message.error(res.message)
        }
        this.loading = false
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
  padding: 10px;
  overflow-y: auto;
}
// ::v-deep  .el-timeline-item__timestamp.is-top {
//     transform: translate(-168px,5px);
// }
// ::v-deep  .el-timeline-item__content {
//     transform: translateY(-35px);
// }
::v-deep .el-timeline-item__icon {
  font-size: 24px;
}

::v-deep .el-timeline-item__node--large {
  width: 24px;
  height: 24px;
  left: -6px;
}

::v-deep .el-timeline-item__content {
  padding-left: 8px;
}

::v-deep .el-collapse-item__header {
  font-size: 16px;
}

.item span {
  font-size: 14px;
}

.content-box {
  display: flex;
  flex-wrap: wrap;
}

.content-box .item {
  width: 30%;
  margin-bottom: 12px;
}

.content-box .desc {
  width: 80%;
}

.content-box .item span:nth-child(2) {
  color: grey;
}

.content-box .item span:nth-child(1) {
  margin-right: 12px;
}

.el-image {
  margin-right: 12px;
}

.el-rate {
  display: inline-block;
}
</style>
