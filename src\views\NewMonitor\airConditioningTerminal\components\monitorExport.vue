<template>
  <el-dialog title="请选择" width="40%" :visible.sync="exportDialogShow" custom-class="model-dialog"
    :before-close="closeDialog">
    <div class="export_content" style="padding: 10px 20px 10px 10px;">
      <el-form ref="exportForm" :model="exportParameter" :rules="exportRules" label-width="100px" style="display:flex">
        <el-form-item label="专业类别:" prop="professionalCategoryCode">
          <el-select v-model.trim="exportParameter.professionalCategoryCode" clearable filterable placeholder="专业类别">
            <el-option v-for="item in majorList" :key="item.id" :label="item.baseName" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="厂商:" prop="manufacturer">
          <el-select ref="treeSelect" v-model="exportParameter.manufacturer" clearable filterable placeholder="厂商">
            <el-option v-for="item in manufacturerList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="groupSubmit('exportForm')">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  name: 'monitorExport',
  props: {
    exportDialogShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      exportParameter: {
        professionalCategoryCode: '', // 资产类别
        manufacturer: '' // 厂商
      },
      exportRules: {
        professionalCategoryCode: [
          { required: true, message: '请选择资产类别', trigger: 'change' }
        ],
        manufacturer: [
          { required: true, message: '请选择厂商', trigger: 'change' }
        ]
      },
      majorList: [], // 专业类别列表
      manufacturerList: [] // 厂商list
    }
  },
  mounted() {
    this.getProfessionalCategoryData()
    this.getDictionaryData()
  },
  methods: {
    // 获取列表
    getProfessionalCategoryData() {
      // 专业类别
      this.$api
        .getDeviceType({
          levelType: 1
        })
        .then((res) => {
          if (res.code == '200') {
            this.majorList = res.data
          }
        })
    },
    // 获取字典项
    getDictionaryData() {
      this.$api
        .getDictionaryList({
          dictType: 3
        })
        .then((res) => {
          if (res.code == '200') {
            this.manufacturerList = res.data
          }
        })
    },
    closeDialog() {
      this.$emit('closeExportDialog')
    },
    groupSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$emit('submitExportDialog', this.exportParameter)
        } else {
          return false
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.model-dialog {
  height: 320px;
  .export_content {
    margin: 0 auto;
    background: #fff;
    border-radius: 4px;
    // display: flex;
    width: 100%;
    height: 100%;
  }
}
</style>
