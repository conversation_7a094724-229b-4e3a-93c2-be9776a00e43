<template>
  <PageContainer>
    <div slot="content" class="role-content" style="height: 100%;">
      <div class="role-content-left">
        <div class="toptip">
          <span class="green_line"></span>
          统计分析
        </div>
        <div class="left_content">
          <div v-loading="treeLoading" class="tree">
            <el-tree
              ref="tree"
              style="margin-top: 10px;"
              :check-strictly="true"
              :data="treeData"
              :props="defaultProps"
              node-key="dictValue"
              :highlight-current="true"
              :default-expanded-keys="expanded"
              @node-click="handleNodeClick"
            ></el-tree>
          </div>
        </div>
      </div>
      <div class="role-content-right">
        <div style="height: 100%;">
          <div class="search-from">
            <el-select v-model="filters.structureType" clearable collapse-tags placeholder="请选择责任部门" style="width: 230px; margin-right: 15px;">
              <el-option v-for="item in teamList" :key="item.id" :label="item.teamName" :value="item.id"></el-option>
            </el-select>
            <el-select v-if="isActived == '2'" v-model="filters.cycleType" multiple clearable placeholder="请选择周期类型" style="width: 230px; margin-right: 15px;">
              <el-option v-for="item in cycleTypeList" :key="item.cycleType" :label="item.label" :value="item.cycleType"> </el-option>
            </el-select>
            <el-select v-if="isActived == '2'" v-model="filters.skipDate" clearable placeholder="是否跳过周末" style="width: 230px; margin-right: 15px;">
              <el-option v-for="item in skipDateList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
            </el-select>
            <el-date-picker
              v-model="filters.timeIterval"
              type="daterange"
              style="margin-right: 16px;"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
            <el-button type="primary" plain @click="reset">重置</el-button>
            <el-button type="primary" @click="searchClick">查询</el-button>
            <el-button v-if="isActived == '2'" type="primary" @click="exportList">导出</el-button>
          </div>
          <div class="contentTable">
            <RiskAnalysis v-if="isActived == '1'" ref="RiskAnalysis" :filters="filters"></RiskAnalysis>
            <TasksAnalysis v-if="isActived == '2'" ref="TasksAnalysis" :filters="filters"></TasksAnalysis>
          </div>
        </div>
      </div>
    </div>
  </PageContainer>
</template>

<script>
import RiskAnalysis from './components/RiskAnalysis.vue'
import TasksAnalysis from './components/TasksAnalysis.vue'
export default {
  name: 'dataStatisticsList',
  components: {
    RiskAnalysis,
    TasksAnalysis
  },
  data() {
    return {
      treeLoading: false,
      defaultProps: {
        children: 'children',
        label: 'dictLabel',
        value: 'dictValue'
      },
      expanded: [],
      checkedData: [],
      treeData: [
        {
          dictLabel: '隐患统计',
          dictValue: 1
        },
        {
          dictLabel: '巡检任务统计',
          dictValue: 2
        }
      ],
      filters: {
        structureType: '', // 部门
        timeIterval: [] // 时间
      },
      isActived: '1',
      teamList: [], // 部门
      from: '',
      cycleTypeList: [
        {
          cycleType: 8,
          label: '单次'
        },
        {
          cycleType: 6,
          label: '每日'
        },
        {
          cycleType: 0,
          label: '每周'
        },
        {
          cycleType: 2,
          label: '每月'
        },
        {
          cycleType: 3,
          label: '季度'
        },
        {
          cycleType: 5,
          label: '全年'
        }
      ],
      // 跳过周末
      skipDateList: [
        {
          id: '1',
          name: '跳过周六'
        },
        {
          id: '0',
          name: '跳过周日'
        },
        {
          id: '2',
          name: '跳过周六日'
        }
      ]
    }
  },
  created() {
    this.getDataValue()
    this.from = this.$route.query.from
    // console.log(this.from, 'from')
  },
  mounted() {
    this.expanded = [this.treeData[0].dictValue]
    if (this.from == 'task') {
      this.checkedData = this.treeData[1]
      this.isActived = 2
    } else {
      this.checkedData = this.treeData[0]
    }
    this.$nextTick(() => {
      this.$refs.tree.setCurrentKey(this.checkedData.dictValue)
    })
  },
  methods: {
    getDataValue() {
      // 执行班组
      this.teamList = []
      this.$api.ipsmGetControlGroupInfoList({}).then((res) => {
        res.data.list.map((v) => {
          if (v.parent != '#') {
            this.teamList.push(v)
          }
        })
      })
    },
    // 重置
    reset() {
      if (this.isActived == '1') {
        this.filters = {
          structureType: '',
          timeIterval: []
        }
        this.$refs.RiskAnalysis.getData(this.filters)
        this.$refs.RiskAnalysis.getEchartsData(this.filters)
      } else {
        this.filters = {
          structureType: '',
          timeIterval: []
        }
        this.$refs.TasksAnalysis.getData(this.filters)
        this.$refs.TasksAnalysis.getEchartsData(this.filters)
      }
    },
    // 查询
    searchClick() {
      if (this.isActived == '1') {
        this.$refs.RiskAnalysis.getData()
        this.$refs.RiskAnalysis.getEchartsData()
      } else {
        this.$refs.TasksAnalysis.getData()
        this.$refs.TasksAnalysis.getEchartsData()
      }
    },
    // 导出
    exportList() {
      this.$refs.TasksAnalysis.exportList()
    },
    // 树状图点击
    handleNodeClick(data) {
      //   console.log('点击树状图', data)
      if (data.dictValue == this.isActived) return
      this.isActived = data.dictValue
      this.filters.structureType = ''
      this.filters.timeIterval = []
      this.checkedData = data
      this.$refs.tree.setCurrentKey(this.checkedData.dictValue)
    }
  }
}
</script>
<style lang="scss" scoped>
// el-tree-node is-expanded is-focusable
::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: linear-gradient(to right, #d9e1f8, #fff);
}

::v-deep .el-tree-node__content {
  height: 32px;
}

.role-content {
  height: 100%;
  display: flex;

  .role-content-left {
    width: 246px;
    height: 100%;
    padding: 10px;
    background: #fff;
    border-radius: 4px;
    margin-right: 12px;

    .left_content {
      height: calc(100% - 60px);
      // overflow: auto;
      .tree {
        height: calc(100% - 40px);
        overflow: auto;
      }

      ul {
        padding: 0;

        li {
          height: 38px;
          width: 100%;
          font-size: 14px;
          font-family: "PingFang SC-Regular", "PingFang SC";
          font-weight: 400;
          color: #606266;
          line-height: 38px;
          list-style-type: none;
          box-sizing: border-box;
          cursor: pointer;
          padding-left: 27px;

          &:hover {
            background: rgb(53 98 219 / 20%);
            border-radius: 4px 0 0 4px;
          }
        }
      }
    }
  }

  .role-content-right {
    flex: 1;
    height: 100%;
    min-width: 0;
    padding: 10px;
    background: #fff;
    border-radius: 4px;

    .search-from {
      padding-bottom: 12px;

      & > div {
        margin-right: 10px;
      }

      & > button {
        margin-top: 12px;
      }
    }

    .contentTable {
      height: calc(100% - 50px);
      display: flex;
      flex-direction: column;
      overflow: auto;
    }
  }

  .content {
    width: 100%;
    max-height: 500px !important;
    overflow: auto;
    background-color: #fff !important;
  }
}

.delet {
  color: red !important;
}

::v-deep .el-tabs__nav-scroll {
  width: 80%;
  margin: 0 auto;
}
</style>
