import { postFormData, postRequest, getRequest, getSecurityData } from '../http.js'
// 服务前缀
const PrefixService = __PATH.BASE_URL_HSC
const newMonitorApi = __PATH.VUE_MONITOR_API
export default {
  // 危化品库房总览
  materialinfoListData: postFormData('materialinfo/listData', PrefixService), // 危化品台账查询
  materialTypeTreeKC: postFormData('materialinfo/materialTypeTree', PrefixService), // 获取危化品分类树
  exportTitleModel: postFormData('materialinfo/exportTitleModel', PrefixService), // 导出列选择危化品台账
  // 库房
  getWarehouseDictList: postFormData('dictUtils/getDictList', PrefixService), // 获取库房字典
  queryWarehouseByPage: postFormData('warehouse/listData', PrefixService), // 库房列表
  queryWarehouseByPageAll: postFormData('warehouse/listDataAll', PrefixService), // 上级库房全部信息
  getWarehouseById: postFormData('warehouse/view', PrefixService), // 查询库房详情
  deleteWarehouseById: postFormData('warehouse/delete', PrefixService), // 删除
  saveWarehouseInfo: postFormData('warehouse/save', PrefixService), // 保存
  // 入库管理
  getWarehouseList: postFormData('warehouse/listData', PrefixService), // 库房下拉
  saveHcsData: postFormData('materialsDict/save', PrefixService), // 新增/编辑危化品
  getHcsById: postFormData('materialsDict/view', PrefixService), // 获取危化品详情
  getWarehouseType: postFormData('materialsTypeSetting/findMaterialsType', PrefixService), // 出入库仓库类型
  getUnitsLsitData: postFormData('unitsManage/lsitData', PrefixService), // 供应商/生产厂家下拉
  saveInwarehouse: postFormData('inwarehouseRecord/save', PrefixService), // 入库保存（差审批流）
  queryInwarehouseRecordByPage: postFormData('inwarehouseRecord/inwarehouseDetailList', PrefixService), // 入库明细查询
  getInwarehouseRecordById: postFormData('inwarehouseRecord/view', PrefixService), // 入库单详情
  queryInwarehouseByPage: postFormData('inwarehouseRecord/listData', PrefixService), // 入库单记录列表
  getHcsRecordByPage: postFormData('materialRecord/materialRecordDetial', PrefixService), // 危化品录详情(入库、出库)
  commitAuditInwarehouseData: postFormData('inwarehouseRecord/commitAudit', PrefixService), // 提交送审入库单
  deleteInwarehouseRecordData: postFormData('inwarehouseRecord/delete', PrefixService), // 入库删除
  withdrawInwarehouseData: postFormData('inwarehouseRecord/revocation', PrefixService), // 撤回（差审批流）
  inwarehouseRecordExportTitleModel: postFormData('inwarehouseRecord/exportTitleModel', PrefixService), // 入库导出title
  queryInWareHouseHcsByPage: postFormData('materialsDict/listDataInWareHouse', PrefixService), // 入库配件选择列表
  queryInWareHouseHcsByList: postFormData('warehouse/listMaterialsDataByWareHouseManage', PrefixService), // 仓库关联危化品
  // 出库管理
  getMaterialinfoByPage: postFormData('materialinfo/materiaInfoList', PrefixService), // 出库选择列表
  queryOutWarehouseRecordByPage: postFormData('outwarehouseRecord/outwarehouseDetailList', PrefixService), // 出库明细查询
  saveOutWarehouseRecordData: postFormData('outwarehouseRecord/save', PrefixService), // 出库保存（差审批流）
  getOutWarehouseRecordById: postFormData('outwarehouseRecord/view', PrefixService), // 出库单详情
  queryOutWarehouseByPage: postFormData('outwarehouseRecord/listData', PrefixService), // 出库单列表
  outWarehouseRecordExportTitleModel: postFormData('outwarehouseRecord/exportTitleModel', PrefixService), // 出库导出title
  deleteOutWarehouseRecordData: postFormData('outwarehouseRecord/delete', PrefixService), // 出库删除
  commitAuditoutwarehouseData: postFormData('outwarehouseRecord/commitAudit', PrefixService), // 提交送审出库单
  withdrawOutWarehouseData: postFormData('outwarehouseRecord/revocation', PrefixService), // 撤回（差审批流）
  queryOutWareHouseHcsByPage: postFormData('warehouse/listDeptDataByWareHouseManage', PrefixService), // 通过库房查询对应部门list
  getQueryRegularRecipients: postFormData('outwarehouseRecord/getCommonlyUsedRecipientsByDeptId', PrefixService), // 通过部门ID 查询常用领用人
  saveRegularRecipients: postRequest('outwarehouseRecord/saveCommonlyUsedRecipients', PrefixService), // 保存常用领用人
  delRegularRecipients: postFormData('outwarehouseRecord/deleteCommonlyUsedRecipients', PrefixService), // 删除常用领用人
  // 盘点管理
  queryTakeStockByPage: postRequest('takeStock/getTakeStockList', PrefixService), // 盘点列表
  deleteTakeStockById: postRequest('takeStock/deleteTakeStockByIds', PrefixService), // 盘点删除
  getTakeStockData: postRequest('takeStock/selectOrTakeStock', PrefixService), // 查询/开始盘点
  saveTakeStockData: postRequest('takeStock/saveTakeStock', PrefixService), // 保存盘点
  getTakeStockById: postRequest('takeStock/getTakeStockInfo', PrefixService), // 盘点详情
  // 领用申请
  queryReceiveApplyRecordByPage: postFormData('receiveApplyRecord/listData', PrefixService), // 领用申请管理列表
  getReceiveApplyRecordById: postFormData('receiveApplyRecord/view', PrefixService), // 领用申请管理 详情
  saveReceiveApplyRecordData: postFormData('receiveApplyRecord/save', PrefixService), // 领用申请管理 保存
  getReceiveApplyRecordType: postFormData('materialsTypeSetting/findMaterialsType', PrefixService), // 领用类型
  commitAuditReceiveApplyRecordData: postFormData('receiveApplyRecord/commitAudit', PrefixService), // 领用申请管理提交送审
  deleteReceiveApplyRecordData: postFormData('receiveApplyRecord/delete', PrefixService), // 领用申请管理删除
  withdrawReceiveApplyRecordData: postFormData('receiveApplyRecord/revocation', PrefixService), // 领用申请管理 撤回
  getHcsDictByPage: postFormData('materialsDict/listData', PrefixService), // 危化品字典库
  getHcsBrandList: postFormData('brand/listData', PrefixService), // 品牌列表
  getHcsRecordList: postFormData('materialRecord/materialLsit', PrefixService), // 危化品记录详情(入库、出库)
  // 库房风险等级
  queryRiskHouseByPage: postFormData('riskHouse/getRiskHouseList', PrefixService), // 库房风险列表
  getRiskHouseById: getRequest('riskHouse/detailById', PrefixService), // 库房风险详情
  addOrUpdateRiskHouseData: postRequest('riskHouse/addOrUpdateRiskHouse', PrefixService), // 库房风险详情
  // 安全巡查
  getCheckTableData: postRequest('/inspectionLedgerController/queryListPage', __PATH.VUE_ICIS_API), // 安全巡查记录
  getCheckTableDetail: postRequest('/inspectionLedgerController/getCheckDetails', __PATH.VUE_ICIS_API), // 安全巡查记录详情
  // 监测配置
  getHcsConfigDataList: postFormData('monitoringConfiguration/listData', PrefixService), // 配置列表
  getHcsConfigDetail: postFormData('monitoringConfiguration/viewMonitoring', PrefixService), // 查看配置
  deleteHcsConfig: postFormData('monitoringConfiguration/deleteMonitoring', PrefixService), // 删除配置
  addHcsConfig: postRequest('monitoringConfiguration/addMonitoring', PrefixService), // 新增配置
  editHcsConfig: postRequest('monitoringConfiguration/updateMonitoring', PrefixService), // 编辑配置
  getHcsTerminalList: postFormData('monitoringConfiguration/list', PrefixService), // 获取终端列表
  getMonitorType: getSecurityData('queryCategoryByCategoryId', newMonitorApi), // 运行监控分类
  getEquipmentByMonitor: postRequest('assetsInfo/page', newMonitorApi), // 监测分类下的设备
  getHcsCheckTerminalList: postFormData('monitoringConfiguration/selectedList', PrefixService) // 获取已选中终端列表
}
