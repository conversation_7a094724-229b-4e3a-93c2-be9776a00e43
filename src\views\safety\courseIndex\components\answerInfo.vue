<template>
  <el-drawer
    :visible.sync="drawerDialog"
    :with-header="true"
    size="55%"
    :show-close="true"
    :before-close="closeDrawer"
  >
    <div slot="title" class="coursrDrawer">
      课后习题（{{ exercisesList.length }}题，共{{ scoreSum }}分）
    </div>
    <div class="drawer_conter">
      <div class="content">
        <div class="table">
          <div
            v-for="(item, index) in exercisesList"
            :key="item.id"
            :name="item.id"
            :ref="'exercisesItem' + index"
            :class="['exercisesItem', item.isExpand ? 'expand' : '']"
          >
            <div class="exercisesTop">
              <div class="left">
                <div class="exercisesType">
                  {{
                    item.type == "1"
                      ? "单选题"
                      : item.type == "2"
                      ? "多选题"
                      : "判断题"
                  }}
                </div>
                <span>({{ item.id }})</span>
              </div>
              <div class="right">
                {{ item.score }}分
                <div class="line"></div>
                <span @click="isExpandBtn(item)">{{
                  item.isExpand ? "折叠" : "展开"
                }}</span>
              </div>
            </div>
            <div :class="['exercisesName', item.isExpand ? '' : 'title']">
              {{ item.topic }}
            </div>
            <div v-if="seeType == 'public'">
              <el-radio-group
                v-if="item.type == '1'"
                class="radio"
                v-model="item.answer"
                disabled
              >
                <el-radio
                  v-for="(k, index) in item.options"
                  :key="index"
                  :label="k.id"
                  >{{ k.id }}. {{ k.label }}</el-radio
                >
              </el-radio-group>
              <el-checkbox-group
                v-if="item.type == '2'"
                v-model="item.answer"
                class="radio"
                disabled
              >
                <el-checkbox
                  v-for="(k, index) in item.options"
                  :key="index"
                  :label="k.id"
                  >{{ k.id }}. {{ k.label }}</el-checkbox
                >
              </el-checkbox-group>
            </div>
            <div v-else>
              <el-radio-group
                v-if="item.type == '1'"
                class="radio"
                v-model="item.userAnswer"
                disabled
              >
                <el-radio
                  v-for="(k, index) in item.options"
                  :key="index"
                  :label="k.id"
                  >{{ k.id }}. {{ k.label }}</el-radio
                >
              </el-radio-group>
              <el-checkbox-group
                v-if="item.type == '2'"
                v-model="item.userAnswer"
                class="radio"
                disabled
              >
                <el-checkbox
                  v-for="(k, index) in item.options"
                  :key="index"
                  :label="k.id"
                  >{{ k.id }}. {{ k.label }}</el-checkbox
                >
              </el-checkbox-group>
            </div>
            <p>答案：{{ item | getAnswer }}</p>
            <p>
              解析：
              {{ item.analysis }}
            </p>
            <!-- 答题是否正确 -->
            <div class="isCorrect" v-if="seeType == 'my'">
              <img  v-if="item.isTrue" src="../../../../assets/images/correct.png" alt="" />
              <img v-else src="../../../../assets/images/error.png" alt="">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="drawer_footer">
      <el-button type="primary" plain @click="closeDrawer">关闭</el-button>
    </div>
  </el-drawer>
</template>

<script>
export default {
  props: {
    drawerDialog: {
      type: Boolean,
      default: false,
    },
    seeType: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      exercisesList: [],
      radio: "",
      paginationData: {
        pageNo: 1,
        pageSize: 15,
        total: 0,
      },
      routeInfo: {},
      scoreSum: 0,
    };
  },
  created() {
    this.routeInfo = JSON.parse(sessionStorage.getItem("routeInfo"));
  },
  filters:{
    getAnswer(val){
      if (val.type == "3") {
        return val.answer == "1" ? "正确" : "错误";
      } else if(val.type=='2'){
        return val.answer.toString()
      }else{
        return val.answer;
      }
    }
  },
  methods: {
    // 获取试题列表
    questionsList(item) {
      this.scoreSum = 0;
      if (item.coursePeriodQuestionList) {
        item.coursePeriodQuestionList.forEach((i) => {
          this.scoreSum += i.score || 0;
        });
      }
      this.exercisesList = item.coursePeriodQuestionList;
    },
    // 获取用户答题详情列表
    getUserList(item) {
      let params = {
        periodId: item.id,
        userId: this.routeInfo.userId,
      };
      this.$api.userAnswerInfo(params).then((res) => {
        if (res.code == 200) {
          if (res.data.length) {
            res.data.forEach((i) => {
              i.isExpand = false;
              i.options = JSON.parse(i.options);
              this.scoreSum += i.score || 0;
              if(i.type=='2'){
                i.userAnswer = i.userAnswer.split(',')
              }
            });
            this.exercisesList = res.data;
          }
        }
      });
    },
    closeDrawer() {
      this.$emit("closeDrawer", false);
    },
    submit() {},
    isExpandBtn(item) {
      item.isExpand = !item.isExpand;
    },
    // 获取答案
    getAnswer(item) {
      if (item.type == "3") {
        return item.answer == "1" ? "正确" : "错误";
      } else {
        return item.answer;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}
.coursrDrawer {
  color: #333333;
  font-weight: 500;
  font-size: 18px;
  span {
    font-size: 14px;
    color: #3562db;
  }
}
::v-deep .el-drawer__header {
  height: 56px;
  line-height: 56px !important;
  margin-bottom: 0 !important;
  border-bottom: 1px solid #dcdfe6;
  box-shadow: -4px 0 4px 0 rgba(203, 205, 220, 0.24);
}
.drawer_conter {
  padding: 24px;
  height: calc(100% - 70px);
  .content {
    height: 100%;
    .table {
      height: calc(100% - 24px);
      overflow: auto;
      padding-right: 16px;
      font-size: 14px;
      .exercisesItem {
        height: 70px;
        overflow: hidden;
        background-color: #fff;
        margin-bottom: 26px;
        position: relative;
        .exercisesTop {
          display: flex;
          justify-content: space-between;
          margin-bottom: 10px;
          .left {
            display: flex;
            align-items: center;
            justify-content: center;
            span {
              color: #7f848c;
            }
          }
          .right {
            color: #ccced3;
            display: flex;
            align-items: center;
            .line {
              width: 2px;
              height: 14px;
              margin: 0 10px 0 26px;
              background-color: #dcdfe6;
            }
            span {
              color: #3562db;
              margin-left: 16px;
              cursor: pointer;
            }
            i {
              color: #3562db;
              cursor: pointer;
            }
          }
          .exercisesType {
            width: 58px;
            height: 22px;
            line-height: 22px;
            text-align: center;
            border-radius: 4px;
            color: #86909c;
          }
        }
        .exercisesName {
          line-height: 20px;
          margin-bottom: 16px;
          word-break: break-all;
        }
        .title {
          overflow: hidden;
          -webkit-line-clamp: 2;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
        }
        .el-radio {
          margin-left: 38px;
          font-size: 14px;
          color: #7f848c !important;
          line-height: 18px;
          display: flex;
        }
        p {
          font-size: 14px;
          color: #7f848c !important;
          line-height: 20px;
          margin-bottom: 16px;
        }
        .isCorrect {
          position: absolute;
          top: 80px;
          right: 20px;
        }
      }
      .expand {
        height: auto;
      }
    }
  }
}
.drawer_footer {
  padding: 16px;
  display: flex;
  justify-content: right;
}
::v-deep .el-radio {
  display: block;
  margin: 10px 0;
  .el-radio__label {
    white-space: normal;
  }
}
::v-deep .el-checkbox {
  display: block;
  margin: 10px 0;
}
::v-deep .el-checkbox-group {
  margin-left: 38px;
}
</style>
