<template>
  <div style="width: 100%; height: 100%; padding: 10px" class="drag_class">
    <div class="card_box_title card_box_long_bg">报警记录</div>
    <div v-if="!tableData.length" class="echart-null">
      <img src="@/assets/images/null.png" alt="" />
      <div>暂无数据~</div>
    </div>
    <div v-else style="width: 100%; height: 100%; margin-top: 10px">
      <!-- <div class="pie-decoration"></div> -->
      <el-table :data="tableData" row-class-name="row-bg-color" style="width: 100%" height="calc(100% - 24px)">
        <el-table-column prop="alarmDeviceName" label="设备名称" show-overflow-tooltip> </el-table-column>
        <el-table-column prop="alarmType" label="报警类型" show-overflow-tooltip> </el-table-column>
        <el-table-column prop="alarmDetails" label="报警描述" show-overflow-tooltip> </el-table-column>
        <el-table-column prop="alarmValue" label="报警数值" show-overflow-tooltip> </el-table-column>
        <el-table-column prop="alarmStartTime" label="报警时间" show-overflow-tooltip> </el-table-column>
        <el-table-column prop="alarmStatus" label="处置状态" width="100">
          <div slot-scope="scope" class="alarmStatus" :style="{ color: scope.row.alarmStatus == 0 ? '#FA403C' : scope.row.alarmStatus == 1 ? '#fd9434' : '#67c23a' }">
            <span class="alarmStatusIcon" :style="{ background: scope.row.alarmStatus == 0 ? '#FA403C' : scope.row.alarmStatus == 1 ? '#ff9435' : '#67c23a' }"></span>
            {{ scope.row.alarmStatus == 0 ? '未处理' : scope.row.alarmStatus == 1 ? '处理中' : '已处理' }}
          </div>
        </el-table-column>
        <el-table-column prop="operation" label="操作" width="120">
          <div slot-scope="scope" class="operationBtn">
            <span style="color: #5188fc; cursor: pointer; margin-right: 10px" @click="handlerAlarm('check', scope.row)"> 详情 </span>
          </div>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script lang="jsx">
import { monitorTypeList } from '@/util/dict.js'
import monent from 'moment'
export default {
  props: {
    query: {
      type: Object,
      default: () => {
        return {
          type: 1
        }
      }
    }
  },
  data() {
    return {
      requestHttp: __PATH.VUE_IEMC_ELEVATOR_API,
      projectCode: monitorTypeList.find((item) => item.projectName == '电梯监测').projectCode, // 电梯code
      tableLoading: false,
      pageData: {
        page: 1,
        size: 200,
        total: 0
      },
      tableData: [],
      timer: null
    }
  },
  watch: {
    query: {
      handler(val) {
        this.getAlarmRecord()
      },
      deep: true
    }
  },
  mounted() {
    this.getAlarmRecord()
    this.scheduledTasks()
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
  },
  methods: {
    scheduledTasks() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
      this.timer = setInterval(() => {
        this.getAlarmRecord()
      }, 30000)
    },
    handlerTime(type) {
      let startTime = ''
      let endTime = ''
      if (type === 1) {
        startTime = monent().date(1).format('YYYY-MM-DD')
        endTime = monent().format('YYYY-MM-DD')
      } else if (type === 2) {
        startTime = monent().week(1).format('YYYY-MM-DD')
        endTime = monent().format('YYYY-MM-DD')
      } else {
        startTime = monent().format('YYYY-MM-DD')
        endTime = ''
      }
      return {
        startTime,
        endTime
      }
    },
    getAlarmRecord() {
      let startTime = this.handlerTime(this.query.type).startTime
      let endTime = this.handlerTime(this.query.type).endTime
      this.$api
        .GetAllAlarmRecord(
          {
            projectCode: this.projectCode,
            pageNo: this.pageData.page,
            pageSize: this.pageData.size,
            startTime: startTime,
            endTime: endTime,
            ...this.query
          },
          this.requestHttp
        )
        .then((res) => {
          this.tableLoading = false
          if (res.code == 200) {
            this.tableData = res.data.records
          }
        })
    },
    handlerAlarm(type, row) {
      this.$router.push({
        path: '/' + monitorTypeList.find((item) => item.projectCode == this.projectCode).policeDetailsPath,
        query: {
          alarmId: row.alarmId
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.card_box_long_bg {
  background: url('~@/assets/images/elevator/module-bg.png') no-repeat;
  background-size: 100% 100%;
}
.card_box_title {
  height: 31px;
  width: 100%;
  line-height: 31px;
  padding-left: 10px;
  font-size: 15px;
  font-family: Alibaba PuHuiTi-Medium, Alibaba PuHuiTi;
  font-weight: 500;
  color: #b7cfff;
}
.echart-null {
  margin: 0 auto;
  height: calc(100% - 31px);
  width: 50%;
  text-align: center;
  color: #8a8c8f;
  img {
    max-width: 100%;
    max-height: calc(100% - 20px);
  }
  div {
    font-size: 14px;
  }
}
.el-table {
  background: none;
  color: #fff;
  ::-webkit-scrollbar-track:hover {
    background-color: transparent;
  }
}
::v-deep .el-table::before {
  background-color: transparent;
}
::v-deep .el-table tr {
  // background: none;
  background: rgba(255, 255, 255, 0.04);
}
::v-deep .el-table .el-table__header .el-table__cell {
  background: none;
  color: #fff;
  border-bottom: 5px solid #08152c;
}
::v-deep .row-bg-color {
  background: rgba(255, 255, 255, 0.04);
  .el-table__cell {
    border-bottom: 5px solid #08152c;
    padding: 8px 0;
  }
}
</style>
