<template>
  <PageContainer>
    <div slot="content">
      <div class="title">手动录入</div>
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px">
        <div v-for="(item, index) in formData.list" :key="index" style="margin: 18px 0;">
          <el-form-item label="所属科室" :prop="'list.' + index + '.officeId'" :rules="rules.officeId">
            <el-cascader
              v-model="item.officeId"
              :options="deptOptions"
              :props="{ expandTrigger: 'hover', checkStrictly: true, emitPath: false, label: 'deptName', value: 'id' }"
              placeholder="所属科室"
              clearable
              filterable
              @change="officeChanged($event, item)"
            ></el-cascader>
          </el-form-item>
          <el-form-item label="医废类型" :prop="'list.' + index + '.wasteCode'">
            <el-select v-model="item.wasteCode" placeholder="医废类型" style="margin-left: 16px;" @change="typeChanged($event, item)">
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="数量" :prop="'list.' + index + '.gatherCount'" :rules="rules.gatherCount">
            <el-input
              v-model="item.gatherCount"
              style="width: 150px;"
              type="number"
              @input="formData.list[index].gatherCount = formData.list[index].gatherCount.replace(/^(0+)|[^\d]+/g, '').slice(0, 3)"
            >
              <span slot="suffix">袋</span>
            </el-input>
          </el-form-item>
          <el-form-item label="重量" :prop="'list.' + index + '.gatherWeigh'" :rules="rules.gatherWeigh">
            <el-input
              v-model="item.gatherWeigh"
              style="width: 150px;"
              @input="
                formData.list[index].gatherWeigh =
                  formData.list[index].gatherWeigh.match(/^\d{1,4}(?:\.\d{0,2})?/) === null ? '' : formData.list[index].gatherWeigh.match(/^\d{1,4}(?:\.\d{0,2})?/)[0]
              "
            >
              <span slot="suffix">kg</span>
            </el-input>
          </el-form-item>
          <el-button type="primary" style="margin-left: 12px;" @click="addRows">增加</el-button>
          <el-button v-if="index != 0" type="danger" style="padding: 8px 22px;" @click="delRows(index)">删除</el-button>
        </div>
        <div style="margin: 15px 0;">
          <el-form-item label="收集时间" prop="gatherTime" :rules="rules.gatherTime">
            <el-date-picker v-model="formData.gatherTime" type="datetime" value-format="yyyy-MM-dd HH:mm:ss"> </el-date-picker>
          </el-form-item>
        </div>
        <div style="margin: 15px 0;">
          <el-form-item label="收集人员" prop="gatherPersonCode" :rules="rules.gatherPersonCode">
            <el-select v-model="formData.gatherPersonCode" placeholder="请选择人员" @change="personChanged">
              <el-option v-for="item in personList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
            </el-select>
          </el-form-item>
        </div>
      </el-form>
      <div class="footer">
        <el-button type="primary" @click="handleSubmit">保存</el-button>
        <el-button type="primary" plain @click="goBack">取消</el-button>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import store from '@/store/index'
import { transData } from '@/util'
export default {
  name: 'manualEntry',
  data() {
    return {
      formData: {
        list: [
          {
            officeId: '',
            officeName: '',
            wasteCode: '3',
            wasteType: '感染类',
            gatherCount: '1',
            gatherWeigh: '1'
          }
        ],
        gatherTime: '',
        gatherPerson: '',
        gatherPersonCode: ''
      },
      rules: {
        officeId: { required: true, message: '所属科室不能为空', trigger: 'change' },
        gatherCount: { required: true, message: '数量不能为空或非法数字', trigger: 'change' },
        gatherWeigh: { required: true, message: '重量不能为空或非法数字', trigger: 'change' },
        gatherTime: { required: true, message: '收集时间不能为空', trigger: 'change' },
        gatherPersonCode: { required: true, message: '收集人员不能为空', trigger: 'change' }
      },
      deptOptions: [],
      options: [
        {
          value: '0',
          label: '损伤类'
        },
        {
          value: '1',
          label: '病理类'
        },
        {
          value: '2',
          label: '化学类'
        },
        {
          value: '3',
          label: '感染类'
        },
        {
          value: '4',
          label: '药物类'
        },
        {
          value: '5',
          label: '涉疫类'
        },
        {
          value: '6',
          label: '其他'
        }
      ],
      deptList: [],
      personList: []
    }
  },
  mounted() {
    this.getDeptList()
    this.getPersonList()
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    checkPriceOut(idx) {
      let checkPlan = '' + this.formData.list[idx].gatherWeigh
      if (checkPlan.indexOf('-') != -1 || checkPlan.indexOf('+') != -1 || checkPlan.indexOf('=') != -1) {
        checkPlan = checkPlan.replace('-', '').replace('+', '')
        this.formData.list[idx].gatherWeigh = 1
        return
      }
      var re = /([0-9]+.[0-9]{2})[0-9]*/
      checkPlan = checkPlan.replace(re, '$1')
      checkPlan = checkPlan >= 0 ? checkPlan : checkPlan * -1
      this.formData.list[idx].gatherWeigh = checkPlan
    },
    // 获取部门列表
    getDeptList() {
      this.$api.getSelectedDept({}).then((res) => {
        if (res.code == 200) {
          this.deptOptions = transData(res.data, 'id', 'pid', 'children')
          this.deptList = res.data
        }
      })
    },
    addRows() {
      this.formData.list.push({
        officeId: '',
        officeName: '',
        wasteCode: '3',
        wasteType: '感染类',
        gatherCount: '1',
        gatherWeigh: '1'
      })
    },
    delRows(index) {
      this.formData.list.splice(index, 1)
    },
    officeChanged(val, item) {
      this.deptList.forEach((i) => {
        if (i.id == val) {
          item.officeName = i.deptName
        }
      })
    },
    typeChanged(val, item) {
      this.options.forEach((i) => {
        if (i.value == val) {
          item.wasteType = i.label
        }
      })
    },
    personChanged(val) {
      this.personList.forEach((i) => {
        if (i.id == val) {
          this.formData.gatherPerson = i.name
        }
      })
    },
    getPersonList() {
      this.$api.selectStaffList().then((res) => {
        this.personList = res.data
      })
    },
    handleSubmit() {
      this.$refs['formRef'].validate((valid) => {
        if (valid) {
          this.$api.manualBatchSave(this.formData).then((res) => {
            if (res.code == '200') {
              this.$message.success('保存成功')
              this.$router.go(-1)
            }
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.container-content > div {
  background-color: #fff;
  height: 90vh;
  position: relative;
}

.footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  padding: 10px 20px;
  background-color: #fff;
  text-align: left;
  border-top: 1px solid #ebeef5;
}

.title {
  font-size: 20px;
  font-weight: 700;
  color: #303133;
  padding: 12px 20px 0;
  margin-bottom: 12px;
}

.el-form > div {
  display: flex;
  align-items: center;
}

.el-form-item {
  margin-bottom: 0;
}
</style>
