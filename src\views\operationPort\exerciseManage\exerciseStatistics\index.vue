<template>
  <PageContainer>
    <div slot="header">
      <div class="searchForm">
        <div class="search-box">
          <el-input v-model="searchForm.deptName" clearable filterable placeholder="请输入部门名称"></el-input>
          <el-select v-model="searchForm.generalId" placeholder="全部" class="ml-16">
            <el-option v-for="item in branchList" :key="item.id" :label="item.orgName" :value="item.id"> </el-option>
          </el-select>
          <el-date-picker v-model="time" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
            range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" class="ml-16">
          </el-date-picker>
          <el-select v-model="searchForm.drillType" placeholder="请选择演练类型" class="ml-16">
            <el-option v-for="item in typeList" :key="item.id" :label="item.labelName" :value="item.id"> </el-option>
          </el-select>
          <div class="ml-16">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button type="primary" plain @click="reset">重置</el-button>
          </div>
        </div>
      </div>
    </div>
    <div slot="content" ref="contentRef" class="table-box">
      <div class="tableContainer">
        <el-button type="primary" style="margin-bottom: 12px" :disabled="multipleSelection.length<1"
          @click="derive">导出</el-button>
        <el-table v-loading="tableLoading" height="calc(100% - 96px)" :data="tableData" border stripe
          table-layout='auto' class="tableAuto" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55">
          </el-table-column>
          <el-table-column label="部门名称" prop="deptName" show-overflow-tooltip></el-table-column>
          <el-table-column label="上级单位" prop="generalName" show-overflow-tooltip></el-table-column>
          <el-table-column label="完成演练总次数" prop="total" show-overflow-tooltip></el-table-column>
          <el-table-column label="演练类型" prop="drillTypeName" show-overflow-tooltip>
            <template slot-scope="scope">
              <div v-for="(item, index) in scope.row.taskList" :key="index" class="columnList">
                <span> {{ item.drillTypeName||"" }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="完成演练时间" prop="taskCompletionTime" show-overflow-tooltip>
            <template slot-scope="scope">
              <div v-for="(item, index) in scope.row.taskList" :key="index" class="columnList">
                <span>{{ item.taskCompletionTime? moment( item.taskCompletionTime).format('YYYY-MM-DD') :"--"}} </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="效果评定" prop="taskEffect">
            <template slot-scope="scope">
              <div v-for="(item, index) in scope.row.taskList" :key="index" class="columnList">
                <el-tooltip v-if="item.taskRemark" :content="getEffectText(JSON.parse(item.taskEffect))"
                  placement="top-start" effect="dark">
                  <span>
                    {{ divideRadioText[JSON.parse(item.taskEffect).divideRadio].text}}
                    {{ dutyRadioText[JSON.parse(item.taskEffect).dutyRadio].text}}
                    {{ effectRadioText[JSON.parse(item.taskEffect).effectRadio].text}}
                  </span>
                </el-tooltip>
                <span v-else>--</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="演练备注" prop="taskRemark">
            <template slot-scope="scope">
              <div v-for="(item, index) in scope.row.taskList" :key="index" class="columnList">
                <el-tooltip v-if="item.taskRemark" :content="getTaskRemarkText(JSON.parse(item.taskRemark))"
                  placement="top-start" effect="dark">
                  <span> <span
                      v-if="JSON.parse(item.taskRemark).remarkCheckList.length&&JSON.parse(item.taskRemark).remarkCheckList[0]==='0'">
                      有领导参加 </span>
                    <span
                      v-if="JSON.parse(item.taskRemark).remarkCheckList.length&&JSON.parse(item.taskRemark).remarkCheckList[0]==='1'">有安全事故</span>
                    <span
                      v-if="JSON.parse(item.taskRemark).remarkCheckList.length&&JSON.parse(item.taskRemark).remarkCheckList.length===2">
                      有领导参加,有安全事故</span>
                    <span> {{ item.taskRemark?JSON.parse(item.taskRemark).remark:'' }}</span></span>
                </el-tooltip>
                <span v-else>--</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label=" 操作">
            <template slot-scope="scope">
              <div v-for="(item, index) in scope.row.taskList" :key="index" class="columnList">
                <el-button type="text" @click="viewDetail(item)">查看</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination :current-page="pagination.page" :page-sizes="[30, 50, 100]" :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper" :total="pagination.total" @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
        </el-pagination>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import moment from 'moment'
export default {
  name: 'exerciseStatistics',
  data() {
    return {
      moment,
      tableLoading: false,
      tableData: [],
      searchForm: {
        deptName: '',
        drillType: '',
        generalId: ''
      },
      time: [],
      typeList: [],
      branchList: [],
      pagination: {
        pageSize: 30,
        page: 1,
        total: 0
      },
      multipleSelection: [],
      divideRadioText: {
        '0': {
          text: '分工明确'
        },
        '1': {
          text: '分工不明确'
        }
      },
      dutyRadioText: {
        '0': {
          text: '职责清晰'
        },
        '1': {
          text: '职责不清晰'
        }
      },
      effectRadioText: {
        '0': {
          text: '达到预期效果'
        },
        '1': {
          text: '基本达到预期效果'
        },
        '2': {
          text: '未达到预期效果'
        }
      }
    }
  },
  mounted() {
    this.getTypeList()
    this.getBranchData()
    this.getDataList()
  },
  methods: {
    // 获取上级部门
    getBranchData() {
      this.$api.getGeneralist({}).then((res) => {
        if (res.code == 200) {
          this.branchList = res.data
        } else if (res.message) {
          this.$message.error(res.message)
        }
      })
    },
    // 获取评定内容
    getEffectText(str) {
      if (str) {
        return this.divideRadioText[str.divideRadio].text + '   ' + this.dutyRadioText[str.dutyRadio].text + '  ' + this.effectRadioText[str.effectRadio].text
      } else {
        return '-'
      }
    },
    // 获取备注内容
    getTaskRemarkText(str) {
      if (str) {
        if (str.remarkCheckList[0] === '0') {
          return '有领导参加' + '   ' + str.remark + '   '
        } else if (str.remarkCheckList[0] === '1') {
          return '有安全事故' + '   ' + str.remark + '   '
        } else {
          return '有领导参加,有安全事故' + '   ' + str.remark + '   '
        }
      } else {
        return '-'
      }
    },
    // 列表数据
    getDataList() {
      this.tableLoading = true
      let data = {
        ...this.searchForm,
        startTime: this.time[0] ? moment(this.time[0]).format('YYYY-MM-DD') : '',
        endTime: this.time[1] ? moment(this.time[1]).format('YYYY-MM-DD') : '',
        page: {
          current: this.pagination.page,
          size: this.pagination.pageSize
        }
      }
      this.$api
        .getPreplanDrillStatisticaData(data)
        .then((res) => {
          this.tableLoading = false
          if (res.code == 200) {
            this.tableData = res.data ? res.data.list : []
            this.pagination.total = res.data ? res.data.totalCount : 0
          } else if (res.message) {
            this.tableData = []
            this.pagination.total = 0
            this.$message.error(res.message)
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    // 获取演练类型列表
    getTypeList() {
      let data = {
        page: {
          current: 1,
          size: 9999
        }
      }
      this.$api
        .getPreplanDrillTypeData(data)
        .then((res) => {
          this.typeList = res.data ? res.data.list : []
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.pagination.page = 1
      this.getDataList()
    },
    handleCurrentChange(val) {
      this.pagination.page = val
      this.getDataList()
    },
    derive() {
      let ids = []
      this.multipleSelection.forEach(item => {
        ids.push(item.deptId)
      })

      let menuList = this.$store.state.menu.routes
      let firstTitle = ''
      let routeList = []
      this.$router.currentRoute.matched.filter(item => item.name).forEach(el => {
        routeList.push(el.meta.title)
      })
      if (menuList.length) {
        firstTitle = menuList[this.$store.state.menu.headerActived].meta.title
        routeList.unshift(firstTitle)
      }

      this.$api.exportPreplanDrillStatisticaData({ ids: ids }, { 'operation-type': 4, 'operation-content': encodeURIComponent(routeList.join(','))}).then((res) => {
        const blob = new Blob([res], { type: 'application/vnd.ms-excel;charset=UTF-8' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = '演练统计.xlsx'
        link.click()
        window.URL.revokeObjectURL(url)
      })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    search() {
      this.pagination.page = 1
      this.getDataList()
    },
    reset() {
      this.searchForm = {
        deptName: '',
        drillType: '',
        generalId: ''
      }
      this.time = []
      this.pagination.page = 1
      this.pagination.total = 0
      this.getDataList()
    },
    viewDetail(row) {
      this.$router.push({
        path: '/exerciseManage/exerciseTask/exerciseTaskDetail',
        query: {
          taskId: row.taskId
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.searchForm {
  display: flex;
  height: 80px;
  padding: 0 16px;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .el-input {
    width: 200px;
    margin-left: 16px;
  }

  > div {
    margin-right: 20px;
  }
}

.search-box {
  display: flex;
  align-items: center;
}

.container-content > div {
  height: 100%;
  margin-top: 16px;
}
.el-table {
  margin-bottom: 12px;
}

::v-deep .el-table__row:hover {
  cursor: pointer;
}
.table-box {
  background-color: #fff;
  height: 100%;
  padding: 16px;
  .tableContainer {
    height: 100%;
    .el-table {
      height: calc(100% - 96px) !important;
    }
  }
}
.ml-16 {
  margin-left: 16px;
}
.columnList {
  font-style: 12px;
  color: #7f848c;
  width: 90%;
  height: 35px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
