<template>
  <PageContainer v-loading="uploading" :footer="true">
    <div slot="content" class="space-content" style="height: 100%">
      <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
        <span class="green_line"></span>
        基本信息
      </div>
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="文件名称" prop="fileName">
              <el-input v-if="activeType != 'detail'" v-model="ruleForm.fileName" :maxlength="50" show-word-limit placeholder="请输入文件名称"></el-input>
              <span v-else>{{ ruleForm.fileName }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="14">
            <el-form-item label="说明" prop="reason">
              <el-input v-if="activeType != 'detail'" v-model="ruleForm.reason" :maxlength="200" :rows="3" show-word-limit type="textarea" placeholder="说明"></el-input>
              <span v-else>{{ ruleForm.reason }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="上传附件" required>
              <el-upload
                v-if="activeType != 'detail'"
                action=""
                class="upload-demo"
                :on-change="handleChange"
                :before-upload="beforeAvatarUpload"
                :http-request="httpRequset"
                :limit="1"
                :on-exceed="handleExceed"
                accept=".rar,.zip,.doc,.docx,.pdf,.jpg,.jpeg,.png"
                :file-list="fileList"
              >
                <el-button icon="el-icon-upload2">上传文件</el-button>
                <div slot="tip" class="el-upload__tip">可上传单个文件，小于20M，支持扩展名：.rar .zip .doc .docx .pdf .jpg .jpeg .png</div>
              </el-upload>
              <div v-else class="fileGroup">
                <div v-for="(item, index) in fileUrl" :key="index" class="fileItem">
                  <div class="fileItemName">
                    <span class="itemName">{{ item.name }}</span>
                    <el-link type="primary" @click="downloadFile(item)">下载</el-link>
                  </div>
                  <div style="margin: 0 20px">大小：{{ fileDetail.fileSize }}MB</div>
                  <div>格式：{{ fileDetail.fileFormat }}</div>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">取消</el-button>
      <el-button v-if="activeType != 'detail'" :disabled="uploading" type="primary" @click="submitForm">保存</el-button>
    </div>
  </PageContainer>
</template>
<script>
import axios from 'axios'
export default {
  components: {},
  data() {
    return {
      id: '',
      activeType: 'add',
      ruleForm: {
        fileName: '',
        reason: ''
      },
      rules: {
        fileName: [
          { required: true, message: '请输入文件名称', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符之间', trigger: 'blur' }
        ]
      },
      fileList: [],
      uploading: false,
      fileUrl: [],
      fileDetail: {}
    }
  },
  created() {
    this.activeType = this.$route.query.type || 'add'
    this.id = this.$route.query.id || ''
    if (this.id) {
      this.getFileDetail()
    }
  },
  methods: {
    submitForm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (!this.fileUrl.length) {
            return this.$message.error('请上传文件')
          }
          const params = {
            fileName: this.ruleForm.fileName,
            reason: this.ruleForm.reason,
            fileFormat: this.fileList[0].name.split('.').pop(),
            fileSize: this.fileList[0].size ? (this.fileList[0].size / 1024 / 1024).toFixed(2) : this.fileDetail.fileSize,
            fileUrl: JSON.stringify(this.fileUrl)
          }
          if (this.activeType == 'edit') {
            params.id = this.id
          }
          this.uploading = true
          this.$api.rentalHousingApi.fileSaveUpdata(params).then((res) => {
            if (res.code == '200') {
              this.$message({
                showClose: true,
                message: `${this.activeType == 'add' ? '添加' : '编辑'}文件成功！`,
                type: 'success',
                duration: 2000,
                onClose: () => {
                  this.$router.go(-1)
                }
              })
            }
          })
        }
      })
    },
    handleChange(file, fileList) {
      this.fileList = fileList
      if (!fileList.length) {
        this.fileUrl = []
      }
    },
    handleExceed() {
      this.$message.warning('当前限制选择 1 个文件，如需更改请删除原文件后再上传')
    },
    beforeAvatarUpload(file) {
      const fileSize = file.size / 1024 / 1024 < 20
      if (!fileSize) {
        this.$message.warning('上传图片大小不能超过 20MB!')
      }
      return fileSize
    },
    httpRequset() {
      const formData = new FormData()
      this.fileList.forEach((item) => {
        formData.append('file', item.raw)
      })
      this.uploading = true
      axios
        .post(__PATH.VUE_SYS_API + 'SysMenu/upload', formData, {
          headers: {
            Authorization: 'Bearer ' + this.$store.state.user.token
          }
        })
        .then((res) => {
          if (res.data.code == 200) {
            const fileObj = {
              name: this.fileList[0].name,
              url: res.data.data
            }
            this.fileUrl = [fileObj]
          } else {
            this.fileList = []
            this.$message.error(res.data.msg || '上传失败')
          }
        })
        .finally(() => {
          this.uploading = false
        })
    },
    getFileDetail() {
      this.$api.rentalHousingApi.fileDeatil({ id: this.id }).then((res) => {
        if (res.code === '200') {
          this.fileDetail = res.data
          this.ruleForm.fileName = res.data.fileName
          this.ruleForm.reason = res.data.reason
          this.fileUrl = JSON.parse(res.data.fileUrl)
          this.fileList = [
            {
              name: this.fileUrl[0].name,
              url: this.$tools.imgUrlTranslation(this.fileUrl[0].url)
            }
          ]
        }
      })
    },
    async downloadFile(item) {
      const response = await fetch(this.$tools.imgUrlTranslation(item.url))
      if (!response.ok) {
        this.$message.error('请求下载文件地址失败')
      }
      const blob = await response.blob()
      const urlBlob = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.style.display = 'none'
      a.href = urlBlob
      a.download = item.name
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      window.URL.revokeObjectURL(urlBlob)
    }
  }
}
</script>
<style lang="scss" scoped>
.space-content {
  height: calc(100% - 30px);
  overflow: auto;
  padding: 20px 40px;
  background: #fff;
  border-radius: 0px 4px 4px 0px;
  .fileGroup {
    width: 100%;
    .fileItem {
      display: flex;
      width: 100%;
      .fileItemName {
        color: #409eff;
        .itemName {
          margin-right: 10px;
        }
      }
    }
  }
}
</style>
