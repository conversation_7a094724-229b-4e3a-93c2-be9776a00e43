<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        border
        height="calc(100% - 50px)"
        style="width: 100%;"
        :cell-style="{ padding: '8px' }"
        stripe
        :empty-text="emptyText"
        highlight-current-row
        :header-cell-style="{ background: '#f2f4fbd1' }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column label="序号" type="index" width="80" align="center">
          <template slot-scope="scope">
            <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="taskName" show-overflow-tooltip label="任务名称"></el-table-column>
        <el-table-column align="center" prop="planTypeName" show-overflow-tooltip label="计划类型"></el-table-column>
        <el-table-column align="center" prop="planName" show-overflow-tooltip label="计划名称"></el-table-column>
        <el-table-column align="center" prop show-overflow-tooltip label="周期类型" width="80">
          <template slot-scope="scope">
            <span>{{ cycleTypeFn(scope.row) }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop show-overflow-tooltip label="应巡日期">
          <template slot-scope="scope">
            <span>{{ scope.row.taskStartTime + '-' + scope.row.taskEndTime }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" show-overflow-tooltip label="巡检小组/人员">
          <template slot-scope="scope">
            <span>{{ scope.row.planPersonName || filterDepartment(scope.row) }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="totalCount" show-overflow-tooltip label="应巡点数" width="80"></el-table-column>
        <el-table-column align="center" prop="hasCount" show-overflow-tooltip label="实巡点数" width="80"></el-table-column>
        <el-table-column align="center" prop="anomalyCount" show-overflow-tooltip label="异常点数" width="80"></el-table-column>
        <el-table-column align="center" show-overflow-tooltip label="完成状态">
          <template slot-scope="scope">
            <span
              class="table_using"
              :class="{
                font_color: scope.row.taskStatus == '1'
              }"
              >{{ scope.row.taskStatus == '1' ? '未完成' : '已完成' }}</span
            >
          </template>
        </el-table-column>
        <el-table-column align="center" prop="upDepartName" show-overflow-tooltip label="操作" width="80">
          <template slot-scope="scope">
            <el-link type="primary" :underline="false" @click="viewDetails(scope.row)">详情</el-link>
          </template>
        </el-table-column>
      </el-table>
      <div class="" style="padding-top: 10px; text-align: right;">
        <!-- class="table-page pagination" -->
        <el-pagination
          :current-page="paginationData.currentPage"
          :page-sizes="[15, 30, 50, 100]"
          :page-size="paginationData.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="paginationData.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>

    <div slot="footer">
      <el-button type="primary" plain @click="goAnalysis">取消</el-button>
    </div>
  </PageContainer>
</template>

<script>
import moment from 'moment'
import axios from 'axios'
export default {
  name: 'taskDataStatistics',
  data() {
    return {
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      LOGINDATA: '',
      statusList: [
        {
          id: '2',
          label: '已完成'
        },
        {
          id: '1',
          label: '未完成'
        },
        {
          id: '0',
          label: '全部'
        }
      ],
      searchDataObj: {
        planName: '',
        planPersonName: '',
        departmentName: '',
        cycleType: '',
        status: '0',
        timeLine: [],
        startTime: '',
        endTime: '',
        skipDate: ''
      },
      tableData: [],
      dictionaryArr: [], // 计划类型
      emptyText: '暂无数据',
      tableLoading: false,
      pageSize: '',
      currentPage: '',
      listRole: '',
      userIds: '',
      activeType: [],
      tableClickArry: [],
      dialogVisible: false,
      searchFrom: {},
      cycleTypeArr: [
        {
          cycleType: 8,
          label: '单次'
        },
        {
          cycleType: 6,
          label: '每日'
        },
        {
          cycleType: 0,
          label: '每周'
        },
        {
          cycleType: 2,
          label: '每月'
        },
        {
          cycleType: 3,
          label: '季度'
        },
        {
          cycleType: 5,
          label: '全年'
        }
      ],
      from: ''
    }
  },
  created() {
    // 获取登录信息
    this.LOGINDATA = JSON.parse(sessionStorage.getItem('LOGINDATA'))
    if (this.$route.query.from && this.$route.query.from == 'taskAnlysis') {
      this.from = this.$route.query.from
      this.searchDataObj.departmentName = this.$route.query.TeamName
      this.searchDataObj.cycleType = JSON.parse(this.$route.query.filters).cycleType
      this.searchDataObj.skipDate = JSON.parse(this.$route.query.filters).skipDate
      this.searchDataObj.status = this.$route.query.status
      if (JSON.parse(this.$route.query.filters).timeIterval.length > 0) {
        const timeIterval = [JSON.parse(this.$route.query.filters).timeIterval[0] + ' 00:00:00', JSON.parse(this.$route.query.filters).timeIterval[1] + ' 23:59:59']
        this.searchDataObj.timeLine = timeIterval
      }
    }
  },
  mounted() {
    // 获取任务管理列表
    this._findTaskList()
  },
  methods: {
    // 查询表格
    _findTaskList() {
      this.tableLoading = true
      let obj = this.LOGINDATA
      const { paginationData, searchDataObj } = this
      const taskStartTime = searchDataObj.timeLine.length > 0 ? moment(searchDataObj.timeLine[0]).format('YYYY-MM-DD HH:mm:ss') : ''
      const taskEndTime = searchDataObj.timeLine.length > 0 ? moment(searchDataObj.timeLine[1]).format('YYYY-MM-DD HH:mm:ss') : ''
      let data = {
        planTypeId: '',
        planName: searchDataObj.planName,
        planPersonName: searchDataObj.planPersonName,
        taskStatus: searchDataObj.status == '0' ? '' : searchDataObj.status, // 状态
        cycleTypes: searchDataObj.cycleType.join(','),
        pageNo: paginationData.currentPage,
        pageSize: paginationData.pageSize,
        departmentName: searchDataObj.departmentName,
        taskStartTime,
        taskEndTime,
        exceptWeekend: searchDataObj.skipDate
      }
      // this.$http.ipsmFindTaskList(data).then(res => {
      this.$api.findPlanSchedule(data).then((res) => {
        this.tableLoading = false
        if (res.code == 200) {
          this.tableData = res.data.list
          this.paginationData.total = parseInt(res.data.sum)
          if (res.data.list.length == 0) {
            this.emptyText = '未查到任何记录，请放大查询条件试试！'
          }
        }
      })
    },
    // 查看详情
    viewDetails(row) {
      this.$router.push({
        name: 'scheduleDetailsStatistics',
        query: {
          id: row.id,
          status: row.taskStatus,
          startTime: row.executeStartTime || '',
          endTime: row.executeEndTime || '',
          taskName: row.taskName,
          cycleType: row.cycleType,
          executeStartTime: row.executeStartTime || '',
          executeEndTime: row.executeEndTime,
          distributionTeamName: this.filterDepartment(row),
          cycleRole: row.cycleRole,
          planPersonName: row.planPersonName
        }
      })
    },
    /**
     * 点击table表格
     */
    handleSelectionChange(val) {
      this.tableClickArry = val
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this._findTaskList(this.activeType)
    },
    // 每页的条数
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.paginationData.currentPage = 1
      this._findTaskList(this.activeType)
    },
    // 周期类型
    cycleTypeFn(row) {
      const item = this.cycleTypeArr.filter((i) => i.cycleType == row.cycleType)
      return item[0].label
    },
    // 科室去重
    filterDepartment(department) {
      const nameArr = department.distributionTeamName.split(',')
      const teamName = Array.from(new Set(nameArr))
      return teamName.toString()
    },
    // 返回数据统计
    goAnalysis() {
      this.$router.push({
        name: 'dataStatisticsList',
        query: {
          from: 'task'
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
  padding: 10px;
  overflow-y: auto;
}
</style>
