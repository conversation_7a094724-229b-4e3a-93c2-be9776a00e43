<template>
  <PageContainer>
    <div slot="content" class="role-content" style="height: 100%">
      <div class="role-content-left">
        <div class="toptip">
          <span class="green_line"></span>
          风险四色图管理
        </div>
        <div class="left_content">
          <el-input v-model.trim="filterText" clearable placeholder="输入关键字进行过滤"></el-input>
          <div class="tree">
            <el-tree
              ref="tree"
              v-loading="treeLoading"
              :check-strictly="true"
              :data="treeData"
              :props="defaultProps"
              node-key="id"
              :highlight-current="true"
              :filter-node-method="filterNode"
              :default-expanded-keys="expanded"
              @node-click="handleNodeClick"
            ></el-tree>
          </div>
        </div>
      </div>
      <div class="role-content-right">
        <div style="height: 100%">
          <div class="contentTable">
            <div class="contentTable-main table-content">
              <el-table ref="multipleTable" v-loading="tableLoading" highlight-current-row :data="tableData" border stripe style="width: 100%" :height="tableHeight">
                <el-table-column type="index" label="序号" width="100" align="center"></el-table-column>
                <el-table-column prop="gridName" label="网格名称" align="center"></el-table-column>
                <el-table-column prop="picture" label="四色图" align="center">
                  <template v-if="scope.row.picture" slot-scope="scope">
                    <el-image style="height: 60px" :src="scope.row.picture" fit="scale-down" :preview-src-list="[scope.row.picture]"></el-image>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center">
                  <template slot-scope="scope">
                    <el-link type="primary" @click="editImg(scope.row)">编辑</el-link>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </div>
      <el-dialog
        v-dialogDrag
        v-loading="saveLoading"
        custom-class="model-dialog"
        title="图片编辑"
        :visible.sync="dialogVisibleRole"
        width="40%"
        :before-close="empty"
        append-to-body
        element-loading-text="上传中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.8)"
      >
        <div style="width: 100%; background-color: #fff; padding: 10px">
          <el-upload
            ref="uploadFile"
            drag
            class="mterial_file"
            action="string"
            list-type="picture-card"
            :file-list="fileEcho"
            :http-request="httpRequest"
            accept=".jpg, .jpeg, .png, .JPG, .JPEG, .bmp"
            :limit="1"
            :on-exceed="handleExceed"
            :on-preview="handlePictureCardPreview"
            :beforeUpload="beforeAvatarUpload"
            :on-remove="handleRemove"
            :on-change="fileChange"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过10MB</div>
          </el-upload>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="empty">取 消</el-button>
          <el-button type="primary" @click="submit">确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </PageContainer>
</template>

<script>
import tableListMixin from '@/mixins/tableListMixin.js'
import axios from 'axios'
import { transData } from '@/util'
export default {
  name: 'RiskFourColorChartMangement',
  components: {},
  mixins: [tableListMixin],
  data() {
    return {
      tableLoading: false,
      defaultProps: {
        children: 'children',
        label: 'ssmName',
        value: 'id'
      },
      treeData: [],
      tableData: [],
      filterText: '',
      treeLoading: false,
      expanded: [],
      riskType: '',
      checkedData: [],
      pictureUrl: '', // 图片url
      srcList: [], // 放大查看的url
      allData: [],
      disabled: false,
      saveLoading: false,
      dialogVisibleRole: false,
      isviwe: false,
      fileEcho: [], // 上传列表
      gridId: '',
      gridLevel: '',
      prentId: '',
      imgUrl: '',
      dialogImageUrl: ''
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    this.getDictValue()
  },
  mounted() {},
  methods: {
    getDictValue() {
      this.treeLoading = true
      // 获取风险位置
      this.$api.ipsmGetSpaceGridTree({ spaceFlag: 'sysConfig' }).then((res) => {
        this.treeLoading = false
        let treeList = transData(res.data, 'id', 'pid', 'children')
        this.treeData = treeList
        this.allData = res.data
        // this.getRiskTable(treeList[0])
        this.expanded = [this.treeData[0].id]
        // this.riskType = [this.treeData[0].gridName];
        this.checkedData = this.treeData[0]
        this.$nextTick(() => {
          this.$refs.tree.setCurrentKey(this.checkedData)
        })
        this.handleNodeClick(this.checkedData)
      })
    },
    filterNode(value, data) {
      if (!value) return true
      return data.ssmName.indexOf(value) !== -1
    },
    // 树状图点击
    handleNodeClick(data) {
      this.riskType = data.gridName
      this.tableCode = Number(data.gridName)
      this.checkedData = data
      this.$refs.tree.setCurrentKey(this.checkedData)
      this.pictureUrl = data.pictureUrl
      this.srcList.push(data.pictureUrl)
      this.getRiskTable(data)
    },
    // 获取右侧table
    getRiskTable(data) {
      const params = {
        gridId: data.id,
        gridLevel: data.ssmType
      }
      this.tableLoading = true
      this.$api.ipsmGetRiskFourColorChartMangement(params).then((res) => {
        if (res.code == '200') {
          this.tableData = res.data
          this.tableData.forEach((item) => {
            item.picture = this.$tools.imgUrlTranslation(item.picture)
          })
        }
        this.tableLoading = false
      })
    },
    // 编辑图片
    editImg(scope) {
      // console.log(scope)
      if (scope.picture && scope.pictureUrl) {
        this.fileEcho = [{ name: 'a.jpg', url: scope.picture }]
        this.imgUrl = scope.pictureUrl
      }
      this.disabled = true
      this.dialogVisibleRole = true
      this.gridId = scope.id
      this.prentId = scope.parentId
      this.gridLevel = scope.gridLevel
    },
    fileChange(file, fileList) {
      this.fileEcho = fileList
    },
    // 删除图片
    handleRemove(file, fileList) {
      this.fileEcho = fileList
      this.imgUrl = ''
    },
    // 弹框关闭
    empty() {
      this.dialogVisibleRole = false
      this.fileEcho = []
      this.imgUrl = ''
      this.gridId = ''
      this.gridLevel = ''
    },
    // 提交
    submit() {
      if (!this.imgUrl) {
        return this.$message.error('请先上传图片')
      }
      const params = {
        gridId: this.gridId,
        pictureUrl: this.imgUrl
      }
      this.$api.ipsmImageUpload(params).then((res) => {
        if (res.code == '200') {
          this.$message.success('保存成功！')
          const data = {
            id: this.prentId == '#' ? this.gridId : this.prentId,
            ssmType: this.prentId == '#' ? '1' : this.gridLevel - 1
          }
          this.getRiskTable(data)
        } else {
          this.$message.error('保存失败！')
        }
      })
      this.empty()
    },
    // 超过上传限制
    handleExceed() {
      this.$message.error('最多上传一份文件')
    },
    // 上传
    httpRequest() {
      this.saveLoading = true
      const formData = new FormData()
      this.fileEcho.forEach((item) => {
        formData.append('file', item.raw)
      })
      let loginData = JSON.parse(sessionStorage.getItem('LOGINDATA'))
      formData.append('hospitalCode', loginData.hospitalCode)
      axios({
        method: 'post',
        url: __PATH.VUE_AQ_URL + 'file/upload',
        data: formData,
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      }).then((res) => {
        if (res.data.code == 200) {
          this.imgUrl = res.data.data.fileKey
        } else {
          this.$message.error(res.data.message)
        }
        this.saveLoading = false
      })
    },
    // 点击已上传文件
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.isviwe = true
    },
    // 上传文件前
    beforeAvatarUpload(data) {
      const isLt5M = data.size / 1024 / 1024 < 10
      if (!isLt5M) {
        this.$message.error('上传图片大小不能超过 10MB!')
        return false
      }
      if (data.name.indexOf(',') != -1) {
        this.$message.error('非法的文件名')
        return false
      }
    }
  }
}
</script>
<style lang="scss" scoped>
// el-tree-node is-expanded is-focusable
::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: linear-gradient(to right, #d9e1f8, #fff);
}

::v-deep .el-tree-node__content {
  height: 32px;
}

.role-content {
  height: 100%;
  display: flex;

  .role-content-left {
    width: 246px;
    height: 100%;
    padding: 10px;
    background: #fff;
    border-radius: 4px;
    margin-right: 12px;

    .left_content {
      height: calc(100% - 60px);
      padding: 10px;
      // overflow: auto;
      .tree {
        margin-top: 20px;
        height: calc(100% - 40px);
        overflow: auto;
      }
    }
  }

  .role-content-right {
    flex: 1;
    height: 100%;
    min-width: 0;
    padding: 10px;
    background: #fff;
    border-radius: 4px;

    .search-from {
      padding-bottom: 12px;

      & > div {
        margin-right: 10px;
      }

      & > button {
        margin-top: 12px;
      }
    }

    .contentTable {
      height: 100%;
      display: flex;
      flex-direction: column;

      .contentTable-main {
        flex: 1;
        overflow: auto;
      }

      .contentTable-footer {
        padding: 10px 0 0;
      }
    }
  }
}

::v-deep .mterial_file {
  position: relative;
}

::v-deep .mterial_file > .el-upload-list {
  position: absolute;
  top: 0;
  // width: 100%;
  margin-left: 430px;
  max-height: 160px;
  // overflow: auto;
}

::v-deep .mterial_file > .el-upload--picture-card {
  border: none;
  width: 300px;
}

::v-deep .mterial_file .el-upload .el-upload-dragger {
  width: 300px;
  height: 148px;
}

::v-deep .mterial_file .el-upload__text {
  position: absolute;
  left: 57px;
  color: #606266;
  font-size: 14px;
  text-align: center;
}

::v-deep .el-upload-list__item {
  transition: none !important;
}
</style>
