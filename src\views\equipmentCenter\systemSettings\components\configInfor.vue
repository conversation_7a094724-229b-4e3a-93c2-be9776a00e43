<template>
  <div>
    <div class="configContent">
      <el-form ref="formInline" :model="formInline" :inline="true" class="form-inline" :rules="addRules" label-width="90px">
        <div class="generation-content">
          <div class="generationContent-title">设备到期时</div>
          <div class="generationContent-info">
            <div v-for="(item, tIndex) in generationContents" :key="tIndex" :gutter="20" class="content-block">
              <div style="display: flex">
                <div class="generationContent-input">
                  <el-form-item label="通知方式" prop="expireNotificationType">
                    <el-select v-model="item.expireNotificationType" multiple :disabled="activeType == 'detail'" placeholder="请选择" style="width: 240px">
                      <el-option v-for="item in informOptions" :key="item.value" :label="item.label" :value="item.value" :disabled="item.disabled"></el-option>
                    </el-select>
                  </el-form-item>
                </div>
                <div class="generationContent-input">
                  <el-form-item label="提醒时间" prop="reminderTime">
                    <el-time-picker
                      v-model="item.reminderTime"
                      value-format="HH:mm:ss"
                      format="HH:mm:ss"
                      :disabled="activeType == 'detail'"
                      :picker-options="{ selectableRange: '00:00:00 - 23:59:59' }"
                      placeholder="任意时间点"
                      style="width: 240px"
                    >
                    </el-time-picker>
                  </el-form-item>
                </div>
                <div class="generationContent-input">
                  <el-form-item label="信息接收人" prop="receiveScope">
                    <el-select
                      v-model="item.receiveScope"
                      placeholder="请选择信息接收人"
                      :disabled="activeType == 'detail'"
                      style="width: 240px"
                      @change="selectUser(item.receiveScope, tIndex)"
                    >
                      <el-option v-for="item in receptionList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                  </el-form-item>
                </div>
                <div v-if="activeType !== 'detail'" class="generationContent-operation">
                  <span v-if="item.receiveScope == 2" class="generationContent-button button-add" @click="control('add')">
                    <i class="el-icon-plus"></i>
                    <span>添加</span>
                  </span>
                </div>
              </div>
              <div v-for="(tVal, vIndex) in item.customReceiverList" :key="vIndex" style="display: flex; margin: 15px 0">
                <div class="generationContent-input">
                  <div class="label">通知部门或人员：</div>
                  <div>
                    <el-select v-model="tVal.type" class="type" :disabled="activeType == 'detail'" style="width: 100px" @change="changeNoticeType(item, vIndex)">
                      <el-option label="人员" :value="'0'"></el-option>
                      <el-option label="部门" :value="'1'"></el-option>
                    </el-select>
                    <el-input
                      v-if="tVal.type == '0'"
                      v-model="tVal.personNames"
                      :disabled="activeType == 'detail'"
                      type="text"
                      readonly
                      placeholder="请选择"
                      suffix-icon="el-icon-arrow-down"
                      style="width: 381px"
                      @focus="control('selectPers', vIndex)"
                    ></el-input>
                    <el-select
                      v-if="tVal.type == '1'"
                      v-model="tVal.departCodes"
                      :disabled="activeType == 'detail'"
                      placeholder="请选择"
                      clearable
                      multiple
                      collapse-tags
                      filterable
                      style="width: 337px"
                      @change="(val) => selectDept(val, vIndex)"
                      @visible-change="filterDeptList"
                    >
                      <el-option v-for="item in deptList" :key="item.id" :label="item.deptName" :value="item.id"></el-option>
                    </el-select>
                  </div>
                </div>
                <div v-show="tVal.type === '0'" class="generationContent-input">
                  <div style="display: flex">
                    <div class="label">联系方式：</div>
                    <div style="display: flex">
                      <el-input v-model="tVal.phoneNumber" :disabled="activeType == 'detail'" placeholder="请输入内容" style="width: 381px"></el-input>
                    </div>
                  </div>
                </div>
                <div>
                  <span v-show="vIndex != 0 || activeType !== 'detail'" class="termContent-button button-detele" @click="control('delete', vIndex)">
                    <i class="el-icon-delete"></i>
                    <span>删除</span>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-form>
    </div>
    <!--选择人员 -->
    <template v-if="personDialogShow">
      <SelectPeopleDialog :personDialogShow="personDialogShow"  @submitPersonDialog="submitPersonDialog" @closePersonDialog="closePersonDialog" />
    </template>
  </div>
</template>
<script>
import SelectPeopleDialog from '@/views/operationPort/exerciseManage/exercisePlan/components/SelectPeople.vue'
export default {
  name: 'configInfor',
  components: {
    SelectPeopleDialog
  },
  props: {
    atExpirationObj: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      formInline: {
        configType: '0', // 0使用期限到期时提醒配置,1使用期限到期前提醒配置
        enabled: '', // 是否开启提醒(0开启,1关闭)
        expireNotificationType: [], // 设备到期时/到期前通知方式(0PC,1APP,2短信,3微信)
        reminderTime: '', // 提醒时间
        receiveScope: '', // 信息接收人范围(0所属部门所有人,1所属部门负责人,2自定义)
        customReceiverList: [] // 自定义接收人列表
      },
      // 校验
      addRules: {},
      personDialogShow: false,
      informOptions: [
        {
          label: 'PC',
          value: '0',
          disabled: false
        },
        {
          label: 'APP',
          value: '1',
          disabled: false
        },
        {
          label: '短信',
          value: '2',
          disabled: ''
        },
        {
          label: '微信',
          value: '3',
          disabled: ''
        }
      ],
      receptionList: [
        // 信息接收人
        {
          label: '所属部门所有人',
          value: '0'
        },
        {
          label: '所属部门负责人',
          value: '1'
        },
        {
          label: '自定义',
          value: '2'
        }
      ],
      generationContents: [
        {
          configType: '0', // 0使用期限到期时提醒配置,1使用期限到期前提醒配置
          reminderTime: '',
          receiveScope: '',
          expireNotificationType: [], // 设备到期时/到期前通知方式(0PC,1APP,2短信,3微信)
          enabled: '0', // 通知方式，0-pc，1-app，2-短信，3-微信，
          customReceiverList: []
        }
      ],
      deptList: [], // 部门列表
      deptListAll: [], // 部门列表
      operationIndex: 0, // 选中项下标
      activeType: ''
    }
  },
  watch: {
    atExpirationObj: {
      handler(val) {
        if (Object.keys(val).length > 0) {
          this.generationContents = [JSON.parse(JSON.stringify(val))]
        }
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    this.activeType = this.$route.query.type
    this.getUseDeptList()
  },
  methods: {
    // 信息接收人change
    selectUser(e, index) {
      if (e != 2) {
        this.generationContents[index].customReceiverList = []
        return
      }
      this.generationContents[index].customReceiverList.push({
        type: '0', // 0-人员，1-部门
        ids: '', // 人员编码
        personNames: '', // 人员名称
        phoneNumber: '', // 联系电话
        departCodes: [] // 部门编码
      })
    },
    // 改变部门人员
    changeNoticeType(val, vIndex) {
      this.operationIndex = vIndex
      const params = this.generationContents[0].customReceiverList[vIndex]
      params.ids = ''
      params.personNames = ''
      params.phoneNumber = ''
      params.departCodes = []
      this.generationContents[0].customReceiverList[vIndex] = params
    },
    // 获取科室
    getUseDeptList() {
      this.$api.getSelectedDept().then((res) => {
        if (res.code == '200') {
          this.deptList = res.data
          this.deptListAll = res.data
        }
      })
    },
    // 选择人员
    control(type, vIndex) {
      if (type == 'selectPers') {
        this.operationIndex = vIndex
        // 选择人员
        this.personDialogShow = true
      } else if (type == 'add') {
        this.generationContents[0].customReceiverList.push({
          type: '0', // 1-部门，0-人员
          ids: '', // 人员编码
          personNames: '', // 人员名称
          phoneNumber: '', // 联系电话
          departCodes: [] // 部门编码
        })
      } else if (type == 'delete') {
        // 删除数组子项
        this.generationContents[0].customReceiverList.splice(vIndex, 1)
      }
    },
    // 过滤部门列表
    filterDeptList(val) {
      let list = this.generationContents[0].customReceiverList
      let arr = []
      list.forEach((item) => {
        if (item.type == '0') {
          let val = item.ids.length > 0 ? item.ids.split(',') : []
          arr.push(...val)
        }
      })
      console.log(arr)
      if (arr.length == 0) return (this.deptList = this.deptListAll)
      // 过滤
      this.deptList = this.deptListAll.filter((item) => {
        return !arr.includes(item.id)
      })
    },
    // 选择部门事件
    selectDept(val, vIndex) {
      if (val.length == 0) {
        this.generationContents[0].customReceiverList[vIndex].ids = []
      } else {
        this.generationContents[0].customReceiverList[vIndex].ids = val.join(',')
      }
    },
    // 人员弹窗
    submitPersonDialog(list) {
      if (list.length !== 1) {
        this.$message.error('人员只能选择一个')
        return
      }
      const { mobile, id, staffName } = list[0]
      let arr = this.generationContents[0].customReceiverList[this.operationIndex]
      arr.phoneNumber = mobile
      arr.ids = id
      arr.personNames = staffName
      this.personDialogShow = false
    },
    // 关闭选择人员弹窗
    closePersonDialog() {
      this.personDialogShow = false
    }
  }
}
</script>
<style lang="scss" scoped>
.el-form-item__content .el-input-group {
  vertical-align: middle;
}
.configContent {
  .generation-content {
    margin: 14px 00 14px 26px;
    display: flex;
    .generationContent-title {
      width: 70px;
      margin-right: 10px;
      padding-top: 10px;
    }
    .generationContent-info {
      flex: 1;
      .content-block {
        display: flex;
        flex-direction: column;
        margin: 0 0 16px 0;
        .generationContent-input {
          color: #666666;
          display: flex;
          margin-right: 23px;
          .label {
            height: 32px;
            line-height: 32px;
          }
        }
        .generationContent-operation {
          height: 32px;
          line-height: 32px;
          cursor: pointer;
          margin-left: 8px;
          .button-add {
            color: #3562db;
          }
        }
      }
    }
  }
}
::v-deep(.type) {
  flex: 0.35;
  .el-input__inner {
    color: #3562db;
  }
}
.button-detele {
  color: #ff6461;
  cursor: pointer;
  margin-left: 16px;
}
</style>
