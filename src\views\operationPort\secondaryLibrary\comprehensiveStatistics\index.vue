<template>
  <div class="comprehensive_statistics_box">
    <div class="comprehensive_statistics">
      <Statistics />
      <InboundAndOutboundStatistics />
      <AccessoriesStatistics />
    </div>
  </div>
</template>
<script>
import Statistics from './components/Statistics.vue'
import InboundAndOutboundStatistics from './components/InboundAndOutboundStatistics.vue'
import AccessoriesStatistics from './components/AccessoriesStatistics.vue'
export default {
  name: 'ComprehensiveStatistics',
  components: { Statistics, InboundAndOutboundStatistics, AccessoriesStatistics },
  data() {
    return {
      // isShow: true
    }
  }
  // methods: {
  //   handleTimeChange(val) {
  //     this.isShow = !val
  //   }
  // }
}
</script>
<style lang="less" scoped>
.comprehensive_statistics_box {
  height: 100%;
  overflow: hidden;
  overflow-y: scroll;
  .comprehensive_statistics {
    margin: 0 4%;
    :deep(.ellipsis_showtip_plus) {
      white-space: nowrap;
      > div {
        width: 100%;
        height: 100%;
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}
</style>
