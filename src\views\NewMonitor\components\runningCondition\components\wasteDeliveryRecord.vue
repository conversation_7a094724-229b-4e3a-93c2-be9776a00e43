<!--
 * @Author: hedd
 * @Date: 2023-03-13 16:13:48
 * @LastEditTime: 2023-09-19 11:03:21
 * @FilePath: \ihcrs_pc\src\views\drag\components\wasteDeliveryRecord.vue
 * @Description:
-->
<template>
  <ContentCard
    :title="item.componentTitle"
    :scrollbarHover="true"
    :cstyle="{ height: '100%' }"
    class="drag_class"
    :hasMoreOper="['edit']"
    @more-oper-event="(val) => $emit('all-more-Oper', val, 'workOderType')"
  >
    <div slot="title-right" class="data-btns">
      <span v-for="item in dataTypeList" :key="item.type" :class="{ 'active-btn': selectDataType == item.type }" @click="changeDateType(item.type)">{{ item.name }}</span>
    </div>
    <div slot="content" style="height: 100%">
      <TablePage
        ref="wasteRecoraTable"
        v-loading="tableLoading"
        class="waste-record-table"
        stripe
        :showPage="false"
        :tableColumn="tableColumn"
        :data="tableData"
        :summary-method="getSummaries"
        show-summary
        height="calc(100% - 1px)"
      />
    </div>
  </ContentCard>
</template>
<script>
import { dataTypeList } from '@/util/dict.js'
export default {
  name: 'wasteDeliveryRecord',
  props: {
    item: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dataTypeList,
      selectDataType: 'day',
      tableData: [],
      tableLoading: false,
      tableColumn: [
        {
          label: '医废类型',
          prop: 'wasteType'
        },
        {
          label: '重量(kg)',
          prop: 'gatherWeigh'
        },
        {
          label: '数量(袋)',
          prop: 'gatherBags'
        }
      ]
    }
  },
  mounted() {
    this.selectOutStoreRecordStatistics()
  },
  methods: {
    // 获取医废出库记录统计
    selectOutStoreRecordStatistics() {
      let params = {
        dateType: this.selectDataType
        // dateType: 'year'
      }
      // this.tableLoading = true
      this.$api.selectOutStoreRecordStatistics(params).then((res) => {
        this.tableLoading = false
        if (res.code == '200') {
          this.tableData = res.data ?? []
        }
      })
    },
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '总计'
          return
        }
        const values = data.map((item) => Number(item[column.property]))
        if (!values.every((value) => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0)
          // 重量保留两位小数
          if (index === 1 && !isNaN(parseFloat(sums[index]))) {
            sums[index] = sums[index].toFixed(2)
          }
        } else {
          sums[index] = 'N/A'
        }
      })
      return sums
    },
    // 切换日期类型
    changeDateType(type) {
      this.selectDataType = type
      this.selectOutStoreRecordStatistics()
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep(.el-table) .el-table__header .el-table__cell {
  background: #f3f4f6;
}
.data-btns {
  position: absolute;
  right: 7%;
  display: flex;
  align-items: center;
  justify-content: space-between;

  & > span {
    width: 40px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    margin: 0 4px;
    background-color: #faf9fc;
    font-size: 14px;
    font-family: 'PingFang SC-Medium', 'PingFang SC';
    border-radius: 4px;
    color: #414653;
    cursor: pointer;
  }

  .active-btn {
    background-color: #e6effc !important;
    color: #3562db !important;
  }
}

#orderTypeCharts {
  width: 100%;
  height: 100%;
  z-index: 999;
}
</style>
