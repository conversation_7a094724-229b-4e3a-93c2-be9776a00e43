<!-- 运行监测 -->
<template>
  <ContentCard :title="item.componentTitleShow" :scrollbarHover="true" :cstyle="{ height: '100%' }" class="drag_class"
    :hasMoreOper="['edit']" @more-oper-event="(val) => $emit('all-more-Oper', val, 'operationMonitor')">
    <div slot="title-right" class="operation-list">
      <el-select v-model="devicename" placeholder="设备名称" style="margin-top:-5px;" filterable clearable
        @change="selectCategory">
        <el-option v-for="(item, index) in deviceTypeList" :key="index" :label="item.assetsName"
          :value="item.id"></el-option>
      </el-select>
    </div>
  </ContentCard>
</template>
<script>
import { transData } from '@/util'
export default {
  name: 'sbmc',
  props: {
    item: {
      type: Object,
      default: () => { }
    },
    systemCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      devicename: "",//设备名称
      deviceTypeList: [],
    }
  },
  created() {
    this.getDataList()
  },
  methods: {
    selectCategory() {
      this.$emit('select-changed', this.devicename);
    },
    // 设备名称
    getDataList() {
      let data = {
        sysOfCode: this.systemCode,
        sysOf1Code: "",
        groupId: "",
      }
      this.deviceTypeList = []
      this.$api
        .getMasterOrMonitoredByCode(data)
        .then((res) => {
          if (res.code === '200' || res.code === 200) {
            this.deviceTypeList = res.data;
            if (this.deviceTypeList.length > 0) {
              this.devicename = this.deviceTypeList[0].id; // 假设 id 是唯一标识
              this.selectCategory(); // 发送默认值
            }
          }
        })
        .catch(() => {
        })
    },
  }
}
</script>
<style lang="scss" scoped>
.operation-list {
  height: 100%;
  display: flex;
  gap: 16px;
  flex-direction: row;
  margin-left: 50px;
}
</style>
