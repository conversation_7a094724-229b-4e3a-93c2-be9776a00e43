.shadow {
  z-index: 999;
}

.must-title::before {
  content: "*";
  color: #f56c6c;
  margin-right: 4px;
}

.filter input {
  width: 217px;
}

.filter {
  text-align: left;
}

.errorinput input {
  border-color: #f56c6c;
}

.input__inner {
  border-color: #f56c6c;
}

.title-header {
  font-size: 16px;
  font-family: "FZLTZHK--GBK1-0";
  font-weight: 600;
  color: #606266;
  height: 60px;
  line-height: 60px;
  text-align: left;
  background-color: #fff;
  margin-bottom: 20px;
  border-bottom: 1px solid #d8dee7;
  position: relative;
}

.title-header::before {
  content: "";
  width: 5px;
  height: 20px;
  background-color: #5188fc;
  position: absolute;
  top: 32%;
  left: -10px;
}

.myradio .el-radio__label {
  padding: 0;
}

.row_tree {
  height: calc(100% - 20px);
  overflow: auto;
}

.title {
  background: #fff;
  padding: 10px 15px;
  overflow: hidden;
  font-size: 20px;
  font-family: "FZLTZHK--GBK1-0";
  font-weight: 600;
  color: #606266;
}

.reportIframe {
  width: 100%;
  height: 100%;
}

.nomal_data_title {
  font-size: 16px;
  font-family: NotoSansHans-Medium;
  font-weight: 500;
  color: rgb(96 98 102 / 100%);

  /* height: 30px; */
  line-height: 30px;
}

.sino-tabs-item:first-child {
  padding-left: 0 !important;
}

.nomal_data {
  font-size: 26px;
  font-family: DIN-Bold;
  font-weight: bold;
  color: rgb(107 157 255 / 100%);
  vertical-align: top;
  margin-right: 30px;
}

.nomal_data_unusual {
  font-size: 26px;
  font-family: DIN-Bold;
  font-weight: bold;
  color: #ff1919;
  vertical-align: top;
  margin-right: 30px;
}

.print-preview-title {
  width: 100%;
  text-align: center;
}

.print-preview-table {
  width: 100%;
}

.print-preview-table tr {
  height: 40px;
}

.print-preview-table td {
  border: 1px solid #ebeef5;
  border-collapse: collapse;
}

.print .sino-panel-content {
  overflow: auto;
}

@media print {
  .print .tables {
    page-break-before: always;
  }
}

.print-preview-h3title {
  width: 150px;
  text-align: right;
}

.cklist .sino-content {
  height: calc(100% + 40px);
}

.dialog-title {
  background: #fff;
  text-align: left;
  height: 20px;
  font-size: 20px;
  font-family: "FZLTZHK--GBK1-0";
  font-weight: 600;
  color: rgb(53 53 53 / 100%);
}

.sino_sdcp_input {
  width: 200px;
  height: 40px;
}

.sino-button {
  width: 80px;
  height: 40px;
  border-radius: 4px;
  font-size: 16px;
  font-family: "FZLTZHK--GBK1-0";

  /* font-weight: 600; */
  color: #606266;
  border-color: #c0eeee;
  background-color: #fff;
}

.sino-button-advanced {
  font-size: 16px;
  color: #5188fc;
  cursor: pointer;
}

.sino-button-search {
  width: 80px;
  height: 40px;
  border-radius: 4px;
  font-size: 16px;
  font-family: "FZLTZHK--GBK1-0";

  /* font-weight: 600; */
  color: #606266;
  border: 1px solid rgb(220 223 229 / 100%);
  background-color: #fff;
}

.sino-button:hover,
.sino-button-search:hover,
.sino-button-advanced:hover {
  background: rgb(249 254 254 / 100%);
  border-radius: 4px;
  border: 1px solid #5188fc;
  color: #5188fc;
}

.sino-button-sure {
  height: 32px;
  color: #fff;
  background-color: #5188fc;
  border: 1px solid #5188fc;
  font-size: 16px;
  font-family: "FZLTZHK--GBK1-0";

  /* font-weight: 600; */
}

.table-tools .sino-button-sure,
.table-tools .sino-button-search,
.table-tools .sino-button-sure-search,
.table-tools .sino-button-cancel {
  font-size: 14px !important;
}

.middle_tools .sino-button-sure,
.middle_tools .sino-button-search,
.middle_tools .sino-button-sure-search,
.middle_tools .sino-button-cancel {
  font-size: 14px !important;
}

.sino-button-cancel {
  width: 80px;
  height: 35px;
  border-radius: 4px;
  font-size: 16px;
  font-family: "FZLTZHK--GBK1-0";

  /* font-weight: 600; */
  color: #606266;
  border: 1px solid rgb(220 223 229 / 100%);
  background-color: #fff;
}

.sino-button-sure-search {
  width: 80px;
  height: 40px;
  color: rgb(255 255 255 / 100%);
  background: #5188fc;
  border: 1px solid #5188fc;
  font-size: 16px;
  font-family: "FZLTZHK--GBK1-0";
}

.sino-button-advanced {
  width: 114px;
  height: 40px;
  color: #5188fc;
  background: rgb(249 254 254 / 100%);
  border: 1px solid #5188fc;
  font-size: 14px;
  font-family: "FZLTZHK--GBK1-0";
}

.sino-button-advanced > span {
  margin: 0 !important;
}

.sino-button-sure-search:hover {
  background: #5188fc;
  color: rgb(255 255 255 / 100%);
}

.sino-button-sure:hover {
  background: #5188fc;
  color: rgb(255 255 255 / 100%);
}

.primary {
  background: #5188fc;
  color: #fff;
}

.primary:hover {
  background: #56d2d1;
  color: #fff;
}

.btn-custom-cancel-right {
  float: right;
  margin-left: 10px;
}

.upload .el-dialog__body {
  display: flex;
  align-items: center;
  justify-content: center;
}

textarea {
  font-family: "FZLTHK--GBK1-0\, NotoSansHans-Medium" !important;
}

.filterbutton {
  margin: 0 auto 20px;
  width: 90px;
  height: 24px;
  background: #5188fc;
  border-radius: 16px;
  color: #fff;
  line-height: 24px;
  cursor: pointer;
}

.filterbutton img {
  margin-left: 6px;
  width: 12px;
  height: 16px;
  vertical-align: middle;
}

.rk_upload {
  display: inline-block;
}

.printTable > .has-gutter {
  color: red;
}

.imas_page {
  position: unset !important;
  padding-top: 10px;
}

.goods-detail input {
  width: 190px;
}

/* 定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background-color: #fff;
}

/* 定义滚动条轨道 内阴影+圆角 */
::-webkit-scrollbar-track {
  box-shadow: inset 0 0 6px #d8dee7;
  border-radius: 5px;
  background-color: #fff;
}

/* 定义滑块 内阴影+圆角 */
::-webkit-scrollbar-thumb {
  border-radius: 5px;
  box-shadow: inset 0 0 6px #d8dee7;
  background-color: #d8dee7;
}

.readonly_block {
  height: 100%;
  width: 100%;
}

.readonly_block textarea {
  resize: none;
}

.readonly_block input::input-placeholder {
  opacity: 0;
}

.readonly_block .is-checked {
  display: inline-block;
}

#optionbox {
  width: 100%;
  height: 350px;
  background-color: #fff;
}

#optionbox > div {
  width: 100% !important;
}

#optionbox > div > canvas {
  width: 100% !important;
}

.sino_item_title {
  display: inline-block;
  height: 54px;
  line-height: 54px;
  width: 120px;
  text-align: right;
}

.sino-talbel-edit {
  font-size: 14px;
  padding-right: 8px;
  cursor: pointer;
}

.sino-tabel-delete {
  color: rgb(255 25 25 / 100%);
}

.sino-tabel-picture {
  color: #5188fc;
}

.main_content .sino-panel-content {
  overflow: auto;
  padding: 0;
}

.topTabCom {
  margin-top: 65px;
}

@media screen and (max-width: 1600px) {
  .topTabCom {
    margin-top: 41px;
  }

  .content-table .table-tools {
    margin: 10px 0 !important;
  }

  .sino_sdcp_input {
    width: 160px;
  }

  .sino-report .sino-tabs-tools {
    margin: 0 !important;
    padding: 5px 0 20px 25px !important;
  }

  .sino-button-search,
  .sino-button-sure-search {
    width: 65px;
    height: 30px;
  }

  .sino-button-advanced {
    width: 90px;
    height: 30px;
  }

  .sino-button-sure {
    font-size: 12px;
    height: 28px;
    border-width: 1px;
  }

  .sino-button-cancel {
    font-size: 12px;
  }

  .sino-button-cancel {
    width: 65px;
  }
}

.reference {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  color: #fff;
}

.reference p {
  text-align: center;
}

.reference a {
  color: #fff;
}

.installmentBoxs {
  width: 80px;
  height: 40px;
  margin-right: 15px;
  box-sizing: border-box;
  float: left;
  border-radius: 4px;
  border: 1px solid rgb(220 223 229 / 100%);
  line-height: 40px;
  text-align: center;
  color: #606266;
  cursor: not-allowed;
}

body {
  opacity: 0;
  animation: page-fade-in 1s forwards;
}

@keyframes page-fade-in {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.isPayed {
  /* background-image: url(../../images/img/payed.png); */
  background-size: 100% 100%;
  border: none;
  color: #fff;
  cursor: pointer;
}

.isPayedChecked {
  background: #5188fc;
  border-radius: 4px;
  border: none;
  color: #fff;
  cursor: pointer;
}

.canChecked {
  background: rgb(249 254 254 / 100%);
  border-radius: 4px;
  border: 1px solid #5188fc;
  color: #5188fc;
  cursor: pointer;
}

.fpcode {
  width: 840px !important;
}

.tableEmptyLabel {
  /* margin-left: 10px; */
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentcolor;
  overflow: hidden;
}

/* 维保样式添加 */
.toptip {
  box-sizing: border-box;
  padding-left: 26px;
  height: 50px;
  width: 100%;
  line-height: 50px;
  text-align: left;
  border-bottom: 1px solid rgb(216 222 231 / 100%);
}

.green_line {
  display: inline-block;
  width: 4px;
  height: 16px;
  background: #5188fc;
  margin-right: 10px;
  vertical-align: middle;
}

.left_content {
  height: calc(100% - 90px);
  overflow: auto;

  /* padding: 20px 29px; */
  padding: 20px 10px;
}

.border-none input {
  border: none;
}

.textarea-border-none textarea {
  border: none;
}

.table-list {
  margin-top: 10px;
}

.advanced-search-form .sino-filter-row {
  height: 40px;
  line-height: 40px;
  min-width: 1350px;
  margin-bottom: 20px;
}

.advanced-search-form .sino-filter-row .sino-filter-col {
  height: 100%;
  display: inline-block;
}

.sino-part-left,
.sino-part-right {
  height: calc(100% - 20px);
  border-radius: 10px;
  background-color: #fff;
}

.sino-part-left {
  float: left;
  width: 268px;
  min-width: 14%;
  margin: 0 10px 10px 0;
  overflow: hidden;
}

.sino-part-right {
  text-align: left;
  box-sizing: border-box;
  margin: 0 0 0 278px;
  overflow-y: auto;
  padding: 20px 26px 0;
}

.sino-part-right .middle_tools {
  margin-top: 20px;
  margin-bottom: 10px;
}

.mr15 {
  margin-right: 15px;
}

.sino-dialog-medium .left {
  width: 268px;
  height: calc(100% - 4px);
  padding: 20px 10px 10px;
}

.sino-dialog-medium .selectPersonContent {
  width: calc(100% - 300px);
  height: 100%;
  margin: 20px 10px 0 0;
  padding: 10px;
}

.sino-dialog-input .sino-filter-row {
  height: 40px;
  line-height: 40px;
  margin-bottom: 20px;
}

.sino-dialog-input .sino-filter-row .sino-filter-col {
  width: 420px;
  height: 100%;
  display: inline-block;
}

.sino-dialog-form .sino-textarea {
  width: 724px;
}

.sino-panel-footer {
  z-index: 999;
  height: 60px;
  width: 100%;
  display: -webkit-box;
  display: flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding-left: 146px;
  position: absolute;
  background: #fff;
  box-shadow: 0 -4px 4px 0 #f3f4f8;

  /* bottom: 40px; */
  bottom: 0;
}

.sino-dialog-medium .left .block1 {
  height: calc(50% - 8px);
  overflow: auto;
}

.sino-dialog-medium .left {
  width: 268px;
  height: calc(100% - 4px);
  border-radius: 10px;
  background-color: #fff;
  box-sizing: border-box;
  padding: 20px 10px 10px;
}

.sino-dialog-medium .left .block1 {
  height: calc(50% - 8px);
  overflow: auto;
}

.sino-dialog-medium .selectPersonContent {
  width: calc(100% - 300px);
  height: 100%;
  border-radius: 10px;
  background-color: #fff;
  margin: 20px 10px 0 0;
  box-sizing: border-box;
  padding: 10px;
}

.storehouseManagementTree .left_content {
  height: calc(100% - 180px);
}

.assetClassification .sino-content {
  overflow: hidden;
}

.upload-demo {
  width: 100%;
}

.paddingTop10 {
  padding-top: 10px;
}

.repairs_msg .sino-form-textarea {
  display: flex !important;
}

.microcheiria {
  color: #5188fc;
}

.microcheiria:hover {
  cursor: pointer;
}

@media screen and (max-width: 1600px) {
  .sino-content {
    padding: 5px 25px 0;

    /* height: calc(100% + 51px) !important; */
    height: calc(100% + 0px) !important;
    box-sizing: border-box;
  }

  .heigth-1366 {
    height: calc(100% - 85px) !important;
  }

  .writtenHeight .sino-panel-content {
    height: calc(100% - 130px);
  }
}

@media screen and (max-width: 1600px) {
  .writtenHeight .sino-panel-content {
    height: calc(100% - 25px);
  }
}

.bottom_0 .sion-paenl-footer {
  bottom: 0;
}

.my-popover {
  background-color: #ffc;
  color: #000;
  overflow: inherit;
}

.my-popover .popper__arrow::after {
  border-right-color: #ffc !important;
}

@media screen and (max-width: 1600px) {
  .set_1366_height {
    height: calc(100% - 101px) !important;
  }
}

.width_lengthen {
  width: 766px;

  /* padding-right: 45px; */
}

.emptyDiv {
  height: 16px;
  width: 100%;
  background-color: rgb(245 245 250);
}
