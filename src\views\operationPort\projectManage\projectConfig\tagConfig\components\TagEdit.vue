<script>
import { UsingStatusOptions } from '@/views/operationPort/constant'
export default {
  name: 'TagEdit',
  props: {
    id: Number,
    visible: Boolean
  },
  events: ['update:visible', 'success'],
  data: function() {
    return {
      formModel: {
        name: '',
        code: '',
        status: 1,
        remark: ''
      },
      rules: {
        name: [{ required: true, message: '请输入标签名称' }],
        code: [{ required: true, message: '请输入标签编码' }]
      },
      loadingStatus: false,
      statusOptions: UsingStatusOptions
    }
  },
  computed: {
    title: function() {
      if (this.readonly) {
        return '查看标签'
      } else {
        return this.id ? '编辑标签' : '新建标签'
      }
    },
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    },
    toFetch() {
      return this.dialogVisible && this.id
    }
  },
  watch: {
    toFetch(val) {
      val && this.getDetail()
    }
  },
  methods: {
    getDetail() {
      this.loadingStatus = true
      this.$api.SporadicProject.selectProjectTagInfoOne({ id: this.id })
        .then((res) => {
          if (res.code === '200') {
            this.formModel.name = res.data.name
            this.formModel.code = res.data.code
            this.formModel.status = +res.data.state
            this.formModel.remark = res.data.remark
          } else {
            throw res.msg
          }
        })
        .catch((msg) => this.$message.error(msg || '获取供应商详情失败'))
        .finally(() => (this.loadingStatus = false))
    },
    onDialogClosed() {
      this.$refs.formRef.resetFields()
    },
    onSubmit() {
      this.$refs.formRef
        .validate()
        .catch(() => Promise.reject())
        .then(() => {
          // config request data
          const params = {
            code: this.formModel.code,
            name: this.formModel.name,
            remark: this.formModel.remark,
            state: this.formModel.status
          }
          this.loadingStatus = true
          // do request
          if (this.id) {
            params.id = this.id
            return this.$api.SporadicProject.updateProjectTagInfo(params)
          } else {
            return this.$api.SporadicProject.saveProjectTagInfo(params)
          }
        })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('操作成功')
            this.$emit('success')
            this.dialogVisible = false
          } else {
            throw res.msg || '操作失败'
          }
        })
        .catch((msg) => {
          msg && this.$message.error(msg)
        })
        .finally(() => {
          this.loadingStatus = false
        })
    }
  }
}
</script>
<template>
  <el-dialog
    v-dialogDrag
    class="component tag-edit"
    :title="title"
    width="750px"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    custom-class="model-dialog"
    @closed="onDialogClosed"
  >
    <el-form ref="formRef" :model="formModel" :rules="rules" label-width="95px">
      <el-row :gutter="12">
        <el-col :span="12">
          <el-form-item label="标签名称" prop="name">
            <el-input v-model="formModel.name" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="标签编码" prop="code">
            <el-input v-model="formModel.code" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select v-model="formModel.status" placeholder="请选择">
              <el-option v-for="item of statusOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="formModel.remark" type="textarea" :rows="4"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button type="primary" plain @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="onSubmit">确认</el-button>
    </template>
  </el-dialog>
</template>
<style lang="scss">
.component.tag-edit {
  .el-form {
    background-color: #fff;
    padding: 10px 16px 0;
    .el-form-item {
      .el-select {
        width: 100%;
      }
      .el-form-item__content .el-input-group {
        vertical-align: middle;
      }
    }
  }
}
</style>
