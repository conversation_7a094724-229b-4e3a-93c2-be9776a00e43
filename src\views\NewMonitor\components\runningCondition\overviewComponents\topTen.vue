<!-- 运行监测 -->
<template>
  <ContentCard :title="item.componentTitleShow" :scrollbarHover="true" :cstyle="{ width: '100%', height: '100%' }"
    class="drag_class" :hasMoreOper="['edit']"
    @more-oper-event="(val) => $emit('all-more-Oper', val, item.componentName, item.chartType)">
    <div slot="title-right" class="data-btns">
      <span :class="{ 'active-btn': totalCostDateType == 'THIS_WEEK' }" @click="changeTime('THIS_WEEK')">周</span>
      <span :class="{ 'active-btn': totalCostDateType == 'THIS_MONTH' }" @click="changeTime('THIS_MONTH')">月</span>
      <span :class="{ 'active-btn': totalCostDateType == 'THIS_YEAR' }" @click="changeTime('THIS_YEAR')">年</span>
    </div>
    <div slot="content" class="operation-list">
      <echarts :ref="`topTenEchars${item.componentDataType}${item.systemCode}`"
        :domId="`topTenEchars${item.componentDataType}${item.systemCode}`" :isTrigger="true" :xyType="'yAxis'"
        width="100%" height="100%" />
    </div>
  </ContentCard>
</template>

<script>
import moment from 'moment'
export default {
  name: 'topTen',
  props: {
    item: {
      type: Object,
      default: () => { }
    },
    systemCode: {
      type: String,
      default: ''
    }
  },
  created() {
  },
  mounted() {
    setTimeout(() => {
      this.getTopTenData()
    }, 100)
  },
  data() {
    return {
      loading: false,
      totalCostDateType: 'THIS_MONTH',
      operationList: [],
      chartType: '1'
    }
  },
  methods: {
    getTopTenData(id) {
      const payload = {
        timePeriod: 'THIS_MONTH',
        limit: 99,
        systemCode: this.systemCode,
        groupId: id || ""
      }
      if (this.item.componentDataType === 'runningTime') {
        this.$api.getAirConditionRunningTimeDataTrend(payload).then((res) => {
          if (res.code === "200") {
            this.appendEchartsData(res.data)
          } else {
            this.hasChart = false
          }
        })
      } else if (this.item.componentDataType === 'offLineCount') {
        this.$api.getAirConditionOfflineCountTrend(payload).then((res) => {
          if (res.code === "200") {
            this.appendEchartsData(res.data)
          } else {
            this.hasChart = false
          }
        })
      }


    },
    appendEchartsData(data) {
      const baseData = {
        grid: {
          left: '0%',
          right: '10%',
          bottom: '5%',
          top: '5%',
          containLabel: true
        },
        xAxis: {
          max: 'dataMax'
        },
        yAxis: {
          type: 'category',
          data: [],
          inverse: true,
          axisLabel: {
            formatter: function (value) {
              return value.length > 5 ? value.substring(0, 5) + '...' : value; // 截断文字
            }
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{b} : {c}',
          confine: true
        },
        series: [
          {
            realtimeSort: true,
            name: 'X',
            type: 'bar',
            data: [],
            label: {
              show: true,
              position: 'right',
              valueAnimation: true
            }
          }
        ]
      }
      if (data && data.offlineDevices && data.offlineDevices.length > 0) {
        data.offlineDevices.forEach((item) => {
          baseData.yAxis.data.push(item.assetsName)
          baseData.series[0].data.push(item.value)
        })
      }
      this.$refs[`topTenEchars${this.item.componentDataType}${this.item.systemCode}`].init(baseData)
    },
    changeTime(type) {
      this.totalCostDateType = type
      this.getTopTenData()
    }
  }
}
</script>

<style lang="scss" scoped>
.data-btns {
  position: absolute;
  right: 7%;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &>span {
    width: 44px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    margin: 0 4px;
    background-color: #f6f5fa;
    font-size: 14px;
    font-family: 'PingFang SC-Medium', 'PingFang SC';
    border: none;
    border-radius: 2px;
    color: #7f848c;
    cursor: pointer;
  }

  .active-btn {
    background-color: #e6effc !important;
    color: #3562db !important;
    border-color: #e6effc !important;
  }
}

.operation-list {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
}
</style>
