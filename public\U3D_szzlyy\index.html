<!DOCTYPE html>
<html lang="en-us">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>Unity WebGL Player | U3DVisualization</title>
    <style>
      html,
      body,
      .unity_container {
        width: 100%;
        height: 100%;
        margin: 0;
        padding: 0;
        border: 0;
        box-sizing: border-box;
        overflow: hidden;
      }
      .unity_container {
        position: absolute;
        z-index: 1;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
      #unityCanvas {
        display: none;
        width: 100%;
        height: 100%;
      }
      .img_mask {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 2;
      }
      #btn {
        position: fixed;
        top: 0;
        right: 0;
        z-index: 2;
      }
      .mouse_Tip {
        display: none;
        position: fixed;
        z-index: 9999;
        background: white;
        border: 1px solid gray;
        padding: 4px;
        border-radius: 4px;
        font-size: 14px;
      }
      .params_name {
        display: inline-block;
        width: 80px;
        text-align: left;
      }
      .params_val {
        display: inline-block;
        width: 100px;
        text-align: left;
      }
    </style>
  </head>
  <body style="text-align: center">
    <div class="unity_container">
      <canvas id="unityCanvas"></canvas>
    </div>
    <div id="imgMask" class="img_mask">
      <img src="loading_logo.png" alt="Pulpit rock" width="50%" />
    </div>
    <script src="Build/szzlyy.loader.js"></script>
    <script>
      // 全局变量声明
      let canvas = document.querySelector('#unityCanvas')
      let imgMask = document.querySelector('#imgMask')
      let SINO_U,
        msg = null
      // U3d_web实例化
      createUnityInstance(document.querySelector('#unityCanvas'), {
        dataUrl: 'Build/szzlyy.data.unityweb',
        frameworkUrl: 'Build/szzlyy.framework.js.unityweb',
        codeUrl: 'Build/szzlyy.wasm.unityweb',
        streamingAssetsUrl: 'StreamingAssets',
        companyName: 'SINOMIS',
        productName: 'U3DVisualization',
        productVersion: '1.0'
      }).then((res) => {
        canvas.style.display = 'block'
        imgMask.style.display = 'none'
        return (SINO_U = res)
      })
      window.addEventListener('message', function (event) {
        if (event.data.type === 'fullScreen') {
          fullScreen()
        } else {
          VueSendToUnity(event.data)
        }
      })
      //全屏显示
      function fullScreen() {
        if (SINO_U == null) return false
        SINO_U.SetFullscreen(1)
      }
      //发送WebGl消息
      function VueSendToUnity(params) {
        if (SINO_U == null) return false
        SINO_U.SendMessage('GameManager', 'GetNetMessage', params)
      }
      //接收unity消息
      function ReceiveData(data) {
        msg = JSON.parse(data)
        let modelCodeArr
        if (msg.Type == 'Hover' || msg.Type == 'Click') {
          modelCodeArr = JSON.parse(msg.SelectRooms)[0].split('_')
        }
        if (msg.Type == 'Hover') {
          window.parent.getModelMsg(modelCodeArr), 2000
          // setTimeout(window.parent.getModelMsg(modelCode, mouseTip), 2000);
        } else if (msg.Type == 'Click') {
          window.parent.openDialog(modelCodeArr)
        } else if (Object.keys(msg).includes('type') && msg.data) {
          window.parent.lightingSwitch(msg.data)
        } else {
          return false
        }
      }
    </script>
  </body>
</html>
