<template>
  <div class="accessories_statistics">
    <div class="title">配件统计</div>
    <div>
      <el-table border :resizable="false" :data="tableData" @sort-change="handleTableSort">
        <el-table-column prop="materialName" label="配件名称" show-overflow-tooltip></el-table-column>
        <el-table-column prop="materialCode" label="配件编码" show-overflow-tooltip></el-table-column>
        <el-table-column prop="materialTypeName" label="所属分类"> </el-table-column>
        <el-table-column prop="unitPrice" label="单价（元）" width="120"> </el-table-column>
        <el-table-column prop="basicUnitName" label="基本单位"> </el-table-column>
        <el-table-column prop="brandName" label="品牌" show-overflow-tooltip></el-table-column>
        <el-table-column prop="model" label="规格型号" show-overflow-tooltip></el-table-column>
        <el-table-column prop="manufacturerName" label="生产厂家"> </el-table-column>
        <el-table-column prop="totalInventory" label="库存总数量" sortable="custom" width="120"> </el-table-column>
        <el-table-column prop="totalPrice" label="库存总金额（元）" sortable="custom" width="160"> </el-table-column>
        <el-table-column prop="totalInCount" label="入库数量" sortable="custom" width="120"> </el-table-column>
        <el-table-column prop="totalInValue" label="入库金额（元）" sortable="custom" width="160" show-overflow-tooltip></el-table-column>
        <el-table-column prop="totalOutCount" label="出库数量" sortable="custom" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="totalOutValue" label="出库金额（元）" width="160" sortable="custom" show-overflow-tooltip></el-table-column>
        <el-table-column label="操作" width="230" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" @click="handleOperating('/secondaryLibrary/inventoryManagement', scope.row)">库存明细</el-button>
            <el-button type="text" @click="handleOperating('/secondaryLibrary/inventoryDetails', scope.row)">入库明细</el-button>
            <el-button type="text" @click="handleOperating('/secondaryLibrary/outboundDetails', scope.row)">出库明细</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination layout="prev, pager, next" :page-size="5" :pager-count="5" :total="pagesInfo.total" @current-change="handleCurrentChange"> </el-pagination>
    </div>
  </div>
</template>
<script>
import api from '@/api/index.js'
export default {
  name: 'AccessoriesStatistics',
  data() {
    return {
      tableData: [],
      pagesInfo: {
        currentPage: 1,
        pageSize: 5,
        total: 0,
        orderBy: '',
        orderDirection: ''
      }
    }
  },
  mounted() {
    this.handleGetData()
  },
  methods: {
    handleTableSort(e) {
      const { order, prop } = e
      if (order) {
        this.pagesInfo.orderBy = prop
        this.pagesInfo.orderDirection = order === 'ascending' ? 'asc' : 'desc'
      } else {
        this.pagesInfo.orderBy = ''
        this.pagesInfo.orderDirection = ''
      }
      this.handleGetData()
    },
    handleCurrentChange(e) {
      this.pagesInfo = { ...this.pagesInfo, currentPage: e }
      this.handleGetData()
    },
    handleGetData() {
      const { username, userId } = this.$store.state.user.userInfo
      const params = {
        username,
        userId,
        ...this.pagesInfo
      }
      api.getInventoryStatisticsList(params).then((res) => {
        const { sum, list } = res.data
        this.tableData = list
        this.pagesInfo.total = sum
      })
    },
    handleOperating(path, row) {
      const { materialCode } = row
      this.$router.push({ path, query: { materialCode } })
    }
  }
}
</script>
<style lang="scss" scoped>
.accessories_statistics {
  background: #fff;
  border-radius: 4px 4px 4px 4px;
  padding: 24px;
  margin-top: 16px;
  .title {
    color: #333333ff;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 24px;
  }
}
</style>
