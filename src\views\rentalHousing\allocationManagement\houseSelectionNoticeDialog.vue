<template>
  <el-dialog
    v-dialogDrag
    class="component housing-edit"
    :title="title"
    width="30%"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    custom-class="model-dialog"
    destroy-on-close
    @close="onDialogClosed"
  >
    <div class="dialog-submit">
      <el-form ref="formRef" class="housing-list__form" :model="formInfo" :rules="rules" inline>
        <el-form-item prop="date" label="选房时间">
          <el-date-picker
            v-model="formInfo.date"
            type="datetimerange"
            start-placeholder="开始时间"
            range-separator="至"
            style="width: 380px; margin-right: 8px"
            value-format="yyyy-MM-dd HH:mm:ss"
            end-placeholder="结束时间"
            :default-time="['00:00:00', '23:59:59']"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item prop="location" label="选房地点">
          <el-input
            type="textarea"
            v-model="formInfo.location"
            style="width: 380px"
            placeholder="选房地点"
            :rows="6"
            resize="none"
            maxlength="20"
            show-word-limit
            clearable
          ></el-input>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <el-button type="primary" plain @click="onDialogClosed">取消</el-button>
      <el-button type="primary" @click="onSubmit">确认</el-button>
    </template>
  </el-dialog>
</template>
  <script>
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    multipleSelection: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      title: '选房通知',
      rules: {
        date: [{ required: true, message: '请选择选房时间', trigger: 'change' }],
        location: [{ required: true, message: '请输入选房地点', trigger: 'blur' }]
      },
      formInfo: {
        date: [],
        location: ''
      }
    }
  },
  mounted() {},
  methods: {
    onDialogClosed() {
      this.$emit('xfDialogclose')
    },
    onSubmit() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.confirmAgain()
        }
      })
    },
    confirmAgain() {
      let html = `<div style="display: flex;align-items: center;">
            <span class="el-icon-warning" style="color: #f59a23;font-size: 22px;margin-right: 5px;"></span><span style="font-weight: 600;">选房通知</span>
        </div>
        <div style="padding-left: 28px;">点击确认，将为所选成员发送选房通知</div>
        <div style="padding-left: 28px;">通知预览：</div>
        <div style="padding-left: 28px;">已入围公租房分配，请在指定时间内进行选房</div>
        <div style="padding-left: 28px;">选房时间：${this.formInfo.date[0]}~${this.formInfo.date[1]}</div>
        <div style="padding-left: 28px;">选房地点：${this.formInfo.location}</div>`
      this.$alert(html, '', {
        dangerouslyUseHTMLString: true,
        showClose: false,
        showCancelButton: true
      })
        .then(() => {
          this.submitFn()
        })
        .catch(() => {
          //   this.$emit('xfDialogOpen')
        })
    },
    submitFn() {
      let arr = this.multipleSelection.map((item) => item.userPhone)
      let params = {
        startTime: this.formInfo.date.length ? this.formInfo.date[0] : '',
        endTime: this.formInfo.date.length ? this.formInfo.date[1] : '',
        location: this.formInfo.location,
        userPhonseList: arr
      }
      this.$api.rentalHousingApi.sendAllocationNotice(params).then((res) => {
        if (res.code == 200) {
          this.$message({
            type: 'success',
            message: res.message
          })
          this.$emit('xfDialogclose')
        } else {
          this.$message({
            type: 'error',
            message: res.message
          })
        }
      })
    }
  }
}
</script>
  <style lang="scss" scoped>
.dialog-submit {
  padding: 15px;
  box-sizing: border-box;
  width: 100%;
  background: #fff;
  .room-box {
    height: 32px;
    display: flex;
    align-items: center;
    .room-box-title {
      font-size: 15px;
      width: 65px;
      flex-shrink: 0;
      padding-left: 5px;
      position: relative;
      text-align: right;
      margin-right: 10px;
    }
    .room-box-title::after {
      content: '*';
      position: absolute;
      top: 0px;
      left: -5px;
      color: #ff1919;
    }
    .room-box-inpit {
      flex-shrink: 0;
      flex: 1;
      display: flex;
      justify-content: space-between;
      .box-input {
        width: calc(100% - 85px);
        flex-shrink: 0;
      }
      .box-div {
        width: 70px;
        text-align: center;
        color: #3664dd;
        cursor: pointer;
      }
    }
  }
  .roomBox {
    margin-top: 15px;
    .room-box-title::after {
      content: '';
      color: #fff;
    }
  }
}
</style>