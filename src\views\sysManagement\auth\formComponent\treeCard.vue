<!--
 * @Author: hedd
 * @Date: 2023-02-27 14:35:20
 * @LastEditTime: 2023-03-17 15:05:44
 * @FilePath: \IHCRS_alarm\src\views\sysManagement\auth\formComponent\treeCard.vue
 * @Description:
-->
<template>
  <div class="tree-box" :style="{ '--treeHeight': treeHeight }">
    <el-tree
      ref="tree"
      :key="refPname"
      :data="treeData"
      show-checkbox
      :node-key="nodeKey"
      :check-strictly="checkStrictly"
      :default-expand-all="defaultExpandAll"
      :default-expanded-keys="defaultExpandedKeys"
      :default-checked-keys="defaultCheckedKeys"
      :props="defaultProps"
      @check="nodeCheckChange"
    >
    </el-tree>
  </div>
</template>
<script>
export default {
  name: 'treeCard',
  props: {
    treeData: {
      type: Array,
      default: () => []
    },
    defaultExpandedKeys: {
      type: Array,
      default: () => []
    },
    defaultCheckedKeys: {
      type: Array,
      default: () => []
    },
    defaultProps: {
      type: Object,
      default: () => {
        return {
          children: 'children',
          label: 'label'
        }
      }
    },
    nodeKey: {
      type: String,
      default: 'id'
    },
    treeHeight: {
      type: String,
      default: '400px'
    },
    defaultExpandAll: {
      type: Boolean,
      default: false
    },
    checkStrictly: {
      type: Boolean,
      default: false
    }, // 是否严格的遵循父子不互相关联的做法，默认为 false
    refPname: {
      type: String,
      default: Math.random().toString()
    }
  },
  data() {
    return {}
  },
  methods: {
    getCheckedNodes(leafOnly = false, includeHalfChecked = false) {
      return this.$refs.tree.getCheckedNodes(leafOnly, includeHalfChecked)
    },
    setCheckedKeys(keys, leafOnly = false) {
      console.log(keys)
      this.$refs.tree.setCheckedKeys(keys, leafOnly)
    },
    getNode(id) {
      return this.$refs.tree.getNode(id)
    },
    nodeCheckChange(data) {
      this.$emit('nodeCheckChange', [this.getNode(data.menuId), this.refPname])
    }
  }
}
</script>
<style lang="scss" scoped>
.tree-box {
  padding: 10px;
  border: 1px solid #dcdfe6;
  height: var(--treeHeight);
  flex: 1;
  overflow: auto;
}
</style>
