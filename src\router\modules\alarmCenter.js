/*
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-03-23 10:11:51
 * @LastEditors: chenx
 * @LastEditTime: 2024-09-07 10:02:40
 * @FilePath: \ihcrs_pc\src\router\modules\alarmCenter.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Layout from '@/layout'
import EmptyLayout from '@/layout/empty'
export default [
  // {
  //   path: '/alarmRecord',
  //   component: Layout,
  //   redirect: '/alarmRecord/index',
  //   name: 'AlarmRecord',
  //   meta: {
  //     title: '报警记录',
  //     menuAuth: '/alarmRecord/index'
  //     // icon: 'sidebar-breadcrumb'
  //   },
  //   children: [
  //     {
  //       path: 'index',
  //       name: 'AlarmRecordIndex',
  //       component: () => import('@/views/alarmCenter/alarmRecord/alarmRecord.vue'),
  //       meta: {
  //         title: '报警记录',
  //         sidebar: false,
  //         breadcrumb: false,
  //         activeMenu: '/alarmRecord'
  //       }
  //     }
  //   ]
  // },
  {
    path: '/warnStatisticAnalysis',
    component: Layout,
    redirect: '/warnStatisticAnalysis/index',
    meta: {
      title: '统计分析',
      menuAuth: '/warnStatisticAnalysis/index'
      // icon: 'sidebar-breadcrumb'
    },
    children: [
      {
        path: 'index',
        name: 'warnStatisticAnalysis',
        component: () => import('@/views/alarmCenter/warnStatisticAnalysis/index.vue'),
        meta: {
          title: '统计分析',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/warnStatisticAnalysis'
        }
      }
    ]
  },
  // {
  //   path: '/linkageConfig',
  //   component: Layout,
  //   redirect: '/linkageConfig/index',
  //   name: 'LinkageConfig',
  //   meta: {
  //     title: '联动配置',
  //     menuAuth: '/linkageConfig/index'
  //     // icon: 'sidebar-breadcrumb'
  //   },
  //   children: [
  //     {
  //       path: 'index',
  //       name: 'LinkageConfigIndex',
  //       component: () => import('@/views/alarmCenter/linkageConfiguration/linkageConfig.vue'),
  //       meta: {
  //         title: '联动配置',
  //         sidebar: false,
  //         breadcrumb: false,
  //         activeMenu: '/linkageConfig'
  //       }
  //     }
  //   ]
  // },
  {
    path: '/dutyManagement',
    component: Layout,
    redirect: '/dutyManagement/index',
    name: 'dutyManagement',
    meta: {
      title: '值班管理',
      menuAuth: '/dutyManagement/index'
    },
    children: [
      {
        path: 'index',
        name: 'dutyManagementIndex',
        component: () => import('@/views/alarmCenter/dutyScheduling/dutyManagement.vue'),
        meta: {
          title: '值班管理',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/dutyManagement'
        }
      }
    ]
  },
  {
    path: '/dutyRecord',
    component: Layout,
    redirect: '/dutyRecord/index',
    name: 'dutyRecord',
    meta: {
      title: '值班记录',
      menuAuth: '/dutyRecord/index'
    },
    children: [
      {
        path: 'index',
        name: 'dutyRecordIndex',
        component: () => import('@/views/alarmCenter/dutyScheduling/dutyRecord.vue'),
        meta: {
          title: '值班记录',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/dutyRecord'
        }
      }
    ]
  },
  {
    path: '/emergencyTeam',
    component: Layout,
    redirect: '/emergencyTeam/index',
    name: 'emergencyTeam',
    meta: {
      title: '应急队伍',
      menuAuth: '/emergencyTeam/index'
      // icon: 'sidebar-breadcrumb'
    },
    children: [
      {
        path: 'index',
        name: 'emergencyTeamIndex',
        component: () => import('@/views/alarmCenter/emergencyTeam/index.vue'),
        meta: {
          title: '应急队伍',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/emergencyTeam'
        }
      }
    ]
  },
  {
    path: '/convergedCommunication',
    component: Layout,
    redirect: '/convergedCommunication/terminalManagement',
    name: 'convergedCommunication',
    meta: {
      title: '融合通讯',
      menuAuth: '/convergedCommunication'
    },
    children: [
      {
        path: 'terminalManagement',
        component: EmptyLayout,
        redirect: { name: 'terminalManagement' },
        meta: {
          title: '通信终端管理',
          menuAuth: '/convergedCommunication/terminalManagement'
        },
        children: [
          {
            path: '',
            name: 'terminalManagement',
            component: () => import('@/views/alarmCenter/convergedCommunication/terminalManagement/terminalManagement.vue'),
            meta: {
              title: '通信终端管理',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/convergedCommunication/terminalManagement'
            }
          }
        ]
      },
      {
        path: 'schedulingManagement',
        component: EmptyLayout,
        redirect: { name: 'groupManagement' },
        meta: {
          title: '指挥调度管理',
          menuAuth: '/convergedCommunication/schedulingManagement'
        },
        children: [
          {
            path: 'groupManagement',
            component: EmptyLayout,
            redirect: { name: 'groupManagement' },
            meta: {
              title: '群组管理',
              menuAuth: '/convergedCommunication/schedulingManagement/groupManagement'
            },
            children: [
              {
                path: '',
                name: 'groupManagement',
                component: () => import('@/views/alarmCenter/convergedCommunication/schedulingManagement/groupManagement/groupManagement.vue'),
                meta: {
                  title: '群组管理',
                  sidebar: false,
                  breadcrumb: false,
                  activeMenu: '/convergedCommunication/schedulingManagement/groupManagement'
                }
              }
            ]
          },
          {
            path: 'alarmRecord',
            component: EmptyLayout,
            redirect: { name: 'alarmRecord' },
            meta: {
              title: '报警记录',
              menuAuth: '/convergedCommunication/schedulingManagement/alarmRecord'
            },
            children: [
              {
                path: '',
                name: 'alarmRecord',
                component: () => import('@/views/alarmCenter/convergedCommunication/schedulingManagement/alarmRecord/alarmRecord.vue'),
                meta: {
                  title: '报警记录',
                  sidebar: false,
                  breadcrumb: false,
                  activeMenu: '/convergedCommunication/schedulingManagement/alarmRecord'
                }
              }
            ]
          },
          {
            path: 'callRecords',
            component: EmptyLayout,
            redirect: { name: 'callRecords' },
            meta: {
              title: '呼叫纪录',
              menuAuth: '/convergedCommunication/schedulingManagement/callRecords'
            },
            children: [
              {
                path: '',
                name: 'callRecords',
                component: () => import('@/views/alarmCenter/convergedCommunication/schedulingManagement/callRecords/callRecords.vue'),
                meta: {
                  title: '呼叫纪录',
                  sidebar: false,
                  breadcrumb: false,
                  activeMenu: '/convergedCommunication/schedulingManagement/callRecords'
                }
              }
            ]
          }
        ]
      },
      {
        path: 'basicConfiguration',
        component: EmptyLayout,
        redirect: { name: 'basicConfiguration' },
        meta: {
          title: '基础配置',
          menuAuth: '/convergedCommunication/basicConfiguration'
        },
        children: [
          {
            path: '',
            name: 'basicConfiguration',
            component: () => import('@/views/alarmCenter/convergedCommunication/basicConfiguration.vue'),
            meta: {
              title: '基础配置',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/convergedCommunication/basicConfiguration'
            }
          }
        ]
      }
    ]
  },
  // 新报警菜单
  {
    path: '/allAlarm',
    component: Layout,
    redirect: '/allAlarm/allAlarmIndex',
    name: 'allAlarm',
    meta: {
      title: '全部报警',
      menuAuth: '/allAlarm/allAlarmIndex'
      // icon: 'sidebar-breadcrumb'
    },
    children: [
      {
        path: 'allAlarmIndex',
        name: 'allAlarmIndex',
        component: () => import('@/views/alarmCenter/allAlarm/index.vue'),
        meta: {
          title: '全部报警',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/allAlarm'
        }
      },
      {
        path: 'alarmDetail',
        name: 'alarmDetail',
        component: () => import('@/views/alarmCenter/alarmDetail/index.vue'),
        meta: {
          title: '报警详情',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/alarmDetail'
        }
      }
    ]
  },
  {
    path: '/noticeMy',
    component: Layout,
    redirect: '/noticeMy/index',
    name: 'noticeMy',
    meta: {
      title: '通知我的',
      menuAuth: '/noticeMy/index'
      // icon: 'sidebar-breadcrumb'
    },
    children: [
      {
        path: 'index',
        name: 'noticeMyIndex',
        component: () => import('@/views/alarmCenter/noticeMy/index.vue'),
        meta: {
          title: '通知我的',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/noticeMy'
        }
      }
    ]
  },
  {
    path: '/classicCases',
    component: Layout,
    redirect: '/classicCases/index',
    name: 'ClassicCases',
    meta: {
      title: '经典案例',
      menuAuth: '/classicCases/index'
      // icon: 'sidebar-breadcrumb'
    },
    children: [
      {
        path: 'index',
        name: 'ClassicCasesIndex',
        component: () => import('@/views/alarmCenter/classicCases.vue'),
        meta: {
          title: '经典案例',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/classicCases'
        }
      }
    ]
  },
  {
    path: '/alarmConfig',
    component: Layout,
    redirect: '/alarmConfig/alarmConfigIndex',
    name: 'alarmConfig',
    meta: {
      title: '报警配置',
      menuAuth: '/alarmConfig'
    },
    children: [
      {
        path: 'alarmConfigIndex',
        component: EmptyLayout,
        redirect: { name: 'alarmConfigIndex' },
        meta: {
          title: '报警联动配置',
          menuAuth: '/alarmConfig/alarmConfigIndex'
        },
        children: [
          {
            path: '',
            name: 'alarmConfigIndex',
            component: () => import('@/views/alarmCenter/alarmConfig/index.vue'),
            meta: {
              title: '报警联动配置',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/alarmConfig/alarmConfigIndex'
            }
          },
          {
            path: 'addAlarmConfig',
            name: 'addAlarmConfig',
            component: () => import('@/views/alarmCenter/alarmConfig/addAlarmConfig.vue'),
            meta: {
              title: '新增报警',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/alarmConfig/alarmConfigIndex'
            }
          }
        ]
      },
      {
        path: '/unifiedFieldConfiguration',
        component: EmptyLayout,
        redirect: '/unifiedFieldConfiguration/index',
        name: 'unifiedFieldConfiguration',
        meta: {
          title: '外部映射配置',
          menuAuth: '/unifiedFieldConfiguration/index'
          // icon: 'sidebar-breadcrumb'
        },
        children: [
          {
            path: 'index',
            name: 'unifiedFieldConfigurationIndex',
            component: () => import('@/views/alarmCenter/unifiedFieldConfiguration/index.vue'),
            meta: {
              title: '外部映射配置',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/unifiedFieldConfiguration'
            }
          }
        ]
      },
      {
        path: '/dictConfiguration',
        component: EmptyLayout,
        redirect: '/dictConfiguration/index',
        name: 'dictConfiguration',
        meta: {
          title: '字典配置',
          menuAuth: '/dictConfiguration/index'
          // icon: 'sidebar-breadcrumb'
        },
        children: [
          {
            path: 'index',
            name: 'dictConfigurationIndex',
            component: () => import('@/views/alarmCenter/dictConfiguration/index.vue'),
            meta: {
              title: '字典配置',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/dictConfiguration'
            }
          }
        ]
      },
      {
        path: '/generalConfiguration',
        component: EmptyLayout,
        redirect: '/generalConfiguration/index',
        name: 'generalConfiguration',
        meta: {
          title: '通用配置',
          menuAuth: '/generalConfiguration/index'
          // icon: 'sidebar-breadcrumb'
        },
        children: [
          {
            path: 'index',
            name: 'generalConfiguration',
            component: () => import('@/views/alarmCenter/generalConfiguration/index.vue'),
            meta: {
              title: '通用配置',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/generalConfiguration'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/alarmMaterials',
    component: Layout,
    redirect: '/alarmMaterials/index',
    name: 'alarmMaterials',
    meta: {
      title: '报警附近物资',
      menuAuth: '/alarmMaterials/index'
      // icon: 'sidebar-breadcrumb'
    },
    children: [
      {
        path: 'index',
        name: 'alarmMaterialsIndex',
        component: () => import('@/views/alarmCenter/alarmMaterials/index.vue'),
        meta: {
          title: '报警附近物资',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/alarmMaterials'
        }
      },
      {
        path: 'addAlarmMaterialsConfig',
        name: 'addAlarmMaterialsConfig',
        component: () => import('@/views/alarmCenter/alarmMaterials/addAlarmMaterialsConfig.vue'),
        meta: {
          title: '新增报警附近物资',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/alarmMaterials'
        }
      }
    ]
  },
]
