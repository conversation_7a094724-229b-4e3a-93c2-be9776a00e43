<template>
  <PageContainer>
    <div slot="header">
      <div class="searchForm">
        <div class="search-box">
          <el-input v-model="searchForm.drillName" clearable filterable placeholder="请输入计划名称"></el-input>
          <el-select v-model="searchForm.drillType" placeholder="请选择演练类型" class="ml-16">
            <el-option v-for="item in typeList" :key="item.id" :label="item.labelName" :value="item.id"> </el-option>
          </el-select>
          <el-input v-model="searchForm.headName" clearable filterable placeholder="请输入演练责任人姓名"></el-input>
          <el-date-picker v-model="time" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
            range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" class="ml-16">
          </el-date-picker>
          <div class="ml-16">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button type="primary" plain @click="reset">重置</el-button>
          </div>
        </div>
      </div>
    </div>
    <div slot="content" ref="contentRef" class="table-box">
      <div class="tableContainer">
        <el-button type="primary" style="margin-bottom: 12px" @click="handleListEvent('add')">新增计划</el-button>
        <el-button type="primary" :disabled="multipleSelection.length<1" @click="batchDelete">批量删除</el-button>
        <el-table v-loading="tableLoading" height="calc(100% - 96px)" :data="tableData" border stripe
          @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55">
          </el-table-column>
          <el-table-column label="演练计划名称" prop="drillName" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column label="演练类型" prop="drillTypeName" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column label="演练部门" prop="deptNames" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column label="演练责任人" prop="headNames" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column label="周期类型" prop="cycleType" show-overflow-tooltip align="center">
            <template slot-scope="scope">
              <span>{{scope.row? cycleTypeFn(scope.row):'' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="计划演练时间" prop="planStartTime" show-overflow-tooltip align="center">
            <template slot-scope="scope">
              <span
                v-if="scope.row.cycleType===0||scope.row.cycleType===5">{{ moment(scope.row.drillStartTime).format('YYYY-MM-DD') }}-{{moment(scope.row.drillEndTime).format('YYYY-MM-DD')}}</span>
              <span v-if="scope.row.cycleType===1">每周{{scope.row.startDate}}-每周{{scope.row.endDate}}</span>
              <span v-if="scope.row.cycleType===2">每月{{scope.row.startDate}}日-每月{{scope.row.endDate}}日</span>
              <span
                v-if="scope.row.cycleType===3||scope.row.cycleType===4">第{{scope.row.startMonth}}个月{{scope.row.startDate}}日-第{{scope.row.endMonth}}个月{{scope.row.endDate}}日</span>
            </template>
          </el-table-column>
          <el-table-column label="创建人" prop="createName" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column label="创建时间" prop="createTime" show-overflow-tooltip align="center">
            <template slot-scope="scope">
              <span>{{ moment(scope.row.createTime).format('YYYY-MM-DD') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="启用/停用" align="center">
            <template slot-scope="scope">
              <el-switch v-model="scope.row.planState" :active-value="1" :inactive-value="0" active-color="#3562DB"
                inactive-color="#CCCED3 " @change="changeStatus(scope.row)">
              </el-switch>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
              <span :class="['btnState', scope.row.planState == 1 ? 'btnState1' : 'btnState2']"
                @click="handleListEvent('edit', scope.row)">
                编辑
              </span>
              <span :class="['btnState', scope.row.planState == 1 ? 'btnState1' : 'btnState2']"
                @click="handleListEvent('del',scope.row)">
                删除
              </span>
              <el-button type="text" :disabled="scope.row.planState===1"></el-button>
              <el-button type="text" :disabled="scope.row.planState===1"></el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination :current-page="pagination.page" :page-sizes="[15, 30, 50, 100]" :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper" :total="pagination.total" @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
        </el-pagination>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import moment from 'moment'
export default {
  name: 'exercisePlan',
  data() {
    return {
      moment,
      tableLoading: false,
      tableData: [],
      searchForm: {
        drillName: '',
        drillType: '',
        headName: ''
      },
      time: [],
      typeList: [],
      pagination: {
        pageSize: 15,
        page: 1,
        total: 0
      },
      multipleSelection: [],
      cycleTypeList: [
        {
          cycleType: 0,
          label: '单次'
        },
        {
          cycleType: 1,
          label: '每周'
        },
        {
          cycleType: 2,
          label: '每月'
        },
        {
          cycleType: 3,
          label: '季度'
        },
        {
          cycleType: 4,
          label: '半年'
        },
        {
          cycleType: 5,
          label: '全年'
        }
      ]
    }
  },
  mounted() {
    this.getTypeList()
    this.getDataList()
  },
  methods: {
    // 获取演练类型列表
    getTypeList() {
      let data = {
        page: {
          current: 1,
          size: 9999
        }
      }
      this.$api
        .getPreplanDrillTypeData(data)
        .then((res) => {
          this.typeList = res.data ? res.data.list : []
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },

    // 列表数据
    getDataList() {
      this.tableLoading = true
      let data = {
        ...this.searchForm,
        createStartTime: this.time[0] ? moment(this.time[0]).format('YYYY-MM-DD') : '',
        createEndTime: this.time[1] ? moment(this.time[1]).format('YYYY-MM-DD') : '',
        page: {
          current: this.pagination.page,
          size: this.pagination.pageSize
        }
      }
      this.$api
        .getPreplanDrillPlanData(data)
        .then((res) => {
          this.tableLoading = false
          if (res.code == 200) {
            this.tableData = res.data ? res.data.list : []
            this.pagination.total = res.data ? res.data.totalCount : 0
          } else if (res.message) {
            this.tableData = []
            this.pagination.total = 0
            this.$message.error(res.message)
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleCurrentChange(val) {
      this.pagination.page = val
      this.getDataList()
    },
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.pagination.page = 1
      this.getDataList()
    },
    search() {
      this.pagination.page = 1
      this.getDataList()
    },
    reset() {
      this.searchForm = {
        drillName: '',
        drillType: '',
        person: ''
      }
      this.time = []
      this.pagination.page = 1
      this.pagination.total = 0
      this.getDataList()
    },
    // 周期类型
    cycleTypeFn(row) {
      const item = this.cycleTypeList.filter((i) => i.cycleType === row.cycleType)
      if (item)
        return item[0].label
    },
    handleListEvent(type, row) {
      this.condition = 'dept'
      if (type == 'add') {
        this.$router.push('/exerciseManage/exercisePlan/addExercisePlan')
      }
      if (type == 'edit') {
        this.$router.push({
          path: '/exerciseManage/exercisePlan/addExercisePlan',
          query: {
            planId: row.id
          }
        })
      }
      if (type == 'del') {
        this.$confirm('确认要删除当前计划吗?删除后将无法恢复。', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let ids = []
          ids.push(row.id)
          this.$api.deletePreplanDrillPlanData({ ids: ids }, { 'operation-type': 3, 'operation-id': row.id, 'operation-name': row.drillName }).then((res) => {
            if (res.code === '200') {
              this.$message.success('删除成功')
              this.getDataList()
            } else {
              this.$message.warning(res.msg)
            }
          })
        })
      }
    },
    // 批量删除
    batchDelete() {
      if (this.multipleSelection.some((e) => e.planState === 1)) {
        return this.$message({
          message: '启用的计划不支持删除！',
          type: 'warning'
        })
      }
      this.$confirm('是否批量删除计划?删除后将无法恢复。', '提示', {
        cancelButtonClass: 'el-button--primary is-plain',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let ids = []
        this.multipleSelection.forEach(item => {
          ids.push(item.id)
        })
        this.$api.deletePreplanDrillPlanData({ ids: ids }, { 'operation-type': 3 }).then((res) => {
          if (res.code === '200') {
            this.$message.success('删除成功')
            this.getDataList()
          } else {
            this.$message.warning(res.msg)
          }
        })
      })
    },
    // 启用停用
    changeStatus(val) {
      if (val.planState === 0) {
        let data = {
          id: val.id,
          planState: val.planState
        }
        this.$api.viewPreplanDrillPlanStateData(data).then((res) => {
          if (res.code === '200') {
            if (res.data) {
              this.$confirm('当前计划包含下发过演练通知且未执行的任务，是否继续停用?', '提示', {
                cancelButtonClass: 'el-button--primary is-plain',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                this.getStateVariation(val.id, 0)
              }).catch(() => {
                this.getDataList()
              })
            } else {
              this.getStateVariation(val.id, 0)
            }
          } else {
            this.$message.warning(res.msg)
          }
        })
      } else {
        this.getStateVariation(val.id, 1)
      }
    },
    getStateVariation(id, planState) {
      let data = {
        id: id,
        planState: planState
      }
      this.$api.updatePreplanDrillPlanStateData(data).then((res) => {
        if (res.code === '200') {
          this.$message.success(res.msg)
          this.getDataList()
        } else {
          this.$message.warning(res.msg)
        }
      })
    }

  }
}
</script>
<style lang="scss" scoped>
.searchForm {
  display: flex;
  height: 80px;
  padding: 0 16px;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .el-input {
    width: 200px;
    margin-left: 16px;
  }

  > div {
    margin-right: 20px;
  }
}

.search-box {
  display: flex;
  align-items: center;
}

.container-content > div {
  height: 100%;
  margin-top: 16px;
}
.el-table {
  margin-bottom: 12px;
}

::v-deep .el-table__row:hover {
  cursor: pointer;
}
.table-box {
  background-color: #fff;
  height: 100%;
  padding: 16px;
  .tableContainer {
    height: 100%;
    .el-table {
      height: calc(100% - 96px) !important;
    }
  }
}
.ml-16 {
  margin-left: 16px;
}
.btnState {
  margin: 0 4px;
  color: #3562db;
}
.btnState1 {
  color: #ccced3 !important;
  cursor: not-allowed;
  pointer-events: none;
}
</style>
