<template>
  <PageContainer>
    <div slot="header" class="control-btn-header">
      <div class="search-from">
        <el-input v-model.trim="searchFrom.inspectionName" placeholder="签到人员" clearable style="width: 200px"></el-input>
        <div style="display: inline-block">
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="searchForm">查询</el-button>
        </div>
      </div>
    </div>
    <div slot="content" class="table-content">
      <TablePage
        ref="table"
        v-loading="tableLoading"
        :showPage="true"
        :tableColumn="tableColumn"
        :data="tableData"
        height="calc(100% - 40px)"
        :pageData="pageData"
        @pagination="paginationChange"
      />
    </div>
  </PageContainer>
</template>
<script>
export default {
  name: 'dutyRecord',
  data() {
    return {
      formInline: {
        inspectionName: null
      },
      tableLoading: false,
      searchFrom: {
        inspectionName: '' // 监测项目全名
      },
      tableColumn: [
        {
          prop: '',
          label: '序号',
          formatter: (scope) => {
            return (this.pageData.page - 1) * this.pageData.pageSize + scope.$index + 1
          },
          width: 80
        },
        {
          prop: 'inspectionDate',
          label: '日期'
        },
        {
          prop: 'signShiftName',
          label: '班次名称'
        },
        {
          prop: 'signName',
          label: '签到人员'
        },
        {
          prop: 'inspectionName',
          label: '值班人员'
        },
        {
          prop: 'dutySituation',
          label: '交接班情况'
        },
        {
          prop: 'remainingProblems',
          label: '遗留问题'
        },
        {
          prop: 'signDate',
          label: '签到时间'
        }
      ],
      tableData: [],
      pageData: {
        page: 1,
        pageSize: 15,
        total: 0
      }
    }
  },
  watch: {},
  mounted() {
    this.getApplicationList()
  },
  methods: {
    // 查询
    searchForm() {
      this.getApplicationList()
    },
    // 重置查询
    resetForm() {
      Object.assign(this.$data.searchFrom, this.$options.data().searchFrom)
      this.searchForm()
    },
    // 获取应用列表
    getApplicationList() {
      let param = {
        ...this.searchFrom,
        pageSize: this.pageData.pageSize,
        page: this.pageData.page
      }
      this.tableLoading = true
      this.$api
        .getSignListForPage(param)
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.list
            this.pageData.total = res.data.totalCount
          }
          this.tableLoading = false
        })
        .catch(() => {
          this.tableLoading = false
        })
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getApplicationList()
    }
  }
}
</script>
<style lang="scss" scoped>
.hide {
  ::v-deep .el-upload--picture-card {
    display: none;
  }
}
.control-btn-header {
  padding: 10px !important;
  .search-from {
    & > div {
      margin-right: 10px;
    }
  }
}
.table-content {
  margin-top: 15px;
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 15px);
  padding: 10px;
}
</style>
