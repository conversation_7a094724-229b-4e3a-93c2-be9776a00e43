<template>
  <div class="main">
    <el-dialog
      v-dialogDrag
      custom-class="mainDialog main"
      append-to-body
      :visible.sync="changeNoticePeopleShow"
      :close-on-click-modal="false"
      :before-close="closeDialog"
      class="personDialog"
    >
      <template slot="title">
        <span class="dialog-title">应急联系人选择</span>
      </template>
      <div class="dialog-content" style="width: 100%;">
        <div style="margin-bottom: 20px;">
          <el-input
            v-model="searchFrom.name"
            style="width: 180px; margin-right: 20px;"
            placeholder="请输入联系人"
            maxlength="25"
            onkeyup="if(value.length>25)value=value.slice(0,25)"
          ></el-input>
          <el-input
            v-model="searchFrom.phone"
            style="width: 180px; margin-right: 20px;"
            placeholder="请输入联系电话"
            maxlength="25"
            onkeyup="if(value.length>25)value=value.slice(0,25)"
          ></el-input>
          <el-button class="sino-button-sure" style="margin-left: 30px;" @click="_searchByCondition">查询</el-button>
          <el-button class="sino-button-sure" @click="_resetCondition">重置</el-button>
        </div>
        <el-table
          v-loading="tableLoading"
          :data="tableData"
          height="300"
          :cell-style="$tools.setCell(3)"
          :header-cell-style="$tools.setHeaderCell(3)"
          style="width: 100%;"
          element-loading-background="rgba(0, 0, 0, 0.2)"
          @selection-change="selectionChange"
        >
          <el-table-column fixed align="center" type="selection" width="45px"></el-table-column>
          <el-table-column fixed type="index" width="70" label="序号"> </el-table-column>
          <el-table-column fixed prop="name" show-overflow-tooltip label="联系人"></el-table-column>
          <el-table-column fixed prop="phone" show-overflow-tooltip label="联系电话"></el-table-column>
          <el-table-column fixed prop="deptName" show-overflow-tooltip label="所属科室"></el-table-column>
          <el-table-column fixed prop="type" show-overflow-tooltip label="人员属性">
            <template slot-scope="scope">
              <span>{{ scope.row.type === '2' ? '外委员' : '院内' }}</span>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :current-page="pageNo"
          :page-sizes="[15, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          class="pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="sino-button-sure" @click="closeDialog">取 消</el-button>
        <el-button class="sino-button-sure" type="primary" @click="savePeople">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: 'noticePeople',
  props: {
    changeNoticePeopleShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tableData: [],
      tableLoading: false,
      selectionParams: {},
      searchFrom: {
        name: '',
        phone: ''
      },
      pageNo: 1,
      pageSize: 15,
      total: 0
    }
  },
  watch: {},
  mounted() {
    this.getEmergencyBook()
  },
  methods: {
    getEmergencyBook() {
      this.tableLoading = true
      const params = {
        name: this.searchFrom.name,
        phone: this.searchFrom.phone,
        pageNo: this.pageNo,
        pageSize: this.pageSize
      }
      getEmergencyBook(params).then((res) => {
        console.log(res)
        const data = res.data
        this.tableData = data.rows
        this.total = data.total
        this.tableLoading = false
      })
    },
    selectionChange(selection) {
      if (selection.length) {
        this.selectionParams = selection
      } else {
        this.selectionParams = {}
      }
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getEmergencyBook()
    },
    handleCurrentChange(val) {
      this.pageNo = val
      this.getEmergencyBook()
    },
    // 取消按钮
    closeDialog() {
      this.$emit('closeDialog')
    },
    // 确认按钮
    savePeople() {
      this.$emit('peopleSure', this.selectionParams)
    },
    _searchByCondition() {
      this.pageNo = 1
      this.getEmergencyBook()
    },
    _resetCondition() {
      this.pageSize = 15
      this.searchFrom = {
        name: '',
        phone: ''
      }
      this._searchByCondition()
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
::v-deep .mainDialog {
  background: #031553;
  width: 45%;

  .el-dialog__body {
    padding: 10px 20px;
  }

  .el-dialog__footer {
    padding-right: 30px;
  }

  .dialog-title {
    color: #fff;
    font-family: PingFangSC-Medium, "PingFang SC";
  }

  .dialog-title::before {
    content: "";
    display: inline-block;
    width: 2px;
    border-radius: 1px;
    height: 13px;
    background: #ffe3a6;
    margin-right: 10px;
  }
}

::v-deep .el-table-column--selection .cell {
  padding-left: 0.875rem;
  padding-right: 0.875rem;
  text-overflow: initial;
}

.redColor {
  color: red;
}
</style>
