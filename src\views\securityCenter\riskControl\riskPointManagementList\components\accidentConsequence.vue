<template>
  <div>
    <el-dialog title="事故后果选择" :visible.sync="dialogVisible" :before-close="closeDialog" custom-class="model-dialog" class="accidentConsequence">
      <div class="content" style="width: 100%; background-color: #fff; padding: 10px;">
        <!-- <div style="margin-bottom:20px;">
           <div style="line-height:30px;display: inline-block;">已选择：</div>
           <span style="width:90%;display: inline-block;max-height:28px;overflow:auto; line-height: initial;">{{checkName.join('，')}}</span>
         </div> -->
        <div style="height: 35px; overflow: auto; margin-bottom: 10px;">
          <span>已选择：</span>
          <span style="color: #5188fc;">{{ checkName.join('、') }}</span>
        </div>
        <div>
          <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
            <el-tab-pane v-for="(item, p) of riskLevelList" :key="p" :label="item.dictLabel" :name="item.dictValue">
              <template>
                <div v-loading="loading">
                  <div v-if="tableData.length" class="img-list">
                    <div v-for="(i, index) in tableData" :key="index" class="img-info">
                      <div style="border: 1px solid #ddd;">
                        <img style="width: 50px; height: 60px;" :src="'data:image/png;base64' + i.attachmentUrl" alt="" />
                      </div>
                      <div style="margin-top: 5px;">
                        <el-checkbox-group v-model="checked" :title="i.dictLabel" @change="checkboxChange">
                          <el-checkbox style="overflow: hidden; width: 80px;" :label="i.id">{{ i.dictLabel }}</el-checkbox>
                        </el-checkbox-group>
                      </div>
                    </div>
                  </div>
                  <div v-else class="img-list">
                    <div style="margin: auto; margin-top: 20px; text-align: center;">
                      <img width="180" src="@/assets/images/null.png" alt="" />
                      <div style="color: #909399;">暂无标志</div>
                    </div>
                  </div>
                </div>
              </template>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button class="sino-button-cancel" @click="closeDialog">取 消</el-button>
        <el-button class="sino-button-sure" @click="complete">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script type="text/ecmascript-6">
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String
    },
    type: {
      type: String
    },
    accidentTypeList: {
      type: Array
    }
  },
  data() {
    return {
      loading: false,
      consequenceName: '',
      checked: [],
      checkName: [],
      activeName: '1',
      riskLevelList: [],
      tableData: [],
      allTableData: []// 全部标签
    }
  },
  watch: {
    accidentTypeList() {
      this.getaccidentTypeList()
    }
  },
  mounted() {
    this.getDictValue()
    this.getAllTableData()
    this.$nextTick(() => {
      this.getaccidentTypeList()
    })
  },
  methods: {
    // 点击确定
    complete() {
      this.$emit('complete')
    },
    // 获取类型
    getDictValue() {
      this.$api.ipsmGetDictValue({
        dictType: 'accident_type',
        isShowParent: 0
      }).then(res => {
        if (res.code == 200) {
          this.riskLevelList = res.data
          this.activeName = this.riskLevelList[0].dictValue
          this.getTableData()
        }
      })
    },
    // 获取全部标签
    getAllTableData() {
      this.$api.ipsmGetAccidentTypeList({}).then((res) => {
        if (res.code == 200) {
          this.allTableData = res.data.list
        }
      })
    },
    // 获取标签
    getTableData() {
      this.loading = true
      let data = {
        dictType: this.activeName
      }
      this.$api.ipsmGetAccidentTypeList(data).then((res) => {
        this.loading = false
        if (res.code == 200) {
          this.tableData = res.data.list
        }
      })
    },
    // 选中标签
    checkboxChange(val) {
      this.checkName = []
      val.map(item => {
        this.allTableData.forEach(p => {
          if (item == p.id) {
            this.checkName.push(p.dictLabel)
          }
        })
      })
    },
    // 回显标签
    getaccidentTypeList() {
      if (this.accidentTypeList) {
        let id = []
        let name = []
        this.accidentTypeList.map(i => {
          id.push(i.id)
          name.push(i.dictLabel)
        })
        this.checked = id
        this.checkName = name
      }
    },
    handleClick(tab, event) {
      this.getTableData()
    },
    closeDialog() {
      this.$emit('closeDialog')
    }
  }
}
</script>
<style lang="scss" scoped>
.accidentConsequence {
  .el-tabs__content {
    height: 275px !important;
    overflow: auto;

    .img-list {
      justify-content: flex-start;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;

      .img-info {
        width: 90px;
        text-align: center;
        border: 1px solid #ddd;
        margin: 10px;
        padding: 5px;
        border-radius: 5px;
      }
    }
  }
}
</style>
