<template>
  <div slot="content">
    <div class="aside">
      <div v-for="item in reportType" :key="item.value" :class="['items', activeVal == item.value ? 'active' : '']" @click="changeType(item.value)">{{ item.name }}</div>
    </div>
    <div class="main-box">
      <report-component ref="reportComponent" :reportTypePara="activeVal"></report-component>
    </div>
  </div>
</template>
<script>
export default {
  name: 'StatisticalAnalysis',
  components: {
    reportComponent: () => import('./components/report.vue')
  },
  beforeRouteEnter(to, from, next) {
    let names = []
    to.matched.map((v, i) => {
      if (i > 0) {
        v.components.default.name && names.push(v.components.default.name)
      }
    })
    // 进入页面时，先将当前页面的 name 信息存入 keep-alive 全局状态
    setTimeout(() => {
      next((vm) => {
        vm.$store.commit('keepAlive/add', names)
      })
    }, 0)
  },
  data() {
    return {
      activeVal: '0',
      reportType: [
        {
          name: '科室维修成本报表',
          value: '0'
        },
        {
          name: '维修区域报表',
          value: '1'
        },
        {
          name: '耗材消耗统计',
          value: '2'
        },
        {
          name: '工人任务量统计',
          value: '3'
        },
        {
          name: '班组工作量报表',
          value: '4'
        },
        {
          name: '维修事项报表',
          value: '5'
        },
        {
          name: '科室报修量',
          value: '6'
        },
        {
          name: '维修综合报表',
          value: '7'
        }
      ]
    }
  },
  methods: {
    changeType(val) {
      this.$nextTick(() => {
        this.$refs.reportComponent.changeType(val)
      })
      this.activeVal = val
    }
  }
}
</script>
<style lang="scss" scoped>
.aside {
  width: 195px;
  height: 92vh;
  background: #fff;
  margin: 16px 0 0 8px;
  border-radius: 10px;
  padding: 8px 5px;
  box-sizing: border-box;
}

.items {
  height: 40px;
  line-height: 40px;
  text-align: center;
  cursor: pointer;
  border-radius: 3px;
}

.items:hover {
  background-color: #f0f2f5;
}

.active {
  color: #5482ee;
}

.main-box {
  width: 85%;
  height: 92vh;
  background: #fff;
  margin: 16px 8px 0 16px;
  border-radius: 10px;
  padding: 8px 5px;
  box-sizing: border-box;
}

.main > div {
  display: flex;
}
</style>
