<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="warehouse-content-title">
        <i class="el-icon-arrow-left" @click="$router.go(-1)"></i> {{ warehouseType === 'add' ? '新增仓库' : warehouseType === 'edit' ? '编辑仓库' : '仓库详情' }}
      </div>
      <div class="content_box">
        <el-form ref="formRef" :model="formModel" :rules="rules" label-width="140px">
          <div class="toptip">
            <span class="green_line"></span>
            基本信息
          </div>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="仓库名称" prop="warehouseName">
                <el-input v-if="warehouseType !== 'view'" v-model="formModel.warehouseName" placeholder="请输入" maxlength="50"> </el-input>
                <span v-else class="detailContent">{{ formModel.warehouseName || '--' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="编码" prop="warehouseCode">
                <el-input v-if="warehouseType !== 'view'" v-model="formModel.warehouseCode" placeholder="请输入" maxlength="50"> </el-input>
                <span v-else class="detailContent">{{ formModel.warehouseCode || '--' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="上级库房" prop="parentId">
                <el-select v-if="warehouseType !== 'view'" v-model="formModel.parentId" filterable placeholder="请选择" @change="parentChangeWarehouse">
                  <el-option v-for="item in storeOptions" :key="item.id" :label="item.warehouseName" :value="item.id"></el-option>
                </el-select>
                <span v-else class="detailContent">{{ formModel.parentName || '--' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="所属院区" prop="gridCode">
                <el-cascader
                  v-if="warehouseType !== 'view'"
                  ref="gridCodeCascader"
                  v-model="formModel.gridCode"
                  :props="gridCodePropsType"
                  :options="gridCodeList"
                  placeholder="请选择院区"
                  @change="gridCodeChange"
                />
                <el-tooltip v-else class="item" effect="dark" :content="formModel.gridName" placement="top" :disabled="formModel.gridName.length < 20">
                  <span class="detailContent">{{ formModel.gridName || '--' }}</span>
                </el-tooltip>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="管理单位" prop="manageUnitId">
                <el-select v-if="warehouseType !== 'view'" v-model="formModel.manageUnitId" filterable placeholder="请选择" @change="manageUnitChange">
                  <el-option v-for="item in managementUnitOptions" :key="item.umId" :label="item.unitComName" :value="item.umId"></el-option>
                </el-select>
                <span v-else class="detailContent">{{ formModel.manageUnitName || '--' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="管理单位类型" prop="warehouseUnitType">
                <el-select v-if="warehouseType !== 'view'" v-model="formModel.warehouseUnitType" filterable placeholder="请选择" @change="warehouseUnitTypeChange">
                  <el-option v-for="item in unitTypeOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"></el-option>
                </el-select>
                <span v-else class="detailContent">{{ formModel.warehouseUnitName || '--' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="仓库地址" prop="warehouseAddress">
                <el-input v-if="warehouseType !== 'view'" v-model="formModel.warehouseAddress" placeholder="请输入"> </el-input>
                <span v-else class="detailContent">{{ formModel.warehouseAddress || '--' }}</span>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="8">
              <el-form-item label="责任人" prop="manageName">
                <el-input v-if="warehouseType !== 'view'" v-model="formModel.manageName" placeholder="请选择" @focus="selectPerson('duty')"> </el-input>
                <span v-else class="detailContent">{{ formModel.manageName || '--' }}</span>
              </el-form-item>
            </el-col> -->
            <!-- <el-col :span="8">
              <el-form-item label="联系方式" prop="managePhone">
                <el-input v-if="warehouseType !== 'view'" v-model="formModel.managePhone" placeholder="请输入"> </el-input>
                <span v-else class="detailContent">{{ formModel.managePhone || '--' }}</span>
              </el-form-item>
            </el-col> -->
          </el-row>
          <template v-if="warehouseType !== 'view'">
            <el-row :gutter="20" v-for="(item, index) in formModel.responsiblePersonInfoList" :key="item.id">
              <el-col :span="8">
                <el-form-item label="责任人" required>
                  <el-input v-model="item.name" placeholder="请选择" @focus="selectPerson('duty', index)"> </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="联系电话" required>
                  <el-input v-model="item.phone" placeholder="请输入" maxlength="11" disabled oninput="value=value.replace(/[^\d.]/g,'')"> </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <div class="operation-btn">
                  <span class="addBtn" @click="addDutyRow(index)">添加责任人</span>
                  <span v-if="index > 0" class="deleteBtn" @click="removeDutyRow(index)">删除</span>
                </div>
              </el-col>
            </el-row>
          </template>
          <template v-else>
            <el-row :gutter="20" v-for="(item, index) in formModel.responsiblePersonInfoList" :key="index">
              <el-col :span="8">
                <el-form-item label="责任人" prop="name">
                  <span class="detailContent">{{ item.name || '--' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="联系电话" prop="phone">
                  <span class="detailContent">{{ item.phone || '--' }}</span>
                </el-form-item>
              </el-col>
            </el-row>
          </template>
          <template v-if="warehouseType !== 'view'">
            <el-row :gutter="20" v-for="(item, index) in formModel.linkInfoList" :key="index">
              <el-col :span="8">
                <el-form-item label="联系人" prop="linkPerson">
                  <el-input v-model="item.name" placeholder="请选择" @focus="selectPerson('link', index)"> </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="联系电话" prop="linkPhone">
                  <el-input v-model="item.phone" placeholder="请输入" maxlength="11" disabled oninput="value=value.replace(/[^\d.]/g,'')"> </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <div class="operation-btn">
                  <span class="addBtn" @click="addRow(index)">添加联系人</span>
                  <span v-if="index > 0" class="deleteBtn" @click="removeRow(index)">删除</span>
                </div>
              </el-col>
            </el-row>
          </template>
          <template v-else>
            <el-row :gutter="20" v-for="(item, index) in formModel.linkInfoList" :key="index">
              <el-col :span="8">
                <el-form-item label="联系人" prop="name">
                  <span class="detailContent">{{ item.name || '--' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="联系电话" prop="phone">
                  <span class="detailContent">{{ item.phone || '--' }}</span>
                </el-form-item>
              </el-col>
            </el-row>
          </template>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="状态" prop="status">
                <el-select v-if="warehouseType !== 'view'" v-model="formModel.status" placeholder="请选择">
                  <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                </el-select>
                <span v-else class="detailContent">{{ formModel.status == '0' ? '启用' : '禁用' }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="16">
              <el-form-item label="说明" prop="remarks">
                <el-input v-if="warehouseType !== 'view'" v-model.trim="formModel.remarks" maxlength="500" show-word-limit type="textarea" placeholder="请输入"></el-input>
                <span v-else class="detailContent">{{ formModel.remarks || '--' }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-col :span="24">
            <el-form-item label="关联科室">
              <!-- <el-select v-if="warehouseType !== 'view'" v-model="formModel.officesCode" placeholder="请选择科室" clearable filterable @change="deptChange" multiple collapse-tags>
                <el-option v-for="item in deptOptions" :key="item.id" :label="item.deptName" :value="item.id"></el-option>
              </el-select>
              <span v-else class="detailContent">{{ formModel.officesName || '--' }}</span> -->
              <el-button type="primary" size="small" v-if="warehouseType !== 'view'" @click="onDeptOperate">去选择</el-button>
              <div class="row-table">
                <el-table height="100%" :data="deptItems" :border="true" stripe table-layout="auto" class="tableAuto" row-key="id">
                  <el-table-column type="index" show-overflow-tooltip label="排序" width="100" :align="'center'">
                    <template slot-scope="scope">
                      <span>{{ (deptCurrentPage - 1) * deptPageSize + scope.$index + 1 }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="deptName" show-overflow-tooltip label="科室名称" :align="'center'"></el-table-column>
                  <el-table-column show-overflow-tooltip width="220" label="操作" :align="'center'" v-if="warehouseType !== 'view'">
                    <template slot-scope="scope">
                      <el-button type="text" size="small" @click="onDeptCancel(scope.row.id)">撤销</el-button>
                      <el-button type="text" size="small" :disabled="scope.row.id === depTableData[0].id" @click="onDeptMoveUp(scope.row.id)">上移</el-button>
                      <el-button type="text" size="small" :disabled="scope.row.id === depTableData[depTableData.length - 1].id" @click="onDeptMoveDown(scope.row.id)"
                        >下移</el-button
                      >
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div class="row-pagination">
                <el-pagination
                  :current-page="deptCurrentPage"
                  :page-sizes="[5, 10, 15, 20]"
                  :page-size="deptPageSize"
                  :layout="'total, sizes, prev, pager, next, jumper'"
                  :total="depTableData.length"
                  @size-change="deptPaginationSizeChange"
                  @current-change="deptPaginationCurrentChange"
                >
                </el-pagination>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="关联危化品">
              <el-button type="primary" size="small" v-if="warehouseType !== 'view'" @click="onWhpOperate">去选择</el-button>
              <div class="row-table">
                <el-table height="100%" :data="whpItems" :border="true" stripe table-layout="auto" class="tableAuto" row-key="id">
                  <el-table-column type="index" show-overflow-tooltip label="排序" width="100" :align="'center'">
                    <template slot-scope="scope">
                      <span>{{ (whpCurrentPage - 1) * whpPageSize + scope.$index + 1 }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="materialName" show-overflow-tooltip label="危化品名称" :align="'center'"></el-table-column>
                  <el-table-column prop="model" show-overflow-tooltip label="规格型号" :align="'center'"></el-table-column>
                  <el-table-column prop="basicUnitName" show-overflow-tooltip label="计量单位" :align="'center'"></el-table-column>
                  <el-table-column prop="materialTypeName" show-overflow-tooltip label="危化品分类" :align="'center'"></el-table-column>
                  <el-table-column prop="supplierName" show-overflow-tooltip label="供应商" :align="'center'"></el-table-column>
                  <el-table-column prop="" show-overflow-tooltip width="220" label="操作" :align="'center'" v-if="warehouseType !== 'view'">
                    <template slot-scope="scope">
                      <!-- <el-button type="text" size="small" @click="onWhpOperate">查看</el-button> -->
                      <el-button type="text" size="small" @click="onWhpCancel(scope.row.id)">撤销</el-button>
                      <el-button type="text" size="small" :disabled="scope.row.id === whpTableData[0].id" @click="onWhpMoveUp(scope.row.id)">上移</el-button>
                      <el-button type="text" size="small" :disabled="scope.row.id === whpTableData[whpTableData.length - 1].id" @click="onWhpMoveDown(scope.row.id)"
                        >下移</el-button
                      >
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div class="row-pagination">
                <el-pagination
                  :current-page="whpCurrentPage"
                  :page-sizes="[5, 10, 15, 20]"
                  :page-size="whpPageSize"
                  :layout="'total, sizes, prev, pager, next, jumper'"
                  :total="whpTableData.length"
                  @size-change="whpPaginationSizeChange"
                  @current-change="whpPaginationCurrentChange"
                >
                </el-pagination>
              </div>
            </el-form-item>
          </el-col>
        </el-form>
      </div>
      <!-- 人员弹窗 -->
      <selectPersDialog
        v-if="isSelectPers"
        selectMode="1"
        :visible="isSelectPers"
        @updateVisible="
          () => {
            isSelectPers = false
          }
        "
        @advancedSearchFn="selectPersChange"
      />
      <!-- 危化品弹窗 -->
      <selectHazardousChemicalDialog
        v-if="sectionDialogShow"
        :sectionDialogShow="sectionDialogShow"
        @submitSectionDialog="submitSectionDialog"
        :warehouseType="'1'"
        @closeSectionDialog="closeSectionDialog"
        :selectHcsList="selectHcsList"
      />
      <!-- 关联科室弹窗 -->
      <selectDeptDialog v-if="deptDialogShow" :sectionDialogShow="deptDialogShow" @submitSectionDialog="submitDeptDialog" @closeSectionDialog="closeDeptSectionDialog" />
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">取消</el-button>
      <el-button type="primary" :loading="formLoading" @click="submitForm()" v-if="warehouseType !== 'view'">保存</el-button>
    </div>
  </PageContainer>
</template>
<script>
import { transData } from '@/util'
export default {
  name: 'addWarehouse',
  components: {
    selectPersDialog: () => import('@/views/alarmCenter/linkageConfiguration/components/dialogConfig.vue'),
    selectHazardousChemicalDialog: () => import('../hazardousChemicalGoodsManage/requisitionRegistrationManage/components/selectHazardousChemical.vue'),
    selectDeptDialog: () => import('@/views/operationPort/exerciseManage/exercisePlan/components/SelectDept.vue')
  },
  data() {
    var managePhoneValidate = (rule, value, callback) => {
      const regex = /^[\d,]+$/
      if (!regex.test(value)) {
        callback(new Error('格式不正确，只能输入数字多个数字用逗号分隔'))
      } else {
        let arr = value.split(',')
        arr.forEach((item) => {
          if (!(item.length == 11 || item.length == 12)) {
            return callback(new Error('格式不正确，每个联系方式长度为11位'))
          }
        })
        callback()
      }
    }
    return {
      isSelectPers: false, // 选择人员
      sectionDialogShow: false, // 选择危化品
      deptDialogShow: false, // 选择部门
      selectType: '', //选择人员类型
      // 正常表单
      formModel: {
        id: '',
        warehouseName: '', // 仓库名称
        warehouseCode: '', // 仓库编码
        parentId: '', //上级库房
        parentName: '', //上级库房name
        gridCode: [], //院区code
        gridName: '', //院区name
        officesCode: [], //关联科室code
        officesName: '', //关联科室名称
        manageUnitId: '', //管理单位id
        manageUnitName: '', //管理单位名称
        warehouseUnitType: '', //管理单位类型
        warehouseUnitName: '', //管理单位类型name
        warehouseAddress: '', //仓库地址
        manageName: '', //责任人
        managePhone: '', //联系方式
        status: '', //状态
        remarks: '', // 说明
        linkInfoList: [
          //联系信息
          {
            name: '',
            phone: ''
          }
        ],
        responsiblePersonInfoList: [
          //责任人信息
          {
            name: '',
            phone: '',
            id: '123654879'
          }
        ]
      },
      rules: {
        warehouseName: [{ required: true, message: '请输入仓库名称', trigger: 'blur' }],
        warehouseCode: [{ required: true, message: '请输入仓库编码', trigger: 'blur' }],
        manageName: [{ required: true, message: '请选择责任人', trigger: 'change' }],
        gridCode: [{ required: true, message: '请选择所属院区', trigger: 'change' }],
        managePhone: [{ required: true, validator: managePhoneValidate, trigger: 'change' }],
        status: [{ required: true, message: '请选择状态', trigger: 'change' }]
      },
      //状态下拉
      statusOptions: [
        {
          label: '启用',
          value: '0'
        },
        {
          label: '禁用',
          value: '1'
        }
      ],
      gridCodePropsType: {
        children: 'children',
        label: 'ssmName',
        value: 'id',
        checkStrictly: true,
        multiple: false
      },
      storeOptions: [], //上级库房
      gridCodeList: [], //院区
      gridCodeAllList: [],
      managementUnitOptions: [], //管理单位
      unitTypeOptions: [], //管理单位类型
      deptOptions: [], //科室
      warehouseId: '',
      warehouseType: '', //add新增 edit编辑 view详情
      linkInfoIndex: 0, //联系人索引
      formLoading: false,
      depTableData: [], // 关联科室数据
      whpTableData: [], // 危化品数据
      selectHcsList: [], //选中危化品数据
      whpCurrentPage: 1,
      whpPageSize: 5,
      deptCurrentPage: 1,
      deptPageSize: 5
    }
  },
  computed: {
    whpItems() {
      if (this.whpTableData.length) {
        const start = (this.whpCurrentPage - 1) * this.whpPageSize
        const end = start + this.whpPageSize
        return this.whpTableData.slice(start, end)
      } else {
        return []
      }
    },
    deptItems() {
      if (this.depTableData.length) {
        const start = (this.deptCurrentPage - 1) * this.deptPageSize
        const end = start + this.deptPageSize
        return this.depTableData.slice(start, end)
      } else {
        return []
      }
    }
  },
  mounted() {
    this.init()
    this.warehouseType = this.$route.query.type
    if (this.$route.query.warehouseId) {
      this.warehouseId = this.$route.query.warehouseId
      this.getWarehouseInfo()
    }
  },
  methods: {
    // 关联科室撤销
    onDeptCancel(id) {
      this.depTableData = this.depTableData.filter((item) => item.id !== id)
      // 如果删除后当前页为空，自动跳到前一页
      if (this.deptItems.length === 0 && this.deptCurrentPage > 1) {
        this.deptCurrentPage--
      }
    },
    // 关联科室上移
    onDeptMoveUp(id) {
      const index = this.depTableData.findIndex((item) => item.id === id)
      if (index > 0) {
        const temp = this.depTableData[index]
        this.$set(this.depTableData, index, this.depTableData[index - 1])
        this.$set(this.depTableData, index - 1, temp)
      }
    },
    // 关联科室下移
    onDeptMoveDown(id) {
      const index = this.depTableData.findIndex((item) => item.id === id)
      if (index < this.depTableData.length - 1) {
        const temp = this.depTableData[index]
        this.$set(this.depTableData, index, this.depTableData[index + 1])
        this.$set(this.depTableData, index + 1, temp)
      }
    },
    // 关联科室table条数变化事件
    deptPaginationSizeChange(size) {
      this.deptPageSize = size
    },
    // 关联科室table页码变化事件
    deptPaginationCurrentChange(current) {
      this.deptCurrentPage = current
    },
    // 危化品撤销
    onWhpCancel(id) {
      this.whpTableData = this.whpTableData.filter((item) => item.id !== id)
      // 如果删除后当前页为空，自动跳到前一页
      if (this.whpItems.length === 0 && this.whpCurrentPage > 1) {
        this.whpCurrentPage--
      }
    },
    // 危化品上移
    onWhpMoveUp(id) {
      const index = this.whpTableData.findIndex((item) => item.id === id)
      if (index > 0) {
        const temp = this.whpTableData[index]
        this.$set(this.whpTableData, index, this.whpTableData[index - 1])
        this.$set(this.whpTableData, index - 1, temp)
      }
    },
    // 危化品下移
    onWhpMoveDown(id) {
      const index = this.whpTableData.findIndex((item) => item.id === id)
      if (index < this.whpTableData.length - 1) {
        const temp = this.whpTableData[index]
        this.$set(this.whpTableData, index, this.whpTableData[index + 1])
        this.$set(this.whpTableData, index + 1, temp)
      }
    },
    // 危化品table条数变化事件
    whpPaginationSizeChange(size) {
      this.whpPageSize = size
    },
    // 危化品table页码变化事件
    whpPaginationCurrentChange(current) {
      this.whpCurrentPage = current
    },
    // 弹窗打开事件
    onWhpOperate() {
      this.selectHcsList = this.whpTableData
      this.sectionDialogShow = true
    },
    // 危化品确认
    submitSectionDialog(list) {
      this.whpTableData = [...this.whpTableData, ...list]
      this.sectionDialogShow = false
    },
    // 关闭危化品选择弹窗
    closeSectionDialog() {
      this.sectionDialogShow = false
    },
    // 关联部门弹窗 打开事件
    onDeptOperate() {
      this.deptDialogShow = true
    },
    // 关联部门确认
    submitDeptDialog(list) {
      this.depTableData = this.removeSame(this.depTableData.concat(list))
      this.deptDialogShow = false
    },
    // 关联部门弹窗关闭
    closeDeptSectionDialog() {
      this.deptDialogShow = false
    },
    // 去重
    removeSame(array) {
      let newArray = []
      let seenIds = new Set()
      for (let item of array) {
        if (!seenIds.has(item.id)) {
          newArray.push(item)
          seenIds.add(item.id)
        }
      }
      return newArray
    },
    // 获取库房详情
    getWarehouseInfo() {
      const userInfo = this.$store.state.user.userInfo.user
      let params = {
        id: this.warehouseId,
        userId: userInfo.staffId,
        userName: userInfo.staffName
      }
      this.$api.getWarehouseById(params).then((res) => {
        if (res.code == '200') {
          this.formModel = { ...res.data, linkInfoList: [], responsiblePersonInfoList: [] }
          this.formModel.gridCode = res.data.gridCode.split(',')
          if (res.data.officesCode) {
            this.formModel.officesCode = res.data.officesCode.split(',')
          }
          if (res.data.contacts) {
            let nameArr = res.data.contacts.split(',')
            let phoneArr = res.data.contactsPhone.split(',')
            let linkPersonArr = nameArr.map((name, i) => ({ name, phone: phoneArr[i] }))
            this.formModel.linkInfoList = linkPersonArr
          } else {
            this.formModel.linkInfoList = [
              {
                name: '',
                phone: ''
              }
            ]
          }
          let { manageUserArrayStr, manageOfficeArrayStr, materialsDictArrayStr } = res.data
          this.formModel.responsiblePersonInfoList =
            manageUserArrayStr && JSON.parse(manageUserArrayStr).length
              ? JSON.parse(manageUserArrayStr).map((item) => {
                  return {
                    name: item.manageName,
                    id: item.manageCode,
                    phone: item.managePhone
                  }
                })
              : [{ name: '', id: '123654879', phone: '' }]
          this.depTableData = manageOfficeArrayStr
            ? JSON.parse(manageOfficeArrayStr).map((item) => {
                return {
                  id: item.officesCode,
                  deptName: item.officesName
                }
              })
            : []
          this.whpTableData = materialsDictArrayStr
            ? JSON.parse(materialsDictArrayStr).map((item) => {
                return {
                  id: item.materialDictId,
                  materialCode: item.materialCode,
                  materialName: item.materialName,
                  model: item.model,
                  basicUnitName: item.basicUnitName,
                  materialTypeName: item.materialTypeName,
                  supplierName: item.supplierName
                }
              })
            : []
          this.$forceUpdate()
          this.$refs.formRef.resetFields()
        }
      })
    },
    //初始化字典
    init() {
      this.getUpperStorehouseFn() // 上级库房
      this.getUnitTypeFn() // 单位类型
      this.getUnitListFn() //单位
      this.getDeptListFn() //科室
      this.getCourtyardFn() //院区
    },
    //上级库房
    getUpperStorehouseFn() {
      let params = {}
      this.$api.queryWarehouseByPageAll(params).then((res) => {
        if (res.code == '200') {
          this.storeOptions = res.data.list
        }
      })
    },
    // 所在院区
    getCourtyardFn() {
      this.$api.spaceTree().then((res) => {
        if (res.code == '200') {
          let arr = res.data
          arr.forEach((el) => {
            if (el.ssmType !== 5) {
              el.disabled = true
            } else {
              el.disabled = false
            }
          })
          this.gridCodeList = transData(arr, 'id', 'pid', 'children')
          this.gridCodeAllList = res.data
        }
      })
    },
    // 使用科室
    getDeptListFn() {
      this.$api.getSelectedDept().then((res) => {
        if (res.code == '200') {
          this.deptOptions = res.data
        }
      })
    },
    // 管理单位下拉
    getUnitListFn() {
      this.$api.getUnitList({}).then((res) => {
        if (res.code == 200) {
          this.managementUnitOptions = res.data
        }
      })
    },
    //单位类型
    getUnitTypeFn() {
      this.$api
        .getWarehouseDictList({
          dictType: 'units_type'
        })
        .then((res) => {
          if (res.code == 200) {
            this.unitTypeOptions = res.data
          }
        })
    },
    //上级库房name
    parentChangeWarehouse(val) {
      if (val) {
        this.formModel.parentName = this.storeOptions.find((item) => item.id == val).warehouseName
      }
    },
    // 院区changes事件
    gridCodeChange(val) {
      if (val && val.length) {
        this.formModel.gridName = this.$refs.gridCodeCascader.getCheckedNodes()[0].pathLabels.join(',') //获取选中name
      }
    },
    // 选择人员
    deptChange(newValue) {
      if (newValue && newValue.length) {
        let arr = this.deptOptions.filter((option) => newValue.includes(option.id)).map((option) => option.deptName)
        this.formModel.officesName = arr.join(',')
      }
    },
    //管理单位name
    manageUnitChange(val) {
      if (val) {
        this.formModel.manageUnitName = this.managementUnitOptions.find((item) => item.umId == val).unitComName
      }
    },
    //管理单位类型name
    warehouseUnitTypeChange(val) {
      if (val) {
        this.formModel.warehouseUnitName = this.unitTypeOptions.find((item) => item.dictValue == val).dictLabel
      }
    },
    // 打开人员
    selectPerson(type, index) {
      this.linkInfoIndex = index
      this.selectType = type
      this.isSelectPers = true
    },
    // 选择人员事件
    selectPersChange(data) {
      if (this.selectType === 'duty') {
        // this.formModel.manageName = data.map((item) => item.staffName).join(',')
        // this.formModel.managePhone = data.map((item) => item.mobile).join(',')
        this.formModel.responsiblePersonInfoList[this.linkInfoIndex].name = data[0].staffName
        this.formModel.responsiblePersonInfoList[this.linkInfoIndex].phone = data[0].mobile
        this.formModel.responsiblePersonInfoList[this.linkInfoIndex].id = data[0].id
      } else if (this.selectType === 'link') {
        this.formModel.linkInfoList[this.linkInfoIndex].name = data[0].staffName
        this.formModel.linkInfoList[this.linkInfoIndex].phone = data[0].mobile
      }
    },
    // 新增责任人
    addDutyRow() {
      this.formModel.responsiblePersonInfoList.push({
        name: '',
        phone: '',
        id: ''
      })
    },
    removeDutyRow(index) {
      this.formModel.responsiblePersonInfoList.splice(index, 1)
    },
    // 新增
    addRow() {
      this.formModel.linkInfoList.push({
        name: '',
        phone: ''
      })
    },
    //删除联系信息
    removeRow(index) {
      this.formModel.linkInfoList.splice(index, 1)
    },
    // 点击确定
    submitForm() {
      this.$refs['formRef'].validate((valid) => {
        if (valid) {
          let zrrStatus = this.formModel.responsiblePersonInfoList.every((item) => item.name)
          if (!zrrStatus) {
            this.$message.warning('请填写责任人信息')
            return
          }
          this.formLoading = true
          const userInfo = this.$store.state.user.userInfo.user
          let params = {
            ...this.formModel,
            userId: userInfo.staffId,
            userName: userInfo.staffName,
            warehouseType: 2,
            manageUserArrayStr: JSON.stringify(
              this.formModel.responsiblePersonInfoList.map((item) => {
                return {
                  manageName: item.name,
                  managePhone: item.phone,
                  manageCode: item.id
                }
              })
            ),
            manageOfficeArrayStr:
              this.depTableData.length > 0
                ? JSON.stringify(
                    this.depTableData.map((item, index) => {
                      return {
                        officesCode: item.id,
                        officesName: item.deptName,
                        sort: index + 1
                      }
                    })
                  )
                : '',
            materialsDictArrayStr:
              this.whpTableData.length > 0
                ? JSON.stringify(
                    this.whpTableData.map((item, index) => {
                      return {
                        materialDictId: item.id,
                        materialCode: item.materialCode,
                        materialName: item.materialName,
                        model: item.model,
                        basicUnitName: item.basicUnitName,
                        materialTypeName: item.materialTypeName,
                        supplierName: item.supplierName,
                        sort: index + 1
                      }
                    })
                  )
                : ''
          }
          params.gridCode = params.gridCode.length > 0 ? params.gridCode.join(',') : ''
          params.officesCode = params.officesCode.length > 0 ? params.officesCode.join(',') : ''
          if (this.formModel.linkInfoList && this.formModel.linkInfoList.length) {
            let contactsArr = this.formModel.linkInfoList.map((item) => {
              return item.name
            })
            let contactsPhoneArr = this.formModel.linkInfoList.map((item) => {
              return item.phone
            })
            params.contacts = contactsArr.length > 0 ? contactsArr.join(',') : ''
            params.contactsPhone = contactsPhoneArr.length > 0 ? contactsPhoneArr.join(',') : ''
          }
          delete params.linkInfoList
          this.$api.saveWarehouseInfo(params).then((res) => {
            this.formLoading = false
            if (res.code == '200') {
              this.$message.success(res.message)
              this.$router.go(-1)
            } else {
              this.$message.error(res.message)
            }
          })
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.container-content > div {
  background-color: #fff;
  height: 100%;
  position: relative;
  .warehouse-content-title {
    border-bottom: 1px solid #e4e7ed;
    padding: 12px 24px;
    cursor: pointer;
  }
  .content_box {
    padding: 0 24px;
    background: #fff;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    height: calc(100% - 50px);
    .operation-btn {
      margin-left: 20px;
      line-height: 40px;
      cursor: pointer;
      .addBtn {
        color: #3562db;
      }
      .deleteBtn {
        margin-left: 20px;
        color: #f53f3f;
      }
    }
    .row-table {
      margin-top: 10px;
      height: 300px;
    }
    .row-pagination {
      margin-top: 10px;
    }
  }
  .el-input,
  .el-select,
  .el-cascader {
    width: 100%;
  }
}
.toptip {
  box-sizing: border-box;
  height: 50px;
  width: 100%;
  line-height: 50px;
  display: flex;
  font-size: 16px;
  align-items: center;
  border: none;
}
.green_line {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 2px;
  background: #3562db;
  margin-right: 10px;
  vertical-align: middle;
}
.detailContent {
  display: inline-block;
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>

