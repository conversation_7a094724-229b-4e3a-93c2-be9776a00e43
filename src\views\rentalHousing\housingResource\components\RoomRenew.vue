<script>
import { LeaveType, LeaveTypeList } from '@/views/rentalHousing/housingResource/constant'
import monent from 'moment'
export default {
  name: 'RoomRenew',
  props: {
    houseId: String,
    visible: Boolean,
    id: String,
    endDate: String,
    tenantName: String,
    spaceName: String,
    roomName: String,
    price: String,
    amount: String
  },
  events: ['update:visible', 'success'],
  data: () => ({
    formModel: {
      // 申请附件
      applyFileList: [],
      // 合同号
      contractNo: '',
      // 合同附件
      contractFileList: [],
      // 租期开始时间
      startDate: '',
      // 租期结束时间
      endDate: ''
    },
    rules: {
      contractNo: [{ required: true, message: '请填写合同号' }],
      contractFileList: [{ required: true, type: 'array', message: '请上传合同附件' }],
      startDate: [{ required: true, message: '租期开始日期不能为空' }],
      endDate: [{ required: true, message: '请选择租期结束日期' }]
    },
    loadingStatus: false,
    // 当前上传类型
    currentUploadProp: '',
    // timerId
    $timerId: -1,
    $startDate: new Date()
  }),
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    },
    // 上传组件可用的数据源key
    uploadProp() {
      return {
        // 申请书
        apply: 'apply',
        // 合同
        contract: 'contract'
      }
    },
    fileAccept() {
      return '.rar,.zip,.doc,.docx,.pdf,.jpg,.jpeg,.png'
    }
  },
  watch: {
    dialogVisible(val) {
      if (!val) return
      let _monent = monent(this.endDate)
      _monent = _monent.add(1, 'd')
      this.$startDate = _monent.toDate()
      this.$nextTick(() => {
        this.formModel.startDate = _monent.format('YYYY-MM-DD')
      })
    }
  },
  methods: {
    onDialogClosed() {
      // dialog关闭时重置form表单
      this.$refs.formRef.resetFields()
      this.currentUploadProp = ''
      clearTimeout(this.$timerId)
    },
    onSubmit() {
      this.$refs.formRef
        .validate()
        .catch(() => Promise.reject())
        .then(() => {
          if (!this.id) {
            throw '缺少记录ID'
          }
          // config request data
          const params = {
            userId: this.$store.getters.userId,
            userName: this.$store.getters.userName,
            historyRecordId: this.id,
            tenantAttachmentUrl: this.formatFileArr(this.formModel.applyFileList),
            rentingStartTime: this.formModel.startDate,
            rentingEndTime: this.formModel.endDate,
            contractNum: this.formModel.contractNo,
            contractAttachmentUrl: this.formatFileArr(this.formModel.contractFileList)
          }
          return this.$api.rentalHousingApi.renewed(params)
        })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('操作成功')
            this.$emit('success')
            this.dialogVisible = false
          } else {
            throw res.message || '操作失败'
          }
        })
        .catch((msg) => {
          msg && this.$message.error(msg)
        })
        .finally(() => {
          this.loadingStatus = false
        })
    },
    // 接口提交附件字段 格式处理
    formatFileArr(arr) {
      if (arr && arr.length) {
        let arrJson = arr.map((item) => {
          return {
            url: item.uploadPath,
            name: item.file ? item.file.name : item.name
          }
        })
        return JSON.stringify(arrJson)
      } else {
        return ''
      }
    },
    // 根据key，获取上传文件对应的数据源
    getFileList(key) {
      if (key === this.uploadProp.apply) {
        return this.formModel.applyFileList
      } else if (key === this.uploadProp.contract) {
        return this.formModel.contractFileList
      } else {
        return []
      }
    },
    // 文件上传代理
    async handleHttpRequest(propKey, file) {
      const fileList = this.getFileList(propKey)
      await this.checkFile(file.file, fileList)
      const params = new FormData()
      params.append('file', file.file)
      let res = await this.$api.uploadFile(params)
      if (res.code === '200') {
        this.$message({
          message: '上传成功',
          type: 'success'
        })
        const fileInfo = fileList.find((it) => it.uid === file.file.uid)
        if (fileInfo) {
          fileInfo.uploadPath = res.data.fileKey
        }
      } else {
        throw res.message
      }
    },
    // 检测文件是否可以上传
    async checkFile(file, fileList) {
      if (/\s/.test(file.name)) {
        throw '文件名不能存在空格'
      }
      if (fileList.filter((x) => x.name === file.name).length > 1) {
        throw '已存在此文件，请重新选择'
      }
      const extension = file.name.substring(file.name.lastIndexOf('.'))
      // 根据类型验证
      const accept = this.fileAccept
      if (!accept.includes(extension)) {
        throw '请选择正确的文件类型'
      }
      if (file.size > 10485760) {
        throw '文件大小不能超过10MB'
      }
    },
    // 上传失败提示
    handleUploadError(err) {
      let errMsg = '上传失败'
      if (typeof err === 'string') {
        errMsg = err
      }
      this.$message.error(errMsg)
    },
    // 失踪同步文件列表
    handleFileChange(file) {
      if (file.status === 'ready') {
        // 给文件绑定上传组件对应的数据源key
        file.prop = this.currentUploadProp
      }
      const fileList = this.getFileList(file.prop)
      if (file.status === 'fail') {
        // 失败的文件，500ms后删除，视觉效果会好一些
        window.clearTimeout(this.$timerId)
        this.$timerId = window.setTimeout(() => {
          let index = -1
          do {
            index = fileList.findIndex((it) => it.status === 'fail')
            if (index > -1) {
              fileList.splice(index, 1)
            }
          } while (index > -1)
          this.$timerId = -1
        }, 500)
      } else {
        const index = fileList.findIndex((it) => it.uid === file.uid)
        if (index > -1) {
          fileList.splice(index, 1, file)
        } else {
          fileList.push(file)
        }
      }
    },
    // 文件移除时
    handleFileRemove(file) {
      const fileList = this.getFileList(file.prop)
      const index = fileList.findIndex((it) => it.uid === file.uid)
      if (index > -1) {
        fileList.splice(index, 1)
      }
    },
    // date-picker 日期禁用方法
    disabledDate(date) {
      return date <= this.$startDate
    }
  }
}
</script>
<template>
  <el-dialog
    v-dialogDrag
    class="component room-renew"
    title="办理续租"
    width="1080px"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    custom-class="model-dialog"
    destroy-on-close
    @close="onDialogClosed"
  >
    <el-form ref="formRef" v-loading="loadingStatus" :model="formModel" :rules="rules" label-width="95px">
      <div class="room-renew__title">
        <svg-icon name="right-arrow" />
        申请信息
      </div>
      <el-row>
        <el-col :span="10">
          <el-form-item label="申请人" prop="buildingName">
            <div class="room-renew__info">{{ tenantName }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="14">
          <el-form-item label="附件文件" prop="applyFileList">
            <el-upload
              action=""
              list-type="file-list"
              class="room-renew__upload"
              data-type="certificate"
              :file-list="formModel.applyFileList"
              :accept="fileAccept"
              :limit="1"
              :http-request="handleHttpRequest.bind(this, uploadProp.apply)"
              :on-change="handleFileChange"
              :on-error="handleUploadError"
              :on-remove="handleFileRemove"
              @click.native="currentUploadProp = uploadProp.apply"
            >
              <el-button type="primary" plain icon="el-icon-upload2">上传文件</el-button>
              <div slot="tip" class="el-upload__tip">支持扩展名：{{ fileAccept }},且大小不超过10M</div>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="room-renew__title">
        <svg-icon name="right-arrow" />
        合同信息
      </div>
      <el-row>
        <el-col :span="5">
          <el-form-item label="甲方">
            <div class="room-renew__info">中国医学科学院肿瘤医院深圳医院</div>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="已方">
            <div class="room-renew__info">{{ tenantName }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="9">
          <el-form-item label="空间位置">
            <div class="room-renew__info">{{ spaceName }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="房间号">
            <div class="room-renew__info">{{ roomName }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="出租单价">
            <el-input :value="price" readonly>
              <template #suffix> 元/㎡/月 </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :offset="5" :span="6">
          <el-form-item label="出租租金">
            <el-input :value="amount" readonly>
              <template #suffix> 元 </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="合同编号" prop="contractNo">
            <el-input v-model="formModel.contractNo" placeholder="请输入" maxlength="50"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="租期开始" prop="startDate">
            <el-input :value="formModel.startDate" disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="租期结束" prop="endDate">
            <el-date-picker
              v-model="formModel.endDate"
              type="date"
              :default-value="formModel.startDate"
              value-format="yyyy-MM-dd"
              :picker-options="{ disabledDate }"
            ></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="附件文件" prop="contractFileList">
        <el-upload
          action=""
          list-type="file-list"
          class="room-renew__upload"
          data-type="certificate"
          :file-list="formModel.contractFileList"
          :accept="fileAccept"
          :limit="1"
          :http-request="handleHttpRequest.bind(this, uploadProp.contract)"
          :on-change="handleFileChange"
          :on-error="handleUploadError"
          :on-remove="handleFileRemove"
          @click.native="currentUploadProp = uploadProp.contract"
        >
          <el-button type="primary" plain icon="el-icon-upload2">上传文件</el-button>
          <div slot="tip" class="el-upload__tip">支持扩展名：{{ fileAccept }},且大小不超过10M</div>
        </el-upload>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" plain @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" :loading="loadingStatus" @click="onSubmit">办理</el-button>
    </template>
  </el-dialog>
</template>
<style lang="scss" scoped>
.room-renew {
  .el-form {
    background: #fff;
    width: 100%;
    padding: 16px;
    height: 100%;
  }
  .el-input-number {
    line-height: 30px;
  }
  &__title {
    margin-bottom: 16px;
  }
  &__info {
    padding-left: 16px;
  }
  ::v-deep(.el-input) {
    .el-input__inner[readonly] {
      border: none;
    }
  }
}
</style>
