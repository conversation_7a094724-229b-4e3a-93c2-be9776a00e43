<template>
  <div id="parkingOverview">
    <div class="batch-control">
      <p>运营总览</p>
      <div v-if="saveAndCancelBtnShow">
        <el-button type="primary" plain @click="cancelStaging">取消</el-button>
        <el-button type="primary" @click="saveStaging">保存</el-button>
      </div>
    </div>
    <dashboard v-if="dashExampleShow" id="dashExample">
      <dash-layout v-bind="dlayout" :debug="false">
        <dash-item v-for="(item, index) in dlayout.items" v-bind.sync="dlayout.items[index]" :key="item.id" @moveEnd="moveEnd" @resizeEnd="resizeEnd(item.id)">
          <div class="card-item">
            <div class="card-empty">
              <header class="header drag_class airconditioning">
                <div class="legend-title">
                  {{ item.name }}
                </div>
                <div v-if="item.name == '运营总览'">
                  <div class="btns">
                    <span :class="{ 'active-btn': collectType == '1' }" @click="changeCompletionRateType('1')">日</span>
                    <span :class="{ 'active-btn': collectType == '2' }" @click="changeCompletionRateType('2')">周</span>
                    <span :class="{ 'active-btn': collectType == '3' }" @click="changeCompletionRateType('3')">月</span>
                    <span :class="{ 'active-btn': collectType == '4' }" @click="changeCompletionRateType('4')">全部</span>
                  </div>
                </div>
                <div v-if="item.name == '菜品销售Top5'">
                  <div class="btns">
                    <span :class="{ 'active-btn': collectType2 == '1' }" @click="changeCompletionRateType2('1')">日</span>
                    <span :class="{ 'active-btn': collectType2 == '2' }" @click="changeCompletionRateType2('2')">周</span>
                    <span :class="{ 'active-btn': collectType2 == '3' }" @click="changeCompletionRateType2('3')">月</span>
                    <span :class="{ 'active-btn': collectType2 == '4' }" @click="changeCompletionRateType2('4')">全部</span>
                  </div>
                </div>
                <div v-if="item.name == '订餐科室Top5'">
                  <div class="btns">
                    <span :class="{ 'active-btn': collectType3 == '1' }" @click="changeCompletionRateType3('1')">日</span>
                    <span :class="{ 'active-btn': collectType3 == '2' }" @click="changeCompletionRateType3('2')">周</span>
                    <span :class="{ 'active-btn': collectType3 == '3' }" @click="changeCompletionRateType3('3')">月</span>
                    <span :class="{ 'active-btn': collectType3 == '4' }" @click="changeCompletionRateType3('4')">全部</span>
                  </div>
                </div>
                <div v-if="item.name == '订餐方式占比'">
                  <div class="btns">
                    <span :class="{ 'active-btn': collectType4 == '1' }" @click="changeCompletionRateType4('1')">日</span>
                    <span :class="{ 'active-btn': collectType4 == '2' }" @click="changeCompletionRateType4('2')">周</span>
                    <span :class="{ 'active-btn': collectType4 == '3' }" @click="changeCompletionRateType4('3')">月</span>
                    <span :class="{ 'active-btn': collectType4 == '4' }" @click="changeCompletionRateType4('4')">全部</span>
                  </div>
                </div>
                <div v-if="item.name == '餐饮投诉'">
                  <div class="btns">
                    <span :class="{ 'active-btn': collectType5 == '1' }" @click="changeCompletionRateType5('1')">日</span>
                    <span :class="{ 'active-btn': collectType5 == '2' }" @click="changeCompletionRateType5('2')">周</span>
                    <span :class="{ 'active-btn': collectType5 == '3' }" @click="changeCompletionRateType5('3')">月</span>
                    <span :class="{ 'active-btn': collectType5 == '4' }" @click="changeCompletionRateType5('4')">全部</span>
                  </div>
                </div>
                <div v-if="item.name == '订餐类别统计'">
                  <div class="btns">
                    <span :class="{ 'active-btn': collectType6 == '1' }" @click="changeCompletionRateType6('1')">日</span>
                    <span :class="{ 'active-btn': collectType6 == '2' }" @click="changeCompletionRateType6('2')">周</span>
                    <span :class="{ 'active-btn': collectType6 == '3' }" @click="changeCompletionRateType6('3')">月</span>
                    <span :class="{ 'active-btn': collectType6 == '4' }" @click="changeCompletionRateType6('4')">全部</span>
                  </div>
                </div>
                <el-dropdown class="dropdown-btn" trigger="click" @command="dropDownCommand">
                  <span class="more-operations" @click.stop.prevent>· · ·</span>
                  <el-dropdown-menu slot="dropdown" class="dropdownSelect">
                    <el-dropdown-item>编辑</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </header>

              <div class="card-content" v-if="item.id == '5d5f8v4x5b5d1b7s9j3d5b7h'">
                <div class="cont">
                  <div>
                    <div style="background: #fff5eb">
                      <div>
                        <div style="font-size: 15px; color: #333333">订单总量</div>
                        <div>
                          <span style="font-size: 24px; color: #333333; font-weight: 600">{{ parkingLot.allOrderCount || 0 }}</span
                          >&nbsp;<span style="font-size: 14px; color: #7f848c">单</span>
                        </div>
                      </div>
                      <img src="../../../assets/images/parkingLot/count-s.png" alt="" />
                    </div>
                    <div style="background: #ebf0fc">
                      <div>
                        <div style="font-size: 15px; color: #333333">销售总额</div>
                        <div>
                          <span style="font-size: 24px; color: #333333; font-weight: 600">{{ parkingLot.allOrderPrice || 0 }}</span
                          >&nbsp;<span style="font-size: 14px; color: #7f848c">元</span>
                        </div>
                      </div>
                      <img src="../../../assets/images/parkingLot/count-o.png" alt="" />
                    </div>
                  </div>
                  <div>
                    <div style="background: #ebf0fc">
                      <div>
                        <div style="font-size: 15px; color: #333333">客单价</div>
                        <div>
                          <span style="font-size: 24px; color: #333333; font-weight: 600">{{ parkingLot.price || 0 }}</span
                          >&nbsp;<span style="font-size: 14px; color: #7f848c">元</span>
                        </div>
                      </div>
                      <img src="../../../assets/images/parkingLot/count-o.png" alt="" />
                    </div>
                    <div style="background: #e6f9f1">
                      <div>
                        <div style="font-size: 15px; color: #333333">满意度</div>
                        <div>
                          <!-- <span style="font-size: 24px; color: #333333; font-weight: 600">{{ parkingLot.satisfaction || 0 }}</span
                          >&nbsp;<span style="font-size: 14px; color: #7f848c">%</span> -->
                        </div>
                      </div>
                      <img src="../../../assets/images/parkingLot/count-t.png" alt="" />
                    </div>
                  </div>
                </div>
              </div>
              <div class="card-content" v-if="item.id == '2z8df12462sc8d21bn86s2f5'">
                <div style="height: 100%; width: 100%">
                  <div id="chargeStatistics" class="full-style" ref="2z8df12462sc8d21bn86s2f5"></div>
                </div>
              </div>
              <div class="card-content" v-if="item.id == '5d9nb6a5bc56d5g958tv3s1f'">
                <div style="height: 100%; width: 100%">
                  <div id="department" class="full-style" ref="5d9nb6a5bc56d5g958tv3s1f"></div>
                </div>
              </div>
              <div class="card-content" v-if="item.id == '39d8w5f8g89s5v5a7d8g5d5d'">
                <div style="height: 100%; width: 100%">
                  <div id="carYardStatistics" class="full-style" ref="39d8w5f8g89s5v5a7d8g5d5d"></div>
                </div>
              </div>
              <div class="card-content" v-if="item.id == '44n9a7n4d66b6s5as9w825f8'">
                <div style="height: 100%; width: 100%">
                  <div class="complaints">
                    <div>
                      <span style="color: #666666">投诉总数：</span>
                      <span style="color: #fa403c">{{ complaints.complaintTotal }}</span>
                    </div>
                    <div>
                      <span style="color: #666666">已处置：</span> <span style="color: #00bc6d">{{ complaints.handle }}</span>
                    </div>
                    <div>
                      <span style="color: #666666">处置率：</span> <span style="color: #ff9435">{{ complaints.handleRate }}%</span>
                    </div>
                  </div>
                  <div id="cateringComplaints" class="full-style" ref="44n9a7n4d66b6s5as9w825f8"></div>
                </div>
              </div>
              <div class="card-content" v-if="item.id == '1s5gva5s7vbs5b7d55b7d5b4'">
                <div style="height: 100%; width: 100%">
                  <div id="orderOrderCategory" class="full-style" ref="1s5gva5s7vbs5b7d55b7d5b4"></div>
                </div>
              </div>
            </div>
          </div>
        </dash-item>
      </dash-layout>
    </dashboard>
  </div>
</template>

<script>
import { Dashboard, DashLayout, DashItem } from 'vue-responsive-dash'
import * as echarts from 'echarts'
export default {
  name: 'parkingOverview',
  components: {
    Dashboard,
    DashLayout,
    DashItem
  },
  data() {
    return {
      orderOrderCategory: [],
      complaints: '',
      complaintsList: [],
      xAxisData: [],
      seriesData: [],
      parkingMonit: '',
      parkingLot: '',
      collectType: '2',
      collectType2: '2',
      collectType3: '2',
      collectType4: '2',
      collectType5: '2',
      collectType6: '2',

      hospitalEvn: __PATH.VUE_APP_HOSPITAL_NODE_ENV,
      dlayout: {
        breakpoint: 'xs',
        numberOfCols: 24,
        items: []
      },
      airData: [],
      carYardStatisticsList: [],
      carYardStatistics: '',
      flowAnalysisList: {},
      chargeStatisticsList: {},
      defaultItems: [],
      echartsDom: ['carYardStatistics', 'chargeStatistics', 'department', 'cateringComplaints', 'orderOrderCategory'],
      dashExampleShow: true,
      saveAndCancelBtnShow: false // 保存按钮组显示
    }
  },
  watch: {
    '$store.state.settings.sidebarCollapse': {
      handler(val) {
        this.$nextTick(() => {
          let echartsDom = this.echartsDom
          setTimeout(() => {
            echartsDom.forEach((item) => {
              echarts.init(document.getElementById(item)).resize()
            })
          }, 250)
        })
      },
      deep: true
    }
  },
  mounted() {
    let items = []

    items = [
      { id: '5d5f8v4x5b5d1b7s9j3d5b7h', name: '运营总览', x: 0, y: 0, width: 12, height: 6 },
      { id: '2z8df12462sc8d21bn86s2f5', name: '菜品销售Top5', x: 12, y: 0, width: 12, height: 6 },
      { id: '5d9nb6a5bc56d5g958tv3s1f', name: '订餐科室Top5', x: 0, y: 6, width: 12, height: 6 },
      { id: '39d8w5f8g89s5v5a7d8g5d5d', name: '订餐方式占比', x: 12, y: 6, width: 12, height: 6 },
      { id: '44n9a7n4d66b6s5as9w825f8', name: '餐饮投诉', x: 0, y: 12, width: 12, height: 6 },
      { id: '1s5gva5s7vbs5b7d55b7d5b4', name: '订餐类别统计', x: 12, y: 12, width: 12, height: 6 }
    ]

    this.defaultItems = items
    this.getDragManageList()
    this.changeCompletionRateType('2')

    setTimeout(() => {
      this.changeCompletionRateType2('2')
      this.changeCompletionRateType3('2')
      this.changeCompletionRateType4('2')
      this.changeCompletionRateType5('2')
      this.changeCompletionRateType6('2')

      this.getCateringComplaints()
      this.getOrderOrderCategory()
    }, 500)
  },
  methods: {
    //订餐类别统计
    changeCompletionRateType6(type) {
      this.collectType6 = type
      this.$api.getBookingTypeCount({ querySection: this.collectType6 }).then((res) => {
        if (res.code == '200') {
          this.orderOrderCategory = res.data.chartData

          this.initorderOrderCategory()
        } else {
          this.orderOrderCategory = []
          this.$message.error(res.message)
        }
      })
    },
    //餐饮投诉
    changeCompletionRateType5(type) {
      this.collectType5 = type
      this.$api.getRestaurantcomplaint({ querySection: this.collectType5 }).then((res) => {
        if (res.code == '200') {
          this.complaints = res.data
          this.complaintsList = res.data.chartData

          this.initCateringComplaints()
        } else {
          this.complaintsList = []
          this.$message.error(res.message)
        }
      })
    },
    //订餐方式占比
    changeCompletionRateType4(type) {
      this.collectType4 = type
      this.$api.getBookingWayCount({ querySection: this.collectType4 }).then((res) => {
        if (res.code == '200') {
          this.carYardStatisticsList = res.data.chartData

          this.initCarYardStatistics()
        } else {
          this.carYardStatisticsList = []
          this.$message.error(res.message)
        }
      })
    },
    // 订餐top5
    changeCompletionRateType3(type) {
      this.xAxisData = []
      this.seriesData = []

      this.collectType3 = type
      this.$api.getBookingDeptTopFive({ querySection: this.collectType3 }).then((res) => {
        if (res.code == 200) {
          this.xAxisData = res.data.deptName
          this.seriesData = res.data.workNum
          this.$nextTick(() => {
            this.getPowerMonthTypeNoEchart2()
          })
        }
      })
    },
    getPowerMonthTypeNoEchart2() {
      const getchart = echarts.init(document.getElementById('department'))
      let arr = []
      this.xAxisData.forEach((item) => {
        let obj = {
          name: item
        }
        arr.push(obj)
      })
      this.seriesData.forEach((item, i) => {
        arr.forEach((item2, n) => {
          if (i == n) {
            item2.value = item
          }
        })
      })
      const data = arr
      const nameList = Array.from(data, ({ name }) => name)
      let option = {}
      if (nameList.length) {
        option = {
          tooltip: {
            trigger: 'axis'
          },
          grid: {
            top: '15%',
            left: '4%',
            right: '5%',
            bottom: '5%',
            containLabel: true
          },

          xAxis: {
            type: 'category',
            data: nameList,
            nameLocation: 'start',

            axisLabel: {
              interval: 0, // 横轴信息全部显示
              axisLabel: {
                interval: 0,
                formatter(val) {
                  if (val.length > 5) {
                    return val.slice(0, 4) + '...'
                  } else {
                    return val
                  }
                }
              }
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            }
          },
          yAxis: [
            {
              type: 'value'
              // axisLabel: { formatter: '{value} 个' }
            }
          ],
          legend: {
            x: 'left',
            left: '4%',
            data: [
              {
                name: '订单数(单)',
                icon: 'circle'
              }
            ],
            textStyle: {
              color: '#333' // 图例文字颜色
            }
          },
          series: [
            {
              name: '订单数(单)',
              type: 'bar',
              barWidth: 10,
              data: data,
              itemStyle: {
                // color: '#00BC6D'
                color: '#3562DB'
              }
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }

      getchart.clear()
      getchart.setOption(option)
      getchart.resize()
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 菜品销售top5
    changeCompletionRateType2(type) {
      this.collectType2 = type
      this.$api.getDishesSaleTopFive({ querySection: this.collectType2 }).then((res) => {
        if (res.code == '200') {
          this.chargeStatisticsList = res.data
        } else {
          this.chargeStatisticsList = {}
          this.$message.error(res.message)
        }
        this.initChargeStatistics(this.chargeStatisticsList)
      })
    },

    initChargeStatistics(data) {
      console.log(data, 'data')
      const getchart = echarts.init(document.getElementById('chargeStatistics'))
      let colors = ['#3562DB', '#FF9435']
      let option = {}
      if (data.dishesName.length) {
        option = {
          color: colors,
          tooltip: {
            trigger: 'axis'
            // axisPointer: {
            //   type: 'cross'
            // }
          },
          grid: {
            right: '15%',
            left: '12%'
          },
          legend: {
            data: [
              {
                name: '销售额/元',
                icon: 'circle'
              },
              {
                name: '数量/笔'
              }
            ],
            textStyle: {
              color: '#333' // 图例文字颜色
            }
          },
          xAxis: [
            {
              type: 'category',
              axisTick: {
                alignWithLabel: true
              },
              data: data.dishesName
            }
          ],
          yAxis: [
            {},
            {
              type: 'value',
              name: '',
              min: 0,
              max: Math.max.apply(null, data.salePrice),
              position: 'left',
              axisLine: {
                lineStyle: {
                  color: '#414653'
                }
              },
              axisLabel: {
                formatter: '{value}'
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: '#e3e7ec'
                }
              }
            },
            {
              type: 'value',
              name: '',
              min: 0,
              max: Math.max.apply(null, data.saleCount),
              offset: 22,
              position: 'right',
              axisLine: {
                lineStyle: {
                  color: '#414653'
                }
              },
              axisLabel: {
                formatter: '{value}'
              },
              splitLine: {
                show: false
              }
            }
          ],
          series: [
            {
              name: '销售额/元',
              type: 'bar',
              yAxisIndex: 1,
              barWidth: 10,
              data: data.salePrice,
              itemStyle: {
                borderRadius: 2
              }
            },
            {
              name: '数量/笔',
              type: 'line',
              yAxisIndex: 2,
              data: data.saleCount,
              symbol: 'circle',
              itemStyle: {
                borderType: 'solid',
                borderColor: '#fff',
                borderWidth: 1
              }
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },

    initorderOrderCategory() {
      let arr = []
      if (this.orderOrderCategory.length) {
        this.orderOrderCategory.forEach((item) => {
          let obj = {
            name: item.name,
            value: item.value,
            rate: item.rate
          }

          arr.push(obj)
        })
      }
      const getchart = echarts.init(document.getElementById('orderOrderCategory'))
      let option = {}
      if (this.orderOrderCategory.length) {
        option = {
          tooltip: {
            trigger: 'item'
          },
          legend: {
            type: 'scroll',
            orient: 'vertical',
            icon: 'circle', // 形状
            itemWidth: 12, // 宽
            itemHeight: 12, // 高
            top: '30%',
            right: '7%',
            selected: {},
            formatter: function (name) {
              var oa = arr
              for (var i = 0; i < oa.length; i++) {
                if (name === oa[i].name) {
                  return ' ' + name + '  (' + oa[i].value + '件' + ')   ' + '' + oa[i].rate + '%'
                }
              }
            }
          },
          series: [
            {
              type: 'pie',
              radius: '60%',
              center: ['35%', '50%'],
              color: ['#3562db', '#0ca6ed', '#ff9435', '#ffbe00', '#3562DB', '#E4E7ED', '#ffbe03'],
              label: {
                color: '#333333',
                formatter: function (params) {
                  return params.data.name + ' (' + params.data.value + '件' + ' )' + params.data.rate + '%'
                }
              },
              data: arr
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }

      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },

    initCateringComplaints() {
      let arr = []
      if (this.complaintsList.length) {
        this.complaintsList.forEach((item) => {
          let obj = {
            name: item.name,
            value: item.value,
            rate: item.rate
          }
          // if (item.name == '食品安全') obj.color = '#3563dc'
          // if (item.name == '服务态度') obj.color = '#0ca7eb'
          // if (item.name == '环境卫生') obj.color = '#ff9537'
          // if (item.name == '送餐速度') obj.color = '#ffbe03'
          arr.push(obj)
        })
      }
      const getchart = echarts.init(document.getElementById('cateringComplaints'))
      let option = {}
      if (this.complaintsList.length) {
        option = {
          // color: arr.map((i) => i.color),
          tooltip: {
            trigger: 'item'
          },
          legend: {
            type: 'scroll',
            orient: 'vertical',
            icon: 'circle', // 形状
            itemWidth: 12, // 宽
            itemHeight: 12, // 高
            top: '30%',
            right: '7%',
            selected: {},
            formatter: function (name) {
              var oa = arr
              for (var i = 0; i < oa.length; i++) {
                if (name === oa[i].name) {
                  return ' ' + name + '  (' + oa[i].value + '件' + ')   ' + '' + oa[i].rate + '%'
                }
              }
            }
          },
          series: [
            {
              type: 'pie',
              roseType: 'radius',
              radius: ['50%', '70%'],
              center: ['35%', '50%'],
              color: ['#3562db', '#0ca6ed', '#ff9435', '#ffbe00', '#3562DB', '#E4E7ED', '#ffbe03'],
              label: {
                color: '#333333',
                formatter: function (params) {
                  return params.data.name + ' (' + params.data.value + '件' + ' )' + params.data.rate + '%'
                }
              },
              data: arr
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }

      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    initCarYardStatistics() {
      let arr = []
      if (this.carYardStatisticsList.length) {
        this.carYardStatisticsList.forEach((item) => {
          let obj = {
            name: item.name,
            value: item.value,
            rate: item.rate
          }
          // if (item.name == '电话') obj.color = '#3562db'
          // if (item.name == '微信') obj.color = '#0ca6ed'
          // if (item.name == '支付宝') obj.color = '#ff9435'
          // if (item.name == '其他') obj.color = '#ffbe00'
          arr.push(obj)
        })
      }
      const getchart = echarts.init(document.getElementById('carYardStatistics'))
      let option = {}
      if (this.carYardStatisticsList.length) {
        option = {
          // color: arr.map((i) => i.color),
          tooltip: {
            trigger: 'item'
          },
          legend: {
            type: 'scroll',
            orient: 'vertical',
            icon: 'circle', // 形状
            itemWidth: 12, // 宽
            itemHeight: 12, // 高
            top: '30%',
            right: '7%',
            selected: {},
            formatter: function (name) {
              var oa = arr
              for (var i = 0; i < oa.length; i++) {
                if (name === oa[i].name) {
                  return ' ' + name + '  (' + oa[i].value + '件' + ')   ' + '' + oa[i].rate + '%'
                }
              }
            }
          },
          series: [
            {
              type: 'pie',
              roseType: 'radius',
              color: ['#3562db', '#0ca6ed', '#ff9435', '#ffbe00', '#3562DB', '#E4E7ED', '#ffbe03'],

              radius: ['50%', '70%'],
              center: ['35%', '50%'],
              label: {
                color: '#333333',
                formatter: function (params) {
                  return params.data.name + ' (' + params.data.value + '件' + ' )' + params.data.rate + '%'
                }
              },
              data: arr
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }

      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    changeCompletionRateType(type) {
      this.collectType = type
      this.$api.getPperationsOverview({ querySection: this.collectType }).then((res) => {
        if (res.code == 200) {
          this.parkingLot = res.data
        } else {
          this.$message.error(res.message)
        }
      })
    },
    moveEnd() {
      console.log('moveEnd', this.dlayout)
    },
    echartsResize() {
      let echartsDom = this.echartsDom
      this.$nextTick(() => {
        setTimeout(() => {
          echartsDom.forEach((item) => {
            echarts.init(document.getElementById(item)).resize()
          })
        }, 100)
      })
    },
    resizeEnd(val) {
      this.echartsResize(val)
    },
    cancelStaging() {
      this.saveAndCancelBtnShow = false
      this.getDragManageList()
      setTimeout(() => {
        this.changeCompletionRateType('2')

        this.changeCompletionRateType2('2')
        this.changeCompletionRateType3('2')
        this.changeCompletionRateType4('2')
        this.changeCompletionRateType5('2')
        this.changeCompletionRateType6('2')
        this.getCateringComplaints()
        this.getOrderOrderCategory()
      }, 300)
    },
    // 保存工作台模块
    saveStaging() {
      console.log(this.dlayout.items)
      const items = this.dlayout.items
      if (!items.length) {
        return this.$message.warning('无可配置模块')
      }
      const jsonList = items.map((e) => {
        return {
          id: e.id,
          name: e.name,
          x: e.x,
          y: e.y,
          width: e.width,
          height: e.height
        }
      })
      const params = {
        jsonList: JSON.stringify(jsonList),
        userId: this.$store.state.user.userInfo.user.staffId,
        menuType: 11
      }
      console.log('save提交', params)
      this.$api.saveWorktopManage(params).then((res) => {
        if (res.code == 200) {
          this.$message.success('保存成功')
          this.saveAndCancelBtnShow = false
          this.getDragManageList()
          setTimeout(() => {
            this.changeCompletionRateType('2')
            this.changeCompletionRateType2('2')
            this.changeCompletionRateType3('2')
            this.changeCompletionRateType4('2')
            this.changeCompletionRateType5('2')
            this.changeCompletionRateType6('2')
            this.getCateringComplaints()
            this.getOrderOrderCategory()
          }, 300)
        } else {
          this.$message.error(res.message)
        }
      })
    },
    dropDownCommand() {
      this.saveAndCancelBtnShow = true
      this.dlayout.items.map((e) => {
        e.resizable = true
        e.draggable = true
      })
      // console.log(val)
    },
    // 获取可拖拽列表
    getDragManageList() {
      const params = {
        userId: this.$store.state.user.userInfo.user.staffId,
        menuType: 11
      }
      this.$api.getWorktopManageList(params).then((res) => {
        if (res.code == 200) {
          const data = res.data
          let componentItems = []
          // 有接口数据取接口数据 无接口数据取字典默认配置数据
          if (data.length) {
            componentItems = data
          } else {
            componentItems = this.defaultItems
          }
          // 遍历增加dragAllowFrom、resizable、draggable字段
          const items = componentItems.map((item) => {
            return {
              id: item.id,
              name: item.name || this.defaultItems.find((e) => e.id == item.id).name,
              x: item.x,
              y: item.y,
              width: item.width,
              height: item.height,
              dragAllowFrom: '.drag_class',
              resizable: false,
              draggable: false
            }
          })
          this.dashExampleShow = false
          this.$nextTick(() => {
            this.dashExampleShow = true
            this.dlayout.items = items
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
#parkingOverview {
  // background: #fff;
  height: calc(100% - 30px);
  // width: calc(100% - 30px);
  max-width: 1620px;
  padding: 0 20px;
  margin: 15px auto;
  display: flex;
  flex-direction: column;
  .batch-control {
    display: flex;
    justify-content: space-between;
    height: 44px;
    padding: 6px 10px;
    > p {
      padding-left: 15px;
      font-size: 16px;
      text-align-last: left;
      font-family: 'PingFang SC-Medium', 'PingFang SC';
      font-weight: 550;
      color: #121f3e;
    }
    > div {
      text-align: right;
    }
  }
}

#dashExample {
  flex: 1;
  max-height: 100%;
  overflow-y: auto;
  overflow-x: hidden;

  ::v-deep(.placeholder) {
    background: #e2e6eb !important;
    border-radius: 10px;
    opacity: 1;
  }
}
.dropdownSelect {
  margin: 0;
  padding: 3px 0;

  .el-dropdown-menu__item {
    line-height: 30px;
    padding: 0 15px;
  }
}
.card-item {
  height: 100%;
  width: 100%;
  background: #fff;
  box-sizing: border-box;
  padding: 5px;
  box-shadow: 0 2px 7px 0 #e4e4ec !important;
  border-radius: 10px !important;

  .card-empty {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  header {
    align-items: center;
    height: 48px;
    // .dropdown-btn {
    //   position: absolute;
    //   right: 0;
    // }
    .more-operations {
      cursor: pointer;
      color: #3562db;
      user-select: none;
      font-size: 14px;
      font-weight: 500;
      height: auto;
    }
  }

  // .mover {
  //   cursor: move;
  // }

  .airconditioning {
    display: flex;
    padding: 0 15px 0 0;
    color: #606266;
    // font-weight: 600;
    // border-bottom: 1px solid #d8dee7;

    .legend-title {
      // font-size: 16px;
      // color: #606266;
      font-size: 15px;
      font-family: 'PingFang SC-Medium', 'PingFang SC';
      font-weight: bold;
      color: #333;
      padding-left: 15px;
      position: relative;
      line-height: 48px;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      height: 100%;
    }

    .notlinkBg {
      background-color: #f2f2f2;
      color: #606266;
    }

    .faultBg {
      background-color: #fff4ea;
      color: #f39038;
    }

    .warnBg {
      background-color: #ffebeb;
      color: #ff4848;
    }

    .legend-right-box {
      width: 49px;
      height: 22px;
      border-radius: 2px;
      margin: auto;
      display: flex;
      margin-right: 15px;

      span {
        display: inline-block;
        width: 22px;
        height: 22px;
        margin-right: 5px;
        line-height: 22px;
      }

      i {
        flex: 1;
        line-height: 22px;
      }

      .notlink {
        background: url('~@/assets/images/monitor/notlink.png') no-repeat;
        background-size: 100% 100%;
      }

      .fault {
        background: url('~@/assets/images/monitor/fault.png') no-repeat;
        background-size: 100% 100%;
      }

      .warn {
        background: url('~@/assets/images/monitor/warn.png') no-repeat;
        background-size: 100% 100%;
      }
    }

    & > span {
      flex: 1;
      text-align: center;
    }
  }

  .card-content {
    display: flex;
    flex-wrap: wrap;
    padding: 10px;
    height: calc(100% - 48px);
    overflow: auto;

    .air_conditioner {
      width: 120px;
      height: 100px;
      text-align: center;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      font-size: 13px;
      padding: 6px 0;

      span {
        font-size: 22px;
        color: #5188fc;
      }

      p {
        padding: 0;
        margin: 0;
        align-items: center;
      }

      p:first-child {
        height: 55px;
        line-height: 55px;
      }

      p {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0;
      }
    }

    .safe_conditioner {
      &:nth-child(4),
      &:nth-child(5) {
        cursor: pointer;

        &:hover {
          box-shadow: 2px 3px 5px 3px #e4e4ec !important;
        }
      }
    }
  }
}
::-webkit-scrollbar-thumb:hover,
::-webkit-scrollbar-track:hover {
  background: transparent;
}
::-webkit-scrollbar-thumb {
  background-color: transparent;
}
.btns {
  width: 200px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-right: 10px;
}

.btns > span {
  width: 22%;
  background-color: #f6f5fa;
  font-size: 14px;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  border: none;
  border-radius: 4px;
  color: #414653;
  cursor: pointer;
}
.complaints {
  width: 100%;
  text-align: center;
  display: flex;
  margin-right: 10px;
  > div {
    flex: 1;
    // padding: 0 10px;
  }
  > div:nth-child(1) {
    border-right: 1px solid #e1e3e9;
  }
  > div:nth-child(2) {
    border-right: 1px solid #e1e3e9;
    margin: 0 30px;
  }
}
.active-btn {
  background-color: #e6effc !important;
  color: #3562db !important;
  border: none !important;
}
.cont {
  width: 100%;
  height: 100%;
  > div {
    height: 50%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    > div {
      width: 49%;
      margin: 0 auto;
      // text-align: center;
      display: flex;
      align-items: center;
      justify-content: space-around;
      height: 90%;
    }
  }
}
.statistics {
  width: 100%;
  height: 100%;
  > div:nth-child(1) {
    background-color: #faf9fc;
    height: 30%;
    width: 100%;
    display: flex;
    align-items: center;
    > div {
      flex: 1;
      text-align: center;
    }
    > div:nth-child(1) {
      border-right: 1px solid #e4e7ed;
    }
  }
  > div:nth-child(2) {
    height: 70%;
    width: 100%;
  }
}
.full-style {
  width: 100%;
  height: 100%;
  .contenWrap {
    display: flex;
    height: 100%;
    .content-left {
      width: 66%;
      height: 100%;
      .item-wrap {
        display: flex;
        height: calc(50% - 5px);
        margin-bottom: 10px;
        .items {
          width: calc(50% - 5px);
          background-color: #ccc;
          display: flex;
          justify-content: center;
          align-items: center;
          .numText {
            text-align: center;
            font-size: 20px;
            font-weight: bold;
          }
          .itemText {
            color: #414653;
          }
        }
        .items:first-child {
          margin-right: 10px;
        }
      }
    }
    .content-right {
      width: 34%;
      height: 100%;
      .chartsWrap {
        margin-left: 10px;
        background-color: #faf9fc;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
      }
    }
  }
}
</style>
