<template>
  <div class="container-content">
    <div class="search-content">
      <div class="search-left">
        <el-input v-model="fieldNameOrId" placeholder="字段名或ID"></el-input>
        <el-select v-model="status" placeholder="全部状态">
          <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
        </el-select>
      </div>
      <div class="search-right">
        <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db; margin-left: 20px" @click="handleReset">重置</el-button>
        <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
      </div>
    </div>
    <div class="btn-box">
      <el-button type="primary" @click="handleAdd">新增</el-button>
      <el-button type="success" size="small" :disabled="checkStartStatus(multipleSelection)" @click="handleStartUsing()">启用</el-button>
      <el-button type="danger" size="small" :disabled="checkStopStatus(multipleSelection)" @click="handleStopUsing()">停用</el-button>
      <el-button type="danger" size="small" :disabled="checkDelStatus(multipleSelection)" @click="handleDel()">删除</el-button>
    </div>
    <div class="table-container">
      <div class="table-box">
        <el-table
          ref="materialTable"
          v-loading="tableLoading"
          :data="tableData"
          height="100%"
          border
          :header-cell-style="{ background: '#F6F5FA' }"
          style="width: 100%"
          :cell-style="{ padding: '8px 0 8px 0' }"
          stripe
          highlight-current-row
          :empty-text="emptyText"
          row-key="id"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55"> </el-table-column>
          <el-table-column show-overflow-tooltip prop="fieldName" label="字段名"></el-table-column>
          <el-table-column show-overflow-tooltip prop="fieldId" label="字段ID"></el-table-column>
          <el-table-column show-overflow-tooltip prop="fieldType" label="类型">
            <template slot-scope="scope">{{ scope.row.fieldType | fieldTypeFilter }}</template>
          </el-table-column>
          <el-table-column prop="fieldStatus" show-overflow-tooltip label="状态">
            <template slot-scope="scope">
              <span :style="{ color: scope.row.fieldStatus == '0' ? '#FA403C' : '#67C23A' }">{{ scope.row.fieldStatus | statusFilter }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="165">
            <template slot-scope="scope">
              <div style="display: flex; gap: 10px">
                <el-link type="primary" :underline="false" @click="operation(scope.row, 'details')"> 查看 </el-link>
                <el-link type="primary" :underline="false" @click="operation(scope.row, 'edit')"> 编辑 </el-link>
                <el-dropdown trigger="click">
                  <span class="el-dropdown-link"> 更多 </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item>
                      <el-link type="success" class="link-stop" :underline="false" v-if="scope.row.fieldStatus == 0" size="small" :disabled="scope.row.fieldStatus == 1"  @click="operation(scope.row, 'start')"> 启用 </el-link>
                      <el-link type="danger" class="link-stop" :underline="false" v-if="scope.row.fieldStatus == 1" size="small" :disabled="scope.row.fieldStatus == 0"  @click="operation(scope.row, 'stop')"> 停用 </el-link>
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <el-link type="danger" class="link-del" :underline="false" size="small" :disabled="scope.row.fieldStatus == 1" @click="operation(scope.row, 'del')"> 删除 </el-link>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="table-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="paginationData.currentPage"
          :page-sizes="[15, 30, 50, 100]"
          :page-size="paginationData.pageSize"
          :total="paginationData.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
    <!-- 弹窗 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="35%" :before-close="handleCloseDialog" :close-on-click-modal="false">
      <div class="content-dialog">
        <el-form ref="ruleForm" :model="ruleForm" :disabled="dialogStatus == 'details'" :rules="rules" label-width="80px">
          <div class="formItem">
            <el-form-item label="字段名 " prop="fieldName">
              <el-input v-model="ruleForm.fieldName" maxlength="50" placeholder="请输入协议名称"></el-input>
            </el-form-item>
            <el-form-item label="编码 " prop="code">
              <el-input v-model="ruleForm.code" maxlength="50" :disabled="dialogStatus != 'add'" placeholder="请输入协议名称"></el-input>
            </el-form-item>
          </div>
          <div class="formItem">
            <el-form-item label="类型 " prop="type">
              <el-select v-model="ruleForm.type" placeholder="请选择类型" :disabled="dialogStatus != 'add'">
                <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="状态 " prop="status">
              <el-select v-model="ruleForm.status" placeholder="请选择状态">
                <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer" v-if="dialogStatus != 'details'">
        <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db" @click="handleCloseDialog">取消</el-button>
        <el-button :loading="saveBtnLoding" :disabled="saveBtnLoding" type="primary" @click="saveDialog">确认</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
export default {
  data() {
    return {
      dialogTitle: '新增扩展字段',
      dialogStatus: '',
      saveBtnLoding: false,
      dialogVisible: false,
      tableLoading: false,
      emptyText: '暂无数据',
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 10
      },
      typeOptions: [
        {
          value: '1',
          label: '单行文本'
        },
        {
          value: '2',
          label: '多行文本'
        }
      ],
      statusOptions: [
        {
          value: '1',
          label: '启用'
        },
        {
          value: '0',
          label: '停用'
        }
      ],
      status: '',
      fieldNameOrId: '',
      tableData: [],
      multipleSelection: [],
      rules: {
        fieldName: [{ required: true, message: '请输入字段名称', trigger: 'blur' }],
        code: [{ required: true, message: '请输入字段编码', trigger: 'blur' }],
        type: [{ required: true, message: '请选择字段类型', trigger: 'change' }]
      },
      ruleForm: {
        id: '',
        fieldName: '',
        code: '',
        type: '',
        status: '1'
      }
    }
  },
  filters: {
    fieldTypeFilter(type) {
      if (!type) return ''
      return type == 1 ? '单行文本' : '多行文本'
    },
    statusFilter(type) {
      if (!type) return ''
      return type == 1 ? '启用' : '停用'
    }
  },
  mounted() {
    this.getListData()
  },
  methods: {
    /** 校验多条数据 删除 */
    checkDelStatus(arr) {
      if (arr && !arr.length) {
        return true
      }
      return arr.some((item) => item.fieldStatus == '1')
    },
    /** 校验多条数据停用 */
    checkStopStatus(arr) {
      if (arr && !arr.length) {
        return true
      }
      return arr.some((item) => item.fieldStatus == '0')
    },
    /** 校验多条数据启用 */
    checkStartStatus(arr) {
      if (arr && !arr.length) {
        return true
      }
      return arr.some((item) => item.fieldStatus == '1')
    },
    /** 重置 */
    handleReset() {
      this.status = ''
      this.fieldNameOrId = ''
      this.paginationData.currentPage = 1
      this.getListData()
    },
    /** 搜索 */
    handleSearch() {
      this.paginationData.currentPage = 1
      this.getListData()
    },
    /** 新增 */
    handleAdd() {
      if (this.paginationData.total >= 20) {
        return this.$message.warning('扩展字段最多支持20个，请删除后再添加！')
      }
      this.dialogTitle = '新增扩展字段'
      this.dialogStatus = 'add'
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['ruleForm'].resetFields()
      })
    },
    /** 启用 */
    handleStartUsing(row) {
      this.$confirm('是否启用该扩展字段?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let params = { idList: row ? [row.id] : this.multipleSelection.map((item) => item.id), state: '1' }
        this.$api.rentalHousingApi.updateExtendFieldStatus(params).then((res) => {
          if (res.code == 200) {
            this.$message.success(res.message)
            this.getListData()
          } else {
            this.$message.error(res.message)
          }
        })
      })
    },
    /** 停用 */
    handleStopUsing(row) {
      this.$confirm('是否停用该扩展字段?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let params = { idList: row ? [row.id] : this.multipleSelection.map((item) => item.id), state: '0' }
        this.$api.rentalHousingApi.updateExtendFieldStatus(params).then((res) => {
          if (res.code == 200) {
            this.$message.success(res.message)
            this.getListData()
          } else {
            this.$message.error(res.message)
          }
        })
      })
    },
    /** 删除 */
    handleDel(row) {
      this.$confirm('此操作将永久删除该扩展字段, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let params = {
          idList: row ? [row.id] : this.multipleSelection.map((item) => item.id)
        }
        this.$api.rentalHousingApi.deleteExtendFieldById(params).then((res) => {
          if (res.code == 200) {
            this.$message.success(res.message)
            this.getListData()
          } else {
            this.$message.error(res.message)
          }
        })
      })
    },
    /** 操作 */
    operation(row, type) {
      let obj = {
        details: '查看扩展字段',
        edit: '编辑扩展字段'
      }
      if (type == 'details' || type == 'edit') {
        this.dialogTitle = obj[type]
        this.dialogVisible = true
        this.$nextTick(() => {
          this.$refs['ruleForm'].resetFields()
          this.ruleForm = {
            id: row.id,
            fieldName: row.fieldName,
            code: row.fieldId,
            type: row.fieldType,
            status: row.fieldStatus
          }
        })
      }
      if (type == 'start') this.handleStartUsing(row)
      if (type == 'stop') this.handleStopUsing(row)
      if (type == 'del') this.handleDel(row)
      this.dialogStatus = type
    },
    /** 弹窗 确认 */
    saveDialog() {
      this.saveBtnLoding = true
      this.$refs.ruleForm.validate((valid) => {
        this.saveBtnLoding = false
        if (valid) {
          let params = {
            fieldName: this.ruleForm.fieldName || '',
            fieldId: this.ruleForm.code || '',
            fieldType: this.ruleForm.type || '',
            fieldStatus: this.ruleForm.status || '',
            id: this.ruleForm.id || ''
          }
          let apiObj = {
            add: 'saveExtendFieldInfo',
            edit: 'updateExtendFieldInfo'
          }
          this.$api.rentalHousingApi[apiObj[this.dialogStatus]](params).then((res) => {
            if (res.code == 200) {
              this.$message.success(res.message)
              this.getListData()
              this.handleCloseDialog()
            } else {
              this.$message.error(res.message)
            }
          })
        }
      })
    },
    /** 弹窗 取消 */
    handleCloseDialog() {
      this.ruleForm.id = ''
      this.$refs['ruleForm'].resetFields()
      this.dialogVisible = false
    },
    /** 获取列表数据 */
    getListData() {
      let params = {
        fieldNameOrId: this.fieldNameOrId,
        fieldStatus: this.status,
        pageNum: this.paginationData.currentPage,
        pageSize: this.paginationData.pageSize
      }
      this.tableLoading = true
      this.$api.rentalHousingApi.queryExtendFieldByPage(params).then((res) => {
        this.tableLoading = false
        if (res.code == 200) {
          this.tableData = res.data.records
          this.paginationData.total = res.data.total
        } else {
          this.$message.error(res.message)
        }
      })
    },
    /** 多选事件 */
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    /** 条数变化事件 */
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.getListData()
    },
    /** 页数变化事件 */
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.getListData()
    }
  }
}
</script>
<style lang="scss" scoped>
.container-content {
  height: 100%;
  overflow-y: auto;
  .search-content {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    .search-left {
      display: flex;
      .el-input {
        margin-right: 10px;
      }
    }
  }
  .table-container {
    margin-top: 10px;
    height: calc(100% - 150px);
    .table-box {
      height: 100%;
    }
    .table-pagination {
      margin-top: 10px;
      text-align: right;
    }
  }
}
.el-dropdown-link,
.el-link:not(.link-stop, .link-del) {
  color: #3562db;
  cursor: pointer;
}
.formItem {
  display: flex;
  .el-form-item {
    width: calc(100% / 2);
  }
  .el-select {
    width: 100%;
  }
}
</style>