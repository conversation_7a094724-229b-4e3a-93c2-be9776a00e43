<template>
  <PageContainer>
    <div slot="content" class="top">
      <div class="left_side">
        <el-tree ref="projectTree" v-scrollbarHover :data="alarmSourceOptions" :props="defaultProps" highlight-current node-key="projectCode" @node-click="handleNodeClick">
        </el-tree>
      </div>
      <div class="right_side">
        <el-table v-loading="loading" :data="tableData" style="width: 100%" border stripe :header-cell-style="{ 'text-align': 'center' }">
          <el-table-column prop="jibie" label="报警等级" min-width="100" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.alarmLevel == '3'" class="djstyle">重要</span>
              <span v-if="scope.row.alarmLevel == '2'" class="djstyle3">紧急</span>
              <span v-if="scope.row.alarmLevel == '1'" class="djstyle2">一般</span>
              <span v-if="scope.row.alarmLevel == '0'" class="djstyle2">通知</span>
            </template>
          </el-table-column>
          <el-table-column prop="" label="是否弹出警单" min-width="140" align="center">
            <template slot-scope="scope">
              <el-switch v-model="scope.row.isClient" active-color="#3562DB" inactive-color="#dcdfe6" :active-value="1" :inactive-value="0" @change="changeStatus(scope.row)">
              </el-switch>
            </template>
          </el-table-column>
          <el-table-column prop="" label="联动监控画面" min-width="140" align="center">
            <template slot-scope="scope">
              <el-switch v-model="scope.row.monitor" active-color="#3562DB" inactive-color="#dcdfe6" :active-value="1" :inactive-value="0" @change="changeStatus(scope.row)">
              </el-switch>
            </template>
          </el-table-column>
          <el-table-column prop="" label="语音播报" align="center" min-width="120">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.broadcastContent"
                active-color="#3562DB"
                inactive-color="#dcdfe6"
                :active-value="1"
                :inactive-value="0"
                @change="changeStatus(scope.row)"
              >
              </el-switch>
            </template>
          </el-table-column>
          <el-table-column prop="autoSendOrder" label="自动派单" align="center" min-width="120">
            <template slot-scope="scope">
              <el-switch v-model="scope.row.autoSendOrder" active-color="#3562DB" inactive-color="#dcdfe6" :active-value="1" :inactive-value="0" @change="changeStatus(scope.row)">
              </el-switch>
            </template>
          </el-table-column>
          <el-table-column prop="checkFlow" label="排查流程" align="center" min-width="120">
            <template slot-scope="scope">
              <el-switch v-model="scope.row.checkFlow" active-color="#3562DB" inactive-color="#dcdfe6" :active-value="1" :inactive-value="0" @change="changeStatus(scope.row)">
              </el-switch>
            </template>
          </el-table-column>
          <el-table-column prop="" label="启动应急预案" align="center" min-width="140">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.contingencyPlan"
                active-color="#3562DB"
                inactive-color="#dcdfe6"
                :active-value="1"
                :inactive-value="0"
                @change="changeStatus(scope.row)"
              >
              </el-switch>
            </template>
          </el-table-column>
          <el-table-column prop="" label="消息通知" align="center" width="400px">
            <template slot-scope="scope">
              <span
                >短信
                <el-switch v-model="scope.row.shortMessage" active-color="#3562DB" inactive-color="#dcdfe6" :active-value="1" :inactive-value="0" @change="changeStatus(scope.row)">
                </el-switch></span
              >&nbsp;&nbsp;&nbsp;
              <span
                >微信
                <el-switch v-model="scope.row.wxMessage" active-color="#3562DB" inactive-color="#dcdfe6" :active-value="1" :inactive-value="0" @change="changeStatus(scope.row)">
                </el-switch
              ></span>
              &nbsp;&nbsp;&nbsp;<span
                >电话
                <el-switch v-model="scope.row.phoneMessage" active-color="#3562DB" inactive-color="#dcdfe6" :active-value="1" :inactive-value="0" @change="changeStatus(scope.row)">
                </el-switch
              ></span>
              &nbsp;&nbsp;&nbsp;<span
                >APP
                <el-switch v-model="scope.row.appMessage" active-color="#3562DB" inactive-color="#dcdfe6" :active-value="1" :inactive-value="0" @change="changeStatus(scope.row)">
                </el-switch
              ></span>
            </template>
          </el-table-column>
          <el-table-column label="更多" align="center">
            <template slot-scope="scope">
              <span class="sty" @click="configure(scope.row)">配置</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-dialog title="报警联动配置" :visible.sync="dialogVisible" width="60%" :before-close="handleClose" custom-class="model-dialog">
        <div class="dialogStyle">
          <div class="dialog-t">
            <p>系统名称：{{ alarmSourceOptions.length && alarmSourceOptions.find((e) => e.projectCode == treeId).projectName }}</p>
            <p>
              严重等级： <span v-if="tableRow.alarmLevel == '3'" class="djstyle" style="text-align: center">重要</span>
              <span v-if="tableRow.alarmLevel == '2'" class="djstyle3" style="text-align: center">紧急</span>
              <span v-if="tableRow.alarmLevel == '1'" class="djstyle2" style="text-align: center">一般</span>
              <span v-if="tableRow.alarmLevel == '0'" class="djstyle2" style="text-align: center">通知</span>
            </p>
          </div>
          <div class="dialog-c">
            <div style="border-top: 1px solid #dcdfe6" class="div">
              <p class="txt">是否弹出警单</p>
              <p>
                <el-switch v-model="tableRow.isClient" active-color="#3562DB" inactive-color="#dcdfe6" :active-value="1" :inactive-value="0"> </el-switch>
              </p>
              &nbsp;&nbsp;
              <p>{{ tableRow.monitor == '0' ? '否' : '是' }}</p>
            </div>
            <div style="border-top: 1px solid #dcdfe6" class="div">
              <p class="txt">联动视频画面</p>
              <p>
                <el-switch v-model="tableRow.monitor" active-color="#3562DB" inactive-color="#dcdfe6" :active-value="1" :inactive-value="0"> </el-switch>
              </p>
              &nbsp;&nbsp;
              <p>{{ tableRow.monitor == '0' ? '否' : '是' }}</p>
            </div>

            <div class="div" style="display: flex; align-items: center">
              <p class="txt">语音播报</p>
              <p><el-switch v-model="tableRow.broadcastContent" active-color="#3562DB" inactive-color="#dcdfe6" :active-value="1" :inactive-value="0"> </el-switch></p>
              &nbsp;&nbsp;
              <p>{{ tableRow.broadcastContent == '0' ? '否' : '是' }}</p>
              <p class="jianju2">播报次数</p>
              <p>
                <el-input-number
                  v-model="tableRow.broadcastNum"
                  controls-position="right"
                  :min="1"
                  :max="10"
                  size="mini"
                  style="width: 80px"
                  @change="handleChange"
                ></el-input-number>
              </p>
              <p style="color: #7f848c">&nbsp;默认播报示例：急诊急救综合楼5层空调机房 空调机组001 设备故障报警， 请及时处理</p>
            </div>
            <div class="div">
              <p class="txt">自动派单</p>
              <p>
                <el-switch v-model="tableRow.autoSendOrder" active-color="#3562DB" inactive-color="#dcdfe6" :active-value="1" :inactive-value="0"> </el-switch>
              </p>
              &nbsp;&nbsp;
              <p>
                {{ tableRow.autoSendOrder == '0' ? '否' : '是' }}
              </p>
            </div>
            <div class="div">
              <p class="txt">标准检查流程</p>
              <p>
                <el-switch v-model="tableRow.checkFlow" active-color="#3562DB" inactive-color="#dcdfe6" :active-value="1" :inactive-value="0"></el-switch>
              </p>
              &nbsp;&nbsp;
              <p>{{ tableRow.checkFlow == '0' ? '否' : '是' }}</p>
            </div>
            <div class="div">
              <p class="txt">启动应急预案</p>
              <p><el-switch v-model="tableRow.contingencyPlan" active-color="#3562DB" inactive-color="#dcdfe6" :active-value="1" :inactive-value="0"></el-switch></p>
              &nbsp;&nbsp;
              <p>{{ tableRow.contingencyPlan == '0' ? '否' : '是' }}</p>
            </div>
            <div class="div" style="margin-top: 20px; border-top: 1px solid #dcdfe6; background-color: #f6f5fa">
              <p>消息通知</p>
            </div>
            <div class="wxNotice">
              <p>
                短信通知 <el-switch v-model="tableRow.shortMessage" active-color="#3562DB" inactive-color="#dcdfe6" :active-value="1" :inactive-value="0"></el-switch>
                {{ tableRow.shortMessage == '0' ? '否' : '是' }}
              </p>
              <div v-if="tableRow.shortMessage == 1">
                <div v-for="item in dxNoticeData" :key="item.id">
                  <div>
                    <span style="color: #3562db; display: inline-block; width: 230px; height: 100%; text-align: right"
                      >{{ item.label }}&nbsp; 发送至：&nbsp;<el-button type="primary" plain @click="open(item.id, item.type)">请选择</el-button>&nbsp;</span
                    >
                    <span style="display: inline-block; width: 70%">{{ item.value.toString() }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="wxNotice">
              <p>
                微信通知 <el-switch v-model="tableRow.wxMessage" active-color="#3562DB" inactive-color="#dcdfe6" :active-value="1" :inactive-value="0"></el-switch>
                {{ tableRow.wxMessage == '0' ? '否' : '是' }}
              </p>
              <div v-if="tableRow.wxMessage == 1">
                <div v-for="item in wxNoticeData" :key="item.id">
                  <div>
                    <span style="color: #3562db; display: inline-block; width: 230px; height: 100%; text-align: right"
                      >{{ item.label }}&nbsp; 发送至：&nbsp;<el-button type="primary" plain @click="open(item.id, item.type)">请选择</el-button>&nbsp;</span
                    >
                    <span style="display: inline-block; width: 70%">{{ item.value.toString() }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="wxNotice">
              <p>
                电话通知 <el-switch v-model="tableRow.phoneMessage" active-color="#3562DB" inactive-color="#dcdfe6" :active-value="1" :inactive-value="0"></el-switch>
                {{ tableRow.phoneMessage == '0' ? '否' : '是' }}
              </p>
              <div v-if="tableRow.phoneMessage == 1">
                <div v-for="item in phoneNoticeData" :key="item.id">
                  <div>
                    <span style="color: #3562db; display: inline-block; width: 230px; height: 100%; text-align: right"
                      >{{ item.label }}&nbsp; 发送至：&nbsp;<el-button type="primary" plain @click="open(item.id, item.type)">请选择</el-button>&nbsp;</span
                    >
                    <span style="display: inline-block; width: 70%">{{ item.value.toString() }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="wxNotice">
              <p>
                APP通知 <el-switch v-model="tableRow.appMessage" active-color="#3562DB" inactive-color="#dcdfe6" :active-value="1" :inactive-value="0"></el-switch>
                {{ tableRow.appMessage == '0' ? '否' : '是' }}
              </p>
              <div v-if="tableRow.appMessage == 1">
                <div v-for="item in appNoticeData" :key="item.id">
                  <div>
                    <span style="color: #3562db; display: inline-block; width: 230px; height: 100%; text-align: right"
                      >{{ item.label }}&nbsp; 发送至：&nbsp;<el-button type="primary" plain @click="open(item.id, item.type)">请选择</el-button>&nbsp;</span
                    >
                    <span style="display: inline-block; width: 70%">{{ item.value.toString() }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="confirm">确 定</el-button>
        </span>
      </el-dialog>
      <dialogConfig :visible="visible" :type="type" :messageType="messageType" @updateVisible="updateVisible" @advancedSearchFn="advancedSearchFn"></dialogConfig>
    </div>
  </PageContainer>
</template>
<script>
import dialogConfig from './components/dialogConfig.vue'
export default {
  name: 'LinkageConfigIndex',
  components: {
    dialogConfig
  },
  data() {
    return {
      loading: true,
      type: '',
      messageType: '',
      treeId: '', // 当前选中节点
      visible: false,
      // 短信
      qj: true,
      wb: true,
      dxNoticeData: [
        {
          id: '0',
          label: '立即',
          value: [],
          type: 'dx'
        },
        {
          id: '1',
          label: '真实报警',
          value: [],
          type: 'dx'
        },
        {
          id: '2',
          label: '误报',
          value: [],
          type: 'dx'
        },
        {
          id: '3',
          label: '演练',
          value: [],
          type: 'dx'
        },
        {
          id: '4',
          label: '调试',
          value: [],
          type: 'dx'
        }
      ],
      // 微信
      qjs: true,
      wbs: true,
      wxNoticeData: [
        {
          id: '0',
          label: '立即',
          value: [],
          type: 'wx'
        },
        {
          id: '1',
          label: '真实报警',
          value: [],
          type: 'wx'
        },
        {
          id: '2',
          label: '误报',
          type: 'wx',
          value: []
        },
        {
          id: '3',
          label: '演练',
          value: [],
          type: 'wx'
        },
        {
          id: '4',
          label: '调试',
          value: [],
          type: 'wx'
        }
      ],
      phoneNoticeData: [
        {
          id: '0',
          label: '立即',
          value: [],
          type: 'phone'
        },
        {
          id: '1',
          label: '真实报警',
          value: [],
          type: 'phone'
        },
        {
          id: '2',
          label: '误报',
          type: 'phone',
          value: []
        },
        {
          id: '3',
          label: '演练',
          value: [],
          type: 'phone'
        },
        {
          id: '4',
          label: '调试',
          value: [],
          type: 'phone'
        }
      ],
      appNoticeData: [
        {
          id: '0',
          label: '立即',
          value: [],
          type: 'app'
        },
        {
          id: '1',
          label: '真实报警',
          value: [],
          type: 'app'
        },
        {
          id: '2',
          label: '误报',
          type: 'app',
          value: []
        },
        {
          id: '3',
          label: '演练',
          value: [],
          type: 'app'
        },
        {
          id: '4',
          label: '调试',
          value: [],
          type: 'app'
        }
      ],
      shortNote: '1', // 0开启1关闭
      weChatNotice: '1', // 0开启1关闭
      num: 1,
      swith: '0', // 0开启1关闭
      dialogVisible: false,
      defaultProps: {
        children: 'childrens',
        label: 'projectName',
        value: 'projectCode'
      },
      alarmSourceOptions: [],
      alarmLevelItem: {
        0: { text: '通知', color: '#3562DB' },
        1: { text: '一般', color: '#3562DB' },
        2: { text: '紧急', color: '#FF9435' },
        3: { text: '重要', color: '#FA403C' }
      },
      tableData: [],
      tableRow: '',
      limAlarmInformList: [],
      changeStatu: ''
    }
  },
  created() {},
  mounted() {
    this.getAlarmSource()
    // this.saveLinkageList()
  },
  methods: {
    // 获取报警来源
    getAlarmSource() {
      this.$api.getSourceByEmpty().then((res) => {
        if (res.code == 200) {
          this.alarmSourceOptions = res.data
          this.treeId = this.alarmSourceOptions[0].projectCode
          this.$nextTick(() => {
            this.$refs.projectTree.setCurrentKey(this.treeId)
          })
          this.getLinkageList()
        }
      })
    },
    handleNodeClick(data, node) {
      this.treeId = data.projectCode
      this.getLinkageList()
    },
    confirm() {
      this.saveLinkageList()
      this.dialogVisible = false
    },
    changeStatus(row) {
      this.$api.saveAlarmLinkage(row).then((res) => {
        if (res.code == 200) {
          this.$message({
            message: '修改配置成功',
            type: 'success'
          })
          this.getLinkageList()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 保存联动配置信息
    saveLinkageList() {
      this.tableRow.limAlarmInformList = this.limAlarmInformList
      this.$api.saveAlarmLinkage(this.tableRow).then((res) => {
        if (res.code == 200) {
          this.$message({
            message: '修改配置成功',
            type: 'success'
          })
          this.getLinkageList()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 查询联动配置信息
    getLinkageList() {
      let params = {
        projectCode: this.treeId
      }
      this.$api.getLimAlarmLinkageByProject(params).then((res) => {
        this.loading = false
        if (res.code == 200) {
          this.tableData = res.data
        }
      })
    },
    advancedSearchFn(form, type, messageType) {
      let dxinformPersonAppId = []
      let dxinformPersonId = []
      let dxinformPersonIphone = []
      let dxinformPersonName = []
      let wxinformPersonAppId = []
      let wxinformPersonId = []
      let wxinformPersonIphone = []
      let wxinformPersonName = []
      let phoneinformPersonId = []
      let phoneinformPersonIphone = []
      let phoneinformPersonName = []
      let appinformPersonId = []
      let appinformPersonIphone = []
      let appinformPersonName = []
      if (messageType == 'dx') {
        let obj = {}
        this.dxNoticeData.forEach((item) => {
          if (item.id == type && item.type == messageType) {
            item.value = []
            form.forEach((item2) => {
              item.value.push(item2.staffName)
              dxinformPersonId.push(item2.id)
              dxinformPersonIphone.push(item2.mobile)
              dxinformPersonName.push(item2.staffName)
              obj.alarmType = item.id
              obj.informPersonAppId = ''
              obj.informPersonId = dxinformPersonId.toString()
              obj.informPersonIphone = dxinformPersonIphone.toString()
              obj.informPersonName = dxinformPersonName.toString()
              obj.messageType = 0
              obj.lalId = this.tableRow.id
              obj.projectCode = this.tableRow.projectCode
              this.limAlarmInformList.forEach((a) => {
                if (a.messageType == 0 && a.alarmType == obj.alarmType) {
                  this.limAlarmInformList.splice(a, 1)
                }
              })
              this.limAlarmInformList.push(obj)
            })
          }
        })
      } else if (messageType == 'wx') {
        let obj2 = {}
        this.wxNoticeData.forEach((item) => {
          if (item.id == type && item.type == messageType) {
            item.value = []
            form.forEach((item2) => {
              item.value.push(item2.staffName)
              wxinformPersonId.push(item2.id)
              wxinformPersonIphone.push(item2.mobile)
              wxinformPersonName.push(item2.staffName)
              obj2.alarmType = item.id
              obj2.informPersonAppId = ''
              obj2.informPersonId = wxinformPersonId.toString()
              obj2.informPersonIphone = wxinformPersonIphone.toString()
              obj2.informPersonName = wxinformPersonName.toString()
              obj2.messageType = 1
              obj2.lalId = this.tableRow.id
              obj2.projectCode = this.tableRow.projectCode
              this.limAlarmInformList.forEach((a) => {
                if (a.messageType == 1 && a.alarmType == obj2.alarmType) {
                  this.limAlarmInformList.splice(a, 1)
                }
              })
              this.limAlarmInformList.push(obj2)
            })
          }
        })
      } else if (messageType == 'phone') {
        let obj2 = {}
        this.phoneNoticeData.forEach((item) => {
          if (item.id == type && item.type == messageType) {
            item.value = []
            form.forEach((item2) => {
              item.value.push(item2.staffName)
              phoneinformPersonId.push(item2.id)
              phoneinformPersonIphone.push(item2.mobile)
              phoneinformPersonName.push(item2.staffName)
              obj2.alarmType = item.id
              obj2.informPersonAppId = ''
              obj2.informPersonId = phoneinformPersonId.toString()
              obj2.informPersonIphone = phoneinformPersonIphone.toString()
              obj2.informPersonName = phoneinformPersonName.toString()
              obj2.messageType = 2
              obj2.lalId = this.tableRow.id
              obj2.projectCode = this.tableRow.projectCode
              this.limAlarmInformList.forEach((a) => {
                if (a.messageType == 2 && a.alarmType == obj2.alarmType) {
                  this.limAlarmInformList.splice(a, 1)
                }
              })
              this.limAlarmInformList.push(obj2)
            })
          }
        })
      } else {
        let obj2 = {}
        this.appNoticeData.forEach((item) => {
          if (item.id == type && item.type == messageType) {
            item.value = []
            form.forEach((item2) => {
              item.value.push(item2.staffName)
              appinformPersonId.push(item2.id)
              appinformPersonIphone.push(item2.mobile)
              appinformPersonName.push(item2.staffName)
              obj2.alarmType = item.id
              obj2.informPersonAppId = ''
              obj2.informPersonId = appinformPersonId.toString()
              obj2.informPersonIphone = appinformPersonIphone.toString()
              obj2.informPersonName = appinformPersonName.toString()
              obj2.messageType = 3
              obj2.lalId = this.tableRow.id
              obj2.projectCode = this.tableRow.projectCode
              this.limAlarmInformList.forEach((a) => {
                if (a.messageType == 3 && a.alarmType == obj2.alarmType) {
                  this.limAlarmInformList.splice(a, 1)
                }
              })
              this.limAlarmInformList.push(obj2)
            })
          }
        })
      }
    },
    updateVisible() {
      this.visible = !this.visible
      this.dialogVisible = !this.dialogVisible
    },
    open(val, messageType) {
      this.type = ''
      this.messageType = ''
      this.type = val
      this.messageType = messageType
      this.visible = true
      this.dialogVisible = false
    },
    configure(row) {
      // this.tableRow = ' '
      this.tableRow = row
      this.dxNoticeData.forEach((item) => {
        item.value = []
        this.tableRow.limAlarmInformList.forEach((item2) => {
          if (item2.messageType == 0) {
            if (item.id == item2.alarmType) {
              item.value.push(item2.informPersonName)
            }
          }
        })
      })
      this.wxNoticeData.forEach((item) => {
        item.value = []
        this.tableRow.limAlarmInformList.forEach((item2) => {
          if (item2.messageType == 1) {
            if (item.id == item2.alarmType) {
              item.value.push(item2.informPersonName)
            }
          }
        })
      })
      this.phoneNoticeData.forEach((item) => {
        item.value = []
        this.tableRow.limAlarmInformList.forEach((item2) => {
          if (item2.messageType == 2) {
            if (item.id == item2.alarmType) {
              item.value.push(item2.informPersonName)
            }
          }
        })
      })
      this.appNoticeData.forEach((item) => {
        item.value = []
        this.tableRow.limAlarmInformList.forEach((item2) => {
          if (item2.messageType == 3) {
            if (item.id == item2.alarmType) {
              item.value.push(item2.informPersonName)
            }
          }
        })
      })
      this.dialogVisible = true
    },
    handleClose() {
      this.getLinkageList()
      this.dialogVisible = false
    },
    handleChange(value) {
      this.tableRow.broadcastNum = value
    }
  }
}
</script>
<style lang="scss" scoped>
.top {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
}

.left_side {
  width: 195px;
  height: 100%;
  background: #fff;
  border-radius: 10px;
}

.el-tree {
  margin-top: 20px;
  height: calc(100% - 40px);
  overflow-y: auto;
}

::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: linear-gradient(to right, #d9e1f8, #fff);
  font-weight: bold;
}

.right_side {
  border-radius: 10px;
  // width: 86.5%;
  width: calc(100% - 205px);
  height: 100%;
  background: #fff;
  padding: 15px;
}

.sty {
  cursor: pointer;
  color: #3562db;
}

::v-deep .el-dialog__body {
  padding: 0 !important;
}

.jianju {
  margin-left: 100px;
  line-height: 40px;
}

.jianju2 {
  margin-left: 20px;
  width: 70px;
  display: inline-block;
}

.jianju3 {
  margin-left: 100px;
}

.djstyle {
  display: inline-block;
  width: 40px;
  height: 20px;
  line-height: 20px;
  background: #fa403c;
  border-radius: 2px;
  color: #fff;
}

.djstyle2 {
  display: inline-block;
  width: 40px;
  height: 20px;
  line-height: 20px;
  background: #3562db;
  border-radius: 2px;
  color: #fff;
}

.djstyle3 {
  display: inline-block;
  width: 40px;
  height: 20px;
  line-height: 20px;
  background: #ff9435;
  border-radius: 2px;
  color: #fff;
}

.dialogStyle {
  width: 100%;
  padding: 0 20px;

  .dialog-t {
    margin: 10px 0;
    height: 50px;
    background-color: #fff;
    padding: 0 10px;
    border-radius: 4px;
    display: flex;

    p {
      margin: 0 !important;
      height: 50px;
      line-height: 50px;
      width: 50%;
    }
  }

  .dialog-c {
    padding: 5px 10px;
    background-color: #fff;

    .div {
      line-height: 25px;
      padding-left: 15px;
      display: flex;
      border-left: 1px solid #dcdfe6;
      border-right: 1px solid #dcdfe6;
      border-bottom: 1px solid #dcdfe6;

      .txt {
        width: 100px;
      }
    }
  }
}

.wxNotice {
  border-left: 1px solid #dcdfe6;
  border-right: 1px solid #dcdfe6;
  border-bottom: 1px solid #dcdfe6;
  padding-left: 60px;
  width: 100%;
  display: flex;
  align-items: center;
}

.wxNotice > div {
  width: 100%;
  line-height: 45px;
  padding-left: 10px;
}

.wxNotice > p {
  width: 150px;
}

.wxNotice > div > div {
  width: 100%;
  margin: 10px 0;
  border-radius: 5px;
  background-color: #faf9fc;
}

.wxNotice > div > div > div {
  display: flex;
  align-items: center;
}

.alarmLevel {
  padding: 3px 6px;
  border-radius: 4px;
  color: #fff;
  line-height: 14px;
}
</style>
