<template>
  <PageContainer :footer="true">
    <div slot="content" ref="contentRef" class="contentBox">
      <h1>施工审批提醒配置</h1>
      <el-form ref="form" label-width="160px" class="form_class">
        <div class="parameter_row">
          <el-form-item label="是否开启提醒" class="is-required">
            <el-radio v-model="state" :label="1">关闭</el-radio>
            <el-radio v-model="state" :label="0">开启</el-radio>
          </el-form-item>
        </div>
        <div class="parameter_row">
          <el-form-item label="提醒方式">
            <el-checkbox-group v-model="checkList">
              <el-checkbox label="1"> App</el-checkbox>
              <el-checkbox label="2">短信</el-checkbox>
              <el-checkbox label="3" disabled>微信</el-checkbox>
              <el-checkbox label="4">小程序</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div slot="footer">
      <el-button type="primary" @click="submitForm()">确认</el-button>
    </div>
  </PageContainer>
</template>
<script>
export default {
  name: 'WorkAduitConfig',
  data() {
    return {
      state: 0,
      checkList: ['1']
    }
  },
  mounted() {
    this.getConfigById()
  },
  methods: {
    getConfigById() {
      this.$api.getConstructionApprovalConfigById({}).then(res => {
        if (res.code == '200') {
          this.state = res.data.state
          this.checkList = res.data.way ? res.data.way.split(',') : []
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    submitForm() {
      let params = {
        state: this.state,
        way: this.checkList.join(',')
      }
      this.$api.saveConstructionApprovalConfig(params).then(res => {
        if (res.code == '200') {
          this.$message.success(res.msg)
          this.getConfigById()
        } else {
          this.$message.error(res.msg)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.contentBox {
  height: 100%;
  width: 100%;
  padding: 20px;
  background: #ffffff;
}
</style>
