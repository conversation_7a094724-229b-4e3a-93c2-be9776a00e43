<template>
  <el-dialog :title="title" :visible.sync="dialogVisible" width="900px" append-to-body destroy-on-close>
    <div class="position-dialog-container">
      <div class="selection-area">
        <div class="title">{{ leftTitle }}</div>
        <div class="search-area">
          <el-input v-model="searchKeyword" :placeholder="searchPlaceholder" prefix-icon="el-icon-search" clearable />
        </div>
        <div v-loading="loading" class="tree-area">
          <el-tree
            ref="tree"
            :data="treeData"
            :props="defaultProps"
            node-key="id"
            show-checkbox
            :expand-on-click-node="false"
            :check-strictly="checkStrictly"
            :default-expand-all="dialogType === 'duty'"
            :highlight-current="true"
            :filter-node-method="filterNode"
            @check="handleCheck"
          />
        </div>
      </div>
      <div class="selected-area">
        <div style="display: flex; justify-content: space-between">
          <div class="title">已选：{{ selectedCount }}个{{ itemName }}</div>
          <!-- 清空 -->
          <div style="color: #3562db; cursor: pointer" @click="clearSelection">清空</div>
        </div>
        <div class="selected-list">
          <div v-if="selectedItems.length === 0" class="empty-tip">
            <span>{{ emptyTip }}</span>
          </div>
          <div v-else class="selected-item-list">
            <div v-for="item in selectedItems" :key="item.id" class="selected-item">
              <span>{{ item.name }}</span>
              <el-button type="text" icon="el-icon-close" @click="removeItem(item)" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </el-dialog>
</template>
<script>
export default {
  name: 'PositionSelectDialog',
  props: {
    // 是否显示弹窗
    visible: {
      type: Boolean,
      default: false
    },
    // 弹窗标题
    title: {
      type: String,
      default: '选择'
    },
    // 左侧标题
    leftTitle: {
      type: String,
      default: '选择岗位'
    },
    // 搜索框占位符
    searchPlaceholder: {
      type: String,
      default: '搜索岗位名称'
    },
    // 选项类型名称
    itemName: {
      type: String,
      default: '岗位'
    },
    // 空数据提示
    emptyTip: {
      type: String,
      default: '编辑时要显示已选岗位'
    },
    // 数据 - 保留这个属性以向后兼容
    data: {
      type: Array,
      default: () => []
    },
    // 初始已选项
    initialSelected: {
      type: Array,
      default: () => []
    },
    // 弹窗类型：position-岗位，duty-值班岗
    dialogType: {
      type: String,
      default: 'position'
    },
    // 是否在父子节点之间严格遵循父子不关联的做法
    checkStrictly: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      dialogVisible: false,
      searchKeyword: '',
      loading: false,
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'name' // 先默认使用name，后面会根据数据结构动态调整
      },
      selectedItems: [],
      tempSelectedItems: []
    }
  },
  computed: {
    selectedCount() {
      return this.selectedItems.length
    }
  },
  watch: {
    visible: {
      immediate: true,
      handler(val) {
        this.dialogVisible = val
        if (val) {
          this.initData()
        }
      }
    },
    dialogType: {
      immediate: true,
      handler(val) {
        // 根据弹窗类型设置不同的label属性
        if (val === 'duty') {
          this.defaultProps.label = 'dutyPostName'
        } else {
          this.defaultProps.label = 'name'
        }
      }
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit('update:visible', false)
      }
    },
    searchKeyword(val) {
      if (this.$refs.tree) {
        this.$refs.tree.filter(val)
      }
    }
  },
  methods: {
    // 初始化数据
    initData() {
      this.loading = true
      // 根据弹窗类型调用不同接口
      if (this.dialogType === 'position') {
        // 调用岗位接口
        this.getPositionData()
      } else if (this.dialogType === 'duty') {
        // 调用值班岗接口
        this.getDutyPostData()
      } else {
        // 使用传入的数据
        this.treeData = this.formatTreeData(this.data)
        this.setInitialSelected()
      }
    },
    // 获取岗位数据
    getPositionData() {
      this.$api.supplierAssess
        .getPostChildData()
        .then((res) => {
          this.loading = false
          if (res.code == '200' && res.data.length > 0) {
            this.treeData = this.formatTreeData(res.data)
            this.setInitialSelected()
          } else {
            this.$message.error('获取岗位数据失败')
            this.treeData = []
          }
        })
        .catch(() => {
          this.loading = false
          this.$message.error('获取岗位数据失败')
          this.treeData = []
        })
    },
    // 获取值班岗数据
    getDutyPostData() {
      this.$api.supplierAssess
        .getDutyPostData()
        .then((res) => {
          this.loading = false
          if (res.code == '200' && res.data.length > 0) {
            this.treeData = this.formatTreeData(res.data)
            this.setInitialSelected()
          } else {
            this.$message.error('获取值班岗数据失败')
            this.treeData = []
          }
        })
        .catch(() => {
          this.loading = false
          this.$message.error('获取值班岗数据失败')
          this.treeData = []
        })
    },
    // 设置初始选中状态
    setInitialSelected() {
      this.selectedItems = JSON.parse(JSON.stringify(this.initialSelected))
      this.tempSelectedItems = JSON.parse(JSON.stringify(this.initialSelected))
      this.$nextTick(() => {
        if (this.$refs.tree && this.initialSelected.length > 0) {
          const selectedIds = this.initialSelected.map((item) => item.id || item.code)
          this.$refs.tree.setCheckedKeys(selectedIds)
        }
      })
    },
    // 格式化树形数据
    formatTreeData(data) {
      // 根据不同的数据结构进行处理
      if (this.dialogType === 'position') {
        // 处理岗位数据，确保节点有正确的id和name属性
        return this.processPositionData(data)
      } else if (this.dialogType === 'duty') {
        // 处理值班岗数据
        return this.processDutyPostData(data)
      }
      return data
    },
    // 处理岗位数据
    processPositionData(data) {
      const processNode = (node) => {
        const result = {
          ...node,
          id: node.code || node.id, // 使用code作为id
          name: node.name || node.postName || '' // 确保有name属性
        }
        // 将child属性转换为children
        if (node.child && node.child.length > 0) {
          result.children = node.child.map((child) => processNode(child))
          // 删除原来的child属性
          delete result.child
        }
        return result
      }
      return data.map((item) => processNode(item))
    },
    // 处理值班岗数据
    processDutyPostData(data) {
      const processNode = (node) => {
        // 确保ID和名称字段存在
        const id = node.id || node.code
        const name = node.dutyPostName || node.name || ''
        const result = {
          ...node,
          id: id,
          name: name // 确保有name属性以便显示
        }
        // 将child属性转换为children（如果存在）
        if (node.child && node.child.length > 0) {
          result.children = node.child.map((child) => processNode(child))
          // 删除原来的child属性
          delete result.child
        } else if (node.children && node.children.length > 0) {
          result.children = node.children.map((child) => processNode(child))
        }
        return result
      }
      const processedData = data.map((item) => processNode(item))
      return processedData
    },
    // 过滤节点
    filterNode(value, data) {
      if (!value) return true
      const label = this.dialogType === 'position' ? data.name || data.postName || '' : data.dutyPostName || data.name || ''
      return label.toLowerCase().indexOf(value.toLowerCase()) !== -1
    },
    // 处理选择
    handleCheck(data, checked) {
      // 获取当前所有选中的节点
      const checkedNodes = this.$refs.tree.getCheckedNodes()
      // 根据弹窗类型处理节点数据
      if (this.dialogType === 'position') {
        // 岗位选择时，如果是严格模式，则可以选择任意节点
        if (this.checkStrictly) {
          this.tempSelectedItems = [...checkedNodes]
        } else {
          // 非严格模式下，只保留叶子节点
          this.tempSelectedItems = checkedNodes.filter((node) => !node.children || node.children.length === 0)
        }
      } else if (this.dialogType === 'duty') {
        // 值班岗选择可以选择任何节点
        this.tempSelectedItems = [...checkedNodes]
      } else {
        // 默认只保留叶子节点
        this.tempSelectedItems = checkedNodes.filter((node) => !node.children || node.children.length === 0)
      }
      // 标准化选中项的数据结构
      this.tempSelectedItems = this.tempSelectedItems.map((item) => {
        const result = {
          id: item.id || item.code,
          name: item.name || item.dutyPostName || item.postName,
          // 保留原始数据以备后用
          originalData: item
        }
        return result
      })
      // 立即更新已选列表显示
      this.selectedItems = [...this.tempSelectedItems]
    },
    // 移除选择项
    removeItem(item) {
      // 从已选列表中移除
      this.tempSelectedItems = this.tempSelectedItems.filter((i) => i.id !== item.id)
      this.selectedItems = this.selectedItems.filter((i) => i.id !== item.id)
      // 更新树的选中状态
      if (this.$refs.tree) {
        // 使用setCheckedKeys更新整个选中状态，这样更可靠
        const currentCheckedKeys = this.tempSelectedItems.map((i) => i.id)
        this.$refs.tree.setCheckedKeys(currentCheckedKeys)
      }
    },
    // 确认选择
    confirm() {
      this.selectedItems = [...this.tempSelectedItems]
      this.$emit('confirm', this.selectedItems)
      this.dialogVisible = false
    },
    // 取消选择
    cancel() {
      this.dialogVisible = false
    },
    // 清空选择
    clearSelection() {
      this.selectedItems = []
      this.tempSelectedItems = []
      if (this.$refs.tree) {
        this.$refs.tree.setCheckedKeys([])
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.position-dialog-container {
  display: flex;
  height: 400px;
  .title {
    font-weight: bold;
    margin-bottom: 10px;
  }
  .selection-area {
    flex: 1;
    padding-right: 15px;
    border-right: 1px solid #ebeef5;
    height: 100%;
    .search-area {
      margin-bottom: 10px;
    }
    .tree-area {
      height: calc(100% - 73px);
      overflow-y: auto;
      ::v-deep .el-tree-node__content {
        height: 32px;
      }
      ::v-deep .el-tree-node.is-current > .el-tree-node__content {
        background-color: #f5f7fa;
      }
      ::v-deep .el-checkbox {
        margin-right: 5px;
      }
    }
  }
  .selected-area {
    width: 300px;
    height: 100%;
    padding-left: 15px;
    .selected-list {
      height: calc(100% - 31px);
      border: 1px dashed #d9d9d9;
      border-radius: 4px;
      padding: 10px;
      overflow-y: auto;
      .empty-tip {
        color: #909399;
        text-align: center;
        line-height: 100px;
      }
      .selected-item-list {
        .selected-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px;
          margin-bottom: 5px;
          background-color: #f5f7fa;
          border-radius: 4px;
          .el-button {
            padding: 0;
          }
        }
      }
    }
  }
}
.dialog-footer {
  // text-align: center;
}
</style>
