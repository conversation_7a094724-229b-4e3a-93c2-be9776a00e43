<template>
  <!-- 给排水运行总览 -->
  <PageContainer class="sewerageOverview">
    <div slot="header" class="sewerageOverview-header">
      <p class="header-title">给排水系统运行总览</p>
      <div class="control-btn-header">
        <el-button class="btn-item" :class="{ 'btn-active': requestInfo.timeType == 0 }" plain @click="timeTypeChange(0)">今日</el-button>
        <el-button class="btn-item" :class="{ 'btn-active': requestInfo.timeType == 1 }" plain @click="timeTypeChange(1)">本月</el-button>
        <el-button class="btn-item" :class="{ 'btn-active': requestInfo.timeType == 2 }" plain @click="timeTypeChange(2)">本年</el-button>
        <el-button class="btn-item" :class="{ 'btn-active': requestInfo.timeType == 3 }" plain style="margin-right: 10px" @click="timeTypeChange(3)">自定义</el-button>
        <el-date-picker
          v-model="requestInfo.dataRange"
          type="daterange"
          unlink-panels
          :disabled="requestInfo.timeType != 3"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          clearable
        />
        <div style="display: inline-block">
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="searchForm">查询</el-button>
        </div>
      </div>
    </div>
    <div slot="content" class="sewerageOverview-content">
      <el-row v-loading="loading.statistics" :gutter="16" style="height: 18%">
        <el-col v-for="item in statisticsList" :key="item.type" :xs="12" :md="12" :lg="6">
          <div class="statistics-item" @click="viewDetails(item.type)">
            <img class="statistics-item-icon" :src="item.icon" :alt="item.name" />
            <p class="statistics-item-name">{{ item.name }}</p>
            <p class="statistics-item-value">
              <span class="value">{{ item.value }}</span>
              <span class="unit">{{ item.unit }}</span>
            </p>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="16" style="height: 45%">
        <el-col :xs="24" :md="24" :lg="12">
          <ContentCard v-loading="loading.waterStatistics" title="排污给水量统计（m³）">
            <div slot="content" class="waterStatistics">
              <div class="waterStatistics-heade">
                <div class="heade-value">
                  <div class="value-item">
                    <p class="item-name">排污总量</p>
                    <p>
                      <span class="item-value">{{ waterSupplyTotalData.waterPolluteTotal.total }}</span>
                      <span class="item-unit">{{ waterSupplyTotalData.waterPolluteTotal.parameterUnit }}</span>
                    </p>
                  </div>
                  <div class="value-item">
                    <p class="item-name">给水总量</p>
                    <p>
                      <span class="item-value">{{ waterSupplyTotalData.waterSupplyTotal.total }}</span>
                      <span class="item-unit">{{ waterSupplyTotalData.waterSupplyTotal.parameterUnit }}</span>
                    </p>
                  </div>
                </div>
                <div class="heade-progress">
                  <el-progress :show-text="false" color="#FF9435" define-back-color="transparent" :percentage="60"></el-progress>
                  <el-progress :show-text="false" color="#3562DB" define-back-color="transparent" :percentage="100"></el-progress>
                </div>
              </div>
              <div class="waterStatistics-chart">
                <echarts ref="waterStatisticsChart" domId="waterStatisticsChart" onTimeLineChange @timelinechanged="(val) => timelinechanged('waterStatistics', val)" />
              </div>
            </div>
          </ContentCard>
        </el-col>
        <el-col :xs="24" :md="24" :lg="12">
          <ContentCard v-loading="loading.waterPumpTotal" title="水泵运行统计">
            <p slot="title-right" class="viewDetails" @click="viewDetails(1)">详情</p>
            <div slot="content" class="waterPumpTotal">
              <div class="waterPumpTotal-left">
                <div>
                  <img src="../../../assets/images/monitor/yxsc_icon.png" />
                  <p class="left-label">运行时长</p>
                  <p style="margin-top: 8px">
                    <span class="left-value">{{ waterRunTimeData.hour }}</span>
                    <span class="left-unit">时</span>
                    <!-- <span class="left-value" style="margin-left: 4px">{{ waterRunTimeData.minute }}</span>
                    <span class="left-unit">分</span> -->
                  </p>
                </div>
                <!-- <div>
                  <p class="left-label">平均运行率</p>
                  <p class="left-value" style="margin-top: 8px;">{{ waterRunTimeData.avgRate }}</p>
                </div> -->
              </div>
              <div class="waterPumpTotal-right">
                <echarts ref="waterPumpTotalChart" domId="waterPumpTotalChart" />
              </div>
            </div>
          </ContentCard>
        </el-col>
      </el-row>
      <el-row :gutter="16" style="height: 37%">
        <el-col>
          <ContentCard v-loading="loading.alarmStatistics" title="报警统计（单）">
            <p slot="title-right" class="viewDetails" @click="viewDetails(6)">详情</p>
            <div slot="content" style="width: 100%; height: 100%; display: flex">
              <div class="cardContent-left">
                <div v-for="item in alarmStatisticsList" :key="item.title" class="left-item">
                  <p class="item-title">{{ item.title }}</p>
                  <p class="item-value">{{ item.value || 0 }}<span>个</span></p>
                  <img class="item-icon" :src="item.icon" :alt="item.title" />
                </div>
              </div>
              <echarts ref="alarmStatistics" domId="alarmStatistics" width="40%" height="100%" />
            </div>
          </ContentCard>
        </el-col>
      </el-row>
      <monitorStatisticsDialog
        v-if="monitorStatisticsDialog"
        :type="detailsType"
        :projectCode="requestInfo.projectCode"
        :entityList="entityList"
        :visible.sync="monitorStatisticsDialog"
      />
      <alarmStatisticsDialog v-if="alarmStatisticsDialog" :projectCode="requestInfo.projectCode" :visible.sync="alarmStatisticsDialog" />
    </div>
  </PageContainer>
</template>
<script>
import gywIcon from '@/assets/images/monitor/gyw_icon.png'
import dywIcon from '@/assets/images/monitor/dyw_icon.png'
import gztjIcon from '@/assets/images/monitor/gztj_icon.png'
import lxtjIcon from '@/assets/images/monitor/lxtj_icon.png'
import alarmStatistics from '@/assets/images/monitor/alarm-statistics.png'
import alarmAlarm from '@/assets/images/monitor/alarm-pending.png'
import alarmDoing from '@/assets/images/monitor/alarm-doing.png'
import monitorStatisticsDialog from './components/monitorStatisticsDialog'
import alarmStatisticsDialog from '../components/alarmStatisticsDialog/index.vue'
import moment from 'moment'
import { monitorTypeList } from '@/util/dict.js'
moment.locale('zh-cn')
export default {
  name: 'sewerageOverview',
  components: {
    monitorStatisticsDialog,
    alarmStatisticsDialog
  },
  data() {
    return {
      detailsType: '', // 详情类型 1:水泵运行状态统计, 2 高液位状态统计, 3 低液位状态统计, 4 统计故障, 5 统计离线
      monitorStatisticsDialog: false,
      alarmStatisticsDialog: false,
      entityList: [], // 设备列表
      statisticsList: [
        {
          type: 2,
          icon: gywIcon,
          name: '高液位',
          unit: '次',
          value: 0
        },
        {
          type: 3,
          icon: dywIcon,
          name: '低液位',
          unit: '次',
          value: 0
        },
        {
          type: 4,
          icon: gztjIcon,
          name: '故障统计',
          unit: '次',
          value: 0
        },
        {
          type: 5,
          icon: lxtjIcon,
          name: '离线统计',
          unit: '次',
          value: 0
        }
      ],
      alarmStatisticsList: [
        {
          title: '报警统计',
          icon: alarmStatistics,
          value: 0
        },
        {
          title: '未处理',
          icon: alarmAlarm,
          value: 0
        },
        {
          title: '处理中',
          icon: alarmDoing,
          value: 0
        }
      ],
      waterSupplyTotalData: {
        waterPolluteTotal: { total: 0 },
        waterSupplyTotal: { total: 0 }
      },
      waterPumpTotalData: {},
      waterRunTimeData: {
        avgRate: '',
        hour: '',
        minute: ''
      },
      requestInfo: {
        projectCode: monitorTypeList.find((item) => item.projectName == '给排水监测').projectCode,
        dataRange: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')], // 时间范围
        timeType: 0 // 0: 当天 1: 本月 2: 本年 3: 自定义)
      },
      loading: {
        statistics: false, // 次数统计
        waterStatistics: false, // 排污给水量统计
        waterPumpTotal: false, // 水泵运行统计
        alarmStatistics: false // 报警统计
      }
    }
  },
  computed: {},
  created() {
    this.init()
    this.getEntityMenuList()
  },
  methods: {
    init() {
      let params = {
        startTime: this.requestInfo.dataRange[0],
        endTime: this.requestInfo.dataRange[1],
        projectCode: this.requestInfo.projectCode,
        timeType: this.requestInfo.timeType
      }
      this.getStatisticsNum(params)
      this.$nextTick(() => {
        this.getWaterSupplyTotal(params)
        this.getWaterRunTime(params)
        this.getAirRunPolice(params)
      })
    },
    // 图表月分分页
    timelinechanged(type, month) {
      let params = {
        startTime: this.requestInfo.dataRange[0],
        endTime: this.requestInfo.dataRange[1],
        projectCode: this.requestInfo.projectCode,
        timeType: this.requestInfo.timeType,
        yearMouth: month.currentIndex + 1
      }
      if (type == 'waterStatistics') {
        this.getWaterSupplyTotal(params, month.currentIndex)
      }
    },
    // 查看详情
    viewDetails(type) {
      this.detailsType = type
      if ([1, 2, 3, 4, 5].includes(type)) {
        this.monitorStatisticsDialog = true
      } else {
        this.alarmStatisticsDialog = true
      }
      console.log(type)
    },
    // 获取设备菜单
    getEntityMenuList() {
      this.$api.GetEntityMenuList({ projectId: this.requestInfo.projectCode }).then((res) => {
        if (res.code == 200) {
          this.entityList = res.data
        }
      })
    },
    // 报警统计
    getAirRunPolice(params) {
      let newArr = []
      this.loading.alarmStatistics = true
      this.$api
        .GetAirRunPolice(params)
        .then((res) => {
          this.loading.alarmStatistics = false
          if (res.code == 200) {
            ;(res.data?.policeList ?? []).forEach((item) => {
              newArr.push({
                name: item.menuName,
                value: item.policeCount
              })
            })
            this.alarmStatisticsList[0].value = res.data?.total ?? 0
            this.alarmStatisticsList[1].value = res.data?.noDealCount ?? 0
            this.alarmStatisticsList[2].value = res.data?.isDealCount ?? 0
            this.$refs.alarmStatistics.init(this.setPieChart(newArr))
          } else {
            this.$refs.alarmStatistics.init(this.setPieChart())
          }
        })
        .catch(() => {
          this.loading.alarmStatistics = false
          this.$refs.alarmStatistics.init(this.setPieChart())
        })
    },
    setPieChart(data) {
      let option
      var colors = ['#5e81ec', '#ffc855', '#98e79b', '#00d695', '#00b29a', '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc']
      var total = data.reduce((p, v) => {
        return p + v.value
      }, 0)
      if (data.length) {
        option = {
          title: {
            text: '监测项类型报警占比',
            left: 'center',
            textStyle: {
              fontSize: 15,
              fontWeight: 500
            }
          },
          legend: {
            orient: 'vertical',
            x: '60%',
            y: 'center',
            itemWidth: 20,
            itemHeight: 20,
            align: 'left',
            textStyle: {
              fontSize: 14,
              color: '#000'
            },
            data: data.map((item) => item.name),
            formatter: (name) => {
              let item = data.find((v) => v.name == name)
              return `${name}  ${((item.value / total) * 100).toFixed(2)}%  ${item.value}单`
            }
          },
          tooltip: {
            trigger: 'item',
            formatter: '{b} : {c} ({d}%)'
          },
          color: colors,
          calculable: true,
          series: [
            {
              type: 'pie',
              radius: ['45%', '80%'],
              center: ['30%', '50%'],
              roseType: 'radius',
              label: {
                show: false
              },
              labelLine: {
                length: 1,
                length2: 20,
                smooth: true
              },
              data: data
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      return option
    },
    getWaterPumpTotalChaet(data = []) {
      let option
      if (data.length) {
        option = {
          title: {
            show: false,
            text: '水泵运行统计'
          },
          tooltip: {
            trigger: 'axis'
          },
          grid: {
            top: '20%',
            left: '3%',
            right: '4%',
            bottom: '5%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            axisLabel: {
              // 坐标轴刻度标签的相关设置。
              textStyle: {
                color: '#414653',
                fontSize: 12
              }
            },
            axisTick: {
              // 坐标轴刻度相关设置。
              show: false
            },
            axisLine: {
              // 坐标轴轴线相关设置
              lineStyle: {
                color: '#F1F3F5'
              }
            },
            splitLine: {
              // 坐标轴在 grid 区域中的分隔线。
              show: false
            }
          },
          yAxis: [
            {
              type: 'value',
              axisLabel: {
                textStyle: {
                  color: '#414653',
                  fontSize: 12
                }
              },
              axisLine: {
                show: false
              },
              axisTick: {
                show: false
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: '#F1F3F5'
                }
              }
            }
          ],
          series: [
            {
              name: '运行时长',
              type: 'bar',
              barWidth: '20',
              tooltip: {
                show: true,
                valueFormatter: (value) => Number(value) + 'h'
              },
              itemStyle: {
                color: '#3562DB'
              },
              data: data.map((item) => [item.name, item.value])
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      return option
    },
    // 水泵运行时长
    getWaterRunTime(params) {
      this.$api
        .WaterRunTime(params)
        .then((res) => {
          this.loading.waterPumpTotal = false
          if (res.code == 200) {
            this.waterRunTimeData = res.data
            this.$refs.waterPumpTotalChart.init(this.getWaterPumpTotalChaet(res.data.carChart))
            this.$nextTick(() => {
              this.$refs.waterPumpTotalChart.chartResize()
            })
          } else {
            this.$refs.waterPumpTotalChart.init(this.getWaterPumpTotalChaet())
          }
        })
        .catch(() => {
          this.$refs.waterPumpTotalChart.init(this.getWaterPumpTotalChaet())
          this.loading.waterPumpTotal = false
        })
    },
    // 排污给水量统计
    getWaterSupplyTotal(params, currentIndex) {
      this.loading.waterStatistics = true
      this.$api
        .WaterSupplyTotal(params)
        .then((res) => {
          this.loading.waterStatistics = false
          if (res.code == 200) {
            this.waterSupplyTotalData = res.data
            this.$refs.waterStatisticsChart.init(this.getWaterSupplyTotalChaet(res.data.waterPolluteTotal.detail, res.data.waterSupplyTotal.detail, currentIndex))
          } else {
            this.$refs.waterStatisticsChart.init(this.getWaterSupplyTotalChaet())
          }
        })
        .catch(() => {
          this.$refs.waterStatisticsChart.init(this.getWaterSupplyTotalChaet())
          this.loading.waterStatistics = false
        })
    },
    getWaterSupplyTotalChaet(
      waterPolluteTotal = [],
      waterSupplyTotal = [],
      index = this.requestInfo.timeType == 2 ? Number(this.waterSupplyTotalData.yearMouth[this.waterSupplyTotalData.yearMouth.length - 1]) - 1 : 0
    ) {
      let option
      if (waterPolluteTotal.length && waterSupplyTotal.length) {
        option = {
          title: {
            show: false,
            text: '排污给水量统计'
          },
          tooltip: {
            trigger: 'axis'
          },
          dataZoom: [
            {
              type: 'inside',
              start: 0,
              end: 100
            }
          ],
          legend: {
            top: '3%',
            itemGap: 50,
            data: ['排污量', '给水量'],
            textStyle: {
              color: '#414653'
            }
          },
          grid: {
            top: '20%',
            left: '3%',
            right: '4%',
            bottom: this.requestInfo.timeType == 2 ? '50' : '3%',
            containLabel: true
          },
          xAxis: {
            type: 'time',
            boundaryGap: false, // 坐标轴两边留白
            // data: ['一', '一', '一', '一', '一', '一', '一', '一', '一', '一', '一', '一'],
            axisLabel: {
              // 坐标轴刻度标签的相关设置。
              margin: 10,
              textStyle: {
                color: '#414653',
                fontSize: 12
              }
            },
            axisTick: {
              // 坐标轴刻度相关设置。
              show: false
            },
            axisLine: {
              // 坐标轴轴线相关设置
              lineStyle: {
                color: '#F1F3F5'
                // opacity:0.2
              }
            },
            splitLine: {
              // 坐标轴在 grid 区域中的分隔线。
              show: false
            }
          },
          yAxis: [
            {
              type: 'value',
              axisLabel: {
                textStyle: {
                  color: '#414653',
                  fontSize: 12
                }
              },
              axisLine: {
                show: false
              },
              axisTick: {
                show: false
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: '#F1F3F5'
                }
              }
            }
          ],
          series: [
            {
              name: '排污量',
              type: 'line',
              itemStyle: {
                normal: {
                  color: '#F0984B',
                  lineStyle: {
                    color: '#F0984B',
                    width: 1
                  },
                  areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                      {
                        offset: 0,
                        color: 'rgba(240, 152, 75, 0)'
                      },
                      {
                        offset: 1,
                        color: 'rgba(240, 152, 75, 0.21)'
                      }
                    ])
                  }
                }
              },
              data: waterPolluteTotal.map((item) => [item.time, item.value])
            },
            {
              name: '给水量',
              type: 'line',
              itemStyle: {
                normal: {
                  color: '#3F63D3',
                  lineStyle: {
                    color: '#3F63D3',
                    width: 1
                  },
                  areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                      {
                        offset: 0,
                        color: 'rgba(63, 99, 211, 0)'
                      },
                      {
                        offset: 1,
                        color: 'rgba(63, 99, 211, 0.21)'
                      }
                    ])
                  }
                }
              },
              data: waterSupplyTotal.map((item) => [item.time, item.value])
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      if (this.requestInfo.timeType == 2) {
        option.timeline = {
          axisType: 'category',
          realtime: false,
          autoPlay: false,
          controlStyle: {
            showPlayBtn: false
          },
          left: 20,
          right: 20,
          bottom: 0,
          currentIndex: index,
          data: this.waterSupplyTotalData?.yearMouth ?? [],
          label: {
            formatter: function (s) {
              return Number(s) + '月'
            }
          }
        }
      }
      return option
    },
    // 统计消息
    getStatisticsNum(params) {
      this.loading.statistics = true
      this.$api
        .WaterOverviewByTime(params)
        .then((res) => {
          this.loading.statistics = false
          if (res.code == 200) {
            this.statisticsList[0].value = res.data?.highLevelCount ?? 0
            this.statisticsList[1].value = res.data?.lowLevelCount ?? 0
            this.statisticsList[2].value = res.data?.breakLineCount ?? 0
            this.statisticsList[3].value = res.data?.offLineCount ?? 0
          }
        })
        .catch(() => {
          this.loading.statistics = false
        })
    },
    // 日期选择
    timeTypeChange(type) {
      let newObj = {
        0: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        1: [moment().startOf('month').format('YYYY-MM-DD'), moment().endOf('month').format('YYYY-MM-DD')],
        2: [moment().startOf('year').format('YYYY-MM-DD'), moment().endOf('year').format('YYYY-MM-DD')],
        3: []
      }
      this.requestInfo.dataRange = newObj[type]
      this.requestInfo.timeType = type
    },
    // 重置查询表单
    resetForm() {
      this.requestInfo = {
        projectCode: monitorTypeList.find((item) => item.projectName == '给排水监测').projectCode,
        dataRange: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        timeType: 0
      }
      this.searchForm()
    },
    // 查询
    searchForm() {
      if (!this.requestInfo.dataRange.length) {
        this.$message.warning('请选择时间段！')
        return
      }
      this.init()
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .container-content {
  overflow-y: auto !important;
}
.sewerageOverview {
  p {
    margin: 0;
  }
  .sewerageOverview-header {
    .header-title {
      border-bottom: 1px solid #e4e7ed;
      padding: 13px 16px;
      font-size: 14px;
      font-weight: 500;
      color: #121f3e;
      line-height: 14px;
    }
    .control-btn-header {
      padding: 6px 6px 16px 16px;
      & > div {
        margin-right: 10px;
        margin-top: 10px;
      }
      .btn-item {
        border: 1px solid #3562db;
        color: #3562db;
        font-family: none;
      }
      .btn-active {
        color: #fff;
        background: #3562db;
      }
    }
  }
  .sewerageOverview-content {
    height: 100%;
    .statistics-item {
      cursor: pointer;
      width: 100%;
      margin-top: 16px;
      height: calc(100% - 16px);
      border-radius: 4px;
      background: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      .statistics-item-icon {
        width: 48px;
        height: 45.6px;
      }
      .statistics-item-name {
        font-size: 15px;
        font-weight: 500;
        color: #121f3e;
        line-height: 18px;
        margin-left: 24px;
      }
      .statistics-item-value {
        margin-left: 24px;
        .value {
          font-size: 30px;
          font-weight: bold;
          color: #121f3e;
          line-height: 36px;
        }
        .unit {
          margin-left: 4px;
          font-size: 15px;
          font-weight: 500;
          color: #ccced3;
          line-height: 18px;
        }
      }
    }
    .waterStatistics {
      height: 100%;
      min-height: 240px;
      display: flex;
      flex-direction: column;
      .waterStatistics-heade {
        height: 100px;
        padding: 0 24px;
        background: #faf9fc;
        display: flex;
        .heade-value {
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: space-around;
          .value-item {
            display: flex;
            align-items: center;
          }
          .item-name {
            font-size: 14px;
            font-weight: 500;
            color: #121f3e;
            line-height: 14px;
          }
          .item-value {
            font-size: 30px;
            font-weight: bold;
            color: #414653;
            margin-left: 16px;
            line-height: 22px;
          }
          .item-unit {
            font-size: 15px;
            font-weight: 500;
            color: #ccced3;
            margin-left: 4px;
            line-height: 15px;
          }
        }
        .heade-progress {
          display: flex;
          flex-direction: column;
          justify-content: space-around;
          margin-left: 24px;
          flex: 1;
          height: 100%;
        }
        ::v-deep .el-progress {
          .el-progress-bar {
            .el-progress-bar__outer {
              height: 18px !important;
              border-radius: 0;
            }
            .el-progress-bar__inner {
              border-radius: 0;
            }
          }
        }
      }
      .waterStatistics-chart {
        flex: 1;
      }
    }
    .waterPumpTotal {
      width: 100%;
      height: 100%;
      display: flex;
      overflow: hidden;
      .waterPumpTotal-left {
        padding: 0 24px;
        background: #faf9fc;
        > div {
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          text-align: center;
        }
        p {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        img {
          width: 66px;
          height: 66px;
          margin: 0 auto;
        }
        .left-label {
          font-size: 15px;
          font-weight: 500;
          color: #121f3e;
        }
        .left-value {
          font-size: 30px;
          font-weight: bold;
          color: #121f3e;
        }
        .left-unit {
          font-size: 15px;
          font-weight: 500;
          color: #ccced3;
          margin-left: 4px;
        }
      }
      .waterPumpTotal-right {
        flex: 1;
        overflow: hidden;
      }
    }
    .cardContent-left {
      display: flex;
      align-items: center;
      width: 60%;
      height: 100%;
      p {
        margin: 0;
      }
      .left-item {
        width: calc(100% / 3 - 16px);
        height: 140px;
        margin-right: 16px;
        padding: 24px 24px 30px;
        background: #faf9fc;
        border-radius: 4px;
        margin-bottom: 7px;
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .item-title {
          font-size: 15px;
          font-weight: 500;
          color: #121f3e;
        }
        .item-value {
          height: 36px;
          font-size: 40px;
          font-weight: bold;
          color: #121f3e;
          line-height: 36px;
          & > span {
            margin-left: 4px;
            font-size: 15px;
            font-weight: 500;
            color: #ccced3;
          }
        }
        .item-icon {
          position: absolute;
          right: 24px;
          bottom: 24px;
          width: 40px;
          height: 40px;
        }
      }
      & :last-child {
        margin-bottom: 0;
      }
    }
    ::v-deep .el-row {
      .el-col {
        height: 100%;
      }
    }
    .box-card {
      margin-top: 16px;
      height: calc(100% - 16px);
      .viewDetails {
        user-select: none;
        cursor: pointer;
        margin: 0 !important;
        font-size: 14px;
        color: #3562db;
        position: absolute;
        right: 15px;
        top: 0;
      }
      .card-body {
        height: calc(100% - 25px);
        margin: 0;
      }
    }
  }
}
</style>
