<template>
  <el-dialog v-dialogDrag :modal="false" :close-on-click-modal="false" :title="certificateDiaTitle" width="35%"
    :visible="certificatevisible" custom-class="model-dialog" :before-close="closeDialog">
    <div class="diaContent">
      <el-form ref="formInline" :model="formInline" label-width="150px" :rules="rules" label-position="right">
        <el-form-item label="证书名称" prop="certDictId">
          <el-select v-model="formInline.certDictId" placeholder="请选择" clearable filterable
            @change="changeCertificateIdRule">
            <el-option v-for="item in certificateOptions" :key="item.id" :value="item.id" :label="item.name" clearable>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="证书编号" prop="certNo">
          <el-input v-model="formInline.certNo" placeholder="请输入" maxlength="20"> </el-input>
        </el-form-item>
        <el-form-item label="颁发机构" prop="issuingAuthority">
          <el-input v-model="formInline.issuingAuthority" maxlength="50" placeholder="请输入"> </el-input>
        </el-form-item>
        <el-form-item label="发证日期" prop="certTime">
          <el-date-picker :picker-options="pickerOptionsStart" v-model="formInline.certTime" type="date"
            value-format="yyyy-MM-dd" format="yyyy-MM-dd" placeholder="请选择日期"> </el-date-picker>
        </el-form-item>
        <el-form-item label="有效期截止日期" class="is-required">
          <el-date-picker :picker-options="pickerOptionsEnd" v-model="formInline.validityPeriod" type="date"
            :disabled="formInline.longTerm" value-format="yyyy-MM-dd" format="yyyy-MM-dd"
            :placeholder="formInline.longTerm?'长期有效':'请选择日期'">
          </el-date-picker>
          <el-checkbox v-model="formInline.longTerm" style="margin-left:10px"
            @change="longTermChange">长期有效</el-checkbox>
        </el-form-item>
        <el-form-item label="上传附件">
          <el-upload ref="uploadFile" action="string" style="width: 300px" :limit="6" :http-request="httpRequset"
            :beforeUpload="beforeAvatarUpload" :file-list="fileList" :on-exceed="handleExceed" :on-remove="handleRemove"
            accept=".jpg,.jpeg,.png,.gif,.bmp,.svg,.webp,.heic,.pdf">
            <el-button size="small" type="primary">点击上传</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="confirm('formInline')">确 定</el-button>
    </span>
  </el-dialog>

</template>
<script>
export default {
  name: 'certificateDialog',
  props: {
    certificatevisible: {
      type: Boolean,
      default: false
    },
    certificateDiaTitle: {
      type: String,
      default: ''
    },
    certificateId: {
      type: Number,
      default: 0
    },
    certificateType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      pickerOptionsStart: this.beginDate(),
      pickerOptionsEnd: this.endDate(),
      formInline: {
        id: '',
        certNo: '', // 证书编号
        certDictId: '', // 证书id
        certDictName: '', // 证书名称
        issuingAuthority: '', // 颁发机构
        validityPeriod: '', // 有效截至日期
        certTime: '', // 发证日期
        longTerm: false,//长期有效
        // urlName: '', // 文件名称
        // url: '',// 文件url
        list: [],//文件list
      },
      rules: {
        certDictId: [{
          required: true,
          message: '请选择证书名称',
          trigger: 'change'
        }],
        deptId: [{
          required: true,
          message: '请选择持证人部门',
          trigger: 'change'
        }],
        personId: [{
          required: true,
          message: '请选择持证人',
          trigger: 'change'
        }],
        // certNo: [{
        //   required: true,
        //   message: '请输入证书编号',
        //   trigger: 'blur'
        // }],
        // issuingAuthority: [{
        //   required: true,
        //   message: '请输入颁发机构',
        //   trigger: 'blur'
        // }],
        certTime: [{
          required: true,
          message: '请选择发证日期',
          trigger: 'change'
        }],
        // validityPeriod: [{
        //   required: true,
        //   message: '请选择有效期截止日期',
        //   trigger: 'change'
        // }],
      },
      certificateOptions: [], // 证书下拉
      fileList: [],
    }
  },
  mounted() {
    this.getCertificateData()
    if (this.certificateId) {
      setTimeout(() => {
        this.getDetailData(this.certificateId)
      }, 300)
    }
  },
  methods: {
    beginDate() {
      let _this = this
      return {
        disabledDate(time) {
          if (_this.formInline.validityPeriod) {
            return time.getTime() > new Date(_this.formInline.validityPeriod);
          }
        }
      }

    },
    endDate() {
      let _this = this
      return {
        disabledDate(time) {
          if (_this.formInline.certTime) {
            return time.getTime() < new Date(_this.formInline.certTime);
          }
        }
      }

    },
    // 证书下拉
    getCertificateData() {
      this.$api.supplierAssess.getDictConfigUseData({
        type: 4,
        status: 1
      }).then((res) => {
        if (res.code == '200') {
          this.certificateOptions = res.data
        }
      })
    },
    // 详情数据
    getDetailData(id) {
      this.fileList = []
      this.$api.supplierAssess.getUserCertificateInfoById({
        id: id
      }).then((res) => {
        if (res.code == '200') {
          if (this.certificateType) {
            this.formInline = res.data
            this.formInline.longTerm = res.data.type === 2 ? true : false
            if (res.data.list && res.data.list.length) {
              this.fileList = res.data.list
              this.formInline.list = res.data.list
              this.fileList.forEach((e) => {
                e.name = e.urlName
              })
            } else {
              this.fileList = []
              this.formInline.list = []
            }
          }
        }
      })
    },
    // 获取证书名称
    changeCertificateIdRule(val) {
      this.formInline.certDictName = this.certificateOptions.find((e) => e.id === val).name
    },
    //长期有效时间
    longTermChange(val) {
      this.$forceUpdate()
      if (val) {
        this.formInline.validityPeriod = ""
      }
    },
    // 确定
    confirm(formName) {
      if (this.formInline.validityPeriod || this.formInline.longTerm) {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            this.formInline.list.forEach((e) => {
              delete e.uid
              delete e.status
              delete e.name
            })
            this.$emit('submitCertificateDialog', this.formInline)
          } else {
            return false
          }
        })
      } else {
        this.$message.error('请选择有效期截止日期!')
      }
    },
    // 文件上传相关
    // 限制文件大小
    beforeAvatarUpload(file) {
      const isLt20M = file.size / 1024 / 1024 > 20
      if (isLt20M) {
        this.$message.error('上传文件大小不能超过 20MB!')
        return false
      }
    },
    /**
     * 文件上传相关
     */
    httpRequset(file) {
      const params = new FormData()
      params.append('file', file.file)
      this.$api.uploadFile(params).then((res) => {
        if (res.code == 200) {
          this.formInline.list.push({
            url: res.data.fileKey,
            urlName: res.data.name,
            name: res.data.name,
            uid: file.file.uid
          })
        }
      })
    },
    handleExceed(files, fileList) {
      this.$message.warning('当前限制选择 5个文件')
    },
    // 删除文件
    handleRemove(file, fileList) {
      this.fileList = fileList
      this.formInline.list = fileList
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('closeDialog')
    }
  }
}

</script>
<style lang="scss" scoped>
.diaContent {
  width: 100%;
  // max-height: 550px !important;
  overflow: auto;
  background-color: #fff !important;

  .el-input,
  .el-select {
    width: 300px;
  }
}
</style>
