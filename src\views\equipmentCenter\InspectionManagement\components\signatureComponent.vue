<template>
  <el-dialog
    class="classify-dialog"
    custom-class="model-dialog"
    title="在线签字"
    :visible.sync="signatureDialog"
    :before-close="handleClose"
    append-to-body
  >
    <vue-esign
      ref="esign"
      :width="width"
      :height="height"
      :isCrop="isCrop"
      :lineWidth="lineWidth"
      :lineColor="lineColor"
    />
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db;" @click="handleReset">重新书写</el-button>
      <el-button type="primary" @click="handleGenerate">确 定</el-button>
    </span>
    <div v-if="imgSrc != ''" class="img">
      <img :src="imgSrc" width="200" height="100" />
    </div>
  </el-dialog>
</template>

<script>
import vueEsign from 'vue-esign'
export default {
  name: 'signatureComponent',
  components: {
    vueEsign
  },
  props: {
    width: {
      type: Number,
      default: 400
    },
    height: {
      type: Number,
      default: 200
    },
    imgSrc: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      img: '',
      signatureDialog: true,
      lineWidth: 2,
      lineColor: '#000000',
      resultImg: '',
      isCrop: false
    }
  },
  methods: {
    handleClose() {
      this.signatureDialog = false
      this.$emit('show', false)
    },
    handleReset() {
      this.$refs.esign.reset()
    },
    // 生成签名的base64图片
    handleGenerate() {
      this.$refs.esign
        .generate()
        .then((res) => {
          this.img = res
          this.$emit('change', this.img, false)
        })
        .catch((err) => {
          this.$message.error('请进行签字')
        })
    },
    // 附：base64转化成图片
    base64ImgtoFile(dataurl, fileName = 'file') {
      const arr = dataurl.split(',')
      const mime = arr[0].match(/:(.*?);/)[1]
      const suffix = mime.split('/')[1]
      const bstr = atob(arr[1])
      let n = bstr.length
      var u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      return new File([u8arr], `${fileName}.${suffix}`, { type: mime })
    }
  }
}
</script>

<style lang="scss" scoped>
.img {
  margin-top: 10px;
  border-top: 1px solid #eee;
  width: 100%;
  text-align: center;
}
::v-deep .el-dialog {
  min-width: unset;
  width: 500px !important;
  .el-dialog__body {
    flex-direction: column;
  }
}
</style>
