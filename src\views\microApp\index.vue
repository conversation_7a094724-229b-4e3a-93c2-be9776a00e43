/* 1、所有子应用都可以走这个页面，需要传给子应用的值通过路由来判断，自行添加对应属性 2、此页面务必不要做缓存
3、因为主应用路由限制的问题，所以只能处理成每个单独页面都认证成一个子应用，因为每次都会卸载并且重新加载，所以不会出现内容臃肿的问题，优点就是可以完美兼容主应用路由系统，缺点是页面跳转不够丝滑
*/
<template>
  <div v-loading.lock="pageLoading" element-loading-background="rgba(0, 0, 0, 0)" class="micro-page">
    <micro-app
      :url="childInfo.url"
      :name="childInfo.name"
      :baseroute="`${childInfo.baseroute}`"
      inline
      router-mode="native"
      iframe
      destroy
      :data="childInfo"
      style="height: 100%"
      @datachange="handleDataChange"
    ></micro-app>
  </div>
</template>
<script>
import microApp from '@micro-zoe/micro-app'
import { Loading } from 'element-ui'
import store from '@/store'
export default {
  name: 'MicroChildApp',
  data() {
    return {
      processOrigin: process.env.VUE_APP_UNIFIED_SERVER.slice(0, -1),
      origin: '',
      pageLoading: false,
      loadingInstance: null,
      childInfo: {
        url: '',
        // url: `${origin}${this.$store.state.childAppsData.childAppInfo.parentName}/`,
        name: '',
        baseroute: '',
        path: '',
        userInfo: this.$store.state.user
      }
    }
  },
  created() {
    this.childInfo.name = this.$store.state.childAppsData.childAppInfo.parentName
    this.childInfo.baseroute = this.$store.state.childAppsData.childAppInfo.parentName
    this.childInfo.path = this.$store.state.childAppsData.childAppInfo.currentPath
    this.childInfo.query = this.$store.state.childAppsData.childAppInfo.childAppQuery
    let arr = ['/projectManage', '/operation', 'processManagement', '/rentalHousingApproveManage']
    if (arr.includes(this.childInfo.baseroute)) {
      // this.loadingInstance = Loading.service({ fullscreen: true, lock: true, background: 'rgba(0, 0, 0, 0.7)' })
      this.pageLoading = true
      this.childInfo.storeState = store.state
      this.childInfo.baseUrl = this.processOrigin
      // this.childInfo.baseUrl = 'http://192.168.10.51:3200'
      this.childInfo.baseFileUrl = store.state.user.picPrefix
    }
    console.log('重新加载子应用', this.childInfo)
    this.setChildAppOrigin()
    microApp.router.setDefaultPage({
      name: this.childInfo.path,
      path: `/${this.childInfo.path}`
    })
    window.addEventListener('message', this.handleMessage)
    this.$once('hook:beforeDestroy', () => {
      window.removeEventListener('message', this.handleMessage)
    })
  },
  methods: {
    setChildAppOrigin() {
      // const origin = process.env.NODE_ENV === 'production' ? 'http://10.20.4.250:20008' : 'http://127.0.0.1:3000'
      if (process.env.NODE_ENV === 'production') {
        this.origin = window.origin
        // this.origin = this.processOrigin
      } else {
        let port = ''
        switch (this.childInfo.baseroute) {
          case '/parkingManage': // 停车场管理
          case '/cateringManagement': // 餐饮管理
            port = 3100
            break
          case '/tissueArchitecture': // 组织架构
          case '/baseAsset': // 资产管理
          case '/baseSpace': // 空间管理
            port = 3000
            break
          case '/projectManage': // 工程管理 / 项目
          case '/operation': // 施工作业管理
          case '/processManagement': // 流程管理
          case '/rentalHousingApproveManage': // 公租房 / 审批管理
            port = 3200
            break
          default:
            break
        }
        this.origin = `http://127.0.0.1:${port}`
      }
      this.childInfo.url = `${this.origin}${this.childInfo.baseroute}/`
    },
    handleMessage(e) {
      const messageData = e.data.data
      console.log('子应用返回iframe message', e)
      if (messageData && messageData.type === 'unity') {
        console.log('传给子应用的messageData', messageData.data)
        microApp.setData(this.childInfo.name, messageData)
      }
    },
    handleDataChange(e) {
      const newVal = e.detail.data
      console.log('子应用返回数据', newVal)
      if (newVal.type === 'event' && newVal.event && newVal.event === 'closeLoading') {
        // this.loadingInstance.close()
        this.pageLoading = false
        return
      }
      // 判断token是否失效，跳转login
      if (!newVal.type || newVal.type != 'jump') {
        if (newVal.route && newVal.route.path === 'login') {
          this.$store.dispatch('user/logout').then((res) => {
            this.$router.push({
              path: '/login',
              query: {
                // redirect: router.currentRoute.fullPath
              }
            })
          })
        }
        this.$store.commit('childAppsData/setChildAppInfo', { parentName: this.childInfo.baseroute, currentPath: newVal.route.path, childAppQuery: newVal.route.query })
      } else {
        this.$router.push({
          path: newVal.parentName + newVal.route.path,
          query: newVal.route.query
        })
        // 这里处理一下子应用跳父应用之后，点返回继续回到子应用的问题
        let childParentNameWhite = ['/operation', '/parkingManage', '/cateringManagement', '/tissueArchitecture', '/baseAsset', '/baseSpace', '/projectManage']
        if (childParentNameWhite.includes(newVal.parentName)) {
          this.$store.commit('childAppsData/setChildAppInfo', { parentName: newVal.parentName, currentPath: newVal.route.path })
        }
      }
      // 路由跳转回显选中效果
      // const nowRouteList = this.$store.getters['menu/sidebarRoutes']
      // const colRouteList = nowRouteList.filter((item) => {
      //   return item.meta.arrangement == 'column'
      // })
      // const pathList = colRouteList.map((item) => {
      //   return item.path
      // })
      // if (pathList.includes(newVal.matched[0].path) && !this.colActiveMenu) {
      //   // this.colActiveMenu = newVal.matched[0].path
      //   this.$store.commit('menu/setColActiveMenu', newVal.matched[0].path)
      // }
    }
  }
}
</script>
<style lang="scss" scoped>
.micro-page {
  width: 100%;
  height: 100%;
}
::v-deep micro-app {
  micro-app-body {
    background: none;
  }
}
</style>
