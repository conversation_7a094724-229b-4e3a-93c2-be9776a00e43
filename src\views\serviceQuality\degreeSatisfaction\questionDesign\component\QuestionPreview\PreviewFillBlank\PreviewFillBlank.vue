<template>
  <div>
    <div class="quesName">
      <span v-show="pathName[diff].isQuestionNum">{{ index + 1 }}、</span>
      {{ inputOption.name }}
      <span v-show="pathName[diff].isShowSubjectType" class="chexMinMax">[填空题]</span>
      <span v-if="inputOption.isMust == 1" class="starRed">*</span>
    </div>
    <div class="questionContainer">
      <el-form ref="radioForm" :model="inputForm" :rules="rules">
        <el-form-item prop="id_input">
          <el-input
            v-if="inputOption.inputType === 'rows'"
            v-model="inputForm.id_input"
            type="textarea"
            :rows="4"
            auto-complete="off"
            :disabled="pathName[diff].isDisable"
            @input="handleInputChange"
          ></el-input>
          <el-date-picker v-else-if="inputOption.inputType === 'date'" v-model="dateValue" type="date" placeholder="选择日期" :disabled="pathName[diff].isDisable"></el-date-picker>
          <el-date-picker
            v-else-if="inputOption.inputType === 'time'"
            v-model="dateValue"
            type="datetime"
            placeholder="选择时间"
            :disabled="pathName[diff].isDisable"
          ></el-date-picker>
          <el-input v-else v-model="inputForm.id_input" auto-complete="off" :disabled="pathName[diff].isDisable" @input="handleInputChange"></el-input>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    // 当前题目的所有相关信息
    previewOption: {
      type: Object
    },
    // 该题目在当前问卷中的序号
    index: {
      type: Number
    },
    // 是否显示题目序号，true为显示，false为不显示
    isQuestionNum: {
      type: Boolean
    },
    // 表示用于在哪个页面显示该题目时的相关设置信息
    pathName: {
      type: Object
    },
    diff: {
      type: String
    }
  },
  data() {
    let validedFormItem = (rule, value, callback) => {
      const regOption = this.typeReg[this.inputOption.inputType]
      if (value == '' || value == undefined) {
        if (this.inputOption.isMust == 1) {
          return callback(new Error(regOption.requireMessage))
        } else {
          callback()
        }
      } else {
        if (regOption.reg && !regOption.reg.test(value)) {
          callback(new Error(regOption.formatMessage))
        }
      }
    }
    return {
      currentPathName: '',
      inputOption: {},
      inputForm: {
        id_input: ''
      },
      answerVal: [],
      dateValue: '请选择', // 日期组件显示日期的值
      inputOption1: {
        name: '①填空题击一为ID哦iwejdooiadio',
        inputType: 'phone', // row, rows, number,phone, email,incard,url,date,time
        isMust: 1 // 是否必填，0表示否，1表示是
      },
      typeReg: {
        row: {
          reg: '',
          requireMessage: '请输入内容',
          formatMessage: ''
        },
        rows: {
          reg: '',
          requireMessage: '请输入内容',
          formatMessage: ''
        },
        number: {
          reg: /^(\-|\+)?\d+(\.\d+)?$/,
          requireMessage: '数字内容不能不空',
          formatMessage: '请输入正确的数字格式'
        },
        phone: {
          reg: /^[1][3,4,5,6,7,8,9][0-9]{9}$/,
          requireMessage: '电话号码不能不空',
          formatMessage: '请输入正确的电话号码格式'
        },
        email: {
          reg: /^[A-Za-z\d]+([-_.][A-Za-z\d]+)*@([A-Za-z\d]+[-.])+[A-Za-z\d]{2,4}$/,
          requireMessage: '邮箱地址不能不空',
          formatMessage: '请输入正确的邮箱地址格式'
        },
        incard: {
          reg: /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}[0-9Xx]$)/,
          requireMessage: '身份证号码不能不空',
          formatMessage: '请输入正确的身份证号码格式'
        },
        url: {
          reg: /^http:\/\/([\w-]+\.)+[\w-]+(\/[\w-./?%&=]*)?$/,
          requireMessage: 'URL地址不能不空',
          formatMessage: '请输入正确的URL地址格式'
        },
        date: {
          reg: '',
          requireMessage: '请选择日期',
          formatMessage: ''
        },
        time: {
          reg: '',
          requireMessage: '请选择日期',
          formatMessage: ''
        }
      },
      rules: {
        id_input: [{ validator: validedFormItem, trigger: 'blur' }]
      }
    }
  },
  created() {
    this.inputOption = this.previewOption
    if (this.pathName[this.diff].isSetDefaultValue) {
      if (this.previewOption.answers.length > 0) {
        if (this.inputOption.inputType === 'date' || this.inputOption.inputType === 'time') {
          this.dateValue = this.previewOption.answers[0].questionValue
        } else {
          this.inputForm.id_input = this.previewOption.answers[0].questionValue
        }
      }
    }
  },

  methods: {
    validedForm() {
      let validedResult = false
      this.$refs['inputForm'].validate((result) => {
        if (result) {
          validedResult = false
        } else {
          validedResult = true
        }
      })
      return validedResult
    },
    checkValided() {
      if (this.inputOption.isMust === 0) {
        return false
      }
      // 判断当前题目是否必填，必填时才需要判断是否已答
      if (this.inputOption.isMust !== 1) {
        return true
      }
      // 判断当前题目是否已答
      if (!this.isAnswer) {
        return true
      }
      if ((this.inputOption.inputType === 'date' || this.inputOption.inputType === 'time') && this.dateValue.length === 0) {
        return true
      }
      // 判断是否输入值
      // if (
      //   this.inputOption.inputType !== "date" &&
      //   this.inputOption.inputType !== "time" &&
      //   this.inputForm.id_input.length === 0
      // ) {
      //   return true;
      // }
      // 当类型为number时判断输入的值是否满足设置的最大值和最小值
      if (
        this.inputOption.inputType === 'number' &&
        this.inputOption.maxValue !== this.inputOption.minValue &&
        (this.inputForm.id_input > this.inputOption.maxValue + 1 || this.inputForm.id_input < this.inputOption.minValue - 1)
      ) {
        return true
      }
      if (this.inputOption.inputType !== 'date' && this.inputOption.inputType !== 'time') {
        return this.validedForm()
      }
      return false
    },
    doValidedFlag() {
      return this.checkValided()
    },
    doValided() {
      this.isValided = this.checkValided()
      return this.isValided
    },
    handleInputChange(val) {
      this.answerVal = [
        {
          pvqId: localStorage.getItem('questId'), // 问卷id
          questionId: this.inputOption.id, // 题目id
          questionType: this.inputOption.type, // 题目类型
          questionValue: val // 输入的值
        }
      ]
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
.quesName {
  font-size: 16px;
}

.starRed {
  color: red;
}

.questionContainer {
  padding: 20px 0 0;
}

.chexMinMax {
  margin-left: 3px;
}

.el-input {
  width: 200px;
}
</style>
