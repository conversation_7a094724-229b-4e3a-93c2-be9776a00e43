<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="addTargetPlan-content-title">{{ $route.query.planId ? '编辑' : '新增' }}指标计划</div>
      <div class="content_box">
        <div v-if="$route.query.planId && planState === 1" class="tips"><img src="@/assets/images/targetAnalysis/tips.png" alt="" /><span>该计划已经执行,请谨慎修改</span></div>
        <div v-if="$route.query.planId && planState === 2" class="tips"><img src="@/assets/images/targetAnalysis/tips.png" alt="" /><span>该计划已经结束,不可以修改</span></div>
        <el-form ref="formInline" :model="addForm" :inline="true" class="form-inline" label-width="142px" :rules="rules">
          <el-form-item label="指标计划名称" prop="name">
            <el-input v-model.trim="addForm.name" maxlength="100" type="text" placeholder="请输入" class="inputWid" :disabled="planState == 2"></el-input>
          </el-form-item>
          <br />
          <el-form-item label="模板" prop="templateManageId">
            <el-select
              v-model.trim="addForm.templateManageId"
              :disabled="planState == 1 || planState == 2"
              filterable
              clearable
              placeholder="请输入"
              class="inputWid"
              @change="tempalteChange"
            >
              <el-option v-for="item in templateArr" :key="item.id" :label="item.name" :value="item.id"> </el-option>
            </el-select>
          </el-form-item>
          <br />
          <el-form-item label="周期规则" prop="cycleRuleId">
            <el-select v-model.trim="addForm.cycleRuleId" filterable :disabled="planState == 2" clearable placeholder="请输入" class="inputWid" @change="cycleChange">
              <el-option v-for="item in cycleArr" :key="item.id" :label="item.name" :value="item.id"> </el-option>
            </el-select>
          </el-form-item>
          <br />
          <el-form-item label="计划开始时间" prop="startTime">
            <el-date-picker
              v-model="addForm.startTime"
              type="date"
              :disabled="planState == 1 || planState == 2"
              value-format="yyyy-MM-dd"
              placeholder="选择日期"
              :picker-options="pickerOptionsStart"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="计划结束时间" prop="endTime">
            <el-date-picker
              v-model="addForm.endTime"
              type="date"
              :disabled="planState == 2 || addForm.longTerm"
              value-format="yyyy-MM-dd"
              placeholder="选择日期"
              :picker-options="pickerOptionsEnd"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="" style="margin-left: 15px">
            <el-checkbox v-model="addForm.longTerm" :disabled="planState == 2">长期</el-checkbox>
          </el-form-item>
          <br />
          <el-form-item label="计划说明" prop="remark">
            <el-input
              v-model.trim="addForm.remark"
              maxlength="500"
              :disabled="planState == 2"
              show-word-limit
              type="textarea"
              placeholder="请输入计划说明"
              class="inputWid"
            ></el-input>
          </el-form-item>
        </el-form>
        <div v-if="scopeType != 7">
          <el-button type="primary" :disabled="planState == 2" style="font-size: 14px" @click="selectRange()">选择考察范围</el-button>
        </div>
        <div v-if="scopeType == 7" class="scopeBox">
          <div class="list-item">
            <div class="list-item-main"><span>考核对象：</span><span>全局</span></div>
          </div>
        </div>
        <div v-else class="scopeBox">
          <div v-for="(item, index) in addForm.list" :key="index" class="list-item">
            <div class="list-item-main">
              <span>考核对象：</span><span>{{ item.objectName }}</span>
            </div>
            <div class="list-item-main">
              <span>对象类型：</span><span>{{ objectTypeList[item.objectType].text || '' }}</span>
            </div>
            <div class="list-item-main" @click="del(index)"><span>操作：</span><el-button :disabled="planState == 2" type="text">删除</el-button></div>
          </div>
        </div>
      </div>
      <template>
        <sinoDialog ref="dialogScope" :title="`选择${objectTypeList[scopeType].text}`" @sureDialog="sureDialog" @closeDialog="closeDialog">
          <ScopeChoice v-if="dialogTableVisible" ref="ScopeChoice" :scopeType="scopeType"></ScopeChoice>
        </sinoDialog>
      </template>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">取消</el-button>
      <el-button type="primary" @click="submitForm()">保存</el-button>
    </div>
  </PageContainer>
</template>
<script>
import moment from 'moment'
import sinoDialog from '../../operationPort/spaceManage/common/sinoDialog.vue'
import ScopeChoice from './components/ScopeChoice.vue'
export default {
  name: 'addTargetPlan',
  components: {
    sinoDialog,
    ScopeChoice
  },
  data() {
    return {
      // 时间判断
      pickerOptionsStart: {
        disabledDate: (time) => {
          let endDateVal = this.addForm.endTime
          if (endDateVal) {
            return time.getTime() > new Date(endDateVal + ' 00:00:00').getTime()
          }
        }
      },
      pickerOptionsEnd: {
        disabledDate: (time) => {
          let beginDateVal = this.addForm.startTime
          if (beginDateVal) {
            return time.getTime() < new Date(beginDateVal + ' 00:00:00').getTime()
          }
        }
      },
      addForm: {
        name: '', // 计划名称
        templateManageId: '', // 模板id
        templateManageName: '', // 模板名称
        cycleRuleId: '', // 周期id
        cycleRuleName: '', // 周期名称
        startTime: moment().format('YYYY-MM-DD'),
        endTime: moment().format('YYYY-MM-DD'),
        remark: '',
        longTerm: false, // 是否长期
        list: []
      },
      templateArr: [], // 模板列表
      allTemplateArr: [],
      cycleArr: [], // 周期列表
      allCycleList: [],
      scopeType: 0,
      rules: {
        name: { required: true, message: '请输入指标计划名称', trigger: 'blur' },
        templateManageId: { required: true, message: '请选择模板', trigger: 'change' },
        cycleRuleId: { required: true, message: '请设置周期规则', trigger: 'change' },
        startTime: { required: true, message: '请选择开始日期', trigger: 'change' },
        endTime: { required: true, message: '请选择结束日期', trigger: 'change' }
      },
      objectTypeList: {
        0: {
          text: '单位'
        },
        1: {
          text: '部门'
        },
        2: {
          text: '人员'
        },
        3: {
          text: '岗位'
        },
        4: {
          text: '职位'
        },
        5: {
          text: '空间'
        },
        6: {
          text: '设备'
        },
        7: {
          text: '全局'
        }
      },
      planState: '',
      dialogTableVisible: false
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    async init() {
      await this.getTemplateList()
      await this.getCycleList()
      const planId = this.$route.query.planId
      if (planId) {
        await this.getTargetPlanId()
      }
    },
    // 详情
    getTargetPlanId() {
      const planId = this.$route.query.planId
      this.$api.getTargetPlanById({ planId: planId }).then((res) => {
        if (res.code == 200) {
          this.planState = res.data.state
          this.addForm = res.data
          if (res.data.longTerm === 1) {
            this.addForm.longTerm = true
          } else {
            this.addForm.longTerm = false
          }
          this.scopeType = this.allTemplateArr.find((ele) => ele.id == this.addForm.templateManageId).type
        } else if (res.message) {
          this.$message.error(res.message)
        }
      })
    },
    submitForm() {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          const planId = this.$route.query.planId
          if (planId) {
            let data = {
              ...this.addForm,
              id: this.$route.query.planId ? this.$route.query.planId : ''
            }
            data.startTime = moment(this.addForm.startTime).format('YYYY-MM-DD') + ' 00:00:00'
            data.endTime = moment(this.addForm.endTime).format('YYYY-MM-DD') + ' 23:59:59'
            data.longTerm = data.longTerm ? 1 : 0
            this.$api
              .updateTargetPlan(data, { 'operation-type': 2, 'operation-id': this.$route.query.planId, 'operation-name': this.addForm.name })
              .then((res) => {
                if (res.code == 200) {
                  this.$message({
                    type: 'success',
                    message: '保存成功'
                  })
                  this.$router.go('-1')
                  this.$api.refreshTargetTemplateState({}).then((res) => {})
                } else {
                  this.$message({
                    type: 'error',
                    message: res.message || '保存失败'
                  })
                }
              })
              .catch(() => {})
          } else {
            let data = {
              ...this.addForm
            }
            data.startTime = moment(this.addForm.startTime).format('YYYY-MM-DD') + ' 00:00:00'
            data.endTime = moment(this.addForm.endTime).format('YYYY-MM-DD') + ' 23:59:59'
            data.longTerm = data.longTerm ? 1 : 0
            this.$api
              .saveTargetPlan(data, { 'operation-type': 1 })
              .then((res) => {
                if (res.code == 200) {
                  this.$message({
                    type: 'success',
                    message: '保存成功'
                  })
                  this.$router.go('-1')
                  this.$api.refreshTargetTemplateState({}).then((res) => {})
                } else {
                  this.$message({
                    type: 'error',
                    message: res.message || '保存失败'
                  })
                }
              })
              .catch(() => {})
          }
        }
      })
    },
    // 周期下拉
    async getCycleList() {
      await new Promise((resolve, reject) => {
        this.$api
          .targetConfigList({
            cycleLevel: '0',
            pageSize: 9999,
            page: 1
          })
          .then((res) => {
            if (res.code === '200') {
              this.allCycleList = res.data.records || []
              this.cycleArr = res.data.records || []
            }
          })
        setTimeout(() => {
          resolve()
        }, 200)
      })
    },
    // 模板下拉
    async getTemplateList() {
      await new Promise((resolve, reject) => {
        this.$api
          .templateManageList({
            pageSize: 9999,
            page: 1
          })
          .then((res) => {
            if (res.code === '200') {
              this.allTemplateArr = res.data.records || []
              this.templateArr = res.data.records || []
            }
          })
        setTimeout(() => {
          resolve()
        }, 200)
      })
    },
    // 模板改变
    tempalteChange(el) {
      if (this.addForm.list.length) {
        this.$confirm('是否更换模板，切换模板需要重新选择考察范围？', '信息提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          closeOnClickModal: false,
          type: 'warning'
        })
          .then(() => {
            this.addForm.list = []
          })
          .catch(() => {})
      }
      this.addForm.templateManageName = this.allTemplateArr.find((ele) => ele.id == el).name
      this.scopeType = this.allTemplateArr.find((ele) => ele.id == el).type
    },
    // 周期改变
    cycleChange(val) {
      this.addForm.cycleRuleName = this.allCycleList.find((ele) => ele.id == val).name
    },
    // 选择范围
    selectRange() {
      if (!this.addForm.templateManageId) {
        this.$message.error('请先选择模板')
        return
      }
      this.$refs.dialogScope.dialogTableVisible = true
      this.dialogTableVisible = true
    },
    // 删除
    del(i) {
      if (this.addForm.list.length > 0) {
        this.addForm.list.splice(i, 1)
        this.$message({
          message: '该对象已删除',
          type: 'success'
        })
      }
    },
    closeDialog() {
      this.$refs.dialogScope.dialogTableVisible = false
      this.dialogTableVisible = false
    },
    sureDialog() {
      let arr = []
      let arr1 = []
      arr = this.$refs.ScopeChoice.selectList
      switch (this.scopeType) {
        case 0:
          arr.forEach((el) => {
            arr1.push({
              objectName: el.unitComName,
              objectId: el.umId,
              objectType: this.scopeType
            })
          })
          break
        case 1:
          arr.forEach((el) => {
            arr1.push({
              objectName: el.deptName,
              objectId: el.id,
              objectType: this.scopeType
            })
          })
          break
        case 2:
          arr.forEach((el) => {
            arr1.push({
              objectName: el.staffName,
              objectId: el.id,
              objectType: this.scopeType
            })
          })
          break
        case 3:
          arr.forEach((el) => {
            arr1.push({
              objectName: el.postName,
              objectId: el.id,
              objectType: this.scopeType
            })
          })
          break
        case 5:
          arr.forEach((el) => {
            arr1.push({
              objectName: el.ssmName,
              objectId: el.id,
              objectType: this.scopeType
            })
          })
          break
        case 6:
          arr.forEach((el) => {
            arr1.push({
              objectName: el.assetName,
              objectId: el.id,
              objectType: this.scopeType
            })
          })
      }
      // this.addForm.list = this.removeSame1(this.addForm.list.concat(arr1))
      this.addForm.list = arr1
      this.$refs.dialogScope.dialogTableVisible = false
    },
    removeSame1(array) {
      let newArray = []
      for (let item of array) {
        // 使用JSON.stringfy()方法将数组和数组元素转换为JSON字符串之后再使用includes()方法进行判断
        if (JSON.stringify(newArray).includes(JSON.stringify(item))) {
          continue
        } else {
          // 不存在的添加到数组中
          newArray.push(item)
        }
      }
      return newArray
    }
  }
}
</script>
<style lang="scss" scoped>
.container-content > div {
  background-color: #fff;
  height: 100%;
  position: relative;
  .addTargetPlan-content-title {
    border-bottom: 1px solid #e4e7ed;
    padding: 13px 24px;
    cursor: pointer;
  }
  .content_box {
    padding: 16px 24px;
    background: #fff;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    height: 100%;
    .scopeBox {
      margin-top: 8px;
      width: 904px;
      .list-item {
        padding: 8px 24px;
        display: flex;
        font-size: 14px;
        font-weight: 400;
        width: 100%;
        .list-item-main {
          width: calc(100% / 3);
        }
        .list-item-main:nth-child(2) {
          text-align: center;
        }
        .list-item-main:nth-child(3) {
          text-align: right !important;
        }
        .list-item-main:nth-child(1),
        span:nth-child(1) {
          color: #7f848c;
        }
        .list-item-main:nth-child(1) span:nth-child(2) {
          color: #333333;
          margin-left: 8px;
        }
        .list-item-main:nth-child(3) span:nth-child(2) {
          cursor: pointer;
          margin-left: 8px;
          color: #3562db;
        }
      }
    }
  }
  .form-inline {
    .el-input,
    .el-select,
    .el-cascader {
      width: 265px;
    }
  }
  .form-inline .inputWid {
    width: 762px;
  }
}
.el-select-dropdown {
  min-width: 200px !important;
}
.el-select-dropdown__item {
  min-width: 200px !important;
}
.tips {
  margin-bottom: 8px;
  background: #fff7e8;
  border-radius: 4px;
  border: 1px solid rgba(255, 125, 0, 0.5);
  height: 40px;
  line-height: 40px;
  padding: 0 16px;
  width: 904px;
  span {
    margin-left: 8px;
  }
}
</style>
