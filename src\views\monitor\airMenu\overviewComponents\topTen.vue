<!-- 运行监测 -->
<template>
  <ContentCard
    :title="item.componentTitle"
    :scrollbarHover="true"
    :hasIcon="false"
    :cstyle="{ width: '100%', height: '100%' }"
    class="drag_class"
    :hasMoreOper="['more', 'edit']"
    @more-oper-event="(val) => $emit('all-more-Oper', val, item.componentName, item.chartType)"
  >
    <div slot="title-right" class="data-btns">
      <span :class="{ 'active-btn': totalCostDateType == 'week' }" @click="changeDateType('week')">周</span>
      <span :class="{ 'active-btn': totalCostDateType == 'month' }" @click="changeDateType('month')">月</span>
      <span :class="{ 'active-btn': totalCostDateType == 'year' }" @click="changeDateType('year')">年</span>
    </div>
    <div slot="content" class="operation-list">
      <echarts :ref="`topTenEchars${chartType}`" :domId="`topTenEchars${chartType}`" :isTrigger="true" :xyType="'yAxis'" width="100%" height="100%" />
    </div>
  </ContentCard>
</template>

<script>
import moment from 'moment'
export default {
  name: 'topTen',
  props: {
    item: {
      type: Object,
      default: () => {}
    },
    projectCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      api: {
        1: {
          api: 'getAirConditionerTime',
          unit: '小时'
        },
        4: {
          api: 'newAirBreakdown',
          unit: '次'
        },
        5: {
          api: 'newAirOffLine',
          unit: '次'
        }
      },
      loading: false,
      totalCostDateType: 'year',
      operationList: [],
      chartType: '1'
    }
  },

  watch: {
    'item.chartType': {
      handler(val) {
        if (val) {
          this.getTopTenData()
          this.chartType = val
        }
      },
      immediate: true
    }
  },
  methods: {
    // 处理统计数据
    getTopCountEchats(data = [], unit) {
      let option = {}
      if (data.length) {
        option = {
          backgroundColor: '#ffffff',
          color: [this.item.barColor],
          textStyle: {
            color: '#333'
          },
          label: {
            show: true,
            position: 'right',
            color: '#121F3E',
            fontSize: '12px'
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          grid: {
            // 让图表占满容器
            top: '3%',
            left: '3%',
            right: '10%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            name: unit,
            type: 'value',
            minInterval: 1,
            gridIndex: 0,
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              color: '#121F3E',
              fontSize: 12
            }
          },
          yAxis: {
            data: data.map((x) => x.name),
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            triggerEvent: true,
            axisLabel: {
              color: '#121F3E',
              fontSize: 13,
              margin: 6,
              formatter: function (params) {
                var val = ''
                if (params.length > 4) {
                  val = params.substr(0, 5) + '...'
                  return val
                } else {
                  return params
                }
              }
            }
          },
          series: [
            {
              type: 'bar',
              barCategoryGap: '10px',
              itemStyle: {
                normal: {
                  color: '#3562DB' // 设置柱子的颜色为红色
                }
              },
              data: data.map((x) => x.value)
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      return option
    },
    // 更改时间类型
    changeDateType(dateType) {
      this.totalCostDateType = dateType
      this.getTopTenData()
    },
    getTopTenData() {
      const timeParamsList = {
        week: {
          // 本周
          startTime: moment().startOf('week').format('YYYY-MM-DD'),
          endTime: moment().format('YYYY-MM-DD')
        },
        month: {
          // 本月
          startTime: moment().startOf('month').format('YYYY-MM-DD'),
          endTime: moment().format('YYYY-MM-DD')
        },
        year: {
          // 本年
          startTime: moment().startOf('year').format('YYYY-MM-DD'),
          endTime: moment().format('YYYY-MM-DD')
        }
      }
      this.$api[this.api[this.item.chartType].api]({ projectCode: this.projectCode, ...timeParamsList[this.totalCostDateType] }).then((res) => {
        if (res.code == 200) {
          let topData = []
          if (this.item.chartType == '1') {
            // 运行时长
            topData = res.data.airRunTime.map((ele) => {
              return {
                name: ele.menuName,
                value: ele.runTime
              }
            })
          } else if (this.item.chartType == '4') {
            // 故障
            topData = res.data.airConditionBreakList.map((ele) => {
              return {
                name: ele.menuName,
                value: ele.breakCount
              }
            })
          } else if (this.item.chartType == '5') {
            // 离线
            topData = res.data.airConditionOffList.map((ele) => {
              return {
                name: ele.menuName,
                value: ele.breakCount
              }
            })
          }
          topData = topData.sort((a, b) => a.value - b.value)
          this.$refs[`topTenEchars${this.item.chartType}`].init(this.getTopCountEchats(topData, this.api[this.item.chartType].unit))
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.data-btns {
  position: absolute;
  right: 7%;
  display: flex;
  align-items: center;
  justify-content: space-between;

  & > span {
    width: 44px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    margin: 0 4px;
    background-color: #f6f5fa;
    font-size: 14px;
    font-family: 'PingFang SC-Medium', 'PingFang SC';
    border: none;
    border-radius: 2px;
    color: #7f848c;
    cursor: pointer;
  }

  .active-btn {
    background-color: #e6effc !important;
    color: #3562db !important;
    border-color: #e6effc !important;
  }
}
.operation-list {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
}
</style>
