<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    title="授权状态"
    width="30%"
    :visible="visible"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <div class="content">
      <el-form ref="ruleForm" :model="form" :rules="rules" label-width="auto">
        <el-form-item prop="activationStats" label="当前状态">
          <span>{{ activateTypeList.find((item) => item.value == form.activationStats)?.label }}</span>
        </el-form-item>
        <el-form-item prop="way" label="激活方式">
          <el-radio-group v-model="form.way" @change="changeActivateListType">
            <el-radio v-for="item in howToActivateList" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <div v-if="form.way == 1">
          <el-form-item prop="appId" label="appId">
            <el-input v-model.trim="form.appId" placeholder="请输入" maxlength="50"> </el-input>
          </el-form-item>
          <el-form-item prop="sdkKey" label="sdkKey">
            <el-input v-model.trim="form.sdkKey" placeholder="请输入" maxlength="50"> </el-input>
          </el-form-item>
          <el-form-item prop="activeKey" label="activeKey">
            <el-input v-model.trim="form.activeKey" placeholder="请输入" maxlength="50"> </el-input>
          </el-form-item>
        </div>
        <div v-else-if="form.way == 2">
          <el-form-item label="激活文件：" prop="fileUrl">
            <el-upload
              action=""
              list-type="picture-card"
              :file-list="fileList"
              :accept="uploadAcceptDict['picture'].type"
              :http-request="httpRequset"
              :on-preview="handlePictureCardPreview"
              :on-remove="handleRemove"
            >
              <i class="el-icon-plus"></i>
              <div slot="tip" class="el-upload__tip">支持{{ uploadAcceptDict['picture'].type }},且大小不超过{{ uploadAcceptDict['picture'].fileSize }}M</div>
            </el-upload>
          </el-form-item>
        </div>
      </el-form>
      <el-dialog v-dialogDrag :modal="false" :visible.sync="dialogVisible">
        <img width="100%" :src="dialogImageUrl" alt="" />
      </el-dialog>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="confirm('ruleForm')">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { howToActivateList, activateTypeList } from '../component/dict.js'
import { uploadAcceptDict } from '@/util/dict.js'
export default {
  name: 'PreSaleStateDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    selectItems: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      howToActivateList,
      activateTypeList,
      dialogVisible: false,
      dialogImageUrl: '',
      form: {
        activationStats: '',
        way: '',
        appId: '',
        sdkKey: '',
        activeKey: '',
        fileUrl: ''
      },
      rules: {
        appId: [{ required: true, message: '请输入', trigger: 'change' }],
        sdkKey: [{ required: true, message: '请输入', trigger: 'change' }],
        activeKey: [{ required: true, message: '请输入', trigger: 'change' }],
        fileUrl: [{ required: true, message: '请选择', trigger: 'change' }]
      },
      fileList: [],
      uploadAcceptDict
    }
  },
  mounted() {
    this.getManagePsd()
  },
  methods: {
    // 初始化获取管理密码
    getManagePsd() {
      this.$api.supplierAssess.getActivationInfo({ id: this.selectItems.id }).then((res) => {
        if (res.code == 200) {
          Object.assign(this.form, res.data)
          if (this.form.fileUrl) {
            this.fileList.push({
              url: this.$tools.imgUrlTranslation(this.form.fileUrl),
              fileKey: this.form.fileUrl
            })
          }
        }
      })
    },
    beforeUpload(file) {
      const fileSize = this.uploadAcceptDict['picture'].fileSize
      const isLt5M = file.size / 1024 / 1024 < fileSize
      const extension = file.name.substring(file.name.lastIndexOf('.'))
      const isFormat = this.uploadAcceptDict['picture'].type.includes(extension)
      if (!isFormat) {
        this.$message.error(`上传图片只能是 ${this.uploadAcceptDict['picture'].type}格式!`)
        return false
      }
      if (!isLt5M) {
        this.$message.error(`上传文件大小不能超过 ${fileSize}MB!`)
        return false
      }
    },
    httpRequset(file) {
      const params = new FormData()
      params.append('file', file.file)
      this.$api.uploadFile(params).then((res) => {
        if (res.code === '200') {
          this.$message({
            message: '上传成功',
            type: 'success'
          })
          this.fileList.push({
            name: res.data.name,
            url: res.data.picUrl,
            fileKey: res.data.fileKey
          })
          this.form.fileUrl = res.data.fileKey
        } else {
          this.$message({
            message: res.message,
            type: 'warning'
          })
        }
      })
    },
    handleRemove(file, fileList) {
      this.fileList = fileList
      this.form.fileUrl = ''
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    changeActivateListType(type) {
      if (type == 1) {
        this.form.fileUrl = ''
        this.fileList = []
      } else {
        this.form.appId = ''
        this.form.sdkKey = ''
        this.form.activeKey = ''
      }
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('close')
    },
    confirm(formName) {
      let params = JSON.parse(JSON.stringify(this.form))
      params.id = this.selectItems.id
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$api.supplierAssess.updateActivationInfo(params).then((res) => {
            if (res.code == 200) {
              this.$message.success('设置考勤密码成功')
              this.$emit('close')
            } else {
              this.$message.error(res.msg)
            }
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  max-height: 400px !important;
  overflow: auto;
  background-color: #fff !important;
  padding: 10px 35px;
}
.model-dialog {
  // padding: 0 !important;
}
::v-deep .el-form-item__error {
  height: 20px;
  width: 300px;
  color: #f56c6c;
  font-size: 12px;
  line-height: 20px;
  padding-top: 4px;
  position: absolute;
  left: 0;
}
</style>
