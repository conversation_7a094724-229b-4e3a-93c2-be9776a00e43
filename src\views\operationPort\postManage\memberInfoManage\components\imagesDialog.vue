<template>
  <el-dialog v-if="imagesShow" v-dialogDrag title="图片查看" width="50%" :visible.sync="imagesShow"
    custom-class="model-dialog" :before-close="closeDialog">
    <div class="images-content">
      <el-carousel trigger="click" height="300px" :autoplay="false">
        <el-carousel-item v-for="(image,index) in picList" :key="index">
          <img :src="image" alt="carousel image" style="width: 100%; height: 100%; object-fit: cover;">
        </el-carousel-item>
      </el-carousel>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取消</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  name: 'imagesDialog',
  props: {
    imagesShow: {
      type: Boolean,
      default: false
    },
    picList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
    }
  },
  mounted() {
  },
  methods: {
    closeDialog() {
      this.$emit('closeImagesDialog')
    },
  }
}
</script>
<style lang="scss" scoped>
.model-dialog {
  .images-content {
    margin: 0 auto;
    background: #fff;
    text-align: center;
    padding: 10px 0;
    border-radius: 4px;
    max-height: 400px;
    width: 100%;
  }
}
</style>
