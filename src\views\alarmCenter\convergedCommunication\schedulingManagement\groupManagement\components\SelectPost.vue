<template>
  <el-dialog title="选择岗位" width="55%" :visible.sync="postDialogShow" custom-class="model-dialog"
    :before-close="closeDialog">
    <div class="content">
      <div class="sino_table">
        <el-table ref="sinoTable" v-loading="tableLoading" :data="tableData" :row-key="getRowKeys" height="300px" stripe
          border @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="50" align="center" :reserve-selection="true"></el-table-column>
          <el-table-column v-for="(column, index) in tableColumn" :key="index" :prop="column.prop" :label="column.label"
            :min-width="column.minWidth" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ scope.row[column.prop] }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="submitDialog">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'SelectPost',
  components: {},
  props: {
    postDialogShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tableLoading: false,
      tableData: [],
      multipleSelection: [],
      selectList: [],
      tableColumn: [
        {
          prop: 'postName',
          label: '名称'
        },
        {
          prop: 'postName',
          label: '职责'
        }
      ]
    }
  },
  mounted() {
    this.postListFn()
  },
  methods: {
    getRowKeys(row) {
      return row.id
    },
    //  获取岗位列表
    postListFn() {
      this.tableLoading = true
      this.$api.selectByList().then((res) => {
        this.tableLoading = false
        if (res.code == 200) {
          this.tableData = res.data
        }
      })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
      this.selectList = JSON.parse(JSON.stringify(this.multipleSelection))
    },
    closeDialog() {
      this.$emit('closePostDialog')
    },
    submitDialog() {
      this.$emit('submitPostDialog', this.selectList)
    }
  }
}
</script>
<style lang="scss" scoped>
.model-dialog {
  .content {
    margin: 0 auto;
    background: #fff;
    border-radius: 4px;
    width: 100%;
    height: 100%;
  }
}
</style>

