<template>
  <PageContainer>
    <div slot="header" class="control-btn-header">
      <div class="search-from">
        <el-input v-model="searchFrom.orderNo" placeholder="订单号" clearable style="width: 200px"></el-input>
        <el-input v-model="searchFrom.personName" placeholder="订餐人" clearable style="width: 200px"></el-input>
        <!-- <el-select v-model="searchFrom.querySection" placeholder="时间范围" clearable>
          <el-option v-for="item in timeTypeList" :key="item.id" :label="item.label" :value="item.id"> </el-option>
        </el-select> -->
        <el-date-picker
          v-model="dataRange"
          type="datetimerange"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
        >
        </el-date-picker>
        <!-- <el-input v-model="searchFrom.proofNum" placeholder="凭证号" clearable style="width: 200px"></el-input>
        <el-input v-model="searchFrom.userName" placeholder="用户名称" clearable style="width: 200px"></el-input> -->
        <div style="display: inline-block">
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="searchForm">查询</el-button>
        </div>
      </div>
      <el-dialog title="查看" :visible.sync="dialogVisible" width="900px" :before-close="handleClose" custom-class="model-dialog">
        <div class="dialogs">
          <div class="item">
            <span>用户姓名：</span>
            <span>{{ currentData.personName }}</span>
          </div>
          <div class="item">
            <span>用户电话：</span>
            <span>{{ currentData.personMobile }}</span>
          </div>
          <div class="item">
            <span>科室名称：</span>
            <span>{{ currentData.deptName }}</span>
          </div>
          <div class="item">
            <span>订餐地点：</span>
            <span>{{ currentData.address }}</span>
          </div>
          <div class="item">
            <span>餐次：</span>
            <span>{{ currentData.mealName }}</span>
          </div>
          <div class="item">
            <span>食堂：</span>
            <span>{{ currentData.canteenName }}</span>
          </div>
          <div class="item">
            <span>需求送达时间：</span>
            <span>{{ currentData.deliveryTime }}</span>
          </div>
          <div class="item">
            <span>订单备注：</span>
            <span>{{ currentData.remark }}</span>
          </div>
          <div class="food-name">餐品名称</div>
          <TablePage ref="table2" :showPage="false" :tableColumn="tableColumn2" :data="currentData.mealList" height="calc(100% - 85px)" />
        </div>
        <!-- <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="handleClose">取 消</el-button>
        </span> -->
      </el-dialog>
    </div>
    <div slot="content" class="table-content">
      <div class="data-box">
        <div class="total-num">
          <span>订单总数：</span>
          <span>{{ pageData.total }}</span>
        </div>
        <div class="total-amount">
          <span>订单金额（元）：</span>
          <span>{{ totalPrice }}</span>
        </div>
      </div>
      <TablePage
        ref="table"
        v-loading="tableLoading"
        :showPage="true"
        :tableColumn="tableColumn"
        :data="tableData"
        height="calc(100% - 120px)"
        :pageData="pageData"
        :pageProps="pageProps"
        @pagination="paginationChange"
        @row-click="handleRowClick"
      />
    </div>
  </PageContainer>
</template>
<script lang="jsx">
import moment from 'moment'

import empty from '@/assets/images/parkingLot/parking-null.png'
import alipay from '@/assets/images/service/alipay.png'
import wechat from '@/assets/images/service/wechat.png'
import tel from '@/assets/images/service/tel.png'
import others from '@/assets/images/service/others.png'
export default {
  name: 'cateringComprehensive',
  components: {},
  data() {
    return {
      empty,
      alipay,
      wechat,
      tel,
      others,
      dialogVisible: false,
      rowList: {},
      dataRange: [], // 时间范围
      searchFrom: {
        // querySection: '', // 时间
        orderNo: '',
        personName: ''
        // proofNum: '', // 凭证号
        // userName: '' // 用户名称
      },
      timeTypeList: [
        {
          id: 1,
          label: '今天'
        },
        {
          id: 2,
          label: '昨天'
        },
        {
          id: 3,
          label: '上周'
        },
        {
          id: 4,
          label: '上个月'
        },
        {
          id: 5,
          label: '最近60天'
        }
      ],
      tableLoading: false,
      tableColumn: [
        // {
        //   type: 'selection',
        //   align: 'center',
        //   selectable: (row) => {
        //     return row.id !== 1
        //   }
        // },
        {
          prop: '',
          label: '序号',
          width: '60',
          formatter: (scope) => {
            return (this.pageData.current - 1) * this.pageData.size + scope.$index + 1
          }
        },
        {
          prop: 'orderNo',
          label: '订单号'
        },
        {
          prop: 'personName',
          label: '订餐人',
          width: '90'
        },
        {
          prop: 'deptName',
          label: '科室'
        },
        {
          prop: 'address',
          label: '地址'
        },
        {
          prop: 'mealName',
          label: '餐次'
        },
        // {
        //   prop: '',
        //   label: '视频图片',
        //   width: '90',
        //   render: (h, row) => {
        //     return (
        //       <div class="operationBtn">
        //         <span class="operationBtn-span" style="color: #5482ee" onClick={() => this.handleDetailEvent(row.row)}>
        //           查看
        //         </span>
        //       </div>
        //     )
        //   }
        // },
        {
          prop: 'orderNum',
          label: '餐品数量'
        },
        {
          prop: 'orderPrice',
          label: '订单总价',
          width: '90'
        },
        {
          prop: 'payMethod',
          label: '支付方式',
          prop: 'payMethod'

          // render: (h, row) => {
          //   return (
          //     <div>
          //       {row.row.payMethod == '支付宝' && (
          //         <div>
          //           <img class="table-icon" src={alipay} />
          //           <span>&emsp;支付宝</span>
          //         </div>
          //       )}
          //       {row.row.payMethod == '微信' && (
          //         <div>
          //           <img class="table-icon" src={wechat} />
          //           <span>&emsp;微信</span>
          //         </div>
          //       )}
          //       {row.row.payMethod == '其他' && (
          //         <div>
          //           <img class="table-icon" src={others} />
          //           <span>&emsp;其他</span>
          //         </div>
          //       )}
          //       {row.row.payMethod == '来电' && (
          //         <div>
          //           <img class="table-icon" src={tel} />
          //           <span>&emsp;来电</span>
          //         </div>
          //       )}
          //     </div>
          //   )
          // }
        },
        {
          prop: 'createTime',
          label: '下单时间'
        },
        {
          prop: 'stateName',
          label: '状态',
          width: '90'
        }
        // {
        //   prop: 'operationModel',
        //   label: '操作方式'
        // }
      ],
      tableColumn2: [
        {
          label: '序号',
          width: '60',
          formatter: (scope) => {
            return scope.$index + 1
          }
        },
        {
          prop: 'mealName',
          label: '餐品名称'
        },
        {
          prop: 'unitPrice',
          label: '折后单价'
        },
        {
          prop: 'mealNum',
          label: '数量'
        },
        {
          prop: 'totalPrice',
          label: '总价'
        }
      ],
      tableData: [],
      pageData: {
        current: 1,
        size: 15,
        total: 0
      },
      pageProps: {
        current: 'current',
        pageSize: 'size',
        total: 'total'
      },
      currentData: ''
    }
  },
  computed: {
    totalPrice() {
      let total = 0
      this.tableData.forEach((i) => {
        total += i.orderPrice
      })
      return total
    }
  },
  mounted() {
    this.getRecordList()
  },
  methods: {
    date() {
      const now = new Date()
      return moment(now).format('yyyy-MM-DD HH:mm:ss')
    },
    // 获取人员列表
    getRecordList() {
      let param = {
        ...this.searchFrom,
        startTime: this.dataRange[0] || '',
        endTime: this.dataRange[1] || this.date(),
        pageSize: this.pageData.size,
        pageNum: this.pageData.current
      }
      this.tableLoading = true
      this.$api
        .getDiningOrder(param)
        .then((res) => {
          console.log(res, 'sssssssss')
          if (res.code == 200) {
            res.data.list.forEach((i) => {
              i.whether = '否'
            })
            this.tableData = res.data.list
            this.pageData.total = res.total
          }
          this.tableLoading = false
        })
        .catch(() => {
          this.tableLoading = false
        })
    },
    // 查询
    searchForm() {
      this.pageData.current = 1
      this.getRecordList()
    },
    // 重置查询
    resetForm() {
      Object.assign(this.$data.searchFrom, this.$options.data().searchFrom)
      this.dataRange = []
      this.searchForm()
    },
    // 查看详情
    handleDetailEvent(row) {
      console.log(row, 'sssssss')
      this.rowList = row
      this.dialogVisible = true
    },
    handleClose() {
      this.dialogVisible = false
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getRecordList()
    },
    // 判断当前页是否是最后一页
    isLastPage(deleteNum) {
      let deleteAfterPage = Math.ceil((this.pageData.total - deleteNum) / this.pageData.size)
      let currentPage = this.pageData.current > deleteAfterPage ? deleteAfterPage : this.pageData.current
      this.pageData.current = currentPage < 1 ? 1 : currentPage
    },
    handleRowClick(row) {
      console.log(row)
      this.dialogVisible = true
      this.currentData = row
      // this.$api.getFoodOrderDetails({}).then((res) => {
      //   if (res.code == 200) {
      //     this.currentData = res.data
      //   }
      // })
    }
  }
}
</script>
<style lang="scss" scoped>
.control-btn-header {
  padding: 10px !important;
  .search-from {
    display: flex;
    & > div {
      margin-right: 10px;
    }
  }
}

.table-content {
  margin-top: 15px;
  border-radius: 4px;
  height: calc(100% - 15px);
  padding: 10px;
  background-color: #fff;
}
.dialogs {
  width: 100%;
  padding: 10px;
  height: 725px;
}
.dialogs .item {
  display: flex;
  margin-bottom: 8px;
}
.dialogs .item > span:nth-child(1) {
  width: 16%;
  font-size: 16px;
  color: #666666;
}
.dialogs .item > span:nth-child(2) {
  font-size: 16px;
}
.dialogs .el-textarea {
  width: 50%;
}
.entrance {
  display: flex;
  padding: 15px;
  background-color: #e6effc;
  > div:nth-child(1) {
    width: 30%;
    > p {
      width: 40px;
      text-align: center;
      margin: 54px auto;
    }
  }
  > div:nth-child(2) {
    width: 70%;
  }
}
.entranceTxt {
  display: flex;
  > p:nth-child(1) {
    width: 100px;
    font-size: 14px;
    color: #666666;
  }
  > p:nth-child(2) {
    text-align: left;
    font-size: 14px;
    color: #333333;
  }
}
.admissionPictures {
  width: 100%;
  height: 200px;
  margin-bottom: 15px;
  img {
    width: 100%;
    height: 200px;
  }
}
::v-deep .el-dialog {
  // height: 800px;
  background-color: #fff;
}
::v-deep .model-dialog .el-dialog__body {
  overflow: hidden;
  // max-height: 800px;
}
::v-deep .el-date-editor .el-icon-time {
  transform: translateY(-3px);
}
.data-box {
  display: flex;
  margin: 8px 16px;
  margin-left: 0;
}
.data-box > div {
  background-color: rgba(53, 98, 219, 0.06);
  padding: 12px 24px;
  border-radius: 4px;
}
.data-box > div > span:nth-child(1) {
  font-size: 16px;
  color: #333;
}
.data-box > div > span:nth-child(2) {
  font-size: 16px;
  font-weight: bold;
  color: #3a62d8;
}
.data-box .total-num {
  margin-right: 24px;
}
.food-name {
  font-size: 18px;
  margin: 8px 0;
  font-weight: bold;
}
::v-deep .table-icon {
  width: 18px;
  transform: translateY(-2px);
}
</style>
<style lang="scss">
.operationBtn-span {
  margin-right: 10px;
}
</style>
