<template>
  <div class="main">
    <el-dialog
      v-dialogDrag
      :modal="false"
      custom-class="mainDialog main"
      append-to-body
      :visible.sync="workOrderDealShow"
      :close-on-click-modal="false"
      :before-close="closeDialog"
      class="personDialog"
    >
      <template slot="title">
        <span class="dialog-title">{{ (dealType === 'add' ? '新建' : '') + workTypeName + '工单' + (dealType === 'deal' ? '（ 处理 ）' : '') }}</span>
        <div class="right-tips"><span class="v-img"></span>{{ workOrderDetail ? workOrderDetail.taskWorkHint : '' }}</div>
      </template>
      <div class="dialog-content">
        <olgMaintenance
          v-if="newWorkTypeCode === '2' || workOrderDetail.olgTaskManagement.workTypeCode === '2'"
          ref="olgMaintenance"
          :dealType="dealType"
          routerFrom="local"
          :workOrderDetail="workOrderDetail"
          @save="getSaveCallback"
        />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="sino-button-sure" type="primary" plain @click="closeDialog">取 消</el-button>
        <el-button class="sino-button-sure" type="primary" @click="savePeople">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import olgMaintenance from './olgMaintenance.vue'
export default {
  name: 'workOrderDeal',
  components: {
    olgMaintenance
  },
  props: {
    workOrderDealShow: {
      type: Boolean,
      default: false
    },
    dealType: {
      type: String,
      default: 'add'
    },
    workTypeCode: {
      type: String,
      default: ''
    },
    workTypeName: {
      type: String,
      default: ''
    },
    workTypeId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      workOrderDetail: {
        olgTaskManagement: {
          workTypeCode: ''
        }
      },
      newWorkTypeCode: ''
    }
  },
  watch: {},
  created() {
    if (this.dealType === 'deal') {
      this.newWorkTypeCode = ''
      this.getWorkOrderOper()
    } else {
      this.newWorkTypeCode = this.workTypeCode
      this.getWorkOrderToAdd()
    }
  },
  mounted() {},
  methods: {
    getWorkOrderOper() {
      const params = {
        id: this.workTypeId,
        operType: 'placeOrder'
      }
      this.$api.getWorkOrderOper(params).then((res) => {
        this.workOrderDetail = res
      })
    },
    getWorkOrderToAdd() {
      const params = {
        type: 1,
        workTypeCode: this.workTypeCode,
        workTypeName: this.getWorkTypeLetter(this.workTypeCode)
      }
      this.$api.getWorkOrderToAdd(params).then((res) => {
        this.workOrderDetail = res
      })
    },
    getWorkTypeLetter(code) {
      switch (code) {
        case '1':
          return 'WX'
      }
    },
    // 取消按钮
    closeDialog() {
      this.$emit('closeDialog')
    },
    // 确认按钮
    savePeople() {
      this.$refs.olgMaintenance.saveForm()
    },
    getSaveCallback(item) {
      this.$emit('workOrderSure', item)
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
::v-deep .mainDialog {
  width: 55%;
  margin-top: 10vh !important;
  height: 80vh;
  background: #fff;
  // border: 1px solid #2f5596;
  .dialog-title {
    display: inline-block;
    width: 38%;
  }
  .right-tips {
    width: 50%;
    background: linear-gradient(90deg, rgba(255, 36, 36, 0) 0%, rgba(255, 43, 43, 0.4) 50%, rgba(255, 49, 49, 0) 100%);
    height: 30px;
    line-height: 30px;
    text-align: center;
    color: #fff;
    display: flex;
    justify-content: center;
    .v-img {
      margin: auto 0;
      margin-right: 10px;
      display: inline-block;
      height: 20px;
      width: 20px;
      background: center url('~@/assets/images/workOrder/volume.png') no-repeat;
      background-size: 100% 100%;
    }
  }
  .el-dialog__body {
    padding: 10px 20px;
    max-height: calc(100% - 130px);
    height: calc(100% - 130px);
    .dialog-content {
      width: 100%;
      height: 100%;
    }
  }
  .el-dialog__footer {
    padding-right: 30px;
  }
}
::v-deep .el-table-column--selection .cell {
  padding-left: 0.875rem;
  padding-right: 0.875rem;
  text-overflow: initial;
}
</style>
