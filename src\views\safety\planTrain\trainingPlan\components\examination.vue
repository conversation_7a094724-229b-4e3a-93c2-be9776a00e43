<template>
  <div class="heard">
    <div>
      <div style="width: 80%;">
        <el-form ref="form" :model="form" label-width="160px">
          <div>
            <el-form-item label="试卷名称" v-if="setingOpty == 1 || setingOpty == 2">
              <el-input v-model="formInfo.name" maxlength="30" show-word-limit placeholder="最多30个字符"
                style="width: 440px;"></el-input>
            </el-form-item>
            <el-form-item label="通过分数" v-if="setingOpty == 1 || setingOpty == 2">
              <el-input v-model="formInfo.passScore" maxlength="30" show-word-limit placeholder="通过分数"
                style="width: 440px;"></el-input>
            </el-form-item>
            <el-form-item label="答题时长" v-if="setingOpty == 2">
              <el-input placeholder="请输入答题时长" v-model="formInfo.duration" style="width: 440px;">
                <template slot="append">分钟</template>
              </el-input>
            </el-form-item>
            <!-- <el-form-item label="考试通过自动发放证书" v-if="setingOpty == 1 || setingOpty == 2">
              <el-select v-model="formInfo.credentialId" placeholder="请选择完成获取证书" style="width: 440px;">
                <el-option v-for="item in certificateList" :key="item.id" :label="item.certificateName" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item> -->
            <el-form-item label="是否录入试题" v-if="setingOpty == 1 || setingOpty == 2">
              <el-radio-group v-model="formInfo.rejectDeal">
                <el-radio v-for="item in noPassList" :key="item.id" :label="item.id">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="试卷描述" v-if="setingOpty == 1 || setingOpty == 2">
              <el-input v-model="formInfo.disc" type="textarea" show-word-limit maxlength="200"
                placeholder="请输入描述,最多输入200个字符" style="width: 440px;"></el-input>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <div v-if="setingOpty == 1">
        <voluntarily ref="voluntarily" :autoInfo="autoInfo" :scoreInfo="scoreInfo" :tmId="tmId"
          @submitAuto="submitAuto">
        </voluntarily>
      </div>
      <div v-if="setingOpty == 2">
        <manualPperation ref="manualPperation" :noAutoList="noAutoList" :scoreInfo="scoreInfo" :tmId="tmId"
          @submitAuto="submitAuto"></manualPperation>
      </div>
    </div>
    <div v-if="setingOpty == 3">
      <div class="deawer_conter">
        <div class="deawer_heard">
          <el-input v-model="formInlineEamian.name" placeholder="请输入计划名称"
            style="width: 200px; margin-right: 10px;"></el-input>
          <el-cascader v-model="formInlineEamian.deptId" placeholder="请选择所属部门" :options="deptList" :props="deptProps"
            clearable style="width: 280px; margin-right: 10px;">
          </el-cascader>
          <el-cascader v-model="formInlineEamian.subjectId" clearable class="sino_sdcp_input mr15"
            style="width: 280px; margin-right: 10px;" :options="subjectList" :props="subjectProps"
            placeholder="请选择所属科目"></el-cascader>
          <el-select v-model="formInlineEamian.examStatus" placeholder="请选择试卷状态"
            style="width: 280px; margin-right: 10px;">
            <el-option v-for="item in examStatusList" :key="item.id" :label="item.label" :value="item.id"></el-option>
          </el-select>
          <el-date-picker v-model="timeLine" type="daterange" value-format="yyyy-MM-dd" range-separator="至"
            start-placeholder="开始日期" end-placeholder="结束日期" style="width: 280px; margin-right: 10px;">
          </el-date-picker>
          <el-button type="primary" plain @click="resetFormExamin">重置</el-button>
          <el-button type="primary" @click="searchExamin">查询</el-button>
        </div>
        <div class="table_conter">
          <el-table ref="userTable" v-loading="tableLoading" :data="tableData" tyle="width: 100%;" height="100%" border
            stripe title="双击查看详情" @selection-change="handleSelectionChangeExamin">
            <el-table-column type="selection" width="55" fixed="left" align="center"></el-table-column>
            <el-table-column label="序号" width="55" align="center" show-overflow-tooltip>

              <template slot-scope="scope">
                <span>{{
          (paginationData.pageNo - 1) * paginationData.pageSize +
          scope.$index +
          1
        }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="试卷计划名称" align="center" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="deptName" label="所属部门" align="center" show-overflow-tooltip></el-table-column>
            <el-table-column prop="subjectName" label="所属科目" align="center" show-overflow-tooltip></el-table-column>
            <el-table-column prop="createTime" label="考试期限" align="center" show-overflow-tooltip>

              <template slot-scope="scope">
                <span>{{ moment(scope.row.startTime).format("YYYY-MM-DD") }}至{{
          moment(scope.row.endTime).format("YYYY-MM-DD") }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="duration" label="时长" align="center" show-overflow-tooltip></el-table-column>
            <el-table-column prop="studentNum" label="考试学员" align="center" show-overflow-tooltip></el-table-column>
            <el-table-column prop="count" label="考试数量" align="center" show-overflow-tooltip></el-table-column>
            <el-table-column prop="score" label="总分数" align="center" show-overflow-tooltip></el-table-column>
            <el-table-column prop="passScore" label="通过分数" align="center" show-overflow-tooltip></el-table-column>
            <el-table-column label="计划状态" align="center" show-overflow-tooltip>

              <template slot-scope="scope">
                <div class="statusBtn">
                  <!-- <span v-if="scope.row.state == '3'" class="inProgress"
                  >学员执行中</span
                > -->
                  <span v-if="scope.row.examStatus == '0'" class="relwaseNo">未发布</span>
                  <span v-if="scope.row.examStatus == '1'" class="auditIng">审核中</span>
                  <span v-if="scope.row.examStatus == '2'" class="relwase">已发布</span>
                  <span v-if="scope.row.examStatus == '3'" class="auditNo">
                    <img src="../../../../../assets/images/icon-wrapper.png" alt="" />
                    未通过
                  </span>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <div class="contentTable-footer">
            <el-pagination style="margin-top: 3px" :current-page="paginationData.pageNo"
              layout="total, sizes, prev, pager, next, jumper" :total="paginationData.total"
              :page-size="paginationData.pageSize" :page-sizes="[15, 30, 50, 100]" @size-change="handleSizeChange"
              @current-change="handleCurrentChange"></el-pagination>
          </div>
        </div>
      </div>
      <div class="derwer_footer">
        <div class="derwer_footerSum"> 已选择 <span class="sumNamber">{{ tableSum }}</span> 个课程</div>
        <div>
          <el-button type="primary" plain @click="cancel">取消</el-button>
          <el-button type="primary" @click="submit">添加</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import moment from "moment";
import manualPperation from '../components/manualPperation.vue';
import voluntarily from '../components/voluntarily.vue'
export default {
  components: { manualPperation, voluntarily },
  props: {
    setingOpty: {
      type: String,
      default: "",
    },
    formInfoDeatil: {
      type: Object,
      default: {},
    },
    userList: {
      type: Array,
      default: [],
    },
    id: {
      type: String,
      default: "",
    }
  },
  data() {

    return {
      tableLoading:false,
      paginationData: {
        pageNo: 1,
        pageSize: 15,
        total: 0,
      },
      moment,
      noPassList: [
        {
          id: "1",
          label: "不补考",
        },
        {
          id: "2",
          label: "自动组卷补考",
        },
        {
          id: "3",
          label: "手动派发补考试卷",
        },
      ],
      formInlineEamian: { // 考试计划相关
        name: "",
        deptId: '',
        subjectId: "",
        examStatus: "",
        startTime: "",
        endTime: "",
      },
      timeLine: [], // 考试计划筛选
      subjectProps: {
        children: "childList",
        label: "name",
        value: "id",
        checkStrictly: true,
        emitPath: false,
      },
      deptProps: {
        children: "children",
        label: "teamName",
        value: "id",
        checkStrictly: true,
        emitPath: false,
      },
      deptList: [],//部门
      subjectList: [],//科目分类
      examStatusList: [
        {
          id: '0',
          label: '草稿'
        },
        {
          id: '1',
          label: '审核中'
        },
        {
          id: '3',
          label: '未通过'
        },
        // {
        //   id:'3',
        //   label:'未发布'
        // },
        {
          id: '2',
          label: '已发布'
        }
      ],
      formInfo: {
        name: "",
        subjectId: null,
        stuOrOrg: '',
        orgId: '',
        deptIds: "",
        timeLine: [],
        startTime: '',
        endTime: '',
        rejectDeal: '',
        duration: "", // 答题时长
        credentialId: "", //证书id
        disc: "", //描述
        passScore: '',//通过分数
      },
      noAutoList: [],
      certificateList: [], // 证书列表
      scoreInfo: {
        nums: 0,
        sumScore: 0
      },
      formInline: {
        questionCode: '',
        dutyDeptName: '',
        riskCode: '',
        questionDetailCode: '',
        createByDeptName: '',
        startTime: '',
        endTime: ''
      },
      tmId: '',
      timeLine: [],
      autoInfo: {
        questionsList: [
          {
            questionTypes: [
              {
                type: "1",
                num: '',
                score: '',
              },
              {
                type: "2",
                num: '',
                score: '',
              },
              {
                type: "3",
                num: '',
                score: '',
              },
            ],
            courseId: '',
            questionsNum: []
          },
        ],
      },
      hiddenLevelList: [],
      drawer: false,
      tableData: [], // 考试计划table
      multipleSelectionExmian: [],
      tableSum: '0',
      radio: 1,
      form: {
        name: '',
        radio: 1,
      },
      topiconFigList: [{
        name: '',
      }],
      options: [{
        value: '选项1',
        label: '黄金糕'
      }, {
        value: '选项2',
        label: '双皮奶'
      }, {
        value: '选项3',
        label: '蚵仔煎'
      }],
      formInfoDeatils: '',
    };
  },
  watch: {
    timeLine(val) {
      if (val.length > 0) {
        this.formInlineEamian.startTime = val[0];
        this.formInlineEamian.endTime = val[1];
      } else {
        this.formInlineEamian.startTime = "";
        this.formInlineEamian.endTime = "";
      }
    },
  },
  created() {
    console.log(this.setingOpty, 'this.setingOpty456');
    this.formInfoDeatils = this.formInfoDeatil;
    console.log(this.formInfoDeatils, 'this.formInfoDeatils');
    this.tmId = this.id
    this.init();
    this.getDataList(); // 列表
  },
  mounted() {
    this.getTrainDetail()
  },
  methods: {
    // ------------------------------ 关联考试计划 -------------------------------------------
    init() {
      // 获取科目分类
      let data = {
        pageNo: 1,
        pageSize: 999,
      };
      this.$api.ipsmFicationlList(data).then((res) => {
        if (res.code == 200) {
          this.subjectList = res.data.records;
        } else {
          this.$message.error(res.message);
        }
      });
      // 获取组织
      this.$api.getDeptListLaboratory({}).then((res) => {
        this.deptList = this.$tools.transData(
          res.data.list,
          "id",
          "parentId",
          "children"
        );
      });
      // 获取证书模板
      this.$api.certificateList({pageNo:1,pageSize:9999}).then((res) => {
        if (res.code == 200) {
          this.certificateList = res.data.records;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 获取父组件中培训详情
    getTrainDetail() {
      console.log(this.setingOpty, 'this.setingOpty123');
      this.$api.trainPlanDetal({ id: this.tmId }).then(res => {
        if (res.code == 200) {
          this.formInfo = { ...res.data.examParamVo }
        }
      })
    },
    // 查询考试计划列表
    getDataList() {
      this.tableLoading = true
      let params = {
        ...this.formInlineEamian,
        current: this.paginationData.pageNo,
        size: this.paginationData.pageSize
      }
      this.$api.getExamPlanList(params).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.list;
          this.paginationData.total = res.data.total
        } else {
          this.$message.error(res.msg);
        }
        this.tableLoading = false
      });

    },
    // 查询
    searchExamin() {
      this.paginationData.pageNo = 1;
      this.getDataList();
    },
    // 重置
    resetFormExamin() {
      this.formInlineEamian.name = "";
      this.formInlineEamian.deptId = "";
      this.formInlineEamian.subjectId = "";
      this.formInlineEamian.startTime = "";
      this.formInlineEamian.endTime = "";
      this.timeLine = [];
      this.paginationData.pageNo = 1;
      this.getDataList();
    },
    // 分页
    handleSizeChange(val) {
      this.paginationData.pageSize = val;
      this.getDataList();
    },
    handleCurrentChange(val) {
      this.paginationData.pageNo = val;
      this.getDataList();
    },
    // 添加考试计划
    addExaminatio() {
      this.drawer = !this.drawer
    },
    toggleSelection(rows) {
      if (rows) {
        rows.forEach(row => {
          this.$refs.multipleTable.toggleRowSelection(row);
        });
      } else {
        this.$refs.multipleTable.clearSelection();
      }
    },
    // 选中考试计划表格数据
    handleSelectionChangeExamin(val) {
      this.tableSum = val.length
      this.multipleSelectionExmian = []
      if (val.length > 1) {
        //移除上一次选中行数据
        val.shift();
        //修改选中图标为未选中状态
        this.$refs.userTable.clearSelection();
        //将当前选中行改为选中状态
        this.$refs.userTable.toggleRowSelection(val[0]);
      } else {
        this.multipleSelectionExmian = val;
      }
    },
    // 取消关闭弹窗
    cancel() {
      this.drawer = false
    },
    submitAuto(obj, scoreInfo, type) {
      console.log(scoreInfo, 'scoreInfo');
      let params = {
        ...this.formInfoDeatils,
        studentIds: this.formInfoDeatils.stuOrOrg == '0' ? this.userList.map(i => i.id).join(',') : '',
        deptIds: this.formInfoDeatils.deptIds,
        trainStatus: type,
      }
      console.log();
      if (typeof this.formInfoDeatils.orgId === 'object') {
        params.orgId = this.formInfoDeatils.orgId.join(',')
      }
      if (typeof this.formInfoDeatils.deptIds === 'object') {
        params.deptIds = this.formInfoDeatils.deptIds.join(',')
      }
      if (this.setingOpty == '1') { //自动组卷
        let examDto = {
          score: scoreInfo.sumScore,
          name: this.formInfo.name,
          disc: this.formInfo.disc,
          duration: this.formInfo.duration,
          rejectDeal: this.formInfo.rejectDeal,
          passScore: this.formInfo.passScore,
          credentialId: this.formInfo.credentialId,
          stuOrOrg: 0,
          studentIds: this.userList.map(i => i.id).join(','),
          examStatus: type,
        }
        if (this.setingOpty == '1') {
          params.startTime = this.formInfoDeatils.startTime
        }
        if (typeof this.formInfoDeatils.orgId === 'object') {
          params.orgId = this.formInfoDeatils.orgId.join(',')
        }
        let auto = obj.questionsList
        auto.forEach(i => delete i.questionsNum)
        // params.auto = obj.questionsList
        params.examDto = Object.assign(examDto, { auto })
      } else if (this.setingOpty == '2') { //手动组卷
        let examDto = {
          score: scoreInfo.sumScore,
          name: this.formInfo.name,
          disc: this.formInfo.disc,
          duration: this.formInfo.duration,
          rejectDeal: this.formInfo.rejectDeal,
          passScore: this.formInfo.passScore,
          credentialId: this.formInfo.credentialId,
          stuOrOrg: 0,
          studentIds: this.userList.map(i => i.id).join(','),
          examStatus: type,
        }

        var newList = JSON.parse(JSON.stringify(obj))
        newList.forEach(k => {
          k.options = JSON.stringify(k.options)
          if (k.courseList) {
            delete k.courseList
          }
        })

        let noAutoQuestions = newList
        if (this.setingOpty == '2') {
          params.startTime = this.formInfoDeatils.startTime
        }
        params.examDto = Object.assign(examDto, { noAutoQuestions })
      }
      if (this.formInfoDeatils.id) {
        this.$api.trainPlanEdit(params).then(res => {
          if (res.code == 200) {
            this.$message.success(res.msg)
            this.$router.go(-1)
          } else {
            this.$message.error(res.msg)
          }
        })
      } else {
        this.$api.trainPlanSave(params).then(res => {
          if (res.code == 200) {
            this.$message.success(res.msg)
            this.$router.go(-1)
          } else {
            this.$message.error(res.msg)
          }
        })
      }
    },
    // 提交
    submit() { },
    // 查看
    detail() { },
    // 删除
    deleteTeain() { },
    // 添加题目
    addTopicDetail() {
      this.topiconFigList.push({
        name: '',
      })
    },
    // 删除题目
    deleteTopicDetail() {
      if (this.topiconFigList.length === 1) {
        return false
      }
      this.topiconFigList.pop("")
    },
    // 查询
    search() { },
    // 重置
    resetForm() { },
  },
};
</script>

<style lang="scss" scoped>
.heard {
  height: 605px;
  overflow-y: auto;
}

.table_conter {
  height: 480px;
}

.course_nav {
  height: 64px;
  background: #FAF9FC;
  border-radius: 8px 8px 8px 8px;
  opacity: 1;
  display: flex;
  justify-content: space-between;
  line-height: 64px;
  padding: 0px 15px;
  font-size: 14px;
  margin: 15px 0px;
}

.course_title {
  margin-top: 8px;
  font-weight: 600;
  color: #333333;
  font-size: 16px;
  display: flex;
}

.courer_name {
  display: flex;
}

.operation {
  display: flex;
  color: #3562DB;
}

.titleImg {
  margin-top: 7px;
  margin-right: 10px;
}

.radioKs {
  margin-left: 20px;
  color: #666666;
}

.Iconx {
  margin-left: 20px;
}

.configuration {
  display: flex;
  width: 100%;
  height: 32px;
  line-height: 32px;
  justify-content: space-between;
  margin: 20px 0px 10px 0px;
}

.configTitle {
  font-weight: 600;
  color: #333333;
  font-size: 16px;
  margin-right: 20px;
}

.topic {
  width: 100%;
  height: 300px;
  background: #FAF9FC;
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
  padding: 10px;
  overflow-y: auto;
}

.topicList {
  width: 100%;
  height: 130px;
  background: #FFFFFF;
  border-radius: 8px 8px 8px 8px;
  opacity: 1;
  padding: 0px 0px 20px 0px;
  margin-bottom: 20px;
}

.topicTitle {
  display: flex;
  justify-content: space-between;
}

.deletex {
  margin: 20px 20px 0px 20px;
  font-weight: 400;
  color: #FF6461;
  font-size: 14px;
  cursor: pointer;
}

.remark {
  margin: 20px 0px 0px 20px;
  font-weight: 400;
  color: #CCCED3;
  font-size: 14px;
}

.btn {
  margin-top: 25 px;
  height: 20px !important;
}

::v-deep .el-form-item {
  display: inline-block;
  margin: 8px 0px;
}

::v-deep .el-form-item:nth-child(5) {
  display: inline;
  // width: 20%;

}

::v-deep .el-textarea .el-input__count {
  color: #909399;
  position: absolute;
  background: none;
  font-size: 12px;
  bottom: -7px;
  right: 10px;
}

::v-deep .el-input-group__append {
  padding: 0px 5px;
}

.statusBtn {
  font-size: 14px;
  display: flex;
  justify-content: center;

  .auditIng {
    width: 58px;
    height: 24px;
    background-color: #fff7e8;
    border-radius: 4px;
    color: #d25f00;
  }

  .auditNo {
    width: 78px;
    height: 24px;
    background-color: #ffece8;
    border-radius: 4px;
    color: #cb2634;

    img {
      vertical-align: middle;
    }
  }

  .relwase {
    width: 58px;
    height: 24px;
    background-color: #e8ffea;
    border-radius: 4px;
    color: #009a29;
  }

  .inProgress {
    width: 86px;
    height: 24px;
    background-color: #E6EFFC;
    border-radius: 4px;
    color: #2749BF;
  }

  .relwaseNo {
    width: 58px;
    height: 24px;
    background-color: #F2F4F9;
    border-radius: 4px;
    color: #86909C;
  }
}

.coursrDrawer {
  color: #333333;
  font-weight: 500;
  font-size: 18px;
}

::v-deep .el-drawer__header {
  height: 56px;
  line-height: 56px !important;
  padding-bottom: 20px;
  border-bottom: 1px solid #DCDFE6;
  box-shadow: -4px 0 4px 0 rgba(203, 205, 220, 0.24);
}

.deawer_heard {
  width: 100%;
  margin-bottom: 30px;
  display: flex;
}

.derwer_footer {
  width: 100%;
  height: 56px;
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0px 0px 12px 3px rgba(0, 0, 0, 0.1);
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
  position: absolute;
  bottom: 0px;
  left: 0px;
  display: flex;
  justify-content: space-between;
  line-height: 56px;
  padding: 0px 20px;
}

.derwer_footerSum {
  font-size: 14px;
  font-weight: 400;
  color: #7F848C;

  .sumNamber {
    font-size: 16px;
    font-weight: 500;
    color: #333333;
  }
}

::v-deep .el-textarea .el-input__count {
  bottom: 1px !important;
}
</style>