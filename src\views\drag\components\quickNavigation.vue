<!--
 * @Author: hedd
 * @Date: 2023-03-07 15:27:01
 * @LastEditTime: 2024-09-11 14:05:10
 * @FilePath: \ihcrs_pc\src\views\drag\components\quickNavigation.vue
 * @Description:
 *
-->
<template>
  <!-- <ContentCard :title="item.componentTitle" :scrollbarHover="true" :cstyle="{ height: '100%' }" class="drag_class"  :hasMoreOper="['more', 'edit']" @more-oper-event="(val) => $emit('all-more-Oper', val, 'quickNavigation')">
    <div slot="content" class="quick-content">
      <div v-for="(item, index) in navigationList" :key="index" class="apply-box" @click="navigationTo(item)">
        <div class="bg-img">
          <img class="apply-defaultIcon" :src="item.url" :alt="item.name" :onerror="defaultImg">
          <img class="apply-activeIcon" :src="item.hoverImg" :alt="item.name" :onerror="defaultImg">
        </div>
        <div class="bg-name">{{item.name}}</div>
      </div>
    </div>
  </ContentCard> -->
  <div class="quick-content">
    <div v-for="(item, index) in navigationList" :key="index" class="apply-box" @click="navigationTo(item)">
      <!-- <img class="bg-img" :src="item.url" :style="{'--hover-img': 'url(' + item.hoverImg +')'}" :onerror="defaultImg" alt=""> -->
      <div class="bg-img">
        <img class="apply-defaultIcon" :src="item.url" :alt="item.name" :onerror="defaultImg" />
        <img class="apply-activeIcon" :src="item.hoverImg" :alt="item.name" :onerror="defaultImg" />
      </div>
      <div class="bg-name">{{ item.name }}</div>
    </div>
  </div>
</template>
<script>
import defaultImgIm from '@/assets/images/staging/space-manage.png'
import { childAppWhite } from '@/util/dict.js'
export default {
  name: 'quickNavigation',
  props: {
    item: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      childAppWhite,
      defaultImg: 'this.src="' + defaultImgIm + '"',
      navigationList: []
    }
  },
  mounted() {
    this.getMyApplyList()
  },
  methods: {
    // 地址错误时，设置默认图片
    // defaultImg(event) {
    //   console.log(event)
    //   let img = event.srcElement
    //   img.src = require('@/assets/images/staging/space-manage.png')
    //   img.onerror = null // 防止闪图
    // },
    // 跳转
    navigationTo(item) {
      // 子模块
      if (item.type == 2) {
        if (this.$auth(item.path)) {
          let pathArr = item.path.split('/')
          if (this.childAppWhite.includes(pathArr[2])) {
            this.$store.commit('childAppsData/setChildAppInfo', { parentName: `/${pathArr[1]}`, currentPath: pathArr[2] })
          }
          this.$router.push(item.path)
        } else {
          this.$message({ message: '无操作权限，请于管理员联系。', type: 'warning' })
        }
        // 子应用或第三方系统
      } else {
        window.open(item.path)
      }
    },
    // 获取我的应用列表
    getMyApplyList() {
      const userId = this.$store.state.user.userInfo.user.staffId
      this.$api.GetMyApplyList({ userId: userId, shortName: '' }).then((res) => {
        if (res.code == 200) {
          this.navigationList = res.data.slice(0, 4).map((item) => {
            const icon = JSON.parse(item.icon)
            return {
              url: this.$tools.imgUrlTranslation(icon.defaultIcon),
              hoverImg: this.$tools.imgUrlTranslation(icon.activeIcon),
              name: item.shortName,
              path: item.path,
              type: item.type
            }
          })
          console.log(res.data)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
// $hover-img: var(--hover-img);
.quick-content {
  // height: 100%;
  display: flex;
  flex-wrap: wrap;
  .apply-box {
    width: 120px;
    height: 130px;
    background: #faf9fc;
    border-radius: 4px;
    margin: 5px;
    display: flex;
    flex-direction: column;
    text-align: center;
    justify-content: space-evenly;
    align-items: center;
    cursor: pointer;
    .bg-img {
      width: 46px;
      height: 46px;
      position: relative;
      img {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
      }
      // background-repeat: no-repeat;
      // background-size: 100% 100%;
    }
    .bg-name {
      font-size: 14px;
      font-family: 'PingFang SC-Medium', 'PingFang SC';
      font-weight: 500;
      color: #121f3e;
    }
    .apply-defaultIcon {
      transition: opacity 0.3s;
      opacity: 1;
    }
    .apply-activeIcon {
      transition: opacity 0.3s;
      opacity: 0;
    }
  }
  .apply-box:hover {
    background: #fff;
    box-shadow: 0 4px 12px 0 rgb(18 31 62 / 12%);
    .apply-defaultIcon {
      opacity: 0;
    }
    .apply-activeIcon {
      opacity: 1;
    }
  }
}
</style>
