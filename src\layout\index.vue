<template>
  <div class="layout">
    <div id="app-main">
      <transition name="header">
        <header v-if="$store.state.settings.mode == 'pc' && $store.state.settings.showHeader">
          <div class="header-container">
            <div class="main">
              <Logo />
              <!-- 当头部导航大于 1 个的时候才会显示 -->
              <div v-if="$store.state.menu.routes.length > 0" class="nav">
                <template v-for="(item, index) in $store.state.menu.routes">
                  <div
                    v-if="item.children && item.children.length !== 0"
                    :key="index"
                    :class="{
                      item: true,
                      active: index == $store.state.menu.headerActived
                    }"
                    @click="switchMenu(index)"
                  >
                    <svg-icon v-if="item.meta.icon" :name="item.meta.icon" class="icon" />
                    <span v-if="item.meta.title">{{ item.meta.title }}</span>
                  </div>
                </template>
              </div>
            </div>
            <UserMenu />
          </div>
        </header>
      </transition>
      <div class="wrapper">
        <div
          :class="{
            'sidebar-container': true,
            show: $store.state.settings.mode == 'mobile' && !$store.state.settings.sidebarCollapse
          }"
        >
          <transition name="main-sidebar">
            <div
              v-if="
                (!$store.state.settings.showHeader || $store.state.settings.mode == 'mobile') &&
                ($store.state.menu.routes.length > 0 || $store.state.settings.alwaysShowMainSidebar)
              "
              class="main-sidebar-container"
            >
              <Logo :show-title="false" class="sidebar-logo" />
              <div v-if="$store.state.settings.sidebarCollapse">
                <el-menu
                  ref="menu"
                  popper-append-to-body
                  unique-opened
                  :default-active="$route.meta.activeMenu || $route.path"
                  collapse
                  class="el-menu-nav"
                  :class="{
                    item: true
                  }"
                  @select="handleMenuSelect"
                >
                  <SidebarItem v-for="(route, index) in $store.state.menu.routes" :key="index" :item="{ ...route, index, parentRouterPath: $route.path }" />
                </el-menu>
              </div>
              <div v-else class="nav">
                <template v-for="(item, index) in $store.state.menu.routes">
                  <div
                    v-if="item.children && item.children.length !== 0"
                    :key="index"
                    :class="{
                      item: true,
                      active: index == $store.state.menu.headerActived
                    }"
                    :title="item.meta.title"
                    @click="switchMenu(index)"
                  >
                    <template v-if="item.meta.icon?.includes('http') || item.meta.activeIcon?.includes('http')">
                      <img v-if="item.meta.icon && index != $store.state.menu.headerActived" class="icon" :src="item.meta.icon" alt="" />
                      <span v-if="index != $store.state.menu.headerActived">{{ item.meta.title }}</span>
                      <img v-else :name="item.meta.activeIcon" class="icon" :src="item.meta.activeIcon" alt="" />
                    </template>
                    <template v-else>
                      <svg-icon v-if="item.meta.icon && index != $store.state.menu.headerActived" :name="item.meta.icon" class="icon" />
                      <span v-if="index != $store.state.menu.headerActived">{{ item.meta.title }}</span>
                      <svg-icon v-else :name="item.meta.activeIcon" class="icon" style="font-size: 30px" />
                    </template>
                  </div>
                </template>
              </div>
            </div>
          </transition>
          <div
            v-if="!$store.state.settings.sidebarCollapse"
            :class="{
              'sub-sidebar-container': true,
              'is-collapse': $store.state.settings.mode == 'pc' && $store.state.settings.sidebarCollapse
            }"
            @scroll="onSidebarScroll"
          >
            <Logo
              class="sidebar-logo"
              :show-logo="$store.state.menu.routes.length < 1 && !$store.state.settings.alwaysShowMainSidebar"
              :class="{
                // 'sidebar-logo-bg': $store.state.menu.routes.length < 1 && !$store.state.settings.alwaysShowMainSidebar,
                shadow: sidebarScrollTop
              }"
            />
            <el-menu
              ref="menu"
              unique-opened
              :default-active="$route.meta.activeMenu || $route.path"
              :collapse="$store.state.settings.mode == 'pc' && $store.state.settings.sidebarCollapse"
              :collapse-transition="false"
              :class="{
                'is-collapse-without-logo': $store.state.menu.routes.length > 0 && $store.state.settings.mode == 'pc' && $store.state.settings.sidebarCollapse
              }"
              @select="handleMenuSelect"
            >
              <transition-group name="sub-sidebar">
                <template v-for="route in $store.getters['menu/sidebarRoutes']">
                  <SidebarItem
                    v-if="route.meta.sidebar !== false && route.meta.arrangement != 'column'"
                    :key="route.path"
                    :item="{ ...route, parentRouterPath: $route.path }"
                    :base-path="route.path"
                  />
                </template>
                <template v-for="route in $store.getters['menu/sidebarRoutes']">
                  <div
                    v-if="route.meta.arrangement == 'column' && !$store.state.settings.sidebarCollapse"
                    :key="route.path + '1'"
                    class="column-row"
                    @click="handleOpen(route.path)"
                  >
                    <el-col
                      :md="8"
                      :class="{
                        'is-active': colActiveMenu == route.path
                      }"
                    >
                      <template v-if="route.meta.icon?.includes('http') || route.meta.activeIcon?.includes('http')">
                        <img
                          v-if="iconName(colActiveMenu == route.path, route.meta.icon, route.meta.activeIcon)"
                          class="icon"
                          :src="iconName(colActiveMenu == route.path, route.meta.icon, route.meta.activeIcon)"
                          alt=""
                        />
                      </template>
                      <template v-else>
                        <svg-icon
                          v-if="iconName(colActiveMenu == route.path, route.meta.icon, route.meta.activeIcon)"
                          :name="iconName(colActiveMenu == route.path, route.meta.icon, route.meta.activeIcon)"
                          class="icon"
                        />
                      </template>
                      <span>{{ generateI18nTitle(route.meta.i18n, route.meta.title) }}======</span>
                    </el-col>
                  </div>
                </template>
                <template v-for="route in $store.getters['menu/sidebarRoutes']">
                  <SidebarItem
                    v-if="route.meta.sidebar !== false && route.meta.arrangement == 'column'"
                    :key="route.path + '2'"
                    :item="{ ...route, parentRouterPath: $route.path }"
                    :base-path="route.path"
                  />
                </template>
              </transition-group>
            </el-menu>
          </div>
        </div>
        <div
          :class="{
            'sidebar-mask': true,
            show: $store.state.settings.mode == 'mobile' && !$store.state.settings.sidebarCollapse
          }"
          @click="$store.commit('settings/toggleSidebarCollapse')"
        />
        <div
          :class="{
            'warning-mask': true,
            show: showWarnMask
          }"
        ></div>
        <div class="main-container" :style="{ 'padding-bottom': $route.meta.paddingBottom }">
          <Tabbar v-if="$store.state.settings.enableTabbar && !$store.state.settings.switchTabbarAndTopbar" />
          <Topbar :class="{ shadow: scrollTop }" />
          <Tabbar v-if="$store.state.settings.enableTabbar && $store.state.settings.switchTabbarAndTopbar" />
          <div id="main_content_container" ref="contentBox" class="main">
            <!-- <scaleScreen :width="parentBox.width" :height="parentBox.height"> -->
            <transition name="main" mode="out-in">
              <keep-alive v-if="isRouterAlive" :include="$store.state.keepAlive.list">
                <RouterView :key="$route.path" />
              </keep-alive>
            </transition>
            <!-- </scaleScreen> -->
          </div>
        </div>
      </div>
      <el-backtop :right="20" :bottom="20" title="回到顶部" />
    </div>
    <Search />
    <ThemeSetting />
  </div>
</template>
<script>
import Logo from './components/Logo'
import UserMenu from './components/UserMenu'
import SidebarItem from './components/SidebarItem'
import Tabbar from './components/Tabbar'
import Topbar from './components/Topbar'
import Search from './components/Search'
import ThemeSetting from './components/ThemeSetting'
import { deepClone } from '@/util'
import { mapGetters } from 'vuex'
export default {
  // eslint-disable-next-line vue/multi-word-component-names
  name: 'Layout',
  components: {
    Logo,
    UserMenu,
    SidebarItem,
    Tabbar,
    Topbar,
    Search,
    ThemeSetting
  },
  inject: ['generateI18nTitle'],
  provide() {
    return {
      reload: this.reload
    }
  },
  data() {
    return {
      alarmId: '',
      messageList: [],
      parentBox: {
        width: 0,
        height: 0
      },
      showWarnMask: false, // 是否打开报警闪烁蒙层
      isRouterAlive: true,
      routePath: '',
      colActiveMenu: '',
      sidebarScrollTop: 0,
      scrollTop: 0,
      pageJumpArr: ['/drag/index'] // 不显示二级侧边栏的地址集合
    }
  },
  computed: {
    ...mapGetters({
      socketMsgs: 'socket/socketMsgs',
      isLogin: 'user/isLogin'
    })
  },
  watch: {
    $route: 'routeChange',
    '$store.state.settings.sidebarCollapse'(val) {
      if (this.$store.state.settings.mode == 'mobile') {
        if (!val) {
          document.querySelector('body').classList.add('body-hidden')
        } else {
          document.querySelector('body').classList.remove('body-hidden')
        }
      }
    },
    socketMsgs(data) {
      const jsonData = JSON.parse(data)
      const alarmData = jsonData.data
      if (alarmData?.webAlarmResponseWay?.includes('1')) {
        this.setWarnMessage(alarmData)
      }
      // this.playList.push({
      //   playCount: alarmDAta.data.broadcastNum,
      //   playText: alarmDAta.data.broadcastMsg
      // })
      // setTimeout(() => {
      //   this.playNextAudio()
      // }, 300)
    },
    // 退出登录清空报警提示
    isLogin(flag) {
      if (!flag && this.messageList.length) {
        this.$message.closeAll()
        this.messageList = []
        this.showWarnMask = false
      }
    }
  },
  mounted() {
    let that = this
    window.getGlobalAlaramDetail = that.getGlobalAlaramDetail
    window.closeGlobalAlarmMessage = that.closeGlobalAlarmMessage
    this.parentBox.width = this.$refs.contentBox.offsetWidth
    this.parentBox.height = this.$refs.contentBox.offsetHeight
    this.initHotkey()
    const menuType = this.$store.state.menu.routes[this.$store.state.menu.headerActived]
    this.getRentHousingCount(menuType)
    window.addEventListener('scroll', this.onScroll)
  },
  destroyed() {
    window.removeEventListener('scroll', this.onScroll)
  },
  methods: {
    setWarnMessage(data) {
      this.showWarnMask = true
      let that = this
      const messageLength = this.messageList.length
      const message = this.$message({
        dangerouslyUseHTMLString: true,
        duration: 0,
        showClose: true,
        type: 'error',
        customClass: 'alarm-description-message',
        message: `
        <div class="alarm-description-box">
          <div class="box-title">${data.webBroadcastMsg}</div>
          <div class="box-btn-group">
            <span onclick="getGlobalAlaramDetail('${data.alarmData.alarmId}', '${messageLength}')">立即查看</span>
            <span class="btn-line">|</span>
            <span onclick="closeGlobalAlarmMessage('${messageLength}')">关闭提示</span>
          </div>
        </div>`,
        onClose() {
          that.messageList[messageLength] = null
          const emptyMsg = that.messageList.some((e) => e)
          // 所有message被清空后，保存message的数组重置，报警闪烁关闭
          if (!emptyMsg) {
            that.messageList = []
            that.showWarnMask = false
          }
        }
      })
      this.messageList.push(message)
    },
    // 查看指定报警详情
    getGlobalAlaramDetail(alarmId, index) {
      this.closeGlobalAlarmMessage(index)
      this.alarmId = alarmId
      this.$nextTick(() => {
        this.$router.push({
          path: '/allAlarm/alarmDetail',
          query: {
            tabType: 1,
            alarmId: alarmId
          }
        })
      })
    },
    // 关闭指定报警message
    closeGlobalAlarmMessage(index) {
      const message = this.messageList[index]
      message.close()
    },
    handleOpen(key) {
      this.$refs.menu.open(key)
      this.colActiveMenu = key
    },
    handleMenuSelect(key, keyPath) {
      // 根据选中菜单使column父菜单高亮
      const nowRouteList = this.$store.getters['menu/sidebarRoutes']
      // 如果菜单为一级无子集 关闭其他展开的菜单
      if (keyPath.length == 1) {
        nowRouteList.forEach((item) => {
          keyPath != item.path && this.$refs.menu.close(item.path)
        })
      }
      const colRouteList = nowRouteList.filter((item) => {
        return item.meta.arrangement == 'column'
      })
      const pathList = colRouteList.map((item) => {
        return item.path
      })
      if (!pathList.includes(keyPath[0])) {
        this.colActiveMenu = ''
      } else {
        this.colActiveMenu = keyPath[0]
      }
    },
    iconName(isActive, icon, activeIcon) {
      let name = ''
      if ((!isActive && icon) || (isActive && !activeIcon)) {
        name = icon
      } else if (isActive && activeIcon) {
        name = activeIcon
      }
      return name
    },
    reload(type = 1) {
      if (this.$store.state.settings.enableTabbar) {
        let path = this.$route.meta.activeMenu || this.$route.fullPath
        let name
        this.$store.state.tabbar.list.map((v) => {
          if (v.path == path) {
            name = v.name
          }
        })
        if (name) {
          this.$store.commit('keepAlive/remove', name)
          this.$router.push({
            name: 'reload'
          })
          this.$nextTick(() => {
            this.$store.commit('keepAlive/add', name)
          })
        }
      } else {
        if (type == 1) {
          this.isRouterAlive = false
          this.$nextTick(() => (this.isRouterAlive = true))
        } else {
          this.$router.push({
            name: 'reload'
          })
        }
      }
    },
    routeChange(newVal, oldVal) {
      if (newVal.name == oldVal.name) {
        // this.reload()
      } else {
        // 路由跳转回显选中效果
        const nowRouteList = this.$store.getters['menu/sidebarRoutes']
        const colRouteList = nowRouteList.filter((item) => {
          return item.meta.arrangement == 'column'
        })
        const pathList = colRouteList.map((item) => {
          return item.path
        })
        if (pathList.includes(newVal.matched[0].path) && !this.colActiveMenu) {
          this.colActiveMenu = newVal.matched[0].path
        }
      }
    },
    initHotkey() {
      this.$hotkeys('alt+s', (e) => {
        if (this.$store.state.settings.enableNavSearch) {
          e.preventDefault()
          this.$eventBus.$emit('global-search-toggle')
        }
      })
      this.$hotkeys('f5', (e) => {
        if (this.$store.state.settings.enablePageReload) {
          e.preventDefault()
          this.reload(this.$store.state.settings.enableTabbar ? 1 : 2)
        }
      })
    },
    onSidebarScroll(e) {
      this.sidebarScrollTop = e.target.scrollTop
    },
    onScroll() {
      this.scrollTop = document.documentElement.scrollTop || document.body.scrollTop
    },
    switchMenu(index) {
      const menuType = this.$store.state.menu.routes[index]
      this.getRentHousingCount(menuType, index)
      // 验证是否有双预防权限
      if (menuType.meta.type && menuType.meta.type == 'ipsm') {
        if (!sessionStorage.getItem('LOGINDATA')) {
          this.$api
            .getIpsmLoginInfo({
              userId: this.$store.state.user.userInfo.user.staffId,
              platform: 1
            })
            .then((res) => {
              if (res.code == '200') {
                sessionStorage.setItem('LOGINDATA', JSON.stringify(res.data))
                this.$store.commit('menu/switchHeaderActived', index)
              } else {
                this.$message({
                  type: 'error',
                  message: `${res.message}` || '暂无权限，请联系管理员'
                })
              }
            })
        } else {
          this.$store.commit('menu/switchHeaderActived', index)
        }
      } else {
        this.$store.commit('menu/switchHeaderActived', index)
      }
      // 跳转缩放侧边栏
      if (menuType.meta.pageJump) {
        this.$store.commit('settings/toggleSidebarCollapse', true)
      }
      if (this.$store.state.settings.switchSidebarAndPageJump || menuType.meta.pageJump) {
        this.$router.push(this.$store.getters['menu/sidebarRoutes'][0].path)
      }
    },
    // 公租房 审批管理 添加菜单角标
    getRentHousingCount(menuType, index = this.$store.state.menu.headerActived) {
      if (menuType.meta.menuAuth === '/rentalHousing') {
        let oldRoutes = deepClone(this.$store.state.menu.routes)
        const route = oldRoutes[index].children.find((route) => route.name === 'rentalHousingApproveManage')
        if (route) {
          this.$api.rentalHousingApi.getApproveCount({ queryType: '0', userId: this.$store.state.user.userInfo.user.staffId }).then((res) => {
            if (res.code == 200) {
              route.meta.badge = res.data.todo ? Number(res.data.todo) : 0
              this.$store.commit('menu/setRoutes', oldRoutes)
            }
          })
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
@use 'sass:color';
[data-layout='adaption'] {
  #app-main {
    width: 100%;
  }
}
[data-layout='adaption-min-width'] {
  #app-main {
    min-width: $g-app-width;
  }
}
[data-layout='center'] {
  #app-main {
    width: $g-app-width;
  }
}
[data-layout='center-max-width'] {
  #app-main {
    width: $g-app-width;
    max-width: 100%;
  }
}
// 侧边栏未折叠
[data-sidebar-no-collapse] {
  .sidebar-container {
    width: calc(#{$g-main-sidebar-width} + #{$g-sub-sidebar-width});
  }
  .main-container {
    margin-left: calc(#{$g-main-sidebar-width} + #{$g-sub-sidebar-width});
  }
  // 没有主侧边栏
  &[data-no-main-sidebar] {
    .sidebar-container {
      width: $g-sub-sidebar-width;
    }
    .main-container {
      margin-left: $g-sub-sidebar-width;
    }
  }
}
// 侧边栏折叠
[data-sidebar-collapse] {
  .sidebar-container {
    width: calc(#{$g-main-sidebar-width} + 0px);
    // width: calc(#{$g-main-sidebar-width} + 64px);
  }
  .main-container {
    margin-left: calc(#{$g-main-sidebar-width} + 0px);
    // margin-left: calc(#{$g-main-sidebar-width} + 64px);
  }
  // 没有主侧边栏
  &[data-no-main-sidebar] {
    .sidebar-container {
      width: 64px;
    }
    .main-container {
      margin-left: 64px;
    }
  }
}
[data-mode='mobile'] {
  #app-main {
    width: 100%;
    min-width: unset;
    max-width: unset;
  }
  .sidebar-container {
    width: calc(#{$g-main-sidebar-width} + #{$g-sub-sidebar-width});
    transform: translateX(-#{$g-main-sidebar-width}) translateX(-#{$g-sub-sidebar-width});
    &.show {
      transform: translateX(0);
    }
  }
  .main-container {
    margin-left: 0;
  }
  &[data-no-main-sidebar] {
    .sidebar-container {
      width: calc(#{$g-main-sidebar-width} + #{$g-sub-sidebar-width});
      transform: translateX(-#{$g-main-sidebar-width}) translateX(-#{$g-sub-sidebar-width});
      &.show {
        transform: translateX(0);
      }
    }
    .main-container {
      margin-left: 0;
    }
  }
}
.layout {
  height: 100%;
}
#app-main {
  height: 100%;
  margin: 0 auto;
  transition: all 0.2s;
}
header {
  position: fixed;
  z-index: 1000;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  padding: 0 20px;
  height: $g-header-height;
  @include themeify {
    color: themed('g-header-color');
    background-color: themed('g-header-bg');
  }
  .header-container {
    width: $g-header-width;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .main {
      display: flex;
      align-items: center;
    }
  }
  @media screen and (max-width: $g-header-width) {
    .header-container {
      width: 100%;
    }
  }
  ::v-deep .title {
    position: relative;
    width: inherit;
    height: inherit;
    padding: inherit;
    background-color: inherit;
    .logo {
      width: 50px;
      height: 50px;
    }
    span {
      font-size: 24px;
      letter-spacing: 1px;
      @include themeify {
        color: themed('g-header-color');
      }
    }
  }
  .nav {
    display: flex;
    margin-left: 50px;
    .item {
      margin: 0 10px;
      padding: 10px;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.3s;
      @include themeify {
        background-color: themed('g-header-bg');
        color: themed('g-header-menu-color');
      }
      &:hover {
        @include themeify {
          color: themed('g-header-menu-hover-color');
          background-color: themed('g-header-menu-hover-bg');
        }
      }
      &.active {
        @include themeify {
          color: themed('g-header-menu-active-color');
          background-color: themed('g-header-menu-active-bg');
        }
      }
      .icon {
        font-size: 20px;
        vertical-align: middle;
        & + span {
          margin-left: 5px;
          vertical-align: middle;
        }
      }
    }
  }
  ::v-deep .user {
    padding: 0;
    .tools [class^='ri-'] {
      @include themeify {
        color: themed('g-header-color');
      }
    }
    .user-container {
      font-size: 16px;
      @include themeify {
        color: themed('g-header-color');
      }
    }
  }
}
.wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  .sidebar-container {
    position: fixed;
    z-index: 1010;
    top: 0;
    bottom: 0;
    display: flex;
    transition: transform 0.3s;
    transform: transition3d(0, 0, 0);
    @include themeify {
      box-shadow: -1px 0 0 0 color.adjust(themed('g-main-bg'), $lightness: -10%);
    }
  }
  .sidebar-mask {
    position: fixed;
    z-index: 1000;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba($color: #000, $alpha: 50%);
    backdrop-filter: blur(2px);
    transition: all 0.2s;
    transform: translateZ(0);
    opacity: 0;
    visibility: hidden;
    &.show {
      opacity: 1;
      visibility: visible;
    }
  }
  .warning-mask {
    position: fixed;
    z-index: 10000;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    visibility: hidden;
    animation: none;
    &.show {
      visibility: visible;
      animation: changeBoxShadow 1.5s infinite;
    }
  }
  @keyframes changeBoxShadow {
    0% {
      box-shadow: 0 0 0 0px rgba(255, 0, 0, 0) inset;
    }
    10% {
      box-shadow: 0 0 10px 1px #ff0000 inset;
    }
    20% {
      box-shadow: 0 0 20px 2px #ff0000 inset;
    }
    30% {
      box-shadow: 0 0 30px 3px #ff0000 inset;
    }
    40% {
      box-shadow: 0 0 40px 4px #ff0000 inset;
    }
    50% {
      box-shadow: 0 0 50px 5px #ff0000 inset;
    }
    60% {
      box-shadow: 0 0 40px 4px #ff0000 inset;
    }
    70% {
      box-shadow: 0 0 30px 3px #ff0000 inset;
    }
    80% {
      box-shadow: 0 0 20px 2px #ff0000 inset;
    }
    90% {
      box-shadow: 0 0 10px 1px #ff0000 inset;
    }
    100% {
      box-shadow: 0 0 0px 0px rgba(255, 0, 0, 0) inset;
    }
  }
  .main-sidebar-container,
  .sub-sidebar-container {
    overflow-x: hidden;
    overflow-y: auto;
    overscroll-behavior: contain;
    // firefox隐藏滚动条
    scrollbar-width: none;
    // chrome隐藏滚动条
    &::-webkit-scrollbar {
      display: none;
    }
  }
  .main-sidebar-container {
    position: relative;
    z-index: 1;
    width: $g-main-sidebar-width;
    @include themeify {
      color: themed('g-main-sidebar-menu-color');
      background-color: themed('g-main-sidebar-bg');
    }
    .sidebar-logo {
      transition: 0.3s;
      @include themeify {
        background-color: themed('g-main-sidebar-bg');
      }
    }
    .nav {
      width: inherit;
      padding-top: $g-sidebar-logo-height;
      .item {
        display: flex;
        flex-direction: column;
        justify-content: center;
        text-align: center;
        margin-top: 11px;
        height: 60px;
        padding: 0 5px;
        cursor: pointer;
        transition: color 0.3s, background-color 0.3s;
        &:hover {
          @include themeify {
            color: themed('g-main-sidebar-menu-hover-color');
            background-color: themed('g-main-sidebar-menu-hover-bg');
          }
        }
        &.active {
          @include themeify {
            color: themed('g-main-sidebar-menu-active-color');
            background-color: themed('g-main-sidebar-menu-active-bg');
          }
        }
        .icon {
          margin: 0 auto;
          font-size: 20px;
        }
        span {
          text-align: center;
          padding-top: 3px;
          font-size: 14px;
          @include text-overflow;
        }
      }
    }
    .el-menu-nav {
      padding-top: $g-sidebar-logo-height;
      border-right: none;
      width: initial;
      background-color: var(--g-main-sidebar-bg);
      transition: background-color 0.3s;
      .sidebar-item {
        margin-top: 11px;
        transition: all 0.3s;
      }
      ::v-deep(.el-submenu.is-active) {
        .el-submenu__title {
          @include themeify {
            color: themed('g-main-sidebar-menu-active-color') !important;
            background-color: themed('g-main-sidebar-menu-active-bg') !important;
          }
        }
      }
      ::v-deep(.el-submenu__title) {
        padding: 0 !important;
        width: 100%;
        height: initial;
        line-height: inherit;
        &:hover {
          background-color: initial !important;
        }
        .item {
          display: flex;
          flex-direction: column;
          justify-content: center;
          text-align: center;
          height: 60px;
          width: $g-main-sidebar-width;
          padding: 0 5px;
          cursor: pointer;
          transition: color 0.3s, background-color 0.3s;
          &:hover {
            @include themeify {
              color: themed('g-main-sidebar-menu-hover-color');
              background-color: themed('g-main-sidebar-menu-hover-bg');
            }
          }
          &.active {
            @include themeify {
              color: themed('g-main-sidebar-menu-active-color');
              background-color: themed('g-main-sidebar-menu-active-bg');
            }
          }
        }
        .icon {
          margin: 0 auto;
          font-size: 20px;
          width: auto;
        }
        span {
          text-align: center;
          font-size: 14px;
          margin-top: 3px;
          color: #ccc;
          @include text-overflow;
        }
        .el-submenu__icon-arrow {
          display: none;
        }
      }
    }
  }
  .sub-sidebar-container {
    width: $g-sub-sidebar-width;
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    transition: 0.3s;
    @include themeify {
      background-color: #fff;
      // background-color: themed('g-sub-sidebar-bg');
      box-shadow: 10px 0 10px -10px color.adjust(themed('g-sub-sidebar-bg'), $lightness: -20%);
    }
    &.is-collapse {
      width: 64px;
      .sidebar-logo {
        &:not(.sidebar-logo-bg) {
          display: none;
        }
        ::v-deep span {
          display: none;
        }
      }
    }
    .sidebar-logo {
      transition: box-shadow 0.2s, background-color 0.3s, color 0.3s;
      @include themeify {
        background-color: #fff;
        border-bottom: 1px solid #dcdfe6;
      }
      &:not(.sidebar-logo-bg) {
        ::v-deep span {
          @include themeify {
            color: themed('g-sub-sidebar-menu-color');
          }
        }
      }
      &.sidebar-logo-bg {
        @include themeify {
          background-color: themed('g-main-sidebar-bg');
        }
      }
      &.shadow {
        @include themeify {
          box-shadow: 0 10px 10px -10px color.adjust(themed('g-sub-sidebar-bg'), $lightness: -20%);
        }
      }
    }
    .el-menu {
      border-right: 0;
      padding-top: $g-sidebar-logo-height;
      transition: border-color 0.3s, background-color 0.3s, color 0.3s;
      @include themeify {
        background-color: #fff;
        // background-color: themed('g-sub-sidebar-bg');
      }
      &:not(.el-menu--collapse) {
        width: inherit;
      }
      &.is-collapse-without-logo {
        padding-top: 0;
      }
      &.el-menu--collapse {
        ::v-deep .icon {
          margin-right: 0;
        }
        ::v-deep .el-menu-item,
        ::v-deep .el-submenu__title {
          span {
            display: none;
          }
          i {
            right: 7px;
            margin-top: -5px;
          }
        }
      }
    }
  }
  .main-sidebar-container + .sub-sidebar-container {
    left: $g-main-sidebar-width;
  }
  .main-container {
    display: flex;
    flex-direction: column;
    min-height: 100%;
    height: 100%;
    transition: margin-left 0.3s;
    @include themeify {
      background-color: themed('g-main-bg');
      box-shadow: 1px 0 0 0 color.adjust(themed('g-main-bg'), $lightness: -10%);
    }
    .tabbar-container + .topbar-container {
      top: $g-tabbar-height;
      z-index: 998;
    }
    .topbar-container + .tabbar-container {
      top: $g-topbar-height;
    }
    .main {
      height: 100%;
      flex: auto;
      position: relative;
      padding: $g-topbar-height 0 0;
      overflow: hidden;
      transition: 0.3s;
    }
    .topbar-container + .main {
      padding: calc(#{$g-topbar-height}) 0 0;
    }
    .tabbar-container + .topbar-container + .main,
    .topbar-container + .tabbar-container + .main {
      padding: calc(#{$g-tabbar-height} + #{$g-topbar-height}) 0 0;
    }
  }
}
header + .wrapper {
  padding-top: $g-header-height;
  .sidebar-container {
    top: $g-header-height;
    .sidebar-logo {
      display: none;
    }
    .el-menu {
      padding-top: 0;
    }
  }
  .main-container {
    .tabbar-container {
      top: $g-header-height;
    }
    .topbar-container {
      top: $g-header-height;
      ::v-deep .user {
        display: none;
      }
    }
    .tabbar-container + .topbar-container {
      top: calc(#{$g-header-height} + #{$g-tabbar-height});
    }
    .topbar-container + .tabbar-container {
      top: calc(#{$g-header-height} + #{$g-topbar-height});
    }
  }
}
// 头部动画
.header-enter-active {
  transition: 0.2s;
}
.header-enter {
  transform: translateY(-#{$g-header-height});
}
// 主侧边栏动画
.main-sidebar-enter-active {
  transition: 0.3s;
}
.main-sidebar-enter {
  transform: translateX(-#{$g-main-sidebar-width});
}
// 次侧边栏动画
.sub-sidebar-enter-active {
  transition: 0.3s;
}
.sub-sidebar-enter,
.sub-sidebar-leave-active {
  opacity: 0;
  transform: translateY(30px) skewY(10deg);
}
.sub-sidebar-leave-active {
  position: absolute;
}
// 主内容区动画
.main-enter-active {
  transition: 0.2s;
}
.main-leave-active {
  transition: 0.15s;
}
.main-enter {
  opacity: 0;
  margin-left: -20px;
}
.main-leave-to {
  opacity: 0;
  margin-left: 20px;
}
.column-row {
  display: inline-block;
  padding: 0 !important;
  margin: 0 !important;
  .icon {
    width: 20px;
    font-size: 20px;
    margin-right: 0;
    vertical-align: -0.25em;
    transition: transform 0.3s;
    color: unset;
    &[class^='el-icon-'] {
      vertical-align: middle;
    }
  }
  &:hover > .icon,
  .el-col:hover > .icon,
  .el-submenu__title:hover > .icon {
    transform: scale(1.2);
  }
  .el-col {
    width: 64px;
    height: 64px;
    margin: 4px;
    background: #f6f5fa;
    border-radius: 4px;
    padding: 0 !important;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    align-items: center;
    font-size: 13px;
    font-family: 'PingFang SC-Regular', 'PingFang SC';
    font-weight: 400;
    color: #414653;
    cursor: pointer;
    @include themeify {
      color: themed('g-sub-sidebar-menu-color');
    }
    &:hover {
      @include themeify {
        color: themed('g-sub-sidebar-menu-hover-color');
        background-color: themed('g-sub-sidebar-menu-hover-bg');
      }
    }
  }
  .is-active {
    @include themeify {
      color: themed('g-sub-sidebar-menu-active-color') !important;
      background-color: themed('g-sub-sidebar-menu-active-bg') !important;
    }
  }
}
</style>
<style lang="scss">
.alarm-description-message {
  height: auto !important;
  padding: 10px 10px 10px 15px !important;
  .alarm-description-box {
    display: flex;
    align-content: stretch;
    max-width: 40vw;
    .box-title {
      flex: 1;
      line-height: 18px;
    }
    .box-btn-group {
      flex-shrink: 0;
      margin: auto;
      padding: 0 8px;
      color: #3562db;
      cursor: pointer;
      .btn-line {
        margin: 0 8px;
        cursor: default;
      }
    }
  }
}
</style>
