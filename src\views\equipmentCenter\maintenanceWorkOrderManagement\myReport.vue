<template>
  <div style="height: 100%">
    <!-- <div class="tool-box">
      <el-select v-model="flowCode" placeholder="工单状态">
        <el-option v-for="item in flowcodeOption" :key="item.label" :label="item.label" :value="item.value"> </el-option>
      </el-select>
      <el-date-picker v-model="dateVal" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd+HH:mm:ss">
      </el-date-picker>
      <el-button type="primary" plain @click="resetForm">重置</el-button>
      <el-button type="primary" @click="searchForm">查询</el-button>
    </div> -->
    <el-table v-loading="tableLoading" :data="tableData" border style="width: 100%" height="calc(100% - 50px)">
      <el-table-column label="序号" type="index" width="50" align="center" :resizable="false">
        <template slot-scope="scope">
          {{ (pageNo - 1) * pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="维修单号" width="180" :resizable="false">
        <template slot-scope="scope">
          <span style="color: #3562db; cursor: pointer" @click="handleClick(scope.row)">{{ scope.row.workNum }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="createDate" label="报修时间" show-overflow-tooltip :resizable="false"> </el-table-column>
      <el-table-column prop="createByName" label="报修人" show-overflow-tooltip :resizable="false"> </el-table-column>
      <el-table-column prop="sourcesPhone" label="报修人电话" show-overflow-tooltip :resizable="false"> </el-table-column>
      <el-table-column prop="attachmentUrl" label="报修图片" show-overflow-tooltip :resizable="false">
        <template slot-scope="scope">
          <span v-if="scope.row.attachmentUrl" style="color: #3562db; cursor: pointer" @click="showPicture(scope.row)">点击查看</span>
          <span v-else>暂无图片</span>
        </template>
      </el-table-column>
      <el-table-column prop="deviceName" label="关联设备名称" show-overflow-tooltip :resizable="false">
        <template slot-scope="scope">
          <span style="color: #3562db; cursor: pointer" @click="showDevice(scope.row)">{{ scope.row.deviceName }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="deviceNumber" label="关联设备编号" show-overflow-tooltip :resizable="false"> </el-table-column>
      <el-table-column label="状态" :resizable="false" width="120">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.flowcode == '1'" type="danger">未受理</el-tag>
          <el-tag v-else-if="scope.row.flowcode == '2'" type="danger">未派工</el-tag>
          <el-tag v-else-if="scope.row.flowcode == '3'" type="success">已派工</el-tag>
          <el-tag v-else-if="scope.row.flowcode == '4'" type="warning">已挂单</el-tag>
          <el-tag v-else-if="scope.row.flowcode == '5'" type="success">已完工</el-tag>
          <el-tag v-else-if="scope.row.flowcode == '15'" type="success">已验收</el-tag>
          <el-tag v-else-if="scope.row.flowcode == '6'" type="danger">已取消</el-tag>
          <el-tag v-else-if="scope.row.flowcode == '7'" type="info">暂存</el-tag>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page="pageNo"
      :page-sizes="[15, 30, 50, 100]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    >
    </el-pagination>
    <imgCarousel :dialogVisibleImg="dialogVisibleImg" :imgArr="imgArr" @closeDialog="closeDialogImg"></imgCarousel>
    <template v-if="workOrderDetailCenterShow">
      <el-dialog :visible.sync="workOrderDetailCenterShow" custom-class="detailDialog main" :close-on-click-modal="false" :before-close="dialogClosed">
        <template slot="title">
          <span class="dialog-title">设备报修</span>
        </template>
        <workOrderDetailList :rowData="detailObj" @close="dialogClosed" />
      </el-dialog>
    </template>
  </div>
</template>
<script>
import store from '@/store/index'
import imgCarousel from '@/components/imgCarousel/imgCarousel'
import workOrderDetailList from './workOrderDetail.vue'
export default {
  name: 'maintenanceOrder',
  components: {
    imgCarousel,
    workOrderDetailList
  },
  props: {
    searchFrom: {
      type: Object,
      default: () => {
        return {
          flowCode: '',
          dateVal: '',
          page: 1
        }
      }
    }
  },
  data() {
    return {
      // flowCode: '',
      // dateVal: '',
      // flowcodeOption: [
      //   {
      //     label: '全部',
      //     value: ''
      //   },
      //   {
      //     label: '未受理',
      //     value: '1'
      //   },
      //   {
      //     label: '暂存',
      //     value: '7'
      //   },
      //   {
      //     label: '未派工',
      //     value: '2'
      //   },
      //   {
      //     label: '已派工',
      //     value: '3'
      //   },
      //   {
      //     label: '已挂单',
      //     value: '4'
      //   },
      //   {
      //     label: '已完工',
      //     value: '5'
      //   },
      //   {
      //     label: '已取消',
      //     value: '6'
      //   }
      // ],
      tableLoading: false,
      tableData: [],
      pageNo: 1,
      pageSize: 15,
      total: 0,
      dialogVisibleImg: false,
      imgArr: [],
      workOrderDetailCenterShow: false,
      detailObj: {}
    }
  },
  mounted() {
    this.getTableData()
  },
  activated() {
    this.getTableData()
  },
  methods: {
    handleClick(row) {
      this.detailObj = row
      this.workOrderDetailCenterShow = true
    },
    showDevice(row) {
      if (!row.deviceId) return this.$message.error('该记录无关联设备')
      this.$api
        .getAssetsIdById({
          assetsId: row.deviceId
        })
        .then((res) => {
          if (!res.data.length) {
            return this.$message.error('该设备已被删除')
          }
          this.$router.push({
            name: 'addDevice',
            query: {
              type: 'details',
              assetsId: row.deviceId,
              id: res.data
            }
          })
        })
    },
    showPicture(row) {
      if (!row.attachmentUrl) {
        return this.$message.error('该记录无图片')
      }
      let imgArr = row.attachmentUrl.split(',')
      if (imgArr.length > 0) {
        this.imgArr = imgArr
        this.dialogVisibleImg = true
      } else {
        this.$message.error('该记录无图片')
      }
    },
    closeDialogImg() {
      this.dialogVisibleImg = false
      this.imgArr = []
    },
    // resetForm() {
    //   this.flowCode = ''
    //   this.dateVal = ''
    //   this.pageNo = 1
    //   this.getTableData()
    // },
    // searchForm() {
    //   this.getTableData()
    // },
    handleSizeChange(val) {
      this.pageSize = val
      this.pageNo = 1
      this.getTableData()
    },
    handleCurrentChange(val) {
      this.pageNo = val
      this.getTableData({pageNo: val})
    },
    getTableData(obj) {
      const { flowCode, dateVal, pageNo } = this.searchFrom
      const userInfo = store.state.user.userInfo.user
      let params = {
        pageNo: pageNo,
        pageSize: this.pageSize,
        startTime: dateVal[0],
        endTime: dateVal[1],
        showTimeType: '4',
        flowcode: flowCode,
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ',
        workTypeCode: '17',
        sourcesDept: '',
        callerCode: userInfo.staffId,
        ...obj
      }
      // if (store.state.user.userInfo.dataScopeSet && store.state.user.userInfo.dataScopeSet[0] != 'DATA_SCOPE_ALL') {
      //   params.sourcesDept = store.state.user.userInfo.dataScopeSet.join(',')
      // }
      this.tableLoading = true
      this.$api.getWorkOrderDataList(params).then((res) => {
        this.tableLoading = false
        this.tableData = res.rows
        this.total = res.total
      })
    },
    dialogClosed() {
      this.workOrderDetailCenterShow = false
    }
  }
}
</script>
<style lang="scss" scoped>

.el-pagination {
  margin-top: 16px;
}

::v-deep .el-dialog__body {
  height: 70vh;
}
</style>
