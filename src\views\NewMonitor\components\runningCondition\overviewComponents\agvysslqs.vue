<!-- 运行监测 -->
<template>
  <ContentCard :title="item.componentTitleShow" :scrollbarHover="true" :cstyle="{ width: '100%', height: '100%' }"
    class="drag_class" :hasMoreOper="['edit']"
    @more-oper-event="(val) => $emit('all-more-Oper', val, item.componentName, item.chartType)">
    <div slot="title-right" class="data-btns">
      <span :class="{ 'active-btn': totalCostDateType == 'week' }" @click="changeTime('week')">按周</span>
      <span :class="{ 'active-btn': totalCostDateType == 'month' }" @click="changeTime('month')">按月</span>
      <span :class="{ 'active-btn': totalCostDateType == 'year' }" @click="changeTime('year')">按年</span>
      <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始月份"
        end-placeholder="结束月份" :picker-options="pickerOptions" :clearable="false" @change="changeTime('custom')"
        value-format="yyyy-MM-dd">
      </el-date-picker>
      <el-select v-model="robotIds" multiple collapse-tags placeholder="请选择设备" @change="handleSelectChange"
        class="ml-10" clearable style="width: 250px;">
        <el-option v-for="item in listData" :key="item.id" :label="item.name" :value="item.id">
        </el-option>
      </el-select>
    </div>
    <div slot="content" class="operation-list">
      <echarts :ref="`agvysslqs${item.componentDataType}`" :domId="`agvysslqs${item.componentDataType}`" width="100%"
        height="100%" />
      <div v-if="!hasChart"
        style="width: 100%; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center; padding: 50px">
        <img src="@/assets/images/newMonitor/no-chat.png" />
        <span>暂无数据</span>
      </div>
    </div>
  </ContentCard>
</template>
<script lang="jsx">
import moment from 'moment'
export default {
  name: 'agvysslqs',
  props: {
    item: {
      type: Object,
      default: () => { }
    },
    systemCode: {
      type: String,
      default: ''
    },
    deviceId: {
      type: String,
      default: ''
    },
  },
  created() {
  },
  watch: {
    // 时间范围数据变化
    dateRange: function () {
      this.diffTime()
    }
  },
  mounted() {
    setTimeout(() => {
      if (this.deviceId) { this.getDeviceAnalysisData() }
      this.getDataList()
      this.dateRange = [this.dateTime.startTime, this.dateTime.endTime]
      this.changeTime('month')
    }, 150)
  },
  data() {
    return {
      hasChart: false,
      loading: false,
      totalCostDateType: 'month',
      dateTime: {
        startTime: moment().startOf('month').format('YYYY-MM-DD  00:00:00'),
        endTime: moment().format('YYYY-MM-DD 23:59:59'),
      },
      chartType: '1',
      dateRange: null,
      pickerOptions: {
      },
      robotIds: '',
      listData: [],
      queryParams: null,
      timeType: 1,
    }
  },
  methods: {
    handleSelectChange(val) {
      this.robotIds = val
      this.getDeviceAnalysisData()
    },
    getDataList() {
      let params = {
        terms: [
          {
            column: "systemTypeCode",
            value: 'AGVJQR'
          },
          {
            column: "deviceTypeCode",
            value: 'WLJQR'
          },
        ]
      }
      this.listData = []
      this.$api
        .getOperationalMonitoringQuery(params)
        .then((res) => {
          if (res.status === '200' || res.status === 200) {
            this.listData = res.result || []
          }
        })
        .catch(() => {
        })
    },
    diffTime() {
      if (!this.dateRange) return
      this.dateTime.startTime = this.dateRange[0]  // 设置之前时间
      this.dateTime.endTime = this.dateRange[1]   // 获取当前时间
    },
    changeTime(type) {
      this.totalCostDateType = type
      if (type == 'week') {
        this.dateTime.startTime = moment().startOf('isoWeek').format('YYYY-MM-DD 00:00:00');
        this.dateTime.endTime = moment().format('YYYY-MM-DD 23:59:59');
        this.dateRange = [this.dateTime.startTime, this.dateTime.endTime]
        this.timeType = 1
      } else if (type == 'month') {
        this.dateTime.startTime = moment().startOf('month').format('YYYY-MM-DD  00:00:00')
        this.dateTime.endTime = moment().format('YYYY-MM-DD 23:59:59')
        this.timeType = 1
      } else if (type == 'year') {
        this.dateTime.startTime = moment().startOf('year').format('YYYY-MM-DD  00:00:00')
        this.dateTime.endTime = moment().format('YYYY-MM-DD 23:59:59')
        this.timeType = 2
      } else if (type == 'custom') {

      }
      this.dateRange = [this.dateTime.startTime, this.dateTime.endTime]
      this.getDeviceAnalysisData()
    },
    getDeviceAnalysisData() {
      this.$api.getCarryTrends({ robotIds: this.robotIds.join(','), timeType: this.timeType, ...this.dateTime }).then((res) => {
        if (res.code == '200') {
          this.hasChart = true
          this.appendEchartsData(res.data)
        } else {
          this.hasChart = false
        }
      })
    },
    appendEchartsData(data) {
      const baseData = {
        grid: {
          left: '0%',
          right: '2%',
          bottom: '0%',
          top: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: data.xAxis.data,
        },
        yAxis: {
          type: 'value',
        },
        tooltip: {
          trigger: 'axis',
          left: 0,
          // position: function (point, params, dom, rect, size) {
          //   const tooltipWidth = size.contentSize[0];
          //   const tooltipHeight = size.contentSize[1];
          //   const x = Math.min(point[0], window.innerWidth - tooltipWidth);
          //   const y = Math.max(point[1] - tooltipHeight, 0);
          //   return [x, y];
          // },
        },
        series: data.series,
        dataZoom: [
          {
            type: 'inside',
            start: 0, // 起始位置
            end: 100, // 结束位置
            height: 10,
          },
          {
            type: 'slider',
            start: 0,
            end: 100,
            height: 10,
          }
        ]
      }
      if (data) {
        data.series.length > 10 ? baseData.dataZoom[0].end = 40 : baseData.dataZoom[0].end = 100
      }
      this.$refs[`agvysslqs${this.item.componentDataType}`].init(baseData)
    },
  }
}
</script>

<style lang="scss" scoped>
.data-btns {
  position: absolute;
  right: 7%;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &>span {
    width: 44px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    margin: 0 4px;
    background-color: #f6f5fa;
    font-size: 14px;
    font-family: 'PingFang SC-Medium', 'PingFang SC';
    border: none;
    border-radius: 2px;
    color: #7f848c;
    cursor: pointer;
  }

  .active-btn {
    background-color: #e6effc !important;
    color: #3562db !important;
    border-color: #e6effc !important;
  }
}

.operation-list {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
}

.ml-10 {
  margin-left: 10px;
}
</style>
