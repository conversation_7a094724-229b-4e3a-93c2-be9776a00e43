import down from '@/assets/images/monitor/down.png'
import up from '@/assets/images/monitor/up.png'
import * as echarts from 'echarts'
export default {
  methods: {
    dataArr(data, colorHalf, colorZero) {
      let arr = [
        {
          name: '全部',
          type: 'line',
          data: [],
          areaStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: colorHalf[0]
                },
                {
                  offset: 1,
                  color: colorZero[0]
                }
              ])
            }
          }
        }
      ]
      data.forEach((v) => {
        let total = 0
        v.dateCounts?.forEach((sonV, index) => {
          let obj = arr.find((ele) => ele.name == sonV.projectName)
          var policeCount = sonV.policeCount
          // var policeCount = Math.floor(Math.random() * 10 + 1)
          total += policeCount
          if (obj) {
            obj.data.push(policeCount)
          } else {
            let dataObj = {
              name: sonV.projectName,
              type: 'line',
              data: [policeCount],
              // symbol: 'circle',
              areaStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: colorHalf[(index + 1) % colorHalf.length]
                    },
                    {
                      offset: 1,
                      color: colorZero[(index + 1) % colorZero.length]
                    }
                  ])
                }
              }
            }
            arr.push(dataObj)
          }
        })
        arr[0].data.push(total)
      })
      return arr
    },
    // 折线图
    setLineChart(data = []) {
      function colorAdd(transparency) {
        return [
          `rgba(255, 100, 97, ${transparency})`,
          `rgba(255, 148, 53, ${transparency})`,
          `rgba(53, 98, 219, ${transparency})`,
          `rgba(0, 188, 109, ${transparency})`,
          `rgba(115, 192, 222, ${transparency})`,
          `rgba(154, 96, 180, ${transparency})`,
          `rgba(250, 200, 88, ${transparency})`,
          `rgba(9, 205, 143, ${transparency})`,
          `rgba(145, 215, 132, ${transparency})`
        ]
      }
      let colorHalf = colorAdd('.5')
      let colorZero = colorAdd('0')
      let option
      // var a = Array.from(new Array(10).keys()).slice(1)
      if (data.length) {
        option = {
          title: {
            text: '报警趋势（件）',
            left: 'center'
          },
          color: colorHalf,
          tooltip: {
            trigger: 'axis'
          },
          legend: {
            // icon: 'rect',
            show: true,
            top: '10%'
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: data.map((x) => {
              return x.policeTime
            })
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: '{value}'
            }
          },
          series: this.dataArr(data, colorHalf, colorZero),
          dataZoom: [
            {
              height: 20, // 时间滚动条的高度
              type: 'inside', // type的作用是指定数据缩放的类型，slider表示使用滑动条进行缩放，inside表示使用鼠标滚轮进行缩放。
              xAxisIndex: 0, // 作用在x轴的下标（因为x轴可以有多个）
              filterMode: 'filter', // 间滚动条的过滤模式,'filter'表示滑动时间条时会直接过滤掉不在时间范围内的数据，'weakFilter'表示滑动时间条时会逐渐过滤掉不在时间范围内的数据。
              start: 0, // 默认开始位置（百分比）
              end: 100 // 默认结束位置（百分比）
            },
            {
              type: 'inside',
              xAxisIndex: 0,
              filterMode: 'filter',
              start: 0,
              end: 100
            }
          ],
          grid: {
            // 让图表占满容器
            // top: '0px',
            left: '50px',
            right: '30px',
            bottom: '0px',
            top: '60px',
            containLabel: true
          }
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      return option
    },
    // 饼图
    setPieChart(unit, data = [], total) {
      let position, center
      if (this.gridTemplateRows == 1) {
        position = {
          left: 'center',
          bottom: '10%'
        }
        center = ['50%', '40%']
      } else {
        position = {
          top: '50',
          right: '0%'
        }
        center = ['15%', '50%']
      }
      let color = ['#3562DB', '#08CB83', '#FF9435', '#F8456B', '#00FFFF', '#4AEAB0']
      let option
      if (data.length) {
        option = {
          customData: data,
          backgroundColor: '#fff',
          color: color,
          title: {
            text: '报警类型占比',
            left: 'center'
          },
          tooltip: {
            show: true,
            formatter: '{b}: {c}个<br/>百分比: {d}%'
          },
          legend: {
            type: 'scroll', // 开启滚动功能
            scrollDataIndex: 0, // 设置需要滚动的系列的数据索引
            orient: 'vertical',
            ...position,
            height: '70%',
            itemHeight: 8,
            itemWidth: 8,
            tooltip: {
              show: true
            },
            formatter: (name) => {
              let obj = data.find((ele) => ele.name == name)
              let ringtype = [0, 1, 2].includes(obj.ringRatioStatus) ? (obj.ringRatioStatus == 2 ? 'ring|' : obj.ringRatioStatus == 0 ? 'ringUp|' : 'ringDown|') : ''
              let ringIcon = [0, 1, 2].includes(obj.ringRatioStatus) ? (obj.ringRatioStatus == 2 ? '' : obj.ringRatioStatus == 0 ? '{up|}' : '{down|}') : ''
              let proportion = total ? (Math.round((obj.value / total) * 10000) / 100).toFixed(2) : 0
              let ringRatio = [0, 1, 2].includes(obj.ringRatioStatus) ? (obj.ringRatioStatus == 1 ? obj.ringRatio.replace('-', '') : obj.ringRatio) : ''
              let str = `{name|${obj.name}：${obj.value}${unit}}{proportion|占比：${proportion}%}{ring|环比：}{${ringtype}${ringRatio || 0}}${ringIcon}`
              // let str = `{name|${obj.name}：12345${unit}}{proportion|占比：100.00%}{ring|环比：}{${ringtype}100.00%}{up|}`
              return str
            },
            textStyle: {
              // color: '#2DA7F6',
              // fontSize: '16',
              rich: {
                up: {
                  color: 'blue',
                  height: 20,
                  width: 20,
                  backgroundColor: {
                    image: up
                  }
                },
                down: {
                  color: 'red',
                  height: 20,
                  width: 20,
                  backgroundColor: {
                    image: down
                  }
                },
                ringUp: {
                  fontSize: 14,
                  color: '#FA403C',
                  padding: [0, 5, 0, 0]
                },
                ringDown: {
                  fontSize: 14,
                  color: '#00BC6D',
                  padding: [0, 5, 0, 0]
                },
                name: {
                  width: 130,
                  fontSize: 14,
                  padding: [0, 10, 0, 0]
                },
                proportion: {
                  width: 100,
                  fontSize: 14,
                  padding: [0, 10, 0, 0]
                },
                ring: {
                  fontSize: 14
                }
              }
            }
          },
          series: [
            {
              type: 'pie',
              radius: ['35', '65'],
              center: center,
              data: data.sort((a, b) => b.value - a.value),
              labelLine: {
                show: false
              },
              label: {
                show: false,
                position: 'center'
              }
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      return option
    },
    // 批量饼图
    setBatchPieChart(unit, data = [], title = [], colors = []) {
      // let color = ['#FF8352', '#0E7CE2']
      let option
      if (data.length) {
        option = {
          customData: data,
          backgroundColor: '#fff',
          color: colors,
          title: [],
          legend: {
            top: 'middle',
            orient: 'vertical',
            // selectedMode: false,   // 设置是否打开默认点击事件
            right: '0',
            itemHeight: 8,
            itemWidth: 8,
            // formatter: `{name|{u}}{value|{i}${unit}}{value|{o}%}`,
            formatter: (name) => {
              let value
              for (let i = 0; i < data.length; i++) {
                if (data[i].name == name) {
                  value = data[i].value
                }
              }
              let arr = `{name|${name}}{${name == '正常' ? 'valueBright|' : 'value|'}${value}}{unit|${unit}}`
              // let arr = `${name}${value}${unit}`
              return arr
            },
            textStyle: {
              rich: {
                name: {
                  fontSize: 12,
                  padding: [0, 10, 0, 0]
                },
                value: {
                  fontSize: 14
                },
                valueBright: {
                  fontSize: 14,
                  color: '#3562DB'
                },
                unit: {
                  fontSize: 12,
                  color: '#CCCED3',
                  padding: [0, 0, 0, 5]
                }
              }
            }
          },
          series: [
            {
              type: 'pie',
              radius: ['55%', '85%'],
              center: ['20%', '50%'],
              data: data,
              labelLine: {
                show: false
              },
              label: {
                show: false,
                position: 'center'
              }
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      return option
    },
    // 弹框折线图
    dialogSetLineChart(data, title = []) {
      let color = ['rgba(63, 99, 211, .5)']
      let option
      // var a = Array.from(new Array(10000).keys()).slice(1)
      if (data.xAxis.length) {
        option = {
          color: color,
          tooltip: {
            trigger: 'axis'
          },
          legend: {
            show: false
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: data.xAxis
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: '{value}'
            }
          },
          series: [
            {
              name: '',
              type: 'line',
              data: data.yAxis,
              // symbol: 'circle',
              areaStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: 'rgba(63, 99, 211, .5)'
                    },
                    {
                      offset: 1,
                      color: 'rgba(63, 99, 211, 0)'
                    }
                  ])
                }
              }
            }
          ],
          dataZoom: [
            {
              height: 20, // 时间滚动条的高度
              type: 'inside', // type的作用是指定数据缩放的类型，slider表示使用滑动条进行缩放，inside表示使用鼠标滚轮进行缩放。
              xAxisIndex: 0, // 作用在x轴的下标（因为x轴可以有多个）
              filterMode: 'filter', // 间滚动条的过滤模式,'filter'表示滑动时间条时会直接过滤掉不在时间范围内的数据，'weakFilter'表示滑动时间条时会逐渐过滤掉不在时间范围内的数据。
              start: 0, // 默认开始位置（百分比）
              end: 100 // 默认结束位置（百分比）
            },
            {
              type: 'inside',
              xAxisIndex: 0,
              filterMode: 'filter',
              start: 0,
              end: 100
            }
          ],
          grid: {
            top: '30px',
            left: '40px',
            right: '20px',
            bottom: '30px',
            containLabel: true
          }
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      return option
    },
    // 弹框柱状图
    dialogSetBarChart(data = [], title = []) {
      function totalLength() {
        var maxNum = data.sort((a, b) => a.offLineNum - b.offLineNum)[0].offLineNum
        return Array(data.length).fill(maxNum)
      }
      let color = '#FF9435'
      let option
      if (data.length) {
        option = {
          title: {
            left: 'left',
            top: 0,
            padding: [8, 0, 20, 0],
            textStyle: {
              color: color,
              fontSize: 14,
              lineHeight: 24,
              fontWeight: 'bold'
            }
          },
          grid: {
            left: '5px',
            right: '5px',
            top: '5px',
            bottom: '0px',
            containLabel: true,
            show: false
          },
          tooltip: {
            show: false
          },
          xAxis: {
            splitLine: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            }
          },
          yAxis: [
            {
              show: true,
              type: 'category',
              inverse: true,
              data: data.map((x) => x.ispHarvesterName),
              axisLine: {
                show: false
              },
              axisTick: {
                show: false
              },
              axisLabel: {
                color: '#666666',
                fontSize: 12
              }
            }
          ],
          series: [
            {
              type: 'bar',
              animation: false,
              barGap: '-100%',
              //  所有数据为
              // data: totalLength(),
              itemStyle: {
                normal: {
                  color: 'rgba(0,0,0,0.05)'
                }
              },
              barCategoryGap: 30, // 同一系列的柱间距离
              barWidth: 7
            },
            {
              type: 'bar',
              hoverAnimation: false,
              data: data.map((x) => x.offLineNum),
              // barGap:'30px',
              itemStyle: {
                normal: {
                  color: color
                }
              },
              label: {
                show: false
              },
              barCategoryGap: 30,
              barWidth: 7
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      return option
    }
  }
}
