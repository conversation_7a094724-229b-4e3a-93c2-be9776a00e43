import Layout from '@/layout'
export default [
  {
    path: '/policyspace',
    component: Layout,
    redirect: '/policyspace/spaceOperations',
    name: 'policyspace',
    meta: {
      title: '空间运维',
      menuAuth: '/policyspace/spaceOperations'
      // icon: 'sidebar-breadcrumb'
    },
    children: [
      {
        path: 'spaceOperations',
        name: 'spaceOperations',
        component: () => import('@/views/operationPort/spaceManage/operationsManage/commonOperations.vue'),
        meta: {
          title: '空间运维',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/policyspace'
        }
      },
      {
        path: '/spaceOperationsList',
        name: 'spaceOperationsList',
        component: () => import('@/views/operationPort/spaceManage/operationsManage/spaceOperationsList.vue'),
        meta: {
          title: '空间',
          sidebar: false,
          breadcrumb: false,
          avtiveMenu: '/spaceManage/dataManage'
        }
      },
      {
        path: '/spaceLedger',
        name: 'spaceLedger',
        component: () => import('@/views/operationPort/spaceManage/components/spaceLedger.vue'),
        meta: {
          title: '空间台账',
          sidebar: false,
          breadcrumb: false,
          avtiveMenu: '/spaceManage/dataManage'
        }
      }
    ]
  },
  {
    path: '/policyioms',
    component: Layout,
    redirect: '/policyioms/iomsOperations',
    name: 'policyioms',
    meta: {
      title: '服务监管',
      menuAuth: '/policyioms/iomsOperations'
      // icon: 'sidebar-breadcrumb'
    },
    children: [
      {
        path: 'iomsOperations',
        name: 'iomsOperations',
        component: () => import('@/views/operationPort/spaceManage/operationsManage/commonOperations.vue'),
        meta: {
          title: '服务监管',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/policyioms'
        }
      }
    ]
  },
  {
    path: '/policyasset',
    component: Layout,
    redirect: '/policyasset/assetOperations',
    name: 'policyasset',
    meta: {
      title: '资产管理',
      menuAuth: '/policyasset/assetOperations'
    },
    children: [
      {
        path: 'assetOperations',
        name: 'assetOperations',
        component: () => import('@/views/operationPort/spaceManage/operationsManage/commonOperations.vue'),
        meta: {
          title: '资产管理',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/policyasset'
        }
      },
      {
        path: 'detailsAssets',
        name: 'DetailsAssets',
        component: () => import('@/views/operationPort/spaceManage/components/sjtyyDetailsAssets.vue'),
        meta: {
          title: '资产详情',
          sidebar: false,
          breadcrumb: false,
          avtiveMenu: '/policyasset'
        }
      },
      {
        path: '/detailsAssets',
        name: 'detailsAssets',
        component: () => import('@/views/operationPort/spaceManage/components/detailsAssets.vue'),
        meta: {
          title: '资产详情',
          sidebar: false,
          breadcrumb: false,
          avtiveMenu: '/policyasset'
        }
      }
    ]
  },
  {
    path: '/policywaste',
    component: Layout,
    redirect: '/policywaste/wasteOperations',
    name: 'policywaste',
    meta: {
      title: '医废监管',
      menuAuth: '/policywaste/wasteOperations'
    },
    children: [
      {
        path: 'wasteOperations',
        name: 'wasteOperations',
        component: () => import('@/views/operationPort/spaceManage/operationsManage/commonOperations.vue'),
        meta: {
          title: '医废监管',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/policywaste'
        }
      }
    ]
  },
  {
    path: '/policydanger',
    component: Layout,
    redirect: '/policydanger/dangerOperations',
    name: 'policydanger',
    meta: {
      title: '隐患管理',
      menuAuth: '/policydanger/dangerOperations',
      sysType: 'ipsm'
    },
    children: [
      {
        path: 'dangerOperations',
        name: 'dangerOperations',
        component: () => import('@/views/operationPort/spaceManage/operationsManage/commonOperations.vue'),
        meta: {
          title: '隐患管理',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/policydanger'
        }
      },
      {
        path: 'hiddenManagementDetails',
        name: 'hiddenManagementDetails3',
        component: () => import('@/views/securityCenter/hiddenDanger/hiddenManagementDetails.vue'),
        meta: {
          title: '隐患详情',
          sidebar: false,
          activeMenu: '/policydanger/hiddenManagementDetails'
        }
      },
      {
        path: 'dangerList',
        name: 'dangerList',
        component: () => import('@/views/securityCenter/hiddenDanger/dangerList.vue'),
        meta: {
          title: '隐患列表',
          sidebar: false,
          activeMenu: '/policydanger/hiddenManagementDetails'
        }
      },
      {
        path: '/Details',
        name: 'Details',
        component: () => import('@/views/operationPort/spaceManage/components/Details.vue'),
        meta: {
          title: '隐患详情',
          sidebar: false,
          breadcrumb: false,
          avtiveMenu: '/spaceManage/dataManage'
        }
      }
    ]
  },
  {
    path: '/policyrisk',
    component: Layout,
    redirect: '/policyrisk/riskOperations',
    name: 'policyrisk',
    meta: {
      title: '风险管控',
      menuAuth: '/policyrisk/riskOperations',
      sysType: 'ipsm'
    },
    children: [
      {
        path: 'riskOperations',
        name: 'riskOperations',
        component: () => import('@/views/operationPort/spaceManage/operationsManage/commonOperations.vue'),
        meta: {
          title: '风险管控',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/policyrisk'
        }
      },
      {
        path: 'riskList',
        name: 'riskList',
        component: () => import('@/views/securityCenter/riskControl/riskPointManagementList/riskList.vue'),
        meta: {
          title: '风险点列表',
          sidebar: false,
          activeMenu: '/policyrisk/riskOperations'
        }
      },
      {
        path: 'taskList',
        name: 'taskList',
        component: () => import('@/views/equipmentCenter/InspectionManagement/components/taskList.vue'),
        meta: {
          title: '任务列表',
          sidebar: false,
          avtiveMenu: '/policyrisk/riskOperations'
        }
      },
      {
        path: 'planProgressDetail',
        name: 'planProgressDetail',
        component: () => import('@/views/equipmentCenter/InspectionManagement/planProgressDetail.vue'),
        meta: {
          title: '任务详情',
          sidebar: false,
          avtiveMenu: '/spaceManage/dataManage'
        }
      },
      {
        path: '/addRisk',
        name: 'addRisk',
        component: () => import('@/views/operationPort/spaceManage/components/addRisk.vue'),
        meta: {
          title: '风险详情',
          sidebar: false,
          breadcrumb: false,
          avtiveMenu: '/spaceManage/dataManage'
        }
      }
    ]
  },
  {
    path: '/policyenergy',
    component: Layout,
    redirect: '/policyenergy/energyOperations',
    name: 'policyenergy',
    meta: {
      title: '能源能耗',
      menuAuth: '/policyenergy/energyOperations'
    },
    children: [
      {
        path: 'energyOperations',
        name: 'energyOperations',
        component: () => import('@/views/operationPort/spaceManage/operationsManage/commonOperations.vue'),
        meta: {
          title: '能源能耗',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/policyenergy'
        }
      }
    ]
  },
  {
    path: '/policycockpit',
    component: Layout,
    redirect: '/policycockpit/safetyDataCockpit',
    name: 'policyenergy',
    meta: {
      title: '安全数据驾驶舱',
      menuAuth: '/policycockpit/safetyDataCockpit'
    },
    children: [
      {
        path: 'safetyDataCockpit',
        name: 'safetyDataCockpit',
        component: () => import('@/views/operationPort/safetyDataCockpit/index.vue'),
        meta: {
          title: '安全数据驾驶舱',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/policycockpit'
        }
      }
    ]
  }
]
