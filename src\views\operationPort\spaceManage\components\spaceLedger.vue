<template>
  <PageContainer :footer="true">
    <div slot="content" class="whole">
      <div style="height: 97%">
        <el-table v-loading="loading" :data="tableData" height="calc(100% - 10px)" stripe border>
          <el-table-column type="index" label="序号" width="50" align="center">
            <template slot-scope="scope">
              <span>{{ (pagination.current - 1) * pagination.size + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column v-for="item in tableHeaders" :key="item.porp" :prop="item.porp" :label="item.label" :width="item.width" show-overflow-tooltip align="center">
            <template slot-scope="scope">
              <span v-if="item.porp == 'localSpaceName'" class="color_blue" @click="ViewFn(scope.row)">
                {{ scope.row.localSpaceName }}
              </span>
              <span v-else>&nbsp;{{ scope.row[item.porp] }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        :current-page="pagination.current"
        :page-sizes="[15, 30, 50, 100]"
        :page-size="pagination.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="revert">取消</el-button>
    </div>
  </PageContainer>
</template>

<script>
export default {
  name: 'spaceLedger',
  data() {
    return {
      loading: false,
      tableData: [],
      filters: {
        unitId: '',
        parentDeptName: '',
        deptPidId: '',
        deptName: '',
        principalName: '',
        pmId: ''
      },
      pagination: {
        current: 1,
        size: 15
      },
      total: 0,
      nation: null,
      tableHeaders: [
        {
          porp: 'localSpaceName',
          label: '空间名称'
        },
        {
          porp: 'buildName',
          label: '所属建筑'
        },
        {
          porp: 'floorName',
          label: '所属楼层'
        },
        {
          porp: 'functionDictName',
          label: '功能类型'
        },
        {
          porp: 'dmName',
          label: '归属部门'
        },
        {
          porp: 'principalName',
          label: '空间责任人'
        },
        {
          porp: 'area',
          label: '建筑面积（㎡）'
        },
        {
          porp: 'useArea',
          label: '使用面积（㎡）'
        },
        {
          porp: 'hight',
          label: '空间高度'
        },
        {
          porp: 'modelCode',
          label: '模型编码'
        }
      ]
    }
  },
  mounted() {
    this.departListFn()
  },
  methods: {
    revert() {
      this.$router.push({
        path: '/policyspace'
        // query: {
        //   pageModel: 'list'
        // }
      })
    },
    //  获取部门列表
    departListFn() {
      this.loading = true
      let param = {
        current: this.pagination.current,
        size: this.pagination.size,
        modelCode: this.$route.query.modelCode,
        haveModel: '0'
      }
      if (this.$route.query.type == 1) {
        param.deptId = this.$route.query.row.dataNameId
      } else if (this.$route.query.type == 2) {
        param.spaceState = '0'
        param.deptId = this.$route.query.row.dataNameId
      } else if (this.$route.query.type == 3) {
        param.functionDictId = this.$route.query.row.dataNameId
        param.spaceState = '0'
      } else {
        param.functionDictId = this.$route.query.row.dataNameId
      }
      this.$api.spaceList(param).then((res) => {
        // this.treeLoading = false;
        if (res.code == 200) {
          this.tableData = res.data.records
          this.total = res.data.total
          this.loading = false
        }
      })
    },
    handleSizeChange(val) {
      this.pagination.size = val
      this.departListFn()
    },
    handleCurrentChange(val) {
      this.pagination.current = val
      this.departListFn()
    },
    ViewFn(row) {
      this.$router.push({
        path: '/particulars',
        query: { type: 'View', id: row.id }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.whole {
  width: 100%;
  height: 100%;
  padding: 15px;
  background: #fff;
}

.color_blue {
  color: #5188fc;
  cursor: pointer;
}
</style>
