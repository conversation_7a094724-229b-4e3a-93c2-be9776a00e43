<script>
import { UsingStatusOptions } from '@/views/operationPort/constant'
import tableListMixin from '@/mixins/tableListMixin'
import SporadicProject from '@/api/module/sporadic-project-api'
export default {
  name: 'SupplierList',
  components: {
    SupplierEdit: () => import('./components/SupplierEdit')
  },
  filters: {
    statusFilter(status) {
      return UsingStatusOptions.find((it) => it.value === +status)?.label ?? '-'
    },
    operateStatusFilter(status) {
      const value = +status === 1 ? 0 : 1
      return UsingStatusOptions.find((it) => it.value === value).label
    }
  },
  mixins: [tableListMixin],
  data: () => ({
    searchForm: {
      name: ''
    },
    tableData: [],
    loadingStatus: false,
    dialog: {
      show: false,
      id: 0,
      readonly: false // 编辑内容只读
    }
  }),
  computed: {
    OperateType() {
      return {
        Edit: 'edit',
        Delete: 'delete',
        Create: 'create',
        View: 'view',
        Status: 'status'
      }
    }
  },
  mounted() {
    this.getDataList()
  },
  methods: {
    getDataList() {
      this.loadingStatus = true
      const params = {
        name: this.searchForm.name,
        pageSize: this.pagination.size,
        pageNo: this.pagination.current
      }
      this.$api.SporadicProject.pageProjectSupplier(params)
        .then((res) => {
          if (res.code === '200') {
            this.tableData = res.data.records
            this.pagination.total = res.data.total
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '查询失败'))
        .finally(() => (this.loadingStatus = false))
    },
    onSearch() {
      this.pagination.current = 1
      this.getDataList()
    },
    onReset() {
      this.$refs.formRef.resetFields()
      this.onSearch()
    },
    // 列表操作相关的事件绑定
    onOperate(row, command) {
      switch (command) {
        case this.OperateType.Delete:
          if (+row.state === 1) {
            this.$message.error('启用的供应商不允许删除！')
          } else {
            this.$confirm('删除后将无法恢复，是否确定删除？', '信息提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消'
            }).then(() => this.doDelete(row.id))
          }
          break
        case this.OperateType.Status:
          const value = +row.state === 1 ? 0 : 1
          this.doUpdateStatus(row.id, value)
          break
        default:
          this.dialog.id = row?.id ?? 0
          // 设置是否只读
          this.dialog.readonly = command === this.OperateType.View
          this.dialog.show = true
          break
      }
    },
    // 删除一行数据
    doDelete(id) {
      this.$api.SporadicProject.deleteProjectSupplier({ id })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('删除成功')
            this.getDataList()
          } else {
            throw res.message
          }
        })
        .catch((msg) => msg && this.$message.error(msg))
        .finally(() => (this.loadingStatus = false))
    },
    /** 修改供应商状态 */
    doUpdateStatus(id, status) {
      const param = { id, state: status }
      this.loadingStatus = true
      this.$api.SporadicProject.disOrUseProjectSupplier(param)
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('修改状态成功')
            this.getDataList()
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg))
        .finally(() => (this.loadingStatus = false))
    },
    businessNameFormatter(row) {
      let value = row.businessName
      if (!!value) {
        value = value.split(',').slice(-1)[0]
      }
      return value || '-'
    }
  }
}
</script>
<template>
  <PageContainer v-loading="loadingStatus" class="supplier-list">
    <template #content>
      <div class="supplier-list__header">
        <el-form ref="formRef" :model="searchForm" class="supplier-list__search" inline @submit.native.prevent="onSearch">
          <el-form-item prop="name">
            <el-input v-model="searchForm.name" clearable filterable placeholder="请输入供应商名称"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" plain @click="onReset">重置</el-button>
          </el-form-item>
        </el-form>
        <div class="supplier-list__actions">
          <el-button type="primary" @click="onOperate(undefined, OperateType.Create)">新建供应商</el-button>
        </div>
      </div>
      <div class="supplier-list__table">
        <el-table height="100%" :data="tableData" border stripe table-layout="auto" class="tableAuto" row-key="id">
          <el-table-column type="index" width="55" label="序号" />
          <el-table-column label="供应商名称" prop="name" show-overflow-tooltip></el-table-column>
          <el-table-column label="简称" prop="shortName" width="100px" show-overflow-tooltip></el-table-column>
          <el-table-column label="编码" prop="code" width="100px"></el-table-column>
          <el-table-column label="业务范围" prop="businessName" width="100px" :formatter="businessNameFormatter"></el-table-column>
          <el-table-column label="负责人" prop="leader" width="100px" show-overflow-tooltip></el-table-column>
          <el-table-column label="维保年限" prop="serviceDate" width="100px" :formatter="(row) => `${row.serviceDate}年`"></el-table-column>
          <el-table-column label="修改人" prop="updateName" width="100px"></el-table-column>
          <el-table-column label="修改时间" prop="updateTime" width="175px"></el-table-column>
          <el-table-column label="状态" prop="status" width="100px">
            <template #default="{ row }">
              <span class="supplier-list__tag" :class="`supplier-list__tag--${row.state}`">
                {{ row.state | statusFilter }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="备注" prop="remark" show-overflow-tooltip :formatter="row => row.remark||'-'"></el-table-column>
          <el-table-column label="操作" width="150px">
            <template #default="{ row }">
              <el-button type="text" @click="onOperate(row, OperateType.View)">查看</el-button>
              <el-button type="text" @click="onOperate(row, OperateType.Edit)">编辑</el-button>
              <el-dropdown @command="(command) => onOperate(row, command)">
                <el-button type="text" style="margin-left: 10px">更多</el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="OperateType.Status">
                      {{ row.state | operateStatusFilter }}
                    </el-dropdown-item>
                    <el-dropdown-item style="color: #ff1919" :command="OperateType.Delete">删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        class="supplier-list__pagination"
        :current-page="pagination.page"
        :page-sizes="pagination.pageSizeOptions"
        :page-size="pagination.size"
        :layout="pagination.layoutOptions"
        :total="pagination.total"
        @size-change="paginationSizeChange"
        @current-change="paginationCurrentChange"
      >
      </el-pagination>
      <!--编辑供应商-->
      <SupplierEdit v-bind="dialog" :visible.sync="dialog.show" @success="getDataList" />
    </template>
  </PageContainer>
</template>
<style scoped lang="scss">
.supplier-list {
  ::v-deep(> .container-content) {
    background-color: #fff;
    border-radius: 4px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    padding: 16px;
  }
  &__header {
    display: flex;
    justify-content: space-between;
  }
  &__table {
    margin-top: -10px;
    flex: 1;
  }
  &__actions {
    line-height: 40px;
  }
  &__pagination {
    margin-top: 10px;
  }
  &__tag {
    // 停用
    &--0 {
      --color: #f64646;
    }
    // 启用
    &--1 {
      --color: #00b42a;
    }
    &:before {
      content: '';
      display: inline-block;
      vertical-align: middle;
      margin-right: 4px;
      height: 4px;
      width: 4px;
      border-radius: 4px;
      background-color: var(--color);
    }
  }
}
</style>
