<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    title="查看详情"
    width="40%"
    :visible.sync="visible"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <div class="content">
      <div style="height: 400px;padding: 16px;">
        <TablePage
          ref="table"
          v-loading="tableLoading"
          :showPage="false"
          :tableColumn="tableColumn"
          :data="tableData"
          height="100%"
        />
      </div>
    </div>
  </el-dialog>
</template>
<script lang="jsx">
export default {
  name: 'circuitList',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tableLoading: false,
      tableData: [],
      tableColumn: [
        {
          prop: 'imsName',
          label: '设备名称'
        },
        {
          prop: 'state',
          label: '运行状态',
          formatter: (scope) => {
            let obj = {
              0: '运行',
              1: '停止',
              2: '故障',
              6: '离线'
            }
            return obj[scope.row.state] || '-'
          }
        }
      ]
    }
  },
  mounted() {
    this.getControlDiff()
  },
  methods: {
    getControlDiff() {
      this.$api.getControlDiff().then((res) => {
        if (res.code == '200') {
          this.tableData = res.data
        }
      })
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', !this.visible)
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  // padding: 16px !important;
  background: #fff;
}
.model-dialog {
  padding: 0 !important;
}
::v-deep .el-dialog__body {
  padding: 0 !important;
}
</style>
