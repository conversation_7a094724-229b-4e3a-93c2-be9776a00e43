<template>
  <div class="content-box">
    <div class="content-right content-right-width">
      <div class="right-top-form">
        <el-form ref="formInline" :inline="true" :model="formInline" class="demo-form-inline">
          <el-form-item label="" prop="unionSel">
            <el-input v-model="formInline.unionSel" placeholder="配件名称/配件编码" style="width: 300px"></el-input>
          </el-form-item>
          <el-form-item label="" prop="materialTypeCode">
            <el-select v-model="formInline.materialTypeCode" placeholder="请选择所属分类" filterable clearable>
              <el-option v-for="item in belongingCategoryList" :key="item.code" :label="item.name" :value="item.code"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="" prop="trademark">
            <el-select v-model="formInline.trademark" placeholder="请选择品牌" filterable clearable>
              <el-option v-for="item in brandList" :key="item.id" :label="item.brandName" :value="item.id"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSubmit">查询</el-button>
            <!-- 重置 -->
            <el-button type="primary" plain @click="onReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="right-bottom-box">
        <!-- 导出 -->
        <div class="right-bottom-table">
          <el-table
            v-loading="tableLoading"
            :data="tableData"
            border
            height="calc(100%)"
            style="width: 100%; overflow: auto"
            :cell-style="{ padding: '8px 0' }"
            stripe
            :header-cell-style="{ background: '#f2f4fbd1' }"
            :empty-text="emptyText"
            highlight-current-row
            @selection-change="handleSelectionChange"
          >
            <template v-for="(item, index) in inventoryWarning.tableHeader">
              <!-- 将 key 设置在最外层的循环 -->
              <!-- 处理正常的表头列 -->
              <el-table-column v-if="item.prop" :key="index" :prop="item.prop" :label="item.label" :width="item.width" show-overflow-tooltip> </el-table-column>
              <!-- 处理插槽列 -->
              <el-table-column v-else-if="item.slot" :key="index" :label="item.label" :width="item.width">
                <template slot-scope="scope">
                  <el-button type="text" @click="handleDetail(scope.row)">明细</el-button>
                </template>
              </el-table-column>
            </template>
          </el-table>
        </div>
        <div class="" style="padding-top: 10px; text-align: right">
          <el-pagination
            :current-page="paginationData.currentPage"
            :page-sizes="[15, 30, 50, 100]"
            :page-size="paginationData.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="paginationData.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { inventoryWarning } from './json/index.js'
export default {
  name: 'inventoryWarning',
  components: {},
  mixins: [],
  props: {},
  data() {
    return {
      inventoryWarning,
      tableData: [],
      brandList: [],
      belongingCategoryList: [],
      multipleSelection: [],
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      formInline: {
        unionSel: '',
        materialTypeCode: '',
        trademark: ''
      },
      emptyText: '暂无数据',
      userInfo: {},
      tableLoading: false
    }
  },
  computed: {},
  watch: {},
  created() {
    this.userInfo = this.$store.state.user.userInfo.user
    this.init()
    this.onSubmit()
  },
  mounted() {},
  methods: {
    // 查询
    onSubmit() {
      const {
        paginationData: { currentPage, pageSize },
        formInline: { unionSel, trademark, materialTypeCode },
        userInfo: { staffId, staffName }
      } = this
      let params = {
        userId: staffId,
        userName: staffName,
        currentPage,
        pageSize,
        trademark,
        unionSel,
        materialTypeCode
      }
      this.tableLoading = true
      this.$api
        .getInventoryNewWarningList(params)
        .then((res) => {
          const { code, message } = res
          if (code === '200') {
            const { list, sum } = res.data
            this.tableData = list
            this.paginationData.total = sum
          } else {
            this.$message.error(message)
          }
        })
        .catch((err) => {
          this.$message.error('查询失败！')
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    // 明细
    handleDetail(val) {
      this.$router.push({
        path: '/secondaryLibrary/inventoryManagement',
        query: {
          materialCode: val.materialCode
        }
      })
    },
    // 重置
    onReset() {
      this.$refs.formInline.resetFields()
      this.onSubmit()
    },
    // 条数
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.onSubmit()
    },
    // 页数
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.onSubmit()
    },
    // 多选
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 初始化
    init() {
      const { staffId, staffName } = this.userInfo
      let params = {
        userId: staffId,
        userName: staffName
      }
      this.$api.getBrandList(params).then((res) => {
        const { code } = res
        if (code === '200') {
          this.brandList = res.data.list
        }
      })
      params = {
        ...params,
        type: 'WZFL'
      }
      this.$api.getHospitalDictList(params).then((res) => {
        const { code } = res
        if (code === '200') {
          this.belongingCategoryList = res.data
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import './styles/index.scss';
.right-bottom-table {
  height: calc(100% - 55px) !important;
  margin-top: 0 !important;
}
</style>
