<!-- 新增设备 -->
<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="form-title"><i class="el-icon-arrow-left" @click="$router.go(-1)"></i>新增/编辑设备</div>
      <div class="content_box">
        <el-form ref="formInline" :model="queryForm" class="form-inline" :rules="rules" label-width="100px">
          <el-form-item label="设备名称" prop="deviceName">
            <el-input v-model.trim="queryForm.deviceName" placeholder="请输入设备名称" maxlength="16"></el-input>
          </el-form-item>
          <el-form-item label="设备编码" prop="deviceCode">
            <el-input v-model.trim="queryForm.deviceCode" placeholder="请输入设备编码" maxlength="16"> </el-input>
          </el-form-item>
          <el-form-item label="设备类型" prop="deviceType">
            <el-select
              v-model="queryForm.deviceType"
              placeholder="请选择设备类型"
              clearable
              @change="(val) => (queryForm.deviceTypeName = deviceTypeList.find((item) => item.id === val).name)"
            >
              <el-option v-for="item in deviceTypeList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="监测系统" prop="projectCode">
            <el-select
              v-model="queryForm.projectCode"
              placeholder="请选择监测系统"
              clearable
              @change="(val) => (queryForm.projectName = sysList.find((item) => item.imhMonitorCode === val).imhMonitorName)"
            >
              <el-option v-for="item in sysList" :key="item.imhMonitorCode" :label="item.imhMonitorName" :value="item.imhMonitorCode"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="安装位置" prop="reginonCode">
            <el-select ref="spaceSelect" v-model="queryForm.reginonCode" placeholder="请选择安装位置" clearable class="spaceTree" @clear="handleClear(1)">
              <el-option hidden :value="queryForm.reginonCode" :label="queryForm.reginonName"> </el-option>
              <el-tree
                :data="serverSpaces"
                :props="serverDefaultProps"
                :load="serverLoadNode"
                lazy
                :expand-on-click-node="false"
                :check-on-click-node="true"
                @node-click="(data) => handleNodeClick(data, 1)"
              >
              </el-tree>
            </el-select>
          </el-form-item>
          <el-form-item label="服务区域" prop="serviceReginonCode">
            <el-select ref="regionSelect" v-model="queryForm.serviceReginonCode" placeholder="请选择服务区域" clearable class="spaceTree" @clear="handleClear(2)">
              <el-option hidden :value="queryForm.serviceReginonCode" :label="queryForm.serviceReginonName"> </el-option>
              <el-tree
                :data="serverSpaces"
                :props="serverDefaultProps"
                :load="serverLoadNode"
                lazy
                :expand-on-click-node="false"
                :check-on-click-node="true"
                @node-click="(data) => handleNodeClick(data, 2)"
              >
              </el-tree>
            </el-select>
          </el-form-item>
          <el-form-item label="监测科室" prop="departmentCode">
            <el-select
              v-model="queryForm.departmentCode"
              placeholder="请选择监测科室"
              clearable
              filterable
              @change="(vla) => (queryForm.departmentName = deptList.find((item) => item.id === vla).deptName)"
            >
              <el-option v-for="item in deptList" :key="item.id" :label="item.deptName" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="抄表类型" prop="recordType">
            <el-select v-model="queryForm.recordType" placeholder="请选择抄表类型" clearable>
              <el-option label="自动" :value="0"> </el-option>
              <el-option label="手动" :value="1"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="queryForm.recordType === 0" label="参数配置" prop="params">
            <el-input v-model="queryForm.paramName" placeholder="请选择参数配置" readonly suffix-icon="el-icon-arrow-down" @focus="() => (isSelectMonitorParma = true)"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <!-- <deptDialog v-if="isDeptDialog" :visible.sync="isDeptDialog" :title="dialogTitle" :nature="'2'" :isNotmultiSector="true" @selectDept="selectDept" /> -->
      <selectMonitorParmaDialog
        v-if="isSelectMonitorParma"
        :dialogShow.sync="isSelectMonitorParma"
        :dialogData="{ eventType: 'add', ...queryForm }"
        @submitDialog="submitMonitorParma"
        @closeDialog="() => (isSelectMonitorParma = false)"
      />
    </div>
    <div slot="footer">
      <el-button type="primary" @click="submitForm()">保存</el-button>
      <el-button type="primary" plain @click="$router.go(-1)">取消</el-button>
    </div>
  </PageContainer>
</template>
<script>
import { transData, ListTree } from '@/util'
export default {
  name: 'addDevice',
  components: {
    selectMonitorParmaDialog: () => import('../components/selectMonitorParmaDialog.vue')
  },
  data() {
    let deviceCodeValidate = (rule, value, callback) => {
      this.$api.meterExistDevice({ deviceCode: value, id: this.$route.query?.type == 'add' ? '' : this.queryForm?.id ?? '' }).then((res) => {
        if (res.code == 200) {
          if (res.data) {
            callback('设备编码已存在')
          } else {
            callback()
          }
        }
      })
    }
    return {
      isSelectMonitorParma: false,
      queryForm: {
        deviceName: '',
        deviceCode: '',
        deviceType: '',
        deviceTypeName: '',
        projectCode: '',
        projectName: '',
        reginonCode: '',
        reginonName: '',
        serviceReginonCode: '',
        serviceReginonName: '',
        recordType: '',
        departmentCode: '',
        departmentName: '',
        paramName: ''
      },
      rules: {
        deviceName: { required: true, message: '请输入设备名称', trigger: 'change' },
        deviceCode: [
          { required: true, message: '请输入设备编码', trigger: 'change' },
          { validator: deviceCodeValidate, trigger: 'blur' }
        ],
        deviceType: {
          required: true,
          message: '请选择设备类型',
          trigger: 'change'
        },
        projectCode: {
          required: true,
          message: '请选择监测系统',
          trigger: 'change'
        },
        recordType: {
          required: true,
          message: '请选择抄表类型',
          trigger: 'change'
        },
        paramName: {
          required: true,
          message: '请选择参数配置',
          trigger: 'change'
        }
      },
      deviceTypeList: [], // 设备类型
      sysList: [], // 监测系统
      deptList: [], // 科室列表
      serverDefaultProps: {
        label: 'ssmName',
        isLeaf: 'leaf',
        children: 'children'
      },
      spaces: [], // 空间位置
      serverSpaces: [] // 空间位置
    }
  },
  computed: {},
  created() {
    this.getSystemList()
    this.getTreelist()
    this.getDeptList()
    this.getDeviceTypeList()
    if (this.$route.query.id && this.$route.query.type == 'edit') {
      this.getMeterDeviceById()
    }
  },
  methods: {
    getMeterDeviceById() {
      this.$api.GetMeterDeviceById({ id: this.$route.query.id }).then((res) => {
        if (res.code == 200) {
          this.queryForm = res.data
        }
      })
    },
    submitMonitorParma(data) {
      Object.assign(this.queryForm, data)
      this.isSelectMonitorParma = false
    },
    submitForm() {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          this.$api[this.$route.query.type == 'edit' ? 'meterEditDevice' : 'meterAddDevice'](this.queryForm)
            .then((res) => {
              if (res.code == 200) {
                this.$message({
                  type: 'success',
                  message: '保存成功'
                })
                this.$router.go('-1')
              } else {
                this.$message({
                  type: 'error',
                  message: res.message || '保存失败'
                })
              }
            })
            .catch(() => {})
        }
      })
    },
    // 获取设备类型
    getDeviceTypeList() {
      this.$api.GetDeviceTypeList().then((res) => {
        if (res.code == '200') {
          this.deviceTypeList = res.data
        }
      })
    },
    // 获取科室
    getDeptList() {
      this.$api.getSelectedDept().then((res) => {
        if (res.code == '200') {
          this.deptList = res.data
        }
      })
    },
    // 获取服务空间树形结构
    getTreelist() {
      this.$api.getStructureTree({}).then((res) => {
        if (res.code == 200) {
          this.spaces = res.data
          // 增加 懒加载节点
          res.data.map((e) => {
            e.leaf = false
          })
          // 转换为树形结构数据
          this.serverSpaces = transData(res.data, 'id', 'pid', 'children')
        }
      })
    },
    spaceTreeChange(value, resolve) {
      const child = ListTree(this.spaces, value.pid)
      child.push(value.id)
      let treeId = child.toString()
      let data = {
        current: 1,
        functionDictId: '',
        simCode: treeId,
        size: 999
      }
      this.$api.getSpaceInfoList(data).then((res) => {
        if (res.code == 200) {
          if (typeof resolve === 'function') {
            let treeNodeData = JSON.parse(JSON.stringify(res.data.records))
            treeNodeData.map((e) => {
              e.leaf = true
            })
            resolve(treeNodeData)
          }
        }
      })
    },
    // 空间数据清除
    handleClear(type) {
      if (type == 1) {
        this.queryForm.reginonCode = ''
        this.queryForm.reginonName = ''
      } else {
        this.queryForm.serviceReginonCode = ''
        this.queryForm.serviceReginonName = ''
      }
    },
    // 选择下拉树 数据
    handleNodeClick(data, type) {
      let code = (data.parentGridIds ? data.parentGridIds : data?.simCode ?? '#') + ',' + data.id
      let name = (data.allSpaceName ? data.allSpaceName : data.simName) + (data.allSpaceName ? '' : '>' + data.ssmName)
      if (type == 1) {
        this.queryForm.reginonCode = code
        this.queryForm.reginonName = name
        this.$refs.spaceSelect.blur()
      } else {
        this.queryForm.serviceReginonCode = code
        this.queryForm.serviceReginonName = name
        this.$refs.regionSelect.blur()
      }
    },
    // 下拉树懒加载
    serverLoadNode(node, resolve) {
      if (node.level == 0) {
        return resolve(node.data)
      } else if (node.level > 0 && node.level < 4) {
        return resolve(node.data.children)
      } else {
        return this.spaceTreeChange(node.data, resolve)
      }
    },
    // 获取监测系统
    getSystemList() {
      let data = {
        projectCodes: ''
      }
      this.$api.getAlarmSystem(data, {}, this.requestHttp).then((res) => {
        if (res.code == 200) {
          this.sysList = res.data
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.container-content > div {
  background-color: #fff;
  height: 100%;
  position: relative;
  .form-title {
    height: 40px;
    line-height: 40px;
    background: #fff;
    border-bottom: 1px solid #dcdfe6;
    font-size: 15px;
    font-family: 'PingFang SC-Medium', 'PingFang SC';
    font-weight: 500;
    color: #121f3e;
    padding-left: 15px;
    i {
      width: 20px;
      cursor: pointer;
    }
  }
  .content_box {
    padding: 16px 24px;
    background: #fff;
    display: flex;
    flex-direction: column;
    height: calc(100% - 50px);
    overflow-y: scroll;
  }
  .form-inline {
    margin-top: 24px;
    .el-input {
      width: 300px;
    }
    .el-select {
      width: 200px;
    }
    .spaceTree {
      width: 350px !important;
    }
  }
  .el-form-item {
    margin-right: 20px;
  }
}
::v-deep .el-input__inner {
  height: 32px !important;
}
</style>
