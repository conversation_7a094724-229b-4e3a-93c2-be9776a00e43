<template>
  <el-dialog title="引用领用申请单" width="50%" v-dialogDrag :visible.sync="sectionDialogShow" custom-class="model-dialog" :before-close="closeDialog">
    <div class="content">
      <div class="search-box">
        <el-input v-model="searchForm.recordNumber" placeholder="计划单号"></el-input>
        <el-date-picker
          v-model="searchForm.applicationTime"
          type="daterange"
          value-format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
        <div class="ml-16">
          <el-button type="primary" @click="search">查询</el-button>
          <el-button type="primary" plain @click="reset">重置</el-button>
        </div>
      </div>
      <div class="sino_table">
        <el-table ref="sinoTable" v-loading="tableLoading" :data="tableData" row-key="id" height="220px" stripe border @row-click="(row) => operating('update', row)">
          <el-table-column prop="status" label="状态" show-overflow-tooltip>
            <template #default="{ row }">
              <span
                :style="{
                  color: row.status == '暂存' ? '#FF9A2E' : '#08CB83'
                }"
                >{{ row.status }}</span
              >
            </template>
          </el-table-column>
          <el-table-column prop="recordNumber" label="计划单号" show-overflow-tooltip></el-table-column>
          <el-table-column prop="receiveTypeName" label="计划类型" show-overflow-tooltip></el-table-column>
          <el-table-column prop="applyCount" label="总数量" show-overflow-tooltip></el-table-column>
          <el-table-column prop="remarks" label="备注" show-overflow-tooltip></el-table-column>
        </el-table>
        <el-pagination
          :current-page="pagination.current"
          :page-sizes="[15, 30, 50, 100]"
          :page-size="pagination.size"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
      <div class="sino_table">
        <el-table ref="hcsTable" v-loading="hcsTableLoadingStatus" :data="hcsTableData" row-key="id" height="180px" stripe border @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" :reserve-selection="true"></el-table-column>
          <el-table-column prop="materialCode" label="危化品编码" show-overflow-tooltip></el-table-column>
          <el-table-column prop="materialName" label="危化品名称" show-overflow-tooltip></el-table-column>
          <el-table-column prop="model" label="规格型号" show-overflow-tooltip></el-table-column>
          <el-table-column prop="basicUnitName" label="采购数量" show-overflow-tooltip></el-table-column>
          <el-table-column prop="operateCount" label="库存数量" show-overflow-tooltip></el-table-column>
          <el-table-column prop="basicUnitName" label="基础单位" show-overflow-tooltip></el-table-column>
        </el-table>
        <el-pagination
          :current-page="hcsPagination.current"
          :page-sizes="[15, 30, 50, 100]"
          :page-size="hcsPagination.size"
          :total="hcsPagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="paginationHcsSizeChange"
          @current-change="paginationHcsCurrentChange"
        ></el-pagination>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="submitDialog">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'selectApplylList',
  components: {},
  props: {
    sectionDialogShow: {
      type: Boolean,
      default: false
    },
    selectList: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      supplierOptions: [], //供应商下拉
      tableLoading: false,
      tableData: [],
      multipleSelection: [],
      // -----------------------------Pagination
      pagination: {
        current: 1,
        size: 15
      }, // 分页数据
      total: 0, // 数据总条数
      searchForm: {
        recordNumber: '', //申请单号
        applicationTime: [] //申请时间
      },
      hcsPagination: {
        current: 1,
        size: 15,
        total: 0
      },
      hcsTableLoadingStatus: false,
      hcsTableData: [] //危化品table
    }
  },
  mounted() {
    this.getTableData()
  },
  methods: {
    search() {
      this.pagination.current = 1
      this.getTableData()
    },
    // 获取列表数据
    getTableData() {
      const userInfo = this.$store.state.user.userInfo.user
      this.tableData = []
      this.tableLoadingStatus = true
      const params = {
        pageSize: this.pagination.size,
        currentPage: this.pagination.current,
        ...this.searchForm,
        userId: userInfo.staffId,
        userName: userInfo.staffName,
        status: "('3','5')"
      }
      params.beginDate = params.applicationTime.length ? params.applicationTime[0] : ''
      params.endDate = params.applicationTime.length ? params.applicationTime[1] : ''
      delete params.applicationTime
      this.$api
        .queryReceiveApplyRecordByPage(params)
        .then((res) => {
          this.tableLoadingStatus = false
          if (res.code === '200') {
            this.tableData = res.data.list
            this.total = res.data.sum
            if (this.tableData && this.tableData.length) {
              this.getHcsInfoData(this.tableData[0].recordNumber)
            } else {
              this.hcsTableData = []
              this.hcsPagination.total = 0
            }
          } else {
            this.tableData = []
            this.total = 0
          }
        })
        .catch((err) => {
          this.tableLoadingStatus = false
        })
    },
    //列表操作
    operating(type, row) {
      if (type === 'update') {
        this.getHcsInfoData(row.recordNumber)
      }
    },
    //获取危化品列表
    getHcsInfoData(recordNumber) {
      const userInfo = this.$store.state.user.userInfo.user
      this.hcsTableData = []
      this.hcsTableLoadingStatus = true
      const params = {
        pageSize: this.hcsPagination.size,
        currentPage: this.hcsPagination.current,
        orderNumber: recordNumber,
        userId: userInfo.staffId,
        userName: userInfo.staffName
      }
      this.$api
        .getHcsRecordList(params)
        .then((res) => {
          this.hcsTableLoadingStatus = false
          if (res.code === '200') {
            this.hcsTableData = res.data.list
            if (this.hcsTableData.length && this.selectList.length) {
              this.selectList.forEach((key) => {
                this.hcsTableData.forEach((row) => {
                  if (row.id == key.id) {
                    this.$refs.hcsTable.toggleRowSelection(row, true)
                  }
                })
              }) //回显选中数据
            }
            this.hcsPagination.total = res.data.sum
          } else {
            this.hcsTableData = []
            this.hcsPagination.total = 0
          }
        })
        .catch((err) => {
          this.hcsTableLoadingStatus = false
        })
    },
    //危化品列表分页
    paginationHcsCurrentChange(val) {
      this.hcsPagination.page = val
      this.getHcsInfoData(this.tableData[0].recordNumber)
    },
    paginationHcsSizeChange(val) {
      this.hcsPagination.size = val
      this.getHcsInfoData(this.tableData[0].recordNumber)
    },
    reset() {
      this.searchForm = {
        recordNumber: '', //计划单号
        applicationTime: '' //时间
      }
      this.pagination.current = 1
      this.getTableData()
    },
    // ---------------------------------------------------------- TabelFn
    handleSizeChange(val) {
      this.pagination.current = 1
      this.pagination.size = val
      this.getTableData()
    },
    handleCurrentChange(val) {
      this.pagination.current = val
      this.getTableData()
    },
    closeDialog() {
      this.$emit('closeSectionDialog')
    },
    submitDialog() {
      this.$emit('submitSectionDialog', this.multipleSelection)
    },
    //table选择
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    //危化品列表分页
    paginationMaterialsCurrentChange(val) {
      this.materialsPagination.page = val
    },
    paginationAssetsSizeChange(val) {
      this.materialsPagination.size = val
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .model-dialog {
  margin-top: 10vh !important;
  .content {
    margin: 0 auto;
    background: #fff;
    border-radius: 4px;
    width: 100%;
    height: 100%;
    padding: 16px;
  }
  .search-box {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    .el-input {
      width: 180px;
      margin-right: 16px;
    }
  }
  .ml-16 {
    margin-left: 20px;
  }
  .el-pagination {
    margin: 10px 0;
    width: 100%;
  }
}
</style>

