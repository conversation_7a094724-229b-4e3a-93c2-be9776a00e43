<!-- 监测项统计弹窗 -->
<template>
  <el-dialog v-if="visible" v-dialogDrag :modal="false" :close-on-click-modal="false" :title="title" width="60%"
    :visible.sync="visible" custom-class="model-dialog" :before-close="closeDialog">
    <div class="content">
      <div class="content-heade">
        <el-button class="btn-item" :class="{ 'btn-active': searchFrom.timeType == 0 }" plain
          @click="timeTypeChange(0)">今日</el-button>
        <el-button class="btn-item" :class="{ 'btn-active': searchFrom.timeType == 1 }" plain
          @click="timeTypeChange(1)">本月</el-button>
        <el-button class="btn-item" :class="{ 'btn-active': searchFrom.timeType == 2 }" plain
          @click="timeTypeChange(2)">本年</el-button>
        <el-button class="btn-item" :class="{ 'btn-active': searchFrom.timeType == 3 }" plain
          style="margin-right: 10px;" @click="timeTypeChange(3)">自定义</el-button>
        <el-date-picker v-model="searchFrom.dataRange" type="daterange" unlink-panels
          :disabled="searchFrom.timeType != 3" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
          format="yyyy-MM-dd" value-format="yyyy-MM-dd" :clearable="false" />
        <div style="display: inline-block;">
          <el-button v-auth="'userManagement:reset'" type="primary" plain @click="resetForm">重置</el-button>
          <el-button v-auth="'userManagement:search'" type="primary" @click="searchForm">查询</el-button>
        </div>
      </div>
      <div v-loading="loading" class="content-main">
        <ContentCard class="content-left" :title="'设备类型（' + (type == 1 || type == 2 ? '次' : 'h') + '）'">
          <echarts slot="content" ref="deviceType" domId="deviceType" />
        </ContentCard>
        <ContentCard class="content-right" title="单设备">
          <div slot="content" class="card-content">
            <el-select v-model="searchFrom.menuCode" placeholder="请选择设备" clearable @change="() => getDataList()">
              <el-option label="全部" value=""> </el-option>
              <el-option v-for="item in entityList" :key="item.code" :label="item.name" :value="item.code"></el-option>
            </el-select>
            <div class="card-content-table table-content">
              <el-table ref="table" :resizable="false" border :data="tableData" :height="tableHeight"
                style="width: 100%;">
                <el-table-column v-for="item in getColumn" :key="item.prop" :prop="item.prop" :label="item.label"
                  :width="item.width" show-overflow-tooltip>
                  <!-- <template slot-scope="scope">{{ scope.row.alarmStartTime }}</template> -->
                </el-table-column>
                <el-table-column label="图表" width="120">
                  <template slot-scope="scope">
                    <el-progress v-if="type == 1 || type == 2"
                      :percentage="(scope.row?.count ?? 0 / tableData[0]?.count ?? 0) * 100" :show-text="false"
                      color="#3562DB" />
                    <el-progress v-else
                      :percentage="(scope.row?.runningHour ?? 0 / tableData[0]?.runningHour ?? 0) * 100"
                      :show-text="false" color="#3562DB" />
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="card-content-footer">
              <el-pagination :current-page="pagination.current" :page-sizes="pagination.pageSizeOptions"
                :page-size="pagination.size" :layout="pagination.layoutOptions" :total="pagination.total"
                @size-change="paginationSizeChange" @current-change="paginationCurrentChange">
              </el-pagination>
            </div>
          </div>
        </ContentCard>
      </div>
    </div>
    <span slot="footer">
      <el-button plain @click="closeDialog">关 闭</el-button>
    </span>
  </el-dialog>
</template>
<script>
import chartMixin from '../mixins/chartMixin'
import tableListMixin from '@/mixins/tableListMixin.js'
import moment from 'moment'
moment.locale('zh-cn')
export default {
  name: 'monitorStatisticsDialog',
  mixins: [chartMixin, tableListMixin],
  props: {
    title: {
      type: String,
      default: ''
    },
    visible: {
      type: Boolean,
      default: false
    },
    type: {
      type: [Number, String],
      default: 1
    },
    projectCode: {
      type: String,
      default: ''
    },
    entityList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      searchFrom: {
        timeType: 0,
        menuCode: '', // 设备
        // dataRange: [moment(new Date().setTime(new Date().getTime() - 3600 * 1000 * 24 * 30)).format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')] // 时间范围
        dataRange: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')] // 时间范围
      }
    }
  },
  computed: {
    getColumn() {
      if (this.type == 1) {
        return [
          {
            label: '名称',
            prop: 'surveyName',
            width: ''
          },
          {
            label: '故障次数统计（次）',
            prop: 'count',
            width: 150
          },
          {
            label: '排行',
            prop: 'sort',
            width: 60
          }
        ]
      } else if (this.type == 2) {
        return [
          {
            label: '名称',
            prop: 'surveyName',
            width: ''
          },
          {
            label: '离线次数统计（次）',
            prop: 'count',
            width: 150
          },
          {
            label: '排行',
            prop: 'sort',
            width: 60
          }
        ]
      } else {
        return [
          {
            label: '名称',
            prop: 'surveyName',
            width: ''
          },
          {
            label: '运行时长（h）',
            prop: 'runningHour'
          },
          {
            label: '运行率（%）',
            prop: 'runningRatio'
          },
          {
            label: '排行',
            prop: 'sort',
            width: 60
          }
        ]
      }
    }
  },
  created() {
    this.$nextTick(() => {
      this.getDataList()
    })
  },
  mounted() { },
  methods: {
    // 日期选择
    timeTypeChange(type) {
      let newObj = {
        0: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        1: [moment().startOf('month').format('YYYY-MM-DD'), moment().endOf('month').format('YYYY-MM-DD')],
        2: [moment().startOf('year').format('YYYY-MM-DD'), moment().endOf('year').format('YYYY-MM-DD')],
        3: []
      }
      this.searchFrom.dataRange = newObj[type]
      this.searchFrom.timeType = type
    },
    // 查看详情
    getDataList(id) {
      let params = {
        timeType: this.searchFrom.timeType,
        menuCode: this.searchFrom.menuCode,
        startTime: this.searchFrom.dataRange[0],
        endTime: this.searchFrom.dataRange[1],
        projectCode: this.projectCode,
        page: this.pagination.current,
        pageSize: this.pagination.size,
        groupId: id || ""
      }
      if (this.type == 1 || this.type == 2) {
        params.faultOrOffline = this.type - 1
      } else {
        params.entityTypeId = this.type
      }
      let newArr = []
      this.loading = true
      this.$api.GetStatisticsInfo(params).then((res) => {
        this.loading = false
        if (res.code == 200) {
          (res.data?.list ?? []).forEach((item) => {
            newArr.push({
              name: item.surveyName,
              value: this.type == 1 || this.type == 2 ? item.count : item.runningHour
            })
          })
          this.tableData = res.data?.list ?? []
          this.pagination.total = res.data?.totalCount ?? 0
          this.$refs.deviceType.init(this.setPieChart(this.type == 1 || this.type == 2 ? '次' : 'h', newArr))
        } else {
          this.$refs.deviceType.init(this.setPieChart())
        }
      }).catch(() => {
        this.loading = false
        this.$refs.deviceType.init(this.setPieChart())
      })
    },
    // 重置查询
    resetForm() {
      Object.assign(this.$data.searchFrom, this.$options.data().searchFrom)
      this.searchForm()
    },
    // 查询
    searchForm() {
      this.getDataList()
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', !this.visible)
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 0;
  height: calc(78vh - 110px);
}

.content {
  width: 100%;
  height: 100%;

  .content-heade {
    padding: 10px;
    background: #fff;

    &>div {
      margin-right: 10px;
    }

    .btn-item {
      border: 1px solid #3562db;
      color: #3562db;
      font-family: none;
    }

    .btn-active {
      color: #fff;
      background: #3562db;
    }
  }

  .content-main {
    width: 100%;
    height: calc(100% - 52px);
    padding: 15px 10px;
    display: flex;
    justify-content: space-between;
  }

  .content-left {
    height: 100%;
    width: calc(50% - 8px);
    margin-right: 8px;
  }

  .content-right {
    height: 100%;
    width: calc(50% - 8px);
    margin-left: 8px;

    .card-content {
      height: 100%;
      padding: 0 0 0 4px;
      display: flex;
      flex-direction: column;

      .card-content-table {
        flex: 1;
        overflow: auto;
        margin-top: 16px;

        ::v-deep .el-progress {
          .el-progress-bar__outer {
            border-radius: 0;
            height: 8px !important;
          }

          .el-progress-bar__inner {
            border-radius: 0;
          }
        }
      }

      .card-content-footer {
        padding: 10px 0 0;
      }
    }
  }
}

.model-dialog {
  padding: 0 !important;
}
</style>
