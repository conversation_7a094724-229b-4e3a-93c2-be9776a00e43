<template>
  <PageContainer>
    <div slot="header" class="control-btn-header">
      <el-tabs v-model="activeTabs">
        <el-tab-pane label="智能预案" name="0"></el-tab-pane>
        <el-tab-pane label="常规预案" name="1"></el-tab-pane>
        <el-tab-pane label="废除预案" name="2"></el-tab-pane>
      </el-tabs>
      <div class="search-from">
        <el-input v-model="searchFrom.planName" placeholder="预案名称" clearable style="width: 200px"></el-input>
        <el-select v-model="searchFrom.planType" placeholder="预案类型" clearable @change="getDictionaryList">
          <el-option v-for="item in planTypeList" :key="item.thirdSystemCode" :label="item.thirdSystemName" :value="item.thirdSystemCode"></el-option>
        </el-select>
        <el-select v-model="searchFrom.alarmType" placeholder="报警类型" clearable :disabled="!searchFrom.planType">
          <el-option v-for="item in alarmTypeList" :key="item.id" :label="item.alarmDictName" :value="item.id"></el-option>
        </el-select>
        <el-cascader
          v-model="searchFrom.spaceType"
          placeholder="空间类型"
          filterable
          :options="spaceTypeList"
          :props="{ value: 'id', label: 'name' }"
          collapse-tags
          clearable
        ></el-cascader>
        <div style="display: inline-block">
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="searchForm">查询</el-button>
          <el-button
            v-if="activeTabs != 2"
            style="position: absolute; right: 0px; top: calc(50% + 5px); transform: translateY(-50%)"
            type="primary"
            icon="el-icon-plus"
            @click="control('isAdd')"
          >
            {{ activeTabs == 0 ? '创建智能预案' : '创建常规预案' }}
          </el-button>
        </div>
        <el-image-viewer v-if="showViewer" :on-close="() => (showViewer = false)" :url-list="iconPathList" />
        <selectTemplate v-if="isSelectTemplate" :visible.sync="isSelectTemplate" @saveTemplate="(val) => control(val ? 'copy' : 'add', { id: val })" />
      </div>
    </div>
    <div slot="content" class="table-content">
      <TablePage
        ref="table"
        v-loading="tableLoading"
        :showPage="true"
        :tableColumn="tableColumn"
        :data="tableData"
        height="calc(100% - 40px)"
        :pageData="pageData"
        :pageProps="pageProps"
        @pagination="paginationChange"
      >
        <template slot="empty">
          <img src="../../../../assets/images/operationPort/yuan-empty.png" style="width: 307px; height: 226px" />
          <p style="color: #ccced3; margin: 24px 0px 38px 0px; line-height: 16px">
            {{ activeTabs == 0 ? '暂无智能预案，请创建智能预案' : activeTabs == 1 ? '暂无常规预案，请创建常规预案' : '暂无废除预案' }}
          </p>
          <!-- <el-button v-if="activeTabs != 2" type="primary" icon="el-icon-plus" @click="control('add')">{{ activeTabs == 0 ? '创建智能预案' : '创建常规预案' }}</el-button> -->
          <el-button v-if="activeTabs != 2" type="primary" icon="el-icon-plus" @click="control('isAdd')">{{ activeTabs == 0 ? '创建智能预案' : '创建常规预案' }}</el-button>
        </template>
      </TablePage>
    </div>
  </PageContainer>
</template>
<script lang="jsx">
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import selectTemplate from '../components/selectTemplate.vue'
export default {
  name: 'planList',
  components: {
    ElImageViewer,
    selectTemplate
  },
  beforeRouteEnter(to, from, next) {
    let names = []
    to.matched.map((v, i) => {
      if (i > 0) {
        v.components.default.name && names.push(v.components.default.name)
      }
    })
    // 进入页面时，先将当前页面的 name 信息存入 keep-alive 全局状态
    setTimeout(() => {
      next((vm) => {
        vm.$store.commit('keepAlive/add', names)
      })
    }, 0)
  },
  async beforeRouteLeave(to, from, next) {
    // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
    if (!['createPlan', 'planDetails'].includes(to.name)) {
      // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      activeTabs: '0',
      isSelectTemplate: false,
      tableLoading: false,
      showViewer: false,
      iconPathList: [], // 图片列表
      planTypeList: [], // 预案类型列表
      alarmTypeList: [], // 报警类型列表
      spaceTypeList: [], // 空间类型列表
      searchFrom: {
        planName: '',
        planType: '',
        alarmType: '',
        spaceType: []
      },
      tableColumn: [
        {
          prop: 'icon',
          label: '',
          width: 100,
          render: (h, row) => {
            const flowData = JSON.parse(row.row.regulationsFlow || '[]')
            const imageUrl = flowData[0]?.url
            if (!imageUrl) return <div></div>
            return <img style="width:48px; height:48px;cursor: pointer;" src={this.$tools.imgUrlTranslation(imageUrl)} onClick={() => this.viewImage(row.row)}></img>
          }
        },
        {
          prop: 'planName',
          label: '预案名称'
        },
        {
          width: 120,
          prop: 'planType',
          label: '预案类型',
          formatter: (scope) => {
            return scope.row.planType ? this.planTypeList.find((v) => v.thirdSystemCode == scope.row.planType)?.thirdSystemName : ''
          }
        },
        {
          prop: 'alarmTypeName',
          label: '报警类型'
        },
        {
          prop: 'spaceTypeName',
          label: '空间类型'
        },
        // {
        //   prop: 'type',
        //   label: '审核状态'
        // },
        {
          prop: 'versionNo',
          label: '版本号'
        },
        {
          width: 180,
          prop: 'operation',
          label: '操作',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span class="operationBtn-span" style="color: #3562DB" onClick={() => this.control('detail', row.row)}>
                  查看
                </span>
                {this.activeTabs == 0 ? (
                  <span class="operationBtn-span" style="color: #3562DB" onClick={() => this.control('preview', row.row)}>
                    预览
                  </span>
                ) : (
                  ''
                )}
                {this.activeTabs != 2 ? (
                  <span class="operationBtn-span" style="color: #3562DB" onClick={() => this.control('edit', row.row)}>
                    编辑
                  </span>
                ) : (
                  ''
                )}
                {this.activeTabs == 1 ? (
                  <span class="operationBtn-span" style="color: #3562DB" onClick={() => this.control('download', row.row)}>
                    下载
                  </span>
                ) : (
                  ''
                )}
                {this.activeTabs == 1 ? (
                  <span class="operationBtn-span" style="color: #3562DB" onClick={() => this.control('repeal', row.row)}>
                    废除
                  </span>
                ) : (
                  ''
                )}
                {this.activeTabs == 0 ? (
                  <el-dropdown onCommand={(val) => this.control(val, row.row)}>
                    <span class="el-dropdown-link" style="color: #3562DB">
                      · · ·
                    </span>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item command="repeal">废除</el-dropdown-item>
                      <el-dropdown-item command="download">下载</el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                ) : (
                  ''
                )}
              </div>
            )
          }
        }
      ],
      tableData: [],
      pageData: {
        current: 1,
        size: 15,
        total: 0
      },
      pageProps: {
        page: 'current',
        pageSize: 'size',
        total: 'total'
      }
    }
  },
  watch: {
    activeTabs() {
      this.getPlanList()
    }
  },
  mounted() {
    this.getPlanList()
    this.getAlarmSystem()
    this.getSuperiorData()
  },
  activated() {
    this.searchForm()
  },
  methods: {
    // 查看图片
    viewImage(row) {
      let icon = JSON.parse(row.regulationsFlow)
      if (!icon.length) {
        this.$message.info('当前没有可预览的图片')
        return
      }
      this.iconPathList = [this.$tools.imgUrlTranslation(icon[0].url)]
      this.showViewer = true
    },
    // 查询
    searchForm() {
      this.getPlanList()
    },
    // 重置查询
    resetForm() {
      Object.assign(this.$data.searchFrom, this.$options.data().searchFrom)
      this.searchForm()
    },
    control(type, row) {
      if (type == 'isAdd') {
        // 添加弹窗
        this.isSelectTemplate = true
      } else if (['add', 'edit', 'copy', 'preview'].includes(type)) {
        // 添加 编辑 复制 预览
        let query = type !== 'add' ? { id: row?.id ?? '' } : {}
        this.$router.push({
          path: '/planManage/planList/createPlan',
          query: {
            type,
            planType: this.activeTabs,
            ...query
          }
        })
      } else if (type == 'detail') {
        // 详情
        this.$router.push({
          path: '/planManage/planList/planDetails',
          query: {
            type,
            planType: this.activeTabs,
            id: row?.id ?? ''
          }
        })
      } else if (type == 'repeal') {
        // 废除
        this.$confirm('确认要废除当前预案吗？废除后数据不可恢复。', '废除预案', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '废除预案',
          cancelButtonText: '取消操作',
          type: 'warning'
        }).then(() => {
          this.$api.RepealPlan({ id: row.id, planSource: row.planSource }, { 'operation-type': 3, 'operation-name': row.planName, 'operation-id': row.id }).then((res) => {
            if (res.code == 200 && res.data) {
              this.$message({ message: '预案废除成功', type: 'success' })
              this.isLastPage(1)
              this.searchForm()
            } else {
              this.$message({ message: res.msg, type: 'error' })
            }
          })
        })
      } else if (type == 'download') {
        // 下载
        const a = document.createElement('a')
        const name = JSON.parse(row.regulationsDoc)[0]?.name ?? ''
        const url = this.$tools.imgUrlTranslation(JSON.parse(row.regulationsDoc)[0]?.url ?? '')
        // 这里是将url转成blob地址，
        fetch(url)
          .then((res) => res.blob())
          .then((blob) => {
            // 将链接地址字符内容转变成blob地址
            a.href = URL.createObjectURL(blob)
            a.download = name || '' // 下载文件的名字
            document.body.appendChild(a)
            a.click()
            // 在资源下载完成后 清除 占用的缓存资源
            window.URL.revokeObjectURL(a.href)
            document.body.removeChild(a)
          })
      }
    },
    // 获取预案列表
    getPlanList() {
      let param = {
        ...this.searchFrom,
        planCategory: this.activeTabs,
        spaceType: this.searchFrom.spaceType.join(','),
        pageSize: this.pageData.size,
        page: this.pageData.current
      }
      this.tableLoading = true
      this.$api
        .GetPlanList(param)
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.records
            this.pageData.total = res.data.total
          }
          this.tableLoading = false
        })
        .catch(() => {
          this.tableLoading = false
        })
    },
    // 判断当前页是否是最后一页
    isLastPage(deleteNum) {
      let deleteAfterPage = Math.ceil((this.pageData.total - deleteNum) / this.pageData.size)
      let currentPage = this.pageData.current > deleteAfterPage ? deleteAfterPage : this.pageData.current
      this.pageData.current = currentPage < 1 ? 1 : currentPage
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getPlanList()
    },
    // 获取空间类型字典
    getSuperiorData() {
      this.$api.GetSuperiorData({ dictionaryCategoryId: 'SPACE_FUNCTION' }).then((res) => {
        if (res.code == 200) {
          this.spaceTypeList = this.handleTreeList(res.data[0].children)
        }
      })
    },
    handleTreeList(list) {
      for (var i = 0; i < list.length; i++) {
        if (list[i].children.length < 1) {
          list[i].children = undefined
        } else {
          this.handleTreeList(list[i].children)
        }
      }
      return list
    },
    // 获取预案类型
    getAlarmSystem() {
      this.$api.getAlarmThirdSystemData().then((res) => {
        if (res.code == 200) {
          this.planTypeList = res.data
        }
      })
    },
    // 获取报警类型
    getDictionaryList(val) {
      this.searchFrom.alarmType = ''
      this.$api.getAlarmThirdTypeData({ thirdSystemCode: val }).then((res) => {
        if (res.code == 200) {
          this.alarmTypeList = res.data
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.control-btn-header {
  padding: 0px 10px 10px 10px !important;
  .search-from {
    padding-right: 180px;
    position: relative;
    & > div {
      margin-top: 10px;
      margin-right: 10px;
    }
  }
}
.table-content {
  margin-top: 15px;
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 15px);
  padding: 10px;
}
</style>
<style lang="scss">
.twoLinesEllipsis {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.operationBtn-span {
  margin-right: 10px;
}
</style>
