<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <el-form ref="form" label-position="right" label-width="80px" :model="formData" :rules="rules">
        <ContentCard title="文档信息">
          <div slot="content" class="footer-role">
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="8">
                <el-form-item label="文档名称" prop="archiveName">
                  <el-input v-model="formData.archiveName" placeholder="请输入文档名称" maxlength="50"
                    show-word-limit></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="8">
                <el-form-item label="位置" prop="folderId">
                  <el-cascader v-model="formData.folderId" :options="options"
                    :props="{ emitPath: false, checkStrictly: true, children: 'children', label: 'label', value: 'id' }"
                    clearable></el-cascader>
                </el-form-item>
              </el-col>
              <el-col :md="24">
                <el-form-item label="存放位置">
                  <el-input v-model="formData.saveLocation" type="textarea" maxlength="200" show-word-limit
                    placeholder="请输入存放位置"></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="24">
                <el-form-item label="摘要信息">
                  <el-input v-model="formData.remark" type="textarea" maxlength="200" show-word-limit
                    placeholder="请输入摘要信息"></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="24">
                <el-form-item label="附件">
                  <el-upload class="upload-demo" :action="''" :on-success="handleOnSuccess"
                    :before-upload="beforeUpload" multiple :http-request="httpRequset" :show-file-list="false">
                    <el-button size="small" type="secondary">
                      <em class="el-icon-upload2"></em>
                      点击上传
                    </el-button>
                    <div slot="tip" class="el-upload__tip">文件大小限制20M</div>
                  </el-upload>
                </el-form-item>
              </el-col>
            </el-row>
            <div class="tableBox">
              <el-table :data="formData.archiveFileList" highlight-current-row>
                <el-table-column label="序号" type="index" width="50"> </el-table-column>
                <el-table-column property="fileName" label="文件名"> </el-table-column>
                <el-table-column property="fileSize" label="大小（M）"> </el-table-column>
                <el-table-column property="option" label="操作" width="120">
                  <template slot-scope="scope">
                    <el-button type="text" @click="handleDelete(scope)"> 删除 </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </ContentCard>
      </el-form>
    </div>
    <div slot="footer">
      <el-button type="primary" @click="handleSave" :loading="saveLoding">保存</el-button>
      <el-button type="primary" plain @click="$router.go(-1)">取消</el-button>
    </div>
  </PageContainer>
</template>
<script>
import mixins from '@/views/operationPort/dossierManager/mixins/index'
import dictMixin from '@/views/operationPort/contractManagement/mixins/index.js'
export default {
  name: 'addDoc',
  mixins: [mixins, dictMixin],
  data() {
    return {
      minioApiFile: {},
      rules: {
        folderId: [{ required: true, message: '请选择位置', trigger: ['change', 'blur'] }],
        archiveName: [
          {
            required: true,
            message: '请输入文档名称',
            trigger: ['change', 'blur']
          }
        ],
      },
      formData: {
        archiveName: '',
        folderId: '',
        saveLocation: '',
        remark: '',
        archiveFileList: []
      },
      archiveModelOptions: [
        {
          label: '安全管理制度',
          value: '1'
        }
      ],
      options: [],
      saveLoding: false,
    }
  },
  computed: {
    Authorization() {
      return getToken()
    }
  },
  created() {
    this.handleGetTreeData()
    if (this.$route.query.id) {
      this.handleGetData()
    }
  },
  methods: {
    handleGetData() {
      const { id } = this.$route.query
      this.$api.fileManagement.getById({ id }).then((res) => {
        this.formData = res.data
      })
    },
    handleSave() {
      this.$refs.form.validate((res) => {
        if (res) {
          this.saveLoding = true
          const { id, type } = this.$route.query
          let fn = type === 'add' ? 'insertArchiveB' : 'updateFile'
          let params = {
            ...this.formData,
            archiveType: '4',
          }
          if (type === 'edit') {
            params.archiveId = id
            params.archiveDate = this.formData.createTime
          }
          this.$api.fileManagement[fn](params).then((res) => {
            this.saveLoding = false
            if (res.code === '200') {
              this.$message.success('保存成功')
              this.handleBack()
            } else {
              this.$message.error('保存失败')
            }
          })
        }
      })
    },
    /**
     * @description 递归删除值为空数组的child属性，避免级联选择器显示后一级空列表
     * @param {Array} optionList 数据源
     * @param {String} childName 需要删除的child属性名 如 chhildren  childList等等
     */
    handleRemoveEmptyChild(optionList, childName) {
      for (let i = 0; i < optionList.length; i++) {
        if (optionList[i][childName].length !== 0) {
          this.handleRemoveEmptyChild(optionList[i][childName], childName)
        } else {
          delete optionList[i][childName]
        }
      }
    },
    handleGetTreeData() {
      this.$api.fileManagement.selectFolderTree({ folderType: '4', isMine: false }).then((res) => {
        this.handleRemoveEmptyChild(res.data, 'children')
        this.options = res.data
      })
    },
    handleBack() {
      this.$router.go(-1)
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep {
  .container-content {
    overflow: auto !important;
    .box-card:first-child {
      padding-bottom: 0px;
    }
    .box-card:last-child {
      padding-top: 0px;
    }
  }
  .el-date-editor,
  .el-cascader {
    width: 100%;
  }
}
.tableBox {
  padding-left: 80px;
}
::v-deep .upload-demo {
  display: flex;
  .el-upload__tip {
    margin-top: 0;
    margin-left: 8px;
    color: #8c8c8c;
  }
}
::v-deep .el-select {
  width: 100%;
}
</style>
