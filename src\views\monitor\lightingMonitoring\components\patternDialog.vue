<template>
  <el-dialog
    v-if="patternDialogShow"
    :title="patternDialogType == 'add' ? '新建模式表' : '编辑模式表'"
    :visible.sync="patternDialogShow"
    custom-class="pattern-dialog"
    :before-close="closeDialog"
  >
    <div class="patternForm_content" style="padding: 10px 20px 10px 10px;">
      <div class="form_row">
        <div class="form_row_label">模式名称：</div>
        <div class="form_row_input">
          <el-input v-model="patternForm.patternName" placeholder="请输入模式名称"></el-input>
        </div>
      </div>
      <div class="form_row">
        <div class="form_row_label">天气类型：</div>
        <div class="form_row_input">
          <div
            v-for="(item, index) in patternTypeList"
            :key="index"
            class="weather_tag"
            :class="patternForm.patternType == item.dictId ? 'active_tag' : ''"
            @click="patternForm.patternType = item.dictId"
          >
            {{ item.dictName }}
          </div>
        </div>
      </div>
      <div class="form_row">
        <div class="form_row_label">备注：</div>
        <div class="form_row_input">
          <el-input v-model="patternForm.remarks" placeholder="请输入备注"></el-input>
        </div>
      </div>
      <div class="form_row">
        <div class="form_row_label">取色：</div>
        <div class="form_row_input">
          <el-input v-model="patternForm.patternColour" style="width: 60%; margin-right: 10px;" readonly="" placeholder="请选择颜色"></el-input>
          <el-color-picker v-model="patternForm.patternColour" popper-class="color_picker"></el-color-picker>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="groupSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import Dict from './dict.js'
export default {
  name: 'patternDialog',
  props: {
    patternDialogShow: {
      type: Boolean,
      default: false
    },
    patternDialogType: {
      type: String,
      default: 'add'
    },
    dialogData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    projectCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      patternTypeList: Dict.patternTypeList,
      patternForm: {
        patternName: '',
        patternType: '1',
        patternColour: '',
        remarks: ''
      }
    }
  },
  mounted() {
    if (this.patternDialogType !== 'add') {
      this.patternForm = JSON.parse(JSON.stringify(this.dialogData))
      delete this.patternForm.patternSrc
      delete this.patternForm.patternActiveSrc
    } else {
    }
  },
  methods: {
    closeDialog() {
      this.$emit('closePatternDialog')
    },
    groupSubmit() {
      this.$api
        .saveWeatherPattern({
          ...this.patternForm,
          projectCode: this.projectCode
        })
        .then((res) => {
          console.log(res)
          if (res.code == 200) {
            this.$message({ type: 'success', message: res.message })
            this.$emit('patternSubmit', true)
          }
          // else{
          //   this.$message({ type: "error", message: res.message });
          // }
        })
    }
  }
}
</script>
<style lang="scss">
.pattern-dialog {
  width: 30% !important;
  height: 30% !important;
  min-width: 562px !important;
  min-height: 376px !important;

  .patternForm_content {
    .form_row {
      display: flex;
      height: 40px;
      line-height: 40px;
      padding-right: 30px;
      margin-bottom: 10px;

      .form_row_label {
        min-width: 100px;
        text-align: right;
        margin-right: 10px;
        font-size: 14px;
        font-family: NotoSansHans-Medium, NotoSansHans;
        color: #606266;
      }

      .form_row_input {
        flex: 1;
        display: flex;

        .weather_tag {
          padding: 5px 10px;
          background: #f5f5fa;
          border-radius: 2px;
          font-size: 14px;
          color: #909399;
          height: 30px;
          line-height: 20px;
          margin: 0 5px;
          margin-top: 6px;
          cursor: pointer;
          border: 1px solid #f5f5fa;
        }

        .active_tag {
          background: #f1f6ff;
          box-sizing: border-box;
          border-color: #5188fc;
          color: #5188fc;
        }
      }
    }
  }
}

.color_picker {
  .el-button {
    padding: 7px 10px;
    min-width: 0;
    height: auto;
    border: 1px solid #dcdfe6;
    font-weight: 500;
    font-size: 12px;
  }

  .el-button--text {
    border-color: transparent;
  }
}
</style>
