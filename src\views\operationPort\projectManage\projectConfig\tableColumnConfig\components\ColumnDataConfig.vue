<script>
import { UsingStatusOptions, YesNoOptions } from '@/views/operationPort/constant'
const DICT_TYPE = 2
export default {
  name: 'ColumnDataConfig',
  props: {
    visible: Boolean,
    fieldData: Object
  },
  events: ['update:visible', 'update'],
  data: function() {
    return {
      formModel: {
        dataSource: '1',
        fieldCode: ''
      },
      rules: {
        fieldCode: [{ required: true, message: '请选择表单项' }]
      },
      // 当前缓存的业务表单字段列表对应的formId
      listFormId: '',
      // 业务表单字段列表
      formFieldList: []
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    },
    toRevertData() {
      return this.dialogVisible && this.fieldData && typeof this.fieldData === 'object'
    }
  },
  watch: {
    // 监听是否反显数据
    toRevertData(value) {
      if (!value) return
      this.formModel.fieldCode = this.fieldData.code
      // 如果当前组件暂存的时这个业务表单对应的字段列表则不用重新获取
      if (this.listFormId !== this.fieldData.formId) {
        this.listFormId = ''
        this.getFieldList()
      }
    }
  },
  methods: {
    onDialogClosed() {
      this.$refs.formRef.resetFields()
    },
    // 获取流程表单字段列表
    getFieldList() {
      const id = this.fieldData.businessFormId
      this.$api.SporadicProject.queryProjectBusinessFormDetail({ id }).then((res) => {
        if (res.code === '200') {
          this.listFormId = id
          this.formFieldList = res.data.detailList
        }
      })
    },
    // 点击提交
    onSubmit() {
      this.$refs.formRef.validate().then(() => {
        // 查找表单项列表，获取字段名称
        const flowInfo = this.formFieldList.find((it) => it.operationCode === this.formModel.fieldCode) ?? {}
        // 要更新的内容
        const params = {
          name: flowInfo.operationName,
          code: this.formModel.fieldCode
        }
        // 与原始行数据合并后为最新数据
        const newData = Object.assign({}, this.fieldData, params)
        this.dialogVisible = false
        this.$emit('update', newData)
      })
    }
  }
}
</script>
<template>
  <el-dialog
    v-dialogDrag
    class="component column-data-config"
    title="编辑数据"
    width="750px"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    custom-class="model-dialog"
    :modal="false"
    @closed="onDialogClosed"
  >
    <el-form ref="formRef" :model="formModel" :rules="rules" label-width="95px">
      <el-row :gutter="12">
        <el-col :span="12">
          <el-form-item label="数据来源" prop="dataSource">
            <el-select v-model="formModel.dataSource" placeholder="请选择">
              <el-option value="1" label="业务表单"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col></el-col>
        <el-col :span="12">
          <el-form-item label="表单项" prop="fieldCode">
            <el-select v-model="formModel.fieldCode" placeholder="请选择" filterable clearable>
              <el-option v-for="item of formFieldList" :key="item.id" :value="item.operationCode" :label="item.operationName"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="Key">
            <el-input :value="formModel.fieldCode" disabled></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button type="primary" plain @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="onSubmit">确认</el-button>
    </template>
  </el-dialog>
</template>
<style lang="scss">
.component.column-data-config {
  .el-form {
    background-color: #fff;
    padding: 10px 16px 0;
    .el-form-item {
      .el-select {
        width: 100%;
      }
    }
  }
}
</style>
