import Layout from '@/layout'
import EmptyLayout from '@/layout/empty'
export default [
  {
    path: '/spaceManage',
    component: Layout,
    redirect: '/spaceManage/spatialChangeRecord',
    name: 'spaceManage',
    meta: {
      title: '建筑空间管理',
      menuAuth: '/spaceManage'
    },
    children: [
      {
        path: 'spatialChangeRecord',
        component: EmptyLayout,
        redirect: { name: 'spatialChangeRecord' },
        meta: {
          title: '空间变更记录',
          menuAuth: '/organizationManage/spatialChangeRecord'
        },
        children: [
          {
            path: '',
            name: 'spatialChangeRecord',
            component: () => import('@/views/operationPort/spaceManage/spatialChangeRecord.vue'),
            meta: {
              title: '空间变更记录',
              sidebar: false,
              breadcrumb: false,
              avtiveMenu: '/spaceManage/spatialChangeRecord'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/spaceUsage',
    component: Layout,
    redirect: '/spaceUsage/spaceInventory',
    name: 'spaceUsage',
    meta: {
      title: '空间使用管理',
      menuAuth: '/spaceUsage'
    },
    children: [
      {
        path: 'spaceInventory',
        component: EmptyLayout,
        redirect: { name: 'spaceInventory' },
        meta: {
          title: '空间盘点',
          menuAuth: '/spaceUsage/spaceInventory'
        },
        children: [
          {
            path: '',
            name: 'spaceInventory',
            component: () => import('@/views/operationPort/spaceUsage/spaceInventory/index.vue'),
            meta: {
              title: '空间盘点',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/spaceUsage/spaceInventory'
            }
          },
          {
            path: 'addInventory',
            name: 'addInventory',
            component: () => import('@/views/operationPort/spaceUsage/spaceInventory/addInventory.vue'),
            meta: {
              title: '新增盘点',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/spaceUsage/spaceInventory'
            }
          },
          {
            path: 'inventoryDetails',
            name: 'inventoryDetails',
            component: () => import('@/views/operationPort/spaceUsage/spaceInventory/inventoryDetails.vue'),
            meta: {
              title: '盘点详情',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/spaceUsage/spaceInventory'
            }
          }
        ]
      },
      {
        path: 'spaceApplication',
        component: EmptyLayout,
        redirect: { name: 'spaceApplication' },
        meta: {
          title: '空间申请',
          menuAuth: '/spaceUsage/spaceApplication'
        },
        children: [
          {
            path: '',
            name: 'spaceApplication',
            component: () => import('@/views/operationPort/spaceUsage/spaceApplication/index.vue'),
            meta: {
              title: '空间申请',
              sidebar: false,
              breadcrumb: false,
              avtiveMenu: '/spaceUsage/spaceApplication'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/spaceLeasing',
    component: Layout,
    redirect: '/spaceLeasing/tenantManagement',
    name: 'spaceLeasing',
    meta: {
      title: '空间租赁管理',
      menuAuth: '/spaceLeasing'
    },
    children: [
      {
        path: 'tenantManagement',
        component: EmptyLayout,
        redirect: { name: 'tenantManagement' },
        meta: {
          title: '租户管理',
          menuAuth: '/spaceLeasing/tenantManagement'
        },
        children: [
          {
            path: '',
            name: 'tenantManagement',
            component: () => import('@/views/operationPort/spaceLeasing/tenantManagement/index.vue'),
            meta: {
              title: '租户管理',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/spaceLeasing/tenantManagement'
            }
          },
          {
            path: 'addTenant',
            name: 'addTenant',
            component: () => import('@/views/operationPort/spaceLeasing/tenantManagement/addTenant.vue'),
            meta: {
              title: '新增租户',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/spaceLeasing/tenantManagement'
            }
          }
        ]
      },
      {
        path: 'leaseManagement',
        component: EmptyLayout,
        redirect: { name: 'leaseManagement' },
        meta: {
          title: '租赁管理',
          menuAuth: '/spaceLeasing/leaseManagement'
        },
        children: [
          {
            path: '',
            name: 'leaseManagement',
            component: () => import('@/views/operationPort/spaceLeasing/leaseManagement/index.vue'),
            meta: {
              title: '租赁管理',
              sidebar: false,
              breadcrumb: false,
              avtiveMenu: '/spaceLeasing/leaseManagement'
            }
          },
          {
            path: 'addLease',
            name: 'addLease',
            component: () => import('@/views/operationPort/spaceLeasing/leaseManagement/addLease.vue'),
            meta: {
              title: '新增租赁',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/spaceLeasing/leaseManagement'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/spaceStatistics',
    component: Layout,
    redirect: '/spaceStatistics/statisticalAnalysis',
    name: 'spaceStatistics',
    meta: {
      title: '统计分析',
      menuAuth: '/spaceStatistics'
    },
    children: [
      {
        path: 'statisticalAnalysis',
        component: EmptyLayout,
        redirect: { name: 'statisticalAnalysis' },
        meta: {
          title: '统计分析',
          menuAuth: '/spaceStatistics/statisticalAnalysis'
        },
        children: [
          {
            path: '',
            name: 'statisticalAnalysis',
            component: () => import('@/views/operationPort/spaceStatistics/statisticalAnalysis/index.vue'),
            meta: {
              title: '统计分析',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/spaceStatistics/statisticalAnalysis'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/spaceFacility',
    component: Layout,
    redirect: '/spaceFacility/facilityLedger',
    name: 'spaceFacility',
    meta: {
      title: '空间设施管理',
      menuAuth: '/spaceFacility'
    },
    children: [
      {
        path: 'facilityLedger',
        component: EmptyLayout,
        redirect: { name: 'facilityLedger' },
        meta: {
          title: '设施台账',
          menuAuth: '/spaceFacility/facilityLedger'
        },
        children: [
          {
            path: '',
            name: 'statisticalAnalysis',
            component: () => import('@/views/operationPort/spaceFacility/facilityLedger/index.vue'),
            meta: {
              title: '设施台账',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/spaceFacility/facilityLedger'
            }
          }
        ]
      },
      {
        path: 'registrationAudit',
        component: EmptyLayout,
        redirect: { name: 'registrationAudit' },
        meta: {
          title: '登记审核',
          menuAuth: '/spaceFacility/registrationAudit'
        },
        children: [
          {
            path: '',
            name: 'registrationAudit',
            component: () => import('@/views/operationPort/spaceFacility/registrationAudit/index.vue'),
            meta: {
              title: '登记审核',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/spaceFacility/registrationAudit'
            }
          }
        ]
      },
      {
        path: 'typeManagement',
        component: EmptyLayout,
        redirect: { name: 'typeManagement' },
        meta: {
          title: '类型管理',
          menuAuth: '/spaceFacility/typeManagement'
        },
        children: [
          {
            path: '',
            name: 'typeManagement',
            component: () => import('@/views/operationPort/spaceFacility/typeManagement/index.vue'),
            meta: {
              title: '类型管理',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/spaceFacility/typeManagement'
            }
          }
        ]
      }
    ],

  }
]
