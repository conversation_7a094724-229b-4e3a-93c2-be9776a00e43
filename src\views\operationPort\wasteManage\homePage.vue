<template>
  <PageContainer>
    <div slot="content">
      <div class="box left-box">
        <div class="title">
          <svg-icon name="right-arrow" />
          <span>医疗废物情况</span>
        </div>
        <div class="subtitle">重量统计</div>
        <div class="card-box">
          <span>今日</span>
          <div class="num-value">
            <span>{{ wasteSituationData.today }}</span>
            <span>kg</span>
          </div>
          <img src="@/assets/images/operationPort/waste-icon2.png" />
        </div>
        <div class="card-box">
          <span>本月</span>
          <div class="num-value">
            <span>{{ wasteSituationData.months }}</span>
            <span>kg</span>
          </div>
          <img src="@/assets/images/operationPort/waste-icon2.png" />
        </div>
        <div class="card-box">
          <span>本年</span>
          <div class="num-value">
            <span>{{ wasteSituationData.years }}</span>
            <span>kg</span>
          </div>
          <img src="@/assets/images/operationPort/waste-icon2.png" />
        </div>
        <div class="subtitle">数量统计</div>
        <div class="card-box card-box2">
          <span>今日</span>
          <div class="num-value">
            <span>{{ wasteSituationData.todaySum }}</span>
          </div>
          <img src="@/assets/images/operationPort/waste-icon1.png" />
        </div>
      </div>
      <div class="middle-box">
        <div class="middle-top-box">
          <div class="title">
            <svg-icon name="right-arrow" />
            <span>今日医疗废物数量</span>
          </div>
          <div class="cards">
            <div v-for="item in todayWasteList" :key="item.wasteCode" class="card-item">
              <span>{{ item.name }}</span>
              <span>{{ item.value }}</span>
            </div>
          </div>
        </div>
        <div class="middle-bottom-box">
          <div class="btns btns2">
            <span :class="{ 'active-btn': chartDateType == 'month' }" @click="changeChartDateType('month')">月度对比</span>
            <span :class="{ 'active-btn': chartDateType == 'year' }" @click="changeChartDateType('year')">年度对比</span>
          </div>
          <div class="title">
            <svg-icon name="right-arrow" />
            <span>医疗废物重量趋势</span>
          </div>
          <div v-if="chartDateType == 'month'" id="monthChart"></div>
          <div v-if="chartDateType == 'year'" id="yearChart"></div>
        </div>
      </div>
      <div class="right-box">
        <div class="right-top-box">
          <div class="top-title">
            <div class="title">
              <svg-icon name="right-arrow" />
              <span>出入站概况</span>
            </div>
            <span style="font-size: 14px;"
              >超时未处理 <span style="color: #ea665d;">{{ inoutData.expiredCount }}</span> 箱</span
            >
          </div>
          <div class="item">
            <div>
              <img src="@/assets/images/operationPort/waste-icon3.png" />
              <span>&ensp;库存箱数</span>
            </div>
            <span style="color: #3562db;">{{ inoutData.amount }}</span>
          </div>
          <div class="item">
            <div>
              <img src="@/assets/images/operationPort/waste-icon3.png" />
              <span>&ensp;今日入站箱数</span>
            </div>
            <span style="color: #3562db;">{{ inoutData.gatherCount }}</span>
          </div>
          <div class="item">
            <div>
              <img src="@/assets/images/operationPort/waste-icon3.png" />
              <span>&ensp;今日出站箱数</span>
            </div>
            <span style="color: #3562db;">{{ inoutData.buyCount }}</span>
          </div>
        </div>
        <div class="right-bottom-box">
          <div class="title">
            <svg-icon name="right-arrow" />
            <span>Top10科室数据统计</span>
          </div>
          <div class="btns">
            <span :class="{ 'active-btn': dateType == 'day' }" @click="changeDateType('day')">今日</span>
            <span :class="{ 'active-btn': dateType == 'month' }" @click="changeDateType('month')">本月</span>
            <span :class="{ 'active-btn': dateType == 'year' }" @click="changeDateType('year')">本年</span>
          </div>
          <div class="table-box">
            <div v-for="(item, index) in top10List" :key="index" class="table-item">
              <span :title="item.name">{{ item.name }}</span>
              <span>{{ item.value }}kg</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import store from '@/store/index'
import * as echarts from 'echarts'
export default {
  name: 'homePage',
  data() {
    return {
      dateType: 'day',
      chartDateType: 'month',
      wasteSituationData: {},
      todayWasteList: [],
      inoutData: {},
      top10List: []
    }
  },
  watch: {
    '$store.state.settings.sidebarCollapse': {
      handler(val) {
        this.$nextTick(() => {
          let echartsDom = ['monthChart', 'yearChart']
          setTimeout(() => {
            echartsDom.forEach((item) => {
              echarts.init(document.getElementById(item)).resize()
            })
          }, 250)
        })
      },
      deep: true
    }
  },
  mounted() {
    this.getMonthChartData()
    this.getData()
  },
  methods: {
    getMonthChartData() {
      this.$api.getHomeMonthChart({}).then((res) => {
        if (res.code == '200') {
          this.initMonthChart(res.data)
        }
      })
    },
    getYearChartData() {
      this.$api.getHomeYearChart({}).then((res) => {
        if (res.code == '200') {
          this.initYearChart(res.data)
        }
      })
    },
    getData() {
      this.$api.getHomeOfficeWasteState({ columnName: 'gather_weigh' }).then((res) => {
        if (res.code == '200') {
          this.wasteSituationData = res.data
        }
      })
      this.$api.getCountGroupByColumnName({ columnName: 'waste_type', dateType: 'day' }).then((res) => {
        if (res.code == '200') {
          this.todayWasteList = res.data
        }
      })
      this.$api.getTodayStationInfo({ columnName: 'gather_weigh' }).then((res) => {
        if (res.code == '200') {
          this.inoutData = res.data
        }
      })
      this.getTop10Data()
    },
    getTop10Data() {
      this.$api.getSumByOfficeId({ columnName: 'gather_weigh', limit: 10, dateType: this.dateType }).then((res) => {
        if (res.code == '200') {
          this.top10List = res.data
        }
      })
    },
    changeChartDateType(type) {
      this.chartDateType = type
      if (type == 'month') {
        this.getMonthChartData()
      } else {
        this.getYearChartData()
      }
    },
    initMonthChart(data) {
      const getchart = echarts.init(document.getElementById('monthChart'))
      let colors = ['#3562DB', '#FF9435']
      const option = {
        color: colors,
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        grid: {
          right: '15%'
        },
        legend: {
          data: ['收集重量', '运出重量'],
          textStyle: {
            color: '#333' // 图例文字颜色
          }
        },
        xAxis: [
          {
            type: 'category',
            axisTick: {
              alignWithLabel: true
            },
            data: data.monthList
          }
        ],
        yAxis: [
          {},
          {
            type: 'value',
            name: '收集重量',
            min: 0,
            max: Math.max.apply(null, data.gatherWeighList),
            position: 'left',
            axisLine: {
              lineStyle: {
                color: colors[0]
              }
            },
            axisLabel: {
              formatter: '{value} kg'
            },
            splitLine: {
              show: false
            }
          },
          {
            type: 'value',
            name: '运出重量',
            min: 0,
            max: Math.max.apply(null, data.buyWeighList),
            offset: 22,
            position: 'right',
            axisLine: {
              lineStyle: {
                color: colors[1]
              }
            },
            axisLabel: {
              formatter: '{value} kg'
            },
            splitLine: {
              show: false
            }
          }
        ],
        series: [
          {
            name: '收集重量',
            type: 'bar',
            yAxisIndex: 1,
            barWidth: 25,
            data: data.gatherWeighList
          },
          {
            name: '运出重量',
            type: 'line',
            yAxisIndex: 2,
            data: data.buyWeighList
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    initYearChart(data) {
      const getchart = echarts.init(document.getElementById('yearChart'))
      let colors = ['#3562DB', '#FF9435']
      const option = {
        color: colors,
        tooltip: {
          trigger: 'none',
          axisPointer: {
            type: 'cross'
          }
        },
        legend: {
          data: [data.lastYearMonth[0].split('-', 1) + ' 收集重量', data.thisYearMonth[0].split('-', 1) + ' 收集重量']
        },
        grid: {
          top: 70,
          bottom: 50
        },
        xAxis: [
          {
            type: 'category',
            axisTick: {
              alignWithLabel: true
            },
            axisLine: {
              onZero: false,
              lineStyle: {
                color: colors[1]
              }
            },
            axisPointer: {
              label: {
                formatter: function (params) {
                  return '收集重量  ' + params.value + (params.seriesData.length ? '：' + params.seriesData[0].data : '')
                }
              }
            },
            data: data.thisYearMonth
          },
          {
            type: 'category',
            axisTick: {
              alignWithLabel: true
            },
            axisLine: {
              onZero: false,
              lineStyle: {
                color: colors[0]
              }
            },
            axisPointer: {
              label: {
                formatter: function (params) {
                  return '收集重量  ' + params.value + (params.seriesData.length ? '：' + params.seriesData[0].data : '')
                }
              }
            },
            data: data.lastYearMonth
          }
        ],
        yAxis: [
          {
            type: 'value'
          }
        ],
        series: [
          {
            name: data.lastYearMonth[0].split('-', 1) + ' 收集重量',
            type: 'line',
            xAxisIndex: 1,
            smooth: true,
            data: data.lastYearGatherWeigh
          },
          {
            name: data.thisYearMonth[0].split('-', 1) + ' 收集重量',
            type: 'line',
            smooth: true,
            data: data.thisYearGatherWeigh
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    changeDateType(type) {
      this.dateType = type
      this.getTop10Data()
    }
  }
}
</script>
<style lang="scss" scoped>
.container-content > div {
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.box {
  border-radius: 4px;
  padding: 12px;
}

.left-box {
  width: 15%;
  height: 100%;
  background-color: #fff;
}

.middle-box {
  width: 68%;
  height: 100%;
}

.right-box {
  width: 15%;
  height: 100%;
  border-radius: 4px;
}

.subtitle {
  margin: 24px 0;
  padding-left: 3px;
}

.card-box {
  background-color: rgb(255 148 53 / 10%);
  border-radius: 4px;
  height: 120px;
  font-size: 15px;
  padding: 16px;
  position: relative;
  margin-bottom: 16px;
}

.card-box2 {
  background: rgb(53 98 219 / 10%);
}

.card-box img {
  position: absolute;
  bottom: 10%;
  right: 8%;
  width: 34px;
  height: 34px;
}

.num-value {
  position: absolute;
  bottom: 12%;
}

.num-value span:nth-child(1) {
  font-size: 24px;
  font-weight: 700;
  margin-right: 5px;
}

.num-value span:nth-child(2) {
  font-size: 14px;
  color: #ccced3;
}

.top-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 35px;
}

.item img {
  width: 16px;
  height: 16px;
}

.right-top-box {
  background-color: #fff;
  border-radius: 4px;
  padding: 12px;
  height: 21%;
}

.right-bottom-box {
  background-color: #fff;
  border-radius: 4px;
  padding: 12px;
  margin-top: 12px;
  height: 79%;
  overflow: hidden;
}

.middle-bottom-box .btns2 {
  width: 200px;
  margin: 0;
  position: absolute;
  top: 24px;
  right: 16px;
}

.middle-bottom-box .btns2 > span {
  width: 48%;
}

.btns {
  width: 150px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0 auto;
  margin-top: 12px;
}

.btns > span {
  width: 30%;
  background-color: #f6f5fa;
  font-size: 14px;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #ededf5;
  color: #7f848c;
  cursor: pointer;
}

.active-btn {
  background-color: #3562db !important;
  color: #fff !important;
  border-color: #3562db !important;
}

.table-box {
  border: 1px solid #dcdfe6;
  margin-top: 16px;
  border-bottom: none;
}

.table-box .table-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 45px;
  border-bottom: 1px solid #dcdfe6;
  padding: 0 12px;
  font-size: 15px;
}

.table-box .table-item:hover {
  background-color: #f5f6fc;
}

.middle-top-box {
  height: 21%;
  background-color: #fff;
  border-radius: 4px;
  margin-bottom: 12px;
  padding: 12px;
}

.middle-bottom-box {
  height: 79%;
  background-color: #fff;
  border-radius: 4px;
  padding: 12px;
  position: relative;
}

.cards {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-top: 12px;
}

.cards .card-item {
  width: 13.5%;
  background: #faf9fc;
  border-radius: 4px;
  height: 100px;
  padding: 16px;
}

.card-item > span:nth-child(1) {
  display: block;
  margin-bottom: 16px;
  color: #121f3e;
  font-weight: 500;
}

.card-item > span:nth-child(2) {
  color: #121f3e;
  font-weight: 700;
  font-size: 24px;
}

#monthChart,
#yearChart {
  width: 100%;
  height: 88%;
  margin-top: 5%;
}

.table-item > span:nth-child(1) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 110px;
}
</style>
