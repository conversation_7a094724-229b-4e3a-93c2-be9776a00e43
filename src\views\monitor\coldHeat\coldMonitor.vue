<template>
  <!-- 冷热源运行监测 -->
  <PageContainer>
    <div slot="content" class="monitor-content">
      <div class="monitor-content-left">
        <el-tree
          slot="content"
          ref="teamTree"
          v-loading="treeLoading"
          class="team-tree"
          :check-strictly="true"
          :data="teamTreeData"
          :props="defaultProps"
          node-key="id"
          :highlight-current="true"
          :default-expanded-keys="expandedTeam"
          @node-click="handleTeamClick"
        >
          <template #default="{node, data}">
            <el-tooltip effect="dark" :disabled="showTooltip" :content="node.label" placement="top">
              <div class="nodeLabel" @mouseover="onMouseOver(data.id)">
                <span :ref="`nodeLabel${data.id}`">{{node.label}}</span>
              </div>
            </el-tooltip>
          </template>
        </el-tree>
      </div>
      <div class="monitor-content-right">
        <div class="right-heade">
          <el-input v-model="searchFrom.surveyName" placeholder="监测项名称" suffix-icon="el-icon-search" style="width: 200px;" clearable />
          <div style="display: inline-block;">
            <el-button v-auth="'userManagement:reset'" type="primary" plain @click="resetForm">重置</el-button>
            <el-button v-auth="'userManagement:search'" type="primary" @click="searchForm">查询</el-button>
          </div>
          <div class="heade-pattern">
            <div class="pattern-item" @click="switchPattern(1)">
              <svg-icon :name="currentPattern == 1 ? 'chartModeActive' : 'chartMode'" class="pattern-icon" />
              <span :style="{ color: currentPattern == 1 ? '#3562DB' : '#414653' }">图形模式</span>
            </div>
            <div class="pattern-item" @click="switchPattern(2)">
              <svg-icon :name="currentPattern == 2 ? 'listModeActive' : 'listMode'" class="pattern-icon" />
              <span :style="{ color: currentPattern == 2 ? '#3562DB' : '#414653' }">列表模式</span>
            </div>
          </div>
        </div>
        <div class="right-content">
          <graphics-mode v-if="currentPattern == 1" ref="scadaShow" :entityMenuCode="searchFrom.entityMenuCode" :projectId="searchFrom.projectCode" />
          <list-mode v-else ref="listMode" />
        </div>
      </div>
    </div>
  </PageContainer>
</template>

<script>
import listMode from './components/listMode'
import graphicsMode from '../airMenu/components/graphicsMode'
import { monitorTypeList } from '@/util/dict.js'
export default {
  name: 'coldMonitor',
  components: {
    listMode,
    graphicsMode
  },
  beforeRouteEnter(to, from, next) {
    let names = []
    to.matched.map((v, i) => {
      if (i > 0) {
        v.components.default.name && names.push(v.components.default.name)
      }
    })
    next((vm) => {
      if (from.query.activeName) {
        vm.activeName = from.query.activeName
      }
      vm.$store.commit('keepAlive/add', names)
    })
  },
  async beforeRouteLeave(to, from, next) {
    // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
    if (!['monitorDetails'].includes(to.name)) {
      // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      treeLoading: true,
      currentPattern: 2, // 当前模式
      teamTreeData: [], // 树状数据
      expandedTeam: [], // 默认展开树
      checkedTeamData: {}, // 当前选中树
      defaultProps: {
        children: 'children',
        label: 'name',
        value: 'code'
      },
      sortList: [
        { label: '按关系', value: 1 },
        { label: '按类型', value: 2 }
      ],
      searchFrom: {
        projectCode: monitorTypeList.find((item) => item.projectName == '冷热源监测').projectCode,
        entityMenuCode: '', // 菜单code
        surveyName: ''
      },
      showTooltip: true
    }
  },
  computed: {},
  created() {
    this.getTreelist()
  },
  activated() {
    this.searchForm()
  },
  methods: {
    getTreelist() {
      this.$api.GetEntityMenuList({ projectId: this.searchFrom.projectCode }).then((res) => {
        this.treeLoading = false
        let list = this.$tools.transData(res.data, 'code', 'parentId', 'children')
        this.teamTreeData = list
        this.checkedTeamData = this.teamTreeData[0]
        this.searchFrom.entityMenuCode = this.teamTreeData[0].code
        this.$nextTick(() => {
          this.$refs.teamTree.setCurrentKey(this.teamTreeData[0])
        })
        this.searchForm()
      }).catch(() => {
        this.treeLoading = false
      })
    },
    // 模式切换
    switchPattern(type) {
      if (this.currentPattern != type) {
        this.currentPattern = type
        this.searchForm()
      }
    },
    // 树状图点击
    handleTeamClick(data) {
      if (this.searchFrom.entityMenuCode != data.code) {
        this.searchFrom.entityMenuCode = data.code
        this.currentPattern = 2
        this.checkedTeamData = data
        this.$refs.teamTree.setCurrentKey(this.checkedTeamData)
        this.searchForm()
      }
    },
    // 重置查询
    resetForm() {
      this.searchFrom.surveyName = ''
      // Object.assign(this.$data.searchFrom, this.$options.data().searchFrom)
      this.searchForm()
    },
    // 查询
    searchForm() {
      if (this.currentPattern == 1) {
      } else {
        this.$nextTick(() => {
          this.$refs.listMode.init(this.searchFrom)
        })
      }
    },
    // 树划入划出效果
    onMouseOver (id) {
      const parentWidth = this.$refs[`nodeLabel${id}`].parentNode.offsetWidth // 获取元素父级可视宽度
      const contentWidth = this.$refs[`nodeLabel${id}`].offsetWidth // 获取元素可视宽度
      this.showTooltip = contentWidth <= parentWidth
    }
  }
}
</script>

<style lang="scss" scoped>
.monitor-content {
  height: 100%;
  display: flex;

  ::v-deep .monitor-content-left {
    width: 246px;
    height: 100%;
    background: #fff;
    border-radius: 4px;
    padding: 16px;
    overflow: auto;

    .el-tree {
      height: 100%;
    }

    .el-tree-node {
      .el-tree-node__content {
        padding: 6px 0;
        height: auto;
      }
    }

    .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
      background-color: #d9e1f8;
    }
  }

  .monitor-content-right {
    height: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;

    .right-heade {
      padding: 0 200px 10px 10px !important;
      margin-left: 16px;
      background: #fff;
      border-radius: 4px;
      position: relative;
      display: flex;
      flex-wrap: wrap;
      align-items: center;

      & > div {
        margin-right: 10px;
        margin-top: 10px;
      }

      .searchBtn {
        user-select: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 12px;
        height: 26px;
        background: #f6f5fa;
        border-radius: 4px;
        font-size: 14px;
        color: #7f848c;

        span {
          display: flex;
          flex-direction: column;

          i {
            width: 10px;
            height: 10px;
            font-size: 10px;
            margin-top: -2px;
            margin-bottom: -2px;
          }
        }
      }

      .heade-pattern {
        display: flex;
        position: absolute;
        right: 16px;
        top: 50%;
        margin: 0 !important;
        transform: translateY(-50%);

        .pattern-item {
          cursor: pointer;
          font-size: 15px;

          .pattern-icon {
            font-size: 16px;
            margin-right: 6px;
          }
        }

        .pattern-item:last-child {
          margin-left: 16px;
        }
      }
    }

    .right-content {
      flex: 1;
      overflow: hidden;
    }
  }
}
.nodeLabel{
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
</style>
