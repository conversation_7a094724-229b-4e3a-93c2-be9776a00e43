<template>
  <PageContainer>
    <div slot="content" class="exerciseType-content">
      <div class="exerciseType-content-top">
        <div class="control-btn-header">
          <el-button type="primary" icon="el-icon-plus" @click="handleListEvent('add')">新增类型</el-button>
          <el-button type="primary" :disabled="multipleSelection.length<1" @click="batchDelete">批量删除</el-button>
        </div>
        <div class="contentTable">
          <div class=" contentTable-main table-content">
            <el-table v-loading="tableLoading" border :data="tableData" height="calc(100% - 10px)" stripe
              @selection-change="handleSelectionChange">
              <el-table-column type="selection" width="55">
              </el-table-column>
              <el-table-column label="演练类型" prop="labelName"></el-table-column>
              <el-table-column label="操作" width="200" align="center">
                <template slot-scope="scope">
                  <el-button type="text" @click="handleListEvent('edit', scope.row)">编辑</el-button>
                  <el-button type="text" @click="handleListEvent('del',scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="contentTable-footer">
            <el-pagination :current-page="pagination.current" :page-sizes="pagination.pageSizeOptions"
              :page-size="pagination.size" :layout="pagination.layoutOptions" :total="pageTotal"
              @size-change="paginationSizeChange" @current-change="paginationCurrentChange">
            </el-pagination>
          </div>
        </div>
      </div>
      <el-dialog v-dialogDrag :visible.sync="dialogVisible" :before-close="dialogClosed" :close-on-click-modal="false"
        :title="diaTitle" width="30%" custom-class="model-dialog">
        <div class="content" style="padding: 10px">
          <el-form ref="formInline" :model="formInline" label-width="100px" :rules="rules" label-position="right">
            <el-form-item label="类型名称：" prop="labelName">
              <el-input v-model.trim="formInline.labelName" placeholder="请输入类型名称" :maxlength="20" style="width: 260px"
                @keydown.enter.native.prevent></el-input>
            </el-form-item>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="dialogClosed">取 消</el-button>
          <el-button type="primary" @click="submit('formInline')">确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </PageContainer>
</template>
<script lang="jsx">
import tableListMixin from '@/mixins/tableListMixin.js'
export default {
  name: 'exerciseType',
  mixins: [tableListMixin],
  data() {
    return {
      tableLoading: false,
      dialogVisible: false,
      diaTitle: '',
      rules: {
        labelName: [{ required: true, message: '请输入类型名称', trigger: 'blur' }]
      },
      formInline: {
        labelName: ''
      },
      pagination: {
        pageSize: 15,
        page: 1,
        total: 0
      },
      rowId: '',
      tableData: [],
      pageData: {
        page: 1,
        pageSize: 15
      },
      pageTotal: 0,
      multipleSelection: []
    }
  },
  mounted() {
    this.getDataList()
  },
  methods: {
    // 列表数据
    getDataList() {
      this.tableLoading = true
      let data = {
        page: {
          current: this.pagination.current,
          size: this.pagination.size
        }
      }
      this.$api
        .getPreplanDrillTypeData(data)
        .then((res) => {
          this.tableLoading = false
          if (res.code == 200) {
            this.tableData = res.data ? res.data.list : []
            this.pageTotal = res.data ? res.data.totalCount : 0
          } else if (res.message) {
            this.tableData = []
            this.pageTotal = 0
            this.$message.error(res.message)
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    // 弹窗取消
    dialogClosed() {
      this.dialogVisible = false
      this.$refs.formInline.resetFields()
    },
    submit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let data = {
            labelName: this.formInline.labelName,
            id: ''
          }
          if (this.rowId) {
            data.id = this.rowId
            this.updateData(data)
          } else {
            this.insert(data)
          }
        } else {
          return false
        }
      })
    },
    // 新增数据
    insert(params) {
      this.$api.insertPreplanDrillTypeData(params, { 'operation-type': 1}).then((res) => {
        if (res.code == 200) {
          this.$message.success(res.msg)
          this.getDataList()
          this.dialogClosed()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 更新数据
    updateData(params) {
      this.$api.updatePreplanDrillTypeData(params, { 'operation-type': 2, 'operation-id': this.rowId, 'operation-name': this.formInline.labelName }).then((res) => {
        if (res.code == 200) {
          this.$message.success(res.msg)
          this.getDataList()
          this.dialogClosed()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    handleListEvent(type, row) {
      if (type == 'add') {
        this.dialogVisible = true
        this.rowId = ''
        this.formInline.labelName = ''
        this.diaTitle = '新增类型'
      }
      if (type == 'edit') {
        this.dialogVisible = true
        this.rowId = row.id
        this.formInline.labelName = row.labelName
        this.diaTitle = '编辑类型'
      }
      if (type == 'del') {
        this.$confirm('是否删除该演练类型?', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let ids = []
          ids.push(row.id)
          this.$api.deletePreplanDrillTypeData({ids: ids}, { 'operation-type': 3, 'operation-id': row.id, 'operation-name': row.labelName }).then((res) => {
            if (res.code === '200') {
              this.$message.success('删除成功')
              this.getDataList()
            } else {
              this.$message.warning(res.msg)
            }
          })
        })
      }
    },
    // 批量删除
    batchDelete() {
      this.$confirm('是否批量删除类型?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let ids = []
        this.multipleSelection.forEach(item => {
          ids.push(item.id)
        })
        this.$api.deletePreplanDrillTypeData({ids: ids}, {'operation-type': 3}).then((res) => {
          if (res.code === '200') {
            this.$message.success('删除成功')
            this.getDataList()
          } else {
            this.$message.warning(res.msg)
          }
        })
      })
    },
    handleCurrentChange(val) {
      this.pagination.page = val
      this.getList()
    },
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.pagination.page = 1
      this.getList()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    }

  }
}
</script>
<style lang="scss" scoped>
.exerciseType-content {
  height: 100%;
  display: flex;

  .exerciseType-content-top {
    height: 100%;
    padding: 10px;
    background: #fff;
    border-radius: 4px;
    width: 100%;

    .control-btn-header {
      display: flex;
      justify-content: flex-start;
      padding-bottom: 15px;
    }
  }
  .content {
    width: 100%;
    max-height: 500px !important;
    overflow: auto;
    background-color: #fff !important;
  }
  .contentTable {
    height: calc(100% - 50px);
    display: flex;
    flex-direction: column;

    .contentTable-main {
      flex: 1;
      overflow: auto;
    }

    .contentTable-footer {
      padding: 10px 0 0;
    }
  }
}
</style>
