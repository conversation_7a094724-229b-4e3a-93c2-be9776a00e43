<template>
  <PageContainer>
    <div v-if="hospitalCode !== 'BJSJTYY'" slot="header" class="tabs-header">
      <el-tabs v-model="queryParams.energyId" @tab-click="handleEnergyTypeTabClick">
        <el-tab-pane v-for="item in activeEnergyList.tabsData" :key="item.energyId" :label="item.name" :name="item.energyId" />
      </el-tabs>
    </div>
    <div slot="content" class="box-content">
      <div class="overview-statistics">
        <div class="statistics-left">
          <div v-loading="monthLoading" class="statistics-left-box">
            <div class="statistics-left-title">本月用{{ activeEnergyList.translate }}</div>
            <div class="statistics-left-value">
              <span>{{ ThisMonth.value | overFixedFilter }}</span
              ><span>{{ activeEnergyList.unit }}</span>
            </div>
            <div class="statistics-left-progress">
              <el-progress
                :percentage="monthRatedThan > 100 ? 100 : monthRatedThan"
                color="#3562DB"
                define-back-color="#CCCED3"
                :stroke-width="10"
                :show-text="false"
              ></el-progress>
            </div>
            <div class="statistics-left-progress-text">
              <span>已用</span><span>月额定用{{ activeEnergyList.translate }}</span>
            </div>
            <div class="statistics-left-progress-value">
              <div>
                <span>{{ monthRatedThan }}</span
                ><span>%</span>
              </div>
              <div>
                <span>{{ ratedData[energyId]?.monthRated || '-' }}</span
                ><span>{{ activeEnergyList.unit }}</span>
              </div>
            </div>
          </div>
          <div v-loading="yearLoading" class="statistics-left-box">
            <div class="statistics-left-title">本年用{{ activeEnergyList.translate }}</div>
            <div class="statistics-left-value">
              <span>{{ ThisYear.value | overFixedFilter }}</span
              ><span>{{ activeEnergyList.unit }}</span>
            </div>
            <div class="statistics-left-progress">
              <el-progress :percentage="yearRatedThan > 100 ? 100 : yearRatedThan" color="#3562DB" define-back-color="#CCCED3" :stroke-width="10" :show-text="false"></el-progress>
            </div>
            <div class="statistics-left-progress-text">
              <span>已用</span><span>年额定用{{ activeEnergyList.translate }}</span>
            </div>
            <div class="statistics-left-progress-value">
              <div>
                <span>{{ yearRatedThan }}</span
                ><span>%</span>
              </div>
              <div>
                <span>{{ ratedData[energyId]?.yearRated || '-' }}</span
                ><span>{{ activeEnergyList.unit }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="statistics-right">
          <div class="statistics-right-statistics">
            <span><svg-icon name="hidden-danger-icon" /></span>
            <span>{{ ratedNum || 0 }}</span>
            <span>本月超限</span>
          </div>
          <div class="statistics-right-table">
            <TablePage
              ref="tablePage"
              v-loading="tableLoading"
              v-loadMore="loadMore"
              border
              :showPage="false"
              :tableColumn="tableColumn"
              :data="tableData"
              height="calc(100% - 0px)"
            >
            </TablePage>
          </div>
        </div>
      </div>
      <div class="search-aside">
        <div
          v-for="(item, index) in searchBtnType"
          :key="index"
          :class="{
            'search-aside-item': true,
            'search-aside-item-active': activeDate === item.type
          }"
          @click="activeDate = item.type"
        >
          {{ item.name }}
        </div>
        <el-date-picker
          v-if="activeDate === 'day'"
          key="day"
          v-model="searchForm.dayDate"
          value-format="yyyy-MM-dd"
          type="date"
          :picker-options="pickerOptions"
          placeholder="选择日期"
          style="margin: 0 10px"
        >
        </el-date-picker>
        <el-date-picker
          v-else-if="activeDate === 'week'"
          key="week"
          v-model="searchForm.weekDate"
          type="week"
          format="yyyy 第 WW 周"
          value-format="yyyy-MM-dd"
          placeholder="选择周"
          :picker-options="{ ...pickerOptions, firstDayOfWeek: 1 }"
          style="margin: 0 10px"
        >
        </el-date-picker>
        <el-date-picker
          v-else-if="activeDate === 'month'"
          key="month"
          v-model="searchForm.monthDate"
          value-format="yyyy-MM"
          type="month"
          :picker-options="pickerOptions"
          placeholder="选择月"
          style="margin: 0 10px"
        >
        </el-date-picker>
        <el-date-picker
          v-else
          key="custom"
          v-model="searchForm.custom"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="至"
          :picker-options="pickerOptions"
          placeholder="选择时间范围"
          style="margin: 0 10px"
        >
        </el-date-picker>
        <!-- <el-button type="text" style="margin: 0 10px;" @click="resetForm">今日</el-button> -->
        <el-button type="primary" plain @click="resetForm">重置</el-button>
        <el-button type="primary" @click="searchFormEvent">查询</el-button>
      </div>
      <div class="overview-content">
        <div v-scrollbarHover v-loading="treeLoading" class="content-left">
          <ZkRenderTree
            :data="spaceTreeData"
            :props="defaultTreeProps"
            node-key="id"
            :default-expanded-keys="defaultExpandedKeys"
            highlight-current
            :expand-on-click-node="false"
            @node-click="handleTreeNodeClick"
          ></ZkRenderTree>
        </div>
        <div class="content-right">
          <div class="content-right-top">
            <ContentCard :title="`能耗趋势（${activeEnergyList.unit}）`" :cstyle="{ height: '100%', width: '100%' }">
              <div slot="content" style="height: 100%; overflow: hidden">
                <div id="EnergyTrendEchart" v-loading="EnergyTrendEchartLoading"></div>
              </div>
            </ContentCard>
          </div>
          <div class="content-right-bottom">
            <div class="content-right-bottom-left">
              <ContentCard :title="`用${activeEnergyList.translate}类型占比`" :cstyle="{ height: '100%', width: '100%' }">
                <div slot="content" style="height: 100%; overflow: hidden">
                  <div id="ProportionTypesEchart" v-loading="ProportionTypesEchartLoading"></div>
                </div>
                <div slot="title-right" class="title_right">
                  <span>单位({{ activeEnergyList.unit }})</span>
                </div>
              </ContentCard>
            </div>
            <div class="content-right-bottom-center">
              <ContentCard :title="`区域用${activeEnergyList.translate}排行`" :cstyle="{ height: '100%', width: '100%' }">
                <div slot="content" style="height: 100%; overflow: hidden">
                  <div id="RegionalRankingEchart" v-loading="RegionalRankingEchartLoading"></div>
                </div>
                <div slot="title-right" class="title_right">
                  <span>单位({{ activeEnergyList.unit }})</span>
                </div>
              </ContentCard>
            </div>
            <div class="content-right-bottom-right">
              <ContentCard :title="`区域用${activeEnergyList.translate}占比（${activeEnergyList.unit}）`" :cstyle="{ height: '100%', width: '100%' }">
                <div slot="content" style="height: 100%; overflow: hidden">
                  <div id="ProportionRegionalEchart" v-loading="RegionalRankingEchartLoading"></div>
                </div>
              </ContentCard>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PageContainer>
</template>
<script lang="jsx">
import { transData } from '@/util'
import * as echarts from 'echarts'
import moment from 'moment'
export default {
  name: 'menuContent',
  filters: {
    // 如果返回值大于1000 数值显示为万
    overFixedFilter(value) {
      if (value > 1000) {
        return (value / 10000).toFixed(2) + '万'
      } else {
        return value
      }
    }
  },
  props: {
    energyId: {
      type: String,
      require: true
    }
  },
  data() {
    return {
      EnergyTrendEchartLoading: false,
      ProportionTypesEchartLoading: false,
      RegionalRankingEchartLoading: false,
      isClickSearch: false, // 是否点击位置树
      // 本月数据
      ThisMonth: {
        value: 0
      },
      // 本年数据
      ThisYear: {
        value: 0
      },
      queryParams: {
        energyId: null, // 选中的能源类型对应的分项tab
        startDate: null,
        endDate: null,
        emodelId: null
      },
      energyList: Object.freeze([
        {
          energyId: 'SU035',
          tabsData: [],
          unit: 'kwh',
          translate: '电'
        },
        {
          energyId: 'CM020',
          tabsData: [],
          unit: 't',
          translate: '水'
        },
        {
          energyId: 'ZQ4400',
          tabsData: [],
          unit: 'm³',
          translate: '汽'
        },
        {
          energyId: 'SU05000',
          tabsData: [],
          unit: 'kw',
          translate: '冷热'
        }
      ]),
      activeEnergyList: [], // 能源类型对应的数据
      tableLoading: false,
      tableData: [],
      currentPage: 1,
      totalNum: 0,
      searchBtnType: [
        {
          name: '按日',
          type: 'day'
        },
        {
          name: '按周',
          type: 'week'
        },
        {
          name: '按月',
          type: 'month'
        },
        {
          name: '自定义',
          type: 'custom'
        }
      ],
      activeDate: 'day', // 按日
      searchForm: {
        // 搜索表单
        dayDate: '',
        weekDate: '',
        monthDate: '',
        custom: []
      },
      pickerOptions: {
        // 日期选择器配置
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      },
      defaultExpandedKeys: [], // 默认展开的节点
      spacesList: [], // 空间列表
      spaceTreeData: [], // 空间树
      defaultTreeProps: {
        children: 'children',
        label: 'ssmName'
      },
      token: null,
      ratedData: {},
      ratedNum: 0,
      // 固定的emodelId   不受筛选条件改变
      emodelId: energyEmodelId,
      hospitalCode: this.$store.state.user.userInfo.user.hospitalCode,
      monthRatedThan: '',
      yearRatedThan: '',
      monthLoading: false,
      yearLoading: false,
      treeLoading: false
    }
  },
  computed: {
    tableColumn() {
      return [
        {
          prop: 'stime',
          label: '时间'
        },
        // {
        //   prop: 'name',
        //   label: `${this.activeEnergyList.translate}表名称`
        // },
        {
          prop: 'objectName',
          label: '服务区域'
        },
        {
          prop: 'limitValue',
          label: `额定${this.activeEnergyList.unit}`
        },
        {
          prop: 'consume',
          label: `消耗${this.activeEnergyList.unit}`
        },
        {
          prop: 'value',
          label: `超限${this.activeEnergyList.unit}`
        },
        {
          prop: 'proportion',
          label: '占比'
        }
      ]
    }
  },
  watch: {
    '$store.state.settings.sidebarCollapse': {
      handler(val) {
        this.$nextTick(() => {
          let echartsDom = ['EnergyTrendEchart', 'ProportionTypesEchart', 'RegionalRankingEchart', 'ProportionRegionalEchart']
          setTimeout(() => {
            echartsDom.forEach((item) => {
              echarts.init(document.getElementById(item)).resize()
            })
          }, 250)
        })
      },
      deep: true
    }
  },
  mounted() {
    this.loginAndEnergyCategory()
    this.activeEnergyList = this.energyList.find((item) => item.energyId === this.energyId)
  },
  methods: {
    loginAndEnergyCategory() {
      this.monthLoading = true
      this.yearLoading = true
      const param = {
        username: intAccount.username,
        password: intAccount.password
      }
      this.$api.integrationLogin(param).then((res) => {
        this.token = res.result.token
        this.$api.getEnergyCategoryTree({}, { 'X-Access-Token': this.token }).then(async (res) => {
          let obj = res.data.find((item) => item.id == this.energyId)
          this.queryParams.energyId = obj.id
          this.activeEnergyList.tabsData = [
            {
              name: '总览',
              energyId: obj.id,
              status: 1 // 1为总览   0为其他
            },
            ...obj.children.map((item) => {
              return {
                name: item.name,
                energyId: item.id,
                status: 0 // 1为总览   0为其他
              }
            })
          ]
          await this.getStructureTree()
          this.getRated()
          this.getThisMonthAndThisYear()
          this.getAlarmInfoList()
          // console.log('this.activeEnergyList==========', this.activeEnergyList)
        })
      })
    },
    getAlarmInfoList() {
      let data = {
        pageNum: 1,
        pageSize: 100,
        energyType: this.queryParams.energyId,
        token: this.token,
        stime: moment().startOf('month').format('YYYY-MM-DD') + ' 00:00:00',
        etime: moment().endOf('month').format('YYYY-MM-DD') + ' 23:59:59'
      }
      this.$api.getAlarmInfoList(data).then((res) => {
        console.log(res, '--------------------')
        if (res.code == 200) {
          this.ratedNum = res.data.ratedNum
          this.tableData = res.data.records.map((ele) => {
            return {
              ...ele,
              consume: (ele.value - ele.limitValue).toFixed(2),
              proportion: (ele.value / ele.limitValue).toFixed(2) + '%'
            }
          })
        }
      })
    },
    getRated() {
      const data = {
        emodelId: [this.emodelId],
        energyIds: [this.queryParams.energyId],
        indicatorType: 1,
        isEnabled: 1,
        pageNo: 1,
        pageSize: 999
      }
      const monthData = {
        startTime: moment().format('YYYY-MM'),
        endTime: moment().format('YYYY-MM'),
        periodId: 8
      }
      let promiseMonth = this.$api.getIndicatorQuery({ ...data, ...monthData }, { 'X-Access-Token': this.token }).then((res) => {
        if (res.data.records && res.data.records.length) {
          res.data.records.forEach((item) => {
            if (Object.hasOwnProperty.call(this.ratedData, [item.energyId])) {
              Object.assign(this.ratedData[item.energyId], {
                monthRated: item.quotaQuantity,
                monthTimePercent: item.timePercent,
                monthRatedThan: Number(item.timePercent.replace('%', '')) || 100
              })
            } else {
              Object.assign(this.ratedData, {
                [item.energyId]: {
                  monthRated: item.quotaQuantity,
                  monthTimePercent: item.timePercent,
                  monthRatedThan: Number(item.timePercent.replace('%', '')) || 100
                }
              })
            }
          })
        }
      })
      const yearData = {
        startTime: moment().format('YYYY'),
        endTime: moment().format('YYYY'),
        periodId: 11
      }
      let promiseYear = this.$api.getIndicatorQuery({ ...data, ...yearData }, { 'X-Access-Token': this.token }).then((res) => {
        if (res.data.records && res.data.records.length) {
          res.data.records.forEach((item) => {
            if (Object.hasOwnProperty.call(this.ratedData, [item.energyId])) {
              Object.assign(this.ratedData[item.energyId], {
                yearRated: item.quotaQuantity,
                yearTimePercent: item.timePercent,
                yearRatedThan: Number(item.timePercent.replace('%', '')) || 100
              })
            } else {
              Object.assign(this.ratedData, {
                [item.energyId]: {
                  yearRated: item.quotaQuantity,
                  yearTimePercent: item.timePercent,
                  yearRatedThan: Number(item.timePercent.replace('%', '')) || 100
                }
              })
            }
          })
        }
      })
      Promise.all([promiseMonth, promiseYear]).then(() => {
        this.monthRatedThan = this.ratedData[this.energyId]?.monthRatedThan || 100
        this.yearRatedThan = this.ratedData[this.energyId]?.yearRatedThan || 100
        console.log(this.monthRatedThan, this.yearRatedThan)
        return
        this.$api.getRatedData({ token: this.token }).then((res) => {
          console.log(res)
          this.ratedData = res.data
        })
      })
    },
    getThisMonthAndThisYear() {
      let monthData = {
        ...this.queryParams,
        emodelId: this.emodelId,
        timeCategory: 'month',
        startDate: moment().startOf('month').format('YYYY-MM-DD') + ' 00:00:00',
        endDate: moment().endOf('month').format('YYYY-MM-DD') + ' 23:59:59'
      }
      this.$api.getModelEnergyDataList(monthData, { 'X-Access-Token': this.token }).then((res) => {
        this.monthLoading = false
        // this.ThisMonth.value = res.data.length ? res.data[0].value?.toFixed(2) ?? 0 : 0
        // 获取res.data中value的累加值
        this.ThisMonth.value = res.data.reduce((sum, e) => sum + e.value, 0).toFixed(2)
      })
      let yearData = {
        ...this.queryParams,
        emodelId: this.emodelId,
        timeCategory: 'year',
        startDate: moment().startOf('year').format('YYYY-MM-DD') + ' 00:00:00',
        endDate: moment().endOf('year').format('YYYY-MM-DD') + ' 23:59:59'
      }
      this.$api.getModelEnergyDataList(yearData, { 'X-Access-Token': this.token }).then((res) => {
        this.yearLoading = false
        // this.ThisYear.value = res.data.length ? res.data[0].value?.toFixed(2) ?? 0 : 0
        this.ThisYear.value = res.data.reduce((sum, e) => sum + e.value, 0).toFixed(2)
      })
    },
    // 获取空间信息
    getStructureTree() {
      return new Promise((resolve, reject) => {
        this.treeLoading = true
        this.$api.getStructureTree({}).then((res) => {
          this.treeLoading = false
          if (res.code == '200') {
            if (res.data.length) {
              this.spacesList = res.data
              this.spaceTreeData = transData(res.data, 'id', 'pid', 'children')
              this.defaultExpandedKeys = res.data.filter((item) => item.ssmType === 1 || item.ssmType === 2).map((item) => item.id)
              const ssmType2List = this.spacesList.filter((e) => e.ssmType == 2) || []
              if (ssmType2List && ssmType2List.length) {
                this.emodelId = Array.from(ssmType2List, (item) => item.id).join(',') || ''
              }
              this.resetForm()
            } else {
              this.spacesList = []
              this.spaceTreeData = []
            }
            resolve()
          } else {
            reject()
          }
        })
      })
    },
    // 获取能耗趋势图
    getEnergyTrendEchart(chartData) {
      const timeFormatter = this.activeDate == 'day' ? 'HH:mm' : 'MM/DD'
      const nameList = chartData.map((item) => moment(item.dataTime).format(timeFormatter))
      const valueList = chartData.map((item) => (item.value == null ? 0 : item.value))
      const getchart = echarts.init(document.getElementById('EnergyTrendEchart'))
      let option
      if (chartData.length) {
        option = {
          tooltip: {
            trigger: 'axis'
          },
          grid: {
            top: '5%',
            left: '1%',
            right: '1%',
            bottom: '6%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: nameList,
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            }
          },
          yAxis: [
            {
              type: 'value',
              axisLine: {
                show: false
              },
              axisTick: {
                show: false
              }
            }
          ],
          series: [
            {
              data: valueList,
              type: 'line',
              symbol: 'circle',
              symbolSize: 6,
              itemStyle: {
                color: '#FF9435'
              },
              lineStyle: {
                color: '#FF9435'
              },
              areaStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: 'rgba(255, 148, 53, 1)'
                    },
                    {
                      offset: 1,
                      color: 'rgba(255, 148, 53, 0)'
                    }
                  ])
                }
              }
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 获取能耗类型占比图
    getProportionTypesEchart(arr) {
      let that = this
      const nameList = Array.from(arr, (item) => item.name)
      const getchart = echarts.init(document.getElementById('ProportionTypesEchart'))
      let option
      if (arr.length) {
        option = {
          tooltip: {
            trigger: 'item'
          },
          legend: {
            show: false,
            orient: 'vertical',
            type: 'scroll',
            top: '65%',
            bottom: 1,
            itemWidth: 15,
            itemHeight: 15,
            // itemGap: 15,
            pageButtonPosition: 'start',
            data: nameList,
            formatter: function (name) {
              var oa = option.series[0].data
              var num = oa.reduce((sum, e) => sum + e.value, 0)
              for (var i = 0; i < option.series[0].data.length; i++) {
                if (name === oa[i].name) {
                  return ' ' + name + '    ' + ((oa[i].value / num) * 100).toFixed(2) + '%' + '     ' + oa[i].value + that.activeEnergyList.unit
                }
              }
            }
          },
          series: [
            {
              type: 'pie',
              // roseType: 'radius',
              // radius: ['40%', '60%'],
              center: ['50%', '50%'],
              data: arr,
              label: {
                normal: {
                  show: true,
                  formatter: '{b}\n{c}  {d}%',
                  color: '#414653',
                  lineHeight: 20
                }
              }
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 获取区域能耗排名图
    getRegionalRankingEchart(data) {
      data.sort((a, b) => {
        return a.value - b.value
      })
      const nameList = Array.from(data, ({ name }) => name)
      const getchart = echarts.init(document.getElementById('RegionalRankingEchart'))
      let option
      if (data.length) {
        option = {
          tooltip: {
            trigger: 'axis'
          },
          label: {
            show: true,
            position: 'right',
            color: '#121F3E',
            fontSize: '12px'
          },
          grid: {
            top: '4%',
            left: '4%',
            right: '10%',
            bottom: '6%',
            containLabel: true
          },
          xAxis: {
            type: 'value',
            splitNumber: 3
          },
          yAxis: [
            {
              type: 'category',
              data: nameList,
              axisLabel: {
                formatter: function (val) {
                  var strs = val.split('') // 字符串数组
                  var str = ''
                  // strs循环 forEach 每五个字符增加换行符
                  strs.forEach((item, index) => {
                    if (index % 7 === 0 && index !== 0) {
                      str += '\n'
                    }
                    str += item
                  })
                  return str
                }
              },
              axisLine: {
                show: false
              },
              axisTick: {
                show: false
              }
            }
          ],
          series: [
            {
              type: 'bar',
              barWidth: 10,
              data: data,
              itemStyle: {
                color: '#FF9435'
              }
            }
          ],
          dataZoom: [
            {
              orient: 'vertical',
              show: true,
              type: 'slider',
              start: 100,
              // end: 100,
              width: 8,
              left: '99%',
              borderColor: 'rgba(43,48,67,.1)',
              fillerColor: '#3562DB',
              zoomLock: true,
              showDataShadow: false,
              backgroundColor: 'rgba(43,48,67,.1)',
              showDetail: false,
              // realtime: true,
              filterMode: 'filter',
              handleIcon: 'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
              handleStyle: {
                color: '#6580b8',
                borderColor: '#6580b8'
              },
              maxValueSpan: 7,
              minValueSpan: 7,
              brushSelect: false
            },
            {
              type: 'inside',
              show: false,
              yAxisIndex: [0, 1],
              height: 8
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 获取区域能耗占比图
    getProportionRegionalEchart(echartData) {
      const totalNum = echartData.reduce((sum, e) => sum + Number(e.value) ?? 0, 0)
      let formatNumber = function (num) {
        return ((num / totalNum) * 100).toFixed(2) + '%'
      }
      const getchart = echarts.init(document.getElementById('ProportionRegionalEchart'))
      let option
      if (echartData.length) {
        option = {
          tooltip: {
            trigger: 'item',
            formatter: '{b} : {c} ({d}%)'
          },
          series: [
            {
              type: 'pie',
              // radius: ['40%', '60%'],
              center: ['50%', '50%'],
              data: echartData,
              hoverAnimation: false,
              itemStyle: {
                normal: {
                  borderColor: '#FFF',
                  borderWidth: 2
                }
              },
              labelLine: {
                normal: {
                  length: 20
                  // length2: 120,
                  // lineStyle: {
                  //   color: '#237CEC'
                  // }
                }
              },
              label: {
                normal: {
                  formatter: (params) => {
                    return '{name|' + params.name + '}{value|' + formatNumber(params.value) + '}'
                  },
                  // padding: [0, -100, 25, -100],
                  rich: {
                    name: {
                      fontSize: 12,
                      padding: [0, 10, 0, 0],
                      color: '#121f3e'
                    },
                    value: {
                      fontSize: 12,
                      // fontWeight: 'bold',
                      color: '#121f3e'
                    }
                  }
                }
              }
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 空间树点击事件
    handleTreeNodeClick(data) {
      this.isClickSearch = true
      this.queryParams.emodelId = data.id
      this.searchFormEvent()
    },
    handleEnergyTypeTabClick() {
      this.monthLoading = true
      this.yearLoading = true
      this.getThisMonthAndThisYear()
      this.getEcharsData()
      // this.getRated()
    },
    // table滚动加载
    loadMore() {
      console.log('loadMore')
      // if (this.currentPage >= this.totalNum) {
      // 当前页码数等于总页数的时候,提示没有更多数据了
      // this.tableLoading = false
      // this.nomore = true
      // } else {
      // 当数据没有加载完的时候,继续加载数据
      // this.loading = true
      // this.currentPage++ // 当前页数字加一
      // var data = { pageSize: 15, pageNum: this.currentPage }
      // 请求后台接口
      // museApi.recommendList(data).then((resp) => {
      //   var list = resp.data.list || ''
      //   this.tableData = this.tableData.concat(list)
      //   this.tableLoading = false
      // })
      // }
    },
    // 重置
    resetForm() {
      this.isClickSearch = false
      this.queryParams.emodelId = this.spaceTreeData[0].id
      this.activeDate = 'day'
      this.searchForm = {
        dayDate: moment().format('YYYY-MM-DD'),
        weekDate: '',
        monthDate: ''
      }
      this.searchFormEvent()
    },
    // 搜索
    searchFormEvent() {
      let startDate, endDate
      if (this.activeDate === 'day' && this.searchForm.dayDate) {
        console.log(this.searchForm.dayDate)
      } else if (this.activeDate === 'week' && this.searchForm.weekDate) {
        startDate = moment(this.searchForm.weekDate).subtract(1, 'days').format('YYYY-MM-DD')
        endDate = moment(startDate).add(6, 'days').format('YYYY-MM-DD')
        console.log(startDate, endDate)
      } else if (this.activeDate === 'month' && this.searchForm.monthDate) {
        startDate = moment(this.searchForm.monthDate).startOf('month').format('YYYY-MM-DD')
        endDate = moment(this.searchForm.monthDate).endOf('month').format('YYYY-MM-DD')
        console.log(startDate, endDate)
      } else if (this.activeDate === 'custom' && this.searchForm.custom) {
        startDate = this.searchForm.custom[0]
        endDate = this.searchForm.custom[1]
      } else {
        return this.$message.error('请输入查询条件')
      }
      this.queryParams.startDate = `${startDate ?? this.searchForm.dayDate} 00:00:00`
      this.queryParams.endDate = `${endDate ?? this.searchForm.dayDate} 23:59:59`
      this.getEcharsData()
    },
    getEcharsData() {
      this.EnergyTrendEchartLoading = true
      this.ProportionTypesEchartLoading = true
      this.RegionalRankingEchartLoading = true
      // 折线图  能耗趋势（kwh）
      console.log(this.activeDate)
      const timeCategory = this.activeDate == 'day' ? 'hour' : 'day'
      this.$api.getModelEnergyDataList({ timeCategory, energyIdCate: this.queryParams.energyId, ...this.queryParams }, { 'X-Access-Token': this.token }).then((res) => {
        let list = res.data.map((ele) => {
          return {
            ...ele,
            name: ele.energyName
          }
        })
        this.EnergyTrendEchartLoading = false
        console.log('--------------', list)
        this.getEnergyTrendEchart(list)
      })
      // 用电类型占比 饼图
      // 后期整理   总览传递剩余energyId  除总览外传单独energyId   status  1为总览   0为其他
      let obj = this.activeEnergyList.tabsData.find((ele) => ele.energyId == this.queryParams.energyId)
      let energyId = this.queryParams.energyId
      if (this.activeEnergyList.energyId != '蒸汽' && this.activeEnergyList.tabsData > 1) {
        energyId = obj.status
          ? this.activeEnergyList.tabsData
            .filter((ele) => !ele.status)
            .map((ele) => ele.energyId)
            .join(',')
          : this.queryParams.energyId
      }
      let data = {
        aggregation: 'sum',
        valueName: 'value',
        timeCategory: 'day',
        groupBy: 'energyId',
        ...this.queryParams,
        energyId
      }
      this.$api.getModelEnergyDataList(data, { 'X-Access-Token': this.token }).then((res) => {
        let list = res.data.map((ele) => {
          return {
            ...ele,
            name: ele.energyName
          }
        })
        this.ProportionTypesEchartLoading = false
        this.getProportionTypesEchart(list)
      })
      const ssmType3List = this.spacesList.filter((e) => e.ssmType == 3) || []
      // 区域用电占比  区域用电排行
      let pieData = {
        aggregation: 'sum',
        valueName: 'value',
        groupBy: 'emodelId',
        timeCategory: 'day',
        ...this.queryParams,
        emodelId: this.isClickSearch ? this.queryParams.emodelId : Array.from(ssmType3List, (item) => item.id).join(',') || '',
        energyIdCate: this.queryParams.energyId
        // energyIdCate: '2f971f3079fa1a86cf84760001574eb6,567cd67b1d0635cb7873ea0939ada40a,CM021'
      }
      this.$api.getModelEnergyDataList(pieData, { 'X-Access-Token': this.token }).then((res) => {
        console.log('this.=========================', this.queryParams.energyId)
        let listRegiona = []
        // 水电去除  综合急诊急救楼  && 北京世纪坛医院
        if (this.queryParams.energyId == 'SU035' || this.queryParams.energyId == 'CM020') {
          listRegiona = res.data.filter((ele) => {
            if (ele.emodelName != '综合急诊急救楼' && ele.emodelName != '北京世纪坛医院') {
              ele['name'] = ele.emodelName
              return true
            }
            return false
          })
          if (__PATH.VUE_APP_HOSPITAL_NODE_ENV === 'bjsjtyy') {
            listRegiona = res.data.filter((ele) => {
              if (ele.emodelName != '综合急诊急救楼' && ele.emodelName != '北京世纪坛医院') {
                ele['name'] = ele.emodelName
                return true
              }
              return false
            })
          } else if (__PATH.VUE_APP_HOSPITAL_NODE_ENV === 'outernet') {
            listRegiona = res.data.filter((ele) => {
              if (ele.emodelName != '中国医学科学院肿瘤医院深圳医院') {
                ele['name'] = ele.emodelName
                return true
              }
              return false
            })
          }
        } else {
          // 蒸汽只需要  综合急诊急救楼
          listRegiona = res.data.filter((ele) => {
            if (ele.emodelName == '综合急诊急救楼') {
              ele['name'] = ele.emodelName
              return true
            }
            return false
          })
        }
        // const listRegiona = res.data.map(ele => {
        //   return {
        //     ...ele,
        //     name: ele.emodelName,
        //     value: ele.value.toFixed(2)
        //   }
        // })
        this.RegionalRankingEchartLoading = false
        this.getRegionalRankingEchart(listRegiona)
        this.getProportionRegionalEchart(listRegiona)
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.page-container {
  ::v-deep .container-header {
    width: 100%;
    margin-left: 0;
  }
}
.tabs-header {
  padding: 0 8px 2px 20px;
  ::v-deep .el-tabs__header {
    .el-tabs__nav-wrap::after {
      display: none;
    }
    .el-tabs__nav-wrap .is-active {
      color: #3562db;
    }
    .el-tabs__item {
      font-size: 15px;
      font-family: 'PingFang SC-Regular', 'PingFang SC';
      font-weight: 400;
      color: #414653;
    }
  }
}
.box-content {
  padding-top: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .overview-statistics {
    height: 24%;
    display: flex;
    justify-content: space-between;
    .statistics-left,
    .statistics-right {
      height: 100%;
      width: calc(50% - 6px);
      background: #fff;
      border-radius: 4px;
    }
    .statistics-left {
      display: flex;
      justify-content: space-between;
      .statistics-left-box {
        height: 100%;
        width: 50%;
        padding: 10px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .statistics-left-title {
          font-size: 15px;
          font-family: 'PingFang SC-Medium', 'PingFang SC';
          font-weight: 500;
          color: #121f3e;
        }
        .statistics-left-value {
          font-size: 24px;
          font-family: 'PingFang SC-Regular', 'PingFang SC';
          font-weight: 400;
          color: #414653;
          span {
            &:first-child {
              font-size: 30px;
              font-family: Arial-Bold, Arial;
              font-weight: bold;
              color: #121f3e;
            }
            &:last-child {
              font-size: 15px;
              font-family: 'PingFang SC-Medium', 'PingFang SC';
              font-weight: 500;
              color: #ccced3;
              margin-left: 4px;
            }
          }
        }
        .statistics-left-progress {
          margin-top: 10px;
          ::v-deep .el-progress-bar__outer {
            border-radius: 0;
            .el-progress-bar__inner {
              border-radius: 0;
            }
          }
        }
        .statistics-left-progress-text {
          font-size: 14px;
          font-family: 'PingFang SC-Medium', 'PingFang SC';
          font-weight: 500;
          color: #121f3e;
          margin-top: 10px;
          display: flex;
          justify-content: space-between;
          span {
            &:first-child {
              margin-right: 10px;
            }
          }
        }
        .statistics-left-progress-value {
          display: flex;
          justify-content: space-between;
          div {
            span {
              &:first-child {
                font-size: 18px;
                font-family: Arial-Bold, Arial;
                font-weight: bold;
                color: #121f3e;
              }
              &:last-child {
                font-size: 15px;
                font-family: 'PingFang SC-Medium', 'PingFang SC';
                font-weight: 500;
                color: #ccced3;
                margin-left: 4px;
              }
            }
          }
        }
      }
    }
    .statistics-right {
      display: flex;
      justify-content: space-between;
      .statistics-right-statistics {
        width: 13%;
        height: 70%;
        margin: auto;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        text-align: center;
        box-sizing: border-box;
        span {
          &:first-child {
            font-size: 30px;
          }
          &:nth-child(2) {
            font-size: 30px;
            font-family: Arial-Bold, Arial;
            font-weight: bold;
            color: #fa403c;
          }
          &:last-child {
            font-size: 14px;
            font-family: 'PingFang SC-Medium', 'PingFang SC';
            font-weight: 500;
            color: #414653;
          }
        }
      }
      .statistics-right-table {
        width: 87%;
        height: 100%;
        padding: 10px;
        ::v-deep .el-table {
          color: #121f3e;
          font-family: 'PingFang SC-Regular', 'PingFang SC';
          .el-table__header-wrapper .el-table__cell {
            padding: 10px 0;
          }
          .el-table__body-wrapper .el-table__cell {
            padding: 8px 0;
          }
        }
      }
    }
  }
  .search-aside {
    height: 60px;
    line-height: 60px;
    background: #fff;
    border-radius: 4px;
    padding: 0 20px;
    .search-aside-item {
      display: inline-block;
      font-size: 14px;
      padding: 0 22px;
      height: 32px;
      line-height: 32px;
      font-family: PingFangSC-Regular;
      color: rgba(65, 70, 83, 1);
      // border: 1px solid $color-primary;
      background: #fff;
      margin-right: 10px;
      border-radius: 4px;
      cursor: pointer;
      &:hover,
      &:focus {
        color: rgba(53, 98, 219, 1);
        font-family: PingFangSC-Regular;
        // border-color: $color-primary;
        background-color: rgba(230, 239, 252, 1);
        font-weight: 500;
      }
    }
    .search-aside-item-active {
      color: rgba(53, 98, 219, 1);
      font-family: PingFangSC-Regular;
      // border-color: $color-primary;
      background-color: rgba(230, 239, 252, 1);
      font-weight: 500;
    }
  }
  .overview-content {
    height: calc(76% - 80px);
    display: flex;
    justify-content: space-between;
    .content-left,
    .content-right {
      height: 100%;
      border-radius: 4px;
      // width: calc(50% - 6px);
    }
    .content-left {
      background: #fff;
      width: calc(15% - 6px);
      padding: 10px;
      box-sizing: border-box;
      overflow: auto;
    }
    .content-right {
      width: calc(85% - 6px);
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .content-right-top,
      .content-right-bottom {
        border-radius: 4px;
        width: 100%;
      }
      .content-right-top {
        background: #fff;
        height: calc(45% - 6px);
      }
      .content-right-bottom {
        height: calc(55% - 6px);
        display: flex;
        justify-content: space-between;
        .content-right-bottom-left,
        .content-right-bottom-center,
        .content-right-bottom-right {
          background: #fff;
          border-radius: 4px;
          width: calc(33.3% - 8px);
          .title_right {
            flex: 1;
            justify-content: flex-end;
            display: flex;
            span {
              font-size: 12px;
              font-weight: 400;
              color: #595a5f;
              line-height: 14px;
            }
          }
        }
      }
    }
  }
  #EnergyTrendEchart,
  #ProportionTypesEchart,
  #RegionalRankingEchart,
  #ProportionRegionalEchart {
    width: 100%;
    height: 100%;
  }
}
</style>
