<!-- 合同档案字典开发较为急迫，暂时copy工程字典，后续字典需要统一优化封装公共字典文件，根据不同步服不同字典类型获取 -->
<script>
import tableListMixin from '@/mixins/tableListMixin'
import { UsingStatusOptions } from '@/views/operationPort/constant'
export default {
  name: 'DictionaryList',
  components: {
    DictionaryValueEdit: () => import('./components/DictionaryValueEdit')
  },
  filters: {
    statusFilter(status) {
      return UsingStatusOptions.find((it) => it.value == status)?.label ?? '-'
    }
  },
  mixins: [tableListMixin],
  data() {
    return {
      currentKey: -1,
      treeSearchKeyWord: '',
      searchForm: {
        name: ''
      },
      treeData: [],
      treeLoadingStatus: false,
      tableData: [],
      tableLoadingStatus: false,
      dialog: {
        show: false,
        id: 0,
        readonly: false // 查看模式
      },
      currentImage: '' // 当前预览的图片
    }
  },
  computed: {
    OperateType() {
      return {
        Edit: 'edit',
        Delete: 'delete',
        Create: 'create',
        View: 'view',
        Child: 'child'
      }
    }
  },
  mounted() {
    this.getTreeData()
  },
  methods: {
    // 获取树数据
    getTreeData() {
      this.treeLoadingStatus = true
      Promise.resolve()
        .then(() => {
          this.treeData = [
            {
              id: 1,
              label: '合同分类'
            },
            {
              id: 2,
              label: '合同类别'
            },
            {
              id: 3,
              label: '所属分类'
            },
            {
              id: 4,
              label: '证书名称'
            }
          ]
          const [first] = this.treeData
          if (first) {
            this.currentKey = first.id
            this.onSearch()
            this.$nextTick(() => {
              this.$refs.treeRef.setCurrentKey(first.id)
            })
          }
        })
        .finally(() => {
          this.treeLoadingStatus = false
        })
    },
    // 搜索树
    onTreeSearch() {
      this.currentKey = ''
      this.getTreeData()
    },
    // 当树点击被点击时
    onTreeNodeClick(data) {
      if (data.id !== this.currentKey) {
        this.currentKey = data.id
        this.onReset()
      }
    },
    // 获取列表数据
    getDataList() {
      this.tableLoadingStatus = true
      const params = {
        pageSize: this.pagination.size,
        pageNo: this.pagination.current,
        name: this.searchForm.name,
        type: this.currentKey // 固定参数，工程类型
      }
      this.$api.fileManagement
        .getDictConfigByPage(params)
        .then((res) => {
          if (res.code === '200') {
            this.tableData = res.data.records
            this.pagination.total = res.data.total
          } else {
            throw res.msg || res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取列表数据失败'))
        .finally(() => {
          this.tableLoadingStatus = false
        })
    },
    // 表单搜索按钮点击
    onSearch() {
      this.pagination.current = 1
      this.getDataList()
    },
    // 表单重置按钮点击
    onReset() {
      this.$refs.formRef.resetFields()
      this.onSearch()
    },
    // 列表操作相关的事件绑定
    onOperate(row, command) {
      switch (command) {
        case this.OperateType.Delete:
          // 删除非启用的字典数据
          if (row.presetsType === 1) {
            this.$message.error('预设的字典不允许删除！')
          } else if (row.status === 1) {
            this.$message.error('启用的字典不允许删除！')
          } else {
            this.$confirm('删除后将无法恢复，是否确定删除？', '信息提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消'
            }).then(() => this.doDelete(row.id))
          }
          break
        default:
          this.dialog.id = row?.id || 0
          this.dialog.readonly = command === this.OperateType.View
          this.dialog.show = true
          break
      }
    },
    // 删除一行数据
    doDelete(id) {
      this.tableLoadingStatus = true
      this.$api.fileManagement
        .deleteDictData({ id })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('已删除')
            this.getDataList()
          } else {
            throw res.message || '删除失败'
          }
        })
        .catch((msg) => msg && this.$message.error(msg))
        .finally(() => (this.tableLoadingStatus = false))
    },
    // 点击图片更换预览地址
    onImageClick(e) {
      this.currentImage = e.target.currentSrc ?? ''
    }
  }
}
</script>
<template>
  <PageContainer class="dictionary-list">
    <template #content>
      <div class="dictionary-list__left">
        <div class="dictionary-list__left__header">
          <el-input v-model="treeSearchKeyWord" placeholder="输入关键字搜索" suffix-icon="el-icon-search"></el-input>
        </div>
        <el-tree ref="treeRef" node-key="id" :data="treeData" :current-node-key="currentKey"
          :expand-on-click-node="false" default-expand-all highlight-current class="dictionary-list__tree"
          @node-click="onTreeNodeClick"></el-tree>
      </div>
      <div class="dictionary-list__right">
        <div class="dictionary-list__right__header">
          <el-form ref="formRef" :model="searchForm" class="dictionary-list__search" inline
            @submit.native.prevent="onSearch">
            <el-form-item prop="name">
              <el-input v-model="searchForm.name" clearable filterable placeholder="搜索名称"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onSearch">查询</el-button>
              <el-button type="primary" plain @click="onReset">重置</el-button>
            </el-form-item>
          </el-form>
          <div class="dictionary-list__right__actions">
            <el-button type="primary" @click="onOperate(undefined, OperateType.Create)">添加字典值</el-button>
          </div>
        </div>
        <div class="dictionary-list__table">
          <el-table v-loading="tableLoadingStatus" height="100%" :data="tableData" border stripe table-layout="auto"
            class="tableAuto" row-key="id">
            <el-table-column label="名称" prop="name" show-overflow-tooltip></el-table-column>
            <el-table-column label="编码" prop="code" show-overflow-tooltip></el-table-column>
            <el-table-column label="颜色" prop="colour" show-overflow-tooltip>
              <template #default="{ row }">
                <span v-if="row.colour" class="dictionary-list__table__color"
                  :style="{ backgroundColor: row.colour }"></span>
                <span>{{ row.colour || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="图片" prop="image" show-overflow-tooltip class-name="dictionary-list__table__col-img">
              <template #default="{ row }">
                <el-image v-if="row.pictureUrl" :src="$tools.imgUrlTranslation(row.pictureUrl)"
                  :preview-src-list="[currentImage]" fit="cover" @click="onImageClick"></el-image>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="状态" prop="status" show-overflow-tooltip>
              <template #default="{ row }">
                <span class="dictionary-list__tag" :class="`dictionary-list__tag--${row.status}`">
                  {{ row.status | statusFilter }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="预设" prop="preset" show-overflow-tooltip
              :formatter="(row) => (row.presetsType === 1 ? '是' : '否')"></el-table-column>
            <el-table-column label="操作">
              <template #default="{ row }">
                <el-button type="text" @click="onOperate(row, OperateType.View)">查看</el-button>
                <el-button type="text" :disabled="row.presetsType === 1" @click="onOperate(row, OperateType.Edit)"> 编辑
                </el-button>
                <el-dropdown :disabled="row.presetsType === 1" @command="(command) => onOperate(row, command)">
                  <el-button :disabled="row.presetsType === 1" type="text" style="margin-left: 10px">更多</el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :command="OperateType.Delete"> 删除 </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination class="dictionary-list__pagination" :current-page="pagination.page"
          :page-sizes="pagination.pageSizeOptions" :page-size="pagination.size" :layout="pagination.layoutOptions"
          :total="pagination.total" @size-change="paginationSizeChange" @current-change="paginationCurrentChange">
        </el-pagination>
      </div>
      <!--字典值编辑-->
      <DictionaryValueEdit :visible.sync="dialog.show" v-bind="dialog" :dictType="currentKey" @success="getDataList" />
    </template>
  </PageContainer>
</template>
<style scoped lang="scss">
.dictionary-list {
  ::v-deep(> .container-content) {
    display: flex;
    background-color: #fff;
  }
  &__left {
    width: 280px;
    border-right: solid 1px #eee;
    display: flex;
    flex-flow: column nowrap;
    &__header {
      padding: 16px;
      line-height: 40px;
    }
    &::v-deep(.el-tree) {
      height: calc(100% - 64px);
      overflow: auto;
      padding: 0 16px 16px 16px;
      .el-tree-node__content {
        line-height: 36px;
        height: 36px;
      }
      .el-tree-node.is-current > .el-tree-node__content {
        color: #3562db;
        background: #e6effc;
      }
    }
  }
  &__right {
    flex: 1;
    overflow: hidden;
    padding: 16px;
    display: flex;
    flex-flow: column nowrap;
  }
  &__right__header {
    display: flex;
    justify-content: space-between;
  }
  &__right__actions {
    line-height: 40px;
  }
  &__table {
    margin-top: -10px;
    flex: 1;
  }
  &__pagination {
    margin-top: 10px;
  }
  &__tag {
    // 停用
    &--0 {
      --color: #f64646;
    }
    // 启用
    &--1 {
      --color: #00b42a;
    }
    &:before {
      content: '';
      display: inline-block;
      vertical-align: middle;
      margin-right: 4px;
      height: 4px;
      width: 4px;
      border-radius: 4px;
      background-color: var(--color);
    }
  }
  &__table__color {
    display: inline-block;
    height: 16px;
    width: 16px;
    vertical-align: text-top;
    margin-right: 4px;
  }
  ::v-deep(.dictionary-list__table__col-img) {
    padding: 0;
    .cell {
      padding: 4px;
    }
    .el-image {
      height: 38px;
      .el-image__error {
        background-color: transparent;
      }
    }
  }
}
</style>
