<!-- 运行率占比弹窗 -->
<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    title="切换提示"
    width="30%"
    :visible.sync="visible"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <div class="switch_content">
      <div class="switch_display">
        <p>您正在执行以下操作</p>
        <div class="switch_display_btn">
          <div :class="false ? 'purple_color' : 'yellow_color'">{{false ? '自动' : '手动'}}</div>
          <i class="el-icon-right"></i>
          <div :class="false ? 'yellow_color' : 'purple_color'">{{false ? '手动' : '自动'}}</div>
        </div>
      </div>
      <div class="switch_reason">
        <div class="switch_reason_prompt">
          <p>请确认原因：</p>
          <div class="prompt_arr">
            <el-tag 
              v-for="item in promptArr" 
              :key="item" 
              :type="prompt == item ? '' : 'info'" 
              :style="{ color : prompt == item ? '#5188FC' : ''}"
              class="prompt_item"
              @click="textarea = prompt = item"
            >
              {{ item }}
            </el-tag>
          </div>
        </div>
        <el-input
          v-model="textarea"
          type="textarea"
          :autosize="{ minRows: 3, maxRows: 4}"
          placeholder="请输入原因">
        </el-input>
      </div>
    </div>
    <span slot="footer">
      <el-button plain @click="closeDialog">关 闭</el-button>
      <el-button type="primary" @click="submitForm">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script lang="jsx">
export default {
  components: {
    
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    projectCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      textarea: '',
      promptArr: ['天气突变', '检修'],
      prompt: ''
    }
  },
  mounted() {
    
  },
  methods: {
    submitForm() {

    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', !this.visible)
    }
  }
}
</script>
<style lang="scss" scoped>
.switch_content{
  width: 100%;
  background: #FFF;
  padding: 16px;
  border-radius: 8px;
}
.switch_reason{
  margin-top: 30px;
  .switch_reason_prompt{
    display: flex;
    p{
      margin: 0;
      margin-top: 5px;
      font-size: 14px;
      font-family: NotoSansHans-Medium, NotoSansHans;
      font-weight: 500;
      color: #606266;
    }
    .prompt_arr{
      flex: 1;
      display: flex;
      flex-wrap: wrap;
      .prompt_item{
        margin: 0 10px 10px 0;
        cursor: pointer;
      }
    }
  }
}
.switch_display{
  width: 100%;
  background: #F2F6FF;
  border: 1px dotted #5188FC;
  display: flex;
  flex-direction: column;
  align-items: center;
  p{
    font-size: 14px;
    font-family: HarmonyOS_Sans_SC;
    color: #606266;
    margin: 18px 0 26px 0;
  }
  .switch_display_btn{
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    div{
      width: 80px;
      height: 32px;
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      font-family: NotoSansHans-Medium, NotoSansHans;
      font-weight: 500;
      color: #FFFFFF;
    }
    .yellow_color{
      background: #FFBB52;
    }
    .purple_color{
      background: #8671DE;
    }
    .el-icon-right{
      font-size: 32px;
      font-weight: bold;
      color: #3562db;
      margin: 0 32px;
    }
  }
}
</style>
