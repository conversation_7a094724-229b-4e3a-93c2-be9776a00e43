<template>
  <PageContainer>
    <div slot="content" ref="contentRef">
      <div class="aside-box">
        <el-calendar v-model="calenderValue">
          <template slot="dateCell" slot-scope="{ data }">
            <div style="margin: 0">
              {{ data.day.split('-').slice(2).join() }}<br />
              <div v-for="(i, index) in dayTime" :key="index">
                <div v-if="data.day == i" class="budge"></div>
              </div>
            </div>
          </template>
        </el-calendar>
      </div>
      <div class="main-box">
        <div class="search-box">
          <el-select v-model="searchParams.reportType" placeholder="报告类型">
            <el-option v-for="item in reportTypeList" :key="item.label" :label="item.label" :value="item.value"> </el-option>
          </el-select>
          <el-date-picker v-model="searchParams.dateVal" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd">
          </el-date-picker>
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="searchForm">查询</el-button>
          <el-button type="primary" class="add-btn" icon="el-icon-plus" @click="creatReport">创建报告</el-button>
        </div>
        <el-table :data="tableData" border style="width: 100%" height="88%">
          <el-table-column label="序号" type="index" width="50" align="center" :resizable="false"></el-table-column>
          <el-table-column prop="name" label="报告名称" show-overflow-tooltip :resizable="false"> </el-table-column>
          <el-table-column prop="reportType" label="报告类型" show-overflow-tooltip :resizable="false"> </el-table-column>
          <el-table-column prop="reportDate" label="报告时间" show-overflow-tooltip :resizable="false"> </el-table-column>
          <el-table-column prop="createDate" label="生成时间" show-overflow-tooltip :resizable="false"> </el-table-column>
          <el-table-column label="操作" show-overflow-tooltip :resizable="false">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="downloadFile(scope.row)">下载</el-button>
              <el-button type="text" size="small" @click="openEditDialog(scope.row)">编辑</el-button>
              <el-button class="delbtn" type="text" size="small" @click="delTemplate(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :current-page="pageNo"
          :page-sizes="[15, 30, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
        <el-dialog v-loading="loading" title="创建报告" :visible.sync="dialogVisible" width="50%" :before-close="handleClose">
          <span class="time">{{ dateToString(new Date()) }}</span>
          <el-select v-model="reportType" placeholder="请选择报告类型">
            <el-option v-for="item in reportTypeList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
          <el-date-picker v-if="reportType == '0'" v-model="startTime" type="date" value-format="yyyy-MM-dd" placeholder="选择日期"> </el-date-picker>
          <el-date-picker
            v-if="reportType == '1'"
            id="getWeek"
            v-model="stage"
            type="week"
            format="第W周"
            :picker-options="{ firstDayOfWeek: 1 }"
            placeholder="选择周"
            @change="weekChanged"
          >
          </el-date-picker>
          <el-date-picker v-if="reportType == '1' && stage" v-model="timeRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
          </el-date-picker>
          <el-date-picker v-if="reportType == '2' || reportType == '3'" v-model="startTime" type="month" placeholder="选择月" value-format="yyyy-MM"> </el-date-picker>
          <el-select v-if="startTime && reportType == '2'" v-model="halfMoon" placeholder="请选择周期">
            <el-option label="上半月" value="1"> </el-option>
            <el-option label="下半月" value="2"> </el-option>
          </el-select>
          <el-select v-if="reportType == '4'" v-model="quarter" placeholder="请选择季度">
            <el-option label="第一季度" value="1"> </el-option>
            <el-option label="第二季度" value="2"> </el-option>
            <el-option label="第三季度" value="3"> </el-option>
            <el-option label="第四季度" value="4"> </el-option>
          </el-select>
          <el-date-picker v-if="reportType == '5'" v-model="year" type="year" placeholder="选择年" value-format="yyyy"> </el-date-picker>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" plain @click="handleClose">取 消</el-button>
            <el-button type="primary" @click="create">确 定</el-button>
          </span>
        </el-dialog>
        <el-dialog title="修改名称" :visible.sync="dialogVisible2" width="30%" :before-close="dialogClosed2">
          <div class="dialog-contet">
            <span>文件名称</span>
            <el-input v-model="rename" placeholder="请输入内容"></el-input>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="editTemplate">确 定</el-button>
            <el-button type="primary" plain @click="dialogClosed2">取 消</el-button>
          </span>
        </el-dialog>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import axios from 'axios'
import qs from 'qs'
import moment from 'moment'
export default {
  name: 'reportManagement',
  data() {
    return {
      reportType: '',
      reportTypeList: [
        // { label: '日报', value: '0' },
        { label: '周报', value: '1' },
        // { label: '半月报', value: '2' },
        { label: '月报', value: '3' },
        { label: '季报', value: '4' },
        { label: '年报', value: '5' }
      ],
      dateVal: '',
      tableData: [],
      pageNo: 1,
      pageSize: 15,
      total: 0,
      calenderValue: new Date(),
      dialogVisible: false,
      dialogVisible2: false,
      startTime: '',
      stage: '',
      timeRange: '',
      halfMoon: '',
      quarter: '',
      editId: '',
      rename: '',
      loading: false,
      searchParams: {
        reportType: '',
        dateVal: ''
      },
      dayTime: [],
      year: ''
    }
  },
  watch: {
    calenderValue(val) {
      this.getList2()
    }
  },
  created() {},
  mounted() {
    this.getList()
  },
  methods: {
    getList() {
      let params = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        type: 2,
        reportType: this.searchParams.reportType || ''
      }
      if (this.searchParams.dateVal[0] && this.searchParams.dateVal[1]) {
        params.startTime = this.searchParams.dateVal[0] + '+'
        params.endTime = '+' + this.searchParams.dateVal[1]
      }
      this.$api.getTemplateList(params).then((res) => {
        this.tableData = res.rows
        this.total = res.total
        let arr = []
        res.rows.forEach((item) => {
          arr.push(item.createDate.split(' ')[0])
        })
        arr = [...new Set(arr)]
        this.dayTime = arr
      })
    },
    getList2() {
      let params = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        type: 2,
        reportType: '',
        startTime: moment(this.calenderValue).format('YYYYMMDD') + '000000',
        endTime: moment(this.calenderValue).format('YYYYMMDD') + '235959'
      }
      this.$api.getTemplateList(params).then((res) => {
        this.tableData = res.rows
        this.total = res.total
      })
    },
    resetForm() {
      this.searchParams = {
        reportType: '',
        dateVal: ''
      }
      this.getList()
    },
    searchForm() {
      this.getList()
    },
    creatReport() {
      this.dialogVisible = true
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.pageNo = val
      this.getList()
    },
    handleClose() {
      this.reportType = ''
      this.startTime = ''
      this.stage = ''
      this.timeRange = ''
      this.halfMoon = ''
      this.quarter = ''
      this.dialogVisible = false
    },
    dateToString(date) {
      return moment(date).format('YYYY年MM月DD日')
    },
    create() {
      if (!this.reportType) {
        this.$message.error('请选择报告类型')
        return
      }
      let params = {
        reportType: this.reportType,
        startTime: this.startTime,
        endTime: '',
        stage: '',
        halfMoon: '',
        quarter: ''
      }
      if (this.reportType == '0') {
        params.endTime = moment(this.startTime).add(1, 'days').format('YYYY-MM-DD')
      }
      if (this.stage && this.reportType == '1') {
        params.stage = document.querySelector('#getWeek').value
        params.startTime = this.timeRange[0]
        params.endTime = this.timeRange[1]
      }
      if (this.reportType == '2') {
        params.halfMoon = this.halfMoon
      }
      if (this.reportType == '4') {
        params.quarter = this.quarter
        if (this.quarter == '1') {
          params.startTime = moment(moment().format('YYYY-01-01')).format('YYYY-MM-DD')
          params.endTime = moment(moment().format('YYYY-03-31')).format('YYYY-MM-DD')
        } else if (this.quarter == '2') {
          params.startTime = moment(moment().format('YYYY-04-01')).format('YYYY-MM-DD')
          params.endTime = moment(moment().format('YYYY-06-30')).format('YYYY-MM-DD')
        } else if (this.quarter == '3') {
          params.startTime = moment(moment().format('YYYY-07-01')).format('YYYY-MM-DD')
          params.endTime = moment(moment().format('YYYY-09-30')).format('YYYY-MM-DD')
        } else if (this.quarter == '4') {
          params.startTime = moment(moment().format('YYYY-10-01')).format('YYYY-MM-DD')
          params.endTime = moment(moment().format('YYYY-12-31')).format('YYYY-MM-DD')
        }
      }
      if (this.reportType == '5') {
        params.startTime = this.year
        params.quarter = '1'
      }
      // let formData = new FormData()
      // Object.keys(params).forEach((key) => {
      //   const value = params[key]
      //   if (Array.isArray(value)) {
      //     value.forEach((subValue, i) => formData.append(key + `[${i}]`, subValue))
      //   } else {
      //     formData.append(key, params[key])
      //   }
      // })
      this.loading = true
      let menuList = this.$store.state.menu.routes
      let firstTitle = ''
      let routeList = []
      this.$router.currentRoute.matched
        .filter((item) => item.name)
        .forEach((el) => {
          routeList.push(el.meta.title)
        })
      if (menuList.length) {
        firstTitle = menuList[this.$store.state.menu.headerActived].meta.title
        routeList.unshift(firstTitle)
      }
      axios
        .post(__PATH.VUE_IOMS_API + 'newReportExport/createReportFile', qs.stringify(params), {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded;charset-utf-8',
            Authorization: 'Bearer ' + this.$store.state.user.token,
            'operation-type': 1,
            'operation-content': encodeURIComponent(routeList.join(','))
          }
        })
        .then((res) => {
          this.loading = false
          if (res.data.success) {
            this.$message.success(res.data.msg)
            this.handleClose()
            this.getList()
          } else {
            this.$message.error(res.data.msg)
          }
        })
    },
    weekChanged(e) {
      let weekOfday = moment(e).format('E')
      let Monday = moment(e)
        .subtract(weekOfday - 1, 'days')
        .format('YYYY-MM-DD')
      let Sunday = moment(e)
        .add(7 - weekOfday, 'days')
        .format('YYYY-MM-DD')
      this.timeRange = [Monday, Sunday]
    },
    downloadFile(row) {
      window.location.href = this.$tools.imgUrlTranslation(row.minioUrl)
    },
    openEditDialog(row) {
      this.editId = row.id
      this.rename = row.name
      this.dialogVisible2 = true
    },
    async delTemplate(row) {
      const confirmRes = await this.$confirm('此操作将永久删除该模板, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).catch((err) => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
      if (confirmRes === 'confirm') {
        this.$api.delTemplate({ id: row.id }, { 'operation-type': 3, 'operation-id': row.id, 'operation-name': row.name }).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.getList()
          } else {
            this.$message.error(res.msg)
          }
        })
      }
    },
    dialogClosed2() {
      this.rename = ''
      this.dialogVisible2 = false
    },
    editTemplate() {
      if (this.rename === '') {
        this.$message.error('请输入报告名称')
        return
      }
      let params = {
        id: this.editId,
        name: this.rename
      }
      this.$api.renameTemplate(params, { 'operation-type': 2, 'operation-id': params.id, 'operation-name': params.name }).then((res) => {
        if (res.success) {
          this.dialogVisible2 = false
          this.$message.success('修改成功')
          this.getList()
        } else {
          this.$message.error(res.msg)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.container-content > div {
  display: flex;
  justify-content: space-between;
  height: 100%;
  background-color: #fff;
  padding: 16px;
}
.container-content > div > div {
  height: 100%;
}
.aside-box {
  width: 26%;
  // background-color: #faf9fc;
}
.main-box {
  width: 72%;
  position: relative;
}
.add-btn {
  position: absolute;
  top: 1px;
  right: 0;
}
.el-select {
  margin-right: 16px;
}
.el-table {
  margin-top: 16px;
}
.el-pagination {
  margin-top: 16px;
}
::v-deep .el-calendar-day {
  height: 50px;
}
.search-box {
  display: flex;
}
.el-date-editor {
  margin-right: 16px;
}
::v-deep .el-date-editor .el-range__icon {
  line-height: 24px;
}
.time {
  display: inline-block;
  width: 100%;
  text-align: right;
}
::v-deep .el-dialog__body {
  padding-top: 8px;
  min-height: 280px;
}
.delbtn {
  color: #fa403c !important;
}
.budge {
  width: 8px;
  height: 8px;
  background: #3562db;
  border-radius: 50%;
  margin: 0 auto;
  margin-top: 10px;
}
</style>
