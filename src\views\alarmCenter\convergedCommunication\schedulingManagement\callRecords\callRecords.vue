<template>
  <PageContainer>
    <div slot="header">
      <div class="searchForm">
        <div class="search-box">
          <el-select v-model="filterInfo.callType" filterable placeholder="呼叫类型" class="ml-16">
            <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
          <el-date-picker v-model="timeLine" type="daterange" range-separator="至" start-placeholder="开始时间"
            end-placeholder="结束时间" format="yyyy-MM-dd" value-format="yyyy-MM-dd" class="ml-16"></el-date-picker>
          <div class="ml-16">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button type="primary" plain @click="reset">重置</el-button>
          </div>
        </div>
      </div>
    </div>
    <div slot="content" ref="contentRef" class="table-box">
      <div class="tableContainer">
        <el-table v-loading="tableLoading" height="calc(100% - 56px)" :data="tableData" border stripe>
          <el-table-column type="selection" width="55"> </el-table-column>
          <el-table-column prop="callTypeVal" label="呼叫类型" width="180" align="center"></el-table-column>
          <el-table-column prop="startTime" label="通话开始时间" align="center"></el-table-column>
          <el-table-column prop="endTime" label="通话结束时间" align="center"></el-table-column>
          <el-table-column prop="timeDuration" label="通话时长" align="center"></el-table-column>
          <el-table-column prop="connectCount" label="通话人数" align="center"></el-table-column>
          <el-table-column width="280" label="操作" align="center">
            <template slot-scope="scope">
              <div class="btns">
                <span @click="handleOperation('detail', scope.row)"> 详情 </span>
                <span v-if="scope.row.callType=='1'||scope.row.callType=='2'"
                  @click="handleOperation('download',scope.row)"> 下载录音 </span>
                <span v-if="scope.row.callType=='7'" @click="handleOperation('download',scope.row)"> 下载视频 </span>
                <span @click="handleOperation('record', scope.row.id)"> 报警记录 </span>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination :current-page="pagination.pageNum" :page-sizes="[15, 30, 50, 100]"
          :page-size="pagination.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="pageTotal"
          @size-change="handleSizeChange" @current-change="handleCurrentChange">
        </el-pagination>
      </div>
      <!--通话记录 -->
      <template v-if="callRecordDialogShow">
        <SelectCallDetailDialog :callRecordDialogShow="callRecordDialogShow" :itemInfo="recordInfo"
          @closeCallRecordDialog="closeCallRecordDialog" />
      </template>
      <!--报警记录 -->
      <template v-if="alarmRecordDialogShow">
        <SelectAlarmRecordDialog :alarmRecordDialogShow="alarmRecordDialogShow" :itemInfo="alarmInfo"
          @closeAlarmRecordDialog="closeAlarmRecordDialog" />
      </template>
      <!--文件 -->
      <template v-if="callFileDialogShow">
        <SelectCallFileDialog :callFileDialogShow="callFileDialogShow" :callFileList="fileList" :fileTitle="fileTitle"
          @closeFileDialog="closeFileDialog" @downLoadCallFile="downLoadCallFile" />
      </template>
    </div>
  </PageContainer>
</template>
<script>
import SelectCallFileDialog from './components/callFile.vue'
import SelectCallDetailDialog from './components/callDetail.vue'
import SelectAlarmRecordDialog from '../alarmRecord/components/alarmDetail.vue'
import store from '@/store/index'
import axios from 'axios'
export default {
  name: 'callRecords',
  components: {
    SelectCallFileDialog,
    SelectCallDetailDialog,
    SelectAlarmRecordDialog
  },
  data() {
    return {
      tableLoading: false,
      callFileDialogShow: false, // 通话纪录文件
      callRecordDialogShow: false, // 通话记录
      alarmRecordDialogShow: false, // 报警纪录
      tableData: [],
      filterInfo: {
        callType: '',
        startTime: '',
        endTime: ''
      },
      timeLine: [],
      pagination: {
        pageSize: 15,
        pageNum: 1
      },
      typeList: [
        {
          value: 0,
          label: '单人语音呼叫'
        },
        {
          value: 1,
          label: '多人语音会议'
        },
        {
          value: 2,
          label: '视频呼叫'
        }
      ],
      pageTotal: 0,
      fileList: [], // 通话文件列表
      recordInfo: {}, // 通话纪录详情
      alarmInfo: {},
      fileTitle: ''
    }
  },
  mounted() {
    this.getTableData()
  },
  methods: {
    getTableData() {
      this.tableLoading = true
      let data = {
        ...this.pagination,
        ...this.filterInfo
      }
      if (this.timeLine && this.timeLine.length > 0) {
        data.startTime = this.timeLine[0] + ' 00:00:00'
        data.endTime = this.timeLine[1] + ' 23:59:59'
      } else {
        data.startTime = ''
        data.endTime = ''
      }
      this.$api
        .callRecordsList(data)
        .then((res) => {
          this.tableLoading = false
          if (res.code == 200) {
            this.tableData = res.data ? res.data.records : []
            this.pageTotal = res.data ? res.data.total : 0
          } else if (res.message) {
            this.tableData = []
            this.pageTotal = 0
            this.$message.error(res.message)
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    search() {
      this.pagination.pageNum = 1
      this.getTableData()
    },
    reset() {
      this.filterInfo = {
        callType: '',
        startTime: '',
        endTime: ''
      }
      this.timeLine = []
      this.pagination.pageNum = 1
      this.pageTotal = 0
      this.getTableData()
    },
    handleCurrentChange(val) {
      this.pagination.pageNum = val
      this.getTableData()
    },
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.pagination.pageNum = 1
      this.getTableData()
    },
    handleOperation(type, row) {
      if (type === 'detail') {
        this.callRecordDialogShow = true
        this.recordInfo = JSON.parse(JSON.stringify(row))
      } else if (type === 'download') {
        this.getCallFileData(row)
      } else {
        let data = {
          uuid: row.alarmUuid,
          pageSize: 1,
          pageNum: 1
        }
        this.$api
          .getAlarmRecordData(data)
          .then((res) => {
            if (res.code == 200) {
              this.alarmInfo = res.data.records[0]
            }
          })
        this.alarmRecordDialogShow = true
      }
    },
    // 通话纪录详情弹窗
    closeCallRecordDialog() {
      this.callRecordDialogShow = false
    },
    // 通话纪录文件弹窗关闭
    closeFileDialog() {
      this.callFileDialogShow = false
    },
    // 获取通话文件列表
    getCallFileData(row) {
      if (row.callType == '7') {
        this.fileTitle = '录像'
      } else if (row.callType == '1' || row.callType == '2') {
        this.fileTitle = '录音'
      }
      let params = {
        uuid: row.uuid,
        callType: row.callType
      }
      this.$api.fileQuery(params).then(res => {
        if (res.code == '200') {
          this.fileList = JSON.parse(res.data)
          this.callFileDialogShow = true
        }
      })
    },
    // 下载通话文件
    downLoadCallFile(val) {
      let cacheInfo = JSON.parse(sessionStorage.getItem('LOGINDATA'))
      let params = {
        unitCode: cacheInfo.unitCode,
        hospitalCode: cacheInfo.hospitalCode,
        filePath: val.filePath
      }
      axios({
        method: 'post',
        url: __PATH.VUE_CONVERGED_COM_API + 'communicationController/callRecord/fileDownload',
        data: params,
        responseType: 'blob',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + store.state.user.token
        }
      }).then((res) => {
        let name = val.file
        let blob = new Blob([res.data]) // { type: "application/vnd.ms-excel" }
        let url = window.URL.createObjectURL(blob) // 创建一个临时的url指向blob对象
        // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
        let a = document.createElement('a')
        a.href = url
        a.download = decodeURI(name)
        a.click()
        // 释放这个临时的对象url
        window.URL.revokeObjectURL(url)
      })
        .catch((res) => {
          this.$message.error('导出失败！')
        })
    },
    closeAlarmRecordDialog() {
      this.alarmRecordDialogShow = false
    }
  }
}
</script>
<style lang="scss" scoped>
.searchForm {
  display: flex;
  height: 80px;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  .el-input {
    width: 200px;
    margin-left: 16px;
  }
}

.search-box {
  display: flex;
  align-items: center;
}

.container-content > div {
  height: 100%;
  margin-top: 16px;
}
.el-table {
  margin-bottom: 12px;
}

::v-deep .el-table__row:hover {
  cursor: pointer;
}
.table-box {
  background-color: #fff;
  height: 100%;
  padding: 16px;
  .tableContainer {
    height: 100%;
    .el-table {
      height: calc(100% - 56px) !important;
    }
  }
}
.diaContent {
  width: 100%;
  max-height: 500px !important;
  overflow: auto;
  background-color: #fff !important;
  .el-input,
  .el-select,
  .el-cascader {
    width: 300px;
  }
}
.ml-16 {
  margin-left: 16px;
}
.btns {
  span {
    color: #3562db;
    cursor: pointer;
    margin-right: 5px;
  }
}
</style>
