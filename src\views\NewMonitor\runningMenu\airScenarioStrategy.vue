<template>
    <ScenarioStrategy :systemCode="systemCode" />
</template>

<script>
import ScenarioStrategy from '@/views/NewMonitor/airConditioningTerminal/runCalendarList/scenarioStrategy.vue'
import { newMonitorTypeList } from '@/util/newDict.js'
export default {
    name: '',
    components: {
        ScenarioStrategy
    },
    data() {
        return {
            systemCode: this.$route.meta.systemType
        }
    },
    computed: {},
    created() {
    },
    methods: {
    }
}
</script>

<style lang="scss" scoped></style>