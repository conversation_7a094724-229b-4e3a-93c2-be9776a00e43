<template>
  <el-dialog title="导出" width="45%" :visible.sync="exportDialogShow" v-dialogDrag custom-class="model-dialog"
    :before-close="closeDialog">
    <div class="content">
      <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate"
        @change="handleCheckAllChange">全选/全不选</el-checkbox>
      <el-checkbox-group v-model="checkedArr" style="margin-left: 20px;" @change="handleNameArrChange">
        <el-checkbox v-for="item in selectList" :key="item.EnglishName" :label="item"
          style="margin-top: 10px; width: 20%;">{{ item.ChineseName }}</el-checkbox>
      </el-checkbox-group>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="getExportData(1)">导出全部记录</el-button>
      <el-button type="primary" :disabled="checkedArr.length===0" @click="getExportData(2)">导出选中记录</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'exportDialog',
  components: {},
  props: {
    exportDialogShow: {
      type: Boolean,
      default: false
    },
    exportType: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      selectList: [],//选中
      checkedArr: [],
      checkAll: false,
      isIndeterminate: false,
    }
  },
  mounted() {
    this.getExportField()
  },
  methods: {
    getExportField() {
      const userInfo = this.$store.state.user.userInfo.user
      let url = ' '
      let params = {
        userId: userInfo.staffId,
        userName: userInfo.staffName,
      }
      switch (this.exportType) {
        case 1:
          url = 'exportTitleModel' //危化品台账导出
          break
        case 2:
          url = 'inwarehouseRecordExportTitleModel' //入库明细导出
          break
        case 3:
          url = 'outWarehouseRecordExportTitleModel' //入库明细导出
          break
      }
      this.$api[url](params).then((res) => {
        if (res.code == 200) {
          for (var i in res.data) {
            this.selectList.push({
              EnglishName: res.data[i],
              ChineseName: res.data[i].split('-')[1]
            })
          }
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    handleNameArrChange(val) {
      if (val && val.length) {
        this.isIndeterminate = true
      } else {
        this.isIndeterminate = false
      }
      this.checkedArr = val
    },
    handleCheckAllChange(val) {
      this.checkedArr = val ? this.selectList : []
      this.isIndeterminate = false
    },
    closeDialog() {
      this.$emit('closeExportDialog')
    },
    getExportData(type) {
      if (type == 1) {
        this.checkedArr = this.selectList
      }
      this.$emit('submitExportDialog', this.checkedArr)
    }
  }
}
</script>
<style lang="scss" scoped>
.model-dialog {
  .content {
    margin: 0 auto;
    background: #fff;
    border-radius: 4px;
    width: 100%;
    height: 100%;
    padding: 16px;
  }
}
</style>

