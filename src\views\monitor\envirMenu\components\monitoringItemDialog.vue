<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    width="200px"
    :visible.sync="visible"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <div v-loading="loading" class="content">
      <p>{{monitoringItem.time}}</p>
      <p>{{monitoringItem.surveyName}}</p>
      <p style="color: #7f848c;">{{monitoringItem.spaceName}}</p>
    </div>
  </el-dialog>
</template>
<script>
export default {
  name: 'monitoringItemDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    requestInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      loading: false,
      monitoringItem: {}
    }
  },
  mounted() {
    this.getMonitoringItem()
  },
  methods: {
    // 获取环境监测最大最小值信息
    getMonitoringItem() {
      this.loading = true
      this.$api.GetMonitoringItem(this.requestInfo).then((res) => {
        this.loading = false
        if (res.code == 200) {
          this.monitoringItem = res.data
        }
      })
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', !this.visible)
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog {
  background: #fff;
  margin-top: 0 !important;
  top: 50% !important;
  transform: translateY(-50%);

  .el-dialog__header {
    padding: 0;
    height: 0;

    .el-dialog__headerbtn {
      top: 5px;
      right: 5px;
    }
  }

  .el-dialog__body {
    padding: 20px 10px;
  }
}

.content {
  width: 100%;
  height: 100%;
  padding: 14px 44px 24px 24px;

  p {
    margin: 14px 0 0;
    font-size: 14px;
    color: #121f3e;
    line-height: 16px;
  }
}

.model-dialog {
  padding: 0 !important;
}
</style>
