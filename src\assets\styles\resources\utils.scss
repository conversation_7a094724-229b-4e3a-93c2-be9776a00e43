// @mixin 通过 @include 调用使用
// % 通过 @extend 调用使用

// 文字超出隐藏，默认为单行超出隐藏，可设置多行
@mixin text-overflow($line: 1, $fixed-width: true) {
  @if ($line==1 and $fixed-width==true) {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  @else {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: $line;
    overflow: hidden;
  }
}

// 定位居中，默认水平居中，可选择垂直居中，或者水平垂直都居中
@mixin position-center($type: x) {
  position: absolute;

  @if ($type==x) {
    left: 50%;
    transform: translateX(-50%);
  }

  @if ($type==y) {
    top: 50%;
    transform: translateY(-50%);
  }

  @if ($type==xy) {
    left: 50%;
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
  }
}

// 文字两端对齐
%justify-align {
  text-align: justify;
  text-align-last: justify;
}

// 清除浮动
%clearfix {
  zoom: 1;

  &::before,
  &::after {
    content: "";
    display: block;
    clear: both;
  }
}
