<!-- 运行总览 -->
<template>
  <PageContainer>
    <div slot="header" class="control-btn-header">
      <el-date-picker
        v-model="searchFrom.dataRange"
        type="daterange"
        unlink-panels
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        :picker-options="pickerOptions"
        :clearable="false"
      />
      <div style="display: inline-block;">
        <el-button type="primary" plain @click="resetForm">重置</el-button>
        <el-button type="primary" @click="searchForm">查询</el-button>
      </div>
    </div>
    <div slot="content" class="content">
      <el-row :gutter="16">
        <el-col :xs="24" :md="12" :lg="8">
          <ContentCard v-loading="loading.runTimeStat" title="运行时长统计（h）">
            <p slot="title-right" class="viewDetails" @click="viewDetails(1)">详情</p>
            <echarts slot="content" ref="runTimeStat" domId="runTimeStat" />
            <!-- <div id="runTimeStat" slot="content"></div> -->
          </ContentCard>
        </el-col>
        <el-col :xs="24" :md="12" :lg="8">
          <ContentCard v-loading="loading.runningRateRanking" title="运行率占比排行（%）">
            <p slot="title-right" class="viewDetails" @click="viewDetails(2)">详情</p>
            <echarts slot="content" ref="runningRateRanking" domId="runningRateRanking" />
            <!-- <div id="runningRateRanking" slot="content"></div> -->
          </ContentCard>
        </el-col>
        <el-col :xs="24" :md="12" :lg="8">
          <ContentCard v-loading="loading.faultStatistics" title="故障统计（次）">
            <p slot="title-right" class="viewDetails" @click="viewDetails(4)">详情</p>
            <echarts slot="content" ref="faultStatistics" domId="faultStatistics" />
            <!-- <div id="faultStatistics" slot="content"></div> -->
          </ContentCard>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :xs="24" :md="24" :lg="12">
          <ContentCard v-loading="loading.alarmStatistics" title="报警统计（个）">
            <p slot="title-right" class="viewDetails" @click="viewDetails(3)">详情</p>
            <div slot="content" style="width: 100%; height: 100%; display: flex;">
              <div class="cardContent-left">
                <div v-for="item in alarmStatisticsList" :key="item.title" class="left-item">
                  <p class="item-title">{{ item.title }}</p>
                  <p class="item-value" :style="{ color: item.color }">{{ item.value || 0 }}<span>个</span></p>
                  <img class="item-icon" :src="item.icon" :alt="item.title" />
                </div>
              </div>
              <echarts ref="alarmStatistics" domId="alarmStatistics" width="75%" height="100%" />
              <!-- <div id="alarmStatistics"></div> -->
            </div>
          </ContentCard>
        </el-col>
        <el-col :xs="24" :md="24" :lg="12">
          <ContentCard v-loading="loading.offlineStatisticsRanking" title="离线统计排行（次）">
            <p slot="title-right" class="viewDetails" @click="viewDetails(5)">详情</p>
            <echarts slot="content" ref="offlineStatisticsRanking" domId="offlineStatisticsRanking" />
            <!-- <div id="offlineStatisticsRanking" slot="content"></div> -->
          </ContentCard>
        </el-col>
      </el-row>
      <monitorStatisticsDialog v-if="monitorStatisticsDialog" :type="detailsType" :projectCode="projectCode" :entityList="entityList" :visible.sync="monitorStatisticsDialog" />
      <runningRateDialog v-if="runningRateDialog" :type="detailsType" :projectCode="projectCode" :entityList="entityList" :visible.sync="runningRateDialog" />
      <alarmStatisticsDialog v-if="alarmStatisticsDialog" :type="detailsType" :projectCode="projectCode" :visible.sync="alarmStatisticsDialog" />
    </div>
  </PageContainer>
</template>

<script>
import alarmStatistics from '@/assets/images/monitor/alarm-statistics.png'
import alarmAlarm from '@/assets/images/monitor/alarm-pending.png'
import alarmDoing from '@/assets/images/monitor/alarm-doing.png'
import chartMixin from './mixins/chartMixin'
import monitorStatisticsDialog from './components/monitorStatisticsDialog'
import alarmStatisticsDialog from '../components/alarmStatisticsDialog/index.vue'
import runningRateDialog from './components/runningRateDialog'
import { monitorTypeList } from '@/util/dict.js'
import moment from 'moment'
moment.locale('zh-cn')
export default {
  name: 'airOverview',
  components: {
    monitorStatisticsDialog,
    runningRateDialog,
    alarmStatisticsDialog
  },
  mixins: [chartMixin],
  data() {
    return {
      projectCode: monitorTypeList.find((item) => item.projectName == '空调监测').projectCode,
      detailsType: '',
      monitorStatisticsDialog: false, // 检测项统计弹窗
      runningRateDialog: false, // 运行率占比弹窗
      alarmStatisticsDialog: false, // 报警统计弹窗
      searchFrom: {
        dataRange: [moment().startOf('month').format('YYYY-MM-DD'), moment().endOf('month').format('YYYY-MM-DD')] // 时间范围
      },
      pickerOptions: {
        shortcuts: [
          {
            text: '今日',
            onClick(picker) {
              picker.$emit('pick', [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')])
            }
          },
          {
            text: '本月',
            onClick(picker) {
              picker.$emit('pick', [moment().startOf('month').format('YYYY-MM-DD'), moment().endOf('month').format('YYYY-MM-DD')])
            }
          },
          {
            text: '本年',
            onClick(picker) {
              picker.$emit('pick', [moment().startOf('year').format('YYYY-MM-DD'), moment().endOf('year').format('YYYY-MM-DD')])
            }
          }
        ]
      },
      alarmStatisticsList: [
        {
          title: '报警统计',
          color: '#3562DB',
          icon: alarmStatistics,
          value: 0
        },
        {
          title: '未处理',
          color: '#FA403C',
          icon: alarmAlarm,
          value: 0
        },
        {
          title: '处理中',
          color: '#FF9435',
          icon: alarmDoing,
          value: 0
        }
      ],
      loading: {
        runTimeStat: false,
        runningRateRanking: false,
        faultStatistics: false,
        offlineStatisticsRanking: false,
        alarmStatistics: false
      },
      entityList: [] // 设备列表
    }
  },
  computed: {},
  created() {
    this.init()
    this.getEntityMenuList()
  },
  mounted() {

  },
  methods: {
    init() {
      let params = {
        startTime: this.searchFrom.dataRange[0],
        endTime: this.searchFrom.dataRange[1],
        projectCode: this.projectCode
      }
      this.$nextTick(() => {
        this.getAirRunTime(params)
        this.getAirRunRate(params)
        this.getAirBreakdown(params)
        this.getAirOffLine(params)
        this.getAirRunPolice(params)
      })
    },
    // 获取设备菜单
    getEntityMenuList() {
      this.$api.GetEntityMenuList({ projectId: this.projectCode }).then((res) => {
        if (res.code == 200) {
          this.entityList = res.data
        }
      })
    },
    // 报警统计
    getAirRunPolice(params) {
      let newArr = []
      this.loading.alarmStatistics = true
      this.$api
        .GetAirRunPolice(params)
        .then((res) => {
          this.loading.alarmStatistics = false
          if (res.code == 200) {
            ;(res.data?.policeList ?? []).forEach((item) => {
              newArr.push({
                name: item.menuName,
                value: item.policeCount
              })
            })
            this.alarmStatisticsList[0].value = res.data?.total ?? 0
            this.alarmStatisticsList[1].value = res.data?.noDealCount ?? 0
            this.alarmStatisticsList[2].value = res.data?.isDealCount ?? 0
            this.$refs.alarmStatistics.init(this.setPieChart('个', newArr, ['监测项类型', '报警占比']))
          } else {
            this.$refs.alarmStatistics.init(this.setPieChart())
          }
        })
        .catch(() => {
          this.loading.alarmStatistics = false
          this.$refs.alarmStatistics.init(this.setPieChart())
        })
    },
    // 获取离线统计排行
    getAirOffLine(params) {
      this.loading.offlineStatisticsRanking = true
      this.$api
        .GetAirOffLine(params)
        .then((res) => {
          this.loading.offlineStatisticsRanking = false
          if (res.code == 200) {
            this.$refs.offlineStatisticsRanking.init(
              this.transverseColumnChart(
                '#3562DB',
                {
                  dataAll: res.data.airConditionOffList.map((item) => item.breakCount),
                  yData: res.data.airConditionOffList.map((item) => item.menuName)
                },
                '离线统计（次）'
              )
            )
          } else {
            this.$refs.offlineStatisticsRanking.init(this.transverseColumnChart())
          }
        })
        .catch(() => {
          this.loading.offlineStatisticsRanking = false
          this.$refs.offlineStatisticsRanking.init(this.transverseColumnChart())
        })
    },
    // 故障统计
    getAirBreakdown(params) {
      this.loading.faultStatistics = true
      this.$api
        .GetAirBreakdown(params)
        .then((res) => {
          this.loading.faultStatistics = false
          if (res.code == 200) {
            this.$refs.faultStatistics.init(
              this.transverseColumnChart(
                '#FF6461',
                {
                  dataAll: res.data.airConditionBreakList.map((item) => item.breakCount),
                  yData: res.data.airConditionBreakList.map((item) => item.menuName)
                },
                '故障统计（次）'
              )
            )
          } else {
            this.$refs.faultStatistics.init(this.transverseColumnChart())
          }
        })
        .catch(() => {
          this.loading.faultStatistics = false
          this.$refs.faultStatistics.init(this.transverseColumnChart())
        })
    },
    // 运行率占比排行
    getAirRunRate(params) {
      this.loading.runningRateRanking = true
      this.$api
        .GetAirRunRate(params)
        .then((res) => {
          this.loading.runningRateRanking = false
          if (res.code == 200) {
            this.$refs.runningRateRanking.init(
              this.setBrokenLineColumnMixingChart({
                xData: res.data.airRunRate.map((item) => item.menuName),
                barData: res.data.airRunRate.map((item) => item.monitorCount),
                lineData: res.data.airRunRate.map((item) => Number(item.runRate.slice(0, item.runRate.length - 1))),
                legendData: ['监测项数量', '平均运行率']
              })
            )
          } else {
            this.$refs.runningRateRanking.init(this.setBrokenLineColumnMixingChart())
          }
        })
        .catch(() => {
          this.loading.runningRateRanking = false
          this.$refs.runningRateRanking.init(this.setBrokenLineColumnMixingChart())
        })
    },
    // 运行时长统计
    getAirRunTime(params) {
      let newArr = []
      this.loading.runTimeStat = true
      this.$api
        .GetAirRunTime(params)
        .then((res) => {
          this.loading.runTimeStat = false
          if (res.code == 200) {
            ;(res.data?.airRunTime ?? []).forEach((item) => {
              newArr.push({
                name: item.menuName,
                value: item.runTime
              })
            })
            // newArr = [
            //   {
            //     name: '你说的名字你说的名字你说的名字你说的名字你说的名字你说的名字',
            //     value: 23
            //   },
            //   {
            //     name: '你说的名字为什么1',
            //     value: 7
            //   },
            //   {
            //     name: '你说的名字为什么2你说的名字为什么2你说的名字为什么2你说的名字为什么2',
            //     value: 14
            //   }
            // ]
            this.$refs.runTimeStat.init(this.setPieChart('h', newArr))
          } else {
            this.$refs.runTimeStat.init(this.setPieChart())
          }
        })
        .catch(() => {
          this.loading.runTimeStat = false
          this.$refs.runTimeStat.init(this.setPieChart())
        })
    },
    // 查看详情
    viewDetails(type) {
      this.detailsType = type
      if ([1, 4, 5].includes(type)) {
        this.monitorStatisticsDialog = true
      } else if (type == 2) {
        this.runningRateDialog = true
      } else {
        this.alarmStatisticsDialog = true
      }
      console.log(type)
    },
    // 重置查询
    resetForm() {
      Object.assign(this.$data.searchFrom, this.$options.data().searchFrom)
      this.searchForm()
    },
    // 查询
    searchForm() {
      this.init()
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .container-content {
  overflow-y: auto !important;
}

.control-btn-header {
  padding: 10px !important;

  & > div {
    margin-right: 10px;
  }
}

.content {
  height: 100%;

  .cardContent-left {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 25%;
    height: 100%;

    p {
      margin: 0;
    }

    .left-item {
      padding: 14px 22px;
      background: #faf9fc;
      border-radius: 4px;
      margin-bottom: 7px;
      position: relative;

      .item-title {
        font-size: 15px;
        font-weight: 500;
        color: #121f3e;
      }

      .item-value {
        margin-top: 4px;
        font-size: 30px;

        & > span {
          font-size: 15px;
          font-weight: 500;
          color: #ccced3;
        }
      }

      .item-icon {
        position: absolute;
        right: 22px;
        bottom: 14px;
        width: 40px;
        height: 40px;
      }
    }

    & :last-child {
      margin-bottom: 0;
    }
  }

  ::v-deep .el-row {
    height: 50%;

    .el-col {
      height: 100%;
    }

    .box-card {
      margin-top: 16px;
      height: calc(100% - 16px);

      .viewDetails {
        user-select: none;
        cursor: pointer;
        margin: 0 !important;
        font-size: 14px;
        color: #3562db;
        position: absolute;
        right: 15px;
        top: 0;
      }

      .card-body {
        height: calc(100% - 25px);
        margin: 0;
      }
    }
  }
}
</style>
