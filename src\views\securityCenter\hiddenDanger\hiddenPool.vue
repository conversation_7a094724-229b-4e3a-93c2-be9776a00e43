<template>
  <PageContainer>
    <div slot="header">
      <div class="search-header">
        <div class="search-from">
          <el-input v-model="formInline.questionCode" placeholder="请输入隐患编号" style="width: 200px;"></el-input>
          <el-input v-model="formInline.dutyDeptName" placeholder="请输入责任部门" style="width: 200px;"></el-input>
          <el-select v-model="formInline.riskCode" placeholder="请选择隐患等级">
            <el-option v-for="item in hiddenLevelList" :key="item.id" :label="item.dictName" :value="item.dictCode"></el-option>
          </el-select>
          <el-cascader
            v-model="formInline.questionDetailCode"
            :options="hiddenClassifyList"
            :show-all-levels="false"
            :props="{ expandTrigger: 'hover', checkStrictly: true, value: 'id', emitPath: false }"
            placeholder="请选择隐患分类"
          ></el-cascader>
          <el-input v-model="formInline.createByDeptName" placeholder="请输入反馈部门" style="width: 200px;"></el-input>
          <el-date-picker v-model="timeLine" type="daterange" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
          </el-date-picker>
        </div>
        <div>
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="search">查询</el-button>
          <el-button class="el-icon-download" type="primary" @click="exportFile">导出Excel</el-button>
        </div>
      </div>
    </div>
    <div slot="content" style="height: 100%; margin-top: 16px; background-color: #fff;">
      <div class="table-content">
        <el-table v-loading="tableLoading" :data="tableData" :height="tableHeight" border stripe title="双击查看详情" @row-dblclick="openDetails">
          <el-table-column label="序号" width="100" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ (paginationData.pageNo - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="questionCode" label="编号" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="questionDetailType" label="隐患分类" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="questionAddress" label="隐患区域" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="createByDeptName" label="反馈部门" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="createPersonName" label="反馈人员" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="createTime" label="反馈时间" width="200" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="riskName" label="隐患等级" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="rectificationPlanTime" label="要求整改完成时间" width="200" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="taskTeamName" label="操作" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <div class="bths">
                <span
                  @click="
                    claimDialogVisible = true
                    currentRowData = scope.row
                  "
                >认领</span
                >
                <span
                  @click="
                    transferDialogVisible = true
                    currentRowData = scope.row
                  "
                >转派</span
                >
              </div>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          style="margin-top: 3px;"
          :current-page="paginationData.pageNo"
          layout="total, sizes, prev, pager, next, jumper"
          :total="paginationData.total"
          :page-size="paginationData.pageSize"
          :page-sizes="[15, 30, 50]"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
      <el-dialog title="隐患认领" :visible.sync="claimDialogVisible" width="30%" :before-close="claimDialogClosed" custom-class="model-dialog">
        <el-form :model="claimForm" class="outermost" style="background-color: #fff; width: 100%; padding: 10px;">
          <el-form-item label="认领科室:" label-width="200px">
            <span>{{ loginData.officeName }}</span>
          </el-form-item>
          <el-form-item label="预计整改完成时间:" label-width="200px">
            <el-date-picker v-model="claimDate" value-format="yyyy-MM-dd" type="date" placeholder="选择日期"> </el-date-picker>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="claim">确 定</el-button>
        </div>
      </el-dialog>
      <el-dialog title="隐患转派" width="30%" :visible.sync="transferDialogVisible" custom-class="model-dialog">
        <el-form :model="transferForm" class="outermost" style="background-color: #fff; width: 100%; padding: 10px;">
          <el-form-item label="责任科室" label-width="200px">
            <el-select v-model="transferForm.dutyDeptCode" placeholder="请选择科室">
              <el-option v-for="item in responsibleDepartmentList" :key="item.id" :label="item.teamName" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="transfer">确 定</el-button>
        </div>
      </el-dialog>
    </div>
  </PageContainer>
</template>

<script>
import axios from 'axios'
export default {
  data() {
    return {
      formInline: {
        questionCode: '',
        dutyDeptName: '',
        riskCode: '',
        questionDetailCode: '',
        createByDeptName: '',
        startTime: '',
        endTime: ''
      },
      tableData: [],
      tableLoading: false,
      paginationData: {
        pageNo: 1,
        pageSize: 15,
        total: 0
      },
      claimDialogVisible: false, // 认领框显示隐藏
      transferDialogVisible: false, // 转派框显示隐藏
      claimForm: {},
      transferForm: {
        dutyDeptCode: ''
      },
      timeLine: [],
      hiddenLevelList: [],
      hiddenClassifyList: [],
      claimDate: '',
      loginData: {},
      currentRowData: '',
      responsibleDepartmentList: [],
      exportDialogVisible: false
    }
  },
  computed: {
    tableHeight() {
      return document.body.clientHeight - 270
    }
  },
  watch: {
    timeLine(val) {
      if (val.length == 2) {
        this.formInline.startTime = val[0]
        this.formInline.endTime = val[1]
      }
    }
  },
  created() {
    this.loginData = JSON.parse(sessionStorage.getItem('LOGINDATA'))
    this.getData()
    this.getDictData()
  },
  methods: {
    getDictData() {
      this.$api.ipsmGetDictList({ dictType: 'hidden_trouble_grade_type' }).then((res) => {
        if (res.code == 200) {
          this.hiddenLevelList = res.data
        } else {
          this.$message.error(res.message)
        }
      })
      this.$api.ipsmGetHiddenClassifyList().then((res) => {
        if (res.code == 200) {
          this.hiddenClassifyList = res.data
        } else {
          this.$message.error(res.message)
        }
      })
      this.$api.ipsmGetResponsibleDepartment().then((res) => {
        if (res.code == 200) {
          this.responsibleDepartmentList = res.data
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 双击列表查看详情
    openDetails(val) {
      this.$router.push({
        name: 'hiddenDetails',
        query: {
          id: val.id
        }
      })
    },
    // 导出
    exportFile() {
      this.$api.ipsmHiddenExportFile({ ...this.formInline }).then((res) => {
        if (res.status == '200') {
          let name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
          console.log(name)
          let blob = new Blob([res.data]) // { type: "application/vnd.ms-excel" }
          let url = window.URL.createObjectURL(blob) // 创建一个临时的url指向blob对象
          // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
          let a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
          // 释放这个临时的对象url
          window.URL.revokeObjectURL(url)
        } else {
          console.log('error', res)
        }
      })
    },

    // 查询列表
    getData() {
      this.tableLoading = true
      let data = {
        ...this.paginationData,
        ...this.formInline
      }
      this.$api.ipsmGetHiddenPoolList(data).then((res) => {
        if (res.code == '200') {
          this.tableData = res.data.list
          this.paginationData.total = res.data.sum
        }
        this.tableLoading = false
      })
    },
    // 查询
    search() {
      this.paginationData.pageNo = 1
      this.getData()
    },

    // 重置
    resetForm() {
      this.formInline.questionCode = ''
      this.formInline.dutyDeptName = ''
      this.formInline.riskCode = ''
      this.formInline.questionDetailCode = ''
      this.formInline.createByDeptName = ''
      this.formInline.startTime = ''
      this.formInline.endTime = ''
      this.timeLine = []
      this.paginationData.pageNo = 1
      this.getData()
    },
    // 分页
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.getData()
    },
    handleCurrentChange(val) {
      this.paginationData.pageNo = val
      this.getData()
    },
    claimDialogClosed(done) {
      this.claimDate = ''
      done()
    },
    closeDialog() {
      this.claimDialogVisible = false
      this.transferDialogVisible = false
      this.claimDate = ''
      this.transferForm.dutyDeptCode = ''
    },
    claim() {
      let params = {
        claimFinishPlanTime: this.claimDate,
        id: this.currentRowData.id
      }
      this.$api.ipsmClaimQuestion(params).then((res) => {
        if (res.code == 200) {
          this.$message.success(res.message)
          this.getData()
        } else {
          this.$message.error(res.message)
        }
        this.claimDialogVisible = false
        this.claimDate = ''
      })
    },
    transfer() {
      let dutyDeptName = ''
      this.responsibleDepartmentList.forEach((item) => {
        if (item.id == this.transferForm.dutyDeptCode) {
          dutyDeptName = item.teamName
        }
      })
      let params = {
        id: this.currentRowData.id,
        dutyDeptCode: this.transferForm.dutyDeptCode,
        dutyDeptName
      }
      this.$api.ipsmHiddenPoolTransfer(params).then((res) => {
        if (res.code == 200) {
          this.$message.success('隐患转派成功！')
          this.getData()
        } else {
          this.$message.error(res.message)
        }
        this.transferDialogVisible = false
        this.transferForm.dutyDeptCode = ''
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.search-header {
  padding: 10px 16px;

  .search-from {
    padding-bottom: 12px;

    & > div {
      margin-top: 12px;
      margin-right: 10px;
    }
  }
}

.table-content {
  padding: 16px;
  height: calc(100% - 80px);
}

.bths {
  > span {
    cursor: pointer;
  }

  > span:nth-child(1) {
    color: #558bf9;
    margin-right: 5px;
  }

  > span:nth-child(2) {
    color: #5ec8c3;
  }
}
</style>
