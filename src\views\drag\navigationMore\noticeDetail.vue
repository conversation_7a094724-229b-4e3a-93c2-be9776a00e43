<!--
 * @Author: hedd
 * @Date: 2023-04-20 15:23:20
 * @LastEditTime: 2025-03-11 11:37:32
 * @FilePath: \ihcrs_pc\src\views\drag\navigationMore\noticeDetail.vue
 * @Description:
-->
<template>
  <PageContainer>
    <div slot="header" class="backBar">
      <span style="cursor: pointer" @click="$router.go(-1)">
        <i class="el-icon-arrow-left"></i>
        <!-- 通知公告详情 -->
        {{ msgData.msgSysTypeName }}
      </span>
    </div>
    <div slot="content" class="notice-detail">
      <div class="notice-title">
        <div class="notice-label">标题：</div>
        <div class="notice-text">{{ msgData.msgTitle }}</div>
      </div>
      <div class="notice-time">
        <div class="notice-label">时间：</div>
        <div class="notice-text">{{ msgData.publishTime }}</div>
      </div>
      <div class="notice-content" v-html="msgData.msgText"></div>
    </div>
  </PageContainer>
</template>
<script lang="jsx">
export default {
  data() {
    return {
      msgData: ''
    }
  },
  created() {
    console.log(this.$route.query)
  },
  mounted() {
    this.getImserverMsgInfo()
  },
  methods: {
    getImserverMsgInfo() {
      const item = this.$route.query
      const params = {
        userId: this.$store.state.user.userInfo.user.staffId,
        msgId: item.msgId,
        msgSysId: item.msgSysId
      }
      this.$api.getImserverMsgInfo(params).then((res) => {
        console.log(res)
        if (res.code == 200) {
          const data = res.data
          this.msgData = {
            ...data,
            msgBody: JSON.parse(data.msgBody).msgBody
          }
        } else {
          this.$message.error(res.message)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.backBar {
  padding: 10px 8px 10px 15px;
  color: #121f3e;
}
.notice-detail {
  padding: 10px 15px;
  background: #fff;
  height: calc(100% - 15px);
  margin-top: 15px;
  overflow-y: scroll;
  .notice-title {
    display: flex;
    margin-bottom: 10px;
  }
  .notice-time {
    display: flex;
    margin-bottom: 10px;
  }
  .notice-label {
    font-size: 16px;
    font-family: 'PingFang SC-Regular', 'PingFang SC';
    font-weight: 600;
    color: #121f3e;
    margin-right: 10px;
  }
  .notice-text {
    font-size: 16px;
    font-family: 'PingFang SC-Regular', 'PingFang SC';
    color: #121f3e;
  }
}
</style>
