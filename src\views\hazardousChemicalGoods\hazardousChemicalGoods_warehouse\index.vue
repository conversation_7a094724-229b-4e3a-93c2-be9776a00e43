<script>
import tableListMixin from '@/mixins/tableListMixin'
export default {
  name: 'hazardousChemicalGoods_warehouse',
  mixins: [tableListMixin],
  data() {
    return {
      searchForm: {
        warehouseName: '', //仓库名称
        status: '' //状态
      },
      stateOptions: [
        {
          label: '启用',
          value: '0'
        },
        {
          label: '禁用',
          value: '1'
        }
      ],
      tableData: [],
      tableLoadingStatus: false
    }
  },
  mounted() {
    this.getDataList()
  },
  methods: {
    // 获取列表数据
    getDataList() {
      this.tableData = []
      this.tableLoadingStatus = true
      const params = {
        pageSize: this.pagination.size,
        currentPage: this.pagination.current,
        ...this.searchForm
      }
      this.$api
        .queryWarehouseByPage(params)
        .then((res) => {
          this.tableLoadingStatus = false
          if (res.code === '200') {
            this.tableData = res.data.list
            this.pagination.total = res.data.sum
          } else {
            this.tableData = []
            this.pagination.total = 0
          }
        })
        .catch((err) => {
          this.tableLoadingStatus = false
        })
    },
    // 表单搜索按钮点击
    onSearch() {
      this.pagination.current = 1
      this.getDataList()
    },
    // 表单重置按钮点击
    onReset() {
      this.$refs.formRef.resetFields()
      this.onSearch()
    },
    //新增
    addOption() {
      this.$router.push({
        name: 'warehouseAdd',
        query: {
          type: 'add'
        }
      })
    },
    //操作
    onOperate(type, row) {
      if (type === 'view') {
        this.$router.push({
          name: 'warehouseAdd',
          query: {
            type: 'view',
            warehouseId: row.id
          }
        })
      } else if (type === 'edit') {
        this.$router.push({
          name: 'warehouseAdd',
          query: {
            type: 'edit',
            warehouseId: row.id
          }
        })
      } else if (type === 'delete') {
        this.$confirm('确认删除选中的仓库吗?', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$api.deleteWarehouseById({ id: row.id }).then((res) => {
            if (res.code == 200) {
              this.onSearch()
              this.$message({
                message: res.message,
                type: 'success'
              })
            } else {
              this.$message.error(res.msg)
            }
          })
        })
      }
    }
  }
}
</script>
<template>
  <PageContainer class="hazardousChemicalGoods_warehouse">
    <template #content>
      <div class="hazardousChemicalGoods_warehouse__header">
        <el-form ref="formRef" :model="searchForm" class="hazardousChemicalGoods_warehouse__search" inline @submit.native.prevent="onSearch">
          <el-form-item prop="warehouseName">
            <el-input v-model="searchForm.warehouseName" clearable filterable suffix-icon="el-icon-search" placeholder="仓库名称"></el-input>
          </el-form-item>
          <el-form-item prop="status">
            <el-select v-model="searchForm.status" placeholder="全部状态" clearable>
              <el-option v-for="item of stateOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" plain @click="onReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="hazardousChemicalGoods_warehouse__actions">
        <el-button type="primary" v-auth="'warehouseManagement:add'" @click="addOption" icon="el-icon-plus"> 新增 </el-button>
      </div>
      <div class="hazardousChemicalGoods_warehouse__table">
        <el-table v-loading="tableLoadingStatus" height="100%" :data="tableData" border stripe table-layout="auto" class="tableAuto" row-key="id">
          <el-table-column type="index" width="70" label="序号"> </el-table-column>
          <el-table-column prop="warehouseName" show-overflow-tooltip label="仓库名称"></el-table-column>
          <el-table-column prop="warehouseCode" show-overflow-tooltip label="编码"></el-table-column>
          <el-table-column prop="warehouseAddress" show-overflow-tooltip label="仓库地址"></el-table-column>
          <el-table-column prop="manageUnitName" show-overflow-tooltip label="管理单位"></el-table-column>
          <el-table-column prop="warehouseUnitType" show-overflow-tooltip label="管理单位类型"></el-table-column>
          <el-table-column prop="manageName" show-overflow-tooltip label="责任人"></el-table-column>
          <el-table-column prop="managePhone" show-overflow-tooltip label="联系电话"></el-table-column>
          <el-table-column prop="status" show-overflow-tooltip label="启用状态">
            <template #default="{ row }">
              <span v-if="row.status === '禁用'" class="hazardousChemicalGoods_warehouse__tag hazardousChemicalGoods_warehouse__tag--0">
                {{ row.status }}
              </span>
              <span v-if="row.status === '启用'" class="hazardousChemicalGoods_warehouse__tag hazardousChemicalGoods_warehouse__tag--1">
                {{ row.status }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150px">
            <template #default="{ row }">
              <el-button type="text" v-auth="'warehouseManagement:check'" @click="onOperate('view', row)">查看</el-button>
              <el-button type="text" v-auth="'warehouseManagement:edit'" @click="onOperate('edit', row)">编辑</el-button>
              <el-button type="text" v-auth="'warehouseManagement:del'" class="text-red" @click="onOperate('delete', row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        class="hazardousChemicalGoods_warehouse__pagination"
        :current-page="pagination.page"
        :page-sizes="pagination.pageSizeOptions"
        :page-size="pagination.size"
        :layout="pagination.layoutOptions"
        :total="pagination.total"
        @size-change="paginationSizeChange"
        @current-change="paginationCurrentChange"
      >
      </el-pagination>
    </template>
  </PageContainer>
</template>
<style scoped lang="scss">
.hazardousChemicalGoods_warehouse {
  ::v-deep(> .container-content) {
    padding: 16px;
    height: 100%;
    width: 100%;
    background-color: #fff;
  }
  &__header {
    display: flex;
    justify-content: space-between;
  }
  &__actions {
    padding-bottom: 10px;
  }
  &__table {
    height: calc(100% - 140px);
  }
  &__pagination {
    margin-top: 10px;
  }
  &__tag {
    // 禁用
    &--0 {
      --color: #f64646;
    }
    // 可用
    &--1 {
      --color: #00b42a;
    }
    &:before {
      content: '';
      display: inline-block;
      vertical-align: middle;
      margin-right: 4px;
      height: 6px;
      width: 6px;
      border-radius: 4px;
      background-color: var(--color);
    }
  }
  &__table__color {
    display: inline-block;
    height: 16px;
    width: 16px;
    vertical-align: text-top;
    margin-right: 4px;
  }
  .text-red {
    color: #ff1919;
  }
}
.el-form-item {
  margin-bottom: 8px !important;
}
</style>
