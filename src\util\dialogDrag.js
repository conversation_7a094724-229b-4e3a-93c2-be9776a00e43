import Vue from 'vue'
import store from '@/store'
import { ElTooltip } from 'element-ui'

// v-dialogDrag: 弹窗拖拽
Vue.directive('dialogDrag', {
  // bind(el, binding, vnode, oldVnode) {
  //   const dialogHeaderEl = el.querySelector('.el-dialog__header')
  //   const dragDom = el.querySelector('.el-dialog')
  //   dialogHeaderEl.style.cursor = 'move'
  //   // 获取原有属性 ie dom元素.currentStyle 火狐谷歌 window.getComputedStyle(dom元素, null);
  //   const sty = dragDom.currentStyle || window.getComputedStyle(dragDom, null)
  //   dialogHeaderEl.onmousedown = (e) => {
  //     // 鼠标按下，计算当前元素距离可视区的距离
  //     const disX = e.pageX - dialogHeaderEl.offsetLeft
  //     const disY = e.pageY - dialogHeaderEl.offsetTop
  //     // 获取到的值带px 正则匹配替换
  //     let styL, styT
  //     // 注意在ie中 第一次获取到的值为组件自带50% 移动之后赋值为px
  //     if (sty.left.includes('%')) {
  //       styL = +document.body.clientWidth * (+sty.left.replace(/\%/g, '') / 100)
  //       styT = +document.body.clientHeight * (+sty.top.replace(/\%/g, '') / 100)
  //     } else {
  //       styL = +sty.left.replace(/\px/g, '')
  //       styT = +sty.top.replace(/\px/g, '')
  //     }
  //     document.onmousemove = function (e) {
  //       // 通过事件委托，计算移动的距离
  //       const l = e.clientX - disX
  //       const t = e.clientY - disY
  //       let finallyL = l + styL
  //       let finallyT = t + styT
  //       // // 限制不让拖出屏幕 *****
  //       // let domToEdge = dragDom.getBoundingClientRect()
  //       // 移动当前元素
  //       dragDom.style.left = `${finallyL}px`
  //       dragDom.style.top = `${finallyT}px`
  //     }
  //     document.onmouseup = function (e) {
  //       document.onmousemove = null
  //       document.onmouseup = null
  //     }
  //   }
  // }
  bind(el, binding, vnode, oldVnode) {
    const dialogHeaderEl = el.querySelector('.el-dialog__header')
    const dragDom = el.querySelector('.el-dialog')
    /*  dragDom.onselectstart = function () {
        return false
      }*/
    // dialogHeaderEl.style.cursor = 'move';
    dialogHeaderEl.style.cssText += ';cursor:move;'
    dragDom.style.cssText += ';top:0px;'
    // 获取原有属性 ie dom元素.currentStyle 火狐谷歌 window.getComputedStyle(dom元素, null);
    const sty = (function () {
      if (window.document.currentStyle) {
        return (dom, attr) => dom.currentStyle[attr]
      } else {
        return (dom, attr) => getComputedStyle(dom, false)[attr]
      }
    })()
    dialogHeaderEl.onmousedown = (e) => {
      // 鼠标按下，计算当前元素距离可视区的距离
      const disX = e.clientX - dialogHeaderEl.offsetLeft
      const disY = e.clientY - dialogHeaderEl.offsetTop
      const screenWidth = document.body.clientWidth // body当前宽度
      const screenHeight = document.documentElement.clientHeight // 可见区域高度(应为body高度，可某些环境下无法获取)
      const dragDomWidth = dragDom.offsetWidth // 对话框宽度
      const dragDomHeight = screenHeight > dragDom.offsetHeight ? dragDom.offsetHeight : screenHeight // 对话框高度
      const minDragDomLeft = dragDom.offsetLeft
      const maxDragDomLeft = screenWidth - dragDom.offsetLeft - dragDomWidth
      const minDragDomTop = dragDom.offsetTop
      const maxDragDomTop = screenHeight - dragDom.offsetTop - dragDomHeight
      // 获取到的值带px 正则匹配替换
      let styL = sty(dragDom, 'left')
      let styT = sty(dragDom, 'top')
      // 注意在ie中 第一次获取到的值为组件自带50% 移动之后赋值为px
      if (styL.includes('%')) {
        styL = +document.body.clientWidth * (+styL.replace(/\%/g, '') / 100)
        styT = +document.body.clientHeight * (+styT.replace(/\%/g, '') / 100)
      } else {
        styL = +styL.replace(/\px/g, '')
        styT = +styT.replace(/\px/g, '')
      }
      document.onselectstart = function () {
        return false
      }
      document.onmousemove = function (e) {
        // 通过事件委托，计算移动的距离
        let left = e.clientX - disX
        let top = e.clientY - disY
        // 边界处理
        if (-left > minDragDomLeft) {
          left = -minDragDomLeft
        } else if (left > maxDragDomLeft) {
          left = maxDragDomLeft
        }
        if (-top > minDragDomTop) {
          top = -minDragDomTop
        } else if (top > maxDragDomTop) {
          top = maxDragDomTop
        }
        // 移动当前元素
        dragDom.style.cssText += `;left:${left + styL}px;top:${top + styT}px;`
      }
      document.onmouseup = function (e) {
        document.onmousemove = null
        document.onmouseup = null
        document.onselectstart = null
      }
    }
  }
})
// v-dialogDragWidth: 弹窗宽度拖大 拖小
Vue.directive('dialogDragWidth', {
  bind(el, binding, vnode, oldVnode) {
    const dragDom = binding.value.$el.querySelector('.el-dialog')
    el.onmousedown = (e) => {
      // 鼠标按下，计算当前元素距离可视区的距离
      const disX = e.clientX - el.offsetLeft
      document.onmousemove = function (e) {
        e.preventDefault() // 移动时禁用默认事件
        // 通过事件委托，计算移动的距离
        const l = e.clientX - disX
        dragDom.style.width = `${l}px`
      }
      document.onmouseup = function (e) {
        document.onmousemove = null
        document.onmouseup = null
      }
    }
  }
})
// v-drag: div拖拽
Vue.directive('drag', {
  bind(el, binding, vnode, oldVnode) {
    const dialogHeaderEl = el.querySelector('.el-dialog__header')
    const dragDom = el.querySelector('.el-dialog')
    dialogHeaderEl.style.cursor = 'move'
    // 获取原有属性 ie dom元素.currentStyle 火狐谷歌 window.getComputedStyle(dom元素, null);
    const sty = dragDom.currentStyle || window.getComputedStyle(dragDom, null)
    dialogHeaderEl.onmousedown = (e) => {
      // 鼠标按下，计算当前元素距离可视区的距离
      const disX = e.clientX - dialogHeaderEl.offsetLeft
      const disY = e.clientY - dialogHeaderEl.offsetTop
      // 获取到的值带px 正则匹配替换
      let styL, styT
      // 注意在ie中 第一次获取到的值为组件自带50% 移动之后赋值为px
      if (sty.left.includes('%')) {
        styL = +document.body.clientWidth * (+sty.left.replace(/\%/g, '') / 100)
        styT = +document.body.clientHeight * (+sty.top.replace(/\%/g, '') / 100)
      } else {
        styL = +sty.left.replace(/\px/g, '')
        styT = +sty.top.replace(/\px/g, '')
      }
      document.onmousemove = function (e) {
        // 通过事件委托，计算移动的距离
        const l = e.clientX - disX
        const t = e.clientY - disY
        // 移动当前元素
        dragDom.style.left = `${l + styL}px`
        dragDom.style.top = `${t + styT}px`
        // 将此时的位置传出去
        // binding.value({x:e.pageX,y:e.pageY})
      }
      document.onmouseup = function (e) {
        document.onmousemove = null
        document.onmouseup = null
      }
    }
  }
})
// v-loadmore: el-tanle滚动加载
Vue.directive('loadMore', {
  bind(el, binding) {
    const selectWrap = el.querySelector('.el-table__body-wrapper')
    selectWrap.addEventListener('scroll', function () {
      let sign = 0
      const scrollDistance = this.scrollHeight - this.scrollTop - this.clientHeight
      if (scrollDistance <= sign) {
        binding.value()
      }
    })
  }
})
// v-scrollHideTooltip: el-table滚动加载 并隐藏打开的tooltip
Vue.directive('scrollHideTooltip', {
  bind(el, binding) {
    const selectWrap = el.querySelector('.el-table__body-wrapper')
    selectWrap.addEventListener('scroll', function () {
      const tooltips = document.querySelectorAll('.el-tooltip__popper')
      tooltips.forEach((element) => {
        element.style.display = 'none'
      })
    })
  }
})
// 目前仅适用于初始化就有滚动条的情况 且滚动条在右侧；类似于tree组件可动态展开的情况暂未处理
const scrollbarEvent = (el, binding, vnode, oldVnode) => {
  setTimeout(() => {
    if (el.scrollHeight > el.clientHeight) {
      if (!el.getAttribute('initFinish')) {
        el.style.paddingRight = '13px'
        el.style.overflowY = 'hidden'
        el.setAttribute('initFinish', true)
      }
      el.onmouseover = function () {
        el.style.overflowY = 'auto'
        el.style.paddingRight = '0px'
      }
      // 鼠标出
      el.onmouseout = function () {
        el.style.overflowY = 'hidden'
        el.style.paddingRight = '13px'
      }
    } else {
      // 移除onmouseover、移除onmouseout事件事件
      el.onmouseover = null
      el.onmouseout = null
    }
  }, 250)
}
// v-scrollbarHover
Vue.directive('scrollbarHover', {
  bind(el, binding, vnode, oldVnode) {
    window.addEventListener('resize', () => {
      scrollbarEvent(el, binding, vnode, oldVnode)
    })
    // 监听 store 中的状态变化
    vnode.context.$watch(
      () => store.state.settings.sidebarCollapse, // 替换为您要监听的 store 状态
      () => {
        // 增加二级菜单切换时动画的延迟
        setTimeout(() => {
          scrollbarEvent(el, binding, vnode, oldVnode)
        }, 250)
      }
    )
  },
  inserted: scrollbarEvent,
  updated: scrollbarEvent,
  componentUpdated: scrollbarEvent,
  unbind(el) {
    el.onmouseover = null
    el.onmouseout = null
  }
})
// 禁止输入特殊字符指令
Vue.directive('filterSpecialChar', {
  update: function (el, binding, vnode, oldVnode) {
    try {
      if (!el.children[0].value) {
        return false
      }
      el.children[0].value = el.children[0].value.replace(/[^\a-\z\A-\Z0-9\u4E00-\u9FA5]/g, '')
      el.children[0].dispatchEvent(new Event(modifiers.lazy ? 'change' : 'input'))
    } catch (e) { }
  }
})
// 设置隐藏函数
var timeout = false
let setRowDisableNone = function (topNum, showRowNum, binding) {
  if (timeout) {
    clearTimeout(timeout)
  }
  timeout = setTimeout(() => {
    binding.value.call(null, topNum, topNum + showRowNum + spillDataNum)
  })
}
Vue.directive('staticLoadMore', {
  componentUpdated: function (el, binding, vnode, oldVnode) {
    setTimeout(() => {
      const dataSize = vnode.data.attrs['data-size']
      const oldDataSize = oldVnode.data.attrs['data-size']
      if (dataSize === oldDataSize) {
        return
      }
      const selectWrap = el.querySelector('.el-table__body-wrapper')
      const selectTbody = selectWrap.querySelector('table tbody')
      const selectRow = selectWrap.querySelector('table tr')
      if (!selectRow) {
        return
      }
      const rowHeight = selectRow.clientHeight
      let showRowNum = Math.round(selectWrap.clientHeight / rowHeight)
      const createElementTR = document.createElement('tr')
      let createElementTRHeight = (dataSize - showRowNum - spillDataNum) * rowHeight
      createElementTR.setAttribute('style', `height: ${createElementTRHeight}px;`)
      selectTbody.append(createElementTR)
      // 监听滚动后事件
      selectWrap.addEventListener('scroll', function () {
        let topPx = this.scrollTop - spillDataNum * rowHeight
        let topNum = Math.round(topPx / rowHeight)
        let minTopNum = dataSize - spillDataNum - showRowNum
        if (topNum > minTopNum) {
          topNum = minTopNum
        }
        if (topNum < 0) {
          topNum = 0
          topPx = 0
        }
        selectTbody.setAttribute('style', `transform: translateY(${topPx}px)`)
        createElementTR.setAttribute('style', `height: ${createElementTRHeight - topPx > 0 ? createElementTRHeight - topPx : 0}px;`)
        setRowDisableNone(topNum, showRowNum, binding)
      })
    })
  }
})
// v-scrollMove
Vue.directive('scrollMove', {
  bind(el, binding, vnode, oldVnode) {
    // 定义滚动事件处理函数
    function handleScroll(e) {
      e.preventDefault()
      let detail = e.wheelDelta || e.detail
      let moveForwardStep = -1
      let moveBackStep = 1
      let step = 0
      step = detail > 0 ? moveForwardStep * 30 : moveBackStep * 30
      el.scrollBy({
        left: step
      })
    }
    el.addEventListener('DOMMouseScroll', handleScroll, false)
    el.addEventListener('mousewheel', handleScroll, false)
    // 存储事件处理函数，以便在unbind中移除监听器
    el._scrollMoveHandler = handleScroll
  },
  unbind(el) {
    // 移除滚动事件监听器
    el.removeEventListener('DOMMouseScroll', el._scrollMoveHandler)
    el.removeEventListener('mousewheel', el._scrollMoveHandler)
  }
})
// 用来存储当前监听元素和value
const map = new WeakMap()
const ob = new ResizeObserver((entries) => {
  // 当元素尺寸发生变化时，循环取出binding更新视图
  for (const entrie of entries) {
    const binding = map.get(entrie.target)
    generateTip(entrie.target, binding)
  }
})
// 创建Tip组件
const TooltipComponent = Vue.extend({
  components: {
    'el-tooltip': ElTooltip
  },
  props: ['value'],
  template: `
		<el-tooltip :content="value" effect="light" placement="top">
			<div><span>{{value}}</span></div>
		</el-tooltip>
	`
})
// 移除body下tip
function removeTip(el) {
  const id = el.firstChild && el.firstChild.getAttribute && el.firstChild.getAttribute('aria-describedby')
  setTimeout(() => {
    const tips = document.getElementById(id)
    if (tips) document.body.removeChild(tips)
  })
}
// 添加taps
function generateTip(el, binding) {
  const fullText = binding.value
  // 判断值是否为空
  if (!fullText || !String(fullText).trim()) {
    // 添加公共样式类名
    el.classList.add('seizeSeat')
    // 自定义占位字符默认 '---'
    const { placeholder } = el.dataset
    el.innerHTML = placeholder === undefined ? '---' : placeholder
  } else {
    // 移除公共样式类名 消除值为空时添加类名带来的影响，没有该类名时不做处理
    el.classList.remove('seizeSeat')
    // 创建组件实例
    const tooltip = new TooltipComponent({
      propsData: { value: binding.value.toString() }
    }).$mount()
    // 获取元素tip id 用于移除body下tip节点
    removeTip(el)
    // 重置父元素内容
    el.innerHTML = ''
    // 添加tip节点到父节点下，用于获取节点尺寸
    el.appendChild(tooltip.$el)
    // 获取元素padding
    const style = window.getComputedStyle(el)
    const paddingLeft = parseFloat(style.getPropertyValue('padding-left'))
    const paddingRight = parseFloat(style.getPropertyValue('padding-right'))
    // 获取元素border
    const borderLeft = parseFloat(style.getPropertyValue('border-left'))
    const borderRight = parseFloat(style.getPropertyValue('border-right'))
    // 获取 tip 元素宽度包含小数
    const tipWidth = tooltip.$el.firstChild.getBoundingClientRect().width
    // 父元素宽度包含小数 - paddingLeft - paddingRight - borderLeft - borderRight 获取父元素实际内容宽度
    const elWidth = el.getBoundingClientRect().width - paddingLeft - paddingRight - borderLeft - borderRight
    // tipWidth宽度如果不大于父元素的宽度则直接给父元素赋值text，覆盖原来添加的tip
    if (tipWidth <= elWidth) {
      el.innerHTML = fullText
    }
  }
}
// 动态元素省略显示tips
Vue.directive('showtipPlus', {
  inserted(el, binding) {
    el.classList.add('ellipsis_showtip_plus')
    map.set(el, binding)
    ob.observe(el)
  },
  update(el, binding) {
    map.set(el, binding)
    generateTip(el, binding)
  },
  unbind(el) {
    ob.unobserve(el)
    map.delete(el)
    removeTip(el)
  }
})
// 动态元素省略显示tips
Vue.directive('selectLoadmore', {
  bind(el, binding, vnode, oldVnode) {
    // 获取element-ui定义好的scroll盒子
    const SELECTWRAP_DOM = el.querySelector(
      ".el-select-dropdown .el-select-dropdown__wrap"
    );
    SELECTWRAP_DOM.addEventListener("scroll", function () {
      /**
       * scrollHeight 获取元素内容高度(只读)
       * scrollTop 获取或者设置元素的偏移值,常用于, 计算滚动条的位置, 当一个元素的容器没有产生垂直方向的滚        动 条, 那它的scrollTop的值默认为0.
       * clientHeight 读取元素的可见高度(只读)
       * 如果元素滚动到底, 下面等式返回true, 没有则返回false:
       * ele.scrollHeight - ele.scrollTop === ele.clientHeight;
       */
      const condition =
        this.scrollHeight - this.scrollTop <= this.clientHeight;
      if (condition) {
        binding.value();
      }
    });
  }
})
