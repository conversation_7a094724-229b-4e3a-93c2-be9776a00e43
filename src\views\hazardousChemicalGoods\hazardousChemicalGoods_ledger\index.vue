<script>
import tableListMixin from '@/mixins/tableListMixin'
import { transData } from '@/util'
export default {
  name: 'hazardousChemicalGoods_ledger',
  mixins: [tableListMixin],
  components: {
    exportDialog: () => import('../hazardousChemicalGoodsManage/components/exportDialog.vue')
  },
  data() {
    return {
      treeSearchKeyWord: '',
      searchForm: {
        name: '' //危化品名称/编码
      },
      defaultProps: {
        value: 'id',
        label: function (data, node) {
          if (data.name) {
            return `${data.name}(${data.count})`
          } else {
            return `${data.warehouseName}`
          }
        }
      },
      checkedData: {}, //tree点击数据
      typeMenu: [
        {
          name: '危化品分类',
          value: '1'
        },
        {
          name: '危化品库房',
          value: '2'
        }
      ],
      activeName: '1',
      treeData: [],
      treeLoadingStatus: false,
      materialTypeCode: '',
      tableData: [],
      multipleSelection: [],
      tableLoadingStatus: false,
      exportDialogShow: false
    }
  },
  mounted() {
    this.getClassifyTreeData()
  },
  methods: {
    // 切换tabs
    handleClick(tab, event) {
      this.activeName = tab.name
      this.checkedData = {}
      this.materialTypeCode = ''
      this.treeData = []
      if (this.activeName === '1') {
        this.getClassifyTreeData()
      } else {
        this.getWarehouseTreeData()
      }
    },
    // 获取分类数据
    getClassifyTreeData() {
      const userInfo = this.$store.state.user.userInfo.user
      this.treeLoadingStatus = true
      let params = {
        type: 'WZFL',
        userId: userInfo.staffId,
        userName: userInfo.staffName,
        status: 1
      }
      this.$api
        .materialTypeTreeKC(params)
        .then((res) => {
          this.treeLoadingStatus = false
          if (res.code === '200') {
            this.treeData = transData(res.data, 'id', 'pid', 'children')
            this.onSearch()
          } else {
            throw res.data.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取字典类型失败'))
        .finally(() => {
          this.treeLoadingStatus = false
        })
    },
    //获取库房tree数据
    getWarehouseTreeData() {
      this.$api
        .getWarehouseList({ status: '0' })
        .then((res) => {
          this.treeLoadingStatus = false
          if (res.code === '200') {
            this.treeData = transData(res.data.list, 'id', 'parentId', 'children')
            this.onSearch()
          } else {
            throw res.data.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取字典类型失败'))
        .finally(() => {
          this.treeLoadingStatus = false
        })
    },
    // 当树点击被点击时
    onTreeNodeClick(data, node) {
      if (this.activeName === '1') {
        let temp = this.findNodeAndChildren(this.treeData, data.id)
        this.materialTypeCode = temp && temp.length ? temp.map((item) => item.code).join(',') : ''
        this.onReset()
      } else {
        this.checkedData = data
        this.onReset()
      }
    },
    // 通过当前ID查找所有子节点，返回结果为一维数组
    findNodeAndChildren(tree, targetId, childKey = 'children') {
      // 查找目标节点
      const findNode = (nodes) => {
        for (const node of nodes) {
          if (node.id === targetId) return node
          if (node[childKey]) {
            const found = findNode(node[childKey])
            if (found) return found
          }
        }
      }

      // 收集所有子节点
      const collectChildren = (node, result = []) => {
        if (!node) return []
        result.push(node)
        if (node[childKey]) {
          node[childKey].forEach((child) => collectChildren(child, result))
        }
        return result
      }

      const target = findNode(tree)
      return target ? collectChildren(target) : []
    },
    //导出
    exportShow() {
      this.exportDialogShow = true
    },
    // 获取列表数据
    getDataList() {
      this.tableData = []
      this.tableLoadingStatus = true
      const params = {
        currentPage: this.pagination.current,
        pageSize: this.pagination.size,
        materialName: this.searchForm.name,
        materialCode: this.searchForm.name,
        materialTypeCode: this.materialTypeCode,
        warehouseId: this.checkedData.id || ''
      }
      this.$api
        .materialinfoListData(params)
        .then((res) => {
          this.tableLoadingStatus = false
          if (res.code === '200') {
            this.tableData = res.data.list
            this.pagination.total = res.data.sum
          } else {
            this.tableData = []
            this.pagination.total = 0
          }
        })
        .catch((err) => {
          this.tableLoadingStatus = false
        })
    },
    // 表单搜索按钮点击
    onSearch() {
      this.pagination.current = 1
      this.getDataList()
    },
    // 表单重置按钮点击
    onReset() {
      this.$refs.formRef.resetFields()
      this.onSearch()
    },
    //  ------------------------------------------------操作表格中数据------------------------------------------------------------
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    //导出
    submitExportDialog(checkList) {
      let arr = []
      checkList.forEach((item) => {
        arr.push(item.EnglishName)
      })
      let temp = arr.join(',')
      let temp1 = []
      this.multipleSelection.forEach((v, i) => {
        temp1[i] = this.multipleSelection[i].id
      })
      const userInfo = this.$store.state.user.userInfo.user
      temp1 = temp1.join(',')
      let url =
        __PATH.BASE_URL_HSC +
        'materialinfo/export?titles=' +
        temp +
        '&ids=' +
        temp1 +
        '&unitCode=' +
        userInfo.unitCode +
        '&hospitalCode=' +
        userInfo.hospitalCode +
        '&userId=' +
        userInfo.staffId +
        '&userName=' +
        userInfo.staffName
      var a = document.createElement('a')
      a.href = url
      a.target = '_self'
      a.click()
      this.exportDialogShow = false
    }
  }
}
</script>
<template>
  <PageContainer class="hazardousChemicalGoods_ledger">
    <template #content>
      <div class="hazardousChemicalGoods_ledger__left">
        <div class="hazardousChemicalGoods_ledger__left__header">
          <div class="toptip">
            <span class="green_line"></span>
            危化品类型
          </div>
          <el-tabs v-model="activeName" @tab-click="handleClick" class="tabs">
            <el-tab-pane v-for="(i, index) in typeMenu" :key="index" :name="i.value">
              <span slot="label">{{ i.name }}</span>
            </el-tab-pane>
          </el-tabs>
        </div>
        <el-tree
          ref="treeRef"
          node-key="id"
          :data="treeData"
          :props="defaultProps"
          current-node-key="id"
          :check-on-click-node="true"
          :expand-on-click-node="false"
          default-expand-all
          highlight-current
          class="hazardousChemicalGoods_ledger__tree"
          @node-click="onTreeNodeClick"
        ></el-tree>
      </div>
      <div class="hazardousChemicalGoods_ledger__right">
        <div class="hazardousChemicalGoods_ledger__right__header">
          <el-form ref="formRef" :model="searchForm" class="hazardousChemicalGoods_ledger__search" inline @submit.native.prevent="onSearch">
            <el-form-item prop="name">
              <el-input v-model="searchForm.name" clearable filterable suffix-icon="el-icon-search" placeholder="危化品名称/编码"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onSearch">查询</el-button>
              <el-button type="primary" plain @click="onReset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="hazardousChemicalGoods_ledger__right__actions">
          <el-button type="primary" v-auth="'hazardousChemicalsLedger:export'" @click="exportShow" :disabled="multipleSelection.length === 0"> 导出 </el-button>
        </div>
        <div class="hazardousChemicalGoods_ledger__table">
          <el-table
            v-loading="tableLoadingStatus"
            height="100%"
            :data="tableData"
            border
            stripe
            table-layout="auto"
            @selection-change="handleSelectionChange"
            class="tableAuto"
            row-key="id"
          >
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column prop="materialName" show-overflow-tooltip label="危化品名称"></el-table-column>
            <el-table-column prop="materialCode" show-overflow-tooltip label="危化品编码"></el-table-column>
            <el-table-column prop="basicUnitName" show-overflow-tooltip label="基本单位"></el-table-column>
            <el-table-column prop="model" show-overflow-tooltip label="规格型号"></el-table-column>
            <el-table-column prop="warehouseName" show-overflow-tooltip label="存放地名称"></el-table-column>
            <el-table-column prop="inventory" show-overflow-tooltip label="库存数量"></el-table-column>
            <el-table-column prop="minStock" show-overflow-tooltip label="最低库存"></el-table-column>
            <el-table-column prop="maxStock" show-overflow-tooltip label="最高库存"></el-table-column>
            <el-table-column prop="materialTypeName" show-overflow-tooltip label="所属分类"></el-table-column>
            <el-table-column prop="goodsCode" show-overflow-tooltip label="商品编码"></el-table-column>
            <!-- <el-table-column prop="trademark" show-overflow-tooltip label="品牌"></el-table-column> -->
            <el-table-column prop="manufacturerName" show-overflow-tooltip label="生产厂家"></el-table-column>
          </el-table>
        </div>
        <el-pagination
          class="hazardousChemicalGoods_ledger__pagination"
          :current-page="pagination.page"
          :page-sizes="pagination.pageSizeOptions"
          :page-size="pagination.size"
          :layout="pagination.layoutOptions"
          :total="pagination.total"
          @size-change="paginationSizeChange"
          @current-change="paginationCurrentChange"
        >
        </el-pagination>
      </div>
      <!--导出 -->
      <template v-if="exportDialogShow">
        <exportDialog
          :exportDialogShow="exportDialogShow"
          @submitExportDialog="submitExportDialog"
          :exportType="1"
          @closeExportDialog="
            () => {
              exportDialogShow = false
            }
          "
        />
      </template>
    </template>
  </PageContainer>
</template>
<style scoped lang="scss">
.hazardousChemicalGoods_ledger {
  ::v-deep(> .container-content) {
    display: flex;
    background-color: #fff;
  }
  &__left {
    width: 260px;
    border-right: solid 1px #eee;
    display: flex;
    justify-content: center;
    flex-flow: column nowrap;
    .tabs {
      padding: 0 8px;
    }
    &__header {
      padding: 0 16px;
      .toptip {
        box-sizing: border-box;
        height: 50px;
        width: 100%;
        line-height: 50px;
        display: flex;
        font-size: 16px;
        align-items: center;
        border: none !important;
      }
      .green_line {
        display: inline-block;
        width: 6px;
        height: 16px;
        border-radius: 2px;
        background: #3562db;
        margin-right: 10px;
        vertical-align: middle;
      }
    }
    &::v-deep(.el-tree) {
      height: calc(100% - 64px);
      overflow: auto;
      padding: 8px 16px 16px 16px;
      .el-tree-node__content {
        line-height: 36px;
        height: 36px;
      }
      .el-tree-node.is-current > .el-tree-node__content {
        color: #3562db;
        background: #e6effc;
      }
    }
  }
  &__right {
    flex: 1;
    overflow: hidden;
    padding: 16px;
    display: flex;
    flex-flow: column nowrap;
    .el-input,
    .el-select {
      width: 180px !important;
    }
  }
  &__right__header {
    display: flex;
    justify-content: space-between;
  }
  &__right__actions {
    padding-bottom: 10px;
  }
  &__table {
    flex: 1;
  }
  &__pagination {
    margin-top: 10px;
  }
}
.el-form-item {
  margin-bottom: 8px !important;
  .el-input {
    width: 250px !important;
  }
}
</style>
