<template>
  <PageContainer>
    <template slot="content">
      <div class="sino-all-content">
        <div class="top">
          <div class="title">运行总览</div>
          <div class="overview_mode">
            <span>今日运行模式</span>
            <div class="pattern_box active_pattern">
              <img style="width: 30px; height: 30px" :src="todayPattern.patternActiveSrc" />
              <span>{{ todayPattern.patternName }}</span>
            </div>
          </div>
        </div>
        <div class="content">
          <div class="left-content">
            <el-input v-model="filterText" style="width: 230px; padding-top: 10px" placeholder="请输入关键字" />
            <div v-loading="treeLoading" class="sino_tree_box">
              <ZkRenderTree
                ref="tree"
                :data="treeData"
                :props="defaultProps"
                :filter-node-method="filterNode"
                :highlight-current="true"
                node-key="id"
                @node-click="nodeClick"
              ></ZkRenderTree>
            </div>
          </div>
          <div class="right-content">
            <iframe ref="unityIframe" class="Iframe_class" src="/U3D_szzlyy/index.html"></iframe>
            <div v-show="showFloorInfo" class="content-right">
              <p class="content-right-title">统计总览</p>
              <div class="content-right-main">
                <div class="content-right-total">
                  <p>回路总数</p>
                  <p>{{ overviewData.total || 0 }}<span>个</span></p>
                </div>
                <echarts slot="content" ref="setPieChart" height="200px" width="320px" domId="setPieChart" />
                <div class="content-right-list">
                  <div v-for="(item, index) in circuitList" :key="item.actuatorId + index" class="right-list-item">
                    <p class="item-title">{{ item.localName }}</p>
                    <div v-for="(v, i) in item.child" :key="v.actuatorId + i" class="item-child">
                      <p class="item-child-name">{{ v.loopName }}</p>
                      <p class="item-child-text">服务空间：{{ v.spaceNames }}</p>
                      <p class="item-child-status" :class="{ 'status-off': v.outputStatus != 1 }">{{ v.outputStatus == 1 ? '开启' : '关闭' }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <spaceDetailsDialog v-if="showSpaceDetails" :selectRoom="selectRoom" :projectCode="projectCode" :visible.sync="showSpaceDetails" />
    </template>
  </PageContainer>
</template>
<script lang="jsx">
import { transData } from '@/util'
import sunshine_weather_active from '@/assets/images/lightingMonitoring/sunshine_weather_active.png'
import Dict from '../components/dict.js'
import { monitorTypeList } from '@/util/dict.js'
export default {
  name: 'filledRecord',
  components: {
    spaceDetailsDialog: () => import('../components/spaceDetailsDialog')
  },
  data() {
    return {
      selectNode: {},
      selectRoom: {},
      rooms: [],
      showSpaceDetails: false, // 空间明细
      showFloorInfo: false,
      projectCode: monitorTypeList.find((item) => item.projectName == '照明监测').projectCode,
      todayPattern: {
        patternName: '晴天模式',
        patternActiveSrc: sunshine_weather_active
      },
      patternTypeIconList: Dict.patternTypeIconList,
      filterText: '',
      treeLoading: true,
      treeData: [],
      defaultProps: {
        label: 'ssmName',
        children: 'list'
      },
      overviewData: {}, // 总览数据
      circuitList: []
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.getTreeRef().filter(val)
    }
  },
  created() {
    window.getModelMsg = this.getModelMsg
    window.openDialog = this.openDialog
    window.lightingSwitch = this.lightingSwitch
  },
  mounted() {
    this.getMonitorStatusList()
    this.spaceTreeListFn()
  },
  methods: {
    // 获取回路列表
    groupOperationMonitoring(param, roomData) {
      let params = {
        type: 1,
        pageSize: 99,
        page: 1,
        projectCode: this.projectCode,
        ...param
      }
      const userInfo = this.$store.state.user.userInfo.user
      let childList = []
      let rooms = {}
      let sendData = []
      this.rooms = []
      const floorCode = this.selectNode.modelCode.split(userInfo.hospitalCode)[1]
      const roomNames = roomData.map((item) => {
        item.floorId = param.floorId
        rooms[item.id] = item.modelCode
        this.rooms[item.modelCode] = item
        return {
          modelCode: floorCode + item.roomCode,
          roomCode: item.roomCode,
          roomName: item.localSpaceName
        }
      })
      // 房间名渲染
      this.$refs.unityIframe.contentWindow.VueSendToUnity(
        JSON.stringify({
          type: 'floorNameModel',
          data: {
            floorCode: floorCode,
            roomNames: roomNames
          }
        })
      )
      this.$api.groupOperationMonitoring(params).then((res) => {
        if (res.code == '200') {
          this.circuitList = res.data
          res.data.forEach((item) => {
            childList = childList.concat(item.child)
          })
          childList.forEach((item) => {
            item.spaceId.split(',').forEach((v) => {
              if (rooms[v]) {
                sendData.push({
                  // RoomCode: '0100101111',
                  RoomCode: rooms[v].split(userInfo.hospitalCode)[1],
                  PointType: 1,
                  StateType: item.outputStatus, // 0开启 1关闭
                  Visibility: 1 // 1,显示灯泡，0隐藏灯泡
                })
              }
            })
          })
          console.log('====', {
            type: 'buildFloorPointModel',
            data: sendData
          })
          this.$refs.unityIframe.contentWindow.VueSendToUnity(
            JSON.stringify({
              type: 'buildFloorPointModel',
              data: sendData
            })
          )
        }
      })
    },
    // 获取回路统计数据
    getLoopStatisticsData(params) {
      const arr = [
        {
          name: '开启',
          value: 0,
          color: '#3562DB'
        },
        {
          name: '关闭',
          value: 0,
          color: '#FF9435'
        },
        {
          name: '其他',
          value: 0,
          color: '#00BC6D'
        }
      ]
      this.$api.GetLightingStatistics(params).then((res) => {
        if (res.code === '200') {
          this.overviewData = res.data
          arr[0].value = res.data.openCount || 0
          arr[1].value = res.data.closeCount || 0
          arr[2].value = res.data.alarmCount || 0
          this.$nextTick(() => {
            if (this.showFloorInfo) {
              this.$refs.setPieChart.init(this.setPieChart(arr))
            }
          })
        }
      })
    },
    // 统计图echarts
    setPieChart(data) {
      return {
        color: data.map((v) => v.color),
        tooltip: {
          formatter: '{b0}<br />{c0}个<br />{d}%'
        },
        series: [
          {
            type: 'pie',
            radius: [0, 60],
            center: ['50%', '50%'],
            data: data,
            // itemStyle: {
            //   borderColor: '#fff',
            //   borderWidth: 2
            // },
            labelLine: {
              length: 10,
              length2: 10
            },
            label: {
              formatter: '{name|{b}}{value|{c}个}{value|{d}%}',
              // padding: [0, -30, 15, -30],
              rich: {
                name: {
                  fontSize: 12,
                  color: '#414653'
                },
                value: {
                  fontSize: 12,
                  padding: [0, 0, 0, 4],
                  color: '#414653'
                }
              }
            }
          }
        ]
      }
    },
    // 模型开关切换事件
    lightingSwitch(data) {
      this.allOperation(data[0].Visibility == 1 ? 0 : 1, data[0].RoomCode)
    },
    allOperation(status, modelCode) {
      const userInfo = this.$store.state.user.userInfo.user
      const params = {
        type: 4,
        forceSwitch: null,
        outputStatus: status,
        serviceSpaceId: this.rooms[userInfo.hospitalCode + modelCode].id
      }
      this.$api.lightOpenOrClose(params).then((res) => {
        if (res.code === '200') {
          this.nodeClick(this.selectNode)
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    // 模型点击事件
    openDialog(modelCode) {
      const userInfo = this.$store.state.user.userInfo.user
      this.selectRoom = this.rooms[userInfo.hospitalCode + modelCode.join('')]
      this.showSpaceDetails = true
    },
    // 模型hover事件
    getModelMsg(modelCode) {},
    spaceTreeListFn() {
      this.$api.spaceTreeList().then((res) => {
        this.treeLoading = false
        if (res.code == 200) {
          this.treeData = transData(res.data, 'id', 'pid', 'list')
        }
      })
    },
    filterNode(value, data) {
      if (!value) return true
      return data.ssmName.indexOf(value) !== -1
    },
    nodeClick(node) {
      this.selectNode = node
      // 执行楼层切换
      this.showFloorInfo = node.ssmType == 4
      if (node.ssmType === 4) {
        let codeArr = this.checkNumber(node.modelCode)
        this.$api.GetRoomInfoList({ modelCode: node.modelCode }).then((res) => {
          if (res.code == 200) {
            this.groupOperationMonitoring(
              {
                constructionId: node.pid,
                floorId: node.id
              },
              res.data
            )
          }
        })
        this.getLoopStatisticsData({
          projectCode: this.projectCode,
          floorId: node.id // 楼层id
        })
        let sendData = JSON.stringify({
          spacesn_area: codeArr[0],
          spacesn_build: codeArr[1],
          spacesn_floor: codeArr[2],
          type: 'openbuildlevel'
        })
        this.$refs.unityIframe.contentWindow.VueSendToUnity(sendData)
      }
    },
    // 按层级获取空间编码
    checkNumber(str) {
      if (!str) return false
      str = str.replace(/[^0-9]/g, '')
      let result = str.split(/(\d{2})(\d{3})/).filter(Boolean)
      return result
    },
    // 获取总览数据
    getMonitorStatusList() {
      this.$api.getCountRunToDayState({}).then((res) => {
        if (res.code == '200') {
          let weatherPattern = res.data[2].weatherPattern
          // 今日运行模式
          this.todayPattern = {
            patternActiveSrc: this.patternTypeIconList.find((item) => item.patternCode == weatherPattern.patternType)?.patternActiveSrc,
            patternName: weatherPattern.patternName
          }
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.sino-all-content {
  height: calc(100% - 0px);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background-color: #f6f5fa;
  .top {
    border-radius: 4px;
    height: 56px;
    line-height: 56px;
    background-color: #fff;
    display: flex;
    justify-content: space-between;
    padding: 0 17px;
    color: #333333;
    .title {
      font-size: 18px;
      font-family: PingFang SC-Semibold, PingFang SC;
      font-weight: 600;
    }
    .overview_mode {
      display: flex;
      .pattern_box {
        box-sizing: border-box;
        height: 38px;
        line-height: 38px;
        padding: 0 30px;
        border-radius: 24px;
        margin: auto 15px;
        cursor: pointer;
        img {
          vertical-align: middle;
          margin-right: 10px;
        }
        span {
          font-size: 14px;
          font-family: 'HarmonyOS_Sans_SC';
          color: #333;
        }
      }
      .active_pattern {
        background: #fffae8;
        border: 1px solid #ffe8ab;
      }
    }
  }
  .content {
    height: calc(100% - 72px);
    margin-top: 10px;
    display: flex;
    justify-content: space-between;
    .left-content {
      width: 250px;
      height: 100%;
      background-color: #fff;
      border-radius: 4px;
      text-align: center;
      ::v-deep .sino_tree_box {
        margin: 10px;
        height: calc(100% - 65px) !important;
        overflow: auto;
      }
    }
    .right-content {
      box-sizing: content-box;
      width: calc(100% - 265px);
      height: 100%;
      background-color: #fff;
      border-radius: 4px;
      position: relative;
      .Iframe_class {
        width: 100%;
        height: 100%;
        border: none;
      }
      .content-right {
        display: flex;
        flex-direction: column;
        position: absolute;
        right: 10px;
        top: 10px;
        bottom: 10px;
        width: 320px;
        background: rgba(255, 255, 255, 0.86);
        border-radius: 4px;
        border: 1px solid #e4e7ed;
        p {
          margin: 0;
        }
        .content-right-title {
          padding: 16px;
          font-size: 16px;
          font-weight: 600;
          color: #333333;
          line-height: 19px;
        }
        .content-right-main {
          box-sizing: content-box;
          flex: 1;
          overflow-y: auto;
          padding-bottom: 16px;
        }
        .content-right-total {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 16px 24px;
          margin: 0px 16px 16px 16px;
          background: #faf9fc;
          border-radius: 4px;
          p:first-child {
            font-size: 14px;
            font-weight: 400;
            color: #666666;
            line-height: 16px;
          }
          p:last-child {
            font-size: 20px;
            font-weight: bold;
            color: #333333;
            line-height: 32px;
            span {
              margin-left: 4px;
              font-size: 12px;
              font-weight: 500;
              color: #7f848c;
              line-height: 14px;
            }
          }
        }
        .content-right-list {
          .right-list-item {
            margin: 16px 16px 0px 16px;
            .item-title {
              font-size: 14px;
              font-weight: 500;
              color: #333333;
              line-height: 16px;
            }
            .item-child {
              padding: 16px 70px 16px 24px;
              background: #fff;
              border: 1px solid #e6effc;
              border-radius: 4px;
              margin-top: 10px;
              position: relative;
              .item-child-name {
                font-size: 15px;
                font-weight: 500;
                color: #333333;
                line-height: 15px;
              }
              .item-child-text {
                font-size: 14px;
                font-weight: 400;
                color: #7f848c;
                margin-top: 10px;
                line-height: 14px;
              }
              .item-child-status {
                position: absolute;
                right: 24px;
                top: 50%;
                transform: translateY(-50%);
                padding: 2px 8px;
                border-radius: 4px;
                font-size: 14px;
                font-weight: 400;
                line-height: 16px;
                background: #e8ffea;
                color: #009a29;
              }
              .status-off {
                background: #fff7e8;
                color: #d25f00;
              }
            }
          }
        }
      }
    }
  }
}
</style>
