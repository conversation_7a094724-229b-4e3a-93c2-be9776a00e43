<template>
  <PageContainer>
    <div slot="content" class="role-content" style="height: 100%;">
      <div class="role-content-left">
        <div class="toptip">
          <span class="green_line"></span>
          管控小组
        </div>
        <div class="left_content">
          <el-input v-model.trim="filterText" clearable placeholder="输入关键字进行过滤"></el-input>
          <div class="tree">
            <el-tree
              ref="tree"
              v-loading="treeLoading"
              style="margin-top: 10px;"
              :data="treeData"
              :props="defaultProps"
              :default-expanded-keys="expanded"
              :filter-node-method="filterNode"
              node-key="id"
              highlight-current
              @node-click="handleNodeClick"
              @check="treeChecked"
            ></el-tree>
          </div>
        </div>
      </div>
      <div class="role-content-right">
        <div style="height: 100%;">
          <div class="search-from">
            <el-input v-model.trim="filters.teamName" clearable placeholder="小组名称" style="width: 200px;"></el-input>
            <el-input v-model.trim="filters.responsiblePersonName" clearable placeholder="负责人姓名" style="width: 200px;"></el-input>
            <el-select v-model="filters.structureType" clearable class="sino_sdcp_input mr15" placeholder="组织机构类型" filterable>
              <el-option v-for="(item, index) in structureTypeList" :key="index" :label="item.responsiblePersonName" :value="item.id" class="set_zindex"></el-option>
            </el-select>
            <el-select v-model="filters.useStatus" class="sino_sdcp_input mr15" placeholder="小组状态" filterable clearable>
              <el-option v-for="(item, index) in ztList" :key="index" :label="item.responsiblePersonName" :value="item.id" class="set_zindex"></el-option>
            </el-select>
            <el-button type="primary" @click="resetData">重置</el-button>
            <el-button type="primary" class="sino-button-sure-search" style="margin-top: 10px;" @click="init">查询</el-button>
          </div>
          <div class="middle_tools">
            <el-button type="primary" icon="el-icon-plus" @click="addDate">新增</el-button>
            <el-button type="primary" icon="el-icon-edit" :disabled="multipleSelection.length != 1" @click="upData">编辑</el-button>
            <el-button type="primary" icon="el-icon-delete" :disabled="multipleSelection.length != 1" @click="delData">删除</el-button>
            <el-button type="primary" icon="el-icon-download" @click="downLoad">导出</el-button>
          </div>
          <div class="contentTable">
            <div class="contentTable-main table-content">
              <el-table
                ref="multipleTable"
                v-loading="tableLoading"
                highlight-current-row
                :data="tableData"
                border
                stripe
                style="width: 100%;"
                :height="tableHeight"
                @row-click="rowHandleClick"
                @selection-change="handleSelectionChange"
                @row-dblclick="dblclick"
              >
                <el-table-column type="selection" width="55" align="center"></el-table-column>
                <el-table-column type="index" width="65" label="序号" align="center">
                  <template slot-scope="scope">
                    <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="teamName" show-overflow-tooltip label="小组名称"></el-table-column>
                <el-table-column prop="teamCode" show-overflow-tooltip label="小组编码"></el-table-column>
                <el-table-column prop="structureType" show-overflow-tooltip label="组织机构">
                  <template slot-scope="scope">
                    <span>
                      {{ scope.row.structureType == 1 ? '院级' : scope.row.structureType == 2 ? '科级' : '' }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="responsiblePersonName" show-overflow-tooltip label="负责人"></el-table-column>
                <el-table-column prop="groupTypeName" show-overflow-tooltip label="状态">
                  <template slot-scope="scope">
                    <span>{{ scope.row.useStatus == 1 ? '启用' : scope.row.useStatus == 2 ? '禁用' : '' }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="contentTable-footer">
              <el-pagination
                :current-page="paginationData.currentPage"
                :page-sizes="[15, 30, 50, 100]"
                :page-size="paginationData.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="paginationData.total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              ></el-pagination>
            </div>
          </div>
        </div>
      </div>
      <exportDialog
        ref="dialog-export"
        :exportType="exportType"
        :materRows="multipleSelection"
        :formInline="filters"
        :dialogVisibleExport="dialogVisibleExport"
        @closeDialog="closeDialog"
      ></exportDialog>
    </div>
  </PageContainer>
</template>

<script>
import tableListMixin from '@/mixins/tableListMixin.js'
import { transData } from '@/util'
import exportDialog from '@/components/recordExport/recordExport' // 导出
export default {
  name: 'securityControl',
  components: { exportDialog },
  mixins: [tableListMixin],
  data() {
    return {
      defaultKeys: '',
      filterText: '',
      structureTypeList: [
        {
          responsiblePersonName: '院级',
          id: 1
        },
        {
          responsiblePersonName: '科级',
          id: 2
        }
      ],
      ztList: [
        {
          responsiblePersonName: '启用',
          id: 1
        },
        {
          responsiblePersonName: '禁用',
          id: 2
        }
      ],
      filters: {
        responsiblePersonName: '',
        teamName: '',
        structureType: '',
        useStatus: ''
      },
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      multipleSelection: [],
      tableData: [],
      advancClose: true,
      treeLoading: false,
      tableLoading: false,
      organizationTypeArr: [],
      defaultProps: {
        children: 'children',
        label: function (data, node) {
          return data.teamName
        },
        value: 'id'
      },
      treeData: [],
      checkedData: '',
      dialogVisibleExport: false,
      exportType: 7,
      expanded: []
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    },
    screenHeight(n, o) {}
  },
  // activated() {
  //   console.log(this.defaultKeys)
  //   this.init()
  // },
  mounted() {
    this.init()
  },
  methods: {
    rowHandleClick(row, column, event) {
      this.$refs.multipleTable.toggleRowSelection(row)
    },
    getTblleList() {
      let data = {
        ...this.filters,
        ...this.paginationData,
        allParentIds: this.checkedData.id
      }
      delete data.total
      this.$api.ipsmGetControlGroupInfoList(data).then((res) => {
        if (res.code == 200) {
          this.paginationData.total = res.data.sum
          this.tableData = res.data.list
        }
      })
    },

    init() {
      this.treeLoading = true
      // 获取管控小组
      this.$api.ipsmGetControlGroupInfoList({}).then((res) => {
        this.treeLoading = false
        let planText = {
          id: '#',
          teamName: '安全管控部门',
          parentId: '',
          allParentIds: '',
          level: 0
        }
        let list = res.data.list
        list.push(planText)
        this.treeData = transData(list, 'id', 'parentId', 'children')
        this.expanded = this.defaultKeys ? [this.defaultKeys] : [this.treeData[0].id]
        this.$nextTick(() => {
          this.$refs.tree.setCurrentKey(this.defaultKeys ? this.defaultKeys : this.treeData[0].id)
        })
        this.checkedData = this.treeData[0]
        this.tableData = []
        this.getTblleList()
      })
    },
    // 新增
    addDate() {
      this.$router.push({
        name: 'addSecurity',
        query: { type: 'add', tableCode: this.checkedData }
      })
    },
    // 修改
    upData() {
      this.$router.push({
        name: 'addSecurity',
        query: { type: 'edit', id: this.multipleSelection[0].id }
      })
    },
    // 详情
    dblclick(val) {
      this.$router.push({
        name: 'addSecurity',
        query: { type: 'check', id: val.id }
      })
    },
    // 删除
    delData() {
      this.$confirm('确认删除?', '提醒', { type: 'warning' }).then((res) => {
        this.$api.ipsmDelControlGroupInfo({ id: this.multipleSelection[0].id }).then((res) => {
          if (res.code == 200) {
            // 删除最后一页的最后一条数据时跳转回最后一页的上一页
            this.paginationData.currentPage = this.$tools.paginationData(this.paginationData.total, this.paginationData.pageSize, this.paginationData.currentPage)

            this.init()
            return this.$message.success(res.message)
          } else {
            return this.$message.error(res.message)
          }
        })
      })
    },
    filterNode(value, data) {
      if (!value) return true
      return data.teamName.indexOf(value) !== -1
    },
    // 导出
    downLoad() {
      this.dialogVisibleExport = true
    },
    closeDialog() {
      this.dialogVisibleExport = false
    },

    // 重置查询表单
    resetData() {
      this.filters.responsiblePersonName = ''
      this.filters.teamName = ''
      this.filters.structureType = ''
      this.filters.useStatus = ''
      this.paginationData = {
        currentPage: 1,
        pageSize: 15,
        total: 0
      }
      this.getTblleList()
    },
    treeChecked(data, checked) {
      this.checkedData = data
      this.$refs.tree.setCheckedNodes([data])
    },

    //  ----------------------------------------------------分页相关------------------------------------------------------------
    handleSizeChange(val) {
      this.paginationData.currentPage = 1
      this.paginationData.pageSize = val
      this.getTblleList()
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.getTblleList()
    },
    //  ----------------------------------------------------树状结构相关---------------------------------------------------------
    /**
     * 树状图点击
     */
    handleNodeClick(data, checked) {
      this.defaultKeys = data.id
      // sessionStorage.setItem("defaultKeys", this.defaultKeys);
      this.paginationData.currentPage = 1
      // this.$store.commit('changeTableLabel', 'search')
      this.checkedData = data
      this.$refs.tree.setCheckedNodes([data])
      this.getTblleList()
    },
    //  ------------------------------------------------操作表格中数据------------------------------------------------------------
    handleSelectionChange(val) {
      this.multipleSelection = val
    }
  }
}
</script>
<style lang="scss" scoped>
// el-tree-node is-expanded is-focusable
::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: linear-gradient(to right, #d9e1f8, #fff);
}

::v-deep .el-tree-node__content {
  height: 32px;
}

.role-content {
  height: 100%;
  display: flex;

  .role-content-left {
    width: 246px;
    height: 100%;
    padding: 10px;
    background: #fff;
    border-radius: 4px;
    margin-right: 12px;

    .left_content {
      height: calc(100% - 60px);
      padding: 10px;
      // overflow: auto;
      .tree {
        margin-top: 10px;
        height: calc(100% - 40px);
        overflow: auto;
      }
    }
  }

  .role-content-right {
    flex: 1;
    height: 100%;
    min-width: 0;
    padding: 10px;
    background: #fff;
    border-radius: 4px;

    .search-from {
      padding-bottom: 12px;

      & > div {
        margin-right: 10px;
      }

      & > button {
        margin-top: 12px;
      }
    }

    .middle_tools {
      margin-bottom: 10px;
    }

    .contentTable {
      height: calc(100% - 100px);
      display: flex;
      flex-direction: column;

      .contentTable-main {
        height: calc(100% - 40px);
        flex: 1;
        overflow: auto;
      }

      .contentTable-footer {
        padding: 10px 0 0;
      }
    }
  }
}
</style>
