<template>
  <el-dialog
    v-dialogDrag
    custom-class="model-dialog"
    append-to-body
    :visible.sync="changeLocationShow"
    :close-on-click-modal="false"
    :before-close="closeDialog"
    width="35%"
    title="服务地点选择"
  >
    <div class="dialog-content">
      <el-input v-model="filterText" style="margin-bottom: 8px;" placeholder="请输入关键词"></el-input>
      <el-radio-group v-model="localRadio" size="mini" @change="locationChange">
        <el-radio-button :label="0">一级区域</el-radio-button>
        <el-radio-button :label="1">二级区域</el-radio-button>
        <el-radio-button :label="2">三级区域</el-radio-button>
        <el-radio-button :label="3">四级区域</el-radio-button>
      </el-radio-group>
      <div :title="selectService.name" class="select-servive">{{ selectService.name }}</div>
      <el-tree
        ref="tree"
        v-loading="treeLoading"
        class="tree"
        :default-expanded-keys="expendData"
        :filter-node-method="filterNode"
        node-key="id"
        :data="itemTreeData"
        :props="defaultProps"
        element-loading-background="rgba(0, 0, 0, 0.2)"
        @node-click="handleNodeClick"
      ></el-tree>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="savePeople">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { listToTree } from '@/util'
export default {
  name: 'Location',
  props: {
    changeLocationShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      filterText: '',
      itemTreeData: [],
      defaultProps: {
        label: 'ssmName',
        children: 'children'
      },
      selectRow: {},
      selectService: {},
      localRadio: '',
      expendData: [],
      expendDataList: [],
      treeLoading: false
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  mounted() {
    this.getGridTreeData()
  },
  methods: {
    getGridTreeData() {
      this.treeLoading = true
      this.$api.getGridTreeData({}).then((res) => {
        if (res.code === '200') {
          for (let i = 1; i < 5; i++) {
            this.expendDataList.push(this.getLevelData(i, res.data))
          }
          this.itemTreeData = listToTree(res.data, 'id', 'parentId')
          this.treeLoading = false
        } else {
          this.$message({
            message: res.message,
            type: 'warning'
          })
        }
      })
    },
    getLevelData(level, data) {
      const filterData = data.filter((e) => e.ssmType === level)
      if (filterData.length) {
        return Array.from(filterData, ({ id }) => id)
      } else {
        return []
      }
    },
    locationChange(val) {
      const nodes = this.$refs.tree.store._getAllNodes()
      nodes.forEach((e) => {
        if (e.expanded) {
          e.expanded = false
        }
      })
      this.expendData = this.expendDataList[val]
    },
    handleNodeClick(data, node) {
      this.selectRow = data
      if (data.ssmType === 3) {
        this.selectService = {
          name: node.parent.parent.data.ssmName + node.parent.data.ssmName + data.ssmName,
          id: node.parent.data.id + '_' + data.id
        }
      } else if (data.ssmType === 4) {
        this.selectService = {
          name: node.parent.parent.parent.data.ssmName + node.parent.parent.data.ssmName + node.parent.data.ssmName + data.ssmName,
          id: node.parent.parent.data.id + '_' + node.parent.data.id + '_' + data.id
        }
      } else if (data.ssmType === 5) {
        this.selectService = {
          name: node.parent.parent.parent.parent.data.ssmName + node.parent.parent.parent.data.ssmName + node.parent.parent.data.ssmName + node.parent.data.ssmName + data.ssmName,
          id: node.parent.parent.parent.data.id + '_' + node.parent.parent.data.id + '_' + node.parent.data.id + '_' + data.id
        }
      }
    },
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    // 取消按钮
    closeDialog() {
      this.$emit('closeDialog')
    },
    // 确认按钮
    savePeople() {
      if (this.selectRow.ssmType < 3 || JSON.stringify(this.selectService) === '{}') {
        return this.$message({
          message: '请选择区域以下节点关联！',
          type: 'warning'
        })
      }
      this.$emit('localSure', this.selectService)
      // }
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
::v-deep .model-dialog {
  .el-dialog__body {
    color: $color-text;
    height: 60vh;
  }

  .dialog-content {
    width: 100%;
    height: 100%;
    background: #fff;
    padding: 10px;

    .tree {
      height: calc(100% - 110px);
      overflow-y: scroll;
    }

    .select-servive {
      height: 30px;
      line-height: 30px;
      width: 100%;
      font-size: 15px;
      color: $color-primary;
      margin-left: 15px;
      display: inline-block;
      margin-top: 5px;
      padding: 0 12px;
    }

    .el-radio-button__orig-radio:checked + .el-radio-button__inner {
      background: #3562db;
      border: none;
      color: #fff;
    }

    .el-radio-button {
      margin-right: 8px;
      border: none;
    }

    .el-radio-button__inner {
      background: rgb(53 98 219 / 20%);
      border: none;
      box-shadow: none;
      color: $color-primary;
    }

    .el-radio-button:last-child .el-radio-button__inner,
    .el-radio-button:first-child .el-radio-button__inner {
      border-radius: 0;
    }

    .el-radio-button:focus:not(.is-focus, :active, .is-disabled) {
      box-shadow: none;
      border: none;
    }
  }
}
</style>
