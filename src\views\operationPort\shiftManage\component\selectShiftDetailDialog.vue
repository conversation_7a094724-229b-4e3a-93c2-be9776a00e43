<!--
 * @Author: hedd heding<PERSON>@sinomis.com
 * @Date: 2025-02-19 15:45:12
 * @LastEditors: hedd heding<PERSON>@sinomis.com
 * @LastEditTime: 2025-02-19 19:02:15
 * @FilePath: \ihcrs_pc\src\views\operationPort\shiftManage\component\selectShiftDetailDialog.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!--
 * @Description:
-->
<template>
  <el-dialog v-if="visible" v-dialogDrag append-to-body title="班次详情" width="80%" :visible.sync="dialogShow" custom-class="model-dialog" :before-close="closeDialog">
    <div class="sapce_content">
      <classConfig ref="classConfig" :parameter="queryParams" />
    </div>
  </el-dialog>
</template>
<script>
export default {
  name: 'selectShiftDetailDialog',
  components: {
    classConfig: () => import('../classeseManage/components/classConfig.vue')
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    rowDetail: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      queryParams: {}
    }
  },
  computed: {
    dialogShow() {
      return this.visible
    }
  },
  created() {},
  mounted() {
    this.queryParams = {
      type: 'view',
      id: this.rowDetail.id
    }
  },
  methods: {
    closeDialog() {
      this.$emit('closeDialog')
    }
  }
}
</script>
<style lang="scss" scoped>
.model-dialog {
  width: 40% !important;
  .sapce_content {
    background: #fff;
    padding: 10px;
    border-radius: 4px;
    width: 100%;
    .search-from {
      height: 40px;
    }
  }
}
::v-deep .el-checkbox__input {
  display: inline-block !important;
}
</style>
