<template>
  <PageContainer>
    <div slot="content" class="role-content" style="height: 100%">
      <div class="role-content-right">
        <div class="center">
          <div>
            <div class="text_top">自动处理报警</div>
            <div class="text_bottom">配置后，收到新的对应类型报警自动将类型设为对应值</div>
          </div>
          <el-button type="primary" @click="configuration">配置</el-button>
        </div>
      </div>
      <!-- 自动处理报警 -->
      <el-drawer v-if="isDrawer" title="自动处理报警" custom-class="content-drawer" :visible.sync="isDrawer" direction="rtl">
        <div class="drawer__body_main">
          <div class="drawer-content">
            <el-table v-loading="tableLoading" :data="tableData" height="700" style="width: 100%; overflow: auto" element-loading-background="rgba(0, 0, 0, 0.2)">
              <el-table-column prop="typeName" width="140" show-overflow-tooltip label="报警类型"></el-table-column>
              <el-table-column prop="parentTypeName" width="140" show-overflow-tooltip label="上级类型"></el-table-column>
              <el-table-column prop="radio" width="600" show-overflow-tooltip label="自动处理">
                <template slot-scope="{ row }">
                  <el-radio v-for="item in radioList" :key="item.value" v-model="row.handlerType" :disabled="isEdit" :label="item.value">{{ item.label }}</el-radio>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div class="drawer-foot">
          <el-button v-if="!isEdit" type="primary" plain @click="goBack">取消</el-button>
          <el-button v-if="!isEdit" type="primary" @click="saveOrUpdate">保存</el-button>
          <el-button v-if="isEdit" type="primary" @click="isEdit = !isEdit">编辑</el-button>
        </div>
      </el-drawer>
    </div>
  </PageContainer>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin.js'
import api from '@/api/index.js'
export default {
  mixins: [tableListMixin],
  data() {
    return {
      isDrawer: false,
      tableLoading: false,
      radioList: [
        {
          value: '1',
          label: '真实报警'
        },
        {
          value: '2',
          label: '误报'
        },
        {
          value: '3',
          label: '演练'
        },
        {
          value: '4',
          label: '调试'
        },
        {
          value: '5',
          label: '不自动处理'
        }
      ],
      tableData: [],
      isEdit: true
    }
  },
  created() {},
  methods: {
    configuration() {
      this.findAlarmHandlerConfigList()
      this.isDrawer = true
    },
    goBack() {
      this.isDrawer = false
      this.isEdit = true
    },
    findAlarmHandlerConfigList() {
      api
        .findAlarmHandlerConfigList()
        .then((res) => {
          const { code, data, msg } = res
          if (code === '200') {
            this.tableData = data
          } else {
            this.$message.error(msg)
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    saveOrUpdate() {
      const params = {
        list: this.tableData
      }
      api
        .limAlarmHandlerConfigSaveOrUpdate(params)
        .then((res) => {
          const { code, msg } = res
          if (code === '200') {
            this.$message.success(msg)
            this.isEdit = true
            this.findAlarmHandlerConfigList()
          } else {
            this.$message.error(msg)
          }
        })
        .catch((err) => {
          console.log(err)
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.role-content {
  height: 100%;
  .role-content-right {
    height: 100%;
    padding: 30px 24px;
    background: #fff;
    border-radius: 4px;
    .center {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .text_top {
        font-weight: 800;
        font-size: 18px;
      }
      .text_bottom {
        color: #aaa;
        font-size: 16px;
      }
    }
  }
}
::v-deep(.content-drawer) {
  width: 50% !important;
  text-align: initial;
  .el-drawer__header {
    height: 56px;
    margin: 0px;
    padding: 0px 26px;
    font-size: 18px;
    color: #333333;
    line-height: 18px;
  }
  .el-drawer__body {
    padding: 0;
    display: flex;
    flex-direction: column;
    .drawer__body_main {
      background: #f6f5fa;
      padding: 24px;
      flex: 1;
      overflow-x: auto;
      display: flex;
      flex-direction: column;
    }
    .drawer-content {
      flex: 1;
      background: #fff;
      padding: 16px 16px 0px 16px;
      border-radius: 4px;
      font-weight: 500;
      font-size: 14px;
      color: #333333;
      word-wrap: break-word;
      word-break: break-all;
    }
    .drawer-foot {
      padding: 12px 16px;
      text-align: right;
      background: #fff;
    }
  }
}
</style>
