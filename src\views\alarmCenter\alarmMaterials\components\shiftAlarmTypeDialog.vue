<template>
  <el-dialog v-if="shiftAlarmTypeShow" v-dialogDrag title="选择报警类型" width="40%" :visible.sync="shiftAlarmTypeShow" custom-class="model-dialog" :before-close="closeDialog">
    <div class="sapce_content">
      <div v-loading="shiftAlarmTypeLoading" class="center">
        <div>
          <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange"> 全选类型 </el-checkbox>
        </div>
        <el-tree :data="treeData" show-checkbox node-key="code" ref="tree" :default-checked-keys="defaultChecked" :props="defaultProps" @check="hanldTreeCheck"> </el-tree>
      </div>
      <div class="right">
        <div class="top">
          <span>
            <span class="label">已选:</span>
            <span class="num">{{ selectData.length ? selectData.length : 0 }}</span>
            <span class="dept">个</span>
          </span>
          <span class="clear" @click="clear"><i class="el-icon-delete"></i></span>
        </div>
        <div v-for="(item, index) in selectData" :key="index" class="item-list">
          <div style="display: flex">
            <div class="info">
              <div class="name">{{ item.name }}</div>
            </div>
          </div>
          <div class="remove" @click="remove(item, index)"><i class="el-icon-close"></i></div>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="submit">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { treeToListData } from '@/util'
import { Code } from 'quill'
export default {
  props: {
    shiftAlarmTypeShow: {
      type: Boolean,
      default: false
    },
    /*
      是否为单选
      checkbox 多选
      radio 单选
    */
    checkType: {
      type: String,
      default: 'checkbox'
    },
    defaultChecked: {
      type: Array,
      default: () => []
    },
    typeString: {
      type: String,
      default: ''
    }
  },
  // watch: {
  //    defaultChecked: {
  //     immediate: true, // 立即执行一次
  //     handler(newVal) {
  //       this.selectedItems = [...newVal]
  //     }
  //   }
  // },
  data() {
    return {
      checkAll: false,
      isIndeterminate: false,
      isAllChecked: false,
      shiftAlarmTypeLoading: false,
      selectData: [],
      treeData: [], // 树形
      treeDataList: [], // 将树形结构转换成列表
      shiftPostBakData: [],
      shiftPostSelect: [],
      searchForm: {
        keyWord: ''
      },
      defaultProps: {
        children: 'childList',
        label: 'name'
      }
    }
  },
  mounted() {
    this.getShiftPostList()
  },
  methods: {
    //  获取值班岗列表
    getShiftPostList() {
      this.shiftAlarmTypeLoading = true
      this.$api.getAlarmMaterialsTypeTree().then((res) => {
        this.shiftAlarmTypeLoading = false
        if (res.code == '200' && res.data.length > 0) {
          this.treeData = res.data
          this.treeDataList = treeToListData(res.data)
          console.log(this.treeDataList, 'this.treeDataList999999')
          if (this.defaultChecked.length >= 1) {
            this.selectData = this.treeDataList.filter((item) => this.defaultChecked.some((id) => id === item.code))
          }
        }
      })
    },
    getAllNodeIds(data, ids = []) {
      data.forEach((item) => {
        if (!item.disabled) {
          ids.push(item.code)
        }
        if (item.childList && item.childList.length) {
          this.getAllNodeIds(item.childList, ids)
        }
      })
      return ids
    },
    // 根据ID获取完整的节点数据
    getNodeDataById(data, code) {
      for (let item of data) {
        if (item.code === code) return item
        if (item.childList && item.childList.length) {
          const found = this.getNodeDataById(item.childList, code)
          if (found) return found
        }
      }
      return null
    },

    // 全选复选框变化事件
    handleCheckAllChange(val) {
      const allIds = this.getAllNodeIds(this.treeData)
      if (val) {
        this.$refs.tree.setCheckedKeys(allIds)
        this.updateSelectedItems(allIds)
      } else {
        this.$refs.tree.setCheckedKeys([])
        this.selectData = []
      }
      this.isIndeterminate = false
    },

    // 树节点选中事件
    hanldTreeCheck(data, checkedInfo) {
      const checkedKeys = checkedInfo.checkedKeys
      const allKeys = this.getAllNodeIds(this.treeData)
      // 更新全选复选框状态
      if (checkedKeys.length === 0) {
        this.checkAll = false
        this.isIndeterminate = false
      } else if (checkedKeys.length === allKeys.length) {
        this.checkAll = true
        this.isIndeterminate = false
      } else {
        this.checkAll = false
        this.isIndeterminate = true
      }
      // 更新右侧已选列表
      this.updateSelectedItems(checkedKeys)
    },
    // 更新右侧已选项目列表
    updateSelectedItems(checkedKeys) {
      this.selectData = checkedKeys.map((Code) => this.getNodeDataById(this.treeData, Code)).filter((item) => item && !item.disabled)
    },
    // 移除
    remove(item, index) {
      const index1 = this.selectData.findIndex((i) => i.code === item.code)
      if (index1 > -1) {
        this.selectData.splice(index, 1)
      }
      this.$refs.tree.setChecked(item.code, false)
    },
    // // 清空
    clear() {
      this.selectData = []
      this.$refs.tree.setCheckedKeys([])
    },
    closeDialog() {
      this.$emit('closeTypeDialog')
    },
    submit() {
      if (this.selectData.length < 1) {
        this.$message({
          message: '请选择至少一条数据',
          type: 'warning'
        })
      } else {
        this.$emit('submitAlarmTypeDialog', this.selectData)
        this.closeDialog()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.model-dialog {
  width: 40% !important;

  .sapce_content {
    margin: 0 auto;
    background: #fff;
    padding: 10px 0;
    border-radius: 4px;
    height: 400px;
    display: flex;
    width: 100%;

    .center {
      width: 300px;
      background-color: #fff;
      height: 100%;
      margin-left: 10px;
      border-radius: 5px;
      padding-left: 16px;
      overflow: scroll;

      .personCheck {
        width: 100%;
        display: flex;
        padding: 10px 0;

        img {
          vertical-align: middle;
        }
      }
    }

    .right {
      width: calc(100% - 320px);
      border-left: 1px solid #dcdfe6;
      background-color: #fff;
      height: 100%;
      margin-left: 10px;
      border-radius: 5px;
      padding-left: 16px;
      overflow: scroll;

      .top {
        display: flex;
        padding: 0 12px;
        justify-content: space-between;

        .label,
        .dept {
          font-size: 14px;
          color: #7f848c;
        }

        .num {
          font-size: 14px;
          color: #333333;
          margin-left: 10px;
        }

        .dept {
          margin-left: 10px;
        }

        .clear {
          font-size: 12px;
          color: red;
          cursor: pointer;
        }
      }

      .item-list {
        display: flex;
        cursor: pointer;
        padding: 0 12px;
        justify-content: space-between;
        margin-top: 8px;

        .remove {
          margin: auto 0;
        }
      }

      .item-list:hover {
        background: #e6effc;
      }
    }

    .info {
      margin-left: 8px;

      .name {
        font-weight: 500;
        color: #333333;
      }

      .mobile {
        font-size: 12px;
        color: #7f848c;
      }
    }
  }
}

::v-deep .el-checkbox__input {
  display: inline-block !important;
}
</style>
