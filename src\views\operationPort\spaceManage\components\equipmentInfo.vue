<template>
  <div class="information-details">
    <div class="sino-lists-wrapper">
      <div class="title-bar">
        <div class="module-title">
          <span>基础信息</span>
        </div>
      </div>
      <div class="content-warp">
        <base-info ref="baseInfo" :detailsInfo="detailsInfo"></base-info>
      </div>
      <div class="title-bar">
        <div class="module-title">
          <span>类别信息</span>
        </div>
      </div>
      <div class="content-warp">
        <category-info :detailsInfo="detailsInfo"></category-info>
      </div>
      <div class="title-bar">
        <div class="module-title">
          <span>生产和供应商信息</span>
        </div>
      </div>
      <div class="content-warp">
        <supplier-info :detailsInfo="detailsInfo"></supplier-info>
      </div>
      <div class="title-bar">
        <div class="module-title">
          <span>折旧信息</span>
        </div>
      </div>
      <div class="content-warp">
        <depreciation-info :detailsInfo="detailsInfo" :departmentShare="departmentShare"></depreciation-info>
      </div>
      <div class="title-bar">
        <div class="module-title">
          <span>财务信息</span>
        </div>
      </div>
      <div class="content-warp">
        <finance-info :detailsInfo="departmentTurn"></finance-info>
      </div>
      <div class="title-bar">
        <div class="module-title">
          <span>其他信息</span>
        </div>
      </div>
      <div class="content-warp">
        <other-info :detailsInfo="detailsInfo" :imgArr="imageArr"></other-info>
      </div>
    </div>
  </div>
</template>
<script>
import { iomsUserInfon } from '@/util/dict.js'
import BaseInfo from './BaseInfo.vue'
import CategoryInfo from './CategoryInfo.vue'
import SupplierInfo from './SupplierInfo.vue'
import DepreciationInfo from './DepreciationInfo.vue'
import financeInfo from './financeInfo.vue'
import OtherInfo from './OtherInfo.vue'
export default {
  name: 'equipmentInfo',
  components: {
    BaseInfo,
    CategoryInfo,
    SupplierInfo,
    DepreciationInfo,
    financeInfo,
    OtherInfo
  },
  props: {
    deviceId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      detailsInfo: {}, // 复制和编辑时详情信息
      departmentTurn: {},
      pageLoading: false,
      imageArr: [],
      departmentShare: []
    }
  },
  created() {
    // this.getAssetsInfoView({
    //   id: this.$route.query.id
    // })
    const id = this.deviceId
    this.getAssetsInfoView(id)
    this.getAssetsChange(id)
  },
  methods: {
    getAssetsInfoView(id) {
      this.$api.getEquimentInfo({ id: id, ...iomsUserInfon }).then((res) => {
        if (res.code === '200') {
          this.detailsInfo = res.data[0]
          if (res.data[0].pictureView) res.data[0].pictureView.split(',').forEach((res) => this.imageArr.push(res))
        }
      })
    },
    getAssetsChange(id) {
      this.$api.assetsExtendsInfoView({ assetsId: id, ...iomsUserInfon }).then((res) => {
        if (res.code === '200') {
          this.departmentTurn = res.data
          if (res.data.useDepartmentName) {
            res.data.useDepartmentName.split(',').forEach((item, index) => {
              this.departmentShare.push({
                value: res.data.officeResidualRatio.split(',')[index],
                name: item
              })
            })
            console.log('分摊', this.departmentShare)
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.information-details {
  margin: 0 auto;
  width: 100%;
  .sino-lists-wrapper {
    background: #fff;
    .title-bar {
      width: calc(100% - 12px);
      padding-left: 22px;
      height: 32px;
      line-height: 32px;
      // background-color: #263057;
      border-radius: 5px;
      position: relative;
      .module-title {
        font-size: 14px;
        color: #303133;
      }
      .module-title::before {
        content: '';
        display: inline-block;
        width: 2px;
        height: 12px;
        background: #3562db;
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
      }
    }
    .content-warp {
      width: calc(100% - 100px);
      padding: 16px 50px;
      .info {
        width: 100%;
        height: 50px;
      }
    }
  }
}
</style>
