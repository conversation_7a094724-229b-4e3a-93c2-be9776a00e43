<template>
  <PageContainer>
    <div slot="content">
      <div class="wc top-box">
        <div class="title">
          <svg-icon name="right-arrow" />
          <span>后勤服务数据</span>
        </div>
        <div class="surround">
          <div class="top-left">
            <div class="response">
              <div>
                <img src="@/assets/images/service/time1.png" />
                <div class="val-box">
                  <span class="desc">工单平均响应时间</span>
                  <span class="time">{{ countData.response }}</span>
                </div>
              </div>
              <div class="average">
                <span>同比去年平均值</span>
                <span v-if="countData.responseRate != '0.00'">
                  <span>{{ countData.responseRate }}</span>
                  <img v-if="countData.responseRate.charAt(0) == '-'" src="@/assets/images/service/down.png" />
                  <img v-else src="@/assets/images/service/up.png" />
                </span>
                <span v-else>{{ countData.responseRate }}</span>
              </div>
            </div>
            <div class="finish response">
              <div>
                <img src="@/assets/images/service/time2.png" />
                <div class="val-box">
                  <span class="desc">工单平均完工时长</span>
                  <span class="time">{{ countData.finishTime }}</span>
                </div>
              </div>
              <div class="average">
                <span>同比去年平均值</span>
                <span v-if="countData.finishTimeRate != '0.00'">
                  <span>{{ countData.finishTimeRate }}</span>
                  <img v-if="countData.finishTimeRate.charAt(0) == '-'" src="@/assets/images/service/down.png" />
                  <img v-else src="@/assets/images/service/up.png" />
                </span>
                <span v-else>{{ countData.finishTimeRate }}</span>
              </div>
            </div>
          </div>
          <div class="top-middle">
            <div class="order-num">
              <el-progress :percentage="percentage" color="#ff6461" :stroke-width="15" define-back-color="#ccced4" :show-text="false"></el-progress>
              <div class="content">
                <div class="left-cont">
                  <span>本日超时工单</span>
                  <span>
                    <span style="cursor: pointer" @click="goTimeout">{{ countOverTimes }}</span>
                    <span>单</span>
                  </span>
                </div>
                <div class="right-cont">
                  <span>本日总工单</span>
                  <span>
                    <span style="cursor: pointer" @click="goToday">{{ workOrderInfo.todayNum }}</span>
                    <span>单</span>
                  </span>
                </div>
              </div>
            </div>
            <div class="complaint">
              <span>后勤投诉</span>
              <div>
                <span>
                  <span style="cursor: pointer" @click="goComplaint">{{ workOrderInfo.complaintOrderNum }}</span>
                  <span>个</span>
                </span>
                <img src="@/assets/images/service/icon1.png" />
              </div>
            </div>
          </div>
          <div class="top-right">
            <div class="content" style="position: relative">
              <span class="cost-title">
                <span>维修耗材总费用</span>
                <i class="el-icon-arrow-right"></i>
              </span>
              <div class="cost-num">
                <span>
                  <span style="cursor: pointer" @click="goMaintenanceCost">{{ totalPrice || 0 }}</span>
                  <span>万元</span>
                </span>
                <img src="@/assets/images/service/icon2.png" />
              </div>
              <div class="btns">
                <span :class="{ 'active-btn': totalCostDateType == 'week' }" @click="changeDateType('week')">周</span>
                <span :class="{ 'active-btn': totalCostDateType == 'month' }" @click="changeDateType('month')">月</span>
                <span :class="{ 'active-btn': totalCostDateType == 'year' }" @click="changeDateType('year')">年</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="wc middle-box">
        <div class="title">
          <svg-icon name="right-arrow" />
          <span>年度工单趋势（单）</span>
        </div>
        <div id="orderTrendChart"></div>
      </div>
      <div class="bottom-box">
        <div class="left" style="position: relative">
          <div class="title">
            <svg-icon name="right-arrow" />
            <span>服务工单类型统计（件）</span>
          </div>
          <div id="orderTypeCharts"></div>
          <div class="btns">
            <span :class="{ 'active-btn': orderType == 'week' }" @click="changeOrderType('week')">周</span>
            <span :class="{ 'active-btn': orderType == 'month' }" @click="changeOrderType('month')">月</span>
            <span :class="{ 'active-btn': orderType == 'year' }" @click="changeOrderType('year')">年</span>
          </div>
        </div>
        <!-- <div class="center">
          <div class="title">
            <svg-icon name="right-arrow" />
            <span>本年度外委职工出勤率（%）</span>
          </div>
          <div id="dutyCharts"></div>
        </div> -->
        <div class="right">
          <div class="title">
            <svg-icon name="right-arrow" />
            <span>今日服务人员分析</span>
          </div>
          <el-table v-loading="tableLoading" :data="tableData" border style="width: 100%" height="85%" size="small">
            <el-table-column label="序号" type="index" width="50" align="center" :resizable="false"> </el-table-column>
            <el-table-column prop="persionName" label="姓名" align="center" show-overflow-tooltip :resizable="false"> </el-table-column>
            <el-table-column prop="deptName" label="所在班组" align="center" show-overflow-tooltip :resizable="false"> </el-table-column>
            <el-table-column prop="serviceCount" label="服务总量" align="center" show-overflow-tooltip :resizable="false">
              <template slot-scope="scope">
                <span style="cursor: pointer; color: #3a62d8" @click="goServiceDetail(scope.row)">{{ scope.row.serviceCount }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="finishCount" label="已完工" align="center" show-overflow-tooltip :resizable="false"> </el-table-column>
            <el-table-column prop="finishTotalTime" label="工时总计" align="center" show-overflow-tooltip :resizable="false"> </el-table-column>
            <el-table-column prop="finishAvgTime" label="平均完工时间（小时）" width="160" align="center" show-overflow-tooltip :resizable="false"> </el-table-column>
            <el-table-column prop="evaluate" label="满意度" align="center" show-overflow-tooltip :resizable="false"> </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import store from '@/store/index'
import * as echarts from 'echarts'
export default {
  name: 'ServiceOverview',
  data() {
    return {
      percentage: 0,
      tableLoading: false,
      tableData: [],
      totalCostDateType: 'year',
      orderType: 'year',
      countData: {
        responseRate: '',
        finishTimeRate: ''
      },
      workOrderInfo: {},
      totalPrice: 0,
      orderStatisticsData: [],
      orderTrendLastList: [],
      orderTrendList: [],
      countOverTimes: 0
    }
  },
  watch: {
    '$store.state.settings.sidebarCollapse': {
      handler(val) {
        this.$nextTick(() => {
          let echartsDom = ['orderTrendChart', 'orderTypeCharts']
          setTimeout(() => {
            echartsDom.forEach((item) => {
              echarts.init(document.getElementById(item)).resize()
            })
          }, 250)
        })
      },
      deep: true
    }
  },
  mounted() {
    // this.initOrderTrendChart()
    // this.initOrderTypeCharts()
    // this.initDutyCharts()
    this.getReckonCount()
    this.getCallCenterData()
    this.getComprehensiveWorkOrderInfo()
    this.getConsumableMaterialTotalExpenses()
    this.getServiceWorkOrderStatistics()
    this.getWorkOrderNumByWeek()
    this.getWorkersTasks()
  },
  methods: {
    goServiceDetail(row) {
      this.$router.push({
        path: '/maintenanceService/workOrderTable',
        query: {
          typeName: 'byWorker',
          showTimeType: '1',
          designatePersonCode: row.persionCode
        }
      })
    },
    getWorkersTasks() {
      this.tableLoading = true
      let params = {
        btnType: '1'
      }
      this.$api.getWorkersTasks(params).then((res) => {
        this.tableLoading = false
        if (res.code == 200) {
          this.tableData = res.data
        }
      })
    },
    initOrderTrendChart() {
      let chartData = []
      this.orderTrendList.forEach((item) => {
        chartData.push({
          name: item.weeks,
          value: item.workNum
        })
      })
      let chartData2 = []
      this.orderTrendLastList.forEach((item) => {
        chartData2.push({
          name: item.weeks,
          value: item.workNum
        })
      })
      const nameList = chartData2.map((item) => item.name)
      const valueList = chartData.map((item) => item.value)
      const valueList2 = chartData2.map((item) => item.value)
      const getchart = echarts.init(document.getElementById('orderTrendChart'))
      const option = {
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            let str = '第' + params[0].axisValue + '周<br/>'
            params.forEach((item) => {
              str += item.marker + item.seriesName + '第' + params[0].axisValue + '周:' + item.value + '单<br/>'
            })
            return str
          }
        },
        grid: {
          top: '15%',
          left: '1%',
          right: '1%',
          bottom: '6%',
          containLabel: true
        },
        legend: {
          data: [this.orderTrendLastList[0] ? this.orderTrendLastList[0].years : '', this.orderTrendList[0] ? this.orderTrendList[0].years : '']
        },
        xAxis: {
          type: 'category',
          data: nameList,
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            interval: 1
          }
        },
        yAxis: [
          {
            type: 'value',
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            }
          }
        ],
        series: [
          {
            name: this.orderTrendList[0] ? this.orderTrendList[0].years : '',
            data: valueList,
            type: 'line',
            symbol: 'circle',
            symbolSize: 6,
            itemStyle: {
              color: '#FF9435'
            },
            lineStyle: {
              color: '#FF9435'
            }
          },
          {
            name: this.orderTrendLastList[0] ? this.orderTrendLastList[0].years : '',
            data: valueList2,
            type: 'line',
            symbol: 'circle',
            symbolSize: 6,
            itemStyle: {
              color: '#4764CC'
            },
            lineStyle: {
              color: '#4764CC'
            }
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    initOrderTypeCharts() {
      let arr = []
      this.orderStatisticsData.forEach((item) => {
        let obj = {
          name: item.workTypeName,
          value: item.workOrderNum
        }
        arr.push(obj)
      })
      const nameList = Array.from(arr, (item) => item.name)
      const getchart = echarts.init(document.getElementById('orderTypeCharts'))
      const option = {
        tooltip: {
          trigger: 'item'
        },
        color: ['#3562db', '#fa403c', '#08cb83', '#ff9435', '#0ca6ed', '#ff9435'],
        legend: {
          orient: 'horizontal',
          type: 'scroll',
          top: '88%',
          bottom: 20,
          itemWidth: 15,
          itemHeight: 15,
          itemGap: 15,
          data: nameList,
          formatter: function (name) {
            var oa = option.series[0].data
            var num = oa.reduce((sum, e) => sum + e.value, 0)
            for (var i = 0; i < option.series[0].data.length; i++) {
              if (name === oa[i].name) {
                return ' ' + name + '(' + oa[i].value + '件' + ')' + '    ' + ((oa[i].value / num) * 100).toFixed(2) + '%'
              }
            }
          }
        },
        series: [
          {
            type: 'pie',
            roseType: 'radius',
            radius: ['45%', '75%'],
            center: ['50%', '45%'],
            data: arr,
            label: {
              normal: {
                show: false
              }
            }
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    initDutyCharts() {
      const data = [
        {
          name: '国天物业',
          value: 38
        },
        {
          name: '爱玛客',
          value: 28
        },
        {
          name: '新天德',
          value: 24
        },
        {
          name: '中豪机电',
          value: 9
        }
      ]
      data.sort((a, b) => {
        return a.value - b.value
      })
      const nameList = Array.from(data, ({ name }) => name)
      const getchart = echarts.init(document.getElementById('dutyCharts'))
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          top: '4%',
          left: '4%',
          right: '5%',
          bottom: '6%',
          containLabel: true
        },
        xAxis: {
          type: 'value'
        },
        yAxis: [
          {
            type: 'category',
            data: nameList,
            axisLabel: {
              formatter: function (val) {
                var strs = val.split('') // 字符串数组
                var str = ''
                // strs循环 forEach 每五个字符增加换行符
                strs.forEach((item, index) => {
                  if (index % 6 === 0 && index !== 0) {
                    str += '\n'
                  }
                  str += item
                })
                return str
              }
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            }
          }
        ],
        series: [
          {
            type: 'bar',
            barWidth: 10,
            data: data,
            label: {
              show: true,
              position: 'right'
            },
            itemStyle: {
              color: '#FF9435'
            }
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    changeDateType(val) {
      this.totalCostDateType = val
      this.getConsumableMaterialTotalExpenses()
    },
    changeOrderType(val) {
      this.orderType = val
      this.getServiceWorkOrderStatistics()
    },
    getReckonCount() {
      const userInfo = store.state.user.userInfo.user
      let params = {
        workTypeCode: '',
        workTypeName: '',
        flowcode: '',
        feedbackFlag: '',
        showTimeType: '3',
        startTime: '',
        endTime: '',
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
      this.$api.getReckonCount(params).then((res) => {
        if (res.success) {
          this.countData = res.body.data
        }
      })
    },
    getComprehensiveWorkOrderInfo() {
      const userInfo = store.state.user.userInfo.user
      let params = {
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
      this.$api.getComprehensiveWorkOrderInfo(params).then((res) => {
        if (res.code == '200') {
          this.workOrderInfo = res.data.resultMap
          if (this.workOrderInfo.todayNum == 0) {
            this.percentage = 0
          } else {
            setTimeout(() => {
              this.percentage = Math.round((this.countOverTimes / this.workOrderInfo.todayNum) * 100)
            }, 100)
          }
        }
      })
    },
    getConsumableMaterialTotalExpenses() {
      const userInfo = store.state.user.userInfo.user
      let params = {
        dateType: this.totalCostDateType,
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
      this.$api.getConsumableMaterialTotalExpenses(params).then((res) => {
        if (res.code == '200') {
          this.totalPrice = res.data.map.totalPrice
        }
      })
    },
    getServiceWorkOrderStatistics() {
      const userInfo = store.state.user.userInfo.user
      let params = {
        dateType: this.orderType,
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
      this.$api.getServiceWorkOrderStatistics(params).then((res) => {
        if (res.code == '200') {
          this.orderStatisticsData = res.data.list
          this.initOrderTypeCharts()
        }
      })
    },
    getWorkOrderNumByWeek() {
      const userInfo = store.state.user.userInfo.user
      let params = {
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
      this.$api.getWorkOrderNumByWeek(params).then((res) => {
        if (res.code == '200') {
          this.orderTrendList = res.data.map.list
          this.orderTrendLastList = res.data.map.lastYearList
          this.initOrderTrendChart()
        }
      })
    },
    goTimeout() {
      this.$router.push({
        path: '/maintenanceService/workOrderTable'
      })
    },
    goToday() {
      this.$router.push({
        path: '/maintenanceService/workOrderTable',
        query: {
          typeName: 'today'
        }
      })
    },
    goComplaint() {
      this.$router.push({
        path: '/maintenanceService/workOrderTable',
        query: {
          typeName: 'complaint'
        }
      })
    },
    goMaintenanceCost() {
      this.$router.push({
        path: '/maintenanceService/workOrderTable',
        query: {
          typeName: 'MaintenanceCost',
          totalCostDateType: this.totalCostDateType
        }
      })
    },
    getCallCenterData() {
      const userInfo = store.state.user.userInfo.user
      let params = {
        curPage: 1,
        pageSize: 100,
        selectType: 5,
        isTimeOut: 1,
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
      this.$api.getCallCenterData(params).then((res) => {
        this.countOverTimes = parseInt(res.body.countOverTimes)
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.container-content > div {
  height: 100%;
  display: flex;
  align-items: apace-between;
  flex-wrap: wrap;
}
.wc {
  width: 100%;
  background-color: #fff;
  border-radius: 4px;
  padding: 12px;
}
.top-box {
  height: 30%;
}
.middle-box {
  height: 28%;
}
.bottom-box {
  width: 100%;
  height: 39%;
  display: flex;
  justify-content: space-between;
}
.bottom-box > div {
  width: 32.8%;
  background-color: #fff;
  border-radius: 4px;
  padding: 12px;
}
.title span {
  font-size: 15px;
}
.surround {
  display: flex;
  height: 80%;
  margin-top: 14px;
  justify-content: space-between;
  padding: 0 3px;
}
.surround .top-left {
  width: 29%;
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  justify-content: space-between;
}
.surround .top-left > div {
  width: 100%;
  height: 46%;
  display: flex;
  justify-content: space-between;
  padding: 0 8px;
  box-sizing: border-box;
  align-items: center;
}
.response {
  background-color: #faf9fc;
}
.response img {
  width: 60px;
  height: 60px;
  margin-right: 8px;
}
.response > div:nth-child(1) {
  width: 60%;
  height: 73%;
  display: flex;
  align-items: center;
  // justify-content: space-between;
}
.response > div:nth-child(2) {
  width: 30%;
}
.response .desc {
  font-size: 14px;
}
.response .time {
  font-size: 26px;
  font-weight: 700;
}
.val-box {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}
.average {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 58%;
}
.average img {
  width: 20px;
  height: 20px;
}
.average > span:nth-child(1) {
  font-size: 12px;
  color: #414653;
}
.average > span:nth-child(2) {
  font-size: 16px;
  display: flex;
  align-items: center;
}
.average > span:nth-child(2) > span:nth-child(1) {
  margin-right: 8px;
}
.surround .top-middle {
  width: 38%;
  display: flex;
  justify-content: space-between;
}
.surround .top-middle > div {
  background-color: #faf9fc;
}
.surround .top-middle .order-num {
  width: 68%;
  padding: 8% 12px 5%;
  box-sizing: border-box;
}
.surround .top-middle .complaint {
  width: 30%;
}
.surround .top-middle .order-num .content {
  display: flex;
  // justify-content: space-between;
}
.surround .top-middle .order-num .content .left-cont {
  display: flex;
  flex-direction: column;
  margin-right: 35%;
}
.surround .top-middle .order-num .content .left-cont > span:nth-child(1),
.surround .top-middle .order-num .content .right-cont > span:nth-child(1) {
  font-size: 14px;
  color: #3562db;
  font-weight: 500;
  margin-bottom: 5px;
}
.surround .top-middle .order-num .content .left-cont > span:nth-child(2) > span:nth-child(1),
.surround .top-middle .order-num .content .right-cont > span:nth-child(2) > span:nth-child(1) {
  font-size: 24px;
  font-weight: 700;
  margin-right: 5px;
}
.surround .top-middle .order-num .content .left-cont > span:nth-child(2) > span:nth-child(2),
.surround .top-middle .order-num .content .right-cont > span:nth-child(2) > span:nth-child(2) {
  font-size: 14px;
  color: #ccced3;
  font-weight: 500;
}
.surround .top-middle .order-num .content .right-cont {
  display: flex;
  flex-direction: column;
}
.el-progress {
  margin-bottom: 24px;
}
.complaint {
  padding: 8% 12px 5%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}
.complaint > span {
  color: #3562db;
  font-size: 15px;
  display: inline-block;
  margin-bottom: 25%;
}
.complaint img {
  width: 40px;
  height: 38px;
}
.complaint > div {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.complaint > div > span > span:nth-child(1) {
  font-size: 28px;
  color: #121f3e;
  font-weight: 700;
  margin-right: 5px;
}
.complaint > div > span > span:nth-child(2) {
  font-size: 14px;
  color: #ccced3;
  font-weight: 500;
}
.surround .top-right {
  width: 29%;
  background: #faf9fc;
  padding: 1.8%;
}
.surround .top-right .content {
  background-color: #fff;
  width: 100%;
  height: 100%;
  border-radius: 4px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.surround .cost-title > span {
  font-size: 15px;
  color: #3562db;
}
.surround .top-right .content img {
  width: 40px;
  height: 38px;
}
.cost-num {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.cost-num > span > span:nth-child(1) {
  font-size: 28px;
  color: #121f3e;
  font-weight: 700;
  margin-right: 5px;
}
.cost-num > span > span:nth-child(2) {
  font-size: 14px;
  color: #ccced3;
  font-weight: 500;
}
#orderTrendChart,
#orderTypeCharts,
#dutyCharts {
  width: 100%;
  height: 88%;
  margin-top: 14px;
}
.el-table {
  margin-top: 14px;
}
.btns {
  position: absolute;
  width: 150px;
  height: 28px;
  right: 5%;
  top: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.btns > span {
  width: 30%;
  background-color: #f6f5fa;
  font-size: 14px;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #ededf5;
  color: #7f848c;
  cursor: pointer;
}
.active-btn {
  background-color: #3562db !important;
  color: #fff !important;
  border-color: #3562db !important;
}
.bottom-box .right {
  width: 66.6%;
}
h1.redudant-selector {
  color: blue;
}
.redudant-style {
  font-size: 16px;
  color: #000;
  background-color: white;
  border: 1px solid #ccc;
  padding: 10px;
}
.redudant-block {
  margin: 0;
  padding: 0;
  border: 1px solid #ccc;
}
.redudant-block {
  margin-top: 10px;
  padding: 5px;
  border: 1px solid #ccc;
}
.redudant-parent .redudant-child {
  color: red;
}
.redudant-parent .redudant-child {
  font-size: 14px;
}
div.redudant-selector {
  color: blue;
}
.redudant-style {
  font-size: 16px;
  color: #000;
  background-color: white;
  border: 1px solid #ccc;
  padding: 10px;
  margin: 0;
}
.redudant-block {
  margin: 0;
  padding: 0;
  border: 1px solid #ccc;
}
.redudant-block {
  margin-top: 10px;
  padding: 5px;
  border: 1px solid #ccc;
}
.redudant-link:hover {
  color: red;
}
.redudant-link:active {
  font-weight: bold;
}
@media screen and (max-width: 768px) {
  .redudant-style {
    font-size: 14px;
  }
}
@media screen and (min-width: 1024px) {
  .redudant-style {
    font-size: 18px;
  }
}
.redudant-class1 {
  color: red;
  font-size: 16px;
  font-weight: bold;
  padding: 10px;
  margin: 5px;
}
.redudant-class2 {
  background-color: yellow;
  border: 1px solid black;
  padding: 8px;
  margin: 3px;
}
.redudant-class3 {
  text-align: center;
  text-transform: uppercase;
  padding: 12px;
  margin: 6px;
}
.redudant-class4 {
  color: blue;
  font-style: italic;
  padding: 14px;
  margin: 7px;
}
.redudant-block1 {
  display: block;
  width: 100px;
  height: 100px;
  background-color: red;
  margin: 10px;
  padding: 5px;
}
.redudant-block2 {
  display: block;
  width: 150px;
  height: 150px;
  background-color: blue;
  margin: 15px;
  padding: 7px;
}
.redudant-block3 {
  display: block;
  width: 200px;
  height: 200px;
  background-color: green;
  margin: 20px;
  padding: 10px;
}
.redudant-selector1 {
  font-weight: bold;
  color: red;
}
.redudant-selector2 {
  font-style: italic;
  color: blue;
}
.redudant-selector3 {
  text-decoration: underline;
  color: green;
}
.redudant-selector4 {
  text-transform: uppercase;
  color: orange;
}
@media screen and (max-width: 480px) {
  .redudant-class1 {
    font-size: 14px;
    padding: 8px;
    margin: 4px;
  }
}
@media screen and (min-width: 768px) {
  .redudant-class2 {
    border: 2px solid black;
    padding: 10px;
    margin: 5px;
  }
}
@media screen and (min-width: 1024px) {
  .redudant-class3 {
    text-align: left;
    padding: 14px;
    margin: 7px;
  }
}
@media screen and (min-width: 1200px) {
  .redudant-class4 {
    font-size: 20px;
    padding: 16px;
    margin: 8px;
  }
}
</style>
