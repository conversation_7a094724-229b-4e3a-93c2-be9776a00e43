<template>
  <PageContainer class="housing-list">
    <template #content>
      <div class="housing-list__left">
        <div class="housing-list__left__tree">
          <SpaceTree ref="treeRef" v-model="currentSpaceId" :default-expanded-ids="expandedIds" :room-count="pagination.total"></SpaceTree>
        </div>
        <!-- <div class="housing-list__left__import">
          <el-button type="primary" icon="el-icon-plus" @click="onOperate(OperateType.IMPORT)">批量导入</el-button>
        </div> -->
      </div>
      <div class="housing-list__right">
        <div class="housing-list__statistics">
          <div class="housing-list__statistics__item" v-for="item of statisticsList" :key="item.title" @click="statisticsSearch(item)">
            <div class="housing-list__statistics__item__title">{{ item.title }}</div>
            <div class="housing-list__statistics__item__value">
              <el-statistic group-separator="," :precision="0" :value-style="{ color: item.color }" suffix="个" :value="item.value" :title="''"></el-statistic>
            </div>
          </div>
        </div>
        <el-form ref="formRef" class="housing-list__form" :model="searchForm" inline>
          <el-form-item prop="roomNo">
            <el-input v-model="searchForm.roomNo" placeholder="全部房间号" maxlength="25" clearable></el-input>
          </el-form-item>
          <el-form-item prop="type">
            <el-select v-model="searchForm.type" placeholder="全部房型" clearable>
              <el-option v-for="item of formOptions.type" :key="item.dictCode" :label="item.dictName" :value="item.dictCode"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="mode">
            <el-select v-model="searchForm.mode" placeholder="房源类型" clearable>
              <el-option v-for="item of formOptions.mode" :key="item.dictCode" :label="item.dictName" :value="item.dictCode"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="rent">
            <el-input
              v-model="searchForm.rent[0]"
              style="width: 105px"
              type="number"
              :min="0"
              :max="maxPrice"
              placeholder="出租租金"
              @change="onInputNumberChange(0, $event)"
            ></el-input>
            至
            <el-input
              v-model="searchForm.rent[1]"
              style="width: 105px"
              type="number"
              :min="0"
              :max="maxPrice"
              placeholder="出租租金"
              @change="onInputNumberChange(1, $event)"
            ></el-input>
          </el-form-item>
          <el-form-item prop="status">
            <el-select v-model="searchForm.status" placeholder="全部状态" clearable>
              <el-option v-for="item of formOptions.status" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" plain @click="onReset">重置</el-button>
            <el-button type="primary" @click="onSearch">查询</el-button>
          </el-form-item>
        </el-form>
        <div class="housing-list__table_actions">
          <el-button type="primary" icon="el-icon-plus" @click="onOperate(OperateType.ADD)">新增</el-button>
          <el-popover placement="right" trigger="hover">
            <el-button type="primary" icon="el-icon-download" style="width: 120px" @click="onOperate(OperateType.DOWNLOAD)"> 模板下载 </el-button>
            <el-button type="primary" icon="el-icon-plus" style="display: block; margin: 8px 0 0 0; width: 120px" @click="onOperate(OperateType.IMPORT)"> 批量导入 </el-button>
            <template #reference>
              <el-button type="primary" style="margin: auto 8px">导入</el-button>
            </template>
          </el-popover>
          <el-button type="danger" plain :disabled="!checkedIds.length" @click="onOperate(OperateType.BATCH_OFF)">下架 </el-button>
          <el-button type="danger" plain :disabled="!checkedIds.length" @click="onOperate(OperateType.BATCH_DELETE)">删除 </el-button>
          <el-button type="primary" :disabled="!checkedIds.length || checkedIds.length != 1 || huseStatus" @click="onOperate(OperateType.BATCH_MOVE_IN)"> 办理入住 </el-button>
          <el-button type="primary" @click="onOperate(OperateType.EXPORT)"> 导出 </el-button>
          <el-upload
            ref="uploadRef"
            class="housing-list__upload"
            :accept="fileAccept"
            :show-file-list="false"
            action=""
            :limit="1"
            :http-request="handleHttpRequest"
            :on-error="handleUploadError"
          >
          </el-upload>
        </div>
        <div class="housing-list__table">
          <el-table
            v-loading="tableLoading"
            height="100%"
            :data="tableData"
            border
            stripe
            table-layout="auto"
            class="tableAuto"
            row-key="id"
            @selection-change="(rows) => (selectionList = rows)"
            @row-dblclick="(row) => onOperate(OperateType.VIEW, row)"
          >
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column prop="houseName" show-overflow-tooltip label="房间名称"></el-table-column>
            <el-table-column prop="spaceNames" show-overflow-tooltip label="空间位置"></el-table-column>
            <el-table-column prop="houseTypeName" label="房型" width="90px"></el-table-column>
            <el-table-column prop="floorArea" label="建筑面积(㎡)" width="100px"></el-table-column>
            <el-table-column prop="hirePrice" label="出租单价(元/㎡/月)" width="140px"></el-table-column>
            <el-table-column prop="hireRent" label="出租租金(元/月)" width="120px"></el-table-column>
            <el-table-column prop="tenantName" show-overflow-tooltip label="承租人" width="90px"></el-table-column>
            <el-table-column prop="residueRentingDay" show-overflow-tooltip label="剩余租期(天)" width="100px">
              <template #default="{ row }">
                <span :style="{ color: formatHouseState(row.houseState) }">{{ row.residueRentingDay }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="houseStateName" label="状态" width="90px"></el-table-column>
            <el-table-column prop="remark" show-overflow-tooltip label="备注"></el-table-column>
            <el-table-column label="操作" width="130px">
              <template #default="{ row }">
                <el-button type="text" @click="onOperate(OperateType.VIEW, row)">查看</el-button>
                <el-button type="text" @click="onOperate(OperateType.EDIT, row)">编辑</el-button>
                <el-dropdown @command="(command) => onOperate(command, row)">
                  <el-button type="text" style="margin-left: 10px">更多</el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :disabled="row.houseState !== HouseStatusType.FREE" :command="OperateType.MOVE_IN"> 办理入住 </el-dropdown-item>
                      <el-dropdown-item style="color: #ff1919" v-if="row.houseState == '0'" :disabled="row.houseState !== HouseStatusType.FREE" :command="OperateType.OFF">
                        下架
                      </el-dropdown-item>
                      <el-dropdown-item style="color: #ff1919" v-if="row.houseState == '3'" :command="OperateType.PUT"> 上架 </el-dropdown-item>
                      <el-dropdown-item style="color: #ff1919" :disabled="row.houseState !== HouseStatusType.FREE" :command="OperateType.DELETE"> 删除 </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination
          :layout="pagination.layoutOptions"
          :current-page="pagination.current"
          :page-sizes="pagination.pageSizeOptions"
          :page-size="pagination.size"
          :total="pagination.total"
          @size-change="paginationSizeChange"
          @current-change="paginationCurrentChange"
        ></el-pagination>
      </div>
      <!--房间编辑-->
      <HousingEdit v-bind="dialogHostingEdit" :visible.sync="dialogHostingEdit.show" @success="getDataList" />
      <!-- 办理入住 -->
      <div v-if="checkInVisible" class="checkInBox">
        <handleCheckInDialog
          :dialogVisible="checkInVisible"
          :roomInfo="roomInfo"
          @closeCheckInDialog="closeCheckInDialog"
          @saveCheckInDialog="saveCheckInDialog"
        ></handleCheckInDialog>
      </div>
    </template>
  </PageContainer>
</template>
<script>
import axios from 'axios'
import tableListMixin from '@/mixins/tableListMixin'
import { HouseStatus, HouseStatusType } from '@/views/rentalHousing/housingResource/constant'
import handleCheckInDialog from './components/handleCheckInDialog.vue'
export default {
  name: 'HousingList',
  components: {
    HousingEdit: () => import('./components/HousingEdit.vue'),
    SpaceTree: () => import('./components/SpaceTree.vue'),
    handleCheckInDialog
  },
  mixins: [tableListMixin],
  data() {
    return {
      checkInVisible: false,
      currentSpaceId: '',
      // 空间树默认展开节点
      expandedIds: [],
      tableLoading: false,
      tableData: [],
      // 选中的行
      selectionList: [],
      // 搜索表单
      searchForm: {
        // 房号
        roomNo: '',
        // 房型
        type: '',
        // 性质
        mode: '',
        // 租金范围
        rent: ['', ''],
        // 出租状态
        status: ''
      },
      // 房间编辑弹窗
      dialogHostingEdit: {
        id: '',
        show: false,
        // 楼层对应的ID空间链
        spaceIds: []
      },
      // 表单搜索项
      formOptions: {
        // 房型
        type: [],
        // 状态
        status: HouseStatus,
        // 性质
        mode: []
      },
      roomInfo: {},
      statisticsList: [
        { title: '全部房源', value: 0, key: 'allNum', color: 'rgb(50, 50, 52)', status: '' },
        { title: '闲置中', value: 0, key: 'idleNum', color: 'rgb(17, 167, 68)', status: '0' },
        { title: '待入住', value: 0, key: 'awaitingNum', color: 'rgb(58, 95, 198)', status: '1' },
        { title: '使用中', value: 0, key: 'useNum', color: 'rgb(225, 132, 48)', status: '2' },
        { title: '已下架', value: 0, key: 'shelvesNum', color: 'rgb(50, 50, 52)', status: '3' },
        { title: '已超期', value: 0, key: 'overdueNum', color: 'rgb(237, 68, 70)', status: '4' }
      ]
    }
  },
  computed: {
    OperateType() {
      return {
        // 增加
        ADD: 'add',
        // 导入
        IMPORT: 'import',
        // 下载
        DOWNLOAD: 'download',
        // 批量下架
        BATCH_OFF: 'batch_off',
        // 批量删除
        BATCH_DELETE: 'batch_delete',
        // 查看
        VIEW: 'view',
        // 编辑
        EDIT: 'edit',
        // 入住
        MOVE_IN: 'move_in',
        // 下架
        OFF: 'off',
        // 上架
        PUT: 'put',
        // 删除
        DELETE: 'delete',
        // 批量入住
        BATCH_MOVE_IN: 'batch_move_in',
        EXPORT: 'export'
      }
    },
    checkedIds() {
      return this.selectionList.map((it) => it.id)
    },
    huseStatus() {
      return this.selectionList.some((item) => item.houseState != '0')
    },
    fileAccept() {
      return '.xls,.xlsx'
    },
    HouseStatusType() {
      return HouseStatusType
    },
    maxPrice() {
      return 99999.99
    }
  },
  watch: {
    currentSpaceId() {
      this.onSearch()
      this.getRoomStatics()
    }
  },
  created() {
    this.getDictData()
    const query = this.$route.query
    if (Object.keys(query).length) {
      this.currentSpaceId = query.spaceId
      this.pagination.current = Number(query.page) || 1
      this.pagination.size = Number(query.size) || this.pagination.pageSizeOptions[0]
      if (query.expandedIds) {
        this.expandedIds = query.expandedIds.split(',').filter((item) => !!item)
      }
      this.$nextTick(() => {
        this.searchForm.roomNo = query.name || ''
        this.searchForm.status = query.status || ''
        this.searchForm.rent = [query.rMin || '', query.rMax || '']
        this.searchForm.type = query.type || ''
        this.searchForm.mode = query.mode || ''
        this.$router.push({ query: null })
        this.getDataList()
        this.getRoomStatics()
      })
    } else {
      this.getDataList()
      this.getRoomStatics()
    }
  },
  methods: {
    // 统计点击查询
    statisticsSearch(item) {
      this.searchForm.status = item.status
      this.onSearch()
    },
    // 获取房源统计
    getRoomStatics() {
      let params = {
        spaceId: this.currentSpaceId,
        houseName: this.searchForm.roomNo,
        pageNum: this.pagination.current,
        pageSize: this.pagination.size,
        houseNatureCode: this.searchForm.mode,
        houseState: this.searchForm.status,
        houseTypeCode: this.searchForm.type,
        hireRentMin: this.searchForm.rent[0],
        hireRentMax: this.searchForm.rent[1]
      }
      if (params.spaceId && params.spaceId.length < 3) {
        params.spaceId = ''
      }
      this.$api.rentalHousingApi.getRoomStatics(params).then((res) => {
        if (res.code === '200') {
          for (const key in res.data) {
            let item = this.statisticsList.find((item) => item.key === key)
            if (item) {
              item.value = res.data[key] ? Number(res.data[key]) : 0
            }
          }
          this.$forceUpdate()
        }
      })
    },
    /** 格式化房间状态 颜色 */
    formatHouseState(val) {
      if (!val) return ''
      let status = ['4', '0,4', '1,4', '2,4']
      return status.includes(val) ? '#ff1919' : ''
    },
    /** 办理入住弹窗 保存 */
    saveCheckInDialog() {
      this.closeCheckInDialog()
      this.onSearch()
    },
    /** 关闭办理入住 弹窗 */
    closeCheckInDialog() {
      this.checkInVisible = false
    },
    // 获取数据字典
    getDictData() {
      const params = {
        pageNo: 1,
        pageSize: 9999
      }
      this.$api.rentalHousingApi.queryDictPage(params).then((res) => {
        if (res.code === '200') {
          res.data.records.forEach((it) => {
            // 只取启用的
            if (it.dictState !== '1') return
            if (it.dictType === 'fangxing') {
              this.formOptions.type.push(it)
            } else if (it.dictType === 'leixing') {
              this.formOptions.mode.push(it)
            }
          })
        }
      })
    },
    // 点击搜索
    onSearch() {
      // 重置页码
      this.pagination.current = 1
      this.getDataList()
    },
    // 点击重置，重置表单，然后触发搜索
    onReset() {
      this.$refs.formRef.resetFields()
      this.onSearch()
    },
    // 分页获取数据
    getDataList() {
      // 防止树节点切换太快造成请求异常。
      if (this.tableLoading) {
        this.$tools.cancelAjax()
      }
      this.tableData = []
      const params = {
        spaceId: this.currentSpaceId,
        houseName: this.searchForm.roomNo,
        pageNum: this.pagination.current,
        pageSize: this.pagination.size,
        houseNatureCode: this.searchForm.mode,
        houseState: this.searchForm.status,
        houseTypeCode: this.searchForm.type,
        hireRentMin: this.searchForm.rent[0],
        hireRentMax: this.searchForm.rent[1]
      }
      // 如果节点ID的长度小于3认为是根节点，传空查全部
      if (params.spaceId && params.spaceId.length < 3) {
        params.spaceId = ''
      }
      this.tableLoading = true
      this.$api.rentalHousingApi
        .queryHouseByPage(params)
        .then((res) => {
          if (res.code === '200') {
            this.pagination.total = res.data.total
            this.tableData = res.data.records
          } else {
            // throw res.message
            this.$message.error(res.message || '获取房源列表失败')
          }
        })
        .catch((msg) => {})
        .finally(() => (this.tableLoading = false))
    },
    // 表格相关操作总线处理
    onOperate(type, row) {
      switch (type) {
        case this.OperateType.MOVE_IN:
          this.roomInfo = row
          this.checkInVisible = true
          break
        case this.OperateType.DOWNLOAD:
          this.$message.info('开始下载中，请稍后')
          // 下载房源导入模板
          // this.$api.rentalHousingApi
          //   .downloadHouseResourceTemplate()
          //   .then(this.$tools.downloadFile.bind(this.$tools))
          //   .catch((msg) => this.$message.error(msg || '下载模板失败'))
          this.downloadTemplate()
          break
        case this.OperateType.BATCH_MOVE_IN:
          this.roomInfo = this.selectionList[0]
          this.checkInVisible = true
          break
        case this.OperateType.DELETE:
          this.$confirm('删除后房间信息和数据将无法恢复', '是否删除该房间？', { type: 'warning' }).then(() => this.doDeleteRoom([row.id]))
          break
        case this.OperateType.BATCH_DELETE:
          if (this.selectionList.some((item) => item.status)) {
            return this.$message.error('只能删除闲置状态的房间')
          }
          this.$confirm('删除后房间信息和数据将无法恢复', '是否删除所选房间？', { type: 'warning' }).then(() => this.doDeleteRoom(this.checkedIds))
          break
        case this.OperateType.OFF:
          this.$confirm('下架后员工将无法再查看对应房间', '是否下架该房间？', { type: 'warning' }).then(() => this.doRoomOff([row.id]))
          break
        case this.OperateType.PUT:
          this.doRoomPut([row.id])
          break
        case this.OperateType.BATCH_OFF:
          if (this.selectionList.some((item) => item.houseState !== HouseStatusType.FREE)) {
            return this.$message.error('只能下架闲置状态的房间')
          }
          this.$confirm('下架后员工将无法再查看对应房间', '是否下架所选房间？', { type: 'warning' }).then(() => this.doRoomOff(this.checkedIds))
          break
        case this.OperateType.VIEW:
          this.updateLocation()
          this.$router.push({
            name: 'housingDetail',
            query: { id: row.id }
          })
          break
        case this.OperateType.IMPORT:
          // 根节点ID的长度小于3
          this.$refs.uploadRef.clearFiles()
          const el = this.$el.querySelector('.housing-list__upload>.el-upload')
          el && el.click()
          break
        case this.OperateType.EXPORT:
          this.$message.info('开始下载中，请稍后')
          this.downloadExport()
          break
        default:
          this.dialogHostingEdit.spaceIds = this.$refs.treeRef.getFloorIdChain()
          this.dialogHostingEdit.id = row?.id || ''
          this.dialogHostingEdit.show = true
          break
      }
    },
    /** 模板下载 */
    downloadTemplate() {
      const userInfo = this.$store.state.user.userInfo.user
      const params = {
        unitCode: userInfo.unitCode ?? 'BJSYGJ',
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY'
      }
      axios({
        method: 'get',
        url: __PATH.VUE_RHMS_API + 'house-info-entity/downloadTemplate',
        params: params,
        responseType: 'blob',
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token,
          'operation-type': 4,
          ...params
        }
      })
        .then((res) => {
          const name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
          const blob = new Blob([res.data])
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
        })
        .catch(() => {
          this.$message.error('下载失败')
        })
    },
    /** 导出 */
    downloadExport() {
      const userInfo = this.$store.state.user.userInfo.user
      const params = {
        spaceId: this.currentSpaceId,
        houseName: this.searchForm.roomNo,
        pageNum: this.pagination.current,
        pageSize: this.pagination.size,
        houseNatureCode: this.searchForm.mode,
        houseState: this.searchForm.status,
        houseTypeCode: this.searchForm.type,
        hireRentMin: this.searchForm.rent[0],
        hireRentMax: this.searchForm.rent[1],
        unitCode: userInfo.unitCode ?? 'BJSYGJ',
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY'
      }
      if (params.spaceId && params.spaceId.length < 3) {
        params.spaceId = ''
      }
      axios({
        method: 'post',
        url: __PATH.VUE_RHMS_API + 'house-info-entity/houseExport',
        data: params,
        responseType: 'blob',
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token,
          unitCode: userInfo.unitCode ?? 'BJSYGJ',
          hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY'
        }
      })
        .then((res) => {
          const name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
          const blob = new Blob([res.data])
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
        })
        .catch(() => {
          this.$message.error('下载失败')
        })
    },
    // 删除房间
    doDeleteRoom(ids) {
      this.tableLoading = true
      this.$api.rentalHousingApi
        .deleteHouseById({ idList: ids })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('删除成功')
            this.getDataList()
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '删除失败'))
        .finally(() => (this.tableLoading = false))
    },
    // 下架房间
    doRoomOff(ids) {
      this.tableLoading = true
      this.$api.rentalHousingApi
        .updateBusProc({
          houseState: HouseStatusType.OFF,
          ids
        })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('下架成功')
            this.getDataList()
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '下架失败'))
        .finally(() => (this.tableLoading = false))
    },
    // 上架房间
    doRoomPut(ids) {
      this.tableLoading = true
      this.$api.rentalHousingApi
        .updateBusProc({
          houseState: HouseStatusType.FREE,
          ids
        })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('上架成功')
            this.getDataList()
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '上架成功'))
        .finally(() => (this.tableLoading = false))
    },
    // 文件上传代理
    handleHttpRequest(request) {
      return this.checkFile(request.file)
        .then(() => {
          this.tableLoading = true
          return this.$api.rentalHousingApi.importHouseFile({ file: request.file })
        })
        .then((res) => {
          if (res.code === '200') {
            this.$message({
              message: '导入成功',
              type: 'success'
            })
            this.onSearch()
          } else {
            throw res.message
          }
        })
        .finally(() => (this.tableLoading = false))
    },
    // 检测文件是否可以上传
    async checkFile(file) {
      if (/\s/.test(file.name)) {
        throw '文件名不能存在空格'
      }
      const extension = file.name.substring(file.name.lastIndexOf('.'))
      if (!this.fileAccept.includes(extension)) {
        throw '请选择正确的文件类型'
      }
      if (file.size > 20971520) {
        throw '文件大小不能超过20MB'
      }
    },
    // 上传失败提示
    handleUploadError(err) {
      let errMsg = '上传失败'
      if (typeof err === 'string') {
        errMsg = err
      }
      this.$message.error(errMsg)
    },
    // 更新查询条件到浏览器URL
    updateLocation() {
      const query = {
        spaceId: this.currentSpaceId,
        size: this.pagination.size,
        page: this.pagination.current,
        name: this.searchForm.roomNo,
        status: this.searchForm.status,
        type: this.searchForm.type,
        mode: this.searchForm.mode,
        rMin: this.searchForm.rent[0],
        rMax: this.searchForm.rent[1],
        // 保存当前树展开的节点，还原时用
        expandedIds: this.$refs.treeRef.expandedIds.join(',')
      }
      this.$router.push({ query })
    },
    // 输入金额发生变化
    onInputNumberChange(index, val) {
      if (!val) return
      let [v1, v2] = this.searchForm.rent
      const fixedValue = Math.min(Number(val), this.maxPrice).toFixed(2)
      if (index === 0) {
        v1 = fixedValue
      } else {
        v2 = fixedValue
      }
      // 如果最小值和最大值都有值，则进行大小排序
      if (v1.length && v2.length) {
        const _v1 = Number(v1)
        const _v2 = Number(v2)
        v1 = Math.min(_v1, _v2).toFixed(2)
        v2 = Math.max(_v1, _v2).toFixed(2)
      }
      this.searchForm.rent = [v1, v2]
    }
  }
}
</script>
<style lang="scss" scoped>
.housing-list {
  ::v-deep(.container-content) {
    display: flex;
    flex-flow: row nowrap;
    background-color: #fff;
  }
  &__left {
    width: 246px;
    height: 100%;
    padding: 16px 0 16px 16px;
    border-right: solid 1px #eee;
    &__tree {
      height: calc(100% - 48px);
      overflow: hidden;
    }
    &__import {
      margin-top: 16px;
      padding-right: 16px;
      text-align: center;
    }
  }
  &__right {
    flex: 1;
    overflow: hidden;
    padding: 16px;
    background: #fff;
    display: flex;
    flex-direction: column;
  }
  &__form {
    ::v-deep(.el-form-item) {
      margin-bottom: 0;
      .el-form-item__content {
        line-height: 1;
      }
    }
  }
  &__table_actions {
    margin: 16px 0;
  }
  &__table {
    flex: 1;
    overflow: hidden;
  }
  .el-pagination {
    margin-top: 10px;
  }
  &__upload {
    height: 0;
  }
  &__statistics {
    display: flex;
    justify-content: space-around;
    &__item {
      width: calc((100% / 6) - 10px);
      padding: 10px;
      box-sizing: border-box;
      background: rgb(250, 250, 252);
      margin-bottom: 15px;
      display: flex;
      flex-direction: column;
      cursor: pointer;
      &__title {
        margin-bottom: 15px;
      }
    }
  }
}
::v-deep .el-statistic {
  text-align: left;
  display: flex;
}
</style>
