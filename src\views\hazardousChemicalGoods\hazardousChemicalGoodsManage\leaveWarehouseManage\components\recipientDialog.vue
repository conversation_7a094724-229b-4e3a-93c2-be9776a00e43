<template>
  <div class="dialog-continer">
    <el-dialog v-dialogDrag title="领用人" width="50%" :visible.sync="recipientDialogShow" custom-class="model-dialog" :before-close="closeDialog">
      <div class="content">
        <div class="left-container">
          <div v-for="(item, index) in itemList" :key="index" class="div-item" :class="index === itemIdx ? 'active' : ''" @click="changeItem(index)">{{ item }}</div>
        </div>
        <div v-if="itemIdx === 0" class="right-container">
          <div class="lable-container">
            <el-tabs v-model="lableNavIdx" style="height: 32px" @tab-click="handleClick">
              <el-tab-pane label="常用人员" name="0"></el-tab-pane>
              <el-tab-pane label="全部人员" name="1"></el-tab-pane>
            </el-tabs>
          </div>
          <div class="search-container">
            <el-input v-model="searchStaffName" placeholder="请输入姓名" clearable style="width: 150px; margin-right: 10px"></el-input>
            <el-input v-model="searchMobile" placeholder="请输入手机号" clearable style="width: 150px; margin-right: 10px"></el-input>
            <el-button type="primary" plain @click="reset">重置</el-button>
            <el-button type="primary" @click="inquiry">查询</el-button>
          </div>
          <div :class="lableNavIdx == 0 ? 'table-container-0' : 'table-container'">
            <el-table
              v-loading="tableLoadingStatus"
              height="100%"
              style="width: 100%"
              :data="tableData"
              :border="true"
              stripe
              class="tableAuto"
              row-key="id"
              @selection-change="handleSelectionChange"
            >
              <el-table-column width="55" align="center">
                <template slot-scope="scope">
                  <el-radio v-model="radio" name="" :label="scope.row.id"></el-radio>
                </template>
              </el-table-column>
              <el-table-column prop="staffName" label="人员姓名" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="mobile" label="手机号" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="officeName" label="所属部门" show-overflow-tooltip> </el-table-column>
              <el-table-column v-if="lableNavIdx == 0" label="操作" :align="'center'" width="100">
                <template #default="{ row }">
                  <el-button type="text" style="color: #ff1919" @click="deleteData(row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div v-if="lableNavIdx == 1" class="pagination-container">
            <el-pagination
              :current-page="currentPage"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              :page-size="pageSize"
              :page-sizes="[5, 10, 15, 20]"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            ></el-pagination>
          </div>
          <div v-if="lableNavIdx == 0 && officeId != '##'" class="setting-container">
            <span @click="changeIsSelectPers">维护常用人员</span>
          </div>
        </div>
        <div v-if="itemIdx === 1" class="right-input">
          <el-input v-model="personnelName" placeholder="请输入人员姓名"></el-input>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" plain @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="submitDialog">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 人员弹窗 -->
    <selectPersDialog v-if="isSelectPers" ref="selectPersDialog" :isSelectPers="isSelectPers" @updateVisible="updateVisible" @advancedSearchFn="selectPersChange" />
  </div>
</template>
<script>
export default {
  components: {
    selectPersDialog: () => import('./setRecipientDialog.vue')
  },
  props: {
    recipientDialogShow: {
      type: Boolean,
      default: false
    },
    officeId: {
      type: String,
      default: ''
    },
    defaultSelectedUser: {
      // 默认选中数据
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      tableLoadingStatus: false,
      isSelectPers: false,
      itemList: ['基础', '其他'],
      lableNav: ['常用人员', '全部人员'],
      itemIdx: 0,
      lableNavIdx: 0,
      currentPage: 1,
      total: 0,
      pageSize: 20,
      tableData: [],
      personnelName: '',
      searchStaffName: '',
      searchMobile: '',
      radio: '',
      selectUserData: []
    }
  },
  mounted() {
    if (this.itemIdx === 0 && this.lableNavIdx === 0) this.getCommonLersonnelList()
  },
  methods: {
    changeItem(index) {
      if (this.itemIdx === index) return
      this.itemIdx = index
      // if (this.itemIdx === 0) {
      //   this.tableData = []
      //   this.getCommonLersonnelList()
      // } else {
      // }
    },
    handleClick(index) {
      this.tableData = []
      this.radio = ''
      this.reset()
    },
    handleSelectionChange() {},
    handleSizeChange(val) {
      this.pageSize = val
      this.getLersonnelList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getLersonnelList()
    },
    reset() {
      this.searchMobile = ''
      this.searchStaffName = ''
      this.inquiry()
    },
    inquiry() {
      this.currentPage = 1
      if (this.lableNavIdx == 1) {
        this.getLersonnelList()
      }
      if (this.lableNavIdx == 0) {
        this.getCommonLersonnelList()
      }
    },
    // 常用领用人删除
    deleteData(row) {
      this.$confirm('确定删除吗?', '提示', {
        cancelButtonClass: 'el-button--primary is-plain',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let params = {
          deptId: this.officeId,
          id: row.id
        }
        this.$api.delRegularRecipients(params).then((res) => {
          if (res.code == 200) {
            this.$message.success('删除成功')
            this.getCommonLersonnelList()
          }
        })
      })
    },
    // 常用人员设置弹窗 打开
    changeIsSelectPers() {
      if (this.tableData.length >= 20) {
        this.$message.warning('常用人员最多20人')
        return
      }
      this.isSelectPers = true
    },
    // 常用人员设置弹窗确认
    selectPersChange(val) {
      this.selectUserData = val
      let params = {
        deptId: this.officeId,
        usedRecipientList:
          val && val.length
            ? val.map((item) => {
                return {
                  userId: item.id,
                  userName: item.staffName,
                  userPhone: item.mobile,
                  // deptId: item.officeId,
                  deptName: item.officeName
                }
              })
            : []
      }
      this.$api.saveRegularRecipients(params).then((res) => {
        if (res.code == 200) {
          this.$message.success('保存成功')
          this.tableData = []
          this.getCommonLersonnelList()
        } else {
          this.selectUserData = []
          this.$message.error(res.message)
        }
      })
    },
    updateVisible(val) {
      if (20 - this.tableData.length < this.selectUserData.length) {
        this.$message.warning('常用人员最多20人')
        return
      }
      this.isSelectPers = false
    },
    // 数组去重
    removeSame(array) {
      let newArray = []
      for (let item of array) {
        // 使用JSON.stringfy()方法将数组和数组元素转换为JSON字符串之后再使用includes()方法进行判断
        if (JSON.stringify(newArray).includes(JSON.stringify(item))) {
          continue
        } else {
          // 不存在的添加到数组中
          newArray.push(item)
        }
      }
      return newArray
    },
    submitDialog() {
      let obj = {}
      if (!this.radio && this.itemIdx === 0) {
        return this.$message.warning('请选择人员')
      }
      if (this.itemIdx === 1) {
        if (!this.personnelName) {
          return this.$message.warning('请输入人员名称')
        }
        obj.staffName = this.personnelName
        obj.staffId = '#'
      } else {
        obj.staffName = this.tableData.find((item) => item.id === this.radio).staffName
        if (this.lableNavIdx === 0) {
          obj.staffId = this.tableData.find((item) => item.id === this.radio).userId
        } else {
          obj.staffId = this.tableData.find((item) => item.id === this.radio).id
        }
      }
      this.$emit('submitRecipientDialog', obj)
    },
    closeDialog() {
      this.$emit('closeRecipientDialog')
    },
    // 获取全部人员信息
    getLersonnelList() {
      let params = {
        current: this.currentPage,
        size: this.pageSize,
        staffName: this.searchStaffName, // 姓名
        mobile: this.searchMobile // 手机号
        // officeId: this.officeId // 领用部门ID
      }
      this.$api.staffList(params).then((res) => {
        if (res.code == 200) {
          res.data.records.forEach((item) => {
            if (item.sex == 2) {
              item.sex = '女'
            } else if (item.sex == 1) {
              item.sex = '男'
            }
            if (item.stationStatus == 0) {
              item.stationStatus = '在职'
            } else {
              item.stationStatus = '离职'
            }
          })
          this.tableData = res.data.records
          this.total = res.data.total
          this.defaultSelectedUser.length &&
            this.defaultSelectedUser.forEach((item) => {
              this.tableData.forEach((v, i) => {
                if (item == v.id) {
                  this.$refs.table.toggleRowSelection(v)
                }
              })
            })
        }
      })
    },
    // 获取常用领用人
    getCommonLersonnelList() {
      let params = {
        deptId: this.officeId,
        staffName: this.searchStaffName, // 姓名
        mobile: this.searchMobile // 手机号
      }
      this.$api.getQueryRegularRecipients(params).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.list
          if (this.tableData.length) {
            this.tableData.forEach((item) => {
              item.staffName = item.userName
              item.mobile = item.userPhone
              item.officeId = item.deptId
              // item.id = item.userId
              item.officeName = item.deptName
            })
          }
          this.total = res.data.list.length
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  display: flex;
  margin: 0 auto;
  background: #fff;
  border-radius: 4px;
  width: 100%;
  height: 100%;
  padding: 16px;
  .left-container {
    width: 100px;
    margin-right: 20px;
    .div-item {
      height: 32px;
      text-align: center;
      line-height: 32px;
      font-size: 16px;
      cursor: pointer;
      border-radius: 2px;
      color: #333;
    }
    .active {
      background: #3562db;
      color: #fff;
    }
  }
  .right-container {
    width: calc(100% - 120px);
    height: 500px;
    display: flex;
    flex-direction: column;
    .search-container {
      margin-top: 10px;
    }
    .table-container {
      height: calc(100% - 154px);
      margin: 10px 0px;
    }
    .table-container-0 {
      height: calc(100% - 75px);
      padding-top: 10px;
      box-sizing: border-box;
    }
    .setting-container {
      margin-top: 10px;
      text-align: right;
      line-height: 23px;
      font-size: 15px;
      font-weight: bolder;
      color: #3562db;
      cursor: pointer;
    }
  }
  .right-input {
    width: calc(100% - 120px);
    margin-top: 16px;
  }
}
::v-deep .el-tabs__item {
  height: 32px;
  line-height: 32px;
  font-size: 16px;
}
::v-deep .el-radio__label {
  display: none;
}
</style>
