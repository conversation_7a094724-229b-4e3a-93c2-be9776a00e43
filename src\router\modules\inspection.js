import Layout from '@/layout'
import EmptyLayout from '@/layout/empty'
export default [
  {
    path: '/comInsCalendar',
    component: Layout,
    redirect: '/comInsCalendar/index',
    name: 'comInsCalendar',
    meta: {
      title: '工作日历',
      menuAuth: '/comInsCalendar/index'
      // icon: 'sidebar-breadcrumb'
    },
    children: [
      {
        path: 'index',
        name: 'comInsCalendar',
        component: () => import('@/views/equipmentCenter/calendar.vue'),
        meta: {
          title: '工作日历',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/comInsCalendar',
          type: '3'
        }
      },
      {
        path: 'calProgressDetail',
        name: 'calProgressDetail',
        component: () => import('@/views/equipmentCenter/InspectionManagement/progressDetail.vue'),
        meta: {
          title: '计划进度详情',
          sidebar: false,
          activeMenu: '/comInsCalendar/index'
        }
      },
      {
        path: 'planProgressDetail',
        name: 'planProgressDetail',
        component: () => import('@/views/equipmentCenter/InspectionManagement/planProgressDetail.vue'),
        meta: {
          title: '任务详情',
          sidebar: false,
          activeMenu: '/comInsCalendar/index'
        }
      },
      {
        path: 'InspectionPointDetail',
        name: 'InspectionPointDetail',
        component: () => import('@/views/equipmentCenter/InspectionManagement/components/InspectionPointDetail.vue'),
        meta: {
          title: '巡检点详情',
          sidebar: false,
          activeMenu: '/comInsCalendar/index'
        }
      },
      {
        path: 'calTaskDetail',
        name: 'calTaskDetail',
        component: () => import('@/views/equipmentCenter/InspectionManagement/taskDetail.vue'),
        meta: {
          title: '任务详情',
          sidebar: false,
          activeMenu: '/comInsCalendar/index',
          type: '3'
        }
      },
      {
        path: 'taskPointDetail',
        name: 'taskPointDetail',
        component: () => import('@/views/equipmentCenter/InspectionManagement/components/InspectionPointDetail.vue'),
        meta: {
          title: '巡检点详情',
          sidebar: false,
          activeMenu: '/comInsCalendar/index'
        }
      }
    ]
  },
  {
    path: '/comInspectionManagement',
    component: Layout,
    redirect: '/comInspectionManagement/planManagement',
    name: 'comInspectionManagement',
    meta: {
      title: '巡检管理',
      menuAuth: '/comInspectionManagement'
    },
    children: [
      {
        path: 'comInstemplateManagement',
        component: EmptyLayout,
        redirect: { name: 'templateManagement' },
        meta: {
          title: '模板管理',
          menuAuth: '/comInspectionManagement/templateManagement'
        },
        children: [
          {
            path: '',
            name: 'comInstemplateManagement',
            component: () => import('@/views/equipmentCenter/InspectionManagement/templateManagement.vue'),
            meta: {
              title: '模板管理',
              sidebar: false,
              breadcrumb: false,
              type: '3'
            }
          },
          {
            path: 'comInsAddTemplate',
            name: 'comInsAddTemplate',
            component: () => import('@/views/equipmentCenter/InspectionManagement/components/addTemplate.vue'),
            meta: {
              title: '新增模板',
              sidebar: false,
              activeMenu: '/comInspectionManagement/templateManagement',
              type: '3'
            }
          },
          {
            path: 'comInsTemplateDetail',
            name: 'comInsTemplateDetail',
            component: () => import('@/views/equipmentCenter/InspectionManagement/components/templateDetail.vue'),
            meta: {
              title: '模板详情',
              sidebar: false,
              activeMenu: '/comInspectionManagement/templateManagement',
              type: '3'
            }
          }
        ]
      },
      {
        path: 'planManagement',
        component: EmptyLayout,
        redirect: { name: 'planManagement' },
        meta: {
          title: '计划管理',
          menuAuth: '/comInspectionManagement/planManagement'
        },
        children: [
          {
            path: '',
            name: 'planManagement',
            component: () => import('@/views/Inspection/InspectionManagement/planManagement.vue'),
            meta: {
              title: '计划管理',
              sidebar: false,
              breadcrumb: false,
              auth: ['planManagement:cleaning', 'planManagement:energySaving', 'planManagement:maintenance', 'planManagement:airConditioner']
            }
          },
          {
            path: 'progressDetail',
            name: 'progressDetail',
            component: () => import('@/views/equipmentCenter/InspectionManagement/progressDetail.vue'),
            meta: {
              title: '计划进度详情',
              sidebar: false,
              activeMenu: '/comInspectionManagement/planManagement'
            }
          },
          {
            path: 'planProgressDetail',
            name: 'planProgressDetail',
            component: () => import('@/views/equipmentCenter/InspectionManagement/planProgressDetail.vue'),
            meta: {
              title: '任务详情',
              sidebar: false,
              activeMenu: '/comInspectionManagement/planManagement'
            }
          },
          {
            path: 'InspectionPointDetail',
            name: 'InspectionPointDetail',
            component: () => import('@/views/equipmentCenter/InspectionManagement/components/InspectionPointDetail.vue'),
            meta: {
              title: '巡检点详情',
              sidebar: false,
              activeMenu: '/comInspectionManagement/planManagement'
            }
          },
          {
            path: 'addPlans',
            name: 'addPlans',
            component: () => import('@/views/equipmentCenter/InspectionManagement/addPlans.vue'),
            meta: {
              title: '新增计划',
              sidebar: false,
              activeMenu: '/comInspectionManagement/planManagement'
            }
          }
        ]
      },
      {
        path: 'taskManagement',
        component: EmptyLayout,
        redirect: { name: 'taskManagement' },
        meta: {
          title: '任务管理',
          menuAuth: '/comInspectionManagement/taskManagement'
        },
        children: [
          {
            path: '',
            name: 'taskManagement',
            component: () => import('@/views/Inspection/InspectionManagement/taskManagement.vue'),
            meta: {
              title: '任务管理',
              sidebar: false,
              breadcrumb: false,
              auth: ['taskManagement:cleaning', 'taskManagement:energySaving', 'taskManagement:maintenance', 'taskManagement:airConditioner']
            }
          },
          {
            path: 'taskDetail',
            name: 'taskDetail',
            component: () => import('@/views/equipmentCenter/InspectionManagement/taskDetail.vue'),
            meta: {
              title: '任务详情',
              sidebar: false,
              activeMenu: '/comInspectionManagement/taskManagement'
            }
          },
          {
            path: 'taskPointDetail',
            name: 'taskPointDetail',
            component: () => import('@/views/equipmentCenter/InspectionManagement/components/InspectionPointDetail.vue'),
            meta: {
              title: '巡检点详情',
              sidebar: false,
              activeMenu: '/comInspectionManagement/taskManagement'
            }
          },
          {
            path: 'taskPointEdit',
            name: 'taskPointEdit',
            component: () => import('@/views/equipmentCenter/InspectionManagement/planProgressDetail.vue'),
            meta: {
              title: '巡检点编辑',
              sidebar: false,
              activeMenu: '/comInspectionManagement/taskManagement'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/comInsSystemSettings',
    component: Layout,
    redirect: '/comInsSystemSettings/comInsDictionaryManagement',
    name: 'comInsSystemSettings',
    meta: {
      title: '系统设置',
      menuAuth: '/comInsSystemSettings'
      // icon: 'sidebar-breadcrumb'
    },
    children: [
      {
        path: 'comInsDictionaryManagement',
        component: EmptyLayout,
        redirect: { name: 'comInsDictionaryManagement' },
        meta: {
          title: '字典管理',
          menuAuth: '/comInsSystemSettings/comInsDictionaryManagement'
        },
        children: [
          {
            path: '',
            name: 'comInsDictionaryManagement',
            component: () => import('@/views/equipmentCenter/systemSettings/dictionaryManagement.vue'),
            meta: {
              title: '字典管理',
              sidebar: false,
              breadcrumb: false,
              type: '2'
            }
          }
        ]
      },
      {
        path: 'comInsParameter',
        component: EmptyLayout,
        redirect: { name: 'comInsParameter' },
        meta: {
          title: '参数配置',
          menuAuth: '/comInsSystemSettings/comInsParameter'
        },
        children: [
          {
            path: '',
            name: 'comInsParameter',
            component: () => import('@/views/equipmentCenter/systemSettings/parameter.vue'),
            meta: {
              title: '参数配置',
              sidebar: false,
              breadcrumb: false,
              type: '2' // 综合巡检
            }
          }
        ]
      },
      {
        path: 'regionalConfiguration',
        component: EmptyLayout,
        redirect: { name: 'regionalConfiguration' },
        meta: {
          title: '区域配置',
          menuAuth: '/systemSettings/regionalConfiguration'
        },
        children: [
          {
            path: '',
            name: 'regionalConfiguration',
            component: () => import('@/views/equipmentCenter/systemSettings/regionalConfiguration.vue'),
            meta: {
              title: '区域配置',
              sidebar: false,
              breadcrumb: false,
              type: '1'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/comInElectronicPatrol',
    component: Layout,
    redirect: '/comInElectronicPatrol/comInsPatroLine',
    name: 'comInElectronicPatrol',
    meta: {
      title: '电子巡更',
      menuAuth: '/electronicPatrol'
      // icon: 'sidebar-breadcrumb'
    },
    children: [
      {
        path: 'comInsPatroLine',
        component: EmptyLayout,
        redirect: { name: 'comInsPatroLine' },
        meta: {
          title: '巡更路线',
          menuAuth: '/electronicPatrol/patroLine'
        },
        children: [
          {
            path: '',
            name: 'comInsPatroLine',
            component: () => import('@/views/equipmentCenter/electronicPatrol/patroLine.vue'),
            meta: {
              title: '巡更路线',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      },
      {
        path: 'comInsPatrolRecord',
        component: EmptyLayout,
        redirect: { name: 'comInsPatrolRecord' },
        meta: {
          title: '巡更记录',
          menuAuth: '/electronicPatrol/patrolRecord'
        },
        children: [
          {
            path: '',
            name: 'comInsPatrolRecord',
            component: () => import('@/views/equipmentCenter/electronicPatrol/patrolRecord.vue'),
            meta: {
              title: '巡更记录',
              sidebar: false,
              breadcrumb: false
            }
          }
        ]
      }
    ]
  },
  // 视频巡逻
  {
    path: '/videoPatrol',
    component: Layout,
    redirect: '/videoPatrol/vp_planManagement',
    name: 'videoPatrol',
    meta: {
      title: '巡检管理',
      menuAuth: '/videoPatrol'
    },
    children: [
      {
        path: 'vp_templateManagement',
        component: EmptyLayout,
        redirect: { name: 'vp_templateManagement' },
        meta: {
          title: '模板管理',
          menuAuth: '/videoPatrol/vp_templateManagement'
        },
        children: [
          {
            path: '',
            name: 'vp_templateManagement',
            component: () => import('@/views/equipmentCenter/videoPatrol/templateManagement/index.vue'),
            meta: {
              title: '模板管理',
              sidebar: false,
              breadcrumb: false,
              type: '6'
            }
          },
          {
            path: 'AddVpTemplate',
            name: 'AddVpTemplate',
            component: () => import('@/views/equipmentCenter/InspectionManagement/components/addTemplate.vue'),
            meta: {
              title: '新增模板',
              sidebar: false,
              activeMenu: '/videoPatrol/vp_templateManagement',
              type: '6'
            }
          },
          {
            path: 'vp_templateDetail',
            name: 'vp_templateDetail',
            component: () => import('@/views/equipmentCenter/InspectionManagement/components/templateDetail.vue'),
            meta: {
              title: '模板详情',
              sidebar: false,
              activeMenu: '/videoPatrol/vp_templateManagement',
              type: '6'
            }
          }
        ]
      },
      {
        path: 'vp_planManagement',
        component: EmptyLayout,
        redirect: { name: 'vp_planManagement' },
        meta: {
          title: '计划管理',
          menuAuth: '/videoPatrol/vp_planManagement'
        },
        children: [
          {
            path: '',
            name: 'vp_planManagement',
            component: () => import('@/views/equipmentCenter/videoPatrol/planManagement/index.vue'),
            meta: {
              title: '计划管理',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'vp_progressDetail',
            name: 'vp_progressDetail',
            component: () => import('@/views/equipmentCenter/InspectionManagement/progressDetail.vue'),
            meta: {
              title: '计划进度详情',
              sidebar: false,
              activeMenu: '/videoPatrol/vp_planManagement'
            }
          },
          {
            path: 'planProgressDetail',
            name: 'planProgressDetail',
            component: () => import('@/views/equipmentCenter/InspectionManagement/planProgressDetail.vue'),
            meta: {
              title: '任务详情',
              sidebar: false,
              activeMenu: '/videoPatrol/vp_planManagement'
            }
          },
          {
            path: 'InspectionPointDetail',
            name: 'InspectionPointDetail',
            component: () => import('@/views/equipmentCenter/InspectionManagement/components/InspectionPointDetail.vue'),
            meta: {
              title: '巡检点详情',
              sidebar: false,
              activeMenu: '/videoPatrol/vp_planManagement'
            }
          },
          {
            path: 'addPlans',
            name: 'addPlans',
            component: () => import('@/views/equipmentCenter/videoPatrol/planManagement/addPlans.vue'),
            meta: {
              title: '新增计划',
              sidebar: false,
              activeMenu: '/videoPatrol/vp_planManagement'
            }
          }
        ]
      },
      {
        path: 'vp_taskManagement',
        component: EmptyLayout,
        redirect: { name: 'vp_taskManagement' },
        meta: {
          title: '任务管理',
          menuAuth: '/videoPatrol/vp_taskManagement'
        },
        children: [
          {
            path: '',
            name: 'vp_taskManagement',
            component: () => import('@/views/equipmentCenter/videoPatrol/taskManagement/index.vue'),
            meta: {
              title: '任务管理',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'vp_taskDetail',
            name: 'vp_taskDetail',
            component: () => import('@/views/equipmentCenter/InspectionManagement/taskDetail.vue'),
            meta: {
              title: '任务详情',
              sidebar: false,
              activeMenu: '/videoPatrol/vp_taskManagement'
            }
          },
          {
            path: 'taskPointDetail',
            name: 'taskPointDetail',
            component: () => import('@/views/equipmentCenter/InspectionManagement/components/InspectionPointDetail.vue'),
            meta: {
              title: '巡检点详情',
              sidebar: false,
              activeMenu: '/videoPatrol/vp_taskManagement'
            }
          },
          {
            path: 'vp_taskPointEdit',
            name: 'vp_taskPointEdit',
            component: () => import('@/views/equipmentCenter/InspectionManagement/planProgressDetail.vue'),
            meta: {
              title: '巡检点编辑',
              sidebar: false,
              activeMenu: '/videoPatrol/vp_taskManagement'
            }
          }
        ]
      }
    ]
  }
]
