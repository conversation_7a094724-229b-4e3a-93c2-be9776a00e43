<script>
import { LeaveType, LeaveTypeList } from '@/views/rentalHousing/housingResource/constant'
export default {
  name: 'RoomRefund',
  props: {
    visible: Boolean,
    id: String,
    startDate: String,
    endDate: String,
    tenantName: String
  },
  events: ['update:visible', 'success'],
  data: () => ({
    formModel: {
      // 退租方式
      refundType: LeaveType.END,
      // 退租日期
      refundDate: '',
      // 回执单
      receiptFileList: [],
      // 房屋照片
      roomPhotos: [],
      // 电表数
      electricityMeterNum: '',
      // 电费余额
      electricityMeterBalance: '',
      // 电表抄表图片
      electricityMeterFileList: [],
      // 水表数
      waterMeterNum: '',
      // 水费余额
      waterMeterBalance: '',
      // 水表抄表图片
      waterMeterFileList: [],
      // 电表数
      gasMeterNum: '',
      // 电费余额
      gasMeterBalance: '',
      // 电表抄表图片
      gasMeterFileList: []
    },
    rules: {
      refundType: [{ required: true, message: '请选择退租方式' }],
      refundDate: [{ required: true, message: '请选择退租日期' }]
    },
    loadingStatus: false,
    // 当前上传类型
    currentUploadProp: '',
    // timerId
    $timerId: -1
  }),
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    },
    fileAccept() {
      return '.rar,.zip,.doc,.docx,.pdf,.jpg,.jpeg,.png'
    },
    pictureAccept() {
      return '.jpg,.jpeg,.png'
    },
    disabledEndDate() {
      return this.formModel.refundType === LeaveType.END
    },
    LeaveTypeList() {
      return LeaveTypeList
    },
    maxValue() {
      return 99999.99
    },
    datePickerStart() {
      return new Date(this.startDate || '2000-01-01')
    },
    datePickerEnd() {
      return new Date(this.endDate || '3000-01-01')
    },
    // 上传组件可用的数据源key
    uploadProp() {
      return {
        // 回执单
        receipt: 'receipt',
        // 房间照片
        room: 'room',
        // 电表照片
        electricity: 'electricity',
        // 水表照片
        water: 'water',
        // 燃气表照片
        gas: 'gas'
      }
    }
  },
  watch: {
    dialogVisible(val) {
      if (!val) return
      this.formModel.refundDate = this.endDate || ''
    }
  },
  methods: {
    /** 退租方式变化事件 */
    onRefundTypeChange(val) {
      if (val === '1') {
        this.formModel.refundDate = this.endDate || ''
      }
    },
    onDialogClosed() {
      // dialog关闭时重置form表单
      this.$refs.formRef.resetFields()
      this.currentUploadProp = ''
      clearTimeout(this.$timerId)
    },
    onSubmit() {
      this.$refs.formRef
        .validate()
        .catch(() => Promise.reject())
        .then(() => {
          if (!this.id) {
            throw '缺少记录ID'
          }
          // config request data
          const params = {
            userId: this.$store.getters.userId,
            userName: this.$store.getters.userName,
            historyRecordId: this.id,
            housePicUrl: this.formatFileArr(this.formModel.roomPhotos),
            leaveTypeCode: this.formModel.refundType,
            leaveType: LeaveTypeList.find((it) => it.value === this.formModel.refundType).label,
            leaveDate: this.formModel.refundDate,
            receiptUrl: this.formatFileArr(this.formModel.receiptFileList)
          }
          // 抄表信息
          const meterInfo = {
            // 抄表类型0入住1换租2退租
            type: '2',
            electricityNum: this.formModel.electricityMeterNum,
            electricitySurplusPrice: this.formModel.electricityMeterBalance,
            electricityPicUrl: this.formatFileArr(this.formModel.electricityMeterFileList),
            gasNum: this.formModel.gasMeterNum,
            gasSurplusPrice: this.formModel.gasMeterBalance,
            gasPicUrl: this.formatFileArr(this.formModel.gasMeterFileList),
            waterNum: this.formModel.waterMeterNum,
            waterSurplusPrice: this.formModel.waterMeterBalance,
            waterPicUrl: this.formatFileArr(this.formModel.waterMeterFileList)
          }
          params.waterPowerRecordEntity = meterInfo
          return this.$api.rentalHousingApi.checkOut(params)
        })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('操作成功')
            this.$emit('success')
            this.dialogVisible = false
          } else {
            throw res.message || '操作失败'
          }
        })
        .catch((msg) => {
          msg && this.$message.error(msg)
        })
        .finally(() => {
          this.loadingStatus = false
        })
    },
    // 接口提交附件字段 格式处理
    formatFileArr(arr) {
      if (arr && arr.length) {
        let arrJson = arr.map((item) => {
          return {
            url: item.uploadPath,
            name: item.file ? item.file.name : item.name
          }
        })
        return JSON.stringify(arrJson)
      } else {
        return ''
      }
    },
    // 根据key，获取上传文件对应的数据源
    getFileList(key) {
      if (key === this.uploadProp.receipt) {
        return this.formModel.receiptFileList
      } else if (key === this.uploadProp.room) {
        return this.formModel.roomPhotos
      } else if (key === this.uploadProp.electricity) {
        return this.formModel.electricityMeterFileList
      } else if (key === this.uploadProp.water) {
        return this.formModel.waterMeterFileList
      } else if (key === this.uploadProp.gas) {
        return this.formModel.gasMeterFileList
      } else {
        return []
      }
    },
    // 文件上传代理
    async handleHttpRequest(propKey, request) {
      const fileList = this.getFileList(propKey)
      await this.checkFile(request.file, fileList)
      const params = new FormData()
      params.append('file', request.file)
      let res = await this.$api.uploadFile(params)
      if (res.code === '200') {
        this.$message({
          message: '上传成功',
          type: 'success'
        })
        const fileInfo = fileList.find((it) => it.uid === request.file.uid)
        if (fileInfo) {
          fileInfo.uploadPath = res.data.fileKey
        }
      } else {
        throw res.message
      }
    },
    // 检测文件是否可以上传
    async checkFile(file, fileList) {
      if (/\s/.test(file.name)) {
        throw '文件名不能存在空格'
      }
      if (fileList.filter((x) => x.name === file.name).length > 1) {
        throw '已存在此文件，请重新选择'
      }
      const extension = file.name.substring(file.name.lastIndexOf('.'))
      // 根据类型验证
      const accept = this.currentUploadProp !== this.uploadProp.receipt ? this.pictureAccept : this.fileAccept
      if (!accept.includes(extension)) {
        throw '请选择正确的文件类型'
      }
      if (file.size > 10485760) {
        throw '文件大小不能超过10MB'
      }
    },
    // 上传失败提示
    handleUploadError(err) {
      let errMsg = '上传失败'
      if (typeof err === 'string') {
        errMsg = err
      }
      this.$message.error(errMsg)
    },
    // 失踪同步文件列表
    handleFileChange(file) {
      if (file.status === 'ready') {
        // 给文件绑定上传组件对应的数据源key
        file.prop = this.currentUploadProp
      }
      const fileList = this.getFileList(file.prop)
      if (file.status === 'fail') {
        // 失败的文件，500ms后删除，视觉效果会好一些
        window.clearTimeout(this.$timerId)
        this.$timerId = window.setTimeout(() => {
          let index = -1
          do {
            index = fileList.findIndex((it) => it.status === 'fail')
            if (index > -1) {
              fileList.splice(index, 1)
            }
          } while (index > -1)
          this.$timerId = -1
        }, 500)
      } else {
        const index = fileList.findIndex((it) => it.uid === file.uid)
        if (index > -1) {
          fileList.splice(index, 1, file)
        } else {
          fileList.push(file)
        }
      }
    },
    // 文件移除时
    handleFileRemove(file) {
      const fileList = this.getFileList(file.prop)
      const index = fileList.findIndex((it) => it.uid === file.uid)
      if (index > -1) {
        fileList.splice(index, 1)
      }
    },
    // 金额发生变化
    onInputNumberChange(field, val) {
      if (!val) return
      const fixedValue = Math.min(Number(val), this.maxValue).toFixed(2)
      this.$set(this.formModel, field, fixedValue)
    },
    // date-picker 日期禁用方法
    disabledDate(date) {
      return date < this.datePickerStart || date > this.datePickerEnd
    }
  }
}
</script>
<template>
  <el-dialog
    v-dialogDrag
    class="component room-refund"
    title="办理退租"
    width="1080px"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    custom-class="model-dialog"
    destroy-on-close
    @close="onDialogClosed"
  >
    <el-form ref="formRef" v-loading="loadingStatus" :model="formModel" :rules="rules" label-width="95px">
      <div class="room-refund__title">
        <svg-icon name="right-arrow" />
        申请信息
      </div>
      <el-form-item label="申请人" prop="buildingName">
        <div style="padding-left: 16px">{{ tenantName }}</div>
      </el-form-item>
      <div class="room-refund__title">
        <svg-icon name="right-arrow" />
        退租信息
      </div>
      <el-row>
        <el-col :span="12">
          <el-form-item label="退租方式" prop="refundType">
            <el-select v-model="formModel.refundType" placeholder="请选择" @change="onRefundTypeChange">
              <el-option v-for="item of LeaveTypeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="退租日期" prop="refundDate">
            <el-date-picker v-model="formModel.refundDate" type="date" value-format="yyyy-MM-dd" :disabled="disabledEndDate" :picker-options="{ disabledDate }"></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="room-refund__title">
        <svg-icon name="right-arrow" />
        退租回执单
      </div>
      <el-form-item label="回执单" prop="receiptFileList">
        <el-upload
          action=""
          list-type="file-list"
          class="room-refund__upload"
          data-type="certificate"
          :file-list="formModel.certificates"
          :accept="fileAccept"
          :limit="1"
          :http-request="handleHttpRequest.bind(this, uploadProp.receipt)"
          :on-change="handleFileChange"
          :on-error="handleUploadError"
          :on-remove="handleFileRemove"
          @click.native="currentUploadProp = uploadProp.receipt"
        >
          <el-button type="primary" plain icon="el-icon-upload2">上传文件</el-button>
          <div slot="tip" class="el-upload__tip">支持扩展名：{{ fileAccept }},且大小不超过10M</div>
        </el-upload>
      </el-form-item>
      <div class="room-refund__title">
        <svg-icon name="right-arrow" />
        退租确认
      </div>
      <el-form-item label="房屋照片" prop="roomFileList">
        <el-upload
          action=""
          list-type="picture-card"
          class="room-refund__upload"
          data-type="certificate"
          :file-list="formModel.roomPhotos"
          :accept="pictureAccept"
          :limit="10"
          :http-request="handleHttpRequest.bind(this, uploadProp.room)"
          :on-change="handleFileChange"
          :on-error="handleUploadError"
          :on-remove="handleFileRemove"
          @click.native="currentUploadProp = uploadProp.room"
        >
          <i class="el-icon-plus">
            <br />
          </i>
          <div slot="tip" class="el-upload__tip">支持扩展名：{{ pictureAccept }},且大小不超过10M</div>
        </el-upload>
      </el-form-item>
      <el-row>
        <el-col :span="5">
          <el-form-item label="电表数" prop="electricityMeterNum">
            <el-input
              v-model="formModel.electricityMeterNum"
              type="number"
              :min="0"
              :max="maxValue"
              placeholder="请输入"
              @change="onInputNumberChange('electricityMeterNum', $event)"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :offset="2" :span="5">
          <el-form-item label="电表余额(元)" prop="electricityMeterBalance">
            <el-input
              v-model="formModel.electricityMeterBalance"
              type="number"
              :min="0"
              :max="maxValue"
              placeholder="请输入"
              @change="onInputNumberChange('electricityMeterBalance', $event)"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="电表照片" prop="electricityMeterFileList">
            <el-upload
              action=""
              list-type="picture-card"
              class="room-refund__upload"
              data-type="certificate"
              :file-list="formModel.electricityMeterFileList"
              :accept="pictureAccept"
              :limit="1"
              :http-request="handleHttpRequest.bind(this, uploadProp.electricity)"
              :on-change="handleFileChange"
              :on-error="handleUploadError"
              :on-remove="handleFileRemove"
              @click.native="currentUploadProp = uploadProp.electricity"
            >
              <i class="el-icon-plus">
                <br />
              </i>
              <div slot="tip" class="el-upload__tip">支持扩展名：{{ pictureAccept }},且大小不超过10M</div>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="5">
          <el-form-item label="水表数" prop="waterMeterNum">
            <el-input
              v-model="formModel.waterMeterNum"
              type="number"
              :min="0"
              :max="maxValue"
              placeholder="请输入"
              @change="onInputNumberChange('waterMeterNum', $event)"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :offset="2" :span="5">
          <el-form-item label="水表余额(元)" prop="waterMeterBalance">
            <el-input
              v-model="formModel.waterMeterBalance"
              type="number"
              :min="0"
              :max="maxValue"
              placeholder="请输入"
              @change="onInputNumberChange('waterMeterBalance', $event)"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="电表照片" prop="waterMeterFileList">
            <el-upload
              action=""
              list-type="picture-card"
              class="room-refund__upload"
              data-type="certificate"
              :file-list="formModel.waterMeterFileList"
              :accept="pictureAccept"
              :limit="1"
              :http-request="handleHttpRequest.bind(this, uploadProp.water)"
              :on-change="handleFileChange"
              :on-error="handleUploadError"
              :on-remove="handleFileRemove"
              @click.native="currentUploadProp = uploadProp.water"
            >
              <i class="el-icon-plus">
                <br />
              </i>
              <div slot="tip" class="el-upload__tip">支持扩展名：{{ pictureAccept }},且大小不超过10M</div>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="5">
          <el-form-item label="燃气表数" prop="gasMeterNum">
            <el-input v-model="formModel.gasMeterNum" type="number" :min="0" :max="maxValue" placeholder="请输入" @change="onInputNumberChange('gasMeterNum', $event)"></el-input>
          </el-form-item>
        </el-col>
        <el-col :offset="1" :span="6">
          <el-form-item label="燃气表余额(元)" prop="gasMeterBalance" label-width="140px">
            <el-input
              v-model="formModel.gasMeterBalance"
              type="number"
              :min="0"
              :max="maxValue"
              placeholder="请输入"
              @change="onInputNumberChange('gasMeterBalance', $event)"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="燃气表照片" prop="gasMeterFileList">
            <el-upload
              action=""
              list-type="picture-card"
              class="room-refund__upload"
              data-type="certificate"
              :file-list="formModel.gasMeterFileList"
              :accept="pictureAccept"
              :limit="1"
              :http-request="handleHttpRequest.bind(this, uploadProp.gas)"
              :on-change="handleFileChange"
              :on-error="handleUploadError"
              :on-remove="handleFileRemove"
              @click.native="currentUploadProp = uploadProp.gas"
            >
              <i class="el-icon-plus">
                <br />
              </i>
              <div slot="tip" class="el-upload__tip">支持扩展名：{{ pictureAccept }},且大小不超过10M</div>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button type="primary" plain @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" :loading="loadingStatus" @click="onSubmit">办理</el-button>
    </template>
  </el-dialog>
</template>
<style lang="scss" scoped>
.room-refund {
  .el-form {
    background: #fff;
    width: 100%;
    padding: 16px;
    height: 100%;
  }
  .el-input-number {
    line-height: 30px;
  }
  &__title {
    margin-bottom: 16px;
  }
}
</style>
