<template>
  <PageContainer :footer="true">
    <div slot="content" v-loading="pageLoading" class="addApp-content">
      <div class="box_top">
        <el-form ref="formInline" style="width: 70%; overflow-y: auto" :model="formInline" :rules="rules" :disabled="$route.query.type == 'detail'">
          <ContentCard title="基础信息">
            <div slot="content" style="overflow: hidden">
              <!-- <span @click="getAppInfo">123</span> -->
              <el-row :gutter="24">
                <el-col :md="12">
                  <el-form-item label="清查任务名称" prop="inventoryTaskName" label-width="120px">
                    <el-input v-model="formInline.inventoryTaskName" maxlength="50" placeholder="请输入清查任务名称" />
                  </el-form-item>
                </el-col>
                <el-col :md="12">
                  <el-form-item v-if="$route.query.type != 'add'" label="清查任务编码" prop="inventoryTaskNo" label-width="120px">
                    <el-input v-model="formInline.inventoryTaskNo" disabled maxlength="20" placeholder="请输入清查任务编码" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="24">
                <el-col :md="12">
                  <el-form-item label="责任人" prop="responsibilityPerson" label-width="120px">
                    <el-input v-model="formInline.responsibilityPerson" placeholder="请选择责任人" readonly suffix-icon="el-icon-arrow-down" @focus="selectScope('all')" />
                  </el-form-item>
                </el-col>
                <el-col :md="12">
                  <el-form-item label="责任人电话" prop="responsibilityTel" label-width="120px">
                    <el-input v-model="formInline.responsibilityTel" maxlength="11" placeholder="请输入责任人电话" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="24">
                <el-col :md="12">
                  <el-form-item label="计划开始日期" prop="taskStartTime" label-width="120px">
                    <el-date-picker v-model="formInline.taskStartTime" style="width: 100%" type="date" value-format="yyyy-MM-dd" placeholder="选择计划开始日期"> </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :md="12">
                  <el-form-item label="计划结束日期" prop="taskEndTime" label-width="120px">
                    <el-date-picker v-model="formInline.taskEndTime" style="width: 100%" type="date" value-format="yyyy-MM-dd" placeholder="选择计划结束日期"> </el-date-picker>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="24">
                <el-col :md="12">
                  <el-form-item label="院区" prop="hospitalId" label-width="120px">
                    <el-cascader
                      ref="hospitalId"
                      v-model="formInline.hospitalId"
                      style="width: 100%"
                      :props="riskPropsType"
                      :options="treeData"
                      placeholder="请选择院区"
                      class="cascaderWid"
                      :show-all-levels="false"
                      filterable
                      @change="hangdleChange"
                    >
                    </el-cascader>
                  </el-form-item>
                </el-col>
                <el-col :md="12">
                  <el-form-item label="清查类型" prop="inventoryType" label-width="120px">
                    <el-radio-group v-model="formInline.inventoryType">
                      <el-radio :label="'1'">所有空间</el-radio>
                      <el-radio :label="'2'">按科室清查</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="24">
                <el-col :md="12">
                  <el-form-item label="备注" prop="remark" label-width="120px">
                    <el-input v-model="formInline.remark" placeholder="请输入备注" type="textarea" maxlength="200" :autosize="{ minRows: 2 }" show-word-limit></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </ContentCard>
        </el-form>
      </div>
      <div class="box_top" style="height: 50%; width: 100%">
        <ContentCard v-if="formInline.inventoryType == 2" title="空间信息" style="width: 70%">
          <div slot="content" style="overflow: hidden; height: 100%">
            <div class="search-from">
              <el-input v-model="searchFrom.nameOrCode" placeholder="科室编码/科室名称" clearable style="width: 200px"></el-input>
              <div style="display: inline-block">
                <el-button type="primary" plain @click="resetForm">重置</el-button>
                <el-button type="primary" @click="searchForm">查询</el-button>
              </div>
            </div>
            <div class="table_two">
              <div style="height: 100%; width: 65%">
                <TablePage
                  ref="table"
                  v-loading="tableLoading"
                  stripe
                  :showPage="true"
                  :tableColumn="tableColumn"
                  :data="tableData"
                  height="calc(100% - 55px)"
                  :pageData="pageData"
                  :pageProps="pageProps"
                  @pagination="paginationChange"
                  @row-dblclick="control('detail', $event)"
                />
              </div>
              <div class="exchange_btn">
                <el-button class="btn" type="primary" icon="el-icon-arrow-left" style="margin-bottom: 20px" @click="choiceLeft"></el-button>
                <el-button class="btn" type="primary" icon="el-icon-arrow-right" @click="choiceRight"></el-button>
              </div>
              <div style="height: 100%; width: 30%">
                <TablePage ref="tableA" stripe :showPage="false" :tableColumn="tableColumnA" :data="departInfos" height="calc(100% - 55px)" />
              </div>
            </div>
          </div>
        </ContentCard>
      </div>
      <space-dialog ref="spaceDialog" />
      <select-user-dialog v-if="isSelectScope" :visible.sync="isSelectScope" @selectFinish="selectFinish" />
    </div>
    <div slot="footer">
      <el-button
        type="primary"
        plain
        @click="
          () => {
            $router.go(-1)
          }
        "
        >关闭</el-button
      >
      <el-button type="primary" :disabled="$route.query.type == 'detail'" @click="submitForm('formInline')">保存</el-button>
    </div>
  </PageContainer>
</template>
<script lang="jsx">
import selectUserDialog from './components/selectUserDialog.vue'
import spaceDialog from './components/spaceDialog.vue'
export default {
  name: 'addApp',
  components: {
    selectUserDialog,
    spaceDialog
  },
  // 路由前置守卫动态修改meta中的title
  beforeRouteEnter(to, from, next) {
    if (to.meta.title) {
      const typeList = {
        add: '新增清查',
        edit: '编辑清查',
        detail: '清查详情'
      }
      to.meta.title = typeList[to.query.type] ?? '清查详情'
    }
    next()
  },
  async beforeRouteLeave(to, from, next) {
    if (!['spaceInventory'].includes(to.name)) {
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      responsiblePersonIndex: 'all', // all 为点击上方责任人   下表为列表的责任人
      isSelectScope: false, // 责任人选择
      pageLoading: false,
      formInline: {
        inventoryTaskName: null, // 清查任务名称
        inventoryTaskNo: null, // 清查任务编码
        responsibilityPerson: null, // 责任人
        responsibilityPersonId: null, // 责任人id
        responsibilityTel: null, // 责任人电话
        taskStartTime: null, // 租赁开始日期
        taskEndTime: null, // 租赁结束日期
        inventoryType: '2', // 盘点类型,1-所有空间，2-按科室清查
        remark: null, // 备注
        hospitalId: [] // 院区
      },
      riskPropsType: {
        children: 'children',
        label: 'deptName',
        value: 'id',
        checkStrictly: true,
        multiple: false
      },
      treeData: [], // 所在区域列表
      rules: {
        inventoryTaskName: [{ required: true, message: '请输入清查任务名称', trigger: ['blur', 'change'] }],
        responsibilityPerson: [{ required: true, message: '请选择责任人', trigger: ['blur', 'change'] }],
        taskStartTime: [{ required: true, message: '请选择计划开始日期', trigger: ['blur', 'change'] }],
        taskEndTime: [{ required: true, message: '请选择计划结束日期', trigger: ['blur', 'change'] }],
        hospitalId: [{ type: 'array', required: true, message: '请选择院区', trigger: ['blur', 'change'] }]
      },
      tableColumnA: [
        {
          type: 'selection',
          width: 55
        },
        // {
        //   width: 100,
        //   prop: 'operation',
        //   label: '操作',
        //   render: (h, row) => {
        //     return (
        //       <div class="operationBtn">
        //         <span class="operationBtn-span" style="color: #fa403c" onClick={() => this.delDepartment(row.index)}>删除</span>
        //       </div>
        //     )
        //   }
        // },
        {
          prop: 'departName',
          align: 'center',
          label: '科室名称'
        }
      ],
      departInfos: [],
      tableLoading: false,
      tableColumn: [],
      tableData: [],
      pageData: {
        current: 1,
        size: 15,
        total: 0
      },
      pageProps: {
        page: 'current',
        pageSize: 'size',
        total: 'total'
      },
      searchFrom: {
        nameOrCode: ''
      },
      departList: []
    }
  },
  activated() {
    this.init()
  },
  created() {
    if (!this.$store.state.keepAlive.list.includes('spaceInventory')) {
      this.init()
    }
  },
  mounted() {
    // this.getSelectDeptByPage()
  },
  methods: {
    choiceLeft() {
      this.departInfos = this.departInfos.filter((ele) => !this.$refs.tableA.selection.some((item) => item.departCode == ele.departCode))
      if (!this.departInfos.length) {
        this.setDisable(this.treeData, 'all')
      }
    },
    choiceRight() {
      // 添加科室--如果是第一个被添加的科室则获取院区层级  是院区只能选择同级的科室   使用pid进行过滤
      if (!this.departInfos.length && this.$refs.table.selection.length) {
        this.courtyardRestrictions()
      }
      this.departInfos = [
        ...this.$refs.table.selection.map((row) => {
          return {
            departName: row.deptName,
            departCode: row.id
          }
        }),
        ...this.departInfos
      ]
      this.$refs.table.clearSelection()
    },
    openDetailDialog(row) {
      this.$refs.spaceDialog.getEchartData(row)
    },
    delDepartment(index) {
      this.departInfos.splice(index, 1)
      // 删除科室--如果是删除到最后一个科室则将院区只能选择同级的限制放开
      if (!this.departInfos.length) {
        this.setDisable(this.treeData, 'all')
      }
    },
    isDepartmentRepeat(row) {
      //
      let obj = this.departInfos.find((ele) => ele.departCode == row.id)
      if (obj) {
        return 'color: #999999 !important;pointer-events: none;'
      } else {
        return 'color: #00BFBF !important'
      }
    },
    courtyardRestrictions() {
      let ssmType = this.formInline.hospitalId[this.formInline.hospitalId.length - 1]
      let obj = this.departList.find((ele) => ele.id == ssmType)
      this.setDisable(this.treeData, obj.pid)
    },
    addDepartment(row) {
      // 添加科室--如果是第一个被添加的科室则获取院区层级  是院区只能选择同级的科室   使用pid进行过滤
      if (!this.departInfos.length) {
        this.courtyardRestrictions()
      }
      this.departInfos.unshift({
        departName: row.deptName,
        departCode: row.id
        // 责任人
        // responsibilityPerson: row.principalName,
        // responsibilityPersonId: row.principalId
      })
    },
    getSelectDeptByPage() {
      const node = this.$refs.hospitalId.getCheckedNodes()[0]
      let data = {
        ...this.pageData,
        ...this.searchFrom
      }
      const dataId = this.formInline.hospitalId[this.formInline.hospitalId.length - 1]
      // 有下级获取下级数据 无下级获取当前层级的数据
      if (node.children && node.children.length) {
        data.pid = dataId
      } else {
        data.id = dataId
      }
      this.$api.selectDeptByPage(data).then((res) => {
        this.tableData = res.data.records
        this.pageData.total = res.data.total
      })
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getSelectDeptByPage()
    },
    resetForm() {
      this.searchFrom.nameOrCode = ''
      this.pageData.current = 1
      this.getSelectDeptByPage()
    },
    searchForm() {
      this.pageData.current = 1
      this.getSelectDeptByPage()
    },
    // 只允许选择同级
    setDisable(data, pid) {
      data.forEach((v) => {
        v.disabled = pid != v.pid && pid != 'all'
        if (v.children && v.children.length) {
          this.setDisable(v.children, pid) // 子级循环时把这一层数据的pid传入
        }
      })
    },
    // 所在区域选择
    hangdleChange(type, node) {
      this.resetForm()
    },
    selectScope(type) {
      this.responsiblePersonIndex = type
      this.isSelectScope = true
    },
    selectFinish(row) {
      if (this.responsiblePersonIndex == 'all') {
        this.formInline.responsibilityPerson = row.staffName
        this.formInline.responsibilityPersonId = row.id
        this.formInline.responsibilityTel = row.mobile
      } else {
        this.tableData[this.responsiblePersonIndex].principalName = row.staffName
        this.tableData[this.responsiblePersonIndex].principalId = row.id
        // this.tableData[this.responsiblePersonIndex].principalPhone = row.mobile
      }
    },
    beforeSubmit() {
      return new Promise((resolve, reject) => {
        if (this.formInline.inventoryType == 1) {
          // 所有空间
          // 拿到所有下级科室
          let depId = this.formInline.hospitalId[this.formInline.hospitalId.length - 1]
          let departInfos = this.departList
            .filter((ele) => depId == ele.pid)
            .map((item) => {
              return {
                departName: item.deptName,
                departCode: item.id
              }
            })
          // 如果没有下级科室则拿到当前科室
          if (!departInfos.length) {
            departInfos = this.departList
              .filter((ele) => depId == ele.id)
              .map((item) => {
                return {
                  departName: item.deptName,
                  departCode: item.id
                }
              })
          }
          resolve(departInfos)
        } else {
          // 按科室清查
          // 判断是否选择了科室
          if (!this.departInfos.length) {
            return this.$message.error('至少选择一个科室')
          }
          resolve(this.departInfos)
        }
      })
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.beforeSubmit().then((departInfos) => {
            let formInline = {
              ...this.formInline,
              hospitalId: this.formInline.hospitalId.toString()
            }
            let data = {
              departInfos,
              tinventory: formInline
            }
            if (this.formInline.id) {
              this.$api.updateInventoryById(data, { 'operation-type': 2, 'operation-id': this.formInline.id, 'operation-name': this.formInline.inventoryTaskName }).then((res) => {
                if (res.code == 200) {
                  this.$message({ message: '修改成功', type: 'success' })
                  this.$router.go(-1)
                } else {
                  this.$message({ message: res.msg, type: 'error' })
                }
              })
            } else {
              this.$api.addInventoryTask(data, { 'operation-type': 1 }).then((res) => {
                if (res.code == 200) {
                  this.$message({ message: '提交成功', type: 'success' })
                  this.$router.go(-1)
                } else {
                  this.$message({ message: res.msg, type: 'error' })
                }
              })
            }
          })
        }
      })
    },
    // 获取应用详情
    getAppInfo() {
      this.pageLoading = true
      this.$api
        .queryOneInventoryTaskById({ id: this.$route.query.id })
        .then((res) => {
          this.pageLoading = false
          if (res.code == 200) {
            res.data.tinventory.hospitalId = res.data.tinventory.hospitalId.split(',')
            this.formInline = res.data.tinventory
            this.departInfos = res.data.departInfos.map((ele) => {
              return {
                departCode: ele.departCode,
                departName: ele.departName
              }
            })
            this.$nextTick(() => {
              this.resetForm()
            })
          }
        })
        .catch(() => {
          this.pageLoading = false
        })
    },
    init() {
      Object.assign(this.$data, this.$options.data())
      this.tableColumn = [
        {
          type: 'selection',
          width: 55,
          selectable: (row, index) => {
            return !this.departInfos.some((item) => item.departCode == row.id)
          }
        },
        {
          prop: '',
          label: '序号',
          width: 80,
          formatter: (scope) => {
            return (this.pageData.current - 1) * this.pageData.size + scope.$index + 1
          }
        },
        {
          prop: 'deptCode',
          label: '科室编码'
        },
        {
          prop: 'deptName',
          label: '科室名称'
        },
        {
          prop: 'spaceNum',
          label: '空间总数'
        },
        {
          width: 150,
          prop: 'operation',
          label: '操作',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span class="operationBtn-span" style="color: #3562DB !important" onClick={() => this.openDetailDialog(row.row)}>
                  预览
                </span>
              </div>
            )
          }
        }
      ]
      this.$nextTick(() => {
        this.$refs.formInline.resetFields()
      })
      // 所在区域
      this.$api.getDeptList().then(async (res) => {
        if (res.code == '200') {
          this.departList = res.data
          this.treeData = await this.$tools.transData(res.data, 'id', 'pid', 'children', false, true)
          if (this.$route.query.type != 'add') {
            this.getAppInfo()
            this.$nextTick(() => {
              this.courtyardRestrictions()
            })
          }
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.box_top {
  display: flex;
  justify-content: center;
}
.exchange_btn {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0 20px;
  .btn {
    width: 32px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0;
  }
}
.table_two {
  height: calc(100% - 40px);
  display: flex;
}
.search-from {
  margin-bottom: 20px;
  & > div {
    margin-right: 10px;
  }
}
.addApp-content {
  background: #fff;
  border-radius: 4px;
  height: 100%;
  padding: 10px;
  overflow-y: auto;
  ::v-deep .el-upload {
    width: 130px;
    height: 130px;
  }
  ::v-deep .el-upload-list {
    .el-upload-list__item {
      width: 130px;
      height: 130px;
    }
  }
  .hide {
    ::v-deep .el-upload--picture-card {
      display: none;
    }
  }
}
</style>
