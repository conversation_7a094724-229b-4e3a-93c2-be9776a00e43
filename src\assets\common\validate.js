// 验证用户名

export function validateUserName(val) {
  if (!<PERSON><PERSON><PERSON>(val)) {
    return true
  } else {
    const reg = /[a-zA-Z0-9_]{6,20}$/
    return reg.test(val)
  }
}

// 验证密码

export function validateUserPwd(val) {
  if (!<PERSON>olean(val)) {
    return true
  } else {
    const reg = /[a-zA-Z0-9!@#$%^&*]{6,16}$/
    return reg.test(val)
  }
}

// 验证手机号

export function validateMobile(val) {
  if (!Boolean(val)) {
    return true
  } else {
    const reg = /(^$)|^[1][3,4,5,6,7,8,9][0-9]{9}$/
    return reg.test(val)
  }
}

// 验证固话号码

export function validatePhone(val) {
  if (!Boolean(val)) {
    return true
  } else {
    const reg = /^((0\d{2,3}-\d{7,8})|(1[3584]\d{9}))$/
    return reg.test(val)
  }
}

// 验证数字

export function validateNum(val) {
  if (!<PERSON><PERSON>an(val)) {
    return true
  } else {
    const reg = /^[0-9]*$/
    return reg.test(val)
  }
}

// 验证真实姓名

export function validateName(val) {
  if (!Boolean(val)) {
    return true
  } else {
    const reg = /^[\u4e00-\u9fa5·]{2,20}$/
    return reg.test(val)
  }
}

// 验证身份证号

export function validateIdCard(val) {
  if (!Boolean(val)) {
    return true
  } else {
    const reg = /^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/
    return reg.test(val)
  }
}

// 验证开户行 / 开户行账户名

export function validateDepositBank(val) {
  if (!Boolean(val)) {
    return true
  } else {
    const reg = /^[\u4e00-\u9fa5]+$/
    return reg.test(val)
  }
}

// 验证银行卡号

export function validateCardnumbe(val) {
  if (!Boolean(val)) {
    return true
  } else {
    const reg = /[0-9]{16,20}$/
    return reg.test(val)
  }
}

// 验证企业名称

export function validateCompanyName(val) {
  if (!Boolean(val)) {
    return true
  } else {
    const reg = /^[\u4e00-\u9fa5·]{2,50}$/
    return reg.test(val)
  }
}

// 验证金额

export function validateMoney(val) {
  if (!Boolean(val)) {
    return true
  } else {
    const reg = /^[-+]?\d{0,16}[.]?\d{0,4}$/
    return reg.test(val)
  }
}

// 验证数字（2位小数）
export function validateNumFor2(val) {
  if (!Boolean(val)) {
    return true
  } else {
    const reg = /^\d{0,8}[.]?\d{0,2}$/
    return reg.test(val)
  }
}

// 验证数字（8位小数）
export function validateNumFor8(val) {
  if (!Boolean(val)) {
    return true
  } else {
    const reg = /^\d{0,8}[.]?\d{0,8}$/
    return reg.test(val)
  }
}
