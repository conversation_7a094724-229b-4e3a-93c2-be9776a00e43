<template>
  <PageContainer v-loading="contentLoading">
    <div slot="header" class="table-content">
      <div class="topFilter">
        <div class="backBar">
          <span style="cursor: pointer" @click="$router.go(-1)">
            <i class="el-icon-arrow-left"></i>
            会议计划详情
          </span>
        </div>
      </div>
      <div class="baseInfo">
        <h1>基础信息</h1>
        <div class="contenter">
          <div class="itemInfo">
            <div class="itemInfoName">
              <div class="itemLable">会议名称：</div>
              <div class="itemInfoIndex">{{ conferenceList.name }}</div>
            </div>
            <div class="itemInfoName">
              <div class="itemLable">所属部门：</div>
              <div class="itemInfoIndex">{{ conferenceList.deptName }}</div>
            </div>
            <div class="itemInfoName">
              <div class="itemLable">培训频率：</div>
              <div class="itemInfoIndex">
                {{ conferenceList.frequency == 0 ? '全年' : conferenceList.frequency == 1 ? '单次' : conferenceList.frequency == 2 ? '每周' : conferenceList.frequency == 3 ? '每月' :conferenceList.frequency == 4 ? '每季度' : '每半年' }}
              </div>
            </div>
            <div class="itemInfoName">
              <div class="itemLable">计划生效期限:</div>
              <div class="itemIndex" v-if="conferenceList.startTime">{{ conferenceList.startTime }} -
                {{ conferenceList.endTime }}</div>
              <div class="itemIndex" v-else>{{ conferenceList.startDay }}</div>
            </div>
          </div>
          <div class="itemInfo">
            <div class="itemInfoName">
              <div class="itemLable">会议时间：</div>
              <div class="itemInfoIndex">{{ conferenceList.conferenceStartTime }} -
                {{ conferenceList.conferenceEndTime }}</div>
            </div>
            <div class="itemInfoName">
              <div class="itemLable" style="width: 100px;">会议方式：</div>
              <div class="itemInfoIndex">{{ conferenceList.type == 0 ? '线上会议' : '线下会议' }}</div>
            </div>
            <div class="itemInfoName">
              <div class="itemLable">培训地址：</div>
              <div class="itemInfoIndex">{{ conferenceList.address }}</div>
            </div>
          </div>
          <div class="itemInfo">
            <div class="itemLable">会议负责人：</div>
            <div>{{ conferenceList.principalName }}</div>
          </div>
        </div>
      </div>
    </div>
    <div slot="content" class="courseContent">
      <div class="top">
        <h1>培训课件</h1>
      </div>
      <div class="table_content">
        <div v-for="(k, index) in trainList" :key="k.id" class="item">
          <div>
            <img src="../../../../assets/images/doc.png" alt="" />
            <span>{{ index + 1 }}.{{ k.originalFilename }}</span>
          </div>
          <div class="operate">
            <span @click="ckeckFile(k)">查看</span>
            <span @click="download(k)">下载</span>
          </div>
        </div>
      </div>
      <div class="contentTable-footer">
        <el-pagination style="margin-top: 3px" :current-page="paginationData.pageNo"
          layout="total, sizes, prev, pager, next, jumper" :total="paginationData.total"
          :page-size="paginationData.pageSize" :page-sizes="[15, 30, 50, 100]" @size-change="handleSizeChange"
          @current-change="handleCurrentChange"></el-pagination>
      </div>
    </div>
  </PageContainer>
</template>
<script>
  import moment from "moment";
  import axios from 'axios'
  export default {
    data() {
      return {
        activeName: 'second',
        contentLoading: false,
        routeInfo: "",
        moment,
        id: "",
        examine: '', // 查看文件状态
        conferenceList: {}, // 会议计划详情信息
        trainList: [{
            originalFilename: '这是个课件',
          },
          {
            originalFilename: '这是个课件',
          },
          {
            originalFilename: '这是个课件',
          },
          {
            originalFilename: '这是个课件',
          }
        ], // 模板课件
        coursewareList: [{
          id: '0',
          name: '课件名称'
        }],
        drawerDialog: false,
        paginationData: {
          pageNo: 1,
          pageSize: 15,
          total: 0
        },
        idsy: [],
        activeIndex: 0,
      };
    },
    created() {
      this.routeInfo = JSON.parse(sessionStorage.getItem("routeInfo"));
      this.id = this.$route.query.id
      if (this.id) {
        this.getDetails();
      }
    },
    methods: {
      // 获取详情
      getDetails() {
        this.contentLoading = true;
        this.$api.confereceDetail({
          id: this.id
        }).then(res => {
          console.log(res, 'RES123122');
          this.conferenceList = res.data
          this.trainList = res.data.materialList
        })
        this.contentLoading = false;
      },
      // 查看文件
      ckeckFile(k) {
        this.$router.push({
          path: '/seeTrainFile',
          query: {
            url: k.viewAddress,
            name: k.originalFilename
          }
        })
      },
      // 下载试题类型模板
      download(k) {
        this.idsy = []
        this.idsy.push(k.id)
        let params = {
          ids: this.idsy
        }
        console.log(params, 'PARAMS789');
        let httpname = '/minio/downloadBatch'
        axios({
            method: 'post',
            url: __PATH.BASE_URL_LABORATORY + httpname,
            data: params,
            responseType: 'blob',
            headers: {
              "Content-Type": "application/json",
            }
          })
          .then((res) => {
            console.log(JSON.stringify(res.data));
            let name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
            let blob = new Blob([res.data]) // { type: "application/vnd.ms-excel" }
            let url = window.URL.createObjectURL(blob) // 创建一个临时的url指向blob对象
            // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
            let a = document.createElement('a')
            a.href = url
            a.download = decodeURI(name)
            a.click()
            // 释放这个临时的对象url
            window.URL.revokeObjectURL(url)
          })
          .catch((res) => {
            this.$message.error('下载失败！')
          })
      },
      // 全部下载
      allDownload() {
        let httpname = 'trainTmp/downloadCoursewares'
        let params = {
          id: this.dataList.id,
        }
        axios({
            method: 'post',
            url: __PATH.BASE_URL_LABORATORY + httpname,
            data: params,
            responseType: 'blob',
            headers: {
              "Content-Type": "application/json",
            }
          })
          .then((res) => {
            console.log(JSON.stringify(res.data));
            let name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
            let blob = new Blob([res.data]) // { type: "application/vnd.ms-excel" }
            let url = window.URL.createObjectURL(blob) // 创建一个临时的url指向blob对象
            // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
            let a = document.createElement('a')
            a.href = url
            a.download = decodeURI(name)
            a.click()
            // 释放这个临时的对象url
            window.URL.revokeObjectURL(url)
          })
          .catch((res) => {
            this.$message.error('下载失败！')
          })
      },
      // 展开试题
      isExpandBtn(item, index) {
        item.isExpand = !item.isExpand;
      },
      handleSizeChange() {},
      handleCurrentChange() {}
    },
  };

</script>
<style lang="scss" scoped>
  .table-content {
    background: #fff;
    border-radius: 4px;
    height: calc(100% - 0px);

    .baseInfo {
      padding: 0 24px 24px 24px;

      .contenter {
        padding-top: 24px;
        font-size: 14px;

        .itemInfo {
          margin-bottom: 16px;
          display: flex;

          .title {
            width: 120px;
            color: #666;
            margin-top: 3px;
          }

          .value {
            flex: 1;
            line-height: 20px;
          }
        }
      }
    }
  }

  .topFilter {
    padding: 15px;
    height: 60px;
    background-color: #fff;

    .backBar {
      color: #121f3e;
      height: 30px;
      border-bottom: 1px solid #dcdfe6;
    }
  }

  .courseContent {
    height: 100%;
    margin-top: 16px;
    background-color: #fff;
    padding: 16px;

    .top {
      margin-bottom: 20px;
      display: flex;
      justify-content: space-between;

      span {
        font-size: 14px;
        color: #3562DB;
      }
    }

    .table_content {
      height: calc(100% - 70px);
      overflow: auto;

      .item {
        height: 56px;
        font-size: 14px;
        line-height: 56px;
        display: flex;
        justify-content: space-between;

        img {
          vertical-align: middle;
          margin-right: 12px;
        }

        .operate {
          span {
            cursor: pointer;
            color: #3562DB;
            margin-right: 16px;
          }
        }

      }
    }
  }

  .itemInfo {
    display: flex;
    justify-content: flex-start;
  }

  .itemInfoName {
    display: flex;
    margin-right: 25px;
  }

  .itemLable {
    width: 115px;
  }

  .itemInfoIndex {
    width: 60%;
    white-space: nowrap;
    /* 不换行 */
    overflow: hidden;
    /* 超出部分隐藏 */
    text-overflow: ellipsis;
  }

  .itemIndex {
    width: 730px;
    white-space: nowrap;
    /* 不换行 */
    overflow: hidden;
    /* 超出部分隐藏 */
    text-overflow: ellipsis;
  }

  ::v-deep .el-radio {
    display: block;
    margin: 10px 0;
  }

  ::v-deep .el-checkbox {
    display: block;
    margin: 10px 0;
  }

  ::v-deep .el-checkbox-group {
    margin-left: 38px;
  }

</style>
