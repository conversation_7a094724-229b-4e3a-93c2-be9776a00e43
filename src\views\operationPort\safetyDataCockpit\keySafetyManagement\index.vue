<!--
 * @Description: 重点安全管理
-->
<template>
  <div class="keySafetyManagement">
    <div ref="innerCircle" class="inner-circle" :style="{ height: innerCircleSize + '%' }">
      <div class="inner-title">重点安全管理</div>
    </div>
    <div
      v-for="(item, index) in outerCircles"
      :key="index"
      class="outer-circle"
      :style="{
        width: outerCircleSize + 'px',
        height: outerCircleSize + 'px',
        transform: `rotate(${index * (360 / outerCircles.length)}deg) translate(${translateDistance}px)`
      }"
    >
      <div
        class="outer-circle-box"
        :style="{
          background: `url(${item.stateBg}) no-repeat`,
          backgroundSize: '100% 100%',
          transform: `rotate(${360 - index * (360 / outerCircles.length)}deg)`
        }"
      >
        <span class="outer-circle-state" :style="{ color: item.textColor }">{{ item.stateText }}</span>
        <span class="outer-circle-name">{{ item.name }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import alarmBg from '@/assets/images/safetyDataCockpit/key-safety-alarm.png'
import defaultBg from '@/assets/images/safetyDataCockpit/key-safety-default.png'
export default {
  name: 'keySafetyManagement',
  data() {
    return {
      alarmBg,
      defaultBg,
      innerCircleSize: 0, // 这里可以根据需要调整内圆的大小
      outerCircles: [], // 这里可以根据需要调整外圆的数量
      outerCircleSize: 0,
      translateDistance: 0
    }
  },
  computed: {},
  watch: {
    '$store.state.settings.sidebarCollapse': {
      handler(val) {
        this.renderDom()
      },
      deep: true
    }
  },
  mounted() {
    window.onresize = () => {
      this.renderDom()
    }
    this.getOuterCirclesData()
  },
  methods: {
    getOuterCirclesData() {
      const outerCircles = [
        {
          name: '氧气站',
          state: 0
        },
        {
          name: '配电间',
          state: 0
        },
        {
          name: '门诊',
          state: 0
        },
        {
          name: '特需门诊',
          state: 0
        },
        {
          name: '危化品库',
          state: 0
        },
        {
          name: '锅炉房',
          state: 0
        },
        {
          name: '急诊',
          state: 0
        }
      ]
      outerCircles.map((e) => {
        if (e.state == 1) {
          e.stateText = '异常'
          e.textColor = '#FF4B4B'
          e.stateBg = this.alarmBg
        } else {
          e.stateText = '正常'
          e.textColor = '#1FFAFF'
          e.stateBg = this.defaultBg
        }
      })
      this.outerCircles = outerCircles
      this.renderDom()
    },
    renderDom() {
      this.innerCircleSize = 54 - Math.ceil(this.outerCircles.length / 5) * 4
      this.$nextTick(() => {
        const innerCircleSize = this.$refs.innerCircle.offsetWidth
        // 暂时用外圆固定大小，后续根绝数量动态调整
        const edgeLength = 150
        // const edgeLength = innerCircleSize / 2
        this.outerCircleSize = this.thirdSideLength(edgeLength, 360 / this.outerCircles.length)
        this.translateDistance = innerCircleSize / 2 + this.outerCircleSize / 2
      })
    },
    // 获取等腰三角形第三边边长
    thirdSideLength(edgeLength, angleInDegrees) {
      const angleInRadians = (angleInDegrees * Math.PI) / 180
      const thirdSide = Math.sqrt(2 * Math.pow(edgeLength, 2) * (1 - Math.cos(angleInRadians)))
      return thirdSide
    }
  }
}
</script>

<style lang="scss" scoped>
.keySafetyManagement {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
  overflow: hidden;
  position: relative;
  .inner-circle {
    height: 45%;
    aspect-ratio: 1/1;
    border-radius: 50%;
    background: url('@/assets/images/safetyDataCockpit/key-safety-bg.png') no-repeat;
    background-size: 100% 100%;
    display: flex;
    .inner-title {
      height: 32px;
      line-height: 32px;
      font-weight: bold;
      font-size: 15px;
      width: 100%;
      margin: auto;
      text-align: center;
      background: url('@/assets/images/safetyDataCockpit/key-safety-title.png') no-repeat;
      background-size: 120% 100%;
      background-position: center;
    }
  }
  .outer-circle {
    border-radius: 50%;
    position: absolute;
    .outer-circle-box {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .outer-circle-state {
        font-size: 18px;
      }
      .outer-circle-name {
        font-size: 14px;
        color: #fff;
      }
    }
  }
}
</style>
