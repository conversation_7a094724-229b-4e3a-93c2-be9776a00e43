<template>
  <PageContainer style="position: relative">
    <div slot="content" class="whole">
      <div class="left">
        <div class="title_box">
          <svg-icon name="right-arrow" /> 空间结构
          <div class="title_btn_icon">
            <i class="el-icon-edit" title="空间结构维护" @click="EditSpace"></i>
          </div>
        </div>
        <el-input v-model="filterText" style="width: 230px; padding-top: 10px" placeholder="请输入关键字"></el-input>
        <div v-loading="treeLoading" class="sino_tree_box">
          <div class="tree_div">
            <el-tree
              ref="tree"
              class="filter-tree"
              :data="treeData"
              :props="defaultProps"
              :default-expanded-keys="idArr"
              :filter-node-method="filterNode"
              :highlight-current="true"
              node-key="id"
              @node-click="nodeClick"
            ></el-tree>
          </div>
        </div>
      </div>
      <div class="right">
        <div style="display: flex">
          <el-input v-model="keywords" placeholder="请输入空间名称" style="width: 200px" @input="spaceIpt"></el-input>&nbsp;
          <el-select v-model="filters.functionDictId" class="ipt" placeholder="请选择功能类型" filterable clearable @change="handelChange">
            <el-option v-for="item in spaceTypeList" :key="item.id" :label="item.dictName" :value="item.id"></el-option>
          </el-select>
          <!-- <el-button class="float_rt" size="medium">编辑</el-button> -->
          <el-button class="float_rt" size="medium" type="primary" @click="addFn">添加</el-button>
          <el-button class="float_rt" size="medium" type="primary" :disabled="multipleSelection.length == 0" @click="setSpaceType">设置空间功能分类</el-button>
          <el-button class="float_rt" size="medium" type="primary" :disabled="multipleSelection.length == 0" @click="setDepart">分配责任部门</el-button>
          <el-button class="float_rt" size="medium" type="primary" :disabled="multipleSelection.length == 0" @click="setPerson">设置责任人</el-button>
          <el-button class="float_rt" size="medium" type="primary" @click="exportFn">导出</el-button>
          <el-button class="float_rt" size="medium" type="primary" @click="importFn">导入</el-button>
          <el-button class="float_rt" size="medium" type="primary" @click="upDownFn">导出模板</el-button>
          <el-button class="float_rt" size="medium" type="primary" @click="QRDownFn">导出空间二维码</el-button>
        </div>
        <div slot="content" style="height: 100%">
          <div class="contentTable">
            <div class="contentTable-main table-content">
              <el-table :data="tableData" :height="tableHeight" stripe border @selection-change="handleSelectionChange" @sort-change="handleSortChange">
                <el-table-column type="selection" width="50" align="center"></el-table-column>
                <el-table-column type="index" label="序号" width="80" align="center">
                  <template slot-scope="scope">
                    <span>{{ (pagination.current - 1) * pagination.size + scope.$index + 1 }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="空间编码" width="150" prop="modelCode" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span class="color_blue" @click="ViewFn(scope.row)">
                      {{ scope.row.modelCode }}
                    </span>
                  </template>
                </el-table-column>

                <el-table-column label="空间本地名称" prop="localSpaceName" show-overflow-tooltip sortable="custom"> </el-table-column>
                <el-table-column label="本地编码" prop="localSpaceCode" show-overflow-tooltip> </el-table-column>
                <el-table-column label="位置" prop="simName" show-overflow-tooltip> </el-table-column>
                <el-table-column label="功能类型" prop="functionDictName" show-overflow-tooltip sortable="custom"> </el-table-column>
                <el-table-column label="归属部门" prop="dmName" show-overflow-tooltip sortable="custom"> </el-table-column>
                <el-table-column label="空间责任人" prop="principalName" show-overflow-tooltip sortable="custom"> </el-table-column>
                <el-table-column label="备注" prop="remark" show-overflow-tooltip> </el-table-column>
                <el-table-column fixed="right" label="操作" align="center" width="200">
                  <template slot-scope="scope">
                    <span style="color: #5188fc; margin-right: 10px; cursor: pointer" @click="EditFn(scope.row)">编辑</span>
                    <span style="color: red; cursor: pointer; margin-right: 10px" @click="DeleteFn(scope.row)">删除</span>
                    <span style="color: #21cf9f; cursor: pointer" @click="record(scope.row)">记录</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <div class="contentTable-footer">
            <el-pagination
              :current-page="pagination.current"
              :page-sizes="[15, 30, 50, 100]"
              :page-size="pagination.size"
              :total="total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            ></el-pagination>
          </div>
        </div>
      </div>
      <sino-slide-Panel title="空间结构维护" :closeState="closeState" :height_100="true">
        <template slot="content">
          <div class="sino_tab">
            <el-tabs v-model="activeTab" type="card" editable @tab-click="clickTab" @tab-add="addTab" @tab-remove="removeTab">
              <el-tab-pane v-for="(areaList, index) in tabAreaList" :key="index" :label="areaList.ssmName" :name="areaList.id">
                <div v-if="tabBuildList" class="sino_tab_content">
                  <el-tabs v-model="activeTabChild" tab-position="left" closable @tab-remove="removeTab">
                    <el-tab-pane v-for="(buildList, index) in tabBuildList" :key="index" :label="buildList.ssmName" :name="buildList.id">
                      <p v-for="(item, index) in buildList.floorDictVoList" :key="index" :class="item.floorDigital == -1 ? 'floor_box line_style' : 'floor_box'">
                        {{ item.floorName }}
                      </p>
                    </el-tab-pane>
                  </el-tabs>
                  <el-button class="sino_tab_btn" size="small" @click="AddBuild">添加建筑</el-button>
                </div>
                <div v-else class="sino_tab_btn_box">
                  <el-button size="small" @click="AddBuild">添加建筑</el-button>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </template>
      </sino-slide-Panel>
      <sinoDialog ref="buildDialog" :title="buildDialogTitle" @sureDialog="buildDialog" @closeDialog="closeDialog">
        <el-form ref="formInline" class="sino_form" :model="formInline" :rules="rules" label-width="100px">
          <el-form-item label="建筑名称" prop="ssmName">
            <el-input v-model="formInline.ssmName" class="sino_form_input" placeholder="请输入建筑名称" maxlength="20" show-word-limit> </el-input>
          </el-form-item>
          <el-form-item label="建筑楼层" prop="floorIdList">
            <div class="checkbox_group">
              <el-checkbox-group v-model="formInline.floorIdList" @change="handleChecked">
                <el-checkbox v-for="(floor, index) in floorList" :key="index" :label="floor.id">{{ floor.floorName }}</el-checkbox>
              </el-checkbox-group>
            </div>
          </el-form-item>
        </el-form>
      </sinoDialog>
      <sinoDialog ref="areaDialog" :title="areaDialogTitle" @sureDialog="areaDialog" @closeDialog="closeDialog">
        <el-form ref="formInline" class="sino_form" :model="formInline" :rules="rules" label-width="100px">
          <el-form-item label="区域名称" prop="ssmName">
            <el-input v-model="formInline.ssmName" class="sino_form_input" placeholder="请输入区域名称" show-word-limit> </el-input>
          </el-form-item>
        </el-form>
      </sinoDialog>
      <sinoDialog ref="dialogSpaceType" title="设置空间功能分类" @sureDialog="sureDialog(1)" @closeDialog="closeDialog">
        <div class="title_box" style="border: none; padding: 0"><svg-icon name="right-arrow" />空间功能类型</div>
        <el-radio-group v-model="checkedRadio" @change="checkedChange">
          <el-radio v-for="(item, index) in spaceTypeList" :key="index" class="el_radio" :label="item.id">{{ item.dictName }}</el-radio>
        </el-radio-group>
      </sinoDialog>
      <sinoDialog ref="dialogDepart" title="设置责任部门" @sureDialog="sureDialog(2)" @closeDialog="closeDialog">
        <div class="title_box" style="border: none; padding: 0"><svg-icon name="right-arrow" />责任部门</div>
        <el-collapse v-model="activeName" style="padding: 0 10px 0 20px" accordion @change="handelChangeCom">
          <el-collapse-item v-for="(list, index) in collapseData" :key="index" :title="list.unitComName" :name="list.umId">
            <div class="sino_tree_box" style="margin: 0; overflow: auto">
              <el-tree
                ref="departTree"
                class="filter-tree"
                style="overflow: hidden"
                :data="departTreeData"
                :props="departProps"
                node-key="id"
                show-checkbox
                :check-strictly="true"
                highlight-current
                @check-change="(data, checked, self) => departNodeClickRad(data, checked, self, index)"
              ></el-tree>
            </div>
          </el-collapse-item>
        </el-collapse>
      </sinoDialog>
      <sinoDialog ref="dialogUsers" title="设置责任人" :customStyle="true" @sureDialog="sureDialog(3)" @closeDialog="closeDialog">
        <StaffManagement ref="StaffManagement" :isDialog="true" :checkbox="false" :nature="true"></StaffManagement>
      </sinoDialog>
      <sinoImportFile :importDialog="importDialog" :addFileName="addFileName" @cancelFile="cancelFile" @getTableData="spaceTreeListFn" />
    </div>
  </PageContainer>
</template>

<script>
import store from '@/store/index'
import axios from 'axios'
import { transData } from '@/util'
import tableListMixin from '@/mixins/tableListMixin.js'
import sinoDialog from './common/sinoDialog.vue'
import sinoSlidePanel from '../../foundation/organizationManage/common/sinoSlidePanel.vue'
import StaffManagement from './components/staffManagement.vue'
import sinoImportFile from './common/sinoImportFile.vue'
export default {
  name: 'ledgerManage',
  components: {
    sinoSlidePanel,
    sinoDialog,
    StaffManagement,
    sinoImportFile
  },
  mixins: [tableListMixin],
  data() {
    return {
      keywords: '',
      treeLoading: true,
      filterText: '',
      treeData: [],
      idArr: [],
      spaceIds: [],
      defaultProps: {
        label: 'ssmName',
        children: 'list'
      },
      filters: {
        functionDictId: ''
      },
      spaceTypeList: [],
      tableData: [],

      multipleSelection: [],
      // -----------------------------Pagination
      pagination: {
        current: 1,
        size: 15
      }, // 分页数据
      total: 0, // 数据总条数
      // -----------------------------sino-slide-Panel
      closeState: true,
      activeTab: '',
      activeTabChild: '',
      tabAreaList: [],
      tabBuildList: [],
      tabFloorList: [],
      // -----------------------------Dialog_空间结构维护
      areaDialogTitle: '',
      buildDialogTitle: '',
      formInline: {
        pid: '',
        ssmName: '',
        floorIdList: []
      },
      rules: {},
      floorList: [],
      // ---------------------Dialog_批量操作
      checkedRadio: '',
      activeName: '',
      collapseData: [],
      departTreeData: [],
      departProps: {
        label: 'deptName',
        children: 'list'
      },
      checkedDepartId: [],
      // ---------------------Dialog_导入
      importDialog: false,
      addFileName: 'space',
      orderItems: []
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {},

  mounted() {
    this.spaceTreeListFn()
    this.valveTypeListFn()
  },
  methods: {
    handleSortChange({ order, prop }) {
      this.orderItems = []
      let obj = {}
      if (order == 'ascending') {
        obj.asc = true
        obj.column = prop
        this.orderItems.push(obj)
      } else if (order == 'descending') {
        obj.asc = false
        obj.column = prop
        this.orderItems.push(obj)
      } else {
        this.orderItems = []
      }
      this.pagination.current = 1
      this.pagination.size = 15
      this.getSpacelistFn()
    },
    spaceIpt(val) {
      this.keywords = val
      this.pagination.current = 1
      this.pagination.size = 15
      this.getSpacelistFn()
    },
    //  ------------------------------获取空间结构 Tree And fn
    spaceTreeListFn() {
      this.spaceIds = []
      this.$api.spaceTreeList().then((res) => {
        this.treeLoading = false
        if (res.code == 200) {
          res.data.map((tree) => {
            tree.ssmType == 1 || tree.ssmType == 2 ? this.idArr.push(tree.id) : ''
            return this.idArr
          })
          this.treeData = transData(res.data, 'id', 'pid', 'list')
          this.$nextTick(function () {
            this.$refs.tree.setCurrentKey(res.data[0].id)
          })
          this.spaceIds.push(res.data[0].id)
          this.getSpacelistFn()
        }
      })
    },
    filterNode(value, data) {
      if (!value) return true
      return data.ssmName.indexOf(value) !== -1
    },
    nodeClick(val) {
      const { tree } = this.$refs
      this.spaceIds = []
      this.pagination.current = 1
      let checkedNode = tree.getNode(val.id)
      this.getTreeNode(checkedNode)
      this.pagination.current = 1
      this.pagination.size = 15
      this.getSpacelistFn()
    },
    getTreeNode(node) {
      if (node.level >= 1) {
        this.spaceIds.unshift(node.data.id)
        this.getTreeNode(node.parent)
      } else {
        this.spaceIds.unshift('#')
      }
    },

    // ---------------------------Table-Fn
    //  导出
    exportFn() {
      let spaceInfoListByPageVo = {
        ...this.filters,
        ...this.pagination.size
      }
      let Ids = []
      this.multipleSelection.forEach((item) => {
        Ids.push(item.id)
      })
      spaceInfoListByPageVo.ids = Ids.join(',')
      spaceInfoListByPageVo.simCode = this.spaceIds.join()
      const userInfo = store.state.user.userInfo.user

      let menuList = this.$store.state.menu.routes
      let firstTitle = ''
      let routeList = []
      this.$router.currentRoute.matched.filter(item => item.name).forEach(el => {
        routeList.push(el.meta.title)
      })
      if (menuList.length) {
        firstTitle = menuList[this.$store.state.menu.headerActived].meta.title
        routeList.unshift(firstTitle)
      }
      axios({
        method: 'post',
        url: __PATH.VUE_SPACE_API + 'space/spaceInfo/exportSpaceInfoList',
        data: spaceInfoListByPageVo,
        responseType: 'arraybuffer',
        headers: {
          'Content-Type': 'application/json;charset=utf-8',
          Authorization: 'Bearer ' + store.state.user.token,
          hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
          unitCode: userInfo.unitCode ?? 'BJSYGJ',
          'operation-type': 4,
          'operation-content': encodeURIComponent(routeList.join(','))
        }
      })
        .then((res) => {
          this.$message.success(res.message || '导出成功')
          this.$tools.downloadFile(res, this)
        })
        .catch((res) => {
          this.$message.error(res.message || '导出失败')
        })
    },
    //  模板下载
    upDownFn() {
      const userInfo = store.state.user.userInfo.user
      axios({
        method: 'post',
        url: __PATH.VUE_SPACE_API + 'space/spaceInfo/exportSpaceInfoTemplateByHandwork',
        data: '',
        responseType: 'arraybuffer',
        headers: {
          'Content-Type': 'application/json;charset=utf-8',
          Authorization: 'Bearer ' + store.state.user.token,
          hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
          unitCode: userInfo.unitCode ?? 'BJSYGJ'
        }
      })
        .then((res) => {
          this.$message.success(res.message || '导出成功')
          this.$tools.downloadFile(res, this)
        })
        .catch((res) => {
          this.$message.error(res.message || '导出失败')
        })
    },
    //  二维码下载
    QRDownFn() {
      let spaceQRCodeZip = {}
      let spaceIds = []
      if (this.multipleSelection.length > 0) {
        this.multipleSelection.forEach((item) => {
          spaceIds.push(item.id)
        })
        spaceQRCodeZip.ids = spaceIds.join(',')

        const userInfo = store.state.user.userInfo.user
        axios({
          method: 'post',
          url: __PATH.VUE_SPACE_API + 'space/spaceInfo/getSpaceQRCodeZip',
          data: spaceQRCodeZip,
          responseType: 'arraybuffer',
          headers: {
            'Content-Type': 'application/json;charset=utf-8',
            Authorization: 'Bearer ' + store.state.user.token,
            hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
            unitCode: userInfo.unitCode ?? 'BJSYGJ'
          }
        })
          .then((res) => {
            this.$message.success(res.message || '导出成功')
            this.$tools.downloadFile(res, this)
          })
          .catch((res) => {
            this.$message.error(res.message || '导出失败')
          })
      } else {
        this.$message.warning('请选择准备生成的空间信息')
      }
    },
    //  导入
    importFn() {
      this.importDialog = true
    },
    cancelFile() {
      this.importDialog = false
    },
    //  获取功能类型字典列表
    valveTypeListFn() {
      this.$api
        .valveTypeList({
          typeValue: 'SP'
        })
        .then((res) => {
          if (res.code == 200) {
            this.spaceTypeList = res.data
          }
        })
    },
    //  获取单位列表
    getUnitListFn() {
      this.$api.getUnitList({}).then((res) => {
        this.treeLoading = false
        if (res.code == 200) {
          // 单位仅为本院
          this.collapseData = []
          res.data.map((list) => {
            list.nature == 1 ? this.collapseData.push(list) : ''
          })
        }
      })
    },
    handelChangeCom(val) {
      this.activeName = val
      this.getDeptListFn(val)
    },
    // 部门列表
    getDeptListFn(unitId) {
      this.departTreeData = []
      this.$api
        .getDeptList({
          unitId: unitId
        })
        .then((res) => {
          if (res.code == 200) {
            this.departTreeData = transData(res.data, 'id', 'pid', 'list')
            // this.$nextTick(() => {
            this.setDisable(this.departTreeData)
            // })
          }
        })
    },
    setDisable(data) {
      data.forEach((v) => {
        if (v.list && v.list.length) {
          v.disabled = true
          this.setDisable(v.list) // 子级循环时把这一层数据的count传入
        }
      })
    },
    // Tree  @check-change  多选
    departNodeClickMul(data, checked, self) {
      if (checked) {
        this.checkedDepartId.push(data.id)
      } else {
        let checkedDepartId = this.checkedDepartId
        let index = checkedDepartId.findIndex((checkedDepartId) => checkedDepartId == data.id)
        this.checkedDepartId.splice(index, 1)
      }
    },
    // Tree  @check-change  单选
    departNodeClickRad(data, checked, self, index) {
      if (checked) {
        let num = this.collapseData.length - 1
        for (let i = 0; i <= num; i++) {
          if (i != index) {
            this.$refs.departTree[i].setCheckedKeys([])
          } else {
            this.$refs.departTree[i].setCheckedKeys([data.id])
            this.checkedDepartId = [data.id]
          }
        }
      }
    },
    getSpacelistFn() {
      let data = {
        simCode: '',
        ...this.filters,
        ...this.pagination,
        keywords: this.keywords,
        orderItems: this.orderItems
      }
      data.simCode = this.spaceIds.join()
      this.$api.getSpacelist(data).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.records
          this.pagination.current = res.data.current
          this.pagination.size = res.data.size
          this.total = res.data.total
        }
      })
    },
    handleSizeChange(val) {
      this.pagination.size = val
      this.getSpacelistFn()
    },
    handleCurrentChange(val) {
      this.pagination.current = val
      this.getSpacelistFn()
    },
    handelChange(val) {
      this.filters.functionDictId = val
      this.pagination.current = 1
      this.pagination.size = 15
      this.getSpacelistFn()
    },
    checkedChange(val) {
      this.checkedRadio = val
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    addFn() {
      this.$router.push({
        path: '/spaceMess',
        query: { type: 'Add' }
      })
    },
    EditFn(row) {
      this.$router.push({
        path: '/spaceMess',
        query: { type: 'Edit', id: row.id }
      })
    },
    ViewFn(row) {
      this.$router.push({
        path: '/particulars',
        query: { type: 'View', id: row.id }
      })
    },
    record(row) {
      this.$router.push({
        path: '/spaceRecord',
        query: {
          id: row.id
        }
      })
    },
    DeleteFn(row) {
      this.$confirm('确认删除选中的空间信息吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$api.deleteSpace({ id: row.id }, { 'operation-type': 3, 'operation-name': row.localSpaceName, 'operation-id': row.id}).then((res) => {
            if (res.code == 200) {
              this.getSpacelistFn()
              this.$message({
                message: res.msg,
                type: 'success'
              })
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    setSpaceType() {
      this.$refs.dialogSpaceType.dialogTableVisible = true
      this.valveTypeListFn()
    },
    setDepart() {
      this.$refs.dialogDepart.dialogTableVisible = true
      this.getUnitListFn()
    },
    setPerson() {
      this.$refs.dialogUsers.dialogTableVisible = true
    },
    // ---------------------------sino-slide-Panel
    EditSpace() {
      this.closeState = !this.closeState
      this.getAreaFn()
    },
    getAreaFn() {
      this.$api.getArea({}).then((res) => {
        if (res.code == 200) {
          this.tabAreaList = res.data
          if (res.data && res.data.length) {
            this.activeTab = res.data[0].id
            this.clickTab()
          }
        }
      })
    },
    getBuildFn(areaId) {
      this.$api
        .getBuildByAreaID({
          areaId: areaId
        })
        .then((res) => {
          if (res.code == 200) {
            this.tabBuildList = res.data
            res.data ? (this.activeTabChild = res.data[0].id) : ''
          }
        })
    },
    clickTab() {
      this.getBuildFn(this.activeTab)
      // if(this.closeState){
      //   this.$refs.areaDialog.dialogTableVisible = true;
      //   this.areaDialogTitle = "修改区域";
      // }
    },
    addTab() {
      this.$refs.areaDialog.dialogTableVisible = true
      this.areaDialogTitle = '新增区域'
    },
    removeTab(targetName) {
      this.$confirm('确认删除选中的空间结构吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.deleteStructure({id: targetName}).then((res) => {
          if (res.code == 200) {
            this.spaceTreeListFn()
            this.getAreaFn()
            this.$message({
              message: res.msg,
              type: 'success'
            })
          } else {
            this.$message({
              message: res.msg,
              type: 'error'
            })
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    AddBuild() {
      this.$refs.buildDialog.dialogTableVisible = true
      this.buildDialogTitle = '新增建筑'
      this.getEmptyBuildFn()
    },
    // ---------------------------Dialog_空间结构维护
    areaDialog() {
      let fnName
      let data = {
        ssmName: this.formInline.ssmName
      }
      if (this.areaDialogTitle == '新增区域') {
        fnName = this.$api.addArea
      } else {
        fnName = this.$api.updateArea
        data.id = this.activeTab
      }
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          fnName(data).then((res) => {
            if (res.code == 200) {
              this.$refs.areaDialog.dialogTableVisible = false
              this.spaceTreeListFn()
              this.getAreaFn()
              this.$message({
                message: res.msg,
                type: 'success'
              })
            }
          })
        }
      })
    },
    // 楼层字典列表
    getEmptyBuildFn() {
      this.$api.getEmptyBuild().then((res) => {
        if (res.code == 200) {
          this.floorList = res.data.floorDictVoList
        }
      })
    },
    handleChecked(val) {
      this.formInline.floorIdList = val
    },
    buildDialog() {
      let fnName
      let data = {
        ...this.formInline
      }
      data.pid = this.activeTab
      if (this.buildDialogTitle == '新增建筑') {
        fnName = this.$api.addBuild
      } else {
        fnName = this.$api.updateBuild
        data.id = this.activeTabChild
      }
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          fnName(data).then((res) => {
            if (res.code == 200) {
              this.$refs.buildDialog.dialogTableVisible = false
              this.spaceTreeListFn()
              this.getAreaFn()
              this.$message({
                message: res.msg,
                type: 'success'
              })
            }
          })
        }
      })
    },
    closeDialog() {
      this.reset()
      this.$refs.areaDialog.dialogTableVisible = false
      this.$refs.buildDialog.dialogTableVisible = false
    },
    // 重置
    reset() {
      this.checkedRadio = ''
      this.checkedDepartId = []
      this.activeName = ''
      this.departTreeData = []
      this.formInline = {
        pid: '',
        ssmName: '',
        floorIdList: []
      }
    },
    // ---------------------------Dialog_表格操作
    sureDialog(type) {
      let spaceInfoIds = []
      let bindIds = []
      this.multipleSelection.forEach((item) => {
        spaceInfoIds.push(item.id)
      })
      if (type == 1) {
        bindIds.push(this.checkedRadio)
      } else if (type == 2) {
        bindIds = this.checkedDepartId
      } else {
        let selected = this.$refs.StaffManagement.radioObj
        bindIds.push(selected.id)
      }
      this.$api
        .bindSpace({
          bindType: type, // 1：空间功能类型，2：分配责任部门，3：设置责任人
          bindIds: bindIds.join(),
          spaceInfoIds: spaceInfoIds.join()
        })
        .then((res) => {
          if (res.code == 200) {
            this.$refs.dialogSpaceType.dialogTableVisible = false
            this.$refs.dialogDepart.dialogTableVisible = false
            this.$refs.dialogUsers.dialogTableVisible = false
            this.getSpacelistFn()
            this.$message({
              message: res.msg,
              type: 'success'
            })
          }
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.page-container {
  position: relative;
}

.sino_tab {
  .sino_tab_content {
    height: 280px;
    overflow: auto;
    ::v-deep .el-tabs{
      .el-tabs__header {
        max-width: 200px;
        .el-tabs__item{
          position: relative;
          width: calc(100% - 15px);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          .el-icon-close{
            position: absolute;
            right: 0;
            top: 15px;
            width: 15px;
            height: 15px;
          }
        }
      }
    }
    .sino_tab_btn {
      position: absolute;
      right: 0;
      top: 0;
      margin: 0;
    }

    .line_style {
      border-bottom: 1px solid #ddd;
    }
  }

  .sino_tab_btn_box {
    height: 280px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .floor_box {
    line-height: 32px;
    margin: 0 100px 0 20px;
    padding-left: 15px;
    font-size: 14px;
  }
}

.checkbox_group {
  height: 200px;
  overflow: auto;
}

.el-main {
  padding: 15px;
  overflow: hidden;
  background-color: #fff;

  .sino_panel {
    height: 100%;
    border-radius: 10px;
    background: #fff;
    position: relative;
    font-size: 14px;
  }

  .sino_page {
    height: 100%;
    position: relative;

    .el-aside {
      width: 260px;
      margin: 0 16px 0 0;
      overflow: hidden;
    }

    .sino_page_left {
      margin: 0 0 0 16px !important;
    }

    .el-aside,
    .el-main {
      // height: 100%;
      background: #fff;
      border-radius: 10px;
      padding: 0;

      .el-collapse {
        height: calc(100% - 40px);
        overflow: auto;
      }
    }
  }
}

.title_box {
  box-sizing: border-box;
  padding-left: 24px;
  height: 50px;
  width: 100%;
  line-height: 50px;
  text-align: left;
  border-bottom: 1px solid #d8dee7;

  .title-tip {
    display: inline-block;
    width: 4px;
    height: 16px;
    margin-right: 8px;
    position: relative;
    top: 2px;
    background: #5188fc;
  }

  .title_name {
    font-size: 16px;
    font-weight: bold;
  }

  .title_btn_icon {
    float: right;
  }

  .title_btn_icon i {
    margin-right: 20px;
    cursor: pointer;
  }
}

.whole {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;

  .left {
    text-align: center;
    width: 250px;
    height: 100%;
    background-color: #fff;
    border-radius: 5px;
    overflow: hidden;

    .left_d {
      height: calc(100% - 20px);
    }
  }

  .right {
    width: calc(100% - 260px);
    background-color: #fff;
    height: 100%;
    border-radius: 5px;
    padding: 10px;
  }
}

.contentTable {
  height: calc(100% - 58px);
  background: #fff;
  border-radius: 4px;
  padding: 16px 0 0;
  display: flex;
  flex-direction: column;
  overflow: auto;

  .contentTable-main {
    flex: 1;
    overflow: auto;
  }

  .contentTable-footer {
    padding: 10px 0 0;
  }

  .alarmLevel {
    padding: 3px 6px;
    border-radius: 4px;
    color: #fff;
    line-height: 14px;
  }

  .alarmStatus {
    position: relative;
    display: inline-block;
    padding-left: 12px;

    .alarmStatusIcon {
      display: inline-block;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 8px;
      height: 8px;
      border-radius: 100%;
    }
  }

  .collectIcon {
    font-size: 16px;
    margin-right: 4px;
  }
}

.sino_tree_box {
  margin: 10px;
  height: calc(100% - 100px) !important;
  overflow: auto;

  .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
    background-color: #f5f6fb;
  }

  .el-tree-node__content {
    height: 38px;

    .el-tree-node__label {
      font-size: 15px;
    }
  }
}

.color_blue {
  color: #5197fd;
  cursor: pointer;
}

.ipt {
  width: 200px;
  margin-right: 10px;
}

.el_radio {
  width: 120px;
  padding: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
}

::v-deep .el-dialog {
  width: 70%;
}
</style>
