<template>
  <div class="navigate-bar-container">
    <el-breadcrumb separator="→">
      <el-breadcrumb-item v-for="menuItem in menuList" :key="menuItem.labelName">
        <router-link :to="menuItem.path">
          <span :class="`breadcrumbLabel  ${menuItem.class}`" @click="changeWeight(menuItem.index)">{{ menuItem.labelName }}</span>
        </router-link>
      </el-breadcrumb-item>
    </el-breadcrumb>
  </div>
</template>

<script>
import utils from '../../utils/utils'
export default {
  data() {
    return {
      flag: -1,
      menuList: [
        {
          labelName: '1、问卷名称',
          path: '/degreeSatisfaction/questManagement/questionCreate',
          class: 'unLine-heigth',
          index: 1
        },
        {
          labelName: '2、问卷题目',
          path: '/degreeSatisfaction/questManagement/questionDesign',
          class: 'unLine-heigth',
          index: 2
        },
        {
          labelName: '3、问卷设置',
          path: '/degreeSatisfaction/questManagement/questionSetting',
          class: 'unLine-heigth',
          index: 3
        },
        {
          labelName: '4、问卷传播',
          path: '/degreeSatisfaction/questManagement/propagation',
          class: 'unLine-heigth',
          index: 4
        },
        {
          labelName: '5、问卷回收',
          path: '/degreeSatisfaction/questManagement/recoveryQues',
          class: 'unLine-heigth',
          index: 5
        },
        {
          labelName: '6、单题分析',
          path: '/degreeSatisfaction/questManagement/questionAnalysis',
          class: 'unLine-heigth',
          index: 6
        },
        {
          labelName: '7、交叉分析',
          path: '/degreeSatisfaction/questManagement/questionTotalAnalysis',
          class: 'unLine-heigth',
          index: 7
        }
      ]
    }
  },
  created() {
    var flag = utils.getLocalStorage('breadLineHeight', '')
    this.flag = flag
    if (this.flag) {
      this.changeWeight(this.flag)
    }
  },
  methods: {
    changeWeight(i) {
      this.$emit('quest')
      this.menuList[i - 1].class = 'line-heigth'
      if (this.flag && this.flag != -1 && i != this.flag) {
        this.menuList[this.flag - 1].class = 'unLine-heigth'
      }
      this.flag = i
      utils.setLocalStorage('breadLineHeight', this.flag)
    }
  }
}
</script>

<style lang="scss" scoped>
.line-heigth {
  font-weight: 600 !important;
}

.unLine-heigth {
  font-weight: normal;
}

.navigate-bar-container {
  padding: 20px;
}

.breadcrumbLabel {
  color: #3562db;
  cursor: pointer;
}

span .el-breadcrumb__separator {
  color: #3562db;
}
</style>
