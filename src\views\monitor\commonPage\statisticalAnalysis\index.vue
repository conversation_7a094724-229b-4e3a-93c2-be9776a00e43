<template>
  <PageContainer>
    <div slot="content" class="monitor-content">
      <div class="monitor-content-left">
        <el-tree
          ref="tree"
          v-loading="treeLoading"
          :data="treeData"
          :props="defaultProps"
          node-key="id"
          :highlight-current="true"
          @check="treeChecked"
          @node-click="handleNodeClick"
        ></el-tree>
      </div>
      <OpenDoor v-if="showOpenDoor" :projectCode="projectCode"/>
      <runFloor v-else-if="showRunFloor" :projectCode="projectCode" />
      <div v-else class="monitor-content-right">
        <div class="right-heade">
          <div class="search-from">
            <el-date-picker
              v-if="checkedData.id != 6"
              v-model="filters.daterange"
              type="daterange"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="margin-right: 20px"
              @change="dateChange"
            >
            </el-date-picker>
            <el-select
              v-if="['7', '8', '14', '11', '12', '13'].includes(checkedData.id)"
              v-model="selectItems"
              placeholder="统计项目"
              multiple
              collapse-tags
              @change="statisticsItemChange"
            >
              <el-option v-for="item in itemList" :key="item.label" :label="item.label" :value="item.label"> </el-option>
            </el-select>
            <el-select v-if="checkedData.id == 6" v-model="filters.surveyCode" class="sino_sdcp_input mr15" filterable multiple collapse-tags placeholder="监测项实体">
              <el-option v-for="item in surveyByProjecList" :key="item.sensorCode" :label="item.sensorName" :value="item.sensorCode"></el-option>
            </el-select>
            <div style="display: inline-block">
              <el-button type="primary" plain @click="reset()">重置</el-button>
              <el-button type="primary" @click="search()">查询</el-button>
            </div>
          </div>
          <div class="batch-control">
            <el-button v-for="item of dateList" :key="item.id" type="primary" :class="activeTitle != item.id ? 'disabled-button' : 'sure'" @click="switchingTime(item.id)">{{
              item.name
            }}</el-button>
            <el-button v-if="projectCode == '713e24b03094410499db0b08a2eccbcc'" type="primary" icon="el-icon-upload2" @click="exportElevator">导出</el-button>
            <el-button v-else type="primary" icon="el-icon-upload2" @click="exportClickExport">导出</el-button>
          </div>
        </div>
        <div v-loading="loading" class="right-content">
          <div style="width: 100%">
            <div v-show="checkedData.id == '6'" style="width: 100%">
              <div id="riskTypeline" class="charts" style="margin: 0 auto; width: 60%; height: 320px"></div>
            </div>
          </div>
          <div id="pdf" class="table-content">
            <div class="table doubleThTable" style="margin: 0 auto; width: 80%; margin-bottom: 20px">
              <div v-if="projectCode == '713e24b03094410499db0b08a2eccbcc'" style="text-align: center; font-size: 28px; padding-bottom: 13px">
                <span v-for="(item, index) in tableTitle" :key="index">
                  {{ item.projectName }}
                </span>
                <div style="padding-top: 5px">{{ filters.daterange[0] }}~{{ filters.daterange[1] }}</div>
              </div>
              <!-- 第一个表 -->
              <el-table
                v-if="checkedData.id != 6"
                :key="Math.random()"
                :data="taskTable"
                :border="true"
                stripe
                :cell-style="{ padding: '8px' }"
                :header-cell-style="headerStyle(3)"
              >
                <el-table-column type="index" label="序号" width="65">
                  <template slot-scope="scope">
                    <span>{{ scope.$index + 1 }}</span>
                  </template>
                </el-table-column>
                <el-table-column v-for="(item, index) in tableTitle" :key="index" :prop="item.value" :label="item.label" show-overflow-tooltip :width="item.value ? item.width : 1">
                  <!-- <template  slot-scope="scope">
                    <span v-if="item.value === 'correspondingAlarmTypesForDevices'">
                      <span v-for="alarmType in scope.row.correspondingAlarmTypesForDevices" :key="alarmType.paramId">{{alarmType.paramName + ': ' }}
                        <span style="color: #5482ee; cursor: pointer" @click="jumpAlarmTypeDetail(alarmType)">{{ alarmType.count + ' '}}</span>
                      </span>
                    </span>
                    <span v-else>{{ scope.row[item.value] }}</span>
                  </template> -->
                </el-table-column>
              </el-table>
              <el-table
                v-if="checkedData.id == 5"
                :key="Math.random()"
                style="margin-top: 30px"
                :data="taskTableTwo"
                :border="true"
                stripe
                :cell-style="{ padding: '8px' }"
                :header-cell-style="headerStyle(3)"
              >
                <el-table-column type="index" label="序号" width="65">
                  <template slot-scope="scope">
                    <span>{{ scope.$index + 1 }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-for="(item, index) in tableTitleTwo"
                  :key="index"
                  :prop="item.value"
                  :label="item.label"
                  show-overflow-tooltip
                  :width="item.value ? item.width : 0"
                ></el-table-column>
              </el-table>
            </div>
            <!-- 第二个表 -->
            <div v-if="checkedData.id == 6" class="table doubleThTable" style="margin: 0 auto; width: 80%; margin-bottom: 20px">
              <el-table :key="Math.random()" :data="taskTable" :border="true" stripe :cell-style="{ padding: '8px' }" :header-cell-style="headerStyle(3)">
                <el-table-column prop="year" label="时间段" align="center" width="150">
                  <el-table-column prop="year" label="科室用氧量" align="center" width="150" show-overflow-tooltip>
                    <template slot-scope="scope">
                      {{ scope.row.name }}
                    </template>
                  </el-table-column>
                </el-table-column>
                <el-table-column type="index" label="序号" width="65">
                  <template slot-scope="scope">
                    <span>{{ scope.$index + 1 }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-for="(item, index) in tableTitle"
                  :key="index"
                  :prop="item.value"
                  :label="item.label"
                  show-overflow-tooltip
                  :width="item.value ? item.width : 0"
                ></el-table-column>
              </el-table>
            </div>
            <div class="" style="width: 100%">
              <!-- 柱形图 -->
              <div
                v-show="
                  checkedData.id == '4' ||
                    checkedData.id == '7' ||
                    checkedData.id == '14' ||
                    checkedData.id == '11' ||
                    checkedData.id == '12' ||
                    checkedData.id == '13' ||
                    checkedData.id == '10'
                "
                style="width: 100%"
              >
                <div id="riskTypeBar" class="charts" style="margin: 0 auto; width: 60%; height: 340px"></div>
              </div>
              <!-- 饼图 -->
              <div v-show="checkedData.id == '8' && !pieNullimg" style="width: 100%">
                <div id="mypreChart" class="charts" style="margin: 0 auto; width: 60%; height: 340px"></div>
              </div>
              <div v-if="pieNullimg" style="text-align: center; margin-top: 20px">
                <img src="@/assets/images/monitor/img-default.png" alt="" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import moment from 'moment'
import * as echarts from 'echarts'
import { downloadPDF } from '@/util/htmlToPdf.js'
moment.locale('zh-cn')
export default {
  name: 'commonStatisticalAnalysis',
  components: {
    OpenDoor: () => import('./openDoor.vue'),
    runFloor: () => import('./runFloor.vue')
  },
  props: {
    projectCode: {
      type: String,
      default: ''
    },
    requestHttp: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      tableTitle: [],
      tableTitleTwo: [],
      taskTable: [],
      taskTableTwo: [],
      header: {
        // 环境的
        oneTableList: [
          { label: '监测实体名称', value: 'surveyName', width: '200' },
          { label: '最低值', value: 'downValue', width: '200' },
          { label: '最高值', value: 'upperValue', width: '200' },
          { label: '平均值', value: 'average', width: '200' },
          { label: '报警次数', value: 'policeCount', width: '' }
        ],
        // 用氧统计分析
        twoTableList: [
          { label: '监测项名称', value: 'name', width: '' },
          { label: '用氧量(m³)', value: 'value', width: '' },
          { label: '所占百分比', value: 'percent', width: '' }
        ],
        threeTableList: [
          { label: '监测项名称', value: 'name', width: '' },
          { label: '用氧量(m³)', value: 'value', width: '' }
        ],
        // 报警统计分析1
        sanTableList: [
          { label: '监测项名称', value: 'surveyName', width: '' },
          { label: '报警次数', value: 'count', width: '' }
        ],
        // 报警统计分析2
        siTableList: [
          { label: '报警类型', value: 'policeReason' },
          { label: '报警次数', value: 'count' }
        ],
        // 用氧走势Fenix
        wuTableList: [],
        // 设备报警统计
        liuTableList: []
      },
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      multipleSelection: [],
      tableData: [],
      advancClose: true,
      treeLoading: false,
      tableLoading: false,
      organizationTypeArr: [],
      defaultProps: {
        children: 'children',
        label: 'name',
        value: 'id'
      },
      treeData: [],
      checkedData: '',
      baseimg: '',
      filters: {
        daterange: '',
        surveyCode: []
      },
      activeTitle: '4',
      dateList: [
        {
          name: '今日',
          id: 0
        },
        {
          name: '本月',
          id: 1
        },
        {
          name: '本年',
          id: 2
        }
      ],
      pieNullimg: false,
      surveyByProjecList: [],
      selectItems: [],
      itemList: [],
      // 显示开门次数
      showOpenDoor: false,
      // 显示运行楼层
      showRunFloor: false
    }
  },
  computed: {},
  created() {
    this.init()
    this.$api
      .getAllSurveyByProjectCode(
        {
          projectCode: this.projectCode
        },
        this.requestHttp
      )
      .then((res) => {
        this.surveyByProjecList = res.data
      })
  },
  methods: {
    // 统计项过滤
    statisticsItemChange(val) {
      this.tableTitle = this.itemList.filter((v) => val.includes(v.label))
    },
    /**
     * @description: 柱状图
     * @param {Obj,str}
     */
    drawBar(barData, chartId, unitName) {
      // 基于准备好的dom，初始化echarts实例
      let myChart = echarts.init(document.getElementById(chartId))
      myChart.setOption(
        this.checkedData.id == 10
          ? {
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'shadow'
              }
            },
            animation: false, // 该属性必须加，不然转换后的base64达不到图表的效果
            grid: {
              left: '5%',
              right: '7%',
              bottom: '3%',
              top: '5%',
              containLabel: true
            },
            color: ['#61a5e8'],
            xAxis: {
              data: barData.timeList,
              type: 'category',
              boundaryGap: [0, 0.01],
              axisLabel: {
                show: true,
                textStyle: {
                  color: '#909399' // 更改坐标轴文字颜色
                }
              },
              axisLine: {
                lineStyle: {
                  color: '#D8DEE7' // 更改坐标轴颜色
                }
              },
              // 网格样式
              splitLine: {
                show: false,
                lineStyle: {
                  color: ['#f5f5f5'],
                  width: 1,
                  type: 'dashed'
                }
              }
            },
            yAxis: {
              type: 'value',
              axisLabel: {
                show: true,
                textStyle: {
                  color: '#909399' // 更改坐标轴文字颜色
                }
              },
              axisLine: {
                lineStyle: {
                  color: '#D8DEE7' // 更改坐标轴颜色
                }
              }
            },
            series: [
              {
                barWidth: 30,
                type: 'bar',
                label: {
                  show: false,
                  position: 'center'
                },
                data: barData.value,
                markLine: {
                  data: [
                    {
                      type: 'average',
                      name: '平均值'
                    }
                  ]
                }
              }
            ]
          }
          : {
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'shadow'
              }
            },
            animation: false, // 该属性必须加，不然转换后的base64达不到图表的效果
            grid: {
              left: '3%',
              right: '4%',
              bottom: '3%',
              top: '0%',
              containLabel: true
            },
            color: ['#61a5e8'],
            xAxis: {
              type: 'value',
              boundaryGap: [0, 0.01],
              axisLabel: {
                show: true,
                textStyle: {
                  color: '#909399' // 更改坐标轴文字颜色
                }
              },
              axisLine: {
                lineStyle: {
                  color: '#D8DEE7' // 更改坐标轴颜色
                }
              },
              // 网格样式
              splitLine: {
                show: false,
                lineStyle: {
                  color: ['#f5f5f5'],
                  width: 1,
                  type: 'dashed'
                }
              }
            },
            yAxis: {
              type: 'category',
              data: barData.yAxisData,
              axisLabel: {
                formatter: '{value}',
                show: true,
                textStyle: {
                  color: '#909399' // 更改坐标轴文字颜色
                }
              },
              // 控制y轴线是否显示
              axisLine: {
                lineStyle: {
                  color: '#D8DEE7' // 更改坐标轴颜色
                }
              },
              // 去除y轴上的刻度线
              axisTick: {
                show: true
              },
              // 网格样式
              splitLine: {
                show: true,
                lineStyle: {
                  color: ['#f5f5f5'],
                  width: 1,
                  type: 'dashed'
                }
              }
            },
            series: [
              {
                barWidth: 30,
                type: 'bar',
                label: {
                  show: false,
                  position: 'center'
                },
                data: barData.seriesData
              }
            ]
          }
      )
      if (this.projectCode == '713e24b03094410499db0b08a2eccbcc') {
        myChart.setOption({ xAxis: { minInterval: 1 } }) // 不允许出现小数位
      }
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
    drawElevatorBar(barData, chartId, unitName) {
      // 基于准备好的dom，初始化echarts实例
      let seriesData = barData.seriesData
      let yAxisData = barData.yAxisData
      let seriesDataReverse = seriesData.reverse()
      let yAxisDataReverse = yAxisData.reverse()
      let myChart = echarts.init(document.getElementById(chartId))
      myChart.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        animation: false, // 该属性必须加，不然转换后的base64达不到图表的效果
        grid: {
          left: '3%',
          right: '12%',
          bottom: '3%',
          top: '0%',
          containLabel: true
        },
        color: ['#61a5e8'],
        xAxis: {
          name: '单位：' + unitName,
          type: 'value',
          boundaryGap: [0, 0.01],
          axisLabel: {
            show: true,
            textStyle: {
              color: '#909399' // 更改坐标轴文字颜色
            }
          },
          axisLine: {
            lineStyle: {
              color: '#D8DEE7' // 更改坐标轴颜色
            }
          },
          // 网格样式
          splitLine: {
            show: false,
            lineStyle: {
              color: ['#f5f5f5'],
              width: 1,
              type: 'dashed'
            }
          }
        },
        yAxis: {
          type: 'category',
          // data: barData.yAxisData,
          data: yAxisDataReverse,
          axisLabel: {
            formatter: '{value}',
            show: true,
            textStyle: {
              color: '#909399' // 更改坐标轴文字颜色
            }
          },
          // 控制y轴线是否显示
          axisLine: {
            lineStyle: {
              color: '#D8DEE7' // 更改坐标轴颜色
            }
          },
          // 去除y轴上的刻度线
          axisTick: {
            show: true
          },
          // 网格样式
          splitLine: {
            show: true,
            lineStyle: {
              color: ['#f5f5f5'],
              width: 1,
              type: 'dashed'
            }
          }
        },
        series: [
          {
            barWidth: 8,
            type: 'bar',
            label: {
              show: false,
              position: 'center'
            },
            data: seriesDataReverse
          }
        ]
      })
      if (this.$route.query.type == '713e24b03094410499db0b08a2eccbcc') {
        myChart.setOption({ xAxis: { minInterval: 1 } }) // 不允许出现小数位
      }
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
    /**
     * @description: 折线图
     * @param {Obj,str}
     */
    drawLine(barData, chartId) {
      // 基于准备好的dom，初始化echarts实例
      let myChart = echarts.init(document.getElementById(chartId))
      myChart.setOption({
        tooltip: {
          trigger: 'axis'
        },
        animation: false, // 该属性必须加，不然转换后的base64达不到图表的效果
        legend: {
          data: barData.legend.data
        },
        color: ['#37BBF0', '#21CAB5', '#FF796D', '#F787D3', '#FCD442', '#B27BEF', '#5E7BE1', '#F5666D'],
        grid: {
          left: '5%',
          right: '9%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          name: '单位:时',
          type: 'category',
          boundaryGap: false,
          data: barData.lineXAxisData,
          axisLabel: {
            formatter: '{value}',
            show: true,
            textStyle: {
              color: '#909399' // 更改坐标轴文字颜色
            }
          },
          // 控制y轴线是否显示
          axisLine: {
            lineStyle: {
              color: '#D8DEE7' // 更改坐标轴颜色
            }
          },
          // 去除y轴上的刻度线
          axisTick: {
            show: true
          }
        },
        yAxis: {
          name: '单位:m³',
          type: 'value',
          axisLabel: {
            formatter: '{value}',
            show: true,
            textStyle: {
              color: '#909399' // 更改坐标轴文字颜色
            }
          },
          // 控制y轴线是否显示
          axisLine: {
            lineStyle: {
              color: '#D8DEE7' // 更改坐标轴颜色
            }
          },
          // 去除y轴上的刻度线
          axisTick: {
            show: true
          }
        },
        series: barData.lineSeriesData
      })
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
    /**
     * 饼图
     */
    drawpie(data) {
      let lable = []
      let xAxisData = []
      let seriesData = []
      data.map((item) => {
        lable.push(item.name + ' (' + item.value + '件)' + ' ' + item.percent)
        xAxisData.push(item.value)
        seriesData.push(item.name)
      })
      // 基于准备好的dom，初始化echarts实例
      let myChart = echarts.init(document.getElementById('mypreChart'))
      // 绘制图表
      myChart.setOption({
        tooltip: {
          trigger: 'item'
        },
        animation: false, // 该属性必须加，不然转换后的base64达不到图表的效果
        legend: {
          orient: 'vertical',
          left: 'right',
          top: 'center',
          icon: 'circle',
          align: 'left',
          selectedMode: false, // 取消图例上的点击事件
          formatter: function (name) {
            var index = 0
            var clientlabels = seriesData
            var clientcounts = xAxisData
            clientlabels.forEach(function (value, i) {
              if (value == name) {
                index = i
              }
            })
            if (lable.length > 0) {
              return lable[index]
            } else {
              return ''
            }
          }
        },
        color: ['#37BBF0', '#FF796D', '#F787D3', '#FCD442', '#B27BEF', '#5E7BE1', '#21CAB5', '#F5666D'],
        series: [
          {
            name: '',
            type: 'pie',
            radius: '70%',
            center: ['30%', '50%'],
            data: data,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      })
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
    /**
     * 获取echarts的base64
     * @params {chart}  要获取的echarts的id
     */
    getBaseImage(chart) {
      let myChart = echarts.init(document.getElementById(chart))
      let url = myChart.getDataURL({
        type: 'png',
        pixelRatio: 1,
        backgroundColor: '#fff'
      })
      let file = this.base64ToFile(url, '图片')
      // this.$http.downLoadWord({ fileName: file }).then((res) => {
      //   this.$tools.docxdownloadFile(res, this);
      // });
      return file
    },
    // base64转文件流
    base64ToFile(urlData, fileName) {
      let arr = urlData.split(',')
      let mime = arr[0].match(/:(.*?);/)[1]
      let bytes = atob(arr[1]) // 解码base64
      let n = bytes.length
      let ia = new Uint8Array(n)
      while (n--) {
        ia[n] = bytes.charCodeAt(n)
      }
      return new File([ia], fileName, { type: mime })
    },
    init() {
      // this.treeLoading = true;
      if (this.projectCode == '00f4ad80e5c04809aae72c7470e8be29') {
        // 环境
        this.treeData = [
          { id: '1', name: '环境温度统计' },
          { id: '2', name: '环境湿度统计' },
          { id: '3', name: '环境颗粒物统计' }
        ]
      } else if (this.projectCode == '73e7aab447b34971b9ae6d8dae034aa3') {
        //  医用气体
        this.treeData = [
          { id: '4', name: '用氧统计分析' },
          { id: '5', name: '报警统计分析' },
          { id: '6', name: '用氧走势分析' }
          // { id: '10', name: '总用氧分析预警' }
        ]
      } else if (this.projectCode == '713e24b03094410499db0b08a2eccbcc') {
        // 电梯监测
        this.treeData = [
          { id: '7', name: '设备运行统计' },
          { id: '8', name: '报警类型统计' },
          { id: '14', name: '运行时间分析' },
          { id: '11', name: '运行距离分析' },
          { id: '12', name: '开门次数分析' },
          { id: '13', name: '运行楼层分析' }
        ]
      } else if (this.projectCode == 'a6623fbbe453422ecr316cc0b7c69imy') {
        // 污水监测
        this.treeData = [{ id: '9', name: '报警统计分析' }]
      }
      // 默认选中第一个
      this.checkedData = this.treeData[0]
      this.$nextTick(() => {
        this.$refs.tree.setCheckedNodes([this.checkedData])
        this.$refs.tree.setCurrentKey(this.checkedData)
      })
      // 默认选今日
      const end = new Date()
      const start = new Date(new Date().getFullYear(), 0)
      this.filters.daterange = [moment(start).format('YYYY-MM-DD'), moment(end).format('YYYY-MM-DD')]
      // 获取参数
      this.switchingTime('0')
      // this.getStaffList(this.checkedData.id);
    },
    // 获取页面参数
    getStaffList(type) {
      this.loading = true
      let params = {
        startTime: this.filters.daterange[0],
        endTime: this.filters.daterange[1]
      }
      // 运行时长
      if (type == 14) {
        params.parameterIds = '2641'
      }
      // 运行距离
      if (type == 11) {
        params.parameterIds = '2639'
      }
      // 开门次数
      if (type == 12) {
        params.parameterIds = '2634'
      }
      if (type == '1' || type == '2' || type == '3') {
        this.tableTitle = this.header.oneTableList
        this.$api
          .getParameterCensus(
            {
              projectCode: this.projectCode,
              parameterId: this.checkedData.id,
              startTime: this.filters.daterange[0],
              endTime: this.filters.daterange[1]
            },
            this.requestHttp
          )
          .then((res) => {
            this.loading = false
            this.taskTable = res.data
          })
      } else if (type == '4') {
        // 医用气体-用氧统计分析
        this.tableTitle = this.header.twoTableList
        this.$api.getOxygenUseStat(params, this.requestHttp).then((res) => {
          this.loading = false
          this.taskTable = res.code == 200 ? res.data : []
        })
        this.$api.getOxygenUseColumnStat(params, this.requestHttp).then((res) => {
          this.drawBar(res.data, 'riskTypeBar')
        })
      } else if (type == '5') {
        // 医用气体-报警统计分析
        this.tableTitle = this.header.sanTableList
        this.tableTitleTwo = this.header.siTableList
        this.$api.getStaAsPolice(params, this.requestHttp).then((res) => {
          this.loading = false
          this.taskTable = res.data.policeListAsMonitor
          this.taskTableTwo = res.data.policeListAsType
        })
      } else if (type == '6') {
        // 医用气体-用氧走势分析
        let surveyCode = this.filters.surveyCode.length > 0 ? this.filters.surveyCode.join(',') : ''
        this.$api
          .getOxygenUseTrendStat(
            {
              timeFlag: this.activeTitle,
              surveyCode: surveyCode
            },
            this.requestHttp
          )
          .then((res) => {
            this.loading = false
            this.tableTitle = res.data.tableHeader
            this.taskTable = res.data.tableData
            this.drawLine(res.data, 'riskTypeline')
          })
      } else if (type == '7') {
        // 电梯监测-设备运行统计
        this.$api.getDeviceOperationStatistic(params, this.requestHttp).then((res) => {
          this.loading = false
          var operationHear = [
            {
              label: '设备名称',
              value: 'surveyName'
            },
            {
              label: '报警次数(次)',
              value: 'policeCount'
            },
            {
              label: '运行时间',
              value: 'operationCount'
            },
            {
              label: '运行距离',
              value: 'operationDistanceCount'
            },
            {
              label: '开关门次数',
              value: 'operationOpenCount'
            },
            {
              label: '运行楼层',
              value: 'floorCount'
            },
            {
              projectName: '设备运行分析'
            }
          ]
          //   var resultData = [{"operationCount":"31.31","surveyName":"3号楼05号","operationDistanceCount":"142927","policeCount":"0","floorCount":"48507","ask":3,"operationOpenCount":"8891"}, {"operationCount":"18.35","surveyName":"3号楼01号","operationDistanceCount":"84835","policeCount":"0","floorCount":"25019","ask":3,"operationOpenCount":"3361"}, {"operationCount":"51.3","surveyName":"3号楼04号","operationDistanceCount":"208726","policeCount":"0","floorCount":"39920","ask":3,"operationOpenCount":"18823"}, {"operationCount":"0","surveyName":"3号楼07号","operationDistanceCount":"0","policeCount":"0","floorCount":"0","ask":3,"operationOpenCount":"0"}, {"operationCount":"25.08","surveyName":"3号楼03号","operationDistanceCount":"74409","policeCount":"0","floorCount":"31047","ask":3,"operationOpenCount":"9259"}, {"operationCount":"57.86","surveyName":"3号楼06号","operationDistanceCount":"232552","policeCount":"0","floorCount":"47949","ask":3,"operationOpenCount":"17398"}, {"operationCount":"22.07","surveyName":"3号楼02号","operationDistanceCount":"93794","policeCount":"0","floorCount":"28810","ask":3,"operationOpenCount":"5229"}, {"operationCount":"0","surveyName":"5号楼8号梯","operationDistanceCount":"0","policeCount":"28","floorCount":"0","ask":5,"operationOpenCount":"0"}, {"operationCount":"0","surveyName":"5号楼9号","operationDistanceCount":"0","policeCount":"4","floorCount":"0","ask":5,"operationOpenCount":"0"}, {"operationCount":"0","surveyName":"5号楼8号","operationDistanceCount":"0","policeCount":"10","floorCount":"0","ask":5,"operationOpenCount":"0"}, {"operationCount":"74.38","surveyName":"5号楼08号","operationDistanceCount":"252290","policeCount":"0","floorCount":"82459","ask":5,"operationOpenCount":"34819"}, {"operationCount":"11.23","surveyName":"5号楼09号","operationDistanceCount":"30292","policeCount":"0","floorCount":"10115","ask":5,"operationOpenCount":"4120"}, {"operationCount":"367.22","surveyName":"8号楼11号","operationDistanceCount":"16756","policeCount":"105","floorCount":"20699","ask":8,"operationOpenCount":"22236"}, {"operationCount":"54.69","surveyName":"8号楼10号","operationDistanceCount":"191069","policeCount":"89","floorCount":"58120","ask":8,"operationOpenCount":"23713"}, {"operationCount":"0","surveyName":"单层电梯","operationDistanceCount":"0","policeCount":"2","floorCount":"0","ask":13,"operationOpenCount":"0"}, {"operationCount":"71.25","surveyName":"康复楼04号","operationDistanceCount":"227837","policeCount":"0","floorCount":"78061","ask":20,"operationOpenCount":"19441"}, {"operationCount":"0","surveyName":"康复楼3号","operationDistanceCount":"0","policeCount":"10","floorCount":"0","ask":20,"operationOpenCount":"0"}, {"operationCount":"46.4","surveyName":"康复楼03号","operationDistanceCount":"73292","policeCount":"0","floorCount":"39447","ask":20,"operationOpenCount":"10774"}, {"operationCount":"0","surveyName":"康复楼2号","operationDistanceCount":"0","policeCount":"4","floorCount":"0","ask":20,"operationOpenCount":"0"}, {"operationCount":"0","surveyName":"康复5号","operationDistanceCount":"0","policeCount":"18","floorCount":"0","ask":20,"operationOpenCount":"0"}, {"operationCount":"146.28","surveyName":"康复楼02号","operationDistanceCount":"426569","policeCount":"0","floorCount":"163544","ask":20,"operationOpenCount":"34384"}, {"operationCount":"0","surveyName":"康复楼5号","operationDistanceCount":"0","policeCount":"4","floorCount":"0","ask":20,"operationOpenCount":"0"}, {"operationCount":"0","surveyName":"康复楼1号","operationDistanceCount":"0","policeCount":"11","floorCount":"0","ask":20,"operationOpenCount":"0"}, {"operationCount":"38.73","surveyName":"康复楼05号","operationDistanceCount":"134068","policeCount":"0","floorCount":"44151","ask":20,"operationOpenCount":"9700"}, {"operationCount":"0","surveyName":"康复楼4号","operationDistanceCount":"0","policeCount":"2","floorCount":"0","ask":20,"operationOpenCount":"0"}, {"operationCount":"129.2","surveyName":"康复楼01号","operationDistanceCount":"436763","policeCount":"0","floorCount":"142773","ask":20,"operationOpenCount":"31615"}, {"operationCount":"20.24","surveyName":"门诊01号","operationDistanceCount":"69912","policeCount":"6","floorCount":"13340","ask":22,"operationOpenCount":"11700"}, {"operationCount":"0","surveyName":"门诊09号","operationDistanceCount":"0","policeCount":"6","floorCount":"0","ask":22,"operationOpenCount":"0"}, {"operationCount":"25.28","surveyName":"门诊04号","operationDistanceCount":"86681","policeCount":"6","floorCount":"14881","ask":22,"operationOpenCount":"11307"}, {"operationCount":"23.48","surveyName":"门诊07号梯","operationDistanceCount":"80418","policeCount":"6","floorCount":"14021","ask":22,"operationOpenCount":"6890"}, {"operationCount":"42.7","surveyName":"门诊03号","operationDistanceCount":"61059","policeCount":"10","floorCount":"13225","ask":22,"operationOpenCount":"12176"}, {"operationCount":"2","surveyName":"门诊10号","operationDistanceCount":"4176","policeCount":"6","floorCount":"554","ask":22,"operationOpenCount":"1632"}, {"operationCount":"19.78","surveyName":"门诊02号","operationDistanceCount":"58607","policeCount":"6","floorCount":"11266","ask":22,"operationOpenCount":"9906"}, {"operationCount":"31.06","surveyName":"门诊08号梯","operationDistanceCount":"104195","policeCount":"0","floorCount":"17263","ask":22,"operationOpenCount":"11619"}, {"operationCount":"7742963.42","surveyName":"门诊09号电梯","operationDistanceCount":"351644","policeCount":"0","floorCount":"3607","ask":22,"operationOpenCount":"138055"}, {"operationCount":"0","surveyName":"双层电梯","operationDistanceCount":"0","policeCount":"4","floorCount":"0","ask":28,"operationOpenCount":"0"}, {"operationCount":"27.26","surveyName":"体检02号","operationDistanceCount":"47678","policeCount":"2575","floorCount":"15696","ask":29,"operationOpenCount":"9808"}, {"operationCount":"29.62","surveyName":"体检01号","operationDistanceCount":"50344","policeCount":"739","floorCount":"16780","ask":29,"operationOpenCount":"10151"}, {"operationCount":"40335.28","surveyName":"行政楼6号","operationDistanceCount":"142611515","policeCount":"2004","floorCount":"525444","ask":33,"operationOpenCount":"6253356"}, {"operationCount":"0","surveyName":"行政楼6号双层电梯","operationDistanceCount":"0","policeCount":"1788","floorCount":"0","ask":33,"operationOpenCount":"0"}, {"operationCount":"300.69","surveyName":"行政楼5号","operationDistanceCount":"928738","policeCount":"370","floorCount":"326005","ask":33,"operationOpenCount":"65167"}, {"operationCount":"0","surveyName":"行政楼5号单层电梯","operationDistanceCount":"0","policeCount":"1189","floorCount":"0","ask":33,"operationOpenCount":"0"}, {"operationCount":"0","surveyName":"总运行距离","operationDistanceCount":"147081146","policeCount":"0","floorCount":"0","ask":35,"operationOpenCount":"0"}, {"operationCount":"0","surveyName":"总开门次数","operationDistanceCount":"0","policeCount":"0","floorCount":"0","ask":35,"operationOpenCount":"6795530"}, {"operationCount":"0","surveyName":"平均","operationDistanceCount":"0","policeCount":"0","floorCount":"65453.68","operationOpenCount":"0"}, {"operationCount":"7784966.16","surveyName":"总计","operationDistanceCount":"0","policeCount":"0","floorCount":"0","operationOpenCount":"0"}]
          this.tableTitle = operationHear
          console.log(this.tableTitle)
          this.itemList = operationHear.filter((v) => v.value)
          this.selectItems = this.itemList.map((v) => v.label)
          this.taskTable = res.data
          // this.drawBar(res.data.columnAsCount, 'riskTypeBar')
        })
      } else if (type == '8') {
        // 电梯监测-报警类型计
        this.$api
          .getStatisticAsPoliceCount(
            {
              startTime: this.filters.daterange[0],
              endTime: this.filters.daterange[1]
            },
            this.requestHttp
          )
          .then((res) => {
            this.loading = false
            this.tableTitle = res.data.tableData.header
            this.itemList = res.data.tableData.header.filter((v) => v.value)
            this.selectItems = this.itemList.map((v) => v.label)
            this.taskTable = res.data.tableData.list
            // this.drawLine(res.data.columnAsType, "riskTypeline");
            // this.drawBar(res.data.columnAsType, 'riskTypeBar')
            this.drawpie(res.data.pieAsType)
            // if(res.data.length>0){
            //   this.pieNullimg = false;
            // }else{
            //   this.pieNullimg = true;
            // }
          })
      } else if (type == '9') {
        // 电梯监测-报警类型计
        let tableData = {
          header: [
            {
              label: '监测项名称',
              value: 'surveyName'
            },
            {
              label: '报警总数',
              value: 'count'
            }
          ],
          list: [
            {
              surveyName: '1#消毒池',
              count: '100'
            },
            {
              surveyName: '2#消毒池',
              count: '90'
            },
            {
              surveyName: '3#消毒池',
              count: '80'
            }
          ]
        }
        this.loading = false
        this.tableTitle = tableData.header
        this.taskTable = tableData.list
      } else if (type == '10') {
        this.tableTitle = this.header.threeTableList
        this.$api.GetSouthTowerBOneOxygen({ ...params, timeFlag: this.activeTitle }).then((res) => {
          if (res.code == 200) {
            this.loading = false
            this.taskTable = res.data
          }
        })
        this.$api.GetSouthTowerBOneHistogram({ ...params, timeFlag: this.activeTitle }).then((res) => {
          this.drawBar(res.data[0], 'riskTypeBar')
        })
      } else if (type == '14') {
        // 电梯监测-运行时长
        this.$api.getElevatorStatisticAsMonitor(params, this.requestHttp).then((res) => {
          this.loading = false
          this.tableTitle = res.data.tableData.header
          this.itemList = res.data.tableData.header.filter((v) => v.value)
          this.selectItems = this.itemList.map((v) => v.label)
          this.taskTable = res.data.tableData.list
          this.drawElevatorBar(res.data.columnAsCount, 'riskTypeBar', '小时')
        })
      } else if (type == '11') {
        // 电梯监测-运行距离
        this.$api.getElevatorStatisticAsMonitor(params, this.requestHttp).then((res) => {
          this.loading = false
          this.tableTitle = res.data.tableData.header
          this.itemList = res.data.tableData.header.filter((v) => v.value)
          this.selectItems = this.itemList.map((v) => v.label)
          this.taskTable = res.data.tableData.list
          this.drawElevatorBar(res.data.columnAsCount, 'riskTypeBar', '米')
        })
      } else if (type == '12') {
        // 电梯监测-开门次数
        this.$api.getElevatorStatisticAsMonitor(params, this.requestHttp).then((res) => {
          this.loading = false
          this.tableTitle = res.data.tableData.header
          this.itemList = res.data.tableData.header.filter((v) => v.value)
          this.selectItems = this.itemList.map((v) => v.label)
          this.taskTable = res.data.tableData.list
          this.drawElevatorBar(res.data.columnAsCount, 'riskTypeBar', '次')
        })
      } else if (type == '13') {
        // 电梯监测-运行楼层
        this.$api.getElevatorFloorStatisticAsMonitor(params, this.requestHttp).then((res) => {
          this.loading = false
          this.tableTitle = res.data.tableData.header
          this.itemList = res.data.tableData.header.filter((v) => v.value)
          this.selectItems = this.itemList.map((v) => v.label)
          this.taskTable = res.data.tableData.list
          this.drawElevatorBar(res.data.columnAsCount, 'riskTypeBar', '层')
        })
      }
    },
    // 重置
    reset() {
      this.filters.surveyCode = []
      this.switchingTime('0')
    },
    // 查询
    search() {
      this.getStaffList(this.checkedData.id)
    },
    // 按钮查询
    switchingTime(type) {
      this.activeTitle = type
      if (this.checkedData.id != 10) {
        if (type == 0) {
          // 日
          this.filters.daterange = [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
        } else if (type == 1) {
          // 月
          const end = new Date()
          const start = new Date()
          start.setDate(1)
          this.filters.daterange = [moment(start).format('YYYY-MM-DD'), moment(end).format('YYYY-MM-DD')]
        } else if (type == 2) {
          // 年
          const end = new Date()
          const start = new Date(new Date().getFullYear(), 0)
          this.filters.daterange = [moment(start).format('YYYY-MM-DD'), moment(end).format('YYYY-MM-DD')]
        }
      } else {
        if (type == 0) {
          this.filters.daterange = [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
        } else if (type == 1) {
          this.filters.daterange = [moment().startOf('week').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
        } else if (type == 2) {
          this.filters.daterange = [moment().startOf('month').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
        } else if (type == 3) {
          this.filters.daterange = [moment().startOf('year').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
        }
      }
      this.getStaffList(this.checkedData.id)
    },
    // 导出
    exportClickExport() {
      let name = ''
      if (this.checkedData.id == '4') {
        name = '用氧统计分析'
      } else if (this.checkedData.id == '5') {
        name = '报警统计分析'
      } else if (this.checkedData.id == '6') {
        name = '用氧走势分析'
      } else if (this.checkedData.id == '10') {
        name = '总用氧分析预警'
      }
      // downloadPDF(
      //   document.querySelector('#pdf'),
      //   name + moment().format('YYYY-MM-DD')
      // )
      // return
      this.loading = true
      if (this.checkedData.id == 4) {
        // 0用氧统计分析
        this.$api
          .getOxygenUseStatToExcel(
            {
              startTime: this.filters.daterange[0],
              endTime: this.filters.daterange[1]
            },
            this.requestHttp
          )
          .then((res) => {
            this.loading = false
            this.$tools.downloadFile(res, '用氧统计分析')
          })
        // this.$api.gasesStatisticExportWord({
        //   flag: '0',
        //   file: this.getBaseImage('riskTypeBar'),
        //   startTime: this.filters.daterange[0],
        //   endTime: this.filters.daterange[1]
        // }, this.requestHttp
        // ).then((res) => {
        //   this.loading = false
        //   this.$tools.docxdownloadFile(res, '用氧统计分析')
        // })
      } else if (this.checkedData.id == 5) {
        //  1报警统计分析
        this.$api
          .gasesStatisticExportWord(
            {
              flag: '1',
              startTime: this.filters.daterange[0],
              endTime: this.filters.daterange[1]
              // file:''
            },
            this.requestHttp
          )
          .then((res) => {
            this.loading = false
            this.$tools.docxdownloadFile(res, '报警统计分析')
          })
      } else if (this.checkedData.id == 6) {
        //  2.用氧走势分析
        this.$api
          .gasesStatisticExportWord(
            {
              flag: '2',
              timeFlag: this.activeTitle,
              file: this.getBaseImage('riskTypeline'),
              surveyCode: ''
            },
            this.requestHttp
          )
          .then((res) => {
            this.loading = false
            this.$tools.docxdownloadFile(res, '用氧走势分析')
          })
      } else if (this.checkedData.id == 10) {
        this.$api
          .gasesStatisticExportWord(
            {
              flag: '3',
              startTime: this.filters.daterange[0],
              endTime: this.filters.daterange[1]
            },
            this.requestHttp
          )
          .then((res) => {
            this.loading = false
            this.$tools.docxdownloadFile(res, '总用氧分析预警')
          })
      }
    },
    // 电梯导出
    exportElevator() {
      let name = ''
      if (this.checkedData.id == '7') {
        name = '设备运行统计'
      } else if (this.checkedData.id == '8') {
        name = '报警类型统计'
      } else if (this.checkedData.id == '11') {
        name = '运行距离分析'
      } else if (this.checkedData.id == '12') {
        name = '开门次数分析'
      } else if (this.checkedData.id == '13') {
        name = '运行楼层分析'
      } else if (this.checkedData.id == '14') {
        name = '运行时间分析'
      }
      downloadPDF(document.querySelector('#pdf'), name + moment().format('YYYY-MM-DD'))
    },
    //
    dateChange() {
      this.activeTitle = ''
    },
    // 电梯报警类型统计的 报警类别数值点击过滤事件
    jumpAlarmTypeDetail(alarmData) {
      this.$router.push({
        path: '/elevatorMenu/elevatorAlarmCenter',
        query: {
          parameterId: alarmData.paramId,
          surveyName: alarmData.surveyName,
          daterange: this.filters.daterange
        }
      })
    },
    treeChecked(data, checked) {
      this.checkedData = data
      this.$refs.tree.setCheckedNodes([data])
      this.$refs.tree.setCurrentKey(this.checkedData)
    },
    // 树状图点击
    handleNodeClick(data, checked) {
      if (data.id === '12') {
        this.showOpenDoor = true
        this.showRunFloor = false
      } else if (data.id === '13') {
        this.showRunFloor = true
        this.showOpenDoor = false
      } else {
        this.showOpenDoor = false
        this.showRunFloor = false
        this.paginationData.currentPage = 1
        this.checkedData = data
        this.$refs.tree.setCheckedNodes([data])
        this.$refs.tree.setCurrentKey(this.checkedData)
        if (this.checkedData.id != 10) {
          this.dateList = [
            { name: '今日', id: 0 },
            { name: '本月', id: 1 },
            { name: '本年', id: 2 }
          ]
        } else {
          this.dateList = [
            { name: '今日', id: 0 },
            { name: '本周', id: 1 },
            { name: '本月', id: 2 },
            { name: '本年', id: 3 }
          ]
        }
        this.reset()
      }
    },
    headerStyle(type) {
      return { background: '#f2f4fbd1', height: ' 20px' }
    },
    //  ----------------------------------------------------分页相关------------------------------------------------------------
    handleSizeChange(val) {
      this.paginationData.currentPage = 1
      this.paginationData.pageSize = val
      this.getStaffList()
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.getStaffList()
    },
    //  ----------------------------------------------------树状结构相关---------------------------------------------------------
    //  ------------------------------------------------操作表格中数据------------------------------------------------------------
    handleSelectionChange(val) {
      this.multipleSelection = val
    }
  }
}
</script>
<style lang="scss" scoped>
.monitor-content {
  height: 100%;
  display: flex;
  ::v-deep .monitor-content-left {
    width: 246px;
    height: 100%;
    background: #fff;
    border-radius: 4px;
    padding: 16px;
    overflow: auto;
    .el-tree {
      height: 100%;
    }
    .el-tree-node {
      .el-tree-node__content {
        padding: 6px 0;
        height: auto;
      }
    }
    .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
      background-color: #d9e1f8;
    }
  }
  .monitor-content-right {
    height: 100%;
    flex: 1;
    flex-direction: column;
    overflow: auto;
    margin-left: 16px;
    display: flex;
    .right-heade {
      border-radius: 4px;
      background: #fff;
      padding: 10px !important;
      .search-from {
        & > div {
          margin-right: 10px;
        }
      }
      .batch-control {
        & > button {
          margin-top: 12px;
          margin-right: 10px;
          margin-left: 0;
        }
      }
    }
    .right-content {
      padding: 16px;
      flex: 1;
      overflow: auto;
      margin-top: 16px;
      background: #fff;
    }
  }
}
.right {
  width: calc(100% - 270px);
  height: calc(100% - 20px);
  text-align: left;
  background-color: #fff;
  padding-left: 30%;
  box-sizing: border-box;
  padding: 20px 26px;
  margin: 10px 10px 10px 0;
  border-radius: 10px;
  overflow-y: auto;
  padding-bottom: 0;
  .top_filters {
    input,
    select {
      margin-right: 15px;
      width: 200px;
      height: 40px;
    }
    .search_button {
      background-color: #2cc7c5;
      color: #fff;
    }
    .search_button:hover {
      opacity: 0.7;
    }
  }
  .middle_tools {
    margin-bottom: 10px;
  }
}
.disabled-button {
  background-color: #77affd;
  border-color: #77affd;
}
.sure:focus {
  background-color: #5288fc;
  border-color: #5288fc;
  color: #fff;
}
</style>
<style lang="scss">
.doubleThTable {
  .el-table thead.is-group th {
    background: none;
    padding: 0;
  }
  .el-table thead.is-group tr:first-of-type th:first-of-type {
    border-bottom: none;
  }
  .el-table thead.is-group tr:first-of-type th:first-of-type div.cell {
    text-align: right;
  }
  .el-table thead.is-group tr:last-of-type th:first-of-type div.cell {
    text-align: left;
  }
  .el-table thead.is-group tr:first-of-type th:first-of-type::before {
    content: '';
    position: absolute;
    width: 1px;
    // height: 55px;
    height: 200px; //自行调整
    top: 0;
    left: 0;
    background-color: grey;
    opacity: 0.2;
    display: block;
    // transform: rotate(-43deg);
    transform: rotate(-55deg); //自行调整
    transform-origin: top;
    transform-origin: top;
  }
  .el-table thead.is-group tr:last-of-type th:first-of-type::before {
    content: '';
    position: absolute;
    width: 1px;
    // height: 60px;
    height: 200px; //自行调整
    bottom: 0;
    right: 0;
    background-color: grey;
    opacity: 0.2;
    display: block;
    // transform: rotate(-45deg);  //自行调整
    transform: rotate(-55deg);
    transform-origin: bottom;
    transform-origin: bottom;
  }
}
</style>
