<template>
  <div class="aside-box">
    <div v-loading="data.loading" class="aside-box-top">
      <div class="box-top-left">
        <svg-icon :name="data.titleIcon" />
        <div>
          <div class="box-top-left-title">
            {{ data.title }}
            <span v-if="data.ratedThan > 100" class="overrun">超限</span>
          </div>
          <div class="box-top-left-value">{{ data.value | overFixedFilter }}</div>
        </div>
      </div>
      <div class="box-top-right">
        <div class="" style="height: 100%">
          <div class="aside-box-center">
            <el-progress :percentage="data.ratedThan > 100 ? 100 : data.ratedThan" color="#3562DB" define-back-color="#CCCED3" :stroke-width="10" :show-text="false"></el-progress>
          </div>
          <div class="aside-box-bottom">
            <!-- <div><span>已用</span><span :style="{color: '#3562DB'}">{{data.timePercent}}</span></div> -->
            <div>
              <span>已用</span><span :style="{ color: data.ratedThan > 100 ? '#FA403C' : '#3562DB' }">{{ data.ratedThan }}</span
              ><span>%</span>
            </div>
            <div>
              <span>{{ data.useText }}</span
              ><span>{{ data.name == '电力' ? data.rated : '-' }}</span
              ><span>{{ data.unit }}</span>
            </div>
          </div>
          <div :id="data.echartsDOM" style="height: calc(100% - 45px)"></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
export default {
  name: 'AsideBox',
  filters: {
    // 如果返回值大于1000 数值显示为万
    overFixedFilter(value) {
      if (value > 1000) {
        return (value / 10000).toFixed(2) + '万'
      } else {
        return value
      }
    }
  },
  props: {
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      progressColor: {
        initial: '#CCCED3',
        normal: '#3562DB',
        overflow: '#FF6461'
      }
    }
  },
  watch: {
    'data.echartsData': {
      handler(val) {
        this.setEcharts(this.data.echartsDOM, val)
      },
      deep: true
    },
    '$store.state.settings.sidebarCollapse': {
      handler(val) {
        this.$nextTick(() => {
          setTimeout(() => {
            echarts.init(document.getElementById(this.data.echartsDOM)).resize()
          }, 250)
        })
      },
      deep: true
    },
    '$store.state.settings.dragSidebarCollapse': {
      handler(val) {
        this.$nextTick(() => {
          setTimeout(() => {
            echarts.init(document.getElementById(this.data.echartsDOM)).resize()
          }, 250)
        })
      },
      deep: true
    }
  },
  mounted() {},
  methods: {
    // echarts 渲染
    setEcharts(dom, data) {
      const getchart = echarts.init(document.getElementById(dom))
      let sortedData = data
        .map((ele) => ele.value)
        .sort((a, b) => {
          return b - a
        })
      let option
      if (data.length) {
        option = {
          xAxis: {
            type: 'category',
            show: true,
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            }
          },
          yAxis: {
            type: 'value',
            show: true
          },
          grid: {
            top: '8%',
            left: '18%',
            right: '5%',
            bottom: '20%'
          },
          series: [
            {
              // 数值在前五的标红
              data: data.map((item, index) => {
                if (sortedData.indexOf(item.value) <= 4) {
                  return { value: item.value, itemStyle: { color: '#FF6461' } }
                } else {
                  return { value: item.value, itemStyle: { color: '#3562DB' } }
                }
              }),
              // data: [
              //   120,
              //   {
              //     value: 200,
              //     itemStyle: {
              //       color: '#FF6461'
              //     }
              //   },
              //   150,
              //   80,
              //   70,
              //   110,
              //   {
              //     value: 130,
              //     itemStyle: {
              //       color: '#3562DB'
              //     }
              //   }
              // ],
              type: 'bar',
              markLine: {
                silent: true,
                symbol: 'none',
                label: {
                  show: false
                },
                data: [
                  {
                    name: '',
                    yAxis: 80,
                    lineStyle: {
                      color: '#FAE6E8',
                      type: 'solid',
                      width: 2
                    }
                  }
                ]
              }
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.aside-box {
  height: 50%;
  background: #faf9fc;
  border-radius: 4px;
  padding: 13px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-content: center;
  justify-content: space-between;
  .aside-box-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    .box-top-left {
      display: flex;
      align-items: center;
      min-width: 35%;
      max-width: 40%;
      height: 100%;
      svg {
        font-size: 54px;
        margin-right: 10px;
      }
      > div {
        height: 100%;
        display: flex;
        justify-content: center;
        flex-direction: column;
        .box-top-left-title {
          font-size: 14px;
          font-family: 'PingFang SC-Regular', 'PingFang SC';
          font-weight: 400;
          color: #666666;
          .overrun {
            background: #fa403c;
            border-radius: 2px;
            font-size: 14px;
            font-weight: 500;
            color: #ffffff;
            padding: 2px 5px;
          }
        }
        .box-top-left-value {
          font-size: 30px;
          font-family: Arial-Bold, Arial;
          font-weight: 600;
          color: #121f3e;
          // FA403C
        }
      }
    }
    .box-top-right {
      // flex: 1;
      height: 100%;
      width: 60%;
      > div {
        width: 100%;
        height: 100%;
        z-index: 2;
      }
    }
  }
  .aside-box-center {
    height: 10%;
  }
  .aside-box-bottom {
    // height: 10%;
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    > div {
      display: flex;
      align-items: center;
      > span {
        font-size: 14px;
        font-family: 'PingFang SC-Regular', 'PingFang SC';
        font-weight: 400;
        color: #121f3e;
        &:nth-child(2) {
          margin: 0 5px;
          font-size: 18px;
          font-family: Arial-Bold, Arial;
          font-weight: bold;
        }
        &:nth-child(3) {
          font-size: 15px;
          font-family: 'PingFang SC-Medium', 'PingFang SC';
          font-weight: 500;
          color: #ccced3;
        }
      }
    }
  }
}
</style>
