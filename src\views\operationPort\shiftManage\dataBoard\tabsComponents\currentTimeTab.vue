<template>
  <PageContainer class="currentTimeTab">
    <template #content>
      <div class="currentTimeTab__card">
        <div class="card_box">
          <div id="dutyAttendanceEchart" style="width: 100%; height: 100%"></div>
        </div>
        <div class="card_box">
          <div id="dutyPersonEchart" style="width: 100%; height: 100%"></div>
        </div>
        <div class="card_box">
          <div id="postDistributionEchart" style="width: 100%; height: 100%"></div>
        </div>
      </div>
      <div class="currentTimeTab__search">
        <el-form ref="formRef" :model="searchForm" inline @submit.native.prevent="name">
          <el-form-item prop="name">
            <el-input v-model="searchForm.name" placeholder="搜索值班考勤组名称" suffix-icon="el-icon-search"> </el-input>
          </el-form-item>
          <el-form-item prop="status" style="margin-right: 0px">
            <el-select v-model="searchForm.status" placeholder="全部状态">
              <el-option label="正常" :value="0"></el-option>
              <el-option label="异常" :value="1"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" plain @click="onReset">重置</el-button>
        </div>
      </div>
      <div class="currentTimeTab__table">
        <el-table v-loading="tableLoadingStatus" height="400" :data="tableData" border stripe table-layout="auto" class="tableAuto" row-key="id">
          <el-table-column prop="dutyAttendanceConfigName" label="值班考勤组名称" show-overflow-tooltip></el-table-column>
          <el-table-column prop="shiftRule" label="类型" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ row.shiftRule === 1 ? '按天循环' : row.shiftRule === 2 ? '按周循环' : '' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="signTimeStr" label="当前班次" show-overflow-tooltip></el-table-column>
          <el-table-column prop="shiftManagementName" label="值班岗" show-overflow-tooltip></el-table-column>
          <el-table-column prop="actualCount" label="实际在岗人数" show-overflow-tooltip></el-table-column>
          <el-table-column prop="dutyAttendanceStatus" label="状态" show-overflow-tooltip>
            <template #default="{ row }">
              <span class="currentTimeTab__tag" :class="`currentTimeTab__tag--${row.dutyAttendanceStatus}`">
                {{ row.dutyAttendanceStatus === 0 ? '正常' : '异常' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150px">
            <template #default="{ row }">
              <el-button type="text" @click="onOperate('view', row)">查看详情</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          class="currentTimeTab__pagination"
          :current-page="pagination.page"
          :page-sizes="pagination.pageSizeOptions"
          :page-size="pagination.size"
          :layout="pagination.layoutOptions"
          :total="pagination.total"
          @size-change="paginationSizeChange"
          @current-change="paginationCurrentChange"
        >
        </el-pagination>
      </div>
    </template>
  </PageContainer>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin'
import * as echarts from 'echarts'
export default {
  name: 'currentTimeTab',
  mixins: [tableListMixin],
  data() {
    return {
      dept: '',
      searchForm: {
        name: '', // 名称
        status: '' // 状态
      },
      deptOptions: [], // 下拉部门
      tableData: [],
      tableLoadingStatus: false
    }
  },
  mounted() {
    this.init()
    this.getDataList()
  },
  methods: {
    // 初始化
    init() {
      // 值班考勤分布
      this.getDutyAttendanceData()
      // 在岗人数
      this.getDutyPersonData()
      // 岗位分布
      this.getPostDistributionData()
    },
    // 值班考勤分布
    getDutyAttendanceData() {
      this.$api.supplierAssess.getSignGroupConutData({}).then((res) => {
        if (res.code === '200') {
          this.getAnnularChart(res.data)
        }
      })
    },
    // 值班考勤分布-图表数据填充
    getAnnularChart(obj) {
      let pieData = [
        { value: obj.normalGroup, name: '正常组数', percentage: obj.normalProportion },
        { value: obj.abnormalGroup, name: '异常组数', percentage: obj.abnormalProportion }
      ]
      // 基于准备好的dom，初始化echarts实例
      let getchart = echarts.init(document.getElementById('dutyAttendanceEchart'))
      let option = {}
      if (pieData?.length > 0) {
        option = {
          // 你的代码
          title: [
            {
              text: '当前值班考勤组情况',
              left: '4%',
              top: '4%',
              textStyle: {
                color: '#333333', // 标题颜色
                fontWeight: 'bold',
                fontSize: 16
              }
            },
            {
              text: `共${obj.total}组`,
              top: '54%',
              left: '22%',
              textStyle: {
                color: '#080404',
                fontSize: 16,
                fontWeight: '400'
              }
            }
          ],
          tooltip: {
            trigger: 'item'
          },
          legend: {
            orient: 'vertical',
            right: '20%',
            top: '45%',
            icon: 'circle',
            itemWidth: 10,
            itemHeight: 10,
            itemGap: 15,
            // 显示 省份占比
            formatter(name) {
              const item = pieData.filter((item) => item.name === name)[0]
              return `${name}  ${item.value}  ${item.percentage + '%'}`
            }
          },
          color: ['#50B042', '#FF6461'],
          series: [
            {
              name: '',
              type: 'pie',
              center: ['27%', '60%'],
              radius: ['48%', '60%'],
              avoidLabelOverlap: false,
              label: {
                show: false,
                position: 'center'
              },
              labelLine: {
                show: false
              },
              data: pieData
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            left: 'center',
            top: 'center',
            textStyle: {
              color: '#000',
              fontSize: 16
            }
          }
        }
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 在岗人数
    getDutyPersonData() {
      this.$api.supplierAssess.getOnDutyCounData({}).then((res) => {
        if (res.code === '200') {
          this.getAnnularChart1(res.data)
        }
      })
    },
    // 在岗人数-图表数据填充
    getAnnularChart1(obj) {
      let pieData = [
        { value: obj.normalGroup, name: '在岗人数', percentage: obj.normalProportion },
        { value: obj.abnormalGroup, name: '不在岗人数', percentage: obj.abnormalProportion }
      ]
      // 基于准备好的dom，初始化echarts实例
      let getchart = echarts.init(document.getElementById('dutyPersonEchart'))
      let option = {}
      if (pieData?.length > 0) {
        option = {
          // 你的代码
          title: [
            {
              text: '当前在岗人数情况',
              left: '4%',
              top: '4%',
              textStyle: {
                color: '#333333', // 标题颜色
                fontWeight: 'bold',
                fontSize: 16
              }
            },
            {
              text: `要求${obj.total}人`,
              top: '54%',
              left: '21%',
              textStyle: {
                color: '#080404',
                fontSize: 16,
                fontWeight: '400'
              }
            }
          ],
          tooltip: {
            trigger: 'item'
          },
          legend: {
            orient: 'vertical',
            right: '16%',
            top: '40%',
            icon: 'circle',
            itemWidth: 10,
            itemHeight: 10,
            itemGap: 15,
            // 显示 省份占比
            formatter(name) {
              const item = pieData.filter((item) => item.name === name)[0]
              return `${name}  ${item.value}  ${item.percentage + '%'}`
            }
          },
          color: ['#50B042', '#FF6461'],
          series: [
            {
              name: '',
              type: 'pie',
              center: ['27%', '60%'],
              radius: ['48%', '60%'],
              avoidLabelOverlap: false,
              label: {
                show: false,
                position: 'center'
              },
              labelLine: {
                show: false
              },
              data: pieData
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            left: 'center',
            top: 'center',
            textStyle: {
              color: '#000',
              fontSize: 16
            }
          }
        }
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 岗位分布（实际/要求）
    getPostDistributionData() {
      this.$api.supplierAssess.getPostCountData({}).then((res) => {
        if (res.code === '200') {
          if (res.data && Array.isArray(res.data)) {
            this.getVerticalBarChart(res.data)
          } else {
            this.getVerticalBarChart([])
          }
        }
      })
    },
    // 岗位分布（实际/要求）-图表数据填充
    getVerticalBarChart(arr) {
      let xData = arr.map((item) => {
        return item.postName
      })
      let yData = arr.map((item) => {
        return item.actualCount
      })
      let yData1 = arr.map((item) => {
        return item.requiredCount
      })
      const color = ['rgba(148, 107, 243)', 'rgba(100, 116, 243)', 'rgba(85, 140, 251)', 'rgba(60, 197, 250)', 'rgba(56, 208, 176)', 'rgba(203, 204, 199)']
      const color1 = ['rgba(148, 107, 243,0.3)', 'rgba(100, 116, 243,0.3)', 'rgba(85, 140, 251,0.3)', 'rgba(60, 197, 250,0.3)', 'rgba(56, 208, 176,0.3)', 'rgba(203, 204, 199,0.3)']
      // 基于准备好的dom，初始化echarts实例
      let getchart = echarts.init(document.getElementById('postDistributionEchart'))
      let option = {}
      if (xData?.length > 0) {
        option = {
          title: {
            text: '岗位分布（实际）',
            left: '4%',
            top: '4%',
            textStyle: {
              color: '#333333', // 标题颜色
              fontWeight: 'bold',
              fontSize: 16
            }
          },
          tooltip: {
            trigger: 'axis',
            borderWidth: 0,
            axisPointer: {
              type: 'shadow'
            },
            backgroundColor: 'rgba(0,0,0,0.75)',
            color: '#fff',
            textStyle: {
              color: '#fff'
            },
            // 避免出现多个信息
            formatter: function (params) {
              return (
                params[0].name + `</br><span style="display:inline-block;width:10px;height:10px;border-radius:5px;background-color:${params[0].color}"></span> ` + params[0].value
              ) // 只显示第一个系列的信息
            }
          },
          grid: {
            top: '15%',
            left: '3%',
            right: '8%',
            bottom: '10%',
            containLabel: true
          },
          xAxis: {
            type: 'value',
            axisLabel: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false
            }
          },
          yAxis: [
            {
              type: 'category',
              data: xData,
              inverse: true,
              axisLabel: {
                fontSize: 16,
                inside: false,
                verticalAlign: 'center',
                padding: [5, 0, 0, 0],
                margin: 20
              },
              axisLine: {
                show: false
              },
              axisTick: {
                show: false
              },
              splitLine: {
                show: false,
                lineStyle: {
                  color: '#13387a'
                }
              }
            },
            {
              type: 'category',
              data: yData,
              inverse: true,
              axisLabel: {
                inside: false,
                verticalAlign: 'center',
                padding: [5, 0, 0, 0],
                margin: 20,
                formatter: function (value, index) {
                  return `{a|${value}}`
                },
                rich: {
                  a: {
                    fontSize: 16,
                    color: '#333',
                    padding: [4, 0, 0, 0]
                  }
                }
              },
              axisLine: {
                show: false
              },
              axisTick: {
                show: false
              },
              splitLine: {
                show: false,
                lineStyle: {
                  color: '#13387a'
                }
              }
            }
          ],
          series: [
            {
              data: yData,
              barWidth: 20,
              zlevel: 1,
              showBackground: false,
              // itemStyle: {
              //   color: function (params) {
              //     // 每个柱子单独颜色渐变 可多加几个渐变过程 colorStops[{},{},{}]
              //     const colorStops = [
              //       {
              //         offset: 1,
              //         color: color1[Math.floor(Math.random() * 6)]
              //       },
              //       {
              //         offset: 0,
              //         color: color[Math.floor(Math.random() * 6)]
              //         // color: lightenColor(colors[params.dataIndex], 0.5) // 使用 lightenColor 函数使颜色变浅
              //         // color: hexToRgb(colorList[params.dataIndex % colorList.length], 0.2) // 使用 lightenColor 函数使颜色变浅
              //       }
              //     ]
              //     return new echarts.graphic.LinearGradient(0, 0, 1, 0, colorStops)
              //   }
              // },
              type: 'bar'
            }
          ],
          // 数据过多纵向滚动
          dataZoom: [
            {
              yAxisIndex: [0, 1],
              orient: 'vertical',
              show: true,
              type: 'slider',
              start: 0,
              // end: 100,
              width: 10,
              left: '98%',
              borderColor: 'rgba(43,48,67,.1)',
              fillerColor: '#6580b8',
              zoomLock: true,
              showDataShadow: false,
              backgroundColor: 'rgba(43,48,67,.1)',
              showDetail: false,
              // realtime: true,
              filterMode: 'filter',
              handleIcon: 'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
              handleStyle: {
                color: '#6580b8',
                borderColor: '#6580b8'
              },
              maxValueSpan: 4,
              minValueSpan: 4,
              brushSelect: false
            },
            {
              type: 'inside',
              show: false,
              yAxisIndex: [0, 1],
              height: 8
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            left: 'center',
            top: 'center',
            textStyle: {
              color: '#000',
              fontSize: 16
            }
          }
        }
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 获取列表数据
    getDataList() {
      this.tableData = []
      this.tableLoadingStatus = true
      const params = {
        ...this.searchForm,
        page: this.pagination.current,
        pageSize: this.pagination.size
      }
      this.$api.supplierAssess
        .queryCurrentDutyGroupByPage(params)
        .then((res) => {
          this.tableLoadingStatus = false
          if (res.code === '200') {
            this.tableData = res.data.records
            this.pagination.total = res.data.total
          } else {
            this.tableData = []
            this.pagination.total = 0
          }
        })
        .catch((err) => {
          this.tableLoadingStatus = false
        })
    },
    // 表单搜索按钮点击
    onSearch() {
      this.pagination.current = 1
      this.getDataList()
    },
    // 表单重置按钮点击
    onReset() {
      this.$refs.formRef.resetFields()
      this.onSearch()
    },
    // 操作
    onOperate(type, row) {
      if (type === 'view') {
        this.$router.push({
          name: 'postDetail',
          query: {
            id: row.dutyAttendanceConfigId
          }
        })
      }
    }
  }
}
</script>
<style scoped lang="scss">
.currentTimeTab {
  ::v-deep(> .container-content) {
    height: 100%;
    width: 100%;
    background-color: #fff;
  }
  &__card {
    display: flex;
    width: 100%;
    height: 270px;
    align-items: center;
    margin-bottom: 10px;
    .card_box {
      width: calc((100% / 3) - 16px);
      height: 100%;
      background: #faf9fc;
      border-radius: 4px;
    }
    .card_box:nth-child(2) {
      margin: 0 24px;
    }
  }
  &__tag {
    // 异常
    &--1 {
      --color: #f64646;
      color: #f64646;
    }
    // 正常
    &--0 {
      --color: #00b42a;
      color: #00b42a;
    }
    &:before {
      content: '';
      display: inline-block;
      vertical-align: middle;
      margin-right: 4px;
      height: 4px;
      width: 4px;
      border-radius: 4px;
      background-color: var(--color);
    }
  }
  &__pagination {
    margin-top: 10px;
  }
  &__table__color {
    display: inline-block;
    height: 16px;
    width: 16px;
    vertical-align: text-top;
    margin-right: 4px;
  }
  &__search {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
.el-form-item {
  margin-bottom: 8px !important;
}
</style>
