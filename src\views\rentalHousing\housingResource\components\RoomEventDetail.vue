<script>
import monent from 'moment'
export default {
  name: 'RoomEventDetail',
  props: {
    visible: Boolean,
    id: String
  },
  events: ['update:visible'],
  data: () => ({
    formModel: {
      // 申请人
      applicant: '',
      // 申请附件
      applyFileList: [],
      // 甲方名称
      firstPartyName: '',
      // 乙方名称
      secondPartyName: '',
      // 空间位置
      spaceName: '',
      // 房号
      houseName: '',
      // 单价
      price: '',
      // 租金
      amount: '',
      // 合同号
      contractNo: '',
      // 合同附件
      contractFileList: [],
      // 租期开始时间
      startDate: '',
      // 租期结束时间
      endDate: '',
      // 房屋照片
      photoList: [],
      // 入住时间
      moveInDate: '',
      // 回执单
      receiptList: [],
      // 节点状态
      eventCode: '',
      // 婚姻状况 0 未婚 1 已婚
      marriage: '',
      copyOfIdCard: [],
      copyOfDiploma: [],
      copyOfProfessionalTitle: [],
      copyOfResidencePermit: [],
      copyOfParentsIdCard: [],
      noHouse: [],
      cohabitant: [],
      copyOfMarriageCertificate: [],
      copyOfSpouseIdCard: [],
      otherInfo: [],
      checkInUrl: [],
      warmTipsUrl: [],
      checkInProveUrl: [],
      otherProofsUrl: []
    },
    loadingStatus: false,
    // 当前上传类型
    uploadType: '',
    // timerId
    $timerId: -1
  }),
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    },
    photoImageUrls() {
      return this.formModel.photoList.map((it) => it.url)
    }
  },
  watch: {
    dialogVisible(val) {
      if (val && this.id) {
        this.getDetail()
      }
    }
  },
  methods: {
    getDetail() {
      this.loadingStatus = true
      this.$api.rentalHousingApi
        .historyEventDetail({ id: this.id })
        .then((res) => {
          if (res.code === '200') {
            const { houseHistoryRecordEntity, houseUserEntity } = res.data
            const houseContractEntity = res.data.houseContractEntity || {}
            this.formModel.applicant = houseUserEntity.tenantName
            this.formModel.applyFileList = this.revertFileList(houseUserEntity.attachmentUrl)
            this.formModel.firstPartyName = houseContractEntity.firstPartyName
            this.formModel.secondPartyName = houseContractEntity.secondPartyName
            this.formModel.spaceName = houseContractEntity.spaceName
            this.formModel.houseName = houseContractEntity.houseName
            this.formModel.price = houseContractEntity.rentingUnitPrice
            this.formModel.amount = houseContractEntity.rentingMoney
            this.formModel.contractNo = houseContractEntity.contractNum
            this.formModel.contractFileList = this.revertFileList(houseContractEntity.attachmentUrl)
            this.formModel.startDate = houseContractEntity.rentingStartTime
            this.formModel.endDate = houseContractEntity.rentingEndTime
            this.formModel.photoList = this.revertFileList(houseHistoryRecordEntity.housePicUrl)
            this.formModel.moveInDate = houseHistoryRecordEntity.liveDate
            this.formModel.receiptList = this.revertFileList(houseHistoryRecordEntity.receiptUrl)
            this.formModel.eventCode = houseHistoryRecordEntity.eventCode
            this.formModel.marriage = houseHistoryRecordEntity.marriage
            this.formModel.copyOfIdCard = this.revertFileList(houseHistoryRecordEntity.copyOfIdCard)
            this.formModel.copyOfDiploma = this.revertFileList(houseHistoryRecordEntity.copyOfDiploma)
            this.formModel.copyOfProfessionalTitle = this.revertFileList(houseHistoryRecordEntity.copyOfProfessionalTitle)
            this.formModel.copyOfResidencePermit = this.revertFileList(houseHistoryRecordEntity.copyOfResidencePermit)
            this.formModel.copyOfParentsIdCard = this.revertFileList(houseHistoryRecordEntity.copyOfParentsIdCard)
            this.formModel.noHouse = this.revertFileList(houseHistoryRecordEntity.noHouse)
            this.formModel.cohabitant = this.revertFileList(houseHistoryRecordEntity.cohabitant)
            this.formModel.copyOfMarriageCertificate = this.revertFileList(houseHistoryRecordEntity.copyOfMarriageCertificate)
            this.formModel.copyOfSpouseIdCard = this.revertFileList(houseHistoryRecordEntity.copyOfSpouseIdCard)
            this.formModel.otherInfo = this.revertFileList(houseHistoryRecordEntity.otherInfo)
            this.formModel.checkInUrl = this.revertFileList(houseHistoryRecordEntity.checkInUrl)
            this.formModel.warmTipsUrl = this.revertFileList(houseHistoryRecordEntity.warmTipsUrl)
            this.formModel.checkInProveUrl = this.revertFileList(houseHistoryRecordEntity.checkInProveUrl)
            this.formModel.otherProofsUrl = this.revertFileList(houseHistoryRecordEntity.otherProofsUrl)
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取详情出错'))
        .finally(() => (this.loadingStatus = false))
    },
    // joined path string
    revertFileList(urlStr) {
      if (!urlStr) return []
      return JSON.parse(urlStr).map((it) => {
        return {
          extension: it.url.substring(it.url.lastIndexOf('.')),
          name: it.name.substring(it.name.lastIndexOf('/') + 1),
          url: this.$tools.imgUrlTranslation(it.url)
        }
      })
    },
    onDialogClosed() {
      // dialog关闭时重置form表单
      this.$refs.formRef.resetFields()
      this.uploadType = ''
      clearTimeout(this.$timerId)
    },
    onFileClick({ url, name }) {
      fetch(url)
        .then((res) => res.blob())
        .then((blob) => {
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = name
          a.click()
          window.URL.revokeObjectURL(url)
        })
        .catch(() => this.$message.error('下载失败'))
    }
  }
}
</script>
<template>
  <el-drawer class="component room-event-detail" title="详细信息" size="1080px" :close-on-press-escape="false" :visible.sync="dialogVisible" @closed="onDialogClosed">
    <el-form ref="formRef" v-loading="loadingStatus" class="room-event-detail__body" :model="formModel" label-width="95px">
      <div class="room-event-detail__title">
        <svg-icon name="right-arrow" />
        申请信息
      </div>
      <el-row>
        <el-col :span="10">
          <el-form-item label="申请人" prop="applicant">
            <div class="room-event-detail__info">{{ formModel.applicant }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="14" v-if="formModel.eventCode == '3'">
          <el-form-item label="附件文件" prop="applyFileList">
            <ul class="el-upload-list el-upload-list--file-list">
              <li v-for="item of formModel.applyFileList" :key="item.url" class="el-upload-list__item is-success">
                <a class="el-upload-list__item-name" @click="onFileClick(item)">
                  <i class="el-icon-document"></i>
                  {{ item.name }}
                </a>
              </li>
            </ul>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="formModel.eventCode == '1'">
        <el-col :span="24">
          <el-form-item label="婚姻状况" prop="applicant">
            <el-radio-group v-model="formModel.marriage" disabled>
              <el-radio :label="'0'" disabled>未婚</el-radio>
              <el-radio :label="'1'" disabled>已婚</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="身份证复印件" prop="copyOfIdCard" label-width="140px">
            <ul class="el-upload-list el-upload-list--file-list">
              <li v-for="item of formModel.copyOfIdCard" :key="item.url" class="el-upload-list__item is-success">
                <a class="el-upload-list__item-name" @click="onFileClick(item)">
                  <i class="el-icon-document"></i>
                  {{ item.name }}
                </a>
              </li>
            </ul>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="学历学位复印件" prop="copyOfDiploma" label-width="140px">
            <ul class="el-upload-list el-upload-list--file-list">
              <li v-for="item of formModel.copyOfDiploma" :key="item.url" class="el-upload-list__item is-success">
                <a class="el-upload-list__item-name" @click="onFileClick(item)">
                  <i class="el-icon-document"></i>
                  {{ item.name }}
                </a>
              </li>
            </ul>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="职称复印件" prop="copyOfProfessionalTitle" label-width="140px">
            <ul class="el-upload-list el-upload-list--file-list">
              <li v-for="item of formModel.copyOfProfessionalTitle" :key="item.url" class="el-upload-list__item is-success">
                <a class="el-upload-list__item-name" @click="onFileClick(item)">
                  <i class="el-icon-document"></i>
                  {{ item.name }}
                </a>
              </li>
            </ul>
          </el-form-item>
        </el-col>
        <el-col :span="10" v-if="formModel.marriage === '0'">
          <el-form-item label="深圳居住证复印件" prop="copyOfResidencePermit" label-width="140px">
            <ul class="el-upload-list el-upload-list--file-list">
              <li v-for="item of formModel.copyOfResidencePermit" :key="item.url" class="el-upload-list__item is-success">
                <a class="el-upload-list__item-name" @click="onFileClick(item)">
                  <i class="el-icon-document"></i>
                  {{ item.name }}
                </a>
              </li>
            </ul>
          </el-form-item>
        </el-col>
        <el-col :span="10" v-if="formModel.marriage === '0'">
          <el-form-item label="父母身份证复印件" prop="copyOfParentsIdCard" label-width="140px">
            <ul class="el-upload-list el-upload-list--file-list">
              <li v-for="item of formModel.copyOfParentsIdCard" :key="item.url" class="el-upload-list__item is-success">
                <a class="el-upload-list__item-name" @click="onFileClick(item)">
                  <i class="el-icon-document"></i>
                  {{ item.name }}
                </a>
              </li>
            </ul>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="深圳无房证明" prop="noHouse" label-width="140px">
            <ul class="el-upload-list el-upload-list--file-list">
              <li v-for="item of formModel.noHouse" :key="item.url" class="el-upload-list__item is-success">
                <a class="el-upload-list__item-name" @click="onFileClick(item)">
                  <i class="el-icon-document"></i>
                  {{ item.name }}
                </a>
              </li>
            </ul>
          </el-form-item>
        </el-col>
        <el-col :span="10" v-if="formModel.marriage == '1'">
          <el-form-item label="结婚证复印件" prop="copyOfMarriageCertificate" label-width="140px">
            <ul class="el-upload-list el-upload-list--file-list">
              <li v-for="item of formModel.copyOfMarriageCertificate" :key="item.url" class="el-upload-list__item is-success">
                <a class="el-upload-list__item-name" @click="onFileClick(item)">
                  <i class="el-icon-document"></i>
                  {{ item.name }}
                </a>
              </li>
            </ul>
          </el-form-item>
        </el-col>
        <el-col :span="10" v-if="formModel.marriage == '1'">
          <el-form-item label="配偶身份证复印件" prop="copyOfSpouseIdCard" label-width="140px">
            <ul class="el-upload-list el-upload-list--file-list">
              <li v-for="item of formModel.copyOfSpouseIdCard" :key="item.url" class="el-upload-list__item is-success">
                <a class="el-upload-list__item-name" @click="onFileClick(item)">
                  <i class="el-icon-document"></i>
                  {{ item.name }}
                </a>
              </li>
            </ul>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="同住人资料" prop="cohabitant" label-width="140px">
            <ul class="el-upload-list el-upload-list--file-list">
              <li v-for="item of formModel.cohabitant" :key="item.url" class="el-upload-list__item is-success">
                <a class="el-upload-list__item-name" @click="onFileClick(item)">
                  <i class="el-icon-document"></i>
                  {{ item.name }}
                </a>
              </li>
            </ul>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="其他资料" prop="otherInfo" label-width="140px">
            <ul class="el-upload-list el-upload-list--file-list">
              <li v-for="item of formModel.otherInfo" :key="item.url" class="el-upload-list__item is-success">
                <a class="el-upload-list__item-name" @click="onFileClick(item)">
                  <i class="el-icon-document"></i>
                  {{ item.name }}
                </a>
              </li>
            </ul>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="room-event-detail__title">
        <svg-icon name="right-arrow" />
        合同信息
      </div>
      <el-row>
        <el-col :span="5">
          <el-form-item label="甲方" prop="firstPartyName">
            <div class="room-event-detail__info">{{ formModel.firstPartyName }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="乙方" prop="secondPartyName">
            <div class="room-event-detail__info">{{ formModel.secondPartyName }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="9">
          <el-form-item label="空间位置" prop="spaceName">
            <div class="room-event-detail__info">{{ formModel.spaceName }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="房间号" prop="houseName">
            <div class="room-event-detail__info">{{ formModel.houseName }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="出租单价" prop="price">
            <el-input :value="formModel.price" readonly>
              <template #suffix> 元/㎡/月 </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :offset="5" :span="6">
          <el-form-item label="出租租金" prop="amount">
            <el-input :value="formModel.amount" readonly>
              <template #suffix> 元 </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="合同编号" prop="contractNo">
            <div class="room-event-detail__info">{{ formModel.contractNo }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="租期开始" prop="startDate">
            <div class="room-event-detail__info">{{ formModel.startDate }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="租期结束" prop="endDate">
            <div class="room-event-detail__info">{{ formModel.endDate }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="附件文件" prop="contractFileList">
            <ul class="el-upload-list el-upload-list--file-list">
              <li v-for="item of formModel.contractFileList" :key="item.url" class="el-upload-list__item is-success">
                <a class="el-upload-list__item-name" @click="onFileClick(item)">
                  <i class="el-icon-document"></i>
                  {{ item.name }}
                </a>
              </li>
            </ul>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="room-event-detail__title">
        <svg-icon name="right-arrow" />
        入住回执单
      </div>
      <el-row>
        <el-col :span="10">
          <el-form-item label="回执单" prop="contractFileList" label-width="140px">
            <ul class="el-upload-list el-upload-list--file-list">
              <li v-for="item of formModel.receiptList" :key="item.url" class="el-upload-list__item is-success">
                <a class="el-upload-list__item-name" @click="onFileClick(item)">
                  <i class="el-icon-document"></i>
                  {{ item.name }}
                </a>
              </li>
            </ul>
          </el-form-item>
        </el-col>
        <el-col :span="10" v-if="formModel.eventCode == '1'">
          <el-form-item label="入住登记确认单" prop="checkInUrl" label-width="140px">
            <ul class="el-upload-list el-upload-list--file-list">
              <li v-for="item of formModel.checkInUrl" :key="item.url" class="el-upload-list__item is-success">
                <a class="el-upload-list__item-name" @click="onFileClick(item)">
                  <i class="el-icon-document"></i>
                  {{ item.name }}
                </a>
              </li>
            </ul>
          </el-form-item>
        </el-col>
        <el-col :span="10" v-if="formModel.eventCode == '1'">
          <el-form-item label="温馨提示确认单" prop="warmTipsUrl" label-width="140px">
            <ul class="el-upload-list el-upload-list--file-list">
              <li v-for="item of formModel.warmTipsUrl" :key="item.url" class="el-upload-list__item is-success">
                <a class="el-upload-list__item-name" @click="onFileClick(item)">
                  <i class="el-icon-document"></i>
                  {{ item.name }}
                </a>
              </li>
            </ul>
          </el-form-item>
        </el-col>
        <el-col :span="10" v-if="formModel.eventCode == '1'">
          <el-form-item label="入住证明确认单" prop="checkInProveUrl" label-width="140px">
            <ul class="el-upload-list el-upload-list--file-list">
              <li v-for="item of formModel.checkInProveUrl" :key="item.url" class="el-upload-list__item is-success">
                <a class="el-upload-list__item-name" @click="onFileClick(item)">
                  <i class="el-icon-document"></i>
                  {{ item.name }}
                </a>
              </li>
            </ul>
          </el-form-item>
        </el-col>
        <el-col :span="10" v-if="formModel.eventCode == '1'">
          <el-form-item label="其他证明材料" prop="otherProofsUrl" label-width="140px">
            <ul class="el-upload-list el-upload-list--file-list">
              <li v-for="item of formModel.otherProofsUrl" :key="item.url" class="el-upload-list__item is-success">
                <a class="el-upload-list__item-name" @click="onFileClick(item)">
                  <i class="el-icon-document"></i>
                  {{ item.name }}
                </a>
              </li>
            </ul>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="room-event-detail__title">
        <svg-icon name="right-arrow" />
        入住确认
      </div>
      <el-row>
        <el-col :span="10">
          <el-form-item label="入住开始" prop="moveInDate">
            <div class="room-event-detail__info">{{ formModel.moveInDate }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="14">
          <el-form-item label="房屋照片" prop="photoList">
            <ul v-if="formModel.photoList.length" class="el-upload-list el-upload-list--picture-card">
              <li v-for="item of formModel.photoList" :key="item.url" class="el-upload-list__item">
                <el-image :src="item.url" alt="" class="el-upload-list__item-thumbnail" :preview-src-list="photoImageUrls"></el-image>
              </li>
            </ul>
            <div v-else class="room-event-detail__info">无</div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="room-event-detail__footer">
      <el-button type="primary" plain @click="dialogVisible = false">关闭</el-button>
    </div>
  </el-drawer>
</template>
<style lang="scss" scoped>
.room-event-detail {
  .el-form {
    background: #fff;
    width: 100%;
    padding: 16px;
  }
  ::v-deep(.el-drawer__header) {
    padding: 0px 16px;
    color: #333;
    font-size: 16px;
    height: 50px;
    line-height: 50px;
    border-bottom: 1px solid #eee;
    margin-bottom: 0px;
  }
  ::v-deep(.el-drawer__body) {
    overflow: hidden;
  }
  &__body {
    height: calc(100% - 56px);
    overflow: auto;
  }
  &__footer {
    height: 56px;
    padding: 0 16px;
    line-height: 56px;
    text-align: right;
    border-top: 1px solid #eee;
  }
  .el-input-number {
    line-height: 30px;
  }
  &__title {
    margin-bottom: 16px;
  }
  &__info {
    padding-left: 16px;
  }
  ::v-deep(.el-input) {
    .el-input__inner[readonly] {
      border: none;
    }
  }
  .el-upload-list__item {
    margin-top: 8px;
  }
}
</style>
