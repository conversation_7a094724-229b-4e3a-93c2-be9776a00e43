<template>
  <PageContainer style="position: relative">
    <div slot="content" class="whole">
      <div class="left">
        <div class="title_box"><svg-icon name="right-arrow" /> 空间结构</div>
        <el-input v-model="filterText" style="width: 230px; padding-top: 10px" placeholder="请输入关键字"></el-input>
        <div v-loading="treeLoading" class="sino_tree_box">
          <div class="tree_div">
            <el-tree
              ref="tree"
              class="filter-tree"
              :data="treeData"
              :props="defaultProps"
              :default-expanded-keys="idArr"
              :filter-node-method="filterNode"
              :highlight-current="true"
              node-key="id"
              @node-click="nodeClick"
            ></el-tree>
          </div>
        </div>
      </div>
      <div ref="spacePlan" style="height: 100%; overflow: hidden" class="right">
        <div class="Iframe_oper_top">
          <!-- 建筑楼层 -->
          <el-select ref="select" v-model="filters.modelCode" class="sino_form_input mr_15" placeholder="请选择建筑楼层" filterable clearable @change="handleChangeModel">
            <el-option v-for="item in modelCodeList" :key="item.modelCode" :label="item.fullName" :value="item.modelCode"></el-option>
          </el-select>
          <!-- 空间名称查询 -->
          <el-autocomplete
            ref="autocomplete"
            v-model="filters.localSpaceName"
            class="inline-input mr_15"
            :fetch-suggestions="querySearch"
            placeholder="请输入空间名称"
            @select="handelChange"
          ></el-autocomplete>
          <el-button size="medium" type="primary" @click="upDownFn">导出模板</el-button>
          <el-button size="medium" type="primary" plain @click="importFn">导入</el-button>
          <el-button size="medium" type="primary" title="全屏切换" icon="el-icon-full-screen" @click="fullScreen"> </el-button>
          <!-- <el-button
            size="medium"
            plain
            @click="downImg"
            >下载图片</el-button
          > -->
        </div>
        <div class="Iframe_oper_left" :style="{ height: spaceTypeRoomList.length ? '60%' : 'auto' }">
          <!-- <div class="title_box" style="border: none; padding: 0;"><span class="title-tip"></span>功能类型</div> -->
          <div class="title_box" style="border: none; padding: 0">
            <!-- <span class="title-tip"></span>功能类型 -->
            <div class="btns">
              <span
                :class="{
                  'active-btn': typeAnalysisType == '0'
                }"
                @click="changeTypeAnalysisType('0')"
                >功能类型</span
              >
              <span
                :class="{
                  'active-btn': typeAnalysisType == '1'
                }"
                @click="changeTypeAnalysisType('1')"
                >归属部门</span
              >
              <span
                :class="{
                  'active-btn': typeAnalysisType == '2'
                }"
                @click="changeTypeAnalysisType('2')"
                >使用状态</span
              >
            </div>
          </div>
          <el-checkbox-group v-if="typeAnalysisType == '0'" v-model="checkList" :class="spaceTypeRoomList.length ? 'checkbox_group_class' : ''" @change="checkedChange">
            <el-checkbox v-for="(item, index) in spaceTypeRoomList" :key="index" :label="item.functionDictId" class="checkbox_block"
              ><span
                :style="{
                  color: item.functionColourBy255 ? 'rgba(' + item.functionColourBy255 + ')' : ''
                }"
                >{{ item.functionDictName }}</span
              ></el-checkbox
            ><br />
          </el-checkbox-group>
          <!-- <div v-if="typeAnalysisType == '1'"></div> -->
          <el-checkbox-group v-if="typeAnalysisType == '1'" v-model="checkList2" :class="departmentList.length ? 'checkbox_group_class' : ''" @change="checkedChange2">
            <el-checkbox v-for="(item, index) in departmentList" :key="index" :label="item.deptId" class="checkbox_block"
              ><span
                :style="{
                  color: item.functionColourBy255 ? 'rgba(' + item.functionColourBy255 + ')' : ''
                }"
                >{{ item.deptNameCount }}</span
              ></el-checkbox
            ><br />
          </el-checkbox-group>
          <el-checkbox-group v-if="typeAnalysisType == '2'" v-model="checkList3" :class="departmentList.length ? 'checkbox_group_class' : ''" @change="checkedChange3">
            <el-checkbox v-for="(item, index) in statusList" :key="index" :label="item.spaceStateId" class="checkbox_block"
              ><span
                :style="{
                  color: item.functionColourBy255 ? 'rgba(' + item.functionColourBy255 + ')' : ''
                }"
                >{{ item.spaceStatusCount }}</span
              ></el-checkbox
            ><br />
          </el-checkbox-group>
        </div>
        <!-- U3D_web  -->
        <iframe ref="unityIframe" class="Iframe_class" src="/U3D_web/index.html"></iframe>
      </div>
      <!-- 导入 -->
      <sinoImportFile :importDialog="importDialog" :addFileName="addFileName" @cancelFile="cancelFile" />
      <!-- 关联空间台账 -->
      <sinoDialog ref="sinoDialog" title="关联空间台账" @sureDialog="sureDialog" @closeDialog="closeDialog">
        <el-form ref="formInline" class="sino_form" :model="formInline" :rules="rules" label-width="100px">
          <el-form-item label="位置" prop="simName">
            <el-input v-model="formInline.simName" class="sino_form_input" placeholder="请选择空间位置" show-word-limit> </el-input>
          </el-form-item>
          <el-form-item label="模型编码" prop="modelCode">
            <el-input v-model="formInline.modelCode" class="sino_form_input" placeholder="系统生成" show-word-limit disabled> </el-input>
          </el-form-item>
          <el-form-item label="空间名称" prop="localSpaceName">
            <el-input v-model="formInline.localSpaceName" class="sino_form_input" placeholder="请输入空间名称" show-word-limit> </el-input>
          </el-form-item>
          <el-form-item label="本地编码" prop="localSpaceCode">
            <el-input v-model="formInline.localSpaceCode" class="sino_form_input" placeholder="请输入本地编码" show-word-limit> </el-input>
          </el-form-item>
          <el-form-item label="功能类型" prop="functionDictId">
            <el-select v-model="formInline.functionDictId" class="sino_form_input" placeholder="请选择功能类型" filterable clearable>
              <el-option v-for="item in spaceTypeList" :key="item.id" :label="item.dictName" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="空间高度" prop="hight">
            <el-input
              v-model="formInline.hight"
              class="sino_form_input"
              placeholder="请输入空间高度"
              onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')"
              show-word-limit
            >
              <template slot="append">m</template>
            </el-input>
          </el-form-item>
          <el-form-item label="建筑面积" prop="area">
            <el-input
              v-model="formInline.area"
              class="sino_form_input"
              placeholder="请输入建筑面积"
              onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')"
              show-word-limit
            >
              <template slot="append">㎡</template>
            </el-input>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="formInline.remark" class="sino_form_input_custom" placeholder="请输入" show-word-limit> </el-input>
          </el-form-item>
        </el-form>
      </sinoDialog>
    </div>
  </PageContainer>
</template>
<script>
import { transData } from '@/util'
import store from '@/store/index'
import axios from 'axios'
import sinoImportFile from './common/sinoImportFile.vue'
import sinoDialog from './common/sinoDialog.vue'
export default {
  // eslint-disable-next-line vue/multi-word-component-names
  name: 'ichnography',
  components: {
    sinoImportFile,
    sinoDialog
  },
  data() {
    return {
      departmentList: [],
      statusList: [],

      checkList: [],
      checkList2: [],
      checkList3: [],
      typeAnalysisType: '',

      filterText: '',
      treeLoading: true,
      treeData: [],
      idArr: [],
      spaceIds: [],

      iframeUrl: '/U3D_web/index.html',
      defaultProps: {
        label: 'ssmName',
        children: 'list'
      },
      modelCodeList: [],
      filters: {
        modelCode: '',
        functionDictId: '',
        localSpaceName: ''
      },
      spaceTypeList: [],
      roomList: [],
      spaceTypeRoomList: [],
      // --------------------Dialog关联空间台账
      formInline: {
        simName: '',
        modelCode: '',
        localSpaceName: '',
        localSpaceCode: '',
        functionDictId: '',
        hight: '',
        area: '',
        remark: ''
      },
      rules: {
        simName: {
          required: true,
          message: '请选择空间位置',
          trigger: 'blur'
        },
        localSpaceName: {
          required: true,
          message: '请输入空间名称',
          trigger: 'blur'
        }
      },
      // ---------------------Dialog_导入
      importDialog: false,
      addFileName: 'spacePlan'
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    window.getModelMsg = this.getModelMsg
    window.openDialog = this.openDialog
  },
  mounted() {
    this.spaceTreeListFn()
    this.valveTypeListFn()
    this.addCloseEvent()
  },
  methods: {
    changeTypeAnalysisType(type) {
      this.typeAnalysisType = type
      if (this.typeAnalysisType == '0') {
        this.spaceTypeListByModelCodeFn(this.filters.modelCode)
        this.$nextTick(() => {
          this.roomListFn()
        })
      } else if (this.typeAnalysisType == '1') {
        this.getDepartment(this.filters.modelCode)
        this.$nextTick(() => {
          this.roomListFn()
        })
      } else if (this.typeAnalysisType == '2') {
        // this.getDepartment()
        this.getspaceStatus(this.filters.modelCode)
        this.$nextTick(() => {
          this.roomListFn()
        })
      }
    },
    getDepartment(modelCode) {
      let params = {
        modelCode: modelCode
      }

      this.$api.getspaceDept(params).then((res) => {
        if (res.code == 200) {
          this.departmentList = res.data
          if (!res.data) return false
          this.checkList2 = res.data.map((item) => {
            return item.deptId
          })
          this.$nextTick(() => {
            this.SendToUnity(this.departmentList, 2) // 按楼层切换房间颜色
          })
        }
      })
    },
    getspaceStatus(modelCode) {
      let params = {
        modelCode: modelCode
      }

      this.$api.getspaceStatus(params).then((res) => {
        if (res.code == 200) {
          this.statusList = res.data
          if (!res.data) return false
          this.checkList3 = res.data.map((item) => {
            return item.spaceStateId
          })
          this.$nextTick(() => {
            this.SendToUnity(this.statusList, 2) // 按楼层切换房间颜色
          })
        }
      })
    },
    filterNode(value, data) {
      if (!value) return true
      return data.ssmName.indexOf(value) !== -1
    },
    nodeClick(val) {
      const { tree } = this.$refs
      this.spaceIds = []
      let checkedNode = tree.getNode(val.id)
      this.checkedNode = checkedNode
      this.spaceIds.push(this.checkedNode.data.modelCode)
      // this.getTreeNode(checkedNode);
      if (this.checkedNode.level == '3') {
        this.getFloorHaveModelFn()
      }
    },
    spaceTreeListFn() {
      this.$api.spaceTreeList().then((res) => {
        this.treeLoading = false
        if (res.code == 200) {
          res.data.map((tree) => {
            tree.ssmType == 1 || tree.ssmType == 2 ? this.idArr.push(tree.id) : ''
            return this.idArr
          })
          this.treeData = transData(res.data, 'id', 'pid', 'list')
          this.$nextTick(function () {
            this.$refs.tree.setCurrentKey(res.data[0].id)
          })
          this.spaceIds.push(res.data[0].modelCode)
        }
      })
    },
    // el-select 监听弹窗隐藏
    addCloseEvent() {
      const fn = () => {
        setTimeout(() => {
          const activeElement = document.activeElement
          const selectVm = this.$refs.select
          const autocomplete = this.$refs.autocomplete
          if (activeElement.nodeName === 'IFRAME') {
            selectVm.handleClose() // element-ui 统一命名的关闭下拉框方法
            autocomplete.activated = false
          }
        }, 100)
      }
      document.addEventListener('focusout', fn)
      this.$once('hook:beforeDestroy', () => {
        document.removeEventListener('focusout', fn)
      })
    },
    // 按层级获取空间编码
    checkNumber(str) {
      if (!str) return false
      str = str.replace(/[^0-9]/g, '')
      let result = str.split(/(\d{2})(\d{3})/).filter(Boolean)
      return result
    },
    //  获取楼层模型列表
    getFloorHaveModelFn() {
      this.$api.getFloorHaveModel({}).then((res) => {
        if (res.code == 200) {
          // this.treeLoading = false
          this.modelCodeList = res.data
        }
      })
    },
    // 获取空间功能类型字典列表
    valveTypeListFn() {
      this.$api
        .valveTypeList({
          typeValue: 'SP'
        })
        .then((res) => {
          if (res.code == 200) {
            this.spaceTypeList = res.data
          }
        })
    },
    handleChangeModel(val) {
      this.changeTypeAnalysisType('0')
      this.filters.modelCode = val
      this.SendToUnity(this.checkNumber(val), 1) // 按楼层切换模型
      this.spaceTypeListByModelCodeFn(val)
      this.roomListFn()
    },
    /**
     * @description: 根据空间名称搜索房间信息
     * @param {*} queryString
     */
    querySearch(queryString, cb) {
      let roomArr = this.roomList
      let results = queryString ? roomArr.filter(this.createFilter(queryString)) : roomArr
      // 调用 callback 返回建议列表的数据
      results.forEach((item) => {
        item.value = item.localSpaceName
      })
      cb(results)
    },
    createFilter(queryString) {
      return (roomArr) => {
        return roomArr.localSpaceName.toLowerCase().indexOf(queryString.toLowerCase()) === 0
      }
    },
    handelChange(obj) {
      this.filters.localSpaceName = obj.value
      this.SendToUnity(obj, 4) // 房间模型信息面板
    },
    // 根据空间名称搜索房间信息
    roomListFn() {
      if (this.filters.modelCode) {
        this.$api
          .roomList({
            modelCode: this.filters.modelCode
          })
          .then((res) => {
            if (res.code == 200) {
              this.roomList = res.data
            }
          })
      } else {
        this.$message({
          message: '请选择建筑楼层或空间功能类型',
          type: 'warning'
        })
      }
    },
    // 根据楼层code获取已设置空间功能的类型列表
    spaceTypeListByModelCodeFn(modelCode) {
      this.$api
        .spaceTypeListByModelCode({
          modelCode: modelCode
        })
        .then((res) => {
          if (res.code == 200) {
            this.spaceTypeRoomList = res.data
            if (!res.data) return false
            this.checkList = res.data.map((item) => {
              return item.functionDictId
            })
            this.$nextTick(() => {
              this.SendToUnity(this.spaceTypeRoomList, 2) // 按楼层切换房间颜色
            })
          }
        })
    },
    checkedChange3(val) {
      this.checkList3 = val
      let checkedObj = []
      this.checkList3.forEach((item) => {
        this.statusList.forEach((list) => {
          if (list.spaceStateId == item) {
            checkedObj.push(list)
          }
        })
      })
      this.SendToUnity(checkedObj, 2) // 按楼层切换房间颜色
    },
    checkedChange2(val) {
      this.checkList2 = val
      let checkedObj = []
      this.checkList2.forEach((item) => {
        this.departmentList.forEach((list) => {
          if (list.deptId == item) {
            checkedObj.push(list)
          }
        })
      })
      this.SendToUnity(checkedObj, 2) // 按楼层切换房间颜色
    },
    checkedChange(val) {
      this.checkList = val
      let checkedObj = []
      this.checkList.forEach((item) => {
        this.spaceTypeRoomList.forEach((list) => {
          if (list.functionDictId == item) {
            checkedObj.push(list)
          }
        })
      })
      this.SendToUnity(checkedObj, 2) // 按楼层切换房间颜色
    },
    //  模板下载
    upDownFn() {
      const userInfo = store.state.user.userInfo.user

      let menuList = this.$store.state.menu.routes
      let firstTitle = ''
      let routeList = []
      this.$router.currentRoute.matched.filter(item => item.name).forEach(el => {
        routeList.push(el.meta.title)
      })
      if (menuList.length) {
        firstTitle = menuList[this.$store.state.menu.headerActived].meta.title
        routeList.unshift(firstTitle)
      }

      axios({
        method: 'post',
        url: __PATH.VUE_SPACE_API + 'space/model/exportSpaceModelInfoTemplate',
        data: '',
        responseType: 'arraybuffer',
        headers: {
          'Content-Type': 'application/json;charset=utf-8',
          Authorization: 'Bearer ' + store.state.user.token,
          hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
          unitCode: userInfo.unitCode ?? 'BJSYGJ',
          'operation-type': 4,
          'operation-content': encodeURIComponent(routeList.join(','))

        }
      })
        .then((res) => {
          this.$message.success(res.message || '导出成功')
          this.$tools.downloadFile(res, this)
        })
        .catch((res) => {
          this.$message.error(res.message || '导出失败')
        })
    },
    //  导入
    importFn() {
      this.importDialog = true
    },
    cancelFile() {
      this.importDialog = false
    },

    // VueSendToUnity
    SendToUnity(params, type) {
      let sendData
      switch (type) {
        case 1: // 切换模型
          sendData = JSON.stringify({
            spacesn_area: params[0],
            spacesn_build: params[1],
            spacesn_floor: params[2],
            type: 'openbuildlevel'
          })
          this.$refs.unityIframe.contentWindow.VueSendToUnity(sendData)
          break
        case 2: // 模型房间着色
          let roomDataArr = []
          params.forEach((param) => {
            let rgba = param.functionColour.split(',')
            let roomCodes = param.roomCodes.split(',')
            roomCodes.map((modelRoomCode) => {
              roomDataArr.push({
                roomColorR: rgba[0],
                roomColorG: rgba[1],
                roomColorB: rgba[2],
                roomColorA: rgba[3],
                roomCode: modelRoomCode
              })
            })
          })
          sendData = JSON.stringify({
            data: roomDataArr,
            type: 'changematerial'
          })
          this.$refs.unityIframe.contentWindow.VueSendToUnity(sendData)
          break
        case 3: // 模型房间名称更改
          let roomNameArr = []
          params.forEach((param) => {
            // let roomCodes = param.roomCodes.split(",");
            let roomData = {
              roomName: param.localSpaceName,
              roomCode: param.modelRoomCode
            }
            roomNameArr.push(roomData)
          })
          sendData = JSON.stringify({
            data: roomNameArr,
            type: 'changeroomname'
          })
          this.$refs.unityIframe.contentWindow.VueSendToUnity(sendData)
          break
        case 4: // 更新房间面板信息
          let roomMsgArr = []
          if (params.localSpaceName) {
            roomMsgArr.push({
              d_name: '空间名称',
              d_text: params.localSpaceName
            })
            if (params.functionDictName) {
              roomMsgArr.push({
                d_name: '功能类型',
                d_text: params.functionDictName
              })
            }
            if (params.dmName) {
              roomMsgArr.push({
                d_name: '归属部门',
                d_text: params.dmName
              })
            }
            sendData = JSON.stringify({
              data: roomMsgArr,
              target: params.modelRoomCode,
              state: 'open',
              type: 'mousealert'
            })
            this.$refs.unityIframe.contentWindow.VueSendToUnity(sendData)
          } else {
            return false
          }
          break
      }
    },
    // ------------------------Dialog
    openDialog() {
      this.$refs.sinoDialog.dialogTableVisible = true
      let sendData = JSON.stringify({
        state: 'false',
        type: 'msgstate'
      })
      this.$refs.unityIframe.contentWindow.VueSendToUnity(sendData)
    },
    // 获取模型关联空间详情
    getModelMsg(modelCode) {
      if (!this.$refs.sinoDialog.dialogTableVisible) {
        // 空间台账弹窗显示，不发送请求
        this.formInline.modelCode = this.filters.modelCode + modelCode
        this.$api
          .getMsgByModelCode({
            modelCode: this.formInline.modelCode
          })
          .then((res) => {
            if (res.code == 200) {
              this.formInline = res.data
              this.SendToUnity(this.formInline, 4) // 更新房间模型信息面板
            }
          })
      }
    },
    sureDialog() {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          this.$api
            .updateModelMsg({
              ...this.formInline
            })
            .then((res) => {
              if (res.code == 200) {
                this.$refs.sinoDialog.dialogTableVisible = false
                this.$message({
                  message: res.msg,
                  type: 'success'
                })
                let roomData = [
                  {
                    roomCodes: this.formInline.modelCode.slice(-3),
                    roomName: this.formInline.localSpaceName,
                    functionColour: ''
                  }
                ]
                this.spaceTypeList.forEach((list) => {
                  if (this.formInline.functionDictId == list.id) {
                    roomData[0].functionColour = list.colour
                  }
                })
                this.SendToUnity(roomData, 2) // 切换房间模型颜色
                this.spaceTypeListByModelCodeFn(this.filters.modelCode) // 更新已设置空间功能的列表
                this.roomListFn() // 更新房间列表
                this.closeDialog()
              }
            })
        }
      })
    },
    closeDialog() {
      this.formInline = {
        simName: '',
        modelCode: '',
        localSpaceName: '',
        localSpaceCode: '',
        functionDictId: '',
        hight: '',
        area: '',
        remark: ''
      }
      let sendData = JSON.stringify({
        state: 'true',
        type: 'msgstate'
      })
      this.$refs.unityIframe.contentWindow.VueSendToUnity(sendData)
    },
    fullScreen() {
      this.$refs.unityIframe.contentWindow.fullScreen()
    },
    downImg() {
      // 对指定容器进行截屏
      let unityIframe = this.$refs.unityIframe.contentWindow
      let unityIframeCanvas = unityIframe.document.querySelector('#unityCanvas')
      // 创建一个a标签
      var a = document.createElement('a')
      // 指定下载文件名称
      a.download = '模型图片'
      a.href = unityIframeCanvas.toDataURL('image/png')
      // 模拟点击
      a.click()
    }
  }
}
</script>

<style lang="scss" scoped>
.Iframe_oper_top {
  position: absolute;
  top: 24px;
  left: 270px;
  z-index: 2;
}

.Iframe_oper_left {
  position: absolute;
  top: 80px;
  left: 270px;
  z-index: 2;
  overflow: hidden;
}

.checkbox_group_class {
  height: calc(100% - 50px);
  padding: 5px 10px;
  box-sizing: border-box;
  overflow: auto;
  border: 2px solid #dcdfe6;
  background-color: rgb(255 255 255 / 80%);
}

.checkbox_block {
  display: block !important;
  margin-bottom: 5px;
}

.Iframe_class {
  width: 100%;
  height: 100%;
  border: 0;
  overflow: hidden;
}

/* 定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸 */
.Iframe_oper_left ::-webkit-scrollbar {
  width: 5px !important;
  height: 5px !important;
}

.whole {
  width: 100%;
  height: 100%;
  // padding: 15px;
  .who {
    position: relative;
    height: 100%;
    background-color: #fff;
    border-radius: 5px;
    padding: 10px;
  }
}

.sino_form_input {
  width: 300px;
  margin-right: 10px;
}

.inline-input {
  width: 200px;
  margin-right: 10px;
}

.whole {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;

  .left {
    text-align: center;
    width: 250px;
    height: 100%;
    background-color: #fff;
    border-radius: 5px;
    overflow: hidden;

    // .title_box {
    //   text-align: left;
    // }

    .left_d {
      height: calc(100% - 20px);
    }
  }

  .right {
    width: calc(100% - 260px);
    background-color: #fff;
    height: 100%;
    border-radius: 5px;
    padding: 10px;
  }
}
.title_box {
  box-sizing: border-box;
  padding-left: 24px;
  height: 50px;
  width: 100%;
  line-height: 50px;
  text-align: left;
  border-bottom: 1px solid #d8dee7;

  .title-tip {
    display: inline-block;
    width: 4px;
    height: 16px;
    margin-right: 8px;
    position: relative;
    top: 2px;
    background: #5188fc;
  }

  .title_name {
    font-size: 16px;
    font-weight: bold;
  }

  .title_btn_icon {
    float: right;
  }

  .title_btn_icon i {
    margin-right: 20px;
    cursor: pointer;
  }
}

.btns {
  // float: right;
  // margin-right: 10px;
  width: 250px;
  height: 30px;
  display: flex;
  align-items: center;
  // justify-content: space-between;
}

.btns > span {
  flex: 1;
  background-color: #f6f5fa;
  font-size: 14px;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #ededf5;
  color: #7f848c;
  cursor: pointer;
}

.active-btn {
  background-color: #3562db !important;
  color: #fff !important;
  border-color: #3562db !important;
}

.sino_tree_box {
  margin: 10px;
  height: calc(100% - 100px) !important;
  overflow: auto;

  .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
    background-color: #f5f6fb;
  }

  .el-tree-node__content {
    height: 38px;

    .el-tree-node__label {
      font-size: 15px;
    }
  }
}
</style>
