<template>
  <PageContainer>
    <div slot="header">
      <div class="searchForm">
        <div class="search-box">
          <el-input v-model="alarmTypeQueryParam.name" placeholder="请输入类型名称" clearable> </el-input>
          <div class="ml-16">
            <el-button type="primary" plain @click="resetData">重置</el-button>
            <el-button type="primary" @click="searchClick">查询</el-button>
          </div>
        </div>
        <el-button type="primary" @click="handleListEvent('add')">新增</el-button>
      </div>
    </div>
    <div slot="content" ref="contentRef" class="table-box">
      <div class="tableContainer">
        <el-table v-loading="tableLoading" height="calc(100% - 85px)" :data="tableData" border stripe row-key="id"
          table-layout='auto' class="tableAuto" default-expand-all
          :tree-props="{children: 'child', hasChildren: 'hasChildren'}">
          <el-table-column label="报警类型" prop="name" show-overflow-tooltip width="200"></el-table-column>
          <el-table-column label="标识类型" prop="no" show-overflow-tooltip width="400"> </el-table-column>
          <el-table-column label="报警描述" prop="description" show-overflow-tooltip></el-table-column>
          <el-table-column label=" 操作" width="200">
            <template slot-scope="scope">
              <el-button type="text" @click="handleOperation('edit',scope.row)">编辑</el-button>
              <el-button type="text" @click="handleOperation('delete',scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination :current-page="pagination.page" :page-sizes="[30, 50, 100]" :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper" :total="pageTotal" @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
        </el-pagination>
      </div>
      <el-dialog v-dialogDrag :visible.sync="dialogVisible" :before-close="dialogClosed" :modal="false"
        :close-on-click-modal="false" :title="diaTitle" width="40%" custom-class="model-dialog">
        <div class="diaContent" style="padding: 10px">
          <el-form ref="formInline" :model="formInline" label-width="100px" :rules="rules" label-position="right">
            <el-form-item label="类型名称：" prop="name">
              <el-input v-model.trim="formInline.name" placeholder="请输入类型名称"></el-input>
            </el-form-item>
            <el-form-item label="类型标识：" prop="no">
              <el-input v-model.trim="formInline.no" placeholder="请输入类型标识"></el-input>
            </el-form-item>
            <el-form-item label="上级类型：" prop="parentId">
              <el-select v-model="formInline.parentId" placeholder="请选择上级类型" clearable filterable :disabled="noParentId"
                @change="parentIdChange">
                <el-option v-for="item in parentsList" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="报警描述：" prop="description" class="inputWidth">
              <el-input v-model.trim="formInline.description" maxlength="50" show-word-limit type="textarea"
                placeholder="这是一段报警描述文字，限制五十个字"></el-input>
            </el-form-item>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="dialogClosed">取 消</el-button>
          <el-button type="primary" @click="submit('formInline')">确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </PageContainer>
</template>
<script >
export default {
  name: 'alarmTypeConfiguration',
  data() {
    return {
      pagination: {
        pageSize: 30,
        page: 1,
      },
      diaTitle: '',// 弹窗标题
      pageTotal: 0,
      tableData: [],
      dialogVisible: false, //新增弹窗
      alarmTypeQueryParam: {
        name: '', //类型名称
      },
      formInline: {
        id: '',
        name: '', //报警类型名称
        no: '', // 报警标识编码
        parentId: '', //父级id
        parentNo: "",//父级no
        parentName: '',  //父级名称
        description: '' //报警描述
      },
      rules: {
        name: [{ required: true, message: '请输入类型名称', trigger: 'blur' }],
        no: [{ required: true, message: '请输入类型标识', trigger: 'blur' }],
      },
      noParentId: false,
      parentsList: [],
      tableLoading: false,
    }
  },
  mounted() {
    this.getTableData()
  },
  methods: {
    // 新增
    handleListEvent(type) {
      if (type === 'add') {
        this.diaTitle = '新增报警类型'
        this.getParentTypeData()
        this.formInline = {
          id: '',
          name: '', //报警类型名称
          no: '', // 报警标识编码
          parentId: '', //父级id
          parentNo: "",//父级no
          parentName: '',  //父级名称
          description: '' //报警描述
        }
        this.dialogVisible = true
      }
    },
    getParentTypeData() {
      this.$api.getAlarmTypeParentsList().then((res) => {
        if (res.code == '200') {
          this.parentsList = res.data
        }
      })
    },
    // 获取parentName
    parentIdChange(val) {
      this.formInline.parentName = this.parentsList.find(item => {
        return item.id == val
      }).name
      this.formInline.parentNo = this.parentsList.find(item => {
        return item.id == val
      }).no
    },
    // 列表操作
    handleOperation(type, item) {
      if (type === 'edit') {
        this.diaTitle = '编辑报警类型'
        this.getParentTypeData()
        this.formInline = JSON.parse(JSON.stringify(item))
        this.noParentId = !this.formInline.parentId ? true : false
        this.dialogVisible = true
      } else if (type === 'delete') {
        this.$confirm('是否删除该报警类型配置?', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let params = {
            id: item.id
          }
          this.$api.deleteAlarmTypeData(params).then((res) => {
            if (res.code === '200') {
              this.$message.success(res.message)
              this.getTableData()
            } else {
              this.$message.error(res.message)
            }
          })
        })
      }
    },
    resetData() {
      // 将查询条件置为空
      this.alarmTypeQueryParam = {
        name: ''
      }
      this.getTableData()
    },
    // 确定
    submit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let data = {
            ...this.formInline
          }
          if (data.id) {
            this.$api.updateAlarmTypeData(data).then((res) => {
              if (res.code === '200') {
                this.$message.success(res.message)
                this.getTableData()
              } else {
                this.$message.error(res.message)
              }
            })
          } else {
            this.$api.insertAlarmTypeData(data).then((res) => {
              if (res.code === '200') {
                this.$message.success(res.message)
                this.getTableData()
              } else {
                this.$message.error(res.message)
              }
            })
          }
          this.dialogVisible = false
          this.$refs.formInline.resetFields()
        } else {
          return false
        }
      })

    },
    // 取消
    dialogClosed() {
      this.dialogVisible = false
      this.noParentId = false
      this.$refs.formInline.resetFields()
    },
    getTableData() {
      // 查询
      let data = {
        ...this.pagination,
        ...this.alarmTypeQueryParam
      }
      this.$api.getAlarmTypePageData(data, {}, this.requestHttp).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.records
          this.pageTotal = res.data.total
        }
      })
    },
    searchClick() {
      this.pagination.page = 1
      this.getTableData()
    },
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.pagination.page = 1
      this.getTableData()
    },
    handleCurrentChange(val) {
      this.pagination.page = val
      this.getTableData()
    },

  }
}
</script>
<style lang="scss" scoped>
.searchForm {
  display: flex;
  height: 80px;
  padding: 0 16px;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .el-input {
    width: 200px;
  }

  > div {
    margin-right: 20px;
  }
}

.search-box {
  display: flex;
  align-items: center;
}

.container-content > div {
  height: 100%;
  margin-top: 16px;
}
.el-table {
  margin-bottom: 12px;
}

::v-deep .el-table__row:hover {
  cursor: pointer;
}
.table-box {
  background-color: #fff;
  height: 100%;
  padding: 16px;
  .tableContainer {
    height: 100%;
    .el-table {
      height: calc(100% - 85px) !important;
      margin-bottom: 20px;
    }
  }
}
.ml-16 {
  margin-left: 16px;
}
.diaContent {
  width: 100%;
  max-height: 550px !important;
  overflow: auto;
  background-color: #fff !important;
  .el-input,
  .el-select,
  .el-cascader {
    width: 550px;
  }
}
.inputWidth {
  width: 650px;
}
</style>
