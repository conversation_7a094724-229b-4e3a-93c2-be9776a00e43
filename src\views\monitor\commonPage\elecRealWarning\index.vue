<template>
  <PageContainer>
    <div slot="content" class="monitor-content">
      <div class="monitor-content-left">
        <el-tree
          ref="tree"
          v-loading="treeLoading"
          class="tree_self"
          :data="data"
          :props="defaultProps"
          :filter-node-method="filterNode"
          :default-expanded-keys="expanded"
          node-key="id"
          highlight-current
          @node-click="handleNodeClick"
          @check="treeChecked"
        >
          <template #default="{ node, data }">
            <el-tooltip effect="dark" :disabled="showTooltip" :content="node.label" placement="top">
              <div class="nodeLabel" @mouseover="onMouseOver(data.id)">
                <span :ref="`nodeLabel${data.id}`">{{ node.label }}</span>
              </div>
            </el-tooltip>
          </template>
        </el-tree>
      </div>
      <div class="monitor-content-right">
        <div class="right-heade">
          <el-button
            v-if="monitorData.projectName == '医用气体' && $auth('/medicalGas/filledRecord')"
            type="primary"
            icon="el-icon-document"
            @click="
              () => {
                $router.push('/medicalGas/filledRecord')
              }
            "
          >充氧记录</el-button
          >
          <!-- <el-button type="primary" icon="el-icon-message-solid" @click="closeAudio">消音</el-button> -->
          <div class="alarm-box" @click="openSeach">
            <svg-icon name="general-alarm" class="" />
            <span style="margin-left: 5px">{{ unConfirmCountFormatter }}</span>
          </div>
          <div class="overview-num">
            <div v-for="(item, index) in overviewList" :key="index" class="num-box">
              <div class="entity-status" :style="{ background: item.background, color: item.color }">
                <svg-icon :name="item.icon" /><span>{{ item.text }}</span>
              </div>
              <span class="num-num" :style="{ color: item.numColor }">{{ item.num }}</span>
            </div>
          </div>
          <div class="heade-pattern">
            <div class="pattern-item" @click="handleClick('first')">
              <svg-icon :name="activeTab == 'first' ? 'chartModeActive' : 'chartMode'" class="pattern-icon" />
              <span :style="{ color: activeTab == 'first' ? '#3562DB' : '#414653' }">图形模式</span>
            </div>
            <div class="pattern-item" @click="handleClick('second')">
              <svg-icon :name="activeTab == 'second' ? 'listModeActive' : 'listMode'" class="pattern-icon" />
              <span :style="{ color: activeTab == 'second' ? '#3562DB' : '#414653' }">列表模式</span>
            </div>
          </div>
        </div>
        <div class="right-content">
          <graphics-mode v-if="activeTab == 'first'" ref="scadaShow" :requestHttp="requestHttp" :entityMenuCode="tableCode" :projectId="projectCode" />
          <div v-if="activeTab == 'second'" style="height: 100%">
            <div v-if="ifTable" style="height: 100%">
              <div v-if="cardList.length" style="height: 100%; overflow: auto">
                <div class="card_info">
                  <el-row :gutter="20">
                    <el-col v-for="(val, i) in cardList" :key="i" :span="8" class="card">
                      <el-card class="box-card">
                        <div slot="header" class="box-card-header">
                          <div class="header-title">
                            <div class="echart-title">
                              <div v-if="val.status == '1' || val.status == '2'" class="entity-status status-error"><svg-icon name="icon-error" /><span>异常</span></div>
                              <div v-if="val.status == '6'" class="entity-status status-offline"><svg-icon name="icon-offline" /><span>离线</span></div>
                              <div v-if="val.status == '0'" class="entity-status status-online"><svg-icon name="icon-online" /><span>在线</span></div>
                              <span @click="jumpMonitorDetail(val)">{{ val.surveyEntityName }}</span>
                            </div>
                            <span class="update-time">数据更新时间：{{ val.updateTime }}</span>
                          </div>
                          <div v-if="cardList[i].parameterList.length > 6" class="title-right" @click="openMoreParameter(cardList[i])">更多</div>
                        </div>
                        <div v-for="(o, p) of cardList[i].parameterList.slice(0, 6)" :key="p" class="text item card_options" @click="checkDetails(val)">
                          <div class="info-list">
                            <!-- <img src="@/assets/images/monitor/battery-voltage.png" class="info_list_icon" alt="" /> -->
                            <img v-if="o.icon" :src="$tools.imgUrlTranslation(o.icon)" class="info_list_icon" alt="" />
                            <span v-else class="info_list_icon"></span>
                            <div class="list-param">
                              <div class="parameterName" :title="o.parameterName">
                                {{ o.dictAlias || o.parameterName || '-' }}
                              </div>
                              <div
                                class="parameterUnit"
                                :title="o.parameterValue + o.parameterUnit"
                                :class="{ 'font-red': o.parameterException == '2' || o.parameterException == '1' }"
                              >
                                <svg-icon v-if="o.parameterException == '1'" name="general-alarm-red" />
                                {{ getParameterValue(o) + (o.parameterUnit ? o.parameterUnit : '') || '-' }}
                              </div>
                            </div>
                          </div>
                        </div>
                        <div v-if="!cardList[i].parameterList || cardList[i].parameterList.length == 0" class="img_null">
                          <img style="width: 150px" src="@/assets/images/monitor/null.png" alt="" />
                          <div>暂无数据~</div>
                        </div>
                        <!-- <div class="card-mask"></div> -->
                      </el-card>
                    </el-col>
                  </el-row>
                </div>
                <div class="table-page">
                  <el-pagination
                    style="margin-top: 10px"
                    :current-page="paginationData.currentPage"
                    :page-sizes="[6, 12, 18, 24]"
                    :page-size="paginationData.pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="paginationData.total"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                  ></el-pagination>
                </div>
              </div>
              <div v-else class="echart-null">
                <img src="@/assets/images/monitor/null.png" alt="" />
                <div>暂无数据~</div>
              </div>
            </div>
            <!-- 其他类型表格展示 -->
            <div v-if="!ifTable" class="echart-info">
              <div class="back-btn" @click="backBtn(tabActive)">&lt; 返回</div>
              <div v-if="echartArray.length" class="echart-content">
                <div v-for="(item, index) in echartArray" :key="index" class="echart-box">
                  <eleHistoricalData :requestHttp="requestHttp" :chartsData="item" :index="index"></eleHistoricalData>
                </div>
              </div>
              <div v-else class="echart-null">
                <img src="@/assets/images/monitor/null.png" alt="" />
                <div>暂无数据~</div>
              </div>
            </div>
          </div>
          <alarmList :closeState="advancClose" :requestHttp="requestHttp" :projectCode="projectCode" :isAlarmMode="isAlarmMode" @isCloseState="getData"></alarmList>
          <parameterListDialog v-if="parameterListDialogShow" :cardData="selectCardData" :visible.sync="parameterListDialogShow" />
        </div>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import moment from 'moment'
// import scadaShow from './components/scadaShow'
import { mapGetters } from 'vuex'
import eleHistoricalData from './components/eleHistoricalData'
import alarmList from './components/alarmList'
import parameterListDialog from './components/parameterListDialog'
import graphicsMode from '../../airMenu/components/graphicsMode'
import { monitorTypeList } from '@/util/dict.js'
// import Parameter from '@/views/equipmentCenter/systemSettings/parameter.vue'
export default {
  name: 'elecRealWarning',
  components: {
    // scadaShow,
    graphicsMode,
    eleHistoricalData,
    alarmList,
    parameterListDialog
    // Parameter
  },
  props: {
    projectCode: {
      type: String,
      default: ''
    },
    requestHttp: {
      type: String,
      default: ''
    },
    isAlarmMode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      parameterListDialogShow: false,
      selectCardData: {},
      treeLoading: true,
      defaultProps: {
        children: 'children',
        label: 'name',
        value: 'code'
      },
      searchFrom: {
        entityMenuCode: '' // 菜单code
      },
      activeTab: '',
      tabActive: false,
      advancClose: true,
      ifTable: true,
      echartArray: [], // 历史详情
      cardList: [],
      tableCode: '',
      menuCodes: '',
      tableName: '',
      filterText: '',
      paginationData: {
        currentPage: 1,
        pageSize: 6,
        total: 0
      },
      tableData: [],
      tableLoading: false,
      data: [],
      checkedData: '',
      expanded: [],
      sumList: {
        allCount: 0,
        offline: 0,
        online: 0
      },
      unConfirmCountFormatter: 0,
      timer: '',
      monitorData: monitorTypeList.find((item) => item.projectCode == this.projectCode),
      tableSockectLoad: null,
      countSocketLoad: null,
      overviewList: [
        {
          background: '#E6EFFC',
          color: '#2749BF',
          numColor: '#3562DB',
          icon: 'icon-total',
          text: '总数',
          num: 0
        },
        {
          background: '#E8FFEA',
          color: '#009A29',
          numColor: '#3562DB',
          icon: 'icon-online',
          text: '在线',
          num: 0
        },
        {
          background: '#FFECE8',
          color: '#CB2634',
          numColor: '#CB2634',
          icon: 'icon-error',
          text: '异常',
          num: 0
        },
        {
          background: '#F2F4F9',
          color: '#86909C',
          numColor: '#666666',
          icon: 'icon-offline',
          text: '离线',
          num: 0
        }
      ],
      showTooltip: false
    }
  },
  computed: {
    ...mapGetters({
      socketIemcMsgs: 'socket/socketIemcMsgs'
    })
  },
  watch: {
    nodeLabelMouseOver() {},
    socketIemcMsgs(data) {
      let projectData = JSON.parse(data)
      // 匹配设备相同的接收消息刷新页面
      if (projectData.projectCode == this.projectCode) {
        // 收到消息刷新页面
        this.cardList.forEach((item, index) => {
          if (item.surveyEntityCode == projectData.surveyEntityCode) {
            projectData['updateTime'] = moment().format('YYYY-MM-DD HH:mm:ss')
            Object.assign(item, projectData)
          }
        })
        // if (this.activeTab == 'second' && this.tableSockectLoad == null) {
        //   this.tableSockectLoad = new Promise((resolve, reject) => {
        //     this.getTableData(false, resolve)
        //   }).finally(() => {
        //     this.tableSockectLoad = null
        //   })
        // }
        if (this.countSocketLoad == null) {
          this.countSocketLoad = new Promise((resolve, reject) => {
            this.getCollectCount(resolve)
          }).finally(() => {
            this.countSocketLoad = null
          })
        }
      }
    }
  },
  created() {
    this.init()
  },
  activated() {
    if (this.data.length > 0) {
      this.getTableData()
    } else {
      this.cardList = []
      this.paginationData.total = 0
    }
  },
  mounted() {
    //  测试Websocket功能
    // setTimeout(() => {
    //   let a = '{"projectCode":"01f4bd80e5c060809aae72c7470e8be30","surveyEntityCode":"c62abda6c934491a947d6cc037762e1e","status":"6","surveyEntityName":"干保1层UPS","sort":0,"totalCount":0,"parameterList":[{"parameterId":11271,"scalaId":"","parameterName":"UPS自测状态","parameterValue":"null","parameterUnit":"","parameterColor":"","parameterException":"0","parameterIcon":""},{"parameterId":11270,"scalaId":"","parameterName":"UPS状态","parameterValue":"null","parameterUnit":"","parameterColor":"","parameterException":"0","parameterIcon":""},{"parameterId":11269,"scalaId":"","parameterName":"UPS电池状态","parameterValue":"null","parameterUnit":"","parameterColor":"","parameterException":"0","parameterIcon":""},{"parameterId":11268,"scalaId":"","parameterName":"UPS市电状态","parameterValue":"null","parameterUnit":"","parameterColor":"","parameterException":"0","parameterIcon":""},{"parameterId":11266,"scalaId":"","parameterName":"温度值","parameterValue":"94.5","parameterUnit":"℃","parameterColor":"","parameterException":"1","parameterIcon":""},{"parameterId":11262,"scalaId":"","parameterName":"市电电压频","parameterValue":"50.0","parameterUnit":"Hz","parameterColor":"","parameterException":"0","parameterIcon":""},{"parameterId":11260,"scalaId":"","parameterName":"当前负载对满载百分比","parameterValue":"0.0","parameterUnit":"%","parameterColor":"","parameterException":"0","parameterIcon":""},{"parameterId":11258,"scalaId":"","parameterName":"当前输出电压值","parameterValue":"380.0","parameterUnit":"V","parameterColor":"","parameterException":"0","parameterIcon":""},{"parameterId":11256,"scalaId":"","parameterName":"逆变前最低值","parameterValue":"380.0","parameterUnit":"V","parameterColor":"","parameterException":"0","parameterIcon":""},{"parameterId":11254,"scalaId":"","parameterName":"当前市电电压","parameterValue":"80.0","parameterUnit":"V","parameterColor":"","parameterException":"0","parameterIcon":""}]}'
    //   console.log('定时触发========')
    //   this.$store.commit('socket/setSocketIemcMsgs', a)
    // }, 2000)
  },
  methods: {
    async routeLeave(next) {
      await (this.activeTab = 'second')
      next()
    },
    getParameterValue(o) {
      if (o.parameterValue && o.parameterValue !== 'null') {
        if ([100026, 100028, 100038, 100040, 100048, 100050, 100052].includes(o.parameterId)) {
          return o.parameterValue < 0 ? '-' : o.parameterValue
        } else {
          return o.parameterValue
        }
      }
      return '-'
    },
    // -----------------------------------未读信息相关
    openSeach() {
      this.advancClose = !this.advancClose
    },
    getData(data) {
      this.advancClose = data
    },
    // 获取左侧树
    init() {
      this.getCollectCount()
      this.getCountMonitoringNum()
      this.treeLoading = true
      this.tableLoading = true
      this.$api
        .getEntityMenuList(
          {
            projectId: this.projectCode
          },
          this.requestHttp
        )
        .then((res) => {
          this.treeLoading = false
          this.tableLoading = false
          let list = this.$tools.transData(res.data, 'code', 'parentId', 'children')
          this.data = list
          if (this.data.length > 0) {
            let treeCode = []
            this.data.map((item) => {
              treeCode.push(item.code)
            })
            this.menuCodes = treeCode.join(',')
            this.expanded = [this.data[0].id]
            this.$nextTick(() => {
              this.$refs.tree.setCurrentKey(this.data[0].id)
            })
            this.checkedData = this.data[0]
            this.tableCode = this.data[0].code
            this.tableName = this.data[0].name
            this.tableData = []
            if (this.monitorData.projectName == '医用气体') {
              this.activeTab = 'first'
            } else {
              this.activeTab = 'second'
            }
            this.getTableData()
          } else {
            this.cardList = []
            this.paginationData.total = 0
          }
        })
    },
    // 获取顶部总数2信息
    getCountMonitoringNum() {
      this.$api.CountMonitoringNum({ projectCode: this.projectCode }).then((res) => {
        if (res.code == 200) {
          if (res.data.length) {
            this.overviewList[0].num = res.data[0].devicesNum
            this.overviewList[1].num = res.data[0].onLineNum
            this.overviewList[2].num = res.data[0].abnormalCount
            this.overviewList[3].num = res.data[0].offLineNum
          }
        }
      })
    },
    // 获取总数信息
    getCollectCount(resolve = null) {
      let paramData = {
        startTime: '',
        endTime: '',
        regionCode: '',
        surveyCode: ''
      }
      this.$api.getMonitorStatisticData({ projectCode: this.projectCode, ...paramData, page: 1, pageSize: 10 }, this.requestHttp).then((res) => {
        resolve && resolve()
        this.unConfirmCountFormatter = Number(res.data.unConfirmCountFormatter) > 99 ? '99+' : res.data.unConfirmCountFormatter
        // this.overviewList[2].num = res.data.unConfirmCount
      })
    },
    // 打开更多监测参数列表
    openMoreParameter(card) {
      this.selectCardData = card
      this.parameterListDialogShow = true
    },
    // Tab切换
    handleClick(type) {
      this.activeTab = type
      if (this.activeTab == 'second') {
        this.getTableData()
      }
    },
    // 获取列表信息
    getTableData(showLoading = true, resolve = null) {
      this.tableLoading = showLoading
      this.$api
        .getRealMonitoringList(
          {
            entityMenuCode: this.tableCode,
            projectCode: this.projectCode,
            isHistory: 0,
            page: this.paginationData.currentPage,
            pageSize: this.paginationData.pageSize
          },
          this.requestHttp
        )
        .then((res) => {
          this.tableLoading = false
          resolve && resolve()
          if (res.code == 200) {
            if (res.data.list) {
              this.cardList = res.data.list.map((ele) => {
                return {
                  ...ele,
                  updateTime: moment().format('YYYY-MM-DD HH:mm:ss')
                }
              })
              this.paginationData.total = res.data.totalCount
            } else {
              this.cardList = []
              this.paginationData.total = 0
            }
          }
        })
    },
    /**
     * 打开视屏弹框
     * */
    openVideo(val) {
      this.$api
        .getSurveyParameterOne(
          {
            surveyCode: val.surveyEntityCode,
            userId: this.$store.state.user.userInfo.user.staffId,
            userName: this.$store.state.user.userInfo.user.staffName
          },
          this.requestHttp
        )
        .then((res) => {})
    },
    // 折线图--外部调用
    checkDetailsForWindow(index) {
      this.activeTab = 'second'
      this.tabActive = true
      this.ifTable = false
      this.$api
        .getHistoryList(
          {
            isHistory: 1,
            surveyCode: this.cardList[index].surveyEntityCode
          },
          this.requestHttp
        )
        .then((res) => {
          if (res.code == 200) {
            let arr = res.data
            this.echartArray = arr
          }
        })
      // }
    },
    // 查看数据历史趋势
    jumpMonitorDetail(val) {
      const projectData = this.monitorData
      let path = ''
      if (projectData.projectName == '医用气体') {
        path = '/medicalGas/realtimeMonitor/monitorDetails'
      } else if (projectData.projectName == 'UPS监测') {
        path = '/upsMenu/upsMonitor/monitorDetails'
      } else if (projectData.projectName == '污水监测') {
        path = '/sewageMenu/sewageMonitor/monitorDetails'
      }
      console.log(this.$router)
      if (!path) return
      this.$router.push({
        path: path,
        query: {
          surveyCode: val.surveyEntityCode,
          surveyName: val.surveyEntityName,
          projectCode: projectData.projectCode,
          assetId: val.assetId
        }
      })
    },
    /**
     * 查看数据详情
     */
    checkDetails(val) {
      this.ifTable = false
      this.$api
        .getHistoryList(
          {
            // isHistory: 1,
            surveyCode: val.surveyEntityCode
          },
          this.requestHttp
        )
        .then((res) => {
          if (res.code == 200) {
            let arr = res.data
            this.echartArray = arr
          }
        })
      // }
    },
    // 返回
    backBtn(tabActive) {
      tabActive ? (this.activeTab = 'first') : (this.activeTab = 'second')
      this.ifTable = true
      this.echartArray = []
      this.tabActive = false
    },
    // 查询
    selectList() {
      this.paginationData.currentPage = 1
      this.getTableData()
    },
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    treeChecked(data, checked) {
      this.checkedData = data
      this.$refs.tree.setCheckedNodes([data])
    },
    //  ----------------------------------------------------分页相关------------------------------------------------------------
    handleSizeChange(val) {
      this.paginationData.currentPage = 1
      this.paginationData.pageSize = val
      this.getTableData(this.checkedData.id)
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.getTableData(this.checkedData.id)
    },
    //  ----------------------------------------------------树状结构相关---------------------------------------------------------
    /**
     * 树状图点击
     */
    handleNodeClick(data, checked) {
      this.ifTable = true
      this.tableCode = data.code
      this.tableName = data.name
      this.$refs.tree.setCheckedNodes([data])
      this.paginationData.currentPage = 1
      // this.$store.commit('changeTableLabel', 'search')
      this.checkedData = data
      if (this.activeTab == 'second') {
        this.getTableData()
      } else {
        this.tableCodeRefesh(data.code)
      }
    },
    /**
     * 监测实体tree切换，图纸更新Fn
     */
    async tableCodeRefesh(code) {
      await (this.tableCode = code)
      this.$refs.scadaShow.getScadaList() // 图形SCADA数据
    },
    closeAudio() {
      this.$refs.sinoPanel.closeAudio()
    },
    // 树划入划出效果
    onMouseOver(id) {
      const parentWidth = this.$refs[`nodeLabel${id}`].parentNode.offsetWidth // 获取元素父级可视宽度
      const contentWidth = this.$refs[`nodeLabel${id}`].offsetWidth // 获取元素可视宽度
      this.showTooltip = contentWidth <= parentWidth
    }
  }
}
</script>
<style lang="scss" scoped>
.monitor-content {
  height: 100%;
  display: flex;
  ::v-deep .monitor-content-left {
    width: 246px;
    height: 100%;
    background: #fff;
    border-radius: 4px;
    padding: 16px;
    overflow: auto;
    .el-tree {
      height: 100%;
    }
    .el-tree-node {
      .el-tree-node__content {
        padding: 6px 0;
        height: auto;
      }
    }
    .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
      background-color: #d9e1f8;
    }
  }
  .monitor-content-right {
    height: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
    .right-heade {
      padding: 0 200px 0 10px !important;
      height: 50px;
      margin-left: 16px;
      background: #fff;
      border-radius: 4px;
      position: relative;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      & > button {
        margin: auto 0;
      }
      .heade-pattern {
        display: flex;
        position: absolute;
        right: 16px;
        top: 50%;
        margin: 0 !important;
        transform: translateY(-50%);
        .pattern-item {
          cursor: pointer;
          font-size: 15px;
          .pattern-icon {
            font-size: 16px;
            margin-right: 6px;
          }
        }
        .pattern-item:last-child {
          margin-left: 16px;
        }
      }
      .alarm-box {
        width: 60px;
        height: 30px;
        background: #f53f3f;
        border-radius: 100px;
        display: flex;
        justify-content: center;
        margin: 0 10px;
        cursor: pointer;
        svg,
        span {
          margin: auto 0;
          color: #fff;
        }
        > span {
          font-size: 12px;
          font-family: 'PingFang SC-Medium', 'PingFang SC';
        }
      }
      .overview-num {
        margin-left: 20px;
        display: flex;
        .num-box {
          display: flex;
          align-items: center;
          .num-num {
            margin-left: 16px;
            font-size: 18px;
            font-family: Arial-Regular, Arial;
          }
        }
        .num-box + .num-box {
          padding-left: 36px;
          margin-left: 36px;
          border-left: 1px solid #dcdfe6;
        }
      }
    }
    .right-content {
      flex: 1;
      overflow: hidden;
    }
  }
  .entity-status {
    width: 65px;
    height: 24px;
    border-radius: 4px;
    text-align: center;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    margin-right: 5px;
    flex-shrink: 0;
    span {
      font-size: 14px;
    }
  }
  ::v-deep .card_info {
    padding-top: 30px;
    height: calc(100% - 50px);
    padding: 20px;
    overflow: auto;
    .card {
      height: fit-content;
      margin-bottom: 16px;
      .card_options {
        display: inline-block;
        width: calc(50% - 2px);
        // min-width: 50%;
      }
      .box-card {
        background: #fff;
        border-radius: 4px;
        position: relative;
        .card-mask {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgb(255 255 255 / 80%);
          z-index: 1000;
        }
      }
      .el-card__header {
        height: 80px;
        background: #fff;
        box-shadow: 0 0 5px 0 rgb(18 31 62 / 12%);
        padding: 12px 16px;
        .box-card-header {
          height: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .header-title {
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            flex: 1;
            overflow: hidden;
            // & > span {
            //   text-overflow: ellipsis;
            //   white-space: nowrap;
            //   overflow: hidden;
            // }
            .echart-title {
              display: flex;
              .status-error {
                background: #ffece8;
                color: #cb2634;
              }
              .status-offline {
                background: #f2f4f9;
                color: #86909c;
              }
              .status-online {
                background: #e8ffea;
                color: #009a29;
              }
              & > span {
                cursor: pointer;
                font-size: 15px;
                font-family: 'PingFang SC-Medium', 'PingFang SC';
                font-weight: 500;
                color: #121f3e;
                flex: 1;
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden;
              }
            }
            .update-time {
              font-size: 14px;
              font-family: PingFang SC-Regular, PingFang SC;
              font-weight: 400;
              color: #666666;
              text-overflow: ellipsis;
              white-space: nowrap;
              overflow: hidden;
            }
          }
          .title-right {
            flex-shrink: 0;
            text-align: center;
            font-size: 14px;
            font-family: 'PingFang SC-Medium', 'PingFang SC';
            font-weight: 500;
            color: #fff;
            cursor: pointer;
            display: inline-block;
            width: 48px;
            height: 24px;
            line-height: 24px;
            background: #3562db;
            border-radius: 4px;
          }
        }
      }
      .el-card__body {
        min-height: 255px;
        overflow: hidden;
        padding: 16px 0 !important;
        .info-list {
          display: flex;
          // justify-content: space-between;
          justify-content: flex-start;
          padding: 0 20px;
          padding-right: 0;
          height: 74px;
          overflow: hidden;
          .info_list_icon {
            width: 40px;
            height: 40px;
            display: inline-block;
            margin: auto 0;
            color: #fd9a5e;
            font-size: 24px;
            text-align: center;
          }
          .list-param {
            margin: 6px 10px;
            max-width: 70%;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            .parameterUnit {
              color: #333;
              font-size: 18px;
              font-weight: bold;
              margin-bottom: 2px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              .svg-icon {
                vertical-align: baseline;
              }
            }
            .parameterName {
              color: #666;
              font-size: 14px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            .font-red {
              color: #fa4764;
            }
          }
        }
      }
    }
    .el-row {
      overflow-y: auto;
    }
  }
  .echart-null {
    margin: 0 auto;
    width: 50%;
    text-align: center;
    color: #8a8c8f;
  }
  .echart-info {
    height: calc(100% - 0px);
    h1 {
      text-align: center;
      font-size: 20px;
      padding: 20px;
    }
    .el-row {
      overflow: auto;
    }
    .back-btn {
      padding: 10px 20px;
      line-height: 20px;
      color: #5188fc;
      cursor: pointer;
    }
    .echart-content {
      height: calc(100% - 40px);
      overflow-y: scroll;
    }
    .echart-box {
      box-sizing: border-box;
      width: calc(100% - 5px);
      // width: 100%;
      height: calc(45% - 2px);
      float: left;
      // padding: 5px;
      margin: 5px;
      background: #f5f6fb;
      box-shadow: 2px 2px 10px 0 #e4e4ec;
    }
  }
  .img_null {
    margin: 0 auto;
    width: 50%;
    text-align: center;
    color: #8a8c8f;
  }
  ::v-deep .el-pagination {
    .btn-next,
    .btn-prev {
      background: transparent;
    }
    .el-pager li {
      background: transparent;
    }
  }
}
.nodeLabel {
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
</style>
