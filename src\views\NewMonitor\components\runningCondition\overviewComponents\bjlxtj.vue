<!-- 运行监测 -->
<template>
    <ContentCard :title="item.componentTitleShow" :scrollbarHover="true" :cstyle="{ width: '100%', height: '100%' }"
        class="drag_class" :hasMoreOper="['edit']"
        @more-oper-event="(val) => $emit('all-more-Oper', val, item.componentName, item.chartType)">
        <div slot="title-right" class="data-btns">
            <span :class="{ 'active-btn': totalCostDateType == 'all' }" @click="changeTime('all')">全部</span>
            <span :class="{ 'active-btn': totalCostDateType == 'day' }" @click="changeTime('day')">今日</span>
            <!-- <span :class="{ 'active-btn': totalCostDateType == 'week' }" @click="changeTime('week')">本周</span> -->
            <span :class="{ 'active-btn': totalCostDateType == 'month' }" @click="changeTime('month')">本月</span>
            <span :class="{ 'active-btn': totalCostDateType == 'year' }" @click="changeTime('year')">本年</span>
        </div>
        <div slot="content" style="height: 300px;">
            <echarts :ref="`bjlxtj${item.componentDataType}`" :domId="`bjlxtj${item.componentDataType}`" width="100%"
                height="100%" style="margin-top: -50px;" />
        </div>
    </ContentCard>
</template>
<script>
import moment from 'moment'
import * as echarts from 'echarts'
export default {
    name: 'bjlxtj',
    props: {
        item: {
            type: Object,
            default: () => { }
        },
        systemCode: {
            type: String,
            default: ''
        },
    },
    data() {
        return {
            hasChart: false,
            totalCostDateType: 'all',
            dateTime: {
                startTime: '',
                endTime: '',
            },
            hourDayOrMouth: "",
            deviceId: "",
        }
    },
    mounted() { },
    methods: {
        changeTime(type) {
            this.totalCostDateType = type
            if (type == 'all') {
                this.dateTime.startTime = ''
                this.dateTime.endTime = ''
            } else if (type == 'day') {
                this.hourDayOrMouth = 1
                this.dateTime.startTime = moment().format('YYYY-MM-DD 00:00:00');
                this.dateTime.endTime = moment().format('YYYY-MM-DD 23:59:59');
            } else if (type == 'week') {
                this.dateTime.startTime = moment().startOf('isoWeek').format('YYYY-MM-DD 00:00:00');
                this.dateTime.endTime = moment().endOf('isoWeek').format('YYYY-MM-DD 23:59:59');
            } else if (type == 'month') {
                this.hourDayOrMouth = 2
                this.dateTime.startTime = moment().startOf('month').format('YYYY-MM-DD  00:00:00')
                this.dateTime.endTime = moment().endOf('month').format('YYYY-MM-DD  23:59:59')
            } else if (type == 'year') {
                this.hourDayOrMouth = 3
                this.dateTime.startTime = moment().startOf('year').format('YYYY-MM-DD  00:00:00')
                this.dateTime.endTime = moment().endOf('year').format('YYYY-MM-DD  23:59:59')
            }
            this.bjlxtjFunction(this.deviceId)
        },
        bjlxtjFunction(id) {
            this.deviceId = id
            let params = {
                objectId: id,
                projectCode: this.systemCode,
                alarmType: "",//报警类型
                startTime: this.dateTime.startTime,
                endTime: this.dateTime.endTime,
                hourDayOrMouth: this.hourDayOrMouth,//趋势跨度(0:小时 1:日 2:月 3:年)
            }
            let newArr = []
            this.$api.getSelectDiffProjectCodeAlarm('', params).then((res) => {
                if (res.code === '200') {


                    (res.data?.policeList ?? []).forEach((item) => {
                        newArr.push({
                            name: item.alarmTypeName,
                            value: item.count
                        })
                    })
                    this.hasChart = true
                    this.appendEchartsData(newArr)
                } else {
                    this.hasChart = false
                    this.$message.error(res.message)
                }
            })
        },
        appendEchartsData(data) {

            const baseData = {
                tooltip: {
                    trigger: 'item'
                },
                legend: {
                    top: 'center',
                    right: '0%',
                    orient: 'vertical',
                    data: data.map((item) => item.name),
                    formatter: (name) => {
                        let item = data.find((v) => v.name == name)
                        return `${name}  ： ${item.value}个`
                    }
                },
                series: [
                    {
                        name: "",
                        type: 'pie',
                        radius: '50%',
                        center: ['30%', '50%'], // 将饼图向左移动
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        },
                        data: data,
                        label: {
                            formatter: '{c}', // {b}是名称，{c}是值
                            position: 'outside', // 标签位置
                            show: true // 显示标签
                        }
                    }
                ]
            }
            // 如果数据为空，设置图表为一个空状态
            if (data.length === 0) {
                baseData.series[0].data = []; // 清空数据
                baseData.legend.data = []; // 清空图例数据
                this.hasChart = false; // 设置状态为无图表
            } else {
                this.hasChart = true; // 有数据时设置为有图表
            }
            this.$refs[`bjlxtj${this.item.componentDataType}`].init(baseData)
        }
    }
}
</script>
<style lang="scss" scoped>
.data-btns {
    position: absolute;
    right: 7%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &>span {
        width: 44px;
        height: 20px;
        line-height: 20px;
        text-align: center;
        margin: 0 4px;
        background-color: #f6f5fa;
        font-size: 14px;
        font-family: 'PingFang SC-Medium', 'PingFang SC';
        border: none;
        border-radius: 2px;
        color: #7f848c;
        cursor: pointer;
    }

    .active-btn {
        background-color: #e6effc !important;
        color: #3562db !important;
        border-color: #e6effc !important;
    }
}

.operation-list {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
}

.bjlxtj {
    flex: 1;
    width: 100%;
    height: 200px;
    margin-top: 5px;
    margin-left: -15px;
}
</style>