<!-- 运行监测 -->
<template>
  <ContentCard :title="item.componentTitleShow" :scrollbarHover="true" :cstyle="{ height: '100%' }" class="drag_class"
    :hasMoreOper="['edit']" @more-oper-event="(val) => $emit('all-more-Oper', val, 'operationalAnalysis')">

    <div slot="content" class="operation-list">
      <div v-for="(item, index) in operationList" v-if="index === 0" :key="item.id" class="list-item"
        @click="detailed(item)" style="cursor: pointer;">
        <div class="item-left">
          <div class="eq_data">
            <div class="eq_data_p">{{ item.value }}</div>
          </div>
          <img class="left-icon"
            :src="$tools.imgUrlTranslation(item.icon) || '../src/assets/images/newMonitor/statistics.png'">
        </div>
      </div>
    </div>
  </ContentCard>
</template>

<script>
import gykgg_icon from '@/assets/images/newMonitor/statistics.png'
import dykgg_icon from '@/assets/images/monitor/dykgg_icon.png'
import byq_icon from '@/assets/images/monitor/byq_icon.png'
import zlp_icon from '@/assets/images/monitor/zlp_icon.png'
import lx_icon from '@/assets/images/monitor/lx_icon.png'
import zcsb_icon from '@/assets/images/monitor/zcsb_icon.png'
import alarm_statistics from '@/assets/images/monitor/alarm-statistics.png'
import alarmTypeIconRed from '@/assets/images/operationPort/alarmTypeIconRed.png'

const iconMap = {
  gykgg: gykgg_icon,
  dykgg: dykgg_icon,
  byq: byq_icon,
  zlp: zlp_icon,
  lx: lx_icon,
  zcsb: zcsb_icon,
  alarm: alarm_statistics,
  alarmRed: alarmTypeIconRed
}

export default {
  name: 'deviceOperationMonitor',
  props: {
    item: {
      type: Object,
      default: () => { }
    },
    systemCode: {
      type: String,
      default: ''
    },
    dataType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      operationList: []
    }
  },
  created() {
    this.getEquipmentOperationMonitor()
  },
  methods: {
    detailed(val) {
      let path = 'operationalMonitoring'
      this.$forceUpdate();
      if (!path) return
      this.$router.push({
        path: path,
        query: {
          skipSign: val.skipSign,
        }
      })
    },
    getEquipmentOperationMonitor(id) {
      this.$api
        .getOperationalOverview({
          systemCode: this.systemCode,
          componentDataTypes: this.dataType,
          groupId: id || "",
        })
        .then((res) => {
          if (res.code == 200) {
            this.operationList = res.data.attributes
          }
        })
    },
    getIconSrc(iconType) {
      return iconMap[iconType] || gykgg_icon // 如果没有匹配的图标，返回默认图标
    }
  }
}
</script>

<style lang="scss" scoped>
.operation-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .list-item {
    // background-color: #faf9fc;
    border-radius: 4px;
    padding-top: 2px;
    padding-bottom: 2px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    .item-left {
      display: flex;
      width: 100%;
      align-items: center;
      justify-content: space-between;
      padding-left: 10px;

      .eq_data {
        display: flex;
        flex-direction: column;
        justify-content: center;

        .eq_data_p {
          font-size: 32px;
          font-weight: bold;
          line-height: 32px;
        }

        span {
          font-size: 18px;
          line-height: 18px;
          font-weight: bold;
        }

        &_p {
          width: 90px;
          font-size: 18px;
          font-weight: bold;
          line-height: 36px;
        }
      }

      .left-icon {
        width: 40px;
        height: 40px;
      }
    }
  }
}
</style>
