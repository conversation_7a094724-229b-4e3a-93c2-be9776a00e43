<template>
  <PageContainer>
    <div slot="content" class="courseContent">
      <div class="topFilter">
        <div class="backBar">
          <span style="cursor: pointer" @click="goBack">
            <i class="el-icon-arrow-left"></i>
            课程详情
          </span>
        </div>
      </div>
      <div class="center_details">
        <div class="courseInfo">
          <el-image class="courseImg" :src="classHourInfo.coverUrl" :preview-src-list="[classHourInfo.coverUrl]">
          </el-image>
          <div class="info_right">
            <p class="courseName">
              {{ classHourInfo.courseName }}
            </p>
            <span><img src="@/assets/images/tag.png" alt="" />{{
              classHourInfo.subjectName
            }}</span>
            <p class="describe">
              {{ classHourInfo.comments }}
            </p>
            <div class="info_bottom">
              <span class="key">讲课老师：</span>
              <span class="value">{{ classHourInfo.createName }}</span>
              <span class="key">学习次数：</span>
              <span class="value">{{ classHourInfo.viewCount || 0 }}次</span>
              <span class="key">上传时间：</span>
              <span class="value">{{ classHourInfo.createTime }}</span>
            </div>
          </div>
        </div>

        <div class="table_content">
          <div class="table_top">
            <h1>课程目录</h1>
            <div v-if="classHourInfo.studyState == '2'" class="collect">未学习</div>
            <div class="courseStatus" v-if="classHourInfo.studyState == '0'">
              <img src="@/assets/images/time.png" alt="" />
              学习中
            </div>
            <span class="studyOK" v-if="classHourInfo.studyState == '3'"><img src="@/assets/images/pass.png"
                alt="" />已学完</span>
          </div>
          <div class="table_center">
            <div class="item" v-for="(item, index) in classHourInfo.coursePeriodDTOList" :key="index">
              <div>
                <img src="@/assets/images/doc.png" alt="" />
                <span>{{ index + 1 }}.{{ item.periodName }}</span>
              </div>
              <div v-if="classHourInfo.studyState != '1'">学习进度：&nbsp;&nbsp;&nbsp;{{ item.periodSpace }}%</div>
              <div v-if="classHourInfo.studyState != '1'">课后习题:
                <span class="questionsNum" @click="questionsAnswerDetails(item)">{{ item.questionCount || 0 }}题
                  <img class="questionImg" src="@/assets/images/question.png" alt="" />
                </span>
              </div>
              <div v-if="classHourInfo.studyState != '1'" class="questionsNum"
                @click="seeCourseware(item, classHourInfo.coursePeriodDTOList)">查看课件</div>
            </div>
          </div>
        </div>
        <div class="btnFooter" v-if="classHourInfo.studyState == '1'">
          <el-button class="addCourse" @click="collectCourse">加入课程学习</el-button>
        </div>
      </div>
      <!-- 答题详情 -->
      <answer-info ref="answerInfo" :drawerDialog="drawerDialog" :seeType="seeType" @closeDrawer="closeDrawer">
      </answer-info>
    </div>
  </PageContainer>
</template>
<script>
  import moment from "moment";
  import answerInfo from '../../courseIndex/components/answerInfo.vue'
  export default {
    components: {
      answerInfo
    },
    data() {
      return {
        routeInfo: {},
        id: '',
        moment,
        classHourInfo: {
          courseName: '',
          coverUrl: '',
          subjectName: '',
          comments: '',
          createName: '',
          viewCount: '',
          createTime: '',
          studyState: '',
          coursePeriodDTOList: []
        },
        paginationData: {
          startSize: 1,
          pageSize: 15,
          total: 0,
        },
        drawerDialog: false,
        seeType: 'my',
        activeName: ''
      };
    },
    created() {
      this.routeInfo = JSON.parse(sessionStorage.getItem('routeInfo'))
      this.id = this.$route.query.id || ''
      this.activeName = this.$route.query.activeName || ''
      if (this.id) {
        this.getQuestionsDetails()
      }
    },
    methods: {
      // 获取课程详情
      getQuestionsDetails() {
        let data = {
          courseId: this.id,
          userId: this.routeInfo.userId
        }
        this.$api.myCourseDetails(data).then((res) => {
          if (res.code == '200') {
            this.classHourInfo = res.data
          } else {
            this.$message.error(res.msg)
          }
        });
      },
      // 收藏课程
      collectCourse() {
        console.log(this.classHourInfo, ' this.classHourInfo');
        this.$confirm('是否要加入该课程?', '提示', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let data = {
            courseId: this.id,
            userId: this.routeInfo.userId
          }
          this.$api.collectCourse(data).then((res) => {
            if (res.code == 200) {
              this.$router.go(-1)
            } else {
              this.$message.error(res.msg);
            }
          });
        })
      },
      // 查看答题详情
      questionsAnswerDetails(item) {
        if (!item.questionCount) {
          return this.$message.warning('没有课后习题哦！');
        }
        if (!item.viewState) { // 如果没有完成课时学习去完成课时学习
          return this.$message.warning('还未完成课时学习！');
        } else if (item.viewState && item.periodSpace != '100') { //如果完成课时学习但没有考试，去考试
          this.$router.push({
            path: 'answerIng',
            query: {
              periodId: item.id, //课时id
              courseId: item.courseId, //课程id
            }
          })
        } else { //已经完成课时并且考试，去看详情
          this.drawerDialog = true
          this.$refs.answerInfo.getUserList(item)
        }

      },
      // 关闭
      closeDrawer(val) {
        this.drawerDialog = val
      },
      // 查看课件
      seeCourseware(item, list) {
        if (item.type == 1) {
          this.$router.push({
            path: 'videoPlay',
            query: {
              courseId: item.courseId,
              id: item.id,
              frequency: this.classHourInfo.frequency || 0, //认证时间
            }
          })
        } else {
          this.$router.push({
            path: 'seeFile',
            query: {
              itemInfo: JSON.stringify(item),
              frequency: this.classHourInfo.frequency || 0, //认证时间
            }
          })
        }
      },
      goBack() {
        this.$router.go(-1)
      }
    },
  };

</script>
<style>
  .process-tooltip {
    width: 712px;
    height: 528px;
  }

</style>
<style lang="scss" scoped>
  .table-content {
    background: #fff;
    border-radius: 4px;
    height: calc(100% - 0px);

    .baseInfo {
      padding: 24px;

      .contenter {
        padding-top: 24px;
        display: flex;
        font-size: 14px;

        img {
          width: 160px;
          height: 90px;
        }

        .center {
          flex: 1;
          margin: 0 24px;
        }

        .right {
          flex: 1;
        }

        .item {
          .key {
            color: #666;
            display: inline-block;
            width: 100px;
            text-align: left;
          }
        }

        .item:nth-child(2) {
          margin: 24px 0;
        }
      }
    }
  }

  .topFilter {
    padding: 15px;
    height: 60px;
    background-color: #fff;

    .backBar {
      color: #121f3e;
      height: 30px;
      border-bottom: 1px solid #dcdfe6;
    }
  }

  .center_details {
    width: 56%;
    height: calc(100% - 50px);
    margin: 0 auto;

    .courseInfo {
      display: flex;
      margin-bottom: 32px;

      .courseImg {
        width: 372px;
        height: 210px;
        margin-right: 24px;
      }

      .info_right {
        flex: 1;
        font-size: 14px;
        color: #999;

        .courseName {
          overflow: hidden;
          -webkit-line-clamp: 2;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          line-height: 24px;
          color: #333;
          margin-bottom: 16px;
          font-weight: 600;
          font-size: 20px;
        }

        span {
          img {
            vertical-align: middle;
          }
        }

        .describe {
          height: 100px;
          line-height: 18px;
          margin: 10px 0;
        }

        .info_bottom {
          .value {
            color: #666;
            margin-right: 24px;
          }
        }
      }
    }

    .btnFooter {
      height: 50px;

      .addCourse {
        width: 100%;
        height: 40px;
        color: #fff;
        border-color: #3562db;
        background: #3562db;
        font-weight: 400;
        font-size: 14px;
        font-family: PingFangSC-Regular;
      }

      .addCourse:hover {
        color: #fff;
        font-family: PingFangSC-Regular;
        border-color: rgba(53, 98, 219, 0.8);
        background-color: rgba(53, 98, 219, 0.8);
        font-weight: 500;
      }
    }
  }

  .courseContent {
    height: 100%;
    margin-top: 16px;
    background-color: #fff;

    // padding: 24px;
    .table_content {
      height: calc(100% - 330px);
      font-size: 14px;

      .table_top {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .courseStatus {
          width: 79px;
          height: 24px;
          background: #3562db;
          border-radius: 4px;
          color: #fff;
          text-align: center;
          line-height: 24px;

          img {
            vertical-align: middle;
          }
        }
      }

      .table_center {
        padding: 16px;
        height: calc(100% - 20px);
        overflow: auto;
      }

      .item {
        height: 56px;
        border-bottom: 1px solid #e5e6eb;
        display: flex;
        align-items: center;
        justify-content: space-between;

        >div:nth-child(1) {
          width: 50%;
        }

        >div:nth-child(2) {
          width: 20%;
        }

        >div:nth-child(3) {
          width: 20%;
        }

        img {
          margin-right: 10px;
          vertical-align: middle;
        }

        span {
          color: #7f848c;
        }
      }
    }
  }

  .questionsNum {
    color: #3562db !important;
    margin-left: 10px;
    cursor: pointer;

    .questionImg {
      width: 16px !important;
      height: 16px !important;
      vertical-align: middle;
    }
  }

  .studyOK {
    width: 79px;
    height: 24px;
    line-height: 24px;
    background: #e8ffea;
    border-radius: 4px;
    color: #009a29;
    text-align: center;

    img {
      vertical-align: middle;
      margin-right: 8px;
    }
  }

  .collect {
    width: 58px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    background-color: #f2f4f9;
    cursor: pointer;
    color: #999999;
  }

</style>
