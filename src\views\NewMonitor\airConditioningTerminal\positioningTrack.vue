<template>
  <PageContainer>
    <div slot="header">
      <div class="searchForm">
        <div class="control-btn-header">
          <el-date-picker v-model="requestInfo.dataRange" type="daterange" unlink-panels range-separator="至"
            start-placeholder="开始日期" end-placeholder="结束日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd" clearable
            @change="timeListData" />
          <div style="display: inline-block">
            <el-button type="primary" plain @click="reset">重置</el-button>
            <el-button type="primary" @click="search">查询</el-button>
          </div>
        </div>
      </div>
    </div>
    <div slot="content" ref="contentRef" class="table-box">
      <div class="tableContainer">
        <TablePage ref="table" v-loading="tableLoading" :showPage="true" :tableColumn="tableColumn" height="420"
          :data="tableData" :pageData="pageData" @pagination="paginationChange" :pageProps="pageProps" />
      </div>

    </div>
  </PageContainer>
</template>
<script lang="jsx">
import moment from 'moment'
moment.locale('zh-cn')
export default {
  name: 'emergencyTeam',
  props: {
    factoryCode: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      tableLoading: false,
      requestInfo: {
        dataRange: [], // 时间范围
      },
      hasChart: false,
      tableData: [],
      pageData: {
        pageNo: 1,
        pageSize: 15,
        total: 0
      },
      pageProps: {
        page: 'pageNo',
        pageSize: 'pageSize',
        total: 'total'
      },
    }
  },
  computed: {
    tableColumn() {
      return [
        {
          prop: '',
          label: '序号',
          formatter: (scope) => {
            return (this.pageData.pageNo - 1) * this.pageData.pageSize + scope.$index + 1
          },
          width: 100
        },
        {
          prop: 'timestamp',
          label: '时间'
        },
        {
          prop: 'spaceLocationText',
          label: '空间位置',
        },
        {
          prop: 'guaranteeCode',
          label: '坐标',
          render: (h, row) => {
            return (
              <div>
                X:{row.row.x} Y:{row.row.y}
              </div>
            )
          }
        }
      ]
    },
  },
  mounted() {
    this.getLocation()
  },
  methods: {
    timeListData(val) {
      this.requestInfo.dataRange[0] = val[0] + ' 00:00:00'
      this.requestInfo.dataRange[1] = val[1] + ' 23:59:59'
    },
    // 获取定位列表
    getLocation() {
      let param = {
        factoryCode: this.factoryCode,
        startTime: this.requestInfo.dataRange[0],
        endTime: this.requestInfo.dataRange[1],
        pageSize: this.pageData.pageSize,
        pageNo: this.pageData.pageNo
      }
      this.tableLoading = true
      this.tableData = []
      this.$api
        .getAssetsPositionPage(param)
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.records
            this.pageData.total = res.data.total
          }
          this.tableLoading = false
        })
        .catch(() => {
          this.tableLoading = false
        })
    },
    // 分页
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getLocation()
    },
    // 重置查询
    reset() {
      this.pageData = {
        pageNo: 1,
        pageSize: 15,
        total: 0
      }
      this.requestInfo.dataRange = [] // 时间范围
      this.getLocation()
    },
    // 查询
    search() {
      this.getLocation()
    },
  }
}
</script>
<style lang="scss" scoped>
.searchForm {
  display: flex;
  height: 50px;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-left: -13px;

  .control-btn-header {
    padding: 0;

    &>div {
      margin-right: 10px;
      margin-top: 10px;
    }

    .btn-item {
      border: 1px solid #3562db;
      color: #3562db;
      font-family: none;
    }

    .btn-active {
      color: #fff;
      background: #3562db;
    }
  }
}

.result {
  .resultImgBox {
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    img {
      width: 80px;
      height: 80px;
      margin-left: 10px;
    }
  }
}

.container-content>div {
  height: 100%;
  margin-top: 16px;
}

.el-table {
  margin-bottom: 12px;
}

::v-deep .el-table__row:hover {
  cursor: pointer;
}

.table-box {
  background-color: #fff;
  height: 100%;

  .tableContainer {
    height: 100%;

    .el-table {
      height: calc(100% - 96px) !important;
    }
  }

  .heade-info {
    padding: 0 0 14px 16px;

    .heade-p {
      display: inline-block;
      width: 25%;
      font-size: 14px;
      line-height: 35px;

      em {
        font-style: normal;
      }
    }
  }
}

.diaContent {
  width: 100%;
  max-height: 550px !important;
  overflow: auto;
  background-color: #fff !important;

  .el-input,
  .el-select,
  .el-cascader {
    width: 300px;
  }
}

.form-item {
  display: inline-block;
  margin-right: 20px;
}

.inputWidth {
  width: 820px;
}

.ml-16 {
  margin-left: 16px;
}

.dialog .el-dialog {
  width: 60% !important;
}
</style>