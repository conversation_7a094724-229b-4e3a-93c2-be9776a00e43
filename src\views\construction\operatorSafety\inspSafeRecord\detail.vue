<template>
  <pageContainer v-loading="pageLoading" footer>
    <div slot="header" class="header_content">
      <div @click="goBack">
        <i class="el-icon-arrow-left"></i>
        <span class="title_text">记录详情</span>
      </div>
    </div>
    <div slot="content" class="page_content">
      <div class="content_top">
        <el-descriptions>
          <div slot="title" style="display: flex; align-items: center">
            <span style="margin-right: 10px">基本信息</span>
          </div>
          <el-descriptions-item label="任务名称">{{ detail.taskPoint.taskName }}</el-descriptions-item>
          <el-descriptions-item label="周期类型">{{ cycleTypeName }}</el-descriptions-item>
          <el-descriptions-item label="巡查日期">{{ detail.taskPoint.workStartTime ? dayjs(detail.taskPoint.workStartTime).format('YYYY-MM-DD') : '' }}</el-descriptions-item>
          <el-descriptions-item label="巡检部门">{{ detail.taskPoint.actualExecutionDeptName }} </el-descriptions-item>
          <el-descriptions-item label="巡检人员">{{ detail.taskPoint.actualExecutionUserName }} </el-descriptions-item>
        </el-descriptions>
      </div>
      <div class="content_bottom">
        <div style="font-size: 16px; font-weight: 700; margin-bottom: 16px">巡检内容</div>
        <el-table :data="recordsList" height="calc(100% - 40px)" style="width: 100%">
          <el-table-column type="index" label="序号"> </el-table-column>
          <el-table-column prop="detailName" :label="projectType == '1' ? '巡检项目' : '巡检内容'" show-overflow-tooltip> </el-table-column>
          <el-table-column :prop="projectType == '1' ? 'content' : 'standardRequirements'" label="巡查要点" show-overflow-tooltip> </el-table-column>
          <el-table-column :label="projectType == '1' ? '巡检内容' : '巡检依据'" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ formate(scope.row) }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="projectType == '0'" label="照片">
            <template slot-scope="scope">
              <el-link type="primary" @click="previewImg(scope.row.pictureUrl)">查看图片</el-link>
            </template>
          </el-table-column>
          <el-table-column v-if="projectType == '0'" label="是否合格">
            <template slot-scope="scope">
              <span>{{ scope.row.normal == '3' ? '不合格' : '合格' }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="projectType == '1'" show-overflow-tooltip label="巡检结果" prop="contentStandard"> </el-table-column>
        </el-table>
      </div>
      <div class="content_top" style="margin-top: 16px">
        <el-descriptions>
          <div slot="title" style="display: flex; align-items: center">
            <span style="margin-right: 10px">巡检执行</span>
          </div>
          <el-descriptions-item label="巡检情况">{{ detail.excute.carryOutFlag == 1 ? '已巡' : '未巡' }}</el-descriptions-item>
          <el-descriptions-item label="检查部门">{{ detail.taskPoint.actualExecutionDeptName }}</el-descriptions-item>
          <el-descriptions-item label="检查人">{{ detail.taskPoint.actualExecutionUserName }}</el-descriptions-item>
          <el-descriptions-item label="实际巡检时间">{{ detail.result.excuteTime ? moment(detail.result.excuteTime).format('YYYY-MM-DD HH:mm:ss') : '' }} </el-descriptions-item>
          <el-descriptions-item label="定位状态">未开启定位 </el-descriptions-item>
        </el-descriptions>
        <el-descriptions>
          <el-descriptions-item label="签名">
            <el-image
              v-if="detail.taskPoint.signatureUrl"
              style="max-height: 120px; max-width: 100px; margin-right: 5px"
              :src="$tools.imgUrlTranslation(detail.taskPoint.signatureUrl)"
              fit="scale-down"
              :preview-src-list="[$tools.imgUrlTranslation(detail.taskPoint.signatureUrl)]"
            ></el-image>
            <span v-else></span>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div class="content_top" style="margin-top: 16px">
        <el-descriptions>
          <div slot="title" style="display: flex; align-items: center">
            <span style="margin-right: 10px">巡检结果</span>
          </div>
          <!-- <el-descriptions-item label="巡检结果">{{
            detail.result.state ? (detail.result.state == '2' ? '合格' : detail.result.state == '3' ? '不合格' : '报修') : ''
          }}</el-descriptions-item> -->
        </el-descriptions>
        <el-descriptions>
          <el-descriptions-item label="现场结果总结">{{ detail.result.desc }}</el-descriptions-item>
        </el-descriptions>
        <el-descriptions>
          <el-descriptions-item label="现场照片和视频">
            <template v-if="detail.taskPoint.imageUrl">
              <el-image
                v-for="(i, index) in detail.taskPoint.imageUrl.split(',')"
                :key="index"
                :src="$tools.imgUrlTranslation(i)"
                :preview-src-list="[$tools.imgUrlTranslation(i)]"
                style="max-height: 100px; max-width: 100px; margin-right: 5px"
              >
                <div slot="error" class="image-slot-error">图片加载失败</div>
              </el-image>
            </template>
            <span v-else></span>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <el-image ref="elImg" :preview-src-list="previewList" style="display: none"></el-image>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="goBack">关闭</el-button>
    </div>
  </pageContainer>
</template>
<script>
import moment from 'moment'
import dayjs from 'dayjs'
export default {
  data() {
    return {
      moment,
      projectType: '', // 1:专业 0:日常
      detail: {
        excute: {},
        project: {
          templateType: ''
        },
        result: {},
        taskPoint: {}
      },
      recordsList: [],
      cycleList: [
        {
          cycleType: '8',
          label: '单次'
        },
        {
          cycleType: '6',
          label: '每日'
        },
        {
          cycleType: '0',
          label: '每周'
        },
        {
          cycleType: '2',
          label: '每月'
        },
        {
          cycleType: '3',
          label: '季度'
        },
        {
          cycleType: '5',
          label: '全年'
        }
      ],
      pageLoading: false,
      previewList: []
    }
  },
  computed: {
    cycleTypeName() {
      if (this.detail.result.cycleType) {
        return this.cycleList.find((el) => el.cycleType == this.detail.result.cycleType).label
      } else {
        return '-'
      }
    }
  },
  mounted() {
    this.getDetail()
  },
  methods: {
    dayjs,
    getDetail() {
      this.pageLoading = true
      this.$api.maintainRecordDetail({ recordId: this.$route.query.id, projectCode: this.$route.query.projectCode }).then((res) => {
        if (res.code == 200) {
          this.projectType = res.data.project.templateType
          this.detail = res.data || { excute: {}, project: { templateType: '' }, result: {}, taskPoint: {} }
          this.recordsList = res.data ? res.data.project.projectdetailsReleaseList : []
        }
        this.pageLoading = false
      })
    },
    handleClick() {},
    goBack() {
      this.$router.go(-1)
    },
    formate(row) {
      if (this.projectType == '1') {
        // 数值
        if (row.isNum == '0') {
          return row.rangeStart + '-' + row.rangeEnd + (row.einheitName || '')
        } else if (['1', '2', '4'].includes(row.isNum)) {
          // 无 || 文本 || 文件上传
          return '无'
        } else if (row.isNum == '3') {
          // 选项
          const option = JSON.parse(row.termJson)
          const contTexts = option.map((i) => i.contText)
          return contTexts.join('、')
        }
      } else {
        return row.inspectionBasis
      }
    },
    previewImg(imgstr) {
      if (imgstr) {
        this.previewList = imgstr.split(',').map((i) => this.$tools.imgUrlTranslation(i))
        const ele = this.$refs.elImg
        this.$nextTick(() => {
          ele.clickHandler()
        })
      } else {
        this.$message.warning('暂无图片')
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.header_content {
  width: 100%;
  height: 60px;
  line-height: 60px;
  padding: 0 16px;
  border-bottom: 1px solid #ccc;
  font-size: 18px;
  > div {
    width: 100px;
    cursor: pointer;
  }
}
.page_content {
  height: 100%;
  width: 100%;
  background: #fff;
  padding: 16px;
  overflow-y: auto;
  .content_bottom {
    height: 40%;
  }
}
.fileContent {
  padding: 10px;
  .fileItem {
    width: 700px;
    padding: 0 10px;
    &:hover {
      background: #4476ff11;
    }
    height: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
