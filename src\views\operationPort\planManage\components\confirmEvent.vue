<template>
  <div class="confirmEvent">
    <div class="confirmEvent-head">
      <p v-show="!confirm_isEditEventName" class="notify-title" @click="editTitleFun('confirm_isEditEventName')">
        {{ confirmData.eventName || '请输入' }}
        <i class="el-icon-edit" style="color: #CCCED3;"></i>
      </p>
      <el-input v-show="confirm_isEditEventName" ref="confirm_isEditEventName" v-model="confirmData.eventName" placeholder="请输入确认事件名称" :maxlength="10" style="width: 200px;" @blur="() => confirm_isEditEventName = false" />
      <el-checkbox v-model="confirmData.showFlag">显示操作按钮</el-checkbox>
    </div>
    <div v-for="(item, index) in confirmEventList" :key="index" class="item-form">
      <div class="item-main">
        <el-checkbox v-show="editContentIndex !== index" v-model="item.checkFlag">
          <p @click.prevent="editContent(index)">
            {{ item.confirmTitle || '请输入' }}
            <i class="el-icon-edit" style="color: #CCCED3;"></i>
          </p>
        </el-checkbox>
        <el-input v-show="editContentIndex === index" ref="editContentInput" v-model="item.confirmTitle" placeholder="请输入" :maxlength="30" style="width: 300px;" @blur="() => editContentIndex = ''" />
        <p class="confirmTip">
          <i class="el-icon-warning"></i>
          <span>{{ item.confirmTip }}</span>
        </p>
        <p v-if="index" class="item-delete" @click="control('delete', index)">
          <i class="el-icon-delete"></i>
          删除
        </p>
      </div>
      <div class="item-foot">
        <el-button v-if="confirmData.showFlag" type="primary" plain style="padding: 6px 16px;">忽略</el-button>
        <el-button v-if="confirmData.showFlag" type="primary" style="padding: 6px 16px;">已到位</el-button>
      </div>
    </div>
    <el-button type="primary" icon="el-icon-plus" plain style="margin-top: 8px;" @click="control('add')">添加事件</el-button>
  </div>
</template>

<script>
export default {
  name: 'confirmEvent',
  data() {
    return {
      confirm_isEditEventName: false, // 修改title
      editContentIndex: '', // 修改内容下标
      confirmData: {
        stepName: '确认事件',
        eventName: '确认事件',
        showFlag: false // 显示按钮
      },
      confirmEventList: [
        {
          checkFlag: true, // 是否通知 0未勾选 1已勾选
          confirmTitle: '指挥人员和扑救是否到位', // 确认内容
          confirmTip: '由中控室值班人员来操作' // 确认提示
        },
        {
          checkFlag: true, // 是否通知 0未勾选 1已勾选
          confirmTitle: '有关人员是否赶赴现场', // 确认内容
          confirmTip: '由中控室值班人员来操作' // 确认提示
        },
        {
          checkFlag: true, // 是否通知 0未勾选 1已勾选
          confirmTitle: '人员疏散、撤离是否完成', // 确认内容
          confirmTip: '由中控室值班人员来操作' // 确认提示
        }
      ] // 确认列表
    }
  },
  computed: {

  },
  created() {

  },
  methods: {
    control(type, index = 0) {
      if (type == 'add') {
        this.confirmEventList.push({
          checkFlag: true,
          confirmTitle: '',
          confirmTip: '由中控室值班人员来操作'
        })
      } else if (type == 'delete') {
        this.confirmEventList.splice(index, 1)
      }
    },
    // 编辑被人
    editContent(index) {
      this.editContentIndex = index
      this.$nextTick(() => {
        this.$refs.editContentInput[0].focus()
      })
    },
    // 编辑标题
    editTitleFun(key) {
      this[key] = true
      this.$nextTick(() => {
        this.$refs[key].focus()
      })
    }
  }
}

</script>

<style lang="scss" scoped>
.confirmEvent {
  padding: 16px;
  background: #FAF9FC;
  border-radius: 4px;
  margin-bottom: 16px;
  p{
    margin: 0px;
  }
  .confirmEvent-head {
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .notify-title {
      cursor: pointer;
      font-weight: bold;
      font-size: 16px;
      color: #333333;
      line-height: 32px;
      width: 200px;
    }
  }
  .item-form {
    border-bottom: 1px solid #E4E7ED;
    margin-bottom: 16px;
    .item-delete {
      color: #FF6461;
      position: absolute;
      top: 50%;
      right: 16px;
      transform: translateY(-50%);
      cursor: pointer;
    }
    .item-main{
      position: relative;
      padding-bottom: 8px;
      .confirmTip{
        padding-left: 24px;
        margin-top: 10px;
        display: flex;
        align-items: center;
        font-weight: 500;
        font-size: 12px;
        color: #FA8C2B;
        line-height: 17px;
        i {
          font-size: 16px;
        }
      }
      ::v-deep(.el-checkbox) {
        line-height: 32px;
      }
    }
    .item-foot {
      text-align: right;
      padding-bottom: 16px;
    }
  }
}
</style>
