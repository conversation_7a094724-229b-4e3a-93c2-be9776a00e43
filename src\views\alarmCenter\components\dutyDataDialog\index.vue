<!--
 * @Author: hedd
 * @Date: 2023-11-27 19:06:35
 * @LastEditTime: 2023-11-28 15:45:49
 * @FilePath: \ihcrs_pc\src\views\alarmCenter\components\dutyDataDialog\index.vue
 * @Description:
-->
<template>
  <div>
    <el-dialog
      v-if="dialogVisible"
      v-dialogDrag
      :modal="false"
      :close-on-click-modal="false"
      title="值排班"
      width="40%"
      :visible.sync="dialogVisible"
      custom-class="model-dialog"
      :before-close="closeDialog"
    >
      <div class="dialog-content">
        <el-form ref="formInline" :model="formInline" label-width="80px">
          <el-row :gutter="24" style="margin: 0">
            <el-col :md="12">
              <el-form-item label="签到时间">
                <el-input :value="currentDateTime" disabled />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24" style="margin: 0">
            <el-col :md="24">
              <el-form-item label="班次" prop="dutyType">
                <el-radio v-model="formInline.dutyType" label="0">早班</el-radio>
                <el-radio v-model="formInline.dutyType" label="2">中班</el-radio>
                <el-radio v-model="formInline.dutyType" label="1">晚班</el-radio>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24" style="margin: 0">
            <el-col>
              <el-form-item label="签到人员">
                <el-button type="primary" @click="openPeopleDialog">选择</el-button>
                <div>
                  <el-tag v-for="tag in checkedStaffList" :key="tag.signInPersonCode" class="camera-tag" closable @close="tagSpaceHandleClose(tag.signInPersonCode)">
                    {{ tag.signInPersonName }}
                  </el-tag>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" plain @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="submitFormData">确 定</el-button>
      </span>
    </el-dialog>
    <template v-if="peopleDialogVisible">
      <dialogConfig
        :visible="peopleDialogVisible"
        :defaultSelectedUser="defaultSelectedUser"
        @updateVisible="closePeopleDialog"
        @advancedSearchFn="submitPeopleDialog"
      ></dialogConfig>
    </template>
  </div>
</template>
<script>
import moment from 'moment'
export default {
  name: 'dutyDataDialog',
  components: {
    dialogConfig: () => import('../../linkageConfiguration/components/dialogConfig.vue')
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    pageType: {
      type: String,
      default: 'sign'
    },
    currentPropDate: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogVisible: this.visible,
      formInline: {
        dutyType: '0'
      },
      peopleDialogVisible: false,
      defaultSelectedUser: [],
      checkedStaffList: []
    }
  },
  computed: {
    currentDateTime() {
      if (this.pageType === 'sign') {
        return moment().format('YYYY-MM-DD')
      } else if (this.pageType === 'duty') {
        return this.currentPropDate
      } else {
        return this.currentPropDate
      }
    }
  },
  mounted() {},
  methods: {
    tagSpaceHandleClose(id) {
      // 根据id 删除checkedStaffList中的对应项
      const index = this.checkedStaffList.findIndex((e) => e.signInPersonCode === id)
      this.checkedStaffList.splice(index, 1)
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', false)
      this.$refs['formInline'].resetFields()
    },
    // 签到提交
    submitFormData() {
      if (!this.checkedStaffList.length) {
        this.$message.warning('请选择签到人员')
        return
      }
      const personCode = Array.from(this.checkedStaffList, ({ signInPersonCode }) => signInPersonCode).toString()
      const personName = Array.from(this.checkedStaffList, ({ signInPersonName }) => signInPersonName).toString()
      let data = {
        ...this.formInline
      }
      let apiName = ''
      if (this.pageType === 'sign') {
        apiName = 'signIn'
        Object.assign(data, {
          signInPersonCode: personCode,
          signInPersonName: personName
        })
      } else {
        apiName = 'saveDutyRota'
        Object.assign(data, {
          dutyDate: this.currentDateTime,
          dutyPersonCode: personCode,
          dutyPersonName: personName
        })
      }
      this.$api[apiName](data).then((res) => {
        this.loading = false
        if (res.code == 200) {
          this.$message.success(res.message)
          this.closeDialog()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    openPeopleDialog() {
      this.defaultSelectedUser = Array.from(this.checkedStaffList, ({ signInPersonCode }) => signInPersonCode)
      this.peopleDialogVisible = true
    },
    closePeopleDialog() {
      this.peopleDialogVisible = false
    },
    submitPeopleDialog(data) {
      this.checkedStaffList = data.map((item) => {
        return {
          signInPersonCode: item.id,
          signInPersonName: item.staffName
        }
      })
      this.peopleDialogVisible = false
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  background: #fff;
  padding: 10px;
  max-height: 500px;
  overflow-y: auto;
  .camera-tag {
    background: #f6f5fa;
    border-radius: 4px;
    font-size: 14px;
    font-family: 'PingFang SC-Regular', 'PingFang SC';
    font-weight: 400;
    color: #121f3e;
    border: none;
    margin-right: 8px;

    ::v-deep .el-tag__close {
      color: #121f3e;

      &:hover {
        color: #fff;
        background-color: #3562db;
      }
    }
  }
}
</style>
