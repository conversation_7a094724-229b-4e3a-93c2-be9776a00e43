<template>
  <el-dialog title="班车选择" :visible.sync="epuipmentVisible" width="55%" :before-close="closePinot" custom-class="model-dialog" :close-on-click-modal="false" append-to-body>
    <div class="outermost">
      <div v-loading="loading" class="content">
        <div class="topTools">
          <el-input v-model.trim="trainNo" placeholder="请输入车次编号" style="width: 200px; margin-right: 10px"></el-input>
          <el-select v-model="busType" placeholder="请选择班车类型" style="width: 200px; margin-right: 10px">
            <el-option v-for="item in regularBusList" :key="item.code" :label="item.name" :value="item.code"> </el-option>
          </el-select>
          <el-select v-model="passengerType" placeholder="请选择乘客类型" style="width: 200px; margin-right: 10px">
            <el-option v-for="item in userTypeList" :key="item.code" :label="item.name" :value="item.code"> </el-option>
          </el-select>
          <el-button type="primary" plain @click="reset">重 置</el-button>
          <el-button type="primary" @click="searchList">查 询</el-button>
        </div>
        <el-table
          ref="personTable"
          v-loading="tableLoading"
          :data="tableData"
          border
          height="100%"
          stripe
          style="width: 100%"
          row-key="id"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" :reserve-selection="true"></el-table-column>
          <el-table-column prop="trainNo" show-overflow-tooltip label="车次编号"></el-table-column>
          <el-table-column prop="lineCode" show-overflow-tooltip label="路线编号"></el-table-column>
          <el-table-column prop="lineName" align="center" show-overflow-tooltip label="路线名称"></el-table-column>
          <el-table-column prop="busTypeAlias" show-overflow-tooltip label="班车类型"></el-table-column>
          <el-table-column prop="passengerTypeAlias" show-overflow-tooltip label="乘客类型"></el-table-column>
          <el-table-column prop="departTime" show-overflow-tooltip label="发车时间"></el-table-column>
        </el-table>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <div class="footer">
        <div class="footer_left">
          <el-pagination
            class="dictionary-list__pagination"
            :current-page="pagination.page"
            :page-sizes="pagination.pageSizeOptions"
            :page-size="pagination.size"
            :layout="pagination.layoutOptions"
            :total="pagination.total"
            @size-change="paginationSizeChange"
            @current-change="paginationCurrentChange"
          ></el-pagination>
        </div>
        <div class="footer_right">
          <el-button type="primary" plain @click="closePinot">取 消</el-button>
          <el-button type="primary" :disabled="addDis" @click="editSubmit">确认</el-button>
        </div>
      </div>
    </span>
  </el-dialog>
</template>
<script>
import { transData } from '@/util'
import tableListMixin from '@/mixins/tableListMixin'
import store from '@/store/index'
export default {
  props: {
    epuipmentVisible: {
      type: Boolean,
      default: false
    },
    regularBusList: {
      type: Array,
      default: () => []
    },
    userTypeList: {
      type: Array,
      default: () => []
    },
    tableDate: {}
  },
  mixins: [tableListMixin],
  data() {
    return {
      trainNo: '', // 车次编码
      busType: '', // 班车类型
      passengerType: '', // 乘客类型
      tableLoading: false,
      multipleSelection: [],
      tableData: [], // table列表
      loading: false,
      addDis: false
    }
  },
  mounted() {
    this.getDataList()
  },

  methods: {
    closePinot() {
      this.$emit('busClose')
    },
    editSubmit() {
      if (this.multipleSelection.length > 0) {
        this.$emit('sure', this.multipleSelection)
        this.closePinot()
      } else {
        this.$message.error('请选择班车')
      }
    },
    handleSelectionChange(val) {
      if (val.length > 1) {
        const del_row = val.shift()
        this.$refs.personTable.toggleRowSelection(del_row, false)
      }
      this.multipleSelection = val
    },
    // 获取列表数据
    getDataList() {
      this.tableLoadingStatus = true
      const params = {
        pageSize: this.pagination.size,
        pageNo: this.pagination.current,
        trainNo: this.trainNo,
        busType: this.busType,
        passengerType: this.passengerType
      }
      this.$api.fileManagement
        .trainConfigurationList(params)
        .then((res) => {
          if (res.code === '200') {
            this.tableData = res.data.records
            this.pagination.total = res.data.total
          } else {
            throw res.msg || res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取列表数据失败'))
        .finally(() => {
          this.tableLoadingStatus = false
        })
    },
    // 条件查询
    searchList() {
      this.pagination.current = 1
      this.getDataList()
    },
    reset() {
      this.trainNo = ''
      this.busType = ''
      this.passengerType = ''
      this.pagination.pageNo = 1
      this.pagination.pageSize = 15
      this.getDataList()
    }
  }
}
</script>

<style lang="scss" scoped>
.outermost {
  display: flex;
  width: 100%;
  height: 100%;
}

.left {
  padding: 10px;
  width: 268px;
  margin-right: 10px;
  height: 100%;
  background-color: #fff;

  .block {
    height: calc(100% - 80px);
    overflow: auto;
  }
}

.footer {
  width: 100%;
  display: flex;
}

.footer_left {
  width: 80%;
}

.footer_right {
  width: 20%;
}

.content {
  background-color: #fff;
  padding: 10px;
  width: 100%;
  height: 100%;
}

.topTools {
  margin-bottom: 15px;
}

.user-pagination {
  text-align: right;
  position: absolute;
  left: 10px;
  bottom: 20px;
}

.tree-title {
  border: 1px solid #ebeef5;
  width: 40%;
  margin-bottom: 5px;
  line-height: 30px;
  border-radius: 4px;
  text-align: center;
  color: #5188fc;
  cursor: pointer;
}

.tree-title-active {
  background: #5188fc;
  border-color: #5188fc;
  color: #fff;
}

::v-deep .el-tree-node__label {
  display: inline-block;
  white-space: normal;
  text-align: left;
}

::v-deep .el-tree-node__content {
  height: auto;
}

::v-deep .el-table--scrollable-y .el-table__body-wrapper {
  overflow-y: visible !important;
}
</style>
