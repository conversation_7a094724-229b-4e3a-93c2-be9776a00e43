<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 150 150"><defs><style>.cls-1{fill:url(#未命名的渐变_74);}.cls-2{fill:url(#未命名的渐变_74-2);}</style><linearGradient id="未命名的渐变_74" y1="75.09" x2="149.81" y2="75.09" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#6b6b6b"/><stop offset="0.01" stop-color="#767676"/><stop offset="0.03" stop-color="#959595"/><stop offset="0.04" stop-color="#aaa"/><stop offset="0.37" stop-color="#ccc"/><stop offset="0.74" stop-color="#eaeaea"/><stop offset="0.94" stop-color="#f6f6f6"/><stop offset="0.95" stop-color="#ededed"/><stop offset="0.96" stop-color="#d4d4d4"/><stop offset="0.97" stop-color="#ababab"/><stop offset="0.99" stop-color="#737373"/><stop offset="0.99" stop-color="#666"/></linearGradient><linearGradient id="未命名的渐变_74-2" x1="17.12" y1="75.09" x2="132.7" y2="75.09" xlink:href="#未命名的渐变_74"/></defs><title>iot-按钮</title><g id="图层_47" data-name="图层 47"><rect class="cls-1" y="0.19" width="149.81" height="149.81" rx="1.23"/><rect class="cls-2" x="17.12" y="17.3" width="115.58" height="115.58"/></g></svg>