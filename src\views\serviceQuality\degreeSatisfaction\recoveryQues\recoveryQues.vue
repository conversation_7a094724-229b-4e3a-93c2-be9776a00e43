<template>
  <div class="main-container">
    <div class="whole">
      <div class="page-title">问卷回收</div>
      <el-row style="border-bottom: 1px solid #d8dee7">
        <el-col :md="16" :lg="16" :xl="14" style="height: auto">
          <BreadcrumbNavBar />
        </el-col>
        <el-col :md="8" :lg="8" :xl="10">
          <HeaderButton />
        </el-col>
      </el-row>
      <div>
        <div style="display: flex" class="outermost">
          <div class="left">
            <div class="collApse">
              <el-collapse v-model="activeName" accordion @change="collChange">
                <el-collapse-item v-for="item in hospitalList" :key="item.umId" :title="item.unitComName" :name="item.umId">
                  <el-tree v-loading="collLoading" :data="treeList" :props="defaultProps" @node-click="handleNodeClick"></el-tree>
                </el-collapse-item>
              </el-collapse>
            </div>
          </div>
          <div class="right">
            <div style="display: flex; margin-bottom: 10px">
              <el-input v-model.trim="form.subName" placeholder="输入姓名" style="width: 200px"> </el-input>
              <el-date-picker
                v-model="form.timeLine"
                type="datetimerange"
                range-separator="至"
                start-placeholder="提交开始日期"
                end-placeholder="提交结束日期"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="margin: 0 10px"
                @change="dateChange"
              ></el-date-picker>
              <el-select v-model="form.answerStatus" placeholder="请选择状态" style="width: 200px">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
              <el-button type="primary" plain @click="reset">重置</el-button>
              <el-button type="primary" @click="inquiry">查询</el-button>
            </div>
            <div style="margin-bottom: 10px; display: flex; justify-content: space-between">
              <div>
                <!-- <el-button type="primary">未答催促</el-button>
                <el-button type="primary">导出答卷</el-button> -->
              </div>
              <div>
                <span class="numbers" style="font-size: 15px">
                  答卷进度统计：
                  <span style="color: #2cc7c5">已答卷人数：{{ submited }}人</span>&nbsp;&nbsp;&nbsp;
                  <span style="color: #ff1919">未答卷人数：{{ unSubmited }}人</span>
                  <!-- <span>回收限制：{{chartsData.maxAnswerCount}}</span> -->
                </span>
              </div>
            </div>
            <div>
              <el-table
                ref="table"
                v-loading="loading"
                :data="tableData.list"
                :height="tableHeight"
                :row-key="getRowKeys"
                :header-cell-style="{ 'text-align': 'center' }"
                tooltip-effect="dark"
                style="width: 100%"
                borderW
                stripe
                @selection-change="handleSelectionChange"
              >
                <el-table-column type="selection" width="55" align="center" :reserve-selection="true"> </el-table-column>
                <el-table-column type="index" label="序号" prop="" width="80" align="center">
                  <template slot-scope="scope">
                    <span>{{ (currentPage - 1) * pageSize + scope.$index + 1 }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="200" align="center">
                  <div slot-scope="scope">
                    <span :class="scope.row.answerStatus == '未答' ? 'not_click' : 'changeStyle'" @click="viewQuestion(scope)">查看答卷</span>
                  </div>
                </el-table-column>
                <el-table-column v-for="col in tableData.questions" :key="col.id" :show-overflow-tooltip="true" min-width="150" :prop="col.id" :label="col.name" align="center">
                </el-table-column>
              </el-table>
              <el-pagination
                :current-page="currentPage"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
                :page-size="pageSize"
                :page-sizes="[15, 30, 50]"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              ></el-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import utils from '../utils/utils'
import { transData, ListTree } from '@/util'
import BreadcrumbNavBar from '../component/BreadcrumbNavBar/BreadcrumbNavBar.vue'
import HeaderButton from '../questionDesign/component/HeaderButton/HeaderButton.vue'
export default {
  components: {
    BreadcrumbNavBar,
    HeaderButton
  },
  data() {
    return {
      collLoading: true,
      loading: true,
      defaultProps: {
        children: 'children',
        label: 'deptName'
      },
      activeName: '',
      pmId: '',
      officeId: '',
      form: {
        subName: '', // 姓名
        timeLine: [], // 时间
        answerStatus: '' // 状态
      },
      startTime: '',
      endTime: '',
      options: [
        {
          value: '1',
          label: '已答'
        },
        {
          value: '0',
          label: '未答'
        }
      ],
      // job: [
      //   {
      //     value: '0',
      //     name: '在职'
      //   },
      //   {
      //     value: '1',
      //     name: '离职'
      //   }
      // ],
      // sex: [
      //   {
      //     value: '0',
      //     name: '女'
      //   },
      //   {
      //     value: '1',
      //     name: '男'
      //   }
      // ],
      // quarters: [
      //   {
      //     id: 27,
      //     postDuty: '空调工',
      //     postName: '空调工'
      //   },
      //   {
      //     id: 28,
      //     postDuty: '',
      //     postName: '综合维修员'
      //   }
      // ],
      pageSize: 15,
      currentPage: 1,
      total: 0,
      tableData: [],
      multipleSelection: [],
      treeList: [],
      spaces: [],
      hospitalList: [],
      submited: '',
      unSubmited: ''
    }
  },
  computed: {
    showDialog: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('updateVisible', val)
      }
    },
    tableHeight() {
      return document.body.clientHeight - 325
    }
  },
  created() {},
  mounted() {
    this.getTreeList()
    this.getLersonnelList()
  },
  methods: {
    viewQuestion(scope) {
      let val = scope.row.answerId
      let answerStatus = scope.row.answerStatus
      if (answerStatus == '未答') return
      utils.setLocalStorage('answerid', val)
      this.$router.push({
        path: '/degreeSatisfaction/questManagement/QuestionView'
      })
    },
    dateChange() {
      if (this.form.timeLine == null) {
        this.form.timeLine = []
        this.startTime = ''
        this.endTime = ''
      } else {
        this.startTime = this.form.timeLine[0]
        this.endTime = this.form.timeLine[1]
      }
    },
    handleNodeClick(val) {
      this.pmId = ''
      this.officeId = val.id
      console.log(this.officeId, 'this.officeId')
      this.getLersonnelList()
    },
    collChange(val) {
      this.officeId = ''
      this.pmId = val
      // this.getLersonnelList()
      if (val != '') {
        this.treeList = []
        this.$api.getSelectedDept({ unitId: val }).then((res) => {
          this.collLoading = false
          if (res.code == 200) {
            this.spaces = res.data
            this.treeList = transData(res.data, 'id', 'pid', 'children')
          }
        })
      } else {
        this.getLersonnelList()
      }
      this.collLoading = true
    },
    getRowKeys(row) {
      return row.id
    },
    getTreeList() {
      this.$api.getSelected({}).then((res) => {
        if (res.code == 200) {
          this.hospitalList = res.data
        }
      })
    },
    getLersonnelList() {
      let params = {
        questionId: localStorage.getItem('questId'),
        pageNum: this.currentPage,
        pageSize: this.pageSize,
        subName: this.form.subName, // 姓名
        startTime: this.startTime, // 开始时间
        endTime: this.endTime, // 结束时间
        answerStatus: this.form.answerStatus,
        userId: this.$store.state.user.userInfo.user.staffId,
        userName: this.$store.state.user.userInfo.user.staffName,
        officeId: this.officeId
      }
      // this.$api.staffList(params, __PATH.USER_CODE).then((res) => {
      this.$api.search(params).then((res) => {
        this.loading = false
        if (res.status == 200) {
          res.data.list.forEach((item) => {
            item.gender = item.gender === '' ? '' : item.gender == 2 ? '女' : '男'
            if (item.answerStatus == 1) {
              item.answerStatus = '已答'
            } else {
              item.answerStatus = '未答'
            }
          })
          this.tableData = res.data
          this.submited = res.data.submited
          this.unSubmited = res.data.unSubmited
          this.total = res.data.total
        }
      })
    },
    // 查询
    inquiry() {
      this.getLersonnelList()
    },
    // 重置
    reset() {
      this.pageSize = 15
      this.currentPage = 1
      this.form.subName = ''
      this.form.answerStatus = ''
      this.startTime = ''
      this.endTime = ''
      this.getLersonnelList()
    },
    handleClose(val) {
      this.$refs.table.clearSelection()
      this.$emit('updateVisible', val)
    },
    qd() {
      if (this.multipleSelection.length == 0) {
        this.$message({
          showClose: true,
          message: '至少选择一个人员',
          type: 'warning'
        })
      } else {
        if (this.selectMode == '2' && this.multipleSelection.length > 1) {
          this.$message({
            message: '仅可选择单个人员',
            type: 'warning'
          })
          return
        }
        this.$emit('advancedSearchFn', this.multipleSelection, this.type, this.messageType)
        this.$refs.table.clearSelection()
        this.$emit('updateVisible', !this.visible)
      }
    },
    // 查询列表数据
    getTableList() {},
    // 监听事件
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 分页
    handleSizeChange(val) {
      this.pageSize = val
      this.getLersonnelList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getLersonnelList()
    }
  }
}
</script>
<style lang="scss" scoped>
.main-container {
  height: 100%;
  padding: 15px;
  margin: 0 !important;
  .whole {
    width: 100%;
    height: 100%;
    border-radius: 5px;
    background-color: #fff !important;
    .page-title {
      height: 50px;
      line-height: 50px;
      color: #606266;
      font-size: 14px;
      padding-left: 25px;
      border-bottom: 1px solid #d8dee7;
    }
    .submit-container {
      display: flex;
      justify-content: center;
      padding-bottom: 20px;
    }
    .el-button--text {
      color: #fff;
      padding: 12px 10px;
    }
    .el-button--text:hover,
    .el-button:hover {
      color: #fff;
      background-color: #5188fc;
      border-radius: 0;
      border-color: transparent;
    }
  }
}
.collApse {
  width: 240px;
  height: 100%;
  padding: 10px;
  background-color: #fff;
  overflow: auto;
  white-space: nowrap;
  /* 防止内容换行 */
  overflow: hidden;
  /* 超出部分隐藏 */
  position: relative;
  /* 相对定位，伪元素定位基于此 */
  cursor: default;
  /* 设置鼠标样式 */
}
.collApse::after {
  content: attr(data-tooltip);
  /* 使用自定义属性作为提示文本 */
  position: absolute;
  white-space: nowrap;
  background-color: #f9f9f9;
  border: 1px solid #ccc;
  box-shadow: 2px 2px 6px #00000029;
  padding: 5px;
  border-radius: 5px;
  display: none;
  /* 默认不显示提示 */
  z-index: 10;
}
@media screen and (max-width: 1366px) {
  .main-container .el-button--text {
    color: #fff;
    padding: 0 15px;
  }
}
.outermost {
  width: 100%;
  height: 100%;
  border: 1px solid #eee;
  padding: 10px;
}
.left {
  padding-right: 10px;
  width: 230px;
  height: calc(100vh - 220px);
  > div {
    width: 100%;
    padding: 10px;
    height: 100%;
    background-color: #fff;
    overflow: auto;
  }
}
.right {
  background-color: #fff;
  padding: 10px;
  width: calc(100% - 230px);
  height: 100%;
}
.el-pagination {
  // display: flex;
  // justify-content: flex-end;
  margin-top: 5px;
}
.model-dialog {
  padding: 10 !important;
}
::v-deep .el-select {
  width: 100px !important;
}
.el-select {
  width: 180px !important;
  margin-right: 10px;
}
::v-deep .model-dialog .el-dialog__body {
  overflow-y: hidden !important;
}
.not_click {
  color: #ccc;
}
.changeStyle {
  color: #2cc7c5;
}
.changeStyle:hover {
  text-decoration: underline;
  cursor: pointer;
}
</style>
