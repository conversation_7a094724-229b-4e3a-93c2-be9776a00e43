<template>
  <div class="main">
    <el-dialog
      v-dialogDrag
      custom-class="mainDialog"
      append-to-body
      :visible.sync="changeMalfunctionShow"
      :close-on-click-modal="false"
      :before-close="closeDialog"
      class="personDialog"
    >
      <template slot="title">
        <span class="dialog-title">选择故障原因和维修方法</span>
      </template>
      <div class="dialog-content" style="width: 100%;">
        <div v-show="treeData.length" class="content-title">提示：右键点击选项可添加故障原因和维修方法</div>
        <el-tree
          ref="MalTree"
          check-on-click-node
          show-checkbox
          class="tree"
          node-key="id"
          default-expand-all
          :data="treeData"
          :props="defaultProps"
          @node-contextmenu="nodeRightClick"
          @node-click="handleNodeClick"
        ></el-tree>
        <div v-show="popBox" class="popBox" :style="boxStyle">
          <p @click="addMalfunction('1')">添加故障原因</p>
          <p @click="addMalfunction('2')">添加维修方法</p>
        </div>
        <el-button v-show="treeData.length === 0" class="sino-button-sure no-tree-data-btn" type="primary" @click="addMalfunction('1')">新增故障原因</el-button>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="sino-button-sure" @click="closeDialog">取 消</el-button>
        <el-button class="sino-button-sure" type="primary" @click="savePeople">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 新增故障原因 -->
    <el-dialog v-dialogDrag :visible.sync="addDialogBoxShow" :close-on-click-modal="false" :append-to-body="true">
      <template slot="title">
        <span class="dialog-title">{{ addMalType === '1' ? '新增故障原因' : '新增维修方法' }}</span>
      </template>
      <div class="dialog-content" style="width: 100%;">
        <div v-if="addMalType === '1'" style="margin-bottom: 20px;">
          <span style="margin: 0 10px;">故障原因：</span>
          <el-input
            v-model="causeReason"
            style="width: 317px; margin-right: 20px;"
            placeholder="请输入故障原因"
            maxlength="25"
            onkeyup="if(value.length>25)value=value.slice(0,25)"
          ></el-input>
        </div>
        <div v-if="addMalType === '2'" style="margin-bottom: 20px;">
          <div v-for="(item, ind) in repairMethodList" :key="ind" class="repair-input">
            <span style="margin: 0 10px;">维修方法：</span>
            <span v-if="ind === 0" class="span-plus" @click="spanPlus">+</span>
            <span v-else class="span-reduce" @click="spanReduce(ind)">-</span>
            <el-input
              v-model="repairMethod[ind]"
              style="width: 317px; margin-right: 20px;"
              placeholder="请输入维修方法"
              maxlength="25"
              onkeyup="if(value.length>25)value=value.slice(0,25)"
            ></el-input>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="sino-button-sure" @click="addDialogBoxShow = false">取 消</el-button>
        <el-button class="sino-button-sure" type="primary" @click="saveReason(addMalType)">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: 'malfunction',
  props: {
    changeMalfunctionShow: {
      type: Boolean,
      default: false
    },
    malfunctionData: {
      type: Object,
      default: function () {
        return {}
      }
    },
    itemServiceCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      popBox: false,
      addDialogBoxShow: false,
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'text'
      },
      clickRow: {},
      boxStyle: {
        top: 0,
        left: 0
      },
      repairMethodList: 0,
      addMalType: '1',
      causeReason: '',
      repairMethod: []
    }
  },
  watch: {},
  mounted() {
    this.getTreeData()
  },
  methods: {
    getTreeData() {
      const params = {
        itemServiceCode: this.itemServiceCode,
        type: '1'
      }
      this.$api.getMalfunctionReasonMethod(params).then((res) => {
        this.treeData = this.$tools.listToTree(res, 'id', 'parent')
      })
    },
    handleNodeClick(data) {
      this.popBox = false
    },
    nodeRightClick(event, data, node, element) {
      // console.log(event, data, node, element)
      if (Object.keys(data).includes('state') && data.state.opened) {
        this.popBox = true
        this.boxStyle = {
          top: event.layerY + 90 + 'px',
          left: event.layerX + 'px'
        }
        this.clickRow = {
          id: data.id,
          name: data.text
        }
      }
    },
    addMalfunction(type) {
      // console.log(type)
      this.popBox = false
      this.addMalType = type
      this.addDialogBoxShow = true
      this.causeReason = ''
      if (type === '2') {
        this.repairMethodList = 0
        this.repairMethod = []
        this.spanPlus()
      }
    },
    // 增加 耗材
    spanPlus() {
      this.repairMethod.push('')
      this.repairMethodList++
    },
    spanReduce(ind) {
      if (this.repairMethodList === 1) {
        return false
      }
      this.repairMethod.splice(ind, 1)
      this.repairMethodList--
    },
    // 取消按钮
    closeDialog() {
      this.$emit('closeDialog')
    },
    // 确认按钮
    savePeople() {
      const list = this.$refs.MalTree.getCheckedNodes(true)
      if (list.some((e) => e.parent === '#')) {
        return this.$message({
          message: '请选择维修方法！',
          type: 'warning'
        })
      }
      const selectList = list.map((e) => {
        const parentRow = this.treeData.filter((item) => item.id === e.parent)
        return {
          reasonId: parentRow[0].id,
          reasonName: parentRow[0].text,
          methodId: e.id,
          methodName: e.text
        }
      })
      // console.log(selectList)
      this.$emit('malfunctionSure', selectList)
    },
    saveReason(type) {
      console.log(this.causeReason, this.repairMethod)
      if (!this.causeReason && !this.repairMethod.length) {
        return this.$message({
          message: '请输入数据后再提交！',
          type: 'warning'
        })
      }
      const params = {
        itemServiceCode: this.itemServiceCode,
        type: type,
        name: this.causeReason
      }
      if (type === '2') {
        Object.assign(params, {
          name: this.repairMethod.toString(),
          parentName: this.clickRow.name,
          parentId: this.clickRow.id
        })
      }
      this.saveAndUpdate(params)
    },
    saveAndUpdate(params) {
      this.$api.saveMalfunctionReasonMethod(params).then((res) => {
        if (res.code === '200') {
          this.addDialogBoxShow = false
          this.getTreeData()
        } else {
          this.$message({
            message: res.data.message,
            type: 'warning'
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
::v-deep .mainDialog {
  .el-dialog__body {
    padding: 10px 20px;
  }

  .el-dialog__footer {
    padding-right: 30px;
  }

  .dialog-title {
    color: #3562db;
    font-family: PingFangSC-Medium, "PingFang SC";
  }

  .dialog-title::before {
    content: "";
    display: inline-block;
    width: 2px;
    border-radius: 1px;
    height: 13px;
    background: #3562db;
    margin-right: 10px;
  }
}

.main {
  ::v-deep .el-dialog {
    width: 30%;

    .el-dialog__body {
      padding: 10px 20px;
    }

    .el-dialog__footer {
      padding-right: 30px;
    }
  }

  ::v-deep .el-table-column--selection .cell {
    padding-left: 0.875rem;
    padding-right: 0.875rem;
    text-overflow: initial;
  }

  .dialog-content {
    width: 100%;

    .content-title {
      color: rgb(194 177 144);
      margin-bottom: 10px;
    }

    .popBox {
      width: 120px;
      height: 60px;
      background: #2b2b45;
      border: 1px solid #9d928d;
      color: #fff;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 99;

      p {
        height: 30px;
        line-height: 30px;
        text-align: center;
        cursor: pointer;
        font-size: 0.875rem;
      }

      p:hover {
        color: #ffe3a6;
      }
    }

    .no-tree-data-btn {
      position: absolute;
      top: 60px;
      left: 30px;
    }

    .repair-input {
      position: relative;
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      .span-plus {
        font-size: 24px;
        color: #ffe3a6;
        cursor: pointer;
        margin-right: 10px;
      }

      .span-reduce {
        font-size: 32px;
        color: #f00;
        cursor: pointer;
        margin-right: 14px;
      }
    }
  }
}

.el-button {
  height: 38px;
}
</style>
