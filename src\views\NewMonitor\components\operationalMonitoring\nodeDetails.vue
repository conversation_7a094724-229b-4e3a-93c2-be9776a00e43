<template>
  <el-dialog
    v-if="dialogShow"
    :title="title"
    width="50%"
    :visible.sync="dialogShow"
    custom-class="model-dialog"
  >
    <div style=" width: 100%; height: 100%;">
      <div slot="content" v-loading="loading" class="card-content">
        <div class="details_header">
          <div class="details_header_item">
            <p>当前监控区域:</p>
            <span>{{equipmentData.surveyName}}</span>
          </div>
          <div class="details_header_item">
            <p>监测设备位置:</p>
            <span>{{equipmentData.locate}}</span>
          </div>
          <div class="details_header_item">
            <p>监测设备类型:</p>
            <span>{{equipmentData.deviceType}}</span>
          </div>
        </div>
        <el-row class="details_content">
          <el-col v-for="(item, index) in equipmentData.paramList" :key="index" :span="12" class="details_content_item">
            <p>{{ item.paramName }}</p>
            <span>{{ item.paramValue }}{{ item.paramUnit }}</span>
          </el-col>
        </el-row>
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'normalDialog',
  props: {

  },
  data() {
    return {
      dialogShow: false,
      equipmentData: {},
      title: ''
    }
  },
  mounted() {

  },
  methods: {
    getEchartData(data, projectCode) {
      this.title = `监测节点设备详情(${data.surveyName})`
      this.getList(data.surveyCode, projectCode)
      this.dialogShow = true
    },
    getList(surveyCode, projectCode) {
      this.loading = true
      this.$api.querySurveyDetail({surveyCode, projectCode}).then(res => {
        if (res.code == 200) {
          res.data.paramList = [
            ...res.data.paramList,
            ...[
              {
                paramName: '报警状态',
                paramUnit: '',
                paramValue: res.data.warnStatus == '1' ? '正常' : '报警',
                parameterId: ''
              },
              {
                paramName: '设备状态',
                paramUnit: '',
                paramValue: res.data.deviceStatus == '1' ? '正常' : '异常',
                parameterId: ''
              }
            ]
          ]
          this.equipmentData = res.data
          this.loading = false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.details_content{
  padding: 16px;
  background: #FAF9FC;
  .details_content_item{
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    span::before{
      content: ':';
      margin-right: 5px;
    }
    p{
      width: 70px;
      font-size: 14px;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: 400;
      color: #666666;
      margin: 0;
    }
    span{
      font-size: 14px;
      font-family: PingFang SC-Medium, PingFang SC;
      font-weight: 500;
      color: #333333;
    }
  }
}
.card-content{
  padding: 0 8px;
  .details_header{
    display: flex;
    flex-direction: column;
    .details_header_item{
      display: flex;
      p{
        font-size: 14px;
        color: #666666;
        margin-right: 10px;
      }
      span{
        font-size: 14px;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        color: #333333;
      }
    }
  }
}
::v-deep .model-dialog .el-dialog__body{
  background: #FFFFFF;
}
</style>
