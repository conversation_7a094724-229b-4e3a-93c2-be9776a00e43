<!--
 * @Description: 保安工作
-->
<template>
  <div class="securityWork">
    <div v-for="(item, index) in taskGroup" :key="index" class="security-list">
      <div class="list-header">
        <span class="header-title">{{item.name}}</span>
        <span class="header-type">(今日)</span>
      </div>
      <div class="list-line"></div>
      <div class="list-content">
        <div v-if="index < 3" class="list-content-static">
          <div class="content-left">
            <div class="content-left-total">
              <svg-icon name="cockpit-static" />
              <span class="right-text">总数</span>
              <span class="right-num">{{item.total}}</span>
            </div>
            <div class="content-left-toBeExecuted">
              <svg-icon name="cockpit-static-red" />
              <span class="right-text">待执行</span>
              <span class="right-num">{{item.unFinish}}</span>
            </div>
          </div>
          <div class="content-right">
            <div class="content-right-perfection">
              <span>当前计划完成度</span>
              <span class="perfection-num">{{item.rateCount}}</span>
            </div>
            <div class="content-right-progress">
              <div class="enerty-box"></div>
              <div class="progress-group">
                <div
                  v-for="(box, bI) in boxGroup"
                  :key="bI"
                  :class="{
                    'progress-group-box': true,
                    'progress-group-box-active': bI < (item.finish / item.total * 25) || 0
                  }"
                >
                  <div v-if="bI < (item.finish / item.total * 25) || 0" class="active-box"></div>
                </div>
              </div>
              <div class="enerty-box"></div>
            </div>
            <div class="content-right-function">
              <div>
                <span class="function-executed-num">已执行数量</span>
                <span>{{item.finish}}</span>
              </div>
              <div>
                <span class="function-total-num">总数</span>
                <span>{{item.total}}</span>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="list-content-peon">
          <div v-for="(peopele, pi) in peopleList" :key="pi" class="people-nem-box">
            <div class="nem-box-img"></div>
            <div class="nem-box-static">
              <span class="static-text">{{ peopele.name }}</span>
              <span class="static-num">{{ peopele.value }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'securityWork',
  data() {
    return {
      boxGroup: new Array(25).fill(0),
      taskGroup: [
        {
          name: '治安巡更',
          finish: '0',
          rateCount: '0%',
          total: '0',
          unFinish: '0'
        },
        {
          name: '治安巡更',
          finish: '0',
          rateCount: '0%',
          total: '0',
          unFinish: '0'
        },
        {
          name: '治安巡更',
          finish: '0',
          rateCount: '0%',
          total: '0',
          unFinish: '0'
        },
        {
          name: '岗位监督'
        }
      ],
      peopleList: [
        {
          name: '应到人数',
          value: 0
        },
        {
          name: '实到人数',
          value: 0
        },
        {
          name: '当前离岗人数',
          value: 0
        }
      ]
    }
  },
  mounted() {
    this.getTaskCountByType()
  },
  methods: {
    getTaskCountByType() {
      this.$api.getTaskCountByType().then((res) => {
        if (res.code == '200') {
          const data = res.data
          data.forEach((item, index) => {
            this.$set(this.taskGroup, index, item)
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.securityWork {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
  overflow: hidden;
  padding: 0px 0px;
  justify-content: space-around;
  .security-list {
    width: 100%;
    height: 18%;
    display: flex;
    .list-header {
      height: 100%;
      width: 21%;
      display: flex;
      flex-direction: column;
      justify-content: space-evenly;
      color: #dceaff;
      text-align: center;
      .header-title {
        font-size: 16px;
      }
      .header-type {
        font-size: 14px;
      }
    }
    .list-line {
      width: 1px;
      background: linear-gradient(225deg, rgba(104, 168, 255, 0) 0%, #a4caff 49%, rgba(104, 168, 255, 0) 100%);
    }
    .list-content {
      width: calc(79% - 1px);
      height: 100%;
      .list-content-static {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: space-between;
        padding: 0px 10px 0 20px;
        .content-left {
          display: flex;
          flex-direction: column;
          justify-content: space-around;
          .content-left-total,
          .content-left-toBeExecuted {
            height: 50%;
            display: flex;
            align-items: center;
            .right-text {
              font-size: 14px;
              color: #1ffaff;
              margin: 0 4px;
            }
            .right-num {
              font-size: 16px;
              color: #ffffff;
            }
          }
        }
        .content-right {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          padding-left: 15px;
          .content-right-perfection {
            width: 100%;
            height: 30%;
            line-height: 100%;
            border-radius: 5px;
            font-size: 14px;
            color: #ffffff;
            .perfection-num {
              color: #1eebf1;
              font-size: 16px;
              margin-left: 5px;
            }
          }
          .content-right-progress {
            width: 100%;
            height: 30%;
            border-radius: 5px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            .enerty-box {
              height: 12px;
              width: 8px;
              border: 1px solid #184a8f;
            }
            .progress-group {
              height: 12px;
              width: calc(100% - 24px);
              background: linear-gradient(90deg, #0085fd 0%, #09f4c4 100%);
              border: 1px solid #184a8f;
              display: flex;
              .progress-group-box {
                height: 100%;
                width: 5%;
                background: #031b45;
              }
              .progress-group-box-active {
                background: center;
                .active-box {
                  width: 100%;
                  height: 100%;
                  background: transparent;
                  border: 1px solid #031b45;
                }
              }
            }
          }
          .content-right-function {
            width: 100%;
            height: 30%;
            border-radius: 5px;
            display: flex;
            justify-content: space-between;
            font-family: DIN, DIN;
            font-weight: 500;
            font-size: 16px;
            color: #ffffff;
            .function-executed-num {
              font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
              font-weight: 400;
              font-size: 14px;
              color: #34ffb2;
              margin-right: 5px;
            }
            .function-total-num {
              font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
              font-weight: 400;
              font-size: 14px;
              color: #8ebeff;
              margin-right: 5px;
            }
          }
        }
      }
      .list-content-peon {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: space-between;
        padding: 0px 10px 0 20px;
        .people-nem-box {
          width: fit-content;
          flex-shrink: 0;
          height: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .nem-box-img {
            height: 50%;
            aspect-ratio: 1/1;
            background: url('~@/assets/images/safetyDataCockpit/nem-box-icon.png') no-repeat;
            background-size: 100% 100%;
          }
          .nem-box-static {
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            align-items: center;
            padding: 0 8px;
            .static-text {
              font-size: 14px;
              color: #94b7ff;
            }
            .static-num {
              font-size: 20px;
              color: #ffffff;
            }
          }
        }
      }
    }
  }
}
</style>
