/*
 * @Author: hedd
 * @Date: 2023-02-27 14:35:19
 * @LastEditTime: 2023-03-11 14:21:39
 * @FilePath: \IHCRS_alarm\src\mixins\tableListMixin.js
 * @Description:
 */
import { throttle } from 'lodash/function'
export default {
  data() {
    return {
      tableHeight: 0, // table高度
      pagination: {
        size: 15, // 每页请求条数
        current: 1, // 当前页码
        total: 0, // 总条数
        layoutOptions: 'total, sizes, ->, prev, pager, next, jumper',
        // layoutOptions: 'total, sizes, prev, pager, next, jumper',
        pageSizeOptions: [15, 30, 50, 100]
      } // 分页信息
    }
  },
  created() {
    this.$nextTick(() => {
      this.tableHeight = document.querySelector('.table-content')?.clientHeight ?? 0
    })
  },
  mounted() {
    window.addEventListener('resize', throttle(() => this.resize(), 400))
  },
  destroyed() {
    window.removeEventListener('resize', throttle(() => this.resize(), 400))
  },
  methods: {
    resize() {
      let dom = document.querySelector('.table-content')
      if (dom) {
        this.tableHeight = document.querySelector('.table-content').clientHeight
      }
    },
    // 分页数量变化事件
    paginationSizeChange(size) {
      this.pagination.size = size
      this.getDataList()
    },
    // 分页页码变化事件
    paginationCurrentChange(current) {
      this.pagination.current = current
      this.getDataList()
    }
  }
}
