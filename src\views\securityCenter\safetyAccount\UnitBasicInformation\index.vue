<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="content_box">
        <div class="toptip">
          <span class="green_line"></span>
          基本概况
        </div>
        <div class="basic">
          <el-form ref="form" :model="form" label-width="180px" :inline="true">
            <el-form-item label="医院名称:">
              <el-input v-model="form.hospitalName" disabled></el-input>
            </el-form-item>
            <el-form-item label="地址:">
              <el-input v-model="form.address" maxlength="50" show-word-limit></el-input>
            </el-form-item>
            <el-form-item label="院区数量:">
              <el-input v-model="form.hospitalAreaNumber" maxlength="10" show-word-limit></el-input>
            </el-form-item>
            <el-form-item label="编制人员:">
              <el-input v-model="form.staffNumber" maxlength="10" show-word-limit></el-input>
            </el-form-item>
            <el-form-item label="医院领导姓名:">
              <el-input v-model="form.hospitalLeaderName" maxlength="10" show-word-limit></el-input>
            </el-form-item>
            <el-form-item label="院领导电话:">
              <el-input v-model="form.hospitalLeaderPhone" maxlength="11" show-word-limit></el-input>
            </el-form-item>
            <el-form-item label="总务处长姓名:">
              <el-input v-model="form.generalLeaderName" maxlength="10" show-word-limit></el-input>
            </el-form-item>
            <el-form-item label="总务处长电话:">
              <el-input v-model="form.generalLeaderPhone" maxlength="11" show-word-limit></el-input>
            </el-form-item>
            <el-form-item label="日门诊量(人次):">
              <el-input v-model="form.singleNumber" maxlength="10" show-word-limit></el-input>
            </el-form-item>
            <el-form-item label="保卫处长姓名:">
              <el-input v-model="form.defendLeaderName" maxlength="10" show-word-limit></el-input>
            </el-form-item>
            <el-form-item label="保卫处长电话:">
              <el-input v-model="form.defendLeaderPhone" maxlength="11" show-word-limit></el-input>
            </el-form-item>
            <el-form-item label="床位数(张):">
              <el-input v-model="form.bedNumber" maxlength="10" show-word-limit></el-input>
            </el-form-item>
            <el-form-item label="建筑面积(㎡):">
              <el-input v-model="form.floorage" maxlength="10" show-word-limit></el-input>
            </el-form-item>
            <el-form-item label="门急诊面积(㎡):">
              <el-input v-model="form.emergencyArea" maxlength="10" show-word-limit></el-input>
            </el-form-item>
            <el-form-item label="管理人员数量:">
              <el-input v-model="form.managerNumber" maxlength="10" show-word-limit></el-input>
            </el-form-item>
            <el-form-item label="下设部门名称:">
              <el-input v-model="form.lowerDeptName" maxlength="30" show-word-limit></el-input>
            </el-form-item>
            <el-form-item label="应急分队:">
              <el-input v-model="form.emergentTeam" maxlength="10" show-word-limit></el-input>
            </el-form-item>
            <el-form-item label="驻院民警量:">
              <el-input v-model="form.hospitalPolice" maxlength="10" show-word-limit></el-input>
            </el-form-item>
            <el-form-item label="保安人数:">
              <el-input v-model="form.securityNumber" maxlength="10" show-word-limit></el-input>
            </el-form-item>
            <el-form-item label="保安公司名称:">
              <el-input v-model="form.securityCompanyName" maxlength="30" show-word-limit></el-input>
            </el-form-item>
            <el-form-item label="备注:">
              <el-input v-model="form.remark" maxlength="200" show-word-limit></el-input>
            </el-form-item>
            <el-form-item label="概况:">
              <el-input v-model="form.summarize" maxlength="200" show-word-limit></el-input>
            </el-form-item>
          </el-form>
        </div>
        <div class="toptip">
          <span class="green_line"></span>
          外委服务公司
          <span class="add-btn" @click="addRows">添加行</span>
        </div>
        <div class="outsourcing">
          <el-table
            :key="randomKey"
            :data="tableData"
            style="width: 100%"
            border
            title="双击输入"
            :header-cell-style="{
              textAlign: 'center',
              lineHeight: '35px',
              padding: '0'
            }"
            @cell-dblclick="editData"
          >
            <el-table-column type="index" width="77" label="序号" align="center"></el-table-column>
            <el-table-column label="服务事项" width="200" property="outsourceName">
              <template slot-scope="scope">
                <el-input
                  v-if="scope.row[scope.column.property + 'isShow']"
                  :ref="scope.column.property"
                  v-model="scope.row.outsourceName"
                  type="text"
                  size="medium"
                  resize="none"
                  maxlength="100"
                  show-word-limit
                  @blur="alterData(scope.row, scope.column)"
                ></el-input>
                <span v-else>{{ scope.row.outsourceName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="服务商名称" width="200" property="companyName">
              <template slot-scope="scope">
                <el-input
                  v-if="scope.row[scope.column.property + 'isShow']"
                  :ref="scope.column.property"
                  v-model="scope.row.companyName"
                  type="text"
                  size="medium"
                  resize="none"
                  maxlength="50"
                  show-word-limit
                  @blur="alterData(scope.row, scope.column)"
                ></el-input>
                <span v-else>{{ scope.row.companyName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="服务人员数量" width="200" property="serviceNumber">
              <template slot-scope="scope">
                <el-input
                  v-if="scope.row[scope.column.property + 'isShow']"
                  :ref="scope.column.property"
                  v-model="scope.row.serviceNumber"
                  type="text"
                  size="medium"
                  resize="none"
                  maxlength="10"
                  show-word-limit
                  @blur="alterData(scope.row, scope.column)"
                ></el-input>
                <span v-else>{{ scope.row.serviceNumber }}</span>
              </template>
            </el-table-column>
            <el-table-column label="负责人姓名" width="200" property="dutyPersonName">
              <template slot-scope="scope">
                <el-input
                  v-if="scope.row[scope.column.property + 'isShow']"
                  :ref="scope.column.property"
                  v-model="scope.row.dutyPersonName"
                  type="text"
                  size="medium"
                  resize="none"
                  maxlength="32"
                  show-word-limit
                  @blur="alterData(scope.row, scope.column)"
                ></el-input>
                <span v-else>{{ scope.row.dutyPersonName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="负责人联系电话" width="250" property="dutyPersonPhone">
              <template slot-scope="scope">
                <el-input
                  v-if="scope.row[scope.column.property + 'isShow']"
                  :ref="scope.column.property"
                  v-model="scope.row.dutyPersonPhone"
                  type="text"
                  size="medium"
                  resize="none"
                  maxlength="11"
                  show-word-limit
                  @blur="alterData(scope.row, scope.column)"
                ></el-input>
                <span v-else>{{ scope.row.dutyPersonPhone }}</span>
              </template>
            </el-table-column>
            <el-table-column label="服务有效期" width="220" property="serviceValidity" align="center">
              <template slot-scope="scope">
                <!-- <el-input
                  type=""
                  size="medium"
                  resize="none"
                  v-if="scope.row[scope.column.property + 'isShow']"
                  :ref="scope.column.property"
                  v-model="scope.row.serviceValidity"
                  @blur="alterData(scope.row, scope.column)"
                ></el-input> -->
                <el-date-picker
                  v-if="scope.row[scope.column.property + 'isShow']"
                  :ref="scope.column.property"
                  v-model="scope.row.serviceValidity"
                  type="date"
                  placeholder="选择日期"
                  value-format="yyyy-MM-dd"
                  style="width: 100%"
                  @blur="alterData(scope.row, scope.column)"
                ></el-date-picker>
                <span v-else>{{ scope.row.serviceValidity }}</span>
              </template>
            </el-table-column>
            <el-table-column label="资质证件" width="100" align="center">
              <template slot-scope="scope">
                <div style="display: flex; justify-content: space-between">
                  <el-upload
                    style="width: 40%"
                    class="upload-demo"
                    action="string"
                    accept=".jpg, .jpeg, .png, .JPG, .JPEG, .bmp"
                    :limit="1"
                    :on-exceed="handleExceed"
                    multiple
                    :show-file-list="false"
                    :http-request="
                      (file) => {
                        return handleUpload(file, scope.row)
                      }
                    "
                  >
                    <div class="upload-btn">上传</div>
                  </el-upload>
                  <span class="look-btn" style="width: 40%" @click="previewPic(scope.row)">查看</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template slot-scope="scope">
                <el-button type="danger" size="mini" @click="deleteRow(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <imgCarousel :dialogVisibleImg="dialogVisibleImg" :imgArr="imgArr" @closeDialog="closeDialogImg"></imgCarousel>
    </div>
    <div slot="footer">
      <el-button type="primary" @click="edit()">保存</el-button>
    </div>
  </PageContainer>
</template>

<script>
import axios from 'axios'
import imgCarousel from '@/components/imgCarousel/imgCarousel'
export default {
  name: 'UnitBasicInformation',
  components: { imgCarousel },
  data() {
    return {
      dialogVisibleImg: false,
      imgArr: [],
      loading: false,
      form: {
        hospitalName: '',
        address: '',
        hospitalAreaNumber: '',
        staffNumber: '',
        hospitalLeaderName: '',
        hospitalLeaderPhone: '',
        generalLeaderName: '',
        generalLeaderPhone: '',
        singleNumber: '',
        defendLeaderName: '',
        defendLeaderPhone: '',
        bedNumber: '',
        floorage: '',
        emergencyArea: '',
        managerNumber: '',
        lowerDeptName: '',
        emergentTeam: '',
        hospitalPolice: '',
        securityNumber: '',
        securityCompanyName: '',
        remark: '',
        summarize: ''
      },
      randomKey: Math.random(),
      tableData: [
        {
          outsourceName: '',
          companyName: '',
          serviceNumber: '',
          dutyPersonName: '',
          dutyPersonPhone: '',
          serviceValidity: ''
        }
      ],
      currentId: ''
    }
  },
  created() {},
  mounted() {
    this.getHospitalInfo()
  },
  methods: {
    close() {
      console.log('关闭')
    },
    edit() {
      let tableArr = []
      this.tableData.forEach((item) => {
        let obj = {
          outsourceName: item.outsourceName || '',
          companyName: item.companyName || '',
          dutyPersonName: item.dutyPersonName || '',
          dutyPersonPhone: item.dutyPersonPhone || '',
          serviceNumber: item.serviceNumber || '',
          serviceValidity: item.serviceValidity || '',
          qualificationUrl: item.qualificationUrl || ''
        }
        tableArr.push(obj)
      })
      let obj = this.form
      obj.outSourceList = tableArr
      obj.id = this.currentId
      console.log('提交参数', obj)
      this.$api.ipsmSaveHospital(obj).then((res) => {
        if (res.code == 200) {
          this.$message.success(res.message)
          this.getHospitalInfo()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    editData(row, column) {
      row[column.property + 'isShow'] = true
      this.refreshTable()
      this.$nextTick(() => {
        this.$refs[column.property] && this.$refs[column.property].focus()
      })
    },
    alterData(row, column) {
      row[column.property + 'isShow'] = false
      this.refreshTable()
    },
    refreshTable() {
      this.randomKey = Math.random()
    },
    getHospitalInfo() {
      this.$api.ipsmGetHospitalInfo().then((res) => {
        if (res.code == 200) {
          let data = res.data
          let obj = {
            hospitalName: data.hospitalName,
            address: data.address,
            hospitalAreaNumber: data.hospitalAreaNumber,
            staffNumber: data.staffNumber,
            hospitalLeaderName: data.hospitalLeaderName,
            hospitalLeaderPhone: data.hospitalLeaderPhone,
            generalLeaderName: data.generalLeaderName,
            generalLeaderPhone: data.generalLeaderPhone,
            singleNumber: data.singleNumber,
            defendLeaderName: data.defendLeaderName,
            defendLeaderPhone: data.defendLeaderPhone,
            bedNumber: data.bedNumber,
            floorage: data.floorage,
            emergencyArea: data.emergencyArea,
            managerNumber: data.managerNumber,
            lowerDeptName: data.lowerDeptName,
            emergentTeam: data.emergentTeam,
            hospitalPolice: data.hospitalPolice,
            securityNumber: data.securityNumber,
            securityCompanyName: data.securityCompanyName,
            remark: data.remark,
            summarize: data.summarize
          }
          this.form = obj
          this.tableData = data.outSourceList || []
          this.currentId = data.id
        } else {
          this.$message.error(res.message)
        }
      })
    },
    addRows() {
      this.tableData.push({})
    },
    deleteRow(index) {
      if (this.tableData.length == 1) {
        return this.$message.error('最少保留一条数据')
      }
      this.tableData.splice(index, 1)
    },
    handleExceed() {},
    fileChange(file, fileList) {
      console.log(file, fileList)
      // this.fileList = fileList
    },
    handleUpload(file, row) {
      // console.log(file)
      // console.log(row)
      let formData = new FormData()
      formData.append('file', file.file)
      // this.reportForm.questionAttachmentUrl.push(item.file)
      let loginData = JSON.parse(sessionStorage.getItem('LOGINDATA'))
      formData.append('hospitalCode', loginData.hospitalCode)
      axios({
        method: 'post',
        url: __PATH.VUE_AQ_URL + 'file/upload',
        data: formData,
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      }).then((res) => {
        console.log('res', res)
        if (res.data.code == 200) {
          // if (row.qualificationList && row.qualificationList.length > 0) {
          //   let arr = row.qualificationUrl.split(',')
          //   arr.push(res.data.data.fileKey)
          //   row.qualificationUrl = arr.join(',')
          // } else {
          //   let arr = []
          //   arr.push(res.data.data.fileKey)
          //   row.qualificationUrl = arr.join(',')
          // }
          row.qualificationUrl = res.data.data.fileKey
          this.$message.success('上传成功!')
        } else {
          this.$message.error('上传失败!')
        }
      })
    },
    previewPic(row) {
      if (row.qualificationList && row.qualificationList.length > 0) {
        // this.imgArr = row.qualificationList
        row.qualificationList.forEach((item) => {
          this.imgArr.push(this.$tools.imgUrlTranslation(item))
        })
        this.dialogVisibleImg = true
      } else {
        this.$message.error('该记录无图片')
      }
    },
    closeDialogImg() {
      this.dialogVisibleImg = false
      this.imgArr = []
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-textarea .el-input__count {
  line-height: normal;
}

.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
  padding: 10px;
  overflow-y: auto;
}

::v-deep .el-textarea__inner {
  min-height: 120px !important;
}

.content_box {
  height: calc(100% - 30px);
  margin-top: 10px;
  padding: 20px 25px 25px;
  background: #fff;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.form-inline {
  .el-input,
  .el-select,
  .el-cascader {
    width: 300px;
  }
}

.el-textarea {
  width: 400px;
}

::v-deep .el-input__inner {
  width: 200px;
}

.basic {
  padding: 10px;
}

.a-title {
  font-size: 18px;
  color: #4387f7;
  border-left: 2px solid #4387f7;
  padding-left: 8px;
  margin: 12px 0 24px;
}

.add-btn {
  cursor: pointer;
  color: #409eff;
  margin-left: 16px;
  font-size: 14px;
}

.look-btn {
  color: #409eff;
  cursor: pointer;
}

.upload-btn {
  color: #409eff;
  cursor: pointer;
}
</style>
