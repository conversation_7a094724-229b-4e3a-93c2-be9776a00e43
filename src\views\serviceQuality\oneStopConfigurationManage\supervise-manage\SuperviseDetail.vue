<script>
export default {
  name: 'SuperviseDetail',
  data() {
    return {
      loadingStatus: false,
      // 详情
      detail: {}
    }
  },
  computed: {
    tableData() {
      const pushInfo = this.detail.pushInfo || '[]'
      return JSON.parse(pushInfo)
    }
  },
  mounted() {
    const id = this.$route.query.id
    if (id) {
      this.getDetail(id)
    }
  },
  methods: {
    // 获取分页列表数据
    getDetail(id) {
      this.loadingStatus = true
      this.$api.superviseRecordById({ id })
        .then((res) => {
          if (res.code === '200') {
            this.detail = res.data
          } else {
            throw res.msg
          }
        })
        .catch((msg) => this.$message.error(msg))
        .finally(() => (this.loadingStatus = false))
    }
  }
}
</script>
<template>
  <PageContainer v-loading="loadingStatus" footer class="supervise-detail">
    <template #content>
      <div class="supervise-detail__title">
        <svg-icon name="right-arrow" />
        基本信息
      </div>
      <el-form class="supervise-detail__form" label-width="100px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="所属项目">
              {{ detail.projectName }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="工单类型">
              {{ detail.workTypeName }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="服务事项">
              {{ detail.itemTypeName }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="超时类型">
              {{ detail.timeoutTypeName }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="工单号">
              {{ detail.workNum }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="接收人">
              <el-tooltip :content="detail.receiverStr">
                <div class="supervise-detail__receiver">{{ detail.receiverStr }}</div>
              </el-tooltip>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="督办时间">
              {{ detail.pushTime }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="supervise-detail__title">
        <svg-icon name="right-arrow" />
        督办明细
      </div>
      <div class="supervise-detail__table table-content">
        <el-table
          height="100%"
          :data="tableData"
          border
          stripe
          table-layout="auto"
          class="tableAuto"
          row-key="id"
        >
          <el-table-column label="通知方式" prop="pushTypeName"></el-table-column>
          <el-table-column label="通知结果" prop="result"></el-table-column>
          <el-table-column label="备注" prop="remark"></el-table-column>
        </el-table>
      </div>
    </template>
    <template #footer>
      <el-button type="primary" plain @click="$router.back()">返回</el-button>
    </template>
  </PageContainer>
</template>
<style scoped lang="scss">
.supervise-detail {
  ::v-deep(.container-content) {
    display: flex;
    flex-flow: column nowrap;
    padding: 16px;
    height: 100%;
    overflow: hidden;
    background-color: #fff;
  }
  &__form {
    margin: 10px 0;
    .el-form-item {
      margin-bottom: 0;
    }
  }
  &__table {
    margin-top: 16px;
    flex: 1;
    overflow: hidden;
  }
  &__receiver {
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
</style>
