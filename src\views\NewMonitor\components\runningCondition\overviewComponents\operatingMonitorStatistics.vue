<!-- 监测 -->
<template>
    <ContentCard :title="item.componentTitleShow" :scrollbarHover="true" :cstyle="{ height: '100%' }" class="drag_class"
        :hasMoreOper="['edit']" @more-oper-event="(val) => $emit('all-more-Oper', val, 'operationMonitor')">
        <div slot="content" class="operation-list">
            <div style="display: flex;justify-content: space-between;">
                <el-select v-model="equipment" placeholder="请选择设备" @change="handleSelectChange" class="ml-10" clearable>
                    <el-option v-for="item in listData" :key="item.id" :label="item.assetsName" :value="item.id">
                    </el-option>
                </el-select>
                <a style="color: #3f63d3;" @click="more()">更多</a>
            </div>
            <div class="list-item">
                <img :src="pictureUrl" alt="" style="width:350px;height: 200px;" v-if="pictureUrl !== ''">
                <img src="../../../../../assets/images/defaultimg.png" alt="" v-if="pictureUrl == ''"
                    style="width:350px;height: 200px;">
                <div class="item-left">
                    <div v-for="item in statisticsList" :key="item.id" @click="detailed(item)" style="cursor: pointer;"
                        class="left-info">
                        <div>
                            <div>
                                <p class="info-name">{{ item.metadataName }}</p>
                                <p style="display: flex;justify-content: space-between;">
                                    <span class="info-num">{{ item.valueText }}</span>
                                    <span class="info-unit" :style="{ background: item.sectionColor }">{{
                                        item.sectionName
                                        }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </ContentCard>
</template>
<script>
export default {
    name: 'deviceOperationMonitor',
    props: {
        item: {
            type: Object,
            default: () => { }
        },
        systemCode: {
            type: String,
            default: ''
        },
    },
    data() {
        return {
            equipment: "",
            listData: [],
            statisticsList: [],
            pictureUrl: '',
            equipAttr: "2",
            assetsName: "",
            assetsId: "",
        }
    },
    mounted() {
        this.getDataList()
    },
    methods: {
        handleSelectChange(val) {
            this.assetsName = this.listData.find(item => {
                return item.id === val
            }).assetsName
            const url = this.listData.find(item => {
                return item.id === val
            }).pictureUrl
            this.assetsId = this.listData.find(item => {
                return item.id === val
            }).id
            this.pictureUrl = this.$tools.imgUrlTranslation(url)
            this.getQueryrealtimeData(this.assetsId)
        },
        getDataList(id) {
            let data = {
                sysOfCode: this.systemCode,
                sysOf1Code: "SSS",
                groupId: id || "",
            }
            this.listData = []
            this.$api
                .getMasterOrMonitoredByCode(data)
                .then((res) => {
                    if (res.code === '200' || res.code === 200) {
                        this.listData = res.data.slice(0, 16);
                        this.equipment = res.data[0]
                        this.assetsName = res.data[0].assetsName
                        this.assetsId = res.data[0].id
                        this.pictureUrl = this.$tools.imgUrlTranslation(res.data[0].pictureUrl)
                        this.getQueryrealtimeData(res.data[0].id)
                    }
                })
                .catch(() => {
                })
        },
        getQueryrealtimeData(id) {
            this.$api.getQueryrealtimeData({ id: id }).then((res) => {
                if (res.code == 200) {
                    this.statisticsList = res.data
                }
            })
        },
        // 更多
        more() {
            let path = 'operationalMonitoring/deviceDetails'
            if (!path) return
            this.$router.push({
                path: path,
                query: {
                    id: this.assetsId,
                    assetsName: this.assetsName,
                    systemCode: this.systemCode,
                    equipAttr: this.equipAttr
                }
            })
        },
    }
}
</script>
<style lang="scss" scoped>
.operation-list {
    height: 100%;
    gap: 16px;

    .list-item {
        padding: 16px 0;
        display: flex;
        flex-direction: row;
        min-width: 368px;


        .item-left {
            margin-left: 18px;
            display: flex;
            flex-wrap: wrap;
            width: 100%;

            .left-info {
                margin: 0 10px 10px 0;
                background-color: #faf9fc;
                width: calc((100% / 8) - 10px);
                height: 95px;
                padding: 15px 20px 10px;

                .info-name {
                    font-size: 18px;
                    font-weight: bold;
                    color: #121f3e;
                    line-height: 18px;
                    width: 90px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .info-num {
                    font-size: 15px;
                    color: #121f3e;
                    line-height: 36px;
                }

                .info-unit {
                    font-size: 13px;
                    font-weight: 500;
                    color: #ccced3;
                    line-height: 18px;
                    margin-left: 4px;
                    height: 20px;
                    background: transparent;
                    padding: 1px 3px;
                    margin-top: 9px;
                }
            }
        }
    }
}
</style>