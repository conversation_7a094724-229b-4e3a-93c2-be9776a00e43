<template>
  <PageContainer>
    <div slot="content" class="role-content" style="height: 100%;">
      <div class="role-content-left">
        <div class="toptip">
          <span class="green_line"></span>
          设备分类
        </div>
        <div class="left_content">
          <div class="tree">
            <el-tree
              ref="tree"
              style="margin-top: 10px;"
              :check-strictly="true"
              :data="treeData"
              :props="defaultProps"
              node-key="id"
              :highlight-current="true"
              :default-expanded-keys="[0]"
              @node-click="handleNodeClick"
            ></el-tree>
          </div>
        </div>
      </div>
      <div class="role-content-right">
        <div style="height: 100%;">
          <div class="search-from">
            <el-input v-model.trim="filters.eqName" clearable placeholder="设备名称" style="width: 200px;"></el-input>
            <el-input v-model.trim="filters.eqType" clearable placeholder="设备型号" style="width: 200px;"></el-input>
            <el-button type="primary" @click="resetData">重置</el-button>
            <el-button type="primary" class="sino-button-sure-search" @click="searchClick()">查询</el-button>
          </div>
          <div class="middle_tools">
            <el-button type="primary" icon="el-icon-plus" @click="goAddPage">新增</el-button>
            <el-button type="primary" icon="el-icon-edit" :disabled="multipleSelection.length != 1" @click="goEditPage">编辑</el-button>
            <el-button type="primary" icon="el-icon-upload2" @click="exportFile">导出</el-button>
          </div>
          <div class="contentTable">
            <div class="contentTable-main table-content">
              <el-table
                ref="multipleTable"
                v-loading="tableLoading"
                highlight-current-row
                :data="tableData"
                border
                stripe
                style="width: 100%;"
                :height="tableHeight"
                @selection-change="handleSelectionChange"
                @row-dblclick="dblclick"
              >
                <!-- <template slot="empty">
              <p class="tableEmptyLabel">{{ $store.state.tableLabel }}</p>
            </template> -->
                <el-table-column type="selection" width="55" fixed></el-table-column>
                <el-table-column type="index" width="60" label="序号">
                  <template slot-scope="scope">
                    <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="eqName" label="设备名称" min-width="100" show-overflow-tooltip></el-table-column>
                <el-table-column prop="eqType" label="设备型号" show-overflow-tooltip width="180"></el-table-column>
                <el-table-column prop="majorName" show-overflow-tooltip label="专业类型" width="100"></el-table-column>
                <el-table-column prop="systemBigName" show-overflow-tooltip label="系统大类"></el-table-column>
                <el-table-column prop="eqBigName" show-overflow-tooltip label="设备大类" min-width="130"></el-table-column>
                <el-table-column prop="firstTime" show-overflow-tooltip label="首次投入日期" min-width="120"></el-table-column>
                <el-table-column prop="removeTime" show-overflow-tooltip label="整体拆除日期" min-width="120"></el-table-column>
                <el-table-column prop="updateTime" show-overflow-tooltip label="数据更新时间" min-width="120"></el-table-column>
                <el-table-column label="操作" width="100" align="center">
                  <template slot-scope="scope">
                    <el-button type="danger" size="mini" @click="remove(scope.row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="contentTable-footer">
              <el-pagination
                :current-page="paginationData.currentPage"
                :page-sizes="[15, 30, 50, 100]"
                :page-size="paginationData.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="paginationData.total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              ></el-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PageContainer>
</template>

<script>
import tableListMixin from '@/mixins/tableListMixin.js'
import axios from 'axios'
export default {
  name: 'KeyLedgeManagement',
  mixins: [tableListMixin],
  data() {
    return {
      loading: false,
      importFileDialog: false,
      confirmDialog: false,
      id: '',
      type: 'edit',
      treeData: [],
      tableCode: 1,
      defaultProps: {
        children: 'children',
        label: 'baseName',
        value: 'id'
      },
      defaultProps2: {
        children: 'children',
        label: 'gridName',
        value: 'id'
      },
      defaultProps4: {
        children: 'children',
        label: 'text',
        value: 'id'
      },
      riskLevelList: [],
      guankongList: [
        {
          name: '启用',
          id: 0
        },
        {
          name: '禁用',
          id: 1
        }
      ],
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      tableData: [],
      treeLoading: false,
      tableLoading: false,
      filters: {
        eqName: '',
        eqType: ''
      },
      multipleSelection: [],
      checkedData: {},
      riskType: '',
      dialogVisibleExport: false,
      dataList: [],
      teamList: [],
      imgArr: [],
      dialogVisibleImg: false,
      // eslint-disable-next-line vue/no-dupe-keys
      confirmDialog: false,
      img: '',
      ledgerId: ''
    }
  },
  // activated() {
  //   this.$store.commit('changeImasTableLabel', 'init')
  //   if (!this.$store.state.keepAlive || this.$store.state.refresh) {
  //     this.paginationData = {
  //       currentPage: 1,
  //       pageSize: 15,
  //       total: 0
  //     }
  //     this.$store.commit('changeImasTableLabel', 'init')
  //     // this.filters.riskName = ''
  //     // this.getTreeData();
  //     this.$store.commit('amendRefresh', false)
  //   }
  //   this.$store.commit('keepAliveChange', false)
  // },
  mounted() {},
  created() {
    this.getTreeData()
  },
  methods: {
    searchClick() {
      // this.$store.commit('changeImasTableLabel')
      // this.$store.commit('changeNetworkError', '查询失败，请检查您的网络…')
      this.paginationData.currentPage = 1
      this.getTableData(true)
    },
    // --------------详情
    dblclick(row) {
      this.$api.ipsmGetKeyLedgeById({ equipmentId: row.id }).then((res) => {
        if (res.code == 200) {
          row.fileUploadList = res.data
          this.$router.push({
            name: 'addKeyLedge',
            query: {
              handleType: 'check',
              echoData: row
            }
          })
        }
      })
    },
    // 勾选
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 获取风险分类
    getTreeData() {
      this.treeLoading = true
      this.$api.ipsmEquipmentListData({ parentId: -1 }).then((res) => {
        this.treeLoading = this.$store.state.loadingShow
        if (res.code == '200' && res.data.length > 0) {
          let arr = [
            {
              id: '0',
              baseName: '设备安全',
              children: res.data
            }
          ]
          this.treeData = arr
          this.$nextTick(() => {
            this.$refs.tree.setCurrentKey(this.treeData[0])
          })
          this.getTableData()
        }
      })
    },
    //  树状图点击
    handleNodeClick(data) {
      console.log(data)
      if (data.id == '0') {
        this.ledgerId = ''
      } else {
        this.ledgerId = data.id
      }
      this.paginationData.currentPage = 1
      // this.$refs.tree.setCurrentKey(this.checkedData)
      this.getTableData()
    },
    getTableData() {
      let data = {
        eqName: this.filters.eqName,
        eqType: this.filters.eqType,
        pageNo: this.paginationData.currentPage,
        pageSize: this.paginationData.pageSize,
        majorType: this.ledgerId
      }
      this.tableLoading = true
      this.$api.ipsmEmphasisstandingbookList(data).then((res) => {
        this.tableLoading = false
        if (res.code == 200) {
          this.tableData = res.data.list
          this.paginationData.total = parseInt(res.data.count)
        } else if (res.message) {
          this.$message.error(res.message)
        }
      })
    },
    resetData() {
      // this.$store.commit('changeImasTableLabel', 'init')
      // this.$store.commit('changeNetworkError', '查询失败，请检查您的网络…')
      this.filters.eqName = ''
      this.filters.eqType = ''
      this.paginationData.currentPage = 1
      this.getTableData()
    },
    handleSizeChange(val) {
      this.paginationData.currentPage = 1
      this.paginationData.pageSize = val
      this.getTableData()
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.getTableData()
    },
    exportClickExport() {
      this.dialogVisibleExport = true
    },
    goAddPage() {
      this.$router.push({
        name: 'addKeyLedge',
        query: {
          ledgerId: this.ledgerId,
          handleType: 'add'
        }
      })
    },
    goEditPage() {
      this.$api.ipsmGetKeyLedgeById({ equipmentId: this.multipleSelection[0].id }).then((res) => {
        if (res.code == 200) {
          this.multipleSelection[0].fileUploadList = res.data
          this.$router.push({
            name: 'addKeyLedge',
            query: {
              ledgerId: this.ledgerId,
              handleType: 'edit',
              echoData: this.multipleSelection[0]
            }
          })
        }
      })
    },
    remove(row) {
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$api.ipsmRemoveKeyLedge({ id: row.id }).then((res) => {
            if (res.code == 200) {
              this.$message.success('删除成功!')
              this.getTableData()
            } else {
              this.$message.error('删除失败!')
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    exportFile() {
      let selectionArr = this.multipleSelection.map((x) => x.id)
      let baseInfo = sessionStorage.getItem('LOGINDATA') ? JSON.parse(sessionStorage.getItem('LOGINDATA')) : ''
      let params = {
        // ledgerId: this.ledgerId,
        eqName: this.filters.eqName,
        eqType: this.filters.eqType,
        unitCode: baseInfo.unitCode,
        hospitalCode: baseInfo.hospitalCode,
        majorType: this.ledgerId
      }
      if (selectionArr.length) {
        params.ids = selectionArr.join(',')
      }
      axios({
        method: 'post',
        url: __PATH.VUE_AQ_URL + 'standingBook/taskExport',
        params,
        responseType: 'blob',
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      })
        .then((res) => {
          // this.downLoading = false
          let name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
          let blob = new Blob([res.data]) // { type: "application/vnd.ms-excel" }
          let url = window.URL.createObjectURL(blob) // 创建一个临时的url指向blob对象
          // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
          let a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
          // 释放这个临时的对象url
          window.URL.revokeObjectURL(url)
        })
        .catch((res) => {
          console.log(res)
          // this.downLoading = false
          this.$message.error('导出失败！')
        })
    }
  }
}
</script>
<style lang="scss" scoped>
// el-tree-node is-expanded is-focusable
::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: linear-gradient(to right, #d9e1f8, #fff);
}

::v-deep .el-tree-node__content {
  height: 32px;
}

.role-content {
  height: 100%;
  display: flex;

  .role-content-left {
    width: 246px;
    height: 100%;
    padding: 10px;
    background: #fff;
    border-radius: 4px;
    margin-right: 12px;

    .left_content {
      height: calc(100% - 60px);
      padding: 10px;
      // overflow: auto;
      .tree {
        margin-top: 10px;
        height: calc(100% - 40px);
        overflow: auto;
      }
    }
  }

  .role-content-right {
    flex: 1;
    height: 100%;
    min-width: 0;
    padding: 10px;
    background: #fff;
    border-radius: 4px;

    .search-from {
      padding-bottom: 12px;

      & > div {
        margin-right: 10px;
      }

      & > button {
        margin-top: 12px;
      }
    }

    .middle_tools {
      margin-bottom: 10px;
    }

    .contentTable {
      height: calc(100% - 100px);
      display: flex;
      flex-direction: column;

      .contentTable-main {
        height: calc(100% - 40px);
        flex: 1;
        overflow: auto;
      }

      .contentTable-footer {
        padding: 10px 0 0;
      }
    }
  }
}
</style>
