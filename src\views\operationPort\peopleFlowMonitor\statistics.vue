<template>
  <PageContainer>
    <div slot="header" class="control-header">
      <el-date-picker
        v-model="searchFrom.date"
        value-format="yyyy-MM-dd"
        popper-class="timePicker"
        type="date"
        :picker-options="pickerOptions"
        placeholder="选择日期"
        @change="dateChange"
      />
    </div>
    <div slot="content" class="table-content">
      <div class="table-content-header">
        <div class="table-content-header-module">
          <el-tabs value="0">
            <el-tab-pane label="院区人数分析" name="0"></el-tab-pane>
          </el-tabs>
          <div class="stat-content">
            <div class="stat-content-item">
              <p class="item-title">进入人数</p>
              <p class="item-value">100</p>
              <img src="@/assets/images/operationPort/enter_icon.png" class="item-icon" />
            </div>
            <div class="stat-content-item">
              <p class="item-title">离开人数</p>
              <p class="item-value">100</p>
              <img src="@/assets/images/operationPort/leave_icon.png" class="item-icon" />
            </div>
          </div>
        </div>
        <div class="table-content-header-module">
          <el-tabs v-model="activeTabs" @tab-click="handleClick">
            <el-tab-pane label="这是入口名称" name="0"></el-tab-pane>
            <el-tab-pane label="这是入口名称" name="1"></el-tab-pane>
          </el-tabs>
          <div class="stat-content">
            <div class="stat-content-item">
              <p class="item-title">进入人数</p>
              <p class="item-value">100</p>
              <img src="@/assets/images/operationPort/enter_icon.png" class="item-icon" />
            </div>
            <div class="stat-content-item">
              <p class="item-title">离开人数</p>
              <p class="item-value">100</p>
              <img src="@/assets/images/operationPort/leave_icon.png" class="item-icon" />
            </div>
          </div>
        </div>
      </div>
      <div class="table-content-body">
        <div class="body-header">
          <p>各楼层人数统计</p>
          <el-select v-model="searchFrom.floor" filterable placeholder="请选择">
            <el-option v-for="item in floorOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
        </div>
        <div class="body-content">
          <ContentCard title="各楼层进入人次统计">
            <div id="enter_chart" slot="content"></div>
          </ContentCard>
          <ContentCard title="各楼层离开人次统计">
            <div id="leave_chart" slot="content"></div>
          </ContentCard>
        </div>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import * as echarts from 'echarts'
import moment from 'moment'
moment.locale('zh-cn')
export default {
  name: 'statistics',
  data() {
    return {
      searchFrom: {
        date: moment().format('YYYY-MM-DD'),
        floor: ''
      },
      activeTabs: '0',
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      },
      floorOptions: [
        { value: '1', label: '1楼' },
        { value: '2', label: '2楼' }
      ]
    }
  },
  computed: {},
  created() {
    this.searchFrom.floor = this.floorOptions[0].value
    this.$nextTick(() => {
      this.dateChange()
    })
  },
  methods: {
    dateChange() {
      this.initChart('enter_chart')
      this.initChart('leave_chart')
    },
    handleClick(tab, event) {
      console.log(tab.name)
    },
    initChart(dom) {
      const getchart = echarts.init(document.getElementById(dom))
      let data = [
        { name: '12F', value: 500 },
        { name: '11F', value: 490 },
        { name: '10F', value: 480 },
        { name: '9F', value: 470 },
        { name: '8F', value: 460 },
        { name: '7F', value: 450 },
        { name: '6F', value: 440 },
        { name: '5F', value: 430 },
        { name: '4F', value: 280 },
        { name: '3F', value: 270 },
        { name: '2F', value: 265 },
        { name: '1F', value: 100 }
      ]
      const option = {
        grid: {
          left: '0%',
          right: '8%',
          top: '0%',
          bottom: '0%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: '#999',
            fontSize: 12
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#f0f0f0',
              type: 'solid'
            }
          }
        },
        yAxis: {
          type: 'category',
          data: data.map((item) => item.name),
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: '#333',
            fontSize: 12,
            margin: 10
          }
        },
        series: [
          {
            type: 'bar',
            data: data.map((item) => item.value),
            barWidth: 10,
            itemStyle: {
              color: '#3562DB',
              borderRadius: [0, 2, 2, 0]
            },
            label: {
              show: true,
              position: 'right',
              color: '#333',
              fontSize: 12,
              formatter: '{c}'
            }
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.control-header {
  padding: 16px !important;
}
.table-content {
  margin-top: 16px;
  border-radius: 4px;
  height: calc(100% - 16px);
  display: flex;
  flex-direction: column;
  p {
    margin: 0;
  }
  .table-content-header {
    width: 100%;
    display: flex;
    justify-content: space-between;
    &-module {
      border-radius: 4px;
      width: calc(50% - 8px);
      background: #fff;
      padding: 16px;
      .stat-content {
        display: flex;
        justify-content: space-between;
        padding-top: 16px;
        .stat-content-item {
          background: #faf9fc;
          width: calc(50% - 5px);
          border-radius: 4px;
          padding: 24px;
          position: relative;
          .item-title {
            font-weight: 500;
            font-size: 15px;
            color: #333333;
          }
          .item-value {
            margin-top: 15px;
            font-weight: bold;
            font-size: 30px;
            color: #333333;
            line-height: 36px;
          }
          .item-icon {
            width: 40px;
            height: 40px;
            position: absolute;
            right: 24px;
            bottom: 24px;
          }
        }
      }
    }
  }
  .table-content-body {
    border-radius: 4px;
    margin-top: 16px;
    background: #fff;
    flex: 1;
    padding: 16px;
    display: flex;
    flex-direction: column;
    .body-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: bold;
      font-size: 16px;
      color: #333333;
    }
    .body-content {
      display: flex;
      justify-content: space-between;
      flex: 1;
      padding-top: 16px;
      .box-card {
        height: 100%;
        width: calc(50% - 8px);
        background: #f8f9fa;
        padding: 16px;
      }
      #enter_chart,
      #leave_chart {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
