<template>
    <RunCalendar :systemCode="systemCode" />
</template>
  
<script>
import RunCalendar from '@/views/NewMonitor/airConditioningTerminal/runCalendarList/runCalendar.vue'
import { newMonitorTypeList } from '@/util/newDict.js'
export default {
    name: 'airRunCalendar',
    components: {
        RunCalendar
    },
    beforeRouteEnter(to, from, next) {
        let names = []
        to.matched.map((v, i) => {
            if (i > 0) {
                v.components.default.name && names.push(v.components.default.name)
            }
        })
        next((vm) => {
            if (from.query.activeName) {
                vm.activeName = from.query.activeName
            }
            vm.$store.commit('keepAlive/add', names)
        })
    },
    async beforeRouteLeave(to, from, next) {
        // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
        if (!['calendarDetails', 'calendarDetailsEquipment'].includes(to.name)) {
            // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
            await this.$store.commit('keepAlive/clean')
        }
        next()
    },
    data() {
        return {
            systemCode: newMonitorTypeList.find((item) => item.systemName == '空调末端').systemCode
        }
    },
    computed: {},
    created() {
    },
    methods: {
    }
}
</script>
  
<style lang="scss" scoped></style>
  