<template>
  <el-drawer
    :visible.sync="drawerDialog"
    :with-header="true"
    size="55%"
    :show-close="true"
    :before-close="closeDrawer"
  >
    <div slot="title" class="coursrDrawer">
      已录入试题（{{questionsList.length}}）
      <span style="cursor: pointer;" @click="allFold">{{
        isAllFold ? "全部折叠" : "全部展开"
      }}</span>
    </div>
    <div class="drawer_conter">
      <div class="content">
        <div class="table">
          <div
          :class="['exercisesItem', item.isExpand ? 'expand' : '']"
            v-for="(item, index) in questionsList"
            :key="item.id"
            :name="item.id"
            :ref="'exercisesItem' + index"
          >
            <div class="exercisesTop">
              <div class="left">
                <div class="exercisesType">
                  {{
                    item.type == 1
                      ? "单选题"
                      : item.type == 2
                      ? "多选题"
                      : "判断题"
                  }}
                </div>
                <span>({{ item.id }})</span>
              </div>
              <div class="right">
                {{ item.score }}分
                <div class="line"></div>
                <i class="el-icon-delete" @click="deletQuestions(item)"></i>
                <span @click="isExpandBtn(item, index)">{{
                  item.isExpand ? "折叠" : "展开"
                }}</span>
              </div>
            </div>
            <div :class="['exercisesName', item.isExpand ? '' : 'title']">
              {{ item.topic }}
            </div>
            <el-radio-group
              v-if="item.type == '1'"
              class="radio"
              v-model="item.answer"
              disabled
            >
              <el-radio
                v-for="(item, index) in item.options"
                :key="index"
                :label="item.id"
                >{{ item.id }}. {{ item.label }}</el-radio
              >
            </el-radio-group>
            <el-checkbox-group
              v-if="item.type == '2'"
              v-model="item.answer"
              class="radio"
              disabled
            >
              <el-checkbox
                v-for="(item, index) in item.options"
                :key="index"
                :label="item.id"
                >{{ item.id }}. {{ item.label }}</el-checkbox
              >
            </el-checkbox-group>

            <p>答案：{{ item | getAnswer }}</p>
            <p>
              解析：
              {{ item.analysis }}
            </p>
          </div>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script>
export default {
  props: {
    drawerDialog: {
      type: Boolean,
      default: false,
    },
    enterQuestionsList: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      isAllFold:true,
      questionsList:[]
    };
  },
  watch:{
    enterQuestionsList(val){
      this.questionsList = val
    },
  },
  filters:{
    getAnswer(val){
      if(val.type=='3'){
        return val.answer=='1'?'正确':'错误'
      }else if(val.type=='2'){
        return val.answer.toString()
      }else{
        return val.answer
      }
    }
  },
  mounted(){
    this.questionsList = this.enterQuestionsList
    this.questionsList.forEach(i=>{
      if(i.type=='2'){
        i.answer = i.answer.split(',')
      }
    })
  },
  methods: {
    closeDrawer() {
      this.$emit("closeDrawer", false);
    },
    isExpandBtn(item, index) {
      item.isExpand = !item.isExpand;
    },
    // 全部展开或者全部折叠
    allFold(){
      this.questionsList.forEach(item=>item.isExpand=!item.isExpand)
      this.isAllFold=!this.isAllFold
    },
    deletQuestions(item){
      this.$emit('deletQuestions',item.id)
    }
  },
};
</script>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}
.coursrDrawer {
  color: #333333;
  font-weight: 500;
  font-size: 18px;
  span {
    font-size: 14px;
    color: #3562db;
  }
}
::v-deep .el-drawer__header {
  height: 56px;
  line-height: 56px !important;
  margin-bottom: 0 !important;
  border-bottom: 1px solid #dcdfe6;
  box-shadow: -4px 0 4px 0 rgba(203, 205, 220, 0.24);
}
.drawer_conter {
  height: 100%;
  .content {
    height: 100%;
    overflow: auto;
    .table {
      height: calc(100% - 48px);
      margin: 24px;
      font-size: 14px;
      .exercisesItem {
        height: 80px;
        overflow: hidden;
        background-color: #fff;
        margin-bottom: 26px;
        .exercisesTop {
          display: flex;
          justify-content: space-between;
          margin-bottom: 10px;
          .left {
            display: flex;
            align-items: center;
            justify-content: center;
            span {
              color: #7f848c;
            }
          }
          .right {
            color: #ccced3;
            display: flex;
            align-items: center;
            .line {
              width: 2px;
              height: 14px;
              margin: 0 10px 0 26px;
              background-color: #dcdfe6;
            }
            span {
              color: #3562db;
              margin-left: 16px;
              cursor: pointer;
            }
            i {
              color: #3562db;
              cursor: pointer;
            }
          }
          .exercisesType {
            width: 58px;
            height: 22px;
            line-height: 22px;
            border-radius: 4px;
            color: #86909c;
          }
        }
        .exercisesName {
          line-height: 20px;
          margin:0 0  26px 0px;
        }
        .title {
          overflow: hidden;
          -webkit-line-clamp: 2;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
        }
        .el-radio {
          margin-left: 38px;
          font-size: 14px;
          color: #7f848c !important;
          line-height: 18px;
          display: flex;
        }
        p {
          font-size: 14px;
          color: #7f848c !important;
          line-height: 20px;
          margin-bottom: 16px;
        }
      }
      .expand {
        height: auto;
      }
    }
  }
}
::v-deep .el-radio {
  display: block;
  margin: 10px 0;
  .el-radio__label {
    white-space: normal;
  }
}
::v-deep .el-checkbox {
  display: block; 
  font-size: 14px;
  color: #7f848c !important;
  line-height: 30px;
  margin: 10px 0;
}
::v-deep .el-checkbox-group {
  margin-left: 38px;
}
</style>
