<template>
  <PageContainer>
    <div slot="content">
      <div class="title">添加周期规则</div>
      <div class="pageContainer">
        <el-form ref="form" class="form" :model="form" :rules="rules" label-width="140px" style="width: 60%">
          <el-form-item label="周期规则名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入周期规则名称" maxlength="20"> </el-input>
          </el-form-item>
          <el-form-item label="周期类型" prop="type">
            <el-select v-model="form.type" placeholder="请选择周期类型" style="width: 100%" clearable @change="cycleTypeChange">
              <el-option v-for="item in cycleList" :key="item.id" :label="item.nodeName" :value="item.node"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="form.type != 6" label="周期设置" :required="form.type == 6">
            <el-radio-group v-model="form.setType" @change="setTypeChange">
              <el-radio :label="0">循环周期</el-radio>
              <el-radio :label="1">固定周期</el-radio>
            </el-radio-group>
          </el-form-item>
          <!-- 循环周期 -->
          <el-table v-if="form.type != 6" :data="form.cycleLevelList" stripe border style="width: 100%">
            <el-table-column prop="name" label="周期名称">
              <template slot-scope="scope">
                <el-form-item
                  :prop="`cycleLevelList.${scope.$index}.name`"
                  label-width="0"
                  :rules="{ required: true, message: '请输入周期名称', trigger: 'blur' }"
                  inline-message
                  :show-message="false"
                >
                  <el-input v-model="form.cycleLevelList[scope.$index].name" placeholder="请输入周期名称"></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <template v-if="form.setType === 0">
              <el-table-column prop="startTimeList" label="开始日期">
                <template slot-scope="scope">
                  <el-form-item
                    :prop="`cycleLevelList.${scope.$index}.startTimeList`"
                    label-width="0"
                    :rules="{ required: true, validator: (rule, value, callback) => startValidate(scope.row, value, callback), trigger: 'change' }"
                    inline-message
                  >
                    <!-- :show-message="false" -->
                    <el-cascader
                      v-model="form.cycleLevelList[scope.$index].startTimeList"
                      clearable
                      :disabled="scope.$index !== 0 && form.type !== 5"
                      :options="mockData(true)"
                      @change="startDateChange"
                    ></el-cascader>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="endTimeList" label="结束日期">
                <template slot-scope="scope">
                  <el-form-item :prop="`cycleLevelList.${scope.$index}.endTimeList`" label-width="0" inline-message>
                    <el-cascader v-model="form.cycleLevelList[scope.$index].endTimeList" :disabled="form.type !== 5" :options="mockData()"></el-cascader>
                  </el-form-item>
                </template>
              </el-table-column>
            </template>
            <el-table-column v-else prop="timeList" label="起始日期">
              <template slot-scope="scope">
                <el-form-item
                  :prop="`cycleLevelList.${scope.$index}.timeList`"
                  label-width="0"
                  :rules="{ required: true, message: '请选择起始日期', trigger: 'change' }"
                  inline-message
                  :show-message="false"
                >
                  <el-date-picker
                    v-model="form.cycleLevelList[scope.$index].timeList"
                    type="daterange"
                    value-format="yyyy-MM-dd"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    @change="(e) => timeListChange(e, scope.row)"
                  >
                  </el-date-picker>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column v-if="form.type === 5 || form.setType === 1" label="操作">
              <template slot-scope="scope">
                <el-button type="text" @click="del(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form>
        <el-button v-if="form.type === 5 || form.setType === 1" style="margin-top: 16px" type="primary" @click="addCycle">添加周期</el-button>
      </div>
      <div class="footer">
        <el-button type="primary" @click="submit">保存</el-button>
        <el-button type="primary" plain @click="goBack">取消</el-button>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import { mockData } from './json.js'
export default {
  name: 'updateCycleRules',
  data() {
    return {
      cycleList: [],
      form: {
        id: '',
        cycleLevel: 0,
        cycleLevelList: [
          {
            name: '',
            startTimeList: [],
            endTimeList: [],
            timeList: []
          }
        ],
        name: '',
        type: 0,
        setType: 0
      },
      realTimeMin: 5, // 默认5分钟
      rules: {
        name: { required: true, message: '请输入周期名称', trigger: 'blur' },
        type: { required: true, message: '请选择周期类型', trigger: 'blur' },
        setType: { required: true, message: '请设置周期执行方式', trigger: 'change' }
      }
    }
  },
  mounted() {
    this.getCycleTypeList()
    if (this.$route.query.id) {
      this.getDetail()
    }
  },
  methods: {
    mockData,
    getCycleTypeList() {
      this.$api.getCycleType().then((res) => {
        if (res.code === '200') {
          this.cycleList = res.data
        }
      })
    },
    getDetail() {
      this.$api.targetConfigDetail({ cycleLevel: this.type, id: this.$route.query.id }).then((res) => {
        if (res.code === '200') {
          this.form = res.data
          if (this.form.setType === 1) {
            this.form.cycleLevelList.forEach((el) => {
              this.$set(el, 'timeList', [el.startTime, el.endTime])
            })
          }
        }
      })
    },
    addCycle() {
      if (this.form.type === '') {
        this.$message.warning('请先选择周期类型')
        return
      }
      if (this.form.setType === 0) {
        if (this.form.type === 0) {
          if (this.form.cycleLevelList.length > 0) {
            return
          } else {
            this.form.cycleLevelList.push({
              name: '',
              startTimeList: [],
              endTimeList: [],
              timeList: []
            })
          }
        } else if (this.form.type === 1) {
          if (this.form.cycleLevelList.length > 1) {
            return
          } else {
            this.form.cycleLevelList.push({
              name: '',
              startTimeList: [],
              endTimeList: [],
              timeList: []
            })
          }
        } else if (this.form.type === 2) {
          if (this.form.cycleLevelList.length > 3) {
            return
          } else {
            this.form.cycleLevelList.push({
              name: '',
              startTimeList: [],
              endTimeList: [],
              timeList: []
            })
          }
        } else if (this.form.type === 3) {
          if (this.form.cycleLevelList.length > 5) {
            return
          } else {
            this.form.cycleLevelList.push({
              name: '',
              startTimeList: [],
              endTimeList: [],
              timeList: []
            })
          }
        } else if (this.form.type === 4) {
          if (this.form.cycleLevelList.length > 11) {
            return
          } else {
            this.form.cycleLevelList.push({
              name: '',
              startTimeList: [],
              endTimeList: [],
              timeList: []
            })
          }
        } else {
          this.form.cycleLevelList.push({
            name: '',
            startTimeList: [],
            endTimeList: [],
            timeList: []
          })
        }
      } else {
        this.form.cycleLevelList.push({
          name: '',
          startTimeList: [],
          endTimeList: [],
          timeList: []
        })
      }
    },
    startValidate(row, val, callback) {
      if (!val.length) {
        callback(new Error('请选择开始日期'))
      }
      if (row.endTimeList.length) {
        if (this.form.type === 5) {
          // 只判断自定义时间，结束如期比开始日期大就可以
          if (val[0] > row.endTimeList[0]) {
            callback(new Error('日期区间选择错误'))
          }
          if (val[0] <= row.endTimeList[0] && val[1] > row.endTimeList[1]) {
            callback(new Error('日期区间选择错误'))
          }
          if (val[0] <= row.endTimeList[0] && val[1] <= row.endTimeList[1] && val[2] > row.endTimeList[2]) {
            callback(new Error('日期区间选择错误'))
          }
        } else {
          // 不是自定义时间全部以当前年为准
          // if (val[0] !== 2) {
          //   callback(new Error('日期区间选择错误'))
          // }
        }
      }
      callback()
    },
    endValidate(row, val, callback) {
      if (!val.length) {
        callback(new Error('请选择结束日期'))
      } else {
        callback()
      }
      // switch (this.form.type) {
      //   case 0:
      //     if (val[0] - row.startTimeList[0] === 0) {
      //       // 当前年
      //       if (val[1] - row.startTimeList[1] !== 11 || row.startTimeList[2] !== val[2]) {
      //         callback(new Error('日期区间选择错误'))
      //       }
      //     } else if (val[0] - row.startTimeList[0] === 1) {
      //       // 相邻两年
      //       if (row.startTimeList[1] !== val[1] || row.startTimeList[2] !== val[2]) {
      //         callback(new Error('日期区间选择错误'))
      //       }
      //     } else {
      //       // 大于1年直接报错，不允许跨2年
      //       callback(new Error('日期区间选择错误'))
      //     }
      //     break
      //   case 1:
      //     if (val[0] - row.startTimeList[0] === 0) {
      //       // 当前年
      //       if (val[1] - row.startTimeList[1] !== 5 || row.startTimeList[2] !== val[2]) {
      //         callback(new Error('日期区间选择错误'))
      //       }
      //     } else if (val[0] - row.startTimeList[0] === 1) {
      //       // 相邻两年
      //       if (12 - row.startTimeList[1] + val[1] !== 6 || row.startTimeList[2] !== val[2]) {
      //         callback(new Error('日期区间选择错误'))
      //       }
      //     } else {
      //       // 大于1年直接报错，不允许跨2年
      //       callback(new Error('日期区间选择错误'))
      //     }
      //     break
      //   case 2:
      //     // 季度
      //     if (val[0] - row.startTimeList[0] === 0) {
      //       // 当前年
      //       if (val[1] - row.startTimeList[1] !== 3 || row.startTimeList[2] !== val[2]) {
      //         callback(new Error('日期区间选择错误'))
      //       }
      //     } else if (val[0] - row.startTimeList[0] === 1) {
      //       // 相邻两年
      //       if (12 - row.startTimeList[1] + val[1] !== 3 || row.startTimeList[2] !== val[2]) {
      //         callback(new Error('日期区间选择错误'))
      //       }
      //     } else {
      //       // 大于1年直接报错，不允许跨2年
      //       callback(new Error('日期区间选择错误'))
      //     }
      //     break
      //   case 3:
      //     // 双月
      //     if (val[0] - row.startTimeList[0] === 0) {
      //       // 当前年
      //       if (val[1] - row.startTimeList[1] !== 2 || row.startTimeList[2] !== val[2]) {
      //         callback(new Error('日期区间选择错误'))
      //       }
      //     } else if (val[0] - row.startTimeList[0] === 1) {
      //       // 相邻两年
      //       if (12 - row.startTimeList[1] + val[1] !== 2 || row.startTimeList[2] !== val[2]) {
      //         callback(new Error('日期区间选择错误'))
      //       }
      //     } else {
      //       // 大于1年直接报错，不允许跨2年
      //       callback(new Error('日期区间选择错误'))
      //     }
      //     break
      //   case 4:
      //     // 单月
      //     if (val[0] - row.startTimeList[0] === 0) {
      //       // 当前年
      //       if (val[1] - row.startTimeList[1] !== 1 || row.startTimeList[2] !== val[2]) {
      //         callback(new Error('日期区间选择错误'))
      //       }
      //     } else if (val[0] - row.startTimeList[0] === 1) {
      //       // 相邻两年
      //       if (12 - row.startTimeList[1] + val[1] !== 1 || row.startTimeList[2] !== val[2]) {
      //         callback(new Error('日期区间选择错误'))
      //       }
      //     } else {
      //       // 大于1年直接报错，不允许跨2年
      //       callback(new Error('日期区间选择错误'))
      //     }
      //     break
      //   default:
      //     break
      // }
    },
    // 获取添加内容的条数
    getSum(n) {
      if (n === 0) return 1
      if (n === 1) return 2
      if (n === 2) return 4
      if (n === 3) return 6
      if (n === 4) return 12
    },
    // 周期类型更改
    cycleTypeChange(val) {
      if (this.form.setType === 0 && val !== 5) {
        this.form.cycleLevelList = []
        for (let i = 0; i < this.getSum(val); i++) {
          this.form.cycleLevelList.push({
            name: '',
            startTimeList: [],
            endTimeList: [],
            timeList: []
          })
        }
      } else {
        this.form.cycleLevelList = [
          {
            name: '',
            startTimeList: [],
            endTimeList: [],
            timeList: []
          }
        ]
      }
      if (val != 6) {
        this.realTimeMin = ''
      }
    },
    // 根据周期类型，开始更改之后，需要更新结束日期
    startDateChange(val, row) {
      if (this.form.type === '') {
        return
      }
      if (this.form.type !== 5) {
        this.$api.getPresetTime({ type: this.form.type, startTime: val }).then((res) => {
          if (res.code === '200') {
            this.form.cycleLevelList.forEach((el, index) => {
              el.startTimeList = res.data.list[index].startTimeList
              el.endTimeList = res.data.list[index].endTimeList
            })
          }
        })
      }
    },
    setTypeChange(val) {
      if (val === 1) {
        this.form.cycleLevelList = [
          {
            name: '',
            startTimeList: [],
            endTimeList: [],
            timeList: []
          }
        ]
      } else {
        this.form.cycleLevelList = []
        for (let i = 0; i < this.getSum(this.form.type); i++) {
          this.form.cycleLevelList.push({
            name: '',
            startTimeList: [],
            endTimeList: [],
            timeList: []
          })
        }
      }
    },
    timeListChange(val, row) {
      row.startTime = val[0]
      row.endTime = val[1]
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          let header = {}
          if (this.form.id) {
            header = {
              'operation-type': 2,
              'operation-id': this.form.id,
              'operation-name': this.form.name
            }
          } else {
            header = {
              'operation-type': 1
            }
          }
          const params = {
            ...this.form
          }
          if (this.form.type == 6) {
            params.realTimeMin = this.realTimeMin
            delete params.setType
          }
          this.$api.updateConfigRule(params, header).then((res) => {
            if (res.code === '200') {
              this.$message.success('操作成功')
              this.goBack()
            }
          })
        }
      })
    },
    goBack() {
      this.$router.go(-1)
    },
    del(i) {
      if (this.form.cycleLevelList.length > 1) {
        this.form.cycleLevelList.splice(i, 1)
      } else {
        this.$message.warning('请至少保留一条数据')
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.container-content > div {
  background-color: #fff;
  height: 100%;
  position: relative;
  .pageContainer {
    width: 100%;
    height: calc(100% - 100px);
    overflow: auto;
    padding: 16px 24px;
  }
}
.footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  padding: 10px 20px;
  background-color: #fff;
  text-align: right;
  border-top: 1px solid #ebeef5;
}
.title {
  font-size: 20px;
  font-weight: 700;
  color: #303133;
  padding: 12px 20px 0;
  margin-bottom: 12px;
}
::v-deep .el-table__cell {
  .el-form-item {
    margin-bottom: 0;
  }
}
</style>
