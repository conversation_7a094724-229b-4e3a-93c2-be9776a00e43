<template>
  <PageContainer :footer="true">
    <div slot="content" class="form-content">
      <div class="form-title"><i class="el-icon-arrow-left" @click="$router.go(-1)"></i>{{ pageTitle }}</div>
      <div class="content-main">
        <!-- 基础信息表单 -->
        <div class="main-title">基础信息</div>
        <el-form ref="formRef" :model="formModel" :rules="rules" label-width="130px" :disabled="['detail'].includes($route.query.type)">
          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item label="名称" prop="name">
                <el-input v-model="formModel.name" placeholder="请输入名称" maxlength="10" show-word-limit clearable />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="是否出入口" prop="isEntrance">
                <el-radio-group v-model="formModel.isEntrance">
                  <el-radio v-for="item in yesNoOptions" :key="item.value" :label="item.value">
                    {{ item.label }}
                  </el-radio>
                </el-radio-group>
                <el-tooltip effect="dark" content="出入口为医院主要大门的出入口，选为出入口后人流量不计入所在楼层的计算。" placement="top">
                  <i class="el-icon-question"></i>
                </el-tooltip>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="所在楼层" prop="floorId">
                <el-select ref="treeSelect" v-model="formModel.regionCode" placeholder="请选择所在楼层" clearable style="width: 100%" @clear="handleClear">
                  <el-option hidden :value="formModel.regionCode" :label="formModel.regionName"> </el-option>
                  <el-tree :data="spaceData" :props="spaceProps" :expand-on-click-node="false" :check-on-click-node="true" @node-click="handleNodeClick"> </el-tree>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <!-- 设备 -->
        <div class="main-title">通行监控设备</div>
        <div class="device-section">
          <div class="device-actions">
            <el-button v-if="$route.query.type !== 'detail'" type="primary" icon="el-icon-plus" @click="handleAddDevice"> 添加设备 </el-button>
            <el-date-picker
              v-else
              v-model="detailDate"
              value-format="yyyy-MM-dd"
              popper-class="timePicker"
              type="date"
              :picker-options="pickerOptions"
              placeholder="选择日期"
              @change="dateChange"
            />
          </div>
          <TablePage ref="exitTable" :table-column="deviceTableColumns" :data="devices" :show-page="false" height="calc(100% - 50px)">
            <template #actions="{ row, index }">
              <el-button type="text" size="small" style="color: #f56c6c" @click="handleDeleteDevice(index)"> 移除 </el-button>
            </template>
          </TablePage>
        </div>
      </div>
      <selectMonitorDevice
        v-if="isSelectMonitorDevice"
        :dialogShow.sync="isSelectMonitorDevice"
        harvesterCheckType="checkbox"
        @submitDialog="selectMonitorDeviceSubmit"
        @closeDialog="isSelectMonitorDevice = false"
      />
    </div>
    <div slot="footer">
      <el-button style="padding: 8px 22px" @click="$router.go(-1)">取消</el-button>
      <el-button type="primary" :loading="submitLoading" @click="submitForm">确认</el-button>
    </div>
  </PageContainer>
</template>
<script>
import { transData } from '@/util'
var pageTitle
export default {
  name: 'add',
  components: {
    selectMonitorDevice: () => import('../components/selectMonitorDevice.vue')
  },
  // 路由前置守卫动态修改meta中的title
  beforeRouteEnter(to, from, next) {
    if (to.meta.title) {
      const typeList = {
        add: '新增',
        edit: '编辑',
        detail: '详情'
      }
      pageTitle = typeList[to.query.type] ?? '新增'
      to.meta.title = pageTitle
    }
    next()
  },
  data() {
    return {
      pageTitle,
      // 表单数据
      formModel: {
        name: '', // 名称
        isEntrance: 0, // 是否出入口，默认为否
        regionCode: '', // 所在楼层ID
        regionName: '' // 所在楼层名称
      },
      // 表单验证规则
      rules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' },
          { max: 10, message: '名称不能超过10个字符', trigger: 'blur' }
        ],
        isEntrance: [{ required: true, message: '请选择是否出入口', trigger: 'change' }],
        regionCode: [{ required: true, message: '请选择所在楼层', trigger: 'change' }]
      },
      // 是否出入口选项
      yesNoOptions: [
        { value: 0, label: '是' },
        { value: 1, label: '否' }
      ],
      devices: [], // 设备列表
      // 提交loading状态
      submitLoading: false,
      // 是否选择设备弹窗
      isSelectMonitorDevice: false,
      // 空间数据
      spaceData: [],
      // 空间数据配置
      spaceProps: {
        label: 'ssmName',
        isLeaf: 'leaf',
        children: 'children'
      },
      // 详情日期
      detailDate: '',
      // 日期选择器配置
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      }
    }
  },
  computed: {
    deviceTableColumns() {
      if (this.$route.query.type === 'detail') {
        return [
          { prop: 'assetName', label: '设备名除', minWidth: 150 },
          { prop: 'enterCount', label: '进入人次', minWidth: 150 },
          { prop: 'leaveCount', label: '高开人次', minWidth: 150 }
        ]
      } else {
        return [
          { prop: 'assetName', label: '设备名称', minWidth: 150 },
          { slot: 'actions', label: '操作', width: 100, fixed: 'right' }
        ]
      }
    }
  },
  created() {
    // 初始化数据
    this.initData()
  },
  methods: {
    // 初始化数据
    initData() {
      this.spaceTreeListFn()
      if (['detail', 'edit'].includes(this.$route.query.type) && this.$route.query.id) {
        this.getDetailData()
      }
    },
    dateChange() {
      console.log('dateChange', this.detailDate)
    },
    // 获取详情数据
    async getDetailData() {
      try {
        const id = this.$route.query.id
        const res = await this.$api.getPeopleFlowMonitorConfigDetail(id)
        console.log('getDetailData', res)
        if (res.code === '200') {
          const data = res.data
          this.formModel = {
            name: data.name,
            isEntrance: data.isEntrance,
            regionCode: data.regionCode,
            regionName: data.regionName
          }
          this.devices = data.assetList || []
        }
      } catch (error) {
        this.$message.error('获取详情数据失败')
        console.error('获取详情数据失败:', error)
      }
    },
    // 选择设备完成
    selectMonitorDeviceSubmit(data) {
      console.log('selectMonitorDeviceSubmit', data)
      // 过滤掉已经添加的设备
      const existingDeviceIds = this.devices.map((d) => d.deviceId)
      const filteredData = data.filter((item) => !existingDeviceIds.includes(item.id))
      if (filteredData.length === 0) {
        this.$message.warning('所选设备均已添加，不能重复添加')
        return
      }
      this.devices.push(
        ...filteredData.map((item) => ({
          deviceId: item.id,
          assetId: item.assetId || '',
          assetName: item.assetsName || ''
        }))
      )
    },
    // 添加设备
    handleAddDevice() {
      this.isSelectMonitorDevice = true
    },
    // 删除设备
    handleDeleteDevice(index) {
      this.devices.splice(index, 1)
    },
    // 空间数据清除
    handleClear() {
      this.formModel.regionCode = ''
      this.formModel.regionName = ''
    },
    // 选择下拉树 数据
    handleNodeClick(data) {
      this.formModel.regionCode = data.id
      this.formModel.regionName = data.ssmName
      this.$refs.treeSelect.blur()
    },
    spaceTreeListFn() {
      this.$api.spaceTreeList().then((res) => {
        if (res.code == 200) {
          this.spaceData = transData(res.data, 'id', 'pid', 'children')
        }
      })
    },
    // 提交表单
    async submitForm() {
      try {
        // 验证基础表单
        const valid = await this.$refs.formRef.validate()
        if (!valid) return
        if (!this.devices.length) {
          this.$message.warning('请至少添加一个设备')
          return
        }
        this.submitLoading = true
        // 构建提交数据
        const submitData = {
          ...this.formModel,
          assetList: this.devices
        }
        const res = await this.$api.addPeopleFlowMonitorConfig(submitData)
        if (res.code === '200') {
          this.$message.success('保存成功')
          this.$router.go(-1)
        } else {
          throw new Error(res.message || '保存失败')
        }
      } catch (error) {
        this.$message.error(error.message || '保存失败')
      } finally {
        this.submitLoading = false
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.form-content {
  height: 100%;
  overflow-y: auto;
  .form-title {
    height: 40px;
    line-height: 40px;
    background: #fff;
    border-bottom: 1px solid #dcdfe6;
    font-size: 15px;
    font-family: 'PingFang SC-Medium', 'PingFang SC';
    font-weight: 500;
    color: #121f3e;
    padding-left: 15px;
    i {
      width: 20px;
      cursor: pointer;
    }
  }
  .content-main {
    height: calc(100% - 40px);
    width: 100%;
    background: #fff;
    padding: 24px 48px;
    display: flex;
    flex-direction: column;
    overflow-y: hidden;
    .main-title {
      font-weight: bold;
      font-size: 16px;
      color: #333333;
      margin-bottom: 24px;
    }
    // 设备管理样式
    .device-section {
      flex: 1;
      .device-actions {
        margin-bottom: 16px;
        display: flex;
        justify-content: flex-start;
      }
    }
    // 提示图标样式
    .el-icon-question {
      font-size: 14px;
      margin-left: 8px;
      color: #909399;
      cursor: help;
      &:hover {
        color: #409eff;
      }
    }
  }
}
</style>
