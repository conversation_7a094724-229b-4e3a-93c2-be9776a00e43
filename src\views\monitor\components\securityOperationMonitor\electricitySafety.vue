<template>
  <div class="securityOperationMonitor-content">
    <div class="statistics-list">
      <div v-for="(item, index) in statisticsList" :key="index" class="statistics-item"
        :style="{ cursor: item.isClick ? 'pointer' : '' }" @click="onStatisticsList(item)">
        <p class="item-title">{{ item.title }}</p>
        <p class="item-value">{{ item.value || 0 }}<span>个</span></p>
        <img class="item-icon" :src="item.icon" :alt="item.title" />
      </div>
    </div>
    <div class="content-main">
      <div class="search-from">
        <el-input v-model="searchFrom.menuName" placeholder="请输入监控区名称" clearable style="width: 200px" />
        <el-input v-model="searchFrom.surveyName" placeholder="请输入监控节点名称" clearable style="width: 200px" />
        <div style="display: inline-block">
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="searchForm">查询</el-button>
        </div>
      </div>
      <div v-loading="listLoading" class="main-content">
        <div v-for="item in list" :key="item.menuCode" class="grouping">
          <div class="grouping_header">
            <div class="grouping_header_left">
              <img src="@/assets/images/monitor/electricity.png" alt="" />
              <p>{{ item.menuName }}</p>
              <div v-if="item.menuAlarm == '0'" class="grouping_header_left_badge">
                <img src="@/assets/images/monitor/general_alarm.png" alt="" />
                <!-- <span>{{item.menuAlarm}}</span> -->
              </div>
            </div>
            <div class="grouping_header_right">
              <div class="grouping_information">
                <div>
                  <p>监测点总数：</p>
                  <span>{{ item.monitorNum.monitoringPointNum }}</span>
                </div>
              </div>
              <div class="grouping_information">
                <div>
                  <p>正常节点数：</p>
                  <span>{{ item.monitorNum.normalNodeNum }}</span>
                </div>
              </div>
              <div class="grouping_information">
                <div>
                  <p>异常节点数：</p>
                  <span>{{ item.monitorNum.abnormalNodeNum }}</span>
                </div>
              </div>
              <div class="grouping_information">
                <div>
                  <p>离线节点数：</p>
                  <span>{{ item.monitorNum.offlineNodeNum }}</span>
                </div>
              </div>
              <div class="grouping_information">
                <div>
                  <p>今日报警：</p>
                  <span>{{ item.monitorNum.todayAlarmNum }}</span>
                </div>
              </div>
              <div class="grouping_information">
                <div>
                  <p>未处理报警：</p>
                  <span>{{ item.monitorNum.notHandleWarnNum }}</span>
                </div>
              </div>
            </div>
          </div>
          <el-row :gutter="16">
            <el-col v-for="v in item.surveyList" :key="v.surveyCode" :xs="12" :sm="12" :md="8" :xl="6">
              <div class="entity_box" :class="v.alarm == '0' ? 'entity_box_alarm' : ''">
                <div class="entity_header" @click="jumpMonitorDetail(v)">
                  <span>{{ v.surveyName }}</span>
                  <div class="entity_type entity_type_no">
                    <img v-if="v.onlineOrOffline == '离线'" src="@/assets/images/monitor/offLine.png" alt="" />
                    <img v-else src="@/assets/images/monitor/onLine.png" alt="" />
                    <p>{{ v.onlineOrOffline }}</p>
                  </div>
                </div>
                <div v-scrollMove class="entity_parameter" @click="toDetails(v)">
                  <div v-for="(ele, index) in v.paramList" :key="v.surveyCode + ele.parameterId"
                    class="entity_parameter_item"
                    :class="{ entity_parameter_item_bor: index == 0, entity_parameter_item_red: ele.warn == '0' }">
                    <p>{{ ele.paramName }}</p>
                    <div class="entity_parameter_num">
                      <span class="entity_parameter_value">{{ (isNaN(ele.paramValue) ? ele.paramValue :
                        Number(ele.paramValue).toFixed(2)) || '-' }}</span>
                      <span class="entity_parameter_unit">{{ ele.paramUnit }}</span>
                    </div>
                  </div>
                </div>
                <div class="alarm">
                  <div class="alarm_item">
                    <p>今日报警：</p>
                    <span>{{ v.alarmNum }}</span>
                  </div>
                  <div class="alarm_item">
                    <p>未处理报警：</p>
                    <span>{{ v.notHandleAlarmNum }}</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
        <NodeDetails ref="nodeDetails" />
      </div>
      <div class="main-footer">
        <el-pagination :current-page="pagination.current" :page-sizes="[3, 6, 9]" :page-size="pagination.size"
          :layout="pagination.layoutOptions" :total="pagination.total" @size-change="paginationSizeChange"
          @current-change="paginationCurrentChange" />
      </div>
    </div>
  </div>
</template>
<script>
import moment from 'moment'
import sbzsIcon from '@/assets/images/monitor/sbzs_icon.png'
import zcsbIcon from '@/assets/images/monitor/zcsb_icon.png'
import lxIcon from '@/assets/images/monitor/lx_icon.png'
import jrbjsIcon from '@/assets/images/monitor/jrbjs_icon.png'
import wclbjsIcon from '@/assets/images/monitor/wclbjs_icon.png'
import tableListMixin from '@/mixins/tableListMixin.js'
import NodeDetails from './nodeDetails.vue'
export default {
  components: { NodeDetails },
  mixins: [tableListMixin],
  props: {
    projectCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      listLoading: false,
      searchFrom: {
        menuName: null,
        surveyName: null,
        status: null
      },
      list: [],
      statisticsList: [
        {
          title: '监测点总数',
          icon: sbzsIcon,
          value: 0,
          isClick: false
        },
        {
          title: '正常节点数',
          icon: zcsbIcon,
          value: 0,
          status: '0',
          isClick: true
        },
        {
          title: '异常节点数',
          icon: lxIcon,
          value: 0,
          status: '6',
          isClick: false
        },
        {
          title: '离线节点数',
          icon: jrbjsIcon,
          value: 0,
          isClick: true,
          status: '6'
        },
        {
          title: '监控区数',
          icon: wclbjsIcon,
          value: 0,
          isClick: false
        },
        {
          title: '今日报警数',
          icon: sbzsIcon,
          value: 0,
          isClick: true,
          params: { dataRange: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')] }
        },
        {
          title: '未处理报警',
          icon: sbzsIcon,
          value: 0,
          isClick: true,
          params: { alarmStatus: '0' }
        }
      ]
    }
  },
  mounted() {
    this.pagination.size = 3
    this.getQuerySurveyCount()
    this.getDataList()
  },
  methods: {
    jumpMonitorDetail(v) {
      console.log(v, '789');

      const item = {
        ...v,
        surveyEntityCode: v.surveyCode,
        surveyEntityName: v.surveyName
      }
      this.$emit('jumpDetail', item)
    },
    toDetails(v) {
      this.$refs.nodeDetails.getEchartData(v, this.projectCode)
    },
    onStatisticsList(item) {
      if (item.isClick) {
        if (item.status) {
          this.pagination.current = 1
          this.searchFrom.status = item.status
          this.getDataList()
        } else {
          this.goToAlarm(item.params)
        }
      }
    },
    // 跳转报警
    goToAlarm(params) {
      this.$router.push({
        name: 'allAlarmIndex',
        params: {
          projectCode: this.projectCode,
          alarmStatus: '3',
          ...params
        }
      })
    },
    getDataList() {
      let data = {
        projectCode: this.projectCode,
        startTime: moment().format('YYYY-MM-DD'),
        endTime: moment().format('YYYY-MM-DD'),
        currentPage: this.pagination.current,
        pageSize: this.pagination.size,
        ...this.searchFrom
      }
      this.listLoading = true
      this.$api.queryMenuByPage(data).then((res) => {
        this.pagination.total = res.data.count
        this.list = res.data.list
        this.listLoading = false
      })
    },
    getQuerySurveyCount() {
      let data = {
        projectCode: this.projectCode,
        startTime: moment().format('YYYY-MM-DD'),
        endTime: moment().format('YYYY-MM-DD')
      }
      this.$api.querySurveyCount(data).then((res) => {
        this.statisticsList[0].value = res.data.monitoringPointNum
        this.statisticsList[1].value = res.data.normalNodeNum
        this.statisticsList[2].value = res.data.abnormalNodeNum
        this.statisticsList[3].value = res.data.offlineNodeNum
        this.statisticsList[4].value = res.data.monitoringAreaNum
        this.statisticsList[5].value = res.data.todayAlarmNum
        this.statisticsList[6].value = res.data.notHandleWarnNum
      })
    },
    resetForm() {
      this.searchFrom.menuName = null
      this.searchFrom.surveyName = null
      this.searchFrom.status = ''
      this.getDataList()
    },
    searchForm() {
      this.pagination.current = 1
      this.getDataList()
    }
  }
}
</script>
<style lang="scss" scoped>
.securityOperationMonitor-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  overflow-x: hidden;

  .statistics-list {
    width: 100%;
    display: flex;
  }

  .statistics-item {
    width: calc(100% / 5);
    height: 120px;
    min-width: 150px;
    margin-left: 16px;
    padding: 24px;
    background: #fff;
    border-radius: 4px;
    margin-top: 16px;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .item-title {
      font-size: 15px;
      font-weight: 500;
      color: #121f3e;
    }

    .item-value {
      height: 36px;
      font-size: 30px;
      font-weight: bold;
      color: #121f3e;
      line-height: 36px;

      &>span {
        margin-left: 4px;
        font-size: 15px;
        font-weight: 500;
        color: #ccced3;
      }
    }

    .item-icon {
      position: absolute;
      right: 24px;
      bottom: 24px;
      width: 40px;
      height: 40px;
    }
  }

  .statistics-item:first-child {
    margin-left: 0;
  }

  .content-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin-top: 16px;
    background: #fff;
    overflow: hidden;

    .search-from {
      padding: 0 0 16px 16px;

      &>div {
        margin-top: 16px;
        margin-right: 16px;
      }
    }

    .main-content {
      flex: 1;
      padding: 0 16px;
      overflow: auto;

      .grouping {
        display: flex;
        flex-direction: column;
        background: #faf9fc;
        padding: 19px 16px;
        margin-bottom: 24px;

        .entity_box_alarm {
          background: linear-gradient(180deg, #ffffff 55%, #ffe2e2 100%) !important;
          border-color: #f53f3f !important;

          .alarm {
            background: transparent !important;
          }
        }

        .entity_box {
          padding: 16px;
          background: #ffffff;
          margin-bottom: 16px;
          border-radius: 4px;
          border: 1px solid #fff;

          .entity_header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;

            .entity_type {
              display: flex;
              align-items: center;
              padding: 8px;
              background: #e8ffea;

              p {
                margin: 0;
                font-size: 14px;
                line-height: 14px;
                color: #009a29;
              }

              img {
                margin-right: 7px;
              }
            }

            .entity_type_no {
              background: #f2f4f9;

              p {
                color: #86909c;
              }
            }
          }

          .entity_parameter {
            cursor: pointer;
            width: 100%;
            display: flex;
            margin: 18px 0 26px 0;
            overflow-x: hidden;
            // firefox隐藏滚动条
            scrollbar-width: none;

            // chrome隐藏滚动条
            &::-webkit-scrollbar {
              display: none;
            }

            .entity_parameter_item_bor {
              border-right: 1px solid #dcdfe6;
            }

            .entity_parameter_item_red {
              p {
                color: #fa403c !important;
              }

              .entity_parameter_value {
                color: #fa403c !important;
              }

              .entity_parameter_unit {
                color: #fa403c !important;
              }
            }

            .entity_parameter_item {
              padding: 0 5px;
              // border-right: 1px solid #DCDFE6;
              flex: 1;
              // min-width: 20%;
              max-width: 50%;
              display: flex;
              flex-direction: column;
              justify-content: space-between;

              p {
                margin: 0;
                margin-bottom: 10px;
                width: 100%;
                text-align: center;
                white-space: nowrap;
              }

              .entity_parameter_num {
                text-align: center;
                white-space: nowrap;

                span {
                  font-size: 18px;
                  font-family: Arial-Bold, Arial;
                  font-weight: bold;
                  color: #333333;
                }

                .entity_parameter_unit {
                  font-size: 12px;
                  font-family: PingFang SC-Medium, PingFang SC;
                  font-weight: 500;
                  color: #ccced3;
                  margin-left: 4px;
                }
              }
            }
          }

          .alarm {
            display: flex;
            padding: 8px 0;
            justify-content: space-around;
            background: #faf9fc;

            .alarm_item {
              display: flex;
              align-items: center;

              p {
                margin: 0;
                color: #666666;
              }

              span {
                font-size: 18px;
                line-height: 18px;
                font-family: Arial-Regular, Arial;
                font-weight: 400;
                color: #333333;
              }
            }
          }
        }

        &_header {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;

          &_left {
            display: flex;
            align-items: center;

            img {
              width: 18px;
              height: 18px;
            }

            p {
              font-size: 15px;
              font-family: PingFang SC-Medium, PingFang SC;
              font-weight: 500;
              color: #333333;
              margin: 0 10px;
            }

            &_badge {
              background: #f53f3f;
              border-radius: 100px;
              display: flex;
              align-items: center;
              padding: 3px 12px;

              img {
                width: 12px;
                height: 12px;
              }

              span {
                font-size: 12px;
                color: #ffffff;
                margin-left: 5px;
              }
            }
          }

          &_right {
            display: flex;

            .grouping_information:last-child {
              margin: 0;

              div {
                padding: 0;
                border: 0;
              }
            }

            .grouping_information {
              margin-right: 11px;
              padding: 5px 0;

              div {
                padding-right: 11px;
                display: flex;
                align-items: center;
                border-right: 1px solid #dcdfe6;
              }

              p {
                margin: 0;
                font-size: 14px;
                font-family: PingFang SC-Regular, PingFang SC;
                font-weight: 400;
                color: #666666;
                line-height: 14px;
              }

              span {
                font-size: 18px;
                font-family: Arial-Regular, Arial;
                font-weight: 400;
                color: #333333;
                line-height: 18px;
              }
            }
          }
        }
      }
    }

    .main-footer {
      padding: 10px 16px;
    }
  }
}
</style>
