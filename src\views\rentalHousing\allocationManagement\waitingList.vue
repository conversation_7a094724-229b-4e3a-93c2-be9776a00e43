<template>
  <div class="wing-container">
    <div class="table-search">
      <div class="table-search-left">
        <el-input v-model="searchInfo.userNameOrEmployeeId" style="width: 200px; margin-right: 8px" placeholder="姓名、工号" maxlength="60"></el-input>
        <el-input v-model="searchInfo.spaceName" style="width: 200px; margin-right: 8px" placeholder="小区名称" maxlength="60"></el-input>
        <el-select v-model="searchInfo.deptCode" filterable clearable placeholder="所属科室" style="margin-right: 8px" @change="changeDeptName">
          <el-option v-for="item in deptList" :key="item.id" :label="item.deptName" :value="item.id"> </el-option>
        </el-select>
        <el-select v-model="searchInfo.houseTypeCode" placeholder="申请房型" style="width: 110px; margin-right: 8px">
          <el-option v-for="item of houseTypeOptions" :key="item.dictCode" :label="item.dictName" :value="item.dictCode"></el-option>
        </el-select>
        <el-select v-model="searchInfo.applicationTypeId" placeholder="申请类型" style="width: 110px; margin-right: 8px">
          <el-option v-for="item in applicationTypeOptions" :key="item.dictCode" :label="item.dictName" :value="item.dictCode"> </el-option>
        </el-select>
        <el-date-picker
          v-model="searchInfo.date"
          type="datetimerange"
          start-placeholder="申请时间"
          range-separator="至"
          style="width: 380px; margin-right: 8px"
          value-format="yyyy-MM-dd HH:mm:ss"
          end-placeholder="申请时间"
          :default-time="['00:00:00', '23:59:59']"
        >
        </el-date-picker>
        <el-date-picker
          v-model="searchInfo.lhDate"
          type="datetimerange"
          start-placeholder="轮候时间"
          range-separator="至"
          style="width: 380px; margin-right: 8px"
          value-format="yyyy-MM-dd HH:mm:ss"
          end-placeholder="轮候时间"
          :default-time="['00:00:00', '23:59:59']"
        >
        </el-date-picker>
      </div>
      <div class="table-search-right">
        <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db" @click="resetCondition">重置</el-button>
        <el-button type="primary" style="font-size: 14px" @click="searchByCondition">查询</el-button>
        <el-button type="primary" style="font-size: 14px" @click="moveInWing()" :disabled="multipleSelection.length == 0">移入待配房</el-button>
        <el-button type="primary" style="font-size: 14px; background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db" @click="handExport">导出</el-button>
      </div>
    </div>
    <div class="table-box">
      <div class="table-list">
        <el-table
          ref="materialTable"
          v-loading="tableLoading"
          :data="tableData"
          height="calc(100% - 10px)"
          border
          :header-cell-style="{ background: '#F6F5FA' }"
          style="width: 100%"
          :cell-style="{ padding: '8px 0 8px 0' }"
          stripe
          highlight-current-row
          :empty-text="emptyText"
          row-key="id"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" :reserve-selection="true"> </el-table-column>
          <el-table-column label="序号" type="index" width="70">
            <template slot-scope="scope">
              <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="userName" label="姓名"></el-table-column>
          <el-table-column show-overflow-tooltip prop="userEmployeeId" label="工号"></el-table-column>
          <el-table-column show-overflow-tooltip prop="userGender" label="性别">
            <template slot-scope="scope">
              {{ scope.row.userGender | sexFilter }}
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="userIdCard" label="身份证号"></el-table-column>
          <el-table-column show-overflow-tooltip prop="userEntryTime" label="入职日期"></el-table-column>
          <el-table-column show-overflow-tooltip prop="userDepartment" label="所属科室"></el-table-column>
          <el-table-column show-overflow-tooltip prop="processName" label="申请类型" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="applyCommunityName" label="申请小区" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="houseTypeName" label="申请房型" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="acceptAdjustment" label="是否接受调剂" align="center">
            <template slot-scope="scope">
              {{ scope.row.acceptAdjustment | acceptAdjustmentFilter }}
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="processTime" label="申请时间" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip prop="processTime" label="轮候时间" align="center"></el-table-column>
          <el-table-column label="操作" align="center" width="200">
            <template slot-scope="scope">
              <el-link type="primary" @click="openDetails(scope.row)"> 申请详情 </el-link>
              <el-link type="primary" style="margin-left: 10px" @click="handRushDoClick(scope.row)"> 移入待配房 </el-link>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="table-pagination" style="padding-top: 10px; text-align: right">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="paginationData.currentPage"
          :page-sizes="[15, 30, 50, 100]"
          :page-size="paginationData.pageSize"
          :total="paginationData.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>
  <script>
import axios from 'axios'
export default {
  components: {},
  data() {
    return {
      tableLoading: false,
      emptyText: '暂无数据',
      searchInfo: {
        userNameOrEmployeeId: '', // 姓名或工号
        spaceName: '', // 小区名称
        houseTypeCode: '', // 申请房型
        applicationTypeId: '', // 申请类型
        deptCode: '', // 部门
        deptName: '',
        date: [], // 申请时间
        lhDate: [] // 轮候时间
      },
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      deptList: [],
      applicationTypeOptions: [],
      houseTypeOptions: [],
      tableData: [],
      multipleSelection: []
    }
  },
  filters: {
    sexFilter(val) {
      let sex = {
        1: '男',
        2: '女'
      }
      if (!val) return ''
      if (val === '男') return val
      if (val === '女') return val
      return sex[val]
    },
    acceptAdjustmentFilter(val) {
      if (!val) return ''
      return val == 1 ? '是' : '否'
    }
  },
  mounted() {
    this.getDeptDataList()
    this.getTypeDataList()
    this.searchByCondition()
  },
  methods: {
    openDetails(row) {
      this.$router.push({
        path: 'allocationDetail',
        query: {
          id: row.id || '',
          activeName: '0'
        }
      })
    },
    moveInWing(id) {
      let html = `<div style="display: flex;align-items: center;">
            <span class="el-icon-warning" style="color: #f59a23;font-size: 22px;margin-right: 5px;"></span><span style="font-weight: 600;">变更确认</span>
        </div>
        <div style="padding-left: 28px;">是否将所选成员移入待配房列表</div>`
      this.$alert(html, '', {
        dangerouslyUseHTMLString: true,
        showClose: false,
        showCancelButton: true
      })
        .then(() => {
          let arr = id ? [id] : this.multipleSelection.map((item) => item.extendId)
          let params = {
            extendIdList: arr
          }
          this.$api.rentalHousingApi.moveToWaitAllocation(params).then((res) => {
            if (res.code == 200) {
              this.$message.success(res.message)
              this.$emit('updateStatistics')
              this.searchByCondition()
            } else {
              this.$message.error(res.message)
            }
          })
        })
        .catch(() => {
          //   this.$emit('xfDialogOpen')
        })
    },
    handRushDoClick(row) {
      this.moveInWing(row.extendId)
    },
    // 表格多选
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 导出
    handExport() {
      const userInfo = this.$store.state.user.userInfo.user
      const params = {
        pageSize: this.paginationData.pageSize,
        pageNum: this.paginationData.currentPage,
        queryType: '5',
        userNameOrEmployeeId: this.searchInfo.userNameOrEmployeeId,
        spaceName: this.searchInfo.spaceName,
        userDepartmentId: this.searchInfo.deptCode,
        userDepartment: this.searchInfo.deptName,
        userName: userInfo.staffName,
        userId: userInfo.staffId,
        houseTypeCode: this.searchInfo.houseTypeCode,
        processType: this.searchInfo.applicationTypeId,
        startTime: this.searchInfo.date.length ? this.searchInfo.date[0] : '',
        endTime: this.searchInfo.date.length ? this.searchInfo.date[1] : '',
        abandonStartTime: this.searchInfo.lhDate.length ? this.searchInfo.lhDate[0] : '',
        abandonEndTime: this.searchInfo.lhDate.length ? this.searchInfo.lhDate[1] : '',
        unitCode: userInfo.unitCode ?? 'BJSYGJ',
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY'
      }
      axios({
        method: 'post',
        url: __PATH.VUE_RHMS_API + 'allocation/houseAllocationExport',
        data: params,
        responseType: 'blob',
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token,
          unitCode: userInfo.unitCode ?? 'BJSYGJ',
          hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY'
        }
      })
        .then((res) => {
          const name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
          const blob = new Blob([res.data])
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
        })
        .catch(() => {
          this.$message.error('下载失败')
        })
    },
    changeDeptName(val) {
      if (val) {
        this.searchInfo.deptName = this.deptList.find((i) => i.id === val).deptName
      } else {
        this.searchInfo.deptName = ''
      }
    },
    /** 重置 */
    resetCondition() {
      this.paginationData = {
        currentPage: 1,
        pageSize: 15,
        total: 0
      }
      this.searchInfo = {
        userNameOrEmployeeId: '',
        applicantId: '',
        houseTypeCode: '',
        deptCode: '',
        deptName: '',
        date: [],
        lhDate: [],
        spaceName: ''
      }
      this.getTableData()
    },
    /** 查询 */
    searchByCondition() {
      this.paginationData.currentPage = 1
      this.getTableData()
    },
    /** 获取列表 */
    getTableData() {
      const userInfo = this.$store.state.user.userInfo.user
      let params = {
        pageSize: this.paginationData.pageSize,
        pageNum: this.paginationData.currentPage,
        queryType: '5',
        userNameOrEmployeeId: this.searchInfo.userNameOrEmployeeId,
        spaceName: this.searchInfo.spaceName,
        userDepartmentId: this.searchInfo.deptCode,
        userDepartment: this.searchInfo.deptName,
        userName: userInfo.staffName,
        userId: userInfo.staffId,
        houseTypeCode: this.searchInfo.houseTypeCode,
        processType: this.searchInfo.applicationTypeId,
        startTime: this.searchInfo.date.length ? this.searchInfo.date[0] : '',
        endTime: this.searchInfo.date.length ? this.searchInfo.date[1] : '',
        startTime: this.searchInfo.lhDate.length ? this.searchInfo.lhDate[0] : '',
        endTime: this.searchInfo.lhDate.length ? this.searchInfo.lhDate[1] : ''
      }
      this.tableLoading = true
      this.$api.rentalHousingApi
        .getRoomAllocationList(params)
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.records
            this.paginationData.total = res.data.total
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取失败'))
        .finally(() => (this.tableLoading = false))
    },
    /** 获取科室 */
    getDeptDataList() {
      this.$api.getSelectedDept().then((res) => {
        if (res.code == '200') {
          this.deptList = res.data
        }
      })
    },
    /** 获取申请类型 */
    getTypeDataList() {
      const params = {
        pageSize: 99999,
        pageNum: 1
      }
      this.$api.rentalHousingApi
        .queryDictPage(params)
        .then((res) => {
          if (res.code === '200') {
            res.data.records.forEach((it) => {
              // 只取启用的
              if (it.dictState !== '1') return
              if (it.dictType === 'fangxing') {
                this.houseTypeOptions.push(it)
              } else if (it.dictType === 'shenqingleixing') {
                this.applicationTypeOptions.push(it)
              }
            })
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取列表数据失败'))
        .finally(() => {})
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.getTableData()
    },
    // 每页的条数
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.paginationData.currentPage = 1
      this.getTableData()
    }
  }
}
</script>
  <style lang="scss" scoped>
.wing-container {
  height: 100%;
  .table-search {
    margin-bottom: 10px;
    .table-search-left,
    .table-search-right {
      flex-shrink: 0;
    }
    .table-search-left {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
    }
  }
  .btn-box {
    margin-bottom: 10px;
  }
  .table-box {
    height: calc(100% - 82px);
    .table-list {
      height: calc(100% - 82px);
    }
  }
}
</style>