<template>
  <PageContainer v-loading="loading" class="elecAlarmCenter">
    <div slot="header" class="control-btn-header">
      <div v-if="hasQueryParams" class="header-back">
        <i
          class="el-icon-arrow-left"
          @click="
            () => {
              $router.go(-1)
            }
          "
        />
        <span
          @click="
            () => {
              $router.go(-1)
            }
          "
        >返回</span
        >
      </div>
      <div class="header-search">
        <el-date-picker
          v-model="filters.daterange"
          type="daterange"
          value-format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="pickerOptions"
        />
        <el-input v-model.trim="filters.surveyName" style="width: 200px" clearable :placeholder="entityConvertName + '名称'" />
        <el-select v-model="filters.disposeResult" style="width: 200px" placeholder="处置状态" clearable>
          <el-option v-for="item in chuzhiList" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
        <el-select v-model="filters.alarmLevelId" placeholder="报警等级" clearable>
          <el-option v-for="item in alarmLevelOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
        </el-select>
        <el-select v-model="filters.parameterId" placeholder="报警类型" clearable>
          <el-option v-for="item in alarmTypeArr" :key="item.parameterId" :label="item.parameterName" :value="item.parameterId + ''"> </el-option>
        </el-select>
        <el-cascader
          ref="regionCode"
          v-model="selectRegionCodeList"
          :props="riskPropsType"
          :options="regionCodeList"
          placeholder="所属区域"
          class="cascaderWid"
          :show-all-levels="false"
          style="margin-right: 10px"
        ></el-cascader>
        <div style="display: inline-block">
          <el-button type="primary" plain @click="reset()">重置</el-button>
          <el-button type="primary" @click="search()">查询</el-button>
          <!-- <el-button type="primary" icon="el-icon-download" @click="downLoad">导出</el-button> -->
          <el-button type="primary" :disabled="multipleSelection.length == 0" @click="batching">批处理</el-button>
        </div>
      </div>
    </div>
    <div slot="content" class="content">
      <div class="content-trend">
        <div class="content-trend-left">
          <div class="num alarm-total">
            <div class="item">
              <span class="item-name">
                <img src="@/assets/images/monitor/ic-alert.png" alt />
                <span class="total-name">今日报警</span>
              </span>
              <span class="count">{{ CountPolice.day }}</span>
            </div>
          </div>
          <div class="num offline-total">
            <div class="item">
              <span class="item-name">
                <img src="@/assets/images/monitor/ic-month-alarm.png" alt />
                <span class="total-name">本月报警</span>
              </span>
              <span class="scale">
                <span class="count">{{ CountPolice.month }}</span>
              </span>
            </div>
          </div>
          <div class="num unprocessed-total">
            <div class="item">
              <span class="item-name">
                <img src="@/assets/images/monitor/ic-year-alarm.png" alt />
                <span class="total-name">本年报警</span>
              </span>
              <span class="scale">
                <span class="count">{{ CountPolice.year }}</span>
              </span>
            </div>
          </div>
        </div>
        <div class="content-trend-right" style="display: flex">
          <ContentCard v-loading="echartLoading" title="报警走势图" style="width: 60%">
            <echarts slot="content" ref="myLineChart" domId="myLineChart" />
          </ContentCard>
          <ContentCard v-loading="echartLoading" title="报警类型分析" style="width: 40%">
            <echarts slot="content" ref="mypreChart" domId="mypreChart" />
          </ContentCard>
        </div>
      </div>
      <div class="table-content">
        <TablePage
          ref="table"
          v-loading="tableLoading"
          :showPage="true"
          :tableColumn="tableColumn"
          :data="tableData"
          height="calc(100% - 40px)"
          :pageData="pageData"
          @pagination="paginationChange"
          @row-dblclick="getDetails('check', $event)"
          @selection-change="handleSelectionChange"
        />
      </div>
      <el-dialog
        v-dialogDrag
        :modal="false"
        custom-class="model-dialog"
        width="50%"
        append-to-body
        title="批处理"
        :visible.sync="batchHandleDia"
        :close-on-click-modal="false"
        :before-close="closeDialog"
      >
        <div class="dialog-content">
          <el-form ref="formInline" :model="batchForm" label-width="130px">
            <el-form-item label="处置人员：" prop="disposeResult">
              <span>{{ $store.state.user.userInfo.user.staffName }}</span>
            </el-form-item>
            <el-form-item label="处置人员电话：" prop="disposeResult">
              <span>{{ $store.state.user.userInfo.user.phone }}</span>
            </el-form-item>
            <el-form-item label="处置结果：" prop="disposeResult">
              <el-select v-model="batchForm.disposeResult" class="sino_sdcp_input mr15" placeholder="请选择处置结果">
                <el-option v-for="item in chuzhiList" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item v-if="monitorData.projectName === '电梯监测'" label="处置时间：" prop="disposeTime">
              <el-date-picker v-model="batchForm.disposeTime" type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="选择日期时间"> </el-date-picker>
            </el-form-item>
            <br />
            <el-form-item label="处置结果说明：">
              <el-input
                v-model="batchForm.disposeText"
                type="textarea"
                style="width: 540px"
                class="project-textarea"
                placeholder="请输入处置结果说明"
                show-word-limit
                :autosize="{ minRows: 4, maxRows: 4 }"
                maxlength="200"
              ></el-input>
            </el-form-item>
            <br />
            <el-form-item label="相关附件：" prop="file">
              <el-upload
                ref="uploadFile"
                drag
                multiple
                class="mterial_file"
                action="string"
                :file-list="fileEcho"
                :http-request="httpRequest"
                accept=".jpg, .jpeg, .png, .JPG, .JPEG, .gif, .word, .WORD, .Excel, .pdf, .PDF, .doc, .docx, .DOCX, .xlsx, .xls, .zip, .zipx, .tar, .7z, .mp4, .mp3"
                :limit="9"
                :on-exceed="handleExceed"
                :beforeUpload="beforeAvatarUpload"
                :on-remove="handleRemove"
              >
                <i class="el-icon-upload"></i>
                <div class="el-upload__text" style="top: 33px">
                  将文件拖到此处，或
                  <em>点击上传</em>
                </div>
                <div slot="tip" class="el-upload__tip">可上传单个文件，小于50M，支持音频、视频、office、图片、压缩包等类型</div>
              </el-upload>
            </el-form-item>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="saveBatchResult">确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </PageContainer>
</template>
<script lang="jsx">
import { monitorTypeList } from '@/util/dict.js'
import * as echarts from 'echarts'
import { transData } from '@/util'
export default {
  name: 'elecAlarmCenter',
  props: {
    projectCode: {
      type: String,
      default: ''
    },
    requestHttp: {
      type: String,
      default: ''
    },
    entityConvertName: {
      type: String,
      default: '监测项'
    }
  },
  data() {
    return {
      alarmTypeArr: [],
      alarmLevelOptions: [
        // 报警等级
        {
          value: '0',
          label: '通知'
        },
        {
          value: '1',
          label: '一般'
        },
        {
          value: '2',
          label: '紧急'
        },
        {
          value: '3',
          label: '重要'
        }
      ],
      filters: {
        daterange: [],
        surveyName: '',
        disposeResult: '',
        alarmLevelId: '',
        parameterId: '',
        regionCode: ''
      },
      selectRegionCodeList: [],
      riskPropsType: {
        children: 'children',
        label: 'ssmName',
        value: 'id',
        checkStrictly: true,
        multiple: false
      },
      regionCodeList: [], // 所在区域列表
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now() + 1000 * 60 * 60
        }
      },
      CountPolice: {
        day: '',
        month: '',
        year: ''
      }, // 报警统计-日-月-年
      echartLoading: false,
      tableLoading: false, // 表格相关
      tableHeight: '',
      tableData: [],
      pageData: {
        page: 1,
        pageSize: 15,
        total: 0
      },
      inquire: false, // 是否为查询操作
      alarmData: {},
      newInfo: '',
      multipleSelection: [],
      base64: '',
      chuzhiList: [
        { id: 0, name: '未处置' },
        { id: 1, name: '已确认' },
        { id: 2, name: '误报' }
      ],
      tableColumn: [
        {
          type: 'selection',
          align: 'center'
        },
        {
          width: 50,
          prop: '',
          label: '序号',
          formatter: (scope) => {
            return (this.pageData.page - 1) * this.pageData.pageSize + scope.$index + 1
          }
        },
        {
          prop: 'surveyName',
          label: this.entityConvertName + '名称'
        },
        {
          prop: 'parameterName',
          label: '报警类型'
        },
        {
          prop: 'policeLevel',
          label: '报警级别'
        },
        {
          prop: 'policeReason',
          label: '报警描述'
        },
        {
          prop: 'policeNumber',
          label: '报警数值',
          hasJudge: monitorTypeList.find((item) => item.projectCode == this.projectCode).projectName != '电梯监测'
        },
        {
          prop: 'policeOffValue',
          label: '报警数值',
          hasJudge: monitorTypeList.find((item) => item.projectCode == this.projectCode).projectName == '电梯监测'
        },
        {
          prop: 'upperThreshold',
          label: '阈值上限',
          hasJudge: monitorTypeList.find((item) => item.projectCode == this.projectCode).projectName != '电梯监测'
        },
        {
          prop: 'downThreshold',
          label: '阈值下限',
          hasJudge: monitorTypeList.find((item) => item.projectCode == this.projectCode).projectName != '电梯监测'
        },
        {
          prop: 'gridName',
          label: '地理位置'
        },
        {
          prop: 'policeTime',
          label: '最新报警时间'
        },
        {
          prop: 'disposeResultName',
          label: '处置状态',
          render: (h, row) => {
            return <span style={{ color: row.row.disposeResult == '0' ? '#5188fc' : '' }}>{row.row.disposeResultName}</span>
          }
        },
        {
          prop: 'remark',
          label: '简介',
          hasJudge: monitorTypeList.find((item) => item.projectCode == this.projectCode).projectName != '电梯监测'
        },
        {
          width: 140,
          prop: 'operation',
          label: '操作',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span style="color: #54c7c2; cursor: pointer; margin-right: 10px;" onClick={() => this.getDetails('add', row.row)}>
                  处置
                </span>
                <span style="color: #5188fc; cursor: pointer; margin-right: 10px;" onClick={() => this.getDetails('check', row.row)}>
                  详情
                </span>
              </div>
            )
          }
        }
      ],
      loading: false,
      monitorData: monitorTypeList.find((item) => item.projectCode == this.projectCode),
      hasQueryParams: false,
      // 批处理弹框
      batchHandleDia: false,
      batchForm: {
        ids: [],
        disposeResult: '',
        disposeTime: '',
        disposeText: '',
        file: []
      },
      fileEcho: [],
      urlList: []
    }
  },
  mounted() {
    this.initEvent()
  },
  activated() {
    this.initEvent()
  },
  created() {},
  methods: {
    initEvent() {
      this.hasQueryParams = Object.keys(this.$route.query).length
      Object.assign(this.filters, this.$route.query)
      this.getRegionList()
      this.getAlarmTypeList()
      this.getData()
      this.getPoliceCount()
      this.getPoliceCountList()
      this.getReasonStatisticPie()
    },
    getRegionList() {
      // 所在区域
      this.$api.spaceTree().then((res) => {
        if (res.code == '200') {
          this.regionCodeList = transData(res.data, 'id', 'pid', 'children')
        }
      })
    },
    getAlarmTypeList() {
      this.$api.alarmTypeList({ projectCode: this.projectCode }).then((res) => {
        this.alarmTypeArr = res.data
      })
    },
    setWebsocket() {
      setTimeout(() => {
        // 收到消息刷新页面
        this.getData()
      }, 200)
    },
    /**
     * 获取报警数量
     * projectCode 项目code
     */
    getPoliceCount() {
      this.$api.getPoliceCount({ projectCode: this.projectCode }, this.requestHttp).then((res) => {
        if (res.code == 200) {
          this.CountPolice = res.data
        }
      })
    },
    /**
     * 报警统计近30天走势图
     */
    getPoliceCountList() {
      this.echartLoading = true
      let params = {
        projectCode: this.projectCode,
        ...this.filters,
        startTime: this.filters.daterange[0] || '',
        endTime: this.filters.daterange[1] || ''
      }
      this.$api.getTrendStatisticLine(params, this.requestHttp).then(
        (res) => {
          this.echartLoading = false
          this.$nextTick(() => {
            if (res.code == 200) {
              this.$refs.myLineChart.init(this.drawLine(res.data.xAxisData, res.data.seriesData))
            } else {
              this.$refs.myLineChart.init(this.drawLine())
            }
          })
        },
        (err) => {
          this.echartLoading = false
          this.$refs.myLineChart.init(this.drawLine())
        }
      )
    },
    /**
     * 获取按类型统计饼图
     */
    getReasonStatisticPie() {
      this.echartLoading = true
      let params = {
        projectCode: this.projectCode,
        ...this.filters,
        startTime: this.filters.daterange[0] || '',
        endTime: this.filters.daterange[1] || ''
      }
      this.$api
        .getReasonStatisticPie(params, this.requestHttp)
        .then((res) => {
          this.echartLoading = false
          if (res.code == 200) {
            this.$nextTick(() => {
              this.$refs.mypreChart.init(this.drawpie(res.data))
            })
          } else {
            this.$refs.mypreChart.init(this.drawpie())
          }
        })
        .catch(() => {
          this.echartLoading = false
          this.$refs.mypreChart.init(this.drawpie())
        })
    },
    drawLine(xAxisData = [], seriesData = []) {
      let option
      if (xAxisData.length && seriesData.length) {
        option = {
          tooltip: {
            trigger: 'axis'
          },
          grid: {
            left: '2%',
            right: '3%',
            bottom: '10',
            top: '20',
            containLabel: true
          },
          lineStyle: {},
          xAxis: [
            {
              type: 'category',
              data: xAxisData,
              // axisPointer: {
              //   type: 'shadow'
              // },
              axisLabel: {
                show: true,
                rotate: 30,
                // interval:1,
                textStyle: {
                  color: '#909399' // 更改坐标轴文字颜色
                }
              },
              axisLine: {
                lineStyle: {
                  color: '#D8DEE7' // 更改坐标轴颜色
                }
              },
              // 去除x轴上的刻度线
              axisTick: {
                show: false
              }
            }
          ],
          yAxis: [
            {
              type: 'value',
              name: '',
              axisLabel: {
                formatter: '{value}',
                show: true,
                textStyle: {
                  color: '#909399' // 更改坐标轴文字颜色
                }
              },
              // 控制y轴线是否显示
              axisLine: {
                lineStyle: {
                  color: '#D8DEE7' // 更改坐标轴颜色
                }
              },
              // 网格样式
              splitLine: {
                show: true,
                lineStyle: {
                  color: ['#f5f5f5'],
                  width: 1,
                  type: 'dashed'
                }
              },
              // 去除y轴上的刻度线
              axisTick: {
                show: false
              }
            }
          ],
          toolbox: {
            show: true,
            feature: {
              mark: { show: false },
              dataView: { show: false, readOnly: false },
              magicType: {
                show: false,
                type: ['pie', 'funnel']
              },
              restore: { show: false },
              saveAsImage: { show: false }
            }
          },
          series: [
            {
              // symbol: "none", //折线的小圆点
              data: seriesData,
              type: 'line',
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                    {
                      // 颜色渐变函数 前四个参数分别表示四个位置依次为左、下、右、上
                      offset: 0,
                      color: '#F76B1C'
                    },
                    {
                      offset: 1,
                      color: '#FAD961'
                    }
                  ])
                }
              }
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      return option
    },
    drawpie(data = []) {
      let lable = []
      let xAxisData = []
      let seriesData = []
      let option
      data.map((item) => {
        lable.push((item.name && item.name.length > 8 ? item.name.slice(0, 8) + '...' : item.name) + ' ' + item.value + '(件)' + ' ' + item.percent)
        xAxisData.push(item.value)
        seriesData.push(item.name)
      })
      if (xAxisData.length && seriesData.length) {
        option = {
          tooltip: {
            trigger: 'item'
          },
          animation: false, // 该属性必须加，不然转换后的base64达不到图表的效果
          legend: {
            orient: 'vertical',
            left: 'right',
            top: 'center',
            icon: 'circle',
            align: 'left',
            pageIconColor: '#5188fc', // 激活的分页按钮颜色
            pageIconInactiveColor: '#c2c6ce', // 没激活的分页按钮颜色
            type: 'scroll',
            formatter: function (name) {
              var index = 0
              var clientlabels = seriesData
              var clientcounts = xAxisData
              clientlabels.forEach(function (value, i) {
                if (value == name) {
                  index = i
                }
              })
              return lable[index]
            },
            tooltip: {
              show: true
            }
          },
          color: ['#37BBF0', '#FF796D', '#F787D3', '#21CAB5', '#FCD442', '#B27BEF', '#5E7BE1', '#F5666D'],
          series: [
            {
              name: '',
              type: 'pie',
              radius: '70%',
              center: ['30%', '50%'],
              data: data,
              label: {
                show: false,
                position: 'center'
              },
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      return option
    },
    // 查询
    search() {
      this.pageData.page = 1
      this.getPoliceCount()
      this.getPoliceCountList()
      this.getReasonStatisticPie()
      this.getData()
    },
    // 查询重置
    reset() {
      this.pageData.page = 1
      this.selectRegionCodeList = []
      this.filters.daterange = []
      this.filters.surveyName = ''
      this.filters.disposeResult = ''
      this.filters.alarmLevelId = ''
      this.filters.parameterId = ''
      this.filters.regionCode = ''
      this.getPoliceCount()
      this.getPoliceCountList()
      this.getReasonStatisticPie()
      this.getData()
    },
    // 查看详情
    getDetails(type, row) {
      this.$router.push({
        path: '/' + this.monitorData.policeDetailsPath,
        query: {
          type: type,
          id: row.id,
          projectCode: this.projectCode,
          entityConvertName: this.entityConvertName
        }
      })
    },
    /**
     * 批处理
     */
    batching() {
      this.$confirm('该操作将会对所有选中的信息进行确认，是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        for (let i = 0; i < this.multipleSelection.length; i++) {
          const el = this.multipleSelection[i]
          if (el.disposeResult !== 0) {
            this.$message.warning('只能处理未处置状态的数据')
            return
          }
        }
        this.batchForm.ids = []
        this.multipleSelection.map((item) => {
          this.batchForm.ids.push(item.id)
        })
        this.batchHandleDia = true
      })
    },
    httpRequest(item) {
      this.batchForm.file.push(item.file)
    },
    handleExceed() {
      this.$message.error('最多上传九份文件')
    },
    beforeAvatarUpload(file) {
      const isLt5M = file.size / 1024 / 1024 < 5
      if (!isLt5M) {
        this.$message.error('上传文件大小不能超过 5MB!')
        return false
      }
      if (file.name.indexOf(',') != -1) {
        this.$message.error('非法的文件名')
        return false
      }
    },
    handleRemove(file, fileList) {
      this.batchForm.file = []
      fileList.forEach((item) => {
        this.batchForm.file.push(item.raw)
      })
    },
    closeDialog() {
      this.batchHandleDia = false
    },
    saveBatchResult() {
      let params = {
        ...this.batchForm,
        disposePersonName: this.$store.state.user.userInfo.user.staffName,
        disposePersonPhone: this.$store.state.user.userInfo.user.personPhone,
        attachmentUrl: this.$store.state.user.userInfo.user.attachmentUrl,
        ids: this.batchForm.ids.join(',')
      }
      this.$api.disposePoliceBatch(params, this.requestHttp).then((res) => {
        if (res.code == 200) {
          this.$confirm('报警消息已确认！', '批处理', {
            confirmButtonText: '确定',
            cancelButtonText: '关闭',
            type: 'warning'
          }).then(() => {})
          this.getData()
        }
      })
      this.batchHandleDia = false
    },
    // --------------------------------表格相关
    // 勾选
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    /**
     * 按时间查询table数据
     * @param
     */
    getData() {
      this.tableLoading = true
      this.filters.regionCode = this.selectRegionCodeList[this.selectRegionCodeList.length - 1]
      this.$api
        .getPoliceList(
          {
            projectCode: this.projectCode,
            pageNo: this.pageData.page,
            pageSize: this.pageData.pageSize,
            startTime: this.filters.daterange[0] || '',
            endTime: this.filters.daterange[1] || '',
            ...this.filters
            // parameterId: this.hasQueryParams ? this.$route.query.parameterId : ''
          },
          this.requestHttp
        )
        .then((res) => {
          this.tableLoading = false
          if (res.code == 200) {
            this.tableData = res.data.list
            this.pageData.total = res.data.count
          }
        })
    },
    /**
     * 导出
     */
    downLoad() {
      this.loading = true
      let ids = []
      this.multipleSelection.forEach((item) => {
        ids.push(item.id)
      })
      this.$api
        .exportExcel(
          {
            projectCode: this.projectCode,
            startTime: this.filters.daterange[0] ? this.filters.daterange[0] : '',
            endTime: this.filters.daterange[1] ? this.filters.daterange[1] : '',
            ...this.filters,
            ids: ids.join(',')
          },
          this.requestHttp
        )
        .then((res) => {
          const blob = new Blob([res], { type: 'application/vnd.ms-excel;charset=UTF-8' })
          const url = window.URL.createObjectURL(blob)
          const link = document.createElement('a')
          link.href = url
          link.download = 'example.xlsx'
          link.click()
          window.URL.revokeObjectURL(url)
          this.loading = false
          if (res.status == 200) {
          }
        })
    },
    paginationChange(pagination) {
      this.pageData.pageSize = pagination.pageSize
      this.pageData.page = pagination.page
      this.getData()
    }
  }
}
</script>
<style lang="scss" scoped>
.elecAlarmCenter {
  .control-btn-header {
    .header-back {
      padding: 0 10px;
      height: 40px;
      line-height: 40px;
      font-size: 14px;
      font-weight: 500;
      color: #121f3e;
      font-family: 'PingFang SC-Medium', 'PingFang SC';
      border-bottom: 1px solid #dcdfe6;
      span {
        padding-left: 6px;
        cursor: pointer;
      }
      i {
        cursor: pointer;
      }
    }
    .header-search {
      padding: 6px 6px 16px 16px;
      & > div {
        margin-right: 10px;
        margin-top: 10px;
      }
    }
  }
  .content {
    height: 100%;
    display: flex;
    flex-direction: column;
    .table-content {
      flex: 1;
      padding: 10px;
      background: #fff;
      margin-top: 16px;
      height: calc(100% - 272px);
    }
  }
  .content-trend {
    margin-top: 16px;
    background-color: #fff;
    padding: 6px 16px;
    display: flex;
    .content-trend-left {
      .num {
        width: 350px;
        padding: 10px 0;
        .item {
          height: 56px;
          background: #f5f5fa;
          border-radius: 10px;
          display: flex;
          align-items: center;
          padding: 0 20px 0 30px;
          justify-content: space-between;
          .item-name {
            display: flex;
            align-items: center;
            img {
              padding-right: 20px;
            }
          }
          .offline {
            color: #fc945e;
          }
          .unprocessed {
            color: #5188fc;
          }
          .slash {
            padding: 0 3px;
          }
          .count,
          .scale {
            color: #353535;
            font-size: 20px;
          }
        }
      }
    }
    .content-trend-right {
      flex: 1;
      padding: 10px 0 0 16px;
      ::v-deep .box-card {
        padding: 0;
      }
    }
  }
}
.dialog-content {
  background: #fff;
  padding: 16px;
  height: 100%;
  .form-data {
    height: 100%;
  }
}
</style>
