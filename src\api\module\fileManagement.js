/*
 * @Description:
 */
/*
 * @Description:
 */
import { getHttp, post_json, post_formData, getRequest, postRequest } from '../http.js'
export default {
  // ---------------------------------字典配置--------------------------------------------------
  /** 分页获取字典 */
  getDictConfigByPage: postRequest('archive/dictConfig/queryDictConfigByPage', __PATH.SPACE_API),
  /** 删除字典 */
  deleteDictData: getRequest('archive/dictConfig/delete', __PATH.SPACE_API),
  /** 新增字典 */
  insertDictConfigData: postRequest('archive/dictConfig/insert', __PATH.SPACE_API),
  /** 编辑字典 */
  updateDictConfigData: postRequest('archive/dictConfig/update', __PATH.SPACE_API),
  /** 字典详情 */
  getDictDataById: getRequest('archive/dictConfig/view', __PATH.SPACE_API),
  /** 查询全部字典 */
  getDictConfigData: postRequest('archive/dictConfig/queryDictConfigAll', __PATH.SPACE_API),
  // ---------------------------------合同统计--------------------------------------------------
  getArchiveTypeStatistics: getRequest('web/archiveB/getArchiveTypeStatistics', __PATH.SPACE_API),
  /** 总金额 */
  getAmountStatistics: getRequest('web/archiveB/getAmountStatistics', __PATH.SPACE_API),
  /** 部门排行 */
  getArchiveDepStatistics: getRequest('web/archiveB/getArchiveDepStatistics', __PATH.SPACE_API),
  /** 部门更多排行 */
  getArchiveDepStatisticsMorePage: getRequest('web/archiveB/getArchiveDepStatisticsMorePage', __PATH.SPACE_API),
  // ---------------------------------文件管理--------------------------------------------------
  selectFolderTree: getHttp('web/archiveFolderB/selectFolderTree', __PATH.SPACE_API, false),
  insertFolder: post_json('web/archiveFolderB/insert', __PATH.SPACE_API, {}, false),
  updateFolder: post_json('web/archiveFolderB/update', __PATH.SPACE_API, {}, false),
  folderDetail: getHttp('web/archiveFolderB/detail', __PATH.SPACE_API, false),
  queryByPage: getHttp('web/archiveB/queryByPage', __PATH.SPACE_API, false),
  insertArchiveB: post_json('web/archiveB/insert', __PATH.SPACE_API, {}, false),
  updateDeleteArchiveB: post_json('web/archiveB/updateDelete', __PATH.SPACE_API, {}, false), // 删除数据(移到回收站)
  recoverFile: post_json('web/archiveB/recover', __PATH.SPACE_API, {}, false), // 从回收站恢复数据
  updateRecoverNewFolder: post_json('web/archiveB/updateRecoverNewFolder', __PATH.SPACE_API, {}, false), // 从回收站恢复数据（重新选择文件夹）
  deleteFile: post_json('web/archiveB/delete', __PATH.SPACE_API, {}, false), // 彻底删除文档
  insertShare: post_json('web/archiveShareB/insert', __PATH.SPACE_API, {}, false), // 添加共享数据
  shareListByPage: getHttp('web/archiveShareB/shareListByPage', __PATH.SPACE_API, false), // 共享文档分页列表
  shareManagerListByPage: getHttp('web/archiveShareB/shareManagerListByPage', __PATH.SPACE_API, false), // 共享管理列表
  cancelShare: post_json('web/archiveShareB/cancelShare', __PATH.SPACE_API, {}, false), // 取消共享
  getById: (params) => getHttp(`web/archiveB/getById/${params.id}`, __PATH.SPACE_API, false)(), // 通过主键查询详情、基本信息
  updateFile: post_json('web/archiveB/update', __PATH.SPACE_API, {}, false), // 根据ID更新数据
  recordQueryByPage: getHttp('web/archiveLogB/queryByPage', __PATH.SPACE_API, false), // 操作记录 浏览记录 分页查询
  queryStastics: getHttp('web/archiveLogB/queryStastics', __PATH.SPACE_API, false), // 访问统计
  deleteFolder: getHttp('web/archiveFolderB/delete', __PATH.SPACE_API, false), // 访问统计
  shareListByArchiveId: getHttp('web/archiveShareB/shareListByArchiveId', __PATH.SPACE_API, false), // 根据共享id查询共享文档下的附件列表
  queryByArchiveId: getHttp('web/archiveFileD/queryByArchiveId', __PATH.SPACE_API, false), // 根据文档ID查询附件列表
  downloadArchiveFile: post_json('web/archiveFileD/downloadFile', __PATH.SPACE_API, { responseType: 'blob' }, false), // 下载文件
  getArchiveStatistics: getHttp('web/archiveB/getArchiveStatistics', __PATH.SPACE_API, false), // 数据统计-合同总数&状态统计
  insertContract: post_json('web/archiveB/insertContract', __PATH.SPACE_API, {}, false), // 添加合同数据
  updateContract: post_json('web/archiveB/updateContract', __PATH.SPACE_API, {}, false), // 根据ID更新合同数据
  queryAllContractPage: getHttp('web/archiveB/queryAllContractPage', __PATH.SPACE_API, false), // 根据ID更新合同数据
  archiveShareUpdate: post_json('web/archiveShareB/update', __PATH.SPACE_API, {}, false), // 根据ID更新借阅数据
  setExpireDay: post_json('web/archiveB/setExpireDay', __PATH.SPACE_API, {}, false), // 过期监控配置
  queryDictConfigAll: post_json('archive/dictConfig/queryDictConfigAll', __PATH.SPACE_API, {}, false), // 查询全部字典

  // ------------------------------------------- 班车管理 ------------------------------------------------------------------------
  roadmapList: postRequest('busLine/queryPage', __PATH.SPACE_API), // 路线列表
  roadmapSave: postRequest('busLine/insert', __PATH.SPACE_API), // 路线新增
  roadmapUpdate: postRequest('busLine/update', __PATH.SPACE_API), // 路线更新
  roadmapDetail: getRequest('busLine/queryById', __PATH.SPACE_API), // 路线详情
  roadmapDelete: postRequest('busLine/deleteBatchByIds', __PATH.SPACE_API), // 路线删除
  roadmapReplyList: postRequest('busLine/findList', __PATH.SPACE_API), // 路线列表不带分页
  // -------------------------------------------- 班车记录 -----------------------------------------------------------------------
  recordList: postRequest('busTravelRecords/queryPage', __PATH.SPACE_API), // 班车记录列表
  recordSave: postRequest('busTravelRecords/insert', __PATH.SPACE_API), //班车新增
  recordUpdate: postRequest('busTravelRecords/update', __PATH.SPACE_API), //班车更新
  recordDetail: getRequest('busTravelRecords/queryById', __PATH.SPACE_API), //班车详情
  recordDelete: postRequest('busTravelRecords/deleteBatchByIds', __PATH.SPACE_API), //班车删除
  getFindDictListByCodeList: postRequest('commonDictData/findDictListByCode', __PATH.SPACE_API), // 获取字典
  getRecordSum: postRequest('busTravelRecords/statistics', __PATH.SPACE_API), // 班车记录统计
  // -------------------------------------------- 车次配置 --------------------------------------------------------------------------
  trainConfigurationList: postRequest('busTrainNumber/queryPage', __PATH.SPACE_API), // 车次列表
  trainConfigurationSave: postRequest('busTrainNumber/insert', __PATH.SPACE_API), // 车次新增
  trainConfigurationUpdate: postRequest('busTrainNumber/update', __PATH.SPACE_API), // 车次更新
  trainConfigurationDetail: getRequest('busTrainNumber/queryById', __PATH.SPACE_API), // 车次详情
  trainConfigurationDelete: postRequest('busTrainNumber/deleteBatchByIds', __PATH.SPACE_API), // 车次删除
  getCustomList: postRequest('dynamicFieldConfig/findDynamicFieldList', __PATH.SPACE_API), // 动态获取表头列表
  customSave: postRequest('dynamicFieldConfig/saveBatch', __PATH.SPACE_API), // 动态获取表头保存
  batchState: postRequest('busLine/updateBatchByIds', __PATH.SPACE_API), // 批量修改状态
}
