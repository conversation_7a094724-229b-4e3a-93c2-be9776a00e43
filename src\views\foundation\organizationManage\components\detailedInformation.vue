<template>
  <PageContainer :footer="true">
    <div v-if="$route.query.type == '1'" slot="content" class="whole">
      <div style="height: 97%;">
        <el-table v-loading="loading" :data="tableData" height="calc(100% - 10px)" stripe border>
          <el-table-column type="index" label="序号" width="50" align="center">
            <template slot-scope="scope">
              <span>{{ (pagination.current - 1) * pagination.size + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column v-for="item in tableHeaders" :key="item.porp" :prop="item.porp" :label="item.label" :width="item.width" show-overflow-tooltip align="center">
            <template slot-scope="scope">
              <span v-if="item.porp == 'deptName'" class="color_blue" @click="ViewFn1(scope.row)">
                {{ scope.row.deptName }}
              </span>
              <span v-else>&nbsp;{{ scope.row[item.porp] }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        :current-page="pagination.current"
        :page-sizes="[15, 30, 50, 100]"
        :page-size="pagination.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
    <div v-else slot="content" class="whole">
      <div style="height: 97%;">
        <el-table v-loading="loading" :data="tableData" height="calc(100% - 10px)" stripe border>
          <el-table-column type="index" label="序号" width="50" align="center">
            <template slot-scope="scope">
              <span>{{ (pagination.current - 1) * pagination.size + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column v-for="item in tableHeader" :key="item.porp" :prop="item.porp" :label="item.label" :width="item.width" show-overflow-tooltip align="center">
            <template slot-scope="scope">
              <span v-if="item.porp == 'sex'">
                {{ scope.row.sex == 1 ? '男' : '女' }}
              </span>
              <span v-else-if="item.porp == 'stationStatus'">
                {{ scope.row.stationStatus == 0 ? '在职' : '离职' }}
              </span>
              <span v-else-if="item.porp == 'staffName'" class="color_blue" @click="ViewFn(scope.row)">
                {{ scope.row.staffName }}
              </span>
              <span v-else-if="item.porp == 'activationFlag'">
                {{ scope.row.activationFlag == 0 ? '已激活' : '未激活' }}
              </span>
              <span v-else>{{ scope.row[item.porp] }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        :current-page="pagination.current"
        :page-sizes="[15, 30, 50, 100]"
        :page-size="pagination.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange2"
        @current-change="handleCurrentChange2"
      ></el-pagination>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">取消</el-button>
    </div>
  </PageContainer>
</template>
<script>
export default {
  name: 'detailedInformation',
  async beforeRouteLeave(to, from, next) {
    if (!['unitManage'].includes(to.name)) {
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      loading: false,
      tableData: [],
      filters: {
        unitId: '',
        parentDeptName: '',
        deptPidId: '',
        deptName: '',
        principalName: '',
        pmId: ''
      },
      pagination: {
        current: 1,
        size: 15
      },
      total: 0,
      nation: null,
      tableHeaders: [
        {
          porp: 'deptName',
          label: '部门名称'
        },
        {
          porp: 'unitName',
          label: '归属单位'
        },
        {
          porp: 'deptPhone',
          label: '部门电话'
        },
        {
          porp: 'parentDeptName',
          label: '上级部门'
        },
        {
          porp: 'officeCode',
          label: '组织机构编码'
        },
        {
          porp: 'financeCode',
          label: '财务核算码'
        },
        {
          porp: 'principalName',
          label: '部门负责人'
        },
        {
          porp: 'principalPhone',
          label: '负责人电话'
        },
        {
          porp: 'remark',
          label: '备注'
        }
      ],
      tableHeader: [
        {
          porp: 'staffNumber',
          label: '职工工号'
        },
        {
          porp: 'staffName',
          label: '人员姓名'
        },
        {
          porp: 'sex',
          label: '性别'
        },
        {
          porp: 'mobile',
          label: '手机号码'
        },
        {
          porp: 'phone',
          label: '办公电话'
        },
        // {
        //   porp: 'entryData',
        //   label: '入职天数'
        // },
        {
          porp: 'unitComName',
          label: '归属单位'
        },
        {
          porp: 'officeName',
          label: '所属部门'
        },
        {
          porp: 'postName',
          label: '岗位'
        },
        {
          porp: 'jobName',
          label: '职务'
        },
        {
          porp: 'stationStatus',
          label: '在职状态'
        },
        {
          porp: 'activationFlag',
          label: '激活状态'
        }
      ]
    }
  },
  activated() {
    this.initEvent()
  },
  created() {
    if (!this.$store.state.keepAlive.list.includes('unitManage')) {
      this.initEvent()
    }
  },
  methods: {
    initEvent() {
      if (this.$route.query.type == '1') {
        this.departListFn()
      } else {
        this.staffListByPageFn()
      }
    },
    //  获取部门列表
    departListFn() {
      this.loading = true
      this.filters.unitId = this.$route.query.row.id
      this.$api
        .departList({
          ...this.filters,
          ...this.pagination
        })
        .then((res) => {
          // this.treeLoading = false;
          if (res.code == 200) {
            this.tableData = res.data.records
            this.pagination.current = res.data.current
            this.pagination.size = res.data.size
            this.total = res.data.total
            this.loading = false
          }
        })
    },
    handleSizeChange(val) {
      this.pagination.size = val
      this.departListFn()
    },
    handleCurrentChange(val) {
      this.pagination.current = val
      this.departListFn()
    },
    //  获取人员信息列表
    staffListByPageFn() {
      this.loading = true
      this.filters.pmId = this.$route.query.row.id
      this.$api
        .staffListByPage({
          notInIds: this.notInIds,
          ...this.filters,
          ...this.pagination,
          nature: this.nature ? 1 : ''
        })
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.records
            this.pagination.current = res.data.current
            this.pagination.size = res.data.size
            this.total = res.data.total
            this.loading = false
          }
        })
    },
    handleSizeChange2(val) {
      this.pagination.size = val
      this.staffListByPageFn()
    },
    handleCurrentChange2(val) {
      this.pagination.current = val
      this.staffListByPageFn()
    },
    ViewFn(row) {
      this.$router.push({
        path: '/staffMess',
        query: { type: 'View', id: row.id }
      })
    },
    ViewFn1(row) {
      this.$router.push({
        path: '/DepetMess',
        query: { type: 'View', id: row.id }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.whole {
  width: 100%;
  height: 100%;
  padding: 15px;
  background: #fff;
}
.color_blue {
  color: #5188fc;
  cursor: pointer;
}
</style>
