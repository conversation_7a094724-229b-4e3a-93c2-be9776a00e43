<template>
  <PageContainer>
    <div slot="header">
      <div class="search-header">
        <el-button type="primary" :disabled="multipleSelection.length != 1" @click="redact">编辑</el-button>
      </div>
    </div>
    <div slot="content" style="height: 100%; margin-top: 16px; background-color: #fff;">
      <div class="table-content">
        <el-table v-loading="tableLoading" stripe :data="tableDate" :border="true" :cell-style="{ padding: '8px' }" :height="tableHeight" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" fixed="left" align="center"></el-table-column>
          <el-table-column type="index" label="序号" width="65">
            <template slot-scope="scope">
              <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="rewardsPunishmentsName" show-overflow-tooltip label="名称"></el-table-column>
          <el-table-column prop="startTime" show-overflow-tooltip label="开始时间"></el-table-column>
          <el-table-column prop="endTime" show-overflow-tooltip label="结束时间"></el-table-column>
          <el-table-column prop="rewardsPunishmentsStatus" show-overflow-tooltip label="奖惩">
            <template slot-scope="scope">
              <span v-if="scope.row.rewardsPunishmentsStatus == 1">奖励</span>
              <span v-if="scope.row.rewardsPunishmentsStatus == 2">惩罚</span>
            </template>
          </el-table-column>
          <el-table-column prop="money" show-overflow-tooltip label="金额"></el-table-column>
        </el-table>
      </div>
      <el-dialog v-dialogDrag title="奖惩配置" custom-class="model-dialog" :visible.sync="dialogVisible" :before-close="closeDialog">
        <div class="content" style="padding: 10px; width: 100%; background-color: #fff;">
          <el-form ref="formInline" :model="formInline" :inline="true" class="advanced-search-form" label-position="right" label-width="90px" :rules="rules">
            <el-form-item label="名称：" prop="name">
              <template>
                <span>{{ formInline.name }}</span>
              </template>
            </el-form-item>
            <br />
            <el-form-item label="奖惩：" prop="status">
              <el-radio v-model="formInline.status" label="1">奖励</el-radio>
              <el-radio v-model="formInline.status" label="2">惩罚</el-radio>
            </el-form-item>
            <br />
            <el-form-item label="金额(元)：" prop="money">
              <el-input v-model="formInline.money" oninput="value=value.replace(/[^\d.]/g,'')" @input="handleInputMin"></el-input>
            </el-form-item>
            <br />
            <el-form-item label="生效时间：" prop="time">
              <el-date-picker
                v-model="formInline.startTime"
                :editable="false"
                class="sino_sdcp_input mr15 filters-date-picker"
                type="datetime"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm"
                placeholder="开始时间"
                :picker-options="pickerOptions1"
              ></el-date-picker>
              <span style="margin-right: 10px;">至</span>
              <el-date-picker
                v-model="formInline.endTime"
                :editable="false"
                class="sino_sdcp_input mr15 filters-date-picker"
                type="datetime"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm"
                placeholder="结束时间"
                :picker-options="pickerOptions2"
              ></el-date-picker>
            </el-form-item>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="submit">提 交</el-button>
        </span>
      </el-dialog>
    </div>
  </PageContainer>
</template>

<script>
export default {
  name: 'incentiveManagement',
  data() {
    return {
      tableDate: [],
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      rules: {
        status: [{ required: true, message: '请选择奖惩方式', trigger: 'change' }],
        money: [{ required: true, message: '请输入金额', trigger: 'change' }]
      },
      multipleSelection: [],
      tableLoading: false,
      dialogVisible: false,
      formInline: {
        status: '',
        money: 0,
        name: '',
        dealTimelist: [],
        startTime: '',
        endTime: '',
        penaltiesId: '' // id
      },
      pickerOptions1: {
        disabledDate: (time) => {
          let delay = this.formInline.endTime
          if (delay) {
            return time.getTime() > new Date(delay).getTime() || time.getTime() < Date.now() - 8.64e7
          }
          return time.getTime() < Date.now() - 8.64e7
        }
      },
      pickerOptions2: {
        disabledDate: (time) => {
          let delay = this.formInline.startTime
          if (delay) {
            return time.getTime() < new Date(delay).getTime()
          }
        }
      }
    }
  },
  computed: {
    tableHeight() {
      return document.body.clientHeight - 200
    }
  },
  mounted() {
    this.getTableDate()
  },
  methods: {
    getTableDate() {
      this.tableLoading = true
      this.$api.ipsmPenaltiesList({}).then((res) => {
        if (res.code == 200) {
          this.tableDate = res.data
        } else {
          this.$message.error(res.message)
        }
      })
      this.tableLoading = false
    },
    redact() {
      this.formInline.name = this.multipleSelection[0].rewardsPunishmentsName
      this.formInline.status = this.multipleSelection[0].rewardsPunishmentsStatus.toString()
      this.formInline.money = this.multipleSelection[0].money
      this.formInline.penaltiesId = this.multipleSelection[0].id
      this.formInline.startTime = this.multipleSelection[0].startTime
      this.formInline.endTime = this.multipleSelection[0].endTime
      this.dialogVisible = true
    },
    submit() {
      if (this.formInline.endTime && !this.formInline.startTime) {
        return this.$message.error('请选择开始时间')
      }
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          this.$confirm('修改生效时间后，奖惩规则将按新规则在生效周期内执行，之前规则生成信息保持不变！请及时关注详细奖惩信息！', '提醒', { type: 'warning' }).then(
            (res) => {
              this.dialogVisible = false
              let data = {
                ...this.formInline
              }
              this.$api.ipsmUpdatePenalties(data).then(res => {
                if (res.code == 200) {
                  this.$message.success(res.message)
                  this.getTableDate()
                } else {
                  this.$message.error(res.message)
                }
              })
            }
          )
        }
      })
    },
    closeDialog() {
      this.dialogVisible = false
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleInputMin(value) {
      if (value != '') {
        if (value.indexOf('.') > -1) {
          this.formInline.money = value.slice(0, value.indexOf('.') + 3)
        } else {
          this.formInline.jien = value
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.search-header {
  padding: 10px 16px;

  .search-from {
    padding-bottom: 12px;

    & > div {
      margin-top: 12px;
      margin-right: 10px;
    }
  }
}

.table-content {
  padding: 16px;
  height: calc(100% - 20px);
}
</style>
