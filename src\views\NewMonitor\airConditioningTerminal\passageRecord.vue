<template>
    <PageContainer>
        <div slot="header">
            <div class="searchForm">
                <div class="search-box">
                    <el-date-picker v-model="dataRange" type="daterange" unlink-panels range-separator="至"
                        start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd" class="ml-16">
                    </el-date-picker>
                    <div class="ml-16">
                        <el-button type="primary" plain @click="reset">重置</el-button>
                        <el-button type="primary" @click="search">查询</el-button>
                    </div>
                </div>
            </div>
        </div>
        <div slot="content" ref="contentRef" class="table-box">
            <div class="tableContainer">
                <TablePage ref="table" v-loading="tableLoading" :showPage="true" :tableColumn="tableColumn" height="420"
                    :data="tableData" :pageData="pageData" @pagination="paginationChange" :pageProps="pageProps" />
            </div>
        </div>
    </PageContainer>
</template>
<script lang="jsx">
import moment from 'moment'
moment.locale('zh-cn')
export default {
    name: 'outcallRecord',
    props: {
        factoryCode: {
            type: String,
            default: ''
        },
    },
    data() {
        return {
            tableLoading: false,
            searchFrom: {
                sTime: '',
                eTime: '',
                dateType: "day",
            }, // 搜索条件
            dataRange: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')], // 时间范围
            tableColumn: [
                {
                    prop: '',
                    label: '序号',
                    formatter: (scope) => {
                        return (this.pageData.pageNo - 1) * this.pageData.pageSize + scope.$index + 1
                    },
                    width: 60
                },
                {
                    prop: 'assetsName',
                    label: '设备名称'
                },
                {
                    prop: 'peopleSum',
                    label: '通行人数'
                },
                {
                    prop: 'dateTime',
                    label: '通行时间',
                },
            ],
            tableData: [],
            pageData: {
                pageNo: 1,
                pageSize: 15,
                total: 0
            },
            pageProps: {
                page: 'pageNo',
                pageSize: 'pageSize',
                total: 'total'
            },
        }
    },
    mounted() {
        this.getApplicationList()
    },
    methods: {
        // 获取通行记录列表
        getApplicationList() {
            let param = {
                monitorDeviceId: this.$route.query.id,
                sTime: this.dataRange[0],
                eTime: this.dataRange[1],
                pageSize: this.pageData.pageSize,
                pageNo: this.pageData.pageNo
            }
            this.tableLoading = true
            this.$api
                .getAdsAreaPeopleTotal(param)
                .then((res) => {
                    if (res.code == 200) {
                        this.tableData = res.result.records
                        this.pageData.total = res.result.total
                    }
                    this.tableLoading = false
                })
                .catch(() => {
                    this.tableLoading = false
                })
        },
        // 分页
        paginationChange(pagination) {
            Object.assign(this.pageData, pagination)
            this.getApplicationList()
        },
        // 重置查询
        reset() {
            Object.assign(this.searchFrom, {
                sTime: '',
                eTime: '',
                dateType: "day",
            })
            this.dataRange = [] // 时间范围
            this.getApplicationList()
        },
        // 查询
        search() {
            this.searchFrom.sTime = this.dataRange[0]
            this.searchFrom.eTime = this.dataRange[1]
            this.getApplicationList()
        },
    }
}
</script>
<style lang="scss" scoped>
.searchForm {
    display: flex;
    height: 50px;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin-left: -28px;

    .el-input {
        width: 200px;
        margin-left: 16px;
    }

    >div {
        margin-right: 20px;
    }
}

.search-box {
    display: flex;
    align-items: center;
}

.container-content>div {
    height: 100%;
    margin-top: 16px;
}

.el-table {
    margin-bottom: 12px;
}

::v-deep .el-table__row:hover {
    cursor: pointer;
}

.table-box {
    background-color: #fff;
    height: 100%;

    .tableContainer {
        height: 100%;

        .el-table {
            height: calc(100% - 96px) !important;
        }
    }
}

.diaContent {
    width: 100%;
    max-height: 550px !important;
    overflow: auto;
    background-color: #fff !important;

    .el-input,
    .el-select,
    .el-cascader {
        width: 300px;
    }
}

.form-item {
    display: inline-block;
    margin-right: 20px;
}

.inputWidth {
    width: 820px;
}

.ml-16 {
    margin-left: 16px;
}

.dialog .el-dialog {
    width: 60% !important;
}
</style>