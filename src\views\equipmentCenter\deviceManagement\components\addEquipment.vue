<template>
  <el-dialog title="选择上级设备" :visible.sync="epuipmentVisible" width="55%" :before-close="closePinot"
    custom-class="model-dialog" :close-on-click-modal="false" append-to-body>
    <div class="outermost">
      <div v-loading="loading" class="content">
        <div class="topTools">
          <el-input v-model.trim="assetName" placeholder="请输入设备名称" style="width: 200px; margin-right: 10px"></el-input>
          <el-input v-model.trim="assetCode" placeholder="请输入设备编码" style="width: 200px; margin-right: 10px"></el-input>
          <el-cascader v-model="systemCategoryCodes" :options="childrenDeviceList" :props="childrenDevicPropsType"
            placeholder="请选择专业类型" :show-all-levels="false" style="width: 200px; margin-right: 10px"
            @change="organization"></el-cascader>
          <el-button type="primary" plain @click="reset">重 置</el-button>
          <el-button type="primary" @click="searchList">查 询</el-button>
        </div>
        <el-table ref="personTable" v-loading="tableLoading" :data="tableData" border :height="tableHeight" stripe
          style="width: 100%" :row-key="getRowKeys" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" :reserve-selection="true"></el-table-column>
          <el-table-column type="index" label="序号" width="75">
            <template slot-scope="scope">
              <span>{{ (currentPage - 1) * pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="assetName" show-overflow-tooltip label="设备名称"></el-table-column>
          <el-table-column prop="assetCode" show-overflow-tooltip label="设备编码"></el-table-column>
          <el-table-column prop="assetCategoryName" align="center" show-overflow-tooltip label="专业类别"></el-table-column>
          <el-table-column prop="systemCategoryName" show-overflow-tooltip label="系统类别"></el-table-column>
          <el-table-column prop="assetModel" show-overflow-tooltip label="模型编码"></el-table-column>
          <el-table-column prop="regionReverseName" show-overflow-tooltip label="所在区域"></el-table-column>
        </el-table>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <div class="footer">
        <div class="footer_left">
          <el-pagination :current-page="currentPage" :page-sizes="[10, 20, 30, 40]" :pager-count="5"
            :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total"
            @size-change="handleSizeChange" @current-change="handleCurrentChange"></el-pagination>
        </div>
        <div class="footer_right">
          <el-button type="primary" plain @click="closePinot">取 消</el-button>
          <el-button type="primary" :disabled="addDis" @click="editSubmit">确认</el-button>
        </div>
      </div>
    </span>
  </el-dialog>
</template>
<script>
import { transData } from '@/util'
import store from '@/store/index'
export default {
  props: {
    epuipmentVisible: {
      type: Boolean,
      default: false,
    },
    // stateOpty: {
    //   type: String,
    //   default: '',
    // },
    // responsibleUserList: {
    //   type: Array,
    //   default: '',
    // },
    // safetyUserList: {
    //   type: Array,
    //   default: '',
    // },
    // minorDetail: {
    //   type: Array,
    //   default: '',
    // },
    tableDate: {},
  },
  data() {
    return {
      assetName: "", // 设备名称
      assetCode: "", // 设备编码
      systemCategoryCodes: null, // 专业类别
      total: 0,
      currentPage: 1,
      treeDataTop: "",
      pageSize: 15,
      tableLoading: false,
      multipleSelection: [],
      childrenDeviceList: [],  // 专业类别
      childrenDevicPropsType: {
        children: 'children',
        label: 'baseName',
        value: 'id',
        checkStrictly: true,
        multiple: false
      },
      tableData: [], // table列表
      loading: false,
      addDis: false,
      wfl: '-1',
    };
  },
  computed: {
    tableHeight() {
      return document.body.clientHeight - 525;
    },
  },
  mounted() {
    this.search();
    // 绑定enter事件
    this.enterKeyup();
    this.getCategoryData()
  },
  destroyed() {
    // 销毁enter事件
    this.enterKeyupDestroyed();
  },
  methods: {
    // 销毁键盘enter事件
    enterKeyupDestroyed() {
      document.removeEventListener("keyup", this.keyupHandle);
    },
    // 绑定键盘enter事件
    enterKeyup() {
      document.addEventListener("keyup", this.keyupHandle);
    },
    // 键盘搜索
    keyupHandle(e) {
      if (e.keyCode === 13) {
        this.searchList()
      }
    },
    // 选择专业类别
    organization(val) {
      console.log(val,'SDAS');
      
      this.professionalCategoryCode = ''
      this.systemCategoryCode = ''
      console.log(val, '123');
      if(val[0] == '' && val.length == 1) {
        console.log(',K,,,');
        
        this.professionalCategoryCode = '-1'
      } else if (val.length == 1) {
        this.professionalCategoryCode = val[0]
      } else if (val.length != 1) {
        this.systemCategoryCode = val[val.length - 1]
      }
      console.log(this.professionalCategoryCode, 'this.professionalCategoryCode');
    },
    getRowKeys(row) {
      return row.id;
    },
    closePinot() {
      this.$emit('closePinot')
    },
    editSubmit() {
      if (this.multipleSelection.length > 0) {
        this.$emit("sure", this.multipleSelection);
      } else {
        this.$message.error("请选择上级设备");
      }
    },
    handleSizeChange(val) {
      this.currentPage = 1;
      this.pageSize = val;
      this.search(true);
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.search(true);
    },
    handleSelectionChange(val) {
      if (val.length > 1) {
        const del_row = val.shift();
        this.$refs.personTable.toggleRowSelection(del_row, false);
      }
      this.multipleSelection = val;
    },
    // 获取
    getCategoryData() {
      const userInfo = this.$store.state.user.userInfo.user
      let params = {
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ',
      }
      this.$api.getCategoryList(params).then((res) => {
        this.childrenDeviceList = transData(res.data, 'id', 'parentId', 'children')
      })
    },
    // 获取右侧table列表
    search(tages = false) {
      let data = {
        pageSize: this.pageSize,
        currentPage: this.currentPage,
        assetName: this.assetName,
        assetCode: this.assetCode,
        systemCategoryCode: this.systemCategoryCode,
        professionalCategoryCode: this.professionalCategoryCode, // 专业类别
        assetCategoryCode: '', // 资产大类
        assetSubcategoryCode: '' // 资产小类
      };
      this.tableLoading = true;
      this.$api.getAssetList(data).then((res) => {
        this.tableLoading = false;
        if (res.code == 200) {
          this.tableData = res.data.assetDetailsList;
          this.total = res.data.sum
        }
      })
      // this.tableData = [
      //   {
      //     name: '张三',
      //     mobilePhone: '13088888888',
      //     controlTeamName: '中科测试',
      //   },
      //   {
      //     name: '李四',
      //     mobilePhone: '13088888889',
      //     controlTeamName: '班组测试',
      //   }
      // ]
      // this.$http.getControlTeamUserList(data).then((res) => {
      //   this.tableLoading = false;
      //   this.tableData = res.data.list;
      //   this.total = parseInt(res.data.sum);
      //   // if(tages) {
      //   //   setTimeout(() => {
      //   //     this.rowSelectFlag = true
      //   //     this.tableData.forEach((row) => {
      //   //       if(this.stateOpty == 'responsible') {
      //   //         const matchedIndex = this.responsibleUserList.findIndex(item => item === row.id)
      //   //         this.$refs['personTable'].toggleRowSelection(row,matchedIndex != -1)
      //   //       } else if(this.stateOpty == 'userType') {
      //   //         const matchedIndex = this.safetyUserList.findIndex(item => item === row.id)
      //   //         this.$refs['personTable'].toggleRowSelection(row,matchedIndex != -1)
      //   //       } else if(this.stateOpty == 'minor') {
      //   //         const matchedIndex = this.minorDetail.findIndex(item => item === row.id)
      //   //         this.$refs['personTable'].toggleRowSelection(row,matchedIndex != -1)
      //   //       }
      //   //     })
      //   //   })
      //   // }
      // });
    },
    // 条件查询
    searchList() {
      this.currentPage = 1
      this.search()
    },
    reset() {
      this.currentPage = 1;
      this.assetName = "";
      this.assetCode = "";
      this.systemCategoryCodes = null
      this.systemCategoryCode = ''
      this.professionalCategoryCode = ""
      this.search();
    },
    // filterNode(value, data) {
    //   if (!value) return true;
    //   return data.officeName.indexOf(value) !== -1;
    // },
    // filterNode2(value, data) {
    //   if (!value) return true;
    //   return data.officeName.indexOf(value) !== -1;
    // },
  },
};
</script>

<style lang="scss" scoped>
.outermost {
  display: flex;
  width: 100%;
  height: 100%;
}

.left {
  padding: 10px;
  width: 268px;
  margin-right: 10px;
  height: 100%;
  background-color: #fff;

  .block {
    height: calc(100% - 80px);
    overflow: auto;
  }
}

.footer {
  width: 100%;
  display: flex;
}

.footer_left {
  width: 80%;
}

.footer_right {
  width: 20%;
}

.content {
  background-color: #fff;
  padding: 10px;
  width: 100%;
  height: 100%;
}

.topTools {
  margin-bottom: 15px;
}

.user-pagination {
  text-align: right;
  position: absolute;
  left: 10px;
  bottom: 20px;
}

.tree-title {
  border: 1px solid #ebeef5;
  width: 40%;
  margin-bottom: 5px;
  line-height: 30px;
  border-radius: 4px;
  text-align: center;
  color: #5188fc;
  cursor: pointer;
}

.tree-title-active {
  background: #5188fc;
  border-color: #5188fc;
  color: #fff;
}

::v-deep .el-tree-node__label {
  display: inline-block;
  white-space: normal;
  text-align: left;
}

::v-deep .el-tree-node__content {
  height: auto;
}

::v-deep .el-table--scrollable-y .el-table__body-wrapper {
  overflow-y: visible !important;
}
</style>
