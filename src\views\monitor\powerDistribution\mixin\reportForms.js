import { monitorTypeList } from '@/util/dict.js'
export default {
  data() {
    return {
      projectCode: monitorTypeList.find(item => item.projectName == '配电监测').projectCode,
      typeTabs: [
        { 
          name: '电力数据表',  // 名称
          type: '0',  // 类型标识
          api: 'selectDataReport',
          dateTypeArr: [
            {
              dateType: 'day',
              name: '日报',
              unit: '日',
              status: 0
            }
          ],
          typeArr: []
        },
        {
          name: '电力极值报表',
          type: '1',
          api: 'selectExtremumReport',
          // 时间类型(0:日 1:月 2:年 3:自定义)
          dateTypeArr: [
            {
              dateType: 'day',
              name: '日报',
              unit: '日',
              status: 0
            },
            {
              dateType: 'month',
              name: '月报',
              unit: '月',
              status: 1
            },
            {
              dateType: 'custom',
              name: '自定义',
              unit: '',
              status: 3
            }
          ],
          typeArr: []
        },
        {  
          name: '用能报表', 
          type: '2',
          api: 'selectEnergyReport',
          dateTypeArr: [
            {
              dateType: 'day',
              name: '日报',
              unit: '日',
              status: 0
            },
            {
              dateType: 'month',
              name: '月报',
              unit: '月',
              status: 1
            },
            {
              dateType: 'year',
              name: '年报',
              unit: '年',
              status: 2
            }
          ],
          typeArr: []
        },
        { 
          name: '分时段用电', 
          type: '3',
          api: 'selectTimeFrame',
          dateTypeArr: [
            {
              dateType: 'custom',
              name: '自定义',
              unit: '',
              status: 3
            }
          ],
          typeArr: []
        }
      ]
    }
  }
}