<template>
  <PageContainer>
    <div slot="header">
      <div class="searchForm">
        <div class="search-box">
          <el-select v-model="searchForm.troopType" placeholder="请选择队伍类型" class="ml-16">
            <el-option v-for="item in typeList" :key="item.dict_code" :label="item.description" :value="item.dict_code">
            </el-option>
          </el-select>
          <el-input v-model="searchForm.troopCaptainName" clearable filterable placeholder="请输入队伍负责人"></el-input>
          <div class="ml-16">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button type="primary" plain @click="reset">重置</el-button>
          </div>
        </div>
      </div>
    </div>
    <div slot="content" ref="contentRef" class="table-box">
      <div class="tableContainer">
        <el-button type="primary" style="margin-bottom: 12px" @click="handleListEvent('add')">新增</el-button>
        <el-table v-loading="tableLoading" height="calc(100% - 96px)" :data="tableData" border stripe>
          <el-table-column type="selection" width="55">
          </el-table-column>
          <el-table-column label="队伍名称" prop="troopName" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column label="队伍类型" prop="troopTypeName" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column label="队伍负责人" prop="troopCaptainName" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column label="队伍人员" prop="troopPersonnelName" show-overflow-tooltip
            align="center"></el-table-column>
          <el-table-column label="队伍职责" prop="troopResponsibility" show-overflow-tooltip
            align="center"></el-table-column>
          <el-table-column label="频道号" prop="groupHostextension" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column label="队伍描述" prop="troopDescribe" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column label="操作" show-overflow-tooltip align="center">
            <template slot-scope="scope">
              <el-button type="text" @click="handleListEvent('edit', scope.row)">编辑</el-button>
              <el-button type="text" @click="handleListEvent('del',scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination :current-page="pagination.page" :page-sizes="[15, 30, 50, 100]" :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper" :total="pageTotal" @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
        </el-pagination>
      </div>
      <el-dialog v-dialogDrag :visible.sync="dialogVisible" :before-close="dialogClosed" :modal="false"
        :close-on-click-modal="false" title="新增队伍" width="50%" custom-class="model-dialog">
        <div class="diaContent" style="padding: 10px">
          <el-form ref="formInline" :model="formInline" label-width="100px" :rules="rules" label-position="right">
            <el-form-item label="频道号：" prop="groupHostextension" class="form-item">
              <el-input v-model="formInline.groupHostextension" disabled placeholder="请输入频道号">
              </el-input>
              <!-- <el-button type="primary" plain class="ml-16" @click="generate">生成</el-button> -->
            </el-form-item>
            <el-form-item label="队伍名称：" prop="troopName" class="form-item">
              <el-input v-model.trim="formInline.troopName" placeholder="请输入队伍名称" :maxlength="20"></el-input>
            </el-form-item>
            <el-form-item label="队伍类型：" prop="troopTypeCode" class="form-item">
              <el-select ref="troopTypeCode" v-model="formInline.troopTypeCode" placeholder="请选择队伍类型" clearable
                filterable @change="troopTypeChange">
                <el-option v-for="item in typeList" :key="item.dict_code" :label="item.description"
                  :value="item.dict_code">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="队伍设备：" prop="troopDeviceCode" class="form-item">
              <el-select ref="troopDeviceCode" v-model="troopDeviceCodeArr" multiple placeholder="请选择队伍设备" clearable
                filterable @remove-tag="removeTag" @change="troopDeviceCodeChange">
                <el-option v-for="item in troopDeviceList" :key="item.extensionNum" :label="item.usrName"
                  :disabled="item.usrType === 1" :value="item.extensionNum">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="队伍人员：" prop="troopPersonnelName" class="form-item">
              <el-input v-model.trim="formInline.troopPersonnelName" placeholder="请输入队伍人员" @focus="handleClick">
              </el-input>
            </el-form-item>
            <el-form-item label="队伍队长：" prop="troopCaptainCode" class="form-item">
              <el-select ref="troopCaptainCode" v-model="formInline.troopCaptainCode" placeholder="请选择队伍队长" clearable
                filterable @change="troopCaptainChange">
                <el-option v-for="item in troopCaptainList" :key="item.id" :label="item.staffName"
                  :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="队伍职责：" prop="troopResponsibility" class="inputWidth">
              <el-input v-model.trim="formInline.troopResponsibility" maxlength="200" show-word-limit type="textarea"
                placeholder="请输入队伍职责"></el-input>
            </el-form-item>
            <el-form-item label="队伍描述：" prop="troopDescribe" class="inputWidth">
              <el-input v-model.trim="formInline.troopDescribe" maxlength="200" show-word-limit type="textarea"
                placeholder="请输入队伍描述"></el-input>
            </el-form-item>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="dialogClosed">取 消</el-button>
          <el-button type="primary" @click="submit('formInline')">确 定</el-button>
        </span>
      </el-dialog>
      <!--选择人员 -->
      <template v-if="personDialogShow">
        <SelectPeopleDialog :personDialogShow="personDialogShow" @submitPersonDialog="submitPersonDialog"
          @closePersonDialog="closePersonDialog" />
      </template>
    </div>
  </PageContainer>
</template>
<script>
import SelectPeopleDialog from '../../operationPort/exerciseManage/exercisePlan/components/SelectPeople.vue'
export default {
  name: 'emergencyTeam',
  components: {
    SelectPeopleDialog
  },
  data() {
    return {
      isSelectPers: false, // 选择人员
      tableLoading: false,
      dialogVisible: false,
      personDialogShow: false,
      tableData: [],
      searchForm: {
        troopCaptainName: '',
        troopType: ''
      },
      typeList: [],
      troopCaptainList: [],
      troopDeviceList: [],
      pagination: {
        pageSize: 15,
        page: 1
      },
      pageTotal: 0,
      formInline: {
        id: '',
        troopName: '',
        troopTypeCode: '',
        troopTypeName: '',
        troopPersonnelCode: '',
        troopPersonnelName: '',
        troopCaptainCode: '',
        troopCaptainName: '',
        troopResponsibility: '',
        troopDescribe: '',
        troopDeviceCode: '',
        troopDeviceName: '',
        usrNumber: '',
        groupHostextension: ''
      },
      troopDeviceCodeArr: [],
      rules: {
        troopName: [{ required: true, message: '请输入队伍名称', trigger: 'blur' }],
        troopTypeCode: [{ required: true, message: '请选择队伍类型', trigger: 'change' }],
        troopPersonnelName: [{ required: true, message: '请选择队伍人员', trigger: 'change' }],
        troopCaptainCode: [{ required: true, message: '请选择队伍队长', trigger: 'change' }],
        // troopResponsibility: [{ required: true, message: '请输入队伍职责', trigger: 'blur' }],
        // troopDescribe: [{ required: true, message: '请输入队伍描述', trigger: 'blur' }],
        // groupHostextension: [{ required: true, message: '请生成频道号', trigger: 'blur' }]
      },
      troopTypeList: []
    }
  },
  mounted() {
    this.getTypeList()
    this.staffListFn()
    this.getTableData()
  },
  methods: {
    //  获取人员信息列表
    staffListFn() {
      this.$api
        .staffList({
          current: 1,
          size: 9999
        })
        .then((res) => {
          if (res.code == 200) {
            this.troopCaptainList = res.data.records || []
          }
        })
    },
    getTypeList() {
      let data = {
        dictType: 'troopTypeCode'
      }
      this.$api.getEmergencyTroopTypeList(data).then((res) => {
        if (res.code === '200') {
          this.typeList = res.data
        }
      })
    },
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.pagination.page = 1
      this.getTableData()
    },
    getTableData() {
      this.tableLoading = true
      let data = {
        ...this.pagination,
        ...this.searchForm
      }
      this.$api
        .getEmergencyTroopManageList(data)
        .then((res) => {
          this.tableLoading = false
          if (res.code == 200) {
            this.tableData = res.data ? res.data.records : []
            this.pageTotal = res.data ? res.data.total : 0
          } else if (res.message) {
            this.tableData = []
            this.pagination.total = 0
            this.$message.error(res.message)
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    // 队长获取name
    troopCaptainChange(val) {
      this.formInline.troopCaptainName = this.troopCaptainList.find(item => {
        return item.id == val
      }).staffName
    },
    // 类型获取name
    troopTypeChange(val) {
      this.formInline.troopTypeName = this.typeList.find(item => {
        return item.dict_code == val
      }).description
    },
    // 队伍设备
    troopDeviceCodeChange(val) {
      if (val && val.length) {
        this.formInline.troopDeviceCode = val.join(',')
        this.formInline.usrNumber = val.join(',')
        let arr = []
        val.forEach(el => {
          this.troopDeviceList.forEach(t => {
            if (el === t.extensionNum) {
              arr.push(t.usrName)

            }
          })
        })
        this.formInline.troopDeviceName = arr.join(',')
      }
    },
    removeTag(val) {
      let checkItem = this.troopDeviceList.find(item => item.extensionNum === val)
      if (checkItem.usrType === 1) {
        let arr = []
        this.troopDeviceList.forEach(el => {
          if (el.usrType === 1) {
            arr.push(el.extensionNum)
          }
        })
        this.troopDeviceCodeArr = arr
      }
    },
    // 融合终端获取终端设备
    getTerminalList() {
      let params = {
        pageNum: 1,
        pageSize: 9999
      }
      this.$api.searchTerminalList(params).then((res) => {
        if (res.code === '200') {
          this.troopDeviceList = res.data.records
          // 默认选择调度台
          this.troopDeviceList.forEach(el => {
            if (el.usrType === 1) {
              this.troopDeviceCodeArr.push(el.extensionNum)
            }
          })
          this.troopDeviceCodeArr = Array.from(new Set(this.troopDeviceCodeArr.map(item => JSON.stringify(item)))).map(item => JSON.parse(item)) // 去重
          this.troopDeviceCodeChange(this.troopDeviceCodeArr)
        } else {
          this.troopDeviceList = []
          this.troopDeviceCodeArr = []
          this.$message.error(res.msg)
        }
      }).catch(() => {
        this.troopDeviceList = []
        this.troopDeviceCodeArr = []
      })
    },
    generate() {
      this.getChannelNumber()
    },
    // 生成频道号
    getChannelNumber() {
      this.$api.groupNumber({ callGrouptype: '4' }).then((res) => {
        if (res.code == '200') {
          this.formInline.groupHostextension = res.data.groupHostextension
        } else {
          this.formInline.groupHostextension = ''
          // this.$message.error(res.msg)
        }
      }).catch(() => {
        this.formInline.groupHostextension = ''
      })
    },
    search() {
      this.pagination.page = 1
      this.getTableData()
    },
    reset() {
      this.searchForm = {
        troopCaptainName: '',
        troopType: ''
      }
      this.pagination.page = 1
      this.pageTotal = 0
      this.getTableData()
    },
    handleCurrentChange(val) {
      this.pagination.page = val
      this.getTableData()
    },
    handleListEvent(type, row) {
      this.getTerminalList()
      if (type == 'add') {
        this.$nextTick(() => {
          this.getChannelNumber()
        })
        this.formInline = {
          id: '',
          troopName: '',
          troopTypeCode: '',
          troopTypeName: '',
          troopPersonnelCode: '',
          troopPersonnelName: '',
          troopCaptainCode: '',
          troopCaptainName: '',
          troopResponsibility: '',
          troopDescribe: '',
          troopDeviceCode: '',
          troopDeviceName: '',
          groupHostextension: '',
          usrNumber: ''
        }
        this.troopDeviceCodeArr = []
        this.dialogVisible = true
      }
      if (type == 'edit') {
        let params = {
          id: row.id
        }
        this.$api.getEmergencyTroopManageDeatil(params).then((res) => {
          if (res.code === '200') {
            this.formInline = res.data
            if (res.data.troopDeviceCode) {
              let arr = res.data.troopDeviceCode.split(',')
              this.troopDeviceCodeArr = arr
            } else {
              this.troopDeviceCodeArr = []
            }
          } else {
            this.$message.error(res.msg)
          }
        })
        this.dialogVisible = true
      }
      if (type == 'del') {
        this.$confirm('此操作将永久删除, 是否继续?', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let params = {
            id: row.id
          }
          this.$api.deleteEmergencyTroopManageData(params, { 'operation-type': 3, 'operation-name': row.troopName, 'operation-id': row.id }).then((res) => {
            if (res.code === '200') {
              this.$message.success(res.msg)
              this.getTableData()
            } else {
              this.$message.error(res.msg)
            }
          })
        })
      }
    },
    // 确定
    submit(formName) {
      // if (!this.formInline.troopDeviceCode) {
      //   this.$message({
      //     message: '请选择队伍设备！',
      //     type: 'warning'
      //   })
      //   return false
      // }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let data = {
            ...this.formInline
          }
          // 添加操作日志
          let header = {}
          if (data.id) {
            header = {
              'operation-type': 2,
              'operation-id': data.id,
              'operation-name': data.troopName
            }
          } else {
            header = {
              'operation-type': 1
            }
          }
          this.$api.addEmergencyTroopData(data, header).then((res) => {
            if (res.code === '200') {
              this.$message.success(res.msg)
              this.getTableData()
            } else {
              this.$message.error(res.msg)
            }
          })
          this.dialogVisible = false
          this.$refs.formInline.resetFields()
        } else {
          return false
        }
      })

    },
    // 取消
    dialogClosed() {
      this.dialogVisible = false
      this.$refs.formInline.resetFields()
    },
    handleClick() {
      this.personDialogShow = true
    },
    submitPersonDialog(list) {
      let arr = list
      this.formInline.troopPersonnelName = arr
        .map(item => {
          return item.staffName
        })
        .join(',')
      this.formInline.troopPersonnelCode = arr
        .map(item => {
          return item.id
        })
        .join(',')
      this.closePersonDialog()
    },
    closePersonDialog() {
      this.personDialogShow = false
    }
  }
}
</script>
<style lang="scss" scoped>
.searchForm {
  display: flex;
  height: 80px;
  padding: 0 16px;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .el-input {
    width: 200px;
    margin-left: 16px;
  }

  > div {
    margin-right: 20px;
  }
}

.search-box {
  display: flex;
  align-items: center;
}

.container-content > div {
  height: 100%;
  margin-top: 16px;
}
.el-table {
  margin-bottom: 12px;
}

::v-deep .el-table__row:hover {
  cursor: pointer;
}
.table-box {
  background-color: #fff;
  height: 100%;
  padding: 16px;
  .tableContainer {
    height: 100%;
    .el-table {
      height: calc(100% - 96px) !important;
    }
  }
}
.diaContent {
  width: 100%;
  max-height: 550px !important;
  overflow: auto;
  background-color: #fff !important;
  .el-input,
  .el-select,
  .el-cascader {
    width: 300px;
  }
}
.form-item {
  display: inline-block;
  margin-right: 20px;
}
.inputWidth {
  width: 820px;
}
.ml-16 {
  margin-left: 16px;
}
.dialog .el-dialog {
  width: 60% !important;
}
</style>
