import Layout from '@/layout'
import EmptyLayout from '@/layout/empty'
export default [
  {
    path: '/calendar',
    component: Layout,
    redirect: '/calendar/index',
    name: 'calendar',
    meta: {
      title: '工作日历',
      menuAuth: '/calendar/index'
      // icon: 'sidebar-breadcrumb'
    },
    children: [
      {
        path: 'index',
        name: 'calendarIndex',
        component: () => import('@/views/equipmentCenter/calendar.vue'),
        meta: {
          title: '工作日历',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/calendar'
        }
      },
      {
        path: 'calendarProgressDetail',
        name: 'calendarProgressDetail',
        component: () => import('@/views/equipmentCenter/InspectionManagement/progressDetail.vue'),
        meta: {
          title: '计划进度详情',
          sidebar: false,
          activeMenu: '/calendar/index'
        }
      },
      {
        path: 'planProgressDetail',
        name: 'planProgressDetail',
        component: () => import('@/views/equipmentCenter/InspectionManagement/planProgressDetail.vue'),
        meta: {
          title: '任务详情',
          sidebar: false,
          activeMenu: '/calendar/index'
        }
      },
      {
        path: 'InspectionPointDetail',
        name: 'InspectionPointDetail',
        component: () => import('@/views/equipmentCenter/InspectionManagement/components/InspectionPointDetail.vue'),
        meta: {
          title: '巡检点详情',
          sidebar: false,
          activeMenu: '/calendar/index'
        }
      },
      {
        path: 'calendarTaskDetail',
        name: 'calendarTaskDetail',
        component: () => import('@/views/equipmentCenter/InspectionManagement/taskDetail.vue'),
        meta: {
          title: '任务详情',
          sidebar: false,
          activeMenu: '/calendar/index'
        }
      },
      {
        path: 'taskPointDetail',
        name: 'taskPointDetail',
        component: () => import('@/views/equipmentCenter/InspectionManagement/components/InspectionPointDetail.vue'),
        meta: {
          title: '巡检点详情',
          sidebar: false,
          activeMenu: '/calendar/index'
        }
      }
    ]
  },
  {
    path: '/statisticAnalysis',
    component: Layout,
    redirect: '/statisticAnalysis/index',
    name: 'statisticAnalysis',
    meta: {
      title: '统计分析',
      menuAuth: '/statisticAnalysis/index'
    },
    children: [
      {
        path: 'index',
        name: 'statisticAnalysisIndex',
        component: () => import('@/views/equipmentCenter/statisticAnalysis.vue'),
        meta: {
          title: '统计分析',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/statisticAnalysis'
        }
      },
      {
        path: 'taskListByStatistic',
        name: 'taskListByStatistic',
        component: () => import('@/views/equipmentCenter/taskListByStatistic.vue'),
        meta: {
          title: '任务列表',
          sidebar: false,
          activeMenu: '/statisticAnalysis/index'
        }
      },
      {
        path: 'taskDetail',
        name: 'taskDetail',
        component: () => import('@/views/equipmentCenter/InspectionManagement/taskDetail.vue'),
        meta: {
          title: '任务详情',
          sidebar: false,
          activeMenu: '/statisticAnalysis/index'
        }
      },
      {
        path: 'taskPointDetail',
        name: 'taskPointDetail',
        component: () => import('@/views/equipmentCenter/InspectionManagement/components/InspectionPointDetail.vue'),
        meta: {
          title: '巡检点详情',
          sidebar: false,
          activeMenu: '/statisticAnalysis/index'
        }
      }
    ]
  },
  {
    path: '/InspectionManagement',
    component: Layout,
    redirect: '/InspectionManagement/planManagement',
    name: 'InspectionManagement',
    meta: {
      title: '巡检管理',
      menuAuth: '/InspectionManagement'
    },
    children: [
      {
        path: 'templateManagement',
        component: EmptyLayout,
        redirect: { name: 'templateManagement' },
        meta: {
          title: '模板管理',
          menuAuth: '/InspectionManagement/templateManagement'
        },
        children: [
          {
            path: '',
            name: 'templateManagement',
            component: () => import('@/views/equipmentCenter/InspectionManagement/templateManagement.vue'),
            meta: {
              title: '模板管理',
              sidebar: false,
              breadcrumb: false,
              type: '1'
            }
          },
          {
            path: 'addTemplate',
            name: 'addTemplate',
            component: () => import('@/views/equipmentCenter/InspectionManagement/components/addTemplate.vue'),
            meta: {
              title: '新增模板',
              sidebar: false,
              activeMenu: '/InspectionManagement/templateManagement',
              type: '1'
            }
          },
          {
            path: 'templateDetail',
            name: 'templateDetail',
            component: () => import('@/views/equipmentCenter/InspectionManagement/components/templateDetail.vue'),
            meta: {
              title: '模板详情',
              sidebar: false,
              activeMenu: '/InspectionManagement/templateManagement',
              type: '1'
            }
          }
        ]
      },
      {
        path: 'planManagement',
        component: EmptyLayout,
        redirect: { name: 'planManagement' },
        meta: {
          title: '计划管理',
          menuAuth: '/InspectionManagement/planManagement'
        },
        children: [
          {
            path: '',
            name: 'planManagement',
            component: () => import('@/views/equipmentCenter/InspectionManagement/planManagement.vue'),
            meta: {
              title: '计划管理',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'progressDetail',
            name: 'progressDetail',
            component: () => import('@/views/equipmentCenter/InspectionManagement/progressDetail.vue'),
            meta: {
              title: '计划进度详情',
              sidebar: false,
              activeMenu: '/InspectionManagement/planManagement'
            }
          },
          {
            path: 'planProgressDetail',
            name: 'planProgressDetail',
            component: () => import('@/views/equipmentCenter/InspectionManagement/planProgressDetail.vue'),
            meta: {
              title: '任务详情',
              sidebar: false,
              activeMenu: '/InspectionManagement/planManagement'
            }
          },
          {
            path: 'InspectionPointDetail',
            name: 'InspectionPointDetail',
            component: () => import('@/views/equipmentCenter/InspectionManagement/components/InspectionPointDetail.vue'),
            meta: {
              title: '巡检点详情',
              sidebar: false,
              activeMenu: '/InspectionManagement/planManagement'
            }
          },
          {
            path: 'addPlans',
            name: 'addPlans',
            component: () => import('@/views/equipmentCenter/InspectionManagement/addPlans.vue'),
            meta: {
              title: '新增计划',
              sidebar: false,
              activeMenu: '/InspectionManagement/planManagement'
            }
          }
        ]
      },
      {
        path: 'taskManagement',
        component: EmptyLayout,
        redirect: { name: 'taskManagement' },
        meta: {
          title: '任务管理',
          menuAuth: '/InspectionManagement/taskManagement'
        },
        children: [
          {
            path: '',
            name: 'taskManagement',
            component: () => import('@/views/equipmentCenter/InspectionManagement/taskManagement.vue'),
            meta: {
              title: '任务管理',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'taskDetail',
            name: 'taskDetail',
            component: () => import('@/views/equipmentCenter/InspectionManagement/taskDetail.vue'),
            meta: {
              title: '任务详情',
              sidebar: false,
              activeMenu: '/InspectionManagement/taskManagement'
            }
          },
          {
            path: 'taskPointDetail',
            name: 'taskPointDetail',
            component: () => import('@/views/equipmentCenter/InspectionManagement/components/InspectionPointDetail.vue'),
            meta: {
              title: '巡检点详情',
              sidebar: false,
              activeMenu: '/InspectionManagement/taskManagement'
            }
          },
          {
            path: 'taskPointEdit',
            name: 'taskPointEdit',
            component: () => import('@/views/equipmentCenter/InspectionManagement/planProgressDetail.vue'),
            meta: {
              title: '巡检点编辑',
              sidebar: false,
              activeMenu: '/InspectionManagement/taskManagement'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/MaintenanceManagement',
    component: Layout,
    redirect: '/MaintenanceManagement/planManagement',
    name: 'MaintenanceManagement',
    meta: {
      title: '保养管理',
      menuAuth: '/MaintenanceManagement'
    },
    children: [
      {
        path: 'maintenanceTemplate',
        component: EmptyLayout,
        redirect: { name: 'maitemplateManagement' },
        meta: {
          title: '模板管理',
          menuAuth: '/MaintenanceManagement/maintenanceTemplate'
        },
        children: [
          {
            path: '',
            name: 'maitemplateManagement',
            component: () => import('@/views/equipmentCenter/InspectionManagement/templateManagement.vue'),
            meta: {
              title: '模板管理',
              sidebar: false,
              breadcrumb: false,
              type: '2'
            }
          },
          {
            path: 'maintenanceAddTemplate',
            name: 'maintenanceAddTemplate',
            component: () => import('@/views/equipmentCenter/InspectionManagement/components/addTemplate.vue'),
            meta: {
              title: '新增模板',
              sidebar: false,
              activeMenu: '/MaintenanceManagement/maintenanceTemplate',
              type: '2'
            }
          },
          {
            path: 'maintenanceTemplateDetail',
            name: 'maintenanceTemplateDetail',
            component: () => import('@/views/equipmentCenter/InspectionManagement/components/templateDetail.vue'),
            meta: {
              title: '模板详情',
              sidebar: false,
              activeMenu: '/MaintenanceManagement/templateManagement',
              type: '2'
            }
          }
        ]
      },
      {
        path: 'planManagement',
        component: EmptyLayout,
        redirect: { name: 'planManagement' },
        meta: {
          title: '计划管理',
          menuAuth: '/MaintenanceManagement/planManagement'
        },
        children: [
          {
            path: '',
            name: 'planManagement',
            component: () => import('@/views/equipmentCenter/InspectionManagement/planManagement.vue'),
            meta: {
              title: '计划管理',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'progressDetail',
            name: 'progressDetail',
            component: () => import('@/views/equipmentCenter/InspectionManagement/progressDetail.vue'),
            meta: {
              title: '计划进度详情',
              sidebar: false,
              activeMenu: '/MaintenanceManagement/planManagement'
            }
          },
          {
            path: 'planProgressDetail',
            name: 'planProgressDetail',
            component: () => import('@/views/equipmentCenter/InspectionManagement/planProgressDetail.vue'),
            meta: {
              title: '任务详情',
              sidebar: false,
              activeMenu: '/MaintenanceManagement/planManagement'
            }
          },
          {
            path: 'InspectionPointDetail',
            name: 'InspectionPointDetail',
            component: () => import('@/views/equipmentCenter/InspectionManagement/components/InspectionPointDetail.vue'),
            meta: {
              title: '保养点详情',
              sidebar: false,
              activeMenu: '/MaintenanceManagement/planManagement'
            }
          },
          {
            path: 'addPlans',
            name: 'addPlans',
            component: () => import('@/views/equipmentCenter/InspectionManagement/addPlans.vue'),
            meta: {
              title: '新增计划',
              sidebar: false,
              activeMenu: '/MaintenanceManagement/planManagement'
            }
          }
        ]
      },
      {
        path: 'taskManagement',
        component: EmptyLayout,
        redirect: { name: 'taskManagement' },
        meta: {
          title: '任务管理',
          menuAuth: '/MaintenanceManagement/taskManagement'
        },
        children: [
          {
            path: '',
            name: 'taskManagement',
            component: () => import('@/views/equipmentCenter/InspectionManagement/taskManagement.vue'),
            meta: {
              title: '任务管理',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'taskDetail',
            name: 'taskDetail',
            component: () => import('@/views/equipmentCenter/InspectionManagement/taskDetail.vue'),
            meta: {
              title: '任务详情',
              sidebar: false,
              activeMenu: '/MaintenanceManagement/taskManagement'
            }
          },
          {
            path: 'taskPointDetail',
            name: 'taskPointDetail',
            component: () => import('@/views/equipmentCenter/InspectionManagement/components/InspectionPointDetail.vue'),
            meta: {
              title: '保养点详情',
              sidebar: false,
              activeMenu: '/MaintenanceManagement/taskManagement'
            }
          },
          {
            path: 'taskPointEdit',
            name: 'taskPointEdit',
            component: () => import('@/views/equipmentCenter/InspectionManagement/planProgressDetail.vue'),
            meta: {
              title: '保养点编辑',
              sidebar: false,
              activeMenu: '/MaintenanceManagement/taskManagement'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/annualManagement',
    component: Layout,
    redirect: '/annualManagement/planManagement',
    name: 'annualManagement',
    meta: {
      title: '年检管理',
      menuAuth: '/annualManagement'
    },
    children: [
      {
        path: 'templateManagement',
        component: EmptyLayout,
        redirect: { name: 'templateManagement' },
        meta: {
          title: '模板管理',
          menuAuth: '/annualManagement/maintenanceTemplate'
        },
        children: [
          {
            path: '',
            name: 'annualTemplateManagement',
            component: () => import('@/views/equipmentCenter/InspectionManagement/templateManagement.vue'),
            meta: {
              title: '模板管理',
              sidebar: false,
              breadcrumb: false,
              type: '5'
            }
          },
          {
            path: 'annualAddTemplate',
            name: 'annualAddTemplate',
            component: () => import('@/views/equipmentCenter/InspectionManagement/components/addTemplate.vue'),
            meta: {
              title: '新增模板',
              sidebar: false,
              activeMenu: '/annualManagement/templateManagement',
              type: '5'
            }
          },
          {
            path: 'annualTemplateDetail',
            name: 'annualTemplateDetail',
            component: () => import('@/views/equipmentCenter/InspectionManagement/components/templateDetail.vue'),
            meta: {
              title: '模板详情',
              sidebar: false,
              activeMenu: '/annualManagement/templateManagement',
              type: '5'
            }
          }
        ]
      },
      {
        path: 'planManagement',
        component: EmptyLayout,
        redirect: { name: 'planManagement' },
        meta: {
          title: '计划管理',
          menuAuth: '/annualManagement/planAnnualManagement'
        },
        children: [
          {
            path: '',
            name: 'planManagement',
            component: () => import('@/views/equipmentCenter/InspectionManagement/planManagement.vue'),
            meta: {
              title: '计划管理',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'progressDetail',
            name: 'progressDetail',
            component: () => import('@/views/equipmentCenter/InspectionManagement/progressDetail.vue'),
            meta: {
              title: '计划进度详情',
              sidebar: false,
              activeMenu: '/annualManagement/planManagement'
            }
          },
          {
            path: 'planProgressDetail',
            name: 'planProgressDetail',
            component: () => import('@/views/equipmentCenter/InspectionManagement/planProgressDetail.vue'),
            meta: {
              title: '任务详情',
              sidebar: false,
              activeMenu: '/annualManagement/planManagement'
            }
          },
          {
            path: 'InspectionPointDetail',
            name: 'InspectionPointDetail',
            component: () => import('@/views/equipmentCenter/InspectionManagement/components/InspectionPointDetail.vue'),
            meta: {
              title: '巡检点详情',
              sidebar: false,
              activeMenu: '/annualManagement/planManagement'
            }
          },
          {
            path: 'addPlans',
            name: 'addPlans',
            component: () => import('@/views/equipmentCenter/InspectionManagement/addPlans.vue'),
            meta: {
              title: '新增计划',
              sidebar: false,
              activeMenu: '/annualManagement/planManagement'
            }
          }
        ]
      },
      {
        path: 'taskManagement',
        component: EmptyLayout,
        redirect: { name: 'taskManagement' },
        meta: {
          title: '任务管理',
          menuAuth: '/annualManagement/taskAnnualManagement'
        },
        children: [
          {
            path: '',
            name: 'taskManagement',
            component: () => import('@/views/equipmentCenter/InspectionManagement/taskManagement.vue'),
            meta: {
              title: '任务管理',
              sidebar: false,
              breadcrumb: false
            }
          },
          {
            path: 'taskDetail',
            name: 'taskDetail',
            component: () => import('@/views/equipmentCenter/InspectionManagement/taskDetail.vue'),
            meta: {
              title: '任务详情',
              sidebar: false,
              activeMenu: '/annualManagement/taskManagement'
            }
          },
          {
            path: 'taskPointDetail',
            name: 'taskPointDetail',
            component: () => import('@/views/equipmentCenter/InspectionManagement/components/InspectionPointDetail.vue'),
            meta: {
              title: '巡检点详情',
              sidebar: false,
              activeMenu: '/annualManagement/taskManagement'
            }
          },
          {
            path: 'taskPointEdit',
            name: 'taskPointEdit',
            component: () => import('@/views/equipmentCenter/InspectionManagement/planProgressDetail.vue'),
            meta: {
              title: '巡检点编辑',
              sidebar: false,
              activeMenu: '/InspectionManagement/taskManagement'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/deviceManagement',
    component: Layout,
    redirect: '/deviceManagement/index',
    name: 'DeviceManagement',
    meta: {
      title: '设备管理',
      menuAuth: '/deviceManagement/index'
      // icon: 'sidebar-breadcrumb'
    },
    children: [
      {
        path: 'index',
        name: 'DeviceManagementIndex',
        component: () => import('@/views/equipmentCenter/deviceManagement/deviceManagement.vue'),
        meta: {
          title: '设备管理',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/deviceManagement'
        }
      },
      {
        path: 'addDevice',
        name: 'addDevice',
        component: () => import('@/views/equipmentCenter/deviceManagement/components/addDevice.vue'),
        meta: {
          title: '新增设备',
          sidebar: false,
          activeMenu: '/deviceManagement/deviceManagement'
        }
      }
    ]
  },
  {
    path: '/maintenanceWorkOrderManagement',
    component: Layout,
    redirect: '/maintenanceWorkOrderManagement/index',
    name: 'MaintenanceWorkOrderManagement',
    meta: {
      title: '维修工单管理',
      menuAuth: '/maintenanceWorkOrderManagement/index'
      // icon: 'sidebar-breadcrumb'
    },
    children: [
      {
        path: 'index',
        name: 'MaintenanceWorkOrderManagement',
        component: () => import('@/views/equipmentCenter/maintenanceWorkOrderManagement/maintenanceWorkOrderManagement.vue'),
        meta: {
          title: '维修工单管理',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/maintenanceWorkOrderManagement'
        }
      }
      // {
      //   path: 'addDevice',
      //   name: 'addDevice',
      //   component: () => import('@/views/equipmentCenter/deviceManagement/components/addDevice.vue'),
      //   meta: {
      //     title: '新增设备',
      //     sidebar: false,
      //     activeMenu: '/deviceManagement/deviceManagement'
      //   }
      // }
    ]
  },
  {
    path: '/systemSettings',
    component: Layout,
    redirect: '/systemSettings/dictionaryManagement',
    name: 'systemSettings',
    meta: {
      title: '系统设置',
      menuAuth: '/systemSettings'
      // icon: 'sidebar-breadcrumb'
    },
    children: [
      {
        path: 'dictionaryManagement',
        component: EmptyLayout,
        redirect: { name: 'dictionaryManagement' },
        meta: {
          title: '字典管理',
          menuAuth: '/systemSettings/dictionaryManagement'
        },
        children: [
          {
            path: '',
            name: 'dictionaryManagement',
            component: () => import('@/views/equipmentCenter/systemSettings/dictionaryManagement.vue'),
            meta: {
              title: '字典管理',
              sidebar: false,
              breadcrumb: false,
              type: '1'
            }
          }
        ]
      },
      {
        path: 'parameter',
        component: EmptyLayout,
        redirect: { name: 'parameter' },
        meta: {
          title: '参数配置',
          menuAuth: '/systemSettings/parameter'
        },
        children: [
          {
            path: '',
            name: 'parameter',
            component: () => import('@/views/equipmentCenter/systemSettings/parameter.vue'),
            meta: {
              title: '字典管理',
              sidebar: false,
              breadcrumb: false,
              type: '1'
            }
          },
          {
            path: 'addEquipment',
            name: 'addEquipment',
            component: () => import('@/views/equipmentCenter/systemSettings/components/addEquipment.vue'),
            meta: {
              title: '新增',
              sidebar: false,
              activeMenu: '/systemSettings/components/addEquipment'
            }
          }
        ]
      },
      {
        path: 'regionalConfiguration',
        component: EmptyLayout,
        redirect: { name: 'regionalConfiguration' },
        meta: {
          title: '区域配置',
          menuAuth: '/systemSettings/regionalConfiguration'
        },
        children: [
          {
            path: '',
            name: 'regionalConfiguration',
            component: () => import('@/views/equipmentCenter/systemSettings/regionalConfiguration.vue'),
            meta: {
              title: '区域配置',
              sidebar: false,
              breadcrumb: false,
              type: '1'
            }
          }
        ]
      }
    ]
  }
]
