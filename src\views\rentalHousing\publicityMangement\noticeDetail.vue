<template>
  <el-dialog title="公示详情" :visible.sync="dialogVisibleRole" width="50%" :close-on-click-modal="false">
    <div class="dialog-content">
      <div class="content-header">
        <div class="item-header">公示范围：</div>
        <div class="item-content">{{ detailObj.typeCode == '0' ? '全院公示' : detailObj.officeName }}</div>
        <span class="item-time">{{ detailObj.timingTime }}</span>
      </div>
      <div class="content-body" v-html="detailObj.content"></div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="dialogVisibleRole = false">关 闭</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  name: 'noticeDetail',
  props: {
    detailObj: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dialogVisibleRole: false
    }
  },
  methods: {
    beforeClose() {
      this.dialogVisibleRole = false
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  height: 400px;
  padding: 0 20px;
  .content-header {
    width: 100%;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    .item-header {
      width: 80px;
    }
    .item-content {
      min-width: 100px;
      max-width: calc(100% - 240px);
    }
    .item-time {
      margin-left: 20px;
      width: 140px;
    }
  }
  .content-body {
    padding: 0 20px;
    overflow: auto;
    height: calc(100% - 21px);
  }
}
</style>
<style lang="scss">
.content-body {
  img {
    max-width: 100%;
  }
}
</style>
