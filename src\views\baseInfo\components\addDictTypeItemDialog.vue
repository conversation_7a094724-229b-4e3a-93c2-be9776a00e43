<template>
  <div>
    <el-dialog
      v-if="dialogVisible"
      v-dialogDrag
      :modal="false"
      :close-on-click-modal="false"
      title="空间功能类型字典"
      width="35%"
      :visible.sync="dialogVisible"
      custom-class="model-dialog"
      :before-close="closeDialog"
    >
      <div class="dialog-content">
        <el-form ref="formInline" :model="queryForm" class="form-inline" :rules="rules" label-width="100px">
          <el-form-item label="类型名称" prop="dictName">
            <el-input v-model.trim="queryForm.dictName" placeholder="请输入类型名称"></el-input>
          </el-form-item>
          <el-form-item label="类型编码" prop="dictValue">
            <el-input v-model.trim="queryForm.dictValue" placeholder="请输入类型编码"> </el-input>
          </el-form-item>
          <el-form-item label="类型颜色" prop="colour">
            <el-input v-model="queryForm.colour" readonly placeholder="请选择类型颜色">
              <el-color-picker slot="prepend" v-model="queryForm.colour" size="small "></el-color-picker>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" plain @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="submitFormData">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: 'addDictTypeItemDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    selectParent: {
      type: Object,
      default: () => {
        return {}
      }
    },
    selectItem: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      dialogVisible: this.visible,
      queryForm: {
        dictName: '',
        dictValue: '',
        colour: ''
      },
      rules: {
        dictName: { required: true, message: '请输入类型名称', trigger: 'change' }
      }
    }
  },
  computed: {},
  mounted() {
    if (Object.keys(this.selectItem).length) {
      let { id, dictName, dictValue, colourByHex } = this.selectItem
      this.queryForm = {
        id,
        dictName,
        dictValue,
        colour: colourByHex
      }
    }
  },
  methods: {
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', false)
      this.$refs['formInline'].resetFields()
    },
    submitFormData() {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          let params = {
            stdId: this.selectParent.id,
            ...this.queryForm
          }
          let apiKey = 'InsertDic'
          if (Object.keys(this.selectItem).length) {
            apiKey = 'UpdateValueDic'
          }
          this.$api[apiKey](params).then((res) => {
            if (res.code == 200) {
              this.$message.success(res.msg)
              this.closeDialog()
            } else {
              this.$message.error(res.msg)
            }
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  background: #fff;
  padding: 10px;
  max-height: 500px;
  overflow-y: auto;
  ::v-deep(.form-inline) {
    margin-top: 24px;
    .el-input {
      width: 300px;
    }
    .el-input-group__prepend {
      padding: 0px;
      border: none;
    }
  }
}
</style>
