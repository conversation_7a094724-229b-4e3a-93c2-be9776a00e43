<template>
  <el-dialog title="选择位置" :visible="visible" width="560px" :before-close="handleClosesubmit">
    <span class="txt-tip">
      <span> {{ names }} 等{{ idList.length }}个文件已无法恢复到原位置请重新选择恢复位置 </span>
    </span>
    <el-form ref="form" label-position="right" label-width="100px" :model="formData" :rules="rules">
      <el-row :gutter="24" style="margin: 0">
        <el-col :md="24">
          <el-form-item label="文件位置" prop="folderId">
            <el-cascader
              v-model="formData.folderId"
              :options="options"
              :props="{ emitPath: false, checkStrictly: true, children: 'children', label: 'label', value: 'id' }"
              clearable
              placeholder="请选择文件位置"
            ></el-cascader>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer">
      <el-button type="text" @click="handleClosesubmit"> 暂不恢复这些文件 </el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </div>
  </el-dialog>
</template>
<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    infoList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      formData: {
        folderId: ''
      },
      options: [],
      rules: {
        folderId: [{ required: true, message: '请选择位置', trigger: ['change', 'blur'] }]
      }
    }
  },
  watch: {
    infoList: {
      handler(val) {
        this.names = val.map((item) => item.archiveName).join(',')
        this.idList = val.map((item) => item.archiveId)
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    this.handleGetTreeData()
  },
  methods: {
    /**
     * @description 递归删除值为空数组的child属性，避免级联选择器显示后一级空列表
     * @param {Array} optionList 数据源
     * @param {String} childName 需要删除的child属性名 如 chhildren  childList等等
     */
    handleRemoveEmptyChild(optionList, childName) {
      for (let i = 0; i < optionList.length; i++) {
        if (optionList[i][childName].length !== 0) {
          this.handleRemoveEmptyChild(optionList[i][childName], childName)
        } else {
          delete optionList[i][childName]
        }
      }
    },
    handleGetTreeData() {
      const isMine = this.$route.path === '/dossierManager/myDossier'
      this.$api.fileManagement.selectFolderTree({ folderType: '1', isMine }).then((res) => {
        this.handleRemoveEmptyChild(res.data, 'children')
        this.options = res.data
      })
    },
    handleClosesubmit() {
      this.formData.folderId = ''
      this.$emit('update:visible', false)
    },
    handleSubmit() {
      this.$refs.form.validate((val) => {
        if (val) {
          const params = {
            ...this.formData,
            idList: this.idList
          }
          this.$api.fileManagement.updateRecoverNewFolder(params).then(() => {
            this.handleClosesubmit()
            this.$emit('success')
            this.$message.success('恢复成功')
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.txt-tip {
  display: flex;
  margin-bottom: 24px;
  font-size: 14px;
  color: #fa8c2b;
  .iconfont {
    margin-top: 6px;
    margin-right: 8px;
    font-size: 14px;
  }
}
</style>
