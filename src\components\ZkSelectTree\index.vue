<template>
  <el-select ref="select" :value="value" :placeholder="placeholder" :size="selectSize" style="width: 100%"
    @visible-change="visibleChange">
    <el-option ref="option" class="option" :value="optionData.id" :label="optionData.name">
      <el-tree ref="selectTree" class="tree" :node-key="nodeKey" :data="data" :props="props"
        :default-expanded-keys="[value]" highlight-current :expand-on-click-node="false"
        @node-click="handleNodeClick"></el-tree>
    </el-option>
  </el-select>
</template>
<script>
export default {
  name: 'zkSelectTree',
  props: {
    // v-model绑定
    value: {
      type: [String, Number],
      default: ''
    },
    // 树形的数据
    data: {
      type: Array,
      default: function () {
        return []
      }
    },
    // 每个树节点用来作为唯一标识的属性
    nodeKey: {
      type: [String, Number],
      default: 'id'
    },
    // tree的props配置
    props: {
      type: Object,
      default: function () {
        return {
          label: 'label',
          children: 'children'
        }
      }
    },
    // 返回数据格式 是否拼接
    callBackData: {
      type: String,
      default: ''
    },
    // 默认文字
    placeholder: {
      type: String,
      default: '请选择'
    },
    selectSize: {
      type: String,
      default: 'medium'
    }
  },
  data() {
    return {
      optionData: {
        id: '',
        name: ''
      }
    }
  },
  watch: {
    value: function (val) {
      if (!this.isEmpty(this.data)) {
        this.init(val)
      }
    },
    data: function (val) {
      if (!this.isEmpty(val)) {
        this.init(this.value)
      }
    }
  },
  mounted() {
    if (!this.isEmpty(this.data)) {
      this.init(this.value)
    }
    this.$nextTick(() => {
      var ele = document.querySelector('.el-select-dropdown__wrap')
      ele.scrollTop = 0
    })
  },
  methods: {
    // 是否为空
    isEmpty(val) {
      for (const key in val) {
        return false
      }
      return true
    },
    handleNodeClick(data, node) {
      const label = this.props.label || 'name'
      this.$emit('input', data[this.nodeKey])
      this.optionData.id = data[this.nodeKey]
      this.optionData.name = data[label]
      this.$emit('getNode', node)
      this.$emit('getData', data)
      this.$refs.select.visible = false
    },
    init(val) {
      if (val) {
        this.$nextTick(() => {
          const selectKey = val
          this.$refs.selectTree.setCurrentKey(selectKey)
          const node = this.$refs.selectTree.getNode(selectKey)
          const label = this.props.label || 'name'
          this.optionData.id = val
          this.optionData.name = node?.data[label]
        })
      } else {
        this.$refs.selectTree.setCurrentKey(null)
      }
    },
    visibleChange(e) {
      if (e) {
        let selectDom = document.querySelector('.is-current')
        if (selectDom === null) {
          selectDom = document.querySelector('.el-tree-node')
        }
        setTimeout(() => {
          this.$refs.select.scrollToOption({ $el: selectDom })
        }, 0)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.option {
  height: auto;
  line-height: 1;
  padding: 0;
  background-color: #2b2b45;
}
.tree {
  padding: 4px 20px;
  font-weight: 400;
}
</style>
