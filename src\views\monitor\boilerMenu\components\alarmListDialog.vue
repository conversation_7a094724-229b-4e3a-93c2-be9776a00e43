<template>
  <el-dialog
    v-if="alarmListDialogVisible"
    v-dialogDrag
    custom-class="polling-dialog"
    :modal="false"
    :close-on-click-modal="false"
    :visible.sync="alarmListDialogVisible"
    :before-close="() => alarmListDialogVisible = false"
  >
    <span slot="title">
      报警提醒
    </span>
    <div class="polling-content">
      <el-table
        v-loading="tableLoading"
        class="diablo-table"
        :data="tableData"
        height="100%"
        style="width: 100%"
        element-loading-background="rgba(0, 0, 0, 0.2)"
      >
        <el-table-column show-overflow-tooltip label="序号" width="80px">
          <template slot-scope="scope">
            <span>{{ scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="alarmStartTime" show-overflow-tooltip label="报警时间" width="220px"></el-table-column>
        <el-table-column prop="alarmObjectName" show-overflow-tooltip label="报警对象名称"></el-table-column>
        <el-table-column prop="iphPoliceLevel" show-overflow-tooltip label="严重等级">
          <template slot-scope="scope">
            <span :style="{ color: alarmLevelItem[scope.row.alarmLevel]?.color }">{{ alarmLevelItem[scope.row.alarmLevel]?.text }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="incidentName" show-overflow-tooltip label="事件类型"></el-table-column>
        <el-table-column prop="menuName" show-overflow-tooltip label="类型"></el-table-column>
        <el-table-column prop="alarmSpaceName" show-overflow-tooltip label="位置"></el-table-column>
        <el-table-column label="操作">
          <template>
            <el-button type="text" @click="toHandle">处理</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </el-dialog>
</template>

<script>
import moment from 'moment'
export default {
  data() {
    return {
      alarmLevelItem: {
        0: { text: '通知', color: '#3562DB' },
        1: { text: '一般', color: '#3562DB' },
        2: { text: '紧急', color: '#FF9435' },
        3: { text: '重要', color: '#FA403C' }
      },
      tableData: [],
      alarmListDialogVisible: false,
      tableLoading: false,
      projectCode: null
    }
  },
  methods: {
    toHandle() {
      this.$router.push({
        path: '/alarmRecord/index',
        query: {
          projectCode: this.projectCode
        }
      })
    },
    getOpenDialog(projectCode) {
      this.alarmListDialogVisible = true
      this.projectCode = projectCode
      let data = {
        startTime: moment().format('YYYY-MM-DD') + ' 00:00:00',
        endTime: moment().format('YYYY-MM-DD HH:mm:ss'),
        projectCode,
        alarmStatus: 0,
        pageNo: 1,
        pageSize: 10000
      }
      this.$api.selectAlarmRecord(data).then(res => {
        this.tableData = res.data
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .polling-dialog {
  width: 1200px;
  height: 550px;
  background: url("~@/assets/images/monitor/alarm-list-dialog.png") no-repeat;
  background-size: 100% 100%;

  .el-dialog__header {
    padding: 20px 10px 10px 26px;
    span {
      font-size: 20px;
      font-family: "HarmonyOS Sans SC-Medium", "HarmonyOS Sans SC";
      font-weight: 500;
      color: #dce9ff;
    }

    .el-dialog__headerbtn .el-dialog__close {
      color: #7cd0ff;
      font-size: 20px;
    }
  }
  .el-dialog__body{
    height: calc(100% - 56px);
    .polling-content {
      height: calc(100%);
      .diablo-table {
        height: calc(100%);
      }
    }
  }
  
}
</style>