<template>
  <el-dialog
    v-if="sceneDialogShow"
    :title="sceneDialogType == 'add' ? '新建时间表' : '编辑时间表'"
    :visible="sceneDialogShow"
    custom-class="scene-dialog"
    :before-close="closeDialog"
  >
    <div class="sceneForm_content" style="padding: 10px 20px 10px 10px;">
      <div class="form_row">
        <div class="form_row_label">时间表名称：</div>
        <div class="form_row_input">
          <el-input v-model="sceneForm.timeTableName" placeholder="请输入时间表名称"></el-input>
        </div>
      </div>
      <div class="form_row">
        <div class="form_row_label">时间表：</div>
        <div class="form_row_input">
          <el-button type="primary" plain @click="addTime"><i class="el-icon-plus"></i>新增</el-button>
        </div>
      </div>
      <div class="form_row">
        <div class="form_row_label"></div>
        <div class="form_row_input">
          <div class="form_row_input_box1">时间类型</div>
          <div class="form_row_input_box2">时间点</div>
          <div class="form_row_input_box3">开关控制</div>
          <div class="form_row_input_box4"></div>
        </div>
      </div>
      <div v-for="(item, index) in sceneForm.timeTableDataList" :key="index" class="form_row">
        <div class="form_row_label"></div>
        <div class="form_row_input">
          <div class="form_row_input_box form_row_input_box1">
            <el-select v-model="item.timeType" size="small" placeholder="请选择" @change="item.timePoint = ''">
              <el-option label="相对" :value="1"> </el-option>
              <el-option label="绝对" :value="2"> </el-option>
            </el-select>
          </div>
          <div class="form_row_input_box form_row_input_box2">
            <el-time-picker
              v-if="item.timeType != 1"
              v-model="item.timePoint"
              size="small"
              value-format="HH:mm"
              format="HH:mm"
              :picker-options="{
                selectableRange: `${nowTime}:00 - 23:59:59`
              }"
              placeholder="任意时间点"
            >
            </el-time-picker>
            <el-select v-else v-model="item.timePoint" size="small" placeholder="请选择">
              <el-option label="日出" value="日出"> </el-option>
              <el-option label="日落" value="日落"> </el-option>
            </el-select>
          </div>
          <div class="form_row_input_box form_row_input_box3">
            <el-select v-model="item.switchControl" size="small" placeholder="请选择">
              <el-option label="打开" :value="1"> </el-option>
              <el-option label="关闭" :value="2"> </el-option>
            </el-select>
          </div>
          <div class="form_row_input_box form_row_input_box4">
            <i class="el-icon-delete" style="font-size: 22px; line-height: 40px; cursor: pointer;" @click="deleteTime(index)"></i>
          </div>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button plain type="primary" @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="groupSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  name: 'sceneDialog',
  props: {
    page: {
      type: Number,
      default: 1
    },
    sceneDialogShow: {
      type: Boolean,
      default: false
    },
    sceneDialogType: {
      type: String,
      default: 'add'
    },
    dialogData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    patternId: {
      type: Number,
      default: Number
    },
    projectCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      nowTime: '00:00',
      sceneForm: {
        timeTableName: '',
        timeTableDataList: []
      }
    }
  },
  mounted() {
    if (this.sceneDialogType === 'add') {
      this.sceneForm.timeTableDataList.push({
        timePoint: '',
        timeType: '',
        switchControl: ''
      })
    } else {
      this.sceneForm = JSON.parse(JSON.stringify(this.dialogData))
    }
  },
  methods: {
    addTime() {
      this.sceneForm.timeTableDataList.push({
        timePoint: '',
        timeType: '',
        switchControl: ''
      })
    },
    deleteTime(index) {
      this.sceneForm.timeTableDataList.splice(index, 1)
    },
    closeDialog() {
      this.$emit('closeSceneDialog')
    },
    groupSubmit() {
      const param = JSON.parse(JSON.stringify(this.sceneForm))
      try {
        param.timeTableDataList.forEach((item) => {
          const fieldArr = ['timePoint', 'timeType', 'switchControl']
          fieldArr.forEach((field) => {
            if ([undefined, NaN, null, ''].includes(item[field])) {
              throw new Error()
            }
          })
        })
      } catch (error) {
        return this.$message.warning('请完善时间表信息!')
      }
      param.timeTable = JSON.stringify(param.timeTableDataList)
      param.patternId = this.patternId
      if (this.sceneDialogType === 'add') {
        param.type = 'add'
        if (this.page == 2) {
          param.id = new Date().getMilliseconds()
        }
      }
      // delete param.timeTableDataList
      param['projectCode'] = this.projectCode
      let header = {}
      if (param.type === 'add') {
        header = {
          'operation-type': 1
        }
      } else {
        header = {
          'operation-type': 2,
          'operation-id': param.id,
          'operation-name': param.timeTableName
        }
      }
      if (this.page == 1) {

        this.$api.saveTimeTable(param, header).then((res) => {
          if (res.code == 200) {
            this.$emit('sceneSubmit', true)
          }
        })
      } else {
        this.$api.saveTimeTableToRedis({ timeTable: JSON.stringify(param), projectCode: this.projectCode }, header).then((res) => {
          if (res.code == 200) {
            this.$emit('sceneSubmit', param)
          }
        })
      }
    }
  }
}
</script>
<style src="@/assets/styles/sinomis-ui.css" scoped></style>
<style lang="scss" scoped>
.el-select {
  width: 100%;
}

.scene-dialog {
  width: 30% !important;
  height: 30% !important;
  min-width: 562px !important;
  min-height: 376px !important;

  .sceneForm_content {
    // .el-form-item {
    //   width: inherit;
    // }
    .form_row {
      display: flex;
      height: 40px;
      line-height: 40px;
      padding-right: 30px;
      margin-bottom: 10px;

      .form_row_label {
        min-width: 100px;
        text-align: right;
        margin-right: 10px;
        font-size: 14px;
        font-family: NotoSansHans-Medium, NotoSansHans;
        color: #606266;
      }

      .form_row_input {
        flex: 1;
        display: flex;

        .form_row_input_box {
          padding-right: 15px;
          box-sizing: border-box;
        }

        .form_row_input_box1 {
          width: 25%;
        }

        .form_row_input_box2 {
          width: 35%;

          .el-date-editor.el-input,
          .el-date-editor.el-input__inner {
            width: 100%;
          }
        }

        .form_row_input_box3 {
          width: 25%;
        }

        .form_row_input_box4 {
          width: 15%;
        }
      }
    }
  }
}
</style>
