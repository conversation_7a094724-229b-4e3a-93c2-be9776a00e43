<template>
  <div class="summaryAnalysis">
    <el-row type="flex">
      <el-col :span="12">
        <div class="label">分析时间：</div>
        <div class="value">{{ data.createTime }}</div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <div class="label">分析文件：</div>
        <div class="value">
          <div v-for="(item, index) in translateFileList(data.fileUrl)" :key="index" class="fileItem" @click="preview(item)">
            {{ item.name }}<i class="el-icon-download" @click="downLoadFile(item)"></i>
          </div>
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <div class="label">总结分析：</div>
        <div class="value">
          {{ data.summaryAnalysis }}
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <div class="label">院内参与人员：</div>
        <div class="value">{{ data.hospitalPerson }}</div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <div class="label">其他参与人员：</div>
        <div class="value">{{ data.otherPerson }}</div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <div class="label">处理人：</div>
        <div class="value">{{ data.createName }}</div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
export default {
  props: {
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      fileList: []
    }
  },
  methods: {
    translateFileList(arr) {
      if (arr) {
        return JSON.parse(arr)
      }
    },
    preview(item) {
      window.open(item.url)
    },
    downLoadFile(item) {
      window.open(item.url)
    }
  }
}
</script>
<style lang="scss" scoped>
.el-row {
  margin-top: 24px;
  margin-bottom: 24px;
  padding: 0 24px;
  &:last-child {
    margin-bottom: 0;
  }
  .el-col {
    display: flex;
  }
}
.label {
  display: inline-block;
  width: 100px;
  color: #96989a;
  text-align: right;
}
.value {
  max-width: calc(100% - 100px);
  .fileItem {
    width: 100%;
    margin-bottom: 10px;
    &:last-child {
      margin-bottom: 0;
    }
    i {
      color: #3562db;
      margin-left: 10px;
      cursor: pointer;
    }
  }
}
</style>
