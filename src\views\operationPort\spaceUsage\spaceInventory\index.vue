<template>
  <PageContainer>
    <div slot="header" class="control-btn-header">
      <div class="search-from">
        <el-select v-model="searchFrom.inventoryStatus" placeholder="清查任务状态" clearable>
          <el-option v-for="item in inventoryStatusArr" :key="item.status" :label="item.name" :value="item.status"> </el-option>
        </el-select>
        <el-input v-model="searchFrom.inventoryTaskName" placeholder="清查任务名称" clearable style="width: 200px;"></el-input>
        <div style="flex: 1;display: flex;justify-content: space-between;">
          <div>
            <el-button type="primary" plain @click="resetForm">重置</el-button>
            <el-button type="primary" @click="searchForm">查询</el-button>
          </div>
          <el-button type="primary" icon="el-icon-plus" @click="control('add')">新增</el-button>
        </div>
      </div>

    </div>
    <div slot="content" class="table-content">
      <el-row :gutter="16" class="state_sum">
        <el-col :span="8">
          <div class="state_sum_item" style="background: #3562DB;">
            <div class="state_sum_item_left">
              <p>{{status_num.ing}}</p>
              <span>进行中</span>
            </div>
            <img src="@/assets//images/space/statistics1.png" alt="">
          </div>
        </el-col>
        <el-col :span="8">
          <div class="state_sum_item" style="background: #00BC6D;">
            <div class="state_sum_item_left">
              <p>{{status_num.noStart}}</p>
              <span>未开始</span>
            </div>
            <img src="@/assets//images/space/statistics2.png" alt="">
          </div>
        </el-col>
        <el-col :span="8">
          <div class="state_sum_item" style="background: #FF9435;">
            <div class="state_sum_item_left">
              <p>{{status_num.endTotal}}</p>
              <span>已结束</span>
            </div>
            <img src="@/assets//images/space/statistics3.png" alt="">
          </div>
        </el-col>
        <!-- <p style="color:#00CC8F">进行中<span>{{status_num.ing}}</span></p> -->
        <!-- <p style="color:#F2CC1F">已结束<span>{{status_num.endTotal}}</span></p> -->
        <!-- <p style="color:#688DFF">未开始<span>{{status_num.noStart}}</span></p> -->
      </el-row>
      <TablePage
        ref="table"
        v-loading="tableLoading"
        :showPage="true"
        stripe
        :tableColumn="tableColumn"
        :data="tableData"
        height="calc(100% - 150px)"
        :pageData="pageData"
        :pageProps="pageProps"
        @pagination="paginationChange"
        @row-dblclick="control('detail', $event)"
      />
    </div>
  </PageContainer>
</template>
<script lang="jsx">
export default {
  name: 'spaceInventory',
  components: {

  },
  beforeRouteEnter(to, from, next) {
    let names = []
    to.matched.map((v, i) => {
      if (i > 0) {
        v.components.default.name && names.push(v.components.default.name)
      }
    })
    // 进入页面时，先将当前页面的 name 信息存入 keep-alive 全局状态
    setTimeout(() => {
      next((vm) => {
        vm.$store.commit('keepAlive/add', names)
      })
    }, 0)
  },
  async beforeRouteLeave(to, from, next) {
    // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
    if (!['addInventory', 'inventoryDetails'].includes(to.name)) {
      // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      srcList: [],
      tableLoading: false,
      searchFrom: {
        inventoryTaskName: null, // 清查任务名称
        inventoryStatus: null    // 盘点单状态，1-未开始，2-进行中，3-已结束
      },
      inventoryStatusArr: [
        {
          name: '未开始',
          status: 1
        },
        {
          name: '进行中',
          status: 2
        },
        {
          name: '已结束',
          status: 3
        }
      ],
      tableColumn: [
        {
          prop: '',
          width: 80,
          label: '序号',
          formatter: (scope) => {
            return (this.pageData.currentPage - 1) * this.pageData.pageSize + scope.$index + 1
          }
        },
        {
          prop: 'inventoryTaskName',
          label: '清查任务名称'
        },
        {
          prop: 'inventoryTaskNo',
          label: '清查任务编码'
        },
        {
          prop: 'finishDegree',
          label: '完成度',
          formatter: (scope) => {
            return `${scope.row.finishDegree}%`
          }
        },
        {
          prop: 'inventoryData',
          label: '已确认/总数量'
        },
        {
          prop: 'taskStartTime',
          label: '任务计划期限',
          width: 200,
          formatter: (scope) => {
            return `${scope.row.taskStartTime}~${scope.row.taskEndTime}`
          }
        },
        {
          prop: 'responsibilityPerson',
          label: '责任人'
        },
        {
          prop: 'inventoryType',
          label: '清查类型',
          formatter: (scope) => {
            if (scope.row.inventoryType == 1) {
              return '所有空间'
            } else if (scope.row.inventoryType == 2) {
              return '按科室清查 '
            } else {
              return ''
            }
          }
        },
        {
          prop: 'inventoryStatus',
          label: '清查任务状态',
          render: (h, scope) => {
            return (
              <div class="operationBtn">
                {scope.row.inventoryStatus == 1
                  ? (
                    <span class="status_btn_space" style="background: #FFF7E8;color: #D25F00;">未开始</span>
                  ) :
                  scope.row.inventoryStatus == 2
                    ?
                    (
                      <span class="status_btn_space" style="background: #E6EFFC; color: #2749BF;">进行中</span>
                    )
                    : (
                      <span class="status_btn_space" style="background: #F2F4F9;color: #86909C">已结束</span>
                    )}
              </div>
            )
          }
        },
        {
          width: 150,
          prop: 'operation',
          label: '操作',
          render: (h, row) => {
            return (
              <div class="">
                <elButton type='text' disabled={row.row.inventoryStatus != 1} style={row.row.inventoryStatus != 1 ? 'color: #A6A6A6 !important' : ''} onClick={() => this.control('edit', row.row)}>编辑</elButton>
                <elButton type='text' onClick={() => this.control('del', row.row)}>删除</elButton>
                <elButton type='text' disabled={row.row.inventoryStatus != 2} style={row.row.inventoryStatus != 2 ? 'color: #A6A6A6 !important' : ''} onClick={() => this.control('complete', row.row)}>完成</elButton>
              </div>
            )
          }
        }
        // {
        //   width: 150,
        //   prop: 'operation',
        //   label: '操作',
        //   render: (h, row) => {
        //     return (
        //       <div class="">
        //         <elButton type='text' disabled={row.row.inventoryStatus != 1} style={row.row.inventoryStatus != 1 ? 'color: #A6A6A6 !important' : 'color: #121F3E !important'} onClick={() => this.control('edit', row.row)}>编辑</elButton>
        //         <elButton type='text' style="color: #fa403c !important" onClick={() => this.control('del', row.row)}>删除</elButton>
        //         <elButton type='text' disabled={row.row.inventoryStatus != 2} style={row.row.inventoryStatus != 2 ? 'color: #A6A6A6 !important' : 'color: #00CC8F !important'} onClick={() => this.control('complete', row.row)}>完成</elButton>
        //       </div>
        //     )
        //   }
        // }
      ],
      tableData: [],
      pageData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      pageProps: {
        page: 'currentPage',
        pageSize: 'pageSize',
        total: 'total'
      },
      status_num: {}
    }
  },
  watch: {

  },
  activated() {
    this.getApplicationList()
  },
  mounted() {
    this.getQueryInventoryTotal()
    this.getApplicationList()
  },
  methods: {
    getQueryInventoryTotal() {
      this.$api.queryInventoryTotal().then(res => {
        this.status_num = res.data
      })
    },
    // 查询
    searchForm() {
      this.pageData.currentPage = 1
      this.getApplicationList()
    },
    // 重置查询
    resetForm() {
      this.pageData.currentPage = 1
      Object.assign(this.$data.searchFrom, this.$options.data().searchFrom)
      this.searchForm()
    },
    control(type, row) {
      if (['add', 'edit'].includes(type)) {
        this.$router.push({
          path: '/spaceUsage/spaceInventory/addInventory',
          query: {
            type,
            id: row?.id ?? ''
          }
        })
      } else if (type == 'detail') {
        this.$router.push({
          path: '/spaceUsage/spaceInventory/inventoryDetails',
          query: {
            type,
            id: row?.id ?? ''
          }
        })
      } else if (type == 'del') {  // 删除
        this.$confirm('数据删除后将无法恢复，确定要删除吗？', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$api.deleteInventoryTaskById({inventoryId: row.id}, { 'operation-type': 3, 'operation-name': row.inventoryTaskName, 'operation-id': row.id}).then(res => {
            if (res.code == 200) {
              this.$message({ message: '删除成功', type: 'success'})
              this.isLastPage(1)
              this.searchForm()
            } else {
              this.$message({ message: res.msg, type: 'error'})
            }
          })
        })
      } else if (type == 'complete') {  // 完成
        this.$confirm(`确定要把“${row.inventoryTaskName}“标记为完成吗？`, '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$api.updateInventoryTaskStatusById({inventoryId: row.id, status: 3}).then(res => {
            if (res.code == 200) {
              this.$message({ message: '操作成功', type: 'success'})
              this.searchForm()
            } else {
              this.$message({ message: res.msg, type: 'error'})
            }
          })
        })
      }
    },
    // 获取应用列表
    getApplicationList() {
      let param = {
        ...this.searchFrom,
        pageParams: {
          currentPage: this.pageData.currentPage,
          pageSize: this.pageData.pageSize
        }
      }
      this.tableLoading = true
      this.$api.queryInventoryTaskListByPage(param).then(res => {
        if (res.code == 200) {
          this.tableData = res.data.records
          this.pageData.total = res.data.total
        }
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    // 判断当前页是否是最后一页
    isLastPage (deleteNum) {
      let deleteAfterPage = Math.ceil((this.pageData.total - deleteNum) / this.pageData.size)
      let currentPage = this.pageData.current > deleteAfterPage ? deleteAfterPage : this.pageData.current
      this.pageData.current = currentPage < 1 ? 1 : currentPage
    },
    paginationChange(pagination) {
      // this.pageData.size = pagination.pageSize
      // this.pageData.current = pagination.page
      Object.assign(this.pageData, pagination)
      this.getApplicationList()
    }
  }
}
</script>
<style lang="scss" scoped>
.control-btn-header {
  padding: 10px !important;
  .search-from {
    display: flex;
    & > div {
      margin-right: 10px;
    }
  }
}

.table-content {
  margin-top: 15px;
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 15px);
  padding: 10px;
  .state_sum{
    margin-bottom: 16px;
    &_item{
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 14px 24px;
      border-radius: 4px;
      &_left{
        p {
          font-size: 30px;
          font-family: Arial-Bold, Arial;
          font-weight: bold;
          color: #FFFFFF;
          margin: 0;
        }
        span{
          font-size: 15px;
          font-family: PingFang SC-Medium, PingFang SC;
          font-weight: 500;
          color: #FFFFFF;
        }
      }
      img{
        width: 40px;
        height: 40px;
      }
    }
  }
}
</style>
<style lang="scss">
.operationBtn-span {
  margin-right: 10px;
}
.status_btn_space{
  padding: 6px 10px;
  border-radius: 4px;
  color: white;
  background:rgba(217,0,27,.1);
}
</style>
