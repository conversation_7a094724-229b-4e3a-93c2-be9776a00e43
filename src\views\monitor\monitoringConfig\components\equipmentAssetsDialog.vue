<template>
  <el-dialog v-if="dialogShow" title="选择关联设备资产" width="60%" :visible.sync="dialogShow" custom-class="model-dialog" :before-close="closeDialog">
    <div class="camera_content" style="padding: 10px 20px 10px 10px;">
      <div class="header_operation">
        <div class="search_box">
          <div class="search_select">
            <el-input v-model="assetsData.assetName" placeholder="设备名称" style="width: 200px;"></el-input>
            <el-input v-model="assetsData.assetCode" placeholder="设备编码" style="width: 200px; margin-left: 10px;"></el-input>
          </div>
        </div>
        <div class="header_btn">
          <el-button type="primary" plain @click="assetsResetting">重置</el-button>
          <el-button type="primary" @click="getAssetsListData">查询</el-button>
        </div>
      </div>
      <div class="table_div">
        <TablePage
          ref="multipleAssetsTable"
          v-loading="tableLoading"
          row-key="assetsId"
          :showPage="true"
          :tableColumn="tableColumn"
          :data="tableData"
          height="300px"
          :pageData="pageinationData"
          @pagination="paginationChange"
        >
        </TablePage>
        <!-- highlight-current-row -->
        <!-- @current-change="assetsdialogCheck" -->
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="groupSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script lang="jsx">
export default {
  name: 'equipmentAssetsDialog',
  props: {
    dialogShow: {
      type: Boolean,
      default: false
    },
    dialogData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      checkRadio: '',
      assetsData: {
        assetName: '',
        assetCode: ''
      },
      tableColumn: [
        {
          label: '',
          width: 80,
          align: 'center',
          render: (h, row) => {
            return (
              <el-radio
                v-model={this.checkRadio}
                label={row.row.assetsId}
                onChange={() => {
                  this.assetsdialogCheck(row.row)
                }}
              >
                &nbsp;
              </el-radio>
            )
          }
        },
        {
          prop: 'serialNumber',
          label: '序号',
          width: 80,
          formatter: (scope) => {
            // 当前分页重置
            // return scope.$index + 1
            // 跨分页序号
            return (this.pageinationData.page - 1) * this.pageinationData.pageSize + scope.$index + 1
          }
        },
        {
          prop: 'assetName',
          label: '设备名称'
        },
        {
          prop: 'assetCode',
          label: '设备编码'
        },
        {
          prop: 'professionalCategoryName',
          label: '专业类别'
        },
        {
          prop: 'systemCategoryName',
          label: '系统类别'
        },
        {
          prop: 'modelCode',
          label: '模型编码'
        },
        {
          prop: 'regionName',
          label: '所在区域'
        }
      ],
      tableData: [],
      tableLoading: false,
      assetsCheckData: {}, // 选中数据
      pageinationData: {
        page: 1,
        pageSize: 15,
        total: 0
      }
    }
  },
  mounted() {
    Object.assign(this.assetsCheckData, this.dialogData)
    if (this.assetsCheckData.assetsId) {
      this.checkRadio = this.assetsCheckData.assetsId
    }
    this.getAssetsListData()
  },
  methods: {
    // 资产重置
    assetsResetting() {
      Object.assign(this.assetsData, {
        assetName: '',
        assetCode: ''
      })
      this.getAssetsListData()
    },
    // 关联资产
    getAssetsListData() {
      let data = {
        ...this.assetsData,
        currentPage: this.pageinationData.page,
        pageSize: this.pageinationData.pageSize,
        professionalCategoryCode: '', // 专业类别
        assetCategoryCode: '', // 资产大类
        assetSubcategoryCode: '' // 资产小类
      }
      this.tableLoading = true
      this.$api.getAssetList(data).then((res) => {
        this.tableLoading = false
        if (res.code == '200') {
          this.tableData = res.data.assetDetailsList
          this.pageinationData.total = parseInt(res.data.sum)
          // 选中数据回显
          if (this.assetsCheckData?.assetsId) {
            let selectRow = this.tableData.find((e) => e.assetsId == this.assetsCheckData.assetsId)
            if (selectRow) {
              this.$refs.multipleAssetsTable.setCurrentRow(selectRow)
            }
          }
        }
      })
    },
    assetsdialogCheck(currentRow) {
      this.assetsCheckData = currentRow ?? this.assetsCheckData
    },
    paginationChange(pagination) {
      Object.assign(this.pageinationData, pagination)
      this.getAssetsListData()
    },
    closeDialog() {
      this.$emit('closeDialog')
    },
    groupSubmit() {
      if (Object.keys(this.assetsCheckData).length) {
        this.$emit('submitDialog', {
          assetsId: this.assetsCheckData.assetsId,
          assetName: this.assetsCheckData.assetName,
          modelCode: this.assetsCheckData.modelCode,
          regionName: this.assetsCheckData.regionName,
          regionCode: this.assetsCheckData.regionCode,
          assetsNumber: this.assetsCheckData.assetCode,
          assetTypeId: this.assetsCheckData.systemCategoryCode,
          assetTypeName: this.assetsCheckData.systemCategoryName,
          assetCategoryName: this.assetsCheckData.professionalCategoryName,
          assetCategoryCode: this.assetsCheckData.professionalCategoryCode
        })
      } else {
        this.$message({
          message: '请选择关联设备！',
          type: 'warning'
        })
      }
    }
  }
}
</script>
<style lang="scss" scope>
.model-dialog {
  // width: 60%;
  .camera_content {
    width: 100%;
    margin: 0 auto;
    background: #fff;
    border-radius: 4px;
    // display: flex;
    width: 100%;
    height: 100%;
    .header_operation {
      // padding: 24px;
      margin-bottom: 10px;
      display: flex;
      .search_box {
        display: flex;
        > div {
          margin-right: 16px;
        }
        .search_input {
          width: 200px;
        }
      }
    }
    .table_div {
      height: calc(100% - 100px);
    }
    .paging_box {
      display: flex;
      justify-content: flex-end;
      padding: 6px;
    }
  }
}
</style>
