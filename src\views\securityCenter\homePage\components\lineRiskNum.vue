<template>
  <div ref="lineRiskNum" style="height: 100%; width: 100%;"></div>
</template>

<script>
import * as echarts from 'echarts'
export default {
  name: 'lineRiskNum',
  props: {
    lineData: {
      type: Object,
      default() {
        return {}
      }
    },
    // eslint-disable-next-line vue/require-prop-type-constructor
    deep: true
  },
  data() {
    return {
      data: ''
    }
  },
  watch: {
    lineData(val) {
      this.data = val
      this.lineCharts()
    }
  },
  methods: {
    lineCharts() {
      let myChart = echarts.init(this.$refs.lineRiskNum)
      var option = {
        color: ['#FA403C', '#FF9435'],
        tooltip: {
          show: true,
          trigger: 'axis'
        },
        legend: {
          data: this.data.IData
          // data: ['Email', 'Union Ads', 'Video Ads', 'Direct', 'Search Engine']
        },
        grid: {
          left: '2%',
          right: '3%',
          bottom: '50',
          top: '15',
          containLabel: true
        },
        dataZoom: [
          {
            fillerColor: '#BBC3CE',
            backgroundColor: '#fff',
            height: 10,
            type: 'slider',
            bottom: 10,
            textStyle: {
              color: '#000'
            },
            start: 0,
            end: 85
          },
          {
            type: 'inside', // 支持内部鼠标滚动平移
            start: 0,
            end: 85,
            zoomOnMouseWheel: false, // 关闭滚轮缩放
            moveOnMouseWheel: true, // 开启滚轮平移
            moveOnMouseMove: true // 鼠标移动能触发数据窗口平移
          }
        ],
        xAxis: [
          {
            type: 'category',
            data: this.data.xAxis,
            axisLabel: {
              color: function (value, index) {
                return value == time ? '#21aced' : '#909399'
              },
              clickable: true
              // show: true,
              // rotate: 0,
              // // interval:5,
              // textStyle: {
              //   color: '#909399' // 更改坐标轴文字颜色
              // }
            },
            axisLine: {
              lineStyle: {
                color: '#D8DEE7' // 更改坐标轴颜色
              }
            },
            // 去除x轴上的刻度线
            axisTick: {
              show: false
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '个',
            axisLabel: {
              formatter: '{value}',
              show: true,
              textStyle: {
                color: '#909399' // 更改坐标轴文字颜色
              }
            },
            // 控制y轴线是否显示
            axisLine: {
              lineStyle: {
                color: '#D8DEE7' // 更改坐标轴颜色
              }
            },
            // 网格样式
            splitLine: {
              show: true,
              lineStyle: {
                //                color: ['rgba(255,255,255,.3)'],
                color: ['#f5f5f5'],
                width: 1,
                type: 'dashed'
              }
            },
            // 去除y轴上的刻度线
            axisTick: {
              show: false
            }
          }
        ],
        series: [
          {
            symbol: 'none',
            name: this.data.array[0].name,
            data: this.data.array[0].data,
            type: 'line'
          },
          {
            symbol: 'none',
            name: this.data.array[1].name,
            data: this.data.array[1].data,
            type: 'line'
          }
        ]
      }
      let time = ''
      // 点击x轴线
      myChart.getZr().on('click', (params) => {
        const pointInPixel = [params.offsetX, params.offsetY]
        if (myChart.containPixel('grid', pointInPixel)) {
          const xIndex = myChart.convertFromPixel({ seriesIndex: 0 }, [params.offsetX, params.offsetY])[0]
          time = option.xAxis[0].data[xIndex]
          myChart.resize()
          this.$emit('getHiddenDate', time)
        }
      })
      // 点击x轴坐标
      // myChart.on('click', 'xAxis.category', function (params, node) {
      //   time = params.value
      //   myChart.resize()
      //   console.log(time)
      // })
      myChart.clear()
      myChart.setOption(option) // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    }
  }
}
</script>

<style></style>
