<template>
  <div ref="tab_content" class="tab_content">
    <div ref="left" class="tab_content_left">
      <VueDragResize
        ref="VueDragResize"
        class="VueDragResize"
        :isActive="true"
        :isDraggable="false"
        :minw="200"
        :minh="1"
        :w="width"
        :h="1"
        preventActiveBehavior
        parentLimitation
        :parentW="parentW"
        :aspectRatio="false"
        :sticks="['mr']"
        @resizing="handleResize"
        @clicked="handleClicked"
      >
        <ContentCard title="目录" :cstyle="{ height: '100%' }">
          <template slot="content">
            <div class="filter" style="margin-bottom: 16px">
              <el-input v-model="filterText" suffix-icon="el-icon-search" placeholder="请输入"></el-input>
            </div>
            <el-tree
              ref="tree"
              :data="treeData"
              node-key="id"
              size="small"
              :highlight-current="true"
              :props="defaultProps"
              :filter-node-method="filterNode"
              :expand-on-click-node="false"
              check-on-click-node
              draggable
              default-expand-all
              @node-click="nodeClick"
            >
              <span slot-scope="{ node, data }" class="custom-tree-node">
                <span class="tree-label" :class="[!isMy ? 'show-label' : '']">
                  <span v-showtipPlus="`${data.label}（${data.count}）`"></span>
                </span>
                <span v-if="!isMy" class="tree-btn" :style="{ width: node.level === 1 && '20px' }">
                  <span v-auth="'dossierManager:addFolder'" @click="handleAddFolder(data)">
                    <i class="icon el-icon-plus" style="margin-bottom: 12px"></i>
                  </span>
                  <span v-if="node.level !== 1" v-auth="'dossierManager:editFolder'" @click="handleEdit(data)">
                    <i class="icon el-icon-edit"></i>
                  </span>
                  <span v-if="node.level !== 1" v-auth="'dossierManager:deleteFolder'" @click="handleDelete(data)">
                    <i class="icon el-icon-delete"></i>
                  </span>
                </span>
              </span>
            </el-tree>
          </template>
        </ContentCard>
      </VueDragResize>
    </div>
    <div ref="right" class="tab_content_right">
      <div class="right_title">全部：</div>
      <div class="right-heade">
        <el-input v-model="searchForm.archiveInfo" placeholder="文件名/文号/摘要" suffix-icon="el-icon-search" clearable />
        <el-cascader
          v-if="!isMy"
          v-model="searchForm.archiveOwnerDeptId"
          placeholder="所属部门"
          :options="deptList"
          :props="{
            value: 'id',
            label: 'deptName',
            checkStrictly: true,
            emitPath: false
          }"
          clearable
          filterable
          size="small"
        >
        </el-cascader>
        <VirtualListSelect
          v-if="!isMy"
          v-model="searchForm.archiveOwnerId"
          placeholder="所有者"
          clearable
          :options="ownerList"
          :propsOptions="{
            label: 'staffName',
            value: 'id'
          }"
        />
        <el-button type="primary" @click="search">查询</el-button>
        <el-button type="primary" plain @click="reset">重置</el-button>
        <div>
          <el-button v-auth="'dossierManager:create'" size="small" type="primary" @click="handleAdd">创建</el-button>
          <!-- <el-button size="small" type="primary" @click="uploadFile">批量创建</el-button>  -->
          <el-button size="small" type="primary" @click="uploadFile">下载</el-button>
          <template v-if="!isMy">
            <el-button v-auth="'dossierManager:delete'" size="small" type="danger" @click="batchDel">删除</el-button>
          </template>
          <el-button v-else size="small" type="danger" @click="batchDel">删除</el-button>
        </div>
      </div>
      <div class="right-content">
        <div class="table-content">
          <TablePage
            ref="tablePage"
            v-loading="tableLoading"
            v-scrollHideTooltip
            :tableColumn="tableColumn"
            :data="tableData"
            border
            :height="`calc(100% - ${height}px)`"
            :showPage="true"
            :pageData="pageData"
            :pageProps="{
              page: 'current',
              pageSize: 'size',
              total: 'total'
            }"
            @pagination="paginationChange"
            @selection-change="handleSelectionChange"
          >
          </TablePage>
        </div>
      </div>
    </div>
    <CreateFolder v-if="visible" :visible.sync="visible" :currentNode="currentNode" :type="editType" @success="handleGetTreeData" />
    <ViewAttachments :id="current.archiveId" :visible.sync="viewVisible" />
    <FileSharing :visible.sync="sharevisible" :current="current" @success="getTableList" />
  </div>
</template>
<script lang="jsx">
import tableListMixin from '@/mixins/tableListMixin.js'
import VueDragResize from 'vue-drag-resize'
import CreateFolder from './CreateFolder.vue'
import ViewAttachments from './ViewAttachments.vue'
import FileSharing from './FileSharing.vue'
import moment from 'moment'
import { transData, auth } from '@/util'
import dictMixin from '@/views/operationPort/contractManagement/mixins/index.js'
export default {
  name: 'MyDoc',
  components: { VueDragResize, CreateFolder, ViewAttachments, FileSharing },
  mixins: [tableListMixin, dictMixin],
  data() {
    return {
      moment,
      filterText: '',
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'label',
        value: 'id'
      },
      tableColumn: [
        {
          type: 'selection',
          align: 'center',
          width: '50'
        },
        {
          prop: 'archiveName',
          label: '文件名称',
          align: 'center'
        },
        {
          prop: 'archiveNumber',
          label: '文号',
          align: 'center'
        },
        {
          prop: 'folderName',
          label: '文件夹',
          align: 'center'
        },
        {
          prop: 'archiveModel',
          label: '分类',
          align: 'center',
          render: (h, row) => {
            const item = this.classification.find((item) => item.value === row.row.archiveModel)
            return item ? item.label : ''
          }
        },
        {
          prop: 'archiveDate',
          label: '成文日期',
          align: 'center',
          render: (h, row) => {
            return row.row.archiveDate ? this.moment(row.row.archiveDate).format('YYYY-MM-DD') : ''
          }
        },
        {
          prop: 'archiveOwnerName',
          label: '所有者',
          align: 'center'
        },
        {
          prop: 'archiveOwnerDeptName',
          label: '所有者部门',
          align: 'center'
        },
        {
          prop: 'remark',
          label: '摘要信息',
          align: 'center'
        },
        {
          width: '180px',
          prop: 'handler',
          label: '操作',
          render: (h, row) => {
            return (
              <div class="operationBtn" style="display: flex;">
                <span class="operationBtn-span" style="color: #3562DB" onClick={() => this.handleViewArchives(row.row)}>
                  查看附件
                </span>
                {this.isMy ? (
                  <div>
                    <span class="operationBtn-span" style="color: #3562DB" onClick={() => this.handleShare(row.row)}>
                      共享
                    </span>
                    <el-dropdown onCommand={(val) => this.handleGotoDetail(row.row)}>
                      <span v-auth={['fileManager:export', 'fileManager:delete']} class="el-dropdown-link" style="color: #3562DB">
                        更多
                      </span>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item command="detail">详情</el-dropdown-item>
                        <el-dropdown-item command="edit">编辑</el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                  </div>
                ) : (
                  <div>
                    <span class="operationBtn-span" style="color: #3562DB" onClick={() => this.handleGotoDetail(row.row)}>
                      详情
                    </span>
                    {auth('dossierManager:edit') ? (
                      <span class="operationBtn-span" style="color: #3562DB" onClick={() => this.handleGotoDetail(row.row)}>
                        编辑
                      </span>
                    ) : (
                      ''
                    )}
                  </div>
                )}
              </div>
            )
          }
        }
      ],
      searchForm: {
        archiveInfo: '',
        archiveOwnerId: '',
        archiveOwnerDeptId: ''
      },
      width: 270,
      parentW: 0,
      sizeInfo: {},
      tableLoading: false,
      tableData: [],
      pageData: {
        current: 1,
        size: 15,
        total: 0
      },
      selectionList: [],
      height: 0,
      currentNode: null,
      editType: '0',
      visible: false,
      viewVisible: false,
      current: {},
      checkedTreeNode: {},
      sharevisible: false,
      ownerList: [],
      deptList: []
    }
  },
  computed: {
    isMy() {
      return this.$route.path === '/dossierManager/myDossier'
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },
  mounted() {
    this.handleGetTreeData()
    if (!this.isMy) {
      this.getLersonnelList()
      this.getDeptList()
    }
    const { width } = this.$refs.tab_content.getBoundingClientRect()
    this.parentW = width * 0.6
    window.addEventListener('resize', this.handleResize)
  },
  methods: {
    // 获取部门列表
    getDeptList() {
      this.$api.getSelectedDept().then((res) => {
        if (res.code == '200') {
          this.deptList = transData(res.data, 'id', 'pid', 'children')
        }
      })
    },
    // 获取人员列表
    getLersonnelList() {
      let params = {
        current: 1,
        size: 99999
      }
      this.$api.staffList(params).then((res) => {
        if (res.code == 200) {
          this.ownerList = res.data.records
        }
      })
    },
    handleViewArchives(row) {
      this.current = row
      this.viewVisible = true
    },
    handleGetTreeData() {
      const isMine = this.isMy
      this.$api.fileManagement.selectFolderTree({ folderType: '1', isMine }).then((res) => {
        this.treeData = res.data
        this.checkedTreeNode = res.data[0]
        this.getTableList()
        this.$nextTick(() => {
          this.$refs.tree.setCurrentKey(res.data[0].id)
        })
      })
    },
    handleGotoDetail(row) {
      const prefix = this.isMy ? 'my' : 'all'
      this.$router.push(`/dossierManager/${prefix}DocumentDetails?id=${row.archiveId}`)
    },
    handleAdd() {
      const prefix = this.isMy ? 'my' : 'all'
      this.$router.push(`/dossierManager/${prefix}CreateDocuments`)
    },
    handleAddFolder(data) {
      this.visible = true
      this.editType = '0'
      this.currentNode = data
    },
    handleEdit(data) {
      this.visible = true
      this.editType = '1'
      this.currentNode = data
    },
    handleShare(data) {
      this.current = data
      this.sharevisible = true
    },
    handleDelete(data) {
      this.$confirm('是否删除所选文件夹?', '删除文件夹', {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$api.fileManagement.deleteFolder({ id: data.id }).then((res) => {
            if (res.code === '200') {
              this.$message.success('删除成功')
              this.handleGetTreeData()
            } else {
              this.$message.error(res.msg)
            }
          })
        })
        .catch(() => {})
    },
    // 解决input无法获取焦点的问题
    handleClicked(e) {
      e.target.focus()
    },
    handleResize(e) {
      const { width } = e instanceof Event ? this.sizeInfo : e
      this.sizeInfo = { width }
      const { width: allWidth } = this.$refs.tab_content.getBoundingClientRect()
      const $rightHeade = this.$refs.right.getElementsByClassName('right-heade')[0]
      const { height } = $rightHeade.getBoundingClientRect()
      this.height = height - 84
      const leftWidth = ((width / allWidth) * 100).toFixed(2)
      const rightWidth = 100 - leftWidth
      this.$refs.left.style.width = `${Number(leftWidth) + 1}%`
      this.$refs.right.style.width = `calc(${rightWidth}% - 30px)`
    },
    filterNode(value, data) {
      if (!value) return true
      return data[this.defaultProps.label].indexOf(value) !== -1
    },
    nodeClick(data) {
      this.checkedTreeNode = data
      this.getTableList()
    },
    getTableList() {
      const isMine = this.isMy
      const folderId = this.checkedTreeNode.id
      this.$api.fileManagement
        .queryByPage({
          archiveType: '1',
          isMine,
          folderId,
          ...this.searchForm,
          ...this.pageData
        })
        .then((res) => {
          this.tableData = res.data.records
          this.pageData.total = res.data.total
        })
    },
    search() {
      this.pageData.current = 1
      this.getTableList()
    },
    // 分页事件
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getTableList()
    },
    // 重置
    reset() {
      Object.keys(this.searchForm).forEach((key) => {
        this.searchForm[key] = ''
      })
      this.search()
    },
    downloadFile(res) {
      const downloadElement = document.createElement('a')
      const href = window.URL.createObjectURL(res.data) // 创建下载的链接
      downloadElement.style.display = 'none'
      downloadElement.href = href
      downloadElement.download = 'files.zip' // 下载后文件名
      document.body.appendChild(downloadElement)
      downloadElement.click() // 点击下载
      document.body.removeChild(downloadElement) // 下载完成移除元素
      window.URL.revokeObjectURL(href) // 释放掉blob对象
    },
    uploadFile() {
      const rows = this.selectionList
      if (rows.length < 1) {
        this.$message.warning('请至少选择一条数据！')
        return
      }
      const ids = rows.map((item) => item.archiveId).join(',')
      this.$api.fileManagement.downloadArchiveFile({ ids, isArchive: true }).then((res) => {
        this.downloadFile(res)
      })
    },
    batchDel() {
      const rows = this.selectionList
      if (rows.length < 1) {
        this.$message.warning('请至少选择一条数据！')
        return
      }
      const message = rows.map((item) => item.archiveName).join('、')
      this.$confirm('确认删除“' + message + '”等数据吗？', '批量删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.tableLoading = true
          const idList = rows.map((v) => v.archiveId)
          this.$api.fileManagement
            .updateDeleteArchiveB({ idList })
            .then((res) => {
              if (res.code === '200') {
                this.$message.success('删除成功')
                this.getTableList()
                this.handleGetTreeData()
                this.$emit('delete', this.data)
              } else {
                this.$message.error('删除失败')
              }
            })
            .finally(() => {
              this.tableLoading = false
            })
        })
        .catch(() => {})
    },
    handleSelectionChange(data) {
      this.selectionList = data
    }
  }
}
</script>
<style lang="scss" scoped>
.tab_content {
  width: 100%;
  height: calc(100% - 40px);
  padding-top: 16px;
  display: flex;
  &_left {
    width: 300px;
    height: 100%;
    position: relative;
    .VueDragResize {
      top: 0 !important;
      height: 100% !important;
      ::v-deep .content-container {
        height: 100% !important;
        overflow: auto;
        padding-right: 6px;
      }
      &::before {
        outline: none;
      }
      ::v-deep .vdr-stick-mr {
        top: -11px !important;
        right: -14px !important;
        width: 8px !important;
        height: 102.5% !important;
        background: transparent;
        border: none;
        border-right: 1px solid #c4c4c4;
        box-shadow: none;
        &:hover {
          border-right: 1px solid #1474a4;
        }
      }
      .box-card {
        padding: 0;
      }
    }
    ::v-deep .el-tree-node__content {
      height: 32px;
      line-height: 32px;
    }
    .custom-tree-node {
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      width: 90%;
      height: 100%;
      line-height: 32px;
      &:hover > .tree-btn {
        display: flex;
        align-items: center;
      }
      &:hover > .show-label {
        width: calc(100% - 70px) !important;
      }
      .tree-label {
        display: flex;
        width: 100%;
        .iconfont {
          margin-right: 8px;
          font-size: 12px;
        }
        span {
          display: inline-block;
          width: 100%;
          font-size: 14px;
        }
      }
      .tree-btn {
        display: none;
        width: 70px;
        text-align: right;
        .icon {
          font-size: 14px;
        }
        span:first-child {
          font-size: 20px;
        }
        span {
          margin-right: 10px;
          &:hover {
            color: #1474a4;
          }
        }
        span:last-child {
          margin-right: 0;
        }
      }
    }
  }
  &_right {
    width: calc(100% - 300px);
    flex: 1;
    height: 100%;
    margin-left: 16px;
    .right_title {
      width: 100%;
      font-size: 18px;
    }
    .right-heade {
      border-radius: 4px;
      ::v-deep .el-input {
        width: 200px;
      }
      & > div {
        margin-right: 10px;
        margin-top: 10px;
      }
    }
    .right-content {
      background: #fff;
      border-radius: 4px;
      overflow: hidden;
      width: 100%;
      height: calc(100% - 130px);
      margin-top: 16px;
      .btns-group {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        & > div {
          display: flex;
        }
        .btns-group-control {
          > div {
            margin-left: 10px;
          }
          // & > div, & > button {
          //   margin-right: 10px;
          // }
        }
      }
      .table-content {
        height: calc(100% - 45px);
        ::v-deep .el-table .el-table__body-wrapper td.el-table__cell {
          border-right: 1px solid #ededf5;
          .tooltip-over-td {
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }
}
</style>
