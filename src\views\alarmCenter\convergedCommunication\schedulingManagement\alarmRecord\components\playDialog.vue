<!--
 * @Author: hedd heding<PERSON>@sinomis.com
 * @Date: 2025-03-04 10:53:37
 * @LastEditors: hedd heding<PERSON>@sinomis.com
 * @LastEditTime: 2025-06-05 11:44:17
 * @FilePath: \ihcrs_pc\src\views\alarmCenter\convergedCommunication\schedulingManagement\alarmRecord\components\playDialog.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-dialog title="播放" width="45%" :visible.sync="alarmPlayDialogShow" custom-class="model-dialog" :before-close="closeDialog">
    <div class="content">
      <div v-if="!noVideo" class="row_item">
        <template v-for="(call, idx) in itemInfo.callRecordList">
          <video v-for="(video, vIdx) in call.filePath" :key="idx + '-' + vIdx" ref="videoElement" autoplay :src="video" controls muted width="100%" height="100%"></video>
        </template>
      </div>
      <div v-else class="noEmpty">暂无视频</div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  name: 'alarmPlay',
  components: {},
  props: {
    alarmPlayDialogShow: {
      type: Boolean,
      default: false
    },
    itemInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {}
  },
  computed: {
    noVideo() {
      if (!this.itemInfo.callRecordList || !this.itemInfo.callRecordList.length) return true
      return !this.itemInfo.callRecordList.some((call) => Array.isArray(call.filePath) && call.filePath.length > 0)
    }
  },
  mounted() {},
  methods: {
    closeDialog() {
      this.$emit('closeAlarmPlayDialog')
    }
  }
}
</script>
<style lang="scss" scoped>
.model-dialog {
  .content {
    margin: 0 auto;
    background: #fff;
    border-radius: 4px;
    width: 100%;
    height: 380px;
    overflow: auto;
    padding-right: 20px;
  }
  .noEmpty {
    display: flex;
    padding: 20px;
    height: 100%;
    font-size: 18px;
    align-items: center;
    justify-content: center;
    color: #999;
    letter-spacing: 2px;
  }
}
</style>
