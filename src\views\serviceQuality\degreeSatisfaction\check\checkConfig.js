import checkObject from './checkObject'
const configAr = {
  readio: {
    // 单选题
    name: [checkObject.title, checkObject.titleSectionOther], // title校验
    test: [checkObject.content, checkObject.contentSection] // 内容校验
  },
  checkBox: {
    // 多选题
    name: [checkObject.title, checkObject.titleSectionOther],
    test: [checkObject.content, checkObject.contentSection]
  },
  completion: {
    // 填空题
    name: [checkObject.title, checkObject.titleSectionOther]
  },
  matrixProblem: {
    // 矩阵题
    name: [checkObject.title, checkObject.titleSectionOther],
    test: [checkObject.content, checkObject.contentSection]
  },
  paragraph: {
    // 段落
    name: [checkObject.title, checkObject.titleSectionOther]
  },
  sort: {
    // 排序
    name: [checkObject.title, checkObject.titleSectionOther],
    test: [checkObject.content, checkObject.contentSection]
  },
  select: {
    // 下拉选择
    name: [checkObject.title, checkObject.titleSectionOther],
    test: [checkObject.content, checkObject.contentSection]
  },
  secondary: {
    // 二级下拉选择
    name: [checkObject.title, checkObject.titleSectionOther],
    test: [checkObject.content, checkObject.contentSection]
  },
  createQuestion: {
    // 创建问卷名称
    questionName: [checkObject.title, checkObject.titleSectionOther],
    questionType: [checkObject.type, checkObject.type]
  }
}
export default configAr
