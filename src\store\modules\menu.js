import { deepClone } from '@/util'
import Tool from '@/assets/common/utils'
function hasPermission(permissions, route) {
  let isAuth = false
  if (route.meta && route.meta.menuAuth) {
    isAuth = permissions.some((auth) => {
      const pathUrl = auth.pathUrl?.split('?')[0] || auth.pathUrl
      return route.meta.menuAuth == pathUrl
    })
  } else {
    isAuth = true
  }
  // if (route.meta && route.meta.auth) {
  //   isAuth = permissions.some((auth) => {
  //     if (typeof route.meta.auth == 'string') {
  //       return route.meta.auth === auth.pathUrl
  //     } else {
  //       return route.meta.auth.some((routeAuth) => {
  //         return routeAuth === auth.pathUrl
  //       })
  //     }
  //   })
  // } else {
  //   isAuth = true
  // }
  return isAuth
}
function filterAsyncRoutes(routes, permissions) {
  const res = []
  routes.forEach((route) => {
    const tmp = { ...route }
    if (hasPermission(permissions, tmp)) {
      // 获取当前路由的权限
      const permissionsRputer = permissions.find((item) => {
        return item.pathUrl == tmp.meta?.menuAuth
      })
      // 菜单权限中的 图标接入本地路由中
      if (permissionsRputer && permissionsRputer.menuType == 1) {
        let icon = {}
        try {
          icon = JSON.parse(permissionsRputer.icon) || {}
        } catch (error) {}
        tmp.meta.title = permissionsRputer.menuName || tmp.meta.title
        tmp.meta.icon = Tool.imgUrlTranslation(icon.approve) ?? ''
        tmp.meta.activeIcon = Tool.imgUrlTranslation(icon.pitch) ?? ''
        // tmp.meta.icon = icon.approve?.replace('192.168.0.214', '193.134.210.93') ?? ''
        // tmp.meta.activeIcon = icon.pitch?.replace('192.168.0.214', '193.134.210.93') ?? ''
        tmp.orderNum = permissionsRputer.orderNum
      }
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, permissions)
        tmp.children.length && res.push(tmp)
      } else {
        res.push(tmp)
      }
    }
  })
  return res
}
// 根据父级meta属性设置子级meta
function setMetaType(arr, parentType = null) {
  if (!Array.isArray(arr)) return arr
  return arr.map(({ meta = {}, children, ...rest }) => {
    const newMeta = { ...meta, sysType: parentType || meta.sysType }
    let newChildren = []
    if (children && children.length > 0) {
      newChildren = setMetaType(children, newMeta.sysType)
    }
    if (parentType || newMeta.sysType) {
      newChildren.forEach((child) => {
        child.meta = { ...child.meta, sysType: newMeta.sysType }
      })
    }
    return { meta: newMeta, children: newChildren, ...rest }
  })
}
// 根据orderNum属性排序
function sortRoutesByOrderNum(routes) {
  if (!Array.isArray(routes)) return routes
  routes.sort((a, b) => a.orderNum - b.orderNum)
  routes.forEach((route) => {
    if (route.children) {
      route.children = sortRoutesByOrderNum(route.children)
    }
  })
  return routes
}
// 将多层嵌套路由处理成平级
function flatAsyncRoutes(routes, breadcrumb, baseUrl = '') {
  let res = []
  routes.forEach((route) => {
    const tmp = { ...route }
    if (tmp.children) {
      let childrenBaseUrl = ''
      if (baseUrl == '') {
        childrenBaseUrl = tmp.path
      } else if (tmp.path != '') {
        childrenBaseUrl = `${baseUrl}/${tmp.path}`
      }
      let childrenBreadcrumb = deepClone(breadcrumb)
      if (route.meta.breadcrumb !== false) {
        childrenBreadcrumb.push({
          path: childrenBaseUrl,
          title: route.meta.title,
          i18n: route.meta.i18n
        })
      }
      let tmpRoute = deepClone(route)
      tmpRoute.path = childrenBaseUrl
      tmpRoute.meta.breadcrumbNeste = childrenBreadcrumb
      delete tmpRoute.children
      res.push(tmpRoute)
      let childrenRoutes = flatAsyncRoutes(tmp.children, childrenBreadcrumb, childrenBaseUrl)
      childrenRoutes.map((item) => {
        // 如果 path 一样则覆盖，因为子路由的 path 可能设置为空，导致和父路由一样，直接注册会提示路由重复
        if (res.some((v) => v.path == item.path)) {
          res.forEach((v, i) => {
            if (v.path == item.path) {
              res[i] = item
            }
          })
        } else {
          res.push(item)
        }
      })
    } else {
      if (baseUrl != '') {
        if (tmp.path != '') {
          tmp.path = `${baseUrl}/${tmp.path}`
        } else {
          tmp.path = baseUrl
        }
      }
      // 处理面包屑导航
      let tmpBreadcrumb = deepClone(breadcrumb)
      if (tmp.meta.breadcrumb !== false) {
        tmpBreadcrumb.push({
          path: tmp.path,
          title: tmp.meta.title,
          i18n: tmp.meta.i18n
        })
      }
      tmp.meta.breadcrumbNeste = tmpBreadcrumb
      res.push(tmp)
    }
  })
  return res
}
// 过滤掉微前端动态路由
function filterMicroRoute(routes) {
  return routes.filter((route) => {
    if (route.name == 'microApp') {
      return false
    }
    if (route.children) {
      route.children = filterMicroRoute(route.children)
    }
    return true
  })
}
const state = {
  isGenerate: false,
  routes: [],
  headerActived: 0,
  subsystemAuth: {},
  colActiveMenu: ''
}
const getters = {
  sidebarRoutes: (state) => {
    return state.routes.length > 0 ? state.routes[state.headerActived].children : []
  }
}
const actions = {
  // 根据权限动态生成路由
  generateRoutes({ rootState, dispatch, commit }, data) {
    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async (resolve) => {
      let accessedRoutes
      // 判断权限功能是否开启
      if (rootState.settings.openPermission) {
        const permissions = await dispatch('user/getPermissions', null, { root: true })
        accessedRoutes = filterAsyncRoutes(data.asyncRoutes, permissions)
      } else {
        accessedRoutes = data.asyncRoutes
      }
      const orderRoutes = sortRoutesByOrderNum(accessedRoutes)
      const newArr = setMetaType(orderRoutes)
      // 过滤掉微前端动态路由
      let routerFilterArr = deepClone(newArr)
      routerFilterArr = routerFilterArr.map((item) => {
        if (item.children) {
          item.children = filterMicroRoute(item.children)
        }
        return item
      })
      commit('setRoutes', routerFilterArr)
      commit('setHeaderActived', data.currentPath)
      let routes = []
      newArr.map((item) => {
        routes.push(...item.children)
      })
      if (rootState.settings.enableFlatRoutes) {
        routes.map((item) => {
          if (item.children) {
            item.children = flatAsyncRoutes(item.children, [
              {
                path: item.path,
                title: item.meta.title,
                i18n: item.meta.i18n
              }
            ])
          }
        })
      }
      resolve(routes)
    })
  }
}
const mutations = {
  invalidRoutes(state) {
    state.isGenerate = false
    state.headerActived = 0
  },
  setRoutes(state, routes) {
    state.isGenerate = true
    let newRoutes = deepClone(routes)
    state.routes = newRoutes.filter((item) => {
      return item.children.length != 0
    })
  },
  // 根据路由判断属于哪个头部导航
  setHeaderActived(state, path) {
    state.routes.map((item, index) => {
      if (
        item.children.some((r) => {
          return path.indexOf(r.path + '/') === 0 || path == r.path
        })
      ) {
        state.headerActived = index
      }
    })
  },
  // 切换头部导航
  switchHeaderActived(state, index) {
    state.headerActived = index
  },
  // 设置子系统路由验证
  setSubsystemAuth(state, permissions) {
    state.subsystemAuth = permissions
  },
  //
  setColActiveMenu(state, val) {
    state.colActiveMenu = val
  }
}
export default {
  namespaced: true,
  state,
  actions,
  getters,
  mutations
}
