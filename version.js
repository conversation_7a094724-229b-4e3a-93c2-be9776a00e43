/*
 * @Author: hedd <EMAIL>
 * @Date: 2025-03-15 10:18:53
 * @LastEditors: hedd <EMAIL>
 * @LastEditTime: 2025-05-28 15:40:38
 * @FilePath: \ihcrs_pc_branch\version.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/*
 * @Description:
 */
const execSync = require('child_process').execSync
const fs = require('fs')
// 获取git信息的相关命令
const COMMITHASH_COMMAND = 'rev-parse HEAD'
const VERSION_COMMAND = 'describe --always'
const BRANCH_COMMAND = 'rev-parse --abbrev-ref HEAD'
const NEW_COMMIT_MESSAGE = 'log -1 --pretty=%B'
const COMMIT_TIME = 'show -s --format=%cd --date=iso'
try {
  const d = new Date()
  const commitId = execSync(`git ${COMMITHASH_COMMAND}`).toString().trim()
  const branch = execSync(`git ${BRANCH_COMMAND}`).toString().trim()
  const commitMessage = execSync(`git ${NEW_COMMIT_MESSAGE}`).toString().trim()
  const commitTime = execSync(`git ${COMMIT_TIME}`).toString().trim()
  const versionStr = `
COMMIT_ID: ${commitId}
Branch: ${branch}
CommitMessage: ${commitMessage}
CommitTime: ${commitTime}
PackingTime: ${d.getFullYear()}-${d.getMonth() + 1}-${d.getDate()} ${d.getHours()}:${d.getMinutes()}
  `
  const filePath = process.argv[2] ? 'dist-' + process.argv[2] : 'dist'
  fs.writeFileSync(filePath + '/version.txt', versionStr)
} catch (e) {
  throw new Error(e)
}
