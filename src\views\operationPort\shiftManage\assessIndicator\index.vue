<template>
  <PageContainer>
    <div slot="content" class="targetFile-body">
      <div class="targetFile-content-title">指标档案</div>
      <div class="targetFile-content">
        <div class="targetFile-content-left">
          <div class="search">
            <el-input v-model="filterText" placeholder="请输入分类" clearable suffix-icon="el-icon-search"> </el-input>
          </div>
          <div class="left_content">
            <el-tree ref="tree" class="filter-tree" :props="defaultProps" :data="treeData" highlight-current
              :load="loadNode" lazy node-key="id" @node-click="handleNodeClick">
              <template #default="{node, data}">
                <el-tooltip effect="dark" :disabled="showTooltip" :content="node.label" placement="top">
                  <div class="nodeLabel" @mouseover="onMouseOver(data.id)">
                    <span :ref="`nodeLabel${data.id}`">{{node.label}}</span>
                  </div>
                </el-tooltip>
              </template>
            </el-tree>
          </div>
        </div>
        <div class="targetFile-content-right">
          <div class="search-from">
            <div>
              <el-input v-model.trim="indicatorPlanName" placeholder="请输入计划名称" style="width: 200px" clearable
                maxlength="25" onkeyup="if(value.length>25)value=value.slice(0,25)"></el-input>
              <el-button type="primary" plain style="margin-left: 10px" @click="resetForm">重置</el-button>
              <el-button type="primary" @click="searchForm">查询</el-button>
            </div>
          </div>
          <div class="contentTable">
            <div class="contentTable-main table-content">
              <el-table v-loading="tableLoading" border style="width: 100%" :data="tableData" height="calc(100% - 20px)"
                :row-class-name="tableRowClassName">
                <el-table-column prop="indicatorPlanName" label="计划名称" show-overflow-tooltip></el-table-column>
                <el-table-column prop="templateManageName" label="指标模板" show-overflow-tooltip></el-table-column>
                <el-table-column prop="cycleName" label="考察周期" show-overflow-tooltip></el-table-column>
                <el-table-column label="操作" width="180" fixed="right">
                  <template slot-scope="scope">
                    <el-button type="text" class="record" @click="check(scope.row)">查看</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="contentTable-footer">
              <el-pagination :current-page="pagination.current" :page-sizes="pagination.pageSizeOptions"
                :page-size="pagination.size" :layout="pagination.layoutOptions" :total="pagination.total"
                @size-change="paginationSizeChange" @current-change="paginationCurrentChange">
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PageContainer>
</template>

<script>
import tableListMixin from '@/mixins/tableListMixin.js'
import { transData } from '@/util'
export default {
  name: 'templateManagement',
  components: {},
  mixins: [tableListMixin],
  data() {
    return {
      activeName: '',
      filterText: '',
      indicatorPlanName: '', // 模板名称,
      treeData: [],
      checkedData: {}, // 选中tree数据
      defaultProps: {
        children: 'list',
        label: 'name',
        isLeaf: 'leaf'
      },
      expanded: [],
      tableLoading: false,
      tableData: [],
      treeLoading: false,
      showTooltip: false
    }
  },
  watch: {
    filterText(val) {
      this.searchTree(val)
    }
  },
  created() {
    this.getTreeData()

  },
  methods: {
    // tree 树
    getTreeData() {
      this.treeLoading = true
      let data = {
        id: '',
        name: '',
        pid: '',
        type: 0
      }
      this.$api.getTargetFileTree(data).then((res) => {
        if (res.code == '200') {
          if (res.data.length > 0) {
            let arr = []
            arr = res.data.filter(item => item.id == 0)
            this.treeData = arr
            this.treeLoading = false
            this.getDataList()
          } else {
            this.treeLoading = false
          }
        }
      })
    },
    searchTree(val) {
      let data = {
        categoryName: val,
        type: '0'
      }
      if (val && val !== '') {
        this.$api.getTargetFileTreeId(data).then((res) => {
          if (res.code == '200') {
            this.treeData = transData(res.data, 'id', 'pid', 'list')
          }
        })
      } else {
        this.getTreeData()
      }
    },

    // 懒加载
    loadNode(node, resolve) {
      if (node.level === 1) {
        if (node.data.list) {
          return resolve(node.data.list)
        } else {
          return resolve([])
        }
      }
      if (node.level > 1) {
        let params = {
          id: node.data.id,
          name: node.data.name,
          pid: node.data.pid,
          type: node.data.type
        }
        this.$api.getTargetFileTreeId(params).then((res) => {
          if (res.code == '200' && res.data.length > 0) {
            return resolve(res.data)
          } else {
            return resolve([])
          }
        })
      }
    },
    // table隔行变色
    tableRowClassName({ row, rowIndex }) {
      if (rowIndex % 2 == 1) {
        return 'warning-row'
      } else {
        return 'success-row'
      }
    },
    getDataList() {
      this.tableLoading = true
      let data = {
        nodeCode: this.checkedData.id,
        indicatorPlanName: this.indicatorPlanName,
        page: this.pagination.current,
        pageSize: this.pagination.size
      }
      this.$api
        .getTargetFilePageList(data)
        .then((res) => {
          this.tableLoading = false
          if (res.code == 200) {
            this.tableData = res.data ? res.data.records : []
            this.pagination.total = res.data ? res.data.total : 0
          } else if (res.message) {
            this.tableData = []
            this.pagination.total = 0
            this.$message.error(res.message)
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    //  ----------------------------------------------Tree_Fn
    // 树状图点击
    handleNodeClick(data) {
      this.pagination.current = 1
      this.checkedData = data
      this.indicatorPlanName = ''
      this.$refs.tree.setCurrentKey(this.checkedData.id)
      this.getDataList()
    },
    // 重置
    resetForm() {
      this.indicatorPlanName = ''
      this.pagination.size = 15
      this.pagination.current = 1
      this.getDataList()
    },
    // 查询
    searchForm() {
      this.pagination.current = 1
      this.getDataList()
    },
    // 点击操作记录
    check(data) {
      this.$router.push({
        path: '/targetAnalysis/targetResult',
        query: {
          planId: data.indicatorPlanId,
          manageId: data.templateManageId,
          nodeCode: this.checkedData.id
        }
      })
    },
    // 树划入划出效果
    onMouseOver(id) {
      const parentWidth = this.$refs[`nodeLabel${id}`].parentNode.offsetWidth // 获取元素父级可视宽度
      const contentWidth = this.$refs[`nodeLabel${id}`].offsetWidth // 获取元素可视宽度
      this.showTooltip = contentWidth <= parentWidth
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: linear-gradient(to right, #d9e1f8, #fff);
}

::v-deep .el-tree-node__content {
  height: 32px;
}
.targetFile-body {
  height: 100%;
  width: 100%;
  background: #fff;
  .targetFile-content-title {
    border-bottom: 1px solid #e4e7ed;
    padding: 13px 16px;
  }
  .targetFile-content {
    display: flex;
    margin-top: 16px;
    padding: 0 24px;
    height: calc(100% - 80px);
    .targetFile-content-left {
      width: 246px;
      height: 100%;
      background: #fff;
      .search {
        padding-bottom: 12px;
      }

      .left_content {
        width: 100%;
        height: calc(100% - 110px);
        border-radius: 4px;
        overflow: scroll;
        .tree-node {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 14px;
          /* padding-right: 8px; */
        }
        .over-ellipsis {
          display: block;
          width: 140px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          -webkit-line-clamp: 1;
        }
      }
    }
    .targetFile-content-right {
      height: 100%;
      min-width: 0;
      padding: 0 20px;
      background: #fff;
      border-radius: 4px;
      flex: 1;

      .search-from {
        padding-bottom: 12px;
        display: flex;
        justify-content: space-between;

        & > div {
          margin-right: 10px;
        }

        & > button {
          margin-top: 12px;
        }
      }

      .contentTable {
        height: calc(100% - 50px);
        display: flex;
        flex-direction: column;

        .contentTable-main {
          flex: 1;
          overflow: auto;
        }
        .contentTable-footer {
          padding: 10px 0 0;
        }
      }
    }

    .content {
      width: 100%;
      max-height: 500px !important;
      overflow: auto;
      background-color: #fff !important;
    }
  }
}
.record {
  color: #3562db !important;
}
.nodeLabel {
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
::v-deep .el-tree-node {
  white-space: normal;
  // outline: 0;
}
::v-deep .warning-row {
  background-color: #f5f5f5 !important;
}
</style>
