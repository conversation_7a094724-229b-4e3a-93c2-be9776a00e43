<template>
  <PageContainer class="classeseManage">
    <template #content>
      <div class="classeseManage__header">
        <el-form ref="formRef" :model="searchForm" inline @submit.native.prevent="name">
          <el-form-item prop="idOrName">
            <el-input v-model="searchForm.idOrName" placeholder="搜索班次名或创建人" suffix-icon="el-icon-search"> </el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" plain @click="onReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="classeseManage__actions">
        <el-button type="primary" @click="onOperate('add')" icon="el-icon-plus" v-auth="'classeseManage:add'">
          新建
        </el-button>
      </div>
      <div class="classeseManage__table">
        <el-table v-loading="tableLoadingStatus" height="100%" :data="tableData" border stripe table-layout="auto"
          class="tableAuto" row-key="id">
          <el-table-column prop="name" label="班次名称" show-overflow-tooltip></el-table-column>
          <el-table-column prop="time" label="考勤时间" show-overflow-tooltip></el-table-column>
          <el-table-column prop="createdName" label="创建人" show-overflow-tooltip></el-table-column>
          <el-table-column prop="updateName" label="最近操作人" show-overflow-tooltip></el-table-column>
          <el-table-column prop="updateTime" label="操作时间" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ row.updateTime?moment(row.updateTime).format('YYYY-MM-DD HH:mm:ss'):''}}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150px">
            <template #default="{ row }">
              <el-button type="text" @click="onOperate('view',row)" v-auth="'classeseManage:detail'">查看</el-button>
              <el-button type="text" @click="onOperate('edit',row)" v-auth="'classeseManage:edit'">编辑</el-button>
              <el-button type="text" class="text-red" @click="onOperate('delete',row)"
                v-auth="'classeseManage:delete'">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination class="classeseManage__pagination" :current-page="pagination.page"
        :page-sizes="pagination.pageSizeOptions" :page-size="pagination.size" :layout="pagination.layoutOptions"
        :total="pagination.total" @size-change="paginationSizeChange" @current-change="paginationCurrentChange">
      </el-pagination>

    </template>
  </PageContainer>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin'
import moment from 'moment'
export default {
  name: 'classeseManage',
  mixins: [tableListMixin],
  data() {
    return {
      moment,
      searchForm: {
        idOrName: '',//班次名或创建人
      },
      tableData: [],
      tableLoadingStatus: false,
    }
  },
  mounted() {
    this.getDataList()
  },
  methods: {
    // 获取列表数据
    getDataList() {
      this.tableData = []
      this.tableLoadingStatus = true
      const params = {
        pageSize: this.pagination.size,
        page: this.pagination.current,
        ...this.searchForm
      }
      this.$api.supplierAssess.queryShiftInfoByPage(params)
        .then((res) => {
          this.tableLoadingStatus = false
          if (res.code === '200') {
            this.tableData = res.data.records
            this.pagination.total = res.data.total
          } else {
            this.tableData = []
            this.pagination.total = 0
          }
        })
        .catch((err) => {
          this.tableLoadingStatus = false
        })
    },
    // 表单搜索按钮点击
    onSearch() {
      this.pagination.current = 1
      this.getDataList()
    },
    // 表单重置按钮点击
    onReset() {
      this.$refs.formRef.resetFields()
      this.onSearch()
    },
    //操作
    onOperate(type, row) {
      if (type === 'add') {
        this.$router.push({
          name: "classeseAdd",
          query: {
            type: 'add',
          }
        })
      } else if (type === 'view') {
        this.$router.push({
          name: "classeseAdd",
          query: {
            type: 'view',
            id: row.id,
            attendanceTimeId: row.attendanceTimeId
          }
        })
      } else if (type === 'edit') {
        this.$router.push({
          name: "classeseAdd",
          query: {
            type: 'edit',
            id: row.id,
            attendanceTimeId: row.attendanceTimeId
          }
        })
      } else if (type === 'delete') {
        this.$confirm('确认删除选中的班次信息吗?', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$api.supplierAssess.deleteShiftInfoById({ id: row.id }).then((res) => {
            if (res.code == 200) {
              this.onSearch()
              this.$message({
                message: res.msg,
                type: 'success'
              })
            } else {
              this.$message.error(res.msg)
            }
          })
        })
      }
    },
  }
}
</script>
<style scoped lang="scss">
.classeseManage {
  ::v-deep(> .container-content) {
    padding: 16px;
    height: 100%;
    width: 100%;
    background-color: #fff;
  }
  &__header {
    display: flex;
  }
  &__actions {
    padding-bottom: 10px;
  }
  &__table {
    height: calc(100% - 140px);
  }
  &__pagination {
    margin-top: 10px;
  }
  .text-red {
    color: #ff1919;
  }
}
.el-form-item {
  margin-bottom: 8px !important;
}
</style>
