<template>
  <div class="sino_page">
    <el-container>
      <el-main>
        <div class="search-box">
          <el-input v-model="searchForm.assetsNameCode" clearable filterable placeholder="设备名称/编码"
            @blur="event => searchForm.assetsNameCode = event.target.value.replace(/\s+/g, '')"></el-input>
          <el-select v-model="searchForm.sysOf1" placeholder="设备类型" class="ml-16">
            <el-option v-for="item in systemCodeList" :key="item.dictionaryDetailsId"
              :label="item.dictionaryDetailsName" :value="item.dictionaryDetailsId">
            </el-option>
          </el-select>
          <el-select ref="groupIdSelect" v-model="searchForm.groupId" placeholder="分组类型" clearable class="ml-16">
            <el-option hidden :label="groupName" :value="searchForm.groupId"></el-option>
            <el-tree :data="customGroupingList" :props="customDefaultProps" :expand-on-click-node="false"
              :check-on-click-node="true" @node-click="customNodeClick">
            </el-tree>
          </el-select>
          <div class="ml-16">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button type="primary" plain @click="reset">重置</el-button>
          </div>
        </div>
        <div class="sino_table">
          <el-table ref="sinoTable" v-loading="tableLoading" :data="tableData" :row-key="getRowKeys" height="300px"
            stripe border @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="50" align="center" :reserve-selection="true"></el-table-column>
            <el-table-column v-for="(column, index) in tableColumn" :key="index" :prop="column.prop"
              :label="column.label" :min-width="column.minWidth" show-overflow-tooltip>
              <template slot-scope="scope">
                <span>{{ scope.row[column.prop] }}</span>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination :current-page="pagination.page" :page-sizes="[15, 30, 50, 100]"
            :page-size="pagination.pageSize" :total="pageTotal" layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange" @current-change="handleCurrentChange"></el-pagination>
        </div>
      </el-main>
    </el-container>
  </div>
</template>
<script>
import { transData } from '@/util'
export default {
  name: 'SelectGroup',
  components: {},
  props: {
    groupList: {
      type: Array,
      default: () => []
    },
    assetsInfoIdNotInList: {
      type: Array,
      default: () => []
    },
  },
  data() {
    return {
      searchForm: {
        assetsNameCode: '',
        sysOf1: '',
        groupId: '',
      },
      customGroupingList: [],//自定义分组
      customDefaultProps: {
        label: 'groupName',
        isLeaf: 'leaf',
        children: 'children'
      },
      groupName: '',
      tableLoading: false,
      tableHeight: '',
      tableData: [],
      multipleSelection: [],
      // -----------------------------Pagination
      pagination: {
        pageSize: 15,
        page: 1
      }, // 分页数据
      pageTotal: 0, // 数据总条数
      tableColumn: [
        {
          prop: 'assetsName',
          label: '设备名称'
        },
        {
          prop: 'assetsCode',
          label: '设备编码'
        },
        {
          prop: 'dictionaryDetailsName',
          label: '设备类型'
        },
        // {
        //   prop: 'groupName',
        //   label: '所属分组'
        // },
      ],
      systemCodeList: [],
      assetsInfoIdNotInList: []
    }
  },
  watch: {
    groupList: {
      handler(newValue, oldValue) {
        this.getSelectData()
        this.$refs.sinoTable.clearSelection();
      },
      immediate: false,
      deep: true
    }
  },
  mounted() {
    this.getSelectData()
    this.systemType()
    this.getAssetsGroup()
  },
  methods: {
    // 设备类型
    systemType() {
      this.systemCodeList = []
      let data = {
        dictionaryCode: this.$route.query.systemCode
      }
      this.$api.getQueryRootSubCategoryDetails(data).then((res) => {
        if (res.code == '200') {
          this.systemCodeList = res.data
        }
      })
    },
    // 已经绑定的设备
    getSelectData() {
      this.$api
        .getQueryTimeScheduleId(this.$route.query.cronTimeId)
        .then((res) => {
          if (res.code == 200) {
            this.assetsInfoIdNotInList = res.data.map(item => {
              return item.assetsInfoId
            })
            this.getTableData()
          } else {
            this.$message.error(res.message)
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    // 自定义分组树
    getAssetsGroup() {
      this.customGroupingList = []
      let data = {
        dictionaryDetailsCode: this.$route.query.systemCode,
        groupName: "",
        equipAttr: "2"
      }
      this.$api.getCustomGroupingTree(data).then((res) => {
        if (res.code == '200') {
          this.customGroupingList = transData(res.data, 'id', 'pid', 'children')
        }
      })
    },
    // 自定义分组树选择 数据
    customNodeClick(data) {
      this.searchForm.groupId = data.id
      this.groupName = data.groupName
      this.$refs.groupIdSelect.blur()
    },

    getRowKeys(row) {
      return row.id
    },
    search() {
      this.pagination.page = 1
      this.getTableData()
    },
    reset() {
      this.searchForm = {
        assetsNameCode: '',
        sysOf1: '',
        groupId: '',
      }
      this.groupName = ''
      this.pagination.page = 1
      this.pageTotal = 0
      this.getTableData()
    },
    getTableData() {
      this.tableLoading = true
      let data = {
        ...this.pagination,
        ...this.searchForm,
        equipAttr: "2",
        dictionaryDetailsCode: this.$route.query.systemCode,
        assetsInfoIdNotInList: this.assetsInfoIdNotInList
      }
      this.$api
        .getOperationalMonitoringList(data)
        .then((res) => {
          this.tableLoading = false
          if (res.code == 200) {
            this.tableData = res.data ? res.data.records : []
            this.pageTotal = res.data ? res.data.total : 0
          } else {
            this.$message.error(res.message)
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    // ---------------------------------------------------------- TabelFn
    handleSizeChange(val) {
      this.pagination.page = 1
      this.pagination.pageSize = val
      this.getTableData()
    },
    handleCurrentChange(val) {
      this.pagination.page = val
      this.getTableData()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    }
  }
}
</script>
<style lang="scss" scoped>
.el-main {
  padding: 6px 24px 8px;
  overflow: hidden;

  .sino_panel {
    height: 100%;
    border-radius: 10px;
    background: #fff;
    position: relative;
    font-size: 14px;
  }

  .sino_page {
    height: 100%;
    position: relative;

    .el-main {
      // height: 100%;
      background: #fff;
      border-radius: 10px;
      padding: 0;
    }
  }
}

.search-box {
  display: flex;
  align-items: center;
  margin-bottom: 10px;

  .el-input,
  .el-select {
    width: 200px;
  }
}

.ml-16 {
  margin-left: 16px;
}

.el-pagination {
  margin-top: 10px;
}
</style>
