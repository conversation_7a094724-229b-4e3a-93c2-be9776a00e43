<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="whloe">
        <el-form ref="formInline" :model="formInline" :inline="true" :rules="rules" label-position="right" label-width="150px" :disabled="$route.query.type == 'View'">
          <el-form-item label="归属单位" prop="umId" style="margin-top: 25px">
            <el-select v-model="formInline.umId" class="sino_form_input" placeholder="请选择归属单位" clearable @change="selectedHandle">
              <el-option v-for="item in unitList" :key="item.umId" :label="item.unitComName" :value="item.umId"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="上级部门" prop="pid" style="margin-top: 25px">
            <el-select v-model="formInline.pid" class="sino_form_input" placeholder="请选择上级部门" clearable filterable>
              <el-option v-for="item in departList" :key="item.id" :label="item.deptName" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <br />
          <el-form-item label="部门名称" prop="deptName">
            <el-input v-model="formInline.deptName" class="sino_form_input" placeholder="请输入部门名称" show-word-limit> </el-input>
          </el-form-item>
          <el-form-item label="部门性质" prop="nature">
            <el-select v-model="formInline.nature" class="sino_form_input" placeholder="请选择部门性质" clearable>
              <el-option v-for="item in deptTypeList" :key="item.id" :label="item.dictName" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <div v-for="(item, i) in formInline.deptPhoneList" :key="i">
            <el-form-item label="部门电话" :prop="'deptPhoneList.' + i" :rules="rules.deptPhone">
              <el-input v-model.trim="formInline.deptPhoneList[i]" class="sino_form_input" placeholder="请输入部门电话" show-word-limit> </el-input>
              <!-- <i class="el-icon-plus reset_icon" @click="AddRow"></i>
              <i v-if="i > 0" class="el-icon-minus reset_icon" @click="DelRow(i)"></i> -->
            </el-form-item>
          </div>
          <!-- <el-form-item label="部门电话" prop="deptPhone">
            <el-input
              class="sino_form_input"
              placeholder="请输入部门电话"
              show-word-limit
              v-model.trim="formInline.deptPhone"
            >
            </el-input>
          </el-form-item> -->
          <br />
          <el-form-item label="组织机构编码" prop="officeCode">
            <el-input v-model="formInline.officeCode" class="sino_form_input" placeholder="请输入组织机构编码" show-word-limit> </el-input>
          </el-form-item>
          <el-form-item label="财务核算码" prop="financeCode">
            <el-input v-model="formInline.financeCode" class="sino_form_input" placeholder="请输入财务核算码" show-word-limit> </el-input>
          </el-form-item>
          <br />
          <el-form-item label="部门负责人" prop="principalId">
            <el-input v-model="formInline.principalName" class="sino_form_input" placeholder="请选择部门负责人" show-word-limit @focus="handlePersonClick">
              <i v-show="$route.query.type != 'View'" slot="suffix" class="el-icon-circle-plus-outline" style="cursor: pointer; font-size: 18px" @click="handlePersonClick"> </i>
            </el-input>
            <i v-show="$route.query.type != 'View'" class="el-icon-circle-close reset_icon" @click="resetPrincipalName"></i>
          </el-form-item>
          <el-form-item label="部门负责人电话" prop="principalPhone">
            <el-input v-model="formInline.principalPhone" class="sino_form_input" placeholder="请输入部门负责人电话" show-word-limit disabled> </el-input>
          </el-form-item>
          <br />
          <el-form-item label="负责空间" prop="">
            <!-- <el-cascader
              v-model="formInline.spaceList"
              class="sino_form_input_custom"
              placeholder="请选择负责空间"
              :options="spaceOptions"
              :show-all-levels="false"
              :props="props"
              clearable
            ></el-cascader> -->
            <el-input v-model="formInline.spaceNames" class="sino_form_input_custom" placeholder="请选择负责空间"></el-input>
          </el-form-item>
          <br />
          <el-form-item label="服务类型" prop="dictList">
            <el-select v-model="formInline.dictList" class="sino_form_input_custom" multiple clearable placeholder="请选择服务类型">
              <el-option v-for="item in serviceTypeList" :key="item.id" :label="item.dictName" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <br />
          <el-form-item label="备注" prop="remark">
            <el-input v-model="formInline.remark" class="sino_form_input_custom" placeholder="请输入" show-word-limit> </el-input>
          </el-form-item>
        </el-form>
        <!-- <sinoDialog ref="sinoDialog" title="人员选择" :customStyle="true" @sureDialog="sureDialogUser" @closeDialog="closeDialog">
          <StaffManagement ref="StaffManagement" :isDialog="true" :checkbox="false"></StaffManagement>
        </sinoDialog> -->
      </div>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">取消</el-button>
    </div>
  </PageContainer>
</template>
<script>
import { transData } from '@/util'
// import StaffManagement from '@page/staffManagement/staffManagement'
import { validateMobile, validatePhone, validateNum } from '@/assets/common/validate'
export default {
  // components: { StaffManagement },
  async beforeRouteLeave(to, from, next) {
    if (!['departmentManage'].includes(to.name)) {
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    let validatePhoneNum = (rule, value, callback) => {
      if (validatePhone(value) || validateMobile(value) || validateNum(value)) {
        this.validatorCheck(value, callback)
      } else {
        callback(new Error('请输入手机号码/固话号码/数字'))
      }
    }
    return {
      // title: this.$route.query.type == 'Add' ? '新增部门' : this.$route.query.type == 'View' ? '查看部门' : '编辑部门',
      isDisable: false,
      formInline: {
        umId: '', // 归属单位ID
        pid: '',
        deptName: '',
        nature: '',
        natureName: '',
        deptPhone: '',
        deptPhoneList: [''],
        officeCode: '',
        financeCode: '',
        principalId: '', // 负责人ID
        principalName: '', // 负责人
        principalPhone: '',
        spaceNames: '',
        dictList: '', // 服务类型
        remark: ''
      },
      rules: {
        umId: {
          required: true,
          message: '请选择归属单位',
          trigger: 'blur'
        },
        deptName: {
          required: true,
          message: '请输入部门名称',
          trigger: 'blur'
        },
        deptPhone: {
          validator: validatePhoneNum,
          trigger: 'blur'
        }
      },
      unitList: [],
      departList: [],
      deptTypeList: [],
      spaceOptions: [],
      props: {
        multiple: true,
        value: 'id',
        label: 'ssmName',
        checkStrictly: true,
        emitPath: false
      },
      serviceTypeList: []
    }
  },
  activated() {
    this.init()
  },
  created() {
    if (!this.$store.state.keepAlive.list.includes('departmentManage')) {
      this.init()
    }
  },
  methods: {
    init() {
      Object.assign(this.$data, this.$options.data())
      this.$nextTick(() => {
        this.$refs.formInline.resetFields()
      })
      this.getUnitListFn() // 单位列表
      this.getDeptListFn('') // 部门列表
      this.valveTypeListFn('') // 部门性质列表
      this.getSpaceTreeFn() // 空间结构树
      this.serviceTypeListFn() // 部门服务类型
      if (this.$route.query.id) {
        this.getDepartByIdFn()
      }
    },
    //  根据单位ID获取单位信息详情
    getDepartByIdFn() {
      this.$api
        .getDepartById({
          id: this.$route.query.id
        })
        .then((res) => {
          if (res.code == 200) {
            this.formInline = res.data
            console.log(this.formInline, 'this.formInline')
            this.formInline.deptPhoneList ? this.formInline.deptPhoneList : (this.formInline.deptPhoneList = [''])
            // this.formInline.unitType = res.data.unitType.split(",").map(Number);
          }
        })
    },
    // ---------------------------------- 下拉框Data
    // 单位列表
    getUnitListFn() {
      this.$api.getUnitList({}).then((res) => {
        if (res.code == 200) {
          this.unitList = res.data
        }
      })
    },
    selectedHandle(val) {
      this.getDeptListFn(val)
    },
    // 部门列表
    getDeptListFn(unitId) {
      this.$api
        .getDeptList({
          unitId: unitId
        })
        .then((res) => {
          if (res.code == 200) {
            this.departList = res.data
          }
        })
    },
    // 空间结构树
    getSpaceTreeFn() {
      this.$api.getSpaceTree({}).then((res) => {
        if (res.code == 200) {
          this.spaceOptions = transData(res.data, 'id', 'pid', 'children')
          this.setDisable(res.data)
        }
      })
    },
    setDisable(data) {
      data.forEach((v) => {
        v.ssmType != 5 ? (v.disabled = true) : '' // 超过设定的最大级数,给这一集的数据添加disabled属性
      })
    },
    AddRow() {
      this.formInline.deptPhoneList.push('')
    },
    DelRow(index) {
      this.formInline.deptPhoneList.splice(index, 1)
    },
    // 部门电话去重校验
    validatorCheck(val, callback) {
      let phoneArr = this.formInline.deptPhoneList
      let counts = (arr, value) => arr.reduce((a, v) => (v === value ? a + 1 : a + 0), 0)
      if (val && counts(phoneArr, val) > 1) {
        callback(new Error('号码重复'))
      } else {
        return callback()
      }
    },
    // 服务类型列表
    serviceTypeListFn() {
      this.$api
        .valveTypeList({
          typeValue: 'BMFW'
        })
        .then((res) => {
          if (res.code == 200) {
            this.serviceTypeList = res.data
          }
        })
    },
    //  获取部门性质字典列表
    valveTypeListFn() {
      this.$api
        .valveTypeList({
          typeValue: 'BMXZ'
        })
        .then((res) => {
          if (res.code == 200) {
            this.deptTypeList = res.data
          }
        })
    },
    /**
     * @description: 部门负责人选择
     * @param {*}
     * @return {*}
     * @note:
     * @Author: Allen
     */
    handlePersonClick() {
      this.$refs.sinoDialog.dialogTableVisible = true
    },
    resetPrincipalName() {
      this.formInline.principalName = ''
    },
    // ------------------------------------DialogFn
    sureDialogUser() {
      let selected = this.$refs.StaffManagement.radioObj
      console.log(selected)
      this.formInline.principalId = selected.id
      this.formInline.principalName = selected.staffName
      this.formInline.principalPhone = selected.mobile
      this.closeDialog()
    },
    closeDialog() {
      this.$refs.sinoDialog.dialogTableVisible = false
    },
    cancel() {
      this.$router.go(-1)
    }
  }
}
</script>
<style lang="scss" scoped>
.avatar {
  width: 150px;
  height: 150px;
  display: block;
}
.reset_icon {
  position: absolute;
  right: 28px;
  top: 9px;
  font-size: 18px;
  color: #c0c4cc;
  cursor: pointer;
}
.el-icon-plus {
  color: #5188fc;
}
.el-icon-minus {
  right: 8px;
  color: red;
}
.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
  padding: 10px;
  overflow-y: auto;
}
.whole {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 15px;
}
.sino_form_input {
  width: 300px;
}
.sino_form_input_custom {
  width: 730px;
}
</style>
