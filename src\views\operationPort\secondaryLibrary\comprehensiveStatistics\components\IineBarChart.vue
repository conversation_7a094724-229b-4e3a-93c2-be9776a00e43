<template>
  <div class="line_bar_chart">
    <div class="title">出入库数量统计</div>
    <div>
      <div ref="line_bar" class="line_bar"></div>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
export default {
  name: 'IineBarChart',
  props: {
    chartData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      chart: null,
      option: {
        grid: {
          left: '0%',
          right: '0%',
          top: '20%',
          bottom: '0%',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        legend: {
          itemWidth: 8,
          itemHeight: 8,
          data: [
            {
              name: '入库数量',
              itemStyle: {
                color: '#3562DB'
              },
              textStyle: {
                color: '#666666',
                fontSize: '14px'
              }
            },
            {
              name: '出库数量',
              itemStyle: {
                color: '#FF9A2E'
              },
              textStyle: {
                color: '#666666',
                fontSize: '14px'
              }
            },
            {
              name: '入库金额',
              itemStyle: {
                color: '#FF6461'
              },
              textStyle: {
                color: '#666666',
                fontSize: '14px'
              }
            },
            {
              name: '出库金额',
              itemStyle: {
                color: '#08CB83'
              },
              textStyle: {
                color: '#666666',
                fontSize: '14px'
              }
            }
          ]
        },
        xAxis: [
          {
            type: 'category',
            data: [],
            axisPointer: {
              type: 'shadow'
            },
            axisLabel: {
              color: '#96989A',
              fontSize: 14,
              margin: 10
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '件',
            min: 0,
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed'
              }
            }
          },
          {
            type: 'value',
            name: '元',
            min: 0,
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed'
              }
            }
          }
        ],
        series: [
          {
            name: '入库数量',
            type: 'bar',
            barWidth: 8,
            tooltip: {
              valueFormatter: function (value) {
                return value + ' 件'
              }
            },
            itemStyle: {
              color: '#3562DB'
            },
            data: []
          },
          {
            name: '出库数量',
            type: 'bar',
            barWidth: 8,
            tooltip: {
              valueFormatter: function (value) {
                return value + ' 件'
              }
            },
            itemStyle: {
              color: '#FF9A2E'
            },
            data: []
          },
          {
            name: '入库金额',
            type: 'line',
            yAxisIndex: 1,
            tooltip: {
              valueFormatter: function (value) {
                return value + ' 元'
              }
            },
            itemStyle: {
              color: '#FF6461'
            },
            data: []
          },
          {
            name: '出库金额',
            type: 'line',
            yAxisIndex: 1,
            tooltip: {
              valueFormatter: function (value) {
                return value + ' 元'
              }
            },
            itemStyle: {
              color: '#08CB83'
            },
            data: []
          }
        ]
      }
    }
  },
  watch: {
    chartData: {
      handler(newVal) {
        this.option.xAxis[0].data = newVal.xData
        this.option.series[0].data = newVal.series0
        this.option.series[1].data = newVal.series1
        this.option.series[2].data = newVal.series2
        this.option.series[3].data = newVal.series3
        this.chart.setOption(this.option)
      },
      deep: true
    }
  },
  mounted() {
    this.initChart()
    addEventListener('resize', this.handleChartResize)
  },
  beforeDestroy() {
    removeEventListener('resize', this.handleChartResize)
  },
  methods: {
    handleChartResize() {
      this.chart.resize()
    },
    initChart() {
      this.chart = echarts.init(this.$refs.line_bar)
      // this.chart.setOption(this.option)
    }
  }
}
</script>
<style lang="scss" scoped>
.line_bar_chart {
  margin-top: 24px;
  background: #faf9fc;
  margin-bottom: 16px;
  padding: 16px;
  border-radius: 4px 4px 4px 4px;
  .title {
    font-weight: bold;
    font-size: 16px;
    color: #333333;
  }
  .line_bar {
    width: 100%;
    height: 200px;
  }
}
</style>
