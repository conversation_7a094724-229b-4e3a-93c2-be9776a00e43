<template>
  <PageContainer class="positionManage-list">
    <template #content>
      <div class="positionManage-list__left">
        <div class="positionManage-list__left__tree">
          <div class="space-tree__search">
            <el-input v-model="treeSearchKeyWord" placeholder="输入关键字搜索" suffix-icon="el-icon-search"></el-input>
          </div>
          <el-tree
            ref="treeRef"
            v-loading="treeLoading"
            class="space-tree__tree"
            :data="treeData"
            node-key="code"
            :props="defaultProps"
            size="small"
            :highlight-current="true"
            :filter-node-method="filterNode"
            default-expand-all
            :expand-on-click-node="false"
            @node-click="handleNodeClick"
          >
            <template #default="{ data }">
              <div class="custom-tree-node">
                <div class="custom-tree-node-wrapper">
                  <el-tooltip class="item" effect="dark" :content="data.name" placement="top-start" :disabled="data.name.length < 9">
                    <span class="custom-tree-node-label">
                      {{ data.name }}
                    </span>
                  </el-tooltip>
                  <el-dropdown trigger="click" class="custom-tree-menu" size="small">
                    <span class="more"><i class="el-icon-more rotate" /></span>
                    <el-dropdown-menu slot="dropdown" class="hide-arrow">
                      <el-dropdown-item divided>
                        <div v-auth="'positionManage:add'" style="padding: 0px 15px; text-align: center" @click.self="clickMenu('addJunior', data)">新增下级</div>
                      </el-dropdown-item>
                      <el-dropdown-item divided>
                        <div v-auth="'positionManage:edit'" style="padding: 0px 15px; text-align: center" @click.self="clickMenu('edit', data)">编辑</div>
                      </el-dropdown-item>
                      <el-dropdown-item divided>
                        <div v-auth="'positionManage:delete'" style="padding: 0px 15px; text-align: center" class="removeNode" @click.self="clickMenu('remove', data)">删除</div>
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </div>
              </div>
            </template>
          </el-tree>
          <el-button v-auth="'positionManage:add'" type="primary" icon="el-icon-plus" @click="addPost">新增岗位 </el-button>
        </div>
      </div>
      <div class="positionManage-list__right">
        <el-form ref="formRef" class="positionManage-list__form" :model="searchForm" inline>
          <el-form-item prop="name">
            <el-input v-model="searchForm.name" placeholder="搜索姓名、工号、手机号" suffix-icon="el-icon-search" clearable></el-input>
          </el-form-item>
          <el-form-item prop="showType">
            <el-select v-model="searchForm.showType" placeholder="展示全部成员" clearable>
              <el-option v-for="item of formOptions.showType" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" plain @click="onReset">重置</el-button>
            <el-button type="primary" @click="onSearch">查询</el-button>
          </el-form-item>
        </el-form>
        <div class="positionManage-list__table_actions">
          <el-button v-auth="'positionManage:add'" type="primary" icon="el-icon-plus" @click="onOperate(OperateType.ADD)">添加成员</el-button>
          <el-button type="danger" plain :disabled="!checkedIds.length" @click="onOperate(OperateType.BATCH_DELETE)">移除 </el-button>
        </div>
        <div class="positionManage-list__table">
          <el-table
            v-loading="tableLoading"
            height="100%"
            :data="tableData"
            border
            stripe
            table-layout="auto"
            class="tableAuto"
            row-key="id"
            @selection-change="(rows) => (selectionList = rows)"
          >
            <el-table-column type="selection" width="50"></el-table-column>
            <el-table-column label="序号" width="60">
              <template slot-scope="scope">
                <span>{{ (pagination.page - 1) * pagination.pageSize + scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="staffName" show-overflow-tooltip label="姓名"></el-table-column>
            <el-table-column prop="sex" label="性别" show-overflow-tooltip>
              <template slot-scope="scope">
                {{ scope.row.sex == 1 ? '男' : scope.row.sex == 0 ? '女' : '' }}
              </template>
            </el-table-column>
            <el-table-column prop="phone" label="手机号" show-overflow-tooltip></el-table-column>
            <el-table-column prop="age" show-overflow-tooltip label="年龄"></el-table-column>
            <el-table-column prop="" show-overflow-tooltip label="学历"></el-table-column>
            <el-table-column prop="certDictName" label="证书" show-overflow-tooltip></el-table-column>
            <el-table-column prop="unit" show-overflow-tooltip label="归属单位"></el-table-column>
            <el-table-column label="操作" width="130px">
              <template #default="{ row }">
                <el-button type="text" @click="onOperate(OperateType.VIEW, row)">查看</el-button>
                <el-button type="text" class="text-red" @click="onOperate(OperateType.DELETE, row)">移除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination
          :layout="pagination.layoutOptions"
          :current-page="pagination.page"
          :page-sizes="pagination.pageSizeOptions"
          :page-size="pagination.pageSize"
          :total="pagination.total"
          @size-change="paginationSizeChange"
          @current-change="paginationCurrentChange"
        ></el-pagination>
      </div>
      <memberDialog
        v-if="isMemberDialog"
        :memberShow="isMemberDialog"
        :postCode="checkedData.code"
        @submitMemberDialog="submitMemberDialog"
        @closeMemberDialog="() => (isMemberDialog = false)"
      />
    </template>
  </PageContainer>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin'
import memberDialog from '../components/selectPersons.vue'
export default {
  name: 'positionManage',
  components: {
    memberDialog
  },
  mixins: [tableListMixin],
  data() {
    return {
      // 树搜索关键字
      treeSearchKeyWord: '',
      treeLoading: false, // tree loading
      treeData: [], // tree数据
      defaultProps: {
        children: 'child',
        label: 'name'
      },
      tableLoading: false,
      tableData: [],
      // 选中的行
      selectionList: [],
      // 搜索表单
      searchForm: {
        // 搜索姓名、工号、手机号
        name: '',
        showType: 1 // 类型
      },
      // 表单搜索项
      formOptions: {
        // 类型
        showType: [
          {
            label: '仅展示直属成员',
            value: 2
          },
          {
            label: '展示全部成员',
            value: 1
          }
        ]
      },
      isMemberDialog: false, // 人员
      checkedData: {}, // 选中数据
      pagination: {
        page: 1,
        pageSize: 15,
        total: 0,
        layoutOptions: 'total, sizes, prev, pager, next, jumper',
        pageSizeOptions: [10, 15, 20, 50, 100]
      }
    }
  },
  computed: {
    OperateType() {
      return {
        // 增加
        ADD: 'add',
        // 批量删除
        BATCH_DELETE: 'batch_delete',
        // 删除
        DELETE: 'delete',
        // 详情
        VIEW: 'view'
      }
    },
    checkedIds() {
      return this.selectionList.map((it) => it.id)
    }
  },
  watch: {
    treeSearchKeyWord(val) {
      this.$refs.treeRef.filter(val)
    }
  },
  mounted() {
    this.getTreeData()
  },
  methods: {
    // 获取tree数据
    getTreeData() {
      this.treeLoading = true
      this.$api.supplierAssess.getPostChildData().then((res) => {
        this.treeLoading = false
        if (res.code == '200' && res.data.length > 0) {
          this.treeData = res.data
          this.checkedData = this.treeData[0]
          this.$nextTick(() => {
            this.$refs.treeRef.setCurrentKey(this.checkedData.code)
          })
          this.getDataList()
        }
      })
    },
    // 树节点过滤方法
    filterNode(value, data) {
      if (!value) return true
      return data.name.includes(value)
    },
    // 点击搜索
    onSearch() {
      // 重置页码
      this.pagination.page = 1
      this.getDataList()
    },
    // 点击重置，重置表单，然后触发搜索
    onReset() {
      this.$refs.formRef.resetFields()
      this.pagination.page = 1
      this.onSearch()
    },
    handleNodeClick(data) {
      this.checkedData = data
      this.pagination.page = 1
      this.getDataList()
    },
    // 成员确认
    submitMemberDialog(list) {
      let ids = list.map((item) => {
        return item.staffId
      })
      let params = {
        staffId: ids.join(','),
        code: this.checkedData.code
      }
      this.$api.supplierAssess.addPersonToPostData(params).then((res) => {
        if (res.code === '200') {
          this.$message.success(res.msg)
          this.isMemberDialog = false
          this.getDataList()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 新增岗位
    addPost() {
      this.$router.push({
        name: 'positionAdd',
        query: {
          type: 'add'
        }
      })
    },
    // 树操作
    clickMenu(funcName, val) {
      if (funcName === 'addJunior') {
        this.$router.push({
          name: 'positionAdd',
          query: {
            type: 'add',
            parentCode: val.code
          }
        })
      } else if (funcName === 'edit') {
        this.$router.push({
          name: 'positionAdd',
          query: {
            type: 'edit',
            code: val.code
          }
        })
      } else if (funcName === 'remove') {
        this.$confirm('是否确认删除所选岗位?', '确定删除', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          if (val.child && val.child.length > 0) {
            this.$alert('存在子级节点的数据无法删除', '无法删除', {
              confirmButtonText: '确定',
              callback: (action) => {
                this.$message({
                  type: 'info',
                  message: '已取消删除'
                })
              }
            })
          } else {
            this.$api.supplierAssess.deletePostData({ code: val.code }, { 'operation-type': 3, 'operation-name': val.name, 'operation-id': val.code }).then((res) => {
              if (res.code === '200') {
                this.$message.success(res.msg)
                this.getTreeData()
              } else {
                this.$message.error(res.msg)
              }
            })
          }
        })
      }
    },
    // 分页获取数据
    getDataList() {
      this.tableData = []
      this.tableLoading = true
      let data = {
        page: this.pagination.page,
        pageSize: this.pagination.pageSize,
        ...this.searchForm,
        postCode: this.checkedData.code
      }
      this.$api.supplierAssess
        .queryPersonByPostByPage(data)
        .then((res) => {
          this.tableLoading = false
          if (res.code == '200') {
            if (res.data.records && res.data.records.length) {
              res.data.records.forEach((e) => {
                if (e.birthDate) {
                  e.age = this.getAge(e.birthDate)
                }
              })
            }
            this.tableData = res.data.records
            this.pagination.total = res.data.total
          } else {
            this.tableData = []
            this.pagination.total = 0
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    // 计算年龄
    getAge(birthDate) {
      // 创建一个 Date 对象表示当前日期
      const currentDate = new Date()
      // 获取当前年份
      const currentYear = currentDate.getFullYear()
      // 获取当前月份
      const currentMonth = currentDate.getMonth()
      // 获取当前日期
      const currentDay = currentDate.getDate()
      // 获取出生年份
      const birthYear = new Date(birthDate).getFullYear()
      // 获取出生月份
      const birthMonth = new Date(birthDate).getMonth()
      // 获取出生日期
      const birthDay = new Date(birthDate).getDate()
      // 计算年龄
      let age = currentYear - birthYear
      // 检查生日是否已过
      if (currentMonth < birthMonth || (currentMonth === birthMonth && currentDay < birthDay)) {
        age--
      }
      return age
    },
    // 表格相关操作总线处理
    onOperate(type, row) {
      switch (type) {
        case 'add':
          if (!this.checkedData.code) return this.$message.error('请先选择岗位')
          this.isMemberDialog = true
          break
        case 'batch_delete':
          this.$confirm('是否移除人员?', '提示', {
            cancelButtonClass: 'el-button--primary is-plain',
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            let ids = this.selectionList.map((it) => it.staffId)
            this.$api.supplierAssess.removePersonToPostData({ staffId: ids.join(','), code: this.checkedData.code }).then((res) => {
              if (res.code === '200') {
                this.$message.success(res.msg)
                this.getDataList()
              } else {
                this.$message.error(res.msg)
              }
            })
          })
          break
        case 'delete':
          this.$confirm('是否移除该人员?', '提示', {
            cancelButtonClass: 'el-button--primary is-plain',
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.$api.supplierAssess.removePersonToPostData({ staffId: row.staffId, code: this.checkedData.code }).then((res) => {
              if (res.code === '200') {
                this.$message.success(res.msg)
                this.getDataList()
              } else {
                this.$message.error(res.msg)
              }
            })
          })
          break
        case 'view':
          this.$router.push({
            name: 'memberDetail',
            query: {
              type: 'view',
              staffId: row.staffId
            }
          })
          break
      }
    },
    paginationSizeChange(val) {
      this.pagination.pageSize = val
      this.getDataList()
    },
    paginationCurrentChange(val) {
      this.pagination.page = val
      this.getDataList()
    }
  }
}
</script>
<style lang="scss" scoped>
.positionManage-list {
  ::v-deep(.container-content) {
    display: flex;
    flex-flow: row nowrap;
    background-color: #fff;
  }
  &__left {
    width: 246px;
    height: 100%;
    padding: 16px;
    border-right: solid 1px #eee;
    text-align: center;
    &__tree {
      height: 100%;
      overflow: hidden;
    }
    ::v-deep(.el-tree) {
      position: relative;
      margin-top: 16px;
      height: calc(100% - 108px);
      overflow: auto;
      .el-tree-node__content {
        line-height: 32px;
        height: 32px;
      }
      .el-tree-node.is-current > .el-tree-node__content {
        color: #3562db;
        background: #e6effc;
      }
      .space-tree {
        height: 100%;
        &__search {
          padding-right: 16px;
        }
      }
      .custom-tree-node {
        width: 100%;
        overflow: hidden;
        .custom-tree-node-wrapper {
          display: flex;
          width: 100%;
          justify-content: space-between;
          .custom-tree-node-label {
            display: inline-block;
            width: 70%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            text-align: left;
          }
        }
      }
      .rotate {
        cursor: pointer;
        margin-left: 5px;
        transform: rotate(90deg);
      }
    }
  }
  &__right {
    flex: 1;
    overflow: hidden;
    padding: 16px;
    background: #fff;
    display: flex;
    flex-direction: column;
  }
  &__form {
    ::v-deep(.el-form-item) {
      margin-bottom: 0;
      .el-form-item__content {
        line-height: 1;
      }
    }
  }
  .text-red {
    color: #ff1919;
  }
  &__table_actions {
    margin: 16px 0;
  }
  &__table {
    flex: 1;
    overflow: hidden;
  }
  .el-pagination {
    margin-top: 10px;
  }
}
::v-deep .el-dropdown-menu__item--divided {
  border: 1px solid #ffffff !important;
}
::v-deep.el-dropdown-menu__item {
  color: #666666 !important;
  &:hover {
    background: #f6f5fa !important;
    color: #3562db !important;
  }
}
::v-deep .el-dropdown-menu--small .el-dropdown-menu__item.el-dropdown-menu__item--divided:before {
  display: none !important;
}
::v-deep .el-dropdown-menu__item--divided:before {
  display: none !important;
}
::v-deep .el-dropdown-menu--small .el-dropdown-menu__item,
::v-deep .el-dropdown-menu__item {
  padding: 0px !important;
}
.removeNode {
  color: #f53f3f !important;
  &:hover {
    background: #f6f5fa !important;
    color: #f53f3f !important;
  }
}
</style>
