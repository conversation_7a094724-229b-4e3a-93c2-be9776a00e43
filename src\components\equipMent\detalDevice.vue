<template>
  <PageContainer>
    <div slot="content" class="table-content">
      <div v-if="assetId" class="content_box">
        <el-form ref="formInline" :model="formInline" :inline="true" class="form-inline" label-width="100px">
          <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
            <span class="green_line"></span>
            基本信息
          </div>
          <el-form-item label="资产名称：" prop="assetName">
            <span>{{ formInline.assetName }}</span>
          </el-form-item>
          <el-form-item label="资产编码：" prop="assetCode">
            <span>{{ formInline.assetCode }}</span>
          </el-form-item>
          <el-form-item label="品牌：" prop="assetBrand">
            <span>{{ formInline.assetBrand }}</span>
          </el-form-item>
          <el-form-item label="型号：" prop="assetModel">
            <span>{{ formInline.assetModel }}</span>
          </el-form-item>
          <el-form-item label="生产日期：" prop="dateOfManufacture">
            <span>{{ formInline.dateOfManufacture }}</span>
          </el-form-item>
          <el-form-item label="SN码：" prop="assetSn">
            <span>{{ formInline.assetSn }}</span>
          </el-form-item>
          <br />
          <el-form-item label="NFC：" prop="nfcCode">
            <span>{{ formInline.nfcCode }}</span>
          </el-form-item>
          <!-- rfid -->
          <el-form-item label="RFID：" prop="rfid">
            <span>{{ formInline.rfid }}</span>
          </el-form-item>
          <br />
          <el-form-item label="所在区域：" prop="regionCode">
            <span>{{ formInline.regionName }}</span>
          </el-form-item>
          <br />
          <el-form-item label="计量单位：" prop="unitOfMeasurementCode">
            <span>{{ formInline.unitOfMeasurement }}</span>
          </el-form-item>
          <el-form-item label="启用日期：" prop="startDate">
            <span>{{ formInline.startDate }}</span>
          </el-form-item>
          <el-form-item label="使用期限：" prop="serviceLife">
            <span>{{ formInline.serviceLife }}</span>
          </el-form-item>
          <el-form-item label="资产状态：" prop="assetStatusCode">
            <span>{{ formInline.assetStatusName }}</span>
          </el-form-item>
          <el-form-item label="使用时长：" prop="useDuration">
            <span>{{ formInline.useDuration }}</span>
          </el-form-item>
          <el-form-item label="是否危险源：" prop="">
            <span>{{ formInline.isDangerSource == 1 ? '是' : '否' }}</span>
          </el-form-item>
          <br />
          <el-form-item style="display: block" label="备注说明：" prop="assetsRemarks">
            <span>{{ formInline.assetsRemarks }}</span>
          </el-form-item>
          <!-- 资产归口部门 -->
          <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
            <span class="green_line"></span>
            资产归口部门
          </div>
          <el-form-item label="归口部门：" prop="centralizedDepartmentCode">
            <span>{{ formInline.centralizedDepartmentName }}</span>
          </el-form-item>
          <!-- 资产国标分类 -->
          <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
            <span class="green_line"></span>
            资产国标分类
          </div>
          <el-form-item label="资产大类：" prop="assetCategoryCode">
            <span>{{ formInline.assetCategoryName }}</span>
          </el-form-item>
          <el-form-item label="资产小类：" prop="assetSubcategoryCode">
            <span>{{ formInline.assetSubcategoryName }}</span>
          </el-form-item>
          <!-- 类别信息 -->
          <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
            <span class="green_line"></span>
            类别信息
          </div>
          <el-form-item label="专业类别：" prop="professionalCategoryCode">
            <span>{{ formInline.professionalCategoryName }}</span>
          </el-form-item>
          <el-form-item label="系统类别：" prop="systemCategoryCode">
            <span>{{ formInline.systemCategoryName }}</span>
          </el-form-item>
          <!-- 使用信息 -->
          <div class="toptip" style="padding: 0; border: 0; font-weight: bold"><span class="green_line"></span>使用信息
          </div>
          <el-form-item label="使用部门：" prop="userDepartmentId">
            <span>{{ formInline.userDepartmentName }}</span>
          </el-form-item>
          <el-form-item style="display: block" label="备注：" prop="remarks">
            <span>{{ formInline.remarks }}</span>
          </el-form-item>
          <el-form-item label="资产图片：" prop="assetImages">
            <template>
              <el-image v-if="fileList.length > 0" style="width: 100px; height: 100px" :src="fileList[0].url"
                :preview-src-list="fileList.map((i) => i.url)"> </el-image>
              <span v-else>暂无</span>
            </template>
          </el-form-item>
          <!-- 模型信息 -->
          <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
            <span class="green_line"></span>
            模型信息
          </div>
          <el-form-item label="模型编码：" prop="modelCode">
            <span>{{ formInline.modelCode }}</span>
          </el-form-item>
          <!-- 模型信息 -->
          <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
            <span class="green_line"></span>
            业务信息
          </div>
          <el-form-item label="主机号：" prop="deviceHostNumber">
            <span>{{ formInline.deviceHostNumber }}</span>
          </el-form-item>
          <el-form-item label="设备号：" prop="equipmentNo">
            <span>{{ formInline.equipmentNo }}</span>
          </el-form-item>
          <el-form-item label="关联摄像机：" prop="cameraName">
            <template v-if="formInline.cameraName">{{ formInline.cameraName }}</template>
            <span v-else>暂无</span>
          </el-form-item>
          <br />
          <el-form-item label="用户名：" prop="deviceUsername">
            <span>{{ formInline.deviceUsername }}</span>
          </el-form-item>
          <el-form-item label="密码：" prop="devicePassword">
            <span>{{ formInline.devicePassword }}</span>
          </el-form-item>
          <el-form-item label="端口号：" prop="devicePortNumber">
            <span>{{ formInline.devicePortNumber }}</span>
          </el-form-item>
          <el-form-item label="通道号：" prop="deviceChannelNumber">
            <span>{{ formInline.deviceChannelNumber }}</span>
          </el-form-item>
          <el-form-item label="设备IP：" prop="deviceIp">
            <span>{{ formInline.deviceIp }}</span>
          </el-form-item>
          <el-form-item label="对接方式：" prop="dockingMethodCode">
            <span>{{ formInline.dockingMethodCode }}</span>
          </el-form-item>
          <el-form-item label="厂家：" prop="equipmentManufacturerCode">
            <span>{{
              formInline.equipmentManufacturerCode == 1 ? '宇视' : formInline.equipmentManufacturerCode == 2 ? '海康' :
                formInline.equipmentManufacturerCode == 3 ? '大华' : '华为'
            }}</span>
          </el-form-item>
          <el-form-item label="上级设备：" prop="equipmentManufacturerCode">
            <template>
              <span>{{ formInline.superiorDeviceName }}</span>
            </template>
          </el-form-item>
          <el-form-item label="视频地址：" prop="mainCodeVideoAddress">
            <span>{{ formInline.mainCodeVideoAddress }}</span>
          </el-form-item>
        </el-form>
      </div>
      <div v-else style="margin-top: 20px; font-size: 16px; font-weight: bold">暂无关联设备资产</div>
    </div>
  </PageContainer>
</template>
<script>
export default {
  name: 'addDevice',
  components: {},
  data() {
    return {
      formInline: {
        assetName: '',
        assetCode: '',
        assetBrand: '',
        assetModel: '',
        dateOfManufacture: '',
        assetSn: '',
        nfcCode: '',
        rfid: '',
        regionCode: [],
        unitOfMeasurementCode: '',
        superiorDeviceName: '', // 上级设备名称
        superiorDeviceCode: '', // 上级设备编码
        useDuration: '', // 使用时长
        startDate: '',
        serviceLife: '',
        assetStatusCode: '',
        isDangerSource: '0', // 是否为危险源
        centralizedDepartmentCode: '',
        assetCategoryCode: '',
        assetSubcategoryCode: '',
        professionalCategoryCode: '',
        systemCategoryCode: [],
        modelCode: '',
        deviceHostNumber: '', // 主机号
        equipmentNo: '', // 设备号
        cameraCode: '', // 关联摄像机code
        cameraName: '', // 关联摄像机名称
        deviceUsername: '', // 用户名
        devicePassword: '', // 密码
        devicePortNumber: '', // 端口号
        deviceChannelNumber: '', // 通道号
        deviceIp: '', // 设备IP
        dockingMethodCode: '', // 设备对接方式Code
        dockingMethodName: '', // 设备对接方式
        equipmentManufacturerCode: '', // 厂家Code
        equipmentManufacturerName: '', // 厂家
        mainCodeVideoAddress: '', // 视频地址（主码）
        assetsRemarks: '' // 资产备注说明
      },
      fileList: [],
      hideUpload: false,
      assetId: ''
    }
  },
  created() {
    this.assetId = this.$route.query.assetId
  },
  mounted() {
    if (!this.assetId) return
    this.getDetails()
  },
  methods: {
    // 获取资产详情
    getDetails() {
      this.$api.getAssetDetailsByAssetIds({ assetsId: this.assetId }).then((res) => {
        if (res.code == '200') {
          this.formInline = { ...res.data }
          const { assetImagesUrl } = res.data
          // 处理图片回显
          if (assetImagesUrl) {
            this.fileList = [
              {
                url: this.$tools.imgUrlTranslation(assetImagesUrl)
              }
            ]
          }
          this.hideUpload = !!assetImagesUrl
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.table-content {
  background: #fff;
  border-radius: 4px;
  height: 100%;
  padding: 10px;
  margin-top: -30px;
  overflow-y: auto;
}

.content_box {
  margin-top: 10px;
  background: #fff;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.form-btn-btn {
  padding: 4px 10px;
  margin: 0 8px;
}

.camera-tag {
  background: #f6f5fa;
  border-radius: 4px;
  font-size: 14px;
  font-family: 'PingFang SC-Regular', 'PingFang SC';
  font-weight: 400;
  color: #121f3e;
  border: none;
  margin-right: 8px;

  ::v-deep .el-tag__close {
    color: #121f3e;

    &:hover {
      color: #fff;
      background-color: #3562db;
    }
  }
}

// 隐藏上传
.hide {
  ::v-deep .el-upload--picture-card {
    display: none;
  }
}

.toptip {
  margin-bottom: 10px;
}

.form-inline {

  .el-input,
  .el-select,
  .el-cascader {
    width: 300px;
  }
}

.form-inline .width_lengthen {
  width: 300px;
}

.form-inline .cascaderWid {
  width: 710px;
}

.detailClass ::v-deep .el-input__inner {
  border: none !important;
}

.detailClass ::v-deep .el-input__suffix-inner {
  display: none !important;
}

.detailClass ::v-deep .el-input-group__append {
  display: none !important;
}
</style>
