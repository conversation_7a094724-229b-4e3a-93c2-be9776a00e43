<script>
import tableListMixin from '@/mixins/tableListMixin'
import LightingCard from './LightingCard'
export default {
  name: 'LightingList',
  components: {
    LightingCard
  },
  mixins: [tableListMixin],
  props: {
    query: Object
  },
  data: () => ({
    tableLoading: false,
    dataList: []
  }),
  methods: {
    // 供外部组件调用的搜索方法
    search() {
      this.pagination.current = 1
      this.$nextTick(this.getDataList)
    },
    // 搜索数据，mixin混入的pagination会调用此名称的方法
    getDataList() {
      this.tableLoading = true
      const params = Object.assign({}, this.query, {
        pageSize: this.pagination.size,
        page: this.pagination.current
      })
      this.$api
        .getRealMonitoringList(params)
        .then((res) => {
          if (res.code === '200') {
            this.dataList = res.data.list || []
            this.pagination.total = res.data.totalCount || 0
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '查询监测实体失败'))
        .finally(() => (this.tableLoading = false))
    },
    // 调用接口更改设备参数状态
    onUpdateParameterValue(params) {
      this.tableLoading = true
      this.$api.setControl(params)
        .then(res => {
          if (res.code === '200' && res.data) {
            this.$message.success('操作成功')
            // 更新成功之后，重新查询数据
            this.getDataList()
          } else {
            throw res.message
          }
        })
        .catch(msg => this.$message.error(msg))
        .finally(() => {
          this.tableLoading = false
        })
    }
  }
}
</script>
<template>
  <div v-loading="tableLoading" class="component LightingList">
    <template>
      <div class="LightingList__content">
        <div v-if="pagination.total" class="LightingList__content__wrapper">
          <LightingCard v-for="entity of dataList" :key="entity.surveyEntityCode" :data="entity" @update="onUpdateParameterValue" />
        </div>
        <div v-else class="LightingList__empty">
          <img src="@/assets/images/monitor/null.png" alt="" />
          <div>暂无数据~</div>
        </div>
      </div>
      <div class="LightingList__footer">
        <el-pagination
          :current-page="pagination.current"
          :page-sizes="pagination.pageSizeOptions"
          :page-size="pagination.size"
          :layout="pagination.layoutOptions"
          :total="pagination.total"
          @size-change="paginationSizeChange"
          @current-change="paginationCurrentChange"
        />
      </div>
    </template>
  </div>
</template>
<style scoped lang="scss">
.LightingList {
  height: 100%;
  &__content {
    height: calc(100% - 48px);
    overflow: hidden;
    background: #fff;
    border-radius: 4px;
    padding: 10px 0 10px 10px;
    &__wrapper {
      height: 100%;
      overflow: auto;
    }
  }
  .LightingCard {
    margin-right: 12px;
    margin-bottom: 12px;
    display: inline-block;
  }
  &__footer {
    height: 60px;
    padding-top: 16px;
  }
  &__empty {
    text-align: center;
    user-select: none;
    img {
      -webkit-user-drag: none;
    }
  }
}
</style>
