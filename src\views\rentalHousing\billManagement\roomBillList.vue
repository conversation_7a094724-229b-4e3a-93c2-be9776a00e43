<template>
  <div class="room-bill-list">
    <div class="top_content">
      <el-input
        v-model="filterData.userName"
        style="width: 180px; margin-right: 8px"
        placeholder="搜索承租人"
        maxlength="60"
        onkeyup="if(value.length>25)value=value.slice(0,60)"
      ></el-input>
      <el-input
        v-model="filterData.tenantDeptName"
        style="width: 150px; margin-right: 8px"
        placeholder="所在部门"
        maxlength="60"
        onkeyup="if(value.length>25)value=value.slice(0,60)"
      ></el-input>
      <el-input
        v-model="filterData.tenantNumber"
        style="width: 180px; margin-right: 8px"
        placeholder="工号"
        maxlength="60"
        onkeyup="if(value.length>25)value=value.slice(0,60)"
      ></el-input>
      <el-date-picker v-model="filterData.date" type="daterange" style="margin-right: 8px" start-placeholder="账单周期" range-separator="至" end-placeholder="账单周期">
      </el-date-picker>
      <el-cascader
        ref="spaceIds"
        style="width: 180px; margin-right: 8px; line-height: 32px"
        v-model="filterData.spaceIds"
        filterable
        :show-all-levels="false"
        placeholder="空间位置"
        :options="icmSpaceArray"
        :props="propsSpace"
        clearable
        @change="spaceChang(filterData.spaceIds)"
      ></el-cascader>
      <el-input v-model="filterData.houseName" style="width: 180px" placeholder="房间名称" maxlength="60" onkeyup="if(value.length>25)value=value.slice(0,60)"></el-input>
      <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db; margin-left: 20px" @click="resetCondition">重置</el-button>
      <el-button type="primary" style="font-size: 14px" @click="searchByCondition">查询</el-button>
    </div>
    <div class="view" v-if="tableData.length">
      <div class="button-group" style="margin-bottom: 14px">
        <el-button type="primary" style="font-size: 14px" @click="exportData">导出</el-button>
      </div>
      <div class="table-title-div">
        <div class="table-title">公租房账单表</div>
        <div class="table-month">月份：{{ startMonth }}-{{ endMonth }}</div>
      </div>
      <div class="table_list" style="text-align: right; height: calc(100% - 200px)">
        <el-table
          ref="materialTable"
          v-loading="tableLoading"
          :data="tableData"
          height="100%"
          border
          :header-cell-style="{ background: '#F6F5FA' }"
          style="width: 100%"
          :cell-style="{ padding: '8px 0 8px 0' }"
          stripe
          highlight-current-row
          :empty-text="emptyText"
          row-key="id"
        >
          <el-table-column label="序号" type="index" width="70">
            <template slot-scope="scope">
              <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="userName" label="姓名"></el-table-column>
          <el-table-column show-overflow-tooltip prop="tenantNumber" label="工号"></el-table-column>
          <el-table-column show-overflow-tooltip prop="tenantDeptName" label="所在部门"></el-table-column>
          <el-table-column show-overflow-tooltip prop="houseName" label="房号"></el-table-column>
          <el-table-column show-overflow-tooltip prop="rentingSquareMeter" label="房屋面积（㎡）"></el-table-column>
          <el-table-column show-overflow-tooltip prop="rentingMoney" label="租金（元）"></el-table-column>
        </el-table>
      </div>
      <div class="" style="padding-top: 10px; text-align: right">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="paginationData.currentPage"
          :page-sizes="[15, 30, 50, 100]"
          :page-size="paginationData.pageSize"
          :total="paginationData.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
    <div class="empty" v-else>
      <el-empty description="暂无数据" :image-size="200"></el-empty>
    </div>
  </div>
</template>
<script>
import axios from 'axios'
import { transData } from '@/util'
import moment from 'moment'
export default {
  data() {
    return {
      moment,
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      tableData: [],
      // 列表过滤条件
      filterData: {
        date: [], // 时间范围，绑定到日期选择器，用于选择账单的开始和结束日期
        userName: '', // 承租人名称，绑定到输入框用于搜索承租人
        tenantNumber: '', // 工号
        spaceIds: '', // 空间位置，绑定到 Cascader 级联选择器
        houseName: '', // 房间名称，绑定到输入框用于搜索房间名称
        tenantDeptName: ''
      },
      emptyText: '暂无数据',
      tableLoading: false,
      pageSize: '',
      currentPage: '',
      propsSpace: {
        checkStrictly: true,
        emitPath: false,
        value: 'id',
        label: 'spaceName',
        children: 'list'
      },
      icmSpaceArray: [],
      spaceList: [],
      expiringCount: 0,
      startMonth: '',
      endMonth: ''
    }
  },

  mounted() {
    this.spaceTreeListFn()
  },
  methods: {
    // 获取账单列表
    getBillList() {
      const params = {
        pageNum: this.paginationData.currentPage, //页码
        pageSize: this.paginationData.pageSize, //	页大小
        houseName: this.filterData.houseName, //房源名称
        userName: this.filterData.userName, //承租人名称
        spaceIds: this.filterData.spaceIds, //空间code
        tenantNumber: this.filterData.tenantNumber, // 工号
        tenantDeptName: this.filterData.tenantDeptName,
        billStartCycle: this.filterData.date && this.filterData.date.length > 0 ? moment(this.filterData.date[0]).format('YYYY-MM-DD') : '',
        billEndCycle: this.filterData.date && this.filterData.date.length > 0 ? moment(this.filterData.date[1]).format('YYYY-MM-DD') : ''
      }
      this.tableLoading = true
      this.$api.rentalHousingApi.getBillList(params).then((res) => {
        if (res.code == '200') {
          this.tableData = res.data.records
          this.paginationData.total = res.data.total
        } else {
          this.$message.error(res.message)
        }
        this.tableLoading = false
      })
    },
    // 条件查询
    searchByCondition() {
      this.paginationData.currentPage = 1
      if (this.filterData.date.length) {
        this.startMonth = moment(this.filterData.date[0]).format('YYYY年MM月')
        this.endMonth = moment(this.filterData.date[1]).format('YYYY年MM月')
        this.getBillList()
      } else {
        this.$message.error('请选择账单周期')
      }
    },
    // 重置查询条件
    resetCondition() {
      this.paginationData = {
        currentPage: 1,
        pageSize: 15,
        total: 0
      }
      this.filterData = {
        date: [], // 时间范围，绑定到日期选择器，用于选择账单的开始和结束日期
        userName: '', // 承租人名称，绑定到输入框用于搜索承租人
        tenantNumber: '', // 工号
        spaceIds: '', // 空间位置，绑定到 Cascader 级联选择器
        houseName: '', // 房间名称，绑定到输入框用于搜索房间名称
        tenantDeptName: ''
      }
      this.tableData = []
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.getBillList()
    },
    // 每页的条数
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.paginationData.currentPage = 1
      this.getBillList()
    },
    /** 导出 */
    exportData() {
      const userInfo = this.$store.state.user.userInfo.user
      const params = {
        unitCode: userInfo.unitCode ?? 'BJSYGJ',
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        houseName: this.filterData.houseName, //房源名称
        userName: this.filterData.userName, //承租人名称
        spaceIds: this.filterData.spaceIds, //空间code
        tenantNumber: this.filterData.tenantNumber, // 工号
        tenantDeptName: this.filterData.tenantDeptName,
        billStartCycle: this.filterData.date && this.filterData.date.length > 0 ? moment(this.filterData.date[0]).format('YYYY-MM-DD') : '',
        billEndCycle: this.filterData.date && this.filterData.date.length > 0 ? moment(this.filterData.date[1]).format('YYYY-MM-DD') : ''
      }
      axios({
        method: 'get',
        url: __PATH.VUE_RHMS_API + 'bill/deductionTableExportExcel',
        // data: formData,
        params: params,
        responseType: 'blob',
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token,
          'operation-type': 4,
          unitCode: userInfo.unitCode ?? 'BJSYGJ',
          hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY'
        }
      })
        .then((res) => {
          const name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
          const blob = new Blob([res.data])
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
        })
        .catch((err) => {
          console.log(err, 'err')
          this.$message.error('下载失败')
        })
    },
    // 空间位置 change事件
    spaceChang(val) {
      if (val) {
        this.spaceList.forEach((item) => {
          if (item.id == val) {
            this.filterData.spaceName = item.spaceName || item.id
          }
        })
      } else {
        this.filterData.spaceName
      }
    },
    // 获取空间位置
    spaceTreeListFn() {
      this.$api.spatialInformationTree().then((res) => {
        if (res.code == 200) {
          this.spaceList = res.data
          this.icmSpaceArray = transData(res.data, 'id', 'parentId', 'list')
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.room-bill-list {
  height: 100%;
  .view,
  .empty {
    height: calc(100% - 42px);
  }
  .empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  .top_content {
    width: 100%;
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    > :deep(.el-input) {
      width: 10% !important;
    }

    > :deep(.el-select) {
      margin-right: 20px;
    }
  }
  .table-title-div {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-bottom: 15px;
    .table-title {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 10px;
    }
    .table-month {
      font-size: 20px;
    }
  }
}
</style>