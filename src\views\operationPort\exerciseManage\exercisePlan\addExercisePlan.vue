<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="addExercisePlan-content-title" @click="goBack"><i class="el-icon-arrow-left"></i><span
          style="margin-left: 10px">演练计划新增</span></div>
      <div class="content_box">
        <div class="top-title">基础信息</div>
        <el-form ref="formInline" :model="addForm" :inline="true" class="form-inline" :rules="rules" label-width="auto">
          <el-form-item label="演练计划名称" prop="drillName">
            <el-input v-model.trim="addForm.drillName" maxlength="20" type="text" placeholder="请输入演练计划名称，最多输入20个字"
              class="inputWid"></el-input>
          </el-form-item>
          <el-form-item label="演练类型" prop="drillType">
            <el-select v-model.trim="addForm.drillType" filterable clearable placeholder="演练类型" class="inputWid">
              <el-option v-for="item in exerciseTypeArr" :key="item.id" :label="item.labelName" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="关联常规预案">
            <el-select v-model="addForm.planBasicId" placeholder="选择关联常规预案" class="inputWid">
              <el-option v-for="item in prePlanOptions" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="参演部门" class="is-required">
            <!-- <div class="deptName"></div> -->
            <div class="rightBox">
              <div class="operation" @click="toDepet">
                <img src="@/assets/images/operationPort/addPlan.png" />
                添加
              </div>
              <div class="rightBox-bottom">
                <div class="dep-box">
                  <div v-for="(val, idx) in deptData" :key="idx" class="dep-small-box">
                    <i>{{ val.deptName }}</i>
                    <img src="@/assets/images/operationPort/deleteDept.png" @click="delDept(idx)" />
                  </div>
                </div>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="演练责任人" class="is-required">
            <!-- <div class="deptName"></div> -->
            <div class="rightBox">
              <div class="operation" @click="toPerson">
                <img src="@/assets/images/operationPort/addPlan.png" />
                添加
              </div>
              <div class="rightBox-bottom">
                <div class="dep-box">
                  <div v-for="(val, idx) in personData" :key="idx" class="dep-small-box">
                    <i>{{ val.staffName }}</i>
                    <img src="@/assets/images/operationPort/deleteDept.png" @click="delPerson(idx)" />
                  </div>
                </div>
              </div>
            </div>
          </el-form-item>
          <br />
          <el-form-item label="周期类型" prop="cycleType">
            <el-select v-model="addForm.cycleType" placeholder="请选择周期类型" class="inputWid" @change="changeCycleType ">
              <el-option v-for="item in typeOptions" :key="item.cycleType" :label="item.label" :value="item.cycleType">
              </el-option>
            </el-select>
          </el-form-item>
          <template v-if="addForm.cycleType ===0||addForm.cycleType ===5">
            <el-form-item key="planStartTime" label="计划生效期限" prop="planStartTime">
              <el-date-picker v-model="addForm.planStartTime" type="date" placeholder="请选择开始日期" format="yyyy-MM-dd"
                value-format="yyyy-MM-dd" class="inputWid" @change="planStartTimeChange">
              </el-date-picker>
            </el-form-item>
          </template>
          <template v-else>
            <el-form-item key="timeInterval" label="计划生效期限" prop="timeInterval">
              <el-date-picker v-model="addForm.timeInterval" type="daterange" start-placeholder="开始日期"
                format="yyyy-MM-dd" value-format="yyyy-MM-dd" range-separator="至" end-placeholder="结束日期"
                placeholder="选择时间范围" class="inputWid" @change="planStartTimeChange">
              </el-date-picker>
            </el-form-item>
          </template>
          <template v-if="addForm.cycleType ===3||addForm.cycleType ===4">
            <el-form-item key="startMonth" label="开始月份" prop="startMonth">
              <el-select v-model="addForm.startMonth" placeholder="选择开始月份" class="inputWid" @change="startMonthChange">
                <el-option v-for="item in startMonthArr" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item key="endMonth" label="结束月份" prop="endMonth">
              <el-select v-model="addForm.endMonth" placeholder="选择结束月份" class="inputWid">
                <el-option v-for="item in endMonthArr" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </template>
          <template v-if="addForm.cycleType === 0">
            <el-form-item key="exerciseDate" label="演练日期" prop="exerciseDate">
              <el-date-picker v-model="addForm.exerciseDate" type="daterange" start-placeholder="开始日期"
                range-separator="至" end-placeholder="结束日期" placeholder="选择时间范围" format="yyyy-MM-dd"
                value-format="yyyy-MM-dd" class="inputWid">
              </el-date-picker>
            </el-form-item>
          </template>
          <template v-if="addForm.cycleType === 5">
            <el-form-item key="exerciseStartsDate" label="演练开始日期" prop="exerciseStartsDate">
              <el-date-picker v-model="addForm.exerciseStartsDate" type="date" placeholder="选择开始日期" format="yyyy-MM-dd"
                value-format="yyyy-MM-dd" class="inputWid" @change="startYearDateChange">
              </el-date-picker>
            </el-form-item>
            <el-form-item key="exerciseEndsDate" label="演练结束日期" prop="exerciseEndsDate">
              <el-date-picker v-model="addForm.exerciseEndsDate" type="date" placeholder="选择结束日期" format="yyyy-MM-dd"
                disabled value-format="yyyy-MM-dd" class="inputWid">
              </el-date-picker>
            </el-form-item>
          </template>
          <template
            v-if="addForm.cycleType === 1 ||addForm.cycleType === 2||addForm.cycleType === 3||addForm.cycleType === 4">
            <el-form-item key="startDate" label="演练开始日期" prop="startDate">
              <el-select v-model="addForm.startDate" placeholder="选择日期" class="inputWid" @change="startDateChange">
                <el-option v-for="item in startDateArr" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item key="endDate" label="演练结束日期" prop="endDate">
              <el-select v-model="addForm.endDate" placeholder="选择日期" class="inputWid">
                <el-option v-for="item in endDateArr" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </template>
          <el-form-item label="演练组织人" prop="organizerId">
            <el-input v-model.trim="addForm.organizerName" placeholder="请选择演练组织人" class="inputWid"
              @focus="changeplanBasicId">
            </el-input>
            <div class="tips">注：任务执行人演练后上传演练资料</div>
          </el-form-item>
          <el-form-item label="演练通知事件">
            <el-radio v-model="addForm.noticeReserve" :label="0">开始日期前</el-radio>
            <el-radio v-model="addForm.noticeReserve" :label="1">开始日期后</el-radio>
            <el-input v-model="addForm.noticeValue" min="1" placeholder="请输入" style="width:97px" maxlength="3"
              oninput="value=value.replace(/^0|[^0-9]/g, '')">
            </el-input>
            <el-select v-model.trim="addForm.noticeType" filterable clearable placeholder="请选择" style="width:75px">
              <el-option v-for="item in daytTypeArr" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="通知方式" prop="noticeWay">
            <el-select v-model="addForm.noticeWay" placeholder="通知方式" class="inputWid">
              <el-option v-for="item in noticeOptions" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <br />
          <el-form-item label="演练地点">
            <el-input v-model="addForm.drillPlace" placeholder="请输入演练地点" maxlength="100" show-word-limit
              style="width: 870px;">
            </el-input>
          </el-form-item>
          <br />
          <el-form-item label="演练任务描述">
            <el-input v-model="addForm.drillDesc" type="textarea" placeholder="请输入演练任务描述" resize="none"
              :autosize="{ minRows: 3, maxRows: 3 }" maxlength="1000" show-word-limit style="width: 870px;">
            </el-input>
          </el-form-item>
          <br />
          <el-form-item label="计划任务预览">
            <el-button type="primary" :loading="previewLoading"
              @click="taskPreview">任务预览{{  previewList.length > 0 ?'(' +  paginationData.total + ')':''  }}</el-button>
          </el-form-item>
          <el-table v-if="previewList.length > 0" row-key="id" :data="previewList"
            style="width: 100%; margin-top: 10px;">
            <el-table-column label="序号" width="100">
              <template slot-scope="scope">
                <span>{{
                    (paginationData.currentPage - 1) * paginationData.pageSize +
                    scope.$index + 1
                  }}</span>
              </template>
            </el-table-column>
            <el-table-column label="任务名称" show-overflow-tooltip prop="taskName"> </el-table-column>
            <el-table-column label="应执行日期" show-overflow-tooltip>
              <template slot-scope="scope">
                <span>{{ moment(scope.row.taskStartTime).format('YYYY-MM-DD') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="组织人" show-overflow-tooltip prop="organizerName"></el-table-column>
          </el-table>
          <div v-if="previewList.length > 0" class="" style="padding-top: 10px; text-align: right;">
            <el-pagination layout="total, sizes, prev, pager, next, jumper" :current-page="paginationData.currentPage"
              :page-sizes="[10, 30, 50, 100]" :page-size="paginationData.pageSize" :total="paginationData.total"
              @size-change="sizeChange" @current-change="currentChange"></el-pagination>
          </div>
        </el-form>
      </div>
      <!--选择人员 -->
      <template v-if="personDialogShow">
        <SelectPeopleDialog :personDialogShow="personDialogShow" @submitPersonDialog="submitPersonDialog"
          @closePersonDialog="closePersonDialog" />
      </template>
      <!--选择部门 -->
      <template v-if="sectionDialogShow">
        <SelectDeptDialog :sectionDialogShow="sectionDialogShow" @submitSectionDialog="submitSectionDialog"
          @closeSectionDialog="closeSectionDialog" />
      </template>
    </div>
    <div slot="footer">
      <el-button type="primary" @click="submitForm()">保存</el-button>
      <el-button type="primary" plain @click="$router.go(-1)">取消</el-button>
    </div>
  </PageContainer>
</template>
<script>
import moment from 'moment'
import SelectDeptDialog from './components/SelectDept.vue'
import SelectPeopleDialog from './components/SelectPeople.vue'
export default {
  name: 'addExercisePlan',
  components: {
    SelectDeptDialog,
    SelectPeopleDialog
  },
  data() {
    return {
      moment,
      addForm: {
        drillName: '', // 计划名称
        drillType: '', // 演练类型
        planBasicId: '', // 预案id
        cycleType: 0, // 周期规则
        planStartTime: '', // 计划生效期限
        organizerId: '', // 演练组织人
        organizerName: '', // 组织name
        noticeWay: 0, // 通知方式
        noticeType: 0, // 演练通知时间类型 (0:分钟,1:小时,2:天)
        timeInterval: [], // 计划生效期限
        startMonth: '', // 开始月份
        endMonth: '', // 结束月份
        noticeReserve: 0,
        noticeValue: 1,
        startDate: '', // 演练开始日期
        endDate: '', // 演练结束日期
        exerciseDate: [], // 演练日期
        exerciseStartsDate: '', // 演练开始时间
        exerciseEndsDate: '', // 演练结束时间
        drillPlace: '', // 演练地点
        drillDesc: ''// 演练任务描述
      },
      deptIds: '',
      deptNames: '',
      headIds: '',
      headNames: '',
      rules: {
        drillName: { required: true, message: '请输入演练计划名称', trigger: 'blur' },
        drillType: { required: true, message: '请选择演练类型', trigger: 'change' },
        cycleType: { required: true, message: '请选择周期规则', trigger: 'change' },
        planStartTime: { required: true, message: '请选择计划生效期限', trigger: 'change' },
        exerciseDate: { required: true, message: '请选择演练日期', trigger: 'change' },
        organizerId: { required: true, message: '请选择演练组织人', trigger: 'change' },
        noticeWay: { required: true, message: '请选择通知方式', trigger: 'change' },
        timeInterval: { required: true, message: '请选择计划生效期限', trigger: 'change' },
        startMonth: { required: true, message: '请选择开始月份', trigger: 'change' },
        endMonth: { required: true, message: '请选择结束月份', trigger: 'change' },
        startDate: { required: true, message: '请选择开始日期', trigger: 'change' },
        endDate: { required: true, message: '请选择结束日期', trigger: 'change' },
        exerciseStartsDate: { required: true, message: '请选择开始日期', trigger: 'change' }
      },
      deptData: [],
      personData: [],
      planStartTimeArr: [],
      // 开始月份
      startMonthArr: [],
      endMonthArr: [],
      // 开始日期数组
      startDateArr: [],
      endDateArr: [],
      prePlanOptions: [],
      // 通知方式
      noticeOptions: [
        {
          id: 0,
          name: '短信'
        },
        {
          id: 1,
          name: 'App通知'
        },
        {
          id: 2,
          name: '公众号通知'
        }
      ],
      personDialogShow: false,
      sectionDialogShow: false,
      exerciseTypeArr: [], // 演练类型
      planArr: [], // 预案list
      templateArr: [], // 模板列表
      allTemplateArr: [],
      cycleArr: [], // 周期列表
      allCycleList: [],
      scopeType: 0,
      // 周期类型
      typeOptions: [
        {
          cycleType: 0,
          label: '单次'
        },
        {
          cycleType: 1,
          label: '每周'
        },
        {
          cycleType: 2,
          label: '每月'
        },
        {
          cycleType: 3,
          label: '每季度'
        },
        {
          cycleType: 4,
          label: '每半年'
        },
        {
          cycleType: 5,
          label: '全年'
        }
      ],
      daytTypeArr: [
        {
          label: '分钟',
          value: 0
        },
        {
          label: '小时',
          value: 1
        },
        {
          label: '天',
          value: 2
        }
      ],
      objectTypeList: {
        0: {
          text: '单位'
        },
        1: {
          text: '部门'
        },
        2: {
          text: '人员'
        },
        3: {
          text: '岗位'
        },
        4: {
          text: '职位'
        },
        5: {
          text: '空间'
        },
        6: {
          text: '设备'
        }
      },
      planState: '',
      dialogTableVisible: false,
      personType: '', // 责任人 or组织人
      previewList: [], // 任务预览list
      // 预览分页
      paginationData: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      previewLoading: false
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.getTypeList()
      this.getPreplanData()
      const planId = this.$route.query.planId
      if (planId) {
        setTimeout(() => {
          this.getExerciseById()
        }, 300)

      }
    },
    // 获取详情
    getExerciseById() {
      const planId = this.$route.query.planId
      this.$api.getPreplanDrillPlanById({ id: planId }).then((res) => {
        if (res.code === '200') {
          this.getTypeList()
          this.addForm = res.data
          if (this.addForm.cycleType === 1) {
            const dateName = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
            this.startDateArr = []
            this.endDateArr = []
            for (let i = 0; i < 7; i++) {
              const item = {
                id: i + 1,
                name: '每' + dateName[i]
              }
              this.startDateArr.push(item)
            }
            for (let i = this.addForm.startDate - 1; i < 7; i++) {
              const item = {
                id: i + 1,
                name: '每' + dateName[i]
              }
              this.endDateArr.push(item)
            }
          } else if (this.addForm.cycleType === 2) {
            this.startDateArr = []
            this.endDateArr = []
            for (let i = 0; i < 30; i++) {
              const item = {
                id: i + 1,
                name: '每月' + (i + 1) + '日'
              }
              this.startDateArr.push(item)
            }
            for (let i = this.addForm.startDate - 1; i < 30; i++) {
              const item = {
                id: i + 1,
                name: '每月' + (i + 1) + '日'
              }
              this.endDateArr.push(item)
            }
          } else if (this.addForm.cycleType === 3) {
            this.startDateArr = []
            this.endDateArr = []
            this.startMonthArr = []
            this.endMonthArr = []
            for (let i = 0; i < 30; i++) {
              const item = {
                id: i + 1,
                name: '每月' + (i + 1) + '日'
              }
              this.startDateArr.push(item)
            }
            for (let i = this.addForm.startDate - 1; i < 30; i++) {
              const item = {
                id: i + 1,
                name: '每月' + (i + 1) + '日'
              }
              this.endDateArr.push(item)
            }
            for (let i = 0; i < 3; i++) {
              const item = {
                id: i + 1,
                name: '第' + (i + 1) + '个月'
              }
              this.startMonthArr.push(item)
            }
            for (let i = this.addForm.startMonth - 1; i < 3; i++) {
              const item = {
                id: i + 1,
                name: '第' + (i + 1) + '个月'
              }
              this.endMonthArr.push(item)
            }
          } else if (this.addForm.cycleType === 4) {
            for (let i = 0; i < 30; i++) {
              const item = {
                id: i + 1,
                name: '每月' + (i + 1) + '日'
              }
              this.startDateArr.push(item)
            }
            for (let i = this.addForm.startDate - 1; i < 30; i++) {
              const item = {
                id: i + 1,
                name: '每月' + (i + 1) + '日'
              }
              this.endDateArr.push(item)
            }
            for (let i = 0; i < 6; i++) {
              const item = {
                id: i + 1,
                name: '第' + (i + 1) + '个月'
              }
              this.startMonthArr.push(item)
            }
            for (let i = this.addForm.startMonth - 1; i < 6; i++) {
              const item = {
                id: i + 1,
                name: '第' + (i + 1) + '个月'
              }
              this.endMonthArr.push(item)
            }

          }
          this.addForm.drillType = Number(this.addForm.drillType)
          if (this.addForm.cycleType === 0) {
            this.addForm.planStartTime = moment(this.addForm.planStartTime).format('YYYY-MM-DD')
            let listDate = [
              moment(this.addForm.drillStartTime).format('YYYY-MM-DD'),
              moment(this.addForm.drillEndTime).format('YYYY-MM-DD')
            ]
            this.$set(this.addForm, 'exerciseDate', listDate)
          } else if (this.addForm.cycleType === 5) {
            this.addForm.planStartTime = moment(this.addForm.planStartTime).format('YYYY-MM-DD')
            this.addForm.exerciseStartsDate = moment(this.addForm.drillStartTime).format('YYYY-MM-DD')
            this.addForm.exerciseEndsDate = moment(this.addForm.drillEndTime).format('YYYY-MM-DD')
          } else {
            let listDateNew = [
              moment(this.addForm.planStartTime).format('YYYY-MM-DD'),
              moment(this.addForm.planEndTime).format('YYYY-MM-DD')
            ]
            this.$set(this.addForm, 'timeInterval', listDateNew)
          }
          let objArr = []
          this.deptNames = res.data.deptNames
          this.deptIds = res.data.deptIds
          this.deptIds.split(',').forEach((val, index) => {
            objArr.push({
              id: val,
              deptName: this.addForm.deptNames.split(',')[index]
            })
          })
          this.deptData = objArr
          let objArr1 = []
          this.headIds = res.data.headIds
          this.headNames = res.data.headNames
          this.headIds.split(',').forEach((val, index) => {
            objArr1.push({
              id: val,
              staffName: this.addForm.headNames.split(',')[index]
            })
          })
          this.personData = objArr1
        }
      })
    },
    // 预案列表
    getPreplanData() {
      this.$api.getBasicData({}).then((res) => {
        if (res.code === '200') {
          this.prePlanOptions = res.data
        }
      })
    },
    // 获取演练类型列表
    getTypeList() {
      let data = {
        page: {
          current: 1,
          size: 9999
        }
      }
      this.$api
        .getPreplanDrillTypeData(data)
        .then((res) => {
          this.exerciseTypeArr = res.data ? res.data.list : []
        })
    },
    // 参演部门
    toDepet() {
      this.sectionDialogShow = true
    },
    // 删除部门
    delDept(index) {
      this.deptData.splice(index, 1)
    },
    // 参演人员
    toPerson() {
      this.personDialogShow = true
      this.personType = 'zrr'
    },
    // 删除人员
    delPerson(index) {
      this.personData.splice(index, 1)
    },
    // 组织name
    changeplanBasicId() {
      this.personDialogShow = true
      this.personType = 'zzr'
    },
    // 选择日期
    startYearDateChange(val) {
      const currentYear = new Date(val).getFullYear()
      this.addForm.exerciseEndsDate = currentYear + '-' + '12-31'
    },
    // 开始日期
    startDateChange(e) {
      this.endDateArr = []
      if (this.addForm.cycleType === 1) {
        const dateName = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        for (let i = e - 1; i < 7; i++) {
          const item = {
            id: i + 1,
            name: '每' + dateName[i]
          }
          this.endDateArr.push(item)
        }
      } else if (this.addForm.cycleType === 2 || this.addForm.cycleType === 3 || this.addForm.cycleType === 4) {
        for (let i = e - 1; i < 30; i++) {
          const item = {
            id: i + 1,
            name: '每月' + (i + 1) + '日'
          }
          this.endDateArr.push(item)
        }
      }
    },
    // 计划生效日期改变
    planStartTimeChange() {
      console.log(333333333333333333333333333)
      this.paginationData = {
        pageSize: 10,
        currentPage: 1,
        total: 0
      }
      this.previewList = []
    },
    // 开始月份
    startMonthChange(e) {
      this.endMonthArr = []
      if (this.addForm.cycleType === 3) {
        for (let i = e - 1; i < 3; i++) {
          const item = {
            id: i + 1,
            name: '第' + (i + 1) + '个月'
          }
          this.endMonthArr.push(item)
        }
      } else if (this.addForm.cycleType === 4) {
        for (let i = e - 1; i < 6; i++) {
          const item = {
            id: i + 1,
            name: '第' + (i + 1) + '个月'
          }
          this.endMonthArr.push(item)
        }
      }
    },
    // 改变周期类型
    changeCycleType(val) {
      this.addForm.startDate = ''
      this.addForm.endDate = ''
      this.addForm.endMonth = ''
      this.addForm.startMonth = ''
      this.startDateArr = []
      this.startMonthArr = []
      if (val === 1) {
        const dateName = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        for (let i = 0; i < 7; i++) {
          const item = {
            id: i + 1,
            name: '每' + dateName[i]
          }
          this.startDateArr.push(item)
        }
      } else if (val === 2) {
        for (let i = 0; i < 30; i++) {
          const item = {
            id: i + 1,
            name: '每月' + (i + 1) + '日'
          }
          this.startDateArr.push(item)
        }
      } else if (val === 3) {
        for (let i = 0; i < 30; i++) {
          const item = {
            id: i + 1,
            name: '每月' + (i + 1) + '日'
          }
          this.startDateArr.push(item)
        }
        for (let i = 0; i < 3; i++) {
          const item = {
            id: i + 1,
            name: '第' + (i + 1) + '个月'
          }
          this.startMonthArr.push(item)
        }
      } else if (val === 4) {
        for (let i = 0; i < 30; i++) {
          const item = {
            id: i + 1,
            name: '每月' + (i + 1) + '日'
          }
          this.startDateArr.push(item)
        }
        for (let i = 0; i < 6; i++) {
          const item = {
            id: i + 1,
            name: '第' + (i + 1) + '个月'
          }
          this.startMonthArr.push(item)
        }
      }
    },
    // 人员弹窗
    submitPersonDialog(list) {
      if (this.personType === 'zrr') {
        let headIds = []
        let headNames = []
        this.personData = this.removeSame(this.personData.concat(list))
        this.personData.forEach(item => {
          headIds.push(item.id)
          headNames.push(item.staffName)
        })
        this.headIds = headIds.join(',')
        this.headNames = headNames.join(',')
        this.personDialogShow = false
      } else if (this.personType === 'zzr') {
        if (list.length !== 1) {
          this.$message.error('组织人只支持单选')
          return
        }
        this.addForm.organizerName = list[0].staffName
        this.addForm.organizerId = list[0].id
        this.personDialogShow = false
      }
    },
    closePersonDialog() {
      this.personDialogShow = false
    },
    // 部门弹窗
    closeSectionDialog() {
      this.sectionDialogShow = false
    },
    submitSectionDialog(list) {
      let deptIds = []
      let deptNames = []
      this.sectionDialogShow = false
      this.deptData = this.removeSame(this.deptData.concat(list))

      this.deptData.forEach(item => {
        deptIds.push(item.id)
        deptNames.push(item.deptName)
      })
      this.deptIds = deptIds.join(',')
      this.deptNames = deptNames.join(',')
    },
    removeSame(array) {
      let newArray = []
      for (let item of array) {
        // 使用JSON.stringfy()方法将数组和数组元素转换为JSON字符串之后再使用includes()方法进行判断
        if (JSON.stringify(newArray).includes(JSON.stringify(item))) {
          continue
        } else {
          // 不存在的添加到数组中
          newArray.push(item)
        }
      }
      return newArray
    },
    // 任务预览
    taskPreview() {
      // 参演部门校验
      if (this.deptData.length < 1) {
        this.$message({
          type: 'error',
          message: '请选择参演部门'
        })
        return false
      }
      // 演练责任人校验
      if (this.personData.length < 1) {
        this.$message({
          type: 'error',
          message: '请选择演练责任人'
        })
        return false
      }
      this.$refs['formInline'].validate((valid) => {
        let userInfo = this.$store.state.user.userInfo.user
        if (valid) {
          let data = {
            ...this.addForm,
            deptIds: this.deptIds,
            deptNames: this.deptNames,
            headIds: this.headIds,
            headNames: this.headNames,
            id: this.$route.query.planId ? this.$route.query.planId : '',
            createName: userInfo.staffName,
            createId: userInfo.staffId,
            page: {
              current: this.paginationData.currentPage,
              size: this.paginationData.pageSize
            }
          }
          if (data.cycleType === 0) {
            data.drillStartTime = this.addForm.exerciseDate[0] ? this.addForm.exerciseDate[0] : ''
            data.drillEndTime = this.addForm.exerciseDate[1] ? this.addForm.exerciseDate[1] : ''
            data.planEndTime = data.planStartTime
          } else if (data.cycleType === 5) {
            data.drillStartTime = this.addForm.exerciseStartsDate
            data.drillEndTime = this.addForm.exerciseEndsDate
            data.planEndTime = data.planStartTime
          } else {
            data.planStartTime = this.addForm.timeInterval[0] ? this.addForm.timeInterval[0] : ''
            data.planEndTime = this.addForm.timeInterval[1] ? this.addForm.timeInterval[1] : ''
          }
          delete data.exerciseDate
          delete data.timeInterval
          delete data.exerciseStartsDate
          delete data.exerciseEndsDate
          this.previewLoading = true
          this.$api.previewPreplanDrillPlanData(data).then((res) => {
            if (res.code == '200') {
              if (res.data.list && res.data.list.length > 0) {
                res.data.list.forEach((i, index) => (i.index = index + 1))
                this.previewList = res.data.list
                this.paginationData.total = res.data.totalCount
              }
            } else {
              this.$message({
                type: 'error',
                message: res.message || '生成任务预览失败'
              })
            }
            this.previewLoading = false
          })
        }
      })
    },
    goBack() {
      this.$router.go(-1)
    },
    // 详情
    getTargetPlanId() {
      const planId = this.$route.query.planId
      this.$api.getTargetPlanById({ planId: planId }).then((res) => {
        if (res.code == 200) {
          this.planState = res.data.state
          this.addForm = res.data
          if (res.data.longTerm === 1) {
            this.addForm.longTerm = true
          } else {
            this.addForm.longTerm = false
          }
          this.scopeType = this.allTemplateArr.find((ele) => ele.id == this.addForm.templateManageId).type
        } else if (res.message) {
          this.$message.error(res.message)
        }
      })
    },
    // 任务预览分页
    getPreviewList() {
      let userInfo = this.$store.state.user.userInfo.user
      let data = {
        ...this.addForm,
        deptIds: this.deptIds,
        deptNames: this.deptNames,
        headIds: this.headIds,
        headNames: this.headNames,
        id: this.$route.query.planId ? this.$route.query.planId : '',
        createName: userInfo.staffName,
        createId: userInfo.staffId,
        page: {
          current: this.paginationData.currentPage,
          size: this.paginationData.pageSize
        }
      }
      if (data.cycleType === 0) {
        data.drillStartTime = this.addForm.exerciseDate[0] ? this.addForm.exerciseDate[0] : ''
        data.drillEndTime = this.addForm.exerciseDate[1] ? this.addForm.exerciseDate[1] : ''
        data.planEndTime = data.planStartTime
      } else if (data.cycleType === 5) {
        data.drillStartTime = this.addForm.exerciseStartsDate
        data.drillEndTime = this.addForm.exerciseEndsDate
        data.planEndTime = data.planStartTime
      } else {
        data.planStartTime = this.addForm.timeInterval[0] ? this.addForm.timeInterval[0] : ''
        data.planEndTime = this.addForm.timeInterval[1] ? this.addForm.timeInterval[1] : ''
      }
      delete data.exerciseDate
      delete data.timeInterval
      delete data.exerciseStartsDate
      delete data.exerciseEndsDate
      this.previewLoading = true
      this.$api.previewPreplanDrillPlanData(data).then((res) => {
        if (res.code == '200') {
          if (res.data.list && res.data.list.length > 0) {
            res.data.list.forEach((i, index) => (i.index = index + 1))
            this.previewList = res.data.list
            // this.paginationData.total = res.data.totalCount
          }
        } else {
          this.$message({
            type: 'error',
            message: res.message || '生成任务预览失败'
          })
        }
        this.previewLoading = false
      })
    },
    sizeChange(val) {
      this.paginationData.pageSize = val
      this.paginationData.currentPage = 1
      this.getPreviewList()

    },
    currentChange(val) {
      this.paginationData.currentPage = val
      this.getPreviewList()
    },
    submitForm() {
      // 计划预览校验
      if (this.previewList.length < 1) {
        this.$message({
          type: 'error',
          message: '请完成计划预览'
        })
        return false
      }
      this.$refs['formInline'].validate((valid) => {
        let userInfo = this.$store.state.user.userInfo.user
        if (valid) {
          const planId = this.$route.query.planId
          if (planId) {
            let data = {
              ...this.addForm,
              deptIds: this.deptIds,
              deptNames: this.deptNames,
              createName: userInfo.staffName,
              createId: userInfo.staffId,
              headIds: this.headIds,
              headNames: this.headNames,
              id: this.$route.query.planId
            }
            if (data.cycleType === 0) {
              data.drillStartTime = this.addForm.exerciseDate[0] ? this.addForm.exerciseDate[0] : ''
              data.drillEndTime = this.addForm.exerciseDate[1] ? this.addForm.exerciseDate[1] : ''
              data.planEndTime = data.planStartTime
            } else if (data.cycleType === 5) {
              data.drillStartTime = this.addForm.exerciseStartsDate
              data.drillEndTime = this.addForm.exerciseEndsDate
              data.planEndTime = data.planStartTime
            } else {
              data.planStartTime = this.addForm.timeInterval[0] ? this.addForm.timeInterval[0] : ''
              data.planEndTime = this.addForm.timeInterval[1] ? this.addForm.timeInterval[1] : ''
            }
            delete data.exerciseDate
            delete data.timeInterval
            delete data.exerciseStartsDate
            delete data.exerciseEndsDate
            this.$api
              .updatePreplanDrillPlanData(data, { 'operation-type': 2, 'operation-id': data.id, 'operation-name': data.drillName })
              .then((res) => {
                if (res.code == 200) {
                  this.$message({
                    type: 'success',
                    message: '保存成功'
                  })
                  this.$router.go('-1')
                } else {
                  this.$message({
                    type: 'error',
                    message: res.message || '保存失败'
                  })
                }
              })
              .catch(() => { })
          } else {
            let data = {
              ...this.addForm,
              createName: userInfo.staffName,
              createId: userInfo.staffId,
              deptIds: this.deptIds,
              deptNames: this.deptNames,
              headIds: this.headIds,
              headNames: this.headNames,
              id: ''
            }
            if (data.cycleType === 0) {
              data.drillStartTime = this.addForm.exerciseDate[0] ? this.addForm.exerciseDate[0] : ''
              data.drillEndTime = this.addForm.exerciseDate[1] ? this.addForm.exerciseDate[1] : ''
              data.planEndTime = data.planStartTime
            } else if (data.cycleType === 5) {
              data.drillStartTime = this.addForm.exerciseStartsDate
              data.drillEndTime = this.addForm.exerciseEndsDate
              data.planEndTime = data.planStartTime
            } else {
              data.planStartTime = this.addForm.timeInterval[0] ? this.addForm.timeInterval[0] : ''
              data.planEndTime = this.addForm.timeInterval[1] ? this.addForm.timeInterval[1] : ''
            }
            delete data.exerciseDate
            delete data.timeInterval
            delete data.exerciseStartsDate
            delete data.exerciseEndsDate
            this.$api
              .addPreplanDrillPlanData(data, { 'operation-type': 1 })
              .then((res) => {
                if (res.code == 200) {
                  this.$message({
                    type: 'success',
                    message: '保存成功'
                  })
                  this.$router.go('-1')
                } else {
                  this.$message({
                    type: 'error',
                    message: res.message || '保存失败'
                  })
                }
              })
              .catch(() => { })
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.container-content > div {
  background-color: #fff;
  height: 100%;
  position: relative;
  .addExercisePlan-content-title {
    border-bottom: 1px solid #e4e7ed;
    padding: 13px 24px;
    cursor: pointer;
    font-weight: 500;
    color: #333333;
  }
  .content_box {
    padding: 16px 24px;
    background: #fff;
    display: flex;
    flex-direction: column;
    height: calc(100% - 50px);
    overflow-y: scroll;
    .top-title {
      font-weight: bold;
      font-size: 16px;
      color: #333333;
    }
  }
  .form-inline {
    margin-top: 24px;
    .el-input,
    .el-select,
    .el-cascader {
      width: 300px;
    }
  }
  .form-inline .inputWid {
    width: 390px;
  }
}

.deptName {
  font-size: 14px;
  color: #333333;
}
.tips {
  font-size: 12px;
  color: #7f848c;
  height: 14px;
  line-height: 14px;
}
.rightBox {
  cursor: pointer;
  .operation {
    display: inline-block;
  }
  .dep-box {
    width: 905px;
    min-height: 40px;
    max-height: 85px;
    overflow-y: auto;
    border: 1px solid #c0c4cc;
    border-radius: 5px;
    box-sizing: border-box;
    display: flex;
    flex-wrap: wrap;
    margin: 5px 0;
    padding: 5px;
    .dep-small-box {
      background: #ededf5;
      border-radius: 4px;
      margin: 2px 0px 2px 6px;
      line-height: 22px;
      padding: 0px 8px;
      font-size: 14px;
      color: #333333;
      cursor: pointer;
      i {
        font-style: normal;
      }
      img {
        margin-left: 4px;
        vertical-align: middle;
      }
    }
    .dep-small-box:first-child {
      margin-left: 0px;
    }
  }
}
.el-select-dropdown {
  min-width: 200px !important;
}
.el-select-dropdown__item {
  min-width: 200px !important;
}
.el-radio {
  margin-right: 15px;
}
.el-range-editor.el-input__inner {
  padding: 0px 10px;
}
</style>
