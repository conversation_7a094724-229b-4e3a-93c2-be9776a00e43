<template>
  <div class="monitor-content-right">
    <div class="right-heade">
      <div class="search-from">
        <span class="btnGroup">
          <el-button v-for="(item, index) in dateTypeList" :key="index" size="small" :type="item.btnType" :plain="item.btnPlain" @click="dateTypeChange(item)">
            {{ item.name }}
          </el-button>
        </span>
        <el-date-picker
          v-model="search.dateTime"
          :picker-options="{ firstDayOfWeek: 1 }"
          :type="dateType"
          style="margin: 10px"
          :format="dateFormat"
          value-format="yyyy-MM-dd"
          @change="dateChange"
        >
        </el-date-picker>
        <span>
          <el-button size="small" type="primary" plain @click="reset">重置</el-button>
          <el-button size="small" type="primary" @click="searchBtn">查询</el-button>
          <el-button size="small" type="primary" @click="exportData">导出</el-button>
        </span>
      </div>
    </div>
    <div id="contentPage" v-loading="loading" class="right-content">
      <div class="content-top">
        <div class="dateTitle">
          <el-button type="text">
            <i class="el-icon-arrow-left" @click="prev"></i>
            {{ startTime }}
            <i v-if="dateType === 'date'" style="margin-left: 0" class="el-icon-arrow-right el-icon--right" @click="next"></i>
          </el-button>
          <el-button v-if="dateType !== 'date'" type="text">{{ endTime }}<i class="el-icon-arrow-right el-icon--right" @click="next"></i></el-button>
        </div>
        <div style="display: flex; height: 212px">
          <div class="left">
            <div class="staticsisticItem">
              <img src="@/assets/images/targetAnalysis/targetPlan-statistics.png" alt="" />
              <div class="staticsisticItemData">
                <div>总开关门</div>
                <div class="staticsisticItemDataNum">
                  {{ allCount }}
                  <span>次</span>
                </div>
              </div>
            </div>
            <div class="staticsisticItem">
              <img src="@/assets/images/parkingLot/count-two.png" alt="" />
              <div class="staticsisticItemData">
                <div>平均开关门</div>
                <div class="staticsisticItemDataNum">
                  {{ timeAvg }}
                  <span>次</span>
                </div>
              </div>
            </div>
          </div>
          <div class="right">
            <div id="barChart"></div>
          </div>
        </div>
      </div>
      <div id="content-bottom" class="content-bottom">
        <el-table
          :key="Math.random()"
          height="100%"
          :data="tableData"
          :border="true"
          stripe
          :cell-style="{ padding: '8px' }"
          :header-cell-style="{ background: '#f2f4fbd1', height: ' 20px' }"
        >
          <el-table-column type="index" label="序号" fixed> </el-table-column>
          <el-table-column
            v-for="(item, index) in tableCloumns"
            :key="index"
            :prop="item.value"
            :label="item.name"
            show-overflow-tooltip
            :fixed="[0, 1].includes(index)"
          ></el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
// import axios from 'axios'
import { downloadPDF } from '@/util/htmlToPdf.js'
export default {
  props: {
    projectCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dateTypeList: [
        {
          val: 1,
          name: '日',
          btnType: 'primary',
          btnPlain: false
        },
        {
          val: 3,
          name: '周',
          btnType: '',
          btnPlain: true
        },
        {
          val: 2,
          name: '月',
          btnType: '',
          btnPlain: true
        },
        {
          val: 4,
          name: '年',
          btnType: '',
          btnPlain: true
        }
      ],
      dateType: 'date',
      dateFormat: '',
      loading: false,
      search: {
        timeType: 1,
        dateTime: this.$dayjs().format('YYYY-MM-DD')
      },
      startTime: this.$dayjs().format('YYYY-MM-DD'),
      endTime: this.$dayjs().format('YYYY-MM-DD'),
      allCount: 0,
      timeAvg: 0,
      tableData: [],
      tableCloumns: []
    }
  },
  mounted() {
    this.searchBtn()
  },
  methods: {
    dateTypeChange(item) {
      this.dateTypeList.forEach((el) => {
        el.btnType = ''
        el.btnPlain = true
      })
      item.btnType = 'primary'
      item.btnPlain = false
      this.search.timeType = item.val
      this.search.dateTime = this.$dayjs().format('YYYY-MM-DD')
      switch (item.val) {
        case 1:
          this.dateType = 'date'
          this.dateFormat = 'yyyy-MM-dd'
          break
        case 3:
          this.dateType = 'week'
          this.dateFormat = 'yyyy 第 WW 周'
          break
        case 2:
          this.dateType = 'month'
          this.dateFormat = 'yyyy-MM'
          break
        case 4:
          this.dateType = 'year'
          this.dateFormat = 'yyyy'
          break
        default:
          break
      }
      this.startTime = this.$dayjs().startOf(this.dateType).format('YYYY-MM-DD')
      this.endTime = this.$dayjs().endOf(this.dateType).format('YYYY-MM-DD')
      this.dateChange(this.search.dateTime)
    },
    dateChange(val) {
      switch (this.search.timeType) {
        case 1:
          this.startTime = this.endTime = val
          break
        case 3:
        case 2:
        case 4:
          this.startTime = this.$dayjs(val).startOf(this.dateType).format('YYYY-MM-DD')
          this.endTime = this.$dayjs(val).endOf(this.dateType).format('YYYY-MM-DD')
          break
        default:
          break
      }
    },
    prev() {
      switch (this.search.timeType) {
        case 1:
          this.search.dateTime = this.startTime = this.endTime = this.$dayjs(this.startTime).subtract(1, 'day').format('YYYY-MM-DD')
          break
        case 3:
        case 2:
        case 4:
          this.search.dateTime = this.startTime = this.$dayjs(this.startTime).subtract(1, this.dateType).format('YYYY-MM-DD')
          this.endTime = this.$dayjs(this.endTime).subtract(1, this.dateType).endOf(this.dateType).format('YYYY-MM-DD')
          break
        default:
          break
      }
      this.searchBtn()
    },
    next() {
      switch (this.search.timeType) {
        case 1:
          this.search.dateTime = this.startTime = this.endTime = this.$dayjs(this.endTime).add(1, 'day').format('YYYY-MM-DD')
          break
        case 3:
        case 2:
        case 4:
          this.search.dateTime = this.startTime = this.$dayjs(this.startTime).add(1, this.dateType).format('YYYY-MM-DD')
          this.endTime = this.$dayjs(this.endTime).add(1, this.dateType).endOf(this.dateType).format('YYYY-MM-DD')
          break
        default:
          break
      }
      this.searchBtn()
    },
    // 获取开门统计数据
    getOpenDoorStatistics() {
      let params = {
        timeType: this.search.timeType,
        startTime: this.startTime,
        endTime: this.endTime,
        projectCode: this.projectCode
      }
      this.$api.openDoorCount(params).then((res) => {
        if (res.code == 200) {
          this.allCount = res.data.allCount
          this.timeAvg = res.data.timeAvg
        }
      })
    },
    getOpenDoorBar() {
      let params = {
        timeType: this.search.timeType,
        startTime: this.startTime,
        endTime: this.endTime,
        projectCode: this.projectCode
      }
      this.$api.openDoorBar(params).then((res) => {
        if (res.code == 200) {
          this.setBarChart(res.data, 'barChart')
        }
      })
    },
    setBarChart(chartData, domId) {
      const nameList = chartData.map((item) => {
        return item.name
      })
      const countList = chartData.map((item) => {
        return item.count
      })
      const getchart = echarts.init(document.getElementById(domId))
      let option
      let colors = ['#3562DB', '#FF9435']
      if (chartData.length) {
        option = {
          tooltip: {
            trigger: 'axis'
          },
          label: {
            show: true,
            position: 'top',
            color: '#121F3E',
            fontSize: '12px'
          },
          grid: {
            top: '10%',
            left: '4%',
            right: '4%',
            bottom: '6%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: nameList
          },
          yAxis: {
            type: 'value'
          },
          series: [
            {
              type: 'bar',
              barWidth: 10,
              data: countList,
              itemStyle: {
                color: function (params) {
                  // 使用函数来设置颜色，实现循环使用两种颜色
                  return colors[params.dataIndex % colors.length]
                },
                barBorderRadius: [0, 2, 2, 0]
              }
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 获取table数据
    getDataList() {
      let params = {
        startTime: this.startTime + ' 00:00:00',
        endTime: this.endTime + ' 23:59:59',
        surveyCodes: '',
        page: this.currentPage,
        pageSize: this.pageSize,
        timeType: this.search.timeType,
        projectCode: this.projectCode
      }
      this.tableCloumns = []
      this.tableData = []
      this.$api.openDoorList(params).then((res) => {
        if (res.code == 200) {
          this.tableCloumns = res.data.header
          this.tableData = res.data.list
        }
      })
    },
    searchBtn() {
      this.getOpenDoorStatistics()
      this.getOpenDoorBar()
      this.getDataList()
    },
    reset() {
      this.startTime = this.endTime = this.search.dateTime = this.$dayjs().format('YYYY-MM-DD')
      this.search.timeType = 1
      this.dateTypeList.forEach((el, index) => {
        el.btnType = ''
        el.btnPlain = true
        if (!index) {
          el.btnType = 'primary'
          el.btnPlain = false
        }
      })
      this.searchBtn()
    },
    exportData() {
      let params = {
        timeType: this.search.timeType,
        startTime: this.startTime + ' 00:00:00',
        endTime: this.endTime + ' 23:59:59',
        projectCode: this.projectCode,
        surveyCodes: ''
      }
      this.$api.exportOpenDoorList(params).then((res) => {
        this.$tools.downloadFile(res, '开门次数分析')
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.monitor-content-right {
  height: 100%;
  flex: 1;
  flex-direction: column;
  overflow: auto;
  margin-left: 16px;
  display: flex;
  .right-heade {
    border-radius: 4px;
    background: #fff;
    padding: 10px !important;
    .search-from {
      .btnGroup {
        .el-button {
          width: 72px;
        }
      }
    }
    .batch-control {
      & > button {
        margin-top: 12px;
        margin-right: 10px;
        margin-left: 0;
      }
    }
  }
  .right-content {
    padding: 16px;
    flex: 1;
    overflow: auto;
    margin-top: 16px;
    background: #fff;
    .content-top {
      width: 100%;
      .dateTitle {
        width: 100%;
        height: 320px;
        text-align: center;
        height: 40px;
        line-height: 40px;
        margin-bottom: 16px;
      }
      .left {
        margin-right: 16px;
        width: 274px;
        height: 212px;
        .staticsisticItem {
          width: 100%;
          height: calc(50% - 8px);
          background: #faf9fc;
          border-radius: 4px 4px 4px 4px;
          padding: 16px;
          display: flex;
          align-items: center;
          &:first-child {
            margin-bottom: 16px;
          }
          img {
            margin-right: 22px;
          }
          .staticsisticItemData {
            color: #96989a;
            .staticsisticItemDataNum {
              color: #333;
              font-size: 24px;
              & > span {
                font-size: 14px;
                color: #96989a;
              }
            }
          }
        }
      }
      .right {
        width: calc(100% - 290px);
        height: 212px;
        #barChart {
          width: 100%;
          height: 100%;
        }
      }
    }
    .content-bottom {
      width: 100%;
      height: calc(100% - 268px - 16px);
      margin-top: 16px;
    }
  }
}
</style>
