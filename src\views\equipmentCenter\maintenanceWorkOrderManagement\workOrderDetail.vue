<template>
  <div class="form-detail">
    <div class="reserve-scorll">
      <div v-for="(taskRecordObj, taskIndex) in workOrderDetail.taskRecord" :key="taskRecordObj.id" class="width: 100%">
        <!-- 创建工单 -->
        <div v-if="taskRecordObj.operationCode === '1'" class="reserve-plan">
          <div class="plan-title">
            <div class="color-box"><i class="el-icon-time"></i></div>
            <div class="linear-g" @click="collectEvent('CZ', taskIndex)">
              <span class="linear-g-span1">{{ taskRecordObj.operationType }}</span>
              <span>{{ workOrderDetail.olgTaskManagement.createDate }}</span>
              <i :ref="'CZright' + taskIndex" style="display: inline-block" class="el-icon-arrow-right title-icon"></i>
              <i v-show="false" :ref="'CZdown' + taskIndex" class="el-icon-arrow-down title-icon"></i>
            </div>
          </div>
          <div class="plan-content plan-content-line">
            <ul class="item-row">
              <!-- <li class="width30">
                <span class="li-first-span">联系人</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.callerName }}</span>
              </li>
              <li class="width30">
                <span class="li-first-span">电话</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.sourcesPhone }}</span>
              </li>
              <li class="width30">
                <span class="li-first-span">工号</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.callerJobNum }}</span>
              </li> -->
              <li style="width: 35%">
                <span class="li-first-span">报修时间</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.createDate }}</span>
              </li>
              <li style="width: 28%">
                <span class="li-first-span">报修人</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.createByName }}</span>
              </li>
              <li style="width: 28%">
                <span class="li-first-span">报修人电话</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.sourcesPhone }}</span>
              </li>
            </ul>
            <!-- <div class="show-content"> -->
            <TransitionHeight :ref="'CZ' + taskIndex" :heigtRef="'CZBox' + taskIndex">
              <ul class="item-row">
                <li class="width45">
                  <span class="li-first-span">报修说明</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.questionDescription }}</span>
                </li>
              </ul>
              <!-- 0 1 3  呼叫中心 web app -->
              <ul
                v-if="
                  workOrderDetail.olgTaskManagement.workSources === '0' ||
                  workOrderDetail.olgTaskManagement.workSources === '1' ||
                  workOrderDetail.olgTaskManagement.workSources === '3'
                "
                class="item-row"
              >
                <li class="width90">
                  <span class="li-first-span">录音</span>
                  <div v-show="workOrderDetail.recordName" id="audio-box">
                    <audio controls>
                      <source :src="$tools.imgUrlTranslation(workOrderDetail.audioPath)" />
                      <!-- src="https://sinomis.oss-cn-beijing.aliyuncs.com/ioms/ZKYXYY/question/2021/06/24/mp3/20210624104642208466.mp3?Expires=1649988786&OSSAccessKeyId=LTAIeUfTgrfT5j7Y&Signature=QI12blGWwn1KzARBWOu7TdeD0rI%3D" -->
                    </audio>
                    <!-- <a href="javascript:;" onclick="downLoad(olgTaskManagement.audioPath)" title="下载">下载</a> -->
                  </div>
                  <span></span>
                </li>
              </ul>
              <!-- 1 3 web app -->
              <ul v-if="workOrderDetail.olgTaskManagement.workSources === '1' || workOrderDetail.olgTaskManagement.workSources === '3'" class="item-row">
                <li class="width90">
                  <span class="li-first-span">附件</span>
                  <!-- listAttUrl -->
                  <p v-show="workOrderDetail.listAttUrl">
                    <span v-for="(img, index) in workOrderDetail.listAttUrl" :key="index">
                      <el-image style="width: 100px; height: 100px; margin-right: 15px" :src="$tools.imgUrlTranslation(img)" :preview-src-list="[$tools.imgUrlTranslation(img)]">
                      </el-image>
                    </span>
                  </p>
                  <span></span>
                </li>
              </ul>
               <ul class="item-row">
                <li class="width45">
                  <span class="li-first-span">要求完工时间</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.requireAccomplishDate }}</span>
                </li>
              </ul>
            </TransitionHeight>
          </div>
        </div>
        <!-- 已派工 -->
        <div v-if="taskRecordObj.operationCode === '3'" class="reserve-plan">
          <div class="plan-title">
            <div class="color-box"><i class="el-icon-time"></i></div>
            <div class="linear-g" @click="collectEvent('PG', taskIndex)">
              <span class="linear-g-span1">{{ taskRecordObj.operationType }}</span>
              <span>{{ taskRecordObj.createDate }}</span>
              <i :ref="'PGright' + taskIndex" style="display: inline-block" class="el-icon-arrow-right title-icon"></i>
              <i v-show="false" :ref="'PGdown' + taskIndex" class="el-icon-arrow-down title-icon"></i>
            </div>
          </div>
          <div class="plan-content plan-content-line">
            <ul class="item-row">
              <li style="width: 35%">
                <span class="li-first-span">派工时间</span><span class="li-last-span">{{ taskRecordObj.createDate }}</span>
              </li>
              <li style="width: 28%">
                <span class="li-first-span">派工人</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.disDesignateByName }}</span>
              </li>
              <li style="width: 28%">
                <span class="li-first-span">执行人</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.designatePersonName }}</span>
              </li>
            </ul>
            <TransitionHeight :ref="'PG' + taskIndex" :heigtRef="'PGBox' + taskIndex"> </TransitionHeight>
          </div>
        </div>
        <!-- 已挂单 -->
        <div v-if="taskRecordObj.operationCode === '4'" class="reserve-plan">
          <div class="plan-title">
            <div class="color-box"><i class="el-icon-time"></i></div>
            <div class="linear-g" @click="collectEvent('GD', taskIndex)">
              <span class="linear-g-span1">{{ taskRecordObj.operationType }}</span>
              <span>{{ taskRecordObj.createDate }}</span>
              <i :ref="'PGright' + taskIndex" style="display: inline-block" class="el-icon-arrow-right title-icon"></i>
              <i v-show="false" :ref="'PGdown' + taskIndex" class="el-icon-arrow-down title-icon"></i>
            </div>
          </div>
          <div class="plan-content plan-content-line">
            <ul class="item-row">
              <li style="width: 35%">
                <span class="li-first-span">挂单原因</span><span class="li-last-span">{{ taskRecordObj.disEntryOrdersReason }}</span>
              </li>
            </ul>
            <ul class="item-row">
              <li style="width: 35%">
                <span class="li-first-span">挂单说明</span><span class="li-last-span">{{ taskRecordObj.disEntryOrdersSolution }}</span>
              </li>
            </ul>
          </div>
        </div>
        <!-- 已完工 -->
        <div v-if="taskRecordObj.operationCode === '6'" class="reserve-plan">
          <div class="plan-title">
            <div class="color-box"><i class="el-icon-time"></i></div>
            <div class="linear-g" @click="collectEvent('WG', taskIndex)">
              <span class="linear-g-span1">{{ taskRecordObj.operationType }}</span>
              <span>{{ taskRecordObj.createDate }}</span>
              <i :ref="'WGright' + taskIndex" style="display: inline-block" class="el-icon-arrow-right title-icon"></i>
              <i v-show="false" :ref="'WGdown' + taskIndex" class="el-icon-arrow-down title-icon"></i>
            </div>
          </div>
          <div class="plan-content plan-content-line">
            <ul class="item-row">
              <li style="width: 35%">
                <span class="li-first-span">维修日期</span><span class="li-last-span">{{ taskRecordObj.createDate }}</span>
              </li>
              <li style="width: 28%">
                <span class="li-first-span">维修人</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.designatePersonName }}</span>
              </li>
              <li style="width: 28%">
                <span class="li-first-span">维修人电话</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.designatePersonPhone }}</span>
              </li>
            </ul>
            <TransitionHeight :ref="'WG' + taskIndex" :heigtRef="'WGBox' + taskIndex">
              <ul v-if="true" class="item-row">
                <li class="width90">
                  <span class="li-first-span">满意度评价</span
                  ><span class="li-last-span">
                    <el-rate
                      v-if="taskRecordObj.disDegree"
                      v-model="taskRecordObj.disDegree"
                      disabled
                      show-text
                      text-color="#fff"
                      :texts="rateTexts"
                    >
                    </el-rate>
                    <span v-else>未评分</span>
                  </span>
                </li>
              </ul>
              <ul v-if="true" class="item-row">
                <li class="width90">
                  <span class="li-first-span">完工说明</span><span class="li-last-span">{{ taskRecordObj.disFinishRemark }}</span>
                </li>
              </ul>
              <!-- <ul v-if="true" class="item-row">
                <li class="width90">
                  <span class="li-first-span">录音</span
                  ><span class="li-last-span">
                    <audio v-if="completeInfo.disFinishVoiceUrl" style="height: 30px" :src="completeInfo.disFinishVoiceUrl" controls="controls"></audio>
                    <span v-else>当前任务未上传录音</span>
                  </span>
                </li>
              </ul> -->
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span">工单附件</span>
                  <!-- disAttachmentUrlList newUrl -->
                  <p v-if="taskRecordObj.disAttachmentUrl && taskRecordObj.disAttachmentUrl != ''">
                    <span v-for="(img, index) in taskRecordObj.disAttachmentUrl.split(',')" :key="index">
                      <el-image
                        style="width: 100px; height: 100px; margin-right: 15px"
                        :src="$tools.imgUrlTranslation(img)"
                        :preview-src-list="[$tools.imgUrlTranslation(img)]"
                      >
                      </el-image>
                    </span>
                  </p>
                  <span></span>
                </li>
              </ul>
              <ul v-if="true" class="item-row">
                <li class="width90">
                  <span class="li-first-span">总服务费</span><span class="li-last-span">{{ taskRecordObj.completePrice }}</span>
                </li>
              </ul>
            </TransitionHeight>
          </div>
        </div>
          <!-- 回退 -->
        <div v-if="taskRecordObj.operationCode === '30'" class="reserve-plan">
          <div class="plan-title">
            <div class="color-box"><i class="el-icon-time"></i></div>
                <div class="linear-g" @click="collectEvent('HT', taskIndex)">
              <span class="linear-g-span1">{{ taskRecordObj.operationType }}</span>
              <span>{{ taskRecordObj.createDate }}</span>
              <i :ref="'HTright' + taskIndex" style="display: inline-block" class="el-icon-arrow-right title-icon"></i>
              <i v-show="false" :ref="'HTdown' + taskIndex" class="el-icon-arrow-down title-icon"></i>
            </div>
          </div>
          <div class="plan-content plan-content-line">
            <ul class="item-row">
              <li class="width90">
                <span class="li-first-span">回退说明</span><span class="li-last-span">{{ taskRecordObj.rollbackExplain }}</span>
              </li>
            </ul>
             <TransitionHeight :ref="'HT' + taskIndex" :heigtRef="'HTBox' + taskIndex">
            <ul class="item-row">
              <li class="width90">
                <span class="li-first-span">回退照片</span>
                <p v-if="taskRecordObj.rollbackImage">
                  <span v-for="(img, index) in taskRecordObj.rollbackImage.split(',')" :key="index">
                    <el-image style="width: 100px; height: 100px; margin-right: 15px" :src="$tools.imgUrlTranslation(img)" :preview-src-list="[$tools.imgUrlTranslation(img)]">
                    </el-image>
                  </span>
                  <span></span>
                </p>
              </li>
            </ul>
             </TransitionHeight>
          </div>
        </div>
        <!-- 已取消 -->
        <div v-if="taskRecordObj.operationCode === '7'" class="reserve-plan">
          <div class="plan-title">
            <div class="color-box"><i class="el-icon-time"></i></div>
            <div class="linear-g" @click="collectEvent('QX', taskIndex)">
              <span class="linear-g-span1">{{ taskRecordObj.operationType }}</span>
              <span>{{ taskRecordObj.createDate }}</span>
              <i :ref="'QXright' + taskIndex" style="display: inline-block" class="el-icon-arrow-right title-icon"></i>
              <i v-show="false" :ref="'QXdown' + taskIndex" class="el-icon-arrow-down title-icon"></i>
            </div>
          </div>
          <div class="plan-content plan-content-line">
            <ul class="item-row">
              <li class="width90">
                <!-- getDictLabel cancel_reason -->
                <span class="li-first-span">取消理由</span><span class="li-last-span">{{ transform(workOrderDetail.olgTaskManagement.cancelReasonId) }}</span>
              </li>
            </ul>
            <TransitionHeight :ref="'QX' + taskIndex" :heigtRef="'QXBox' + taskIndex">
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span">取消说明</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.cancelExplain }}</span>
                </li>
              </ul>
            </TransitionHeight>
          </div>
        </div>
        <!-- 已转单 -->
        <div v-if="taskRecordObj.operationCode === '9'" class="reserve-plan">
          <div class="plan-title">
            <div class="color-box"><i class="el-icon-time"></i></div>
            <div class="linear-g" @click="collectEvent('ZD', taskIndex)">
              <span class="linear-g-span1">{{ taskRecordObj.operationType }}</span>
              <span>{{ taskRecordObj.createDate }}</span>
              <i :ref="'ZDright' + taskIndex" style="display: inline-block" class="el-icon-arrow-right title-icon"></i>
              <i v-show="false" :ref="'ZDdown' + taskIndex" class="el-icon-arrow-down title-icon"></i>
            </div>
          </div>
          <div class="plan-content plan-content-line">
            <ul class="item-row">
              <li class="width90">
                <span class="li-first-span">服务部门</span><span class="li-last-span">{{ taskRecordObj.designateDeptName }}</span>
              </li>
            </ul>
            <TransitionHeight :ref="'ZD' + taskIndex" :heigtRef="'ZDBox' + taskIndex">
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span">转单说明</span><span class="li-last-span">{{ taskRecordObj.feedbackExplain }}</span>
                </li>
              </ul>
            </TransitionHeight>
          </div>
        </div>
        <!-- 已变更 -->
        <div v-if="taskRecordObj.operationCode === '10'" class="reserve-plan">
          <div class="plan-title">
            <div class="color-box"><i class="el-icon-time"></i></div>
            <div class="linear-g" @click="collectEvent('BG', taskIndex)">
              <span class="linear-g-span1">{{ taskRecordObj.operationType }}</span>
              <span>{{ taskRecordObj.createDate }}</span>
              <i :ref="'BGright' + taskIndex" style="display: inline-block" class="el-icon-arrow-right title-icon"></i>
              <i v-show="false" :ref="'BGdown' + taskIndex" class="el-icon-arrow-down title-icon"></i>
            </div>
          </div>
          <div class="plan-content plan-content-line">
            <ul class="item-row">
              <li class="width90">
                <span class="li-first-span">原服务部门</span><span class="li-last-span">{{ taskRecordObj.designateDeptName }}</span>
              </li>
            </ul>
            <TransitionHeight :ref="'BG' + taskIndex" :heigtRef="'BGBox' + taskIndex">
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span">原服务地点</span>
                  <span class="li-last-span">{{ taskRecordObj.localtionName }}</span>
                  <!-- <span
                  ><a href="javascript:void(0)" class="recording-ALabel" @click="openTab(1, taskRecordObj.localtion, taskRecordObj.localtionName)"
                  >
                  <i class="el-icon-tickets"></i>申报记录
                  </a
                  ></span
                  > -->
                </li>
              </ul>
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span">原所属科室</span>
                  <span class="li-last-span">{{ workOrderDetail.olgTaskManagement.sourcesDeptName }}</span>
                  <!-- <span
                  ><a href="javascript:void(0)" class="recording-ALabel" @click="openTab(2, taskRecordObj.sourcesDept, workOrderDetail.olgTaskManagement.sourcesDeptName)"
                  >
                  <i class="el-icon-tickets"></i>申报记录
                  </a
                  ></span
                  > -->
                </li>
              </ul>
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span">原服务事项</span>
                  <span v-if="taskRecordObj.itemTypeName !== null && taskRecordObj.itemTypeName !== ''" class="li-last-span">{{ taskRecordObj.itemTypeName }}</span
                  >- <span v-if="taskRecordObj.itemDetailName !== null && taskRecordObj.itemDetailName !== ''" class="li-last-span">{{ taskRecordObj.itemDetailName }}</span
                  >-
                  <span v-if="taskRecordObj.itemServiceName !== null && taskRecordObj.itemServiceName !== ''" class="li-last-span">{{ taskRecordObj.itemServiceName }}</span>
                </li>
              </ul>
              <!-- completePrice && workOrderDetail.olgTaskManagement.flowcode===5 已完工 -->
              <ul v-if="taskRecordObj.completePrice && workOrderDetail.olgTaskManagement.flowcode === '5'" class="item-row">
                <li class="width90">
                  <span class="li-first-span">总服务费</span><span class="li-last-span">{{ taskRecordObj.completePrice || '' }}</span>
                </li>
              </ul>
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span">原申报描述</span><span class="li-last-span">{{ taskRecordObj.questionDescription }}</span>
                </li>
              </ul>
            </TransitionHeight>
          </div>
        </div>
        <!-- 已转派 -->
        <div v-if="taskRecordObj.operationCode === '11'" class="reserve-plan">
          <div class="plan-title">
            <div class="color-box"><i class="el-icon-time"></i></div>
            <div class="linear-g" @click="collectEvent('ZP', taskIndex)">
              <span class="linear-g-span1">{{ taskRecordObj.operationType }}</span>
              <span>{{ taskRecordObj.createDate }}</span>
              <i :ref="'ZPright' + taskIndex" style="display: inline-block" class="el-icon-arrow-right title-icon"></i>
              <i v-show="false" :ref="'ZPdown' + taskIndex" class="el-icon-arrow-down title-icon"></i>
            </div>
          </div>
          <div class="plan-content plan-content-line">
            <ul class="item-row">
              <li class="width45">
                <span class="li-first-span">服务人员</span><span class="li-last-span">{{ taskRecordObj.designatePersonName }}</span>
              </li>
              <li class="width45">
                <span class="li-first-span">人员电话</span><span class="li-last-span">{{ taskRecordObj.designatePersonPhone }}</span>
              </li>
            </ul>
            <TransitionHeight :ref="'ZP' + taskIndex" :heigtRef="'ZPBox' + taskIndex">
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span">服务部门</span><span class="li-last-span">{{ taskRecordObj.designateDeptName }}</span>
                </li>
              </ul>
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span">转派说明</span><span class="li-last-span">{{ taskRecordObj.feedbackExplain }}</span>
                </li>
              </ul>
            </TransitionHeight>
          </div>
        </div>
      </div>
      <!-- 处理 -->
      <div v-if="workOrderDetail.olgTaskManagement.flowcode !== '6' && !workOrderDetail.relevanceWorknumFlag && isHandle" class="reserve-plan">
        <div class="plan-title">
          <div class="color-box"><i class="el-icon-time"></i></div>
          <div class="linear-g">
            <span class="linear-g-span1">处理</span>
            <span>{{ $tools.dateToStr() }}</span>
          </div>
        </div>
        <div class="plan-content plan-content-line">
          <workOrderHandle
            ref="workOrder"
            :designateDeptCode="workOrderDetail.olgTaskManagement.designateDeptCode"
            :deviceId="workOrderDetail.olgTaskManagement.deviceId"
            :workOrderType="workOrderDetail.olgTaskManagement.flowcode"
            :workOrderObj="{}"
            :data="workOrderDetail.olgTaskManagement"
          ></workOrderHandle>
        </div>
      </div>
    </div>
    <div class="btn-box">
      <el-button v-if="workOrderDetail.olgTaskManagement.flowcode != 5 && isHandle" type="primary" @click="handleSubmit">提交</el-button>
      <el-button type="primary" plain @click="handleClose">关闭</el-button>
    </div>
  </div>
</template>

<script>
import TransitionHeight from './transitionHeight.vue'
import moment from 'moment'
import workOrderHandle from './workOrderHandle.vue'
import store from '@/store/index'
let that
export default {
  name: 'workOrderDetailList',
  components: {
    TransitionHeight,
    workOrderHandle
  },
  filters: {
    UDfilter(val) {
      if (val === '0') {
        return '紧急事故'
      } else if (val === '1') {
        return '紧急催促'
      } else {
        return '一般'
      }
    }
    // cancelFilter(val) {
    //   const row = that.cancelReasonOptions.forEach((e) => {
    //     if (row.length && e.value === val) {
    //       this.cancelFilter = e.label
    //     } else {
    //       this.cancelFilter = ''
    //     }
    //   })
    // }
  },
  props: {
    rowData: {
      type: Object,
      default: function () {
        return {}
      }
    },
    isHandle: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      moment,
      cancelFilter: '',
      rateTexts: ['非常差', '差', '一般', '满意', '非常满意'],
      cancelReasonOptions: [],
      workOrderDetail: {
        olgTaskManagement: {
          repairWork: ''
        }
      }
    }
  },
  beforeCreate() {
    that = this
  },
  created() {
    const params = this.rowData
    this.getWorkOrderDetail(params)
    this.getNewStaffInfoById()
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    getNewStaffInfoById() {
      this.$api
        .getNewStaffInfoById({
          id: store.state.user.userInfo.user.staffId
        })
        .then((res) => {
          console.log('getNewStaffInfoById', res)
          if (res.code == '200') {
            localStorage.setItem('userInfo', JSON.stringify(res.data))
          }
        })
    },
    handleSubmit() {
      this.$refs.workOrder.handleSubmit()
    },
    getWorkOrderDetail(params) {
      this.$api.getWorkOrderDetail({ id: params.id, operSource: 'souye' }).then((res) => {
        // console.log('@', res)
        this.workOrderDetail = res
        // 已取消 获取 字典项
        if (res.taskRecord.length && res.taskRecord.some((e) => e.operationCode === '7')) {
          this.getIomsDictList('cancel_reason')
        }
      })
    },
    // 获取字典
    getIomsDictList(type) {
      const params = {
        type: type
      }
      this.$api.getIomsDictList(params).then((res) => {
        this.cancelReasonOptions = res
      })
    },
    transform(val) {
      let arr = this.cancelReasonOptions.find((i) => {
        return i.value == val
      })
      if (arr) {
        return arr.label
      } else {
        return ''
      }
    },
    // 展开关闭事件
    collectEvent(box, i) {
      // console.log(this.$refs[box + 'right' + i][0].style.display)
      if (this.$refs[box + 'right' + i][0].style.display === 'inline-block') {
        this.$refs[box + 'right' + i][0].style.display = 'none'
        this.$refs[box + 'down' + i][0].style.display = 'inline-block'
        this.$refs[box + i][0].show = true
      } else {
        this.$refs[box + 'right' + i][0].style.display = 'inline-block'
        this.$refs[box + 'down' + i][0].style.display = 'none'
        this.$refs[box + i][0].show = false
      }
    },
    /*
     *  综合维修工单科室及地点的历史申报记录
     * type 对应查询类型：1：地点 2：科室
     * code 对应的科室地点code或者地点code
     * name 对应的科室地点name或者地点name
     */
    openTab(type, code, name) {
      let historyDept = '' // eslint-disable-line no-unused-vars
      let historyDeptName = '' // eslint-disable-line no-unused-vars
      let historyLocaltion = '' // eslint-disable-line no-unused-vars
      let historyLocaltionName = '' // eslint-disable-line no-unused-vars
      if (type === 1) {
        historyLocaltion = code
        if (code !== undefined && code !== '' && code !== null) {
          historyLocaltion = code.replace(/\_/g, ',') // eslint-disable-line no-useless-escape
        }
        historyLocaltionName = name
      } else if (type === 2) {
        historyDept = code
        historyDeptName = name
      }
      console.log(historyDept, historyDeptName, historyLocaltion, historyLocaltionName)
      this.$router.push({
        path: '/maintenanceService/workOrderTable',
        query: {
          typeName: 'location',
          historyDept: historyDept,
          historyLocaltion: historyLocaltion,
          workTypeCode: this.workOrderDetail.olgTaskManagement.workTypeCode
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.btn-box {
  text-align: right;
}

.form-detail {
  height: 100%;
  width: 100%;
  padding: 10px 25px;
  overflow-y: scroll;
  box-sizing: border-box;

  .reserve-scorll {
    width: 100%;
    height: calc(100% - 50px);
    overflow-y: auto;
    box-sizing: border-box;
    padding-right: 15px;
  }

  .reserve-plan {
    width: 100%;
    position: relative;
    // height: inherit;
    .plan-title {
      display: flex;
      width: 100%;
      height: 30px;

      .color-box {
        height: 1.25rem;
        width: 1.25rem;
        color: #3562db;
        line-height: 1.25rem;
        text-align: center;
        border-radius: 3px;
        font-size: 16px;
        margin: auto;
      }

      .linear-g {
        margin-left: 2px;
        width: calc(100% - 22px);
        height: 100%;
        background: #f6f5fa;
        font-size: 13px;
        line-height: 30px;
        font-family: PingFangSC-Medium;
        color: #3562db;
        border-radius: 6px;
        cursor: pointer;

        .linear-g-span1 {
          margin: 0 15px;
          font-weight: 600;
        }

        .title-icon {
          float: right;
          line-height: 30px;
          margin-right: 5px;
        }
      }
    }

    .plan-content {
      width: calc(100% - 33px);
      margin-left: 11px;
      color: #676a6c;
      font-size: 13px;

      .item-row {
        width: 100%;
        display: flex;
        padding: 20px 0 20px 30px;
        box-sizing: border-box;

        .width30 {
          width: 30%;
        }

        .width45 {
          width: 45%;
        }

        .width90 {
          width: 90%;
          display: flex;
          align-items: center;
        }

        ::v-deep .el-image__error,
        ::v-deep .el-image__placeholder {
          background: center;
        }

        .li-first-span {
          display: inline-block;
          width: 90px;
          white-space: nowrap;
          font-size: 14px;
          font-family: PingFang-SC-Medium, PingFang-SC;
          font-weight: 700;
          color: #676a6c;
        }

        .li-last-span {
          font-size: 14px;
          font-family: PingFang-SC-Medium, PingFang-SC;
          font-weight: 500;
          color: #676a6c;
        }

        .recording-ALabel {
          color: #0379f1;
          font-size: 14px;
          text-decoration: none;

          i {
            margin: 0 3px 0 10px;
          }
        }

        #audio-box {
          display: flex;
        }

        #audio-box > audio {
          width: 260px;
          height: 30px;
        }

        #audio-box > a {
          width: 40px;
          text-align: center;
          background-color: #2cc7c5;
          height: 35px;
          line-height: 35px;
          color: #fff;
          border-radius: 5px;
          margin-left: 10px;
        }
      }

      .show-content {
        width: 100%;
      }
    }

    .plan-content-line {
      border-left: 1px solid #676a6c;
    }

    .plan-content-noline {
      width: calc(100% - 33px);
      margin-left: 11px;
      padding: 20px 0 20px 20px;
      color: #b5bacb;
      font-size: 13px;
    }

    .maint-table {
      width: 60%;

      td {
        padding: 5px 0 5px 10px;
        border: 1px solid #eee;
        height: 25px;
        line-height: 25px;
        display: table-cell;
        vertical-align: middle;
        color: #676a6c;
      }

      tr:first-child {
        background-color: #f5f6fc;
      }

      td:first-child {
        width: 35%;
      }

      .one-line {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .repair-work {
      float: right;
      margin: 15px 10px;
      position: absolute;
      top: 40px;
      right: 10px;

      ::v-deep .el-checkbox__label {
        color: #121f3e;
      }
    }
  }
}
</style>
