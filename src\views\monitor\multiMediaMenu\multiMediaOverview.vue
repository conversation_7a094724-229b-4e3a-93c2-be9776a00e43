<!-- 多媒体运行总览 -->
<template>
  <OverviewPublic :projectName="projectName" />
</template>
<script>
import OverviewPublic from './../components/overviewPublic/index.vue'
export default {
  name: 'multiMediaOverview',
  components: { OverviewPublic },
  data() {
    return {
      projectName: '多媒体系统监测'
      // callList: [
      //   {sysName: '空调监测'}, 
      //   {sysName: '环境监测'}, 
      //   {sysName: '给排水监测'}, 
      //   {sysName: '广播终端'}, 
      //   {sysName: 'LED屏幕'}, 
      //   {sysName: '多媒体屏幕'}, 
      //   {sysName: '防排烟风机'}
      // ]
    }
  }
}
</script>