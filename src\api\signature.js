/*
 * @Author: hedd heding<PERSON>@sinomis.com
 * @Date: 2025-05-13 14:50:51
 * @LastEditors: hedd <EMAIL>
 * @LastEditTime: 2025-06-11 16:46:33
 * @FilePath: \ybs_h5\src\utils\signature.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// utils/signature.js
import CryptoJS from 'crypto-js'
const SECRET_KEY = 'A615324747421311' // 前后端统一，建议配置到环境变量
// 生成签名
export function generateSignature(config) {
  const combinedParams = {}
  // 提取 query 参数（来自 URL）
  Object.assign(combinedParams, getQueryParamsFromUrl(config.url || ''))
  // 提取 body 参数（JSON 或 form-urlencoded
  const configData = config.data || {}
  if (config.method == 'GET' || config.method == 'get') {
    // Convert all values in config.params to strings before merging
    const stringParams = {}
    if (config.params && Object.keys(config.params).length) {
      Object.keys(config.params).forEach((key) => {
        const value = config.params[key]
        // axios 的默认行为，如果 value 为 null 或 undefined，会自动删除该 key
        if (value !== null && value !== undefined) {
          stringParams[key] = String(value)
        }
      })
    }
    Object.assign(combinedParams, stringParams)
  }
  if (configData && Object.keys(configData).length) {
    // form-urlencoded及传递的参数是form-urlencoded但是没有设置contentType的
    if (
      typeof configData === 'string' &&
      ((config.headers['Content-Type'] && config.headers['Content-Type'].includes('application/x-www-form-urlencoded')) || isValidFormData(configData))
    ) {
      Object.assign(combinedParams, getParamsFromFormData(configData, 'encodeUrl'))
    } else if (typeof configData === 'object' && Object.prototype.toString.call(configData) === '[object Object]') {
      Object.assign(combinedParams, configData)
    } else {
      return {}
    }
  }
  if (Object.keys(combinedParams).length) {
    const nonce = generateNonce(6)
    const timestamp = Date.now().toString()
    const sortedData = sortObject(combinedParams)
    const stringToSign = JSON.stringify(sortedData) + timestamp + nonce
    // console.log(config.url, configData, combinedParams, stringToSign, 'stringToSign')
    const hash = CryptoJS.HmacSHA256(stringToSign, SECRET_KEY)
    return {
      signature: CryptoJS.enc.Hex.stringify(hash),
      timestamp: timestamp,
      nonce: nonce
    }
  } else {
    return {}
  }
}
// 排序对象
function sortObject(obj) {
  const sorted = {}
  Object.keys(obj)
    .sort()
    .forEach((key) => {
      sorted[key] = obj[key]
    })
  return sorted
}
// 生成随机 nonce
function generateNonce(length = 16) {
  const array = new Uint32Array(length)
  window.crypto.getRandomValues(array)
  return Array.from(array, (dec) => ('0' + dec.toString(16)).slice(-2))
    .join('')
    .substring(0, length)
}
// 提取 query 参数（来自 URL）
function getQueryParamsFromUrl(url) {
  const queryIndex = url.indexOf('?')
  if (queryIndex === -1) return {}
  const queryString = url.substring(queryIndex + 1)
  return getParamsFromFormData(queryString, 'decodeUrl')
}
// 提取 FormData 参数
function getParamsFromFormData(queryString, codeUrl = '') {
  const result = {}
  // First identify parameters without values in original query string
  const paramsWithoutValue = new Set()
  const segments = queryString.split('&')
  for (const segment of segments) {
    if (!segment.includes('=')) {
      paramsWithoutValue.add(segment)
    }
  }
  const formData = new URLSearchParams(queryString)
  for (const [key, value] of formData.entries()) {
    // 判断key是否包含特殊字符，只对包含特殊字符的key进行编码
    const hasSpecialChars = /[\[\]{}()<>@#$%^&*+=|\\\/\s\u4e00-\u9fa5]/.test(key)
    const finalKey = hasSpecialChars ? encodeURIComponent(key) : key
    // If this key was originally without a value, set it to null
    if (paramsWithoutValue.has(key) && value === '') {
      result[finalKey] = null
    } else {
      if (codeUrl === 'encodeUrl') {
        result[finalKey] = encodeURIComponent(value)
      } else if (codeUrl === 'decodeUrl') {
        result[finalKey] = decodeURIComponent(value)
      } else {
        result[finalKey] = value
      }
    }
  }
  return result
}
// 判断是否为有效的表单数据
function isValidFormData(str) {
  try {
    // 先尝试解码，因为输入可能是已编码的
    const decodedStr = decodeURIComponent(str)
    const params = new URLSearchParams(decodedStr)
    // 检查是否至少有一个参数
    if (params.toString().length === 0) {
      return false
    }
    // 检查每个参数是否都是有效的键值对
    for (const [key, value] of params.entries()) {
      if (!key || key.trim() === '') {
        return false
      }
    }
    return true
  } catch (e) {
    return false
  }
}
