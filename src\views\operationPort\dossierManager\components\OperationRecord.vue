<template>
  <div>
    <div class="right-heade">
      <el-input v-model="formData.operInfo" placeholder="操作人/操作项/操作结果" suffix-icon="el-icon-search" clearable />
      <el-select v-model="formData.operType">
        <el-option v-for="item in optionList" :key="item.value" placeholder="操作类型" :label="item.label" :value="item.value"></el-option>
      </el-select>
      <el-date-picker
        v-model="formData.datetimerange"
        value-format="timestamp"
        type="datetimerange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="handleDateChange"
      >
      </el-date-picker>
      <div>
        <el-button type="primary" @click="search">查询</el-button>
        <el-button type="primary" plain @click="reset">重置</el-button>
      </div>
    </div>
    <div>
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="logDate" label="操作时间">
          <template slot-scope="scope">
            {{ moment(scope.row.logDate).format('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </el-table-column>
        <el-table-column prop="logPersonName" label="操作人"> </el-table-column>
        <el-table-column prop="operType" label="操作类型">
          <template slot-scope="scope">
            {{ optionList.find((item) => item.value === scope.row.operType).label }}
          </template>
        </el-table-column>
        <el-table-column prop="operItem" label="操作项"> </el-table-column>
        <el-table-column prop="operResult" label="操作结果"> </el-table-column>
      </el-table>
      <el-pagination
        class="pagination"
        :current-page="pageData.current"
        :page-sizes="[15, 30, 45, 60]"
        :page-size="pageData.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
      </el-pagination>
    </div>
  </div>
</template>
<script>
import moment from 'moment'
const list = [
  {
    label: '创建',
    value: '0'
  },
  {
    label: '删除',
    value: '1'
  },
  {
    label: '编辑',
    value: '2'
  },
  {
    label: '共享',
    value: '3'
  },
  {
    label: '恢复',
    value: '4'
  },
  {
    label: '取消共享',
    value: '5'
  }
]
const list2 = [
  {
    label: '创建',
    value: '0'
  },
  {
    label: '删除',
    value: '1'
  },
  {
    label: '编辑',
    value: '2'
  },
  {
    label: '借阅',
    value: '3'
  },
  {
    label: '恢复',
    value: '4'
  }
]
export default {
  data() {
    return {
      moment,
      formData: {
        operInfo: '',
        operType: '',
        logDateStart: '',
        logDateEnd: '',
        datetimerange: []
      },
      tableLoading: false,
      tableData: [],
      pageData: {
        current: 1,
        size: 15,
        total: 0
      }
    }
  },
  computed: {
    optionList() {
      return this.$route.path === '/dossierManager/myDocumentDetails' ? list : list2
    }
  },
  created() {
    this.handleQueryTablelist()
  },
  methods: {
    handleQueryTablelist() {
      const { id: archiveId } = this.$route.query
      const params = {
        archiveId,
        ...this.formData,
        ...this.pageData,
        logType: '0' // 0：操作记录，1：浏览记录
      }
      delete params.datetimerange
      this.$api.fileManagement.recordQueryByPage(params).then((res) => {
        this.tableData = res.data.records
        this.pageData.total = res.data.total
      })
    },
    search() {
      this.pageData.current = 1
      this.handleQueryTablelist()
    },
    handleDateChange(e) {
      this.formData.logDateStart = e[0]
      this.formData.logDateEnd = e[1]
    },
    reset() {
      Object.keys(this.formData).forEach((key) => {
        if (key === 'datetimerange') {
          this.formData[key] = []
        } else {
          this.formData[key] = ''
        }
      })
      this.search()
    },
    handleSizeChange(size) {
      this.pageData.size = size
      this.handleQueryTablelist()
    },
    handleCurrentChange(current) {
      this.pageData.current = current
      this.handleQueryTablelist()
    }
  }
}
</script>
<style lang="scss" scoped>
.right-heade {
  display: flex;
  margin: 16px 0;
  > div {
    flex: 1;
    margin-right: 20px;
  }
}
.pagination {
  margin-top: 16px;
}
</style>
