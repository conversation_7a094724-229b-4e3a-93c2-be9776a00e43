<template>
  <div class="historical_location">
    <TablePage
      ref="table"
      border
      :showPage="true"
      :tableColumn="tableColumn"
      :data="tableData"
      height="calc(100% - 40px)"
      :pageData="pageData"
      :pageProps="pageProps"
      @pagination="paginationChange"
    >
    </TablePage>
  </div>
</template>
<script>
export default {
  name: 'HistoricalLocation',
  props: {
    searchForm: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      tableColumn: [
        {
          prop: 'time',
          label: '时间'
        },
        {
          prop: 'beaconName',
          label: '定位信标'
        },
        {
          prop: 'gridName',
          label: '位置信息'
        }
      ],
      tableData: [],
      pageData: {
        current: 1,
        size: 15,
        total: 0
      },
      pageProps: {
        page: 'current',
        pageSize: 'size',
        total: 'total'
      }
    }
  },
  mounted() {
    this.getDetails()
  },
  methods: {
    getDetails() {
      let { projectCode, surveyCode, dataRange, paramId } = this.searchForm
      const params = {
        page: this.pageData.current,
        pageSize: this.pageData.size,
        projectCode: projectCode,
        paramId,
        imsCode: surveyCode,
        startTime: dataRange[0],
        endTime: dataRange[1]
      }
      this.$api.getLocationInfo(params).then((res) => {
        if (res.code == '200') {
          this.tableData = res.data.dataList
          this.pageData.total = res.data.count
        } else {
          this.tableData = []
          this.pageData.total = 0
        }
      })
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getDetails()
    }
  }
}
</script>
<style lang="scss" scoped>
.historical_location {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 0 16px 10px 16px;
}
</style>
