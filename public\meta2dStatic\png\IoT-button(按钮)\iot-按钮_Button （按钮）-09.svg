<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 150 150"><defs><style>.cls-1{fill:url(#未命名的渐变_105);}.cls-2{fill:url(#未命名的渐变_51);}</style><linearGradient id="未命名的渐变_105" x1="-0.06" y1="75" x2="149.94" y2="75" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#e2e2e2"/><stop offset="0.03" stop-color="#d0d0d0"/><stop offset="0.06" stop-color="#bcbcbc"/><stop offset="0.09" stop-color="#b5b5b5"/><stop offset="0.49" stop-color="#f6f6f6"/><stop offset="0.63" stop-color="#f3f3f3"/><stop offset="0.73" stop-color="#eaeaea"/><stop offset="0.82" stop-color="#dbdbdb"/><stop offset="0.9" stop-color="#c6c6c6"/><stop offset="0.97" stop-color="#aaa"/><stop offset="1" stop-color="#9b9b9b"/></linearGradient><linearGradient id="未命名的渐变_51" x1="14.01" y1="75" x2="135.86" y2="75" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#f6f6f6"/><stop offset="0.29" stop-color="#f3f3f3"/><stop offset="0.48" stop-color="#eaeaea"/><stop offset="0.65" stop-color="#dbdbdb"/><stop offset="0.8" stop-color="#c6c6c6"/><stop offset="0.94" stop-color="#aaa"/><stop offset="1" stop-color="#9b9b9b"/></linearGradient></defs><title>iot-按钮</title><g id="图层_9" data-name="图层 9"><circle class="cls-1" cx="74.94" cy="75" r="75"/><circle class="cls-2" cx="74.94" cy="75" r="60.93"/></g></svg>