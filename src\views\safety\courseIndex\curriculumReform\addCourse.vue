<template>
  <PageContainer :footer="true" v-loading="contenerLoading">
    <div slot="content" class="table-content">
      <div class="topFilter">
        <div class="backBar">
          <span style="cursor: pointer" @click="onCancel">
            <i class="el-icon-arrow-left"></i>
            {{ typeStr == "add" ? "创建课程" : "编辑课程" }}
          </span>
        </div>
      </div>
      <div class="courseContent">
        <div class="courseTab">
          <div class="left activeLeft" @click="upStep">1.设置课程基础信息</div>
          <div class="centerLeft"></div>
          <div v-if="active == '1'" class="centerRight"></div>
          <div :class="['right', active == 1 ? 'activeRight' : '']" @click="nextStep">
            2.设置课程目录
          </div>
        </div>
        <div class="courseInfo" v-if="active == '0'">
          <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
            基础信息
          </div>
          <el-form label-width="120px" :model="formInfo" :rules="rules" ref="formInfo" class="formInfo">
            <el-form-item label="课程名称" prop="courseName">
              <el-input v-model="formInfo.courseName" placeholder="请输入课程名称" show-word-limit maxlength="30"
                style="width: 300px"></el-input>
            </el-form-item>
            <el-form-item label="所属科目" prop="subjectId">
              <el-cascader v-model="formInfo.subjectId" clearable class="sino_sdcp_input mr15" style="width: 300px"
                :options="subjectList" :props="props" placeholder="请选择类型"></el-cascader>
            </el-form-item>
            <el-form-item label="课程封面" class="">
              <span style="color: red; margin-left: -80px">*</span>
              <el-upload 
                ref="uploadFile" 
                drag 
                multiple 
                class="mterial_file courseImg" 
                action="string"
                list-type="picture-card" 
                :file-list="fileCourse" :http-request="() => {
                    return httpRequest('0');
                  }
                  " 
                accept=".jpg,.png," 
                :limit="1" 
                :on-exceed="() => {
                  return handleExceed('0');
                }
                " 
                :before-upload="(file) => {
                  return beforeAvatarUpload(file, '0');
                }
                " 
                :on-remove="(file, fileList) => {
                  return handleRemove(file, fileList, '0');
                }
                " 
                :on-change="(file, fileList) => {
                  return fileChange(file, fileList, '0');
                }
                ">
                <i class="el-icon-upload"></i>
                <div class="el-upload__text" style="top: 33px">
                  将文件拖到此处，或
                  <em>点击上传</em>
                </div>
                <div slot="tip" class="el-upload__tip">
                  可上传一张以内,小于5M的图片
                </div>
              </el-upload>
            </el-form-item>
            <el-form-item label="学习中是否认证" prop="taskBookType">
              <div class="certified">
                <el-radio-group v-model.trim="formInfo.isAuth" @input="isAuthBtn">
                  <el-radio v-for="item in certifiedList" :key="item.id" :label="item.id">{{ item.label }}</el-radio>
                </el-radio-group>
                <div class="cretifiedOk" v-if="formInfo.isAuth == 0">
                  <el-tooltip placement="top" effect="light" popper-class="custom-tooltip">
                    <div slot="content">
                      <p>开启学习中认证后 :</p>
                      <p style="margin: 10px 0">
                        1.手机端根据设置时间，课件播放中暂停播放开启、脸认证，通过后自动继续学习
                      </p>
                      <p>
                        2.电脑端根据设置时间，课件播放中暂停播放单出确认框点击确认后继续学习
                      </p>
                    </div>
                    <span class="iconfont icon-24gl-questionCircle"></span>
                  </el-tooltip>
                  <el-input type="number" placeholder="每间隔" min="1" style="width: 300px" v-model="formInfo.frequency">
                    <template slot="append">分钟/次</template>
                  </el-input>
                </div>
              </div>
            </el-form-item>
            <div v-if="!(typeStr == 'edit' && formInfo.approvalState != '0')">
              <el-form-item label="是否录入试题" prop="taskBookType">
                <el-radio-group v-model="formInfo.isEntry">
                  <el-radio v-for="item in enterList" :key="item.id" :label="item.id">{{ item.label }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item v-if="formInfo.isEntry == 0" label="录入试题" prop="taskBookType">
                <el-button type="primary" @click="batchBtn">批量录题</el-button>
                <el-button type="primary" plain class="delBtn" @click="addEnter">逐题录题</el-button>
                <div class="enterNum">
                  <span>已录入课时试题数：{{ enterNum || 0 }}题</span>
                  <span v-if="enterNum" @click="questionsDetails">查看已录入题目</span>
                </div>
              </el-form-item>
            </div>

            <el-form-item label="课程公开范围" prop="deptId">
              <el-cascader v-model="formInfo.deptId" placeholder="请选择课程公开范围" :options="courseRangeList" :props="deptTree"
                :show-all-levels="false" clearable filterable collapse-tags style="width: 300px">
              </el-cascader>
            </el-form-item>
            <el-form-item label="课程介绍" prop="">
              <el-input v-model="formInfo.comments" type="textarea" show-word-limit maxlength="200" placeholder="请输入"
                :autosize="{ minRows: 4, maxRows: 6 }" style="width: 400px"></el-input>
            </el-form-item>
          </el-form>
        </div>
        <div class="courseList" v-if="active == '1'">
          <class-hour ref="classHour" :coursePeriodDTOList="formInfo.coursePeriodDTOList" @addQuestion="addQuestion"
            @submit="submit"></class-hour>
        </div>
      </div>
      <el-dialog class="changeStatusDialog" title="批量录题" :visible.sync="dialogVisible" width="50%"
        :before-close="handleClose">
        <el-form label-width="140px" :model="formInfo" :rules="rules" ref="formInfo" class="formInfo">
          <el-form-item label="所属科目" prop="subjectId">
            <el-cascader v-model="formInfo.subjectId" clearable class="sino_sdcp_input mr15" :options="subjectList"
              :props="props" disabled placeholder="请选择类型" style="width: 300px"></el-cascader>
          </el-form-item>
          <el-form-item label="知识点所属课程" prop="courseName">
            <el-input v-model="formInfo.courseName" placeholder="请输入课程名称" show-word-limit maxlength="30"
              style="width: 300px" disabled></el-input>
          </el-form-item>
          <el-form-item label="">
            <el-upload 
              ref="uploadFile2" 
              drag 
              multiple 
              class="mterial_file file" 
              action="string"
              :file-list="fileQuestions" :http-request="() => {
                  return httpRequest('1');
                }
                " 
              accept=".excel,.xlsx" :limit="1" :on-exceed="() => {
                return handleExceed('1');
              }
              " 
              :before-upload="(file) => {
                return beforeAvatarUpload(file, '1');
              }
              " 
              :on-remove="(file, fileList) => {
                return handleRemove(file, fileList, '1');
              }
              " 
              :on-change="(file, fileList) => {
                return fileChange(file, fileList, '1');
              }
              ">
              <i class="el-icon-upload"></i>
              <div class="el-upload__text" style="top: 100px">
                将文件拖到此处，或
                <em>点击上传</em>
              </div>
              <div slot="tip" class="el-upload__tip">支持上传单个Excel文件</div>
            </el-upload>
          </el-form-item>
          <span>下载模板文件：</span>
          <span v-for="(item, index) in exTypeList" :key="index" @click="downloadTemplate(item.id)" class="exType">{{
            item.label }}</span>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="handleClose">取消</el-button>
          <el-button type="primary" @click="batchAddQuestions">确认</el-button>
        </span>
      </el-dialog>
      <!-- 添加试题 -->
      <test-questions ref="testQuestions" :drawerDialog="drawerDialog" :subjectId="formInfo.subjectId"
        :courseName="formInfo.courseName" :testQuestionsList="testQuestionsList" @testQuestions="testQuestions"
        @closeDrawer="closeDrawer"></test-questions>
      <!-- 逐题录题 -->
      <add-questions :drawerDialog="addDrawerDialog" :subjectId="formInfo.subjectId" :courseName="formInfo.courseName"
        :type="addType" @getEnterQuestionsList="getEnterQuestionsList" @closeDrawer="closeAddDrawer"></add-questions>
      <!-- 已录入题目 -->
      <questions-info :drawerDialog="questionsInfoDialog" :enterQuestionsList="enterQuestionsList"
        @deletQuestions="deletQuestions" @closeDrawer="closeQuestionsInfo"></questions-info>
    </div>
    <div slot="footer">
      <el-button v-if="active == '0'" type="primary" @click="nextStep">下一步</el-button>
      <el-button v-if="active == '1'" type="primary" plain @click="onCancel">取消</el-button>
      <el-button v-if="active == '1'" type="primary" plain @click="upStep">上一步</el-button>
      <el-button v-if="active == '1'" type="primary" @click="addCourse('0')">保存草稿</el-button>
      <el-button v-if="active == '1'" type="primary" @click="addCourse('3')">提交</el-button>
    </div>
  </PageContainer>
</template>
<script>
import classHour from "./components/classHour.vue";
import testQuestions from "../components/testQuestions.vue";
import AddQuestions from "./components/addQuestions.vue";
import questionsInfo from "./components/questionsInfo.vue";
import { transData, ListTree } from '@/util'
import axios from "axios";
export default {
  components: { classHour, testQuestions, AddQuestions, questionsInfo },
  data() {
    return {
      props: {
        children: "children",
        label: "name",
        value: "id",
        checkStrictly: true,
        emitPath: false,
      },
      routeInfo: "",
      id: "",
      typeStr: "",
      active: "0",
      formInfo: {
        courseName: "",
        approvalState: "",
        subjectId: null,
        coverUrl: "",
        isAuth: 1, //是否认证
        frequency: "", //认证时间
        isEntry: 1,
        deptId: "", // 公开范围
        comments: "", //课程介绍
        coursePeriodDTOList: [
          {
            periodName: "",
            type: 1,
            url: "",
            fileEcho: [],
            coursePeriodQuestionList: [],
          },
        ],
      },
      fileCourse: [], //课程封面
      fileQuestions: [], //批量录题
      subjectList: [],
      certifiedList: [
        {
          id: 1,
          label: "不认证",
        },
        {
          id: 0,
          label: "学习中认证",
        },
      ],
      enterList: [
        {
          id: 1,
          label: "不录入试题",
        },
        {
          id: 0,
          label: "录入试题",
        },
      ],
      courseRangeList: [],
      deptTree: {
        children: "children",
        label: "teamName",
        value: "id",
        multiple: true,
        emitPath: false,
      },
      rules: {
        courseName: [
          { required: true, message: "请输入课程名称", trigger: "blur" },
          {
            min: 1,
            max: 30,
            message: "长度在 1 到 30 个字符",
            trigger: "blur",
          },
        ],
        subjectId: [
          { required: true, message: "请选择所属科目", trigger: "change" },
        ],
      },
      dialogVisible: false,
      exTypeList: [
        {
          id: 1,
          label: "单选题",
        },
        {
          id: 2,
          label: "多选题",
        },
        {
          id: 3,
          label: "判断题",
        },
      ],
      drawerDialog: false,
      addDrawerDialog: false,
      questionsInfoDialog: false,
      enterQuestionsList: [],
      enterNum: "", //已录入试题数
      questionsIndex: "", //课时index
      addType: 'enter',
      testQuestionsList: [],
      contenerLoading:false
    };
  },
  created() {
    this.routeInfo = JSON.parse(sessionStorage.getItem("routeInfo"));
    console.log(this.routeInfo,'this.routeInfo123');
    this.getDeptList();
    this.getTblleList();
    this.id = this.$route.query.id || "";
    this.typeStr = this.$route.query.typeStr || "";
    if (this.id) {
      this.getDetails();
    }
  },
  methods: {
    getTblleList() {
      let data = {
        pageNo: 1,
        pageSize: 999,
      };
      this.$http.subjectListAll(data).then((res) => {
        if (res.code == 200) {
          this.subjectList = this.$tools.transData(
            res.data,
            "id",
            "parentId",
            "children"
          )
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 获取组织
    getDeptList() {
      this.$http.getDeptList({}).then((res) => {
        this.courseRangeList = transData(
          res.data.list,
          "id",
          "parentId",
          "children"
        );
      });
    },
    // 获取详情
    getDetails() {
      this.$http.getCourseDetails({ id: this.id }).then((res) => {
        if (res.code == "200") {
          this.fileCourse = [
            {
              url: res.data.coverUrl,
            },
          ];
          if (res.data.coursePeriodDTOList.length) {
            res.data.coursePeriodDTOList.forEach((item) => {
              item.duration = item.duration / 60;
              if (item.url) {
                item.fileEcho = JSON.parse(item.url);
              }
              if (item.coursePeriodQuestionList) {
                item.coursePeriodQuestionList.forEach((i) => {
                  i.isExpand = false;
                  i.options = JSON.parse(i.options);
                  if (i.type == "2") {
                    i.answer = i.answer.split(",");
                  }
                });
              } else {
                item.coursePeriodQuestionList = [];
              }
            });
          }
          res.data.deptId = res.data.deptId.split(",");
          this.formInfo = res.data;
          if (this.typeStr == "edit" && this.formInfo.approvalState == "0") {
            this.getEnterQuestionsList();
          }
        } else {
          this.$message.error(res.msg)
        }
      });
    },
    // 是否认证
    isAuthBtn(val) {
      if (val == "0") {
        this.formInfo.frequency = "";
      }
    },
    // 上一步
    upStep() {
      this.active = "0";
      this.formInfo.coursePeriodDTOList =
        this.$refs.classHour.classHourInfo.classList;
    },
    // 下一步
    nextStep() {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          if (!this.fileCourse.length) {
            return this.$message.error("请上传课程封面");
          }
          this.active = "1";
        }
      });
    },
    // 提交课程
    addCourse(type) {
      this.$refs.classHour.getValidate(type);
    },
    submit(type) {
      this.contenerLoading = true
      var newObj = {};
      newObj = JSON.parse(JSON.stringify(this.formInfo));
      newObj.coursePeriodDTOList.forEach((item) => {
        if (item.type != "1") {
          item.duration = item.duration ? item.duration * 60 : "";
        }
        console.log(item.coursePeriodQuestionList,'coursePeriodQuestionList');
        if (item.coursePeriodQuestionList) {
          item.coursePeriodQuestionList.forEach((k, index) => {
            k.sort = index;
            k.options = JSON.stringify(k.options);
            if (k.type == '2') {
              k.answer = k.answer.join(',')
            }
          });
        }
      });
      let params = {
        createName: this.routeInfo.createName,
        ...newObj,
      };
      if (this.id) {
        //是否为编辑
        params.updateBy = this.routeInfo.userId,
        params.id = this.id;
      }
      params.approvalState = type;
      params.deptId = this.formInfo.deptId.length
        ? this.formInfo.deptId.join(",")
        : "";
      console.log(params,'params');
      this.$http.addCourse(params).then((res) => {
          if (res.code == "200") {
            this.$router.go(-1);
            // 提交成功
          } else {
            this.$message.error(res.msg);
          }
          this.contenerLoading = false
        })
        .catch((res) => {
          this.$message.error(res.data.message);
        });
    },
    // 批量录题----------------------------------------------------
    batchBtn() {
      if (!this.formInfo.subjectId || !this.formInfo.courseName) {
        return this.$message.error("请先填写所属科目和知识点所属课程");
      }
      this.dialogVisible = true;
    },
    handleClose() {
      this.dialogVisible = false;
      this.fileQuestions = [];
    },
    // 批量录题确定
    batchAddQuestions() {
      if (!this.fileQuestions.length) {
        return this.$message.error("请先选择模板");
      }
      let formData = new FormData();
      formData.append("hospitalCode", this.routeInfo.hospitalCode);
      formData.append("unitCode", this.routeInfo.unitCode);
      formData.append("subjectId", this.formInfo.subjectId);
      formData.append("systemCode", this.routeInfo.systemCode);
      formData.append("file", this.fileQuestions[0].raw);
      formData.append("courseName", this.formInfo.courseName);
      formData.append("isDraft", true);
      formData.append('importType', '0')
      axios({
        method: "post",
        url: __PATH.BASE_URL_LABORATORY + "question/import",
        data: formData,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded;charset=utf8",
          token: this.routeInfo.token,
        },
      })
        .then((res) => {
          if (res.data.code == "200") {
            this.handleClose();
            this.getEnterQuestionsList();
          } else {
            this.$message.error(res.data.msg);
          }
        })
        .catch((res) => {
          this.$message.error("上传失败");
        });
    },
    // 获取录入试题
    getEnterQuestionsList() {
      let data = {
        subjectId: this.formInfo.subjectId,
        systemCode: this.routeInfo.systemCode,
        createBy: this.routeInfo.userId,
        pageNo: 1,
        pageSize: 9999,
        isDraft: true, //是否查看
        moduleType: "2",
        courseName: this.formInfo.courseName,
      };
      this.$http.getQuestionsList(data).then((res) => {
        if (res.code == "200") {
          if (res.data.list.length) {
            res.data.list.forEach((i) => {
              i.options = JSON.parse(i.options);
              i.isExpand = true;
            });
            this.enterQuestionsList = res.data.list;
            this.enterNum = res.data.total;
          }
        }
      });
    },
    // 逐题录入-------
    addEnter() {
      if (!this.formInfo.subjectId || !this.formInfo.courseName) {
        return this.$message.error("请先填写所属科目和知识点所属课程");
      }
      this.addDrawerDialog = true;
    },
    closeAddDrawer(val) {
      this.addDrawerDialog = val;
    },
    // 查看已录入题目----
    questionsDetails() {
      this.questionsInfoDialog = true;
    },
    closeQuestionsInfo(val) {
      this.questionsInfoDialog = val;
    },
    // 添加试题--------------------------------------------
    addQuestion(index) {
      this.questionsIndex = index;
      this.testQuestionsList = this.$refs.classHour.classHourInfo.classList[
        this.questionsIndex
      ].coursePeriodQuestionList
      if (this.testQuestionsList.length == 50) {
        return this.$message.error('所选试题个数不能超过50个！')
      }
      this.$refs.testQuestions.getTblleList();
      this.drawerDialog = true;
    },
    closeDrawer(val) {
      this.drawerDialog = val;
    },
    // 添加
    testQuestions(list) {
      list.forEach(k => {
        k.free1 = k.id
      })
      this.$refs.classHour.classHourInfo.classList[
        this.questionsIndex
      ].coursePeriodQuestionList = list
    },
    // 删除录入题目
    deletQuestions(id) {
      this.$confirm("删除后将无法恢复，是否确定删除？", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let data = {
          userId: this.routeInfo.userId,
          id,
        };
        this.$http.deleteOneQuestions(data).then((res) => {
          if (res.code == 200) {
            this.enterQuestionsList = this.enterQuestionsList.filter(
              (item) => item.id != id
            );
            this.enterNum = this.enterQuestionsList.length;
          } else {
            this.$message.error(res.message);
          }
        });
      });
    },
    // 取消
    onCancel() {
      this.$confirm(
        "确定要放弃当前编辑的课程内容吗？放弃后编辑内容无法找回。",
        "提示",
        {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(() => {
        this.$http.deleteCourseExamin().then( res => {
          if(res.code == 200) {
            this.$router.go(-1);
          }
        })
      });
    },
    // 下载试题类型模板
    downloadTemplate(id) {
      axios({
        method: "get",
        url: __PATH.BASE_URL_LABORATORY + "question/template",
        params: { type: id },
        responseType: "blob",
        headers: {
          "Content-Type": "application/json",
          token: this.routeInfo.token || "",
        },
      })
        .then((res) => {
          console.log(res, "res");
          let name = res.headers["content-disposition"]
            .split(";")[1]
            .split("filename=")[1];
          let blob = new Blob([res.data]); // { type: "application/vnd.ms-excel" }
          let url = window.URL.createObjectURL(blob); // 创建一个临时的url指向blob对象
          // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
          let a = document.createElement("a");
          a.href = url;
          a.download = decodeURI(name);
          a.click();
          // 释放这个临时的对象url
          window.URL.revokeObjectURL(url);
        })
        .catch((res) => {
          this.$message.error("导出失败！");
        });
    },
    async fileChange(file, fileList, val) {
      if (val == "0") {
        this.fileCourse = fileList;
      } else {
        this.fileQuestions = fileList;
      }
    },
    httpRequest(val) {
      let formData = new FormData();
      if (val == "0") {
        formData.append("file", this.fileCourse[0].raw);
      } else {
        formData.append("file", this.fileQuestions[0].raw);
      }
      axios({
        method: "post",
        url: __PATH.BASE_URL_LABORATORY + "minio/upload",
        data: formData,
        headers: {
          "Content-Type": "application/json",
          token: this.routeInfo.token,
        },
      })
        .then((res) => {
          if (val == "0") {
            this.formInfo.coverUrl = res.data.data.viewAddress;
            this.fileCourse[0].url = res.data.data.viewAddress;
          }
        })
        .catch((res) => {
          this.$message.error("上传失败");
        });
    },
    handleExceed(val) {
      if (val == "0") {
        this.$message.error("最多上传一张图片");
      } else {
        this.$message.error("最多上传一个附件");
      }
    },
    beforeAvatarUpload(file, val) {
      if (val == "0") {
        const isLt5M = file.size / 1024 / 1024 < 5;
        if (!isLt5M) {
          this.$message.error("上传图片大小不能超过 5MB!");
          return false;
        }
        if (file.name.indexOf(",") != -1) {
          this.$message.error("非法的文件名");
          return false;
        }
      }
    },
    handleRemove(file, fileList, val) {
      if (val == "0") {
        this.fileCourse = fileList;
      } else {
        this.fileQuestions = fileList;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
}

.topFilter {
  padding: 15px;
  height: 60px;
  background-color: #fff;

  .backBar {
    color: #121f3e;
    height: 30px;
    border-bottom: 1px solid #dcdfe6;
  }
}

.courseContent {
  height: calc(100% - 70px);
  padding: 0 16px;

  .courseTab {
    height: 40px;
    background-color: #f6f5fa;
    font-size: 14px;
    text-align: center;
    line-height: 40px;
    display: flex;
    margin-bottom: 20px;

    .left {
      flex: 1;
      height: 40px;
      box-sizing: border-box;
    }

    .activeLeft {
      color: #fff;
      background: #3562db;
    }

    .centerLeft {
      width: 0;
      height: 0;
      border-left: 20px solid #3562db;
      border-top: 20px solid transparent;
      border-bottom: 20px solid transparent;
    }

    .centerRight {
      width: 0;
      height: 0;
      border-left: 20px solid transparent;
      border-right: 0 solid transparent;
      border-bottom: 20px solid #3562db;
      border-top: 20px solid #3562db;
      margin-left: -16px;
    }

    .right {
      flex: 1;
      height: 40px;
      background: red($color: #000000);
    }

    .activeRight {
      color: #fff;
      background: #3562db;
    }
  }

  .courseInfo {
    height: calc(100% - 60px);
  }

  .formInfo {
    height: calc(100% - 100px);
    overflow: auto;
    margin: 0 0 20px 50px;

    .certified {
      height: 40px;
      display: flex;
      align-items: center;

      .cretifiedOk {
        height: 40px;
        display: flex;
        align-items: center;

        span {
          margin: 0 10px;
        }
      }
    }

    .enterNum {
      width: 50%;
      height: 40px;
      line-height: 40px;
      display: flex;
      justify-content: space-between;

      // padding: 0 10px;
      span:nth-child(2) {
        color: #3562db;
        cursor: pointer;
      }
    }
  }

  .courseList {
    height: calc(100% - 50px);
    overflow: auto;
    background-color: #f5f5fa;
    margin: 0 -16px;
    padding-top: 10px;
  }
}

.btns {
  margin-left: 170px;
}

.changeStatusDialog {
  .exType {
    color: #3562db;
    margin-right: 16px;
    cursor: pointer;
  }
}

.courseImg {
  margin-top: -30px;
}

::v-deep .mterial_file>.el-upload-list {
  position: absolute;
  top: 0;
  width: 500px;
  margin-left: 430px;
  max-height: 160px;
  // overflow: auto;
}

::v-deep .file>.el-upload-list {
  position: absolute;
  top: 0;
  width: 230px;
  margin-left: 400px;
  max-height: 160px;
  // overflow: auto;
}

::v-deep .mterial_file>.el-upload--picture-card {
  border: none;
  width: 300px;
}

::v-deep .mterial_file .el-upload .el-upload-dragger {
  width: 300px;
  height: 148px;
}

::v-deep .mterial_file .el-upload__text {
  position: absolute;
  left: 57px;
  color: #606266;
  font-size: 14px;
  text-align: center;
}

//鼠标悬浮提示边框
.custom-tooltip .el-tooltip__popper .tooltip-inner {
  border: 1px solid transparent;
  /* 自定义边框颜色 */
  background-color: #fff;
  border-radius: 4px;
  /* 自定义边框圆角 */
}
</style>
