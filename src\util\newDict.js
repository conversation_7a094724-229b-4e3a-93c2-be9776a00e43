import on_mdss from '@/assets/images/monitor/on_mdss.png'
import off_mdss from '@/assets/images/monitor/off_mdss.png'
import on_gbzd from '@/assets/images/monitor/on_gbzd.png'
import off_gbzd from '@/assets/images/monitor/off_gbzd.png'
import on_dmtxsp from '@/assets/images/monitor/on_dmtxsp.png'
import off_dmtxsp from '@/assets/images/monitor/off_dmtxsp.png'
import on_flrb from '@/assets/images/monitor/on_flrb.png'
import off_flrb from '@/assets/images/monitor/off_flrb.png'
export const newMonitorTypeList = [
    {
        systemName: '空调末端',
        systemCode: 'ACAT',
        overviewRoutePath: '/airConditioningTerminal/airRunningCondition',
    },
    {
        systemName: 'UPS系统',
        systemCode: 'UPS',
        overviewRoutePath: '/upsRunMenu/upsRunningCondition',
    },
    {
        systemName: '电梯系统',
        systemCode: 'DTXT',
        overviewRoutePath: '/elevatorSystem/eleRunningCondition',
    },
    {
        systemName: '安防系统',
        systemCode: 'AFXT',
        overviewRoutePath: '/securitySystem/securityRunningCondition',
    },
    {
        systemName: '配电系统',
        systemCode: 'PDXT',
        overviewRoutePath: '/distributionSystem/distributionRunningCondition',
    },
]
export const equipmentStatus = [ //设备状态
    {
        value: '0',
        name: '离线'
    },
    {
        value: '1',
        name: '在线'
    },
    {
        value: '2',
        name: '报警'
    }
]
export const materially = [ //物联信息
    {
        value: '0',
        name: '无'
    },
    {
        value: '1',
        name: '有'
    },
]
export const useOfState = [ //使用状态
    {
        value: '0',
        name: '正常'
    },
    {
        value: '1',
        name: '停用'
    },
]
export const newMonitorItemImg = {
    '1': { // 空调末端
        on: on_flrb,
        off: off_flrb
    },
    '2': { // UPS系统
        on: on_dmtxsp,
        off: off_dmtxsp
    },
    'DTXT': { // 电梯系统
        on: on_gbzd,
        off: off_gbzd
    },
    'AFXT': { // 安防系统
        on: on_mdss,
        off: off_mdss
    },

}
export const monitoringFacility = [ //是否主监测设备
    {
        value: 0,
        name: '否'
    },
    {
        value: 1,
        name: '是'
    },
]
export const periodType = [ // 周期类型
    {
        value: 'fixed',
        name: '固定周期'
    },
    {
        value: 'custom',
        name: '自定义'
    },
]
export const beforeOrAfter = [ // 相对时间点的前或后
    {
        value: 'before',
        label: '前'
    },
    {
        value: 'after',
        label: '后'
    },
]
export const executionType = [ // 相对时间点的前或后
    {
        value: 'SIM',
        label: '同时'
    },
    {
        value: 'ASC',
        label: '顺序'
    },
    {
        value: 'DESC',
        label: '倒序'
    },
]
export const relativeTime = [ // 相对时间点
    {
        value: 'sunrise',
        label: '日出'
    },
    {
        value: 'sunset',
        label: '日落'
    },
]
export const timeType = [ // 时间类型
    {
        value: 'absolute',
        label: '绝对'
    },
    {
        value: 'relative',
        label: '相对'
    },
]
export const startAndStop = [ // 启停用状态
    {
        value: '0',
        name: '禁用'
    },
    {
        value: '1',
        name: '启用'
    },
]
