<!--
 * @Author: hedd
 * @Date: 2023-03-08 17:09:35
 * @LastEditTime: 2024-04-07 15:30:14
 * @FilePath: \ihcrs_pc\src\views\drag\components\dialog\subscribeDialog.vue
 * @Description:
-->
<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    title="消息订阅"
    width="50%"
    append-to-body
    :visible.sync="visible"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <div class="dialog-content">
      <div class="sub-btns">
        <el-button type="primary" icon="el-icon-plus" @click="setSubAllChange(true)">全部订阅</el-button>
        <el-button type="primary" plain @click="setSubAllChange(false)">全部取消订阅</el-button>
      </div>
      <ContentCard title="消息订阅" :cstyle="{}">
        <div slot="content">
          <TablePage
            ref="tableMsg"
            v-loading="loadingMsg"
            border
            :showPage="false"
            :tableColumn="tableColumn"
            :data="tableMagData"
          />
        </div>
      </ContentCard>
      <ContentCard title="设备报警" :cstyle="{}">
        <div slot="content">
          <TablePage
            ref="tableWarn"
            v-loading="loadingWarn"
            border
            :showPage="false"
            :tableColumn="tableColumn"
            :data="tableWarnData"
          />
        </div>
      </ContentCard>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog()">取消</el-button>
      <el-button type="primary" @click="submitForm()">保存</el-button>
    </span>
  </el-dialog>
</template>
<script lang="jsx">
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loadingMsg: false,
      loadingWarn: false,
      tableMagData: [],
      tableWarnData: [],
      tableColumn: [
        {
          label: '类型',
          prop: 'label'
        },
        {
          label: '订阅与我相关',
          prop: 'sub',
          render: (h, scope) => {
            return (
              scope.row.sub == 0
                ? (
                  <span onClick={() => this.subStateChange(scope.row, 'sub')} style="color: #3562DB;cursor: pointer;">订阅</span>
                )
                : (
                  <span onClick={() => this.subStateChange(scope.row, 'sub')} style="color: #FA403C;cursor: pointer;">取消订阅</span>
                )
            )
          }
        }
        // {
        //   label: '订阅全部',
        //   prop: 'subAll',
        //   render: (h, scope) => {
        //     return (
        //       scope.row.subAll == 0
        //         ? (
        //           <span onClick={() => this.subStateChange(scope.row, 'subAll')} style="color: #3562DB;cursor: pointer;">订阅</span>
        //         )
        //         : (
        //           <span onClick={() => this.subStateChange(scope.row, 'subAll')} style="color: #FA403C;cursor: pointer;">取消订阅</span>
        //         )
        //     )
        //   }
        // }
      ]
    }
  },
  mounted() {
    this.getNewsLabelList()
  },
  methods: {
    closeDialog() {
      this.$emit('update:visible', false)
    },
    // 消息订阅提交
    submitForm() {
      const userId = this.$store.state.user.userInfo.user.staffId
      const params = [...this.tableMagData, ...this.tableWarnData]?.filter(e => e.sub == 1).map(e => {
        return {
          labelId: e.id,
          subscribeId: e.subscribeId,
          staffId: userId
          // subscribeType: e.subAll == 1 ? 1 : (e.sub == 1 ? 0 : 2)
        }
      }) ?? []
      this.$api.saveOrUpdateNewsSubscribe({jsonList: JSON.stringify(params), staffId: userId}).then(res => {
        if (res.code == 200) {
          this.$message.success('保存成功')
          this.$emit('update:visible', false)
        } else {
          this.$message.error(res.msg)
        }
      })
      console.log(this.tableMagData, this.tableWarnData)
      // this.$emit('update:visible', false)
    },
    // 获取消息订阅列表
    getNewsLabelList() {
      this.loadingMsg = true
      this.loadingWarn = true
      const userId = this.$store.state.user.userInfo.user.staffId
      this.$api.getNewsLabelList({staffId: userId}).then(res => {
        if (res.code == 200) {
          // subscribeType 0 与我相关, 1 全部订阅, 2 未订阅
          const list = res.data.length && res.data.map(e => {
            return {
              ...e,
              isChange: false, // 增加是否操作过字段
              sub: (e.subscribeType == 0 || e.subscribeType == 1) ? '1' : '0'
              // subAll: e.subscribeType == 1 ? '1' : '0'
            }
          })
          // promptType  0 消息table 1 报警table
          this.tableMagData = list.filter(e => e.promptType == 0)
          this.tableWarnData = list.filter(e => e.promptType == 1)
        }
      })
      setTimeout(() => {
        this.loadingMsg = false
        this.loadingWarn = false
      }, 1000)
    },
    // 订阅状态改变 0 未订阅 1 已订阅
    subStateChange(row, type) {
      // 操作过得数据用于提交记录
      row.isChange = true
      // 订阅全部默认订阅与我相关；取消订阅与我相关默认取消订阅全部
      if (type == 'sub') {
        if (row.sub == 1) {
          row.sub = 0
          // row.subAll = 0
        } else {
          row.sub = 1
        }
      } else {
        // if (row.subAll == 1) {
        //   row.subAll = 0
        // } else {
        //   row.sub = 1
        //   row.subAll = 1
        // }
      }
    },
    // 全部订阅与我相关
    setSubAllChange(type) {
      this.tableMagData.forEach(item => {
        item.isChange = true
        item.sub = type ? 1 : 0
        // item.subAll = type ? 1 : 0
      })
      this.tableWarnData.forEach(item => {
        item.isChange = true
        item.sub = type ? 1 : 0
        // item.subAll = type ? 1 : 0
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  background: #fff;
  padding: 16px;
  width: 100%;
  overflow-y: auto;

  .sub-btns {
    text-align: right;
  }
}
</style>
