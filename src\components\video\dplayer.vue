// 直接在组件中引入 // dplayer.vue

<template>
  <div :id="flag" :src="src" :type="type" :viewState="viewState"></div>
</template>
<script>
import DPlayer from "dplayer";

export default {
  props: {
    flag: {
      type: String,
      default: "",
    },
    src: {
      type: String,
      default: "",
    },
    type: {
      type: String,
      default: "",
    },
    viewState:{
      type: Boolean,
      default: false,
    },
    frequency:{
      type: String,
      default: '',
    }
  },
  data() {
    return {
      dp: null,
      lastTime:null,
      playedTime: '',
      frequencySum:0,
      frequencyNum:0,
      timer:null
    };
  },
  created(){
    this.playedTime = this.lastTime
    this.frequencySum= this.frequency*60
    this.frequencyNum = this.frequencySum
  },
  mounted() {
    setTimeout(()=>{
      this.init();
    },500)
  },
  destroyed() {
    clearInterval(this.timer); // 清除计时器以避免内存泄漏
  },
  methods: {
    // 认证时长倒计时
    frequencyTime(){
        this.timer = setInterval(() => {
          this.frequencyNum--;
          console.log(this.frequencyNum,'学习了');
          if (this.frequencyNum === 0) {
            this.frequencyNum = this.frequencySum
            clearInterval(this.timer);
            this.dp.pause()
            this.$confirm("为充分保证学习效果，请账号所有人认真学习，不要学习期间做其他工作哦！", "提示", {
              confirmButtonText: "确认",
              cancelButtonText: "取消",
              type: "warning",
            }).then(() => {
               this.dp.play()
            });
          }
        }, 1000);
      },
    async init(id) {
      let _this = this;
      let currentTime = 0;
      let maxTime = 0;
      //   let res = await api();  // 调用获取视频的地址
      //   let src = res.data.path;
      let src = this.src;
      console.log(src,'视频地址');
      this.dp = new DPlayer({
        container: document.getElementById(this.flag), // 播放器容器元素
        hotkey: true, // 开启热键，支持快进、快退、音量控制、播放暂停，默认是true
        lang: "zh-cn", // 可选值：'en'、'zh-cn'、'zh-tw'
        screenshot: false, // 开启截图，如果开启，视频和视频封面需要允许跨域
        autoplay: false, //是否自动播放
        playbackSpeed: [1], // 可选的视频播放倍速，可以设置自定义的数组
        video: {
          // 视频信息，更多可以参考官网：https://dplayer.diygod.dev/zh/guide.html
          url: src, // 视频地址
          type: 'auto'
        },
      });
      // 监听视频开始播放
      this.dp.on("play", () => {
        _this.frequencyTime()
        let playTime = 0;
        if (
          Number(Math.floor(this.playedTime)) ===
          Number(Math.floor(this.dp.video.duration))
        ) {
          this.playedTime = 0;
          playTime = 0;
        } else if (
          Number(Math.floor(this.dp.video.currentTime)) !==
          Number(Math.floor(this.playedTime))
        ) {
          playTime = this.playedTime;
          this.dp.seek(playTime);
        }
      });
      // 监听视频暂停
      this.dp.on("pause", () => {
        clearInterval(this.timer);
        this.playedTime = this.dp.video.currentTime;
      });
      // 设置不可以快进（timeupdate：当前播放位置更改时触发）
      this.dp.on("timeupdate", () => {
        if (_this.type != "see"&&!_this.viewState) {
          let timeDisplay = this.dp.video.currentTime;
          // 如果当前时间是0并且当前播放时间等于设置时间  放行
          if (currentTime == 0 && timeDisplay == this.lastTime) {
          } else if (timeDisplay < maxTime) {
          } else if (timeDisplay - currentTime > 1) {
            this.dp.video.currentTime =
              currentTime > maxTime ? currentTime : maxTime;
            this.dp.notice("禁止快进", 2000);
          }
          // if(((currentTime==0&&timeDisplay==this.lastTime)||(timeDisplay < maxTime))||(timeDisplay - currentTime > 1)){
          //   this.dp.video.currentTime =
          //     currentTime > maxTime ? currentTime : maxTime;
          //   this.dp.notice("禁止快进", 2000)
          // }
          currentTime = this.dp.video.currentTime;
          maxTime = currentTime > maxTime ? currentTime : maxTime;
        }
      });
      // 快进的提示，在禁止快进的提醒存在时不显示
      this.dp.on("notice_show", function (e) {
        var text = e.innerHTML;
        if (
          "禁止快进" != text &&
          (text.indexOf("快进") > -1 || text.indexOf("快退") > -1)
        ) {
          e.style.display = "none";
        }
      });
      this.dp.on("ended", function () {
        _this.$emit("playEnd", true);
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.dplayer {
  height: 100%;
}
</style>
