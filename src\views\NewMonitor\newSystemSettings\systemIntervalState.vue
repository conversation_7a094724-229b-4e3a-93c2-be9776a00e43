<template>
    <PageContainer>
        <div slot="header">
            <div class="searchForm">
                <div class="search-box">
                    <el-select v-model="searchForm.systemTypeCode" placeholder="请选择系统类型" @change="systemTypeChange">
                        <el-option v-for="item in systemType" :key="item.dictionaryDetailsCode"
                            :label="item.dictionaryDetailsName" :value="item.dictionaryDetailsCode"></el-option>
                    </el-select>
                    <el-select v-model="searchForm.deviceTypeCode" placeholder="请选择设备类型" class="ml-16">
                        <el-option v-for="item in deviceType" :key="item.dictionaryDetailsCode"
                            :label="item.dictionaryDetailsName" :value="item.dictionaryDetailsCode"></el-option>
                    </el-select>
                    <div class="ml-16">
                        <el-button type="primary" @click="search">查询</el-button>
                        <el-button type="primary" plain @click="reset">重置</el-button>
                    </div>
                </div>
            </div>
        </div>
        <div slot="content" ref="contentRef" class="table-box">
            <div class="tableContainer">
                <el-button type="primary" style="margin-bottom: 12px" @click="handleListEvent('add')">新建</el-button>
                <el-table :data="tableData" style="width: 100%;margin-bottom: 20px;" border height="250">
                    <el-table-column prop="systemTypeName" label="系统类型">
                    </el-table-column>
                    <el-table-column prop="deviceTypeName" label="设备类型">
                    </el-table-column>
                    <el-table-column prop="enable" label="状态">
                        <template slot-scope="scope">
                            <span>
                                {{ scope.row.enable === 1 ? '已启用' : "已禁用" }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="updateTime" label="更新时间">
                    </el-table-column>
                    <el-table-column label="操作" width="200">
                        <template slot-scope="scope">
                            <el-button type="text" @click="handleListEvent('edit', scope.row)">编辑</el-button>
                            <el-button type="text" v-if="scope.row.enable == 1"
                                @click="handleListEvent('forbidden', scope.row, 0)">禁用</el-button>
                            <el-button type="text" v-if="scope.row.enable == 0"
                                @click="handleListEvent('forbidden', scope.row, 1)">启用</el-button>
                            <el-button type="text" @click="handleListEvent('del', scope.row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination :current-page="pagination.page" :page-sizes="[15, 30, 50, 100]"
                    :page-size="pagination.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="pageTotal"
                    @size-change="handleSizeChange" @current-change="handleCurrentChange">
                </el-pagination>
            </div>
        </div>
    </PageContainer>
</template>
<script>
export default {
    name: 'emergencyTeam',
    data() {
        return {
            systemType: [],
            deviceType: [],
            searchForm: {
                systemTypeCode: '',//系统类型编码
                deviceTypeCode: '',//设备类型编码
            },
            pagination: {
                pageSize: 15,
                page: 1
            },
            tableData: [],
            pageTotal: 0,
        }
    },
    mounted() {
        this.getQueryCategoryByCategoryId()
        this.getTableData()
    },
    methods: {
        //获取产品类型
        getQueryCategoryByCategoryId() {
            let data = {
                dictionaryCategoryId: 'PRODUCT_CATEGORY',
                level: 1
            }
            this.$api.getQueryCategoryByCategoryId(data).then(res => {
                if (res.code === '200') {
                    this.systemType = res.data
                }
            })
        },
        //产品类型选择
        systemTypeChange(val) {
            this.searchForm.deviceTypeCode = ''
            this.$api.getIntervalStateDeviceType({ dictionaryCode: val }).then(res => {
                if (res.code === '200') {
                    this.deviceType = res.data[0].children
                }
            })
        },
        // 品类table列表
        getTableData() {
            this.tableLoading = true
            let data = {
                ...this.pagination,
                ...this.searchForm
            }
            this.$api
                .getIntervalStatePage(data)
                .then((res) => {
                    this.tableLoading = false
                    if (res.code == 200) {
                        this.tableData = res.data.records
                        this.pageTotal = res.data.total
                    } else if (res.message) {
                        this.tableData = []
                        this.pagination.total = 0
                        this.$message.error(res.message)
                    }
                })
                .catch((err) => {
                    this.tableLoading = false
                })
        },
        handleSizeChange(val) {
            this.pagination.pageSize = val
            this.pagination.page = 1
            this.getTableData()
        },
        search() {
            this.pagination.page = 1
            this.getTableData()
        },
        reset() {
            this.searchForm = {
                systemTypeCode: '',//系统类型编码
                deviceTypeCode: '',//设备类型编码
            }
            this.pagination.page = 1
            this.pageTotal = 0
            this.getTableData()
        },
        handleCurrentChange(val) {
            this.pagination.page = val
            this.getTableData()
        },
        handleListEvent(type, row, num) {
            if (type == 'add') {
                this.$router.push({
                    path: 'systemIntervalState/addIntervalForm',
                    query: {
                        type: type,
                    }
                })
            }
            if (type == 'edit') {
                this.$router.push({
                    path: 'systemIntervalState/addIntervalForm',
                    query: {
                        id: row.id
                    }
                })
                this.dialogVisible = true
            }
            if (type == 'forbidden') {
                let params = {
                    enable: num,
                    id: row.id
                }
                this.$api.getIntervalStateEnable(params).then((res) => {
                    if (res.code === '200') {
                        this.$message.success(res.message)
                        this.getTableData()
                    } else {
                        this.$message.error(res.message)
                    }
                })
            }
            if (type == 'del') {
                this.$confirm('此操作将永久删除, 是否继续?', '提示', {
                    cancelButtonClass: 'el-button--primary is-plain',
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    let params = {
                        id: row.id
                    }
                    this.$api.getIntervalStateDeleteById(params).then((res) => {
                        if (res.code === '200') {
                            this.$message.success(res.message)
                            this.getTableData()
                        } else {
                            this.$message.error(res.message)
                        }
                    })
                })
            }
        },
    }
}
</script>
<style lang="scss" scoped>
.searchForm {
    display: flex;
    height: 80px;
    padding: 0 16px;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .el-input {
        width: 200px;
        margin-left: 16px;
    }

    >div {
        margin-right: 20px;
    }
}

.search-box {
    display: flex;
    align-items: center;
}

.container-content>div {
    height: 100%;
    margin-top: 16px;
}

.el-table {
    margin-bottom: 12px;

}

::v-deep .el-table__row:hover {
    cursor: pointer;
}

.table-box {
    background-color: #fff;
    height: 100%;
    padding: 16px;

    .tableContainer {
        height: 100%;

        .el-table {
            height: calc(100% - 96px) !important;
        }
    }
}

.diaContent {
    width: 100%;
    max-height: 550px !important;
    overflow: auto;
    background-color: #fff !important;

    .el-input,
    .el-select,
    .el-cascader {
        width: 300px;
    }
}

.form-item {
    display: inline-block;
    margin-right: 20px;
}

.inputWidth {
    width: 820px;
}

.ml-16 {
    margin-left: 16px;
}

.dialog .el-dialog {
    width: 60% !important;
}
</style>
