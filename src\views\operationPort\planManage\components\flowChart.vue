<template>
  <div class="tree-main">
    <vue2-org-tree :data="treeData" :horizontal="false" label-class-name="bg-none" :render-content="renderContent" :collapsable="false" @on-node-click="onNodeClick" />
    <el-drawer v-if="isNotifyDrawer" title="通知" custom-class="content-drawer" :visible.sync="isNotifyDrawer" direction="rtl">
      <div class="drawer__body_main">
        <div class="drawer-content">
          <notifyEvent ref="notifyEvent" type="notify" />
        </div>
      </div>
      <div class="drawer-foot">
        <el-button type="primary" plain @click="isNotifyDrawer = false">取消</el-button>
        <el-button type="primary" @click="notifyEditFinish">保存</el-button>
      </div>
    </el-drawer>
    <el-drawer v-if="isActionDrawer" title="动作" custom-class="content-drawer" :visible.sync="isActionDrawer" direction="rtl">
      <div class="drawer__body_main">
        <div class="drawer-content">
          <div class="notifyEvent-head">
            <p v-show="!action_isEditEventName" class="notify-title" @click="editTitleFun('action_isEditEventName')">
              {{ stepName || '请输入' }}
              <i class="el-icon-edit" style="color: #ccced3"></i>
            </p>
            <el-input
              v-show="action_isEditEventName"
              ref="action_isEditEventName"
              v-model="stepName"
              placeholder="请输入节点名称"
              :maxlength="10"
              style="width: 200px"
              @blur="() => (action_isEditEventName = false)"
            />
          </div>
          <promptEvent ref="action_promptEvent" />
          <notifyEvent ref="action_notifyEvent" type="action" />
          <confirmEvent ref="action_confirmEvent" />
        </div>
      </div>
      <div class="drawer-foot">
        <el-button type="primary" plain @click="isActionDrawer = false">取消</el-button>
        <el-button type="primary" @click="actionEditFinish">保存</el-button>
      </div>
    </el-drawer>
  </div>
</template>
<script lang="jsx">
import { transData } from '@/util'
import notifyEvent from './notifyEvent'
import promptEvent from './promptEvent'
import confirmEvent from './confirmEvent'
import alarmTypeIcon from '@/assets/images/operationPort/alarmTypeIcon.png'
import Vue2OrgTree from 'vue2-org-tree'
export default {
  name: 'flowChart',
  components: {
    Vue2OrgTree,
    promptEvent,
    notifyEvent,
    confirmEvent
  },
  props: {
    type: {
      type: String,
      default: 'edit' // view edit
    },
    isPreview: {
      type: Boolean,
      default: false
    },
    currentStepId: {
      type: String,
      default: ''
    },
    eventData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      stepName: '节点名称', // 步骤名称
      action_isEditEventName: false, // 修改title
      activeStepType: '', // 当前操作步骤类型
      isNotifyDrawer: false, // 通知抽屉
      isActionDrawer: false, // 动作抽屉
      depts: {}, // 部门列表
      treeData: {}
    }
  },
  computed: {},
  mounted() {
    this.$nextTick(() => {
      this.getUseDeptList()
    })
  },
  methods: {
    setTreeData() {
      this.treeData = {
        id: '0',
        name: '系统报警',
        step: 1,
        children: [
          {
            id: '1',
            name: '误报',
            step: 2,
            tip: '点击误报按钮',
            notify: true,
            action: false,
            children: [
              {
                id: '1-1',
                name: '解除报警',
                step: 3,
                tip: '点击解除报警按钮'
              }
            ]
          },
          {
            id: '2',
            name: '演习',
            step: 2,
            tip: '点击演习按钮',
            notify: true,
            action: false,
            stepType: 1,
            children: [
              {
                id: '2-1',
                name: '取消预案',
                step: 3,
                tip: '点击取消启用预案按钮',
                children: [
                  {
                    id: '2-1-1',
                    name: '解除报警',
                    step: 4,
                    tip: '点击解除报警按钮'
                  }
                ]
              },
              {
                id: '2-2',
                name: '启动预案',
                step: 3,
                tip: '点击确认按钮',
                notify: true,
                action: false,
                stepType: 2,
                children: [
                  {
                    id: '2-2-1',
                    name: '节点名称',
                    step: 4,
                    tip: '请添加节点动作',
                    notify: true,
                    action: true,
                    stepType: 3,
                    children: [
                      {
                        id: '2-2-1-1',
                        name: '解除报警',
                        step: 5,
                        tip: '点击解除报警按钮'
                      }
                    ]
                  }
                ]
              }
            ]
          },
          {
            id: '3',
            name: '确警',
            step: 2,
            tip: '点击确警按钮',
            notify: true,
            action: false,
            stepType: 4,
            children: [
              {
                id: '3-1',
                name: '取消预案',
                step: 3,
                tip: '点击取消启用预案按钮',
                children: [
                  {
                    id: '3-1-1',
                    name: '解除报警',
                    step: 4,
                    tip: '点击解除报警按钮'
                  }
                ]
              },
              {
                id: '3-2',
                name: '启动预案',
                step: 3,
                tip: '点击确认按钮',
                notify: true,
                action: false,
                stepType: 5,
                children: [
                  {
                    id: '3-2-1',
                    name: '节点名称',
                    step: 4,
                    tip: '请添加节点动作',
                    notify: true,
                    action: true,
                    stepType: 6,
                    children: [
                      {
                        id: '3-2-1-1',
                        name: '解除报警',
                        step: 5,
                        tip: '点击解除报警按钮'
                      }
                    ]
                  }
                ]
              }
            ]
          }
        ]
      }
    },
    // 动作事件完成
    actionEditFinish() {
      let noticeEventList,
        warnEventList,
        confirmEventList = []
      // 通知
      let notifyData = this.$refs.action_notifyEvent.notifyData
      noticeEventList = this.$refs.action_notifyEvent.noticeEventList.map((item, index) => {
        return {
          stepType: this.activeStepType,
          stepName: this.stepName,
          eventName: notifyData.eventName,
          showFlag: notifyData.showFlag ? 1 : 0,
          eventSort: index + 1,
          checkFlag: item.checkFlag ? 1 : 0,
          noticeContent: item.noticeContent,
          noticePhone: item.noticePhone,
          noticeWay: item.noticeWay,
          noticeType: item.noticeType,
          personName: item.personName,
          departCode: item.departCode.join(','),
          noticePersonList: item.noticePersonList
        }
      })
      // 提示
      if (this.$refs.action_promptEvent.warnEventList.length) {
        let warnData = this.$refs.action_promptEvent.warnData
        warnEventList = this.$refs.action_promptEvent.warnEventList.map((item, index) => {
          return {
            stepType: this.activeStepType,
            stepName: this.stepName,
            eventName: warnData.eventName,
            showFlag: warnData.showFlag ? 1 : 0,
            eventSort: index + 1,
            checkFlag: item.checkFlag ? 1 : 0,
            warnContent: item.warnContent,
            warnRoles: item.warnRoles
          }
        })
      }
      // 确认
      if (this.$refs.action_confirmEvent.confirmEventList.length) {
        let confirmData = this.$refs.action_confirmEvent.confirmData
        confirmEventList = this.$refs.action_confirmEvent.confirmEventList.map((item, index) => {
          return {
            stepType: this.activeStepType,
            stepName: this.stepName,
            eventName: confirmData.eventName,
            showFlag: confirmData.showFlag ? 1 : 0,
            eventSort: index + 1,
            checkFlag: item.checkFlag ? 1 : 0,
            confirmTip: item.confirmTip,
            confirmTitle: item.confirmTitle
          }
        })
      }
      this.$emit('eventFinish', this.activeStepType, { noticeEventList, warnEventList: warnEventList || [], confirmEventList: confirmEventList || [] })
      this.isActionDrawer = false
    },
    // 通知事件完成
    notifyEditFinish() {
      let { stepName, eventName, showFlag } = this.$refs.notifyEvent.notifyData
      let noticeEventList = this.$refs.notifyEvent.noticeEventList.map((item, index) => {
        return {
          stepType: this.activeStepType,
          stepName,
          eventName,
          showFlag: showFlag ? 1 : 0,
          eventSort: index + 1,
          checkFlag: item.checkFlag ? 1 : 0,
          noticeContent: item.noticeContent,
          noticePhone: item.noticePhone,
          noticeWay: item.noticeWay,
          noticeType: item.noticeType,
          personName: item.personName,
          departCode: item.departCode.join(','),
          noticePersonList: item.noticePersonList
        }
      })
      this.$emit('eventFinish', this.activeStepType, { noticeEventList })
      this.isNotifyDrawer = false
    },
    // 流程图节点控制
    nodeControl(type, nodeItem) {
      if (this.type == 'view') return
      if (!type) return
      let currentNodeData = this.eventData[nodeItem.stepType]
      let keys = Object.keys(currentNodeData)
      this.activeStepType = nodeItem.stepType
      this.stepName = '节点名称'
      if (type == 'notify') {
        // 通知
        this.isNotifyDrawer = true
        this.$nextTick(() => {
          if (keys.includes('noticeEventList') && currentNodeData.noticeEventList.length) {
            this.$refs.notifyEvent.notifyData = {
              stepName: currentNodeData.noticeEventList[0].stepName || nodeItem.name,
              eventName: currentNodeData.noticeEventList[0].eventName || nodeItem.name,
              showFlag: currentNodeData.noticeEventList[0].showFlag == 1 || false
            }
            this.$refs.notifyEvent.noticeEventList = (currentNodeData?.noticeEventList ?? []).map((item) => {
              return {
                ...item,
                showFlag: item.showFlag == 1,
                checkFlag: item.checkFlag == 1,
                departCode: item.departCode.split(',')
              }
            })
          }
          this.$refs.notifyEvent.notifyData.stepName = nodeItem.name
        })
      } else if (type == 'action') {
        // 动作
        this.isActionDrawer = true
        this.$nextTick(() => {
          // 通知
          if (keys.includes('noticeEventList') && currentNodeData.noticeEventList.length) {
            this.stepName = currentNodeData.noticeEventList[0].stepName || nodeItem.name
            this.$refs.action_notifyEvent.notifyData = {
              stepName: currentNodeData.noticeEventList[0].stepName || nodeItem.name,
              eventName: currentNodeData.noticeEventList[0].eventName || nodeItem.name,
              showFlag: currentNodeData.noticeEventList[0].showFlag == 1 || false
            }
            this.$refs.action_notifyEvent.noticeEventList = (currentNodeData?.noticeEventList ?? []).map((item) => {
              return {
                ...item,
                showFlag: item.showFlag == 1,
                checkFlag: item.checkFlag == 1,
                departCode: item.departCode.split(',')
              }
            })
          }
          // 提示
          if (keys.includes('warnEventList') && currentNodeData.warnEventList.length) {
            this.stepName = currentNodeData.warnEventList[0].stepName || nodeItem.name
            this.$refs.action_promptEvent.warnData = {
              stepName: currentNodeData.warnEventList[0].stepName || nodeItem.name,
              eventName: currentNodeData.warnEventList[0].eventName || nodeItem.name,
              showFlag: currentNodeData.warnEventList[0].showFlag == 1 || false
            }
            this.$refs.action_promptEvent.warnEventList = (currentNodeData?.warnEventList ?? []).map((item) => {
              return {
                ...item,
                showFlag: item.showFlag == 1,
                checkFlag: item.checkFlag == 1
              }
            })
          }
          // 确认
          if (keys.includes('confirmEventList') && currentNodeData.confirmEventList.length) {
            this.stepName = currentNodeData.confirmEventList[0].stepName || nodeItem.name
            this.$refs.action_confirmEvent.confirmData = {
              stepName: currentNodeData.confirmEventList[0].stepName || nodeItem.name,
              eventName: currentNodeData.confirmEventList[0].eventName || nodeItem.name,
              showFlag: currentNodeData.confirmEventList[0].showFlag == 1 || false
            }
            this.$refs.action_confirmEvent.confirmEventList = currentNodeData.confirmEventList.map((item) => {
              return {
                ...item,
                showFlag: item.showFlag == 1,
                checkFlag: item.checkFlag == 1
              }
            })
          }
        })
      }
    },
    // 流程图节点点击
    onNodeClick(e, data) {
      console.log(e, data)
    },
    // 获取科室
    getUseDeptList() {
      this.$api.getSelectedDept().then((res) => {
        if (res.code == '200') {
          transData(res.data, 'id', 'parentId', 'children').forEach((v) => {
            this.depts[v.id] = v.deptName
          })
          this.setTreeData()
        }
      })
    },
    // 流程图渲染方法
    renderContent(h, data) {
      let currentNodeData,
        startContent,
        isNotice,
        isWarn,
        isConfirm,
        noticeContent,
        warnContent,
        confirmContent = ''
      if (data.name == '系统报警') {
        return (
          <div class={['tree-item', 'alarm-item', this.currentStepId == data.id && this.isPreview ? 'current-step' : '']}>
            <p class="item-title">{data.name}</p>
            <p class="item-step">步骤{data.step}</p>
            <p class="item-tips">
              报警面板样式<span>可设置提示内容</span>
            </p>
            <div class="item-main alarm-main">
              <p class="alarm-tyle">
                <img src={alarmTypeIcon} />
                <span>报警类型</span>
              </p>
              <p class="alarm-info">
                <span class="alarm-label">报警位置：</span>
                <span class="alarm-value">门诊楼西侧花坛</span>
              </p>
              <p class="alarm-info">
                <span class="alarm-label">报警设备：</span>
                <span class="alarm-value">跌倒报警</span>
              </p>
              <p class="alarm-info">
                <span class="alarm-label">报警时间：</span>
                <span class="alarm-value">2024-02-28 12:09:13</span>
              </p>
              <p class="alarm-info">
                <span class="alarm-label">报警级别：</span>
                <span class="alarmLevel">紧急</span>
              </p>
              <div class="alarm-btns">
                <el-button>误报</el-button>
                <el-button type="primary">演习</el-button>
                <el-button type="primary">确警</el-button>
              </div>
            </div>
          </div>
        )
      } else if (data.name == '误报' || data.name == '解除报警' || data.name == '取消预案') {
        return (
          <div class={['tree-item', this.currentStepId == data.id && this.isPreview ? 'current-step' : '']}>
            <p class="item-title">{data.name}</p>
            <p class="item-step">步骤{data.step}</p>
            <div class="item-main end-main">
              <p class="end-label">触发条件</p>
              <p class="end-value">{data.tip}</p>
            </div>
          </div>
        )
      } else if (data.name == '演习' || data.name == '确警' || data.name == '启动预案' || data.name == '节点名称' || data.step == 4) {
        currentNodeData = this.eventData[data.stepType]
        isNotice = Object.keys(currentNodeData).includes('noticeEventList') && currentNodeData['noticeEventList'].length
        isWarn = Object.keys(currentNodeData).includes('warnEventList') && currentNodeData['warnEventList'].length
        isConfirm = Object.keys(currentNodeData).includes('confirmEventList') && currentNodeData['confirmEventList'].length
        if (!Object.keys(currentNodeData).length) {
          startContent = (
            <div class="item-main start-main">
              <p class="start-label">触发条件</p>
              <p class="start-value">{data.tip}</p>
              {this.type != 'view' ? (
                data.notify && !data.action ? (
                  <el-button type="primary" plain icon="el-icon-plus" onClick={() => this.nodeControl('notify', data)}>
                    通知
                  </el-button>
                ) : data.action ? (
                  <el-button type="primary" plain icon="el-icon-plus" onClick={() => this.nodeControl('action', data)}>
                    动作
                  </el-button>
                ) : (
                  ''
                )
              ) : (
                ''
              )}
            </div>
          )
        } else {
          if (isNotice) {
            noticeContent = (
              <div class="start-main-item">
                <p class="start-main-item-title">{currentNodeData.noticeEventList[0].eventName}</p>
                {currentNodeData.noticeEventList.map((item) => {
                  if (item.checkFlag == true || item.checkFlag == 1) {
                    return (
                      <p class="start-main-item-value">
                        <span>{item.noticeType == 0 ? '通知人员：' + item.personName : '通知部门：' + item.departCode.split(',').map((v) => this.depts[v])}</span>
                        <i class="el-icon-arrow-right"></i>
                      </p>
                    )
                  }
                })}
              </div>
            )
          }
          if (isWarn) {
            warnContent = (
              <div class="start-main-item">
                <p class="start-main-item-title">{currentNodeData.warnEventList[0].eventName}</p>
                {currentNodeData.warnEventList.map((item) => {
                  if (item.checkFlag == true || item.checkFlag == 1) {
                    return (
                      <p class="start-main-item-value">
                        <span>{item.warnContent}</span>
                        <i class="el-icon-arrow-right"></i>
                      </p>
                    )
                  }
                })}
              </div>
            )
          }
          if (isConfirm) {
            confirmContent = (
              <div class="start-main-item">
                <p class="start-main-item-title">{currentNodeData.confirmEventList[0].eventName}</p>
                {currentNodeData.confirmEventList.map((item) => {
                  if (item.checkFlag == true || item.checkFlag == 1) {
                    return (
                      <p class="start-main-item-value">
                        <span>{item.confirmTitle}</span>
                        <i class="el-icon-arrow-right"></i>
                      </p>
                    )
                  }
                })}
              </div>
            )
          }
          startContent = (
            <div>
              {isWarn ? (
                <div
                  class="item-main start-main"
                  style="cursor: pointer;"
                  onClick={() => this.nodeControl(data.notify && !data.action ? 'notify' : data.action ? 'action' : '', data)}
                >
                  {warnContent}
                </div>
              ) : (
                ''
              )}
              {isNotice ? (
                <div
                  class="item-main start-main"
                  style="cursor: pointer;"
                  onClick={() => this.nodeControl(data.notify && !data.action ? 'notify' : data.action ? 'action' : '', data)}
                >
                  {noticeContent}
                </div>
              ) : (
                ''
              )}
              {isConfirm ? (
                <div
                  class="item-main start-main"
                  style="cursor: pointer;"
                  onClick={() => this.nodeControl(data.notify && !data.action ? 'notify' : data.action ? 'action' : '', data)}
                >
                  {confirmContent}
                </div>
              ) : (
                ''
              )}
            </div>
          )
        }
        return (
          <div class={['tree-item', 'start-item', !this.isPreview ? 'start-item-hover' : '', this.currentStepId == data.id && this.isPreview ? 'current-step' : '']}>
            <p class="item-title">{isNotice ? currentNodeData.noticeEventList[0].stepName : data.name}</p>
            {(data.stepType == 3 || data.stepType == 6) && !isNotice && !isWarn && !isConfirm ? <i class="el-icon-warning"></i> : ''}
            <p class="item-step">步骤{data.step}</p>
            {startContent}
          </div>
        )
      }
    },
    // 编辑标题
    editTitleFun(key) {
      console.log(key)
      this[key] = true
      this.$nextTick(() => {
        this.$refs[key].focus()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.tree-main {
  width: 100%;
  height: 100%;
  text-align: center;
  overflow: auto;
  p {
    margin: 0px;
  }
  ::v-deep(.content-drawer) {
    width: 50% !important;
    text-align: initial;
    .el-drawer__header {
      height: 56px;
      margin: 0px;
      padding: 0px 26px;
      font-size: 18px;
      color: #333333;
      line-height: 18px;
    }
    .el-drawer__body {
      padding: 0;
      display: flex;
      flex-direction: column;
      .drawer__body_main {
        background: #f6f5fa;
        padding: 24px;
        flex: 1;
        overflow-x: auto;
        display: flex;
        flex-direction: column;
      }
      .drawer-content {
        flex: 1;
        background: #fff;
        padding: 16px 16px 0px 16px;
        border-radius: 4px;
        font-weight: 500;
        font-size: 14px;
        color: #333333;
        word-wrap: break-word;
        word-break: break-all;
        .notifyEvent-head {
          margin-bottom: 16px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .notify-title {
            cursor: pointer;
            font-weight: bold;
            font-size: 16px;
            color: #333333;
            line-height: 32px;
            width: 200px;
          }
        }
      }
      .drawer-foot {
        padding: 12px 16px;
        text-align: right;
        background: #fff;
      }
    }
  }
}
</style>
<style lang="less">
@import './vue2-org-tree.less';
.org-tree {
  p {
    margin: 0px;
  }
  .bg-none {
    padding: 0px !important;
    border-radius: 8px !important;
  }
  .tree-item {
    transition: all 0.3s;
    background: #f6f5fa;
    border: 2px solid #f6f5fa;
    border-radius: 8px;
    padding: 14px;
    position: relative;
    text-align: initial;
    .item-title {
      padding: 3px 0px 3px 11px;
      font-weight: 500;
      font-size: 16px;
      color: #333333;
      line-height: 19px;
      position: relative;
      padding-left: 11px;
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        display: inline-block;
        width: 3px;
        height: 16px;
        background: #bbbbbb;
        border-radius: 999px;
      }
    }
    .item-step {
      padding: 3px 8px;
      font-weight: 400;
      font-size: 12px;
      color: #333333;
      line-height: 14px;
      position: absolute;
      top: 17px;
      right: 2px;
      background: #ffe4ba;
      border-radius: 999px 0px 0px 999px;
    }
    .el-icon-warning {
      position: absolute;
      top: 17px;
      right: 57px;
      font-size: 20px;
      color: #ff7d00;
    }
    .item-tips {
      margin-top: 10px;
      font-size: 14px;
      color: #333333;
      line-height: 16px;
      span {
        font-size: 12px;
        color: #7f848c;
        line-height: 14px;
        margin-left: 10px;
      }
    }
    .item-main {
      min-width: 208px;
      background: #fff;
      border-radius: 4px;
      padding: 16px;
      margin-top: 10px;
    }
  }
  .alarm-item {
    background: #dfe7f2;
    border: 2px solid #dfe7f2;
    .item-title {
      &::before {
        background: #3562db;
      }
    }
    .alarm-main {
      width: 368px;
      margin-top: 8px !important;
      .alarm-tyle {
        line-height: 16px;
        font-size: 14px;
        color: #ff9435;
        display: flex;
        align-items: center;
        margin-bottom: 6px;
        img {
          width: 14px;
          height: 13px;
          margin-right: 8px;
        }
      }
      .alarm-info {
        margin-top: 2px;
        font-size: 12px;
        line-height: 21px;
        .alarm-label {
          color: #666666;
        }
        .alarm-value {
          color: #333333;
        }
        .alarmLevel {
          color: #cb2634;
          padding: 3px 8px;
          background: #ffece8;
          border-radius: 4px;
        }
      }
      .alarm-btns {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        button {
          padding: 6px 11px;
          font-size: 12px;
        }
      }
    }
  }
  .end-main {
    .end-label {
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      line-height: 16px;
    }
    .end-value {
      margin-top: 10px;
      font-weight: 300;
      font-size: 14px;
      color: #666666;
      line-height: 21px;
    }
  }
  .start-item {
    border: 2px solid #dfe7f2;
    background: #dfe7f2;
    .item-title {
      &::before {
        background: #3562db;
      }
    }
    .start-main {
      position: relative;
      .start-label {
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        line-height: 16px;
      }
      .start-value {
        margin-top: 10px;
        font-weight: 300;
        font-size: 14px;
        color: #666666;
        line-height: 21px;
      }
      button {
        position: absolute;
        right: 16px;
        bottom: 16px;
        padding: 6px 11px;
        font-size: 12px;
        background: #fff;
        border: 1px solid #3562db;
        color: #3562db;
      }
      .start-main-item {
        display: flex;
        flex-direction: column;
        .start-main-item-title {
          flex: 1;
          font-weight: 400;
          font-size: 14px;
          color: #ff9435;
          line-height: 16px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-width: 220px;
        }
        .start-main-item-value {
          flex: 1;
          font-weight: 300;
          font-size: 14px;
          color: #666666;
          line-height: 14px;
          padding: 9px 0px 11px 0px;
          border-bottom: 1px solid #e4e7ed;
          display: flex;
          max-width: 220px;
          span {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
        & :last-child {
          border-bottom: none;
        }
      }
    }
  }
  .start-item-hover:hover {
    background: #ffe4ba;
    border: 2px solid #ff9435;
  }
  .current-step {
    background: #ffe4ba;
    border: 2px solid #ff9435;
  }
}
</style>
