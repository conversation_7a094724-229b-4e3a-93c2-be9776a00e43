<!-- 运行监测 -->
<template>
    <ContentCard :title="item.componentTitleShow" :scrollbarHover="true" :cstyle="{ width: '100%', height: '100%' }"
        class="drag_class" :hasMoreOper="['edit']"
        @more-oper-event="(val) => $emit('all-more-Oper', val, item.componentName, item.chartType)">
        <div slot="title-right" class="data-btns">
            <span :class="{ 'active-btn': totalCostDateType == 'day' }" @click="changeTime('day')">今日</span>
            <span :class="{ 'active-btn': totalCostDateType == 'month' }" @click="changeTime('month')">本月</span>
            <span :class="{ 'active-btn': totalCostDateType == 'year' }" @click="changeTime('year')">本年</span>
        </div>
        <div slot="content" class="operation-list">
            <echarts :ref="`airConditionerConsumption${item.componentDataType}`"
                :domId="`airConditionerConsumption${item.componentDataType}`" width="100%" height="100%" />
        </div>
    </ContentCard>
</template>

<script lang="jsx">
import moment from 'moment'
export default {
    name: 'airConditionerConsumption',
    props: {
        item: {
            type: Object,
            default: () => { }
        },
        systemCode: {
            type: String,
            default: ''
        },
    },
    created() {
    },
    mounted() {
        setTimeout(() => {
            this.getDeviceAnalysisData()
        }, 100)
    },
    data() {
        return {
            loading: false,
            totalCostDateType: 'day',
            dateTime: {
                startTime: moment().format('YYYY-MM-DD'),
                endTime: moment().format('YYYY-MM-DD'),
            },
            chartType: '1',
            timeType: 1,
        }
    },
    methods: {
        changeTime(type) {
            this.totalCostDateType = type
            if (type == 'day') {
                this.dateTime.startTime = moment().format('YYYY-MM-DD');
                this.dateTime.endTime = moment().format('YYYY-MM-DD');
                this.timeType = 1
            } else if (type == 'month') {
                this.dateTime.startTime = moment().startOf('month').format('YYYY-MM-DD ')
                this.dateTime.endTime = moment().endOf('month').format('YYYY-MM-DD')
                this.timeType = 3
            } else if (type == 'year') {
                this.dateTime.startTime = moment().startOf('year').format('YYYY-MM-DD ')
                this.dateTime.endTime = moment().endOf('year').format('YYYY-MM-DD')
                this.timeType = 4
            }
            this.getDeviceAnalysisData()
        },
        getDeviceAnalysisData(id) {
            let data = {
                assetsIds: [],
                dictionaryDetailsCode: this.systemCode,
                endTime: this.dateTime.endTime,
                groupId: id || "",
                startTime: this.dateTime.startTime,
                timeType: this.timeType
            }
            this.$api.getAirconditionList(data).then((res) => {
                if (res.code == '200') {
                    this.appendEchartsData(res.data)
                }
            })
        },
        appendEchartsData(data) {
            const baseData = {
                grid: {
                    left: '0%',
                    right: '2%',
                    bottom: '5%',
                    top: '5%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: data.chartX,
                },
                yAxis: {
                    type: 'value'
                },
                tooltip: {
                    trigger: 'item',
                    formatter: '{b} : {c}',
                    confine: true
                },
                series: [
                    {
                        type: 'line',
                        data: data.chartY,
                    }
                ],
            }
            // if (data) {
            //     baseData.xAxis.data.push(data.chartX)
            //     baseData.series[0].data.push(data.chartY)
            // }
            this.$refs[`airConditionerConsumption${this.item.componentDataType}`].init(baseData)
        },
    }
}
</script>

<style lang="scss" scoped>
.data-btns {
    position: absolute;
    right: 7%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &>span {
        width: 44px;
        height: 20px;
        line-height: 20px;
        text-align: center;
        margin: 0 4px;
        background-color: #f6f5fa;
        font-size: 14px;
        font-family: 'PingFang SC-Medium', 'PingFang SC';
        border: none;
        border-radius: 2px;
        color: #7f848c;
        cursor: pointer;
    }

    .active-btn {
        background-color: #e6effc !important;
        color: #3562db !important;
        border-color: #e6effc !important;
    }
}

.operation-list {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
}
</style>