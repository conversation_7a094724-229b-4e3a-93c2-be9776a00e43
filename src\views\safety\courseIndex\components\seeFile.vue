<template>
  <PageContainer :footer="true">
    <div slot="header" class="file_header">
      <div class="topFilter_left">
        <span>{{ itemInfo.periodName }}</span>
      </div>
      <div v-if="type != 'see' && !itemInfo.viewState" class="topFilter_right">
        <img v-if="studyFlag" src="../../../../assets/images/time1.png" alt="" />
        <img v-else src="../../../../assets/images/play.png" alt="" @click="goStudy" />
        学习进度{{ !studyFlag ? '已暂停' : '' }}：<span class="time">{{ countDown }}</span>
      </div>
    </div>
    <div slot="content" class="file_content">
      <div class="content-box">
        <div v-if="doctype == 'pdf'" class="content-box">
          <pdf v-for="item in numPages" :key="item" :page="item" :src="fileUrl"></pdf>
        </div>
        <div v-else-if="doctype == 'jpg' || doctype == 'png' || doctype == 'gif'" class="content-box flex-c">
          <el-image style="width: 50%" :src="fileUrl" :preview-src-list="[fileUrl]"> </el-image>
        </div>
        <div v-else-if="doctype == ''" class="content-box text">
          <div v-html="itemInfo.document"></div>
        </div>
        <iframe v-else id="iframe1" frameborder="no" border="0" marginwidth="0" marginheight="0" scrolling="no" allowtransparency="yes" :src="currentProtocol + fileUrl"></iframe>
      </div>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="onCancel">取消</el-button>
    </div>
  </PageContainer>
</template>
<script>
import { Base64 } from 'js-base64'
// import pdf from "vue-pdf";
export default {
  // components: {
  //   pdf,
  // },
  data() {
    return {
      itemInfo: {},
      frequency: 0, // 认证时间
      frequencyNum: 0,
      numPages: 0,
      doctype: '',
      fileUrl: '',
      currentProtocol: __PATH.VUE_PREVIEW_URL,
      type: '',
      interval: null, // 定时器
      timer: null,
      countDown: '',
      residueTime: '', // 剩余时长
      routeInfo: {},
      studyFlag: true
    }
  },
  watch: {
    residueTime(val) {
      if (val == 0) {
        this.isGoTest()
      }
    }
  },
  created() {
    this.routeInfo = JSON.parse(sessionStorage.getItem('routeInfo'))
    this.itemInfo = JSON.parse(this.$route.query.itemInfo) || {}
    this.itemInfo.duration = this.itemInfo.duration
    this.residueTime = this.itemInfo.userDuration || this.itemInfo.duration
    this.frequency = this.$route.query.frequency
    this.type = this.$route.query.type || ''
    if (this.itemInfo.url) {
      let url = JSON.parse(this.itemInfo.url)[0].url
      this.fileUrl = encodeURIComponent(Base64.encode(url))
      let newArr = this.fileUrl.split('.')
      this.doctype = newArr[newArr.length - 1]
    } else {
      this.doctype = ''
    }
  },
  mounted() {
    if (this.doctype == 'pdf') {
      this.getNumPages()
    }
    if (this.type != 'see' && !this.itemInfo.viewState) {
      this.frequencyTime() // 认证时长倒计时
      this.startCountDown() // 阅读时长倒计时
    }
  },
  destroyed() {
    clearInterval(this.timer) // 清除计时器以避免内存泄漏
    clearInterval(this.interval)
  },
  methods: {
    // 认证时长倒计时
    frequencyTime() {
      this.studyFlag = true
      this.frequencyNum = this.frequency * 60
      this.timer = setInterval(() => {
        this.frequencyNum--
        if (this.frequencyNum === 0) {
          clearInterval(this.timer)
          this.studyFlag = false
          clearInterval(this.interval)
          this.$confirm('为充分保证学习效果，请账号所有人认真学习，不要学习期间做其他工作哦！', '提示', {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.startCountDown()
            this.frequencyTime()
          })
        }
      }, 1000)
    },
    // 阅读时长倒计时
    startCountDown() {
      this.interval = setInterval(() => {
        this.residueTime--
        let hour = Math.floor((this.residueTime / 60 / 60) % 24) // 时
        let minutes = Math.floor((this.residueTime / 60) % 60)
        let seconds = Math.floor(this.residueTime % 60)
        hour = hour < 10 ? '0' + hour : hour
        minutes = minutes < 10 ? '0' + minutes : minutes
        seconds = seconds < 10 ? '0' + seconds : seconds
        this.countDown = hour + ':' + minutes + ':' + seconds
        if (this.residueTime === 0) {
          clearInterval(this.interval)
        }
      }, 1000)
    },
    // 去学习
    goStudy() {
      this.startCountDown()
      this.frequencyTime()
    },
    getNumPages() {
      let loadingTask = pdf.createLoadingTask(this.fileUrl)
      loadingTask.promise
        .then((pdf) => {
          this.numPages = pdf.numPages
        })
        .catch((err) => {
          console.error('pdf 加载失败', err)
        })
    },
    // 试题结束，去考试
    isGoTest() {
      // 如果没有试题，不处理
      if (!this.itemInfo.questionCount) {
        return
      }
      this.$confirm('是否要开始考试?', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.saveStudy('goAnswer')
      })
    },
    // 取消
    onCancel() {
      if (this.type != 'see' && !this.itemInfo.viewState) {
        this.saveStudy('cancel')
      } else {
        this.$router.go(-1)
      }
    },
    // 保留学习记录
    saveStudy(type) {
      let params = {
        courseId: this.itemInfo.courseId, // 课程id
        duration: this.itemInfo.duration, // 视频总长
        periodId: this.itemInfo.id, // 课时id
        userId: this.routeInfo.userId,
        textDuration: this.residueTime, // 文本剩余时长
        videoDuration: '' // 阅读时长
      }
      this.$api.seeClassHour(params).then((res) => {
        if (res.code == '200') {
          setTimeout(() => {
            if (type == 'cancel') {
              this.$router.go(-1)
            } else {
              this.$router.push({
                path: '/answerIng',
                query: {
                  periodId: this.itemInfo.id, // 课时id
                  courseId: this.itemInfo.courseId // 课程id
                }
              })
            }
          }, 500)
        } else {
          this.$message.error(res.msg)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.file_header {
  background: #fff;
  border-radius: 4px;
  height: 80px;
  padding: 0 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .topFilter_left {
    color: #121f3e;
    height: 30px;
    font-size: 20px;
    // border-bottom: 1px solid #dcdfe6;
    span {
      font-weight: 600;
      margin-left: 20px;
    }
  }
  .topFilter_right {
    width: 225px;
    height: 40px;
    background-color: #ededf5;
    border-radius: 40px;
    line-height: 40px;
    text-align: center;
    font-size: 14px;
    color: #3562db;
    img {
      vertical-align: middle;
    }
    .time {
      color: #333;
    }
  }
}
.file_content {
  margin-top: 16px;
  height: 100%;
  padding: 16px;
  overflow: auto;
  background-color: #fff;
  .content-box {
    width: 100%;
    height: 100%;
  }
  #iframe1 {
    width: 100%;
    height: 100%;
  }
}
.flex-c {
  display: flex;
  justify-content: center;
  align-items: center;
}
.text {
  height: 100%;
  overflow: auto;
}
</style>
