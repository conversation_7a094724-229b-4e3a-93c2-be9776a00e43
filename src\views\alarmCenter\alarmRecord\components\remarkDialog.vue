<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    title="备注"
    width="30%"
    :visible.sync="visible"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <div class="content" style="padding: 10px;">
      <el-form ref="ruleForm" :model="form" :rules="rules">
        <el-form-item prop="remark">
          <el-input v-model.trim="form.remark" type="textarea" :rows="4" placeholder="请输入备注" maxlength="100" show-word-limit class="ipt"> </el-input>
        </el-form-item>
      </el-form>
      <!-- v-if="" -->
      <div>
        <div v-for="(item, index) in remaksData" :key="index" class="txt">
          <div style="color: #414653;">{{ item.createdTime }}</div>
          <div style="color: #121f3e;">
            <span>备注人 :</span>
            <span style="padding-left: 10px;">{{ item.operationPersonName + '（' + item.operationPersonId + '）' }}</span>
          </div>
          <div style="color: #121f3e;">
            <span>备注 :</span>
            <span style="padding-left: 10px;">{{ item.remark || '-' }}</span>
          </div>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="confirm('ruleForm')">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  name: 'remarkDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    item: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      form: {
        remark: ''
      },
      rules: {
        remark: [{ required: true, message: '请输入备注', trigger: 'change' }]
      },
      remaksData: []
    }
  },
  created() {
    this.getRemaksList()
  },
  mounted() {},
  methods: {
    getRemaksList() {
      let params = {
        // hospitalCode: 'bjsjtyy',
        // unitCode: 'bjsygj',
        isRemark: '1',
        operationId: this.item.alarmId
      }
      this.$api.selectAlarmOperationOrRemarkById(params).then((res) => {
        if (res.code == 200) {
          this.remaksData = res.data
        }
      })
    },

    // 关闭弹窗
    closeDialog() {
      this.form.remark = ''
      this.$emit('update:visible', !this.visible)
    },
    confirm(formName) {
      let params = {
        // hospitalCode: 'bjsjtyy',
        // unitCode: 'bjsygj',
        alarmId: this.item.alarmId,
        remark: this.form.remark
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$api.insertRemarkById(params).then((res) => {
            if (res.code == 200) {
              this.$message({
                message: '添加备注成功',
                type: 'success'
              })
              this.form.remark = ''
              this.$emit('update:visible', !this.visible)
            } else {
              this.$message.error(res.msg)
              this.form.remark = ''
              this.$emit('update:visible', !this.visible)
            }
          })
        } else {
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  max-height: 300px !important;
  overflow: auto;
  background-color: #fff !important;
}

.model-dialog {
  padding: 0 !important;
}

.ipt {
  margin: 10px auto 0;
  width: 100% !important;
}

::v-deep .el-form-item__error {
  height: 20px;
  width: 300px;
  color: #f56c6c;
  font-size: 12px;
  line-height: 20px;
  padding-top: 4px;
  position: absolute;
  left: 0;
}

::v-deep .el-dialog__body {
  padding: 0 !important;
}

.txt {
  border-bottom: 1px solid #dcdfe6;
  width: 100%;
  padding: 5px 10px;
  background-color: #faf9fc;
  border-radius: 1px;

  div {
    line-height: 25px;
  }
}
</style>
