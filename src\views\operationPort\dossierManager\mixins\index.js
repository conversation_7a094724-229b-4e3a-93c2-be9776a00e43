export default {
  data() {
    return {
      fileVisible: false,
      fileUrl: '',
      title: ''
    }
  },
  methods: {
    httpRequset(file) {
      const params = new FormData()
      params.append('file', file.file)
      this.$api.uploadFile(params).then((res) => {
        if (res.code === '200') {
          this.handleOnSuccess(res, file.file)
          this.$message({
            message: '上传成功',
            type: 'success'
          })
        } else {
          this.$message({
            message: res.message,
            type: 'warning'
          })
        }
      })
    },
    handleView(row) {
      window.open(row.fileUrl)
    },
    handleDownload(row) {
      fetch(row.fileUrl)
        .then((response) => response.blob())
        .then((blob) => {
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = row.fileName
          document.body.appendChild(a)
          a.click()
          a.remove()
          window.URL.revokeObjectURL(url)
        })
    },
    handleDelete(scope) {
      const { index } = scope
      this.formData.archiveFileList.splice(index, 1)
    },
    // 上传前判断
    beforeUpload(file) {
      // 上传大小的限制提示
      const isFileSize = file.size / 1024 / 1024 <= 20
      if (!isFileSize) {
        this.$message.error('上传文件大小不能超过20MB!')
        return false
      }
      //  文件格式限制
      const fileFormat = [
        // 图片格式
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/bmp',
        'image/svg+xml',
        'image/webp',
        'image/heic',
        // 文本文件
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/plain',
        // 音频
        'audio/wav',
        'audio/mpeg',
        'audio/aac',
        'audio/flac',
        // 视频
        'video/mp4',
        'video/x-flv',
        'video/x-msvideo',
        'video/x-ms-wmv',
        'video/quicktime',
        'video/webm',
        'video/x-m4v',
        'video/mpeg',
        'application/vnd.rn-realmedia',
        'application/vnd.rn-realmedia-vbr',
        'video/x-matroska',
        'video/x-m4v',
        'video/avi',
        // 其他
        'video/mp2t', // ts格式
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'image/vnd.dwg',
        'application/acad',
        'application/x-rar-compressed',
        'application/zip',
        'application/x-zip-compressed'
      ]
      // 格式判断
      const isFileFormat = fileFormat.length !== 0 ? fileFormat.includes(file.type) : true
      if (!isFileFormat) {
        this.$message.error('上传格式不支持')
        return false
      }
      return true
    },
    handleOnSuccess(response, file) {
      const { name, picUrl } = response.data
      const { size } = file
      const mSize = size && size / 1024 / 1024
      this.formData.archiveFileList.push({
        fileName: name,
        fileSize: mSize.toFixed(3),
        fileUrl: picUrl
      })
      console.log(this.formData.archiveFileList)
    }
  }
}
