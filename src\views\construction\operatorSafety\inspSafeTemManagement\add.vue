<template>
  <PageContainer v-loading="blockLoading" :footer="true">
    <div slot="content" class="table-content">
      <div class="content_box">
        <el-form ref="formInline" :model="formInline" :inline="true" class="form-inline" label-width="100px" :rules="rules">
          <el-form-item label="模板名称" prop="templateName">
            <el-input
              v-model.trim="formInline.templateName"
              type="textarea"
              :readonly="readonly"
              placeholder="请填写模板名称"
              show-word-limit
              :maxlength="50"
              style="width: 500px"
              class="width_lengthen"
            ></el-input>
          </el-form-item>
          <br />
          <el-form-item label="模板类型" prop="templateType" @change="toggleBookType">
            <el-radio-group v-model.trim="formInline.templateType">
              <el-radio v-for="item in taskBookTypeArr" :key="item.id" :label="item.id">{{ item.dictName }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <br />
          <el-form-item label="模板说明">
            <el-input
              v-model.trim="formInline.templateExplain"
              :readonly="readonly"
              style="width: 500px"
              type="textarea"
              class="project-textarea"
              placeholder="请输入模板说明，最多200字"
              show-word-limit
              :autosize="{ minRows: 4, maxRows: 8 }"
              maxlength="200"
            ></el-input>
          </el-form-item>
        </el-form>
        <template>
          <div style="margin-bottom: 20px">
            <div class="content-table">
              <div class="table-title">
                <span class="title"> <i></i>模板内容</span>
                <span class="line"></span>
              </div>
              <div v-if="formInline.templateType == '1'" class="inspection-content">
                <div v-for="(item, index) in maintainTemplateDetails" :key="index" class="content-block">
                  <div class="content-line"></div>
                  <div class="porject-name">
                    <div class="porject porject-index">
                      <span class="index-icon">
                        <img :src="icon" alt />
                      </span>
                      <span class="index-text">{{ index + 1 }}</span>
                    </div>
                    <div class="porject porject-input">
                      巡检项目：
                      <el-input
                        v-model.trim="item.detailName"
                        maxlength="200"
                        autosize
                        type="textarea"
                        :readonly="readonly"
                        :placeholder="`请填写巡检项目`"
                        show-word-limit
                        class="word-limit-input"
                        auto-complete="off"
                      ></el-input>
                    </div>
                    <div v-if="!readonly" class="porject porject-button">
                      <el-button type="primary" @click="addProject">添加项目</el-button>
                      <el-button v-if="index != 0" type="danger" @click="deteleRow(maintainTemplateDetails, index)">删除</el-button>
                    </div>
                  </div>
                  <div class="content-block">
                    <div v-for="(e, i) in item.maintainTemplateDetailLasts" :key="i" class="termContent">
                      <div class="termContent-input">
                        巡检选项：
                        <el-select v-model="e.isNum" :disabled="readonly" placeholder="请选择">
                          <el-option v-for="item in termTypeOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"></el-option>
                        </el-select>
                      </div>
                      <div class="termContent-input">
                        <span>巡检要点：</span>
                        <el-input
                          v-model.trim="e.content"
                          maxlength="200"
                          autosize
                          type="textarea"
                          :readonly="readonly"
                          :placeholder="`请填写巡检要点说明`"
                          show-word-limit
                          style="width: 400px"
                          class="word-limit-input"
                        ></el-input>
                      </div>
                      <div v-if="!readonly" class="termContent-input">
                        <span class="termContent-button button-add" @click="addrow(item.maintainTemplateDetailLasts)">
                          <i class="el-icon-plus"></i>
                          <span>添加行</span>
                        </span>
                        <span v-if="i != 0" class="termContent-button button-detele" @click="deteleRow(item.maintainTemplateDetailLasts, i)">
                          <i class="el-icon-delete"></i>
                          <span>删除</span>
                        </span>
                      </div>
                      <template>
                        <!-- 数值填空 -->
                        <div v-if="e.isNum == '0'" class="termContent-tools tools-number">
                          <div class="termContent-number">
                            正常范围：
                            <el-input
                              v-model.trim="e.rangeStart"
                              style="width: 180px"
                              maxlength="15"
                              :readonly="readonly"
                              onkeyup="value=value.replace(/[^\w\.\/]/ig,'')"
                            ></el-input>
                            <span style="padding: 0 10px">—</span>
                            <el-input
                              v-model.trim="e.rangeEnd"
                              maxlength="15"
                              onkeyup="value=value.replace(/[^\w\.\/]/ig,'')"
                              :readonly="readonly"
                              style="margin: 0; width: 180px"
                            ></el-input>
                          </div>
                          <div class="termContent-number">
                            单位：
                            <el-select v-model="e.einheitCode" :disabled="readonly" placeholder="请选择" @change="selectUnit(e, e.einheitCode)">
                              <el-option v-for="item in unitOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"></el-option>
                            </el-select>
                          </div>
                        </div>
                        <!-- 巡检选项选项为单选  isNum:3 -->
                        <div v-if="e.isNum == '3'" class="termContent-tools tools-radio">
                          <el-table stripe :data="e.termJson" :cell-style="{ padding: '8px' }" style="width: 718px">
                            <el-table-column prop="contText" label="选项文字" width="450">
                              <template slot-scope="scope">
                                <div class="radio-text">
                                  <el-input
                                    v-model.trim="scope.row.contText"
                                    show-word-limit
                                    autosize
                                    type="textarea"
                                    :maxlength="50"
                                    :readonly="readonly"
                                    style="width: 300px; display: inline-block; margin-right: 15px"
                                  ></el-input>
                                  <span v-if="!readonly" style="margin-right: 15px" :class="['sion-icon', 'el-icon-plus']" @click="addTabelRow(e)"></span>
                                  <span
                                    v-if="!readonly && scope.$index != 0"
                                    style="color: #fc2c61"
                                    :class="['sion-icon', 'el-icon-delete', scope.$index == 0 ? 'icon-disabled' : '']"
                                    @click="radioDeteleRow(e.termJson, scope.$index)"
                                  ></span>
                                </div>
                              </template>
                            </el-table-column>
                            <el-table-column label="默认" align="center">
                              <template slot="header">
                                <span class="iconfont icon-xuanzhong">默认</span>
                                <el-link type="primary" @click="clearDefault(e, item.id)">（清除）</el-link>
                              </template>
                              <template slot-scope="scope">
                                <el-radio v-model="scope.row.isDefault" label="0" :disabled="readonly" @change.native="selectRadio(scope.row, scope.$index, e)">&nbsp;</el-radio>
                              </template>
                            </el-table-column>
                            <el-table-column v-if="!readonly" label="上移 下移" width="100">
                              <template slot-scope="scope">
                                <span :class="['sion-icon', 'el-icon-top', scope.$index == 0 ? 'icon-disabled' : '']" @click="upRow(scope.row, scope.$index, e.termJson)"></span>
                                <span
                                  :class="['sion-icon', 'el-icon-bottom', scope.$index == e.termJson.length - 1 ? 'icon-disabled' : '']"
                                  @click="downRow(scope.row, scope.$index, e.termJson)"
                                ></span>
                              </template>
                            </el-table-column>
                          </el-table>
                        </div>
                      </template>
                    </div>
                  </div>
                </div>
              </div>
              <div v-else class="inspection-content">
                <div class="content-block">
                  <el-button class="sino-button-sure" @click="addNewRow">新增一项</el-button>
                  <el-table ref="materialTable" :data="tableData" border style="width: 90%; overflow: auto; margin-top: 15px" :cell-style="{ padding: '6px' }">
                    <el-table-column label="序号" width="60" align="center">
                      <template slot-scope="scope">
                        <span>{{ scope.$index + 1 }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="巡检内容" align="center">
                      <template slot-scope="scope">
                        <el-input v-model.trim="scope.row.content" show-word-limit autosize type="textarea" placeholder="请输入巡检内容" :maxlength="50" :readonly="readonly">
                        </el-input>
                      </template>
                    </el-table-column>
                    <el-table-column label="标准要求" align="center">
                      <template slot-scope="scope">
                        <el-input
                          v-model.trim="scope.row.standardRequirements"
                          show-word-limit
                          autosize
                          type="textarea"
                          placeholder="请填写标准要求"
                          :maxlength="50"
                          :readonly="readonly"
                        >
                        </el-input>
                      </template>
                    </el-table-column>
                    <el-table-column label="巡检依据" align="center">
                      <template slot-scope="scope">
                        <el-input v-model.trim="scope.row.inspectionBasis" show-word-limit autosize type="textarea" placeholder="巡检依据" :maxlength="50" :readonly="readonly">
                        </el-input>
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="100" align="center">
                      <template v-if="tableData.length > 1" slot-scope="scope">
                        <el-button type="text" size="small" @click="deleteNewRow(scope.$index, scope.row)"> 删除 </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">关闭</el-button>
      <el-button type="primary" @click="submitForm()">保存</el-button>
    </div>
  </PageContainer>
</template>
<script>
import { treeToList } from '@/util'
import icon from '@/assets/images/inspectionCenter/ic-document.png'
export default {
  name: 'maintenanceAddTemplate',
  beforeRouteEnter(to, from, next) {
    if (to.meta.title) {
      const typeList = {
        add: '新增模板',
        edit: '编辑模板',
        copy: '复制模板'
      }
      to.meta.title = typeList[to.query.type] ?? '新增模板'
    }
    next()
  },
  async beforeRouteLeave(to, from, next) {
    if (!['maitemplateManagement', 'templateManagement', 'comInstemplateManagement'].includes(to.name)) {
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      icon,
      activeName: '',
      title: '',
      baseName: '', // 选择设备
      taskBookTypeArr: [
        {
          id: '0',
          dictName: '日常巡检'
        },
        {
          id: '1',
          dictName: '专业巡检'
        }
      ],
      tableData: [
        {
          index: 0,
          content: '',
          standardRequirements: '',
          inspectionBasis: ''
        }
      ],
      readonly: false,
      formInline: {
        templateName: '',
        templateType: '0', // 默认日常巡检
        templateExplain: '',
        id: '' // 模板id，修改必传
      },
      typeList: [],
      maintainTemplateDetails: [
        {
          detailName: '', // 项目名称
          id: 0,
          maintainTemplateDetailLasts: [
            {
              isNum: '1', // 巡检选项
              content: '', // 巡检要点
              id: 0,
              rangeStart: '', // 数值范围
              rangeEnd: '',
              einheitCode: '', // 单位code
              einheitName: '', // 单位名称
              termJson: [{ contText: '', isDefault: '0' }] // 单选选项文字
            }
          ]
        }
      ],
      unitOptions: [],
      termTypeOptions: [],
      query: {},
      id: '',
      blockLoading: false,
      typeArr: [],
      typeTreeData: [],
      propsType: {
        children: 'children',
        label: 'typeName',
        value: 'typeId',
        checkStrictly: true
      },
      rules: {
        templateName: [{ required: true, message: '请输入模板名称', trigger: 'change' }],
        templateType: [{ required: true, message: '请选择模板类型', trigger: 'change' }]
      },
      total: 0,
      parentName: '',
      equipmentTypeName: ''
    }
  },
  mounted() {
    this.initEvent()
  },
  methods: {
    initEvent() {
      // 重置form表单
      this.$nextTick(() => {
        this.$refs.formInline.resetFields()
        this.query = this.$route.query
        this.title = this.query.type == 'add' ? '新增模板管理' : this.query.type == 'copy' ? '复制模板管理' : '修改模板管理'
        if (this.query.type == 'edit' || this.query.type == 'copy') {
          this._getTaskBookDetails() // 查询详情
        }
        // 任务书类型、巡检选项、单位
        this._getDictValue('inspection_options') // 巡检选项
        this._getDictValue('engineering_unit') // 单位
      })
    },
    // 查询详情
    _getTaskBookDetails() {
      let data = {
        id: this.query.id
      }
      this.$api.workMaintainDetail(data).then((res) => {
        const { code, data, message } = res
        if (code == 200) {
          if (data.templateType == 0) {
            this.tableData = data.maintainTemplateDetails.map((item, index) => {
              const children = {}
              children.index = index + 1
              children.content = item.detailName
              children.standardRequirements = item.standardRequirements
              children.inspectionBasis = item.inspectionBasis
              return children
            })
          } else {
            if (data.maintainTemplateDetails.length) {
              data.maintainTemplateDetails.forEach((el) => {
                el.maintainTemplateDetailLasts.forEach((item) => {
                  item.termJson = JSON.parse(item.termJson)
                })
              })
              this.maintainTemplateDetails = data.maintainTemplateDetails
            }
          }
          this.formInline.templateName = this.query.type == 'copy' ? data.templateName + '副本' : data.templateName
          this.formInline.templateType = data.templateType
          this.formInline.templateExplain = data.templateExplain
        }
      })
    },
    // 切换任务数类型
    toggleBookType() {
      if (this.formInline.templateType == 1) {
        this.maintainTemplateDetails.forEach((i, index) => {
          const item = {
            isNum: '2',
            content: '',
            id: '',
            rangeStart: '',
            rangeEnd: '',
            einheitCode: '',
            termJson: [{ contText: '', isDefault: '0' }]
          }
          this.maintainTemplateDetails[index].maintainTemplateDetailLasts = [item]
        })
      }
    },
    // 获取巡检选项/单位
    _getDictValue(types) {
      this.$api.getDictValueList({ dictType: types }).then((res) => {
        if (types == 'inspection_options') {
          this.termTypeOptions = res.data
        } else {
          this.unitOptions = res.data
        }
      })
    },
    // 点击单选按钮
    selectRadio(row, index, val) {
      for (var i = 0; i < val.termJson.length; i++) {
        if (index == i) {
          val.termJson[i].isDefault = '0'
        } else {
          val.termJson[i].isDefault = '1'
        }
      }
    },
    // 选择单位
    selectUnit(i, j) {
      i.einheitName = this.unitOptions.find((item) => {
        return j == item.dictValue
      }).dictLabel
    },
    // 点击确定
    submitForm() {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          this.tableData.forEach((i, index) => {
            const item = {
              isNum: '2',
              content: i.content,
              id: '',
              rangeStart: '',
              rangeEnd: '',
              einheitCode: '',
              termJson: [{ contText: '', isDefault: '0' }]
            }
            this.tableData[index].maintainTemplateDetailLasts = [item]
          })
          this.blockLoading = true
          let data = {
            templateName: this.formInline.templateName,
            templateType: this.formInline.templateType,
            templateExplain: this.formInline.templateExplain,
            maintainTemplateDetails: this.formInline.templateType == '1' ? this.maintainTemplateDetails : this.tableData,
            id: this.query.type == 'edit' ? this.query.id : ''
          }
          let header = {}
          this.$api.workMaintainSave(data, header).then((res) => {
            if (res.code == 200) {
              this.$message.success(res.msg)
              this.$router.go(-1)
            } else {
              this.$message.error(res.message)
            }
            this.blockLoading = false
          })
        }
      })
    },
    // 点击添加行
    addrow(arr) {
      arr.push({
        id: arr.length,
        isNum: '1',
        content: '',
        rangeStart: '',
        rangeEnd: '',
        einheitCode: '',
        termJson: [{ contText: '', isDefault: '0' }]
      })
    },
    // 专业巡检点击添加项目
    addProject() {
      this.maintainTemplateDetails.push({
        detailName: '',
        id: this.maintainTemplateDetails.length,
        maintainTemplateDetailLasts: [
          {
            isNum: '1',
            content: '',
            id: '',
            rangeStart: '',
            rangeEnd: '',
            einheitCode: '',
            termJson: [{ contText: '', isDefault: '0' }]
          }
        ]
      })
    },
    // 日常巡检新增一行
    addNewRow() {
      this.tableData.push({
        index: this.tableData.length,
        content: '',
        standardRequirements: '',
        inspectionBasis: ''
      })
    },
    // 日常巡检删除行
    deleteNewRow(index, row) {
      this.tableData.splice(index, 1)
    },
    // 删除按钮行
    radioDeteleRow(arr, index) {
      // 第一个不可删除
      if (index == 0) return
      // 如果删除的选项为默认选中项 0 把第一个改为默认选中
      if (arr[index].isDefault == '0') {
        arr[0].isDefault = '0'
        arr.splice(index, 1)
      } else {
        arr.splice(index, 1)
      }
    },
    // 删除
    deteleRow(arr, index) {
      if (index == 0) return
      arr.splice(index, 1)
    },
    // 添加单选按钮行
    addTabelRow(option) {
      option.termJson.push({ contText: '', isDefault: '1' })
    },
    // 清除单选默认
    clearDefault(option, parentId) {
      this.maintainTemplateDetails.forEach((e) => {
        if (e.id == parentId) {
          let index = e.maintainTemplateDetailLasts.findIndex((item) => item.id == option.id)
          if (index != -1) {
            let arr = JSON.parse(JSON.stringify(e.maintainTemplateDetailLasts[index].termJson))
            arr.forEach((el) => {
              el.isDefault = '1'
            })
            this.$set(e.maintainTemplateDetailLasts[index], 'termJson', arr)
          }
        }
      })
    },
    upRow(row, index, arr) {
      if (index == 0) return
      arr.splice(index - 1, 1, ...arr.splice(index, 1, arr[index - 1]))
    },
    downRow(row, index, arr) {
      if (index == arr.length - 1) return
      arr.splice(index + 1, 1, ...arr.splice(index, 1, arr[index + 1]))
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-textarea .el-input__count {
  line-height: normal;
}
.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
  padding: 10px;
  overflow-y: auto;
}
::v-deep .el-textarea__inner {
  height: 35px;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  appearance: none !important;
  margin: 0;
}
input[type='number'] {
  appearance: textfield;
}
.content_box {
  height: calc(100% - 30px);
  margin-top: 10px;
  padding: 20px 25px 25px;
  background: #fff;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}
.project-textarea {
  ::v-deep .el-textarea__inner {
    height: 96px !important;
    max-height: 96px !important;
    overflow-y: auto !important;
  }
}
.width_lengthen {
  width: 766px;
  /* padding-right: 45px; */
}
.el-textarea ::v-deep .el-input__count {
  // bottom: -6px !important;
}
.project-textarea textarea {
  height: 120px;
}
.sion-icon {
  cursor: pointer;
  padding-left: 3px;
  color: #5188fc;
}
.icon-disabled {
  cursor: not-allowed;
}
.form-inline {
  .el-input,
  .el-select,
  .el-cascader {
    width: 300px;
  }
}
.inspection-content {
  width: 100%;
  padding-left: 42px;
  font-size: 14px;
  font-weight: 400;
  color: rgb(96 98 102 / 100%);
  .content-block {
    margin: 20px 0;
    .porject-name {
      .porject {
        display: inline-block;
      }
      .porject-index {
        margin-right: 10px;
        .index-icon {
          position: relative;
          right: 8px;
          top: 1px;
        }
      }
      .porject-input {
        width: 795px;
        .el-input {
          display: inline-block;
          width: 90%;
        }
      }
      .porject-button {
        // width: 200px;
        padding-left: 30px;
      }
    }
    .termContent {
      padding-left: 52px;
      margin-top: 20px;
      .termContent-input {
        display: inline-block;
        width: auto;
        margin-right: 23px;
        .el-textarea {
          vertical-align: middle;
        }
        .el-input {
          display: inline-block;
          width: 400px;
        }
        .termContent-button {
          height: 42px;
          line-height: 42px;
          color: #5188fc;
          margin-right: 20px;
          cursor: pointer;
        }
        .button-detele {
          color: rgb(252 44 97 / 100%);
        }
      }
      .termContent-tools {
        padding-left: 75px;
        margin-top: 20px;
        .termContent-number {
          display: inline-block;
          margin-right: 20px;
          .el-input,
          .el-select {
            display: inline-block;
            width: 100px;
            margin-left: 10px;
          }
        }
      }
      .termContent-radio {
        .radio-text {
          .el-input {
            display: inline-block;
            width: 300px;
          }
        }
      }
    }
  }
}
.content-table .table-title {
  .title {
    width: 80px;
    padding: 0;
    font-size: 14px;
    color: #606266;
    i {
      display: inline-block;
      width: 8px;
      height: 16px;
      border-radius: 0 8px 8px 0;
      background: #3562db;
      margin-right: 8px;
      position: relative;
      top: 2px;
    }
  }
  .line {
    display: inline-block;
    width: calc(100% - 85px);
    height: 1px;
    border-top: 1px dashed #dcdfe6;
    position: relative;
    top: -2px;
    left: 2px;
  }
}
.sino-button-sure {
  height: 32px;
  color: #fff;
  background-color: #5188fc;
  border: 1px solid #5188fc;
  font-size: 16px;
  padding: 2px 10px;
}
</style>
