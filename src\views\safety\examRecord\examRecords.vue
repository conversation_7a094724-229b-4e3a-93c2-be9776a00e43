<template>
  <PageContainer v-if="routeInfo.isFalg == 0">
    <div slot="header">
      <div class="search-header">
        <el-tabs v-model="activeName" @tab-click="handleClick" class="tabsMenu">
          <el-tab-pane label="考试任务" name="0"></el-tab-pane>
          <el-tab-pane label="我的考试任务" name="1"></el-tab-pane>
        </el-tabs>
        <div class="search-from">
          <el-input v-model="formInline.name" placeholder="试卷名称" style="width: 200px"></el-input>
          <el-cascader v-model="formInline.subjectId" clearable class="sino_sdcp_input mr15" style="width: 280px"
            :options="subjectList" :props="subjectProps" placeholder="请选择所属科目"></el-cascader>
          <el-date-picker v-model="timeLine" type="daterange" value-format="yyyy-MM-dd" range-separator="至"
            start-placeholder="开始日期" end-placeholder="结束日期">
          </el-date-picker>
          <el-select v-model="formInline.taskStatusPc" placeholder="请选择考试状态">
            <el-option v-for="item in taskStatusPcList" :key="item.id" :label="item.label" :value="item.id"></el-option>
          </el-select>
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="search">查询</el-button>
        </div>
        <div>
          <el-button type="primary" :disabled="multipleSelection.length < 1" @click="exportClickExport">导出</el-button>
        </div>
      </div>
    </div>
    <div slot="content" style="height: 100%; margin-top: 16px; background-color: #fff">
      <div class="table-content">
        <el-table v-loading="tableLoading" :data="tableData" tyle="width: 100%;" height="100%" border stripe
          title="双击查看详情" @row-dblclick="openDetails" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" fixed="left" align="center"></el-table-column>
          <el-table-column label="序号" width="55" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{
          (paginationData.pageNo - 1) * paginationData.pageSize +
          scope.$index +
          1
        }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="试卷名称" align="center" show-overflow-tooltip>
          </el-table-column>
          <el-table-column prop="deptName" label="所属单位" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="subjectName" label="所属科目" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="" label="考试期限" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ moment(scope.row.startTime).format("YYYY-MM-DD") }}至{{
          moment(scope.row.endTime).format("YYYY-MM-DD") }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="activeName == '0'" prop="duration" label="时长" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ scope.row.duration  }} 分钟
            </template>
          </el-table-column>
          <el-table-column v-if="activeName == '0'" prop="alRStudentNum" label="已答卷人数" align="center"
            show-overflow-tooltip>
            <template slot-scope="scope">
              <span>
                {{ scope.row.alRStudentNum }} 人
                <img v-if="scope.row.showPlaint == '1'" src="../../../assets/images/icon-wrapper.png" alt="" />
              </span>
            </template>

          </el-table-column>
          <el-table-column v-if="activeName == '0'" prop="noStudentNum" label="未答卷人数" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ scope.row.noStudentNum  }} 人
            </template>
          </el-table-column>
          <el-table-column v-if="activeName == '1'" prop="score" label="试卷总分" align="center"
            show-overflow-tooltip></el-table-column>
          <el-table-column v-if="activeName == '1'" prop="actualScore" label="考试得分" align="center"
            show-overflow-tooltip></el-table-column>
          <el-table-column v-if="activeName == '1'" prop="pass" label="通过状态" align="center"
          show-overflow-tooltip>
              <template slot-scope="scope">
                <div class="passStatus">
                  <span v-if="scope.row.pass == '1'" class="auditNo">
                    <img src="../../../assets/images/icon-wrapper.png" alt="" />
                    未通过
                  </span>
                  <span v-if="scope.row.pass == '0'" class="relwase"
                    >
                    <img src="../../../assets/images/pass.png" alt="">
                    通过</span
                  >
                  <span v-if="scope.row.pass == '2'" class="relwaseNo"
                    >未参与</span
                  >
                </div>
              </template>
          </el-table-column>
          <el-table-column label="考试状态" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <div class="statusBtn" v-if="activeName == '0'">
                <span v-if="scope.row.taskStatusPc == '0'" class="auditNo">
                  <img src="../../../assets/images/icon-wrapper.png" alt="" />
                  未开始
                </span>
                <span v-if="scope.row.taskStatusPc == '1'" class="auditIng">进行中</span>
                <span v-if="scope.row.taskStatusPc == '2'" class="relwaseNo">已结束</span>
              </div>
              <div class="statusBtn" v-if="activeName == '1'">
                <span v-if="scope.row.taskStatusPc == '0'" class="auditNo">
                  <img src="../../../assets/images/icon-wrapper.png" alt="" />
                  未作答
                </span>
                <span v-if="scope.row.taskStatusPc == '1'" class="relwase">已作答</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="taskTeamName" label="操作" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <div class="operateBths" v-if="activeName == '0'">
                <el-link type="primary" @click="openDetails(scope.row)">查看</el-link>
                <el-link type="danger" @click="deletExam(scope.row)">删除</el-link>
              </div>
              <div class="operateBths" v-if="activeName == '1'">
                <el-link type="primary" v-if="scope.row.taskStatusPc == '1'"
                  @click="openDetails(scope.row)">查看</el-link>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="contentTable-footer">
          <el-pagination style="margin-top: 3px" :current-page="paginationData.pageNo"
            layout="total, sizes, prev, pager, next, jumper" :total="paginationData.total"
            :page-size="paginationData.pageSize" :page-sizes="[15, 30, 50, 100]" @size-change="handleSizeChange"
            @current-change="handleCurrentChange"></el-pagination>
        </div>
      </div>
      <exportDialog :exportType="exportType" :materRows="multipleSelection" :activeName="activeName"
        :dialogVisibleExport="dialogVisibleExport" @closeDialog="closeDialog" ref="dialogExport"></exportDialog>
    </div>
  </PageContainer>
    <div v-else>
  <permissionPrompt></permissionPrompt>
  </div>
</template>

<script>
import axios from "axios";
import moment from 'moment'
// import { servicesLoading } from "@/assets/js/temp.js";
import exportDialog from "@components/recordExport/recordExport";  //导出
import permissionPrompt from "@/views/safety/courseIndex/components/permissionPrompt.vue";
export default {
  components: { exportDialog,permissionPrompt },
  data() {
    return {
      moment,
      formInline: {
        name: "",
        subjectId: "",
        taskStatusPc: '',
        startTime: "",
        endTime: "",
      },
      subjectProps: {
        children: "childList",
        label: "name",
        value: "id",
        checkStrictly: true,
        emitPath: false,
      },
      deptProps: {
        children: "children",
        label: "teamName",
        value: "id",
        checkStrictly: true,
        emitPath: false,
      },
      subjectList: [],//科目分类
      taskStatusPcList: [
        {
          id: '1',
          label: '已结束'
        },
        {
          id: '2',
          label: '已作答'
        },
        {
          id: '3',
          label: '进行中'
        },
        {
          id: '4',
          label: '未作答'
        }
      ],
      timeLine: [],
      hiddenLevelList: [], // 获取筛选类型数据
      tableData: [],
      tableLoading: false,
      paginationData: {
        pageNo: 1,
        pageSize: 15,
        total: 0,
      },
      multipleSelection: [],
      dialogVisibleExport: false,
      exportType: 1,
      activeName: '0',
      routeInfo: {}
    };
  },
  computed: {},
  created() {
    // if (this.$route.query) {
    //   sessionStorage.setItem("routeInfo", JSON.stringify(this.$route.query));
    //   this.routeInfo = JSON.parse(sessionStorage.getItem("routeInfo"));
    // }
      this.routeInfo = JSON.parse(sessionStorage.getItem('routeInfo'))
    if (this.routeInfo.isFalg == 1) {
      return
    }
    this.init();
    this.getDataList(); // 列表
  },
  methods: {
    // 切换tabs
    handleClick(tab, event) {
      this.resetForm()
      this.$refs.dialogExport.getExportField()
    },
    init() {
      // 获取科目分类
      let data = {
        pageNo: 1,
        pageSize: 999,
      };
      this.$api.ipsmFicationlList(data).then((res) => {
        if (res.code == 200) {
          this.subjectList = res.data.records;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 查询列表
    getDataList() {
      this.tableLoading = true
      let params = {
        name: this.formInline.name,
        subjectId: this.formInline.subjectId,
        taskStatusPc: this.formInline.taskStatusPc,
        startTime: this.timeLine[0],
        endTime: this.timeLine[1],
        current: this.paginationData.pageNo,
        size: this.paginationData.pageSize,
        listType: this.activeName
      }
      this.$api.examTaskList(params).then(res => {
        if (res.code == '200') {
          this.tableData = res.data.list
          this.paginationData.total = res.data.total
        } else {
          this.$message.error(res.msg)
        }
        this.tableLoading = false
      })
    },
    // 删除
    deletExam(row) {
      this.$confirm("删除后将无法恢复，是否确定删除？", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let ids = row.id.split(',')
        this.$api.deleteTaskByIds({ ids }).then(res => {
          if (res.code == '200') {
            this.getDataList()
          } else {
            this.$message.error(res.msg)
          }
        })
      });
    },
    // 双击列表查看详情
    openDetails(val) {
      if (this.activeName == '0') {
        this.$router.push({
          path: "recordsDetails",
          query: {
            id: val.id,
          },
        });
      } else {
        if (val.taskStatusPc == '0') {
          return
        }
        this.$router.push({
          path: "answerInfo",
          query: {
            respondent: val.respondent,//考试人员id
            recordId: val.id //任务id
          },
        });
      }

    },
    // 查询
    search() {
      this.paginationData.pageNo = 1;
      this.getDataList();
    },
    // 重置
    resetForm() {
      this.formInline.name = "";
      this.formInline.subjectId = "";
      this.formInline.taskStatusPc = "";
      this.formInline.startTime = "";
      this.formInline.endTime = "";
      this.timeLine = [];
      this.paginationData.pageNo = 1;
      this.getDataList();
    },
    // 分页
    handleSizeChange(val) {
      this.paginationData.pageSize = val;
      this.getDataList();
    },
    handleCurrentChange(val) {
      this.paginationData.pageNo = val;
      this.getDataList();
    },
    //勾选
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    exportClickExport() {
      this.dialogVisibleExport = true;
      this.$refs.dialogExport.getExportField(this.activeName)
    },
    closeDialog() {
      this.dialogVisibleExport = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.search-header {
  padding: 10px 16px;

  .search-from {
    padding-bottom: 12px;

    &>div {
      margin-top: 12px;
      margin-right: 10px;
    }
  }
}

.table-content {
  padding: 16px;
  height: calc(100% - 80px);

  .statusBtn {
    font-size: 14px;
    display: flex;
    justify-content: center;

    .auditIng {
      width: 58px;
      height: 24px;
      background-color: #fff7e8;
      border-radius: 4px;
      color: #d25f00;
    }

    .auditNo {
      width: 78px;
      height: 24px;
      background-color: #ffece8;
      border-radius: 4px;
      color: #cb2634;

      img {
        vertical-align: middle;
      }
    }

    .relwase {
      width: 58px;
      height: 24px;
      background-color: #e8ffea;
      border-radius: 4px;
      color: #009a29;
    }

    .inProgress {
      width: 86px;
      height: 24px;
      background-color: #E6EFFC;
      border-radius: 4px;
      color: #2749BF;
    }

    .relwaseNo {
      width: 58px;
      height: 24px;
      background-color: #F2F4F9;
      border-radius: 4px;
      color: #86909C;
    }
  }

  .operateBths {
    color: #3562DB;

    .el-link {
      margin-right: 8px;
    }
  }
}

.contentTable-footer {
  padding: 10px 0 0 0;
}
.passStatus {
  text-align: center;
  font-size: 14px;
  display: flex;
  justify-content: center;
  .auditIng {
    width: 58px;
    height: 24px;
    background-color: #fff7e8;
    border-radius: 4px;
    color: #d25f00;
  }
  .auditNo {
    width: 78px;
    height: 24px;
    background-color: #ffece8;
    border-radius: 4px;
    color: #cb2634;
    img {
      vertical-align: middle;
    }
  }
  .relwase {
    width: 58px;
    height: 24px;
    background-color: #e8ffea;
    border-radius: 4px;
    color: #009a29;
    img {
      vertical-align: middle;
    }
  }
  .inProgress{
    width: 86px;
    height: 24px;
    background-color: #E6EFFC ;
    border-radius: 4px;
    color: #2749BF;
  }
  .relwaseNo{
    width: 58px;
    height: 24px;
    background-color: #F2F4F9;
    border-radius: 4px;
    color: #86909C;   
  }
}
</style>