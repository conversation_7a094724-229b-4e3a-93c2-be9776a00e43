<template>
  <div>
    <el-dialog
      v-if="dialogVisible"
      v-dialogDrag
      :modal="false"
      :close-on-click-modal="false"
      :title="parentParams.title"
      width="40%"
      :visible.sync="dialogVisible"
      custom-class="model-dialog"
      :before-close="closeDialog"
    >
      <div class="dialog-content">
        <el-form ref="formInline" :model="queryForm" inline class="form-inline" :rules="rules" label-width="120px">
          <el-form-item :label="parentParams.title.slice(-2) + '名称：'">
            <p style="width: 500px; margin: 0">{{ parentParams.name }}</p>
          </el-form-item>
          <el-form-item label="建筑面积：" prop="constructionArea">
            <el-input
              v-model.trim="queryForm.constructionArea"
              placeholder="请输入建筑面积"
              :maxlength="10"
              oninput="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')"
            ></el-input>
          </el-form-item>
          <el-form-item label="公摊面积：" prop="sharedArea">
            <el-input v-model.trim="queryForm.sharedArea" disabled> </el-input>
          </el-form-item>
          <el-form-item label="总面积：" prop="totalArea">
            <el-input :value="(Number(queryForm.constructionArea) + Number(queryForm.sharedArea)).toFixed(2)" disabled> </el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" plain @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="submitFormData">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: 'editSpaceDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    parentParams: {
      type: Object,
      default: () => {
        return {
          title: '',
          name: ''
        }
      }
    }
  },
  data() {
    return {
      dialogVisible: this.visible,
      queryForm: {
        constructionArea: 0,
        sharedArea: 0,
        totalArea: 0
      },
      rules: {
        constructionArea: { required: true, message: '请输入建筑面积', trigger: 'change' }
      }
    }
  },
  computed: {},
  mounted() {
    this.getInfoByBaseId()
  },
  methods: {
    getInfoByBaseId() {
      this.$api.GetInfoByBaseId({ baseId: this.parentParams.baseId }).then((res) => {
        if (res.code == 200) {
          Object.assign(this.queryForm, res.data)
        }
      })
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', false)
      this.$refs['formInline'].resetFields()
    },
    submitFormData() {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          this.$emit('change', this.queryForm)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  background: #fff;
  padding: 10px;
  max-height: 500px;
  overflow-y: auto;
  ::v-deep(.form-inline) {
    margin-top: 24px;
    .el-input {
      width: 220px;
    }
  }
}
</style>
