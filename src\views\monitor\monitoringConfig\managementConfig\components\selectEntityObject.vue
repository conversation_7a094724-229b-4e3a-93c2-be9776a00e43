<template>
  <el-dialog title="选择报警实体对象" width="62%" :visible.sync="entityObjectDialogShow" custom-class="model-dialog" :before-close="closeDialog">
    <div class="translateContent">
      <div class="leftTable">
        <div class="translateTitle">设备列表</div>
        <div class="searchForm">
          <div class="search-box">
            <el-input v-model="leftFilter.surveyName" placeholder="请输入设备名称"></el-input>
            <el-select v-model="leftFilter.entityTypeId" filterable placeholder="实体类型" class="ml-16">
              <el-option v-for="item in entityTypeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </div>
          <div class="ml-16">
            <el-button type="primary" @click="leftSearch">查询</el-button>
            <el-button type="primary" plain @click="leftReset">重置</el-button>
          </div>
        </div>
        <el-table ref="leftTable" v-loading="tableLoading" :data="leftPageList" row-key="imsId" height="300px" stripe border @selection-change="leftSelect">
          <el-table-column type="selection" width="50" align="center" :reserve-selection="true"></el-table-column>
          <el-table-column prop="surveyName" label="实体名称" show-overflow-tooltip></el-table-column>
          <el-table-column prop="imsNo" label="实体编号" show-overflow-tooltip></el-table-column>
          <el-table-column prop="entityTypeName" label="实体类型" show-overflow-tooltip></el-table-column>
        </el-table>
        <el-pagination
          :current-page="pagination.current"
          :page-sizes="pagination.pageSizeOptions"
          :page-size="pagination.size"
          layout="total, sizes, prev, pager, next"
          :total="pagination.total"
          @size-change="paginationSizeChange"
          @current-change="paginationCurrentChange"
        ></el-pagination>
      </div>
      <div class="translateBtn">
        <el-button type="primary" icon="el-icon-right" :disabled="leftSelection.length === 0" @click="addSelected">选中
        </el-button>
        <el-button type="primary" icon="el-icon-back" :disabled="rightSelection.length === 0" @click="delSelected">
          移除
        </el-button>
      </div>
      <div class="rightTable">
        <div class="translateTitle">已选设备</div>
        <div class="searchForm">
          <div class="search-box">
            <el-input v-model="rightFilter.surveyName" placeholder="请输入设备名称"></el-input>
            <el-select v-model="rightFilter.entityTypeId" filterable placeholder="实体类型" class="ml-16">
              <el-option v-for="item in entityTypeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </div>
          <div class="ml-16">
            <el-button type="primary" @click="rightSearch">查询</el-button>
            <el-button type="primary" plain @click="rightReset">重置</el-button>
          </div>
        </div>
        <el-table ref="rightTable" :data="targetList" height="300px" stripe border row-key="imsId" @selection-change="rightSelect">
          <el-table-column type="selection" width="50" align="center" :reserve-selection="true"></el-table-column>
          <el-table-column prop="surveyName" label="实体名称" show-overflow-tooltip></el-table-column>
          <el-table-column prop="imsNo" label="实体编号" show-overflow-tooltip></el-table-column>
          <el-table-column prop="entityTypeName" label="实体类型" show-overflow-tooltip></el-table-column>
        </el-table>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="submitDialog">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { cloneDeep } from 'lodash'
import tableListMixin from '@/mixins/tableListMixin'
export default {
  name: 'selectEntityObject',
  components: {},
  mixins: [tableListMixin],
  props: {
    entityObjectDialogShow: {
      type: Boolean,
      default: false
    },
    projectCode: {
      type: String,
      default: ''
    },
    // 数据主键id的键名
    rowId: {
      type: [String, Number],
      default: 'imsId'
    },
    // 回显数据
    surveyCode: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      tableLoading: false,
      unCheckedList: [], // 左侧所有数据
      targetOriginList: [], // 右侧源所有数据  ----这里需要在每次选择之后，对已选源数据进行一次深拷贝，因为右侧还有过滤功能
      // 右侧展示数据
      targetList: [],
      leftFilter: {
        // 左侧过滤
        surveyName: '',
        entityTypeId: ''
      },
      rightFilter: {
        // 右侧过滤
        surveyName: '',
        entityTypeId: ''
      },
      entityTypeList: [],
      // 全部的实体数据
      entitySourceData: [],
      // 左侧过滤总数据
      leftFilterList: [],
      // 左侧表格展示数据
      leftPageList: [],
      // 左侧选中数据
      leftSelection: [],
      // 右侧选中数据
      rightSelection: [],
      page: 1,
      pageSize: 15,
      total: 0,
      // 选中的设备code
      selectedCodes: []
    }
  },
  computed: {
    // 右侧数据源 是来自总数据源过滤得到的
    rightFilterList() {
      return this.entitySourceData.filter((it) => this.selectedCodes.includes(it.surveyCode))
    }
  },
  mounted() {
    // 选中数据回填到本地
    this.selectedCodes = [...this.surveyCode]
    this.getEntityList()
    this.getAllAlarmEntityObject()
  },
  methods: {
    // 获取报警实体类型
    getEntityList() {
      let data = {
        dictType: 12
      }
      this.$api.getDictionaryList(data).then((res) => {
        // h获取实体类型数据
        if (res.code == 200) {
          this.entityTypeList = res.data
        }
      })
    },
    // 获取全部的报警实体对象
    getAllAlarmEntityObject() {
      this.tableLoading = true
      // 采用一次全部获取，然后本地分页，本地过滤的方式
      const params = {
        projectCode: this.projectCode || '',
        entityTypeId: this.leftFilter.entityTypeId,
        surveyName: this.leftFilter.surveyName,
        page: 1,
        pageSize: 999999
      }
      this.$api
        .getAlarmEntityObjectList(params)
        .then((res) => {
          if (res.code === '200') {
            this.entitySourceData = res.data.list
            this.leftFilterList = this.entitySourceData
            // 左侧搜索
            this.leftSearch()
            // 右侧查询
            this.rightSearch()
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取实体数据失败'))
        .finally(() => (this.tableLoading = false))
    },
    // 左侧table 翻页方法
    getDataList() {
      const offset = (this.pagination.current - 1) * this.pagination.size
      this.leftPageList = this.leftFilterList.slice(offset, offset + this.pagination.size)
      // 计算左侧的分页数量总数
      this.pagination.total = this.leftFilterList.length
      // 触发左侧表格行选中
      this.triggerLeftSelection()
    },
    // 左侧查询
    leftSearch() {
      const { surveyName, entityTypeId } = this.leftFilter
      // 根据表单条件过滤得到左侧的所有数据
      this.leftFilterList = this.entitySourceData.filter((item) => {
        // 如果条件没有输入值，那么就返回true，否则如果匹配则返回true
        return (item.surveyName.includes(surveyName) || !surveyName) && (entityTypeId == item.entityTypeId || !entityTypeId)
      })
      // 重置分页
      this.pagination.current = 1
      // 触发分页
      this.getDataList()
    },
    // 左侧表单点击重置
    leftReset() {
      this.leftFilter.surveyName = ''
      this.leftFilter.entityTypeId = ''
      this.leftSearch()
    },
    // 右侧表单搜索
    rightSearch() {
      // 清空右侧选择项
      this.$refs.rightTable.clearSelection()
      const { surveyName, entityTypeId } = this.rightFilter
      this.targetList = this.rightFilterList.filter((item) => {
        // 如果没有输入值，那么就返回true，否则如果匹配则返回true
        return (item.surveyName.includes(surveyName) || !surveyName) && (entityTypeId == item.entityTypeId || !entityTypeId)
      })
    },
    // 右侧表单重置
    rightReset() {
      this.rightFilter.surveyName = ''
      this.rightFilter.entityTypeId = ''
      // 触发搜索
      this.rightSearch()
    },
    // 左侧表格选中项发生改变
    leftSelect(val) {
      this.leftSelection = val
    },
    // 左侧表格选中项发生改变
    rightSelect(val) {
      this.rightSelection = val
    },
    // 左 -> 右 添加数据
    addSelected() {
      // 判断左侧有没有选中
      if (!this.leftSelection.length) {
        return this.$message.error('请至少选择一条实体对象')
      }
      // 遍历左边选中的code
      this.leftSelection.forEach((it) => {
        // 如果右侧不包含当前实体的code,则添加进去
        if (!this.selectedCodes.includes(it.surveyCode)) {
          this.selectedCodes.push(it.surveyCode)
        }
      })
      this.rightSearch()
    },
    // 右 -> 左 删除数据
    delSelected() {
      // 判断左侧有没有选中
      if (!this.rightSelection.length) {
        return this.$message.error('请至少选择一条实体对象')
      }
      // 遍历右边选中的code
      this.rightSelection.forEach((it) => {
        const index = this.selectedCodes.indexOf(it.surveyCode)
        // 如果包含，那么就删除
        if (index !== -1) {
          this.selectedCodes.splice(index, 1)
        }
      })
      // 触发右侧搜索
      this.rightSearch()
      // 左侧选中只是动作，但是没有触发添加是不生效的，所以不需要进行左侧选中行处理
    },
    // 左侧表格展示的数据根据选中的code进行选择
    triggerLeftSelection() {
      // 先重置选中的行，清空表头选中
      this.$refs.leftTable.clearSelection()
      this.leftPageList.forEach((it) => {
        // 如果左侧存在选中的code,就需要选中这行
        const isChecked = this.selectedCodes.includes(it.surveyCode)
        // 设置行是否选中
        this.$refs.leftTable.toggleRowSelection(it, isChecked)
      })
    },
    // 点击关闭按钮
    closeDialog() {
      this.$emit('closeEntityObjecDialog')
    },
    // 点击确定按钮
    submitDialog() {
      if (!this.selectedCodes.length) {
        return this.$message.error('请至少选择一条实体对象')
      }
      // 从源数据获取选中的行
      const rows = this.entitySourceData
        .filter((it) => this.selectedCodes.includes(it.surveyCode))
        // 并且进行深拷贝，防止修改源数据
        .map((it) => Object.assign({}, it))
      // 触发父组件的方法
      this.$emit('submitEntityObjectDialog', rows)
    }
  }
}
</script>
<style lang="scss" scoped>
.model-dialog {
  .translateContent {
    width: 100%;
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 20px;
  }
  .leftTable,
  .rightTable {
    width: calc((100% - 100px) / 2);
    height: 100%;
    .translateTitle {
      font-weight: 600;
      font-size: 16px;
      color: #333333;
    }
    .searchForm {
      display: flex;
      padding: 10px 0;
      align-items: center;
      width: 100%;
      .el-input,
      .el-select {
        width: 150px;
      }
    }
    .ml-16 {
      margin-left: 16px;
    }
  }
  .translateBtn {
    height: 100%;
    width: 86px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    margin: 0 20px;
    .el-button {
      margin-top: 20px;
      margin-left: 0 !important;
      &:nth-child(1) {
        margin-top: 0;
      }
    }
  }
}
::v-deep .model-dialog {
  background-color: #ffffff !important;
}
</style>
