<template>
  <PageContainer class="monitorDetails">
    <div slot="header" class="monitorDetails-heade">
      <span
        @click="
          () => {
            $router.go(-1)
          }
        "
      >
        <i class="el-icon-arrow-left" />
        <span>{{ $route.query.surveyName }}</span>
      </span>
    </div>
    <div slot="content" class="content-details">
      <div class="content-heade">
        <el-tabs v-model="activeWarnType" @tab-click="handleWarnTypeClick">
          <el-tab-pane label="设备详情" name="equipment" />
          <el-tab-pane
            v-if="$route.meta.activeMenu === '/upsMenu/upsMonitor' || $route.meta.activeMenu === '/upsMenu/upsMonitor/monitorDetails'"
            label="实时数据"
            name="realTimeData"
          />
          <el-tab-pane label="历史数据" name="history" />
          <el-tab-pane label="离线记录" name="offline" />
          <el-tab-pane label="报警历史" name="warnHistory" />
          <el-tab-pane v-if="authRouter.includes($route.meta.activeMenu)" label="运行时长" name="workDuration" />
          <el-tab-pane v-if="$route.query.projectCode == 'IEMC-AccessControlEquipment'" label="出入记录" name="accessRecord" />
          <el-tab-pane v-if="$route.query.projectCode == 'IEMC_AlarmChestCard'" label="历史位置" name="historicalLocation" />
        </el-tabs>
        <div v-if="activeWarnType !== 'equipment'" class="control-btn-header">
          <template v-if="activeWarnType !== 'workDuration'">
            <el-date-picker
              v-model="requestInfo.dataRange"
              type="daterange"
              unlink-panels
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              clearable
            />
            <el-select v-if="activeWarnType === 'history'" v-model="requestInfo.monitorParams" placeholder="监测参数" clearable>
              <el-option v-for="item in monitorParamsOptions" :key="item.paramId" :label="item.paramName" :value="item.paramId"> </el-option>
            </el-select>
            <el-select v-if="activeWarnType === 'history' && currentPattern == 1" v-model="requestInfo.type" placeholder="时间轴" clearable>
              <el-option v-for="item in echartsXOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
            <el-select v-if="activeWarnType === 'offline'" v-model="requestInfo.inlineStatus" placeholder="在线状态" clearable>
              <el-option v-for="item in inlineStatusOptions" :key="item.value" :label="item.paramName" :value="item.value"> </el-option>
            </el-select>
            <el-select v-if="activeWarnType === 'warnHistory'" v-model="requestInfo.alarmLevel" placeholder="报警等级" clearable>
              <el-option v-for="item in alarmLevelOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
            <el-select v-if="activeWarnType === 'warnHistory'" v-model="requestInfo.alarmStatus" placeholder="报警状态" clearable>
              <el-option v-for="item in alarmStatusOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
            <el-input v-if="activeWarnType === 'accessRecord'" v-model="requestInfo.accessName" placeholder="请输入人员名称" clearable style="width: 200px"></el-input>
          </template>
          <template v-else>
            <div style="display: inline-block">
              <el-radio-group v-model="requestInfo.dateType" size="small" @input="dateChange">
                <el-radio-button :label="1">今日</el-radio-button>
                <el-radio-button :label="2">本月</el-radio-button>
                <el-radio-button :label="3">本年</el-radio-button>
                <el-radio-button :label="4">自定义</el-radio-button>
              </el-radio-group>
            </div>
            <el-date-picker
              v-model="requestInfo.dataRange"
              type="daterange"
              unlink-panels
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              clearable
              :disabled="disabledDate"
            />
          </template>
          <div style="display: inline-block">
            <el-button type="primary" plain @click="resetForm">重置</el-button>
            <el-button type="primary" @click="searchForm">查询</el-button>
          </div>
          <div v-if="activeWarnType === 'history'" class="heade-pattern">
            <div class="pattern-item" @click="switchPattern(1)">
              <svg-icon :name="currentPattern == 1 ? 'chartModeActive' : 'chartMode'" class="pattern-icon" />
              <span :style="{ color: currentPattern == 1 ? '#3562DB' : '#414653' }">图形模式</span>
            </div>
            <div class="pattern-item" @click="switchPattern(2)">
              <svg-icon :name="currentPattern == 2 ? 'listModeActive' : 'listMode'" class="pattern-icon" />
              <span :style="{ color: currentPattern == 2 ? '#3562DB' : '#414653' }">列表模式</span>
            </div>
          </div>
        </div>
      </div>
      <div class="content-main">
        <detalDevice v-if="activeWarnType === 'equipment'"></detalDevice>
        <RealTimeData v-if="activeWarnType === 'realTimeData'" ref="dataComponent" :currentPattern="currentPattern" :searchForm="requestInfo" />
        <MonitorHistory v-if="activeWarnType === 'history'" ref="dataComponent" :currentPattern="currentPattern" :searchForm="requestInfo" />
        <OfflineRecord v-if="activeWarnType === 'offline'" ref="dataComponent" :searchForm="requestInfo" />
        <WarnHistory v-if="activeWarnType === 'warnHistory'" ref="dataComponent" :searchForm="requestInfo" />
        <AccessRecord v-if="activeWarnType === 'accessRecord'" ref="dataComponent" :searchForm="requestInfo" />
        <WorkDuration v-if="activeWarnType === 'workDuration'" ref="dataComponent" :searchForm="requestInfo" />
        <HistoricalLocation v-if="activeWarnType === 'historicalLocation'" ref="dataComponent" :searchForm="requestInfo" />
      </div>
    </div>
  </PageContainer>
</template>
<script>
import dayjs from 'dayjs'
import { alarmLevelOptions, alarmStatusOptions } from '@/util/dict.js'
// moment.locale('zh-cn')
export default {
  name: 'monitorDetails',
  components: {
    detalDevice: () => import('../../../../components/equipMent/detalDevice.vue'),
    MonitorHistory: () => import('./monitorHistory.vue'),
    OfflineRecord: () => import('./offlineRecord.vue'),
    WarnHistory: () => import('./warnHistory.vue'),
    WorkDuration: () => import('./WorkDuration.vue'),
    RealTimeData: () => import('./RealTimeData.vue'),
    AccessRecord: () => import('./accessRecord.vue'),
    HistoricalLocation: () => import('./HistoricalLocation.vue')
  },
  async beforeRouteLeave(to, from, next) {
    if (!this.routerNameList.includes(to.name)) {
      await this.$store.commit('keepAlive/clean')
    } else {
      // 获取当前组件对应的key值
      const component = this.$vnode.parent
      const componentKey = component.key
      const parentComponent = component.parent.componentInstance
      // 在对应缓存的父组件上获取cache并清除当前组件的缓存
      if (parentComponent.cache[componentKey]) {
        const index = parentComponent.keys.indexOf(componentKey)
        parentComponent.keys.splice(index, 1)
        delete parentComponent.cache[componentKey]
      }
    }
    next()
  },
  data() {
    const routerNameList = [
      'coldMonitor',
      'realtimeMonitor',
      'sewageMonitor',
      'upsMonitor',
      'airMonitor',
      'envirMonitor',
      'sewerageMonitor',
      'airCooledMonitor',
      'securityMonitor',
      'fireControlMonitor',
      'multiMediaMonitor',
      'elevatorMonitor',
      'monitorDetails',
      'batteryDetail'
    ]
    return {
      routerNameList: Object.freeze(routerNameList),
      alarmLevelOptions: Object.freeze(alarmLevelOptions),
      alarmStatusOptions: Object.freeze(alarmStatusOptions),
      monitorParamsOptions: [],
      inlineStatusOptions: [],
      echartsXOptions: [
        {
          label: '小时',
          value: 1
        },
        {
          label: '日',
          value: 2
        }
      ],
      currentPattern: 2,
      activeWarnType: '',
      requestInfo: {
        projectCode: '',
        type: 1,
        surveyCode: '',
        alarmLevel: '',
        alarmStatus: '',
        monitorParams: '',
        inlineStatus: '',
        dataRange: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')], // 时间范围
        dateType: 1,
        accessName: '', // 出入记录
        paramId: '' // 位置信息id
      },
      disabledDate: true,
      authRouter: ['/airMenu/airMonitor', '/coldHeat/coldMonitor', '/airCooled/airCooledMonitor', '/sewerageMenu/sewerageMonitor', '/fireControlMenu/fireControlMonitor']
    }
  },
  computed: {},
  mounted() {
    if (!this.$store.state.keepAlive.list.some((e) => this.routerNameList.includes(e))) {
      this.initEvent()
    }
  },
  activated() {
    this.initEvent()
  },
  methods: {
    initEvent() {
      this.$nextTick(() => {
        Object.assign(this.$data, this.$options.data())
        // this.$refs.dataComponent?.$destroy()
        console.log(this.$refs.dataComponent)
        if (this.$route.meta.activeMenu === '/upsMenu/upsMonitor' || this.$route.meta.activeMenu === '/upsMenu/upsMonitor/monitorDetails') {
          this.activeWarnType = 'realTimeData'
        } else {
          this.activeWarnType = 'history'
        }
        this.requestInfo.projectCode = this.$route.query.projectCode
        this.requestInfo.surveyCode = this.$route.query.surveyCode
        this.requestInfo.paramId = this.$route.query.locationId
        this.$refs.dataComponent?.getDetails()
        this.getParamSurveyCode()
      })
    },
    getParamSurveyCode(statusParamId = '', dataField = 'monitorParamsOptions') {
      const params = {
        surveyCode: this.requestInfo.surveyCode,
        statusParamId: statusParamId
      }
      this.$api.getParamSurveyCode(params).then((res) => {
        if (res.code == '200') {
          this[dataField] = res.data
        } else {
          this[dataField] = []
        }
      })
    },
    switchPattern(type) {
      if (this.currentPattern != type) {
        this.currentPattern = type
      }
    },
    handleWarnTypeClick(val) {
      if (this.activeWarnType === 'offline' && !this.inlineStatusOptions.length) {
        this.getParamSurveyCode('100638', 'inlineStatusOptions')
      }
      // this.searchForm()
    },
    // 重置查询表单
    resetForm() {
      this.requestInfo = {
        projectCode: this.$route.query.projectCode,
        surveyCode: this.$route.query.surveyCode,
        paramId: this.$route.query.locationId,
        type: 1,
        alarmLevel: '',
        alarmStatus: '',
        monitorParams: '',
        inlineStatus: '',
        dataRange: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
      }
      this.searchForm()
    },
    // 查询
    searchForm() {
      if (!this.requestInfo.dataRange.length) {
        this.$message.warning('请选择时间段！')
        return
      }
      this.$nextTick(() => {
        this.$refs.dataComponent.getDetails()
      })
    },
    dateChange(val) {
      this.disabledDate = val !== 4
      switch (val) {
        case 1:
          this.requestInfo.dataRange = [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
          break
        case 2:
          this.requestInfo.dataRange = [dayjs().startOf('month').format('YYYY-MM-DD'), dayjs().endOf('month').format('YYYY-MM-DD')]
          break
        case 3:
          this.requestInfo.dataRange = [dayjs().startOf('year').format('YYYY-MM-DD'), dayjs().endOf('year').format('YYYY-MM-DD')]
          break
        case 4:
          this.requestInfo.dataRange = []
          break
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.monitorDetails {
  margin: 16px;
  p {
    margin: 0;
  }
  .monitorDetails-heade {
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    align-items: center;
    padding: 13px 16px;
    font-size: 14px;
    font-weight: 500;
    color: #121f3e;
    line-height: 14px;
    > i,
    span {
      cursor: pointer;
    }
    .el-icon-arrow-left {
      font-size: 13px;
      margin-right: 8px;
      color: #7f848c;
    }
  }
  ::v-deep .container-header {
    border-radius: 4px 4px 0 0;
  }
  .content-details {
    height: 100%;
    display: flex;
    flex-direction: column;
    // overflow: auto;
    .content-heade {
      padding: 0px 16px 16px;
      background: #fff;
      border-radius: 0 0 4px 4px;
      ::v-deep .el-tabs__header .el-tabs__nav-wrap::after {
        height: 0;
      }
    }
    ::v-deep .box-card {
      padding: 0;
      height: auto;
      .card-body {
        overflow: hidden;
      }
    }
    .control-btn-header {
      padding: 0;
      position: relative;
      & > div {
        margin-right: 10px;
        margin-top: 10px;
      }
      .btn-item {
        border: 1px solid #3562db;
        color: #3562db;
        font-family: none;
      }
      .btn-active {
        color: #fff;
        background: #3562db;
      }
      .heade-pattern {
        display: flex;
        position: absolute;
        right: 16px;
        top: 50%;
        margin: 0 !important;
        transform: translateY(-50%);
        .pattern-item {
          cursor: pointer;
          font-size: 15px;
          .pattern-icon {
            font-size: 16px;
            margin-right: 6px;
          }
        }
        .pattern-item:last-child {
          margin-left: 16px;
        }
      }
    }
    .content-main {
      flex: 1 1 auto;
      overflow: hidden;
      background: #fff;
    }
  }
}
</style>
