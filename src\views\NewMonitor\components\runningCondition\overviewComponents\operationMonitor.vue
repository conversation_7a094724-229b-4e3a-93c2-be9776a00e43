<!-- 运行监测 -->
<template>
  <ContentCard :title="item.componentTitleShow" :scrollbarHover="true" :cstyle="{ height: '100%' }" class="drag_class"
    :hasMoreOper="['edit']" @more-oper-event="(val) => $emit('all-more-Oper', val, 'operationMonitor')">
    <div slot="content" class="operation-list">
      <div v-for="item in operationList" :key="item.id" class="list-item" @click="detailed(item)"
        style="cursor: pointer;">
        <div class="item-left">
          <img class="left-icon" :src="$tools.imgUrlTranslation(item.dictionaryDetailsPicUrl)"
            v-if="item.dictionaryDetailsPicUrl !== ''">
          <img class="left-icon" v-if="item.dictionaryDetailsPicUrl == ''"
            src='../../../../../assets/images/newMonitor/universalIcon.png'>
          <div class="left-info">
            <p class="info-name">{{ item.dictionaryDetailsName }}</p>
            <p style="margin-top: 8px;">
              <span class="info-num">{{ item.totalCount }}</span>
              <span class="info-unit">台</span>
            </p>
          </div>
        </div>
        <div class="item-right">
          <el-progress type="circle" :width="67" :stroke-width="10" strokeLinecap="" color="#3562DB"
            define-back-color="#CCCED3" :show-text="false"
            :percentage="percentage(item.onlineStatusCount, item.totalCount)" />
          <div class="right-info">
            <p class="info-box">
              <span class="info-chart" style="background-color: #3562db;"></span>
              <span class="info-name">在线</span>
              <span class="info-num" style="color: #3562db;">{{ item.onlineStatusCount }}</span>
              <span class="info-unit">个</span>
            </p>
            <p class="info-box" style="margin-top: 14px;">
              <span class="info-chart" style="background-color: #CCCED3;"></span>
              <span class="info-name">离线</span>
              <span class="info-num">{{ item.offlineStatusCount }}</span>
              <span class="info-unit">个</span>
            </p>
          </div>
        </div>
      </div>
    </div>
  </ContentCard>
</template>
<script>
export default {
  name: 'deviceOperationMonitor',
  props: {
    item: {
      type: Object,
      default: () => { }
    },
    systemCode: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      operationList: []
    }
  },
  created() {
    this.getDeviceAnalysisData()
  },
  methods: {
    // 查看详情
    detailed(val) {
      let path = 'operationalMonitoring'
      // 强制更新组件
      this.$forceUpdate();
      if (!path) return
      this.$router.push({
        path: path,
        query: {
          sysOf1: val.sysOf1,
        }
      })
    },
    percentage(num, denom) {
      if (num && denom && denom !== 0) {
        return (num / denom) * 100
      } else {
        return 0
      }
    },
    getDeviceAnalysisData(id) {
      this.$api.getDeviceAnalysis({ systemCode: this.systemCode, groupId: id || "" }).then((res) => {
        if (res.code == 200 || res.code == '200') {
          this.operationList = res.data
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.operation-list {
  height: 100%;
  display: flex;
  gap: 16px;
  flex-direction: row;

  .list-item {
    background-color: #faf9fc;
    border-radius: 4px;
    padding: 16px;
    margin-top: 16px;
    display: flex;
    flex-wrap: wrap;
    min-width: 368px;

    .item-left {
      display: flex;
      align-items: center;

      .left-icon {
        width: 60px;
        height: 60px;
      }

      .left-info {
        margin-left: 16px;
        margin-top: 5px;

        .info-name {
          font-size: 15px;
          font-weight: 500;
          color: #121f3e;
          line-height: 18px;
          width: 90px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .info-num {
          font-size: 30px;
          font-weight: bold;
          color: #121f3e;
          line-height: 36px;
        }

        .info-unit {
          font-size: 15px;
          font-weight: 500;
          color: #ccced3;
          line-height: 18px;
          margin-left: 4px;
        }
      }
    }

    .item-right {
      display: flex;
      align-items: center;

      .info-box {
        display: flex;
        align-items: center;
        margin-left: 20px;
      }

      .info-chart {
        display: inline-block;
        width: 8px;
        height: 8px;
      }

      .info-name {
        font-size: 14px;
        font-weight: 500;
        color: #121f3e;
        margin-left: 6px;
      }

      .info-num {
        font-size: 18px;
        font-weight: bold;
        margin-left: 10px;
        color: #414653;
      }

      .info-unit {
        font-size: 15px;
        font-weight: 500;
        color: #ccced3;
        margin-left: 4px;
      }
    }
  }
}
</style>
