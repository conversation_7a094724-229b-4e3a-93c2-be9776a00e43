<template>
  <div>
    <div class="bg-banner" :style="{ 'background-image': 'url(' + hospitalEvnClass['bg-banner'] + ')' }" />
    <div v-if="hospitalEvnClass.VUE_APP_HOSPITAL_NODE_ENV === 'sinomis'" class="sion-login-header">
      <img src="../assets/images/logo-1.png" alt="" />
    </div>
    <div v-else-if="hospitalEvnClass.VUE_APP_HOSPITAL_NODE_ENV === 'szzlyy'" class="sion-login-header image-szzlyy">
      <img src="../assets/images/szzlyy.png" alt="" />
    </div>
    <div v-else-if="hospitalEvnClass.VUE_APP_HOSPITAL_NODE_ENV === 'bjazyy'" class="sion-login-header">
      <img src="../assets/images/bjazyy.png" alt="" />
    </div>
    <div v-else-if="hospitalEvnClass.VUE_APP_HOSPITAL_NODE_ENV === 'szlhyy'" class="sion-login-header image-szzlyy">
      <img src="../assets/images/szlhyy.png" alt="" />
    </div>
    <div v-else-if="hospitalEvnClass.VUE_APP_HOSPITAL_NODE_ENV === 'szdeyy'" class="sion-login-header image-szzlyy">
      <img src="../assets/images/szdeyy.png" alt="" />
    </div>
    <div v-else-if="hospitalEvnClass.VUE_APP_HOSPITAL_NODE_ENV === 'fjslyy'" class="sion-login-header">
      <img src="../assets/images/fjslyy.png" alt="" />
    </div>
    <div v-else-if="hospitalEvnClass.VUE_APP_HOSPITAL_NODE_ENV === 'schxyy'" class="sion-login-header image-schxyy">
      <img src="../assets/images/schxyy.png" alt="" />
    </div>
    <div v-else-if="hospitalEvnClass.VUE_APP_HOSPITAL_NODE_ENV === 'szrmyy'" class="sion-login-header image-szrmyy">
      <img src="../assets/images/szrmyy.png" alt="" />
    </div>
    <div v-else-if="hospitalEvnClass.VUE_APP_HOSPITAL_NODE_ENV === 'szdermyy'" class="sion-login-header image-szrmyy">
      <img src="../assets/images/szdermyy.png" alt="" />
    </div>
    <div v-else-if="hospitalEvnClass.VUE_APP_HOSPITAL_NODE_ENV === 'mhzyy'" class="sion-login-header">
      <img src="../assets/images/mhzyy.png" alt="" />
    </div>
    <div class="sion-login-footer">
      <!-- <a href="http://beian.miit.gov.cn/" target="_blank">CopyRight 2020 logimis.com All Rights Reserved 京ICP备17052614号-3</a> -->
      <a href="http://beian.miit.gov.cn/" target="_blank">中科医信公司版权所有 Copyright © 2023 logimis.com All Rights Reserved. </a>
    </div>
    <div
      id="login-box"
      :class="{
        'szzlyy-login-box': hospitalEvnClass.VUE_APP_HOSPITAL_NODE_ENV === 'szzlyy'
      }"
    >
      <div class="login-banner">
        <div class="login-banner-img"></div>
      </div>
      <el-form v-show="formType == 'login'" ref="loginForm" :model="loginForm" :rules="loginRules" size="default" class="login-form" autocomplete="on" label-position="left">
        <img class="triangle_top" src="./../assets/images/rectangle.png" alt="" />
        <img class="triangle_bottom" src="./../assets/images/rectangle.png" alt="" />
        <div class="title-container">
          <div class="sty"></div>
          <div class="title">登录</div>
        </div>
        <div>
          <el-form-item prop="account" class="interval">
            <el-input ref="name" v-model="loginForm.account" :placeholder="$t('app.account')" type="text" tabindex="1" autocomplete="on">
              <svg-icon slot="prefix" name="user" />
            </el-input>
          </el-form-item>
          <el-form-item prop="password" class="interval">
            <el-input
              ref="password"
              v-model="loginForm.password"
              :type="passwordType"
              :placeholder="$t('app.password')"
              tabindex="2"
              autocomplete="on"
              @keyup.enter.native="handleLogin"
            >
              <svg-icon slot="prefix" name="password" />
              <svg-icon slot="suffix" :name="passwordType === 'password' ? 'eye' : 'eye-open'" @click="showPassword" />
            </el-input>
          </el-form-item>
          <el-form-item v-if="errorNum >= 5" prop="code" class="interval">
            <div style="display: flex">
              <el-input v-model="loginForm.code" placeholder="请输入验证码"></el-input>
              <canvas id="code" width="100" height="48" @click="getCode"></canvas>
            </div>
          </el-form-item>
        </div>
        <el-button :loading="loading" type="primary" size="default" style="width: 100%; height: 48px" @click.native.prevent="handleLogin">
          {{ $t('app.login') }}
        </el-button>
        <div class="flex-bar">
          <!-- <el-checkbox v-model="loginForm.remember">记住我</el-checkbox> -->
          <el-button type="text" @click="formType = 'reset'">忘记密码？</el-button>
        </div>
        <div style="text-align: center; color: #7f848c; font-size: 14px; margin-top: 40px">请使用Google浏览器，最佳分辨率1920x1080</div>
        <div style="text-align: center; color: #121f3e; font-size: 14px; margin-top: 40px">{{ projectVersion }}</div>
      </el-form>
      <el-form v-show="formType == 'reset'" ref="resetForm" :model="resetForm" :rules="resetRules" size="default" class="login-form" auto-complete="on" label-position="left">
        <img class="triangle_top" src="./../assets/images/rectangle.png" alt="" />
        <img class="triangle_bottom" src="./../assets/images/rectangle.png" alt="" />
        <div class="title-container">
          <div class="sty"></div>
          <div class="title">重置密码</div>
        </div>
        <div>
          <el-form-item prop="phone" class="interval">
            <el-input ref="name" v-model="resetForm.phone" :placeholder="$t('app.phone')" type="text" tabindex="1" autocomplete="on">
              <svg-icon slot="prefix" name="user" />
            </el-input>
          </el-form-item>
          <el-form-item prop="captcha" class="interval">
            <el-input ref="captcha" v-model="resetForm.captcha" :placeholder="$t('app.captcha')" type="text" tabindex="2" autocomplete="on">
              <svg-icon slot="prefix" name="user" />
              <el-button slot="append" :disabled="clickFlag" @click="getPhoneCode">
                {{ clickFlag ? getCodeText : $t('app.sendCaptcha') }}
              </el-button>
            </el-input>
          </el-form-item>
          <el-form-item prop="newPassword" class="interval">
            <el-input ref="newPassword" v-model="resetForm.newPassword" :type="passwordType" :placeholder="$t('app.newPassword')" tabindex="3" autocomplete="on">
              <svg-icon slot="prefix" name="password" />
              <svg-icon slot="suffix" :name="passwordType === 'password' ? 'eye' : 'eye-open'" @click="showPassword" />
            </el-input>
          </el-form-item>
          <el-form-item prop="confirmPassword" class="interval">
            <el-input ref="confirmPassword" v-model="resetForm.confirmPassword" :type="confirmPasswordType" :placeholder="$t('app.confirmPassword')" tabindex="3" autocomplete="on">
              <svg-icon slot="prefix" name="password" />
              <svg-icon slot="suffix" :name="confirmPasswordType === 'password' ? 'eye' : 'eye-open'" @click="showConfirmPassword" />
            </el-input>
          </el-form-item>
        </div>
        <el-row :gutter="15">
          <el-col :md="6">
            <el-button size="default" style="width: 100%; height: 48px" @click="formType = 'login'">{{ $t('app.goLogin') }} </el-button>
          </el-col>
          <el-col :md="18">
            <el-button :loading="loading" type="primary" size="default" style="width: 100%; height: 48px" @click.native.prevent="handleFind">
              {{ $t('app.check') }}
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>
<script>
import code_draw from '@/util/code'
import storage from '@/util/storage'
import bgBannerSinomis from '@/assets/images/login-bg.jpg'
import bgBannerSjtyy from '@/assets/images/login-bg-sjtyy.png'
import bgBannerGqgjzyy from '@/assets/images/login-bg-gqgjzyy.png'
import bgBannerJxsdeyy from '@/assets/images/login-bg-jxsdeyy.png'
import bgBannerBjsyzyyy from '@/assets/images/login-bg-bjsyzyyy.png'
import bgBannerLjxyy from '@/assets/images/login-bg-ljxyy.png'
import moment from 'moment'
import axios from 'axios'
export default {
  // eslint-disable-next-line vue/multi-word-component-names
  name: 'login',
  data() {
    const hospitalEvnClassList = {
      sinomis: bgBannerSinomis,
      bjsjtyy: bgBannerSjtyy,
      gqgjzyy: bgBannerGqgjzyy,
      jxsdeyy: bgBannerJxsdeyy,
      bjsyzyyy: bgBannerBjsyzyyy,
      ljxyy: bgBannerLjxyy
    }
    const codeCheck = (rule, value, callback) => {
      let val = value.toLowerCase()
      let num = document.getElementById('code').getAttribute('data-code')
      if (!val) {
        callback(new Error('请输入验证码'))
      } else if (val != num) {
        callback(new Error('请输入正确的验证码'))
      } else {
        callback()
      }
    }
    // 新密码密码
    const newPasswordCheck = (rule, value, callback) => {
      // 6-16位字母和数字组合 包含字符
      if (!/^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d!@#$%^&*()_+\-=\[\]{};':"\\|,.<>/?]{6,16}$/.test(value)) {
        callback(new Error('登录密码为6-16位字母和数字组合'))
      } else {
        callback()
      }
    }
    // 确认密码
    const confirmPasswordCheck = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入新的登录密码'))
      } else if (value !== this.resetForm.newPassword) {
        callback(new Error('两次密码输入不一致'))
      } else {
        callback()
      }
    }
    return {
      // 表单类型，login 登录，reset 重置密码
      formType: 'login',
      errorNum: 1,
      loginForm: {
        account: storage.local.get('login_account'),
        password: '',
        remember: storage.local.has('login_account'),
        code: ''
      },
      loginRules: {
        account: [{ required: true, trigger: 'blur', message: '请输入用户名' }],
        password: [
          { required: true, trigger: 'blur', message: '请输入密码' },
          { min: 6, max: 16, trigger: 'blur', message: '密码长度为6到16位' }
        ],
        code: [{ validator: codeCheck, trigger: 'blur' }]
      },
      resetForm: {
        phone: storage.local.get('login_account'),
        captcha: '',
        newPassword: '',
        confirmPassword: ''
      },
      resetRules: {
        phone: [{ required: true, trigger: 'blur', message: '请输入手机号' }],
        captcha: [
          { required: true, trigger: 'blur', message: '请输入验证码' },
          { min: 6, max: 6, trigger: 'blur', message: '请输入正确的验证码' }
        ],
        newPassword: [
          { required: true, trigger: 'blur', message: '请输入新密码' },
          { min: 6, max: 16, trigger: 'blur', message: '密码长度为6到16位' },
          { validator: newPasswordCheck, trigger: 'blur' }
        ],
        confirmPassword: [
          { min: 6, max: 18, trigger: 'blur', message: '密码长度为6到18位' },
          { validator: confirmPasswordCheck, trigger: 'blur' }
        ]
      },
      loading: false,
      passwordType: 'password',
      confirmPasswordType: 'password',
      redirect: undefined,
      hospitalEvnClass: {
        'bg-banner': hospitalEvnClassList[__PATH.VUE_APP_HOSPITAL_NODE_ENV] || bgBannerSinomis,
        VUE_APP_HOSPITAL_NODE_ENV: __PATH.VUE_APP_HOSPITAL_NODE_ENV
      },
      projectVersion: __PATH.PROJECT_VERSION,
      clickFlag: false, // true:不可点击；false:可点击
      getCodeText: '获取验证码'
    }
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    },
    errorNum(val) {
      if (val >= 5) {
        this.getCode()
      }
    }
  },
  methods: {
    // 获取验证码
    getPhoneCode() {
      if (!/^1\d{10}$/.test(this.resetForm.phone)) {
        this.$message({ message: '请输入正确的手机号', type: 'warning' })
        return
      }
      this.$api
        .getPhoneCode(this.resetForm.phone)()
        .then((ret) => {
          if (ret.code == 200) {
            this.showMsgCountdown()
            this.$message({ message: ret.msg, type: 'success' })
          } else {
            this.$message({ message: ret.msg, type: 'warning' })
          }
        })
        .catch((err) => {
          this.$message({ message: '获取验证码失败，请重试！', type: 'warning' })
        })
    },
    // 显示手机短信倒计时
    showMsgCountdown() {
      let count = 61
      let timeout = null
      this.clickFlag = true
      let loop = () => {
        count--
        if (count == 0) {
          clearTimeout(timeout)
          this.clickFlag = false
          timeout = null
          return
        } else {
          this.getCodeText = '重新发送(' + count + 's)'
          clearTimeout(timeout)
          timeout = setTimeout(loop, 1000)
        }
      }
      loop()
    },
    getCode() {
      this.$nextTick(() => {
        code_draw('code')
      })
    },
    showConfirmPassword() {
      this.confirmPasswordType = this.confirmPasswordType === 'password' ? '' : 'password'
      this.$nextTick(() => {
        this.$refs.confirmPassword.focus()
      })
    },
    showPassword() {
      this.passwordType = this.passwordType === 'password' ? '' : 'password'
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    handleLogin() {
      console.log('handleLogin')
      // this.$router.go(0)
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true
          this.$store
            .dispatch('user/login', this.loginForm)
            .then((res) => {
              if (this.loginForm.remember) {
                storage.local.set('login_account', this.loginForm.account)
              } else {
                storage.local.remove('login_account')
              }
              this.getJurisdictionData()
              // 登录成功之后添加登录日志
              this.addLoginLog(this.$store.state.user.userInfo.user.staffName, 1)
              this.$router
                .push({ path: this.redirect || '/' })
                .then(() => {})
                .catch(() => {})
                .finally(() => {
                  setTimeout(() => {
                    this.loading = false
                  }, 500)
                })
            })
            .catch((err) => {
              this.errorNum += 1
              this.loading = false
              this.$message.error(err.message)
              // 登录失败之后添加登录日志
              this.addLoginLog('', 0, err.message)
            })
        }
      })
    },
    // 调取实验室登录信息
    getJurisdictionData() {
      let params = {
        mobile: this.loginForm.account,
        platform: 1
      }
      this.$api.loginLaboratory(params).then((res) => {
        if (res.code == 200) {
          sessionStorage.setItem(
            'routeInfo',
            JSON.stringify({
              systemCode: 'course',
              token: res.data.token,
              hospitalCode: res.data.hospitalCode,
              unitCode: res.data.unitCode,
              createName: res.data.name,
              userId: res.data.userId,
              isFalg: '0'
            })
          )
        } else {
          sessionStorage.setItem('routeInfo', JSON.stringify({ systemCode: 'course', isFalg: '1' }))
        }
      })
    },
    // 登录之后添加登录日志
    addLoginLog(userName, result, msg) {
      let params = {
        loginPlatform: 1,
        loginResult: result,
        userAccount: this.loginForm.account,
        userName: userName,
        loginTime: moment().format('YYYY-MM-DD HH:mm:ss'),
        loginType: 1,
        resultMessage: msg
      }
      axios
        .get('https://api.ipify.org?format=json')
        .then((data) => {
          const ip = data.data.ip
          params.clientIp = ip
          // 通过ip地址获取所在地
          axios
            .get(`https://api.vore.top/api/IPdata?ip=${ip}`)
            .then((res) => {
              const address = res.data.ipdata.info1 + res.data.ipdata.info2
              params.loginAdress = address
              this.$api.addLoginLogList(params)
            })
            .catch((error) => {
              this.$api.addLoginLogList(params)
            })
        })
        .catch((error) => {
          this.$api.addLoginLogList(params)
        })
    },
    handleFind() {
      this.$refs.resetForm
        .validate()
        .catch(() => Promise.reject(null))
        .then(() => {
          const params = {
            mobile: this.resetForm.phone,
            code: this.resetForm.captcha
          }
          this.loading = true
          // 先做短信验证码的校验
          return this.$api.smsCodeCheck(params)
        })
        .then((res) => {
          if (res.code === '200') {
            const params = {
              mobile: this.resetForm.phone,
              password: this.resetForm.newPassword,
              repeatPassword: this.resetForm.confirmPassword
            }
            // 调用修改密码接口
            return this.$api.resetPassword(params)
          } else {
            throw res.msg || res.message || '短信验证码验证失败'
          }
        })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success(res.data || '已重置为新密码')
            // 切换为登录模式
            this.formType = 'login'
          } else {
            throw res.msg || res.message || '修改密码失败'
          }
        })
        .catch((msg) => msg && this.$message.error(msg))
        .finally(() => {
          this.loading = false
        })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep input[type='password']::-ms-reveal {
  display: none;
}
.bg-banner {
  position: absolute;
  z-index: 0;
  width: 100%;
  height: 100%;
  // --bg-banner: url(../assets/images/login-bg.jpg);
  // url(../assets/images/login-bg-sjtyy.png)
  // background-image: url(../assets/images/login-bg.jpg);
  // background-image: url(../assets/images/login-bg-sjtyy.png);
  // background-image: var(--bg-banner);
  background-size: 100% 100%;
  background-position: center center;
}
.szzlyy-login-box {
  background: url(../assets/images/login-aside-szzlyybg.png) no-repeat !important;
}
#login-box {
  min-width: 1200px;
  height: 550px;
  margin: auto;
  background: url(../assets/images/login-aside-bg.png) no-repeat;
  background-size: 100% 100%;
  display: flex;
  justify-content: space-between;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
  box-shadow: 0 0 5px #999;
  border-radius: 4px;
  .title_describe {
    width: 100%;
    height: 50px;
    margin-top: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
    p {
      font-size: 36px;
      font-weight: bold;
      color: #fff;
      padding: 0 15px;
      margin: 0;
    }
    span {
      width: 2px;
      height: 28px;
      background: #fff;
      border-radius: 0;
      opacity: 0.5;
    }
  }
  .interval {
    margin-bottom: 40px;
  }
  .login-banner {
    width: calc(97% - 500px);
    position: relative;
    .login-banner-img {
      width: 67%;
      height: 62%;
      position: absolute;
      left: 50%;
      bottom: 7%;
      transform: translateX(-50%);
      background: url(../assets/images/Frame.png) no-repeat;
      background-size: 100% 100%;
    }
  }
  .login-form {
    position: absolute;
    top: -35px;
    right: 3%;
    box-shadow: 0 0 30px 0 rgb(106 135 205 / 20%);
    background-color: #fff;
    height: 620px;
    width: 500px;
    padding: 30px 60px;
    border-radius: 0 4px 4px 0;
    // 左侧三角形向外显示  取消overflow: hidden;
    // overflow: hidden;
    .triangle_top {
      position: absolute;
      top: 0;
      left: -35px;
    }
    .triangle_bottom {
      position: absolute;
      bottom: 0;
      left: -35px;
      transform: rotate(-90deg);
    }
    .svg-icon {
      vertical-align: -0.35em;
    }
    .title-container {
      position: relative;
      .sty {
        position: absolute;
        bottom: 0;
        left: 140px;
        border-bottom: 3px solid #3562db;
        width: 100px;
      }
      .title {
        width: 380px;
        height: 66px;
        line-height: 66px;
        border-radius: 0;
        opacity: 1;
        border-bottom: 1px solid #dcdfe6;
        font-size: 24px;
        color: #3562db;
        margin: 0 auto 50px;
        text-align: center;
        font-weight: bold;
        @include text-overflow;
      }
    }
  }
  ::v-deep .el-input {
    height: 48px;
    line-height: inherit;
    width: 100%;
    input {
      height: 48px;
    }
    .el-input__prefix {
      left: 10px;
    }
    .el-input__suffix {
      right: 10px;
    }
  }
  .flex-bar {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 40px;
    margin-top: 12px;
  }
  .el-checkbox {
    margin: 10px 0;
  }
}
.sion-login-header {
  width: 200px;
  height: calc(64px + 70px);
  display: flex;
  align-items: center;
  margin-left: 64px;
  position: absolute;
  img {
    width: 100%;
  }
}
.image-szzlyy,
.image-szrmyy {
  width: 376px;
}
.image-schxyy {
  height: 300px;
}
.sion-login-footer {
  position: absolute;
  bottom: 5%;
  color: #121f3e;
  width: 100%;
  text-align: center;
  a {
    font-size: 14px;
    color: #121f3e;
    text-decoration: none;
  }
}
</style>
