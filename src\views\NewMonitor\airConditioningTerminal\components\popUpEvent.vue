<template>
  <el-dialog :title="title" width="500px" :visible.sync="DialogShow" custom-class="model-dialog"
    :before-close="closeDialog">
    <div class="export_content" style="padding: 10px 20px 10px 10px;">
      <el-form ref="Form" :model="typeData" :rules="exportRules" label-width="100px" style="display:flex">
        <el-form-item :label="dataType.name" prop="value" v-if="enumDialogShow">
          <el-select v-model.trim="typeData.value" clearable filterable placeholder="请选择">
            <el-option v-for="item in dataType.valueType.elements" :label="item.textAlias ? item.textAlias : item.text" :key="item.value"
              :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="dataType.name" prop="value" v-else-if="booleDialogShow">
          <el-select v-model.trim="typeData.value" clearable filterable placeholder="请选择">
            <el-option
              :label="dataType.valueType.trueTextAlias ? dataType.valueType.trueTextAlias : dataType.valueType.trueText"
              :value="dataType.valueType.trueValue"></el-option>
            <el-option
              :label="dataType.valueType.falseTextAlias ? dataType.valueType.falseTextAlias : dataType.valueType.falseText"
              :value="dataType.valueType.falseValue"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="dataType.name" prop="value" v-else>
          <el-input v-model.trim="typeData.value" placeholder="请输入"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="groupSubmit('Form')">执 行</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  name: 'monitorExport',
  props: {
    title: {
      type: String,
      default: ''
    },
    metadataTag: {
      type: String,
      default: ''
    },
    DialogShow: {
      type: Boolean,
      default: false
    },
    booleDialogShow: {
      type: Boolean,
      default: false
    },
    enumDialogShow: {
      type: Boolean,
      default: false
    },
    dataType: {
      type: Object,
      default: () => { }
    },
  },
  data() {
    return {
      typeData: {
        value: '',
        metadataTag: this.metadataTag,
        id: this.dataType.id
      },
      exportRules: {
        value: [
          { required: true, message: '请选择', trigger: 'change' }
        ],

      },
    }
  },
  mounted() {
    console.log(this.dataType, 'this.dataType');
    console.log(this.enumDialogShow, 'enumDialogShow', this.booleDialogShow, 'booleDialogShow');
  },
  methods: {
    closeDialog() {
      this.$emit('closeDialog')
    },
    groupSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$emit('submitDialog', this.typeData)
        } else {
          return false
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.model-dialog {
  height: 320px;

  .export_content {
    margin: 0 auto;
    background: #fff;
    border-radius: 4px;
    // display: flex;
    width: 100%;
    height: 100%;
  }
}
</style>
