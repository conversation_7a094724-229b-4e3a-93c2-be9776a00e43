<!--
 * @Description:
-->
<template>
  <div class="warn-history">
    <div class="wholeTable-main table-content">
      <ContentCard title="实时监控">
        <div slot="content" class="batteryContent">
          <div v-for="(item, index) in batteryList" :key="index" class="batteryItem">
            <div>
              {{ item.paramName }}
            </div>
            <div style="margin-top: 20px">
              <span style="font-size: 24px; font-weight: blod">{{ item.paramValue }}</span>
              <span>{{ item.unitName }}</span>
            </div>
          </div>
        </div>
      </ContentCard>
      <ContentCard v-if="batteryGroupList.length" title="单电池数据">
        <div slot="content">
          <div class="title-tabs">
            <el-button-group>
              <el-button size="small" :type="showList ? 'primary' : ''" :plain="!showList" @click="modelChange(true)">列表</el-button>
              <el-button size="small" :type="!showList ? 'primary' : ''" :plain="showList" @click="modelChange(false)">图表</el-button>
            </el-button-group>
          </div>
          <div v-if="showList" class="batteryContent">
            <TablePage ref="table" style="width: 100%" :showPage="false" :tableColumn="tableColumn" :data="batteryGroupList"> </TablePage>
          </div>
          <div v-else>
            <div class="chartsContent">
              <div>温度（℃）</div>
              <div id="temperature" ref="temperature"></div>
            </div>
            <div class="chartsContent">
              <div>电压（V）</div>
              <div id="voltage" ref="voltage"></div>
            </div>
            <div class="chartsContent">
              <div>内阻（mΩ）</div>
              <div id="internalResistance" ref="internalResistance"></div>
            </div>
          </div>
        </div>
      </ContentCard>
    </div>
  </div>
</template>
<script lang="jsx">
import tableListMixin from '@/mixins/tableListMixin.js'
import * as echarts from 'echarts'
export default {
  name: 'warnHistory',
  mixins: [tableListMixin],
  props: {
    searchForm: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      loading: false,
      batteryList: [],
      showList: true,
      tableColumn: [
        {
          prop: 'surveyName',
          label: '设备名称',
          render: (h, scope) => {
            return (
              <div class="tableLineLink" onClick={() => this.goDetail(scope.row)}>
                {scope.row.surveyName}
              </div>
            )
          }
        },
        {
          prop: 'surveyNo',
          label: '设备编码',
          render: (h, scope) => {
            return (
              <div class="tableLineLink" onClick={() => this.goDetail(scope.row)}>
                {scope.row.surveyNo}
              </div>
            )
          }
        },
        {
          prop: 'paramValue',
          label: '温度（℃）',
          render: (h, row) => {
            let val = row.row.paramIds.filter((item) => {
              return item.paramId === 100739
            })
            return <div>{val[0].paramValue}</div>
          }
        },
        {
          prop: 'dianya',
          label: '电压（V）',
          render: (h, row) => {
            let val = row.row.paramIds.filter((item) => {
              return item.paramId === 100737
            })
            return <div>{val[0].paramValue}</div>
          }
        },
        {
          prop: 'neizu',
          label: '内阻（mΩ）',
          render: (h, row) => {
            let val = row.row.paramIds.filter((item) => {
              return item.paramId === 100741
            })
            return <div>{val[0].paramValue}</div>
          }
        }
      ],
      batteryGroupList: [],
      temperature: [],
      voltage: [],
      internalResistance: []
    }
  },
  activated() {
    // this.$nextTick(() => {
    //   setTimeout(() => {
    //     this.getDetails()
    //   }, 50)
    //   this.$forceUpdate()
    // })
  },
  mounted() {
    this.getDetails()
  },
  methods: {
    getDetails() {
      this.getUpsBatteryList()
      this.getUpsBatteryGroupList()
    },
    // 初始化组件数据
    getUpsBatteryList() {
      let { projectCode, surveyCode } = this.searchForm
      console.log(1, surveyCode)
      let params = {
        projectCode: projectCode,
        surveyCode: surveyCode,
        surveyName: '',
        surveyNo: ''
      }
      this.loading = true
      this.$api
        .upsElectData(params)
        .then((res) => {
          this.loading = false
          if (res.code == 200) {
            this.batteryList = res.data ? res.data.paramIds : []
          } else {
            this.batteryList = []
          }
        })
        .catch((err) => {
          this.loading = false
        })
    },
    modelChange(val) {
      this.showList = val
      if (this.showList) {
        this.getUpsBatteryGroupList()
      } else {
        this.getUpsBatteryChartsData()
      }
    },
    // 电池组
    getUpsBatteryGroupList() {
      let { projectCode, surveyCode } = this.searchForm
      console.log(2, surveyCode)
      let params = {
        projectCode: projectCode,
        surveyCode: surveyCode,
        surveyName: '',
        surveyNo: ''
      }
      this.loading = true
      this.$api
        .upsBatteryGroupData(params)
        .then((res) => {
          this.loading = false
          if (res.code == 200) {
            this.batteryGroupList = res.data ? res.data : []
          } else {
            this.batteryGroupList = []
          }
        })
        .catch((err) => {
          this.loading = false
        })
    },
    getUpsBatteryChartsData() {
      let { projectCode, surveyCode } = this.searchForm
      let params = {
        projectCode: projectCode,
        surveyCode: surveyCode
      }
      this.$api.upsBatteryChartsData(params).then((res) => {
        if (res.code == 200) {
          res.data.forEach((item) => {
            if (item.paramId === 100737) {
              // 电压
              this.voltage = item.paramIds
            } else if (item.paramId === 100739) {
              // 温度
              this.temperature = item.paramIds
            } else if (item.paramId === 100741) {
              // 内阻
              this.internalResistance = item.paramIds
            }
          })
          this.$nextTick(() => {})
          this.setBarCharts(this.temperature, 'temperature')
          this.setBarCharts(this.voltage, 'voltage')
          this.setBarCharts(this.internalResistance, 'internalResistance')
        }
      })
    },
    setBarCharts(chartData, domId) {
      let options
      let nameList = chartData.map((item) => item.surveyName)
      let valueList = chartData.map((item) => (item.value == null ? 0 : item.value))
      const getchart = echarts.init(document.getElementById(domId))
      if (chartData.length) {
        options = {
          tooltip: {
            trigger: 'axis'
          },
          grid: {
            top: '5%',
            left: '1%',
            right: '1%',
            bottom: '6%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: nameList,
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            }
          },
          yAxis: [
            {
              type: 'value',
              axisLine: {
                show: false
              },
              axisTick: {
                show: false
              }
            }
          ],
          series: [
            {
              data: valueList,
              type: 'bar',
              symbol: 'circle',
              symbolSize: 6,
              itemStyle: {
                color: '#FF9435'
              },
              lineStyle: {
                color: '#FF9435'
              }
            }
          ]
        }
      } else {
        options = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      getchart.clear()
      getchart.setOption(options)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    goDetail(row) {
      let params = {
        surveyName: row.surveyName,
        surveyCode: row.surveyCode
      }
      this.$router.push({
        path: 'batteryDetail',
        query: params
      })
      console.log(row)
    }
  }
}
</script>
<style lang="scss" scoped>
.warn-history {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 0 16px 10px 16px;
  .wholeTable-main {
    width: 100%;
    height: 100%;
    overflow: auto;
    .title-tabs {
      width: 100%;
      text-align: right;
      margin-bottom: 16px;
    }
    .batteryContent {
      width: 100%;
      height: 100%;
      display: flex;
      flex-wrap: wrap;
      .batteryItem {
        width: 240px;
        height: 100px;
        margin: 8px;
        background: #faf9fc;
        border-radius: 4px;
        padding: 16px;
      }
    }
    .chartsContent {
      height: 240px;
      font-weight: bold;
      font-size: 16px;
      #temperature,
      #voltage,
      #internalResistance {
        margin: 8px 0;
        height: calc(100% - 36px);
      }
    }
  }
}
::v-deep .tableLineLink {
  color: #3562db;
  cursor: pointer;
}
</style>
