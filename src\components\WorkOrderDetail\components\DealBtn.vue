<template>
  <div class="footer-btn">
    <div v-if="data.type === '1' || data.type === ''">
      <!-- 未处理 -->
      <div v-if="data.olgTaskManagement.flowcode === '1'">
        <el-button type="primary" @click="taskDetailPlaceOrder()">处理</el-button>
        <el-button type="primary" @click="dealBtnEvent('taskDetailCancelFlag')">取消</el-button>
      </div>
      <!-- 已受理 -->
      <div v-if="data.olgTaskManagement.flowcode === '2'">
        <el-button v-if="data.olgTaskManagement.workTypeCode != '16'" type="primary" @click="dealBtnEvent('taskDetailReturnVisitFlag', '督办')">督办</el-button>
        <el-button type="primary" @click="dealBtnEvent('taskDetailTransferFlag')">转派</el-button>
        <el-button type="primary" @click="dealBtnEvent('taskDetailToPersonFlag')">指派人员</el-button>
        <el-button v-if="data.olgTaskManagement.workTypeCode != '16'" type="primary" @click="dealBtnEvent('taskDetailEntryOrderFlag')">挂单</el-button>
        <el-button v-if="data.olgTaskManagement.workTypeCode != '16'" type="primary" @click="dealBtnEvent('taskDetailCancelFlag')">取消</el-button>
        <el-button v-if="data.olgTaskManagement.workTypeCode != '16'" type="primary" @click="dealBtnEvent('taskDetailUpdateOrderFlag')">修改</el-button>
      </div>
      <!-- 已派工 -->
      <div v-if="data.olgTaskManagement.flowcode === '3'">
        <el-button v-if="data.olgTaskManagement.workTypeCode != '16'" type="primary" @click="dealBtnEvent('taskDetailReturnVisitFlag', '督办')">督办</el-button>
        <el-button type="primary" @click="dealBtnEvent('taskDetailTransferFlag')">转派</el-button>
        <el-button type="primary" @click="dealBtnEvent('taskDetailToPersonFlag')">指派人员</el-button>
        <el-button v-if="data.olgTaskManagement.workTypeCode != '16'" type="primary" @click="dealBtnEvent('taskDetailEntryOrderFlag')">挂单</el-button>
        <el-button type="primary" @click="dealBtnEvent('taskDetailFinishOrderFlag')">完工</el-button>
        <el-button type="primary" @click="taskDetailDoPrint()">打印</el-button>
        <el-button v-if="data.olgTaskManagement.workTypeCode != '16'" type="primary" @click="dealBtnEvent('taskDetailCancelFlag')">取消</el-button>
        <el-button v-if="data.olgTaskManagement.workTypeCode != '16'" type="primary" @click="dealBtnEvent('taskDetailUpdateOrderFlag')">修改</el-button>
      </div>
      <!-- 已挂单 -->
      <div v-if="data.olgTaskManagement.flowcode === '4'">
        <el-button v-if="data.olgTaskManagement.workTypeCode != '16'" type="primary" @click="dealBtnEvent('taskDetailReturnVisitFlag', '督办')">督办</el-button>
        <el-button type="primary" @click="dealBtnEvent('taskDetailTransferFlag')">转派</el-button>
        <el-button type="primary" @click="dealBtnEvent('taskDetailToPersonFlag')">指派人员</el-button>
        <el-button type="primary" @click="dealBtnEvent('taskDetailFinishOrderFlag')">完工</el-button>
        <el-button type="primary" @click="taskDetailDoPrint()">打印</el-button>
        <el-button v-if="data.olgTaskManagement.workTypeCode != '16'" type="primary" @click="dealBtnEvent('taskDetailCancelFlag')">取消</el-button>
        <el-button v-if="data.olgTaskManagement.workTypeCode != '16'" type="primary" @click="dealBtnEvent('taskDetailUpdateOrderFlag')">修改</el-button>
      </div>
      <!-- 已完工 -->
      <div v-if="data.olgTaskManagement.flowcode === '5'">
        <el-button v-if="data.olgTaskManagement.workTypeCode != '16'" type="primary" @click="dealBtnEvent('taskDetailReturnVisitFlag', '回访')">回访</el-button>
        <el-button v-if="data.olgTaskManagement.workTypeCode != '16'" type="primary" @click="dealBtnEvent('taskDetailDegreeFlag')">评价</el-button>
        <el-button type="primary" @click="taskDetailDoPrint()">打印</el-button>
        <el-button v-if="data.olgTaskManagement.workTypeCode != '16'" type="primary" @click="dealBtnEvent('taskDetailUpdateOrderFlag')">修改</el-button>
      </div>
    </div>
    <div v-if="data.type === '2'">
      <el-button v-if="data.olgTaskManagement.workTypeCode != '16'" type="primary" @click="dealBtnEvent('taskDetailReturnVisitFlag', '回复')">回复</el-button>
    </div>
    <DealFormDialog
      v-if="dealFormShow"
      :visible="dealFormShow"
      :showBtnType="showBtnType"
      :dialogDetail="data"
      :returnVisitName="returnVisitName"
      :projectCode="alarmDetail.projectCode"
      @submit="btnTypeSubmit"
      @closeDialog="btnTypeClose"
    />
    <!-- 处理工单 -->
    <template v-if="workOrderDealShow">
      <CreatedWorkOrder
        :workOrderDealShow.sync="workOrderDealShow"
        :workTypeCode="data.olgTaskManagement.workTypeCode"
        :workTypeName="data.olgTaskManagement.workTypeName"
        :workTypeId="data.olgTaskManagement.id"
        :projectCode="alarmDetail.projectCode"
        :spaceId="alarmDetail.alarmSpaceId"
        :alarmId="alarmDetail.alarmId"
        dealType="deal"
        @workOrderSure="workOrderSure"
      ></CreatedWorkOrder>
    </template>
    <iframe id="printTemplate" ref="iframeDom" hidden name="printTemplate" src="./printTemplate.html" frameborder="0"></iframe>
    <!-- 打印工单区域 start -->
    <div v-show="false">
      打印内容
      <div id="pagec3">
        <div class="table-content">
          <div class="table-title title-1">{{ data.hospitalFullName }}</div>
          <div class="table-title title-2">后勤维修工作任务单</div>
          <div class="repairNum">
            <span>维修单号</span>
            <span>{{ data.olgTaskManagement.workNum }}</span>
          </div>
          <div class="table-up-box">
            <div>
              <span>接报员</span>
              <span>{{ data.olgTaskManagement.createByName ? data.olgTaskManagement.createByName.slice(0, 4) : '' }}</span>
            </div>
            <div>
              <span>任务分派</span>
              <span>{{ data.olgTaskManagement.designateDeptName ? data.olgTaskManagement.designateDeptName.slice(0, 6) : '' }}</span>
            </div>
            <div>
              <span>维修员</span>
              <span>{{ data.olgTaskManagement.designatePersonName ? data.olgTaskManagement.designatePersonName.slice(0, 4) : '' }}</span>
            </div>
            <div>
              <span>派工时间</span>
              <span>{{ data.olgTaskManagement.createDate }}</span>
            </div>
            <div>
              <span>完工时间</span>
              <span>{{ data.olgTaskManagement.wgTime }}</span>
            </div>
          </div>
          <table>
            <tr>
              <td colspan="1" style="max-width: 80px; width: 80px">报修人</td>
              <td colspan="2" style="max-width: 160px; width: 160px">{{ data.olgTaskManagement.callerName }}</td>
              <td colspan="1" style="max-width: 80px; width: 80px">报修工号</td>
              <td colspan="2" style="max-width: 160px; width: 160px">{{ data.olgTaskManagement.createByNo }}</td>
              <td colspan="1" style="max-width: 80px; width: 80px">联系电话</td>
              <td colspan="2" style="max-width: 160px; width: 160px">{{ data.olgTaskManagement.sourcesPhone }}</td>
            </tr>
            <tr>
              <td colspan="1" style="max-width: 80px; width: 80px">报修科室</td>
              <td colspan="2" style="max-width: 160px; width: 160px">{{ data.olgTaskManagement.sourcesDeptName }}</td>
              <td colspan="1" style="max-width: 80px; width: 160px">地 点</td>
              <td colspan="5" style="max-width: 400px; width: 400px">{{ data.olgTaskManagement.localtionNames }}</td>
            </tr>
            <tr>
              <td colspan="1" style="max-width: 80px; width: 80px">故障描述</td>
              <td colspan="6" style="max-width: 480px; width: 480px"></td>
              <td colspan="1" style="max-width: 80px; width: 80px">维修数量</td>
              <td colspan="1" style="max-width: 80px; width: 80px"></td>
            </tr>
            <tr>
              <td colspan="1" style="max-width: 80px; width: 80px">备注</td>
              <td colspan="8" style="max-width: 640px; width: 640px">{{ data.olgTaskManagement.questionDescription }}</td>
            </tr>
            <tr>
              <td colspan="2" style="max-width: 160px; width: 160px">维修材料记录</td>
              <td colspan="1" style="max-width: 80px; width: 80px">单位</td>
              <td colspan="1" style="max-width: 80px; width: 80px">数量</td>
              <td colspan="1" style="max-width: 80px; width: 80px">单价</td>
              <td colspan="1" style="max-width: 80px; width: 80px">总价</td>
              <td colspan="3" style="max-width: 160px; width: 160px">维修说明</td>
            </tr>
            <tr>
              <td colspan="2">{{ consumablesData[0] ? consumablesData[0].depName : '' }}</td>
              <td colspan="1">{{ consumablesData[0] ? consumablesData[0].specification : '' }}</td>
              <td colspan="1">{{ consumablesData[0] ? consumablesData[0].outNum : '' }}</td>
              <td colspan="1">{{ consumablesData[0] ? consumablesData[0].price : '' }}</td>
              <td colspan="1">{{ consumablesData[0] ? consumablesData[0].totalPrice : '' }}</td>
              <td colspan="3"></td>
            </tr>
            <tr>
              <td colspan="2">{{ consumablesData[1] ? consumablesData[1].depName : '' }}</td>
              <td colspan="1">{{ consumablesData[1] ? consumablesData[1].specification : '' }}</td>
              <td colspan="1">{{ consumablesData[1] ? consumablesData[1].outNum : '' }}</td>
              <td colspan="1">{{ consumablesData[1] ? consumablesData[1].price : '' }}</td>
              <td colspan="1">{{ consumablesData[1] ? consumablesData[1].totalPrice : '' }}</td>
              <td colspan="3"></td>
            </tr>
            <tr>
              <td colspan="2">{{ consumablesData[2] ? consumablesData[2].depName : '' }}</td>
              <td colspan="1">{{ consumablesData[2] ? consumablesData[2].specification : '' }}</td>
              <td colspan="1">{{ consumablesData[2] ? consumablesData[2].outNum : '' }}</td>
              <td colspan="1">{{ consumablesData[2] ? consumablesData[2].price : '' }}</td>
              <td colspan="1">{{ consumablesData[2] ? consumablesData[2].totalPrice : '' }}</td>
              <td colspan="3"></td>
            </tr>
            <tr>
              <td colspan="2">{{ consumablesData[3] ? consumablesData[3].depName : '' }}</td>
              <td colspan="1">{{ consumablesData[3] ? consumablesData[3].specification : '' }}</td>
              <td colspan="1">{{ consumablesData[3] ? consumablesData[3].outNum : '' }}</td>
              <td colspan="1">{{ consumablesData[3] ? consumablesData[3].price : '' }}</td>
              <td colspan="1">{{ consumablesData[3] ? consumablesData[3].totalPrice : '' }}</td>
              <td colspan="3"></td>
            </tr>
            <tr>
              <td colspan="5">
                <input type="checkbox" />很满意&emsp; <input type="checkbox" />满意&emsp; <input type="checkbox" />一般&emsp; <input type="checkbox" />差&emsp;
                <input type="checkbox" />很差&emsp;
              </td>
              <td>科室确认</td>
              <td colspan="3"></td>
            </tr>
          </table>
          <div class="remark">
            备注：因所用材料要纳入科室经济成本核算，请维修人员如实填写。病区确认请认真审核，多余空格请划除。<br />
            &emsp;&emsp;&emsp;第一联一站式后勤服务中心保留，第二联维修班组保留，第三联保修部门保留
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import $ from 'jquery'
import moment from 'moment'
export default {
  name: 'DealBtn',
  components: {
    DealFormDialog: () => import('./dealFormDialog')
  },
  props: {
    data: {
      type: Object,
      default: function () {
        return {}
      }
    },
    alarmDetail: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  data() {
    return {
      consumablesData: [],
      workOrderDealShow: false, // 工单处理弹窗
      dealFormShow: false, // 操作按钮弹窗
      showBtnType: '', // 操作按钮类型
      returnVisitName: '' // 督办回访回复判断
    }
  },
  mounted() {},
  methods: {
    // 处理 打开处理工单弹窗
    taskDetailPlaceOrder() {
      this.workOrderDealShow = true
    },
    dealBtnEvent(type, returnVisitName) {
      this.showBtnType = type
      // 督办 回访 回复
      if (type === 'taskDetailReturnVisitFlag') {
        this.returnVisitName = returnVisitName
      }
      this.dealFormShow = true
    },
    // 处理工单保存返回
    workOrderSure(item) {
      this.workOrderDealShow = false
    },
    // 打印
    taskDetailDoPrint() {
      this.doPrintOperator()
    },
    // 打印
    doPrintOperator() {
      var content = ''
      // var str = document.getElementById('pagec1').innerHTML // 获取需要打印的页面元素 ，page1元素设置样式page-break-after:always，意思是从下一行开始分割。
      // content = content + str
      // str = document.getElementById('pagec2').innerHTML // 获取需要打印的页面元素
      // content = content + str
      var str = document.getElementById('pagec3').innerHTML
      content += str
      var styleCode = `<style>
        * {
      margin: 0;
      padding: 0;
    }
    .table-title {
      text-align: center;
    }
    .table-content {
        padding-top: 24px;
    }
    .title-1 {
        font-size: 30px;
    }
    .title-2 {
        font-size: 24px;
        letter-spacing: 3px;
    }
    .table-up-box {
        display: flex;
        justify-content: center;
    }
    .table-up-box>div {
        margin: 0 5px;
    }
    .remark {
        width: 70%;
        margin: 0 auto;
    }
    .repairNum {
        padding-left: 15vw;
    }
    table,td {
        border: 1px solid #000;
        border-collapse: collapse;
    }
    td {
        padding: 6px;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
    }
    tr {
        height: 36px;
    }
    table {
        margin: 0 auto;
        margin-top: 5px;
        table-layout: fixed;
    }
        </style>`
      $('#printTemplate').contents().find('body').html(content)
      $('#printTemplate').contents().find('head').append(styleCode)
      window.frames['printTemplate'].print() // eslint-disable-line dot-notation
    },
    dateToString(date) {
      return moment(date).format('YYYY-MM-DD HH:mm:ss')
    },
    // 操作按钮提交返回
    btnTypeSubmit(type) {
      this.btnTypeClose()
      this.$emit('submit')
    },
    // 操作按钮关闭返回
    btnTypeClose() {
      this.dealFormShow = false
    }
  }
}
</script>
