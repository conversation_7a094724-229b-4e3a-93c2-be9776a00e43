<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="positionManage-content-title" @click="$router.go(-1)"><i class="el-icon-arrow-left"></i> {{ queryParams.type === 'add' ? '新增岗位' : '编辑岗位' }}</div>
      <div class="content_box">
        <el-form ref="formRef" :model="formModel" :rules="rules" label-width="auto">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="上级岗位" prop="parentPostCode">
                <el-cascader
                  v-if="queryParams.type !== 'view'"
                  ref="parentPostCode"
                  v-model="formModel.parentPostCode"
                  :disabled="queryParams.type === 'edit'"
                  :props="propsType"
                  :options="parentPostOptions"
                  placeholder="请选择上级岗位"
                  @change="parentPostCodeChange"
                />
                <span v-else class="detailContent">{{ formModel.parentPostName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="岗位名称" prop="name">
                <el-input v-if="queryParams.type !== 'view'" v-model="formModel.name" placeholder="请输入" maxlength="50"> </el-input>
                <span v-else class="detailContent">{{ formModel.name }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="年龄要求">
                <div v-if="queryParams.type !== 'view'">
                  <el-input
                    v-model="formModel.ageMin"
                    :maxlength="2"
                    placeholder="请输入"
                    style="width: 40%"
                    size="small"
                    onkeyup="value=value.replace(/[^\d.]/g, '')"
                    @change="minAgeChange"
                  >
                    <template v-slot:append>周岁</template>
                  </el-input>
                  -
                  <el-input
                    v-model="formModel.ageMax"
                    :maxlength="2"
                    placeholder="请输入"
                    style="width: 40%"
                    size="small"
                    onkeyup="value=value.replace(/[^\d.]/g, '')"
                    @change="maxAgeIChange"
                  >
                    <template v-slot:append>周岁</template>
                  </el-input>
                </div>
                <span v-else class="detailContent">{{ formModel.ageMin }}-{{ formModel.ageMax }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="学历要求">
                <el-select v-if="queryParams.type !== 'view'" v-model="formModel.educationalDictId" filterable multiple @change="educationChange">
                  <el-option v-for="item of educationOptions" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
                <span v-else class="detailContent">{{ formModel.educationalDictName }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="证书要求">
                <el-select v-if="queryParams.type !== 'view'" v-model="formModel.certificateDictId" filterable multiple @change="certificateChange">
                  <el-option v-for="item of certificateOptions" :key="item.id" :value="item.id" :label="item.name" clearable></el-option>
                </el-select>
                <span v-else class="detailContent">{{ formModel.certificateDictName }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="16">
              <el-form-item label="岗位职责">
                <el-input v-if="queryParams.type !== 'view'" v-model="formModel.postDuty" type="textarea" maxlength="500" show-word-limit placeholder="请输入"></el-input>
                <span v-else class="detailContent">{{ formModel.postDuty }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="权力范围">
                <el-input v-if="queryParams.type !== 'view'" v-model="formModel.scopeOfRights" type="textarea" maxlength="500" show-word-limit placeholder="请输入"></el-input>
                <span v-else class="detailContent">{{ formModel.scopeOfRights }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 岗位SOP -->
          <el-row v-if="queryParams.type !== 'view'">
            <el-col :span="24">
              <div class="sop-section">
                <div class="section-header">
                  <div class="section-title">岗位SOP</div>
                  <el-button v-if="queryParams.type !== 'view'" type="primary" size="small" @click="openSopDialog">添加</el-button>
                </div>
                <el-table :data="sopList" border style="width: 100%">
                  <el-table-column prop="sopName" label="名称" min-width="120" show-overflow-tooltip></el-table-column>
                  <el-table-column prop="sopTitle" label="标题" min-width="120" show-overflow-tooltip></el-table-column>
                  <el-table-column prop="remark" label="备注" min-width="160" show-overflow-tooltip></el-table-column>
                  <el-table-column prop="status" label="状态" min-width="80">
                    <template slot-scope="scope">
                      <span :class="scope.row.status === 1 ? 'status-active' : 'status-disabled'">
                        {{ scope.row.status === 1 ? '启用' : '停用' }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="100" fixed="right">
                    <template slot-scope="scope">
                      <el-button type="text" size="small" style="color: #ff4d4f" @click="removeSop(scope.row, scope.$index)">移除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <!-- SOP选择弹窗 -->
      <el-dialog title="请选择" :visible.sync="sopDialogVisible" width="50%" class="sop-dialog" :close-on-click-modal="false">
        <div class="search-container">
          <el-input v-model="searchKeyword" placeholder="搜索SOP名称、标题" class="search-input" prefix-icon="el-icon-search"> </el-input>
          <el-button icon="el-icon-search" type="primary" @click="searchSop">搜索</el-button>
          <el-button icon="el-icon-refresh" type="primary" plain @click="resetSearch">重置</el-button>
        </div>
        <el-table ref="sopSelectTable" :data="sopSelectList" border height="400px" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column prop="sopName" label="SOP名称" min-width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="sopTitle" label="标题" min-width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="remark" label="备注" min-width="160" show-overflow-tooltip></el-table-column>
        </el-table>
        <div class="pagination-container">
          <el-pagination
            :current-page="pageParams.pageNum"
            :page-sizes="[15, 20, 50, 100]"
            :page-size="pageParams.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            class="pagination-box"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          >
          </el-pagination>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="sopDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmSelect">确定</el-button>
        </span>
      </el-dialog>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">取消</el-button>
      <el-button v-if="queryParams.type !== 'view'" type="primary" :loading="formLoading" @click="submitForm">保存</el-button>
    </div>
  </PageContainer>
</template>
<script>
export default {
  name: 'addPosition',
  data() {
    return {
      // 正常表单
      formModel: {
        parentPostCode: '', // 上级岗位id
        parentPostCodes: '', // 上级岗位ids
        parentPostName: '', // 上级岗位名称
        educationalDictId: '', // 学历要求id
        ageMin: null, // 最小年龄
        ageMax: null, // 最大年龄
        educationalDictName: '', // 学历要求名称
        certificateDictId: '', // 证书要求id
        certificateDictName: '', // 证书要求name
        name: '', // 岗位名称
        postDuty: '', // 岗位职责
        scopeOfRights: '' // 权力范围
      },
      rules: {
        name: [{ required: true, message: '请输入岗位名称', trigger: 'blur' }]
      },
      propsType: {
        children: 'child',
        label: 'name',
        value: 'code',
        checkStrictly: true,
        multiple: false
      },
      parentPostOptions: [], // 上级岗位
      educationOptions: [], // 学历要求
      certificateOptions: [], // 证书下拉
      formLoading: false, // 表单loading
      queryParams: {},
      // 岗位SOP相关
      sopList: [], // 已选择的SOP列表
      sopDialogVisible: false, // SOP选择弹窗
      sopSelectList: [], // SOP选择列表
      selectedSops: [], // 已在弹窗中选择的SOP
      searchKeyword: '', // 搜索关键词
      pageParams: {
        pageNum: 1,
        pageSize: 15
      },
      total: 0 // 总记录数
    }
  },
  created() {
    this.queryParams = this.$route.query
    this.init()
  },
  methods: {
    // 初始化
    init() {
      this.getCertificateData()
      this.getParentCodeData()
      this.getDictListFn()
      // 编辑：获取数据
      if (!this.queryParams.code) return
      this.getDetailData()
    },
    // 获取详情数据
    getDetailData() {
      this.$api.supplierAssess
        .getPostById({ code: this.queryParams.code })
        .then((res) => {
          const { code, data } = res
          if (code == 200) {
            this.formModel = data
            this.formModel.educationalDictId = data.educationalDictId ? data.educationalDictId.split(',') : ''
            if (data.certificateDictId) {
              let certificateDictIdArr = data.certificateDictId ? data.certificateDictId.split(',') : ''
              let certificateDictIdNewArr = certificateDictIdArr.map((item) => Number(item))
              this.formModel.certificateDictId = certificateDictIdNewArr
            }
            // 获取岗位关联的SOP列表
            this.sopList = data.supPostSopList || []
          } else {
            this.$message.error(res.msg || '获取数据失败!')
          }
        })
        .catch((err) => {
          this.$message.error(err.msg || '获取数据失败!')
        })
    },
    // 获取上级岗位数据
    getParentCodeData() {
      this.$api.supplierAssess.getPostChildData().then((res) => {
        if (res.code == '200' && res.data.length > 0) {
          this.parentPostOptions = res.data
          if (this.queryParams.parentCode) {
            this.$api.supplierAssess.getPostById({ code: this.queryParams.parentCode }).then((res) => {
              const { code, data } = res
              if (code == 200) {
                this.formModel.parentPostCode = data.code
                this.formModel.parentPostName = data.name
                this.formModel.parentPostCodes = data.parentPostCodes ? data.parentPostCodes + ',' + data.code : data.code
              }
            })
          }
        }
      })
    },
    // 上级岗位改变
    parentPostCodeChange(val) {
      if (val && val.length) {
        this.formModel.parentPostCode = val[val.length - 1]
        let names = this.$refs.parentPostCode.getCheckedNodes()[0].pathLabels // 获取选中name
        this.formModel.parentPostCodes = val.join(',')
        this.formModel.parentPostName = names[names.length - 1]
      }
    },
    // 证书下拉
    getCertificateData() {
      this.$api.supplierAssess
        .getDictConfigUseData({
          type: 4,
          status: 1
        })
        .then((res) => {
          if (res.code == '200') {
            this.certificateOptions = res.data
          }
        })
    },
    getDictListFn() {
      // 文化程度
      this.$api.supplierAssess
        .getDictData({
          dictionaryCategoryId: 'EDUCATION_LEVEL_CATEGORY'
        })
        .then((res) => {
          if (res.code == 200) {
            this.educationOptions = res.data[0].children
          }
        })
    },
    // 最小年龄限制
    minAgeChange(val) {
      if (Number(val) > Number(this.formModel.ageMax) && Number(this.formModel.ageMax) != 0) {
        this.formModel.ageMin = null
      }
    },
    // 最大年龄限制
    maxAgeIChange(val) {
      if (Number(val) < Number(this.formModel.ageMin)) {
        this.formModel.ageMax = null
      }
    },
    // 学历获取name
    educationChange(val) {
      if (val && val.length) {
        const selectedLabels = val.map((el) => {
          return this.educationOptions.find((option) => option.id === el).name
        })
        this.formModel.educationalDictName = selectedLabels.join(',')
      }
    },
    // 证书获取name
    certificateChange(val) {
      if (val && val.length) {
        const selectedLabels = val.map((el) => {
          return this.certificateOptions.find((option) => option.id === el).name
        })
        this.formModel.certificateDictName = selectedLabels.join(',')
      }
    },
    // 岗位SOP相关方法
    // 打开SOP选择弹窗
    openSopDialog() {
      this.sopDialogVisible = true
      this.searchKeyword = ''
      this.pageParams.pageNum = 1
      this.getSopSelectList()
    },
    // 获取SOP选择列表
    getSopSelectList() {
      // 获取可选择的SOP列表，排除已选择的
      const params = {
        page: this.pageParams.pageNum,
        pageSize: this.pageParams.pageSize,
        queryKey: this.searchKeyword,
        status: '1', // 只显示启用的SOP
        selectIds: this.sopList.map((item) => item.id || item.sopId).join(',') // 排除已选择的SOP
      }
      this.$api.supplierAssess.querySupPostSopByPage(params).then((res) => {
        const { code, data, msg } = res
        if (code === '200') {
          const { records, total } = data
          this.sopSelectList = records
          this.total = total
        } else {
          this.$message.error(msg)
        }
      })
    },
    // 搜索SOP
    searchSop() {
      this.pageParams.pageNum = 1
      this.getSopSelectList()
    },
    // 重置搜索
    resetSearch() {
      this.searchKeyword = ''
      this.pageParams.pageNum = 1
      this.getSopSelectList()
    },
    // 处理表格选择变化
    handleSelectionChange(selection) {
      this.selectedSops = selection
    },
    // 分页大小变化
    handleSizeChange(val) {
      this.pageParams.pageSize = val
      this.getSopSelectList()
    },
    // 页码变化
    handleCurrentChange(val) {
      this.pageParams.pageNum = val
      this.getSopSelectList()
    },
    // 确认选择SOP
    confirmSelect() {
      if (this.selectedSops.length === 0) {
        this.$message.warning('请至少选择一个SOP')
        return
      }
      // 将选择的SOP添加到列表中，避免重复添加
      this.selectedSops.forEach((sop) => {
        if (!this.sopList.some((item) => item.id === sop.id)) {
          this.sopList.push(sop)
        }
      })
      this.sopDialogVisible = false
    },
    // 移除SOP
    removeSop(row, index) {
      this.$confirm('确定要移除该SOP吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.sopList.splice(index, 1)
          this.$message.success('移除成功')
        })
        .catch(() => {})
    },
    // 点击确定
    submitForm() {
      this.$refs['formRef'].validate((valid) => {
        if (valid) {
          this.formLoading = true
          const { code } = this.$route.query
          let params = {
            ...this.formModel
          }
          params.ageMax = params.ageMax ? Number(params.ageMax) : null
          params.ageMin = params.ageMin ? Number(params.ageMin) : null
          params.educationalDictId = params.educationalDictId.length ? params.educationalDictId.join(',') : ''
          params.certificateDictId = params.certificateDictId.length ? params.certificateDictId.join(',') : ''
          // 添加SOP IDs
          params.supPostSopList = this.sopList.map((item) => {
            return {
              sopId: item.id || item.sopId,
              sopName: item.sopName,
              sopTitle: item.sopTitle,
              remark: item.remark,
              status: item.status
            }
          })
          let fn = code ? this.$api.supplierAssess.updatePostData : this.$api.supplierAssess.insertPostData
          fn(params).then((res) => {
            this.formLoading = false
            if (res.code == 200) {
              this.$message.success(res.msg)
              setTimeout(() => {
                this.$router.go(-1)
              }, 500)
            } else {
              this.$message.error(res.msg)
            }
          })
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.container-content > div {
  background-color: #fff;
  height: 100%;
  position: relative;
  .positionManage-content-title {
    border-bottom: 1px solid #e4e7ed;
    padding: 12px 24px;
    cursor: pointer;
    margin-bottom: 24px;
  }
  .content_box {
    padding: 0 24px;
    background: #fff;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    height: 100%;
    .text-red {
      color: #ff1919;
    }
  }
  .el-input,
  .el-select,
  .el-cascader {
    width: 80%;
  }
}
.width100 {
  width: 100px !important;
}
/* 岗位SOP相关样式 */
.sop-section {
  margin: 20px 0;
  .section-header {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 15px;
    .section-title {
      font-size: 16px;
      font-weight: 500;
      margin-right: 10px;
    }
  }
}
/* 状态样式 */
.status-active {
  color: #67c23a;
}
.status-disabled {
  color: #f56c6c;
}
/* 弹窗样式 */
.sop-dialog {
  .search-container {
    display: flex;
    margin-bottom: 15px;
    .search-input {
      width: 280px;
      margin-right: 10px;
    }
  }
  .filter-tip {
    margin-top: 15px;
    .filter-box {
      border: 1px dashed #ffc0cb;
      background-color: #fff0f5;
      padding: 10px;
      color: #ff69b4;
      font-size: 14px;
      line-height: 1.8;
    }
  }
  .pagination-container {
    margin-top: 15px;
    .pagination-box {
      width: 100%;
    }
    .jump-text {
      margin: 0 5px;
    }
    .jump-input {
      width: 50px;
      margin: 0 5px;
    }
  }
}
/* 在全局样式添加 */
::v-deep .sop-dialog .el-dialog__body {
  padding: 15px 20px;
}
</style>
