<!-- 用电分析 -->
<template>
  <ContentCard
    :title="item.componentTitle + '（kwh）'"
    :scrollbarHover="true"
    :cstyle="{ height: '100%' }"
    class="drag_class"
    :hasMoreOper="['edit']"
    @more-oper-event="(val) => $emit('all-more-Oper', val, 'electricityAnalysis')"
  >
    <div slot="title-right" class="data-btns">
      <span v-for="item in typeList" :key="item.type" :class="{ 'active-btn': typeActive == item.type }" @click="changeType(item.type)">{{ item.name }}</span>
    </div>
    <div slot="content" class="analysis-main">
      <echarts ref="analysisChart" domId="analysisChart" />
    </div>
  </ContentCard>
</template>

<script>
import moment from 'moment'
moment.locale('zh-cn')
export default {
  name: 'electricityAnalysis',
  props: {
    item: {
      type: Object,
      default: () => {}
    },
    requestInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      typeList: [
        { name: '总用电量', type: 1 },
        { name: '分时段用电', type: 2 }
      ],
      typeActive: 0
    }
  },
  computed: {},
  watch: {
    '$store.state.settings.sidebarCollapse': {
      handler(val) {
        this.$nextTick(() => {
          this.echartsResize()
        })
      },
      deep: true
    },
    // dragSidebarCollapse
    '$store.state.settings.dragSidebarCollapse': {
      handler(val) {
        this.$nextTick(() => {
          this.echartsResize()
        })
      },
      deep: true
    },
    requestInfo: {
      handler(val, oldVal) {
        if (val.entityMenuCode != oldVal.entityMenuCode) {
          this.changeType(1)
        }
      },
      deep: true
    }
  },
  mounted() {
    this.changeType(1)
  },
  methods: {
    // 获取总用电量
    queryPowerAnalyseByTotal(params) {
      this.$api.QueryPowerAnalyseByTotal(params).then((res) => {
        if (res.code == 200) {
          this.$nextTick(() => {
            this.$refs.analysisChart.init(this.totalPowerChartData(res.data))
            this.echartsResize()
          })
        }
      })
    },
    // 总用电量图标
    totalPowerChartData(data) {
      let option
      if (data.length) {
        option = {
          tooltip: {
            trigger: 'axis'
          },
          legend: {
            x: 'center',
            y: '0',
            textStyle: {
              color: '#414653',
              fontSize: 12
            },
            data: ['今日用电量', '昨日用电量']
          },
          grid: {
            right: '5%',
            bottom: '5%',
            left: '5%',
            top: '40px',
            containLabel: true
          },
          dataZoom: [
            {
              type: 'inside',
              start: 0,
              end: 100
            }
          ],
          xAxis: [
            {
              type: 'time',
              axisLine: {
                lineStyle: {
                  color: '#E7EAEE'
                }
              },
              axisTick: {
                show: false
              },
              axisLabel: {
                show: true,
                textStyle: {
                  color: '#414653',
                  fontSize: 12
                }
              }
            }
          ],
          yAxis: [
            {
              type: 'value',
              position: 'left',
              axisLine: {
                show: false
              },
              splitLine: {
                lineStyle: {
                  color: '#E7EAEE'
                }
              },
              axisTick: {
                show: false
              },
              axisLabel: {
                color: '#414653',
                fontSize: 12
              }
            }
          ],
          series: [
            {
              name: '今日用电量',
              type: 'line',
              yAxisIndex: 0,
              symbolSize: 5,
              itemStyle: {
                color: '#FA5854'
              },
              lineStyle: {
                width: 1
              },
              data: data.map((item) => [item.todayTime, item.todayValue])
            },
            {
              name: '昨日用电量',
              type: 'line',
              yAxisIndex: 0,
              symbolSize: 5,
              itemStyle: {
                color: '#5E77D2'
              },
              lineStyle: {
                width: 1
              },
              data: data.map((item) => [item.todayTime, item.lastDayalue])
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      return option
    },
    // 获取分时段用电量
    queryPowerAnalyseByTime(params) {
      this.$api.QueryPowerAnalyseByTime(params).then((res) => {
        if (res.code == 200) {
          this.$nextTick(() => {
            this.$refs.analysisChart.init(this.daypartingPowerChartData(res.data))
            this.echartsResize()
          })
        }
      })
    },
    // 分时段用电量
    daypartingPowerChartData(data) {
      let newObj = {
        '尖峰': {valueKey: 'jianfeng'},
        '高峰': {valueKey: 'gaofeng'},
        '平时': {valueKey: 'pingshi'},
        '谷峰': {valueKey: 'gufeng'}
      }
      let total = 0
      let option
      if (data.length) {
        // 计算总数
        for (const key in newObj) {
          if (Object.hasOwnProperty.call(newObj, key)) {
            newObj[key].total = data.map(item => item[newObj[key].valueKey]).reduce((p, v) => {
              return p + v
            }, 0)
            total += newObj[key].total
          }
        }
        option = {
          legend: {
            x: 'center',
            y: '0',
            itemWidth: 12,
            itemHeight: 12,
            textStyle: {
              color: '#212121',
              fontSize: 12
            },
            data: Object.keys(newObj),
            formatter: (name) => {
              let item = newObj[name]
              return `${name}  ${item.total}kwh  ${((item.total ? item.total / total : 0) * 100).toFixed(2)}%`
            }
          },
          dataZoom: [
            {
              type: 'inside',
              start: 0,
              end: 100
            }
          ],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          grid: {
            right: '5%',
            bottom: '5%',
            left: '5%',
            top: '40px',
            containLabel: true
          },
          xAxis: {
            show: true,
            axisLabel: {
              // interval: 0,
              // rotate: -20,
              // margin: 30,
              textStyle: {
                align: 'center',
                color: '#414653'
              }
            },
            axisLine: {
              lineStyle: {
                color: '#E7EAEE'
              }
            },
            axisTick: {
              show: false
            },
            data: data.map(item => item.date)
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              textStyle: {
                color: '#414653'
              }
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              lineStyle: {
                color: '#E7EAEE'
              }
            }
          },
          series: [
            {
              type: 'bar',
              name: '尖峰',
              stack: '分时段用电',
              barWidth: 8,
              data: data.map(item => item.jianfeng),
              itemStyle: {
                normal: {
                  color: '#FF7D00'
                }
              }
            },
            {
              type: 'bar',
              name: '高峰',
              barWidth: 8,
              stack: '分时段用电',
              data: data.map(item => item.gaofeng),
              itemStyle: {
                normal: {
                  color: '#FFCF8B'
                }
              }
            },
            {
              type: 'bar',
              name: '平时',
              barWidth: 8,
              stack: '分时段用电',
              data: data.map(item => item.pingshi),
              itemStyle: {
                normal: {
                  color: '#3562DB'
                }
              }
            },
            {
              type: 'bar',
              name: '谷峰',
              barWidth: 8,
              stack: '分时段用电',
              data: data.map(item => item.gufeng),
              itemStyle: {
                normal: {
                  color: '#A0B8F6'
                }
              }
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      return option
    },
    echartsResize() {
      setTimeout(() => {
        if (this.$refs.analysisChart) {
          this.$refs.analysisChart.chartResize()
        }
      }, 250)
    },
    // 切换类型
    changeType(type) {
      this.typeActive = type
      let params = {
        currentDate: moment().format('YYYY-MM-DD HH:mm:ss'),
        ...this.requestInfo
      }
      if (type == 1) {
        this.queryPowerAnalyseByTotal(params)
      } else {
        this.queryPowerAnalyseByTime(params)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.analysis-main {
  width: 100%;
  height: 100%;
}

.data-btns {
  position: absolute;
  right: 36px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  & > span {
    height: 20px;
    line-height: 20px;
    padding: 0 8px;
    text-align: center;
    margin: 0 4px;
    background-color: #f6f5fa;
    font-size: 14px;
    font-family: "PingFang SC-Medium", "PingFang SC";
    border: 1px solid #ededf5;
    border-radius: 2px;
    color: #7f848c;
    cursor: pointer;
  }

  .active-btn {
    background-color: #3562db !important;
    color: #fff !important;
    border-color: #3562db !important;
  }
}
</style>
