<!-- 环境监测 -->
<template>
  <ContentCard :title="item.componentTitle" :scrollbarHover="true" :cstyle="{ height: '100%' }" class="drag_class"  :hasMoreOper="['more', 'edit']" @more-oper-event="(val) => $emit('all-more-Oper', val, 'envirMonitor')">
    <div slot="content" class="envir-list">
      <div v-for="item in envirData.slice(0, 5)" :key="item.paramName"  class="list-item">
        <p class="item-title">{{ item.paramName }}</p>
        <div class="item-value">
          <p>
            <span class="item-num" :style="{ color: item.name ? item.colour : '#121F3E' }">{{ item.number || '-' }}</span>
            <span class="item-unit">{{ item.paramUnit || '' }}</span>
          </p>
          <span v-if="item.name" class="item-info" :style="{ backgroundColor: item.colour }">{{ item.name }}</span>
        </div>
      </div>
    </div>
  </ContentCard>
</template>

<script>
export default {
  name: 'envirMonitor',
  props: {
    item: {
      type: Object,
      default: () => {}
    },
    requestInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      envirData: []
    }
  },
  computed: {

  },
  watch: {
    requestInfo: {
      handler(val, oldVal) {
        if (val.entityMenuCode != oldVal.entityMenuCode) {
          this.getEnvironmentMonitor()
        }
      },
      deep: true
    }
  },
  created() {
    this.getEnvironmentMonitor()
  },
  methods: {
    getEnvironmentMonitor() {
      this.$api.GetEnvironmentMonitor({easyOrInfo: 0, projectCode: this.requestInfo.projectCode, menuCode: this.requestInfo.entityMenuCode}).then((res) => {
        if (res.code == 200) {
          this.envirData = res.data
        }
      })
    }
  }
}

</script>

<style lang="scss" scoped>
.envir-list {
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;

  p {
    margin: 0;
  }

  .list-item {
    min-width: 225px;
    padding: 16px 24px;
    margin: 5px;
    background: #faf9fc;
    border-radius: 4px;

    .item-title {
      font-size: 14px;
      color: #414653;
      line-height: 14px;
    }

    .item-value {
      margin-top: 9px;
      display: flex;
      font-weight: 500;
      align-items: flex-end;

      .item-num {
        color: #121f3e;
        line-height: 26px;
        font-weight: bold;
      }

      .item-unit {
        font-size: 12px;
        color: #ccced3;
        margin-left: 4px;
      }

      .item-info {
        height: 20px;
        display: inline-block;
        font-size: 14px;
        color: #fff;
        line-height: 20px;
        padding: 0 6px;
        border-radius: 4px;
        margin-bottom: 4px;
        margin-left: 16px;
      }
    }
  }
}
</style>
