<!-- 盘点详情 -->
<template>
  <PageContainer :footer="true">
    <div slot="content" class="addApp-content">
      <div class="control">
        <div class="control_left">
          <div class="search-from">
            <el-input v-model="searchFrom.nameOrCode" placeholder="科室编码/科室名称" clearable style="width: 200px;"></el-input>
            <div style="display: inline-block;">
              <el-button type="primary" plain @click="resetForm">重置</el-button>
              <el-button type="primary" @click="searchForm">查询</el-button>
              <el-button :loading="exportLoading" type="primary" icon="el-icon-upload2" @click="exportData()">导出</el-button>
            </div>
          </div>
          <div class="left_table">
            <TablePage
              ref="tableDepartment"
              v-loading="tableLoading"
              :showPage="true"
              :tableColumn="tableColumn"
              :data="tableData"
              highlight-current-row
              height="calc(100% - 55px)"
              :pageData="pageData"
              :pageProps="pageProps"
              @pagination="paginationChange"
              @row-click="clickRow"
            />
          </div>
        </div>
        <div class="control_right">
          <div class="search-from">
            <div class="total_space">
              <img src="@/assets/images/icon_device_total.png" alt="">
              <div class="total_space_item">
                <span>总空间</span>
                <span>{{spaceNum.endTotal}}</span>
              </div>
            </div>
            <div class="type_num">
              <div class="type_num_item" style="background: rgba(219,0,27,.1);">
                <p>未确认</p>
                <span style="color: #D9001B;">{{ spaceNum.ing }}</span>
              </div>
              <div class="type_num_item" style="background: rgba(9,183,50,.1);">
                <p>已确认</p>
                <span>{{ spaceNum.noStart }}</span>
              </div>
            </div>
            <el-input v-model="searchFromA.nameOrCode" placeholder="空间编码/空间名称" clearable style="width: 200px;"></el-input>
            <div style="display: inline-block;">
              <el-button type="primary" plain @click="resetFormSpace">重置</el-button>
              <el-button type="primary" @click="searchFormSpace">查询</el-button>
            </div>
          </div>
          <div class="left_table">
            <TablePage
              ref="table"
              v-loading="tableLoadingA"
              :showPage="true"
              :tableColumn="tableColumnA"
              :data="tableDataA"
              height="calc(100% - 55px)"
              :pageData="pageDataA"
              :pageProps="pageProps"
              @pagination="paginationChangeA"
            />
          </div>
        </div>
      </div>

    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="() => { $router.go(-1) }">关闭</el-button>
    </div>
  </PageContainer>
</template>

<script lang="jsx">
export default {
  name: 'inventoryDetails',
  async beforeRouteLeave(to, from, next) {
    if (!['spaceInventory'].includes(to.name)) {
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      spaceNum: {

      },
      exportLoading: false,
      tableLoading: false,
      tableColumn: [],
      tableLoadingA: false,
      tableColumnA: [],
      pageData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      pageDataA: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      pageProps: {
        page: 'currentPage',
        pageSize: 'pageSize',
        total: 'total'
      },
      tableData: [],
      tableDataA: [],
      searchFrom: {
        nameOrCode: null
      },
      // 当前选中的科室id
      departId: null,
      searchFromA: {
        nameOrCode: null
      }
    }
  },
  computed: {

  },
  activated() {
    this.init()
  },
  mounted() {
    if (!this.$store.state.keepAlive.list.includes('spaceInventory')) {
      this.init()
    }
  },
  methods: {
    init() {
      Object.assign(this.$data, this.$options.data())
      this.tableColumn = [
        {
          prop: '',
          label: '序号',
          width: 80,
          formatter: (scope) => {
            return (this.pageData.currentPage - 1) * this.pageData.pageSize + scope.$index + 1
          }
        },
        {
          prop: 'departCode',
          width: 180,
          label: '科室编码'
        },
        {
          prop: 'departName',
          label: '科室名称'
        },
        {
          prop: 'schedule',
          label: '进度',
          render: (h, row) => {
            return (
              <el-progress percentage={row.row.schedule}></el-progress>
            )
          }
        }
      ]
      this.tableColumnA = [
        {
          prop: '',
          label: '序号',
          formatter: (scope) => {
            return (this.pageDataA.currentPage - 1) * this.pageDataA.pageSize + scope.$index + 1
          }
        },
        {
          prop: 'tenantName',
          label: '清查状态',
          render: (h, scope) => {
            return (
              scope.row.checkStatus == 1
                ? (
                  <span style="padding: 6px 10px;border-radius: 4px;color: #09B732;background:rgba(9,183,50,.1)">已确认</span>
                )
                : (
                  <span style="padding: 6px 10px;border-radius: 4px;color: #D9001B;background:rgba(217,0,27,.1)">未确认</span>
                )
            )
          }
        },
        // 清查结果，1-非科室归属，2-确认归属3，新登记
        {
          prop: 'checkResult',
          label: '清查结果',
          formatter: (scope) => {
            if (scope.row.checkResult == 1) {
              return '非科室归属'
            } else if (scope.row.checkResult == 2) {
              return '确认归属 '
            } else if (scope.row.checkResult == 3) {
              return '新登记'
            } else {
              return '-'
            }
          }
        },
        {
          prop: 'confirmTime',
          label: '确认时间'
        },
        {
          prop: 'confirmPerson',
          label: '确认人'
        },
        {
          prop: 'spaceCode',
          label: '空间编码'
        },
        {
          prop: 'spaceLocalName',
          label: '空间本地名称'
        },
        {
          prop: 'spaceLocalNo',
          label: '本地编码'
        },
        {
          prop: 'localtion',
          label: '位置'
        },
        {
          prop: 'spaceStatus',
          label: '空间状态'
        },
        {
          prop: 'spaceArea',
          width: 150,
          label: '建筑面积（㎡）'
        },
        {
          prop: 'functionType',
          label: '功能类型'
        },
        {
          prop: 'deptName',
          label: '归属部门'
        },
        {
          prop: 'responsibilityPerson',
          label: '空间责任人'
        }
      ]
      this.getQuerySpaceNum()
      this.getQueryDepartListByPage()
    },
    getQuerySpaceNum() {
      let data = {
        inventoryId: this.$route.query.id,
        flag: 1
      }
      this.$api.querySpaceNum(data).then(res => {
        if (res.code == 200) {
          this.spaceNum = res.data
        }
      })
    },
    clickRow(row, column, event) {
      this.pageDataA.currentPage = 1
      this.departId = row.id
      this.getQuerySpaceListPageByDepartId()
    },
    getQuerySpaceListPageByDepartId() {
      let data = {
        departId: this.departId,
        inventoryId: this.$route.query.id,
        ...this.searchFromA,
        pageParams: {
          currentPage: this.pageDataA.currentPage,
          pageSize: this.pageDataA.pageSize
        }
      }
      this.tableLoadingA = true
      this.$api.querySpaceListPageByDepartId(data).then(res => {
        if (res.code == 200) {
          this.tableDataA = res.data.records
          this.pageDataA.total = res.data.total
        }
        this.tableLoadingA = false
      }).catch(() => {
        this.tableLoadingA = false
      })
    },
    getQueryDepartListByPage() {
      let data = {
        ...this.searchFrom,
        inventoryId: this.$route.query.id,
        pageParams: {
          currentPage: this.pageData.currentPage,
          pageSize: this.pageData.pageSize
        }
      }
      this.tableLoading = true
      this.$api.queryDepartListByPage(data).then(res => {
        if (res.code == 200) {
          this.tableData = res.data.records
          this.pageData.total = res.data.total
          if (res.data.records.length) {
            this.departId = res.data.records[0].id
            this.getQuerySpaceListPageByDepartId()
            this.$refs.tableDepartment.setCurrentRow(res.data.records[0])
          }
        }
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getQueryDepartListByPage()
    },
    paginationChangeA(pagination) {
      Object.assign(this.pageData, pagination)
      this.getQuerySpaceListPageByDepartId()
    },
    resetFormSpace() {
      this.pageData.currentPage = 1
      this.searchFromA.nameOrCode = null
      this.getQuerySpaceListPageByDepartId()
    },
    searchFormSpace() {
      this.pageData.currentPage = 1
      this.getQuerySpaceListPageByDepartId()
    },
    searchForm() {
      this.pageData.currentPage = 1
      this.getQueryDepartListByPage()
    },
    resetForm() {
      this.searchFrom.nameOrCode = null
      this.pageData.currentPage = 1
      this.getQueryDepartListByPage()
    },
    exportData() {
      let param = {
        departId: this.departId,
        inventoryId: this.$route.query.id
      }
      this.exportLoading = true
      this.$api.exportSpaceInfo(param).then(res => {
        this.$tools.downloadFile(res)
        this.exportLoading = false
      }).catch(() => {
        this.exportLoading = false
      })
    }
  }
}

</script>

<style lang="scss" scoped>

.addApp-content {
  background: #fff;
  border-radius: 4px;
  height: 100%;
  padding: 10px;
  overflow-y: auto;
  .control{
    height: 100%;
    display: flex;
  }
  .control_left{
    width: 35%;
  }
  .control_right{
    width: 65%;
  }
  .control_left, .control_right {
    height: 100%;
    padding: 10px !important;
    .left_table{
      margin-top: 20px;
      height: calc(100% - 45px);
    }
    .state_sum{
      display: flex;
      height: calc(100% - 40%);
      margin: 10px 0;
      p{
        font-size: 18px;
        font-weight: 600;
        margin: 0;
        margin-right: 30px;
        span{
          color: black;
          font-size: 14px;
          margin-left: 5px;
        }
      }
    }
    .search-from {
      height: 45px;
      display: flex;
      align-items: flex-end;
      & > div {
        margin-right: 10px;
      }
      .total_space{
        display: flex;
        align-items: center;
        // padding: 16px 20px;
        padding: 8px 10px;
        background: #E6EFFC;
        img{
          margin-right: 16px;
          // width: 30px;
        }
        .total_space_item{
          span:first-child{
            // margin: 0;
            margin-right: 10px;
            color: #697484;
          }
          span:last-child{
            color: #5077DF;
            font-weight: 550;
          }
        }
      }
      .type_num{
        display: flex;
        &_item{
          display: flex;
          align-items: flex-end;
          margin-left: 20px;
          padding: 8px 10px;
          border-radius: 5px;

          p{
            margin: 0;
            margin-right: 10px;
          }
          span{
            font-weight: 550;
            font-size: 20px;
            line-height: 22px;
          }
        }
      }
    }
  }
}

</style>
