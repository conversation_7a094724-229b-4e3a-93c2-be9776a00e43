<template>
  <PageContainer>
    <div slot="header">
      <div class="searchForm">
        <el-input v-model="workNum" placeholder="工单号" style="width: 200px;"></el-input>
        <el-select v-model="flowCode" placeholder="工单状态">
          <el-option v-for="item in flowcodeOption" :key="item.label" :label="item.label" :value="item.value"> </el-option>
        </el-select>
        <el-select v-model="feedbackFlag" placeholder="跟踪状态">
          <el-option v-for="item in feedbackFlagOption" :key="item.label" :label="item.label" :value="item.value"> </el-option>
        </el-select>
        <el-select v-model="showTimeType" placeholder="申报时间">
          <el-option v-for="item in showTimeTypeOption" :key="item.value" :label="item.label" :value="item.value"> </el-option>
        </el-select>
        <el-date-picker
          v-show="showTimeType == '4'"
          v-model="dateVal"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd+HH:mm:ss"
        >
        </el-date-picker>
        <el-button type="primary" plain @click="resetForm">重置</el-button>
        <el-button type="primary" @click="searchForm">查询</el-button>
      </div>
    </div>
    <div slot="content" ref="contentRef">
      <div class="statistics">
        <div :class="['item', 'pointer-style', { 'hover-style': flowCode == '' }]" @click="changeFlowCode('')">
          <span>服务总量</span>
          <span>{{ countData.all }}</span>
        </div>
        <div :class="['item', 'pointer-style', { 'hover-style': flowCode == '5' }]" @click="changeFlowCode('5')">
          <span>已完工</span>
          <span>{{ countData.completed }}</span>
        </div>
        <div :class="['item', 'pointer-style', { 'hover-style': free1 == '30' }]" @click="changeFlowCode('30')">
          <span>未完工</span>
          <span>{{ countData.unfinishedwork }}</span>
        </div>
        <div :class="['item', 'pointer-style', { 'hover-style': flowCode == '6' }]" @click="changeFlowCode('6')">
          <span>已取消</span>
          <span>{{ countData.cancelled }}</span>
        </div>
        <div class="item pure">
          <span>完工率</span>
          <span>{{ countData.completionRate }}</span>
        </div>
        <div class="item pure">
          <span>平均响应</span>
          <span>{{ countData.response }}</span>
        </div>
        <div class="item pure">
          <span>平均接单</span>
          <span>{{ countData.avgOrderReceivingDate }}</span>
        </div>
        <div class="item pure">
          <span>平均完工</span>
          <span>{{ countData.finishTime }}</span>
        </div>
        <!-- <div class="item pure">
          <span>返修率</span>
          <span>{{ countData.repairWork }}</span>
        </div> -->
        <div class="item pure">
          <span>综合评价</span>
          <span>{{ countData.evaluate }}</span>
        </div>
        <div class="item pure">
          <span>回访率</span>
          <span>{{ countData.callBack }}</span>
        </div>
      </div>
      <el-table v-loading="tableLoading" :data="tableData" border style="width: 100%;" height="77%" @row-click="handleClick">
        <el-table-column label="序号" type="index" width="50" align="center" :resizable="false">
          <template slot-scope="scope">
            {{ (pageNo - 1) * pageSize + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="工单号" width="180" :resizable="false">
          <template slot-scope="scope">
            <span style="color: #3562db;">{{ scope.row.workNum }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="sourcesDeptName" label="所属科室" width="180" show-overflow-tooltip :resizable="false"> </el-table-column>
        <el-table-column label="服务起点" show-overflow-tooltip :resizable="false">
          <template slot-scope="scope">
            <span>{{ scope.row.olgTaskDetailList[0].transportStartLocalName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="服务终点" show-overflow-tooltip :resizable="false">
          <template slot-scope="scope">
            <span>{{ scope.row.olgTaskDetailList[0].transportEndLocalName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="transportName" label="服务事项" show-overflow-tooltip :resizable="false"></el-table-column>
        <el-table-column prop="designateDeptName" label="服务部门" show-overflow-tooltip :resizable="false"> </el-table-column>
        <el-table-column prop="designatePersonName" label="服务人员" show-overflow-tooltip :resizable="false"> </el-table-column>
        <el-table-column prop="designatePersonPhone" label="联系方式" show-overflow-tooltip :resizable="false"> </el-table-column>
        <el-table-column prop="questionDescription" label="说明" show-overflow-tooltip :resizable="false"> </el-table-column>
        <el-table-column label="工单状态" :resizable="false">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.flowcode == '1'" type="danger">未受理</el-tag>
            <el-tag v-else-if="scope.row.flowcode == '2'" type="danger">未派工</el-tag>
            <el-tag v-else-if="scope.row.flowcode == '3'" type="success">已派工</el-tag>
            <el-tag v-else-if="scope.row.flowcode == '4'" type="warning">已挂单</el-tag>
            <el-tag v-else-if="scope.row.flowcode == '5'" type="success">已完工</el-tag>
            <el-tag v-else-if="scope.row.flowcode == '15'" type="success">已验收</el-tag>
            <el-tag v-else-if="scope.row.flowcode == '6'" type="danger">已取消</el-tag>
            <el-tag v-else-if="scope.row.flowcode == '7'" type="info">暂存</el-tag>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="pageNo"
        :page-sizes="[15, 30, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
      </el-pagination>
      <template v-if="workOrderDetailCenterShow">
        <el-dialog :visible.sync="workOrderDetailCenterShow" custom-class="detailDialog main" :close-on-click-modal="false" :before-close="workOrderDetailCloseDialog">
          <template slot="title">
            <span class="dialog-title">{{ dialogTitle }}</span>
          </template>
          <workOrderDetailList :rowData="detailObj" />
        </el-dialog>
      </template>
    </div>
  </PageContainer>
</template>
<script>
import store from '@/store/index'
import workOrderDetailList from './components/workOrderDetailList.vue'
export default {
  name: 'WorkOrder',
  components: {
    workOrderDetailList
  },
  data() {
    return {
      workNum: '', // 工单号
      flowCode: '', // 工单状态
      feedbackFlag: '', // 跟踪状态
      showTimeType: '1', // 申报时间
      dateVal: '', // 自定义时间
      tableData: [], // 表格数据
      total: 0, // 总条数
      pageNo: 1, // 当前页
      pageSize: 15, // 每页条数
      tableLoading: false, // 表格加载状态
      flowcodeOption: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '未受理',
          value: '1'
        },
        {
          label: '暂存',
          value: '7'
        },
        {
          label: '未派工',
          value: '2'
        },
        {
          label: '已派工',
          value: '3'
        },
        {
          label: '已挂单',
          value: '4'
        },
        {
          label: '已完工',
          value: '5'
        },
        {
          label: '已取消',
          value: '6'
        }
      ],
      feedbackFlagOption: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '未督办',
          value: '2'
        },
        {
          label: '已督办',
          value: '3'
        },
        {
          label: '未回访',
          value: '0'
        },
        {
          label: '已回访',
          value: '1'
        },
        {
          label: '未回复',
          value: '5'
        },
        {
          label: '已回复',
          value: '4'
        },
        {
          label: '未验收',
          value: '6'
        },
        {
          label: '已验收',
          value: '7'
        }
      ],
      showTimeTypeOption: [
        {
          label: '本年',
          value: '3'
        },
        {
          label: '昨天',
          value: '5'
        },
        {
          label: '今天',
          value: '1'
        },
        {
          label: '本周',
          value: '6'
        },
        {
          label: '本月',
          value: '2'
        },
        {
          label: '其他',
          value: '4'
        }
      ],
      countData: {},
      workOrderDetailCenterShow: false,
      detailObj: {},
      dialogTitle: '',
      free1: ''
    }
  },
  created() {},
  mounted() {
    this.getList()
    this.getReckonCount()
  },
  methods: {
    resetForm() {
      this.workNum = ''
      this.flowCode = ''
      this.feedbackFlag = ''
      this.showTimeType = '1'
      this.dateVal = ''
      this.getList()
      this.getReckonCount()
    },
    searchForm() {
      this.free1 = ''
      this.getList()
      this.getReckonCount()
    },
    getList() {
      const userInfo = store.state.user.userInfo.user
      let params = {
        id: '',
        contrastType: '',
        free1: '',
        free2: '',
        workTypeCode: 3,
        workTypeName: 'YS',
        urgencyDegree: '',
        disDegree: '',
        disDegreeNew: '',
        workSources: '',
        feedbackFlag: this.feedbackFlag,
        responseTime: '',
        responseTimeType: '',
        transportTypeCode: '',
        showTimeType: this.showTimeType,
        typeSources: '',
        startTime: this.dateVal[0],
        endTime: this.dateVal[1],
        sectionStartDate: '',
        sectionEndDate: '',
        sectionStartTime: '',
        sectionEndTime: '',
        sourcesDept: '',
        sourcesDeptName: '',
        region: '',
        buliding: '',
        storey: '',
        room: '',
        localtionName: '',
        companyCode: '',
        designateDeptCode: '',
        designatePersonCode: '',
        replyToCode: '',
        replyToPeople: '',
        callerName: '',
        sourcesPhone: '',
        itemDetailCode: '',
        itemDetailName: '',
        itemTypeCode: '',
        itemTypeName: '',
        itemServiceCode: '',
        itemServiceName: '',
        itemList: '',
        questionDescription: '',
        workNum: this.workNum,
        restaurantId: '',
        restaurantName: '',
        haveStartTime: '',
        haveEndTime: '',
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        orderBy: '',
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
      if (this.free1 == '30') {
        params.free1 = this.free1
      } else {
        params.flowcode = this.flowCode
      }
      this.tableLoading = true
      this.$api.getWorkOrderDataList(params).then((res) => {
        this.tableLoading = false
        this.tableData = res.rows
        this.total = res.total
      })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.pageNo = val
      this.getList()
    },
    getReckonCount() {
      const userInfo = store.state.user.userInfo.user
      let params = {
        workNum: this.workNum,
        workTypeCode: 3,
        workTypeName: 'YS',
        feedbackFlag: this.feedbackFlag,
        showTimeType: this.showTimeType,
        startTime: this.dateVal[0],
        endTime: this.dateVal[1],
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
      if (this.free1 == '30') {
        params.free1 = this.free1
      } else {
        params.flowcode = this.flowCode
      }
      this.$api.getReckonCount(params).then((res) => {
        if (res.success) {
          this.countData = res.body.data
        }
      })
    },
    handleClick(row) {
      this.detailObj = row
      this.dialogTitle = `中央运送（${this.detailObj.flowtype}）`
      this.workOrderDetailCenterShow = true
    },
    workOrderDetailCloseDialog() {
      this.workOrderDetailCenterShow = false
    },
    changeFlowCode(val) {
      if (val == '30') {
        this.free1 = val
        this.flowCode = null
      } else {
        this.flowCode = val
        this.free1 = ''
      }
      this.getList()
      // this.getReckonCount()
    }
  }
}
</script>
<style lang="scss" scoped>
.searchForm {
  display: flex;
  height: 80px;
  padding: 0 16px;
  align-items: center;
  .el-input {
    width: 300px;
  }
  > div {
    margin-right: 20px;
  }
}
.container-content > div {
  height: 100%;
  background-color: #fff;
  padding: 16px;
  margin-top: 16px;
}
.el-table {
  margin-bottom: 12px;
}
::v-deep .el-date-editor .el-range__icon {
  line-height: 24px;
}
.statistics {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
  height: 78px;
}
.statistics .item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-around;
  flex: 1;
  background-color: rgb(53 98 219 / 6%);
  margin-right: 8px;
  border-radius: 4px;
}
.statistics .item span:nth-child(2) {
  font-size: 18px;
  color: #3562db;
  font-weight: 700;
}
.statistics .hover-style {
  background-color: #fff;
  box-shadow: 0 0 10px 0 rgb(32 56 114 / 13%);
  border-bottom: 3px solid #3562db;
}
.statistics .pointer-style {
  cursor: pointer;
}
.statistics .pure {
  background-color: #fff;
}
.statistics .pure span:nth-child(2) {
  color: #121f3e;
}
::v-deep .el-dialog {
  height: 80vh;
  margin-top: 10vh;
}
::v-deep .el-dialog__wrapper {
  overflow: hidden;
}
::v-deep .el-dialog__body {
  height: 93%;
}
::v-deep .el-table__row:hover {
  cursor: pointer;
}
</style>
