<template>
  <div class="form-config">
    <div class="form-config__top">
      <el-form ref="formRef" :model="searchForm" class="form-config__search" inline @submit.native.prevent="onSearch">
        <el-form-item prop="name">
          <el-input v-model="searchForm.name" clearable filterable placeholder="表单名称"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" plain @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
      <div class="form-config__actions">
        <el-button type="primary" icon="el-icon-plus" @click="onOperate('add')">新增类型 </el-button>
      </div>
    </div>
    <div class="form-config__table">
      <el-table v-loading="loadingStatus" height="100%" :data="tableData" border stripe table-layout="auto" class="tableAuto" row-key="id">
        <el-table-column label="序号" type="index" width="55" />
        <el-table-column label="所属类型" prop="assignmentType" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.assignmentType == 0 ? '作业' : '作业证' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="类型名称" prop="name" show-overflow-tooltip></el-table-column>
        <el-table-column label="类型编码" prop="code" width="150px"></el-table-column>
        <el-table-column label="适用流程" prop="flowModelName"></el-table-column>
        <el-table-column label="备注" prop="remark" show-overflow-tooltip :formatter="(row) => row.remark || '-'"></el-table-column>
        <el-table-column label="操作" width="150px">
          <template #default="{ row }">
            <el-button type="text" @click="onOperate('view', row)">查看</el-button>
            <el-button type="text" @click="onOperate('edit', row)">编辑</el-button>
            <el-dropdown @command="(command) => onOperate(command, row)">
              <el-button type="text" style="margin-left: 10px">更多</el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="'sync'">同步流程</el-dropdown-item>
                  <el-dropdown-item style="color: #ff1919" :command="'del'">删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-pagination
      class="form-config__pagination"
      :current-page="pagination.page"
      :page-sizes="pagination.pageSizeOptions"
      :page-size="pagination.size"
      :layout="pagination.layoutOptions"
      :total="pagination.total"
      @size-change="paginationSizeChange"
      @current-change="paginationCurrentChange"
    >
    </el-pagination>
    <!--表单基础信息 新增或者编辑-->
    <AddForm :visible.sync="dialog.baseVisible" @success="getDataList" @config="(id) => onOperate('edit', { id })"></AddForm>
    <!--表单详情 查看-->
    <FormDetail :id="dialog.id" :visible.sync="dialog.detailVisible" :readonly="dialog.detailReadonly" @success="getDataList"></FormDetail>
  </div>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin'
import { UsingStatusOptions } from '@/views/operationPort/constant'
export default {
  name: 'FormConfig',
  components: {
    FormDetail: () => import('./detail.vue'),
    AddForm: () => import('./add.vue')
  },
  filters: {
    statusFilter(status) {
      return UsingStatusOptions.find((it) => it.value === +status)?.label ?? '-'
    }
  },
  mixins: [tableListMixin],
  data: () => ({
    searchForm: {
      name: ''
    },
    tableData: [],
    loadingStatus: false,
    dialog: {
      // 基础表单
      baseVisible: false,
      detailVisible: false,
      detailReadonly: false,
      id: 0
    }
  }),
  mounted() {
    this.getDataList()
  },
  methods: {
    getDataList() {
      this.loadingStatus = true
      const params = {
        name: this.searchForm.name,
        pageSize: this.pagination.size,
        page: this.pagination.current
      }
      this.$api
        .businessFormList(params)
        .then((res) => {
          if (res.code === '200') {
            this.tableData = res.data.records
            this.pagination.total = res.data.total
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '查询失败'))
        .finally(() => (this.loadingStatus = false))
    },
    onSearch() {
      this.pagination.current = 1
      this.getDataList()
    },
    onReset() {
      this.$refs.formRef.resetFields()
      this.onSearch()
    },
    paginationSizeChange(val) {
      this.pagination.current = 1
      this.pagination.pageSize = val
      this.getDataList()
    },
    paginationCurrentChange(val) {
      this.pagination.current = val
      this.getDataList()
    },
    onOperate(type, row) {
      switch (type) {
        case 'del':
          if (+row.state === 1) {
            this.$message.error('启用的配置不允许删除！')
          } else {
            this.$confirm('删除后将无法恢复，是否确定删除？', '信息提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消'
            }).then(() => this.doDelete(row.id))
          }
          break
        case 'sync':
          this.doSync(row.id)
          break
        case 'add':
          this.dialog.baseVisible = true
          break
        default:
          this.dialog.detailVisible = true
          this.dialog.id = row?.id ?? ''
          this.dialog.detailReadonly = type === 'view'
          break
      }
    },
    // 删除业务表单配置
    doDelete(id) {
      this.$api
        .delBusinessForm({ id })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('删除成功')
            this.getDataList()
          } else {
            throw res.msg || '删除失败'
          }
        })
        .catch((msg) => msg && this.$message.error(msg))
        .finally(() => (this.loadingStatus = false))
    },
    // 同步工作流
    doSync(id) {
      this.$api
        .businessFormSync({ id })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('同步成功')
            this.getDataList()
          } else {
            throw res.msg || '同步失败'
          }
        })
        .catch((msg) => msg && this.$message.error(msg))
        .finally(() => (this.loadingStatus = false))
    }
  }
}
</script>
<style scoped lang="scss">
.form-config {
  display: flex;
  flex-flow: column nowrap;
  padding: 16px;
  &__top {
    display: flex;
    justify-content: space-between;
  }
  &__actions {
    line-height: 40px;
  }
  &__pagination {
    margin-top: 10px;
  }
  &__table {
    margin-top: -10px;
    flex: 1;
  }
  &__tag {
    // 停用
    &--0 {
      --color: #f64646;
    }
    // 启用
    &--1 {
      --color: #00b42a;
    }
    &:before {
      content: '';
      display: inline-block;
      vertical-align: middle;
      margin-right: 4px;
      height: 4px;
      width: 4px;
      border-radius: 4px;
      background-color: var(--color);
    }
  }
}
</style>
