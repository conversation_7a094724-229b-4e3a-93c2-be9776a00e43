<template>
  <PageContainer>
    <div slot="content" class="configuration-content">
      <div class="configuration-content-left">
        <div v-loading class="left_content">
          <el-input v-model="filterText" placeholder="输入关键字进行过滤" suffix-icon="el-icon-search" clearable> </el-input>
          <el-tree
            ref="tree"
            v-loading="treeLoading"
            style="margin-top: 10px"
            :data="treeData"
            :props="defaultProps"
            node-key="id"
            :highlight-current="true"
            :filter-node-method="filterNode"
            @node-click="handleNodeClick"
          >
          </el-tree>
        </div>
      </div>
      <div class="configuration-content-right">
        <div style="height: 100%">
          <div class="search-from">
            <div>
              <el-input v-model="filters.name" suffix-icon="el-icon-search" placeholder="搜索名称"></el-input>
            </div>
            <div>
              <el-button type="primary" plain class="ml-16" @click="resetForm">重置</el-button>
              <el-button type="primary" @click="searchForm">查询</el-button>
            </div>
          </div>
          <el-button type="primary" icon="el-icon-plus" @click="handleListEvent('add')">添加字典值</el-button>
          <div class="contentTable">
            <div class="contentTable-main table-content">
              <el-table
                v-loading="tableLoading"
                border
                style="width: 100%"
                height="calc(100% - 20px)"
                row-key="id"
                :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
                :data="tableData"
                stripe
              >
                <el-table-column prop="name" label="名称" show-overflow-tooltip width="140"></el-table-column>
                <el-table-column prop="code" label="编码" show-overflow-tooltip></el-table-column>
                <el-table-column prop="colour" label="颜色" show-overflow-tooltip width="200">
                  <template slot-scope="scope">
                    <div class="colorClass">
                      <el-color-picker v-model="scope.row.colour" disabled size="mini"></el-color-picker>
                      <span> {{ scope.row.colour }}</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="pictureUrl" label="图片" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <div v-if="scope.row.pictureUrl">
                      <img :src="$tools.imgUrlTranslation(scope.row.pictureUrl) || ''" alt="" style="width: 48px; height: 48px; cursor: pointer" @click="viewImage(scope.row)" />
                    </div>
                    <div v-else>-</div>
                  </template>
                </el-table-column>
                <el-table-column prop="parentName" label="上级字典" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span>{{ scope.row.parentName || '-' }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="status" show-overflow-tooltip label="状态">
                  <template slot-scope="scope">
                    <div v-if="scope.row.status === 1" class="status-color1"><span class="dot"></span><span>启用</span></div>
                    <div v-if="scope.row.status === 0" class="status-color2"><span class="dot"></span><span>停用</span></div>
                  </template>
                </el-table-column>
                <el-table-column prop="presetsType" label="预设" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span>{{ scope.row.presetsType === 1 ? '是' : '否' }}</span>
                  </template>
                </el-table-column>
                <el-table-column show-overflow-tooltip label="操作" width="200" align="center">
                  <template slot-scope="scope">
                    <el-button type="text" @click="handleListEvent('check', scope.row)">查看</el-button>
                    <el-button type="text" @click="handleListEvent('edit', scope.row)">编辑</el-button>
                    <el-dropdown @command="(val) => handleListEvent(val, scope.row)">
                      <span style="color: #3562db; margin-left: 10px; cursor: pointer"> 更多 <i class="el-icon-arrow-down"></i> </span>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item command="addSubordinate">新增下级</el-dropdown-item>
                        <el-dropdown-item v-if="scope.row.presetsType === 0" command="del" class="deleteItem">删除</el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="contentTable-footer">
              <el-pagination
                :current-page="pagination.pageNo"
                :page-sizes="pagination.pageSizeOptions"
                :page-size="pagination.pageSize"
                layout="total, sizes, prev, pager, next"
                :total="pageTotal"
                @size-change="paginationSizeChange"
                @current-change="paginationCurrentChange"
              >
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
      <el-dialog
        v-dialogDrag
        :visible.sync="dialogVisible"
        :before-close="dialogClosed"
        :modal="false"
        :close-on-click-modal="false"
        :title="diaTitle"
        width="40%"
        custom-class="model-dialog"
      >
        <div class="diaContent" style="padding: 10px">
          <el-form ref="formInline" :model="formInline" label-width="100px" :rules="rules" label-position="right">
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="上级字典">
                  <el-cascader
                    v-if="operationType !== 'detail'"
                    ref="alarmDictId"
                    v-model="formInline.parentId"
                    :props="propsType"
                    :disabled="operationType === 'edit'"
                    :options="alarmDictList"
                    style="width: 100%"
                    placeholder="请选择"
                    :show-all-levels="false"
                    @change="alarmDictIdChange"
                  ></el-cascader>
                  <span v-else>{{ detailInfo.parentName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="字典名称" prop="name">
                  <el-input v-if="operationType !== 'detail'" v-model="formInline.name" maxlength="50" placeholder="请输入字典名称"></el-input>
                  <span v-else>{{ detailInfo.name }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="字典编码" prop="code">
                  <el-input v-if="operationType !== 'detail'" v-model="formInline.code" maxlength="50" placeholder="请输入字典编码" :disabled="operationType === 'edit'"></el-input>
                  <span v-else>{{ detailInfo.code }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="颜色">
                  <el-input v-model="formInline.colour" readonly placeholder="请选择颜色" :disabled="operationType === 'detail'">
                    <el-color-picker slot="prepend" v-model="formInline.colour" size="small " :disabled="operationType === 'detail'"></el-color-picker>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="启用状态" prop="status">
                  <el-select v-if="operationType !== 'detail'" v-model="formInline.status" placeholder="请选择状态" style="width: 100%">
                    <el-option v-for="item in statusList" :key="item.value" :label="item.name" :value="item.value"></el-option>
                  </el-select>
                  <span v-else>{{ detailInfo.status === 0 ? '停用' : '启用' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="图片">
                  <el-upload
                    action=""
                    list-type="picture-card"
                    :class="{ hide: hideUpload }"
                    :disabled="operationType === 'detail'"
                    :file-list="fileList"
                    :accept="uploadAcceptDict['picture'].type"
                    :limit="1"
                    :before-upload="beforeAvatarUpload"
                    :on-preview="handlePictureCardPreview"
                    :http-request="httpRequset"
                    :on-remove="handleRemove"
                    :on-change="fileChange"
                  >
                    <i class="el-icon-plus"><br /></i>
                    <div slot="tip" class="el-upload__tip">支持{{ uploadAcceptDict['picture'].type }},且大小不超过{{ uploadAcceptDict['picture'].fileSize }}M</div>
                  </el-upload>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="dialogClosed">取 消</el-button>
          <el-button v-show="operationType !== 'detail'" type="primary" @click="submit('formInline')">确 定</el-button>
        </span>
      </el-dialog>
      <el-dialog :visible.sync="dialogImageVisible">
        <img width="100%" :src="dialogImageUrl" alt="" />
      </el-dialog>
    </div>
  </PageContainer>
</template>
<script>
import { uploadAcceptDict } from '@/util/dict.js'
export default {
  name: 'unifiedFieldConfiguration',
  data() {
    return {
      uploadAcceptDict,
      defaultProps: {
        label: 'name',
        value: 'value'
      },
      filterText: '',
      checkedData: {},
      treeData: [
        {
          id: '1',
          name: '报警类型'
        }
      ],
      tableLoading: false,
      treeLoading: false,
      dialogVisible: false,
      filters: {
        name: '', // 名称
        type: 1
      },
      propsType: {
        children: 'children',
        label: 'name',
        value: 'id',
        checkStrictly: true,
        multiple: false
      },
      formInline: {
        parentId: [], // 父级id
        parentIds: '', // 父级ID集合
        parentName: '', // 字典父级名称
        code: '', // 	报警字典编码
        name: '', // 报警字典名称
        colour: '', // 颜色
        pictureUrl: '', // 图片url
        status: '', // 状态 0-停用 1-启用
        type: 1 // 报警类型
      },
      pagination: {
        pageNo: 1,
        pageSize: 15
      },
      diaTitle: '',
      pageTotal: 0,
      configStatusList: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '已配置',
          value: '1'
        },
        {
          label: '未配置',
          value: '0'
        }
      ],
      statusList: [
        {
          name: '停用',
          value: 0
        },
        {
          name: '启用',
          value: 1
        }
      ],
      rules: {
        name: [{ required: true, message: '请输入字典名称', trigger: 'blur' }],
        code: [{ required: true, message: '请输入字典编码', trigger: 'blur' }],
        status: [{ required: true, message: '请选择状态', trigger: 'change' }]
      },
      tableData: [],
      multipleSelection: [],
      alarmDictList: [],
      operationType: '',
      fileList: [],
      rowId: '',
      hideUpload: false,
      detailInfo: {}, // 详情数据
      dialogImageUrl: '',
      dialogImageVisible: false
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  mounted() {
    this.getDataList()
  },
  methods: {
    // 过滤树
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    getDataList() {
      this.$nextTick(() => {
        this.$refs.tree.setCurrentKey('1')
      })
      let data = {
        ...this.pagination,
        ...this.filters
      }
      this.tableLoading = true
      this.$api.getAlarmDictConfigByPage(data).then((res) => {
        if (res.code == '200') {
          this.tableData = res.data.records
          this.pageTotal = res.data.total
        }
      })
      this.tableLoading = false
    },
    paginationSizeChange(val) {
      this.pagination.pageSize = val
      this.getDataList()
    },
    paginationCurrentChange(val) {
      this.pagination.pageNo = val
      this.getDataList()
    },
    handleNodeClick(data) {
      this.checkedData = data
      this.pagination.pageNo = 1
      this.$refs.tree.setCurrentKey(this.checkedData.id)
      this.getDataList()
    },
    resetForm() {
      this.filters = {
        name: '' // 名称
      }
      this.getDataList()
    },
    searchForm() {
      this.getDataList()
    },
    // 弹窗取消
    dialogClosed() {
      this.dialogVisible = false
      this.dialogImageUrl = ''
      this.fileList = []
      this.$refs.formInline.resetFields()
    },
    submit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let params = {
            ...this.formInline
          }
          if (params.parentId && params.parentId.length) {
            params.parentId = this.formInline.parentId[this.formInline.parentId.length - 1]
          } else {
            params.parentId = ''
          }
          if (this.rowId) {
            params.id = this.rowId
            this.$api.updateAlarmDictConfigData(params).then((res) => {
              if (res.code == 200) {
                this.$message.success('保存成功')
                this.getDataList()
                this.dialogClosed()
              } else {
                this.$message.error('保存失败')
              }
            })
          } else {
            this.$api.insertAlarmDictConfigData(params).then((res) => {
              if (res.code == 200) {
                this.$message.success('保存成功')
                this.getDataList()
                this.dialogClosed()
              } else {
                this.$message.error('保存失败')
              }
            })
          }
        } else {
          return false
        }
      })
    },
    // 图片相关
    fileChange(file, fileList) {
      this.fileList = fileList
    },
    beforeAvatarUpload(file) {
      const fileSize = this.uploadAcceptDict['annexGeneralized'].fileSize
      const isLt2M = file.size / 1024 / 1024 < fileSize
      if (!isLt2M) {
        this.$message.error(`上传文件大小不能超过 ${fileSize}MB!`)
        return false
      }
      const extension = file.name.substring(file.name.lastIndexOf('.'))
      const isFormat = this.uploadAcceptDict['annexGeneralized'].type.includes(extension)
      if (!isFormat) {
        this.$message.error(`上传图片只能是 ${this.uploadAcceptDict['annexGeneralized'].type}格式!`)
        return false
      }
      if (file.name.indexOf(',') != -1) {
        this.$message.error('非法的文件名')
        return false
      }
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogImageVisible = true
    },
    // 查看图片
    viewImage(row) {
      if (!row.pictureUrl) {
        this.$message.info('当前没有可预览的图片')
        return
      }
      this.dialogImageUrl = this.$tools.imgUrlTranslation(row.pictureUrl)
      this.dialogImageVisible = true
    },
    handleRemove(file, fileList) {
      this.hideUpload = false
      this.formInline.pictureUrl = ''
      this.fileList = fileList
    },
    httpRequset(file, type) {
      const params = new FormData()
      params.append('file', file.file)
      this.$api.dictionaryUploadIcon(params).then((res) => {
        if (res.code == 200) {
          this.formInline.pictureUrl = res.data
          this.$message({
            message: '上传成功',
            type: 'success'
          })
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },
    // 获取报警字典值
    getDictList() {
      this.$api.getAlarmDictConfigData({ type: 1 }).then((res) => {
        if (res.code == 200) {
          this.alarmDictList = res.data
        }
      })
    },
    alarmDictIdChange(value) {
      if (value && value.length && value.length > 0) {
        this.formInline.parentIds = value.join(',')
      }
      let node = this.$refs.alarmDictId.getCheckedNodes()
      this.formInline.parentName = node[0].label
    },
    handleListEvent(type, row) {
      if (type == 'add') {
        this.getDictList()
        this.diaTitle = '添加字典值'
        this.dialogVisible = true
        this.operationType = 'add'
        this.fileList = []
        this.formInline = {
          parentId: [], // 父级id
          parentIds: '', // 父级ID集合
          parentName: '', // 字典父级名称
          code: '', // 	报警字典编码
          name: '', // 报警字典名称
          colour: '', // 颜色
          pictureUrl: '', // 图片url
          status: '', // 状态 0-停用 1-启用
          type: 1 // 报警类型
        }
      } else if (type == 'edit') {
        this.dialogVisible = true
        this.getDictList()
        this.diaTitle = '编辑字典值'
        this.operationType = 'edit'
        this.rowId = row.id
        this.$api.getAlarmDictDataById({ id: row.id }).then((res) => {
          if (res.code == 200) {
            this.formInline = res.data
            if (res.data.parentIds) {
              let arr = res.data.parentIds.split(',')
              arr = arr.map((item) => {
                item = Number(item)
                return item
              })
              this.formInline.parentId = arr
            } else {
              this.formInline.parentId = []
            }
            if (res.data.pictureUrl) {
              this.fileList = [
                {
                  url: this.$tools.imgUrlTranslation(res.data.pictureUrl)
                }
              ]
            } else {
              this.fileList = []
            }
          }
        })
      } else if (type == 'check') {
        this.dialogVisible = true
        this.diaTitle = '字典值详情'
        this.operationType = 'detail'
        this.$api.getAlarmDictDataById({ id: row.id }).then((res) => {
          if (res.code == 200) {
            this.detailInfo = res.data
            this.formInline.colour = res.data.colour
            if (res.data.pictureUrl) {
              this.fileList = [
                {
                  url: this.$tools.imgUrlTranslation(res.data.pictureUrl)
                }
              ]
            } else {
              this.fileList = []
            }
          }
        })
        this.getDictList()
      } else if (type == 'addSubordinate') {
        this.dialogVisible = true
        this.getDictList()
        this.rowId = ''
        this.operationType = 'add'
        this.fileList = []
        this.formInline = {
          parentId: [], // 父级id
          parentIds: '', // 父级ID集合
          parentName: '', // 字典父级名称
          code: '', // 	报警字典编码
          name: '', // 报警字典名称
          colour: '', // 颜色
          pictureUrl: '', // 图片url
          status: '', // 状态 0-停用 1-启用
          type: 1 // 报警类型
        }
        if (row.parentId) {
          let arr = row.parentIds.split(',')
          arr = arr.map((item) => {
            item = Number(item)
            return item
          })
          arr.push(row.id)
          this.formInline.parentId = arr
          this.formInline.parentIds = arr.join(',')
        } else {
          this.formInline.parentId = [row.id]
          this.formInline.parentIds = row.id
        }
        this.formInline.parentName = row.name
      } else if (type == 'del') {
        if (row.status === 1) {
          this.$message.error('启用的字典不允许删除！')
          return false
        }
        this.$confirm('确认删除当前字典?', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.$api.deleteAlarmDictData({ id: row.id }).then((res) => {
              if (res.code == 200) {
                this.$message({ message: '删除成功', type: 'success' })
                this.getDataList()
              } else {
                this.$message({ message: res.message, type: 'error' })
              }
            })
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            })
          })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: linear-gradient(to right, #d9e1f8, #fff);
}
::v-deep .el-tree-node__content {
  height: 32px;
}
.configuration-content {
  height: 100%;
  display: flex;
  .configuration-content-left {
    width: 246px;
    height: 100%;
    padding: 10px;
    background: #fff;
    border-radius: 4px;
    margin-right: 12px;
    .left_content {
      height: calc(100% - 60px);
      overflow: auto;
    }
  }
  .configuration-content-right {
    height: 100%;
    min-width: 0;
    padding: 10px 16px;
    background: #fff;
    border-radius: 4px;
    flex: 1;
    .search-from {
      display: flex;
      justify-content: space-between;
      padding-bottom: 12px;
      ::v-deep .el-select,
      .el-input {
        width: 200px !important;
      }
      & > div {
        margin-right: 10px;
      }
      & > button {
        margin-top: 12px;
      }
    }
    .contentTable {
      height: calc(100% - 95px);
      display: flex;
      margin-top: 10px;
      flex-direction: column;
      .colorClass {
        display: flex;
        align-items: center;
        height: 32px;
        line-height: 32px;
      }
      ::v-deep .el-color-picker__trigger {
        border: none !important ;
        padding: 6px !important;
      }
      .contentTable-main {
        flex: 1;
        overflow: auto;
      }
      .contentTable-footer {
        padding: 10px 0 0;
      }
    }
  }
  .dot {
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    vertical-align: middle;
  }
  span {
    margin-left: 5px;
  }
  .status-color1 {
    .dot {
      background: #009a29;
    }
    color: #009a29;
  }
  .status-color2 {
    .dot {
      background: #cb2634;
    }
    color: #cb2634;
  }
  .diaContent {
    width: 100%;
    max-height: 500px !important;
    overflow: auto;
    background-color: #fff !important;
  }
  .ml-16 {
    margin-left: 16px;
  }
}
::v-deep .el-color-picker--small .el-color-picker__trigger {
  height: 32px !important;
  width: 32px !important;
}
::v-deep .el-color-picker--small {
  height: 32px !important;
}
::v-deep .el-input-group__prepend {
  padding: 0px !important;
  border: none !important;
}
::v-deep .deleteItem {
  color: #f53f3f !important;
}
</style>
