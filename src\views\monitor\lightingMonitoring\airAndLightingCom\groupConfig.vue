<template>
  <PageContainer :title="type == '1' ? '空调分组配置' : '照明分组配置'" type="list" :isClose="false" style="background-color: #f5f7fa">
    <template slot="content">
      <div class="content-left">
        <div class="toptip">
          <span class="green_line"></span>
          {{ type == '1' ? '空调分组配置' : '照明分组配置' }}
          <div class="title_btn_icon">
            <i title="新增" class="el-icon-plus" @click="nodeClick({}, 'add')"></i>
          </div>
        </div>
        <div class="left_content">
          <ZkRenderTree
            ref="tree"
            v-loading="treeLoading"
            :data="treeData"
            :props="defaultProps"
            node-key="id"
            :expand-on-click-node="false"
            default-expand-all
            highlight-current
            @node-click="handleNodeClick"
          >
            <span slot-scope="{ node, data }" class="custom-tree-node">
              <el-tooltip class="item" effect="dark" :content="node.label" placement="top-start" :disabled="!isShowTooltip">
                <span @mouseenter="visibilityChange($event)">{{ node.label }}（{{ data.count || 0 }}）</span>
              </el-tooltip>
              <span v-show="data.parentId !== 0 && data.id !== 2" class="tree-row-icon">
                <i class="el-icon-edit-outline" @click="() => nodeClick(data, 'edit')"></i>
                <i class="el-icon-delete" @click="() => nodeClick(data, 'del')"></i>
              </span>
            </span>
          </ZkRenderTree>
        </div>
      </div>
      <div class="content-right">
        <div class="content-top">
          <el-select v-model="searchForm.buildCode" class="sino_sdcp_input mr15" style="width: 15%" clearable multiple collapse-tags placeholder="建筑">
            <el-option v-for="item in buildingOption" :key="item.id" :label="item.ssmName" :value="item.id"></el-option>
          </el-select>
          <el-select v-model="searchForm.floorCode" class="sino_sdcp_input mr15" style="width: 15%" clearable multiple collapse-tags placeholder="楼层">
            <el-option v-for="item in floorOption" :key="item.id" :label="item.ssmName" :value="item.id"></el-option>
          </el-select>
          <el-select v-model="searchForm.areaCode" class="sino_sdcp_input mr15" style="width: 15%" clearable multiple collapse-tags placeholder="空间类型">
            <el-option v-for="item in areaOption" :key="item.id" :label="item.dictName" :value="item.id"></el-option>
          </el-select>
          <el-input v-model.trim="searchForm.loopName" clearable placeholder="回路名称" class="sino_sdcp_input mr15"></el-input>
          <el-button type="primary" plain @click="reset()">重置</el-button>
          <el-button type="primary" @click="search()">查询</el-button>
          <div class="mut-tooltip">
            <el-tooltip popper-class="tooltip" effect="light" placement="right">
              <div slot="content" class="group-config-type">
                <div v-for="item in groupConfigList" :key="item.id" @click="selectGroupConfig('', item.id, item.dictName)">
                  {{ item.dictName }}
                </div>
              </div>
              <el-button type="primary" plain style="width: auto" :disabled="!multipleSelection.length">批量修改分组至<i class="el-icon-arrow-right el-icon--right"></i></el-button>
            </el-tooltip>
          </div>
        </div>
        <div class="table-content">
          <el-table v-loading="tableLoading" :data="tableData" :border="true" height="calc(100% - 40px)" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column v-for="(item, index) in tableTitle" :key="index" :prop="item.value" :label="item.label" show-overflow-tooltip :width="item.width"></el-table-column>
            <el-table-column prop="entityTypeName" label="标签">
              <template slot-scope="scope">
                <el-select
                  v-model="scope.row.lightingDict"
                  class="no-border"
                  multiple
                  collapse-tags
                  placeholder="请选择"
                  @change="selectGroupConfig($event, scope.row.sensorCode, '', scope.row)"
                >
                  <el-option v-for="item in groupConfigList" :key="item.id" :label="item.dictName" :value="item.id + '_' + item.dictName"> </el-option>
                </el-select>
              </template>
            </el-table-column>
          </el-table>
          <div class="table-page">
            <el-pagination
              class="pagination"
              :current-page="paginationData.currentPage"
              :page-sizes="[15, 30, 50, 100]"
              :page-size="paginationData.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="paginationData.total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            ></el-pagination>
          </div>
        </div>
      </div>
      <el-dialog v-if="dialogVisible" :title="dialogType == 'add' ? '新建分组' : '编辑分组'" :visible.sync="dialogVisible" custom-class="group-dialog" :before-close="closeDialog">
        <div class="content" style="padding: 20px">
          <div class="label-input">分组名称:</div>
          <el-input v-model.trim="groupName" maxlength="20" placeholder="请输分组名称"></el-input>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="groupSubmit">确 定</el-button>
        </span>
      </el-dialog>
    </template>
  </PageContainer>
</template>
<script lang="jsx">
export default {
  components: {},
  props: {
    projectCode: {
      type: String,
      default: ''
    },
    type: {
      // 1 为空调
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tableTitle: [
        {
          label: '回路名称',
          value: 'sensorName'
        },
        {
          label: '服务的空间类型',
          value: 'areaType'
        },
        {
          label: '建筑',
          value: 'constructName'
        },
        {
          label: '楼层',
          value: 'floorName'
        },
        {
          label: '服务的空间名称',
          value: 'spaceName'
        },
        {
          label: '控制模块',
          value: 'upstreamEntityName'
        }
      ],
      tableData: [],
      loading: false,
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      isShowTooltip: false,
      treeLoading: false,
      tableLoading: false,
      defaultProps: {
        children: 'children',
        label: 'dictName',
        value: 'id'
      },
      treeData: [],
      buildingOption: [],
      floorOption: [],
      areaOption: [],
      searchForm: {
        buildCode: [],
        floorCode: [],
        areaCode: [],
        loopName: ''
      },
      selectNode: '', // 选择分组
      multipleSelection: [], // table选中数据集合
      groupConfigList: [],
      dialogType: 'add', // 弹窗类型
      dialogVisible: false, // 分组弹窗
      groupName: '', // 分组名称 form
      groupId: '' // 分组id form
    }
  },
  mounted() {
    this.getStructureTree()
    this.getAreaTypeList()
    this.getTableData()
  },
  methods: {
    // 获取数据-----------------------------------
    // 获取空间 建筑 楼层信息
    getStructureTree() {
      this.$api.getStructureTree({ projectCode: this.projectCode }).then((res) => {
        const data = res.data
        if (data && data.length) {
          this.buildingOption = data.filter((e) => e.ssmType == 3)
          this.floorOption = data.filter((e) => e.ssmType == 4)
        }
      })
    },
    // 获取空间类型
    getAreaTypeList() {
      this.$api
        .valveTypeList({
          projectCode: this.projectCode,
          typeValue: 'SP'
        })
        .then((res) => {
          const data = res.data
          if (data && data.length) {
            this.areaOption = data
          }
        })
    },
    // 获取列表
    getTableData() {
      let data = {
        menuCode: '', // 实体菜单code
        projectCode: this.projectCode, // 项目code
        pageSize: this.paginationData.pageSize,
        page: this.paginationData.currentPage,
        sensorName: this.searchForm.loopName,
        spaceType: this.searchForm.areaCode.toString(),
        constructId: this.searchForm.buildCode.toString(),
        floorId: this.searchForm.floorCode.toString(),
        lightingDictId: this.selectNode,
        airPage: true
      }
      this.tableLoading = true
      this.$api.getSurveyParameterList(data).then((res) => {
        this.tableLoading = false
        if (res.code == 200) {
          if (res.data.pageList.list && res.data.pageList.list.length) {
            res.data.pageList.list.map((e) => {
              e.lightingDictId = e.lightingDictId ? e.lightingDictId.toString().split(',') : []
              e.lightingDictName = e.lightingDictName ? e.lightingDictName.split(',') : []
              e.lightingDict = e.lightingDictId.map((item, index) => item + '_' + e.lightingDictName[index])
            })
            this.tableData = res.data.pageList.list
            if (this.tableData && this.tableData.length) {
              this.tableData.map((e) => {
                if (e.spaceList?.length) {
                  // 服务的空间类型
                  const areaType = [...new Set(Array.from(e.spaceList, ({ spaceType }) => spaceType))]
                  const areaTypeList = this.areaOption?.filter((item) => areaType.includes(item.id.toString())) ?? []
                  e.areaType = Array.from(areaTypeList, ({ dictName }) => dictName).toString()
                  // 建筑
                  e.constructName = [...new Set(Array.from(e.spaceList, ({ constructName }) => constructName))].toString()
                  // 楼层
                  e.floorName = [...new Set(Array.from(e.spaceList, ({ floorName }) => floorName))].toString()
                  // 服务的空间名称
                  e.spaceName = [...new Set(Array.from(e.spaceList, ({ spaceName }) => spaceName))].toString()
                }
              })
            }
            this.paginationData.total = parseInt(res.data.pageList.totalCount)
          } else {
            this.tableData = []
          }
          this.treeData = [res.data.lightingDicts]
          this.groupConfigList = res.data.lightingDicts.children
        } else if (res.message) {
          this.$message.error(res.message)
        }
      })
    },
    // 重置
    reset() {
      Object.assign(this.searchForm, {
        buildCode: [],
        floorCode: [],
        areaCode: [],
        loopName: ''
      })
      this.paginationData.currentPage = 1
      this.paginationData.pageSize = 15
      this.search()
    },
    // 查询
    search() {
      this.getTableData()
    },
    // dialog -------------------------------
    groupSubmit() {
      if (!this.groupName) {
        this.$message({
          message: '分组名称不为空',
          type: 'warning'
        })
      }
      // 新增
      if (this.dialogType == 'add') {
        this.$api
          .insertLightingDict(
            {
              projectCode: this.projectCode,
              name: this.groupName
            },
            {
              'operation-type': 1
            }
          )
          .then((res) => {
            this.promiseThen(res)
          })
        // 编辑
      } else {
        this.$api
          .updateLightingDict(
            {
              projectCode: this.projectCode,
              name: this.groupName,
              id: this.groupId
            },
            {
              'operation-type': 2,
              'operation-id': this.groupId,
              'operation-name': this.groupName
            }
          )
          .then((res) => {
            this.promiseThen(res)
          })
      }
    },
    promiseThen(res) {
      if (res.code == 200) {
        this.$message({
          message: '操作成功',
          type: 'success'
        })
        this.getTableData()
        this.closeDialog()
      } else {
        this.$message({
          message: res.message,
          type: 'warning'
        })
      }
    },
    closeDialog() {
      this.dialogVisible = false
    },
    // tree事件-----------------------------------
    handleNodeClick(data) {
      this.selectNode = data.id
      this.$nextTick(() => {
        // this.$refs.tree.setCurrentNode([data]);
      })
      this.getTableData()
    },
    visibilityChange(e) {
      const item = e.target
      const itemWidth = item.offsetWidth
      const itemScrollWidth = item.scrollWidth
      if (itemWidth < itemScrollWidth) {
        this.isShowTooltip = true
      } else {
        this.isShowTooltip = false
      }
    },
    mouseenter(data) {
      // 全部 和 未分组 不支持 修改
      if (data.parentId === 0 || data.id === 2) return
      this.$set(data, 'isShow', true)
    },
    mouseleave(data) {
      if (data.parentId === 0 || data.id === 2) return
      this.$set(data, 'isShow', false)
    },
    nodeClick(data, type) {
      if (type !== 'del') {
        this.dialogType = type
        if (data) {
          this.groupName = data.dictName
          this.groupId = data.id
        }
        this.dialogVisible = true
      } else {
        this.delGroupFn(data)
      }
    },
    delGroupFn(data) {
      this.$confirm('删除后，相关数据将自动解绑，确认删除？', '提示', {
        cancelButtonClass: 'el-button--primary is-plain',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api
          .deleteLightingDict(
            {
              projectCode: this.projectCode,
              id: data.id
            },
            {
              'operation-type': 3,
              'operation-id': data.id,
              'operation-name': data.dictName
            }
          )
          .then((res) => {
            if (res.code == 200) {
              this.$message({
                message: '删除成功',
                type: 'success'
              })
              this.getTableData()
            } else {
              this.$message({
                message: res.message,
                type: 'warning'
              })
            }
          })
      })
    },
    // table事件-----------------------------------
    // 修改分组
    selectGroupConfig(item, valId, valName, selectRow) {
      let params = {
        projectCode: this.projectCode
      }
      // 多选params
      if (!item) {
        let ids = Array.from(this.multipleSelection, ({ sensorCode }) => sensorCode)
        let historyGroupIds = Array.from(this.multipleSelection, ({ lightingDictId }) => lightingDictId).join('_')
        let historyGroupNames = Array.from(this.multipleSelection, ({ lightingDictName }) => lightingDictName).join('_')
        Object.assign(params, {
          ids,
          historyGroupIds,
          historyGroupNames,
          dictId: valId,
          dictName: valName
        })
        // 表格单选修改
      } else {
        this.$tools.cancelAjax()
        let dictId = []
        let dictName = []
        item.forEach((e) => {
          const splitData = e.split('_')
          dictId.push(splitData[0])
          dictName.push(splitData[1])
        })
        Object.assign(params, {
          ids: valId,
          historyGroupIds: selectRow.lightingDictId?.join('_') ?? '',
          historyGroupNames: selectRow.lightingDictName?.join('_') ?? '',
          dictId: dictId.toString(),
          dictName: dictName.toString()
        })
      }
      this.$api.updateLightingGroup(params).then((res) => {
        if (res.code == 200) {
          this.$message({
            message: '更改分组成功',
            type: 'success'
          })
        } else {
          this.$message({
            message: res.message,
            type: 'warning'
          })
        }
        this.getTableData()
      })
    },
    // table行修改分组
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleSizeChange(val) {
      this.paginationData.currentPage = 1
      this.paginationData.pageSize = val
      this.search()
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.search()
    }
  }
}
</script>
<style src="@/assets/styles/sinomis-ui.css" scoped></style>
<style lang="scss" scoped>
.el-tree-node__content:hover {
  background-color: red;
}
.table-page {
  position: relative;
  .mut-tooltip {
    position: absolute;
    top: 5px;
    left: 0;
  }
}
.title_btn_icon {
  float: right;
  cursor: pointer;
  margin-right: 20px;
  i {
    font-size: 18px;
  }
}
.left_content {
  height: calc(100% - 50px);
  padding: 3px 10px;
  // firefox隐藏滚动条
  scrollbar-width: none;
  // chrome隐藏滚动条
  &::-webkit-scrollbar {
    display: none;
  }
  ::v-deep .custom-tree-node {
    overflow: hidden;
    flex: 1;
    display: flex;
    justify-content: space-between;
    padding-right: 10px;
    & > span {
      &:first-child {
        flex: 1;
        display: block;
        align-items: center;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        color: #121f3e;
        font-size: 14px;
        font-family: 'PingFang SC-Regular', 'PingFang SC';
      }
      &:last-child {
        display: flex;
        align-items: center;
        i {
          margin-left: 6px;
          cursor: pointer;
          color: #3562db;
        }
      }
    }
  }
  ::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
    background-color: #d9e1f8 !important;
  }
  .tree-row-icon {
    float: right;
    margin-right: 10px;
    cursor: pointer;
    i {
      font-size: 16px;
      margin-right: 6px;
    }
  }
}
.content-left {
  width: 268px;
  margin: 0 10px 0 0;
  background: #fff;
  height: 100%;
  border-radius: 4px;
  float: left;
}
.content-right {
  display: flex;
  flex-direction: column;
  // width: 100%;
  margin: 0 0 0 278px;
  height: 100%;
  background: #fff;
  padding: 10px 10px 0;
  box-sizing: border-box;
}
.table-content {
  flex: 1;
  // padding: 3px 10px;
  padding-top: 14px;
  box-sizing: border-box;
}
.sure:focus {
  background-color: #5288fc;
  border-color: #5288fc;
  color: #fff;
}
</style>
<style lang="scss">
.group-dialog {
  width: 30% !important;
  min-width: 30% !important;
  height: 250px !important;
  min-height: 250px !important;
  .content {
    display: flex;
    > div {
      height: 50px;
      line-height: 50px;
    }
    .label-input {
      width: 100px;
      text-align: center;
    }
    .el-input {
      width: calc(90% - 100px);
    }
  }
}
.tooltip {
  border: none !important;
  padding: 0 !important;
  border-radius: 5px;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  z-index: 999;
  .popper__arrow {
    border-width: 0 !important;
  }
}
.group-config-type {
  height: auto;
  width: auto;
  color: #606266;
  max-height: 300px;
  overflow-y: auto;
  div {
    padding: 10px 25px;
    font-size: 14px;
    cursor: pointer;
    // border-bottom: 1px solid;
    &:hover {
      background-color: #f7f6f9;
      color: #5188fc;
    }
  }
}
.no-border {
  .el-input__inner {
    border: 0;
  }
}
</style>
