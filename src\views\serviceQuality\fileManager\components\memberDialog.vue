<template>
  <el-dialog v-if="memberShow" v-dialogDrag title="添加成员" width="50%" :visible.sync="memberShow"
    custom-class="interDialogDiv" :before-close="closeDialog" append-to-body>
    <div class="sapce_content">
      <div class="left">
        <div v-loading="treeLoading" class="sino_tree_box">
          <el-collapse v-model="activeName" style="padding: 0 10px 0 20px" accordion @change="handelChange">
            <el-collapse-item v-for="(list, index) in collapseData" :key="index" :title="list.unitComName"
              :name="list.umId">
              <div class="sino_tree_box" style="margin: 0">
                <el-tree ref="tree" class="filter-tree" :data="treeData" :props="defaultProps" node-key="id"
                  highlight-current @node-click="nodeClick"></el-tree>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
      <div class="center">
        <el-input v-model.trim="userInfo" placeholder="搜索姓名、手机号、部门" clearable suffix-icon="el-icon-search"
          @input="nameInput"></el-input>
        <el-checkbox-group v-model="staffSelect" @change="handleCheck">
          <el-checkbox v-for="item in staffData" :key="item.id" :label="item.id" style="display:block;">
            <div class="personCheck">
              <img src="../../../../assets/images/avatar.png" />
              <div class="info">
                <div class="name">{{ item.staffName }}</div>
                <div class="mobile">{{item.mobile}}</div>
              </div>
            </div>
          </el-checkbox>
        </el-checkbox-group>
      </div>
      <div class="right">
        <div class="top">
          <span>
            <span class="label">已选:</span>
            <span class="num">{{selectData.length?selectData.length:0}}</span>
            <span class="dept">名用户</span>
          </span>
          <span class="clear" @click="clear">清空</span>
        </div>
        <div v-for="(item,index) in selectData " :key="index" class="item-list">
          <div style="display:flex">
            <img src="../../../../assets/images/avatar.png" />
            <div class="info">
              <div class="name">{{ item.staffName }}</div>
              <div class="mobile">{{item.mobile}}</div>
            </div>
          </div>
          <div class="remove" @click="remove(item,index)"><i class="el-icon-close"></i></div>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" v-if="peopleTyle =='see'" @click="submit('seeTrue')">确 定</el-button>
      <el-button type="primary" v-else @click="submit('downloadTrue')">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
  import {
    transData
  } from "@/util";
  // import {
  //   log
  // } from '@/components/rtspCavas/webrtcstreamer/adapter.min';
  export default {
    name: 'spaceDialog',
    props: {
      memberShow: {
        type: Boolean,
        default: false
      },
      peopleTyle: {
        type: String,
        default: ''
      },
      echoPeopleSeeList: {
        type: Array,
        default: () => {}
      },
      echoPeopleDownloadList: {
        type: Array,
        default: () => {}
      },
    },
    data() {
      return {
        keyWord: '', // 编码/名称/通用名/规格型号/SN
        treeData: [],
        defaultProps: {
          label: "deptName",
          children: "list",
        },
        treeLoading: false,
        selectData: [],
        activeName: "",
        collapseData: [],
        staffData: [], // 人员列表
        staffSelect: [],
        officeId: '',
        pmId: '',
        userInfo: '',
        seeList: [],
        downloadList: [],
      }
    },
    mounted() {
      this.getUnitListFn()

    },
    watch: {},
    methods: {
      //  获取单位列表
      getUnitListFn() {
        this.$api.getUnitList({}).then((res) => {
          this.treeLoading = false;
          if (res.code == 200) {
            this.collapseData = this.unitList = res.data;
            this.staffListByPageFn();
          }
        });
      },
      nameInput() {
        this.staffListByPageFn()
        this.staffSelect = this.selectData.map(item => {
          return item.id
        })
      },
      handelChange(val) {
        this.activeName = val;
        this.officeId = ""
        this.pmId = val
        this.getDeptListFn(val);
        this.staffListByPageFn();
      },
      // 部门列表
      getDeptListFn(unitId) {
        this.$api
          .getDeptList({
            unitId: unitId,
          })
          .then((res) => {
            if (res.code == 200) {
              this.departList = res.data;
              this.treeData = transData(
                res.data,
                "id",
                "pid",
                "list"
              );
            }
          });
      },
      //  获取人员信息列表
      staffListByPageFn() {
        this.$api
          .getPostMemberListByPage({
            pmId: this.pmId,
            current: 1,
            size: 99999,
            officeId: this.officeId,
            userInfo: this.userInfo
          })
          .then((res) => {
            if (res.code == 200) {
              this.staffData = res.data.records;
            }
          });
      },
      // 选择
      handleCheck(list) {
        if (this.peopleTyle == 'see') {
          this.seeList = this.staffSelect
        } else if (
          this.peopleTyle == 'download'
        ) {
          this.downloadList = this.staffSelect
        }
        let arr = []
        this.selectData = []
        list.forEach(el => {
          this.staffData.forEach(item => {
            if (el === item.id) {
              arr.push(item)
            }
          })
        });
        this.selectData = this.removeSame(this.selectData.concat(arr))

      },
      removeSame(array) {
        let newArray = []
        for (let item of array) {
          // 使用JSON.stringfy()方法将数组和数组元素转换为JSON字符串之后再使用includes()方法进行判断
          if (JSON.stringify(newArray).includes(JSON.stringify(item))) {
            continue
          } else {
            // 不存在的添加到数组中
            newArray.push(item)
          }
        }
        return newArray
      },
      nodeClick(val) {
        this.pmId = val.umId;
        this.officeId = val.id
        this.staffListByPageFn();
      },
      // 移除
      remove(list, index) {
        this.selectData.splice(index, 1);
        this.staffSelect = this.selectData.map(item => {
          return item.id
        })

      },
      // 清空
      clear() {
        this.selectData = []
        this.staffSelect = []
        this.$nextTick(() => {
          this.$refs.tree.setCheckedKeys([])
        })
      },
      closeDialog() {
        this.$emit('closeMemberDialog')
      },
      submit(type) {
        if (this.selectData.length < 1) {
          this.$message({
            message: '请选择至少一条数据',
            type: 'warning'
          })
        } else {
          if (type == 'seeTrue') {
            // this.$api.queryUserIdByStaffId({
            //   staffIds: this.seeList
            // }).then((res) => {
            //   this.$nextTick(() => {
            //     this.$emit('submitMembersee', res.data)
            //   })
            // })

            this.$emit('submitMembersee', this.seeList)

          }
          if (type == 'downloadTrue') {
            // this.$api.queryUserIdByStaffId({
            //   staffIds: this.downloadList
            // }).then((res) => {
            //   this.$nextTick(() => {
            //     this.$emit('submitMemberdownload', res.data)
            //   })
            // })

            this.$emit('submitMemberdownload', this.downloadList)

          }
          this.closeDialog()
        }
      }
    }
  }

</script>
<style lang="scss" scoped>
  .interDialogDiv {
    .sapce_content {
      margin: 0 auto;
      background: #fff;
      padding: 10px 0;
      border-radius: 4px;
      height: 400px;
      display: flex;
      width: 100%;

      .left {
        text-align: center;
        width: 300px;
        height: 100%;
        background-color: #fff;
        border-radius: 5px;
        overflow: hidden;
      }

      .center {
        width: 300px;
        border-left: 1px solid #dcdfe6;
        background-color: #fff;
        height: 100%;
        margin-left: 10px;
        border-radius: 5px;
        padding-left: 16px;
        overflow: scroll;

        // display: flex;
        .personCheck {
          width: 100%;
          display: flex;
          padding: 10px 0;

          img {
            vertical-align: middle;
          }
        }
      }



      .right {
        width: calc(100% - 620px);
        border-left: 1px solid #dcdfe6;
        background-color: #fff;
        height: 100%;
        margin-left: 10px;
        border-radius: 5px;
        padding-left: 16px;
        overflow: scroll;

        .top {
          display: flex;
          padding: 0 12px;
          justify-content: space-between;

          .label,
          .dept {
            font-size: 14px;
            color: #7f848c;
          }

          .num {
            font-size: 14px;
            color: #333333;
            margin-left: 10px;
          }

          .dept {
            margin-left: 10px;
          }

          .clear {
            font-size: 12px;
            color: #3562db;
            cursor: pointer;
          }
        }

        .item-list {
          display: flex;
          cursor: pointer;
          padding: 0 12px;
          justify-content: space-between;
          margin-top: 8px;

          .remove {
            margin: auto 0;
          }
        }

        .item-list:hover {
          background: #e6effc;
        }
      }

      .info {
        margin-left: 8px;

        .name {
          font-weight: 500;
          color: #333333;
        }

        .mobile {
          font-size: 12px;
          color: #7f848c;
        }
      }
    }

  }

  .sino_tree_box {
    margin-top: 10px;
    height: calc(100% - 40px);
    overflow: auto;

    .custom-tree-node-label {
      display: inline-block;
      width: 200px;
      white-space: nowrap;
      /* 防止换行 */
      overflow: hidden;
      /* 隐藏超出部分 */
      text-overflow: ellipsis;
      text-align: left;
    }
  }

  ::v-deep .el-tree .el-tree-node__content {
    height: 36px;
    line-height: 16px;
    padding: 0;
  }

  ::v-deep .el-tree-node {
    .is-leaf+.el-checkbox .el-checkbox__inner {
      display: inline-block;
    }

    .el-checkbox .el-checkbox__inner {
      display: none;
    }
  }

  ::v-deep .el-collapse-item__content {
    padding-bottom: 0px !important;
  }

  ::v-deep .el-checkbox__input {
    display: inline-block !important;
    margin-bottom: 26px !important;
  }

</style>
