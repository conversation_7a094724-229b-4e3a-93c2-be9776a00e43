<!--
 * @Author: hedd
 * @Date: 2023-11-23 15:30:36
 * @LastEditTime: 2024-11-26 18:11:01
 * @FilePath: \ihcrs_pc\src\views\monitor\monitoringConfig\commonConfiguration.vue
 * @Description: 公共的配置页面
  :isComputerRoom="true" 为true时，显示机房配置页面，false时显示非机房配置页面（配电专用 暂时未使用）
-->
<template>
  <component :is="componentId" :projectCode="monitorData.projectCode" :requestHttp="requestHttp"></component>
</template>
<script>
import monitorSetting from './monitorSetting'
import monitorSettingOld from './monitorSettingOld'
import { monitorTypeList } from '@/util/dict.js'
export default {
  name: 'commonConfiguration',
  components: { monitorSetting, monitorSettingOld },
  beforeRouteEnter(to, from, next) {
    let names = []
    to.matched.map((v, i) => {
      if (i > 0) {
        v.components.default.name && names.push(v.components.default.name)
      }
    })
    // 进入页面时，先将当前页面的 name 信息存入 keep-alive 全局状态
    setTimeout(() => {
      next((vm) => {
        // 由于当前页面的 name 信息是在 beforeRouteEnter 钩子中获取的，最后一位的name是当前公共组件的name，需要替换为当前页面的name
        names[names.length - 1] = vm.pathArr[0]
        vm.$store.commit('keepAlive/add', names)
      })
    }, 0)
  },
  async beforeRouteLeave(to, from, next) {
    // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
    if (![this.pathArr[this.pathArr.length - 1]].includes(to.name)) {
      // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    const monitorData = monitorTypeList.find((item) => {
      const pathArr = item?.formPath?.split('/') || []
      // 0 为当前页面的name  1为跳转form的name
      return pathArr[0] == this.$route.name
    })
    return {
      monitorData: monitorData || {},
      requestHttp: monitorData?.requestUrl || __PATH.VUE_IEMC_API,
      pathArr: monitorData?.formPath?.split('/') || []
    }
  },
  computed: {
    componentId() {
      // 医用气体配置、UPS配置、污水配置、电梯监测使用旧的配置页面
      if (['medicalGasConfiguration', 'upsConfiguration', 'sewageConfiguration', 'elevatorConfiguration'].includes(this.pathArr[0])) {
        return 'monitorSettingOld'
      } else {
        return 'monitorSetting'
      }
    }
  },
  mounted() {
    console.log(this.pathArr)
  },
  methods: {
    // 123
  }
}
</script>
<style lang="scss" scoped>
// 123</style>
