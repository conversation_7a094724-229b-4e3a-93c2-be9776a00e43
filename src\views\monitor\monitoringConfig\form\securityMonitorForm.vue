<template>
  <PageContainer :footer="true">
    <div slot="content" v-loading="formLoading" class="form-content">
      <div class="form-title">监测实体配置</div>
      <el-form ref="formInline" :model="formInline" :rules="rules" :validate-on-rule-change="false">
        <ContentCard title="基本信息">
          <div slot="content" class="footer-role">
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="7">
                <el-form-item label="监测实体名称" prop="sensorName" label-width="110px">
                  <el-input v-model="formInline.sensorName" maxlength="25" placeholder="请输入监测实体名称"></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="7">
                <el-form-item label="监测实体编号" prop="sensorNo" label-width="110px">
                  <el-input v-model="formInline.sensorNo" type="text" maxLength="20" show-word-limit
                    placeholder="请输入数字和字母"></el-input>
                  <!-- v-filterSpecialChar -->
                  <!-- @input="formInline.sensorNo=formInline.sensorNo.replace(/[^\dA-Z\a-z]/g,'')" -->
                </el-form-item>
              </el-col>
              <el-col :md="7">
                <el-form-item label="实体类型" prop="entityTypeId" label-width="110px">
                  <el-select v-model="formInline.entityTypeId" filterable clearable @change="entityChang">
                    <el-option v-for="item in entityTypeList" :key="item.id" :label="item.name" :value="item.id + ''">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24" style="margin: 0">
              <el-col v-if="monitorData.projectName === '消防水箱'" :md="7">
                <el-form-item label="水箱高度" prop="height" label-width="110px">
                  <el-input v-model="formInline.height" type="text" maxLength="8" placeholder="请输入水箱高度"
                    onkeyup="this.value= this.value.match(/\d+(\.\d{0,2})?/) ? this.value.match(/\d+(\.\d{0,2})?/)[0] : ''"><span
                      slot="suffix">M</span></el-input>
                </el-form-item>
              </el-col>
              <el-col v-if="monitorData.projectName === '防火门'" :md="7">
                <el-form-item label="初始状态" prop="initialState" label-width="110px">
                  <el-radio-group v-model="formInline.initialState">
                    <el-radio :label="0">开门</el-radio>
                    <el-radio :label="1">关闭</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24" style="margin: 0 0 20px">
              <el-col :md="24">
                <div>
                  <span class="form-btn-title">关联设备资产</span><el-button class="form-btn-btn" type="primary"
                    @click="chooseAssets('relation')">选择</el-button>
                  <el-tag v-if="assetsData.assetsId" closable class="camera-tag" @close="delAssetsData()">
                    <span class="assets-info">资产名称：<span>{{ assetsData.assetName }}</span></span>
                    <span class="assets-info">专业类别：<span>{{ assetsData.assetCategoryName }}</span></span>
                    <span class="assets-info">系统类别：<span>{{ assetsData.assetTypeName }}</span></span>
                    <span class="assets-info">资产编码：<span>{{ assetsData.assetsNumber }}</span></span>
                    <span class="assets-info">安装位置：<span v-html="assetsData.regionName"></span></span>
                    <span class="assets-info">模型ID：<span>{{ assetsData.modelCode }}</span></span>
                  </el-tag>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="24" style="margin: 0 0 20px">
              <el-col :md="24">
                <div>
                  <span class="form-btn-title">关联监控画面</span><el-button class="form-btn-btn" type="primary"
                    @click="associatedCamera()">选择</el-button><el-tag v-for="tag in imsVidiconList"
                    :key="tag.imsVidiconId" class="camera-tag" closable @close="tagHandleClose(tag.imsVidiconId)">
                    {{ tag.imsVidiconName }}
                  </el-tag>
                </div>
              </el-col>
            </el-row>
          </div>
        </ContentCard>
        <ContentCard title="监测参数配置">
          <el-button slot="title-right" class="form-btn-btn" type="primary" @click="addrow('monitor')"><i
              class="el-icon-plus"></i>
            添加</el-button>
          <div slot="content">
            <!-- :span-method="objectSpanMethod" -->
            <TablePage ref="tablePage" v-loading="tableLoading" :tableColumn="tableColumn" :showPage="false"
              :data="tableData" border> </TablePage>
          </div>
        </ContentCard>
        <ContentCard title="控制参数配置" v-if="monitorData.projectCode == 'IEMC-AccessControlEquipment'">
          <el-button slot="title-right" class="form-btn-btn" type="primary" @click="addrow('control')"><i
              class="el-icon-plus"></i>
            添加</el-button>
          <div slot="content">
            <!-- :span-method="objectSpanMethod" -->
            <TablePage ref="tablePage" v-loading="tableLoading" :tableColumn="tableControlColumn" :showPage="false"
              :data="tableControlData" border> </TablePage>
          </div>
        </ContentCard>
      </el-form>
      <!-- 弹窗事件 -->
      <template>
        <monitoringParamsGather :cameraDialogShow="cameraDialogShow" :cameraDialogData="cameraDialogData"
          :equipmentAssetsDialogShow="equipmentAssetsDialogShow" :equipmentAssetsDialogData="equipmentAssetsDialogData"
          :assetsClickType="assetsClickType" @submitDialog="submitMonitoringParamsGather" />
      </template>
      <!-- 选择传感器弹窗 -->
      <template v-if="harvesterDialogShow">
        <harvesterDialog :dialogShow.sync="harvesterDialogShow" :dialogData="selectHarvesterDialogData"
          harvesterCheckType="radio" @submitDialog="submitHarvesterDialog" @closeDialog="closeHarvesterDialog" />
      </template>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">关闭</el-button>
      <el-button v-if="$route.query?.type != 'detail'" type="primary" @click="submitForm()">保存</el-button>
    </div>
  </PageContainer>
</template>
<script lang="jsx">
import monitoringParamsGather from '../components/monitoringParamsGather'
import harvesterDialog from '../components/harvesterDialog'
import { monitorTypeList } from '@/util/dict.js'
export default {
  name: 'securityMonitorForm',
  components: {
    monitoringParamsGather,
    harvesterDialog
  },
  async beforeRouteLeave(to, from, next) {
    if (![to.path.split('/')[2]].includes(to.name)) {
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      // 传感器
      harvesterDialogShow: false,
      selectHarvesterDialogData: {},
      // 摄像机
      cameraDialogShow: false,
      cameraDialogData: '',
      imsVidiconList: [],
      operationType: '',
      // 设备资产
      equipmentAssetsDialogShow: false,
      equipmentAssetsDialogData: {},
      assetsClickType: '',
      assetsData: {
        assetsId: '', // 资产ID
        assetName: '', // 资产名称
        regionName: '', // 区域名称
        regionCode: '', // 区域ID
        modelCode: '', // 模型ID
        assetsNumber: '', // 资产编码
        assetTypeName: '', // 系统类别
        assetTypeId: '', // 系统类别ID
        assetCategoryName: '', // 专业类别
        assetCategoryCode: '' // 专业类别ID
      },
      formInline: {
        entityTypeId: '', // 实体类型ID
        sensorName: '', // 监测项实体名称
        sensorNo: '' // 监测项编号
      },
      entityTypeName: '', // 实体类型名称
      entityTypeList: [], // 实体类型列表
      formLoading: false,
      rules: {
        sensorName: {
          required: true,
          message: '请输入监测实体名称',
          trigger: 'change'
        },
        sensorNo: [
          {
            required: true,
            message: '请输入监测项编号',
            trigger: 'change'
          },
          { min: 6, message: '长度不低于6位', trigger: 'blur' }
        ],
        entityTypeId: {
          required: true,
          message: '请选择实体类型',
          trigger: 'change'
        }
      },
      setType: '',
      tableLoading: false,
      tableData: [],
      tableControlData: [],
      tableColumn: [
        {
          prop: 'dataServerName',
          label: '数据主机'
        },
        {
          prop: 'harvesterType',
          label: '传感器类型'
        },
        {
          prop: 'harvesterName',
          label: '传感器名称'
        },
        {
          prop: 'parameterName',
          label: '监测参数'
        },
        {
          prop: 'unitName',
          label: '单位'
        },
        {
          prop: 'istId',
          label: '图纸参数',
          showOverflowTooltip: false,
          render: (h, row) => {
            return (
              <el-select v-model={row.row.istId} clearable filterable collapse-tags class="monitor-select">
                {this.scadaParameterList.map((item) => {
                  return <el-option key={item.istId} label={item.dataTag} value={item.istId + ''}></el-option>
                })}
              </el-select>
            )
          },
          hasJudge: false
        },
        {
          prop: 'dictAliasId',
          label: '监测项目别名',
          showOverflowTooltip: false,
          render: (h, row) => {
            return (
              <el-select v-model={row.row.dictAliasId} clearable filterable collapse-tags class="monitor-select">
                {this.aliasDictList.map((item) => {
                  return <el-option key={item.id + ''} label={item.paramAlias + '(' + item.paramName + ')'} value={item.id}></el-option>
                })}
              </el-select>
            )
          }
        },
        {
          prop: 'operation',
          label: '操作',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span class="operationBtn-span" onClick={() => this.handleParameterEvent('edit', row.row, 'monitor')}>
                  修改
                </span>
                <span class="operationBtn-span" onClick={() => this.handleParameterEvent('del', row.row, 'monitor')}>
                  删除
                </span>
              </div>
            )
          }
        }
      ],
      tableControlColumn: [
        {
          prop: 'dataServerName',
          label: '数据主机'
        },
        {
          prop: 'harvesterType',
          label: '传感器类型'
        },
        {
          prop: 'harvesterName',
          label: '传感器名称'
        },
        {
          prop: 'parameterName',
          label: '监测参数'
        },
        {
          prop: 'unitName',
          label: '单位'
        },
        {
          prop: 'istId',
          label: '图纸参数',
          showOverflowTooltip: false,
          render: (h, row) => {
            return (
              <el-select v-model={row.row.istId} clearable filterable collapse-tags class="monitor-select">
                {this.scadaParameterList.map((item) => {
                  return <el-option key={item.istId} label={item.dataTag} value={item.istId + ''}></el-option>
                })}
              </el-select>
            )
          },
          hasJudge: false
        },
        {
          prop: 'dictAliasId',
          label: '监测项目别名',
          showOverflowTooltip: false,
          render: (h, row) => {
            return (
              <el-select v-model={row.row.dictAliasId} clearable filterable collapse-tags class="monitor-select">
                {this.aliasDictList.map((item) => {
                  return <el-option key={item.id + ''} label={item.paramAlias + '(' + item.paramName + ')'} value={item.id}></el-option>
                })}
              </el-select>
            )
          }
        },
        {
          prop: 'operation',
          label: '操作',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span class="operationBtn-span" onClick={() => this.handleParameterEvent('edit', row.row, 'control')}>
                  修改
                </span>
                <span class="operationBtn-span" onClick={() => this.handleParameterEvent('del', row.row, 'control')}>
                  删除
                </span>
              </div>
            )
          }
        }
      ],
      scadaParameterList: [],
      aliasDictList: [],
      monitorData: {}
    }
  },
  activated() {
    this.initEvent()
  },
  mounted() {
    if (!this.$store.state.keepAlive.list.includes(this.$route.path.split('/')[2])) {
      this.initEvent()
    }
  },
  methods: {
    // 接口数据初始化
    initEvent() {
      // 重置form表单
      const data = this.$options.data.call(this)
      delete data.rules
      delete data.tableColumn
      this.$route.query.isiId ? this.getImageSelectById(this.$route.query.isiId, 'scadaParameterList') : ''
      this.$nextTick(() => {
        for (const key in data) {
          this.$data[key] = data[key]
        }
        this.setType = this.$route.query.type
        this.monitorData = monitorTypeList.find((e) => e.projectCode == this.$route.query.projectCode)
        this.tableColumn.find(item => item.prop === 'istId').hasJudge = this.monitorData.hasScada === true
        this.getDictionaryList()
        // 设置部分监测项专属字段
        const assignData = this.setExclusiveField('func')
        for (const key in assignData) {
          const setData = { ...this.$data[key], ...assignData[key] }
          this.$set(this.$data, key, setData)
        }
        this.$refs.formInline.resetFields()
        this.$refs.formInline.clearValidate()
        // this.$focusUpdate()
      })
    },
    getDictionaryList() {
      // 数据主机
      this.dataServer = JSON.parse(sessionStorage.getItem('dataServer'))
      let data = {
        dictType: 1,
        projectCode: this.$route.query.projectCode
      }
      this.$api.getDictionaryList(data).then((res) => {
        this.parameterList = res.data
        this.setType != 'add' ? this.getSurveyByOne() : ''
      })
      // 获取实体类型数据
      this.$api
        .getDictionaryList({
          dictType: 12
        })
        .then((res) => {
          // 获取实体类型数据
          if (res.code == 200) {
            this.entityTypeList = res.data
          }
        })
      // 获取监测项目别名列表
      this.$api.getAliasDictList().then(res => {
        this.aliasDictList = res.data
      })
    },
    // 实体类型改变
    entityChang() {
      this.entityTypeName = this.entityTypeList.find((item) => item.id == this.formInline.entityTypeId)?.name ?? ''
    },
    // 获取监测项详情
    getSurveyByOne() {
      this.formLoading = true
      const userInfo = this.$store.state.user.userInfo.user
      this.$api
        .getSurveyParameterOne({
          surveyCode: this.$route.query.sensorCode,
          userId: userInfo.staffId,
          userName: userInfo.staffName
        })
        .then((res) => {
          this.tableData = res.data.parameterList ?? []
          this.tableControlData = res.data.controlList ?? []
          this.formInline.sensorName = res.data.sensorName + (this.setType == 'copy' ? '副本' : '')
          this.formInline.sensorNo = res.data.sensorNo
          this.formInline.height = res.data.height
          this.formInline.initialState = res.data.initialState
          this.formInline.entityTypeId = res.data.entityTypeId
          this.entityTypeName = res.data.entityTypeName
          this.assetsData.assetsId = res.data.assetId
          this.assetsData.assetName = res.data.assetName
          this.assetsData.regionName = res.data.regionName
          this.assetsData.regionCode = res.data.regionCode
          this.assetsData.assetsNumber = res.data.assetsNumber
          this.assetsData.assetTypeName = res.data.assetTypeName
          this.assetsData.assetTypeId = res.data.assetTypeId
          this.assetsData.assetCategoryName = res.data.assetCategoryName
          this.assetsData.assetCategoryCode = res.data.assetCategoryCode
          this.assetsData.modelCode = res.data.modelCode
          const imsVidiconId = res.data.imsVidiconId ? res.data.imsVidiconId.split(',') : []
          const imsVidiconName = res.data.imsVidiconName ? res.data.imsVidiconName.split(',') : []
          this.imsVidiconList =
            imsVidiconId?.map((item, index) => {
              return {
                imsVidiconId: item,
                imsVidiconName: imsVidiconName[index]
              }
            }) ?? []
          this.formLoading = false
        })
    },
    // 监测实体保存提交
    submitForm() {
      this.$refs.formInline.validate((valid) => {
        if (valid) {
          if (this.tableData.length == 0) {
            this.$message.error('请添加监测参数配置')
            return
          }
          this.formLoading = true
          const userInfo = this.$store.state.user.userInfo.user
          let reqInsertSurvey = {
            userId: userInfo.staffId,
            userName: userInfo.staffName,
            entityMenuCode: this.$route.query.entityMenuCode,
            projectCode: this.$route.query.projectCode, // 项目编号
            sensorCode: this.$route.query.sensorCode,
            sensorName: this.formInline.sensorName, // 监测实体名称
            sensorNo: this.formInline.sensorNo, // 监测项编号
            entityTypeName: this.entityTypeName, // 实体类型名称
            entityTypeId: this.formInline.entityTypeId, // 实体类型ID
            ...this.assetsData, // 关联资产设备
            assetId: this.assetsData.assetsId, // 资产设备ID
            imsVidiconId: Array.from(this.imsVidiconList, (e) => e.imsVidiconId).toString(), // 关联摄像头
            imsVidiconName: Array.from(this.imsVidiconList, (e) => e.imsVidiconName).toString(), // 关联摄像头
            parameterList: this.tableData.map((e) => {
              return {
                harvesterName: e.harvesterName,
                harvesterId: e.harvesterId,
                harvesterRealId: e.harvesterRealId,
                dataServerId: e.dataServerId,
                dataServerName: e.dataServerName,
                harvesterTypeId: e.harvesterTypeId,
                harvesterType: e.harvesterType,
                parameterId: e.parameterId,
                parameterName: e.parameterName,
                ispParamType: 11,
                dictAliasId: e.dictAliasId,
                unitName: e.unitName,
                unitId: e.unitId,
                istId: e.istId, // SCADA图形参数IDS
                state: e.state,
                ip: e.ip,
                port: e.port,
                paramSource: e.paramSource,
                projectCode: this.$route.query.projectCode // 项目编号
              }
            }),
            controlList: this.tableControlData.map((e) => {
              return {
                harvesterName: e.harvesterName,
                harvesterId: e.harvesterId,
                harvesterRealId: e.harvesterRealId,
                dataServerId: e.dataServerId,
                dataServerName: e.dataServerName,
                harvesterTypeId: e.harvesterTypeId,
                harvesterType: e.harvesterType,
                parameterId: e.parameterId,
                parameterName: e.parameterName,
                ispParamType: 10,
                dictAliasId: e.dictAliasId,
                unitName: e.unitName,
                unitId: e.unitId,
                istId: e.istId, // SCADA图形参数IDS
                state: e.state,
                ip: e.ip,
                port: e.port,
                paramSource: e.paramSource,
                projectCode: this.$route.query.projectCode // 项目编号
              }
            })
          }
          Object.assign(reqInsertSurvey, this.setExclusiveField('value'))
          if (this.monitorData.projectName === '消防水箱') {
            Object.assign(reqInsertSurvey, {
              height: this.formInline.height // 水箱高度
            })
          } else if (this.monitorData.projectName === '防火门') {
            Object.assign(reqInsertSurvey, {
              initialState: this.formInline.initialState // 初始状态门
            })
          }
          if (this.setType == 'add' || this.setType == 'copy') {
            // 新增
            this.$api.insertSurveyAndParameter(reqInsertSurvey, { 'operation-type': 1 }).then((res) => {
              this.formLoading = false
              if (res.code == '200') {
                this.$message.success(res.message)
                this.$refs.formInline.resetFields()
                this.$router.go(-1)
              } else {
                this.$message.error(res.message)
              }
            })
          } else {
            this.$api.updateSurveyAndParameter(reqInsertSurvey, { 'operation-type': 2, 'operation-id': reqInsertSurvey.sensorCode, 'operation-name': reqInsertSurvey.sensorName }).then((res) => {
              this.formLoading = false
              if (res.code == 200) {
                this.$message.success(res.message)
                this.$refs.formInline.resetFields()
                this.$router.go(-1)
              } else {
                this.$message.error(res.message)
              }
            })
          }
        } else {
          return false
        }
      })
    },
    // 监测实体列合并
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      const dataProvider = this.tableData
      const cellValue = row[column.property]
      const cellIndexList = [0, 1, 2]
      if (cellValue || cellIndexList.includes(columnIndex)) {
        if (column.label == '传感器名称' || cellIndexList.includes(columnIndex)) {
          // 上一条数据
          const prevRow = dataProvider[rowIndex - 1]
          // 下一条数据
          let nextRow = dataProvider[rowIndex + 1]
          // 当上一条数据等于下一条数据
          if (prevRow && prevRow[column.property] === cellValue) {
            return { rowspan: 0, colspan: 0 }
          } else {
            let rowspan = 1
            while (nextRow && nextRow[column.property] === cellValue) {
              rowspan++
              nextRow = dataProvider[rowspan + rowIndex]
            }
            if (rowspan > 1) {
              return { rowspan, colspan: 1 }
            }
          }
        }
      }
    },
    getImageSelectById(id, params) {
      this.$api.getImageSelectById({ id: id }).then((res) => {
        if (res.code == 200) {
          this[params] = res.data
        }
      })
    },
    // 添加参数
    addrow(parameterType) {
      this.operationType = parameterType
      this.selectHarvesterDialogData = { eventType: 'add' }
      this.harvesterDialogShow = true
    },
    // 监测项事件
    handleParameterEvent(type, row, parameterType) {
      this.operationType = parameterType
      if (this.operationType === 'monitor') {
        if (type === 'del') {
          const filterData = this.tableData.filter((e) => !(e.harvesterRealId === row.harvesterRealId && e.parameterId === row.parameterId))
          this.tableData = filterData
        } else {
          // radio 选中的参数id
          const parameterIdsList = Array.from(
            this.tableData.filter((e) => e.harvesterRealId === row.harvesterRealId),
            (e) => e.parameterId
          )
          this.selectHarvesterDialogData = Object.assign(row, { eventType: 'edit', parameterIdsList })
          this.harvesterDialogShow = true
        }
      } else if (this.operationType === 'control') {
        if (type === 'del') {
          const filterData = this.tableControlData.filter((e) => !(e.harvesterRealId === row.harvesterRealId && e.parameterId === row.parameterId))
          this.tableControlData = filterData
        } else {
          // radio 选中的参数id
          const parameterIdsList = Array.from(
            this.tableControlData.filter((e) => e.harvesterRealId === row.harvesterRealId),
            (e) => e.parameterId
          )
          this.selectHarvesterDialogData = Object.assign(row, { eventType: 'edit', parameterIdsList })
          this.harvesterDialogShow = true
        }
      }
    },
    // 设置部分监测项专属字段
    setExclusiveField(type = 'func') {
      if (this.monitorData.projectName === '消防水箱') {
        if (type == 'value') {
          return {
            height: this.formInline.height // 水箱高度
          }
        } else if (type == 'func') {
          return {
            rules: {
              height: [
                {
                  required: true,
                  message: '请输入水箱高度',
                  trigger: 'change'
                }
              ] // 水箱高度
            },
            formInline: {
              height: '' // 水箱高度
            }
          }
        }
      } else if (this.monitorData.projectName === '防火门') {
        if (type == 'value') {
          return {
            initialState: this.formInline.initialState // 初始状态门
          }
        } else if (type == 'func') {
          return {
            rules: {
              initialState: [
                {
                  required: true,
                  message: '请选择初始状态门数',
                  trigger: 'change'
                }
              ] // 初始状态门
            },
            formInline: {
              initialState: '' // 初始状态门
            }
          }
        }
      } else {
        return {}
      }
    },
    // 摄像机tag事件
    tagHandleClose(id) {
      // 根据id 删除imsVidiconList中的对应项
      const index = this.imsVidiconList.findIndex((e) => e.imsVidiconId === id)
      this.imsVidiconList.splice(index, 1)
    },
    // 设备资产tag事件
    delAssetsData() {
      Object.assign(this.assetsData, {
        assetsId: '', // 资产ID
        assetName: '', // 资产名称
        regionName: '', // 区域名称
        regionCode: '', // 区域ID
        modelCode: '', // 模型ID
        assetsNumber: '', // 资产编码
        assetTypeName: '', // 系统类别
        assetTypeId: '', // 系统类别ID
        assetCategoryName: '', // 专业类别
        assetCategoryCode: '' // 专业类别ID
      })
    },
    // 弹窗事件------------start
    // 摄像机
    associatedCamera() {
      this.cameraDialogData = Array.from(this.imsVidiconList, ({ imsVidiconId }) => imsVidiconId).toString()
      this.cameraDialogShow = true
    },
    // 设备资产
    chooseAssets(type) {
      // 关联
      this.assetsClickType = type
      if (type === 'relation') {
        this.equipmentAssetsDialogData = JSON.parse(JSON.stringify(this.assetsData))
      }
      this.equipmentAssetsDialogShow = true
    },
    submitMonitoringParamsGather(data) {
      this.equipmentAssetsDialogData = {}
      Object.assign(this, data)
    },
    // 弹窗事件------------end
    submitHarvesterDialog(data) {
      // 将监测参数抽出赋值在传感器数据上
      // tableData中与data中相同的传感器id数据过滤掉
      if (this.operationType === 'monitor') {
        let newSplitData = []
        let newSetTableData = this.tableData
        data.forEach((e) => {
          newSetTableData = newSetTableData.filter((item) => item.harvesterRealId !== e.harvesterRealId && this.selectHarvesterDialogData.harvesterRealId !== item.harvesterRealId)
          e.parameterList.forEach((item) => {
            newSplitData.push({
              ...e,
              ...item,
              parameterList: null,
              isdId: "",
              istId: '',
              dictAliasId: '',
            })
          })
        })
        let concatData = [...newSplitData, ...newSetTableData]
        this.tableData = concatData
      } else if (this.operationType === 'control') {
        let newSplitData = []
        let newSetTableData = this.tableControlData
        data.forEach((e) => {
          newSetTableData = newSetTableData.filter((item) => item.harvesterRealId !== e.harvesterRealId && this.selectHarvesterDialogData.harvesterRealId !== item.harvesterRealId)
          e.parameterList.forEach((item) => {
            newSplitData.push({
              ...e,
              ...item,
              parameterList: null,
              isdId: "",
              istId: '',
              dictAliasId: '',
            })
          })
        })
        let concatData = [...newSplitData, ...newSetTableData]
        this.tableControlData = concatData
      }
      this.harvesterDialogShow = false
    },
    closeHarvesterDialog() {
      this.harvesterDialogShow = false
    }
  }
}
</script>
<style lang="scss" scoped>
.footer-role ::v-deep .el-input__inner {
  padding-right: 50px;
}

.form-content {
  height: calc(100% - 0px);
  overflow-y: auto;

  .form-title {
    height: 40px;
    line-height: 40px;
    background: #fff;
    border-bottom: 1px solid #dcdfe6;
    font-size: 15px;
    font-family: 'PingFang SC-Medium', 'PingFang SC';
    font-weight: 500;
    color: #121f3e;
    padding-left: 15px;
  }

  .box-card {
    padding-left: 3%;
    margin-bottom: 3px;
  }

  .form-btn-title {
    font-size: 14px;
    font-family: 'PingFang SC-Regular', 'PingFang SC';
    font-weight: 400;
    color: #121f3e;
    margin-right: 8px;
  }

  .form-btn-btn {
    padding: 4px 10px;
    margin: 0 8px;
  }

  .assets-info {
    font-size: 14px;
    font-family: 'PingFang SC-Regular', 'PingFang SC';
    font-weight: 400;
    color: #121f3e;
    margin-right: 15px;

    >span {
      color: #3562db;
    }
  }

  .assets-info-close {
    cursor: pointer;

    &:hover {
      color: #3562db;
      font-weight: 600;
    }
  }

  .parameter-box {
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    margin-bottom: 20px;

    .parameter-title {
      font-size: 14px;
      font-family: 'PingFang SC-Regular', 'PingFang SC';
      font-weight: 400;
      color: #121f3e;
      background: #faf9fc;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 48px;
      line-height: 48px;
      padding: 0 10px;

      &>span {
        &:first-child {
          font-size: 16px;
        }
      }
    }

    .unit-style {
      font-size: 14px;
      font-family: 'PingFang SC-Regular', 'PingFang SC';
      font-weight: 400;
      color: #121f3e;
      height: 40px;
      line-height: 40px;
    }
  }

  ::v-deep .el-select,
  .el-input {
    width: fit-content;
  }

  .camera-tag {
    background: #f6f5fa;
    border-radius: 4px;
    font-size: 14px;
    font-family: 'PingFang SC-Regular', 'PingFang SC';
    font-weight: 400;
    color: #121f3e;
    border: none;
    margin-right: 8px;

    ::v-deep .el-tag__close {
      color: #121f3e;

      &:hover {
        color: #fff;
        background-color: #3562db;
      }
    }
  }
}
</style>
