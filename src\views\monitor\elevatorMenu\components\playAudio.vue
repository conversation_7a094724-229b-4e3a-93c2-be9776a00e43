<template>
  <div class="play-audio">
    <el-dialog title="提示" :visible.sync="messageDialogShow" width="30%"
      :before-close="() => (messageDialogShow = false)">
      <span>这是一段信息</span>
      <span slot="footer" class="dialog-footer">
        <!-- <el-button @click="dialogVisible = false">取 消</el-button> -->
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      ws: null,
      mediaStack: null,
      audioCtx: null,
      scriptNode: null,
      source: null,
      play: true,
      messageDialogShow: false
    }
  },
  mounted() {
    // this.startCall()
  },
  methods: {
    initWebsocket() {
      // 连接 websocket,边端发送音频流
      const wsUrl = 'ws://**************:12306/socket/************'
      this.ws = new WebSocket(wsUrl)
      this.ws.onopen = () => {
        console.log('socket 已连接')
      }
      this.ws.binaryType = 'arraybuffer'
      this.ws.onmessage = ({ data }) => {
        console.log('录音接收中...', data)
        this.listenerEvent()
      }
      this.ws.onerror = (e) => {
        console.log('error', e)
      }
      this.ws.onclose = function (e) {
        this.ws = null
        console.log('socket closed')
      }
    },
    // 开始对讲
    startCall() {
      this.play = true
      this.audioCtx = new (window.AudioContext || window.webkitAudioContext)({
        sampleRate: 8000 // 设置采样率
      })
      // 该变量存储当前MediaStreamAudioSourceNode的引用
      // 可以通过它关闭麦克风停止音频传输
      // 创建一个ScriptProcessorNode 用于接收当前麦克风的音频
      this.scriptNode = this.audioCtx.createScriptProcessor(4096, 1, 1)
      if (navigator.mediaDevices) {
        this.initWebsocket()
        navigator.mediaDevices
          .getUserMedia({ audio: true, video: false })
          .then((stream) => {
            this.mediaStack = stream
            this.mediaStack.stop = function () {
              this.getAudioTracks().forEach(function (track) {
                track.stop()
              })
              // this.getVideoTracks().forEach(function (track) {
              //   track.stop()
              // })
            }
            this.source = this.audioCtx.createMediaStreamSource(stream)
            this.source.connect(this.scriptNode)
            this.scriptNode.connect(this.audioCtx.destination)
          })
          .catch(function (err) {
            // alert('出错，请确保已允许浏览器获取音视频权限')
            /* 处理error */
            console.log('err', err)
          })
      } else {
        // this.messageDialogShow = true
        this.$message.warning('录音权限开启失败，请先开启录音权限！')
      }
      // 当麦克风有声音输入时，会调用此事件
      // 实际上麦克风始终处于打开状态时，即使不说话，此事件也在一直调用
      this.scriptNode.onaudioprocess = (audioProcessingEvent) => {
        // q: 这生成的音频数据是什么格式的？是不是可以直接传输？
        // a: 生成的音频数据是Float32Array类型的，可以直接传输
        // q: 如何生成PCM格式的音频数据？
        // a: 通过AudioContext.createBuffer()方法创建一个空白的AudioBuffer对象，然后将Float32Array类型的数据赋值给AudioBuffer对象，这样就可以得到PCM格式的音频数据
        // q: 为什么要转换成Float32Array类型的数据？
        // a: 因为WebSocket传输的数据类型只支持ArrayBuffer和Blob，而ArrayBuffer是一个通用的二进制数据缓冲区，可以用来传输任意类型的数据，所以可以将G711格式的音频数据转换成Float32Array类型的数据，然后通过WebSocket传输
        // 获取PCM格式的音频数据
        const inputBuffer = audioProcessingEvent.inputBuffer
        // 由于只创建了一个音轨，这里只取第一个频道的数据
        const inputData = inputBuffer.getChannelData(0)
        const arrayBuffer = inputData.buffer
        let pcm16Array = new Int16Array(inputData.length)
        for (let i = 0; i < inputData.length; i++) {
          // 将Float32数据转换为16位整数，取值范围在[-32768, 32767]之间
          pcm16Array[i] = Math.max(-32768, Math.min(32767, inputData[i] * 32768))
        }
        let pcm32Array = new Float32Array(inputData.length)
        for (let i = 0; i < inputData.length; i++) {
          // 在这里可以进行其他音频处理，根据需要
          pcm32Array[i] = inputData[i]
        }
        // this.listenerEvent(pcm16Array)
        // 通过socket传输数据，实际上传输的是Float32Array
        if (this.ws.readyState === 1) {
          console.log('录音传输中...')
          this.ws.send(pcm16Array)
        }
      }
    },
    // 语音接收播放
    listenerEvent(data) {
      // const buffer = new Float32Array(data.length)
      // for (let i = 0; i < data.length; i++) {
      //   buffer[i] = data[i] / 32768.0
      // }
      // const buffer = new Float32Array(data.length)
      // for (let i = 0; i < data.length; i++) {
      //   buffer[i] = data[i]
      // }
      const buffer = new Float32Array(data)
      // 创建一个空白的AudioBuffer对象，这里的4096跟发送方保持一致，48000是采样率
      const myArrayBuffer = this.audioCtx.createBuffer(1, 4096, 48000)
      // 也是由于只创建了一个音轨，可以直接取到0
      const nowBuffering = myArrayBuffer.getChannelData(0)
      // 通过循环，将接收过来的数据赋值给简单音频对象
      for (let i = 0; i < 4096; i++) {
        nowBuffering[i] = buffer[i]
      }
      // 使用AudioBufferSourceNode播放音频
      const source = this.audioCtx.createBufferSource()
      source.buffer = myArrayBuffer
      const gainNode = this.audioCtx.createGain()
      source.connect(gainNode)
      gainNode.connect(this.audioCtx.destination)
      var muteValue = 1
      if (!this.play) {
        // 是否静音
        muteValue = 0
      }
      gainNode.gain.setValueAtTime(muteValue, this.audioCtx.currentTime)
      source.start()
    },
    // 关闭麦克风
    stopCall() {
      // debugger
      if (this.ws) {
        this.ws.close(1000)
        this.ws = null
      }
      this.play = false
      this.mediaStack.getTracks()[0].stop()
      this.scriptNode.disconnect()
    }
  }
}
</script>
