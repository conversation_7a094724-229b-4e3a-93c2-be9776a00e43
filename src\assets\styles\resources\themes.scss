@use 'sass:color';
$themes: (
  default: ( // 【主区域】
    // 背景色
    g-main-bg: #f0f2f5,
    // 【头部】
    // 背景色
    g-header-bg: #222b45,
    // 文字颜色
    g-header-color: #fff,
    // 导航文字颜色
    g-header-menu-color: #ccc,
    // 导航鼠标悬浮文字颜色
    g-header-menu-hover-color: #fff,
    // 导航鼠标悬浮背景色
    g-header-menu-hover-bg: #334067,
    // 导航选中文字颜色
    g-header-menu-active-color: #fff,
    // 导航选中背景色
    g-header-menu-active-bg: #334067,
    // 【主侧边栏】
    // 背景色
    g-main-sidebar-bg: #222b45,
    // 菜单文字颜色
    g-main-sidebar-menu-color: #ccc,
    // 菜单鼠标悬浮文字颜色
    g-main-sidebar-menu-hover-color: #fff,
    // 菜单鼠标悬浮背景色
    g-main-sidebar-menu-hover-bg: #334067,
    // 菜单选中文字颜色
    g-main-sidebar-menu-active-color: #fff,
    // 菜单选中背景色
    g-main-sidebar-menu-active-bg: #3562DB,
    // 【次侧边栏】
    // 背景色
    g-sub-sidebar-bg: #fafafa,
    // 菜单背景色
    g-sub-sidebar-menu-bg: color.adjust(#fafafa, $lightness: -7%),
    // 菜单文字颜色
    g-sub-sidebar-menu-color: #37414b,
    // 菜单鼠标悬浮文字颜色
    g-sub-sidebar-menu-hover-color: #37414b,
    // 菜单鼠标悬浮背景色
    g-sub-sidebar-menu-hover-bg: color.adjust(#fafafa, $lightness: -10%),
    // 菜单选中文字颜色
    g-sub-sidebar-menu-active-color: #e7f4ff,
    // 菜单选中背景色
    g-sub-sidebar-menu-active-bg: #5482ee,
    // 【导航标记】
    // 背景色
    g-badge-bg: #f56c6c,
    // 文字颜色
    g-badge-color: #fff,
    // 边框颜色
    g-badge-border-color:#fff,
    // 【标签栏】
    // 背景色
    g-tabbar-bg: #f0f2f5,
    // 分割线颜色
    g-tabbar-dividers-bg: #a9adb0,
    // 标签文字颜色
    g-tabbar-tab-color: #999,
    // 标签鼠标悬浮文字颜色
    g-tabbar-tab-hover-color: #999,
    // 标签鼠标悬浮背景色
    g-tabbar-tab-hover-bg: #dee1e6,
    // 标签选中文字颜色
    g-tabbar-tab-active-color: #97a8be,
    // 标签选中背景色
    g-tabbar-tab-active-bg: #fff),
  vue-cli: ( // 【主区域】
    // 背景色
    g-main-bg: #f0f2f5,
    // 【头部】
    // 背景色
    g-header-bg: #1d2935,
    // 文字颜色
    g-header-color: #fff,
    // 导航文字颜色
    g-header-menu-color: #fff,
    // 导航鼠标悬浮文字颜色
    g-header-menu-hover-color: #42b983,
    // 导航鼠标悬浮背景色
    g-header-menu-hover-bg: #1f3039,
    // 导航选中文字颜色
    g-header-menu-active-color: #42b983,
    // 导航选中背景色
    g-header-menu-active-bg: #1f3039,
    // 【主侧边栏】
    // 背景色
    g-main-sidebar-bg: #1d2935,
    // 菜单文字颜色
    g-main-sidebar-menu-color: #fff,
    // 菜单鼠标悬浮文字颜色
    g-main-sidebar-menu-hover-color: #42b983,
    // 菜单鼠标悬浮背景色
    g-main-sidebar-menu-hover-bg: #1f3039,
    // 菜单选中文字颜色
    g-main-sidebar-menu-active-color: #42b983,
    // 菜单选中背景色
    g-main-sidebar-menu-active-bg: #1f3039,
    // 【次侧边栏】
    // 背景色
    g-sub-sidebar-bg: #2c3e50,
    // 菜单背景色
    g-sub-sidebar-menu-bg: color.adjust(#2c3e50, $lightness: -7%),
    // 菜单文字颜色
    g-sub-sidebar-menu-color: #fff,
    // 菜单鼠标悬浮文字颜色
    g-sub-sidebar-menu-hover-color: #fff,
    // 菜单鼠标悬浮背景色
    g-sub-sidebar-menu-hover-bg: color.adjust(#2c3e50, $lightness: -10%),
    // 菜单选中文字颜色
    g-sub-sidebar-menu-active-color: #11c2e6,
    // 菜单选中背景色
    g-sub-sidebar-menu-active-bg: #2e4854,
    // 【导航标记】
    // 背景色
    g-badge-bg: #f56c6c,
    // 文字颜色
    g-badge-color: #fff,
    // 边框颜色
    g-badge-border-color:#fff,
    // 【标签栏】
    // 背景色
    g-tabbar-bg: #f0f2f5,
    // 标签之间分割线颜色
    g-tabbar-dividers-bg: #a9adb0,
    // 标签文字颜色
    g-tabbar-tab-color: #999,
    // 标签鼠标悬浮文字颜色
    g-tabbar-tab-hover-color: #999,
    // 标签鼠标悬浮背景色
    g-tabbar-tab-hover-bg: #dee1e6,
    // 标签选中文字颜色
    g-tabbar-tab-active-color: #97a8be,
    // 标签选中背景色
    g-tabbar-tab-active-bg: #fff),
  gitee: ( // 【主区域】
    // 背景色
    g-main-bg: #f0f2f5,
    // 【头部】
    // 背景色
    g-header-bg: #303643,
    // 文字颜色
    g-header-color: #fff,
    // 导航文字颜色
    g-header-menu-color: #d6d7d9,
    // 导航鼠标悬浮文字颜色
    g-header-menu-hover-color: #fff,
    // 导航鼠标悬浮背景色
    g-header-menu-hover-bg: #40485b,
    // 导航选中文字颜色
    g-header-menu-active-color: #fff,
    // 导航选中背景色
    g-header-menu-active-bg: #40485b,
    // 【主侧边栏】
    // 背景色
    g-main-sidebar-bg: #303643,
    // 菜单文字颜色
    g-main-sidebar-menu-color: #d6d7d9,
    // 菜单鼠标悬浮文字颜色
    g-main-sidebar-menu-hover-color: #fff,
    // 菜单鼠标悬浮背景色
    g-main-sidebar-menu-hover-bg: #40485b,
    // 菜单选中文字颜色
    g-main-sidebar-menu-active-color: #fff,
    // 菜单选中背景色
    g-main-sidebar-menu-active-bg: #40485b,
    // 【次侧边栏】
    // 背景色
    g-sub-sidebar-bg: #f6f6f6,
    // 菜单背景色
    g-sub-sidebar-menu-bg: color.adjust(#f6f6f6, $lightness: -7%),
    // 菜单文字颜色
    g-sub-sidebar-menu-color: #40485b,
    // 菜单鼠标悬浮文字颜色
    g-sub-sidebar-menu-hover-color: #40485b,
    // 菜单鼠标悬浮背景色
    g-sub-sidebar-menu-hover-bg: color.adjust(#f6f6f6, $lightness: -10%),
    // 菜单选中文字颜色
    g-sub-sidebar-menu-active-color: #f6f6f6,
    // 菜单选中背景色
    g-sub-sidebar-menu-active-bg: #fe7300,
    // 【导航标记】
    // 背景色
    g-badge-bg: #f56c6c,
    // 文字颜色
    g-badge-color: #fff,
    // 边框颜色
    g-badge-border-color:#fff,
    // 【标签栏】
    // 背景色
    g-tabbar-bg: #f0f2f5,
    // 标签之间分割线颜色
    g-tabbar-dividers-bg: #a9adb0,
    // 标签文字颜色
    g-tabbar-tab-color: #999,
    // 标签鼠标悬浮文字颜色
    g-tabbar-tab-hover-color: #999,
    // 标签鼠标悬浮背景色
    g-tabbar-tab-hover-bg: #dee1e6,
    // 标签选中文字颜色
    g-tabbar-tab-active-color: #97a8be,
    // 标签选中背景色
    g-tabbar-tab-active-bg: #fff),
  freshness: ( // 【主区域】
    // 背景色
    g-main-bg: #f3f0f3,
    // 【头部】
    // 背景色
    g-header-bg: #81c1b2,
    // 文字颜色
    g-header-color: #fff,
    // 导航文字颜色
    g-header-menu-color: #f3f0f3,
    // 导航鼠标悬浮文字颜色
    g-header-menu-hover-color: #8f75a2,
    // 导航鼠标悬浮背景色
    g-header-menu-hover-bg: #b6e9d5,
    // 导航选中文字颜色
    g-header-menu-active-color: #8f75a2,
    // 导航选中背景色
    g-header-menu-active-bg: #b6e9d5,
    // 【主侧边栏】
    // 背景色
    g-main-sidebar-bg: #81c1b2,
    // 菜单文字颜色
    g-main-sidebar-menu-color: #f3f0f3,
    // 菜单鼠标悬浮文字颜色
    g-main-sidebar-menu-hover-color: #8f75a2,
    // 菜单鼠标悬浮背景色
    g-main-sidebar-menu-hover-bg: #b6e9d5,
    // 菜单选中文字颜色
    g-main-sidebar-menu-active-color: #8f75a2,
    // 菜单选中背景色
    g-main-sidebar-menu-active-bg: #b6e9d5,
    // 【次侧边栏】
    // 背景色
    g-sub-sidebar-bg: #b6e9d5,
    // 菜单背景色
    g-sub-sidebar-menu-bg: color.adjust(#b6e9d5, $lightness: -7%),
    // 菜单文字颜色
    g-sub-sidebar-menu-color: #8b91ab,
    // 菜单鼠标悬浮文字颜色
    g-sub-sidebar-menu-hover-color: #8b91ab,
    // 菜单鼠标悬浮背景色
    g-sub-sidebar-menu-hover-bg: color.adjust(#b6e9d5, $lightness: -10%),
    // 菜单选中文字颜色
    g-sub-sidebar-menu-active-color: #5c6074,
    // 菜单选中背景色
    g-sub-sidebar-menu-active-bg: #feefc6,
    // 【导航标记】
    // 背景色
    g-badge-bg: #f56c6c,
    // 文字颜色
    g-badge-color: #fff,
    // 边框颜色
    g-badge-border-color:#fff,
    // 【标签栏】
    // 背景色
    g-tabbar-bg: #f0f2f5,
    // 标签之间分割线颜色
    g-tabbar-dividers-bg: #a9adb0,
    // 标签文字颜色
    g-tabbar-tab-color: #999,
    // 标签鼠标悬浮文字颜色
    g-tabbar-tab-hover-color: #999,
    // 标签鼠标悬浮背景色
    g-tabbar-tab-hover-bg: #dee1e6,
    // 标签选中文字颜色
    g-tabbar-tab-active-color: #97a8be,
    // 标签选中背景色
    g-tabbar-tab-active-bg: #fff),
  elegant: ( // 【主区域】
    // 背景色
    g-main-bg: #f5f7f9,
    // 【头部】
    // 背景色
    g-header-bg: #736477,
    // 文字颜色
    g-header-color: #fff,
    // 导航文字颜色
    g-header-menu-color: #fff,
    // 导航鼠标悬浮文字颜色
    g-header-menu-hover-color: #fff,
    // 导航鼠标悬浮背景色
    g-header-menu-hover-bg: #8d7d91,
    // 导航选中文字颜色
    g-header-menu-active-color: #fff,
    // 导航选中背景色
    g-header-menu-active-bg: #8d7d91,
    // 【主侧边栏】
    // 背景色
    g-main-sidebar-bg: #736477,
    // 菜单文字颜色
    g-main-sidebar-menu-color: #fff,
    // 菜单鼠标悬浮文字颜色
    g-main-sidebar-menu-hover-color: #fff,
    // 菜单鼠标悬浮背景色
    g-main-sidebar-menu-hover-bg: #8d7d91,
    // 菜单选中文字颜色
    g-main-sidebar-menu-active-color: #fff,
    // 菜单选中背景色
    g-main-sidebar-menu-active-bg: #8d7d91,
    // 【次侧边栏】
    // 背景色
    g-sub-sidebar-bg: #ddcdcd,
    // 菜单背景色
    g-sub-sidebar-menu-bg: color.adjust(#ddcdcd, $lightness: -7%),
    // 菜单文字颜色
    g-sub-sidebar-menu-color: #89768f,
    // 菜单鼠标悬浮文字颜色
    g-sub-sidebar-menu-hover-color: #89768f,
    // 菜单鼠标悬浮背景色
    g-sub-sidebar-menu-hover-bg: color.adjust(#ddcdcd, $lightness: -10%),
    // 菜单选中文字颜色
    g-sub-sidebar-menu-active-color:  color.adjust(#89768f, $lightness: -30%),
    // 菜单选中背景色
    g-sub-sidebar-menu-active-bg: #b5a5a5,
    // 【导航标记】
    // 背景色
    g-badge-bg: #f56c6c,
    // 文字颜色
    g-badge-color: #fff,
    // 边框颜色
    g-badge-border-color:#fff,
    // 【标签栏】
    // 背景色
    g-tabbar-bg: #f0f2f5,
    // 标签之间分割线颜色
    g-tabbar-dividers-bg: #a9adb0,
    // 标签文字颜色
    g-tabbar-tab-color: #999,
    // 标签鼠标悬浮文字颜色
    g-tabbar-tab-hover-color: #999,
    // 标签鼠标悬浮背景色
    g-tabbar-tab-hover-bg: #dee1e6,
    // 标签选中文字颜色
    g-tabbar-tab-active-color: #97a8be,
    // 标签选中背景色
    g-tabbar-tab-active-bg: #fff),
  pure-white: ( // 【主区域】
    // 背景色
    g-main-bg: #f5f7f9,
    // 【头部】
    // 背景色
    g-header-bg: #fff,
    // 文字颜色
    g-header-color: #595959,
    // 导航文字颜色
    g-header-menu-color: #595959,
    // 导航鼠标悬浮文字颜色
    g-header-menu-hover-color: #1890ff,
    // 导航鼠标悬浮背景色
    g-header-menu-hover-bg: #e6f7ff,
    // 导航选中文字颜色
    g-header-menu-active-color: #fff,
    // 导航选中背景色
    g-header-menu-active-bg: #1890ff,
    // 【主侧边栏】
    // 背景色
    g-main-sidebar-bg: #e6f7ff,
    // 菜单文字颜色
    g-main-sidebar-menu-color: #595959,
    // 菜单鼠标悬浮文字颜色
    g-main-sidebar-menu-hover-color: #1890ff,
    // 菜单鼠标悬浮背景色
    g-main-sidebar-menu-hover-bg: #e6f7ff,
    // 菜单选中文字颜色
    g-main-sidebar-menu-active-color: #fff,
    // 菜单选中背景色
    g-main-sidebar-menu-active-bg: #1890ff,
    // 【次侧边栏】
    // 背景色
    g-sub-sidebar-bg: #fff,
    // 菜单背景色
    g-sub-sidebar-menu-bg: color.adjust(#fff, $lightness: 0%),
    // 菜单文字颜色
    g-sub-sidebar-menu-color: #595959,
    // 菜单鼠标悬浮文字颜色
    g-sub-sidebar-menu-hover-color: #1890ff,
    // 菜单鼠标悬浮背景色
    g-sub-sidebar-menu-hover-bg: #fff,
    // 菜单选中文字颜色
    g-sub-sidebar-menu-active-color: #1890ff,
    // 菜单选中背景色
    g-sub-sidebar-menu-active-bg: #e6f7ff,
    // 【导航标记】
    // 背景色
    g-badge-bg: #f56c6c,
    // 文字颜色
    g-badge-color: #fff,
    // 边框颜色
    g-badge-border-color:#fff,
    // 【标签栏】
    // 背景色
    g-tabbar-bg: #f0f2f5,
    // 标签之间分割线颜色
    g-tabbar-dividers-bg: #a9adb0,
    // 标签文字颜色
    g-tabbar-tab-color: #999,
    // 标签鼠标悬浮文字颜色
    g-tabbar-tab-hover-color: #999,
    // 标签鼠标悬浮背景色
    g-tabbar-tab-hover-bg: #dee1e6,
    // 标签选中文字颜色
    g-tabbar-tab-active-color: #97a8be,
    // 标签选中背景色
    g-tabbar-tab-active-bg: #fff)
);
$theme-map: (
);

@mixin themeify() {

  @each $theme-name,
  $map in $themes {
    $theme-map: $map  !global;

    [data-theme=#{$theme-name}] & {
      @content;
    }
  }
}

@function themed($key) {
  @return map-get($theme-map, $key);
}
