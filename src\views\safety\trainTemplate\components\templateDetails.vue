<template>
  <PageContainer v-loading="contentLoading">
    <div slot="header" class="table-content">
      <div class="topFilter">
        <div class="backBar">
          <span style="cursor: pointer" @click="$router.go(-1)">
            <i class="el-icon-arrow-left"></i>
            课程详情
          </span>
        </div>
      </div>
      <div class="baseInfo">
        <h1>基础信息</h1>
        <div class="contenter">
          <div class="itemInfo">
            <span class="title">培训模板名称：</span>
            <span class="value">{{ dataList.name }}</span>
          </div>
          <div class="itemInfo">
            <span class="title">培训模板描述：</span>
            <span class="value">{{ dataList.disc }}</span>
          </div>
        </div>
      </div>
    </div>
    <div slot="content" class="courseContent">
      <div class="top">
        <h1>培训课件</h1>
        <span @click="allDownload">全部下载</span>
      </div>
      <div class="table_content">
        <div v-for="(k, index) in trainList" :key="k.id" class="item">
          <div>
            <img src="../../../../assets/images/doc.png" alt="" />
            <span>{{ index + 1 }}.{{ k.originalFilename }}</span>
          </div>
          <div class="operate">
            <span @click="ckeckFile(k)">查看</span>
            <span @click="download(k)">下载</span>
          </div>
        </div>
      </div>
    </div>
  </PageContainer>
</template>
<script>
  import moment from "moment";
  import axios from 'axios'
  export default {
    data() {
      return {
        contentLoading: false,
        routeInfo: "",
        moment,
        id: "",
        examine: '', // 查看文件状态
        dataList: {}, // 模板信息
        trainList: [], // 模板课件
        coursewareList: [{
          id: '0',
          name: '课件名称'
        }],
        drawerDialog: false,
        paginationData: {
          pageNo: 1,
          pageSize: 15,
          total: 0
        },
        idsy: [],
      };
    },
    created() {
      // this.routeInfo = JSON.parse(sessionStorage.getItem("routeInfo"));
      this.routeInfo = JSON.parse(sessionStorage.getItem('routeInfo'))
      this.id = this.$route.query.id
      if (this.id) {
        this.getDetails();
      }
    },
    methods: {
      // 获取详情
      getDetails() {
        this.contentLoading = true;
        this.$api.tarainTemplateDetail({
          id: this.id
        }).then(res => {
          this.dataList = res.data
          this.trainList = res.data.fileUploadRecords
        })
        this.contentLoading = false;
      },
      // 查看文件
      ckeckFile(k) {
        console.log('JINLAILE');

        console.log(k, 'k');
        this.$router.push({
          path: 'seeTrainFile',
          query: {
            url: k.viewAddress,
            name: k.originalFilename
          }
        })
      },
      // 下载试题类型模板
      download(k) {
        this.idsy = []
        this.idsy.push(k.id)
        let params = {
          ids: this.idsy
        }
        console.log(params, 'PARAMS789');
        let httpname = '/minio/downloadBatch'
        axios({
            method: 'post',
            url: __PATH.BASE_URL_LABORATORY + httpname,
            data: params,
            responseType: 'blob',
            headers: {
              "Content-Type": "application/json",
            }
          })
          .then((res) => {
            console.log(JSON.stringify(res.data));
            let name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
            let blob = new Blob([res.data]) // { type: "application/vnd.ms-excel" }
            let url = window.URL.createObjectURL(blob) // 创建一个临时的url指向blob对象
            // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
            let a = document.createElement('a')
            a.href = url
            a.download = decodeURI(name)
            a.click()
            // 释放这个临时的对象url
            window.URL.revokeObjectURL(url)
          })
          .catch((res) => {
            this.$message.error('下载失败！')
          })
      },
      // 全部下载
      allDownload() {
        let httpname = 'trainTmp/downloadCoursewares'
        let params = {
          id: this.dataList.id,
        }
        axios({
            method: 'post',
            url: __PATH.BASE_URL_LABORATORY + httpname,
            data: params,
            responseType: 'blob',
            headers: {
              "Content-Type": "application/json",
            }
          })
          .then((res) => {
            console.log(JSON.stringify(res.data));
            let name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
            let blob = new Blob([res.data]) // { type: "application/vnd.ms-excel" }
            let url = window.URL.createObjectURL(blob) // 创建一个临时的url指向blob对象
            // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
            let a = document.createElement('a')
            a.href = url
            a.download = decodeURI(name)
            a.click()
            // 释放这个临时的对象url
            window.URL.revokeObjectURL(url)
          })
          .catch((res) => {
            this.$message.error('下载失败！')
          })
      },
      // 展开试题
      isExpandBtn(item, index) {
        item.isExpand = !item.isExpand;
      },
      handleSizeChange() {},
      handleCurrentChange() {}
    },
  };

</script>
<style lang="scss" scoped>
  .table-content {
    background: #fff;
    border-radius: 4px;
    height: calc(100% - 0px);

    .baseInfo {
      padding: 0 24px 24px 24px;

      .contenter {
        padding-top: 24px;
        font-size: 14px;

        .itemInfo {
          margin-bottom: 16px;
          display: flex;

          .title {
            width: 120px;
            color: #666;
            margin-top: 3px;
          }

          .value {
            flex: 1;
            line-height: 20px;
          }
        }
      }
    }
  }

  .topFilter {
    padding: 15px;
    height: 60px;
    background-color: #fff;

    .backBar {
      color: #121f3e;
      height: 30px;
      border-bottom: 1px solid #dcdfe6;
    }
  }

  .courseContent {
    height: 100%;
    margin-top: 16px;
    background-color: #fff;
    padding: 16px;

    .top {
      margin-bottom: 20px;
      display: flex;
      justify-content: space-between;

      span {
        font-size: 14px;
        color: #3562DB;
      }
    }

    .table_content {
      height: calc(100% - 70px);
      overflow: auto;

      .item {
        height: 56px;
        font-size: 14px;
        line-height: 56px;
        display: flex;
        justify-content: space-between;

        img {
          vertical-align: middle;
          margin-right: 12px;
        }

        .operate {
          span {
            cursor: pointer;
            color: #3562DB;
            margin-right: 16px;
          }
        }

      }
    }
  }

  ::v-deep .el-radio {
    display: block;
    margin: 10px 0;
  }

  ::v-deep .el-checkbox {
    display: block;
    margin: 10px 0;
  }

  ::v-deep .el-checkbox-group {
    margin-left: 38px;
  }

</style>
