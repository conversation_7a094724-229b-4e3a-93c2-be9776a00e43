<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 150 150"><defs><style>.cls-1{fill:url(#未命名的渐变_9);}</style><radialGradient id="未命名的渐变_9" cx="75.01" cy="75.18" r="74.99" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#f7d7a7"/><stop offset="0.68" stop-color="#f5d8ac"/><stop offset="0.78" stop-color="#f4d8ad"/><stop offset="0.79" stop-color="#f5dab1"/><stop offset="0.87" stop-color="#f9e5c8"/><stop offset="0.94" stop-color="#fbecd6"/><stop offset="1" stop-color="#fceedb"/></radialGradient></defs><title>iot-按钮</title><g id="图层_46" data-name="图层 46"><circle class="cls-1" cx="75.01" cy="75.18" r="74.99"/></g></svg>