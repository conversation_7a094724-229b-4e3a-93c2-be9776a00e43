<template>
  <PageContainer class="leavePostRecord-list">
    <template #content>
      <div class="leavePostRecord-list-content">
        <el-form ref="formRef" class="leavePostRecord-list__form" :model="searchForm" inline>
          <el-form-item prop="startTime">
            <el-date-picker v-model="searchForm.startTime" type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="离岗开始时间"> </el-date-picker>
          </el-form-item>
          <el-form-item prop="endTime">
            <el-date-picker v-model="searchForm.endTime" type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="离岗结束时间"> </el-date-picker>
          </el-form-item>
          <el-form-item prop="name">
            <el-input v-model="searchForm.name" placeholder="搜索值班岗名称" suffix-icon="el-icon-search" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" plain @click="onReset">重置</el-button>
            <el-button type="primary" @click="onSearch">查询</el-button>
          </el-form-item>
        </el-form>
        <div class="leavePostRecord-list__table_actions">
          <el-button type="primary" @click="exportExcel">导出</el-button>
        </div>
        <div class="leavePostRecord-list__table">
          <TablePage
            ref="table"
            v-loading="tableLoading"
            :showPage="true"
            height="calc(100% - 0px)"
            border
            stripe
            row-key="id"
            :tableColumn="tableColumn"
            :data="tableData"
            :pageData="pagination"
            @pagination="paginationChange"
          />
        </div>
      </div>
    </template>
  </PageContainer>
</template>
<script>
import axios from 'axios'
export default {
  data() {
    return {
      tableLoading: false,
      tableData: [],
      tableColumn: [
        {
          prop: 'offlineStartTime',
          label: '离岗开始时间'
        },
        {
          prop: 'offlineEndTime',
          label: '离岗结束时间'
        },
        {
          prop: 'offlineTime',
          label: '离岗时间(分钟)'
        },
        {
          prop: 'dutyPostName',
          label: '值班岗'
        },
        {
          prop: 'locationName',
          label: '定位终端'
        },
        {
          prop: 'attendanceName',
          label: '离岗考勤组'
        },
        {
          prop: 'shiftName',
          label: '班次'
        },
        {
          prop: 'shiftTime',
          label: '时段'
        }
      ],
      // 选中的行
      selectionList: [],
      pagination: {
        page: 1,
        pageSize: 15,
        total: 0
      },
      searchForm: {
        name: '',
        startTime: '',
        endTime: ''
      }
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    onReset() {
      this.searchForm = { name: '', startTime: '', endTime: '' }
      this.onSearch()
    },
    onSearch() {
      this.pagination.page = 1
      this.getData()
    },
    // 导出
    exportExcel() {
      const userInfo = this.$store.state.user.userInfo.user
      let params = {
        dutyPostName: this.searchForm.name,
        startTime: this.searchForm.startTime,
        endTime: this.searchForm.endTime,
        page: this.pagination.page,
        pageSize: this.pagination.pageSize
      }
      axios({
        method: 'post',
        url: __PATH.SPACE_API + 'offlinePost/exportOfflineRecord',
        data: params,
        responseType: 'blob',
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token,
          'operation-type': 4,
          unitCode: userInfo.unitCode ?? 'BJSYGJ',
          hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY'
        }
      })
        .then((res) => {
          const name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
          const blob = new Blob([res.data])
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
        })
        .catch(() => {
          this.$message.error('下载失败')
        })
    },
    getData() {
      let params = {
        dutyPostName: this.searchForm.name,
        startTime: this.searchForm.startTime,
        endTime: this.searchForm.endTime,
        page: this.pagination.page,
        pageSize: this.pagination.pageSize
      }
      this.tableLoading = true
      this.$api.supplierAssess.getOfflineRecord(params).then((res) => {
        this.tableLoading = false
        if (res.code == 200) {
          this.tableData = res.data.records
          this.pagination.total = res.data.total
        } else {
          this.$message({
            message: res.msg,
            type: 'error'
          })
        }
      })
    },
    paginationChange(pagination) {
      Object.assign(this.pagination, pagination)
      this.getData()
    }
  }
}
</script>
<style lang="scss" scoped>
.leavePostRecord-list {
  padding: 15px;
  box-sizing: border-box;
  overflow-y: auto;
  background-color: #fff;
  &-content {
    height: 100%;
  }
  &__table_actions {
    margin-bottom: 15px;
  }
  &__table {
    height: calc(100% - 160px);
    margin-bottom: 10px;
  }
}
</style>
