<template>
  <div style="height: 100%;">
    <div class="content_box">
      <div class="top_content">
        <div style="margin-bottom: 20px;">
          <el-input
            v-model="locationPointCode"
            style="width: 217px; margin-right: 15px;"
            placeholder="定位点编号"
            maxlength="25"
            onkeyup="if(value.length>25)value=value.slice(0,25)"
          ></el-input>
          <el-input
            v-model="locationPointName"
            style="width: 217px; margin-right: 15px;"
            placeholder="定位点名称"
            maxlength="25"
            onkeyup="if(value.length>25)value=value.slice(0,25)"
          ></el-input>
          <el-button type="primary" plain @click="_resetCondition">重置</el-button>
          <el-button type="primary" @click="_searchByCondition">查询</el-button>
        </div>
        <div>
          <el-button type="primary" icon="el-icon-plus" @click="addFn('add')">新增</el-button>
        </div>
      </div>
      <div class="table_list" style="text-align: right; height: 87%;">
        <el-table
          ref="materialTable"
          v-loading="tableLoading"
          :data="tableData"
          height="calc(100% - 70px)"
          border
          style="width: 100%;"
          :cell-style="{ padding: '8px' }"
          stripe
          :header-cell-style="{ background: '#f2f4fbd1' }"
          highlight-current-row
          :empty-text="emptyText"
          :row-key="getRowKeys"
          @row-dblclick="toDetails"
        >
          <!-- <el-table-column
                  :reserve-selection="true"
                  type="selection"
                  width="55"
                  align="center"
                ></el-table-column> -->
          <el-table-column align="center" label="序号" type="index" width="80">
            <template slot-scope="scope">
              <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="locationPointCode" show-overflow-tooltip label="定位点编号"></el-table-column>
          <el-table-column align="center" prop="locationPointName" show-overflow-tooltip label="定位点名称"></el-table-column>
          <el-table-column align="center" prop="deviceUuid" show-overflow-tooltip label="设备UUID"></el-table-column>
          <el-table-column align="center" prop="deviceMinor" show-overflow-tooltip label="设备minor"></el-table-column>
          <el-table-column align="center" prop="deviceMajor" show-overflow-tooltip label="设备major"></el-table-column>
          <el-table-column align="center" prop="remarks" show-overflow-tooltip label="描述"></el-table-column>
          <el-table-column align="center" prop="createDate" show-overflow-tooltip label="创建时间"></el-table-column>
          <el-table-column align="center" label="操作" width="200">
            <template slot-scope="scope">
              <el-link :underline="false" type="danger" style="margin-right: 10px;" @click="deleteLocationPoint(scope.row)">删除</el-link>
              <el-link :underline="false" type="primary" @click="updateFn('edit', scope.row)">编辑</el-link>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          class="table-page pagination"
          style="margin-top: 10px;"
          :current-page="paginationData.currentPage"
          :page-sizes="[15, 30, 50, 100]"
          :page-size="paginationData.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="paginationData.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>
<script type="text/ecmascript-6">
export default {
  name: 'feedbackRecord',
  components: {},
  data() {
    return {
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      LOGINDATA: '',
      taskBookTypeArr: [],
      locationPointName: '', // 定位点名称
      locationPointCode: '', // 定位点编号
      searchDataObj: {
        workTypeCode: '',
        endDate: '',
        startDate: '',
        dateLine: '',
        unionSel: ''
      },
      tableData: [],
      selectedTableList: [],
      emptyText: '暂无数据',
      tableLoading: false,
      pageSize: '',
      currentPage: '',
      listRole: '',
      userIds: '',
      taskBookTypeName: ''
    }
  },
  created() {
    // 获取登录信息
    this.LOGINDATA = JSON.parse(sessionStorage.getItem('LOGINDATA'))
  },
  mounted() {
    // 获取定位点列表
    this._findLocationPointList()
  },
  methods: {
    // 新增
    addFn(type) {
      console.log(type)
      const uuids = []
      this.tableData.forEach((i) => uuids.push(i.deviceUuid))
      this.$router.push({
        path: 'locationPoint/addLocaltionPoint',
        query: {
          type: type,
          uuids
        }
      })
    },
    updateFn(type, row) {
      console.log(type)
      const uuids = []
      this.tableData.forEach((i) => uuids.push(i.deviceUuid))
      this.$router.push({
        path: 'locationPoint/addLocaltionPoint',
        query: {
          type: type,
          id: row.id,
          uuids
        }
      })
    },
    // 查询表格
    _findLocationPointList() {
      this.tableLoading = true
      let obj = this.LOGINDATA
      const { locationPointName, locationPointCode, paginationData } = this
      let data = {
        locationPointName: locationPointName,
        locationPointCode: locationPointCode,
        pageNo: paginationData.currentPage,
        pageSize: paginationData.pageSize
      }
      this.$api.ipsmFindLocationPointList(data).then((res) => {
        this.tableLoading = false
        if (res.code == 200) {
          this.tableData = res.data.list
          this.paginationData.total = parseInt(res.data.count)
          if (res.data.list.length == 0) {
            this.emptyText = '未查到任何记录，请放大查询条件试试！'
          }
        }
        this.tableLoading = false
      })
    },
    // 条件查询
    _searchByCondition() {
      this.paginationData.currentPage = 1
      this._findLocationPointList()
    },
    // 重置查询条件
    _resetCondition() {
      this.paginationData = {
        currentPage: 1,
        pageSize: 15,
        total: 0
      }
      this.locationPointCode = ''
      this.locationPointName = ''
      this._findLocationPointList()
    },
    // 删除列表
    deleteLocationPoint(row) {
      // const { selectedTableList } = this
      // let id = selectedTableList&&selectedTableList.length&&selectedTableList.map((val=>{
      //   return val.id
      // }))
      //  let locationName = selectedTableList&&selectedTableList.length&&selectedTableList.map((val=>{
      //   return val.locationPointName
      // }))
      this.$confirm('确定要删除该定位点吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let data = {
          // id:id.join(','),
          // locationPointName: locationName.join(',')
          id: row.id,
          locationPointName: row.locationPointName
        }
        this.$api.ipsmDeleteLocationListList(data).then((res) => {
          const { code, data, message } = res
          if (code == 200) {
            this.$message.success(message)
            this._findLocationPointList()
            this.$refs.materialTable.clearSelection() // 清空表格选中状态
          } else {
            this.$message.error(message)
          }
        })
      })
    },
    // 双击查看详情
    toDetails(row) {
      this.$router.push({
        path: 'locationPoint/addLocaltionPoint',
        query: {
          data: JSON.stringify(row),
          type: 'details'
        }
      })
    },

    // 表格,勾选表格，对表格进行操作
    handleSelectionChange(val) {
      this.selectedTableList = val
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this._findLocationPointList()
    },
    // 每页的条数
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.paginationData.currentPage = 1
      this._findLocationPointList()
    },
    getRowKeys(row) {
      return row.id
    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.el-button--danger) {
  background: #f78989;
  border-color: #f78989;
  color: #fff;
  min-width: 55px;
  height: 30px;
}

:deep(.el-button--danger:hover) {
  background: #f78989;
  border-color: #f78989;
  color: #fff;
}

:deep(.el-button-edit) {
  min-width: 55px;
  height: 30px;
}

:deep(.el-button-edit:hover) {
  background: #5188fc;
  color: #fff;
}

.content_box {
  height: 100%;
  margin: 15px;
  padding: 20px 25px 25px;
  background: #fff;
  display: flex;
  flex-direction: column;
}

.top_content {
  width: 100%;
  display: flex;
  flex-direction: column;
  margin-bottom: 25px;
}

.el-input {
  width: 200px;
}

.viewButton {
  color: #5188fc;
  cursor: pointer;
}

.green_line {
  display: inline-block;
  width: 4px;
  height: 16px;
  background: #5188fc;
  margin-right: 10px;
  vertical-align: middle;
}

.toptip {
  box-sizing: border-box;
  padding-left: 26px;
  height: 50px;
  width: 100%;
  line-height: 50px;
  text-align: left;
  border-bottom: 1px solid rgb(216 222 231 / 100%);
}

.left_content {
  margin: 20px 20px 0 35px;
}

.left {
  float: left;
  width: 268px;
  min-width: 14%;
  height: 97%;
  border-radius: 10px;
  background-color: #fff;
  margin: 10px;
  margin-top: 0;

  .middle_tools {
    margin-top: 10px !important;
  }
}

.right {
  position: relative;
  width: calc(100% - 300px);
  // flex: 1;
  // width: 56%;
  height: 97%;
  text-align: left;
  background-color: #fff;
  padding-left: 30%;
  // float: left;
  box-sizing: border-box;
  padding: 20px 10px 10px;
  margin: 10px 10px 10px 0;
  margin-left: 288px;
  border-radius: 10px;

  .top_filters {
    input,
    select {
      margin-right: 15px;
      width: 200px;
      height: 40px;
    }

    .search_button {
      background-color: #5188fc;
      color: #fff;
    }

    .search_button:hover {
      opacity: 0.7;
    }
  }

  .middle_tools {
    // margin-top: 20px;
    margin-bottom: 10px;
  }
}

.deleteButton {
  color: #f43530;
  cursor: pointer;
}

@media screen and (max-width: 1600px) {
  .pagination {
    position: absolute;
    bottom: 0;
    right: 15px;
  }

  .personDialog .el-dialog {
    height: 545px;
  }

  .toptip {
    height: 35px;
    line-height: 35px;
  }

  .left_content {
    height: 415px;
  }

  .left .middle_tools {
    line-height: 36px;
    box-shadow: 0 -4px 4px 0 #f3f4f8;
    box-shadow: 0 -4px 4px 0 #f3f4f8;
  }
}
</style>