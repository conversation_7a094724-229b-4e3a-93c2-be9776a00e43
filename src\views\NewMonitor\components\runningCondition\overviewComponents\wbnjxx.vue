<!-- 运行监测 -->
<template>
  <ContentCard :title="item.componentTitleShow" :scrollbarHover="true" :cstyle="{ width: '100%', height: '100%' }"
    class="drag_class" :hasMoreOper="['edit']"
    @more-oper-event="(val) => $emit('all-more-Oper', val, item.componentName, item.chartType)">

    <div slot="content" class="operation-list">
      <el-timeline v-if="hasChart">
        <el-timeline-item v-for="(activity, index) in jobsData" :key="index"
          :icon="activity.comment == '已维保' || activity.comment == '已年检' ? 'el-icon-success' : 'el-icon-error'"
          :size="index == 0 ? 'large' : 'large'" type="primary" :color="activity.color">
          <p class="time">{{ activity.date }}
          </p>
          <div class="continer">
            <span class="item1" v-if="activity.comment == '已维保'"> {{ activity.comment }}</span>
            <span class="item2" v-if="activity.comment == '未维保'"> {{ activity.comment }}</span>
            <span class="item3" v-if="activity.comment == '未开始'"> {{ activity.comment }}</span>
            <span v-if="activity.comment == '无下次维保任务'"> {{ activity.comment }}</span>
            <span class="item1" v-if="activity.comment == '已年检'"> {{ activity.comment }}</span>
            <span class="item2" v-if="activity.comment == '未年检'"> {{ activity.comment }}</span>
          </div>
        </el-timeline-item>
      </el-timeline>
      <div v-if="!hasChart" style="display: flex;justify-content: center;">
        暂无数据
      </div>
    </div>
  </ContentCard>
</template>
<script>
import moment from 'moment'
export default {
  name: 'wbnjxx',
  props: {
    item: {
      type: Object,
      default: () => { }
    },
    systemCode: {
      type: String,
      default: ''
    }
  },
  created() {
  },
  mounted() {
  },
  data() {
    return {
      jobsData: [],
      loading: false,
      operationList: [],
      chartType: '1',
      wbnj: "",
      hasChart: false
    }
  },
  methods: {
    wbnjData(id) {
      const payload = {
        taskPointId: id || "",
      }
      this.jobsData = []
      if (this.item.componentDataType === 'wbxx') {
        payload.systemIdentificationClassification = '2'
        this.$api.getTaskRecordByTaskPointId(payload).then((res) => {
          if (res.code === "200") {
            this.hasChart = true
            this.jobsData = res.data.filter(item => {
              return item !== "" && (typeof item === 'object' ? Object.keys(item).length > 0 : true);
            });
          } else {
            this.hasChart = false
            this.$message.error(res.message)
          }
        })
      } else if (this.item.componentDataType === 'njxx') {
        payload.systemIdentificationClassification = '5'
        this.$api.getTaskRecordByTaskPointId(payload).then((res) => {
          if (res.code === "200") {
            this.hasChart = true
            this.jobsData = res.data.filter(item => {
              return item !== "" && (typeof item === 'object' ? Object.keys(item).length > 0 : true);
            });
          } else {
            this.hasChart = false
            this.$message.error(res.message)
          }
        })
      }


    },
  }
}
</script>

<style lang="scss" scoped>
.data-btns {
  position: absolute;
  right: 7%;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &>span {
    width: 44px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    margin: 0 4px;
    background-color: #f6f5fa;
    font-size: 14px;
    font-family: 'PingFang SC-Medium', 'PingFang SC';
    border: none;
    border-radius: 2px;
    color: #7f848c;
    cursor: pointer;
  }

  .active-btn {
    background-color: #e6effc !important;
    color: #3562db !important;
    border-color: #e6effc !important;
  }
}

.operation-list {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
}

.item1,
.item2,
.item3 {
  background-color: #E8FFEA;
  color: #00B42A;
  font-size: 14px;
  padding: 4px 10px;
  border-radius: 5px;
}

.item2 {
  background-color: #FFECE8;
  color: #F53F3F;
}

.item3 {
  background-color: #E5E6EB;
  color: #96989A;
}
</style>
