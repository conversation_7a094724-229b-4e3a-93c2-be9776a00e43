<!--
 * @Author: hedd
 * @Date: 2023-03-13 16:13:48
 * @LastEditTime: 2023-03-24 15:51:13
 * @FilePath: \ihcrs_pc\src\views\drag\components\logisticsService.vue
 * @Description:
-->
<template>
  <ContentCard
    :title="item.componentTitle"
    :scrollbarHover="true"
    :cstyle="{ height: '100%' }"
    class="drag_class"
    :hasMoreOper="['edit']"
    @more-oper-event="(val) => $emit('all-more-Oper', val, 'workOderType')"
  >
    <div slot="content" class="surround">
      <div class="top-middle">
        <div class="order-num">
          <el-progress :percentage="percentage > 100 ? 100 : percentage" color="#ff6461" :stroke-width="15" define-back-color="#3562DB" :show-text="false"></el-progress>
          <div class="content">
            <div class="left-cont">
              <span>本日超时工单</span>
              <span>
                <span style="cursor: pointer;">{{ countOverTimes }}</span>
                <span>单</span>
              </span>
            </div>
            <div class="right-cont">
              <span>本日总工单</span>
              <span>
                <span style="cursor: pointer;">{{ workOrderInfo.todayNum }}</span>
                <span>单</span>
              </span>
            </div>
          </div>
        </div>
      </div>
      <div class="top-left">
        <div class="response">
          <div>
            <!-- <img src="@/assets/images/service/time1.png" /> -->
            <div class="val-box">
              <span class="desc">工单平均响应时间</span>
              <p class="time" v-html="countData.response"></p>
            </div>
          </div>
          <div class="average">
            <span>同比去年平均值</span>
            <span v-if="countData.responseRate != '0.00'">
              <span :style="{color: countData.responseRate.charAt(0) == '-' ? '#00BC6D' : '#FA403C'}">{{ countData.responseRate }}</span>
              <img v-if="countData.responseRate.charAt(0) == '-'" src="@/assets/images/service/down.png" />
              <img v-else src="@/assets/images/service/up.png" />
            </span>
            <span v-else>{{ countData.responseRate }}</span>
          </div>
        </div>
        <div class="response">
          <div>
            <!-- <img src="@/assets/images/service/time2.png" /> -->
            <div class="val-box">
              <span class="desc">工单平均完工时长</span>
              <p class="time" v-html="countData.finishTime"></p>
            </div>
          </div>
          <div class="average">
            <span>同比去年平均值</span>
            <span v-if="countData.finishTimeRate != '0.00'">
              <span :style="{color: countData.finishTimeRate.charAt(0) == '-' ? '#00BC6D' : '#FA403C'}">{{ countData.finishTimeRate }}</span>
              <img v-if="countData.finishTimeRate.charAt(0) == '-'" src="@/assets/images/service/down.png" />
              <img v-else src="@/assets/images/service/up.png" />
            </span>
            <span v-else>{{ countData.finishTimeRate }}</span>
          </div>
        </div>
      </div>
    </div>
  </ContentCard>
</template>
<script>
export default {
  name: 'logisticsService',
  props: {
    item: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      countData: {
        responseRate: '',
        finishTimeRate: ''
      },
      percentage: 0,
      workOrderInfo: {},
      countOverTimes: 0
    }
  },
  mounted() {
    this.getReckonCount()
    this.getComprehensiveWorkOrderInfo()
  },
  methods: {
    getReckonCount() {
      let params = {
        workTypeCode: '',
        workTypeName: '',
        flowcode: '',
        feedbackFlag: '',
        showTimeType: '3',
        startTime: '',
        endTime: ''
      }
      this.$api.getReckonCount(params).then((res) => {
        if (res.success) {
          this.countData = res.body.data
          this.countData.finishTime = this.countData.finishTime.replace(/(年|月|天|小时|分钟|秒)/g, '<span class="unit">$1</span>')
          this.countData.response = this.countData.response.replace(/(年|月|天|小时|分钟|秒)/g, '<span class="unit">$1</span>')
        }
      })
    },
    getComprehensiveWorkOrderInfo() {
      let params = {}
      this.$api.getComprehensiveWorkOrderInfo(params).then((res) => {
        if (res.code == '200') {
          this.workOrderInfo = res.data.resultMap
          if (this.workOrderInfo.todayNum == 0) {
            this.percentage = 0
          } else {
            this.getCallCenterData()
          }
        }
      })
    },
    getCallCenterData() {
      let params = {
        curPage: 1,
        pageSize: 100,
        selectType: 5,
        isTimeOut: 1
      }
      this.$api.getCallCenterData(params).then((res) => {
        this.countOverTimes = parseInt(res.body.countOverTimes)
        this.percentage = Math.round((this.countOverTimes / this.workOrderInfo.todayNum) * 100)
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.surround {
  display: flex;
  flex-direction: column;
  margin-top: 14px;
  padding: 0 3px;
  .top-left {
    width: 100%;
    display: flex;
    justify-content: space-between;
    > div {
      width: calc(50% - 8px);
      padding: 16px;
      box-sizing: border-box;
      background: #FAF9FC;
      border-radius: 4px;
    }
  }
}
.response img {
  width: 60px;
  height: 60px;
  margin-left: 8px;
}
.response > div:nth-child(1) {
  // width: 60%;
  display: flex;
  align-items: center;
}
.response > div:nth-child(2) {
  // width: 30%;
}
.response .desc {
  font-size: 14px;
  color: #7F848C;
}
.response .time {
  font-size: 24px;
  font-weight: 700;
  margin: 6px 0px 0px 0px;
}
::v-deep(.time) .unit{
  font-size: 15px;
  font-weight: 500;
  color: #CCCED3;
  line-height: 18px;
  margin: 0px 4px;
}
.val-box {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}
.average {
  margin-top: 20px;
  display: flex;
  align-items: center;
}
.average img {
  width: 20px;
  height: 20px;
}
.average > span:nth-child(1) {
  font-size: 12px;
  color: #414653;
}
.average > span:nth-child(2) {
  font-size: 16px;
  display: flex;
  align-items: center;
}
.average > span:nth-child(2) > span:nth-child(1) {
  margin-left: 16px;
}
.surround .top-middle {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.surround .top-middle .order-num {
  width: 100%;
  padding: 12% 12px 12%;
  box-sizing: border-box;
}
.surround .top-middle .order-num .content {
  display: flex;
  justify-content: space-between;
}
.surround .top-middle .order-num .content .left-cont {
  display: flex;
  flex-direction: column;
  margin-right: 35%;
}
.surround .top-middle .order-num .content .left-cont > span:nth-child(1),
.surround .top-middle .order-num .content .right-cont > span:nth-child(1) {
  font-size: 14px;
  color: #7F848C;
  font-weight: 500;
  margin-bottom: 5px;
}
.surround .top-middle .order-num .content .left-cont > span:nth-child(2) > span:nth-child(1),
.surround .top-middle .order-num .content .right-cont > span:nth-child(2) > span:nth-child(1) {
  font-size: 24px;
  font-weight: 700;
  margin-right: 5px;
  color: #3562DB;
}
.surround .top-middle .order-num .content .left-cont > span:nth-child(2) > span:nth-child(2),
.surround .top-middle .order-num .content .right-cont > span:nth-child(2) > span:nth-child(2) {
  font-size: 14px;
  color: #CCCED3;
  font-weight: 500;
}
.surround .top-middle .order-num .content .right-cont {
  display: flex;
  flex-direction: column;
}
::v-deep .el-progress {
  margin-bottom: 24px;
  .el-progress-bar__outer,
  .el-progress-bar__inner {
    border-radius: 0;
  }
}
</style>
