<template>
  <el-dialog
    v-if="shiftContentDialogShow"
    v-dialogDrag
    title="选择显示内容"
    width="40%"
    :visible.sync="shiftContentDialogShow"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <div class="sapce_content">
      <div v-loading="treeLoading" class="center">
        <div class="tab-header">
          <div class="tab-item" v-for="(item, index) in tabList" :key="item.id" :class="{ active: activeIndex == index }" @click="selectChange(index)">{{ item.name }}</div>
        </div>
        <div class="center-box">
          <div v-show="activeIndex == '0'">
            <div>
              <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange"> 全选类型 </el-checkbox>
            </div>
            <el-tree :data="treeData" show-checkbox node-key="id" ref="treeFrom" :default-checked-keys="defaultCheckedConter" :props="defaultProps" @check="hanldTreeCheck">
            </el-tree>
          </div>
          <div v-show="activeIndex == '1'">
            <el-checkbox-group v-model="fieldsConfigSelect">
              <el-checkbox v-for="item in fieldsConfigData" :key="item.id" :label="item.id" style="display: block" @change="(val) => checkFieldschange(item, val)">
                <div class="personCheck">
                  <div class="info">
                    <div class="name">{{ item.name }}</div>
                  </div>
                </div>
              </el-checkbox>
            </el-checkbox-group>
          </div>
          <div v-show="activeIndex == '2'">
            <el-checkbox-group v-model="evacuateSelect" @change="changegroup">
              <el-checkbox v-for="item in evacuateBakData" :key="item.id" :label="item.id" style="display: block" @change="(val) => checkboxchange(item, val)">
                <div class="personCheck">
                  <div class="info">
                    <div class="name">{{ item.name }}</div>
                  </div>
                </div>
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
      </div>
      <div class="right">
        <div class="top">
          <span>
            <span class="label">已选:</span>
            <span class="num">{{ selectedItems.length ? selectedItems.length : 0 }}</span>
            <span class="dept">个</span>
          </span>
          <!-- <span class="clear" @click="clear">清空</span> -->
        </div>
        <div v-for="(item, index) in selectedItems" :key="index" class="item-list">
          <div style="display: flex">
            <div class="info">
              <div class="name">{{ item.name }}</div>
            </div>
          </div>
          <div class="remove" @click="remove(item)"><i class="el-icon-close"></i></div>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="submit">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { treeToList } from '@/util'
export default {
  props: {
    shiftContentDialogShow: {
      type: Boolean,
      default: false
    },
    defaultCheckedConter: {
      type: Array,
      default: () => []
    },
    emergencyId: {
      type: Array,
      default: () => []
    },
    lineId: {
      type: Array,
      default: () => []
    },
    contenList: {
      type: Array,
      default: () => []
    },
    typeString: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      checkAll: false,
      isIndeterminate: false,
      activeIndex: 0,
      defaultProps: {
        label: 'name',
        children: 'children'
      },
      evacuateSelect: [],
      fieldsConfigSelect: [],
      treeData: [],
      tabList: [
        {
          name: '设备类型',
          id: '1'
        },
        {
          name: '应急资源类型',
          id: '2'
        },
        {
          name: '疏散路线',
          id: '3'
        }
      ],
      evacuateBakData: [
        {
          name: '疏散路线丶安全出口',
          id: '99'
        }
      ],
      fieldsConfigData: [], // 应急资源
      treeLoading: false,
      shiftPostData: [], // 人员列表
      treeDataList: [],
      checkedKeys: [],
      searchForm: {
        keyWord: ''
      },
      treeSelected: [], // 树形选中数据
      checkboxSelected: [], // 复选框选中数据
      otherSelected: [] // 其他选中数据
    }
  },
  mounted() {
    this.getEquipmentData()
    this.getfieldsConfigData()
    this.initSelection()
  },
  watch: {
    emergencyId: {
      immediate: true, // 立即执行一次
      handler(newVal) {
        this.fieldsConfigSelect = [...newVal]
      }
    },
    lineId: {
      immediate: true, // 立即执行一次
      handler(newVal) {
        this.evacuateSelect = [...newVal]
      }
    }
  },
  computed: {
    selectedItems() {
      return [...this.treeSelected, ...this.checkboxSelected, ...this.otherSelected]
    }
  },
  methods: {
    selectChange(val) {
      this.activeIndex = val
    },
    initSelection() {
      const treeKeys = []
      const list1Keys = []
      const list2Keys = []
      this.contenList.forEach((item) => {
        if (item.type == '0') {
          treeKeys.push(item)
        } else if (item.type == '1') {
          list1Keys.push(item)
        } else if (item.type == '2') {
          list2Keys.push(item)
        }
      })
      this.treeSelected = treeKeys
      this.checkboxSelected = list1Keys
      console.log(this.checkboxSelected, 'this.checkboxSelected************')

      this.otherSelected = list2Keys
    },
    // 获取设备类型
    getEquipmentData() {
      this.treeLoading = true
      this.$api.getEquipmentType().then((res) => {
        this.treeLoading = false
        if (res.code == '200') {
          this.treeData = res.data
          this.treeDataList = treeToList(res.data)
        }
      })
    },
    // 获取应急资源
    getfieldsConfigData() {
      this.treeLoading = true
      this.$api.getfieldsConfigList().then((res) => {
        this.treeLoading = false
        if (res.code == '200') {
          this.fieldsConfigData = res.data
        }
      })
    },
    // 获取所有可选的节点ID
    getAllNodeIds(data, ids = []) {
      data.forEach((item) => {
        if (!item.disabled) {
          ids.push(item.id)
        }
        if (item.children && item.children.length) {
          this.getAllNodeIds(item.children, ids)
        }
      })
      return ids
    },

    // 根据ID获取完整的节点数据
    getNodeDataById(data, id) {
      for (let item of data) {
        if (item.id === id) return item
        if (item.children && item.children.length) {
          const found = this.getNodeDataById(item.children, id)
          if (found) return found
        }
      }
      return null
    },

    // 全选复选框变化事件
    handleCheckAllChange(val) {
      const allIds = this.getAllNodeIds(this.treeData)
      if (val) {
        this.$refs.treeFrom.setCheckedKeys(allIds)
        this.updateSelectedItems(allIds)
      } else {
        this.$refs.treeFrom.setCheckedKeys([])
        this.selectData = []
      }
      this.isIndeterminate = false
    },

    // 树节点选中事件
    hanldTreeCheck(data, checkedInfo) {
      const checkedKeys = checkedInfo.checkedKeys
      const allKeys = this.getAllNodeIds(this.treeData)
      // 更新全选复选框状态
      if (checkedKeys.length === 0) {
        this.checkAll = false
        this.isIndeterminate = false
      } else if (checkedKeys.length === allKeys.length) {
        this.checkAll = true
        this.isIndeterminate = false
      } else {
        this.checkAll = false
        this.isIndeterminate = true
      }
      // 更新右侧已选列表
      this.updateSelectedItems(checkedKeys)
    },

    // 更新右侧已选项目列表
    updateSelectedItems(checkedKeys) {
      this.treeSelected = checkedKeys.map((id) => this.getNodeDataById(this.treeData, id)).filter((item) => item && !item.disabled)
      this.treeSelected.forEach((el) => {
        el.type = this.activeIndex
      })
    },
    // 移除
    remove(item) {
      console.log(item, 'item')

      if (this.activeIndex == '0') {
        const index = this.treeSelected.findIndex((i) => i.id === item.id)
        console.log(index, ',,,,,,,,,,,,,,')

        if (index > -1) {
          this.treeSelected.splice(index, 1)
        }
        this.$refs.treeFrom.setChecked(item.id, false)
      } else if (this.activeIndex == '1') {
        const index = this.checkboxSelected.findIndex((item) => item.id == item.id)
        if (index !== -1) {
          this.checkboxSelected.splice(index, 1)
        }
        this.fieldsConfigSelect = this.checkboxSelected.map((item) => item.id)
      } else if (this.activeIndex == '2') {
        this.evacuateSelect = this.otherSelected.filter((item) => item.id == item.id)
        if (this.evacuateSelect !== -1) {
          this.otherSelected.splice(this.evacuateSelect, 1)
        }
      }
    },
    // // 清空
    // clear() {
    //   this.selectData = []
    //   this.shiftPostSelect = []
    // },
    closeDialog() {
      this.$emit('closeContenDialog')
    },
    changegroup(e) {
      console.log(e, '444444444444444')
    },
    // 选择疏散路线
    checkboxchange(item, val) {
      this.$set(item, 'type', this.activeIndex)
      if (this.otherSelected.some((el) => el.id === item.id)) {
        this.otherSelected = this.otherSelected.filter((el) => el.id !== item.id)
      } else {
        this.otherSelected.push(item)
      }
    },
    // 选择应急
    checkFieldschange(item, val) {
      this.$set(item, 'type', this.activeIndex)
      if (this.checkboxSelected.some((el) => el.id === item.id)) {
        this.checkboxSelected = this.checkboxSelected.filter((el) => el.id !== item.id)
      } else {
        this.checkboxSelected.push(item)
      }
    },
    submit() {
      if (this.selectedItems.length < 1) {
        this.$message({
          message: '请选择至少一条数据',
          type: 'warning'
        })
      } else {
        this.$emit('submitAlarmConterDialog', this.selectedItems, this.activeIndex)
        this.closeDialog()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.model-dialog {
  width: 40% !important;

  .sapce_content {
    margin: 0 auto;
    background: #fff;
    padding: 10px 0;
    border-radius: 4px;
    height: 400px;
    display: flex;
    width: 100%;

    .center {
      width: 500px;
      background-color: #fff;
      height: 100%;
      margin-left: 10px;
      border-radius: 5px;
      padding-left: 16px;

      .personCheck {
        width: 100%;
        display: flex;
        padding: 10px 0;

        img {
          vertical-align: middle;
        }
      }
    }
    .center-box {
      width: 100%;
      height: 94%;
      overflow: scroll;
      margin-top: 10px;
    }
    .right {
      width: calc(100% - 320px);
      border-left: 1px solid #dcdfe6;
      background-color: #fff;
      height: 100%;
      margin-left: 10px;
      border-radius: 5px;
      padding-left: 16px;
      overflow: scroll;

      .top {
        display: flex;
        padding: 0 12px;
        justify-content: space-between;

        .label,
        .dept {
          font-size: 14px;
          color: #7f848c;
        }

        .num {
          font-size: 14px;
          color: #333333;
          margin-left: 10px;
        }

        .dept {
          margin-left: 10px;
        }

        .clear {
          font-size: 12px;
          color: #3562db;
          cursor: pointer;
        }
      }

      .item-list {
        display: flex;
        cursor: pointer;
        padding: 0 12px;
        justify-content: space-between;
        margin-top: 8px;

        .remove {
          margin: auto 0;
        }
      }

      .item-list:hover {
        background: #e6effc;
      }
    }

    .info {
      margin-left: 8px;

      .name {
        font-weight: 500;
        color: #333333;
      }

      .mobile {
        font-size: 12px;
        color: #7f848c;
      }
    }
  }
}
.tab-header {
  display: flex;
  color: #333333;
  cursor: pointer;
}
.tab-item {
  margin: 0px 15px;
  margin-left: -3px;
  font-size: 14px;
}
.tab-item.active {
  color: #3562db;
}
::v-deep .el-checkbox__input {
  display: inline-block !important;
}
</style>
