const items = [
  // 左侧电梯运行监测
  {
    id: 'equOperationMonitor',
    componentName: 'equOperationMonitor',
    componentTitle: '设备运行监测',
    x: 0,
    y: 0,
    width: 6,
    height: 15,
    dragAllowFrom: '.drag_class',
    resizable: true,
    draggable: false,
    status: 1
  },
  // 电梯报警数量
  {
    id: 'equAlarmNum',
    componentName: 'equAlarmNum',
    componentTitle: '设备报警统计',
    x: 6,
    y: 0,
    width: 8,
    height: 2,
    dragAllowFrom: '.drag_class',
    resizable: true,
    draggable: false,
    status: 1
  },
  // 电梯运行统计
  {
    id: 'workingStatistics',
    componentName: 'workingStatistics',
    componentTitle: '设备总览统计',
    x: 6,
    y: 2,
    width: 8,
    height: 3,
    dragAllowFrom: '.drag_class',
    resizable: true,
    draggable: false,
    status: 1
  },
  // 运行时间
  {
    id: 'workingTime',
    componentName: 'workingTime',
    componentTitle: '运行时间排行',
    x: 14,
    y: 0,
    width: 5,
    height: 5,
    dragAllowFrom: '.drag_class',
    resizable: true,
    draggable: false,
    status: 1
  },
  // 运行楼层
  {
    id: 'workingFloor',
    componentName: 'workingFloor',
    componentTitle: '运行楼层排行',
    x: 19,
    y: 0,
    width: 5,
    height: 5,
    dragAllowFrom: '.drag_class',
    resizable: true,
    draggable: false,
    status: 1
  },
  // 电梯品牌
  {
    id: 'elevatorBrand',
    componentName: 'elevatorBrand',
    componentTitle: '电梯品牌分布',
    x: 6,
    y: 5,
    width: 4,
    height: 5,
    dragAllowFrom: '.drag_class',
    resizable: true,
    draggable: false,
    status: 1
  },
  // 使用年限
  {
    id: 'serviceLife',
    componentName: 'serviceLife',
    componentTitle: '使用年限分布',
    x: 10,
    y: 5,
    width: 4,
    height: 5,
    dragAllowFrom: '.drag_class',
    resizable: true,
    draggable: false,
    status: 1
  },
  // 运行距离
  {
    id: 'workingFloorDistance',
    componentName: 'workingFloorDistance',
    componentTitle: '运行距离排行',
    x: 14,
    y: 5,
    width: 5,
    height: 5,
    dragAllowFrom: '.drag_class',
    resizable: true,
    draggable: false,
    status: 1
  },
  // 开门次数
  {
    id: 'openDoorNum',
    componentName: 'openDoorNum',
    componentTitle: '开门次数排行',
    x: 19,
    y: 5,
    width: 5,
    height: 5,
    dragAllowFrom: '.drag_class',
    resizable: true,
    draggable: false,
    status: 1
  },
  // 报警类型统计
  {
    id: 'alarmTypeAnalysis',
    componentName: 'alarmTypeAnalysis',
    componentTitle: '报警类型分析',
    x: 6,
    y: 10,
    width: 8,
    height: 5,
    dragAllowFrom: '.drag_class',
    resizable: true,
    draggable: false,
    status: 1
  },
  // 重复开关门报警
  {
    id: 'evevatorRepeatOpen',
    componentName: 'evevatorRepeatOpen',
    componentTitle: '重复开关门报警排行',
    x: 14,
    y: 10,
    width: 5,
    height: 5,
    dragAllowFrom: '.drag_class',
    resizable: true,
    draggable: false,
    status: 1
  },
  // 电梯困人报警
  {
    id: 'elevatorTrapped',
    componentName: 'elevatorTrapped',
    componentTitle: '困人报警排行',
    x: 19,
    y: 10,
    width: 5,
    height: 5,
    dragAllowFrom: '.drag_class',
    resizable: true,
    draggable: false,
    status: 1
  },
  // 报警记录
  {
    id: 'alarmRecord',
    componentName: 'alarmRecord',
    componentTitle: '报警记录统计',
    x: 0,
    y: 15,
    width: 14,
    height: 5,
    dragAllowFrom: '.drag_class',
    resizable: true,
    draggable: false,
    status: 1
  },
  // // 卡层报警排行
  {
    id: 'seizingFloor',
    componentName: 'seizingFloor',
    componentTitle: '卡层报警排行',
    x: 14,
    y: 15,
    width: 5,
    height: 5,
    dragAllowFrom: '.drag_class',
    resizable: true,
    draggable: false,
    status: 1
  },
  // // 温度报警排行
  {
    id: 'heatAlarm',
    componentName: 'heatAlarm',
    componentTitle: '温度报警排行',
    x: 19,
    y: 15,
    width: 5,
    height: 5,
    dragAllowFrom: '.drag_class',
    resizable: true,
    draggable: false,
    status: 1
  }
]
