<template>
  <PageContainer>
    <div slot="header">
      <div class="search-header">
        <div class="sino-tabs">
          <div class="sino-tabs-item">
            <span :class="['item-text',isActive==1?'active':'']" @click="tabsSwitch(1)">LEC-D研判</span>
          </div>
          <div class="sino-tabs-item">
            <span :class="['item-text',isActive==2?'active':'']" @click="tabsSwitch(2)">LS-R研判</span>
          </div>
        </div>
      </div>
    </div>
    <div slot="content" style="height: 100%; margin-top: 16px; background-color: #fff;">
      <div class="table-content">
        <LECD v-if="isActive == 1"></LECD>
        <LSR v-if="isActive == 2"></LSR>
      </div>
    </div>
  </PageContainer>
</template>

<script>
import LECD from './components/LECD.vue'
import LSR from './components/LSR.vue'
export default {
  name: 'JudgedMethodManagement',
  components: {
    LECD,
    LSR
  },
  data() {
    return {
      isActive: 1
    }
  },
  // activated() {
  //   if (!this.$store.state.keepAlive || this.$store.state.refresh) {
  //     this.isActive = 1;
  //     this.$store.commit("amendRefresh", false);
  //   }
  //   this.$store.commit("keepAliveChange", false);
  // },
  created() {},
  methods: {
    tabsSwitch(type) {
      this.isActive = type
    }
  }
}
</script>

<style lang="scss" scoped>
.search-header {
  padding: 10px 16px;

  .search-from {
    padding-bottom: 12px;

    & > div {
      margin-top: 12px;
      margin-right: 10px;
    }
  }
}

.table-content {
  padding: 16px;
  height: calc(100% - 20px);
  overflow: auto;
}

.sino-tabs {
  height: 43px;
  border-bottom: 1px solid #dcdfe6;

  .sino-tabs-item {
    display: inline-block;
    padding-right: 24px;
    height: 40px;
    line-height: 40px;

    .item-text {
      font-size: 16px;
      display: inline-block;
      // height: 48px;
      cursor: pointer;
      color: #606266;
    }

    .active {
      color: #5188fc;
      border-bottom: 3px solid #5188fc;
    }
  }
}
</style>
