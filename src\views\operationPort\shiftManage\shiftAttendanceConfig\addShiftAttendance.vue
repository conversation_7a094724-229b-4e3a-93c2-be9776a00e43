<!--
 * @Description:
-->
<template>
  <PageContainer :footer="true">
    <div slot="content" v-loading="pageLoading" class="attendance-content">
      <div class="classese-content-title" @click="$router.go(-1)"><i class="el-icon-arrow-left"></i> {{ pageTitle }}</div>
      <div class="content_box">
        <el-form ref="form" :model="form" :rules="rules" label-width="auto">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="值班考勤组名称" prop="name">
                <el-input v-model="form.name" placeholder="请输入值班考勤组名称" :disabled="queryParams.type === 'detail'" maxlength="50"> </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="负责人" prop="chargePersonName">
                <el-input v-model="form.chargePersonName" placeholder="请选择负责人" disabled>
                  <span v-if="queryParams.type !== 'detail'" slot="suffix" class="text-style" style="right: 10px" @click="selectLeader">选择负责人</span>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="22">
              <el-form-item label="考勤组类型" prop="type">
                <el-radio-group
                  v-model="form.type"
                  :disabled="queryParams.type === 'detail' || (queryParams.type === 'edit' && form.type == '4')"
                  @change="changeAttendanceGroupType"
                >
                  <el-radio v-for="item in attendanceGroupTypeList" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col v-if="form.type != 1" :span="22">
              <el-form-item v-if="form.type != '4'" label="" prop="type">
                <el-checkbox v-model="form.idleClockPerson" :true-label="1" :false-label="0" :disabled="queryParams.type === 'detail'">允许符合要求的空闲成员进行打卡</el-checkbox>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="打卡值班岗" prop="dutyPostCode">
                <div class="duty-box">
                  <div ref="tagBox" class="tag-box">
                    <template v-if="form.type == 1">
                      <el-tag
                        v-for="(item, index) in punchInAndOutOfDutyList"
                        :key="index"
                        size="small"
                        :closable="queryParams.type !== 'detail'"
                        @close="handleCloseTag(item.value)"
                      >
                        {{ item.label }}
                      </el-tag>
                    </template>
                    <template v-else>
                      <span>{{ form.dutyPostName }}</span>
                    </template>
                  </div>
                  <span v-if="queryParams.type !== 'detail'" class="box-select text-style" @click="selectShiftPostEvent"><i class="el-icon-plus"></i> 选择值班岗</span>
                </div>
                <div v-if="punchInAndOutOfDutyList.length && form.type == 1" class="text-style" @click="openPeopleListDialog">查看人员列表</div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="22">
              <el-form-item label="班次规则" prop="shiftRule">
                <el-radio-group v-model="form.shiftRule" :disabled="queryParams.type === 'detail'" @change="changeShiftRules">
                  <el-radio :label="1">按天循环</el-radio>
                  <el-radio :label="2">按周循环</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="20">
              <el-form-item label="考勤班次" class="is-required">
                <div v-if="queryParams.type !== 'detail'" class="shift-change-box">
                  <span>批量配置为</span>
                  <span v-if="shiftData.id">
                    <span class="shift-name">{{ shiftData.name }}: {{ shiftData.time }}</span>
                    <span class="change-shift-btn" @click="selectShift('detail', shiftData)">详情</span>
                    <span class="change-shift-btn" @click="selectShift('edit', shiftData)">更改</span>
                  </span>
                  <span v-else class="change-shift-btn" @click="selectShift('add')">选择</span>
                </div>
                <TablePage
                  ref="shiftTablePage"
                  border
                  row-key="weekNum"
                  :showPage="false"
                  :tableColumn="tableColumn"
                  :data="form.signRules"
                  @select="shiftSelectChange"
                  @select-all="shiftSelectAllChange"
                >
                  <template #shiftTime="{ row }">
                    <div v-for="item in row.detailReqs" :key="item.attendanceTimeId" class="table-rows">{{ item.signTimeStr }}</div>
                  </template>
                  <template #signPersonNum="{ row, scopeIndex }">
                    <div v-for="(item, index) in row.detailReqs" :key="item.attendanceTimeId" class="table-rows">
                      <el-form-item
                        label-width="0"
                        :prop="`signRules[${scopeIndex}].detailReqs[${index}].signPersonNum`"
                        :rules="[{ required: true, message: '请输入要求人数', trigger: 'blur' }]"
                        class="sign-person-num-item"
                      >
                        <el-input
                          v-model="item.signPersonNum"
                          :disabled="queryParams.type === 'detail'"
                          oninput="value=value.replace(/^0|[^0-9]/g, '')"
                          maxlength="5"
                          placeholder="请输入"
                          width="50"
                          size="mini"
                          @change="changeSignPersonNum(item, index, row)"
                        ></el-input>
                      </el-form-item>
                    </div>
                  </template>
                  <template #shouldSignPersonName="{ row }">
                    <div v-for="(item, index) in row.detailReqs" :key="item.attendanceTimeId" class="table-rows">
                      <span v-if="!item.signPersonNum">请先输入要求人数</span>
                      <div v-else class="flex">
                        <div class="sing-people-name">{{ item.shouldSignPersonName || '无' }}</div>
                        <div v-if="queryParams.type !== 'detail'" class="select-sign-people" @click="selectSignPeople(item, index, row)">请选择人员</div>
                      </div>
                    </div>
                  </template>
                  <template #ZBCY="{ row }">
                    <div v-for="(item, index) in row.detailReqs" :key="item.attendanceTimeId" class="table-rows">
                      <div class="flex">
                        <div v-if="item.shouldSignPersonName" class="sing-people-name">{{ item.shouldSignPersonName || '无' }}</div>
                        <div v-if="queryParams.type !== 'detail'" style="color: #3562db" @click="selectSignPeople(item, index, row)">请选择人员</div>
                      </div>
                    </div>
                  </template>
                </TablePage>
                <span v-if="form.shiftRule == 1 && queryParams.type !== 'detail'" class="text-style" @click="addShiftDay"><i class="el-icon-plus"></i>添加</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="22">
              <el-form-item label="打卡终端" prop="signDevice">
                <el-radio-group v-model="form.signDevice" :disabled="queryParams.type === 'detail'">
                  <el-radio :label="1">Pad端</el-radio>
                  <el-radio v-if="form.type == '4'" :label="2">专业客户端</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="form.signDevice == '1'" :gutter="20">
            <el-col style="width: fit-content">
              <el-form-item label="未打下班卡逻辑" prop="workLogic">
                <el-radio-group v-model="form.workLogic" :disabled="queryParams.type === 'detail'" @change="changeWorkLogicRadio">
                  <el-radio :label="1">记为全时段缺勤</el-radio>
                  <el-radio :label="2">记为缺勤部分时间</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col style="width: fit-content">
              <el-form-item label-width="0" prop="absenceWork">
                <el-input v-model="form.absenceWork" maxlength="5" placeholder="请输入" :disabled="form.workLogic == 1 || queryParams.type === 'detail'" style="width: 100px">
                  <span slot="suffix">%</span>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="form.signDevice == '1'" :gutter="20">
            <el-col :span="20">
              <el-form-item label="打卡终端" class="is-required">
                <TablePage ref="tablePage" border :showPage="false" :tableColumn="equipmentTableColumn" :data="equipmentTableData">
                  <template #createdTime="scope">
                    <span>{{ $tools.dateToStr(scope.row.createdTime) }}</span>
                  </template>
                </TablePage>
                <span v-if="queryParams.type !== 'detail'" class="text-style" @click="addEquipment"><i class="el-icon-plus"></i>添加终端</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="form.type == '4'" :gutter="20">
            <el-col v-if="form.signDevice == '2'" :span="20">
              <span v-if="queryParams.type !== 'detail'" class="binding"
                ><i class="el-icon-info" style="margin-right: 5px"></i>
                <span v-if="queryParams.type != 'add' && controlCodeIds">已绑定 ( {{ controlCodeIds }} )</span>
                <div v-if="queryParams.type != 'add' && controlCodeIds" class="binding">请在专业客户端进行考勤组的绑定与解绑</div>
                <span v-if="queryParams.type == 'add'">未绑定</span>
                <span v-if="!controlCodeIds && queryParams.type == 'edit'">未绑定</span>
              </span>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <memberDialog
        v-if="isMemberDialog"
        :memberShow="isMemberDialog"
        checkType="radio"
        nodeKey="staffId"
        :defaultChecked="defaultCheckedShiftLeader"
        @submitMemberDialog="submitMemberDialog"
        @closeMemberDialog="() => (isMemberDialog = false)"
      />
      <shiftPostPeopleDialog
        v-if="shiftPostPeopleDialogShow"
        :visible.sync="shiftPostPeopleDialogShow"
        :defaultChecked="defaultCheckedshiftPostPeople"
        :defaultSelectedPeople="selectedShiftPostPeople"
        :filterShiftPost="filterShiftPost"
        checkType="checkbox"
        @submitDialog="submitshiftPostPeopleDialog"
        @closeDialog="() => (shiftPostPeopleDialogShow = false)"
      />
      <shiftPostDialog
        v-if="shiftPostDialogShow"
        :visible.sync="shiftPostDialogShow"
        :defaultChecked="defaultCheckedShiftPost"
        :checkType="shiftPostCheckType"
        @submitDialog="submitPostDialog"
        @closeDialog="() => (shiftPostDialogShow = false)"
      />
      <selectShiftDialog
        v-if="selectShiftDialogShow"
        :visible.sync="selectShiftDialogShow"
        :defaultChecked="defaultCheckedShiftData"
        :type="selectShiftType"
        @submitDialog="submitSelectShiftDialog"
        @closeDialog="() => (selectShiftDialogShow = false)"
      />
      <selectShiftDetailDialog
        v-if="selectShiftDetailDialogShow"
        :visible.sync="selectShiftDetailDialogShow"
        :rowDetail="defaultCheckedShiftData"
        @closeDialog="() => (selectShiftDetailDialogShow = false)"
      />
      <shiftConflictDialog
        v-if="shiftConflictDialogShow"
        :visible.sync="shiftConflictDialogShow"
        :conflictData="conflictData"
        @closeDialog="() => (shiftConflictDialogShow = false)"
      />
      <selectAttendanceTerminalDialog
        v-if="selectAttendanceTerminalDialogShow"
        :visible.sync="selectAttendanceTerminalDialogShow"
        :defaultChecked="defaultCheckedAttendanceTerminalData"
        @submitDialog="submitSelectAttendanceTerminalDialog"
        @closeDialog="() => (selectAttendanceTerminalDialogShow = false)"
      />
      <peopleListByShiftPostDialog
        v-if="peopleListByShiftPostDialogShow"
        :visible.sync="peopleListByShiftPostDialogShow"
        :shiftPostList="punchInAndOutOfDutyList"
        @closeDialog="() => (peopleListByShiftPostDialogShow = false)"
      />
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="onClose">取消</el-button>
      <el-button v-if="queryParams.type !== 'detail'" type="primary" :loading="formLoading" @click="onPreservation">保存</el-button>
    </div>
  </PageContainer>
</template>
<script lang="jsx">
import { attendanceGroupTypeList, attendanceStateList } from '../component/dict.js'
import memberDialog from '@/views/operationPort/postManage/components/selectPersons.vue'
import shiftPostDialog from '../component/shiftPostDialog.vue'
export default {
  name: 'addShiftAttendance',
  components: {
    memberDialog,
    shiftPostDialog,
    selectShiftDialog: () => import('../component/selectShiftDialog.vue'),
    shiftPostPeopleDialog: () => import('../component/shiftPostPeopleDialog.vue'),
    selectShiftDetailDialog: () => import('../component/selectShiftDetailDialog.vue'),
    shiftConflictDialog: () => import('./shiftConflictDialog.vue'),
    selectAttendanceTerminalDialog: () => import('../component/selectAttendanceTerminalDialog.vue'),
    peopleListByShiftPostDialog: () => import('../component/peopleListByShiftPostDialog.vue')
  },
  async beforeRouteLeave(to, from, next) {
    if (!['shiftAttendanceConfig'].includes(to.name)) {
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    var validateaBsenceWork = (rule, value, callback) => {
      if (this.form.workLogic == 2) {
        if (value === '') {
          callback(new Error('请输入占比'))
        } else if (value > 100) {
          callback(new Error('占比不能超过100'))
        } else if (value < 0) {
          callback(new Error('占比不能小于0'))
        } else if (isNaN(value)) {
          callback(new Error('请输入数字'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    return {
      // 正常表单
      form: {
        name: '', // 值班考勤组名称
        chargePersonName: '', // 负责人
        chargePersonId: '', // 负责人id
        type: 1, // 考勤组类型
        dutyPostId: '', // 值班岗ids
        dutyPostCode: '',
        signDevice: '', //  打卡终端
        dutyPostName: '', // 值班岗names
        shiftRule: 1, // 班次规则
        workLogic: 1, // 未打下班卡逻辑
        absenceWork: '', // 缺勤部分时间百分比
        attendanceTerminalId: '', // 打卡终端ids
        idleClockPerson: '', // 是否允许空闲成员打卡，0-不允许，1-允许
        signRules: [
          {
            selected: 1, // 0-勾选，1-未勾选
            shiftName: '',
            shiftManagementId: '',
            weekNum: 1,
            detailReqs: []
          }
        ]
      },
      attendanceStateList,
      tableColumn: [
        {
          key: '1',
          type: 'selection',
          prop: 'selection',
          align: 'center',
          width: '50'
        },
        {
          key: '2',
          type: 'index',
          width: '80',
          formatter: (scope) => {
            return this.filterSequence(scope.row.weekNum)
          },
          label: this.form?.shiftRule == 2 ? '工作日' : '天'
        },
        {
          key: '3',
          prop: 'shiftName',
          label: '班次',
          formatter: (scope) => {
            return scope.row.shiftName || '休息'
          }
        },
        {
          key: '4',
          prop: 'BC',
          label: '班次',
          align: 'center',
          slot: 'BC',
          formatter: (scope) => {
            return scope.row.shiftName || '请选择班次'
          }
        },
        {
          key: '5',
          prop: 'shiftTime',
          label: '时段',
          align: 'center',
          slot: 'shiftTime'
        },
        {
          key: '6',
          prop: 'signPersonNum',
          label: '要求人数',
          align: 'center',
          slot: 'signPersonNum',
          width: '120',
          showOverflowTooltip: false,
          hasJudge: false
        },
        {
          key: '7',
          prop: 'shouldSignPersonName',
          label: '要求打卡人员',
          align: 'center',
          slot: 'shouldSignPersonName',
          hasJudge: false
        },
        {
          key: '8',
          prop: 'ZBCY',
          label: '值班成员',
          align: 'center',
          slot: 'ZBCY',
          hasJudge: false
        },
        {
          key: '9',
          prop: 'operation',
          label: '操作',
          align: 'center',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                {this.form.type != 4 ? (
                  <span class="operationBtn-span" onClick={() => this.selectShiftByRow('edit', row.row)}>
                    修改
                  </span>
                ) : (
                  ''
                )}
                {this.form.shiftRule == 1 && this.form.signRules.length > 1 ? (
                  <span class="operationBtn-span operationBtn-del" onClick={() => this.selectShiftByRow('del', row.row)}>
                    移除
                  </span>
                ) : (
                  ''
                )}
                {this.form.type == 4 ? (
                  <span class="operationBtn-span" onClick={() => this.selectShiftByRow('edit', row.row)}>
                    更改班次
                  </span>
                ) : (
                  ''
                )}
              </div>
            )
          }
        }
      ],
      equipmentTableColumn: [
        {
          label: '序号',
          type: 'index',
          width: '80',
          formatter: (scope) => {
            return scope.$index + 1
          }
        },
        {
          prop: 'name',
          label: '设备名称'
        },
        {
          prop: 'spaceDesc',
          label: '位置描述'
        },
        {
          prop: 'sn',
          label: 'SN码'
        },
        {
          prop: 'ip',
          label: 'IP地址'
        },
        {
          prop: 'mac',
          label: 'MAC地址'
        },
        {
          prop: 'versionName',
          label: '软件版本'
        },
        {
          prop: 'createdTime',
          label: '添加时间',
          slot: 'createdTime'
        },
        {
          prop: 'state',
          label: '状态',
          width: 80,
          render: (h, row) => {
            const stateData = attendanceStateList.find((v) => v.value === row.row.state)
            return <span style={{ color: stateData?.color }}>{stateData?.label}</span>
          }
        },
        {
          prop: 'operation',
          label: '操作',
          width: 80,
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span class="operationBtn-span operationBtn-del" onClick={() => this.handleTableEvent('del', row.row)}>
                  移除
                </span>
              </div>
            )
          }
        }
      ],
      equipmentTableData: [],
      formLoading: false, // 表单loading
      pageLoading: false, // 初始化加载页面loading
      rules: {
        name: [{ required: true, message: '请输入值班考勤组名称', trigger: 'blur' }],
        chargePersonName: [{ required: true, message: '请选择负责人', trigger: 'change' }],
        workLogic: [{ required: true, message: '请选择负责人', trigger: 'change' }],
        type: [{ required: true, message: '请选择考勤组类型', trigger: 'change' }],
        dutyPostCode: [{ required: true, message: '请选择打卡值班岗', trigger: 'change' }],
        shiftRule: [{ required: true, message: '请选择班次规则', trigger: 'change' }],
        absenceWork: [{ validator: validateaBsenceWork, trigger: 'blur' }],
        signDevice: [{ required: true, message: '请选择打卡终端', trigger: 'change' }]
      },
      checkShiftTimeData: {}, // 选中的考勤班次中时段的信息
      attendanceGroupTypeList,
      punchInAndOutOfDutyList: [],
      tableData: [], // 表格数据
      selectShiftType: '', // 选择考勤班次类型
      selectShiftDialogShow: false, // 是否显示选择考勤班次弹窗
      shiftData: {}, // 班次数据 通过班次或者列表选中修改或者详情的选中班次数据，用是否包含index区分判断
      defaultCheckedShiftData: {}, // 默认选中的班次对应shiftData
      queryParams: {},
      isMemberDialog: false, // 是否显示成员弹窗
      defaultCheckedShiftLeader: [], // 默认选中的负责人
      shiftPostCheckType: 'checkbox', // 值班岗多选或radio
      shiftPostDialogShow: false, // 是否显示值班岗弹窗
      defaultCheckedShiftPost: [], // 默认选中的值班岗
      filterShiftPost: '', // 默认过滤的值班岗
      shiftPostPeopleDialogShow: false, // 选择值班岗成员弹窗
      defaultCheckedshiftPostPeople: [], // 默认选中的值班岗成员
      selectedShiftPostPeople: [], // 已选人员列表（完整数据）
      selectShiftDetailDialogShow: false, // 是否显示考勤班次详情弹窗
      defaultCheckedAttendanceTerminalData: [], // 默认选中的打卡终端
      selectAttendanceTerminalDialogShow: false, // 是否显示考勤终端弹窗
      peopleListByShiftPostDialogShow: false,
      shiftConflictDialogShow: false, // 班次冲突弹窗
      conflictData: {}, // 班次冲突数据
      controlCodeIds: '1000'
    }
  },
  computed: {
    pageTitle() {
      let title = '值班考勤组'
      switch (this.queryParams.type) {
        case 'add':
          title = '新增' + title
          break
        case 'edit':
          title = '编辑' + title
          break
        case 'view':
          title += '详情'
          break
      }
      return title
    }
  },
  activated() {
    this.initEvent()
  },
  mounted() {
    if (!this.$store.state.keepAlive.list.includes('shiftAttendanceConfig')) {
      this.initEvent()
    }
  },
  beforeDestroy() {
    this.$refs['tagBox'].removeEventListener('DOMMouseScroll', this.handlerMouserScroll)
    this.$refs['tagBox'].removeEventListener('mousewheel', this.handlerMouserScroll)
  },
  methods: {
    initEvent() {
      // 重置form表单
      const data = this.$options.data()
      delete data.rules
      delete data.tableColumn
      delete data.equipmentTableColumn
      this.$nextTick(() => {
        Object.assign(this.$data, data)
        this.$refs.form.resetFields()
        this.queryParams = this.$route.query
        this.$refs['tagBox'].addEventListener('DOMMouseScroll', this.handlerMouserScroll, false)
        this.$refs['tagBox'].addEventListener('mousewheel', this.handlerMouserScroll, false)
        this.queryParams.type != 'add' ? this.getShiftAttendanceDetail() : this.resetTableColumn()
        // 详情不显示操作列
        const findIndex = this.equipmentTableColumn.findIndex((i) => i.prop == 'operation')
        this.equipmentTableColumn[findIndex].hasJudge = this.queryParams.type !== 'detail'
      })
    },
    // 获取考勤详情
    getShiftAttendanceDetail() {
      this.pageLoading = true
      this.$api.supplierAssess.queryDutyAttendanceById({ id: this.queryParams.id }).then((res) => {
        try {
          if (res.code == 200) {
            const data = res.data
            // this.controlCodeIds = data.controlCode
            console.log(this.controlCodeIds, 'this.controlCodeIds789')
            for (const key in this.form) {
              if (Object.prototype.hasOwnProperty.call(this.form, key)) {
                this.$set(this.form, key, data[key])
              }
            }
            if (this.queryParams.type == 'edit') {
              this.form.id = data.id
            }
            const labelArray = data.dutyPostName.split(',')
            const valueArray = data.dutyPostCode.split(',')
            const postIdArray = data.dutyPostId.split(',')
            this.punchInAndOutOfDutyList = valueArray.map((e, index) => {
              return {
                label: labelArray[index],
                value: e,
                dutyPostId: postIdArray[index]
              }
            })
            this.form.signRules.map((list) => {
              this.$nextTick(() => {
                this.$refs.shiftTablePage.toggleRowSelection(list, list.selected == 0)
              })
            })
            this.equipmentTableData = data.devices
            this.pageLoading = false
            // this.shiftPostCheckType = data.type == 1 || data.type == 4 ? 'checkbox' : 'radio'
            this.shiftPostCheckType = 'checkbox'
            this.resetTableColumn()
          }
        } catch (error) {
          this.pageLoading = false
        }
      })
    },
    // 关闭
    onClose() {
      this.$router.go(-1)
    },
    // 保存
    onPreservation() {
      this.formLoading = true
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.formLoading = false
          const attendanceTerminalIdArr = Array.from(this.equipmentTableData, ({ id }) => id) || []
          if (this.form.signDevice == '1' && attendanceTerminalIdArr.length == 0) {
            this.$message({
              message: '请添加打卡终端',
              type: 'warning'
            })
            return
          } else if (this.form.signRules.length == 0 || this.form.signRules.every((item) => item.shiftManagementId == '')) {
            this.$message({
              message: '请添加考勤班次',
              type: 'warning'
            })
            return
          }
          this.form.attendanceTerminalId = attendanceTerminalIdArr.toString()
          const parame = { attendanceDto: JSON.parse(JSON.stringify(this.form)), effectiveType: 0 }
          if (parame.attendanceDto.type == '4') {
            for (let i = 0; i < parame.attendanceDto.signRules.length; i++) {
              const shiftName = parame.attendanceDto.signRules.every((item) => item.shiftName)
              const allHaveValue = parame.attendanceDto.signRules[i].detailReqs.every((item) => item.shouldSignPersonName)
              if (shiftName === false) {
                this.$message({
                  message: '请选择班次',
                  type: 'warning'
                })
                return
              } else if (allHaveValue === false) {
                this.$message({
                  message: '请选择值班成员',
                  type: 'warning'
                })
                return
              }
            }
          }
          console.log(parame, 'parame')
          const apiStr = this.queryParams.type == 'add' ? 'insertDutyAttendance' : 'updateDutyAttendance'
          this.$api.supplierAssess[apiStr](parame).then((res) => {
            if (res.code == '200') {
              if (res.data && res.data.code == '299') {
                // 班次冲突
                this.shiftConflictEvent(res.data)
              } else {
                this.$message({
                  message: '保存成功',
                  type: 'success'
                })
                this.$router.go(-1)
              }
            } else {
              this.$message({
                message: res.message,
                type: 'error'
              })
            }
          })
        } else {
          this.formLoading = false
        }
      })
    },
    // 选择负责人
    selectLeader() {
      this.defaultCheckedShiftLeader = this.form.chargePersonId ? [this.form.chargePersonId] : []
      this.isMemberDialog = true
    },
    // 删除打卡值班岗
    handleCloseTag(value) {
      const dutyPostList = this.punchInAndOutOfDutyList.filter((i) => i.value !== value)
      this.setDutyPostData(dutyPostList)
      // 切换打卡值班岗
      if (this.form.type != 1) {
        this.resetShouldSignPerson()
      }
    },
    // 重置已选择打卡人员
    resetShouldSignPerson() {
      this.form.signRules.map((item) => {
        item.detailReqs.map((e) => {
          e.shouldSignPersonId = ''
          e.shouldSignPersonName = ''
        })
      })
    },
    handlerMouserScroll(event) {
      let detail = event.wheelDelta || event.detail
      let moveForwardStep = -1
      let moveBackStep = 1
      let step = 0
      step = detail > 0 ? moveForwardStep * 50 : moveBackStep * 50
      this.$refs['tagBox'].scrollBy({
        left: step
      })
    },
    // 考勤班次详情更改选择事件
    selectShift(type, row = {}) {
      this.defaultCheckedShiftData = {
        id: row.id
      }
      if (type == 'detail') {
        this.selectShiftDetailDialogShow = true
      } else {
        this.selectShiftDialogShow = true
      }
    },
    // 考勤班次详情更改选择事件 行内操作row数据不同
    selectShiftByRow(type, row) {
      if (type == 'del') {
        this.form.signRules = this.form.signRules.filter((i) => i.weekNum !== row.weekNum)
        this.form.signRules.map((list, index) => {
          list.weekNum = index + 1
          this.$nextTick(() => {
            this.$refs.shiftTablePage.toggleRowSelection(list, list.selected == 0)
          })
        })
      } else {
        this.defaultCheckedShiftData = {
          id: row.shiftManagementId,
          weekNum: row.weekNum
        }
        this.selectShiftDialogShow = true
      }
    },
    submitSelectShiftDialog(data) {
      this.selectShiftDialogShow = false
      // index可能存在等于0   为单项只修改单个，如果没有则替换所有
      if (data.weekNum || data.weekNum == 0) {
        this.form.signRules.map((list) => {
          if (list.weekNum == data.weekNum) {
            list.selected = 0
            list.shiftName = data.name
            list.shiftManagementId = data.id
            list.detailReqs = this.splitToArray(data)
            this.$nextTick(() => {
              this.$refs.shiftTablePage.toggleRowSelection(list, true)
            })
          }
        })
      } else {
        this.shiftData = data
        this.form.signRules.map((list) => {
          list.selected = 0
          list.shiftName = data.name
          list.shiftManagementId = data.id
          list.detailReqs = this.splitToArray(data)
          this.$nextTick(() => {
            this.$refs.shiftTablePage.toggleRowSelection(list, true)
          })
        })
      }
    },
    // 切换未打开下班逻辑
    changeWorkLogicRadio(val) {
      if (val == 1) {
        this.form.absenceWork = ''
        this.$refs.form.clearValidate('absenceWork')
      }
    },
    splitToArray(data) {
      const attendanceArray = data.attendanceTimeId.split(',')
      const timeArray = data.time.split(',')
      return attendanceArray.map((id, index) => {
        return {
          attendanceName: data.name,
          attendanceTimeId: id,
          shiftManagementId: data.id,
          signTimeStr: timeArray[index],
          signRuleId: '', // 父表主键
          shouldSignPersonId: '',
          shouldSignPersonName: '',
          signPersonNum: ''
        }
      })
    },
    // 班次列表及打卡终端table事件
    handleTableEvent(type, row) {
      const index = this.equipmentTableData.findIndex((i) => i.id == row.id)
      this.equipmentTableData.splice(index, 1)
    },
    // 切换班次规则
    changeShiftRules(type) {
      this.resetShiftData()
      this.changeShiftRuleLable()
    },
    // 增加考勤班次
    addShiftDay() {
      this.form.signRules.push({
        selected: 1,
        shiftName: '',
        shiftManagementId: '',
        detailReqs: [],
        weekNum: this.form.signRules.length + 1
      })
    },
    // 考勤班粗选中事件   当前条选中时，有批量配置为批量配置数据，无批量数据判断自己是否有班组，无班组不允许选中
    shiftSelectChange(selection, row) {
      // 如果是详情模式，不允许选中/取消选中
      if (this.queryParams.type === 'detail') {
        this.$nextTick(() => {
          // 恢复原来的选中状态
          this.$refs.shiftTablePage.toggleRowSelection(row, !selection.some((i) => i.weekNum === row.weekNum))
        })
        return
      }
      // 当前为选中
      if (selection.some((i) => i.weekNum == row.weekNum)) {
        // 如果当前有批量配置班次
        if (Object.keys(this.shiftData).length) {
          this.form.signRules.map((item) => {
            if (item.weekNum == row.weekNum) {
              item.selected = 0
              item.shiftName = this.shiftData.name
              item.shiftManagementId = this.shiftData.id
              item.detailReqs = this.splitToArray(this.shiftData)
            }
          })
        } else {
          if (!row.shiftManagementId) {
            this.$message({
              message: '请对当日增加班次后再选中！',
              type: 'warning'
            })
            this.$nextTick(() => {
              this.$refs.shiftTablePage.toggleRowSelection(row, false)
            })
          }
        }
      } else {
        // 取消选中 全部设置为休息
        this.form.signRules.map((item) => {
          if (item.weekNum == row.weekNum) {
            item.selected = 1
            item.shiftName = ''
            item.shiftManagementId = ''
            item.detailReqs = []
          }
        })
      }
    },
    // 考勤班粗全选事件 选中时如果有班次就勾选，没有就不勾选，有批量设置班次全部替换
    shiftSelectAllChange(selection) {
      // 如果是详情模式，不允许选中/取消选中
      if (this.queryParams.type === 'detail') {
        return
      }
      if (selection.length) {
        if (Object.keys(this.shiftData).length) {
          this.form.signRules.map((item) => {
            item.selected = 0
            item.shiftName = this.shiftData.name
            item.shiftManagementId = this.shiftData.id
            item.detailReqs = this.splitToArray(this.shiftData)
            this.$refs.shiftTablePage.toggleRowSelection(item, true)
          })
        } else {
          this.$nextTick(() => {
            this.form.signRules.map((item) => {
              item.selected = !!item.shiftManagementId ? 0 : 1
              this.$refs.shiftTablePage.toggleRowSelection(item, !!item.shiftManagementId)
            })
          })
        }
      } else {
        // 取消选中 全部设置为休息
        this.form.signRules.map((item) => {
          this.$refs.shiftTablePage.toggleRowSelection(item, false)
          item.selected = 1
          item.shiftName = ''
          item.shiftManagementId = ''
          item.detailReqs = []
        })
      }
    },
    // 切换要求打卡人数
    changeSignPersonNum(item, timeIndex, row) {
      // 存储当前选中条数及如何通过tabledata获取并修改该数据
      this.checkShiftTimeData = {
        weekNum: row.weekNum,
        timeIndex: timeIndex,
        ...item
      }
      const shouldSignPerson = item.shouldSignPersonId.split(',') || []
      // 如果没有要求人数或者要求人数小于当前选择人数，则清空选择
      if (!item.signPersonNum || item.signPersonNum < shouldSignPerson.length) {
        this.form.signRules.map((e) => {
          if (e.weekNum == row.weekNum) {
            e.detailReqs[timeIndex].shouldSignPersonId = ''
            e.detailReqs[timeIndex].shouldSignPersonName = ''
          }
        })
      }
    },
    // 切换table序号名称
    changeShiftRuleLable() {
      this.$set(this.tableColumn[1], 'label', this.form.shiftRule == 2 ? '工作日' : '天')
    },
    // 切换显示考勤班次列
    resetTableColumn() {
      const notShowFieldArray = {
        1: ['signPersonNum', 'shouldSignPersonName', 'BC', 'ZBCY'],
        2: ['shouldSignPersonName', 'BC', 'ZBCY'],
        3: ['BC', 'ZBCY'],
        4: ['shiftName', 'signPersonNum', 'shouldSignPersonName']
      }
      let notSHhowByType = notShowFieldArray[this.form.type]
      // 详情不显示勾选和操作列
      if (this.queryParams.type == 'detail') {
        notSHhowByType.push(...['operation'])
      }
      this.tableColumn = this.tableColumn.map((item) => {
        let hasJudge = !notSHhowByType.includes(item.prop)
        let label = item.label
        if (item.type == 'index') {
          label = this.form.shiftRule == 2 ? '工作日' : '天'
        }
        return {
          ...item,
          label,
          hasJudge
        }
      })
      this.$forceUpdate()
    },
    // 增加打卡终端
    addEquipment() {
      this.defaultCheckedAttendanceTerminalData = Array.from(this.equipmentTableData, ({ id }) => id) || []
      this.selectAttendanceTerminalDialogShow = true
    },
    // 提交选中打卡终端
    submitSelectAttendanceTerminalDialog(list) {
      this.equipmentTableData = [...this.equipmentTableData, ...list]
      this.selectAttendanceTerminalDialogShow = false
    },
    // 选择值班岗弹窗
    selectShiftPostEvent() {
      this.defaultCheckedShiftPost = Array.from(this.punchInAndOutOfDutyList, ({ value }) => value)
      this.shiftPostDialogShow = true
    },
    // 值班岗选择确认
    submitPostDialog(data) {
      const dutyPostList = data.map((e) => {
        return {
          label: e.dutyPostName,
          value: e.dutyPostCode,
          dutyPostId: e.id
        }
      })
      this.setDutyPostData(dutyPostList)
      this.shiftPostDialogShow = false
      // 切换打卡值班岗
      if (this.form.type != 1) {
        this.resetShouldSignPerson()
      }
    },
    // 改变值班考勤组类型
    changeAttendanceGroupType(type) {
      // this.shiftPostCheckType = type == 1 || type == 4 ? 'checkbox' : 'radio'
      this.shiftPostCheckType = 'checkbox' // 3.4.30需求改为全选
      // 重置打开值班岗及考勤班次
      this.setDutyPostData([])
      this.resetTableColumn()
      this.resetShiftData()
    },
    // 设置打卡值班岗数据
    setDutyPostData(data) {
      this.punchInAndOutOfDutyList = data
      this.form.dutyPostName = Array.from(this.punchInAndOutOfDutyList, ({ label }) => label).toString() || ''
      this.form.dutyPostCode = Array.from(this.punchInAndOutOfDutyList, ({ value }) => value).toString() || ''
      this.form.dutyPostId = Array.from(this.punchInAndOutOfDutyList, ({ dutyPostId }) => dutyPostId).toString() || ''
    },
    // 重置考勤班次相关信息
    resetShiftData() {
      this.shiftData = {}
      this.form.signRules = []
      if (this.form.shiftRule == 1) {
        this.form.signRules.push({
          selected: 1,
          shiftName: '',
          shiftManagementId: '',
          weekNum: 1,
          detailReqs: []
        })
      } else {
        for (let i = 0; i < 7; i++) {
          this.form.signRules.push({
            selected: 1,
            shiftName: '',
            shiftManagementId: '',
            weekNum: i + 1,
            detailReqs: []
          })
        }
      }
    },
    filterSequence(index) {
      if (this.form.shiftRule == 2) {
        const chineseNumbers = ['一', '二', '三', '四', '五', '六', '日']
        // 按周
        return '周' + chineseNumbers[index - 1]
      } else {
        // 按天
        return '第' + index + '天'
      }
    },
    // 显示要求打开人员信息
    // getShowNames(item) {
    //   return item.
    // },
    // 成员确认
    submitMemberDialog(list) {
      const selectData = list[0]
      this.form.chargePersonName = selectData.staffName
      this.form.chargePersonId = selectData.staffId
      this.isMemberDialog = false
    },
    // 班次冲突事件
    shiftConflictEvent(data) {
      this.conflictData = data
      this.shiftConflictDialogShow = true
    },
    // 选择要求打卡人员
    selectSignPeople(item, timeIndex, row) {
      console.log(item, 'ite111')
      console.log(timeIndex, 'ite222')
      console.log(row, 'ite333')
      if (!this.punchInAndOutOfDutyList.length) {
        this.$message({
          message: '请先选择打卡值班岗！',
          type: 'warning'
        })
      } else {
        this.filterShiftPost = this.form.dutyPostCode
        // 如果已有选中的人员ID
        if (item.shouldSignPersonId) {
          this.defaultCheckedshiftPostPeople = item.shouldSignPersonId.split(',') || []
        } else {
          this.defaultCheckedshiftPostPeople = []
        }
        // 获取已选人员详细信息
        this.selectedShiftPostPeople = []
        // 如果有已选人员，需要获取完整人员信息
        if (item.shouldSignPersonId && item.shouldSignPersonName) {
          const ids = item.shouldSignPersonId.split(',')
          const names = item.shouldSignPersonName.split(',')
          // 构建已选人员列表
          this.selectedShiftPostPeople = ids.map((id, index) => {
            return {
              staffId: id,
              staffName: names[index],
              phone: '' // 注意：这里没有电话信息，如果需要完整展示还需要通过API获取
            }
          })
        }
        this.checkShiftTimeData = {
          weekNum: row.weekNum,
          timeIndex: timeIndex,
          ...item
        }
        this.shiftPostPeopleDialogShow = true
      }
    },
    // 值班岗成员确认
    submitshiftPostPeopleDialog(list) {
      if (this.form.type != '4') {
        if (list.length > this.checkShiftTimeData.signPersonNum) {
          this.$message({
            message: '选中人数大于要求人数！',
            type: 'warning'
          })
          return
        }
      }
      console.log(list, '选择的成员')
      const shouldSignPersonIds = Array.from(list, ({ staffId }) => staffId).toString() || ''
      const shouldSignPersonNames = Array.from(list, ({ staffName }) => staffName).toString() || ''
      this.form.signRules.map((e) => {
        if (e.weekNum == this.checkShiftTimeData.weekNum) {
          e.detailReqs[this.checkShiftTimeData.timeIndex].shouldSignPersonId = shouldSignPersonIds
          e.detailReqs[this.checkShiftTimeData.timeIndex].shouldSignPersonName = shouldSignPersonNames
        }
      })
      this.shiftPostPeopleDialogShow = false
    },
    // 打开岗位关联人员列表弹窗
    openPeopleListDialog() {
      this.peopleListByShiftPostDialogShow = true
    }
  }
}
</script>
<style scoped lang="scss">
.attendance-content {
  background-color: #fff;
  height: 100%;
  position: relative;
  .classese-content-title {
    border-bottom: 1px solid #e4e7ed;
    padding: 12px 24px;
    cursor: pointer;
    margin-bottom: 24px;
  }
  .content_box {
    padding: 0 24px;
    background: #fff;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    height: calc(100% - 100px);
    .text-style {
      cursor: pointer;
      color: #3562db;
    }
    .sign-person-num-item {
      ::v-deep(.el-form-item__content) {
        line-height: 0px;
      }
    }
    ::v-deep(.el-table) {
      .el-table__cell {
        line-height: normal;
      }
      .table-rows {
        height: 50px;
        line-height: 50px;
        .sing-people-name {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .select-sign-people {
          padding-left: 8px;
          flex-shrink: 0;
          cursor: pointer;
          color: #3562db;
        }
      }
      .flex {
        display: flex;
        justify-content: space-between;
        margin-left: 80px;
      }
    }
    .duty-box {
      width: 100%;
      height: 32px;
      line-height: 32px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      margin-top: 4px;
      display: flex;
      ::-webkit-scrollbar {
        display: none;
      }
      .tag-box {
        flex: 1;
        overflow-y: hidden;
        overflow-x: scroll;
        white-space: nowrap;
        padding: 0 8px;
        ::v-deep(.el-tag) {
          background: #f6f5fa;
          border-radius: 4px;
          font-size: 12px;
          font-family: 'PingFang SC-Regular', 'PingFang SC';
          font-weight: 400;
          color: #121f3e;
          border: none;
          margin-right: 8px;
          .el-tag__close {
            color: #121f3e;
            font-size: 14px;
            &:hover {
              color: #fff;
              background-color: #3562db;
            }
          }
        }
      }
      .box-select {
        flex-shrink: 0;
        padding: 0 8px;
      }
    }
    .binding {
      margin-left: 120px;
      color: #3562db;
      font-size: 14px;
    }
    .shift-change-box {
      .shift-name {
        color: #121f3e;
        margin: 0 10px;
      }
      .change-shift-btn {
        margin-left: 10px;
        cursor: pointer;
        color: #3562db;
      }
    }
  }
}
</style>
