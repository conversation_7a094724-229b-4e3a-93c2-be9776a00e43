<template>
  <!-- <sinoPanel v-loading="blockLoading" :title="title" type="button" :isClose="true" :setHeight="true" class="writtenHeight" @close="$router.go(-1)" @complete="complete">
    <template slot="content">
      
    </template>
  </sinoPanel> -->
  <div style="height: 100%;">
    <div class="content_box">
      <div class="pointTitle">
        <div class="line"></div>
        <div class="titleText">基本信息</div>
      </div>
      <el-form ref="formInline" :model="formInline" :inline="true" class="form-inline" label-width="100px" :rules="rules">
        <el-form-item label="巡检点名称" prop="taskPointName">
          <el-input v-model.trim="formInline.taskPointName" :readonly="readonly" placeholder="请输入巡检点名称" maxlength="50" class="width_lengthen"></el-input>
        </el-form-item>
        <el-form-item label="巡检点编码" prop="taskPointCode" style="margin-left: 20px;">
          <el-input v-model.trim="formInline.taskPointCode" :readonly="readonly" placeholder="请输入巡检点编码" maxlength="64" class="width_lengthen"></el-input>
        </el-form-item>
        <div style="display: flex; flex-wrap: wrap;">
          <el-form-item label="巡检点类型" prop="">
            <el-select v-model.trim="formInline.taskPointTypeId" filterable placeholder="巡检点类型" class="width_lengthen" @change="changeTaskPoint">
              <el-option v-for="item in taskPointTypeArr" :key="item.dictCode" :label="item.dictName" :value="item.dictCode"> </el-option>
            </el-select>
          </el-form-item>
          <!-- 选择空间 -->
          <!-- <div v-show="formInline.taskPointTypeId=='SPACE_001'" style="display:flex;margin-left:30px;">
            <el-button class="sino-button-sure-search" @click="selectSpace('kj')" style="max-width:100px;min-width:90px;width:auto;">选择空间</el-button>
            <div class="selectArr" style="margin-left:10px;" v-if="spaceName!==''">
              <div>
                  <span>{{spaceName}}</span>
                </div>
            </div>
            <div class="selectDvArr" v-else>
            <div v-for="(item,index) in selectSpaceArr" :key="index">
              <span>{{item.address}}</span>
              <span v-show="selectSpaceArr.length>1 && index !== selectSpaceArr.length-1">、</span>
            </div>
          </div>
        </div> -->
          <el-form-item v-show="formInline.taskPointTypeId == 'SPACE_001'" label="选择空间" prop="" style="display: flex; margin-left: 30px;">
            <el-cascader
              ref="myCascader"
              v-model="selectSpaceArr"
              :props="riskPropsType"
              :options="spaceOption"
              :collapse-tags="true"
              placeholder="请选择空间位置"
              class="sino_sdcp_input mr15"
              
              @change="hangdleChange"
            ></el-cascader>
          </el-form-item>
          <!-- 选择设备 -->
          <div v-show="formInline.taskPointTypeId == 'DEVICE_001'" class="selectDevBox">
            <div class="selectDevClass">
              <el-button type="primary" style="min-width: 100px; width: auto; height: 40px;" @click="selectDevice('dv')">请选择设备</el-button>
              <div v-if="deviceName !== ''" class="selectArr" style="margin-left: 20px;">
                <div>
                  <span>已选设备名称：{{ deviceName }}</span>
                </div>
              </div>
              <div v-for="(item, index) in selectDeviceArr" v-else :key="index" class="selectDvArr" style="height: 40px; line-height: 40px;">
                <span>已选设备名称：{{ item.assetsName }}</span>
                <span v-show="selectDeviceArr.length > 1 && index !== selectDeviceArr.length - 1">、</span>
              </div>
            </div>
            <el-form-item v-show="selectDeviceArr.length || formInline.id !== ''" label="设备所在网格" style="display: flex; margin-left: 20px;">
              <el-input v-model.trim="formInline.engineerRegion" readonly placeholder="设备所在网格" style="width: 240px;"></el-input>
            </el-form-item>
          </div>
          <!-- 选择自定义-->
          <el-form-item v-show="formInline.taskPointTypeId !== 'DEVICE_001' && formInline.taskPointTypeId !== 'SPACE_001'" label="自定义内容" style="margin-left: 22px;">
            <el-input v-model.trim="formInline.customText" :readonly="readonly" placeholder="请输入自定义内容" maxlength="50" class="width_lengthen"></el-input>
          </el-form-item>
        </div>
        <div style="width: 100%; display: flex;">
          <el-form-item label="备注" class="itemLabel">
            <el-input
              v-model.trim="formInline.remarks"
              :readonly="readonly"
              style="width: 350px;"
              type="textarea"
              class="project-textarea"
              placeholder="请输入备注，限制200字以内"
              show-word-limit
              :autosize="{ minRows: 4, maxRows: 6 }"
              maxlength="200"
            ></el-input>
          </el-form-item>
        </div>
      </el-form>
      <div class="pointTitle" style="margin: 20px 0;">
        <div class="line"></div>
        <div class="titleText">定位点信息</div>
      </div>
      <div>
        <span style="color: #606266; font-size: 14px;">关联定位点：</span>
        <el-button type="primary" style="min-width: 80px; width: auto;" @click="selectLocation">选择定位点</el-button>
        <!-- <div class="selectArr" v-if="!selectLocationArr.length">
            <div >
              <span>{{formInline.locationPointName?formInline.locationPointName:'暂无'}}</span>
            </div>
        </div> -->
        <!-- v-else -->
        <div class="selectArr">
          <div v-for="(item, index) in selectLocationArr" :key="index">
            <span>{{ item.locationPointName }}</span>
            <span v-show="selectLocationArr.length > 1 && index !== selectLocationArr.length - 1">、</span>
          </div>
        </div>
      </div>
    </div>
    <div class="bottomBar">
      <div class="bottomWrap">
        <el-button v-if="query.type != 'details'" type="primary" @click="complete">确定</el-button>
        <el-button type="primary" plain @click="$router.go('-1')">关闭</el-button>
      </div>
    </div>
    <!-- 选择空间、设备等 -->
    <SelectTableList ref="materialDialog" :dialogVisibleWz="dialogVisibleWz" :spaceIds="spaceId" @closeDialog="closeDialogFn" @addMaterials="addMaterials" />
    <!-- 使用中定位点组件 -->
    <SelectLocationPoint ref="locationMaterialDialog" :dialogVisibleWz="dialogVisibleLp" @multipleSelection="multipleSelectionFn" @closeDialog="closeDialogFnLp" />
    <!-- 未使用定位点组件 -->
    <SelectDevicePoint
      ref="deviceMaterialDialog"
      :dialogVisibleWz="dialogVisibleDv"
      :deviceId="spaceId"
      @closeDialog="closeDialogFnDv"
      @multipleSelection="multipleSelectionDvFn"
    />
  </div>
</template>
<script>
import SelectTableList from './components/selectTableList' // 选择空间、设备
import SelectLocationPoint from './components/selectLocationPoint' // 选择定位点
import SelectDevicePoint from './components/selectDevicePoint' // 选择定位点

export default {
  name: 'addTaskPoint',
  components: { SelectTableList, SelectLocationPoint, SelectDevicePoint },
  beforeRouteEnter(to, from, next) {
    if (to.meta.title) {
      to.meta.title = to.query.type == 'add' ? '新增巡检点' : to.query.type == 'edit' ? '编辑巡检点' : '巡检点详情'
    }
    next()
  },
  data() {
    let formMate = (rule, value, callback) => {
      /* eslint-disable */
      var flag = new RegExp("[`~!@$^&*()=|{}':;',\\[\\].<>《》/?~！@￥……&*（）——|{}【】‘；：”“'。，、？ ]")
      if (flag.test(value)) {
        callback(new Error('名称不能包含特殊字符'))
      } else {
        callback()
      }
    }
    return {
      dialogVisibleWz: false,
      dialogVisibleLp: false,
      dialogVisibleDv: false,
      title: '',
      readonly: false,
      formInline: {
        taskPointName: '',
        taskPointCode: '',
        taskPointTypeId: 'SPACE_001',
        taskPointTypeName: '',
        locationPointId: '',
        locationPointName: '',
        customText: '',
        locationCode: '',
        location: '', // 所处区域
        buildingCode: '', // 区域楼宇代码
        building: '', // 区域楼宇
        floorCode: '', // 楼层代码
        floor: '', // 楼层
        roomCode: '', // 网格编码
        roomName: '', // 房间号

        engineerCode: '', // 设备编号
        engineerName: '', // 设备名称
        engineerBrand: '', // 设备品牌
        engineerModel: '', // 规格型号
        engineerRegion: '', // 设备所处区域（网格）

        remarks: '',
        id: '' //模板id，修改必传
      },
      deviceName: '',
      spaceName: '',
      spaceId: '',
      taskPointTypeIdByDictCode: '', //新增时传递的taskPointTypeId
      parentDictList: [],
      unitOptions: [],
      termTypeOptions: [],
      taskPointTypeArr: [],
      query: {},
      id: '',
      blockLoading: false,
      rules: {
        taskPointName: [
          { required: true, message: '请输入巡检点名称', trigger: 'change' },
          { validator: formMate, trigger: 'blur' }
        ],
        taskPointCode: [{ required: true, message: '请输入巡检点编码', trigger: 'change' }]
      },
      currentPage: 1,
      total: 0,
      fileList: [],
      parentName: '',
      updateId: '',
      title: '新增任务点管理',
      selectLocationArr: [],
      selectDeviceArr: [],
      selectSpaceArr: [],
      spaceOption: [],
      riskPropsType: {
        children: 'children',
        label: 'gridName',
        value: 'id',
        checkStrictly: true,
        multiple: false
      },
      defaultCheck: '',
      allData: null,
      parentIds: null
    }
  },
  created() {
    console.log(this.$route.query)
    this._findDictionaryTableList() //获取任务点类型
    this.getSpaceData()
  },
  mounted() {
    this.query = this.$route.query
    if (this.query.id) {
      this.detailId = this.query.id
      this.formInline.id = this.query.id
      this.title = '编辑任务点管理'
      this._getTaskPointDetails() //获取定位点管理详情
    }
  },
  methods: {
    //选择任务点类型
    changeTaskPoint(val) {
      console.log(val)
      if (val == 'SPACE_001') {
        this.selectDeviceArr = []
        this.formInline.engineerRegion = ''
      } else if (val == 'DEVICE_001') {
        this.selectSpaceArr = []
      } else {
        this.selectDeviceArr = []
        this.formInline.engineerRegion = ''
        this.selectSpaceArr = []
      }
      this.formInline.taskPointTypeId = val
    },
    //选择空间
    // selectSpace(type){
    //   this.dialogVisibleWz = !this.dialogVisibleWz
    // },
    // new选择空间
    hangdleChange(type) {
      const names = []
      type.forEach((i) => {
        this.allData.find((j) => {
          if (i == j.id) {
            names.push(j.gridName)
          }
        })
      })
      this.location = names.join('>')
      this.parentIds = type
    },
    addMaterials(data) {
      // console.log(data)
      if (data) {
        this.spaceName = ''
      }
      this.selectSpaceArr = data
    },
    //选择定位点
    selectLocation() {
      this.dialogVisibleLp = !this.dialogVisibleLp
    },
    //获取定位点已选数据
    multipleSelectionFn(data) {
      // console.log(data)
      this.selectLocationArr = data
    },
    //获取设备列表
    getTableDatasFn(data) {
      // console.log(data)
      this.rendTableData = data
    },
    //选择设备
    selectDevice() {
      this.dialogVisibleDv = !this.dialogVisibleDv
      if (this.rendTableData) {
        this.rendTableDataList = this.rendTableData
      }
    },
    //获取设备已选数据
    multipleSelectionDvFn(data) {
      console.log('已选设备data', data)
      if (data) {
        this.deviceName = ''
      }
      this.selectDeviceArr = data
      this.formInline.engineerRegion = this.selectDeviceArr[0].areaName.replace(/,/g, '>') //设备所在网格
    },
    closeDialogFn() {
      this.dialogVisibleWz = false
    },
    closeDialogFnLp() {
      this.dialogVisibleLp = false
    },
    closeDialogFnDv() {
      this.dialogVisibleDv = false
    },
    //获取任务点类型
    _findDictionaryTableList() {
      let data = {
        dictCode: '',
        dictName: '',
        pageNo: 1,
        pageSize: 9999,
        dictType: 'task_point_type'
      }
      this.$api.findDictionaryTableList(data).then((res) => {
        const { code, data, message } = res
        if (code == 200) {
          this.taskPointTypeArr = data.list
        } else {
          this.$message.error(message)
        }
      })
    },
    //获取详情
    _getTaskPointDetails() {
      let data = {
        id: this.detailId
      }
      this.$api.getTaskPointDetails(data).then((res) => {
        const { code, data, message } = res
        if (code == 200) {
          this.formInline = data
          console.log('data', data)
          //需要回显一下任务点类型，，，回显dictCode
          this.formInline.taskPointTypeId = data.taskPointTypeCode
          this.floorCode = data.floorCode || ''
          this.floor = data.floor || ''
          this.locationCode = data.locationCode || ''
          this.location = data.location || ''
          this.buildingCode = data.buildingCode || '' // 区域楼宇代码
          this.building = data.building || '' // 区域楼宇
          this.roomCode = data.roomCode || '' // 房间编码
          this.roomName = data.roomName || ''
          this.engineerCode = data.engineerCode || ''
          this.engineerName = data.engineerName || '' //设备名称
          this.engineerBrand = data.engineerBrand || '' //设备分类名称
          this.engineerModel = data.engineerModel || '' //设备分类编码
          //回显空间网格名称
          if (data.location || data.building || data.floor) {
            const a = []
            if (data.locationCode) {
              a[0] = data.locationCode
            }
            if (data.buildingCode) {
              a[1] = data.buildingCode
            }
            if (data.floorCode) {
              a[2] = data.floorCode
            }
            if (data.roomCode) {
              a[3] = data.roomCode
            }
            this.selectSpaceArr = a
          } else {
            this.spaceName = ''
          }
          if (data.locationPointInfos) {
            this.selectLocationArr = JSON.parse(data.locationPointInfos)
          }
          this.spaceId = data.sourceId || ''
          //回显设备名称、设备所在网格
          if (data.engineerName || data.engineerRegion) {
            this.deviceName = data.engineerName
            this.formInline.engineerRegion = data.engineerRegion.replace(/&gt;/g, '>')
          }
          // 回显定位点信息
          if (data.locationPointName) {
            this.selectLocationArr = data.locationPointName.split(',').map((i) => {
              const item = {}
              item.locationPointName = i
              return item
            })
          }
        }
      })
    },
    //点击确定
    complete() {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          if (this.formInline.taskPointTypeId == 'SPACE_001' && this.selectSpaceArr.length != 1 && !this.location) {
            this.$message.error('请选择一个空间区域！')
            this.blockLoading = false
            return
          }
          if (this.formInline.taskPointTypeId == 'DEVICE_001' && this.selectDeviceArr.length == 0 && !this.deviceName) {
            this.$message.error('请选择设备！')
            this.blockLoading = false
            return
          }
          if (this.formInline.taskPointTypeId !== 'SPACE_001' && this.formInline.taskPointTypeId !== 'DEVICE_001' && this.formInline.customText == '') {
            this.$message.error('请填写自定义内容！')
            this.blockLoading = false
            return
          }
          const { formInline, taskPointTypeArr, selectLocationArr, selectDeviceArr, selectSpaceArr } = this
          //传递任务点类型id，和dictName
          if (taskPointTypeArr && taskPointTypeArr.length) {
            taskPointTypeArr.forEach((val, index) => {
              if (formInline.taskPointTypeId == val.dictCode) {
                formInline.taskPointTypeName = val.dictName
                this.taskPointTypeIdByDictCode = val.id
              }
            })
          }
          let location = {},
            building = {},
            floor = {},
            room = {}
          //定位点
          let selectPointId =
            selectLocationArr &&
            selectLocationArr.length &&
            selectLocationArr.map((val, index) => {
              return val.id
            })
          let selectPointName =
            selectLocationArr &&
            selectLocationArr.length &&
            selectLocationArr.map((val, index) => {
              return val.locationPointName
            })
          // locationPointId:selectLocationArr.length?selectPointId.join(','):'' || formInline.locationPointId,
          // locationPointName:selectLocationArr.length?selectPointName.join(','):'' || formInline.locationPointName,
          let item = this.formInline.taskPointTypeId == 'SPACE_001' ? this.allData.find((i) => i.id == selectSpaceArr[selectSpaceArr.length - 1]) : ''
          console.log('item', item)
          const parentIdArr = []
          // 空间
          if (item) {
            if (this.selectSpaceArr.length > 0) {
              this.selectSpaceArr
                .toString()
                .split(',')
                .forEach((i) => {
                  if (i != '#' && i != '' && i != 'nullnull') {
                    parentIdArr.push(i)
                  }
                })
            } else {
              this.parentIds
                .toString()
                .split(',')
                .forEach((i) => {
                  if (i != '#' && i != '' && i != 'nullnull') {
                    parentIdArr.push(i)
                  }
                })
            }
            if (item.gridLevel == 2) {
              // 区域location
              location.id = item.id
              location.name = item.gridName
            } else if (item.gridLevel == 3) {
              // 楼宇building
              building.id = item.id
              building.name = item.gridName
              if (parentIdArr.length > 0) {
                parentIdArr.forEach((i) => {
                  const child = this.allData.find((node) => node.id == i)
                  if (child && child.gridLevel == 2) {
                    location.id = child.id
                    location.name = child.gridName
                  }
                })
              }
            } else if (item.gridLevel == 4) {
              // 楼层floor
              floor.id = item.id
              floor.name = item.gridName
              if (parentIdArr.length > 0) {
                parentIdArr.forEach((i) => {
                  const child = this.allData.find((node) => node.id == i)
                  if (child && child.gridLevel == 3) {
                    building.id = child.id
                    building.name = child.gridName
                  } else if (child && child.gridLevel == 2) {
                    location.id = child.id
                    location.name = child.gridName
                  }
                })
              }
            } else if (item.gridLevel == 5) {
              // 房间room
              room.id = item.id
              room.name = item.gridName
              if (parentIdArr.length > 0) {
                parentIdArr.forEach((i) => {
                  const child = this.allData.find((node) => node.id == i)
                  if (child && child.gridLevel == 4) {
                    floor.id = child.id
                    floor.name = child.gridName
                  } else if (child && child.gridLevel == 3) {
                    building.id = child.id
                    building.name = child.gridName
                  } else if (child && child.gridLevel == 2) {
                    location.id = child.id
                    location.name = child.gridName
                  }
                })
              }
            }
          }
          //设备
          const sourceArr = []
          if (this.selectDeviceArr.length !== 0) {
            const sourceIdArr = this.selectDeviceArr[0].areaCode.split(',')
            const sourceNameArr = this.selectDeviceArr[0].areaName.split(',')
            for (let i = 0; i < sourceIdArr.length; i++) {
              sourceArr[i] = {
                id: sourceIdArr[i],
                name: sourceNameArr[i]
              }
            }
            location = sourceArr[1] || {}
            building = sourceArr[2] || {}
            floor = sourceArr[3] || {}
            room = sourceArr[4] || {}
            this.location = formInline.engineerRegion
          }
          let data = {
            taskPointName: formInline.taskPointName,
            taskPointCode: formInline.taskPointCode,
            taskPointTypeId: this.taskPointTypeIdByDictCode, //id
            taskPointTypeCode: formInline.taskPointTypeId, //任务点类型code
            taskPointTypeName: formInline.taskPointTypeName,
            locationPointInfos: JSON.stringify(selectLocationArr),
            customText: formInline.customText, //自定义

            // 选择设备传值
            engineerCode: selectDeviceArr.length ? selectDeviceArr[0].assetsNumber : '' || this.engineerCode,
            engineerName: selectDeviceArr.length ? selectDeviceArr[0].assetsName : '' || this.deviceName, //设备名称
            engineerBrand: selectDeviceArr.length ? selectDeviceArr[0].competentDeptTypeName : '' || this.engineerBrand, //设备分类名称
            engineerModel: selectDeviceArr.length ? selectDeviceArr[0].competentDeptTypeCode : '' || this.engineerModel, //设备分类编码
            engineerRegion: formInline.engineerRegion,

            // 选择空间传值
            floorCode: floor.id ? floor.id : this.floorCode || '', //楼层code
            floor: floor.name ? floor.name : this.floor || '', //楼层name
            locationCode: location.id ? location.id : this.locationCode || '', //院区code
            location: this.location || '', //院区name
            buildingCode: building.id ? building.id : this.buildingCode || '', // 区域楼宇代码
            building: building.name ? building.name : this.building || '', // 区域楼宇
            roomCode: room.id ? room.id : this.roomCode || '', // 房间code
            roomName: room.name ? room.name : this.roomName || '', // 房间name

            remarks: formInline.remarks,
            sourceId: selectSpaceArr.length ? selectSpaceArr[0].regionCode : selectDeviceArr.length ? selectDeviceArr[0].id : '' || this.spaceId,
            id: formInline.id
          }
          this.blockLoading = true
          this.$api.addTaskPoint(data).then((res) => {
            this.blockLoading = false
            if (res.code == 200) {
              this.$message.success(res.message)
              this.$router.go(-1)
            } else {
            }
          })
        }
      })
    },
    findNode(tree, func) {
      for (const node of tree) {
        if (func(node)) return node
        if (node.children) {
          const res = this.findNode(node.children, func)
          if (res) return res
        }
      }
      return null
    },
    // 获取空间列表
    getSpaceData() {
      const data = {}
      this.$api.ipsmGetGridList(data).then((res) => {
        if (res.code == '200') {
          this.allData = res.data
          let treeList = this.$tools.transData(res.data, 'id', 'parentId', 'children')
          this.spaceOption = treeList
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.bottomBar {
  height: 52px;
  width: 100%;
  background-color: #fff;
  display: flex;
  justify-content: left;
  align-items: center;

  .bottomWrap {
    padding: 0 16px;
  }
}

.sino-content {
  height: calc(100% + 60px);
}

.selectDevClass {
  max-width: 500px;
  min-width: 270px;
  width: auto;
  display: flex;
}

.selectDevBox {
  display: flex;
  margin-left: 30px;
  width: auto;
}

.pointTitle {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  .line {
    width: 4px;
    height: 20px;
    background: #5188fc;
  }

  .titleText {
    color: #5b5b5b;
    font-weight: 600;
    margin-left: 10px;
  }
}

.selectArr {
  width: 62%;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin: 20px 0;
  color: #5b5b5b;
}

.selectDvArr {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  color: #5b5b5b;
  margin-left: 20px;
}

.content_box {
  height: calc(100% - 82px);
  overflow-y: auto;
  margin: 15px;
  padding: 20px;
  background: #fff;
  display: flex;
  flex-direction: column;
}

.project-textarea textarea {
  height: 120px;
}

.sion-icon {
  cursor: pointer;
  padding-left: 3px;
  color: #5188fc;
}

.icon-disabled {
  cursor: not-allowed;
}

.form-inline {
  .el-input,
  .el-select,
  .el-cascader {
    width: 300px;
  }
}

.inspection-content {
  width: 1200px;
  padding-left: 42px;
  font-size: 14px;
  font-weight: 400;
  color: rgb(96 98 102 / 100%);

  .content-block {
    margin-bottom: 20px;

    .porject-name {
      .porject {
        display: inline-block;
      }

      .porject-index {
        width: 50px;

        .index-icon {
          position: relative;
          right: 8px;
          top: 1px;
        }
      }

      .porject-input {
        width: 795px;

        .el-input {
          display: inline-block;
          width: 90%;
        }
      }

      .porject-button {
        width: 200px;
        padding-left: 30px;
      }
    }

    .termContent {
      padding-left: 52px;
      margin-top: 20px;

      .termContent-input {
        display: inline-block;
        width: auto;
        margin-right: 23px;

        .el-input {
          display: inline-block;
          width: 400px;
        }

        .termContent-button {
          height: 42px;
          line-height: 42px;
          color: #5188fc;
          margin-right: 20px;
          cursor: pointer;
        }

        .button-detele {
          color: rgb(252 44 97 / 100%);
        }
      }

      .termContent-tools {
        padding-left: 75px;
        margin-top: 20px;

        .termContent-number {
          display: inline-block;
          margin-right: 20px;

          .el-input,
          .el-select {
            display: inline-block;
            width: 100px;
            margin-left: 10px;
          }
        }
      }

      .termContent-radio {
        .radio-text {
          .el-input {
            display: inline-block;
            width: 300px;
          }
        }
      }
    }
  }
}

.form-inline {
  // width:1100px;
  width: 100%;

  .itemLabel {
    display: flex;
  }
}

.form-inline .width_lengthen {
  width: 350px;
}

:deep(.el-upload-dragger) {
  width: 255px;
  height: 164px;
}

:deep(.sino-vertical-file>.el-upload__tip) {
  top: -43px;
  left: 57px;
  color: #ccc;
}

:deep(.el-input__inner) {
  height: 40px !important;
  line-height: 40px !important;
}
</style>
                