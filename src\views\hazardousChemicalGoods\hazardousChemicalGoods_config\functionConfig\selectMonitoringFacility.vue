<!--
 * @Author: hedd heding<PERSON>@sinomis.com
 * @Date: 2025-02-28 19:11:06
 * @LastEditors: hedd heding<PERSON>@sinomis.com
 * @LastEditTime: 2025-02-28 19:12:48
 * @FilePath: \ihcrs_pc\src\views\hazardousChemicalGoods\hazardousChemicalGoods_config\functionConfig\selectMonitoringFacility.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="inner">
    <div class="left">
      <div class="typeTitle">运行监控</div>
      <div class="itemGroup">
        <el-tree
          ref="tree"
          v-loading="treeLoading"
          :data="typeDataList"
          node-key="dictionaryDetailsId"
          :props="defaultProps"
          :highlight-current="true"
          @node-click="handleNodeClick"
        ></el-tree>
      </div>
    </div>
    <div class="right">
      <div class="filterWrap">
        <el-input v-model="searchText" :rows="2" style="width: 240px" placeholder="资产名称/SN/通用名">
          <i slot="suffix" class="el-icon-search"></i>
        </el-input>
        <div class="searchBtn">
          <el-button type="primary" @click="search">搜索</el-button>
          <el-button type="primary" plain @click="reset">重置</el-button>
        </div>
      </div>
      <div class="selected">
        <span>已选择:</span>
        <div class="tagsWrap">
          <el-tag v-for="tag in tagsData" :key="tag.id" type="info" closable @close="deleteTag(tag.id)">
            {{ tag.assetsName }}
          </el-tag>
        </div>
      </div>
      <el-table ref="table" v-loading="tableLoading" :data="tableData" height="calc(100% - 112px)" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55"> </el-table-column>
        <el-table-column type="index" label="序号"> </el-table-column>
        <el-table-column label="设备属性" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.isMainDevice == 1 ? '物联设备' : '被监测设备' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="dictionaryDetailsName" label="系统类型" show-overflow-tooltip> </el-table-column>
        <el-table-column prop="assetsName" label="资产名称" show-overflow-tooltip> </el-table-column>
        <el-table-column prop="factoryCode" label="S/N出场编码" show-overflow-tooltip> </el-table-column>
        <el-table-column prop="commonName" label="通用名" show-overflow-tooltip> </el-table-column>
      </el-table>
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        :current-page="pagination.currentPage"
        :page-size="pagination.pageSize"
        :page-sizes="[15, 30, 50, 100]"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
      </el-pagination>
    </div>
  </div>
</template>
<script>
import axios from 'axios'
export default {
  data() {
    return {
      searchText: '',
      typeDataList: [],
      defaultProps: {
        children: 'children',
        label: 'dictionaryDetailsName',
        value: 'dictionaryDetailsId'
      },
      tableData: [],
      pagination: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      activeObj: {},
      tagsData: [],
      selectedList: [],
      treeLoading: false,
      tableLoading: false
    }
  },
  created() {
    this.getTypeData()
  },
  methods: {
    getTypeData() {
      // const url = 'http://***************:9800/dictionary/queryCategoryByCategoryId'
      const url = __PATH.VUE_MONITOR_API + 'dictionary/queryCategoryByCategoryId'
      const params = {
        level: '1',
        dictionaryCategoryId: 'PRODUCT_CATEGORY',
        token: this.$store.state.user.token
      }
      this.treeLoading = true
      axios({
        method: 'get',
        url,
        params,
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token,
          token: this.$store.state.user.token
        }
      })
        .then((res) => {
          const { code, data, message } = res.data
          if (code == 200) {
            this.typeDataList = data
            this.activeObj = data[0]
            this.$nextTick(() => {
              this.$refs.tree.setCurrentKey(this.activeObj.dictionaryDetailsId)
            })
            this.getTableList(this.activeObj)
          } else {
            this.$message.error(message || '请求失败')
          }
        })
        .catch((err) => {
          this.$message.error(err || '请求失败')
        })
        .finally(() => {
          this.treeLoading = false
        })
    },
    getTableList(val) {
      const params = {
        alarmStatus: '',
        assetsNameCode: this.searchText,
        dictionaryDetailsCode: val.dictionaryDetailsCode,
        equipAttr: '2',
        groupId: '',
        page: this.pagination.currentPage,
        pageSize: this.pagination.pageSize,
        sysOf1: ''
      }
      this.tableLoading = true
      this.$api.getEquipmentByMonitor(params).then((res) => {
        if (res.code == '200') {
          this.tableData = res.data.records
          this.pagination.total = res.data.total
          if (this.tableData.length) {
            this.tableKey = Math.random()
          }
        }
        this.tableLoading = false
      })
    },
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.pagination.currentPage = 1
      this.getTableList(this.activeObj)
    },
    handleCurrentChange(val) {
      this.pagination.currentPage = val
      this.getTableList(this.activeObj)
    },
    handleNodeClick(val) {
      this.activeObj = val
      this.getTableList(this.activeObj)
    },
    handleSelectionChange(val) {
      this.selectedList = val
      this.tagsData = val
    },
    deleteTag(val) {
      const target = this.tableData.find((i) => i.id == val)
      this.tagsData = this.tagsData.filter((i) => i.id != val)
      this.selectedList = this.selectedList.filter((i) => i.id != val)
      this.$refs.table.toggleRowSelection(target, false)
    },
    emitParams() {
      const transmitParams = {
        parent: {
          systemId: this.activeObj.dictionaryDetailsId,
          systemName: this.activeObj.dictionaryDetailsName,
          dictionaryDetailsCode: this.activeObj.dictionaryDetailsCode
        },
        listData: this.tagsData
      }
      this.$emit('selectedData', transmitParams)
    },
    search() {
      this.pagination.currentPage = 1
      this.pagination.pageSize = 15
      this.getTableList(this.activeObj)
    },
    reset() {
      this.searchText = ''
      this.pagination.currentPage = 1
      this.pagination.pageSize = 15
      this.getTableList(this.activeObj)
    }
  }
}
</script>
<style lang="scss" scoped>
.inner {
  display: flex;
  padding: 10px !important;
  height: 100%;
  .left {
    width: 25%;
    height: 100%;
    overflow-y: auto;
    margin-right: 10px;
    .typeTitle {
      width: 100%;
      text-align: center;
      font-size: 16px;
      color: #333;
      font-weight: bold;
    }
    .itemGroup {
      height: calc(100% - 44px);
      overflow-y: auto;
    }
  }
  .right {
    width: calc(75% - 10px);
    .filterWrap {
      height: 40px;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      .searchBtn {
        display: flex;
        align-items: flex-start;
        justify-content: flex-end;
      }
    }
    .selected {
      height: 35px;
      margin-bottom: 4px;
      display: flex;
      align-items: center;
      > span {
        display: inline-block;
        margin-right: 10px;
        width: 48px;
      }
      .tagsWrap {
        width: calc(100% - 58px);
        height: 100%;
        display: flex;
        align-items: center;
        overflow-x: auto;
        overflow-y: hidden;
        > span {
          margin-right: 10px;
        }
      }
      .tagsWrap::-webkit-scrollbar {
        height: 10px;
      }
    }
  }
}
::v-deep .el-tag {
  color: #666;
  height: 25px;
  line-height: 25px;
}
::v-deep .el-input__suffix {
  display: flex;
  align-items: center;
}
</style>
