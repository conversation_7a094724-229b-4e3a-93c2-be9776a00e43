<template>
  <div class="allDoc-content">
    <div class="allDoc-content-left">
      <div class="left_title">目录</div>
      <el-input v-model="filterText" placeholder="搜索" suffix-icon="el-icon-search"></el-input>
      <div class="left_content">
        <el-tree ref="tree" :data="treeData" node-key="id" size="small" :highlight-current="true" :props="defaultProps"
          :filter-node-method="filterNode" :expand-on-click-node="false" check-on-click-node draggable
          default-expand-all @node-click="nodeClick">
          <span slot-scope="{ node, data }" class="custom-tree-node">
            <span class="tree-label show-label">
              <span v-showtipPlus="`${data.label}（${data.count}）`"></span>
            </span>
            <span class="tree-btn" :style="{ width: node.level === 1 && '20px' }">
              <span @click="handleAddFolder(data)">
                <i class="icon el-icon-plus" style="margin-bottom: 12px"></i>
              </span>
              <span v-show="node.level !== 1" @click="handleEdit(data)">
                <i class="icon el-icon-edit"></i>
              </span>
              <span v-show="node.level !== 1" @click="handleDelete(data)">
                <i class="icon el-icon-delete"></i>
              </span>
            </span>
          </span>
        </el-tree>
      </div>
    </div>
    <div class="allDoc-content-right">
      <div class="rightTitle">全部</div>
      <div style="height: 100%; display: flex; flex-direction: column">
        <div class="search-from">
          <el-input v-model.trim="searchForm.archiveInfo" placeholder="搜索文件名称、摘要" suffix-icon="el-icon-search"
            style="width: 200px" clearable></el-input>
          <el-cascader v-model="searchForm.archiveOwnerDeptId" placeholder="所属部门" :options="deptList" :props="{
            value: 'id',
            label: 'deptName',
            checkStrictly: true,
            emitPath: false
          }" clearable filterable size="small">
          </el-cascader>
          <VirtualListSelect v-model="searchForm.archiveOwnerId" placeholder="所有者" clearable :options="ownerList"
            :propsOptions="{
            label: 'staffName',
            value: 'id'
          }" />
          <el-button type="primary" plain style="margin-right: 10px" @click="resetForm">重置</el-button>
          <el-button type="primary" style="margin-right: 10px" @click="search">查询</el-button>
          <!-- <el-dropdown>
            <el-button type="primary" @click="searchForm">查询<i
                class="el-icon-arrow-down el-icon--right"></i></el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item>查询当前文件夹</el-dropdown-item>
              <el-dropdown-item>查询全部文件夹</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown> -->
        </div>
        <div class="search-control">
          <el-button type="primary" icon="el-icon-plus" @click="tableControl('add')">创建</el-button>
          <!-- <el-button type="primary" @click="tableControl('add')">批量创建</el-button> -->
          <el-button type="primary" :disabled="!multipleSelection.length" @click="
            tableControl('export')">下载</el-button>
          <el-button type="danger" :disabled="!multipleSelection.length"
            @click="tableControl('batchDel')">批量删除</el-button>
        </div>
        <div class="contentTable-main table-content">
          <el-table v-loading="tableLoading" border style="width: 100%; height: 100%" :data="tableData"
            :height="tableHeight" stripe @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="60" align="center"></el-table-column>
            <el-table-column label="文件名称" prop="archiveName" show-overflow-tooltip></el-table-column>
            <el-table-column label="文件夹" prop="folderName" show-overflow-tooltip> </el-table-column>
            <el-table-column label="所有者部门" prop="archiveOwnerDeptName" show-overflow-tooltip> </el-table-column>
            <el-table-column label="摘要信息" prop="remark" show-overflow-tooltip> </el-table-column>
            <el-table-column label="操作" width="180" fixed="right">
              <template slot-scope="scope">
                <el-button type="text" @click="tableControl('viewFile', scope.row)">查看附件</el-button>
                <el-button type="text" @click="tableControl('view', scope.row)">详情</el-button>
                <el-dropdown @command="(val) => tableControl(val, scope.row)">
                  <span style="color: #3562db; margin-left: 10px; cursor: pointer"> 更多 <i
                      class="el-icon-arrow-down"></i>
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="edit">编辑</el-dropdown-item>
                    <el-dropdown-item command="del">删除</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="contentTable-footer">
          <el-pagination :current-page="pagination.current" :page-sizes="pagination.pageSizeOptions"
            :page-size="pagination.size" :layout="pagination.layoutOptions" :total="pagination.total"
            @size-change="handleSizeChange" @current-change="handleCurrentChange">
          </el-pagination>
        </div>
      </div>
      <CreateFolder v-if="visible" :visible.sync="visible" :currentNode="currentNode" :type="editType" :folderType="'4'"
        :constructionType="2" @success="handleGetTreeData" />
      <ViewAttachments :id="current.archiveId" :visible.sync="viewVisible" />
    </div>
  </div>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin.js'
import CreateFolder from '@/views/operationPort/dossierManager/components/CreateFolder.vue'
import ViewAttachments from '@/views/operationPort/dossierManager/components/ViewAttachments.vue'
import { transData, auth } from '@/util'
import dictMixin from '@/views/operationPort/contractManagement/mixins/index'
import moment from 'moment'
export default {
  name: 'allDoc',
  components: { CreateFolder, ViewAttachments },
  mixins: [tableListMixin, dictMixin],
  data() {
    return {
      moment,
      visible: false,//文档新建
      viewVisible: false,//查看附件
      currentNode: null,
      filterText: '', // 树形结构筛选
      treeLoading: false,
      current: {},
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'label',
        value: 'id'
      },
      searchForm: {
        archiveInfo: '',
        archiveOwnerName: '',
        archiveOwnerDeptName: '',
      },
      tableLoading: false,
      tableData: [],
      multipleSelection: [], // 多选
      checkedTreeNode: {},
      visible: false,
      ownerList: [],//人员
      deptList: [],//部门
    }
  },
  props: {
    fromId: {
      type: String,
      default: ''
    },
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  mounted() {
    this.handleGetTreeData()
    this.getLersonnelList()
    this.getDeptList()
  },
  methods: {
    // 获取部门列表
    getDeptList() {
      this.$api.getSelectedDept().then((res) => {
        if (res.code == '200') {
          this.deptList = transData(res.data, 'id', 'pid', 'children')
        }
      })
    },
    // 获取人员列表
    getLersonnelList() {
      let params = {
        current: 1,
        size: 99999
      }
      this.$api.staffList(params).then((res) => {
        if (res.code == 200) {
          this.ownerList = res.data.records
        }
      })
    },
    //获取tree树
    handleGetTreeData() {
      this.treeLoading = true
      this.$api.fileManagement.selectFolderTree({ folderType: '4', isMine: false }).then((res) => {
        this.treeLoading = false
        if (res.code === '200') {
          this.treeData = res.data
          if (this.fromId) {
            this.checkedTreeNode.id = this.fromId
            this.$nextTick(() => {
              this.$refs.tree.setCurrentKey(this.fromId)
            })
          } else {
            this.checkedTreeNode = res.data[0]
            this.$nextTick(() => {
              this.$refs.tree.setCurrentKey(res.data[0].id)
            })
          }
          this.getTableList()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    //tree树操作
    handleDelete(data) {
      this.$confirm('是否删除所选文件夹?', '删除文件夹', {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$api.fileManagement.deleteFolder({ id: data.id }).then((res) => {
            if (res.code === '200') {
              this.$message.success('删除成功')
              this.handleGetTreeData()
            } else {
              this.$message.error(res.msg)
            }
          })
        })
        .catch(() => { })
    },
    handleAddFolder(data) {
      this.visible = true
      this.editType = '0'
      this.currentNode = data
    },
    handleEdit(data) {
      this.visible = true
      this.editType = '1'
      this.currentNode = data
    },
    tableControl(key, data) {
      if (key == 'add') {
        // 新增
        this.$router.push({
          name: "constructionFileDocAdd",
          query: {
            type: 'add',
            fromId: this.checkedTreeNode ? this.checkedTreeNode.id : ''
          }
        })
      } else if (key == 'view') {
        // 详情
        this.$router.push({
          name: "constructionFileDocView",
          query: {
            id: data.archiveId,
            fromId: this.checkedTreeNode ? this.checkedTreeNode.id : ''
          }
        })
      } else if (key == 'edit') {
        // 编辑
        this.$router.push({
          name: "constructionFileDocAdd",
          query: {
            type: 'edit',
            id: data.archiveId,
            fromId: this.checkedTreeNode ? this.checkedTreeNode.id : ''
          }
        })
      } else if (key == 'del') {
        // 删除
        this.$confirm('确认删除所选的数据吗?', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let idList = []
          idList.push(data.archiveId)
          this.$api.fileManagement
            .updateDeleteArchiveB({ idList }).then((res) => {
              if (res.code == 200) {
                this.$message.success('删除成功')
                this.getTableList()
                this.handleGetTreeData()
                this.$emit('delete', this.data)
              } else {
                this.$message.error('删除失败')
              }
            })
        })
      } else if (key == 'batchAdd') {
        // 批量添加
      } else if (key == 'batchDel') {
        // 批量删除
        const rows = this.multipleSelection
        if (rows.length < 1) {
          this.$message.warning('请至少选择一条数据！')
          return
        }
        const message = rows.map((item) => item.archiveName).join('、')
        this.$confirm('确认删除“' + message + '”等数据吗？', '批量删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.tableLoading = true
            const idList = rows.map((v) => v.archiveId)
            this.$api.fileManagement
              .updateDeleteArchiveB({ idList })
              .then((res) => {
                if (res.code === '200') {
                  this.$message.success('删除成功')
                  this.getTableList()
                  this.handleGetTreeData()
                  this.$emit('delete', this.data)
                } else {
                  this.$message.error('删除失败')
                }
              })
              .finally(() => {
                this.tableLoading = false
              })
          })
          .catch(() => { })
      } else if (key == 'export') {
        //下载
        const rows = this.multipleSelection
        const ids = rows.map((item) => item.archiveId).join(',')
        this.$api.fileManagement.downloadArchiveFile({ ids, isArchive: true }).then((res) => {
          this.downloadFile(res)
        })
      } else if (key == 'viewFile') {
        // 查看附件
        this.current = data
        this.viewVisible = true
      }
    },
    //文件下载
    downloadFile(res) {
      const downloadElement = document.createElement('a')
      const href = window.URL.createObjectURL(res.data) // 创建下载的链接
      downloadElement.style.display = 'none'
      downloadElement.href = href
      downloadElement.download = 'files.zip' // 下载后文件名
      document.body.appendChild(downloadElement)
      downloadElement.click() // 点击下载
      document.body.removeChild(downloadElement) // 下载完成移除元素
      window.URL.revokeObjectURL(href) // 释放掉blob对象
    },
    getTableList() {
      const folderId = this.checkedTreeNode.id
      const params = {
        archiveType: '4',
        folderId,
        isMine: false,
        ...this.searchForm,
        size: this.pagination.size,
        current: this.pagination.current,
      }
      this.tableLoading = true
      delete params.datetimerange
      this.$api.fileManagement.queryByPage(params).then((res) => {
        this.tableLoading = false
        if (res.code === '200') {
          this.tableData = res.data.records
          this.pagination.total = res.data.total
        } else {
          this.tableData = []
          this.pagination.total = 0
        }
      })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    search() {
      this.pagination.current = 1
      this.getTableList()
    },
    // 分页事件
    paginationChange(pagination) {
      Object.assign(this.pagination, pagination)
      this.getTableList()
    },
    // 重置
    resetForm() {
      this.pagination.size = 15
      this.pagination.current = 1
      Object.keys(this.searchForm).forEach((key) => {
        this.searchForm[key] = ''
      })
      this.search()
    },
    filterNode(value, data) {
      if (!value) return true
      return data[this.defaultProps.label].indexOf(value) !== -1
    },
    nodeClick(data) {
      this.checkedTreeNode = data
      this.getTableList()
    },
    handleSizeChange(val) {
      this.pagination.size = val
      this.getTableList()
    },
    handleCurrentChange(val) {
      this.pagination.current = val
      this.getTableList()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
  }
}
</script>
<style lang="scss" scoped>
.allDoc-content {
  display: flex;
  width: 100%;
  height: 100%;
  height: calc(100% - 70px);
  background-color: #fff;
  .allDoc-content-left {
    width: 300px;
    height: 100%;
    border-right: solid 1px #eee;
    display: flex;
    flex-flow: column nowrap;
    padding: 0 16px 0 0;
    &::v-deep(.el-tree) {
      height: calc(100% - 64px);
      overflow: auto;
      .el-tree-node__content {
        line-height: 36px;
        height: 36px;
      }
      .el-tree-node.is-current > .el-tree-node__content {
        color: #3562db;
        background: #e6effc;
      }
    }
    .left_content {
      width: 100%;
      margin-top: 10px;
      height: calc(100% - 45px);
      overflow: auto;
      .custom-tree-node {
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;
        width: 90%;
        height: 100%;
        line-height: 32px;
        &:hover > .tree-btn {
          display: flex;
          align-items: center;
        }
        &:hover > .show-label {
          width: calc(100% - 70px) !important;
        }
        .tree-label {
          display: flex;
          width: 100%;
          .iconfont {
            margin-right: 8px;
            font-size: 12px;
          }
          span {
            display: inline-block;
            width: 100%;
            font-size: 14px;
          }
        }
        .tree-btn {
          display: none;
          width: 70px;
          text-align: right;
          .icon {
            font-size: 14px;
          }
          span:first-child {
            font-size: 20px;
          }
          span {
            margin-right: 10px;
            &:hover {
              color: #1474a4;
            }
          }
          span:last-child {
            margin-right: 0;
          }
        }
      }
    }
    .left_title {
      font-weight: bold;
      font-size: 16px;
      color: #333333;
      padding: 12px 0;
    }
  }
  .allDoc-content-right {
    height: 100%;
    flex: 1;
    padding: 10px 20px 20px 20px;
    border-radius: 0px 4px 4px 0px;
    flex: 1;
    .rightTitle {
      font-weight: bold;
      font-size: 16px;
    }
    .search-from {
      & > div,
      .el-button {
        margin-right: 10px;
        margin-top: 6px;
      }
      & > .el-button:last-child {
        margin: 0px;
      }
    }
    .search-control {
      margin-bottom: 10px;
      & > .el-button {
        margin-top: 10px;
        margin-right: 10px;
        margin-left: 0px;
      }
      & > .el-button:last-child {
        margin: 0px;
      }
    }
    .contentTable-main {
      flex: 1;
      height: calc(100% - 200px);
      overflow: hidden;
    }
    .contentTable-footer {
      padding: 10px 0 0;
    }
  }
}
</style>
