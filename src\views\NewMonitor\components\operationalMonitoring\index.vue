<template>
  <PageContainer class="securityOperationMonitor">
    <!-- header -->
    <div slot="header" class="securityOperationMonitor-header">
      <!-- tabs 设备监测/物联监测-->
      <el-tabs v-show="currentPattern == 2" v-model="tabActive" @tab-click="tabClick">
        <el-tab-pane v-for="(item, index) in tabs" :key="index" :label="item.projectName" :name="item.equipAttr" />
      </el-tabs>
      <!-- Display mode 组态/列表 -->
      <div class="heade-pattern">
        <div class="pattern-item" @click="switchPattern(1)">
          <svg-icon :name="currentPattern == 1 ? 'chartModeActive' : 'chartMode'" class="pattern-icon" />
          <span :style="{ color: currentPattern == 1 ? '#3562DB' : '#414653' }">图形模式</span>
        </div>
        <div class="pattern-item" @click="switchPattern(2)">
          <svg-icon :name="currentPattern == 2 ? 'listModeActive' : 'listMode'" class="pattern-icon" />
          <span :style="{ color: currentPattern == 2 ? '#3562DB' : '#414653' }">列表模式</span>
        </div>
      </div>
    </div>
    <!-- content -->
    <div slot="content" style="height: 100%">
      <ElectricitySafety v-if="tabActive === 2" :equipAttr="tabActive" @jumpDetail="jumpMonitorDetail" />
      <!-- ListMode -->
      <div v-else-if="currentPattern == 2" class="securityOperationMonitor-content">
        <div class="content-main">
          <div class="search-from">
            <el-input v-model="searchFrom.assetsNameCode" :placeholder="'请输入设备名称编码'" clearable style="width: 200px"
              @blur="event => searchFrom.assetsNameCode = event.target.value.replace(/\s+/g, '')"></el-input>
            <el-select v-model="searchFrom.sysOf1" :placeholder="'请选择设备类型'" clearable style="width: 200px">
              <el-option v-for="(item, index) in deviceTypeList" :key="index" :label="item.dictionaryDetailsName"
                :value="item.dictionaryDetailsId"></el-option>
            </el-select>
            <el-cascader ref="refHandle" :options="customGroupingList"
              :props="{ checkStrictly: true, expandTrigger: 'hover' }" clearable placeholder="自定义分组"
              :show-all-levels="false" v-model="handlerValue" @change="handleChange"></el-cascader>
            <el-select v-model="alarmStatusList" :placeholder="'请选择设备状态'" clearable style="width: 200px"
              @change="equipmentStatusSelect">
              <el-option v-for="(item, index) in deviceStateList" :key="index" :label="item.name"
                :value="item.value"></el-option>
            </el-select>
            <el-select ref="treeSelect" v-model="searchFrom.spaceLocation" clearable :placeholder="'请选择所属区域'"
              @clear="handleClear">
              <el-option hidden :value="searchFrom.spaceLocation" :label="areaName"> </el-option>
              <el-tree :data="serverSpaces" :props="serverDefaultProps" :load="serverLoadNode" lazy
                :expand-on-click-node="false" :check-on-click-node="true" @node-click="handleNodeClick">
              </el-tree>
            </el-select>
            <el-select v-model="searchFrom.iotMessage" placeholder="物联信息" clearable style="width: 200px"
              v-if="!fadeShow">
              <el-option v-for="(item, index) in informationList" :key="index" :label="item.name"
                :value="item.value"></el-option>
            </el-select>
            <el-select v-model="searchFrom.assetStatus" placeholder="资产状态" clearable style="width: 200px"
              v-if="!fadeShow">
              <el-option v-for="(item, index) in assetStatusList" :key="index" :label="item.dictName
                " :value="item.dictValue"></el-option>
            </el-select>
            <el-select v-model="searchFrom.assetsStatus" placeholder="使用状态" clearable style="width: 200px"
              v-if="!fadeShow">
              <el-option v-for="(item, index) in useOfStatusList" :key="index" :label="item.name"
                :value="item.value"></el-option>
            </el-select>
            <i v-if="fadeShow" title="展开" class="el-icon-bottom fadeBtn mr-6" @click="fadeChange">展开</i>
            <i v-else title="关闭" class="el-icon-top fadeBtn mr-6" @click="fadeChange">关闭</i>
            <div style="display: inline-block">
              <el-button type="primary" plain @click="resetForm">重置</el-button>
              <el-button type="primary" @click="searchForm">查询</el-button>
            </div>
            <div class="batch-control">
              <el-button type="primary" @click="handleCommand('add', '')">新建设备</el-button>
              <el-button type="primary" @click="clusterSet()">配置分组</el-button>
            </div>
          </div>
          <div v-loading="listLoading" class="main-content">
            <el-row :gutter="16" style="margin-bottom: 20px">
              <el-col v-for="(item, index) in listData" :key="index" :xs="12" :sm="12" :md="8" :xl="6"
                v-if="listData.length">
                <div class="monitor-item">
                  <div class="item-heade">
                    <span><span v-if="item.onlineStatus === 1" class="lineStatus onlineStatus "><img
                          src="../../../../assets/images/newMonitor/online.png" alt=""> 在线</span><span
                        v-if="item.onlineStatus === 0" class="lineStatus offlineStatus"><img
                          src="../../../../assets/images/newMonitor/offline.png" alt=""> 离线</span>
                      <span @click="jumpMonitorDetail(item)" style="cursor: pointer;">{{
                        item.assetsName }}</span>
                      <el-badge :value="item.alarmUnHandlerCount" class="bjitem3" style="margin-top: 9px;" :max="99"
                        v-if="item.alarmUnHandlerCount > 0 && item.alarmCardColor === 3" />
                      <el-badge :value="item.alarmUnHandlerCount" class="bjitem2" style="margin-top: 9px;" :max="99"
                        v-if="item.alarmUnHandlerCount > 0 && item.alarmCardColor === 2" />
                      <el-badge :value="item.alarmUnHandlerCount" class="bjitem1" style="margin-top: 9px;" :max="99"
                        v-if="item.alarmUnHandlerCount > 0 && item.alarmCardColor === 1" />
                      <el-badge :value="item.alarmUnHandlerCount" class="bjitem0" style="margin-top: 9px;" :max="99"
                        v-if="item.alarmUnHandlerCount > 0 && item.alarmCardColor === 0" />
                    </span>
                    <el-dropdown trigger="hover" @command="(val) => handleCommand(val, item)">
                      <p class="clickBtn moreBtn" @click.stop.prevent><img
                          src="../../../../assets/images/newMonitor/configuration.png" alt=""></p>
                      <el-dropdown-menu slot="dropdown" class="dropdownSelect">
                        <el-dropdown-item command="edit">编辑</el-dropdown-item>
                        <el-dropdown-item command="delete">删除</el-dropdown-item>
                        <el-dropdown-item command="configuration">配置</el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                  </div>
                  <div class="item-main">
                    <img class="main-img" :src="$tools.imgUrlTranslation(item.dictionaryDetailsPicUrl)"
                      :alt="item.assetsName" v-if="item.dictionaryDetailsPicUrl !== ''" />
                    <img class="main-img" v-if="item.dictionaryDetailsPicUrl == ''"
                      src="../../../../assets/images/newMonitor/universalIcon.png" alt="">
                    <div class="main-content">
                      <div class="main-item" v-for="(obj, i) in item.iotPropertyList" :key="i">
                        <div v-if="obj.metadataType === 'properties'">
                          <p class="item-title">{{ obj.metadataName }}</p>
                          <p class="item-value" :title="obj.valueText">{{ obj.valueText }}</p>
                        </div>
                        <div v-if="obj.metadataType === 'functions' && systemCode !== 'AFXT'">
                          <p class="item-title">{{ obj.metadataName }}</p>
                          <p @click="oncontrolList(item, obj)" class="item-buttom"><img
                              src="../../../../assets/images/newMonitor/execute.png" alt=""> 执行</p>
                        </div>
                        <div v-if="obj.metadataType === 'functions' && systemCode === 'AFXT'">
                          <p class="item-title">{{ obj.metadataName }}</p>
                          <p @click="openVideo(obj)" class="item-buttom"><img
                              src="../../../../assets/images/newMonitor/execute.png" alt=""> 执行</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </el-col>
              <div v-if="listData.length == 0"
                style="width: 100%; height: 100%; display: flex; flex-direction: column;  align-items: center; justify-content: center; padding: 50px;margin-top:80px;">
                <img src="@/assets/images/newMonitor/no-chat.png" />
                <span style="color: #909399;">暂无数据</span>
              </div>
            </el-row>
          </div>
          <div class="main-footer">
            <el-pagination :current-page="pagination.current" :page-sizes="[16, 32, 64, 128]"
              :page-size="pagination.size" layout="total, sizes, prev, pager, next, jumper" :total="pagination.total"
              @size-change="paginationSizeChange" @current-change="paginationCurrentChange" />
          </div>
        </div>
      </div>
      <!-- ScadaMode -->
      <div v-else class="securityOperationMonitor-mode-content">
        <div class="monitor-content-left">
          <div v-for="(item, index) in scadaList" :key="index">
            <p @click="handleTeamClick(item.id, index)" :class="{ 'active': currentSelectedIndex === index }">{{
              item.name
            }}</p>
          </div>
        </div>
        <div class="monitor-content-right">
          <graphics-mode ref="scadaShow" :checkedTeamData="checkedTeamData" />
        </div>
      </div>
      <!-- 配置分组弹窗 -->
      <el-drawer title="自定义分组" :visible.sync="isDrawer" :before-close="handleClose" size="35%">
        <div style="padding:0 20px 20px">
          <el-button type="primary" plain @click="addFun()">新增</el-button>
        </div>
        <div style="padding:0 20px">

          <div class="filter">
            <el-input v-model="filterText" suffix-icon="el-icon-search" placeholder="请输入" clearable></el-input>
          </div>
          <el-tree ref="treeType" key="id" node-key="id" size="small" :data="treeData" :props="treeDataDefaultProps"
            :highlight-current="true" :load="serverLoadNode" draggable :filter-node-method="filterNode"
            default-expand-all :expand-on-click-node="false" :check-on-click-node="true" @node-click="nodeClick"
            @node-drop="nodeDrop">
            <div slot-scope="{ node, data }" class="custom-tree-node">
              <span>
                {{ node.label }}
              </span>
              <span class="operate-btns">
                <dot-dropdown :events="directoryList" :data="{ node, data }" @addNode="opertaionDirNode" />
              </span>
            </div>
          </el-tree>
        </div>
      </el-drawer>
      <!--配置分组新增编辑弹窗 -->
      <el-dialog v-if="isVisible" v-dialogDrag :modal="false" custom-class="model-dialog" width="30%" append-to-body
        :visible.sync="isVisible" :close-on-click-modal="false" :before-close="closeDialog">
        <template slot="title">
          <span class="dialog-title">{{ visibleType === 'add' ? (formList.ledgerType === 1 ? '新增父级' : '新增自定义分组') :
            formList.ledgerType === 1 ? '编辑父级' : '编辑自定义分组' }}</span>
        </template>
        <el-form ref="formList" class="form-data" style="width: 100%" :model="formList">
          <el-form-item label="分组名称" prop="groupName" :rules="{ required: true, message: '请输入分组名称', trigger: 'blur' }">
            <el-input v-model="formList.groupName" placeholder="请输入分组名称" maxlength="10" show-word-limit />
          </el-form-item>
          <el-form-item label="上级分组">
            <el-cascader ref="refHandle1" :options="customGroupingList"
              :props="{ checkStrictly: true, expandTrigger: 'hover' }" clearable placeholder="请选择上级分组"
              :show-all-levels="false" v-model="handlerValue1" @change="handleChange1"
              style="width: 100%;"></el-cascader>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="submit">确 定</el-button>
        </span>
      </el-dialog>
      <!--配置分组-选择设备弹窗 -->
      <template v-if="isEquipment">
        <choiceDevice :dialogShow="isEquipment" :dialogData="checkedTreeNode" @submitDialog="submitEquipmentDialog"
          @closeDialog="closeEquipmentDialog" />
      </template>
      <template v-if="DialogShow">
        <popUpEvent :DialogShow="DialogShow" :booleDialogShow="booleDialogShow" :enumDialogShow="enumDialogShow"
          :metadataTag="metadataTag" :dataType="dataType" :title="title" @submitDialog="submitParamsGather"
          @closeDialog="closeHarvesterDialog" />
      </template>
      <!-- 云平台视频 -->
      <template v-if="videoDialgoShow">
        <videoDialgo :videoDialgoShow="videoDialgoShow" :title="title1" :factoryCode="factoryCode"
          @submitDialog="submitParamsGather" @closeVideoDialog="closeVideoDialog" />
      </template>
    </div>
  </PageContainer>
</template>
<script>
import { mapGetters } from 'vuex'
import tableListMixin from '@/mixins/tableListMixin.js'
import { monitorItemImg } from '@/util/dict.js'
import { newMonitorItemImg, newMonitorTypeList, equipmentStatus, materially, useOfState } from '@/util/newDict.js'
import { transData, ListTree } from '@/util'
import graphicsMode from '../../components/graphicsMode.vue'
import ElectricitySafety from './electricitySafety.vue'
import DotDropdown from './DotDropdown.vue'
import choiceDevice from './choiceDevice.vue'
import popUpEvent from '../../airConditioningTerminal/components/popUpEvent.vue'
import videoDialgo from '../../airConditioningTerminal/components/videoDialgo.vue'
export default {
  name: 'airOperationalMonitoring',
  components: {
    graphicsMode,
    ElectricitySafety,
    DotDropdown,
    choiceDevice,
    popUpEvent,
    videoDialgo
  },
  mixins: [tableListMixin],
  props: {
    tabs: {
      type: Array,
      default: () => []
    },
    systemName: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      fadeShow: true, // 淡入淡出
      currentSelectedIndex: 0,
      DialogShow: false,//弹窗
      videoDialgoShow: false,//弹窗
      title1: "云平台视频监控",//视频弹窗title
      factoryCode: '',
      metadataTag: "",
      booleDialogShow: false,//布尔值弹窗
      enumDialogShow: false,//枚举值弹窗
      dataType: [],//布尔值弹窗数据
      title: "",//布尔值弹窗title
      systemCode: this.$route.meta.systemType,
      deviceTypeList: [],//设备类型
      customGroupingList: [],//自定义分组
      customDefaultProps: {
        label: 'groupName',
        isLeaf: 'leaf',
        children: 'children'
      },
      deviceStateList: equipmentStatus,//设备状态
      informationList: materially,//物联信息
      assetStatusList: [],//资产状态
      useOfStatusList: useOfState,//使用状态
      filterText: '',//配置分组搜索
      treeId: '', // 当前选中节点
      defaultProps: {
        label: 'ssmName',
        isLeaf: 'leaf',
        children: 'children'
      },
      dragNodeObj: {},
      directoryList: [
        { label: '新增', funcName: 'addDir' },
        { label: '编辑', funcName: 'edit' },
        { label: '删除', funcName: 'delete' }
      ],
      newMonitorItemImg,
      alarmUnHandlerCount: 0,
      isDrawer: false,
      monitorItemImg,
      currentPattern: 2, // 当前模式
      tabActive: this.tabs[0].equipAttr,
      listLoading: false,
      listData: [],
      searchFrom: {
        assetsNameCode: '', // 设备编码
        sysOf1: this.$route.query.sysOf1 || '', // 设备类型
        groupId: "",//自定义分组
        onlineStatus: this.$route.query.skipSign === '1' || this.$route.query.skipSign === '0' ? this.$route.query.skipSign : null,//在线离线（number）
        alarmStatus: this.$route.query.skipSign === '2' ? '1' : null,//报警状态
        spaceLocation: "",//所属区域
        iotMessage: '',//物联信息 0无1有
        assetStatus: '',//资产状态
        assetsStatus: '',//使用状态
      },
      groupName: '', // 自定义分组名称
      serverSpaces: [], // 空间位置
      spaces: [], // 空间位置
      serverDefaultProps: {
        label: 'ssmName',
        isLeaf: 'leaf',
        children: 'children'
      },
      areaName: '', // 选中 下拉树的name
      treeData: [],//自定义分组树
      treeDataDefaultProps: {
        children: 'children',
        label: 'groupName',
        value: 'id'
      },
      selectGraphicsCode: '', // 选中图纸code
      treeLoading: false,
      defaultProps: {
        children: 'children',
        label: 'name',
        value: 'id'
      },
      formList: {
        groupName: '',
        pid: '',
      },
      visibleType: 'add',
      isVisible: false,
      isEquipment: false,
      teamTreeData: [], // 树状数据
      isShowTooltip: false,
      checkedTeamData: '',
      selectItem: {},
      checkedTreeNode: {},
      groupIDName: "",
      dataId: '',
      addFunType: 'addFun',
      scadaList: [],
      handlerValue: [],
      handlerValue1: [],
      alarmStatusList: this.$route.query.skipSign || '',
      pagination: {
        size: 16,
        current: 1,
        total: 0
      },
    }
  },
  computed: {},
  watch: {
    handlerValue() {
      if (this.$refs.refHandle) {
        this.$refs.refHandle.dropDownVisible = false; //监听值发生变化就关闭它
      }
    },
    handlerValue1() {
      if (this.$refs.refHandle1) {
        this.$refs.refHandle1.dropDownVisible = false;
      }
    },
    filterText(val) {
      this.$refs.treeType.filter(val)
    },
    tabActive: {
      handler: function (val, oldVal) {
        this.pagination.current = 1
        Object.assign(this.searchFrom, {
          assetsNameCode: '', // 设备编码
          sysOf1: this.$route.query.sysOf1 || '', // 设备类型
          groupId: "",//自定义分组
          onlineStatus: this.$route.query.skipSign === '1' || this.$route.query.skipSign === '0' ? this.$route.query.skipSign : null,//在线离线（number）
          alarmStatus: this.$route.query.skipSign === '2' ? '1' : null,//报警状态
          spaceLocation: "",//所属区域
          iotMessage: '',//物联信息 0无1有
          assetStatus: '',//资产状态
          assetsStatus: '',//使用状态
        })
        this.handleClear()
        this.getDataList()

      },
      immediate: true
    },
    // socketIemcMsgs(data) {
    //   let itemData = JSON.parse(data)
    //   let newList = JSON.parse(JSON.stringify(this.listData))
    //   newList.forEach((item) => {
    //     if (item.id == itemData.id) {
    //       Object.assign(item, itemData)
    //     }
    //   })
    //   this.listData = this.setData(newList)
    // },
    searchFrom: {
      handler(val, oldVal) {
        this.pagination.current = 1
      },
      deep: true
    }
  },
  created() {
    this.$nextTick(() => {
      this.getDeviceType()//设备类型
      this.getAssetStatus()//资产状态
      this.getAssetsGroup()//自定义分组树
      this.getTreelist()
      this.getScaleImgByProjectCode()
    })
  },
  activated() {
    this.getDataList()
  },
  methods: {
    handleChange(value) {
      this.searchFrom.groupId = value.length > 0 ? value[value.length - 1] : '';
    },
    handleChange1(value) {
      this.formList.pid = value.length > 0 ? value[value.length - 1] : '';
    },
    // 展开隐藏
    fadeChange() {
      this.fadeShow = !this.fadeShow
    },
    // 云平台视频
    openVideo(item) {
      this.factoryCode = item.factoryCode
      this.videoDialgoShow = true
    },
    closeVideoDialog() {
      this.videoDialgoShow = false
    },
    // 控制参数修改
    oncontrolList(item, obj, index) {
      this.dataId = item.id
      let data = JSON.parse(obj.valueType)
      this.dataType = data
      if (this.dataType) {
        this.DialogShow = true
        if (data.valueType.type === 'enum') {
          this.enumDialogShow = true
        } else if (data.valueType.type === 'boolean') {
          this.booleDialogShow = true
        }
        this.metadataTag = obj.metadataTag
        this.title = obj.metadataName
      }
    },
    // 设备类型
    getDeviceType() {
      this.deviceTypeList = []
      let data = {
        dictionaryCode: this.systemCode,
        equipAttr: this.tabActive,
      }
      this.$api.querySubCategoryDetailsRelatedAssets(data).then((res) => {
        if (res.code == '200') {
          this.deviceTypeList = res.data
        }
      })
    },
    // 查询资产字典
    getSelectByList(str, getArr) {

    },
    // 资产状态
    getAssetStatus() {
      this.assetStatusList = []
      this.$api.selectByListAsset({ dictTypeId: '24' }).then((res) => {
        if (res.code == '200') {
          this.assetStatusList = res.data
        }
      })
    },
    tabClick() {
      this.groupName = ''
      this.getDeviceType()
      this.getAssetsGroup()
    },
    // 自定义分组树
    getAssetsGroup() {
      this.customGroupingList = []
      let data = {
        dictionaryDetailsCode: this.systemCode,
        equipAttr: this.tabActive,
        groupName: "",
      }
      this.$api.getCustomGroupingTree(data).then((res) => {
        if (res.code == '200') {
          this.customGroupingList = transData(res.data, 'id', 'pid', 'children')
          this.treeData = transData(res.data, 'id', 'pid', 'children')
        }
      })
    },
    // 自定义分组树选择 数据
    customNodeClick(data) {
      this.searchFrom.groupId = data.id
      this.groupName = data.groupName
      this.$refs.groupIdSelect.blur()
    },
    // 设备状态
    equipmentStatusSelect(val) {
      if (val === '2') {
        this.searchFrom.onlineStatus = null
        this.searchFrom.alarmStatus = '1'
      } else {
        this.searchFrom.onlineStatus = val
        this.searchFrom.alarmStatus = null
      }
    },
    // 重置查询表单
    resetForm() {
      this.searchFrom = {
        assetsNameCode: '', // 设备编码
        sysOf1: '', // 设备类型
        groupId: "",//自定义分组
        onlineStatus: null,//在线离线（number）
        alarmStatus: null,//报警状态
        spaceLocation: "",//所属区域
        iotMessage: '',//物联信息 0无1有
        assetStatus: '',//资产状态
        assetsStatus: '',//使用状态
      }
      this.pagination.size = 16
      this.pagination.current = 1
      this.groupName = ''
      this.areaName = ''
      this.handlerValue = []
      this.alarmStatusList = ''
      this.searchForm()
    },
    // 查询
    searchForm() {
      this.pagination.current = 1
      this.getDataList()
    },
    // 设备新建/编辑 删除 配置
    handleCommand(command, item) {
      // edit(编辑) delete(删除) configuration(配置) 
      let path = ''
      path = `${this.$route.meta.jumpAddress}/addNewForm`
      if (command === 'add' || command === 'edit') {
        if (!path) return
        // 强制更新组件
        this.$forceUpdate();
        this.$router.push({
          path: path,
          query: {
            id: item.id,
            systemCode: this.systemCode,
          }
        })
      } else if (command === 'delete') {
        let params = {
          id: item.id
        }
        this.$confirm('删除后将无法恢复，是否确定删除？', '信息提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(() => {
          this.$api.getQueryDeleteById(params).then(res => {
            if (res.code === '200') {
              this.$message({
                message: '删除成功',
                type: 'success'
              })
              this.getDataList()
            } else {
              this.$message({
                type: 'error',
                message: res.message || '删除失败'
              })
            }
          })
        }).catch(() => { })
      } else if (command === 'configuration') {
        path = `${this.$route.meta.jumpAddress}/clusterSetting`
        if (!path) return
        this.$router.push({
          path: path,
          query: {
            id: item.id,
            factoryCode: item.factoryCode,
            assetsName: item.assetsName,
            tabActive: this.tabActive,
            systemCode: this.systemCode,
          }
        })
      }
    },
    // 配置分组
    clusterSet() {
      this.isDrawer = true
    },
    // 没有数据时 分组新增
    addFun() {
      this.visibleType = 'add'
      this.addFunType = 'addFun'
      this.isVisible = true
    },
    // 配置分组关闭
    handleClose() {
      this.isDrawer = false
    },
    // 配置分组树结构开始---------------------------
    // 自定义分组树选择 弹窗数据
    customNodeClick1(data) {
      this.formList.pid = data.id
      this.groupIDName = data.groupName
      this.$refs.selectGroup.blur()
    },
    // 操作目录
    opertaionDirNode(fnName, data) {
      this.visibleType = 'add'
      this.addFunType = ''
      if (fnName === 'addDir') {
        this.formList.pid = data.data.id
        this.groupIDName = data.data.groupName
        this.handlerValue1 = data.data.pid
        this.isVisible = true
      } else if (fnName === 'edit') {
        this.$api.getCustomGroupingQueryById(data.data.id).then(res => {
          if (res.code === '200') {
            if (data.data.pid === '-1') {
              this.formList.id = res.data.id
              this.formList.pid = res.data.pid
              this.handlerValue1 = ''
            } else {
              this.formList.id = res.data.id
              this.formList.pid = res.data.pid
              this.handlerValue1 = res.data.pid
            }
            this.groupIDName = res.data.parentGroupName
            this.formList.groupName = res.data.groupName
          }
        })
        this.isVisible = true
      } else {
        let params = {
          id: data.data.id
        }
        this.$api.getCustomGroupingDeleteById(params).then(res => {
          if (res.code === '200') {
            this.$message({
              message: '删除成功',
              type: 'success'
            })
            this.closeDialog()
            this.getAssetsGroup()
            this.getTreeList()
          }
        })
      }
    },
    submit() {
      this.$refs.formList.validate((valid) => {
        if (valid) {
          if (this.addFunType === 'addFun') {
            this.formList.pid = '-1'
          }
          let data = {
            ...this.formList,
            equipAttr: this.tabActive,
            dictionaryDetailsCode: this.systemCode,
          }
          this.$api.getCustomGroupingSave(data).then(res => {
            if (res.code === '200') {
              this.$message({
                message: '保存成功',
                type: 'success'
              })
              this.closeDialog()
              this.getAssetsGroup()
              this.getTreeList()
            }
          })
        }
      })
    },
    closeDialog() {
      this.formList = {
        id: "",
        groupName: '',
        pid: '',
      }
      this.isVisible = false
    },
    filterNode(value, data) {
      if (!value) return true
      return data.groupName.indexOf(value) !== -1
    },
    nodeClick(data, node) {
      this.isEquipment = true
      this.checkedTreeNode = data
    },
    nodeDrop(source, target, ev) {
      this.dragNodeObj = {}
      this.dragNodeObj.sourceFileMenuId = source.data.ledgerId
      this.dragNodeObj.sourcessmType = source.data.ledgerType
      this.dragNodeObj.targetFileMenuId = target.data.ledgerId
      this.dragNodeObj.targetLedgerType = target.data.ledgerType
      this.$api.dragMoveFile(this.dragNodeObj).then(res => {
        this.$message[res.code == 200 ? 'success' : 'error'](res[`${res.code == 200 ? 'msg' : 'message'}`])
      })
      this.getTreeList()
    },
    closeDelDialog() {
      this.delVisible = false
    },
    delSubmit() {
      let params = {
        isMoveFile: this.isMoveFile,
        ledgerId: this.delId
      }
      this.$api.delDirOrClass(params).then(res => {
        if (res.code == 200) {
          this.delVisible = false
          this.getTreeList()
          this.$message({
            message: '删除成功',
            type: 'success'
          })
        }
      })
    },
    // 选择设备弹窗保存
    submitEquipmentDialog(data) {
      this.$api.getCustomGroupingsaveRelated(data).then(res => {
        if (res.code == 200) {
          this.isEquipment = false
          this.getAssetsGroup()//自定义分组树
        }
      })
    },
    // 选择设备弹窗关闭
    closeEquipmentDialog() {
      this.isEquipment = false
    },
    // 配置分组树结构结束---------------------------

    // 跳转监测详情
    jumpMonitorDetail(item) {
      const activeData = this.tabs.find((v) => v.equipAttr == this.tabActive)
      const name = newMonitorTypeList.find((item) => item.systemCode == activeData.parentCode).systemName
      let path = ''
      path = `${this.$route.meta.jumpAddress}/deviceDetails`
      if (!path) return
      this.$router.push({
        path: path,
        query: {
          id: item.id,
          assetsName: item.assetsName,
          systemCode: this.systemCode,
          equipAttr: this.tabActive
        }
      })
    },
    getParameterPercent(item, value) {
      const parameter = item.parameterList.find((e) => e.parameterId === value) || {}
      const parameterValue = parameter.parameterValue || 0
      return ((parameterValue / item.imsHeight || 0) * 100).toFixed(2)
    },
    getParameterValueById(item, value) {
      return item.parameterList.find((e) => e.parameterId === value) || {}
    },

    // 控制参数弹窗----------------
    submitParamsGather(data) {
      let obj = {
        [data.id]: data.value
      };
      this.$api.getQueryInstanceFunction(this.dataId, data.metadataTag, obj).then(res => {
        if (res.status === 200) {
          this.$message.success(res.message)
          this.closeHarvesterDialog()
        } else {
          this.$message.error(res.message)
          this.closeHarvesterDialog()
        }
      })
    },
    closeHarvesterDialog() {
      this.enumDialogShow = false
      this.booleDialogShow = false
      this.DialogShow = false
    },
    // 获取监测项列表
    getDataList() {
      let params = {
        ...this.searchFrom,
        dictionaryDetailsCode: this.systemCode,
        equipAttr: this.tabActive,
        page: this.pagination.current,
        pageSize: this.pagination.size
      }
      this.listLoading = true
      this.listData = []
      this.$api
        .getOperationalMonitoringList(params)
        .then((res) => {
          this.listLoading = false
          if (res.code == '200') {
            this.listData = res.data.records ? this.setData(res.data.records) : []
            this.pagination.total = res.data.total ? res.data.total : 0
          }
        })
        .catch(() => {
          this.listLoading = false
        })
    },
    setData(list) {
      list.forEach((item) => {
        item.parameterObj = {}
        item.iotPropertyList.forEach((v) => {
          if (!item.parameterObj[v.metadataName]) {
            item.parameterObj[v.metadataName] = []
          }
          item.parameterObj[v.metadataName].push(v)
        })
      })
      return list
    },
    // 空间数据清除
    handleClear() {
      this.searchFrom.spaceLocation = ''
      this.areaName = ''
    },
    // 选择下拉树 数据
    handleNodeClick(data) {
      this.searchFrom.spaceLocation = data.id
      this.areaName = data.ssmName
      this.$refs.treeSelect.blur()
    },
    // 下拉树懒加载
    serverLoadNode(node, resolve) {
      if (node.level == 0) {
        return resolve(node.data)
      } else if (node.level > 0 && node.level < 4) {
        return resolve(node.data.children)
      } else {
        return this.spaceTreeChange(node.data, resolve)
      }
    },
    // 获取服务空间树形结构
    getTreelist() {
      // this.$api.getStructureTree({}, __PATH.USER_CODE).then((res) => {
      this.$api.getSpaceTreeList({}).then((res) => {
        if (res.code == 200) {
          this.spaces = res.data
          // 增加 懒加载节点
          res.data.map((e) => {
            e.leaf = false
          })
          // 转换为树形结构数据
          this.serverSpaces = transData(res.data, 'id', 'pid', 'children')
        }
      })
    },
    // 服务空间树结构点击获取自己及父节点ID并请求数据实现懒加载
    spaceTreeChange(value, resolve) {
      const child = ListTree(this.spaces, value.pid)
      child.push(value.id)
      let treeId = child.toString()
      let data = {
        current: 1,
        functionDictId: '',
        simCode: treeId,
        size: 999
      }
      // this.$api.getSpaceInfoList(data, __PATH.USER_CODE).then((res) => {
      this.$api.getSpaceInfoList(data).then((res) => {
        if (res.code == 200) {
          if (typeof resolve === 'function') {
            let treeNodeData = JSON.parse(JSON.stringify(res.data.records))
            treeNodeData.map((e) => {
              e.leaf = true
            })
            resolve(treeNodeData)
          }
        }
      })
    },
    // 图纸模式-列表获取
    getScaleImgByProjectCode() {
      this.$api.getDictionaryDetailsCode(this.systemCode).then(res => {
        if (res.code === '200') {
          this.scadaList = res.data
          if (res.data.length) {
            this.checkedTeamData = res.data[0].id
          }

        }
      })
    },
    //scada图纸获取
    setScadaShow() {
      this.$refs.scadaShow.getScadaList(this.checkedTeamData)
    },
    // 图纸/列表模式切换
    switchPattern(type) {
      if (this.currentPattern != type) {
        this.currentPattern = type
        // this.searchForm()
        if (type === 1) {
          if (this.checkedTeamData) {
            this.$nextTick(() => {
              this.setScadaShow()
            })
          } else {
            this.$message.warning('暂未绑定设备图纸！')
          }
        }
      }
    },
    // 图纸模式-列表单击
    handleTeamClick(data, index) {
      this.currentSelectedIndex = index;
      this.checkedTeamData = data
      this.setScadaShow()
    },
    // 图纸
    visibilityChange(e) {
      const item = e.target
      const itemWidth = item.offsetWidth
      const itemScrollWidth = item.scrollWidth
      if (itemWidth < itemScrollWidth) {
        this.isShowTooltip = true
      } else {
        this.isShowTooltip = false
      }
    },

  }
}
</script>
<style lang="scss">
.tooltip-content {
  font-size: 14px;
  font-weight: 400;
  color: #fff;
  line-height: 22px;

  p {
    margin: 0;
  }

  .tooltip-content-value {
    display: flex;
    flex-wrap: wrap;
    max-width: 500px;

    p {
      margin-left: 15px;
    }
  }
}
</style>
<style lang="scss" scoped>
.securityOperationMonitor {
  p {
    margin: 0;
  }

  ::v-deep .container-header {
    border-radius: 4px 4px 0 0;
  }

  .securityOperationMonitor-header {
    position: relative;
    height: 40px;

    ::v-deep .el-tabs {
      .el-tabs__item {
        padding: 0 25px;
      }
    }

    .heade-pattern {
      display: flex;
      position: absolute;
      right: 16px;
      top: 50%;
      margin: 0 !important;
      transform: translateY(-50%);

      .pattern-item {
        cursor: pointer;
        font-size: 15px;

        .pattern-icon {
          font-size: 16px;
          margin-right: 6px;
        }
      }

      .pattern-item:last-child {
        margin-left: 16px;
      }
    }
  }

  .securityOperationMonitor-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    overflow-x: hidden;

    .statistics-item {
      width: calc(100% / 5);
      height: 120px;
      min-width: 150px;
      margin-left: 16px;
      padding: 24px;
      background: #fff;
      border-radius: 4px;
      margin-top: 16px;
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .item-title {
        font-size: 15px;
        font-weight: 500;
        color: #121f3e;
      }

      .item-value {
        height: 36px;
        font-size: 30px;
        font-weight: bold;
        color: #121f3e;
        line-height: 36px;

        &>span {
          margin-left: 4px;
          font-size: 15px;
          font-weight: 500;
          color: #ccced3;
        }
      }

      .item-icon {
        position: absolute;
        right: 24px;
        bottom: 24px;
        width: 40px;
        height: 40px;
      }
    }

    .statistics-item:first-child {
      margin-left: 0;
    }

    .content-main {
      flex: 1;
      display: flex;
      flex-direction: column;
      margin-top: 16px;
      background: #fff;
      overflow: hidden;

      .search-from {
        padding: 0 0 10px 16px;

        .fadeBtn {
          cursor: pointer;
        }

        &>div {
          margin-top: 16px;
          margin-right: 16px;
        }
      }

      .main-content {
        flex: 1;
        padding: 0 16px;
        overflow: auto;

        .monitor-item:hover {
          box-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 2px 7px 3px rgba(0, 0, 0, 0.09);
        }

        .monitor-item {
          margin-top: 16px;
          width: 100%;
          padding: 16px;
          background: #faf9fc;
          border-radius: 4px;
          height: 200px;

          .item-heade {
            padding: 0 0 10px;
            margin-bottom: 10px;
            font-size: 14px;
            font-weight: 500;
            color: #121f3e;
            line-height: 14px;
            word-break: break-all;
            border-bottom: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
          }

          ::v-deep .bjitem0 .el-badge__content {
            background-color: #f5dc4f;
          }

          ::v-deep .bjitem1 .el-badge__content {
            background-color: #da8a5c;
          }

          ::v-deep .bjitem2 .el-badge__content {
            background-color: #e24f48;
          }

          ::v-deep .bjitem3 .el-badge__content {
            background-color: #942625;
          }

          .lineStatus {
            display: inline-block;
            padding: 4px 8px;
            margin-right: 5px;
            border-radius: 4px;
          }

          .onlineStatus {
            background: #E8FFEA;
            color: #00B42A;
          }

          .offlineStatus {
            background: #E5E6EB;
            color: #96989A;
          }

          .item-main {
            display: flex;

            .main-img {
              width: 60px;
              height: 60px;
              margin-top: 15px;
            }

            .main-content {
              display: flex;
              flex-wrap: wrap;
              justify-content: space-between;
              align-items: center;
            }

            .main-item {
              display: flex;
              flex-direction: column;
              justify-content: space-between;
              padding: 6px 0 6px 20px;
              width: 50%;

              .main-item-box {
                display: flex;
                align-items: center;
              }

              .main-item-num {
                font-size: 16px;
                font-weight: bold;
                color: #121f3e;
              }
            }

            .item-title {
              font-size: 14px;
              font-weight: 400;
              color: #414653;
              line-height: 30px;
              margin-right: 10px;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 1;
              overflow: hidden;
              text-overflow: ellipsis;
              width: 77px;
            }

            .item-buttom {
              width: 68px;
              height: 22px;
              line-height: 20px;
              text-align: center;
              background: #fff;
              font-size: 12px;
              border: 1px solid #E4E7ED;
              border-radius: 4px;
              color: #666;
              cursor: pointer;

              img {
                width: 12px;
                height: 12px;
              }
            }

            .item-value {
              font-size: 16px;
              font-weight: bold;
              color: #121f3e;
              line-height: 20px;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 1;
              overflow: hidden;
              text-overflow: ellipsis;
              width: 100px;
            }
          }


        }

        ::v-deep .el-row {
          display: flex;
          flex-wrap: wrap;
        }
      }

      .main-footer {
        padding: 10px 16px;
      }
    }
  }

  .securityOperationMonitor-mode-content {
    height: calc(100% - 16px);
    margin-top: 16px;
    display: flex;

    ::v-deep .monitor-content-left {
      width: 246px;
      height: 100%;
      background: #fff;
      border-radius: 4px;
      padding: 16px;
      overflow: auto;

      p {
        padding: 10px 15px;
        color: #333;
        border-radius: 4px;
        margin-bottom: 0;
        cursor: pointer;
      }

      p.active {
        background: #E6EFFC;
        color: #3562DB;
      }

    }

    .monitor-content-right {
      height: 100%;
      flex: 1;
    }
  }
}

.filter {
  margin-bottom: 10px;
}

::v-deep .el-badge__content {
  line-height: 15px;
}

.mr-6 {
  margin-right: 6px;
}
</style>
