<template>
  <PageContainer>
    <div slot="content" class="space-content" style="height: 100%">
      <div class="space-content-left">
        <div class="left_title">单位类型</div>
        <div class="left_content">
          <el-tree ref="unitTree" :data="treeData" :props="defaultProps" node-key="id" :highlight-current="true" @node-click="nodeClick"> </el-tree>
        </div>
      </div>
      <div class="space-content-right">
        <div style="height: 100%; display: flex; flex-direction: column">
          <div class="search-from">
            <el-input
              v-model="searchFrom.unitsName"
              placeholder="单位名称"
              style="width: 300px"
              suffix-icon="el-icon-search"
              clearable
              maxlength="25"
              onkeyup="if(value.length>25)value=value.slice(0,25)"
            >
            </el-input>
            <el-select v-model="searchFrom.status" placeholder="全部状态" clearable>
              <el-option v-for="(item, index) in statusData" :key="index" :label="item.name" :value="item.id"></el-option>
            </el-select>
            <el-button type="primary" plain style="margin-right: 10px" @click="resetForm">重置</el-button>
            <el-button type="primary" @click="searchForm">查询</el-button>
          </div>
          <div class="search-control">
            <el-button type="primary" icon="el-icon-plus" @click="tableControl('add')">新增</el-button>
          </div>
          <div class="contentTable-main table-content">
            <el-table v-loading="tableLoading" border style="width: 100%; height: 100%" :data="tableData" :height="tableHeight">
              <el-table-column type="index" label="序号" width="70">
                <template slot-scope="scope">
                  <span>{{ (pageParam.currentPage - 1) * pageParam.pageSize + scope.$index + 1 }}</span>
                </template>
              </el-table-column>
              <el-table-column label="单位名称" prop="unitsName" width="150" show-overflow-tooltip></el-table-column>
              <el-table-column label="编码" prop="unitsCode" show-overflow-tooltip> </el-table-column>
              <el-table-column label="联系人" prop="contacts" show-overflow-tooltip> </el-table-column>
              <el-table-column label="联系方式" prop="contactsPhone" show-overflow-tooltip> </el-table-column>
              <el-table-column label="说明" prop="remarks" show-overflow-tooltip> </el-table-column>
              <el-table-column label="启用状态" prop="status"></el-table-column>
              <el-table-column label="操作" width="140" fixed="right">
                <template slot-scope="scope">
                  <el-button type="text" @click="tableControl('view', scope.row)">查看</el-button>
                  <el-button type="text" @click="tableControl('edit', scope.row)">编辑</el-button>
                  <el-button type="text" style="color: #fa403c" @click="tableControl('del', scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="contentTable-footer">
            <el-pagination
              :current-page="pageParam.currentPage"
              :page-size="pageParam.pageSize"
              :total="pageParam.total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin.js'
import { transData } from '@/util'
export default {
  name: 'unitsManage',
  components: {},
  mixins: [tableListMixin],
  data() {
    return {
      showTooltip: false,
      treeLoading: false,
      treeData: [
        {
          id: '3',
          name: '供应商'
        },
        {
          id: '5',
          name: '生产厂家'
        }
      ],
      idArr: [],
      spaceIds: [], // 选中空间
      simNames: [], // 选中空间名称
      defaultProps: {
        label: 'name',
        value: 'id',
        children: ''
      },
      statusData: [
        {
          name: '启用',
          id: '0'
        },
        {
          name: '禁用',
          id: '1'
        }
      ],
      searchFrom: {
        unitsName: '',
        status: ''
      },
      tableLoading: false,
      tableData: [],
      pageParam: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      checkNode: {}
    }
  },
  computed: {},
  mounted() {
    if (this.$route.query.type) {
      this.checkNode = this.treeData.find((i) => i.id == this.$route.query.type)
    } else {
      this.checkNode = this.treeData[0]
    }
    this.$refs.unitTree.setCurrentKey(this.checkNode.id)
    this.getSpacelistFn()
  },
  methods: {
    tableControl(key, data) {
      if (key == 'del') {
        // 删除
        this.$confirm('确认删除当前数据吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            const params = {
              id: data.id,
              userName: this.$store.state.user.userInfo.user.staffName,
              userId: this.$store.state.user.userInfo.user.staffId
            }
            this.$api.hscUnitDelete(params).then((res) => {
              if (res.code == 200) {
                this.$message({ message: '删除成功', type: 'success' })
                this.isLastPage(1)
                this.getSpacelistFn()
              }
            })
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            })
          })
      } else {
        let queryData = {
          parentId: this.checkNode.id,
          type: key
        }
        if (data) {
          queryData.id = data.id
        }
        this.$router.push({
          name: 'addUnitsManage',
          query: queryData
        })
      }
    },
    getSpacelistFn() {
      let data = {
        currentPage: this.pageParam.currentPage,
        pageSize: this.pageParam.pageSize,
        unitsTypeCode: this.checkNode.id,
        category: 2,
        userName: this.$store.state.user.userInfo.user.staffName,
        userId: this.$store.state.user.userInfo.user.staffId,
        unitsName: this.searchFrom.unitsName,
        status: this.searchFrom.status
      }
      this.tableLoading = true
      this.$api.hscUnitManger(data).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.list
          this.pageParam.total = res.data.sum
        }
        this.tableLoading = false
      })
    },
    nodeClick(val) {
      this.checkNode = val
      this.$router.push({
        query: {
          type: val.id
        }
      })
      this.pageParam = {
        currentPage: 1,
        pageSize: 15
      }
      this.getSpacelistFn()
    },
    getTreeNode(node) {
      if (node.level >= 1) {
        this.spaceIds.unshift(node.data.id)
        this.simNames.unshift(node.data.ssmName)
        this.getTreeNode(node.parent)
      } else {
        this.spaceIds.unshift('#')
      }
    },
    // 判断当前页是否是最后一页
    isLastPage(deleteNum) {
      let deleteAfterPage = Math.ceil((this.pagination.total - deleteNum) / this.pagination.size)
      let currentPage = this.pagination.current > deleteAfterPage ? deleteAfterPage : this.pagination.current
      this.pagination.current = currentPage < 1 ? 1 : currentPage
    },
    // 重置
    resetForm() {
      this.searchFrom = {
        unitsName: '',
        status: ''
      }
      this.pageParam = {
        currentPage: 1,
        pageSize: 15
      }
      this.getSpacelistFn()
    },
    // 查询
    searchForm() {
      this.pageParam = {
        currentPage: 1,
        pageSize: 15
      }
      this.getSpacelistFn()
    },
    handleSizeChange(val) {
      this.pageParam.pageSize = val
      this.getSpacelistFn()
    },
    handleCurrentChange(val) {
      this.pageParam.currentPage = val
      this.getSpacelistFn()
    },
    // 树划入划出效果
    onMouseOver(id) {
      const parentWidth = this.$refs[`nodeLabel${id}`].parentNode.offsetWidth // 获取元素父级可视宽度
      const contentWidth = this.$refs[`nodeLabel${id}`].offsetWidth // 获取元素可视宽度
      this.showTooltip = contentWidth <= parentWidth
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: #e6effc !important;
}
::v-deep .el-tree-node__content {
  height: 36px !important;
  position: relative;
}
.space-content {
  height: 100%;
  display: flex;
  .space-content-left {
    width: 280px;
    height: 100%;
    padding: 0px 10px 10px 10px;
    background: #fff;
    border-radius: 4px 0px 0px 4px;
    border-right: 1px solid #e4e7ed;
    .left_content {
      width: 100%;
      height: calc(100% - 95px);
      overflow: auto;
    }
    .left_title {
      font-weight: bold;
      font-size: 16px;
      color: #333333;
      line-height: 22px;
      padding: 16px 14px;
    }
  }
  .space-content-right {
    height: 100%;
    min-width: 0;
    padding: 10px 20px 20px 20px;
    background: #fff;
    border-radius: 0px 4px 4px 0px;
    flex: 1;
    .search-from {
      & > div,
      .el-button {
        margin-right: 10px;
        margin-top: 10px;
      }
      & > .el-button:last-child {
        margin: 0px;
      }
    }
    .search-control {
      margin-bottom: 10px;
      & > .el-button {
        margin-top: 10px;
        margin-right: 10px;
        margin-left: 0px;
      }
      // & > .el-button:last-child {
      //   margin: 0px;
      // }
    }
    .contentTable-main {
      flex: 1;
      overflow: hidden;
    }
    .contentTable-footer {
      padding: 10px 0 0;
    }
  }
  .content {
    width: 100%;
    max-height: 500px !important;
    overflow: auto;
    background-color: #fff !important;
  }
}
::v-deep .el-tree-node {
  white-space: normal;
  // outline: 0;
}
.nodeLabel {
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  padding-right: 20px;
}
</style>
