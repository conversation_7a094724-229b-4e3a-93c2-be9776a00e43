<template>
  <PageContainer :footer="true">
    <div slot="content" ref="contentRef" class="contentBox">
      <h1>调度配置</h1>
      <el-form ref="form" label-width="280px" class="form_class">
        <div class="parameter_row">
          <el-form-item label="开始任务后是否自动打开摄像头">
            <el-radio v-model="openCamera" :label="2">否</el-radio>
            <el-radio v-model="openCamera" :label="1">是</el-radio>
          </el-form-item>
        </div>
        <div class="parameter_row">
          <el-form-item label="摄像头打开后是否保存实时视频到客户端">
            <el-radio v-model="saveVideo" :label="2">否</el-radio>
            <el-radio v-model="saveVideo" :label="1">是</el-radio>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div slot="footer">
      <el-button type="primary" @click="submitForm()">确认</el-button>
    </div>
  </PageContainer>
</template>
<script>
export default {
  name: 'basicConfiguration',
  data() {
    return {
      openCamera: 2,
      saveVideo: 2
    }
  },
  mounted() {
    this.getConfigById()
  },
  methods: {
    getConfigById() {
      this.$api.queryConfigData({}).then(res => {
        if (res.code == '200') {
          this.openCamera = res.data.openCamera
          this.saveVideo = res.data.saveVideo
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    submitForm() {
      let params = {
        openCamera: this.openCamera,
        saveVideo: this.saveVideo
      }
      this.$api.saveConfigData(params).then(res => {
        if (res.code == '200') {
          this.$message.success(res.msg)
          this.getConfigById()
        } else {
          this.$message.error(res.msg)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.contentBox {
  height: 100%;
  width: 100%;
  padding: 20px;
  background: #ffffff;
}
</style>
