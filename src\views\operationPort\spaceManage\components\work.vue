<template>
  <div style="width: 100%; height: 100%">
    <div class="control-btn-header">
      <div class="statistics">
        <div :class="['item', 'pointer-style', { 'hover-style': flowCode == '' }]" @click="changeFlowCode('')">
          <span>服务总量</span>
          <span>{{ countData.all }}</span>
        </div>
        <div :class="['item', 'pointer-style', { 'hover-style': flowCode == '5' }]" @click="changeFlowCode('5')">
          <span>已完工</span>
          <span>{{ countData.completed }}</span>
        </div>
        <div :class="['item', 'pointer-style', { 'hover-style': free1 == '30' }]" @click="changeFlowCode('30')">
          <span>未完工</span>
          <span>{{ countData.unfinishedwork }}</span>
        </div>
        <div :class="['item', 'pointer-style', { 'hover-style': flowCode == '6' }]" @click="changeFlowCode('6')">
          <span>已取消</span>
          <span>{{ countData.cancelled }}</span>
        </div>
        <div class="item pure">
          <span>完工率</span>
          <span>{{ countData.completionRate }}</span>
        </div>
        <div class="item pure">
          <span>平均响应</span>
          <span :class="{ 'small-text': countData.finishTime.length > 8 }">{{ countData.response }}</span>
        </div>
        <div class="item pure">
          <span>平均完工</span>
          <span :class="{ 'small-text': countData.finishTime.length > 8 }">{{ countData.finishTime }}</span>
        </div>
        <div class="item pure">
          <span>返修率</span>
          <span>{{ countData.repairWork }}</span>
        </div>
        <div class="item pure">
          <span>综合评价</span>
          <span>{{ countData.evaluate }}</span>
        </div>
        <div class="item pure">
          <span>回访率</span>
          <span>{{ countData.callBack }}</span>
        </div>
      </div>
    </div>
    <div style="overflow: auto; height: 100%; margin-top: 8px">
      <div class="content">
        <!-- <el-row :gutter="16">
          <el-col :xs="24" :md="24" :lg="12" style="padding: 10px 5px 10px 0;">
            <div class="col" style="height: 100%;">
              <div class="title">
                <svg-icon name="right-arrow" />
                <span>服务概况</span>
              </div>
              <div ref="taskAnalysis" class="charts"></div>
            </div>
          </el-col>
          <el-col :xs="24" :md="24" :lg="12" style="padding: 10px 0 10px 5px;">
            <div class="col">
              <div class="title">
                <svg-icon name="right-arrow" />
                <span>近6个月工单走势</span>
              </div>
              <div id="workOdertTrendEcharts"></div>
            </div>
          </el-col>
        </el-row> -->
        <el-row>
          <el-col :xs="24" :md="24" :lg="12" style="width: 100%">
            <div class="col" style="height: 100%; width: 100%">
              <div class="contentTable">
                <div class="contentTable-main table-content">
                  <el-table :data="tableData" height="95%">
                    <el-table-column type="index" label="序号" width="50" align="center">
                      <template slot-scope="scope">
                        <span>{{ (pagination.current - 1) * pagination.size + scope.$index + 1 }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="工单号" prop="" width="200px">
                      <template slot-scope="scope">
                        <span class="color_blue" @click="ViewFn(scope.row)">
                          {{ scope.row.workNum }}
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-for="(column, index) in tableColumn"
                      :key="index"
                      :prop="column.prop"
                      :label="column.label"
                      :min-width="column.minWidth"
                      show-overflow-tooltip
                    >
                      <template slot-scope="scope">
                        <table-render v-if="column.render" :sc="scope" :row="scope.row" :render="column.render"></table-render>
                        <div v-else-if="!column.formatter">
                          {{ scope.row[column.prop] }}
                        </div>
                        <div v-else>
                          {{ column.formatter(scope.row) }}
                        </div>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>
              <div class="contentTable-footer">
                <el-pagination
                  style="margin-top: 3px"
                  :current-page="pagination.current"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="total"
                  :page-size="pagination.size"
                  :page-sizes="[15, 30, 50]"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                ></el-pagination>
              </div>
              <template v-if="workOrderDetailCenterShow">
                <el-dialog :visible.sync="workOrderDetailCenterShow" custom-class="detailDialog main" :close-on-click-modal="false" :before-close="workOrderDetailCloseDialog">
                  <template slot="title">
                    <span class="dialog-title">{{ dialogTitle }}</span>
                  </template>
                  <workOrderDetailList :rowData="detailObj" />
                </el-dialog>
              </template>
            </div>
          </el-col>
        </el-row>
      </div>
      <!-- <div class="buttom">1</div> -->
    </div>
  </div>
</template>

<script>
import store from '@/store/index'
import * as echarts from 'echarts'
import tableListMixin from '@/mixins/tableListMixin.js'
import workOrderDetailList from '../../../serviceQuality/maintenance/components/workOrderDetailList.vue'
// let echart = require('echarts/lib/echarts')
// require('echarts/lib/chart/bar')
// require('echarts/lib/component/tooltip')
// require('echarts/lib/component/graphic')
// require('echarts/lib/component/legend')
export default {
  name: 'work',
  components: {
    workOrderDetailList
  },
  mixins: [tableListMixin],
  props: {
    roomData: {
      type: Array
    }
  },
  data() {
    return {
      flowCode: '', // 工单状态
      free1: '',
      orderStatisticsName: [],
      orderType: 'year',
      seriesData: [],
      tableData: [],
      pagination: {
        current: 1,
        size: 15
      },
      total: 0,
      countData: {},
      tableColumn: [],
      detailObj: {},
      dialogTitle: '',
      workOrderDetailCenterShow: false,
      iomsTableColumn: [
        {
          prop: 'sourcesDeptName',
          label: '所属科室',
          formatter: (row) => {
            return row.sourcesDeptName === 'undefined' ? '' : row.sourcesDeptName
          }
        },
        {
          prop: 'workTypeName',
          label: '工单类型'
        },
        {
          prop: 'localtionName',
          label: '服务地点'
        },
        {
          prop: 'itemServiceName',
          label: '服务事项'
        },
        {
          prop: 'designateDeptName',
          label: '服务部门'
        },
        {
          prop: 'designatePersonName',
          label: '服务人员'
        },
        {
          prop: 'designatePersonPhone',
          label: '联系方式'
        },
        {
          prop: 'questionDescription',
          label: '说明'
        },
        {
          prop: 'flowtype',
          label: '状态'
        }
      ],
      workTypeName: '',
      echartsData: '',
      workTypeCode: '',
      iomsWorkOrderParams: {
        typeSources: '',
        contrastType: '',
        free1: '',
        free2: '',
        workTypeCode: '',
        urgencyDegree: '',
        disDegree: '',
        disDegreeNew: '',
        workSources: '',
        feedbackFlag: '',
        responseTime: '',
        responseTimeType: '',
        transportTypeCode: '',
        isPack: '',
        releaseType: '',
        statementFlagCode: '',
        showTimeType: 'all',
        hqfwsj: 'hqfwsj',
        startTime: '',
        endTime: '',
        sectionStartDate: '',
        sectionEndDate: '',
        sectionStartTime: '',
        sectionEndTime: '',
        sourcesDept: '',
        region: '',
        buliding: '',
        storey: '',
        room: '',
        localtionName: '',
        replyToCode: '',
        sourcesPhone: '',
        itemDetailCode: '',
        itemDetailName: '',
        itemTypeCode: '',
        itemTypeName: '',
        itemServiceCode: '',
        itemServiceName: '',
        itemList: '',
        questionDescription: '',
        workNum: '',
        restaurantId: '',
        haveStartTime: '',
        haveEndTime: '',
        orderBy: ''
      }
    }
  },
  watch: {
    '$store.state.settings.sidebarCollapse': {
      handler(val) {
        this.$nextTick(() => {
          let echartsDom = ['workOdertTypeEcharts', 'workOdertTrendEcharts']
          setTimeout(() => {
            echartsDom.forEach((item) => {
              echarts.init(document.getElementById(item)).resize()
            })
          }, 250)
        })
      },
      deep: true
    }
  },
  mounted() {
    this.getReckonCount()
    // this.getWorkOderType()
    // this.getWorkOderTrend()
    this.getTableList()
  },
  methods: {
    getTableList() {
      let params = {
        workTypeName: this.workTypeName,
        region: this.roomData[0],
        buliding: this.roomData[1],
        storey: this.roomData[2],
        room: this.roomData[3],
        pageNo: this.pagination.current,
        pageSize: this.pagination.size
      }
      if (this.free1 == '30') {
        params.free3 = this.free1
      } else {
        params.flowcode = this.flowCode
      }
      // params.workTypeCode = this.workTypeCode
      this.tableColumn = this.iomsTableColumn

      this.$api.getWorkOrderListBySpaceRuiAn(params).then((res) => {
        this.tableData = res.rows
        this.total = res.total
      })
    },
    // 工单类型统计
    getWorkOderType() {
      let params = {
        region: this.roomData[0],
        buliding: this.roomData[1],
        storey: this.roomData[2],
        room: this.roomData[3]
      }
      this.$api.getWorkOrder(params).then((res) => {
        if (res.code === '200') {
          this.echartsData = res.data.list
          this.$nextTick(() => {
            this.setEcharts()
          })
        }
      })
    },
    setEcharts() {
      let myChart = echarts.init(this.$refs.taskAnalysis)
      let pieData = []
      this.echartsData.forEach((i) => {
        const item = {}
        item.value = i.workNum || 0
        item.name = i.workTypeName
        item.workTypeCode = i.workTypeCode
        item.label = {}
        pieData.push(item)
      })
      pieData.sort((a, b) => a.value - b.name)
      if (pieData.length > 0) {
        pieData[0].label = {
          show: true,
          position: 'center',
          fontSize: 14,
          color: '#989898',
          formatter(params) {
            let text = params.data.name
            let value_format = params.data.value
            let proportion = params.percent
            return (text = `{time|${text}}\n ${proportion}%`)
          },
          rich: {
            time: {
              fontSize: 30,
              color: '#666',
              lineHeight: 35
            }
          }
        }
      }
      myChart.setOption({
        tooltip: {
          trigger: 'item'
        },
        legend: {
          type: 'scroll',
          orient: 'vertical',
          top: '30%',
          right: '1%',
          selected: {},
          formatter: function (name) {
            var oa = pieData
            var num = oa.reduce((sum, e) => sum + e.value, 0)
            for (var i = 0; i < pieData.length; i++) {
              if (name === oa[i].name) {
                return ' ' + name + '(' + oa[i].value + '单' + ')' + '    ' + ((oa[i].value / num) * 100).toFixed(2) + '%'
              }
            }
          }
        },
        series: [
          {
            name: '',
            type: 'pie',
            radius: ['50%', '65%'],
            avoidLabelOverlap: false,
            label: {
              normal: {
                show: false,
                position: 'center'
              }
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 14,
                color: '#989898',
                backgroundColor: '#fff',
                width: 135,
                formatter(params) {
                  let text = params.data.name
                  let value_format = params.data.value
                  let proportion = params.percent
                  return (text = `{time|${text}}\n ${proportion}%`)
                },
                rich: {
                  time: {
                    fontSize: 30,
                    color: '#666',
                    lineHeight: 35
                  }
                }
              }
            },
            labelLine: {
              show: false
            },
            data: pieData
          }
        ]
      })
      // myChart.on('click', (params) => {
      //   this.workTypeCode = params.data.workTypeCode
      //   this.getTableList()
      // })
    },
    getReckonCount() {
      let params = {
        region: this.roomData[0],
        buliding: this.roomData[1],
        storey: this.roomData[2],
        room: this.roomData[3]
      }
      this.$api.reckonCount({ ...this.iomsWorkOrderParams, ...params }).then((res) => {
        this.countData = res.body.data
      })
    },
    changeFlowCode(val) {
      if (val == '30') {
        this.free1 = val
        this.flowCode = null
      } else {
        this.flowCode = val
        this.free1 = ''
      }
      this.getTableList()
    },
    ViewFn(row) {
      this.detailObj = row
      this.dialogTitle = `综合维修（${this.detailObj.flowtype}）`
      this.workOrderDetailCenterShow = true
    },
    workOrderDetailCloseDialog() {
      this.workOrderDetailCenterShow = false
    },
    getWorkOderTrend() {
      let params = {
        region: this.roomData[0],
        buliding: this.roomData[1],
        storey: this.roomData[2],
        room: this.roomData[3]
      }
      this.$api.getWorkOrderTypeStatisticsBySpaceRuiAn(params).then((res) => {
        if (res.code === '200') {
          const arr = res.data.list
          if (arr.length) {
            // this.workOderTrendShow = false
            const dateArr = []
            const echartsData = []
            // 获取日期 以及 获取工单类型每日对应数据
            for (let i = 0; i < arr.length; i++) {
              if (dateArr.indexOf(arr[i].createDate) === -1) {
                dateArr.push(arr[i].createDate)
              }
              if (!echartsData.length || !echartsData.some((e) => e.name === arr[i].workTypeName)) {
                echartsData.push({
                  name: arr[i].workTypeName,
                  value: [arr[i].workOrderNum]
                })
              } else {
                for (let j = 0; j < echartsData.length; j++) {
                  if (echartsData[j].name === arr[i].workTypeName) {
                    echartsData[j].value.push(arr[i].workOrderNum)
                    break
                  }
                }
              }
            }
            this.$nextTick(() => {
              this.workOderTrendEchart(dateArr, echartsData)
            })
          } else {
            // this.workOderTrendShow = true
          }
        }
      })
    },
    workOderTrendEchart(dateArr, echartsData) {
      const getchart = echarts.init(document.getElementById('workOdertTrendEcharts'))
      var color = ['rgba(31, 250, 255, 0.3)', 'rgba(255, 227, 166, 0.28)', 'rgba(147, 130, 255, 0.26)', 'rgba(0, 248, 114, 0.24)', 'rgba(46, 119, 251, 0.3)']
      var borderColor = ['rgba(31, 250, 255, 1)', 'rgba(255, 227, 166, 1)', 'rgba(147, 130, 255, 1)', 'rgba(0, 248, 114, 1)', 'rgba(46, 119, 251, 1)']
      const seriesObj = []
      echartsData.forEach((item, index) => {
        console.log(item.value, '3333')

        const randomRgbColor = this.$tools.randomRgbColor('array')
        seriesObj.push({
          name: item.name,
          type: 'bar',
          // stack: 'total',
          barWidth: '10%',
          smooth: true,
          showAllSymbol: true,
          symbol: 'circle',
          symbolSize: 8,
          itemStyle: {
            // color: '#3eb5dd',
            // borderColor: '#f1f1f1',
            borderWidth: 1
          },
          markLine: {
            data: [
              {
                type: 'average',
                name: 'Avg'
              }
            ]
          },
          lineStyle: {
            normal: {
              width: 2
              // color: borderColor[index] ?? randomRgbColor[1]
            }
          },
          areaStyle: {
            // 区域填充样式
            normal: {
              color: new echarts.graphic.LinearGradient(
                0,
                0,
                0,
                1,
                [
                  {
                    offset: 0,
                    color: borderColor[index] ?? randomRgbColor[1]
                  },
                  {
                    offset: 1,
                    color: color[index] ?? randomRgbColor[0]
                  }
                ],
                false
              )
            }
          },
          data: item.value,
          markLine: {
            data: [{ type: 'average', name: 'Avg' }]
          }
        })
      })
      const option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: '#fff',
          textStyle: {
            color: '#000'
          },
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        legend: {
          data: Array.from(echartsData, ({ name }) => name),
          type: 'scroll',
          orient: 'horizontal',
          right: '50',
          top: '0',
          itemGap: 16,
          textStyle: {
            color: '#000'
          }
        },
        grid: {
          left: '2%',
          right: '0%',
          bottom: '10%',
          top: '18%',
          width: '90%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: dateArr
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            show: true,
            textStyle: {
              color: '#000'
            }
          },
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: '#fff',
              width: '1'
            }
          },
          splitLine: {
            lineStyle: {
              color: '#eee',
              width: 1,
              type: 'solid'
            }
          },
          axisTick: {
            show: false
          }
        },
        series: seriesObj
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    handleSizeChange(val) {
      this.pagination.size = val
      this.getTableList()
    },
    handleCurrentChange(val) {
      this.pagination.current = val
      this.getTableList()
    }
  }
}
</script>

<style lang="scss" scoped>
.small-text {
  font-size: 14px !important;
}
.control-btn-header {
  background-color: #fff;

  & > div {
    display: flex;
    padding: 10px 5px;
  }
}

.content {
  height: 89%;

  ::v-deep .el-row {
    height: 100%;

    .el-col {
      height: 100%;
    }
  }
}

.buttom {
  height: 50%;
  background-color: #fff;
  border-radius: 5px;
}

.ipt {
  width: 200px;
  margin-right: 10px;
}

.col {
  background-color: #fff;
  height: 100%;
  border-radius: 5px;
  padding: 10px;
}

#workOdertTypeEcharts,
#workOdertTrendEcharts,
.charts {
  position: relative;
  width: 100%;
  height: 95%;
}

.title span {
  font-size: 15px;
}

.table-block-text {
  width: 80px;
  line-height: 24px;
  text-align: center;
  cursor: pointer;
  color: #fff;
}

.color_blue {
  color: #5188fc;
  cursor: pointer;
}

.statistics {
  display: flex;
  justify-content: space-between;
  height: 78px;
}

.statistics .item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-around;
  flex: 1;
  background-color: rgb(53 98 219 / 6%);
  margin-right: 8px;
  border-radius: 4px;
}

.statistics .item span:nth-child(2) {
  font-size: 18px;
  color: #3562db;
  font-weight: 700;
}

.statistics .hover-style {
  background-color: #fff;
  box-shadow: 0 0 10px 0 rgb(32 56 114 / 13%);
  border-bottom: 3px solid #3562db;
}

.statistics .pointer-style {
  cursor: pointer;
}

.statistics .pure {
  background-color: #fff;
}

.statistics .pure span:nth-child(2) {
  color: #121f3e;
}

.color_blue {
  color: #5188fc;
  cursor: pointer;
}

.contentTable {
  height: 90%;
  background: #fff;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  overflow: auto;

  .contentTable-main {
    flex: 1;
    overflow: auto;
  }

  .contentTable-footer {
    padding: 10px 0 0;
  }

  .alarmLevel {
    padding: 3px 6px;
    border-radius: 4px;
    color: #fff;
    line-height: 14px;
  }

  .alarmStatus {
    position: relative;
    display: inline-block;
    padding-left: 12px;

    .alarmStatusIcon {
      display: inline-block;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 8px;
      height: 8px;
      border-radius: 100%;
    }
  }

  .collectIcon {
    font-size: 16px;
    margin-right: 4px;
  }
}
</style>
