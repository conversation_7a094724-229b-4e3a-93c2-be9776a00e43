<template>
  <router-link
    v-slot="{ navigate }"
    custom
    :to="to"
    :class="{
      title: true,
      'is-link': $store.state.settings.enableDashboard
    }"
    :title="title"
  >
    <div @click="navigate">
      <img v-if="showLogo" :src="logo" class="logo" />
      <span v-if="showTitle">{{ title }}</span>
    </div>
  </router-link>
</template>
<script>
import imgLogo from '@/assets/images/logo.png'
import schxyyLogo from '@/assets/images/schxyy.png'
import szzlyyLogo from '@/assets/images/szzlyy-logo.png'
import szlhyyLogo from '@/assets/images/szlhyy-logo.png'
import fjslyyLogo from '@/assets/images/fjslyy-logo.png'
import szrmyyLogo from '@/assets/images/szrmyy-logo.png'
import szdermyyLogo from '@/assets/images/szdermyy-logo.png'
import mhzyyLogo from '@/assets/images/mhzyy-logo.png'
export default {
  // eslint-disable-next-line vue/multi-word-component-names
  name: '<PERSON><PERSON>',
  props: {
    showLogo: {
      type: Boolean,
      default: true
    },
    showTitle: {
      type: Boolean,
      default: true
    }
  },
  data() {
    const menu = this.$store.state.menu
    const logoList = {
      szzlyy: szzlyyLogo,
      fjslyy: fjslyyLogo,
      szlhyy: szlhyyLogo,
      schxyy: schxyyLogo,
      szrmyy: szrmyyLogo,
      szdermyy: szdermyyLogo,
      mhzyy: mhzyyLogo
    }
    return {
      title: menu.routes[menu.headerActived].meta?.fullTitle ?? process.env.VUE_APP_TITLE,
      // logo: imgLogo
      logo: logoList[__PATH.VUE_APP_HOSPITAL_NODE_ENV] || imgLogo
    }
  },
  computed: {
    to() {
      let rtn = {}
      if (this.$store.state.settings.enableDashboard) {
        rtn.name = 'dashboard'
      }
      return { path: this.$route.path, query: this.$route.query }
    }
  },
  watch: {
    $route() {
      const menu = this.$store.state.menu
      this.title = menu.routes[menu.headerActived].meta?.fullTitle ?? process.env.VUE_APP_TITLE
    },
    '$store.state.menu.headerActived'() {
      const menu = this.$store.state.menu
      this.title = menu.routes[menu.headerActived].meta?.fullTitle ?? process.env.VUE_APP_TITLE
    }
  },
  mounted() {
    console.log()
  }
}
</script>
<style lang="scss" scoped>
.title {
  position: fixed;
  z-index: 1000;
  top: 0;
  width: inherit;
  padding: 0 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: $g-sidebar-logo-height;
  text-align: center;
  overflow: hidden;
  text-decoration: none;
  &.is-link {
    cursor: pointer;
  }
  .logo {
    width: 35px;
    height: 35px;
    & + span {
      margin-left: 10px;
    }
  }
  span {
    display: block;
    font-weight: bold;
    color: #fff;
    @include text-overflow;
  }
}
</style>
