<template>
  <el-dialog v-dialogDrag title="选择" :visible.sync="visable" width="70%" :before-close="closePeopleDialog" custom-class="model-dialog">
    <div style="display: flex" class="outermost">
      <div class="left">
        <div>
          <el-collapse v-model="activeName" accordion @change="collChange">
            <el-collapse-item v-for="item in hospitalList" :key="item.umId" :title="item.unitComName" :name="item.umId">
              <el-tree v-loading="collLoading" :data="treeList" :props="defaultProps" @node-click="handleNodeClick"></el-tree>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
      <div class="right">
        <div style="display: flex; margin-bottom: 10px">
          <el-input v-model.trim="form.staffName" class="sino_sdcp_input mr_15" placeholder="请输入姓名"></el-input>
          <el-input v-model.trim="form.mobile" class="sino_sdcp_input mr_15" placeholder="请输入手机号"></el-input>
          <el-select v-model.trim="form.job" placeholder="请选择在职状态">
            <el-option v-for="item in job" :key="item.value" :label="item.name" :value="item.value"> </el-option>
          </el-select>
          <el-select v-model.trim="form.quarters" placeholder="请选择岗位">
            <el-option v-for="item in quarters" :key="item.id" :label="item.postName" :value="item.id"> </el-option>
          </el-select>
          <el-select v-model.trim="form.sex" placeholder="请选择性别">
            <el-option v-for="item in sex" :key="item.value" :label="item.name" :value="item.value"> </el-option>
          </el-select>
          <el-button type="primary" plain @click="reset">重置</el-button>
          <el-button type="primary" @click="inquiry">查询</el-button>
        </div>
        <div>
          <el-table
            ref="treeTable"
            v-loading="loading"
            :data="tableData"
            :height="tableHeight"
            :row-key="getRowKeys"
            :header-cell-style="{ 'text-align': 'center' }"
            tooltip-effect="dark"
            style="width: 100%"
            border
            stripe
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" align="center" :reserve-selection="true"> </el-table-column>
            <el-table-column type="index" label="序号" prop="" width="50" align="center">
              <template slot-scope="scope">
                <span>{{ (currentPage - 1) * pageSize + scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="staffNumber" label="职工工号" align="center" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="staffName" label="人员姓名" align="center" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="sex" label="性别" align="center"> </el-table-column>
            <el-table-column prop="mobile" label="手机号码" align="center" show-overflow-tooltip> </el-table-column>
            <el-table-column prop=" phone" label="办公电话" align="center" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="entryData" label="入职天数" align="center" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="unitName" label="归属单位" width="80" align="center" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="officeName" label="所属部门" align="center" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="postName" label="岗位" align="center" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="jobName" label="职务" align="center" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="stationStatus" label="在职状态" align="center" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="activationFlagStr" label="激活状态" align="center" show-overflow-tooltip> </el-table-column>
          </el-table>
          <el-pagination
            :current-page="currentPage"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            :page-size="pageSize"
            :page-sizes="[15, 30, 50]"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closePeopleDialog">取消</el-button>
      <el-button type="primary" @click="qd">确定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { transData, ListTree } from '@/util'
export default {
  // props: {
  //   selectMode: {
  //     // 1多选 2单选
  //     type: String,
  //     default: '1'
  //   },
  //   visible: {
  //     type: Boolean
  //   },
  //   type: {
  //     type: String
  //   },
  //   messageType: {
  //     type: String
  //   }
  // },
  props: ['arrData', 'tableDataList', 'cancleDatasLists', 'tableCancleDatas3Lists', 'delPeopleIds', 'selectTypes'],
  data() {
    return {
      selectionIdsArr: [], // 表格选中的数据id
      cancleData: [],
      visable: true, // 弹框显示
      collLoading: true,
      loading: true,
      defaultProps: {
        children: 'children',
        label: 'deptName'
      },
      activeName: '',
      pmId: '',
      officeId: '',
      form: {
        staffName: '', // 姓名
        mobile: '', // 手机号
        job: '', // 职务
        quarters: '', // 岗位
        sex: '', // 性别
        keyword: '' // 关键词
      },
      tableVal: '',
      job: [
        {
          value: '0',
          name: '在职'
        },
        {
          value: '1',
          name: '离职'
        }
      ],
      sex: [
        {
          value: '2',
          name: '女'
        },
        {
          value: '1',
          name: '男'
        }
      ],
      quarters: [
        {
          id: 27,
          postDuty: '空调工',
          postName: '空调工'
        },
        {
          id: 28,
          postDuty: '',
          postName: '综合维修员'
        }
      ],
      pageSize: 15,
      currentPage: 1,
      total: 0,
      tableData: [],
      multipleSelection: [],
      treeList: [],
      spaces: [],
      hospitalList: []
    }
  },
  computed: {
    tableHeight() {
      return document.body.clientHeight - 525
    }
  },
  watch: {
    arrData(newValue, oldValue) {
      if (newValue.length != 0) {
        this.tableVal = newValue
        this.tableRangeFn()
      } else {
        this.$refs.treeTable.clearSelection()
      }
    },
    tableDataList(newValue, oldValue) {
      if (newValue) {
        this.tableDataRange = newValue
        this.tableRangeFn()
      } else {
        this.$refs.treeTable.clearSelection()
      }
    },
    cancleDatasLists(newValue, oldValue) {
      if (newValue) {
        this.tableVal = newValue
        this.tableRangeFns()
      } else {
        this.$refs.treeTable.clearSelection()
      }
    },
    tableCancleDatas3Lists(newValue, oldValue) {
      if (newValue) {
        this.tableData = newValue
        this.tableRangeFns()
      } else {
        this.$refs.treeTable.clearSelection()
      }
    }
  },
  created() {},
  mounted() {
    this.getTreeList()
    this.getLersonnelList()
  },
  methods: {
    // 回显表格数据
    tableRangeFn() {
      // console.log(this.tableVal,this.tableDataRange) // this.tableVal为选中的人员，，this.tableData为表格数据
      if (this.tableVal) {
        this.tableVal.forEach((item, index) => {
          // 回显之前选择的表格
          this.tableDataRange.forEach((v, i) => {
            if (item) {
              if (item.id == v.id) {
                this.cancleData.push(v)
                // console.log(its,v)
                this.$nextTick(() => {
                  this.$refs.treeTable.toggleRowSelection(v, true)
                })
              }
            }
          })
        })
      } else {
        this.$nextTick(() => {
          this.$refs.treeTable.clearSelection()
        })
      }
    },
    tableRangeFns() {
      // console.log(this.tableVal,this.tableData) //this.tableVal为选中的人员，，this.tableData为表格数据
      if (this.tableVal) {
        this.tableVal.forEach((item, index) => {
          // 回显之前选择的表格
          this.tableDataRange.forEach((v, i) => {
            if (item) {
              if (item.id == v.id) {
                this.cancleData.push(v)
                this.$nextTick(() => {
                  this.$refs.treeTable.toggleRowSelection(v, true)
                })
              }
            }
          })
        })
      } else {
        this.$nextTick(() => {
          this.$refs.treeTable.clearSelection()
        })
      }
    },
    // 获取岗位信息
    getQuarters() {
      // this.$api.selectByList({}).then((res) => {
      //   if (res.code == 200) {
      //     this.quarters = res.data
      //   }
      // })
    },
    handleNodeClick(val) {
      this.pmId = ''
      this.officeId = val.id
      this.getLersonnelList()
    },
    collChange(val) {
      this.officeId = ''
      this.pmId = val
      this.getLersonnelList()
      if (val != '') {
        this.treeList = []
        // this.$api.getSelectedDept({ unitId: val }, __PATH.USER_CODE).then((res) => {
        this.$api.getSelectedDept({ unitId: val }).then((res) => {
          this.collLoading = false
          if (res.code == 200) {
            this.spaces = res.data
            this.treeList = transData(res.data, 'id', 'pid', 'children')
          }
        })
      } else {
        this.getLersonnelList()
      }
      this.collLoading = true
    },
    getRowKeys(row) {
      return row.id
    },
    getTreeList() {
      // this.$api.getSelected({}, __PATH.USER_CODE).then((res) => {
      this.$api.getSelected({}).then((res) => {
        if (res.code == 200) {
          this.hospitalList = res.data
        }
      })
    },
    getLersonnelList() {
      let params = {
        current: this.currentPage,
        size: this.pageSize,
        sex: this.form.sex,
        staffName: this.form.staffName,
        mobile: this.form.mobile,
        pmId: this.pmId,
        officeId: this.officeId,
        postId: this.form.quarters, // 岗位
        stationStatus: this.form.job,
        activationFlag: '0'
      }
      // this.$api.staffList(params, __PATH.USER_CODE).then((res) => {
      this.$api.staffList(params).then((res) => {
        this.loading = false
        if (res.code == 200) {
          res.data.records.forEach((item) => {
            item.sex = item.sex ? (item.sex == 2 ? '女' : '男') : ''
            if (item.stationStatus == 0) {
              item.stationStatus = '在职'
            } else {
              item.stationStatus = '离职'
            }
          })
          this.tableData = res.data.records
          this.total = res.data.total
        }
      })
    },
    // 查询
    inquiry() {
      this.getLersonnelList()
    },
    closePeopleDialog() {
      this.$emit('closePeopleDialog')
      this.$emit('cancleDataRange', this.cancleData, this.tableData)
    },
    // 重置
    reset() {
      this.pageSize = 15
      this.currentPage = 1
      this.form.job = ''
      this.form.quarters = ''
      this.form.sex = ''
      this.form.staffName = ''
      this.form.mobile = ''
      // this.form.keyword = ''
      this.getLersonnelList()
    },
    handleClose(val) {
      this.$refs.table.clearSelection()
    },
    qd() {
      if (this.tableVal.length > 100) {
        this.$message.warning('每次页面最多显示指定人员不得超过100人')
        return
      }
      console.log()
      this.$emit('chosedPeopleFn', this.selectionIdsArr) // 如果只取this.selectionIdsArr[0]，会导致第二次选择的人员无法显示
      this.$emit('tableValFn', this.tableVal, this.tableData)
      this.closePeopleDialog()
    },
    // 查询列表数据
    getTableList() {},
    // 监听事件
    handleSelectionChange(val) {
      this.tableVal = val
      let idxArr = []
      if (!val) return
      val.forEach((val, index) => {
        this.tableData.forEach((v, i) => {
          if (val) {
            if (val.id == v.id) {
              idxArr.push(v)
            }
          }
        })
      })
      this.selectionIdsArr[this.currentPage - 1] = idxArr
    },
    // 分页
    handleSizeChange(val) {
      this.pageSize = val
      this.getLersonnelList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getLersonnelList()
    }
  }
}
</script>
<style lang="scss" scoped>
.outermost {
  width: 100%;
  max-height: 600px;
  border: 1px solid #eee;
  padding: 10px;
}
.left {
  padding-right: 10px;
  width: 230px;
  height: 100%;
  > div {
    width: 100%;
    padding: 10px;
    height: 100%;
    background-color: #fff;
    overflow: auto;
  }
}
.right {
  background-color: #fff;
  padding: 10px;
  width: calc(100% - 230px);
  height: 100%;
}
.el-pagination {
  // display: flex;
  // justify-content: flex-end;
  margin-top: 5px;
}
.model-dialog {
  padding: 10 !important;
}
::v-deep .el-select {
  width: 100px !important;
}
.el-select {
  width: 180px !important;
  margin-right: 10px;
}
::v-deep .model-dialog .el-dialog__body {
  overflow-y: hidden !important;
}
.sino_sdcp_input {
  width: 200px;
  margin-right: 10px;
}
</style>
