<template>
  <div>
    <el-dialog v-dialogDrag :modal="false" custom-class="interDialogDiv" width="40%" append-to-body :visible="visible" :close-on-click-modal="false" :before-close="close">
      <template slot="title">
        <span class="dialog-title">上传文件</span>
      </template>
      <el-form ref="form" class="form-data" style="width: 100%" :model="form" label-position="left" label-width="140px">
        <el-form-item label="文件位置" prop="fileMenuId" :rules="{ required: true, message: '请选择文件位置', trigger: 'blur' }">
          <el-cascader ref="cascader" v-model="filePosition" style="width: 100%" :options="list" :props="cascaderProps" @change="handleChange"></el-cascader>
        </el-form-item>
        <el-form-item label="名称" prop="dataName" :rules="{ required: true, message: '请输入标题', trigger: 'blur' }">
          <el-input v-model="form.dataName" placeholder="请输入标题" maxlength="50" />
        </el-form-item>
        <el-form-item label="上传附件" prop="fileUrl" :rules="{ required: true, message: '请上传附件', trigger: 'change' }">
          <el-upload
            action=""
            :file-list="fileList"
            :limit="1"
            :before-upload="beforeAvatarUpload"
            :http-request="(file) => httpRequset(file)"
            :on-remove="(file, fileList) => handleRemove(file, fileList)"
            :on-change="(file, fileList) => fileChange(file, fileList)"
          >
            <el-button :disabled="Boolean(form.fileUrl) || fileLoading" size="small" type="primary" :loading="fileLoading">点击上传</el-button>
            <span slot="tip" style="margin-left: 10px; font-size: 12px">附件大小不超过500M！</span>
          </el-upload>
          <div v-if="uploadProgress > 0" class="upload-progress">
            <el-progress :percentage="uploadProgress" color="#3562db"></el-progress>
          </div>
        </el-form-item>
        <el-form-item>
          <span slot="label">
            <span style="font-size: 18px">权限设置</span>
          </span>
        </el-form-item>
        <el-form-item>
          <span slot="label">
            <span style="font-size: 16px">可查看</span>
          </span>
          <el-radio v-model="form.viewPerm" label="0">所有人</el-radio>
          <el-radio v-model="form.viewPerm" label="1">指定成员</el-radio>
          <span v-if="form.viewPerm == 1" style="width: 200px; display: inline-block" @click="clickspanPeople">
            <el-input placeholder="选择人员"> </el-input>
          </span>
          <div v-if="form.viewPerm == 1">
            <span>已选人员：</span><span v-for="item in selectData" :key="item.id">{{ item.staffName }}{{ '、' }}</span>
          </div>
          <div v-else><span>已选人员：所有人</span></div>
        </el-form-item>
        <el-form-item>
          <span slot="label">
            <span style="font-size: 16px">可查看和下载</span>
          </span>
          <el-radio v-model="form.downloadPerm" :disabled="form.viewPerm == 1" label="0">所有人</el-radio>
          <el-radio v-model="form.downloadPerm" label="1">指定成员</el-radio>
          <span v-if="form.downloadPerm == 1" style="width: 200px; display: inline-block" @click="clickspanPeopleDownload">
            <el-input placeholder="选择人员"> </el-input>
          </span>
          <div v-if="form.downloadPerm == 1">
            <span>已选人员：</span><span v-for="item in selectDataDownload" :key="item.id">{{ item.staffName }}{{ '、' }}</span>
          </div>
          <div v-else><span>已选人员：所有人</span></div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" plain @click="close">取 消</el-button>
        <el-button type="primary" :loading="btnLoading" @click="submit">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 选择人员 -->
    <el-dialog v-if="memberShow" v-dialogDrag title="添加成员" width="50%" :visible.sync="memberShow" custom-class="interDialogDiv" :before-close="closeDialog" append-to-body>
      <div class="sapce_content">
        <div class="left">
          <div v-loading="treeLoading" class="sino_tree_box">
            <el-collapse v-model="activeName" style="padding: 0 10px 0 20px" accordion @change="handelChange">
              <el-collapse-item v-for="(list, index) in collapseData" :key="index" :title="list.unitComName" :name="list.umId">
                <div class="sino_tree_box" style="margin: 0">
                  <el-tree ref="tree" class="filter-tree" :data="treeData" :props="defaultProps" node-key="id" highlight-current @node-click="nodeClick"></el-tree>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>
        <div v-if="peopleTyle == 'see'" class="center">
          <el-input v-model.trim="userInfo" placeholder="搜索姓名、手机号、部门" clearable suffix-icon="el-icon-search" @input="nameInput"></el-input>
          <el-checkbox-group v-model="staffSelect" @change="handleCheck">
            <el-checkbox v-for="item in staffData" :key="item.id" :label="item.id" style="display: block">
              <div class="personCheck">
                <img src="../../../../assets/images/avatar.png" />
                <div class="info">
                  <div class="name">{{ item.staffName }}</div>
                  <div class="mobile">{{ item.mobile }}</div>
                </div>
              </div>
            </el-checkbox>
          </el-checkbox-group>
        </div>
        <div v-if="peopleTyle == 'download'" class="center">
          <el-input v-model.trim="userInfo" placeholder="搜索姓名、手机号、部门" clearable suffix-icon="el-icon-search" @input="nameInput"></el-input>
          <el-checkbox-group v-model="staffSelectDownload" @change="handleCheckDownload">
            <el-checkbox v-for="item in staffDataDownload" :key="item.id" :label="item.id" style="display: block">
              <div class="personCheck">
                <img src="../../../../assets/images/avatar.png" />
                <div class="info">
                  <div class="name">{{ item.staffName }}</div>
                  <div class="mobile">{{ item.mobile }}</div>
                </div>
              </div>
            </el-checkbox>
          </el-checkbox-group>
        </div>
        <div v-if="peopleTyle == 'see'" class="right">
          <div class="top">
            <span>
              <span class="label">已选:</span>
              <span class="num">{{ selectData.length ? selectData.length : 0 }}</span>
              <span class="dept">名用户</span>
            </span>
            <span class="clear" @click="clear">清空</span>
          </div>
          <div v-for="(item, index) in selectData" :key="index" class="item-list">
            <div style="display: flex">
              <img src="../../../../assets/images/avatar.png" />
              <div class="info">
                <div class="name">{{ item.staffName }}</div>
                <div class="mobile">{{ item.mobile }}</div>
              </div>
            </div>
            <div class="remove" @click="remove(item, index)"><i class="el-icon-close"></i></div>
          </div>
        </div>
        <div v-if="peopleTyle == 'download'" class="right">
          <div class="top">
            <span>
              <span class="label">已选:</span>
              <span class="num">{{ selectDataDownload.length ? selectDataDownload.length : 0 }}</span>
              <span class="dept">名用户</span>
            </span>
            <span class="clear" @click="clearDownload">清空</span>
          </div>
          <div v-for="(item, index) in selectDataDownload" :key="index" class="item-list">
            <div style="display: flex">
              <img src="../../../../assets/images/avatar.png" />
              <div class="info">
                <div class="name">{{ item.staffName }}</div>
                <div class="mobile">{{ item.mobile }}</div>
              </div>
            </div>
            <div class="remove" @click="removeDownload(item, index)"><i class="el-icon-close"></i></div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" plain @click="closeDialog">取 消</el-button>
        <el-button v-if="peopleTyle == 'see'" type="primary" @click="submitPeople('seeTrue')">确 定</el-button>
        <el-button v-else type="primary" @click="submitPeople('downloadTrue')">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { transData } from '@/util'
// import template from '../../reportManagement/template.vue'
// import memberDialog from './memberDialog.vue'
export default {
  components: {
    // template,
    // memberDialog
  },
  model: {
    prop: 'visible',
    event: 'close'
  },
  props: {
    // 弹框显示隐藏
    visible: {
      type: Boolean,
      default: false
    },
    // 下拉框列表
    list: {
      type: Array,
      default: () => []
    },
    // 选中的左侧树节点，新增的时候需要取id集合回显文件位置
    checkedTreeNode: {
      type: Object,
      default: () => {}
    },
    // 编辑需要用的当前行数据
    currentForm: {
      type: Object,
      default: () => {}
    },
    // 操作类型 新增，编辑
    type: {
      type: String,
      default: 'add'
    }
  },
  data() {
    return {
      keyWord: '', // 编码/名称/通用名/规格型号/SN
      treeData: [],
      defaultProps: {
        label: 'deptName',
        children: 'list'
      },
      treeLoading: false,
      selectData: [],
      selectDataDownload: [],
      activeName: '',
      collapseData: [],
      staffData: [], // 人员列表
      staffDataDownload: [], // 人员列表
      staffSelect: [],
      staffSelectDownload: [],
      officeId: '',
      pmId: '',
      userInfo: '',
      seeList: [],
      downloadList: [],
      peopleTyle: '',
      memberShow: false, // 成员
      form: {
        fileMenuId: '',
        fileMenuName: '',
        dataName: '',
        fileUrl: '',
        fileName: '',
        viewPerm: '0',
        downloadPerm: '0',
        viewUserIds: [],
        downloadUserIds: []
      },
      filePosition: [],
      fileList: [],
      cascaderProps: {
        label: 'ledgerName',
        value: 'ledgerId',
        checkStrictly: true
      },
      logInforId: '',
      uploadProgress: 0,
      chunkSize: 5 * 1024 * 1024, // 5MB per chunk
      btnLoading: false,
      fileLoading: false
    }
  },
  created() {
    this.getUnitListFn()
  },
  mounted() {
    this.logInforId = JSON.parse(sessionStorage.getItem('ihcrs_userInfo')).user.staffId
    this.$nextTick(() => {
      if (this.type === 'edit') {
        this.form.fileMenuId = this.currentForm.fileMenuId
        this.form.fileMenuName = this.currentForm.fileMenuName
        this.form.dataName = this.currentForm.dataName
        this.form.fileName = this.currentForm.fileName
        this.form.fileUrl = this.currentForm.fileUrl
        this.form.id = this.currentForm.id
        this.form.viewPerm = this.currentForm.viewPerm.toString()
        this.$api
          .getPostMemberListByPage({
            pmId: this.pmId,
            current: 1,
            size: 99999,
            officeId: this.officeId,
            userInfo: this.userInfo
          })
          .then((res) => {
            if (res.code == 200) {
              if (this.currentForm.viewUserIds) {
                this.form.viewUserIds = this.currentForm.viewUserIds.split(',')
                this.staffSelect = this.currentForm.viewUserIds.split(',')
                let arr = []
                this.staffSelect.forEach((el) => {
                  res.data.records.forEach((item) => {
                    if (el === item.id) {
                      arr.push(item)
                    }
                  })
                })
                this.selectData = arr
              }
              if (this.currentForm.downUserIds) {
                this.form.downloadUserIds = this.currentForm.downUserIds.split(',')
                this.staffSelectDownload = this.currentForm.downUserIds.split(',')
                let arr1 = []
                this.staffSelectDownload.forEach((el) => {
                  res.data.records.forEach((item) => {
                    if (el === item.id) {
                      arr1.push(item)
                    }
                  })
                })
                this.selectDataDownload = arr1
              }
            }
          })
        this.filePosition = this.currentForm.fileParentMenuId.split(',')
        this.form.downloadPerm = this.currentForm.downloadPerm.toString()
        if (this.filePosition[0] == '#') {
          this.filePosition.splice(0, 1)
        }
        this.filePosition = this.filePosition.filter((item) => {
          return item != ''
        })
        this.filePosition = Array.from(new Set(this.filePosition))
        this.fileList = [
          {
            name: this.currentForm.fileName,
            url: this.currentForm.fileUrl
          }
        ]
      } else {
        this.$api
          .getPostMemberListByPage({
            pmId: this.pmId,
            current: 1,
            size: 99999,
            officeId: this.officeId,
            userInfo: this.userInfo
          })
          .then((res) => {
            if (res.code == 200) {
              this.form.viewUserIds = [this.logInforId]
              this.staffSelect = [this.logInforId]
              let arr = []
              this.staffSelect.forEach((el) => {
                res.data.records.forEach((item) => {
                  if (el === item.id) {
                    arr.push(item)
                  }
                })
              })
              this.selectData = arr
              this.form.downloadUserIds = [this.logInforId]
              this.staffSelectDownload = [this.logInforId]
              let arr1 = []
              this.staffSelectDownload.forEach((el) => {
                res.data.records.forEach((item) => {
                  if (el === item.id) {
                    arr1.push(item)
                  }
                })
              })
              this.selectDataDownload = arr1
            }
          })
        this.form.fileMenuId = this.checkedTreeNode.ledgerId
        this.form.fileMenuName = this.checkedTreeNode.ledgerName
        this.form.viewPerm = '0'
        this.form.downloadPerm = '0'
        this.filePosition = `${this.checkedTreeNode.parentLedgerIds},${this.checkedTreeNode.ledgerId}`.split(',')
        this.filePosition.splice(0, 1)
        this.filePosition = Array.from(new Set(this.filePosition))
        this.filePosition = this.filePosition.filter((item) => {
          return item != ''
        })
      }
    })
  },
  methods: {
    //  获取单位列表
    getUnitListFn() {
      this.$api.getUnitList({}).then((res) => {
        this.treeLoading = false
        if (res.code == 200) {
          this.collapseData = this.unitList = res.data
          this.staffListByPageFn()
        }
      })
    },
    nameInput() {
      this.staffListByPageFn()
      this.staffSelect = this.selectData.map((item) => {
        return item.id
      })
      this.staffSelectDownload = this.selectDataDownload.map((item) => {
        return item.id
      })
    },
    handelChange(val) {
      this.activeName = val
      this.officeId = ''
      this.pmId = val
      this.getDeptListFn(val)
      this.staffListByPageFn()
    },
    // 部门列表
    getDeptListFn(unitId) {
      this.$api
        .getDeptList({
          unitId: unitId
        })
        .then((res) => {
          if (res.code == 200) {
            this.departList = res.data
            this.treeData = transData(res.data, 'id', 'pid', 'list')
          }
        })
    },
    //  获取人员信息列表
    staffListByPageFn() {
      this.$api
        .getPostMemberListByPage({
          pmId: this.pmId,
          current: 1,
          size: 99999,
          officeId: this.officeId,
          userInfo: this.userInfo
        })
        .then((res) => {
          if (res.code == 200) {
            this.staffData = res.data.records
            this.staffDataDownload = res.data.records
          }
        })
    },
    // 选择
    handleCheck(list) {
      let arr = []
      list.forEach((el) => {
        this.staffData.forEach((item) => {
          if (el === item.id) {
            arr.push(item)
          }
        })
      })
      this.selectData = arr
    },
    // 选择
    handleCheckDownload(list) {
      let arr = []
      list.forEach((el) => {
        this.staffDataDownload.forEach((item) => {
          if (el === item.id) {
            arr.push(item)
          }
        })
      })
      this.selectDataDownload = arr
    },
    removeSame(array) {
      let newArray = []
      for (let item of array) {
        // 使用JSON.stringfy()方法将数组和数组元素转换为JSON字符串之后再使用includes()方法进行判断
        if (JSON.stringify(newArray).includes(JSON.stringify(item))) {
          continue
        } else {
          // 不存在的添加到数组中
          newArray.push(item)
        }
      }
      return newArray
    },
    nodeClick(val) {
      this.pmId = val.umId
      this.officeId = val.id
      this.staffListByPageFn()
    },
    // 移除
    remove(list, index) {
      this.selectData.splice(index, 1)
      this.staffSelect = this.selectData.map((item) => {
        return item.id
      })
    },
    // 移除
    removeDownload(list, index) {
      this.selectDataDownload.splice(index, 1)
      this.staffSelectDownload = this.selectDataDownload.map((item) => {
        return item.id
      })
    },
    // 清空
    clear() {
      this.selectData = []
      this.staffSelect = []
      this.$nextTick(() => {
        this.$refs.tree.setCheckedKeys([])
      })
    },
    // 清空
    clearDownload() {
      this.selectDataDownload = []
      this.staffSelectDownload = []
      this.$nextTick(() => {
        this.$refs.tree.setCheckedKeys([])
      })
    },
    closeDialog() {
      if (this.type === 'add') {
        if (this.peopleTyle == 'see') {
          this.clear()
          this.form.viewUserIds = []
        } else {
          this.clearDownload()
          this.form.downloadUserIds = []
        }
      }
      this.memberShow = false
    },
    submitPeople(type) {
      if (this.peopleTyle == 'see') {
        if (this.selectData.length < 1) {
          this.$message({
            message: '请选择至少一条数据',
            type: 'warning'
          })
          return
        }
        this.form.viewUserIds = this.staffSelect
      } else {
        if (this.selectDataDownload.length < 1) {
          this.$message({
            message: '请选择至少一条数据',
            type: 'warning'
          })
          return
        }
        this.form.downloadUserIds = this.staffSelectDownload
      }
      this.memberShow = false
    },
    // 选择人员(查看)
    clickspanPeople() {
      this.peopleTyle = 'see'
      this.memberShow = true
    },
    // 选择人员(查看和下载)
    clickspanPeopleDownload() {
      this.peopleTyle = 'download'
      this.memberShow = true
    },
    handleChange(val) {
      if (val.length) {
        this.form.fileMenuId = val[val.length - 1]
        this.form.fileMenuName = this.$refs.cascader.getCheckedNodes()[0].label
      }
    },
    beforeAvatarUpload(file) {
      const fileSize = file.size / 1024 / 1024 <= 500
      if (!fileSize) {
        this.$message.error('上传附件大小不能超过 500MB!')
        return false
      }
      return true
    },
    fileChange(file, fileList) {
      // this.fileList = fileList
    },
    handleRemove(file, fileList) {
      this.form.fileUrl = ''
      this.form.fileName = ''
      this.fileList = []
      this.uploadProgress = 0
    },
    async httpRequset(file) {
      try {
        const fileSize = file.file.size
        const chunks = Math.ceil(fileSize / this.chunkSize)
        let uploadedChunks = 0
        let partETags = []
        this.fileLoading = true
        // 初始化上传
        const initResponse = await this.$api.initUpload({
          filename: file.file.name
        })
        if (initResponse.code !== '200') {
          throw new Error('初始化上传失败')
        }
        const uploadId = initResponse.data.uploadId
        for (let i = 0; i < chunks; i++) {
          const start = i * this.chunkSize
          const end = Math.min(start + this.chunkSize, fileSize)
          const chunk = file.file.slice(start, end)
          let params = {
            file: chunk,
            filename: file.file.name,
            partNumber: i + 1,
            uploadId: uploadId
          }
          const uploadListResponse = await this.$api.uploadList(params, {
            headers: {
              'Content-Type': 'multipart/form-data'
            },
            config: {
              onUploadProgress: (progressEvent) => {
                const chunkProgress = (progressEvent.loaded / progressEvent.total) * 100
                this.uploadProgress = Math.round((uploadedChunks * 100 + chunkProgress) / chunks)
              }
            }
          })
          if (uploadListResponse.code !== '200') {
            throw new Error('分片上传失败')
          }
          partETags.push(uploadListResponse.data)
          uploadedChunks++
        }
        const completeResponse = await this.$api.finishUpload({
          uploadId,
          filename: file.file.name,
          partETags
        })
        if (completeResponse.code !== '200') {
          throw new Error('完成上传失败')
        }
        if (!this.form.dataName) {
          this.form.dataName = file.file.name.replace(/\.[^/.]+$/, '')
        }
        this.fileList = [
          {
            name: file.file.name,
            url: completeResponse.data.url
          }
        ]
        this.form.fileUrl = completeResponse.data.url
        this.form.fileName = file.file.name
        this.fileLoading = false
        this.$message.success('上传成功')
      } catch (error) {
        console.error('上传失败:', error)
        this.fileLoading = false
        this.fileList = []
        this.$message.error('上传失败')
      }
    },
    close() {
      this.$emit('close')
    },
    submit() {
      let username = this.type === 'add' ? 'createName' : 'updateName'
      let fn = this.type === 'add' ? 'addFile' : 'updateFile'
      this.form[username] = this.$store.state.user.userInfo.user.staffName
      this.form.fileParentMenuId = this.filePosition.join(',')
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.form.viewPerm == 1) {
            this.form.downloadPerm = '1'
          }
          if (this.form.viewPerm == 0) {
            this.form.viewUserIds = []
          }
          if (this.form.downloadPerm == 0) {
            this.form.downloadUserIds = []
          }
          let arr = [...this.form.viewUserIds, ...this.form.downloadUserIds]
          this.form.viewUserIds = Array.from(new Set(arr))
          if (this.form.viewPerm == 1) {
            this.form.downloadPerm = '1'
          }
          // 增加按钮loading
          this.btnLoading = true
          this.$api[fn](this.form)
            .then((res) => {
              if (res.code === '200') {
                this.$message({
                  message: '保存成功',
                  type: 'success'
                })
                this.close()
                this.$emit('success', true)
              }
            })
            .finally(() => {
              this.btnLoading = false
            })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.form-data {
  padding: 0 36px;
}
::v-deep .interDialogDiv {
  background: #fff;
  border-radius: 4px;
  .el-dialog__header {
    height: 56px;
    padding: 15px 20px;
    background: #fff;
    border-bottom: 1px solid $color-text-secondary;
    box-shadow: 0 0 12px 3px rgb(0 0 0 / 10%);
    .el-dialog__title,
    .dialog-title {
      font-size: 18px;
      font-family: 'PingFang SC-Medium', 'PingFang SC';
      font-weight: 500;
      color: $color-text;
    }
    .el-dialog__close {
      color: $color-text;
      font-weight: 600;
      font-size: 18px;
    }
  }
  .el-dialog__body {
    padding: 15px;
    display: flex;
    .dialog-content {
      flex: 1;
      .el-row {
        height: 100%;
      }
    }
  }
  .el-dialog__footer {
    max-height: 56px;
    padding: 10px 20px;
    background: #fff;
    box-shadow: 0 0 12px 3px rgb(0 0 0 / 10%);
  }
}
.interDialogDiv {
  .sapce_content {
    margin: 0 auto;
    background: #fff;
    padding: 10px 0;
    border-radius: 4px;
    height: 400px;
    display: flex;
    width: 100%;
    .left {
      text-align: center;
      width: 300px;
      height: 100%;
      background-color: #fff;
      border-radius: 5px;
      overflow: hidden;
    }
    .center {
      width: 300px;
      border-left: 1px solid #dcdfe6;
      background-color: #fff;
      height: 100%;
      margin-left: 10px;
      border-radius: 5px;
      padding-left: 16px;
      overflow: scroll;
      .personCheck {
        width: 100%;
        display: flex;
        padding: 10px 0;
        img {
          vertical-align: middle;
        }
      }
    }
    .right {
      width: calc(100% - 620px);
      border-left: 1px solid #dcdfe6;
      background-color: #fff;
      height: 100%;
      margin-left: 10px;
      border-radius: 5px;
      padding-left: 16px;
      overflow: scroll;
      .top {
        display: flex;
        padding: 0 12px;
        justify-content: space-between;
        .label,
        .dept {
          font-size: 14px;
          color: #7f848c;
        }
        .num {
          font-size: 14px;
          color: #333333;
          margin-left: 10px;
        }
        .dept {
          margin-left: 10px;
        }
        .clear {
          font-size: 12px;
          color: #3562db;
          cursor: pointer;
        }
      }
      .item-list {
        display: flex;
        cursor: pointer;
        padding: 0 12px;
        justify-content: space-between;
        margin-top: 8px;
        .remove {
          margin: auto 0;
        }
      }
      .item-list:hover {
        background: #e6effc;
      }
    }
    .info {
      margin-left: 8px;
      .name {
        font-weight: 500;
        color: #333333;
      }
      .mobile {
        font-size: 12px;
        color: #7f848c;
      }
    }
  }
}
.sino_tree_box {
  margin-top: 10px;
  height: calc(100% - 40px);
  overflow: auto;
  .custom-tree-node-label {
    display: inline-block;
    width: 200px;
    white-space: nowrap;
    /* 防止换行 */
    overflow: hidden;
    /* 隐藏超出部分 */
    text-overflow: ellipsis;
    text-align: left;
  }
}
::v-deep .el-tree .el-tree-node__content {
  height: 36px;
  line-height: 16px;
  padding: 0;
}
::v-deep .el-tree-node {
  .is-leaf + .el-checkbox .el-checkbox__inner {
    display: inline-block;
  }
  .el-checkbox .el-checkbox__inner {
    display: none;
  }
}
::v-deep .el-collapse-item__content {
  padding-bottom: 0px !important;
}
::v-deep .el-checkbox__input {
  display: inline-block !important;
  margin-bottom: 26px !important;
}
.upload-progress {
  margin-top: 10px;
}
</style>
