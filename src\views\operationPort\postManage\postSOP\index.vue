<template>
  <PageContainer class="positionManage-list">
    <template #content>
      <div class="post-sop-container">
        <div class="filter-container">
          <el-form ref="queryForm" :inline="true" :model="queryParams" class="filter-form">
            <el-form-item label="" prop="name">
              <el-input v-model="queryParams.queryKey" placeholder="请输入SOP名称/标题" clearable @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
              <el-button icon="el-icon-refresh" plain type="primary" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="operate-container">
          <el-button type="primary" icon="el-icon-plus" @click="handleAdd">新增</el-button>
        </div>
        <el-table v-loading="loading" :data="sopList" height="calc(100% - 156px)" row-key="id" table-layout="auto" border stripe @selection-change="handleSelectionChange">
          <el-table-column label="序号" align="center" type="index" width="50" />
          <el-table-column label="SOP名称" align="center" prop="sopName" show-overflow-tooltip />
          <el-table-column label="标题" align="center" prop="sopTitle" show-overflow-tooltip />
          <el-table-column label="应用岗位" align="center" prop="postName" show-overflow-tooltip />
          <el-table-column label="应用值班岗" align="center" prop="dutyPostName" show-overflow-tooltip />
          <el-table-column label="备注" align="center" prop="remark" width="180" show-overflow-tooltip />
          <el-table-column label="状态" align="center" prop="status">
            <template slot-scope="{ row }">
              <div :style="{ color: row.status === 1 ? '#95f204' : '#d9001b' }">
                {{ row.status === 1 ? '启用' : '停用' }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="{ row }">
              <el-button size="mini" type="text" @click="handleView(row)">查看</el-button>
              <el-button size="mini" type="text" @click="handleUpdate(row)">编辑</el-button>
              <el-dropdown trigger="hover" @command="(command) => handleCommand(command, row)">
                <span class="el-dropdown-link"> 更多<i class="el-icon-arrow-down el-icon--right"></i> </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item :command="row.status === 1 ? 'disable' : 'enable'">{{ row.status === 1 ? '停用' : '启用' }}</el-dropdown-item>
                  <el-dropdown-item command="delete">删除</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :layout="pagination.layoutOptions"
          :current-page="pagination.current"
          :page-sizes="pagination.pageSizeOptions"
          :page-size="pagination.size"
          :total="pagination.total"
          @size-change="paginationSizeChange"
          @current-change="paginationCurrentChange"
        ></el-pagination>
      </div>
    </template>
  </PageContainer>
</template>
<script>
export default {
  name: 'PostSOP',
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // SOP列表
      sopList: [],
      // 状态数据字典
      statusOptions: [
        {
          value: '1',
          label: '启用'
        },
        {
          value: '0',
          label: '停用'
        }
      ],
      // 查询参数
      queryParams: {
        queryKey: ''
      },
      // 分页参数
      pagination: {
        current: 1,
        size: 15,
        pageSizeOptions: [15, 20, 50, 100],
        layoutOptions: 'total, sizes, prev, pager, next, jumper',
        total: 0
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 获取SOP列表
    getList() {
      this.loading = true
      const params = {
        page: this.pagination.current,
        pageSize: this.pagination.size,
        queryKey: this.queryParams.queryKey || ''
      }
      // 此处应调用API获取数据
      this.$api.supplierAssess
        .querySupPostSopByPage(params)
        .then((response) => {
          const { code, data, msg } = response
          if (code === '200') {
            const { records, total } = data
            this.sopList = records
            this.pagination.total = total
          } else {
            this.$message.error(msg)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 表单重置
    resetQuery() {
      this.queryParams.queryKey = ''
      this.handleQuery()
    },
    // 搜索按钮操作
    handleQuery() {
      this.pagination.current = 1
      this.getList()
    },
    // 分页大小变化
    paginationSizeChange(val) {
      this.pagination.size = val
      this.getList()
    },
    // 当前页变化
    paginationCurrentChange(val) {
      this.pagination.current = val
      this.getList()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    // 新增按钮操作
    handleAdd() {
      this.$router.push('/postManage/postSOP/addPostSOP')
    },
    // 修改按钮操作
    handleUpdate(row) {
      this.$router.push({
        path: '/postManage/postSOP/addPostSOP',
        query: { id: row.id, type: 'edit' }
      })
    },
    // 查看详情
    handleView(row) {
      this.$router.push({
        path: '/postManage/postSOP/postSOPDetail',
        query: { id: row.id }
      })
    },
    // 删除按钮操作
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$confirm('删除后将无法恢复,是否删除?', '删除岗位SOP', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // 调用删除接口
          this.$api.supplierAssess
            .deleteSupPostSopById({ id: ids })
            .then((response) => {
              const { code, msg } = response
              if (code === '200') {
                this.$message.success('删除成功')
                this.getList()
              } else {
                this.$message.error(msg || '删除失败')
              }
            })
            .catch(() => {
              this.$message.error('删除失败')
            })
        })
        .catch(() => {})
    },
    handleCommand(command, row) {
      // 删除
      if (command === 'delete') {
        this.handleDelete(row)
        return
      }
      // 启用/停用
      const params = {
        id: row.id,
        status: command === 'disable' ? '0' : command === 'enable' ? '1' : ''
      }
      // 停用前需要确认
      if (command === 'disable') {
        this.$confirm('正在使用的SOP将无法继续显示，是否停用?', '停用岗位SOP', {
          confirmButtonText: '停用',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            // 用户确认后执行停用
            this.executeStatusChange(params)
          })
          .catch(() => {
            // 用户取消操作，不执行任何操作
          })
      } else {
        // 其他操作（如启用）直接执行
        this.executeStatusChange(params)
      }
    },
    // 执行状态变更
    executeStatusChange(params) {
      this.$api.supplierAssess
        .disOrUse(params)
        .then((response) => {
          const { code, msg } = response
          if (code === '200') {
            this.$message.success(msg)
            this.getList()
          } else {
            this.$message.error(msg || '操作失败')
          }
        })
        .catch(() => {
          this.$message.error('操作失败')
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.positionManage-list {
  height: 100%;
  background-color: #fff;
  padding: 15px;
  .post-sop-container {
    height: 100%;
    .operate-container {
      margin-bottom: 22px;
    }
    .el-dropdown-link {
      cursor: pointer;
      color: #3562db;
      margin-left: 10px;
    }
  }
}
</style>
