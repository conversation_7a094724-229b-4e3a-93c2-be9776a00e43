import storage from '@/util/storage'
import { treeToList } from '@/util'
import api from '../../api'
import crypto from '@/util/crypto'
const state = {
  token: storage.session.get('token') || '',
  picPrefix: storage.session.get('picPrefix') || '',
  userInfo: JSON.parse(storage.session.get('userInfo')) || {},
  permissions: [],
  ipsmUserInfo: JSON.parse(storage.session.get('ipsmUserInfo')) || {}
}
const getters = {
  isLogin: (state) => {
    let retn = false
    if (state.token) {
      retn = true
    }
    return retn
  },
  userId: (state) => {
    return state.userInfo.userId
  },
  userName: (state) => {
    return state.userInfo.user?.staffName ?? state.userInfo.username
  }
}
const actions = {
  login({ commit }, data) {
    return new Promise((resolve, reject) => {
      const params = {
        username: data.account,
        // password: data.password,
        password: crypto.set(data.password),
        platform: '1' // 双预防专用字段 为1代表pc端
      }
      api
        .loginSystem(params)
        .then((res) => {
          if (res.code === '200') {
            if (res.data.user && res.data.token) {
              commit('setUserData', res.data)
              resolve()
            } else {
              reject({
                msg: '无法获取用户信息'
              })
            }
          } else {
            reject(res)
          }
        })
        .catch((error) => {
          reject(error)
        })
    })
  },
  logout({ commit }) {
    return new Promise((resolve, reject) => {
      api
        .logoutSystem()
        .then((res) => {
          if (res.code === '200') {
            sessionStorage.removeItem('LOGINDATA')
            commit('removeUserData')
            commit('menu/invalidRoutes', null, { root: true })
            commit('tabbar/clean', null, { root: true })
            resolve()
          } else {
            reject()
          }
        })
        .catch((error) => {
          reject(error)
        })
    })
  },
  // 获取我的权限
  getPermissions({ state, commit }) {
    return new Promise((resolve) => {
      const params = {
        userId: state.userInfo.userId,
        state: 0
      }
      api.getMenuTreeData(params)
        .then((res) => {
          // 验证是否有安全模块权限
          if (!state.ipsmUserInfo.id) {
            res.data.forEach(i => {
              if (i.pathUrl == '/securityCenter') {
                i.children = []
              }
            })
          } else {
            sessionStorage.setItem('LOGINDATA', JSON.stringify(state.ipsmUserInfo))
          }
          const menuList = treeToList(res.data) ?? []
          commit('setPermissions', menuList)
          resolve(menuList)
        })
    })
  }
}
const mutations = {
  setUserData(state, data) {
    storage.session.set('token', data.token)
    storage.session.set('picPrefix', data.picPrefix || '')
    storage.session.set('userInfo', JSON.stringify(data.user))
    storage.session.set('ipsmUserInfo', JSON.stringify(data.ipsmUser || {}))
    state.userInfo = data.user
    state.picPrefix = data.picPrefix || ''
    state.ipsmUserInfo = data.ipsmUser || {}
    state.token = data.token
  },
  removeUserData(state) {
    storage.session.remove('token')
    storage.session.remove('userInfo')
    storage.session.remove('ipsmUserInfo')
    state.userInfo = {}
    state.ipsmUserInfo = {}
    state.token = ''
  },
  setPermissions(state, permissions) {
    state.permissions = permissions
  }
}
export default {
  namespaced: true,
  state,
  actions,
  getters,
  mutations
}
