<template>
  <meta2d-vue-preview ref="meta2dVuePreview" :queryData="$route.query" :userInfo="userInfo" :baseUrl="baseUrl" />
</template>
<script>
export default {
  name: 'preview',
  data() {
    return {
      baseUrl: __PATH.VUE_MONITOR_API,
      userInfo: {
        avatarUrl: '',
        captcha: '',
        createdAt: '',
        deletedAt: '',
        email: '',
        id: '1',
        phone: '',
        updatedAt: '',
        username: 'admin',
        vip: 1,
        vipExpiry: '',
        isVip: true // 该属性是计算出来的，不是数据库中的
      }
    }
  },
  created() {
    // 网关需要token 组件获取token通过localStorage
    window.localStorage.setItem('token', 'Bearer ' + this.$store.state.user.token)
  },
  mounted() {
    console.log(this.$refs.meta2dVuePreview)
  }
}
</script>
<style lang="scss" scoped></style>
