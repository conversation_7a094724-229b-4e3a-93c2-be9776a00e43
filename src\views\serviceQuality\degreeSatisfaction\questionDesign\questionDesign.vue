<template>
  <div class="main-container">
    <div class="whole">
      <div class="page-title">问卷题目</div>
      <el-row style="border-bottom: 1px solid #d8dee7;">
        <el-col :md="16" :lg="16" :xl="14" style="height: auto;">
          <BreadcrumbNavBar />
        </el-col>
        <el-col :md="8" :lg="8" :xl="10">
          <HeaderButton />
        </el-col>
      </el-row>
      <div class="question-item-container">
        <div v-for="item in questionArray" :key="item.status" class="questionItem">
          <el-button type="primary" :icon="item.icon" @click="stateJudgment(item)">{{ item.questionName }}</el-button>
        </div>
      </div>
      <div class="question-content">
        <div v-for="(qSubject, index) in arr" :key="index" class="preview-container">
          <PreviewCards v-if="flag" :currentPreviewSubject="qSubject">
            <component
              :is="questionPreview[qSubject.type]"
              :previewOption="qSubject"
              :index="qSubject.indexCopy"
              :pathName="pathName"
              :diff="diff"
              :isQuestionNum="questionPreviewList.isQuestionNum == 1"
            ></component>
          </PreviewCards>
        </div>
      </div>
      <QuestionDialog :visible.sync="isDialogVisible">
        <template slot="question-dialog">
          <component v-bind:is="whichComponent" :questionSubject="questionSubject"></component>
        </template>
      </QuestionDialog>
    </div>
  </div>
</template>

<script>
import notice from '../utils/notice'
import utils from '../utils/utils'
import QuestionDialog from '../QuestionDialog/QuestionDialogs.vue'
import RadioQuestion from '../RadioQuestion/RadioQuestion.vue'
import CheckBoxQuestion from '../CheckBoxQuestion/CheckBoxQuestion'
import InputQuestion from '../InputQuestion/InputQuestion'
import SelectQuestion from '../SelectQuestion/SelectQuestion'
import BreadcrumbNavBar from '../component/BreadcrumbNavBar/BreadcrumbNavBar.vue'
import HeaderButton from './component/HeaderButton/HeaderButton.vue'
import PreviewCards from '../component/PreviewCards/PreviewCard.vue'
import PreviewRadio from '../questionDesign/component/QuestionPreview/PreviewRadio/PreviewRadio.vue'
import PreviewCheckBox from '../questionDesign/component/QuestionPreview/PreviewCheckBox/PreviewCheckBox.vue'
import PreviewFillBlank from '../questionDesign/component/QuestionPreview/PreviewFillBlank/PreviewFillBlank.vue'
import PreviewSelect from '../questionDesign/component/QuestionPreview/PreviewSelect/PreviewSelect.vue'
export default {
  components: {
    QuestionDialog,
    RadioQuestion,
    CheckBoxQuestion,
    InputQuestion,
    SelectQuestion,
    BreadcrumbNavBar,
    HeaderButton,
    PreviewCards,
    PreviewRadio,
    PreviewCheckBox,
    PreviewFillBlank,
    PreviewSelect
  },
  data() {
    return {
      checkConfig: [
        {
          key: 'statusRun',
          value: '0',
          title: '暂停'
        },
        {
          key: 'status',
          value: 'publish',
          title: '收集'
        },
        {
          key: 'status',
          value: 'recovery',
          title: '完成'
        }
      ],
      arr: [],
      diff: 'title',
      flag: false,
      questionPreviewList: {}, // 当前问卷的题目列表
      isDialogVisible: false,
      whichComponent: '',
      questionSubject: {},
      pathName: {
        check: {
          isShowSubjectType: false,
          isDisable: true,
          isQuestionNum: true,
          isSetDefaultValue: true
        },
        preview: {
          isShowSubjectType: true,
          isDisable: false,
          isQuestionNum: true,
          isSetDefaultValue: false
        },
        title: {
          isShowSubjectType: true,
          isDisable: false,
          isQuestionNum: true,
          isSetDefaultValue: false
        }
      },
      questionPreview: {
        radio: 'PreviewRadio',
        checkbox: 'PreviewCheckBox',
        input: 'PreviewFillBlank',
        // array: 'PreviewMatrix',
        // paragraph: 'PreviewParagraph',
        // sort: 'PreviewSort',
        select: 'PreviewSelect'
        // nd_select: 'PreviewMulSelect'
      },
      questionArray: [
        {
          icon: 'el-icon-burger',
          status: 'radio',
          questionName: '单选',
          component: 'RadioQuestion',
          previewComponent: 'PreviewRadio'
        },
        {
          icon: 'el-icon-burger',
          status: 'checkbox',
          questionName: '多选',
          component: 'CheckBoxQuestion',
          previewComponent: 'PreviewCheckBox'
        },
        {
          icon: 'el-icon-burger',
          status: 'input',
          questionName: '填空',
          component: 'InputQuestion',
          previewComponent: 'PreviewFillBlank'
        },
        // {
        //   icon: 'el-icon-burger',
        //   status: 'array',
        //   questionName: '矩阵',
        //   component: 'MatrixQuestion',
        //   previewComponent: 'PreviewMatrix'
        // },
        // {
        //   icon: 'el-icon-burger',
        //   status: 'paragraph',
        //   questionName: '段落',
        //   component: 'ParagraphQuestion',
        //   previewComponent: 'PreviewParagraph'
        // },
        // {
        //   icon: 'el-icon-burger',
        //   status: 'sort',
        //   questionName: '排序',
        //   component: 'SortQuestion',
        //   previewComponent: 'PreviewSort'
        // },
        {
          icon: 'el-icon-burger',
          status: 'select',
          questionName: '下拉菜单',
          component: 'SelectQuestion',
          previewComponent: 'PreviewSelect'
        }
        // {
        //   icon: 'el-icon-burger',
        //   status: 'nd_select',
        //   questionName: '二级下拉菜单',
        //   component: 'MulSelectQuestion',
        //   previewComponent: 'PreviewMulSelect'
        // }
      ]
    }
  },
  destroyed() {
    notice.$off('openPopup')
    notice.$off('showQuestionDialog')
    notice.$off('getCurrentQuestionAllSubject')
  },
  created() {
    this.getPaperQuestions()
  },
  destroyed() {
    notice.$off('openPopup')
    notice.$off('showQuestionDialog')
    notice.$off('getCurrentQuestionAllSubject')
  },
  mounted() {
    // this.questionData = utils.getLocalStorage('localData', '')
    // if (!this.questionData.id) {
    //   return
    // }
    // this.getCurrentQuestionAllSubject(this.questionData.id)
    // notice.$on('getCurrentQuestionAllSubject', () => {
    //   this.getCurrentQuestionAllSubject(utils.getLocalStorage('localData', 'id'))
    // })
    notice.$on('showQuestionDialog', (selectedPreviewSubject) => {
      console.log('擦拭的奥尔格哈瑟瑞吧风格', selectedPreviewSubject)

      this.handleQuestionDialogShow(selectedPreviewSubject)
    })
    this.registerAssembly()
    notice.$on('cleanOpenPopup', () => {
      notice.$off('openPopup')
      this.registerAssembly()
    })
  },
  methods: {
    handleQuestionDialogShow(selectedPreviewSubject) {
      console.log('执行了')
      const item = this.questionArray.filter((item) => item.status === selectedPreviewSubject.type)
      this.questionSubject = selectedPreviewSubject
      console.log(this.questionSubject, 'this.questionSubject')
      this.isDialogVisible = !this.isDialogVisible
      this.whichComponent = item[0].component
    },
    getPaperQuestions() {
      let params = {
        questionId: localStorage.getItem('questId'),
        userId: this.$store.state.user.userInfo.userId
      }
      this.$api.getPaperQuestions(params).then((res) => {
        if (res.status == 200) {
          this.flag = false
          this.arr = res.data.questions
          let questionLength = JSON.stringify(this.arr.length)
          localStorage.setItem('questionLength', questionLength)
          var indexCount = 0
          for (let index = 0; index < this.arr.length; index++) {
            const element = this.arr[index]
            if (element.type == 'paragraph') {
              indexCount = indexCount + 1
            } else {
              element['indexCopy'] = index - indexCount
            }
          }

          var that = this
          this.$nextTick(function () {
            that.flag = true
          })
          this.questionPreviewList = res.data
        }
      })
    },
    registerAssembly() {
      notice.$on('openPopup', (openPopup) => {
        // 监听弹出层打开  页面逻辑
        notice.$emit('openPopupDialog', openPopup, this.questionPreviewList.questions) // 把数据抛给 弹出层组件
      })
    },
    stateJudgment(item) {
      var obj = null
      var objQuestion = utils.getLocalStorage('localData', '')
      console.log(objQuestion, 'objQuestion')
      for (var a = 0; a < this.checkConfig.length; a++) {
        if (objQuestion[this.checkConfig[a].key] == this.checkConfig[a].value) {
          obj = this.checkConfig[a]
          break
        }
      }
      if (obj) {
        this.$alert(`问卷处于${obj.title}状态，不能添加题型!`, '提示', {
          confirmButtonText: '确定',
          type: 'warning',
          callback: (action) => {}
        })
        return
      }

      this.handleQuestionSubjectClick(item)
    },
    handleQuestionSubjectClick(item) {
      this.questionSubject = {}
      this.isDialogVisible = !this.isDialogVisible
      this.whichComponent = item.component
    }
  }
}
</script>

<style lang="scss" scoped>
.question-item-container {
  width: 450px;
  display: flex;
  // margin-left: 30px;
  margin: 5px auto;
  justify-content: space-around;

  .questionItem {
    .el-button {
      display: flex;
      align-items: center;
      height: 40px;
    }
  }
}

.question-main-container {
  background-color: #f5f7fa;
  height: 100%;

  .el-row,
  .el-col {
    height: 100%;
  }

  .question-content {
    background-color: #fff;
    // height: 100%;
    height: calc(100% - 320px);
    overflow-y: auto;
    padding: 0 20px 20px;
  }

  .preview-container {
    padding: 10px;
    background-color: #fff;
  }
}

::v-deep .page-container .container-content {
  background-color: #fff !important;
}

.main-container {
  height: 100%;
  padding: 15px;
  margin: 0 !important;

  .whole {
    width: 100%;
    height: 100%;
    border-radius: 5px;
    background-color: #fff !important;

    .page-title {
      height: 50px;
      line-height: 50px;
      color: #606266;
      font-size: 14px;
      padding-left: 25px;
      border-bottom: 1px solid #d8dee7;
    }

    .el-button--text {
      color: #fff;
      padding: 12px 10px;
    }

    .el-button--text:hover,
    .el-button:hover {
      color: #fff;
      background-color: #5188fc;
      border-radius: 0;
      border-color: transparent;
    }
  }
}
// @media screen and (max-width: 1366px) {
//   .main-container .el-button--text {
//     color: #fff;
//     padding: 0 15px;
//   }
// }
.question-content {
  margin-top: 10px;
  background-color: #fff;
  // height: 100%;
  height: calc(100% - 180px);
  overflow-y: auto;
  padding: 0 20px 20px;
}
</style>
