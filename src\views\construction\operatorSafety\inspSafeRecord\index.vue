<template>
  <div v-loading="exportLoading" style="height: 100%">
    <div class="special_box">
      <div class="content_box">
        <div class="top_content">
          <el-input v-model="searchForm.workName" style="width: 200px; margin-right: 16px" placeholder="作业名称" maxlength="25" @keyup.enter.native="search"></el-input>
          <el-select v-model="searchForm.deptId" filterable style="width: 180px; margin-right: 16px" placeholder="巡检部门" clearable>
            <el-option v-for="item in deptList" :key="item.id" :label="item.deptName" :value="item.id"></el-option>
          </el-select>
          <el-input v-model="searchForm.personName" style="width: 200px; margin-right: 16px" placeholder="巡检人员" maxlength="25"></el-input>
          <el-date-picker
            v-model="date"
            type="datetimerange"
            start-placeholder="开始日期"
            range-separator="至"
            end-placeholder="结束日期"
            :default-time="['00:00:00', '23:59:59']"
            clearable
          >
          </el-date-picker>
          <div style="margin-left: 10px">
            <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db" @click="reset">重置</el-button>
            <el-button type="primary" style="font-size: 14px" @click="search">查询</el-button>
          </div>
        </div>
        <div class="top_content">
          <!-- <el-button slot="reference" type="primary">导出</el-button> -->
        </div>
        <div class="table_list">
          <el-table
            ref="materialTable"
            v-loading="tableLoading"
            :data="tableData"
            height="100%"
            border
            :header-cell-style="{ background: '#F6F5FA' }"
            style="width: 100%"
            :cell-style="{ padding: '8px 0 8px 0' }"
            stripe
            highlight-current-row
            :empty-text="emptyText"
            row-key="id"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column label="序号" type="index" width="50">
              <template slot-scope="scope">
                <span>{{ (paginationData.currentPage - 1) * paginationData.size + scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip label="作业名称" prop="workName"> </el-table-column>
            <el-table-column show-overflow-tooltip label="作业类型" prop="workTypeName"> </el-table-column>
            <el-table-column show-overflow-tooltip label="施工地点" prop="workLocationName"></el-table-column>
            <el-table-column show-overflow-tooltip label="周期类型" prop="cycleType">
              <template slot-scope="scope">
                <span>{{ cycleTypeList.find((i) => i.cycleType == scope.row.cycleType).label }}</span>
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip label="频次" prop="frequency"></el-table-column>
            <el-table-column show-overflow-tooltip label="巡检部门" prop="actualExecutionDeptName"></el-table-column>
            <el-table-column show-overflow-tooltip label="巡检人员" prop="actualExecutionUserName"></el-table-column>
            <el-table-column show-overflow-tooltip label="巡检模板" prop="templateName"></el-table-column>
            <el-table-column show-overflow-tooltip label="应巡" prop="shouldMaintainNum"></el-table-column>
            <el-table-column show-overflow-tooltip label="已巡" prop="alreadyMaintainNum"></el-table-column>
            <el-table-column show-overflow-tooltip label="完成状态" prop="planStatus">
              <template slot-scope="scope">
                <span>{{ scope.row.planStatus == '0' ? '未完成' : '已完成' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="view(scope.row)">查看</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="bottom_content">
          <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="paginationData.currentPage"
            :page-sizes="[15, 30, 50, 100]"
            :page-size="paginationData.size"
            :total="paginationData.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import moment from 'moment'
export default {
  name: 'inspSafeRecord',
  data() {
    return {
      moment,
      date: [],
      paginationData: {
        currentPage: 1,
        size: 15,
        total: 1
      },
      deptList: [],
      // 周期类型
      cycleTypeList: [
        {
          cycleType: 8,
          label: '单次'
        },
        {
          cycleType: 6,
          label: '每日'
        },
        {
          cycleType: 0,
          label: '每周'
        },
        {
          cycleType: 2,
          label: '每月'
        },
        {
          cycleType: 3,
          label: '季度'
        },
        {
          cycleType: 5,
          label: '全年'
        }
      ], // 周期类型
      tableData: [],
      // 列表过滤条件
      searchForm: {
        workName: '',
        deptId: '',
        personName: '',
        startTime: '',
        endTime: ''
      },
      emptyText: '暂无数据',
      tableLoading: false,
      // 导出loading
      exportLoading: false
    }
  },
  mounted() {
    this.getDeptList()
    this.getList()
  },
  methods: {
    // 条件查询
    search() {
      this.paginationData.currentPage = 1
      this.getList()
    },
    // 重置查询条件
    reset() {
      this.paginationData.currentPage = 1
      this.date = []
      this.searchForm = {
        workName: '',
        deptId: '',
        personName: '',
        startTime: '',
        endTime: '',
        taskStatus: '1'
      }
      this.getList()
    },
    getDeptList() {
      this.$api.getDeptList().then((res) => {
        if (res.code == 200) {
          this.deptList = res.data
        }
      })
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.getList()
    },
    // 每页的条数
    handleSizeChange(val) {
      this.paginationData.size = val
      this.paginationData.currentPage = 1
      this.getList()
    },
    // 列表操作
    // 导出
    exportExcel() {},
    // 列表
    getList() {
      this.searchForm.startTime = this.date[0] ? moment(this.date[0]).format('YYYY-MM-DD HH:mm:ss') : ''
      this.searchForm.endTime = this.date[1] ? moment(this.date[1]).format('YYYY-MM-DD HH:mm:ss') : ''
      const params = {
        ...this.searchForm,
        ...this.paginationData
      }
      this.tableLoading = true
      this.$api
        .maintainRecordList(params)
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.list
            this.paginationData.total = res.data.count
          }
        })
        .finally(() => (this.tableLoading = false))
    },
    view(row) {
      this.$router.push({
        // name: 'maintainRecordDetail',
        // query: {
        //   id: row.id
        // }
        name: 'inspSafeRecordPoint',
        query: {
          id: row.id
        }
      })
    },
    dwonLoadFile(name, url) {
      fetch(url).then((res) =>
        res.blob().then((blob) => {
          var a = document.createElement('a')
          var url = window.URL.createObjectURL(blob)
          a.href = url
          a.download = name
          a.click()
          window.URL.revokeObjectURL(url)
        })
      )
    }
  }
}
</script>
<style lang="scss" scoped>
.special_box {
  height: 100%;
  padding: 16px;
  .content_box {
    width: 100%;
    height: 100%;
    border-radius: 5px;
    padding: 16px;
    background: #fff;
    .top_content {
      width: 100%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin-bottom: 16px;
    }
    .table_list {
      height: calc(100% - 96px - 10px);
      .disable {
        color: #414653;
        span {
          display: inline-block;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: #ccced3;
        }
      }
      .enable {
        color: #08cb83;
        span {
          display: inline-block;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: #08cb83;
        }
      }
      .acceptanceText {
        cursor: pointer;
      }
      .acceptanceColor0 {
        color: #414653;
      }
      .acceptanceColor1 {
        color: #08cb83;
      }
      .acceptanceColor2 {
        color: #f56c6c;
      }
    }
    .bottom_content {
      margin-top: 10px;
      text-align: right;
    }
  }
}
</style>
<style lang="scss">
.messageIndex {
  z-index: 3000 !important;
}
</style>
