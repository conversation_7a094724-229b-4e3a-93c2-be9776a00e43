<template>
  <el-dialog v-if="visible" v-dialogDrag title="添加成员" width="50%" :visible.sync="dialogShow" custom-class="model-dialog" :before-close="closeDialog">
    <div class="sapce_content">
      <div class="left">
        <div v-loading="treeLoading" class="sino_tree_box">
          <div class="sino_tree_box" style="margin: 0">
            <el-tree
              ref="tree"
              class="filter-tree"
              :data="treePostData"
              :props="defaultProps"
              node-key="dutyPostCode"
              highlight-current
              default-expand-all
              :expand-on-click-node="false"
              @node-click="nodePostClick"
            ></el-tree>
          </div>
        </div>
      </div>
      <div class="center">
        <el-input v-model.trim="userInfo" placeholder="搜索姓名、手机号、部门" clearable suffix-icon="el-icon-search" @input="nameInput"></el-input>
        <el-checkbox v-if="checkType == 'checkbox' && staffData.length > 0" v-model="allChecked" :indeterminate="isIndeterminate" class="selectAll" @change="checkAllChange"
          >全选</el-checkbox
        >
        <el-checkbox-group v-model="staffSelect" @change="changegroup">
          <el-checkbox v-for="item in staffData" :key="item.staffId" :label="item.staffId" style="display: block" @change="(val) => checkboxchange(item, val)">
            <div class="personCheck">
              <img src="@/assets/images/avatar.png" />
              <div class="info">
                <div class="name">{{ item.staffName }}</div>
                <div class="mobile">{{ item.phone }}</div>
              </div>
            </div>
          </el-checkbox>
        </el-checkbox-group>
      </div>
      <div class="right">
        <div class="top">
          <span>
            <span class="label">已选:</span>
            <span class="num">{{ selectData.length ? selectData.length : 0 }}</span>
            <span class="dept">名用户</span>
          </span>
          <span class="clear" @click="clear">清空</span>
        </div>
        <div v-for="(item, index) in selectData" :key="index" class="item-list">
          <div style="display: flex">
            <img src="@/assets/images/avatar.png" />
            <div class="info">
              <div class="name">{{ item.staffName }}</div>
              <div class="mobile">{{ item.phone }}</div>
            </div>
          </div>
          <div class="remove" @click="remove(item, index)"><i class="el-icon-close"></i></div>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="submit">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  name: 'shiftPostPeopleDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    /*
      是否为单选
      checkbox 多选
      radio 单选
    */
    checkType: {
      type: String,
      default: 'checkbox'
    },
    // 是否过滤值班岗的数据
    filterShiftPost: {
      type: String,
      default: ''
    },
    defaultChecked: {
      type: Array,
      default: () => []
    },
    // 添加已选人员属性，接收完整的已选人员对象列表
    defaultSelectedPeople: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      keyWord: '', // 编码/名称/通用名/规格型号/SN
      treePostData: [],
      defaultProps: {
        label: 'dutyPostName',
        children: 'child'
      },
      treeLoading: false,
      selectData: [], // 已选人员列表
      staffData: [], // 人员列表
      staffSelect: [], // 已选人员ID列表
      userInfo: '',
      isIndeterminate: false,
      allChecked: false, // 全选
      checkedData: {} // 选中数据
    }
  },
  computed: {
    dialogShow() {
      return this.visible
    }
  },
  mounted() {
    this.getTreeData()
    // 初始化时从defaultSelectedPeople恢复已选人员
    if (this.defaultSelectedPeople && this.defaultSelectedPeople.length) {
      this.selectData = [...this.defaultSelectedPeople]
      this.staffSelect = this.defaultSelectedPeople.map((item) => item.staffId)
    }
  },
  methods: {
    // 获取tree数据
    getTreeData() {
      this.treeLoading = true
      this.$api.supplierAssess.getDutyPostData().then((res) => {
        this.treeLoading = false
        if (res.code == '200' && res.data.length > 0) {
          if (Object.keys(this.filterShiftPost)) {
            let arr = this.filterShiftPost.split(',')
            this.treePostData = res.data.filter((e) => arr.includes(e.dutyPostCode))
          } else {
            this.treePostData = res.data
          }
          // 有值班岗就查询，没有则不查询
          if (this.treePostData.length) {
            this.checkedData = this.treePostData[0]
            this.$nextTick(() => {
              this.$refs.tree.setCurrentKey(this.checkedData.dutyPostCode)
            })
            this.getDataList('init')
          }
        }
      })
    },
    // 全选改变
    checkAllChange(val) {
      this.isIndeterminate = false
      if (val) {
        // 获取当前所有可选人员ID
        const newStaffIds = this.staffData.map((item) => item.staffId)
        // 过滤出未选中的人员
        const newStaffToAdd = this.staffData.filter((item) => !this.staffSelect.includes(item.staffId))
        // 将新的ID添加到已选ID中
        this.staffSelect = [...new Set([...this.staffSelect, ...newStaffIds])]
        // 将新的人员添加到已选人员中
        this.selectData = [...this.selectData, ...newStaffToAdd]
      } else {
        // 取消选中当前显示的所有人员
        const currentStaffIds = this.staffData.map((item) => item.staffId)
        this.staffSelect = this.staffSelect.filter((id) => !currentStaffIds.includes(id))
        this.selectData = this.selectData.filter((item) => !currentStaffIds.includes(item.staffId))
      }
    },
    nameInput() {
      // 有值班岗就查询，没有则不查询
      if (this.checkedData.dutyPostCode) {
        // 保存当前选中状态
        const currentSelected = [...this.staffSelect]
        this.getDataList()
        // 查询后恢复选中状态
        this.$nextTick(() => {
          this.staffSelect = currentSelected
        })
      }
    },
    // 人员获取数据
    getDataList(type) {
      let data = {
        page: 1,
        pageSize: 999,
        name: this.userInfo,
        postCode: this.checkedData.dutyPostCode
      }
      this.$api.supplierAssess.queryPersonByDutyPostByPage(data).then((res) => {
        this.tableLoading = false
        if (res.code === '200') {
          this.staffData = res.data.records
          // 仅在初始化且没有传入defaultSelectedPeople时使用defaultChecked
          if (this.defaultChecked.length && type == 'init' && !this.selectData.length) {
            this.staffSelect = this.defaultChecked
            this.selectData = this.staffData.filter((item) => {
              return this.defaultChecked.includes(item.staffId)
            })
          }
          // 设置当前页面中已选人员的勾选状态
          this.allChecked = this.staffData.length > 0 && this.staffData.every((item) => this.staffSelect.includes(item.staffId))
          this.isIndeterminate = !this.allChecked && this.staffData.some((item) => this.staffSelect.includes(item.staffId))
        }
      })
    },
    // 控制单选
    changegroup(list) {
      if (this.checkType == 'radio') {
        this.staffSelect = [list[list.length - 1]]
        this.selectData = this.staffData.filter((el) => el.staffId == this.staffSelect[0])
      } else {
        this.allChecked = list.length == this.staffData.length
        this.isIndeterminate = list.length > 0 && list.length < this.staffData.length
      }
    },
    // 选择
    checkboxchange(item, val) {
      if (!val) {
        this.selectData = this.selectData.filter((el) => {
          return el.staffId !== item.staffId
        })
      } else {
        if (this.selectData.includes(item)) {
          return
        } else {
          this.selectData.push(item)
        }
      }
    },
    // 岗位点击
    nodePostClick(val) {
      this.checkedData = val
      this.getDataList()
      // 不重置已选状态，只更新当前页面的勾选状态
      this.$nextTick(() => {
        // 检查当前岗位下是否有已选人员
        const hasSelected = this.staffData.some((item) => this.staffSelect.includes(item.staffId))
        this.allChecked = this.staffData.length > 0 && this.staffData.every((item) => this.staffSelect.includes(item.staffId))
        this.isIndeterminate = !this.allChecked && hasSelected
      })
    },
    // 移除
    remove(list, index) {
      // 从已选列表中移除
      this.selectData.splice(index, 1)
      // 从已选ID中移除
      this.staffSelect = this.staffSelect.filter((id) => id !== list.staffId)
      // 更新当前页面的全选状态
      if (this.staffData.some((item) => item.staffId === list.staffId)) {
        this.allChecked = false
        this.isIndeterminate = this.staffData.some((item) => this.staffSelect.includes(item.staffId))
      }
    },
    // 清空
    clear() {
      this.selectData = []
      this.staffSelect = []
      this.$nextTick(() => {
        this.$refs.tree.setCheckedKeys([])
      })
    },
    closeDialog() {
      this.$emit('closeDialog')
    },
    submit() {
      if (this.selectData.length < 1) {
        this.$message({
          message: '请选择至少一条数据',
          type: 'warning'
        })
      } else {
        this.$emit('submitDialog', this.selectData)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.model-dialog {
  .sapce_content {
    margin: 0 auto;
    background: #fff;
    padding: 10px 0;
    border-radius: 4px;
    height: 400px;
    display: flex;
    width: 100%;
    .left {
      text-align: center;
      width: 300px;
      height: 100%;
      background-color: #fff;
      border-radius: 5px;
      overflow: hidden;
    }
    .center {
      width: 300px;
      border-left: 1px solid #dcdfe6;
      background-color: #fff;
      height: 100%;
      margin-left: 10px;
      border-radius: 5px;
      padding-left: 16px;
      overflow: scroll;
      // display: flex;
      .personCheck {
        width: 100%;
        display: flex;
        padding: 10px 0;
        img {
          vertical-align: middle;
        }
      }
    }
    .right {
      width: calc(100% - 620px);
      border-left: 1px solid #dcdfe6;
      background-color: #fff;
      height: 100%;
      margin-left: 10px;
      border-radius: 5px;
      padding-left: 16px;
      overflow: scroll;
      .top {
        display: flex;
        padding: 0 12px;
        justify-content: space-between;
        .label,
        .dept {
          font-size: 14px;
          color: #7f848c;
        }
        .num {
          font-size: 14px;
          color: #333333;
          margin-left: 10px;
        }
        .dept {
          margin-left: 10px;
        }
        .clear {
          font-size: 12px;
          color: #3562db;
          cursor: pointer;
        }
      }
      .item-list {
        display: flex;
        cursor: pointer;
        padding: 0 12px;
        justify-content: space-between;
        margin-top: 8px;
        .remove {
          margin: auto 0;
        }
      }
      .item-list:hover {
        background: #e6effc;
      }
    }
    .info {
      margin-left: 8px;
      .name {
        font-weight: 500;
        color: #333333;
      }
      .mobile {
        font-size: 12px;
        color: #7f848c;
      }
    }
  }
}
.sino_tree_box {
  margin-top: 10px;
  height: calc(100% - 40px);
  overflow: auto;
  .custom-tree-node-label {
    display: inline-block;
    width: 200px;
    white-space: nowrap; /* 防止换行 */
    overflow: hidden; /* 隐藏超出部分 */
    text-overflow: ellipsis;
    text-align: left;
  }
}
::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: #d9e1f8;
}
::v-deep .el-tree .el-tree-node__content {
  height: 36px;
  line-height: 16px;
  padding: 0;
}
::v-deep .el-tree-node {
  .is-leaf + .el-checkbox .el-checkbox__inner {
    display: inline-block;
  }
  .el-checkbox .el-checkbox__inner {
    display: none;
  }
}
::v-deep .el-collapse-item__content {
  padding-bottom: 0px !important;
}
::v-deep .el-checkbox__input {
  display: inline-block !important;
  margin-bottom: 26px !important;
}
.selectAll {
  margin: 10px 0;
}
::v-deep.selectAll .el-checkbox__input {
  margin-bottom: 0 !important;
}
::v-deep .selectAll .el-checkbox__label {
  padding-left: 20px;
}
</style>
