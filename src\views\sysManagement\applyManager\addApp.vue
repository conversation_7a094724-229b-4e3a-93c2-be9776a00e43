<template>
  <PageContainer :footer="true">
    <div slot="content" v-loading="pageLoading" class="addApp-content">
      <el-form ref="formInline" :model="formInline" :rules="rules" :disabled="$route.query.type == 'detail'">
        <ContentCard title="新增应用">
          <div slot="content" style="overflow: hidden">
            <el-row :gutter="24">
              <el-col :md="7">
                <el-form-item label="应用类型" prop="type" label-width="100px">
                  <el-select
                    v-model="formInline.type"
                    placeholder="请选择应用类型"
                    style="width: 100%"
                    @change="
                      () => {
                        formInline.path = ''
                        formInline.menuId = ''
                      }
                    "
                  >
                    <el-option label="子系统" :value="1"></el-option>
                    <el-option label="子模块" :value="2"></el-option>
                    <el-option label="外部应用" :value="3"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :md="7">
                <el-form-item label="应用简称" prop="shortName" label-width="100px">
                  <el-input v-model="formInline.shortName" placeholder="请输入应用简称" />
                </el-form-item>
              </el-col>
              <el-col :md="7">
                <el-form-item label="应用全称" prop="fullName" label-width="100px">
                  <el-input v-model="formInline.fullName" placeholder="请输入应用全称"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :md="14">
                <el-form-item label="应用地址" prop="path" label-width="100px">
                  <el-input v-if="formInline.type != 2" v-model="formInline.path" placeholder="请输入应用地址" />
                  <el-select v-else ref="treeSelect" v-model="formInline.path" placeholder="请选择应用地址" style="width: 100%">
                    <el-option hidden :value="formInline.path" :label="formInline.path"> </el-option>
                    <el-tree :data="menuTreeData" :props="menuSelectProps" @node-click="selectMenuInfo"> </el-tree>
                  </el-select>
                  <!-- <SelectTree v-model="formInline.path" :data="menuTreeData" :props="menuSelectProps" @getName="selectMenuInfo" /> -->
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :md="14">
                <el-form-item label="应用图标" prop="staffName" label-width="100px">
                  <span style="font-size: 10px; color: #7f848c">支持{{ uploadAcceptDict['picture'].type }},且大小不超过{{ uploadAcceptDict['picture'].fileSize }}M</span>
                  <div style="display: flex">
                    <el-upload
                      action=""
                      :class="{ hide: iconFileList.length }"
                      list-type="picture-card"
                      :file-list="iconFileList"
                      :accept="uploadAcceptDict['picture'].type"
                      :limit="1"
                      :before-upload="beforeAvatarUpload"
                      :http-request="(file) => httpRequset(file, 'default')"
                      :on-remove="(file, fileList) => handleRemove(file, fileList, 'default')"
                      :on-change="(file, fileList) => fileChange(file, fileList, 'default')"
                      :disabled="$route.query.type == 'detail'"
                    >
                      <i class="el-icon-circle-plus-outline" style="color: #3562db"
                        ><br /><span style="font-size: 10px; color: #7f848c">应用默认图标</span><span style="font-size: 10px; color: #7f848c">(20*20)</span></i
                      >
                    </el-upload>
                    <el-upload
                      action=""
                      :class="{ hide: activeIconFileList.length }"
                      style="margin-left: 16px"
                      list-type="picture-card"
                      :file-list="activeIconFileList"
                      :accept="uploadAcceptDict['picture'].type"
                      :limit="1"
                      :before-upload="beforeAvatarUpload"
                      :http-request="(file) => httpRequset(file, 'active')"
                      :on-remove="(file, fileList) => handleRemove(file, fileList, 'active')"
                      :on-change="(file, fileList) => fileChange(file, fileList, 'active')"
                      :disabled="$route.query.type == 'detail'"
                    >
                      <i class="el-icon-circle-plus-outline" style="color: #3562db"
                        ><br /><span style="font-size: 10px; color: #7f848c">应用选中图标</span><span style="font-size: 10px; color: #7f848c">(32*32)</span></i
                      >
                    </el-upload>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :md="10">
                <el-form-item label="简介" prop="remark" label-width="100px">
                  <el-input v-model="formInline.remark" placeholder="请输入简介" type="textarea" maxlength="200" :autosize="{ minRows: 4 }" show-word-limit></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </ContentCard>
      </el-form>
    </div>
    <div slot="footer">
      <el-button
        type="primary"
        plain
        @click="
          () => {
            $router.go(-1)
          }
        "
        >关闭</el-button
      >
      <el-button type="primary" :disabled="$route.query.type == 'detail'" @click="submitForm('formInline')">保存</el-button>
    </div>
  </PageContainer>
</template>
<script>
import { uploadAcceptDict } from '@/util/dict.js'
export default {
  name: 'addApp',
  // 路由前置守卫动态修改meta中的title
  beforeRouteEnter(to, from, next) {
    if (to.meta.title) {
      const typeList = {
        add: '新增应用',
        edit: '编辑应用',
        detail: '应用详情'
      }
      to.meta.title = typeList[to.query.type] ?? '应用详情'
    }
    next()
  },
  async beforeRouteLeave(to, from, next) {
    if (!['ApplyManager'].includes(to.name)) {
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      uploadAcceptDict,
      pageLoading: false,
      iconFileList: [],
      activeIconFileList: [],
      formInline: {
        type: '', // 应用类型
        shortName: '', // 应用简称
        fullName: '', // 应用全称
        path: '', // 应用地址
        remark: '', // 简介
        menuId: '', // 菜单id
        icon: {
          defaultIcon: '',
          activeIcon: ''
        } // 图标
      },
      menuTreeData: [],
      rules: {
        type: [{ required: true, message: '请选择应用类型', trigger: ['blur', 'change'] }],
        shortName: [{ required: true, message: '请输入应用简称', trigger: ['blur', 'change'] }],
        path: [{ required: true, message: '请输入应用地址', trigger: ['blur', 'change'] }]
      },
      menuSelectProps: {
        label: 'menuName',
        children: 'children'
      }
    }
  },
  activated() {
    this.init()
  },
  created() {
    if (!this.$store.state.keepAlive.list.includes('ApplyManager')) {
      this.init()
    }
  },
  methods: {
    init() {
      this.getAllMenuList()
      if (this.$route.query.type != 'add') {
        this.getAppInfo()
      }
    },
    // 选择菜单
    selectMenuInfo(data) {
      if (!data.children.length) {
        this.formInline.path = data.pathUrl
        this.formInline.menuId = data.menuId
        this.$refs.treeSelect.blur()
      }
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.formInline.icon = JSON.stringify(this.formInline.icon)
          let headerParams = {}
          if (this.formInline.id) {
            headerParams = {
              'operation-type': 2,
              'operation-id': this.formInline.id,
              'operation-name': this.formInline.shortName
            }
          } else {
            headerParams = {
              'operation-type': 1
            }
          }
          this.$api.AddOrUpdateApp(this.formInline, headerParams).then((res) => {
            if (res.code == 200) {
              this.$message({ message: '提交成功', type: 'success' })
              this.$router.go(-1)
            } else {
              this.$message({ message: res.msg, type: 'error' })
            }
          })
        }
      })
    },
    // 获取全部菜单
    getAllMenuList() {
      this.$api.GetAllMenuList().then((res) => {
        if (res.code == 200) {
          this.menuTreeData = res.data
        }
      })
    },
    // 获取应用详情
    getAppInfo() {
      this.pageLoading = true
      this.$api
        .GetAppInfo({ appId: this.$route.query.id })
        .then((res) => {
          this.pageLoading = false
          if (res.code == 200) {
            let { type, shortName, fullName, path, remark, menuId, icon, id } = res.data
            let icons = JSON.parse(icon)
            this.formInline = {
              type,
              shortName,
              fullName,
              path,
              remark,
              menuId,
              id,
              icon: icons
            }
            this.iconFileList = icons.defaultIcon ? [{ url: this.$tools.imgUrlTranslation(icons.defaultIcon) }] : []
            this.activeIconFileList = icons.activeIcon ? [{ url: this.$tools.imgUrlTranslation(icons.activeIcon) }] : []
            console.log(this.iconFileList, this.activeIconFileList)
          }
        })
        .catch(() => {
          this.pageLoading = false
        })
      console.log('应用信息', this.formInline)
    },
    fileChange(file, fileList, type) {
      this.fileList = fileList
      if (type == 'default') {
        this.iconFileList = fileList
      } else {
        this.activeIconFileList = fileList
      }
    },
    handleRemove(file, fileList, type) {
      if (type == 'default') {
        this.formInline.icon.defaultIcon = ''
        this.iconFileList = []
      } else {
        this.formInline.icon.activeIcon = ''
        this.activeIconFileList = []
      }
    },
    httpRequset(file, type) {
      const params = new FormData()
      params.append('file', file.file)
      this.$api.uploadIcon(params).then((res) => {
        if (res.code == 200) {
          if (type == 'default') {
            this.formInline.icon.defaultIcon = res.data
          } else {
            this.formInline.icon.activeIcon = res.data
          }
          this.$message({
            message: '上传成功',
            type: 'success'
          })
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },
    beforeAvatarUpload(file) {
      const extension = file.name.substring(file.name.lastIndexOf('.'))
      const isFormat = this.uploadAcceptDict['picture'].type.includes(extension)
      if (!isFormat) {
        this.$message.error(`上传图片只能是 ${this.uploadAcceptDict['picture'].type}格式!`)
        return false
      }
      const fileSize = this.uploadAcceptDict['picture'].fileSize
      const isLt2M = file.size / 1024 / 1024 < fileSize
      if (!isLt2M) {
        this.$message.error(`上传文件大小不能超过 ${fileSize}MB!`)
        return false
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.addApp-content {
  background: #fff;
  border-radius: 4px;
  height: 100%;
  padding: 10px;
  overflow-y: auto;
  ::v-deep .el-upload {
    width: 130px;
    height: 130px;
  }
  ::v-deep .el-upload-list {
    .el-upload-list__item {
      width: 130px;
      height: 130px;
    }
  }
  .hide {
    ::v-deep .el-upload--picture-card {
      display: none;
    }
  }
}
</style>
