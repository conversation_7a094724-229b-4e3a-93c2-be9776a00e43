<template>
  <!-- 医用气体充装记录 -->
  <PageContainer>
    <div slot="header" class="control-btn-header">
      <el-date-picker
        v-model="filters.daterange"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        :picker-options="pickerOptions"
      >
      </el-date-picker>
      <el-input v-model.trim="filters.surveyName" clearable placeholder="设备名称" style="width: 200px;"></el-input>
      <el-input v-model.trim="filters.company" clearable placeholder="公司" style="width: 200px;"></el-input>
      <div style="display: inline-block;">
        <el-button type="primary" plain @click="resetData">重置</el-button>
        <el-button type="primary" @click="searchClick">查询</el-button>
        <!-- <el-button type="primary" @click="exportFillingRecord">导出</el-button> -->
      </div>
    </div>
    <div slot="content" class="table-content">
      <TablePage
        ref="table"
        v-loading="tableLoading"
        :showPage="true"
        :tableColumn="tableColumn"
        :data="tableData"
        height="calc(100% - 40px)"
        :pageData="paginationData"
        @pagination="paginationChange"
        @row-dblclick="tableRowClick($event)"
      />
      <fillingRecordEditDialog
        ref="fillingRecordEdit"
        :visible.sync="dialogVisibleFillingRecord"
        :itemData="selectItem"
      />
    </div>
  </PageContainer>
</template>

<script lang="jsx">
import fillingRecordEditDialog from './components/fillingRecordEditDialog'
export default {
  name: 'filledRecord',
  components: {
    fillingRecordEditDialog
  },
  data() {
    return {
      requestHttp: __PATH.VUE_IEMC_API,
      paginationData: {
        page: 1,
        pageSize: 15,
        total: 0
      },
      tableData: [],
      tableLoading: false,
      filters: {
        surveyName: '', // 人员姓名
        daterange: [],
        company: '' // 公司
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now() + 1000 * 60 * 60
        }
      },
      selectItem: {},
      dialogVisibleFillingRecord: false,
      tableColumn: [
        {
          width: 50,
          prop: '',
          label: '序号',
          formatter: (scope) => {
            return (this.paginationData.page - 1) * this.paginationData.pageSize + scope.$index + 1
          }
        },
        {
          prop: 'fillingDate',
          label: '充装时间'
        },
        {
          prop: 'endDate',
          label: '充装结束时间'
        },
        {
          prop: 'surveyName',
          label: '设备名称'
        },
        {
          prop: 'beforeValue',
          label: '充装前重量(t)'
        },
        {
          prop: 'currentValue',
          label: '充装后重量(t)'
        },
        {
          prop: 'fillingValue',
          label: '本次充装(t)'
        },
        {
          prop: 'company',
          label: '公司'
        },
        {
          prop: 'fillingPerson',
          label: '充装人'
        },
        {
          prop: 'recipient',
          label: '接收人'
        },
        {
          width: 70,
          prop: 'operation',
          label: '操作',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span class="operationBtn-span" style="color: #fa403c" onClick={() => this.deleteFilling(row.row)}>删除</span>
              </div>
            )
          }
        }
      ]
    }
  },
  computed: {},
  watch: {
    dialogVisibleFillingRecord(val) {
      if (!val) {
        this.getFillingList()
      }
    }
  },
  created() {
    this.getFillingList()
  },
  methods: {
    deleteFilling(row) {
      this.$confirm('是否确定删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$api.DeleteFilling({}, this.requestHttp, { id }).then((res) => {
            if (res.code == 200) {
              this.$message({ message: '删除充装记录成功', type: 'success'})
              this.getFillingList()
            } else {
              this.$message.error(res.message)
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    exportFillingRecord() {
      let data = {
        pageSize: this.paginationData.pageSize,
        page: this.paginationData.page,
        surveyName: this.filters.surveyName,
        startDate: this.filters.daterange ? this.filters.daterange[0] : '',
        endDate: this.filters.daterange ? this.filters.daterange[1] : '',
        company: this.filters.company
      }
      this.$api.ExportFillingRecord(data, this.requestHttp).then((res) => {
        this.$tools.downloadFile(res, this)
      })
    },
    tableRowClick(row) {
      this.selectItem = row
      this.dialogVisibleFillingRecord = true
    },
    // 查询
    searchClick() {
      this.paginationData.page = 1
      this.getFillingList()
    },
    // 获取列表
    getFillingList() {
      let data = {
        pageSize: this.paginationData.pageSize,
        page: this.paginationData.page,
        surveyName: this.filters.surveyName,
        startDate: this.filters.daterange ? this.filters.daterange[0] : '',
        endDate: this.filters.daterange ? this.filters.daterange[1] : '',
        company: this.filters.company
      }
      this.tableLoading = true
      this.$api.getFillingList(data, this.requestHttp).then((res) => {
        this.tableLoading = false
        if (res.code == 200) {
          this.tableData = res.data.list
          this.paginationData.total = parseInt(res.data.totalCount)
        } else if (res.message) {
          this.$message.error(res.message)
        }
      })
    },
    resetData() {
      this.filters.surveyName = ''
      this.filters.daterange = []
      this.paginationData.page = 1
      this.getFillingList()
    },
    paginationChange(pagination) {
      this.paginationData.page = pagination.page
      this.paginationData.pageSize = pagination.pageSize
      this.getFillingList()
    },
    closeDialogFillingRecord() {
      this.dialogVisibleFillingRecord = false
    },
    sureFillingRecord() {
      this.dialogVisibleFillingRecord = false
      this.getFillingList()
    }
  }
}
</script>

<style lang="scss" scoped>
.control-btn-header {
  padding: 6px 6px 16px 16px;

  & > div {
    margin-right: 10px;
    margin-top: 10px;
  }
}

.table-content {
  margin-top: 15px;
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 15px);
  padding: 10px;
}
</style>
