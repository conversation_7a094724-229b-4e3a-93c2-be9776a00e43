<template>
  <div style="width: 100%; height: 100%;">
    <div class="control-btn-header">
      <div style="display: flex; padding: 10px 5px;">
        <el-input v-model="questionCode" placeholder="请输入编号" class="ipt"></el-input>
        <el-select v-model="flowCode" placeholder="请选择处理状态" class="ipt">
          <el-option v-for="item in statusData" :key="item.flowCode" :label="item.name" :value="item.flowCode"></el-option>
        </el-select>
        <el-button type="primary" @click="inquiry">查询</el-button>
        <el-button type="primary" plain @click="reset">重置</el-button>
      </div>
    </div>
    <div style="overflow: auto; height: 100%;">
      <div class="content" style="margin-top: 8px;">
        <!-- <el-row :gutter="16">
          <el-col :xs="24" :md="24" :lg="12" style="padding: 10px 5px 10px 0;">
            <div class="col" style="height: 100%;">
              <div class="title">
                <svg-icon name="right-arrow" />
                <span>隐患状态分析</span>
              </div>
              <div id="orderTypeCharts"></div>
            </div>
          </el-col>
          <el-col :xs="24" :md="24" :lg="12" style="padding: 10px 0 10px 5px;">
            <div class="col">
              <div class="title">
                <svg-icon name="right-arrow" />
                <span>隐患类型分析</span>
              </div>
              <div id="powerMonthTypeNoEchart"></div>
            </div>
          </el-col>
        </el-row> -->
        <!-- <el-row> -->
        <!-- <el-col :xs="24" :md="24" :lg="12" style="width: 100%;"> -->
        <div class="col" style="height: 100%; width: 100%;">
          <div class="contentTable">
            <div class="contentTable-main table-content">
              <el-table :data="tableData" :height="tableHeight" stripe border>

                <el-table-column type="index" label="序号" width="50" align="center">
                  <template slot-scope="scope">
                    <span>{{ (pagination.current - 1) * pagination.size + scope.$index + 1 }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="编号" prop="" width="150px">
                  <template slot-scope="scope">
                    <span class="color_blue" @click="ViewFn(scope.row)">
                      {{ scope.row.questionCode }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column label="状态" prop="flowType" width="80"></el-table-column>
                <el-table-column label="隐患分类" prop="questionDetailType" show-overflow-tooltip></el-table-column>
                <el-table-column label="隐患区域" prop="questionAddress" show-overflow-tooltip></el-table-column>
                <el-table-column label="反馈部门" prop="createByDeptName" show-overflow-tooltip></el-table-column>
                <el-table-column label="反馈人" prop="createPersonName"></el-table-column>
                <el-table-column label="反馈时间" prop="createTime" show-overflow-tooltip></el-table-column>
                <el-table-column label="隐患等级" prop="riskName" show-overflow-tooltip></el-table-column>
                <el-table-column label="要求整改完成时间" prop="rectificationPlanTime" show-overflow-tooltip></el-table-column>
              </el-table>
            </div>
          </div>
          <div class="contentTable-footer">
            <el-pagination
              style="margin-top: 3px;"
              :current-page="pagination.current"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              :page-size="pagination.size"
              :page-sizes="[15, 30, 50]"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            ></el-pagination>
          </div>
        </div>
        <!-- </el-col>
        </el-row> -->
      </div>
    </div>
  </div>
</template>

<script>
import tableListMixin from '@/mixins/tableListMixin.js'

import store from '@/store/index'
import * as echarts from 'echarts'
export default {
  mixins: [tableListMixin],

  props: {
    placeIds: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      questionCode: '',
      flowCode: '',
      statusData: [
        {
          flowCode: '1',
          name: '待整改'
        },
        {
          flowCode: '2',
          name: '已整改'
        },
        {
          flowCode: '3',
          name: '已挂账'
        },
        {
          flowCode: '4',
          name: '已完结'
        }
      ],
      orderStatisticsName: [],
      orderType: 'year',
      seriesData: [],
      xAxisData: [],
      tableData: [],
      pagination: {
        current: 1,
        size: 15
      },
      total: 0
    }
  },
  // computed: {
  //   tableHeight() {
  //     return document.body.clientHeight - 630
  //   }
  // },
  created() {
    // this.getStateStatistics()
    // this.getHiddenDangerTypeAnalysis()
    this.getTableList()
  },
  methods: {
    inquiry() {
      // this.getStateStatistics()
      // this.getHiddenDangerTypeAnalysis()
      this.getTableList()
    },
    reset() {
      this.flowCode = ''
      this.questionCode = ''
      // this.getStateStatistics()
      // this.getHiddenDangerTypeAnalysis()
      this.getTableList()
    },
    ViewFn(row) {
      this.$router.push({
        path: '/Details',
        query: { id: row.id }
      })
    },
    getTableList() {
      let params = {
        pageNo: this.pagination.current,
        pageSize: this.pagination.size,
        placeIds: this.placeIds,
        flowCode: this.flowCode,
        questionCode: this.questionCode
      }
      this.$api.selectHiddenDangerList(params).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.list
          this.total = res.data.sum
        }
      })
    },
    getStateStatistics() {
      let params = {
        placeIds: this.placeIds,
        flowCode: this.flowCode,
        questionCode: this.questionCode
      }
      this.$api.getStateStatistics(params).then((res) => {
        if (res.code == '200') {
          this.orderStatisticsName = res.data.array
          this.initOrderTypeCharts()
        }
      })
    },
    initOrderTypeCharts() {
      let arr = []
      this.orderStatisticsName.forEach((item) => {
        let obj = {
          name: item.name,
          value: item.value,
          rate: item.rate
        }
        arr.push(obj)
      })
      const nameList = Array.from(arr, (item) => item.name)
      const getchart = echarts.init(document.getElementById('orderTypeCharts'))
      const option = {
        tooltip: {
          trigger: 'item',
          axisPointer: {
            lineStyle: {
              color: '#FFE3A6'
            }
          },
          backgroundColor: '#fff',
          borderColor: '#fff',
          textStyle: {
            color: '#000'
          },
          formatter: function (name) {
            console.log(name, '111')

            var oa = option.series[0].data
            for (var i = 0; i < option.series[0].data.length; i++) {
              if (name.name === oa[i].name) {
                return ' ' + name.name + '(' + oa[i].value + '件' + ')' + oa[i].rate
              }
            }
          }
        },
        legend: {
          orient: 'vertical',
          type: 'scroll',
          right: 30,
          top: 60,
          bottom: 20,
          itemWidth: 15,
          itemHeight: 15,
          itemGap: 15,
          data: nameList,
          formatter: function (name) {
            console.log(name, '222')
            var oa = option.series[0].data
            var num = oa.reduce((sum, e) => sum + e.value, 0)
            for (var i = 0; i < option.series[0].data.length; i++) {
              if (name === oa[i].name) {
                return ' ' + name + '(' + oa[i].value + '件' + ')' + '    ' + '(' + oa[i].rate + ')'
              }
            }
          }
        },
        series: [
          {
            type: 'pie',
            roseType: 'radius',
            radius: ['45%', '75%'],
            center: ['35%', '45%'],
            data: arr,
            label: {
              normal: {
                show: false
              }
            }
          }
        ]
      }
      getchart.on('click', (params) => {
        // 此处的value值为饼状图里 data的name 值
        var value = params.name
        this.statusData.forEach((item) => {
          if (value == item.name) {
            this.flowCode = item.flowCode
          }
        })
        this.getTableList()
      })

      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    getHiddenDangerTypeAnalysis() {
      let params = {
        placeIds: this.placeIds,
        flowCode: this.flowCode,
        questionCode: this.questionCode
      }
      this.$api.getHiddenDangerTypeAnalysis(params).then((res) => {
        if (res.code == '200') {
          this.seriesData = res.data.seriesData
          this.xAxisData = res.data.xAxisData
          this.getPowerMonthTypeNoEchart()
        }
      })
    },
    getPowerMonthTypeNoEchart() {
      const getchart = echarts.init(document.getElementById('powerMonthTypeNoEchart'))
      let arr = []
      this.xAxisData.forEach((item) => {
        let obj = {
          name: item
        }
        arr.push(obj)
      })
      this.seriesData.forEach((item, i) => {
        arr.forEach((item2, n) => {
          if (i == n) {
            item2.value = item
          }
        })
      })
      const data = arr
      data.sort((a, b) => {
        return a.value - b.value
      })
      const nameList = Array.from(data, ({ name }) => name)
      const option = {
        tooltip: {
          trigger: 'item',
          position: 'left'
        },
        grid: {
          top: '4%',
          left: '4%',
          right: '5%',
          bottom: '5%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: nameList,
          axisLabel: {
            formatter: function (val) {
              var strs = val.split('') // 字符串数组
              var str = ''
              // strs循环 forEach 每五个字符增加换行符
              strs.forEach((item, index) => {
                if (index % 6 === 0 && index !== 0) {
                  str += '\n'
                }
                str += item
              })
              return str
            }
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        yAxis: [
          {
            type: 'value'
          }
        ],
        series: [
          {
            type: 'bar',
            barWidth: 10,
            data: data,
            itemStyle: {
              color: '#3562db'
            }
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    handleSizeChange(val) {
      this.pagination.size = val
      this.getTableList()
    },
    handleCurrentChange(val) {
      this.pagination.current = val
      this.getTableList()
    }
  }
}
</script>

<style lang="scss" scoped>
.control-btn-header {
  background-color: #fff;

  & > div {
    display: flex;
    padding: 10px 5px;
  }
}

.content {
  height: 91%;

  ::v-deep .el-row {
    height: 100%;

    .el-col {
      height: 100%;
    }
  }
}

.ipt {
  width: 200px;
  margin-right: 10px;
}

.col {
  background-color: #fff;
  height: 100%;
  border-radius: 5px;
  padding: 10px;
}

#orderTypeCharts,
#powerMonthTypeNoEchart {
  width: 100%;
  height: 95%;
}

.title span {
  font-size: 15px;
}

.color_blue {
  color: #5188fc;
  cursor: pointer;
}

.contentTable {
  height: calc(100% - 30px);
  background: #fff;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  overflow: auto;

  .contentTable-main {
    flex: 1;
    overflow: auto;
  }

  .contentTable-footer {
    padding: 10px 0 0;
  }

  .alarmLevel {
    padding: 3px 6px;
    border-radius: 4px;
    color: #fff;
    line-height: 14px;
  }

  .alarmStatus {
    position: relative;
    display: inline-block;
    padding-left: 12px;

    .alarmStatusIcon {
      display: inline-block;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 8px;
      height: 8px;
      border-radius: 100%;
    }
  }

  .collectIcon {
    font-size: 16px;
    margin-right: 4px;
  }
}
</style>
