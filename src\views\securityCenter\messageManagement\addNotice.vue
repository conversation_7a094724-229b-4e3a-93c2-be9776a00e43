<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="content_box">
        <el-form ref="formInline" :model="formInline" class="form-inline" label-width="110px" :rules="rules">
          <el-form-item label="公告名称：" prop="title">
            <el-input v-model.trim="formInline.title" style="width: 450px" placeholder="请控制标题于30字内" show-word-limit :maxlength="30" :disabled="disabled"></el-input>
          </el-form-item>
          <br />
          <el-form-item label="公告类别：" prop="typeCode">
            <el-select v-model="formInline.typeCode" :disabled="disabled" placeholder="请选择公告类别" style="width: 210px">
              <el-option
                v-for="(item, index) in noticeType"
                :key="index"
                :label="item.typeName"
                :value="item.typeCode"
                :disabled="disabled"
                @click.native="workcode(item)"
              ></el-option>
            </el-select>
            <el-select
              v-if="formInline.typeCode && formInline.typeCode != 0"
              ref="issueScopeIds"
              v-model="formInline.issueScopeIds"
              v-loading="selectLoading"
              multiple
              prop="issueScopeIds"
              filterable
              collapse-tags
              :style="{ width: '210px', marginLeft: '30px' }"
              placeholder="可多选"
              :disabled="disabled"
              @change="handleChange"
            >
              <el-option v-for="item in issueScopeList" :key="item.value" :label="item.text" :value="item.id" :disabled="disabled"></el-option>
            </el-select>
          </el-form-item>
          <br />
          <el-form-item label="公告概述：" prop="overview">
            <el-input
              v-model="formInline.overview"
              type="textarea"
              :rows="3"
              style="width: 450px"
              class="project-textarea"
              placeholder="请输入公告概述"
              show-word-limit
              :disabled="disabled"
              maxlength="100"
            ></el-input>
          </el-form-item>
          <el-form-item label="公告内容：" prop="text" class="is-required">
            <div style="height: calc(300px + 50px)">
              <Editor ref="myTextEditor" v-model="editorContent" class="my-editor"></Editor>
            </div>
          </el-form-item>
          <br />
          <el-form-item label="定时发布时间：">
            <el-date-picker
              v-model="formInline.timingTime"
              popper-class="no-atTheMoment"
              type="datetime"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              :picker-options="pickerOptions"
              placeholder="至少大于当前时间五分钟"
              :disabled="disabled"
              @change="selectTime"
            ></el-date-picker>
          </el-form-item>
        </el-form>
      </div>
      <el-dialog class title="通知公告" :visible.sync="dialogVisibleRole" custom-class="model-dialog" @close="close">
        <div v-loading class="dialog-contnet">
          <div class="caption">
            <div class="caption-text">
              <span>公告类别：</span>
              <span>{{ typeName }}</span>
            </div>
            <div class="caption-text">
              <span>{{ $tools.getCurrentDate() }}</span>
            </div>
          </div>
          <div class="preview ql-snow" style>
            <div class="ql-editor" v-html="editorContent"></div>
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="close">关闭</el-button>
        </span>
      </el-dialog>
    </div>
    <div slot="footer">
      <el-button type="primary" @click="createNotice(0)">存草稿</el-button>
      <el-button type="primary" @click="createNotice(1)">发布</el-button>
      <el-button type="primary" @click="preview">预览</el-button>
      <el-button type="primary" @click="cancelAdd">取消</el-button>
    </div>
  </PageContainer>
</template>

<script>
import Editor from '@/components/quillEditor'
export default {
  name: 'addNotice',
  components: {
    // eslint-disable-next-line vue/no-unused-components
    Editor
  },
  beforeRouteEnter(to, from, next) {
    if (to.meta.title) {
      to.meta.title = to.query.id ? '修改公告' : '新增公告'
    }
    next()
  },
  data() {
    return {
      editorContent: '',
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7
        }
      },
      formInline: {
        title: '',
        typeCode: '',
        overview: '',
        formInline: '',
        issueScopeIds: '',
        issueScopeNames: ''
      },
      typeName: '',
      rules: {
        title: [{ required: true, message: '请输入公告名称', trigger: 'change' }],
        typeCode: [{ required: true, message: '请选择公告类别', trigger: 'change' }],
        overview: [{ required: true, message: '请选择公告概述', trigger: 'change' }],
        issueScopeIds: [{ required: true, message: '请选择公告范围', trigger: 'change' }]
      },
      noticeType: [],
      leibie: '',
      typeArr: [],
      typeTreeData: [],
      propsType: {
        children: 'children',
        label: 'typeName',
        value: 'typeId',
        checkStrictly: true
      },
      dialogVisibleRole: false,
      issueScopeList: [],
      disabled: false,
      contentLoading: false,
      selectLoading: false
    }
  },
  created() {
    this.getNoticeType()
  },
  mounted() {
    if (this.$route.query.id) {
      this.getNoticeDetails(this.$route.query.id)
    }
  },
  methods: {
    // 获取公告详情
    getNoticeDetails(id) {
      this.contentLoading = true
      this.$api
        .ipsmGetNoticeDetails({
          noticeId: id
        })
        .then((res) => {
          if (res.code == 200) {
            this.formInline = res.data
            this.editorContent = res.data.content
            this.$refs.myTextEditor.content = this.editorContent
            let ids = res.data.issueScopeIds.split(',')
            this.$api
              .ipsmGetNoticeScopeByType({
                typeCode: res.data.typeCode
              })
              .then((ret) => {
                this.issueScopeList = ret.data
                this.formInline.issueScopeIds = ids
              })
          } else {
            this.$message.error(res.message)
          }
        })
      this.contentLoading = false
    },
    /**
     * 获取公告类别
     */
    getNoticeType() {
      this.$api.ipsmGetNoticeType({}).then((res) => {
        if (res.code == 200) {
          this.noticeType = res.data
        } else if (res.code == '502') {
          this.$message.error(res.message)
        }
      })
    },
    // 选择公告范围
    handleChange(value) {
      let a = []
      for (let i of value) {
        this.issueScopeList.map((v) => {
          if (i == v.id) {
            return a.push(v.text)
          }
        })
      }
      this.formInline.issueScopeNames = a
    },
    // 根据公告类别获取范围
    workcode(item) {
      this.selectLoading = true
      this.formInline.issueScopeIds = ''
      this.typeName = item.typeName
      this.$api
        .ipsmGetNoticeScopeByType({
          typeCode: item.typeCode
        })
        .then((res) => {
          if (res.code == 200) {
            this.issueScopeList = res.data
          } else {
            this.$message.error(res.message)
          }
          this.selectLoading = false
        })
    },
    // 新建公告
    createNotice(i) {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          if (this.formInline.typeCode != 0 && this.formInline.issueScopeIds.length == 0) {
            this.$message.error('请选择公告区域')
            return
          }
          if (this.editorContent == '') {
            this.$message.error('请输入公告内容')
            return
          }
          let data = {
            title: this.formInline.title,
            typeCode: this.formInline.typeCode,
            issueScopeIds: this.formInline.issueScopeIds.toString(),
            overview: this.formInline.overview,
            content: this.editorContent,
            timingTime: this.formInline.timingTime,
            status: i,
            noticeId: ''
          }
          if (this.$route.query.id) {
            data.noticeId = this.$route.query.id
            this.$api.ipsmUpdateNotice(data).then((res) => {
              if (res.code == 200) {
                this.$message.success(res.message)
                this.$router.go(-1)
              } else {
                this.$message.error(res.message)
              }
            })
          } else {
            delete data.noticeId
            this.$api.ipsmCreateNotice(data).then((res) => {
              if (res.code == 200) {
                this.$message.success(res.message)
                this.$router.go(-1)
              } else {
                this.$message.error(res.message)
              }
            })
          }
        } else {
          this.$tools.focusFunc()
        }
      })
    },
    // 选中时间
    selectTime(value) {
      if (!value) return
      var thisTime = value
      thisTime = thisTime.replace(/-/g, '/')
      var time = new Date(thisTime)
      time = time.getTime()
      // this.formInline.timingTime = this.$tools.formatDateTime(value)
      var now = Date.now()
      if (time < now + 5 * 60 * 1000) {
        this.$message.error('至少大于当前时间五分钟')
        this.formInline.timingTime = ''
      }
    },
    // 点击确定
    complete() {},
    // 取消
    cancelAdd() {
      this.$confirm('确认取消发布?', '提醒', { type: 'warning' }).then((res) => {
        this.$router.go(-1)
      })
    },
    preview() {
      this.dialogVisibleRole = true
    },
    close(val) {
      this.dialogVisibleRole = false
    }
  }
}
</script>

<style lang="scss" scoped>
.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
  padding: 10px;
  overflow-y: auto;
}

.content_box {
  height: calc(100% - 30px);
  margin-top: 10px;
  padding: 20px 25px 25px;
  background: #fff;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.project-textarea {
  ::v-deep .el-textarea__inner {
    height: 96px !important;
    max-height: 96px !important;
    overflow-y: auto !important;
  }
}

.project-textarea textarea {
  height: 120px;
}

.dialog-contnet {
  width: 100%;
  background-color: #fff;
  padding: 10px;

  .caption {
    height: 36px;
    line-height: 36px;
    border-bottom: 1px solid #ddd;

    .caption-text {
      display: inline-block;
      margin-right: 40px;
    }
  }

  .preview {
    .ql-editor {
      max-height: 328px;
      overflow: auto;
    }
  }
}

.my-editor {
  width: 929px;
  min-width: 100px;
}
</style>
