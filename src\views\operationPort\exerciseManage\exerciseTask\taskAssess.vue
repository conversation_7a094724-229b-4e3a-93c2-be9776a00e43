<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="targetPlanDetail-content-title" @click="goBack"><i class="el-icon-arrow-left"></i><span
          style="margin-left: 10px">返回</span></div>
      <div class="content_box">
        <div class="detaiBox">
          <div v-for="(item, index) in taskStatisticsData" :key="index" class="list-item">
            <div class="list-label">{{ item.name }}</div>
            <div v-if="taskInfo[item.key]" class="list-value">
              <el-tooltip class="item" effect="dark" :content="taskInfo[item.key]" placement="bottom-start"
                :disabled="taskInfo[item.key].length<22">
                <span>{{ taskInfo[item.key] || '--' }}</span>
              </el-tooltip>
            </div>
            <div v-else class="list-value">
              <span>--</span>
            </div>
          </div>
          <div class="list-item1">
            <div class="list-label">参与人员:</div>
            <div class="list-value1">{{ taskInfo.actorName || '--' }}</div>
          </div>
        </div>
        <div class="taskBox">
          <div class="detaiTitle">演练任务</div>
          <div class="taskList">
            {{taskInfo.drillDesc||"--"}}
          </div>
        </div>
        <div class="taskBox">
          <div class="detaiTitle">预案法规文档</div>
          <div class="regulationsDocBox" @click="openFile1(regulationsDoc)">
            <span class="list-label">预案法规文档:</span>
            <span class="list-value">{{regulationsDoc.length?regulationsDoc[0].name:""}}</span>
            <span v-if="regulationsDoc.length" class="list-upLoad">
              <i class="el-icon-download"></i>
            </span>
          </div>
        </div>
        <div class="taskBox">
          <div class="detaiTitle">法规文案</div>
          <div class="copywritinBox" @click="toDrawer">
            <span class="list-copywriting" v-html="regulationsText">
            </span>
          </div>
        </div>
        <div class="assessBox">
          <div class="detaiTitle">演练效果评定</div>
          <div class="assessList">
            <div class="radioSelect">
              <span>1、演练分工</span>
              <el-radio v-model="divideRadio" :label="'0'">分工明确</el-radio>
              <el-radio v-model="divideRadio" :label="'1'">分工不明确</el-radio>
            </div>
            <div class="radioSelect">
              <span>2、演练职责</span>
              <el-radio v-model="dutyRadio" :label="'0'">职责清晰</el-radio>
              <el-radio v-model="dutyRadio" :label="'1'">职责不清晰</el-radio>
            </div>
            <div class="radioSelect">
              <span>3、演练效果</span>
              <el-radio-group v-model="effectRadio">
                <el-radio :label="'0'">达到预期效果</el-radio>
                <el-radio :label="'1'">基本达到预期效果</el-radio>
                <el-radio :label="'2'">未达到预期效果</el-radio>
              </el-radio-group>
            </div>
          </div>
        </div>
        <div class="attachmentsBox">
          <div class="detaiTitle">演练附件 <span class="total">共</span><span
              class="number">{{fileList.length?fileList.length:0}}</span><span class="total">个附件</span></div>
          <div class="attachmentsLists">
            <el-upload ref="uploadFile" action="string" drag :http-request="httpRequest" :on-remove='onRemove'
              :beforeUpload="beforeAvatarUpload" :on-exceed="handleExceed"
              accept=".jpg, .jpeg, .png, .JPG, .JPEG, .word, .WORD, .Excel, .pdf, .PDF, .doc, .docx, .DOCX, .xlsx, .xls, .mp4, .mp3"
              multiple :on-preview='openFile' :file-list="fileList">
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            </el-upload>
          </div>
        </div>
        <div class="remarkBox">
          <div class="detaiTitle">
            <div>演练备注</div>
            <div>
              <el-checkbox-group v-model="remarkCheckList">
                <el-checkbox :label="'0'">有领导参加</el-checkbox>
                <el-checkbox :label="'1'">有安全事故</el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
          <div class="remarkList">
            <el-input v-model="remark" type="textarea" placeholder="请输入备注" resize="none"
              :autosize="{ minRows: 3, maxRows: 3 }" maxlength="200" show-word-limit style="width: 1046px;">
            </el-input>
          </div>
        </div>
        <el-drawer title="法规文案" :visible.sync="drawer" :direction="direction" :before-close="handleClose">
          <div class="drawerBox" v-html="regulationsText"></div>
        </el-drawer>
      </div>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">关闭</el-button>
      <el-button type="primary" @click="submit">提交</el-button>
    </div>
  </PageContainer>
</template>
<script>
import moment from 'moment'
export default {
  name: 'taskAssess',
  data() {
    return {
      moment,
      taskStatisticsData: [
        {
          name: '演练任务名称:',
          key: 'drillName'
        },
        {
          name: '演练类型:',
          key: 'drillTypeName'
        },
        {
          name: '演练部门:',
          key: 'deptNames'
        },
        {
          name: '周期类型:',
          key: 'cycleTypeName'
        },
        {
          name: '完成状态:',
          key: 'taskStateName'
        },
        {
          name: '演练时间:',
          key: 'exerciseTime'
        },
        {
          name: '演练地点:',
          key: 'drillPlace'
        },
        {
          name: '演练负责人:',
          key: 'headNames'
        },
        {
          name: '演练组织人:',
          key: 'organizerName'
        }
      ],
      regulationsDoc: [], // 法规文档
      regulationsText: '', // 法规文案
      taskInfo: {
        drillName: '', // 任务名称
        drillType: '', // 演练类型
        drillTypeName: '', // 类型名称
        deptNames: '', // 演练部门
        cycleTypeName: '', // 周期类型
        taskStateName: '', // 完成状态Name
        exerciseTime: '', // 演练时间
        drillPlace: '', // 演练地点
        headNames: '', // 演练负责人
        organizerName: '', // 演练组织人
        actorName: '', // 参演人员,
        drillDesc: ''// 演练任务描述
      },
      taskFilelist: [],
      cycleTypeList: [
        {
          cycleType: 0,
          label: '单次'
        },
        {
          cycleType: 1,
          label: '每周'
        },
        {
          cycleType: 2,
          label: '每月'
        },
        {
          cycleType: 3,
          label: '季度'
        },
        {
          cycleType: 4,
          label: '半年'
        },
        {
          cycleType: 5,
          label: '全年'
        }
      ],
      drillTypeList: [],
      fileList: [],
      dutyRadio: '0',
      divideRadio: '0',
      effectRadio: '0',
      remarkCheckList: ['0'],
      remark: '',
      taskStartTime: '',
      drawer: false,
      direction: 'rtl',
      divideRadioText: {
        '0': {
          text: '分工明确'
        },
        '1': {
          text: '分工不明确'
        }
      },
      dutyRadioText: {
        '0': {
          text: '职责清晰'
        },
        '1': {
          text: '职责不清晰'
        }
      },
      effectRadioText: {
        '0': {
          text: '达到预期效果'
        },
        '1': {
          text: '基本达到预期效果'
        },
        '2': {
          text: '未达到预期效果'
        }
      }
    }
  },
  mounted() {
    this.getTypeList()
    setTimeout(() => {
      this.getTaskData()
    }, 300)
  },
  methods: {
    // 打开抽屉
    toDrawer() {
      this.drawer = true
    },
    // 关闭文案抽屉
    handleClose(dne) {
      this.drawer = false
    },
    // 获取演练类型列表
    getTypeList() {
      let data = {
        page: {
          current: 1,
          size: 9999
        }
      }
      this.$api
        .getPreplanDrillTypeData(data)
        .then((res) => {
          this.drillTypeList = res.data ? res.data.list : []
        })

    },
    getTaskData() {
      const taskId = this.$route.query.taskId
      this.$api.getTaskDataById({ id: taskId }).then((res) => {
        if (res.code == 200) {
          this.taskInfo = res.data
          this.taskInfo.cycleTypeName = this.cycleTypeFn(res.data.cycleType) || ''
          this.taskInfo.taskStateName = res.data.taskState === 0 ? '未完成' : '已完成'
          this.taskStartTime = res.data.drillStartTime
          this.taskInfo.exerciseTime = moment(res.data.drillStartTime).format('YYYY-MM-DD') + '-' + moment(res.data.drillEndTime).format('YYYY-MM-DD')
          this.taskInfo.actorName = res.data.deptPersonList ? res.data.deptPersonList.join(',') : ''
          this.regulationsDoc = res.data.regulationsDoc ? JSON.parse(res.data.regulationsDoc) : []
          this.regulationsText = res.data.regulationsText || ''
        } else if (res.message) {
          this.$message.error(res.message)
        }
      })
    },
    // 周期类型
    cycleTypeFn(e) {
      const item = this.cycleTypeList.filter((i) => i.cycleType === e)
      if (item)
        return item[0].label || ''
    },
    // 提交
    submit() {
      // // 演练文件校验
      if (this.fileList.length < 1) {
        this.$message({
          type: 'error',
          message: '请上传演练文件'
        })
        return false
      }
      let userInfo = this.$store.state.user.userInfo.user
      const taskId = this.$route.query.taskId
      let data = {
        updateName: userInfo.staffName,
        updateId: userInfo.staffId,
        taskStartTime: this.taskStartTime,
        taskEffect: '',
        taskRemark: '',
        id: taskId,
        taskUrl: JSON.stringify(this.fileList)
      }
      let taskEffectObj = {
        dutyRadio: this.dutyRadio,
        divideRadio: this.divideRadio,
        effectRadio: this.effectRadio,
        result: this.getEffectText(this.dutyRadio, this.divideRadio, this.effectRadio)
      }
      let taskRemarkObj = {
        remark: this.remark,
        remarkCheckList: this.remarkCheckList,
        result: this.getRemarkText(this.remarkCheckList)
      }
      const taskEffectStr = JSON.stringify(taskEffectObj)
      const taskRemarkStr = JSON.stringify(taskRemarkObj)
      data.taskEffect = taskEffectStr
      data.taskRemark = taskRemarkStr
      this.$api.updateTaskData(data).then((res) => {
        if (res.code == '200') {
          this.$message({
            type: 'success',
            message: '保存成功'
          })
          this.$router.go('-1')
        } else {
          this.$message({
            type: 'error',
            message: res.message || '保存失败'
          })
        }
      })
    },
    // 获取效果评定文字
    getEffectText(dutyRadio, divideRadio, effectRadio) {
      return this.divideRadioText[dutyRadio].text + ',' + this.dutyRadioText[divideRadio].text + ',' + this.effectRadioText[effectRadio].text
    },
    // 获取备注文字
    getRemarkText(remarkCheckList) {
      if (remarkCheckList.length) {
        if (remarkCheckList[0] === '0') {
          return '有领导参加'
        } else if (remarkCheckList[0] === '1') {
          return '有安全事故'
        } else {
          return '有领导参加,有安全事故'
        }
      } else {
        return '-'
      }
    },
    /**
    * 文件上传成功
    */
    // 限制文件大小
    beforeAvatarUpload(file) {
      const isLt20M = file.size / 1024 / 1024 < 20
      if (!isLt20M) {
        this.$message.error('上传文件大小不能超过 20MB!')
        return false
      }
    },
    httpRequest(file) {
      const params = new FormData()
      params.append('file', file.file)
      this.$api.uploadTask(params).then((res) => {
        if (res.code == 200) {
          this.fileList.push({
            url: res.data.picUrl,
            name: res.data.name
          })
          this.$message({
            message: '上传成功',
            type: 'success'
          })
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },
    openFile(val) {
      let url = val.url
      window.open(url)
    },
    openFile1(list) {
      let url = this.$tools.imgUrlTranslation(list[0].url)
      window.open(url)
    },
    // 移除
    onRemove(file, fileList) {
      this.fileList = fileList
    },
    handleExceed(files, fileList) {
      this.$message.warning('最多选择一个文件')
    },
    // 返回
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.container-content > div {
  background-color: #fff;
  height: 100%;
  position: relative;
  .targetPlanDetail-content-title {
    border-bottom: 1px solid #e4e7ed;
    padding: 13px 24px;
    cursor: pointer;
  }
  .content_box {
    padding: 16px 24px;
    background: #fff;
    display: flex;
    height: calc(100% - 50px);
    flex-direction: column;
    overflow-y: auto;
    .detaiTitle {
      font-weight: 500;
      font-size: 16px;
      color: #333333;
      padding: 10px 0px;
    }
    .detaiBox {
      display: flex;
      flex-wrap: wrap;
      font-size: 14px;
      .list-item {
        width: calc(100% / 3) !important;
        margin-bottom: 16px;
        display: flex;
      }
      .list-item1 {
        width: 100%;
        margin-bottom: 16px;
        display: flex;
      }
      .list-value {
        flex: 1;
        color: #666666;
        font-weight: 400;
        margin-left: 10px;
        white-space: nowrap; /* 不换行 */
        overflow: hidden; /* 隐藏溢出部分 */
        text-overflow: ellipsis; /* 使用省略号表示被截断的内容 */
      }
      .list-value1 {
        flex: 1;
        color: #666666;
        font-weight: 400;
        margin-left: 10px;
      }
    }
    .list-label {
      display: inline-block;
      width: 100px;
      color: #666666;
      font-weight: 400;
      text-align: right;
    }
    .list-value {
      display: inline-block;
      color: #666666;
      font-weight: 400;
      margin-left: 10px;
    }
    .list-copywriting {
      display: inline-block;
      width: 300px;
      overflow: scroll;
      white-space: wrap;
      margin-left: 10px;
      cursor: pointer;
      text-overflow: ellipsis;
    }
    .list-state {
      padding: 2px 4px;
      display: inline-block;
      border-radius: 2px;
      margin-left: 10px;
    }
    .state-color1 {
      color: #f53f3f;
      background: #ffece8;
    }
    .state-color2 {
      color: #00b42a;
      background: #e8ffea;
    }
    .state-color3 {
      color: #ff7d00;
      background: #fff7e8;
    }
    .remarkBox {
      width: 100%;
      padding: 0 8px;
      .detaiTitle {
        display: flex;
        div {
          margin-right: 16px;
        }
      }
      .remarkList {
        margin: 8px 0 8px 0;
      }
    }
    .assessBox {
      width: 100%;
      padding: 0 8px;
      .assessList {
        margin: 8px 0 8px 0;
        .radioSelect {
          margin-bottom: 20px;
          span {
            margin-right: 16px;
          }
        }
      }
    }
    .taskBox {
      width: 100%;
      padding: 0 8px;
      .regulationsDocBox {
        cursor: pointer;
        display: flex;
        .list-upLoad {
          margin-left: 30px;
          color: #3562db;
          font-size: 16px;
          font-weight: bold;
        }
      }
      .copywritinBox {
        display: flex;
        .list-copywriting {
          display: inline-block;
          flex: 1;
          // height: 100px;
          // overflow: scroll;
          margin-left: 10px;
          cursor: pointer;
        }
      }
      .taskList {
        padding: 2px 0 16px 8px;
      }
    }
    .attachmentsBox {
      width: 100%;
      padding: 0 8px;
      .attachmentsLists {
        margin: 8px 0 8px 0;
        display: flex;
        flex-wrap: wrap;
        .attachmentsLists-item {
          width: calc((100% - 32px) / 3);
          display: flex;
          margin: 5px 5px 5px 5px;
          .content {
            width: 40%;
            margin-left: 8px;
          }
          .operation {
            flex: 1;
            text-align: right;
          }
        }
      }
      .total {
        font-size: 14px;
        color: #7f848c;
      }
      .total {
        font-size: 14px;
        color: #333333;
        margin: 0 5px;
      }
    }
  }
}
.drawerBox {
  padding: 0 20px;
  word-break: break-all !important;
}
</style>
