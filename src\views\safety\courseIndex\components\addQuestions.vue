<template>
  <div class="inner">
    <div class="center_contenter">
      <div class="top" v-if="type != 'edit'">
        <div class="quesType">
          <span class="addQues"
            >添加题目类型：<span class="remarks"
              >(注：点击题型按钮选择想要的录入题型)</span
            ></span
          >
          <span>已添加 <span class="quesNum">{{this.formInfo.exercisesList.length}}</span> 题</span>
        </div>
        <el-button type="primary" @click="addQuestions('1')">单选题</el-button>
        <el-button type="primary" @click="addQuestions('2')">多选题</el-button>
        <el-button type="primary" @click="addQuestions('3')" plain
          >判断题</el-button
        >
      </div>
      <div class="center">
        <el-form
          label-width="140px"
          :model="formInfo"
          ref="questionsInfo"
          class="demo-form-inline questionsInfo"
        >
          <div
            class="item"
            v-for="(item, index) in formInfo.exercisesList"
            :key="index"
          >
            <div class="item_top">
              <div class="left">
                <div class="exercisesType">
                  {{
                    item.type == 1
                      ? "单选题"
                      : item.type == 2
                      ? "多选题"
                      : "判断题"
                  }}
                </div>
              </div>
              <div class="right">
                <el-input
                  type="number"
                  placeholder="小题分数"
                  min="1"
                  style="width: 160px"
                  v-model="item.score"
                >
                  <template slot="append">分</template>
                </el-input>
                <div class="delQue" @click="delQuestion(index)">
                  <i class="el-icon-delete"></i>
                  <span>删除</span>
                </div>
              </div>
            </div>
            <el-row>
              <el-col :span="12" v-if="type == 'edit'">
                <el-form-item
                  label="所属类型"
                  :prop="`exercisesList.${index}.type`"
                  :rules="questionsRules.type"
                >
                  <el-select
                    v-model="item.type"
                    filterable
                    placeholder="全部题型"
                    style="width: 100%;"
                    @change="(val) => changeType(val, item)"
                  >
                    <el-option
                      v-for="item in quesTypeList"
                      :key="item.id"
                      :label="item.dictName"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="所属科目"
                  :prop="`exercisesList.${index}.subjectId`"
                  :rules="questionsRules.subjectId"
                >
                  <el-cascader
                    v-model="item.subjectId"
                    clearable
                    class="sino_sdcp_input mr15"
                    :options="questionsSubList"
                    :props="props"
                    :disabled="type=='enter'"
                    placeholder="请选择类型"
                    style="width: 100%"
                    @change="(val) => allCourseList(val, item)"
                  ></el-cascader>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="知识点所属课程"
                  :prop="type=='enter'?`exercisesList.${index}.courseName`:`exercisesList.${index}.courseId`"
                  :rules="type=='enter'?questionsRules.courseId:questionsRules.courseName"
                >
                  <el-select
                    v-if="type!='enter'"
                    v-model="item.courseId"
                    filterable
                    placeholder="请输入知识点所属课程"
                    style="width: 100%"
                    @change="(val) => courseChange(val, item)"
                  >
                    <el-option
                      v-for="k in item.courseList"
                      :key="k.id"
                      :label="k.courseName"
                      :value="k.id"
                    >
                    </el-option>
                  </el-select>
                  <el-input
                    v-else
                    v-model="item.courseName"
                    placeholder="请输入所属课程"
                    show-word-limit
                    :disabled="type=='enter'"
                    maxlength="1000"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item
                  label="题目"
                  :prop="`exercisesList.${index}.topic`"
                  :rules="questionsRules.topic"
                >
                  <el-input
                    v-model="item.topic"
                    placeholder="请输入题目"
                    show-word-limit
                    type="textarea"
                    :autosize="{ minRows: 2, maxRows: 6}"
                    maxlength="1000"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <template v-if="item.type != '3'">
              <el-row v-for="(k, ind) in item.options" :key="ind">
                <el-col :span="18">
                  <el-form-item :label="$tools.addLetter(ind)" :prop="`exercisesList.${index}.options.${ind}.label`"
                  :rules="questionsRules.label">
                    <el-input
                      v-model="k.label"
                      placeholder="请填写选项内容，最多输入200个字"
                      show-word-limit
                      maxlength="200"
                      type="textarea"
                      autosize
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="4" class="icon_class">
                  <i class="el-icon-upload2" @click="moveUp(ind, item)"></i>
                  <i class="el-icon-download" @click="moveDown(ind, item)"></i>
                  <div class="line"></div>
                  <i
                    class="el-icon-plus"
                    @click="addQuestionOption(item.options, ind)"
                  ></i>
                  <i
                    class="el-icon-delete"
                    @click="delQuestionOption(item.options, ind)"
                  ></i>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="正确答案：" :prop="`exercisesList.${index}.answer`" :rules="questionsRules.answer">
                    <el-radio-group
                      v-if="item.type == '1'"
                      v-model="item.answer"
                    >
                      <el-radio
                        v-for="(j, ind1) in $tools.addLetter(
                          '',
                          item.options.length
                        )"
                        :key="ind1"
                        :label="j"
                        >{{ j }}</el-radio
                      >
                    </el-radio-group>
                    <el-checkbox-group
                      v-if="item.type == '2'"
                      v-model="item.answer"
                    >
                      <el-checkbox
                        v-for="(j, ind1) in $tools.addLetter(
                          '',
                          item.options.length
                        )"
                        :key="ind1"
                        :label="j"
                        >{{ j }}</el-checkbox
                      >
                    </el-checkbox-group>
                  </el-form-item>
                </el-col>
              </el-row>
            </template>
            <el-row v-if="item.type == '3'">
              <el-col :span="24">
                <el-form-item label="正确答案：" :prop="`exercisesList.${index}.answer`" :rules="questionsRules.answer">
                  <el-radio-group v-model="item.answer">
                    <el-radio :label="'1'">正确</el-radio>
                    <el-radio :label="'2'">错误</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="解析：">
                  <el-input
                    type="textarea"
                    :autosize="{ minRows: 2, maxRows: 4 }"
                    placeholder="请输入解析，最多输入500字"
                    show-word-limit
                    maxlength="500"
                    v-model="item.analysis"
                  >
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    type: {
      type: String,
      default: "",
    },
    questionsId: {
      type: String,
      default: "",
    },
    subjectId:{
      type:Number,
      default:null
    },
    courseName:{
      type:String,
      default:''
    }
  },
  data() {
    return {
      props: {
        children: "childList",
        label: "name",
        value: "id",
        checkStrictly: true,
        emitPath: false,
      },
      formInfo: {
        exercisesList: [
          // {
          //   score: "",
          //   type: 1,
          //   subjectId: '',
          //   courseId: "",
          //   courseName: "",
          //   courseList:[],
          //   topic: "",
          //   answer:"",
          //   analysis: "",
          //   options: [
          //     {
          //       label: "",
          //     },
          //     {
          //       label: "",
          //     },
          //   ],
          // }
        ],
      },
      questionsSubList: [],
      questionsRules: {
        type: [
          { required: true, message: "请选择所属类型", trigger: "change" },
        ],
        subjectId: [
          { required: true, message: "请选择所属科目", trigger: "change" },
        ],
        answer:[
          { required: true, message: "请选择正确答案", trigger: "change" },
        ],
        courseId: [
          { required: true, message: "请输入知识点所属课程", trigger: "blur" },
        ],
        courseName: [{ required: true, message: "请输入知识点所属课程", trigger: "blur" }],
        topic: [{ required: true, message: "请输入题目", trigger: "blur" }],
        label: [{ required: true, message: "请输入选项", trigger: "blur" }],
      },
      quesTypeList: [
        {
          id: 1,
          dictName: "单选题",
        },
        {
          id: 2,
          dictName: "多选题",
        },
        {
          id: 3,
          dictName: "判断题",
        },
      ],
    };
  },
  created() {
    this.getTblleList();
    // 编辑获取详情
    if(this.questionsId){
      this.getQuestionsDetails()
    }
  },
  methods: {
    getTblleList() {
      let data = {
        pageNo: 1,
        pageSize: 999,
      };
      this.$api.ipsmFicationlList(data).then((res) => {
        if (res.code == 200) {
          this.questionsSubList = res.data.records;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    getQuestionsDetails(){
      this.$api.getQuestionsDetails({id:this.questionsId}).then((res) => {
        if (res.code == 200) {
          this.formInfo.exercisesList = [res.data]
          this.allCourseList(res.data.subjectId,this.formInfo.exercisesList[0])
           this.formInfo.exercisesList[0].answer=res.data.type=='2'? res.data.answer.split(','):res.data.answer
          this.formInfo.exercisesList[0].options= JSON.parse(res.data.options)
         
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 获取课程列表
    allCourseList(id, item) {
      this.$api.allCourseList({ subjectId: id || "" }).then((res) => {
        if (res.code == 200) {
          item.courseList = res.data;
          this.$forceUpdate() //解决bug36644,编辑题库时所属课程未回显问题
          if(this.type == 'edit'){
            this.courseChange(this.formInfo.exercisesList[0].courseId,item)
          }
        } else {
          this.$message.error(res.msg);
        }
      });
    },

    // 选择课程
    courseChange(val, item) {
      let obj= item.courseList.find(item=>item.id==val)
      item.courseName =obj?.courseName
    },
    // 所属类型
    changeType(val,item){
      val!='2'?item.answer='':item.answer=[]
    },
    // 添加试题
    addQuestions(type) {
      let questionsItem = {
        score: "2",
        type: type,
        subjectId: "",
        courseId: "",
        courseName: "",
        courseList:[],
        topic: "",
        answer: type == "2" ? [] : "",
        analysis: "",
        options: [
          {
            label: "",
          },
          {
            label: "",
          },
        ],
      };
      if(this.type=='enter'){
        questionsItem.courseName=this.courseName
        questionsItem.subjectId=this.subjectId
      }else if(this.type=='plan'){
        questionsItem.subjectId=this.subjectId
        this.allCourseList(this.subjectId,questionsItem)
      }
      this.formInfo.exercisesList.push(questionsItem);
    },
    // 删除试题
    delQuestion(index) {
      this.formInfo.exercisesList.splice(index, 1);
    },
    // 上移选择项
    moveUp(index, item) {
      if (index != 0) {
        let nums = item.options;
        [nums[index], nums[index - 1]] = [nums[index - 1], nums[index]];
        item.options = [...nums];
      }else{
        this.$message.error('禁止上移')
      }
    },
    //下移选择项
    moveDown(index, item) {
      if(item.options.length&&index!=item.options.length-1){
        let nums = item.options;
        [nums[index], nums[index + 1]] = [nums[index + 1], nums[index]];
        item.options = [...nums];
      }else{
        this.$message.error('禁止下移')
      }
    },
    // 添加选项
    addQuestionOption(list, ind) {
      let obj = {
        label: "",
      };
      list.splice(ind + 1, 0, obj);
    },
    // 删除选择项
    delQuestionOption(list, ind) {
      if (list.length > 2) {
        list.splice(ind, 1);
      }else{
        this.$message.error('最少保留两个选项')
      }
    },
    // 校验
    getValidate() {
      if(!this.formInfo.exercisesList.length){
        return this.$message.error('添加试题不能为空')
      }
      this.$refs.questionsInfo.validate((valid) => {
        if (valid) {
          if(this.type=='plan'){
            this.$emit("planIsOk",this.formInfo.exercisesList)
          }else{
            this.$emit("isOk", this.formInfo.exercisesList);
          }
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.inner {
  height: 100%;
  .center_contenter {
    height: calc(100% - 90px);
    .top {
      background-color: #faf9fc;
      padding: 16px;
      color: #8c8c8c;
      .quesType {
        display: flex;
        justify-content: space-between;
        margin-bottom: 16px;
        .addQues {
          color: #000;
          .remarks {
            color: #8c8c8c;
          }
        }
      }
      .quesNum {
        color: #000;
      }
    }
    .center {
      height: calc(100% - 60px);
      overflow: auto;
    }
    .item {
      padding: 16px;
      border-bottom: 4px solid #faf9fc;
      .item_top {
        display: flex;
        justify-content: space-between;
        .left {
          display: flex;
          align-items: center;
          justify-content: center;
          .exercisesType {
            width: 58px;
            height: 22px;
            line-height: 22px;
            text-align: center;
            border-radius: 4px;
            background-color: #ededf5;
            font-size: 14px;
            color: #86909c;
          }
        }
        .right {
          display: flex;
          align-items: center;

          .delQue {
            cursor: pointer;
            color: #ff6461;
            i {
              margin: 0 10px 0 16px;
              color: #ff6461;
            }
            span {
              font-size: 14px;
            }
          }
        }
      }
    }
    .questionsInfo {
      margin-top: 16px;
      .chioce_item {
        height: 40px;
        display: flex;
      }
      .icon_class {
        display: flex;
        align-items: center;
      }
      i {
        margin-left: 16px;
        color: #3562db;
        line-height: 40px;
        cursor: pointer;
      }
      .line {
        width: 2px;
        height: 14px;
        margin: 0 10px 0 26px;
        background-color: #dcdfe6;
      }
    }
  }
}
::v-deep .el-form-item {
  display: block !important;
}
</style>
