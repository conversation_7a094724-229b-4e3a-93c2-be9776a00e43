<template>
  <PageContainer title="今日运行模式" type="list" :isClose="false" style="background-color: rgb(245 246 251); overflow: hidden;">
    <template slot="content">
      <div class="todaysOperationMode">
        <div class="top">
          <div class="toptip">
            <span class="green_line"></span>
            今日运行模式
          </div>
          <el-button v-if="!updataTodayMode" type="primary" class="sino-button-sure top_btn" @click="adjustTodayMode">调整今日模式</el-button>
          <div v-else class="top_right_btn">
            <el-button type="primary" style="background: #67c23a; border-color: #67c23a; margin-right: 50px;" class="sino-button-sure" @click="todayDatePreview">预览</el-button>
            <el-button type="primary" @click="todayDateSubmit">保 存</el-button>
            <el-button plain type="primary" @click="todayDateCancel">取 消</el-button>
          </div>
        </div>
        <todayDataMode v-if="!updataTodayMode" class="content" />
        <updateOperationMode v-else />
        <el-dialog
          v-if="todayDatePreviewShow"
          title="预览今日运行模式"
          :visible.sync="todayDatePreviewShow"
          custom-class="today-preview-dialog"
          :before-close="closeTodayDatePreviewDialog"
        >
          <todayDataMode ref="todayDataMode" type="preview" />
        </el-dialog>
      </div>
    </template>
  </PageContainer>
</template>
<script>
import updateOperationMode from '../components/updateOperationMode.vue'
import todayDataMode from '../components/todayDataMode.vue'
export default {
  name: 'todaysOperationMode',
  components: {
    updateOperationMode,
    todayDataMode
  },
  data() {
    return {
      updataTodayMode: false, // 更新今日模式
      todayDatePreviewShow: false // 今日模式预览
    }
  },
  mounted() {},
  methods: {
    // 调整今日模式
    adjustTodayMode() {
      this.updataTodayMode = true
      // this.$message({
      //   message: "调整成功",
      //   type: "success",
      // });
    },
    // 保存今日模式
    todayDateSubmit() {
      this.saveOrCancel(true)
    },
    // 取消保存 今日模式
    todayDateCancel() {
      this.saveOrCancel(false)
    },
    saveOrCancel(isSave) {
      this.$api.saveOrCancel({ isSave }).then((res) => {
        if (res.code == '200') {
          this.updataTodayMode = false
          this.$message({
            message: isSave ? '保存成功' : '取消保存',
            type: 'success'
          })
        }
      })
    },
    // 预览
    todayDatePreview() {
      this.todayDatePreviewShow = true
    },
    // 关闭预览
    closeTodayDatePreviewDialog() {
      this.todayDatePreviewShow = false
    }
  }
}
</script>
<style src="@/assets/styles/sinomis-ui.css" scoped></style>
<style lang="scss" type="text/css" scoped>
.todaysOperationMode {
  width: calc(100% - 20px);
  height: calc(100% - 20px);
  padding: 20px;

  .top {
    border-radius: 10px;
    height: 48px;
    line-height: 48px;
    background-color: #fff;
    display: flex;

    .toptip {
      border-bottom: none;
      width: auto;
      font-size: 16px;
      font-weight: 400;
    }

    .top_btn {
      margin: auto 0;
      margin-left: 20px;
      font-size: 14px;
      height: 28px;
    }

    .top_right_btn {
      margin: auto 0;
      flex: 1;
      text-align: right;
    }
  }

  .today-preview-dialog {
    margin: auto !important;
    margin-top: 5vh !important;
    width: 85%;
    height: 90%;
  }

  .content {
    height: calc(100% - 66px);
    margin-top: 10px;
  }
}
</style>
