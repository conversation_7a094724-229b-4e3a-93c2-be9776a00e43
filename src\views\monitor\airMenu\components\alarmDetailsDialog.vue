<!-- 运行率占比弹窗 -->
<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    title="运行报警详情"
    width="60%"
    :visible.sync="visible"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <div v-loading="loading" class="content">
      <ContentCard :title="'报警ID：' + detailsData.alarmId">
        <div slot="content">
          <el-row v-for="(arr, index) in infoList" :key="index" :gutter="24" style="margin: 0;">
            <el-col v-for="item in arr" :key="item.label" :md="8" class="card-item">
              <p class="card-label">{{ item.label }}</p>
              <p v-if="item.prop != 'alarmLevel'" class="card-value">{{ detailsData[item.prop] || '' }}</p>
              <p v-else class="card-value">{{ alarmType[detailsData[item.prop]] || '' }}</p>
            </el-col>
          </el-row>
        </div>
      </ContentCard>
      <ContentCard title="报警规则" style="margin-top: 15px; flex: 1;">
        <div id="cartChart" slot="content" class="cartChart">
          <div></div>
        </div>
      </ContentCard>
    </div>
    <span slot="footer">
      <el-button plain @click="closeDialog">关 闭</el-button>
    </span>
  </el-dialog>
</template>
<script>
import * as echarts from 'echarts'
export default {
  name: 'alarmDetailsDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    alarmId: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      detailsData: {},
      alarmType: {
        3: '重要',
        2: '紧急',
        1: '一般',
        0: '通知'
      },
      infoList: [
        [
          { label: '报警类型：', prop: 'incidentName' },
          { label: '报警名称：', prop: 'alarmObjectName' },
          { label: '报警描述：', prop: 'alarmDetails' }
        ],
        [
          { label: '报警系统：', prop: 'projectName' },
          { label: '报警级别：', prop: 'alarmLevel' },
          { label: '报警实体对象：', prop: 'alarmObjectName' }
        ],
        [
          { label: '报警开始时间：', prop: 'alarmStartTime' },
          { label: '报警结束时间：', prop: 'alarmEndTime' },
          { label: '持续时间：', prop: 'continueTime' }
        ],
        [
          // { label: '报警实体类型：', prop: 'alarmEntityType' },
          { label: '备注：', prop: 'remark' }
        ]
      ]
    }
  },
  mounted() {
    this.getDetailsData()
  },
  methods: {
    getDetailsData() {
      this.loading = true
      this.$api.GetDetectionItemAlarmDetails({ alarmId: this.alarmId }).then((res) => {
        this.loading = false
        if (res.code == 200) {
          this.detailsData = res.data
          this.$nextTick(() => {
            this.drawline(res.data.trend)
          })
        }
      })
    },
    drawline(data) {
      const myChart = echarts.init(document.getElementById('cartChart'))
      let option
      if (data.length) {
        option = {
          title: {
            text: data[0].paramName,
            // text: '回风温度/℃',
            textStyle: {
              fontSize: 12,
              fontWeight: 'normal',
              color: '#999'
            }
            // subtext: '回风温度/℃'
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross'
            }
          },
          dataZoom: [
            {
              type: 'inside',
              start: 0,
              end: 100
            }
          ],
          grid: {
            top: '15%',
            left: '2%',
            bottom: '3%',
            right: '2%',
            containLabel: true
          },
          xAxis: {
            type: 'time',
            boundaryGap: false,
            // prettier-ignore
            axisTick: {
              show: false
            }
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: '{value} '
            },
            axisPointer: {
              snap: false
            }
          },
          visualMap: {
            show: false,
            dimension: 0
          },
          series: [
            {
              name: data[0].paramName,
              type: 'line',
              stack: 'Total',
              showSymbol: false,
              lineStyle: {
                normal: {
                  color: '#797979'
                }
              },
              markArea: {
                itemStyle: {
                  color: 'rgba(255, 173, 177, 0.4)'
                }
              },
              data: data.map((item) => [item.time, item.number])
            },
            {
              name: '报警时段',
              type: 'line',
              showSymbol: false,
              smooth: true,
              lineStyle: {
                normal: {
                  color: '#797979'
                }
              },
              markArea: {
                itemStyle: {
                  color: 'pink'
                },
                data: [
                  [
                    {
                      xAxis: this.detailsData.alarmStartTime
                    },
                    {
                      xAxis: this.detailsData.alarmEndTime
                    }
                  ],
                  [
                    {
                      name: this.detailsData.alarmStartTime,
                      xAxis: this.detailsData.alarmStartTime
                    },

                    {
                      xAxis: this.detailsData.alarmStartTime
                    }
                  ],
                  [
                    {
                      name: this.detailsData.alarmEndTime,
                      xAxis: this.detailsData.alarmEndTime
                    },
                    {
                      xAxis: this.detailsData.alarmEndTime
                    }
                  ]
                ]
              }
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      myChart.clear()
      myChart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', !this.visible)
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog__body {
  height: calc(78vh - 110px);
}

.content {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.model-dialog {
  padding: 0 !important;
}

.card-item {
  display: flex;
}

.card-label {
  min-width: 100px;
}

.cartChart {
  width: 100%;
  height: 100%;
  min-height: 150px;
}

::v-deep .box-card {
  .card-body {
    overflow: hidden;
  }
}
</style>
