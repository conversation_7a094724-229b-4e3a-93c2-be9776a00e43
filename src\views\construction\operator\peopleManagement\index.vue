<template>
  <PageContainer>
    <div slot="content" class="content">
      <div class="left">
        <div v-loading="unitLoading" class="sino_tree_box">
          <el-tooltip v-for="(item, index) in unitList" :key="index" class="item" effect="dark" :content="item.companyName" placement="top">
            <div :class="[item.id === deptId ? 'active' : '']" class="unit_item" @click="nodeClick(item)">
              {{ item.companyName }}
            </div>
          </el-tooltip>
        </div>
      </div>
      <div class="right">
        <div class="rignt-top-search">
          <div>
            <el-input v-model="queryCriteria" suffix-icon="el-icon-search" placeholder="请输入姓名、手机号" clearable></el-input>
          </div>
          <div>
            <el-button type="primary" plain @click="reset">重置</el-button>
            <el-button type="primary" @click="search">查询</el-button>
          </div>
        </div>
        <div class="right-center-opertaion">
          <el-button type="primary" @click="handleListEvent('add')">新增</el-button>
          <el-button type="primary" plain @click="handleListEvent('import')">导入</el-button>
        </div>
        <div class="contentTable">
          <TablePage
            ref="table"
            v-loading="tableLoading"
            :showPage="true"
            :border="true"
            :tableColumn="tableColumn"
            :data="tableData"
            height="calc(100% - 40px)"
            :pageData="pageData"
            @pagination="paginationChange"
          ></TablePage>
        </div>
      </div>
      <AddUser v-if="addVisible" :id="detailId" v-model="addVisible" :activeCompany="getActiveCompany()" :handleType="handleType" @success="addSuccess" />
      <Preview v-if="viewVisible" v-model="viewVisible" :list="fileList" />
      <ImportFile v-if="importVisible" v-model="importVisible" type="user" url="fieldOperationStaffController/importStaff" @success="getUserTableList" />
    </div>
  </PageContainer>
</template>
<script lang="jsx">
export default {
  name: 'peopleManagement',
  components: {
    AddUser: () => import('./add.vue'),
    Preview: () => import('../components/preview.vue'),
    ImportFile: () => import('../components/importFile.vue')
  },
  data() {
    return {
      queryCriteria: '',
      tableLoading: false,
      tableColumn: [
        {
          prop: '',
          label: '序号',
          formatter: (scope) => {
            return (this.pageData.page - 1) * this.pageData.pageSize + scope.$index + 1
          },
          width: 80
        },
        {
          prop: 'name',
          label: '姓名'
        },
        {
          prop: 'sex',
          label: '性别',
          render: (h, row) => {
            return h('span', row.row.sex == 1 ? '男' : '女')
          }
        },
        {
          prop: 'phone',
          label: '手机号'
        },
        {
          prop: 'companyName',
          label: '成员归属单位'
        },
        {
          prop: 'idCardImage',
          label: '身份证证件',
          render: (h, row) => {
            if (row.row.idCardImage) {
              return (
                <el-button type="text" onClick={() => this.handleListEvent('idCard', row.row)}>
                  查看
                </el-button>
              )
            } else {
              return <span>未上传</span>
            }
          }
        },
        {
          prop: 'operation',
          label: '操作',
          width: '240',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <el-button type="text" onClick={() => this.handleListEvent('detail', row.row)}>
                  详情
                </el-button>
                <el-button type="text" onClick={() => this.handleListEvent('edit', row.row)}>
                  修改
                </el-button>
                <span class="operationBtn-span" onClick={() => this.handleListEvent('del', row.row)}>
                  删除
                </span>
              </div>
            )
          }
        }
      ],
      tableData: [],
      pageData: {
        pageSize: 15,
        page: 1,
        total: 0
      },
      unitLoading: false,
      unitList: [],
      addVisible: false,
      deptId: '',
      detailId: '',
      idCardVisible: false,
      handleType: 'add',
      viewVisible: false,
      fileList: [],
      importVisible: false
    }
  },
  mounted() {
    this.getUnitListFn()
    this.getUserTableList()
  },
  methods: {
    getActiveCompany() {
      if (this.deptId) {
        const findData = this.unitList.find((item) => item.id == this.deptId)
        return {
          companyId: findData.id,
          companyName: findData.companyName
        }
      }
    },
    nodeClick(item) {
      this.deptId = item.id
      this.getUserTableList()
    },
    reset() {
      this.pageData.page = 1
      this.deptId = ''
      this.queryCriteria = ''
      this.getUserTableList()
    },
    search() {
      this.getUserTableList()
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getUserTableList()
    },
    // 获取单位列表
    getUnitListFn() {
      this.unitLoading = true
      this.$api.getConstructionUnitListNoParams().then((res) => {
        if (res.code == 200) {
          this.unitLoading = false
          this.unitList = res.data
        }
      })
    },
    getUserTableList() {
      let params = {
        companyId: this.deptId,
        queryCriteria: this.queryCriteria,
        pageSize: this.pageData.pageSize,
        currentPage: this.pageData.page
      }
      this.$api.getConstructionUserList(params).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.records
          this.pageData.total = res.data.total
        }
      })
    },
    setDisable(data) {
      data.forEach((v) => {
        if (v.children && v.children.length) {
          v.disabled = true
          this.setDisable(v.children) // 子级循环时把这一层数据的count传入
        }
      })
    },
    handleListEvent(key, row) {
      switch (key) {
        case 'add':
          if (!this.deptId) {
            this.$message.warning('请选择单位')
            return
          }
        case 'detail':
        case 'edit':
          this.handleType = key
          this.detailId = row ? row.id : ''
          this.addVisible = true
          break
        case 'import':
          if (!this.deptId) {
            this.$message.warning('请选择单位')
            return
          }
          this.importVisible = true
          break
        case 'del':
          this.$confirm('是否确认删除该成员?', '提示', {
            cancelButtonClass: 'el-button--primary is-plain',
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.$api.delConstrucitonUser({ id: row.id }).then((res) => {
              if (res.code == 200) {
                this.getUserTableList()
                this.$message.success(res.msg)
              } else {
                this.$message.error(res.msg)
              }
            })
          })
          break
        case 'idCard':
          if (!row.idCardImage) return
          this.viewVisible = true
          this.fileList = row.idCardImage.split(',').map((e) => {
            return this.$tools.imgUrlTranslation(e)
          })
          break
        default:
          break
      }
    },
    addSuccess() {
      this.addVisible = false
      this.detailId = ''
      this.getUserTableList()
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  background: #fff;
  border-radius: 4px;
  padding: 10px;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  .left {
    text-align: center;
    width: 280px;
    height: 100%;
    background-color: #fff;
    border-radius: 5px;
    overflow: hidden;
  }
  .right {
    width: calc(100% - 290px);
    border-left: 1px solid #dcdfe6;
    background-color: #fff;
    height: 100%;
    margin-left: 10px;
    border-radius: 5px;
    .rignt-top-search {
      display: flex;
      justify-content: space-between;
      padding: 0px 24px;
      .el-input {
        width: 321px;
      }
      .el-select {
        width: 321px;
        margin-left: 16px;
      }
    }
    .right-center-opertaion {
      padding: 0 24px;
      margin-top: 16px;
    }
    .contentTable {
      height: calc(100% - 64px) !important;
      display: flex;
      padding: 16px 24px;
      flex-direction: column;
      .contentTable-main {
        flex: 1;
        overflow: auto;
      }
    }
  }
}
.sino_tree_box {
  margin-top: 10px;
  height: 100%;
  overflow: auto;
  .unit_item {
    padding: 10px;
    height: 40px;
    line-height: 20px;
    cursor: pointer;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    &.active {
      background: #00a6ff48;
    }
  }
}
.el-tree-node__content {
  position: relative;
  .operate-btns {
    width: 30px;
    position: absolute;
    right: 2px;
  }
}
::v-deep .operationBtn-span {
  margin-left: 10px;
  color: #f53f3f;
  cursor: pointer;
}
</style>
<style lang="scss">
.el-popover.el-popper.el-popover-self {
  min-width: 100px !important;
  width: 100px !important;
  padding: 12px 8px !important;
}
.custom-icon-color .el-message-box__status {
  color: #f53f3f !important;
}
</style>
