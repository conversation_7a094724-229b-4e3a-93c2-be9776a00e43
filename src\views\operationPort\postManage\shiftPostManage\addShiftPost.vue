<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="inventory-content-title" @click="$router.go(-1)"><i class="el-icon-arrow-left"></i> {{ queryParams.type === 'add' ? '新增值班岗' : '编辑值班岗' }}</div>
      <div class="content_box">
        <el-form ref="formRef" :model="formModel" :rules="rules" label-width="auto" class="formRef">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="值班岗名称" prop="name">
                <el-input v-if="queryParams.type !== 'view'" v-model="formModel.name" placeholder="请输入" maxlength="50"> </el-input>
                <span v-else class="detailContent">{{ formModel.name }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="年龄要求">
                <div v-if="queryParams.type !== 'view'">
                  <el-input
                    v-model="formModel.ageMin"
                    :maxlength="2"
                    placeholder="请输入"
                    style="width: 40%"
                    size="small"
                    onkeyup="value=value.replace(/^0|\D/,'')"
                    @change="minAgeChange"
                  >
                    <template v-slot:append>周岁</template>
                  </el-input>
                  -
                  <el-input
                    v-model="formModel.ageMax"
                    :maxlength="2"
                    placeholder="请输入"
                    style="width: 40%"
                    size="small"
                    onkeyup=" value=value.replace(/^0|\D/,'')"
                    @change="maxAgeIChange"
                  >
                    <template v-slot:append>周岁</template>
                  </el-input>
                </div>
                <span v-else class="detailContent">{{ formModel.ageMin }}-{{ formModel.ageMax }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="学历要求">
                <el-select v-if="queryParams.type !== 'view'" v-model="formModel.educationalDictId" filterable multiple @change="educationChange">
                  <el-option v-for="item of educationOptions" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
                <span v-else class="detailContent">{{ formModel.educationalDictName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="证书要求">
                <el-select v-if="queryParams.type !== 'view'" v-model="formModel.certificateDictId" filterable multiple @change="certificateChange">
                  <el-option v-for="item of certificateOptions" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
                <span v-else class="detailContent">{{ formModel.certificateDictName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="岗位要求" prop="postName">
                <el-select v-if="queryParams.type !== 'view'" ref="treeSelect" v-model="formModel.postName" placeholder="岗位要求" clearable multiple collapse-tags>
                  <el-option :value="selectTree" class="setstyle" disabled>
                    <el-tree
                      ref="postCodes"
                      :data="postOptions"
                      default-expand-all
                      :expand-on-click-node="false"
                      show-checkbox
                      :check-strictly="true"
                      :props="postPropsType"
                      :check-on-click-node="true"
                      :default-checked-keys="checkCode"
                      node-key="code"
                      @check-change="handleCheckChange"
                    >
                    </el-tree>
                  </el-option>
                </el-select>
                <span v-else class="detailContent">{{ formModel.postName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="关联空间">
                <el-cascader
                  v-if="queryParams.type !== 'view'"
                  ref="gridCodeCascader"
                  v-model="formModel.regionCode"
                  :props="gridCodePropsType"
                  collapse-tags
                  :show-all-levels="false"
                  :options="gridCodeList"
                  placeholder="请选择院区"
                  @change="gridCodeChange"
                />
                <el-tooltip v-else class="item" effect="dark" :content="formModel.regionName" placement="top" :disabled="formModel.regionName.length < 20">
                  <span class="detailContent">{{ formModel.regionName || '--' }}</span>
                </el-tooltip>
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="定位终端">
                <el-button type="primary" @click="onCreate">添加</el-button>
                <el-table :data="tableData" border row-key="id">
                  <el-table-column prop="name" label="设备名称" show-overflow-tooltip></el-table-column>
                  <el-table-column prop="code" label="资产编码" show-overflow-tooltip></el-table-column>
                  <el-table-column prop="sn" label="SN" show-overflow-tooltip></el-table-column>
                  <el-table-column prop="sysTypeName" label="专业类别" show-overflow-tooltip></el-table-column>
                  <el-table-column prop="typeName" label="系统类别" show-overflow-tooltip></el-table-column>
                  <el-table-column prop="state" label="在线状态" show-overflow-tooltip>
                    <template slot-scope="scope">
                      <el-tag v-if="scope.row.state == 1" size="mini" type="success">在线</el-tag>
                      <el-tag v-else-if="scope.row.state == 0" size="mini" type="danger">离线</el-tag>
                      <span v-else></span>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="130px">
                    <template #default="{ row }">
                      <el-button type="text" class="text-red" @click="deleteRow(row)">移除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="值班岗职责">
                <el-input v-if="queryParams.type !== 'view'" v-model="formModel.postDuty" type="textarea" maxlength="500" show-word-limit placeholder="请输入"></el-input>
                <span v-else class="detailContent">{{ formModel.postDuty }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="权力范围">
                <el-input v-if="queryParams.type !== 'view'" v-model="formModel.scopeOfRights" type="textarea" maxlength="500" show-word-limit placeholder="请输入"></el-input>
                <span v-else class="detailContent">{{ formModel.scopeOfRights }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="负责区域">
                <el-input v-if="queryParams.type !== 'view'" v-model="formModel.serviceArea" type="textarea" placeholder="请输入负责区域" maxlength="50" show-word-limit></el-input>
                <span v-else class="detailContent">{{ formModel.serviceArea }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="负责区域面积">
                <el-input
                  v-if="queryParams.type !== 'view'"
                  v-model="formModel.regionalArea"
                  type="textarea"
                  placeholder="请输入负责区域面积"
                  maxlength="50"
                  show-word-limit
                ></el-input>
                <span v-else class="detailContent">{{ formModel.regionalArea }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 岗位SOP -->
          <el-row v-if="queryParams.type !== 'view'">
            <el-col :span="24">
              <div class="sop-section">
                <div class="section-header">
                  <div class="section-title">岗位SOP</div>
                  <el-button v-if="queryParams.type !== 'view'" type="primary" size="small" @click="openSopDialog">+添加</el-button>
                </div>
                <el-table :data="sopList" border style="width: 100%">
                  <el-table-column prop="sopName" label="名称" min-width="120" show-overflow-tooltip></el-table-column>
                  <el-table-column prop="sopTitle" label="标题" min-width="120" show-overflow-tooltip></el-table-column>
                  <el-table-column prop="remark" label="备注" min-width="160" show-overflow-tooltip></el-table-column>
                  <el-table-column prop="status" label="状态" min-width="80">
                    <template slot-scope="scope">
                      <span :class="scope.row.status === 1 ? 'status-active' : 'status-disabled'">
                        {{ scope.row.status === 1 ? '启用' : '停用' }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="100" fixed="right">
                    <template slot-scope="scope">
                      <el-button type="text" size="small" style="color: #ff4d4f" @click="removeSop(scope.row, scope.$index)">移除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <!-- 关联定位终端 -->
      <template v-if="positionTerminalShow">
        <positionTerminalDialog
          :isRadio="true"
          :positionTerminalShow="positionTerminalShow"
          :selectaDialogData="selectaDialogData"
          @submitTerminalDialog="submitTerminalDialog"
          @closeTerminalDialog="closeTerminalDialog"
        />
      </template>
      <!-- SOP选择弹窗 -->
      <el-dialog title="请选择" :visible.sync="sopDialogVisible" width="50%" class="sop-dialog" :close-on-click-modal="false">
        <div class="search-container">
          <el-input v-model="searchKeyword" placeholder="搜索SOP名称、标题" class="search-input" prefix-icon="el-icon-search"> </el-input>
          <el-button icon="el-icon-search" type="primary" @click="searchSop">搜索</el-button>
          <el-button icon="el-icon-refresh" type="primary" plain @click="resetSearch">重置</el-button>
        </div>
        <el-table ref="sopSelectTable" :data="sopSelectList" border height="400px" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column prop="sopName" label="SOP名称" min-width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="sopTitle" label="标题" min-width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="remark" label="备注" min-width="160" show-overflow-tooltip></el-table-column>
        </el-table>
        <div class="pagination-container">
          <el-pagination
            :current-page="pageParams.pageNum"
            :page-sizes="[15, 20, 50, 100]"
            :page-size="pageParams.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          >
          </el-pagination>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="sopDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmSelect">确定</el-button>
        </span>
      </el-dialog>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">取消</el-button>
      <el-button type="primary" @click="submitForm">保存</el-button>
    </div>
  </PageContainer>
</template>
<script>
import { transData } from '@/util'
import positionTerminalDialog from './components/positionTerminalDialog.vue'
export default {
  name: 'addShiftPost',
  components: {
    positionTerminalDialog
  },
  data() {
    return {
      isSelectPers: false, // 选择人员
      // 正常表单
      formModel: {
        ageMin: null, // 最小年龄
        ageMax: null, // 最大年龄
        educationalDictId: '', // 学历要求id
        educationalDictName: '', // 学历要求名称
        certificateDictId: '', // 证书要求id
        certificateDictName: '', // 证书要求name
        name: '', // 值班岗位名称
        postId: '', // 岗位要求id
        postName: [], // 岗位名称
        regionCode: [], // 院区code
        regionName: '', // 院区name
        postDuty: '', // 岗位职责
        scopeOfRights: '', // 权力范围
        serviceArea: '', // 负责区域
        regionalArea: '' // 负责区域面积
      },
      rules: {
        postName: [{ required: true, message: '请选择岗位要求', trigger: 'blur' }],
        name: [{ required: true, message: '请输入值班岗名称', trigger: 'blur' }]
      },
      gridCodePropsType: {
        children: 'children',
        label: 'ssmName',
        value: 'id',
        checkStrictly: true,
        multiple: true
      },
      postPropsType: {
        children: 'child',
        label: 'name',
        value: 'code'
      },
      postOptions: [], // 岗位要求
      tableLoading: false,
      tableData: [],
      gridCodeList: [], // 空间
      educationOptions: [], // 学历要求
      certificateOptions: [], // 证书下拉
      tableLoadingStatus: false,
      positionTerminalShow: false, // 定位终端弹窗
      selectaDialogData: '', // 定位终端数据回显
      queryParams: {},
      selectTree: [],
      checkCode: [], // 默认选中
      // 岗位SOP相关
      sopList: [], // 已选择的SOP列表
      sopDialogVisible: false, // SOP选择弹窗
      sopSelectList: [], // SOP选择列表
      selectedSops: [], // 已在弹窗中选择的SOP
      searchKeyword: '', // 搜索关键词
      pageParams: {
        pageNum: 1,
        pageSize: 15
      },
      total: 0 // 总记录数
    }
  },
  mounted() {
    this.queryParams = this.$route.query
    this.init()
  },
  methods: {
    // 初始化
    init() {
      this.getDictListFn()
      this.getParentCodeData()
      this.getCourtyardFn()
      this.getCertificateData()
      // 编辑：获取数据
      if (!this.queryParams.id) return
      this.getDetailData()
    },
    // 点击node
    handleCheckChange() {
      const checkedNodes = this.$refs.postCodes.getCheckedNodes() // 获取所有被选中的节点
      const checkedNames = checkedNodes.map((node) => node.name) // 提取节点名称
      const checkedCodes = checkedNodes.map((node) => node.code) // 提取节点id
      this.formModel.postName = checkedNames
      checkedNodes.forEach((item) => {
        this.selectTree.push({ code: item.code, label: item.name })
      })
      this.formModel.postId = checkedCodes.join(',')
    },
    // 获取详情数据
    getDetailData() {
      this.$api.supplierAssess
        .getDutyPostById({ id: this.queryParams.id })
        .then((res) => {
          const { code, data } = res
          if (code == 200) {
            let regionCodes = []
            if (data.regionCode) {
              data.regionCode.split('/').forEach((item) => {
                regionCodes.push(item.split(','))
              })
            }
            this.formModel = data
            this.formModel.educationalDictId = data.educationalDictId ? data.educationalDictId.split(',') : ''
            this.formModel.regionCode = regionCodes
            this.formModel.postName = data.postName.split(',')
            this.checkCode = data.postId.split(',')
            this.tableData = data.list?.length ? data.list : []
            this.sopList = data.supPostSopList || []
            if (data.certificateDictId) {
              let certificateDictIdArr = data.certificateDictId ? data.certificateDictId.split(',') : ''
              let certificateDictIdNewArr = certificateDictIdArr.map((item) => Number(item))
              this.formModel.certificateDictId = certificateDictIdNewArr
            }
          } else {
            this.$message.error(res.msg || '获取数据失败!')
          }
        })
        .catch((err) => {
          this.$message.error(err.msg || '获取数据失败!')
        })
    },
    // 获取岗位数据
    getParentCodeData() {
      this.$api.supplierAssess.getPostChildData().then((res) => {
        if (res.code == '200' && res.data.length > 0) {
          this.postOptions = res.data
        }
      })
    },
    // 证书下拉
    getCertificateData() {
      this.$api.supplierAssess
        .getDictConfigUseData({
          type: 4,
          status: 1
        })
        .then((res) => {
          if (res.code == '200') {
            this.certificateOptions = res.data
          }
        })
    },
    // 最小年龄限制
    minAgeChange(val) {
      if (Number(val) > Number(this.formModel.ageMax) && Number(this.formModel.ageMax) != 0) {
        this.formModel.ageMin = null
      }
    },
    // 最大年龄限制
    maxAgeIChange(val) {
      if (Number(val) < Number(this.formModel.ageMin)) {
        this.formModel.ageMax = null
      }
    },
    // 空间change事件
    gridCodeChange(val) {
      if (val && val.length) {
        const names = []
        this.$refs.gridCodeCascader.getCheckedNodes().forEach((i) => {
          names.push(i.pathLabels.join(','))
        })
        this.formModel.regionName = names.join('/')
      }
    },
    // 定位终端确定
    submitTerminalDialog(list) {
      this.tableData = [{ ...list }]
      this.positionTerminalShow = false
    },
    // 关闭定位终端
    closeTerminalDialog() {
      this.positionTerminalShow = false
    },
    // 所在空间
    getCourtyardFn() {
      this.$api.spaceTree().then((res) => {
        if (res.code == '200') {
          let arr = res.data
          this.gridCodeList = transData(arr, 'id', 'pid', 'children')
        }
      })
    },
    getDictListFn() {
      // 文化程度
      this.$api.supplierAssess
        .getDictData({
          dictionaryCategoryId: 'EDUCATION_LEVEL_CATEGORY'
        })
        .then((res) => {
          if (res.code == 200) {
            this.educationOptions = res.data[0].children
          }
        })
    },
    // 学历获取name
    educationChange(val) {
      if (val && val.length) {
        const selectedLabels = val.map((el) => {
          return this.educationOptions.find((option) => option.id === el).name
        })
        this.formModel.educationalDictName = selectedLabels.join(',')
      }
    },
    // 证书获取name
    certificateChange(val) {
      if (val && val.length) {
        const selectedLabels = val.map((el) => {
          return this.certificateOptions.find((option) => option.id === el).name
        })
        this.formModel.certificateDictName = selectedLabels.join(',')
      }
    },
    // 选择终端
    onCreate() {
      let arr = this.tableData.map((item) => {
        return item.id
      })
      this.selectaDialogData = arr.join(',')
      this.positionTerminalShow = true
    },
    deleteRow(row) {
      this.tableData = this.tableData.filter((obj) => obj.id != row.id)
    },
    // 岗位SOP相关方法
    // 打开SOP选择弹窗
    openSopDialog() {
      this.sopDialogVisible = true
      this.searchKeyword = ''
      this.pageParams.pageNum = 1
      this.getSopSelectList()
    },
    // 获取SOP选择列表
    getSopSelectList() {
      // 获取可选择的SOP列表，排除已选择的
      const params = {
        page: this.pageParams.pageNum,
        pageSize: this.pageParams.pageSize,
        queryKey: this.searchKeyword,
        status: '1', // 只显示启用的SOP
        selectIds: this.sopList.map((item) => item.id || item.sopId).join(',') // 排除已选择的SOP
      }
      this.$api.supplierAssess.querySupPostSopByPage(params).then((res) => {
        const { code, data, msg } = res
        if (code === '200') {
          const { records, total } = data
          this.sopSelectList = records
          this.total = total
        } else {
          this.$message.error(msg)
        }
      })
    },
    // 搜索SOP
    searchSop() {
      this.pageParams.pageNum = 1
      this.getSopSelectList()
    },
    // 重置搜索
    resetSearch() {
      this.searchKeyword = ''
      this.pageParams.pageNum = 1
      this.getSopSelectList()
    },
    // 处理表格选择变化
    handleSelectionChange(selection) {
      this.selectedSops = selection
    },
    // 分页大小变化
    handleSizeChange(val) {
      this.pageParams.pageSize = val
      this.getSopSelectList()
    },
    // 页码变化
    handleCurrentChange(val) {
      this.pageParams.pageNum = val
      this.getSopSelectList()
    },
    // 确认选择SOP
    confirmSelect() {
      if (this.selectedSops.length === 0) {
        this.$message.warning('请至少选择一个SOP')
        return
      }
      // 将选择的SOP添加到列表中，避免重复添加
      this.selectedSops.forEach((sop) => {
        if (!this.sopList.some((item) => item.id === sop.id)) {
          this.sopList.push(sop)
        }
      })
      this.sopDialogVisible = false
    },
    // 移除SOP
    removeSop(row, index) {
      this.$confirm('确定要移除该SOP吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.sopList.splice(index, 1)
          this.$message.success('移除成功')
        })
        .catch(() => {})
    },
    // 保存
    submitForm() {
      this.$refs.formRef.validate((valid) => {
        if (!valid) return
        const { id } = this.queryParams
        const params = {
          ...this.formModel,
          list: this.tableData.length ? this.tableData : [],
          id: id || ''
        }
        params.regionCode = params.regionCode.length ? params.regionCode.join('/') : ''
        params.ageMax = params.ageMax ? Number(params.ageMax) : null
        params.ageMin = params.ageMin ? Number(params.ageMin) : null
        params.educationalDictId = params.educationalDictId.length ? params.educationalDictId.join(',') : ''
        params.certificateDictId = params.certificateDictId.length ? params.certificateDictId.join(',') : ''
        params.postName = params.postName.join(',')
        // 添加SOP IDs
        params.supPostSopList = this.sopList.map((item) => {
          return {
            sopId: item.id || item.sopId,
            sopName: item.sopName,
            sopTitle: item.sopTitle,
            remark: item.remark,
            status: item.status
          }
        })
        let fn = id ? this.$api.supplierAssess.updateDutyPostInfo : this.$api.supplierAssess.saveDutyPostInfo
        fn(params).then((res) => {
          if (res.code == 200) {
            this.$message.success(res.msg)
            setTimeout(() => {
              this.$router.go(-1)
            }, 500)
          } else {
            this.$message.error(res.msg)
          }
        })
      })
    }
  }
}
</script>
<style scoped lang="scss">
.container-content > div {
  background-color: #fff;
  height: 100%;
  overflow: hidden;
  .inventory-content-title {
    border-bottom: 1px solid #e4e7ed;
    padding: 12px 24px;
    cursor: pointer;
    margin-bottom: 24px;
  }
  .inventory_table {
    height: calc(60% - 20px);
  }
  .content_box {
    padding: 0 24px;
    background: #fff;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    height: calc(100% - 80px);
    .text-red {
      color: #ff1919;
    }
  }
  .el-input,
  .el-select,
  .el-cascader {
    width: 80%;
  }
}
.setstyle {
  min-height: 200px;
  padding: 0 !important;
  margin: 0;
  overflow: auto;
  cursor: default !important;
}
/* 岗位SOP相关样式 */
.sop-section {
  margin: 20px 0;
  .section-header {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 15px;
    .section-title {
      font-size: 16px;
      font-weight: 500;
      margin-right: 10px;
    }
  }
}
/* 状态样式 */
.status-active {
  color: #67c23a;
}
.status-disabled {
  color: #f56c6c;
}
/* 弹窗样式 */
.sop-dialog {
  .search-container {
    display: flex;
    margin-bottom: 15px;
    .search-input {
      width: 280px;
      margin-right: 10px;
    }
  }
  .pagination-container {
    margin-top: 15px;
    .pagination-box {
      width: 100%;
    }
    .jump-text {
      margin: 0 5px;
    }
    .jump-input {
      width: 50px;
      margin: 0 5px;
    }
  }
}
/* 在全局样式添加 */
::v-deep .sop-dialog .el-dialog__body {
  padding: 15px 20px;
}
</style>
