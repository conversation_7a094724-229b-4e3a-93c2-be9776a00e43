<template>
  <PageContainer>
    <div slot="header">
      <div class="search-header">
        <div class="sino-tabs">
          <div class="sino-tabs-item">
            <span :class="['item-text', isActive == '7' ? 'active' : '']" @click="taskChange('7')">全部</span>
          </div>
          <div v-for="item of taskStateOption" :key="item.value" class="sino-tabs-item">
            <span :class="['item-text', isActive == item.value ? 'active' : '']" @click="taskChange(item.value)">{{ item.label }}</span>
          </div>
        </div>
        <div class="search-from">
          <el-input v-model.trim="formInline.questionCode" placeholder="编号" style="width: 200px"></el-input>
          <el-cascader
            v-model="formInline.repairPlaceIds"
            clearable
            class="sino_sdcp_input mr15"
            :options="areaArr"
            :props="props"
            placeholder="隐患区域"
            @change="handleAreaChange"
          ></el-cascader>
          <el-select v-if="isActive == '7'" v-model="formInline.state" clearable placeholder="处理状态">
            <el-option v-for="(item, index) in taskStateOption" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
          <el-button type="primary" @click="reset">重置</el-button>
          <el-button type="primary" @click="searchClick">查询</el-button>
          <el-button type="primary" icon="el-icon-search" @click="openSeach">高级搜索</el-button>
        </div>
        <div class="middle_tools">
          <el-button type="primary" icon="el-icon-download" :disabled="!multipleSelection.length >= 1" @click="downFile('exportReportWord')">导出报告单</el-button>
          <el-button type="primary" icon="el-icon-download" :disabled="!multipleSelection.length >= 1" @click="downFile('exportNoticeWord')">导出通知单</el-button>
          <el-button type="primary" icon="el-icon-download" @click="exportFile()">导出隐患清单</el-button>
        </div>
      </div>
    </div>
    <div slot="content" style="height: 100%; margin-top: 16px; background-color: #fff">
      <div class="table-content">
        <el-table
          v-loading="tableLoading"
          stripe
          :data="purchaseTable"
          :border="true"
          :height="tableHeight"
          title="双击查看详情"
          @row-dblclick="watchDetail"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" fixed="left" align="center"></el-table-column>
          <el-table-column type="index" label="序号" width="65">
            <template slot-scope="scope">
              <span>{{ (paginationData.pageNo - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="questionCode" label="编号" show-overflow-tooltip width="150"> </el-table-column>
          <el-table-column prop="flowType" label="状态" show-overflow-tooltip width="80"></el-table-column>
          <el-table-column prop="questionDetailType" label="隐患分类" show-overflow-tooltip></el-table-column>
          <el-table-column prop="questionAddress" label="隐患区域" show-overflow-tooltip width="150"></el-table-column>
          <el-table-column prop="createByDeptName" label="反馈部门" show-overflow-tooltip width="180">
            <template slot-scope="scope">
              <span v-if="scope.row.questionBelongs == '0'" class="identification el-icon-s-flag">医管局</span>
              <span>{{ scope.row.createByDeptName }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="createPersonName" label="反馈人" show-overflow-tooltip width="140"></el-table-column>
          <el-table-column prop="createTime" label="反馈时间" show-overflow-tooltip width="180"></el-table-column>
          <el-table-column prop="riskName" label="隐患等级" show-overflow-tooltip width="110"></el-table-column>
          <el-table-column prop="rectificationPlanTime" label="要求整改完成时间" show-overflow-tooltip width="180"></el-table-column>
          <el-table-column v-if="!(isActive == 5 || isActive == 7 || isActive == 4)" label="操作" width="140">
            <template slot-scope="scope">
              <div class="btns">
                <span
                  v-if="isActive == 0"
                  @click="
                    claimDialogVisible = true
                    currentRowData = scope.row
                  "
                  >认领</span
                >
                <span
                  v-if="isActive == 0"
                  @click="
                    assignDialogVisible = true
                    currentRowData = scope.row
                  "
                  >指派</span
                >
                <span v-if="(isActive == 1 || isActive == 3) && scope.row.submitCredit != '1'" @click="openRectifyDialog(scope)">整改</span>
                <span
                  v-if="isActive == 1 && scope.row.submitCredit != '1'"
                  @click="
                    accountDialogVisible = true
                    currentRowData = scope.row
                  "
                  >挂帐</span
                >
                <span
                  v-if="(isActive == 2 || isActive == 5) && scope.row.questionBelongs == '1'"
                  @click="
                    auditsDialogVisible = true
                    currentRowData = scope.row
                  "
                  >审核</span
                >
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="contentTable-footer">
          <el-pagination
            style="margin-top: 3px"
            :current-page="paginationData.pageNo"
            layout="total, sizes, prev, pager, next, jumper"
            :total="paginationData.total"
            :page-size="paginationData.pageSize"
            :page-sizes="[15, 30, 50]"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
      <advancedSearch :closeState="advancClose" class="advanced-search" @isCloseState="getData" @resetSearch="reset" @searchList="searchList">
        <template slot="content">
          <el-form ref="formInline" :model="formInline" :inline="true" class="advanced-search-form" label-position="right" label-width="80px">
            <div class="sino-filter-row">
              <div class="sino-filter-col">
                <el-form-item label="隐患等级" prop="riskCode">
                  <el-select v-model="formInline.riskCode" clearable placeholder="请选择隐患等级" style="width: 230px">
                    <el-option v-for="item of riskLevelList" :key="item.id" :label="item.dictName" :value="item.dictCode"></el-option>
                  </el-select>
                </el-form-item>
                <div class="sino-filter-col">
                  <el-form-item label="隐患分类" prop="questionDetailCode">
                    <el-cascader
                      ref="classifyCascader"
                      v-model="rectifyForm.questionDetailCode"
                      style="width: 230px"
                      :options="hiddenClassifyList"
                      :props="{ expandTrigger: 'hover', checkStrictly: true, value: 'id', emitPath: true }"
                      placeholder="请选择隐患分类"
                      @change="classifyChange"
                    ></el-cascader>
                  </el-form-item>
                </div>
              </div>
            </div>
            <div class="sino-filter-row">
              <div class="sino-filter-col">
                <el-form-item label="反馈人" porp="createPersonName">
                  <el-input v-model.trim="formInline.createPersonName" placeholder="请输入反馈人" style="width: 230px"></el-input>
                </el-form-item>
              </div>
            </div>
            <div class="sino-filter-row">
              <div class="sino-filter-col">
                <el-form-item label="反馈时间" prop="reportTimeList">
                  <el-date-picker
                    v-model="formInline.reportTimeList"
                    :editable="false"
                    :clearable="false"
                    class="sino_sdcp_input mr15 filters-date-picker"
                    type="daterange"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    @change="handleTime"
                  ></el-date-picker>
                </el-form-item>
              </div>
            </div>
          </el-form>
        </template>
      </advancedSearch>
      <el-dialog title="隐患认领" :visible.sync="claimDialogVisible" width="35%" custom-class="model-dialog" @close="closeClaimDialog">
        <el-form :model="claimForm" style="background-color: #fff; width: 100%">
          <el-form-item label="认领科室:" label-width="180px">
            <span>{{ loginData.officeName }}</span>
          </el-form-item>
          <el-form-item label="预计整改完成时间:" label-width="180px">
            <el-date-picker v-model="claimDate" value-format="yyyy-MM-dd" type="date" placeholder="选择日期"> </el-date-picker>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="closeClaimDialog">取 消</el-button>
          <el-button type="primary" @click="claim">确 定</el-button>
        </div>
      </el-dialog>
      <el-dialog title="隐患指派" :visible.sync="assignDialogVisible" width="35%" custom-class="model-dialog" @close="closeAssignDialog">
        <el-form :model="assignForm" style="background-color: #fff; width: 100%">
          <el-form-item label="责任科室" label-width="180px">
            <el-select v-model="assignForm.dutyDeptCode" placeholder="请选择部门" @change="dutyDeptChanged">
              <el-option v-for="item in responsibleDepartmentList" :key="item.id" :label="item.teamName" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="责任人员" label-width="180px">
            <el-select v-model="assignForm.claimPersonCode" placeholder="请选择人员" :disabled="!(dutyPeopleList && dutyPeopleList.length > 0)">
              <el-option v-for="item in dutyPeopleList" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="closeAssignDialog">取 消</el-button>
          <el-button type="primary" @click="assign">确 定</el-button>
        </div>
      </el-dialog>
      <el-dialog title="隐患审核" :visible.sync="auditsDialogVisible" width="35%" custom-class="model-dialog" @close="closeAuditsDialog">
        <el-form ref="auditsForm" :model="auditsForm" :rules="auditsFormRules" label-width="120px" style="background-color: #fff; width: 100%">
          <el-form-item label="审核结果:" prop="auditType">
            <el-radio-group v-model="auditsForm.auditType">
              <el-radio label="1">通过</el-radio>
              <el-radio label="2">驳回</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="审核评价:" prop="rectificatioEvaluate">
            <el-input v-model="auditsForm.rectificatioEvaluate" type="textarea" maxlength="120" show-word-limit :autosize="{ minRows: 4, maxRows: 5 }"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="closeAuditsDialog">取 消</el-button>
          <el-button type="primary" @click="audit">确 定</el-button>
        </div>
      </el-dialog>
      <el-dialog title="隐患整改" :visible.sync="rectifyDialogVisible" width="50%" custom-class="model-dialog" class="rectify-dialog" @close="closeRectifyDialog">
        <el-form ref="rectifyForm" v-loading="rectifyDialogLoading" :model="rectifyForm" label-width="120px" style="background-color: #fff; width: 100%">
          <el-form-item label="整改时间:" prop="resource">
            <el-date-picker v-model="currentTime" placeholder="选择日期" readonly type="datetime" value-format="yyyy-MM-dd HH:mm:ss"> </el-date-picker>
          </el-form-item>
          <el-form-item label="隐患分类" prop="questionDetailCode">
            <el-cascader
              ref="classifyCascader"
              v-model="rectifyForm.questionDetailCode"
              :options="hiddenClassifyList"
              :props="{ expandTrigger: 'hover', checkStrictly: true, value: 'id', emitPath: true }"
              placeholder="请选择隐患分类"
              @change="classifyChange"
            ></el-cascader>
          </el-form-item>
          <el-form-item label="整改详情" prop="finishContent">
            <el-input v-model="rectifyForm.finishContent" type="textarea" maxlength="500" show-word-limit :autosize="{ minRows: 4, maxRows: 5 }"></el-input>
          </el-form-item>
          <el-form-item label="附件:" prop="file">
            <el-upload
              ref="uploadFile"
              drag
              multiple
              class="mterial_file"
              action="string"
              list-type="picture-card"
              :file-list="fileEcho"
              :http-request="httpRequest"
              accept=".jpg, .jpeg, .png, .JPG, .JPEG, .bmp"
              :limit="3"
              :on-exceed="handleExceed"
              :beforeUpload="beforeAvatarUpload"
              :on-remove="handleRemove"
              :on-change="fileChange"
              :auto-upload="false"
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text" style="top: 33px">
                将文件拖到此处，或
                <em>点击上传</em>
              </div>
              <div slot="tip" class="el-upload__tip">可上传三张以内,小于10M的图片</div>
            </el-upload>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="closeRectifyDialog">取 消</el-button>
          <el-button type="primary" @click="submitCheck">确 定</el-button>
        </div>
      </el-dialog>
      <el-dialog title="隐患挂帐" :visible.sync="accountDialogVisible" width="35%" custom-class="model-dialog" @close="closeAccountDialog">
        <el-form ref="accountFormRef" :model="accountForm" label-width="120px" :rules="accountFormRules" style="background-color: #fff; width: 100%">
          <el-form-item label="预计解决时间:" prop="estimateSolutionTime">
            <el-date-picker v-model="accountForm.estimateSolutionTime" :picker-options="pickerOptions" placeholder="选择日期" type="date" value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="挂单原因描述:" prop="paymentReason">
            <el-input v-model="accountForm.paymentReason" type="textarea" maxlength="120" show-word-limit :autosize="{ minRows: 4, maxRows: 5 }"></el-input>
          </el-form-item>
          <el-form-item label="现阶段防控措施:" prop="riskControl">
            <el-input v-model="accountForm.riskControl" type="textarea" maxlength="120" show-word-limit :autosize="{ minRows: 4, maxRows: 5 }"></el-input>
          </el-form-item>
          <el-form-item label="整改计划:" prop="rectificationPlan">
            <el-input v-model="accountForm.rectificationPlan" type="textarea" maxlength="120" show-word-limit :autosize="{ minRows: 4, maxRows: 5 }"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="closeAccountDialog">取 消</el-button>
          <el-button type="primary" @click="account">确 定</el-button>
        </div>
      </el-dialog>
    </div>
  </PageContainer>
</template>

<script>
import axios from 'axios'
import tableListMixin from '@/mixins/tableListMixin.js'
import advancedSearch from '@/components/advancedSearch'
import { transData } from '@/util'
export default {
  name: 'dangerManagement',
  components: { advancedSearch },
  mixins: [tableListMixin],
  data() {
    return {
      isActive: 7,
      formInline: {
        questionCode: '', // 处理编号
        // riskType: '', // 隐患类型
        // riskTypeSecond: '', // 隐患类型2级
        riskLevel: '', // 隐患等级
        reportTimeList: [], // 反馈时间
        reportTimeStart: '', // 反馈开始时间
        reportTimeEnd: '', // 反馈结束时间
        // createPersonCode: '', // 反馈人
        // assignPersonCode: '', // 处理人
        state: '', // 处理状态
        questionAddressCode: '', // 隐患区域
        // dealTimelist: [], // 处理时间
        // dealTimeStart: '', // 处理时间开始
        // dealTimeEnd: '', // 处理时间结束
        repairPlaceIds: [],
        createPersonName: '' // 创建人名称
        // assignPersonName: '', // 处理人名称
        // orderFormFlag: '' // 隐患来源
      },
      areaArr: [], // 隐患区域
      // componyList: [],
      // conductorList: [], // 处理人列表
      companyList: [], // 反馈人列表
      taskStateOption: [
        // { label: "全部", value: 7 },
        { label: '待认领', value: 0 },
        { label: '待整改', value: 1 },
        { label: '已挂帐', value: 3 },
        { label: '已整改', value: 2 },
        { label: '已完结', value: 4 },
        { label: '已取消', value: 5 }
      ],
      purchaseTable: [],
      advancClose: true,
      tableLoading: false,
      assetsTypeIdArr: [],
      assetsTypeIdArr2: [],
      riskLevelList: [],
      propsType: {
        children: 'children',
        label: 'dictLabel',
        value: 'id',
        checkStrictly: true
      },
      props: {
        label: 'gridName',
        value: 'id',
        children: 'children',
        checkStrictly: true
      },
      // dialogVisibleImg: false,
      // imgArr: [],
      // stateType: '',
      multipleSelection: [],
      downLoading: false,
      claimDialogVisible: false, // 隐患认领框显示隐藏
      claimForm: {}, // 隐患认领表单
      assignDialogVisible: false, // 隐患指派框显示隐藏
      assignForm: {
        dutyDeptCode: '',
        claimPersonCode: ''
      }, // 隐患指派表单
      auditsDialogVisible: false, // 隐患审核框显示隐藏
      auditsForm: {
        auditType: '',
        rectificatioEvaluate: ''
      }, // 隐患审核表单
      rectifyDialogVisible: false, // 隐患整改框显示隐藏
      rectifyForm: {
        questionDetailCode: '',
        questionDetailCodes: '',
        questionDetailType: '',
        finishPicUrl: '',
        finishContent: ''
      }, // 隐患整改表单
      fileArr: [], // 隐患整改上传文件列表
      accountDialogVisible: false, // 隐患挂帐框显示隐藏
      accountForm: {
        estimateSolutionTime: '',
        paymentReason: '',
        riskControl: '',
        rectificationPlan: ''
      }, // 隐患挂帐框显示隐藏
      accountDate: '', // 隐患挂帐框预计解决时间
      claimDate: '',
      responsibleDepartmentList: [],
      dutyPeopleList: [],
      currentRowData: {},
      hiddenClassifyList: [],
      fileEcho: [],
      formData: '',
      rectifyDialogLoading: false,
      accountFormRules: {
        estimateSolutionTime: [{ type: 'string', required: true, message: '请选择日期', trigger: 'change' }],
        paymentReason: [{ required: true, message: '请填写挂单原因描述', trigger: 'blur' }]
      },
      // 审核校验
      auditsFormRules: {
        auditType: [{ required: true, message: '请选择审核结果', trigger: 'change' }],
        rectificatioEvaluate: [{ required: true, message: '请填写审核评价', trigger: 'blur' }]
      },
      hospitalList: [],
      currentTime: new Date(),
      pickerOptions: {
        disabledDate(date) {
          let startTime = new Date(new Date(new Date().toLocaleDateString()).getTime())
          return date.getTime() < startTime
        }
      },
      date: [],
      paginationData: {
        pageNo: 1,
        pageSize: 15,
        total: 0
      },
      loginData: JSON.parse(sessionStorage.getItem('LOGINDATA'))
    }
  },
  created() {
    this.requestType()
    this.getDataList()
  },
  methods: {
    /**
     * @description:  切换table
     * @param  {string} i 当前选中
     */
    taskChange(i) {
      i == 7 ? (this.formInline.state = '') : ''
      this.isActive = i
      this.paginationData.pageNo = 1
      this.getDataList()
    },
    handleSizeChange(val) {
      this.paginationData.pageNo = 1
      this.paginationData.pageSize = val
      this.getDataList()
    },
    handleCurrentChange(val) {
      this.paginationData.pageNo = val
      this.getDataList()
    },
    searchList() {
      this.advancClose = true
      this.searchClick()
    },
    searchClick() {
      this.paginationData.pageNo = 1
      this.getDataList()
    },
    handleTime(val) {
      this.formInline.startTime = val[0]
      this.formInline.endTime = val[1]
    },
    getData(data) {
      this.advancClose = data
    },
    // 查询列表
    getDataList() {
      let params = {}
      this.tableLoading = true
      params = {
        ...this.paginationData,
        ...this.formInline,
        flowCode: this.isActive == 7 ? this.formInline.state : this.isActive // 只有全部中可根据状态查询
      }
      // params.riskTypeId = this.formInline.riskType
      params.taskTeamId = ''
      params.userType = JSON.parse(sessionStorage.getItem('LOGINDATA')).roleCode
      params.userTeamId = ''

      delete params.total
      delete params.reportTimeList
      // delete params.dealTimelist
      params.questionDetailCode = this.rectifyForm.questionDetailCodes.toString()
      params.questionDetailType = this.rectifyForm.questionDetailType
      this.$api
        .ipsmGetHiddenManageList(params)
        .then((res) => {
          if (res.code == '200') {
            this.purchaseTable = res.data.list
            this.paginationData.total = res.data.sum
          } else if (res.message) {
            this.$message.error(res.message)
          }
          this.tableLoading = false
        })
        .catch(() => {
          this.tableLoading = false
        })
    },
    handleAreaChange(val) {
      if (val) {
        this.formInline.questionAddressCode = val[val.length - 1]
      }
    },
    reset() {
      // this.$store.commit('changeImasTableLabel', 'init')
      // this.$store.commit('changeNetworkError', '查询失败，请检查您的网络…')
      this.formInline.questionCode = ''
      // this.assetsTypeIdArr = []
      // this.assetsTypeIdArr2 = []
      // this.formInline.riskType = ''
      this.formInline.questionAddressCode = ''
      this.formInline.repairPlaceIds = []
      this.formInline.state = ''
      this.formInline.reportTimeList = []
      this.formInline.reportTimeStart = []
      this.formInline.reportTimeEnd = []
      this.$refs['formInline'].resetFields()
      // this.formInline.createPersonCode = ''
      this.formInline.createPersonName = ''
      // this.formInline.assignPersonName = ''
      // this.formInline.assignPersonCode = ''
      // this.formInline.orderFormFlag = ''
      this.formInline.riskCode = ''
      this.formInline.startTime = ''
      this.formInline.endTime = ''
      this.rectifyForm.questionDetailCode = ''
      this.rectifyForm.questionDetailCodes = ''
      this.date = []
      if (this.type && this.type == 'pro') {
        this.formInline.hospitalCode = ''
      }
      this.paginationData = {
        pageNo: 1,
        pageSize: 15,
        total: 0
      }
      this.getDataList()
    },
    requestType() {
      this.$api.ipsmGetGridList().then((res) => {
        if (res && res.code == 200) {
          this.areaArr = transData(res.data, 'id', 'parentId', 'children')
        }
      })
      //   // 反馈人 处理人
      //   this.$http.riskWorkGetControlTeamUserList({}).then((res) => {
      //     this.conductorList = res.data
      //     this.companyList = res.data
      //   })
      //   // 科室
      this.$api.ipsmGetResponsibleDepartment().then((res) => {
        if (res.code == 200) {
          this.responsibleDepartmentList = res.data
        } else {
          this.$message.error(res.message)
        }
      })
      // 隐患分类
      this.$api.ipsmGetHiddenClassifyList().then((res) => {
        if (res.code == 200) {
          this.hiddenClassifyList = res.data
        } else {
          this.$message.error(res.message)
        }
      })
      // 隐患等级
      this.$api.ipsmGetDictList({ dictType: 'hidden_trouble_grade_type' }).then((res) => {
        if (res.code == 200) {
          this.riskLevelList = res.data
        } else {
          this.$message.error(res.message)
        }
      })
    },
    exportFile() {
      // console.log(this.$http);
      // return
      this.downLoading = true
      let riskIdList = []
      this.multipleSelection.map((item) => {
        riskIdList.push(item.id)
      })
      let riskId = riskIdList.join(',')
      // let URL_API = 'exportExcel'
      // this.$http[URL_API]({
      //   ...this.formInline,
      //   ids: riskId,
      // }).then((res) => {
      //   this.downLoading = false
      // })
      let baseInfo = sessionStorage.getItem('LOGINDATA') ? JSON.parse(sessionStorage.getItem('LOGINDATA')) : ''
      let httpname = 'safetyManager/hospitalQuestion/exportHiddenInventory'
      let params = {
        unitName: baseInfo.unitName,
        unitCode: baseInfo.unitCode,
        hospitalCode: baseInfo.hospitalCode,
        hospitalName: baseInfo.hospitalName,
        officeName: baseInfo.officeName,
        officeId: baseInfo.officeId,
        ids: riskId,
        questionCode: this.formInline.questionCode,
        questionAddressCode: this.formInline.questionAddressCode,
        flowCode: this.isActive == 7 ? this.formInline.state : this.isActive,
        userId: baseInfo.id
      }
      axios({
        method: 'post',
        url: __PATH.VUE_AQ_URL + httpname,
        data: params,
        responseType: 'blob',
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      })
        .then((res) => {
          this.downLoading = false
          let name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
          let blob = new Blob([res.data]) // { type: "application/vnd.ms-excel" }
          let url = window.URL.createObjectURL(blob) // 创建一个临时的url指向blob对象
          // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
          let a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
          // 释放这个临时的对象url
          window.URL.revokeObjectURL(url)
        })
        .catch((res) => {
          console.log(res)
          this.downLoading = false
          this.$message.error('导出失败！')
        })
    },
    // 导出
    downFile(url) {
      this.downLoading = true
      let riskIdList = []
      this.multipleSelection.map((item) => {
        riskIdList.push(item.id)
      })
      let riskId = riskIdList.join(',')
      // let URL_API = url
      // this.$http[URL_API]({
      //   workOrderIds: riskId,
      // }).then((res) => {
      //   this.downLoading = false
      // })
      let baseInfo = sessionStorage.getItem('LOGINDATA') ? JSON.parse(sessionStorage.getItem('LOGINDATA')) : ''
      let httpname = ''
      if (url == 'exportReportWord') {
        httpname = 'safetyManager/hospitalQuestion/exportReportWord'
      } else if (url == 'exportNoticeWord') {
        httpname = 'safetyManager/hospitalQuestion/exportNoticeWord'
      }
      let params = {
        unitName: baseInfo.unitName,
        unitCode: baseInfo.unitCode,
        hospitalCode: baseInfo.hospitalCode,
        hospitalName: baseInfo.hospitalName,
        officeName: baseInfo.officeName,
        officeId: baseInfo.officeId,
        ids: riskId
      }
      axios({
        method: 'post',
        url: __PATH.VUE_AQ_URL + httpname,
        data: params,
        responseType: 'blob',
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      })
        .then((res) => {
          this.downLoading = false
          let name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
          console.log(name)
          let blob = new Blob([res.data]) // { type: "application/vnd.ms-excel" }
          let url = window.URL.createObjectURL(blob) // 创建一个临时的url指向blob对象
          // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
          let a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
          // 释放这个临时的对象url
          window.URL.revokeObjectURL(url)
        })
        .catch((res) => {
          this.downLoading = false
          this.$message.error('导出失败！')
        })
    },
    // 勾选
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    watchDetail(row) {
      this.$router.push({
        name: 'hiddenManagementDetails1',
        query: { id: row.id }
      })
    },
    openSeach() {
      // return this.$message.error('出错了...')
      this.advancClose = !this.advancClose
    },
    httpRequest() {
      this.formData = new FormData()
      this.fileEcho.forEach((item) => {
        console.log(item)
        this.formData.append('file', item.raw)
      })
      let loginData = JSON.parse(sessionStorage.getItem('LOGINDATA'))
      this.formData.append('hospitalCode', loginData.hospitalCode)
      axios({
        method: 'post',
        url: __PATH.VUE_AQ_URL + 'file/upload',
        data: this.formData,
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      })
        .then((res) => {
          this.rectifyForm.finishPicUrl = res.data.data.fileKey
          this.rectify()
        })
        .catch(() => {
          this.$message.error(res.data.message)
        })
    },
    closeClaimDialog() {
      this.claimDialogVisible = false
      this.claimDate = ''
    },
    claim() {
      if (!this.claimDate) {
        return this.$message.error('请选择预计整改完成时间')
      }
      let params = {
        claimFinishPlanTime: this.claimDate,
        id: this.currentRowData.id
      }
      this.$api.ipsmClaimQuestion(params).then((res) => {
        if (res.code == 200) {
          this.$message.success(res.message)
          this.requestType()
          this.getDataList()
        } else {
          this.$message.error(res.message)
        }
        this.claimDialogVisible = false
        this.claimDate = ''
      })
    },
    dutyDeptChanged(val) {
      this.$api.ipsmGetDeptPersonById({ departId: val }).then((res) => {
        if (res.code == 200) {
          this.dutyPeopleList = res.data
        } else {
          this.$message.error(res.message)
        }
      })
    },
    closeAssignDialog() {
      this.assignDialogVisible = false
      this.assignForm.dutyDeptCode = ''
      this.assignForm.claimPersonCode = ''
      this.dutyPeopleList = []
    },
    assign() {
      let dutyDeptName = ''
      let claimPersonName = ''
      this.responsibleDepartmentList.forEach((item) => {
        if (item.id == this.assignForm.dutyDeptCode) {
          dutyDeptName = item.teamName
        }
      })
      this.dutyPeopleList.forEach((item) => {
        if (item.id == this.assignForm.claimPersonCode) {
          claimPersonName = item.name
        }
      })
      let params = {
        dutyDeptCode: this.assignForm.dutyDeptCode,
        claimPersonCode: this.assignForm.claimPersonCode,
        dutyDeptName,
        claimPersonName,
        id: this.currentRowData.id
      }
      this.$api.ipsmHiddenPoolTransfer(params).then((res) => {
        if (res.code == 200) {
          this.$message.success(res.message)
          this.requestType()
          this.getDataList()
        } else {
          this.$message.error(res.message)
        }
        this.closeAssignDialog()
      })
    },
    closeAuditsDialog() {
      this.auditsDialogVisible = false
      this.auditsForm.auditType = ''
      this.auditsForm.rectificatioEvaluate = ''
    },
    audit() {
      this.$refs.auditsForm.validate((valid) => {
        if (valid) {
          let params = {
            id: this.currentRowData.id,
            rectificatioEvaluate: this.auditsForm.rectificatioEvaluate,
            auditType: this.auditsForm.auditType,
            flowCode: this.currentRowData.flowCode
          }
          this.$api.ipsmAuditQuestion(params).then((res) => {
            if (res.code == 200) {
              this.$message.success(res.message)
              this.requestType()
              this.getDataList()
            } else {
              this.$message.error(res.message)
            }
            this.closeAuditsDialog()
          })
        }
      })
    },
    classifyChange(val) {
      let list = this.$refs.classifyCascader.getCheckedNodes()
      let name = []
      list.forEach((e) => {
        name.push(e.pathLabels.join('>'))
      })
      this.rectifyForm.questionDetailType = name.toString()
      this.rectifyForm.questionDetailCodes = val.join(',')
    },
    submitCheck() {
      this.$refs.rectifyForm.validate((valid) => {
        if (valid) {
          this.rectifyDialogLoading = true
          if (this.fileEcho && this.fileEcho.length > 0) {
            this.httpRequest()
          } else {
            this.rectify()
          }
        }
      })
    },
    handleExceed() {
      this.$message.error('最多上传三张图片')
    },
    beforeAvatarUpload(file) {
      const isLt40M = file.size / 1024 / 1024 < 10
      if (!isLt40M) {
        this.$message.error('上传图片大小不能超过 10MB!')
        return false
      }
      if (file.name.indexOf(',') != -1) {
        this.$message.error('非法的文件名')
        return false
      }
    },
    handleRemove() {},
    fileChange(file, fileList) {
      let imgSize = Number(file.size / 1024 / 1024)
      if (imgSize > 10) {
        // 删除文件
        fileList.splice(fileList.length - 1, 1)
        return this.$message.error('上传图片大小不能超过 10MB!')
      }
      this.fileEcho = fileList
    },
    rectify() {
      if (!this.rectifyForm.finishContent) {
        this.rectifyDialogLoading = false
        return this.$message.error('整改详情不能为空!')
      }
      let params = {
        id: this.currentRowData.id,
        questionDetailCode: this.rectifyForm.questionDetailCodes.toString() || this.currentRowData.questionDetailCode,
        questionDetailType: this.rectifyForm.questionDetailType || this.currentRowData.questionDetailType,
        finishContent: this.rectifyForm.finishContent,
        finishPicUrl: this.rectifyForm.finishPicUrl,
        flowCode: this.currentRowData.flowCode
      }
      this.$api.ipsmRectifyQuestion(params).then((res) => {
        if (res.code == 200) {
          this.$message.success(res.message)
          this.reset()
          this.requestType()
          this.getDataList()
        } else {
          this.$message.error(res.message)
        }
        this.closeRectifyDialog()
      })
    },
    closeRectifyDialog() {
      this.rectifyDialogLoading = false
      this.rectifyDialogVisible = false
      this.rectifyForm = {
        questionDetailCode: '',
        questionDetailCodes: '',
        questionDetailType: '',
        finishPicUrl: ''
      }
      this.fileEcho = []
      this.formData = ''
      this.$refs.rectifyForm.resetFields()
    },
    closeAccountDialog() {
      this.accountDialogVisible = false
      this.accountForm.estimateSolutionTime = ''
      this.accountForm.paymentReason = ''
      this.accountForm.riskControl = ''
      this.accountForm.rectificationPlan = ''
    },
    account() {
      this.$refs.accountFormRef.validate((valid) => {
        if (valid) {
          let { estimateSolutionTime, paymentReason, riskControl, rectificationPlan } = this.accountForm
          let params = {
            id: this.currentRowData.id,
            paymentReason,
            riskControl,
            rectificationPlan,
            estimateSolutionTime
          }
          this.$api.ipsmCreditQuestion(params).then((res) => {
            if (res.code == 200) {
              this.$message.success(res.message)
              this.requestType()
              this.getDataList()
            } else {
              this.$message.error(res.message)
            }
            this.closeAccountDialog()
          })
        }
      })
    },
    openRectifyDialog(scope) {
      this.rectifyDialogVisible = true
      this.currentRowData = scope.row
      this.currentTime = new Date()
      this.rectifyForm.questionDetailCode = scope.row.questionDetailCode.split(',')
    }
  }
}
</script>

<style lang="scss" scoped>
.search-header {
  padding: 10px 16px;

  .search-from {
    padding-bottom: 12px;

    & > div {
      margin-top: 12px;
      margin-right: 10px;
    }
  }
}

.table-content {
  padding: 16px;
  height: calc(100% - 80px);
}

.contentTable-footer {
  padding: 10px 0 0;
}

.sino-tabs {
  height: 43px;
  border-bottom: 1px solid #dcdfe6;

  .sino-tabs-item {
    display: inline-block;
    padding-right: 24px;
    height: 40px;
    line-height: 40px;

    .item-text {
      font-size: 16px;
      display: inline-block;
      // height: 48px;
      cursor: pointer;
      color: #606266;
    }

    .active {
      color: #5188fc;
      border-bottom: 3px solid #5188fc;
    }
  }
}

::v-deep .mterial_file > .el-upload-list {
  position: absolute;
  top: 0;
  width: 500px;
  margin-left: 310px;
  max-height: 160px;
  // overflow: auto;
}

::v-deep .mterial_file > .el-upload--picture-card {
  border: none;
  width: 300px;
}

::v-deep .mterial_file .el-upload .el-upload-dragger {
  width: 300px;
  height: 148px;
}

::v-deep .mterial_file .el-upload__text {
  position: absolute;
  left: 57px;
  color: #606266;
  font-size: 14px;
  text-align: center;
}

::v-deep .el-textarea {
  width: 300px;
}

.el-form {
  padding: 10px 20px;
}

.identification {
  color: #5188fc;
  font-size: 12px;
  margin-right: 5px;
}

.btns {
  span {
    margin: 0 8px;
    cursor: pointer;
  }

  span:nth-child(1) {
    color: #6a90fc;
  }

  span:nth-child(2) {
    color: #68ccc7;
  }

  span:nth-child(3) {
    color: #6a90fc;
  }

  span:nth-child(4) {
    color: #68ccc7;
  }

  span:nth-child(5) {
    color: #6a90fc;
  }
}
</style>
