<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    title="空间审核"
    width="60%"
    :visible.sync="visible"
    custom-class="model-dialog"
    :before-close="closeDialog"
    z-index="99999"
  >
    <div class="dialogContent">
      <div class="leftTree">
        <div class="toptip" style="padding-left: 0; border: 0; font-weight: bold;">
          <span class="green_line"></span>
          空间结构
        </div>
        <el-input v-model="filterText" placeholder="输入关键字"> </el-input>
        <el-tree
          ref="tree"
          :data="treeData"
          :props="treeProps"
          :highlight-current="true"
          node-key="id"
          :filter-node-method="filterNode"
          @node-click="nodeClick">
        </el-tree>
      </div>
      <div class="rightTable">
        <div class="topFilter">
          <el-select v-model="spaceFilter.functionDictId" placeholder="请选择空间功能用途" @keyup.enter.native="spaceSearch">
            <el-option v-for="item in spaceFunctionType" :key="item.id" :label="item.dictName" :value="item.id"> </el-option>
          </el-select>
          <el-input v-model="spaceFilter.localSpaceName" placeholder="空间本地名称" @keyup.enter.native="spaceSearch"></el-input>
          <el-input v-model="spaceFilter.localSpaceCode" placeholder="本地编码" @keyup.enter.native="spaceSearch"></el-input>
          <div>
            <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db;" @click="spaceReset">重置</el-button>
            <el-button type="primary" @click="spaceSearch">查询</el-button>
          </div>
        </div>
        <div class="selected">
          <span>已选择</span>
          <div class="tagsWrap">
            <el-tag v-for="tag in tags" :key="tag.id" type="info" closable @close="deleteTag(tag.id)">
              {{ tag.localSpaceName }}
            </el-tag>
          </div>
        </div>
        <div class="table">
          <el-table ref="spaceTable" v-loading="spaceLoading" :data="spaceTableData" style="width: 100%;" height="265" border @select="selectRow" @select-all="selectAll">
            <el-table-column align="center" type="selection" width="55"> </el-table-column>
            <el-table-column type="index" label="序号" width="60"> </el-table-column>
            <el-table-column prop="localSpaceName" label="空间本地名称" width="180" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="localSpaceCode" label="本地编码" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="simName" label="位置" width="180" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="functionDictName" label="功能类型" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="dmName" label="归属部门" show-overflow-tooltip> </el-table-column>
          </el-table>
          <div class="" style="padding-top: 10px; text-align: right;">
            <el-pagination
              layout="total, sizes, prev, pager, next, jumper"
              :current-page="paginationData.currentPage"
              :page-sizes="[10, 30, 50, 100]"
              :page-size="paginationData.pageSize"
              :total="paginationData.total"
              @size-change="sizeChange"
              @current-change="currentChange"
            ></el-pagination>
          </div>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="confirm('ruleForm')">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { transData } from '@/util'
export default {
  name: 'spaceAllocationDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      spaceLoading: false,
      allSpaceTreeData: [], // 未处理的空间树结构
      treeData: [], // 树数据来源
      spaceFunctionType: [], // 空间功能类型
      checkItem: '', // 当前树选中项
      spaceFilter: {
        functionDictId: '', // 已选功能类型
        localSpaceName: '', // 空间本地名称
        localSpaceCode: '' // 本地编码
      },
      spaceTableData: [], // 空间列表
      treeProps: {
        children: 'children',
        label: (data, node) => {
          return data.ssmName
        },
        isLeaf: 'leaf'
      },
      filterText: '',
      tags: [],
      // 空间设备分页
      paginationData: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      }
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    },
    visible(val) {
      if (val) {
        this.tags = []
        if (this.$refs.spaceTable) {
          this.$refs.spaceTable.clearSelection()
        }
        this.$nextTick(() => {
          this.$refs.tree.setCurrentKey(this.checkItem)
        })
      }
    }
  },
  created() {
    this.getSpaceTree()
    this.getFunctionType()
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true
      return data.ssmName.indexOf(value) !== -1
    },
    // 空间tree
    getSpaceTree() {
      this.$api.getStructureTree({}).then((res) => {
        if (res.code == '200') {
          this.allSpaceTreeData = res.data
          this.treeData = transData(res.data, 'id', 'pid', 'children')
          this.checkItem = this.treeData[0].id
          this.$nextTick(() => {
            this.$refs.tree.setCurrentKey(this.checkItem)
          })
          this.getSpaceList(this.checkItem)
        }
      })
    },
    // 空间功能类型
    getFunctionType() {
      this.$api
        .getFunctionType({
          typeValue: 'SP'
        })
        .then((res) => {
          if (res.code == '200') {
            this.spaceFunctionType = res.data
          }
        })
    },
    // 空间列表
    getSpaceList(treeId) {
      this.spaceLoading = true
      let data = {
        current: this.paginationData.currentPage,
        functionDictId: this.spaceFilter.functionDictId,
        localSpaceName: this.spaceFilter.localSpaceName,
        localSpaceCode: this.spaceFilter.localSpaceCode,
        simCode: '#,' + treeId || '',
        size: this.paginationData.pageSize
      }
      this.$api.getSpaceInfoList(data).then((res) => {
        if (res.code == '200') {
          this.spaceTableData = res.data.records
          this.paginationData.total = res.data.total
          this.spaceLoading = false
        }
      })
    },
    // 空间过滤
    spaceSearch() {
      this.getSpaceList(this.checkItem)
    },
    // 空间重置
    spaceReset() {
      this.spaceFilter.functionDictId = ''
      this.spaceFilter.localSpaceName = ''
      this.spaceFilter.localSpaceCode = ''
      this.getSpaceList(this.checkItem)
    },
    nodeClick(data, node) {
      let prentIds = []
      let aimId = data.pid
      for (let i = 0; i < data.ssmType; i++) {
        const prent = this.allSpaceTreeData.find((i) => i.id == aimId)
        if (prent) {
          prentIds.unshift(prent.id)
          aimId = prent.pid
        } else {
          prentIds.push(data.id)
        }
      }
      this.checkItem = prentIds.join(',')
      this.getSpaceList(prentIds.join(','))
    },
    sizeChange(val) {
      this.paginationData.pageSize = val
      this.paginationData.currentPage = 1
      this.getSpaceList(this.checkItem)
    },
    currentChange(val) {
      this.paginationData.currentPage = val
      this.getSpaceList(this.checkItem)
    },
    // 选择空间
    selectRow(selection, row) {
      this.tags = selection
    },
    // 全选空间
    selectAll(selection) {
      this.tags = selection
    },
    // 删除标签
    deleteTag(id) {
      this.tags.splice(
        this.tags.find((i) => i.id == id),
        1
      )
      const selectRow = this.spaceTableData.find(i => i.id == id)
      this.$refs.spaceTable.toggleRowSelection(selectRow, false)
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', !this.visible)
    },
    confirm() {
      this.$emit('confirm', this.tags)
      this.closeDialog()
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: linear-gradient(to right, #d9e1f8, #fff);
}

::v-deep .el-tree-node__content {
  height: 32px;
}

.dialogContent {
  width: 100%;
  display: flex;
  justify-content: space-between;

  .leftTree {
    background-color: #fff;
    padding: 0 16px;
    width: 250px;
    height: 425px;

    .filter-tree {
      height: 343px;
      overflow: auto;
    }

    :deep(.el-tree) {
      height: calc(100% - 82px);
      overflow: auto;
    }
  }

  .rightTable {
    background-color: #fff;
    width: calc(100% - 268px);
    height: 425px;
    padding: 0 16px;

    .topFilter {
      width: 100%;
      padding: 16px 0 0;
      display: flex;
      justify-content: space-between;

      :deep(.el-input) {
        width: 200px;
      }
    }

    .selected {
      height: 40px;
      margin: 10px 0;
      display: flex;
      align-items: center;

      > span {
        display: inline-block;
        margin-right: 10px;
        width: 42px;
      }

      .tagsWrap {
        width: calc(100% - 52px);
        height: 100%;
        display: flex;
        align-items: center;
        overflow-x: auto;
        overflow-y: hidden;

        >span {
          margin-right: 10px;
        }
      }

      .tagsWrap::-webkit-scrollbar {
        height: 10px;
      }
    }

    .table {
      :deep(.el-table) {
        .el-table__cell {
          padding: 10px 0;
        }
      }
    }
  }
}
// :deep(.el-dialog) {
//   .el-dialog__header {
//     border-bottom: 1px solid #dcdfe6;
//   }
//   .el-dialog__body {
//     background-color: #f6f5fa;
//     padding: 64px 24px 24px 24px;
//     height: calc(100% - 107px);
//   }
//   .el-dialog__footer {
//     padding: 10px 20px;
//   }
// }

</style>
