<template>
  <div class="">
    <template v-for="(item, index) in dlayoutItems">
      <dash-item
        v-if="item.id == '1'"
        v-bind.sync="dlayoutItems[index]"
        :key="item.id"
        @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)">
        <ContentCard
          title="资产总览"
          :scrollbarHover="true"
          :cstyle="{ height: '100%' }"
          class="drag_class"
          :hasMoreOper="['edit']"
          @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')"
        >
          <div slot="content" style="height: 100%;">
            <div class="overview">
              <div class="overview_item" style="background: rgba(53, 98, 219, 0.10);cursor: pointer;" @click="toList({costTypeCode: 1})">
                <div class="overview_item_left">
                  <span>{{ assetsInfoStatistics.fixedAsset?.count }}</span>
                  <p>资产总数/件</p>
                </div>
                <img class="overview_item_right" src="@/assets/images/policymaking/overview7.png" alt="">
              </div>
              <div class="overview_item" style="background: rgba(53, 98, 219, 0.10);">
                <div class="overview_item_left">
                  <span>{{ assetsInfoStatistics.fixedAsset?.amount }}</span>
                  <p>资产总价值/万元</p>
                </div>
                <img class="overview_item_right" src="@/assets/images/policymaking/overview12.png" alt="">
              </div>
              <div class="overview_item" style="background: rgba(255, 236, 232, 1);cursor: pointer;" @click="toList({costTypeCode: 1, isSyncStatus: 1})">
                <div class="overview_item_left">
                  <span>{{ assetsInfoStatistics.withinFixed?.count }}</span>
                  <p>帐内资产数量/件</p>
                </div>
                <img class="overview_item_right" src="@/assets/images/policymaking/overview1.png" alt="">
              </div>
              <div class="overview_item" style="background: rgba(255, 236, 232, 1);">
                <div class="overview_item_left">
                  <span>{{ assetsInfoStatistics.withinFixed?.amount }}</span>
                  <p>帐内资产价值/万元</p>
                </div>
                <img class="overview_item_right" src="@/assets/images/policymaking/overview2.png" alt="">
              </div>
              <div class="overview_item" style="background: rgba(232, 255, 234, 1);cursor: pointer;" @click="toList({costTypeCode: 1, isSyncStatus: 0})">
                <div class="overview_item_left">
                  <span>{{ assetsInfoStatistics.fixedOff?.count }}</span>
                  <p>帐外资产数量/件</p>
                </div>
                <img class="overview_item_right" src="@/assets/images/policymaking/overview3.png" alt="">
              </div>
              <div class="overview_item" style="background: rgba(232, 255, 234, 1);">
                <div class="overview_item_left">
                  <span>{{ assetsInfoStatistics.fixedOff?.amount }}</span>
                  <p>帐外资产价值/万元</p>
                </div>
                <img class="overview_item_right" src="@/assets/images/policymaking/overview4.png" alt="">
              </div>
              <div class="overview_item" style="background: rgba(255, 247, 232, 1);cursor: pointer;" @click="toList({costTypeCode: 0})">
                <div class="overview_item_left">
                  <span>{{ assetsInfoStatistics.lowAssets?.count }}</span>
                  <p>低值资产数量/件</p>
                </div>
                <img class="overview_item_right" src="@/assets/images/policymaking/overview5.png" alt="">
              </div>
              <div class="overview_item" style="background: rgba(255, 247, 232, 1);">
                <div class="overview_item_left">
                  <span>{{ assetsInfoStatistics.lowAssets?.amount }}</span>
                  <p>低值资产价值/万元</p>
                </div>
                <img class="overview_item_right" src="@/assets/images/policymaking/overview6.png" alt="">
              </div>
            </div>
          </div>
        </ContentCard>
      </dash-item>
      <dash-item
        v-if="item.id == '2'"
        v-bind.sync="dlayoutItems[index]"
        :key="item.id"
        @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)">
        <ContentCard
          title="资产使用年限分析"
          :scrollbarHover="true"
          :cstyle="{ height: '100%' }"
          class="drag_class"
          :hasMoreOper="['edit']"
          @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')"
        >
          <div slot="content" style="height: 100%;">
            <div id="analysisOfAssetServiceLife"></div>
          </div>
        </ContentCard>
      </dash-item>
      <dash-item
        v-if="item.id == '3'"
        v-bind.sync="dlayoutItems[index]"
        :key="item.id"
        @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)">
        <ContentCard
          title="资产状态分析"
          :scrollbarHover="true"
          :cstyle="{ height: '100%' }"
          class="drag_class"
          :hasMoreOper="['edit']"
          @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')"
        >
          <div slot="content" style="height: 100%;">
            <div class="overview">
              <div class="overview_item" style="background: rgba(53, 98, 219, 0.10);cursor: pointer;" @click="toList({})">
                <div class="overview_item_left">
                  <span>{{ getUseStatusStatisticsName('资产总数') }}</span>
                  <p>资产总数/件</p>
                </div>
                <img class="overview_item_right" src="@/assets/images/policymaking/overview7.png" alt="">
              </div>
              <div class="overview_item" style="background: rgba(53, 98, 219, 0.10);cursor: pointer;" @click="toList({useStatus: '1'})">
                <div class="overview_item_left">
                  <span>{{ getUseStatusStatisticsName('闲置') }}</span>
                  <p>闲置/件</p>
                </div>
                <img class="overview_item_right" src="@/assets/images/policymaking/overview11.png" alt="">
              </div>
              <div class="overview_item" style="background: rgba(232, 255, 234, 1);cursor: pointer;" @click="toList({useStatus: '2'})">
                <div class="overview_item_left">
                  <span>{{ getUseStatusStatisticsName('正常') }}</span>
                  <p>正常/件</p>
                </div>
                <img class="overview_item_right" src="@/assets/images/policymaking/overview8.png" alt="">
              </div>
              <div class="overview_item" style="background: rgba(255, 236, 232, 1);cursor: pointer;" @click="toList({useStatus: '0'})">
                <div class="overview_item_left">
                  <span>{{ getUseStatusStatisticsName('报废') }}</span>
                  <p>报废/件</p>
                </div>
                <img class="overview_item_right" src="@/assets/images/policymaking/overview9.png" alt="">
              </div>
              <div class="overview_item" style="background: rgba(255, 236, 232, 1);cursor: pointer;" @click="toList({useStatus: '3'})">
                <div class="overview_item_left">
                  <span>{{ getUseStatusStatisticsName('停用') }}</span>
                  <p>停用/件</p>
                </div>
                <img class="overview_item_right" src="@/assets/images/policymaking/overview9.png" alt="">
              </div>
              <div class="overview_item" style="background: rgba(255, 247, 232, 1);cursor: pointer;" @click="toList({useStatus: '4'})">
                <div class="overview_item_left">
                  <span>{{ getUseStatusStatisticsName('待维修') }}</span>
                  <p>待维修/件</p>
                </div>
                <img class="overview_item_right" src="@/assets/images/policymaking/overview14.png" alt="">
              </div>
              <div class="overview_item" style="background: rgba(53, 98, 219, 0.10);cursor: pointer;" @click="toList({useStatus: '5'})">
                <div class="overview_item_left">
                  <span>{{ getUseStatusStatisticsName('未领用') }}</span>
                  <p>未领用/件</p>
                </div>
                <img class="overview_item_right" src="@/assets/images/policymaking/overview11.png" alt="">
              </div>
              <div class="overview_item" style="background: rgba(255, 247, 232, 1);cursor: pointer;" @click="toList({useStatus: '7'})">
                <div class="overview_item_left">
                  <span>{{ getUseStatusStatisticsName('差错调整') }}</span>
                  <p>差错调整/件</p>
                </div>
                <img class="overview_item_right" src="@/assets/images/policymaking/overview13.png" alt="">
              </div>
            </div>
          </div>
        </ContentCard>
      </dash-item>
      <dash-item
        v-if="item.id == '4'"
        v-bind.sync="dlayoutItems[index]"
        :key="item.id"
        @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)">
        <ContentCard
          title="资产归口部门分析"
          :scrollbarHover="true"
          :cstyle="{ height: '100%' }"
          class="drag_class"
          :hasMoreOper="['edit']"
          @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')"
        >
          <div slot="content" style="height: 100%;">
            <div id="analysisOfAssetManagementDepartment"></div>
          </div>
        </ContentCard>
      </dash-item>
      <dash-item
        v-if="item.id == '5'"
        v-bind.sync="dlayoutItems[index]"
        :key="item.id"
        @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)">
        <ContentCard
          title="资产巡检分析"
          :scrollbarHover="true"
          :cstyle="{ height: '100%' }"
          class="drag_class"
          :hasMoreOper="['edit']"
          @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')"
        >
          <div slot="content" style="height: 100%;">
            <div id="assetInspectionAnalysis"></div>
          </div>
        </ContentCard>
      </dash-item>
    </template>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { DashItem } from 'vue-responsive-dash'
import fecha from 'element-ui/src/utils/date' // element-ui中处理时间的工具类
import { iomsUserInfon } from '@/util/dict.js'
export default {
  name: 'assetOperationsDM',
  components: {
    DashItem
  },
  props: {
    dlayoutItems: {
      type: Array,
      default: () => []
    },
    componentData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      assetsInfoStatistics: {}, // 资产总览
      useStatusStatistics: [] // 资产状态分析
    }
  },
  created() {
    this.getStatisticsAll()
  },
  methods: {
    toList(params) {
      this.$emit('toList', params)
    },
    getUseStatusStatisticsName(name) {
      let obj = this.useStatusStatistics.find(ele => ele.name == name)
      return obj ? obj.count : ''
    },
    setEcharts(domId, pieData, unit) {
      let myChart = echarts.init(document.getElementById(domId))
      let total = pieData.reduce((totalPrice, item) => totalPrice + item.value, 0)
      pieData.forEach(ele => {
        ele['proportion'] = total ? ((Math.round((ele.value / total) * 10000)) / 100).toFixed(2) : 0
      })
      myChart.setOption({
        color: ['#3562DB', '#FF9435', '#FA403C'],
        legend: {
          data: pieData.map(ele => ele.name),
          type: 'scroll',
          orient: 'vertical',
          icon: 'rect', // 形状
          itemWidth: 8, // 宽
          itemHeight: 8, // 高
          top: '30%',
          right: '0',
          selected: {},
          formatter: function (name) {
            var oa = pieData
            for (var i = 0; i < pieData.length; i++) {
              if (name === oa[i].name) {
                return `{name|${name}}{value|(${oa[i].value}${unit})}{proportion|${oa[i].proportion}%}`
              }
            }
          },
          textStyle: {
            rich: {
              name: {
                width: 60,
                fontSize: 14,
                padding: [0, 10, 0, 0],
                color: '#333333'
              },
              value: {
                width: 'auto',
                fontSize: 14,
                padding: [0, 10, 0, 0],
                color: '#3562DB'
              },
              proportion: {
                fontSize: 14,
                color: '#333333'
              }
            }
          }
        },
        series: [
          {
            name: '',
            type: 'pie',
            // startAngle: 80,
            radius: ['43%'],
            hoverAnimation: false,
            center: ['20%', '50%'],
            tooltip: {
              show: false
            },
            itemStyle: {
              color: 'rgba(204, 220, 252, .7)'
            },
            data: [100]
          },
          // {
          //   center: ['20%', '50%'], // 仪表的位置
          //   name: '', // 仪表的名字
          //   type: 'gauge', // 统计图类型为仪表
          //   radius: '55%', // 统计图的半径大小
          //   min: 0, // 最小刻度
          //   max: 100, // 最大刻度
          //   splitNumber: 7, // 刻度数量
          //   startAngle: 1, // 开始刻度的角度
          //   endAngle: 360, // 结束刻度的角度
          //   legendHoverLink: false,
          //   axisLine: { // 设置默认刻度盘上的刻度不显示，重新定义刻度盘
          //     show: false,
          //     lineStyle: {
          //       width: 1,
          //       color: [
          //         [1, 'rgba(255,255,255,0)']
          //       ]
          //     }
          //   }, // 仪表盘轴线
          //   axisLabel: { // 仪表盘上的数据
          //     show: false,
          //     color: '#4d5bd1', // 仪表盘上的轴线颜色
          //     distance: 2 // 图形与刻度的间距
          //   }, // 刻度标签。
          //   axisTick: {
          //     show: true,
          //     splitNumber: 6, // 刻度的段落数
          //     lineStyle: {
          //       color: '#CCDCFC',
          //       width: 1, // 刻度的宽度
          //       shadowColor: '#67FFFC',
          //       shadowBlur: 2
          //     },
          //     length: 8 // 刻度的长度
          //   }, // 刻度样式
          //   pointer: { // 表盘上的指针
          //     show: false
          //   },
          //   itemStyle: {
          //     color: '#18c8ff'
          //   },
          //   // data: [{

          //   // }],
          //   label: {
          //     show: false
          //   },
          //   splitLine: { // 文字和刻度的偏移量
          //     show: false
          //   } // 分隔线样式
          // },
          {
            name: '',
            type: 'pie',
            radius: ['60%', '75%'],
            center: ['20%', '50%'],
            avoidLabelOverlap: false,
            label: {
              normal: {
                show: false,
                position: 'center'
              }
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 20,
                color: '#333333',
                borderRadius: 50,
                formatter(params) {
                  let text = params.data.name
                  return (text = `{proportion|${params.data.proportion}%}\n {time|${text}}`)
                },
                rich: {
                  proportion: {
                    fontWeight: 'bold',
                    align: 'center',
                    verticalAlign: 'middle'
                  },
                  time: {
                    fontSize: 14,
                    color: '#3562DB',
                    lineHeight: 30
                  }
                }
              }
            },
            labelLine: {
              show: false
            },
            data: pieData
          }
        ]
      })
    },
    getStatisticsAll() {
      this.$api.statisticsAll(iomsUserInfon).then(res => {
        this.assetsInfoStatistics = res.data.assetsInfoStatistics
        this.useStatusStatistics = res.data.useStatusStatistics

        this.$nextTick(() => {
          // 柱状图 资产巡检分析
          let data = res.data.assetsClassStatistics.map(ele => {
            return {
              name: ele.name,
              value: ele.count
            }
          })
          this.drawAssetInspectionAnalysis(data)
          // 资产使用年限分析
          let dictObj = {
            '1': '0-3年',
            '2': '3-10年',
            '3': '10年以上'
          }
          let pieData = res.data.buyDateStatistics.map(ele => {
            return {
              name: dictObj[ele.name],
              value: ele.count
            }
          })
          this.setEcharts('analysisOfAssetServiceLife', pieData, '件')
          // 资产归口部门分析
          let dictObj2 = {
            '30021300': '总务处',
            '30021700': '信息中心',
            '30021900': '医学工程处'
          }
          let pieData2 = res.data.affiliationOfficeStatistics.map(ele => {
            return {
              name: dictObj2[ele.name],
              value: ele.amount
            }
          })
          this.setEcharts('analysisOfAssetManagementDepartment', pieData2, '万元')
        })
      })
    },
    // 资产巡检分析 柱状图
    drawAssetInspectionAnalysis(data) {
      const getchart = echarts.init(document.getElementById('assetInspectionAnalysis'))
      let colors = ['#3562DB', '#00BC6D', '#FF9435']
      data.sort((a, b) => {
        return b.value - a.value
      })
      data.forEach((ele, index) => {
        ele['itemStyle'] = {
          color: colors[index % 3]
        }
      })
      const nameList = Array.from(data, ({ name }) => name)
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        label: {
          show: false,
          position: 'right',
          color: '#121F3E',
          fontSize: '12px'
        },
        grid: {
          top: '4%',
          left: '4%',
          right: '6%',
          bottom: '15%',
          containLabel: false
        },
        dataZoom: [
          {
            type: 'slider',
            realtime: true,
            startValue: 0,
            endValue: 10,
            height: 4,
            fillerColor: 'rgba(17, 100, 210, 0.42)', // 滚动条颜色
            borderColor: 'rgba(17, 100, 210, 0.12)',
            handleSize: 0, // 两边手柄尺寸
            showDetail: false, // 拖拽时是否展示滚动条两侧的文字
            top: '96%',
            zoomLock: true // 是否只平移不缩放
            // moveOnMouseMove:true, //鼠标移动能触发数据窗口平移
            // zoomOnMouseWheel :true, //鼠标移动能触发数据窗口缩放
          },
          {
            type: 'inside', // 支持内部鼠标滚动平移
            start: 0,
            end: 40,
            zoomOnMouseWheel: false, // 关闭滚轮缩放
            moveOnMouseWheel: true, // 开启滚轮平移
            moveOnMouseMove: true // 鼠标移动能触发数据窗口平移
          }
        ],
        yAxis: {
          type: 'value',
          axisLine: {
            show: false,
            lineStyle: {
              color: '#414653'
            }
          },
          splitLine: {
            show: true,         // x轴切割线是否显示
            lineStyle: {
              color: 'rgba(65, 97, 128, 0.15)'    // x轴切割线颜色
            }
          },
          axisTick: {
            show: false
          }
        },
        xAxis: [
          {
            type: 'category',
            data: nameList,
            axisLabel: {
              formatter: function (val) {
                var strs = val.split('') // 字符串数组
                var str = ''
                // strs循环 forEach 每五个字符增加换行符
                strs.forEach((item, index) => {
                  if (index % 7 === 0 && index !== 0) {
                    str += '\n'
                  }
                  str += item
                })
                return str
              }
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: '#414653'
              }
            },
            axisTick: {
              show: false
            }
          }
        ],
        series: [
          {
            type: 'bar',
            barWidth: 10,
            data: data,
            itemStyle: {
              color: '#FF9435'
            }
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
*{
  margin: 0;
}
#analysisOfAssetServiceLife,
#assetInspectionAnalysis,
#analysisOfAssetManagementDepartment {
  width: 100%;
  height: 100%;
}
.overview {
  display: grid;
  grid-template-columns: auto auto auto auto;
  gap: 16px;
  height: 100%;
  &_item{
    padding: 20px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    &_left{
      span{
        font-size: 24px;
        font-family: Arial-Bold, Arial;
        font-weight: bold;
        color: #333333;
        line-height: 28px;
      }
      p{
        font-size: 15px;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        color: #414653;
        line-height: 14px;
      }
    }
    &_tight{
      width: 30px;
      height: 30px;
    }
  }
}
</style>
