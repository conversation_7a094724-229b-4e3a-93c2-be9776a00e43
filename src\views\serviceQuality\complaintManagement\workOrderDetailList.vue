<template>
  <div class="form-detail">
    <div class="reserve-scorll">
      <div v-for="(taskRecordObj, taskIndex) in workOrderDetail.taskRecord" :key="taskRecordObj.id" class="width: 100%">
        <!-- 创建工单 -->
        <div v-if="taskRecordObj.operationCode === '1'" class="reserve-plan">
          <div class="plan-title">
            <div class="color-box"><i class="el-icon-time"></i></div>
            <div class="linear-g" @click="collectEvent('CZ', taskIndex)">
              <span class="linear-g-span1">{{ taskRecordObj.operationType }}</span>
              <span>{{ workOrderDetail.olgTaskManagement.createDate }}</span>
              <i :ref="'CZright' + taskIndex" style="display: inline-block" class="el-icon-arrow-right title-icon"></i>
              <i v-show="false" :ref="'CZdown' + taskIndex" class="el-icon-arrow-down title-icon"></i>
            </div>
          </div>
          <div class="plan-content plan-content-line">
            <!-- <div class="show-content"> -->
            <TransitionHeight :ref="'CZ' + taskIndex" :heigtRef="'CZBox' + taskIndex">
              <ul class="item-row">
                <li class="width45">
                  <span class="li-first-span">工单号</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.workNum }}</span>
                </li>
                <li class="width45">
                  <span class="li-first-span">工单类型</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.workTypeName }}</span>
                </li>
              </ul>
              <ul class="item-row">
                <li class="width45">
                  <span class="li-first-span">申报来源</span><span class="li-last-span">{{ workOrderDetail.workSourceName }}</span>
                </li>
                <li class="width45">
                  <span class="li-first-span">投诉标题</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.complaintTitle }}</span>
                </li>
              </ul>
              <ul class="item-row">
                <li class="width45">
                  <span class="li-first-span">关联工单</span
                  ><span class="li-last-span" style="cursor: pointer; color: #4061d4" @click="jumpOrderDetail(workOrderDetail.olgTaskManagement.relevanceWorknum)">{{
                    workOrderDetail.olgTaskManagement.relevanceWorknum
                  }}</span>
                  <span class="li-first-span" style="margin-left: 16px">{{ workOrderDetail.olgTaskManagement.designateDeptName }}</span>
                </li>
              </ul>
              <!-- 0 1 3  呼叫中心 web app -->
              <ul
                v-if="
                  workOrderDetail.olgTaskManagement.workSources === '0' ||
                  workOrderDetail.olgTaskManagement.workSources === '1' ||
                  workOrderDetail.olgTaskManagement.workSources === '3'
                "
                class="item-row"
              >
                <li class="width90">
                  <span class="li-first-span">录音</span>
                  <div v-show="workOrderDetail.recordName" id="audio-box">
                    <audio controls>
                      <source :src="$tools.imgUrlTranslation(workOrderDetail.audioPath)" />
                      <!-- src="https://sinomis.oss-cn-beijing.aliyuncs.com/ioms/ZKYXYY/question/2021/06/24/mp3/20210624104642208466.mp3?Expires=1649988786&OSSAccessKeyId=LTAIeUfTgrfT5j7Y&Signature=QI12blGWwn1KzARBWOu7TdeD0rI%3D" -->
                    </audio>
                    <!-- <a href="javascript:;" onclick="downLoad(olgTaskManagement.audioPath)" title="下载">下载</a> -->
                  </div>
                  <span></span>
                </li>
              </ul>
              <!-- 1 3 web app -->
              <ul v-if="workOrderDetail.olgTaskManagement.workSources === '1' || workOrderDetail.olgTaskManagement.workSources === '3'" class="item-row">
                <li class="width90">
                  <span class="li-first-span">附件</span>
                  <!-- listAttUrl -->
                  <p v-show="workOrderDetail.listAttUrl">
                    <span v-for="(img, index) in workOrderDetail.listAttUrl" :key="index">
                      <el-image style="width: 100px; height: 100px; margin-right: 15px" :src="$tools.imgUrlTranslation(img)" :preview-src-list="[$tools.imgUrlTranslation(img)]">
                      </el-image>
                    </span>
                  </p>
                  <span></span>
                </li>
              </ul>
            </TransitionHeight>
            <ul class="item-row">
              <li class="width45">
                <span class="li-first-span">申报描述</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.questionDescription }}</span>
              </li>
            </ul>
            <ul class="item-row">
              <li class="width30">
                <span class="li-first-span">联系人</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.callerName }}</span>
              </li>
              <li class="width30">
                <span class="li-first-span">电话</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.sourcesPhone }}</span>
              </li>
              <li class="width30">
                <span class="li-first-span">工号</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.callerJobNum }}</span>
              </li>
            </ul>
            <ul class="item-row">
              <li class="width30">
                <span class="li-first-span">身份隐匿</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.signatureFlag == '1' ? '匿名投诉' : '署名投诉' }}</span>
              </li>
            </ul>
          </div>
        </div>
        <!-- 已受理 -->
        <div v-if="taskRecordObj.operationCode === '2'" class="reserve-plan">
          <div class="plan-title">
            <div class="color-box"><i class="el-icon-time"></i></div>
            <div class="linear-g" @click="collectEvent('SL', taskIndex)">
              <span class="linear-g-span1">{{ taskRecordObj.operationType }}</span>
              <span>{{ taskRecordObj.createDate }}</span>
              <i :ref="'SLright' + taskIndex" style="display: inline-block" class="el-icon-arrow-right title-icon"></i>
              <i v-show="false" :ref="'SLdown' + taskIndex" class="el-icon-arrow-down title-icon"></i>
            </div>
          </div>
          <div class="plan-content plan-content-line">
            <ul class="item-row">
              <li class="width45">
                <span class="li-first-span">调度员</span><span class="li-last-span">{{ taskRecordObj.createBy ? taskRecordObj.createBy.name : '' }}</span>
              </li>
              <li class="width45">
                <span class="li-first-span">职工工号</span><span class="li-last-span">{{ taskRecordObj.createBy ? taskRecordObj.createBy.no : '' }}</span>
              </li>
            </ul>
            <TransitionHeight :ref="'SL' + taskIndex" :heigtRef="'SLBox' + taskIndex">
              <ul v-if="workOrderDetail.olgTaskManagement.workSources === '1'" class="item-row">
                <!-- APP来电 -->
                <li class="width90">
                  <span class="li-first-span">来电号码</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.needPhone }}</span>
                </li>
              </ul>
              <!-- <ul class="item-row">
                    <li class="width45">
                      <span class="li-first-span">工单号</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.workNum }}</span>
                    </li>
                    <li class="width45">
                      <span class="li-first-span">工单类型</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.workTypeName }}</span>
                    </li>
                  </ul> -->
              <div v-if="workOrderDetail.handle !== '1'">
                <div v-for="(wx, index) in workOrderDetail.wxDetail" :key="index">
                  <ul class="item-row">
                    <li class="width90">
                      <span class="li-first-span">服务地点</span>
                      <span class="li-last-span">{{ wx[1] }}</span>
                      <!-- <span
                      ><a href="javascript:void(0)" class="recording-ALabel" @click="openTab(1, wx[4], wx[1])">
                        <i class="el-icon-tickets"></i>申报记录
                        </a></span
                      > -->
                    </li>
                  </ul>
                  <ul class="item-row">
                    <li class="width90">
                      <span class="li-first-span">所属科室</span>
                      <span class="li-last-span">{{
                        workOrderDetail.olgTaskManagement.sourcesDeptName === 'undefined' ? '' : workOrderDetail.olgTaskManagement.sourcesDeptName
                      }}</span>
                      <!-- <span
                      ><a
                        href="javascript:void(0)"
                        class="recording-ALabel"
                        @click="openTab(2, workOrderDetail.olgTaskManagement.sourcesDept, workOrderDetail.olgTaskManagement.sourcesDeptName)"
                      ><i class="el-icon-tickets"></i>申报记录</a
                      ></span
                      > -->
                    </li>
                  </ul>
                  <ul class="item-row">
                    <li class="width90">
                      <span class="li-first-span">服务事项</span>
                      <span v-if="wx[2] !== undefined && wx[2] !== '' && wx[3] !== undefined && wx[3] !== '' && wx[5] !== undefined && wx[5] !== ''" class="li-last-span">{{
                        wx[2] + '-' + wx[3] + '-' + wx[5]
                      }}</span>
                    </li>
                  </ul>
                </div>
              </div>
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span">服务部门</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.designateDeptName }}</span>
                </li>
              </ul>
              <ul class="item-row">
                <li class="width90" style="height: 50px; overflow: auto">
                  <span class="li-first-span">申报描述</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.questionDescription }}</span>
                </li>
              </ul>
            </TransitionHeight>
          </div>
        </div>
        <!-- 已派工 -->
        <div v-if="taskRecordObj.operationCode === '3'" class="reserve-plan">
          <div class="plan-title">
            <div class="color-box"><i class="el-icon-time"></i></div>
            <div class="linear-g" @click="collectEvent('PG', taskIndex)">
              <span class="linear-g-span1">{{ taskRecordObj.operationType }}</span>
              <span>{{ taskRecordObj.createDate }}</span>
              <i :ref="'PGright' + taskIndex" style="display: inline-block" class="el-icon-arrow-right title-icon"></i>
              <i v-show="false" :ref="'PGdown' + taskIndex" class="el-icon-arrow-down title-icon"></i>
            </div>
          </div>
          <div class="plan-content plan-content-line">
            <ul class="item-row">
              <li class="width45">
                <span class="li-first-span">服务人员</span><span class="li-last-span">{{ taskRecordObj.designatePersonName }}</span>
              </li>
              <li class="width45">
                <span class="li-first-span">人员电话</span><span class="li-last-span">{{ taskRecordObj.designatePersonPhone }}</span>
              </li>
            </ul>
            <TransitionHeight :ref="'PG' + taskIndex" :heigtRef="'PGBox' + taskIndex"> </TransitionHeight>
          </div>
        </div>
        <!-- 已挂单 -->
        <div v-if="taskRecordObj.operationCode === '4'" class="reserve-plan">
          <div class="plan-title">
            <div class="color-box"><i class="el-icon-time"></i></div>
            <div class="linear-g" @click="collectEvent('GD', taskIndex)">
              <span class="linear-g-span1">{{ taskRecordObj.operationType }}</span>
              <span>{{ taskRecordObj.createDate }}</span>
              <i :ref="'GDright' + taskIndex" style="display: inline-block" class="el-icon-arrow-right title-icon"></i>
              <i v-show="false" :ref="'GDdown' + taskIndex" class="el-icon-arrow-down title-icon"></i>
            </div>
          </div>
          <div class="plan-content plan-content-line">
            <ul class="item-row">
              <li class="width90">
                <span class="li-first-span">挂单说明</span><span class="li-last-span">{{ taskRecordObj.disEntryOrdersReason }}</span>
              </li>
            </ul>
            <TransitionHeight :ref="'GD' + taskIndex" :heigtRef="'GDBox' + taskIndex">
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span">解决方案</span><span class="li-last-span">{{ taskRecordObj.disEntryOrdersSolution }}</span>
                </li>
              </ul>
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span">预计解决</span><span class="li-last-span">{{ moment(taskRecordObj.disPlanSolutionTime).format('YYYY-MM-DD') }}</span>
                </li>
              </ul>
            </TransitionHeight>
          </div>
        </div>
        <!-- 已回访 -->
        <div v-if="taskRecordObj.operationCode === '5'" class="reserve-plan">
          <div class="plan-title">
            <div class="color-box"><i class="el-icon-time"></i></div>
            <div class="linear-g" @click="collectEvent('HF', taskIndex)">
              <span class="linear-g-span1">{{ taskRecordObj.operationType }}</span>
              <span>{{ taskRecordObj.createDate }}</span>
              <i :ref="'HFright' + taskIndex" style="display: inline-block" class="el-icon-arrow-right title-icon"></i>
              <i v-show="false" :ref="'HFdown' + taskIndex" class="el-icon-arrow-down title-icon"></i>
            </div>
          </div>
          <div class="plan-content plan-content-line">
            <ul class="item-row">
              <li class="width45">
                <span class="li-first-span">回访人</span><span class="li-last-span">{{ taskRecordObj.createBy ? taskRecordObj.createBy.name : '' }}</span>
              </li>
              <li class="width45">
                <span class="li-first-span">回访时间</span><span class="li-last-span">{{ taskRecordObj.createDate }}</span>
              </li>
            </ul>
            <TransitionHeight :ref="'HF' + taskIndex" :heigtRef="'HFBox' + taskIndex">
              <!-- 判断登录人医院是否是 某个医院才展示 -->
              <!-- <div>
                <ul class="item-row">
                  <li class="width90">
                    <span class="li-first-span">回访电话</span><span class="li-last-span">{{ taskRecordObj.callbackCusttel }}</span>
                  </li>
                </ul>
                <ul class="item-row">
                  <li class="width90">
                    <span class="li-first-span">回访录音</span>
                    <div v-show="taskRecordObj.callbackVoiceUrl" id="audio-box">
                      <audio controls>
                        <source :src="taskRecordObj.callbackVoiceUrl" />
                      </audio>
                    </div>
                    <span></span>
                  </li>
                </ul>
              </div> -->
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span">回访说明</span><span class="li-last-span">{{ taskRecordObj.feedbackExplain }}</span>
                </li>
              </ul>
            </TransitionHeight>
          </div>
        </div>
        <!-- 已督办 -->
        <div v-if="taskRecordObj.operationCode === '8'" class="reserve-plan">
          <div class="plan-title">
            <div class="color-box"><i class="el-icon-time"></i></div>
            <div class="linear-g" @click="collectEvent('DB', taskIndex)">
              <span class="linear-g-span1">{{ taskRecordObj.operationType }}</span>
              <span>{{ taskRecordObj.createDate }}</span>
              <i :ref="'DBright' + taskIndex" style="display: inline-block" class="el-icon-arrow-right title-icon"></i>
              <i v-show="false" :ref="'DBdown' + taskIndex" class="el-icon-arrow-down title-icon"></i>
            </div>
          </div>
          <div class="plan-content plan-content-line">
            <ul class="item-row">
              <li class="width90">
                <span class="li-first-span">督办说明</span><span class="li-last-span">{{ taskRecordObj.feedbackExplain }}</span>
              </li>
            </ul>
            <TransitionHeight :ref="'DB' + taskIndex" :heigtRef="'DBBox' + taskIndex"> </TransitionHeight>
          </div>
        </div>
        <!-- 已完工 -->
        <div v-if="taskRecordObj.operationCode === '6'" class="reserve-plan">
          <div class="plan-title">
            <div class="color-box"><i class="el-icon-time"></i></div>
            <div class="linear-g" @click="collectEvent('WG', taskIndex)">
              <span class="linear-g-span1">{{ taskRecordObj.operationType }}</span>
              <span>{{ taskRecordObj.createDate }}</span>
              <i :ref="'WGright' + taskIndex" style="display: inline-block" class="el-icon-arrow-right title-icon"></i>
              <i v-show="false" :ref="'WGdown' + taskIndex" class="el-icon-arrow-down title-icon"></i>
            </div>
          </div>
          <div class="plan-content plan-content-line">
            <ul class="item-row">
              <li class="width90">
                <span class="li-first-span">完工说明</span><span class="li-last-span">{{ taskRecordObj.disFinishRemark }}</span>
              </li>
            </ul>
            <TransitionHeight :ref="'WG' + taskIndex" :heigtRef="'WGBox' + taskIndex">
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span" style="width: 150px">耗材实际使用</span>
                  <table v-if="taskRecordObj.actual?.length" class="maint-table" style="table-layout: fixed">
                    <tbody>
                      <tr>
                        <td style="width: 80%">耗材名称</td>
                        <td>耗材数量</td>
                      </tr>
                      <tr v-for="(item, index) in taskRecordObj.actual" :key="index">
                        <td>
                          <div :title="item[1]" class="one-line">{{ item[1] }}</div>
                        </td>
                        <td>
                          <div :title="item[2]" class="one-line">{{ item[2] }}</div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </li>
              </ul>
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span" style="width: 150px">故障原因和维修方法</span>
                  <table v-if="taskRecordObj.taskMalfunctionList?.length" class="maint-table" style="table-layout: fixed">
                    <tbody>
                      <tr>
                        <td>故障原因</td>
                        <td>维修方法</td>
                      </tr>
                      <tr v-for="(item, index) in taskRecordObj.taskMalfunctionList" :key="index">
                        <td :title="item.reasonName">
                          <div class="one-line">{{ item.reasonName }}</div>
                        </td>
                        <td>
                          <div v-for="(methodListObj, i) in item.methodList" :key="i" class="one-line" :title="methodListObj.methodName">{{ methodListObj.methodName }}</div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </li>
              </ul>
              <ul v-if="true" class="item-row">
                <li class="width90">
                  <span class="li-first-span">总服务费</span
                  ><span class="li-last-span">{{ taskRecordObj.completePrice === 'undefined' ? '' : taskRecordObj.completePrice }}</span>
                </li>
              </ul>
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span">工单附件</span>
                  <!-- disAttachmentUrlList newUrl -->
                  <p v-if="taskRecordObj.disAttachmentUrl && taskRecordObj.disAttachmentUrl != ''">
                    <span v-for="(img, index) in taskRecordObj.disAttachmentUrl.split(',')" :key="index">
                      <el-image
                        style="width: 100px; height: 100px; margin-right: 15px"
                        :src="$tools.imgUrlTranslation(img)"
                        :preview-src-list="[$tools.imgUrlTranslation(img)]"
                      >
                      </el-image>
                    </span>
                  </p>
                  <span></span>
                </li>
              </ul>
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span">满意度评价</span>
                  <!-- <span class="li-last-span">{{ workOrderDetail.olgTaskManagement.disDegreeNew }}</span> -->
                  <span class="li-last-span">
                    <el-rate v-model="taskRecordObj.disDegreeNew" show-text text-color="#fff" disabled :texts="rateTexts"> </el-rate>
                  </span>
                </li>
              </ul>
            </TransitionHeight>
          </div>
        </div>
        <!-- 已取消 -->
        <div v-if="taskRecordObj.operationCode === '7'" class="reserve-plan">
          <div class="plan-title">
            <div class="color-box"><i class="el-icon-time"></i></div>
            <div class="linear-g" @click="collectEvent('QX', taskIndex)">
              <span class="linear-g-span1">{{ taskRecordObj.operationType }}</span>
              <span>{{ taskRecordObj.createDate }}</span>
              <i :ref="'QXright' + taskIndex" style="display: inline-block" class="el-icon-arrow-right title-icon"></i>
              <i v-show="false" :ref="'QXdown' + taskIndex" class="el-icon-arrow-down title-icon"></i>
            </div>
          </div>
          <div class="plan-content plan-content-line">
            <ul class="item-row">
              <li class="width90">
                <!-- getDictLabel cancel_reason -->
                <span class="li-first-span">取消理由</span><span class="li-last-span">{{ transform(workOrderDetail.olgTaskManagement.cancelReasonId) }}</span>
              </li>
            </ul>
            <TransitionHeight :ref="'QX' + taskIndex" :heigtRef="'QXBox' + taskIndex">
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span">取消说明</span><span class="li-last-span">{{ workOrderDetail.olgTaskManagement.cancelExplain }}</span>
                </li>
              </ul>
            </TransitionHeight>
          </div>
        </div>
        <!-- 已转单 -->
        <div v-if="taskRecordObj.operationCode === '9'" class="reserve-plan">
          <div class="plan-title">
            <div class="color-box"><i class="el-icon-time"></i></div>
            <div class="linear-g" @click="collectEvent('ZD', taskIndex)">
              <span class="linear-g-span1">{{ taskRecordObj.operationType }}</span>
              <span>{{ taskRecordObj.createDate }}</span>
              <i :ref="'ZDright' + taskIndex" style="display: inline-block" class="el-icon-arrow-right title-icon"></i>
              <i v-show="false" :ref="'ZDdown' + taskIndex" class="el-icon-arrow-down title-icon"></i>
            </div>
          </div>
          <div class="plan-content plan-content-line">
            <ul class="item-row">
              <li class="width90">
                <span class="li-first-span">服务部门</span><span class="li-last-span">{{ taskRecordObj.designateDeptName }}</span>
              </li>
            </ul>
            <TransitionHeight :ref="'ZD' + taskIndex" :heigtRef="'ZDBox' + taskIndex">
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span">转单说明</span><span class="li-last-span">{{ taskRecordObj.feedbackExplain }}</span>
                </li>
              </ul>
            </TransitionHeight>
          </div>
        </div>
        <!-- 已变更 -->
        <div v-if="taskRecordObj.operationCode === '10'" class="reserve-plan">
          <div class="plan-title">
            <div class="color-box"><i class="el-icon-time"></i></div>
            <div class="linear-g" @click="collectEvent('BG', taskIndex)">
              <span class="linear-g-span1">{{ taskRecordObj.operationType }}</span>
              <span>{{ taskRecordObj.createDate }}</span>
              <i :ref="'BGright' + taskIndex" style="display: inline-block" class="el-icon-arrow-right title-icon"></i>
              <i v-show="false" :ref="'BGdown' + taskIndex" class="el-icon-arrow-down title-icon"></i>
            </div>
          </div>
          <div class="plan-content plan-content-line">
            <ul class="item-row">
              <li class="width90">
                <span class="li-first-span">原服务部门</span><span class="li-last-span">{{ taskRecordObj.designateDeptName }}</span>
              </li>
            </ul>
            <TransitionHeight :ref="'BG' + taskIndex" :heigtRef="'BGBox' + taskIndex">
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span">原服务地点</span>
                  <span class="li-last-span">{{ taskRecordObj.localtionName }}</span>
                  <!-- <span
                  ><a href="javascript:void(0)" class="recording-ALabel" @click="openTab(1, taskRecordObj.localtion, taskRecordObj.localtionName)"
                  >
                  <i class="el-icon-tickets"></i>申报记录
                  </a
                  ></span
                  > -->
                </li>
              </ul>
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span">原所属科室</span>
                  <span class="li-last-span">{{ workOrderDetail.olgTaskManagement.sourcesDeptName }}</span>
                  <!-- <span
                  ><a href="javascript:void(0)" class="recording-ALabel" @click="openTab(2, taskRecordObj.sourcesDept, workOrderDetail.olgTaskManagement.sourcesDeptName)"
                  >
                  <i class="el-icon-tickets"></i>申报记录
                  </a
                  ></span
                  > -->
                </li>
              </ul>
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span">原服务事项</span>
                  <span v-if="taskRecordObj.itemTypeName !== null && taskRecordObj.itemTypeName !== ''" class="li-last-span">{{ taskRecordObj.itemTypeName }}</span
                  >- <span v-if="taskRecordObj.itemDetailName !== null && taskRecordObj.itemDetailName !== ''" class="li-last-span">{{ taskRecordObj.itemDetailName }}</span
                  >-
                  <span v-if="taskRecordObj.itemServiceName !== null && taskRecordObj.itemServiceName !== ''" class="li-last-span">{{ taskRecordObj.itemServiceName }}</span>
                </li>
              </ul>
              <!-- completePrice && workOrderDetail.olgTaskManagement.flowcode===5 已完工 -->
              <ul v-if="taskRecordObj.completePrice && workOrderDetail.olgTaskManagement.flowcode === '5'" class="item-row">
                <li class="width90">
                  <span class="li-first-span">总服务费</span><span class="li-last-span">{{ taskRecordObj.completePrice || '' }}</span>
                </li>
              </ul>
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span">原申报描述</span><span class="li-last-span">{{ taskRecordObj.questionDescription }}</span>
                </li>
              </ul>
            </TransitionHeight>
          </div>
        </div>
        <!-- 已转派 -->
        <div v-if="taskRecordObj.operationCode === '11'" class="reserve-plan">
          <div class="plan-title">
            <div class="color-box"><i class="el-icon-time"></i></div>
            <div class="linear-g" @click="collectEvent('ZP', taskIndex)">
              <span class="linear-g-span1">{{ taskRecordObj.operationType }}</span>
              <span>{{ taskRecordObj.createDate }}</span>
              <i :ref="'ZPright' + taskIndex" style="display: inline-block" class="el-icon-arrow-right title-icon"></i>
              <i v-show="false" :ref="'ZPdown' + taskIndex" class="el-icon-arrow-down title-icon"></i>
            </div>
          </div>
          <div class="plan-content plan-content-line">
            <ul class="item-row">
              <li class="width45">
                <span class="li-first-span">服务人员</span><span class="li-last-span">{{ taskRecordObj.designatePersonName }}</span>
              </li>
              <li class="width45">
                <span class="li-first-span">人员电话</span><span class="li-last-span">{{ taskRecordObj.designatePersonPhone }}</span>
              </li>
            </ul>
            <TransitionHeight :ref="'ZP' + taskIndex" :heigtRef="'ZPBox' + taskIndex">
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span">服务部门</span><span class="li-last-span">{{ taskRecordObj.designateDeptName }}</span>
                </li>
              </ul>
              <ul class="item-row">
                <li class="width90">
                  <span class="li-first-span">转派说明</span><span class="li-last-span">{{ taskRecordObj.feedbackExplain }}</span>
                </li>
              </ul>
            </TransitionHeight>
          </div>
        </div>
      </div>
      <!-- 处理 -->
      <!-- <div v-if="workOrderDetail.olgTaskManagement.flowcode !== '6' && !workOrderDetail.relevanceWorknumFlag" class="reserve-plan">
        <div class="plan-title">
          <div class="color-box"><i class="el-icon-time"></i></div>
          <div class="linear-g">
            <span class="linear-g-span1">处理</span>
            <span>{{ $tools.dateToStr() }}</span>
          </div>
        </div>
        <div class="repair-work">
          <el-checkbox v-model="workOrderDetail.olgTaskManagement.repairWork" true-label="2" false-label="1" disabled>返修工单</el-checkbox>
        </div>
      </div> -->
    </div>
  </div>
</template>

<script>
import TransitionHeight from './transitionHeight.vue'
import moment from 'moment'

let that
export default {
  name: 'workOrderDetailList',
  components: {
    TransitionHeight
  },
  filters: {
    UDfilter(val) {
      if (val === '0') {
        return '紧急事故'
      } else if (val === '1') {
        return '紧急催促'
      } else {
        return '一般'
      }
    }
    // cancelFilter(val) {
    //   const row = that.cancelReasonOptions.forEach((e) => {
    //     if (row.length && e.value === val) {
    //       this.cancelFilter = e.label
    //     } else {
    //       this.cancelFilter = ''
    //     }
    //   })
    // }
  },
  props: {
    rowData: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  data() {
    return {
      moment,
      cancelFilter: '',
      rateTexts: ['非常差', '差', '一般', '满意', '非常满意'],
      cancelReasonOptions: [],
      workOrderDetail: {
        olgTaskManagement: {
          repairWork: ''
        }
      }
    }
  },
  beforeCreate() {
    that = this
  },
  created() {
    const params = this.rowData
    this.getWorkOrderDetail(params)
  },
  mounted() {
    setTimeout(() => {
      this.collectEvent('CZ', 0)
    }, 100)
  },
  methods: {
    jumpOrderDetail() {},
    getWorkOrderDetail(params) {
      this.$api.getWorkOrderDetail({ id: params.id, operSource: 'souye' }).then((res) => {
        // console.log('@', res)
        this.workOrderDetail = res
        // 已取消 获取 字典项
        if (res.taskRecord.length && res.taskRecord.some((e) => e.operationCode === '7')) {
          this.getIomsDictList('cancel_reason')
        }
      })
    },
    // 获取字典
    getIomsDictList(type) {
      const params = {
        type: type
      }
      this.$api.getIomsDictList(params).then((res) => {
        this.cancelReasonOptions = res
      })
    },
    transform(val) {
      let arr = this.cancelReasonOptions.find((i) => {
        return i.value == val
      })
      if (arr) {
        return arr.label
      } else {
        return ''
      }
    },
    // 展开关闭事件
    collectEvent(box, i) {
      console.log(box, i)
      // console.log(this.$refs[box + 'right' + i][0].style.display)
      if (this.$refs[box + 'right' + i][0].style.display === 'inline-block') {
        this.$refs[box + 'right' + i][0].style.display = 'none'
        this.$refs[box + 'down' + i][0].style.display = 'inline-block'
        this.$refs[box + i][0].show = true
      } else {
        this.$refs[box + 'right' + i][0].style.display = 'inline-block'
        this.$refs[box + 'down' + i][0].style.display = 'none'
        this.$refs[box + i][0].show = false
      }
    },
    /*
     *  综合维修工单科室及地点的历史申报记录
     * type 对应查询类型：1：地点 2：科室
     * code 对应的科室地点code或者地点code
     * name 对应的科室地点name或者地点name
     */
    openTab(type, code, name) {
      let historyDept = '' // eslint-disable-line no-unused-vars
      let historyDeptName = '' // eslint-disable-line no-unused-vars
      let historyLocaltion = '' // eslint-disable-line no-unused-vars
      let historyLocaltionName = '' // eslint-disable-line no-unused-vars
      if (type === 1) {
        historyLocaltion = code
        if (code !== undefined && code !== '' && code !== null) {
          historyLocaltion = code.replace(/\_/g, ',') // eslint-disable-line no-useless-escape
        }
        historyLocaltionName = name
      } else if (type === 2) {
        historyDept = code
        historyDeptName = name
      }
      console.log(historyDept, historyDeptName, historyLocaltion, historyLocaltionName)
      this.$router.push({
        name: 'workOrderList',
        params: {
          isHistory: '1',
          historyDept: historyDept,
          historyLocaltion: historyLocaltion,
          historyDeptName: historyDeptName,
          historyLocaltionName: historyLocaltionName,
          workTypeCode: this.workOrderDetail.olgTaskManagement.workTypeCode
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.form-detail {
  height: 100%;
  width: 100%;
  padding: 10px 25px;
  overflow-y: scroll;
  box-sizing: border-box;

  .reserve-scorll {
    width: 100%;
    height: calc(100% - 50px);
    overflow-y: auto;
    box-sizing: border-box;
    padding-right: 15px;
  }

  .reserve-plan {
    width: 100%;
    position: relative;
    // height: inherit;
    .plan-title {
      display: flex;
      width: 100%;
      height: 30px;

      .color-box {
        height: 1.25rem;
        width: 1.25rem;
        color: #3562db;
        line-height: 1.25rem;
        text-align: center;
        border-radius: 3px;
        font-size: 16px;
        margin: auto;
      }

      .linear-g {
        margin-left: 2px;
        width: calc(100% - 22px);
        height: 100%;
        background: #f6f5fa;
        font-size: 13px;
        line-height: 30px;
        font-family: PingFangSC-Medium;
        color: #3562db;
        border-radius: 6px;
        cursor: pointer;

        .linear-g-span1 {
          margin: 0 15px;
          font-weight: 600;
        }

        .title-icon {
          float: right;
          line-height: 30px;
          margin-right: 5px;
        }
      }
    }

    .plan-content {
      width: calc(100% - 33px);
      margin-left: 11px;
      color: #676a6c;
      font-size: 13px;

      .item-row {
        width: 100%;
        display: flex;
        padding: 20px 0 20px 30px;
        box-sizing: border-box;

        .width30 {
          width: 30%;
        }

        .width45 {
          width: 45%;
        }

        .width90 {
          width: 90%;
          display: flex;
          align-items: center;
        }

        ::v-deep .el-image__error,
        ::v-deep .el-image__placeholder {
          background: center;
        }

        .li-first-span {
          display: inline-block;
          width: 80px;
          white-space: nowrap;
          font-size: 14px;
          font-family: PingFang-SC-Medium, PingFang-SC;
          font-weight: 700;
          color: #676a6c;
        }

        .li-last-span {
          font-size: 14px;
          font-family: PingFang-SC-Medium, PingFang-SC;
          font-weight: 500;
          color: #676a6c;
        }

        .recording-ALabel {
          color: #0379f1;
          font-size: 14px;
          text-decoration: none;

          i {
            margin: 0 3px 0 10px;
          }
        }

        #audio-box {
          display: flex;
        }

        #audio-box > audio {
          width: 260px;
          height: 30px;
        }

        #audio-box > a {
          width: 40px;
          text-align: center;
          background-color: #2cc7c5;
          height: 35px;
          line-height: 35px;
          color: #fff;
          border-radius: 5px;
          margin-left: 10px;
        }
      }

      .show-content {
        width: 100%;
      }
    }

    .plan-content-line {
      border-left: 1px solid #676a6c;
    }

    .plan-content-noline {
      width: calc(100% - 33px);
      margin-left: 11px;
      padding: 20px 0 20px 20px;
      color: #b5bacb;
      font-size: 13px;
    }

    .maint-table {
      width: 60%;

      td {
        padding: 5px 0 5px 10px;
        border: 1px solid #eee;
        height: 25px;
        line-height: 25px;
        display: table-cell;
        vertical-align: middle;
        color: #676a6c;
      }

      tr:first-child {
        background-color: #f5f6fc;
      }

      td:first-child {
        width: 35%;
      }

      .one-line {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .repair-work {
      float: right;
      margin: 15px 10px;
      position: absolute;
      top: 40px;
      right: 10px;

      ::v-deep .el-checkbox__label {
        color: #121f3e;
      }
    }
  }
}
</style>
