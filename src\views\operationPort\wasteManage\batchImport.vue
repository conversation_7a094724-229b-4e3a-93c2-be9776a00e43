<template>
  <PageContainer>
    <div slot="content" v-loading="downLoading">
      <div class="title">批量导入</div>
      <div style="padding-left: 16px; display: flex;">
        <el-button type="primary" @click="downloadTemplate">模板下载</el-button>
        <el-upload ref="upload" v-loading="downLoading" :limit="1" action="" :file-list="fileList" :http-request="httpRequest">
          <el-button type="primary">数据导入</el-button>
        </el-upload>
      </div>
      <div class="table-box">
        <el-table :data="tableData" border style="width: 100%;" :height="tableData.length ? '60vh' : '120px'">
          <el-table-column label="序号" type="index" width="100" :resizable="false" align="center"></el-table-column>
          <el-table-column label="所属科室" prop="officeName" :resizable="false" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="医废数量" prop="gatherCountStr" :resizable="false" align="center"></el-table-column>
          <el-table-column label="医废类型" prop="wasteType" :resizable="false" align="center"></el-table-column>
          <el-table-column label="收集重量(kg)" prop="gatherWeigh" :resizable="false" align="center"></el-table-column>
        </el-table>
      </div>
      <el-form ref="formRef" :model="formData" label-width="100px">
        <div>
          <el-form-item label="收集时间" prop="gatherTime" :rules="rules.gatherTime">
            <el-date-picker v-model="formData.gatherTime" type="datetime" value-format="yyyy-MM-dd HH:mm:ss"> </el-date-picker>
          </el-form-item>
        </div>
        <div style="margin: 15px 0;">
          <el-form-item label="收集人员" prop="gatherPersonCode" :rules="rules.gatherPersonCode">
            <el-select v-model="formData.gatherPersonCode" placeholder="请选择人员" @change="personChanged">
              <el-option v-for="item in personList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
            </el-select>
          </el-form-item>
        </div>
      </el-form>
      <div class="footer">
        <el-button type="primary" @click="handleSubmit">保存</el-button>
        <el-button type="primary" plain @click="goBack">取消</el-button>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import { transData } from '@/util'
import axios from 'axios'
import qs from 'qs'
export default {
  name: 'manualEntry',
  data() {
    return {
      fileList: [],
      downLoading: false,
      formData: {
        gatherTime: '',
        gatherPerson: '',
        gatherPersonCode: ''
      },
      rules: {
        gatherPersonCode: { required: true, message: '收集人员不能为空', trigger: 'change' },
        gatherTime: { required: true, message: '收集时间不能为空', trigger: 'change' }
      },
      deptOptions: [],
      options: [
        {
          value: '0',
          label: '损伤类'
        },
        {
          value: '1',
          label: '病理类'
        },
        {
          value: '2',
          label: '化学类'
        },
        {
          value: '3',
          label: '感染类'
        },
        {
          value: '4',
          label: '药物类'
        },
        {
          value: '5',
          label: '涉疫类'
        },
        {
          value: '6',
          label: '其他'
        }
      ],
      deptList: [],
      personList: [],
      tableData: []
    }
  },
  mounted() {
    this.getDeptList()
    this.getPersonList()
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    // 获取部门列表
    getDeptList() {
      this.$api.getSelectedDept({}).then((res) => {
        if (res.code == 200) {
          this.deptOptions = transData(res.data, 'id', 'pid', 'children')
          this.deptList = res.data
        }
      })
    },
    officeChanged(val, item) {
      this.deptList.forEach((i) => {
        if (i.id == val) {
          item.officeName = i.deptName
        }
      })
    },
    typeChanged(val, item) {
      this.options.forEach((i) => {
        if (i.value == val) {
          item.wasteType = i.label
        }
      })
    },
    personChanged(val) {
      this.personList.forEach((i) => {
        if (i.id == val) {
          this.formData.gatherPerson = i.name
        }
      })
    },
    getPersonList() {
      this.$api.selectStaffList().then((res) => {
        this.personList = res.data
      })
    },
    handleSubmit() {
      if (this.tableData.length == 0) {
        this.$message.error('请先导入数据再进行保存！')
        return
      }
      this.$refs['formRef'].validate((valid) => {
        if (valid) {
          let list = this.tableData.map((item) => {
            let obj = {}
            obj.officeId = item.officeId
            obj.officeName = item.officeName
            obj.gatherCount = parseInt(item.gatherCountStr)
            obj.wasteType = item.wasteType
            obj.wasteCode = item.wasteCode
            obj.gatherWeigh = item.gatherWeigh
            return obj
          })
          this.formData.list = list
          this.$api.manualBatchSave(this.formData).then((res) => {
            if (res.code == '200') {
              this.$message.success('保存成功')
              this.$router.go(-1)
            }
          })
        }
      })
    },
    downloadTemplate() {
      this.downLoading = true
      const userInfo = this.$store.state.user.userInfo.user
      let params = {
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
      axios({
        method: 'post',
        url: __PATH.VUE_APP_IMWS_API + 'ihcrsStatisticsInterfaceController/getExcelTemplate',
        data: qs.stringify(params),
        responseType: 'blob',
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      })
        .then((res) => {
          this.downLoading = false
          let name = '医疗废物信息.xlsx'
          let blob = new Blob([res.data]) // { type: "application/vnd.ms-excel" }
          let url = window.URL.createObjectURL(blob) // 创建一个临时的url指向blob对象
          // 创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
          let a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
          // 释放这个临时的对象url
          window.URL.revokeObjectURL(url)
        })
        .catch((res) => {
          this.downLoading = false
          this.$message.error('导出失败！')
        })
    },
    httpRequest(file) {
      const params = new FormData()
      params.append('file', file.file)
      this.downLoading = true
      this.$api
        .uploadTemplate(params)
        .then((res) => {
          this.downLoading = false
          if (res.code == 200) {
            this.tableData = res.data
            this.$message.success('上传成功')
          } else {
            this.$message.error(res.data)
          }
        })
        .catch((res) => {
          this.downLoading = false
          this.$message.error('上传失败！')
        })
      this.$refs.upload.clearFiles() // 清空当前 files
    }
  }
}
</script>
<style lang="scss" scoped>
.container-content > div {
  background-color: #fff;
  height: 90vh;
  position: relative;
}

.footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  padding: 10px 20px;
  background-color: #fff;
  text-align: left;
  border-top: 1px solid #ebeef5;
}

.title {
  font-size: 20px;
  font-weight: 700;
  color: #303133;
  padding: 12px 20px 0;
  margin-bottom: 12px;
}

.el-form > div {
  display: flex;
  align-items: center;
}

.el-form-item {
  margin-bottom: 0;
}

.el-table {
  margin: 16px 0;
}

.table-box {
  padding: 0 16px;
}

::v-deep .el-upload-list {
  display: none;
}

::v-deep .el-upload {
  margin-left: 12px;
}
</style>
