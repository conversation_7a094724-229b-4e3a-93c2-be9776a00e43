<template>
  <ContentCard
    :title="item.componentTitle"
    :scrollbarHover="true"
    :cstyle="{ height: '100%' }"
    class="drag_class"
    :hasMoreOper="['edit']"
    @more-oper-event="(val) => $emit('all-more-Oper', val, 'workOderType')"
  >
    <div slot="title-right" class="data-btns">
      <span v-for="item in dataTypeList" :key="item.type" :class="{ 'active-btn': selectDataType == item.type }" @click="changeDateType(item.type)">{{ item.name }}</span>
    </div>
    <div slot="content" class="equipment-content">
      <div class="tasksNumber">
        <div class="item" style="background: #3562DB;">
          <span>任务总数</span>
          <span>{{ taskQuantity.sum ?? 0 }}</span>
        </div>
        <div class="item" style="background: #FF9435;">
          <span>已完成</span>
          <span>{{ taskQuantity.accomplishCount ?? 0 }}</span>
        </div>
        <div class="item" style="background: #00BC6D;">
          <span>完成率</span>
          <span>{{ (taskQuantity.percentage ?? 0) + '%' }}</span>
        </div>
      </div>
      <div class="equipment-table">
        <TablePage
          ref="equipmentTable"
          v-el-table-infinite-scroll="tableLoad"
          v-loading="tableLoading"
          stripe
          :showPage="false"
          :tableColumn="tableColumn"
          :data="tableData"
          height="100%"
          @row-dblclick="clickRow"
        />
      </div>
    </div>
  </ContentCard>
</template>
<script lang="jsx">
import { dataTypeList, taskStatusList } from '@/util/dict.js'
export default {
  name: 'equipmentPatrolTask',
  props: {
    item: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dataTypeList: dataTypeList,
      selectDataType: 'day',
      taskQuantity: {},
      tableColumn: [
        {
          label: '任务名称',
          prop: 'taskName'
        },
        {
          label: '任务类型',
          prop: 'systemCode',
          formatter: (row) => {
            return row.row.systemCode == 1 ? '设备巡检' : '设备保养'
          }
        },
        {
          label: '时间',
          prop: 'taskStartTime'
        },
        {
          label: '部门',
          prop: 'distributionTeamName'
        },
        {
          label: '状态',
          prop: 'taskStatus',
          render(h, scope) {
            return (
              <div class="alarmStatus" style={{ color: taskStatusList[scope.row.taskStatus].color }}>
                <span class="alarmStatusIcon" style={{ background: taskStatusList[scope.row.taskStatus].color }}></span>
                {taskStatusList[scope.row.taskStatus].text}
              </div>
            )
          }
        }
      ],
      tableLoading: false,
      tableData: [],
      pagination: {
        total: 0,
        pageSize: 15,
        pageNo: 1
      }
    }
  },
  mounted() {
    this.getTaskQuantity()
    this.getTaskEquipmentMap()
  },
  methods: {
    // 后勤设备巡检任务完成率
    getTaskQuantity() {
      let params = {
        dateType: this.selectDataType,
        systemCode: 1
      }
      this.$api.getTaskQuantity(params).then((res) => {
        if (res.code == '200') {
          this.taskQuantity = res.data?.list[0] ?? []
        }
      })
    },
    // 后勤设备巡检任务统计
    getTaskEquipmentMap() {
      let params = {
        dateType: this.selectDataType,
        systemCode: 1,
        pageNo: this.pagination.pageNo,
        pageSize: this.pagination.pageSize
      }
      this.tableLoading = true
      this.$api.getTaskEquipmentMap(params).then((res) => {
        this.tableLoading = false
        if (res.code == '200') {
          this.tableData = this.tableData.concat(res.data?.list ?? [])
          this.pagination.total = parseInt(res.data.sum)
        }
      })
    },
    tableLoad() {
      if (this.pagination.total > this.pagination.pageNo * this.pagination.pageSize) {
        this.pagination.pageNo++
        this.getTaskEquipmentMap()
      }
    },
    clickRow(row) {
      this.$router.push({
        path: '/InspectionManagement/taskManagement/taskDetail',
        query: {
          taskId: row.id,
          type: 'detail',
          systemType: row.systemCode
        }
      })
    },
    // 切换日期类型
    changeDateType(type) {
      this.pagination.pageNo = 1
      this.tableData = []
      this.selectDataType = type
      this.getTaskQuantity()
      this.getTaskEquipmentMap()
    }
  }
}
</script>
<style lang="scss" scoped>
.data-btns {
  position: absolute;
  right: 7%;
  display: flex;
  align-items: center;
  justify-content: space-between;

  & > span {
    width: 40px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    margin: 0 4px;
    background-color: #FAF9FC;
    font-size: 14px;
    font-family: "PingFang SC-Medium", "PingFang SC";
    border-radius: 4px;
    color: #414653;
    cursor: pointer;
  }

  .active-btn {
    background-color: #E6EFFC !important;
    color: #3562DB !important;
  }
}

.equipment-content {
  display: flex;
  flex-direction: column;
  height: 100%;

  .tasksNumber {
    display: flex;
    justify-content: space-between;
    padding: 14px 0px 16px 0px;
    .item {
      width: calc(100% / 3 - 6px);
      display: flex;
      flex-direction: column;
      align-items: center;
      border-radius: 4px;
      padding: 16px;
      :first-child {
        font-size: 15px;
        font-family: "PingFang SC-Medium", "PingFang SC";
        font-weight: 500;
        color: #fff;
      }

      :last-child {
        line-height: 28px;
        font-size: 24px;
        margin-top: 8px;
        font-family: Arial-Bold, Arial;
        font-weight: bold;
        color: #fff;
      }
    }
  }

  .equipment-table {
    flex: 1;
    ::v-deep(.el-table) .el-table__header .el-table__cell{
      background: #F3F4F6;
    }
  }
}
</style>
<style lang="scss">
.equipment-content {
  .alarmStatus {
    position: relative;
    display: inline-block;
    padding-left: 12px;

    .alarmStatusIcon {
      display: inline-block;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 8px;
      height: 8px;
      border-radius: 100%;
    }
  }
}
</style>
