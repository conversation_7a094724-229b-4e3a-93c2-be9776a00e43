<template>
  <PageContainer v-if="routeInfo.isFalg == 0">
    <div slot="content" class="role-content" style="height: 100%">
      <div class="content_top">
        <el-tabs v-model="activeName" @tab-click="handleClick" class="tabsMenu">
          <el-tab-pane label="公开课程" name="open"></el-tab-pane>
          <!-- <el-tab-pane label="实验室准入课单" name="access"></el-tab-pane>
          <el-tab-pane label="我的任务" name="task"></el-tab-pane>
          <el-tab-pane label="我的课程" name="myCourse"></el-tab-pane> -->
        </el-tabs>
        <div class="content_box">
          <div class="open_conter" v-if="activeName == 'open'">
            <div class="content_left">
              <div class="title">试题分类</div>
              <div class="tree">
                <el-tree ref="tree" v-loading="treeLoading" style="margin-top: 10px" :data="treeData"
                  :props="defaultProps" :default-expanded-keys="expanded" node-key="label" highlight-current
                  @node-click="handleNodeClick">
                  <span class="span-ellipsis" slot-scope="{ node }">
                    <span :title="node.label">{{ node.label }}</span>
                  </span>
                </el-tree>
              </div>
            </div>
            <div class="content_right">
              <div class="seachTop">
                <el-input v-model="seachForm.courseName" style="width: 200px; margin: 0 16px" placeholder="请输入课件名称">
                </el-input>
                <el-input v-model="seachForm.courseTeacher" style="width: 200px; margin: 0 16px" placeholder="请输入课件老师">
                </el-input>
                <div v-if="activeName == 'open'">
                  <el-date-picker v-model="timeLine" type="daterange" value-format="yyyy-MM-dd" range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" style="width: 260px; margin: 0 16px">
                  </el-date-picker>
                </div>
                <div v-if="activeName == 'myCourse'">
                  <el-select v-model="seachForm.studySchedule" placeholder="请选择学习状态"
                    style="width: 200px; margin: 0 16px">
                    <el-option v-for="item in studyScheduleList" :key="item.id" :label="item.label" :value="item.id">
                    </el-option>
                  </el-select>
                </div>
                <el-button type="primary" plain @click="resetForm">重置</el-button>
                <el-button type="primary" @click="search">查询</el-button>
              </div>
              <div class="table">
                <div class="course-content" v-loading="tableLoading">
                  <el-row :gutter="24" class="overview">
                    <el-col v-for="(item, index) in tableData" :key="index" :span="6">
                      <div class="item" @click="getDetils(item)">
                        <div class="course_cove" :style="{ backgroundImage: 'url(' + item.coverUrl + ')'}">
                          <div v-if="activeName == 'myCourse' && item.mustLearn" class="status-ing">
                            必学课程
                          </div>
                          <!-- <img :src="item.coverUrl" alt="" /> -->
                        </div>
                        <div class="text">
                          <div class="headline">
                            <p>
                              {{ item.courseName }}
                            </p>
                          </div>
                          <p class="introduce">{{ item.subjectName }}</p>
                          <p class="describe">
                            {{ item.comments }}
                          </p>
                          <div class="course_time introduce">
                            <span>{{ item.updateTime }}</span>
                            <span>来自 {{ item.createName }}</span>
                            <span>{{ item.periodCount || 0 }}课时</span>
                          </div>
                          <div class="item_bottom" @click.stop>
                            <span>{{ item.viewCount || 0 }}播放</span>
                            <el-button v-if="item.studyState == '1'" @click="collectCourse(item.id)" class="miniBtn"
                              size="mini"><i class="el-icon-star-off"></i>收藏学习</el-button>
                            <!-- 已收藏未学习 -->
                            <span v-if="item.studyState == '2'" class="collect" @click="getDetils(item)">立即学习</span>
                            <!-- 学习中 -->
                            <span v-if="item.studyState == '0'" class="studyIng" @click="getDetils(item)">继续学习</span>
                            <span v-if="item.studyState == '3'" class="studyOK"><img
                                src="../../../assets/images/pass.png" alt="" />已学完</span>
                          </div>
                        </div>
                      </div>
                    </el-col>
                  </el-row>
                </div>
              </div>
              <div class="contentTable-footer">
                <el-pagination style="margin-top: 3px" :current-page="paginationData.pageNo"
                  layout="total, sizes, prev, pager, next, jumper" :total="paginationData.total"
                  :page-size="paginationData.pageSize" :page-sizes="[15, 30, 50, 100]" @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"></el-pagination>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PageContainer>
   <div v-else>
  <permissionPrompt></permissionPrompt>
  </div>
</template>

<script type="text/ecmascript-6">
import permissionPrompt from "@/views/safety/courseIndex/components/permissionPrompt.vue";
import moment from 'moment'
export default {
  name: 'feedbackRecord',
  components: {permissionPrompt},
  data() {
    return {
      moment,
      routeInfo: '',
      activeName: 'open',
      treeLoading: false,
      tableLoading: false,
      treeData: [],
      defaultProps: {
        children: "childList",
        label: "name",
      },
      expanded: [],
      subjectId: '',
      seachForm: {
        courseName: "",
        courseTeacher: "",
        startTime: '',
        endTime: '',
        studySchedule: ''
      },
      timeLine: [],
      studyScheduleList: [
        // {
        //   id:'1',
        //   label:'收藏学习'
        // },
        {
          id: '2',
          label: '未学习'
        },
        {
          id: '0',
          label: '学习中'
        },
        {
          id: '3',
          label: '已学习'
        }
      ],
      laboratoryInfo: {
        labName: '',
        labType: ''
      },
      laboratoryTypeList: [],
      taskInfo: {
        name: '',
        taskStatusPc: '',
        startTime: '',
        endTime: ''
      },
      taskScheduleList: [
        {
          id: '0',
          label: '未开始'
        },
        {
          id: '1',
          label: '学习中'
        },
        {
          id: '2',
          label: '已完成'
        },
        {
          id: '3',
          label: '已超时'
        },
        {
          id: '4',
          label: '即将超时'
        }
      ],
      paginationData: {
        pageNo: 1,
        pageSize: 15,
        total: 0
      },
      tableData: []
    }
  },
  created() {
    // if (this.$route.query) {
    //   sessionStorage.setItem("routeInfo", JSON.stringify(this.$route.query));
    //   this.routeInfo = JSON.parse(sessionStorage.getItem("routeInfo"));
    // }
    this.routeInfo = JSON.parse(sessionStorage.getItem('routeInfo'))
     if (this.routeInfo.isFalg == 1) {
      return
    }
    this.getLaborList()
    this.getTblleList();
    this.getCourseList()
  },
  watch: {
    timeLine(val) {
      if (val.length > 0) {
        if (this.activeName == 'open') {
          this.seachForm.startTime = val[0];
          this.seachForm.endTime = val[1];
        } else if (this.activeName == 'task') {
          this.taskInfo.startTime = val[0]
          this.taskInfo.endTime = val[1]
        }
      } else {
        if (this.activeName == 'open') {
          this.seachForm.startTime = "";
          this.seachForm.endTime = "";
        } else if (this.activeName == 'task') {
          this.taskInfo.startTime = ''
          this.taskInfo.endTime = ''
        }
      }
    }
  },
  methods: {
    // 获取实验室类型
    getLaborList() {
      this.tableLoading = true
      let params = {
        pageNo: 1,
        pageSize: 999,
      }
      this.$api.laboratorylList(params).then((res) => {
        if (res.code == 200) {
          this.laboratoryTypeList = res.data.list;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 获取试题分类列表
    getTblleList() {
      this.treeLoading = true
      let data = {
        pageNo: 1,
        pageSize: 9999,
      };
      this.$api.ipsmFicationlList(data).then((res) => {
        if (res.code == 200) {
          this.treeData = res.data.records;
        } else {
          this.$message.error(res.message);
        }
        this.treeLoading = false
      });
    },
    getCourseList() {
      this.tableLoading=true
      this.tableData= []
      if(this.activeName=='open'||this.activeName=='myCourse'){
        let getList =this.activeName=='open'?'getOnlineCourseList':'getMyCourseList'
        let data ={
          userId:this.routeInfo.userId,
          pageNo:this.paginationData.pageNo,
          pageSize:this.paginationData.pageSize,
          courseName:this.seachForm.courseName,
          createName:this.seachForm.courseTeacher,
          subjectId:this.subjectId
        }
        if(this.activeName=='open'){
          data.startTime=this.seachForm.startTime,
          data.endTime=this.seachForm.endTime
        }
        this.$api[getList](data).then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.list;
            this.paginationData.total = res.data.total;
          } else {
            this.$message.error(res.msg);
          }
          this.tableLoading = false
        });
      }
    },
    // 切换tabs
    handleClick(tab, event) {
      this.getTblleList()
      this.subjectId = ''
      this.paginationData.total = 0
      this.resetForm()
    },
    resetForm() {
      this.paginationData.pageNo = 1
      this.paginationData.pageSize = 15
      this.timeLine = []
      if (this.activeName == 'open' || this.activeName == 'myCourse') {
        this.seachForm = {
          courseName: "",
          courseTeacher: "",
          startTime: '',
          endTime: '',
          studySchedule: ''
        }
      } else if (this.activeName == 'task') {
        this.taskInfo = {
          name: '',
          taskStatusPc: ''
        }
      } else if (this.activeName == 'access') {
        this.laboratoryInfo = {
          labName: '',
          labType: ''
        }
      }
      this.getCourseList()
    },
    search() {
      this.paginationData.pageNo = 1
      this.getCourseList()
    },
    // 收藏课程
    collectCourse(id) {
      this.$confirm('是否要收藏该课程?', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let data = {
          courseId: id,
          userId: this.routeInfo.userId
        }
        this.$api.collectCourse(data).then((res) => {
          if (res.code == 200) {
            this.getCourseList()
          } else {
            this.$message.error(res.msg);
          }
        });
      })
    },
    // 查看详情
    getDetils(item) {
      if(this.activeName=='open'||this.activeName=='myCourse'){
        this.$router.push({
          path:'courseDetils',
          query:{
            id:item.id||item.courseId,
          }
        })
      }
      // else if(this.activeName=='access'){
      //   this.$router.push({
      //     name:'/accessDetails',
      //     query:{
      //       id:item.id
      //     }
      //   })
      // }else{
      //   this.$router.push({
      //     path:'/taskDetails',
      //     query:{
      //       id:item.id
      //     }
      //   })
      // }
    },
    handleNodeClick(data, node) {
      this.paginationData.pageNo = 1
      this.subjectId = data.id
      this.getCourseList()
    },
    handleCurrentChange(val) {
      this.paginationData.pageNo = val
      this.getCourseList()
    },
    // 每页的条数
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.paginationData.pageNo = 1
      this.getCourseList()
    },
    joinCourse(item) {
      let data = {
        courseId: item.courseId,
        examPlanId: item.examPlanId,
        trainPlanId: item.trainPlanId,
        id: item.id,
        userId: this.routeInfo.userId
      }
      this.$api.joinCourse(data).then((res) => {
        if (res.code == 200) {
          this.getCourseList()
        } else {
          this.$message.error(res.message);
        }
      });
    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.el-tabs--left) {
  width: 200px;
  margin: 20px auto 0 auto;
}

.content_top {
  height: 100%;
  border-radius: 10px;
  padding: 16px;
  background: #fff;
  display: flex;
  flex-direction: column;

  .tabsMenu {
    margin-bottom: 15px;

    :deep(.el-tabs__item) {
      width: 120px;
    }
  }

  .top_content {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }
}

.content_box {
  width: 100%;
  height: 100%;
}

.open_conter {
  padding: 16px;
  height: 100%;
  display: flex;

  .content_left {
    width: 240px;
    border-radius: 4px;
    border: 1px solid #e5e6eb;

    .title {
      margin: 16px;
      font-size: 16px;
    }

    .tree {
      height: calc(100% - 50px);
      overflow: auto;
    }
  }

  .content_right {
    width: calc(100% - 264px);
    margin-left: 24px;

  }

}

.seachTop {
  height: 64px;
  background-color: #faf9fc;
  padding: 16px;
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-size: 14px;

  .expand {
    color: #3562db;
    font-size: 14px;
  }
}

.table {
  height: calc(100% - 160px);
  overflow: auto;
  font-size: 14px;
}

:deep(.el-tabs__nav) {
  width: 100%;

  .el-tabs__item {
    padding: 0 10px;
    width: 50%;
    text-align: center;
  }
}

.course-content {
  height: 100%;
  padding: 0 16px;
  overflow: auto;
}

.item {
  // width: 372px;
  margin-bottom: 30px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e6eb;
  display: flex;
  flex-direction: column;

  .text {
    padding: 16px 24px 10px 24px;
    font-family: PingFang SC-Regular, PingFang SC;

    .item_bottom {
      height: 40px;
      line-height: 40px;
      padding-top: 10px;
      border-top: 1px solid #e4e4e4;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #999999;

      .collect {
        width: 58px;
        height: 24px;
        line-height: 24px;
        text-align: center;
        background-color: #f2f4f9;
        cursor: pointer;
      }

      .studyIng {
        width: 68px;
        height: 24px;
        line-height: 24px;
        text-align: center;
        background-color: #d7e0f8;
        color: #3562db;
        cursor: pointer;
      }

      .studyOK {
        width: 79px;
        height: 24px;
        line-height: 24px;
        margin-left: 20px;
        background: #e8ffea;
        border-radius: 4px;
        color: #009a29;
        text-align: center;

        img {
          vertical-align: middle;
          margin-right: 8px;
        }
      }
    }
  }
}

.course_cove {
  width: 100%;
  height: 120px;
  border-radius: 8px 8px 0px 0px;
  position: relative;
  background-repeat: no-repeat;
  background-size: cover;
  background-size: 100% 100%;

  .status-ing {
    width: 72px;
    height: 24px;
    background: #f53f3f;
    opacity: 1;
    font-size: 14px;
    color: #fff;
    line-height: 24px;
    text-align: center;
    border-radius: 4px;
    position: absolute;
    left: 8px;
    top: 8px;
  }
}

.headline {

  display: flex;

  p {
    font-size: 16px;
    line-height: 22px;
    color: #333333;
    font-weight: 600;
    overflow: hidden;

    white-space: nowrap;
  }

  .draft {
    width: 44px;
    height: 100%;
    background: #fff7e8;
    border-radius: 4px;
    color: #d25f00;
    font-size: 14px;
    line-height: 22px;
    margin-right: 8px;
  }
}

.introduce {
  margin: 6px 0;
  font-size: 12px;
  color: #999999;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.describe {
  height: 24px;
  margin-bottom: 20px;
  font-size: 14px;
  color: #666666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.course_time {
  .course_bottom {
    i {
      color: #3562db;
      font-size: 14px;
    }

    .el-icon-edit {
      margin-right: 10px;
    }
  }
}

// minibutton
.miniBtn {
  color: #fff;
  border-color: #3562db;
  background: #3562db;
  font-weight: 400;
  font-size: 14px;
  padding: 6px 16px;
  font-family: PingFangSC-Regular;
}

.miniBtn:hover {
  color: #fff;
  font-family: PingFangSC-Regular;
  border-color: rgba(53, 98, 219, 0.8);
  background-color: rgba(53, 98, 219, 0.8);
  font-weight: 500;
}

.laboratoryItem {
  height: 260px;
  border: 1px solid #E5E6EB;
  border-radius: 8px;
  margin-bottom: 16px;

  .lab_base {
    height: 160px;
    padding: 16px;
    background: url(../../../assets/images/labBg.png) no-repeat;
    margin-bottom: 7px;

    .base_top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;

      img {
        margin-right: 8px;
        vertical-align: middle;
      }

      .top_name {
        font-size: 16px;
        font-weight: 500;
      }

      .top_operate {
        width: 68px;
        height: 24px;
        color: #fff;
        font-size: 12px;
        border-radius: 4px;
        line-height: 24px;
        text-align: center;
        cursor: pointer;
      }

      .join {
        background-color: #3562DB;
      }

      .joinOK {
        background-color: rgba(0, 0, 0, 0.4);
      }

      .continueStudy {
        background-color: rgba(53, 98, 219, 0.2);
        color: #3562DB;
      }
    }

    .base_center {
      height: 82px;
      background-color: #fff;
      padding: 10px;

      .itemInfo {
        margin-bottom: 10px;
        display: flex;
        font-size: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .title {
          width: 80px;
          color: #7F848C;
        }
      }
    }
  }

  .lab_center {
    padding: 0 16px;
    height: calc(100% - 180px);

    .access_name {
      font-size: 16px;
      height: 20px;
      line-height: 20px;
    }

    .access_describe {
      height: 20px;
      margin-bottom: 16px;
      line-height: 20px;
      font-size: 12px;
      color: #7F848C;
      white-space: nowrap;
      /* 不换行 */
      overflow: hidden;
      /* 隐藏超出部分 */
      text-overflow: ellipsis;
      /* 使用省略号表示被裁切的文本 */
    }

    .number {
      .number_item {
        margin-right: 12px;

        .type {
          color: #3562DB;
        }
      }
    }

    .timeoutClass {
      height: 22px;
      display: flex;
      align-items: center;
      font-size: 12px;
      background-color: #FFECE8;
      color: #CB2634;
      margin-bottom: 16px;

      img {
        margin-right: 10px;
      }
    }
  }
}
</style>
