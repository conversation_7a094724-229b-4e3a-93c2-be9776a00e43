<template>
  <div class="previewProcess">
    <div v-if="current == 1" class="alarm-box">
      <p class="alarm-title">系统报警</p>
      <div class="alarm-main">
        <p class="alarm-tyle">
          <img :src="alarmTypeIcon" />
          <span>报警类型</span>
        </p>
        <p class="alarm-info">
          <span class="alarm-label">报警位置：</span>
          <span class="alarm-value">门诊楼西侧花坛</span>
        </p>
        <p class="alarm-info">
          <span class="alarm-label">报警设备：</span>
          <span class="alarm-value">跌倒报警</span>
        </p>
        <p class="alarm-info">
          <span class="alarm-label">报警时间：</span>
          <span class="alarm-value">2024-02-28  12:09:13</span>
        </p>
        <p class="alarm-info">
          <span class="alarm-label">报警级别：</span>
          <span class="alarmLevel">紧急</span>
        </p>
        <div class="alarm-btns">
          <el-button @click="control('', 'close')">误报</el-button>
          <el-button type="primary" @click="control(2, '2')">演习</el-button>
          <el-button type="primary" @click="control(2, '3')">确警</el-button>
        </div>
      </div>
    </div>
    <el-drawer v-if="isPreviewDrawer" title="预览" custom-class="preview-drawer" :visible.sync="isPreviewDrawer" direction="rtl">
      <div class="drawer__body_main">
        <div class="drawer-content">
          <div class="drawer-title" style="background: #FFECE8;">
            <div style="display: flex; align-items: center;">
              <img :src="alarmTypeIconRed">
              <p style="font-weight: 500;font-size: 16px;color: #000000;line-height: 19px;">报警类型</p>
            </div>
            <p style="font-weight: 300;font-size: 14px;color: #000000;">这是报警设备名称</p>
          </div>
          <div class="drawer-urgent">
            <p style="font-weight: bold;font-size: 16px;color: #333333;line-height: 19px;">应急流程</p>
            <div style="display: flex; align-items: center; padding: 16px 0px;">
              <img :src="phoneIcon">
              <p style="font-weight: 400; font-size: 14px; color: #3562DB; line-height: 16px; flex: 1;">系统自动呼叫中…</p>
              <p style="font-weight: 300;font-size: 14px;color: #7F848C;line-height: 16px;">系统跟进执行</p>
            </div>
            <p style="font-weight: 300; font-size: 14px; color: #333333; line-height: 22px;">机器电话通知指挥人员、扑救人员到位，语音电话拨打x位   已接通x位</p>
          </div>
        </div>
        <div class="drawer-content">
          <div class="drawer-title" style="border-bottom: 1px solid #E5E6EB;">
            <div style="display: flex; align-items: center;">
              <i class="el-icon-message-solid"></i>
              <p style="font-weight: 500;font-size: 16px;color: #000000;line-height: 19px;">通知事件</p>
            </div>
          </div>
          <div class="drawer-notify">
            <div v-for="(item, index) in notifyList" :key="index" class="notify-item">
              <img :src="phoneBgIcon">
              <div style="flex: 1;font-size: 14px;margin-right: 8px;">
                <p style="flex: 1;font-weight: 400;color: #333333;">{{ item.departCode ? item.departCode.split(',').map(v => depts[v]).join('，') : '通知' + item.personName + '电话'}}</p>
                <p v-if="item.noticeType == 0" style="margin-top: 6px;">
                  <span style="font-weight: 300;color: #666666; min-width: 70px;text-align: right;">联系方式：</span>
                  <span style="font-weight: 500;color: #333333;">{{ item.noticePhone }}</span>
                </p>
                <p v-if="item.noticeType == 0 && (item.noticeWay == 0 || item.noticeWay == 1)" style="margin-top: 6px; display: flex;">
                  <span style="font-weight: 300;color: #666666; min-width: 70px;text-align: right;">通知：</span>
                  <span style="font-weight: 500;color: #333333;">{{item.noticeContent}}</span>
                </p>
              </div>
              <el-button v-if="item.noticeWay == 0" type="primary" style="padding: 4px 10px;">拨打电话</el-button>
              <p v-else style="color: #C2C4C8;">系统自动通知</p>
            </div>
          </div>
        </div>
        <div class="drawer-content">
          <div class="drawer-title" style="border-bottom: 1px solid #E5E6EB;">
            <div style="display: flex; align-items: center;">
              <i class="el-icon-warning" style="color: #FF7D00;"></i>
              <p style="font-weight: 500;font-size: 16px;color: #000000;line-height: 19px;">提示事件</p>
            </div>
          </div>
          <div class="drawer-warn">
            <!-- <div class="warn-main">
              <p style="margin-bottom: 10px;font-size: 14px;">
                <span style="font-weight: 300;color: #666666; min-width: 98px;text-align: right;">区域排烟风机：</span>
                <span style="font-weight: 400;color: #333333;">设备名称</span>
              </p>
              <p style="margin-bottom: 10px;font-size: 14px;">
                <span style="font-weight: 300;color: #666666;min-width: 98px;text-align: right;">区域水泵：</span>
                <span style="font-weight: 400;color: #333333;">设备名称</span>
              </p>
              <p style="margin-bottom: 10px;font-size: 14px;">
                <span style="font-weight: 300;color: #666666;min-width: 98px;text-align: right;">喷淋泵：</span>
                <span style="font-weight: 400;color: #333333;">设备名称</span>
              </p>
              <p style="margin-bottom: 10px;font-size: 14px;">
                <span style="font-weight: 300;color: #666666;min-width: 98px;text-align: right;">正压送风：</span>
                <span style="font-weight: 400;color: #333333;">设备名称</span>
              </p>
              <p style="margin-bottom: 10px;font-size: 14px;">
                <span style="font-weight: 300;color: #666666;min-width: 98px;text-align: right;">广播：</span>
                <span style="font-weight: 400;color: #333333;">设备名称</span>
              </p>
              <p style="margin-bottom: 10px;font-size: 14px;">
                <span style="font-weight: 300;color: #666666;min-width: 98px;text-align: right;">声光报警：</span>
                <span style="font-weight: 400;color: #333333;">设备名称</span>
              </p>
            </div> -->
            <div v-for="(item, index) in warnList" :key="index" :style="{borderBottom: index == warnList.length - 1 ? 'none' : '1px solid #E4E7ED', padding: '0px 0px 8px 0px'}">
              <p style="font-weight: 300;font-size: 14px;color: #666666;margin: 16px 0px;">{{ item.warnRoles }}职责：</p>
              <p style="font-weight: 400;font-size: 14px;color: #333333;">{{ item.warnContent }}</p>
            </div>
          </div>
        </div>
        <div class="drawer-content">
          <div class="drawer-title" style="border-bottom: 1px solid #E5E6EB;">
            <div style="display: flex; align-items: center;">
              <i class="el-icon-warning" style="color: #3562DB;"></i>
              <p style="font-weight: 500;font-size: 16px;color: #000000;line-height: 19px;">确认事件</p>
            </div>
          </div>
          <div class="drawer-confirm">
            <div v-for="(item, index) in confirmList" :key="index" class="confirm-item">
              <i class="el-icon-success"></i>
              <p style="flex: 1;font-weight: 400;font-size: 14px;color: #333333;">{{ item.confirmTitle }}</p>
              <div style="text-align: right;">
                <p style="font-weight: 400;font-size: 14px;color: #7F848C;">中控人员跟进执行</p>
                <div style="margin-top: 10px;">
                  <el-button type="primary" plain style="padding: 4px 10px;">忽略</el-button>
                  <el-button type="primary" style="padding: 4px 10px;">已到位</el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="drawer-foot">
        <el-button type="primary" icon="el-icon-message-solid" style="background: #FA403C;border-color: #FA403C;" @click="isPreviewDrawer = false">解除报警</el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script lang="jsx">
import { transData } from '@/util'
import alarmTypeIcon from '@/assets/images/operationPort/alarmTypeIcon.png'
import alarmTypeIconRed from '@/assets/images/operationPort/alarmTypeIconRed.png'
import phoneBgIcon from '@/assets/images/operationPort/phoneBgIcon.png'
import phoneIcon from '@/assets/images/operationPort/phoneIcon.png'
export default {
  name: 'previewProcess',
  props: {
    isPreview: {
      type: Boolean,
      default: false
    },
    currentStepId: {
      type: String,
      default: ''
    },
    eventData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      startNotify: '',
      current: 1,
      isPreviewDrawer: false,
      alarmTypeIcon,
      alarmTypeIconRed,
      phoneBgIcon,
      phoneIcon,
      depts: {}, // 部门列表
      notifyList: [], // 通知列表
      warnList: [], // 提示列表
      confirmList: [] // 确认列表
    }
  },
  computed: {

  },
  watch: {
    isPreviewDrawer(val) {
      if (!val) {
        // 关闭后结束预案
        this.control('', 'close')
      }
    },
    currentStepId(val) {
      if (val == 0) {
        this.startNotify.close()
        this.current = 1
      }
    }
  },
  created() {
    this.getUseDeptList()
  },
  methods: {
    control(type, id) {
      this.$emit('previewChange', id)
      if (type == 1) {
        this.current = 1
      } else if (type == 2) {
        const h = this.$createElement
        this.current = 2
        this.startNotify = this.$notify({
          type: 'success',
          title: '启用应急预案',
          dangerouslyUseHTMLString: true,
          duration: 0,
          offset: 100,
          message: h('div', {class: 'alarm-notify'}, [
            h('p', {class: 'alarm-notify-content'}, '请确认该报警是否需要启动预案，进行处理。'),
            h('div', {class: 'alarm-notify-foot'}, [
              h('button', {
                class: 'alarm-notify-button',
                on: {
                  click: () => {
                    this.startNotify.close()
                    this.control('', 'close')
                  }
                }
              }, '放弃预案'),
              h('button', {
                class: 'alarm-notify-button',
                on: {
                  click: () => {
                    this.control(3, id == '2' ? '2-2-1' : '3-2-1')
                  }
                }
              }, '启用预案')
            ])
          ])
        })
      } else if (type == 3) {
        this.startNotify.close()
        this.isPreviewDrawer = true

        const nodeData = id == '2-2-1' ? this.eventData[3] : (id == '3-2-1' ? this.eventData[6] : {})
        this.notifyList = nodeData.noticeEventList.filter(v => v.checkFlag == 1 || v.checkFlag == true)
        this.warnList = nodeData.warnEventList.filter(v => v.checkFlag == 1 || v.checkFlag == true)
        this.confirmList = nodeData.confirmEventList.filter(v => v.checkFlag == 1 || v.checkFlag == true)
        console.log('预览', nodeData)
      }
    },
    // 获取科室
    getUseDeptList() {
      this.$api.getSelectedDept().then((res) => {
        if (res.code == '200') {
          transData(res.data, 'id', 'parentId', 'children').forEach(v => {
            this.depts[v.id] = v.deptName
          })
        }
      })
    }
  }
}

</script>

<style lang="scss" scoped>
.previewProcess {
  p {
    margin: 0;
  }
  .alarm-box {
    position: absolute;
    right: 16px;
    top: 0px;
    transition: all .3s;
    background: #DFE7F2;
    border: 2px solid #DFE7F2;
    border-radius: 8px;
    padding: 14px;
    text-align: initial;

    .alarm-title {
      padding: 3px 0px 3px 11px;
      font-weight: 500;
      font-size: 16px;
      color: #333333;
      line-height: 19px;
      position: relative;
      padding-left: 11px;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        display: inline-block;
        width: 3px;
        height: 16px;
        background: #3562DB;
        border-radius: 999px;
      }
    }

    .item-step {
      padding: 3px 8px;
      font-weight: 400;
      font-size: 12px;
      color: #333333;
      line-height: 14px;
      position: absolute;
      top: 17px;
      right: 2px;
      background: #FFE4BA;
      border-radius: 999px 0px 0px 999px;
    }

    .alarm-main {
      width: 368px;
      margin-top: 8px;
      background: #fff;
      border-radius: 4px;
      padding: 16px;
      .alarm-tyle {
        line-height: 16px;
        font-size: 14px;
        color: #FF9435;
        display: flex;
        align-items: center;
        margin-bottom: 6px;

        img {
          width: 14px;
          height: 13px;
          margin-right: 8px;
        }
      }
      .alarm-info {
        margin-top: 2px;
        font-size: 12px;
        line-height: 21px;

        .alarm-label {
          color: #666666;
        }

        .alarm-value {
          color: #333333;
        }

        .alarmLevel {
          color: #CB2634;
          padding: 3px 8px;
          background: #FFECE8;
          border-radius: 4px;
        }
      }

      .alarm-btns {
        display: flex;
        align-items: center;
        justify-content: flex-end;

        button {
          padding: 6px 11px;
          font-size: 12px;
        }
      }
    }
  }
  ::v-deep(.preview-drawer) {
    width: 35% !important;
    text-align: initial;

    .el-drawer__header {
      height: 56px;
      margin: 0px;
      padding: 0px 26px;
      font-size: 18px;
      color: #333333;
      line-height: 18px;
    }

    .el-drawer__body {
      padding: 0;
      display: flex;
      flex-direction: column;

      .drawer__body_main {
        background: #F6F5FA;
        padding: 8px 24px 24px;
        flex: 1;
        overflow-x: auto;
        display: flex;
        flex-direction: column;
      }

      .drawer-content {
        margin-top: 16px;
        flex: 1;
        background: #fff;
        // padding: 16px 16px 0px 16px;
        border-radius: 8px;
        font-weight: 500;
        font-size: 14px;
        color: #333333;
        word-wrap: break-word;
        word-break: break-all;

        .notifyEvent-head {
          margin-bottom: 16px;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .notify-title {
            cursor: pointer;
            font-weight: bold;
            font-size: 16px;
            color: #333333;
            line-height: 32px;
            width: 200px;
          }
        }
      }
      .drawer-foot {
        padding: 12px 16px;
        text-align: right;
        background: #fff;
      }
    }
  }
  .drawer-title {
    padding: 16px 24px 16px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 8px 8px 0px 0px;
    img {
      width: 20px;
      height: 20px;
      margin-right: 8px;
    }
    .el-icon-message-solid {
      font-size: 19px;
      margin-right: 8px;
      color: #3562DB;
    }
    .el-icon-warning  {
      font-size: 19px;
      margin-right: 8px;
    }
  }
  .drawer-urgent {
    padding: 16px;
    img {
      width: 18px;
      height: 18px;
      margin-right: 4px;
    }
  }
  .drawer-notify {
    padding: 0px 16px;
    .notify-item {
      padding: 16px 0px;
      border-bottom: 1px solid #E4E7ED;
      display: flex;
      align-items: center;
      img {
        width: 30px;
        height: 30px;
        margin-right: 16px;
      }
      span {
        display: inline-block;
      }
    }
    .notify-item:last-child {
      border: none;
    }
  }
  .drawer-warn {
    padding: 0px 16px 16px;
    .warn-main {
      padding: 16px;
      background: rgba(53,98,219,0.05);
      border-radius: 8px;
      span {
        display: inline-block;
      }
    }
  }
  .drawer-confirm {
    padding: 0px 16px;
    .confirm-item {
      padding: 16px 0px;
      border-bottom: 1px solid #E4E7ED;
      display: flex;
      .el-icon-success {
        font-size: 14px;
        line-height: 23px;
        margin-right: 16px;
        color: #CCCED3;
      }
    }
    .confirm-item:last-child {
      border: none;
    }
  }
}
</style>
<style lang="scss">
.alarm-notify {
  .alarm-notify-content {
    margin-top: 16px;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 18px;
  }
  .alarm-notify-foot {
    margin-top: 16px;
    text-align: right;
    button {
      cursor: pointer;
      padding: 3px 12px;
      border-radius: 4px;
      font-size: 14px;
      line-height: 22px;
    }
    .alarm-notify-button:first-child {
      background: #fff;
      color: #666666;
      border: 1px solid #DCDFE6;
    }
    .alarm-notify-button:last-child {
      margin-left: 8px;
      background: #3562DB;
      color: #fff;
      border: 1px solid #3562DB;
    }
  }
}
</style>
