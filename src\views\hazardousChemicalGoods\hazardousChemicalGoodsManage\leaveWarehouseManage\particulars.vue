<script>
import tableListMixin from '@/mixins/tableListMixin'
export default {
  name: 'particulars',
  mixins: [tableListMixin],
  data() {
    return {
      searchForm: {
        unionSel: '', //出库单号/危化品编码/危化品名称
        officesId: '', //领用单位id
        officesName: '',
        warehouseName: '', //仓库id
        supplierId: '', //供应商id
        dateLine: [] //入库时间
      },
      deptOptions: [], //领用单位下拉
      supplierOptions: [], //供应商下拉
      warehouseOptions: [], //库房下拉
      supplierCurrentPage: 1, //供应商分页
      multipleSelection: [],
      tableData: [],
      tableLoadingStatus: false
    }
  },
  mounted() {
    this.getDeptList()
    this.getWarehouseListFn()
    this.getSupplierListFn()
    this.getDataList()
  },
  methods: {
    // 获取库房下拉
    getWarehouseListFn() {
      this.$api.getWarehouseList({ status: '0' }).then((res) => {
        if (res.code == '200') {
          this.warehouseOptions = res.data.list
        }
      })
    },
    //  获取部门列表
    getDeptList() {
      this.$api.getSelectedDept().then((res) => {
        if (res.code == 200) {
          this.deptOptions = res.data
        }
      })
    },
    /** 供应商懒加载 */
    loadmore() {
      this.supplierCurrentPage++
      this.getSupplierListFn()
    },
    //获取供应商下拉
    getSupplierListFn() {
      let params = {
        category: 2,
        unitsTypeCode: 'GYS',
        userId: this.$store.state.user.userInfo.user.staffId,
        userName: this.$store.state.user.userInfo.user.staffName,
        status: '0',
        currentPage: this.supplierCurrentPage,
        pageSize: 15
      }
      this.$api.getUnitsLsitData(params).then((res) => {
        if (res.code == '200') {
          this.supplierOptions = [...this.supplierOptions, ...res.data.list]
        }
      })
    },
    //导出
    exportShow() {
      const userInfo = this.$store.state.user.userInfo.user
      let temp1 = []
      this.multipleSelection.forEach((v, i) => {
        temp1[i] = this.multipleSelection[i].id
      })
      temp1 = temp1.join(',')
      let url =
        __PATH.BASE_URL_HSC +
        'outwarehouseRecord/materialRecordExport?ids=' +
        temp1 +
        '&unitCode=' +
        userInfo.unitCode +
        '&hospitalCode=' +
        userInfo.hospitalCode +
        '&userId=' +
        userInfo.staffId +
        '&userName=' +
        userInfo.staffName
      var a = document.createElement('a')
      a.href = url
      a.target = '_self'
      a.click()
    },
    // 获取列表数据
    getDataList() {
      this.tableData = []
      this.tableLoadingStatus = true
      const params = {
        pageSize: this.pagination.size,
        currentPage: this.pagination.current,
        userId: this.$store.state.user.userInfo.user.staffId,
        userName: this.$store.state.user.userInfo.user.staffName,
        ...this.searchForm
      }
      params.beginDate = params.dateLine.length ? params.dateLine[0] : ''
      params.endDate = params.dateLine.length ? params.dateLine[1] : ''
      delete params.dateLine
      this.$api
        .queryOutWarehouseRecordByPage(params)
        .then((res) => {
          this.tableLoadingStatus = false
          if (res.code === '200') {
            this.tableData = res.data.list
            this.pagination.total = res.data.sum
          } else {
            this.tableData = []
            this.pagination.total = 0
          }
        })
        .catch((err) => {
          this.tableLoadingStatus = false
        })
    },
    // 表单搜索按钮点击
    onSearch() {
      this.pagination.current = 1
      this.getDataList()
    },
    // 表单重置按钮点击
    onReset() {
      this.$refs.formRef.resetFields()
      this.onSearch()
    },
    //表格选择操作
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    //操作
    onOperate(type, row) {
      if (type === 'export') {
        this.exportShow()
      }
    }
  }
}
</script>
<template>
  <PageContainer class="particulars">
    <template #content>
      <div class="particulars__header">
        <el-form ref="formRef" :model="searchForm" class="particulars__search" inline @submit.native.prevent="onSearch">
          <el-form-item prop="unionSel">
            <el-input v-model="searchForm.unionSel" clearable placeholder="出库单号/危化品编码/危化品名称"></el-input>
          </el-form-item>
          <el-form-item prop="officesName">
            <!-- <el-select v-model="searchForm.officesId" placeholder="领用部门" filterable>
              <el-option v-for="item in deptOptions" :key="item.id" :label="item.deptName" :value="item.id"></el-option>
            </el-select> -->
            <el-input v-model="searchForm.officesName" clearable placeholder="领用部门"></el-input>
          </el-form-item>
          <!-- <el-form-item prop="supplierId">
            <el-select v-model="searchForm.supplierId" placeholder="供应商" filterable clearable
              v-selectLoadmore="loadmore">
              <el-option v-for="item in supplierOptions" :key="item.id" :label="item.unitsName"
                :value="item.id"></el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item prop="warehouseName">
            <el-select v-model="searchForm.warehouseName" filterable placeholder="请选择仓库">
              <el-option v-for="item of warehouseOptions" :key="item.id" :label="item.warehouseName" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="dateLine">
            <el-date-picker
              v-model="searchForm.dateLine"
              type="daterange"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="出库开始时间"
              end-placeholder="出库结束时间"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" plain @click="onReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="particulars__actions">
        <el-button type="primary" v-auth="'ckManagement:export'" @click="onOperate('export')" :disabled="multipleSelection.length === 0">导出</el-button>
      </div>
      <div class="particulars__table">
        <el-table
          v-loading="tableLoadingStatus"
          height="100%"
          :data="tableData"
          border
          stripe
          table-layout="auto"
          @selection-change="handleSelectionChange"
          class="tableAuto"
          row-key="id"
        >
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column prop="orderNumber" show-overflow-tooltip label="出库单号"></el-table-column>
          <!-- <el-table-column prop="materialCode" show-overflow-tooltip label="危化品编码"></el-table-column> -->
          <el-table-column prop="materialName" show-overflow-tooltip label="危化品名称"></el-table-column>
          <el-table-column prop="model" show-overflow-tooltip label="规格型号"></el-table-column>
          <el-table-column prop="basicUnitName" show-overflow-tooltip label="基础单位"></el-table-column>
          <el-table-column prop="warehouseName" show-overflow-tooltip label="出库仓库"></el-table-column>
          <el-table-column prop="createTime" show-overflow-tooltip label="出库时间"></el-table-column>
          <el-table-column prop="operateCount" show-overflow-tooltip label="出库数量"></el-table-column>
          <!-- <el-table-column prop="inventory" label="进货单价（元）" show-overflow-tooltip></el-table-column>
          <el-table-column prop="amount" label="总金额（元）" show-overflow-tooltip></el-table-column> -->
          <el-table-column prop="serviceLife" label="有效期" show-overflow-tooltip></el-table-column>
          <!-- <el-table-column prop="trademark" show-overflow-tooltip label="品牌"></el-table-column> -->
          <!-- <el-table-column prop="supplierName" show-overflow-tooltip label="供应商"></el-table-column> -->
        </el-table>
      </div>
      <el-pagination
        class="particulars__pagination"
        :current-page="pagination.page"
        :page-sizes="pagination.pageSizeOptions"
        :page-size="pagination.size"
        :layout="pagination.layoutOptions"
        :total="pagination.total"
        @size-change="paginationSizeChange"
        @current-change="paginationCurrentChange"
      >
      </el-pagination>
    </template>
  </PageContainer>
</template>
<style scoped lang="scss">
.particulars {
  ::v-deep(> .container-content) {
    height: 100%;
    width: 100%;
    background-color: #fff;
  }
  &__header {
    display: flex;
    justify-content: space-between;
    .el-input {
      width: 200px;
    }
  }
  &__actions {
    padding-bottom: 10px;
  }
  &__table {
    height: calc(100% - 170px);
  }
  &__pagination {
    margin-top: 10px;
  }
  .text-red {
    color: #ff1919;
  }
}
.el-form-item {
  margin-bottom: 8px !important;
}
</style>
