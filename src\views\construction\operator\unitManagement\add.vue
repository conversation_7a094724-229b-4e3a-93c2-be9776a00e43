<template>
  <el-dialog
    v-dialogDrag
    custom-class="model-dialog"
    :modal="false"
    :close-on-click-modal="false"
    :title="id ? '编辑单位' : '新增单位'"
    :visible="visible"
    width="50%"
    :before-close="dialogClose"
  >
    <div class="dialog-content">
      <el-form ref="form" :model="form" :rules="rules" label-width="130px">
        <div class="table-title">
          <span class="title"> <i></i>基本信息</span>
        </div>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="单位名称" prop="companyName">
              <el-input v-model="form.companyName" placeholder="请输入单位名称" maxlength="50"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="信用代码" prop="creditCode">
              <el-input v-model="form.creditCode" placeholder="请输入信用代码" maxlength="50"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="法人姓名" prop="legalPersonName">
              <el-input v-model="form.legalPersonName" placeholder="请输入法人姓名" maxlength="50"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号码" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入手机号码" maxlength="11"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="启用状态" prop="enableStatus">
              <el-radio-group v-model="form.enableStatus">
                <el-radio label="0">启用</el-radio>
                <el-radio label="1">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" show-word-limit maxlength="200"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="24" v-if="idCardImageRInfo.isShow">
            <el-form-item :label="idCardImageRInfo.label" :required="idCardImageRInfo.required">
              <el-upload
                action=""
                list-type="picture-card"
                :file-list="idCardImageRList"
                :accept="uploadAcceptDict['picture'].type"
                :limit="1"
                :http-request="(file) => httpRequset(file, 'idCardImageR')"
                :on-remove="(file, fileList) => handleRemove(fileList, 'idCardImageR')"
              >
                <i class="el-icon-plus"><br /></i>
                <div slot="tip" class="el-upload__tip">支持{{ uploadAcceptDict['picture'].type }},且大小不超过{{ 5 }}M</div>
              </el-upload>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="idCardImageSInfo.isShow">
            <el-form-item :label="idCardImageSInfo.label" :required="idCardImageSInfo.required">
              <el-upload
                action=""
                list-type="picture-card"
                :file-list="idCardImageSList"
                :accept="uploadAcceptDict['picture'].type"
                :limit="1"
                :before-upload="beforeAvatarUpload"
                :http-request="(file) => httpRequset(file, 'idCardImageS')"
                :on-remove="(file, fileList) => handleRemove(fileList, 'idCardImageS')"
              >
                <i class="el-icon-plus"><br /></i>
                <div slot="tip" class="el-upload__tip">支持{{ uploadAcceptDict['picture'].type }},且大小不超过{{ 20 }}M</div>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10" v-if="businessImageInfo.isShow">
          <el-col :span="24">
            <el-form-item :label="businessImageInfo.label" :required="businessImageInfo.required">
              <el-upload
                action=""
                list-type="picture-card"
                :file-list="businessImageList"
                :accept="uploadAcceptDict['picture'].type"
                :limit="1"
                :before-upload="beforeAvatarUpload"
                :http-request="(file) => httpRequset(file, 'businessImage')"
                :on-remove="(file, fileList) => handleRemove(fileList, 'businessImage')"
              >
                <i class="el-icon-plus"><br /></i>
                <div slot="tip" class="el-upload__tip">支持{{ uploadAcceptDict['picture'].type }},且大小不超过{{ 20 }}M</div>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10" v-if="attachmentInfoInfo.isShow">
          <el-col :span="24">
            <el-form-item :label="attachmentInfoInfo.label" :required="attachmentInfoInfo.required">
              <el-upload
                action=""
                list-type="list"
                :file-list="attachmentInfoList"
                accept=".jpg,.jpeg,.png,.gif,.doc,.docx,.pdf,.word"
                :limit="50"
                :before-upload="beforeAvatarUpload"
                :http-request="(file) => httpRequset(file, 'attachmentInfo')"
                :on-remove="(file, fileList) => handleRemove(fileList, 'attachmentInfo')"
              >
                <el-button size="small" type="primary">点击上传</el-button>
                <div slot="tip" class="el-upload__tip">支持.jpg,.jpeg,.png,.gif,.doc,.docx,.pdf,.word,且大小不超过{{ 20 }}M</div>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10" v-if="fireControlInfoInfo.isShow">
          <el-col :span="24">
            <el-form-item :label="fireControlInfoInfo.label" :required="fireControlInfoInfo.required">
              <el-upload
                action=""
                list-type="list"
                :file-list="fireControlInfoList"
                accept=".jpg,.jpeg,.png,.gif,.doc,.docx,.pdf,.word"
                :limit="50"
                :before-upload="beforeAvatarUpload"
                :http-request="(file) => httpRequset(file, 'fireControlInfo')"
                :on-remove="(file, fileList) => handleRemove(fileList, 'fireControlInfo')"
              >
                <el-button size="small" type="primary">点击上传</el-button>
                <div slot="tip" class="el-upload__tip">支持.jpg,.jpeg,.png,.gif,.doc,.docx,.pdf,.word,且大小不超过{{ 20 }}M</div>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10" v-if="UnitElseFileInfo.isShow">
          <el-col :span="24">
            <el-form-item :label="UnitElseFileInfo.label" :required="UnitElseFileInfo.required">
              <el-upload
                action=""
                list-type="list"
                :file-list="elseFileList"
                accept="*"
                :limit="50"
                :http-request="(file) => httpRequset(file, 'elseFile')"
                :on-remove="(file, fileList) => handleRemove(fileList, 'elseFile')"
              >
                <el-button size="small" type="primary">点击上传</el-button>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="24" v-if="handleType != 'detail'"><el-button type="primary" plain icon="el-icon-plus" @click="addCertificate">添加证照</el-button></el-col>
          <el-col :span="24" v-if="certificateList.length">
            <div class="zzBox" v-for="(item, index) in certificateList" :key="index">
              <div class="zzBox-left">
                <el-form-item label="证照名称" style="margin-bottom: 0px" :required="filterIsWrited(item.fileCode)">
                  <el-select v-model="item.fileCode" placeholder="请选择活动区域" :disabled="handleType == 'detail'" clearable @change="changeKey($event, index)">
                    <el-option v-for="i in certificateOptions" :key="i.id" :label="i.fileName" :value="i.fileCode"></el-option>
                  </el-select>
                </el-form-item>
              </div>
              <div class="zzBox-right">
                <el-upload
                  :disabled="handleType == 'detail'"
                  action=""
                  list-type="list"
                  :file-list="item.url"
                  accept="*"
                  :before-upload="beforeAvatarUpload"
                  :http-request="(file) => httpCertificateRequset(file, index)"
                  :on-remove="(file, fileList) => handleCertificateRemove(fileList, index)"
                  :on-change="handleCertificateFileChange.bind(this, index)"
                  :on-preview="handlePreview"
                >
                  <div slot="tip" class="el-upload__tip">大小不超过20M</div>
                  <el-button :disabled="handleType == 'detail'" size="medium" type="primary">点击上传</el-button>
                </el-upload>
              </div>
              <div class="zzBox-del" @click="delCertificate(index)" v-if="handleType != 'detail'"><span class="el-icon-delete" style="color: #f53f3f"></span></div>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div slot="footer">
      <template>
        <el-button @click="dialogClose">取 消</el-button>
        <el-button type="primary" @click="submit" :loading="buttonLoading">确 定</el-button>
      </template>
    </div>
  </el-dialog>
</template>
<script>
import { uploadAcceptDict } from '@/util/dict.js'
import dayjs from 'dayjs'
export default {
  model: {
    prop: 'visible',
    event: 'update:visible'
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    },
    handleType: {
      type: String,
      default: 'add'
    }
  },
  data() {
    return {
      uploadAcceptDict,
      buttonLoading: false,
      unitList: [],
      businessImageList: [],
      attachmentInfoList: [],
      fireControlInfoList: [],
      elseFileList: [],
      idCardImageRList: [],
      idCardImageSList: [],
      form: {
        id: this.id,
        companyName: '',
        creditCode: '',
        phone: '',
        enableStatus: '0',
        legalPersonName: '',
        remark: '',
        businessImage: '',
        attachmentInfo: '',
        fireControlInfo: '',
        elseFile: '',
        idCardImageR: '',
        idCardImageS: ''
      },
      rules: {
        companyName: [{ required: true, message: '请输入单位名称', trigger: 'blur' }],
        // creditCode: [{ required: true, message: '请输入单位社会信用代码', trigger: 'blur' }],
        legalPersonName: [{ required: true, message: '请输入法人姓名', trigger: 'blur' }]
        // phone: [{ required: false, message: '请输入手机号', trigger: 'blur' }]
      },
      certificateList: [], // 证照添加列表
      certificateOptions: [], // 证照列表信息
      idCardImageRInfo: {
        label: '法人身份证正面',
        isShow: true,
        required: false
      },
      idCardImageSInfo: {
        label: '法人身份证反面',
        isShow: true,
        required: false
      },
      businessImageInfo: {
        label: '营业执照',
        isShow: true,
        required: false
      },
      attachmentInfoInfo: {
        label: '施工安全协议',
        isShow: true,
        required: false
      },
      fireControlInfoInfo: {
        label: '消防安全承诺',
        isShow: true,
        required: false
      },
      UnitElseFileInfo: {
        label: '其他文件',
        isShow: true,
        required: false
      }
    }
  },
  mounted() {
    this.getCertificateList()
    if (this.id) {
      this.getDetail()
    }
  },
  methods: {
    filterIsWrited(val) {
      if (!val) return false
      let obj = this.certificateOptions.find((item) => item.fileCode === val)
      if (obj && obj.writed == 1) return true
      return false
    },
    // 证照名称选择事件
    changeKey(val, index) {
      if (val) {
        let obj = this.certificateOptions.find((item) => item.fileCode === val)
        if (obj) {
          this.certificateList[index].writed = obj.writed
          this.certificateList[index].fileName = obj.fileName
        }
      } else {
        this.certificateList[index].writed = 0
        this.certificateList[index].fileName = ''
      }
    },
    // 添加证照
    addCertificate() {
      let obj = {
        fileCode: '',
        fileName: '',
        writed: 0,
        url: []
      }
      this.certificateList.push(obj)
    },
    // 删除证照
    delCertificate(index) {
      this.certificateList.splice(index, 1)
    },
    // 证照上传
    async httpCertificateRequset(file, index) {
      let res = await this.$api.constructionUpload(__PATH.SPACE_API, 'SecurityLedger/upload', file)
      if (res.code === '200') {
        this.$message({
          message: '上传成功',
          type: 'success'
        })
        const fileInfo = this.certificateList[index].url.find((it) => it.uid === file.file.uid)
        if (fileInfo) {
          fileInfo.uploadPath = res.data.fileKey
        }
      } else {
        this.$message.error(res.message)
      }
    },
    // 证照上传删除
    handleCertificateRemove(fileList, index) {
      this.certificateList[index].url = fileList
    },
    // 证照上传 失踪同步文件列表
    handleCertificateFileChange(idx, file) {
      if (file.status === 'fail') {
        // 失败的文件，500ms后删除，视觉效果会好一些
        window.clearTimeout(this.$timerId)
        this.$timerId = window.setTimeout(() => {
          let index = -1
          do {
            index = this.certificateList[idx].url.findIndex((it) => it.status === 'fail')
            if (index > -1) {
              this.certificateList[idx].url.splice(index, 1)
            }
          } while (index > -1)
          this.$timerId = -1
        }, 500)
      } else {
        const index = this.certificateList[idx].url.findIndex((it) => it.uid === file.uid)
        if (index > -1) {
          this.certificateList[idx].url.splice(index, 1, file)
        } else {
          this.certificateList[idx].url.push(file)
        }
      }
    },
    // 获取证照信息
    getCertificateList() {
      const params = {
        fileName: '',
        enableStatus: '',
        type: 1,
        page: 1,
        pageSize: 999999
      }
      this.$api
        .getConstructionCertificateList(params)
        .then((res) => {
          if (res.code === '200') {
            if (res.data.records && res.data.records.length) {
              let field = ['attachmentInfo', 'fireControlInfo', 'UnitElseFile', 'idCardImageR', 'idCardImageS', 'businessImage']
              field.forEach((item) => {
                let findData = res.data.records.find((i) => i.fileCode === item)
                if (findData) {
                  this[`${item}Info`].label = findData.fileName
                  this[`${item}Info`].isShow = findData.enableStatus === 1 ? true : findData.enableStatus === 0 ? false : true
                  this[`${item}Info`].required = findData.writed === 1 ? true : findData.writed === 0 ? false : true
                }
              })
              this.certificateOptions = res.data.records.filter((item) => item.initialed == 2 && item.enableStatus == 1)
            }
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '查询失败'))
    },
    handlePreview(file) {
      window.open(file.url)
    },
    getDetail() {
      this.$api.unitConstructionDetail({ id: this.id }).then((res) => {
        if (res.code == 200) {
          this.form = res.data
          if (res.data.businessImage) {
            res.data.businessImage.split(',').forEach((el) => {
              if (el) {
                this.businessImageList.push({
                  name: '',
                  url: el
                })
              }
            })
          }
          if (res.data.idCardImageR) {
            res.data.idCardImageR.split(',').forEach((el) => {
              if (el) {
                this.idCardImageRList.push({
                  name: '',
                  url: el
                })
              }
            })
          }
          if (res.data.idCardImageS) {
            res.data.idCardImageS.split(',').forEach((el) => {
              if (el) {
                this.idCardImageSList.push({
                  name: '',
                  url: el
                })
              }
            })
          }
          let arr = ['attachmentInfo', 'fireControlInfo', 'elseFile']
          arr.forEach((key) => {
            console.log(res.data[key], JSON.parse(res.data[key]))
            this[`${key}List`] = JSON.parse(res.data[key]) || []
          })
        }
      })
    },
    dialogClose() {
      this.buttonLoading = false
      this.$emit('update:visible', false)
    },
    beforeAvatarUpload(file) {
      const fileSize = file.size / 1024 / 1024 < 20
      if (!fileSize) {
        this.$message.error('上传图片大小不能超过 20MB!')
      }
      return fileSize
    },
    httpRequset(file, type) {
      this.$api.constructionUpload(__PATH.SPACE_API, 'SecurityLedger/upload', file).then((res) => {
        if (res.code == 200) {
          let obj = {
            attachment: res.data.name,
            name: res.data.name,
            createBy: this.$store.state.user.userInfo.user.staffName,
            url: res.data.picUrl,
            uploadPath: res.data.fileKey,
            createDate: dayjs().format('YYYY-MM-DD HH:mm:ss')
          }
          this[`${type}List`].push(obj)
          if (type == 'businessImage' || type == 'idCardImageR' || type == 'idCardImageS') {
            this.form[type] = this[`${type}List`].map((v) => v.uploadPath).join(',')
          } else {
            this.form[type] = JSON.stringify(this[`${type}List`])
          }
        }
      })
    },
    handleRemove(fileList, key) {
      this[`${key}List`] = fileList
      if (key == 'businessImage' || key == 'idCardImageR' || key == 'idCardImageS') {
        this.form[key] = this[`${key}List`].map((v) => v.uploadPath).join(',') || ''
      } else {
        this.form[key] = JSON.stringify(this[`${key}List`]) || ''
      }
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          let fn = this.id ? 'editConstructionUnit' : 'addConstructionUnit'
          let arr = []
          if (this.idCardImageRInfo.required && this.idCardImageRInfo.isShow && !this.idCardImageRList.length) {
            return this.$message.error(`请上传${this.idCardImageRInfo.label}`)
          }
          if (this.idCardImageSInfo.required && this.idCardImageSInfo.isShow && !this.idCardImageSList.length) {
            return this.$message.error(`请上传${this.idCardImageSInfo.label}`)
          }
          if (this.businessImageInfo.required && this.businessImageInfo.isShow && !this.businessImageList.length) {
            return this.$message.error(`请上传${this.businessImageInfo.label}`)
          }
          if (this.attachmentInfoInfo.required && this.attachmentInfoInfo.isShow && !this.attachmentInfoList.length) {
            return this.$message.error(`请上传${this.attachmentInfoInfo.label}`)
          }
          if (this.fireControlInfoInfo.required && this.fireControlInfoInfo.isShow && !this.fireControlInfoList.length) {
            return this.$message.error(`请上传${this.fireControlInfoInfo.label}`)
          }
          if (this.UnitElseFileInfo.required && this.UnitElseFileInfo.isShow && !this.elseFileList.length) {
            return this.$message.error(`请上传${this.UnitElseFileInfo.label}`)
          }
          if (this.certificateList.length) {
            let status = this.certificateList.filter((v) => v.writed == 1).every((item) => item.url.length)
            if (!status) {
              return this.$message.error('请完善证照信息')
            }
            this.certificateList.forEach((item) => {
              if (item.url.length) {
                arr.push({
                  fileCode: item.fileCode,
                  fileName: item.fileName,
                  url: JSON.stringify(
                    item.url.map((i) => {
                      return {
                        url: i.uploadPath,
                        name: i.name
                      }
                    })
                  )
                })
              }
            })
          }
          this.buttonLoading = true
          this.$api[fn]({ ...this.form, fileAttachment: arr.length ? JSON.stringify(arr) : '' })
            .then((res) => {
              if (res.code == 200) {
                this.$message.success('添加成功')
                this.$emit('success')
                this.dialogClose()
              } else {
                this.$message.error(res.msg || res.message)
              }
            })
            .catch(() => {})
            .finally(() => {
              this.buttonLoading = false
            })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 16px;
}
.el-form {
  .el-form-item {
    .el-form-item__content {
      .el-input,
      .el-select,
      .el-date-picker,
      .el-cascader {
        width: 100%;
      }
    }
  }
}
.zzBox {
  margin: 15px 0px;
  display: flex;
  .zzBox-left {
    width: 400px;
    flex-shrink: 0;
  }
  .zzBox-right {
    width: 350px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    margin-left: 20px;
  }
  .zzBox-del {
    margin-left: 20px;
    font-size: 26px;
    cursor: pointer;
  }
}
/* 隐藏提示 */
::v-deep .el-upload-list__item.is-success.focusing .el-icon-close-tip {
  display: none !important;
}
</style>
