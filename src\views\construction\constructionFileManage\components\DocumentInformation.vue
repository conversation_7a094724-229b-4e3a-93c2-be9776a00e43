<!-- eslint-disable vue/no-mutating-props -->
<template>
  <div>
    <div v-if="!isEdit" class="details">
      <el-row :gutter="24">
        <el-col :span="8">
          <FormTextItem title="文档名称" :value="formData.archiveName" />
        </el-col>
        <el-col :span="16">
          <FormTextItem title="存放位置" :value="formData.saveLocation" type="textarea" />
        </el-col>
        <el-col :span="24">
          <FormTextItem title="摘要信息" :value="formData.remark" type="textarea" />
        </el-col>
        <el-col :span="24">
          <FormTextItem title="附件资料" type="slot">
            <el-table :data="formData.archiveFileList">
              <el-table-column label="序号" type="index" width="50"> </el-table-column>
              <el-table-column property="fileName" label="文件名"> </el-table-column>
              <el-table-column property="fileSize" label="大小（M）"> </el-table-column>
              <el-table-column property="option" label="操作" width="120">
                <template slot-scope="scope">
                  <el-button type="text" @click="handleView(scope.row)">查看</el-button>
                  <el-button type="text" @click="handleDownload(scope.row)"> 下载 </el-button>
                </template>
              </el-table-column>
            </el-table>
          </FormTextItem>
        </el-col>
      </el-row>
    </div>
    <div v-else>
      <el-form ref="form" :model="formData" label-width="80px" :rules="rules">
        <el-row :gutter="24">
          <el-col :span="9">
            <el-form-item label="文档名称" prop="archiveName">
              <el-input v-model="formData.archiveName" maxlength="50" show-word-limit></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="存放位置">
              <el-input v-model="formData.saveLocation" type="textarea" maxlength="200" show-word-limit
                placeholder="请输入存放位置"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="摘要信息">
              <el-input v-model="formData.remark" type="textarea" maxlength="200" show-word-limit
                placeholder="请输入摘要信息"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="附件资料">
              <el-upload class="upload-demo" :action="''" :on-success="handleOnSuccess" :before-upload="beforeUpload"
                multiple :http-request="httpRequset" :show-file-list="false">
                <el-button size="small" type="secondary">
                  <em class="el-icon-upload2"></em>
                  点击上传
                </el-button>
                <div slot="tip" class="el-upload__tip">文件大小限制20M</div>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
        <div class="tableBox">
          <el-table :data="formData.archiveFileList" highlight-current-row>
            <el-table-column label="序号" type="index" width="50"> </el-table-column>
            <el-table-column property="fileName" label="文件名"> </el-table-column>
            <el-table-column property="fileSize" label="大小（M）"> </el-table-column>
            <el-table-column property="option" label="操作" width="120">
              <template slot-scope="scope">
                <el-button type="text" @click="handleDelete(scope)"> 删除 </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-form>
    </div>
  </div>
</template>
<script>
import FormTextItem from '@/views/operationPort/dossierManager/components/FormTextItem.vue'
import mixins from '@/views/operationPort/dossierManager/mixins/index'
import moment from 'moment'
export default {
  components: { FormTextItem },
  mixins: [mixins],
  props: {
    formData: {
      type: Object,
      default: () => ({})
    },
    tableData: {
      type: Array,
      default: () => []
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      moment,
      rules: {
        archiveName: [
          {
            required: true,
            message: '请输入文档名称',
            trigger: ['change', 'blur']
          }
        ],
      }
    }
  },
}
</script>
<style lang="scss" scoped>
::v-deep .el-select,
::v-deep .el-date-editor.el-input {
  width: 100%;
}
::v-deep .upload-demo {
  display: flex;
  .el-upload__tip {
    margin-top: 0;
    margin-left: 8px;
    color: #8c8c8c;
  }
}
.tableBox {
  padding-left: 80px;
}
::v-deep .formLabel {
  display: inline-block;
  min-width: 80px;
}
::v-deep .ellipsis_showtip_plus {
  white-space: wrap !important;
}
</style>
