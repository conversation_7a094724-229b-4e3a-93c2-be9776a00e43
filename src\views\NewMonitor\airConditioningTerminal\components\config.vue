<template>
  <div>
    <div class="configContent">
      <div class="configTitle">
        <img src="@/assets/images/parkingLot/warpper.png" alt="" />
        <span>警单配置</span>
      </div>
      <el-form ref="formInline" :model="formInline" :inline="true" class="form-inline" :rules="addRules"
        label-width="110px">
        <el-form-item label="是否弹出警单" prop="alarmModel">
          <el-switch v-model="formInline.alarmModel" active-color="#3562DB" inactive-color="#CCCED3 "
            :disabled="activeType === 'detail'" @change="alarmModelChange">
          </el-switch>
        </el-form-item>
        <el-form-item style="margin-left:7px">
          <el-tooltip popper-class="tps1" effect="dark" content="报警产生时，专业客户端会弹出报警的弹窗" placement="right-start">
            <span><i class="el-icon-question"></i></span>
          </el-tooltip>
        </el-form-item>
        <div>
          <el-form-item v-if="formInline.alarmModel" label="处置终端" prop="disposalErminal">
            <el-checkbox-group v-model="formInline.disposalErminal" :disabled="activeType === 'detail'">
              <el-checkbox label="0">专业客户端</el-checkbox>
              <el-checkbox label="1">手机端</el-checkbox>
              <el-checkbox label="2" disabled>web端</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </div>
        <template v-if="formInline.alarmModel">
          <el-form-item label="提醒方式" prop="alarmResponseWay">
            <el-checkbox-group v-model="formInline.alarmResponseWay" :disabled="activeType === 'detail'">
              <el-checkbox label="0" disabled>弹窗监控联动</el-checkbox>
              <el-checkbox label="3">是否上墙</el-checkbox>
              <el-checkbox label="1">声光报警</el-checkbox>
              <el-checkbox label="2">语音播报</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="播放次数">
            <el-input v-model="formInline.alarmResponseNum" placeholder="请输入内容"
              :disabled="activeType === 'detail'|| !formInline.alarmResponseWay.includes('2')" style="width:120px">
              <template slot="append">次</template>
            </el-input>
            <span style="color: #7F848C;margin-left:5px">默认播报示例: 急诊急救综合楼5层空调机房 空调机组001 设备故障报警， 请及时处理</span>
          </el-form-item>
        </template>
        <template v-if="!formInline.alarmModel">
          <el-form-item label="警情解除能力" prop="alarmSecureWay">
            <el-checkbox-group v-model="formInline.alarmSecureWay" :disabled="activeType === 'detail'">
              <el-checkbox label="0">中控室解除（三屏客户端）</el-checkbox>
              <el-checkbox label="1">手机端</el-checkbox>
              <el-checkbox label="2">web端</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </template>
        <br />
        <div class="configTitle">
          <img src="@/assets/images/parkingLot/warpper.png" alt="" />
          <span>警情联动配置</span>
        </div>
        <br />
        <el-form-item v-if="formInline.alarmModel" key="preplanId" label="启用预案">
          <el-input v-model.trim="formInline.preplanName" :disabled="activeType === 'detail'" placeholder="请选择智能预案"
            style="width:340px" @focus="handleClick">
          </el-input>
        </el-form-item>
        <br />
        <el-form-item label="自动派发工单">
          <el-radio-group v-model="formInline.autoSendOrder" :disabled="activeType === 'detail'"
            @input="autoSendOrderInput">
            <el-radio :label="'0'">否</el-radio>
            <el-radio :label="'1'">是</el-radio>
          </el-radio-group>
        </el-form-item>
        <br />
        <el-form-item v-if="formInline.autoSendOrder==='0'" label="工单事项">
          <el-cascader v-model="formInline.workOrderCode"
            :disabled="activeType === 'detail'||formInline.autoSendOrder=='0'" :options="itemTreeData"
            :props="orderPropsType" clearable placeholder="选择关联事项" style="width:340px"
            @change="handleOrderChange"></el-cascader>
        </el-form-item>
        <el-form-item v-if="formInline.autoSendOrder==='0'" label="班组名称">
          <el-select v-model="formInline.teamCode" :disabled="activeType === 'detail'||formInline.autoSendOrder=='0'"
            placeholder="选择工单班组" style="width:340px" clearable @change="teamCodeChange">
            <el-option v-for="item in teamsOptions" :key="item.id" :label="item.team_name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="formInline.autoSendOrder==='1'" key="workOrderCode" label="工单事项" prop="workOrderCode"
          required>
          <el-cascader ref="cascader" v-model="formInline.workOrderCode" :options="itemTreeData" :props="orderPropsType"
            clearable placeholder="选择关联事项" style="width:340px" @change="handleOrderChange"></el-cascader>
        </el-form-item>
        <el-form-item v-if="formInline.autoSendOrder==='1'" key="teamCode" label="班组名称" prop="teamCode" required>
          <el-select v-model="formInline.teamCode" placeholder="选择工单班组" style="width:340px" clearable
            @change="teamCodeChange">
            <el-option v-for="item in teamsOptions" :key="item.id" :label="item.team_name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <div class="generation-content">
          <div class="generationContent-title"> 报警产生时</div>
          <div class="generationContent-info">
            <div v-for="(item, index) in generationContents" :key="index" :gutter="20" class="content-block">
              <div class="generationContent-input">
                <div class="label">通知方式：</div>
                <el-select v-model="item.noticeWay" placeholder="请选择" :disabled="activeType === 'detail'">
                  <el-option v-for="item in informOptions" :key="item.value" :label="item.label"
                    :value="item.value"></el-option>
                </el-select>
              </div>
              <div class="generationContent-input">
                <div class="label">通知部门或人员：</div>
                <div style="display: flex;">
                  <el-select v-model="item.noticeType" class="noticeType" :disabled="activeType === 'detail'"
                    @change="changeNoticeType(item, index)">
                    <el-option label="人员" :value="'1'"></el-option>
                    <el-option label="部门" :value="'0'"></el-option>
                  </el-select>
                  <el-input v-if="item.noticeType === '1'" v-model="item.personName" :disabled="activeType === 'detail'"
                    type="text" readonly placeholder="请选择" suffix-icon="el-icon-arrow-down" style="flex: 1;width:100px"
                    @focus="control('selectPers', index,'produce')"></el-input>
                  <el-select v-else v-model="item.departCodes" placeholder="请选择" :disabled="activeType === 'detail'"
                    clearable multiple collapse-tags filterable style="flex: 1; width:100px "
                    @change="(val) => selectDept(val, index)">
                    <el-option v-for="item in deptList" :key="item.id" :label="item.deptName"
                      :value="item.id"></el-option>
                  </el-select>
                </div>
              </div>
              <div class="generationContent-input">
                <div v-if="item.noticeType ==='1'" style="display:flex">
                  <div class="label">联系方式：</div>
                  <div style="display: flex;">
                    <el-input v-model="item.tel" placeholder="请输入内容" :disabled="activeType === 'detail'"></el-input>
                    <el-tooltip v-if="item.noticeWay==='1'" popper-class="tps" effect="dark"
                      content="默认语音电话示例: 急诊急救综合楼5层空调机房 空调机组001 设备故障报警，报警等级：重要 ，请及时处理" placement="top"
                      style="margin-left:8px">
                      <span style="margin:auto"><i class="el-icon-question"></i></span>
                    </el-tooltip>
                  </div>
                </div>
                <el-tooltip v-else popper-class="tps3" placement="bottom" :open-delay="500">
                  <div slot="content" class="tooltip-content" style="width:400px">
                    <div class="content-head">
                      <div class="deptName">
                        <p class="deptName-name">{{ item.departName }}</p>
                        <p class="deptName-num">{{ item.noticePersonList.length}}人</p>
                      </div>
                      <div class="head-mobie">联系方式：</div>
                    </div>
                    <div class="content-list">
                      <div v-for="dept in item.selectDeptList" :key="dept.id" class="list-item">
                        <p class="item-name">{{ dept.deptName }}</p>
                        <div v-for="user in dept.userList" :key="user.personId" class="item-user">
                          <div
                            :style="{color: (user.noticePhone && user.noticePhone.length == 11) && user.haveAppAccount ? '' : '#FA403C'}">
                            {{ user.personName }}</div>
                          <div style="display: flex;">
                            <p
                              :style="{color: (user.noticePhone && user.noticePhone.length == 11) && user.haveAppAccount ? '' : '#FA403C'}">
                              {{ user.noticePhone }}</p>
                            <p style="color: #FA403C; margin-left: 20px;">
                              {{ user.noticePhone && user.noticePhone.length == 11 ? (user.haveAppAccount ? '' : '未开通APP账号') : '缺失号码' }}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="deptName">
                    <p class="deptName-name">{{ item.departName }}</p>
                    <p v-show="item.noticePersonList.length" class="deptName-num">{{ item.noticePersonList.length}}人</p>
                    <i v-show="item.noticePersonList.map(v => v.haveAppAccount).includes(false) || item.noticePersonList.map(v => v.noticePhone ? v.noticePhone.length == 11 : false).includes(false)"
                      class="el-icon-warning"></i>
                  </div>
                </el-tooltip>
              </div>
              <div v-if="activeType !== 'detail'" class="generationContent-operation">
                <span class="generationContent-button button-add" @click="control('add')">
                  <i class="el-icon-plus"></i>
                  <span>添加</span>
                </span>
                <span v-if="index != 0" class="termContent-button button-detele" @click="control('delete', index)">
                  <i class="el-icon-delete"></i>
                  <span>删除</span>
                </span>
              </div>
            </div>
          </div>
        </div>
        <div v-if="formInline.preplanId==''||!formInline.preplanId" class="generation-content">
          <div class="generationContent-title"> 点击确警时</div>
          <div class="generationContent-info">
            <div v-for="(item, index) in generationSureContents" :key="index" :gutter="20" class="content-block">
              <div class="generationContent-input">
                <div class="label">通知方式：</div>
                <el-select v-model="item.noticeWay" placeholder="请选择" :disabled="activeType === 'detail'">
                  <el-option v-for="item in informOptions" :key="item.value" :label="item.label"
                    :value="item.value"></el-option>
                </el-select>
              </div>
              <div class="generationContent-input">
                <div class="label">通知部门或人员：</div>
                <div style="display: flex;">
                  <el-select v-model="item.noticeType" class="noticeType" :disabled="activeType === 'detail'"
                    @change="changeNoticeType1(item,index)">
                    <el-option label="人员" :value="'1'"></el-option>
                    <el-option label="部门" :value="'0'"></el-option>
                  </el-select>
                  <el-input v-if="item.noticeType === '1'" v-model="item.personName" :disabled="activeType === 'detail'"
                    type="text" readonly placeholder="请选择" suffix-icon="el-icon-arrow-down" style="flex: 1;width:100px"
                    @focus="controlSure('selectPers', index,'sure')"></el-input>
                  <el-select v-else v-model="item.departCodes" placeholder="请选择" :disabled="activeType === 'detail'"
                    clearable multiple collapse-tags filterable style="flex: 1; width:100px "
                    @change="(val) => selecSuretDept(val, index)">
                    <el-option v-for="item in deptList" :key="item.id" :label="item.deptName"
                      :value="item.id"></el-option>
                  </el-select>
                </div>
              </div>
              <div class="generationContent-input">
                <div v-if="item.noticeType ==='1'" style="display:flex">
                  <div class="label">联系方式：</div>
                  <div style="display: flex;">
                    <el-input v-model="item.tel" :disabled="activeType === 'detail'" placeholder="请输入内容"></el-input>
                    <el-tooltip v-if="item.noticeWay==='1'" popper-class="tps" effect="dark"
                      content="默认语音电话示例: 急诊急救综合楼5层空调机房 空调机组001 设备故障报警，报警等级：重要 ，请及时处理" placement="top"
                      style="margin-left:8px">
                      <span style="margin:auto"><i class="el-icon-question"></i></span>
                    </el-tooltip>
                  </div>
                </div>
                <el-tooltip v-else placement="bottom" popper-class="tps3" :open-delay="500">
                  <div slot="content" class="tooltip-content">
                    <div class="content-head">
                      <div class="deptName">
                        <p class="deptName-name">{{ item.departName }}</p>
                        <p class="deptName-num">{{ item.noticePersonList.length}}人</p>
                      </div>
                      <div class="head-mobie">联系方式：</div>
                    </div>
                    <div class="content-list">
                      <div v-for="dept in item.selectDeptList" :key="dept.id" class="list-item">
                        <p class="item-name">{{ dept.deptName }}</p>
                        <div v-for="user in dept.userList" :key="user.personId" class="item-user">
                          <div
                            :style="{color: (user.noticePhone && user.noticePhone.length == 11) && user.haveAppAccount ? '' : '#FA403C'}">
                            {{ user.personName }}</div>
                          <div style="display: flex;">
                            <p
                              :style="{color: (user.noticePhone && user.noticePhone.length == 11) && user.haveAppAccount ? '' : '#FA403C'}">
                              {{ user.noticePhone }}</p>
                            <p style="color: #FA403C; margin-left: 20px;">
                              {{ user.noticePhone && user.noticePhone.length == 11 ? (user.haveAppAccount ? '' : '未开通APP账号') : '缺失号码' }}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="deptName">
                    <p class="deptName-name">{{ item.departName }}</p>
                    <p v-show="item.noticePersonList.length" class="deptName-num">{{ item.noticePersonList.length}}人</p>
                    <i v-show="item.noticePersonList.map(v => v.haveAppAccount).includes(false) || item.noticePersonList.map(v => v.noticePhone ? v.noticePhone.length == 11 : false).includes(false)"
                      class="el-icon-warning"></i>
                  </div>
                </el-tooltip>
              </div>
              <div v-if="activeType !== 'detail'" class="generationContent-operation">
                <span class="generationContent-button button-add" @click="controlSure('add')">
                  <i class="el-icon-plus"></i>
                  <span>添加</span>
                </span>
                <span v-if="index != 0" class="termContent-button button-detele" @click="controlSure('delete', index)">
                  <i class="el-icon-delete"></i>
                  <span>删除</span>
                </span>
              </div>
            </div>
          </div>
        </div>
        <el-form-item v-if="formInline.alarmModel" class="is-required" label="应急队伍">
          <el-button v-if="activeType !== 'detail'" type="primary" style="margin:0 8px"
            @click="addTeam">添加队伍</el-button>
          <el-tooltip popper-class="tps1" effect="dark" content="战时页面显示的应急队伍人员" placement="right-start">
            <span><i class="el-icon-question"></i></span>
          </el-tooltip>
        </el-form-item>
        <div v-if="formInline.alarmModel" class="teamOperation">
          <div class="emergencyTeamTable">
            <el-table :data="groupTableData" border stripe>
              <el-table-column prop="troopName" label="队伍名称" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="troopTypeName" label="队伍类型" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="troopCaptainName" label="队伍队长" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="troopPersonnelName" label="队伍人员" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="troopResponsibility" label="队伍职责" show-overflow-tooltip> </el-table-column>
              <el-table-column label="操作" v-if="activeType !== 'detail'">
                <template slot-scope="scope">
                  <el-button type="text" @click="del(scope.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div v-if="!isPoliceAlarm" class="configTitle">
          <img src="@/assets/images/parkingLot/warpper.png" alt="" />
          <span>警情升级</span>
        </div>
        <div v-if="!isPoliceAlarm" class="policeAlarm">
          <div> <el-form-item label="是否升级警情">
              <el-switch v-model="formInline.upgraded" active-color="#3562db" :disabled="activeType === 'detail'">
              </el-switch>
            </el-form-item></div>
          <div v-if="formInline.upgraded">
            <el-form-item>
              <span style="margin-right:5px">产生报警后</span> <el-input v-model.number="formInline.alarmWaitTime" min="1"
                placeholder="请输入" style="width:100px" type="number"
                oninput="if(isNaN(value)) { value = parseFloat(value) } if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+2)}"></el-input>
              <el-select v-model="formInline.alarmWaitTimeUnit" placeholder="请选择" style="width:100px"
                :disabled="activeType === 'detail'">
                <el-option label="分钟" value="0"></el-option>
                <el-option label="秒" value="1"></el-option>
              </el-select><span style="margin:0 10px">仍未处理，则升级为</span>
              <el-select v-model="formInline.alarmUpgradedLevel" placeholder="请选择" :disabled="activeType === 'detail'">
                <el-option v-for="item in policeInfoList" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select><span style="margin-left:5px"> 等级的报警</span>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>
    <!--选择人员 -->
    <template v-if="personDialogShow">
      <SelectPeopleDialog :personDialogShow="personDialogShow" @submitPersonDialog="submitPersonDialog"
        @closePersonDialog="closePersonDialog" />
    </template>
    <template>
      <sinoDialog ref="dialogGroup" title="选择应急队伍" @sureDialog="sureGroupDialog" @closeDialog="closeGroupDialog">
        <SelectGroup ref="groupChoice" :groupList="groupTableData"></SelectGroup>
      </sinoDialog>
    </template>
    <selectTemplate v-if="isSelectPrePlan" :visible.sync="isSelectPrePlan" @savePrePlan="savePrePlan" />
  </div>
</template>

<script>
import SelectPeopleDialog from '../../../operationPort/exerciseManage/exercisePlan/components/SelectPeople.vue'
import SelectGroup from '../components/SelectGroup.vue'
import sinoDialog from '../../../operationPort/spaceManage/common/sinoDialog.vue'
import selectTemplate from './selectPreplan.vue'
import { transData } from '@/util'
import { listToTree } from '@/util'
export default {
  name: 'contingencyChoose',
  components: {
    SelectPeopleDialog,
    SelectGroup,
    sinoDialog,
    selectTemplate
  },
  props: {
    detailsInfo: {
      type: Object,
      default: () => {
        return {}
      }
    },
    projectCode: {
      type: String,
      default: ''
    },
    isPoliceAlarm: {  // 重要无警情开启
      type: Boolean,
      default: false
    },
    policeAlarmType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      formInline: {
        alarmModel: true, // 是否弹出
        disposalErminal: ['0', '2'], // 处置终端
        alarmResponseWay: ['0'], // 提醒方式
        alarmSecureWay: ['0'], // 解除方式
        preplanId: '', // 启用预案
        preplanName: '', // 预案name
        autoSendOrder: '0',
        alarmResponseNum: '', // 播放次数
        workOrderCode: [],
        workOrderCodes: '',
        workOrderName: '',
        teamCode: '',
        teamName: '',
        emergencyTeamId: '',
        id: '',
        upgraded: false, // 是否升级警情
        alarmWaitTime: 0, // 报警等待时间
        alarmWaitTimeUnit: '0', // 报警升级时间单位
        alarmUpgradedLevel: '' // 报警升级级别，
      },
      addRules: {
        alarmModel: { required: true, message: '请选择是否弹出警单', trigger: 'change' },
        disposalErminal: { required: true, message: '请至少选择一个处置终端', trigger: 'change' },
        alarmResponseWay: { required: true, message: '请至少选择一个提醒方式', trigger: 'change' },
        alarmSecureWay: { required: true, message: '请至少选择一个警情解除能力', trigger: 'change' },
        workOrderCode: { required: true, message: '请选择工单事项', trigger: 'change' },
        teamCode: { required: true, message: '请选择班组', trigger: 'change' }
      },
      personDialogShow: false,
      isSelectPrePlan: false,
      groupTableData: [], // 应急队伍table
      policeInfoList: [],
      informOptions: [
        {
          label: '短信',
          value: '0'
        }, {
          label: '语音电话',
          value: '1'
        }, {
          label: '企业微信',
          value: '2'
        }, {
          label: '微信服务号',
          value: '3'
        }, {
          label: 'APP',
          value: '4'
        }
      ],
      informTypeOptions: [
        {
          label: '部门',
          value: '0'
        }, {
          label: '人员',
          value: '1'
        }
      ],
      generationContents: [
        {
          alarmConfigType: '0', // 报警处理方式，0-报警产生时，1-确警时
          noticeWay: '0', // 通知方式，0-短信，1-app，2-企业微信，3-微信服务号，4-语音电话
          noticeType: '1', // 0-部门，1-人员
          personId: '', // 人员编码
          personName: '', // 人员名称
          tel: '', // 联系电话
          departName: '', // 部门名称
          departCodes: [], // 部门code
          departCode: '', // 部门code（逗号分割提交）
          noticePersonList: [], // 人员列表
          selectDeptList: [] // 部门 人员归类（不提交）
        }
      ],
      generationSureContents: [
        {
          alarmConfigType: '1', // 报警处理方式，0-报警产生时，1-确警时
          noticeWay: '0', // 通知方式，0-短信，1-app，2-企业微信，3-微信服务号，4-语音电话
          noticeType: '1', // 0-部门，1-人员
          personId: '', // 人员编码
          personName: '', // 人员名称
          tel: '', // 联系电话
          departName: '', // 部门名称
          departCodes: [], // 部门code
          departCode: '', // 部门code（逗号分割提交）
          noticePersonList: [], // 人员列表
          selectDeptList: [] // 部门 人员归类（不提交）
        }
      ],
      deptList: [], // 部门列表
      operationIndex: 0, // 选中项下标
      teamsOptions: [],
      planList: [], // 预案list
      itemTreeData: [], // 服务事项
      autoSendOrderItemList: [], // 工单事项,
      orderPropsType: {
        children: 'children',
        label: 'name',
        value: 'id',
      },
      typeList: [],
      isSelectPers: false,
      alarmConfigType: '',
      activeType: '',
    }
  },
  watch: {
    projectCode() {
      this.getItemTreeData()
    },
    policeAlarmType() {
      this.getUpAlarmList()
    },
  },
  mounted() {
    this.activeType = this.$route.query.activeType
    this.getUpAlarmList()
    this.getUseDeptList()
    this.getItemTreeData()
  },
  methods: {
    getUpAlarmList() {
      if (this.policeAlarmType == '0') {
        this.policeInfoList = [
          {
            label: '重要警情',
            value: '3'
          }, {
            label: '紧急警情',
            value: '2'
          }, {
            label: '一般警情',
            value: '1'
          }
        ]
      } else if (this.policeAlarmType == '1') {
        this.policeInfoList = [
          {
            label: '重要警情',
            value: '3'
          }, {
            label: '紧急警情',
            value: '2'
          }
        ]
      } else if (this.policeAlarmType == '2') {
        this.policeInfoList = [
          {
            label: '重要警情',
            value: '3'
          }
        ]
      }
    },
    del(i) {
      this.groupTableData.splice(i, 1)
      let arr = []
      this.groupTableData.forEach(item => {
        arr.push(item.id)
      })
      this.formInline.emergencyTeamId = arr.join(',')
    },
    // 改变部门人员
    changeNoticeType(val, index) {
      if (val.noticeType === '0') {
        this.generationContents[index].personId = ''
        this.generationContents[index].personName = ''
        this.generationContents[index].tel = ''
      } else {
        this.generationContents[index].departName = ''
        this.generationContents[index].departCodes = []
        this.generationContents[index].departCode = ''
        this.generationContents[index].noticePersonList = []
        this.generationContents[index].selectDeptList = []
      }
    },
    // 改变部门人员
    changeNoticeType1(val, index) {
      if (val.noticeType === '0') {
        this.generationSureContents[index].personId = ''
        this.generationSureContents[index].personName = ''
        this.generationSureContents[index].tel = ''
      } else {
        this.generationSureContents[index].departName = ''
        this.generationSureContents[index].departCodes = []
        this.generationSureContents[index].departCode = ''
        this.generationSureContents[index].noticePersonList = []
        this.generationSureContents[index].selectDeptList = []
      }
    },
    // 获取服务事项
    getItemTreeData() {
      const params = {
        workTypeCode: '16',
        free1: this.projectCode
      }
      this.$api.getItemTreeData(params).then((res) => {
        this.itemTreeData = listToTree(res, 'id', 'parent')
      })
    },
    // 获取班组
    getTeamsByWorkTypeCode(selectRowId) {
      const params = {
        localtionId: '',
        workTypeCode: '16',
        matterId: selectRowId
      }
      this.$api.getDataByTypeTeam(params).then((res) => {
        if (res.code === '200') {
          this.teamsOptions = res.data.list
        }
      })
    },
    // 打开预案
    handleClick() {
      this.isSelectPrePlan = true
    },
    // 服务事项
    handleOrderChange(e) {
      if (e && e.length) {
        this.getTeamsByWorkTypeCode(e[e.length - 1])
        this.formInline.workOrderCodes = e.join(',')
        let list = this.$refs.cascader.getCheckedNodes()
        if (list[0].pathLabels && list[0].pathLabels.length) {
          let workOrderName = list[0].pathLabels
          this.formInline.workOrderName = workOrderName.join(',')
          console.log(this.formInline.workOrderCodes, this.formInline.workOrderName, 'dasdasdasdas')
        }
      }
    },
    // 获取科室
    getUseDeptList() {
      this.$api.getSelectedDept().then((res) => {
        if (res.code == '200') {
          this.deptList = transData(res.data, 'id', 'parentId', 'children')
        }
      })
    },
    // 班组change
    teamCodeChange(e) {
      if (e) {
        this.formInline.teamName = this.teamsOptions.find((ele) => ele.id == e).team_name
      }
    },
    savePrePlan(obj) {
      this.formInline.preplanId = obj.planId
      this.formInline.preplanName = obj.planName
    },
    // 确警删除
    deteleSureRow(arr, index) {
      if (index == 0) return
      arr.splice(index, 1)
    },
    // 选择人员
    control(type, index = 0, alarmConfigType) {
      this.alarmConfigType = alarmConfigType
      this.operationIndex = index
      if (type == 'selectPers') { // 选择人员
        this.personDialogShow = true
      } else if (type == 'add') {
        this.generationContents.push({
          alarmConfigType: '0', // 报警处理方式，0-报警产生时，1-确警时
          noticeWay: '0', // 通知方式，0-短信，1-app，2-企业微信，3-微信服务号，4-语音电话
          noticeType: '1', // 0-部门，1-人员
          personId: '', // 人员编码
          personName: '', // 人员名称
          tel: '', // 联系电话
          departName: '', // 部门名称
          departCodes: [], // 部门code
          departCode: '', // 部门code（逗号分割提交）
          noticePersonList: [], // 人员列表
          selectDeptList: [] // 部门 人员归类（不提交）
        })
      } else if (type == 'delete') {
        this.generationContents.splice(index, 1)
      }
    },
    // 确警选择人员
    controlSure(type, index = 0, alarmConfigType) {
      this.operationIndex = index
      this.alarmConfigType = alarmConfigType
      if (type == 'selectPers') { // 选择人员
        this.personDialogShow = true
      } else if (type == 'add') {
        this.generationSureContents.push({
          alarmConfigType: '0', // 报警处理方式，0-报警产生时，1-确警时
          noticeWay: '0', // 通知方式，0-短信，1-app，2-企业微信，3-微信服务号，4-语音电话
          noticeType: '1', // 0-部门，1-人员
          personId: '', // 人员编码
          personName: '', // 人员名称
          tel: '', // 联系电话
          departName: '', // 部门名称
          departCodes: [], // 部门code
          departCode: '', // 部门code（逗号分割提交）
          noticePersonList: [], // 人员列表
          selectDeptList: [] // 部门 人员归类（不提交）
        })
      } else if (type == 'delete') {
        this.generationSureContents.splice(index, 1)
      }
    },
    // 选择部门事件
    selectDept(val, index) {
      if (!val.length) {
        this.generationContents[index].noticePersonList = []
        this.generationContents[index].departName = ''
        this.generationContents[index].selectDeptList = []
        return
      }
      let selectDeptList = []
      this.deptList.forEach(item => {
        if (val.includes(item.id)) {
          selectDeptList.push(item)
        }
      })
      this.generationContents[index].departCode = selectDeptList.map(v => v.id).join(',')
      this.generationContents[index].departName = selectDeptList.map(v => v.deptName).join(',')
      // 根据科室id获取人员列表
      this.$api.GetNoticePersonInfoList({ departCode: val.join(',') }).then(res => {
        if (res.code == 200) {
          // 人员归类到部门下
          selectDeptList.forEach((item, index) => {
            item.userList = []
            res.data.map(v => v.departCode.split(',')).forEach((v, i) => {
              if (v.includes(item.id)) {
                item.userList.push(res.data[i])
              }
            })
          })
          this.generationContents[index].noticePersonList = res.data
          this.generationContents[index].selectDeptList = selectDeptList
        }
      })

    },
    // 选择部门事件
    selecSuretDept(val, index) {
      if (!val.length) {
        this.generationSureContents[index].noticePersonList = []
        this.generationSureContents[index].departName = ''
        this.generationSureContents[index].selectDeptList = []
        return
      }
      let selectDeptList = []
      this.deptList.forEach(item => {
        if (val.includes(item.id)) {
          selectDeptList.push(item)
        }
      })
      this.generationSureContents[index].departCode = selectDeptList.map(v => v.id).join(',')
      this.generationSureContents[index].departName = selectDeptList.map(v => v.deptName).join(',')
      // 根据科室id获取人员列表
      this.$api.GetNoticePersonInfoList({ departCode: val.join(',') }).then(res => {
        if (res.code == 200) {
          // 人员归类到部门下
          selectDeptList.forEach((item, index) => {
            item.userList = []
            res.data.map(v => v.departCode.split(',')).forEach((v, i) => {
              if (v.includes(item.id)) {
                item.userList.push(res.data[i])
              }
            })
          })
          this.generationSureContents[index].noticePersonList = res.data
          this.generationSureContents[index].selectDeptList = selectDeptList
        }
      })
    },
    // 警单change
    alarmModelChange(e) {
      if (!e) {
        this.formInline.preplanId = ''
        this.formInline.preplanName = ''
        this.formInline.alarmResponseNum = ''
      }
    },
    // 自动派发工单
    autoSendOrderInput(e) {
      if (e === '0') {
        this.formInline.workOrderCode = []
        this.formInline.workOrderCodes = ''
        this.formInline.workOrderName = ''
        this.formInline.teamCode = ''
        this.formInline.teamName = ''
      }
    },
    // 添加队伍
    addTeam() {
      this.$refs.dialogGroup.dialogTableVisible = true
    },
    sureGroupDialog() {
      let arr = []
      let arr1 = []
      arr = this.$refs.groupChoice.selectList
      this.groupTableData = this.removeSame(this.groupTableData.concat(arr))
      this.$refs.dialogGroup.dialogTableVisible = false
      this.groupTableData.forEach(item => {
        arr1.push(item.id)
      })
      this.formInline.emergencyTeamId = arr1.join(',')
    },
    removeSame(array) {
      let newArray = []
      for (let item of array) {
        // 使用JSON.stringfy()方法将数组和数组元素转换为JSON字符串之后再使用includes()方法进行判断
        if (JSON.stringify(newArray).includes(JSON.stringify(item))) {
          continue
        } else {
          // 不存在的添加到数组中
          newArray.push(item)
        }
      }
      return newArray
    },
    closeGroupDialog() {
      this.$refs.dialogGroup.dialogTableVisible = false
    },
    // 人员弹窗
    submitPersonDialog(list) {
      if (list.length !== 1) {
        this.$message.error('人员只能选择一个')
        return
      }
      if (this.alarmConfigType === 'produce') {
        this.generationContents[this.operationIndex].tel = list[0].mobile
        this.generationContents[this.operationIndex].personId = list[0].id
        this.generationContents[this.operationIndex].personName = list[0].staffName
      } else {
        this.generationSureContents[this.operationIndex].tel = list[0].mobile
        this.generationSureContents[this.operationIndex].personId = list[0].id
        this.generationSureContents[this.operationIndex].personName = list[0].staffName
      }
      this.personDialogShow = false

    },
    closePersonDialog() {
      this.personDialogShow = false
    },
  }
}
</script>
<style lang="scss" scoped>
.input-with-select {
  .el-select .el-input {
    width: 130px !important;
  }
}
.el-form-item__content .el-input-group {
  vertical-align: middle;
}
.configContent {
  .generation-content {
    margin: 14px 00 14px 26px;
    display: flex;

    .generationContent-title {
      width: 70px;
      margin-right: 10px;
      padding-top: 4px;
    }
    .generationContent-info {
      flex: 1;
      .content-block {
        display: flex;
        margin: 0 0 16px 0;
        .generationContent-input {
          color: #666666;
          display: flex;
          margin-right: 23px;
          .label {
            height: 32px;
            line-height: 32px;
          }
        }
        .generationContent-operation {
          height: 32px;
          line-height: 32px;
          cursor: pointer;
          margin-left: 8px;
          .button-add {
            color: #3562db;
          }
          .button-detele {
            color: #ff6461;
            margin-left: 16px;
          }
        }
      }
    }
  }
  .emergencyTeamTable {
    margin-bottom: 20px;
    width: 90%;
  }
}
.policeAlarm {
  padding: 10px 30px;
}
.ml-16 {
  margin-left: 16px;
}
::v-deep(.noticeType) {
  flex: 0.35;
  .el-input__inner {
    color: #3562db;
  }
}
.configTitle {
  display: inline-block;
  background: #e6effc;
  margin: 10px 0;
  padding: 3px 16px 3px 10px;
  border-radius: 0px 99px 99px 0px;
  span {
    margin-left: 5px;
    img {
      vertical-align: text-bottom;
    }
  }
}
.dialog-content {
  .el-input {
    width: 200px;
  }
  .el-pagination {
    margin-top: 10px;
  }
}
</style>
<style lang="scss" >
/* 设置背景色 */
.tps.el-tooltip__popper,
.tps1.el-tooltip__popper {
  background: rgba(0, 0, 0, 0.5);
}
.tps .popper__arrow {
  border-top-color: #000000 !important;
  opacity: 0.5;
}
.tps .popper__arrow:after {
  border-top-color: #000000 !important;
  opacity: 0.5;
}
.tps1 .popper__arrow {
  border-right-color: #000000 !important;
  opacity: 0.5;
}
.tps1 .popper__arrow:after {
  border-right-color: #000000 !important;
  opacity: 0.5;
}
.el-form-item {
  margin-bottom: 14px;
}
.teamOperation {
  margin-left: 28px;
}
.down-popover {
  .groupTips {
    font-weight: 400;
    font-size: 14px;
    color: #666666;

    .title {
      width: 30%;
      text-align: left;
    }
    .extra {
      height: 30px;
      margin-top: 5px;
    }
    .group {
      width: 40%;
      text-align: left !important;
      .groupType {
        display: inline-block;
        padding: 1px 4px;
        border-radius: 2px;
        border: 1px solid #dcdfe6;
        color: #cacaca;
        margin-left: 8px;
        font-size: 10px;
      }
      .groupLable {
        display: inline-block;
        height: 16px;
        width: 100px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .phone {
      width: 30%;
      text-align: left;
    }
    .bubble {
      display: inline-block;
      width: 31px;
      height: 16px;
      background: #e4e7ed;
      border-radius: 99px 99px 99px 99px;
      font-weight: 400;
      font-size: 12px;
      color: #666666;
      line-height: 14px;
      text-align: center;
      margin: 0px 10px;
    }
  }
}
.deptName {
  display: flex;
  align-items: center;
  height: 32px;
  line-height: 32px;
  cursor: pointer;
  .deptName-name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .deptName-num {
    height: 16px;
    line-height: 16px;
    padding: 0px 6px;
    background: #e4e7ed;
    border-radius: 99px;
    font-weight: 400;
    font-size: 12px;
    color: #666666;
    margin: 0px 8px;
  }
  i {
    color: #fa403c;
  }
}
p {
  margin: 0;
}
.tps3 .tooltip-content {
  width: 400px !important;
}
.tps3 .tooltip-content .content-head {
  display: flex;
  padding: 0px 8px 8px 8px;
  div {
    width: 50%;
  }
  .head-mobie {
    margin-top: 8px;
    margin-left: 16px;
  }
}
.tps3 .tooltip-content .content-list {
  max-height: 230px;
  overflow: auto;
  padding: 0px 8px 8px 8px;
  .item-name {
    margin-top: 8px;
  }
  .list-item {
    .item-user {
      margin-left: 16px;
      margin-top: 8px;
      display: flex;
      div {
        width: 50%;
      }
    }
  }
}
</style>
