<template>
  <el-dialog :title="(checkedData.state === 'edit' ? '修改' : '添加') + '分组'" :visible="dialogVisible" :before-close="closeDialog">
    <div class="sino-content">
      <el-form ref="filters" :rules="rules" label-position="right" label-width="110px" :model="filters">
        <el-form-item label="分组名称：" prop="name">
          <el-input v-model="filters.name" maxlength="20" placeholder="请输入分组名称"></el-input>
        </el-form-item>
        <el-form-item v-if="hasScada && hasScadaOther" label="图纸类型：" prop="isdId">
          <el-select v-model="filters.isdId" placeholder="请选择图纸类型" clearable @change="changeIsiList(filters.isdId)">
            <el-option v-for="item in drawingTypeArr" :key="item.isdId" :value="item.isdId" :label="item.isdName"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="hasScada && hasScadaOther" label="图纸：" prop="isiId">
          <el-select v-model="filters.isiId" placeholder="请选择图纸" clearable>
            <el-option v-for="item in drawingArr" :key="item.isiId" :value="item.isiId" :label="item.isiName"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="hasComputerRoom && treeLevel == 1" label="机组模型id：" prop="modelCode">
          <el-input v-model="filters.modelCode" maxlength="20" placeholder="请输入机组模型id"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog(false)">取 消</el-button>
      <el-button type="primary" :loading="submitLoading" @click="editSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { monitorTypeList } from '@/util/dict.js'
export default {
  name: 'addTreeNode',
  props: {
    hasScadaOther: {
      type: Boolean,
      default: true
    },
    dialogVisible: {
      type: Boolean,
      default: false
    },
    checkedData: {
      type: Object,
      default: () => {}
    },
    treeLevel: {
      type: Number,
      default: 1
    }, // 1同级 2 下级  //第一次添加
    monitorType: {
      type: String,
      default: ''
    }, // 项目code
    requestHttp: {
      type: String,
      default: __PATH.VUE_IEMC_API
    },
    hasComputerRoom: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      rules: {
        name: [{ required: true, message: '请输入分组名称', trigger: 'blur' }]
      },
      filters: {
        name: '',
        isdId: '',
        isiId: '',
        modelCode: '' // 机组模型id
      },
      drawingTypeArr: [], // SCADA图纸分类列表
      drawingArr: [], // SCADA图纸列表
      submitLoading: false,
      hasScada: monitorTypeList.find((item) => item.projectCode == this.monitorType)?.hasScada ?? false
    }
  },
  watch: {
    'filters.name': {
      immediate: true,
      handler: function (newVal, oldVal) {
        this.$forceUpdate()
      }
    }
  },
  mounted() {
    this.getFolderSelect()
    if (this.checkedData.state === 'edit') {
      this.echoData()
    }
  },
  methods: {
    /**
     * 获取SCADA图纸类型（下拉列表）
     */
    getFolderSelect() {
      this.$api.getFolderSelect().then((res) => {
        if (res.code == 200) {
          this.drawingTypeArr = res.data
        }
      })
    },
    /**
     * 获取SCADA图纸列表（级联图纸类型）
     * params id {图纸类型ID}
     */
    getImageListById(id) {
      this.$api.getImageListById({ id: id }).then((res) => {
        if (res.code == 200) {
          this.drawingArr = res.data
        }
      })
    },
    // 图纸类型改变
    changeIsiList(id) {
      this.filters.isiId = ''
      this.$forceUpdate()
      this.getImageListById(id)
    },
    /**
     * 修改数据回显
     */
    echoData() {
      this.filters = {
        name: this.checkedData.name,
        isdId: this.checkedData.isdId ? Number(this.checkedData.isdId) : '', // Number
        isiId: this.checkedData.isiId ? Number(this.checkedData.isiId) : '',
        modelCode: this.checkedData.modelCode
      }
      this.hasScada && this.filters.isdId != '' && this.getImageListById(this.filters.isdId)
    },
    /**
     * 提交
     */
    editSubmit() {
      this.$refs.filters.validate((valid) => {
        if (valid) {
          if (this.checkedData.state === 'edit') {
            this.$api
              .updateEntityMenu(
                {
                  id: this.checkedData.id,
                  entityMenuName: this.filters.name,
                  isdId: this.filters.isdId, // SCADA图纸类型ID
                  isiId: this.filters.isiId, // SCADA图纸ID
                  modelCode: this.filters.modelCode // 机组模型id
                },
                {
                  'operation-type': 2,
                  'operation-id': this.checkedData.id,
                  'operation-name': this.filters.name
                },
                this.requestHttp
              )
              .then((res) => {
                this.submitLoading = false
                if (res.code == 200) {
                  this.$message.success(res.message)
                  this.closeDialog()
                } else {
                  this.$message.error(res.message)
                }
              })
          } else {
            // 新增
            let data = {
              projectCode: this.monitorType, // 项目code
              parentId: this.checkedData.code, // 上级code
              parentIds: this.checkedData.parentIds + ',' + this.checkedData.code, // 所有的上级code
              name: this.filters.name, // 菜单名称
              isdId: this.filters.isdId, // SCADA图纸类型ID
              isiId: this.filters.isiId, // SCADA图纸ID
              modelCode: this.filters.modelCode // 机组模型id
            }
            if (this.treeLevel == 1) {
              data.parentId = '#'
              data.parentIds = '#'
            }
            this.$api.setEntityMenu(data, {'operation-type': 1}, this.requestHttp).then((res) => {
              this.submitLoading = false
              if (res.code == 200) {
                this.$message.success(res.message)
                this.closeDialog()
              } else {
                this.$message.error(res.message)
              }
            })
          }
        }
      })
    },
    /**
     * 关闭弹窗
     */
    closeDialog(refresh = true) {
      this.$emit('closeDialog', refresh)
      this.filters = {
        name: '',
        isdId: '',
        isiId: ''
      }
      this.$refs.filters.resetFields()
      this.$forceUpdate()
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog {
  width: 500px;

  .el-select {
    width: 100%;
  }
}
</style>
