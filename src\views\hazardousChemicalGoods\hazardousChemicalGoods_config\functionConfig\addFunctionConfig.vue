<template>
  <PageContainer :footer="true" :v-loading="pageLoading">
    <div slot="content" class="inner" style="height: 100%">
      <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
        <span class="green_line"></span>
        基本信息
      </div>
      <el-form v-if="functionType == 1" ref="form1" :model="form1" :rules="rules1" label-width="90px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="分类编码" prop="materialTypeCode">
              <el-input v-if="type != 'view'" v-model="form1.materialTypeCode" placeholder="请输入分类编码"></el-input>
              <span v-else>{{ form1.materialTypeCode }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="分类名称" prop="materialTypeName">
              <el-input v-if="type != 'view'" v-model="form1.materialTypeName" placeholder="请输入分类名称"></el-input>
              <span v-else>{{ form1.materialTypeName }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="上级分类" prop="materialParentId">
              <el-cascader
                v-if="type != 'view'"
                v-model="form1.materialParentId"
                :options="paramsType"
                :props="props"
                placeholder="请选择上级分类（不选默认是根节点）"
                clearable
                :show-all-levels="false"
                style="width: 100%"
              ></el-cascader>
              <span v-else>{{ $route.query.rowData.parentName || '' }}</span>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="8">
            <el-form-item label="排序">
              <el-input v-if="type != 'view'" v-model="form1.dictionaryDetailsSort" placeholder="请输入排序"></el-input>
              <span v-else>{{ form1.dictionaryDetailsSort }}</span>
            </el-form-item>
          </el-col> -->
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="说明">
              <el-input v-if="type != 'view'" v-model="form1.dictionaryDetailsRemake" type="textarea" :rows="2" placeholder="请输入内容"></el-input>
              <span v-else>{{ form1.dictionaryDetailsRemake }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状态" required>
              <el-radio v-if="type != 'view'" v-model="form1.dictionaryDetailsStatus" label="1">启用</el-radio>
              <el-radio v-if="type != 'view'" v-model="form1.dictionaryDetailsStatus" label="0">禁用</el-radio>
              <span v-else>{{ form1.dictionaryDetailsStatus == '1' ? '启用' : '禁用' }}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-form v-if="functionType == 2" ref="form2" :model="form2" :rules="rules2" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="危化品名称" prop="materialName">
              <el-input v-if="type != 'view'" v-model="form2.materialName" placeholder="请输入危化品名称" style="width: 300px; height: 40px; margin-right: 122px"></el-input>
              <span v-else>{{ form2.materialName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="规格型号" prop="model">
              <el-input v-if="type != 'view'" v-model="form2.model" placeholder="请输入规格型号" style="width: 300px; height: 40px"></el-input>
              <span v-else>{{ form2.model }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="基本单位" prop="basicUnitCode">
              <el-select v-if="type != 'view'" v-model="form2.basicUnitCode" placeholder="请选择基本单位" style="width: 300px; height: 40px; margin-right: 122px">
                <el-option v-for="(item, index) in basicUnitArr" :key="index" :label="item.name" :value="item.code"></el-option>
              </el-select>
              <span v-else>{{ form2.basicUnitName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属分类" prop="materialTypeCode">
              <el-cascader
                v-if="type != 'view'"
                v-model="form2.materialTypeCode"
                :options="classifyArr"
                :show-all-levels="false"
                :props="{
                  value: 'code',
                  label: 'name',
                  children: 'children',
                  checkStrictly: true,
                  leaf: false
                }"
                clearable
                style="width: 100%"
                placeholder="请选择所属分类"
              >
              </el-cascader>
              <span v-else>{{ form2.materialTypeName }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="生产厂家">
              <el-select v-if="type != 'view'" v-model="form2.manufacturer" placeholder="请选择生产厂家" style="width: 300px; height: 40px; margin-right: 122px">
                <el-option v-for="(item, index) in manufacturersArr" :key="index" :label="item.unitsName" :value="item.id"></el-option>
              </el-select>
              <span v-else>{{ form2.manufacturerName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="商品编码">
              <el-input v-if="type != 'view'" v-model.number="form2.goodsCode" :maxlength="13" placeholder="请输入商品编码" style="width: 300px; height: 40px; margin-right: 122px">
              </el-input>
              <span v-else>{{ form2.goodsCode }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="供应商">
              <el-select v-if="type != 'view'" v-model="form2.supplierId" placeholder="请选择供应商" style="width: 300px; height: 40px; margin-right: 122px">
                <el-option v-for="(item, index) in supplierArr" :key="index" :label="item.unitsName" :value="item.id"></el-option>
              </el-select>
              <span v-else>{{ form2.supplierName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="生产许可">
              <el-input v-if="type != 'view'" v-model="form2.manufacturingLicense" placeholder="请输入生产许可" style="width: 300px; height: 40px"></el-input>
              <span v-else>{{ form2.manufacturingLicense }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="最低库存">
              <el-input
                v-if="type != 'view'"
                v-model="form2.minStock"
                placeholder="手动输入"
                style="width: 300px; height: 40px; margin-right: 122px"
                @keyup.native="proving"
              ></el-input>
              <span v-else>{{ form2.minStock }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最高库存">
              <el-input v-if="type != 'view'" v-model="form2.maxStock" placeholder="手动输入" style="width: 300px; height: 40px"></el-input>
              <span v-else>{{ form2.maxStock }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状态" required>
              <el-radio v-if="type != 'view'" v-model="form2.status" label="0">启用</el-radio>
              <el-radio v-if="type != 'view'" v-model="form2.status" label="1">禁用</el-radio>
              <span v-else>{{ form2.status == '0' ? '启用' : '禁用' }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="配件图片">
              <el-upload
                v-if="type != 'view'"
                ref="uploadFile"
                drag
                multiple
                class="mterial_file"
                action="string"
                list-type="picture-card"
                :file-list="fileEcho"
                :http-request="httpRequest"
                accept=".jpg, .jpeg, .png, .JPG, .JPEG, .bmp"
                :limit="1"
                :on-exceed="handleExceed"
                :on-preview="handlePictureCardPreview"
                :on-remove="removeImg"
                :on-change="changeFileEcho"
              >
                <i class="el-icon-upload"></i>
                <div class="el-upload__text" style="top: 33px">将文件拖到此处，或<em>点击上传</em></div>
              </el-upload>
              <template v-else>
                <el-image v-if="form2.materialPhoto" :src="form2.materialPhoto" :preview-src-list="[form2.materialPhoto]" style="width: 100px; height: 100px"> </el-image>
              </template>
              <el-dialog :visible.sync="dialogVisible" top="5vh" style="z-index: 99999">
                <img width="100%" :src="dialogImageUrl" alt />
              </el-dialog>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="说明">
              <el-input v-if="type != 'view'" v-model="form2.remarks" type="textarea" :rows="2" placeholder="请输入内容"></el-input>
              <span v-else>{{ form2.remarks }}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-form v-if="functionType == 3" ref="form3" :model="form3" :rules="rules3" label-width="90px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="编码" prop="typeCode">
              <el-input v-if="type != 'view'" v-model="form3.typeCode" placeholder="请输入编码"></el-input>
              <span v-else>{{ form3.typeCode }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="名称" prop="name">
              <el-input v-if="type != 'view'" v-model="form3.name" placeholder="请输入名称"></el-input>
              <span v-else>{{ form3.name }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="参数类型" prop="parentId">
              <el-select v-if="type != 'view'" v-model="form3.parentId" placeholder="请选择参数类型" style="width: 100%">
                <el-option v-for="(item, index) in paramsType" :key="index" :label="item.name" :value="item.id"></el-option>
              </el-select>
              <span v-else>{{ paramsType.find((i) => i.id == $route.query.rowData.parentId) ? paramsType.find((i) => i.id == $route.query.rowData.parentId).name : '' }}</span>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="8">
            <el-form-item label="排序">
              <el-input v-if="type != 'view'" v-model="form3.sort" placeholder="请输入排序"></el-input>
              <span v-else>{{ form3.sort }}</span>
            </el-form-item>
          </el-col> -->
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="说明">
              <el-input v-if="type != 'view'" v-model="form3.explain" type="textarea" :rows="2" placeholder="请输入内容"></el-input>
              <span v-else>{{ form3.explain }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状态" required>
              <el-radio v-if="type != 'view'" v-model="form3.status" label="0">启用</el-radio>
              <el-radio v-if="type != 'view'" v-model="form3.status" label="1">禁用</el-radio>
              <span v-else>{{ form3.status == '0' ? '启用' : '禁用' }}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-form v-if="functionType == 4" ref="form4" :model="form4" :rules="rules4" label-width="90px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="编码" prop="typeCode">
              <el-input v-if="type != 'view'" v-model="form4.typeCode" placeholder="请输入编码"></el-input>
              <span v-else>{{ form4.typeCode }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="名称" prop="name">
              <el-input v-if="type != 'view'" v-model="form4.name" placeholder="请输入名称"></el-input>
              <span v-else>{{ form4.name }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="说明">
              <el-input v-if="type != 'view'" v-model="form4.explain" type="textarea" :rows="2" placeholder="请输入内容"></el-input>
              <span v-else>{{ form4.explain }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状态" required>
              <el-radio v-if="type != 'view'" v-model="form4.status" label="0">启用</el-radio>
              <el-radio v-if="type != 'view'" v-model="form4.status" label="1">禁用</el-radio>
              <span v-else>{{ form4.status == '0' ? '启用' : '禁用' }}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-form v-if="functionType == 5" ref="form5" :model="form5" :rules="rules5" label-width="90px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="终端" prop="terminalId">
              <el-select
                v-if="type != 'view'"
                v-model="form5.terminalId"
                placeholder="请选择终端"
                :disabled="type == 'edit'"
                style="width: 300px; height: 40px; margin-right: 122px"
              >
                <el-option v-for="(item, index) in terminaArr" :key="index" :disabled="item.disabled" :label="item.endpoint" :value="item.id"></el-option>
              </el-select>
              <span v-else>{{ form5.terminal }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="说明">
              <el-input v-if="type != 'view'" v-model="form5.explain" type="textarea" :rows="2" placeholder="请输入内容"></el-input>
              <span v-else>{{ form5.explain }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状态" required>
              <el-radio v-if="type != 'view'" v-model="form5.status" label="1">启用</el-radio>
              <el-radio v-if="type != 'view'" v-model="form5.status" label="0">禁用</el-radio>
              <span v-else>{{ form5.status == '1' ? '启用' : '禁用' }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="terminaWrap">
          <el-col v-for="(item, index) in warehouseList" :key="index" :span="24">
            <el-form-item label="监测仓库">
              <template v-if="type != 'view'">
                <el-select v-model="item.warehouseId" placeholder="请选择基本单位" style="width: 250px; height: 40px">
                  <el-option v-for="(ite, ind) in warehouseOptions" :key="ind" :label="ite.warehouseName" :value="ite.id"></el-option>
                </el-select>
                <el-button type="primary" style="margin: 0 10px" @click="setEquipment(index)">设置监测设备</el-button>
                <el-select v-model="item.activeEquipment" multiple clearable popper-class="selectPopover" class="equipmentGroup" placeholder="请设置监测设备">
                  <el-option v-for="(ite, ind) in equipmentList[index]" :key="ind" :label="ite.assetsName" :value="ite.id"> </el-option>
                </el-select>
                <i class="el-icon-plus" style="font-size: 22px; margin-left: 10px" @click="addWarehouse"></i>
                <i v-if="index > 0" class="el-icon-delete" style="font-size: 22px; margin-left: 10px; color: #fa403c" @click="deleteWarehouse(index)"></i>
              </template>
              <template v-else>
                {{ item.storageLocation }}
                <span style="margin-left: 20px">监测设备：</span>
                {{ item.monitoringDevice }}
              </template>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-dialog title="选择监测设备" :visible.sync="selectDialog" custom-class="monitoringDialog" width="50%" :close-on-click-modal="false" :before-close="handleClose">
        <select-monitoring-facility v-if="selectDialog" ref="dialogContent" @selectedData="completeData"></select-monitoring-facility>
        <span slot="footer" class="dialog-footer">
          <el-button @click="selectDialog = false">取 消</el-button>
          <el-button type="primary" @click="completeSelect">确 定</el-button>
        </span>
      </el-dialog>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">取消</el-button>
      <el-button v-if="type == 'add' || type == 'edit'" type="primary" @click="submit">保存</el-button>
    </div>
  </PageContainer>
</template>
<script>
import { transData } from '@/util'
import selectMonitoringFacility from './selectMonitoringFacility.vue'
export default {
  components: {
    selectMonitoringFacility
  },
  data() {
    return {
      pageLoading: false,
      form1: {
        materialTypeCode: '',
        materialTypeName: '',
        materialParentId: '',
        dictionaryDetailsRemake: '',
        dictionaryDetailsStatus: '1'
      },
      form2: {
        status: '0',
        goodsCode: ''
      },
      form3: {
        typeCode: '',
        name: '',
        parentId: '',
        explain: '',
        status: '0'
      },
      form4: {
        typeCode: '',
        name: '',
        explain: '',
        status: '0'
      },
      form5: {
        terminalId: '',
        terminal: '',
        explain: '',
        status: '1'
      },
      terminaArr: [],
      // 危化品仓库列表
      warehouseOptions: [],
      // 监测仓库列表
      warehouseList: [
        {
          activeEquipment: '',
          warehouseId: ''
        }
      ],
      typeData: [
        {
          id: '3',
          name: '供应商'
        },
        {
          id: '5',
          name: '生产厂家'
        }
      ],
      rules1: {
        materialTypeCode: [{ required: true, message: '请输入分类编码', trigger: 'blur' }],
        materialTypeName: [{ required: true, message: '请输入分类名称', trigger: 'blur' }]
      },
      rules2: {
        materialName: [{ required: true, message: '请输入危化品名称', trigger: 'blur' }],
        model: [{ required: true, message: '请输入规格型号', trigger: 'blur' }],
        basicUnitCode: [{ required: true, message: '请选择基本单位', trigger: 'change' }],
        materialTypeCode: [{ required: true, message: '请选择所属分类', trigger: 'change' }]
      },
      rules3: {
        typeCode: [{ required: true, message: '请输入编码', trigger: 'blur' }],
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }]
      },
      rules4: {
        typeCode: [{ required: true, message: '请输入编码', trigger: 'blur' }],
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }]
      },
      rules5: {
        terminalId: [{ required: true, message: '请选择终端类型', trigger: 'blur' }]
      },
      paramsType: [],
      props: {
        label: 'name',
        value: 'id',
        children: 'children',
        checkStrictly: true
      },
      type: 'add',
      functionType: 1, // 字典类型
      basicUnitArr: [],
      classifyArr: [],
      supplierArr: [],
      manufacturersArr: [],
      fileEcho: [],
      materialPhoto: '', // 配件图片
      dialogVisible: false,
      dialogImageUrl: false,
      allClassify: [],
      hazardousArray: [],
      selectDialog: false,
      activeIndex: 0, // 当前选择的监测仓库下标
      equipmentList: [], // 监测设备列表
      parentObj: [] // 监测设备上级分类
    }
  },
  created() {
    this.type = this.$route.query.type || 'add'
    this.functionType = this.$route.query.parentId
    // 危化品分类
    if (this.functionType == 1) {
      this.getParentTypeData()
      if (this.type != 'add') {
        const rowData = this.$route.query.rowData
        this.form1.materialTypeCode = rowData.dictionaryDetailsCode
        this.form1.materialTypeName = rowData.dictionaryDetailsName
        this.form1.dictionaryDetailsRemake = rowData.dictionaryDetailsRemake
        this.form1.dictionaryDetailsStatus = rowData.dictionaryDetailsStatus || '0'
      }
    } else if (this.functionType == 2) {
      this.getBaseUnitList('JLDW')
      this.getBaseUnitList('WZFL')
      this.gethscUnitManger('GYS')
      this.gethscUnitManger('sccj')
      if (this.type == 'view' || this.type == 'edit') {
        this.getHscDetail()
      }
    } else if (this.functionType == 3) {
      this.getParamsTypeData()
      if (this.type != 'add') {
        const rowData = this.$route.query.rowData
        this.form3.explain = rowData.explain
        this.form3.name = rowData.name
        this.form3.typeCode = rowData.typeCode
        this.form3.status = rowData.status == '启用' ? '0' : '1'
        this.form3.parentId = rowData.parentId
      }
    } else if (this.functionType == 4) {
      if (this.type != 'add') {
        const rowData = this.$route.query.rowData
        this.form4.explain = rowData.explain
        this.form4.name = rowData.name
        this.form4.typeCode = rowData.typeCode
        this.form4.status = rowData.status == '启用' ? '0' : '1'
        this.form4.parentId = rowData.parentId
      }
    } else if (this.functionType == 5) {
      this.getWarehouseList()
      this.getHcsTerminalData()
      if (this.type != 'add') {
        this.getMonitoringDetail()
      }
    }
  },
  methods: {
    submit() {
      this.$refs['form' + this.functionType].validate((valid) => {
        if (valid) {
          let url = ''
          let params = {
            ...this['form' + this.functionType],
            userName: this.$store.state.user.userInfo.user.staffName,
            userId: this.$store.state.user.userInfo.user.staffId
          }
          if (this.functionType == 1) {
            url = 'addHscType'
            if (params.materialParentId.length) {
              params.materialParentId = params.materialParentId[params.materialParentId.length - 1]
            } else {
              params.materialParentId = this.hazardousArray.find((i) => i.code == 'All_HAZARDOUS_CHEMICALS').id
            }
          } else if (this.functionType == 2) {
            url = 'addHscDictionaries'
            if (this.type == 'add') {
              params.materialTypeCode = params.materialTypeCode[params.materialTypeCode.length - 1]
            }
            // 商品编码必须为13位纯数字
            if (this.form2.goodsCode) {
              if (String(this.form2.goodsCode).length != 13) {
                return this.$message.error('商品编码必须为13位数字')
              }
            }
            params.basicUnitName = params.basicUnitCode ? this.basicUnitArr.find((i) => i.code == params.basicUnitCode).name : ''
            params.materialTypeName = params.materialTypeCode ? this.allClassify.find((i) => i.code == params.materialTypeCode).name : ''
            params.supplierName = params.supplierId ? this.supplierArr.find((i) => i.id == params.supplierId).unitsName : ''
            params.manufacturerName = params.manufacturer ? this.manufacturersArr.find((i) => i.id == params.manufacturer).unitsName : ''
            params.materialPhoto = this.materialPhoto
          } else if (this.functionType == 3) {
            url = 'addTypeSetting'
          } else if (this.functionType == 4) {
            url = 'addHscRiskLeve'
          } else if (this.functionType == 5) {
            url = 'addHcsConfig'
            const deviceArr = []
            this.equipmentList.forEach((item, index) => {
              const deviceObj = {
                storageLocationId: this.warehouseList[index].warehouseId, // 仓库id
                storageLocation: this.warehouseOptions.find((i) => i.id == this.warehouseList[index].warehouseId).warehouseName, // 仓库name
                monitoringDeviceId: item.map((i) => i.id).join(','), // 设备id
                monitoringDevice: item.map((i) => i.assetsName).join(','), // 设备name
                systemId: this.parentObj[index] ? this.parentObj[index].systemId : Array.from(new Set(item.map((i) => i.systemId))).join(','), // 设备分类id
                systemName: this.parentObj[index] ? this.parentObj[index].systemName : Array.from(new Set(item.map((i) => i.systemName))).join(','), // 设备分类name
                dictionaryDetailsCode: this.parentObj[index] ? this.parentObj[index].dictionaryDetailsCode : Array.from(new Set(item.map((i) => i.dictionaryDetailsCode))).join(',')
              }
              deviceArr.push(deviceObj)
            })
            const newParams = {
              monitoringDevice: {
                id: this.form5.terminalId,
                endpoint: this.terminaArr.find((i) => i.id == this.form5.terminalId).endpoint, // 终端类型
                description: this.form5.explain,
                status: this.form5.status,
                userId: this.$store.state.user.userInfo.user.staffId,
                userName: this.$store.state.user.userInfo.user.staffName,
                hospitalCode: this.$store.state.user.userInfo.user.hospitalCode,
                unitCode: this.$store.state.user.userInfo.user.unitCode
              },
              deviceDetails: deviceArr
            }
            params = newParams
          }
          if (this.type == 'edit') {
            params.id = this.$route.query.rowData.id
            if (this.functionType == 1) {
              const editObj = this.$route.query.rowData
              params = {
                ...params,
                ...editObj,
                name: this.form1.materialTypeName,
                code: this.form1.materialTypeCode,
                materialParentId: editObj.dictionaryCategoryId,
                oldParentId: editObj.id,
                dictionaryDetailsName: this.form1.materialTypeName,
                dictionaryDetailsCode: this.form1.materialTypeCode,
                dictionaryDetailsRemake: this.form1.dictionaryDetailsRemake,
                dictionaryDetailsStatus: this.form1.dictionaryDetailsStatus
              }
              if (this.form1.materialParentId.length) {
                params.parentId = this.form1.materialParentId[this.form1.materialParentId.length - 1]
              } else {
                params.parentId = this.hazardousArray.find((i) => i.code == 'All_HAZARDOUS_CHEMICALS').id
                params.parentName = this.hazardousArray.find((i) => i.code == 'All_HAZARDOUS_CHEMICALS').name
              }
              url = 'hscDictionariesEdit'
            } else if (this.functionType == 5) {
              url = 'editHcsConfig'
            }
          }
          this.pageLoading = true
          this.$api[url](params).then((res) => {
            if (res.code == '200') {
              this.$message.success('保存成功！')
              this.$router.go(-1)
            } else {
              this.$message.error(res.message || '保存失败！')
            }
            this.pageLoading = false
          })
        }
      })
    },
    // 危化品类型 上级分类
    getParentTypeData() {
      const params = {
        userType: 1,
        userName: this.$store.state.user.userInfo.user.staffName,
        userId: this.$store.state.user.userInfo.user.staffId,
        dictionaryDetailsStatus: 1 // 启用：1 禁用：0
      }
      this.$api.hscParentTypeData(params).then((res) => {
        if (res.code == '200') {
          this.paramsType = transData(res.data, 'id', 'pid', 'children')
          this.hazardousArray = JSON.parse(JSON.stringify(res.data))
          if (this.type != 'add') {
            this.form1.materialParentId = this.findParentId(this.$route.query.rowData.pid)
          }
        }
      })
    },
    // 根据传递得id找到自己和自己得父级
    findParentId(pid, id) {
      const parentIds = []
      const findParent = (pids) => {
        const item = this.hazardousArray.find((item) => item.id === pids)
        if (item) {
          parentIds.unshift(item.id)
          findParent(item.pid)
        }
      }
      findParent(pid)
      return id ? [...parentIds, id] : [...parentIds]
    },
    // 类型设置 参数类型
    getParamsTypeData() {
      const params = {
        parentId: '#',
        userName: this.$store.state.user.userInfo.user.staffName,
        userId: this.$store.state.user.userInfo.user.staffId
      }
      this.$api.hscTypeSettingList(params).then((res) => {
        if (res.code == '200') {
          this.paramsType = res.data.list
        }
      })
    },
    // 基本单位/所属分类
    getBaseUnitList(type) {
      const params = {
        type,
        userName: this.$store.state.user.userInfo.user.staffName,
        userId: this.$store.state.user.userInfo.user.staffId
      }
      this.$api.getBaseUnitData(params).then((res) => {
        if (res.code == '200') {
          if (type == 'JLDW') {
            this.basicUnitArr = res.data
          } else {
            this.allClassify = res.data
            this.classifyArr = transData(res.data, 'id', 'parentId', 'children')
          }
        }
      })
    },
    // 生产厂家/供应商
    gethscUnitManger(type) {
      const params = {
        unitsTypeCode: type,
        category: 2,
        userName: this.$store.state.user.userInfo.user.staffName,
        userId: this.$store.state.user.userInfo.user.staffId,
        status: '0'
      }
      this.$api.hscUnitManger(params).then((res) => {
        if (res.code == '200') {
          if (type == 'GYS') {
            this.supplierArr = res.data.list
          } else {
            this.manufacturersArr = res.data.list
          }
        }
      })
    },
    changeFileEcho(file, fileList) {
      this.fileEcho = fileList
    },
    httpRequest(item) {
      const params = new FormData()
      params.append('file', item.file)
      this.fileEcho.forEach((i) => {
        i.status = 'uploading'
      })
      this.$api.uploadFile(params).then((res) => {
        if (res.code === '200') {
          this.$message({
            message: '上传成功',
            type: 'success'
          })
          this.fileEcho.forEach((i) => {
            i.status = 'success'
          })
          this.materialPhoto = res.data.fileKey
        } else {
          this.$message({
            message: res.message,
            type: 'warning'
          })
        }
      })
    },
    handleExceed() {
      this.$message.error('只允许传一份文件')
    },
    removeImg(file) {
      this.fileEcho = []
      this.materialPhoto = ''
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    // 危化品字典详情
    getHscDetail() {
      const params = {
        id: this.$route.query.rowData.id,
        userName: this.$store.state.user.userInfo.user.staffName,
        userId: this.$store.state.user.userInfo.user.staffId
      }
      this.$api.hscDictionariesView(params).then((res) => {
        if (res.code == '200') {
          if (res.data.materialPhoto) {
            this.materialPhoto = res.data.materialPhoto
            res.data.materialPhoto = this.$tools.imgUrlTranslation(res.data.materialPhoto)
            this.fileEcho = [
              {
                name: '',
                url: res.data.materialPhoto
              }
            ]
          }
          if (!res.data.supplierId) {
            res.data.supplierId = null
          }
          this.form2 = res.data
          if (this.form2.materialPhoto) {
            this.form2.materialPhoto = this.$tools.imgUrlTranslation(this.form2.materialPhoto)
          }
          this.$forceUpdate()
        }
      })
    },
    // 仓库列表
    getWarehouseList() {
      this.$api.queryWarehouseByPageAll({ status: '0' }).then((res) => {
        if (res.code == '200') {
          this.warehouseOptions = res.data.list
        }
      })
    },
    // 终端列表
    getHcsTerminalData() {
      this.pageLoading = true
      this.$api.getHcsTerminalList({}).then((res) => {
        if (res.code == '200') {
          if (this.type == 'add') {
            this.$api.getHcsCheckTerminalList({}).then((ret) => {
              if (ret.code == '200') {
                if (ret.data.length) {
                  res.data.forEach((i) => {
                    ret.data.forEach((j) => {
                      if (i.id == j.id) {
                        console.log('i', i)
                        i.disabled = true
                      }
                    })
                  })
                }
                this.terminaArr = res.data
              }
              this.pageLoading = false
            })
          } else {
            this.terminaArr = res.data
          }
        }
      })
    },
    addWarehouse() {
      this.warehouseList.push({
        activeEquipment: '',
        warehouseId: ''
      })
    },
    deleteWarehouse(index) {
      this.warehouseList = this.warehouseList.filter((item, i) => index != i)
    },
    setEquipment(index) {
      this.activeIndex = index
      this.selectDialog = true
    },
    handleClose() {
      this.selectDialog = false
    },
    completeSelect() {
      this.$refs.dialogContent.emitParams()
    },
    completeData(data) {
      this.equipmentList[this.activeIndex] = data.listData
      this.parentObj[this.activeIndex] = data.parent
      this.warehouseList[this.activeIndex].activeEquipment = data.listData.map((i) => i.id)
      this.selectDialog = false
    },
    // 配置详情
    getMonitoringDetail() {
      const params = {
        id: this.$route.query.rowData.id,
        userName: this.$store.state.user.userInfo.user.staffName,
        userId: this.$store.state.user.userInfo.user.staffId
      }
      this.$api.getHcsConfigDetail(params).then((res) => {
        if (res.code == '200') {
          this.$nextTick(() => {
            this.form5.terminalId = Number(res.data.monitoringDevice.id)
          })
          const recordList = res.data.deviceDetails
          this.form5.terminal = res.data.monitoringDevice.endpoint
          this.form5.endpoint = res.data.monitoringDevice.endpoint
          this.form5.explain = res.data.monitoringDevice.description
          this.form5.status = res.data.monitoringDevice.status
          this.form5.statusText = res.data.monitoringDevice.status
          const deviceData = []
          recordList.forEach((i) => {
            i.warehouseId = i.storageLocationId
            i.activeEquipment = i.monitoringDeviceId.split(',')
            const idArr = i.monitoringDeviceId.split(',')
            const nameArr = i.monitoringDevice.split(',')
            const arr = []
            idArr.forEach((j, index) => {
              arr.push({
                id: j,
                assetsName: nameArr[index],
                systemId: i.systemId,
                systemName: i.systemName,
                dictionaryDetailsCode: i.dictionaryDetailsCode
              })
            })
            deviceData.push(arr)
          })
          this.equipmentList = deviceData
          this.warehouseList = recordList
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.inner {
  background-color: #fff;
  padding: 15px 60px;
}
.terminaWrap {
  ::v-deep .el-form-item {
    display: flex;
    align-items: center;
    .el-form-item__content {
      margin-left: 0 !important;
    }
  }
}
::v-deep .monitoringDialog > .el-dialog__body {
  padding: 0 20px;
  height: 380px;
}
::v-deep .terminaWrap {
  .el-form-item__content {
    display: flex;
    align-items: center;
  }
}
::v-deep .el-upload-dragger {
  height: 148px;
}
::v-deep .el-cascader {
  width: 300px !important;
}
::v-deep .equipmentGroup {
  .el-input__suffix {
    display: none;
  }
}
</style>
<style>
.el-select-dropdown {
  width: 200px;
}
.selectPopover {
  display: none;
}
</style>
