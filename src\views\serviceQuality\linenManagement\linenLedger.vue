<template>
  <PageContainer>
    <div slot="content" class="role-content" style="height: 100%">
      <div class="role-content-right">
        <div slot="heard" class="role-content-heard">
          <div class="time">
            <span style="margin-left: 0px;" :class="{ active: activeIndex == '0' }" @click="selectTab('0')">全部布草</span>
            <span :class="{ active: activeIndex == '1' }" @click="selectTab('1')">科室布草</span>
            <span :class="{ active: activeIndex == '2' }" @click="selectTab('2')">库存布草</span>
            <span :class="{ active: activeIndex == '3' }" @click="selectTab('3')">在用布草</span>
            <span :class="{ active: activeIndex == '4' }" @click="selectTab('4')">报废布草</span>
          </div>
        </div>
        <div style="height: 100%">
          <div class="search-from">
            <div style="margin-bottom: 5px;">
              <span class="green_line"></span>
              <span style="font-size: 15px; color: #333; font-weight: 500;">{{ titleName }}</span>
            </div>
            <div v-if="activeIndex == '1' && ksType != '1'">
              <el-input v-model.trim="title" placeholder="请输入科室名称" style="width: 200px;margin-right: 10px;" clearable
                maxlength="25" onkeyup="if(value.length>25)value=value.slice(0,25)"></el-input>
              <el-button type="primary" plain @click="resetForm">重置</el-button>
              <el-button type="primary" @click="searchForm">查询</el-button>

            </div>
          </div>
          <div class="contentTable">
            <div class="contentTable-main table-content">
              <el-table-column type="selection" width="60" align="center"></el-table-column>
              <el-table v-loading="tableLoading" border style="width: 100%" :data="tableData" height="100%" stripe>
                <el-table-column type="index" label="序号" width="70">
                  <template slot-scope="scope">
                    <span>{{ (pagination.current - 1) * pagination.size + scope.$index + 1 }}</span>
                  </template>
                </el-table-column>
                <template v-if="activeIndex != '1'">
                  <el-table-column v-for="col in tableList" :prop="col.column" :label="col.fieldName"
                    show-overflow-tooltip> </el-table-column>
                </template>
                <template v-if="activeIndex == '1' && ksType != '1'">
                  <el-table-column v-for="col in tableKSList" :prop="col.column" :label="col.fieldName"
                    show-overflow-tooltip> </el-table-column>
                </template>
                <template v-if="activeIndex == '1' && ksType == '1'">
                  <el-table-column v-for="col in tableList" :prop="col.column" :label="col.fieldName"
                    show-overflow-tooltip> </el-table-column>
                </template>
                <el-table-column v-if="activeIndex == '1' && ksType != '1'" label="操作" width="120" fixed="right">
                  <template slot-scope="scope">
                    <el-button type="text" @click="examine(scope.row, '1')">查看</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="contentTable-footer">
              <el-pagination :current-page="pagination.current" :page-sizes="pagination.pageSizeOptions"
                :page-size="pagination.size" :layout="pagination.layoutOptions" :total="pagination.total"
                @size-change="paginationSizeChange" @current-change="paginationCurrentChange">
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin.js'
export default {
  name: 'patroLine',
  mixins: [tableListMixin,],
  data() {
    return {
      tableId: '',
      title: '', // 科室名称,
      tableLoading: false,
      tableData: [], // 全部布草
      tableOfficeList: [], // 科室布草列表
      drawerPageNo: 1,
      drawerPageSize: 10,
      drawerTotal: 0,
      tableKSList: [
        {
          fieldName: '科室id',
          column: 'id',
        },
        {
          fieldName: '科室名称',
          column: 'title',
        },
        // {
        //   fieldName: '数量',
        //   column: 'nums',
        // },
      ],
      tableList: [
        {
          fieldName: '布草名称',
          column: 'title',
        },
        {
          fieldName: '布草类型',
          column: 'category_name',
        },
        {
          fieldName: '布草规格',
          column: 'spec_name',
        },
        {
          fieldName: '数量',
          column: 'nums',
        }
      ],
      activeIndex: 0,
      ksType: '',
      titleName: '全部布草',
      ksIds: '',
    }
  },
  created() {
    this.getDataList() // 全部布草
  },

  methods: {
    formatThousands(num) {
      const parts = num.toString().split('.')
      parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      return parts.join('.')
    },
    selectTab(index) {
      this.activeIndex = index
      this.tableData = []
      this.ksType = ''
      if (this.activeIndex == '0') {
        this.getDataList()
        this.titleName = '全部布草'
      } else if (this.activeIndex == '1') {
        this.getOfficeList('getOfficeList')
        this.titleName = '科室布草'
      } else if (this.activeIndex == '2') {
        this.getOfficeList('getStockList')
        this.titleName = '库存布草'
      } else if (this.activeIndex == '3') {
        this.getOfficeList('getInuseList')
        this.titleName = '在用布草'
      } else if (this.activeIndex == '4') {
        this.getOfficeList('getKilledList')
        this.titleName = '报废布草'
      }
      this.pagination.current = 1
      this.pagination.size = 15
      this.pagination.total = 0
    },
    // 科室查看
    examine(row, type) {
      this.ksType = type
      this.ksIds = row.id
      if (this.activeIndex == '1' && this.ksType == '1') {
        this.getOfficeGoodsData('getOfficeGoodsList')
      }
    },
    // 获取全部布草
    getDataList() {
      this.tableData = []
      let data = {
        page: this.pagination.current,
        pagesize: this.pagination.size,
        id: '0'
      }
      this.tableLoading = true
      this.$api.getFullLinenList(data).then((res) => {
        if (res.code == '200') {
          this.tableData = res.data
          this.pagination.total = parseInt(res.count)
        }
      })
      this.tableLoading = false
    },
    // 获取布草接口
    getOfficeList(str) {
      this.tableData = []
      let data = {
        page: this.pagination.current,
        pagesize: this.pagination.size,
        id: '0'
      }
      if (this.activeIndex == '1') {
        delete data.id
        data.title = this.title
      }
      this.tableLoading = true
      this.$api[str](data).then((res) => {
        if (res.code == '200') {
          this.tableData = res.data
          this.pagination.total = parseInt(res.count)
        }
      })
      this.tableLoading = false
    },
    // 获取科室布草数量
    getOfficeGoodsData(str) {
      this.tableData = []
      let data = {
        page: this.pagination.current,
        pagesize: this.pagination.size,
        officeId: this.ksIds
      }
      this.tableLoading = true
      this.$api[str](data).then((res) => {
        if (res.code == '200') {
          console.log(res.count);
          this.tableData = res.data
          this.pagination.total = parseInt(res.count)
        }
      })
      this.tableLoading = false
    },
    // 重置
    resetForm() {
      if (this.activeIndex == '1') {
        this.title = ''
        this.getOfficeList('getOfficeList')
      }
    },
    // 查询
    searchForm() {
      this.pagination.current = 1
      this.getOfficeList('getOfficeList')
    },
    //分页相关
    paginationSizeChange(val) {
      this.pagination.current = 1
      this.pagination.size = val
      if (this.activeIndex == '1' && this.ksType != '1') {
        this.getOfficeList('getOfficeList')
      } else if (this.activeIndex == '1' && this.ksType == '1') {
        this.getOfficeGoodsData('getOfficeGoodsList')
      } else {
        this.getDataList()
      }
    },
    paginationCurrentChange(val) {
      this.pagination.current = val
      if (this.activeIndex == '1' && this.ksType != '1') {
        this.getOfficeList('getOfficeList')
      } else if (this.activeIndex == '1' && this.ksType == '1') {
        this.getOfficeGoodsData('getOfficeGoodsList')
      } else {
        this.getDataList()
      }
    },
  }
}
</script>
<style lang="scss" scoped>
.el-tree-node__content {
  width: 100%;

  .custom-tree-node {
    display: inline-block;
    width: 100%;
    overflow: hidden;

    .item {
      display: inline-block;
      width: calc(100%);
      // width: calc(100% - 10px);
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
  }
}

::v-deep .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
  color: #3562db;
  background: linear-gradient(to right, #d9e1f8, #fff);
}

::v-deep .el-tree-node__content {
  height: 32px;
}

.role-content {
  height: 100%;
  display: flex;

  .role-content-right {
    height: 100%;
    min-width: 0;
    padding: 20px;
    background: #fff;
    border-radius: 4px;
    flex: 1;


    .search-from {
      padding-bottom: 12px;
      display: flex;
      flex-direction: column;

      &>div {
        margin-right: 10px;
      }

      &>button {
        margin-top: 12px;
      }
    }

    .contentTable {
      height: calc(100% - 125px);
      display: flex;
      flex-direction: column;

      .contentTable-main {
        flex: 1;
        overflow: auto;
      }

      .contentTable-footer {
        padding: 10px 0 0;
      }
    }
  }

  .role-content-heard {
    height: 50px;
    width: 100%;
    margin-bottom: 15px;
    display: flex;
  }

  .content {
    width: 100%;
    max-height: 500px !important;
    overflow: auto;
    background-color: #fff !important;
  }
}

::v-deep .el-tabs__nav-scroll {
  width: 80%;
  margin: 0 auto;
}

::v-deep .el-drawer__open .el-drawer.rtl {
  background: #f6f5fa;
  border-radius: 4px;
}

::v-deep .el-drawer__header {
  height: 56px;
  padding: 15px 20px;
  margin-bottom: 0;
  background: #fff;
  border-bottom: 1px solid $color-text-secondary;
  box-shadow: 0 0 12px 3px rgb(0 0 0 / 10%);

  &>span {
    font-size: 18px;
    font-family: 'PingFang SC-Medium', 'PingFang SC';
    font-weight: 500;
    color: $color-text;
  }

  .el-dialog__close {
    color: $color-text;
    font-weight: 600;
    font-size: 18px;
  }
}

::v-deep .el-drawer__body {
  background-color: #fff;
  margin: 20px;
  padding: 20px 10px;
  height: calc(100% - 80px);
  overflow-y: auto;
}

::v-deep .el-timeline-item__timestamp.is-bottom {
  font-size: 14px;
  position: absolute;
  left: -100px;
  top: -5px;
  font-weight: 600;
  color: #121f3e;
}

::v-deep .el-timeline {
  padding-left: 120px;
}

.timeContent {
  height: 100%;
  overflow: auto;

  .time {
    font-size: 14px;
    font-family: 'PingFang SC-Regular', 'PingFang SC';
    color: #414653;
  }

  .continer {
    display: flex;
    // justify-content: space-between;
    flex-wrap: wrap;

    .item {
      height: 32px;
      flex: 1;
      padding: 0 16px;
      background-color: #faf9fc;
      line-height: 32px;
      white-space: nowrap;
      margin-bottom: 20px;
      margin-right: 10px;
    }

    .itemContent {
      height: 32px;
      width: 220px;
      padding: 0 16px;
      background-color: #faf9fc;
      line-height: 32px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      cursor: pointer;
      margin-right: 10px;
    }
  }
}

.green_line {
  display: inline-block;
  width: 8px;
  height: 16px;
  border-radius: 0 8px 8px 0;
  background: #3562db;
  margin-right: 10px;
  vertical-align: middle;
}

::v-deep .el-timeline-item__node--normal {
  background: #fff;
  border: 2px solid #3562db;
}

.noData {
  display: inline-block;
  padding-bottom: 10px;
  width: 100%;
  margin: 0;
  font-size: 14px;
  color: #999;
  text-align: center;
}

.record {
  color: #66b1ff !important;
}

.delete {
  color: red !important;
}

::v-deep .el-tree-node {
  white-space: normal;
  // outline: 0;
}

.rightBtn {
  height: 36px;
  margin: 0;
  margin-right: 10px;
}

.leadFile {
  display: flex;
}

.leadFile_item {
  margin: 10px 35px;
  color: #66b1ff;
  cursor: pointer;
}

::v-deep .el-message-box.no-close-btn .el-message-box__headerbtn {
  display: none;
}

.time {
  display: flex;
  height: 50px;

  span {
    line-height: 50px;
    font-size: 15px;
    color: #414653;
    margin: 0 16px;
    cursor: pointer;
  }
}

.active {
  color: #3562db !important;
  border-bottom: 2px solid #3562db;
}
</style>
