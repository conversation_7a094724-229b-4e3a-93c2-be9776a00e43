<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    :title="title"
    width="40%"
    :visible.sync="visible"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <div class="dialog-content">
      <div class="content-left">
        <el-input v-model="treeFilter" placeholder="请输入关键字" clearable></el-input>
        <div v-loading="treeLoading" class="left-tree">
          <el-tree
            v-if="deptTreeData.length"
            ref="tree"
            :data="deptTreeData"
            node-key="id"
            size="small"
            :highlight-current="true"
            show-checkbox
            :check-strictly="false"
            :props="treeProps"
            :filter-node-method="filterNode"
            @check="checkData"
            @check-change="handleCheckChange"
          >
          </el-tree>
        </div>
      </div>
      <div class="content-right">
        <div class="right-title">
          <p class="title">
            已选：
            <span>{{ selectDept.length }}</span>
            个空间
          </p>
          <p class="clear" @click="clear">清空</p>
        </div>
        <div v-infinite-scroll="load" :infinite-scroll-immediate="false" class="right-list">
          <div v-for="(item, index) in pageListData" :key="index" class="list-item">
            <span>{{ `${item.parentSpaceName}>${item.ssmName}` }}</span>
            <i class="el-icon-close" @click="remove(item, index)"></i>
          </div>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="confirm('ruleForm')">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { transData } from '@/util'
export default {
  name: 'deptDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '所属科室'
    },
    isRadio: {
      type: Boolean,
      default: false
    },
    nature: {
      type: String,
      default: '1'
    },
    isNotmultiSector: {
      type: Boolean,
      default: false
    },
    spaceList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      treeFilter: '',
      treeLoading: false,
      deptTreeData: [],
      treeProps: {
        label: 'ssmName',
        children: 'children',
        disabled: 'disabled'
      },
      selectDept: [],
      lastTreeParentId: '',
      disabledNode: [],
      pageListData: [],
      pageParams: {
        pageNum: 1,
        pageSize: 15
      }
    }
  },
  watch: {
    treeFilter(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    this.getDisabledTreeNode()
    this.getUnitListFn()
  },
  methods: {
    // 移除
    remove(node, index) {
      this.$refs.tree.setChecked(node.id, false)
      this.selectDept.splice(index, 1)
      this.pageListData.splice(index, 1)
      if (!this.selectDept.length) {
        this.clear()
      }
    },
    // 清空
    clear() {
      this.selectDept = []
      this.pageListData = []
      this.$nextTick(() => {
        this.$refs.tree.setCheckedKeys([])
      })
    },
    filterNode(value, data) {
      if (!value) return true
      return data.ssmName == value || data.parentName == value
    },
    checkData(data, checked, indeterminate) {
      if (this.isRadio) {
        this.selectDept = [data]
        this.$refs.tree.setCheckedKeys([data.id])
      } else {
        const checkedArr = this.$refs.tree.getCheckedNodes()
        this.selectDept = checkedArr.filter((i) => i.ssmType == 5)
      }
      if (!this.selectDept.length) {
        this.$message.warning('当前空间下暂无房间')
        this.$refs.tree.getCheckedNodes().forEach((item) => {
          if (item.ssmType != 5) {
            this.$refs.tree.setChecked(item.id, false)
          }
        })
      }
      this.pageListData = this.selectDept.slice(0, this.pageParams.pageSize)
    },
    // 获取单位列表
    getUnitListFn() {
      this.treeLoading = true
      this.$api.getAllSpaceInfo({}).then(async (res) => {
        if (res.code == 200) {
          let list = res.data
          for (let i = 0; i < list.length; i++) {
            if (this.disabledNode.includes(list[i].id)) {
              list[i].disabled = true
            } else {
              list[i].disabled = false
            }
          }
          this.deptTreeData = transData(list, 'id', 'parentId', 'children')
          this.treeLoading = false
          if (this.spaceList.length) {
            this.$nextTick(() => {
              for (let i = 0; i < this.spaceList.length; i++) {
                const target = this.spaceList[i]
                this.$refs.tree.setChecked(target.spaceId, true)
                const item = {
                  parentSpaceIds: target.regionCode,
                  parentSpaceName: target.regionName,
                  modelCode: target.modelCode,
                  ssmName: target.spaceName,
                  localSpaceCode: target.localCode,
                  id: target.spaceId
                }
                this.selectDept.push(item)
              }
              this.pageListData = this.selectDept.slice(0, 15)
              this.pageParams.pageNum += 1
            })
          }
        }
      })
    },
    getDisabledTreeNode() {
      this.$api.getSelectAllSpaceIds({}).then((res) => {
        this.disabledNode = res.data.map((v) => v.spaceId)
      })
    },
    confirm() {
      if (!this.selectDept.length) {
        this.$message({ message: '请选择空间', type: 'error' })
        return
      }
      this.$emit('selectDept', this.selectDept)
      this.closeDialog()
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', !this.visible)
    },
    handleCheckChange(node, check, childCheck) {
      if (this.isNotmultiSector) {
        let checkedArr = this.$refs.tree.getCheckedNodes()
        if (check) {
          if (checkedArr.length > 1) {
            for (let i = 0; i < checkedArr.length; i++) {
              /*
            在已选节点中(包含最后次勾选不在原先同层节点的数据)判断当前勾选的节点是否跟原先的节点同层
            */
              if (node.id != checkedArr[i].id) {
                this.lastTreeParentId = checkedArr[i].umId
              }
            }
            if (node.umId != this.lastTreeParentId) {
              this.$message.warning('选择的节点不在同一层级请重新选择')
              // 移除已选中的并且不在同一层的节点
              this.$refs.tree.setChecked(node.id, false, false)
              return
            }
          }
        } else {
          if (checkedArr.length == 0) {
            this.lastTreeParentId = null
          }
        }
      }
    },
    load() {
      if (this.selectDept.length > this.pageParams.pageSize) {
        const targetData = this.selectDept.slice(this.pageListData.length, this.pageParams.pageNum * this.pageParams.pageSize)
        this.pageListData = this.pageListData.concat(targetData)
        this.pageParams.pageNum += 1
      }
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .model-dialog {
  // min-height: 60vh;
}
.dialog-content {
  width: 100%;
  background: #fff;
  padding: 24px 0px;
  display: flex;
  p {
    margin: 0;
  }
  .content-left {
    height: 480px;
    flex: 1;
    padding: 0px 24px;
    border-right: 1px solid #e4e7ed;
    display: flex;
    flex-direction: column;
    .left-tree {
      flex: 1;
      margin-top: 16px;
      overflow: auto;
    }
    ::v-deep .el-tree-node {
      .is-leaf + .el-checkbox .el-checkbox__inner {
        display: inline-block;
      }
      .el-checkbox .el-checkbox__inner {
        // display: none;
      }
    }
  }
  .content-right {
    height: 480px;
    flex: 1;
    padding: 0px 24px;
    display: flex;
    flex-direction: column;
    .right-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 10px;
      .title {
        font-size: 14px;
        color: #7f848c;
        line-height: 22px;
        span {
          color: #333333;
          margin: 0 8px;
        }
      }
      .clear {
        cursor: pointer;
        font-size: 12px;
        color: #3562db;
      }
    }
    .right-list {
      flex: 1;
      overflow: auto;
      .list-item {
        transition: all 0.3s;
        padding: 8px 12px;
        border-radius: 4px;
        color: #333333;
        margin-top: 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        i {
          cursor: pointer;
          color: #666666;
          font-size: 16px;
        }
      }
      .list-item:hover {
        background: #e6effc;
      }
    }
  }
}
</style>
