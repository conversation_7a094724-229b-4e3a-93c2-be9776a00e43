<script>
import { UsingStatusOptions } from '@/views/operationPort/constant'
import {
  DocumentType,
  GenerateMode,
  NodeStatus
} from '@/views/operationPort/projectManage/projectConfig/billConfig/constant'
export default {
  name: 'BillEdit',
  props: {
    editData: Object,
    visible: <PERSON><PERSON><PERSON>,
    readonly: <PERSON><PERSON><PERSON>
  },
  events: ['update:visible', 'success'],
  data: function() {
    return {
      formModel: {
        // 单据名称
        name: '',
        // 单据编码
        code: '',
        // 项目ID
        formId: '',
        // 生成方式
        mode: '',
        // 项目下的节点
        endPoint: '',
        // 节点状态
        endPointStatus: '',
        // 单据状态
        status: 1,
        // 报表key
        reportKey: '',
        // 单据备注
        remark: '',
        // 单据类型
        documentType: ''
      },
      rules: {
        name: [{ required: true, message: '请输入单据名称' }],
        code: [{ required: true, message: '请输入单据编码' }],
        formId: [{ required: true, message: '请选择项目类型' }],
        mode: [{ required: true, message: '请选择生成方式' }],
        endPoint: [{ required: true, message: '请选择生成节点' }],
        endPointStatus: [{ required: true, message: '请选择节点状态' }],
        reportKey: [{ required: true, message: '请输入报表key' }],
        documentType: [{ required: true, message: '请选择单据类型' }]
      },
      // 项目列表
      projectList: [],
      // 流程节点列表
      endPointList: [],
      loadingStatus: false,
      statusOptions: UsingStatusOptions,
      documentTypeOptions: DocumentType,
      nodeStatusOptions: NodeStatus,
      generateModeOptions: GenerateMode,
      // 要反显的流程节点
      tmpEndPoint: ''
    }
  },
  computed: {
    title: function() {
      if (this.readonly) {
        return '查看单据'
      } else {
        return this.editData ? '编辑单据' : '新建单据'
      }
    },
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    },
    // 是否可以还原数据
    canRevertData() {
      return this.dialogVisible && this.editData && typeof this.editData === 'object'
    },
    // 项目详情
    flowKey() {
      const item = this.projectList.find(it => it.id === +this.formModel.formId)
      return item?.flowKey
    }
  },
  watch: {
    dialogVisible(val) {
      if (val && !this.projectList.length) {
        this.getProjectList()
      }
    },
    flowKey(val) {
      // 选中项目对应的流程KEY发生变化,已选择的流程节点作废
      this.formModel.endPoint = ''
      this.endPointList = []
      // 需要获取一次流程列表
      if (val) this.getFlowEndPointListByProjectId()
    },
    // 反显数据
    canRevertData(value) {
      if (!value) return
      this.$nextTick(() => {
        this.formModel.name = this.editData.name
        this.formModel.code = this.editData.code
        this.formModel.status = +this.editData.state
        this.formModel.remark = this.editData.remark
        this.formModel.formId = this.editData.businessFormId || ''
        this.formModel.reportKey = this.editData.reportKey || ''
        this.formModel.endPointStatus = this.editData.nodeState
        this.formModel.mode = this.editData.createType || ''
        this.tmpEndPoint = this.editData.workNodeId
        this.formModel.endPoint = this.editData.workNodeId
        this.formModel.documentType = this.editData.documentType || ''
      })
    }
  },
  methods: {
    getProjectList() {
      this.loadingStatus = true
      this.$api.SporadicProject.queryProjectBusinessFormAll()
        .then((res) => {
          if (res.code === '200') {
            this.projectList = res.data
          }
        })
        .finally(() => {
          this.loadingStatus = false
        })
    },
    getFlowEndPointListByProjectId() {
      const param = { flowKey: this.flowKey }
      this.loadingStatus = true
      this.$api.SporadicProject.getWorkNodeListByFlowKey(param)
        .then((res) => {
          if (res.code === '200') {
            this.endPointList = res.data
            // revert endPoint
            if (this.tmpEndPoint) {
              this.formModel.endPoint = this.tmpEndPoint
              this.tmpEndPoint = ''
            }
          }
        })
        .finally(() => {
          this.loadingStatus = false
        })
    },
    clearForm() {
      this.$nextTick(() => {
        this.$refs.formRef.resetFields()
      })
    },
    onSubmit() {
      this.$refs.formRef.validate().catch(() => Promise.reject())
        .then(() => {
          // config request data
          const endPointInfo = this.endPointList.find(it => it.id === this.formModel.endPoint)
          const params = {
            name: this.formModel.name,
            code: this.formModel.code,
            remark: this.formModel.remark,
            state: this.formModel.status,
            businessFormId: this.formModel.formId,
            reportKey: this.formModel.reportKey,
            createType: this.formModel.mode,
            documentType: this.formModel.documentType,
            // 节点信息
            nodeState: this.formModel.endPointStatus,
            workNodeId: this.formModel.endPoint,
            workNodeName: endPointInfo.name
          }
          this.loadingStatus = true
          // do request
          if (this.editData) {
            params.id = this.editData.id
            return this.$api.SporadicProject.updateProjectDocumentConfig(params)
          } else {
            return this.$api.SporadicProject.createProjectDocumentConfig(params)
          }
        })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('操作成功')
            this.$emit('success')
            this.dialogVisible = false
          } else {
            throw res.msg || '操作失败'
          }
        })
        .catch((msg) => {
          msg && this.$message.error(msg)
        })
        .finally(() => {
          this.loadingStatus = false
        })
    }
  }
}
</script>
<template>
  <el-dialog
    v-dialogDrag
    v-loading="loadingStatus"
    class="component bill-edit"
    :title="title"
    width="750px"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    custom-class="model-dialog"
    @closed="clearForm"
  >
    <el-form ref="formRef" :model="formModel" :rules="rules" label-width="95px" :disabled="readonly" :class="{readonly}">
      <el-row :gutter="12">
        <el-col :span="12">
          <el-form-item label="单据名称" prop="name">
            <el-input v-model="formModel.name" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="单据编码" prop="code">
            <el-input v-model="formModel.code" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="单据类型" prop="documentType">
            <el-select v-model="formModel.documentType" placeholder="请选择">
              <el-option v-for="item of documentTypeOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select v-model="formModel.status" placeholder="请选择">
              <el-option v-for="item of statusOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="项目类型" prop="formId">
            <el-select v-model="formModel.formId" filterable placeholder="请选择">
              <el-option v-for="item of projectList" :key="item.id" :value="item.id.toString()" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="生成方式" prop="mode">
            <el-select v-model="formModel.mode" placeholder="请选择">
              <el-option v-for="item of generateModeOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="生成节点" prop="endPoint">
            <el-select v-model="formModel.endPoint" filterable placeholder="请选择">
              <el-option v-for="item of endPointList" :key="item.id" :value="item.id" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="节点状态" prop="endPointStatus">
            <el-select v-model="formModel.endPointStatus" placeholder="请选择">
              <el-option v-for="item of nodeStatusOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="报表key" prop="reportKey">
            <el-input v-model="formModel.reportKey" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="formModel.remark" type="textarea" :rows="4" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button type="primary" plain @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="onSubmit">确认</el-button>
    </template>
  </el-dialog>
</template>
<style lang="scss">
.component.bill-edit {
  .el-form {
    background-color: #fff;
    padding: 10px 16px 0;
    .el-form-item {
      .el-select {
        width: 100%;
      }
    }
    &.readonly {
      .el-form-item {
        .el-form-item__label::before {
          display: none;
        }
        .el-input__inner,
        .el-cascader,
        .el-select {
          background-color: transparent;
          border: none;
        }
        .el-input__suffix {
          display: none;
        }
        .el-upload-list__item-status-label {
          display: none;
        }
        .el-upload__tip {
          display: none;
        }
        .el-input-group {
          width: auto;
          .el-input__inner {
            width: 50px;
          }
          .el-input-group__append {
            padding: 0;
            border: none;
            background-color: transparent;
          }
        }
        .el-textarea__inner {
          background-color: transparent;
          border: none;
          padding-top: 10px;
        }
      }
    }
  }
}
</style>
