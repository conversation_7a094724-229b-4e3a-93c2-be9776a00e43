<template>
  <PageContainer :footer="true">
    <div slot="content" class="courseContent">
      <div class="topFilter">
        <div class="backBar">
          <span style="cursor: pointer" @click="$router.go(-1)">
            <i class="el-icon-arrow-left"></i>
            逐道录题
          </span>
        </div>
      </div>
      <add-questions
        ref="addQuestions"
        :type="type"
        :questionsId="questionsId"
        @isOk="isOk"
      ></add-questions>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">取消</el-button>
      <el-button type="primary" @click="submitBtn">{{type?'修改试题':'录入题库'}}</el-button>
    </div>
  </PageContainer>
</template>
<script>
import moment from "moment";
import addQuestions from "../courseIndex/components/addQuestions.vue";
export default {
  components: {
    addQuestions,
  },
  data() {
    return {
      type: this.$route.query.type,
      questionsId: this.$route.query.id,
      moment,
    };
  },
  created() {
    console.log(this.type,'type');
  },
  methods: {
    // 录入题库
    submitBtn() {
      this.$refs.addQuestions.getValidate();
    },
    isOk(list) {
      var newList = []
      newList = JSON.parse(JSON.stringify(list))
      newList.forEach((item) => {
        if (item.type == "2") {
          item.answer = item.answer.join(",");
        }
        item.options.forEach((i,ind)=>{
          i.id=this.$tools.addLetter(ind)
        })
        item.options = JSON.stringify(item.options);
      });
      newList = newList.filter((obj) => delete obj.courseList);
      let params = {};
      let str = this.type?'updateQuestions':'addQuestions'
      if(this.type){
        params = newList[0]
        params.id=this.questionsId
      }else{
        params.questions=newList
      }
      this.$api[str](params).then((res) => {
        if (res.code == 200) {
          this.$router.go(-1);
        } else {
          this.$message.error(res.msg);
        }
      });
    },
  },
};
</script>
<style>
.process-tooltip {
  width: 712px;
  height: 528px;
}
</style>
<style lang="scss" scoped>
.courseContent {
  height: 100%;
  margin-top: 16px;
  background-color: #fff;
  // padding: 24px;
  .topFilter {
    padding: 15px;
    height: 60px;
    background-color: #fff;
    .backBar {
      color: #121f3e;
      height: 30px;
      border-bottom: 1px solid #dcdfe6;
    }
  }
  .center_contenter {
    height: calc(100% - 90px);
    .top {
      background-color: #faf9fc;
      padding: 16px;
      color: #8c8c8c;
      .quesType {
        display: flex;
        justify-content: space-between;
        margin-bottom: 16px;
        .addQues {
          color: #000;
          .remarks {
            color: #8c8c8c;
          }
        }
      }
      .quesNum {
        color: #000;
      }
    }
    .center {
      height: calc(100% - 60px);
      overflow: auto;
    }
    .item {
      padding: 16px;
      border-bottom: 4px solid #faf9fc;
      .item_top {
        display: flex;
        justify-content: space-between;
        .left {
          display: flex;
          align-items: center;
          justify-content: center;
          .exercisesType {
            width: 58px;
            height: 22px;
            line-height: 22px;
            text-align: center;
            border-radius: 4px;
            background-color: #ededf5;
            font-size: 14px;
            color: #86909c;
          }
        }
        .right {
          display: flex;
          align-items: center;

          .delQue {
            cursor: pointer;
            color: #ff6461;
            i {
              margin: 0 10px 0 16px;
            }
            span {
              font-size: 14px;
            }
          }
        }
      }
    }
    .questionsInfo {
      margin-top: 16px;
      .chioce_item {
        height: 40px;
        display: flex;
      }
      .icon_class {
        display: flex;
        align-items: center;
      }
      i {
        margin-left: 16px;
        color: #3562db;
        line-height: 40px;
        cursor: pointer;
      }
      .line {
        width: 2px;
        height: 14px;
        margin: 0 10px 0 26px;
        background-color: #dcdfe6;
      }
    }
  }
}
</style>
