<template>
  <el-drawer :visible="visible" title="开始处理" :before-close="handleClose">
    <div class="drawer_content">
      <el-form ref="ruleForm" :model="form" label-width="120px" :rules="rules">
        <el-form-item label="警情类型：" prop="alarmAffirm">
          <el-radio-group v-model="form.alarmAffirm" @change="changeAlarmAffirm">
            <el-radio :label="1">真实报警</el-radio>
            <el-radio :label="2">误报</el-radio>
            <el-radio :label="3">演练</el-radio>
            <el-radio :label="4">调试</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="警情说明：" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入警情说明，最多100字。" maxlength="100" show-word-limit rows="3"></el-input>
        </el-form-item>
        <el-form-item v-if="form.alarmAffirm == 1 || form.alarmAffirm == 3" label="同时派单：" prop="dispatch">
          <el-switch v-model="form.dispatch" active-color="#3562db" :active-value="1" inactive-color="#ccc" :inactive-value="0" @change="handleChangeDispatch"> </el-switch>
        </el-form-item>
        <el-form-item v-if="form.dispatch === 1" label="选择班组：" prop="dispatch">
          <el-switch v-model="form.changeTeam" active-color="#3562db" :active-value="1" inactive-color="#ccc" :inactive-value="0" @change="handelChangeTeam"> </el-switch>
        </el-form-item>
        <template v-if="form.changeTeam === 1">
          <el-form-item key="workTypeCode" label="工单类型：" prop="workTypeCode">
            <el-select v-model="form.workTypeCode" placeholder="选择工单类型" filterable clearable @change="handleWorkChange">
              <el-option v-for="item in workTypeData" :key="item.workTypeCode" :label="item.workTypeName" :value="item.workTypeCode"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item key="workOrderCode" label="工单事项：" prop="workOrderCode">
            <el-select v-if="form.workTypeCode == '6' || form.workTypeCode == '3'" v-model="form.workOrderCode" placeholder="选择工单事项" @change="handleMatterChange">
              <el-option v-for="item in itemTreeData" :key="item.id" :label="item.label" :value="item.id"></el-option>
            </el-select>
            <el-cascader
              v-else
              ref="workOrderCascader"
              v-model="form.workOrderCode"
              :options="itemTreeData"
              :props="orderPropsType"
              :show-all-levels="false"
              clearable
              placeholder="选择关联事项"
              @change="handleOrderChange"
            ></el-cascader>
          </el-form-item>
          <el-form-item key="teamCode" label="指派班组：" prop="teamCode">
            <el-select v-model="form.teamCode" placeholder="选择指派班组" clearable filterable @change="teamCodeChange">
              <el-option v-for="item in teamsOptions" :key="item.id" :label="item.team_name" :value="item.id"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item key="teamPerson" label="班组成员：" prop="teamPerson">
            <el-select v-model="form.teamPerson" placeholder="选择班组成员" clearable filterable multiple @change="teamPersonChange">
              <el-option v-for="item in teamsPersonOptions" :key="item.id" :label="item.name" :value="item.id"> </el-option>
            </el-select>
          </el-form-item>
        </template>
        <el-form-item label="现场文件：">
          <el-upload
            action=""
            :file-list="fileList"
            multiple
            drag
            :accept="uploadAcceptDict['annexGeneralized'].type"
            :http-request="httpRequset"
            :on-remove="handleRemove"
            :before-upload="beforeUpload"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div slot="tip" class="el-upload__tip">单个文件最大{{ uploadAcceptDict['annexGeneralized'].fileSize }}M，可上传多个文件</div>
            <el-button slot="tip" class="el-upload__tip" size="small" icon="el-icon-full-screen" @click="scanUpload">扫码上传</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
    </div>
    <div class="drawer_footer">
      <el-button plain @click="handleClose">取消</el-button>
      <el-button type="primary" @click="submit(1)">解除并存为经典</el-button>
      <el-button type="primary" @click="submit(0)">解除报警</el-button>
    </div>
    <el-dialog
      v-if="scanVisible"
      v-dialogDrag
      :modal="false"
      :close-on-click-modal="false"
      title="扫码上传"
      width="30%"
      :visible="scanVisible"
      custom-class="model-dialog"
      :before-close="closeScanDialog"
    >
      <div ref="qrCode" class="qrCode">
        <img :src="qrCodeImg" alt="" />
        <p class="DialogUploadCode__tip">
          请移动设备与客户端保持同一网段 <br />
          支持文件格式：jpg、png、pdf、doc、txt、xls
        </p>
      </div>
    </el-dialog>
  </el-drawer>
</template>
<script>
import { uploadAcceptDict } from '@/util/dict.js'
import { listToTree } from '@/util'
import axios from 'axios'
export default {
  model: {
    prop: 'visible',
    event: 'close'
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    selectItems: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      uploadAcceptDict,
      form: {
        alarmAffirm: '',
        dispatch: 0,
        classic: 0,
        changeTeam: 0,
        workType: '',
        workTypeCode: '', // 工单类型id
        workTypeName: '', // 工单类型name
        workOrderName: '', // 工单事项名称
        workOrderCode: '', // 工单事项编码
        teamCode: '', // 班组code
        teamName: '', // 班组名称
        teamPerson: [] // 班组成员
      },
      teamUser: [], // 给后端穿的list值
      rules: {
        alarmAffirm: [{ required: true, message: '请选择解除报警原因', trigger: 'change' }],
        workTypeCode: [{ required: true, message: '请选择工单类型', trigger: 'change' }],
        workOrderCode: [{ required: true, message: '请选择工单事项', trigger: 'change' }],
        teamCode: [{ required: true, message: '请选择指派班组', trigger: 'change' }],
        teamPerson: [{ required: true, message: '请选择班组成员', trigger: 'change' }]
      },
      fileList: [],
      scanVisible: false,
      qrCodeImg: '',
      webSocket: null,
      workTypeData: [], // 工单类型
      autoSendOrderItemList: [], // 工单事项,
      teamsOptions: [], // 班组
      orderPropsType: {
        children: 'children',
        label: 'name',
        value: 'id',
        checkStrictly: true,
        multiple: false
      },
      teamsPersonOptions: [], // 班组人员
      workType: '', // 工单类型
      itemTreeData: []
    }
  },
  watch: {
    scanVisible(val) {
      if (!val) {
        if (this.webSocket) {
          this.webSocket.close()
          this.webSocket = null
        }
      }
    }
  },
  mounted() {
    console.log(this.selectItems, 'selectItems')
    this.getWorkOrderList()
  },
  methods: {
    handleWorkChange(e) {
      if (e) {
        this.form.workTypeName = this.workTypeData.find((el) => el.workTypeCode == e).workTypeName
        this.form.workType = this.workTypeData.find((el) => el.workTypeCode == e).type
      }
      this.form.workOrderCode = ''
      this.form.workOrderName = ''
      this.form.teamCode = ''
      this.form.teamName = ''
      this.form.teamPerson = []
      this.teamUser = []
      if (e == '6' || e == '3') {
        this.getWorkMatter(e)
      } else {
        this.getItemTreeData(e)
      }
      this.workType = e
    },
    handleChangeDispatch() {
      this.form.changeTeam = 0
      this.handelChangeTeam()
    },
    handelChangeTeam() {
      this.form.workTypeCode = ''
      this.form.workTypeName = ''
      this.form.workOrderCode = ''
      this.form.workOrderName = ''
      this.form.teamCode = ''
      this.form.teamName = ''
      this.form.teamPerson = []
      this.teamUser = []
    },
    // 获取工单类型
    getWorkOrderList() {
      const parmas = {}
      this.$api.getTaskmentList(parmas).then((res) => {
        if (res.code == 200) {
          this.workTypeData = res.data
        }
      })
    },
    // 获取服务事项
    getItemTreeData(type) {
      this.itemTreeData = []
      const params = {
        workTypeCode: type
      }
      if (type == '16') {
        params.free1 = this.selectItems.map((el) => el.projectCode)
      } else {
        params.free1 = ''
      }
      this.$api.getItemTreeData(params).then((res) => {
        this.itemTreeData = listToTree(res, 'id', 'parent')
      })
    },
    // 获取综合服务下的工单事项
    getWorkMatter(type) {
      const params = new URLSearchParams()
      if (type == '6') {
        params.append('type', 'convenience_items')
      } else if (type == '3') {
        params.append('type', 'transport_variety')
      }
      axios.post(__PATH.VUE_IOMS_API + 'iHCRSOperOrderController/getDictListData?' + params.toString(), null).then((res) => {
        if (res.data.code == 200) {
          this.itemTreeData = res.data.data
        }
      })
    },
    // 服务事项
    handleOrderChange(e) {
      this.form.teamCode = ''
      this.form.teamName = ''
      this.form.teamPerson = []
      this.teamUser = []
      if (e && e.length) {
        this.teamsOptions = []
        this.getTeamsByWorkTypeCode(e[0])
        let list = this.$refs.workOrderCascader.getCheckedNodes()
        if (list[0].pathLabels && list[0].pathLabels.length) {
          let workOrderName = list[0].pathLabels
          this.form.workOrderName = workOrderName.join(',')
        }
      }
    },
    // 获取班组
    getTeamsByWorkTypeCode(selectRowId) {
      this.teamsOptions = []
      const params = {
        localtionId: '',
        workTypeCode: this.workType,
        matterId: selectRowId
      }
      if (this.workType == '6' || this.workType == '3') {
        delete params.localtionId
        delete params.workTypeCode
        delete params.matterId
      }
      this.$api.getDataByTypeTeam(params).then((res) => {
        if (res.code === '200') {
          this.teamsOptions = res.data.list
        }
      })
    },
    // 班组change
    teamCodeChange(e) {
      if (e) {
        this.form.teamName = this.teamsOptions.find((ele) => ele.id == e).team_name
        this.getDesignPersonByTeam(e)
      }
    },
    getDesignPersonByTeam(e) {
      let params = {
        id: e
      }
      this.$api.getServicePersonName(params).then((res) => {
        this.teamsPersonOptions = res.data.list
        this.teamsPersonOptions.forEach((item) => {
          item.name = item.member_name + '(' + item.phone + ')'
        })
      })
    },
    // 根据综合服务、运送服务下的事项过滤班组
    handleMatterChange(e) {
      this.form.teamCode = ''
      this.form.teamName = ''
      this.form.teamPerson = []
      this.teamUser = []
      this.getTeamsByWorkTypeCode(e)
      let obj = this.itemTreeData.find((item) => item.id == e)
      this.form.workOrderName = obj.label
    },
    changeAlarmAffirm(val) {
      if (val == 2 || val == 4) {
        this.form.dispatch = 0
        this.handleChangeDispatch()
      }
    },
    // 获取选中的人员list
    teamPersonChange(value) {
      this.teamUser = []
      if (value && value.length) {
        const selectedTeamPersons = this.getSelectedTeamPersons(value)
        selectedTeamPersons.forEach((item) => {
          this.teamUser.push({
            staffId: item.user_id,
            staffName: item.name,
            phone: item.phone,
            id: item.id
          })
        })
      }
    },
    getSelectedTeamPersons(value) {
      return value
        .map((id) => {
          return this.teamsPersonOptions.find((item) => item.id === id)
        })
        .filter((item) => item !== undefined)
    },
    beforeUpload(file) {
      const fileSize = this.uploadAcceptDict['annexGeneralized'].fileSize
      const isLt5M = file.size / 1024 / 1024 < fileSize
      const extension = file.name.substring(file.name.lastIndexOf('.'))
      const isFormat = this.uploadAcceptDict['annexGeneralized'].type.includes(extension)
      if (!isFormat) {
        this.$message.error(`上传图片只能是 ${this.uploadAcceptDict['annexGeneralized'].type}格式!`)
        return false
      }
      if (!isLt5M) {
        this.$message.error(`上传文件大小不能超过 ${fileSize}MB!`)
        return false
      }
    },
    httpRequset(file) {
      const params = new FormData()
      params.append('file', file.file)
      this.$api.uploadTask(params).then((res) => {
        if (res.code === '200') {
          this.$message({
            message: '上传成功',
            type: 'success'
          })
          this.fileList.push({
            name: res.data.name,
            url: res.data.picUrl
          })
        } else {
          this.$message({
            message: res.message,
            type: 'warning'
          })
        }
      })
    },
    handleRemove(file, fileList) {
      this.fileList = fileList
    },
    submit(type) {
      let params = {
        ...this.form,
        operationUrl: JSON.stringify(this.fileList),
        alarmId: this.selectItems.map((el) => el.alarmId).toString(),
        operationSource: 0,
        classic: type,
        teamUser: this.teamUser,
        workOrderCode: this.form.workOrderCode.toString()
      }
      delete params.teamPerson
      // params.workOrderCode = params.workOrderCode.length ? params.workOrderCode[params.workOrderCode.length - 1] : ''
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$api.handleAlarmAffirmById(params).then((res) => {
            if (res.code == 200) {
              this.$message.success('处理成功')
              this.handleClose()
            } else {
              this.$message.error(res.msg || res.message)
            }
          })
        }
      })
    },
    handleClose() {
      this.form = {
        alarmAffirm: '',
        dispatch: 0,
        classic: 0,
        changeTeam: 0,
        workTypeCode: '', // 工单类型id
        workTypeName: '', // 工单类型name
        workOrderName: '', // 工单事项名称
        workOrderCode: '', // 工单事项编码
        teamCode: '', // 班组code
        teamName: '', // 班组名称
        teamPerson: [] // 班组成员
      }
      this.fileList = []
      this.$refs.ruleForm.resetFields()
      this.$emit('close')
    },
    // 扫码上传
    scanUpload() {
      let params = {
        alarmId: this.selectItems.map((el) => el.alarmId).toString()
      }
      this.$api.createScan(params).then((res) => {
        if (res.code == 200) {
          this.qrCodeImg = `data:image/png;base64,${res.data}`
        }
        if (!this.webSocket) {
          this.webSocket = this.fileUploadSocket()
          this.webSocket.onmessage = this.onUploadSocketMessage.bind(this)
          this.webSocket.onerror = () => {
            this.$message.error('二维码上传交互服务异常')
          }
        }
        this.scanVisible = true
      })
    },
    /** 报警详情，二维码扫码上传文件后消息 */
    fileUploadSocket() {
      return new WebSocket(`${__PATH.WS_ALARM_URL}alarmServer/wartime/${this.selectItems.map((el) => el.alarmId).toString()}`)
    },
    onUploadSocketMessage(msg) {
      let arr = JSON.parse(msg.data)
      this.fileList = this.fileList.concat(
        arr.data.map((el) => {
          return {
            name: el.operationName,
            url: el.operationUrl
          }
        })
      )
    },
    closeScanDialog() {
      this.scanVisible = false
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-drawer__header {
  padding: 0 16px;
  height: 56px;
  border-bottom: 1px solid #ccc;
  color: #333;
  font-weight: 500;
  font-size: 16px;
  margin: 0;
}
.drawer_content {
  padding: 16px;
  width: 100%;
  height: calc(100% - 56px);
  overflow: auto;
}
.drawer_footer {
  padding: 0 16px;
  height: 56px;
  border-top: 1px solid #ccc;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.qrCode {
  width: 100%;
  height: 400px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #fff;
  flex-direction: column;
}
.el-form-item {
  margin-bottom: 16px;
}
</style>
