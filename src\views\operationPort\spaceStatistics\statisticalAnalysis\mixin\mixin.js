export default {
  methods: {
    // 柱状图
    getBarChartStatus(data, unit) {
      let option
      if (data.length) {
        option = {
          tooltip: {
            trigger: 'item'
          },
          grid: {
            top: '4%',
            left: '4%',
            right: '5%',
            bottom: '5%',
            containLabel: true
          },
          dataZoom: [
            {
              type: 'slider',
              realtime: true,
              startValue: 0,
              endValue: 5,
              height: 4,
              fillerColor: 'rgba(17, 100, 210, 0.42)', // 滚动条颜色
              borderColor: 'rgba(17, 100, 210, 0.12)',
              handleSize: 0, // 两边手柄尺寸
              showDetail: false, // 拖拽时是否展示滚动条两侧的文字
              top: '96%',
              zoomLock: true // 是否只平移不缩放
              // moveOnMouseMove:true, //鼠标移动能触发数据窗口平移
              // zoomOnMouseWheel :true, //鼠标移动能触发数据窗口缩放
            },
            {
              type: 'inside', // 支持内部鼠标滚动平移
              start: 0,
              end: 40,
              zoomOnMouseWheel: false, // 关闭滚轮缩放
              moveOnMouseWheel: true, // 开启滚轮平移
              moveOnMouseMove: true // 鼠标移动能触发数据窗口平移
            }
          ],
          xAxis: {
            type: 'category',
            data: data.map(ele => ele.name),
            nameLocation: 'start',
  
            axisLabel: {
              interval: 0,
              formatter(val) {
                if (val.length > 5) {
                  return val.slice(0, 4) + '...'
                } else {
                  return val
                }
              }
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            }
          },
          yAxis: [
            {
              type: 'value'
            }
          ],
          series: [
            {
              type: 'bar',
              barWidth: 10,
              data: data,
              itemStyle: {
                color: '#00BC6D'
              }
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      return option
    },
    // 饼图
    getSpaceStatus(data, unit) {
      let option
      let position, center
      if (this.choiceTypeTab == 4) {
        position = {
          top: 'center',
          right: '30%'
        }
        center = ['45%', '50%']
      } else {
        position = {
          top: '50',
          right: '10%'
        }
        center = ['25%', '50%']
      }
      // let color = ['#FF8352', '#0E7CE2']
      if (data.length) {
        option = {
          customData: data,
          backgroundColor: '#fff',
          // color: colors,
          title: [],
          tooltip: {
            show: true,
            formatter: '{b}: {c}' + unit + '<br/>百分比: {d}%'
          },
          legend: {
            type: 'scroll',  // 开启滚动功能
            scrollDataIndex: 0,  // 设置需要滚动的系列的数据索引
            orient: 'vertical',
            ...position,
            itemHeight: 8,
            itemWidth: 8,
            // formatter: `{name|{u}}{value|{i}${unit}}{value|{o}%}`,
            formatter: name => {
              let value
              let rate
              let num = data.reduce((sum, e) => sum + e.value, 0)
              for (let i = 0; i < data.length; i++) {
                if (data[i].name == name) {
                  value = data[i].value
                  rate = Math.round((value / num) * 10000) / 100.0 + '%'
                }
              }
              let arr = `{name|${name}}(${value}${unit}){rate|${rate}}`
              return arr
            },
            textStyle: {
              rich: {
                name: {
                  fontSize: 14,
                  padding: [0, 10, 0, 0]
                },
                rate: {
                  fontSize: 12,
                  color: '#CCCED3',
                  padding: [0, 0, 0, 5]
                }
              }
            }
          },
          series: [
            {
              type: 'pie',
              radius: '90%',
              center: center,
              data: data,
              labelLine: {
                show: false
              },
              label: {
                show: false,
                position: 'center'
              }
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      return option
    }
  }
}