<template>
  <el-dialog
    class="component form-base"
    custom-class="model-dialog"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :title="handle == 'add' ? `新增证照` : '编辑证照'"
    :visible="visible"
    width="50%"
    :before-close="close"
  >
    <div class="form-content">
      <el-form ref="form" :model="form" :rules="rules" :disabled="handle == 'view'" label-width="120px">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="证照名称" prop="fileName">
              <el-input v-model="form.fileName" placeholder="请输入证照名称" maxlength="50"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="证照编码" prop="fileCode">
              <el-input v-model="form.fileCode" placeholder="请输入证照编码" :disabled="['edit', 'view'].includes(handle)" maxlength="50"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否必填" prop="writed">
              <el-select v-model="form.writed" placeholder="请选择">
                <el-option label="否" :value="0"></el-option>
                <el-option label="是" :value="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-select v-model="form.enableStatus" placeholder="请选择状态">
                <el-option label="启用" :value="1"></el-option>
                <el-option label="停用" :value="0"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" maxlength="200" show-word-limit></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div slot="footer">
      <el-button @click="close">取 消</el-button>
      <el-button type="primary" v-if="handle !== 'view'" :loading="buttonLoading" @click="submit">确 定</el-button>
    </div>
  </el-dialog>
</template>
    <script>
export default {
  model: {
    prop: 'visible',
    event: 'update:visible'
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    handle: {
      type: String,
      default: 'add'
    },
    detail: {
      type: [Object, null],
      default: null
    }
  },
  data() {
    return {
      buttonLoading: false,
      form: {
        fileName: '',
        fileCode: '',
        writed: '',
        enableStatus: 1,
        remark: '',
        initialed: 2
      },
      rules: {
        fileName: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        fileCode: [{ required: true, message: '请输入编码', trigger: 'blur' }],
        writed: [{ required: true, message: '请选择是否必填', trigger: 'change' }]
      },
      projectList: [],
      flowNodeKeyList: []
    }
  },
  mounted() {
    if (this.detail) {
      this.form = {
        id: this.detail.id,
        fileName: this.detail.fileName,
        fileCode: this.detail.fileCode,
        initialed: this.detail.initialed,
        writed: this.detail.writed,
        enableStatus: this.detail.enableStatus,
        remark: this.detail.remark
      }
    }
  },
  methods: {
    close() {
      this.buttonLoading = false
      this.$emit('update:visible', false)
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          let fn = this.form.id ? 'editConstructionCertificate' : 'addConstructionCertificate'
          this.buttonLoading = true
          this.$api[fn]({ ...this.form, type: 1 })
            .then((res) => {
              if (res.code == 200) {
                this.$message.success('添加成功')
                this.$emit('success')
                this.close()
              } else {
                this.$message.error(res.message)
              }
            })
            .catch(() => {})
            .finally(() => {
              this.buttonLoading = false
            })
        }
      })
    }
  }
}
</script>
    <style lang="scss" scoped>
.el-form {
  background-color: #fff;
  padding: 10px 16px 0;
  .el-form-item {
    .el-select {
      width: 100%;
    }
  }
}
</style>
    