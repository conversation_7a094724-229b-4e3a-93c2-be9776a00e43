<template>
  <PageContainer>
    <div slot="header">
      <div class="searchForm">
        <div class="search-box">
          <el-input v-model="filterInfo.usrName" placeholder="请输入终端名称"></el-input>
          <el-input v-model="filterInfo.extensionNum" placeholder="请输入号码" class="ml-16"></el-input>
          <el-select v-model="filterInfo.onlineState" filterable placeholder="状态" class="ml-16">
            <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
          <el-select v-model="filterInfo.usrType" filterable placeholder="类型" class="ml-16">
            <el-option v-for="item in typeList" :key="item.code" :label="item.name" :value="item.code">
            </el-option>
          </el-select>
          <div class="ml-16">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button type="primary" plain @click="reset">重置</el-button>
          </div>
        </div>
      </div>
    </div>
    <div slot="content" ref="contentRef" class="table-box">
      <div class="tableContainer">
        <el-button type="primary" style="margin-bottom: 12px" @click="handleListEvent('add')">新增</el-button>
        <el-button type="primary" style="margin-bottom: 12px" :disabled="multipleSelection.length < 1  "
          @click="handleListEvent('delete')">删除</el-button>
        <el-table v-loading="tableLoading" height="calc(100% - 96px)" :data="tableData" border stripe
          @selection-change="selectionChange">
          <el-table-column type="selection" width="55"> </el-table-column>
          <el-table-column prop="usrName" label="终端名称" width="180" align="center"></el-table-column>
          <el-table-column prop="extensionModel" label="型号" align="center"></el-table-column>
          <el-table-column prop="extensionNum" label="号码" align="center"></el-table-column>
          <el-table-column prop="usrType" label="类型" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.usrType | filterType }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="onlineState" label="状态" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.onlineState=='-1'">-</span>
              <span v-if="scope.row.onlineState=='0'" class="offOnline">离线</span>
              <span v-if="scope.row.onlineState=='1'" class="online">在线</span>
            </template>
          </el-table-column>
          <el-table-column label="通讯状态" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.callState=='-1'">-</span>
              <span v-if="scope.row.callState=='0'" class="online">空闲</span>
              <span v-if="scope.row.callState=='1'" class="offOnline">通话中</span>
              <span v-if="scope.row.callState=='2'" class="exhale">振铃中</span>
            </template>
          </el-table-column>
          <el-table-column width="150" label="操作" align="center">
            <template slot-scope="scope">
              <el-button type="text" @click="handleOperation('detail', scope.row)">详情</el-button>
              <el-button type="text" @click="handleOperation('edit', scope.row)">编辑</el-button>
              <el-button type="text" @click="handleOperation('delete',scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination :current-page="pagination.pageNum" :page-sizes="[15, 30, 50, 100]"
          :page-size="pagination.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="pageTotal"
          @size-change="handleSizeChange" @current-change="handleCurrentChange">
        </el-pagination>
      </div>
      <el-dialog v-dialogDrag :visible.sync="dialogVisible" :before-close="dialogClosed" :modal="false"
        :close-on-click-modal="false" :title="diaTitle" width="40%" custom-class="model-dialog">
        <div class="diaContent" style="padding: 10px">
          <el-form ref="formInline" :model="formInline" label-width="100px" :rules="rules" label-position="right">
            <el-form-item label="设备类型" prop="usrType">
              <el-select v-model="formInline.usrType" filterable placeholder="请选择类型" :disabled="isForbid" clearable>
                <el-option v-for="item in typeList" :key="item.code" :label="item.name" :value="item.code">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="终端名称" prop="usrName">
              <el-input v-model="formInline.usrName" placeholder="请输入终端名称" :disabled="isForbid"></el-input>
            </el-form-item>
            <el-form-item label="设备型号">
              <el-input v-model="formInline.extensionModel" :disabled="isForbid"
                :placeholder="isForbid?'':'请输入设备型号'"></el-input>
            </el-form-item>
            <el-form-item prop="extensionSuffixNum">
              <span slot="label"> 号码：{{ numberPrefix }} </span>
              <el-input v-model="formInline.extensionSuffixNum" :disabled="isForbid" placeholder="请输入对讲机号码"></el-input>
            </el-form-item>
            <el-form-item label="登录密码" prop="sipPassword">
              <el-input v-model="formInline.sipPassword" :disabled="isForbid" placeholder="请输入登录密码"></el-input>
            </el-form-item>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="dialogClosed">取 消</el-button>
          <el-button v-if="diaTitle!=='终端详情'" type="primary" @click="submit('formInline')">确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </PageContainer>
</template>
<script>
export default {
  name: 'terminalManagement',
  filters: {
    filterType(val) {
      return val == '2'
        ? '智能调度终端'
        : val == '3'
          ? '移动调度APP'
          : val == '1'
            ? '调度台'
            : val == '4'
              ? '数字对讲网关'
              : '电话网关'
    }
  },
  data() {
    return {
      numberPrefix: '332',
      tableLoading: false,
      dialogVisible: false,
      tableData: [],
      filterInfo: {
        usrName: '',
        extensionNum: '',
        onlineState: '',
        usrType: ''
      },
      statusList: [
        {
          value: -1,
          label: '无状态'
        },
        {
          value: 0,
          label: '离线'
        },
        {
          value: 1,
          label: '在线'
        }
      ],
      typeList: [],
      pagination: {
        pageSize: 15,
        pageNum: 1
      },
      pageTotal: 0,
      formInline: {
        usrType: '',
        usrName: '',
        extensionModel: '',
        extensionSuffixNum: '',
        sipPassword: ''
      },
      diaTitle: '',
      multipleSelection: [],
      rules: {
        usrType: [
          { required: true, message: '请选择设备类型', trigger: 'change' }
        ],
        usrName: [
          { required: true, message: '请输入终端名称', trigger: 'blur' }
        ],
        extensionSuffixNum: [
          { required: true, message: '请输入对讲机号码', trigger: 'blur' },
          { min: 1, max: 5, message: '号码总长度不能超过8个字符', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (!value) {
                return callback()
              }
              if (!/^[0-9]*$/.test(value)) {
                callback(new Error('只能输入数字'))
              } else {
                callback()
              }
            }, trigger: 'blur'
          }
        ],
        sipPassword: [
          { required: true, message: '请输入登录密码', trigger: 'blur' }
        ]
      },
      isForbid: false
    }
  },
  mounted() {
    this.getTypeList()
    this.getTableData()
  },
  methods: {
    getTypeList() {
      this.$api.getTypeList({}).then((res) => {
        if (res.code === '200') {
          this.typeList = res.data
        }
      })
    },
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.pagination.pageNum = 1
      this.getTableData()
    },
    getTableData() {
      this.tableLoading = true
      let data = {
        ...this.pagination,
        ...this.filterInfo
      }
      this.$api
        .searchTerminalList(data)
        .then((res) => {
          this.tableLoading = false
          if (res.code == 200) {
            this.tableData = res.data ? res.data.records : []
            this.pageTotal = res.data ? res.data.total : 0
          } else if (res.message) {
            this.tableData = []
            this.pageTotal = 0
            this.$message.error(res.message)
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    search() {
      this.pagination.pageNum = 1
      this.getTableData()
    },
    reset() {
      this.filterInfo = {
        usrName: '',
        extensionNum: '',
        onlineState: '',
        usrType: ''
      }
      this.pagination.pageNum = 1
      this.pageTotal = 0
      this.getTableData()
    },
    handleCurrentChange(val) {
      this.pagination.pageNum = val
      this.getTableData()
    },
    // 表格操作
    handleOperation(type, row) {
      if (type === 'detail') {
        this.formInline = JSON.parse(JSON.stringify(row))
        this.isForbid = true
        this.dialogVisible = true
        this.diaTitle = '终端详情'
      } else if (type === 'edit') {
        this.diaTitle = '编辑终端'
        this.formInline = JSON.parse(JSON.stringify(row))
        this.dialogVisible = true
        this.isForbid = false
      } else {
        this.$confirm(`是否删除${row.usrName}终端?`, '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let ids = []
          ids.push(row.id)
          this.$api.deleteTerminalList({ ids: ids }, { 'operation-type': 3, 'operation-name': row.usrName, 'operation-id': row.id }).then((res) => {
            if (res.code === '200') {
              this.$message.success(res.msg)
              this.getTableData()
            } else {
              this.$message.error(res.msg)
            }
          })
        })
      }
    },
    handleListEvent(type) {
      if (type == 'add') {
        this.diaTitle = '新增终端'
        this.dialogVisible = true
        this.isForbid = false
        this.formInline = {
          usrType: '',
          usrName: '',
          extensionModel: '',
          extensionSuffixNum: '',
          sipPassword: ''
        }
        this.$nextTick(() => {
          this.$refs['formInline'].clearValidate()
        })
      } else if (type == 'delete') {
        this.$confirm('是否删除所选终端?', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          if (this.multipleSelection.length) {
            let ids = []
            this.multipleSelection.map((item) => {
              ids.push(item.id)
            })
            this.$api.deleteTerminalList({ ids: ids }, { 'operation-type': 3 }).then((res) => {
              if (res.code === '200') {
                this.$message.success(res.msg)
                this.getTableData()
              } else {
                this.$message.error(res.msg)
              }
            })
          }
        })
      }
    },
    // 选中框
    selectionChange(val) {
      this.multipleSelection = val
    },
    // 确定
    submit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let data = {
            ...this.formInline,
            usrDepUuid: '6de6f162-c190-4f99-affa-9896878d0c4a',
            extensionPrefixNum: this.numberPrefix,
            extensionNum:
              this.numberPrefix + this.formInline.extensionSuffixNum
          }
          if (data.id) {
            this.$api.editTerminal(data, { 'operation-type': 2, 'operation-id': data.id, 'operation-name': data.usrName }).then((res) => {
              if (res.code === '200') {
                this.$message.success(res.msg)
                this.getTableData()
              } else {
                this.$message.error(res.msg)
              }
            })
          } else {
            this.$api.addTerminal(data, { 'operation-type': 1 }).then((res) => {
              if (res.code === '200') {
                this.$message.success(res.msg)
                this.getTableData()
              } else {
                this.$message.error(res.msg)
              }
            })
          }
          this.dialogVisible = false
          this.$refs.formInline.resetFields()
        } else {
          return false
        }
      })

    },
    // 取消
    dialogClosed() {
      this.dialogVisible = false
      this.formInline = {
        usrType: '',
        usrName: '',
        extensionModel: '',
        extensionSuffixNum: '',
        sipPassword: ''
      }
      this.$nextTick(() => {
        this.$refs['formInline'].clearValidate()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.searchForm {
  display: flex;
  height: 80px;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  .el-input {
    width: 200px;
    margin-left: 16px;
  }
}

.search-box {
  display: flex;
  align-items: center;
}

.container-content > div {
  height: 100%;
  margin-top: 16px;
}
.el-table {
  margin-bottom: 12px;
}

::v-deep .el-table__row:hover {
  cursor: pointer;
}
.table-box {
  background-color: #fff;
  height: 100%;
  padding: 16px;
  .tableContainer {
    height: 100%;
    .el-table {
      height: calc(100% - 96px) !important;
    }
  }
}
.diaContent {
  width: 100%;
  max-height: 500px !important;
  overflow: auto;
  background-color: #fff !important;
  .el-input,
  .el-select,
  .el-cascader {
    width: 300px;
  }
}
.ml-16 {
  margin-left: 16px;
}
.dialog .el-dialog {
  width: 60% !important;
}
.online {
  color: #33cc00;
}
.offOnline {
  color: #ff0000;
}
.exhale {
  color: #ff9900;
}
</style>
