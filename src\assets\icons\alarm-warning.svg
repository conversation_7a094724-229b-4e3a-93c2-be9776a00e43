<svg width="46" height="55" viewBox="0 0 46 55" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#231;&#131;&#159;" clip-path="url(#clip0_7_2212)">
<g id="&#230;&#164;&#173;&#229;&#156;&#134;&#229;&#189;&#162;&#229;&#164;&#135;&#228;&#187;&#189; 12" filter="url(#filter0_f_7_2212)">
<ellipse cx="23.1379" cy="23.6267" rx="12.0259" ry="12.007" fill="url(#paint0_linear_7_2212)"/>
</g>
<g id="&#231;&#155;&#190;&#231;&#137;&#140;" opacity="0.5">
<path id="&#232;&#183;&#175;&#229;&#190;&#132;" d="M22.75 0L43.54 6.93C44.5614 7.27018 45.2503 8.22598 45.25 9.3025V27.5C45.25 37.625 37.75 46.7925 22.75 55C7.75 46.7925 0.25 37.625 0.25 27.5V9.3C0.250817 8.2244 0.939516 7.26989 1.96 6.93L22.75 0Z" fill="url(#paint1_linear_7_2212)"/>
<mask id="mask0_7_2212" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="46" height="55">
<path id="&#232;&#183;&#175;&#229;&#190;&#132;_2" d="M22.75 0L43.54 6.93C44.5614 7.27018 45.2503 8.22598 45.25 9.3025V27.5C45.25 37.625 37.75 46.7925 22.75 55C7.75 46.7925 0.25 37.625 0.25 27.5V9.3C0.250817 8.2244 0.939516 7.26989 1.96 6.93L22.75 0Z" fill="white"/>
</mask>
<g mask="url(#mask0_7_2212)">
<rect id="&#231;&#159;&#169;&#229;&#189;&#162;" x="22.75" y="-4.375" width="16.875" height="71.25" fill="url(#paint2_linear_7_2212)"/>
</g>
</g>
<ellipse id="&#230;&#164;&#173;&#229;&#156;&#134;&#229;&#189;&#162;" opacity="0.2" cx="23.1379" cy="23.6267" rx="17.069" ry="17.0423" fill="url(#paint3_linear_7_2212)"/>
<ellipse id="&#230;&#164;&#173;&#229;&#156;&#134;&#229;&#189;&#162;&#229;&#164;&#135;&#228;&#187;&#189; 7" cx="23.1379" cy="23.6268" rx="13.9655" ry="13.9437" fill="url(#paint4_linear_7_2212)"/>
<path id="Union" fill-rule="evenodd" clip-rule="evenodd" d="M22.3925 17.1346V16.0166H23.8831V17.1346H22.3925ZM18.6717 22.7371C18.6717 20.672 20.6739 18.9979 23.1436 18.9979C25.6133 18.9979 27.6154 20.672 27.6154 22.7371V27.7228C27.6154 27.7275 27.6154 27.7322 27.6152 27.7368H29.7242V29.2368H16.7258V27.7368H18.6719C18.6718 27.7322 18.6717 27.7275 18.6717 27.7228V22.7371ZM27.0065 17.1504L26.2879 18.0069L27.4298 18.965L28.1484 18.1086L27.0065 17.1504ZM28.6896 21.1737L29.7906 20.9796L30.0495 22.4475L28.9485 22.6417L28.6896 21.1737ZM16.2262 22.4473L17.3272 22.6414L17.586 21.1734L16.485 20.9793L16.2262 22.4473ZM18.875 18.965L18.1564 18.1086L19.2983 17.1504L20.0169 18.0069L18.875 18.965Z" fill="#0E1134"/>
</g>
<defs>
<filter id="filter0_f_7_2212" x="-30.8807" y="-30.3731" width="108.037" height="108" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="20.9964" result="effect1_foregroundBlur_7_2212"/>
</filter>
<linearGradient id="paint0_linear_7_2212" x1="11.1121" y1="11.6196" x2="11.1121" y2="35.6337" gradientUnits="userSpaceOnUse">
<stop offset="0.000818639" stop-color="#FF5869"/>
<stop offset="1" stop-color="#E72E5B"/>
</linearGradient>
<linearGradient id="paint1_linear_7_2212" x1="45.25" y1="55.1013" x2="45.4977" y2="0.101713" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF5159" stop-opacity="0.01"/>
<stop offset="1" stop-color="#FF5959" stop-opacity="0.505685"/>
</linearGradient>
<linearGradient id="paint2_linear_7_2212" x1="39.625" y1="-9.52893" x2="22.75" y2="-9.52893" gradientUnits="userSpaceOnUse">
<stop stop-color="#FD4353" stop-opacity="0.01"/>
<stop offset="1" stop-color="#FF6D79" stop-opacity="0.224363"/>
</linearGradient>
<linearGradient id="paint3_linear_7_2212" x1="6.06897" y1="6.58447" x2="6.06897" y2="40.669" gradientUnits="userSpaceOnUse">
<stop offset="0.000818639" stop-color="#FF5869"/>
<stop offset="1" stop-color="#E72E5B"/>
</linearGradient>
<linearGradient id="paint4_linear_7_2212" x1="9.17236" y1="9.6831" x2="9.17236" y2="37.5704" gradientUnits="userSpaceOnUse">
<stop offset="0.000818639" stop-color="#FF5869"/>
<stop offset="1" stop-color="#E72E5B"/>
</linearGradient>
<clipPath id="clip0_7_2212">
<rect width="45" height="55" fill="white" transform="translate(0.25)"/>
</clipPath>
</defs>
</svg>
