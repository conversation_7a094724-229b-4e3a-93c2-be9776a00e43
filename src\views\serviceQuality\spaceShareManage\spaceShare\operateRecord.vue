<!-- 操作记录 -->
<template>
  <PageContainer>
    <div slot="content" class="form-content">
      <div class="form-title"><i class="el-icon-arrow-left" @click="$router.go(-1)"></i>{{ $route.query.name }}</div>
      <div class="content-main">
        <div class="search-from">
          <el-input v-model.trim="queryForm.keywords" placeholder="操作人/操作类型/操作前后" style="width: 300px" suffix-icon="el-icon-search" clearable></el-input>
          <el-select v-model="queryForm.operationType" placeholder="操作类型">
            <el-option label="新增" value="0" />
            <el-option label="修改" value="1" />
          </el-select>
          <el-date-picker v-model="dataRange" type="daterange" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd">
          </el-date-picker>
          <div style="display: inline-block">
            <el-button type="primary" plain @click="resetForm">重置</el-button>
            <el-button type="primary" @click="searchForm">查询</el-button>
          </div>
        </div>
        <TablePage
          ref="table"
          v-loading="tableLoading"
          :showPage="true"
          :tableColumn="tableColumn"
          :data="tableData"
          height="calc(100% - 115px)"
          :pageData="pageData"
          @pagination="paginationChange"
        >
        </TablePage>
      </div>
    </div>
  </PageContainer>
</template>
<script lang="jsx">
export default {
  name: 'operateRecord',
  data() {
    return {
      queryForm: {
        keywords: '',
        operationType: ''
      },
      dataRange: [],
      tableLoading: false,
      tableData: [],
      tableColumn: [
        {
          prop: 'operationTime',
          label: '操作时间'
        },
        {
          prop: 'operationUserName',
          label: '操作人'
        },
        {
          prop: 'operationType',
          label: '操作类型',
          render: (h, row) => {
            return <span>{row.row.operationType == '0' ? '新增' : '修改'}</span>
          }
        },
        {
          prop: 'operationItem',
          label: '操作项'
        },
        {
          prop: 'operationBefore',
          label: '操作前'
        },
        {
          prop: 'operationAfter',
          label: '操作后'
        }
      ],
      pageData: {
        page: 1,
        pageSize: 15,
        total: 0
      }
    }
  },
  computed: {},
  watch: {},
  created() {
    this.getOperationRecordPage()
  },
  methods: {
    getOperationRecordPage() {
      let params = {
        ...this.queryForm,
        sssId: this.$route.query.id,
        startDate: this.dataRange[0],
        endDate: this.dataRange[1],
        page: this.pageData.page,
        pageSize: this.pageData.pageSize
      }
      this.$api.GetOperationRecordPage(params).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.records
          this.pageData.total = res.data.total
        }
      })
    },
    // 查询
    searchForm() {
      this.pageData.page = 1
      this.getOperationRecordPage()
    },
    // 重置查询
    resetForm() {
      this.dataRange = []
      Object.assign(this.$data.queryForm, this.$options.data().queryForm)
      this.searchForm()
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getOperationRecordPage()
    }
  }
}
</script>
<style lang="scss" scoped>
.form-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: hidden;
  p {
    margin: 0px;
  }
  .form-title {
    height: 40px;
    line-height: 40px;
    background: #fff;
    border-bottom: 1px solid #dcdfe6;
    font-size: 15px;
    font-family: 'PingFang SC-Medium', 'PingFang SC';
    font-weight: 500;
    color: #121f3e;
    padding-left: 15px;
    i {
      width: 20px;
      cursor: pointer;
    }
  }
  .content-main {
    flex: 1;
    width: 100%;
    background: #fff;
    padding: 14px 24px 0px;
    ::v-deep .search-from {
      margin-bottom: 24px;
      & > div {
        margin-top: 10px;
        margin-right: 10px;
      }
      .el-date-editor {
        .el-range__icon,
        .el-range__close-icon {
          line-height: 25px !important;
        }
      }
    }
  }
}
</style>
<style lang="scss">
.operationBtn {
  .is-disabled {
    color: #dcdfe6 !important;
  }
}
</style>
