<template>
  <PageContainer>
    <div slot="header">
      <div class="searchForm">
        <div class="search-box">
          <div class="btns">
            <div :class="['date-btn', dateType == 'day' ? 'active-btn' : '']" @click="changeDateType('day')">今日</div>
            <div :class="['date-btn', dateType == 'month' ? 'active-btn' : '']" @click="changeDateType('month')">本月</div>
            <div :class="['date-btn', dateType == 'year' ? 'active-btn' : '']" @click="changeDateType('year')">本年</div>
            <div :class="['date-btn', dateType == 'all' ? 'active-btn' : '']" @click="changeDateType('all')">全部</div>
          </div>
        </div>
      </div>
      <div class="form-box">
        <el-cascader
          v-model="officeId"
          :options="deptOptions"
          :props="{ expandTrigger: 'hover', checkStrictly: true, label: 'deptName', value: 'id' }"
          placeholder="所属科室"
          clearable
          filterable
          @change="handleChange"
        ></el-cascader>
        <el-input v-model="rfidCode" placeholder="请输入编码"></el-input>
        <el-select v-model="flag" placeholder="是否出库">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
        </el-select>
        <el-date-picker
          v-model="dateArr"
          type="datetimerange"
          range-separator="至"
          start-placeholder="入站时间"
          end-placeholder="入站时间"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
        >
        </el-date-picker>
        <div style="margin-left: 16px">
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button type="primary" plain @click="reset">重置</el-button>
        </div>
      </div>
    </div>
    <div slot="content" ref="contentRef">
      <div class="cards">
        <div class="left-card">
          <img src="@/assets/images/operationPort/waste-icon2.png" />
          <span>入站重量</span>
          <span>{{ sumData.gatherWeigh }}</span>
          <span>kg</span>
        </div>
        <div class="right-card">
          <img src="@/assets/images/operationPort/waste-icon1.png" />
          <span>入站数量</span>
          <span>{{ sumData.count }}</span>
          <span>袋（件）</span>
        </div>
      </div>
      <div class="table-box">
        <el-table v-loading="tableLoading" :data="tableData" border style="width: 100%" height="90%">
          <el-table-column label="转运箱（桶）编码" prop="rfidCode" :resizable="false" align="center"></el-table-column>
          <el-table-column label="医废类型" prop="wasteType" width="180" :resizable="false" align="center"></el-table-column>
          <el-table-column label="入站时间" prop="date" width="180" :resizable="false" align="center"></el-table-column>
          <el-table-column label="医废袋（件）数量" prop="count" width="180" :resizable="false" align="center"></el-table-column>
          <el-table-column label="重量" prop="gatherWeigh" width="180" :resizable="false" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.gatherWeigh }}kg</span>
            </template>
          </el-table-column>
          <el-table-column label="入站人员" prop="stationCode" width="180" :resizable="false" align="center"></el-table-column>
          <el-table-column label="入库人员签字" width="120" :resizable="false" align="center">
            <template slot-scope="scope">
              <span style="color: #5188fc; cursor: pointer" @click="showPicture(scope.row)">查看图片</span>
            </template>
          </el-table-column>
          <el-table-column label="是否出站" prop="flag" width="120" :resizable="false" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.flag == 1">是</span>
              <span v-else style="color: red">否</span>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :current-page="pageNo"
          :page-sizes="[15, 30, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
        <imgCarousel :dialogVisibleImg="dialogVisibleImg" :imgArr="imgArr" @closeDialog="closeDialogImg"></imgCarousel>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import store from '@/store/index'
import imgCarousel from '../spaceManage/common/imgCarousel.vue'
import { transData } from '@/util'
export default {
  name: 'inboundRecord',
  components: {
    imgCarousel
  },
  data() {
    return {
      dateType: 'day',
      dateVal: '',
      tableLoading: false,
      tableData: [],
      pageNo: 1,
      pageSize: 15,
      total: 0,
      dateArr: [],
      rfidCode: '',
      flag: '',
      options: [
        {
          value: '1',
          label: '是'
        },
        {
          value: '0',
          label: '否'
        }
      ],
      deptOptions: [],
      officeId: '',
      imgArr: [],
      dialogVisibleImg: false,
      sumData: {
        gatherWeigh: 0,
        count: 0
      }
    }
  },
  watch: {
    // 监听dateArr,如果有值，则dateType为all
    dateArr: {
      handler(val) {
        if (val.length > 0) {
          this.dateType = 'all'
        }
      },
      deep: true
    }
  },
  created() {},
  mounted() {
    this.getTableData()
    this.getSum()
    this.getDeptList()
  },
  methods: {
    reset() {
      this.dateType = 'day'
      this.dateArr = []
      this.rfidCode = ''
      this.flag = ''
      this.officeId = ''
      this.getTableData()
      this.getSum()
    },
    handleSearch() {
      this.pageNo = 1
      this.getTableData()
      this.getSum()
    },
    changeDateType(type) {
      this.dateType = type
      this.getTableData()
      this.getSum()
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.pageNo = 1
      this.getTableData()
    },
    getTableData() {
      this.tableLoading = true
      let params = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        dateType: this.dateType,
        beginDate: this.dateArr[0],
        endDate: this.dateArr[1],
        officeId: this.officeId[this.officeId.length - 1],
        rfidCode: this.rfidCode,
        flag: this.flag
      }
      this.$api.getInboundRecordList(params).then((res) => {
        this.tableLoading = false
        this.tableData = res.data.rows
        this.total = res.data.total
      })
    },
    handleCurrentChange(val) {
      this.pageNo = val
      this.getTableData()
    },
    goManualEntry() {
      this.$router.push({ path: '/wasteManage/inoutManage/manualEntry' })
    },
    handleChange() {
      console.log(this.officeId)
    },
    showPicture(row) {
      if (row.handoverSignature) {
        // let str = row.ossFilePrefix + row.handoverSignature
        let str = this.$tools.imgUrlTranslation(row.handoverSignature)
        this.imgArr.push(str)
        this.dialogVisibleImg = true
      } else {
        this.$message.error('无收集人员签名')
      }
    },
    closeDialogImg() {
      this.dialogVisibleImg = false
      this.imgArr = []
    },
    getSum() {
      let params = {
        dateType: this.dateType,
        beginDate: this.dateArr[0],
        endDate: this.dateArr[1],
        officeId: this.officeId[this.officeId.length - 1],
        rfidCode: this.rfidCode,
        flag: this.flag
      }
      this.$api.inboundDataSum(params).then((res) => {
        this.sumData = res.data
      })
    },
    // 获取部门列表
    getDeptList() {
      this.$api.getSelectedDept({}).then((res) => {
        if (res.code == 200) {
          this.deptOptions = transData(res.data, 'id', 'pid', 'children')
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.searchForm {
  display: flex;
  height: 60px;
  padding: 0 16px;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .el-input {
    width: 200px;
  }

  > div {
    margin-right: 20px;
  }
}

.search-box {
  display: flex;
  align-items: center;
}

.container-content > div {
  height: 100%;
  margin-top: 16px;
}

.el-table {
  margin-bottom: 12px;
}

::v-deep .el-date-editor .el-range__icon {
  line-height: 24px;
}

.statistics {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
  height: 78px;
}

.statistics .item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-around;
  flex: 1;
  background-color: rgb(53 98 219 / 6%);
  margin-right: 8px;
  border-radius: 4px;
}

.statistics .item span:nth-child(2) {
  font-size: 18px;
  color: #3562db;
  font-weight: 700;
}

.statistics .hover-style {
  background-color: #fff;
  box-shadow: 0 0 10px 0 rgb(32 56 114 / 13%);
  border-bottom: 3px solid #3562db;
}

.statistics .pointer-style {
  cursor: pointer;
}

.statistics .pure {
  background-color: #fff;
}

.statistics .pure span:nth-child(2) {
  color: #121f3e;
}

::v-deep .el-dialog {
  height: 80vh;
  margin-top: 10vh;
}

::v-deep .el-dialog__wrapper {
  overflow: hidden;
}

::v-deep .el-dialog__body {
  height: 93%;
}

::v-deep .el-table__row:hover {
  cursor: pointer;
}

.btns {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.btns .date-btn {
  width: 100px;
  height: 32px;
  border: 1px solid #3562db;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  color: #3562db;
  margin-right: 12px;
  cursor: pointer;
}

.active-btn {
  background-color: #3562db;
  color: #fff !important;
}

.cards {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.cards > div {
  width: 49%;
  background-color: #fff;
  border-radius: 4px;
  height: 90px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.cards img {
  width: 45px;
  height: 45px;
}

.cards span:nth-child(2) {
  margin: 0 16px;
}

.cards span:nth-child(3) {
  font-size: 28px;
  font-weight: 700;
}

.cards span:nth-child(4) {
  font-size: 14px;
  color: #ccced3;
  margin-left: 5px;
  transform: translateY(3px);
}

.table-box {
  background-color: #fff;
  height: 63vh;
  padding: 16px;
}

.form-box {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding-left: 16px;
}

.form-box .el-input {
  width: 200px !important;
  margin: 0 16px;
}

.el-range-editor.el-input__inner {
  margin-left: 16px;
}
</style>
