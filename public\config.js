/*
 * @Author: hedd <EMAIL>
 * @Date: 2025-06-17 09:42:06
 * @LastEditors: hedd <EMAIL>
 * @LastEditTime: 2025-06-17 10:39:42
 * @FilePath: \ihcrs_pc\public\config.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const energyData = {
  intAccount: {
    username: 'gy_function',
    password: 'Xm12345678!'
  },
  energyEmodelId: '1574997196833566721'
}
window.__CONFIG__ = {
  // 医院环境配置
  hospitalConfig: {
    default: {
      intAccount: energyData.intAccount,
      energyEmodelId: energyData.energyEmodelId
    },
    szzlyy: {
      oAuthData: {
        clientId: 'szzlyy',
        clientSecret: '660a2957'
      },
      intAccount: energyData.intAccount,
      energyEmodelId: '1724753192417259521'
    },
    tjzlyy: {
      intAccount: energyData.intAccount,
      energyEmodelId: '1574997196057620481'
    },
    ljxyy: {
      oAuthData: {
        clientId: 'ljxrmyy',
        clientSecret: '4498093c'
      },
      intAccount: {
        username: 'admin',
        password: 'Es12345678!'
      },
      energyEmodelId: '1767475808307785729'
    },
    bjsjtyy: {
      intAccount: {
        username: 'technician',
        password: 'Gy12345678!'
      },
      energyEmodelId: energyData.energyEmodelId
    },
    fjslyy: {
      intAccount: energyData.intAccount,
      energyEmodelId: '1780885716285796354'
    },
    szrmyy: {
      intAccount: energyData.intAccount,
      energyEmodelId: '1724753192383705090'
    }
  },
  // API路径配置
  apiConfig: {
    VUE_SPORADIC_API: 'sinomis-sporadic/',
    VUE_IEMC_API: 'sinomis-aircondition/',
    VUE_IEMC_ELEVATOR_API: 'iemc_medical_elevator/',
    VUE_SYS_API: 'sinomis-authweb/',
    VUE_WARN_API: 'sinomis-alarm/',
    VUE_ICIS_API: 'inspection/',
    VUE_IOMS_API: 'oneLogistics/',
    VUE_AQ_URL: 'ipsm/',
    VUE_APP_IMWS_API: 'medicalWaste/',
    VUE_SPACE_API: 'base_info/',
    SPACE_API: 'sinomis-service-manage/',
    VUE_SPACE_FILE: 'base_info_file/',
    VUE_ICIS_LZP: 'sinomis-service-external/',
    VUE_NEWS_API: 'service-message/',
    VUE_APP_PARTS_LIBRARY: 'chemical/',
    VUE_PLAN_API: 'preplan/',
    VUE_RHMS_API: 'sinomis-rental-housing/',
    VUE_CONVERGED_COM_API: 'sinomis-service-internet/',
    VUE_COURSE_URL: 'courseApi/',
    VUE_MONITOR_API: 'ifem/',
    BASE_URL_HSC: 'sinomis-chemicalWeb/',
    BASE_URL_LAB: 'labApi/',
    BASE_URL_LABORATORY: 'courseLabApi/',
    // VUE_PREVIEW_URL: 'kkfileview/onlinePreview?url='
    VUE_PREVIEW_URL: 'preview/onlinePreview?url=' // 现场
  },
  // WebSocket配置
  wsConfig: {
    WS_ALARM_URL: 'websocket/',
    WS_IEMC_URL: 'iemcWebsocket/',
    WS_ELEVATOR_URL: 'iemcEleWebsocket/'
  },
  // 其他配置
  otherConfig: {
    PROJECT_VERSION: 'V3.3.LO.ISS'
  }
}
