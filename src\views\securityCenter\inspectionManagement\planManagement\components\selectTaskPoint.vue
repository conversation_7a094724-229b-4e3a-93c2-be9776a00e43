<template>
  <el-dialog class="sino-dialog-device" :title="title" :visible.sync="dialogVisibleWz" @close="closeDialog">
    <div class="top-search" style="margin-bottom: 15px;">
      <el-input v-model.trim="dialogFilters.taskPointCode" placeholder="请输入巡检点编码" style="width: auto; margin-right: 10px;"></el-input>
      <el-input v-model.trim="dialogFilters.taskPointName" placeholder="请输入巡检点名称" style="width: auto; margin-right: 10px;"></el-input>
      <el-button type="primary" plain @click="_resetCondition">重置</el-button>
      <el-button type="primary" @click="_searchByCondition">查询</el-button>
    </div>
    <div class="hasSelected">
      <div style="color: #5b5b5b; font-weight: 600;">已选择：</div>
      <span v-if="!multipleSelection.length">暂无</span>
      <div v-for="(item, index) in multipleSelection" v-else :key="index" style="color: #5188fc;">
        <span>{{ item.taskPointName }}</span>
        <span v-show="multipleSelection.length > 1 && index !== multipleSelection.length - 1">、</span>
      </div>
    </div>
    <div class="table-list">
      <!--  @select="handleSelectionChangeDialog" -->
      <el-table
        ref="materialTable"
        v-loading="dialogtableLoading"
        :data="tableWz"
        :border="true"
        stripe
        height="300px"
        :row-key="
          (row) => {
            return row.id
          }
        "
        :cell-style="{ padding: '8px' }"
        style="overflow: auto;"
        :header-cell-style="$tools.setHeaderCell(3)"
        @selection-change="handleSelectionChangeDialog"
      >
        <el-table-column :reserve-selection="true" type="selection" width="55"></el-table-column>
        <el-table-column type="index" label="序号" width="65"></el-table-column>
        <el-table-column prop="taskPointCode" show-overflow-tooltip label="巡检点编号"></el-table-column>
        <el-table-column prop="taskPointName" show-overflow-tooltip label="巡检点名称"></el-table-column>
        <el-table-column prop="engineerRegion" label="所属网格" show-overflow-tooltip :formatter="formatter"></el-table-column>
        <el-table-column prop="engineerName" label="设备" show-overflow-tooltip></el-table-column>
        <el-table-column prop="remarks" label="描述" show-overflow-tooltip></el-table-column>
      </el-table>
    </div>
    <div class="table-page my_page">
      <el-pagination
        :current-page="paginationData.currentPage"
        :page-sizes="[15, 20, 30, 50]"
        :page-size="paginationData.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="paginationData.total"
        style="margin: 15px;"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="sureWz">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
// import { findTaskPointList } from "@/common/api/index";
export default {
  name: '',
  props: {
    title: {
      type: String,
      default: '选择巡检点'
    },
    dialogVisibleWz: {
      type: Boolean,
      default: false
    },
    flag: {
      type: String,
      default: 'maintainFlag'
    }
  },
  data() {
    return {
      dialogFiltersSelectArr: [],
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      props: {
        label: 'gridName',
        value: 'id',
        children: 'children'
      },
      arr: [],
      materialTypeID: '',
      areaList: [],
      multipleSelection: [],
      tableWz: [],
      dialogtableLoading: false,
      dialogFilters: {
        taskPointCode: '',
        taskPointName: ''
      },
      buttonLoading: false,
      tableSort: '',
      tableOrderBy: '',
      list: []
    }
  },
  created() {
    // 获取任务点列表
    this._findTaskPointList()
  },
  methods: {
    // 选择
    handleSelectionChangeDialog(val) {
      this.multipleSelection = val
      console.log(this.multipleSelection, '选择的巡检点')
      this.multipleSelection.forEach((v, i) => {
        this.$nextTick(() => {
          this.$refs.materialTable.toggleRowSelection(v, true) // 然后让未删除的列表显示选中状态
        })
      })
    },
    // 点击确定
    sureWz() {
      this.$emit('multipleSelection', this.multipleSelection)
      this.closeDialog()
    },
    // 查询表格
    _findTaskPointList() {
      this.dialogtableLoading = true
      let obj = this.LOGINDATA
      const { dialogFilters, paginationData } = this
      let data = {
        taskPointName: dialogFilters.taskPointName,
        taskPointCode: dialogFilters.taskPointCode,
        pageNo: paginationData.currentPage,
        pageSize: paginationData.pageSize
      }
      this.$api.findTaskPointList(data).then((res) => {
        this.dialogtableLoading = false
        if (res.code == 200) {
          this.tableWz = res.data.list
          this.paginationData.total = parseInt(res.data.count)
          if (res.data.list.length == 0) {
            this.emptyText = '未查到任何记录，请放大查询条件试试！'
          }
        }
        this.dialogtableLoading = false
      })
    },

    // 条件查询
    _searchByCondition() {
      this.paginationData.currentPage = 1
      this._findTaskPointList()
    },
    // 重置查询条件
    _resetCondition() {
      this.paginationData = {
        currentPage: 1,
        pageSize: 15,
        total: 0
      }
      this.dialogFilters = {
        taskPointCode: '',
        taskPointName: ''
      }
      this.$refs.materialTable.clearSelection()
      this._findTaskPointList()
    },
    closeDialog() {
      this.dialogFilters = {
        taskPointCode: '',
        taskPointName: ''
      }
      this.$emit('closeDialog')
    },

    handleSizeChange(val) {
      this.paginationData.currentPage = 1
      this.paginationData.pageSize = val
      this._findTaskPointList()
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this._findTaskPointList()
    },
    // 格式化处理内容
    formatter(row, column) {
      return row.engineerRegion ? row.engineerRegion.replace(/&gt;/g, '>') : ''
    }
  }
}
</script>
<style lang="scss" scoped>
.hasSelected {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
  min-height: 20px;
  max-height: 38px;
  height: auto;
  overflow-y: auto;
}

:deep(.el-dialog) {
  height: 580px;

  .el-dialog__body {
    padding: 15px 20px;
  }
}
</style>