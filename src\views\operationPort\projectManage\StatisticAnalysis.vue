<template>
  <PageContainer v-loading="loadingStatus">
    <template #content>
      <div class="statistic-analysis">
        <div class="statistic_search">
          <el-input v-model="search.startUser" placeholder="发起人" style="width: 200px"></el-input>
          <el-select v-model="search.supplierId" placeholder="全部供应商">
            <el-option v-for="item in supplierList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
          <el-cascader v-model="search.projectDictId" :options="projectDictList" :props="defaultProps" placeholder="全部工程类型"> </el-cascader>
          <el-select v-model="search.tagId" placeholder="全部标签项">
            <el-option v-for="item in tagList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
          <el-date-picker v-model="dataRange" type="datetimerange" start-placeholder="发起时间" end-placeholder="发起时间" value-format="yyyy-MM-dd HH:mm:ss"></el-date-picker>
          <el-button type="primary" plain @click="reset">重置</el-button>
          <el-button type="primary" @click="searchData">查询</el-button>
          <el-button type="primary" @click="exportData">导出</el-button>
        </div>
        <div class="statistic_content">
          <div class="statistic_content_state">
            <div class="statistic_content_state_item">
              <div class="title">施工状态统计</div>
              <div class="statistic_content_state_item_content">
                <div class="sum">
                  <img src="@/assets/images/projectManage/bill.png" width="60" height="60" />
                  <div class="name">总数</div>
                  <div class="value">{{ executionMap.length ? executionMap[0].value : 0 }}</div>
                </div>
                <div class="classify">
                  <template v-for="(item, index) in executionMap">
                    <div v-if="index !== 0" :key="index" class="classify_item">
                      <div class="name">{{ item.name }}</div>
                      <div class="value">{{ item.value }}</div>
                    </div>
                  </template>
                </div>
              </div>
            </div>
            <div class="statistic_content_state_item">
              <div class="title">结算状态统计</div>
              <div class="statistic_content_state_item_content">
                <div class="sum">
                  <img src="@/assets/images/projectManage/bill.png" width="60" height="60" />
                  <div class="name">总数</div>
                  <div class="value">{{ settlementMap.length ? settlementMap[0].value : 0 }}</div>
                </div>
                <div class="classify">
                  <template v-for="(item, index) in settlementMap">
                    <div v-if="index !== 0" :key="index" class="classify_item">
                      <div class="name">{{ item.name }}</div>
                      <div class="value">{{ item.value }}</div>
                    </div>
                  </template>
                </div>
              </div>
            </div>
          </div>
          <div class="statistic_content_price">
            <div class="title">金额统计</div>
            <div class="statistic_content_price_content">
              <div class="sum">
                <img src="@/assets/images/comprehensiveStatistics/statisticalIcon2x.png" width="60" height="60" />
                <div class="name">总额</div>
                <div class="value">{{ sumAmount }}元</div>
              </div>
              <div class="price_classify">
                <div v-for="(item, index) in firstPrice" :key="`first${index}`" class="price_classify_item">
                  <div class="name">{{ item.name }}</div>
                  <div class="value">{{ item.value }}</div>
                </div>
                <div v-for="(item, index) in warrantyPrice" :key="`warranty${index}`" class="price_classify_item">
                  <div class="name">{{ item.name }}</div>
                  <div class="value">{{ item.value }}</div>
                </div>
              </div>
            </div>
          </div>
          <div class="statistic_content_chart">
            <div class="statistic_content_chart_item">
              <div class="title">施工单位占比</div>
              <Echarts ref="pieWorkTeamRef" dom-id="pieCompany" class="chart"></Echarts>
            </div>
            <div class="statistic_content_chart_item">
              <div class="title">专管员占比</div>
              <Echarts ref="pieDeputyRef" dom-id="pieDeputyRef" class="chart"></Echarts>
            </div>
            <div class="statistic_content_chart_item">
              <div class="title">工程类型占比</div>
              <Echarts ref="pieProTypeRef" dom-id="pieProTypeRef" class="chart"></Echarts>
            </div>
          </div>
          <div style="height: 770px">
            <el-table v-loading="tableLoading" height="100%" :data="tableData" border stripe table-layout="auto" class="tableAuto">
              <el-table-column v-for="(item, index) in tableColumns" :key="index" :label="item.label" :prop="item.prop" show-overflow-tooltip></el-table-column>
            </el-table>
          </div>
          <el-pagination
            style="margin: 16px 0"
            :current-page="pagination.pageNo"
            :page-sizes="['15', '30', '50', '100']"
            :page-size="pagination.pageSize"
            :layout="'total, sizes, ->, prev, pager, next, jumper'"
            :total="pagination.total"
            @size-change="paginationSizeChange"
            @current-change="paginationCurrentChange"
          >
          </el-pagination>
        </div>
      </div>
    </template>
  </PageContainer>
</template>
<script>
import PageContainer from '@/components/pageContainer/index.vue'
import Echarts from '@/components/Echarts/index.vue'
export default {
  name: 'StatisticAnalysis',
  components: { PageContainer, Echarts },
  data() {
    return {
      search: {
        startUser: '',
        supplierId: '',
        startTime: '',
        endTime: '',
        projectDictId: '',
        tagId: ''
      },
      supplierList: [],
      projectDictList: [],
      defaultProps: {
        label: 'name',
        value: 'id',
        children: 'children',
        checkStrictly: true
      },
      tagList: [],
      dataRange: [],
      firstPrice: [
        {
          name: '首款金额',
          value: '',
          key: 'firstAmount'
        },
        {
          name: '首款已支付',
          value: '',
          key: 'paymentAmount'
        },
        {
          name: '首款未支付',
          value: '',
          key: 'unpaidAmount'
        },
        {
          name: '支付比例',
          value: '',
          key: 'payRate'
        }
      ],
      warrantyPrice: [
        {
          name: '质保金总额',
          value: '',
          key: 'totalAmount'
        },
        {
          name: '质保金已支付',
          value: '',
          key: 'paymentAmount'
        },
        {
          name: '质保金未支付',
          value: '',
          key: 'unpaidAmount'
        },
        {
          name: '支付比例',
          value: '',
          key: 'payRate'
        }
      ],
      // 施工单位占比
      workTeamData: [],
      // 专员占比
      deputyData: [],
      // 工程类型占比
      proTypeData: [],
      // 预算总金额
      sumAmount: 0,
      executionMap: [],
      settlementMap: [],
      tableData: [],
      loadingStatus: false,
      tableLoading: false,
      tableColumns: [],
      pagination: {
        pageSize: 15,
        pageNo: 1,
        total: 0
      }
    }
  },
  created() {
    this.getColum()
  },
  mounted() {
    this.init()
    this.getStatisticData()
    this.getTableList()
  },
  methods: {
    init() {
      this.getSupplierList()
      this.getProjectDictList()
      this.getTagList()
    },
    getSupplierList() {
      this.$api.SporadicProject.getProjectSupplierAll().then((res) => {
        if (res.code == 200) {
          this.supplierList = res.data
        }
      })
    },
    getProjectDictList() {
      this.$api.SporadicProject.getDictConfigData().then((res) => {
        if (res.code == 200) {
          this.projectDictList = res.data
        }
      })
    },
    getTagList() {
      this.$api.SporadicProject.proTagList().then((res) => {
        if (res.code == 200) {
          this.tagList = res.data
        }
      })
    },
    // 获取表头
    async getColum() {
      const res = await this.$api.SporadicProject.getAllProColumnsData({})
      if (res && res.data.length) {
        this.tableColumns = res.data.map((item) => {
          return {
            label: item.columnName,
            prop: item.columnKey
          }
        })
      }
    },
    exportData() {
      let params = {
        ...this.search,
        startTime: this.dataRange[0],
        endTime: this.dataRange[1]
      }
      params.projectDictId = params.projectDictId.length ? params.projectDictId.join(',') : ''
      this.$api.SporadicProject.projectStatisticExport(params).then((res) => {
        const blob = new Blob([res], { type: 'application/vnd.ms-excel;charset=UTF-8' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = '工程项目统计分析.xlsx'
        link.click()
        window.URL.revokeObjectURL(url)
        this.loading = false
      })
    },
    getTableList() {
      let params = {
        ...this.search,
        ...this.pagination,
        startTime: this.dataRange[0],
        endTime: this.dataRange[1]
      }
      params.projectDictId = params.projectDictId.length ? params.projectDictId.join(',') : ''
      this.$api.SporadicProject.getAllProList(params).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.records
          this.pagination.total = res.data.total
        }
      })
    },
    searchData() {
      this.pagination.pageNo = 1
      this.getStatisticData()
      this.getTableList()
    },
    reset() {
      this.pagination.pageNo = 1
      this.search = {
        startUser: '',
        supplierId: '',
        startTime: '',
        endTime: '',
        projectDictId: '',
        tagId: ''
      }
      this.dataRange = []
      this.getStatisticData()
      this.getTableList()
    },
    paginationSizeChange(val) {
      this.pagination.pageNo = 1
      this.pagination.pageSize = val
      this.getTableList()
    },
    paginationCurrentChange(val) {
      this.pagination.pageNo = val
      this.getTableList()
    },
    // 获取统计数据
    getStatisticData() {
      this.loadingStatus = true
      let params = {
        ...this.search,
        ...this.pagination,
        startTime: this.dataRange[0],
        endTime: this.dataRange[1]
      }
      params.projectDictId = params.projectDictId.length ? params.projectDictId.join(',') : ''
      this.$api.SporadicProject.queryProjectStatisticAnalysis(params)
        .then((res) => {
          if (res.code === '200') {
            this.sumAmount = res.data.firstAmount.totalAmount
            this.executionMap = res.data.constructionList
            this.settlementMap = res.data.settlementList
            this.setPriceField(res.data.firstAmount, 'firstPrice')
            this.setPriceField(res.data.warrantyAmount, 'warrantyPrice')
            this.initWorkTeamChart(res.data.supplierRate)
            this.initDeputyChart(res.data.specialOfficerRate)
            this.initProTypeChart(res.data.projectDictRate)
          } else {
            throw res.msg || '获取统计数据失败'
          }
        })
        .catch((msg) => {
          msg && this.$message.error(msg)
        })
        .finally(() => {
          this.loadingStatus = false
        })
    },
    setPriceField(data, orogin) {
      for (const key in data) {
        this[orogin].forEach((el) => {
          if (el.key === key) {
            el.value = data[key] + '元'
            if (key === 'payRate') {
              el.value = (data[key] * 100).toFixed(2) + '%'
            }
          }
        })
      }
    },
    // 饼图配置
    getPieChartOption(data) {
      const total = data.reduce((res, it) => res + it.value, 0)
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: function (param) {
            const icon = `<span style="display: inline-block;height: 8px;width: 8px;border-radius: 8px;background-color: ${param.color}"></span>`
            const lab1 = `<span style="margin-left: 5px;">${param.name}</span>`
            const lab2 = `<span style="margin-left: 10px">${param.value}单</span>`
            const lab3 = `<span style="margin-left: 10px">${param.percent}%</span>`
            return icon + lab1 + lab2 + lab3
          }
        },
        legend: {
          orient: 'vertical',
          left: '65%',
          top: 'center',
          align: 'left',
          itemHeight: 8,
          itemWidth: 8,
          formatter: (name) => {
            const item = data.find((it) => it.name === name)
            if (!item) return name
            const recent = ((item.value / total) * 100).toFixed(2)
            return `${name}: ${item.value}单  ${recent}%`
          }
        },
        series: [
          {
            type: 'pie',
            center: ['30%', '50%'],
            radius: ['60%', '80%'],
            label: {
              show: false
            },
            data
          }
        ]
      }
      return option
    },
    // 初始化施工队占比图表
    initWorkTeamChart(data) {
      this.workTeamData = data.map((it) => ({
        value: it.num,
        name: it.name
      }))
      this.$nextTick(() => {
        const option = this.getPieChartOption(this.workTeamData)
        this.$refs.pieWorkTeamRef.init(option)
      })
    },
    // 初始化专员占比
    initDeputyChart(data) {
      this.deputyData = data.map((it) => ({
        value: it.num,
        name: it.name
      }))
      this.$nextTick(() => {
        const option = this.getPieChartOption(this.deputyData)
        this.$refs.pieDeputyRef.init(option)
      })
    },
    // 初始化专员占比
    initProTypeChart(data) {
      this.proTypeData = data.map((it) => ({
        value: it.num,
        name: it.name
      }))
      this.$nextTick(() => {
        const option = this.getPieChartOption(this.proTypeData)
        this.$refs.pieProTypeRef.init(option)
      })
    }
  }
}
</script>
<style scoped lang="scss">
.statistic-analysis {
  background: #fff;
  height: 100%;
  padding-bottom: 16px;
  .statistic {
    width: 100%;
    height: 100%;
    &_search {
      padding: 16px;
      display: flex;
      align-items: center;
      & > div {
        margin-right: 10px;
      }
    }
    &_content {
      height: calc(100% - 48px);
      overflow-y: auto;
      padding: 0 16px;
      &_state {
        display: flex;
        height: 272px;
        justify-content: space-between;
        &_item {
          width: calc(50% - 8px);
          &_content {
            display: flex;
            height: calc(100% - 48px);
          }
        }
      }
      &_price {
        width: 100%;
        height: 272px;
        &_content {
          height: calc(100% - 48px);
          display: flex;
          .price_classify {
            width: calc(100% - 395px);
            height: 100%;
            display: grid;
            grid-template: repeat(2, minmax(calc((100% - 10px) / 2), 1fr)) / repeat(4, 1fr);
            gap: 10px;
            .price_classify_item {
              background: #faf9fc;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              .name {
                color: #666;
              }
              .value {
                color: #333;
                font-size: 18px;
                font-weight: bold;
              }
            }
          }
        }
      }
      &_chart {
        display: flex;
        width: 100%;
        height: 256px;
        &_item {
          flex: 1;
          height: 100%;
          .chart {
            width: 100%;
            height: calc(100% - 48px) !important;
          }
        }
      }
    }
  }
}
.title {
  font-size: 16px;
  padding: 8px 0px;
}
.sum {
  display: flex;
  width: 395px;
  height: 100%;
  margin-right: 10px;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #faf9fc;
  .name {
    color: #666;
    margin-top: 10px;
  }
  .value {
    color: #333;
    font-size: 18px;
    margin-top: 10px;
    font-weight: bold;
  }
}
.classify {
  width: calc(100% - 405px);
  height: 100%;
  overflow-y: auto;
  &_item {
    height: calc((100% - 20px) / 3);
    background: #faf9fc;
    margin-bottom: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .name {
      color: #666;
    }
    .value {
      color: #333;
      font-size: 18px;
      font-weight: bold;
    }
    &:last-child {
      margin-bottom: 0;
    }
  }
}
::v-deep .el-date-editor .el-range__icon {
  line-height: 24px;
}
</style>
