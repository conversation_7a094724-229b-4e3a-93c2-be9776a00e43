<script>
import tableListMixin from '@/mixins/tableListMixin'
import { UsingStatusOptions } from '@/assets/common/constant'
export default {
  name: 'DictionaryList',
  components: {
    DictionaryValueEdit: () => import('./components/DictionaryValueEdit')
  },
  filters: {
    statusFilter(status) {
      return UsingStatusOptions.find((it) => it.value == status)?.label ?? '-'
    }
  },
  mixins: [tableListMixin],
  data() {
    return {
      // 字典类型
      currentType: '',
      treeSearchKeyWord: '',
      searchForm: {
        name: ''
      },
      treeData: [],
      treeLoadingStatus: false,
      tableData: [],
      tableLoadingStatus: false,
      dialog: {
        show: false,
        id: '',
        readonly: false // 查看模式
      }
    }
  },
  computed: {
    OperateType() {
      return {
        Edit: 'edit',
        Delete: 'delete',
        Create: 'create',
        View: 'view',
        Status: 'Status'
      }
    }
  },
  watch: {
    // 监听树搜索关键字变化时，调用树的过滤方法
    treeSearchKeyWord(val) {
      this.$refs.treeRef.filter(val)
    }
  },
  mounted() {
    this.getTreeData()
  },
  methods: {
    // 获取树数据
    getTreeData() {
      this.treeLoadingStatus = true
      this.$api.rentalHousingApi
        .selectDictTypeList()
        .then((res) => {
          if (res.code === '200') {
            // 创建root节点，固定
            const root = { dictName: '公租房', id: '#', children: [] }
            root.children = res.data.map((it) => {
              // 设置父级ID
              it.parentId = root.id
              return it
            })
            this.treeData = [root]
            // 当树数据加载完成后，默认选中第一个节点
            const [first] = res.data
            this.currentType = first.dictType
            this.onSearch()
            this.$nextTick(() => {
              this.$refs.treeRef.setCurrentKey(first.id)
            })
          } else {
            throw res.data.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取字典类型失败'))
        .finally(() => {
          this.treeLoadingStatus = false
        })
    },
    // 搜索树
    onTreeSearch(value, data) {
      if (!value) return true
      return data.dictName.includes(value)
    },
    // 当树点击被点击时
    onTreeNodeClick(data) {
      if (data.dictType !== this.currentType) {
        this.currentType = data.dictType
        this.onReset()
      }
    },
    // 获取列表数据
    getDataList() {
      this.tableData = []
      this.tableLoadingStatus = true
      const params = {
        pageSize: this.pagination.size,
        pageNum: this.pagination.current,
        dictName: this.searchForm.name,
        dictType: this.currentType
      }
      this.$api.rentalHousingApi
        .queryDictPage(params)
        .then((res) => {
          if (res.code === '200') {
            this.tableData = res.data.records
            this.pagination.total = res.data.total
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取列表数据失败'))
        .finally(() => {
          this.tableLoadingStatus = false
        })
    },
    // 表单搜索按钮点击
    onSearch() {
      this.pagination.current = 1
      this.getDataList()
    },
    // 表单重置按钮点击
    onReset() {
      this.$refs.formRef.resetFields()
      this.onSearch()
    },
    // 列表操作相关的事件绑定
    onOperate(row, command) {
      switch (command) {
        case this.OperateType.Delete:
          // 删除非启用的字典数据
          if (row.presetsType === 1) {
            this.$message.error('预设的字典不允许删除！')
          } else if (row.status === 1) {
            this.$message.error('启用的字典不允许删除！')
          } else {
            this.$confirm('删除后将无法恢复，是否确定删除？', '信息提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消'
            }).then(() => this.doDelete(row.id))
          }
          break
        case this.OperateType.Status:
          const status = Math.abs(+row.dictState - 1)
          this.doUpdateStatus(row.id, status)
          break
        default:
          this.dialog.parentIds = []
          this.dialog.id = row?.id ?? ''
          this.dialog.readonly = command === this.OperateType.View
          this.dialog.show = true
          break
      }
    },
    // 删除一行数据
    doDelete(id) {
      this.tableLoadingStatus = true
      this.$api.rentalHousingApi
        .deleteDictById({ idList: [id] })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('已删除')
            this.getDataList()
          } else {
            throw res.message || '删除失败'
          }
        })
        .catch((msg) => msg && this.$message.error(msg))
        .finally(() => (this.tableLoadingStatus = false))
    },
    // 修改字典状态
    doUpdateStatus(id, status) {
      const params = {
        idList: [id],
        state: status
      }
      this.tableLoadingStatus = true
      this.$api.rentalHousingApi
        .enableOrDisable(params)
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('操作成功')
            this.getDataList()
          } else {
            throw res.message || '操作失败'
          }
        })
        .catch((msg) => msg && this.$message.error(msg))
        .finally(() => (this.tableLoadingStatus = false))
    }
  }
}
</script>
<template>
  <PageContainer class="dictionary-list">
    <template #content>
      <div class="dictionary-list__left">
        <div class="dictionary-list__left__header">
          <el-input v-model="treeSearchKeyWord" placeholder="输入关键字搜索" suffix-icon="el-icon-search"></el-input>
        </div>
        <el-tree
          ref="treeRef"
          node-key="id"
          :data="treeData"
          :props="{ label: 'dictName' }"
          :filter-node-method="onTreeSearch"
          :current-node-key="currentType"
          :expand-on-click-node="false"
          default-expand-all
          highlight-current
          class="dictionary-list__tree"
          @node-click="onTreeNodeClick"
        ></el-tree>
      </div>
      <div class="dictionary-list__right">
        <div class="dictionary-list__right__header">
          <el-form ref="formRef" :model="searchForm" class="dictionary-list__search" inline @submit.native.prevent="onSearch">
            <el-form-item prop="name">
              <el-input v-model="searchForm.name" clearable filterable placeholder="搜索名称"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onSearch">查询</el-button>
              <el-button type="primary" plain @click="onReset">重置</el-button>
            </el-form-item>
          </el-form>
          <div class="dictionary-list__right__actions">
            <el-button type="primary" :disabled="!currentType" @click="onOperate(undefined, OperateType.Create)">
              添加字典值
            </el-button>
          </div>
        </div>
        <div class="dictionary-list__table">
          <el-table v-loading="tableLoadingStatus" height="100%" :data="tableData" border stripe table-layout="auto" class="tableAuto" row-key="id">
            <el-table-column label="名称" prop="dictName" show-overflow-tooltip></el-table-column>
            <el-table-column label="编码" prop="dictCode"></el-table-column>
            <el-table-column label="状态" prop="dictState">
              <template #default="{ row }">
                <span class="dictionary-list__tag" :class="`dictionary-list__tag--${row.dictState}`">
                  {{ row.dictState | statusFilter }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="预设" prop="preset" :formatter="(row) => (row.dictPreset === '1' ? '是' : '否')"></el-table-column>
            <el-table-column label="操作" width="150px">
              <template #default="{ row }">
                <el-button type="text" @click="onOperate(row, OperateType.View)">查看</el-button>
                <el-button type="text" @click="onOperate(row, OperateType.Edit)">编辑</el-button>
                <el-dropdown @command="(command) => onOperate(row, command)">
                  <el-button type="text" style="margin-left: 10px">更多</el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :command="OperateType.Status">
                        {{ Math.abs(+row.dictState - 1) | statusFilter }}
                      </el-dropdown-item>
                      <el-dropdown-item style="color: #ff1919" :command="OperateType.Delete">删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination
          class="dictionary-list__pagination"
          :current-page="pagination.page"
          :page-sizes="pagination.pageSizeOptions"
          :page-size="pagination.size"
          :layout="pagination.layoutOptions"
          :total="pagination.total"
          @size-change="paginationSizeChange"
          @current-change="paginationCurrentChange"
        >
        </el-pagination>
      </div>
      <!--字典值编辑-->
      <DictionaryValueEdit :visible.sync="dialog.show" :type="currentType" v-bind="dialog" @success="getDataList" />
    </template>
  </PageContainer>
</template>
<style scoped lang="scss">
.dictionary-list {
  ::v-deep(> .container-content) {
    display: flex;
    background-color: #fff;
  }
  &__left {
    width: 280px;
    border-right: solid 1px #eee;
    display: flex;
    flex-flow: column nowrap;
    &__header {
      padding: 16px;
      line-height: 40px;
    }
    &::v-deep(.el-tree) {
      height: calc(100% - 64px);
      overflow: auto;
      padding: 0 16px 16px 16px;
      .el-tree-node__content {
        line-height: 36px;
        height: 36px;
      }
      .el-tree-node.is-current > .el-tree-node__content {
        color: #3562db;
        background: #e6effc;
      }
    }
  }
  &__right {
    flex: 1;
    overflow: hidden;
    padding: 16px;
    display: flex;
    flex-flow: column nowrap;
  }
  &__right__header {
    display: flex;
    justify-content: space-between;
  }
  &__right__actions {
    line-height: 40px;
  }
  &__table {
    margin-top: -10px;
    flex: 1;
  }
  &__pagination {
    margin-top: 10px;
  }
  &__tag {
    // 停用
    &--0 {
      --color: #f64646;
    }
    // 启用
    &--1 {
      --color: #00b42a;
    }
    &:before {
      content: '';
      display: inline-block;
      vertical-align: middle;
      margin-right: 4px;
      height: 4px;
      width: 4px;
      border-radius: 4px;
      background-color: var(--color);
    }
  }
  &__table__color {
    display: inline-block;
    height: 16px;
    width: 16px;
    vertical-align: text-top;
    margin-right: 4px;
  }
  ::v-deep(.dictionary-list__table__col-img) {
    padding: 0;
    .cell {
      padding: 4px;
    }
    .el-image {
      height: 38px;
      .el-image__error {
        background-color: transparent;
      }
    }
  }
}
</style>
