<template>
  <el-dialog title="文件列表" width="40%" :visible.sync="callFileDialogShow" custom-class="model-dialog"
    :before-close="closeDialog">
    <div class="content">
      <div v-for="(item,index) in callFileList" :key="index">
        <div class="fileTitle">{{fileTitle}}</div>
        <div class="fileClass">
          <span>{{item.file}}</span>
          <span class="operate" @click="downLoadFile(item)">下载</span>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'callFile',
  components: {},
  props: {
    callFileDialogShow: {
      type: Boolean,
      default: false
    },
    callFileList: {
      type: Array,
      default: () => []
    },
    fileTitle: {
      type: String,
      default: ''
    }
  },
  data() {
    return {

    }
  },
  mounted() {
  },
  methods: {
    downLoadFile(item) {
      this.$emit('downLoadCallFile', item)
    },
    closeDialog() {
      this.$emit('closeFileDialog')
    }
  }
}
</script>
<style lang="scss" scoped>
.model-dialog {
  .content {
    margin: 0 auto;
    background: #fff;
    border-radius: 4px;
    width: 100%;
    height: 380px;
    overflow: auto;
  }
  .fileTitle {
    padding: 4px 16px 0 16px;
  }
  .fileClass {
    display: flex;
    justify-content: space-between;
    margin-bottom: 24px;
    padding: 10px 16px;
    border-bottom: 2px solid #f0f0f0;
    .operate {
      color: #438ccf;
      cursor: pointer;
    }
  }
}
</style>

