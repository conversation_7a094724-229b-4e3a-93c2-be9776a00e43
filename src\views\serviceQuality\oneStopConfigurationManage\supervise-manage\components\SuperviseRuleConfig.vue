<script>
import { SuperviseDictType } from '../constant'
export default {
  name: 'SuperviseRuleConfig',
  components: {
    DialogSelectUser: () => import('../../components/DialogSelectUser.vue')
  },
  props: {
    type: String,
    optionWays: {
      type: Array,
      default: () => []
    },
    optionUnit: {
      type: Array,
      default: () => []
    },
    data: Object,
    readonly: Boolean,
    project: String
  },
  data() {
    return {
      loadingStatus: false,
      // 表单数据
      formModel: {
        // 是否重新开始挂单重置
        isRestart: '',
        // 超时时间
        duration: '',
        // 单位
        unit: '',
        // 通知方式
        notifyWays: [],
        // 用户类型 当前处理人，自定义
        userType: '',
        // 接收人姓名
        userName: '',
        // 接收人电话
        userPhone: '',
        // 接收人ID
        userId: ''
      },
      rules: {
        // isRestart: [{ required: true, message: '请选择是否挂单重置' }],
        duration: [{ required: true, message: '请输入超时时间', trigger: 'blur' }],
        userType: [{ required: true, message: '请选择通知配置' }],
        notifyWays: [{ required: true, type: 'array', message: '请选择通知方式' }],
        userName: [{ required: true, message: '请选择通知人员' }],
        userPhone: [
          { required: true, message: '请输入联系方式' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的联系方式' }
        ]
      },
      dialog: false
    }
  },
  computed: {
    isCustom() {
      return this.formModel.userType === '0'
    },
    // 是否派工超时类型
    isFinshType() {
      return this.type === SuperviseDictType.OverTime_Finish
    },
    // 是否到达超时类型
    isReachType() {
      return this.type === SuperviseDictType.OverTime_Reach
    },
    // 是否显示当前操作人选项
    showUserTypeCurrent() {
      const types = [SuperviseDictType.OverTime_Assign, SuperviseDictType.OverTime_Finish, SuperviseDictType.OverTime_Check, SuperviseDictType.OverTime_Reach]
      return types.includes(this.type)
    }
  },
  mounted() {
    if (Object.keys(this.data || {}).length > 0) {
      this.formModel.isRestart = this.data.hangingOrderRes || ''
      this.formModel.duration = this.data.timeoutPeriod || ''
      this.formModel.unit = this.data.timeoutUnit || ''
      this.formModel.notifyWays = this.data.pushType.split(',')
      this.formModel.userType = this.data.receiverType
      this.formModel.userName = this.data.receiverName || ''
      this.formModel.userPhone = this.data.receiverPhone || ''
      this.formModel.userId = this.data.receiverId || ''
    } else {
      if (this.optionUnit.length) {
        this.formModel.unit = this.optionUnit[0].dictCode || ''
      }
      if (this.isFinshType) {
        this.formModel.isRestart = '1'
      }
      if (this.isReachType) {
        this.formModel.isRestart = '1'
      }
    }
  },
  methods: {
    updateVisible() {
      this.dialog = !this.dialog
    },
    onUserNameClick() {
      if (!this.isCustom) return
      this.dialog = true
    },
    /**
     * 校验表单项数据
     */
    validate() {
      return this.$refs.formRef.validate().then(() => {
        return {
          timeoutType: this.type,
          timeoutPeriod: this.formModel.duration,
          timeoutUnit: this.formModel.unit,
          hangingOrderRes: this.formModel.isRestart,
          pushType: this.formModel.notifyWays.join(),
          receiverType: this.formModel.userType,
          receiverId: this.formModel.userId,
          receiverName: this.formModel.userName,
          receiverPhone: this.formModel.userPhone
        }
      })
    },
    // 选择人员后
    onUserSelected(user) {
      this.formModel.userId = user.userId
      this.formModel.userName = user.staffName
      this.formModel.userPhone = user.mobile
    }
  }
}
</script>
<template>
  <div class="supervise-rule-config">
    <el-form ref="formRef" :model="formModel" :disabled="readonly" :rules="rules" label-width="100px">
      <el-row>
        <el-col v-if="isFinshType" :span="8">
          <el-form-item prop="isRestart" label="挂单重置" :rules="[{ required: true, message: '请选择是否挂单重置' }]">
            <el-radio-group v-model="formModel.isRestart">
              <el-radio label="1">是</el-radio>
              <el-radio label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col v-if="isReachType" :span="8">
          <el-form-item prop="isRestart" label="确认接单重置" label-width="120px" :rules="[{ required: true, message: '请选择是否确认重置' }]">
            <el-radio-group v-model="formModel.isRestart">
              <el-radio label="1">是</el-radio>
              <el-radio label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="duration" label="超时时间">
            <el-input v-model="formModel.duration" class="supervise-rule-config__duration" :maxlength="9" onkeyup="value=value.replace(/^0|\D/,'')" placeholder="请输入">
              <template #append>
                <el-select v-model="formModel.unit" style="width: 80px">
                  <el-option v-for="{ dictCode, dictName } of optionUnit" :key="dictCode" :label="dictName" :value="dictCode"></el-option>
                </el-select>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="notifyWays" label="通知方式">
            <el-checkbox-group v-model="formModel.notifyWays">
              <el-checkbox v-for="{ dictCode, dictName } of optionWays" :key="dictCode" :label="dictCode">{{ dictName }} </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item prop="userType" label="通知配置">
            <el-select v-model="formModel.userType">
              <el-option label="自定义" value="0"></el-option>
              <el-option v-if="showUserTypeCurrent" label="当前处理人" value="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <template v-if="isCustom">
          <el-col :span="8">
            <el-form-item prop="userName" label="通知人员">
              <el-input class="supervise-rule-config__receiver" :value="formModel.userName" readonly @focus="onUserNameClick">
                <template #suffix>
                  <i class="el-icon-arrow-down"></i>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="userPhone" label="联系方式">
              <el-input v-model="formModel.userPhone" type="phone" :maxlength="11"></el-input>
            </el-form-item>
          </el-col>
        </template>
      </el-row>
    </el-form>
    <template v-if="isCustom">
      <DialogSelectUser :visible.sync="dialog" title="选择接收人" @selected="onUserSelected" @updateVisible="updateVisible" />
    </template>
  </div>
</template>
<style lang="scss" scoped>
.el-form-item {
  .el-input {
    max-width: 200px;
    &.el-input-group {
      vertical-align: middle;
    }
  }
}
.supervise-rule-config {
  &__receiver {
    ::v-deep(.el-input__inner) {
      cursor: pointer;
    }
  }
  &__duration {
    ::v-deep(.el-input-group__append) {
      background: #fff;
    }
  }
}
</style>
