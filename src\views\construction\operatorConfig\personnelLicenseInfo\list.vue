<template>
  <div class="bill-config">
    <div class="bill-config__top">
      <el-form ref="formRef" :model="searchForm" class="bill-config__search" inline @submit.native.prevent="onSearch">
        <el-form-item prop="name">
          <el-input v-model="searchForm.name" clearable filterable placeholder="证照名称"></el-input>
        </el-form-item>
        <el-form-item prop="enableStatus">
          <el-select v-model="searchForm.enableStatus" placeholder="请选择启用状态">
            <el-option v-for="item in enableStatusOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" plain @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
      <div class="bill-config__actions">
        <el-button type="primary" icon="el-icon-plus" @click="onOperate('add')">新增</el-button>
      </div>
    </div>
    <div class="bill-config__table">
      <el-table v-loading="loadingStatus" height="100%" :data="tableData" border stripe table-layout="auto" class="tableAuto" row-key="id">
        <el-table-column label="序号" type="index" width="55">
          <template slot-scope="scope">
            <span>{{ (pagination.page - 1) * pagination.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="证照名称" prop="fileName" show-overflow-tooltip></el-table-column>
        <el-table-column label="编码" prop="fileCode" show-overflow-tooltip></el-table-column>
        <el-table-column label="启用状态" prop="enableStatus" width="120px">
          <template #default="{ row }">
            <span v-if="row.enableStatus == '0'" style="color: #ff1919">停用</span>
            <span v-if="row.enableStatus == '1'" style="color: rgb(8, 203, 131)">启用</span>
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" show-overflow-tooltip></el-table-column>
        <el-table-column label="操作" width="150px">
          <template #default="{ row }">
            <el-button type="text" @click="onOperate('view', row)">查看</el-button>
            <el-button type="text" @click="onOperate('edit', row)">编辑</el-button>
            <el-button type="text" style="color: #ff1919" v-if="row.initialed !== 1 && row.enableStatus != 1" @click="onOperate('del', row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-pagination
      class="bill-config__pagination"
      :page-current="pagination.page"
      :page-sizes="[15, 20, 30, 50]"
      :page-size="pagination.pageSize"
      layout="total, sizes, ->, prev, pager, next, jumper"
      :total="pagination.total"
      @size-change="paginationSizeChange"
      @current-change="paginationCurrentChange"
    >
    </el-pagination>
    <BillEdit v-if="addVisible" v-model="addVisible" :detail="detail" :handle="handle" @success="getDataList" />
  </div>
</template>
  <script>
import dayjs from 'dayjs'
export default {
  name: 'BillConfig',
  components: {
    BillEdit: () => import('./add.vue')
  },
  data() {
    return {
      searchForm: {
        name: '',
        enableStatus: ''
      },
      enableStatusOptions: [
        {
          value: '1',
          label: '启用'
        },
        {
          value: '0',
          label: '停用'
        }
      ],
      tableData: [],
      loadingStatus: false,
      dialog: {
        show: false,
        editData: null,
        readonly: false
      },
      pagination: {
        page: 1,
        pageSize: 15,
        total: 0
      },
      addVisible: false,
      detail: null,
      handle: 'add'
    }
  },
  mounted() {
    this.getDataList()
  },
  methods: {
    dayjs,
    getDataList() {
      this.loadingStatus = true
      const params = {
        fileName: this.searchForm.name,
        enableStatus: this.searchForm.enableStatus,
        type: 2,
        ...this.pagination
      }
      this.$api
        .getConstructionCertificateList(params)
        .then((res) => {
          if (res.code === '200') {
            this.tableData = res.data.records
            this.pagination.total = res.data.total
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '查询失败'))
        .finally(() => (this.loadingStatus = false))
    },
    paginationSizeChange(val) {
      this.pagination.pageSize = val
      this.getDataList()
    },
    paginationCurrentChange(val) {
      this.pagination.page = val
      this.getDataList()
    },
    onSearch() {
      this.getDataList()
    },
    onReset() {
      this.pagination.page = 1
      this.searchForm.name = ''
      this.searchForm.enableStatus = ''
      this.$refs.formRef.resetFields()
      this.onSearch()
    },
    onOperate(type, row) {
      if (type == 'del') {
        this.doDelete(row.id)
        return
      }
      this.handle = type
      this.detail = row || null
      this.addVisible = true
    },
    // 删除单据
    doDelete(id) {
      this.$confirm('删除后将无法恢复，是否确定删除？', '信息提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        this.$api
          .deleteConstructionCertificate({ id, type: 2 })
          .then((res) => {
            if (res.code === '200') {
              this.$message.success('删除成功')
              this.loadingStatus = true
              this.getDataList()
            } else {
              throw res.message
            }
          })
          .catch((msg) => msg && this.$message.error(msg))
          .finally(() => (this.loadingStatus = false))
      })
    }
  }
}
</script>
  <style scoped lang="scss">
.bill-config {
  display: flex;
  flex-flow: column nowrap;
  padding: 16px;
  &__top {
    display: flex;
    justify-content: space-between;
  }
  &__actions {
    line-height: 40px;
  }
  &__pagination {
    margin-top: 10px;
  }
  &__table {
    margin-top: -10px;
    flex: 1;
  }
  &__tag {
    // 停用
    &--0 {
      --color: #f64646;
    }
    // 启用
    &--1 {
      --color: #00b42a;
    }
    &:before {
      content: '';
      display: inline-block;
      vertical-align: middle;
      margin-right: 4px;
      height: 4px;
      width: 4px;
      border-radius: 4px;
      background-color: var(--color);
    }
  }
}
</style>
  