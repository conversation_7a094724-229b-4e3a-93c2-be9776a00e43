<template>
  <el-dialog class="sino-dialog-location" :title="title" :visible.sync="dialogVisibleWz" @close="closeDialog">
    <div class="top-search" style="margin-bottom: 15px;">
      <el-input v-model.trim="dialogFilters.locationPointCode" placeholder="定位点编号" style="margin-right: 10px; width: auto;"></el-input>
      <el-input v-model.trim="dialogFilters.locationPointName" placeholder="定位点名称" style="margin-right: 10px; width: auto;"></el-input>
      <el-button type="primary" plain @click="_resetCondition">重置</el-button>
      <el-button type="primary" @click="_searchByCondition">查询</el-button>
    </div>
    <div class="hasSelected">
      <div style="color: #5b5b5b; font-weight: 600;">已选择：</div>
      <span v-if="!multipleSelection.length">暂无</span>
      <div v-for="(item, index) in multipleSelection" v-else :key="index" style="color: #5188fc;">
        <span>{{ item.locationPointName }}</span>
        <span v-show="multipleSelection.length > 1 && index !== multipleSelection.length - 1">、</span>
      </div>
    </div>
    <div class="table-list">
      <el-table
        ref="materialTable"
        v-loading="dialogtableLoading"
        :data="tableWz"
        :border="true"
        height="300px"
        stripe
        row-key="id"
        :cell-style="{ padding: '8px' }"
        style="overflow: auto;"
        :header-cell-style="$tools.setHeaderCell(3)"
        @selection-change="handleSelectionChangeDialog"
      >
        <el-table-column :reserve-selection="true" type="selection" width="55"></el-table-column>
        <el-table-column type="index" label="序号" width="65"></el-table-column>
        <el-table-column prop="locationPointCode" show-overflow-tooltip label="定位点编号"></el-table-column>
        <el-table-column prop="locationPointName" show-overflow-tooltip label="定位点名称"></el-table-column>
        <el-table-column prop="remarks" label="描述" show-overflow-tooltip></el-table-column>
        <el-table-column prop="createDate" label="创建时间" show-overflow-tooltip></el-table-column>
      </el-table>
    </div>
    <div class="table-page my_page">
      <el-pagination
        :current-page="paginationData.currentPage"
        :page-sizes="[15, 20, 30, 50]"
        :page-size="paginationData.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="paginationData.total"
        class="selectLocation"
        style="margin: 10px 0 15px;"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="sureWz">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  name: '',
  props: {
    title: {
      type: String,
      default: '选择定位点'
    },
    dialogVisibleWz: {
      type: Boolean,
      default: false
    },
    flag: {
      type: String,
      default: 'maintainFlag'
    }
  },
  data() {
    return {
      dialogFiltersSelectArr: [],
      workTypeCodeList: [],
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      props: {
        label: 'gridName',
        value: 'id',
        children: 'children'
      },
      multipleSelection: [],
      tableWz: [],
      dialogtableLoading: false,
      dialogFilters: {
        locationPointCode: '',
        locationPointName: ''
      },
      buttonLoading: false,
      tableSort: '',
      tableOrderBy: '',
      LOGINDATA: ''
    }
  },
  created() {
    // 获取登录信息
    this.LOGINDATA = JSON.parse(sessionStorage.getItem('LOGINDATA'))
    // 根据条件去判断执行获取方法(判断条件待定)
    if (this.LOGINDATA.hospitalCode == process.env.HOSPITALCODE) {
      this._findLocationPointGetBeacon()
    } else {
      // 获取定位点列表
      this._findLocationPointList()
    }
  },
  methods: {
    // 查询表格
    _findLocationPointList() {
      this.tableLoading = true
      let obj = this.LOGINDATA
      const { dialogFilters, paginationData } = this
      let data = {
        locationPointName: dialogFilters.locationPointName,
        locationPointCode: dialogFilters.locationPointCode,
        pageNo: paginationData.currentPage,
        pageSize: paginationData.pageSize
      }
      this.$api.ipsmFindLocationPointList(data).then((res) => {
        this.tableLoading = false
        if (res.code == 200) {
          this.tableWz = res.data.list
          this.paginationData.total = parseInt(res.data.count)
          if (res.data.list.length == 0) {
            this.emptyText = '未查到任何记录，请放大查询条件试试！'
          }
        }
        this.tableLoading = false
      })
    },
    // 查询表格（GetBeacon）
    _findLocationPointGetBeacon() {
      this.tableLoading = true
      let obj = this.LOGINDATA
      const { dialogFilters, paginationData } = this
      let data = {
        locationPointName: dialogFilters.locationPointName,
        locationPointCode: dialogFilters.locationPointCode,
        pageNo: paginationData.currentPage,
        pageSize: paginationData.pageSize
      }
      this.$api.findLocationPointGetBeacon(data).then((res) => {
        this.tableLoading = false
        if (res.code == 200) {
          this.tableWz = res.data.list
          this.paginationData.total = parseInt(res.data.count)
          if (res.data.list.length == 0) {
            this.emptyText = '未查到任何记录，请放大查询条件试试！'
          }
        }
        this.tableLoading = false
      })
    },
    // 条件查询
    _searchByCondition() {
      this.paginationData.currentPage = 1
      if (this.LOGINDATA.hospitalCode == process.env.HOSPITALCODE) {
        this._findLocationPointGetBeacon()
      } else {
        // 获取定位点列表
        this._findLocationPointList()
      }
    },
    // 重置查询条件
    _resetCondition() {
      this.paginationData = {
        currentPage: 1,
        pageSize: 15,
        total: 0
      }
      this.dialogFilters = {
        locationPointCode: '',
        locationPointName: ''
      }
      if (this.LOGINDATA.hospitalCode == process.env.HOSPITALCODE) {
        this._findLocationPointGetBeacon()
      } else {
        // 获取定位点列表
        this._findLocationPointList()
      }
    },
    handleSelectionChangeDialog(val) {
      this.multipleSelection = val
    },
    closeDialog() {
      this.dialogFilters = {
        locationPointCode: '',
        locationPointName: ''
      }
      this.$emit('closeDialog')
    },
    // 点击确定
    sureWz() {
      this.$emit('multipleSelection', this.multipleSelection)
      this.closeDialog()
    },
    handleSizeChange(val) {
      this.paginationData.currentPage = 1
      this.paginationData.pageSize = val
      // this._findLocationPointList()
      if (this.LOGINDATA.hospitalCode == process.env.HOSPITALCODE) {
        this._findLocationPointGetBeacon()
      } else {
        // 获取定位点列表
        this._findLocationPointList()
      }
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      // this._findLocationPointList()
      if (this.LOGINDATA.hospitalCode == process.env.HOSPITALCODE) {
        this._findLocationPointGetBeacon()
      } else {
        // 获取定位点列表
        this._findLocationPointList()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.sino-dialog-location {
  .hasSelected {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
  }
}

.selectLocation :deep(.el-input__inner) {
  height: 25px !important;
  line-height: 25px !important;
}

:deep(.el-dialog) {
  height: 580px;
}

:deep(.el-dialog) {
  .el-dialog__body {
    padding: 20px 20px 10px;
  }

  .el-dialog__footer {
    padding: 0 20px;
  }

  .el-input__inner {
    height: 30px !important;
  }
}
</style>
