<template>
  <el-dialog title="呼叫记录" width="45%" :visible.sync="callRecordDialogShow" custom-class="model-dialog"
    :before-close="closeDialog">
    <div class="content">
      <el-form ref="form" :model="itemInfo" label-width="120px">
        <div class="row_item">
          <el-form-item label="呼叫类型：" prop="channelNumber">
            <div class="input_width">
              <span>{{itemInfo.callTypeVal}}</span>
            </div>
          </el-form-item>
          <el-form-item label="通话时长：" prop="groupName">
            <div class="input_width">
              <span>{{itemInfo.timeDuration}}</span>
            </div>
          </el-form-item>
        </div>
        <div class="row_item">
          <el-form-item label="通话开始时间：" prop="remarks">
            <div class="input_width">
              <span>{{itemInfo.startTime}}</span>
            </div>
          </el-form-item>
          <el-form-item label="通话结束时间：">
            <div class="input_width">
              <span>{{itemInfo.endTime}}</span>
            </div>
          </el-form-item>
        </div>
        <div class="row_item">
          <el-form-item label="被叫人：">
            <div class="call_class">
              <span>{{itemInfo.destinationNumber}}</span>
            </div>
          </el-form-item>
        </div>
        <div class="row_item">
          <el-form-item label="通话记录" prop="office">
            <div v-if="itemInfo.callType == '1'||itemInfo.callType=='2'">
              <audio v-for="(item,index) in itemInfo.filePath" :key="index" :src="item" style="height: 30px"
                controls="controls"></audio>
            </div>
            <div v-if="itemInfo.callType == '7'">
              <video v-for="(item,index) in itemInfo.filePath" id="videoElement" :key="index" ref="videoElement"
                autoplay :src="item" controls muted width="100%" height="100%"></video>
            </div>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'callRecord',
  components: {},
  props: {
    callRecordDialogShow: {
      type: Boolean,
      default: false
    },
    itemInfo: {
      type: Object,
      default: () => { }
    },
    callFileList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {

    }
  },
  mounted() {
  },
  methods: {
    closeDialog() {
      this.$emit('closeCallRecordDialog')
    }
  }
}
</script>
<style lang="scss" scoped>
.model-dialog {
  .content {
    margin: 0 auto;
    background: #fff;
    border-radius: 4px;
    width: 100%;
    height: 380px;
    overflow: auto;
    padding-right: 20px;
  }
  .input_width {
    width: 300px;
    .el-select,
    .el-date-editor.el-input,
    .el-cascader {
      width: 100%;
    }
  }
  .call_class {
    > span {
      margin-right: 16px;
    }
  }
}
</style>

