<!--
 * @Description: 安全管理
-->
<template>
  <div ref="boxContent" class="securityOrganization">
      <div class="ability-icon" @click="fullScreen('boxContent')">
        <i class="screen-icon" :class="isFullScreen ? 'exit-full-screen' : 'full-screen'"></i>
        <span>全屏</span>
      </div>
    <div ref="treeParent" class="securityOrganization-scroll">
      <div ref="treeContent" class="securityOrganization-content" @mousedown.stop="move">
        <tree-node :tree-data="treeData" :tree-first="true" />
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'securityOrganization',
  components: {
    TreeNode: () => import('../components/TreeNode.vue')
  },
  data() {
    return {
      isFullScreen: false, // 是否全屏
      treeData: [{ id: '#', level: 0, name: '各安全组织人数', userCount: 0, children: [] }]
    }
  },
  mounted() {
    window.onresize = () => {
      debugger
      // 可视区域的高度
      const clientHeight = document.documentElement.clientHeight || document.body.clientHeight
      // screen是window的属性方法，window.screen可省略window，指的是窗口
      this.isFullScreen = screen.height == clientHeight
      this.initTreeContent()
    }
    this.getSafetyOrganizationData()
  },
  methods: {
    // 获取安全组织数据
    getSafetyOrganizationData() {
      this.$api.getSecurityControlPersonCount().then((res) => {
        if (res.code == '200') {
          const data = res.data
          this.treeData[0].children = data
          // 从树形结构中的data获取所有层级的人数
          this.treeData[0].userCount = this.getTreeUserCount(data)
          this.initTreeContent()
        }
      })
    },
    getTreeUserCount(data) {
      let count = 0
      data.forEach((item) => {
        count += item.userCount
        if (item.children && item.children.length > 0) {
          count += this.getTreeUserCount(item.children)
        }
      })
      return count
    },
    // 拖拽移动
    move(e) {
      // return
      const odiv = e.currentTarget // 获取元素
      // 算出鼠标相对元素的位置
      const disX = e.clientX - odiv.offsetLeft
      const disY = e.clientY - odiv.offsetTop
      document.onmousemove = (e) => {
        // 鼠标按下并移动的事件
        // 用鼠标的位置减去鼠标相对元素的位置，得到元素的位置
        const left = e.clientX - disX
        const top = e.clientY - disY
        // 绑定元素位置到positionX和positionY上面
        this.positionX = top
        this.positionY = left
        // 移动当前元素
        odiv.style.left = left + 'px'
        odiv.style.top = top + 'px'
      }
      document.onmouseup = () => {
        document.onmousemove = null
        document.onmouseup = null
      }
    },
    initTreeContent() {
      this.$nextTick(() => {
        const boxParent = this.$refs.treeParent
        const boxContent = this.$refs.treeContent
        if (boxContent.offsetWidth && (boxParent.offsetWidth > boxContent.offsetWidth)) {
          boxContent.style.left = `calc(50% - ${boxContent.offsetWidth / 2}px)`
          boxContent.style.top = 0
          // boxContent.style.top = `calc(50% - ${boxContent.offsetHeight / 2}px)`
        } else {
          boxContent.style.left = 0
          boxContent.style.top = 0
        }
      })
    },
    // 全屏事件
    fullScreen(dom) {
      this.isFullScreen = !this.isFullScreen
      const full = this.$refs[dom]
      if (this.isFullScreen) {
        if (full.RequestFullScreen) {
          full.RequestFullScreen()
          // 兼容Firefox
        } else if (full.mozRequestFullScreen) {
          full.mozRequestFullScreen()
          // 兼容Chrome, Safari and Opera等
        } else if (full.webkitRequestFullScreen) {
          full.webkitRequestFullScreen()
          // 兼容IE/Edge
        } else if (full.msRequestFullscreen) {
          full.msRequestFullscreen()
        }
      } else {
        if (document.exitFullscreen) {
          document.exitFullscreen()
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen()
        } else if (document.webkitCancelFullScreen) {
          document.webkitCancelFullScreen()
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen()
        }
      }
      this.initTreeContent()
    }
  }
}
</script>

<style lang="scss" scoped>
.securityOrganization {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
  .ability-icon {
    position: absolute;
    top: 8px;
    right: 15px;
    z-index: 999;
    cursor: pointer;
    height: 12px;
    .screen-icon {
      display: inline-block;
      vertical-align: baseline;
      width: 12px;
      height: 12px;
      margin-right: 4px;
      color: #fff;
    }
    span {
      font-size: 12px;
      color: #ffffff;
    }
    .full-screen {
      background: url('~@/assets/images/elevator/full-screen.png') no-repeat;
      background-size: 100% 100%;
    }
    .exit-full-screen {
      background: url('~@/assets/images/elevator/exit-full-screen.png') no-repeat;
      background-size: 100% 100%;
    }
  }
  &-scroll {
    width: 100%;
    height: 100%;
    overflow: auto;
    position: relative;
    // chrome隐藏滚动条
    &::-webkit-scrollbar {
      display: none;
    }
  }
  &-content {
    background: center;
    position: absolute;
    // min-width: 100%;
    width: fit-content;
    height: 100%;
    top: 0;
    left: 0;
  }
}
</style>
