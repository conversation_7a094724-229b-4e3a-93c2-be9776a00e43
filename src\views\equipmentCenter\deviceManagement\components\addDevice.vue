<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div v-loading="loadLoading" class="content_box">
        <el-form ref="formInline" :model="formInline" :inline="true" class="form-inline" label-width="100px"
          :rules="rules">
          <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
            <span class="green_line"></span>
            <span>基本信息</span>
            <div class="lifeClass">
              <div v-show="query.configType == '0'">已过期</div>
              <div v-show="query.configType == '1'">即将到期</div>
            </div>
          </div>
          <el-form-item label="资产名称：" prop="assetName">
            <el-input v-model.trim="formInline.assetName" maxlength="100" show-word-limit :readonly="readonly"
              type="text" placeholder="资产名称" :disabled="query.type == 'details'"></el-input>
          </el-form-item>
          <el-form-item label="资产编码：" prop="assetCode">
            <el-input v-model.trim="formInline.assetCode" maxlength="30" show-word-limit :readonly="readonly"
              type="text" placeholder="资产编码" :disabled="query.type == 'details'"></el-input>
          </el-form-item>
          <el-form-item label="品牌：" prop="assetBrand">
            <el-input v-model.trim="formInline.assetBrand" maxlength="100" show-word-limit :readonly="readonly"
              type="text" placeholder="品牌" :disabled="query.type == 'details'"></el-input>
          </el-form-item>
          <el-form-item label="型号：" prop="assetModel">
            <el-input v-model.trim="formInline.assetModel" maxlength="100" show-word-limit :readonly="readonly"
              type="text" placeholder="型号" :disabled="query.type == 'details'"></el-input>
          </el-form-item>
          <el-form-item label="生产日期：" prop="dateOfManufacture">
            <el-date-picker v-model="formInline.dateOfManufacture" :readonly="readonly" type="date"
              value-format="yyyy-MM-dd" placeholder="选择日期" :disabled="query.type == 'details'">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="SN码：" prop="assetSn">
            <el-input v-model.trim="formInline.assetSn" maxlength="100" show-word-limit type="text" :readonly="readonly"
              placeholder="SN码" :disabled="query.type == 'details'"></el-input>
          </el-form-item>
          <br />
          <el-form-item label="NFC：" prop="nfcCode">
            <el-input v-model.trim="formInline.nfcCode" maxlength="100" show-word-limit type="text" :readonly="readonly"
              onkeyup="this.value=this.value.replace(/[^\w]/g,'')" placeholder="NFC"
              :disabled="query.type == 'details'"></el-input>
          </el-form-item>
          <!-- rfid -->
          <el-form-item label="RFID：" prop="rfid">
            <el-input v-model.trim="formInline.rfid" maxlength="100" show-word-limit type="text" :readonly="readonly"
              onkeyup="this.value=this.value.replace(/[^\w]/g,'')" placeholder="RFID"
              :disabled="query.type == 'details'"></el-input>
          </el-form-item>
          <br />
          <el-form-item label="所在区域：" prop="regionCode">
            <el-cascader ref="regionCode" v-model="formInline.regionCode" :props="riskPropsType"
              :options="regionCodeList" :collapse-tags="true" placeholder="请选择区域" :disabled="query.type == 'details'"
              class="cascaderWid" :show-all-levels="true" @change="hangdleChange"></el-cascader>
          </el-form-item>
          <br />
          <el-form-item label="计量单位：" prop="unitOfMeasurementCode">
            <el-select v-model.trim="formInline.unitOfMeasurementCode" :readonly="readonly" filterable
              placeholder="计量单位" :disabled="query.type == 'details'">
              <el-option v-for="item in companyList" :key="item.dictValue" :label="item.dictName"
                :value="item.dictValue"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="启用日期：" prop="startDate">
            <el-date-picker v-model="formInline.startDate" :readonly="readonly" type="date" value-format="yyyy-MM-dd"
              placeholder="选择日期" :disabled="query.type == 'details'">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="使用期限：" prop="serviceLife">
            <el-input v-model="formInline.serviceLife" maxlength="10" show-word-limit
              oninput="value=value.replace(/[^\d.]/g,'')" :disabled="query.type == 'details'" :readonly="readonly"
              placeholder="请输入期限" class="month">
              <template slot="append">月</template>
            </el-input>
          </el-form-item>
          <el-form-item label="资产状态：" prop="assetStatusCode">
            <el-select v-model.trim="formInline.assetStatusCode" :readonly="readonly" filterable placeholder="资产状态"
              :disabled="query.type == 'details'">
              <el-option v-for="item in statusList" :key="item.dictValue" :label="item.dictName"
                :value="item.dictValue">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="使用时长：" prop="useDuration">
            <el-input v-model="formInline.useDuration" show-word-limit oninput="value=value.replace(/[^\d.]/g,'')"
              :disabled="query.type == 'details'" :readonly="readonly" placeholder="使用时长" class="month">
              <template slot="append">月</template>
            </el-input>
          </el-form-item>
          <el-form-item label="是否危险源：" prop="">
            <el-radio-group v-model="formInline.isDangerSource" :disabled="query.type == 'details'">
              <el-radio label="1">是</el-radio>
              <el-radio label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <br />
          <el-form-item style="display: block" label="备注说明：" prop="assetsRemarks">
            <el-input v-model.trim="formInline.assetsRemarks" maxlength="200" show-word-limit :readonly="readonly"
              type="textarea" placeholder="备注说明" class="cascaderWid" :disabled="query.type == 'details'"></el-input>
          </el-form-item>
          <!-- 资产归口部门 -->
          <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
            <span class="green_line"></span>
            资产归口部门
          </div>
          <el-form-item label="归口部门：" prop="centralizedDepartmentCode">
            <el-select v-model.trim="formInline.centralizedDepartmentCode" :readonly="readonly" filterable
              placeholder="归口部门" :disabled="query.type == 'details'">
              <el-option v-for="item in useDeptList" :key="item.id" :label="item.deptName" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <!-- 资产国标分类 -->
          <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
            <span class="green_line"></span>
            资产国标分类
          </div>
          <el-form-item label="资产大类：" prop="assetCategoryCode">
            <el-select v-model.trim="formInline.assetCategoryCode" :readonly="readonly" filterable placeholder="资产大类"
              :disabled="query.type == 'details'" @change="onAssetCategory">
              <el-option v-for="item in majorCategoriesList" :key="item.id" :label="item.dictName" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="资产小类：" prop="assetSubcategoryCode">
            <el-cascader ref="assetSubcategoryCode" :key="assetIndex" v-model="formInline.assetSubcategoryCode"
              :props="assetPropsType" :options="subcategoryList" :collapse-tags="true" placeholder="资产小类"
              :disabled="query.type == 'details'" @change="handleAssetClick"></el-cascader>
          </el-form-item>
          <!-- 类别信息 -->
          <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
            <span class="green_line"></span>
            类别信息
          </div>
          <el-form-item label="专业类别：" prop="professionalCategoryCode">
            <el-select v-model.trim="formInline.professionalCategoryCode" :readonly="readonly" filterable
              placeholder="专业类别" :disabled="query.type == 'details'" @change="onMajorType">
              <el-option v-for="item in majorList" :key="item.id" :label="item.baseName" :value="item.id"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="系统类别：" prop="systemCategoryCode">
            <el-cascader ref="systemCategoryCode" :key="systemIndex" v-model="formInline.systemCategoryCode"
              :props="systemPropsType" :options="systemCodeList" :collapse-tags="true" placeholder="系统类别"
              :disabled="query.type == 'details'" @change="handleSystemClick"></el-cascader>
          </el-form-item>
          <!-- 使用信息 -->
          <div class="toptip" style="padding: 0; border: 0; font-weight: bold"><span class="green_line"></span>使用信息
          </div>
          <el-form-item label="使用部门：" prop="userDepartmentId">
            <el-select ref="userDepartmentId" v-model="formInline.userDepartmentId" placeholder="请选择部门"
              :disabled="query.type == 'details'" clearable filterable @change="selectDept">
              <el-option v-for="item in useDeptList" :key="item.id" :label="item.deptName" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item style="display: block" label="备注：" prop="remarks">
            <el-input v-model.trim="formInline.remarks" maxlength="200" show-word-limit :readonly="readonly"
              type="textarea" placeholder="备注" class="cascaderWid" :disabled="query.type == 'details'"></el-input>
          </el-form-item>
          <el-form-item label="资产图片：" prop="assetImages">
            <template v-if="query.type == 'details'">
              <el-image v-if="fileList.length > 0" style="width: 100px; height: 100px" :src="fileList[0].url"
                :preview-src-list="fileList.map((i) => i.url)"> </el-image>
              <span v-else>暂无</span>
            </template>
            <template v-else>
              <el-upload action="" list-type="picture-card" :file-list="fileList" :on-change="fileChange"
                :on-preview="handlePictureCardPreview" :on-remove="deletImg" :http-request="handleUpload"
                :before-upload="beforeUpload" :class="{ hide: hideUpload }" accept=".jpg,.jpeg,.png" :limit="1">
                <i class="el-icon-plus"></i>
              </el-upload>
            </template>
            <el-dialog :visible.sync="imgVisible" :append-to-body="true">
              <img width="100%" :src="assetsImgUrl" alt="" />
            </el-dialog>
          </el-form-item>
          <!-- 模型信息 -->
          <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
            <span class="green_line"></span>
            模型信息
          </div>
          <el-form-item label="模型编码：" prop="modelCode">
            <el-input v-model.trim="formInline.modelCode" maxlength="64" show-word-limit :readonly="readonly"
              type="text" placeholder="模型编码" :disabled="query.type == 'details'"></el-input>
          </el-form-item>
          <!-- 模型信息 -->
          <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
            <span class="green_line"></span>
            业务信息
          </div>
          <el-form-item label="主机号：" prop="deviceHostNumber">
            <el-input v-model.trim="formInline.deviceHostNumber" :readonly="readonly" type="text" placeholder="主机号"
              :disabled="query.type == 'details'"></el-input>
          </el-form-item>
          <el-form-item label="设备号：" prop="equipmentNo">
            <el-input v-model.trim="formInline.equipmentNo" :readonly="readonly" type="text" placeholder="设备号"
              :disabled="query.type == 'details'"></el-input>
          </el-form-item>
          <el-form-item label="关联摄像机：">
            <el-button v-if="query.type != 'details'" class="form-btn-btn" type="primary"
              @click="associatedCamera()">选择</el-button>
            <template v-if="imsVidiconList.length > 0">
              <el-tag v-for="tag in imsVidiconList" :key="tag.imsVidiconId" class="camera-tag"
                :closable="query.type != 'details'" @close="tagHandleClose(tag.imsVidiconId)">
                {{ tag.imsVidiconName }}
              </el-tag>
            </template>
            <span v-else>暂无</span>
          </el-form-item>
          <br />
          <el-form-item label="用户名：" prop="deviceUsername">
            <el-input v-model.trim="formInline.deviceUsername" :readonly="readonly" type="text" placeholder="用户名"
              :disabled="query.type == 'details'"></el-input>
          </el-form-item>
          <el-form-item label="密码：" prop="devicePassword">
            <el-input v-model.trim="formInline.devicePassword" :readonly="readonly" type="text" placeholder="密码"
              :disabled="query.type == 'details'"></el-input>
          </el-form-item>
          <el-form-item label="端口号：" prop="devicePortNumber">
            <el-input v-model.trim="formInline.devicePortNumber" :readonly="readonly" type="text" placeholder="端口号"
              :disabled="query.type == 'details'"></el-input>
          </el-form-item>
          <el-form-item label="通道号：" prop="deviceChannelNumber">
            <el-input v-model.trim="formInline.deviceChannelNumber" :readonly="readonly" type="text" placeholder="通道号"
              :disabled="query.type == 'details'"></el-input>
          </el-form-item>
          <el-form-item label="设备IP：" prop="deviceIp">
            <el-input v-model.trim="formInline.deviceIp" :readonly="readonly" type="text" placeholder="设备IP"
              :disabled="query.type == 'details'" class="cascaderWid"></el-input>
          </el-form-item>
          <el-form-item label="对接方式：" prop="dockingMethodCode">
            <el-select v-model.trim="formInline.dockingMethodCode" :readonly="readonly" filterable placeholder="对接方式"
              :disabled="query.type == 'details'">
              <el-option v-for="item in dockingMethodArr" :key="item.dictCode" :label="item.dictName"
                :value="item.dictCode">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="厂家：" prop="equipmentManufacturerCode">
            <el-select v-model.trim="formInline.equipmentManufacturerCode" :readonly="readonly" filterable
              placeholder="厂家" :disabled="query.type == 'details'">
              <el-option v-for="item in manufacturerArr" :key="item.dictCode" :label="item.dictName"
                :value="item.dictCode">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="上级设备：" prop="equipmentManufacturerCode">
            <template>
              <div @click="addEquipment()">
                <el-input v-model.trim="formInline.superiorDeviceName" :disabled="query.type == 'details'"
                  placeholder="请选择上级设备"> </el-input>
              </div>
            </template>
            <!-- <el-select v-model.trim="formInline.equipmentManufacturerCode" :readonly="readonly" filterable
              placeholder="请选择上级设备" :disabled="query.type == 'details'">
              <el-option v-for="item in manufacturerArr" :key="item.dictCode" :label="item.dictName"
                :value="item.dictCode">
              </el-option>
            </el-select> -->
          </el-form-item>
          <el-form-item label="视频地址：" prop="mainCodeVideoAddress">
            <el-input v-model.trim="formInline.mainCodeVideoAddress" :readonly="readonly" type="text" placeholder="视频地址"
              :disabled="query.type == 'details'" class="cascaderWid"></el-input>
            <el-button v-if="query.type != 'details'" class="form-btn-btn" type="primary" :loading="generateLoading"
              @click="generate">生成</el-button>
          </el-form-item>
        </el-form>
      </div>
      <!-- 弹窗事件 -->
      <!-- 关联摄像机 -->
      <template v-if="cameraDialogShow">
        <cameraHKDialog :cameraDialogShow="cameraDialogShow" :cameraDialogData="cameraDialogData"
          @submitCameraDialog="submitCameraDialog" @closeCameraDialog="closeCameraDialog" />
        <!-- :limitSelectLength="limitSelectLength" -->
      </template>
      <template v-if="epuipmentVisible">
        <AddEquipment ref="addEquipment" :epuipmentVisible="epuipmentVisible" @closePinot="closePinot" @sure="sure">
        </AddEquipment>
      </template>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">关闭</el-button>
      <el-button v-if="$route.query.type != 'details'" type="primary" :loading="btnLoading"
        @click="submitForm()">保存</el-button>
    </div>
  </PageContainer>
</template>
<script>
import { transData } from '@/util'
import axios from 'axios'
import AddEquipment from './addEquipment.vue'
import cameraHKDialog from './cameraHKDialog.vue'
export default {
  name: 'addDevice',
  components: {
    cameraHKDialog,
    AddEquipment
  },
  beforeRouteEnter(to, from, next) {
    if (to.meta.title) {
      const typeList = {
        add: '新增资产',
        edit: '编辑资产',
        details: '资产详情'
      }
      to.meta.title = typeList[to.query.type] ?? '资产详情'
    }
    next()
  },
  data() {
    return {
      epuipmentVisible: false, // 选择上级设备弹窗
      defaultProps: {
        children: 'children',
        label: (data) => {
          return data.baseName
        },
        isLeaf: 'leaf'
      },
      riskPropsType: {
        children: 'children',
        label: 'ssmName',
        value: 'id',
        checkStrictly: true,
        multiple: false
      },
      assetPropsType: {
        children: 'children',
        label: 'dictName',
        value: 'id',
        checkStrictly: true,
        multiple: false
      },
      systemPropsType: {
        children: 'children',
        label: 'baseName',
        value: 'id',
        checkStrictly: true,
        multiple: false
      },
      treeData: [],
      btnLoading: false,
      loadLoading: false,
      query: '', // 路由传递参数
      readonly: false, // 是否只读
      formInline: {
        assetName: '',
        assetCode: '',
        assetBrand: '',
        assetModel: '',
        dateOfManufacture: '',
        assetSn: '',
        nfcCode: '',
        rfid: '',
        regionCode: [],
        unitOfMeasurementCode: '',
        superiorDeviceName: '', // 上级设备名称
        superiorDeviceCode: '', // 上级设备编码
        useDuration: '', // 使用时长
        startDate: '',
        serviceLife: '',
        assetStatusCode: '',
        isDangerSource: '0', // 是否为危险源
        centralizedDepartmentCode: '',
        assetCategoryCode: '',
        assetSubcategoryCode: '',
        professionalCategoryCode: '',
        systemCategoryCode: [],
        modelCode: '',
        deviceHostNumber: '', // 主机号
        equipmentNo: '', // 设备号
        cameraCode: '', // 关联摄像机code
        cameraName: '', // 关联摄像机名称
        deviceUsername: '', // 用户名
        devicePassword: '', // 密码
        devicePortNumber: '', // 端口号
        deviceChannelNumber: '', // 通道号
        deviceIp: '', // 设备IP
        dockingMethodCode: '', // 设备对接方式Code
        dockingMethodName: '', // 设备对接方式
        equipmentManufacturerCode: '', // 厂家Code
        equipmentManufacturerName: '', // 厂家
        mainCodeVideoAddress: '', // 视频地址（主码）
        assetsRemarks: '' // 资产备注说明
      },
      generateLoading: false,
      manufacturerArr: [], // 厂家list
      dockingMethodArr: [], //  对接方式list
      cameraDialogShow: false, // 摄像机
      rules: {
        assetName: [{ required: true, message: '请输入资产名称', trigger: 'blur' }],
        assetCode: [{ required: true, message: '请输入资产编码', trigger: 'blur' }],
        assetCategoryCode: [{ required: true, message: '请选择资产大类', trigger: 'change' }],
        assetSubcategoryCode: [{ required: true, message: '请选择资产小类', trigger: 'change' }],
        professionalCategoryCode: [{ required: true, message: '请选择专业类别', trigger: 'change' }],
        assetStatusCode: [{ required: true, message: '请选择资产状态', trigger: 'change' }]
      },
      regionCodeList: [], // 所在区域列表
      companyList: [], // 计量单位列表
      statusList: [], // 资产状态列表
      departmentList: [], // 部门归口列表
      majorCategoriesList: [], // 资产大类列表
      subcategoryList: [], // 资产小类列表
      majorList: [], // 专业类别列表
      // majorName: '', // 选择专业类别名称
      regionNameList: [], // 所选区域列表名称
      regionCodeAll: [], // 所有区域
      systemCodeList: [], // 系统类别
      systemCategoryName: '', // 系统名称
      systemAllList: [], // 系统类别全部
      subcategoryAll: [], // 资产小类全部
      assetSubcategoryName: '', // 选择资产小类名称
      assetIndex: 0, // 资产小类
      systemIndex: 0, // 系统类别
      isTreeData: false, // 是否有数据
      imsVidiconList: [],
      useDeptList: [], // 使用部门
      fileList: [],
      imgVisible: false,
      assetsImgUrl: '',
      hideUpload: false,
      cameraDialogData: ''
    }
  },
  watch: {
    'formInline.assetCategoryCode'(newVal) {
      this.assetIndex++
    },
    'formInline.professionalCategoryCode'(newVal) {
      this.systemIndex++
    }
  },
  created() {
    this.init()
    this.query = this.$route.query
  },
  mounted() {
    if (this.$route.query.id) {
      // 判断当isTreeData为true时，说明数据已经加载完成，可以进行回显
      this.$watch(
        'isTreeData',
        (newVal) => {
          if (newVal) {
            this.getDetails()
          }
        },
        { immediate: true }
      )
    }
    if (this.query.type == 'details') {
      this.readonly = true
    }
  },
  methods: {
    init() {
      this.getSelectByList('24', 'statusList') // 资产状态
      this.getSelectByList('26', 'companyList') // 计量单位
      // this.getSelectByList('28', 'departmentList') // 归口部门归口
      this.getUseDeptList()
      this.$api
        .sysDictData({
          pageSize: 9999,
          pageNo: 1,
          dictType: 'equipment_manufacturer'
        })
        .then((res) => {
          if (res.code == 200) {
            this.manufacturerArr = res.data.list
          } else {
            this.$message.error(res.message)
          }
        })
      this.$api
        .sysDictData({
          pageSize: 9999,
          pageNo: 1,
          dictType: 'equipment_docking_method'
        })
        .then((res) => {
          if (res.code == 200) {
            this.dockingMethodArr = res.data.list
          } else {
            this.$message.error(res.message)
          }
        })
      // 资产大类接口
      this.$api.getDictMenuTree({ dictType: 'ZCFL', pid: '#' }).then((res) => {
        if (res.code == '200') {
          this.majorCategoriesList = res.data
        }
      })
      // 专业类别
      this.$api
        .getDeviceType({
          levelType: 1
        })
        .then((res) => {
          if (res.code == '200') {
            this.majorList = res.data
          }
        })
      // 所在区域
      this.$api.spaceTree().then((res) => {
        this.isTreeData = true
        if (res.code == '200') {
          this.regionCodeList = transData(res.data, 'id', 'pid', 'children')
          this.regionCodeAll = res.data
        }
      })
    },
    // 查询二级以下设备字典
    onMajorType(val) {
      this.systemCodeList = []
      let data = {
        startLevel: '2',
        parentId: val,
        levelType: '5'
      }
      this.$api.getDeviceType(data).then((res) => {
        if (res.code == '200') {
          this.systemCodeList = transData(res.data, 'id', 'parentId', 'children')
          this.systemAllList = res.data
          ++this.systemIndex
        }
      })
    },
    // 查询资产小类
    onAssetCategory(id) {
      this.subcategoryList = []
      let data = {
        current: 1,
        size: 1000000,
        dictTypeId: '27',
        pids: id
      }
      this.$api.selectByPage(data).then((res) => {
        if (res.code == '200') {
          this.subcategoryList = transData(res.data.records, 'id', 'pid', 'children')
          this.subcategoryAll = res.data.records
          ++this.assetIndex
        }
      })
    },
    // 选择资产小类
    handleAssetClick(type) {
      const names = []
      type.forEach((i) => {
        this.subcategoryAll.find((j) => {
          if (i == j.id) {
            names.push(j.dictName)
          }
        })
      })
      this.assetSubcategoryName = names.join(',')
    },
    // 选择下拉树 数据
    handleSystemClick(type) {
      const names = []
      type.forEach((i) => {
        this.systemAllList.find((j) => {
          if (i == j.id) {
            names.push(j.baseName)
          }
        })
      })
      this.systemCategoryName = names.join(',')
    },
    // 使用科室
    getUseDeptList() {
      this.$api.getSelectedDept().then((res) => {
        if (res.code == '200') {
          this.useDeptList = transData(res.data, 'id', 'parentId', 'children')
        }
      })
    },
    selectDept(val) {
      this.formInline.userDepartmentName = this.useDeptList.filter((el) => {
        return val === el.id
      })[0].deptName
    },
    // 查询资产字典
    getSelectByList(str, getArr) {
      this.$api.selectByListAsset({ dictTypeId: str }).then((res) => {
        if (res.code == '200') {
          this[getArr] = res.data
        }
      })
    },
    // 所在区域选择
    hangdleChange(type) {
      const names = []
      type.forEach((i) => {
        this.regionCodeAll.find((j) => {
          if (i == j.id) {
            names.push(j.ssmName)
          }
        })
      })
      this.regionNameList = names.join('>')
    },
    // 根据最后一级选中的值回显整个选中的数组  即通过最后一级的id 获取整个路径的数组
    getPath(treeData, targetId) {
      let path = []
      let toggle = false
      for (let i = 0; i < treeData.length; i++) {
        let item = treeData[i]
        path.push(item.id)
        if (item.id === targetId) {
          toggle = true
          break
        } else {
          if (item.children) {
            toggle = this.findPath(item.children, targetId, path, toggle)
            if (!toggle) {
              path.pop()
            } else {
              break
            }
          } else {
            path.pop()
          }
        }
      }
      if (toggle) {
        return path
      } else {
        return []
      }
    },
    findPath(nodes, targetId, path, toggle) {
      for (let i = 0; i < nodes.length; i++) {
        let item = nodes[i]
        path.push(item.id)
        if (item.id === targetId) {
          return true
        } else {
          if (item.children) {
            toggle = this.findPath(item.children, targetId, path, toggle)
            if (toggle) {
              return toggle
            } else {
              path.pop()
            }
          } else {
            path.pop()
          }
        }
      }
      return toggle
    },
    // 获取资产详情
    getDetails() {
      let arr = ''
      this.$api.getAssetDetails({ id: this.query.id }).then((res) => {
        if (res.code == '200') {
          res.data.assetStatusCode == '0' ? (res.data.assetStatusCode = '') : res.data.assetStatusCode
          this.formInline = res.data
          if (res.data.regionCode) {
            arr = res.data.regionCode.split(',')
            this.formInline.regionCode = this.getPath(this.regionCodeList, arr[arr.length - 1])
          } else {
            this.formInline.regionCode = []
          }
          if (res.data.systemCategoryCode) {
            this.formInline.systemCategoryCode = res.data.systemCategoryCode.split(',')
          }
          // 处理图片回显
          if (res.data.assetImagesUrl) {
            this.fileList = [
              {
                url: this.$tools.imgUrlTranslation(res.data.assetImagesUrl)
              }
            ]
          }
          this.hideUpload = !!res.data.assetImagesUrl
          this.formInline.assetSubcategoryCode = res.data.assetSubcategoryCode ? res.data.assetSubcategoryCode.split(',') : ''
          this.onMajorType(this.formInline.professionalCategoryCode)
          this.onAssetCategory(this.formInline.assetCategoryCode)
          this.regionNameList = res.data.regionName
          this.assetSubcategoryName = res.data.assetSubcategoryName // 资产小类
          this.systemCategoryName = res.data.systemCategoryName // 系统类别
          const imsVidiconId = res.data.cameraCode ? res.data.cameraCode.split(',') : []
          const imsVidiconName = res.data.cameraName ? res.data.cameraName.split(',') : []
          this.imsVidiconList =
            imsVidiconId?.map((item, index) => {
              return {
                imsVidiconId: item,
                imsVidiconName: imsVidiconName[index]
              }
            }) ?? []
        }
      })
    },
    // 选择上级设备
    addEquipment() {
      this.epuipmentVisible = true
    },
    // 关闭上级设备
    closePinot() {
      this.epuipmentVisible = false
    },
    // 确认上级设备
    sure(list) {
      this.formInline.superiorDeviceName = list[0].assetName
      this.formInline.superiorDeviceCode = list[0].assetCode
      this.epuipmentVisible = false
    },
    // 获取选择对应名称
    getName(str, getArr, type) {
      if (type === 'manufacturer' || type === 'dockingMethod') {
        let newObj = this[getArr].find((item) => str == item.dictCode)
        return newObj?.dictName
      } else if (type === 'useDeptList') {
        let newObj = this[getArr].find((el) => {
          return str === el.id
        })
        console.log(newObj, '111')
        return newObj?.deptName
      } else {
        let newObj = this[getArr].find((item) => str == item.dictValue)
        return newObj?.dictName
      }
    },
    // 获取资产大类名称
    getAssetsName(str, getArr) {
      let newObj = this[getArr].find((item) => str == item.id)
      return newObj?.dictName
    },
    // 获取专业类别名称
    getMajorName(str, getArr) {
      let newObj = this[getArr].find((item) => str == item.id)
      return newObj?.baseName
    },
    submitForm() {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          this.btnLoading = true
          this.loadLoading = true
          let data = {
            ...this.formInline,
            regionName: this.regionNameList, // 所在区域
            unitOfMeasurement: this.getName(this.formInline.unitOfMeasurementCode, 'companyList'), // 计量单位
            assetStatusName: this.getName(this.formInline.assetStatusCode, 'statusList'), // 资产状态名称
            centralizedDepartmentName: this.getName(this.formInline.centralizedDepartmentCode, 'useDeptList', 'useDeptList'), // 归口部门
            assetCategoryName: this.getAssetsName(this.formInline.assetCategoryCode, 'majorCategoriesList'), // 资产大类名称
            assetSubcategoryName: this.assetSubcategoryName, // 资产小类
            professionalCategoryName: this.getMajorName(this.formInline.professionalCategoryCode, 'majorList'), // 专业类别
            systemCategoryName: this.systemCategoryName, // 系统类别
            assetsId: this.$route.query.assetsId ? this.$route.query.assetsId : '',
            id: this.$route.query.id ? this.$route.query.id : '',
            regionCode: this.formInline.regionCode.length ? this.formInline.regionCode[this.formInline.regionCode.length - 1] : '',
            systemCategoryCode: this.formInline.systemCategoryCode ? this.formInline.systemCategoryCode.toString() : '',
            assetSubcategoryCode: this.formInline.assetSubcategoryCode ? this.formInline.assetSubcategoryCode.toString() : '',
            equipmentManufacturerName: this.getName(this.formInline.equipmentManufacturerCode, 'manufacturerArr', 'manufacturer'), // 厂家,
            dockingMethodName: this.getName(this.formInline.dockingMethodCode, 'dockingMethodArr', 'dockingMethod'), // 对接方式,
            cameraCode: Array.from(this.imsVidiconList, (e) => e.imsVidiconId).toString(), // 关联摄像头
            cameraName: Array.from(this.imsVidiconList, (e) => e.imsVidiconName).toString() // 关联摄像头
          }
          // 添加操作日志
          let header = {}
          if (data.id) {
            header = {
              'operation-type': 2,
              'operation-id': data.id,
              'operation-name': data.assetName
            }
          } else {
            header = {
              'operation-type': 1
            }
          }
          this.$api.saveOrUpdate(data, header).then((res) => {
            if (res.code == '200') {
              this.$message.success('保存成功')
              this.$router.go(-1)
            } else {
              this.$message.error(res.message)
            }
            this.btnLoading = false
            this.loadLoading = false
          })
        }
      })
    },
    // 摄像机
    associatedCamera() {
      this.cameraDialogData = Array.from(this.imsVidiconList, ({ imsVidiconId }) => imsVidiconId).toString()
      this.cameraDialogShow = true
    },
    // 摄像机事件------------start
    submitCameraDialog(data) {
      this.imsVidiconList = data.map((e) => {
        return {
          imsVidiconId: e.icmCode,
          imsVidiconName: e.icmName
        }
      })
      this.cameraDialogShow = false
      this.formInline.deviceIp = data[0].icmIp
      this.formInline.equipmentNo = data[0].icmCode
    },
    closeCameraDialog() {
      this.cameraDialogShow = false
    },
    // 摄像机tag事件
    tagHandleClose(id) {
      // 根据id 删除imsVidiconList中的对应项
      const index = this.imsVidiconList.findIndex((e) => e.imsVidiconId === id)
      this.imsVidiconList.splice(index, 1)
    },
    // 摄像机事件------------end
    // 生成视频
    generate() {
      const { equipmentManufacturerCode, deviceUsername, devicePassword, deviceIp } = this.formInline
      if (!equipmentManufacturerCode) {
        return this.$message.error('请选择厂家')
      }
      this.generateLoading = true
      const params = {
        equipmentManufacturerCode: equipmentManufacturerCode,
        deviceUsername: deviceUsername,
        devicePassword: devicePassword,
        deviceIp: deviceIp
      }
      this.$api.getVideoPath(params).then((res) => {
        if (res.code == '200') {
          this.formInline.mainCodeVideoAddress = res.data
          this.$message.success(res.message)
        } else {
          this.$message.error(res.message)
        }
        this.generateLoading = false
      })
    },
    // 新增编辑上传
    beforeUpload(file) {
      let fileName = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()
      if (fileName != 'png' && fileName != 'jpg' && fileName != 'bmp' && fileName != 'tif' && fileName != 'gif' && fileName != 'jpeg') {
        this.$message({
          message: '上传图片只能是png、jpg、jpeg、bmp、gif、tif格式',
          type: 'warning'
        })
        return false
      }
      let sizes = 0
      this.fileList.forEach((i) => {
        sizes += i.raw.size
      })
      if (sizes / 1024 > 10000) {
        this.$message({
          type: 'warning',
          message: '上传文件大小不能超过10MB'
        })
        return false
      }
    },
    // 图片上传
    handleUpload() {
      this.fileList.forEach((i) => {
        return (i.status = 'uploading')
      })
      const urlData = new FormData()
      this.fileList.forEach((item) => {
        urlData.append('file', item.raw)
        urlData.append('platform', 1)
      })
      axios({
        method: 'post',
        url: __PATH.VUE_ICIS_API + 'file/upload',
        data: urlData,
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      })
        .then((res) => {
          this.hideUpload = true
          this.fileList.forEach((i) => {
            return (i.status = 'done')
          })
          this.formInline.assetImages = res.data.data.fileKey
        })
        .catch(() => {
          this.$message.error(res.data.message)
        })
    },
    deletImg(file, fileList) {
      this.hideUpload = false
      this.formInline.assetImages = ''
      this.assetsImgUrl = ''
      this.fileList = fileList
    },
    handlePictureCardPreview(file) {
      this.assetsImgUrl = file.url
      this.imgVisible = true
    },
    fileChange(file, fileList) {
      this.fileList = fileList
    }
  }
}
</script>
<style lang="scss" scoped>
.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
  padding: 10px;
  overflow-y: auto;
}

.content_box {
  height: calc(100% - 30px);
  margin-top: 10px;
  padding: 20px 25px 25px;
  background: #fff;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.form-btn-btn {
  padding: 4px 10px;
  margin: 0 8px;
}

.camera-tag {
  background: #f6f5fa;
  border-radius: 4px;
  font-size: 14px;
  font-family: 'PingFang SC-Regular', 'PingFang SC';
  font-weight: 400;
  color: #121f3e;
  border: none;
  margin-right: 8px;

  ::v-deep .el-tag__close {
    color: #121f3e;

    &:hover {
      color: #fff;
      background-color: #3562db;
    }
  }
}

.hide {
  ::v-deep .el-upload--picture-card {
    display: none;
  }
}

.toptip {
  margin-bottom: 10px;
}

.form-inline {

  .el-input,
  .el-select,
  .el-cascader {
    width: 300px;
  }
}

.form-inline .width_lengthen {
  width: 300px;
}

.form-inline .cascaderWid {
  width: 710px;
}

.detailClass ::v-deep .el-input__inner {
  border: none !important;
}

.detailClass ::v-deep .el-input__suffix-inner {
  display: none !important;
}

.detailClass ::v-deep .el-input-group__append {
  display: none !important;
}

.lifeClass {
  display: flex;
  margin-left: 25px;

  >div {
    width: 100px;
    height: 100%;
    border: 1px solid #dcdfe6;
    text-align: center;
    border-radius: 5px;
  }

  :first-child {
    margin-right: 15px;
    background: #f2f3f5;
    color: #4e5969;
  }

  :last-child {
    background: #fff7e8;
    color: #ff7d00;
  }
}
</style>
