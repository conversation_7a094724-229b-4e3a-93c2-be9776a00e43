<template>
  <el-dialog
    :class="dialogContentType == 'customize' ? 'customizeDialog' : 'spaceDialog'"
    title="选择设备"
    :visible.sync="Visible"
    :close-on-click-modal="false"
    :modal-append-to-body="false"
    top="8vh"
    width="80%"
    :before-close="closePinot"
  >
    <div class="dialogContent">
      <div class="pointListWrap">
        <div class="spaceContent">
          <div class="leftTree">
            <div class="toptip" style="padding-left: 0; border: 0; font-weight: bold">
              <span class="green_line"></span>
              {{ '设备分类' }}
            </div>
            <el-input v-model="filterText" placeholder="输入关键字"> </el-input>
            <!-- <el-tree
              ref="tree"
              :data="treeData"
              :props="treeProps"
              :highlight-current="true"
              node-key="id"
              :filter-node-method="filterNode"
              @node-click="nodeClick">
            </el-tree> -->
            <!-- {{ treeData }} -->
            <ZkRenderTree
              ref="tree"
              :data="treeData"
              :props="treeProps"
              :highlight-current="true"
              node-key="id"
              :filter-node-method="filterNode"
              @node-click="nodeClick"
            ></ZkRenderTree>
          </div>
          <div class="rightTable">
            <div class="topFilter">
              <div class="deviceFilter">
                <el-input v-model="deviceFilter" placeholder="设备名称" @keyup.enter.native="deviceSearch"></el-input>
                <el-cascader
                  ref="regionCode"
                  v-model="regionCode"
                  :props="riskPropsType"
                  :options="regionCodeList"
                  placeholder="请选择所在区域"
                  class="cascaderWid"
                  :show-all-levels="false"
                  style="margin-right: 10px"
                ></el-cascader>
                <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db" @click="deviceReset">重置</el-button>
                <el-button type="primary" @click="deviceSearch">查询</el-button>
              </div>
            </div>
            <div class="selected">
              <span>已选择</span>
              <div class="tagsWrap">
                <el-tag v-for="tag in tags" :key="tag.id" type="info" closable @close="deleteTag(tag.id)">
                  {{ tag.assetName }}
                </el-tag>
              </div>
            </div>
            <div class="table">
              <el-table
                ref="deviceTable"
                v-loading="spaceLoading"
                :data="spaceTableData"
                :row-key="getRowKeys"
                style="width: 100%"
                height="265"
                border
                @select="selectRow"
                @select-all="selectAll"
              >
                <el-table-column align="center" :reserve-selection="true" type="selection" width="55"> </el-table-column>
                <el-table-column type="index" label="序号" width="60"> </el-table-column>
                <el-table-column prop="assetName" label="设备名称" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="assetsRemarks" label="备注说明" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="assetBrand" label="品牌" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="assetModel" label="型号" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="regionName" label="位置" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="assetStatusName" label="报废状态" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="professionalCategoryName" label="专业类别" show-overflow-tooltip> </el-table-column>
                <el-table-column label="操作">
                  <template slot-scope="scope">
                    <el-link type="primary" @click="detail(scope.row)">详情</el-link>
                  </template>
                </el-table-column>
              </el-table>
              <div class="" style="padding-top: 10px; text-align: right">
                <el-pagination
                  layout="total, sizes, prev, pager, next, jumper"
                  :current-page="paginationData.currentPage"
                  :page-sizes="[10, 30, 50, 100]"
                  :page-size="paginationData.pageSize"
                  :total="paginationData.total"
                  @size-change="sizeChange"
                  @current-change="currentChange"
                ></el-pagination>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db" @click="closePinot">取 消</el-button>
      <el-button type="primary" @click="checkConfirm">确 定</el-button>
    </span>
    <div>
      <el-dialog title="设备详情" :visible.sync="dialogDateilVisible" :append-to-body="true" :modal-append-to-body="false" class="inspectionPointDateil" top="18vh" width="55%">
        <div>
          <div class="detailDialog">
            <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
              <span class="green_line"></span>
              基本信息
            </div>
            <el-row :gutter="60">
              <el-col :span="8" class="cosRow">
                <div class="titleWrap">设备名称:</div>
                <div class="contenWrap">{{ deviceRowDetail.assetName }}</div>
              </el-col>
              <el-col :span="8" class="cosRow">
                <div class="titleWrap">设备编码:</div>
                <div class="contenWrap">{{ deviceRowDetail.assetCode }}</div>
              </el-col>
              <el-col :span="8" class="cosRow">
                <div class="titleWrap">设备ID:</div>
                <div class="contenWrap">{{ deviceRowDetail.id }}</div>
              </el-col>
              <el-col :span="8" class="cosRow">
                <div class="titleWrap">品牌:</div>
                <div class="contenWrap">{{ deviceRowDetail.assetBrand }}</div>
              </el-col>
              <el-col :span="16" class="cosRow">
                <div class="titleWrap">型号:</div>
                <div class="contenWrap">{{ deviceRowDetail.assetModel }}</div>
              </el-col>
              <el-col :span="8" class="cosRow">
                <div class="titleWrap">出厂日期:</div>
                <div class="contenWrap">{{ moment(deviceRowDetail.dateOfManufacture).format('YYYY-MM-DD') }}</div>
              </el-col>
              <el-col :span="16" class="cosRow">
                <div class="titleWrap">SN码:</div>
                <div class="contenWrap">{{ deviceRowDetail.assetSn }}</div>
              </el-col>
              <el-col :span="8" class="cosRow">
                <div class="titleWrap">启用日期:</div>
                <div class="contenWrap">{{ moment(deviceRowDetail.startDate).format('YYYY-MM-DD') }}</div>
              </el-col>
              <el-col :span="16" class="cosRow">
                <div class="titleWrap">使用期限:</div>
                <div class="contenWrap">{{ deviceRowDetail.serviceLife + '月' }}</div>
              </el-col>
              <el-col :span="12" class="cosRow">
                <div class="titleWrap">所在区域:</div>
                <div class="contenWrap">{{ deviceRowDetail.regionName }}</div>
              </el-col>
              <el-col :span="12" class="cosRow">
                <div class="titleWrap">使用状态:</div>
                <div class="contenWrap">{{ deviceRowDetail.assetStatusName }}</div>
              </el-col>
            </el-row>
          </div>
          <div class="detailDialog typeInfo">
            <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
              <span class="green_line"></span>
              类别信息
            </div>
            <el-row :gutter="60">
              <el-col :span="8" class="cosRow">
                <div class="titleWrap">归口部门:</div>
                <div class="contenWrap">{{ deviceRowDetail.centralizedDepartmentName }}</div>
              </el-col>
              <el-col :span="16" class="cosRow">
                <div class="titleWrap">归属系统:</div>
                <div class="contenWrap">{{ deviceRowDetail.systemCategoryName }}</div>
              </el-col>
            </el-row>
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db" @click="dialogDateilVisible = false">关闭</el-button>
        </span>
      </el-dialog>
    </div>
  </el-dialog>
</template>
<script>
import { transData } from '@/util'
import moment from 'moment'
export default {
  components: {},
  props: {
    Visible: {
      type: Boolean,
      default: false
    },
    projectIds: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      moment,
      systemType: '',
      dialogDateilVisible: false,
      spaceLoading: false,
      activeName: 'space',
      dialogContentType: 'space',
      allSpaceTreeData: [], // 未处理的空间树结构
      treeData: [], // 树数据来源
      spaceFunctionType: [], // 空间功能类型
      checkItem: '', // 当前树选中项
      spaceFilter: {
        functionDictId: '', // 已选功能类型
        localSpaceName: '', // 空间本地名称
        localSpaceCode: '' // 本地编码
      },
      spaceTableData: [], // 空间设备巡检点列表
      zdyTableData: [], // 自定义巡检点列表
      treeProps: {
        children: 'children',
        label: (data, node) => {
          return data.baseName
        },
        isLeaf: 'leaf'
      },
      filterText: '',
      deviceTree: [], // 设备分类
      deviceRowDetail: {}, // 设备详情
      deviceFilter: '',
      regionCode: [],
      riskPropsType: {
        children: 'children',
        label: 'ssmName',
        value: 'id',
        checkStrictly: true,
        multiple: false
      },
      regionCodeList: [],
      deviceProps: {
        label: 'baseName',
        isLeaf: 'leaf',
        children: 'children'
      },
      tags: [],
      // 空间设备分页
      paginationData: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      // 自定义分页
      zdyPaginationData: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      // 创建自定义巡检点
      customizeForm: {
        name: '',
        code: '',
        description: '',
        nfcCode: ''
      },
      rules: {
        name: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!value) {
                if (this.systemType == '2') {
                  callback(new Error('请输入保养点名称'))
                } else {
                  callback(new Error('请输入巡检点名称'))
                }
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        code: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!value) {
                if (this.systemType == '2') {
                  callback(new Error('请输入保养点编码'))
                } else {
                  callback(new Error('请输入巡检点编码'))
                }
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      },
      // 选中的自定义巡检点
      selectedZdyPoint: []
    }
  },
  created() {
    this.getDeviceList()
    this.getDeviceType()
    this.getRegionList()
  },
  mounted() {},
  methods: {
    getRowKeys(row) {
      return row.id
    },
    filterNode(value, data) {
      if (!value) return true
      return data.baseName.indexOf(value) !== -1
    },
    nodeClick(data, node) {
      if (data.levelType == '1') {
        this.checkItem = data.id
        this.getDeviceList(data.id)
      } else {
        this.checkItem = data.parentId + ',' + data.id
        this.getDeviceList(data.parentId + ',' + data.id)
      }
    },
    sizeChange(val) {
      this.paginationData.pageSize = val
      this.paginationData.currentPage = 1
      this.getDeviceList(this.checkItem)
    },
    currentChange(val) {
      this.paginationData.currentPage = val
      this.getDeviceList(this.checkItem)
    },
    // 设备分类列表
    getDeviceType() {
      this.$api.getDeviceType({ levelType: '1' }).then((res) => {
        if (res.code == '200') {
          this.treeData = transData(res.data, 'id', 'parentId', 'children')
          const unknown = {
            baseName: '未知',
            id: '-1',
            levelType: '1'
          }
          this.treeData.push(unknown)
          this.checkItem = this.treeData[0].id
          if (this.visible) {
            this.$nextTick(() => {
              this.$refs.tree.getTreeRef().setCurrentKey(this.checkItem)
            })
          }
          this.getDeviceList(this.checkItem)
        }
      })
    },
    // 设备列表
    getDeviceList(typeId) {
      this.spaceLoading = true
      this.$api
        .getDeviceList({
          professionalCategoryCode: typeId || '',
          assetName: this.deviceFilter,
          regionCode: this.regionCode.length > 0 ? this.regionCode[this.regionCode.length - 1] : '',
          pageSize: this.paginationData.pageSize,
          pageNo: this.paginationData.currentPage,
          deviceIds: this.projectIds
        })
        .then((res) => {
          if (res.code == '200') {
            res.data.list.forEach((i) => {
              if (i.regionName) {
                i.regionName = i.regionName.replace(/&gt;/g, '>')
              }
            })
            this.spaceTableData = res.data.list
            this.paginationData.total = res.data.total
            this.spaceLoading = false
          }
        })
    },
    closePinot() {
      this.$emit('closePinot')
    },
    // 设备列表过滤
    deviceSearch() {
      this.paginationData.currentPage = 1
      this.getDeviceList(this.checkItem)
    },
    // 设备重置
    deviceReset() {
      this.paginationData.currentPage = 1
      this.deviceFilter = ''
      this.regionCode = []
      this.getDeviceList(this.checkItem)
    },
    // 设备详情
    detail(row) {
      this.deviceRowDetail = row
      this.dialogDateilVisible = true
    },
    // 选择巡检点
    selectRow(selection, row) {
      this.tags = selection
      console.log(this.tags, 'this.tags111111111111111')
    },
    // 全选空间、设备巡检点
    selectAll(selection) {
      this.tags = selection
      console.log(this.tags, 'this.tags')
    },
    // 删除标签
    deleteTag(id) {
      this.tags.splice(
        this.tags.find((i) => i.id == id),
        1
      )
      const zdySelectRow = this.zdyTableData.find((i) => i.id == id)
      this.$refs.deviceTable.toggleRowSelection(zdySelectRow, false)
    },
    // 确认选择
    checkConfirm() {
      this.tags.forEach((i) => {
        i.projectId = ''
        i.projectName = ''
      })
      this.$emit('closePinot')
      this.$emit('confirm', this.tags)
      this.$refs.deviceTable.clearSelection()
    },
    getRegionList() {
      this.$api.spaceTree().then((res) => {
        if (res.code == '200') {
          this.regionCodeList = transData(res.data, 'id', 'pid', 'children')
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: linear-gradient(to right, #d9e1f8, #fff);
}
::v-deep .el-tree-node__content {
  height: 32px;
}
.dialogContent {
  height: 100%;
  .pointListWrap {
    height: 100%;
    .spaceContent {
      display: flex;
      justify-content: space-between;
      .leftTree {
        background-color: #fff;
        padding: 0 16px;
        width: 250px;
        height: 455px;
        .filter-tree {
          height: 343px;
          overflow: auto;
        }
        ::v-deep .custom-tree {
          height: calc(100% - 82px);
          overflow: auto;
        }
        // :deep(.el-tree) {
        // }
      }
      .rightTable {
        background-color: #fff;
        width: calc(100% - 268px);
        height: 455px;
        padding: 0 16px;
        .topFilter {
          width: 100%;
          padding: 16px 0 0;
          display: flex;
          justify-content: space-between;
          :deep(.el-input) {
            width: 200px;
          }
          .deviceFilter {
            :deep(.el-input) {
              width: 260px;
              margin-right: 15px;
            }
          }
        }
        .selected {
          height: 40px;
          margin: 10px 0;
          display: flex;
          align-items: center;
          > span {
            display: inline-block;
            margin-right: 10px;
            width: 42px;
          }
          .tagsWrap {
            width: calc(100% - 52px);
            height: 100%;
            display: flex;
            align-items: center;
            overflow-x: auto;
            overflow-y: hidden;
            > span {
              margin-right: 10px;
            }
          }
          .tagsWrap::-webkit-scrollbar {
            height: 10px;
          }
        }
        .table {
          :deep(.el-table) {
            .el-table__cell {
              padding: 10px 0;
            }
          }
        }
      }
    }
    .customizeContent {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .topBaseInfo {
        height: 200px;
        padding: 0 16px;
        width: 100%;
        background-color: #fff;
      }
      .customizePointList {
        margin-top: 24px;
        padding: 0 16px;
        height: calc(100% - 224px);
        background-color: #fff;
        .zdyTopTitle {
          display: flex;
          width: 100%;
          > .toptip {
            width: 130px !important;
            margin-right: 10px;
          }
          .selected {
            width: calc(100% - 140px);
            height: 40px;
            margin: 5px 0;
            display: flex;
            align-items: center;
            > span {
              display: inline-block;
              margin-right: 10px;
              width: 42px;
            }
            .tagsWrap {
              width: calc(100% - 52px);
              height: 100%;
              display: flex;
              align-items: center;
              overflow-x: auto;
              overflow-y: hidden;
              > span {
                margin-right: 10px;
              }
            }
            .tagsWrap::-webkit-scrollbar {
              height: 10px;
            }
          }
        }
        .table {
          :deep(.el-table) {
            .el-table__cell {
              padding: 10px 0;
            }
          }
        }
      }
    }
  }
}
.inspectionPointDateil {
  :deep(.el-dialog) {
    .el-dialog__header {
      border-bottom: 1px solid #dcdfe6;
    }
    .el-dialog__body {
      background-color: #f6f5fa;
      padding: 24px;
    }
    .el-dialog__footer {
      padding: 10px 20px;
    }
  }
  .detailDialog {
    background-color: #fff;
    padding: 0 16px;
    .cosRow {
      display: flex;
      align-items: center;
      padding: 12px 0;
      .titleWrap {
        color: #414653;
        width: 100px;
        text-align: right;
      }
      .contenWrap {
        color: #121f3e;
        margin-left: 14px;
      }
    }
  }
  .typeInfo {
    margin-top: 24px;
  }
}
.spaceDialog {
  > :deep(.el-dialog) {
    height: 620px;
  }
}
.customizeDialog {
  > :deep(.el-dialog) {
    height: 800px !important;
  }
}
:deep(.el-dialog) {
  .el-dialog__header {
    border-bottom: 1px solid #dcdfe6;
  }
  .el-dialog__body {
    background-color: #f6f5fa;
    height: calc(100% - 107px);
  }
  .el-dialog__footer {
    padding: 10px 20px;
  }
}
</style>
