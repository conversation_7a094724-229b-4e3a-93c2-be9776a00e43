<template>
  <div class="card">
    <ul>
      <li v-for="(item, index) in treeData" :key="index">
        <div class="item" :class="{ 'line-left': index !== 0, 'line-right': index != treeData.length - 1 }">
          <div class="item-name" :class="{ 'line-bottom': item.children && item.children.length > 0, 'line-top': !treeFirst }">
            <div v-if="item.level < 2" class="leader-group">
              <svg-icon name="work-people-icon" />
              <span class="item-name-text">{{ item.name }}</span>
              <span class="item-name-count">{{ item.userCount }}</span>
            </div>
            <div v-else class="work-group">
              <div class="item-people-icon"></div>
              <div class="item-name-text">{{ item.name }}</div>
              <div class="item-name-count">{{ item.userCount }}</div>
            </div>
          </div>
        </div>
        <tree-node v-if="item.children && item.children.length > 0" :tree-data="item.children" />
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'TreeNode',
  props: {
    treeData: {
      type: Array,
      default: () => []
    },
    treeFirst: {
      // 判断是否第一个
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  created() {},
  methods: {}
}
</script>

<style lang="scss" scoped>
$line-length: 20px; //线长
$spacing: 20px; //间距
$extend: calc(#{$spacing} + 2px); //延长线

ul {
  list-style: none; //去掉标签默认的左边符号
  display: flex;
  // flex-wrap: wrap;
  padding: 0;
  // width: max-content;
}
li {
  list-style-type: none; //去掉标签默认的左边符号
  text-align: center;
  flex-shrink: 0;
}
// 线样式
@mixin line {
  content: '';
  display: block;
  width: 1px;
  height: $line-length;
  position: absolute;
  left: 0;
  right: 0;
  margin: auto;
  background: #94b7ff;
}

.card {
  font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
  ul {
    display: flex;
    justify-content: center;
    li {
      .item {
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        &-name {
          position: relative;
          width: fit-content;
          margin: $spacing;
          .leader-group {
            display: flex;
            height: 38px;
            justify-content: center;
            align-items: center;
            padding: 0px 10px;
            background: url('@/assets/images/safetyDataCockpit/safety-org-text-bg.png') no-repeat;
            background-size: 100% 100%;
            color: #94b7ff;
            font-size: 14px;
          }
          &-text {
            margin: 0 4px;
          }
          &-count {
            font-weight: 500;
            font-size: 16px;
            color: #ffffff;
          }
          .work-group {
            display: flex;
            flex-direction: column;
            // justify-content: center;
            align-items: center;
            color: #94b7ff;
            font-size: 14px;
          }
          .item-people-icon {
            width: 26px;
            height: 26px;
            background: url('@/assets/images/safetyDataCockpit/nem-box-icon.png') no-repeat;
            background-size: 100% 100%;
          }
        }
      }
    }
  }
  // 向下的线
  .line-bottom {
    &::after {
      @include line();
      bottom: -$line-length;
    }
  }
  // 向上的线
  .line-top {
    &::before {
      @include line();
      top: -$line-length;
    }
  }
  // 向左的线
  .line-left {
    &::after {
      @include line();
      width: calc(50% + #{$spacing});
      height: 1px;
      left: calc(-50% - #{$extend});
      top: 0;
    }
  }
  // 向右的线
  .line-right {
    &::before {
      @include line();
      width: calc(50% + #{$spacing});
      height: 1px;
      right: calc(-50% - #{$extend});
      top: 0;
    }
  }
}
</style>
