<template>
  <div class="inner">
    <div class="top">
      <span>考题配置
        <span class="examSum">总共题数：{{ questionsScore.nums || 0 }}题 总分数：{{ questionsScore.sumScore || 0 }}分</span></span>
    </div>
    <div class="contener">
      <el-form label-width="100px" :model="questionsInfo" :rules="questionsRlues" :inline="true" ref="autoExamInfo"
        class="demo-form-inline">
        <div class="testItem" v-for="(i, index) in questionsInfo.questionsList" :key="index">
          <el-form-item label="题目来源" :prop="`questionsList.${index}.courseId`" :rules="questionsRlues.courseId">
            <el-select v-model="i.courseId" placeholder="请选择课程" style="width: 460px;"
              @change="changeCourse($event, index)">
              <el-option v-for="item in topicList" :key="item.id" :label="item.courseName" :value="item.id">
              </el-option>
            </el-select>
            <span class="note">(
              注当前课时包含单选题{{ getTypeNum(1, index) }}道、多选题{{ getTypeNum(2, index) }}道、判断题{{ getTypeNum(3, index) }}道 )</span>
          </el-form-item>
          <div class="questionsType">
            <div v-for="(k, ind) in i.questionTypes" :key="ind">
              <h1 style="margin-bottom: 16px; font-size: 14px;">
                {{
          k.type == "1" ? "单选题" : k.type == "2" ? "多选题" : "判断题"
        }}
              </h1>
              <el-form-item :prop="`questionsList.${index}.questionTypes.${ind}.num`" :rules="questionsRlues.num"
                label=" " class="itemNum">
                <el-input type="number" placeholder="请输入" min="1" style="width: 150px" v-model="k.num"
                  @change="numChange(i, ind, index)">
                  <template slot="append">个</template>
                </el-input>
              </el-form-item>
              <el-form-item :prop="`questionsList.${index}.questionTypes.${ind}.score`" :rules="questionsRlues.score"
                label="每小题分数">
                <el-input type="number" placeholder="请输入" min="1" style="width: 150px" v-model="k.score"
                  @change="scoreChange(i, ind)">

                  <template slot="append">分</template>
                </el-input>
              </el-form-item>
            </div>
          </div>
        </div>
      </el-form>
    </div>
    <el-button class="footer" type="primary" @click="addClassHour">添加题目</el-button>
  </div>
</template>

<script>
import axios from "axios";
export default {
  props: {
    // autoInfo:{
    //   type:Object,
    //   default:{}
    // },
    scoreInfo: {
      type: Object,
      default: {}
    },
    tmId: {
      type: String,
      default: '',
    },
  },
  data() {
    let validateNum = (rule, value, callback) => {
      // if (!validateUserName(value)) {
      //   callback(new Error("用户名不正确，请重新输入！"));
      // } else {
      callback();
      // }
    };
    return {
      topicList: [],
      checked: false,
      questionsScore: {
        nums: 0,
        sumScore: 0
      },
      questionsInfo: {
        questionsList: [
          {
            questionTypes: [
              {
                type: "1",
                num: 0,
                score: 0,
              },
              {
                type: "2",
                num: 0,
                score: 0,
              },
              {
                type: "3",
                num: 0,
                score: 0,
              },
            ],
            courseId: '',
            questionsNum: []
          },
        ],
      },
      questionsRlues: {
        courseId: [
          { required: true, message: "请输入课时名称", trigger: "change" },
        ],
        num: [
          { validator: validateNum, trigger: "blur" },
        ],
        // score: [
        //   { required: true, message: "", trigger: "blur" },
        // ]
      }
    };
  },
  created() {
    this.routeInfo = JSON.parse(sessionStorage.getItem("routeInfo"));
    // this.questionsInfo = this.autoInfo
    // console.log(this.questionsInfo,' this.questionsInfo789*-*-**');
    if (this.tmId) {
      this.getTrainDetail()
    }
    // this.questionsScore = this.scoreInfo
  },
  methods: {
    async getTrainDetail() {
      console.log(this.setingOpty, 'this.setingOpty123');
      this.$api.trainPlanDetal({ id: this.tmId }).then(res => {
        if (res.code == 200) {
          // let obj = {}
          // obj = JSON.parse(res.data.examParamVo.auto)
          // console.log(obj,'res.data.examParamVo.auto');
          // this.questionsInfo = obj
          console.log(res, 'RES456*-*-*-*-*-*-*-*-*');
          res.data.examParamVo.auto.forEach(el => {
            this.$api.courseQuestionNum({ courseId: el.courseId }).then((res) => {
              if (res.code == '200') {
                el.questionsNum = res.data
              } else {
                this.$message.error(res.msg)
              }
            })
            console.log(el, 'ELELELELEL');

          })
          this.questionsScore.sumScore = res.data.examParamVo.score
          // this.$set(this.questionsInfo.questionsList,'questionsList',res.data.examParamVo.auto)
          // console.log(res.data.examParamVo.auto,'res.data.examParamVo');
          // console.log( this.questionsInfo.questionsList,' this.questionsInfo');
          this.questionsInfo.questionsList = res.data.examParamVo.auto
          console.log(this.questionsInfo.questionsList, 'this.questionsList45797/*/*///++++++++++++++++++++++');


        }
      })
    },
    // 获取科目分类下的课程
    getCourseList(val) {
      console.log(val, 'val123');
      this.$api.allCourseList({ subjectId: val || "" }).then((res) => {
        if (res.code == 200) {
          this.topicList = res.data;
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    // 添加题目来源
    addClassHour() {
      let obj = {
        questionTypes: [
          {
            type: "1",
            num: 0,
            score: 0,
          },
          {
            type: "2",
            num: 0,
            score: 0,
          },
          {
            type: "3",
            num: 0,
            score: 0,
          },
        ],
        courseId: '',
        questionsNum: []
      };
      this.questionsInfo.questionsList.push(obj);
    },
    // 选择试题
    changeCourse(val, index) {
      this.$api.courseQuestionNum({ courseId: val }).then((res) => {
        if (res.code == '200') {
          this.questionsInfo.questionsList[index].questionsNum = res.data || []
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    getTypeNum(val, index) {
      console.log(val, 'val123');
      console.log(index, 'INDEX456');
      this.$nextTick(() => {
        // this.questionsInfo.questionsList.forEach(el => {
        //   console.log(el.questionsNum,'EL789*-*--*-*-*-*-*');
        // })
        console.log(this.questionsInfo.questionsList,'this.questionsInfo.questionsList++++++++++++++++++++++++++++++++++++++++++++++++');
      })
      // if (this.questionsInfo.questionsList) {
      //   let obj = this.questionsInfo.questionsList[index].questionsNum.find(i => i.type == val)
      //   console.log(obj,'1123OBJ');
      //   // if (obj) {
      //   //   return obj.num || 0
      //   // } else {
      //   //   return 0
      //   // }
      // }
      // console.log(this.questionsInfo.questionsList, 'this.questionsInfo.questionsList--------------------------------------------123456---------------');
      // console.log(this.questionsInfo,"123456this.questionsInfothis.questionsInfothis.questionsInfothis.questionsInfothis.questionsInfo");
      // // console.log(this.questionsInfo.questionsList[index].questionsNum,'this.questionsInfo.questionsList[index].questionsNum');

    },
    // 试题个数选择
    numChange(val, ind, index) {
      if (!val.courseId) {
        return this.$message.error("请先选择题目来源");
      }
      let obj = this.questionsInfo.questionsList[index].questionsNum.find(
        (i) => i.type == val.questionTypes[ind].type
      );
      if (!obj) {
        return this.$message.error("该课程下没有该类型的题目");
      }
      if (obj && val.questionTypes[ind].num > obj.num) {
        return this.$message.error("超出该类型的题目个数！");
      }
      this.questionsScore.nums = 0
      this.questionsScore.sumScore = 0
      this.questionsInfo.questionsList.forEach(item => {
        item.questionTypes.forEach(k => {
          this.questionsScore.nums += k.num == '' ? 0 : parseInt(k.num)
          console.log(this.questionsScore.nums, 'this.questionsScore.nums++++++');
          this.questionsScore.sumScore += k.score == '' ? 0 : parseInt(k.num) * parseInt(k.score)
        })
      })
    },
    // 分数选择
    scoreChange() {
      this.questionsScore.sumScore = 0
      this.questionsInfo.questionsList.forEach(item => {
        item.questionTypes.forEach(k => {
          this.questionsScore.sumScore += k.score == '' ? 0 : parseInt(k.num) * parseInt(k.score)
        })
      })
    },
    // 提交考试
    submit(type) {
      this.$refs.autoExamInfo.validate((valid) => {
        if (valid) {
          this.$emit('submitAuto', this.questionsInfo, this.questionsScore, type)
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.inner {
  background-color: #fff;
  height: calc(100% - 140px);
  padding: 16px 22px;

  .top {
    display: flex;
    justify-content: space-between;

    .examSum {
      font-size: 14px;
      color: #7f848c;
    }

    .passScore {
      font-size: 14px;
    }
  }

  .contener {
    background-color: #faf9fc;
    height: calc(100% - 50px);
    overflow: auto;
    padding: 16px;

    // margin-bottom: 16px;
    .testItem {
      height: 172px;
      background-color: #fff;
      font-size: 14px;
      padding: 24px;
      margin-bottom: 16px;
      border-radius: 4px;

      .note {
        color: #ccced3;
        margin-left: 24px;
      }

      .questionsType {
        display: flex;
        margin: 0 0 0 70px;

        >div {
          flex: 1;
          margin-right: 24px;
        }
      }
    }
  }
}

::v-deep .el-form--label-top .el-form-item__label {
  float: left !important;
}

.itemNum {
  ::v-deep .el-form-item__label {
    width: 10px !important;
  }
}
</style>
