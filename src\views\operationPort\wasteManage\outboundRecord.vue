<template>
  <PageContainer>
    <div slot="content" ref="contentRef">
      <el-tabs v-model="activeName" @tab-click="changeActive">
        <el-tab-pane label="列表模式" name="0">
          <div class="searchForm">
            <div class="search-box">
              <div class="btns">
                <div :class="['date-btn', dateType == 'day' ? 'active-btn' : '']" @click="changeDateType('day')">今日</div>
                <div :class="['date-btn', dateType == 'month' ? 'active-btn' : '']" @click="changeDateType('month')">本月</div>
                <div :class="['date-btn', dateType == 'year' ? 'active-btn' : '']" @click="changeDateType('year')">本年</div>
                <div :class="['date-btn', dateType == 'all' ? 'active-btn' : '']" @click="changeDateType('all')">全部</div>
              </div>
              <el-date-picker v-model="dateArr" type="daterange" range-separator="至" start-placeholder="日期区间" end-placeholder="日期区间" value-format="yyyy-MM-dd">
              </el-date-picker>
              <div style="margin-left: 16px">
                <el-button type="primary" @click="handleSearch">查询</el-button>
                <el-button type="primary" plain @click="reset">重置</el-button>
              </div>
            </div>
          </div>
          <div class="cards">
            <div>
              <img src="@/assets/images/operationPort/waste-icon1.png" />
              <span>出站数量</span>
              <span>{{ sumData.goNum }}</span>
              <span>箱（桶）</span>
            </div>
            <div>
              <img src="@/assets/images/operationPort/waste-icon2.png" />
              <span>出站重量</span>
              <span>{{ sumData.goWeight }}</span>
              <span>kg</span>
            </div>
            <div>
              <img src="@/assets/images/operationPort/waste-icon1.png" />
              <span>入库数量</span>
              <span>{{ sumData.getNum }}</span>
              <span>箱（桶）</span>
            </div>
            <div>
              <img src="@/assets/images/operationPort/waste-icon2.png" />
              <span>入站重量</span>
              <span>{{ sumData.getWeight }}</span>
              <span>kg</span>
            </div>
            <div>
              <img src="@/assets/images/operationPort/waste-icon1.png" />
              <span>超时未出站数量</span>
              <span>{{ sumData.overtimeNum }}</span>
              <span>箱（桶）</span>
            </div>
          </div>
          <div class="table-box">
            <el-table v-loading="tableLoading" :data="tableData" border style="width: 100%" height="90%">
              <el-table-column label="出站时间" prop="outTime" :resizable="false" align="center"></el-table-column>
              <el-table-column label="转运箱（桶）数量" prop="boxCount" :resizable="false" align="center"></el-table-column>
              <el-table-column label="重量" prop="weight" :resizable="false" align="center">
                <template slot-scope="scope">
                  <span>{{ scope.row.weight }}kg</span>
                </template>
              </el-table-column>
              <el-table-column label="医废公司签字" prop="companySignature" :resizable="false" align="center">
                <template slot-scope="scope">
                  <span style="color: #5188fc; cursor: pointer" @click="showPicture(scope.row, 'companySignature')">查看图片</span>
                </template>
              </el-table-column>
              <el-table-column label="院方签字" prop="hospitalSignature" :resizable="false" align="center">
                <template slot-scope="scope">
                  <span style="color: #5188fc; cursor: pointer" @click="showPicture(scope.row, 'hospitalSignature')">查看图片</span>
                </template>
              </el-table-column>
              <el-table-column label="单据" prop="invoicesPicture" :resizable="false" align="center">
                <template slot-scope="scope">
                  <span style="color: #5188fc; cursor: pointer" @click="showPicture(scope.row, 'invoicesPicture')">查看图片</span>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination
              :current-page="pageNo"
              :page-sizes="[15, 30, 50, 100]"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            >
            </el-pagination>
            <imgCarousel :dialogVisibleImg="dialogVisibleImg" :imgArr="imgArr" @closeDialog="closeDialogImg"></imgCarousel>
          </div>
        </el-tab-pane>
        <el-tab-pane label="日历模式" name="1">
          <div class="cardWrap">
            <el-calendar v-if="!isDetail" v-model="calendarValue">
              <template slot="dateCell" slot-scope="{ data }">
                <div class="calendar_content">
                  <div style="text-align: right; margin-bottom: 5px">
                    {{ data.day.split('-')[2] }}
                  </div>
                  <div v-for="(item, index) in calendarList" :key="index">
                    <div v-if="moment(item.createTime).format('YYYY-MM-DD') == data.day" @click="toListDetail(data, item)">
                      <div class="calNumber">
                        <span>入库重量：{{ Number(item.inWeigh).toFixed(2) }}kg</span>
                        <span>出库重量：{{ Number(item.buyWeigh).toFixed(2) }}kg</span>
                        <span :class="item.expiredBox > 0 ? 'unprocessed' : ''">到期未处理箱(桶)数：{{ item.expiredBox }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </el-calendar>
            <div v-else class="tableList">
              <tableList :date="clickDate" :detailInfo="clickItem" @close="closed"></tableList>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </PageContainer>
</template>
<script>
import imgCarousel from '../spaceManage/common/imgCarousel.vue'
import fecha from 'element-ui/src/utils/date' // element-ui中处理时间的工具类
import moment from 'moment'
import tableList from './components/tableList.vue'
export default {
  name: 'outboundRecord',
  components: {
    imgCarousel,
    tableList
  },
  beforeRouteLeave(to, from, next) {
    this.activeName = '0'
    this.isDetail = false
    next()
  },
  data() {
    return {
      moment,
      dateType: 'day',
      calendarValue: new Date(),
      dateVal: '',
      tableLoading: false,
      tableData: [],
      pageNo: 1,
      pageSize: 15,
      total: 0,
      activeName: '0',
      dateArr: [],
      imgArr: [],
      dialogVisibleImg: false,
      sumData: {},
      calendarList: [],
      isDetail: false,
      deptOptions: [],
      officeId: '',
      listType: 'out',
      outTableLoading: false,
      outTableData: [],
      clickDate: '',
      clickItem: {}
    }
  },
  created() {
    this.changeMonth(this.calendarValue)
  },
  mounted() {
    this.getTableData()
    this.getSum()
    this.$nextTick(() => {
      // 点击前一个月
      let prevBtn = document.querySelector('.el-calendar__button-group .el-button-group>button:nth-child(1)')
      prevBtn.addEventListener('click', () => {
        this.changeMonth(this.calendarValue)
      })
      // 点击下一个月
      let nextBtn = document.querySelector('.el-calendar__button-group .el-button-group>button:nth-child(3)')
      nextBtn.addEventListener('click', () => {
        this.changeMonth(this.calendarValue)
      })
      // 点击今天
      let todayBtn = document.querySelector('.el-calendar__button-group .el-button-group>button:nth-child(2)')
      todayBtn.addEventListener('click', () => {
        this.changeMonth(this.calendarValue)
      })
    })
  },
  methods: {
    getSum() {
      let params = {
        dateType: this.dateType,
        beginDate: this.dateArr[0],
        endDate: this.dateArr[1]
      }
      this.$api.outboundDataSum(params).then((res) => {
        this.sumData = res.data
      })
    },
    showPicture(row, label) {
      if (row[label]) {
        // let str = row.ossFilePrefix + row[label]
        let str = this.$tools.imgUrlTranslation(row[label])
        this.imgArr.push(str)
        this.dialogVisibleImg = true
      } else {
        this.$message.error('无收集人员签名')
      }
    },
    closeDialogImg() {
      this.dialogVisibleImg = false
      this.imgArr = []
    },
    handleSearch() {
      this.pageNo = 1
      this.getTableData()
      this.getSum()
    },
    reset() {
      this.dateType = 'day'
      this.dateArr = []
      this.getTableData()
      this.getSum()
    },
    changeDateType(type) {
      this.dateType = type
      this.getTableData()
      this.getSum()
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.pageNo = 1
      this.getTableData()
    },
    getTableData() {
      this.tableLoading = true
      let params = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        dateType: this.dateType,
        beginDate: this.dateArr[0],
        endDate: this.dateArr[1]
      }
      this.$api.getOutboundRecordList(params).then((res) => {
        this.tableLoading = false
        this.tableData = res.data.rows
        this.total = res.data.total
      })
    },
    handleCurrentChange(val) {
      this.pageNo = val
      this.getTableData()
    },
    goManualEntry() {
      this.$router.push({ path: '/wasteManage/inoutManage/manualEntry' })
    },
    // 获取日历板上第一天
    getMondayDay(Month) {
      let now = new Date(Month)
      let day = now.getDay() || 7
      return fecha.format(new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1 - day), 'yyyy-MM-dd')
    },
    // 获取日历板上最后一天
    getLastSundayDay(Month) {
      let monday = new Date(this.getMondayDay(Month))
      let sunday = monday.setDate(monday.getDate() + 41)
      return fecha.format(new Date(sunday), 'yyyy-MM-dd')
    },
    // 改变月份
    changeMonth(date) {
      let month = (date.getMonth() + 1).toString().padStart(2, '0')
      let year = date.getFullYear()
      let oneday = year + '-' + month // 获取某月
      this.monthData = year + '年' + month + '月'
      let startTime = this.getMondayDay(oneday)
      let endTime = this.getLastSundayDay(oneday)
      this.calendarList = []
      let params = {
        beginDate: startTime,
        endDate: endTime
      }
      this.$api.calendarData(params).then((res) => {
        if (res.code == '200') {
          console.log(res.data)
          this.calendarList = res.data
        } else {
          this.$message.error(res.message)
        }
      })
    },
    toListDetail(data, item) {
      this.clickDate = data.day
      this.clickItem = item
      this.isDetail = true
    },
    closed() {
      this.isDetail = false
      this.changeMonth(this.calendarValue)
    },
    changeActive(tab) {
      if (tab.name == '0') {
        this.isDetail = false
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.searchForm {
  background-color: #fff;
  margin-bottom: 16px;
  display: flex;
  height: 60px;
  padding: 0 16px;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .el-input {
    width: 300px;
  }

  > div {
    margin-right: 20px;
  }
}

.search-box {
  display: flex;
  align-items: center;
}

.container-content > div {
  height: 100%;
}

.el-table {
  margin-bottom: 12px;
}

::v-deep .el-date-editor .el-range__icon {
  line-height: 24px;
}

.statistics {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
  height: 78px;
}

.statistics .item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-around;
  flex: 1;
  background-color: rgb(53 98 219 / 6%);
  margin-right: 8px;
  border-radius: 4px;
}

.statistics .item span:nth-child(2) {
  font-size: 18px;
  color: #3562db;
  font-weight: 700;
}

.statistics .hover-style {
  background-color: #fff;
  box-shadow: 0 0 10px 0 rgb(32 56 114 / 13%);
  border-bottom: 3px solid #3562db;
}

.statistics .pointer-style {
  cursor: pointer;
}

.statistics .pure {
  background-color: #fff;
}

.statistics .pure span:nth-child(2) {
  color: #121f3e;
}

::v-deep .el-dialog {
  height: 80vh;
  margin-top: 10vh;
}

::v-deep .el-dialog__wrapper {
  overflow: hidden;
}

::v-deep .el-dialog__body {
  height: 93%;
}

::v-deep .el-table__row:hover {
  cursor: pointer;
}

.btns {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.btns .date-btn {
  width: 100px;
  height: 32px;
  border: 1px solid #3562db;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  color: #3562db;
  margin-right: 12px;
  cursor: pointer;
}

.active-btn {
  background-color: #3562db;
  color: #fff !important;
}

.cards {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.cards > div {
  width: 19.5%;
  background-color: #fff;
  border-radius: 4px;
  height: 90px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.cards img {
  width: 45px;
  height: 45px;
}

.cards span:nth-child(2) {
  margin: 0 16px;
}

.cards span:nth-child(3) {
  font-size: 28px;
  font-weight: 700;
}

.cards span:nth-child(4) {
  font-size: 14px;
  color: #ccced3;
  margin-left: 5px;
  transform: translateY(3px);
}

.table-box {
  background-color: #fff;
  height: 65vh;
  padding: 16px;
}

::v-deep .el-tabs__header {
  background-color: #fff;
  padding-left: 16px;
}

:deep(.el-tabs) {
  height: 100%;

  .el-tabs__content {
    height: calc(100% - 60px);

    .el-tab-pane {
      height: 100%;
    }

    .el-calendar {
      height: 100%;

      .el-calendar__body {
        height: 100%;

        .el-calendar-table {
          padding-bottom: 20px;
          height: calc(100% - 20px);
        }
      }
    }
  }
}

::v-deep .el-calendar-table thead th::before {
  content: '周';
}

::v-deep .el-calendar .el-calendar__header .el-calendar__title {
  font-size: 30px;
}

::v-deep .el-calendar-table td.is-today {
  color: #333;
}

::v-deep .el-calendar-table td.is-selected {
  background-color: #fff;
}

.cardWrap {
  width: 100%;
  background-color: #fff;
  height: 100%;

  .calendar_content {
    .calNumber {
      font-size: 14px;
      display: flex;
      flex-direction: column;

      span {
        padding: 2px;
        margin-bottom: 2px;
        background-color: #5482ee;
        border-radius: 4px;
        color: #fff;
      }

      .unprocessed {
        color: #fa403c;
      }
    }
  }

  .tableList {
    height: 100%;
  }
}
</style>
