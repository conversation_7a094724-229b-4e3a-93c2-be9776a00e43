<template>
  <ControlStrategy :projectCode="projectCode" :type="'1'" />
</template>

<script>
import ControlStrategy from '@/views/monitor/lightingMonitoring/airAndLightingCom/controlStrategy.vue'
import { monitorTypeList } from '@/util/dict.js'
export default {
  components: {
    ControlStrategy
  },
  data() {
    return {
      projectCode: monitorTypeList.find((item) => item.projectName == '空调监测').projectCode
    }
  },
  mounted() {
    
  },

  methods: {
    
  }
}
</script>
<style lang="scss" scoped>

</style>
