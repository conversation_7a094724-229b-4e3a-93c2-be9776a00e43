<template>
  <div class="">
    <template v-for="(item, index) in dlayoutItems">
      <dash-item v-if="item.id == '1'" v-bind.sync="dlayoutItems[index]" :key="item.id" @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)">
        <ContentCard
          title="风险等级分析"
          :scrollbarHover="true"
          :cstyle="{ height: '100%' }"
          class="drag_class"
          :hasMoreOper="['edit']"
          @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')"
        >
          <div slot="content" style="height: 100%;">
            <div class="childrenCenter">
              <div v-for="(items, index) in riskNumList" :key="index" :class="'childItems' + index" class="childItems" @click="getRiskList(items.dictValue, '', riskLevelList[items.value])">
                <div class="childContent">
                  <img height="100%" :src="items.src" alt="">
                  <div class="childTextWrap">
                    <div class="countFont" :class="items.color">{{ riskLevelList[items.value] }}</div>
                    <div class="typeFont">{{ items.lable }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ContentCard>
      </dash-item>
      <dash-item v-if="item.id == '2'" v-bind.sync="dlayoutItems[index]" :key="item.id" @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)">
        <ContentCard
          title="风险类型分析"
          :scrollbarHover="true"
          :cstyle="{ height: '100%' }"
          class="drag_class"
          :hasMoreOper="['edit']"
          @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')"
        >
          <div slot="content" style="height: 100%;">
            <div id="riskTypeAnalysis" ref="riskTypeAnalysis"></div>
          </div>
        </ContentCard>
      </dash-item>
      <dash-item v-if="item.id == '3'" v-bind.sync="dlayoutItems[index]" :key="item.id" @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)">
        <ContentCard
          title="风险巡查任务分析"
          :scrollbarHover="true"
          :cstyle="{ height: '100%' }"
          class="drag_class"
          :hasMoreOper="['edit']"
          @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')"
        >
          <div slot="content" style="height: 100%;">
            <div class="inspAnalysis">
              <div class="inspItem">
                <div>今日巡检</div>
                <div class="inspCount">
                  <span class="doInspIcon" style="background-color: #3562db;"></span>已巡查
                  <span class="doInspCount" style="color: #3562db;">{{ dayInspCountAnalysis.accomplishCount || 0 }}</span>
                  <span style="color: #ccced3;">个</span>
                  <span class="totalInspCount">{{ dayInspCountAnalysis.percentage || 0 }}</span>
                  <span style="color: #ccced3;">%</span>
                </div>
                <el-progress
                  :stroke-width="10"
                  color="#3562DB"
                  define-back-color="#CCCED3"
                  :show-text="false"
                  stroke-linecap="square"
                  :percentage="dayInspCountAnalysis.percentage || 0"
                ></el-progress>
              </div>
              <div class="inspItem">
                <div>本周巡检</div>
                <div class="inspCount">
                  <span class="doInspIcon" style="background-color: #ff9435;"></span>已巡查
                  <span class="doInspCount" style="color: #ff9435;">{{ weekInspCountAnalysis.accomplishCount }}</span>
                  <span style="color: #ccced3;">个</span>
                  <span class="totalInspCount">{{ weekInspCountAnalysis.percentage }}</span>
                  <span style="color: #ccced3;">%</span>
                </div>
                <el-progress
                  :stroke-width="10"
                  color="#FF9435"
                  define-back-color="#CCCED3"
                  :show-text="false"
                  stroke-linecap="square"
                  :percentage="weekInspCountAnalysis.percentage || 0"
                ></el-progress>
              </div>
            </div>
          </div>
        </ContentCard>
      </dash-item>
      <dash-item v-if="item.id == '4'" v-bind.sync="dlayoutItems[index]" :key="item.id" @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)">
        <ContentCard
          title="风险部门分布"
          :scrollbarHover="true"
          :cstyle="{ height: '100%' }"
          class="drag_class"
          :hasMoreOper="['edit']"
          @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')"
        >
          <div slot="content" style="height: 100%;">
            <div id="riskDeptAnalysis" ref="riskDeptAnalysis"></div>
          </div>
        </ContentCard>
      </dash-item>
      <dash-item v-if="item.id == '5'" v-bind.sync="dlayoutItems[index]" :key="item.id" @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)">
        <ContentCard
          title="科室任务巡检分析"
          :scrollbarHover="true"
          :cstyle="{ height: '100%' }"
          class="drag_class"
          :hasMoreOper="['edit']"
          @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')"
        >
          <div slot="content" style="height: 100%;">
            <div class="btns">
              <span :class="{ 'active-btn': taskType == 'day' }" @click="changeTaskType('day')">日</span>
              <span :class="{ 'active-btn': taskType == 'week' }" @click="changeTaskType('week')">周</span>
              <span :class="{ 'active-btn': taskType == 'month' }" @click="changeTaskType('month')">月</span>
              <span :class="{ 'active-btn': taskType == 'year' }" @click="changeTaskType('year')">年</span>
            </div>
            <el-table :data="tableData" height="100%" style="width: 100%;" @row-click="toTaskList">
              <el-table-column v-loading="tableLoading" show-overflow-tooltip prop="teamName" label="部门名称"> </el-table-column>
              <el-table-column prop="count" label="任务数" width="100"> </el-table-column>
              <el-table-column prop="unfinished" label="逾期未巡检"> </el-table-column>
              <el-table-column prop="percentage" label="完成率" width="100">
                <template slot-scope="scope">
                  <span>{{ scope.row.percentage + '%' }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </ContentCard>
      </dash-item>
    </template>
  </div>
</template>
<script>
import * as echarts from 'echarts'
import { DashItem } from 'vue-responsive-dash'
import majorRiskImg from '@/assets/images/securityCenter/<EMAIL>'
import majorRiskImg1 from '@/assets/images/securityCenter/risk-major@2x(1).png'
import majorRiskImg2 from '@/assets/images/securityCenter/<EMAIL>'
import riskLow from '@/assets/images/securityCenter/<EMAIL>'
import riskTotalCount from '@/assets/images/securityCenter/<EMAIL>'
export default {
  name: 'riskOperations',
  components: {
    DashItem
  },
  props: {
    dlayoutItems: {
      type: Array,
      default: () => []
    },
    componentData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      taskType: 'week',
      riskLevelList: [],
      riskNumList: [
        {
          src: riskTotalCount,
          value: 'totalCount',
          lable: '风险总数',
          dictValue: ''
        },
        {
          src: majorRiskImg,
          value: 'majorRisk',
          lable: '重大风险',
          dictValue: '1'
        },
        {
          src: majorRiskImg2,
          value: 'commonDangerCount',
          lable: '一般风险',
          dictValue: '3'
        },
        {
          src: majorRiskImg1,
          value: 'moreRisk',
          lable: '较大风险',
          dictValue: '2'
        },
        {
          src: riskLow,
          value: 'lowDangerCount',
          lable: '低风险',
          dictValue: '4'
        }
      ],
      riskTypeData: {},
      dayInspCountAnalysis: {},
      weekInspCountAnalysis: {},
      tableData: [],
      tableLoading: false,
      riskDeptData: [],
      echartsDom: ['riskTypeAnalysis', 'riskDeptAnalysis']
    }
  },
  created() {
    this.getRiskInspAnalysis('day')
    this.getRiskInspAnalysis('week')
    this.getWorkOrderCount()
    setTimeout(() => {
      this.getRiskType()
      this.getRiskDeptAnalysis()
    }, 300)
    this.getDeptInspAnalysis(this.taskType)
  },
  methods: {
    changeTaskType(val) {
      this.taskType = val
      this.getDeptInspAnalysis(this.taskType)
    },
    getRiskList(riskLevel, riskType, num) {
      if (num == 0) {
        return this.$message.error('风险数为0')
      }
      const LOGINDATA = {
        unitCode: 'BJSYGJ',
        hospitalCode: 'BJSJTYY'
      }
      sessionStorage.setItem('LOGINDATA', JSON.stringify(LOGINDATA))
      this.$router.push({
        name: 'riskList',
        query: {
          riskLevel,
          riskType
        }
      })
    },
    // 风险等级分析
    getWorkOrderCount() {
      this.$api.getRiskStatusCount({ isPanel: 1 }).then((res) => {
        this.riskLevelList = res.data
      })
    },
    // 风险类型分析
    getRiskType() {
      this.$api.getRiskTypeAnalysis({}).then((res) => {
        if (res.code == 200) {
          this.riskTypeData = res.data
          this.$nextTick(() => {
            this.chartsRiskRenderer()
          })
        }
      })
    },
    // 风险饼图渲染
    chartsRiskRenderer() {
      const pieData = []
      let totalCount = 0
      this.riskTypeData.list.map((i) => (totalCount += i.value))
      this.riskTypeData.list.forEach((i, index) => {
        const item = {
          name: this.riskTypeData.xAxis[index],
          value: i.value,
          percentage: ((i.value / totalCount) * 100).toFixed(2)
        }
        pieData.push(item)
      })
      const myChart = echarts.init(document.getElementById('riskTypeAnalysis'))
      // 绘制图表
      var option = {
        color: ['#08CB83', '#3562DB', '#FF9435', '#0CA6ED'],
        tooltip: {
          trigger: 'item'
        },
        legend: {
          show: false
        },
        series: [
          {
            type: 'pie',
            radius: '60%',
            center: ['48%', '50%'],
            label: {
              color: '#333333',
              formatter: function (params) {
                return params.data.name + ' ' + params.data.percentage + '% ' + params.data.value + '件'
              }
            },
            data: pieData
          }
        ]
      }
      myChart.clear()
      myChart.setOption(option) // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
    echartsResize() {
      let echartsDom = this.echartsDom
      this.$nextTick(() => {
        setTimeout(() => {
          echartsDom.forEach((item) => {
            echarts.init(document.getElementById(item)).resize()
          })
        }, 100)
      })
    },
    // 风险巡查
    getRiskInspAnalysis(type) {
      this.$api.ipsmTaskQuantity({ dateType: type }).then((res) => {
        if (res.code == 200) {
          if (type == 'day') {
            this.dayInspCountAnalysis = res.data.list[0]
          } else {
            this.weekInspCountAnalysis = res.data.list[0]
          }
          this.$forceUpdate()
        }
      })
    },
    // 风险部门
    getRiskDeptAnalysis() {
      this.$api.getRiskDeptAnalysis().then((res) => {
        if (res.code == 200) {
          this.riskDeptData = res.data
          // this.$nextTick(() => {
          //   this.chartsDeptRenderer()
          // })
        }
      })
    },
    // 渲染部门图表
    chartsDeptRenderer() {
      const teamData = []
      const lowCountData = []
      const generalCountData = []
      const moreCountData = []
      const greatCountData = []
      this.riskDeptData.forEach((i) => {
        teamData.push(i.teamName)
        lowCountData.push(i.lowCount)
        generalCountData.push(i.generalCount)
        moreCountData.push(i.moreCount)
        greatCountData.push(i.greatCount)
      })
      const myChart = echarts.init(document.getElementById('riskDeptAnalysis'))
      var option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'none'
          }
        },
        legend: {
          icon: 'circle',
          itemWidth: 8, // 宽
          itemHeight: 8 // 高
        },
        grid: {
          left: '3%',
          right: '3%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: teamData,
            axisLabel: {
              interval: 0,
              rotate: 30,
              margin: 15
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            max: 10
          }
        ],
        yAxis: [
          {
            type: 'value',
            axisLine: {
              show: false
            }
          }
        ],
        dataZoom: [
          {
            fillerColor: '#BBC3CE',
            backgroundColor: '#fff',
            height: 10,
            type: 'slider',
            bottom: 10,
            textStyle: {
              color: '#000'
            },
            start: 0,
            end: 60
          },
          {
            type: 'inside', // 支持内部鼠标滚动平移
            start: 0,
            end: 100,
            zoomOnMouseWheel: false, // 关闭滚轮缩放
            moveOnMouseWheel: true, // 开启滚轮平移
            moveOnMouseMove: true // 鼠标移动能触发数据窗口平移
          }
        ],
        series: [
          {
            name: '低风险',
            type: 'bar',
            stack: 'Ad',
            emphasis: {
              focus: 'series'
            },
            data: lowCountData,
            barWidth: 14,
            itemStyle: {
              color: '#3562DB',
              borderColor: '#fff',
              borderWidth: 2
            }
          },
          {
            name: '一般风险',
            type: 'bar',
            stack: 'Ad',
            emphasis: {
              focus: 'series'
            },
            data: generalCountData,
            barWidth: 14,
            itemStyle: {
              color: '#FFBE00',
              borderColor: '#fff',
              borderWidth: 2
            }
          },
          {
            name: '较大风险',
            type: 'bar',
            stack: 'Ad',
            emphasis: {
              focus: 'series'
            },
            data: moreCountData,
            barWidth: 14,
            itemStyle: {
              color: '#FF9435',
              borderColor: '#fff',
              borderWidth: 2
            }
          },
          {
            name: '重大风险',
            type: 'bar',
            stack: 'Ad',
            emphasis: {
              focus: 'series'
            },
            data: greatCountData,
            barWidth: 14,
            itemStyle: {
              color: '#FA403C',
              borderColor: '#fff',
              borderWidth: 2
            }
          }
        ]
      }
      myChart.clear()
      myChart.setOption(option) // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
    // 科室巡检
    getDeptInspAnalysis(type) {
      this.tableLoading = true
      this.$api
        .ipsmDeptInspAnalysis({
          dateType: type,
          pageNo: 1,
          pageSize: 999
        })
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.list
          }
          this.tableLoading = false
        })
    },
    toTaskList(row, column, event) {
      this.$router.push({
        name: 'taskList',
        query: {
          id: row.id,
          dateType: this.taskType,
          from: 'risk'
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.childrenCenter {
  width: 100%;
  display: flex;
  height: 100%;
  font-size: 20px;
  font-weight: bold;
  justify-content: space-between;
  .childItems {
    width: 25%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    .childContent {
      margin: 16px 0;
      // height: calc(100% - 32px);
      height: 75px;
      display: flex;
      align-items: center;
      .childTextWrap {
        margin-left: 16px;
        width: 60px;
        .typeFont {
          font-size: 14PX;
          color: #666;
          font-weight: normal;
        }
      }
    }
  }
  .childItems:nth-child(2n) {
    margin: 0 16px;
  }
  .childItems0 {
    background-color: #FAF9FC;
    .countFont {
      color: #121F3E;
    }
  }
  .childItems1 {
    background-color: rgba(255,100,97,0.1);
    .countFont {
      color: #FA403C;
    }
  }
  .childItems2 {
    background-color: rgba(255,148,53,0.1);
    .countFont {
      color: #FFBE00;
    }
  }
  .childItems3 {
    background-color: rgba(255,148,53,0.1);
    .countFont {
      color: #FF9435;
    }
  }
  .childItems4 {
    background-color: rgba(53,98,219,0.1);
    .countFont {
      color: #3562DB;
    }
  }
}

#riskTypeAnalysis {
  width: 100% !important;
  height: 100% !important;
}

.inspAnalysis {
  display: flex;
  flex-direction: column;
  height: 100%;

  .inspItem {
    height: 50%;
    padding: 32px 24px;

    .inspCount {
      margin: 20px 0;
      display: flex;
      align-items: center;

      .doInspIcon {
        display: inline-block;
        width: 8px;
        height: 8px;
        margin-right: 6px;
      }

      .doInspCount {
        margin: 0 8px;
        font-size: 20px;
        font-weight: bold;
      }

      .totalInspCount {
        margin: 0 8px;
        font-size: 20px;
        color: #121f3e;
        font-weight: bold;
      }
    }
  }
}

#riskDeptAnalysis {
  height: 330px !important;
}

:deep(.el-progress-bar) {
  .el-progress-bar__outer {
    border-radius: 0;

    .el-progress-bar__inner {
      border-radius: 0;
    }
  }
}

:deep(.el-table__body) {
  cursor: pointer;
}
.btns {
  position: absolute;
  width: 180px;
  height: 24px;
  right: 10%;
  top: 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 99999;
}
.btns > span {
  width: 25%;
  margin-right: 8px;
  background-color: #FAF9FC;
  font-size: 12px;
  border-radius: 4px;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #7f848c;
  cursor: pointer;
}
.active-btn {
  background-color: #E6EFFC !important;
  color: #3562DB !important;
}
.box-card {
  padding: 24px;
}
::v-deep .svg-icon {
  display: none;
}
</style>
