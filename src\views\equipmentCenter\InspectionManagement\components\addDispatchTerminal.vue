<template>
  <el-dialog
    custom-class="model-dialog"
    title="调度终端选择"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    :modal-append-to-body="false"
    :before-close="closeDialog"
    top="8vh"
    width="60%"
  >
    <div class="dialogContent">
      <div class="leftTree">
        <div class="toptip" style="padding-left: 0; border: 0; font-weight: bold">
          <span class="green_line"></span>
          设备分类
        </div>
        <el-input v-model="filterText" placeholder="输入关键字"> </el-input>
        <ZkRenderTree
          ref="tree"
          :data="treeData"
          :props="treeProps"
          :highlight-current="true"
          node-key="id"
          :filter-node-method="filterNode"
          @node-click="nodeClick"
        ></ZkRenderTree>
      </div>
      <div class="rightTable">
        <div class="topFilter">
          <div class="deviceFilter">
            <el-input v-model="deviceFilter" placeholder="设备名称" @keyup.enter.native="deviceSearch"></el-input>
            <el-cascader
              ref="regionCode"
              v-model="regionCode"
              :props="riskPropsType"
              :options="regionCodeList"
              placeholder="请选择所在区域"
              class="cascaderWid"
              :show-all-levels="false"
              style="margin-right: 10px"
            ></el-cascader>
            <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db" @click="deviceReset">重置</el-button>
            <el-button type="primary" @click="deviceSearch">查询</el-button>
          </div>
        </div>
        <div class="selected">
          <span>已选择</span>
          <div class="tagsWrap">
            <el-tag v-for="tag in tags" :key="tag.id" type="info" closable @close="deleteTag(tag.id)">
              {{ tag.assetName }}
            </el-tag>
          </div>
        </div>
        <div class="table">
          <TablePage
            ref="deviceTable"
            v-loading="spaceLoading"
            row-key="id"
            :tableColumn="tableColumn"
            :data="spaceTableData"
            height="100%"
            :pageProps="pageProps"
            :pageData="paginationData"
            border
            @pagination="paginationChange"
            @selection-change="selectChange"
          >
          </TablePage>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db" @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="checkConfirm">确 定</el-button>
    </span>
    <div>
      <el-dialog title="设备详情" :visible.sync="dialogDateilVisible" :append-to-body="true" :modal-append-to-body="false" class="inspectionPointDateil" top="18vh" width="55%">
        <div>
          <div class="detailDialog">
            <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
              <span class="green_line"></span>
              基本信息
            </div>
            <el-row :gutter="60">
              <el-col :span="8" class="cosRow">
                <div class="titleWrap">设备名称:</div>
                <div class="contenWrap">{{ deviceRowDetail.assetName }}</div>
              </el-col>
              <el-col :span="8" class="cosRow">
                <div class="titleWrap">设备编码:</div>
                <div class="contenWrap">{{ deviceRowDetail.assetCode }}</div>
              </el-col>
              <el-col :span="8" class="cosRow">
                <div class="titleWrap">设备ID:</div>
                <div class="contenWrap">{{ deviceRowDetail.id }}</div>
              </el-col>
              <el-col :span="8" class="cosRow">
                <div class="titleWrap">品牌:</div>
                <div class="contenWrap">{{ deviceRowDetail.assetBrand }}</div>
              </el-col>
              <el-col :span="16" class="cosRow">
                <div class="titleWrap">型号:</div>
                <div class="contenWrap">{{ deviceRowDetail.assetModel }}</div>
              </el-col>
              <el-col :span="8" class="cosRow">
                <div class="titleWrap">出厂日期:</div>
                <div class="contenWrap">{{ moment(deviceRowDetail.dateOfManufacture).format('YYYY-MM-DD') }}</div>
              </el-col>
              <el-col :span="16" class="cosRow">
                <div class="titleWrap">SN码:</div>
                <div class="contenWrap">{{ deviceRowDetail.assetSn }}</div>
              </el-col>
              <el-col :span="8" class="cosRow">
                <div class="titleWrap">启用日期:</div>
                <div class="contenWrap">{{ moment(deviceRowDetail.startDate).format('YYYY-MM-DD') }}</div>
              </el-col>
              <el-col :span="16" class="cosRow">
                <div class="titleWrap">使用期限:</div>
                <div class="contenWrap">{{ deviceRowDetail.serviceLife + '月' }}</div>
              </el-col>
              <el-col :span="12" class="cosRow">
                <div class="titleWrap">所在区域:</div>
                <div class="contenWrap">{{ deviceRowDetail.regionName }}</div>
              </el-col>
              <el-col :span="12" class="cosRow">
                <div class="titleWrap">使用状态:</div>
                <div class="contenWrap">{{ deviceRowDetail.assetStatusName }}</div>
              </el-col>
            </el-row>
          </div>
          <div class="detailDialog typeInfo">
            <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
              <span class="green_line"></span>
              类别信息
            </div>
            <el-row :gutter="60">
              <el-col :span="8" class="cosRow">
                <div class="titleWrap">归口部门:</div>
                <div class="contenWrap">{{ deviceRowDetail.centralizedDepartmentName }}</div>
              </el-col>
              <el-col :span="16" class="cosRow">
                <div class="titleWrap">归属系统:</div>
                <div class="contenWrap">{{ deviceRowDetail.systemCategoryName }}</div>
              </el-col>
            </el-row>
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db" @click="dialogDateilVisible = false">关闭</el-button>
        </span>
      </el-dialog>
    </div>
  </el-dialog>
</template>
<script lang="jsx">
import { transData } from '@/util'
import moment from 'moment'
export default {
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    selectTags: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      moment,
      dialogVisible: this.visible,
      dialogDateilVisible: false,
      spaceLoading: false,
      treeData: [], // 树数据来源
      checkItem: '', // 当前树选中项
      treeProps: {
        children: 'children',
        label: (data, node) => {
          return data.baseName
        },
        isLeaf: 'leaf'
      },
      filterText: '',
      deviceRowDetail: {}, // 设备详情
      deviceFilter: '',
      regionCode: [],
      riskPropsType: {
        children: 'children',
        label: 'ssmName',
        value: 'id',
        checkStrictly: true,
        multiple: false
      },
      regionCodeList: [],
      tags: [],
      rowSelectFlag: false,
      spaceTableData: [], // 设备列表
      tableColumn: [
        {
          type: 'selection',
          align: 'center',
          width: 80,
          reserveSelection: true
        },
        {
          prop: 'serialNumber',
          label: '序号',
          width: 80,
          formatter: (scope) => {
            // 当前分页重置
            // return scope.$index + 1
            // 跨分页序号
            return (this.paginationData.currentPage - 1) * this.paginationData.pageSize + scope.$index + 1
          }
        },
        {
          prop: 'assetName',
          label: '设备名称'
        },
        {
          prop: 'assetBrand',
          label: '品牌'
        },
        {
          prop: 'assetModel',
          label: '型号'
        },
        {
          prop: 'regionName',
          label: '位置'
        },
        {
          prop: 'assetStatusName',
          label: '报废状态'
        },
        {
          prop: 'professionalCategoryName',
          label: '专业类别'
        },
        {
          prop: 'operation',
          label: '操作',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span class="operationBtn-span" style="color: #3562db" onClick={() => this.detail(row.row)}>
                  详情
                </span>
              </div>
            )
          }
        }
      ],
      pageProps: {
        page: 'currentPage',
        pageSize: 'pageSize',
        total: 'total'
      },
      // 空间设备分页
      paginationData: {
        pageSize: 15,
        currentPage: 1,
        total: 0
      },
      fixedDeviceCode: '1802634520343216128' // 安防系统-调度终端id，固定查询此id下的设备
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.getTreeRef().filter(val)
    }
    // dialogVisible(val) {
    //   if (val) {
    //     this.tags = []
    //     if (this.$refs.deviceTable) {
    //       this.$refs.deviceTable.clearSelection()
    //     }
    //     this.$nextTick(() => {
    //       this.$refs.tree.getTreeRef().setCurrentKey(this.checkItem)
    //     })
    //   }
    // }
  },
  created() {},
  mounted() {
    this.tags = JSON.parse(JSON.stringify(this.selectTags))
    this.getDeviceType()
    this.getRegionList()
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true
      return data.baseName.indexOf(value) !== -1
    },
    nodeClick(data, node) {
      if (data.levelType == '1') {
        this.checkItem = data.id
        this.getDeviceList(data.id)
      } else {
        this.checkItem = data.parentId + ',' + data.id
        this.getDeviceList(data.parentId + ',' + data.id)
      }
    },
    paginationChange(pagination) {
      Object.assign(this.paginationData, pagination)
      this.getDeviceList(this.checkItem)
    },
    // 设备分类列表
    getDeviceType() {
      this.$api.getDeviceType({ levelType: 2, startType: 1, parentId: this.fixedDeviceCode }).then((res) => {
        if (res.code == '200') {
          if (res.data && res.data.length) {
            this.treeData = transData(res.data, 'id', 'parentId', 'children')
            // const unknown = {
            //   baseName: '未知',
            //   id: '-1',
            //   levelType: '1'
            // }
            // this.treeData.push(unknown)
            this.checkItem = this.treeData[0].id
            this.$nextTick(() => {
              this.$refs.tree.getTreeRef().setCurrentKey(this.checkItem)
            })
            this.getDeviceList(this.checkItem)
          } else {
            this.checkItem = ''
            this.spaceTableData = []
            this.$message.warning('请配置调度终端类型后再选择设备！')
          }
        }
      })
    },
    // 设备列表
    getDeviceList(typeId) {
      this.spaceLoading = true
      this.$api
        .getDeviceList({
          // professionalCategoryCode: typeId || '',
          systemCategoryCode: this.fixedDeviceCode,
          assetName: this.deviceFilter,
          regionCode: this.regionCode.length > 0 ? this.regionCode[this.regionCode.length - 1] : '',
          pageSize: this.paginationData.pageSize,
          pageNo: this.paginationData.currentPage
        })
        .then((res) => {
          if (res.code == '200') {
            const list = res.data?.list ?? []
            if (list.length) {
              list.forEach((i) => {
                i.regionName = i.regionName && i.regionName.replace(/&gt;/g, '>')
              })
              this.rowSelectFlag = false
              this.spaceTableData = list
              // 选中已选中的
              setTimeout(() => {
                this.rowSelectFlag = true
                this.spaceTableData.forEach((row) => {
                  const matchedIndex = this.tags.findIndex((item) => item.id === row.id)
                  this.$refs['deviceTable'].toggleRowSelection(row, matchedIndex != -1)
                })
                this.rowSelectFlag = false
              }, 0)
            } else {
              this.spaceTableData = []
            }
            this.paginationData.total = res.data.total
            this.spaceLoading = false
          }
        })
    },
    // 设备列表过滤
    deviceSearch() {
      this.getDeviceList(this.checkItem)
    },
    // 设备重置
    deviceReset() {
      this.deviceFilter = ''
      this.regionCode = []
      this.getDeviceList(this.checkItem)
    },
    // 设备详情
    detail(row) {
      this.deviceRowDetail = row
      this.dialogDateilVisible = true
    },
    selectChange(selection) {
      if (this.rowSelectFlag) return
      this.tags = selection
    },
    // 删除标签
    deleteTag(id) {
      this.tags.splice(
        this.tags.find((i) => i.id == id),
        1
      )
      const selectRow = this.spaceTableData.find((i) => i.id == id)
      this.$refs.deviceTable.toggleRowSelection(selectRow, false)
    },
    // 确认选择
    checkConfirm() {
      this.$emit('submit', this.tags)
      this.$emit('update:visible', false)
    },
    closeDialog() {
      this.$emit('update:visible', false)
    },
    getRegionList() {
      this.$api.spaceTree().then((res) => {
        if (res.code == '200') {
          this.regionCodeList = transData(res.data, 'id', 'pid', 'children')
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialogContent {
  width: 100%;
  height: 55vh;
  display: flex;
  justify-content: space-between;
  .leftTree {
    background-color: #fff;
    padding: 0 16px;
    width: 250px;
    height: 100%;
    .filter-tree {
      height: 343px;
      overflow: auto;
    }
    ::v-deep .custom-tree {
      height: calc(100% - 82px);
      overflow: auto;
    }
  }
  .rightTable {
    background-color: #fff;
    width: calc(100% - 268px);
    height: 100%;
    padding: 0 16px;
    .topFilter {
      width: 100%;
      padding: 16px 0 0;
      display: flex;
      justify-content: space-between;
      ::v-deep .el-input {
        width: 200px;
      }
      .deviceFilter {
        ::v-deep .el-input {
          width: 260px;
          margin-right: 15px;
        }
      }
    }
    .selected {
      height: 40px;
      margin: 10px 0;
      display: flex;
      align-items: center;
      > span {
        display: inline-block;
        margin-right: 10px;
        width: 42px;
      }
      .tagsWrap {
        width: calc(100% - 52px);
        height: 100%;
        display: flex;
        align-items: center;
        overflow-x: auto;
        overflow-y: hidden;
        > span {
          margin-right: 10px;
        }
      }
      .tagsWrap::-webkit-scrollbar {
        height: 10px;
      }
    }
    .table {
      height: calc(100% - 160px);
      ::v-deep .el-table {
        .el-table__cell {
          padding: 10px 0;
        }
      }
    }
  }
}
.inspectionPointDateil {
  :deep(.el-dialog) {
    .el-dialog__header {
      border-bottom: 1px solid #dcdfe6;
    }
    .el-dialog__body {
      background-color: #f6f5fa;
      padding: 24px;
    }
    .el-dialog__footer {
      padding: 10px 20px;
    }
  }
  .detailDialog {
    background-color: #fff;
    padding: 0 16px;
    .cosRow {
      display: flex;
      align-items: center;
      padding: 12px 0;
      .titleWrap {
        color: #414653;
        width: 100px;
        text-align: right;
      }
      .contenWrap {
        color: #121f3e;
        margin-left: 14px;
      }
    }
  }
  .typeInfo {
    margin-top: 24px;
  }
}
</style>
