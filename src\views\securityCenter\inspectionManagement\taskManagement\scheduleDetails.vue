<template>
  <div style="height: 100%;">
    <!-- <sinoPanel title="巡检详情" type="list" :isClose="true" style="background-color: rgb(245, 246, 251);" @close="$router.go(-1)">
      <template slot="content">
        <div class="backBar" @click="$router.go(-1)">
          <span class="icon el-icon-arrow-left"></span>
          <span class="header-title">巡检详情</span>
        </div>
      </template>
    </sinoPanel> -->
    <div class="topFilter">
      <div class="backBar">
        <span style="cursor: pointer;" @click="$router.go(-1)">
          <i class="el-icon-arrow-left"></i>
          任务详情
        </span>
      </div>
    </div>
    <div class="content_box">
      <div class="top_content">
        <div style="margin-bottom: 20px;">
          <div>
            <span class="label">任务名称：</span>
            <span class="labelVal">{{ query.taskName }}</span>
          </div>
          <div style="margin: 0 30px;">
            <span class="label">工作频率：</span>
            <span class="labelVal">{{ query.cycleRole }}</span>
          </div>
          <div>
            <span class="label">周期类型：</span>
            <span class="labelVal">{{ cycleTypeFn(query.cycleType) }}</span>
          </div>
          <div style="margin-left: 30px;">
            <span class="label" style="width: 155px;">巡检小组/人员：</span>
            <span class="labelVal">{{ query.planPersonName || query.distributionTeamName }}</span>
          </div>
        </div>
        <div>
          <div>
            <span class="label">实际巡检开始时间-实际巡检结束时间：</span>
            <span class="labelVal">{{ query.executeStartTime + ' - ' + query.executeEndTime }}</span>
          </div>
          <div style="margin-left: 100px;">
            <span class="label">完成状态：</span>
            <span class="labelVal">{{ status == '1' ? '未完成' : '已完成' }}</span>
          </div>
        </div>
      </div>
      <div class="table_list" style="text-align: right; height: 87%;">
        <el-table
          v-loading="tableLoading"
          :data="tableData"
          height="calc(100% - 30px)"
          border
          style="width: 100%;"
          :cell-style="{ padding: '8px' }"
          stripe
          :header-cell-style="{ background: '#f2f4fbd1' }"
          :empty-text="emptyText"
          highlight-current-row
          @selection-change="handleSelectionChange"
        >
          <!-- <el-table-column type="selection" width="55"></el-table-column> -->
          <el-table-column type="index" label="序号" width="55" align="center"></el-table-column>
          <el-table-column prop="taskPointName" show-overflow-tooltip label="巡检点名称" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="巡检结果" align="center">
            <template slot-scope="scope" :class="scope.row">
              <span :style="{ color: scope.row.state == '3' || scope.row.state == '4' ? 'red' : '' }">{{
                scope.row.state == '2' ? '合格' : scope.row.state == '3' ? '不合格' : scope.row.state == '4' ? '异常报修' : '未巡检'
              }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="hiddenDangerId" show-overflow-tooltip label="隐患编号" align="center"></el-table-column>
          <el-table-column prop="excuteTime" show-overflow-tooltip label="实际巡检时间" align="center"> </el-table-column>
          <el-table-column prop="implementPersonName" show-overflow-tooltip label="执行人员" align="center"> </el-table-column>
          <el-table-column prop="spyScan" show-overflow-tooltip label="定位状态" align="center"> </el-table-column>
          <el-table-column prop="upDepartName" show-overflow-tooltip label="操作" align="center">
            <template slot-scope="scope">
              <el-link type="primary" :underline="false" @click="viewDetails(scope.row)">详情</el-link>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          class="table-page pagination"
          style="margin-top: 10px; margin-bottom: 10px;"
          :current-page="paginationData.currentPage"
          :page-sizes="[15, 30, 50, 100]"
          :page-size="paginationData.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="paginationData.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>
<script type="text/ecmascript-6">
export default {
  name: 'feedbackRecord',
  components: {},
  data() {
    return {
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      cycleTypeArr: [
        {
          cycleType: 8,
          label: '单次'
        },
        {
          cycleType: 6,
          label: '每日'
        },
        {
          cycleType: 0,
          label: '每周'
        },
        {
          cycleType: 2,
          label: '每月'
        },
        {
          cycleType: 3,
          label: '季度'
        },
        {
          cycleType: 5,
          label: '全年'
        }
      ],
      LOGINDATA: '',
      searchDataObj: {
        workTypeCode: '',
        endDate: '',
        startDate: '',
        dateLine: '',
        unionSel: ''
      },
      tableData: [],
      tableList: [],
      emptyText: '暂无数据',
      tableLoading: false,
      pageSize: '',
      currentPage: '',
      listRole: '',
      userIds: '',
      activeType: '0',
      tableClickArry: [],
      startTime: '',
      endTime: '',
      query: {},
      status: '',
      id: ''
    }
  },
  created() {
    // 获取登录信息
    this.LOGINDATA = JSON.parse(sessionStorage.getItem('LOGINDATA'))
  },
  mounted() {
    this.query = this.$route.query
    console.log(this.query)
    // this.startTime = this.$route.query.startTime
    // this.endTime = this.$route.query.endTime
    this.status = this.$route.query.status
    this.id = this.$route.query.id
    // 获取详情
    this._findScheduleDetails()
  },
  methods: {
    /**
     * 点击table表格
     */
    handleSelectionChange(val) {
      this.tableClickArry = val
    },
    // 查询表格
    _findScheduleDetails() {
      this.tableLoading = true
      const { paginationData } = this
      let data = {
        taskId: this.id,
        pageNo: paginationData.currentPage,
        pageSize: paginationData.pageSize
      }
      this.$api.findScheduleDetails(data).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.list
          this.paginationData.total = parseInt(res.data.sum)
          if (res.data.list.length == 0) {
            this.emptyText = '未查到任何记录，请放大查询条件试试！'
          }
        }
        this.tableLoading = false
      })
    },
    // 查看详情
    viewDetails(row) {
      this.$router.push({
        path: 'recordDetails',
        query: {
          id: row.id
        }
      })
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this._findScheduleDetails()
    },
    // 每页的条数
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.paginationData.currentPage = 1
      this._findScheduleDetails()
    },
    // 周期类型
    cycleTypeFn(cycleType) {
      const [item] = this.cycleTypeArr.filter((i) => i.cycleType == cycleType)
      return item ? item.label : ''
    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.el-tabs__nav-wrap.is-left::after) {
  height: 0 !important;
}

:deep(.el-tabs__active-bar) {
  height: 0 !important;
}

:deep(.el-tabs__nav) {
  display: flex;
  flex-direction: column;
  align-items: center;
}

:deep(.el-tabs__header) {
  float: none !important;
}

:deep(.is-active) {
  background: #eee;
  border-radius: 4px;
  width: 180px;
  height: 35px;
  line-height: 35px;
  text-align: center !important;
}

:deep(.el-tabs__item.is-active) {
  color: #5188fc;
}

:deep(.el-tabs__item:hover) {
  color: #5188fc;
}

.topFilter {
  margin: 15px 15px 0;
  padding: 15px;
  width: calc(100% - 30px);
  height: 70px;
  background-color: #fff;

  .backBar {
    color: #121f3e;
    height: 30px;
    border-bottom: 1px solid #dcdfe6;
  }
}

.backBar {
  height: 20px;
}

.backBar:hover {
  cursor: pointer;
}

.content {
  display: flex;
}

.pagination {
  bottom: 20px !important;
}

.special_box {
  // width: 100%;
  height: 100%;
  display: flex;
}

.content_box {
  font-size: 14px;
  height: calc(100% - 80px);
  margin: 0 15px;
  padding: 20px 25px 25px;
  background: #fff;
  display: flex;
  flex-direction: column;
}

.top_content {
  width: 100%;
  display: flex;
  flex-direction: column;
  margin-bottom: 25px;

  >div {
    width: 100%;
    display: flex;
    align-items: center;

    >div {
      display: flex;
      align-items: center;

      .label {
        min-width: 70px;
        margin-right: 10px;
        color: #5b5b5b;
        font-weight: 600;
      }

      .labelVal {
        min-width: 150px;
        color: #909399;
        font-weight: 500;
      }
    }
  }
}

.el-input {
  width: 200px;
}

.viewButton {
  color: #5188fc;
  cursor: pointer;
}

.green_line {
  display: inline-block;
  width: 4px;
  height: 16px;
  background: #5188fc;
  margin-right: 10px;
  vertical-align: middle;
}

.toptip {
  box-sizing: border-box;
  padding-left: 26px;
  height: 50px;
  width: 100%;
  line-height: 50px;
  text-align: left;
  border-bottom: 1px solid rgb(216 222 231 / 100%);
}

.left_content {
  margin: 20px 20px 0 35px;
}

.topTitle {
  display: flex;
  align-items: center;
  padding-bottom: 20px;
  border-bottom: 1px solid #ddd;
}

.line {
  width: 4px;
  height: 16px;
  background-color: #5188fc;
  margin-right: 10px;
}

.left {
  width: 268px;
  min-width: 14%;
  height: 97%;
  border-radius: 10px;
  background-color: #fff;
  margin: 10px;
  margin-top: 10px;

  .middle_tools {
    margin-top: 10px !important;
  }
}

.right {
  position: relative;
  width: calc(100% - 300px);
  // flex: 1;
  // width: 56%;
  height: 97%;
  text-align: left;
  background-color: #fff;
  padding-left: 30%;
  // float: left;
  box-sizing: border-box;
  padding: 20px 10px 10px;
  margin: 10px 10px 10px 0;
  margin-left: 288px;
  border-radius: 10px;

  .top_filters {
    input,
    select {
      margin-right: 15px;
      width: 200px;
      height: 40px;
    }

    .search_button {
      background-color: #5188fc;
      color: #fff;
    }

    .search_button:hover {
      opacity: 0.7;
    }
  }

  .middle_tools {
    // margin-top: 20px;
    margin-bottom: 10px;
  }
}

.deleteButton {
  color: #f43530;
  cursor: pointer;
}

@media screen and (max-width: 1600px) {
  .pagination {
    position: absolute;
    bottom: 0;
    right: 15px;
  }

  .personDialog .el-dialog {
    height: 545px;
  }

  .toptip {
    height: 35px;
    line-height: 35px;
  }

  .left_content {
    height: 415px;
  }

  .left .middle_tools {
    line-height: 36px;
    box-shadow: 0 -4px 4px 0 #f3f4f8;
    box-shadow: 0 -4px 4px 0 #f3f4f8;
  }
}
</style>