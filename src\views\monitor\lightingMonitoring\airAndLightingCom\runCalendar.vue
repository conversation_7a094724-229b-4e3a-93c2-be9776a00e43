<template>
  <PageContainer title="运行日历管理" type="list" :isClose="false" style="background-color: rgb(245, 246, 251); overflow: hidden;">
    <template slot="content">
      <div class="runCalendar">
        <div class="top" style="display: flex;">
          <div class="toptip">
            <span class="green_line"></span>
            运行日历管理
          </div>
          <el-radio-group v-show="!selectDateUpdata" v-model="selectTabType" style="margin: auto 30px;" size="mini">
            <el-radio-button label="month">月</el-radio-button>
            <el-radio-button label="year">年</el-radio-button>
          </el-radio-group>
          <div v-show="!selectDateUpdata" class="top_year">
            <div class="user_select_none">
              <i class="el-icon-arrow-left" @click="changeDateData('-', 'year')"></i>
              <span>{{ yearData }} 年</span>
              <i class="el-icon-arrow-right" @click="changeDateData('+', 'year')"></i>
            </div>
          </div>
          <div v-if="!selectDateUpdata" class="top_btn">
            <el-button type="primary" @click="planConfig">计划配置</el-button>
          </div>
          <div v-else class="top_btn">
            <el-button type="primary" @click="selectDateSubmit">保 存</el-button>
            <el-button plain type="primary" @click="selectDateCancel">取 消</el-button>
          </div>
        </div>
        <div v-loading="isLoading" class="content">
          <div v-if="selectTabType === 'month'" class="content-left">
            <div class="change_month">
              <i class="el-icon-arrow-left" @click="changeDateData('-', 'month')"></i>
              <span>{{ monthData }} 月</span>
              <i class="el-icon-arrow-right" @click="changeDateData('+', 'month')"></i>
            </div>
            <calendarUpdate v-model="calendarDate">
              <template slot="dateCell" slot-scope="{ data }">
                <!-- <div
                  class="calendar_content"
                  :style="{ 'background-color': getDayData(data.day, 'color') }"
                >
                  <p class="calendar_content_date">
                    {{ Number(data.day.split("-")[2]) }}
                  </p>
                  <div
                    v-if="getDayData(data.day, 'image')"
                    class="calendar_content_image"
                  >
                    <img :src="getDayData(data.day, 'image')" alt="" />
                  </div>
                  <div
                    v-if="getDayData(data.day, 'sunrise')"
                    class="time_box"
                    :style="{
                      top: getDayData(data.day, 'positionTop', 'sunrise'),
                    }"
                  >
                    <span>{{ getDayData(data.day, "sunrise") }}</span>
                    <i class="el-icon-sunrise-1"></i>
                    <div></div>
                  </div>
                  <div
                    v-if="getDayData(data.day, 'sunset')"
                    class="time_box"
                    :style="{
                      top: getDayData(data.day, 'positionTop', 'sunset'),
                    }"
                  >
                    <span>{{ getDayData(data.day, "sunset") }}</span>
                    <i class="el-icon-sunrise"></i>
                    <div></div>
                  </div>
                </div> -->
                <div
                  class="calendar_content"
                  :style="{
                    'background-color': selectMonthList.find((item) => item.date === data.day)?.color
                  }"
                >
                  <p class="calendar_content_date">
                    {{ Number(data.day.split('-')[2]) }}
                  </p>
                  <div v-if="selectMonthList.find((item) => item.date === data.day)?.image" class="calendar_content_image">
                    <img :src="selectMonthList.find((item) => item.date === data.day)?.image" alt="" />
                  </div>
                  <div
                    v-if="selectMonthList.find((item) => item.date === data.day)?.sunrise"
                    class="time_box"
                    :style="{
                      top: selectMonthList.find((item) => item.date === data.day)?.positionTopRise
                    }"
                  >
                    <span>{{ selectMonthList.find((item) => item.date === data.day)?.sunrise }}</span>
                    <i class="el-icon-sunrise-1"></i>
                    <div></div>
                  </div>
                  <div
                    v-if="selectMonthList.find((item) => item.date === data.day)?.sunset"
                    class="time_box"
                    :style="{
                      top: selectMonthList.find((item) => item.date === data.day)?.positionTopSet
                    }"
                  >
                    <span>{{ selectMonthList.find((item) => item.date === data.day)?.sunset }}</span>
                    <i class="el-icon-sunrise"></i>
                    <div></div>
                  </div>
                </div>
              </template>
            </calendarUpdate>
          </div>
          <div v-else class="content-left content-left_year">
            <div v-for="(item, index) in 12" :key="index">
              <calendarUpdate v-model="calendarYearDate[index]" showModule="year">
                <template slot="dateCell" slot-scope="{ data }">
                  <div
                    class="calendar_content_year user_select_none"
                    :style="{
                      'background-color': selectMonthList.find((item) => item.date === data.day)?.color,
                      border: selectDateList.includes(data.day) ? '1px solid #5188FC' : 'none'
                    }"
                    @click="selectDayDate(data.day)"
                  >
                    <p class="calendar_content_date">
                      {{ Number(data.day.split('-')[2]) }}
                      <span v-if="holidays.includes(data.day)" class="holiday">节</span>
                      <!-- {{ data.day }} -->
                    </p>
                  </div>
                </template>
              </calendarUpdate>
            </div>
          </div>
          <div class="content-right">
            <!-- 数据渲染Start -->
            <div v-if="!selectDateUpdata" class="icon_explain">
              <div class="icon_explain_title">模式图例说明:</div>
              <div v-for="(item, index) in patternList" :key="index" class="icon_explain_content">
                <i :style="{ background: item.patternColour || '#d4dad6' }"></i><span>{{ item.patternName }}</span>
              </div>
            </div>
            <div v-if="!selectDateUpdata" class="icon_explain">
              <div class="icon_explain_title">时间图例说明:</div>
              <div class="icon_explain_content"><i class="el-icon-sunrise-1"></i><span>日出</span></div>
              <div class="icon_explain_content"><i class="el-icon-sunrise-1" style="transform: rotate(180deg);"></i><span>日落</span></div>
            </div>
            <!-- 数据渲染End -->
            <!-- 计划配置 -->
            <div v-if="selectDateUpdata" class="icon_explain">
              <div class="icon_explain_title">模式图例说明:</div>
              <p style="margin: 10px 0;">计划年份</p>
              <el-date-picker v-model="yearData" style="width: 100%;" value-format="yyyy" type="year" placeholder="选择年"> </el-date-picker>
              <p style="margin: 10px 0;">时间区间</p>
              <el-date-picker
                v-model="dataForm.jjDate"
                style="width: 100%;"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :picker-options="expireTimeOption"
                format="MM-dd"
                value-format="yyyy-MM-dd"
                :clearable="false"
                @change="changejjDateData"
              ></el-date-picker>
              <div class="left_right_structure">
                <div class="left_structure">时间</div>
                <div class="right_structure">
                  <el-checkbox-group v-model="dataForm.checkDateList" @change="weekCheckEvent">
                    <el-checkbox label="holiday">节假日</el-checkbox>
                    <el-checkbox label="1">周一</el-checkbox>
                    <el-checkbox label="2">周二</el-checkbox>
                    <el-checkbox label="3">周三</el-checkbox>
                    <el-checkbox label="4">周四</el-checkbox>
                    <el-checkbox label="5">周五</el-checkbox>
                    <el-checkbox label="6">周六</el-checkbox>
                    <el-checkbox label="7">周日</el-checkbox>
                  </el-checkbox-group>
                </div>
              </div>
              <div class="left_right_structure">
                <div class="left_structure">运行模式</div>
                <div class="right_structure">
                  <el-radio-group v-model="dataForm.radioDate">
                    <el-radio v-for="(item, index) in patternList" :key="index" :label="item.id">{{ item.patternName }}</el-radio>
                  </el-radio-group>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </PageContainer>
</template>
<script>
import moment from 'moment'
import calendarUpdate from './../components/calendarUpdate'
import Dict from '../components/dict.js'
export default {
  name: 'runCalendar',
  components: {
    calendarUpdate
  },
  props: {
    projectCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isLoading: false,
      selectTabType: 'month',
      yearData: moment().format('YYYY'),
      monthData: moment().format('M'),
      calendarYearDate: [],
      patternTypeIconList: Dict.patternTypeIconList,
      patternList: [],
      selectMonthList: [
        // {
        //   date: "2022-08-01",
        //   pattern: "0", // 默认模式
        //   sunrise: "06:00", // 日出
        //   sunset: "18:00", // 日落
        // },
        // {
        //   date: "2022-08-02",
        //   pattern: "1", // 晴天模式
        //   sunrise: "06:00", // 日出
        //   sunset: "18:00", // 日落
        // },
        // {
        //   date: "2022-08-03",
        //   pattern: "2", // 阴天模式
        //   sunrise: "06:00", // 日出
        //   sunset: "18:00", // 日落
        // },
        // {
        //   date: "2022-08-04",
        //   pattern: "0", // 阴天模式
        //   sunrise: "06:00", // 日出
        //   sunset: "18:00", // 日落
        // },
      ],
      selectDateList: [], // 选中日期需要改变模式的数组
      selectDateUpdata: false, // 是否选中计划配置
      // dataValidate: "",
      dataForm: {
        jjDate: [],
        checkDateList: [],
        radioDate: ''
      },
      checkDateList: [], // 选中日期类型
      holidays: [], // 节假日
      allCalendar: []
    }
  },
  computed: {
    expireTimeOption() {
      return {
        disabledDate(time) {
          return time.getTime() < Number(moment().format('x'))
        }
      }
    },
    calendarDate() {
      return this.yearData + '-' + this.monthData + '-01'
    }
  },
  // watch监听yearData改变
  watch: {
    yearData(val) {
      // this.monthData = moment(val).format("M");
      this.calendarYearDate = []
      for (let i = 1; i <= 12; i++) {
        let index = i <= 9 ? '0' + i : i
        this.calendarYearDate.push(val + '-' + index + '-01')
      }
      this.dataForm.jjDate = [
        val == moment().format('YYYY') ? moment().add(1, 'd').format('YYYY-MM-DD') : moment(val).startOf('year').format('YYYY') + '-01-01',
        moment(val).endOf('year').format('YYYY-MM-DD')
      ]
      this.getSelectAllCalendar()
    },
    selectTabType(val) {
      this.getSelectAllCalendar('init')
    },
    monthData(val) {
      this.getSelectAllCalendar('init')
    }
  },
  created() {
    var that = this
    for (let i = 1; i <= 12; i++) {
      let index = i <= 9 ? '0' + i : i
      this.calendarYearDate.push(this.yearData + '-' + index + '-01')
    }
    this.dataForm.jjDate = [that.yearData + moment().add(1, 'd').format('-MM-DD'), moment(that.yearData).endOf('year').format('YYYY-MM-DD')]
    this.getWeatherPatternList()
    this.getSelectAllCalendar('init')
    this.getSelectHolidayByYear()
  },
  mounted() {},
  methods: {
    // 根据年份获取节假日
    getSelectHolidayByYear() {
      let dateArr = []
      this.$api.GetSelectHolidayByYear({ year: this.yearData, projectCode: this.projectCode }).then((res) => {
        if (res.code == 200) {
          res.data.forEach((item) => {
            dateArr = dateArr.concat(item.holidayDate.split(','))
          })
          this.holidays = Array.from(new Set([...this.holidays, ...dateArr]))
        }
      })
    },
    // 根据年/月获取运行日历管理
    getSelectAllCalendar(type) {
      this.isLoading = true
      let params =
        this.selectTabType == 'year'
          ? {
            year: this.yearData
          }
          : {
            year: this.yearData,
            month: this.monthData < 10 ? '0' + this.monthData : '' + this.monthData
          }
      params['projectCode'] = this.projectCode
      let newArr = []
      if (type == 'init') this.allCalendar = []
      this.$api.GetSelectAllCalendar(params).then((res) => {
        this.isLoading = false
        if (res.code == 200 && res.data.length) {
          console.log('allCalendar', this.allCalendar)
          this.allCalendar = this.allCalendar.concat(res.data)
          res.data.forEach((data) => {
            if (JSON.parse(data.sunTime)?.length) {
              newArr.push(
                ...JSON.parse(data.sunTime).map((item) => {
                  return {
                    date: item.date,
                    pattern: item.pattern, // 模式
                    sunrise: item.sunRise, // 日出
                    sunset: item.sunSet, // 日落
                    color: data.weatherPattern.patternColour, // 背景色
                    image: this.patternTypeIconList.find((item) => item.patternCode == data.weatherPattern.patternType)?.patternActiveSrc, // 图标
                    positionTopRise: (moment.duration(item.sunRise + ':00').as('seconds') / 86400) * 100 + '%', // 日出位置
                    positionTopSet: (moment.duration(item.sunSet + ':00').as('seconds') / 86400) * 100 + '%' // 日出位置
                  }
                })
              )
            }
          })
          this.selectMonthList = newArr
          console.log(newArr)
        }
      })
    },
    // 获取初始化模式列表
    getWeatherPatternList() {
      this.$api.selectWeatherPatternAllList({projectCode: this.projectCode}).then((res) => {
        if (res.code == 200) {
          if (res.data.length) {
            res.data.map((e) => {
              const patternData = this.patternTypeIconList.find((item) => item.patternCode == e.patternType)
              e.patternSrc = patternData?.patternSrc
              e.patternActiveSrc = patternData?.patternActiveSrc
            })
            this.patternList = res.data
            this.dataForm.radioDate = res.data[0].id // 运行模式默认选中第一个
          } else {
            this.patternList = []
          }
        }
      })
    },
    // 改变年份
    changeDateData(type, dateType) {
      if (type === '+') {
        if (dateType == 'month' && this.monthData == 12) {
          this.monthData = 1
          this.yearData = Number(this.yearData) + 1 + ''
        } else if (dateType == 'year') {
          this.yearData = Number(this.yearData) + 1 + ''
          this.calendarYearDate = this.calendarYearDate.map((item) => {
            return moment(item).add(1, 'year').format('YYYY-MM-DD')
          })
        } else {
          this.monthData = Number(this.monthData) + 1
        }
        // this.yearData = dateType == 'year' ? Number(this.yearData) + 1 : this.yearData;
        // this.monthData = dateType == 'month' ? Number(this.monthData) + 1 : this.monthData;
      } else {
        if (dateType == 'month' && this.monthData == 1) {
          this.monthData = 12
          this.yearData = Number(this.yearData) - 1 + ''
        } else if (dateType == 'year') {
          this.yearData = Number(this.yearData) - 1 + ''
          this.calendarYearDate = this.calendarYearDate.map((item) => {
            return moment(item).subtract(1, 'year').format('YYYY-MM-DD')
          })
        } else {
          this.monthData = Number(this.monthData) - 1
        }
        // this.yearData = dateType == 'year' ? Number(this.yearData) - 1 : this.yearData;
        // this.monthData = dateType == 'month' ? Number(this.monthData) - 1 : this.monthData;
      }
    },
    getDayData(val, type, param) {
      let sameDate = this.selectMonthList.find((item) => item.date == val)
      // console.log(val, type, sameDate);
      // 渲染 背景颜色
      if (type === 'color') {
        // console.log(
        //   "color",sameDate?.pattern);
        return sameDate ? this.patternList.find((item) => item.patternType == sameDate.pattern)?.patternColour : ''
        // 渲染 模式背景图片
      } else if (type === 'image') {
        return sameDate ? this.patternList.find((item) => item.patternType == sameDate.pattern)?.patternActiveSrc : ''
        // 渲染 时间线
      } else if (type === 'positionTop') {
        let timeSec = moment.duration(sameDate[param] + ':00').as('seconds')
        return (timeSec / 86400) * 100 + '%'
      } else {
        return sameDate ? sameDate[type] : ''
      }
    },
    // 计划配置改变时间区间
    changejjDateData() {
      this.selectDateList = []
      this.dataForm.checkDateList = []
    },
    selectDayDate(day) {
      // moment日期比较 只能配置当前以后的日期
      if (!this.selectDateUpdata || moment(day).isBefore(moment())) return
      if (this.selectDateList.includes(day)) {
        this.selectDateList.splice(this.selectDateList.indexOf(day), 1)
      } else {
        this.selectDateList.push(day)
      }
    },
    // 计划配置
    planConfig() {
      // console.log("计划配置");
      this.selectDateUpdata = true
      this.changejjDateData()
      this.selectTabType = 'year'
    },
    // 确定计划配置
    selectDateSubmit() {
      // console.log("确定计划配置");
      if (this.selectDateList.length) {
        let params = []
        let newObj = {}
        this.selectDateList.forEach((item) => {
          if (newObj[item.slice(0, 4)]) {
            newObj[item.slice(0, 4)].push(item)
          } else {
            newObj[item.slice(0, 4)] = [item]
          }
        })
        Object.keys(newObj).forEach((item) => {
          params.push({
            planYear: item,
            patternId: this.dataForm.radioDate,
            planDate: newObj[item].join(',')
          })
        })
        let newItem = {}
        params.forEach((param) => {
          newItem = this.allCalendar.find((item) => param.planYear == item.planYear && param.patternId == item.weatherPattern.id)
          if (newItem) {
            param.id = newItem.id
          }
        })
        console.log(params)
        this.$api.SaveCalendar({ calendar: JSON.stringify(params), projectCode: this.projectCode }).then((res) => {
          if (res.code == 200) {
            this.$message.success(res.message)
            this.selectDateUpdata = false
            this.getSelectAllCalendar('init')
            this.selectDateCancel()
          }
        })
      } else {
        this.$message.warning('无可保存日期')
      }
    },
    // 取消计划配置
    selectDateCancel() {
      // console.log("取消计划配置");
      this.selectDateUpdata = false
      this.selectDateList = []
      Object.assign(this.dataForm, {
        jjDate: [
          this.yearData == moment().format('YYYY') ? moment().add(1, 'd').format('YYYY-MM-DD') : moment(this.yearData).startOf('year').format('YYYY-MM-DD') + '-01-01',
          moment(this.yearData).endOf('year').format('YYYY-MM-DD')
        ],
        checkDateList: [],
        radioDate: this.patternList[0]?.id
      })
    },
    cDatePicker(vm) {
      // const year = vm.split("-")[0];
      // const conYear = new Date().getFullYear();
      // this.dataValidate = "";
      // if (year !== conYear) {
      //   this.dataValidate = "";
      // }
    },
    // 计划配置 选中星期
    weekCheckEvent(check) {
      let selectDataArr = []
      this.selectDateList = []
      // moment 获取时间段内所有星期一
      const start = moment(this.dataForm.jjDate[0])
      const end = moment(this.dataForm.jjDate[1])
      check.forEach((item) => {
        if (item != 'holiday') {
          let tmp = moment(start).day(item)
          console.log(tmp, 111111111)
          if (tmp.isAfter(start, 'd') || tmp.isSame(start, 'd')) {
            // 过滤当前日期以前的数据
            if (!tmp.isBefore(moment())) {
              selectDataArr.push(tmp.format('YYYY-MM-DD'))
            }
          }
          while (tmp.isBefore(end)) {
            tmp.add(7, 'days')
            // 过滤当前日期以前的数据和截止日期以后的数据
            if (!tmp.isBefore(moment()) && !tmp.isAfter(end)) {
              selectDataArr.push(tmp.format('YYYY-MM-DD'))
            }
          }
        } else {
          selectDataArr = [...selectDataArr, ...this.holidays]
        }
      })
      // 数组合并去重
      this.selectDateList = Array.from(new Set([...this.selectDateList, ...selectDataArr]))
    }
  }
}
</script>
<style src="@/assets/styles/sinomis-ui.css" scoped></style>
<style lang="scss" type="text/css" scoped>
.runCalendar {
  width: 100%;
  height: 100%;
  .top {
    border-radius: 10px;
    height: 48px;
    line-height: 48px;
    background-color: #fff;
    justify-content: space-between;
    .toptip {
      border-bottom: none;
      width: auto;
      font-size: 16px;
      font-weight: 400;
    }
    .top_year {
      flex: 1;
      > div {
        display: flex;
        color: #595959;
        i {
          display: inline-block;
          width: 32px;
          height: 32px;
          line-height: 32px;
          margin: auto 0;
          text-align: center;
          cursor: pointer;
          &:hover {
            background-color: #eeeef0;
            border-radius: 50%;
          }
        }
        span {
          margin: 0 10px;
        }
      }
    }
    .top_btn {
      margin: auto 0;
      margin-right: 20px;
    }
  }
  .content {
    height: calc(100% - 66px);
    margin-top: 10px;
    display: flex;
    .content-left {
      width: 82%;
      height: 100%;
      background-color: #fff;
      border-radius: 10px;
      padding: 10px;
      display: block;
      .change_month {
        text-align: center;
        height: 40px;
        width: 100%;
        i {
          display: inline-block;
          width: 64px;
          height: 32px;
          line-height: 32px;
          margin: auto 0;
          font-size: 16px;
          text-align: center;
          cursor: pointer;
          background: #f1f6ff;
          border: 1px solid #5188fc;
          &:hover {
            color: #5b8afd;
            background-color: #f1f6ff;
          }
        }
        i:first-child {
          border-radius: 4px 0 0 4px;
        }
        i:last-child {
          border-radius: 0 4px 4px 0;
        }
        span {
          margin: auto 50px;
          color: #121f3e;
          font-weight: 500;
        }
      }
      .calendar_content {
        width: 100%;
        height: 100%;
        padding: 8px;
        box-sizing: border-box;
        position: relative;
        .calendar_content_date {
          text-align: right;
          // color: #121f3e;
          font-size: 18px;
          font-family: DIN-Medium, DIN;
        }
        .calendar_content_image {
          width: 30px;
          height: 30px;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          img {
            width: 100%;
            height: 100%;
          }
        }
        .time_box {
          width: calc(100% - 16px);
          display: flex;
          position: absolute;
          transform: translateY(-50%);
          i {
            margin: 0 6px;
          }
          div {
            flex: 1;
            border-top: 1px dashed #99a2b1;
            margin: auto;
          }
        }
      }
    }
    .content-left_year {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-around;
      overflow: scroll;
      > div {
        width: 24%;
      }
      .calendar_content_year {
        height: 36px;
      }
      .calendar_content_date {
        font-size: 14px;
        font-family: DIN-Medium, DIN;
        position: relative;
        .holiday {
          position: absolute;
          top: 2px;
          right: 2px;
          color: #ff4848;
          font-size: 10px;
          line-height: 10px;
          transform: scale(0.83);
        }
      }
    }
    .content-right {
      width: calc(18% - 10px);
      margin-left: 10px;
      background-color: #fff;
      border-radius: 10px;
      padding: 20px 15px;
      .icon_explain {
        padding: 10px;
        .icon_explain_title {
          font-size: 16px;
          font-weight: 600;
          color: #121f3e;
          height: 30px;
          line-height: 30px;
        }
        .icon_explain_content {
          padding-left: 20px;
          display: flex;
          height: 30px;
          line-height: 30px;
          i {
            width: 14px;
            height: 14px;
            margin: auto 0;
          }
          span {
            font-size: 14px;
            color: #595959;
            margin-left: 10px;
          }
        }
        .el-date-editor .el-range-separator {
          width: auto;
        }
        .left_right_structure {
          display: flex;
          margin: 5px 0;
          .left_structure {
            width: 35%;
            margin-top: 5px;
          }
          .right_structure {
            width: 65%;
            .el-checkbox,
            .el-radio {
              display: block;
              margin: 5px;
            }
          }
        }
      }
    }
  }
  .user_select_none {
    -moz-user-select: none; /* 火狐 */
    -webkit-user-select: none; /* webkit浏览器 */
    -ms-user-select: none; /* IE10 */
    -khtml-user-select: none; /* 早期浏览器 */
    user-select: none;
  }
}
</style>
