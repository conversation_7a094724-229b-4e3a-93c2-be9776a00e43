<template>
    <PageContainer style="position: relative;">
        <div slot="content" class="whole">
            <div class="left">
                <div class="title_box"><svg-icon name="right-arrow" /> 选择设备</div>
                <el-select v-model="filters.sysOf1" placeholder="所属品类" style="width: 230px; padding-top: 10px;"
                    filterable clearable @change="selectCategory">
                    <el-option v-for="(item, index) in deviceTypeList" :key="index" :label="item.dictionaryDetailsName"
                        :value="item.dictionaryDetailsId"></el-option>
                </el-select>
                <el-cascader ref="refHandle" :options="customGroupingList" style="width: 230px; padding-top: 10px;"
                    :props="{ checkStrictly: true, expandTrigger: 'hover' }" clearable placeholder="所属分组"
                    :show-all-levels="false" v-model="handlerValue" @change="handleChange"></el-cascader>
                <el-input v-model="filters.assetsNameCode" style="width: 230px; padding-top: 10px;"
                    placeholder="设备名称/编码" @blur="blurInput"></el-input>
                <div class="left-div">
                    <p v-for="item in listData" :key="item.id" @click="selectClick(item.id)" :title="item.assetsName"
                        :class="{ selected: selectedId === item.id }">{{ item.assetsName }}</p>
                </div>
            </div>
            <div class="right">
                <el-tabs v-model="isKeyDevice" style="margin-bottom: 10px;" @tab-click="handTabClick">
                    <el-tab-pane label="原始数据" name="0"> </el-tab-pane>
                    <el-tab-pane label="极值数据" name="1"> </el-tab-pane>
                </el-tabs>
                <div v-if="isKeyDevice === '0'" style="height: 100%;">
                    <div style="display: flex;">
                        <el-date-picker v-model="filters.dataRange" type="daterange" unlink-panels
                            start-placeholder="开始日期" end-placeholder="结束日期" range-separator="至" clearable
                            :picker-options="pickerOptions" value-format="yyyy-MM-dd" format="yyyy-MM-dd">
                        </el-date-picker>&nbsp;&nbsp;
                        <el-select v-model="selectDeviceFactoryCode" filterable placeholder="请选择设备" class="ipt"
                            @change="selectDev">
                            <el-option v-for="(item, index) in bindingIotAssetsList" :key="index"
                                :label="item.assetsName" :value="item.factoryCode"> </el-option>
                        </el-select>
                        <el-select v-model="selectAssetsProperties" filterable placeholder="请选择参数" class="ipt"
                            clearable>
                            <el-option v-for="(item, index) in bindingIotAssetsPropertiesList" :key="index"
                                :label="item.metadataName" :value="item.metadataTag"> </el-option>
                        </el-select>
                        <el-button type="primary" @click="inquiry">查询</el-button>
                        <el-button type="primary" plain @click="rest">重置</el-button>
                        <el-popover placement="bottom" width="180" trigger="hover" style="margin-left: 15px;">
                            <el-button type="primary" style="font-size: 14px;" icon="el-icon-upload2"
                                @click="originalExportExcel">导出当前设备</el-button>
                            <el-button type="primary" style="font-size: 14px;margin: 5px 0 0 0;" icon="el-icon-upload2"
                                @click="originalExportAll">批量导出设备</el-button>
                            <el-button slot="reference" type="primary">导出</el-button>
                        </el-popover>
                    </div>
                    <div slot="content" style="height: 100%;">
                        <div class="contentTable">
                            <div class="contentTable-main table-content">
                                <TablePage ref="table" v-loading="tableLoading" :showPage="true" border
                                    :tableColumn="tableColumn" :data="tableData" height="calc(100% - 45px)"
                                    :pageData="pageData" @pagination="paginationChange" />
                            </div>
                        </div>
                    </div>
                </div>
                <div v-if="isKeyDevice === '1'" style="height: 100%;">
                    <div style="display: flex;">
                        <div v-for="(v, i) in dateTypeArr" :key="i" :class="{
                            'search-aside-item': true,
                            'search-aside-item-active': selectedTimeType.dateType === v.dateType
                        }" @click="onDateType(v)">
                            {{ v.name }}
                        </div>&nbsp;
                        <!-- 日选择 -->
                        <div v-if="selectedTimeType.dateType == 'day'">
                            <el-date-picker key="day" v-model="dateTime.day" type="date" placeholder="请选择日期"
                                :picker-options="pickerOptions" :clearable="false" value-format="yyyy-MM-dd">
                            </el-date-picker>
                        </div>
                        <!-- 月选择 -->
                        <div v-else-if="selectedTimeType.dateType == 'month'">
                            <el-date-picker key="month" v-model="dateTime.month" type="month" placeholder="请选择日期"
                                :picker-options="pickerOptions" :clearable="false" value-format="yyyy-MM-dd">
                            </el-date-picker>
                        </div>
                        <!-- 年选择 -->
                        <div v-else-if="selectedTimeType.dateType == 'year'">
                            <el-date-picker key="year" v-model="dateTime.year" type="year" placeholder="请选择日期"
                                :picker-options="pickerOptions" :clearable="false" value-format="yyyy-MM-dd">
                            </el-date-picker>
                        </div>&nbsp;
                        <el-select v-model="extremumFacility" filterable placeholder="请选择设备" class="ipt"
                            @change="selectChange">
                            <el-option v-for="(item, index) in extremumFacilityList" :key="index"
                                :label="item.assetsName" :value="item.factoryCode"> </el-option>
                        </el-select>
                        <el-select v-model="extremumParameter" filterable placeholder="请选择参数" class="ipt"
                            @change="extremumName">
                            <el-option v-for="(item, index) in extremumFacilityPropertiesList" :key="index"
                                :label="item.metadataName" :value="item.metadataTag"> </el-option>
                        </el-select>
                        <el-button type="primary" @click="examine">查询</el-button>
                        <el-button type="primary" plain @click="replacement">重置</el-button>
                        <el-popover placement="bottom" width="180" trigger="hover" style="margin-left: 15px;">
                            <el-button type="primary" style="font-size: 14px;" icon="el-icon-upload2"
                                @click="extremumExportExcel('')">导出当前设备</el-button>
                            <el-button type="primary" style="font-size: 14px;margin: 5px 0 0 0;" icon="el-icon-upload2"
                                @click="extremumExportExcel('all')">批量导出设备</el-button>
                            <el-button slot="reference" type="primary">导出</el-button>
                        </el-popover>
                    </div>
                    <div slot="content" style="height: 100%;">
                        <div class="contentTable">
                            <div class="contentTable-main table-content">
                                <el-table v-loading="tableLoading" :data="tableData" height="calc(100% - 45px)" border
                                    style="margin-bottom: 9px;overflow-y: auto;">
                                    <el-table-column prop="assetsName" show-overflow-tooltip
                                        label="设备名称"></el-table-column>
                                    <el-table-column prop="date" show-overflow-tooltip label="日期时间"></el-table-column>
                                    <el-table-column :label="extremumParameterName" align="center">
                                        <el-table-column prop="max" show-overflow-tooltip label="最大值"></el-table-column>
                                        <el-table-column prop="min" show-overflow-tooltip label="最小值"></el-table-column>
                                        <el-table-column prop="avg" show-overflow-tooltip label="平均值"></el-table-column>
                                    </el-table-column>
                                </el-table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--选择设备弹窗 -->
            <template v-if="isEquipment">
                <choiceDevice :dialogShow="isEquipment" :dialogData="filters" :systemCode="systemCode"
                    :isKeyDevice="isKeyDevice" :handlerArr="handlerValue" @submitDialog="submitEquipmentDialog"
                    @closeDialog="closeEquipmentDialog" />
            </template>
        </div>
    </PageContainer>
</template>
<script>
import moment from 'moment'
moment.locale('zh-cn')
import choiceDevice from './choiceDevice.vue'
import { transData } from '@/util'
import tableListMixin from '@/mixins/tableListMixin.js'
export default {
    name: 'spatialChangeRecord',
    components: { choiceDevice, },
    mixins: [tableListMixin],
    props: {
        systemName: {
            type: String,
            default: () => {
                throw '必须参数systemName'
            }
        }
    },
    data() {
        return {
            systemCode: this.$route.meta.systemType,
            deviceTypeList: [],//设备类型
            customGroupingList: [],//自定义分组
            customDefaultProps: {
                label: 'groupName',
                isLeaf: 'leaf',
                children: 'children'
            },
            handlerValue: [],
            listData: [],
            selectedId: null, // 维护选中的 ID
            filters: {
                sysOf1: "",
                groupId: "",
                assetsNameCode: "",
                dataRange: [moment().format('YYYY-MM-DD 00:00:00'), moment().format('YYYY-MM-DD 23:59:59')],
                assetsName: "",
                deviceId: "",
                productId: '',

            },
            selectDeviceFactoryCode: '',
            selectAssetsProperties: '',
            extremumFacility: '',
            extremumParameter: '',
            tableColumn: [
                {
                    prop: 'belongAssetsName',
                    label: '设备名称',
                    width: 200
                },
                {
                    prop: 'assetsName',
                    label: '物联设备',
                    width: 200
                },
                {
                    prop: 'timestamp',
                    label: '日期时间',
                    formatter: (row) => {
                        return moment(row.row.timestamp).format('YYYY-MM-DD HH:mm:ss')
                    },
                    width: 200
                },
                {
                    prop: 'propertiesText',
                    label: '监测参数'
                }
            ],
            pageData: {
                page: 1,
                pageSize: 15,
                total: 0
            },
            tableLoading: false,
            bindingIotAssetsList: [],
            bindingIotAssetsPropertiesList: [],
            extremumFacilityList: [],
            extremumFacilityPropertiesList: [],
            filter: false,
            isEquipment: false,
            isKeyDevice: '0', // 默认原始数据
            tableData: [],
            pagination: {
                current: 1,
                size: 15
            }, // 分页数据
            total: 0,// 数据总条数
            pickerOptions: {
                onPick: ({ maxDate, minDate }) => {
                    this.selectDate = minDate.getTime()
                    if (maxDate) { this.selectDate = '' }
                },
                disabledDate: (time) => {
                    if (this.selectDate !== '') {
                        const one = 30 * 24 * 3600 * 1000
                        const minTime = this.selectDate - one
                        const maxTime = this.selectDate + one
                        return time.getTime() < minTime || time.getTime() > maxTime
                    }
                }
            },
            dateTime: {
                day: moment().format('YYYY-MM-DD'),
                month: null,
                year: null,
            },  // 查询的日期时间
            dateTypeArr: [
                { dateType: 'day', name: '日', status: 0 },
                { dateType: 'month', name: '月', status: 1 },
                { dateType: 'year', name: '年', status: 2 }],
            selectedTimeType: { dateType: 'day' },
            timeType: 1,
            queryParams: {
                endTime: null,
                startTime: null,
                dateType: 0,       //  时间类型
            },
            extremumParameterName: '',
            belongAssetsName: ''
        }
    },
    watch: {
        handlerValue() {
            if (this.$refs.refHandle) {
                this.$refs.refHandle.dropDownVisible = false; //监听值发生变化就关闭它
            }
        },
    },
    created() { },
    mounted() {
        this.$nextTick(() => {
            this.getDeviceType()//品类
            this.getAssetsGroup()//分组
            this.getDataList()//左侧列表
        })
    },
    methods: {
        extremumName(val) {
            this.extremumParameterName = this.extremumFacilityPropertiesList.find(item => {
                return item.metadataTag == val
            }).metadataName
        },
        selectDev() {
            this.selectAssetsProperties = ''
            this.getCustomIotAssetsProperties(this.selectedId, this.selectDeviceFactoryCode, false)
        },
        // 所属品类
        getDeviceType() {
            this.deviceTypeList = []
            let data = {
                dictionaryCode: this.systemCode,
                equipAttr: '2',
            }
            this.$api.querySubCategoryDetailsRelatedAssets(data).then((res) => {
                if (res.code == '200') {
                    this.deviceTypeList = res.data
                }
            })
        },
        selectCategory(val) {
            this.filters.sysOf1 = val
            this.getDataList()
        },
        // 所属分组
        getAssetsGroup() {
            this.customGroupingList = []
            let data = {
                dictionaryDetailsCode: this.systemCode,
                equipAttr: '2',
                groupName: "",
            }
            this.$api.getCustomGroupingTree(data).then((res) => {
                if (res.code == '200') {
                    this.customGroupingList = transData(res.data, 'id', 'pid', 'children')
                }
            })
        },
        // 分组选择
        handleChange(value) {
            this.filters.groupId = value.length > 0 ? value[value.length - 1] : '';
            this.getDataList()
        },
        selectClick(id) {
            // 如果点击的项是已选中的项，则取消选中
            this.selectedId = this.selectedId === id ? null : id;
            this.belongAssetsName = this.listData.find(item => {
                return item.id === id
            }).assetsName
            this.selectAssetsProperties = ""
            this.pageData.page = 0
            this.getCustomIotAssets(id)
        },
        blurInput(event) {
            this.filters.assetsNameCode = event.target.value.replace(/\s+/g, '')
            this.getDataList()
        },
        // 查询
        inquiry() {
            this.pageData.page = 1
            this.pageData.pageSize = 15
            this.filters.dataRange = [moment(this.filters.dataRange[0]).format('YYYY-MM-DD 00:00:00'), moment(this.filters.dataRange[1]).format('YYYY-MM-DD 23:59:59')]
            this.getApplicationList(this.selectDeviceFactoryCode, this.selectAssetsProperties)
        },
        // 重置
        rest() {
            this.pageData.page = 1
            this.pageData.pageSize = 15
            this.filters.dataRange = [moment().format('YYYY-MM-DD 00:00:00'), moment().format('YYYY-MM-DD 23:59:59')]
            this.selectDeviceFactoryCode = this.bindingIotAssetsList[0].factoryCode
            this.selectAssetsProperties = ''
            this.getCustomIotAssetsProperties(this.selectedId, this.selectDeviceFactoryCode, this.filter)
            this.getApplicationList(this.selectDeviceFactoryCode, this.selectAssetsProperties)
        },
        // 获取左侧监测项列表
        getDataList() {
            let params = {
                assetsNameCode: this.filters.assetsNameCode,
                dictionaryDetailsCode: this.systemCode,
                equipAttr: '2',
                groupId: this.filters.groupId,
                page: 1,
                pageSize: 1000,
                sysOf1: this.filters.sysOf1,
            }
            this.listData = []
            this.$api
                .getOperationalMonitoringList(params)
                .then((res) => {
                    if (res.code === '200' || res.code === 200) {
                        this.listData = res.data.records || []
                        this.selectedId = this.listData[0].id;
                        this.belongAssetsName = this.listData.find(item => {
                            return item.id === this.selectedId
                        }).assetsName
                        this.getCustomIotAssets(this.listData[0].id)
                    }
                })
                .catch(() => {
                })
        },
        // 选择设备弹窗关闭
        closeEquipmentDialog() {
            this.isEquipment = false
        },
        // 原始数据导出当前
        originalExportExcel() {
            let param = {
                pageSize: this.pageData.pageSize,
                pageIndex: this.pageData.page - 1,
                terms: [
                    {
                        column: 'timestamp$BTW',
                        value: [moment(this.filters.dataRange[0]).valueOf(), moment(this.filters.dataRange[1]).valueOf()]
                    }
                ]
            }
            if (this.selectAssetsProperties === undefined) {
                this.selectAssetsProperties = ''
            }
            this.$api.getCustomIotExport(this.selectDeviceFactoryCode, this.selectAssetsProperties, this.belongAssetsName, param).then((res) => {
                let url = window.URL.createObjectURL(new Blob([res]), { type: 'application/vnd.ms-excel;charset=UTF-8' })
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', '历史数据.xlsx')
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) // 下载完成移除元素
                window.URL.revokeObjectURL(url) // 释放掉blob对象
            })
        },
        // 原始数据批量导出
        originalExportAll() {
            this.isEquipment = true
        },
        // 极值数据导出
        extremumExportExcel(val) {
            if (val === 'all') {
                this.isEquipment = true
            } else {
                this.handleDateChange()
                let param = {
                    endTime: this.queryParams.endTime,
                    startTime: this.queryParams.startTime,
                    timeType: this.timeType,
                    devicePropertyList: [{
                        assetsName: this.extremumFacilityList
                            .find(item => item.factoryCode === this.extremumFacility)?.assetsName,
                        deviceId: this.extremumFacilityPropertiesList
                            .find(item => item.metadataTag === this.extremumParameter)?.factoryCode,
                        metadataTag: this.extremumFacilityPropertiesList
                            .find(item => item.metadataTag === this.extremumParameter)?.metadataTag,
                        productId: this.extremumFacilityPropertiesList
                            .find(item => item.metadataTag === this.extremumParameter)?.product,
                    }]
                }
                this.$api.getHistoryDataExport(param).then(res => {
                    let url = window.URL.createObjectURL(new Blob([res]), { type: 'application/vnd.ms-excel;charset=UTF-8' })
                    let link = document.createElement('a')
                    link.style.display = 'none'
                    link.href = url
                    link.setAttribute('download', '历史数据.xlsx')
                    document.body.appendChild(link)
                    link.click()
                    document.body.removeChild(link) // 下载完成移除元素
                    window.URL.revokeObjectURL(url) // 释放掉blob对象
                })
            }
        },
        // 选择设备弹窗保存
        submitEquipmentDialog(data) {
            if (this.isKeyDevice === '0') {
                let param = {
                    deviceBatch: data.deviceBatch,
                    jsonObject: {
                        pageSize: this.pageData.pageSize,
                        pageIndex: this.pageData.page - 1,
                        terms: [
                            {
                                column: 'timestamp$BTW',
                                value: [moment(this.filters.dataRange[0]).valueOf(), moment(this.filters.dataRange[1]).valueOf()]
                            }
                        ]
                    }
                }
                this.$api.getHistoryDataExportBatch(param).then((res) => {
                    let url = window.URL.createObjectURL(new Blob([res]), { type: 'application/vnd.ms-excel;charset=UTF-8' })
                    let link = document.createElement('a')
                    link.style.display = 'none'
                    link.href = url
                    link.setAttribute('download', '原始历史数据批量.xlsx')
                    document.body.appendChild(link)
                    link.click()
                    document.body.removeChild(link) // 下载完成移除元素
                    window.URL.revokeObjectURL(url) // 释放掉blob对象
                    this.isEquipment = false
                })
            } else if (this.isKeyDevice === '1') {
                this.handleDateChange()
                let param = {
                    endTime: this.queryParams.endTime,
                    startTime: this.queryParams.startTime,
                    timeType: this.timeType,
                    devicePropertyList: data.deviceBatch
                }
                this.$api.getHistoryDataExport(param).then(res => {
                    let url = window.URL.createObjectURL(new Blob([res]), { type: 'application/vnd.ms-excel;charset=UTF-8' })
                    let link = document.createElement('a')
                    link.style.display = 'none'
                    link.href = url
                    link.setAttribute('download', '极值历史数据批量.xlsx')
                    document.body.appendChild(link)
                    link.click()
                    document.body.removeChild(link) // 下载完成移除元素
                    window.URL.revokeObjectURL(url) // 释放掉blob对象
                    this.isEquipment = false
                })
            }
        },
        //  ------------------------------原始数据
        paginationChange(pagination) {
            Object.assign(this.pageData, pagination)
            this.getApplicationList(this.selectDeviceFactoryCode, this.selectAssetsProperties)
        },
        selectChange(val) {
            this.extremumParameter = ''
            this.extremumParameterName = ''
            if (this.isKeyDevice === '0') {
                this.assetsName = this.bindingIotAssetsList.filter(item => {
                    return item.id === val
                }).assetsName
            } else if (this.isKeyDevice === '1') {
                this.assetsName = this.extremumFacilityList.filter(item => {
                    return item.id === val
                }).assetsName
            }
            this.getCustomIotAssetsProperties(this.selectedId, this.extremumFacility, false)
        },
        // 获取被监测设备绑定列表
        getCustomIotAssets(id) {
            this.$api.getCustomIotAssets(id).then((res) => {
                if (res.code === '200') {
                    this.bindingIotAssetsList = res.data
                    this.selectDeviceFactoryCode = this.bindingIotAssetsList[0].factoryCode
                    this.extremumFacilityList = res.data
                    this.extremumFacility = this.extremumFacilityList[0].factoryCode
                    if (this.isKeyDevice === '0') {
                        this.getCustomIotAssetsProperties(id, this.selectDeviceFactoryCode, this.filter)
                    } else if (this.isKeyDevice === '1') {
                        this.getCustomIotAssetsProperties(id, this.extremumFacility, this.filter)
                    }
                }
            })
        },
        //获取参数
        getCustomIotAssetsProperties(id, code, filter) {
            this.$api.getCustomIotAssetsProperties(id, code, filter).then((res) => {
                if (res.code === '200') {
                    if (this.isKeyDevice === '0') {
                        this.bindingIotAssetsPropertiesList = Array.isArray(res.data) ? res.data : [res.data];
                        this.getApplicationList(this.selectDeviceFactoryCode, this.selectAssetsProperties)
                    } else if (this.isKeyDevice === '1') {
                        this.extremumFacilityPropertiesList = Array.isArray(res.data) ? res.data : [res.data];
                        this.extremumParameter = this.extremumFacilityPropertiesList[0].metadataTag
                            ;
                        this.extremumParameterName = this.extremumFacilityPropertiesList[0].metadataName
                            ;
                        this.getHistoryDataList()
                    }
                }
            })
        },
        // 获取原始数据列表
        getApplicationList(id, code) {
            let param = {
                pageSize: this.pageData.pageSize,
                pageIndex: this.pageData.page - 1,
                terms: [
                    {
                        column: 'timestamp$BTW',
                        value: [moment(this.filters.dataRange[0]).valueOf(), moment(this.filters.dataRange[1]).valueOf()]
                    }
                ]
            }
            this.tableLoading = true
            if (code === undefined) {
                code = ''
            }
            this.tableData = []
            this.$api
                .getQueryPropertiesRow(id, code, this.belongAssetsName, param)
                .then((res) => {
                    if (res.code == 200) {
                        this.tableData = res.data.data
                        this.pageData.total = res.data.total
                    }
                    this.tableLoading = false
                })
                .catch(() => {
                    this.tableLoading = false
                })
        },
        // 获取极值数据列表
        getHistoryDataList() {
            this.handleDateChange()
            let param = {
                assetsName: this.extremumFacilityList
                    .find(item => item.factoryCode === this.extremumFacility)?.assetsName,
                deviceId: this.extremumFacilityPropertiesList
                    .find(item => item.metadataTag === this.extremumParameter)?.factoryCode,
                metadataTag: this.extremumFacilityPropertiesList
                    .find(item => item.metadataTag === this.extremumParameter)?.metadataTag,
                productId: this.extremumFacilityPropertiesList
                    .find(item => item.metadataTag === this.extremumParameter)?.product,
                endTime: this.queryParams.endTime,
                startTime: this.queryParams.startTime,
                timeType: this.timeType,
            }
            this.tableData = []
            this.tableLoading = true
            this.$api
                .getHistoryDataList(param)
                .then((res) => {
                    if (res.code == 200) {
                        this.tableData = res.data
                    }
                    this.tableLoading = false
                })
                .catch(() => {
                    this.tableLoading = false
                })
        },
        // ---------------------------Table-Fn
        // 切换数据
        handTabClick(val) {
            if (val.label === '原始数据') {
                this.rest()

            } else if (val.label === '极值数据') {
                this.replacement()
            }
        },
        // 极值数据查询
        examine() {
            this.pageData.page = 1
            this.pageData.pageSize = 15
            this.getHistoryDataList()
        },
        // 极值数据重置
        replacement() {
            this.pageData.page = 1
            this.pageData.pageSize = 15
            this.extremumFacility = this.extremumFacilityList[0].factoryCode
            this.getCustomIotAssetsProperties(this.selectedId, this.extremumFacility, false)
            this.queryParams.dateType = 0
            this.dateTime = {
                day: moment().format('YYYY-MM-DD'),
                month: null,
                year: null,
            }
            this.selectedTimeType = { dateType: 'day' }
            this.timeType = 1
        },
        handleDateChange() {
            // 时间类型(0:日 1:月 2:年 3:自定义)
            let type = this.queryParams.dateType
            if (type == 0) {
                this.queryParams.startTime = this.dateTime.day
                this.queryParams.endTime = this.dateTime.day
            } else if (type == 1) {
                this.queryParams.startTime = moment(this.dateTime.month).startOf('month').format('YYYY-MM-DD')
                this.queryParams.endTime = moment(this.dateTime.month).endOf('month').format('YYYY-MM-DD')
            } else if (type == 2) {
                this.queryParams.startTime = moment(this.dateTime.year).startOf('year').format('YYYY-MM-DD')
                this.queryParams.endTime = moment(this.dateTime.year).endOf('year').format('YYYY-MM-DD')
            } else if (type == 3) {
                this.queryParams.startTime = this.dateTime.custom[0]
                this.queryParams.endTime = this.dateTime.custom[1]
            }
        },
        onDateType(v) {
            const dateTypeMapping = {
                day: 1,
                month: 3,
                year: 4
            };
            this.timeType = dateTypeMapping[v.dateType] || null;

            this.selectedTimeType = v
            this.queryParams.dateType = v.status
            this.resetDateTime()
        },
        resetDateTime() {
            let current = moment().format('YYYY-MM-DD')
            this.dateTime = {
                day: current,
                month: current,
                year: current,
                custom: [current, current]
            }
        },
    }
}
</script>
<style lang="scss" scoped>
.page-container {
    position: relative;
}

.sino_tab {
    .sino_tab_content {
        height: 280px;
        overflow: auto;

        .sino_tab_btn {
            position: absolute;
            right: 0;
            top: 0;
            margin: 0;
        }

        .line_style {
            border-bottom: 1px solid #ddd;
        }
    }

    .sino_tab_btn_box {
        height: 280px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .floor_box {
        line-height: 32px;
        margin: 0 100px 0 20px;
        padding-left: 15px;
        font-size: 14px;
    }
}

.checkbox_group {
    height: 200px;
    overflow: auto;
}

.el-main {
    padding: 15px;
    overflow: hidden;
    background-color: #fff;

    .sino_panel {
        height: 100%;
        border-radius: 10px;
        background: #fff;
        position: relative;
        font-size: 14px;
    }

    .sino_page {
        height: 100%;
        position: relative;

        .el-aside {
            width: 260px;
            margin: 0 16px 0 0;
            overflow: hidden;
        }

        .sino_page_left {
            margin: 0 0 0 16px !important;
        }

        .el-aside,
        .el-main {
            // height: 100%;
            background: #fff;
            border-radius: 10px;
            padding: 0;

            .el-collapse {
                height: calc(100% - 40px);
                overflow: auto;
            }
        }
    }
}

.title_box {
    box-sizing: border-box;
    padding-left: 24px;
    height: 50px;
    width: 100%;
    line-height: 50px;
    text-align: left;
    border-bottom: 1px solid #d8dee7;

    .title-tip {
        display: inline-block;
        width: 4px;
        height: 16px;
        margin-right: 8px;
        position: relative;
        top: 2px;
        background: #5188fc;
    }

    .title_name {
        font-size: 16px;
        font-weight: bold;
    }

    .title_btn_icon {
        float: right;
    }

    .title_btn_icon i {
        margin-right: 20px;
        cursor: pointer;
    }
}

.whole {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;

    .left {
        text-align: center;
        width: 250px;
        height: 100%;
        background-color: #fff;
        border-radius: 5px;
        overflow: hidden;

        .left_d {
            height: calc(100% - 20px);
        }

        .left-div {
            text-align: left;
            margin-top: 10px;
            overflow-y: auto;
            height: calc(100% - 200px);

            .selected {
                color: #007bff;
            }

            p {
                cursor: pointer;
                padding-left: 35px;
                width: 230px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
    }

    .right {
        width: calc(100% - 260px);
        background-color: #fff;
        height: 100%;
        border-radius: 5px;
        padding: 10px;

        .search-aside-item {
            display: inline-block;
            font-size: 14px;
            padding: 0 20px;
            height: 32px;
            line-height: 32px;
            font-family: PingFangSC-Regular;
            color: $color-primary;
            border: 1px solid $color-primary;
            background: #fff;
            cursor: pointer;
            margin-right: 5px;

            &:hover,
            &:focus {
                color: #fff;
                font-family: PingFangSC-Regular;
                border-color: $color-primary;
                background-color: $color-primary;
                font-weight: 500;
            }
        }

        .search-aside-item-active {
            color: #fff;
            font-family: PingFangSC-Regular;
            border-color: $color-primary;
            background-color: $color-primary;
            font-weight: 500;
        }
    }
}

.contentTable {
    height: calc(100% - 80px);
    background: #fff;
    border-radius: 4px;
    padding: 16px 0 0;
    display: flex;
    flex-direction: column;
    overflow: auto;

    .contentTable-main {
        flex: 1;
        overflow: auto;
    }

    .contentTable-footer {
        padding: 10px 0 0;
    }

    .alarmLevel {
        padding: 3px 6px;
        border-radius: 4px;
        color: #fff;
        line-height: 14px;
    }

    .alarmStatus {
        position: relative;
        display: inline-block;
        padding-left: 12px;

        .alarmStatusIcon {
            display: inline-block;
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            border-radius: 100%;
        }
    }

    .collectIcon {
        font-size: 16px;
        margin-right: 4px;
    }
}

.sino_tree_box {
    margin: 10px;
    height: calc(100% - 100px) !important;
    overflow: auto;

    .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
        background-color: #f5f6fb;
    }

    .el-tree-node__content {
        height: 38px;

        .el-tree-node__label {
            font-size: 15px;
        }
    }
}

.color_blue {
    color: #5197fd;
    cursor: pointer;
}

.ipt {
    width: 200px;
    margin-right: 10px;
}

.el_radio {
    width: 120px;
    padding: 5px;
    overflow: hidden;
    text-overflow: ellipsis;
}

::v-deep .el-dialog {
    width: 70%;
}
</style>