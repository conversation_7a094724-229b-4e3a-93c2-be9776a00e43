<template>
  <PageContainer>
    <div slot="header">
      <div class="search-header">
        <div class="search-from">
          <span class="ipt-title">公告标题：</span>
          <el-input v-model="select.title" placeholder="请输入" maxlength="20" show-word-limit style="width: 200px;"></el-input>
          <span class="ipt-title">公告类别：</span>
          <el-select v-model="select.typeCode" class="el-input left" :disabled="readonly" placeholder="请选择">
            <el-option v-for="item in noticeType" :key="item.typeCode" :label="item.typeName" :value="item.typeCode"></el-option>
          </el-select>
          <span class="ipt-title">公告状态：</span>
          <el-select v-model="select.status" class="el-input left" :disabled="readonly" placeholder="请选择">
            <el-option v-for="item in statusList" :key="item.dictCode" :label="item.dictLabel" :value="item.dictValue"></el-option>
          </el-select>
          <span class="ipt-title">发布人：</span>
          <el-input v-model="select.issuerName" placeholder="请输入" style="width: 200px;" maxlength="20" show-word-limit></el-input>
          <el-button type="primary" @click="reset">重置</el-button>
          <el-button type="primary" @click="queryList">查询</el-button>
        </div>
        <div>
          <el-button type="primary" icon="el-icon-plus" @click="addNotice">新建公告</el-button>
          <!-- <el-button type="primary" icon="el-icon-refresh-right" style="min-width: 35px; background: #fff; color: #5188fc;" @click="queryList(1)"></el-button> -->
        </div>
      </div>
    </div>
    <div slot="content" style="height: 100%; margin-top: 16px; background-color: #fff;">
      <div class="table-content">
        <el-table
          v-loading="tableLoading"
          :data="tableData"
          reserve-selection="true"
          :row-key="getRowKeys"
          border
          stripe
          :height="tableHeight"
          @selection-change="handleSelectionChange"
        >
          <!-- <template slot="empty">
            <p class="tableEmptyLabel">{{ $store.state.tableLabel }}</p>
          </template> -->
          <el-table-column type="index" label="序号" width="65">
            <template slot-scope="scope">
              <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="公告标题" prop="title" show-overflow-tooltip></el-table-column>
          <el-table-column prop="typeName" show-overflow-tooltip label="公告类别"></el-table-column>
          <el-table-column prop="overview" show-overflow-tooltip label="公告概述"></el-table-column>
          <el-table-column prop="statusName" show-overflow-tooltip label="公告状态">
            <template slot-scope="scope">
              <span v-if="scope.row.statusName == '草稿'" type="success" style="color: #f98784;" class="tablebtn-c4">草稿</span>
              <span v-if="scope.row.statusName == '已发布'" type="info" class="tablebtn-c1">已发布</span>
              <span v-if="scope.row.statusName == '待发布'" type="info" style="color: #2bbdbb;" class="tablebtn-c1">待发布</span>
            </template>
          </el-table-column>
          <el-table-column prop="issuerName" show-overflow-tooltip label="发布人"></el-table-column>
          <el-table-column prop="issueTime" show-overflow-tooltip label="发布时间"></el-table-column>
          <el-table-column show-overflow-tooltip width="260" label="操作">
            <template slot-scope="scope" style="width: 260px;">
              <span class="operation-text" style="background: #77affd; cursor: pointer;" @click="LockNotice(scope.row)">查看</span>
              <span v-if="scope.row.statusName !== '已发布'" class="operation-text" style="background: #2abdbb; cursor: pointer;" @click="issueNotice(scope.row)">发布</span>
              <span v-if="scope.row.statusName !== '已发布'" style="background: #2abdbb; cursor: pointer;" class="operation-text" @click="addNotice(scope.row)">修改</span>
              <span style="background: #f98784; cursor: pointer;" class="operation-text" @click="delNotice(scope.row)">删除</span>
            </template>
          </el-table-column>
        </el-table>
        <div class="table-page">
          <el-pagination
            class="pagination imas_page"
            :current-page="paginationData.currentPage"
            :page-sizes="[15, 30, 50, 100]"
            :page-size="paginationData.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="paginationData.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
      <el-dialog title="通知公告" :visible.sync="dialogVisibleRole" custom-class="model-dialog" @close="close">
        <div v-loading class="dialog-contnet">
          <div class="caption">
            <div class="caption-text">
              <span>公告类别：</span>
              <span>{{ row.typeName }}</span>
            </div>
            <div class="caption-text">
              <span>{{ row.issueTime }}</span>
            </div>
          </div>
          <div class="preview ql-snow" style>
            <div class="ql-editor" v-html="row.content"></div>
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="close">关闭</el-button>
        </span>
      </el-dialog>
    </div>
  </PageContainer>
</template>

<script>
export default {
  name: 'announcements',
  data() {
    return {
      select: {
        title: '',
        typeCode: '',
        status: '',
        issuerName: ''
      },
      tableData: [],
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      noticeType: [],
      leibie: '',
      tableLoading: false,
      readonly: false,
      row: {},
      dialogVisibleRole: false,
      statusList: []
    }
  },
  computed: {
    // eslint-disable-next-line vue/no-dupe-keys
    tableHeight() {
      return document.body.clientHeight - 260
    }
  },
  watch: {
    screenHeight(n, o) {}
  },
  mounted() {
    this.getNoticeList()
    this.getNoticeType()
  },
  methods: {
    /**
     * 获取公告类别
     */
    getNoticeType() {
      // 公告类别
      this.$api.ipsmGetNoticeType({}).then((res) => {
        if (res.code == 200) {
          this.noticeType = res.data
          this.paginationData.total = parseInt(res.data.sum)
        } else {
          this.$message.error(res.message)
        }
      })
      // // 公告状态
      this.$api
        .ipsmGetDictValue({
          dictType: 'notice_status',
          isShowParent: 0
        })
        .then((res) => {
          if (res.code == 200) {
            this.statusList = res.data
          } else {
            this.$message.error(res.message)
          }
        })
    },
    /**
     * 获取公告列表
     */
    getNoticeList() {
      this.tableLoading = true
      this.$api
        .ipsmGetNoticeList({
          title: this.select.title,
          typeCode: this.select.typeCode,
          status: this.select.status,
          issuerName: this.select.issuerName,
          currentPage: this.paginationData.currentPage,
          pageSize: this.paginationData.pageSize
        })
        .then((res) => {
          this.tableLoading = false
          if (res.code == 200) {
            this.tableData = res.data.list
            this.paginationData.total = parseInt(res.data.total)
          } else if (res.code == '502') {
            this.$message.error(res.message)
          }
        })
        .catch(() => {
          this.tableLoading = this.$store.state.loadingShow
        })
    },
    // 发布公告
    issueNotice(row) {
      this.$confirm('确认发布这条公告?', '提醒', { type: 'warning' }).then((res) => {
        this.$api
          .ipsmIssueNotice({
            noticeId: row.noticeId
          })
          .then((res) => {
            if (res.code == 200) {
              this.$message.success(res.message)
            } else {
              this.$message.error(res.message)
            }
            this.getNoticeList()
          })
      })
    },
    // 删除公告
    delNotice(row) {
      this.$confirm('确认删除这条公告?', '提醒', { type: 'warning' }).then((res) => {
        this.$api
          .ipsmDelNotice({
            noticeId: row.noticeId
          })
          .then((res) => {
            if (res.code == 200) {
              this.$message.success(res.message)
            } else {
              this.$message.error(res.message)
            }
            this.getNoticeList()
          })
      })
    },
    // 查看公告
    LockNotice(row) {
      this.row = row
      this.dialogVisibleRole = true
    },
    close(val) {
      this.dialogVisibleRole = false
    },
    // 查询列表
    queryList(i) {
      if (i) {
        this.paginationData.currentPage = 1
        this.paginationData.pageSize = 15
      }
      this.getNoticeList()
    },
    reset() {
      this.select.title = ''
      this.select.typeCode = ''
      this.select.status = ''
      this.select.issuerName = ''
      this.getNoticeList()
    },
    addNotice(id) {
      if (id.noticeId) {
        this.$router.push({
          name: 'addNotice',
          query: {
            id: id?.noticeId
          }
        })
      } else {
        this.$router.push({
          name: 'addNotice'
        })
      }
    },
    handleChange(val) {},
    getRowKeys(row) {
      return row.id
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
      this.queryList()
    },
    handleSizeChange(val) {
      this.paginationData.currentPage = 1
      this.paginationData.pageSize = val
      this.queryList()
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.queryList()
    }
  }
}
</script>

<style lang="scss" scoped>
.search-header {
  padding: 10px 16px;

  .search-from {
    padding-bottom: 12px;

    & > div {
      margin-top: 12px;
      margin-right: 10px;
    }
  }
}

.table-content {
  padding: 16px;
  height: calc(100% - 80px);
}

.ipt-title {
  width: 94px;
  display: inline-block;
  text-align: right;
  // white-space: nowrap;
  margin-top: 20px;
}

.operation-text {
  width: 40px;
  display: inline-block;
  margin-right: 5px;
  color: #fff;
  text-align: center;
}

.dialog-contnet {
  background-color: #fff;
  width: 100%;
  padding: 10px;

  .caption {
    height: 36px;
    line-height: 36px;
    border-bottom: 1px solid #ddd;

    .caption-text {
      display: inline-block;
      margin-right: 40px;
    }
  }

  .preview {
    .ql-editor {
      max-height: 328px;
      overflow: auto;
    }
  }
}
</style>
