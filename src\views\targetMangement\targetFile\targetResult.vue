<template>
  <PageContainer>
    <div slot="content" class="targetResult-body">
      <div class="targetResult-content-title">
        <span @click="goBack"> <i class="el-icon-arrow-left"></i><span style="margin-left: 10px">指标结果</span> </span>
      </div>
      <div class="targetResult-content">
        <div class="targetResult-content-top">
          <div class="topTitle">{{ planInfo.nodeName || '' }}</div>
          <div class="chartBox">
            <div class="chartBox-left">
              <img src="@/assets/images/targetAnalysis/targetResult-record.png" alt="" />
              <div class="chartLeft-info">
                <p>
                  <span>计划名称：</span><span>{{ planInfo.indicatorPlanName || '' }}</span>
                </p>
                <p>
                  <span>指标模版：</span><span>{{ planInfo.templateManageName || '' }}</span>
                </p>
                <p>
                  <span>考察周期：</span><span>{{ planInfo.cycleName || '' }}</span>
                </p>
              </div>
            </div>
            <div v-if="scoreData.length && scoreData.length > 0" class="chartBox-right">
              <div class="echart-title">
                <span>综合得分</span><span>{{ planInfo.scoreTotal }}</span
                ><img :src="planInfo.trend === 1 ? riseImg : planInfo.trend === 2 ? downImg : levelImg" alt="" />
              </div>
              <div id="Echart" style="height: 100%; width: 100%"></div>
            </div>
          </div>
        </div>
        <div class="targetResult-content-bottom">
          <div class="contentTable">
            <div class="contentTable-main table-content">
              <el-table v-loading="tableLoading" border style="width: 100%" :data="tableData" :height="tableHeight" :row-class-name="tableRowClassName">
                <el-table-column prop="indicatorLibraryName" label="指标名称" show-overflow-tooltip></el-table-column>
                <el-table-column v-if="scoreData.length" prop="weights" label="权重" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span>{{ scope.row.weights }}%</span>
                  </template></el-table-column
                >
                <el-table-column prop="illustrate" label="指标说明" show-overflow-tooltip></el-table-column>
                <el-table-column prop="nodeName" label="指标分类" show-overflow-tooltip></el-table-column>
                <el-table-column v-if="scoreData.length" prop="score" label="得分" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <el-tooltip content="Bottom center" placement="right-start" effect="light">
                      <div slot="content" class="tooltip">
                        <div>得分</div>
                        <div>本周期：{{ scope.row.score }}</div>
                        <div>上周期：{{ scope.row.lastScore }}</div>
                      </div>
                      <span>{{ scope.row.score }}<img class="tableImg" :src="scope.row.trend === 1 ? riseImg : scope.row.trend === 2 ? downImg : levelImg" alt="" /> </span>
                    </el-tooltip>
                  </template>
                </el-table-column>
                <el-table-column prop="data" label="数据" show-overflow-tooltip></el-table-column>
                <el-table-column v-if="scoreData.length" prop="calculationMethodString" label="计算方法" show-overflow-tooltip></el-table-column>
                <el-table-column label="操作" width="180" fixed="right">
                  <template slot-scope="scope">
                    <el-button type="text" class="record" @click="trackDown(scope.row)">指标跟踪</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <!-- <div class="contentTable-footer">
              <el-pagination
                :current-page="pagination.current"
                :page-sizes="pagination.pageSizeOptions"
                :page-size="pagination.size"
                :layout="pagination.layoutOptions"
                :total="pagination.total"
                @size-change="paginationSizeChange"
                @current-change="paginationCurrentChange"
              >
              </el-pagination>
            </div> -->
          </div>
        </div>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import * as echarts from 'echarts'
import tableListMixin from '@/mixins/tableListMixin.js'
import riseImg from '@/assets/images/targetAnalysis/rise.png'
import downImg from '@/assets/images/targetAnalysis/down.png'
import levelImg from '@/assets/images/targetAnalysis/level.png'
export default {
  name: 'targetResult',
  mixins: [tableListMixin],
  data() {
    return {
      riseImg,
      downImg,
      levelImg,
      tableLoading: false,
      tableData: [],
      current: 1,
      size: 15,
      total: 0,
      currentIndex: -1,
      scoreData: [],
      planInfo: {
        templateManageName: '', // 模板名称
        cycleName: '', // 周期名称
        indicatorPlanName: '', // 计划名称
        scoreTotal: 0, // 总得分
        totalTrend: 0, // 总趋势
        nodeName: '' // 名称
      }
    }
  },
  computed: {},
  created() {
    this.getTargetFileInfo()
  },
  methods: {
    getTargetFileInfo() {
      this.tableLoading = true
      const { planId, manageId, nodeCode, nodeName } = this.$route.query
      let data = {
        planId: planId,
        manageId: manageId,
        nodeCode: nodeCode || '',
        nodeName: nodeName || ''
      }
      this.$api.getTargetFileById(data).then((res) => {
        this.tableLoading = false
        if (res.code == 200) {
          this.planInfo = res.data
          this.tableData = res.data.list
          this.scoreData = res.data.scoreList
          if (this.scoreData) {
            this.$nextTick(() => {
              this.getPieData(res.data.scoreList)
            })
          } else {
            this.scoreData = []
          }
        } else if (res.message) {
          this.$message.error(res.message)
        }
      })
    },
    // 图标渲染
    getPieData(list) {
      const getchart = echarts.init(document.getElementById('Echart'))
      getchart.resize()
      var scaleData = list
      var data = []
      for (var i = 0; i < scaleData.length; i++) {
        data.push({
          value: scaleData[i].score,
          name: scaleData[i].libraryName ? scaleData[i].libraryName : '',
          trend: scaleData[i].trend
        })
      }
      const option = {
        color: ['#D56383', '#00FF95', '#FFE900', '#F5B157', '#1DA7FF', '#2967EA'],
        legend: {
          icon: 'circle',
          orient: 'vertical',
          // type: 'scroll',
          itemWidth: 10,
          top: '25%',
          bottom: '20%',
          left: '45%',
          itemHeight: 8,
          itemGap: 10,
          textStyle: {
            rich: {
              a: {
                padding: [0, 20, 0, 10],
                color: '#666',
                fontSize: 14,
                align: 'left',
                width: 140
              },
              b: {
                color: '#666',
                fontSize: 14,
                align: 'right',
                width: 40,
                padding: [0, 10, 0, 0]
              },
              c: {
                padding: [0, 0, 0, 40],
                color: '#666',
                fontSize: 14,
                align: 'right'
              },
              img: {
                backgroundColor: {
                  image: riseImg
                },
                align: 'right'
              },
              img1: {
                backgroundColor: {
                  image: downImg
                },
                align: 'right'
              },
              img2: {
                backgroundColor: {
                  image: levelImg
                },
                align: 'right'
              }
            }
          },
          formatter: function (libraryName) {
            let target, imgValue
            for (let i = 0; i < list.length; i++) {
              if (list[i].libraryName === libraryName) {
                target = list[i].score
                imgValue = list[i].trend
              }
            }
            if (imgValue === 1) {
              return `{a| ${libraryName}} {b|${target}}{img|}{c|}`
            } else if (imgValue === 2) {
              return `{a| ${libraryName}} {b|${target}}{img1|}{c|}`
            } else {
              return `{a| ${libraryName}} {b|${target}}{img2|}{c|}`
            }
          }
        },
        series: [
          {
            name: '',
            avoidLabelOverlap: true, // 防止标签重叠
            type: 'pie',
            radius: ['70%', '85%'], // 大小
            center: ['30%', '50%'], // 位置
            data: data,
            label: {
              show: false,
              position: 'center',
              formatter: function (params) {
                let target = ''
                for (let i = 0; i < list.length; i++) {
                  if (list[i].libraryName === params.name) {
                    target = list[i].levelName
                  }
                }
                return `{b_style| 指标等级} \n{c_style|${target}}`
              },
              rich: {
                b_style: {
                  padding: [10, 0, 0, 0],
                  fontSize: 18,
                  fontWeight: 400,
                  color: '#333333'
                },
                c_style: {
                  padding: [15, 0, 0, 0],
                  fontSize: 14,
                  fontWeight: 400,
                  color: '#666666'
                }
              }
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '14',
                fontWeight: 'normal'
              }
            },
            labelLine: {
              show: false
            }
          },
          // 内环
          {
            name: '内环',
            type: 'pie',
            silent: true,
            radius: ['64%', '61%'], // 大小
            center: ['30%', '50%'], // 位置
            label: {
              show: false
            },
            itemStyle: {
              labelLine: {
                show: false
              },
              color: new echarts.graphic.RadialGradient(0.5, 0.5, 1, [
                {
                  offset: 1,
                  color: 'rgba(133,145,206,0.15)'
                },
                {
                  offset: 0,
                  color: 'rgba(133,145,206,0.15)'
                }
              ]),
              shadowBlur: 60
            },
            data: [0]
          },
          // 外边框
          {
            name: '外边框',
            type: 'pie',
            radius: ['90%', '92%'], // 大小
            center: ['30%', '50%'], // 位置
            label: {
              show: false
            },
            emphasis: {
              scale: false
            },
            data: [
              {
                value: 0,
                name: '',
                itemStyle: {
                  borderWidth: 1,
                  color: 'rgba(133,145,206,0.15)',
                  borderColor: 'rgba(133,145,206,0.15)'
                }
              }
            ]
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
      this.currentIndex = -1
      this.startTooltipLoop(getchart, list.length)
      // 鼠标移入暂停
      getchart.on('mouseover', (params) => {
        getchart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: this.currentIndex
        })
      })
      // 鼠标移出继续
      getchart.on('mouseout', (params) => {
        this.startTooltipLoop(getchart, list.length)
      })
    },
    startTooltipLoop(getchart, length) {
      // 取消之前高亮的图形
      getchart.dispatchAction({
        type: 'downplay',
        seriesIndex: 0,
        dataIndex: this.currentIndex
      })
      this.currentIndex = (this.currentIndex + 1) % length
      // 高亮当前图形
      getchart.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: this.currentIndex
      })
      // 显示tooltip
      // getchart.dispatchAction({
      //   type: 'showTip',
      //   seriesIndex: 0,
      //   dataIndex: this.currentIndex
      // })
    },
    // table隔行变色
    tableRowClassName({ row, rowIndex }) {
      if (rowIndex % 2 == 1) {
        return 'warning-row'
      } else {
        return 'success-row'
      }
    },
    trackDown(val) {
      this.$router.push({
        path: '/targetAnalysis/targetTracking',
        query: {
          libraryId: val.indicatorLibraryId,
          planId: val.indicatorPlanId,
          manageId: val.templateManageId,
          nodeCode: val.nodeCode
        }
      })
    },
    // 返回
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>
<style lang="scss" scoped>
.targetResult-body {
  height: 100%;
  width: 100%;
  background: #fff;
  .targetResult-content-title {
    border-bottom: 1px solid #e4e7ed;
    padding: 13px 24px;
    cursor: pointer;
  }
  .targetResult-content {
    padding: 8px 24px;
    height: calc(100% - 50px);
    .targetResult-content-top {
      .topTitle {
        font-size: 16px;
        font-weight: 600;
        color: #333333;
        padding-top: 16px;
      }
      .chartBox {
        margin: auto;
        display: flex;
        padding: 16px 0 30px 0;
        .chartBox-left {
          width: 40%;
          display: flex;
          .chartLeft-info {
            margin: auto 0;
            p {
              height: 16px;
            }
            p span:nth-child(1) {
              font-size: 14px;
              font-weight: 300;
              color: #7f848c;
              margin: 0 16px;
            }
            p span:nth-child(2) {
              font-size: 14px;
              font-weight: 400;
              color: #333333;
            }
          }
        }
        .chartBox-right {
          width: 50%;
          height: 162px;
          position: relative;
          .echart-title {
            position: absolute;
            top: 7%;
            left: 45.5%;
            span:nth-child(1),
            span:nth-child(2) {
              color: #333333;
            }
            span:nth-child(2) {
              margin: 0 10px;
              vertical-align: middle;
            }
          }
        }
      }
    }
    .targetResult-content-bottom {
      height: calc(100% - 272px);
      min-width: 0;
      background: #fff;
      border-radius: 4px;
      flex: 1;
      .contentTable {
        height: calc(100%);
        display: flex;
        flex-direction: column;
        .contentTable-main {
          flex: 1;
          overflow: auto;
        }
        .contentTable-footer {
          padding: 10px 0 0;
        }
        .tableImg {
          vertical-align: middle;
          margin-left: 5px;
        }
      }
    }
    .content {
      width: 100%;
      max-height: 500px !important;
      overflow: auto;
      background-color: #fff !important;
    }
  }
}
.record {
  color: #3562db !important;
}
.tooltip {
  div {
    padding: 2px 4px;
  }
}
::v-deep .warning-row {
  background-color: #f5f5f5 !important;
}
</style>
