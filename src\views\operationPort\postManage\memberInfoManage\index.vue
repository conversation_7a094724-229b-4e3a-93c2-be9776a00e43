<template>
  <PageContainer class="positionManage-list">
    <template #content>
      <div class="positionManage-list__left">
        <div class="positionManage-list__left__tree">
          <div class="space-tree__search">
            <el-input v-model="treeSearchKeyWord" placeholder="输入关键字搜索" suffix-icon="el-icon-search" clearable></el-input>
          </div>
          <el-tree
            ref="treeRef"
            v-loading="treeLoading"
            class="space-tree__tree"
            :data="treeData"
            node-key="id"
            :props="defaultProps"
            size="small"
            :highlight-current="true"
            :filter-node-method="filterNode"
            :expand-on-click-node="false"
            @node-click="nodeClick"
          >
            <template #default="{ node, data }">
              <div class="custom-tree-node">
                <div class="custom-tree-node-wrapper">
                  <el-tooltip class="item" effect="dark" :content="node.label" placement="top-start" :disabled="node.label.length < 13">
                    <span class="custom-tree-node-label">
                      {{ node.label }}
                    </span>
                  </el-tooltip>
                </div>
              </div>
            </template>
          </el-tree>
        </div>
      </div>
      <div class="positionManage-list__right">
        <el-form ref="formRef" class="positionManage-list__form" :model="searchForm" inline>
          <el-form-item prop="userName">
            <el-input v-model="searchForm.userName" placeholder="搜索姓名、工号、手机号" suffix-icon="el-icon-search" clearable></el-input>
          </el-form-item>
          <el-form-item prop="showType">
            <el-select v-model="searchForm.showType" placeholder="类型" clearable>
              <el-option v-for="item of formOptions.showType" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" plain @click="onReset">重置</el-button>
            <el-button type="primary" @click="onSearch">搜索</el-button>
          </el-form-item>
        </el-form>
        <div class="positionManage-list__table_actions">
          <el-button v-auth="'memberInfoManage:add'" type="primary" icon="el-icon-plus" @click="onOperate(OperateType.ADD)">添加成员</el-button>
          <el-popover placement="right" trigger="hover">
            <el-button type="primary" icon="el-icon-download" style="width: 120px" @click="onOperate(OperateType.DOWNLOAD)"> 模板下载 </el-button>
            <el-button type="primary" icon="el-icon-plus" style="display: block; margin: 8px 0 0 0; width: 120px" @click="onOperate(OperateType.IMPORT)"> 批量导入 </el-button>
            <template #reference>
              <el-button v-auth="'memberInfoManage:import'" type="primary" plain style="margin: auto 8px">导入</el-button>
            </template>
          </el-popover>
          <el-button v-auth="'memberInfoManage:export'" type="primary" plain :disabled="selectionList.length === 0" @click="onOperate(OperateType.EXPORT)">导出</el-button>
          <el-button v-auth="'memberInfoManage:delete'" type="danger" plain :disabled="!checkedIds.length" @click="onOperate(OperateType.BATCH_DELETE)">删除 </el-button>
        </div>
        <div class="positionManage-list__table">
          <el-table
            v-loading="tableLoading"
            height="100%"
            :data="tableData"
            border
            stripe
            table-layout="auto"
            class="tableAuto"
            @selection-change="(rows) => (selectionList = rows)"
          >
            <el-table-column type="selection" width="50"></el-table-column>
            <el-table-column label="序号" width="55">
              <template slot-scope="scope">
                <span>{{ (pagination.current - 1) * pagination.size + scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="staffName" show-overflow-tooltip label="姓名"></el-table-column>
            <el-table-column prop="staffNum" show-overflow-tooltip label="工号"></el-table-column>
            <el-table-column prop="sex" label="性别" show-overflow-tooltip>
              <template slot-scope="scope">
                {{ scope.row.sex == 0 ? '女' : scope.row.sex == 1 ? '男' : '' }}
              </template>
            </el-table-column>
            <el-table-column prop="phone" label="手机号" show-overflow-tooltip></el-table-column>
            <el-table-column prop="phoneOffice" label="办公号码" show-overflow-tooltip></el-table-column>
            <el-table-column prop="workEntryDay" show-overflow-tooltip label="入职天数"></el-table-column>
            <el-table-column prop="unit" show-overflow-tooltip label="归属单位"></el-table-column>
            <el-table-column label="操作" width="180px">
              <template #default="{ row }">
                <el-button type="text" @click="onOperate(OperateType.VIEW, row)">查看</el-button>
                <el-button v-auth="'memberInfoManage:edit'" type="text" @click="onOperate(OperateType.CERTIFICATE, row)">管理证书</el-button>
                <el-dropdown @command="(command) => onOperate(command, row)">
                  <el-button type="text" style="margin-left: 10px">更多</el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item v-auth="'memberInfoManage:edit'" :command="OperateType.EDIT"> 编辑 </el-dropdown-item>
                      <el-dropdown-item v-auth="'memberInfoManage:delete'" style="color: #ff1919" :command="OperateType.DELETE"> 删除 </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination
          :layout="pagination.layoutOptions"
          :current-page="pagination.current"
          :page-sizes="pagination.pageSizeOptions"
          :page-size="pagination.size"
          :total="pagination.total"
          @size-change="paginationSizeChange"
          @current-change="paginationCurrentChange"
        ></el-pagination>
      </div>
      <!-- 导入人员文件 -->
      <template v-if="fileImportShow">
        <fileImportDialog
          v-if="fileImportShow"
          :fileImportShow="fileImportShow"
          @submitfileImportDialog="submitfileImportDialog"
          @closefileImportDialog="() => (fileImportShow = false)"
        />
      </template>
    </template>
  </PageContainer>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin'
import memberDialog from '../components/selectPersons.vue'
import certificateDialog from '../components/certificateDialog.vue'
import fileImportDialog from './components/fileImportDialog.vue'
import { transData } from '@/util'
import axios from 'axios'
export default {
  name: 'memberInfoManage',
  components: {
    memberDialog,
    certificateDialog,
    fileImportDialog
  },
  mixins: [tableListMixin],
  data() {
    return {
      checkInVisible: false,
      // 树搜索关键字
      treeSearchKeyWord: '',
      treeLoading: false, // tree loading
      treeData: [], // tree数据
      defaultProps: {
        children: 'child',
        label: 'deptName'
      },
      menuEvents: [
        { label: '新增下级', funcName: 'addJunior' },
        { label: '编辑', funcName: 'edit' },
        { label: '删除', funcName: 'remove' }
      ],
      tableLoading: false,
      tableData: [],
      // 选中的行
      selectionList: [],
      // 搜索表单
      searchForm: {
        // 搜索姓名、工号、手机号
        userName: '', // 名称
        showType: 1
      },
      // 表单搜索项
      formOptions: {
        // 房型
        showType: [
          {
            label: '仅展示直属成员',
            value: 2
          },
          {
            label: '展示全部成员',
            value: 1
          }
        ]
      },
      fileImportShow: false, // 文件导入
      staffId: '',
      checkItem: {} // 当前树选中项
    }
  },
  computed: {
    OperateType() {
      return {
        // 增加
        ADD: 'add',
        // 批量删除
        BATCH_DELETE: 'batch_delete',
        // 删除
        DELETE: 'delete',
        // 导出
        EXPORT: 'export',
        // 下载模板
        DOWNLOAD: 'download',
        // 导入
        IMPORT: 'import',
        // 编辑
        EDIT: 'edit',
        // 查看
        VIEW: 'view',
        // 管理证书
        CERTIFICATE: 'certificate'
      }
    },
    checkedIds() {
      return this.selectionList.map((it) => it.id)
    }
  },
  watch: {
    treeSearchKeyWord(val) {
      this.$refs.treeRef.filter(val)
    }
  },
  mounted() {
    this.getUnitListFn()
  },
  methods: {
    // 单位树
    getUnitTree(unitList, hosptailInfo) {
      unitList.forEach(async (v) => {
        let { data: deptList } = await this.$api.supplierAssess.getSelectedDept({
          unitId: v.umId
        })
        v.disabled = true
        v.id = v.id
        v.umId = v.umId
        v.deptName = v.unitComName
        v.children = transData(deptList, 'id', 'pid', 'children')
        this.setDisable(v.children)
      })
      hosptailInfo.deptName = hosptailInfo.unitComName
      hosptailInfo.disabled = true
      hosptailInfo.id = hosptailInfo.id
      hosptailInfo.umId = hosptailInfo.umId
      let timeout = setTimeout(() => {
        hosptailInfo.children = unitList
        this.treeData = [hosptailInfo]
        this.treeLoading = false
        clearTimeout(timeout)
      }, 1000)
    },
    setDisable(data) {
      data.forEach((v) => {
        if (v.children && v.children.length) {
          v.disabled = true
          this.setDisable(v.children) // 子级循环时把这一层数据的count传入
        }
      })
    },
    // 获取单位列表
    getUnitListFn() {
      this.treeLoading = true
      this.$api.supplierAssess.getDeptTreeData({ name: '' }).then((res) => {
        this.treeLoading = false
        if (res.code == 200) {
          this.treeData = res.data
          this.checkItem = this.treeData[0]
          this.$nextTick(() => {
            const firstNode = this.$refs.treeRef.$el.querySelector('.el-tree-node__content')
            if (firstNode) {
              firstNode.classList.add('highlight-node') // 添加自定义的类来控制样式。
            }
          })
          this.getDataList()
        }
      })
    },
    // 树节点过滤方法
    filterNode(value, data) {
      if (!value) return true
      return data.deptName.indexOf(value) !== -1
    },
    nodeClick(data, node) {
      this.$nextTick(() => {
        const firstNode = this.$refs.treeRef.$el.querySelector('.el-tree-node__content')
        if (firstNode) {
          firstNode.classList.remove('highlight-node') // 添加自定义的类来控制样式。
        }
      })
      this.pagination.current = 1
      this.pagination.size = 15
      this.checkItem = data
      this.getDataList()
    },
    // 点击搜索
    onSearch() {
      // 重置页码
      this.pagination.current = 1
      this.getDataList()
    },
    // 点击重置，重置表单，然后触发搜索
    onReset() {
      this.$refs.formRef.resetFields()
      this.onSearch()
    },
    // 导入文件确认
    submitfileImportDialog() {
      this.getDataList()
      this.fileImportShow = false
    },
    // 分页获取数据
    getDataList() {
      // 防止树节点切换太快造成请求异常。
      if (this.tableLoading) {
        this.$tools.cancelAjax()
      }
      this.tableData = []
      const params = {
        page: this.pagination.current,
        pageSize: this.pagination.size,
        deptId: this.checkItem.id ? this.checkItem.id : '',
        umId: this.checkItem.umId ? this.checkItem.umId : '',
        ...this.searchForm
      }
      this.tableLoading = true
      this.$api.supplierAssess
        .queryUserInfoListByPage(params)
        .then((res) => {
          if (res.code === '200') {
            this.pagination.total = res.data.total
            this.tableData = res.data.records
          } else {
            this.$message.error(res.message || '获取用户失败')
          }
        })
        .catch((msg) => {})
        .finally(() => (this.tableLoading = false))
    },
    // 表格相关操作总线处理
    onOperate(type, row) {
      switch (type) {
        case 'add':
          this.$router.push({
            name: 'memberAdd',
            query: {
              type: 'add'
            }
          })
          break
        case 'batch_delete':
          this.$confirm('是否删除数据?', '提示', {
            cancelButtonClass: 'el-button--primary is-plain',
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            let params = {
              id: this.selectionList.map((item) => item.id).join(','),
              delFlag: 1
            }
            this.$api.supplierAssess
              .bathDeleteUserData(params)
              .then((res) => {
                if (res.code === '200') {
                  this.$message.success('删除成功')
                  this.getDataList()
                } else {
                  throw res.message
                }
              })
              .catch((msg) => this.$message.error(msg || '删除失败'))
              .finally(() => (this.tableLoading = false))
          })
          break
        case 'delete':
          this.$confirm('是否删除该人员?', '提示', {
            cancelButtonClass: 'el-button--primary is-plain',
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            let params = {
              id: row.id,
              delFlag: 1
            }
            this.$api.OperationUser(params, { 'operation-type': 3, 'operation-id': params.id, 'operation-name': row.staffName }).then((res) => {
              if (res.code === '200') {
                this.$message.success(res.msg)
                this.getDataList()
              } else {
                this.$message.error(res.msg)
              }
            })
          })
          break
        case 'export':
          let param = {
            userIds: this.selectionList.map((item) => item.id).join(',')
          }
          let menuList = this.$store.state.menu.routes
          let firstTitle = ''
          let routeList = []
          this.$router.currentRoute.matched
            .filter((item) => item.name)
            .forEach((el) => {
              routeList.push(el.meta.title)
            })
          if (menuList.length) {
            firstTitle = menuList[this.$store.state.menu.headerActived].meta.title
            routeList.unshift(firstTitle)
          }
          this.$api.ExportUser(param, { 'operation-type': 4, 'operation-content': encodeURIComponent(routeList.join(',')) }).then((res) => {
            if (res.status == 200) {
              this.$tools.downloadFile(res)
            }
          })
          break
        case 'download':
          const userInfo = this.$store.state.user.userInfo.user
          axios({
            method: 'post',
            url: `${__PATH.VUE_SPACE_API}` + 'hospitalStaff/hospital-staff/exportStaffModel',
            data: '',
            responseType: 'arraybuffer',
            headers: {
              'Content-Type': 'application/json;charset=utf-8',
              Authorization: this.$store.state.user.token,
              hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
              unitCode: userInfo.unitCode ?? 'BJSYGJ'
            }
          })
            .then((res) => {
              this.$message.success(res.message || '导出成功')
              this.$tools.downloadFile(res, this)
            })
            .catch((res) => {
              this.$message.error(res.message || '导出失败')
            })
          break
        case 'import':
          this.fileImportShow = true
          break
        case 'edit':
          this.$router.push({
            name: 'memberDetail',
            query: {
              type: 'edit',
              staffId: row.staffId
            }
          })
          break
        case 'view':
          this.$router.push({
            name: 'memberDetail',
            query: {
              type: 'view',
              staffId: row.staffId
            }
          })
          break
        case 'certificate':
          this.$router.push({
            name: 'memberDetail',
            query: {
              type: 'view',
              staffId: row.staffId,
              currentType: '2'
            }
          })
          break
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.positionManage-list {
  ::v-deep(.container-content) {
    display: flex;
    flex-flow: row nowrap;
    background-color: #fff;
  }
  &__left {
    width: 276px;
    height: 100%;
    padding: 16px;
    border-right: solid 1px #eee;
    text-align: center;
    &__tree {
      height: 100%;
      overflow: hidden;
    }
    ::v-deep(.el-tree) {
      position: relative;
      margin-top: 16px;
      height: calc(100% - 80px);
      overflow: auto;
      .el-tree-node__content {
        line-height: 32px;
        height: 32px;
      }
      .el-tree-node.is-current > .el-tree-node__content {
        color: #3562db;
        background: #e6effc;
      }
      .space-tree {
        height: 100%;
        &__search {
          padding-right: 16px;
        }
      }
      .custom-tree-node {
        width: 100%;
        .custom-tree-node-wrapper {
          display: flex;
          justify-content: space-between;
        }
        .custom-tree-node-label {
          display: inline-block;
          width: 200px;
          white-space: nowrap; /* 防止换行 */
          overflow: hidden; /* 隐藏超出部分 */
          text-overflow: ellipsis;
          text-align: left;
        }
      }
      .rotate {
        cursor: pointer;
        margin-left: 5px;
        transform: rotate(90deg);
      }
    }
  }
  &__right {
    flex: 1;
    overflow: hidden;
    padding: 16px;
    background: #fff;
    display: flex;
    flex-direction: column;
  }
  &__form {
    ::v-deep(.el-form-item) {
      margin-bottom: 0;
      .el-form-item__content {
        line-height: 1;
      }
    }
  }
  .text-red {
    color: #ff1919;
  }
  &__table_actions {
    margin: 16px 0;
  }
  &__table {
    flex: 1;
    overflow: hidden;
  }
  .el-pagination {
    margin-top: 10px;
  }
}
::v-deep .highlight-node {
  color: #3562db;
  background: #e6effc;
}
</style>
