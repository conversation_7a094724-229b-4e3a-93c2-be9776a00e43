<template>
  <PageContainer :footer="true">
    <div slot="content" class="form-content">
      <div class="form-title">报警规则配置</div>
      <el-form ref="formInline" :model="formInline" :rules="rules">
        <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
          <span class="green_line"></span>
          报警信息
        </div>
        <div class="footer-role">
          <el-row :gutter="24" style="margin: 0">
            <el-col :md="8">
              <el-form-item label="报警类型:" prop="iatId" label-width="100px">
                <el-cascader
                  ref="iatCascader"
                  v-model="formInline.iatId"
                  :options="alarmTypeData"
                  :props="alarmPropsType"
                  :disabled="activeType === 'detail'"
                  clearable
                  :show-all-levels="false"
                  placeholder="请选择报警类型"
                  @change="alarmTypeChange"
                ></el-cascader>
              </el-form-item>
            </el-col>
            <el-col :md="8">
              <el-form-item label="报警描述：" label-width="150px" prop="description">
                <el-input v-model="formInline.description" maxlength="30" placeholder="请输入报警描述" :disabled="activeType === 'detail'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="8">
              <el-form-item label="报警级别：" label-width="150px" prop="levelId">
                <el-select v-model="formInline.levelId" placeholder="请选择" :disabled="activeType === 'detail'" @change="alarmLevelChange(formInline.levelId)">
                  <el-option v-for="item in alarmLevelList" :key="item.value" :label="item.name" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24" style="margin: 0">
            <el-col :md="8">
              <el-form-item label="报警系统:" prop="sysCode" label-width="100px">
                <el-select v-model="formInline.sysCode" filterable placeholder="请选择" :disabled="activeType === 'detail'" @change="policeRystem(formInline.sysCode)">
                  <el-option v-for="item in policeSystem" :key="item.imhMonitorCode" :label="item.imhMonitorName" :value="item.imhMonitorCode"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :md="8">
              <el-form-item label="报警实体类型：" prop="entityTypeId" label-width="150px">
                <el-select
                  v-model="formInline.entityTypeId"
                  filterable
                  placeholder="请选择"
                  :disabled="activeType === 'detail'"
                  @change="surverTypeChange(formInline.entityTypeId)"
                >
                  <el-option v-for="item in entityTypeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :md="8">
              <el-form-item label="报警实体对象：" prop="surveyCode" label-width="150px">
                <el-button class="form-btn-btn" type="primary" :disabled="activeType === 'detail'" @click="selectEntityObject">配置</el-button>
                <span class="objectTips">
                  <span class="selected">已选:</span><span class="number">{{ formInline.surveyCode ? formInline.surveyCode.length : 0 }}</span
                  ><span class="object">个对象</span></span
                >
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24" style="margin: 0">
            <el-col :md="8">
              <el-form-item label="备注：" label-width="100px">
                <el-input v-model="formInline.remark" type="textarea" show-word-limit maxlength="250" :disabled="activeType === 'detail'"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
          <span class="green_line"></span>
          报警规则
        </div>
        <div v-for="(domains, index) in formInline.domains" ref="formInline2" :key="index" :model="formInline" class="ml-16">
          <div v-if="index > 0" class="logicalClass">
            <el-select v-model="formInline.logical" placeholder="请选择逻辑关系" :disabled="activeType === 'detail'">
              <el-option v-for="item in LogicalRelationList" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </div>
          <div class="parameter-box">
            <div class="leftBox">
              <!-- 状态：区间非区间 其他参数  区间为0非区间为1 其他参数为3 -->
              <div v-for="(item, indexI) in formInline.domains[index]" :key="indexI" class="leftBox-parameter">
                <div class="leftBox-parameter-box">
                  <div class="itemLogicalClass">
                    <el-form-item>
                      <el-select
                        v-if="indexI > 0"
                        v-model="item.logical"
                        placeholder="请选择逻辑关系"
                        :disabled="activeType === 'detail'"
                        @change="logicalChange(index, item.logical)"
                      >
                        <el-option v-for="item in LogicalRelationList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                      </el-select>
                      <span v-else></span>
                    </el-form-item>
                  </div>
                  <div>
                    <el-form-item label-width="10" :prop="`domains[${index}][${indexI}].paramId`" :rules="[{ required: true, message: '请选择参数', trigger: 'change' }]">
                      <el-select
                        v-model="item.paramId"
                        filterable
                        collapse-tags
                        placeholder="请选择参数"
                        clearable=""
                        :disabled="activeType === 'detail'"
                        @change="paramChange(index, indexI)"
                      >
                        <el-option v-for="(item, jIndex) in parameterChooseList" :key="jIndex" :label="item.parameterName" :value="item.parameterId"></el-option>
                      </el-select>
                    </el-form-item>
                  </div>
                  <!-- 区间非区间 -->
                  <div v-if="item.showHidden">
                    <el-form-item :prop="`domains[${index}][${indexI}].intervals`" :rules="[{ required: true, message: '请选择区间', trigger: 'change' }]">
                      <el-select v-model="item.intervals" :disabled="activeType === 'detail'" @change="intervalChange(item.intervals, index, indexI)">
                        <el-option v-for="item in interval" :key="item.id" :label="item.name" :value="item.id"></el-option>
                      </el-select>
                    </el-form-item>
                  </div>
                  <div v-if="item.showHiddenStatus">
                    <!-- 状态 -->
                    <el-form-item label="" label-width="10" :prop="`domains[${index}][${indexI}].stateValue`" :rules="[{ required: true, message: '请选择', trigger: 'change' }]">
                      <el-select v-model="item.stateValue" :disabled="activeType === 'detail'" @change="stateValueChange(item.stateValue, item.statusList, index, indexI)">
                        <el-option v-for="item in item.statusList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue"></el-option>
                      </el-select>
                    </el-form-item>
                  </div>
                  <div v-if="item.showHiddenRange" class="compareClass">
                    <!-- 区间input -->
                    <el-form-item label="" label-width="5" :prop="`domains[${index}][${indexI}].min`" :rules="[{ required: true, message: '请输入', trigger: 'blur' }]">
                      <el-input placeholder="大于" disabled class="borderClass"> </el-input>
                      <el-input-number v-model="item.min" type="number" :precision="2" :controls="false" :disabled="activeType === 'detail'" @input="forceUpdate">
                      </el-input-number>
                      {{ item.unit }}
                    </el-form-item>
                    <span class="symbol">-</span>
                    <el-form-item label="" label-width="5" :prop="`domains[${index}][${indexI}].max`" :rules="[{ required: true, message: '请输入', trigger: 'blur' }]">
                      <el-input placeholder="小于" disabled class="borderClass"> </el-input>
                      <el-input-number v-model="item.max" :precision="2" :controls="false" :disabled="activeType === 'detail'" @input="forceUpdate"> </el-input-number>
                      {{ item.unit }}
                    </el-form-item>
                  </div>
                  <div v-if="item.showHiddenNotRange" class="compareClass">
                    <!-- 非区间input -->
                    <el-form-item label="" label-width="5" :prop="`domains[${index}][${indexI}].min`" :rules="[{ required: true, message: '请输入', trigger: 'blur' }]">
                      <el-input placeholder="小于" disabled class="borderClass"> </el-input>
                      <el-input-number v-model="item.min" :precision="2" :controls="false" :disabled="activeType === 'detail'" @input="forceUpdate"> </el-input-number>
                      {{ item.unit }}
                    </el-form-item>
                    <span class="symbol">或</span>
                    <el-form-item label="" label-width="5" :prop="`domains[${index}][${indexI}].max`" :rules="[{ required: true, message: '请输入', trigger: 'blur' }]">
                      <el-input placeholder="大于" disabled class="borderClass"></el-input>
                      <el-input-number v-model="item.max" :precision="2" :controls="false" :disabled="activeType === 'detail'" @input="forceUpdate"> </el-input-number>
                      {{ item.unit }}
                    </el-form-item>
                  </div>
                  <div class="continuousClass">
                    <el-form-item label="持续时长" :prop="`domains[${index}][${indexI}].continuous`" :rules="[{ required: true, message: '请输入持续时长', trigger: 'blur' }]">
                      <el-input-number v-model="item.continuous" :precision="2" :controls="false" :disabled="activeType === 'detail'" :min="0"></el-input-number>
                      s
                    </el-form-item>
                  </div>
                </div>
                <div v-if="activeType !== 'detail'" class="leffBox-btn operation-btn">
                  <span class="addBtn" @click="addGroupRow(index, indexI)">添加</span>
                  <span v-if="indexI > 0" class="deleteBtn" @click="removeGroupDomain(index, indexI)">删除</span>
                </div>
              </div>
            </div>
            <div v-if="activeType !== 'detail'" class="operation-btn">
              <span class="addBtn" @click="addrow(index)">添加</span>
              <span v-if="index > 0" class="deleteBtn" @click="removeDomain(index)">删除</span>
            </div>
          </div>
        </div>
      </el-form>
      <!--实体对象 -->
      <template v-if="entityObjectDialogShow">
        <SelectEntityObjectDialog
          :entityObjectDialogShow="entityObjectDialogShow"
          :projectCode="projectCode"
          :surveyCode="formInline.surveyCode"
          @submitEntityObjectDialog="submitEntityObjectDialog"
          @closeEntityObjecDialog="closeEntityObjecDialog"
        />
      </template>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">关闭</el-button>
      <el-button v-if="activeType !== 'detail'" :loading="subLoading" type="primary" @click="submitForm()">保存</el-button>
    </div>
  </PageContainer>
</template>
<script>
import SelectEntityObjectDialog from './components/selectEntityObject.vue'
export default {
  name: 'alarmConfigForm',
  components: {
    SelectEntityObjectDialog
  },
  async beforeRouteLeave(to, from, next) {
    if (!this.routerNameList.includes(to.name)) {
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    const routerNameList = ['elevatorAlarmConfiguration', 'alarmConfiguration']
    return {
      routerNameList: Object.freeze(routerNameList),
      requestHttp: __PATH.VUE_IEMC_API,
      entityObjectDialogShow: false, // 实体对象
      parameterId: '', // 参数ID
      policeDescribe: '', // 报警描述
      GetList: [],
      policeType: '',
      policeName: '',
      policeSystem: [], // 报警系统
      entityObject: {},
      entityTypeList: '', // 实体类型数据
      alarmLevelList: [], // 报警级别数据
      parameterList: [], // 参数数据
      alarmlist: '',
      parameterChooseList: [],
      interval: [
        {
          id: 1,
          name: '区间'
        },
        {
          id: 2,
          name: '非区间'
        }
      ],
      LogicalRelationList: [
        {
          id: 1,
          name: '且'
        },
        {
          id: 2,
          name: '或'
        }
      ],
      parameterIds: [],
      paramId: [],
      formInline: {
        id: '',
        iatName: '', // 报警类型名称
        iatId: '', // 报警类型ID
        iatParentId: '', // 父级id
        iatParentName: '', // 父级name
        description: '', // 报警描述
        sysCode: '', // 报警系统ID
        sysName: '', // 报警系统名称
        surveyName: [], // 实体名称
        surveyCode: [], // 实体对象code
        entityTypeId: '', // 实体类型ID
        entityTypeName: '', // 实体类型名称
        remark: '', // 备注
        levelId: '', // 报警等级id
        levelName: '', // 报警等级名称
        logical: 1,
        domains: [
          [
            {
              paramName: '', // 参数名称
              paramId: '', // 参数ID
              intervals: '', // 区间0，非区间1，状态3
              min: '', // 最小值
              max: '', // 最大值
              unit: '', // 单位
              stateName: '', // 状态名称
              stateValue: '', // 状态值
              continuous: '0', // 持续时间
              statusList: [], // 状态
              logical: 1, // 逻辑关系(1:且 2:或)
              showHidden: false, // 显示隐藏  区间
              showHiddenStatus: false, // 显示隐藏 状态下拉框
              showHiddenNotRange: false, // 显示隐藏  非区间input框
              showHiddenRange: false // 显示隐藏   区间input框
            }
          ]
        ]
      },
      alarmPropsType: {
        children: 'children',
        label: 'name',
        value: 'id',
        checkStrictly: true,
        emitPath: false
      },
      alarmTypeData: [], // 报警类型
      alarmAllTypeData: [], // 报警类型用来匹配备注
      rules: {
        iatId: [
          {
            required: true,
            message: '请选择报警类型',
            trigger: 'blur'
          }
        ],
        description: [
          {
            required: true,
            message: '请输入报警描述',
            trigger: 'blur'
          }
        ],
        sysCode: [
          {
            required: true,
            message: '请选择报警系统',
            trigger: 'blur'
          }
        ],
        entityTypeId: [
          {
            required: true,
            message: '请选择报警实体类型',
            trigger: 'blur'
          }
        ],
        surveyCode: [
          {
            required: true,
            message: '请选择报警实体对象',
            trigger: 'blur'
          }
        ],
        levelId: [
          {
            required: true,
            message: '请选择报警级别',
            trigger: 'blur'
          }
        ]
      },
      projectCode: '',
      activeType: '',
      subLoading: false
    }
  },
  created() {
    if (!this.$store.state.keepAlive.list.some((e) => this.routerNameList.includes(e))) {
      this.initEvent()
    }
  },
  activated() {
    this.initEvent()
  },
  mounted() {
    this.initEvent()
  },
  methods: {
    initEvent() {
      // 重置form表单
      const data = this.$options.data()
      delete data.rules
      Object.assign(this.$data, data)
      this.$nextTick(() => {
        this.$refs.formInline.resetFields()
      })
      this.activeType = this.$route.query.activeType
      this.requestHttp = this.$route.query.requestHttp
      this.getAlarmLevelList()
      this.getEntityList()
      this.getPoliceType()
      this.alarmsystem()
      if (this.$route.query.id) {
        setTimeout(() => {
          this.getAlarmConfigById()
        }, 300)
      }
    },
    // param改变
    paramChange(index, indexI) {
      let obj = this.parameterChooseList.find((item) => item.parameterId == this.formInline.domains[index][indexI].paramId)
      if (obj.state === 1) {
        this.formInline.domains[index][indexI].showHidden = true
        this.formInline.domains[index][indexI].showHiddenStatus = false
        this.formInline.domains[index][indexI].unit = obj.unit
      } else if (obj.state === 2) {
        this.formInline.domains[index][indexI].showHiddenStatus = true
        this.formInline.domains[index][indexI].showHidden = false
        this.formInline.domains[index][indexI].statusList = obj.child
      }
      this.formInline.domains[index][indexI].showHiddenNotRange = false
      this.formInline.domains[index][indexI].showHiddenRange = false
      this.formInline.domains[index][indexI].paramName = obj.parameterName
      this.formInline.domains[index][indexI].stateValue = ''
      this.formInline.domains[index][indexI].intervals = ''
    },
    // 逻辑关系改变
    logicalChange(index, val) {
      this.formInline.domains[index].forEach((item) => {
        item.logical = val
      })
      this.$forceUpdate()
    },
    // 小组添加数据
    addGroupRow(index, indexI) {
      this.formInline.domains[index].push({
        paramName: '', // 参数名称
        paramId: '', // 参数ID
        intervals: '', // 区间0，非区间1，状态3
        min: '', // 最小值
        max: '', // 最大值
        unit: '', // 单位
        stateName: '', // 状态名称
        stateValue: '', // 状态值
        continuous: '0', // 持续时间
        logical: 1, // 逻辑关系(1:且 2:或)
        statusList: [], // 状态
        showHidden: false, // 显示隐藏  区间
        showHiddenStatus: false, // 显示隐藏 状态下拉框
        showHiddenNotRange: false, // 显示隐藏  非区间input框
        showHiddenRange: false // 显示隐藏   区间input框
      })
      this.$forceUpdate()
    },
    // 小组删除数据
    removeGroupDomain(index, indexI) {
      this.formInline.domains[index].splice(indexI, 1)
      this.$forceUpdate()
    },
    // 添加参数
    addrow(index) {
      let arr = [
        {
          paramName: '', // 参数名称
          paramId: '', // 参数ID
          intervals: '', // 区间0，非区间1，状态3
          min: '', // 最小值
          max: '', // 最大值
          unit: '', // 单位
          stateName: '', // 状态名称
          stateValue: '', // 状态值
          continuous: '0', // 持续时间
          logical: 1, // 逻辑关系(1:且 2:或)
          statusList: [], // 状态
          showHidden: false, // 显示隐藏  区间
          showHiddenStatus: false, // 显示隐藏 状态下拉框
          showHiddenNotRange: false, // 显示隐藏  非区间input框
          showHiddenRange: false // 显示隐藏   区间input框
        }
      ]
      this.formInline.domains.splice(index + 1, 0, arr)
      this.$forceUpdate()
    },
    // 删除报警规则
    removeDomain(index) {
      this.formInline.domains.splice(index, 1)
      this.$forceUpdate()
    },
    getAlarmConfigById() {
      let queryId = this.$route.query.id
      return this.$api
        .getAlarmDeployById({
          id: queryId
        })
        .then((res) => {
          this.formInline = res.data
          this.formInline.surveyCode = res.data.surveyCode.split(',')
          this.formInline.surveyName = res.data.surveyName.split(',')
          this.formInline.levelId = res.data.levelId.toString()
          this.formInline.domains = res.data.ruleJsonList
          let surveyCodes = this.formInline.surveyCode
          this.$api.getParamEntityObjectList({ surveyCodes: surveyCodes, projectCode: this.formInline.sysCode }).then((res) => {
            if (res.code == '200') {
              this.parameterChooseList = res.data
              this.formInline.domains.forEach((item) => {
                item.forEach((el) => {
                  if (el.intervals === 1) {
                    el.statusList = []
                    el.showHidden = true
                    el.showHiddenRange = true
                    el.showHiddenNotRange = false
                    el.showHiddenStatus = false
                  } else if (el.intervals === 2) {
                    el.statusList = []
                    el.showHidden = true
                    el.showHiddenRange = false
                    el.showHiddenNotRange = true
                    el.showHiddenStatus = false
                  } else if (el.intervals === 3) {
                    let arr = res.data
                    el.statusList = arr.find((item) => item.parameterId === el.paramId).child
                    el.showHidden = false
                    el.showHiddenRange = false
                    el.showHiddenNotRange = false
                    el.showHiddenStatus = true
                  }
                })
              })
            }
          })
          this.projectCode = this.formInline.sysCode
        })
    },
    // 点击确定
    submitForm() {
      let jsonForm = {
        ...this.formInline
      }
      let arr = this.formInline.domains
      arr.forEach((item) => {
        item.forEach((el) => {
          delete el.showHiddenNotRange
          delete el.showHidden
          delete el.showHiddenRange
          delete el.showHiddenStatus
          delete el.statusList
          el.max = Number(el.max)
          el.min = Number(el.min)
        })
      })
      jsonForm.ruleJsonList = arr
      jsonForm.surveyCode = this.formInline.surveyCode.join(',')
      jsonForm.surveyName = this.formInline.surveyName.join(',')
      delete jsonForm.domains
      delete jsonForm.domains
      if (this.formInline.domains.length == 0) {
        return this.$message.error('请添加报警规则')
      }
      if (!this.formInline.surveyCode) {
        return this.$message.error('请添加报警实体对象')
      }
      this.$refs.formInline.validate((valid) => {
        if (valid) {
          this.subLoading = true
          if (this.$route.query.id) {
            jsonForm.id = this.$route.query.id
            this.$api.updateAlarmDeployData(jsonForm, { 'operation-type': 2, 'operation-id': jsonForm.id, 'operation-name': '' }, this.requestHttp).then((res) => {
              if (res.code == 200) {
                this.$message.success(res.message)
                this.$router.go(-1)
                this.subLoading = false
              } else {
                this.$message.error(res.message)
              }
            })
          } else {
            this.$api.insertAlarmDeployData(jsonForm, { 'operation-type': 1 }).then((res) => {
              if (res.code == 200) {
                this.$message.success(res.message)
                this.$router.go(-1)
                this.subLoading = false
              } else {
                this.$message.error(res.message)
              }
            })
          }
        }
      })
    },
    // 获取报警实体类型
    getEntityList() {
      let data = {
        dictType: 12
      }
      this.$api.getDictionaryList(data).then((res) => {
        // h获取实体类型数据
        if (res.code == 200) {
          this.entityTypeList = res.data
        }
      })
    },
    surverTypeChange(val) {
      this.entityTypeList.forEach((item) => {
        if (val == item.id) {
          this.formInline.entityTypeName = item.name
        }
      })
    },
    getPoliceType() {
      this.$api.getAlarmDeployAllTypeList().then((res) => {
        if (res.code == 200) {
          this.alarmAllTypeData = res.data
          let list = this.$tools.transData(res.data, 'id', 'parentId', 'children')
          this.alarmTypeData = list
        }
      })
    },
    // 报警系统
    alarmsystem() {
      let data = {
        projectCodes: ''
      }
      this.$api.getAlarmSystem(data, {}, this.requestHttp).then((res) => {
        if (res.code == 200) {
          this.policeSystem = res.data
        }
      })
    },
    // 报警实体对象
    alarmEntityObject() {
      let data = {
        projectCode: this.formInline.sysCode
      }
      return this.$api.getAlarmEntityList(data, {}, this.requestHttp).then((res) => {
        if (res.code == 200) {
          this.entityObject = res.data
        }
      })
    },
    // input强制刷新
    forceUpdate(event) {
      this.$forceUpdate()
    },
    // 选择实体对象
    selectEntityObject() {
      if (this.formInline.sysCode) {
        this.entityObjectDialogShow = true
      } else {
        this.$message.error('请先选择报警系统')
      }
    },
    // 实体对象弹窗关闭
    closeEntityObjecDialog() {
      this.entityObjectDialogShow = false
    },
    // 实体对象确定
    submitEntityObjectDialog(list) {
      this.formInline.surveyCode = []
      this.formInline.surveyName = []
      let surveyCodes = []
      list.forEach((item) => {
        this.formInline.surveyCode.push(item.surveyCode)
        this.formInline.surveyName.push(item.surveyName)
        surveyCodes.push(item.surveyCode)
      })
      this.$api.getParamEntityObjectList({ surveyCodes: surveyCodes, projectCode: this.formInline.sysCode }).then((res) => {
        if (res.code == '200') {
          this.parameterChooseList = res.data
        }
        this.entityObjectDialogShow = false
      })
    },
    // 报警级别
    getAlarmLevelList() {
      this.$api.getEntityTypeList({ dictType: 0 }, {}, this.requestHttp).then((res) => {
        if (res.code == 200) {
          res.data.map((e) => {
            e.id = e.id.toString()
          })
          this.alarmLevelList = res.data
        }
      })
    },
    // 报警级别监听
    alarmLevelChange(val) {
      this.alarmLevelList.forEach((item) => {
        if (val == item.value) {
          this.formInline.levelName = item.name
        }
      })
    },
    // 报警类型监听
    alarmTypeChange(value) {
      this.alarmAllTypeData.forEach((item) => {
        if (item.id === value) {
          this.formInline.description = item.description
          this.formInline.iatParentId = item.parentId
          this.formInline.iatParentName = item.parentName
        }
      })
      let node = this.$refs.iatCascader.getCheckedNodes()
      this.formInline.iatName = node[0].label
    },
    // 报警系统
    policeRystem(val) {
      this.projectCode = val
      this.formInline.sysName = this.policeSystem.find((item) => item.imhMonitorCode == val).imhMonitorName
      this.formInline.surveyCode = []
      this.formInline.surveyName = []
      this.formInline.domains = [
        [
          {
            paramName: '', // 参数名称
            paramId: '', // 参数ID
            intervals: '', // 区间0，非区间1，状态3
            min: '', // 最小值
            max: '', // 最大值
            unit: '', // 单位
            stateName: '', // 状态名称
            stateValue: '', // 状态值
            continuous: '0', // 持续时间
            logical: 1, // 逻辑关系(1:且 2:或)
            statusList: [], // 状态
            showHidden: false, // 显示隐藏  区间
            showHiddenStatus: false, // 显示隐藏 状态下拉框
            showHiddenNotRange: false, // 显示隐藏  非区间input框
            showHiddenRange: false // 显示隐藏   区间input框
          }
        ]
      ]
      this.parameterChooseList = []
      this.alarmEntityObject()
    },
    // 获取状态名称
    stateValueChange(val, arr, i, idx) {
      this.formInline.domains[i][idx].stateName = arr.find((item) => item.dictValue == val).dictName
      this.formInline.domains[i][idx].intervals = 3
    },
    intervalChange(val, i, idx) {
      if (val === 1) {
        this.formInline.domains[i][idx].showHiddenRange = true
        this.formInline.domains[i][idx].showHiddenNotRange = false
      } else if (val === 2) {
        this.formInline.domains[i][idx].showHiddenRange = false
        this.formInline.domains[i][idx].showHiddenNotRange = true
      }
      this.$forceUpdate()
    }
  }
}
</script>
<style lang="scss" scoped>
.form-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 10px);
  padding: 10px 20px;
  overflow-y: auto;
  .toptip {
    margin-bottom: 10px;
  }
  .symbol {
    margin: 0 12px;
    line-height: 36px;
  }
  .form-title {
    height: 40px;
    line-height: 40px;
    background: #fff;
    border-bottom: 1px solid #dcdfe6;
    font-size: 15px;
    font-family: 'PingFang SC-Medium', 'PingFang SC';
    font-weight: 500;
    color: #121f3e;
    padding-left: 15px;
  }
  .box-card {
    padding-left: 3%;
    margin-bottom: 3px;
  }
  .form-btn-title {
    font-size: 14px;
    font-family: 'PingFang SC-Regular', 'PingFang SC';
    font-weight: 400;
    color: #121f3e;
    margin-right: 8px;
  }
  .objectTips {
    color: #666666;
    .selected {
      margin-left: 5px;
    }
    .number {
      margin: 0 5px;
    }
  }
  .itemLogicalClass {
    width: 100px;
    .el-select {
      width: 100px !important;
      width: fit-content;
    }
  }
  .continuousClass {
    display: flex;
    line-height: 32px;
    margin-left: 16px;
    .el-form-item {
      display: flex !important;
    }
    .el-input-number {
      width: 100px !important;
      width: fit-content;
    }
  }
  .logicalClass {
    margin-bottom: 20px;
    .el-select {
      width: 150px !important;
      width: fit-content;
    }
  }
  .parameter-box {
    width: 100%;
    display: flex;
    margin-bottom: 20px;
    .leftBox {
      border-radius: 4px;
      border: 1px solid #dcdfe6;
      width: 88%;
      padding: 20px 10px 0 10px;
      .leftBox-parameter {
        width: 100%;
        display: flex;
        .leftBox-parameter-box {
          div {
            margin-right: 5px;
          }
          width: 92%;
          display: flex;
          align-items: center;
        }
        .leffBox-btn {
          flex: 1;
        }
      }
    }
    .operation-btn {
      margin-left: 20px;
      cursor: pointer;
      .addBtn {
        color: #3562db;
      }
      .deleteBtn {
        margin-left: 20px;
        color: #f53f3f;
      }
    }
  }
}
.ml-16 {
  margin-left: 16px;
}
::v-deep .footer-role .el-input,
.el-select,
.el-cascader {
  width: fit-content;
  width: 280px !important;
}
::v-deep .leftBox .el-select,
.el-input {
  width: 150px !important;
}
::v-deep .el-form-item__content {
  line-height: 32px !important;
}
::v-deep .compareClass {
  display: flex;
  .el-input__inner {
    border-radius: 0 !important;
  }
  .borderClass {
    .el-input__inner {
      border-right: none !important;
    }
  }
  .el-input {
    width: 60px !important;
    margin-right: 0 !important;
  }
  .el-input-number {
    .el-input--large {
      width: 100px !important;
    }
    width: 100px !important;
  }
}
</style>
