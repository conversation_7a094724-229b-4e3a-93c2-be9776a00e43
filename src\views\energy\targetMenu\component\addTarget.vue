<template>
  <div class="content-box">
    <el-dialog :title="`${dialogTitle}指标`" :visible.sync="dialogVisible" width="40%" :before-close="handleClose">
      <div v-loading="loading">
        <!-- form 指标日期，万元收入能耗支出 -->
        <el-form ref="formAddInfo" :model="formAddInfo" label-position="top" :label-margin-bottom="10" :rules="rules">
          <el-row>
            <el-col :span="11">
              <el-form-item label="指标日期" prop="inspectDate">
                <el-date-picker v-model="formAddInfo.inspectDate" type="month" placeholder="选择日期" value-format="timestamp"></el-date-picker>
              </el-form-item>
            </el-col>
            <!-- 站元素但是不显示 -->
            <el-col :span="11">
              <el-form-item label="站元素" style="visibility: hidden">
                <el-input placeholder="站元素"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="11">
              <el-form-item label="万元收入能耗支出（元）" prop="inspectEnergy">
                <el-input
                  v-model="formAddInfo.inspectEnergy"
                  placeholder="万元收入能耗支出（元）"
                  oninput="value=value.replace(/[^\d.]/g, '').replace(/^\./g, '').replace(/\.\d{3,}/g, function(m){ return m.slice(0, -1);}).replace('.', '$#$').replace(/\./g, '').replace('$#$', '.').replace(/^0*(\d+)/g, '$1')"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="单位面积用电量（kwh/㎡）" prop="inspectElectricity">
                <el-input
                  v-model="formAddInfo.inspectElectricity"
                  placeholder="单位面积用电量（kwh/㎡）"
                  oninput="value=value.replace(/[^\d.]/g, '').replace(/^\./g, '').replace(/\.\d{3,}/g, function(m){ return m.slice(0, -1);}).replace('.', '$#$').replace(/\./g, '').replace('$#$', '.').replace(/^0*(\d+)/g, '$1')"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="11">
              <el-form-item label="单位面积生活用水量（t）" prop="inspectWater">
                <el-input
                  v-model="formAddInfo.inspectWater"
                  placeholder="单位面积生活用水量（t）"
                  oninput="value=value.replace(/[^\d.]/g, '').replace(/^\./g, '').replace(/\.\d{3,}/g, function(m){ return m.slice(0, -1);}).replace('.', '$#$').replace(/\./g, '').replace('$#$', '.').replace(/^0*(\d+)/g, '$1')"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="单位床位能耗支出（元/床）" prop="inspectBed">
                <el-input
                  v-model="formAddInfo.inspectBed"
                  placeholder="单位床位能耗支出（元/床）"
                  oninput="value=value.replace(/[^\d.]/g, '').replace(/^\./g, '').replace(/\.\d{3,}/g, function(m){ return m.slice(0, -1);}).replace('.', '$#$').replace(/\./g, '').replace('$#$', '.').replace(/^0*(\d+)/g, '$1')"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" plain @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="onSubmit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'addTarget',
  components: {},
  props: {
    dialogTitle: {
      type: String,
      default: '',
      required: true
    }
  },
  data() {
    return {
      rules: {
        inspectDate: [{ required: true, message: '请输入指标日期', trigger: 'change' }],
        inspectEnergy: [{ required: true, message: '请输入万元收入能耗支出', trigger: 'blur' }],
        inspectElectricity: [{ required: true, message: '请输入单位面积用电量', trigger: 'blur' }],
        inspectWater: [{ required: true, message: '请输入单位面积生活用水量', trigger: 'blur' }],
        inspectBed: [{ required: true, message: '请输入单位床位能耗支出', trigger: 'blur' }]
      },
      dialogVisible: false,
      loading: false,
      flag: '0', // 默认0表示新增  1 表示编辑新增
      formAddInfo: {
        inspectDate: '',
        inspectEnergy: '',
        inspectElectricity: '',
        inspectWater: '',
        inspectBed: ''
      }
    }
  },
  created() {},
  mounted() {},
  methods: {
    // 通过id查询
    edit(id) {
      this.loading = true
      this.dialogVisible = true
      this.flag = '1' // 更改标识 表示是编辑进来新增
      this.$api.editTargetEnergy({ id }).then((res) => {
        const {
          code,
          data: { inspectDate, inspectEnergy, inspectElectricity, inspectWater, inspectBed }
        } = res
        if (code === '200') {
          this.formAddInfo = {
            inspectDate,
            inspectEnergy,
            inspectElectricity,
            inspectWater,
            inspectBed,
            id
          }
        }
        this.loading = false
      })
    },
    // 重置表单
    resetForm() {
      this.flag = '0'
      this.formAddInfo = {
        inspectDate: '',
        inspectEnergy: '',
        inspectElectricity: '',
        inspectWater: '',
        inspectBed: ''
      }
      this.$nextTick(() => {
        this.dialogVisible = true
        this.$refs.formAddInfo.clearValidate()
      })
    },
    openDialog() {
      this.dialogVisible = true
    },
    handleClose() {
      this.dialogVisible = false
    },
    onSubmit() {
      this.$refs.formAddInfo.validate((valid) => {
        if (valid) {
          this.$emit('onAddTarget', this.formAddInfo, this.flag)
          this.dialogVisible = false
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.content-box {
  ::v-deep .el-dialog__body {
    padding: 0;
    .el-form {
      .el-row {
        display: flex;
        justify-content: space-between;
        .el-form-item {
          margin-bottom: 5px;
          .el-form-item__label {
            padding: 0;
          }
          .el-date-editor {
            width: 100%;
          }
        }
      }
    }
  }
}
</style>
