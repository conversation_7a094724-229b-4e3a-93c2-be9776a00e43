<template>
  <PageContainer>
    <div slot="content" class="monitor-content">
      <div class="monitor-content-left">
        <el-tree
          ref="tree"
          v-loading="treeLoading"
          class="tree_self"
          :data="data"
          :props="defaultProps"
          :filter-node-method="filterNode"
          :default-expanded-keys="expanded"
          node-key="id"
          highlight-current
          @node-click="handleNodeClick"
          @check="treeChecked"
        >
          <template #default="{ node, data }">
            <el-tooltip effect="dark" :disabled="showTooltip" :content="node.label" placement="top">
              <div class="nodeLabel" @mouseover="onMouseOver(data.id)">
                <span :ref="`nodeLabel${data.id}`">{{ node.label }}</span>
              </div>
            </el-tooltip>
          </template>
        </el-tree>
      </div>
      <div class="monitor-content-right">
        <div class="right-heade">
          <div class="group-btns">
            <div class="voice-box" @click="changeVoiceFlag">
              <div :class="openVoice ? 'voice-on-icon' : 'voice-off-icon'"></div>
            </div>
            <div class="alarm-box" @click="openSeach">
              <svg-icon name="general-alarm" class="" />
              <span style="margin-left: 5px">{{ dayCount }}/{{ unConfirmCount }}</span>
            </div>
          </div>
          <div v-if="alarmAlertShow" class="alarm-alert">
            <svg-icon name="urgent_icon" /> <span>{{ alarmAlert }}</span>
          </div>
          <div class="heade-pattern">
            <div class="pattern-item" @click="handleClick('first')">
              <svg-icon :name="activeTab == 'first' ? 'chartModeActive' : 'chartMode'" class="pattern-icon" />
              <span :style="{ color: activeTab == 'first' ? '#3562DB' : '#414653' }">图形模式</span>
            </div>
            <div class="pattern-item" @click="handleClick('second')">
              <svg-icon :name="activeTab == 'second' ? 'listModeActive' : 'listMode'" class="pattern-icon" />
              <span :style="{ color: activeTab == 'second' ? '#3562DB' : '#414653' }">列表模式</span>
            </div>
          </div>
        </div>
        <div class="right-content">
          <graphics-mode v-if="activeTab == 'first'" ref="scadaShow" :requestHttp="requestHttp" :entityMenuCode="tableCode" :projectId="projectCode" />
          <div v-if="activeTab == 'second'" v-loading="tableLoading" style="height: 100%">
            <div v-if="cardList.length" style="height: 100%; overflow: auto">
              <div class="card_info">
                <el-row :gutter="20">
                  <el-col v-for="(val, i) in cardList" :key="i" :span="8" class="card">
                    <el-card class="box-card">
                      <div slot="header" class="clearfix cardTitle">
                        <div class="echart-title" @click="jumpMonitorDetail(val)">{{ val.surveyEntityName }}</div>
                        <div class="titleRight">
                          <div class="totalBox" @click="showMoreRecord(val)">
                            {{ val.policeCount }}
                          </div>
                          <div v-if="cardList[i].parameterList" class="title-right" @click="elevatorDetails(val)">更多</div>
                        </div>
                      </div>
                      <div v-for="(o, p) of cardList[i].parameterList" :key="p" class="text item card_options" style="border: 1px solid #f8f9fc; cursor: pointer">
                        <div class="info-list">
                          <span class="info_list_icon iconfont" :class="o.parameterIcon ? o.parameterIcon : ''" :style="{ color: o.parameterColor || '#fd9a5e' }"></span>
                          <div style="margin: auto 10px; max-width: 50%; flex: 1">
                            <div class="parameterUnit" :title="o.parameterValue + o.parameterUnit">
                              {{ (o.parameterValue ? (o.parameterValue === 'null' ? '-' : o.parameterValue) : '') + (o.parameterUnit ? o.parameterUnit : '') || '-' }}
                            </div>
                            <div class="parameterName">
                              {{ o.parameterName || '-' }}
                            </div>
                          </div>
                          <div style="margin: auto 0" :class="o.parameterException == '0' ? '' : 'font-red'">
                            {{ o.parameterException == '2' ? '离线' : o.parameterException == '1' ? '异常' : '' }}
                          </div>
                        </div>
                      </div>
                      <div v-if="!cardList[i].parameterList || cardList[i].parameterList.length == 0" class="img_null">
                        <img style="width: 187px" src="@/assets/images/monitor/null.png" alt="" />
                        <div>暂无数据~</div>
                      </div>
                    </el-card>
                    <div class="card_bottom">
                      <div class="">{{ val.ibdCollertorTime }}</div>
                    </div>
                  </el-col>
                </el-row>
              </div>
              <div class="table-page">
                <el-pagination
                  style="margin-top: 10px"
                  :current-page="paginationData.currentPage"
                  :page-sizes="[6, 12, 18, 24]"
                  :page-size="paginationData.pageSize"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="paginationData.total"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                ></el-pagination>
              </div>
            </div>
            <div v-else class="echart-null">
              <img src="@/assets/images/monitor/null.png" alt="" />
              <div>暂无数据~</div>
            </div>
          </div>
          <alarmList
            :closeState="advancClose"
            :requestHttp="requestHttp"
            :projectCode="projectCode"
            :surveyEntityCode="surveyEntityCode"
            :treeCode="checkedData.code"
            @isCloseState="closeDrawer"
          ></alarmList>
        </div>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import { mapGetters } from 'vuex'
import alarmList from '@/views/monitor/commonPage/elecRealWarning/components/alarmList'
import graphicsMode from '@/views/monitor/airMenu/components/graphicsMode'
import { monitorTypeList } from '@/util/dict.js'
export default {
  name: 'elevatorMonitor',
  components: {
    graphicsMode,
    alarmList
  },
  // 监听路由携带报警参数跳转
  beforeRouteEnter(to, from, next) {
    let names = []
    to.matched.map((v, i) => {
      if (i > 0) {
        v.components.default.name && names.push(v.components.default.name)
      }
    })
    next((vm) => {
      if (to.query.isAlarm == 1) {
        vm.alarmAlertShow = true
        vm.alarmAlert = to.query.alarmMsg
      }
      vm.$store.commit('keepAlive/add', names)
    })
  },
  async beforeRouteLeave(to, from, next) {
    // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
    if (!['monitorDetails', 'elevatorDetail'].includes(to.name)) {
      // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      requestHttp: __PATH.VUE_IEMC_ELEVATOR_API,
      projectCode: monitorTypeList.find((item) => item.projectName == '电梯监测').projectCode,
      alarmAlert: '',
      alarmAlertShow: false,
      treeLoading: true,
      defaultProps: {
        children: 'children',
        label: 'name',
        value: 'code'
      },
      openVoice: true, // 是否开启电梯全局报警声音
      activeTab: '',
      advancClose: false,
      cardList: [],
      tableCode: '',
      paginationData: {
        currentPage: 1,
        pageSize: 6,
        total: 0
      },
      tableData: [],
      tableLoading: false,
      data: [],
      checkedData: '',
      expanded: [],
      unConfirmCount: 0,
      dayCount: 0,
      monitorData: '',
      tableSockectLoad: null,
      countSocketLoad: null,
      showTooltip: true,
      surveyEntityCode: ''
    }
  },
  computed: {
    ...mapGetters({
      socketelevatorMsgs: 'socket/socketelevatorMsgs'
    })
  },
  watch: {
    socketelevatorMsgs(data) {
      let projectData = JSON.parse(data)
      // 匹配设备相同的接收消息刷新页面
      if (projectData.projectCode == this.projectCode) {
        // 收到消息刷新页面
        // 报警消息不包含实体信息
        if (projectData.isAlarm == 1) {
          this.alarmAlertShow = true
          this.alarmAlert = projectData.alarmMsg
        }
        if (projectData.code == 2) {
          this.cardList.forEach((item) => {
            if (item.surveyEntityCode == projectData.surveyEntityCode) {
              Object.assign(item, projectData)
            }
          })
        }
        // if (this.activeTab == 'second' && this.tableSockectLoad == null) {
        //   this.tableSockectLoad = new Promise((resolve, reject) => {
        //     this.getTableData(false, resolve)
        //   }).finally(() => {
        //     this.tableSockectLoad = null
        //   })
        // }
        if (this.countSocketLoad == null) {
          this.countSocketLoad = new Promise((resolve, reject) => {
            this.getCollectCount(resolve)
          }).finally(() => {
            this.countSocketLoad = null
          })
        }
      }
    }
  },
  created() {
    this.monitorData = monitorTypeList.find((item) => item.projectCode == this.projectCode)
    this.init()
  },
  mounted() {
    const localOpenVoice = localStorage.getItem('elevatorOpenAlarmVoice')
    this.openVoice = localOpenVoice ? JSON.parse(localOpenVoice) : true
  },
  activated() {
    if (this.data.length > 0) {
      this.getCollectCount()
      this.getTableData()
    } else {
      this.cardList = []
      this.paginationData.total = 0
    }
  },
  methods: {
    async routeLeave(next) {
      await (this.activeTab = 'second')
      next()
    },
    // -----------------------------------未读信息相关
    openSeach() {
      this.advancClose = !this.advancClose
    },
    closeDrawer(data) {
      this.surveyEntityCode = ''
      this.advancClose = data
    },
    // 获取左侧树
    init() {
      this.treeLoading = true
      this.tableLoading = true
      this.$api
        .getEntityMenuList(
          {
            projectId: this.projectCode
          },
          this.requestHttp
        )
        .then((res) => {
          this.treeLoading = false
          this.tableLoading = false
          let list = this.$tools.transData(res.data, 'code', 'parentId', 'children')
          this.data = list
          if (this.data.length > 0) {
            let treeCode = []
            this.data.map((item) => {
              treeCode.push(item.code)
            })
            this.expanded = [this.data[0].id]
            this.$nextTick(() => {
              this.$refs.tree.setCurrentKey(this.data[0].id)
            })
            this.checkedData = this.data[0]
            this.tableCode = this.data[0].code
            this.tableData = []
            this.activeTab = 'second'
            this.getCollectCount()
            this.getTableData()
          } else {
            this.cardList = []
            this.paginationData.total = 0
          }
        })
    },
    // 获取总数信息
    getCollectCount(resolve = null) {
      let paramData = {
        surveyCode: '',
        startTime: '',
        endTime: '',
        regionCode: ''
      }
      this.$api.getMonitorStatisticData({ menuCode: this.checkedData.code, projectCode: this.projectCode, page: 1, pageSize: 10, ...paramData }, this.requestHttp).then((res) => {
        resolve && resolve()
        this.dayCount = Number(res.data.dayCount) || 0
        this.unConfirmCount = Number(res.data.unConfirmCount) || 0
      })
    },
    // Tab切换
    handleClick(type) {
      this.activeTab = type
      if (this.activeTab == 'second') {
        this.getTableData()
      }
    },
    // 改变电梯消音
    changeVoiceFlag() {
      this.openVoice = !this.openVoice
      localStorage.setItem('elevatorOpenAlarmVoice', this.openVoice)
    },
    // 获取列表信息
    getTableData(showLoading = true, resolve = null) {
      this.tableLoading = showLoading
      this.$api
        .getRealMonitoringListOld(
          {
            entityMenuCode: this.tableCode,
            projectCode: this.projectCode,
            isHistory: 0,
            page: this.paginationData.currentPage,
            pageSize: this.paginationData.pageSize
          },
          this.requestHttp
        )
        .then((res) => {
          this.tableLoading = false
          resolve && resolve()
          if (res.code == 200) {
            if (res.data.list) {
              this.cardList = res.data.list
              this.paginationData.total = res.data.totalCount
            } else {
              this.cardList = []
              this.paginationData.total = 0
            }
          }
        })
    },
    // 电梯跳转详情
    elevatorDetails(val) {
      this.$router.push({
        path: '/elevatorMenu/elevatorMonitor/elevatorDetail',
        query: {
          surveyEntityCode: val.surveyEntityCode,
          projectCode: this.projectCode
        }
      })
    },
    // 跳转监测历史趋势
    jumpMonitorDetail(item) {
      return
      this.$router.push({
        path: 'elevatorMonitor/monitorDetails',
        query: {
          surveyCode: item.surveyEntityCode,
          surveyName: item.surveyEntityName,
          projectCode: this.projectCode
        }
      })
    },
    showMoreRecord(item) {
      this.surveyEntityCode = item.surveyEntityCode
      this.advancClose = !this.advancClose
    },
    // 查询
    selectList() {
      this.paginationData.currentPage = 1
      this.getTableData()
    },
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    treeChecked(data, checked) {
      this.checkedData = data
      this.$refs.tree.setCheckedNodes([data])
    },
    //  ----------------------------------------------------分页相关------------------------------------------------------------
    handleSizeChange(val) {
      this.paginationData.currentPage = 1
      this.paginationData.pageSize = val
      this.getTableData(this.checkedData.id)
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.getTableData(this.checkedData.id)
    },
    //  ----------------------------------------------------树状结构相关---------------------------------------------------------
    /**
     * 树状图点击
     */
    handleNodeClick(data, checked) {
      this.tableCode = data.code
      this.$refs.tree.setCheckedNodes([data])
      this.paginationData.currentPage = 1
      // this.$store.commit('changeTableLabel', 'search')
      this.checkedData = data
      if (this.activeTab == 'second') {
        this.getCollectCount()
        this.getTableData()
      } else {
        this.tableCodeRefesh(data.code)
      }
    },
    /**
     * 监测实体tree切换，图纸更新Fn
     */
    async tableCodeRefesh(code) {
      await (this.tableCode = code)
      this.$refs.scadaShow.getScadaList() // 图形SCADA数据
    },
    closeAudio() {
      this.$refs.sinoPanel.closeAudio()
    },
    // 树划入划出效果
    onMouseOver(id) {
      const parentWidth = this.$refs[`nodeLabel${id}`].parentNode.offsetWidth // 获取元素父级可视宽度
      const contentWidth = this.$refs[`nodeLabel${id}`].offsetWidth // 获取元素可视宽度
      this.showTooltip = contentWidth <= parentWidth
    }
  }
}
</script>
<style lang="scss" scoped>
.monitor-content {
  height: 100%;
  display: flex;
  ::v-deep .monitor-content-left {
    width: 246px;
    height: 100%;
    background: #fff;
    border-radius: 4px;
    padding: 16px;
    overflow: auto;
    .el-tree {
      height: 100%;
    }
    .el-tree-node {
      .el-tree-node__content {
        padding: 6px 0;
        height: auto;
      }
    }
    .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
      background-color: #d9e1f8;
    }
  }
  .monitor-content-right {
    height: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
    .right-heade {
      padding: 0 10px !important;
      height: 50px;
      margin-left: 16px;
      background: #fff;
      border-radius: 4px;
      position: relative;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: space-between;
      .group-btns {
        display: flex;
        align-items: center;
        & > button {
          margin: auto 0;
        }
      }
      .alarm-alert {
        width: fit-content;
        max-width: 60%;
        height: 38px;
        line-height: 38px;
        background: linear-gradient(90deg, rgb(255 228 228 / 0%) 0%, #ffe4e4 49%, rgb(255 228 228 / 0%) 100%);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        > span {
          font-size: 14px;
          font-family: 'PingFang SC-Regular', 'PingFang SC';
          font-weight: 400;
          color: #fa403c;
        }
        > svg {
          font-size: 18px;
          color: #fff;
        }
      }
      .heade-pattern {
        display: flex;
        // position: absolute;
        // right: 16px;
        // top: 50%;
        // margin: 0 !important;
        // transform: translateY(-50%);
        .pattern-item {
          cursor: pointer;
          font-size: 15px;
          .pattern-icon {
            font-size: 16px;
            margin-right: 6px;
          }
        }
        .pattern-item:last-child {
          margin-left: 16px;
        }
      }
      .voice-box {
        margin: auto 0;
        width: 36px;
        height: 24px;
        background: #e6effc;
        border-radius: 100px;
        display: flex;
        cursor: pointer;
        > div {
          width: 16px;
          height: 16px;
          margin: auto;
        }
      }
      .voice-on-icon {
        background: url('~@/assets/images/monitor/voice-on.png') no-repeat;
        background-size: 100% 100%;
      }
      .voice-off-icon {
        background: url('~@/assets/images/monitor/voice-off.png') no-repeat;
        background-size: 100% 100%;
      }
      .alarm-box {
        // width: 60px;
        padding: 0 10px 0 5px;
        height: 30px;
        background: #f53f3f;
        border-radius: 100px;
        display: flex;
        justify-content: center;
        margin: 0 10px;
        cursor: pointer;
        svg,
        span {
          margin: auto 0;
          color: #fff;
        }
        > span {
          font-size: 12px;
          font-family: 'PingFang SC-Medium', 'PingFang SC';
        }
      }
    }
    .right-content {
      flex: 1;
      overflow: hidden;
      padding: 20px 20px 0 20px;
    }
  }
  ::v-deep .card_info {
    // padding-top: 30px;
    height: calc(100% - 50px);
    padding: 0px;
    overflow: auto;
    overflow-x: hidden;
    .el-row {
      overflow-y: auto;
    }
    // .el-col {
    //   height: calc(100% - 45px);
    // }
  }
  .card {
    height: 310px;
    margin-bottom: 16px;
    .card_options {
      display: inline-block;
      width: calc(50% - 2px);
      // min-width: 50%;
    }
    .box-card {
      height: 310px;
      background: #fff;
      border-radius: 4px;
      .cardTitle {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .echart-title {
          width: 50%;
          font-size: 15px;
          font-family: 'PingFang SC-Medium', 'PingFang SC';
          font-weight: 500;
          color: #121f3e;
          cursor: pointer;
        }
        .titleRight {
          width: 50%;
          display: flex;
          justify-content: flex-end;
          .totalBox {
            max-width: 60px;
            color: #fff;
            background: #f53f3f;
            border-radius: 100px;
            margin-right: 10px;
            text-align: center;
            padding: 0 10px;
            cursor: pointer;
          }
        }
      }
    }
    .title-right {
      float: right;
      font-size: 14px;
      font-family: 'PingFang SC-Medium', 'PingFang SC';
      font-weight: 500;
      color: #7f848c;
      cursor: pointer;
      display: flex;
    }
    ::v-deep .el-card__header {
      height: 54px;
      background: #fff;
      box-shadow: 0 0 5px 0 rgb(18 31 62 / 12%);
    }
    ::v-deep .el-card__body {
      height: 256px;
      overflow: auto;
      padding: 0 !important;
      .info-list {
        display: flex;
        // justify-content: space-between;
        justify-content: flex-start;
        padding: 0 20px;
        padding-right: 0;
        max-height: 66px;
        min-height: 66px;
        overflow: auto;
      }
    }
    .card_bottom {
      height: 45px;
      line-height: 45px;
      padding-left: 20px;
      position: relative;
      top: -45px;
      color: #bbc3ce;
    }
  }
  .echart-null {
    margin: 0 auto;
    width: 50%;
    text-align: center;
    color: #8a8c8f;
  }
  .font-red {
    color: #fa4764;
  }
  .info-list {
    .info_list_icon {
      width: 48px;
      height: 48px;
      display: inline-block;
      margin: auto 0;
      color: #fd9a5e;
      font-size: 24px;
      text-align: center;
    }
    .parameterUnit {
      color: #353535;
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 2px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .parameterName {
      color: #929cb3;
      font-size: 14px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .img_null {
    margin: 0 auto;
    width: 50%;
    text-align: center;
    color: #8a8c8f;
  }
  .video_icon {
    margin-right: 20px;
    font-weight: 700 !important;
    font-size: 16px !important;
  }
  ::v-deep .el-pagination {
    .btn-next,
    .btn-prev {
      background: transparent;
    }
    .el-pager li {
      background: transparent;
    }
  }
}
.nodeLabel {
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
</style>
