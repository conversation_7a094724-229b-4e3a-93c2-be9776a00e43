<!--
 * @Author: hedd
 * @Date: 2023-08-16 16:11:21
 * @LastEditTime: 2023-09-04 17:06:28
 * @FilePath: \ihcrs_pc\src\components\pageContainer\index.vue
 * @Description:
-->
<template>
  <div v-if="!footer" class="page-container">
    <header class="container-header">
      <slot name="header" />
    </header>
    <aside class="container-content">
      <slot name="content" />
    </aside>
  </div>
  <div v-else class="page-container container-has-header">
    <div>
      <header class="container-header">
        <slot name="header" />
      </header>
      <aside class="container-content">
        <slot name="content" />
      </aside>
    </div>
    <footer class="container-footer">
      <slot name="footer" />
    </footer>
  </div>
</template>
<script>
export default {
  name: 'PageContainer',
  props: {
    footer: {
      type: Boolean,
      default: false
    }
  }
}
</script>
<style lang="scss" scoped>
.page-container {
  display: flex;
  flex-direction: column;
  height: calc(100% - 30px);
  width: calc(100% - 30px);
  margin: 15px;
  position: relative;
  // padding: 10px;
  .container-header {
    background: #fff;
    flex: 0 0 auto;
    border-radius: 4px;
  }

  .container-aside {
    margin: 7px 0;
  }

  .container-content {
    /* background: #fff; */
    flex: 1 1 auto;
    border-radius: 4px;
    overflow: hidden;
  }

  .container-footer {
    position: absolute;
    bottom: 0;
    height: 56px;
    line-height: 56px;
    padding: 0 20px;
    text-align: right;
    width: 100%;
    background: #fff;
    flex: 0 0 auto;
    box-shadow: 0 0 12px 3px rgb(0 0 0 / 10%);
  }
}

.container-has-header {
  height: 100%;
  margin: 0;
  width: 100%;

  & > div {
    width: calc(100% - 30px);
    margin: 15px;
    height: calc(100% - 84px);
    display: flex;
    flex-direction: column;
  }
}
</style>
