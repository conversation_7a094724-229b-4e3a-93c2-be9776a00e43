<template>
  <PageContainer class="securityOperationMonitor">
    <div slot="header" class="securityOperationMonitor-header">
      <el-tabs v-show="currentPattern == 2" v-model="tabActive">
        <el-tab-pane v-for="item in tabs" :key="item.projectCode" :label="item.projectName" :name="item.projectCode" />
      </el-tabs>
      <div class="heade-pattern">
        <div class="pattern-item" @click="switchPattern(1)">
          <svg-icon :name="currentPattern == 1 ? 'chartModeActive' : 'chartMode'" class="pattern-icon" />
          <span :style="{ color: currentPattern == 1 ? '#3562DB' : '#414653' }">图形模式</span>
        </div>
        <div class="pattern-item" @click="switchPattern(2)">
          <svg-icon :name="currentPattern == 2 ? 'listModeActive' : 'listMode'" class="pattern-icon" />
          <span :style="{ color: currentPattern == 2 ? '#3562DB' : '#414653' }">列表模式</span>
        </div>
      </div>
    </div>
    <div slot="content" style="height: 100%">
      <ElectricitySafety v-if="tabActive == 'IEMC-ElectricitySafe'" :projectCode="tabActive" @jumpDetail="jumpMonitorDetail" />
      <div v-else-if="currentPattern == 2" class="securityOperationMonitor-content">
        <div class="statistics-list">
          <div v-for="item in filterStatisticsList" :key="item.title" class="statistics-item" :style="{ cursor: item.isClick ? 'pointer' : '' }" @click="onStatisticsList(item)">
            <p class="item-title">{{ item.title }}</p>
            <p class="item-value">{{ item.value || 0 }}<span>个</span></p>
            <img class="item-icon" :src="item.icon" :alt="item.title" />
          </div>
        </div>
        <div class="content-main">
          <div v-if="tabActive != 'IEMC-AccessControlEquipment'" class="search-from">
            <el-input v-model="searchFrom.surveyName" placeholder="请输入设备名称" clearable style="width: 200px"></el-input>
            <el-select
              v-if="['IEMC_AlarmCard', 'IEMC_PositioningCard', 'IEMC-SecurityDoor', 'IEMC-FixedAlarmBtn', 'IEMC_AlarmChestCard'].includes(tabActive)"
              v-model="searchFrom.status"
              placeholder="请选择设备状态"
              clearable
              style="width: 200px"
            >
              <el-option v-for="item in quipmentStatusList" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
            <el-select v-if="tabActive !== 'IEMC_AlarmChestCard'" v-model="searchFrom.paramId" placeholder="请选择监控参数" clearable style="width: 200px">
              <el-option v-for="(item, index) in monitoringParameterList" :key="index + item.paramName" :label="item.paramName" :value="item.paramId"></el-option>
            </el-select>
            <el-select v-if="getName" v-model="searchFrom.entityMenuCode" :placeholder="'请选择' + getName + '分组'" clearable style="width: 200px">
              <el-option v-for="item in entityMenuList" :key="item.code" :label="item.name" :value="item.code"></el-option>
            </el-select>
            <el-select v-if="tabActive !== 'IEMC_AlarmChestCard'" ref="treeSelect" v-model="searchFrom.space" clearable placeholder="请选择设备位置" @clear="handleClear">
              <el-option hidden :value="searchFrom.space" :label="areaName"> </el-option>
              <el-tree
                :data="serverSpaces"
                :props="serverDefaultProps"
                :load="serverLoadNode"
                lazy
                :expand-on-click-node="false"
                :check-on-click-node="true"
                @node-click="handleNodeClick"
              >
              </el-tree>
            </el-select>
            <div style="display: inline-block">
              <el-button type="primary" plain @click="resetForm">重置</el-button>
              <el-button type="primary" @click="searchForm">查询</el-button>
            </div>
          </div>
          <AccessControl v-if="tabActive == 'IEMC-AccessControlEquipment'" ref="AccessControlEquipment" :projectCode="tabActive" @jumpDetail="jumpMonitorDetail" />
          <div v-loading="listLoading" class="main-content">
            <el-row v-if="['IEMC_AlarmCard', 'IEMC_PositioningCard', 'IEMC-SecurityDoor', 'IEMC-FixedAlarmBtn', 'IEMC_AlarmChestCard'].includes(tabActive)" :gutter="16">
              <el-col v-for="item in listData" :key="item.surveyEntityCode" :xs="12" :sm="12" :md="8" :xl="6" class="grouping">
                <!-- <el-tooltip :disabled="getParameterHoverDisabled(item)" placement="top" :open-delay="500"> -->
                <!-- <div slot="content" class="tooltip-content">
                    <div v-for="(value, key) in item.parameterObj" :key="key" style="display: flex">
                      <p>{{ key }}</p>
                      <div class="tooltip-content-value">
                        <p v-for="v in value" :key="v.parameterId">
                          <span>{{ v.parameterName }}：{{ v.parameterValue || '-' }} {{ v.parameterUnit || '' }}</span>
                        </p>
                      </div>
                    </div>
                    </div> -->
                <div class="entity_box" :class="item.alarm ? 'entity_box_alarm' : ''">
                  <div class="entity_header" @click="jumpMonitorDetail(item)">
                    <div class="entity_header_left">
                      <div class="entity_type entity_type_no">
                        <img v-if="item.statusName == '离线'" src="@/assets/images/monitor/offLine.png" alt="" />
                        <img v-else src="@/assets/images/monitor/onLine.png" alt="" />
                        <p>{{ item.statusName }}</p>
                      </div>
                      <span class="surveyName">{{ item.surveyEntityName }}</span>
                    </div>
                    <span class="moreBtn" @click.stop="toDetails(item)">更多</span>
                  </div>
                  <div v-scrollMove class="entity_parameter">
                    <img class="main-img" :src="monitorItemImg[tabActive][item.status == 0 ? 'on' : 'off']" :alt="item.surveyEntityName" @click="deviceClick(item)" />
                    <div
                      v-for="(ele, index) in item.parameterList1"
                      :key="item.surveyEntityCode + ele.parameterId"
                      class="entity_parameter_item"
                      :class="{ entity_parameter_item_bor: index == 0 }"
                    >
                      <p>{{ ele.parameterName }}</p>
                      <div class="entity_parameter_num">
                        <span v-showtipPlus="(isNaN(ele.parameterValue) ? ele.parameterValue : Number(ele.parameterValue).toFixed(2)) || '-'" class="entity_parameter_value"></span>
                        <span class="entity_parameter_unit">{{ ele.parameterUnit }}</span>
                      </div>
                    </div>
                  </div>
                  <div class="alarm">
                    <div class="alarm_item">
                      <p>今日报警：</p>
                      <span>{{ item.allPolice }}</span>
                    </div>
                    <div class="alarm_item">
                      <p>未处理报警：</p>
                      <span>{{ item.unDisposePolice }}</span>
                    </div>
                  </div>
                </div>
                <!-- </el-tooltip> -->
              </el-col>
            </el-row>
            <newMonitorList
              v-else-if="tabActive == 'IEMC-Anti-exhaustFan'"
              ref="newMonitorList"
              :listData="listData"
              :projectCode="tabActive"
              @jumpDetail="jumpMonitorDetail"
              @goToAlarm="goToAlarm"
            />
            <el-row v-else :gutter="16">
              <el-col v-for="item in listData" :key="item.surveyEntityCode" :xs="12" :sm="12" :md="8" :xl="4">
                <el-tooltip :disabled="getParameterHoverDisabled(item)" placement="top" :open-delay="500">
                  <div slot="content" class="tooltip-content">
                    <div v-for="(value, key) in item.parameterObj" :key="key" style="display: flex">
                      <p>{{ key }}</p>
                      <div class="tooltip-content-value">
                        <p v-for="v in value" :key="v.parameterId">
                          <!-- <span v-if="getTabActiveProjectName === '消防水箱' && v.parameterId === 5">{{ v.parameterName }}：{{ getParameterPercent(item, 5) || '-' }}%</span> -->
                          <span>{{ v.parameterName }}：{{ v.parameterValue || '-' }} {{ v.parameterUnit || '' }}</span>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div class="monitor-item">
                    <div class="item-heade" @click="jumpMonitorDetail(item)">{{ item.surveyEntityName }}</div>
                    <div class="item-main">
                      <img
                        class="main-img"
                        :style="{ cursor: ['IEMC-CameraEquipment'].includes(tabActive) ? 'pointer' : 'default' }"
                        :src="monitorItemImg[tabActive][item.status == 0 ? 'on' : 'off']"
                        :alt="item.surveyEntityName"
                        @click="deviceClick(item)"
                      />
                      <div class="main-item">
                        <div class="main-item-box">
                          <p class="item-title">状态:</p>
                          <p class="item-value">{{ item.status == 0 ? '正常' : item.status == 6 ? '离线' : '-' }}</p>
                        </div>
                        <div v-if="['烟感温感', '防火门', '手报'].includes(getTabActiveProjectName)" class="main-item-box">
                          <p class="item-title">故障状态:</p>
                          <p class="item-value">{{ getParameterValueById(item, 100249).parameterValue || '-' }}</p>
                        </div>
                        <div v-if="getTabActiveProjectName === '消防水箱'" class="main-item-num">{{ getParameterPercent(item, 5) || '-' }}%</div>
                        <div v-if="getTabActiveProjectName === '末端试水'" class="main-item-num">
                          {{ getParameterValueById(item, 6).parameterValue || '-' }}{{ getParameterValueById(item, 6).parameterUnit }}
                        </div>
                      </div>
                      <!-- <div class="main-item">
                        <p class="item-title">运行动态</p>
                        <p class="item-value">关</p>
                      </div> -->
                    </div>
                    <div class="item-footer">
                      <div class="footer-item" @click="goToAlarm({ objectName: item.surveyEntityName, dataRange: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')] })">
                        <p>
                          今日报警：<span>{{ item.allPolice }}</span>
                        </p>
                      </div>
                      <div class="footer-item" @click="goToAlarm({ objectName: item.surveyEntityName, alarmStatus: '0' })">
                        <p>
                          未处理报警：<span>{{ item.unDisposePolice }}</span>
                        </p>
                      </div>
                    </div>
                  </div>
                </el-tooltip>
              </el-col>
            </el-row>
            <moreDetails ref="moreDetails" />
          </div>
          <div v-if="tabActive != 'IEMC-AccessControlEquipment'" class="main-footer">
            <el-pagination
              :current-page="pagination.current"
              :page-sizes="pagination.pageSizeOptions"
              :page-size="pagination.size"
              :layout="pagination.layoutOptions"
              :total="pagination.total"
              @size-change="paginationSizeChange"
              @current-change="paginationCurrentChange"
            />
          </div>
        </div>
      </div>
      <div v-else class="securityOperationMonitor-mode-content">
        <div class="monitor-content-left">
          <el-tree
            slot="content"
            ref="teamTree"
            v-loading="treeLoading"
            class="team-tree"
            :check-strictly="true"
            :data="teamTreeData"
            :props="defaultProps"
            node-key="id"
            :highlight-current="true"
            @node-click="handleTeamClick"
          >
            <span slot-scope="{ node }" class="custom-tree-node">
              <el-tooltip class="item" effect="dark" :content="node.label" placement="top-start" :disabled="!isShowTooltip">
                <span @mouseenter="visibilityChange($event)">{{ node.label }}</span>
              </el-tooltip>
            </span>
          </el-tree>
        </div>
        <div class="monitor-content-right">
          <graphics-mode ref="scadaShow" />
        </div>
      </div>
      <rtspVideoDialog :visible.sync="isRtspVideo" :selectItem="selectItem" />
      <!--安检监控画面  -->
      <scuritySafety v-if="DialogShow" ref="scuritySafety" :visible.sync="DialogShow" :title="title" :positionId="positionId"></scuritySafety>
    </div>
  </PageContainer>
</template>
<script>
import sbzsIcon from '@/assets/images/monitor/sbzs_icon.png'
import zcsbIcon from '@/assets/images/monitor/zcsb_icon.png'
import lxIcon from '@/assets/images/monitor/lx_icon.png'
import jrbjsIcon from '@/assets/images/monitor/jrbjs_icon.png'
import wclbjsIcon from '@/assets/images/monitor/wclbjs_icon.png'
// import xzwdbjIcon from '@/assets/images/monitor/xzwdbj_icon.png'
import graphicsMode from '../../airMenu/components/graphicsMode'
import { mapGetters } from 'vuex'
import tableListMixin from '@/mixins/tableListMixin.js'
import { monitorItemImg, monitorTypeList } from '@/util/dict.js'
import { transData, ListTree } from '@/util'
import ElectricitySafety from './electricitySafety.vue'
import AccessControl from './accessControl.vue'
import rtspVideoDialog from './rtspVideoDialog.vue'
import moreDetails from './moreDetails.vue'
import newMonitorList from './newMonitorList.vue'
import moment from 'moment'
import scuritySafety from '../screeningDetails'
export default {
  name: 'securityOperationMonitor',
  components: {
    graphicsMode,
    ElectricitySafety,
    rtspVideoDialog,
    moreDetails,
    scuritySafety,
    AccessControl,
    newMonitorList
  },
  mixins: [tableListMixin],
  props: {
    tabs: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      moment,
      isRtspVideo: false,
      monitorItemImg,
      currentPattern: 2, // 当前模式
      tabActive: this.tabs[0].projectCode,
      listLoading: false,
      listData: [],
      searchFrom: {
        surveyName: '', // 设备名称
        status: '', // 设备状态
        space: '', // 设备位置
        paramId: '', // 监控参数id
        entityMenuCode: '' // 分组
      },
      quipmentStatusList: [
        {
          value: '0',
          label: '在线'
        },
        {
          value: '6',
          label: '离线'
        },
        {
          value: '2',
          label: '报警'
        }
      ],
      serverSpaces: [], // 空间位置
      spaces: [], // 空间位置
      serverDefaultProps: {
        label: 'ssmName',
        isLeaf: 'leaf',
        children: 'children'
      },
      areaName: '', // 选中 下拉树的name
      entityMenuList: [], // 分组列表
      monitoringParameterList: [], // 参数列表
      statisticsList: [
        {
          title: '设备总数',
          icon: sbzsIcon,
          value: 0,
          isClick: false
        },
        {
          title: '正常设备',
          icon: zcsbIcon,
          value: 0,
          status: '0',
          isClick: true
        },
        {
          title: '离线设备',
          icon: lxIcon,
          value: 0,
          status: '6',
          isClick: true
        },
        {
          title: '故障设备',
          icon: lxIcon,
          value: 0,
          status: '2',
          isClick: true
        },
        {
          title: '今日报警数',
          icon: jrbjsIcon,
          value: 0,
          isClick: true,
          params: { dataRange: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')] }
        },
        {
          title: '未处理报警数',
          icon: wclbjsIcon,
          value: 0,
          isClick: true,
          params: { alarmStatus: '0' }
        }
        // {
        //   title: '新增未读报警',
        //   icon: xzwdbjIcon,
        //   value: 0
        // }
      ],
      selectGraphicsCode: '', // 选中图纸code
      treeLoading: false,
      defaultProps: {
        children: 'children',
        label: 'name',
        value: 'id'
      },
      teamTreeData: [], // 树状数据
      isShowTooltip: false,
      checkedTeamData: {},
      selectItem: {},
      DialogShow: false,
      title: '',
      currentStatus: ''
    }
  },
  computed: {
    getName() {
      let item = this.tabs.find((v) => v.projectCode == this.tabActive)
      return item.parentCode == monitorTypeList.find((item) => item.projectName == '安防系统监测').projectCode ? item.projectName : ''
    },
    getTabActiveProjectName() {
      return monitorTypeList.find((item) => item.projectCode == this.tabActive).projectName
    },
    filterStatisticsList() {
      const filterFlag = ['烟感温感', '防火门', '手报'].includes(this.getTabActiveProjectName)
      if (filterFlag) {
        return this.statisticsList.filter((item) => item.status != '6')
      } else {
        return this.statisticsList.filter((item) => item.status != '2')
      }
    },
    ...mapGetters({
      socketIemcMsgs: 'socket/socketIemcMsgs'
    })
  },
  watch: {
    tabActive: {
      handler: function (val, oldVal) {
        this.pagination.current = 1
        Object.assign(this.searchFrom, {
          surveyName: '', // 设备名称
          space: '', // 设备位置
          paramId: '', // 监控参数id
          entityMenuCode: '', // 分组
          status: ''
        })
        console.log('tab切换')
        this.handleClear()
        this.getDataList()
        this.getStatisticsOverview()
        this.getParamInfoByProject()
        if (this.getName) {
          this.getEntityMenuList()
        }
      },
      immediate: true,
      positionId: ''
    },
    socketIemcMsgs(data) {
      let itemData = JSON.parse(data)
      let newList = JSON.parse(JSON.stringify(this.listData))
      newList.forEach((item) => {
        if (item.surveyEntityCode == itemData.surveyEntityCode) {
          Object.assign(item, itemData)
        }
      })
      this.listData = this.setData(newList)
      console.log(this.listData)
    },
    searchFrom: {
      handler(val, oldVal) {
        this.pagination.current = 1
      },
      deep: true
    }
  },
  created() {
    this.getTreelist()
    this.getParamInfoByProject()
    this.getScaleImgByProjectCode()
    // this.getEntityMenuList()
  },
  activated() {
    this.getDataList()
    this.getStatisticsOverview()
    this.getParamInfoByProject()
    if (this.getName) {
      this.getEntityMenuList()
    }
  },
  methods: {
    // 跳转监测历史趋势
    jumpMonitorDetail(item) {
      const activeData = this.tabs.find((v) => v.projectCode == this.tabActive)
      const name = monitorTypeList.find((item) => item.projectCode == activeData.parentCode).projectName
      let path = ''
      if (name == '安防系统监测') {
        path = '/securityMenu/securityMonitor/monitorDetails'
      } else if (name == '消防系统监测') {
        path = '/fireControlMenu/fireControlMonitor/monitorDetails'
      } else if (name == '多媒体系统监测') {
        path = '/multiMediaMenu/multiMediaMonitor/monitorDetails'
      }
      if (!path) return
      let locationId = null
      if (activeData.projectCode === 'IEMC_AlarmChestCard') {
        locationId = item.parameterList?.find((item) => item.parameterName === '位置信息')?.parameterId
      }
      this.$router.push({
        path: path,
        query: {
          surveyCode: item.surveyEntityCode,
          surveyName: item.surveyEntityName,
          projectCode: this.tabActive,
          assetId: item.assetId,
          locationId
        }
      })
    },
    // 设备点击
    deviceClick(item) {
      this.selectItem = item
      if (this.tabActive == 'IEMC-CameraEquipment') {
        this.$api.getVideoUrlByDeviceCode({ deviceCode: item.assetId }).then((res) => {
          // this.isRtspVideo = true
          this.selectItem.rtsp = 'rtsp://admin:Sinomis+123@************:554/h264/ch1/main/av_stream'
          if (res.code == 200 && res.data) {
            this.isRtspVideo = true
            this.selectItem.rtsp = res.data
          } else {
            this.$message.warning('暂未关联摄像机视频！')
          }
        })
      }
    },
    getParameterPercent(item, value) {
      const parameter = item.parameterList.find((e) => e.parameterId === value) || {}
      const parameterValue = parameter.parameterValue || 0
      return ((parameterValue / item.imsHeight || 0) * 100).toFixed(2)
    },
    getParameterValueById(item, value) {
      return item.parameterList.find((e) => e.parameterId === value) || {}
    },
    getParameterHoverDisabled(item) {
      const ActiceList = ['末端试水', '消防水箱']
      if (ActiceList.includes(this.getTabActiveProjectName)) {
        return !(item.parameterList.length > 0)
      }
    },
    // 模式切换
    switchPattern(type) {
      if (this.currentPattern != type) {
        this.currentPattern = type
        // this.searchForm()
        if (type === 1) {
          if (Object.keys(this.checkedTeamData).length) {
            this.$nextTick(() => {
              this.setScadaShow()
            })
          } else {
            this.$message.warning('暂无数据！')
          }
        }
      }
    },
    onStatisticsList(item) {
      if (this.tabActive == 'IEMC-AccessControlEquipment') {
        this.$refs.AccessControlEquipment.getDataList(item.status)
        this.goToAlarm(item.params)
      } else if (item.isClick) {
        if (item.status) {
          this.pagination.current = 1
          this.searchFrom.status = item.status
          this.getDataList()
        } else {
          this.goToAlarm(item.params)
        }
      }
    },
    // 跳转报警
    goToAlarm(params) {
      this.$router.push({
        name: 'allAlarmIndex',
        params: {
          projectCode: this.tabActive,
          alarmStatus: '3',
          ...params
        }
      })
    },
    // 获取检测项列表
    getDataList() {
      let params = {
        ...this.searchFrom,
        projectCode: this.tabActive,
        page: this.pagination.current,
        pageSize: this.pagination.size
      }
      this.listLoading = true
      this.listData = []
      this.$api
        .GetSecurityOtherSysList(params)
        .then((res) => {
          this.listLoading = false
          if (res.code == 200) {
            if (res.data.list && ['IEMC_AlarmCard', 'IEMC_PositioningCard', 'IEMC-SecurityDoor', 'IEMC-FixedAlarmBtn', 'IEMC_AlarmChestCard'].includes(this.tabActive)) {
              res.data.list.forEach((item) => {
                item.parameterList1 = item.parameterList.slice(0, 2)
              })
            }
            this.listData = res.data.list ? this.setData(res.data.list) : []
            this.pagination.total = res.data.totalCount ? res.data.totalCount : 0
          }
        })
        .catch(() => {
          this.listLoading = false
        })
    },
    setData(list) {
      list.forEach((item) => {
        item.parameterObj = {}
        item.parameterList.forEach((v) => {
          if (!item.parameterObj[v.harvesterName]) {
            item.parameterObj[v.harvesterName] = []
          }
          item.parameterObj[v.harvesterName].push(v)
        })
      })
      return list
    },
    // 获取参数
    getParamInfoByProject() {
      this.$api.GetParamInfoByProject({ projectCode: this.tabActive }).then((res) => {
        if (res.code == 200) {
          this.monitoringParameterList = res.data
        }
      })
    },
    // 获取分组列表
    getEntityMenuList() {
      this.$api.GetEntityMenuList({ projectId: this.tabActive }).then((res) => {
        if (res.code == 200) {
          this.entityMenuList = res.data
        }
      })
    },
    // 获取统计信息
    getStatisticsOverview() {
      this.$api.getStatisticsOverview({ projectCode: this.tabActive, isCockpit: false }).then((res) => {
        if (res.code == 200) {
          this.statisticsList[0].value = res.data.deviceCount
          this.statisticsList[1].value = res.data.normalCount
          this.statisticsList[2].value = res.data.offLineCount
          this.statisticsList[3].value = res.data.abnormalCount
          this.statisticsList[4].value = res.data.policeCount
          this.statisticsList[5].value = res.data.unDisposedCount
        }
      })
    },
    // 获取图纸列表
    getScaleImgByProjectCode() {
      console.log(this.tabs)
      const filterNames = ['末端试水', '消防水箱']
      const filterTabs = this.tabs.filter((e) => filterNames.includes(e.projectName))
      if (filterTabs.length) {
        const filterProjectArr = Array.from(filterTabs, ({ projectCode }) => projectCode)
        const params = {
          projectCode: filterProjectArr.toString()
        }
        // this.treeLoading = true
        this.$api.getScaleImgByProjectCode(params).then((res) => {
          // this.treeLoading = false
          if (res.code == '200') {
            this.teamTreeData = res.data
            this.checkedTeamData = this.teamTreeData.length ? this.teamTreeData[0] : {}
          } else {
            this.teamTreeData = []
          }
        })
      }
    },
    // 空间数据清除
    handleClear() {
      this.searchFrom.space = ''
      this.areaName = ''
    },
    // 选择下拉树 数据
    handleNodeClick(data) {
      this.searchFrom.space = data.id
      this.areaName = data.ssmName
      this.$refs.treeSelect.blur()
    },
    // 下拉树懒加载
    serverLoadNode(node, resolve) {
      if (node.level == 0) {
        return resolve(node.data)
      } else if (node.level > 0 && node.level < 4) {
        return resolve(node.data.children)
      } else {
        return this.spaceTreeChange(node.data, resolve)
      }
    },
    // 获取服务空间树形结构
    getTreelist() {
      // this.$api.getStructureTree({}, __PATH.USER_CODE).then((res) => {
      this.$api.getStructureTree({}).then((res) => {
        if (res.code == 200) {
          this.spaces = res.data
          // 增加 懒加载节点
          res.data.map((e) => {
            e.leaf = false
          })
          // 转换为树形结构数据
          this.serverSpaces = transData(res.data, 'id', 'pid', 'children')
        }
      })
    },
    // 服务空间树结构点击获取自己及父节点ID并请求数据实现懒加载
    spaceTreeChange(value, resolve) {
      const child = ListTree(this.spaces, value.pid)
      child.push(value.id)
      let treeId = child.toString()
      let data = {
        current: 1,
        functionDictId: '',
        simCode: treeId,
        size: 999
      }
      // this.$api.getSpaceInfoList(data, __PATH.USER_CODE).then((res) => {
      this.$api.getSpaceInfoList(data).then((res) => {
        if (res.code == 200) {
          if (typeof resolve === 'function') {
            let treeNodeData = JSON.parse(JSON.stringify(res.data.records))
            treeNodeData.map((e) => {
              e.leaf = true
            })
            resolve(treeNodeData)
          }
        }
      })
    },
    // 树状图点击
    handleTeamClick(data) {
      this.checkedTeamData = data
      this.setScadaShow()
    },
    setScadaShow() {
      this.$refs.teamTree.setCurrentKey(this.checkedTeamData.id)
      this.$refs.scadaShow.setScadaData(this.checkedTeamData.id)
    },
    visibilityChange(e) {
      const item = e.target
      const itemWidth = item.offsetWidth
      const itemScrollWidth = item.scrollWidth
      if (itemWidth < itemScrollWidth) {
        this.isShowTooltip = true
      } else {
        this.isShowTooltip = false
      }
    },
    // 重置查询表单
    resetForm() {
      this.searchFrom = {
        surveyName: '',
        orderBy: 0,
        descOrAsc: '',
        status: ''
      }
      this.pagination.size = 15
      this.pagination.current = 1
      this.areaName = ''
      this.searchForm()
    },
    // 查询
    searchForm() {
      this.pagination.current = 1
      this.getDataList()
    },
    toDetails(v) {
      if (this.tabActive == 'IEMC-SecurityDoor') {
        this.title = v.surveyEntityName
        this.positionId = v.parameterList[0].harvesterId
        this.DialogShow = true
      } else {
        this.$refs.moreDetails.getEchartData(v, this.projectCode)
      }
    }
  }
}
</script>
<style lang="scss">
.tooltip-content {
  font-size: 14px;
  font-weight: 400;
  color: #fff;
  line-height: 22px;
  p {
    margin: 0;
  }
  .tooltip-content-value {
    display: flex;
    flex-wrap: wrap;
    max-width: 500px;
    p {
      margin-left: 15px;
    }
  }
}
</style>
<style lang="scss" scoped>
.securityOperationMonitor {
  p {
    margin: 0;
  }
  ::v-deep .container-header {
    border-radius: 4px 4px 0 0;
  }
  .securityOperationMonitor-header {
    position: relative;
    height: 40px;
    ::v-deep .el-tabs {
      .el-tabs__item {
        padding: 0 25px;
      }
    }
    .heade-pattern {
      display: flex;
      position: absolute;
      right: 16px;
      top: 50%;
      margin: 0 !important;
      transform: translateY(-50%);
      .pattern-item {
        cursor: pointer;
        font-size: 15px;
        .pattern-icon {
          font-size: 16px;
          margin-right: 6px;
        }
      }
      .pattern-item:last-child {
        margin-left: 16px;
      }
    }
  }
  .securityOperationMonitor-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    overflow-x: hidden;
    .statistics-list {
      width: 100%;
      display: flex;
    }
    .statistics-item {
      width: calc(100% / 5);
      height: 120px;
      min-width: 150px;
      margin-left: 16px;
      padding: 24px;
      background: #fff;
      border-radius: 4px;
      margin-top: 16px;
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .item-title {
        font-size: 15px;
        font-weight: 500;
        color: #121f3e;
      }
      .item-value {
        height: 36px;
        font-size: 30px;
        font-weight: bold;
        color: #121f3e;
        line-height: 36px;
        & > span {
          margin-left: 4px;
          font-size: 15px;
          font-weight: 500;
          color: #ccced3;
        }
      }
      .item-icon {
        position: absolute;
        right: 24px;
        bottom: 24px;
        width: 40px;
        height: 40px;
      }
    }
    .statistics-item:first-child {
      margin-left: 0;
    }
    .content-main {
      flex: 1;
      display: flex;
      flex-direction: column;
      margin-top: 16px;
      background: #fff;
      overflow: hidden;
      .search-from {
        padding: 0 0 10px 16px;
        & > div {
          margin-top: 16px;
          margin-right: 16px;
        }
      }
      .main-content {
        flex: 1;
        padding: 0 16px;
        overflow: auto;
        .grouping {
          display: flex;
          flex-direction: column;
          background: #faf9fc;
          padding: 19px 16px;
          margin: 0 4px;
          margin-bottom: 24px;
          .entity_box_alarm {
            background: linear-gradient(180deg, #ffffff 55%, #ffe2e2 100%) !important;
            border-color: #f53f3f !important;
            .alarm {
              background: transparent !important;
            }
          }
          .entity_box {
            padding: 16px;
            background: #ffffff;
            margin-bottom: 16px;
            border-radius: 4px;
            border: 1px solid #fff;
            .entity_header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              cursor: pointer;
              .entity_header_left {
                display: flex;
                align-items: center;
                .surveyName {
                  margin-left: 10px;
                }
              }
              .entity_type {
                display: flex;
                align-items: center;
                padding: 8px;
                background: #e8ffea;
                p {
                  margin: 0;
                  font-size: 14px;
                  line-height: 14px;
                  color: #009a29;
                }
                img {
                  margin-right: 7px;
                }
              }
              .entity_type_no {
                background: #f2f4f9;
                p {
                  color: #86909c;
                }
              }
              .moreBtn {
                display: inline-block;
                padding: 2px 5px;
                background-color: #3562db;
                color: #fff;
                border-radius: 2px;
              }
            }
            .entity_parameter {
              cursor: pointer;
              width: 100%;
              display: flex;
              margin: 18px 0 26px 0;
              overflow-x: hidden;
              // firefox隐藏滚动条
              scrollbar-width: none;
              // chrome隐藏滚动条
              &::-webkit-scrollbar {
                display: none;
              }
              .entity_parameter_item_bor {
                border-right: 1px solid #dcdfe6;
              }
              .entity_parameter_item_red {
                p {
                  color: #fa403c !important;
                }
                .entity_parameter_value {
                  color: #fa403c !important;
                }
                .entity_parameter_unit {
                  color: #fa403c !important;
                }
              }
              .entity_parameter_item {
                padding: 0 5px;
                // border-right: 1px solid #DCDFE6;
                flex: 1;
                // min-width: 20%;
                max-width: 50%;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                p {
                  margin: 0;
                  margin-bottom: 10px;
                  width: 100%;
                  text-align: center;
                  white-space: nowrap;
                }
                .entity_parameter_num {
                  text-align: center;
                  white-space: nowrap;
                  span {
                    font-size: 18px;
                    font-family: Arial-Bold, Arial;
                    font-weight: bold;
                    color: #333333;
                  }
                  .entity_parameter_unit {
                    font-size: 12px;
                    font-family: PingFang SC-Medium, PingFang SC;
                    font-weight: 500;
                    color: #ccced3;
                    margin-left: 4px;
                  }
                }
              }
            }
            .alarm {
              display: flex;
              padding: 8px 0;
              justify-content: space-around;
              background: #faf9fc;
              .alarm_item {
                display: flex;
                align-items: center;
                p {
                  margin: 0;
                  color: #666666;
                }
                span {
                  font-size: 18px;
                  line-height: 18px;
                  font-family: Arial-Regular, Arial;
                  font-weight: 400;
                  color: #333333;
                }
              }
            }
          }
          &_header {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            &_left {
              display: flex;
              align-items: center;
              img {
                width: 18px;
                height: 18px;
              }
              p {
                font-size: 15px;
                font-family: PingFang SC-Medium, PingFang SC;
                font-weight: 500;
                color: #333333;
                margin: 0 10px;
              }
              &_badge {
                background: #f53f3f;
                border-radius: 100px;
                display: flex;
                align-items: center;
                padding: 3px 12px;
                img {
                  width: 12px;
                  height: 12px;
                }
                span {
                  font-size: 12px;
                  color: #ffffff;
                  margin-left: 5px;
                }
              }
            }
            &_right {
              display: flex;
              .grouping_information:last-child {
                margin: 0;
                div {
                  padding: 0;
                  border: 0;
                }
              }
              .grouping_information {
                margin-right: 11px;
                padding: 5px 0;
                div {
                  padding-right: 11px;
                  display: flex;
                  align-items: center;
                  border-right: 1px solid #dcdfe6;
                }
                p {
                  margin: 0;
                  font-size: 14px;
                  font-family: PingFang SC-Regular, PingFang SC;
                  font-weight: 400;
                  color: #666666;
                  line-height: 14px;
                }
                span {
                  font-size: 18px;
                  font-family: Arial-Regular, Arial;
                  font-weight: 400;
                  color: #333333;
                  line-height: 18px;
                }
              }
            }
          }
        }
        .monitor-item {
          margin-top: 16px;
          width: 100%;
          padding: 16px;
          background: #faf9fc;
          border-radius: 4px;
          .item-heade {
            padding: 0 0 20px;
            font-size: 14px;
            font-weight: 500;
            color: #121f3e;
            line-height: 14px;
            word-break: break-all;
            cursor: pointer;
            // white-space: nowrap;/* 给文本设置不换行在一行中显示 */
            // overflow: hidden;/* 文本超出的部分隐藏 */
            // text-overflow: ellipsis;/* 文本超出的部分用省略号代替 */
          }
          .item-main {
            display: flex;
            padding-bottom: 20px;
            .main-img {
              width: 60px;
              height: 60px;
            }
            .main-item {
              display: flex;
              flex-direction: column;
              justify-content: space-between;
              padding: 6px 0 6px 20px;
              .main-item-box {
                display: flex;
                align-items: center;
              }
              .main-item-num {
                font-size: 16px;
                font-weight: bold;
                color: #121f3e;
              }
            }
            .item-title {
              font-size: 14px;
              font-weight: 400;
              color: #414653;
              line-height: 14px;
              margin-right: 10px;
            }
            .item-value {
              font-size: 16px;
              font-weight: bold;
              color: #121f3e;
              line-height: 20px;
            }
          }
          .item-footer {
            border-top: 1px solid #dcdfe6;
            padding: 10px 0 0;
            display: flex;
            .footer-item {
              width: 50%;
              text-align: center;
              cursor: pointer;
              p {
                font-size: 14px;
                font-weight: 400;
                color: #414653;
                line-height: 18px;
                span {
                  font-size: 18px;
                  color: #121f3e;
                }
              }
            }
            .footer-item:last-child {
              border-left: 1px solid #dcdfe6;
            }
          }
        }
        ::v-deep .el-row {
          display: flex;
          flex-wrap: wrap;
        }
      }
      .main-footer {
        padding: 10px 16px;
      }
    }
  }
  .securityOperationMonitor-mode-content {
    height: calc(100% - 16px);
    margin-top: 16px;
    display: flex;
    ::v-deep .monitor-content-left {
      width: 246px;
      height: 100%;
      background: #fff;
      border-radius: 4px;
      padding: 16px;
      overflow: auto;
      .el-tree {
        height: 100%;
      }
      .el-tree-node {
        .el-tree-node__content {
          padding: 6px 0;
          height: auto;
        }
      }
      .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
        background-color: #d9e1f8;
      }
    }
    .monitor-content-right {
      height: 100%;
      flex: 1;
    }
  }
}
.main-img {
  width: 60px;
  height: 60px;
}
</style>
