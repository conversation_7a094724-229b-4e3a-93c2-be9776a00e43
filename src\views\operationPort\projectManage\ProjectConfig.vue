<script>
export default {
  name: 'ProjectConfig',
  components: {
    TagConfig: () => import('./projectConfig/tagConfig/TagConfig.vue'),
    BuildingGroupConfig: () => import('./projectConfig/buildingGroupConfig/BuildingGroupConfig.vue'),
    FormConfig: () => import('./projectConfig/formConfig/FormConfig.vue'),
    TableColumnConfig: () => import('./projectConfig/tableColumnConfig/TableColumnConfig.vue'),
    StatusConfig: () => import('./projectConfig/statusConfig/StatusConfig.vue'),
    BillConfig: () => import('./projectConfig/billConfig/BillConfig.vue'),
    TimeOutConfig: () => import('./projectConfig/timeOutConfig/TimeOutConfig.vue')
  },
  data() {
    return {
      menuData: [
        { id: 'FormConfig', label: '业务表单配置' },
        { id: 'StatusConfig', label: '状态配置' },
        { id: 'TableColumnConfig', label: '列表项配置' },
        { id: 'BillConfig', label: '单据管理' },
        { id: 'TagConfig', label: '标签管理' },
        // { id: 'BuildingGroupConfig', label: '建筑分区管理' },
        { id: 'TimeOutConfig', label: '超时提醒' }
      ]
    }
  },
  computed: {
    currentKey() {
      const item = this.menuData.find((it) => it.id === this.$route.query.name) ?? this.menuData[0]
      return item.id
    }
  },
  methods: {
    onTreeNodeClick(data) {
      if (data.id !== this.currentKey) {
        this.$router.push({ query: { name: data.id } })
      }
    }
  }
}
</script>
<template>
  <PageContainer class="project-config">
    <template #content>
      <div class="project-config__left">
        <el-tree :data="menuData" node-key="id" :current-node-key="currentKey" highlight-current @node-click="onTreeNodeClick"></el-tree>
      </div>
      <component :is="currentKey" class="project-config__right" />
    </template>
  </PageContainer>
</template>
<style scoped lang="scss">
.project-config {
  ::v-deep(> .container-content) {
    display: flex;
    background-color: #fff;
  }
  &__left {
    width: 280px;
    border-right: solid 1px #eee;
    padding: 16px;
    &::v-deep(.el-tree) {
      .el-tree-node__content {
        line-height: 36px;
        height: 36px;
      }
      .el-tree-node.is-current > .el-tree-node__content {
        color: #3562db;
        background: #e6effc;
      }
    }
  }
  &__right {
    flex: 1;
    overflow: hidden;
  }
}
</style>
