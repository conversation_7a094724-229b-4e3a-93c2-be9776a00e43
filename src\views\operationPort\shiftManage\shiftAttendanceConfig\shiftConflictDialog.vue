<!--
 * @Description:
-->
<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    title="班次冲突"
    width="30%"
    :visible="visible"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <div class="content">
      <div class="content-message">{{ conflictData.message }}</div>
      <div class="content-box">
        <div class="content-box-item">
          <span>值班考勤组名称：</span>
          <span>{{ conflictData.name }}</span>
        </div>
        <div class="content-box-item">
          <span>冲突对象：</span>
          <span>{{ conflictData.confltObj }}</span>
        </div>
        <div class="content-box-item">
          <span>冲突班次：</span>
          <span>{{ conflictData.shiftName }}</span>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">关闭</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  name: 'shiftConflictDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    conflictData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {}
  },
  mounted() {},
  methods: {
    // 关闭弹窗
    closeDialog() {
      this.$emit('closeDialog')
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  overflow: auto;
  background-color: #fff !important;
  padding: 10px 35px;
  color: #333333;
  line-height: 16px;
  .content-message {
    font-size: 15px;
  }
  .content-box {
    margin: 10px 0;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #e4e7ed;
    .content-box-item {
      margin-bottom: 10px;
      span:last-child {
        margin-bottom: 0px;
      }
    }
  }
}
.model-dialog {
  padding: 0 !important;
}
::v-deep .el-dialog__body {
  padding: 0 !important;
}
</style>
