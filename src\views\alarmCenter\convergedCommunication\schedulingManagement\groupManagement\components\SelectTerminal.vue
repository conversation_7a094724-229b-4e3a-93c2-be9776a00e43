<template>
  <el-dialog title="选择终端" width="55%" :visible.sync="terminalDialogShow" custom-class="model-dialog"
    :before-close="closeDialog">
    <div class="content">
      <div class="searchForm">
        <div class="search-box">
          <el-input v-model="filterInfo.usrName" placeholder="请输入终端名称"></el-input>
          <el-select v-model="filterInfo.usrType" filterable placeholder="类型" class="ml-16">
            <el-option v-for="item in typeList" :key="item.code" :label="item.name" :value="item.code">
            </el-option>
          </el-select>
        </div>
        <div class="ml-16">
          <el-button type="primary" @click="search">查询</el-button>
          <el-button type="primary" plain @click="reset">重置</el-button>
        </div>
      </div>
      <div class="change_top">
        <div class="change_top_left">
          <span class="change_num">已选：{{multipleSelection.length}}个终端</span>
          <span v-for="(item ,index) in multipleSelection" :key="index" class="item">
            {{item.usrName}}
            <i class="el-icon-close" @click="deleteItem(item)"></i>
          </span>
        </div>
        <div class="change_top_right">
          <span v-if="multipleSelection.length" class="change_operate" @click="deleteAll">清空</span>
        </div>
      </div>
      <div class="sino_table">
        <el-table ref="sinoTable" v-loading="tableLoading" :data="tableData" :row-key="getRowKeys" height="300px" stripe
          border @selection-change="handleSelectionChange">
          <el-table-column type="selection" align="center" :reserve-selection="true" :selectable="selectEnable"
            width="55"></el-table-column>
          <el-table-column prop="usrName" label="名称">
          </el-table-column>
          <el-table-column prop="extensionModel" label="型号">
          </el-table-column>
          <el-table-column prop="extensionNum" label="号码">
          </el-table-column>
          <el-table-column prop="usrType" label="类型" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.usrType | filterType }}</span>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination :current-page="pagination.pageNum" :page-sizes="[15, 30, 50, 100]"
            :page-size="pagination.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="pageTotal"
            @size-change="handleSizeChange" @current-change="handleCurrentChange">
          </el-pagination>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="submitDialog">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'SelectTerminal',
  components: {},
  filters: {
    filterType(val) {
      return val == '2'
        ? '智能调度终端'
        : val == '3'
          ? '移动调度APP'
          : val == '1'
            ? '调度台'
            : val == '4'
              ? '数字对讲网关'
              : '电话网关'
    }
  },
  props: {
    terminalDialogShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tableLoading: false,
      filterInfo: {
        usrName: '',
        usrType: ''
      },
      tableData: [],
      multipleSelection: [],
      selectList: [],
      pagination: {
        pageSize: 15,
        pageNum: 1
      },
      pageTotal: 0,
      typeList: []
    }
  },
  mounted() {
    this.getTableData()
    this.getTypeList()
  },
  methods: {
    getTypeList() {
      this.$api.getTypeList({}).then((res) => {
        if (res.code === '200') {
          this.typeList = res.data
        }
      })
    },
    getRowKeys(row) {
      return row.id
    },
    getTableData() {
      this.tableLoading = true
      let data = {
        ...this.pagination,
        ...this.filterInfo
      }
      this.$api
        .searchTerminalList(data)
        .then((res) => {
          this.tableLoading = false
          if (res.code == 200) {
            this.tableData = res.data ? res.data.records : []
            this.pageTotal = res.data ? res.data.total : 0
            this.tableData.forEach(item => {
              if (item.usrType === 1) {
                this.$refs.sinoTable.toggleRowSelection(item, true)
              }
            })
          } else if (res.message) {
            this.tableData = []
            this.pageTotal = 0
            this.$message.error(res.message)
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    search() {
      this.pagination.pageNum = 1
      this.$refs.sinoTable.clearSelection()
      this.getTableData()
    },
    selectEnable(row, rowIndex) { // 复选框可选情况
      if (row.usrType === 1) { // 禁用
        return false
      } else {
        return true
      }
    },
    reset() {
      this.filterInfo = {
        usrName: '',
        usrType: ''
      }
      this.pagination.pageNum = 1
      this.pageTotal = 0
      this.$refs.sinoTable.clearSelection()
      this.getTableData()
    },
    handleSelectionChange(val) {
      let arr = val
      this.multipleSelection = Array.from(new Set(arr.map(item => JSON.stringify(item)))).map(item => JSON.parse(item))
      this.selectList = JSON.parse(JSON.stringify(this.multipleSelection))
    },
    newArrFn(arr) {
      return arr.filter((item, index) => {
        return arr.indexOf(item) === index
      })
    },
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.pagination.pageNum = 1
      this.getTableData()
    },
    handleCurrentChange(val) {
      this.pagination.pageNum = val
      this.getTableData()
    },
    deleteItem(item) {
      if (item.usrType !== 1) {
        this.$refs.sinoTable.toggleRowSelection(
          this.tableData.find((ite) => ite.id == item.id),
          false
        )
      }
    },
    deleteAll() {
      this.multipleSelection.forEach(item => {
        if (item.usrType !== 1) {
          this.$refs.sinoTable.toggleRowSelection(item, false)
        }
      })
    },
    closeDialog() {
      this.$emit('closeTerminalDialog')
    },
    submitDialog() {
      this.$emit('submitTerminalDialog', this.selectList)
    }
  }
}
</script>
<style lang="scss" scoped>
.model-dialog {
  .content {
    margin: 0 auto;
    background: #fff;
    border-radius: 4px;
    width: 100%;
    height: 100%;
    padding: 0 10px;
  }

  .change_top {
    font-size: 14px;
    margin: 8px 0;
    display: flex;
    justify-content: space-between;
    .change_top_left {
      width: 95%;
    }
    .change_top_right {
      flex: 1;
      text-align: right;
    }
    .change_num {
      color: #7f848c;
      margin-right: 16px;
    }
    .change_operate {
      color: #3562db;
      cursor: pointer;
    }
    .item {
      display: inline-block;
      height: 30px;
      padding: 0 10px;
      line-height: 30px;
      border-radius: 6px;
      text-align: center;
      color: #3562db;
      margin-right: 16px;
      margin-bottom: 8px;
      background: rgba(53, 98, 219, 0.2);
    }
  }
}
.searchForm {
  display: flex;
  padding: 10px 0;
  align-items: center;
  width: 100%;
  .el-input {
    width: 200px;
  }
}
.ml-16 {
  margin-left: 16px;
}
.pagination {
  margin-top: 10px;
}
</style>

