<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="content_box">
        <el-form ref="formInline" class="dialog-form" :model="formInline" :inline="true" :rules="rules" label-position="right" label-width="110px">
          <el-form-item label="成员名称" prop="planName">
            <template>
              <div v-if="saveType == 'add'" class="person_div el-input__inner" style="cursor: pointer" @click="addPersonShow">
                <span v-if="workerJsonStr.length == 0" style="color: #dcdfe3">请选择成员</span>
                <span v-else>{{ staffNameArr.join(',') }}</span>
              </div>
              <span v-else>{{ name }}</span>
            </template>
          </el-form-item>
          <br />
          <el-form-item label="部门权限" prop="positionType">
            <div v-if="!disabledFlom">
              <el-radio v-model="formInline.positionType" label="1">组长</el-radio>
              <el-radio v-model="formInline.positionType" label="2">组员</el-radio>
              <el-radio v-model="formInline.positionType" label="3">数据管理员</el-radio>
            </div>
            <span v-else>{{ formInline.positionType == '1' ? '组长' : formInline.positionType == '2' ? '组员' : '数据管理员' }}</span>
          </el-form-item>
          <br />
          <el-form-item label="所属部门" prop="controlTeamId">
            <el-cascader
              v-if="!disabledFlom"
              ref="cascaderAddr"
              v-model="formInline.controlTeamId"
              :show-all-levels="false"
              style="width: 530px"
              placeholder="请选择所属部门"
              :options="treeData"
              filterable
              label="teamName"
              value="id"
              :props="{
                emitPath: false,
                label: 'teamName',
                value: 'id',
                multiple: true,
                checkStrictly: true
              }"
              @change="controlTeamChange"
            ></el-cascader>
            <span v-else>{{ formInline.controlTeamName }}</span>
          </el-form-item>
          <br />
          <el-form-item label="平台登录角色" prop="webRoleCode">
            <el-select v-if="!disabledFlom" v-model="formInline.webRoleCode" placeholder="请选择平台登录角色" style="width: 530px">
              <el-option v-for="item in RoleList" :key="item.id" :label="item.roleName" :value="item.id"></el-option>
            </el-select>
            <span v-for="item in RoleList" v-else :key="item.id">{{ formInline.webRoleCode == item.id ? item.roleName : '' }}</span>
          </el-form-item>
        </el-form>
      </div>
      <addPerson :dialogVisible="dialogVisible" @closeDialog="closeDialog" @sure="sure"></addPerson>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">关闭</el-button>
      <el-button v-if="!disabledFlom" type="primary" @click="complete()">保存</el-button>
    </div>
  </PageContainer>
</template>

<script>
import axios from 'axios'
import { transData } from '@/util'
import addPerson from '../securityControl/components/addperson.vue'
export default {
  name: 'addControl',
  components: { addPerson },
  beforeRouteEnter(to, from, next) {
    if (to.meta.title) {
      const typeList = {
        add: '新增人员',
        edit: '编辑人员',
        check: '人员详情'
      }
      to.meta.title = typeList[to.query.type] ?? '人员详情'
    }
    next()
  },
  data() {
    return {
      disabledFlom: false,
      saveType: '',
      dialogVisible: false,
      loading: false,
      title: '新增人员',
      formInline: {
        planName: '',
        controlTeamId: [],
        controlTeamName: '',
        webRoleCode: '',
        positionType: '2'
      },
      name: '',
      RoleList: [
        {
          name: '1部门',
          id: 1
        }
      ],
      treeData: [],
      rules: {
        // planName: [{ required: true, message: "请选择成员", trigger: "blur" }],
        controlTeamId: [{ required: true, message: '请选择所属部门', trigger: 'change' }],
        webRoleCode: [{ required: true, message: '请选择平台登录角色', trigger: 'change' }]
      },
      workerJsonStr: [],
      staffNameArr: [],
      staffIdArr: []
    }
  },
  created() {
    this.init()
    this.saveType = this.$route.query.type
    if (this.$route.query.tableCode && this.$route.query.tableCode.id != '#') {
      this.formInline.controlTeamId = this.$route.query.tableCode.id
      this.formInline.controlTeamName = this.$route.query.tableCode.teamName
    }
    this.saveType != 'add' ? this.getControlTeamUserInfo() : ''
    this.disabledFlom = this.saveType == 'check'
  },
  methods: {
    // 获取详情
    getControlTeamUserInfo() {
      this.$api
        .ipsmGetControlTeamUserInfo({
          id: this.$route.query.id
        })
        .then((res) => {
          if (res.data.allContent) {
            this.formInline = JSON.parse(res.data.allContent)
          } else {
            this.formInline = res.data
            this.formInline.positionType = res.data.positionType.toString()
          }
          this.name = res.data.name
          this.formInline.controlTeamId = this.formInline.controlTeamId.split(',')
        })
    },
    // 点击确定
    complete() {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          let list = this.$refs.cascaderAddr.getCheckedNodes()
          this.formInline.controlTeamName = list
            .map((item) => {
              return item.label
            })
            .join(',')
          this.formInline.controlTeamId = list
            .map((item) => {
              return item.data.id
            })
            .join(',')
          let data = {
            ...this.formInline,
            // controlTeamId:this.formInline.controlTeamId[this.formInline.controlTeamId.length-1],
            workerJsonStr: JSON.stringify(this.workerJsonStr),
            ids: this.saveType == 'edit' ? this.$route.query.id : '',
            allContent: JSON.stringify(this.formInline)
          }
          console.log(data)
          // return
          let url = this.saveType == 'add' ? 'ipsmAddControlTeamUser' : 'ipsmUpdateControlTeamUser'
          this.$api[url](data).then((res) => {
            if (res.code == 200) {
              this.$message.success(res.message)
              this.$router.go(-1)
            } else {
              this.$message.error(res.message)
            }
          })
        }
      })
    },
    init() {
      // 所属部门
      this.$api.ipsmGetControlGroupInfoList({}).then((res) => {
        this.treeLoading = false
        this.treeData = transData(res.data.list, 'id', 'parentId', 'children')
      })
      // 角色列表
      this.$api.ipsmUserRoleGetRoleList({}).then((res) => {
        this.RoleList = res.data
      })
    },
    // 选择角色
    addPersonShow() {
      this.dialogVisible = true
    },
    sure(list) {
      this.workerJsonStr = []
      list.forEach((item, i) => {
        this.workerJsonStr.push({
          staffId: item.id,
          name: item.name,
          phone: item.mobile,
          gender: item.sex,
          userType: item.type,
          userName: item.userName
        })
      })
      this.staffIdArr = list.map((o) => {
        return o.id
      })
      this.staffNameArr = list.map((o) => {
        return o.name
      })
      this.dialogVisible = false
    },
    closeDialog() {
      this.dialogVisible = false
    },
    tabelDelRow(index) {
      this.workerJsonStr.splice(index, 1)
    },
    controlTeamChange(val) {
      // this.$nextTick(() => {
      //   console.log(this.$refs.cascaderAddr.setCheckedNodes);
      //   let list = this.$refs.cascaderAddr.getCheckedNodes();
      //   this.formInline.controlTeamName = list.map((item) => {
      //     return item.label;
      //   });
      //   this.formInline.controlTeamId = list.map((item) => {
      //     return item.data.id;
      //   });
      //   console.log(this.formInline.controlTeamName);
      //   console.log(this.formInline.controlTeamId);
      //   // this.formInline.controlTeamName = list[0].label;
      //   // this.formInline.controlTeamId = list[0].data.id;
      // });
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-textarea .el-input__count {
  line-height: normal;
}

.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
  padding: 10px;
  overflow-y: auto;
}

::v-deep .el-textarea__inner {
  min-height: 120px !important;
}

.content_box {
  height: calc(100% - 30px);
  margin-top: 10px;
  padding: 20px 25px 25px;
  background: #fff;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.form-inline {
  .el-input,
  .el-select,
  .el-cascader {
    width: 300px;
  }
}

.el-textarea {
  width: 400px;
}

.person_div {
  width: 230px;
  height: 32px;
  line-height: 32px;
  //   min-width: 230px;
  //   max-width: 230px;
  //   overflow-y: auto;
  //   overflow-x: hidden;
  //   display: inline-block;
  //   border-radius: 4px;
  //   vertical-align: top;
  //   padding-right: 20px;
  //   box-sizing: border-box;
  //   min-height: 42px;
  //   max-height: calc(42 * 3);
}
</style>
