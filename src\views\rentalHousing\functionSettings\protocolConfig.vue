<template>
  <div class="container-content">
    <div class="search-content">
      <div class="search-left">
        <el-input v-model="agreementValue" maxlength="50" placeholder="协议名称、标题、内容"></el-input>
        <!-- <el-select v-model="status" placeholder="全部状态">
          <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
        </el-select> -->
      </div>
      <div class="search-right">
        <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db; margin-left: 20px" @click="handleReset">重置</el-button>
        <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
      </div>
    </div>
    <div class="btn-box">
      <!-- <el-button type="danger" size="small" :disabled="checkStopStatus(multipleSelection)" @click="handleStopUsing()">停用</el-button> -->
    </div>
    <div class="table-container">
      <div class="table-box">
        <el-table
          ref="materialTable"
          v-loading="tableLoading"
          :data="tableData"
          height="100%"
          border
          :header-cell-style="{ background: '#F6F5FA' }"
          style="width: 100%"
          :cell-style="{ padding: '8px 0 8px 0' }"
          stripe
          highlight-current-row
          :empty-text="emptyText"
          row-key="id"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55"> </el-table-column>
          <el-table-column show-overflow-tooltip prop="agreementName" label="协议名称"></el-table-column>
          <el-table-column show-overflow-tooltip prop="agreementCode" label="编码"></el-table-column>
          <el-table-column show-overflow-tooltip prop="agreementTitle" label="协议标题"></el-table-column>
          <el-table-column prop="agreementContent" show-overflow-tooltip label="协议内容"> </el-table-column>
          <el-table-column prop="agreementStatus" show-overflow-tooltip label="状态">
            <template slot-scope="scope">
              <span :style="{ color: scope.row.agreementStatus == '0' ? '#FA403C' : '#67C23A' }">{{ scope.row.agreementStatus | statusFilter }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="135">
            <template slot-scope="scope">
              <div style="display: flex; gap: 10px">
                <el-link type="primary" :underline="false" @click="operation(scope.row, 'details')"> 查看 </el-link>
                <el-link type="primary" :underline="false" @click="operation(scope.row, 'edit')"> 编辑 </el-link>
                <!-- <el-link type="danger" :underline="false" size="small" :disabled="scope.row.agreementStatus == '0'" @click="operation(scope.row, 'stop')"> 停用 </el-link> -->
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="table-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="paginationData.currentPage"
          :page-sizes="[15, 30, 50, 100]"
          :page-size="paginationData.pageSize"
          :total="paginationData.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
    <!-- 协议编辑弹窗 -->
    <div class="agreementDialog">
      <el-dialog title="协议配置" :visible.sync="dialogVisible" top="10vh" width="40%" :close-on-click-modal="false" :before-close="handleCloseDialog">
        <div class="dialog-content">
          <div class="dialog-box">
            <div class="a-title">阅读校验</div>
            <div class="checkbox">
              <el-checkbox v-model="dialogForm.requireTimeChecked" :disabled="dialogStatus == 'details'">
                <span style="margin-right: 5px">最少阅读时间要求</span>
                <el-input v-model="dialogForm.time" size="mini" style="width: 60px" :disabled="dialogStatus == 'details'" @keyup.native="proving"></el-input>
                <span style="margin-left: 5px">秒</span>
              </el-checkbox>
            </div>
            <div class="checkbox">
              <el-checkbox v-model="dialogForm.timeChecked" :disabled="dialogStatus == 'details' || !dialogForm.requireTimeChecked">滚动到最低端后开始计时</el-checkbox>
            </div>
          </div>
          <div class="dialog-box">
            <div class="a-title">协议内容</div>
            <div class="content">
              <span>协议标题</span>
              <el-input v-model="dialogForm.agreementTitle" :disabled="dialogStatus == 'details'" maxlength="50" placeholder="免责协议"></el-input>
            </div>
            <div class="content">
              <span>协议标题</span>
              <div class="div-editor">
                <Editor ref="myTextEditor" v-model="dialogForm.editorContent" class="my-editor"></Editor>
              </div>
            </div>
          </div>
        </div>
        <span slot="footer" class="dialog-footer" v-if="dialogStatus != 'details'">
          <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db" @click="handleCloseDialog">取消</el-button>
          <el-button :loading="saveBtnLoding" :disabled="saveBtnLoding" type="primary" @click="saveDialog">确认</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import Editor from '@/components/quillEditor'
export default {
  data() {
    return {
      tableLoading: false,
      dialogVisible: false,
      saveBtnLoding: false,
      emptyText: '暂无数据',
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 10
      },
      statusOptions: [
        {
          value: '1',
          label: '启用'
        },
        {
          value: '0',
          label: '禁用'
        }
      ],
      status: '',
      agreementValue: '',
      tableData: [],
      multipleSelection: [],
      dialogForm: {
        editorContent: '',
        time: 10,
        agreementTitle: '',
        requireTimeChecked: false,
        timeChecked: false,
        id: ''
      },
      dialogStatus: ''
    }
  },
  components: {
    Editor
  },
  filters: {
    statusFilter(type) {
      if (!type) return ''
      return type == 1 ? '启用' : '禁用'
    }
  },
  mounted() {
    this.getListData()
  },
  methods: {
    /** 校验多条数据停用 */
    checkStopStatus(arr) {
      if (arr && !arr.length) {
        return true
      }
      return arr.some((item) => item.fieldStatus == '0')
    },
    /** 重置 */
    handleReset() {
      this.paginationData.currentPage = 1
      this.status = ''
      this.agreementValue = ''
      this.getListData()
    },
    /** 搜索 */
    handleSearch() {
      this.paginationData.currentPage = 1
      this.getListData()
    },
    /** 停用 */
    handleStopUsing(row) {
      this.$confirm('是否停用该协议?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let params = {
          idList: row ? [row.id] : this.multipleSelection.map((item) => item.id),
          state: '0'
        }
        this.$api.rentalHousingApi.stopAgreement(params).then((res) => {
          if (res.code == 200) {
            this.$message.success(res.message)
            this.getListData()
          } else {
            this.$message.error(res.message)
          }
        })
      })
    },
    /** 弹窗保存 */
    saveDialog() {
      const userInfo = this.$store.state.user.userInfo.user
      let params = {
        id: this.dialogForm.id || '',
        minReadTime: this.dialogForm.requireTimeChecked ? this.dialogForm.time : '',
        minReadStatus: this.dialogForm.requireTimeChecked ? '1' : '0',
        scrollTiming: this.dialogForm.timeChecked ? '是' : '否',
        agreementTitle: this.dialogForm.agreementTitle,
        agreementContent: this.dialogForm.editorContent,
        userId: userInfo.id,
        userName: userInfo.staffName
      }
      this.saveBtnLoding = true
      this.$api.rentalHousingApi.updateAgreement(params).then((res) => {
        this.saveBtnLoding = false
        if (res.code == 200) {
          this.$message.success(res.message)
          this.handleCloseDialog()
          this.getListData()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    /** 操作 */
    operation(row, type) {
      if (type == 'details' || type == 'edit') {
        this.dialogVisible = true
        this.dialogForm.editorContent = row.agreementContent || ''
        this.dialogForm.id = row.id || ''
        this.dialogForm.agreementTitle = row.agreementTitle || ''
        this.dialogForm.time = row.minReadTime || 10
        this.dialogForm.timeChecked = row.scrollTiming === '是' ? true : false
        this.dialogForm.requireTimeChecked = row.minReadStatus == '1' ? true : false
        this.$nextTick(() => {
          this.$refs.myTextEditor.content = this.dialogForm.editorContent
          const toolbar = this.$refs.myTextEditor.$refs.myQuillEditor.quill.getModule('toolbar')
          if (type == 'details') {
            if (toolbar) {
              toolbar.container.style.display = 'none'
            }
            this.$refs.myTextEditor.$refs.myQuillEditor.quill.enable(false)
          } else {
            this.$refs.myTextEditor.$refs.myQuillEditor.quill.enable(true)
            if (toolbar) {
              toolbar.container.style.display = 'block'
            }
          }
        })
      }
      if (type == 'stop') this.handleStopUsing(row)
      this.dialogStatus = type
    },
    /** 获取列表数据 */
    getListData() {
      let params = {
        agreementNameTitleContent: this.agreementValue,
        agreementStatus: this.status,
        pageNum: this.paginationData.currentPage,
        pageSize: this.paginationData.pageSize
      }
      this.tableLoading = true
      this.$api.rentalHousingApi.getAgreementList(params).then((res) => {
        this.tableLoading = false
        if (res.code == 200) {
          this.tableData = res.data.records
          this.paginationData.total = res.data.total
        } else {
          this.$message.error(res.message)
        }
      })
    },
    /** 关闭弹窗 */
    handleCloseDialog() {
      this.dialogVisible = false
    },
    /** 多选事件 */
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    /** 条数变化事件 */
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.getListData()
    },
    /** 页数变化事件 */
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.getListData()
    },
    /** 输入框输入限制 */
    proving(e) {
      if (e.target.value && e.target.value.length == 1) {
        e.target.value = e.target.value.toString().replace(/[^1-9]/g, '') //只能输入正整数.replace(/[^1-9]/g, '')
      } else {
        e.target.value = e.target.value.toString().replace(/[^0-9]/g, '') //只能输入正整数.replace(/[^1-9]/g, '')
      }
      if (e.target.value.indexOf('.') < 0 && e.target.value != '') {
        //输入替换，如输入05，直接替换为5，防止出现01，02这种情况
        e.target.value = parseInt(e.target.value)
      }
    }
  }
}
</script>
  <style lang="scss" scoped>
.container-content {
  height: 100%;
  overflow-y: auto;
  .search-content {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    .search-left {
      display: flex;
      .el-input {
        margin-right: 10px;
      }
    }
  }
  .table-container {
    margin-top: 20px;
    height: calc(100% - 110px);
    .table-box {
      height: 100%;
    }
    .table-pagination {
      margin-top: 10px;
      text-align: right;
    }
  }
}
.dialog-box {
  .checkbox {
    padding-left: 20px;
    margin-bottom: 10px;
  }
  .content {
    padding-left: 20px;
    display: flex;
    margin-bottom: 10px;
    span {
      flex-shrink: 0;
      margin-right: 10px;
    }
    .div-editor {
      flex: 1;
      height: 360px;
    }
  }
}
.content-textarea {
  width: 100%;
  height: 360px;
}
::v-deep .el-textarea__inner {
  height: 360px;
}
.a-title {
  font-size: 15px;
  font-weight: bolder;
  color: #333;
  padding-left: 15px;
  margin-bottom: 15px;
  position: relative;
}
.a-title::after {
  content: '';
  width: 8px;
  height: 16px;
  border-radius: 0 8px 8px 0;
  background: #4387f7;
  position: absolute;
  left: 0px;
  top: 50%;
  transform: translateY(-50%);
}
</style>