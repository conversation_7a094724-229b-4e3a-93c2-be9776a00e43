<template>
  <div>
    <el-dialog
      v-if="dialogVisible"
      v-dialogDrag
      :modal="false"
      :close-on-click-modal="false"
      title="配置空间类型"
      width="30%"
      :visible.sync="dialogVisible"
      custom-class="model-dialog"
      :before-close="closeDialog"
    >
      <div class="dialog-content">
        <el-form ref="formInline" :model="queryForm" inline class="form-inline" :rules="rules" label-width="120px">
          <el-form-item label="空间类型：" prop="spaceType">
            <el-select v-model="queryForm.spaceType" placeholder="请选择空间类型">
              <el-option v-for="item in spaceTypeDict" :key="item.id" :label="item.name" :value="item.code" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" plain @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="submitFormData">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: 'spaceTypeDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    selectItems: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dialogVisible: this.visible,
      queryForm: {
        spaceType: ''
      },
      rules: {
        spaceType: { required: true, message: '请选择空间类型', trigger: 'change' }
      },
      spaceTypeDict: []
    }
  },
  computed: {},
  mounted() {
    this.getSpaceTypeDict()
  },
  methods: {
    getSpaceTypeDict() {
      this.$api.GetSpaceTypeDict().then((res) => {
        if (res.code == 200) {
          this.spaceTypeDict = res.data
        }
      })
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', false)
      this.$refs['formInline'].resetFields()
    },
    submitFormData() {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          let params = this.selectItems.map((item) => {
            return {
              ...item,
              paceTypeCode: this.queryForm.spaceType,
              paceTypeName: this.spaceTypeDict.find((v) => v.code == this.queryForm.spaceType).name
            }
          })
          this.$api.SpaceConfigBatch(params).then((res) => {
            if (res.code == 200) {
              this.$message.success('配置成功')
              this.closeDialog()
            } else {
              this.$message.error(res.msg)
            }
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  background: #fff;
  padding: 10px;
  max-height: 500px;
  overflow-y: auto;
  ::v-deep(.form-inline) {
    margin-top: 24px;
    .el-input {
      width: 260px;
    }
  }
}
</style>
