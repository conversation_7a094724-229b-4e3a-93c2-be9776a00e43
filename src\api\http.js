// 导入封装好的axios实例
import request from './request'
import qs from 'qs'
import store from '@/store/index'
import router from '@/router/index'
/**
 * methods: 请求
 * @param url 请求地址
 * @param params 请求参数
 */
function getBaseInfo() {
  let getBaseInfo = sessionStorage.getItem('routeInfo') ? JSON.parse(sessionStorage.getItem('routeInfo')) : ''
  return getBaseInfo
}
function getUserCode() {
  let userCode = {}
  if (store.getters['user/isLogin']) {
    const userInfo = store.state.user.userInfo.user
    userCode = {
      hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
      unitCode: userInfo.unitCode ?? 'BJSYGJ'
    }
  }
  return userCode
}
export function getRequest(url, http = __PATH.VUE_SPACE_API, contentType = 'json') {
  return function (params, headers = {}, newHttp = http) {
    let userCode = getUserCode()
    const config = {
      method: 'get',
      url: newHttp + url,
      headers: {
        'Content-Type': contentType == 'json' ? 'application/json' : 'application/x-www-form-urlencoded;charset=utf8',
        ...headers,
        ...userCode
      }
    }
    config.params = { ...params, ...userCode }
    return request(config)
  }
}
export function getQueryQS(url, http = __PATH.VUE_SPACE_API, contentType = 'json') {
  return function (params, headers = {}, newHttp = http) {
    let userCode = {}
    if (store.getters['user/isLogin']) {
      const userInfo = store.state.user.userInfo.user
      userCode = {
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
    }
    const config = {
      method: 'get',
      url: newHttp + url + '?' + qs.stringify({ ...userCode, ...params }),
      headers: {
        'Content-Type': contentType == 'json' ? 'application/json' : 'application/x-www-form-urlencoded;charset=utf8',
        ...headers,
        ...userCode
      }
    }
    return request(config)
  }
}
export function postRequest(url, http = __PATH.VUE_SPACE_API, contentType = 'json') {
  return function (params = {}, headers = {}, newHttp = http) {
    let userCode = {}
    if (store.getters['user/isLogin']) {
      const userInfo = store.state.user.userInfo.user
      userCode = {
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
    }
    const config = {
      method: 'post',
      url: newHttp + url,
      headers: {
        'Content-Type': contentType == 'json' ? 'application/json' : 'application/x-www-form-urlencoded;charset=utf8',
        ...headers,
        ...userCode
      }
    }
    config.data = { ...params, ...userCode }
    return request(config)
  }
}
export function postQueryQS(url, http = __PATH.VUE_SPACE_API, contentType = 'form') {
  return function (params, headers = {}, newHttp = http) {
    let userCode = {}
    if (store.getters['user/isLogin']) {
      const userInfo = store.state.user.userInfo.user
      userCode = {
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
    }
    const config = {
      method: 'post',
      url: newHttp + url + '?' + qs.stringify({ ...params, ...userCode }),
      headers: {
        'Content-Type': contentType == 'json' ? 'application/json' : 'application/x-www-form-urlencoded;charset=utf8',
        ...headers,
        ...userCode
      }
    }
    // if (params) config.data = params
    return request(config)
  }
}
export function postQuarters(url, http = __PATH.VUE_SPACE_API, contentType = 'json') {
  return function (params, headers = {}) {
    let userCode = {}
    if (store.getters['user/isLogin']) {
      const userInfo = store.state.user.userInfo.user
      userCode = {
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
    }
    const config = {
      method: 'post',
      url: http + url,
      headers: {
        'Content-Type': contentType == 'json' ? 'application/json' : 'application/x-www-form-urlencoded;charset=utf8',
        ...headers,
        ...userCode
      }
    }
    config.data = { ...params, ...userCode }
    return request(config)
  }
}
export function postParamsQS(url, http = __PATH.VUE_IOMS_API, contentType = 'form') {
  return function (params, headers = {}) {
    let userCode = {}
    if (store.getters['user/isLogin']) {
      const userInfo = store.state.user.userInfo.user
      userCode = {
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
    }
    const config = {
      method: 'post',
      url: http + url,
      headers: {
        'Content-Type': contentType == 'json' ? 'application/json' : 'application/x-www-form-urlencoded;charset=utf8',
        ...headers,
        ...userCode
      }
    }
    config.data = qs.stringify({ ...params, ...userCode })
    return request(config)
  }
}
export function postParamsDR(url, http = __PATH.VUE_SYS_API, contentType = 'json') {
  return function (params, headers = {}) {
    let userCode = {}
    if (store.getters['user/isLogin']) {
      const userInfo = store.state.user.userInfo.user
      userCode = {
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
    }
    const config = {
      method: 'post',
      url: http + url,
      headers: {
        'Content-Type': contentType == 'json' ? 'application/json;charset=UTF-8' : 'application/x-www-form-urlencoded;charset=utf8',
        ...headers,
        ...userCode
      }
    }
    config.data = { ...params, ...userCode }
    return request(config)
  }
}
export function postFormData(url, http = __PATH.VUE_SYS_API, contentType = 'json') {
  return function (params, configObj = {}, newHttp = http) {
    let userCode = {}
    if (store.getters['user/isLogin']) {
      const userInfo = store.state.user.userInfo.user
      userCode = {
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
      Object.assign(params, userCode)
    }
    let formdata = new FormData()
    for (var item in params) {
      formdata.append(item, params[item] || '')
    }
    const config = {
      method: 'post',
      url: newHttp + url,
      headers: {
        'Content-Type': contentType == 'json' ? 'application/json;charset=UTF-8' : 'application/x-www-form-urlencoded;charset=utf8',
        ...(configObj.headers || configObj),
        ...userCode
      },
      ...(configObj.config || {}),
      data: formdata
    }
    return request(config)
  }
}
export function postURL(url, contentType = 'json') {
  return function (params = {}, headers = {}) {
    let ipAndProt = JSON.parse(sessionStorage.getItem('iotIp'))
    let userCode = {}
    if (store.getters['user/isLogin']) {
      const userInfo = store.state.user.userInfo.user
      userCode = {
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
    }
    const config = {
      method: 'post',
      url: 'http://' + ipAndProt.serverIp + ':' + ipAndProt.serverPort + '/' + url,
      headers: {
        'Content-Type': contentType == 'json' ? 'application/json' : 'application/x-www-form-urlencoded;charset=utf8',
        ...headers,
        ...userCode
      }
    }
    config.data = { ...params, ...userCode }
    return request(config)
  }
}
export function websocketService(url, http = __PATH.WS_URL) {
  return function (id) {
    var websocket = new WebSocket(http + url + id)
    return websocket
  }
}
export function postFile(url, http = __PATH.VUE_IOMS_API, contentType = 'json') {
  return function (params = {}, headers = {}) {
    const config = {
      method: 'post',
      url: http + url,
      headers: {
        'Content-Type': contentType == 'json' ? 'application/json' : 'application/x-www-form-urlencoded;charset=utf8'
      }
    }
    config.data = params
    return request(config)
  }
}
export function downFile(url, http = __PATH.VUE_SPACE_API, contentType = 'json') {
  return function (params = {}) {
    let userCode = {}
    if (store.getters['user/isLogin']) {
      const userInfo = store.state.user.userInfo.user
      userCode = {
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
    }
    const config = {
      method: 'post',
      url: http + url,
      responseType: 'blob',
      headers: {
        'Content-Type': contentType == 'json' ? 'application/json;charset=utf-8' : 'application/x-www-form-urlencoded;charset=utf-8',
        ...userCode
      }
    }
    config.data = { ...params, ...userCode }
    return request(config)
  }
}
export function postRequestCS(url, http = __PATH.VUE_SPACE_API, contentType = 'json') {
  return function (params = {}, headers = {}) {
    let userCode = {}
    if (store.getters['user/isLogin']) {
      const userInfo = store.state.user.userInfo.user
      userCode = {
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
    }
    const config = {
      method: 'post',
      url: http + url,
      headers: {
        'Content-Type': contentType == 'json' ? 'application/json' : 'application/x-www-form-urlencoded;charset=utf8',
        ...headers
      }
    }
    config.data = JSON.parse(params)
    return request(config)
  }
}
export function postParamsQueryString(url, http = __PATH.VUE_ICIS_API, contentType = 'form') {
  return function (params, headers = {}) {
    let userCode = {}
    if (store.getters['user/isLogin']) {
      const userInfo = store.state.user.userInfo.user
      userCode = {
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ',
        hospitalName: '',
        userId: userInfo.staffId,
        staffId: userInfo.staffId,
        roleCode: ''
      }
      params.staffName = userInfo.staffName
      params.userName = userInfo.staffName
    }
    const config = {
      method: 'post',
      url: http + url,
      headers: {
        'Content-Type': contentType == 'json' ? 'application/json' : 'application/x-www-form-urlencoded;charset=utf8',
        ...headers,
        ...userCode
      }
    }
    config.data = qs.stringify({ ...params, ...userCode })
    return request(config)
  }
}
export function put(url, http = __PATH.VUE_SPACE_API, contentType = 'json') {
  return function (params = {}, headers = {}) {
    let userCode = {}
    if (store.getters['user/isLogin']) {
      const userInfo = store.state.user.userInfo.user
      userCode = {
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
    }
    const config = {
      method: 'put',
      url: http + url,
      headers: {
        'Content-Type': contentType == 'json' ? 'application/json' : 'application/x-www-form-urlencoded;charset=utf8',
        ...headers,
        ...userCode
      }
    }
    config.data = { ...params, ...userCode }
    return request(config)
  }
}
export function deleteFn(url, http = __PATH.VUE_SPACE_API, contentType = 'form') {
  return function (params, headers = {}) {
    let userCode = {}
    if (store.getters['user/isLogin']) {
      const userInfo = store.state.user.userInfo.user
      userCode = {
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
    }
    const config = {
      method: 'delete',
      url: http + url,
      headers: {
        'Content-Type': contentType == 'json' ? 'application/json' : 'application/x-www-form-urlencoded;charset=utf8',
        ...headers,
        ...userCode
      }
    }
    config.data = qs.stringify({ ...params, ...userCode })
    return request(config)
  }
}
export function postQuartersSecurity(url, http = __PATH.VUE_AQ_API, contentType = 'json') {
  return function (params, headers = {}) {
    let userCode = {}
    if (store.getters['user/isLogin']) {
      const userInfo = JSON.parse(sessionStorage.getItem('LOGINDATA'))
      userCode.unitCode = userInfo.unitCode || 'BJSYGJ' //
      userCode.hospitalCode = userInfo.hospitalCode || 'BJSJTYY'
      userCode.controlGroupIds = userInfo.controlGroupIds
      userCode.hospitalName = userInfo.hospitalName
      userCode.officeId = userInfo.officeId
      userCode.officeName = userInfo.officeName
      userCode.phone = userInfo.phone
      userCode.platformFlag = 1
      userCode.roleCode = userInfo.roleCode
      userCode.unitName = userInfo.unitName
      userCode.userId = userInfo.userId
      userCode.userName = userInfo.name
      userCode.userTeamId = userInfo.controlGroupIds
      userCode.positionType = userInfo.positionType
    }
    const config = {
      method: 'post',
      url: http + url,
      headers: {
        'Content-Type': contentType == 'json' ? 'application/json' : 'application/x-www-form-urlencoded;charset=utf8',
        ...headers
      }
    }
    config.data = { ...userCode, ...params }
    return request(config)
  }
}
export function downFileAQ(url, http = __PATH.VUE_AQ_API, contentType = 'json') {
  return function (params = {}) {
    let userCode = {}
    if (store.getters['user/isLogin']) {
      const userInfo = JSON.parse(sessionStorage.getItem('LOGINDATA'))
      userCode.unitCode = userInfo.unitCode || 'BJSYGJ'
      userCode.hospitalCode = userInfo.hospitalCode || 'BJSJTYY'
      userCode.controlGroupIds = userInfo.controlGroupIds
      userCode.hospitalName = userInfo.hospitalName
      userCode.officeId = userInfo.officeId
      userCode.officeName = userInfo.officeName
      userCode.platformFlag = 1
      userCode.roleCode = userInfo.roleCode
      userCode.unitName = userInfo.unitName
      userCode.userId = userInfo.userId
      userCode.userName = userInfo.name
      userCode.userTeamId = userInfo.controlGroupIds
      userCode.positionType = userInfo.positionType
    }
    const config = {
      method: 'post',
      url: http + url,
      responseType: 'blob',
      headers: {
        'Content-Type': contentType == 'json' ? 'application/json;charset=utf-8' : 'application/x-www-form-urlencoded;charset=utf-8'
        // ...userCode
      }
    }
    config.data = { ...params, ...userCode }
    return request(config)
  }
}
export function postParamsQSSecurity(url, http = __PATH.VUE_AQ_API, contentType = 'form') {
  return function (params, headers = {}) {
    let userCode = {}
    if (store.getters['user/isLogin']) {
      const userInfo = JSON.parse(sessionStorage.getItem('LOGINDATA'))
      userCode.unitCode = userInfo.unitCode || 'BJSYGJ'
      userCode.hospitalCode = userInfo.hospitalCode || 'BJSJTYY'
      userCode.controlGroupIds = userInfo.controlGroupIds
      userCode.hospitalName = userInfo.hospitalName
      userCode.platformFlag = 1
      userCode.roleCode = userInfo.roleCode
      userCode.unitName = userInfo.unitName
      userCode.userId = userInfo.userId
      userCode.userName = userInfo.name
      userCode.userTeamId = userInfo.controlGroupIds
      userCode.positionType = userInfo.positionType
    }
    const config = {
      method: 'post',
      url: http + url,
      headers: {
        'Content-Type': contentType == 'json' ? 'application/json' : 'application/x-www-form-urlencoded;charset=utf8',
        ...headers
        // ...userCode
      }
    }
    config.data = qs.stringify({ ...userCode, ...params })
    return request(config)
  }
}
export function getSecurity(url, http = __PATH.VUE_AQ_API, contentType = 'json') {
  return function (params, headers = {}) {
    let userCode = {}
    if (store.getters['user/isLogin']) {
      // const userInfo = store.state.user.userInfo.user
      const userInfo = JSON.parse(sessionStorage.getItem('LOGINDATA'))
      userCode.unitCode = userInfo.unitCode || 'BJSYGJ'
      userCode.hospitalCode = userInfo.hospitalCode || 'BJSJTYY'
      userCode.controlGroupIds = userInfo.controlGroupIds
      userCode.hospitalName = userInfo.hospitalName
      userCode.platformFlag = 1
      userCode.unitName = userInfo.unitName
      userCode.userId = userInfo.userId
      userCode.userName = userInfo.name
      userCode.positionType = userInfo.positionType
    }
    const config = {
      method: 'get',
      url: http + url,
      // responseType: 'blob',
      headers: {
        'Content-Type': contentType == 'json' ? 'application/json' : 'application/x-www-form-urlencoded;charset=utf8',
        ...headers
      }
    }
    config.params = { ...userCode, ...params }
    return request(config)
  }
}
export function postParamsSecurity(url, http = __PATH.VUE_AQ_API, contentType = 'form') {
  return function (params, headers = {}) {
    let userCode = {}
    if (store.getters['user/isLogin']) {
      let baseInfo = sessionStorage.getItem('LOGINDATA') ? JSON.parse(sessionStorage.getItem('LOGINDATA')) : ''
      userCode.unitCode = baseInfo.unitCode || 'BJSYGJ'
      userCode.hospitalCode = baseInfo.hospitalCode || 'BJSJTYY'
      userCode.userId = baseInfo.id
      userCode.staffId = baseInfo.id
      userCode.userName = baseInfo.name
      userCode.positionType = baseInfo.positionType
      userCode.roleCode = baseInfo.roleCode
      userCode.userTeamId = baseInfo.controlGroupIds
      userCode.controlGroupIds = baseInfo.controlGroupIds
      userCode.platformFlag = 1
    }
    const config = {
      method: 'post',
      url: http + url,
      headers: {
        'Content-Type': contentType == 'json' ? 'application/json' : 'application/x-www-form-urlencoded;charset=utf8',
        ...headers
        // ...userCode
      }
    }
    config.data = qs.stringify({ ...params, ...userCode })
    return request(config)
  }
}
export function postSaveSecurity(url, http = __PATH.VUE_AQ_API, contentType = 'form') {
  return function (params, headers = {}) {
    if (store.getters['user/isLogin']) {
      let baseInfo = sessionStorage.getItem('LOGINDATA') ? JSON.parse(sessionStorage.getItem('LOGINDATA')) : ''
      params.unitCode = baseInfo.unitCode || 'BJSYGJ'
      params.hospitalCode = baseInfo.hospitalCode || 'BJSJTYY'
      params.userTeamId = baseInfo.controlGroupIds
      params.controlGroupIds = baseInfo.controlGroupIds
      params.hospitalName = baseInfo.hospitalName
      params.positionType = baseInfo.positionType
      params.unitName = baseInfo.unitName
      params.userId = baseInfo.id
      params.userName = baseInfo.name
      params.platformFlag = 1
    }
    params.sysFlag = 'idps'
    let formdata = new FormData()
    for (var item in params) {
      if (
        (item == 'card' ||
          item == 'infomation' ||
          item == 'photo' ||
          item == 'file' ||
          item == 'repairAttachment' ||
          item == 'attachment' ||
          item == 'repairAttachmentUrl' ||
          item == 'files' ||
          item == 'attachmentUrl' ||
          item == 'processUrl' ||
          item == 'workAttachment' ||
          item == 'informUrl') &&
        params[item] instanceof Array
      ) {
        if (params[item]) {
          params[item].forEach((res) => {
            formdata.append(item, res)
          })
        } else {
          formdata.append(item, params[item] || '')
        }
      } else {
        formdata.append(item, params[item] || '')
      }
    }
    const config = {
      method: 'post',
      url: http + url,
      data: formdata,
      headers: {
        'Content-Type': contentType == 'json' ? 'application/json' : 'application/x-www-form-urlencoded;charset=utf8',
        ...headers
        // ...userCode
      }
    }
    // config.data = qs.stringify({ ...params, ...userCode })
    return request(config)
  }
}
export function getSecurityType(url, http = __PATH.VUE_AQ_API, contentType = 'json') {
  return function (params, headers = {}) {
    let userCode = {}
    if (store.getters['user/isLogin']) {
      // const userInfo = store.state.user.userInfo.user
      const userInfo = JSON.parse(sessionStorage.getItem('LOGINDATA'))
      userCode.unitCode = userInfo.unitCode || 'BJSYGJ'
      userCode.hospitalCode = userInfo.hospitalCode || 'BJSJTYY'
      userCode.controlGroupIds = userInfo.controlGroupIds
      userCode.hospitalName = userInfo.hospitalName
      userCode.platformFlag = 1
      userCode.unitName = userInfo.unitName
      userCode.userId = userInfo.userId
      userCode.userName = userInfo.name
      userCode.positionType = userInfo.positionType
      userCode.roleCode = userInfo.roleCode
    }
    const config = {
      method: 'get',
      url: http + url,
      responseType: 'blob',
      headers: {
        'Content-Type': contentType == 'json' ? 'application/json' : 'application/x-www-form-urlencoded;charset=utf8',
        ...headers
      }
    }
    config.params = { ...userCode, ...params }
    return request(config)
  }
}
export function getSecurityData(url, http = __PATH.VUE_AQ_API, contentType = 'json') {
  return function (params, headers = {}) {
    let userCode = {}
    if (store.getters['user/isLogin']) {
      // const userInfo = store.state.user.userInfo.user
      const userInfo = JSON.parse(sessionStorage.getItem('LOGINDATA'))
    }
    const config = {
      method: 'get',
      url: http + url,
      headers: {
        'Content-Type': contentType == 'json' ? 'application/json' : 'application/x-www-form-urlencoded;charset=utf8',
        ...headers
      }
    }
    config.params = { ...userCode, ...params }
    return request(config)
  }
}
export function postParamsTY(url, http = __PATH.VUE_AQ_API, contentType = 'form') {
  return function (params, headers = {}) {
    let userCode = {}
    if (store.getters['user/isLogin']) {
      const userInfo = store.state.user.userInfo.user
      userCode = {
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ',
        platformFlag: 1,
        userId: userInfo.staffId,
        userName: userInfo.staffName
      }
    }
    const config = {
      method: 'post',
      url: http + url,
      headers: {
        'Content-Type': contentType == 'json' ? 'application/json' : 'application/x-www-form-urlencoded;charset=utf8',
        ...headers
        // ...userCode
      }
    }
    config.data = qs.stringify({ ...userCode, ...params })
    return request(config)
  }
}
export function postQuartersTY(url, http = __PATH.VUE_AQ_API, contentType = 'json') {
  return function (params, headers = {}) {
    let userCode = {}
    if (store.getters['user/isLogin']) {
      const userInfo = store.state.user.userInfo.user
      userCode = {
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ',
        platformFlag: 1,
        userId: userInfo.staffId,
        userName: userInfo.staffName
      }
      // const userInfo = JSON.parse(sessionStorage.getItem('LOGINDATA'))
      // userCode.unitCode = userInfo.unitCode || 'BJSYGJ' //
      // userCode.hospitalCode = userInfo.hospitalCode || 'BJSJTYY'
      // userCode.controlGroupIds = userInfo.controlGroupIds
      // userCode.hospitalName = userInfo.hospitalName
      // userCode.officeId = userInfo.officeId
      // userCode.officeName = userInfo.officeName
      // userCode.phone = userInfo.phone
      // userCode.platformFlag = 1
      // userCode.roleCode = userInfo.roleCode
      // userCode.unitName = userInfo.unitName
      // userCode.userId = userInfo.userId
      // userCode.userName = userInfo.name
      // userCode.userTeamId = userInfo.controlGroupIds
      // userCode.positionType = userInfo.positionType
    }
    const config = {
      method: 'post',
      url: http + url,
      headers: {
        'Content-Type': contentType == 'json' ? 'application/json' : 'application/x-www-form-urlencoded;charset=utf8',
        ...headers
      }
    }
    config.data = { ...userCode, ...params }
    return request(config)
  }
}
export function post_iemc(url, contentType = 'json') {
  return function (params, http = __PATH.VUE_IEMC_API, query) {
    let userCode = {}
    if (store.getters['user/isLogin']) {
      const userInfo = store.state.user.userInfo.user
      userCode = {
        hospitalCode: userInfo.hospitalCode,
        unitCode: userInfo.unitCode
        // userName: userInfo.staffName,
        // userId: userInfo.staffId
      }
    }
    const config = {
      method: 'post',
      url: http + url + (query ? '?' + qs.stringify(query) : ''),
      headers: {
        'Content-Type': contentType == 'json' ? 'application/json' : 'application/x-www-form-urlencoded;charset=utf8'
        // 'operation-type': header['operation-type'],
        // 'operation-id': header['operation-id'],
        // 'operation-name': header['operation-name']
      }
    }
    config.data = { ...params, ...userCode }
    return request(config)
  }
}
export function post_iemc_notInfo(url, type, contentType = 'json') {
  return function (params, header = {}, http = __PATH.VUE_IEMC_API) {
    let userCode = {}
    if (store.getters['user/isLogin']) {
      const userInfo = store.state.user.userInfo.user
      userCode = {
        userName: userInfo.staffName,
        userId: userInfo.staffId
      }
    }
    const paramData = { ...params, ...userCode }
    const query = type === 'query' ? qs.stringify(paramData) : ''
    const data = type === 'query' ? {} : paramData
    const config = {
      method: 'post',
      url: http + url + (type == 'query' ? '?' + query : ''),
      headers: {
        'Content-Type': contentType == 'json' ? 'application/json' : 'application/x-www-form-urlencoded;charset=utf8',
        ...header
      },
      data: data
    }
    return request(config)
  }
}
export function save(url, http = __PATH.VUE_IEMC_API) {
  return function (params, newHttp = http) {
    let userCode = {}
    if (store.getters['user/isLogin']) {
      const userInfo = store.state.user.userInfo.user
      userCode = {
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ',
        userName: userInfo.staffName,
        userId: userInfo.staffId
      }
    }
    Object.assign(params, userCode)
    let formdata = new FormData()
    for (var item in params) {
      if ((item == 'fileName' || item == 'file') && params[item] instanceof Array) {
        if (params[item]) {
          params[item].forEach((res) => {
            formdata.append(item, res)
          })
        } else {
          formdata.append(item, params[item] || '')
        }
      } else if (item == 'inspectionParameterVoList' && params[item] instanceof Array) {
        if (params[item]) {
          params[item].forEach((res, index) => {
            for (var key in res) {
              formdata.append('inspectionParameterVoList[' + index + '].' + key, res[key])
            }
          })
        }
      } else {
        formdata.append(item, params[item] || '')
      }
    }
    const config = {
      method: 'post',
      url: newHttp + url,
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
        user: encodeURIComponent(userCode.userName)
      },
      data: formdata
    }
    return request(config)
  }
}
export function iemc_downFile(url, contentType = 'json') {
  return function (query, http = __PATH.VUE_IEMC_API) {
    let userCode = {}
    if (store.getters['user/isLogin']) {
      const userInfo = store.state.user.userInfo.user
      userCode = {
        hospitalCode: userInfo.hospitalCode,
        unitCode: userInfo.unitCode
        // userName: userInfo.staffName,
        // userId: userInfo.staffId
      }
    }
    let menuList = store.state.menu.routes
    let firstTitle = ''
    let routeList = []
    router.currentRoute.matched
      .filter((item) => item.name)
      .forEach((el) => {
        routeList.push(el.meta.title)
      })
    if (menuList.length) {
      firstTitle = menuList[store.state.menu.headerActived].meta.title
      routeList.unshift(firstTitle)
    }
    const config = {
      method: 'get',
      url: http + url + (query ? '?' + qs.stringify({ ...query, ...userCode }) : ''),
      headers: {
        'Content-Type': contentType == 'json' ? 'application/json;charset=utf-8' : 'application/x-www-form-urlencoded;charset=utf-8',
        'operation-type': 4,
        'operation-content': encodeURIComponent(routeList.join(','))
      }
    }
    return request(config)
  }
}
export function postQs(url, http = __PATH.VUE_APP_IEMS_API, contentType = 'form') {
  return function (params, headers = {}) {
    const config = {
      method: 'post',
      url: http + url,
      headers: {
        'Content-Type': contentType == 'json' ? 'application/json' : 'application/x-www-form-urlencoded;charset=utf8',
        ...headers
      }
    }
    config.data = qs.stringify(params)
    return request(config)
  }
}
export function post_isp_iaast(url, http = __PATH.VUE_APP_IEMS_API, contentType = 'form') {
  return function (params, headers = {}) {
    let baseInfo = sessionStorage.getItem('LOGINDATA') ? JSON.parse(sessionStorage.getItem('LOGINDATA')) : ''
    let data = {}
    if (baseInfo) {
      params.unitCode = baseInfo.unitCode == 'BJSYGJ' ? 'BJSYYGLJ' : baseInfo.unitCode
      params.hospitalCode = baseInfo.hospitalCode == 'BJSJTYY' ? 'BJSJTY' : baseInfo.hospitalCode
      params.userId = 'd6aeca8128fa400c8f60f9c69402d0b3'
      params.userName = '系统管理员'
      params.roleCode = baseInfo.hospitalCode + '_systemAdminCode_IMES_CORE'
      params.officeCode = baseInfo.officeCode
      params.sysIdentity = 'systemAdminCode'
      params.moduleIdentity = 'IMES_CORE'
      params.sysCome = '2'
      params.onOff = '1'
      params.permissionsType = '1'
    }
    Object.assign(data, params)
    const config = {
      method: 'post',
      url: http + url,
      headers: {
        'Content-Type': contentType == 'json' ? 'application/json' : 'application/x-www-form-urlencoded;charset=utf8',
        ...headers
      }
    }
    config.data = qs.stringify(params)
    return request(config)
  }
}
// useGeneralCode 是否使用通用code，用此来判断是否需要添加单位code和医院code，getBaseInfo为安防接口缓存，其他业务功能尽量不要使用
export function getHttp(url, base = __PATH.BASE_URL_LABORATORY, useGeneralCode = true) {
  return function (params) {
    let routeInfo = getBaseInfo()
    let baseInfo = {}
    if (routeInfo && useGeneralCode) {
      baseInfo.unitCode = routeInfo.unitCode
      baseInfo.hospitalCode = routeInfo.hospitalCode
    }
    const config = {
      method: 'get',
      url: base + url,
      headers: {
        'Content-Type': 'application/json',
        token: routeInfo ? routeInfo.token : ''
      }
    }
    config.params = { ...baseInfo, ...params }
    return request(config)
  }
}
export function post_formData(url, http, useGeneralCode = true) {
  return function (params, headers = {}) {
    let routeInfo = getBaseInfo()
    let baseInfo = {}
    if (routeInfo && useGeneralCode) {
      baseInfo.unitCode = routeInfo.unitCode
      baseInfo.hospitalCode = routeInfo.hospitalCode
    }
    const config = {
      method: 'post',
      url: http + url,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded;charset=utf8',
        token: routeInfo ? routeInfo.token : ''
      }
    }
    config.data = qs.stringify({ ...baseInfo, ...params })
    return request(config)
  }
}
export function post_json(url, base = __PATH.BASE_URL_LABORATORY, conf = {}, useGeneralCode = true) {
  return function (params, headers = {}) {
    let routeInfo = getBaseInfo()
    let baseInfo = {}
    if (routeInfo && useGeneralCode) {
      baseInfo.unitCode = routeInfo.unitCode
      baseInfo.hospitalCode = routeInfo.hospitalCode
    }
    const config = {
      method: 'post',
      url: base + url,
      headers: {
        'Content-Type': 'application/json',
        token: routeInfo ? routeInfo.token : ''
      },
      ...conf
    }
    config.data = { ...baseInfo, ...params }
    return request(config)
  }
}
export function uploadCommon(headers = {}) {
  return function (http, url, file) {
    let routeInfo = getBaseInfo()
    const params = new FormData()
    params.append('file', file.file)
    const config = {
      method: 'post',
      url: http + url,
      headers: {
        'Content-Type': 'multipart/form-data',
        token: routeInfo ? routeInfo.token : '',
        ...headers
      }
    }
    config.data = params
    return request(config)
  }
}
export function getRequestSafe(url, http = __PATH.BASE_URL_SAFE, contentType = 'json') {
  return function (params, headers = {}) {
    let routeInfo = getBaseInfo()
    const config = {
      method: 'get',
      url: http + url,
      headers: {
        'Content-Type': contentType == 'json' ? 'application/json' : 'application/x-www-form-urlencoded;charset=utf8',
        ...headers,
        token: routeInfo ? routeInfo.token : ''
      }
    }
    config.params = { ...params }
    return request(config)
  }
}
export function postRequestSafe(url, base = __PATH.BASE_URL_SAFE) {
  return function (params, headers = {}) {
    let routeInfo = getBaseInfo()
    const config = {
      method: 'post',
      url: base + url,
      headers: {
        'Content-Type': 'application/json',
        token: routeInfo ? routeInfo.token : ''
      }
    }
    config.data = { ...params }
    return request(config)
  }
}
export function postRequest_iemc(url, http = __PATH.VUE_IEMC_API, contentType = 'json') {
  return function (params = {}, headers = {}, newHttp = http) {
    let userCode = {}
    if (store.getters['user/isLogin']) {
      const userInfo = store.state.user.userInfo.user
      userCode = {
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ'
      }
    }
    const config = {
      method: 'post',
      url: newHttp + url,
      headers: {
        'Content-Type': contentType == 'json' ? 'application/json' : 'application/x-www-form-urlencoded;charset=utf8',
        ...headers,
        ...userCode,
        path: 'iemc2'
      }
    }
    config.data = { ...params, ...userCode }
    return request(config)
  }
}
export function getRequestDR(url, base = __PATH.VUE_SYS_API) {
  return function (params) {
    let routeInfo = getBaseInfo()
    const config = {
      method: 'get',
      url: base + url,
      headers: {
        'Content-Type': 'application/json',
        token: routeInfo ? routeInfo.token : ''
      }
    }
    config.params = { ...params }
    return request(config)
  }
}
