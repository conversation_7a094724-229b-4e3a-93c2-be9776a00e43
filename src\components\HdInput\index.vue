<!--
 * @Author: hedd
 * @Date: 2023-04-12 11:26:13
 * @LastEditTime: 2023-04-12 11:34:53
 * @FilePath: \ihcrs_pc\src\components\HdInput\index.vue
 * @Description:
-->
<template>
  <div class="hd-input">
    <el-input ref="ipt" v-bind="$attrs">
      <template v-for="(value, name) in $slots" #[name]="slotData">
        <slot :name="name" v-bind="slotData || {}"></slot>
      </template>
    </el-input>
  </div>
</template>
<script>
export default {
  name: 'HdInput',
  mounted() {
    const entries = Object.entries(this.$attrs)
    for (const [key, value] of entries) {
      this[key] = value
    }
  }
}
</script>
<style lang="scss" scoped>
.hd-input {
  background: red;

  .el-input__inner {
    padding-right: 0;
  }
}
</style>
