<template>
  <div class="rule-config-container">
    <div class="config-box">
      <div class="config-box-title">
        <span class="green_line"></span>
        离岗监测
      </div>
      <div class="config-box-content">
        <div class="config-box-content-left">
          <div v-if="!leaveShow" class="check-box">
            <div class="check-value">{{ filtersMonitoringInterval(leaveRadio) }}</div>
            <div v-if="leaveRadio === 0" class="check-title">监测间隔</div>
            <div v-if="leaveRadio === 0" class="check-value">{{ monitoringInterval ? `${monitoringInterval} 分钟` : '' }}</div>
          </div>
          <div v-else class="edit-box">
            <el-radio-group v-model="leaveRadio">
              <el-radio :label="0">开启</el-radio>
              <el-radio :label="1">关闭</el-radio>
            </el-radio-group>
            <div v-if="leaveRadio === 0" class="input-box">
              <el-input v-model="monitoringInterval" placeholder="请输入监测间隔" class="input-with-select" @keyup.native="proving">
                <template slot="prepend">监测间隔：</template>
                <template slot="append">分钟</template>
              </el-input>
            </div>
          </div>
        </div>
        <div class="config-box-content-right">
          <el-button v-if="!leaveShow" type="primary" size="small" @click="leaveClick">编辑</el-button>
          <el-button v-if="leaveShow" size="small" @click="leaveCancelClick">取消</el-button>
          <el-button v-if="leaveShow" type="primary" size="small" @click="saveLeaveRule('monitor')">保存</el-button>
        </div>
      </div>
    </div>
    <div class="config-box">
      <div class="config-box-title">
        <span class="green_line"></span>
        判定规则
      </div>
      <div class="config-box-content">
        <div class="config-box-content-left">
          <div v-if="!judgeRuleShow" class="check-box">
            <div class="check-title">
              半径范围
              <el-popover placement="right" title="" width="300" trigger="hover" content="根据现场环境数据可能存在少许误差，请根据实际环境进行范围配置">
                <span slot="reference" class="el-icon-info" style="color: #3562db"></span>
              </el-popover>
            </div>
            <div class="check-value">{{ rangeOfRadius ? `${rangeOfRadius} m` : '' }}</div>
            <div class="check-title">离岗时间</div>
            <div class="check-value">{{ departureTime ? `${departureTime} 分钟` : '' }}</div>
          </div>
          <div v-else class="edit-box">
            <div class="edit-title">
              半径范围<el-popover placement="right" title="" width="300" trigger="hover" content="根据现场环境数据可能存在少许误差，请根据实际环境进行范围配置">
                <span slot="reference" class="el-icon-info" style="color: #3562db"></span>
              </el-popover>
            </div>
            <div class="edit-value">
              <el-input v-model="rangeOfRadius" placeholder="请输入半径范围" @keyup.native="proving">
                <template slot="append">m</template>
              </el-input>
            </div>
            <div class="edit-title">离岗时间</div>
            <div class="edit-value">
              <el-input v-model="departureTime" placeholder="请输入离岗时间" @keyup.native="proving">
                <template slot="append">分钟</template>
              </el-input>
            </div>
          </div>
        </div>
        <div class="config-box-content-right">
          <el-button v-if="!judgeRuleShow" type="primary" size="small" @click="judgeRuleClick">编辑</el-button>
          <el-button v-if="judgeRuleShow" size="small" @click="judgeRuleCancelClick">取消</el-button>
          <el-button v-if="judgeRuleShow" type="primary" size="small" @click="saveLeaveRule('rule')">保存</el-button>
        </div>
      </div>
    </div>
    <div class="config-box">
      <div class="config-box-title">
        <span class="green_line"></span>
        定位点
      </div>
      <div class="config-box-content">
        <div class="config-box-content-left">
          <div class="btn-box"><el-button type="primary" size="small" icon="el-icon-plus" @click="openAnchorPoint">添加定位点</el-button></div>
          <div class="table-box" :class="!leaveShow && !judgeRuleShow ? 'table-box-hide' : ''">
            <TablePage ref="table" v-loading="tableLoading" :showPage="false" :tableColumn="tableColumn" :data="tableDataItems">
              <template #operate="{ row }">
                <el-button type="text" class="text-red" @click="onOperate(row)">移除</el-button>
              </template>
            </TablePage>
          </div>
        </div>
      </div>
    </div>
    <div v-if="anchorPointShow" class="anchorPoint">
      <anchorPoint
        :echoTableData="tableData"
        :anchorPointShow="anchorPointShow"
        @submitAnchorPointDialog="submitAnchorPointDialog"
        @closeAnchorPoint="closeAnchorPoint"
      ></anchorPoint>
    </div>
  </div>
</template>
<script>
import anchorPoint from './components/anchorPoint.vue'
export default {
  components: { anchorPoint },
  props: {
    dutyPostCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      anchorPointShow: false,
      tableLoading: false,
      leaveShow: false,
      judgeRuleShow: false,
      leaveRadio: 0, // 离岗监测开关
      monitoringInterval: '', // 监测间隔
      rangeOfRadius: '', // 半径范围
      departureTime: '', // 离岗时间
      tableData: [],
      tableColumn: [
        {
          prop: 'name',
          label: '定位点名称'
        },
        {
          prop: 'code',
          label: '资产编码'
        },
        {
          prop: 'sn',
          label: 'SN'
        },
        {
          prop: 'typeName',
          label: '类型'
        },
        {
          prop: 'spaceName',
          label: '所在空间'
        },
        {
          prop: 'x',
          label: '坐标',
          formatter(row) {
            const { x, y, z } = row.row
            return `${x},${y},${z}`
          }
        },
        {
          label: '操作',
          width: '130px',
          slot: 'operate'
        }
      ],
      selectionList: [],
      id: '',
      locationInfo: {}
    }
  },
  computed: {
    tableDataItems() {
      return this.tableData
    }
  },
  watch: {
    dutyPostCode: {
      handler(newVal) {
        this.getDetails()
      },
      deep: true
    }
  },
  mounted() {
    this.getDetails()
  },
  methods: {
    // 获取离岗规则设置详情
    getDetails() {
      let params = {
        dutyPostCode: this.dutyPostCode
      }
      this.$api.supplierAssess.getRuleConfigDetails(params).then((res) => {
        if (res.code == 200) {
          this.id = res.data ? res.data.id : ''
          this.leaveRadio = res.data ? res.data.state : 1
          this.monitoringInterval = res.data ? res.data.intervalTime : 0
          this.rangeOfRadius = res.data ? res.data.radius : 0
          this.departureTime = res.data ? res.data.offlineTime : 0
          if (res.data && res.data.location) {
            this.tableData = [{ ...res.data.location }]
          } else {
            this.tableData = []
          }
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 格式化 监测间隔
    filtersMonitoringInterval(val) {
      if (val === '' || val === null || val === undefined) return ''
      if (val === 0) return '开启'
      if (val === 1) return '关闭'
    },
    // 离岗监测 编辑
    leaveClick() {
      this.leaveShow = !this.leaveShow
    },
    // 离岗监测 取消
    leaveCancelClick() {
      this.leaveRadio = 1
      this.monitoringInterval = ''
      this.leaveShow = false
      this.getDetails()
    },
    // 判定规则 编辑
    judgeRuleClick() {
      this.judgeRuleShow = !this.judgeRuleShow
    },
    // 判定规则 取消
    judgeRuleCancelClick() {
      this.rangeOfRadius = ''
      this.departureTime = ''
      this.judgeRuleShow = false
      this.getDetails()
    },
    // 新增编辑 离岗规则
    saveLeaveRule(type) {
      let params = {
        intervalTime: this.monitoringInterval,
        offlineTime: this.departureTime,
        radius: this.rangeOfRadius,
        state: this.leaveRadio,
        dutyPostCode: this.dutyPostCode || '',
        location: {
          name: this.locationInfo.deviceName || '',
          code: this.locationInfo.assetCode || '',
          sn: this.locationInfo.assetSn || '',
          typeName: this.locationInfo.assetSubcategoryName || '',
          spaceName: this.locationInfo.spaceName || '',
          x: this.locationInfo.x || '',
          y: this.locationInfo.y || '',
          z: this.locationInfo.z || ''
        }
      }
      if (type === 'monitor' && this.leaveRadio === 0 && !params.intervalTime) {
        return this.$message.error('离岗监测开启时监测间隔不能为空或0')
      }
      let fn
      if (this.id) {
        params.id = this.id
        switch (type) {
          case 'monitor':
            fn = this.$api.supplierAssess.updateLeavePostMonitor
            break
          case 'rule':
            fn = this.$api.supplierAssess.updateLeavePostRadius
            break
          default:
            fn = this.$api.supplierAssess.updateLeavePostLocatePoint
            break
        }
      } else {
        fn = this.$api.supplierAssess.submitOffDutyRuleData
      }
      fn(params).then((res) => {
        if (res.code == '200') {
          this.$message.success(res.msg)
          if (type === 'monitor') {
            this.leaveCancelClick()
          } else {
            this.judgeRuleCancelClick()
          }
          this.anchorPointShow = false
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 获取配置信息
    getConfigInfo() {},
    // 移除
    onOperate(row) {
      let params = {
        dutyPostCode: this.dutyPostCode || ''
      }
      this.$api.supplierAssess
        .removeLeavePostLocatePoint(params)
        .then((res) => {
          if (res.code == 200) {
            this.$message.success(res.msg)
            this.getDetails()
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          this.$message.error(err.message || '操作失败！')
        })
    },
    // 打开选择定位点弹窗
    openAnchorPoint() {
      this.anchorPointShow = true
    },
    submitAnchorPointDialog(row) {
      this.locationInfo = row
      this.saveLeaveRule()
    },
    closeAnchorPoint() {
      this.anchorPointShow = false
    },
    /** 输入框输入限制 */
    proving(e) {
      if (e.target.value && e.target.value.length == 1) {
        e.target.value = e.target.value.toString().replace(/[^1-9]/g, '') // 只能输入正整数.replace(/[^1-9]/g, '')
      } else {
        e.target.value = e.target.value.toString().replace(/[^0-9]/g, '') // 只能输入正整数.replace(/[^1-9]/g, '')
      }
      if (e.target.value.indexOf('.') < 0 && e.target.value != '') {
        // 输入替换，如输入05，直接替换为5，防止出现01，02这种情况
        e.target.value = parseInt(e.target.value)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.rule-config-container {
  .config-box {
    &-title {
      font-size: 16px;
      padding: 0;
      border: 0;
      font-weight: bold;
      .green_line {
        margin-right: 2px;
      }
    }
    &-content {
      display: flex;
      align-items: center;
      padding-left: 10px;
      &-left {
        flex: 1;
        flex-shrink: 0;
        .check-box {
          font-size: 20px;
          margin: 5px 0px;
          .check-title {
            font-size: 16px;
          }
          .check-value {
            font-size: 14px;
            margin: 10px 0px;
            padding-left: 5px;
          }
        }
        .edit-box {
          margin: 5px 0px;
          .el-radio-group {
            margin-top: 5px;
          }
          .input-box,
          .edit-value {
            margin: 10px 0px;
            width: 300px;
          }
        }
        .btn-box {
          margin: 10px 0px;
        }
      }
    }
  }
}
::v-deep .el-button--default {
  height: 32px;
  box-sizing: border-box;
}
</style>
