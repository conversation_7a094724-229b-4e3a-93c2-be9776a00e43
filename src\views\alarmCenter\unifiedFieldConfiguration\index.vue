<template>
  <PageContainer>
    <div slot="content" class="configuration-content">
      <div class="configuration-content-left">
        <div v-loading class="left_content">
          <el-input v-model="filterText" placeholder="输入关键字进行过滤" suffix-icon="el-icon-search" clearable></el-input>
          <el-tree
            ref="tree"
            v-loading="treeLoading"
            style="margin-top: 10px"
            :data="treeData"
            :props="defaultProps"
            node-key="value"
            :highlight-current="true"
            :filter-node-method="filterNode"
            @node-click="handleNodeClick"
          >
          </el-tree>
        </div>
      </div>
      <div class="configuration-content-right">
        <div style="height: 100%">
          <div class="search-from">
            <div>
              <el-input v-model="filters.thirdAlarmType" suffix-icon="el-icon-search" placeholder="三方报警类型名称、编码"></el-input>
              <el-input v-model="filters.thirdAlarmSystem" suffix-icon="el-icon-search" placeholder="三方报警系统" class="ml-16"></el-input>
              <el-cascader
                ref="alarmDictId"
                v-model="filters.alarmDictId"
                :props="propsType"
                class="ml-16"
                clearable
                :options="alarmTypeList"
                placeholder="中心报警类型名称"
                :show-all-levels="false"
              ></el-cascader>
              <el-select v-model="filters.configStatus" placeholder="全部配置状态" class="ml-16" clearable>
                <el-option v-for="item in configStatusList" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </div>
            <div>
              <el-button type="primary" plain class="ml-16" @click="resetForm">重置</el-button>
              <el-button type="primary" @click="searchForm">查询</el-button>
            </div>
          </div>
          <el-button type="primary" icon="el-icon-plus" :disabled="multipleSelection.length < 1" @click="handleListEvent('add')">
            配置
          </el-button>
          <div class="contentTable">
            <div class="contentTable-main table-content">
              <el-table v-loading="tableLoading" border style="width: 100%" height="calc(100% - 20px)" :data="tableData" stripe @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55"></el-table-column>
                <el-table-column prop="thirdParentName" label="三方报警类型名称" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span v-if="scope.row.thirdParentCode">{{ scope.row.thirdParentName + '/' + scope.row.thirdName
                      }}</span>
                    <span v-else>{{ scope.row.thirdName }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="thirdParentCode" label="三方报警类型编码" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span v-if="scope.row.thirdParentCode">{{ scope.row.thirdParentCode + '/' + scope.row.thirdCode
                      }}</span>
                    <span v-else>{{ scope.row.thirdCode }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="thirdSystemName" label="三方报警系统" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span>{{ scope.row.thirdSystemName }}（{{ scope.row.thirdSystemCode }}）</span>
                  </template>
                </el-table-column>
                <el-table-column prop="alarmDictName" label="中心报警类型名称" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span v-if="scope.row.alarmDictName">{{ scope.row.alarmDictName }}</span>
                    <span v-else class="noConfig">未配置</span>
                  </template>
                </el-table-column>
                <el-table-column prop="alarmDictCode" label="中心报警类型编码" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span v-if="scope.row.alarmDictCode">{{ scope.row.alarmDictCode }}</span>
                    <span v-else class="noConfig">未配置</span>
                  </template>
                </el-table-column>
                <el-table-column show-overflow-tooltip label="操作" width="200" align="center">
                  <template slot-scope="scope">
                    <el-button type="text" @click="handleListEvent('edit', scope.row)">配置</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="contentTable-footer">
              <el-pagination
                :current-page="pagination.current"
                :page-sizes="pagination.pageSizeOptions"
                :page-size="pagination.size"
                :layout="pagination.layoutOptions"
                :total="pagination.total"
                @size-change="paginationSizeChange"
                @current-change="paginationCurrentChange"
              >
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
      <el-dialog
        v-dialogDrag
        :visible.sync="dialogVisible"
        :before-close="dialogClosed"
        :modal="false"
        :close-on-click-modal="false"
        title="字段配置"
        width="35%"
        custom-class="model-dialog"
      >
        <div class="diaContent" style="padding: 10px">
          <el-form ref="formInline" :model="formInline" label-width="120px" :rules="rules" label-position="right">
            <el-form-item label="中心报警类型" prop="alarmDictId">
              <el-cascader
                ref="alarmDictId"
                v-model="formInline.alarmDictId"
                :props="propsType"
                :options="alarmTypeList"
                placeholder="请选择"
                class="cascaderWid"
                :show-all-levels="false"
              ></el-cascader>
            </el-form-item>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="dialogClosed">取 消</el-button>
          <el-button type="primary" @click="submit('formInline')">确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </PageContainer>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin'
export default {
  name: 'unifiedFieldConfiguration',
  mixins: [tableListMixin],
  data() {
    return {
      defaultProps: {
        label: 'name',
        value: 'value'
      },
      filterText: '',
      checkedData: {},
      treeData: [],
      tableLoading: false,
      treeLoading: false,
      dialogVisible: false,
      filters: {
        configStatus: '', // 配置状态,0-未配置，1-已配置
        thirdAlarmType: '', // 三方报警类型
        thirdAlarmSystem: '', // 三方报警系统
        alarmDictId: '', // 中心报警类型名称
        type: 1 // 类型，1-报警类型
      },
      propsType: {
        children: 'children',
        label: 'name',
        value: 'id',
        checkStrictly: true,
        multiple: false,
        emitPath: false
      },
      formInline: {
        alarmDictId: ''
      },
      configStatusList: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '已配置',
          value: '1'
        },
        {
          label: '未配置',
          value: '0'
        }
      ],
      rules: {
        alarmDictId: [{ required: true, message: '请选择中心报警类型', trigger: 'change' }]
      },
      tableData: [],
      multipleSelection: [],
      alarmTypeList: [],
      rowId: ''
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  mounted() {
    this.getTreeData()
    this.getDataList()
    this.getConfigData()
  },
  methods: {
    // 获取树数据
    getTreeData() {
      this.treeLoading = true
      this.$api.getAlarmFieldTypeData().then((res) => {
        if (res.code == '200') {
          this.treeData = res.data
          this.checkedData = this.treeData[0]
          this.$nextTick(() => {
            this.$refs.tree.setCurrentKey(this.checkedData.value)
          })
        }
      })
      this.treeLoading = false
    },
    // 过滤树
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    getDataList() {
      let data = {
        ...this.filters,
        pageNo: this.pagination.current,
        pageSize: this.pagination.size
      }
      this.tableLoading = true
      this.$api.getFieldsConfigByPage(data).then((res) => {
        if (res.code == '200') {
          this.tableData = res.data.records
          this.pagination.total = res.data.total
        }
      })
      this.tableLoading = false
    },
    // 报警字典
    getConfigData() {
      this.$api.getAlarmDictConfigData({ type: 1 }).then((res) => {
        if (res.code == '200') {
          this.alarmTypeList = res.data
        }
      })
    },
    handleNodeClick(data) {
      this.checkedData = data
      this.pagination.current = 1
      this.$refs.tree.setCurrentKey(this.checkedData.value)
      this.getDataList()
    },
    resetForm() {
      this.filters = {
        configStatus: '', // 配置状态,0-未配置，1-已配置
        thirdAlarmType: '', // 三方报警类型
        thirdAlarmSystem: '', // 三方报警系统
        alarmDictId: '' // 中心报警类型名称
      }
      this.getDataList()
    },
    searchForm() {
      this.getDataList()
    },
    // 弹窗取消
    dialogClosed() {
      this.dialogVisible = false
      this.$refs.formInline.resetFields()
    },
    submit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let ids = []
          this.multipleSelection.forEach((el) => {
            ids.push(el.id)
          })
          let params = {
            ids: this.rowId ? this.rowId.toString() : ids.join(','),
            alarmDictId: this.formInline.alarmDictId.toString()
          }
          this.$api.insertAlarmFieldConfigData(params).then((res) => {
            if (res.code == 200) {
              this.$message.success('配置成功')
              this.getDataList()
              this.dialogVisible = false
            } else {
              this.$message.error('配置失败')
            }
          })
        } else {
          return false
        }
      })
    },
    handleListEvent(type, row) {
      this.dialogVisible = true
      if (type == 'add') {
        if (this.multipleSelection && this.multipleSelection.length === 1) {
          this.rowId = this.multipleSelection[0].id
          this.formInline.alarmDictId = this.multipleSelection[0].alarmDictId
        } else {
          this.rowId = ''
          this.formInline.alarmDictId = ''
        }
        this.getConfigData()
      } else if (type == 'edit') {
        this.rowId = row.id
        this.formInline.alarmDictId = row.alarmDictId
        this.getConfigData()
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: linear-gradient(to right, #d9e1f8, #fff);
}
::v-deep .el-tree-node__content {
  height: 32px;
}
.configuration-content {
  height: 100%;
  display: flex;
  .configuration-content-left {
    width: 246px;
    height: 100%;
    padding: 10px;
    background: #fff;
    border-radius: 4px;
    margin-right: 12px;
    .left_content {
      height: calc(100% - 60px);
      overflow: auto;
    }
  }
  .configuration-content-right {
    height: 100%;
    min-width: 0;
    padding: 10px 16px;
    background: #fff;
    border-radius: 4px;
    flex: 1;
    .search-from {
      display: flex;
      justify-content: space-between;
      padding-bottom: 12px;
      ::v-deep .el-select,
      .el-input {
        width: 200px !important;
      }
      & > div {
        margin-right: 10px;
      }
      & > button {
        margin-top: 12px;
      }
    }
    .contentTable {
      height: calc(100% - 95px);
      display: flex;
      margin-top: 10px;
      flex-direction: column;
      .contentTable-main {
        flex: 1;
        overflow: auto;
      }
      .contentTable-footer {
        padding: 10px 0 0;
      }
    }
  }
  .noConfig {
    color: #f53f3f;
  }
  .diaContent {
    width: 100%;
    max-height: 500px !important;
    overflow: auto;
    background-color: #fff !important;
  }
  .ml-16 {
    margin-left: 16px;
  }
}
</style>
