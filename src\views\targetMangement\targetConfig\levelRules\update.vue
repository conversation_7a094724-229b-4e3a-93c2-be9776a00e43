<template>
  <PageContainer>
    <div slot="content">
      <div class="title">添加等级规则</div>
      <div class="pageContainer">
        <el-form ref="form" class="form" :model="form" :rules="rules" label-width="140px">
          <el-form-item label="等级规则名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入等级规则名称" maxlength="20"> </el-input>
          </el-form-item>
          <el-form-item label="自动开启" prop="auto">
            <el-radio-group v-model="form.auto">
              <el-radio :label="0">不开启</el-radio>
              <el-radio :label="1">开启</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-table :data="form.cycleLevelList" border stripe style="width: 100%">
            <el-table-column prop="name" label="等级名称">
              <template slot-scope="scope">
                <el-input v-model="form.cycleLevelList[scope.$index].name" placeholder="请输入等级名称" maxlength="20"></el-input>
              </template>
            </el-table-column>
            <el-table-column prop="min">
              <template slot="header">
                <span>最小分数</span>
                <el-tooltip placement="right">
                  <i class="el-icon-warning"></i>
                  <div slot="content" class="popper">{{ tips }}</div>
                </el-tooltip>
              </template>
              <template slot-scope="scope">
                <el-input v-model="form.cycleLevelList[scope.$index].min" placeholder="请输入最小分数" @change="(e) => minValidate(e, scope.$index, scope.row)"></el-input>
              </template>
            </el-table-column>
            <el-table-column prop="max">
              <template slot="header">
                <span>最大分数</span>
                <el-tooltip placement="right">
                  <i class="el-icon-warning"></i>
                  <div slot="content" class="popper">{{ tips }}</div>
                </el-tooltip>
              </template>
              <template slot-scope="scope">
                <el-input v-model="form.cycleLevelList[scope.$index].max" placeholder="请输入最大分数" @change="(e) => maxValidate(e, scope.$index, scope.row)"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template slot-scope="scope">
                <el-button type="text" @click="del(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form>

        <el-button style="margin-top: 16px" type="primary" @click="addLevel">添加等级</el-button>
      </div>
      <div class="footer">
        <el-button type="primary" @click="submit">保存</el-button>
        <el-button type="primary" plain @click="goBack">取消</el-button>
      </div>
    </div>
  </PageContainer>
</template>
<script>
export default {
  name: 'updateLevelRules',
  data() {
    return {
      form: {
        cycleLevelList: [
          {
            name: '',
            min: '',
            max: ''
          }
        ],
        cycleLevel: 1,
        name: '',
        auto: 0
      },
      rules: {
        name: { required: true, message: '请输入周期名称', trigger: 'blur' },
        type: { required: true, message: '请选择周期类型', trigger: 'blur' },
        auto: { required: true, message: '请设置周期执行方式', trigger: 'change' }
      },
      tips: '分数区间填写规则：等级需要按照分数区间从高到低填写，两个连续的等级，前一等级的最小分数的包含关系不能和后一等级的最大等级重复，后一等级的最大分数必须等于上一等级的最小分数。\n 举例：\n S:90~100 (包含)，A:80~90(包含)先添加S(最小分数:不包含90，最大分数:包含100)再增加A(最小分数:不包含80，最大分数:包含90)'
    }
  },
  mounted() {
    if (this.$route.query.id) {
      this.getDetail()
    }
  },
  methods: {
    // 最小值校验
    minValidate(value, index, row) {
      let val = value !== '' ? Number(value) : ''
      if (val === '') {
        this.$message.warning('请输入最小值')
      } else {
        if (100 < val || val < 0) {
          this.$message.warning('最小值输入有误')
        }
        if (row.max && val > row.max) {
          this.$message.warning('最小值不能大于最大值')
        }
        // if (this.form.cycleLevelList.length > 1) {
        //   if (val > this.form.cycleLevelList[index - 1].max) {
        //     callback(new Error('最小值不能大于上一项的最小值'))
        //   }
        // }
      }
    },
    // 最大值校验
    maxValidate(value, index, row) {
      let val = value !== '' ? Number(value) : ''
      if (val === '') {
        this.$message.warning('请输入最大值')
      } else {
        if (0 > val || val > 100) {
          this.$message.warning('最大值输入有误')
        }
        if (row.min && val <= row.min) {
          this.$message.warning('最大值不能小于等于最小值')
        }
        // if (this.form.cycleLevelList.length > 1) {
        //   if (val > this.form.cycleLevelList[index - 1].min) {
        //     callback(new Error('最大值不能大于上一项的最小值'))
        //   }
        // }
      }
    },

    getDetail() {
      this.$api.targetConfigDetail({ cycleLevel: this.type, id: this.$route.query.id }).then((res) => {
        if (res.code === '200') {
          this.form = res.data
        }
      })
    },

    addLevel() {
      if (this.form.cycleLevelList.length > 0) {
        let last = this.form.cycleLevelList[this.form.cycleLevelList.length - 1]
        if ((!last.min && last.min !== 0) || !last.max) {
          this.$message.warning('请先完善当前规则')
          return
        }
      }
      this.form.cycleLevelList.push({
        name: '',
        min: '',
        max: ''
      })
    },

    submit() {
      let arr = this.form.cycleLevelList
      for (let i = 0; i < arr.length; i++) {
        if (!arr[i].name) {
          this.$message.error('请输入等级名称')
          return
        }
        if (i === 0) {
          if (Number(arr[i].max) !== 100) {
            this.$message.warning('填写分数不符合规范，请阅读填写规则！')
            return
          }

          if (arr.length > 1) {
            if (Number(arr[i].min) !== Number(arr[i + 1].max)) {
              this.$message.warning('填写分数不符合规范，请阅读填写规则！')
              return
            }
          } else {
            if (Number(arr[i].min) !== 0) {
              this.$message.warning('填写分数不符合规范，请阅读填写规则！')
              return
            }
          }
        } else if (i === arr.length - 1) {
          if (Number(arr[i].min) !== 0) {
            this.$message.warning('填写分数不符合规范，请阅读填写规则！')
            return
          }
        } else {
          if (Number(arr[i].min) !== Number(arr[i + 1].max)) {
            this.$message.warning('填写分数不符合规范，请阅读填写规则！')
            return
          }
          if (Number(arr[i].max) !== Number(arr[i - 1].min)) {
            this.$message.warning('填写分数不符合规范，请阅读填写规则！')
            return
          }
        }
      }
      this.$refs.form.validate((valid) => {
        if (valid) {
          // 提交之前判断一下是否已开启的等级规则，如果有，return，否则的话提交
          if (this.form.auto === 1) {
            this.$api.haveOpenCycleRule({ id: this.form.id }).then((res) => {
              if (res.code === '200') {
                if (!res.data) {
                  this.$api.updateConfigRule(this.form).then((res) => {
                    if (res.code === '200') {
                      this.$message.success('操作成功')
                      this.goBack()
                    }
                  })
                } else {
                  this.$message.warning('同时仅可以生效一条等级规则')
                  this.form.auto = 0
                }
              }
            })
          } else {
            let header = {}
            if (this.form.id) {
              header = {
                'operation-type': 2,
                'operation-id': this.form.id,
                'operation-name': this.form.name
              }
            } else {
              header = {
                'operation-type': 1
              }
            }
            this.$api.updateConfigRule(this.form, header).then((res) => {
              if (res.code === '200') {
                this.$message.success('操作成功')
                this.goBack()
              }
            })
          }
        }
      })
    },
    goBack() {
      this.$router.go(-1)
    },
    del(i) {
      if (this.form.cycleLevelList.length > 1) {
        this.form.cycleLevelList.splice(i, 1)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.container-content > div {
  background-color: #fff;
  height: 100%;
  position: relative;
  .pageContainer {
    width: 60%;
    padding: 16px 24px;
  }
}

.footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  padding: 10px 20px;
  background-color: #fff;
  text-align: right;
  border-top: 1px solid #ebeef5;
}

.title {
  font-size: 20px;
  font-weight: 700;
  color: #303133;
  padding: 12px 20px 0;
  margin-bottom: 12px;
}
::v-deep .popper {
  white-space: pre-line !important;
}
</style>
