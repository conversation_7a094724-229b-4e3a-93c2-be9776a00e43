<template>
  <div class="tabs_content">
    <el-dropdown @command="addWorkOrder">
      <el-button type="primary"> 新建工单<i class="el-icon-arrow-down el-icon--right"></i> </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item v-for="(item, index) in workOrderList" :key="index" :command="item">{{ item.workTypeName }}</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <div v-for="(item, index) in workInfoList" :key="index" class="workOrder_item" @click="showDetail(item)">
      <img src="@/assets/images/alarmCenter/workOrder.png" alt="" />
      <div class="workOrder_item_content">
        <div class="workOrder_item_content_top">
          <div class="label">工单号：</div>
          <div class="orderNum">{{ item.workNum }}</div>
          <el-tag :type="item.flowcode === 3 ? 'warning' : item.flowcode === 5 ? 'success' : 'danger'" size="small">{{ item.flowtype }}</el-tag>
        </div>
        <div class="workOrder_item_content_bottom">
          <div class="label">工单类型：</div>
          <div>{{ item.workTypeName }}</div>
        </div>
      </div>
    </div>
    <CreatedWorkOrder
      v-if="visible"
      :workOrderDealShow.sync="visible"
      :workTypeCode="currentChoose.workTypeCode"
      :workTypeName="currentChoose.workTypeName"
      :alarmId="alarmId"
      :projectCode="projectCode"
      :spaceId="spaceId"
      dealType="add"
      @workOrderSure="workOrderSure"
    />
    <!-- 关联工单详情 -->
    <WorkOrderDetailDialog v-if="workOrderDetailShow" sourceType="alarm" :visible.sync="workOrderDetailShow" :alarmDetail="alarmDetail" :dialogDetail="workOderDetailData" />
  </div>
</template>
<script>
export default {
  props: {
    workInfoList: {
      type: Array,
      default: () => []
    },
    alarmId: {
      type: [String, Number],
      default: ''
    },
    projectCode: {
      type: String,
      default: ''
    },
    alarmSpaceId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      visible: false,
      workOrderList: [
        // 工单类型列表
        {
          workTypeName: '综合维修',
          workTypeCode: '1'
        }
        // ,
        // {
        //   workTypeName: '确警',
        //   id: '16'
        // }
      ],
      currentChoose: {
        workTypeCode: '',
        workTypeName: ''
      },
      workOrderDetailShow: false,
      alarmDetail: {},
      workOderDetailData: [],
      spaceId: ''
    }
  },
  watch: {
    workInfoList: {
      handler(val) {
        this.$forceUpdate()
      },
      deep: true
    },
    workOrderDetailShow(val) {
      if (!val) {
        this.$emit('closeWorkDetail')
      }
    }
  },
  mounted() {
    this.spaceId = this.alarmSpaceId.split(',')[this.alarmSpaceId.split(',').length - 1]
  },
  methods: {
    addWorkOrder(val) {
      console.log(this.spaceId, this.projectCode)
      this.currentChoose = val
      this.visible = true
    },
    beforeCommand(item) {
      return item
    },
    workOrderSure(data) {
      this.$emit('addWorkOrderSuccess', data)
      this.visible = false
    },
    showDetail(item) {
      this.alarmDetail = {
        projectCode: item.projectCode,
        alarmSpaceId: item.alarmSpaceId,
        alarmId: item.alarmId
      }
      this.workOderDetailData = [
        {
          workTypeName: item.workTypeName,
          id: item.workNum,
          active: true
        }
      ]
      this.workOrderDetailShow = true
    }
  }
}
</script>
<style lang="scss" scoped>
.tabs_content {
  width: 100%;
  height: 100%;
  padding: 24px 0;
  overflow: auto;
}
.workOrder_item {
  padding: 16px 24px;
  background: #faf9fc;
  border-radius: 4px;
  display: flex;
  align-items: center;
  margin-top: 16px;
  img {
    width: 32px;
    height: 32px;
    margin-right: 24px;
  }
  .workOrder_item_content_top,
  .workOrder_item_content_bottom {
    display: flex;
    align-items: center;
    .label {
      width: 80px;
      text-align: right;
      color: #96989a;
    }
    .orderNum {
      color: #3562db;
      margin-right: 8px;
    }
  }
}
</style>
