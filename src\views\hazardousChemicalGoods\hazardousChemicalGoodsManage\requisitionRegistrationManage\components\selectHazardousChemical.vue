<template>
  <el-dialog v-dialogDrag title="添加危化品" width="55%" :visible.sync="sectionDialogShow" custom-class="model-dialog" :before-close="closeDialog">
    <div class="content">
      <div class="search-box">
        <el-input v-model="searchForm.unionSel" placeholder="危化品编码/危化品名称/规格型号"></el-input>
        <!-- <el-input v-model="searchForm.trademark" placeholder="品牌"></el-input> -->
        <el-select v-model="searchForm.supplierId" placeholder="供应商" filterable>
          <el-option v-for="item in supplierOptions" :key="item.id" :label="item.unitsName" :value="item.id"></el-option>
        </el-select>
        <div class="ml-16">
          <el-button type="primary" @click="search">查询</el-button>
          <el-button type="primary" plain @click="reset">重置</el-button>
        </div>
      </div>
      <div class="sino_table">
        <el-table ref="sinoTable" v-loading="tableLoading" :data="tableData" row-key="id" height="300px" stripe border @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" :reserve-selection="true"></el-table-column>
          <el-table-column prop="materialCode" label="危化品编码" show-overflow-tooltip></el-table-column>
          <el-table-column prop="materialName" label="危化品名称" show-overflow-tooltip></el-table-column>
          <el-table-column prop="model" label="规格型号" show-overflow-tooltip></el-table-column>
          <el-table-column prop="basicUnitName" label="计量单位" show-overflow-tooltip></el-table-column>
          <el-table-column prop="materialTypeName" label="危化品分类" show-overflow-tooltip></el-table-column>
          <el-table-column prop="minStock" label="最低库存" show-overflow-tooltip></el-table-column>
          <el-table-column prop="maxStock" label="最高库存" show-overflow-tooltip></el-table-column>
          <el-table-column v-if="warehouseType === '2'" prop="inventory" label="库存数量" show-overflow-tooltip></el-table-column>
          <el-table-column prop="supplierName" label="供应商" show-overflow-tooltip></el-table-column>
          <el-table-column prop="manufacturerName" label="生产厂家" show-overflow-tooltip></el-table-column>
        </el-table>
        <el-pagination
          :current-page="pagination.current"
          :page-sizes="[15, 30, 50, 100]"
          :page-size="pagination.size"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="submitDialog('exportForm')">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  name: 'selectHazardousChemical',
  components: {},
  props: {
    sectionDialogShow: {
      type: Boolean,
      default: false
    },
    // 根据仓库id查询危化品
    warehouseId: {
      type: String,
      default: ''
    },
    // 根据仓库id查询危化品
    warehouseType: {
      type: String,
      default: '' // 1入库 2出库
    },
    selectHcsList: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      tableLoading: false,
      tableData: [],
      multipleSelection: [],
      // -----------------------------Pagination
      pagination: {
        current: 1,
        size: 15
      }, // 分页数据
      total: 0, // 数据总条数
      supplierOptions: [], // 供应商下拉
      selectList: [],
      searchForm: {
        unionSel: '', // 危化品名称
        trademark: '', // 品牌
        supplierId: '' // 供应商id
      }
    }
  },
  mounted() {
    this.getTableData()
    this.getSupplierData()
  },
  methods: {
    // 获取供应商数据
    getSupplierData() {
      let params = {
        unitsTypeCode: 3,
        category: 2,
        status: '0',
        userId: this.$store.state.user.userInfo.user.staffId,
        userName: this.$store.state.user.userInfo.user.staffName
      }
      this.$api.hscUnitManger(params).then((res) => {
        if (res.code == 200) {
          this.supplierOptions = res.data.list
        }
      })
    },
    // 获取列表数据
    getTableData() {
      const userInfo = this.$store.state.user.userInfo.user
      this.tableData = []
      this.tableLoadingStatus = true
      let idList = []
      if (this.selectHcsList && this.selectHcsList.length) {
        this.selectHcsList.forEach((item) => {
          idList.push(item.id)
        })
      }
      const params = {
        pageSize: this.pagination.size,
        currentPage: this.pagination.current,
        ...this.searchForm,
        userId: userInfo.staffId,
        userName: userInfo.staffName,
        warehouseId: this.warehouseId,
        idList: idList.join(',')
      }
      let url = this.warehouseType === '1' ? 'queryInWareHouseHcsByPage' : 'getMaterialinfoByPage'
      if (this.warehouseType === '1') {
        params.status = '0'
      }
      this.$api[url](params)
        .then((res) => {
          this.tableLoadingStatus = false
          if (res.code === '200') {
            this.tableData = res.data.list
            this.total = res.data.sum
          } else {
            this.tableData = []
            this.total = 0
          }
        })
        .catch((err) => {
          this.tableLoadingStatus = false
        })
    },
    search() {
      this.pagination.current = 1
      this.getTableData()
    },
    reset() {
      this.searchForm = {
        unionSel: '', // 危化品名称
        trademark: '', // 品牌名称
        supplierId: '' // 供应商id
      }
      this.getTableData()
    },
    // ---------------------------------------------------------- TabelFn
    handleSizeChange(val) {
      this.pagination.current = 1
      this.pagination.size = val
      this.getTableData()
    },
    handleCurrentChange(val) {
      this.pagination.current = val
      this.getTableData()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
      this.selectList = JSON.parse(JSON.stringify(this.multipleSelection))
    },
    closeDialog() {
      this.$emit('closeSectionDialog')
    },
    submitDialog() {
      this.$emit('submitSectionDialog', this.selectList)
    }
  }
}
</script>
<style lang="scss" scoped>
.model-dialog {
  .content {
    margin: 0 auto;
    background: #fff;
    border-radius: 4px;
    width: 100%;
    height: 100%;
    padding: 16px;
  }
  .search-box {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    .el-input {
      width: 180px;
      margin-right: 16px;
    }
  }
  .ml-16 {
    margin-left: 20px;
  }
  .el-pagination {
    margin-top: 10px;
    width: 100%;
  }
}
</style>
