<template>
  <el-dialog :title="dialogTitle" custom-class="model-dialog" :visible="outerVisible" @close="closeOutDialog">
    <div class="content_box">
      <el-form ref="formInline" class="dialog-form" :model="formInline" :inline="true" :rules="rules" label-position="right" label-width="110px" :disabled="disabledFlom">
        <el-form-item label="成员名称" prop="planName">
          <template>
            <div v-if="saveType == 'add'" class="person_div" style="cursor: pointer;" @click="addPersonShow">
              <span v-if="workerJsonStr.length == 0" style="color: #dcdfe3;">请选择成员</span>
              <span v-else>{{ staffNameArr.join(',') }}</span>
            </div>
            <span v-else class="detaillClass">{{ name }}</span>
          </template>
        </el-form-item>
        <br />
        <el-form-item label="部门权限" prop="positionType">
          <div v-if="saveType != 'check'">
            <el-radio v-model="formInline.positionType" label="1">组长</el-radio>
            <el-radio v-model="formInline.positionType" label="2">组员</el-radio>
            <el-radio v-model="formInline.positionType" label="3">数据管理员</el-radio>
          </div>
          <span v-else class="detaillClass">{{ formInline.positionType == '1' ? '组长' : formInline.positionType == '2' ? '组员' : '数据管理员' }}</span>
        </el-form-item>
        <br />
        <el-form-item label="所属部门" prop="controlTeamId">
          <el-cascader
            v-if="saveType != 'check'"
            ref="cascaderAddr"
            v-model="formInline.controlTeamId"
            style="width: 530px;"
            placeholder="请选择所属部门"
            :options="treeData"
            filterable
            label="teamName"
            value="id"
            :props="{
              label: 'teamName',
              value: 'id',
              multiple: true,
              checkStrictly: true
            }"
            @change="controlTeamChange"
          ></el-cascader>
          <span v-else class="detaillClass">{{ formInline.controlTeamName }}</span>
        </el-form-item>
        <br />
        <el-form-item label="平台登录角色" prop="webRoleCode">
          <el-select v-if="saveType != 'check'" v-model="formInline.webRoleCode" placeholder="请选择登录角色" :disabled="disabled" style="width: 530px;">
            <el-option v-for="item in RoleList" :key="item.id" :label="item.roleName" :value="item.id"></el-option>
          </el-select>
          <span v-else class="detaillClass">{{ formInline.webRoleName }}</span>
        </el-form-item>
      </el-form>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button v-if="saveType != 'check'" type="primary" @click="closeOutDialog">取 消</el-button>
      <el-button v-if="saveType != 'check'" type="primary" @click="complete()">保存</el-button>
      <el-button v-if="saveType == 'check'" type="primary" @click="closeOutDialog">关闭</el-button>
    </div>
    <addPerson :dialogVisible="dialogVisible" @closeDialog="closeDialog" @sure="sure"></addPerson>
  </el-dialog>
</template>

<script>
import { transData } from '@/util'
import addPerson from './addperson.vue'
export default {
  name: 'addControlRow',
  components: { addPerson },
  props: {
    outerVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      disabledFlom: false,
      dialogVisible: false,
      disabled: false,
      loading: false,
      formInline: {
        planName: '',
        controlTeamId: [],
        controlTeamName: '',
        webRoleCode: '',
        positionType: '2',
        webRoleName: ''
      },
      name: '',
      RoleList: [],
      treeData: [],
      rules: {
        // planName: [{ required: true, message: "请选择成员", trigger: "blur" }],
        controlTeamId: [{ required: true, message: '请选择所属部门', trigger: 'blur' }],
        webRoleCode: [{ required: true, message: '请选择角色', trigger: 'blur' }]
      },
      workerJsonStr: [],
      staffNameArr: [],
      staffIdArr: [],
      userId: '',
      dialogTitle: '',
      saveType: ''
    }
  },
  created() {
    this.init()
  },
  methods: {
    getSaveType(type, rowId, userId) {
      this.saveType = type
      this.dialogTitle = type == 'add' ? '新增用户' : type == 'edit' ? '编辑用户' : '用户详情'
      if (type == 'add') {
        this.formInline.webRoleCode = rowId
      } else {
        this.userId = userId
        this.getControlTeamUserInfo()
      }
    },
    closeOutDialog() {
      this.$emit('closeDialog')
      this.staffNameArr = []
      this.formInline = {
        planName: '',
        controlTeamId: [],
        controlTeamName: '',
        webRoleCode: '',
        positionType: '2',
        webRoleName: ''
      }
    },
    // 获取详情
    getControlTeamUserInfo() {
      this.$api
        .getControlTeamUserInfoIpsm({
          id: this.userId
        })
        .then((res) => {
          if (res.data.allContent) {
            this.formInline = JSON.parse(res.data.allContent)
          } else {
            this.formInline = res.data
            this.formInline.positionType = res.data.positionType.toString()
          }
          let newArr = this.RoleList.filter((item) => {
            return item.id == this.formInline.webRoleCode
          })
          this.formInline.webRoleName = newArr.length ? newArr[0].roleName : ''
          this.name = res.data.name
          this.formInline.controlTeamId = this.formInline.controlTeamId.split(',')
        })
    },
    // 点击确定
    complete() {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          let list = this.$refs.cascaderAddr.getCheckedNodes()
          this.formInline.controlTeamName = list
            .map((item) => {
              return item.label
            })
            .join(',')
          this.formInline.controlTeamId = list
            .map((item) => {
              return item.data.id
            })
            .join(',')
          let data = {
            ...this.formInline,
            // controlTeamId:this.formInline.controlTeamId[this.formInline.controlTeamId.length-1],
            workerJsonStr: JSON.stringify(this.workerJsonStr),
            ids: this.saveType == 'edit' ? this.userId : '',
            allContent: JSON.stringify(this.formInline)
          }
          console.log(data)
          // return
          let url = this.saveType == 'add' ? 'addControlTeamUserIpsm' : 'updateControlTeamUserIpsm'
          this.$api[url](data).then((res) => {
            if (res.code == 200) {
              this.$message.success(res.message)
              this.$emit('closeDialog')
              this.$emit('getUserList')
            } else {
              this.$message.error(res.message)
            }
          })
        }
      })
    },
    init() {
      // 所属部门
      this.$api.getControlGroupInfoListIpsm({}).then((res) => {
        this.treeLoading = false
        this.treeData = transData(res.data.list, 'id', 'parentId', 'children')
      })
      // 角色列表
      this.$api.userRoleGetRoleListIpsm({}).then((res) => {
        this.RoleList = res.data
      })
    },
    // 选择角色
    addPersonShow() {
      this.dialogVisible = true
    },
    sure(list) {
      this.workerJsonStr = []
      list.forEach((item, i) => {
        this.workerJsonStr.push({
          staffId: item.id,
          name: item.name,
          phone: item.mobile,
          gender: item.sex,
          userType: item.type == 0 ? 1 : 2,
          userName: item.userName
        })
      })
      this.staffIdArr = list.map((o) => {
        return o.id
      })
      this.staffNameArr = list.map((o) => {
        return o.name
      })
      this.dialogVisible = false
    },
    closeDialog() {
      this.dialogVisible = false
    },
    tabelDelRow(index) {
      this.workerJsonStr.splice(index, 1)
    },
    controlTeamChange(val) {
      // this.$nextTick(() => {
      //   console.log(this.$refs.cascaderAddr.setCheckedNodes);
      //   let list = this.$refs.cascaderAddr.getCheckedNodes();
      //   this.formInline.controlTeamName = list.map((item) => {
      //     return item.label;
      //   });
      //   this.formInline.controlTeamId = list.map((item) => {
      //     return item.data.id;
      //   });
      //   console.log(this.formInline.controlTeamName);
      //   console.log(this.formInline.controlTeamId);
      //   // this.formInline.controlTeamName = list[0].label;
      //   // this.formInline.controlTeamId = list[0].data.id;
      // });
    }
  }
}
</script>
<style lang="scss" scoped>
.content_box {
  width: 100%;
  background-color: #fff;
  padding: 10px;
}

.person_div {
  width: 300px;
  min-height: 32px;
  line-height: 32px;
  appearance: none;
  background-color: #fff;
  background-image: none;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  box-sizing: border-box;
  display: inline-block;
  outline: 0;
  padding: 0 15px;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  color: #121f3e;
}

.detaillClass {
  display: inline-block;
  width: 200px;
}
</style>
