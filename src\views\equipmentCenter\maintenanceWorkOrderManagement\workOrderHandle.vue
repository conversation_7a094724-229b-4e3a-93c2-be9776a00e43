<template>
  <div style="padding-left: 16px; margin-top: 8px;">
    <div class="sino-attached" style="margin-bottom: 10px;">
      <el-button v-if="workOrderType == '1' || workOrderType == '2' || workOrderType == '4'" type="primary" @click="state = '1'">指派 </el-button>
      <el-button v-if="workOrderType == '3'" type="primary" @click="state = 2">挂单</el-button>
      <!-- 只有未派工的工单可以进行取消操作 -->
      <!-- v-if="workOrderType == '1' || workOrderType == '2' " -->
      <el-button v-if="workOrderType == '1' || workOrderType == '2'" type="primary" plain @click="cancelWork()">取消</el-button>
      <el-button
        v-if="workOrderType == '3' || workOrderType == '4'"
        type="primary"
        @click="
          state = 4
          spanPlus()
        "
        >完工</el-button
      >
    </div>

    <div>
      <el-form ref="workItem" :rules="rules" :model="workItem" :inline="true" class="form-inline" label-width="120px">
        <!-- 指派 -->
        <template v-if="state == 1">
          <el-form-item label="维修班组" prop="designateDeptCode">
            <el-select v-model="workItem.designateDeptCode" show-word-limit class="sino_sdcp_input" style="width: 300px;" placeholder="请选择维修班组" @change="HandleTeamChange">
              <el-option v-for="(item, index) in maintenanceTeamList" :key="index" :label="item.team_name" :value="item.id" class="set_zindex"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="本组成员" prop="designatePersonCode">
            <el-select v-model="workItem.designatePersonCode" show-word-limit class="sino_sdcp_input" style="width: 300px;" placeholder="请选择成员">
              <el-option v-for="(item, index) in designatePersonList" :key="index" :label="item.member_name" :value="item.id" class="set_zindex"></el-option>
            </el-select>
          </el-form-item>
          <br />
          <!-- <el-form-item
            label="指派说明"
            class="is-required"
            prop="assignExplain"
          >
            <el-input
              type="textarea"
              :rows="3"
              v-filterSpecialChar
              style="width: 450px"
              class="project-textarea"
              placeholder="请输入指派说明"
              show-word-limit
              v-model="workItem.assignExplain"
              maxlength="200"
            ></el-input>
          </el-form-item> -->
        </template>
        <!-- 挂单 -->
        <template v-if="state == 2">
          <el-form-item label="挂单原因" class="is-required" prop="hangWorkExplain">
            <el-select v-model="workItem.hangWorkExplain" show-word-limit allow-create class="sino_sdcp_input" style="width: 300px;" placeholder="请选择挂单原因">
              <el-option
                v-for="(item, index) in hangWorkExplainList"
                :key="index"
                :label="item.disEntryOrdersReason"
                :value="item.disEntryOrdersReasonCode"
                class="set_zindex"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="其他说明" class="is-required" prop="otherExplain">
            <el-input
              v-model="workItem.otherExplain"
              v-filterSpecialChar
              type="textarea"
              :rows="3"
              style="width: 450px;"
              class="project-textarea"
              placeholder="请输入其他说明"
              show-word-limit
              maxlength="200"
            ></el-input>
          </el-form-item>
          <!-- <el-form-item label="预计解决日期：" prop="endTime">
            <el-date-picker
              :editable="false"
              v-model="workItem.endTime"
              type="date"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
              :picker-options="pickerOptions"
              placeholder="选择日期"
            ></el-date-picker>
          </el-form-item> -->
          <br />
        </template>
        <!-- 取消 -->
        <template v-if="state == 3">
          <el-form-item label="取消原因" class="is-required" prop="cancelReasonId">
            <el-select
              v-model="workItem.cancelReasonId"
              show-word-limit
              allow-create
              class="sino_sdcp_input"
              style="width: 300px;"
              placeholder="取消原因"
              @change="cancelExplainChange"
            >
              <el-option v-for="(item, index) in cancelExplainList" :key="index" :label="item.label" :value="item.value" class="set_zindex"></el-option>
            </el-select>
          </el-form-item>
          <br />
          <el-form-item label="取消说明" prop="cancelExplain">
            <el-input
              v-model="workItem.cancelExplain"
              v-filterSpecialChar
              type="textarea"
              :rows="3"
              style="width: 450px;"
              class="project-textarea"
              placeholder="取消说明"
              show-word-limit
              maxlength="200"
            ></el-input>
          </el-form-item>
          <br />
        </template>
        <!-- 完工 -->
        <template v-if="state == 4">
          <!-- 选择耗材弹窗 -->
          <template v-if="changefactMaterialShow">
            <factMaterial
              ref="changefactMaterial"
              :factMaterialData="factMaterialData"
              :changefactMaterialShow="changefactMaterialShow"
              @factMaterialSure="factMaterialSure"
              @closeDialog="closefactMaterialDialog"
            ></factMaterial>
          </template>
          <el-form-item label="完工时间：" prop="finishTime">
            <el-input v-show="false" v-model="workItem.finishTime"></el-input><span>{{ dateToStr() }}</span>
          </el-form-item>
          <br />
          <el-form-item label="满意度评价：" prop="score">
            <el-rate v-model="workItem.score" show-text text-color="#fff" :texts="rateTexts"> </el-rate>
          </el-form-item>
          <!-- <br />
          <el-form-item label="耗材实际使用：" prop="factMaterialUse" class="fact-materia">
            <div v-for="(factMaterial, ind) in factMaterialList" :key="ind" class="fact-materia-form">
              <div class="fact-materia-first">
                <span v-if="ind === 0" class="span-plus" @click="spanPlus">+</span>
                <span v-else class="span-reduce" @click="spanReduce(ind)">-</span>
                <el-input
                  style="width:120px"
                  v-model="workItem.factMaterialUse[ind]"
                  class="fact-materia-input"
                  readonly="readonly"
                  placeholder="请选择耗材"
                  @focus="materialUsed(ind)"
                ></el-input>
                <el-input v-show="false" v-model="materialId[ind]"></el-input>
                <el-input v-show="false" v-model="factMaterialPrice[ind]"></el-input>
                <el-input v-show="false" v-model="specification[ind]"></el-input>
              </div>
              <div v-if="workItem.factMaterialUse[ind]">
                <span style="margin-left: 15px;">数量：</span>
                <el-input-number v-model="factMaterialNum[ind]" size="mini" :min="1"></el-input-number>
              </div>
            </div>
          </el-form-item> -->
          <br />
          <el-form-item label="完工说明" class="is-required" prop="finishExplain" style="margin-bottom: 24px;">
            <el-input
              v-model="workItem.finishExplain"
              v-filterSpecialChar
              type="textarea"
              :rows="3"
              style="width: 450px;"
              class="project-textarea"
              placeholder="请输入完成说明"
              show-word-limit
              maxlength="200"
            ></el-input>
          </el-form-item>
          <br />
          <el-form-item label="总服务费" prop="completePrice">
            <el-input
              v-model="workItem.completePrice"
              v-filterSpecialChar
              type="text"
              style="width: 150px;"
              placeholder="请输入总服务费"
              onkeyup="value=value.replace(/[^\d-]/g,'')"
              maxlength="10"
            ></el-input>
            <span style="margin-left: 15px;">元</span>
          </el-form-item>
          <br />
          <!-- <el-form-item label="故障原因和维修方法：">
            <div>
              <span style="color: #3562db; cursor: pointer;" @click="changeMalfunction">请选择</span>
              <table v-if="newReason.length" class="maint-table" style="table-layout: fixed; margin-left: 0;">
                <tbody>
                  <tr>
                    <td>故障原因</td>
                    <td>维修方法</td>
                    <td>操作</td>
                  </tr>
                  <tr v-for="(item, index) in newReason" :key="index">
                    <td :title="item.reasonName">
                      <div class="one-line">{{ item.reasonName }}</div>
                    </td>
                    <td>
                      <div
                        v-for="(methodListObj, i) in item.methodName.split(',')"
                        :key="i"
                        class="one-line"
                        :title="methodListObj"
                      >
                        {{ methodListObj }}
                      </div>
                    </td>
                    <td>
                      <div
                        v-for="(methodListObj, i) in item.methodId.split(',')"
                        :key="i"
                        class="one-line scope-del"
                        @click="malDel(methodListObj)"
                      >
                        删除
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </el-form-item> -->
          <!-- <el-form-item label="损坏原因" prop="damageReason">
						<el-input type="textarea" :rows="3" v-filterSpecialChar style="width: 450px"
							class="project-textarea" placeholder="请输入损坏原因" show-word-limit
							v-model="workItem.damageReason" maxlength="200"></el-input>
					</el-form-item> -->
          <!-- <el-form-item label="耗材名称" class="is-required" prop="getConsumable">
						<el-select show-word-limit multiple clearable filterable collapse-tags class="sino_sdcp_input"
							style="width: 300px" v-model="workItem.getConsumable" placeholder="请选择耗材">
							<el-option style="height:auto" class="set_zindex" :value="workItem.faultTypeParentId">
								<el-tree :data="getConsumablesArr" highlight-current show-checkbox ref="tree"
									node-key="id" :props="defaultProps" :current-node-key="workItem.faultTypeParentId"
									:default-checked-keys="[workItem.faultTypeParentId]"
									@check-change="handleCheckChange"></el-tree>
							</el-option>
						</el-select>
					</el-form-item> -->
          <br />
          <el-form-item v-if="consumableList.length > 0">
            <template>
              <el-table :data="consumableList" :border="true" style="width: 100%; display: inline-block;" :cell-style="{ padding: '8px' }">
                <el-table-column type="index" label="序号" width="65"></el-table-column>
                <el-table-column prop="name" label="耗材名称" width="100" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="specification" label="耗材规格" width="150" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="unit" label="耗材单位"></el-table-column>
                <el-table-column prop="accOutNum" label="数量" width="150">
                  <template slot-scope="scope">
                    <el-form :model="scope.row">
                      <el-form-item prop="accOutNum">
                        <el-input v-model="scope.row.accOutNum" v-digitalCommand size="mini" maxlength="10" placeholder="输入耗材数量" />
                      </el-form-item>
                    </el-form>
                  </template>
                </el-table-column>
                <el-table-column prop="price" label="单价" width="150">
                  <template slot-scope="scope">
                    <el-form :model="scope.row">
                      <el-form-item prop="price">
                        <el-input v-model="scope.row.price" v-digitalCommand size="mini" maxlength="10" placeholder="输入耗材单价" />
                      </el-form-item>
                    </el-form>
                  </template>
                </el-table-column>
                <!-- <el-table-column prop="totalPrice" label="总价" width="150">
									<template slot-scope="scope">
										{{scope.row.totalPrice=scope.row.price*scope.row.accOutNum || 0}}
									</template>
								</el-table-column> -->
              </el-table>
            </template>
          </el-form-item>
          <!-- <el-form-item label="其他支出" prop="otherExpensesForDevice">
						<el-input v-digitalCommand style="width: 300px" class="project-textarea" placeholder="请输入其他支出"
							show-word-limit id='other' v-model="workItem.otherExpensesForDevice" maxlength="10">
						</el-input>
					</el-form-item>
					<el-form-item label="维修总支出">
						<span>{{completePrice}}</span>
					</el-form-item> -->
          <div>
            <el-form-item label="图片">
              <el-upload
                ref="uploadFile"
                drag
                multiple
                :class="{ mterial_file: true, disUoloadSty: noneDiv }"
                action="string"
                list-type="picture-card"
                :file-list="fileEcho"
                :http-request="httpRequest"
                accept=".jpg, .jpeg, .png, .JPG, .JPEG, .bmp"
                :limit="9"
                :on-exceed="handleExceed"
                :on-change="imgChange"
                :on-preview="handlePictureCardPreview"
                :beforeUpload="beforeAvatarUpload"
                :on-remove="handleRemove"
              >
                <i class="el-icon-upload"></i>

                <div class="el-upload__text" style="width: 100%; position: absolute; top: 33px;">
                  将文件拖到此处，或
                  <em>点击上传</em>
                </div>
              </el-upload>
              <el-dialog :visible.sync="dialogVisible" style="z-index: 99999;">
                <img width="100%" :src="dialogImageUrl" alt />
              </el-dialog>
            </el-form-item>
          </div>
        </template>
        <!-- <template
          v-if="
            state == 7 || state == 6 || state == 8 || state == 5 || state == 4
          "
        >
          <br />
          <el-form-item label="知会人员" prop="">
            <el-select
              show-word-limit
              allow-create
              class="sino_sdcp_input"
              style="width: 300px"
              v-model="workItem.noticePersonCodeList"
              placeholder="请选择"
              @change="userNameChange"
              clearable
              multiple
              collapse-tags
            >
              <el-option
                v-for="(item, index) in notifyUserList"
                :key="index"
                :label="item.name"
                :value="item.id"
                class="set_zindex"
              ></el-option>
            </el-select>
          </el-form-item>
        </template> -->
      </el-form>
      <!-- <div class="btn_class">
        <el-button
          class="sino-button-sure-search  disabled-button"
          @click="submit(state)"
          >确定</el-button
        >
        <el-button
          class="sino-button-sure-search"
          @click="closeWindow"
          >取消</el-button
        > -->
    </div>
  </div>
</template>

<script>
/* eslint-disable */
import axios from 'axios'
import store from '@/store/index'
import factMaterial from './factMaterial.vue'
const ossPath = 'http://sinomis.oss-cn-beijing.aliyuncs.com/'
export default {
  name: '',
  components: {
    factMaterial
  },
  props: {
    workOrderType: {
      // 工单状态
      type: String,
      default: ''
    },
    designateDeptCode: {
      type: String,
      default: ''
    },
    deviceId: {
      type: String,
      default: ''
    },
    // createById: {
    //   type: String,
    //   default: ''
    // },
    data: {
      type: Object,
      default: {}
    }
  },
  inject: ['closeAndRefresh'],
  data() {
    return {
      cancelExplainList: [],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 3600 * 1000 * 24
        }
      },
      rules: {
        designateDeptCode: [
          {
            required: true,
            trigger: ['blur'],
            message: '请选择维修班组'
          }
        ],
        designatePersonCode: [
          {
            required: true,
            trigger: ['blur'],
            message: '请选择维修成员'
          }
        ],
        hangWorkExplain: [
          {
            required: true,
            trigger: ['blur'],
            message: '请选择挂单原因'
          }
        ],
        otherExplain: [
          {
            required: true,
            trigger: ['blur'],
            message: '请输入其他说明'
          }
        ],
        finishExplain: [
          {
            required: true,
            trigger: ['blur'],
            message: '请输入完工说明'
          }
        ],
        getConsumable: [
          {
            required: true,
            trigger: ['blur'],
            message: '请选择耗材'
          }
        ]
      },
      newReason: [],
      noneDiv: false, // 图片上传框
      limitCountImg: 9, // 上传图片的最大数量
      attachmentFile: [], // 上传图片file
      rateTexts: ['非常差', '差', '一般', '满意', '非常满意'],
      materialId: [], // 耗材id
      factMaterialPrice: [], // 耗材实际价格
      factMaterialData: {},
      specification: [], // 耗材规格
      factMaterialNum: [], // 耗材实际数量
      factMaterialList: 0, // 耗材列表
      workItem: {
        designateDeptCode: '', // 班组id
        designatePersonCode: '', // 被指派人员名称Code
        assignExplain: '', // 指派说明
        hangWorkExplain: '', // 挂单说明code
        otherExplain: '', // 挂单其他说明
        cancelExplain: '', // 取消说明
        taskTeamName: '', // 完工班组名称
        finishExplain: '', // 完工/整改说明
        damageReason: '', // 损坏原因
        attachment: [],
        getConsumable: '', // 完工耗材
        otherExpensesForDevice: '', // 其他支出
        faultTypeParentId: '', //
        userName: '', // 整改人
        noticePersonCodeList: [], // 知会人员id
        noticePersonNameList: [], // 知会人员name
        noticePersonCode: [], // 知会人员id
        noticePersonName: [], // 知会人员name
        endTime: '', // 预计解决时间
        qita: '', // 其他取消说明
        completePrice: '', // 总服务费
        finishTime: '', // 完工时间
        score: 0, // 评分
        factMaterialUse: [] // 耗材使用量
      },
      changefactMaterialShow: false, // 耗材弹窗
      selectFactIndex: 0, // 选中耗材下标
      state: '', // 工单状态
      teamNameList: [],
      notifyUserList: [], // 知会人员列表
      fileEcho: [],
      dialogImageUrl: '',
      dialogVisible: false,
      hangWorkExplainList: [], // 挂单原因
      maintenanceTeamList: [], // 维修班组
      designatePersonList: [], // 班组人员列表
      getConsumablesArr: [], // 完工耗材列表
      consumableList: [], // 选中耗材
      sums: [], // 选中耗材合计
      accOutNum: 0, // 耗材数量
      price: '', // 选中单价
      defaultProps: {
        children: 'list',
        label: 'name'
      }
    }
  },
  computed: {
    // 表格中的金额合计总价格
    // completePrice() {
    //   let count = 0;
    //   let other = parseFloat(this.workItem.otherExpensesForDevice);
    //   if (other) {
    //     count += other;
    //     for (let item of this.consumableList) {
    //       count += (item.accOutNum || 0) * (item.price || 0);
    //     }
    //     return count ? count : 0;
    //   } else {
    //     for (let item of this.consumableList) {
    //       count += (item.accOutNum || 0) * (item.price || 0);
    //     }
    //     return count ? count : 0;
    //   }
    // }
  },
  watch: {
    designateDeptCode(n, o) {
      this.workItem.designateDeptCode = n
      this.getStaffListByTeamIdFn(n)
    },
    deviceId(n, o) {
      this.deviceId = n
    }
    // createById(n, o) {
    //   this.createById = n
    // }
  },
  created() {},
  mounted() {
    this.getDictValueFn()
    this.getTeamIdListFn()
    this.getConsumablesFn()
    this.getStaffListByTeamIdFn()
    // this.getStaffListByTeamIdFn('0ba947234d5e42158a7f0155a16aa884')
  },
  methods: {
    changeMalfunction() {
      this.itemServiceCodeToMal = this.data.wxDetail[0][8]
      this.changeMalfunctionShow = true
    },
    closefactMaterialDialog() {
      this.changefactMaterialShow = false
    },
    factMaterialSure(item) {
      // console.log(item)
      if (this.materialId.includes(item.id)) {
        return this.$message({
          message: '耗材选择重复！',
          type: 'warning'
        })
      }
      const index = this.selectFactIndex
      this.workItem.factMaterialUse[index] = item.depProductName
      this.materialId[index] = item.id
      this.factMaterialPrice[index] = item.price
      this.specification[index] = item.specification
      this.factMaterialNum[index] = 0
      this.changefactMaterialShow = false
    },
    spanReduce(ind) {
      if (this.factMaterialList === 1) {
        return false
      }
      this.workItem.factMaterialUse.splice(ind, 1)
      this.materialId.splice(ind, 1)
      this.factMaterialPrice.splice(ind, 1)
      this.specification.splice(ind, 1)
      this.factMaterialNum.splice(ind, 1)
      this.factMaterialList--
    },
    // 增加 耗材
    spanPlus() {
      this.workItem.factMaterialUse.push('')
      this.materialId.push('')
      this.factMaterialPrice.push('')
      this.specification.push('')
      this.factMaterialNum.push(0)
      this.factMaterialList++
    },
    materialUsed(index) {
      this.changefactMaterialShow = true
      this.selectFactIndex = index
      // this.formInline.factMaterialUse[index] = index.toString()
      this.$forceUpdate()
    },
    dateToStr() {
      var dateTime = new Date()
      var year = dateTime.getFullYear()
      var month = dateTime.getMonth() + 1
      var date = dateTime.getDate()
      var hour = dateTime.getHours()
      var minutes = dateTime.getMinutes()
      var second = dateTime.getSeconds()
      if (month < 10) {
        month = '0' + month
      }
      if (date < 10) {
        date = '0' + date
      }
      if (hour < 10) {
        hour = '0' + hour
      }
      if (minutes < 10) {
        minutes = '0' + minutes
      }
      if (second < 10) {
        second = '0' + second
      }
      return year + '-' + month + '-' + date + ' ' + hour + ':' + minutes + ':' + second
    },
    getDictValueFn() {
      this.$api.getDictValue2({}).then((res) => {
        this.hangWorkExplainList = res.data
      })
    },
    /**
     * @description: 获取维修班组
     */
    getTeamIdListFn() {
      // this.$api.getTeamIdList({
      // 	iteamCodeList: ''
      // }).then(res => {
      // 	this.maintenanceTeamList = res.data;
      // });
      this.$api.getDataByTypeTeam({}).then((res) => {
        this.maintenanceTeamList = res.data.list
      })
      this.getStaffListByTeamIdFn(this.workItem.designateDeptCode)
    },
    // 维修班组切换
    HandleTeamChange(val) {
      this.workItem.designatePersonCode = ''
      this.getStaffListByTeamIdFn(val)
    },
    /**
     * @description: 获取指派人员
     * @param {*} {TeamCodeList}
     */
    getStaffListByTeamIdFn(TeamCode) {
      this.$api
        .getServicePersonName({
          id: this.workItem.designateDeptCode
        })
        .then((res) => {
          this.designatePersonList = res.data.list
        })
      // if (TeamCode) {
      // 	this.$api.getStaffListByTeamId({
      // 		TeamCodeList: TeamCode
      // 	}).then(res => {
      // 		this.designatePersonList = res.data;
      // 	});
      // }
    },
    // 获取耗材列表
    getConsumablesFn() {
      this.$api
        .getConsumables({ workTypeCode: '17' })
        .then((res) => {
          if (res.code == 200) {
            let list = this.$tools.transData(res.data, 'id', 'parentId', 'list')
            this.getConsumablesArr = list
          } else {
            this.$message.warning(res.message)
          }
        })
        .catch(() => {
          // this.blockLoading = this.$store.state.loadingShow;
        })
    },
    /**
     * @description: 选中耗材切换触发方法
     * @param {*} data
     * @param {*} checked
     * @param {*} indeterminate
     */
    handleCheckChange(data, checked, indeterminate) {
      if (data.nodeLevel == 3) {
        if (checked) {
          // 第三级才去赋值
          this.workItem.faultTypeParentId = data.id
          // this.workItem.faultType=e.name;
          this.consumableList.push(data)
          this.rules.getConsumable[0].required = false
        } else {
          this.workItem.faultTypeParentId = ''
          this.consumableList.forEach((item, index) => {
            if (item == data) {
              this.consumableList.splice(index, 1)
            }
          })
          if (this.consumableList.length == 0) {
            this.rules.getConsumable[0].required = true
          }
        }
      }
    },
    handleSubmit() {
      console.log('state', this.state)
      this.submit()
    },
    // 提交方法
    submit() {
      let state = this.state
      this.$refs['workItem'].validate((valid) => {
        if (valid) {
          const userInfo = store.state.user.userInfo.user
          let data
          const { id, staffName } = store.state.user.userInfo.user
          const { deptCode } = JSON.parse(localStorage.getItem('userInfo')).teamId
          if (!state) return this.$message.warning('请先选择操作')
          if (state == '1') {
            // 指派
            let _index2 = this.maintenanceTeamList.findIndex((item) => {
              return item.id == this.workItem.designateDeptCode
            })
            _index2 = _index2 || 0
            let designateDeptCode = this.workItem.designateDeptCode || this.$parent.designateDeptCode
            let designateDeptName = this.maintenanceTeamList[_index2].team_name || this.$parent.designateDeptName
            let _indexP = this.designatePersonList.findIndex((item) => {
              return item.id == this.workItem.designatePersonCode
            })
            _indexP = _indexP || 0
            data = {
              id: this.data.id,
              designateDeptCode: designateDeptCode, // 维修班组code,
              designateDeptName: designateDeptName, // 维修班组name,
              designatePersonCode: this.designatePersonList[_indexP].id, // 被指派人ID（认领传自己）
              designatePersonName: this.designatePersonList[_indexP].member_name, // 被指派人名称（认领传自己）
              designatePersonPhone: this.designatePersonList[_indexP].phone, // 被指派人手机（认领传自己）
              disDesignateByCode: id, // 指派人ID（认领传自己）
              disDesignateByName: staffName, // 指派人名称（认领传自己）
              disDesignateByJobnum: '', //	指派人工号（认领传自己）
              disDesignateByDeptCode: deptCode, // 调度员的所属部门ID
              disDesignateByDeptName: '' // 调度员的所属部门名称
            }
            this.$store.commit('changeNetworkError', '报修保存失败，请检查您的网络…')
            this.blockLoading = true
            this.$api
              .dispatchingTheWorkOrder(data)
              .then((res) => {
                if (res.code == 200) {
                  this.$message.success(res.message)
                  this.closeWindow() // 关闭页面
                } else {
                  this.$message.warning(res.message)
                }
              })
              .catch(() => {
                // this.blockLoading = this.$store.state.loadingShow;
              })
          } else if (state == '2') {
            // 挂单
            let _index = this.hangWorkExplainList.findIndex((item) => {
              return item.disEntryOrdersReasonCode == this.workItem.hangWorkExplain
            })
            _index = _index || 0
            data = {
              id: this.data.id, // 工单ID
              disEntryOrdersReasonCode: this.workItem.hangWorkExplain, // 挂单原因code
              disEntryOrdersReason: this.hangWorkExplainList[_index].disEntryOrdersReason, // 挂单原因name
              disEntryOrdersSolution: this.workItem.otherExplain, // 其他说明
              createById: id, // 挂单人ID
              createByName: staffName, // 挂单人
              createByJobnum: '' // 挂单人工号
            }
            this.$api
              .suspendTheWorkOrder(data)
              .then((res) => {
                if (res.code == 200) {
                  this.$message.success(res.message)
                  this.closeWindow() // 关闭页面
                } else {
                  this.$message.warning(res.message)
                }
              })
              .catch(() => {
                // this.blockLoading = this.$store.state.loadingShow;
              })
          } else if (state == '3') {
            // 取消
            this.handleCancelWork()
          } else {
            this.consumableList.forEach((list) => {
              list.depId = list.id
              list.depName = list.name
            })
            data = {
              id: this.data.id, // 工单ID
              //   finishTime: this.workItem.finishTime, //完工时间
              //   factMaterialUse: this.workItem.factMaterialUse, //实际耗材使用
              //   materialId: this.workItem.materialId || "", //耗材ID
              //   factMaterialPrice: this.factMaterialPrice,
              //   specification: this.specification,
              //   factMaterialNum: this.factMaterialNum,
              operFlag: '1',
              operType: 'operatorFinish',
              finishRemark: this.workItem.finishExplain,
              score: this.workItem.score,
              completePrice: this.workItem.completePrice,
              // disAttachmentUpload: this.workItem.attachment.toString(),
              attachmentUrl: this.workItem.attachment.toString(),
              //   userId: userInfo.staffId,
              //   userName: userInfo.staffName,
              officeName: '',
              disFlowcode: '',
              //   operSource: this.data.operSource,
              //   print: this.data.print,
              workTypeCode: '17',
              flowcode: this.data.flowCode,
              workNum: this.data.workNum
              //   damageCause: this.workItem.damageReason, //损坏原因
              //   otherExpenses: this.workItem.otherExpensesForDevice, //其他支出
              //   repairPay: this.completePrice, // 维修总支出
              //   repairFinishExplain: this.workItem.finishExplain, //维修说明
              //   finishAttachmentUrl: this.workItem.attachment.toString(),
              //   repairPersonCode: id, //完工人ID
              //   repairPersonName: name, //完工人
              //   repairPersonJobNum: "", //报修人工号
              //   repairSources: "2", //维修来源
              //   sysFlag: "imes",
              //   equipmentId: this.deviceId,
              //   createPersonId: this.createById, //报修人
              //   consumableListJson: JSON.stringify(this.consumableList) //完工耗材
            }
            // return console.log(data);
            this.$api
              .workOrderOperOrder(data)
              .then((res) => {
                console.log(res)
                if (res.success) {
                  this.$message.success(res.msg)
                  this.closeWindow() // 关闭页面
                } else {
                  this.$message.warning(res.msg)
                }
              })
              .catch(() => {
                // this.blockLoading = this.$store.state.loadingShow;
              })
          }
        } else {
          this.$tools.focusFunc()
        }
      })
    },
    // 取消工单
    cancelWork() {
      this.state = '3'
      this.getIomsDictList('cancel_reason')
    },
    handleCancelWork() {
      const userInfo = store.state.user.userInfo.user
      const params = {
        userId: userInfo.staffId,
        userName: userInfo.staffName,
        officeName: '',
        disFlowcode: '',
        id: this.data.id,
        operSource: this.data.operSource,
        print: this.data.print,
        workTypeCode: this.data.workTypeCode,
        flowcode: this.data.flowcode,
        workNum: this.data.workNum,
        operType: 'cancelOrder',
        cancelReasonId: this.workItem.cancelReasonId,
        cancelExplain: this.workItem.cancelExplain
      }
      this.$api
        .placeAndCancelOrder(params)
        .then((res) => {
          if (res.success) {
            this.$message.success('工单取消成功！')
            this.closeWindow() // 关闭页面
          } else {
            this.$message.warning(res.message)
          }
        })
        .catch(() => {
          // this.blockLoading = this.$store.state.loadingShow;
        })
    },
    // 选择取消原因
    cancelExplainChange(val) {
      // console.log(this.workItem.cancelExplain);
    },
    switchingOperation(i) {
      this.state = i
      this.workItem.map((v) => {
        return (v = '')
      })
    },
    //  ------------------------------------------上传图片相关-----------------------------------------
    httpRequest(item) {
      this.attachmentFile.push(item.file)
      let formData = new FormData()
      let cacheInfo = JSON.parse(localStorage.getItem('userInfo'))
      formData.append('hospitalCode', store.state.user.userInfo.user.hospitalCode)
      formData.append('file', item.file)
      axios
        .post(__PATH.VUE_IOMS_API + '/minio/upload', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })
        .then((res) => {
          console.log(res)
          this.workItem.attachment.push(res.data.data.picUrl)
        })
        .catch(() => {})
    },
    handleExceed() {
      this.$message.error('最多上传九份文件')
    },
    beforeAvatarUpload(file) {
      const isLt40M = file.size / 1024 / 1024 < 40
      if (!isLt40M) {
        this.$message.error('上传图片大小不能超过 40MB!')
        return false
      }
      if (file.name.indexOf(',') != -1) {
        this.$message.error('非法的文件名')
        return false
      }
    },
    imgChange(file, fileList) {
      this.noneDiv = fileList.length >= this.limitCountImg
    },
    handleRemove(file, fileList) {
      this.noneDiv = fileList.length >= this.limitCountImg
      let index = this.attachmentFile.findIndex((item) => {
        return item.uid == file.uid
      })
      this.workItem.attachment.splice(index, 1)
      // fileList.forEach((item) => {
      //   this.workItem.attachment.push(item.raw);
      // });
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    /**
     * @description: 关闭页面
     */
    closeWindow() {
      this.closeAndRefresh()
    },
    // 知会人员
    userNameChange(val) {
      let name = []
      for (let i of val) {
        this.notifyUserList.map((v) => {
          if (i == v.id) {
            return name.push(v.name)
          }
        })
      }
      this.workItem.noticePersonNameList = name
    },
    getIomsDictList(type) {
      const params = {
        type: type
      }
      this.$api.getIomsDictList(params).then((res) => {
        this.cancelExplainList = res
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.sino-button-sure-search {
  border: 1px solid #5288fc;
}

.disabled-button {
  background-color: #77affd;
  border-color: #77affd;
}

.sure:focus {
  background-color: #5288fc;
  border-color: #5288fc;
  color: #fff;
}

.fact-materia {
  ::v-deep .el-form-item__content {
    flex-direction: column;
  }

  .fact-materia-form {
    display: flex;
    margin-bottom: 15px;

    > .el-input,
    > div {
      width: 40%;
    }

    .fact-materia-first {
      padding-left: 15px;
      position: relative;

      .span-plus {
        position: absolute;
        top: 0;
        left: -10px;
        font-size: 24px;
        color: #3562db;
        cursor: pointer;
      }

      .span-reduce {
        position: absolute;
        top: 0;
        left: -10px;
        font-size: 32px;
        color: #f00;
        cursor: pointer;
      }
    }

    ::v-deep .el-input-number--mini {
      width: 6rem;
    }
  }
}

::v-deep .el-form-item__error {
  top: 100%;
}

::v-deep .el-rate {
  transform: translateY(10px);
}
</style>
