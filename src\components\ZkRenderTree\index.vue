<!--
 * @Author: hedd
 * @Date: 2024-03-22 14:38:18
 * @LastEditTime: 2024-08-20 11:43:39
 * @FilePath: \ihcrs_pc\src\components\ZkRenderTree\index.vue
 * @Description: 主要用于树形组件的渲染,使用方式和el-tree一样,如果需要自定义渲染,可以使用slot插槽
 * @Description: 增加了文字过长显示tooltip的功能
 * @Description: 由于ref无法当做参数传给子组件父组件调用的时候需要通过$refs[].getTreeRef()来获取子组件的方法
-->
<template>
  <div class="custom-tree">
    <el-tree ref="ZkRenderTree" :render-content="renderContent" v-bind="$attrs" :style="$attrs.style || {}" v-on="$listeners"></el-tree>
  </div>
</template>
<script lang="jsx">
export default {
  name: 'ZkRenderTree',
  data() {
    return {
      isShowTooltip: false,
      renderContent: function () {}
    }
  },
  mounted() {
    this.renderContent = this.renderContentEvent
    const entries = Object.entries(this.$attrs)
    for (const [key, value] of entries) {
      // this[key] = value
      if (key == 'render-content') {
        this.renderContent = value
      }
    }
    // const listeners = Object.entries(this.$listeners)
    // for (const [event, handler] of listeners) {
    //   this.$on(event, handler)
    // }
    // const customRef = this.$refs.ZkRenderTree
    // for (const key in customRef) {
    //   this[key] = customRef[key]
    // }
  },
  methods: {
    getTreeRef() {
      return this.$refs.ZkRenderTree
    },
    renderContentEvent(h, { node, data, store }) {
      if (this.$scopedSlots.default) {
        return this.$scopedSlots.default({ node, data })
      } else {
        return (
          <span class="custom-tree-node">
            <el-tooltip class="item" effect="dark" content={node.label} disabled={!this.isShowTooltip} placement="top-start">
              <span
                onMouseenter={(e) => {
                  this.visibilityChange(e)
                }}
              >
                {node.label}
              </span>
            </el-tooltip>
          </span>
        )
      }
    },
    visibilityChange(e) {
      const item = e.target
      const itemWidth = item.offsetWidth
      const itemScrollWidth = item.scrollWidth
      if (itemWidth < itemScrollWidth) {
        this.isShowTooltip = true
      } else {
        this.isShowTooltip = false
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.custom-tree {
  ::v-deep .custom-tree-node {
    overflow: hidden;
    & > span {
      max-width: 100%;
      display: block;
      align-items: center;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      color: #121f3e;
      font-size: 14px;
      font-family: 'PingFang SC-Regular', 'PingFang SC';
    }
  }
  ::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
    background-color: #d9e1f8 !important;
  }
  ::v-deep .el-tree-node__content {
    height: 38px;
    .el-tree-node__label {
      font-size: 15px;
    }
  }
}
</style>
