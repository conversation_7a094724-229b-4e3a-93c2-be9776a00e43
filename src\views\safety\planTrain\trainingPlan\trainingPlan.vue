<template>
  <PageContainer>
    <div slot="content" class="role-content" style="height: 100%">
      <div class="content_top">
        <el-tabs @tab-click="handleClick" class="tabsMenu">
          <el-tab-pane v-for="(item, index) in tabsList" :key="index" :label="item.name"
            :class="{ active: activeIndex == index }"></el-tab-pane>
        </el-tabs>
        <!-- 培训任务 -->
        <div class="content_box" style="margin-top: 15px;" v-if="activeIndex == 0">
          <div class="open_conter">
            <div class="seachTop">
              <el-input v-model="seachForm.name" style="width: 200px; margin-right:16px" placeholder="请输入计划名称">
              </el-input>
              <el-cascader ref="myCascader" v-model="seachForm.deptId" :options="deptList" :props="deptTree"
                placeholder="请选择所属部门" clearable style="width: 200px; margin-right: 10px"></el-cascader>
              <el-input v-model="seachForm.studentName" style="width: 200px; margin-right:16px" placeholder="请输入培训老师">
              </el-input>
              <el-date-picker v-model="timeLine" type="daterange" value-format="yyyy-MM-dd" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" style="width: 260px; margin: 0 16px">
              </el-date-picker>
              <el-select v-model="seachForm.trainStatus" placeholder="请选择计划状态" style="width: 260px; margin: 0 16px">
                <el-option v-for="item in statusList" :key="item.id" :label="item.label" :value="item.id"></el-option>
              </el-select>
              <el-button type="primary" plain @click="resetFormConferen">重置</el-button>
              <el-button type="primary" @click="searchConferen">查询</el-button>
            </div>
            <div>
              <el-button type="primary" @click="addTrain('train')">创建培训计划</el-button>
              <el-button type="primary" :disabled="!multipleSelectionTrain.length >= 1"
                @click="selcetDelectTrain">批量删除</el-button>
            </div>
            <div class="table">
              <el-table v-loading="tableLoading" :data="trainData" tyle="width: 100%;" height="100%" border stripe
                title="双击查看详情" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" align="center">
                </el-table-column>
                <el-table-column prop="name" label="培训计划名称" width="120" show-overflow-tooltip
                  align="center"></el-table-column>
                <el-table-column prop="deptName" label="所属单位" show-overflow-tooltip align="center"></el-table-column>
                <el-table-column label="培训频率" width="120" show-overflow-tooltip align="center">
                  <template slot-scope="scope">
                    <span>{{ scope.row.frequency == 0 ? '全年' : scope.row.frequency == 1 ? '单次' : scope.row.frequency ==
          2
          ? '每周' : scope.row.frequency == 3 ? '每月' : scope.row.frequency == 4 ? '每季度' : '每半年' }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="计划有效时间" show-overflow-tooltip align="center">
                  <template slot-scope="scope">
                    <span>{{ scope.row.startTime }} - {{ scope.row.endTime }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="studentName" label="培训部门人员" width="120" show-overflow-tooltip
                  align="center"></el-table-column>
                <el-table-column prop="principal" label="培训负责人" show-overflow-tooltip align="center"></el-table-column>
                <el-table-column prop="trainStatus" label="计划状态" show-overflow-tooltip align="center">
                  <template slot-scope="scope">
                    <span>{{ scope.row.trainStatus == 0 ? '未发布' : scope.row.trainStatus == 1 ? '审核中' :
          scope.row.trainStatus == 2 ? '已发布' : '驳回' }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="enable" label="启用状态" show-overflow-tooltip align="center">
                  <template slot-scope="scope">
                    <el-switch v-model="scope.row.enable" active-color="#1890ff" inactive-color="#dcdfe6"
                      active-value="0" inactive-value="1" @change="changeStatus(scope.row)">
                    </el-switch>
                  </template>
                  <!-- <template slot-scope="scope">
                    
                    <el-switch v-model="scope.row.enable"  active-color="#1890ff"  inactive-color="#dcdfe6"  @change="changeStatus($event, scope.row)" ></el-switch>
                    
                  </template> -->
                </el-table-column>
                <el-table-column prop="examName" label="考试内容" show-overflow-tooltip align="center"></el-table-column>
                <el-table-column label="操作" width="220" show-overflow-tooltip align="center">
                  <template slot-scope="scope">
                    <el-button type="text" v-if="scope.row.trainStatus == 0 || scope.row.trainStatus == 1"
                      @click="openPublish(scope.row, 'train')">发布</el-button>
                    <el-button type="text" @click="openCopy(scope.row, 'train')">复制</el-button>
                    <el-button type="text" @click="openTrainDetail(scope.row)">查看</el-button>
                    <el-button type="text" @click="trainEdit(scope.row, 'trainEdit')">编辑</el-button>
                    <el-button type="text" @click="delectTrain(scope.row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="contentTable-footer">
              <el-pagination style="margin-top: 3px" :current-page="paginationData.pageNo"
                layout="total, sizes, prev, pager, next, jumper" :total="paginationData.total"
                :page-size="paginationData.pageSize" :page-sizes="[15, 30, 50, 100]" @size-change="handleSizeChange"
                @current-change="handleCurrentChange"></el-pagination>
            </div>
          </div>
        </div>
        <!-- 会议计划 -->
        <div class="content_box" v-if="activeIndex == 1">
          <template>
            <PageContainer>
              <div slot="header">
                <div class="search-header">
                  <div class="search-from">
                    <el-input v-model="conferenForm.name" style="width: 200px; margin-right:16px" placeholder="会议计划名称">
                    </el-input>
                    <el-cascader v-model="conferenForm.organizeDept" placeholder="请选择所属部门" :options="deptList"
                      :props="deptTree" :show-all-levels="false" clearable filterable collapse-tags
                      style="width: 200px; margin-right: 10px">
                    </el-cascader>
                    <el-select v-model="conferenForm.conferenceStatus" placeholder="请选择会议状态">
                      <el-option v-for="item in statusList" :key="item.id" :label="item.label"
                        :value="item.id"></el-option>
                    </el-select>
                    <el-date-picker v-model="timeLine" type="daterange" value-format="yyyy-MM-dd" range-separator="至"
                      start-placeholder="开始日期" end-placeholder="结束日期" style="width: 260px; margin: 0 16px">
                    </el-date-picker>
                    <el-button type="primary" plain @click="resetFormConferen">重置</el-button>
                    <el-button type="primary" @click="searchConferen">查询</el-button>
                  </div>
                  <div>
                    <el-button type="primary" @click="addConferences('addCon')">创建会议计划</el-button>
                    <el-button type="primary" :disabled="!multipleSelectionConferen.length >= 1"
                      @click="batchDetel">批量删除</el-button>
                  </div>
                </div>
              </div>
              <div slot="content" style="height: 100%; background-color: #fff">
                <div class="table-content">
                  <el-table v-loading="tableLoading" :data="conferenceData" style="width: 100%;" height="100%" border
                    stripe title="双击查看详情" @selection-change="handleSelectionChangeConferen">
                    <el-table-column type="selection" width="55" align="center">
                    </el-table-column>
                    <el-table-column prop="name" label="会议计划名称" show-overflow-tooltip align="center"></el-table-column>
                    <el-table-column prop="organizeDeptName" label="组会部门" align="center"></el-table-column>
                    <el-table-column label="会议时间" width="200" show-overflow-tooltip align="center">
                      <template slot-scope="scope">
                        <span>{{ scope.row.startTime }} - {{ scope.row.endTime }}-{{ scope.row.conferenceStartTime }} - {{ scope.row.conferenceEndTime }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="address" label="参会部门人员" show-overflow-tooltip align="center">
                      <template slot-scope="scope">
                        <span>{{ scope.row.stuOrOrg == 0 ? '线上会议' : '线下会议' }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="principalName" label="会议负责人" show-overflow-tooltip
                      align="center"></el-table-column>
                    <el-table-column prop="conferenceStatus" label="计划状态" show-overflow-tooltip align="center">
                      <template slot-scope="scope">
                        <span>{{ scope.row.conferenceStatus == 0 ? '未发布' : scope.row.conferenceStatus == 1 ? '审核中' :
          scope.row.conferenceStatus == 2 ? '已发布' : '驳回' }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="address" label="操作" show-overflow-tooltip align="center">
                      <template slot-scope="scope">
                        <el-button type="text" v-if="scope.row.conferenceStatus == 0 || scope.row.conferenceStatus == 1"
                          @click="openPublish(scope.row, 'conference')">发布</el-button>
                        <el-button type="text" @click="confereceEdit(scope.row, 'edit')">编辑</el-button>
                        <el-button type="text" @click="examineConference(scope.row)">查看</el-button>
                        <el-button type="text" @click="conferenceDelet(scope.row)">删除</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <div class="contentTable-footer">
                    <el-pagination style="margin-top: 3px" :current-page="paginationData.pageNo"
                      layout="total, sizes, prev, pager, next, jumper" :total="paginationData.total"
                      :page-size="paginationData.pageSize" :page-sizes="[15, 30, 50, 100]"
                      @size-change="handleSizeChange" @current-change="handleCurrentChange"></el-pagination>
                  </div>
                </div>
              </div>
            </PageContainer>
          </template>
        </div>
      </div>
    </div>
  </PageContainer>
</template>

<script type="text/ecmascript-6">
import { Alert } from 'element-ui';
import moment from 'moment'
export default {
  name: 'feedbackRecord',
  components: {},
  data() {
    return {
      moment,
      conferenForm: {
        name: '',
        organizeDept: '',
        conferenceStatus: '',
        startTime: '',
        endTime: '',
      },
      trainData: [], // 培训任务列表
      conferenceData: [], // 会议任务列表
      tableLoading: false,
      routeInfo: '',
      activeName: 'train',
      props: {
        children: "childList",
        label: "name",
        value: "id",
        checkStrictly: true,
      },
      seachForm: { // 培训计划筛选
        name: "",
        deptId: "",
        teacherName: '',
        trainStatus: '',
        startTime: '',
        endTime: ''
      },
      deptTree: {
        children: "children",
        label: "teamName",
        value: "id",
        checkStrictly: true,
        emitPath: false,
      },
      tabsList: [
        {
          name: '培训计划',
          value: '0',
        },
        {
          name: '会议计划',
          value: '1',
        }
      ],
      deptList: [],
      timeLine: [],
      statusList: [
        {
          id: '0',
          label: '未发布'
        },
        {
          id: '1',
          label: '审核中'
        },
        {
          id: '2',
          label: '已发布'
        },
        {
          id: '3',
          label: '驳回 '
        },
      ],
      paginationData: {
        pageNo: 1,
        pageSize: 15,
        total: 0
      },
      type: '',
      trainType: '',  // 点击培训任务弹窗状态
      activeIndex: 0,
      tableData: [],
      confereceIds: [], // 删除会议
      routeInfo: "",
      publishType: '', // 发布状态
      multipleSelectionConferen: [], // 选中会议计划表格数据
      multipleSelectionTrain: [], // 选中培训计划表格数据
    }
  },
  created() {
    if (this.$route.query) {
      sessionStorage.setItem("routeInfo", JSON.stringify(this.$route.query));
      this.routeInfo = JSON.parse(sessionStorage.getItem("routeInfo"));
    }
    this.getdeptList()
    this.routeInfo = JSON.parse(sessionStorage.getItem("routeInfo"));
    this.getTrainList() // 培训任务列表
    // this.getTonferenceList() // 会议记录列表
  },
  watch: {
    timeLine(val) {
      if (val.length > 0) {
        this.seachForm.startTime = val[0];
        this.seachForm.endTime = val[1];
      } else {
        this.seachForm.startTime = "";
        this.seachForm.endTime = "";
      }
    },
  },
  methods: {
    // 发布
    openPublish(row, publishType) {
      this.publishType = publishType
      if (this.publishType == 'train') {
        this.$api.trainPublish({ id: row.id }).then(res => {
          if (res.code == 200) {
            this.$message.success(res.msg)
            this.getTrainList()
          } else {
            this.$message.error(res.msg)
          }
        })
      } else if (this.publishType == 'conference') {
        this.$api.conferecePublish({ id: row.id }).then(res => {
          if (res.code == 200) {
            this.$message.success(res.msg)
            this.getTonferenceList()
          } else {
            this.$message.error(res.msg)
          }
        })
      }
    },
    // 复制
    openCopy(row, publishType) {
      this.publishType = publishType
      if (this.publishType == 'train') {
        this.$api.trainCopy({ id: row.id }).then(res => {
          if (res.code == 200) {
            this.$message.success(res.msg)
            this.getTrainList()
          } else {
            this.$message.error(res.msg)
          }
        })
      }
    },
    // 获取组织
    getdeptList() {
      
      this.$api.getDeptListLaboratory({}).then((res) => {
        this.deptList = this.$tools.transData(
          res.data.list,
          "id",
          "parentId",
          "children"
        );
      });
    },
    // ---------------------------------------------------------------- 培训任务 -------------------------------------------------------------------------------------------
    // 培训列表
    getTrainList() {
      let data = {
        current: this.paginationData.pageNo,
        size: this.paginationData.pageSize,
        ...this.seachForm,
      }
      this.$api.trainPlanList(data).then(res => {
        if (res.code == 200) {
          this.trainData = res.data.list
          this.paginationData.total = res.data.total
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },
    handleSelectionChange(val) {
      this.multipleSelectionTrain = val
    },
    // 创建培训计划
    addTrain(trainType) {
      this.trainType = trainType
      this.$router.push({
        path: '/addTrainPlan',
        query: {
          type: this.trainType,
        }
      })
    },
    // 编辑培训计划
    trainEdit(row, trainType) {
      this.trainType = trainType
      this.$router.push({
        path: '/addTrainPlan',
        query: {
          id: row.id,
          type: this.trainType,
        }
      })
    },
    // 查看培训计划详情
    openTrainDetail(row) {
      this.$router.push({
        path: '/trainPlanDetail',
        query: {
          id: row.id,
        }
      })
    },
    // 删除培训计划
    delectTrain(row) {
      this.params = []
      this.params.push(row.id)
      this.$confirm("删除后将无法恢复，是否确定删除？", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$api.trainPlanDelet({ ids: this.params }).then(res => {
          if (res.code == 200) {
            this.$message.success(res.msg)
            this.getTrainList()
          } else {
            this.$message.error(res.msg)
          }
        })
      });
    },
    //批量删除培训计划
    selcetDelectTrain() {
      this.paramsIds = []
      this.multipleSelectionTrain.map((item) => {
        this.confereceIds.push(item.id)
        this.paramsIds = this.confereceIds
      })
      this.$confirm('确定要删除当前培训计划吗，删除后将无法恢复，是否确定删除？', "提醒", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        cancelButtonClass: "btn-cancel",
        type: "warning"
      }).then(res => {
        this.$api.trainPlanDelet({ ids: this.paramsIds }).then(res => {
          if (res.code == 200) {
            this.$message.success(res.msg)
            this.getTrainList()
          } else {
            this.$message.error(res.msg)
          }
        })
      })
    },
    // 列表启用状态修改
    changeStatus(row) {
      this.$api.trainOnOrOff({ id: row.id, enable: row.enable }).then((res) => {
        if (res.code == 200) {
          this.$message.success(res.msg)
          this.getTrainList()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // -------------------------------------------------------------- 会议记录 ---------------------------------------------------------------------------------------------
    // 会议记录列表
    getTonferenceList() {
      let data = {
        current: this.paginationData.pageNo,
        size: this.paginationData.pageSize,
        ...this.conferenForm,
      }
      this.$api.confereceList(data).then(res => {
        if (res.code == 200) {
          this.conferenceData = res.data.list
          this.paginationData.total = res.data.total
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },
    // 查看会议计划详情
    examineConference(row) {
      this.$router.push({
        path: '/conferencePlanDetail',
        query: {
          id: row.id,
        }
      })
    },
    // 创建会议记录
    addConferences(publishType) {
      this.publishType = publishType
      this.$router.push({
        path: '/addConferencePlan',
        query: {
          type: this.publishType,
        }
      })
    },
    // 编辑会议计划
    confereceEdit(row, publishType) {
      this.publishType = publishType
      this.$router.push({
        path: '/addConferencePlan',
        query: {
          id: row.id,
          type: this.publishType,
        }
      })
    },
    // 切换tabs
    handleClick(tab, event) {
      this.activeIndex = tab.index
      if (this.activeIndex == 0) {
        this.getTrainList() // 培训任务列表
      } else if (this.activeIndex == 1) {
        this.getTonferenceList()
      }
    },
    handleSelectionChangeConferen(val) {
      this.multipleSelectionConferen = val
    },
    // 删除会议
    conferenceDelet(row) {
      this.params = []
      this.params.push(row.id)
      this.$confirm("删除后将无法恢复，是否确定删除？", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$api.confereceDelet({ ids: this.params }).then(res => {
          if (res.code == 200) {
            this.$message.success(res.msg)
            this.getTonferenceList()
          } else {
            this.$message.error(res.msg)
          }
        })
      });
    },
    // 批量姗迟
    batchDetel() {
      this.paramsIds = []
      this.multipleSelectionConferen.map((item) => {
        this.confereceIds.push(item.id)
        this.paramsIds = this.confereceIds
      })
      this.$confirm('确定要删除当前会议计划吗，删除后将无法恢复，是否确定删除？', "提醒", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        cancelButtonClass: "btn-cancel",
        type: "warning"
      }).then(res => {
        this.$api.confereceDelet({ ids: this.paramsIds }).then(res => {
          if (res.code == 200) {
            this.$message.success(res.msg)
            this.getTonferenceList()
          } else {
            this.$message.error(res.msg)
          }
        })
      })
    },
    // 会议计划筛选
    searchConferen() {
      this.paginationData.pageNo = 1
      if (this.activeIndex == 0) {
        this.getTrainList()
      } else {
        this.getTonferenceList()
      }
    },
    resetFormConferen() {
      this.paginationData.pageNo = 1
      this.paginationData.pageSize = 15
      this.timeLine = []
      if (this.activeIndex == 0) {
        this.seachForm = { // 培训计划筛选
          name: "",
          deptId: "",
          teacherName: '',
          trainStatus: '',
          startTime: '',
          endTime: ''
        },
          this.getTrainList()
      } else {
        this.conferenForm = {
          name: '',
          organizeDept: '',
          conferenceStatus: '',
          startTime: '',
          endTime: '',
        }
        this.getTonferenceList()
      }

    },
    search() {
      this.paginationData.pageNo = 1
      this.getPlanList()
    },
    // 收藏课程
    collectCourse(id) {
      this.$confirm('是否要收藏该课程?', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.collectCourse({ courseId: id }).then((res) => {
          if (res.code == 200) {
            this.$message.success(res.msg)
          } else {
            this.$message.error(res.msg);
          }
        });
      })
    },
    // 查看详情
    getDetils() {
      this.$router.push('/courseDetils')
    },
    handleNodeClick(data, node) {
      this.paginationData.pageNo = 1
      this.subjectId = data.id
      this.getPlanList()
    },
    handleCurrentChange(val) {
      this.paginationData.pageNo = val
      if (this.activeIndex == 0) {
        this.getTrainList()
      } else {
        this.getTonferenceList()
      }
    },
    // 每页的条数
    handleSizeChange(val) {
      if (this.activeIndex == 0) {
        this.paginationData.pageSize = val
        this.paginationData.pageNo = 1
        this.getTrainList()
      } else {
        this.paginationData.pageSize = val
        this.paginationData.pageNo = 1
        this.getTonferenceList()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.el-tabs--left) {
  width: 200px;
  margin: 20px auto 0 auto;
}

.content_top {
  height: 100%;
  border-radius: 10px;
  padding: 16px;
  background: #fff;
  display: flex;
  flex-direction: column;

  .tabsMenu {
    // margin-bottom: 15px;

    :deep(.el-tabs__item) {
      width: 120px;
    }
  }

  .top_content {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }
}

.content_box {
  height: 100%;
}

.open_conter {
  height: 100%;

  .seachTop {
    margin-bottom: 16px;
    font-size: 14px;
  }

  .table {
    height: calc(100% - 150px);
    margin-top: 16px;
    overflow: auto;
    font-size: 14px;
  }
}

:deep(.el-tabs__nav) {
  width: 100%;

  .el-tabs__item {
    padding: 0 10px;
    width: 50%;
    text-align: center;
  }
}

.item {
  // width: 372px;
  margin-bottom: 30px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e6eb;
  display: flex;
  flex-direction: column;

  .text {
    padding: 16px 24px 10px 24px;
    font-family: PingFang SC-Regular, PingFang SC;

    .item_bottom {
      height: 40px;
      line-height: 40px;
      padding-top: 10px;
      border-top: 1px solid #e4e4e4;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #999999;

      .collect {
        width: 58px;
        height: 24px;
        line-height: 24px;
        text-align: center;
        background-color: #f2f4f9;
      }

      .studyIng {
        width: 68px;
        height: 24px;
        line-height: 24px;
        text-align: center;
        background-color: #d7e0f8;
        color: #3562db;
      }

      .studyOK {
        width: 79px;
        height: 24px;
        line-height: 24px;
        margin-left: 20px;
        background: #e8ffea;
        border-radius: 4px;
        color: #009a29;
        text-align: center;

        img {
          vertical-align: middle;
          margin-right: 8px;
        }
      }
    }
  }
}

.course_cove {
  width: 100%;
  height: 172px;
  border-radius: 8px 8px 0px 0px;
  position: relative;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  .status-ing {
    width: 72px;
    height: 24px;
    background: #f53f3f;
    opacity: 1;
    font-size: 14px;
    color: #fff;
    line-height: 24px;
    text-align: center;
    border-radius: 4px;
    position: absolute;
    left: 8px;
    top: 8px;
  }
}

.headline {
  height: 22px;

  display: flex;

  p {
    font-size: 16px;
    line-height: 22px;
    color: #333333;
    font-weight: 600;
    overflow: hidden;

    white-space: nowrap;
  }

  .draft {
    width: 44px;
    height: 100%;
    background: #fff7e8;
    border-radius: 4px;
    color: #d25f00;
    font-size: 14px;
    line-height: 22px;
    margin-right: 8px;
  }
}

.introduce {
  margin: 6px 0;
  font-size: 12px;
  color: #999999;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.describe {
  margin-bottom: 20px;
  font-size: 14px;
  color: #666666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.course_time {
  .course_bottom {
    i {
      color: #3562db;
      font-size: 14px;
    }

    .el-icon-edit {
      margin-right: 10px;
    }
  }
}

// minibutton
.miniBtn {
  color: #fff;
  border-color: #3562db;
  background: #3562db;
  font-weight: 400;
  font-size: 14px;
  padding: 6px 16px;
  font-family: PingFangSC-Regular;
}

.miniBtn:hover {
  color: #fff;
  font-family: PingFangSC-Regular;
  border-color: rgba(53, 98, 219, 0.8);
  background-color: rgba(53, 98, 219, 0.8);
  font-weight: 500;
}

/deep/ .el-cascader .el-input .el-input__inner {
  min-height: 32px !important;
}

/deep/ .el-input__inner {
  height: 32px !important;
}

.search-header {
  padding: 0px 16px 10px 16px;

  .search-from {
    padding-bottom: 12px;

    &>div {
      margin-top: 12px;
      margin-right: 10px;
    }
  }
}

.table-content {
  padding: 0px 16px;
  background-color: #fff;
  height: calc(100% - 80px);

  .statusBtn {
    font-size: 14px;
    display: flex;
    justify-content: center;

    .auditIng {
      width: 58px;
      height: 24px;
      background-color: #fff7e8;
      border-radius: 4px;
      color: #d25f00;
    }

    .auditNo {
      width: 78px;
      height: 24px;
      background-color: #ffece8;
      border-radius: 4px;
      color: #cb2634;

      img {
        vertical-align: middle;
      }
    }

    .relwase {
      width: 58px;
      height: 24px;
      background-color: #e8ffea;
      border-radius: 4px;
      color: #009a29;
    }

    .inProgress {
      width: 86px;
      height: 24px;
      background-color: #E6EFFC;
      border-radius: 4px;
      color: #2749BF;
    }

    .relwaseNo {
      width: 58px;
      height: 24px;
      background-color: #F2F4F9;
      border-radius: 4px;
      color: #86909C;
    }
  }

  .operateBths {
    color: #3562DB;

    .el-link {
      margin-right: 8px;
    }
  }
}

.contentTable-footer {
  padding: 10px 0 0 0;
}
</style>
