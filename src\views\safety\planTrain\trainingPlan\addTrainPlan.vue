<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="topFilter">
        <div class="backBar">
          <span style="cursor: pointer" @click="$router.go(-1)">
            <i class="el-icon-arrow-left"></i>
            创建培训
          </span>
        </div>
      </div>
      <div class="courseContent">
        <div class="courseTab">
          <div class="left activeLeft">1.基础内容</div>
          <div class="centerLeft"></div>
          <div v-if="active == '1'" class="centerRight"></div>
          <div :class="['right', active == 1 ? 'activeRight' : '']">
            2.考试配置
          </div>
        </div>
        <div class="courseInfo" v-if="active == '0'">
          <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
            基础信息
          </div>
          <el-form label-width="160px" :rules="rules" :model="formInfo" ref="formInfo" class="formInfo">
            <el-form-item label="培训计划名称:" prop="name">
              <el-input v-model="formInfo.name" placeholder="请输入培训计划名称" show-word-limit maxlength="30"
                style="width: 360px"></el-input>
            </el-form-item>
            <el-form-item label="所属科目:" prop="subjectId">
              <el-cascader v-model="formInfo.subjectId" clearable class="sino_sdcp_input mr15" style="width: 360px"
                :options="subjectList" :props="props" placeholder="请选择所属科目"></el-cascader>
            </el-form-item>
            <br />
            <el-form-item label="所属部门:" prop="deptIds">
              <el-cascader v-model="formInfo.deptIds" placeholder="请选择课程公开范围" :options="deptList" :props="deptTree"
                :show-all-levels="false" clearable filterable collapse-tags style="width: 360px">
              </el-cascader>
            </el-form-item>
            <el-form-item label="培训老师:" prop="teacherName">
              <template>
                <div @click="addPersonShow('teacher')">
                  <el-input v-model="formInfo.teacherName" placeholder="请选择培训老师" style="width:360px"> </el-input>
                </div>
              </template>
            </el-form-item>

            <el-form-item label="培训频率:" prop="frequency">
              <el-select v-model="formInfo.frequency" placeholder="请选择会议频率方式" style="width: 360px"
                @change="changeCycleType">
                <el-option v-for="item in frequencyList" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="培训负责人:" prop="">
              <!-- <el-input v-model.trim="formInfo.principal" show-word-limit style="width: 360px;"></el-input> -->
              <el-select v-model.trim="formInfo.principal" placeholder="请选择人员" style="width: 360px;">
                <el-option v-for="(item, index) in claimList" :key="index" :label="item.name" :value="item.name">
                </el-option>
              </el-select>
            </el-form-item>

            <br />
            <el-form-item label="培训开始日期:" v-if="formInfo.frequency == 0">
              <el-date-picker v-model="formInfo.startTime" type="date" value-format="yyyy-MM-dd" placeholder="选择日期" :picker-options="pickerOptions"
                style="width: 360px;">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="培训开始日期:" v-if="formInfo.frequency == 1">
              <el-date-picker v-model="formInfo.startTime" type="date" value-format="yyyy-MM-dd" placeholder="选择日期" :picker-options="pickerOptions"
                style="width: 360px;">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="培训开始日期:" prop="startDay" v-if="formInfo.frequency == 2">
              <el-select v-model="formInfo.startDay" placeholder="请选择开始周" style="width: 360px;">
                <el-option v-for="item in startDateArr" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="培训开始日期:" prop="startDay" v-if="formInfo.frequency == 3">
              <el-select v-model="formInfo.startDay" placeholder="请选择月" style="width: 360px;">
                <el-option v-for="item in startDateArr" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="培训开始日期:" prop="startDay" v-if="formInfo.frequency == 4">
              <el-select v-model="formInfo.startMonth" placeholder="请选择季度" style="width: 360px;">
                <el-option v-for="item in startMonthArr" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="培训开始日期:" prop="startDay" v-if="formInfo.frequency == 5">
              <el-date-picker v-model="formInfo.startDay" type="date" value-format="yyyy-MM-dd" placeholder="选择日期" :picker-options="pickerOptions"
                style="width: 360px;">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="培训时间:" prop="timeLine">
              <el-time-picker is-range v-model="formInfo.timeLine" range-separator="至" start-placeholder="开始时间"
                end-placeholder="结束时间" placeholder="选择时间范围" style="width: 360px" value-format="HH:mm:ss"
                @change="dataPinkTime">
              </el-time-picker>
            </el-form-item>
            <el-form-item label="培训方式:">
              <el-select v-model="formInfo.type" placeholder="请选择培训方式" style="width: 360px;">
                <el-option v-for="item in trainList" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="培训地址:" prop="">
              <el-input v-model="formInfo.address" placeholder="请输入培训地址" show-word-limit maxlength="200"
                style="width: 360px"></el-input>
            </el-form-item>
            <br />
            <el-form-item label="通知培训消息:" prop="notice">
              <!-- <el-input type="number" placeholder="每间隔" min="1" style="width: 360px" v-model="formInfo.notice">
                <template slot="append">分钟/次</template>
              </el-input> -->
              <el-input type="number" placeholder="请输入" v-model="formInfo.notice" class="input-with-select" style="width: 360px;">
                <el-select v-model="select" slot="prepend" style="width: 90px; position: relative;left: 0;">
                  <el-option label="分钟" value="1"></el-option>
                  <el-option label="小时" value="2"></el-option>
                  <el-option label="天" value="3"></el-option>
                </el-select>
              </el-input>
            </el-form-item>
            <el-form-item label="培训组织或学员:">
              <el-select v-model="formInfo.stuOrOrg" placeholder="请选择分配方式" style="width: 360px">
                <el-option v-for="item in stuOrOrgList" :key="item.id" :label="item.label" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <br />
            <el-form-item label="计划开始日期:" v-if="formInfo.frequency != 1 && formInfo.frequency != 0">
              <el-date-picker v-model="formInfo.courseTimeLine" type="daterange" range-separator="至"
                start-placeholder="开始日期" value-format="yyyy-MM-dd" end-placeholder="结束日期" style="width: 360px" :picker-options="pickerOptions"
                @change="dataPinkS">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="考试学员:" class="" v-if="formInfo.stuOrOrg == '0'">
              <span style="color: red; margin-left: -80px">*</span>
              <div class="set_select_width" @click="showUserDialog('xueyuan')">
                <template v-if="userNewList.length">
                  <span v-for="(item, index) in userNewList" :key="index">
                    {{ item }}
                    <i class="el-icon-error" @click.stop="deleteTag(index, 'user')"></i>
                  </span>
                </template>
                <p v-else>请选择人员</p>
              </div>
            </el-form-item>
            <el-form-item label="组织" prop="orgId" v-if="formInfo.stuOrOrg == '1'">
              <el-select v-model="formInfo.orgId" placeholder="请选择组织" style="width: 360px" multiple>
                <el-option v-for="item in deptAllList" :key="item.id" :label="item.teamName" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <br />
            <el-form-item label="培训后组卷考试设置" prop="">
              <el-radio-group v-model="formInfo.exam">
                <el-radio v-for="item in noPassList" :key="item.id" :label="item.id">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <div>
              <div class="courseNav">
                <div class="courseTitle"> <span>*</span> 培训课件</div>
                <div class="courseBtn">
                  <div>
                    <el-upload ref="uploadFile" multiple class="mterial_file" action="string" :file-list="fileEcho"
                      :http-request="httpRequest"
                      accept=".jpg,.png,.pdf,.JPG,.PBG,.GIF,.BMP,.PDF,.mp4,.avi,.wimv,.mpeg,.pdf,.doc,.docx,pptx"
                      :limit="30" :on-exceed="handleExceed" :before-upload="beforeAvatarUpload"
                      :on-remove="handleRemove" :on-change="fileChange">
                      <el-button type="primary"><i class="el-icon-plus"></i>上传培训资料</el-button>
                    </el-upload>
                  </div>
                  <div><el-button type="primary" @click="openCourseMb"><i class="el-icon-plus"></i>添加培训模板</el-button>
                  </div>
                  <div><el-button type="primary" @click="openCourseKj"><i class="el-icon-plus"></i>添加课程资源</el-button>
                  </div>
                </div>
              </div>
              <div class="courseConnter">
                <!-- <div class="courseImg"></div> -->
                <div class="courseMb" v-if="templateList != ''">
                  <div style="padding: 0px 10px;">
                    <div style="margin-top: 8px;color: #333333;font-size: 14px;">关联培训模板</div>
                    <div class="courseMbnav" v-for="(item, index) in templateList" :key="index">
                      <div class="courseMbtitie">
                        <div class="courseMbBox">
                          <div class="courseMbBoxLable">培训名称:</div>
                          <div class="courseMbBoxItem">{{ item.name }}</div>
                        </div>
                        <div class="courseMbBox">
                          <div class="courseMbBoxLable">培训内容：</div>
                          <div class="courseMbBoxItem">{{ item.disc }}</div>
                        </div>
                        <div class="courseMbBox">
                          <div class="courseMbBoxLable">培训老师：</div>
                          <div class="courseMbBoxItem">{{ item.disc }}</div>
                        </div>
                        <div class="courseMbBox">
                          <div class="courseMbBoxLable">培训地址：</div>
                          <div class="courseMbBoxItem">{{ item.disc }}</div>
                        </div>
                        <!-- <div class="courseMbBox">
                          <div>查看</div>
                          <div>删除</div>
                        </div> -->
                      </div>
                    </div>
                  </div>
                </div>
                <div class="courseKj" v-if="coursePlanList != ''">
                  <div class="courseKjBox">
                    <div style="margin-top: 8px;color: #333333;font-size: 14px;">关联课程资源</div>
                    <div class="courseMbnav" v-for="(item, index) in coursePlanList" :key="index">
                      <div class="courseMbtitie">
                        <div class="courseMbBox">
                          <div>名称:</div>
                          <div>{{ item.courseName }}</div>
                        </div>
                        <div class="courseMbBox">
                          <div>课程类型：</div>
                          <div>{{ item.subjectName }}</div>
                        </div>
                        <div class="courseMbBox">
                          <div>课时数：</div>
                          <div>{{ item.periodCount }}</div>
                        </div>
                        <div class="courseMbBox">
                          <div>创建人：</div>
                          <div>{{ item.createName }}</div>
                        </div>
                        <!-- <div class="courseMbBox">
                          <div>查看</div>
                          <div>删除</div>
                        </div> -->
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-form>
        </div>
        <div class="courseList" v-if="active == '1'">
          <div class="examInfo">
            <examination ref="examination" :setingOpty="setingOpty" :formInfoDeatil="formInfoDeatil"
              :userList="userList" :id="id">
            </examination>
          </div>
        </div>
        <!-- 选择模板 -->
        <el-drawer title="选择模板" :visible.sync="drawerMb" size="65%" :show-close="true">
          <div style="padding: 0px 10px 10px 10px;">
            <el-input v-model="filter.name" placeholder="请输入培训模板名称" style="width: 200px;margin-right: 10px;"></el-input>
            <el-date-picker v-model="filter.timeLine" type="daterange" value-format="yyyy-MM-dd" range-separator="至"
              start-placeholder="开始日期" end-placeholder="结束日期" @change="dataPink">
            </el-date-picker>
            <el-button type="primary" plain @click="reset" style="margin-left: 10px;">重置</el-button>
            <el-button type="primary" @click="search">查询</el-button>
          </div>
          <div style="padding: 16px;  height: calc(100% - 80px);">
            <el-table :data="tableData" tyle="width: 100%;" height="100%" border stripe title="双击查看详情"
              @selection-change="handleSelectionChangeTmpid">
              <el-table-column type="selection" width="55" fixed="left" align="center"></el-table-column>
              <el-table-column label="序号" width="55" align="center" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span>{{
    (paginationData.pageNo - 1) * paginationData.pageSize +
    scope.$index +
    1
  }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="name" label="培训模板名称" align="center" show-overflow-tooltip>
              </el-table-column>
              <el-table-column prop="disc" label="培训内容" align="center" show-overflow-tooltip></el-table-column>
              <el-table-column prop="coursewareNum" label="培训课件数" align="center"
                show-overflow-tooltip></el-table-column>
              <el-table-column prop="createTime" label="创建时间" align="center" show-overflow-tooltip></el-table-column>
            </el-table>
          </div>
          <div class="derwer_footer">
            <div class="derwer_footerSum"> 已选择 <span class="sumNamber">{{ tableSum }}</span> 个课程</div>
            <div>
              <el-button type="primary" plain @click="cancel">取消</el-button>
              <el-button type="primary" @click="submit">添加</el-button>
            </div>
          </div>
        </el-drawer>
        <!-- 选择课程 -->
        <el-drawer title="选择课程" :visible.sync="drawerkj" size="65%" :show-close="true">
          <div style="padding: 0px 10px 10px 10px;">
            <el-input v-model="seachForm.courseName" placeholder="请输入课程名称"
              style="width: 200px; margin: 0px 10px;"></el-input>
            <el-input v-model="seachForm.courseTeacher" placeholder="请输入培训老师"
              style="width: 200px; margin: 0px 10px;"></el-input>
            <el-date-picker v-model="timeLine" type="daterange" value-format="yyyy-MM-dd" range-separator="至"
              start-placeholder="开始日期" end-placeholder="结束日期" style="width: 260px; margin: 0 16px">
            </el-date-picker>
            <el-button type="primary" plain @click="resetFormCourse">重置</el-button>
            <el-button type="primary" @click="searchCourse">查询</el-button>
          </div>
          <div class="courseBoxw">
            <el-table ref="multipleTable" :data="courseData" tooltip-effect="dark" style="width: 100%" height="100%"
              border @selection-change="handleSelectionChangeCourse">
              <el-table-column type="selection" width="55" fixed="left" align="center"></el-table-column>
              <el-table-column label="课程名称" width="150" align="center" show-overflow-tooltip>
                <template slot-scope="scope">{{ scope.row.courseName }}</template>
              </el-table-column>
              <el-table-column prop="subjectName" label="所属单位" width="200" show-overflow-tooltip
                align="center"></el-table-column>
              <el-table-column prop="createName" label="课件老师" show-overflow-tooltip align="center"
                width="150"></el-table-column>
              <el-table-column prop="comments" label="课程内容" width="150" show-overflow-tooltip
                align="center"></el-table-column>
              <el-table-column prop="periodCount" label="课时数" show-overflow-tooltip align="center"
                width="100"></el-table-column>
              <el-table-column prop="updateTime" label="创建时间" width="200" show-overflow-tooltip
                align="center"></el-table-column>
            </el-table>
            <div class="contentTable-footer">
              <el-pagination style="margin-top: 3px" :current-page="paginationData.pageNo"
                layout="total, sizes, prev, pager, next, jumper" :total="paginationData.total"
                :page-size="paginationData.pageSize" :page-sizes="[15, 30, 50, 100]"
                @size-change="handleSizeChangeCouesr" @current-change="handleCurrentChangeCouesr"></el-pagination>
            </div>
          </div>
          <div class="derwer_footer">
            <div class="derwer_footerSum"> 已选择 <span class="sumNamber">{{ tableSumCourse }}</span> 个课程</div>
            <div>
              <el-button type="primary" plain @click="cancelCourse">取消</el-button>
              <el-button type="primary" @click="submitCourse">添加</el-button>
            </div>
          </div>
        </el-drawer>
      </div>
      <!-- 学员弹窗 -->
      <tasksDialog ref="userDialogRef" :opty="opty" :peopleDialog="peopleDialog" @closeDialog="closeDialog"
        @sureDialogUser="sureDialogUser"></tasksDialog>
    </div>
    <div slot="footer">
      <div v-if="this.formInfo.exam == 0">
        <el-button type="primary" plain @click="onCancel">取消</el-button>
        <el-button type="primary" @click="addExam('0')">保存草稿</el-button>
        <el-button type="primary" @click="addExam('2')">发布培训</el-button>
      </div>
      <div v-if="this.formInfo.exam != 0">
        <el-button v-if="active == '0'" type="primary" @click="nextStep">下一步</el-button>
        <el-button type="primary" plain @click="onCancel">取消</el-button>
        <el-button v-if="active == '1'" type="primary" plain @click="upStep">上一步</el-button>
        <el-button v-if="active == '1'" type="primary" @click="addExam('0')">保存草稿</el-button>
        <el-button v-if="active == '1'" type="primary" @click="addExam('2')">发布培训</el-button>
      </div>

    </div>
  </PageContainer>
</template>
<script>
import axios from "axios";
import moment from 'moment'
// import voluntarily from "./components/voluntarily.vue";
// import manualPperation from "./components/manualPperation.vue";
import tasksDialog from '../trainingTasks/components/tasksDialog.vue';
import examination from '../trainingPlan/components/examination.vue'
export default {
  components: { examination, tasksDialog },
  data() {
    return {
      moment,
      select: '1',
      input3: '',
      userNewList: [],
      trainTeacher: [], // 选择培训老师
      trainTeacherName: '',
      trainUser: [], // 到会人员
      trainUserName: '',
      deptAllList: [],
      opty: '', // 学员弹窗状态
      setingOpty: '', // 考试设置状态
      tableSum: 0, // 已选择培训模板数
      tableSumCourse: 0, // 已选择课程数
      filter: { // 培训模板
        name: '',
        timeLine: [],
        startTime: '',
        endTime: '',
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7;
        }
      },
      formInline: { // 培训模板
        templateName: "",
        startTime: "",
        endTime: "",
      },
      seachForm: { // 课程筛选
        courseName: "",
        courseTeacher: "",
        startTime: '',
        endTime: '',
        studySchedule: ''
      },
      fileEcho: [], // 上传课件
      timeLine: [],
      templateList: [], // 选中模板列表
      coursePlanList: [], // 选中课程列表
      courseData: [],
      drawerMb: false, // 选择模板
      drawerkj: false, // 选择课程
      props: {
        children: "childList",
        label: "name",
        value: "id",
        checkStrictly: true,
        emitPath: false,
      },
      deptTree: {
        children: "children",
        label: "teamName",
        value: "id",
        multiple: true,
        emitPath: false,
      },
      routeInfo: "",
      loginData: "",
      id: "",
      typeStr: "",
      active: "0",
      formInfo: {
        name: "", // 计划名称
        subjectId: null, // 科目id
        teacherIds: '', // 培训老师
        teacherName: '', // 培训老师名称
        frequency: '0', // 通过频率
        stuOrOrg: '',
        orgId: '',
        studentIds: '', // 学生ids
        courseTimeLine: [], // 会议开始时间
        deptIds: "", // 部门id
        timeLine: '', // 培训时间
        startTime: '', //  计划开始日期
        type: '', // 培训方式
        address: '', // 培训地址
        endTime: '',  //计划结束日期
        notice: '', // 通知培训消息
        principal: '',  // 培训负责人
        exam: '0', // 组卷考试设置
        tmpId: '', // 模板id
        material: '', // 培训资料id
        examPlanIds: '', // 关联考试计划id
        courseId: '', // 课程资源id
        passScore: '',//通过分数
        trainStartTime: '', // 开始时间
        trainEndTime: '', // 结束时间
        startDay: '', // 开始日期
        startMonth: '', // 开始日期月
      },
      // 开始月份
      startMonthArr: [
        {
          id: 1,
          name: "第一季度",
        },
        {
          id: 2,
          name: "第二季度",
        },
        {
          id: 3,
          name: "第三季度",
        },
        {
          id: 4,
          name: "第四季度",
        },
      ],
      subjectNames: '',
      subjectList: [],
      subjectAllList: [],
      deptList: [],
      deptAllList: [],
      certificateList: [],
      stuOrOrgList: [
        {
          id: '0',
          label: '学生'
        },
        {
          id: '1',
          label: '组织'
        }
      ],
      noPassList: [
        {
          id: "0",
          label: "培训后不考试",
        },
        {
          id: "1",
          label: "自动组卷",
        },
        {
          id: "2",
          label: "手动组卷",
        },
        {
          id: "3",
          label: "关联考试计划",
        },
      ],
      rules: {
        name: [
          { required: true, message: "请输入试卷名称", trigger: "blur" },
          {
            min: 1,
            max: 30,
            message: "长度在 1 到 30 个字符",
            trigger: "blur",
          },
        ],
        deptIds: [
          { required: true, message: "请选择所属部门", trigger: "change" },
        ],
        teacherName: [
          { required: true, message: "请选择培训老师", trigger: "change" },
        ],
        frequency: [
          { required: true, message: "请选择培训频率", trigger: "change" },
        ],
        courseTimeLine: [
          { required: true, message: "请选择开始日期", trigger: "change" },
        ],
        startDay: [
          { required: true, message: "请选择开日期", trigger: "blur" },
        ],
        timeLine: [
          { required: true, message: "请选择开始时间", trigger: "blur" },
        ],
        type: [
          { required: true, message: "请选择培训方式", trigger: "blur" },
        ],
        stuOrOrg: [
          { required: true, message: "请输入培训组织或学员", trigger: "blur" },
        ],
        notice: [
          { required: true, message: "请输入培训消息时长", trigger: "blur" },
        ],
        orgId: [
          { required: true, message: "请选择组织", trigger: "blur" },
        ],
      },
      claimList: [], // 培训责任人列表
      peopleDialog: false,
      userList: [],
      frequencyList: [  // 计划频率
        {
          value: '0',
          label: '全年'
        }, {
          value: '1',
          label: '单次'
        }, {
          value: '2',
          label: '每周'
        }, {
          value: '3',
          label: '每月'
        }, {
          value: '4',
          label: '季度'
        },
        {
          value: '5',
          label: '每半年'
        }
      ],
      trainList: [{
        value: '0',
        label: '线上会议'
      }, {
        value: '1',
        label: '线下会议'
      }],
      autoInfo: {
        questionsList: [
          {
            questionTypes: [
              {
                type: "1",
                num: '',
                score: '',
              },
              {
                type: "2",
                num: '',
                score: '',
              },
              {
                type: "3",
                num: '',
                score: '',
              },
            ],
            courseId: '',
            questionsNum: []
          },
        ],
      },
      scoreInfo: {
        nums: 0,
        sumScore: 0
      },
      // 开始日期数组
      startDateArr: () => {
        if (this.formInfo.frequency == 2) {
          const dateName = [
            "周一",
            "周二",
            "周三",
            "周四",
            "周五",
            "周六",
            "周日",
          ];
          const dateArr = [];
          for (let i = 0; i < 7; i++) {
            const item = {
              id: i + 1,
              name: "每" + dateName[i],
            };
            dateArr.push(item);
          }
          return dateArr;
        } else if (this.formInfo.frequency == "3") {
          const dateArr = [];
          for (let i = 0; i < 30; i++) {
            const item = {
              id: i + 1,
              name: "每月" + (i + 1) + "日",
            };
            dateArr.push(item);
          }
          return dateArr;
        } else if (val == "3") {
          const dateArr = [];
          for (let i = 0; i < 30; i++) {
            const item = {
              id: i + 1,
              name: "每月" + (i + 1) + "日",
            };
            dateArr.push(item);
          }
          return dateArr;
        }
      },
      formInfoDeatil: {},
      noAutoList: [],
      userAllList: [],
      tableData: [], // 模板数据
      paginationData: {
        pageNo: 1,
        pageSize: 15,
        total: 0,
      },
      multipleSelectionTmpid: [],
      ExaminDeatil: '', // 考试计划列表选中的数据
      trainId: '',
    };

  },
  created() {
    // if (this.$route.query) {
    //   sessionStorage.setItem("routeInfo", JSON.stringify(this.$route.query));
    //   this.routeInfo = JSON.parse(sessionStorage.getItem("routeInfo"));
    // }
    this.getTableList()
    this.getDataList() // 模板
    this.getCourseyList() // 课程
    this.routeInfo = JSON.parse(sessionStorage.getItem("routeInfo"));
    this.formInfo.principal = this.routeInfo.createName
    this.getdeptList();
    this.getTblleList();
    this.id = this.$route.query.id || "";
    this.typeStr = this.$route.query.type || "";
    if (this.id) {
      this.getTableList()
      this.getDetails()
    }
    this.getResponsibilityList()
    console.log(this.setingOpty, 'this.setingOpty123');
  },
  watch: {
    timeLine(val) {
      if (val.length > 0) {
        this.seachForm.startTime = val[0];
        this.seachForm.endTime = val[1];
      } else {
        this.seachForm.startTime = "";
        this.seachForm.endTime = "";
      }
    },
  },
  methods: {
    changeCycleType(val) {
      this.formInfo.frequency = val
      this.formInfo.startDay = "";
      if (val == "2") {
        const dateName = [
          "周一",
          "周二",
          "周三",
          "周四",
          "周五",
          "周六",
          "周日",
        ];
        this.startDateArr = [];
        for (let i = 0; i < 7; i++) {
          const item = {
            id: i + 1,
            name: "每" + dateName[i],
          };
          this.startDateArr.push(item);
        }
      } else if (val == "2") {
        this.startDateArr = [];
        for (let i = 0; i < 30; i++) {
          const item = {
            id: i + 1,
            name: "每月" + (i + 1) + "日",
          };
          this.startDateArr.push(item);
        }
      } else if (val == "3") {
        this.startDateArr = [];
        for (let i = 0; i < 30; i++) {
          const item = {
            id: i + 1,
            name: "每月" + (i + 1) + "日",
          };
          this.startDateArr.push(item);
        }
      }
    },
    openCourseMb() {
      this.drawerMb = true
    },
    openCourseKj() {
      this.drawerkj = true
    },
    // 获取培训责任人列表
    getResponsibilityList() {
      let params = {
        pageSize: 99999,
        currentPage: 1,
        controlTeamId: this.routeInfo.controlGroupIds
      }
      this.$api.getControlTeamUserListLaboratory(params).then((res) => {
        if (res.code == 200) {
          this.claimList = res.data.list
        }
      })
      this.tableLoading = false
    },
    // 选择筛选时间
    dataPink(data) {
      this.filter.startTime = data[0]
      this.filter.endTime = data[1]
    },

    handleCurrentChange(val) {
      this.paginationData.pageNo = val;
      this.getDataList();
    },
    // 计划开始日期
    dataPinkS(data) {
      this.formInfo.startTime = data[0]
      this.formInfo.endTime = data[1]
    },
    // 选择时间
    dataPinkTime(data) {
      this.formInfo.trainStartTime = data[0]
      this.formInfo.trainEndTime = data[1]
    },
    addPersonShow(opty) {
      console.log(1111111111111);
      
      this.opty = opty
      this.peopleDialog = true;
      this.$nextTick(() => {
        // this.$refs.userDialogRef.userSelectData = this.userList;
        this.$refs.userDialogRef.getTableList(true);
      });
    },
    // ------------------------------------ 培训模板 --------------------------------------------------------
    // 查询模板列表
    getDataList() {
      let params = {
        current: this.paginationData.pageNo,
        size: this.paginationData.pageSize,
        ...this.filter
      }
      this.$api.tarainTemplateList(params).then(res => {
        if (res.code == 200) {
          this.tableData = res.data.list
          this.paginationData.total = res.data.total
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 选择频率
    seleceFrequency(e) {
      this.formInfo.frequency = e
    },
    // 取消模板
    cancel() { },
    // 选中模板表格数据 
    handleSelectionChangeTmpid(val) {
      this.multipleSelectionTmpid = val
      this.tableSum = val.length
    },
    // 提交模板
    submit() {
      let objMb = [];
      this.templateList = this.multipleSelectionTmpid
      this.drawerMb = false
      if (this.templateList) {
        this.templateList.forEach(el => {
          objMb.push(el.id)
        })
        this.formInfo.tmpId = objMb.join(',')
      }
    },
    // ------------------------------------ 课程资源 --------------------------------------------------------
    // 获取课程列表
    getCourseyList() {
      let params = {
        userId: '24cbff7638ce45db8ed491f2d0347f97',
        pageNo: this.paginationData.pageNo,
        pageSize: this.paginationData.pageSize,
        ...this.seachForm,

      }
      this.$api.getOnlineCourseList(params).then(res => {
        this.courseData = res.data.list
        this.paginationData.total = res.data.total
      })
    },

    // 操作表格数据
    handleSelectionChangeCourse(val) {

      this.tableSumCourse = val.length
      this.multipleCourse = val;

    },
    handleSizeChangeCouesr(val) {
      this.paginationData.pageSize = val
      this.paginationData.pageNo = 1
      this.getCourseyList()
    },
    handleCurrentChangeCouesr(val) {
      this.paginationData.pageNo = val
      this.getCourseyList()
    },
    // 选中学员、老师
    sureDialogUser() {
      this.peopleDialog = false;
      if (this.opty == 'xueyuan') {
        this.userList = this.$refs.userDialogRef.userSelectData;
        const userNewList = this.userList.map(item => item.name)
        this.userNewList = userNewList
      }
      if (this.opty == 'teacher') {
        this.userList = this.$refs.userDialogRef.userSelectData;
        this.formInfo.teacherIds = this.userList.map(item => item.id)
        this.formInfo.teacherIds = this.formInfo.teacherIds.join(',')
        this.trainTeacher = this.userList.map(item => item.name)
        this.formInfo.teacherName = this.trainTeacher.join(',')
      }
      if (this.opty == 'user') {
        this.userList = this.$refs.userDialogRef.userSelectData;
        this.formInfo.studentIds = this.userList.map(item => item.id)
        this.formInfo.studentIds = this.formInfo.studentIds.join(',')
        this.trainUser = this.userList.map(item => item.name)
        this.trainUserName = this.trainUser.join(',')
      }
    },
    // 取消
    cancelCourse() { },
    // 提交
    submitCourse() {
      let idsArray = [];
      this.coursePlanList = this.multipleCourse;
      this.drawerkj = false
      if (this.coursePlanList) {
        this.coursePlanList.forEach(item => {
          idsArray.push(item.id);
        })
        this.formInfo.courseId = idsArray.join(',')
      }
    },
    // 查询
    searchCourse() {
      this.paginationData.pageNo = 1
      this.getCourseyList() // 获取课程
    },
    // 重置
    resetFormCourse() {
      this.paginationData.pageNo = 1
      this.paginationData.pageSize = 15
      this.timeLine = []
      this.seachForm = {
        courseName: "",
        courseTeacher: "",
        startTime: '',
        endTime: '',
        studySchedule: ''
      }
      this.getCourseyList()
    },
    // ---------------------------------------- 创建计划 --------------------------------------------------------
    // 获取人员列表
    getTableList() {
      let data = {
        currentPage: 1,
        pageSize: 9999,
        controlTeamId: ''
      }
      this.$api.getControlTeamUserListLaboratory(data).then((res) => {
        if (res.code == 200) {
          this.userAllList = res.data.list
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 培训编辑详情
    getDetails() {
      this.$api.trainPlanDetal({ id: this.id }).then(res => {
        if (res.code == '200') {
          this.getTblleList()
          this.changeCycleType()
          this.formInfo = { ...res.data }
          this.formInfo.trainTeacher = res.data.teacherIds.split(',');
          this.formInfo.subjectId = parseInt(res.data.subjectId);
          this.formInfo.courseTimeLine = [res.data.startTime, res.data.endTime];
          this.formInfo.timeLine = [res.data.trainStartTime, res.data.trainEndTime];
          this.formInfo.orgId = res.data.orgId.split(',');
          this.formInfo.deptIds = res.data.deptIds.split(',')
          // 考试人员回显
          let studentIdList = res.data.studentIds.split(',')
          this.userList = []
          studentIdList.forEach(i => {
            this.userAllList.forEach(k => {
              if (i == k.id) {
                this.userList.push(k)
              }
            })
          })
          this.templateList = res.data.tmps
          const userNewList = this.userList.map(item => item.name)
          this.userNewList = userNewList
          // 培训老师回显
          let teacherIdsList = res.data.teacherIds.split(',')
          this.userList = []
          teacherIdsList.forEach(i => {
            this.userAllList.forEach(k => {
              if (i == k.id) {
                this.userList.push(k)
              }
            })
          })
          this.scoreInfo.sumScore = res.data.saveDto.score
          this.scoreInfo.nums = res.data.saveDto.count
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 获取所有科目数据
    flattenTree(tree) {
      let result = []; // 存放拆分后的节点列表
      function traverseNode(node) {
        if (Array.isArray(node)) {
          node.forEach((child) => {
            traverseNode(child);
          });
        } else {
          result.push(node);
          if (node.childList && Array.isArray(node.childList)) {
            node.childList.forEach((child) => {
              traverseNode(child);
            });
          }
        }
      }
      traverseNode(tree);
      return result;
    },
    getTblleList() {
      let data = {
        pageNo: 1,
        pageSize: 999,
      };
      this.$api.ipsmFicationlList(data).then((res) => {
        if (res.code == 200) {
          this.subjectList = res.data.records;
          this.subjectAllList = this.flattenTree(res.data.records)
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 获取组织
    getdeptList() {
      this.$api.getDeptListLaboratory({}).then((res) => {
        this.deptList = this.$tools.transData(
          res.data.list,
          "id",
          "parentId",
          "children"
        );
        this.deptAllList = res.data.list
      });
    },
    // 上一步
    upStep() {
      this.active = "0";
      if (this.setingOpty == '1') {
        const childComponent = this.$refs.examination;
        const innerComponent = childComponent.$refs.voluntarily;
        this.autoInfo = innerComponent.questionsInfo
        this.scoreInfo = innerComponent.questionsScore
      } else if (this.setingOpty == '2') {
        const childComponent = this.$refs.examination;
        const innerComponenty = childComponent.$refs.manualPperation;
        this.noAutoList = innerComponenty.questionsList
        this.scoreInfo = innerComponenty.questionsScore
      }
    },
    // 下一步
    nextStep() {
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          if (this.setingOpty == '0' && !this.templateList.length) {
            return this.$message.error("请添加培训模板");
          }
          this.active = "1";
          this.formInfoDeatil = this.formInfo;
          this.setingOpty = this.formInfo.exam;
          console.log(this.setingOpty, 'this.setingOpty');
          if (this.setingOpty == '1') {
            this.$nextTick(() => {
              const childComponent = this.$refs.examination;
              const innerComponent = childComponent.$refs.voluntarily;
              innerComponent.getCourseList(this.formInfo.subjectId)
            })
          }

        }
      });
    },
    // 提交课程
    addExam(type) {
      // const childComponent = this.$refs.examination;
      // const innerComponent = childComponent.$refs.voluntarily;
      // const innerComponenty = childComponent.$refs.manualPperation;
      if (this.setingOpty == '1') {
        const childComponent = this.$refs.examination;
        const innerComponent = childComponent.$refs.voluntarily;
        innerComponent.submit(type)
      } else if (this.setingOpty == '2') {
        const childComponent = this.$refs.examination;
        const innerComponenty = childComponent.$refs.manualPperation;
        innerComponenty.submit(type)
      }
      if (this.formInfo.exam == 0) {
        if (this.typeStr == 'train') {
          let params = {
            ...this.formInfo,
            deptIds: this.formInfo.deptIds.join(','),
            trainStatus: type,
            studentIds: this.formInfo.stuOrOrg == '0' ? this.userList.map(i => i.id).join(',') : '',
          }
          if (typeof this.formInfo.orgId === 'object') {
            params.orgId = this.formInfo.orgId.join(',')
          }
          if (this.formInfo.frequency == 1 || this.formInfo.frequency == 0) {
            params.startTime = this.formInfo.startTime
          }
          this.$api.trainPlanSave(params).then(res => {
            if (res.code == 200) {
              this.$message.success(res.msg)
              this.$router.go(-1)
            } else {
              this.$message.error(res.msg)
            }
          })
        } else if (this.typeStr == 'trainEdit') {
          let params = {
            ...this.formInfo,
            deptIds: this.formInfo.deptIds.join(','),
            trainStatus: type,
            id: this.formInfo.id,
          }
          if (typeof this.formInfo.orgId === 'object') {
            params.orgId = this.formInfo.orgId.join(',')
          }
          this.$api.trainPlanEdit(params).then(res => {
            if (res.code == 200) {
              this.$message.success(res.msg)
              this.$router.go(-1)
            } else {
              this.$message.error(res.msg)
            }
          })
        }
      } else if (this.setingOpty == '3') {
        if (this.typeStr == 'train') {
          let params = {
            ...this.formInfo,
            deptIds: this.formInfo.deptIds.join(','),
            trainStatus: type,
            studentIds: this.formInfo.stuOrOrg == '0' ? this.userList.map(i => i.id).join(',') : '',
          }
          if (this.setingOpty == '3') {
            params.examPlanIds = this.$refs.examination.multipleSelectionExmian[0].id
          }
          if (this.formInfo.orgId) {
            params.orgId = this.formInfo.orgId.join(',')
          }
          if (this.formInfo.frequency == 1 || this.formInfo.frequency == 0) {
            params.startTime = this.formInfo.startTime
          }
          this.$api.trainPlanSave(params).then(res => {
            if (res.code == 200) {
              this.$message.success(res.msg)
              this.$router.go(-1)
            } else {
              this.$message.error(res.msg)
            }
          })
        } else if (this.typeStr == 'trainEdit') {
          let params = {
            ...this.formInfo,
            id: this.formInfo.id,
            trainStatus: type,
          }
          if (this.setingOpty == '3') {
            params.examPlanIds = this.formInfo.examPlanIds
          }
          if (this.formInfo.orgId) {
            params.orgId = this.formInfo.orgId.join(',')
          }
          this.$api.trainPlanEdit(params).then(res => {
            if (res.code == 200) {
              this.$message.success(res.msg)
              this.$router.go(-1)
            } else {
              this.$message.error(res.msg)
            }
          })
        }
      }
    },
    // 学员选择弹窗-------------------------------
    showUserDialog(opty) {
      this.opty = opty
      this.peopleDialog = true;
      this.$nextTick(() => {
        // this.$refs.userDialogRef.userSelectData = this.userList;
        this.$refs.userDialogRef.getTableList(true);
      });
    },
    //关闭弹窗
    closeDialog() {
      this.peopleDialog = false
    },
    // sureDialogUser() {
    //   this.peopleDialog = false;
    //   this.userList = this.$refs.userDialogRef.userSelectData;
    //   const userNewList = this.userList.map(item => item.name)
    //   this.userNewList = userNewList
    // },
    deleteTag(index) {
      this.userNewList.splice(index, 1);
      this.userList.splice(index, 1);
    },
    onCancel() {
      this.$router.go(-1)
    },
    // 查询
    search() {
      this.paginationData.pageNo = 1;
      this.getDataList();
    },
    // 重置
    reset() {
      this.filter.name = "";
      this.filter.startTime = "";
      this.filter.endTime = "";
      this.filter.timeLine = [];
      this.paginationData.pageNo = 1;
      this.getDataList();
    },
    async fileChange(file, fileList) {
      const isLt40M = file.size / 1024 / 1024 < 40
      if (!isLt40M) {
        this.$message.error('上传图片大小不能超过 40MB!')
        fileList.splice(-1, 1); //移除选中图片
        return false
      }
      this.fileEcho = fileList
    },
    // 上传课件
    httpRequest() {
      this.formData = new FormData()
      this.fileEcho.forEach((item) => {
        this.formData.append('file', item.raw)
      })
      // this.formData.append('hospitalCode', this.routeInfo.hospitalCode)
      axios({
        method: 'post',
        url: __PATH.BASE_URL_LABORATORY + "minio/upload",
        data: this.formData,
        headers: {
          "Content-Type": "application/json",
          token: this.routeInfo.token,
        },
      }).then(res => {
        this.formInfo.material = res.data.data.fileRecordIds
        this.submit()
      }).catch(() => {
        this.$message.error(res.data.message)
      })
    },
    handleExceed() {
      this.$message.error('最多上传三张图片')
    },
    beforeAvatarUpload(file) {
      const isLt40M = file.size / 1024 / 1024 < 40
      if (!isLt40M) {
        this.$message.error('上传图片大小不能超过 40MB!')
        return false
      }
      if (file.name.indexOf(',') != -1) {
        this.$message.error('非法的文件名')
        return false
      }
    },
    handleRemove(file, fileList) {
      this.fileEcho = fileList
    },
  },
};
</script>
<style lang="scss" scoped>
.courseBtn {
  display: flex;
}

.courseBtn>div {
  margin: 0px 10px;
}

.courseMbnav {
  width: 100%;
  height: 64px;
  border-radius: 4px;
  opacity: 1;
  background-color: #fff;
  margin: 10px 0px;
  color: #333333;
  font-size: 14px;

}

.courseMbtitie {
  height: 100%;
  display: flex;
  justify-content: space-between;
}

.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
}

.courseBoxw {
  padding: 16px;
  height: 550px;
}

.courseMbBox {
  display: flex;
  align-items: center;
  padding: 0px 10px;
  width: 20%;

  .courseMbBoxLable {
    margin-right: 5px;
  }

  .courseMbBoxItem {
    width: 60%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.courseKjBox {
  width: 100%;
  height: 180px;
  overflow-y: auto;
  padding: 0px 10px;
}

.topFilter {
  padding: 15px;
  height: 60px;
  background-color: #fff;

  .backBar {
    color: #121f3e;
    height: 30px;
    border-bottom: 1px solid #dcdfe6;
  }
}

.courseContent {
  height: calc(100% - 80px);
  padding: 0 16px;

  .courseTab {
    height: 40px;
    background-color: #f6f5fa;
    font-size: 14px;
    text-align: center;
    line-height: 40px;
    display: flex;

    .left {
      flex: 1;
      height: 40px;
      box-sizing: border-box;
    }

    .activeLeft {
      color: #fff;
      background: #3562db;
    }

    .centerLeft {
      width: 0;
      height: 0;
      border-left: 20px solid #3562db;
      border-top: 20px solid transparent;
      border-bottom: 20px solid transparent;
    }

    .centerRight {
      width: 0;
      height: 0;
      border-left: 20px solid transparent;
      border-right: 0 solid transparent;
      border-bottom: 20px solid #3562db;
      border-top: 20px solid #3562db;
      margin-left: -16px;
    }

    .right {
      flex: 1;
      height: 40px;
      background: red($color: #000000);
    }

    .activeRight {
      color: #fff;
      background: #3562db;
    }
  }
  /deep/ .el-input-group__prepend .el-selec {
    position: relative;
    top: 0;
    right: 0;
  }
  .courseInfo {
    height: calc(100% - 60px);
  }

  .formInfo {
    height: calc(100% - 100px);
    overflow: auto;
    margin: 0 0 20px 50px;
  }
  .courseList {
    // height: calc(100% - 50px);
    overflow: auto;
    margin: 0 -16px;

    .examInfo {
      // height: 584px;
      background-color: #fff;
      padding: 16px 24px;
      margin-bottom: 16px;

      .examContent {
        font-size: 14px;
        display: flex;
        flex-wrap: wrap;

        .itemInfo {
          width: 50%;
          margin-top: 16px;

          .title {
            color: #666;
          }

          .value {
            margin-left: 46px;
            color: #333;
          }
        }

        .fraction {
          height: 54px;
          width: 100%;
          margin-top: 16px;
          background-color: #faf9fc;
          padding: 10px;
          font-size: 14px;
        }
      }
    }
  }
}

/deep/ .el-form-item {
  display: inline-block;
}
/deep/ .el-input-group__prepend {
  background-color: #fff;
  width: 80px;
  // position: absolute;
  // right: 10%;
  // top: 10px;
  
}
.set_select_width {
  width: 300px;
  min-height: 80px;
  margin-top: -30px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  display: flex;
  flex-wrap: wrap;

  span {
    height: 20px;
    line-height: 20px;
    background-color: #f4f4f5;
    border-color: #e9e9eb;
    color: #909399;
    padding: 3px 8px;
    font-size: 12px;
    border-radius: 4px;
    margin: 2px 0 2px 6px;
    cursor: pointer;
  }

  p {
    padding-left: 15px;
    color: rgb(191, 196, 204);
    font-size: inherit;
  }
}

.courseTitle {
  font-size: 14px;
  color: #606266;
  margin: 10px 10px 0px 70px;
}

.courseNav {
  display: flex;
}

.courseConnter {
  width: 100%;
  height: 350px;
  margin-top: 10px;
  // border: 1px solid #000;
}

.courseImg {
  margin: 10px 0px;
  width: 100%;
  height: 100px;
  border: 1px solid #ccc;
}

.courseMb {
  margin: 10px 0px;
  width: 100%;
  height: 180px;
  overflow-y: auto;
  background-color: #F6F5FA;
  border-radius: 4px 4px 4px 4px;
}

.courseKj {
  margin: 10px 0px;
  width: 100%;
  height: 180px;
  background-color: #F6F5FA;
  border-radius: 4px 4px 4px 4px;
}

.derwer_footer {
  width: 100%;
  height: 56px;
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0px 0px 12px 3px rgba(0, 0, 0, 0.1);
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
  position: absolute;
  bottom: 0px;
  left: 0px;
  display: flex;
  justify-content: space-between;
  line-height: 56px;
  padding: 0px 20px;
}

.derwer_footerSum {
  font-size: 14px;
  font-weight: 400;
  color: #7F848C;

  .sumNamber {
    font-size: 16px;
    font-weight: 500;
    color: #333333;
  }
}

/deep/ .el-upload-list {
  width: 120px;
}
</style>
