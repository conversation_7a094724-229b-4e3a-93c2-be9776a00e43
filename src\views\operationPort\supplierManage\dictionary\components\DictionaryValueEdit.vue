<script>
import { UsingStatusOptions } from '@/views/operationPort/constant'
import { uploadAcceptDict } from '@/util/dict.js'
// 工程类型字典类型
const DICT_TYPE = 2
export default {
  name: 'DictionaryValueEdit',
  props: {
    visible: Boolean,
    id: Number,
    parentIds: Array,
    readonly: <PERSON><PERSON><PERSON>
  },
  events: ['update:visible', 'success'],
  data: function () {
    return {
      uploadAcceptDict,
      formModel: {
        name: '',
        code: '',
        parentIds: [],
        colour: '',
        status: 1,
        image: ''
      },
      rules: {
        name: [{ required: true, message: '请输入字典值名称' }],
        code: [{ required: true, message: '请输入字典值编码' }],
        status: [{ required: true, message: '请选择字典值状态' }]
      },
      dictData: [],
      fileList: [], // 文件上传组件内容
      loadingStatus: false,
      statusOptions: UsingStatusOptions
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    },
    title() {
      if (this.readonly) {
        return '查看字典值'
      } else {
        return !this.id ? '新增字典值' : '编辑字典值'
      }
    },
    fileAccept() {
      return this.uploadAcceptDict['picture'].type
    },
    toFetch() {
      return this.dialogVisible && this.id
    }
  },
  watch: {
    // 监测dialog打开就获取一次字典
    dialogVisible(value) {
      if (!value) return
      this.$api.SporadicProject.getDictConfigData({ type: DICT_TYPE }).then((res) => {
        if (res.code === '200') {
          this.dictData = res.data
          if (this.parentIds && this.parentIds.length > 0) {
            this.formModel.parentIds = this.parentIds
          }
        }
      })
    },
    toFetch(value) {
      value && this.getDetail()
    }
  },
  methods: {
    // 获取详情信息进行反显
    getDetail() {
      this.loadingStatus = true
      this.$api.SporadicProject.getDictDataById({ id: this.id })
        .then((res) => {
          if (res.code === '200') {
            this.formModel.name = res.data.name
            this.formModel.code = res.data.code
            this.formModel.colour = res.data.colour
            this.formModel.status = res.data.status
            this.formModel.image = res.data.pictureUrl
            // 反显图片
            if (this.formModel.image) {
              this.fileList = [
                {
                  id: Date.now(),
                  url: this.$tools.imgUrlTranslation(this.formModel.image),
                  status: 'success'
                }
              ]
            }
            if (res.data.parentIds) {
              this.formModel.parentIds = res.data.parentIds.split(',').map((it) => Number(it))
            }
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取详情失败'))
        .finally(() => (this.loadingStatus = false))
    },
    // dialog点击右上角关闭按钮，重置表单
    onDialogClosed() {
      this.$refs.formRef.resetFields()
      this.fileList = []
    },
    // 表单提交
    onSubmit() {
      this.$refs.formRef
        .validate()
        .catch(() => Promise.reject())
        .then(() => {
          this.loadingStatus = true
          const [parentId] = this.formModel.parentIds.slice(-1)
          const params = {
            parentId: '', // 父级id
            parentIds: '', // 父级ID集合
            parentName: '', // 字典父级名称
            code: this.formModel.code, // 	字典编码
            name: this.formModel.name, // 字典名称
            colour: this.formModel.colour, // 颜色
            pictureUrl: this.formModel.image, // 图片url
            status: this.formModel.status, // 状态 0-停用 1-启用
            type: DICT_TYPE
          }
          // 如果有父ID，则需要取名称传参
          if (parentId) {
            params.parentId = parentId
            params.parentIds = this.formModel.parentIds.toString()
          }
          if (this.id) {
            params.id = this.id
            return this.$api.SporadicProject.updateDictConfigData(params)
          } else {
            return this.$api.SporadicProject.insertDictConfigData(params)
          }
        })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('操作成功')
            this.$emit('success')
            this.dialogVisible = false
          } else {
            throw '操作失败'
          }
        })
        .catch((msg) => {
          msg && this.$message.error(msg)
        })
        .finally(() => {
          this.loadingStatus = false
        })
    },
    // 文件上传代理
    handleHttpRequest(file) {
      return this.checkFile(file.file)
        .then(() => {
          return this.$api.uploadCommon(__PATH.VUE_SYS_API, 'SysMenu/upload', file)
        })
        .then((res) => {
          if (res.code === '200') {
            this.$message({
              message: '上传成功',
              type: 'success'
            })
            this.formModel.image = res.data
          } else {
            throw res.message
          }
        })
    },
    // 检测文件是否可以上传
    async checkFile(file) {
      if (this.fileList.filter((x) => x.name === file.name).length > 1) {
        throw '已存在此文件，请重新选择'
      }
      const extension = file.name.substring(file.name.lastIndexOf('.'))
      if (!this.fileAccept.includes(extension)) {
        throw '请选择正确的文件类型'
      }
      const fileSize = this.uploadAcceptDict['picture'].fileSize
      const isLt2M = file.size / 1024 / 1024 < fileSize
      if (!isLt2M) {
        throw `上传文件大小不能超过 ${fileSize}MB!`
      }
    },
    // 上传失败提示
    handleUploadError(err) {
      let errMsg = '上传失败'
      if (typeof err === 'string') {
        errMsg = err
      }
      this.$message.error(errMsg)
    },
    // 失踪同步文件列表
    handleFileChange(_, fileList) {
      this.fileList = fileList
    },
    // 文件移除时
    handleFileRemove(_, fileList) {
      this.formModel.image = ''
      this.fileList = fileList
    }
  }
}
</script>
<template>
  <el-dialog
    v-dialogDrag
    class="component dictionary-value-edit"
    :title="title"
    width="750px"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    custom-class="model-dialog"
    @closed="onDialogClosed"
  >
    <el-form ref="formRef" :model="formModel" :rules="rules" label-width="95px" :disabled="readonly" :class="{ readonly }">
      <el-row :gutter="12">
        <el-col :span="24">
          <el-form-item label="上级字典" prop="parentIds">
            <el-cascader
              ref="cascaderRef"
              v-model="formModel.parentIds"
              :options="dictData"
              :props="{ label: 'name', checkStrictly: true, value: 'id' }"
              :placeholder="readonly ? '-' : '请选择'"
              :show-all-levels="false"
              clearable
              :disabled="!!id"
            >
            </el-cascader>
          </el-form-item>
        </el-col>
        <el-col></el-col>
        <el-col :span="12">
          <el-form-item label="字典值名称" prop="name">
            <el-input v-model="formModel.name" placeholder="请输入" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="字典值编码" prop="code">
            <el-input v-model="formModel.code" placeholder="请输入" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="颜色" prop="colour">
            <el-input :value="formModel.colour" :placeholder="readonly ? '-' : '请选择'" class="dictionary-value-edit__color" clearable @clear="formModel.colour = ''">
              <template #prepend>
                <el-color-picker v-model="formModel.colour" size="small"></el-color-picker>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="启用状态" prop="status">
            <el-select v-model="formModel.status" placeholder="请选择">
              <el-option v-for="item of statusOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="图片" prop="image" class="full-line">
            <el-upload
              action=""
              list-type="picture-card"
              class="dictionary-value-edit__upload"
              :class="{ hide: !!formModel.image || readonly }"
              :file-list="fileList"
              :accept="fileAccept"
              :limit="1"
              :http-request="handleHttpRequest"
              :on-change="handleFileChange"
              :on-error="handleUploadError"
              :on-remove="handleFileRemove"
            >
              <i class="el-icon-plus">
                <br />
              </i>
              <div slot="tip" class="el-upload__tip">支持{{ uploadAcceptDict['picture'].type }},且大小不超过{{ uploadAcceptDict['picture'].fileSize }}M</div>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button type="primary" plain @click="dialogVisible = false">取消</el-button>
      <el-button v-if="!readonly" type="primary" @click="onSubmit">确认</el-button>
    </template>
  </el-dialog>
</template>
<style lang="scss">
.component.dictionary-value-edit {
  .el-form {
    background-color: #fff;
    padding: 10px 16px 0;
    .el-form-item {
      .el-cascader,
      .el-select {
        width: 100%;
      }
    }
    &.readonly {
      .el-form-item__label::before {
        display: none;
      }
      .el-form-item {
        .el-input__inner,
        .el-cascader,
        .el-select {
          background-color: transparent;
          border: none;
        }
        .el-input__suffix {
          display: none;
        }
        .el-upload-list__item-status-label {
          display: none;
        }
        .el-upload__tip {
          display: none;
        }
      }
    }
  }
  .dictionary-value-edit {
    &__color {
      vertical-align: middle;
      .el-input-group__prepend {
        padding: 0;
        line-height: 0;
        border-radius: 4px 0 0 4px;
      }
      .el-color-picker {
        height: 30px;
      }
      .el-color-picker__trigger {
        border: none;
        height: 30px;
        width: 30px;
        padding: 2px;
      }
      .el-input__inner {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      }
    }
    &__upload {
      &.hide {
        .el-upload {
          opacity: 0;
          transition-duration: 0.5s;
          pointer-events: none;
        }
      }
    }
  }
}
</style>
