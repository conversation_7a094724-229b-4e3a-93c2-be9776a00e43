<template>
  <div class="bill-view">
    <div class="top_content">
      <el-input
        v-model="filterData.billName"
        style="width: 150px; margin-right: 8px"
        placeholder="搜索账单名称"
        maxlength="60"
        onkeyup="if(value.length>25)value=value.slice(0,60)"
        @keyup.enter.native="searchByCondition"
      ></el-input>
      <el-date-picker
        v-model="filterData.date"
        type="datetimerange"
        style="margin-right: 8px"
        start-placeholder="账单周期"
        range-separator="至"
        end-placeholder="账单周期"
        :default-time="['00:00:00', '23:59:59']"
      >
      </el-date-picker>
      <el-input
        v-model="filterData.userName"
        style="width: 180px; margin-right: 8px"
        placeholder="搜索承租人"
        maxlength="60"
        onkeyup="if(value.length>25)value=value.slice(0,60)"
        @keyup.enter.native="searchByCondition"
      ></el-input>
      <el-input
        v-model="filterData.contractNum"
        style="width: 180px; margin-right: 8px"
        placeholder="搜索合同号"
        maxlength="60"
        onkeyup="if(value.length>25)value=value.slice(0,60)"
        @keyup.enter.native="searchByCondition"
      ></el-input>
      <el-cascader
        ref="spaceIds"
        style="width: 180px; margin-right: 8px; line-height: 32px"
        v-model="filterData.spaceIds"
        filterable
        :show-all-levels="false"
        placeholder="空间位置"
        :options="icmSpaceArray"
        :props="propsSpace"
        clearable
        @change="spaceChang(filterData.spaceIds)"
      ></el-cascader>
      <el-input
        v-model="filterData.houseName"
        style="width: 180px"
        placeholder="房间名称"
        maxlength="60"
        onkeyup="if(value.length>25)value=value.slice(0,60)"
        @keyup.enter.native="searchByCondition"
      ></el-input>
    </div>
    <div class="button-group" style="margin-bottom: 14px">
      <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db" @click="resetCondition">重置</el-button>
      <el-button type="primary" style="font-size: 14px" @click="searchByCondition">查询</el-button>
      <el-button type="primary" style="font-size: 14px" @click="exportData">导出</el-button>
    </div>
    <div class="table_list" style="text-align: right; height: calc(100% - 140px)">
      <el-table
        ref="materialTable"
        v-loading="tableLoading"
        :data="tableData"
        height="100%"
        border
        :header-cell-style="{ background: '#F6F5FA' }"
        style="width: 100%"
        :cell-style="{ padding: '8px 0 8px 0' }"
        stripe
        highlight-current-row
        :empty-text="emptyText"
        row-key="id"
      >
        <el-table-column label="序号" type="index" width="70">
          <template slot-scope="scope">
            <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="billNum" label="账单编号"></el-table-column>
        <el-table-column show-overflow-tooltip prop="billName" label="账单名称"></el-table-column>
        <el-table-column show-overflow-tooltip prop="billCycle" label="账单周期"></el-table-column>
        <el-table-column show-overflow-tooltip prop="billMoney" label="账单金额（元）"></el-table-column>
        <el-table-column show-overflow-tooltip prop="billUserName" label="承租人"></el-table-column>
        <el-table-column show-overflow-tooltip prop="contractNum" label="合同号">
          <template slot-scope="scope">
            <span style="color: rgb(60, 101, 200); cursor: pointer" @click="dblclick(scope.row)">{{ scope.row.contractNum || '' }}</span>
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="spaceName" label="空间位置"></el-table-column>
        <el-table-column show-overflow-tooltip prop="houseName" label="房间名称"></el-table-column>
      </el-table>
    </div>
    <div class="" style="padding-top: 10px; text-align: right">
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        :current-page="paginationData.currentPage"
        :page-sizes="[15, 30, 50, 100]"
        :page-size="paginationData.pageSize"
        :total="paginationData.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
  </div>
</template>
<script>
import axios from 'axios'
import { transData } from '@/util'
import moment from 'moment'
export default {
  data() {
    return {
      moment,
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      tableData: [],
      // 列表过滤条件
      filterData: {
        billName: '', // 账单名称，绑定到输入框用于搜索账单名称
        date: [], // 时间范围，绑定到日期选择器，用于选择账单的开始和结束日期
        userName: '', // 承租人名称，绑定到输入框用于搜索承租人
        contractNum: '', // 合同号，绑定到输入框用于搜索合同号
        spaceIds: '', // 空间位置，绑定到 Cascader 级联选择器
        houseName: '' // 房间名称，绑定到输入框用于搜索房间名称
      },
      emptyText: '暂无数据',
      tableLoading: false,
      pageSize: '',
      currentPage: '',
      propsSpace: {
        checkStrictly: true,
        emitPath: false,
        value: 'id',
        label: 'spaceName',
        children: 'list'
      },
      icmSpaceArray: [],
      spaceList: [],
      expiringCount: 0
    }
  },
  mounted() {
    this.spaceTreeListFn()
    this.getBillList()
  },
  methods: {
    // 获取账单列表
    getBillList() {
      const params = {
        pageNum: this.paginationData.currentPage, //页码
        pageSize: this.paginationData.pageSize, //	页大小
        billName: this.filterData.billName, //账单名称
        houseName: this.filterData.houseName, //房源名称
        userName: this.filterData.userName, //承租人名称
        spaceIds: this.filterData.spaceIds, //空间code
        contractNum: this.filterData.contractNum, //合同号
        billStartCycle: this.filterData.date && this.filterData.date.length > 0 ? moment(this.filterData.date[0]).format('YYYY-MM-DD HH:mm:ss') : '',
        billEndCycle: this.filterData.date && this.filterData.date.length > 0 ? moment(this.filterData.date[1]).format('YYYY-MM-DD HH:mm:ss') : ''
      }
      this.tableLoading = true
      this.$api.billListByContractId(params).then((res) => {
        if (res.code == '200') {
          this.tableData = res.data.records
          this.paginationData.total = res.data.total
        } else {
          this.$message.error(res.message)
        }
        this.tableLoading = false
      })
    },
    // 条件查询
    searchByCondition() {
      this.paginationData.currentPage = 1
      this.getBillList()
    },
    // 重置查询条件
    resetCondition() {
      this.paginationData = {
        currentPage: 1,
        pageSize: 15,
        total: 0
      }
      this.filterData = {
        planName: '',
        dept: '',
        personName: '',
        status: '0',
        date: [],
        acceptance: ''
      }
      this.getBillList()
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.getBillList()
    },
    // 每页的条数
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.paginationData.currentPage = 1
      this.getBillList()
    },
    /** 导出 */
    exportData() {
      const userInfo = this.$store.state.user.userInfo.user
      const params = {
        unitCode: userInfo.unitCode ?? 'BJSYGJ',
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        billName: this.filterData.billName, //账单名称
        houseName: this.filterData.houseName, //房源名称
        userName: this.filterData.userName, //承租人名称
        spaceIds: this.filterData.spaceIds, //空间code
        contractNum: this.filterData.contractNum, //合同号
        billStartCycle: this.filterData.date && this.filterData.date.length > 0 ? moment(this.filterData.date[0]).format('YYYY-MM-DD HH:mm:ss') : '',
        billEndCycle: this.filterData.date && this.filterData.date.length > 0 ? moment(this.filterData.date[1]).format('YYYY-MM-DD HH:mm:ss') : ''
      }
      axios({
        method: 'get',
        url: __PATH.VUE_RHMS_API + 'bill/exportExcel',
        // data: formData,
        params: params,
        responseType: 'blob',
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token,
          'operation-type': 4,
          unitCode: userInfo.unitCode ?? 'BJSYGJ',
          hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY'
        }
      })
        .then((res) => {
          const name = res.headers['content-disposition'].split(';')[1].split('filename=')[1]
          const blob = new Blob([res.data])
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
        })
        .catch((err) => {
          console.log(err, 'err')
          this.$message.error('下载失败')
        })
    },
    //  ------------------------------获取空间结构 Tree And fn
    spaceTreeListFn() {
      this.$api.spatialInformationTree().then((res) => {
        if (res.code == 200) {
          this.spaceList = res.data
          this.icmSpaceArray = transData(res.data, 'id', 'parentId', 'list')
        }
      })
    },
    spaceChang(val) {
      if (val) {
        this.spaceList.forEach((item) => {
          if (item.id == val) {
            this.filterData.spaceName = item.spaceName || item.id
          }
        })
      } else {
        this.filterData.spaceName
      }
    },

    // 双击查看详情
    dblclick(val) {
      this.$router.push({
        path: '/billManagement/ContractDetails',
        query: {
          id: val.contractId
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.bill-view {
  height: 100%;
  .top_content {
    width: 100%;
    // height: 10%;
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    // gap: 10px;
    > :deep(.el-input) {
      width: 20% !important;
    }

    > :deep(.el-select) {
      margin-right: 20px;
    }
  }
}
</style>