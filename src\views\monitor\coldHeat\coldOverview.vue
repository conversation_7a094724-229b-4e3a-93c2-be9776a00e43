<template>
  <!-- 给排水运行总览 -->
  <PageContainer class="sewerageOverview">
    <div slot="header" class="sewerageOverview-header">
      <p class="header-title">冷热源系统运行总览</p>
      <div class="control-btn-header">
        <el-button class="btn-item" :class="{ 'btn-active': requestInfo.timeType == 0 }" plain @click="timeTypeChange(0)">今日</el-button>
        <el-button class="btn-item" :class="{ 'btn-active': requestInfo.timeType == 1 }" plain @click="timeTypeChange(1)">本月</el-button>
        <el-button class="btn-item" :class="{ 'btn-active': requestInfo.timeType == 2 }" plain @click="timeTypeChange(2)">本年</el-button>
        <el-button class="btn-item" :class="{ 'btn-active': requestInfo.timeType == 3 }" plain style="margin-right: 10px" @click="timeTypeChange(3)">自定义</el-button>
        <el-date-picker
          v-model="requestInfo.dataRange"
          type="daterange"
          unlink-panels
          :disabled="requestInfo.timeType != 3"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          clearable
        />
        <div style="display: inline-block">
          <el-button v-auth="'userManagement:reset'" type="primary" plain @click="resetForm">重置</el-button>
          <el-button v-auth="'userManagement:search'" type="primary" @click="searchForm">查询</el-button>
        </div>
      </div>
    </div>
    <div slot="content" class="sewerageOverview-content">
      <el-row v-loading="loading.statistics" :gutter="16" style="height: 18%">
        <el-col v-for="item in statisticsList" :key="item.name" :xs="12" :md="12" :lg="4">
          <div class="statistics-item" :style="{ cursor: item.type ? 'pointer' : '' }" @click="viewDetails(item.type)">
            <div>
              <p class="statistics-item-name">{{ item.name }}</p>
              <p class="statistics-item-value">
                <span class="value">{{ item.value }}</span>
                <span class="unit">{{ item.unit }}</span>
              </p>
            </div>
            <img v-if="item.icon" class="statistics-item-icon" :src="item.icon" :alt="item.name" />
            <div v-else class="statistics-item-average">
              <p>平均能效比</p>
              <span>{{ item.average }}</span>
            </div>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="16" style="height: 45%">
        <el-col :xs="24" :md="24" :lg="16">
          <ContentCard v-loading="loading.waterPumpTotal" :showTitle="false">
            <div slot="tabs">
              <el-tabs v-model="equipmentType" @tab-click="equipmentClick">
                <el-tab-pane v-for="item in equipmentTypeList" :key="item.entityTypeId" :label="item.entityTypeName" :name="item.entityTypeId"></el-tab-pane>
              </el-tabs>
            </div>
            <div slot="content" class="waterPumpTotal" :style="{ height: 'calc(100% - 40px)' }">
              <div class="waterPumpTotal-left">
                <div>
                  <img src="../../../assets/images/monitor/yxsc_icon.png" />
                  <p class="left-label">运行时长</p>
                  <p style="margin-top: 8px">
                    <span class="left-value">{{ waterRunTimeData.hour }}</span>
                    <span class="left-unit">时</span>
                    <!-- <span class="left-value" style="margin-left: 4px">{{ waterRunTimeData.minute }}</span>
                    <span class="left-unit">分</span> -->
                  </p>
                </div>
                <!-- <div>
                  <p class="left-label">平均运行率</p>
                  <p class="left-value" style="margin-top: 8px;">{{ waterRunTimeData.svgRatio }}</p>
                </div> -->
              </div>
              <div class="waterPumpTotal-right" @click="entityTypeQuery">
                <echarts ref="waterPumpTotalChart" domId="waterPumpTotalChart" />
                <!-- <echarts ref="waterPumpTotalChart" domId="waterPumpTotalChart" onTimeLineChange @timelinechanged="(val) => timelinechanged('waterPumpTotal', val)" /> -->
              </div>
            </div>
          </ContentCard>
        </el-col>
        <el-col :xs="24" :md="24" :lg="8">
          <ContentCard title="供冷趋势">
            <div slot="title-right" class="view_right">
              <span>最近1小时平均能效比：{{ coolingData.avgPowerRatio || '-' }}</span>
              <p>{{ coolingData.avgPowerRatioLevel }}</p>
            </div>
            <div slot="content" style="width: 100%; height: 100%">
              <echarts ref="coolingTrend" domId="coolingTrend" />
            </div>
          </ContentCard>
        </el-col>
      </el-row>
      <el-row :gutter="16" style="height: 37%">
        <el-col :xs="24" :md="24" :lg="16">
          <ContentCard v-loading="loading.alarmStatistics" title="报警统计（单）">
            <p slot="title-right" class="viewDetails" @click="viewDetails(6)">详情</p>
            <div slot="content" style="width: 100%; height: 100%; display: flex">
              <div class="cardContent-left">
                <div v-for="item in alarmStatisticsList" :key="item.title" class="left-item">
                  <p class="item-title">{{ item.title }}</p>
                  <p class="item-value">{{ item.value || 0 }}<span>个</span></p>
                  <img class="item-icon" :src="item.icon" :alt="item.title" />
                </div>
              </div>
              <div>
                <echarts ref="alarmStatistics" domId="alarmStatistics" width="40%" height="100%" />
              </div>
            </div>
          </ContentCard>
        </el-col>
        <el-col :xs="24" :md="24" :lg="8">
          <ContentCard title="供热趋势">
            <div slot="title-right" class="view_right">
              <span>最近1小时平均能效比：{{ heatingData.avgPowerRatio || '-' }}</span>
              <p>{{ heatingData.avgPowerRatioLevel }}</p>
            </div>
            <div slot="content" style="width: 100%; height: 100%">
              <echarts ref="heatingTrends" domId="heatingTrends" />
            </div>
          </ContentCard>
        </el-col>
      </el-row>
      <monitorStatisticsDialog
        v-if="monitorStatisticsDialog"
        :type="detailsType"
        :title="detailsTitle"
        :projectCode="requestInfo.projectCode"
        :entityList="entityList"
        :visible.sync="monitorStatisticsDialog"
      />
      <alarmStatisticsDialog v-if="alarmStatisticsDialog" :projectCode="requestInfo.projectCode" :visible.sync="alarmStatisticsDialog" />
    </div>
  </PageContainer>
</template>
<script>
import zhslIcon from '@/assets/images/monitor/zhsl_icon.png'
import zhdlIcon from '@/assets/images/monitor/zhdl_icon.png'
import gztjIcon from '@/assets/images/monitor/gztj_icon.png'
import lxtjIcon from '@/assets/images/monitor/lxtj_icon.png'
import alarmStatistics from '@/assets/images/monitor/alarm-statistics.png'
import alarmAlarm from '@/assets/images/monitor/alarm-pending.png'
import alarmDoing from '@/assets/images/monitor/alarm-doing.png'
import monitorStatisticsDialog from './components/monitorStatisticsDialog'
import alarmStatisticsDialog from '../components/alarmStatisticsDialog/index.vue'
import moment from 'moment'
import { monitorTypeList } from '@/util/dict.js'
moment.locale('zh-cn')
export default {
  name: 'sewerageOverview',
  components: {
    monitorStatisticsDialog,
    alarmStatisticsDialog
  },
  data() {
    return {
      detailsTitle: '',
      equipmentTypeList: [],
      equipmentType: '',
      detailsType: '', // 详情类型 1:水泵运行状态统计, 2 高液位状态统计, 3 低液位状态统计, 4 统计故障, 5 统计离线
      monitorStatisticsDialog: false,
      alarmStatisticsDialog: false,
      entityList: [], // 设备列表
      statisticsList: [
        // 总览顶部6个
        {
          type: 1,
          icon: gztjIcon,
          name: '故障统计',
          unit: '次',
          value: 0
        },
        {
          type: 2,
          icon: lxtjIcon,
          name: '离线统计',
          unit: '次',
          value: 0
        },
        {
          icon: zhdlIcon,
          name: '总耗电量',
          unit: 'kw',
          value: 0
        },
        {
          icon: zhslIcon,
          name: '总耗水量',
          unit: 't',
          value: 0
        },
        {
          icon: null,
          name: '总供冷量',
          unit: 'W',
          value: 0,
          average: 0
        },
        {
          icon: null,
          name: '总供热量',
          unit: 'W',
          value: 0,
          average: 0
        }
      ],
      alarmStatisticsList: [
        {
          title: '报警统计',
          icon: alarmStatistics,
          value: 0
        },
        {
          title: '未处理',
          icon: alarmAlarm,
          value: 0
        },
        {
          title: '处理中',
          icon: alarmDoing,
          value: 0
        }
      ],
      waterPumpTotalData: {},
      waterRunTimeData: {
        svgRatio: '',
        hour: '',
        minute: ''
      },
      heatingData: {}, // 供热
      coolingData: {}, // 供冷
      requestInfo: {
        projectCode: monitorTypeList.find((item) => item.projectName == '冷热源监测').projectCode,
        dataRange: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')], // 时间范围
        timeType: 0 // 0: 当天 1: 本月 2: 本年 3: 自定义)
      },
      loading: {
        statistics: false, // 次数统计
        waterStatistics: false, // 排污给水量统计
        waterPumpTotal: false, // 水泵运行统计
        alarmStatistics: false // 报警统计
      }
    }
  },
  computed: {},
  created() {
    this.init()
    this.getEntityMenuList()
  },
  methods: {
    entityTypeQuery() {
      this.detailsTitle = this.equipmentTypeList.find((v) => v.entityTypeId == this.equipmentType).entityTypeName
      this.detailsType = this.equipmentType
      this.monitorStatisticsDialog = true
    },
    // 供冷/热趋势
    getMockEcharts(params) {
      this.$api
        .GetColdHotTrend(params)
        .then((res) => {
          // debugger
          if (res.code == 200) {
            let data = res.data
            this.heatingData = data.find((v) => v.coldOrHot == 1)
            this.coolingData = data.find((v) => v.coldOrHot == 0)
            this.$refs.coolingTrend.init(
              this.lineChartBarChart(
                0,
                this.coolingData.dataList.map((v) => v.time),
                this.coolingData.powerList.map((v) => v.count),
                this.coolingData.dataList.map((v) => v.count),
                this.coolingData.paramUnit
              )
            )
            this.$refs.heatingTrends.init(
              this.lineChartBarChart(
                1,
                this.heatingData.dataList.map((v) => v.time),
                this.heatingData.powerList.map((v) => v.count),
                this.heatingData.dataList.map((v) => v.count),
                this.heatingData.paramUnit
              )
            )
          } else {
            console.log(2)
            this.$refs.coolingTrend.init(this.lineChartBarChart())
            this.$refs.heatingTrends.init(this.lineChartBarChart())
          }
        })
        .catch(() => {
          console.log(3)
          this.$refs.coolingTrend.init(this.lineChartBarChart())
          this.$refs.heatingTrends.init(this.lineChartBarChart())
        })
    },
    // 折线图+柱状图
    lineChartBarChart(type, xData = [], barData = [], lineData = [], unit) {
      let option
      if (xData && xData.length) {
        option = {
          backgroundColor: '#fff',
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              label: {
                color: '#fff'
              },
              crossStyle: {
                color: '#999'
              }
            }
          },
          grid: {
            top: '10%',
            left: '3%',
            right: '3%',
            bottom: '20',
            containLabel: true
          },
          legend: {
            top: '1%',
            itemGap: 50,
            data: type == 0 ? ['供冷量', '能效比'] : ['供热量', '能效比'],
            textStyle: {
              color: '#121F3E'
            }
          },
          dataZoom: [
            {
              type: 'slider',
              show: true,
              xAxisIndex: [0],
              bottom: 5,
              start: 0,
              end: 20,
              height: 6,
              borderRadius: 0,
              borderColor: '#D7DEE8',
              fillerColor: '#C4C4C4', // 滑动块的颜色
              // backgroundColor: "#33384b", //两边未选中的滑动条区域的颜色
              // 是否显示detail，即拖拽时候显示详细数值信息
              showDetail: false,
              zoomLock: false,
              brushSelect: false,
              // 控制手柄的尺寸
              handleSize: 8,
              // 是否在 dataZoom-silder 组件中显示数据阴影。数据阴影可以简单地反应数据走势。
              showDataShadow: false,
              // filterMode: 'filter',
              handleIcon: 'M0,0 v11h3 v-11h-3 Z',
              handleStyle: {
                color: '#FFF',
                shadowOffsetX: 0, // 阴影偏移x轴多少
                shadowOffsetY: 0 // 阴影偏移y轴多少
                // borderCap: 'square',
                // borderColor: '#D8DFE9',
                // borderType: [15, 20],
              }
            },
            {
              type: 'inside',
              // show: false,
              xAxisIndex: [0],
              zoomOnMouseWheel: false, // 关闭滚轮缩放
              moveOnMouseWheel: true, // 开启滚轮平移
              moveOnMouseMove: true // 鼠标移动能触发数据窗口平移
            }
          ],
          xAxis: [
            {
              type: 'category',
              boundaryGap: true,
              axisLine: {
                show: false
              },
              axisLabel: {
                color: '#414653',
                textStyle: {
                  fontSize: 12
                }
                // formatter: function (name) {
                //   return name.length > 3 ? name.slice(0, 3) + '...' : name
                // }
              },
              axisTick: {
                show: false
              },
              data: xData
            }
          ],
          yAxis: [
            {
              type: 'value',
              min: 0,
              max: Math.max(...barData, ...lineData),
              splitNumber: 7,
              splitLine: {
                show: true,
                lineStyle: {
                  color: '#eceff3'
                }
              },
              axisLine: {
                show: false
              },
              axisLabel: {
                margin: 20,
                formatter: '{value}',
                color: '#414653'
              },
              axisTick: {
                show: false
              }
            },
            {
              type: 'value',
              min: 0,
              max: 100,
              splitNumber: 7,
              splitLine: {
                show: false,
                lineStyle: {
                  color: '#eceff3'
                }
              },
              axisLine: {
                show: false
              },
              axisLabel: {
                formatter: '{value} %',
                margin: 20,
                color: '#414653'
              },
              axisTick: {
                show: false
              }
            }
          ],
          series: [
            {
              name: '能效比',
              type: 'bar',
              // barMinWidth: 10,
              // barMaxWidth: 20,
              barWidth: '45%',
              tooltip: {
                show: true,
                valueFormatter: (value) => Number(value) + '%'
              },
              // label: {
              //   show: true,
              //   position: 'top',
              //   color: '#3562DB'
              // },
              itemStyle: {
                color: '#3562DB'
              },
              data: barData
            },
            {
              name: type == 0 ? '供冷量' : '供热量',
              type: 'line',
              // smooth: true, //是否平滑曲线显示
              symbol: 'circle', // 默认是空心圆（中间是白色的），改成实心圆
              showAllSymbol: true,
              symbolSize: 6,
              lineStyle: {
                color: '#FF9435', // 线条颜色
                borderColor: '#f0f'
              },
              // label: {
              //   show: true,
              //   position: 'top',
              //   color: '#FF9435',
              // },
              itemStyle: {
                color: '#FF9435'
              },
              tooltip: {
                show: true,
                valueFormatter: (value) => Number(value) + unit
              },
              data: lineData
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      return option
    },
    // 点击设备选项卡
    equipmentClick(tab) {
      let params = {
        startTime: this.requestInfo.dataRange[0],
        endTime: this.requestInfo.dataRange[1],
        projectCode: this.requestInfo.projectCode,
        timeType: this.requestInfo.timeType,
        entityTypeId: this.equipmentType
      }
      this.getWaterPumpTotal(params)
      this.getMockEcharts(params)
    },
    init() {
      let params = {
        startTime: this.requestInfo.dataRange[0],
        endTime: this.requestInfo.dataRange[1],
        projectCode: this.requestInfo.projectCode,
        timeType: this.requestInfo.timeType
      }
      this.getStatisticsNum(params)
      this.$nextTick(() => {
        this.getEntityTypeGroup(params)
        this.getAirRunPolice(params)
      })
    },
    // 图表月分页
    timelinechanged(type, month) {
      let params = {
        startTime: this.requestInfo.dataRange[0],
        endTime: this.requestInfo.dataRange[1],
        projectCode: this.requestInfo.projectCode,
        timeType: this.requestInfo.timeType,
        yearMouth: month.currentIndex + 1
      }
      if (type == 'waterPumpTotal') {
        this.getWaterPumpTotal(params, month.currentIndex)
      }
    },
    // 查看详情
    viewDetails(type) {
      if (!type) return
      this.detailsType = type
      if ([1, 2].includes(type)) {
        this.monitorStatisticsDialog = true
        this.detailsTitle = this.statisticsList.find((v) => v.type == type).name
      } else {
        this.alarmStatisticsDialog = true
      }
      console.log(type)
    },
    // 获取设备菜单
    getEntityMenuList() {
      this.$api.GetEntityMenuList({ projectId: this.requestInfo.projectCode }).then((res) => {
        if (res.code == 200) {
          this.entityList = res.data
        }
      })
    },
    // 报警统计
    getAirRunPolice(params) {
      let newArr = []
      this.loading.alarmStatistics = true
      this.$api
        .GetAirRunPolice(params)
        .then((res) => {
          this.loading.alarmStatistics = false
          if (res.code == 200) {
            ;(res.data?.policeList ?? []).forEach((item) => {
              newArr.push({
                name: item.menuName,
                value: item.policeCount
              })
            })
            this.alarmStatisticsList[0].value = res.data?.total ?? 0
            this.alarmStatisticsList[1].value = res.data?.noDealCount ?? 0
            this.alarmStatisticsList[2].value = res.data?.isDealCount ?? 0
            this.$refs.alarmStatistics.init(this.setPieChart(newArr))
          } else {
            this.$refs.alarmStatistics.init(this.setPieChart())
          }
        })
        .catch(() => {
          this.loading.alarmStatistics = false
          this.$refs.alarmStatistics.init(this.setPieChart())
        })
    },
    setPieChart(data) {
      let option
      var colors = ['#5e81ec', '#ffc855', '#98e79b', '#00d695', '#00b29a', '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc']
      var total = data.reduce((p, v) => {
        return p + v.value
      }, 0)
      if (data.length) {
        option = {
          title: {
            text: '监测项类型报警占比',
            left: 'center',
            textStyle: {
              fontSize: 15,
              fontWeight: 500
            }
          },
          // grid: {
          //   top: '15%',
          //   left: '3%',
          //   right: '3%',
          //   bottom: '20',
          //   containLabel: true
          // },
          legend: {
            orient: 'vertical',
            x: '60%',
            y: 'center',
            itemWidth: 20,
            itemHeight: 20,
            align: 'left',
            textStyle: {
              fontSize: 14,
              color: '#000'
            },
            data: data.map((item) => item.name),
            formatter: (name) => {
              let item = data.find((v) => v.name == name)
              return `${name}  ${((item.value / total) * 100).toFixed(2)}%  ${item.value}单`
            }
          },
          tooltip: {
            trigger: 'item',
            formatter: '{b} : {c} ({d}%)'
          },
          color: colors,
          calculable: true,
          series: [
            {
              type: 'pie',
              radius: ['45%', '70%'],
              center: ['30%', '50%'],
              roseType: 'radius',
              label: {
                show: false
              },
              labelLine: {
                length: 1,
                length2: 20,
                smooth: true
              },
              data: data
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      return option
    },
    // 运行统计
    getWaterPumpTotal(params, currentIndex) {
      this.loading.waterPumpTotal = true
      this.$api
        .GetRunningTrend(params)
        .then((res) => {
          this.loading.waterPumpTotal = false
          if (res.code == 200) {
            this.waterRunTimeData = res.data
            // this.waterPumpTotalData = res.data.barChart
            this.$refs.waterPumpTotalChart.init(this.getWaterPumpTotalChaet(res.data.barChart, currentIndex))
            this.$nextTick(() => {
              this.$refs.waterPumpTotalChart.chartResize()
            })
          } else {
            this.$refs.waterPumpTotalChart.init(this.getWaterPumpTotalChaet())
          }
        })
        .catch(() => {
          this.$refs.waterPumpTotalChart.init(this.getWaterPumpTotalChaet())
          this.loading.waterPumpTotal = false
        })
    },
    getWaterPumpTotalChaet(data = []) {
      let option
      if (data.length) {
        option = {
          title: {
            show: false,
            text: '水泵运行统计'
          },
          tooltip: {
            trigger: 'axis'
          },
          grid: {
            top: '20%',
            left: '3%',
            right: '4%',
            bottom: '5%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            axisLabel: {
              // 坐标轴刻度标签的相关设置。
              textStyle: {
                color: '#414653',
                fontSize: 12
              }
            },
            axisTick: {
              // 坐标轴刻度相关设置。
              show: false
            },
            axisLine: {
              // 坐标轴轴线相关设置
              lineStyle: {
                color: '#F1F3F5'
              }
            },
            splitLine: {
              // 坐标轴在 grid 区域中的分隔线。
              show: false
            }
          },
          yAxis: [
            {
              type: 'value',
              axisLabel: {
                textStyle: {
                  color: '#414653',
                  fontSize: 12
                }
              },
              axisLine: {
                show: false
              },
              axisTick: {
                show: false
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: '#F1F3F5'
                }
              }
            }
          ],
          series: [
            {
              name: '运行时长',
              type: 'bar',
              barWidth: '20',
              tooltip: {
                show: true,
                valueFormatter: (value) => Number(value) + 'h'
              },
              itemStyle: {
                color: '#3562DB'
              },
              data: data.map((item) => [item.name, item.value])
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      return option
    },
    // 获取实体类型分组
    getEntityTypeGroup(params) {
      let newParams = {
        projectCode: params.projectCode
      }
      this.$api.GetEntityTypeGroup(newParams).then((res) => {
        if (res.code == 200) {
          this.equipmentTypeList = res.data.map((item) => ({ entityTypeName: item.entityTypeName, entityTypeId: item.entityTypeId.toString() }))
          this.equipmentType = this.equipmentTypeList[0].entityTypeId
          this.getWaterPumpTotal({ ...params, entityTypeId: res.data[0].entityTypeId })
          this.getMockEcharts({ ...params, entityTypeId: res.data[0].entityTypeId })
        }
      })
    },
    // 统计信息
    getStatisticsNum(params) {
      this.loading.statistics = true
      this.$api
        .GetColdOverview(params)
        .then((res) => {
          this.loading.statistics = false
          if (res.code == 200) {
            this.statisticsList[0].value = res.data?.faultCount ?? 0
            this.statisticsList[1].value = res.data?.offLineCount ?? 0
            this.statisticsList[2].value = res.data?.powerCount ?? 0
            this.statisticsList[3].value = res.data?.waterCount ?? 0
            this.statisticsList[4].value = res.data?.coldCount ?? 0
            this.statisticsList[5].value = res.data?.hotCount ?? 0
            this.statisticsList[4].average = res.data?.coldPowerRatio ?? 0
            this.statisticsList[5].average = res.data?.hotPowerRatio ?? 0
          }
        })
        .catch(() => {
          this.loading.statistics = false
        })
    },
    // 日期选择
    timeTypeChange(type) {
      let newObj = {
        0: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        1: [moment().startOf('month').format('YYYY-MM-DD'), moment().endOf('month').format('YYYY-MM-DD')],
        2: [moment().startOf('year').format('YYYY-MM-DD'), moment().endOf('year').format('YYYY-MM-DD')],
        3: []
      }
      this.requestInfo.dataRange = newObj[type]
      this.requestInfo.timeType = type
    },
    // 重置查询表单
    resetForm() {
      this.requestInfo = {
        projectCode: monitorTypeList.find((item) => item.projectName == '冷热源监测').projectCode,
        dataRange: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        timeType: 0
      }
      this.init()
    },
    // 查询
    searchForm() {
      if (!this.requestInfo.dataRange.length) {
        this.$message.warning('请选择时间段！')
        return
      }
      this.init()
    }
  }
}
</script>
<style lang="scss" scoped>
.view_right {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #414653;
  position: absolute;
  right: 15px;
  top: 0;
  p {
    padding: 0 5px;
    background: #ffece8;
    color: #f53f3f;
    margin-left: 5px !important;
  }
}
::v-deep .container-content {
  overflow-y: auto !important;
}
.sewerageOverview {
  p {
    margin: 0;
  }
  .sewerageOverview-header {
    .header-title {
      border-bottom: 1px solid #e4e7ed;
      padding: 13px 16px;
      font-size: 14px;
      font-weight: 500;
      color: #121f3e;
      line-height: 14px;
    }
    .control-btn-header {
      padding: 6px 6px 16px 16px;
      & > div {
        margin-right: 10px;
        margin-top: 10px;
      }
      .btn-item {
        border: 1px solid #3562db;
        color: #3562db;
        font-family: none;
      }
      .btn-active {
        color: #fff;
        background: #3562db;
      }
    }
  }
  .sewerageOverview-content {
    height: 100%;
    .statistics-item {
      width: 100%;
      margin-top: 16px;
      height: calc(100% - 16px);
      border-radius: 4px;
      background: #fff;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-right: 20px;
      .statistics-item-average {
        display: flex;
        flex-direction: column;
        p {
          font-size: 12px;
          color: #414653;
          margin-bottom: 8px;
        }
        span {
          font-size: 16px;
          color: #3562db;
        }
      }
      .statistics-item-icon {
        width: 48px;
        height: 45.6px;
      }
      .statistics-item-name {
        font-size: 15px;
        font-weight: 500;
        color: #121f3e;
        line-height: 18px;
        margin-left: 24px;
        margin-bottom: 10px;
      }
      .statistics-item-value {
        margin-left: 24px;
        .value {
          font-size: 30px;
          font-weight: bold;
          color: #121f3e;
          line-height: 36px;
        }
        .unit {
          margin-left: 4px;
          font-size: 15px;
          font-weight: 500;
          color: #ccced3;
          line-height: 18px;
        }
      }
    }
    .waterStatistics {
      height: 100%;
      min-height: 240px;
      display: flex;
      flex-direction: column;
      .waterStatistics-heade {
        height: 100px;
        padding: 0 24px;
        background: #faf9fc;
        display: flex;
        .heade-value {
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: space-around;
          .value-item {
            display: flex;
            align-items: center;
          }
          .item-name {
            font-size: 14px;
            font-weight: 500;
            color: #121f3e;
            line-height: 14px;
          }
          .item-value {
            font-size: 30px;
            font-weight: bold;
            color: #414653;
            margin-left: 16px;
            line-height: 22px;
          }
          .item-unit {
            font-size: 15px;
            font-weight: 500;
            color: #ccced3;
            margin-left: 4px;
            line-height: 15px;
          }
        }
        .heade-progress {
          display: flex;
          flex-direction: column;
          justify-content: space-around;
          margin-left: 24px;
          flex: 1;
          height: 100%;
        }
        ::v-deep .el-progress {
          .el-progress-bar {
            .el-progress-bar__outer {
              height: 18px !important;
              border-radius: 0;
            }
            .el-progress-bar__inner {
              border-radius: 0;
            }
          }
        }
      }
      .waterStatistics-chart {
        flex: 1;
      }
    }
    .waterPumpTotal {
      width: 100%;
      height: calc(100% - 16px);
      display: flex;
      .waterPumpTotal-left {
        padding: 0 24px;
        background: #faf9fc;
        > div {
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          text-align: center;
        }
        p {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        img {
          width: 66px;
          height: 66px;
          margin: 0 auto;
        }
        .left-label {
          font-size: 15px;
          font-weight: 500;
          color: #121f3e;
        }
        .left-value {
          font-size: 30px;
          font-weight: bold;
          color: #121f3e;
        }
        .left-unit {
          font-size: 15px;
          font-weight: 500;
          color: #ccced3;
          margin-left: 4px;
        }
      }
      .waterPumpTotal-right {
        flex: 1;
        overflow: hidden;
      }
    }
    .cardContent-left {
      display: flex;
      align-items: center;
      width: 60%;
      height: 100%;
      p {
        margin: 0;
      }
      .left-item {
        width: calc(100% / 3 - 16px);
        height: 140px;
        margin-right: 16px;
        padding: 24px 24px 30px;
        background: #faf9fc;
        border-radius: 4px;
        margin-bottom: 7px;
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .item-title {
          font-size: 15px;
          font-weight: 500;
          color: #121f3e;
        }
        .item-value {
          height: 36px;
          font-size: 40px;
          font-weight: bold;
          color: #121f3e;
          line-height: 36px;
          & > span {
            margin-left: 4px;
            font-size: 15px;
            font-weight: 500;
            color: #ccced3;
          }
        }
        .item-icon {
          position: absolute;
          right: 24px;
          bottom: 24px;
          width: 40px;
          height: 40px;
        }
      }
      & :last-child {
        margin-bottom: 0;
      }
    }
    ::v-deep .el-row {
      .el-col {
        height: 100%;
      }
    }
    .box-card {
      margin-top: 16px;
      height: calc(100% - 16px);
      .viewDetails {
        user-select: none;
        cursor: pointer;
        margin: 0 !important;
        font-size: 14px;
        color: #3562db;
        position: absolute;
        right: 15px;
        top: 0;
      }
      .card-body {
        height: calc(100% - 25px);
        margin: 0;
      }
    }
  }
}
</style>
