<template>
  <PageContainer>
    <div slot="content" class="camera-management">
      <div class="top_filters">
        <el-input v-model.trim="parameter.icmName" clearable placeholder="摄像机名称" class="sino_sdcp_input mr15"></el-input>
        <el-select v-model="parameter.icmManufacturer" class="sino_sdcp_input mr15" placeholder="请选择厂家">
          <el-option v-for="item in icmManufacturer" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <el-select v-model="parameter.icmType" class="sino_sdcp_input mr15" placeholder="请选择摄像机类型">
          <el-option v-for="item in icmType" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <el-button type="primary" plain @click="resetData">重置</el-button>
        <el-button type="primary" @click="searchClick()">查询</el-button>
        <el-button type="primary" @click="operation('add')">新增</el-button>
      </div>
      <div class="right-content">
        <div class="table-content">
          <TablePage
            ref="tablePage"
            v-loading="tableLoading"
            :tableColumn="tableColumn"
            :data="tableData"
            border
            height="calc(100% - 40px)"
            :showPage="true"
            :pageData="paginationData"
            @pagination="paginationChange"
          >
          </TablePage>
        </div>
      </div>
      <template v-if="dialogVisible">
        <addCamera ref="addCamera" :dialogVisible.sync="dialogVisible" :ifType="ifType" :checkedData="checkedData" @sure="sure"></addCamera>
      </template>
    </div>
  </PageContainer>
</template>
<script lang="jsx">
export default {
  name: 'cameraManagement',
  components: { addCamera: () => import('./addCamera') },
  data() {
    return {
      ifType: '',
      dialogVisible: false,
      paginationData: {
        page: 1,
        pageSize: 15,
        total: 0
      },
      tableLoading: false,
      tableData: [],
      tableColumn: [
        {
          prop: 'sensorName',
          label: '序号',
          type: 'index',
          width: '80',
          formatter: (scope) => {
            // 当前分页重置
            // return scope.$index + 1
            // 跨分页序号
            return (this.paginationData.page - 1) * this.paginationData.pageSize + scope.$index + 1
          }
        },
        {
          label: '摄像机名称',
          prop: 'icmName'
        },
        {
          label: '摄像机IP',
          prop: 'icmIp'
        },
        {
          label: '通道号',
          prop: 'icmNumber'
        },
        {
          label: '厂家',
          prop: 'icmManufacturer'
        },
        {
          label: '类型',
          prop: 'icmType'
        },
        {
          label: '用户名',
          prop: 'icmUserName'
        },
        {
          label: '密码',
          prop: 'icmPassword'
        },
        {
          prop: '',
          label: '操作',
          render: (h, scope) => {
            return (
              <div class="operationBtn">
                <span class="operationBtn-span" onClick={() => this.operation('edit', scope.row)}>
                  修改
                </span>
                <span class="operationBtn-span" onClick={() => this.delData(scope.row)}>
                  删除
                </span>
              </div>
            )
          }
        }
      ],
      parameter: {
        icmName: '', // 摄像机名称
        icmManufacturer: '', // 摄像机厂家
        icmType: '' // 摄像机类型
      },
      icmManufacturer: [
        {
          label: '宇视',
          value: '宇视'
        },
        {
          label: '海康',
          value: '海康'
        },
        {
          label: '大华',
          value: '大华'
        },
        {
          label: '华为',
          value: '华为'
        },
        {
          label: '其他',
          value: '其他'
        }
      ],
      icmType: [
        {
          label: '数字',
          value: '数字'
        },
        {
          label: '模拟',
          value: '模拟'
        }
      ],
      checkedData: {},
      tableHeight: '',
      value: ''
    }
  },
  mounted() {
    this.getStaffListByPage()
  },
  methods: {
    // --------------新增，修改
    operation(type, row) {
      this.ifType = type
      if (type == 'edit') {
        this.checkedData = row
      } else {
        this.checkedData = {}
      }
      this.dialogVisible = true
    },
    // --------------删除
    delData(row) {
      this.$confirm('确定删除?', '消息', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api
          .deleteCameraById({
            id: row.icmId
          }, { 'operation-type': 3, 'operation-id': row.icmId, 'operation-name': row.icmName })
          .then((res) => {
            if (res.code == 200) {
              this.$message.success(res.message)
              this.getStaffListByPage()
            }
          })
      })
    },
    // 重置
    resetData() {
      this.parameter = {
        icmName: '', // 摄像机名称
        icmManufacturer: '', // 摄像机厂家
        icmType: '' // 摄像机类型
      }
      this.getStaffListByPage()
    },
    // 查询
    searchClick() {
      this.paginationData.page = 1
      this.getStaffListByPage()
    },
    // 分页查询摄像机数据
    getStaffListByPage() {
      let data = {
        currentPage: this.paginationData.page,
        pageSize: this.paginationData.pageSize,
        startPage: this.paginationData.page,
        icmName: this.parameter.icmName,
        icmManufacturer: this.parameter.icmManufacturer,
        icmType: this.parameter.icmType
      }
      this.$api.cameraManageByPage(data).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.list
          this.paginationData.total = res.data.count
        }
      })
    },
    // 分页事件
    paginationChange(pagination) {
      Object.assign(this.paginationData, pagination)
      this.getStaffListByPage()
    },
    sure() {
      this.dialogVisible = false
      this.getStaffListByPage()
    }
  }
}
</script>
<style lang="scss" scoped>
.camera-management {
  height: 100%;
  display: flex;
  flex-direction: column;

  .top_filters {
    padding: 0 200px 10px 10px !important;
    background: #fff;
    border-radius: 4px;

    ::v-deep .el-input {
      width: 200px;
    }

    & > div {
      margin-right: 10px;
      margin-top: 10px;
    }
  }

  .right-content {
    margin-top: 16px;
    background: #fff;
    padding: 10px;
    border-radius: 4px;
    flex: 1;
    overflow: hidden;

    .table-content {
      height: calc(100% - 0px);

      ::v-deep .el-table .el-table__body-wrapper td.el-table__cell {
        border-right: 1px solid #ededf5;
      }
    }
  }
}
</style>
