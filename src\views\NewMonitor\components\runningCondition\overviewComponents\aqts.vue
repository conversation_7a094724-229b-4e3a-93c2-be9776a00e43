<!-- 运行监测 -->
<template>
  <ContentCard :title="item.componentTitleShow" :scrollbarHover="true" :cstyle="{ width: '100%', height: '100%' }"
    class="drag_class" :hasMoreOper="['edit']"
    @more-oper-event="(val) => $emit('all-more-Oper', val, item.componentName, item.chartType)">
    <div slot="content" style="width: 100%; height: 100%; display: flex">
      <echarts :ref="`topFiveNoTimeSelect${item.componentDataType}`"
        :domId="`topFiveNoTimeSelect${item.componentDataType}`" width="100%" height="100%" />

      <!-- <div style="width: 100%; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center; padding: 50px">
        <img src="@/assets/images/newMonitor/no-chat.png" />
        <span>暂无数据</span>
      </div> -->
    </div>
  </ContentCard>
</template>

<script>
import moment from 'moment'
import * as echarts from 'echarts'
export default {
  name: 'aqts',
  props: {
    item: {
      type: Object,
      default: () => { }
    },
    systemCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {}
  },
  mounted() {
    setTimeout(() => {
      this.getTopTenData()
    }, 150)
  },
  methods: {
    getTopTenData(id) {
      const payload = {
        projectCode: this.systemCode,
        timeType: '2',
        alarmType: this.item.alarmTypeId,
      }
      let groupId = id || ""
      this.$api.getTopRunTimeData(groupId, payload).then((res) => {
        if (res.code === "200") {
          this.appendEchartsData(res.data)
        } else {
          this.hasChart = false
        }
      })
    },
    appendEchartsData(data) {
      const baseData = {
        tooltip: {
          trigger: 'item',
          formatter: '{b} : {c}',
          confine: true
        },
        radar: {
          indicator: [
            { name: '电机', max: 6500 },
            { name: '制动系统', max: 16000 },
            { name: '安全回路', max: 30000 },
            { name: '轿厢', max: 38000 },
            { name: '限速器', max: 52000 },
          ]
        },
        series: [
          {
            name: 'Budget vs spending',
            type: 'radar',
            data: [
              {
                value: [100],
                name: '电机'
              },
              {
                value: [100],
                name: '制动系统'
              },
              {
                value: [100],
                name: '安全回路'
              },
              {
                value: [100],
                name: '轿厢'
              },
              {
                value: [100],
                name: '限速器'
              },
            ]
          }
        ]
      }
      // if (data && data.length > 0) {
      //   data.forEach((item) => {
      //     baseData.yAxis.data.push(item.alarmObjectName)
      //     baseData.series[0].data.push(item.count)
      //   })
      // }
      this.$refs[`topFiveNoTimeSelect${this.item.componentDataType}`].init(baseData)
    }
  }
}
</script>

<style lang="scss" scoped>
.data-btns {
  position: absolute;
  right: 7%;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &>span {
    width: 44px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    margin: 0 4px;
    background-color: #f6f5fa;
    font-size: 14px;
    font-family: 'PingFang SC-Medium', 'PingFang SC';
    border: none;
    border-radius: 2px;
    color: #7f848c;
    cursor: pointer;
  }

  .active-btn {
    background-color: #e6effc !important;
    color: #3562db !important;
    border-color: #e6effc !important;
  }
}

.operation-list {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
}
</style>
