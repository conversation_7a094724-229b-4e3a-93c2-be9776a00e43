<template>
    <PageContainer>
        <div slot="header">
            <div class="searchForm">
                <div class="search-box">
                    <div class="search-from">
                        <el-date-picker v-model="dateRange" type="daterange" range-separator="至" @change="timeData"
                            start-placeholder="开始月份" end-placeholder="结束月份" :clearable="false"
                            value-format="yyyy-MM-dd">
                        </el-date-picker>
                        <el-select v-model="searchForm.robotIds" placeholder="请选择设备" clearable>
                            <el-option v-for="item in listData" :key="item.id" :label="item.name" :value="item.id">
                            </el-option>
                        </el-select>
                        <el-select v-model="searchForm.jobType" placeholder="请选择任务类型">
                            <el-option label="全部" value="0" />
                            <el-option label="配送任务" value="10" />
                            <el-option label="呼叫任务" value="20" />
                            <el-option label="返程任务" value="30" />
                            <el-option label="等待任务" value="40" />
                            <el-option label="充电任务" value="50" />
                            <el-option label="电梯等待区域任务" value="60" />
                            <el-option label="管制等待区域任务" value="70" />
                            <el-option label="定时消毒任务" value="80" />
                            <el-option label="到跨楼宇等待位置任务" value="90" />
                            <el-option label="即时消毒任务" value="100" />
                            <el-option label="查房任务" value="110" />
                            <el-option label="退药任务" value="120" />
                            <el-option label="补位任务" value="130" />
                            <el-option label="补货任务" value="140" />
                            <el-option label="送餐任务" value="150" />
                            <el-option label="回收任务" value="160" />
                            <el-option label="宣教任务" value="170" />
                        </el-select>
                        <el-select v-model="searchForm.jobState" placeholder="请选择任务状态">
                            <el-option label="全部" value="0" />
                            <el-option label="进行中" value="1" />
                            <el-option label="已结束" value="2" />
                        </el-select>
                        <div style="display: inline-block;">
                            <el-button type="primary" @click="search">查询</el-button>
                            <el-button type="primary" plain @click="reset">重置</el-button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div slot="content" ref="contentRef" class="table-box">
            <div class="tableContainer">
                <el-table :data="tableData" style="width: 100%;margin-bottom: 20px;" border height="250">
                    <el-table-column prop="robotName" label="机器人名称" show-overflow-tooltip>
                    </el-table-column>
                    <el-table-column prop="robotModel" label="机器人类型" show-overflow-tooltip>
                    </el-table-column>
                    <el-table-column prop="startPosition" label="始发地" show-overflow-tooltip>
                    </el-table-column>
                    <el-table-column prop="endPosition" label="目的地" show-overflow-tooltip>
                    </el-table-column>
                    <el-table-column prop="startTime" label="开始时间" show-overflow-tooltip>
                        <template slot-scope="scope">
                            <span>
                                {{ scope.row.startTime.split(' ')[0] }} {{ scope.row.startTime.split(' ')[1]
                                }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="acceptState" label="物品接收状态" show-overflow-tooltip>
                        <template slot-scope="scope">
                            <span>
                                {{ getJobAcceptStatus(scope.row.acceptState) }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="jobType" label="任务类型" show-overflow-tooltip>
                        <template slot-scope="scope">
                            <span>
                                {{ getJobType(scope.row.jobType) }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="jobState" label="任务状态" show-overflow-tooltip>
                        <template slot-scope="scope">
                            <span>
                                {{ getJobState(scope.row.jobState) }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="userName" label="发起人" show-overflow-tooltip>
                    </el-table-column>
                    <el-table-column label="操作" width="80">
                        <template slot-scope="scope">
                            <el-button type="text" @click="handleListEvent('look', scope.row)">查看</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination :current-page="pagination.pageNo" :page-sizes="[15, 30, 50, 100]"
                    :page-size="pagination.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="pageTotal"
                    @size-change="handleSizeChange" @current-change="handleCurrentChange">
                </el-pagination>
            </div>

        </div>
    </PageContainer>
</template>
<script>
export default {
    name: 'emergencyTeam',
    data() {
        return {
            systemType: [],
            searchForm: {
                robotIds: '',//设备id
                jobType: '',//任务类型
                jobState: '',//	0:全部 1:进行中 2:已结束
                startTime: '',
                endTime: '',
            },
            dateRange: null,
            pagination: {
                pageSize: 15,
                pageNo: 1
            },
            tableData: [],
            title: '',
            pageTotal: 0,
            deviceId: "",
            fadeShow: true, // 淡入淡出
            listData: [],
        }
    },
    mounted() {
        this.getDataList()
        this.getFactoryCodeByAgv()
    },
    methods: {
        getJobState(state) {
            switch (state) {
                case 0:
                    return '全部';
                case 1:
                    return '任务队列中';
                case 2:
                    return '任务下发中';
                case 3:
                    return '任务执行中';
                case 4:
                    return '任务到达';
                case 5:
                    return '任务完成';
                case 6:
                    return '取消任务';
                case 7:
                    return '电量低终止';
                case 8:
                    return '无消毒液终止';
                case 9:
                    return '新任务终止';
                case 10:
                    return '系统设置结束';
                case 11:
                    return '资源不存在导致取消任务';
                case 12:
                    return '任务过期取消任务';
                case 13:
                    return '任务创建失败';
                case 14:
                    return 'APP异常';
                case 15:
                    return 'App放餐失败';
                case 16:
                    return 'APP上报的任务执行超时';
                case 17:
                    return '抽水传感器异常终止';
                default:
                    return '';
            }
        },
        getJobAcceptStatus(state) {
            switch (state) {
                case 1:
                    return '待接收';
                case 2:
                    return '已接收';
                case 3:
                    return '超时未取';
                case 4:
                    return '有退药';
                case 5:
                    return '超时未配送';
                case 6:
                    return '暂不配送';
                default:
                    return '';
            }
        },
        getJobType(state) {
            switch (state) {
                case 0:
                    return '全部';
                case 10:
                    return '配送任务';
                case 20:
                    return '呼叫任务';
                case 30:
                    return '返程任务';
                case 40:
                    return '等待任务';
                case 50:
                    return '充电任务';
                case 60:
                    return '电梯等待区域任务';
                case 70:
                    return '管制等待区域任务';
                case 80:
                    return '定时消毒任务';
                case 90:
                    return '到跨楼宇等待位置任务';
                case 100:
                    return '即时消毒任务';
                case 110:
                    return '查房任务';
                case 120:
                    return '退药任务';
                case 130:
                    return '补位任务';
                case 140:
                    return '补货任务';
                case 150:
                    return '送餐任务';
                case 160:
                    return '回收任务';
                case 170:
                    return '宣教任务';
                default:
                    return '';
            }
        },
        timeData(val) {
            this.searchForm.startTime = val[0] + ' 00:00:00'
            this.searchForm.endTime = val[1] + ' 23:59:59'
        },
        getDataList() {
            let params = {
                terms: [
                    {
                        column: "systemTypeCode",
                        value: 'AGVJQR'
                    },
                    {
                        column: "deviceTypeCode",
                        value: 'WLJQR'
                    },
                ]
            }
            this.listData = []
            this.$api
                .getOperationalMonitoringQuery(params)
                .then((res) => {
                    if (res.status === '200' || res.status === 200) {
                        this.listData = res.result || []
                    }
                })
                .catch(() => {
                })
        },
        getFactoryCodeByAgv() {
            this.$api.getFactoryCodeByAgv().then((res) => {
                if (res.code == '200') {
                    this.deviceId = res.data
                    this.getTableData()
                }
            })
        },
        // 品类table列表
        getTableData() {
            this.tableLoading = true
            let data = {
                ...this.searchForm,
                ...this.pagination
            }
            this.$api.getQueryInstanceFunction(this.deviceId, 'jobs', data).then((res) => {
                if (res.status == 200) {
                    // 提取每个对象的jobs中的第一条记录
                    this.tableData = res.result[0].items.map(item => {
                        return {
                            ...item, // 保留原有的item属性
                            ...item.jobs[0] // 添加第一条job
                        };
                    });
                    this.pageTotal = res.result[0].count
                } else {
                    this.$message.error(res.message)
                }
            })
                .catch((err) => {
                    this.tableLoading = false
                })
        },
        handleSizeChange(val) {
            this.pagination.pageSize = val
            this.pagination.pageNo = 1
            this.getTableData()
        },
        search() {
            this.pagination.pageNo = 1
            this.getTableData()
        },
        reset() {
            this.searchForm = {
                robotIds: '',//设备id
                jobType: '',//任务类型
                jobState: '',//	0:全部 1:进行中 2:已结束
                startTime: '',
                endTime: '',
            }
            this.dateRange = null
            this.pagination.pageNo = 1
            this.pageTotal = 0
            this.getTableData()
        },
        handleCurrentChange(val) {
            this.pagination.pageNo = val
            this.getTableData()
        },
        handleListEvent(type, row, num) {
            if (type == 'look') {
                localStorage.setItem('jobsData', JSON.stringify(row));
                this.$router.push(`${this.$route.meta.jumpAddress}/agvStatisticAnalysisDetails`)
            }
        },
        handleClick() {
            this.personDialogShow = true
        },
    }
}
</script>
<style lang="scss" scoped>
.searchForm {
    display: flex;
    padding: 0 16px;
    margin-top: 4px;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;

    .search-box {
        flex: 1;
        display: flex;
        flex-direction: column;
        margin-bottom: 20px;
        background: #fff;
        overflow: hidden;
    }

    .mr-6 {
        margin-right: 6px;
    }

    .search-from {

        .fadeBtn {
            cursor: pointer;
        }

        &>div {
            margin-top: 16px;
            margin-right: 16px;
        }
    }

    .el-input {
        width: 200px;
        margin-left: 16px;
    }

    >div {
        margin-right: 20px;
    }
}

.container-content>div {
    height: 100%;
    margin-top: 16px;
}

.el-table {
    margin-bottom: 12px;

}

::v-deep .el-table__row:hover {
    cursor: pointer;
}

.table-box {
    background-color: #fff;
    height: 100%;
    padding: 16px;

    .tableContainer {
        height: 100%;

        .el-table {
            height: calc(100% - 66px) !important;
        }
    }
}

.diaContent {
    width: 100%;
    max-height: 550px !important;
    overflow: auto;
    background-color: #fff !important;

    .el-input,
    .el-select,
    .el-cascader {
        width: 300px;
    }
}

.form-item {
    display: inline-block;
    margin-right: 20px;
}

.inputWidth {
    width: 820px;
}

.ml-16 {
    margin-left: 16px;
}

.dialog .el-dialog {
    width: 60% !important;
}
</style>
