<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="content-title" @click="$router.go(-1)"><i class="el-icon-arrow-left"></i>
        入库单</div>
      <div class="content_box">
        <div class="content-table" id="printDiv" style="width:800px">
          <div class="content_step" style="margin-top:30px">
            <el-steps :active="stepActive" finish-status="success" class="steps" align-center>
              <el-step title="暂存"></el-step>
              <el-step title="已入库"></el-step>
            </el-steps>
          </div>
          <h2 class="sino-detail-title" style=" letter-spacing: 12px;text-align: center">入库单</h2>
          <div class="detailInfo" style="display: flex;flex-wrap: wrap;">
            <div v-for="(item,index) in inWarehouseList" :key="index" class="list" style="width:calc(100%/3)">
              <div class="item" style="display: flex">
                <div class="item-label">{{item.label}}</div>
                <div class="item-value">{{detailInfo[item.value]}}</div>
              </div>
            </div>
          </div>
          <div class="aggregateInfo" style="display:flex">
            <div>
              <span>合计数量:</span>
              <span>{{detailInfo.inwarehouseCount||0}}个</span>
            </div>
            <!-- <div style="margin-left:20px">
              <span>合计金额:</span>
              <span>{{detailInfo.amount||0}}元</span>
            </div> -->
          </div>
          <el-table v-loading="tableLoading" :data="tableData" row-key="id" stripe border :header-cell-style="rowClass"
            style="width:100%;margin-top:10px" :cell-style="cellStyle">
            <el-table-column label="序号" type="index" width="100"></el-table-column>
            <el-table-column prop="materialName" label="危化品名称" show-overflow-tooltip></el-table-column>
            <el-table-column prop="model" label="规格型号" show-overflow-tooltip></el-table-column>
            <el-table-column prop="basicUnitName" label="单位" show-overflow-tooltip></el-table-column>
            <el-table-column prop="operateCount" label="数量" show-overflow-tooltip></el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">返回</el-button>
      <el-button type="primary" @click="print">打印</el-button>
    </div>
  </PageContainer>
</template>
<script>
export default {
  name: 'warehousingEntryDetail',
  data() {
    return {
      tableLoading: false,
      tableData: [],
      inWarehouseList: [
        {
          label: "计划单号：",
          value: "recordNumber"
        },
        {
          label: "创建人：",
          value: "createName"
        },
        {
          label: "创建时间：",
          value: "createTime"
        },
        {
          label: "入库类型：",
          value: "inwarehouseName"
        },
        {
          label: "总数量：",
          value: "inwarehouseCount"
        },
        // {
        //   label: "总金额：",
        //   value: "amount"
        // },
        {
          label: "备注：",
          value: "remarks",
        }
      ],
      detailInfo: {
        recordNumber: '',
        createName: '',
        createTime: '',
        inwarehouseName: '',
        inwarehouseCount: '',
        amount: '',
        remarks: '',
      },
      stepActive: 0,//状态
    }
  },

  mounted() {
    this.getInWarehouseData()
  },
  methods: {
    //获取入库单详情
    getInWarehouseData() {
      this.tableLoading = true
      const userInfo = this.$store.state.user.userInfo.user
      let params = {
        id: '',
        recordNumber: this.$route.query.inWarehouseId,
        userId: userInfo.staffId,
        userName: userInfo.staffName,
      }
      this.$api.getInwarehouseRecordById(params).then((res) => {
        this.tableLoading = false
        if (res.code == '200') {
          this.detailInfo = res.data
          this.stepActive = res.data.status === '暂存' ? 1 : 2
          this.tableData = res.data.materialRecordList
        }
      })
    },
    rowClass() {
      return "color:#000;padding:0";
    },
    cellStyle() {
      return "color:#000;padding:0";
    },
    //打印
    print() {
      let subOutputRankPrint = document.getElementById("printDiv");
      let newContent = subOutputRankPrint.innerHTML;
      let oldContent = document.body.innerHTML;
      document.body.innerHTML = newContent;
      window.print();
      window.location.reload();
      document.body.innerHTML = oldContent;
      return false;
    },
  }
}
</script>
<style scoped lang="scss">
.container-content > div {
  background-color: #fff;
  height: 100%;
  position: relative;
  .content-title {
    border-bottom: 1px solid #e4e7ed;
    padding: 12px 24px;
    cursor: pointer;
    margin-bottom: 24px;
  }
  .content_box {
    padding: 0 24px;
    background: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow-y: auto;
    height: calc(100% - 90px);
    .content-table {
      h2 {
        letter-spacing: 12px;
        text-align: center;
      }
      .detailInfo {
        margin: auto;
        display: flex;
        flex-wrap: wrap;
        .list {
          display: inline-block;
          width: 33.3333%;
          line-height: 25px;
          color: #000;
          .item {
            display: flex;
            align-items: center;
            .item-label {
              font-weight: bold;
              color: #000;
            }
          }
        }
      }
      .aggregateInfo {
        display: flex;
        margin-top: 10px;
        color: #333 !important;
        font-weight: bold !important;
        div:nth-child(2) {
          margin-left: 20px;
        }
      }
    }
  }
}
::v-deep .el-table__header {
  width: 100% !important;
}
::v-deep .el-table .el-table__header .el-table__cell {
  color: #333 !important;
  font-weight: bold !important;
  border-color: #000 !important;
}
::v-deep .el-table__body {
  width: 100% !important;
}
::v-deep .el-table {
  border-color: #000 !important;
  border-bottom: 1px solid #000;
  border-right: 1px solid #000;
  .el-table__header {
    .el-table__cell {
      border-bottom: 1px solid #000;
      border-right: 1px solid #000;
      background-color: #ffffff !important;
    }
  }
  .el-table__row {
    .el-table__cell {
      border-bottom: 1px solid #000;
      border-right: 1px solid #000;
      padding: 0;
    }
  }
}
</style>
<style lang="less" scoped>
@publicColor: #3562db;
.steps {
  width: 80%;
  margin: 20px auto 0;
  ::v-deep .el-step {
    height: 100%;
    .el-step__line {
      background-color: rgba(0, 0, 0, 0.15);
      margin-right: 30px !important;
      margin-left: 35px !important;
      top: 50%;
      height: 1px;
    }
    .el-step__icon {
      font-size: 16px;
      border: 1px solid;
      .el-step__icon-inner {
        font-weight: unset !important;
      }
    }
    .el-step__head.is-process {
      color: @publicColor;
      border-color: @publicColor;
    }
    .el-step__head.is-success {
      color: @publicColor;
      border-color: @publicColor;
    }
    .is-success .el-step__icon.is-text {
      background: #e6effc;
      border-color: #e6effc;
    }
    .is-process .el-step__icon.is-text {
      background: @publicColor;
      color: #fff;
    }
    .is-wait .el-step__icon {
      background: #ededf5 !important;
      font-weight: 600;
      font-size: 14px;
      border-color: #ededf5;
      color: #666666;
    }
    .el-step__title.is-process {
      color: @publicColor;
    }
    .el-step__title.is-success {
      color: #565656;
    }
  }
}
</style>
