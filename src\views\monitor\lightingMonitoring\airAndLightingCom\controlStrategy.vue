<template>
  <PageContainer title="控制策略管理" type="list" :isClose="false" style="background-color: rgb(245 246 251); overflow: hidden">
    <template slot="content">
      <div class="controlStrategy">
        <div class="top">
          <div v-for="(item, index) in patternList" :key="index" class="pattern_box" :class="selectId == item.id ? 'active_pattern' : ''" @click="changePattern(item)">
            <el-tooltip v-if="item.patternType != 0" placement="bottom" popper-class="pattern_popper">
              <div slot="content" class="popper_icon_box">
                <i class="el-icon-edit" @click="pattertEvent('edit', item)"></i>
                <i class="el-icon-delete" @click="pattertEvent('del', item)"></i>
              </div>
              <div>
                <img style="width: 30px; height: 30px" :src="selectId == item.id ? item.patternActiveSrc : item.patternSrc" alt="" />
                <span>{{ item.patternName }}</span>
              </div>
            </el-tooltip>
            <div v-else>
              <img style="width: 30px; height: 30px" :src="selectId == item.id ? item.patternActiveSrc : item.patternSrc" alt="" />
              <span>{{ item.patternName }}</span>
            </div>
          </div>
          <i class="el-icon-circle-plus" style="color: #5188fc; font-size: 22px; line-height: 50px; margin-left: 10px; cursor: pointer" @click="pattertEvent('add', null)"></i>
        </div>
        <div class="content">
          <div class="content-left">
            <div class="toptip">
              <span class="green_line"></span>
              场景配置
              <div class="title_btn_icon">
                <i title="新增" class="el-icon-plus" @click="sceneEvent('add', null)"></i>
              </div>
            </div>
            <div class="left_content">
              <div v-for="(item, index) in sceneList" :key="index" class="scene_list">
                <div class="scene_list_name">
                  <span>{{ item.timeTableName }}</span>
                  <span>
                    <i class="el-icon-edit" @click="sceneEvent('edit', item)"></i>
                    <i class="el-icon-delete" @click="sceneEvent('del', item)"></i>
                  </span>
                </div>
                <div v-if="type == '1'" class="scene_list_time">
                  <div v-for="(table, idx) in item.timeTableDataList" :key="idx" style="display: flex">
                    {{ table.timePoint }}
                    <div style="display: flex; flex-direction: column">
                      <span v-for="listItem in table.list" :key="listItem.paramId" style="display: flex">
                        <span v-if="listItem.type == 6">{{ listItem.paramName }}: {{ listItem.value == 1 ? '开启' : '关闭' }}</span>
                        <span v-else>{{ listItem.paramName }}:{{ listItem.value }}</span>
                      </span>
                    </div>
                  </div>
                </div>
                <div v-else class="scene_list_time">
                  <div v-for="(table, idx) in item.timeTableDataList" :key="idx">{{ table.timePoint }}（{{ table.switchControl == 1 ? '开启' : '关闭' }}）</div>
                </div>
              </div>
            </div>
          </div>
          <div class="content-right">
            <div class="content-top" style="margin: 12px">
              <el-select v-model="searchForm.timeTableId" class="mr15" style="width: 15%" clearable placeholder="场景选择">
                <el-option v-for="item in sceneList" :key="item.id" :label="item.timeTableName" :value="item.id"></el-option>
              </el-select>
              <el-input v-model="searchForm.dictName" placeholder="分组名称" style="width: 15%; margin-right: 15px"></el-input>
              <el-button type="primary" plain @click="reset()">重置</el-button>
              <el-button type="primary" @click="search()">查询</el-button>
            </div>
            <div class="table-content">
              <el-table v-loading="tableLoading" :data="tableData" border height="calc(100% - 50px)">
                <el-table-column v-for="(item, index) in tableTitle" :key="index" :prop="item.value" :label="item.label" show-overflow-tooltip :width="item.width">
                  <template slot-scope="scope">
                    <el-select
                      v-if="scope.column.property == 'timeTableId'"
                      class="no-border"
                      placeholder="请选择"
                      :value="scope.row.timeTableId"
                      @change="changeSceneTable($event, scope.row)"
                    >
                      <el-option v-for="item in sceneList" :key="item.id" :label="item.timeTableName" :value="item.id"></el-option>
                    </el-select>
                    <span v-else>{{ scope.row[scope.column.property] }}</span>
                  </template>
                </el-table-column>
              </el-table>
              <div class="table-page">
                <el-pagination
                  class="pagination"
                  :current-page="paginationData.currentPage"
                  :page-sizes="[15, 30, 50, 100]"
                  :page-size="paginationData.pageSize"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="paginationData.total"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                ></el-pagination>
              </div>
            </div>
          </div>
        </div>
      </div>
      <template v-if="sceneDialogShow && type == '1'">
        <isAirSceneDialog
          :projectCode="projectCode"
          :sceneDialogShow="sceneDialogShow"
          :sceneDialogType="sceneDialogType"
          :dialogData="sceneDialogData"
          :patternId="selectId"
          @closeSceneDialog="closeSceneDialog"
          @sceneSubmit="sceneSubmit"
        ></isAirSceneDialog>
      </template>
      <template v-if="sceneDialogShow && type != '1'">
        <sceneDialog
          :projectCode="projectCode"
          :sceneDialogShow="sceneDialogShow"
          :sceneDialogType="sceneDialogType"
          :dialogData="sceneDialogData"
          :patternId="selectId"
          @closeSceneDialog="closeSceneDialog"
          @sceneSubmit="sceneSubmit"
        ></sceneDialog>
      </template>
      <template v-if="patternDialogShow">
        <patternDialog
          :projectCode="projectCode"
          :patternDialogShow="patternDialogShow"
          :patternDialogType="patternDialogType"
          :dialogData="patternDialogData"
          @closePatternDialog="closePatternDialog"
          @patternSubmit="patternSubmit"
        ></patternDialog>
      </template>
    </template>
  </PageContainer>
</template>
<script>
import sceneDialog from '../components/sceneDialog.vue'
import isAirSceneDialog from '../components/isAirSceneDialog.vue'
import patternDialog from '../components/patternDialog.vue'
import Dict from '../components/dict.js'
export default {
  components: {
    isAirSceneDialog,
    sceneDialog,
    patternDialog
  },
  props: {
    type: {
      // 1 为空调
      type: String,
      default: ''
    },
    projectCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      sceneDialogType: 'add', // 场景弹窗类型
      sceneDialogShow: false, // 场景弹窗显示
      sceneDialogData: {}, // 场景弹窗数据
      patternDialogType: 'add', // 模式弹窗类型
      patternDialogShow: false, // 模式弹窗显示
      patternDialogData: {}, // 模式弹窗数据
      selectId: '', // 选中 模式的type
      patternTypeIconList: Dict.patternTypeIconList,
      patternList: [], // 模式列表
      sceneList: [],
      areaOption: [], // 场景选择
      searchForm: {
        timeTableId: '',
        dictName: ''
      },
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      tableTitle: [
        {
          label: '分组名称',
          value: 'dictName'
        },
        {
          label: '回路数量',
          value: 'count'
        },
        {
          label: '回路名称',
          value: 'circuitName'
        },
        {
          label: '场景',
          value: 'timeTableId'
        },
        {
          label: '时刻表预览',
          value: 'timeTable'
        }
      ],
      tableData: [
        {
          sensorName: '分组名称',
          areaType: '回路数量',
          constructName: '回路名称',
          floorName: '场景',
          spaceName: '时刻表预览'
        }
      ],
      tableLoading: false
    }
  },
  created() {},
  mounted() {
    this.getWeatherPatternList()
  },
  methods: {
    // 获取初始化模式列表
    getWeatherPatternList() {
      this.$api.selectWeatherPatternAllList({ projectCode: this.projectCode }).then((res) => {
        if (res.code == 200) {
          if (res.data.length) {
            res.data.map((e) => {
              const patternData = this.patternTypeIconList.find((item) => item.patternCode == e.patternType)
              e.patternSrc = patternData?.patternSrc
              e.patternActiveSrc = patternData?.patternActiveSrc
            })
            this.patternList = res.data
            this.selectId = res.data[0].id
            this.getSceneListByPatternId(res.data[0].id)
            this.getTableData()
          } else {
            this.patternList = []
          }
        }
      })
    },
    // 获取场景列表
    getSceneListByPatternId(id) {
      this.$api
        .selectTimeTableByPatternId({
          projectCode: this.projectCode,
          patternId: id
        })
        .then((res) => {
          if (res.code == 200) {
            if (res.data.length) {
              this.sceneList = res.data
            } else {
              this.sceneList = []
            }
          }
        })
    },
    // 切换模式
    changePattern(item) {
      this.selectId = item.id
      this.getSceneListByPatternId(this.selectId)
      this.getTableData()
    },
    // 获取列表
    getTableData() {
      let data = {
        ...this.searchForm,
        projectCode: this.projectCode,
        patternId: this.selectId,
        pageSize: this.paginationData.pageSize,
        page: this.paginationData.currentPage
      }
      this.tableLoading = true
      this.$api.getLightingGroupByPattern(data).then((res) => {
        this.tableLoading = false
        if (res.code == 200) {
          this.tableData = res.data.list
          this.paginationData.total = parseInt(res.data.count)
        }
      })
    },
    handleSizeChange(val) {
      this.paginationData.currentPage = 1
      this.paginationData.pageSize = val
      this.search()
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.search()
    },
    changeSceneTable(val, row) {
      const params = {
        id: row.id,
        patternId: this.selectId,
        projectCode: this.projectCode
      }
      if (row.timeTableId) {
        Object.assign(params, {
          newTimeTableId: val,
          timeTableId: row.timeTableId
        })
      } else {
        Object.assign(params, {
          timeTableId: val
        })
      }
      this.$api.associatedScene(params).then((res) => {
        if (res.code == 200) {
          this.$message.success('关联成功')
          this.search()
        }
      })
    },
    reset() {
      this.paginationData.currentPage = 1
      Object.assign(this.searchForm, {
        timeTableId: '',
        dictName: ''
      })
      this.search()
    },
    search() {
      this.getTableData()
    },
    sceneEvent(type, selectRow) {
      if (type !== 'del') {
        this.sceneDialogType = type
        this.sceneDialogData = selectRow
        this.sceneDialogShow = true
      } else {
        this.$confirm('删除后，相关数据将自动解绑，确认删除？', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$api
            .delTimeTableById(
              {
                projectCode: this.projectCode,
                id: selectRow.id
              },
              {
                'operation-type': 3,
                'operation-id': selectRow.id,
                'operation-name': selectRow.timeTableName
              }
            )
            .then((res) => {
              if (res.code == 200) {
                this.getSceneListByPatternId(this.selectId)
                this.$message({
                  message: '删除成功',
                  type: 'success'
                })
              }
            })
        })
      }
    },
    // 模式 新增修改删除事件
    pattertEvent(type, selectRow) {
      if (type != 'del') {
        this.patternDialogType = type
        this.patternDialogData = selectRow
        this.patternDialogShow = true
      } else {
        this.$confirm('删除后，相关数据将自动解绑，确认删除？', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$api
            .delWeatherPatternById(
              {
                projectCode: this.projectCode,
                id: selectRow.id
              },
              {
                'operation-type': 3,
                'operation-id': selectRow.id,
                'operation-name': selectRow.timeTableName
              }
            )
            .then((res) => {
              if (res.code == 200) {
                this.getWeatherPatternList()
                this.$message({
                  message: '删除成功',
                  type: 'success'
                })
              }
            })
        })
      }
    },
    closeSceneDialog() {
      this.sceneDialogShow = false
    },
    sceneSubmit(data) {
      this.sceneDialogShow = false
      this.getSceneListByPatternId(this.selectId)
      this.getTableData()
    },
    closePatternDialog() {
      this.patternDialogShow = false
    },
    patternSubmit(data) {
      this.patternDialogShow = false
      this.getWeatherPatternList()
    }
  }
}
</script>

<style src="@/assets/styles/sinomis-ui.css" scoped></style>
<style lang="scss" scoped>
.controlStrategy {
  width: 100%;
  height: 100%;

  .top {
    display: flex;
    border-radius: 10px;
    height: 48px;
    background-color: #fff;

    .pattern_box {
      box-sizing: border-box;
      height: 38px;
      line-height: 38px;
      padding: 0 30px;
      border-radius: 24px;
      margin: auto 0;
      margin-left: 30px;
      cursor: pointer;

      img {
        vertical-align: middle;
        margin-right: 10px;
      }

      span {
        font-size: 14px;
        font-family: 'HarmonyOS_Sans_SC';
        color: #333;
      }
    }

    .active_pattern {
      background: #fffae8;
      border: 1px solid #ffe8ab;
    }
  }

  .content {
    height: calc(100% - 66px);
    margin-top: 10px;
    display: flex;

    .content-left {
      width: calc(18% - 10px);
      height: 100%;
      background-color: #fff;
      border-radius: 10px;
      display: block;

      .title_btn_icon {
        float: right;
        cursor: pointer;
        margin-right: 20px;
        color: #5188fc;

        i {
          font-size: 18px;
        }
      }

      .left_content {
        height: calc(100% - 50px) !important;
        padding: 0;

        .scene_list {
          .scene_list_name {
            width: 100%;
            height: 38px;
            line-height: 38px;
            background: #f5f6fb;
            display: flex;
            justify-content: space-between;
            padding: 0 20px;

            span {
              font-size: 15px;
              color: #5188fc;
            }

            i {
              line-height: 38px;
              font-size: 22px;
              margin-left: 20px;
              color: #848da1;
              cursor: pointer;
            }
          }

          .scene_list_time {
            width: 100%;
            padding: 6px 20px;

            div {
              // height: 25px;
              line-height: 25px;
              padding-left: 10px;
              font-size: 14px;
              color: #606266;
            }
          }
        }
      }
    }

    .content-right {
      width: 82%;
      margin-left: 10px;
      background-color: #fff;
      border-radius: 10px;
      display: flex;
      flex-direction: column;

      .table-content {
        flex: 1;
        padding: 3px 10px;
        box-sizing: border-box;
      }
    }
  }
}

.pattern_popper {
  width: 99px;
  height: 30px;
  background: #7b7b7d !important;
  border-radius: 15px !important;
  padding: 0 6px !important;

  .popper__arrow {
    display: none !important;
  }

  .popper_icon_box {
    display: flex;
    justify-content: space-evenly;

    i {
      width: 28px;
      height: 28px;
      line-height: 28px !important;
      border-radius: 50%;
      font-size: 18px;
      text-align: center;
      cursor: pointer;

      &:hover {
        background: #979799;
      }
    }
  }
}
</style>
