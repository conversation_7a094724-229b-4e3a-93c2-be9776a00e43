<template>
  <div style="width: 100%; height: 100%">
    <div style="height: 100%">
      <div class="content">
        <!-- <el-row :gutter="16"> -->
        <!-- <el-col :xs="24" :md="24" :lg="12" style="padding: 0 5px 10px 9px;">
            <div class="col" style="height: 100%;">
              <div class="title">
                <svg-icon name="right-arrow" />
                <span>医废情况分析</span>
              </div>
              <div id="workOdertTypeEcharts"></div>
              <div class="btns">
                <span :class="{ 'active-btn': completionRateType == 'day' }" @click="changeCompletionRateType('day')">今日</span>
                <span :class="{ 'active-btn': completionRateType == 'month' }" @click="changeCompletionRateType('month')">本月</span>
                <span :class="{ 'active-btn': completionRateType == 'year' }" @click="changeCompletionRateType('year')">本年</span>
              </div>
            </div>
          </el-col> -->
        <!-- <el-col :xs="24" :md="24" :lg="12" style="padding: 0 10px 10px 5px;">
            <div class="col" style="height: 100%;">
              <div class="title" style="margin-bottom: 7px;">
                <svg-icon name="right-arrow" />
                <span>科室医废情况</span>
              </div>
              <div id="workOdertTrendEcharts"></div>
              <div class="btns">
                <span :class="{ 'active-btn': completionRateType2 == 'day' }" @click="changeCompletionRateType2('day')">今日</span>
                <span :class="{ 'active-btn': completionRateType2 == 'month' }" @click="changeCompletionRateType2('month')">本月</span>
                <span :class="{ 'active-btn': completionRateType2 == 'year' }" @click="changeCompletionRateType2('year')">本年</span>
              </div>
            </div>
          </el-col> -->
        <!-- </el-row> -->
        <el-row :gutter="24" style="height: 100%">
          <el-col :xs="24" :md="24" :lg="24" style="width: 100%">
            <div class="col" style="height: 100%; width: 100%">
              <div style="display: flex; justify-content: space-between">
                <div class="title">
                  <svg-icon name="right-arrow" />
                  <span>收集记录</span>
                </div>
                <div class="btn">
                  <span :class="{ 'active-btn': completionRateType3 == 'day' }" @click="changeCompletionRateType3('day')">今日</span>
                  <span :class="{ 'active-btn': completionRateType3 == 'month' }" @click="changeCompletionRateType3('month')">本月</span>
                  <span :class="{ 'active-btn': completionRateType3 == 'year' }" @click="changeCompletionRateType3('year')">本年</span>
                </div>
              </div>
              <div style="height: 100%">
                <div class="contentTable">
                  <div class="contentTable-main table-content">
                    <el-table :data="tableData" :height="tableHeight" style="width: 100%; overflow: auto">
                      <el-table-column type="index" label="序号" width="50" align="center">
                        <template slot-scope="scope">
                          <span>{{ (pagination.current - 1) * pagination.size + scope.$index + 1 }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="officeName" align="center" label="所属科室" width="150" show-overflow-tooltip></el-table-column>
                      <el-table-column prop="count" align="center" label="医废数量"></el-table-column>
                      <el-table-column prop="barCode" align="center" s label="医废编码" width="130" show-overflow-tooltip></el-table-column>
                      <el-table-column prop="wasteType" align="center" label="医废类型" show-overflow-tooltip></el-table-column>
                      <el-table-column prop="gatherTime" align="center" label="收集时间" show-overflow-tooltip></el-table-column>
                      <el-table-column prop="gatherWeigh" align="center" label="收集重量">
                        <template slot-scope="scope">
                          <span>{{ scope.row.gatherWeigh }}kg</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="receivedPersonName" align="center" label="收集人员"> </el-table-column>
                      <el-table-column prop="receivedSignature" align="center" label="收集人员签字">
                        <template slot-scope="scope">
                          <span style="color: #5188fc; cursor: pointer" @click="showPicture(scope.row)">查看图片</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="officeSignature" align="center" label="科室人员签字">
                        <template slot-scope="scope">
                          <span style="color: #5188fc; cursor: pointer" @click="showPicture2(scope.row)">查看图片</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="inventoryStatus" align="center" label="状态">
                        <template slot-scope="scope">
                          <span v-if="scope.row.inventoryStatus === '1'">已收集</span>
                          <span v-if="scope.row.inventoryStatus === '2'">已入站</span>
                          <span v-if="scope.row.inventoryStatus === '3'">已出站</span>
                          <span v-else></span>
                        </template>
                      </el-table-column>
                      <el-table-column align="center" label="医废追溯" width="100">
                        <template slot-scope="scope">
                          <div>
                            <span style="color: #5188fc; cursor: pointer" @click="selectConfigRowData(scope.row.id)">追溯</span>
                          </div>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </div>
                <div class="contentTable-footer">
                  <el-pagination
                    style="margin-top: 3px"
                    :current-page="pagination.current"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                    :page-size="pagination.size"
                    :page-sizes="[15, 30, 50]"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                  ></el-pagination>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <imgCarousel :dialogVisibleImg="dialogVisibleImg" :imgArr="imgArr" @closeDialog="closeDialogImg"></imgCarousel>
    </div>
    <template v-if="retrospectShow">
      <retrospect ref="retrospect" :dialogShow="retrospectShow" :detailId="detailId" @retrospectCloseDialog="retrospectCloseDialog"></retrospect>
    </template>
  </div>
</template>

<script>
import store from '@/store/index'
import * as echarts from 'echarts'
import imgCarousel from '../common/imgCarousel.vue'
import retrospect from './retrospect.vue'
import tableListMixin from '@/mixins/tableListMixin.js'
export default {
  name: 'waste',
  components: {
    imgCarousel,
    retrospect
  },
  mixins: [tableListMixin],

  props: {
    treeList: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      flowCode: '', // 工单状态
      free1: '',
      orderStatisticsName: [],
      orderType: 'year',
      seriesData: [],
      tableData: [],
      pagination: {
        current: 1,
        size: 15
      },
      total: 0,
      completionRateType: 'day',
      completionRateType2: 'day',
      completionRateType3: 'day',

      countData: {},
      tableColumn: [],
      dialogTitle: '',
      workOrderDetailCenterShow: false,
      imgArr: [],
      dialogVisibleImg: false,
      retrospectShow: false,
      workTypeCode: '',
      timer: ''
    }
  },
  watch: {
    '$store.state.settings.sidebarCollapse': {
      handler(val) {
        this.$nextTick(() => {
          let echartsDom = ['workOdertTypeEcharts', 'workOdertTrendEcharts']
          setTimeout(() => {
            echartsDom.forEach((item) => {
              echarts.init(document.getElementById(item)).resize()
            })
          }, 250)
        })
      },
      deep: true
    }
  },
  mounted() {
    // this.getReckonCount()
    // this.getWorkOderType()
    // this.getWorkOderTrend()
    this.timer = setInterval(() => {
      if (this.treeList.modelCode) {
        this.getTableList()
        clearInterval(this.timer)
      }
    }, 20)
  },
  methods: {
    closeDialogImg() {
      this.dialogVisibleImg = false
      this.imgArr = []
    },
    showPicture2(row) {
      if (row.officeSignature) {
        // let str = row.ossFilePrefix + row.officeSignature
        let str = this.$tools.imgUrlTranslation(row.officeSignature)

        this.imgArr.push(str)

        this.dialogVisibleImg = true
      } else {
        this.$message.error('无科室人员签名')
      }
    },
    showPicture(row) {
      if (row.receivedSignature) {
        // let str = row.ossFilePrefix + row.receivedSignature
        let str = this.$tools.imgUrlTranslation(row.receivedSignature)
        this.imgArr.push(str)
        this.dialogVisibleImg = true
      } else {
        this.$message.error('无收集人员签名')
      }
    },
    selectConfigRowData(id) {
      this.detailId = id
      this.retrospectShow = true
    },
    retrospectCloseDialog() {
      this.retrospectShow = false
    },
    changeCompletionRateType(type) {
      this.completionRateType = type
      this.getWorkOderType()
    },
    changeCompletionRateType2(type) {
      this.completionRateType2 = type
      this.getWorkOderTrend()
    },
    changeCompletionRateType3(type) {
      this.completionRateType3 = type
      this.getTableList()
    },
    replace() {
      let str = this.treeList.modelCode
      let num = str.replace(/[^0-9]/gi, '') || ''
      return num
    },
    getTableList() {
      let params = {
        ssmType: this.treeList.ssmType,
        spatialId: this.replace(),
        dateType: this.completionRateType3,
        pageNo: this.pagination.current,
        pageSize: this.pagination.size
      }
      this.$api.getDepartMedicalWasteList(params).then((res) => {
        this.tableData = res.rows
        this.total = res.total
      })
    },
    getWorkOderType() {
      let params = {
        ssmType: this.treeList.ssmType,
        spatialId: this.replace(),

        dateType: this.completionRateType
      }
      this.$api.getRuiAnTypeAnalysisInfo(params).then((res) => {
        if (res.code === '200') {
          // this.workOdertTypeShow = false
          const arr = res.data.list
          const total = res.data.totalPrice ? res.data.totalPrice : 0
          this.$nextTick(() => {
            this.workOderTypeEchart(arr, total)
          })
        }
      })
    },
    workOderTypeEchart(arr, total) {
      const getchart = echarts.init(document.getElementById('workOdertTypeEcharts'))
      const data = []
      const xdata = Array.from(arr, ({ wasteType }) => wasteType)
      for (var i = 0; i < arr.length; i++) {
        data.push({
          name: arr[i].wasteType,
          value: arr[i].totalweigh,
          itemStyle: {
            normal: {
              borderWidth: 1,
              label: {
                normal: { show: true }
              }
            }
          }
        })
      }
      console.log(data, 'data')
      var seriesObj = [
        {
          name: data,
          type: 'pie',
          clockWise: false,
          radius: '50%',
          center: ['50%', '50%'],

          // hoverAnimation: false,
          itemStyle: {
            normal: {
              labelLine: {
                length: 50
              }
            }
          },
          label: {
            textStyle: {
              fontSize: 14 // 文字大小
            },
            formatter: '{b}:\n{c}' + 'kg' + '\n{d}%',
            borderWidth: 1,
            borderRadius: 1
          },
          data: data
        }
      ]
      const option = {
        backgroundColor: '',
        tooltip: {
          trigger: 'item',
          axisPointer: {
            lineStyle: {
              color: '#FFE3A6'
            }
          },
          backgroundColor: '#fff',
          borderColor: '#fff',
          textStyle: {
            color: '#000'
          },
          formatter: '{b}:\n{c}' + 'kg' + '\n{d}%'
        },
        series: seriesObj
      }
      getchart.on('click', (params) => {
        // 此处的value值为饼状图里 data的name 值
        var name = params.name
        arr.forEach((item) => {
          if (item.workTypeName == name) {
            this.workTypeCode = item.workTypeCode
          }
        })
        this.getTableList()
      })
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    getReckonCount() {
      let params = {
        ssmType: this.treeList.ssmType,
        spatialId: this.replace(),
        dateType: this.completionRateType2
      }
      this.$api.getMedicalTypeWeekTrendInfo(params).then((res) => {
        this.countData = res.data.list
      })
    },

    workOrderDetailCloseDialog() {
      this.workOrderDetailCenterShow = false
    },
    getWorkOderTrend() {
      let params = {
        ssmType: this.treeList.ssmType,
        spatialId: this.replace(),
        dateType: this.completionRateType2
      }
      this.$api.getAreaDepartmentMedicalWasteInfo(params).then((res) => {
        if (res.code === '200') {
          const arr = res.data.list
          const sortArr = arr.sort((a, b) => a.totalweigh - b.totalweigh)
          this.deptTopShow = false
          this.$nextTick(() => {
            this.getDeptProduceEcharts(sortArr)
          })
        }
      })
    },
    getDeptProduceEcharts(arr) {
      const getchart = echarts.init(document.getElementById('workOdertTrendEcharts'))
      const name = Array.from(arr, ({ officeName }) => officeName)
      const Infectedweigh = Array.from(arr, ({ Infectedweigh }) => Infectedweigh)
      const chemistryweigh = Array.from(arr, ({ chemistryweigh }) => chemistryweigh)
      const damageweigh = Array.from(arr, ({ damageweigh }) => damageweigh)
      const epidemicweigh = Array.from(arr, ({ epidemicweigh }) => epidemicweigh)
      const medicineweigh = Array.from(arr, ({ medicineweigh }) => medicineweigh)
      const pathoweigh = Array.from(arr, ({ pathoweigh }) => pathoweigh)
      const otherweigh = Array.from(arr, ({ otherweigh }) => otherweigh)
      const option = {
        backgroundColor: '',
        tooltip: {
          trigger: 'axis',
          backgroundColor: '#fff',
          textStyle: {
            color: '#000'
          },
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          },
          formatter: function (params) {
            var relVal = params[0].name
            for (var i = 0, l = params.length; i < l; i++) {
              relVal += '<br/>' + params[i].marker + params[i].seriesName + ' : ' + params[i].value.toLocaleString() + 'kg'
            }
            return relVal
          }
        },
        grid: {
          x: '12%',
          width: '75%',
          // y: '22%'
          bottom: '18%'
        },
        legend: {
          orient: 'horizontal',
          left: 'center',
          top: '0',
          // data: xdata,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 16,
          textStyle: {
            color: '#000' //  字体颜色
          }
        },
        xAxis: {
          type: 'category',
          axisLine: {
            show: true,
            lineStyle: {
              color: '#878EA9',
              width: 1,
              type: 'solid'
            }
          },
          // boundaryGap: true,
          axisTick: {
            show: false
          },
          axisLabel: {
            margin: 8,
            interval: 0,
            formatter: function (val) {
              var strs = val.split('') // 字符串数组
              var str = ''
              for (var i = 0, s; (s = strs[i++]); ) {
                str += s
                if (!(i % 10)) str += '\n' // 按需要求余
              }
              return str
            }
          },
          data: name
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            show: true,
            textStyle: {
              color: '#878EA9'
            }
          },
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: '#3862B7',
              width: '1'
            }
          },
          splitLine: {
            lineStyle: {
              color: '#314A89',
              width: 1,
              type: 'solid'
            }
          },
          axisTick: {
            show: false
          }
        },
        series: [
          {
            name: '损伤类',
            data: damageweigh,
            type: 'bar',
            stack: 'one',
            barWidth: '6',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(175, 255, 217, 0.2)'
                },
                {
                  offset: 1,
                  color: '#2AF598 '
                }
              ])
            }
          },
          {
            name: '病理类',
            data: pathoweigh,
            type: 'bar',
            stack: 'one',
            barWidth: '6',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(255, 229, 143, 0.2)'
                },
                {
                  offset: 1,
                  color: '#FFE58F '
                }
              ])
            }
          },
          {
            name: '化学类',
            data: chemistryweigh,
            type: 'bar',
            stack: 'one',
            barWidth: '6',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(124, 174, 255, 0.2)'
                },
                {
                  offset: 1,
                  color: '#7CAEFF'
                }
              ])
            }
          },
          {
            name: '感染类',
            data: Infectedweigh,
            type: 'bar',
            stack: 'one',
            barWidth: '6',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(255, 198, 144, 0.2)'
                },
                {
                  offset: 1,
                  color: '#FFC690'
                }
              ])
            }
          },
          {
            name: '药物类',
            data: medicineweigh,
            type: 'bar',
            stack: 'one',
            barWidth: '6',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(255, 96, 96, 0.2)'
                },
                {
                  offset: 1,
                  color: '#FF6060'
                }
              ])
            }
          },
          {
            name: '涉疫类',
            data: epidemicweigh,
            type: 'bar',
            stack: 'one',
            barWidth: '6',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(0, 248, 114, 0.2)'
                },
                {
                  offset: 1,
                  color: 'rgba(0, 248, 114, 1) '
                }
              ])
            }
          },
          {
            name: '其他类',
            data: otherweigh,
            type: 'bar',
            stack: 'one',
            barWidth: '6',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(147, 130, 255, 0.2)'
                },
                {
                  offset: 1,
                  color: 'rgba(147, 130, 255, 1)'
                }
              ])
            }
          }
        ],
        dataZoom: [
          {
            type: 'slider',
            realtime: true,
            startValue: 0,
            endValue: 5,
            height: 4,
            fillerColor: 'rgba(17, 100, 210, 0.42)', // 滚动条颜色
            borderColor: 'rgba(17, 100, 210, 0.12)',
            handleSize: 0, // 两边手柄尺寸
            showDetail: false, // 拖拽时是否展示滚动条两侧的文字
            top: '96%',
            zoomLock: true // 是否只平移不缩放
            // moveOnMouseMove:true, //鼠标移动能触发数据窗口平移
            // zoomOnMouseWheel :true, //鼠标移动能触发数据窗口缩放
          },
          {
            type: 'inside', // 支持内部鼠标滚动平移
            start: 0,
            end: 40,
            zoomOnMouseWheel: false, // 关闭滚轮缩放
            moveOnMouseWheel: true, // 开启滚轮平移
            moveOnMouseMove: true // 鼠标移动能触发数据窗口平移
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    handleSizeChange(val) {
      this.pagination.size = val
      this.getTableList()
    },
    handleCurrentChange(val) {
      this.pagination.current = val
      this.getTableList()
    }
  }
}
</script>

<style lang="scss" scoped>
.control-btn-header {
  background-color: #fff;

  & > div {
    display: flex;
    padding: 10px 5px;
  }
}

.content {
  height: 100%;

  ::v-deep .el-row {
    height: 100%;

    .el-col {
      height: 100%;
    }
  }
}

.buttom {
  height: 50%;
  background-color: #fff;
  border-radius: 5px;
}

.ipt {
  width: 200px;
  margin-right: 10px;
}

.col {
  position: relative;
  background-color: #fff;
  height: 100%;
  border-radius: 5px;
  padding: 10px;
}

#workOdertTypeEcharts,
#workOdertTrendEcharts {
  position: relative;
  width: 100%;
  height: 95%;
}

.title span {
  font-size: 15px;
}

.table-block-text {
  width: 80px;
  line-height: 24px;
  text-align: center;
  cursor: pointer;
  color: #fff;
}

.color_blue {
  color: #5188fc;
  cursor: pointer;
}

.color_blue {
  color: #5188fc;
  cursor: pointer;
}

.btns {
  position: absolute;
  width: 140px;
  height: 24px;
  right: 3%;
  top: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.btns > span {
  width: 30%;
  background-color: #f6f5fa;
  font-size: 12px;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #ededf5;
  color: #7f848c;
  cursor: pointer;
}

.btn {
  width: 140px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 3px;
}

.btn > span {
  width: 30%;
  background-color: #f6f5fa;
  font-size: 12px;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #ededf5;
  color: #7f848c;
  cursor: pointer;
}

.active-btn {
  background-color: #3562db !important;
  color: #fff !important;
  border-color: #3562db !important;
}

.contentTable {
  height: calc(100% - 65px);
  background: #fff;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  overflow: auto;

  .contentTable-main {
    flex: 1;
    overflow: auto;
  }

  .contentTable-footer {
    padding: 10px 0 0;
  }

  .alarmLevel {
    padding: 3px 6px;
    border-radius: 4px;
    color: #fff;
    line-height: 14px;
  }

  .alarmStatus {
    position: relative;
    display: inline-block;
    padding-left: 12px;

    .alarmStatusIcon {
      display: inline-block;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 8px;
      height: 8px;
      border-radius: 100%;
    }
  }

  .collectIcon {
    font-size: 16px;
    margin-right: 4px;
  }
}
</style>
