<!-- 运行总览 -->
<template>
  <PageContainer class="envirOverview">
    <div slot="header" class="control-btn-header">环境监测总览</div>
    <div slot="content" v-loading="loading" class="envirOverview-content">
      <ContentCard v-for="gather in monitorData" :key="gather.name" :title="gather.name">
        <div v-if="gather.name == '室外'" slot="content" class="outdoor-content">
          <div v-for="item in gather.list" :key="item.parameterId" class="outdoor-item">
            <p class="item-title">{{ item.averageName }}</p>
            <div class="item-value">
              <!-- <p style="min-width: 115px;"> -->
              <p>
                <span class="item-num">{{ item.average }}</span>
                <span class="item-unit">{{ item.unit }}</span>
              </p>
              <span v-if="item.averageConfigName" class="item-info" :style="{ backgroundColor: item.colour }">{{ item.averageConfigName }}</span>
            </div>
          </div>
        </div>
        <el-row v-else-if="gather.name == '室内'" slot="content" class="indoor-content">
          <el-col v-for="item in gather.list" :key="item.parameterId" :xs="24" :md="24" :lg="12" :xl="8">
            <div class="indoor-item">
              <div class="item-heade">
                <p class="heade-title">{{ item.parameterName }}</p>
                <div class="heade-right" @click="monitoringList(1, item)">
                  <p class="right-name">当前平均</p>
                  <p class="right-value">{{ (item.average || '-') + (item.unit || '') }}</p>
                  <p v-if="item.averageConfigName" class="right-info" :style="{ backgroundColor: item.colour }">{{ item.averageConfigName }}</p>
                </div>
              </div>
              <div class="item-main">
                <div class="main-alarm">
                  <div class="alarm-img">
                    <img v-if="item.exceptionInfoEntityList.length" src="../../../assets/images/monitor/lnAlarm-img.png" />
                    <img v-else src="../../../assets/images/monitor/noAlarm-img.png" />
                  </div>
                  <div v-if="item.exceptionInfoEntityList.length" class="alarm-list" >
                    <div
                      v-for="alarm in item.exceptionInfoEntityList"
                      :key="alarm.exceptionName" class="alarm-item"
                      :style="{ backgroundColor: getColor(alarm.colour, 0.1) }"
                      @click="monitoringList(2, item, alarm.exceptionName)"
                    >
                      <p class="alarm-name">{{ alarm.exceptionName }}</p>
                      <p class="alarm-count" :style="{ color: alarm.colour }">{{ alarm.exceptionCount }}</p>
                    </div>
                  </div>
                  <div v-else class="noAlarm-text">全部舒适</div>
                </div>
                <div class="main-progress">
                  <div v-for="(v, i) in item.environmentConfig" :key="v.id" class="progress-item" :style="{ width: v.ratio + '%', backgroundColor: v.colour }">
                    <span class="progress-min">{{ v.min }}</span>
                    <span class="progress-name">{{ v.configName }}</span>
                    <span v-if="i == item.environmentConfig.length - 1" class="progress-max">{{ v.max + v.unit }}</span>
                  </div>
                  <div
                    v-if="item.environmentConfig.length && item.minNumber != null"
                    class="progress-tips"
                    :style="{
                      left: getRatio(item.environmentConfig, item.minNumber) + '%',
                      border: '1px dashed' + item.minNumberColour,
                      transform: 'translateX(' + getOffset(item.environmentConfig, item.minNumber) + ')'
                    }"
                    @click="monitoringItem(1, item)"
                  >
                    <div
                      :style="{
                        width: '100%',
                        height: '100%',
                        padding: '8px',
                        background: getColor(item.minNumberColour || '', 0.1)
                      }"
                    >
                      <p class="tips-title">今日最低</p>
                      <p>
                        <span class="tips-num">{{ item.minNumber }}</span>
                        <span class="tips-unit">{{ item.unit }}</span>
                      </p>
                    </div>
                  </div>
                  <div
                    v-if="item.environmentConfig.length && item.maxNumber != null"
                    class="progress-tips"
                    :style="{
                      left: getRatio(item.environmentConfig, item.maxNumber) + '%',
                      border: '1px dashed' + item.maxNumberColour,
                      transform: 'translateX(' + getOffset(item.environmentConfig, item.maxNumber) + ')'
                    }"
                    @click="monitoringItem(0, item)"
                  >
                    <div
                      :style="{
                        width: '100%',
                        height: '100%',
                        padding: '8px',
                        background: getColor(item.maxNumberColour || '', 0.1)
                      }"
                    >
                      <p class="tips-title">今日最高</p>
                      <p>
                        <span class="tips-num">{{ item.maxNumber }}</span>
                        <span class="tips-unit">{{ item.unit }}</span>
                      </p>
                    </div>
                  </div>
                  <span
                    v-if="item.environmentConfig.length && item.minNumber != null"
                    class="relationLine"
                    :style="{
                      borderLeft: '1px dashed' + item.minNumberColour,
                      left: getRatio(item.environmentConfig, item.minNumber) + '%'
                    }"
                  ></span>
                  <span
                    v-if="item.environmentConfig.length && item.maxNumber != null"
                    class="relationLine"
                    :style="{
                      borderLeft: '1px dashed' + item.maxNumberColour,
                      left: getRatio(item.environmentConfig, item.maxNumber) + '%'
                    }"
                  ></span>

                  <span
                    v-if="item.environmentConfig.length && item.minNumber != null"
                    class="relationSpot"
                    :style="{
                      background: item.minNumberColour,
                      left: getRatio(item.environmentConfig, item.minNumber) + '%'
                    }"
                  ></span>
                  <span
                    v-if="item.environmentConfig.length && item.maxNumber != null"
                    class="relationSpot"
                    :style="{
                      background: item.maxNumberColour,
                      left: getRatio(item.environmentConfig, item.maxNumber) + '%'
                    }"
                  ></span>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </ContentCard>
      <monitoringListDialog v-if="isMonitoringListDialog" :requestInfo="listRequestInfo" :visible.sync="isMonitoringListDialog" />
      <monitoringItemDialog v-if="isMonitoringItemDialog" :requestInfo="itemRequestInfo" :visible.sync="isMonitoringItemDialog" />
    </div>
  </PageContainer>
</template>

<script>
import monitoringListDialog from './components/monitoringListDialog'
import monitoringItemDialog from './components/monitoringItemDialog'
import { monitorTypeList } from '@/util/dict.js'
import { getColor } from '@/util'
export default {
  name: 'envirOverview',
  components: {
    monitoringListDialog,
    monitoringItemDialog
  },
  data() {
    return {
      projectCode: monitorTypeList.find(item => item.projectName == '环境监测').projectCode,
      getColor: getColor,
      isMonitoringListDialog: false,
      isMonitoringItemDialog: false,
      itemRequestInfo: {}, // 监测详情请求参数
      listRequestInfo: {}, // 监测列表请求参数
      monitorData: [],
      loading: true
    }
  },
  computed: {
    getRatio() {
      return (list, value) => {
        let min = list[0].min
        let max = list[list.length - 1].max
        return Number(((Math.abs(value - min) / Math.abs(max - min)) * 100).toFixed(2))
      }
    },
    getOffset() {
      return (list, value) => {
        let min = list[0].min
        let max = list[list.length - 1].max
        let ratio = Number(((Math.abs(value - min) / Math.abs(max - min)) * 100).toFixed(2))
        if (ratio > 50) {
          return ratio > 80 ? '-100%' : '-50%'
        } else {
          return ratio > 30 ? '-50%' : '0'
        }
      }
    }
  },
  created() {
    this.getEnvirOverview()
  },
  methods: {
    // 查看监测列表
    monitoringList(type, item, name = '') {
      let params = {
        paramId: item.parameterId,
        parameterName: item.parameterName,
        projectCode: this.projectCode,
        exception: type == 1 ? 1 : 0,
        unit: item.unit
      }
      if (type == 1) {
        this.listRequestInfo = {
          ...params,
          average: item.average
        }
      } else {
        this.listRequestInfo = {
          ...params,
          exceptionInfo: item.exceptionInfoEntityList.find(v => v.exceptionName == name),
          averageConfigName: name
        }
      }
      this.isMonitoringListDialog = true
    },
    // 查看监测详情
    monitoringItem(type, item) {
      this.itemRequestInfo = {
        maxOrMinNumber: type == 0 ? item.maxNumber : item.minNumber,
        maxOrMin: type,
        paramId: item.parameterId,
        projectCode: this.projectCode
      }
      this.isMonitoringItemDialog = true
    },
    // 获取环境总览
    getEnvirOverview() {
      let params = {
        projectCode: this.projectCode
      }
      this.$api.GetEnvirOverview(params).then((res) => {
        this.loading = false
        if (res.code == 200) {
          this.monitorData = res.data
          console.log(this.monitorData)
        }
      }).catch(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.envirOverview {
  p {
    margin: 0;
  }

  .control-btn-header {
    border-bottom: 1px solid #e4e7ed;
    padding: 13px 16px;
    font-size: 14px;
    font-weight: 500;
    color: #121f3e;
    line-height: 14px;
  }

  ::v-deep .container-header {
    border-radius: 4px 4px 0 0;
  }

  ::v-deep .container-content {
    border-radius: 0 0 4px 4px;
  }

  .envirOverview-content {
    background: #fff;
    height: 100%;
    overflow: auto;
  }

  .outdoor-content {
    display: flex;
    flex-wrap: wrap;

    .outdoor-item {
      padding: 24px 40px;
      margin: 16px 0 0 16px;
      background: #faf9fc;
      border-radius: 8px;

      .item-title {
        font-size: 15px;
        font-weight: 500;
        color: #121f3e;
      }

      .item-value {
        margin-top: 9px;
        display: flex;
        font-weight: 500;
        align-items: flex-end;

        .item-num {
          font-size: 30px;
          font-weight: bold;
          color: #121f3e;
        }

        .item-unit {
          font-size: 15px;
          color: #ccced3;
          margin-left: 4px;
        }

        .item-info {
          height: 20px;
          display: inline-block;
          font-size: 14px;
          color: #fff;
          line-height: 20px;
          padding: 0 6px;
          border-radius: 4px;
          margin-bottom: 6px;
          margin-left: 16px;
        }
      }
    }
  }

  ::v-deep .el-row {
    width: 100%;

    .el-col {
      margin-top: 16px;
      padding-left: 16px;
    }
  }

  .indoor-content {
    display: flex;
    flex-wrap: wrap;

    .indoor-item {
      width: 100%;
      background: #fff;
      border-radius: 4px;
      opacity: 1;
      border: 1px solid #e4e7ed;

      .item-heade {
        padding: 12px 16px;
        box-shadow: 0 0 5px 0 rgb(18 31 62 / 12%);
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .heade-title {
        font-size: 15px;
        font-weight: 500;
        color: #121f3e;
      }

      .heade-right {
        cursor: pointer;
        display: flex;
        align-items: center;
        padding: 5px 16px;
        background: #f6f5fa;
        border-radius: 4px;

        .right-name {
          font-size: 14px;
          font-weight: 500;
          color: #121f3e;
        }

        .right-value {
          margin-left: 8px;
          font-size: 16px;
          font-weight: bold;
          color: #121f3e;
        }

        .right-info {
          height: 20px;
          display: inline-block;
          font-size: 14px;
          color: #fff;
          line-height: 20px;
          padding: 0 6px;
          border-radius: 4px;
          margin-left: 8px;
        }
      }

      .item-main {
        width: 100%;
        padding: 16px 24px 24px;

        .main-alarm {
          display: flex;
          align-items: center;
          justify-content: center;
          min-height: 114px;
          padding: 15px 0;
        }

        .alarm-img {
          width: 15%;
          text-align: center;
          height: 48px;

          img {
            width: 48px;
            height: 48px;
          }
        }

        .alarm-list {
          max-width: 85%;
          display: flex;
          flex-wrap: wrap;
        }

        .alarm-item {
          cursor: pointer;
          margin-left: 16px;
          width: 82px;
          height: 84px;
          border-radius: 4px;
          display: flex;
          justify-content: space-evenly;
          align-items: center;
          flex-direction: column;
        }

        .alarm-name {
          font-size: 15px;
          font-weight: 500;
          color: #121f3e;
        }

        .alarm-count {
          font-size: 30px;
          font-weight: bold;
          line-height: 36px;
        }

        .noAlarm-text {
          font-size: 30px;
          font-weight: 600;
          color: #00bc6d;
        }

        .main-progress {
          width: 100%;
          display: flex;
          padding: 100px 0 30px;
          position: relative;
          margin-top: 16px;
        }

        .progress-item {
          height: 16px;
          position: relative;
          font-size: 12px;
          line-height: 14px;
          border-left: 2px #fff solid;

          .progress-min {
            position: absolute;
            transform: translateX(-50%);
            bottom: -20px;
          }

          .progress-name {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            white-space: nowrap;
            color: #fff;
          }

          .progress-max {
            position: absolute;
            right: 0;
            bottom: -20px;
          }
        }

        .progress-item:first-child {
          border-left: none;

          .progress-min {
            transform: none;
            left: 0;
          }
        }

        .progress-tips {
          position: absolute;
          top: 10px;
          border-radius: 4px;
          background: #fff;
          cursor: pointer;
          z-index: 1;

          .tips-title {
            white-space: nowrap;
            font-size: 14px;
            font-weight: 500;
            color: #121f3e;
            line-height: 16px;
            margin-bottom: 8px;
          }

          .tips-num {
            font-size: 20px;
            font-weight: bold;
            color: #121f3e;
            line-height: 20px;
          }

          .tips-unit {
            font-size: 15px;
            font-weight: 500;
            color: #ccced3;
            line-height: 18px;
            margin-left: 4px;
          }
        }

        .progress-tips:hover {
          z-index: 10;
        }

        .relationLine {
          position: absolute;
          top: 75px;
          display: inline-block;
          height: 25px;
        }

        .relationSpot {
          position: absolute;
          top: 95px;
          width: 8px;
          height: 8px;
          border: 2px solid #fff;
          border-radius: 100%;
          transform: translateX(-50%);
          z-index: 10;
        }
      }
    }
  }

  ::v-deep .box-card {
    padding: 16px 16px 16px 0;

    .card-title {
      padding-left: 13px;
    }

    .card-body {
      margin: 0;
    }
  }
}
</style>
