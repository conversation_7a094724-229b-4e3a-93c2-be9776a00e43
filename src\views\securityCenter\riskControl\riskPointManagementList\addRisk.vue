<template>
  <div style="height: 100%;">
    <div class="content_box">
      <el-form ref="formInline" :model="formInline" :inline="true" class="form-inline" label-width="135px" :rules="rules" @submit.native.prevent>
        <el-form-item label="风险点名称" prop="riskName">
          <el-input
            v-if="!disabled"
            v-model.trim="formInline.riskName"
            :disabled="disabled"
            placeholder="请填写风险点名称"
            show-word-limit
            :maxlength="50"
            class="sino_sdcp_input mr15"
          ></el-input>
          <span v-else class="detaillClass">{{ formInline.riskName }}</span>
        </el-form-item>
        <el-form-item label="风险位置" prop="riskPlaceId">
          <el-cascader
            v-if="!disabled"
            ref="myCascader"
            v-model="formInline.riskPlaceId"
            :options="riskLocalArrData"
            :props="riskPropsType"
            :collapse-tags="true"
            placeholder="请选择风险位置"
            class="sino_sdcp_input mr15"
            @change="hangdleChange"
          ></el-cascader>
          <span v-else class="detaillClass">{{ formInline.riskPlace }}</span>
        </el-form-item>
        <el-form-item label="风险研判" prop="riskLevel">
          <span v-if="!disabled" class="el-input el-input--suffix el-input__inner mr15" style="cursor: pointer;" @click="goyanpan">{{ formInline.yanpanName }}</span>
          <span v-else class="detaillClass">{{ formInline.yanpanName }}</span>
        </el-form-item>
        <el-form-item label="责任部门" prop="taskTeamId">
          <el-select
            v-if="!disabled"
            ref="groupRis"
            v-model="formInline.taskTeamId"
            class="sino_sdcp_input mr15"
            multiple
            :props="groupPropsType"
            filterable
            collapse-tags
            placeholder="请选择责任部门"
            @change="groupHangdleChange"
          >
            <el-option v-for="item in groupRiskArrData" :key="item.value" :label="item.teamName" :value="item.id"></el-option>
          </el-select>
          <span v-else class="detaillClass">{{ formInline.taskTeamName }}</span>
        </el-form-item>
        <el-form-item label="责任人" prop="responsiblePersonName">
          <el-select
            v-if="!disabled"
            ref="groupRis"
            v-model="formInline.responsiblePersonName"
            filterable
            multiple
            collapse-tags
            class="sino_sdcp_input mr15"
            placeholder="请选择责任人"
            @change="zerenrenChange"
          >
            <el-option v-for="item in groupRiskArrNameList" :key="item.dictValue" :label="item.name" :value="item.name"></el-option>
          </el-select>
          <span v-else class="detaillClass">{{ formInline.responsiblePersonName.toString() }}</span>
        </el-form-item>
        <el-form-item label="风险点编号" prop="riskCode">
          <el-input
            v-if="!disabled"
            v-model.trim="formInline.riskCode"
            :disabled="disabled"
            placeholder="请填写风险点编号"
            show-word-limit
            :maxlength="11"
            class="sino_sdcp_input mr15"
          ></el-input>
          <span v-else class="detaillClass">{{ formInline.riskCode }}</span>
        </el-form-item>
        <!-- <br /> -->
        <el-form-item label="应急电话" prop="urgentPhone">
          <el-input
            v-if="!disabled"
            v-model.number="formInline.urgentPhone"
            :disabled="disabled"
            placeholder="请填写应急电话"
            show-word-limit
            :maxlength="11"
            class="sino_sdcp_input mr15"
          ></el-input>
          <span v-else class="detaillClass">{{ formInline.urgentPhone }}</span>
        </el-form-item>
        <el-form-item label="应急联系电话" prop="urgentContactPhone">
          <el-input
            v-if="!disabled"
            v-model.number="formInline.urgentContactPhone"
            :disabled="disabled"
            placeholder="请填写应急联系电话"
            show-word-limit
            :maxlength="11"
            class="sino_sdcp_input mr15"
          ></el-input>
          <span v-else class="detaillClass">{{ formInline.urgentContactPhone }}</span>
        </el-form-item>
        <el-form-item label="事故后果" prop="accidentTypeName" class="result">
          <span
            v-if="!disabled"
            class="el-input el-input--suffix el-input__inner"
            style="overflow: auto;"
            :style="formInline.accidentTypeName ? 'cursor: pointer;' : 'cursor: pointer;color:#c0c4cc'"
            @click="goConsequence"
            >{{ formInline.accidentTypeName || '请选择事故后果' }}</span
          >
          <span v-else class="detaillClass">{{ formInline.accidentTypeName }}</span>
        </el-form-item>
        <el-form-item label="风险类型" prop="riskType">
          <el-select v-if="!disabled" ref="groupRis" v-model="formInline.riskType" filterable class="sino_sdcp_input mr15" placeholder="请选择风险类型" @change="riskTypeChanged">
            <el-option v-for="item in riskTypeList" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"></el-option>
          </el-select>
          <span v-else class="detaillClass">{{ riskTypeName }}</span>
        </el-form-item>
        <el-form-item v-if="formInline.riskType != '3'" label="管控层级" prop="controlLevel">
          <el-select v-if="!disabled" v-model="formInline.free1" filterable class="sino_sdcp_input mr15" placeholder="请选择管控层级">
            <el-option v-for="item in controlLevelList" :key="item.id" :label="item.dictName" :value="item.dictCode"></el-option>
          </el-select>
          <span v-else class="detaillClass">{{ free1Name }}</span>
        </el-form-item>
        <br />
        <el-form-item label="图片">
          <el-upload
            v-if="!disabled"
            ref="uploadFile"
            drag
            multiple
            class="mterial_file"
            action="string"
            list-type="picture-card"
            :file-list="fileEcho"
            :http-request="httpRequest"
            accept=".jpg, .jpeg, .png, .JPG, .JPEG, .bmp"
            :limit="9"
            :on-exceed="handleExceed"
            :on-preview="handlePictureCardPreview"
            :beforeUpload="beforeAvatarUpload"
            :on-remove="handleRemove"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text" style="top: 33px;">
              将文件拖到此处，或
              <em>点击上传</em>
            </div>
            <div slot="tip" class="el-upload__tip">可上传九张以内，小于40M的图片</div>
          </el-upload>
          <el-dialog :visible.sync="dialogVisible" style="z-index: 99999;">
            <img width="100%" :src="dialogImageUrl" alt />
          </el-dialog>
          <div v-if="disabled" class="sion-icon">
            <div v-for="(src, p) in attachmentStr" :key="p" class="attachmentUrl-block" @click="seePictrue(attachmentStr, p)">
              <img :src="src" />
            </div>
            <img v-if="attachmentStr.length == 0" src="@/assets/images/defaultimg.png" />
          </div>
        </el-form-item>
        <br />
        <el-form-item label="预案流程图">
          <el-upload
            v-if="!disabled"
            ref="uploadFile"
            drag
            multiple
            class="mterial_file"
            action="string"
            list-type="picture-card"
            :file-list="fileEcho3"
            :http-request="httpRequest3"
            accept=".jpg, .jpeg, .png, .JPG, .JPEG, .bmp"
            :limit="1"
            :on-exceed="handleExceed3"
            :beforeUpload="beforeAvatarUpload3"
            :on-preview="handlePictureCardPreview3"
            :on-remove="handleRemove3"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text" style="top: 33px;">
              将文件拖到此处，或
              <em>点击上传</em>
            </div>
            <div slot="tip" class="el-upload__tip">可上传单张图片</div>
          </el-upload>
          <el-dialog :visible.sync="dialogVisible" style="z-index: 99999;">
            <img width="100%" :src="dialogImageUrl" alt />
          </el-dialog>
          <div v-if="disabled" class="sion-icon">
            <div v-for="(src, p) in processUrl" :key="p" class="attachmentUrl-block" @click="seePictrue(processUrl, p)">
              <img :src="src" />
            </div>
            <img v-if="processUrl.length == 0" src="@/assets/images/defaultimg.png" />
          </div>
        </el-form-item>

        <br />

        <el-form-item label="风险告知栏">
          <el-upload
            v-if="!disabled"
            ref="uploadFile"
            drag
            multiple
            class="mterial_file"
            action="string"
            list-type="picture-card"
            :file-list="fileEcho4"
            :http-request="httpRequest4"
            accept=".jpg, .jpeg, .png, .JPG, .JPEG, .bmp"
            :limit="1"
            :on-exceed="handleExceed4"
            :beforeUpload="beforeAvatarUpload4"
            :on-preview="handlePictureCardPreview4"
            :on-remove="handleRemove4"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text" style="top: 33px;">
              将文件拖到此处，或
              <em>点击上传</em>
            </div>
            <div slot="tip" class="el-upload__tip">可上传单张图片</div>
          </el-upload>
          <el-dialog :visible.sync="dialogVisible" style="z-index: 99999;">
            <img width="100%" :src="dialogImageUrl" alt />
          </el-dialog>
          <div v-if="disabled" class="sion-icon">
            <div v-for="(src, p) in informUrl" :key="p" class="attachmentUrl-block" @click="seePictrue(informUrl, p)">
              <img :src="src" />
            </div>
            <img v-if="informUrl.length == 0" src="@/assets/images/defaultimg.png" />
          </div>
        </el-form-item>

        <br />

        <el-form-item class="risk">
          <div class="risk-analysis">
            <div class="header">
              <span class="title">风险分析</span>
              <span v-if="query.type != 'check'" class="add-btn" @click="addRows">添加行</span>
            </div>
            <!-- 模版1 -->
            <el-table
              v-if="isFirstTableShow"
              :key="randomKey"
              :data="tableData"
              style="width: 98%;"
              border
              :header-cell-style="{
                textAlign: 'center',
                lineHeight: '35px',
                padding: '0'
              }"
              @cell-dblclick="editData"
            >
              <el-table-column type="index" width="50" label="序号"></el-table-column>
              <el-table-column :label="formInline.riskType == '3' ? '作业步骤' : '检查项目'" width="180" property="checkItem">
                <template slot-scope="scope">
                  <el-input
                    v-if="scope.row[scope.column.property + 'isShow']"
                    :ref="scope.column.property"
                    v-model="scope.row.checkItem"
                    type="textarea"
                    maxlength="1500"
                    @blur="alterData(scope.row, scope.column)"
                  ></el-input>
                  <span v-else>{{ scope.row.checkItem }}</span>
                </template>
              </el-table-column>
              <el-table-column :label="formInline.riskType == '3' ? '危险源或潜事件' : '标准'" width="180" property="standard">
                <template slot-scope="scope">
                  <el-input
                    v-if="scope.row[scope.column.property + 'isShow']"
                    :ref="scope.column.property"
                    v-model="scope.row.standard"
                    type="textarea"
                    maxlength="1500"
                    @blur="alterData(scope.row, scope.column)"
                  ></el-input>
                  <span v-else>{{ scope.row.standard }}</span>
                </template>
              </el-table-column>
              <el-table-column :label="formInline.riskType == '3' ? '可能发生的事故类型及后果' : '不符合标准情况及后果'" width="190" property="result">
                <template slot-scope="scope">
                  <el-input
                    v-if="scope.row[scope.column.property + 'isShow']"
                    :ref="scope.column.property"
                    v-model="scope.row.result"
                    type="textarea"
                    maxlength="1500"
                    @blur="alterData(scope.row, scope.column)"
                  ></el-input>
                  <span v-else>{{ scope.row.result }}</span>
                </template>
              </el-table-column>
              <el-table-column label="现有控制措施">
                <el-table-column label="工程技术措施" width="150" property="technicalMeasure">
                  <template slot-scope="scope">
                    <el-input
                      v-if="scope.row[scope.column.property + 'isShow']"
                      :ref="scope.column.property"
                      v-model="scope.row.technicalMeasure"
                      type="textarea"
                      maxlength="1500"
                      @blur="alterData(scope.row, scope.column)"
                    ></el-input>
                    <span v-else>{{ scope.row.technicalMeasure }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="管理措施" width="150" property="manageMeasure">
                  <template slot-scope="scope">
                    <el-input
                      v-if="scope.row[scope.column.property + 'isShow']"
                      :ref="scope.column.property"
                      v-model="scope.row.manageMeasure"
                      type="textarea"
                      maxlength="1500"
                      @blur="alterData(scope.row, scope.column)"
                    ></el-input>
                    <span v-else>{{ scope.row.manageMeasure }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="培训教育措施" width="150" property="educationMeasure">
                  <template slot-scope="scope">
                    <el-input
                      v-if="scope.row[scope.column.property + 'isShow']"
                      :ref="scope.column.property"
                      v-model="scope.row.educationMeasure"
                      type="textarea"
                      maxlength="1500"
                      @blur="alterData(scope.row, scope.column)"
                    ></el-input>
                    <span v-else>{{ scope.row.educationMeasure }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="个体防护措施" width="150" property="protectiveMeasure">
                  <template slot-scope="scope">
                    <el-input
                      v-if="scope.row[scope.column.property + 'isShow']"
                      :ref="scope.column.property"
                      v-model="scope.row.protectiveMeasure"
                      type="textarea"
                      maxlength="1500"
                      @blur="alterData(scope.row, scope.column)"
                    ></el-input>
                    <span v-else>{{ scope.row.protectiveMeasure }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="应急措施" width="150" property="emergencyMeasure">
                  <template slot-scope="scope">
                    <el-input
                      v-if="scope.row[scope.column.property + 'isShow']"
                      :ref="scope.column.property"
                      v-model="scope.row.emergencyMeasure"
                      type="textarea"
                      maxlength="1500"
                      @blur="alterData(scope.row, scope.column)"
                    ></el-input>
                    <span v-else>{{ scope.row.emergencyMeasure }}</span>
                  </template>
                </el-table-column>
              </el-table-column>
              <el-table-column label="建议改进(新增)措施" width="150" property="adviceMeasure">
                <template slot-scope="scope">
                  <el-input
                    v-if="scope.row[scope.column.property + 'isShow']"
                    :ref="scope.column.property"
                    v-model="scope.row.adviceMeasure"
                    type="textarea"
                    maxlength="1500"
                    @blur="alterData(scope.row, scope.column)"
                  ></el-input>
                  <span v-else>{{ scope.row.adviceMeasure }}</span>
                </template>
              </el-table-column>
              <el-table-column v-if="query.type != 'check'" label="操作" width="80">
                <template slot-scope="scope">
                  <el-button v-if="scope.$index != 0" type="danger" size="mini" @click="deleteRow(scope.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
            <!-- 模版2 -->
            <el-table
              v-else
              :key="randomKey2"
              :data="tableData2"
              style="width: 100%;"
              border
              title="双击输入"
              :header-cell-style="{
                textAlign: 'center',
                lineHeight: '35px',
                padding: '0'
              }"
              @cell-dblclick="editData"
            >
              <el-table-column type="index" width="60" label="序号"></el-table-column>
              <el-table-column label="主要风险因素" width="320" property="riskFactor">
                <template slot-scope="scope">
                  <el-input
                    v-if="scope.row[scope.column.property + 'isShow']"
                    :ref="scope.column.property"
                    v-model="scope.row.riskFactor"
                    type="textarea"
                    size="medium"
                    resize="none"
                    maxlength="1500"
                    @blur="alterData(scope.row, scope.column)"
                  ></el-input>
                  <span v-else>{{ scope.row.riskFactor }}</span>
                </template>
              </el-table-column>
              <el-table-column label="安全操作要点" width="320" property="operationElement">
                <template slot-scope="scope">
                  <el-input
                    v-if="scope.row[scope.column.property + 'isShow']"
                    :ref="scope.column.property"
                    v-model="scope.row.operationElement"
                    type="textarea"
                    size="medium"
                    resize="none"
                    maxlength="1500"
                    @blur="alterData(scope.row, scope.column)"
                  ></el-input>
                  <span v-else>{{ scope.row.operationElement }}</span>
                </template>
              </el-table-column>
              <el-table-column label="主要风险管控措施" width="320" property="controlElement">
                <template slot-scope="scope">
                  <el-input
                    v-if="scope.row[scope.column.property + 'isShow']"
                    :ref="scope.column.property"
                    v-model="scope.row.controlElement"
                    type="textarea"
                    size="medium"
                    resize="none"
                    maxlength="1500"
                    @blur="alterData(scope.row, scope.column)"
                  ></el-input>
                  <span v-else>{{ scope.row.controlElement }}</span>
                </template>
              </el-table-column>
              <el-table-column label="应急处置措施" width="320" property="handleElement">
                <template slot-scope="scope">
                  <el-input
                    v-if="scope.row[scope.column.property + 'isShow']"
                    :ref="scope.column.property"
                    v-model="scope.row.handleElement"
                    type="textarea"
                    size="medium"
                    resize="none"
                    maxlength="1500"
                    @blur="alterData(scope.row, scope.column)"
                  ></el-input>
                  <span v-else>{{ scope.row.handleElement }}</span>
                </template>
              </el-table-column>
              <el-table-column v-if="query.type != 'check'" label="操作" width="120">
                <template slot-scope="scope">
                  <el-button v-if="scope.$index != 0" type="danger" size="mini" @click="deleteRow2(scope.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-form-item>
        <!-- <el-form-item label="状态" prop="status">
            <el-radio v-model="formInline.status" label="0">启用</el-radio>
            <el-radio v-model="formInline.status" label="1">禁用</el-radio>
          </el-form-item> -->
      </el-form>
      <conductJudged
        :id="id"
        ref="conductJudged"
        :dialogVisible="dialogVisibleDialog"
        :type="type"
        :dataContent="dataContent"
        @closeDialog="closeDialogList"
        @yanpan="yanpan"
      ></conductJudged>
      <accidentConsequence
        :id="id"
        ref="accidentConsequence"
        :dialogVisible="dialogVisibleDialog2"
        :type="type"
        :accidentTypeList="accidentTypeList"
        @closeDialog="closeDialog2"
        @complete="saveHouGuo"
      ></accidentConsequence>
      <imgCarousel ref="carousel" :dialogVisibleImg="dialogVisibleImg" :imgArr="imgArr" @closeDialog="closeDialog"></imgCarousel>
    </div>
    <div class="bottomBar">
      <div class="bottomWrap">
        <el-button type="primary" plain @click="$router.go(-1)">关闭</el-button>
        <el-button v-if="query.type != 'check'" type="primary" @click="complete()">保存</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { transData } from '@/util'
import conductJudged from './components/conductJudged.vue'
import accidentConsequence from './components/accidentConsequence.vue'
import imgCarousel from '@/components/imgCarousel/imgCarousel'
export default {
  name: 'addRisk',
  components: {
    conductJudged,
    accidentConsequence,
    imgCarousel
  },
  beforeRouteEnter(to, from, next) {
    if (to.meta.title) {
      const typeList = {
        add: '新增风险点',
        edit: '编辑风险点',
        check: '风险点详情'
      }
      to.meta.title = typeList[to.query.type] ?? '风险点详情'
    }
    next()
  },
  data() {
    return {
      // textarea: "",
      dialogVisibleImg: false,
      imgArr: [],
      id: '',
      dataContent: [],
      title: '',
      disabled: false,
      type: 'add',
      formInline: {
        yanpanName: '研判',
        riskName: '', // 风险点名称
        riskLevel: '', // 风险等级
        riskLevelCode: '', // 风险等级code
        riskPlaceId: '', // 风险位置ID
        riskPlaceIds: [], // 风险位置IDS
        riskPlacename: '', // 风险位置名称
        taskTeamId: [], // 负责班组ID
        taskTeamName: '', // 负责班组名称
        responsiblePersonCode: [], // 责任人id
        responsiblePersonName: [], // 责任人name
        status: '1', // 状态  1.3.0 默认禁用
        attachmentUrl: [], // 图片
        processUrl: [], // 预案流程图
        informUrl: [], // 风险告知栏
        urgentPhone: '', // 应急电话
        urgentContactPhone: '', // 应急联系电话
        riskElement: '', // 风险因素
        operationElement: '', // 操作要点
        controlElement: '', // 主要风险管控措施
        handleElement: '', // 应急处理措施
        isjudge: '',
        accidentTypeName: '', // 事故后果name
        accidentTypeId: '', // 事故后果id
        riskCode: '', // 风险点编号
        riskType: '2' // 风险分类
      },
      riskTypeName: '', // 风险分类名称
      free1Name: '', // 管控层级名称
      groupRiskArrNameList: [], // 责任人列表
      riskTypeList: [], // 风险分类列表
      // 风险等级
      riskLevelList: [],
      query: {}, // 页面传参
      tableCode: '', // 模板id，新增/修改必传
      // eslint-disable-next-line vue/no-dupe-keys
      id: '',
      blockLoading: false,
      // ----------------------------风险位置
      riskLocalArr: [],
      riskLocalArrData: [],
      riskPropsType: {
        children: 'children',
        label: 'gridName',
        value: 'id',
        checkStrictly: true,
        multiple: true
      },
      // ----------------------------负责班组
      groupRiskArr: [],
      groupRiskArrData: [],
      groupPropsType: {
        children: 'children',
        label: 'typeName',
        value: 'typeId',
        checkStrictly: true
      },
      rules: {
        riskName: [{ required: true, message: '请填写风险名称', trigger: 'change' }],
        riskType: [{ required: true, message: '请选择风险大类', trigger: 'change' }],
        riskPlaceId: [{ required: true, message: '请选择风险位置', trigger: 'change' }],
        // riskLevel: [
        //   { required: true, message: "请进行风险研判", trigger: "change" },
        // ],
        taskTeamId: [{ required: true, message: '请选择责任部门', trigger: 'change' }],
        responsiblePersonName: [{ required: true, message: '请选择责任人', trigger: 'change' }]
        // accidentTypeName: [{ required: true, message: '请选择事故后果', trigger: 'blur' }]
      },
      total: 0,
      // 图片相关
      fileEcho: [],
      fileEcho3: [],
      fileEcho4: [],
      dialogVisible: false,
      dialogImageUrl: '',
      attachmentStr: [], // 原图片附件路径
      dialogVisibleDialog: false,
      dialogVisibleDialog2: false,
      // eslint-disable-next-line vue/no-dupe-keys
      disabled: false,
      processUrl: [], // 原预案流程图路径
      informUrl: [],

      accidentTypeList: [],
      jsonContent: [], // 研判详情
      aaaid: [],
      tableData: [
        {
          checkItem: '',
          standard: '',
          result: '',
          technicalMeasure: '',
          manageMeasure: '',
          educationMeasure: '',
          protectiveMeasure: '',
          emergencyMeasure: '',
          adviceMeasure: ''
        }
      ],
      randomKey: Math.random(),
      randomKey2: Math.random(),
      controlLevelList: [],
      templateForm: {
        activities: '', // 作业活动
        facilities: '', // 设备设施
        regional: '' // 区域场所
      },
      tableData2: [
        {
          riskFactor: '',
          operationElement: '',
          controlElement: '',
          handleElement: ''
        }
      ],
      isFirstTableShow: ''
    }
  },
  watch: {
    formInline: {
      handler(val) {
        if (
          (val.riskType == '1' && this.templateForm.facilities == '1') ||
          (val.riskType == '2' && this.templateForm.regional == '1') ||
          (val.riskType == '3' && this.templateForm.activities == '1')
        ) {
          this.isFirstTableShow = true
        } else {
          this.isFirstTableShow = false
        }
      },
      deep: true
    }
  },
  created() {
    this.getDictionaryEntry()
    let dictValue = this.$route.query.dictValue
    if (dictValue) {
      this.formInline.riskType = dictValue
    }
  },
  mounted() {
    this.query = this.$route.query
    this.query.type = this.checkType ? this.checkType : this.$route.query.type
    this.getGridIdsByTeamId()
    this.tableCode = this.query.tableCode
    this.title = this.query.type == 'add' ? '新增巡检模板' : this.query.type == 'edit' ? '修改巡检模板' : '巡检模板详情'
    if (this.query.id) {
      // 修改，详情，获取模板详情
      this.getDetails()
      this.type = 'edit2'
      // this.disabled = true
    }
    this.disabled = this.query.type == 'check'
  },
  methods: {
    // enter() {
    //   console.log(window.event.keyCode);
    //   if (window.event.keyCode == 13) {
    //     console.log(this.formInline.riskElement);
    //   }
    // },
    // preText(pretext) {
    //   return pretext
    //     .replace(/\r\n/g, "<br/>")
    //     .replace(/\n/g, "<br/>")
    //     .replace(/\s/g, "&nbsp;");
    // },
    // 负责人
    async getUserList() {
      // 负责人
      this.$api
        .ipsmRiskWorkGetControlTeamUserList({
          controlTeamIds: this.formInline.taskTeamId.join(',')
        })
        .then((res) => {
          this.groupRiskArrNameList = res.data
        })
    },
    /**
     * 获取字典项
     */
    getDictionaryEntry() {
      // 获取负责班组
      this.groupRiskArrData = []
      this.$api.ipsmGetControlGroupInfoList({}).then((res) => {
        res.data.list.map((v) => {
          if (v.parent != '#' && v.useStatus != 2) {
            this.groupRiskArrData.push(v)
          }
        })
      })
      // 风险等级
      this.$api
        .ipsmGetDictValue({
          dictType: 'risk_level',
          isShowParent: 0
        })
        .then((res) => {
          if (res.code == 200) {
            this.riskLevelList = res.data
          }
        })
      // 获取风险分类
      this.treeLoading = true
      this.$api
        .ipsmGetDictValue({
          dictType: 'risk_order_type',
          isShowParent: 0
        })
        .then((res) => {
          this.treeLoading = this.$store.state.loadingShow
          if (res.code == '200' && res.data.length > 0) {
            let arr = this.$tools.transData(res.data, 'dictCode', 'parentCode', 'children')
            this.riskTypeList = arr
          }
        })
      this.$api.ipsmGetDictList({ dictType: 'hidden_danger_control' }).then((res) => {
        if (res.code == 200) {
          this.controlLevelList = res.data
        } else {
          this.$message.error(res.message)
        }
      })
      this.$api.ipsmGetRiskEvaluation({ dictType: 'risk_evaluation' }).then((res) => {
        if (res.code == 200) {
          this.templateForm.activities = res.data.activities
          this.templateForm.facilities = res.data.facilities
          this.templateForm.regional = res.data.regional
          if (
            (this.formInline.riskType == '1' && this.templateForm.facilities == '1') ||
            (this.formInline.riskType == '2' && this.templateForm.regional == '1') ||
            (this.formInline.riskType == '3' && this.templateForm.activities == '1')
          ) {
            this.isFirstTableShow = true
          } else {
            this.isFirstTableShow = false
          }
        } else {
          this.$message.error(res.message)
        }
      })
    },
    riskLevelChange(value) {},

    hangdleChange(val) {
      console.log(this.formInline.riskPlaceId)
      console.log(JSON.stringify(this.formInline.riskPlaceId))
      let list = this.$refs.myCascader.getCheckedNodes()
      // console.log(list);
      let name = []
      // let id = [];
      list.forEach((e) => {
        name.push(e.pathLabels.join('>'))
        // id.push(e.data.id);
      })
      this.formInline.riskPlaceIds = val.join('>')
      console.log(this.formInline.riskPlaceIds)
      // this.formInline.riskPlaceIds = id;
      this.formInline.riskPlacename = name
    },
    // 获取风险详情详情
    getDetails() {
      this.id = this.query.id
      let obj = JSON.parse(sessionStorage.getItem('LOGINDATA'))
      let data = {
        deptName: obj.officeName,
        deptCode: obj.officeCode,
        id: this.query.id
      }
      this.blockLoading = true
      this.$api
        .ipsmRiskManageGetRiskDetail(data)
        .then((res) => {
          if (res.code == '200') {
            this.accidentTypeList = res.data.accidentTypeList
            this.dataContent = res.data
            this.formInline = res.data
            this.free1Name = res.data.free1Name
            this.processUrl = res.data.processUrl
            this.attachmentStr = res.data.attachmentUrl
            this.informUrl = res.data.informUrl || []
            res.data.attachmentUrl.forEach((item) => {
              this.fileEcho.push({
                url: item,
                name: item.split('/')[item.split('/').length - 1].split('?')[0]
              })
            })
            res.data.processUrl.forEach((item) => {
              this.fileEcho3.push({
                url: item,
                name: item.split('/')[item.split('/').length - 1].split('?')[0]
              })
            })
            res.data.informUrl.forEach((item) => {
              this.fileEcho4.push({
                url: item,
                name: item.split('/')[item.split('/').length - 1].split('?')[0]
              })
            })
            let analysisListArr = []
            let analysisListArr2 = []
            if (res.data.analysisList && res.data.analysisList.length) {
              res.data.analysisList.forEach((item) => {
                let obj = {
                  checkItem: item.checkItem,
                  standard: item.standard,
                  result: item.result,
                  technicalMeasure: item.technicalMeasure,
                  manageMeasure: item.manageMeasure,
                  educationMeasure: item.educationMeasure,
                  protectiveMeasure: item.protectiveMeasure,
                  emergencyMeasure: item.emergencyMeasure,
                  adviceMeasure: item.adviceMeasure
                }
                analysisListArr.push(obj)
              })
              res.data.analysisList.forEach((item) => {
                let obj = {
                  riskFactor: item.riskFactor,
                  operationElement: item.operationElement,
                  controlElement: item.controlElement,
                  handleElement: item.handleElement
                }
                analysisListArr2.push(obj)
              })
            }
            this.tableData = analysisListArr
            this.tableData2 = analysisListArr2
            this.getaccidentTypeList()

            this.formInline.riskType = res.data.riskType.toString()
            this.riskTypeName = res.data.riskTypeName

            this.formInline.riskLevel = res.data.riskLevel.toString()
            this.formInline.riskPlaceIds = res.data.riskPlaceId.split(',')
            var riskArray = res.data.riskPlaceId.split('>')
            this.formInline.riskPlaceId = []

            riskArray.forEach((item) => {
              this.formInline.riskPlaceId.push(item)
            })
            console.log(JSON.stringify(this.formInline.riskPlaceId))
            this.formInline.riskPlacename = res.data.riskPlace.split(',')
            this.formInline.equipmentTypeId = res.data.equipmentTypeId
            this.formInline.status = res.data.status
            this.formInline.taskTeamId = res.data.taskTeamId.split(',')
            this.formInline.taskTeamName = res.data.taskTeamName
            this.getUserList().then((res) => {
              this.formInline.responsiblePersonName = this.dataContent.responsiblePersonName.split(',')
              this.formInline.responsiblePersonCode = this.dataContent.responsiblePersonCode.split(',')
            })
            this.blockLoading = false
            if (res.data.judgeType || res.data.riskLevelName) {
              this.formInline.yanpanName = res.data.riskLevelName
            } else {
              this.formInline.yanpanName = '研判'
            }
            let hascontrolLevel = false
            this.controlLevelList.forEach((item) => {
              if (this.formInline.free1 == item.dictCode) {
                hascontrolLevel = true
              }
            })
            if (!hascontrolLevel) {
              this.formInline.free1 = ''
            }
            console.log('vvi', hascontrolLevel)
            this.taskList(res.data.taskTeamId)
          } else {
            this.$message.error(res.message)
          }
        })
        .catch(() => {
          this.blockLoading = this.$store.state.loadingShow
        })
    },
    // 回显多选地区
    /**
     * treeData 列表数组
     */
    changeDetSelect(key, treeData) {
      let arr = [] // 在递归时操作的数组
      let returnArr = [] // 存放结果的数组
      let depth = 0 // 定义全局层级
      return childrenEach(treeData, depth)
      // 定义递归函数
      function childrenEach(childrenData, depthN) {
        for (var j = 0; j < childrenData.length; j++) {
          depth = depthN // 将执行的层级赋值 到 全局层级
          arr[depthN] = childrenData[j].id
          if (childrenData[j].id == key) {
            returnArr = arr.slice(0, depthN + 1) // 将目前匹配的数组，截断并保存到结果数组，
            break
          } else {
            if (childrenData[j].children) {
              depth++
              childrenEach(childrenData[j].children, depth)
            }
          }
        }
        return returnArr
      }
    },

    // 详情查看图片
    seePictrue(arr, index) {
      this.imgArr = arr
      this.$refs.carousel.index = index
      this.dialogVisibleImg = !this.dialogVisibleImg
    },
    closeDialog() {
      this.dialogVisibleImg = false
    },
    // 处理负责班组回显
    taskList(res) {
      let tasklist = []
      let list = res.split(',')
      this.groupRiskArrData.find((item, index) => {
        list.forEach((val) => {
          if (item.id == val) {
            tasklist.push(item.id)
          }
        })
      })
      this.formInline.taskTeamId = tasklist
    },
    getGridIdsByTeamId() {
      this.$api.ipsmGetGridList({}).then((res) => {
        let treeList = transData(res.data, 'id', 'parentId', 'children')
        this.riskLocalArrData = treeList
      })
    },
    goyanpan() {
      this.dialogVisibleDialog = true
    },
    goConsequence() {
      this.dialogVisibleDialog2 = true
    },
    closeDialogList() {
      this.dialogVisibleDialog = false
    },
    closeDialog2() {
      this.dialogVisibleDialog2 = false
    },
    // 选中事故后果
    saveHouGuo() {
      this.formInline.accidentTypeName = this.$refs.accidentConsequence.checkName.join(',')
      this.formInline.accidentTypeId = this.$refs.accidentConsequence.checked.join(',')
      this.dialogVisibleDialog2 = false
    },
    // 编辑 详情回显事故类型标签
    getaccidentTypeList() {
      if (this.accidentTypeList) {
        let id = []
        let name = []
        this.accidentTypeList.map((i) => {
          id.push(i.id)
          name.push(i.dictLabel)
        })
        this.formInline.accidentTypeId = id.join(',')
        this.formInline.accidentTypeName = name.join(',')
      }
    },
    yanpan() {
      this.dialogVisibleDialog = false
      this.formInline.yanpanName = this.$refs.conductJudged.formInline.riskLevelName
      this.formInline.riskLevel = this.$refs.conductJudged.formInline.riskLevel
      this.jsonContent = this.$refs.conductJudged.jsonContent
    },
    // 点击确定
    complete() {
      if (!this.formInline.accidentTypeName) {
        return this.$message.error('请先选择事故后果')
      }
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          let obj = JSON.parse(sessionStorage.getItem('LOGINDATA'))

          let data = { ...this.formInline }
          data.riskPlace = this.formInline.riskPlacename.toString()
          // data.riskPlaceId = this.formInline.riskPlaceId.toString();
          data.riskPlaceId = this.formInline.riskPlaceIds
          data.taskTeamName = this.formInline.taskTeamName
          data.taskTeamId = this.formInline.taskTeamId.toString()
          data.id = ''
          data.attachmentUrl = this.formInline.attachmentUrl
          data.processUrl = this.formInline.processUrl
          data.informUrl = this.formInline.informUrl
          data.platformFlag = 1
          if (this.$refs.conductJudged.formInline.riskLevel) {
            data.judgeScore = this.$refs.conductJudged.formInline.judgeScore
            data.judgeType = this.$refs.conductJudged.formInline.judgeType
            data.riskLevel = this.$refs.conductJudged.formInline.riskLevel
            data.judgeProbableScore = this.$refs.conductJudged.formInline.judgeProbableScore
            data.judgeResultScore = this.$refs.conductJudged.formInline.judgeResultScore
            data.judgeFrequencyScore = this.$refs.conductJudged.formInline.judgeFrequencyScore
            if (data.judgeType == '3') {
              data.judgeProbableScore = this.$refs.conductJudged.formInline.lsl
              data.judgeResultScore = this.$refs.conductJudged.formInline.lss
              // data.judgeScore = this.$refs.conductJudged.formInline.judgeScore
            }
            data.jsonContent = JSON.stringify(this.jsonContent)
          }
          data.attachmentStr = this.attachmentStr
          delete data.riskPlaceIds
          this.blockLoading = true
          // this.$store.commit('changeNetworkError', this.$route.query.type == 'edit' ? '修改失败，请检查您的网络…' : '新增失败，请检查您的网络…')
          let submitTableData = []
          if (this.isFirstTableShow) {
            this.tableData.forEach((item) => {
              let obj = {
                checkItem: item.checkItem,
                standard: item.standard,
                result: item.result,
                technicalMeasure: item.technicalMeasure,
                manageMeasure: item.manageMeasure,
                educationMeasure: item.educationMeasure,
                protectiveMeasure: item.protectiveMeasure,
                emergencyMeasure: item.emergencyMeasure,
                adviceMeasure: item.adviceMeasure
              }
              submitTableData.push(obj)
            })
          } else {
            this.tableData2.forEach((item) => {
              let obj = {
                riskFactor: item.riskFactor,
                operationElement: item.operationElement,
                controlElement: item.controlElement,
                handleElement: item.handleElement
              }
              submitTableData.push(obj)
            })
          }
          data.riskAnalysisList = JSON.stringify(submitTableData)
          if (this.query.type == 'edit') {
            // 修改
            data.id = this.query.id
            this.$api.ipsmRiskManageUpdateRisk(data).then((res) => {
              if (res.code == 200) {
                this.$message.success(res.message)
                this.$store.commit('keepAliveChange', false)
                this.$router.go(-1)
              } else {
                this.$message.error(res.message)
              }
              this.blockLoading = false
            })
          } else if (this.query.type == 'add') {
            // 新增
            this.$api.ipsmRiskManageInsertRisk(data).then((res) => {
              if (res.code == 200) {
                this.$message.success(res.message)
                this.$store.commit('keepAliveChange', false)
                this.$router.go(-1)
              } else {
                this.$message.error(res.message)
              }
              this.blockLoading = false
            })
          }
        } else {
          setTimeout(() => {
            var isError = document.getElementsByClassName('is-error')
            isError[0].querySelector('input').focus()
          }, 100)
        }
      })
    },
    // 选择负责班组
    groupHangdleChange(val) {
      this.formInline.responsiblePersonName = []
      this.formInline.responsiblePersonCode = []
      this.getUserList()
      let a = []
      for (let i of val) {
        this.groupRiskArrData.map((v) => {
          if (i == v.id) {
            return a.push(v.teamName)
          }
        })
      }
      this.formInline.taskTeamName = a.toString()
      console.log(this.formInline.taskTeamName)
    },
    // 责任人
    zerenrenChange(val) {
      this.formInline.responsiblePersonCode = []
      this.formInline.responsiblePersonName.forEach((item) => {
        this.formInline.responsiblePersonCode.push(
          this.groupRiskArrNameList.find((element) => {
            return item == element.name
          })
        )
      })
      this.formInline.responsiblePersonCode = this.formInline.responsiblePersonCode.map((item) => {
        return item.id
      })
      // return;
      // this.formInline.responsiblePersonName = this.groupRiskArrNameList.find(
      //   (item) => {
      //     return item.id == this.formInline.responsiblePersonCode;
      //   }
      // ).name;
      // console.log(this.formInline.responsiblePersonName);
    },

    //  ------------------------------------------上传图片相关1-----------------------------------------
    handleRemove(file, fileList) {
      this.attachmentStr.forEach((item, index) => {
        if (file.url == item) {
          this.attachmentStr.splice(index, 1)
        }
      })
      this.formInline.attachmentUrl = []
      fileList.forEach((item) => {
        this.formInline.attachmentUrl.push(item.raw)
      })
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    httpRequest(item) {
      this.formInline.attachmentUrl.push(item.file)
    },
    handleExceed() {
      this.$message.error('最多上传九份文件')
    },
    beforeAvatarUpload(file) {
      const isLt40M = file.size / 1024 / 1024 < 40
      if (!isLt40M) {
        this.$message.error('上传图片大小不能超过 40MB!')
        return false
      }
      if (file.name.indexOf(',') != -1) {
        this.$message.error('非法的文件名')
        return false
      }
    },

    //  ------------------------------------------3-----------------------------------------
    handleRemove3(file, fileList) {
      this.formInline.processUrl = []
      fileList.forEach((item) => {
        this.formInline.processUrl.push(item.raw)
      })
    },
    handleRemove4(file, fileList) {
      this.formInline.informUrl = []
      fileList.forEach((item) => {
        this.formInline.informUrl.push(item.raw)
      })
    },
    handlePictureCardPreview3(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handlePictureCardPreview4(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    httpRequest3(item) {
      this.formInline.processUrl.push(item.file)
    },
    httpRequest4(item) {
      this.formInline.informUrl.push(item.file)
    },
    handleExceed3() {
      this.$message.error('最多上传一份文件')
    },
    handleExceed4() {
      this.$message.error('最多上传一份文件')
    },
    beforeAvatarUpload3(file) {
      const isLt5M = file.size / 1024 / 1024 < 5
      if (!isLt5M) {
        this.$message.error('上传图片大小不能超过 5MB!')
        return false
      }
      if (file.name.indexOf(',') != -1) {
        this.$message.error('非法的文件名')
        return false
      }
    },
    beforeAvatarUpload4(file) {
      const isLt5M = file.size / 1024 / 1024 < 5
      if (!isLt5M) {
        this.$message.error('上传图片大小不能超过 5MB!')
        return false
      }
      if (file.name.indexOf(',') != -1) {
        this.$message.error('非法的文件名')
        return false
      }
    },
    editData(row, column) {
      row[column.property + 'isShow'] = true
      this.refreshTable()
      this.$nextTick(() => {
        this.$refs[column.property] && this.$refs[column.property].focus()
      })
    },
    refreshTable() {
      this.randomKey = Math.random()
      this.randomKey2 = Math.random()
    },
    alterData(row, column) {
      row[column.property + 'isShow'] = false
      this.refreshTable()
    },
    riskTypeChanged(val) {},
    deleteRow(index) {
      this.tableData.splice(index, 1)
    },
    deleteRow2(index) {
      this.tableData2.splice(index, 1)
    },
    addRows() {
      if (this.isFirstTableShow) {
        this.tableData.push({})
      } else {
        this.tableData2.push({})
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.attachmentUrl-block {
  display: inline-block;
  margin-right: 8px;

  img {
    width: 90px;
    height: 90px;
    cursor: pointer;
  }
}

.addRisk {
  overflow-x: hidden;

  .el-input__inner {
    padding-right: 45px !important;
  }
}

::v-deep .el-textarea .el-input__count {
  line-height: normal;
}

::v-deep .el-textarea__inner {
  height: 35px;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  appearance: none !important;
  margin: 0;
}

input[type="number"] {
  appearance: textfield;
}

.content_box {
  height: calc(100% - 82px);
  overflow-y: auto;
  margin: 15px;
  padding: 20px;
  background: #fff;
  display: flex;
  flex-direction: column;
}

.bottomBar {
  height: 52px;
  width: 100%;
  background-color: #fff;
  display: flex;
  justify-content: right;
  align-items: center;

  .bottomWrap {
    padding: 0 16px;
  }
}

.project-textarea {
  ::v-deep .el-textarea__inner {
    height: 96px !important;
    max-height: 96px !important;
    overflow-y: auto !important;
  }
}

.width_lengthen {
  width: 766px;

  /* padding-right: 45px; */
}

.el-textarea ::v-deep .el-input__count {
  // bottom: -6px !important;
}

.project-textarea textarea {
  height: 120px;
}

.sion-icon {
  cursor: pointer;
  padding-left: 3px;
  color: #5188fc;
}

.icon-disabled {
  cursor: not-allowed;
}

.form-inline {
  .el-input,
  .el-select,
  .el-cascader {
    width: 300px;
  }
}

::v-deep .mterial_file > .el-upload-list li {
  float: left;
}

::v-deep .mterial_file > .el-upload-list {
  position: absolute;
  top: 0;
  width: 500px;
  margin-left: 310px;
  max-height: 160px;
  overflow: auto;
}

::v-deep .mterial_file > .el-upload--picture-card {
  border: none;
  width: 300px;
}

::v-deep .mterial_file .el-upload .el-upload-dragger {
  width: 300px;
  height: 148px;
}

::v-deep .mterial_file .el-upload__text {
  position: absolute;
  left: 57px;
  color: #606266;
  font-size: 14px;
  text-align: center;
}

@media screen and (max-width: 1366px) {
  ::v-deep .el-upload-list--picture-card .el-upload-list__item {
    width: 90px;
    height: 90px;
  }
}

.risk {
  width: 100%;
  padding-left: 30px;

  .el-form-item__content {
    width: 95%;
  }

  .el-input__inner {
    padding-right: 15px !important;
  }

  .risk-analysis {
    .el-input {
      width: auto !important;
    }
  }

  .header {
    .title {
      font-size: 16px;
      padding-left: 0;
    }

    .add-btn {
      cursor: pointer;
      color: #409eff;
    }
  }

  ::v-deep .el-button--danger {
    background: #f78989;
    border-color: #f78989;
    color: #fff;
    min-width: 55px;
    height: 30px;
  }

  ::v-deep .el-button--danger:hover {
    background: #f78989;
    border-color: #f78989;
    color: #fff;
  }
}

.title {
  background: #fff;
  padding: 10px 15px;
  overflow: hidden;
  font-size: 20px;
  font-family: "FZLTZHK--GBK1-0";
  font-weight: 600;
  color: #606266;
}

.result ::v-deep .el-form-item__label::before {
  content: "*";
  color: #f56c6c;
  margin-right: 4px;
}

.detaillClass {
  display: inline-block;
  width: 300px;
}

::v-deep .el-upload-list__item {
  transition: none !important;
}
</style>
