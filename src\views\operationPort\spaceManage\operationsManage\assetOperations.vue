<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-06-06 18:13:06
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-10-11 14:29:05
 * @FilePath: \ihcrs_pc\src\views\operationPort\spaceManage\operationsManage\assetOperations.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="">
    <template v-for="(item, index) in dlayoutItems">
      <dash-item
        v-if="item.id == '1'"
        v-bind.sync="dlayoutItems[index]"
        :key="item.id"
        @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)">
        <ContentCard
          title="资产总览"
          :scrollbarHover="true"
          :cstyle="{ height: '100%' }"
          class="drag_class"
          :hasMoreOper="['edit']"
          @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')"
        >
          <div slot="content" style="height: 100%;">
            <div class="overview">
              <div class="overviewItemWarp">
                <div class="analysisItem" @click="goDeviceList(countData.totalAssets, '')">
                  <div class="statisticsTitle">资产总数</div>
                  <div class="countStatistics">
                    <span class="countBlod">
                      {{ countData.totalAssets }}
                      <span class="garyColor">件</span>
                    </span>
                    <img style="width: 20%;" src="../../../../assets/images/operationPort/waste-icon1.png" alt="">
                  </div>
                </div>
                <div class="analysisItem" @click="goDeviceList(countData.idleAssets, '2')">
                  <div class="statisticsTitle">闲置</div>
                  <div class="countStatistics">
                    <span class="countBlod">
                      {{ countData.idleAssets }}
                      <span class="garyColor">件</span>
                    </span>
                    <img style="width: 20%;" src="../../../../assets/images/operationPort/统计 闲置@2x.png" alt="">
                  </div>
                </div>
                <div class="analysisItem" @click="goDeviceList(countData.deactivationAsset, '4')">
                  <div class="statisticsTitle">停用</div>
                  <div class="countStatistics">
                    <span class="countBlod">
                      {{ countData.deactivationAsset }}
                      <span class="garyColor">件</span>
                    </span>
                    <img style="width: 20%;" src="../../../../assets/images/operationPort/waste-icon4.png" alt="">
                  </div>
                </div>
              </div>
              <div class="overviewItemWarp">
                <div class="analysisItem" @click="goDeviceList(countData.normalAssets, '3')">
                  <div class="statisticsTitle">正常</div>
                  <div class="countStatistics">
                    <span class="countBlod">
                      {{ countData.normalAssets }}
                      <span class="garyColor">件</span>
                    </span>
                    <img style="width: 20%;" src="../../../../assets/images/operationPort/统计 正常@2x.png" alt="">
                  </div>
                </div>
                <div class="analysisItem" @click="goDeviceList(countData.retiredAssets, '1')">
                  <div class="statisticsTitle">报废</div>
                  <div class="countStatistics">
                    <span class="countBlod">
                      {{ countData.retiredAssets }}
                      <span class="garyColor">件</span>
                    </span>
                    <img style="width: 20%;" src="../../../../assets/images/operationPort/统计 报废@2x.png" alt="">
                  </div>
                </div>
                <div class="analysisItem" @click="goDeviceList(countData.repairedAssets, '5')">
                  <div class="statisticsTitle">待维修</div>
                  <div class="countStatistics">
                    <span class="countBlod">
                      {{ countData.repairedAssets }}
                      <span class="garyColor">件</span>
                    </span>
                    <img style="width: 20%;" src="../../../../assets/images/operationPort/统计 待维修@2x.png" alt="">
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ContentCard>
      </dash-item>
      <dash-item
        v-if="item.id == '2'"
        v-bind.sync="dlayoutItems[index]"
        :key="item.id"
        @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)">
        <ContentCard
          title="工作日历"
          :scrollbarHover="true"
          :cstyle="{ height: '100%' }"
          class="drag_class"
          :hasMoreOper="['edit']"
          @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')"
        >
          <div slot="content" style="height: 100%;">
            <div class="role-content-left">
              <div class="header">
                <div style="width: 240px; display: flex; align-items: center;">
                  <div v-for="(item, index) in statusList" :key="index" style="font-size: 14px; margin-right: 20px;">
                    <span style="display: inline-block; width: 8px; height: 8px;" :class="item.color"></span>
                    {{ item.label }}
                  </div>
                </div>
                <div class="change_month" style="flex: 1;">
                  <i class="el-icon-arrow-left" @click="clickPrevious"></i>
                  <span>{{ monthData }}</span>
                  <i class="el-icon-arrow-right" @click="clickNext"></i>
                </div>
                <div class="today" @click="clickToday">
                  <span> 今天 </span>
                </div>
              </div>
              <el-calendar ref="uploadImgBtn" v-model="calendarDate">
                <template slot="dateCell" slot-scope="{ data }">
                  <div class="calendar_content">
                    <div style="text-align: right; margin-bottom: 10px;">
                      {{ data.day.split('-')[2] }}
                    </div>
                    <div v-for="(item, index) in tableTimeData" :key="index">
                      <!-- 设备 -->
                      <div v-if="data.day == item.dateStr && item.taskStatus != 3">
                        <div :class="item.taskStatus == '1' ? 'triangle noOk' : 'triangle isOK'"></div>
                        <div class="calNumber" @click="dataChange(data.day, '1')">
                          <span>设备巡检</span>
                          <span class="num">{{ item.inspectionSum }}</span>
                        </div>
                        <div class="calNumber1" @click="dataChange(data.day, '2')">
                          <span>设备保养</span>
                          <span class="num">{{ item.maintainSum }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
              </el-calendar>
            </div>
          </div>
        </ContentCard>
      </dash-item>
      <dash-item
        v-if="item.id == '3'"
        v-bind.sync="dlayoutItems[index]"
        :key="item.id"
        @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)">
        <ContentCard
          title="专业类别分析"
          :scrollbarHover="true"
          :cstyle="{ height: '100%' }"
          class="drag_class"
          :hasMoreOper="['edit']"
          @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')"
        >
          <div slot="content" style="height: 100%;">
            <div id="workOdertTrendEcharts"></div>
          </div>
        </ContentCard>
      </dash-item>
      <dash-item
        v-if="item.id == '4'"
        v-bind.sync="dlayoutItems[index]"
        :key="item.id"
        @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)">
        <ContentCard
          title="资产使用年限分析"
          :scrollbarHover="true"
          :cstyle="{ height: '100%' }"
          class="drag_class"
          :hasMoreOper="['edit']"
          @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')"
        >
          <div slot="content" style="height: 100%;">
            <div id="taskAnalysis"></div>
          </div>
        </ContentCard>
      </dash-item>
      <dash-item
        v-if="item.id == '5'"
        v-bind.sync="dlayoutItems[index]"
        :key="item.id"
        @resizeEnd="(val) => $emit('resizeEnd', val, componentData.componentRouter)">
        <ContentCard
          title="资产巡检分析"
          :scrollbarHover="true"
          :cstyle="{ height: '100%' }"
          class="drag_class"
          :hasMoreOper="['edit']"
          @more-oper-event="(val) => $emit('all-more-Oper', val, 'personalInfo')"
        >
          <div slot="content" style="height: 100%;">
            <div class="inspAnalysis">
              <div class="inspItem">
                <div class="inspCount">
                  <div style="width: 50%; cursor: pointer;" @click="goTaskList(deviceCountAnalysis.percentage, '2', 1)">
                    <span class="doInspIcon" style="background-color: #ff9435;"></span>完成率
                    <span class="doInspCount" style="color: #ff9435;">{{ deviceCountAnalysis.percentage }}</span>
                    <span style="color: #ccced3;">%</span>
                  </div>
                  <div style="width: 50%; cursor: pointer;" @click="goTaskList(deviceCountAnalysis.sum, '', 1)">
                    <span>今日巡检任务</span>
                    <span class="totalInspCount">{{ deviceCountAnalysis.sum }}</span>
                    <span style="color: #ccced3;">个</span>
                  </div>
                </div>
                <el-progress :stroke-width="10" color="#FF9435" define-back-color="#CCCED3" :show-text="false" stroke-linecap="square" :percentage="deviceCountAnalysis.percentage || 0"></el-progress>
              </div>
              <div class="inspItem">
                <div class="inspCount">
                  <div style="width: 50%; cursor: pointer;" @click="goTaskList(maintainCountAnalysis.percentage, '2', 2)">
                    <span class="doInspIcon" style="background-color: #3562db;"></span>完成率
                    <span class="doInspCount" style="color: #3562db;">{{ maintainCountAnalysis.percentage }}</span>
                    <span style="color: #ccced3;">%</span>
                  </div>
                  <div style="width: 50%; cursor: pointer;" @click="goTaskList(maintainCountAnalysis.sum, '0', 1)">
                    <span>今日保养任务</span>
                    <span class="totalInspCount">{{ maintainCountAnalysis.sum }}</span>
                    <span style="color: #ccced3;">个</span>
                  </div>
                </div>
                <el-progress :stroke-width="10" color="#3562DB" define-back-color="#CCCED3" :show-text="false" stroke-linecap="square" :percentage="maintainCountAnalysis.percentage || 0"></el-progress>
              </div>
            </div>
          </div>
        </ContentCard>
      </dash-item>
    </template>
  </div>
</template>
<script>
import * as echarts from 'echarts'
import { DashItem } from 'vue-responsive-dash'
import fecha from 'element-ui/src/utils/date' // element-ui中处理时间的工具类
export default {
  name: 'assetOperations',
  components: {
    DashItem
  },
  props: {
    dlayoutItems: {
      type: Array,
      default: () => []
    },
    componentData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      // dlayoutItems: [
      //   { id: '1', name: '资产总览', x: 0, y: 0, width: 8, height: 7, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
      //   { id: '2', name: '工作日历', x: 8, y: 0, width: 16, height: 7, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
      //   { id: '3', name: '专业类别分析', x: 0, y: 7, width: 8, height: 5, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
      //   { id: '4', name: '资产使用年限分析', x: 8, y: 7, width: 8, height: 5, dragAllowFrom: '.drag_class', resizable: true, draggable: true },
      //   { id: '5', name: '资产巡检分析', x: 16, y: 7, width: 8, height: 5, dragAllowFrom: '.drag_class', resizable: true, draggable: true }
      // ],
      countData: {},
      statusList: [
        {
          label: '已完成',
          color: 'bgc1'
        },
        {
          label: '未完成',
          color: 'bgc2'
        },
        {
          label: '当前日期',
          color: 'bgc3'
        }
      ],
      calendarDate: new Date(),
      tableTimeData: [],
      monthData: '',
      pagination: {
        pagination: 1,
        size: 15,
        total: 0
      },
      deviceCountAnalysis: {},
      maintainCountAnalysis: {},
      echartsDom: [
        'taskAnalysis',
        'workOdertTrendEcharts'
      ]
    }
  },
  created() {
    this.getAssetStatistics()
    this.changeMonth(this.calendarDate)
    this.getDataList()
    setTimeout(() => {
      this.getWorkOderTrend()
      this.getWorkOderType()
    }, 300)
    this.getRiskInspAnalysis('1')
    this.getRiskInspAnalysis('2')
  },
  mounted() {
    this.$nextTick(() => {
      // 点击前一个月
      let prevBtn = document.querySelector('.el-calendar__button-group .el-button-group>button:nth-child(1)')
      prevBtn.addEventListener('click', () => {
        this.changeMonth(this.calendarDate)
      })
      // 点击下一个月
      let nextBtn = document.querySelector('.el-calendar__button-group .el-button-group>button:nth-child(3)')
      nextBtn.addEventListener('click', () => {
        this.changeMonth(this.calendarDate)
      })
      // 点击今天
      let todayBtn = document.querySelector('.el-calendar__button-group .el-button-group>button:nth-child(2)')
      todayBtn.addEventListener('click', () => {
        this.changeMonth(this.calendarDate)
      })
    })
  },
  methods: {
    echartsResize() {
      let echartsDom = this.echartsDom
      this.$nextTick(() => {
        setTimeout(() => {
          echartsDom.forEach((item) => {
            echarts.init(document.getElementById(item)).resize()
          })
        }, 100)
      })
    },
    getAssetStatistics() {
      this.$api.getAssetOverview({regionCode: ''}).then((res) => {
        if (res.code == 200) {
          this.countData = res.data
        }
      })
    },
    clickPrevious() {
      this.$refs.uploadImgBtn[0].$children[0].$children[0].$el.click()
    },
    clickNext() {
      this.$refs.uploadImgBtn[0].$children[0].$children[2].$el.click()
    },
    clickToday() {
      this.$refs.uploadImgBtn[0].$children[0].$children[1].$el.click()
    },
    // 获取日历板上第一天
    getMondayDay(Month) {
      let now = new Date(Month)
      let day = now.getDay() || 7
      return fecha.format(new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1 - day), 'yyyy-MM-dd')
    },
    // 获取日历板上最后一天
    getLastSundayDay(Month) {
      let monday = new Date(this.getMondayDay(Month))
      let sunday = monday.setDate(monday.getDate() + 41)
      return fecha.format(new Date(sunday), 'yyyy-MM-dd')
    },
    // 改变月份
    changeMonth(date) {
      let month = (date.getMonth() + 1).toString().padStart(2, '0')
      let year = date.getFullYear()
      let oneday = year + '-' + month // 获取某月
      this.monthData = year + '年' + month + '月'
      let startTime = this.getMondayDay(oneday)
      let endTime = this.getLastSundayDay(oneday)
      this.tableTimeData = []
      let data = {
        startDate: startTime,
        endDate: endTime
      }
      this.$api.selectCalendarList(data).then((res) => {
        if (res.code == 200) {
          this.tableTimeData = res.data
        }
      })
    },
    dataChange(data, type, menuType) {
      this.$router.push({
        name: type != '3' ? 'calendarProgressDetail' : 'calProgressDetail',
        query: {
          type: type,
          menuType: menuType || '',
          taskStartTime: data,
          taskEndTime: data
        }
      })
    },
    getDataList() {
      let data = {
        systemIdentificationClassification: '1',
        pageNo: this.pagination.current,
        pageSize: this.pagination.size
      }
      this.$api.getTaskDynamics(data).then((res) => {
        this.tableLoading = true
        if (res.code == 200) {
          this.tableData = res.data.list
          this.pagination.total = parseInt(res.data.sum)
        }
        this.tableLoading = false
      })
    },
    getWorkOderTrend() {
      let params = {
        regionCode: ''
      }
      this.$api.getAssetClassification(params).then((res) => {
        if (res.code === '200') {
          const arr = res.data
          this.workOderTrendEchart(arr)
        }
      })
    },
    workOderTrendEchart(val) {
      const getchart = echarts.init(document.getElementById('workOdertTrendEcharts'))
      let arr = []
      val.forEach((item) => {
        let obj = {
          name: item.assetType,
          value: item.assetNumber
        }
        arr.push(obj)
      })
      const data = arr
      data.sort((a, b) => {
        return a.value - b.value
      })
      const nameList = Array.from(data, ({ name }) => name)
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'none'
          }
        },
        grid: {
          top: '4%',
          left: '4%',
          right: '5%',
          bottom: '20%',
          containLabel: true
        },
        dataZoom: [
          {
            fillerColor: '#BBC3CE',
            backgroundColor: '#fff',
            height: 10,
            type: 'slider',
            bottom: 10,
            textStyle: {
              color: '#000'
            },
            start: 0,
            end: 100
          },
          {
            type: 'inside', // 支持内部鼠标滚动平移
            start: 0,
            end: 100,
            zoomOnMouseWheel: false, // 关闭滚轮缩放
            moveOnMouseWheel: true, // 开启滚轮平移
            moveOnMouseMove: true // 鼠标移动能触发数据窗口平移
          }
        ],
        xAxis: {
          type: 'category',
          data: nameList,
          axisLabel: {
            rotate: -40,
            formatter: function (val) {
              return val
            }
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        yAxis: [
          {
            type: 'value',
            axisLine: {
              show: false
            }
          }
        ],
        series: [
          {
            type: 'bar',
            barWidth: 10,
            data: data,
            itemStyle: {
              color: '#3562db'
            }
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 资产使用年限
    getWorkOderType() {
      let params = {
        regionCode: ''
      }
      this.$api.getAssetsServiceLife(params).then((res) => {
        if (res.code == '200') {
          this.echartsData = res.data.array
          this.$nextTick(() => {
            this.setEcharts()
          })
        }
      })
    },
    setEcharts() {
      let myChart = echarts.init(document.getElementById('taskAnalysis'))
      let pieData = []
      this.echartsData.forEach((i) => {
        const item = {}
        item.value = i.value || 0
        item.name = i.name
        item.completion = i.proportion
        item.label = {}
        pieData.push(item)
      })
      pieData.sort((a, b) => a.value - b.name)
      myChart.setOption({
        legend: {
          type: 'scroll',
          orient: 'vertical',
          icon: 'rect', // 形状
          itemWidth: 8, // 宽
          itemHeight: 8, // 高
          top: '30%',
          right: '7%',
          selected: {},
          formatter: function (name) {
            var oa = pieData
            for (var i = 0; i < pieData.length; i++) {
              if (name === oa[i].name) {
                return ' ' + name + '(' + oa[i].value + '件' + ')' + '' + oa[i].completion + ''
              }
            }
          }
        },
        color: ['#3562DB', '#FF9435', '#FA403C'],
        series: [
          {
            name: '',
            type: 'pie',
            radius: ['50%', '75%'],
            center: ['40%', '50%'],
            avoidLabelOverlap: false,
            label: {
              normal: {
                show: false,
                position: 'center'
              }
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 14,
                color: '#989898',
                width: 135,
                formatter(params) {
                  let text = params.data.name
                  let value_format = params.data.value
                  return (text = `{time|${text}}\n ${value_format}件`)
                },
                rich: {
                  time: {
                    fontSize: 20,
                    color: '#666',
                    lineHeight: 20
                  }
                }
              }
            },
            labelLine: {
              show: false
            },
            data: pieData
          }
        ]
      })
    },
    getRiskInspAnalysis(code) {
      this.$api.onePersonTaskQuantity({dateType: 'day', systemCode: code}).then(res => {
        if (res.code == 200) {
          if (code == '1') {
            this.deviceCountAnalysis = res.data.list[0]
          } else {
            this.maintainCountAnalysis = res.data.list[0]
          }
        }
      })
    },
    goTaskList(sum, type, systemCode) {
      console.log(sum)
      if (sum < 1) {
        this.$message.warning('暂无数据')
      } else {
        this.$router.push({
          name: 'taskList',
          query: {
            systemCode,
            type,
            from: 'asset'
          }
        })
      }
    },
    goDeviceList(sum, type) {
      this.$router.push({
        name: 'deviceLsit',
        query: {
          type
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.overview {
  height: 100%;
  display: flex;
  justify-content: space-between;

  .overviewItemWarp {
    width: 50%;

    .analysisItem {
      cursor: pointer;
      margin: 8px;
      padding: 24px;
      width: calc(100% - 16px);
      height: 30%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .statisticsTitle {
        color: #121f3e;
      }

      .countStatistics {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .countBlod {
          color: #121f3e;
          font-weight: bold;

          .garyColor {
            color: #ccced3;
            font-weight: normal;
          }
        }
      }
    }
  }
}

.role-content-left {
  border-radius: 6px;
  height: 100%;
  background-color: #fff;
  overflow: auto;

  .header {
    position: relative;
    display: flex;
    align-items: center;
    padding: 12px 20px 10px;

    .change_month {
      text-align: center;
      height: 40px;
      line-height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;

      i {
        display: inline-block;
        width: 24px;
        height: 24px;
        line-height: 24px;
        margin: auto 0;
        font-size: 14px;
        text-align: center;
        cursor: pointer;
        background: #f1f6ff;
        border-radius: 12px;

        &:hover {
          color: #fff;
          background-color: #3562db;
        }
      }

      span {
        margin: auto 24px;
        font-size: 20px;
        color: #4e8cda;
      }
    }

    .today {
      width: 160px;
      text-align: right;
      font-size: 14px;
      cursor: pointer;

      span {
        display: inline-block;
        width: 48px;
        height: 30px;
        border-radius: 4px;
        background-color: rgb(53 98 219 / 20%);
        text-align: center;
        line-height: 30px;
        color: #3562db;
      }
    }
  }

  .calendar_content {
    width: 100%;
    height: 100%;
    padding: 8px;
    position: relative;

    .triangle {
      position: absolute;
      top: 0;
      left: 0;
      width: 0;
      height: 0;
      border-right: 24px solid transparent;
    }

    .noOk {
      border-top: 24px solid #fa403c;
    }

    .isOK {
      border-top: 24px solid #08cb83;
    }

    .calNumber,
    .calNumber1 {
      width: 100%;
      height: 30px;
      margin: 0 auto;
      font-size: 14px;
      background-color: #3562db;
      border-radius: 4px;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;

      .num {
        margin-left: 5px;
        width: 30px;
        height: 28px;
        line-height: 28px;
        text-align: center;
        border-radius: 28px;
        background-color: rgb(255 255 255 / 20%);
      }
    }

    .calNumber1 {
      margin-top: 5px;
      background-color: #ff9435;
    }
  }
}

::v-deep .el-calendar-table thead th::before {
  content: "周";
}

::v-deep .el-calendar-table thead th {
  font-weight: 500;
  font-size: 14px;
}

::v-deep .el-calendar__body {
  width: 100%;
  padding: 0 20px;
  height: calc(100% - 60px);
}

::v-deep .el-calendar-table .el-calendar-day {
  height: 114px;
  padding: 0;
}

.bgc1 {
  background-color: #08cb83;
}

.bgc2 {
  background-color: #fa403c;
}

.bgc3 {
  background-color: #ccced3;
}

::v-deep .el-calendar__header {
  padding: 0;
}

::v-deep .el-calendar__header .el-calendar__title {
  display: none;
}

::v-deep .el-calendar__button-group {
  display: none;
}

#taskAnalysis,
#workOdertTrendEcharts {
  width: 100%;
  height: 95%;
}

.inspAnalysis {
  display: flex;
  flex-direction: column;
  height: 100%;

  .inspItem {
    height: 50%;
    padding: 32px 24px;

    .inspCount {
      margin: 20px 0;
      display: flex;
      align-items: center;

      .doInspIcon {
        display: inline-block;
        width: 8px;
        height: 8px;
        margin-right: 6px;
      }

      .doInspCount {
        margin: 0 8px;
        font-size: 22px;
        font-weight: bold;
      }

      .totalInspCount {
        margin: 0 8px;
        font-size: 22px;
        color: #121f3e;
        font-weight: bold;
      }
    }
  }
}

:deep(.el-progress-bar) {
  .el-progress-bar__outer {
    border-radius: 0;

    .el-progress-bar__inner {
      border-radius: 0;
    }
  }
}
</style>
