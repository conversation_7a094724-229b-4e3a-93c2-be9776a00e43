<template>
  <PageContainer>
    <div slot="content" class="monitor-setting">
      <div class="monitor-setting-left">
        <ContentCard title="区间类型配置" :cstyle="{ height: '100%' }">
          <span slot="title-right" class="select-servive" @click="handleTeamEvent('add', 1, {})"><i class="el-icon-plus"></i>分组</span>
          <el-tree
            slot="content"
            ref="teamTree"
            class="team-tree"
            :check-strictly="true"
            :data="teamTreeData"
            :props="defaultProps"
            node-key="id"
            :expand-on-click-node="false"
            :highlight-current="true"
            :render-content="renderContent"
            :default-expanded-keys="expandedTeam"
            @node-click="handleTeamClick"
          ></el-tree>
        </ContentCard>
      </div>
      <div class="monitor-setting-right">
        <div class="right-heade">
          <el-input v-model="searchFrom.name" placeholder="区间监测名称" suffix-icon="el-icon-search" clearable />
          <el-input v-model="searchFrom.stateName" placeholder="区间状态名称" suffix-icon="el-icon-search" clearable />
          <div style="display: inline-block;">
            <el-button type="primary" plain @click="resetForm">重置</el-button>
            <el-button type="primary" @click="searchForm">查询</el-button>
          </div>
        </div>
        <div class="right-content">
          <div class="btns-group">
            <div>
              <el-button type="primary" style="margin-right: 10px;" @click="handleMonitorEvent('add', {})">添加</el-button>
              <el-button type="primary" plain :disabled="!multipleSelection.length" @click="delMonitor">删除</el-button>
            </div>
          </div>
          <div class="table-content">
            <TablePage
              ref="tablePage"
              v-loading="tableLoading"
              :tableColumn="tableColumn"
              :data="tableData"
              border
              height="100%"
              :showPage="false"
              :pageData="pageData"
              :span-method="objectSpanMethod"
              @pagination="paginationChange"
              @selection-change="handleSelectionChange"
            >
            </TablePage>
          </div>
        </div>
      </div>
      <addTreeNode
        ref="addNode"
        :dialogVisible="dialogVisible"
        :checkedData="handleEventData"
        :treeLevel="treeLevel"
        :monitorType="projectCode"
        @closeDialog="closeDialog"
      ></addTreeNode>
    </div>
  </PageContainer>
</template>

<script lang="jsx">
import addTreeNode from './components/addTreeNode.vue'
import { monitorTypeList } from '@/util/dict.js'
export default {
  name: 'monitorSetting',
  components: {
    addTreeNode
  },
  props: {
    projectCode: {
      type: String,
      default: ''
    },
    monitorData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      defaultProps: {
        children: 'children',
        label: 'name',
        value: 'code'
      },
      teamTreeData: [],
      expandedTeam: [],
      checkedTeamData: {},
      searchFrom: {
        name: '',
        stateName: ''
      },
      tableLoading: false,
      tableData: [],
      tableColumn: [
        {
          prop: 'sensorName',
          type: 'selection',
          align: 'center',
          width: '50'
        },
        {
          prop: 'sensorName',
          label: '序号',
          type: 'index',
          width: '80',
          formatter: (scope) => {
            return scope.$index + 1
          }
        },
        {
          prop: 'name',
          label: '区间监测名称'
        },
        {
          prop: 'stateName',
          label: '区间状态'
        },
        {
          prop: 'maxAndMinStr',
          label: '区间值'
        },
        {
          prop: 'colour',
          label: '色值',
          render: (h, row) => {
            return (
              <span style={{ color: row.row.colour }}>{ row.row.colour }</span>
            )
          }
        },
        {
          prop: 'sectionMenuName',
          label: '归属分组'
        },
        {
          prop: 'sensorName',
          label: '操作',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span class="operationBtn-span" onClick={() => this.handleMonitorEvent('edit', row.row)}>
                  修改
                </span>
                <span class="operationBtn-span" onClick={() => this.handleMonitorEvent('del', row.row, false)}>
                  删除
                </span>
              </div>
            )
          }
        }
      ],
      pageData: {
        page: 1,
        pageSize: 15,
        total: 0
      },
      isShowTooltip: false,
      // monitorData: monitorTypeList.find(item => item.projectCode == this.projectCode), // 当前监测类型对应字典数据
      multipleSelection: [],
      spanArr: [],
      handleEventData: {},
      dialogVisible: false, // tree节点弹窗
      treeLevel: 1, // tree节点弹窗
      groupConfigList: [] // 分组配置列表
    }
  },
  computed: {},
  activated() {
    this.getTableData()
  },
  created() {
    this.getTreelist()
    this.getDataServerList()
  },
  mounted() {
    // type: 'security' 时为 安防菜单集合，隐藏上游实体 显示数据主机
    // this.tableColumn.find(item => item.prop === 'upstreamEntityName').hasJudge = this.monitorData?.type !== 'security'
    // this.tableColumn.find(item => item.prop === 'dataServerName').hasJudge = this.monitorData?.type === 'security'
  },
  methods: {
    // 获取table列表
    getTableData() {
      // 未获取到实体菜单时 不请求列表
      if (!this.checkedTeamData?.id ?? false) {
        return
      }
      let data = {
        menuCode: this.checkedTeamData.code,
        sectionMenuId: this.checkedTeamData.id,
        // projectCode: this.projectCode,
        // pageSize: this.pageData.pageSize,
        // page: this.pageData.page,
        ...this.searchFrom
      }
      this.tableLoading = true
      this.tableData = []
      this.$api.selectSection(data).then((res) => {
        this.tableLoading = false
        if (res.code == 200) {
          // if (!res.data.list.length && this.pageData.page > 1) {
          //   this.pageData.page--
          //   this.getTableData()
          // }
          res.data.forEach(item => {
            if (item.list.length == 0) {
              this.tableData.push({
                ...item
              })
            } else {
              item.list.forEach(ele => {
                this.tableData.push({
                  ...ele,
                  ...item,
                  list: null
                })
              })
            }
          })
          // this.pageData.total = parseInt(res.data.totalCount)
        } else if (res.message) {
          this.$message.error(res.message)
        }
      })
    },
    // 获取分组树列表
    getTreelist() {
      this.$api
        .sectionMenuList({
          // projectId: this.projectCode,
          // sectionIs: 1
        })
        .then((res) => {
          this.treeLoading = false
          // this.treeList = res.data
          // let list = this.$tools.transData(res.data, 'code', 'parentId', 'children')
          this.teamTreeData = res.data
          if (this.teamTreeData.length) {
            this.checkedTeamData = this.teamTreeData[0]
            // this.geTreeName(this.treeData[0].parentIds, this.treeData[0].name)
            this.$nextTick(() => {
              this.$refs.teamTree?.setCurrentKey(this.teamTreeData[0])
            })
            // 获取列表
            this.getTableData()
          } else {
            this.tableData = []
            this.pageData.total = 0
            this.tableLoading = false
          }
        })
    },
    // 监测实体列合并
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      let arr = [0, 1, 2]
      if (arr.indexOf(columnIndex) != -1) {
        // 判断当前列是否为第一列
        if (rowIndex === 0 || row.name !== this.tableData[rowIndex - 1].name) {
          // 判断当前行是否为第一行或者与上一行的name属性不相同
          const rowspan = this.tableData.filter(
            item => item.name === row.name
          ).length
          // 计算需要合并的行数
          return {
            rowspan,
            colspan: 1
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
    },
    // 树状图点击
    handleTeamClick(data) {
      this.pageData.page = 1
      this.checkedTeamData = data
      this.$refs.teamTree.setCurrentKey(this.checkedTeamData)
      this.getTableData()
    },
    renderContent(h, { node, data, store }) {
      const notTeamNodeRole = data.notTeamNodeRole?.split(',') ?? []
      // const nowProjectName = monitorTypeList.find(item => item.projectCode == this.projectCode).projectName
      // 空调监测为非树形结构
      return (
        <span class="custom-tree-node">
          <el-tooltip class="item" effect="dark" content={ node.label } placement="top-start" disabled={ !this.isShowTooltip }>
            <span onMouseenter={(e) => { this.visibilityChange(e) }}>{node.label}</span>
          </el-tooltip>
          <span>
            {/* {!notTeamNodeRole.includes('add') && <i class="el-icon-plus" onClick={(e) => { this.handleTeamEvent('add', 2, data), e.stopPropagation() }}></i>} */}
            {!notTeamNodeRole.includes('edit') && <i class="el-icon-edit" onClick={(e) => { this.handleTeamEvent('edit', null, data, node), e.stopPropagation() }}></i>}
            {!notTeamNodeRole.includes('del') && <i class="el-icon-delete" onClick={(e) => { this.handleTeamEvent('del', null, data, node), e.stopPropagation() }}></i>}
          </span>
        </span>
      )
    },
    visibilityChange(e) {
      const item = e.target
      const itemWidth = item.offsetWidth
      const itemScrollWidth = item.scrollWidth
      if (itemWidth < itemScrollWidth) {
        this.isShowTooltip = true
      } else {
        this.isShowTooltip = false
      }
    },
    // 重置查询表单
    resetForm() {
      this.searchFrom = {
        name: '',
        stateName: ''
      }
      this.searchForm()
    },
    // 查询
    searchForm() {
      this.pageData.page = 1
      this.getTableData()
    },
    // 获取多选列表
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 分页事件
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getTableData()
    },
    // 删除
    delMonitor() {
      const row = {
        sensorCode: Array.from(this.multipleSelection.map((item) => item.id)).toString()
      }
      this.handleMonitorEvent('del', row, true)
    },

    // 获取数据主机
    getDataServerList() {
      this.$api.getDataServerList({}).then((res) => {
        if (res.code == '200') {
          sessionStorage.setItem('dataServer', JSON.stringify(res.data))
        }
      })
    },
    // 监测项事件
    handleMonitorEvent(type, row, isMultipleChoice) {
      if (type === 'del') {
        let content = isMultipleChoice ? '确定要删除该批区间配置吗？' : '确定要删除区间配置吗？'
        let title = isMultipleChoice ? '批量删除' : '删除'
        this.$confirm(content, title, {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 确定删除回调的内容
          if (isMultipleChoice) {
            this.$api
              .deleteSectionEntity({
                id: [...new Set(row.sensorCode.split(','))].toString()
              }, { 'operation-type': 3 })
              .then((res) => {
                if (res.code == 200) {
                  this.$message.success(res.message)
                  this.getTableData()
                }
              })
          } else {
            this.$api
              .deleteSection({
                id: row.id,
                order: row.order
              }, { 'operation-type': 3, 'operation-id': row.id, 'operation-name': row.name })
              .then((res) => {
                if (res.code == 200) {
                  this.$message.success(res.message)
                  this.getTableData()
                }
              })
          }
        })
        return
      }
      if (type == 'add' && (!this.checkedTeamData.id || !this.teamTreeData.length)) {
        return this.$message.warning('请先添加分组')
      }
      this.$router.push({
        path: '/powSystem/' + (this.monitorData?.formPath),
        query: {
          type: type,
          entityMenuCode: this.checkedTeamData.id,
          id: row.id
        }
      })
    },
    // 分组事件
    handleTeamEvent(type, addType, data, node) {
      if (data?.level >= 5 && type == 'add') return this.$message.error('最多支持添加4级子分组')
      if (type == 'add') {
        Object.assign(this.handleEventData, {
          ...data,
          state: 'add'
        })
        this.treeLevel = addType // 1同级 2下级
        this.dialogVisible = true
      } else if (type == 'edit') {
        Object.assign(this.handleEventData, {
          ...data,
          state: 'edit'
        })
        this.dialogVisible = true
        this.$refs.addNode.echoData()
      } else if (type == 'del') {
        this.$confirm('确定删除?', '消息', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.$api
              .sectionMenuDelete({
                id: data.id
              }, { 'operation-type': 3, 'operation-id': data.id, 'operation-name': data.name })
              .then((res) => {
                if (res.code == 200) {
                  this.$message.success(res.message)
                  this.getTreelist()
                } else {
                  this.$message.error(res.message)
                }
              })
          })
          .catch(() => {})
      }
    },
    closeDialog(refresh) {
      refresh && this.getTreelist()
      this.dialogVisible = false
    },
    // 选择下拉树 数据
    handleNodeClick(data, node, obj, row) {
      const params = {
        imsCodes: row.sensorCode,
        iemCode: data.code
      }
      this.updataListBelongGroup(params, 'once')
    },
    // 更新实体所属分组
    updataListBelongGroup(params, type) {
      this.$api.updateSurveyGroup(params).then(res => {
        if (res.code === '200') {
          this.$message({
            message: '更改分组成功',
            type: 'success'
          })
          if (type === 'lots') {
            // 关闭tooltip
            this.$refs.tooltip.showPopper = false
          }
          this.getTreelist()
        } else {
          this.$message.warning(res.message)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.monitor-setting {
  height: 100%;
  display: flex;

  .monitor-setting-left {
    width: 246px;
    height: 100%;
    // padding: 8px 16px 16px 16px;
    background: #fff;
    border-radius: 4px;

    ::v-deep .box-card {
      .card-title {
        position: relative;

        .select-servive {
          position: absolute;
          right: 0;
          top: 2px;
          cursor: pointer;
          font-family: PingFangSC-Regular, "PingFang SC";
          font-size: 14px;
          color: $color-primary;
          font-weight: 600;
          padding-left: 15px;

          i {
            font-weight: 600;
            font-size: 13px;
            margin-right: 2px;
          }
        }
      }

      .card-body {
        border-top: 1px solid #dcdfe6;
        padding-top: 10px;

        .custom-tree-node {
          flex: 1;
          display: flex;
          justify-content: space-between;
          padding-right: 10px;

          & > span {
            &:first-child {
              flex: 1;
              display: block;
              align-items: center;
              text-overflow: ellipsis;
              white-space: nowrap;
              overflow: hidden;
              color: #121f3e;
              font-size: 14px;
              font-family: "PingFang SC-Regular", "PingFang SC";
              // .el-tree-node__expand-icon {
              //   margin-right: 10px;
              // }
            }

            &:last-child {
              display: flex;
              align-items: center;

              i {
                margin-left: 6px;
                cursor: pointer;
                color: #3562db;
              }
            }
          }
        }

        .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
          background-color: #d9e1f8;
        }
      }
    }

    .team-tree {
      ::v-deep .custom-tree-node {
        overflow: hidden;
      }
    }
  }

  .monitor-setting-right {
    height: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;

    .right-heade {
      padding: 0 200px 10px 10px !important;
      margin-left: 16px;
      background: #fff;
      border-radius: 4px;

      ::v-deep .el-input {
        width: 200px;
      }

      & > div {
        margin-right: 10px;
        margin-top: 10px;
      }
    }

    .right-content {
      margin-left: 16px;
      margin-top: 16px;
      background: #fff;
      padding: 10px;
      border-radius: 4px;
      flex: 1;
      overflow: hidden;

      .btns-group {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;

        & > div {
          display: flex;
        }
      }

      .table-content {
        height: calc(100% - 45px);

        ::v-deep .el-table .el-table__body-wrapper td.el-table__cell {
          border-right: 1px solid #ededf5;
        }
      }
    }
  }
}
</style>
<style lang="scss">
.monitor-setting {
  .operationBtn-span {
    margin-right: 10px;
    color: #3562db;
  }

  .monitor-select {
    width: auto;
  }
}

.mut-tooltip {
  border: none !important;
  padding: 0 !important;

  .group-config-type {
    width: 180px;
    max-height: 300px;
    overflow-y: scroll;
    border-radius: 4px;
    padding: 5px;
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 30%);

    .el-tree {
      .el-tree-node {
        margin: 3px 0;
      }

      .el-tree-node__label {
        font-size: 14px;
        color: #121f3e;
        font-family: "PingFang SC-Regular", "PingFang SC";
      }
      // color: $color-primary;
      // padding: 10px 25px;
      // font-size: 14px;
    }
  }

  .popper__arrow {
    border-width: 4px;
  }
}
</style>
