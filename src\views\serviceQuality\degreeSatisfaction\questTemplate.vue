<template>
  <PageContainer>
    <div slot="content" class="whole">
      <div class="left">
        <ContentCard title="问卷类型">
          <div slot="content" class="footer-role">
            <el-tree ref="tree" :data="treeData" highlight-current node-key="id" :current-node-key="treeId" :props="Props" @node-click="treexz"> </el-tree>
          </div>
        </ContentCard>
      </div>
      <div class="right">
        <div class="right_t" style="display: flex; justify-content: space-between">
          <div style="display: flex">
            <el-input v-model.trim="templateName" placeholder="请输入模板名称" class="ipt"></el-input>
            <el-button type="primary" plain @click="resetForm">重置</el-button>
            <el-button type="primary" @click="searchForm">查询</el-button>
          </div>
          <el-button type="primary" icon="el-icon-plus" @click="newAdd">新增</el-button>
        </div>
        <div slot="content" style="height: 100%" class="right_b">
          <div class="contentTable">
            <div class="contentTable-main table-content">
              <el-table v-loading="loading" :data="tableData" :height="tableHeight" border stripe>
                <el-table-column prop="index" label="序号" align="center" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span>{{ (currentPage - 1) * pageSize + scope.$index + 1 }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="name" label="模板名称" align="center" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="type" label="问卷类型" align="center" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="templateDesc" label="描述" align="center" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="userName" label="添加人" align="center" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="createTime" label="更新时间" align="center" show-overflow-tooltip> </el-table-column>
                <el-table-column prop="" label="操作" align="center" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span style="cursor: pointer; color: red; padding: 0 5px" @click="dele(scope.row)">删除</span>
                    <span style="cursor: pointer; padding: 0 5px; color: #3562db" @click="edit(scope.row)">编辑</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <div class="contentTable-footer">
            <el-pagination
              style="margin-top: 3px"
              :current-page="currentPage"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              :page-size="pageSize"
              :page-sizes="[15, 30, 50]"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            ></el-pagination>
          </div>
        </div>
      </div>
      <el-dialog title="新建问卷" :visible.sync="dialogVisible" width="30%" :before-close="handleClose" custom-class="model-dialog">
        <div class="outermost" style="background-color: #fff">
          <el-form ref="ruleForm" :model="ruleForm" :rules="rules">
            <el-form-item label="模板名称" label-width="80px" prop="templateName">
              <el-input v-model.trim="ruleForm.templateName" placeholder="请输入模板名称" class="ipt"></el-input>
            </el-form-item>
            <!-- <el-form-item label="问卷类别" label-width="80px" prop="questType"> -->
            <!-- <el-select v-model="ruleForm.questType" placeholder="请选择模板类别">
              <el-option v-for="item in treeData" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select> -->
            <!-- <el-input v-model="ruleForm.questType" class="ipt"></el-input>
          </el-form-item> -->
            <el-form-item label="描述" label-width="80px">
              <el-input
                v-model.trim="ruleForm.describe"
                style="width: 80%"
                placeholder="请输入描述"
                type="textarea"
                maxlength="500"
                show-word-limit
                :autosize="{ minRows: 4, maxRows: 4 }"
              ></el-input>
            </el-form-item>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="confirm('ruleForm')">确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </PageContainer>
</template>

<script>
import tableListMixin from '@/mixins/tableListMixin.js'

export default {
  name: 'questTemplate',
  mixins: [tableListMixin],
  data() {
    return {
      pageSize: 15,
      currentPage: 1,
      total: 0,
      ruleForm: {
        templateName: '',
        questType: '',
        describe: ''
      },
      treeId: '',
      rules: {
        templateName: [{ required: true, message: '请输入模板名称', trigger: 'change' }]
        // questType: [{ required: true, message: '请选择模板类别', trigger: 'change' }]
      },
      dialogVisible: false,
      templateName: '',
      loading: false,
      tableData: [],
      treeData: [],
      Props: {
        value: 'id',
        label: function (data, node) {
          return `${data.dictName}  (${data.total})`
        },
        children: 'children'
      }
    }
  },
  created() {
    this.getTreeList()
  },
  methods: {
    treexz(val) {
      this.treeId = val.id
      this.tableList()
    },
    getTreeList() {
      this.$api.queryDictTypeTree().then((res) => {
        if (res.status == 200) {
          this.treeData = res.data
          this.treeData.forEach((item) => {
            if (!item.total) {
              item.total = 0
            }
          })
          this.treeId = this.treeId == '' ? res.data[0].id : this.treeId
          this.$nextTick(() => {
            this.$refs.tree.setCurrentKey(this.treeId)
          })
          this.tableList()
        }
      })
    },
    tableList() {
      let params = {
        userId: this.$store.state.user.userInfo.userId,
        userName: this.$store.state.user.userInfo.user.staffName,
        pageNum: this.currentPage,
        pageSize: this.pageSize,
        name: this.templateName,
        type: this.treeId,
        isTemplate: '1'
      }
      this.$api.listPvq(params).then((res) => {
        if (res.status == 200) {
          res.data.list.forEach((item) => {
            this.treeData.forEach((item2) => {
              if (item.type == item2.id) {
                item.type = item2.dictName
              }
            })
          })
          this.tableData = res.data.list
          this.total = res.data.total
        }
      })
    },
    confirm(formName) {
      let params = {
        userId: this.$store.state.user.userInfo.userId,
        userName: this.$store.state.user.userInfo.user.staffName,
        questionName: this.ruleForm.templateName, // 问卷名称
        questionTypeCode: this.treeId, // 问卷类型标识
        questionDesc: this.ruleForm.describe, // 描述
        templateRemark: '1' // 1是模板0不是模板
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$api.paper(params, { 'operation-type': 1 }).then((res) => {
            if (res.status == 200) {
              this.$message({
                message: '创建问卷模板成功',
                type: 'success'
              })
              this.ruleForm.templateName = ''
              this.ruleForm.questType = ''
              this.ruleForm.describe = ''
              this.tableList()
              this.getTreeList()
              this.dialogVisible = false
            } else {
              this.$message.error(res.message)
            }
          })
        } else {
          return false
        }
      })
    },
    handleClose() {
      this.ruleForm.templateName = ''
      this.ruleForm.questType = ''
      this.ruleForm.describe = ''
      this.dialogVisible = false
      this.$nextTick(() => {
        this.$refs['ruleForm'].clearValidate()
      })
    },
    // 查询
    searchForm() {
      this.currentPage = 1
      this.tableList()
    },
    // 重置
    resetForm() {
      this.templateName = ''
      this.currentPage = 1
      this.tableList()
    },
    // 新增
    newAdd() {
      this.dialogVisible = true
    },
    edit(row) {
      localStorage.setItem('questId', row.id)
      localStorage.setItem('questionnaireType', JSON.stringify(this.treeData))
      this.$router.push({
        name: 'newQuestionnaire',
        query: {
          id: row.id
        }
      })
    },
    // 删除
    dele(row) {
      this.$confirm('删除后将无法恢复，是否确定删除?', '信息提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$api.deletePvq({ questionId: row.id }, { 'operation-type': 3, 'operation-name': row.name, 'operation-id': row.id }).then((res) => {
            if (res.status == 200) {
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
              this.tableList()
              this.getTreeList()
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.tableList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.tableList()
    }
  }
}
</script>

<style lang="scss" scoped>
.whole {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;

  .left {
    width: 250px;
    height: 100%;
    background-color: #fff;
    border-radius: 5px;
    padding-right: 5px;
    padding-left: 10px;
    overflow: hidden;
  }

  .right {
    width: calc(100% - 260px);
    // background-color: #fff;
    height: 100%;
    border-radius: 10px;
    // padding: 10px;
    .right_t {
      border-radius: 5px;
      padding: 10px;
      background-color: #fff;
    }

    .right_b {
      border-radius: 5px;
      margin-top: 10px;
      padding: 10px;
      background-color: #fff;
      height: calc(100% - 62px);
    }
  }

  .ipt {
    width: 200px;
    margin-right: 10px;
  }
}

::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: linear-gradient(to right, #d9e1f8, #fff);
  font-weight: bold;
}

.outermost {
  width: 100%;
  max-height: 600px;
  border: 1px solid #eee;
  padding: 10px;
}

.contentTable {
  height: calc(100% - 90px);
  background: #fff;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  overflow: auto;

  .contentTable-main {
    flex: 1;
    overflow: auto;
  }

  .contentTable-footer {
    padding: 10px 0 0;
  }

  .alarmLevel {
    padding: 3px 6px;
    border-radius: 4px;
    color: #fff;
    line-height: 14px;
  }

  .alarmStatus {
    position: relative;
    display: inline-block;
    padding-left: 12px;

    .alarmStatusIcon {
      display: inline-block;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 8px;
      height: 8px;
      border-radius: 100%;
    }
  }

  .collectIcon {
    font-size: 16px;
    margin-right: 4px;
  }
}
</style>
