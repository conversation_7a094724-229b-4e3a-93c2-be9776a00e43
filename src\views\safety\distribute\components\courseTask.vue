<template>
  <div class="heard">
    <div class="course_title">课程任务</div>
    <div class="course_content">
      <div class="course_nav" v-for="(item, index) in courseTaskList" :key="index">
        <div class="courer_name">
          <img src="../../../../assets/images/icon_course.png" alt="">
          <div>{{ item.courseName }}</div>
        </div>
        <span class="courseType">课程类型：{{ item.subjectName?item.subjectName:newSubJectName }}</span>
        <span>课时数：<span>{{ item.periodCount }}</span> 课时</span>
        <span>创建人：{{ item.createName }}</span>
        <div class="operation">
          <i class="el-icon-delete Iconx" @click="deleteCourse(item, index)"></i>
        </div>
      </div>
    </div>
    <div>
      <el-button type="primary" @click="addcourse">添加课程</el-button>
    </div>
    <el-drawer :visible.sync="drawer" :with-header="true" size="65%" :show-close="true">
      <div slot="title" class="coursrDrawer">选择课程</div>
      <div class="deawer_conter">
        <div class="deawer_heard">
          <el-input v-model="formInline.courseName" placeholder="请输入课程名称"
            style="width: 200px; margin-right:10px;"></el-input>
          <el-input v-model="formInline.courseTeacher" placeholder="请输入创建人姓名"
            style="width: 200px; margin-right:10px;"></el-input>
          <el-cascader v-model="formInline.subjectId" clearable class="sino_sdcp_input mr15"
            style="width: 200px;margin-right:10px;" :options="subjectList" :props="props"
            placeholder="请选择类型"></el-cascader>
          <el-date-picker style="width: 200px; margin-right:10px;" v-model="timeLine" type="daterange"
            value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
          </el-date-picker>
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="search">查询</el-button>
        </div>
        <div class="table_conter">
          <el-table ref="multipleTable" :data="tableData" tooltip-effect="dark" style="width: 100%"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="60" fixed="left" align="center"></el-table-column>
            <el-table-column label="课程封面图" width="120" align="center">
              <template slot-scope="scope">
                <img class="imgClass" :src="scope.row.coverUrl" alt="">
              </template>
            </el-table-column>
            <el-table-column prop="courseName" label="课程名称" width="120" show-overflow-tooltip
              align="center"></el-table-column>
            <el-table-column prop="subjectName" label="课程类型" show-overflow-tooltip align="center"></el-table-column>
            <el-table-column prop="periodCount" label="课时数" width="120" show-overflow-tooltip
              align="center"></el-table-column>
            <el-table-column prop="viewCount" label="学习人数" show-overflow-tooltip align="center"></el-table-column>
            <el-table-column prop="updateTime" label="创建人时间" width="120" show-overflow-tooltip
              align="center"></el-table-column>
            <el-table-column prop="createName" label="创建人" show-overflow-tooltip align="center"></el-table-column>
          </el-table>
        </div>
      </div>
      <div class="derwer_footer">
        <div class="derwer_footerSum"> 已选择 <span class="sumNamber">{{ tableSum }}</span> 个课程</div>
        <div>
          <el-button type="primary" plain @click="cancel">取消</el-button>
          <el-button type="primary" @click="submit">添加</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script>
import moment from "moment";
export default {
  props: {
    courseList: {
      type: Array,
      default: []
    },
      newSubJectName: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      formInline: {
        courseName: '',
        courseTeacher: '',
        subjectId: '',
        startTime: '',
        endTime: ''
      },
      timeLine: [],
      subjectList: [],
      drawer: false,
      tableData: [],
      multipleSelection: [],
      tableSum: '0',
      props: {
        children: "children",
        label: "name",
        value: "id",
        checkStrictly: true,
        emitPath: false,
      },
      courseTaskList: [],
      routeInfo: {}
    };
  },
  watch: {
    timeLine(val) {
      if (val.length > 0) {
        this.formInline.startTime = val[0];
        this.formInline.endTime = val[1];
      } else {
        this.formInline.startTime = "";
        this.formInline.endTime = "";
      }
    },
  },
  created() {
    this.routeInfo = JSON.parse(sessionStorage.getItem("routeInfo"));
    this.courseTaskList = this.courseList
    this.init()
    this.getTableList()
  },
  methods: {
    init() {
      // 获取科目
      let data = {
        pageNo: 1,
        pageSize: 999,
      };
      this.$api.subjectListAll(data).then((res) => {
        if (res.code == 200) {
          this.subjectList = this.$tools.transData(
            res.data,
            "id",
            "parentId",
            "children"
          )
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 获取课程列表
    getTableList(tages = false) {
      let data = {
        userId: this.routeInfo.userId,
        pageNo: 1,
        pageSize: 9999,
        courseName: this.formInline.courseName,
        createName: this.formInline.courseTeacher,
        subjectId: this.formInline.subjectId,
        startTime: this.formInline.startTime,
        endTime: this.formInline.endTime
      }
      this.$api.getOnlineCourseList(data).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.list;
          if (tages) {
            setTimeout(() => {
              this.tableData.forEach((row) => {
                const matchedIndex = this.courseTaskList.findIndex(item => item.id === row.id)
                this.$refs.multipleTable.toggleRowSelection(row, matchedIndex != -1)
              })
            })
          }
        } else {
          this.$message.error(res.msg);
        }
        this.tableLoading = false
      });
    },
    handleSelectionChange(val) {
      this.tableSum = val.length
      this.multipleSelection = val;
    },
    // 取消关闭弹窗
    cancel() {
      this.$refs.multipleTable.clearSelection()
      this.drawer = false
    },
    // 提交
    submit() {
      this.courseTaskList = this.multipleSelection
      console.log(this.courseTaskList, ' this.courseTaskList');
      this.$refs.multipleTable.clearSelection()
      this.drawer = false
    },
    // 添加课程
    addcourse() {
      this.drawer = true
      this.$nextTick(() => {
        this.getTableList(true)
      })
    },
    // 查询
    search() {
      this.getTableList()
    },
    // 重置
    resetForm() {
      this.formInline = {
        courseName: '',
        courseTeacher: '',
        subjectId: '',
        startTime: '',
        endTime: ''
      },
        this.timeLine = []
      this.getTableList()
    },
    //删除
    deleteCourse(item, index) {
      this.courseTaskList.splice(index, 1)
    },
  },
};
</script>
<style lang="scss" scoped>
.heard {
  height: 100%;
}

.course_content {
  height: calc(100% - 70px);
  overflow: auto;
  margin-top: 16px;
}

.course_nav {
  height: 62px;
  background: #FAF9FC;
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  line-height: 62px;
  padding: 0 15px;
  font-size: 14px;
  margin: 15px 0;
}

.table_conter {
  height: 600px;
  overflow: auto;
}

.course_title {
  margin-top: 8px;
  font-weight: 600;
  color: #333333;
  font-size: 16px;
}

.courer_name {
  display: flex;
  align-items: center;
  width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  img {
    margin-right: 12px;
  }
}

.courseType {
  width: 500px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.operation {
  color: #3562DB;
}

.Iconx {
  margin-left: 20px;
}

.coursrDrawer {
  color: #333333;
  font-weight: 500;
  font-size: 18px;
}

::v-deep .el-drawer__body {
  overflow: hidden;
}

::v-deep .el-drawer__header {
  height: 56px;
  line-height: 56px !important;
  padding-bottom: 20px;
  border-bottom: 1px solid #DCDFE6;
  box-shadow: -4px 0 4px 0 rgba(203, 205, 220, 0.24);
}

.deawer_conter {
  padding: 0 16px;
}

.deawer_heard {
  margin-bottom: 16px;
  display: inline-block;
  // display: flex;
}

.derwer_footer {
  width: 100%;
  height: 56px;
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0px 0px 12px 3px rgba(0, 0, 0, 0.1);
  position: absolute;
  bottom: 0px;
  left: 0px;
  display: flex;
  justify-content: space-between;
  line-height: 56px;
  padding: 0px 20px;
}

.derwer_footerSum {
  font-size: 14px;
  font-weight: 400;
  color: #7F848C;

  .sumNamber {
    font-size: 16px;
    font-weight: 500;
    color: #333333;
  }
}

.imgClass {
  width: 80px;
  height: 80px;
}
</style>