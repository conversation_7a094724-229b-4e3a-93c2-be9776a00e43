<template>
  <div style="height: 100%;">
    <div class="search-from">
      <el-input v-model.trim="filters.questName" placeholder="字典名称" clearable class="input-item"></el-input>
      <el-button type="primary" @click="resetData">重置</el-button>
      <el-button type="primary" @click="searchClick()">查询</el-button>
    </div>
    <div class="middle_tools">
      <el-button type="primary" icon="el-icon-plus" @click="openDialog('add')">新增</el-button>
      <el-button type="primary" icon="el-icon-edit" :disabled="selectedTableList.length != 1" @click="openDialog('edit')">编辑</el-button>
      <el-upload
        ref="importFile"
        class="upload-demo"
        action="string"
        :limit="1"
        :on-exceed="handleImportExceed"
        multiple
        :show-file-list="false"
        :http-request="
          (file) => {
            return httpImport(file)
          }
        "
      >
        <el-button type="primary" icon="el-icon-download">导入</el-button>
      </el-upload>
    </div>
    <div class="contentTable">
      <div class="contentTable-main table-content">
        <el-table
          v-loading="tableLoading"
          :data="tableData"
          style="width: 100%;"
          row-key="id"
          border
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          :height="tableHeight"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center"></el-table-column>
          <el-table-column label="序号" type="index" width="55"></el-table-column>
          <el-table-column prop="label" label="问题类别名称"> </el-table-column>
          <el-table-column prop="level" label="所属等级" width="180"> </el-table-column>
          <el-table-column prop="sort" label="排序" width="100"> </el-table-column>
          <el-table-column label="操作" width="100">
            <template slot-scope="scope">
              <el-button type="danger" size="mini" @click="remove(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <el-dialog v-dialogDrag custom-class="model-dialog" :title="dialogTitle + '字典'" :visible.sync="dialogVisible" :before-close="dialogClosed">
      <div style="height: 100%; background-color: #fff; padding: 10px;">
        <el-form ref="formInline" :model="formInline" :rules="rules" :inline="true" class="advanced-search-form" label-position="right" label-width="110px">
          <el-form-item label="字典名称：" prop="questName">
            <el-input v-model="formInline.questName" placeholder="请输入字典名称" autocomplete="off" :maxlength="50" show-word-limit style="width: 260px;"></el-input>
          </el-form-item>
          <el-form-item label="上级字典：" prop="parentId">
            <el-cascader
              v-model="formInline.parentId"
              :options="tableData2"
              :props="{ checkStrictly: true, value: 'id', emitPath: false }"
              placeholder="请选择上级字典"
            ></el-cascader>
          </el-form-item>
          <el-form-item label="字典键值：" prop="dictCode">
            <el-input v-model.number="formInline.dictCode" placeholder="请输入字典键值" autocomplete="off" show-word-limit maxlength="6" style="width: 260px;"></el-input>
          </el-form-item>
          <el-form-item label="排序号：" prop="serialNum">
            <el-input v-model.number="formInline.serialNum" placeholder="请输入排序号" maxlength="6" autocomplete="off" style="width: 260px;"></el-input>
          </el-form-item>
          <br />
          <el-form-item label="图片：">
            <el-upload
              ref="uploadFile"
              drag
              multiple
              class="mterial_file"
              action="string"
              list-type="picture-card"
              :file-list="fileEcho"
              :http-request="httpRequest"
              accept=".jpg, .jpeg, .png, .JPG, .JPEG, .bmp"
              :limit="1"
              :on-exceed="handleExceed"
              :beforeUpload="beforeAvatarUpload"
              :on-remove="handleRemove"
              :on-change="fileChange"
            >
              <i class="el-icon-upload"></i>

              <div class="el-upload__text" style="top: 33px;">
                将文件拖到此处，或
                <em>点击上传</em>
              </div>
              <div slot="tip" class="el-upload__tip">可上传单张图片,图片分辨率不低于50px*50px</div>
            </el-upload>
            <!-- <div v-else style="width: 100px">
                <img width="100%" :src="'data:image/png;base64' + formInline.attachment" alt="" />
              </div> -->
          </el-form-item>
          <el-form-item label="备注信息：" prop="remarks" class="sino-form-textarea">
            <el-input
              v-model.trim="formInline.remarks"
              show-word-limit
              placeholder="请输入备注信息"
              class="sino-textarea"
              style="width: 480px;"
              maxlength="800"
              type="textarea"
              :autosize="{ minRows: 8, maxRows: 8 }"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <span>
          <el-button type="primary" plain @click="dialogClosed">取 消</el-button>
          <el-button type="primary" class="sino-button-sure" @click="submit">确定</el-button>
        </span>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios'
export default {
  data() {
    return {
      loading: false,
      filters: {
        questName: ''
      },
      tableData: [],
      tableLoading: true,
      dialogTitle: '',
      dialogVisible: false,
      rules: {
        questName: [{ required: true, message: '请输入字典名称', trigger: 'blur' }],
        dictCode: [{ required: true, message: '请输入字典键值', trigger: 'blur' }]
      },
      formInline: {
        questName: '',
        parentId: '',
        serialNum: '',
        remarks: '',
        dictCode: '',
        questIconUrl: ''
      },
      selectedTableList: [],
      tableData2: [],
      fileEcho: [],
      formData: ''
    }
  },
  computed: {
    tableHeight() {
      return document.body.clientHeight - 200
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    resetData() {
      this.filters.questName = ''
      this.getData()
    },
    searchClick() {
      this.getData()
    },
    getData() {
      this.$api.ipsmGetClassifyDictList({ questName: this.filters.questName || '' }).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data
          this.tableData2 = [...this.tableData]
          this.tableData2.unshift({
            id: '',
            label: '无上级字典',
            level: '1'
          })
          this.tableLoading = false
        } else {
          this.$message.error(res.message)
        }
      })
    },
    dialogClosed() {
      this.$refs.formInline.resetFields()
      this.formInline.questName = ''
      this.formInline.parentId = ''
      this.formInline.serialNum = ''
      this.formInline.remarks = ''
      this.formInline.dictCode = ''
      this.fileEcho = []
      this.formData = ''
      this.dialogVisible = false
    },
    httpRequest() {
      this.formData = new FormData()
      this.fileEcho.forEach((item) => {
        console.log(item)
        this.formData.append('file', item.raw)
      })
      let loginData = JSON.parse(sessionStorage.getItem('LOGINDATA'))
      this.formData.append('hospitalCode', loginData.hospitalCode)
      axios({
        method: 'post',
        url: __PATH.VUE_AQ_URL + 'file/upload',
        data: this.formData,
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      }).then((res) => {
        console.log('res', res)
        if (res.data.code == 200) {
          this.formInline.questIconUrl = res.data.data.fileKey
        } else {
          this.$message.error(res.data.message)
        }
      })
    },
    fileChange(file, fileList) {
      this.fileEcho = fileList
    },
    submit() {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          if (this.dialogTitle == '新增') {
            let { questName, parentId, serialNum, remarks, dictCode, questIconUrl } = this.formInline
            let params = {
              questName,
              parentId,
              serialNum,
              remarks,
              questCode: dictCode,
              questIconUrl
            }
            this.$api.ipsmSaveClassifyDictList(params).then((res) => {
              if (res.code == 200) {
                this.$message.success('新增成功!')
                this.getData()
              } else {
                this.$message.error('操作失败!')
              }
              this.dialogClosed()
            })
          } else {
            let { questName, parentId, serialNum, remarks, dictCode, questIconUrl } = this.formInline
            let params = {
              questName,
              parentId,
              serialNum,
              remarks,
              id: this.selectedTableList[0].id,
              questCode: dictCode,
              questIconUrl
            }
            this.$api.ipsmUpdateClassifyDictList(params).then((res) => {
              if (res.code == 200) {
                this.$message.success('修改成功!')
                this.getData()
              } else {
                this.$message.error('操作失败!')
              }
              this.dialogClosed()
            })
          }
        }
      })
    },
    handleExceed() {
      this.$message.error('最多上传一份文件')
    },
    handleImportExceed() {
      this.$message.error('最多上传一份文件')
    },
    handleRemove() {},
    beforeAvatarUpload(file) {
      const isLt5M = file.size / 1024 / 1024 < 5
      if (!isLt5M) {
        this.$message.error('上传图片大小不能超过 5MB!')
        return false
      }
      if (file.name.indexOf(',') != -1) {
        this.$message.error('非法的文件名')
        return false
      }
    },
    openDialog(type) {
      if (type == 'add') {
        this.dialogTitle = '新增'
        this.dialogVisible = true
      } else {
        this.dialogTitle = '修改'
        let id = this.selectedTableList[0].id
        this.$api.ipsmGetClassifyInfoById({ id }).then((res) => {
          if (res.code == 200) {
            this.dialogVisible = true
            let currentData = res.data
            this.formInline.questName = currentData.questName
            this.formInline.parentId = currentData.parentId
            this.formInline.dictCode = currentData.questCode
            this.formInline.serialNum = currentData.serialNum || ''
            this.formInline.remarks = currentData.remarks || ''
            if (currentData.imageList.length) {
              this.fileEcho.push({
                url: currentData.imageList[0],
                name: ''
              })
            }
          } else {
            this.$message.error(res.message)
          }
        })
      }
    },
    handleSelectionChange(val) {
      this.selectedTableList = val
    },
    remove(row) {
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$api.ipsmDeleteClassifyDictList({ id: row.id }).then((res) => {
            if (res.code == 200) {
              this.$message.success('删除成功!')
              this.getData()
            } else {
              this.$message.error('删除失败!')
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    httpImport(file) {
      this.loading = true
      let formData = new FormData()
      formData.append('fileUrl', file.file)
      // this.reportForm.questionAttachmentUrl.push(item.file)
      let loginData = JSON.parse(sessionStorage.getItem('LOGINDATA'))
      formData.append('hospitalCode', loginData.hospitalCode)
      formData.append('unitCode', loginData.unitCode)
      formData.append('hospitalName', loginData.hospitalName)
      formData.append('userId', loginData.userId)
      formData.append('userName', loginData.userName)
      axios({
        method: 'post',
        url: __PATH.VUE_AQ_URL + 'safetyManager/safetyQuestionType/importData',
        data: formData,
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      }).then((res) => {
        this.loading = false
        console.log('res', res)
        this.$refs.importFile.clearFiles()
        if (res.data.code == 200) {
          this.$message.success('导入成功!')
          this.getData()
        } else {
          this.$message.error('导入失败!')
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.input-item {
  width: 200px;
  margin-right: 16px;
}

.search-from {
  padding-bottom: 12px;

  & > div {
    margin-right: 10px;
  }

  & > button {
    margin-top: 12px;
  }
}

.middle_tools {
  // margin-top: 20px;
  margin-bottom: 10px;
}

.contentTable {
  height: calc(100% - 100px);
  //   display: flex;
  //   flex-direction: column;
  .contentTable-main {
    height: 100%;
    // flex: 1;
    overflow: auto;
  }

  .contentTable-footer {
    padding: 10px 0;
  }
}

::v-deep .el-button--danger {
  background: #f78989;
  border-color: #f78989;
  color: #fff;
  min-width: 55px;
  height: 30px;
}

::v-deep .el-button--danger:hover {
  background: #f78989;
  border-color: #f78989;
  color: #fff;
}

.upload-demo {
  display: inline-block;
  width: 100px;
  margin-left: 10px;
}

::v-deep .mterial_file > .el-upload-list {
  position: absolute;
  top: 0;
  width: 500px;
  margin-left: 430px;
  max-height: 160px;
  // overflow: auto;
}

::v-deep .mterial_file > .el-upload--picture-card {
  border: none;
  width: 300px;
}

::v-deep .mterial_file .el-upload .el-upload-dragger {
  width: 300px;
  height: 148px;
}

::v-deep .mterial_file .el-upload__text {
  position: absolute;
  left: 57px;
  color: #606266;
  font-size: 14px;
  text-align: center;
}
</style>
