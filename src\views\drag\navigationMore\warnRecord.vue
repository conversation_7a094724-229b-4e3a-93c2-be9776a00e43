<template>
  <PageContainer>
    <div slot="header">
      <div class="search-header">
        <div class="backBar">
          <span style="cursor: pointer;" @click="$router.go(-1)">
            <i class="el-icon-arrow-left"></i>
            报警管理
          </span>
        </div>
        <div class="search-from">
          <el-select
            v-model="searchFrom.projectCode"
            placeholder="报警来源"
            clearable
            @change="getIncidentAndSourceGroup"
            @clear="
              () => {
                searchFrom.incidentType = ''
              }
            "
          >
            <el-option v-for="item in alarmSourceOptions" :key="item.projectCode" :label="item.projectName" :value="item.projectCode"> </el-option>
          </el-select>
          <el-select v-model="searchFrom.incidentType" placeholder="事件类型" clearable :disabled="!searchFrom.projectCode.length">
            <el-option v-for="item in eventTypeOptions" :key="item.incidentName" :label="item.incidentName" :value="item.incidentType"> </el-option>
          </el-select>
          <el-select v-model="searchFrom.alarmLevel" placeholder="报警等级" clearable>
            <el-option v-for="item in alarmLevelOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
          <el-select ref="treeSelect" v-model="searchFrom.alarmSpaceId" clearable placeholder="空间位置" @clear="handleClear">
            <el-option hidden :value="searchFrom.alarmSpaceId" :label="areaName"> </el-option>
            <el-tree
              :data="serverSpaces"
              :props="serverDefaultProps"
              :load="serverLoadNode"
              lazy
              :expand-on-click-node="false"
              :check-on-click-node="true"
              @node-click="handleNodeClick"
            >
            </el-tree>
          </el-select>
          <el-date-picker
            v-model="searchFrom.dataRange"
            type="daterange"
            unlink-panels
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            :picker-options="pickerOptions"
          >
          </el-date-picker>
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="searchForm">查询</el-button>
        </div>
      </div>
    </div>
    <div slot="content" class="wholeTable" style="height: 100%;">
      <div class="wholeTable-main table-content">
        <el-table
          ref="table"
          v-loading="loading"
          :resizable="false"
          border
          :data="tableData"
          :height="tableHeight"
          style="width: 100%;"
          @sort-change="tableSortChange"
        >
          <el-table-column prop="incidentName" label="事件类型" show-overflow-tooltip></el-table-column>
          <el-table-column prop="alarmObjectName" label="对象" show-overflow-tooltip></el-table-column>
          <el-table-column prop="alarmStartTime" label="报警时间" sortable="custom" width="170">
            <template slot-scope="scope">{{ scope.row.alarmStartTime }}</template>
          </el-table-column>
          <el-table-column prop="alarmLevel" label="报警等级" sortable="custom" width="110">
            <span slot-scope="scope" class="alarmLevel" :style="{ background: alarmLevelItem[scope.row.alarmLevel].color }">
              {{ alarmLevelItem[scope.row.alarmLevel].text }}
            </span>
          </el-table-column>
          <el-table-column prop="alarmSpaceName" label="位置" show-overflow-tooltip></el-table-column>
          <el-table-column prop="alarmSource" label="报警来源" show-overflow-tooltip></el-table-column>
          <el-table-column prop="alarmStatus" label="报警处理状态" width="120">
            <div slot-scope="scope" class="alarmStatus" :style="{ color: scope.row.alarmStatus == 0 ? '#FA403C' : scope.row.alarmStatus == 1 ? '#fd9434' : '#999' }">
              <span class="alarmStatusIcon" :style="{ background: scope.row.alarmStatus == 0 ? '#FA403C' : scope.row.alarmStatus == 1 ? '#ff9435' : '#999' }"></span>
              {{ scope.row.alarmStatus == 0 ? '未处理' : scope.row.alarmStatus == 1 ? '处理中' : '已关闭' }}
            </div>
          </el-table-column>
          <el-table-column prop="alarmAffirm" label="警情确认" width="100">
            <template slot-scope="scope">{{ alarmAffirmItem[scope.row.alarmAffirm] || '-' }}</template>
          </el-table-column>
          <!-- 0：非经典案例，1：经典案例 -->
          <el-table-column prop="classic" label="经典案例" width="100">
            <span slot-scope="scope" style="display: flex; align-items: center;">
              <svg-icon v-if="scope.row.classic == 1" name="collect_icon" class="collectIcon" />
              <svg-icon v-else name="collect_linear_icon" class="collectIcon" />
              {{ scope.row.classic == 1 ? '已存' : '未存' }}
            </span>
          </el-table-column>
          <!-- 0：不屏蔽，1：屏蔽中 -->
          <el-table-column prop="shield" label="屏蔽状态" width="120">
            <div slot-scope="scope" style="display: flex; align-items: center;">
              <span style="margin-left: 4px;">{{ scope.row.shield == 1 ? '已屏蔽' : '未屏蔽' }}</span>
            </div>
          </el-table-column>
          <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button type="text" @click="getWarnDetail(scope.row)">查看</el-button>
          </template>
        </el-table-column>
        </el-table>
      </div>
      <div class="wholeTable-footer">
        <el-pagination
          :current-page="pagination.current"
          :page-sizes="pagination.pageSizeOptions"
          :page-size="pagination.size"
          :layout="pagination.layoutOptions"
          :total="pagination.total"
          @size-change="paginationSizeChange"
          @current-change="paginationCurrentChange"
        >
        </el-pagination>
      </div>
      <template v-if="alarmDetailShow">
        <AlarmDetailDialog ref="alarmDetail" :alarmId="alarmDetail.alarmId" :visible.sync="alarmDetailShow" />
      </template>
    </div>
  </PageContainer>
</template>
<script>
import { transData, ListTree } from '@/util'
import tableListMixin from '@/mixins/tableListMixin.js'
export default {
  name: 'warnRecord',
  mixins: [tableListMixin],
  data() {
    return {
      alarmDetailShow: false,
      alarmDetail: {},
      alarmSourceOptions: [], // 报警来源
      eventTypeOptions: [], // 事件类型
      alarmLevelOptions: [
        // 报警等级
        {
          value: '0',
          label: '通知'
        },
        {
          value: '1',
          label: '一般'
        },
        {
          value: '2',
          label: '紧急'
        },
        {
          value: '3',
          label: '重要'
        }
      ],
      serverSpaces: [], // 空间位置
      spaces: [], // 空间位置
      serverDefaultProps: {
        label: 'ssmName',
        isLeaf: 'leaf',
        children: 'children'
      },
      areaName: '', // 选中 下拉树的name
      pickerOptions: {
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      searchFrom: {
        // 搜索条件
        projectCode: [], // 报警来源
        incidentType: '', // 事件类型
        alarmLevel: '', // 报警等级
        alarmSpaceId: '', // 空间位置
        dataRange: [] // 时间范围
      },
      loading: false,
      alarmLevelItem: {
        0: { text: '通知', color: '#3562DB' },
        1: { text: '一般', color: '#3562DB' },
        2: { text: '紧急', color: '#FF9435' },
        3: { text: '重要', color: '#FA403C' }
      },
      alarmAffirmItem: { 0: '未确认', 1: '真实报警', 2: '误报', 3: '演练', 4: '调试' },
      tableData: [],
      timeOrType: '' // 排序 0.按紧急降序  1.按紧急升序  2.按时间降序  3.按时间升序
    }
  },
  mounted() {
    this.getDataList()
    this.getTreelist()
    this.getAlarmSource()
  },
  methods: {
    // 获取报警来源
    getAlarmSource() {
      this.$api.getSourceByEmpty().then((res) => {
        if (res.code == 200) {
          this.alarmSourceOptions = res.data
        }
      })
    },
    // 获取事件类型以及报警来源分组
    getIncidentAndSourceGroup(val) {
      this.searchFrom.incidentType = ''
      this.$api.getIncidentGroupByProjectCode({projectCode: val.toString()}).then((res) => {
        if (res.code == 200) {
          // this.alarmSourceOptions = res.data
          // this.alarmSourceOptions = res.data.source
          this.eventTypeOptions = res.data
        }
      })
    },
    // 初始化组件数据
    getDataList() {
      let { projectCode, incidentType, alarmLevel, alarmSpaceId, dataRange } = this.searchFrom
      let params = {
        timeOrType: this.timeOrType,
        pageNo: this.pagination.current,
        pageSize: this.pagination.size,
        projectCode: projectCode.toString(),
        incidentType,
        alarmLevel,
        alarmSpaceId,
        startTime: dataRange[0],
        endTime: dataRange[1]
      }
      this.loading = true
      this.$api
        .GetAllAlarmRecord(params)
        .then((res) => {
          this.loading = false
          if (res.code == 200) {
            this.tableData = res.data ? res.data.records : []
            this.pagination.total = res.data ? res.data.total : 0
          } else {
            this.tableData = []
            this.pagination.total = 0
          }
        })
        .catch((err) => {
          this.loading = false
        })
    },
    // table排序
    tableSortChange(column) {
      if (column.prop === 'alarmStartTime') {
        this.timeOrType = column.order ? (column.order == 'ascending' ? 3 : 2) : ''
      } else {
        this.timeOrType = column.order ? (column.order == 'ascending' ? 1 : 0) : ''
      }
      this.getDataList()
    },
    // 查看详情
    getWarnDetail(row) {
      this.alarmDetailShow = true
      this.alarmDetail = row
    },
    // 选择下拉树 数据
    handleNodeClick(data) {
      this.searchFrom.alarmSpaceId = data.id
      this.areaName = data.ssmName
      this.$refs.treeSelect.blur()
    },
    // 重置查询
    resetForm() {
      this.handleClear()
      Object.assign(this.searchFrom, {
        projectCode: [], // 报警来源
        incidentType: '', // 事件类型
        alarmLevel: '', // 报警等级
        dataRange: [] // 时间范围
      })
      this.getDataList()
    },
    // 查询
    searchForm() {
      this.pagination.current = 1
      this.pagination.size = 15
      this.getDataList()
    },
    // 空间数据清除
    handleClear() {
      this.searchFrom.alarmSpaceId = ''
      this.areaName = ''
    },
    // 下拉树懒加载
    serverLoadNode(node, resolve) {
      if (node.level == 0) {
        return resolve(node.data)
      } else if (node.level > 0 && node.level < 4) {
        return resolve(node.data.children)
      } else {
        return this.spaceTreeChange(node.data, resolve)
      }
    },
    // 获取服务空间树形结构
    getTreelist() {
      this.$api.getStructureTree({}).then((res) => {
        if (res.code == 200) {
          this.spaces = res.data
          // 增加 懒加载节点
          res.data.map((e) => {
            e.leaf = false
          })
          // 转换为树形结构数据
          this.serverSpaces = transData(res.data, 'id', 'pid', 'children')
        }
      })
    },
    // 服务空间树结构点击获取自己及父节点ID并请求数据实现懒加载
    spaceTreeChange(value, resolve) {
      const child = ListTree(this.spaces, value.pid)
      child.push(value.id)
      let treeId = child.toString()
      let data = {
        current: 1,
        functionDictId: '',
        simCode: treeId,
        size: 999
      }
      // this.$api.getSpaceInfoList(data, __PATH.USER_CODE).then((res) => {
      this.$api.getSpaceInfoList(data).then((res) => {
        if (res.code == 200) {
          if (typeof resolve === 'function') {
            let treeNodeData = JSON.parse(JSON.stringify(res.data.records))
            treeNodeData.map((e) => {
              e.leaf = true
            })
            resolve(treeNodeData)
          }
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.search-header {
  padding: 10px 8px 10px 15px;

  .search-from {
    padding-bottom: 12px;

    & > div {
      margin-top: 12px;
      margin-right: 10px;
    }
  }

  .backBar {
    color: #121f3e;
    height: 30px;
    border-bottom: 1px solid #dcdfe6;
  }
}

.wholeTable {
  height: calc(100% - 16px);
  background: #fff;
  border-radius: 4px;
  padding: 16px;
  display: flex;
  margin-top: 16px;
  flex-direction: column;
  overflow: auto;

  .wholeTable-main {
    flex: 1;
    overflow: auto;
  }

  .wholeTable-footer {
    padding: 10px 0;
  }

  .alarmLevel {
    padding: 3px 6px;
    border-radius: 4px;
    color: #fff;
    line-height: 14px;
  }

  .alarmStatus {
    position: relative;
    display: inline-block;
    padding-left: 12px;

    .alarmStatusIcon {
      display: inline-block;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 8px;
      height: 8px;
      border-radius: 100%;
    }
  }

  .collectIcon {
    font-size: 16px;
    margin-right: 4px;
  }
}
</style>
