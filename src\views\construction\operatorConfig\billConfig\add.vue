<template>
  <el-dialog
    class="component form-base"
    custom-class="model-dialog"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :title="handle == 'add' ? `新增单据` : '编辑单据'"
    :visible="visible"
    width="50%"
    :before-close="close"
  >
    <div class="form-content">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="单据名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入单据名称" maxlength="50"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单据编码" prop="code">
              <el-input v-model="form.code" placeholder="请输入单据单据编码" maxlength="50"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单据类型" prop="documentType">
              <el-radio-group v-model="form.documentType" @change="docTypeChange">
                <el-radio label="0">作业</el-radio>
                <el-radio label="1">作业证</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目类型" prop="businessFormId">
              <el-select v-model="form.businessFormId" placeholder="请选择项目类型" filterable @change="changeBusinessFormId">
                <el-option v-for="(item, index) in projectList" :key="index" :label="item.flowModelName" :value="item.flowKey"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生成方式" prop="createType">
              <el-select v-model="form.createType" placeholder="请选择生成方式">
                <el-option label="自动" value="0"></el-option>
                <el-option label="手动" value="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-select v-model="form.state" placeholder="请选择状态">
                <el-option label="启用" value="1"></el-option>
                <el-option label="停用" value="0"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报表key" prop="reportKey">
              <el-input v-model="form.reportKey" placeholder="请输入单据名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" maxlength="200" show-word-limit></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div slot="footer">
      <el-button @click="close">取 消</el-button>
      <el-button type="primary" @click="submit">确 定</el-button>
    </div>
  </el-dialog>
</template>
<script>
export default {
  model: {
    prop: 'visible',
    event: 'update:visible'
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    handle: {
      type: String,
      default: 'add'
    },
    detail: {
      type: [Object, null],
      default: null
    }
  },
  data() {
    return {
      form: {
        name: '',
        code: '',
        documentType: '0',
        businessFormId: '',
        createType: '',
        reportKey: '',
        state: '',
        remark: ''
      },
      rules: {
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        code: [{ required: true, message: '请输入编码', trigger: 'blur' }],
        documentType: [{ required: true, message: '请选择作业类型', trigger: 'change' }],
        businessFormId: [{ required: true, message: '请选择项目类型', trigger: 'change' }],
        createType: [{ required: true, message: '请选择生成方式', trigger: 'change' }],
        state: [{ required: false, message: '请选择状态', trigger: 'change' }],
        reportKey: [{ required: true, message: '请输入报表key', trigger: 'blur' }]
      },
      projectList: [],
      flowNodeKeyList: []
    }
  },
  mounted() {
    if (this.detail) {
      this.form = {
        id: this.detail.id,
        name: this.detail.name,
        code: this.detail.code,
        documentType: this.detail.documentType,
        businessFormId: this.detail.businessFormId,
        createType: this.detail.createType,
        reportKey: this.detail.reportKey,
        state: this.detail.state,
        remark: this.detail.remark
      }
      this.getWorkNodeListByFlowKey(this.form.businessFormId)
    }
    this.getProjectList(this.form.documentType)
  },
  methods: {
    getProjectList(val) {
      this.$api
        .businessFormList({
          name: '',
          pageSize: 999,
          page: 1,
          assignmentType: val
        })
        .then((res) => {
          if (res.code === '200') {
            this.projectList = res.data.records
          }
        })
    },
    docTypeChange(val) {
      this.form.businessFormId = ''
      this.getProjectList(val)
    },
    changeBusinessFormId(val) {
      this.getWorkNodeListByFlowKey(val)
    },
    getWorkNodeListByFlowKey(val) {
      this.$api
        .workNodeListByFlowKey({
          flowKey: val
        })
        .then((res) => {
          if (res.code == 200) {
            this.flowNodeKeyList = res.data
          }
        })
    },
    close() {
      this.$emit('update:visible', false)
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          let fn = this.form.id ? 'receiptUpdate' : 'receiptAdd'
          this.$api[fn](this.form).then((res) => {
            if (res.code == 200) {
              this.$message.success(res.msg)
              this.$emit('success')
              this.close()
            }
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.el-form {
  background-color: #fff;
  padding: 10px 16px 0;
  .el-form-item {
    .el-select {
      width: 100%;
    }
  }
}
</style>
