<template>
  <el-dialog
    v-if="sceneDialogShow"
    :title="sceneDialogType == 'add' ? '新建时间表' : '编辑时间表'"
    :visible="sceneDialogShow"
    custom-class="scene-dialog"
    :before-close="closeDialog"
  >
    <div class="sceneForm_content" style="padding: 10px 20px 10px 10px;">
      <div class="form_row">
        <div class="form_row_label">时间表名称：</div>
        <div class="form_row_input">
          <el-input v-model="sceneForm.timeTableName" placeholder="请输入时间表名称"></el-input>
        </div>
      </div>
      <div class="form_row">
        <div class="form_row_label">时间表：</div>
        <div class="form_row_input">
          <el-button type="primary" plain @click="addTime"><i class="el-icon-plus"></i>新增</el-button>
        </div>
      </div>
      <div class="form_row">
        <div class="form_row_label"></div>
        <div class="form_row_input">
          <div class="form_row_input_box0"></div>
          <div class="form_row_input_box1">时间类型</div>
          <div class="form_row_input_box2">时间点</div>
          <div style="width: 40%;display: flex;">
            <div class="form_row_input_box5">配置类型</div>
            <div class="form_row_input_box3">配置参数</div>
            <div class="form_row_input_box4"></div>
          </div>
        </div>
      </div>
      <div v-for="(item, index) in sceneForm.timeTableDataList" :key="index" class="form_row">
        <div class="form_row_label"></div>
        <div class="form_row_input">
          <div class="form_row_input_box0" @click="deleteTime(index)">
            <i class="el-icon-error" style="font-size: 26px;color: #FA403C;"></i>
          </div>
          <div class="form_row_input_box form_row_input_box1">
            <el-select v-model="item.timeType" size="small" placeholder="请选择" @change="item.timePoint = ''">
              <el-option label="相对" :value="1"> </el-option>
              <el-option label="绝对" :value="2"> </el-option>
            </el-select>
          </div>
          <div class="form_row_input_box form_row_input_box2">
            <el-time-picker
              v-if="item.timeType != 1"
              v-model="item.timePoint"
              size="small"
              value-format="HH:mm"
              format="HH:mm"
              :picker-options="{
                selectableRange: `${nowTime}:00 - 23:59:59`
              }"
              placeholder="任意时间点"
            >
            </el-time-picker>
            <el-select v-else v-model="item.timePoint" size="small" placeholder="请选择">
              <el-option label="日出" value="日出"> </el-option>
              <el-option label="日落" value="日落"> </el-option>
            </el-select>
          </div>
          <div style="width: 40%;">
            <div v-for="(v, i) in item.list" :key="i + 'chid'" class="allocation">
              <div class="form_row_input_box form_row_input_box5">
                <el-select v-model="v.paramId" size="small" placeholder="请选择" @change="(e) => changeSele(e, v)">
                  <el-option v-for="sele in allocationArr" :key="sele.paramId" :label="sele.paramName" :value="sele.paramId"></el-option>
                </el-select>
              </div>
              <div v-if="v.type == 6" class="form_row_input_box form_row_input_box3">
                <el-select v-model="v.value" size="small" placeholder="请选择">
                  <el-option label="打开" :value="1"> </el-option>
                  <el-option label="关闭" :value="2"> </el-option>
                </el-select>
              </div>
              <div v-else class="form_row_input_box form_row_input_box3">
                <el-input v-model="v.value" placeholder="请输入数值"></el-input>
              </div>
              <div class="form_row_input_box form_row_input_box4" style="display: flex;">
                <i class="el-icon-delete" style="font-size: 22px; line-height: 40px; cursor: pointer;margin-right: 16px;" @click="deleteItemList(item, i)"></i>
                <i v-if="i == 0" class="el-icon-plus" style="font-size: 22px; line-height: 40px; cursor: pointer;" @click="addItemList(item)"></i>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button plain type="primary" @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="groupSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  name: 'sceneDialog',
  props: {
    page: {
      type: Number,
      default: 1
    },
    sceneDialogShow: {
      type: Boolean,
      default: false
    },
    sceneDialogType: {
      type: String,
      default: 'add'
    },
    dialogData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    patternId: {
      type: Number,
      default: Number
    },
    projectCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      nowTime: '00:00',
      sceneForm: {
        timeTableName: '',
        timeTableDataList: [
          {
            paramId: null
          }
        ]
      },
      allocationArr: [

      ]
    }
  },
  mounted() {
    if (this.sceneDialogType === 'add') {
      this.sceneForm.timeTableDataList = [
        {
          timePoint: '',
          // switchControl: '',
          timeType: '',
          list: [
            {
              paramId: '',
              paramName: '',
              value: '',
              type: ''
            }
          ]
        }
      ]
    } else {
      this.sceneForm = JSON.parse(JSON.stringify(this.dialogData))
    }
    this.getParamInfoByProjectCode()
  },
  methods: {
    getParamInfoByProjectCode() {
      this.$api.getParamInfoByProjectCode({projectCode: this.projectCode}).then(res => {
        this.allocationArr = res.data
      })
    },
    addItemList(item) {
      item.list.unshift({
        paramId: '',
        paramName: '',
        value: '',
        type: ''
      })
    },
    deleteItemList(item, i) {
      if (item.list.length == 1) return this.$message.warning('至少保留一条!')
      item.list.splice(i, 1)
    },
    addTime() {
      this.sceneForm.timeTableDataList.push({
        timePoint: '',
        timeType: '',
        // switchControl: '',
        list: [
          {
            paramId: '',
            paramName: '',
            value: '',
            type: ''
          }
        ]
      })
    },
    deleteTime(index) {
      this.sceneForm.timeTableDataList.splice(index, 1)
    },
    closeDialog() {
      this.$emit('closeSceneDialog')
    },
    changeSele(e, v) {
      let obj = this.allocationArr.find(ele => ele.paramId == e)
      v.paramName = obj.paramName
      v.type = obj.type
    },
    groupSubmit() {
      const param = JSON.parse(JSON.stringify(this.sceneForm))
      try {
        param.timeTableDataList.forEach((item) => {
          // item.timeTableName = param.timeTableName
          let judgeObj = {
            timePoint: item.timePoint,
            timeType: item.timeType
          }
          if (Object.values(judgeObj).some((i) => [undefined, NaN, null, ''].includes(i))) {
            throw new Error()
          }
        })
      } catch (error) {
        return this.$message.warning('请完善时间表信息!')
      }
      param.timeTable = JSON.stringify(param.timeTableDataList)
      param.patternId = this.patternId

      if (this.sceneDialogType === 'add') {
        param.type = 'add'
        if (this.page == 2) {
          param.id = new Date().getMilliseconds()
        }
      }
      // delete param.timeTableDataList
      param['projectCode'] = this.projectCode
      let header = {}
      if (param.type === 'add') {
        header = {
          'operation-type': 1
        }
      } else {
        header = {
          'operation-type': 2,
          'operation-id': param.id,
          'operation-name': param.timeTableName
        }
      }
      if (this.page == 1) {
        this.$api.saveTimeTable(param, header).then((res) => {
          if (res.code == 200) {
            this.$emit('sceneSubmit', true)
          } else {
            this.$message.warning(res.message)
          }
        })
      } else {
        this.$api.saveTimeTableToRedis({ timeTable: JSON.stringify(param), projectCode: this.projectCode }, header).then((res) => {
          if (res.code == 200) {
            this.$emit('sceneSubmit', param)
          } else {
            this.$message.warning(res.message)
          }
        })
      }
    }
  }
}
</script>
<style src="@/assets/styles/sinomis-ui.css" scoped></style>
<style lang="scss" scoped>
.allocation{
  width: 100%;
  display: flex;
}
.el-select {
  width: 100%;
}

.scene-dialog {
  width: 30% !important;
  height: 30% !important;
  min-width: 562px !important;
  min-height: 376px !important;

  .sceneForm_content {
    // .el-form-item {
    //   width: inherit;
    // }
    .form_row {
      display: flex;
      // height: 40px;
      width: 100%;
      line-height: 40px;
      padding-right: 30px;
      margin-bottom: 10px;

      .form_row_label {
        min-width: 100px;
        text-align: right;
        margin-right: 10px;
        font-size: 14px;
        font-family: NotoSansHans-Medium, NotoSansHans;
        color: #606266;
      }

      .form_row_input {
        flex: 1;
        display: flex;
        .form_row_input_box0{
          width: 5%;
          display: flex;
          margin: 5px 16px 0 0;
          cursor: pointer;
        }

        .form_row_input_box {
          padding-right: 15px;
          box-sizing: border-box;
        }

        .form_row_input_box1 {
          width: 15%;
        }

        .form_row_input_box2 {
          width: 25%;

          .el-date-editor.el-input,
          .el-date-editor.el-input__inner {
            width: 100%;
          }
        }

        .form_row_input_box3 {
          width: 50%;
        }
        .form_row_input_box5{
          width: 40%;
        }
        .form_row_input_box4 {
          width: 15%;
        }
      }
    }
  }
}
</style>
