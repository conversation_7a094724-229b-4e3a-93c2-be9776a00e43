export default {
  newDataList: {
    overheat: {
      total: 0,
      data: [],
      bgColor: '#ff7676',
      height: '14%',
      title: '过热',
      scale: '℃',
      startNum: 28,
      endNum: null,
      fontColor: '#e40e0e',
      name: '平均温度',
      unit: '℃',
      boxBgColor: '#FFE9E9'
    },
    heat: {
      total: 0,
      data: [],
      bgColor: '#f38831',
      height: '10%',
      title: '热',
      scale: '28',
      startNum: 26,
      endNum: 28,
      fontColor: '#ef7a1a',
      name: '平均温度',
      unit: '℃',
      boxBgColor: '#fee4cf'
    },
    comfort: {
      total: 0,
      data: [],
      bgColor: '#73e58f',
      height: '30%',
      title: '舒适',
      scale: '26',
      startNum: 21,
      endNum: 26,
      fontColor: '#34B253',
      name: '平均温度',
      unit: '℃',
      boxBgColor: '#ecfcf0'
    },
    cold: {
      total: 0,
      data: [],
      bgColor: '#a5e6ff',
      height: '24%',
      title: '冷',
      scale: '21',
      startNum: 14,
      endNum: 21,
      fontColor: '#43c2f4',
      name: '平均温度',
      unit: '℃',
      boxBgColor: '#c1e2ef'
    },
    freezing: {
      total: 0,
      data: [],
      bgColor: '#7fa0ff',
      height: '22%',
      title: '寒冷',
      scale: '14',
      type: 'bottom',
      startNum: null,
      endNum: 14,
      fontColor: '#3772E8',
      name: '平均温度',
      unit: '℃',
      boxBgColor: '#EDF2FF'
    }
  },
  humDataList: {
    damp: {
      total: 0,
      data: [],
      bgColor: '#a5e6ff',
      height: '40%',
      title: '潮湿',
      scale: '100',
      startNum: 60,
      endNum: 100,
      fontColor: '#43c2f4',
      name: '平均湿度',
      unit: '%',
      boxBgColor: '#c1e2ef'
    },
    comfort: {
      total: 0,
      data: [],
      bgColor: '#73e58f',
      height: '20%',
      title: '舒适',
      scale: '60',
      startNum: 40,
      endNum: 60,
      fontColor: '#34B253',
      name: '平均湿度',
      unit: '%',
      boxBgColor: '#ecfcf0'
    },
    dry: {
      total: 0,
      data: [],
      bgColor: '#f38831',
      height: '40%',
      title: '干燥',
      scale: '40',
      startNum: 0,
      endNum: 40,
      fontColor: '#ef7a1a',
      name: '平均湿度',
      unit: '%',
      boxBgColor: '#fee4cf'
    },
    zero: {
      total: 0,
      data: [],
      bgColor: '#f38831',
      height: '0',
      title: '干燥',
      scale: '0',
      afterStartNum: 40,
      startNum: -0.0000000001,
      endNum: 0,
      fontColor: '#ef7a1a',
      name: '平均湿度',
      unit: '%',
      boxBgColor: '#fee4cf'
    }
  },
  CO2DataList: {
    peril: {
      total: 0,
      data: [],
      bgColor: '#f38831',
      height: '50%',
      title: '危险',
      scale: 'ppm',
      startNum: 800,
      endNum: null,
      fontColor: '#ef7a1a',
      name: 'CO2平均浓度',
      unit: 'ppm',
      boxBgColor: '#fee4cf'
    },
    comfort: {
      total: 0,
      data: [],
      bgColor: '#73e58f',
      height: '50%',
      title: '舒适',
      scale: '800',
      startNum: 0,
      endNum: 800,
      fontColor: '#34B253',
      name: '平均CO2浓度',
      unit: 'ppm',
      boxBgColor: '#ecfcf0'
    },
    zero: {
      total: 0,
      data: [],
      bgColor: '#73e58f',
      height: '0',
      title: '舒适',
      scale: '0',
      afterStartNum: 800,
      startNum: -0.0000000001,
      endNum: 0,
      fontColor: '#34B253',
      name: '平均CO2浓度',
      unit: 'ppm',
      boxBgColor: '#ecfcf0'
    }
  },
  PMDataList: {
    serious: {
      total: 0,
      data: [],
      bgColor: '#A834E6',
      height: '20%',
      title: '严重',
      scale: 'ug/m3',
      startNum: 200,
      endNum: null,
      fontColor: '#A00FEA',
      name: 'PM2.5平均浓度',
      unit: '',
      boxBgColor: '#cc9be7'
    },
    heavy: {
      total: 0,
      data: [],
      bgColor: '#8D4EE9',
      height: '20%',
      title: '重',
      scale: '200',
      startNum: 150,
      endNum: 200,
      fontColor: '#741FEA',
      name: '平均PM2.5浓度',
      unit: 'ug/m3',
      boxBgColor: '#b99ce5'
    },
    fit: {
      total: 0,
      data: [],
      bgColor: '#89C2F0',
      height: '20%',
      title: '重',
      scale: '150',
      startNum: 100,
      endNum: 150,
      fontColor: '#4BA5ED',
      name: '平均PM2.5浓度',
      unit: 'ug/m3',
      boxBgColor: '#91a5b6'
    },
    good: {
      total: 0,
      data: [],
      bgColor: '#5CFBDC',
      height: '20%',
      title: '良',
      scale: '100',
      startNum: 50,
      endNum: 100,
      fontColor: '#2CF2CB',
      name: '平均PM2.5浓度',
      unit: 'ug/m3',
      boxBgColor: '#bff3e9'
    },
    fine: {
      total: 0,
      data: [],
      bgColor: '#73e58f',
      height: '20%',
      title: '优',
      scale: '50',
      startNum: 0,
      endNum: 50,
      fontColor: '#34B253',
      name: '平均PM2.5浓度',
      unit: 'ug/m3',
      boxBgColor: '#ecfcf0'
    },
    zero: {
      total: 0,
      data: [],
      bgColor: '#73e58f',
      height: '0',
      title: '优',
      scale: '0',
      afterStartNum: 50,
      startNum: -0.0000000001,
      endNum: 0,
      fontColor: '#34B253',
      name: '平均PM2.5浓度',
      unit: 'ug/m3',
      boxBgColor: '#ecfcf0'
    }
  }
}
