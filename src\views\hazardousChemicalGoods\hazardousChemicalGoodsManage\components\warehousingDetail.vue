<template>
  <el-dialog :title="warehouseType === '1' ? '入库单' : '出库单'" width="55%" :visible.sync="sectionDialogShow" custom-class="model-dialog" :before-close="closeDialog">
    <div class="content">
      <div class="content_step">
        <el-steps :active="stepActive" finish-status="success" class="steps" align-center>
          <el-step title="暂存"></el-step>
          <el-step :title="warehouseType === '1' ? '已入库' : '已出库'"></el-step>
        </el-steps>
      </div>
      <div class="detailInfo">
        <div v-for="(item, index) in warehouseList" :key="index" class="item-list">
          <div class="item-label">{{ item.label }}</div>
          <div class="item-value">{{ detailInfo[item.value] }}</div>
        </div>
      </div>
      <div class="statistics">
        <div class="statistics_box">
          <p class="statistics_label">合计数量</p>
          <p class="statistics_value">
            <span>{{ warehouseType === '1' ? detailInfo.inwarehouseCount : detailInfo.outwarehouseCount }}</span>
            <span class="unit">个</span>
          </p>
        </div>
        <!-- <div class="statistics_box">
          <p class="statistics_label">合计金额</p>
          <p class="statistics_value">
            <span>{{detailInfo.amount||0}}</span>
            <span class="unit">元</span>
          </p>
        </div> -->
      </div>
      <div class="sino_table">
        <el-table ref="sinoTable" :data="tableData" row-key="id" height="200px" stripe border>
          <el-table-column label="序号" type="index" width="100"></el-table-column>
          <el-table-column prop="materialName" label="危化品名称" show-overflow-tooltip></el-table-column>
          <el-table-column prop="model" label="规格型号" show-overflow-tooltip></el-table-column>
          <el-table-column prop="basicUnitName" label="单位" show-overflow-tooltip></el-table-column>
          <el-table-column prop="operateCount" label="数量" show-overflow-tooltip></el-table-column>
        </el-table>
        <!-- <el-pagination :current-page="pagination.current" :page-sizes="[15, 30, 50, 100]" :page-size="pagination.size"
          :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
          @current-change="handleCurrentChange"></el-pagination> -->
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="submitDialog('exportForm')">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'warehousingDetail',
  components: {},
  props: {
    sectionDialogShow: {
      type: Boolean,
      default: false
    },
    warehouseType: {
      type: String,
      default: '' //1入库单，2出库单
    },
    recordNumber: {
      type: String,
      default: '' //计划单号
    }
  },
  data() {
    return {
      tableLoading: false,
      stepActive: 0,
      tableData: [],
      // -----------------------------Pagination
      // pagination: {
      //   current: 1,
      //   size: 15
      // }, // 分页数据
      // total: 0, // 数据总条数
      warehouseList: [],
      inwarehouseList: [
        {
          label: '计划单号：',
          value: 'recordNumber'
        },
        {
          label: '创建人：',
          value: 'createName'
        },
        {
          label: '创建时间：',
          value: 'createTime'
        },
        {
          label: '入库类型：',
          value: 'inwarehouseName'
        },
        {
          label: '总数量：',
          value: 'inwarehouseCount'
        },
        {
          label: '总金额：',
          value: 'amount'
        },
        {
          label: '备注：',
          value: 'remarks'
        }
      ],
      outWarehouseList: [
        {
          label: '计划单号：',
          value: 'recordNumber'
        },
        {
          label: '创建人：',
          value: 'createName'
        },
        {
          label: '创建时间：',
          value: 'createTime'
        },
        {
          label: '出库类型：',
          value: 'outwarehouseName'
        },
        {
          label: '总数量：',
          value: 'outwarehouseCount'
        },
        {
          label: '领用部门：',
          value: 'officesName'
        },
        {
          label: '备注：',
          value: 'remarks'
        }
      ],
      detailInfo: {
        recordNumber: '',
        createName: '',
        createTime: '',
        outwarehouseCount: '',
        inwarehouseName: '',
        officesName: '',
        inwarehouseCount: '',
        amount: '',
        remarks: ''
      }
    }
  },
  mounted() {
    if (this.warehouseType === '1') {
      this.getInWarehouseData()
    } else {
      this.getOutWarehouseData()
    }
    this.warehouseList = this.warehouseType === '1' ? this.inwarehouseList : this.outWarehouseList
  },
  methods: {
    //获取出库单详情
    getOutWarehouseData() {
      this.tableLoading = true
      let params = {
        id: '',
        recordNumber: this.recordNumber,
        userId: this.$store.state.user.userInfo.user.staffId,
        userName: this.$store.state.user.userInfo.user.staffName
      }
      this.$api.getOutWarehouseRecordById(params).then((res) => {
        this.tableLoading = false
        if (res.code == '200') {
          this.detailInfo = res.data
          this.stepActive = res.data.status === '暂存' ? 0 : 1
          this.tableData = res.data.materialRecordList
        }
      })
    },
    //获取入库单详情
    getInWarehouseData() {
      this.tableLoading = true
      let params = {
        id: '',
        recordNumber: this.recordNumber,
        userId: this.$store.state.user.userInfo.user.staffId,
        userName: this.$store.state.user.userInfo.user.staffName
      }
      this.$api.getInwarehouseRecordById(params).then((res) => {
        this.tableLoading = false
        if (res.code == '200') {
          this.detailInfo = res.data
          this.stepActive = res.data.status === '暂存' ? 0 : 1
          this.tableData = res.data.materialRecordList
        }
      })
    },
    // ---------------------------------------------------------- TabelFn
    // handleSizeChange(val) {
    //   this.pagination.current = 1
    //   this.pagination.size = val
    //   this.getTableData()
    // },
    // handleCurrentChange(val) {
    //   this.pagination.current = val
    //   this.getTableData()
    // },
    closeDialog() {
      this.$emit('closeSectionDialog')
    },
    submitDialog() {
      this.$emit('submitSectionDialog')
    }
  }
}
</script>
<style lang="scss" scoped>
p {
  margin-bottom: 0;
}
.model-dialog {
  .content {
    margin: 0 auto;
    background: #fff;
    border-radius: 4px;
    width: 100%;
    height: 100%;
    padding: 16px;
    .detailInfo {
      display: flex;
      flex-wrap: wrap;
      margin-top: 16px;
      .item-list {
        padding: 6px 0;
        width: calc(50%);
        display: flex;
        font-weight: 400;
        font-size: 14px;
        .item-label {
          min-width: 100px;
          text-align: right;
          color: #96989a;
        }
        .item-value {
          min-width: 100px;
          text-align: left;
          color: #333333;
        }
      }
    }
    .statistics {
      display: flex;
      width: 100%;
      margin: 16px 0;
      .statistics_box {
        width: calc(50% - 8px);
        background: #faf9fc;
        border-radius: 4px;
        text-align: center;
        padding: 16px 0;
        .statistics_label {
          color: #666666;
        }
        .statistics_value {
          margin-top: 8px;
          font-weight: bold;
          font-size: 18px;
          color: #333333;
          .unit {
            font-weight: 500;
            font-size: 14px;
            color: #96989a;
          }
        }
      }
      .statistics_box:nth-child(2) {
        margin-left: 16px;
      }
    }
  }
  .el-pagination {
    margin-top: 10px;
    width: 100%;
  }
}
</style>
<style lang="less" scoped>
@publicColor: #3562db;
.steps {
  width: 80%;
  margin: auto;
  ::v-deep .el-step {
    height: 100%;
    .el-step__line {
      background-color: rgba(0, 0, 0, 0.15);
      margin-right: 30px !important;
      margin-left: 35px !important;
      top: 50%;
      height: 1px;
    }
    .el-step__icon {
      font-size: 16px;
      border: 1px solid;
      .el-step__icon-inner {
        font-weight: unset !important;
      }
    }
    .el-step__head.is-process {
      color: @publicColor;
      border-color: @publicColor;
    }
    .el-step__head.is-success {
      color: @publicColor;
      border-color: @publicColor;
    }
    .is-success .el-step__icon.is-text {
      background: #e6effc;
      border-color: #e6effc;
    }
    .is-process .el-step__icon.is-text {
      background: @publicColor;
      color: #fff;
    }
    .is-wait .el-step__icon {
      background: #ededf5 !important;
      font-weight: 600;
      font-size: 14px;
      border-color: #ededf5;
      color: #666666;
    }
    .el-step__title.is-process {
      color: @publicColor;
    }
    .el-step__title.is-success {
      color: #565656;
    }
  }
}
</style>


