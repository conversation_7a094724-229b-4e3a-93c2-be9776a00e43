<template>
  <el-tabs v-model="tabActive" @tab-click="handleClick">
    <el-tab-pane label="任务验收" name="1">
      <div v-loading="pageLoading" element-loading-text="文件上传中..." class="conten">
        <el-form ref="form" :model="form" :rules="rules" label-width="100px">
          <el-form-item label="验收人" prop="acceptancePersonName">
            <span>{{ form.acceptancePersonName }}</span>
            <!-- <el-select
          v-model="form.implementPersonName"
          filterable
          placeholder="请选择人员"
          style="width: 100%;">
          <el-option v-for="item in personList" :key="item.id" :label="item.staffName" :value="item.id"> </el-option>
        </el-select> -->
          </el-form-item>
          <el-form-item label="验收结论" prop="inspectionReportId">
            <el-select v-model="form.inspectionReportId" filterable placeholder="请选择验收结论" style="width: 60%">
              <el-option v-for="item in conclusions" :key="item.id" :label="item.name" :value="item.id"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="验收意见" prop="acceptanceOpinion">
            <el-input
              v-model="form.acceptanceOpinion"
              type="textarea"
              placeholder="请填写验收意见"
              resize="none"
              :autosize="{ minRows: 3, maxRows: 3 }"
              maxlength="200"
              show-word-limit
            >
            </el-input>
          </el-form-item>
          <el-form-item label="附件" prop="accessoryUrl">
            <el-upload
              action="string"
              :on-remove="handleRemove"
              :before-upload="beforeUpload"
              :limit="1"
              :on-exceed="handleExceed"
              :http-request="handleUpload"
              :file-list="fileList"
            >
              <el-button size="small" type="primary">点击上传</el-button>
            </el-upload>
          </el-form-item>
          <el-form-item label="签字" prop="signatureUrl" class="signatureWrap">
            <div v-if="form.signatureUrl" class="imgWrap" @click="showSin = true">
              <img style="width: 100%; height: 100%" :src="form.signatureUrl" alt="" />
            </div>
            <el-button v-else size="small" type="primary" @click="showSin = true">去签名</el-button>
          </el-form-item>
        </el-form>
        <div style="text-align: right">
          <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db" @click="closeContent">取消</el-button>
          <el-button v-loading="acceptanceLoding" type="primary" @click="saveAcceptance">保存</el-button>
        </div>
        <template v-if="showSin">
          <signature :imgSrc="form.signatureUrl" :width="500" :height="300" @change="getImg" @show="show"></signature>
        </template>
      </div>
    </el-tab-pane>
    <el-tab-pane label="任务详情" name="2">
      <el-table
        ref="materialTable"
        v-loading="tableLoading"
        :data="tableData"
        border
        :header-cell-style="{ background: '#F6F5FA' }"
        style="width: 100%"
        :cell-style="{ padding: '8px 0 8px 0' }"
        stripe
        highlight-current-row
        :empty-text="emptyText"
        row-key="id"
        lazy
        @expand-change="expandChange"
      >
        <el-table-column type="expand">
          <template slot-scope="prop">
            <el-table
              :data="prop.row.taskChildren || []"
              stripe
              :header-cell-style="{ background: '#F6F5FA' }"
              :cell-style="{ padding: '8px 0 8px 0' }"
              border
              highlight-current-row
            >
              <el-table-column label="序号" type="index"></el-table-column>
              <el-table-column label="巡检点" prop="inspectionPointName"></el-table-column>
              <el-table-column label="结果">
                <template slot-scope="scope">
                  <span v-if="scope.row.state == '2'" :style="{ color: '#3562DB' }">合格</span>
                  <span v-if="scope.row.state == '3'" :style="{ color: 'red' }">不合格</span>
                  <span v-if="scope.row.state == '4'" :style="{ color: '#FF9435' }">异常报修</span>
                </template>
              </el-table-column>
              <el-table-column label="巡检时间" prop="excuteTime"></el-table-column>
              <el-table-column label="巡检人员" prop="implementPersonName"></el-table-column>
            </el-table>
          </template>
        </el-table-column>
        <el-table-column label="任务名称" prop="taskName"> </el-table-column>
        <el-table-column show-overflow-tooltip label="周期类型">
          <template slot-scope="scope">
            <span>{{ cycleTypeFn(scope.row) }}</span>
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip :label="systemType == '2' ? '应保养日期' : '应巡日期'">
          <template slot-scope="scope">
            <span>{{ moment(scope.row.taskStartTime).format('YYYY-MM-DD') }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="distributionTeamName" show-overflow-tooltip :label="systemType == '2' ? '保养部门' : '巡检部门'"></el-table-column>
      </el-table>
    </el-tab-pane>
  </el-tabs>
</template>
<script>
import axios from 'axios'
import signature from './signatureComponent.vue'
import moment from 'moment'
import _ from 'lodash'
export default {
  components: {
    signature
  },
  props: ['listData', 'systemType', 'cycleTypeList'],
  data() {
    return {
      moment,
      tabActive: '1',
      form: {
        acceptancePersonName: this.$store.state.user.userInfo.user.staffName,
        inspectionReportId: '',
        acceptanceOpinion: '',
        accessoryUrl: '',
        accessoryName: '',
        signatureUrl: ''
      },
      rules: {
        acceptancePersonName: [{ required: true, message: '请选择巡检人员', trigger: 'blur' }],
        inspectionReportId: [{ required: true, message: '请选择验收结论', trigger: 'blur' }],
        signatureUrl: [{ required: true, message: '请签字', trigger: 'blur' }]
      },
      conclusions: [
        {
          id: '1',
          name: '验收通过'
        },
        {
          id: '2',
          name: '验收驳回'
        }
      ],
      fileList: [],
      showSin: false,
      acceptanceLoding: false,
      pageLoading: false,
      tableLoading: false,
      tableData: [],
      emptyText: '暂无数据'
    }
  },
  created() {
    this.tableData = _.cloneDeep(this.listData)
  },
  methods: {
    handleClick(tab, event) {
      this.tabActive = tab.name
    },
    // 周期类型
    cycleTypeFn(row) {
      const item = this.cycleTypeList.filter((i) => i.cycleType == row.cycleType)
      return item[0].label
    },
    beforeUpload(file) {
      this.fileList = [file]
      let sizes = 0
      this.fileList.forEach((i) => {
        sizes += i.size
      })
      if (sizes / 1024 > 20000) {
        this.$message({
          type: 'warning',
          message: '上传文件大小不能超过20MB'
        })
        return false
      }
    },
    // 图片超过最大长度时的事件
    handleExceed() {
      this.$message({
        type: 'info',
        message: '最多上传1份附件'
      })
    },
    // 图片上传
    handleUpload(file) {
      const urlData = new FormData()
      urlData.append('file', file.file)
      urlData.append('platform', 1)
      this.pageLoading = true
      axios({
        method: 'post',
        url: __PATH.VUE_ICIS_API + 'file/upload',
        data: urlData,
        // 上传进度
        onUploadProgress: (progressEvent) => {
          let num = ((progressEvent.loaded / progressEvent.total) * 100) | 0 // 百分比
          file.onProgress({ percent: num }) // 进度条
        },
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      })
        .then((res) => {
          this.pageLoading = false
          file.onSuccess()
          this.form.accessoryUrl = res.data.data.fileKey
          this.form.accessoryName = file.file.name
        })
        .catch(() => {
          this.pageLoading = false
          this.$message.error(res.data.message)
        })
    },
    handleRemove(file, fileList) {
      this.fileList = fileList
      this.form.accessoryUrl = ''
      this.form.accessoryName = ''
    },
    getImg(img, status) {
      this.form.signatureUrl = img
      this.showSin = status
    },
    show(status) {
      this.showSin = status
    },
    saveAcceptance() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const idArr = this.listData.map((i) => i.id)
          const params = {
            auftragnehmerId: this.$store.state.user.userInfo.staffId, // 验收人id
            auftragnehmerName: this.$store.state.user.userInfo.user.staffName, // 验收人name
            planPersonCode: this.$store.state.user.userInfo.staffId, // 当前人员id
            inspectionReportId: this.form.inspectionReportId, // 验收结论id(0：未验收 1：验收通过 2：验收驳回)
            inspectionReportName: this.conclusions.find((i) => i.id == this.form.inspectionReportId).name, // 验收结论name
            acceptanceOpinion: this.form.acceptanceOpinion, // 验收意见
            accessoryArr: this.form.accessoryName, // 附件Name
            accessoryUrl: this.form.accessoryUrl, // 附件url
            signatureUrl: this.form.signatureUrl, // 签字
            ids: idArr.join(',') // 任务id
          }
          this.$api.taskAcceptanceInspection(params).then((res) => {
            if (res.code == '200') {
              this.$message.success('验收成功')
              this.$emit('close')
              this.$emit('updata')
            } else {
              this.$message.error('验收失败')
            }
          })
        }
      })
    },
    closeContent() {
      this.$emit('close')
    },
    expandChange(row, expandedRows) {
      const data = {
        taskId: row.id,
        pageNo: 1,
        pageSize: 999
      }
      this.$api.getInspectionPointList(data).then((res) => {
        if (res.code == '200') {
          if (res.code == '200') {
            const listArr = []
            res.data.list.forEach((i) => {
              let items = JSON.parse(i.particulars)
              items.pointId = i.id
              items.carryOutFlag = i.carryOutFlag
              items.excuteTime = i.excuteTime
              items.guaranteeCode = i.guaranteeCode
              items.spyScan = i.spyScan
              items.state = i.state
              items.implementPersonName = i.implementPersonName
              listArr.push(items)
            })
            // listArr.sort((a, b) => a.sort - b.sort)
            this.$set(row, 'taskChildren', listArr)
          }
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.signatureWrap) {
  .el-form-item__content {
    display: flex;
    align-items: flex-end;
    .imgWrap {
      height: 100px;
    }
  }
}
</style>
