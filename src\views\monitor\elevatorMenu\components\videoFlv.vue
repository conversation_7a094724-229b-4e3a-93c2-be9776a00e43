<!--
 * @Author: hedd
 * @Date: 2023-04-25 20:21:22
 * @LastEditTime: 2023-08-08 13:50:51
 * @FilePath: \ihcrs_pc\src\views\monitor\elevatorMenu\components\videoFlv.vue
 * @Description:
-->
<template>
  <video id="videoElement" ref="videoElement" muted controls autoplay width="100%" height="100%"></video>
</template>
<script>
// import flvjs from 'flv.js'
export default {
  name: 'videoFlv',
  props: {
    url: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      flvPlayer: null,
      vloading: false
    }
  },
  mounted() {
  },
  destroyed() {
    this.destroyFlvjs()
  },
  methods: {
    initFlvjs(url) {
      this.destroyFlvjs()
      if (flvjs.isSupported()) {
        var videoElement = document.getElementById('videoElement')
        this.flvPlayer = flvjs.createPlayer(
          {
            type: 'flv',
            isLive: true,
            url: url,
            hasAudio: false // 禁止音频
          }
        )
        this.flvPlayer.attachMediaElement(videoElement)
        this.flvPlayer.load()
        this.flvPlayer.play()
      }
    },
    destroyFlvjs() {
      if (this.flvPlayer) {
        this.flvPlayer.pause()
        this.flvPlayer.unload()
        this.flvPlayer.detachMediaElement()
        this.flvPlayer.destroy()
        this.flvPlayer = null
      }
    }
  }
}
</script>
