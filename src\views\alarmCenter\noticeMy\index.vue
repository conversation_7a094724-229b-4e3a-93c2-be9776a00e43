<template>
  <PageContainer>
    <div slot="header">
      <div class="search-header">
        <el-tabs v-model="searchForm.alarmStatus" @tab-click="handleWarnTypeClick">
          <el-tab-pane v-for="(item, index) in alarmTabStatusList" :key="index" :label="`${item.label}(${item.count})`"
            :name="item.value" />
        </el-tabs>
        <div class="search-from">
          <el-select v-model="searchForm.alarmLevel" placeholder="报警等级" clearable>
            <el-option v-for="item in alarmLevelOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
          <el-input v-model="searchForm.alarmId" placeholder="报警ID" clearable></el-input>
          <el-select v-model="searchForm.projectCode" placeholder="报警系统" filterable clearable
            @change="getIncidentAndSourceGroup" @clear="
              () => {
                searchForm.alarmType = []
              }
            ">
            <el-option v-for="item in alarmSourceOptions" :key="item.thirdSystemCode" :label="item.thirdSystemName"
              :value="item.thirdSystemCode"> </el-option>
          </el-select>
          <el-select v-model="searchForm.alarmType" placeholder="报警类型" multiple collapse-tags clearable
            :disabled="!searchForm.projectCode">
            <el-option v-for="item in eventTypeOptions" :key="item.id" :label="item.alarmDictName" :value="item.id">
            </el-option>
          </el-select>
          <el-date-picker v-model="searchForm.dataRange" type="daterange" unlink-panels range-separator="至"
            start-placeholder="报警开始日期" end-placeholder="报警结束日期" value-format="yyyy-MM-dd"
            :picker-options="pickerOptions">
          </el-date-picker>
          <el-select ref="treeSelect" v-model="searchForm.alarmSpaceId" placeholder="报警位置" clearable
            @clear="handleClear">
            <el-option hidden :value="searchForm.alarmSpaceId" :label="areaName"> </el-option>
            <el-tree :data="serverSpaces" :props="serverDefaultProps" :load="serverLoadNode" lazy
              :expand-on-click-node="false" :check-on-click-node="true" @node-click="handleNodeClick">
            </el-tree>
          </el-select>
          <el-input v-model="searchForm.objectName" placeholder="报警对象" clearable />
          <el-select v-if="!isHide && (searchForm.alarmStatus === '0' || searchForm.alarmStatus === '1')"
            v-model="searchForm.alarmAffirm" placeholder="警情类型" clearable>
            <el-option v-for="item in alarmAffirmList" :key="item.value" :label="item.name" :value="item.value">
            </el-option>
          </el-select>
          <el-select v-if="!isHide && searchForm.alarmStatus === '3'" v-model="searchForm.alarmStatusAll"
            placeholder="处理状态" clearable>
            <el-option v-for="item in alarmStatusList" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </div>
        <div class="batch-control">
          <el-button type="primary" plain @click="reset">重置</el-button>
          <el-button type="primary" @click="search">查询</el-button>
          <template v-if="!isHide && searchForm.alarmStatus === '2'">
            <el-button v-for="(item, index) in processedList" :key="index" type="primary"
              @click="batchControlEvent(item.key, selectAlarmList)">
              {{ item.label }}
            </el-button>
          </template>
          <template v-else-if="!isHide">
            <el-button v-for="(item, index) in batchControlList" :key="index" type="primary"
              @click="batchControlEvent(item.key, selectAlarmList)">
              {{ item.label }}
            </el-button>
          </template>
        </div>
      </div>
    </div>
    <div slot="content" style="height: 100%">
      <div class="contentTable">
        <div class="contentTable-main table-content">
          <el-table ref="table" v-loading="loading" border :resizable="false" :data="tableData" height="100%"
            style="width: 100%" @sort-change="tableSortChange" @selection-change="tableSelectChange"
            @row-dblclick="(row) => operating('details', row)">
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column prop="alarmLevel" label="报警等级" sortable="custom" width="110">
              <template slot-scope="scope">
                <span class="alarmLevel" :style="{ background: alarmLevelItem[scope.row.alarmLevel].color }">
                  {{ alarmLevelItem[scope.row.alarmLevel].text }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="alarmSource" label="报警系统" show-overflow-tooltip></el-table-column>
            <el-table-column prop="alarmType" label="报警类型" show-overflow-tooltip></el-table-column>
            <el-table-column prop="alarmStartTime" label="报警时间" sortable="custom" width="170"> </el-table-column>
            <el-table-column prop="alarmSpaceName" label="报警位置" width="170">
              <template slot-scope="scope">
                <el-tooltip effect="dark" :content="scope.row.alarmSpaceName" placement="top">
                  <span class="alarmSpace">{{ showSpaceName(scope.row.alarmSpaceName) }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="alarmObjectName" label="报警对象" show-overflow-tooltip></el-table-column>
            <el-table-column prop="alarmDetails" label="报警描述" show-overflow-tooltip></el-table-column>
            <el-table-column v-if="!isHide" prop="alarmStatus" label="处理状态" width="120">
              <div slot-scope="scope" class="alarmStatus"
                :style="{ color: scope.row.alarmStatus == 0 ? '#FA403C' : scope.row.alarmStatus == 1 ? '#fd9434' : '#67c23a' }">
                <span class="alarmStatusIcon"
                  :style="{ background: scope.row.alarmStatus == 0 ? '#FA403C' : scope.row.alarmStatus == 1 ? '#ff9435' : '#67c23a' }"></span>
                {{ scope.row.alarmStatus == 0 ? '未处理' : scope.row.alarmStatus == 1 ? '处理中' : '已处理' }}
              </div>
            </el-table-column>
            <el-table-column v-if="!isHide" prop="alarmAffirm" label="警情类型" show-overflow-tooltip>
              <template slot-scope="scope">
                <span v-if="scope.row.alarmAffirm == '0'">未确认报警</span>
                <span v-if="scope.row.alarmAffirm == '1'">真实报警</span>
                <span v-if="scope.row.alarmAffirm == '2'">误报</span>
                <span v-if="scope.row.alarmAffirm == '3'">演练</span>
                <span v-if="scope.row.alarmAffirm == '4'">调试</span>
              </template>
            </el-table-column>
            <el-table-column v-if="!isHide" label="关联工单" show-overflow-tooltip>
              <template slot-scope="scope">
                <span v-if="!scope.row.workList.length">无</span>
                <span v-else-if="scope.row.workList.length && scope.row.workList.length > 1"
                  style="color: #3562db; cursor: pointer" @click="operating('details', scope.row, 3)">
                  已关联（{{ scope.row.workList.length }}）
                </span>
                <span v-else style="color: #3562db; cursor: pointer" @click="showWorkOrder(scope.row.workList[0])">
                  {{ scope.row.workList[0].flowtype }}
                </span>
              </template>
            </el-table-column>
            <el-table-column v-if="!isHide" prop="alarmId" label="报警ID" show-overflow-tooltip></el-table-column>
            <el-table-column v-if="!isHide" prop="classic" label="收藏案例" width="80">
              <template slot-scope="scope">
                <span style="color: #3562db; cursor: pointer" @click="collectAlarmRecords(scope.row)">
                  <i :class="`el-icon-star-${scope.row.classic ? 'on' : 'off'}`"></i>
                  {{ scope.row.classic ? '已存' : '未存' }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template slot-scope="scope">
                <el-button type="text" @click="operating('details', scope.row)">查看</el-button>
                <el-button v-if="!isHide && scope.row.alarmStatus == 2" type="text" :disabled="scope.row.isSummary"
                  @click="operating('summary', scope.row)">总结</el-button>
                <el-button v-else-if="!isHide && scope.row.alarmStatus != 2" type="text"
                  :disabled="scope.row.alarmStatus != 0 || (scope.row.disposalTerminal && scope.row.disposalTerminal.indexOf('2') === -1)"
                  @click="operating('handle', scope.row)">
                  处理
                </el-button>
                <el-dropdown v-if="!isHide" @command="command">
                  <span class="el-dropdown-link" style="margin-left: 10px; color: #3562db; cursor: pointer"> 更多<i
                      class="el-icon-arrow-down el-icon--right"></i> </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item :disabled="!!scope.row.workNum"
                      :command="beforeCommand('dispatch', scope.row)">派单</el-dropdown-item>
                    <el-dropdown-item :command="beforeCommand('remark', scope.row)">添加说明</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="contentTable-footer">
          <el-pagination :current-page="pagination.pageNo" :page-sizes="pagination.pageSizeOptions"
            :page-size="pagination.pageSize" :layout="'total, sizes, ->, prev, pager, next, jumper'"
            :total="pagination.total" @size-change="paginationSizeChange" @current-change="paginationCurrentChange">
          </el-pagination>
        </div>
      </div>
      <!-- 处理 -->
      <HandleDrawer v-model="handleDrawerVisible" :selectItems="selectAlarmList" @close="closeHandle" />
      <!-- 屏蔽 -->
      <ScreenDialog v-if="scrDialog" :selectItems="selectAlarmList" :visible.sync="scrDialog"
        @update="closeScrDialog" />
      <!-- 添加说明 -->
      <RemarkDialog v-if="remarkVisible" :visible.sync="remarkVisible" :selectItems="selectAlarmList"
        @close="closeRemark">
      </RemarkDialog>
      <!-- 总结分析 -->
      <SummaryDrawer :visible.sync="summaryVisible" :selectItems="selectAlarmList" @close="closeSummary" />
      <!-- 关联工单详情 -->
      <WorkOrderDetailDialog v-if="workOrderDetailShow" sourceType="alarm" :visible.sync="workOrderDetailShow"
        :alarmDetail="alarmDetail" :dialogDetail="workOderDetailData" />
    </div>
  </PageContainer>
</template>
<script>
import { transData, ListTree } from '@/util'
import { mapGetters } from 'vuex'
import axios from 'axios'
export default {
  name: 'noticeMyIndex',
  components: {
    ScreenDialog: () => import('../alarmDetail/components/screenDialog.vue'),
    HandleDrawer: () => import('../alarmDetail/components/handleDrawer.vue'),
    RemarkDialog: () => import('../alarmDetail/components/remarkDialog.vue'),
    SummaryDrawer: () => import('../alarmDetail/components/summaryDrawer.vue')
  },
  beforeRouteEnter(to, from, next) {
    // 进入页面时，先将当前页面的 name 信息存入 keep-alive 全局状态（当前页面的name需要和路由的name一致）
    next((vm) => {
      // 二级页面存储当前级，多级页面存储多级
      vm.$store.commit('keepAlive/add', 'noticeMyIndex')
    })
  },
  async beforeRouteLeave(to, from, next) {
    // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
    if (!['alarmDetail'].includes(to.name)) {
      // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      alarmLevelItem: {
        0: { text: '通知', color: '#3562DB' },
        1: { text: '一般', color: '#3562DB' },
        2: { text: '紧急', color: '#FF9435' },
        3: { text: '重要', color: '#FA403C' }
      },
      alarmLevelOptions: [
        // 报警等级
        {
          value: '0',
          label: '通知'
        },
        {
          value: '1',
          label: '一般'
        },
        {
          value: '2',
          label: '紧急'
        },
        {
          value: '3',
          label: '重要'
        }
      ],
      alarmTabStatusList: [
        {
          value: '0',
          label: '未处理',
          count: 0,
          key: 'untreatedCount'
        },
        {
          value: '1',
          label: '处理中',
          count: 0,
          key: 'processingCount'
        },
        {
          value: '2',
          label: '已处理',
          count: 0,
          key: 'isDealCount'
        },
        {
          value: '3',
          label: '全部',
          count: 0,
          key: 'allCount'
        }
      ],
      alarmStatusList: [
        {
          label: '未处理',
          value: '0'
        },
        {
          label: '处理中',
          value: '1'
        },
        {
          label: '已处理',
          value: '2'
        }
      ],
      alarmAffirmList: [
        {
          name: '未确认报警',
          value: '0'
        },
        {
          name: '真实报警',
          value: '1'
        },
        {
          name: '误报',
          value: '2'
        },
        {
          name: '演练',
          value: '3'
        },
        {
          name: '调试',
          value: '4'
        }
      ],
      alarmSourceOptions: [], // 报警来源
      eventTypeOptions: [], // 事件类型
      serverSpaces: [], // 空间位置
      spaces: [], // 空间位置
      serverDefaultProps: {
        label: 'ssmName',
        isLeaf: 'leaf',
        children: 'children'
      },
      areaName: '', // 选中 下拉树的name
      pickerOptions: {
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      searchForm: {
        alarmId: '',
        alarmType: [],
        alarmAffirm: '',
        // 搜索条件
        alarmStatus: '0',
        alarmStatusAll: '',
        projectCode: '', // 报警来源
        alarmLevel: '', // 报警等级
        alarmSpaceId: '', // 空间位置
        dataRange: [], // 时间范围
        objectName: '' // 对象id
      },
      processedList: [
        {
          key: 'remark',
          label: '添加说明'
        },
        {
          key: 'export',
          label: '导出'
        }
      ],
      batchControlList: [
        // 批量控制
        {
          key: 'handle',
          label: '开始处理'
        },
        {
          key: 'remark',
          label: '添加说明'
        }
      ],
      loading: false,
      tableData: [],
      pagination: {
        pageSizeOptions: [15, 30, 50, 100],
        pageNo: 1,
        pageSize: 15,
        total: 0
      },
      alarmAffirmItem: {
        0: '未确认',
        1: '真实报警',
        2: '误报',
        3: '演练',
        4: '调试'
      },
      // 列表选中的数据
      selectAlarmList: [],
      // 处理的抽屉
      handleDrawerVisible: false,
      // 屏蔽
      scrDialog: false,
      // 添加说明
      remarkVisible: false,
      // 总结分析
      summaryVisible: false,
      // 排序字段
      timeOrType: '',
      workOrderDetailShow: false,
      workOderDetailData: [],
      alarmDetail: {},
      isHide: false
    }
  },
  computed: {
    ...mapGetters({
      socketMsgs: 'socket/socketMsgs'
    })
  },
  watch: {
    socketMsgs() {
      this.initEvent()
    }
  },
  mounted() {
    this.isHide = this.isSjyyy()
    this.searchForm.alarmStatus = this.$route.query.alarmStatus || '0'
    if (this.isHide) {
      this.searchForm.alarmStatus = '3'
      this.alarmTabStatusList = [
        {
          value: '3',
          label: '全部',
          count: 0,
          key: 'allCount'
        }
      ]
    }
    this.initEvent()
    this.getAlarmSource()
    this.getTreelist()
  },
  activated() {
    this.initEvent()
  },
  deactivated() {
    let nodeList = document.getElementsByClassName('el-tooltip__popper')
    if (nodeList.length) {
      for (let i = 0; i < nodeList.length; i++) {
        nodeList[i].style.display = 'none'
      }
    }
  },
  methods: {
    initEvent() {
      this.getTabsAndCount()
      this.getDataList()
    },
    showSpaceName(val) {
      if (val && val.length > 10) {
        let str = val.split('').reverse().splice(0, 10).reverse().join('')
        return `...${str}`
      } else {
        return val
      }
    },
    getTabsAndCount() {
      this.$api.GetMyAlarmStatisticsCount({ staffId: this.$store.state.user.userInfo.user.staffId }).then((res) => {
        if (res.code == 200) {
          this.alarmTabStatusList.forEach((el) => {
            el.count = res.data[el.key] || 0
          })
        }
      })
    },
    // tabs切换
    handleWarnTypeClick(tab) {
      if (tab.name === '0' || tab.name === '1') {
        this.searchForm.alarmStatusAll = ''
      }
      if (tab.name === '3') {
        this.searchForm.alarmAffirm = ''
      }
      this.selectAlarmList = []
      this.pagination.pageNo = 1
      this.getDataList()
      // this.resetForm()
    },
    // 获取报警来源
    getAlarmSource() {
      this.$api.getAlarmThirdSystemData().then((res) => {
        if (res.code == 200) {
          this.alarmSourceOptions = res.data
        }
      })
    },
    // 获取事件类型以及报警来源分组
    getIncidentAndSourceGroup(val) {
      this.searchForm.alarmType = []
      this.$api.getAlarmThirdTypeData({ thirdSystemCode: val }).then((res) => {
        if (res.code == 200) {
          this.eventTypeOptions = res.data
        }
      })
    },
    // 获取服务空间树形结构
    getTreelist() {
      // this.$api.getStructureTree({}, __PATH.USER_CODE).then((res) => {
      this.$api.getStructureTree({}).then((res) => {
        if (res.code == 200) {
          this.spaces = res.data
          // 增加 懒加载节点
          res.data.map((e) => {
            e.leaf = false
          })
          // 转换为树形结构数据
          this.serverSpaces = transData(res.data, 'id', 'pid', 'children')
        }
      })
    },
    // 下拉树懒加载
    serverLoadNode(node, resolve) {
      if (node.level == 0) {
        return resolve(node.data)
      } else if (node.level > 0 && node.level < 4) {
        return resolve(node.data.children)
      } else {
        return this.spaceTreeChange(node.data, resolve)
      }
    },
    // 空间数据清除
    handleClear() {
      this.searchForm.alarmSpaceId = ''
      this.areaName = ''
    },
    // 选择下拉树 数据
    handleNodeClick(data) {
      this.searchForm.alarmSpaceId = data.id
      this.areaName = data.ssmName
      this.$refs.treeSelect.blur()
    },
    // 服务空间树结构点击获取自己及父节点ID并请求数据实现懒加载
    spaceTreeChange(value, resolve) {
      const child = ListTree(this.spaces, value.pid)
      child.push(value.id)
      let treeId = child.toString()
      let data = {
        current: 1,
        functionDictId: '',
        simCode: treeId,
        size: 999
      }
      this.$api.getSpaceInfoList(data).then((res) => {
        if (res.code == 200) {
          if (typeof resolve === 'function') {
            let treeNodeData = JSON.parse(JSON.stringify(res.data.records))
            treeNodeData.map((e) => {
              e.leaf = true
            })
            resolve(treeNodeData)
          }
        }
      })
    },
    reset() {
      this.searchForm = {
        alarmLevel: '', // 报警等级
        alarmId: '',
        projectCode: '', // 报警系统
        alarmType: [],
        dataRange: [], // 时间范围
        alarmSpaceId: '', // 空间位置
        objectName: '',
        alarmAffirm: '',
        // 搜索条件
        alarmStatus: this.searchForm.alarmStatus
      }
      this.areaName = ''
      this.search()
    },
    search() {
      this.getTabsAndCount()
      this.pagination.pageNo = 1
      this.getDataList()
    },
    // 获取全部报警记录
    getDataList() {
      let { alarmId, projectCode, alarmType, alarmLevel, alarmSpaceId, dataRange, alarmStatus, objectName, alarmAffirm, alarmStatusAll } = this.searchForm
      let params = {
        alarmId,
        timeOrType: this.timeOrType,
        pageNo: this.pagination.pageNo,
        pageSize: this.pagination.pageSize,
        projectCode,
        alarmStatus: alarmStatus === '3' ? '' : alarmStatus,
        objectName,
        alarmType: alarmType.join(','),
        alarmLevel,
        alarmSpaceId,
        alarmStatusAll,
        startTime: dataRange.length ? dataRange[0] : '',
        endTime: dataRange.length ? dataRange[1] : '',
        alarmAffirm,
        shield: '',
        staffId: this.$store.state.user.userInfo.user.staffId
      }
      this.loading = true
      this.$api
        .GetMyAlarmRecord(params)
        .then((res) => {
          this.loading = false
          if (res.code == 200) {
            this.tableData = res.data ? res.data.records : []
            this.pagination.total = res.data ? res.data.total : 0
          } else {
            this.tableData = []
            this.pagination.total = 0
          }
        })
        .catch((err) => {
          this.loading = false
        })
    },
    tableSelectChange(data) {
      this.selectAlarmList = data
      this.$nextTick(() => {
        this.$forceUpdate()
      })
    },
    // 列表更多按钮
    beforeCommand(type, row) {
      return {
        type: type,
        row: row
      }
    },
    command(data) {
      this.$refs.table.clearSelection()
      // 派单
      if (data.type == 'dispatch') {
        this.$confirm('是否确认派发确警工单?', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.oneKeyDispatch(data.row)
        })
      }
      // 添加说明
      if (data.type == 'remark') {
        this.selectAlarmList = [data.row]
        this.remarkVisible = !this.remarkVisible
      }
    },
    // 子/孙组件流程操作
    operating(type, selectItem, tabType) {
      this.$refs.table.clearSelection()
      // 报警详情
      if (type == 'details') {
        this.$router.push({
          path: '/allAlarm/alarmDetail',
          query: {
            tabType: tabType,
            alarmId: selectItem.alarmId
          }
        })
      }
      // 开始处理
      if (type == 'handle') {
        this.selectAlarmList = [selectItem]
        this.handleDrawerVisible = true
      }
      // 总结分析
      if (type == 'summary') {
        this.selectAlarmList = [selectItem]
        this.summaryVisible = true
      }
    },
    closeHandle() {
      this.selectAlarmList = []
      this.initEvent()
    },
    // 一键派单
    oneKeyDispatch(row) {
      const userInfo = this.$store.state.user.userInfo.user
      let param = {
        alarmId: row.alarmId,
        alarmLevel: row.alarmLevel,
        alarmSourceName: row.alarmSource,
        incidentName: row.incidentName,
        spaceId: row.alarmSpaceId,
        userName: userInfo.staffName,
        userId: userInfo.staffId,
        projectCode: row.projectCode,
        operationSource: 0
      }
      this.$api.OneKeyDispatch(param).then((res) => {
        if (res.code == 200) {
          this.$message({
            message: '已派单',
            type: 'success'
          })
          this.getDataList()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    batchControlEvent(key, arr) {
      this.selectAlarmList = []
      this.selectAlarmList = arr
      if (key !== 'export') {
        if (!arr.length) {
          this.$message.error('请选择报警')
          return
        }
      }
      let obj = this.selectAlarmList.filter((el) => {
        return el.alarmStatus !== 0 || (el.disposalTerminal && el.disposalTerminal.indexOf('2') === -1)
      })
      if (key === 'handle') {
        this.$confirm('开始批量处理所选报警？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          if (obj.length) {
            this.$message.error('请选择未处理的报警或者只可在web端处理的报警')
            return
          } else {
            this.handleDrawerVisible = true
          }
        })
      } else if (key === 'export') {
        // 导出
        const userInfo = this.$store.state.user.userInfo.user
        let params = {
          ...this.searchForm,
          hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
          unitCode: userInfo.unitCode ?? 'BJSYGJ',
          staffId: this.$store.state.user.userInfo.user.staffId
        }
        if (this.searchForm.dataRange.length) {
          params.startTime = this.searchForm.dataRange[0]
          params.endTime = this.searchForm.dataRange[1]
        }
        if (this.searchForm.alarmType.length) {
          params.alarmType = this.searchForm.alarmType.join(',')
        } else {
          delete params.alarmType
        }
        if (params.alarmStatus === '3') {
          params.alarmStatus = ''
        }
        delete params.dataRange
        axios({
          method: 'post',
          url: __PATH.VUE_WARN_API + 'alarm/record/exportAlarmRecord',
          data: params,
          responseType: 'blob',
          headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer ' + this.$store.state.user.token
            // 'operation-type': 4
          }
        })
          .then((res) => {
            let name = ''
            let index = this.alarmTabStatusList.findIndex((item) => item.value === this.searchForm.alarmStatus)
            if (index !== -1) {
              name = this.alarmTabStatusList[index].label
            }
            const blob = new Blob([res.data])
            const url = window.URL.createObjectURL(blob)
            const a = document.createElement('a')
            a.href = url
            a.download = decodeURI(`${name}报警记录.xls`)
            a.click()
          })
          .catch(() => {
            this.$message.error('导出失败')
          })
      } else {
        if (obj.length) {
          this.$message.error('请选择未处理的报警添加说明')
          return
        } else {
          this.remarkVisible = true
        }
      }
    },
    // 关闭屏蔽弹框
    closeScrDialog(val) {
      this.scrDialog = false
      this.selectAlarmList = []
      this.getDataList()
    },
    closeRemark() {
      this.remarkVisible = false
      this.selectAlarmList = []
      this.getDataList()
    },
    closeSummary() {
      this.summaryVisible = false
      this.selectAlarmList = []
      this.getDataList()
    },
    // 存为经典案例
    collectAlarmRecords(row) {
      let param = {
        alarmId: row.alarmId,
        classic: row.classic == 1 ? '0' : '1',
        operationSource: 0
      }
      this.$api.CollectAlarmRecords(param).then((res) => {
        if (res.code == 200) {
          this.getDataList()
          this.$message.success(res.msg)
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    tableSortChange(column) {
      if (column.prop === 'alarmStartTime') {
        this.timeOrType = column.order ? (column.order == 'ascending' ? 3 : 2) : ''
      } else {
        this.timeOrType = column.order ? (column.order == 'ascending' ? 1 : 0) : ''
      }
      this.getDataList()
    },
    paginationSizeChange(val) {
      this.pagination.pageSize = val
      this.pagination.pageNo = 1
      this.getDataList()
    },
    paginationCurrentChange(val) {
      this.pagination.pageNo = val
      this.getDataList()
    },
    // 打开关联工单
    showWorkOrder(row) {
      this.alarmDetail = {
        projectCode: row.projectCode,
        alarmSpaceId: row.alarmSpaceId,
        alarmId: row.alarmId
      }
      this.workOderDetailData = [
        {
          workTypeName: row.workTypeName,
          id: row.workNum,
          active: true
        }
      ]
      this.workOrderDetailShow = true
    }
  }
}
</script>
<style lang="scss" scoped>
.search-header {
  padding: 0 8px 10px;

  .search-from {
    padding-bottom: 12px;

    &>div {
      margin-top: 12px;
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
    }
  }
}

.contentTable {
  height: calc(100% - 16px);
  background: #fff;
  border-radius: 4px;
  padding: 16px;
  display: flex;
  margin-top: 16px;
  flex-direction: column;
  overflow: auto;

  .contentTable-main {
    flex: 1;
    overflow: auto;
  }

  .contentTable-footer {
    padding: 10px 0 0;
  }

  .alarmLevel {
    padding: 3px 6px;
    border-radius: 4px;
    color: #fff;
    line-height: 14px;
  }

  .alarmStatus {
    position: relative;
    display: inline-block;
    padding-left: 12px;

    .alarmStatusIcon {
      display: inline-block;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 8px;
      height: 8px;
      border-radius: 100%;
    }
  }

  .collectIcon {
    font-size: 16px;
    margin-right: 4px;
  }
}
</style>
