<template>
  <PageContainer>
    <div slot="content">
      <div class="title">添加模板管理</div>
      <div class="pageContainer">
        <el-form ref="form" class="form" :model="form" :rules="rules" label-width="140px">
          <el-form-item label="模板名称" prop="name">
            <el-input v-model="form.name" style="width: 100%" placeholder="请输入模板名称" maxlength="20" :disabled="detailDisabled"> </el-input>
          </el-form-item>
          <el-form-item label="考察对象类型" prop="type">
            <el-select v-model="form.type" placeholder="请选择周期类型" style="width: 100%" :disabled="detailDisabled">
              <el-option v-for="item in typeList" :key="item.node" :label="item.nodeName" :value="item.node"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="权重和得分" prop="weightsState">
            <el-radio-group v-model="form.weightsState" :disabled="detailDisabled" @change="weightsStateChange">
              <el-radio :label="1">开启</el-radio>
              <el-radio :label="0">关闭</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="考察说明" prop="remark">
            <el-input v-model="form.remark" type="textarea" maxlength="500" show-word-limit :disabled="detailDisabled"> </el-input>
          </el-form-item>
          <div style="width: 140px; margin-bottom: 16px; padding-right: 12px">考察内容</div>
          <el-form-item label="" label-width="0">
            <el-table v-if="form.weightsState === 1" :key="tableKey" :data="form.inspectContentList" border stripe style="width: 100%">
              <el-table-column prop="name" label="指标名称" width="140"> </el-table-column>
              <el-table-column label="权重" width="140">
                <template slot-scope="scope">
                  <el-input :ref="`casc${scope.$index}`" v-model="form.inspectContentList[scope.$index].weights" :disabled="detailDisabled">
                    <template slot="append">%</template>
                  </el-input>
                </template>
              </el-table-column>
              <el-table-column label="得分计算方法">
                <template slot-scope="scope">
                  <div class="scoreComputedColumn">
                    <div class="scoreColumn">
                      <div>{{ scope.row.scoreMechanism == 1 ? '扣分' : '得分' }}</div>
                      <div v-for="(item, index) in scope.row.calculationMethodList" :key="index">{{ scope.row.scoreMechanism == 1 ? item.scoreValue : item.score }}</div>
                    </div>
                    <div class="scoreColumn">
                      <div>{{ scope.row.intervalNumber == 1 ? '单位数量' : '指标数据' }}</div>
                      <div v-for="(item, index) in scope.row.calculationMethodList" :key="index">
                        <template v-if="scope.row.intervalNumber == 1">
                          <span>{{ item.number }}</span>
                        </template>
                        <template v-else>
                          <span v-if="scope.row.dataType === 0">{{ item.min }} - {{ item.max }}</span>
                          <span v-else-if="scope.row.dataType === 1">{{ item.min }}% - {{ item.max }}%</span>
                          <span v-else>{{ computedTime(item.min) }} - {{ computedTime(item.max) }}</span>
                        </template>
                      </div>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="remark" label="参考值"> </el-table-column>
              <el-table-column label="指标说明" prop="illustrate"> </el-table-column>
              <el-table-column v-if="!detailDisabled" label="操作" width="120">
                <template slot-scope="scope">
                  <el-button type="text" @click="addTarget(scope.row)">编辑</el-button>
                  <el-button type="text" @click="del(scope.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-table v-else :key="tableKey" border stripe :data="form.inspectContentList" style="width: 100%">
              <el-table-column prop="name" label="指标名称"> </el-table-column>
              <el-table-column prop="remark" label="参考值"> </el-table-column>
              <el-table-column label="指标说明" prop="illustrate"> </el-table-column>
              <el-table-column v-if="!detailDisabled" label="操作" width="120">
                <template slot-scope="scope">
                  <el-button type="text" @click="del(scope.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-form>
        <el-button v-if="!detailDisabled" style="margin-top: 16px" type="primary" @click.native="addTarget()">添加指标</el-button>
      </div>
      <div class="footer">
        <el-button v-if="!detailDisabled" type="primary" @click="submit">保存</el-button>
        <el-button type="primary" plain @click="goBack">取消</el-button>
      </div>
      <TargetDialog v-if="visible" ref="targetDialog" v-model="visible" :type="form.weightsState" :title="dialogTitle" :selectedId="selectedListId" @success="addSuccess" />
    </div>
  </PageContainer>
</template>
<script>
import TargetDialog from './targetDialog.vue'
export default {
  name: 'updateTemplate',
  components: {
    TargetDialog
  },
  data() {
    return {
      typeList: [],
      targetList: [],
      form: {
        inspectContentList: [],
        name: '',
        type: '',
        weightsState: 1
      },
      rules: {
        name: { required: true, message: '请输入周期名称', trigger: 'blur' },
        type: { required: true, message: '请选择周期类型', trigger: 'blur' }
      },
      detailDisabled: false,
      visible: false,
      dialogTitle: '',
      selectedId: [], // 已选指标id
      tableKey: Math.random()
    }
  },
  mounted() {
    this.getTypeList()
    if (this.$route.query.id) {
      this.getDetail()
    }
    if (this.$route.query.type === 'detail') {
      this.detailDisabled = true
    }
  },
  methods: {
    // 获取详情
    getDetail() {
      this.$api.targetTmpDetail({ id: this.$route.query.id }).then((res) => {
        if (res.code === '200') {
          this.form = res.data
          res.data.inspectContentList.forEach((i) => {
            if (i.dataType == 0) {
              i.calculationMethodList.forEach((el) => {
                i.intervalNumber = el.intervalNumber
                i.scoreMechanism = el.scoreMechanism
              })
            }
          })
        }
      })
    },
    // 计算时间，列表回显
    computedTime(val) {
      let str = ''
      if (Number(val) < 60) {
        str = `0小时${val}分钟`
      } else {
        str = `${parseInt(val / 60)}小时${val % 60}分钟`
      }
      return str
    },
    getTypeList() {
      this.$api.templateType().then((res) => {
        if (res.code === '200') {
          this.typeList = res.data
        }
      })
    },
    weightsStateChange(val) {
      this.$confirm('权重和得分更改，需要重新添加指标', '提示', {
        cancelButtonText: '取消',
        confirmButtonText: '确定',
        type: 'warning'
      })
        .then(() => {
          this.form.inspectContentList = []
        })
        .catch(() => {
          this.form.weightsState = val === 1 ? 0 : 1
        })
    },
    addTarget(row) {
      this.selectedListId = []
      this.form.inspectContentList.forEach((el) => {
        this.selectedListId.push(el.indicatorLibraryId)
      })
      this.dialogTitle = row ? '编辑指标' : '添加指标'
      this.visible = true
      if (row) {
        this.$nextTick(() => {
          if (row.dataType === 2) {
            row.calculationMethodList.forEach((el) => {
              el.minTime = [
                {
                  hours: parseInt(el.min / 60),
                  minute: el.min % 60
                }
              ]
              el.maxTime = [
                {
                  hours: parseInt(el.max / 60),
                  minute: el.max % 60
                }
              ]
            })
          } else if (row.dataType == 0) {
            this.$refs.targetDialog.$refs.openTargetTemplate._data.numberType = row.scoreMechanism
            this.$refs.targetDialog.$refs.openTargetTemplate._data.intervalType = row.intervalNumber
          }
          this.$refs.targetDialog.$refs.openTargetTemplate.selectSingleRow(row, false)
        })
      }
    },
    addSuccess(val) {
      let result = []
      this.form.inspectContentList.forEach((item) => {
        if (val.some((el) => el.indicatorLibraryId === item.indicatorLibraryId)) {
          result.push(item)
        }
      })
      if (!result.length) {
        this.form.inspectContentList = [...this.form.inspectContentList, ...val]
      }
      this.tableKey = Math.random()
      this.visible = false
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          // 验证考察内容的数据是否填写完整，并且所有权重综合不能超过100%
          if (this.form.weightsState) {
            let sum = this.form.inspectContentList.reduce((pre, next) => {
              return pre + Number(next.weights)
            }, 0)
            if (sum !== 100) {
              this.$message.error('权重总和不等于100')
              return
            }
          }
          let header = {}
          if (this.form.id) {
            header = {
              'operation-type': 2,
              'operation-name': this.form.name,
              'operation-id': this.form.id
            }
          } else {
            header = {
              'operation-type': 1
            }
          }
          this.form.inspectContentList.forEach((i) => {
            if (this.form.weightsState === 1 && i.dataType == 0) {
              i.calculationMethodList.forEach((el) => {
                el.intervalNumber = i.intervalNumber
                el.scoreMechanism = i.scoreMechanism
              })
            }
          })
          this.$api.saveTemplate(this.form, header).then((res) => {
            if (res.code === '200') {
              this.$message.success('操作成功')
              this.goBack()
            }
          })
        }
      })
    },
    goBack() {
      this.$router.go(-1)
    },
    del(i) {
      this.$confirm('是否确定删除该指标？', '提示', {
        cancelButtonText: '取消',
        confirmButtonText: '确定',
        type: 'warning'
      })
        .then(() => {
          this.form.inspectContentList.splice(i, 1)
        })
        .catch(() => {})
    }
  }
}
</script>
<style lang="scss" scoped>
.container-content > div {
  background-color: #fff;
  height: 100%;
  position: relative;
  .pageContainer {
    width: 80%;
    padding: 16px 24px;
    overflow: auto;
    height: calc(100% - 104px);
  }
}
.footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  padding: 10px 20px;
  background-color: #fff;
  text-align: right;
  border-top: 1px solid #ebeef5;
}
.title {
  font-size: 20px;
  font-weight: 700;
  color: #303133;
  padding: 12px 20px 0;
  margin-bottom: 12px;
}
.scoreComputedColumn {
  display: flex;
  .scoreColumn {
    padding: 4px 0;
    &:first-child {
      width: 30%;
    }
    &:last-child {
      width: 70%;
    }
  }
}
</style>
