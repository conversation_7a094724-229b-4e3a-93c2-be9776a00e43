/*
 * @Author: ycw
 * @Date: 2020-05-26 14:56:38
 * @LastEditors: hedd heding<PERSON>@sinomis.com
 * @LastEditTime: 2025-06-18 16:09:57
 * @Description:
 */
import moment from 'moment'
import { Message } from 'element-ui'
import store from '@/store/index'
export default {
  /**
   * 将json串转换成树形结构
   * @param a 树dataList
   * @param idStr 树节点id string 'id'
   * @param pidStr 树parentId string '树parentId'
   * @param chindrenStr children string 'children'
   * @returns {Array}
   */
  transData(a, idStr, pidStr, chindrenStr, extraParameter, isDisabled = false) {
    const r = []
    const hash = {}
    const id = idStr
    const pid = pidStr
    const children = chindrenStr
    const len = a.length
    let i = 0
    let j = 0
    for (; i < len; i++) {
      hash[a[i][id]] = a[i]
    }
    for (; j < len; j++) {
      if (isDisabled) {
        a[j]['disabled'] = false
      }
      const aVal = a[j]
      const hashVP = hash[aVal[pid]]
      if (hashVP) {
        !hashVP[children] && (hashVP[children] = [])
        hashVP[children].push(aVal)
      } else {
        r.push(aVal)
      }
      // 查找已部署节点id集
      if (extraParameter && aVal.state === '1') extraParameter.push(aVal.id)
    }
    return r
  },
  setHeightNew: (cell, count, extra) => {
    return cell * count + extra
  },
  // setHeight: (type) => {
  //   let screenHeight = window.screen.height
  //   console.log(screenHeight, 'screenHeight')
  //   if (type == 1) {
  //     if (900 < screenHeight <= 1080) {
  //       return 10 * 40 + 42
  //     }
  //   } else if (type == 2) {
  //     if (screenHeight <= 900 && 768 < screenHeight) {
  //       return 3 * 40 + 52
  //     }
  //     // if (901 < screenHeight <= 1080) {
  //     if (screenHeight <= 1080 && 901 < screenHeight) {
  //       return 5 * 40 + 52
  //     }
  //   }
  //   if (type == 3) {
  //     if (screenHeight <= 768) {
  //       return 160
  //     }
  //     if (screenHeight <= 900 && 768 <= screenHeight) {
  //       return 2 * 40 + 52
  //     }
  //     if (900 < screenHeight && screenHeight <= 1080) {
  //       return 5 * 40 + 52
  //     }
  //   }
  //   if (type == 4) {
  //     if (screenHeight <= 768) {
  //       return 215
  //     }
  //     if (screenHeight <= 900 && 768 < screenHeight) {
  //       return 3 * 57 + 52
  //     }
  //     if (screenHeight <= 1080 && 901 < screenHeight) {
  //       return 6 * 57 + 52
  //     }
  //   }
  //   if (type == 5) {
  //     if (screenHeight <= 768) {
  //       return 5 * 40 - 12
  //     }
  //     if (screenHeight <= 900 && 768 < screenHeight) {
  //       return 5 * 40 + 49
  //     }
  //     if (screenHeight <= 1080 && 901 < screenHeight) {
  //       return 6 * 40
  //     }
  //   }
  //   if (type == 6) {
  //     if (screenHeight <= 768) {
  //       return 10 * 30 + 45
  //     }
  //     if (screenHeight <= 900 && 768 < screenHeight) {
  //       return 10 * 30 + 25
  //     }
  //     if (900 < screenHeight <= 1080) {
  //       return 10 * 40 + 49
  //     }
  //   }
  //   if (type == 7) {
  //     if (screenHeight <= 768) {
  //       return 10 * 30 + 25
  //     }
  //     if (screenHeight <= 900 && 768 < screenHeight) {
  //       return 8 * 40 + 52
  //     }
  //     if (screenHeight <= 1080 && 901 < screenHeight) {
  //       return 13 * 40 + 52
  //     }
  //   }
  //   if (type == 8) {
  //     if (900 < screenHeight <= 1080) {
  //       return 7 * 40 + 52
  //     }
  //     if (768 <= screenHeight < 900) {
  //       return 7 * 30 + 52
  //     }
  //   }
  //   if (type == 9) {
  //     if (screenHeight <= 768) {
  //       return 12 * 30
  //     }
  //     if (screenHeight <= 900 && 768 < screenHeight) {
  //       return 9 * 30 + 48
  //     }
  //     if (screenHeight <= 1080 && 901 < screenHeight) {
  //       return 14 * 40 + 48
  //     }
  //   }
  //   if (type == 10) {
  //     if (screenHeight <= 768) {
  //       return 6 * 30 + 10
  //     }
  //     if (screenHeight <= 900 && 768 < screenHeight) {
  //       return 4 * 50 + 20
  //     }
  //     if (screenHeight <= 1080 && 901 < screenHeight) {
  //       return 7 * 50 + 20
  //     }
  //   }
  //   if (type == 11) {
  //     if (screenHeight <= 768) {
  //       return 14 * 30 + 10
  //     }
  //     if (screenHeight <= 900 && 768 < screenHeight) {
  //       return 10 * 30 + 25
  //     }
  //     if (screenHeight <= 1080 && 901 < screenHeight) {
  //       return 15 * 40 + 25
  //     }
  //   }
  //   if (type == 12) {
  //     if (screenHeight <= 768) {
  //       return 440
  //     }
  //     if (screenHeight <= 900 && 768 < screenHeight) {
  //       540
  //     }
  //     if (screenHeight <= 1080 && 901 < screenHeight) {
  //       return 15 * 40 + 35
  //     }
  //   }
  //   if (type == 13) {
  //     if (screenHeight <= 768) {
  //       return 15 * 30 + 25
  //     }
  //     if (screenHeight <= 900 && 768 < screenHeight) {
  //       return 11 * 30 + 35
  //     }
  //     if (screenHeight <= 1080 && 901 < screenHeight) {
  //       return 16 * 40 + 35
  //     }
  //   }
  //   if (type == 14) {
  //     if (screenHeight <= 768) {
  //       return 7 * 31 - 30
  //     }
  //     if (screenHeight <= 900 && 768 <= screenHeight) {
  //       return 2 * 40 + 52
  //     }
  //     if (900 < screenHeight && screenHeight <= 1080) {
  //       return 6 * 40 + 46
  //     }
  //   }
  //   if (type == 15) {
  //     if (screenHeight <= 768) {
  //       return 3 * 40 + 50
  //     }
  //     if (screenHeight <= 900 && 768 < screenHeight) {
  //       return 5 * 40 + 20
  //     }
  //     if (900 < screenHeight && screenHeight <= 1080) {
  //       return 5 * 40 + 10
  //     }
  //   }
  //   if (type == 16) {
  //     if (screenHeight <= 768) {
  //       return 510
  //     }
  //     if (screenHeight <= 900 && 768 < screenHeight) {
  //       return 540
  //     }
  //     if (screenHeight <= 1080 && 901 < screenHeight) {
  //       return 15 * 40 + 35
  //     }
  //   }
  //   if (type == 17) {
  //     if (screenHeight <= 768) {
  //       return 5 * 31
  //     }
  //     if (screenHeight <= 900 && 768 <= screenHeight) {
  //       return 2 * 40 + 52
  //     }
  //     if (900 < screenHeight && screenHeight <= 1080) {
  //       return 6 * 40 + 46
  //     }
  //   }
  //   if (type == 18) {
  //     if (screenHeight <= 768) {
  //       return 300
  //     }
  //     if (screenHeight <= 900 && 768 < screenHeight) {
  //       return 5 * 40 + 49
  //     }
  //     if (screenHeight <= 1080 && 901 < screenHeight) {
  //       return 10 * 40 + 49
  //     }
  //   }
  //   if (type == 19) {
  //     if (screenHeight <= 768) {
  //       return 300
  //     }
  //     if (screenHeight <= 900 && 768 < screenHeight) {
  //       return 5 * 40 + 49
  //     }
  //     if (screenHeight <= 1080 && 901 < screenHeight) {
  //       return 9 * 40 + 25
  //     }
  //   }
  //   if (type == 20) {
  //     if (screenHeight <= 768) {
  //       return 7 * 30 + 10
  //     }
  //     if (screenHeight <= 900 && 768 < screenHeight) {
  //       return 8 * 30 + 25
  //     }
  //     if (screenHeight <= 1080 && 901 < screenHeight) {
  //       return 13 * 40
  //     }
  //   }
  // },
  tableLabel: (type) => {
    const labelObj = {
      init: '暂无数据',
      search: '未查到任何记录，请放大查询条件试试！',
      offLine: '查询失败，请检查您的网络…'
    }
    return labelObj[type] || '暂无数据'
  },
  // 用户名
  validateUserName: (val) => {
    if (!val) {
      return true
    } else {
      const reg = /[a-zA-Z0-9_]{1,18}$/
      return reg.test(val)
    }
  },
  // 名称/项目
  checkedProject: (data) => {
    var regu = /^[\u4e00-\u9fa5_a-zA-Z0-9]{2,50}$/
    var re = new RegExp(regu)
    if (data) {
      if (re.test(data)) {
        return true
      } else {
        return false
      }
    } else {
      return false
    }
  },
  // 中文字母数字下划线正则
  checkedName: (data) => {
    var regu = /^[\u4e00-\u9fa5_a-zA-Z0-9]{1,20}$/
    var re = new RegExp(regu)
    if (data) {
      if (re.test(data)) {
        return true
      } else {
        return false
      }
    } else {
      return false
    }
  },
  // 密码校验
  checkedpassWord: (data) => {
    var regu = /^[a-zA-Z0-9!@#$%^&*]{6,16}$/
    var re = new RegExp(regu)
    if (data) {
      if (re.test(data)) {
        return true
      } else {
        return false
      }
    } else {
      return false
    }
  },
  // 手机号验证
  checkedPhone: (data) => {
    var regu = /^1[34578]\d{9}$/
    var re = new RegExp(regu)
    if (data) {
      if (re.test(data)) {
        return true
      } else {
        return false
      }
    } else {
      return false
    }
  },
  // 真实姓名
  checkedCompellation: (data) => {
    var regu = /^([\u4e00-\u9fa5]{2,20})$/
    var re = new RegExp(regu)
    if (data) {
      if (re.test(data)) {
        return true
      } else {
        return false
      }
    } else {
      return false
    }
  },
  // 身份证号
  checkedidentity: (data) => {
    var regu =
      /^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/ ||
      /^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}[0-9Xx]$/
    var re = new RegExp(regu)
    if (data) {
      if (re.test(data)) {
        return true
      } else {
        return false
      }
    } else {
      return false
    }
  },
  // 开户行
  checkedBank: (data) => {
    var regu = /^[\u4e00-\u9fa5]{1,20}$/
    var re = new RegExp(regu)
    if (data) {
      if (re.test(data)) {
        return true
      } else {
        return false
      }
    } else {
      return false
    }
  },
  // 银行卡
  checkedbackCard: (data) => {
    var regu = /^[0-9]{16,20}$/
    var re = new RegExp(regu)
    if (data) {
      if (re.test(data)) {
        return true
      } else {
        return false
      }
    } else {
      return false
    }
  },
  // 字母数字正则
  checkedCode: (data) => {
    var regu = /^[a-zA-Z0-9]{1,10}$/
    var re = new RegExp(regu)
    if (data) {
      if (re.test(data)) {
        return true
      } else {
        return false
      }
    } else {
      return false
    }
  },
  positiveNumberRule: (data) => {
    var regu = /^[0-9]{1,15}$/
    var re = new RegExp(regu)
    if (data !== '') {
      if (re.test(data)) {
        return true
      } else {
        return false
      }
    } else {
      return false
    }
  },
  // 排序正则 1-20个数字
  checkedSort: (data) => {
    var regu = /^[0-9]{1,20}$/
    var re = new RegExp(regu)
    if (data) {
      if (re.test(data)) {
        return true
      } else {
        return false
      }
    } else {
      return false
    }
  },
  // 设定宽度
  setWidth: (type) => {
    const screenWidth = window.screen.width
    if (type === 1) {
      if (screenWidth >= 1366 && screenWidth < 1600) {
        return 3 * 57 + 52
      }
    }
  },
  // 设定高度
  setHeight: (type) => {
    const screenHeight = window.screen.height
    if (type === 1) {
      if (screenHeight > 900 && screenHeight <= 1080) {
        return 10 * 40 + 42
      }
    }
    if (type === 2) {
      if (screenHeight > 900 && screenHeight <= 1080) {
        return 14 * 40 + 42
      } else if (screenHeight < 900) {
        return 340
      }
    }
    if (type === 3) {
      if (screenHeight > 900 && screenHeight <= 1080) {
        return 620
      } else if (screenHeight < 900) {
        return 372
      }
    }
    if (type === 4) {
      if (screenHeight > 900 && screenHeight <= 1080) {
        return 693
      } else if (screenHeight < 900) {
        return 436
      }
    }
    if (type === 5) {
      if (screenHeight > 900 && screenHeight <= 1080) {
        return 680
      } else if (screenHeight < 900) {
        return 410
      }
    }
    if (type === 6) {
      if (screenHeight > 900 && screenHeight <= 1080) {
        return 353
      } else if (screenHeight < 900) {
        return 340
      }
    }
    if (type === 7) {
      if (screenHeight > 900 && screenHeight <= 1080) {
        return 280
      } else if (screenHeight < 900) {
        return 200
      }
    }
    if (type === 11) {
      if (screenHeight <= 900 && screenHeight >= 768) {
        return 10 * 30 + 25
      }
      if (screenHeight <= 1080 && screenHeight > 901) {
        return 15 * 40 + 36
      } else {
        return 347
      }
    }
    if (type === 16) {
      if (screenHeight <= 900 && screenHeight >= 768) {
        return 13 * 30 + 15
      }
      if (screenHeight > 900 && screenHeight <= 1080) {
        return 18 * 40 + 24
      } else {
        return 20 * 20 + 84
      }
    }
  },
  setCell: (type) => {
    const screenWidth = window.screen.width
    if (type === 3) {
      if (screenWidth > 1600 && screenWidth <= 1920) {
        return { padding: ' 8px' }
      }
      if (screenWidth <= 1600) {
        return { padding: '3px 8px' }
      }
    }
  },
  setHeaderCell: (type) => {
    const screenWidth = window.screen.width
    if (type === 3) {
      if (screenWidth > 1600 && screenWidth <= 1920) {
        return { background: '#f2f4fbd1', padding: ' 8px' }
      }
      if (screenWidth <= 1600) {
        return { background: '#f2f4fbd1', padding: '3px 8px' }
      }
    }
  },
  // getDateByStr: (str) => {
  //   return moment(str).toDate()
  // },
  // getDate: (m) => {
  //   return moment(m)
  // },
  // 时间转字符串，转成yyyy-MM-dd HH:mm:SS格式
  dateToStr: (time = new Date()) => {
    var dateTime = new Date(time)
    var year = dateTime.getFullYear()
    var month = dateTime.getMonth() + 1
    var date = dateTime.getDate()
    var hour = dateTime.getHours()
    var minutes = dateTime.getMinutes()
    var second = dateTime.getSeconds()
    if (month < 10) {
      month = '0' + month
    }
    if (date < 10) {
      date = '0' + date
    }
    if (hour < 10) {
      hour = '0' + hour
    }
    if (minutes < 10) {
      minutes = '0' + minutes
    }
    if (second < 10) {
      second = '0' + second
    }
    return year + '-' + month + '-' + date + ' ' + hour + ':' + minutes + ':' + second
  },
  getDate: (type = 'day') => {
    if (type === 'day') {
      return moment().format('YYYY-MM-DD')
    } else if (type === 'week') {
      return moment().startOf('week').format('YYYY-MM-DD')
    } else if (type === 'month') {
      return moment().startOf('month').format('YYYY-MM-DD')
    }
  },
  getEditDate: (type = 'day', arrow, date) => {
    let x = Number(moment().format('x'))
    if (arrow === 'left') {
      if (type === 'day') {
        return moment(date).subtract(1, 'd').format('YYYY-MM-DD')
      } else if (type === 'week') {
        return moment(date).subtract(1, 'w').format('YYYY-MM-DD')
        // return [moment(date[0]).subtract(1, 'w').format('YYYY-MM-DD'), moment(date[1]).subtract(7, 'd').format('YYYY-MM-DD')]
      } else if (type === 'month') {
        return moment(date).subtract(1, 'M').format('YYYY-MM-DD')
      }
    } else if (arrow === 'right') {
      if (type === 'day' && x >= Number(moment(date).add(1, 'd').format('x'))) {
        return moment(date).add(1, 'd').format('YYYY-MM-DD')
      } else if (type === 'week' && x >= Number(moment(date).add(1, 'w').format('x'))) {
        return moment(date).add(1, 'w').format('YYYY-MM-DD')
        // return [moment(date[0]).add(1, 'w').format('YYYY-MM-DD'), moment(date[1]).add(1, 'w').format('YYYY-MM-DD')]
      } else if (type === 'month' && x >= Number(moment(date).add(1, 'M').format('x'))) {
        return moment(date).add(1, 'M').format('YYYY-MM-DD')
      } else {
        return date
      }
    }
  },
  // 从content-disposition获取文件名
  getFileName(response) {
    if (response.headers['content-disposition']) {
      const disposition = response.headers['content-disposition']
      const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
      const matches = filenameRegex.exec(disposition)
      const fileName = matches != null && matches[1] ? matches[1].replace(/['"]/g, '') : 'file.txt'
      return fileName
    }
  },
  downloadFile(_res) {
    // 接口失败返回提示 对提示进行转码
    if (_res.headers && _res.headers.code && _res.headers.code != 200) {
      return Message.error(decodeURIComponent(_res.headers.message))
    }
    let aData = new Date()
    let currentTime = aData.getFullYear() + '-' + (aData.getMonth() + 1) + '-' + aData.getDate()
    const blob = new Blob([_res.data], { type: 'application/vnd.ms-excel;' })
    const a = document.createElement('a')
    // 生成文件路径
    let href = window.URL.createObjectURL(blob)
    a.href = href
    let _fileName = ''
    if (_res.headers) {
      _fileName = _res.headers.filename || this.getFileName(_res)
    } else {
      _fileName = '模板'
    }
    // let _fileName = _res.headers['content-disposition'].split(';')[1].split('=')[1].split('.')[0]
    // 文件名中有中文 则对文件名进行转码
    a.download = decodeURIComponent(_fileName)
    // 利用a标签做下载
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    window.URL.revokeObjectURL(href)
  },
  docxdownloadFile(_res, filename) {
    // 接口失败返回提示 对提示进行转码
    // if (_res.headers.code != 200) {
    //   return Message.error(decodeURIComponent(_res.headers.message))
    // }
    // let aData = new Date()
    // let currentTime =
    //   aData.getFullYear() +
    //   '-' +
    //   (aData.getMonth() + 1) +
    //   '-' +
    //   aData.getDate()
    const blob = new Blob([_res], {
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document;charset=UTF-8'
    })
    const a = document.createElement('a')
    // 生成文件路径
    let href = window.URL.createObjectURL(blob)
    a.href = href
    let _fileName = filename
    // let _fileName = _res.headers['content-disposition'].split(';')[1].split('=')[1].split('.')[0]
    // 文件名中有中文 则对文件名进行转码
    a.download = decodeURIComponent(_fileName)
    // 利用a标签做下载
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    // 释放内存
    window.URL.revokeObjectURL(href)
  },
  getCurrentDate: () => {
    // 获取当前时间
    return moment().format('YYYY-MM-DD HH:mm:ss')
  },
  getUserInfo: (key) => {
    return localStorage.getItem(key) ? JSON.parse(localStorage.getItem('LOGINDATA')) : {}
  },
  // 二次提醒
  confirm: (self, message, title, type, confirmButtonText, cancelButtonText, callback) => {
    self
      .$confirm(message, title || '提示', {
        type: type,
        confirmButtonText: confirmButtonText || '确定',
        cancelButtonText: cancelButtonText || '取消'
      })
      .then(() => {
        callback(new Error('confirm'))
      })
      .catch(() => {
        callback(new Error('cancel'))
      })
  },
  formatMenus: (menus) => {
    const list = []
    menus.forEach((item) => {
      if (item.parentId === '0') {
        const children = []
        menus.forEach((child) => {
          if (child.parentId === item.id) {
            child.children = []
            children.push(child)
          }
        })
        item.children = children
        list.push(item)
      }
    })
    return list
  },
  listToTree: (list, id, pid) => {
    const temp = {}
    const tree = []
    list.forEach((item) => {
      temp[item[id]] = item
    })
    for (const i in temp) {
      if (temp[i][pid] === '' || temp[i][pid] === null || temp[i][pid] === undefined) {
        temp[i][pid] = 0
      }
      if (temp[i][pid] !== 0 && temp[temp[i][pid]] !== undefined) {
        if (!temp[temp[i][pid]].children) {
          temp[temp[i][pid]].children = []
        }
        temp[temp[i][pid]].children.push(temp[i])
      } else {
        tree.push(temp[i])
      }
    }
    return tree
  },
  randomRgbColor: (type) => {
    // 随机生成RGB颜色
    var r = Math.floor(Math.random() * 256) // 随机生成256以内r值
    var g = Math.floor(Math.random() * 256) // 随机生成256以内g值
    var b = Math.floor(Math.random() * 256) // 随机生成256以内b值
    if (type == 'array') {
      return [`rgba(${r},${g},${b},0.3)`, `rgba(${r},${g},${b},1)`]
    }
    return `rgb(${r},${g},${b}})` // 返回rgb(r,g,b)格式颜色
  },
  paginationData: (total, pageSize, currentPage) => {
    let totalPage = Math.ceil((total - 1) / pageSize)
    let current = currentPage > totalPage ? totalPage : currentPage
    return currentPage < 1 ? 1 : current
  },
  // 设备分类树检索
  filterNode(value, data) {
    if (!value) return true
    return data.dictLabel.indexOf(value) !== -1
  },
  // 分页删除最后一条数据，返回上一级的分页
  resetTablePage(page, pageSize, total) {
    const totalPage = Math.ceil((total - 1) / pageSize) // 总页数
    const currentPage = page > totalPage ? totalPage : page
    return currentPage < 1 ? 1 : currentPage
  },
  /**
   * 数字转成汉字
   * @params num === 要转换的数字
   * @return 汉字
   * */
  toChinesNum(num) {
    let changeNum = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
    let unit = ['', '十', '百', '千', '万']
    num = parseInt(num)
    let getWan = (temp) => {
      let strArr = temp.toString().split('').reverse()
      let newNum = ''
      let newArr = []
      strArr.forEach((item, index) => {
        newArr.unshift(item === '0' ? changeNum[item] : changeNum[item] + unit[index])
      })
      let numArr = []
      newArr.forEach((m, n) => {
        if (m !== '零') numArr.push(n)
      })
      if (newArr.length > 1) {
        newArr.forEach((m, n) => {
          if (newArr[newArr.length - 1] === '零') {
            if (n <= numArr[numArr.length - 1]) {
              newNum += m
            }
          } else {
            newNum += m
          }
        })
      } else {
        newNum = newArr[0]
      }
      return newNum
    }
    let overWan = Math.floor(num / 10000)
    let noWan = num % 10000
    if (noWan.toString().length < 4) {
      noWan = '0' + noWan
    }
    return overWan ? getWan(overWan) + '万' + getWan(noWan) : getWan(num)
  },
  /**
   * minio 图片前缀改为访问地址前缀
   * @params url === 全拼或半拼图片地址
   * @return 地址全拼
   * */
  imgUrlTranslation(url) {
    const baseAddress = store.state.user.picPrefix
    // const baseAddress = __PATH.FILEPREFIX;
    // 用户信息未返回可替换的基础地址，直接返回原地址
    if (!baseAddress || !url) return url
    // 定义一个正则表达式来匹配以http或https开头的ip或域名地址，包括带端口号的情况
    // 支持以下格式：
    // http://minio:9000/...
    // https://minio:9000/...
    // http://***********:9000/...
    // https://***********:9000/...
    // http://example.com:9000/...
    // https://example.com:9000/...
    const fullAddressRegex = /^((https?|ftp):\/\/(?:\d+\.\d+\.\d+\.\d+(?::\d+)?|[\w-]+(?::\d+)?|\w+\.\w+\.\w+(?::\d+)?))/
    // 使用正则表达式检查输入地址是否匹配完整地址格式
    const match = url.match(fullAddressRegex)
    if (match && match.length) {
      const lastBaseAddress = baseAddress[baseAddress.length - 1]
      // 如果匹配到完整地址，提取出半地址部分
      let halfAddress = url.replace(match[0], '')
      // 获取baseAddress的最后一个路径部分（如 'minio'）
      const basePathParts = baseAddress.split('/').filter(Boolean)
      const baseSuffix = basePathParts[basePathParts.length - 1]
      // 获取halfAddress的第一个路径部分
      const halfPathParts = halfAddress.split('/').filter(Boolean)
      // 如果baseAddress有后缀，并且halfAddress以这个后缀开头，则移除重复部分
      if (baseSuffix && halfPathParts[0] === baseSuffix) {
        halfPathParts.shift()
        halfAddress = '/' + halfPathParts.join('/')
      }
      if (lastBaseAddress === '/' && halfAddress[0] === '/') {
        // 如果半地址部分以/开头，则去掉/,否则会重复
        halfAddress = halfAddress.slice(1)
      }
      if (lastBaseAddress !== '/' && halfAddress[0] !== '/') {
        halfAddress = '/' + halfAddress
      }
      // 替换完整地址部分为新地址，同时保留半地址
      const fullAddress = baseAddress + halfAddress
      return fullAddress
    } else {
      // 如果没有匹配到完整地址，则拼接地址
      if (url[0] !== '/') {
        url = '/' + url
      }
      const fullAddress = baseAddress + url
      return fullAddress
    }
  },
  // 取消请求
  cancelAjax() {
    let cancelArr = window.axiosCancel
    cancelArr = cancelArr || []
    cancelArr.forEach((ele, index) => {
      ele.cancel()
      delete window.axiosCancel[index]
    })
  },
  // 添加选项
  addLetter(index, num) {
    let words = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
    if (num) {
      let newArr = words.splice(0, num)
      return newArr
    } else {
      return words[index]
    }
  }
}
// function formatDate(date) {
//   const myyear = date.getFullYear()
//   let mymonth = date.getMonth() + 1
//   let myweekday = date.getDate()
//   if (mymonth < 10) {
//     mymonth = '0' + mymonth
//   }
//   if (myweekday < 10) {
//     myweekday = '0' + myweekday
//   }
//   return myyear + '-' + mymonth + '-' + myweekday
// }
