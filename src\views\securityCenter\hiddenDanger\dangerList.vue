<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="content_box">
        <el-table v-loading="tableLoading" stripe :data="purchaseTable" :border="true" height="cacl(100% - 30px)" title="双击查看详情" @row-dblclick="watchDetail">
          <!-- <el-table-column type="selection" width="55" fixed="left" align="center"></el-table-column> -->
          <el-table-column type="index" label="序号" width="65">
            <template slot-scope="scope">
              <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="questionCode" label="编号" show-overflow-tooltip width="200"> </el-table-column>
          <el-table-column prop="flowType" label="状态" show-overflow-tooltip width="120"></el-table-column>
          <el-table-column prop="questionDetailType" label="隐患分类" show-overflow-tooltip width="180"></el-table-column>
          <el-table-column prop="questionAddress" label="隐患区域" show-overflow-tooltip width="180"></el-table-column>
          <el-table-column prop="createByDeptName" label="反馈部门" show-overflow-tooltip width="180">
            <template slot-scope="scope">
              <span v-if="scope.row.questionBelongs == '0'" class="identification el-icon-s-flag">医管局</span>
              <span>{{ scope.row.createByDeptName }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="createPersonName" label="反馈人" show-overflow-tooltip width="180"></el-table-column>
          <el-table-column prop="createTime" label="反馈时间" show-overflow-tooltip width="180"></el-table-column>
          <el-table-column prop="riskName" label="隐患等级" show-overflow-tooltip width="180"></el-table-column>
          <el-table-column prop="rectificationPlanTime" label="要求整改完成时间" show-overflow-tooltip width="180"></el-table-column>
        </el-table>
        <div class="contentTable-footer">
          <el-pagination
            style="margin-top: 3px;"
            :current-page="paginationData.pageNo"
            layout="total, sizes, prev, pager, next, jumper"
            :total="paginationData.total"
            :page-size="paginationData.pageSize"
            :page-sizes="[15, 30, 50]"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">关闭</el-button>
    </div>
  </PageContainer>
</template>

<script>
export default {
  name: 'dangerList',
  data() {
    return {
      tableLoading: false,
      purchaseTable: [],
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      riskCode: '', // 隐患等级
      dateType: '', // 日期类型
      flowCode: '', // 隐患状态
      deptCode: '', // 部门id
      gridId: '', // 网格id
      currMonth: '' // 隐患月份
    }
  },
  created() {
    this.riskCode = this.$route.query.riskCode || ''
    this.dateType = this.$route.query.dateType || ''
    this.flowCode = this.$route.query.flowCode || ''
    this.deptCode = this.$route.query.deptCode || ''
    this.gridId = this.$route.query.gridId || ''
    this.currMonth = this.$route.query.currMonth || ''
    this.getTableData()
  },
  methods: {
    getTableData() {
      let data = {
        pageSize: this.paginationData.pageSize,
        pageNo: this.paginationData.currentPage,
        riskCode: this.riskCode,
        dateType: this.dateType,
        flowCode: this.flowCode,
        deptCode: this.deptCode,
        gridId: this.gridId,
        currMonth: this.currMonth
      }
      this.$api.ipsmGetHiddenList(data).then((res) => {
        if (res.code == 200) {
          this.purchaseTable = res.data.list
          this.paginationData.total = parseInt(res.data.total)
        }
      })
    },
    watchDetail(val) {
      this.$router.push({
        name: 'hiddenManagementDetails3',
        query: { id: val.id }
      })
    },
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.paginationData.currentPage = 1
      this.getTableData()
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.getTableData()
    }
  }
}
</script>

<style lang="scss" scoped>
.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
  padding: 10px;
  overflow-y: auto;
}

.content_box {
  height: calc(100% - 30px);
  margin-top: 10px;
  //   padding: 20px 25px 25px 25px;
  background: #fff;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.contentTable-footer {
  padding: 10px 0 0;
}
</style>
