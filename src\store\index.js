/*
 * @Author: hedd
 * @Date: 2023-02-27 14:35:19
 * @LastEditTime: 2024-03-01 09:27:45
 * @FilePath: \IHCRS_alarm\src\store\index.js
 * @Description:
 */
import Vue from 'vue'
import Vuex from 'vuex'
Vue.use(Vuex)
const modules = {}
const require_module = import.meta.globEager('./modules/*.js')
for (const file_name in require_module) {
  modules[file_name.slice(10, -3)] = require_module[file_name].default
}
export default new Vuex.Store({
  modules: modules,
  strict: process.env.NODE_ENV !== 'production',
  getters: {
    userId: (_, getters) => {
      return getters['user/userId']
    },
    userName: (_, getters) => {
      return getters['user/userName']
    }
  }
})
