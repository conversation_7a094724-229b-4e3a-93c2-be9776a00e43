<!-- 运行监测 -->
<template>
  <PageContainer>
    <div slot="content" class="monitor-content">
      <div class="monitor-content-left">
        <!-- <p v-for="item in teamTreeData" :key="item.code" class="left-item" :class="{'item-active': item.code == searchFrom.entityMenuCode}" @click="switchItem(item.code)">{{item.name}}</p> -->
        <el-tree slot="content" ref="teamTree" v-loading="treeLoading" class="team-tree" :check-strictly="true"
          :data="teamTreeData" :props="defaultProps" node-key="id" :highlight-current="true"
          :default-expanded-keys="expandedTeam" @node-click="handleTeamClick">
          <span slot-scope="{ node }" class="custom-tree-node">
            <el-tooltip class="item" effect="dark" :content="node.label" placement="top-start"
              :disabled="!isShowTooltip">
              <span @mouseenter="visibilityChange($event)">{{ node.label }}</span>
            </el-tooltip>
          </span>
        </el-tree>
      </div>
      <div class="monitor-content-right">
        <div class="right-heade">
          <el-input v-model="searchFrom.surveyName" placeholder="监测项名称" suffix-icon="el-icon-search"
            style="width: 200px;" clearable />
          <el-select v-model="searchFrom.status" placeholder="请选择设备" clearable>
            <el-option label="全部" value=""></el-option>
            <el-option label="运行" :value="0"></el-option>
            <el-option label="停止" :value="1"></el-option>
            <el-option label="离线" :value="6"></el-option>
          </el-select>
          <div style="display: inline-block;">
            <el-button type="primary" plain @click="resetForm">重置</el-button>
            <el-button type="primary" @click="searchForm">查询</el-button>
          </div>
          <div class="heade-pattern">
            <div class="pattern-item" @click="switchPattern(1)">
              <svg-icon :name="currentPattern == 1 ? 'chartModeActive' : 'chartMode'" class="pattern-icon" />
              <span :style="{ color: currentPattern == 1 ? '#3562DB' : '#414653' }">图形模式</span>
            </div>
            <div class="pattern-item" @click="switchPattern(2)">
              <svg-icon :name="currentPattern == 2 ? 'listModeActive' : 'listMode'" class="pattern-icon" />
              <span :style="{ color: currentPattern == 2 ? '#3562DB' : '#414653' }">列表模式</span>
            </div>
          </div>
        </div>
        <div class="right-content">
          <graphics-mode v-if="currentPattern == 1" ref="scadaShow" :entityMenuCode="searchFrom.entityMenuCode"
            :projectId="searchFrom.projectCode" />
          <list-mode v-else ref="listMode" />
        </div>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import listMode from './components/listMode.vue'
import graphicsMode from './components/graphicsMode.vue'
import { monitorTypeList } from '@/util/dict.js'
export default {
  name: 'airMonitor',
  components: {
    listMode,
    graphicsMode
  },
  beforeRouteEnter(to, from, next) {
    let names = []
    to.matched.map((v, i) => {
      if (i > 0) {
        v.components.default.name && names.push(v.components.default.name)
      }
    })
    next((vm) => {
      if (from.query.activeName) {
        vm.activeName = from.query.activeName
      }
      vm.$store.commit('keepAlive/add', names)
    })
  },
  async beforeRouteLeave(to, from, next) {
    // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
    if (!['monitorDetails'].includes(to.name)) {
      // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      loading: true,
      currentPattern: 2, // 当前模式
      treeLoading: true,
      defaultProps: {
        children: 'children',
        label: 'name',
        value: 'code'
      },
      teamTreeData: [], // 树状数据
      expandedTeam: [], // 默认展开树
      checkedTeamData: {}, // 当前选中树
      searchFrom: {
        projectCode: monitorTypeList.find((item) => item.projectName == '空调监测').projectCode,
        entityMenuCode: '', // 菜单code
        surveyName: '',
        status: ''
      },
      isShowTooltip: false
    }
  },
  computed: {},
  created() {
    this.getEntityMenuList()
  },
  activated() {
    this.$refs.listMode.init(this.searchFrom)
  },
  methods: {
    isDisabled($event) {
      console.log('$event===========', $event)
    },
    visibilityChange(e) {
      const item = e.target
      const itemWidth = item.offsetWidth
      const itemScrollWidth = item.scrollWidth
      if (itemWidth < itemScrollWidth) {
        this.isShowTooltip = true
      } else {
        this.isShowTooltip = false
      }
    },
    // 获取菜单
    getEntityMenuList() {
      this.$api.GetEntityMenuList({ projectId: this.searchFrom.projectCode }).then((res) => {
        this.treeLoading = false
        if (res.code == 200) {
          let list = this.$tools.transData(res.data, 'code', 'parentId', 'children')
          this.teamTreeData = list
          if (this.teamTreeData.length) {
            this.checkedTeamData = this.teamTreeData[0]
            this.searchFrom.entityMenuCode = this.checkedTeamData.code
            this.$nextTick(() => {
              this.$refs.teamTree.setCurrentKey(this.teamTreeData[0])
              this.$refs.listMode.init(this.searchFrom)
            })
          }
        }
      })
    },
    // 树状图点击
    handleTeamClick(data) {
      this.checkedTeamData = data
      this.$refs.teamTree.setCurrentKey(this.checkedTeamData)
      this.searchFrom.entityMenuCode = data.code
      this.currentPattern = 2
      this.searchForm()
    },
    // 模式切换
    switchPattern(type) {
      if (this.currentPattern != type) {
        this.currentPattern = type
        this.searchForm()
      }
    },
    // 菜单切换
    switchItem(code) {
      if (this.searchFrom.entityMenuCode != code) {
        this.searchFrom.entityMenuCode = code
        this.currentPattern = 2
        this.searchForm()
      }
    },
    // 重置查询
    resetForm() {
      this.searchFrom.surveyName = ''
      this.searchFrom.status = ''
      // Object.assign(this.$data.searchFrom, this.$options.data().searchFrom)
      this.searchForm()
    },
    // 查询
    searchForm() {
      if (this.currentPattern == 1) {
      } else {
        this.$nextTick(() => {
          this.$refs.listMode.init(this.searchFrom)
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.el-tree-node__content {
  width: 100%;

  .custom-tree-node {
    display: inline-block;
    width: 100%;

    .item {
      display: inline-block;
      width: calc(100% - 10px);
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
  }
}

.monitor-content {
  height: 100%;
  display: flex;

  ::v-deep .monitor-content-left {
    width: 246px;
    height: 100%;
    background: #fff;
    border-radius: 4px;
    padding: 16px;
    overflow: auto;

    .el-tree {
      height: 100%;
    }

    .el-tree-node {
      .el-tree-node__content {
        padding: 6px 0;
        height: auto;
      }
    }

    .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
      background-color: #d9e1f8;
    }
  }

  .monitor-content-right {
    height: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;

    .right-heade {
      padding: 0 200px 10px 10px !important;
      margin-left: 16px;
      background: #fff;
      border-radius: 4px;
      position: relative;

      &>div {
        margin-right: 10px;
        margin-top: 10px;
      }

      .heade-pattern {
        display: flex;
        position: absolute;
        right: 16px;
        top: 50%;
        margin: 0 !important;
        transform: translateY(-50%);

        .pattern-item {
          cursor: pointer;
          font-size: 15px;

          .pattern-icon {
            font-size: 16px;
            margin-right: 6px;
          }
        }

        .pattern-item:last-child {
          margin-left: 16px;
        }
      }
    }

    .right-content {
      flex: 1;
      overflow: hidden;
    }
  }
}
</style>
