<template>
  <el-dialog :title="(checkedData.state === 'edit' ? '修改' : '添加') + '分组'" :visible.sync="dialogVisible" :before-close="closeDialog">
    <div class="sino-content">
      <el-form ref="filters" :rules="rules" label-position="right" label-width="100px" :model="filters">
        <el-form-item label="分组名称：" prop="name">
          <el-input v-model="filters.name" maxlength="20" placeholder="请输入分组名称"></el-input>
        </el-form-item>
        <!-- <el-form-item v-if="hasScada && hasScadaOther" label="图纸类型：" prop="isdId">
          <el-select v-model="filters.isdId" placeholder="请选择图纸类型" @change="changeIsiList(filters.isdId)">
            <el-option v-for="item in drawingTypeArr" :key="item.isdId" :value="item.isdId" :label="item.isdName"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="hasScada && hasScadaOther" label="图纸：" prop="isiId">
          <el-select v-model="filters.isiId" placeholder="请选择图纸">
            <el-option v-for="item in drawingArr" :key="item.isiId" :value="item.isiId" :label="item.isiName"></el-option>
          </el-select>
        </el-form-item> -->
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog(false)">取 消</el-button>
      <el-button type="primary" :loading="submitLoading" @click="editSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { monitorTypeList } from '@/util/dict.js'
export default {
  name: 'addTreeNode',
  props: {
    // hasScadaOther: {
    //   type: Boolean,
    //   default: true
    // },
    dialogVisible: {
      type: Boolean,
      default: false
    },
    checkedData: {
      type: Object,
      default: () => {}
    },
    treeLevel: {
      type: Number,
      default: 1
    }, // 1同级 2 下级  //第一次添加
    monitorType: {
      type: String,
      default: ''
    }, // 项目code
    requestHttp: {
      type: String,
      default: __PATH.VUE_IEMC_API
    }
  },
  data() {
    return {
      rules: {
        name: [{ required: true, message: '请输入分组名称', trigger: 'blur' }]
      },
      filters: {
        name: '',
        isdId: '',
        isiId: ''
      },
      drawingTypeArr: [], // SCADA图纸分类列表
      drawingArr: [], // SCADA图纸列表
      submitLoading: false
      // hasScada: monitorTypeList.find((item) => item.projectCode == this.monitorType)?.hasScada ?? false
    }
  },
  watch: {
    'filters.name': {
      immediate: true,
      handler: function (newVal, oldVal) {
        this.$forceUpdate()
      }
    }
  },
  mounted() {
    if (this.checkedData.state === 'edit') {
      this.echoData()
    }
  },
  methods: {
    /**
     * 修改数据回显
     */
    echoData() {
      this.filters = {
        name: this.checkedData.name,
        isdId: this.checkedData.isdId ? Number(this.checkedData.isdId) : '', // Number
        isiId: this.checkedData.isiId ? Number(this.checkedData.isiId) : ''
      }
    },
    /**
     * 提交
     */
    editSubmit() {
      this.$refs.filters.validate((valid) => {
        if (valid) {
          if (this.checkedData.state === 'edit') {
            this.$api
              .sectionMenuUpdate(
                {
                  id: this.checkedData.id,
                  name: this.filters.name
                },
                {'operation-type': 2},
                this.requestHttp
              )
              .then((res) => {
                this.submitLoading = false
                if (res.code == 200) {
                  this.$message.success(res.message)
                  this.closeDialog()
                } else {
                  this.$message.error(res.message)
                }
              })
          } else {
            // 新增
            let data = {
              name: this.filters.name // 菜单名称
            }
            this.$api.sectionMenuSave(data,  {'operation-type': 1}, this.requestHttp).then((res) => {
              this.submitLoading = false
              if (res.code == 200) {
                this.$message.success(res.message)
                this.closeDialog()
              } else {
                this.$message.error(res.message)
              }
            })
          }
        }
      })
    },
    /**
     * 关闭弹窗
     */
    closeDialog(refresh = true) {
      this.$emit('closeDialog', refresh)
      this.filters = {
        name: '',
        isdId: '',
        isiId: ''
      }
      this.$forceUpdate()
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog {
  width: 500px;
  .el-select {
    width: 100%;
  }
}
</style>
