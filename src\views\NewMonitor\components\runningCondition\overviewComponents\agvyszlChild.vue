<!-- 运行监测 -->
<template>
    <ContentCard :title="item.componentTitleShow" :scrollbarHover="true" :cstyle="{ width: '100%', height: '100%' }"
        class="drag_class" :hasMoreOper="['edit']"
        @more-oper-event="(val) => $emit('all-more-Oper', val, item.componentName, item.chartType)">
        <div slot="title-right" class="data-btns">
            <span :class="{ 'active-btn': totalCostDateType == 'all' }" @click="changeTime('all')">全部</span>
            <span :class="{ 'active-btn': totalCostDateType == 'day' }" @click="changeTime('day')">今日</span>
            <span :class="{ 'active-btn': totalCostDateType == 'week' }" @click="changeTime('week')">本周</span>
            <span :class="{ 'active-btn': totalCostDateType == 'month' }" @click="changeTime('month')">本月</span>
            <span :class="{ 'active-btn': totalCostDateType == 'year' }" @click="changeTime('year')">本年</span>
        </div>
        <div slot="content" class="operation-list">
            <p>运送次数 <span>{{ operationList.distributeCount }}次</span></p>
            <p>运送距离 <span>{{ operationList.distance }}米</span></p>
            <p>平均运送耗时 <span v-if="operationList.distributeTime !== 0 && operationList.distributeTime !== null">{{
                parseInt((operationList.distributeTime / 60) /
                    operationList.distributeCount) }}分钟</span>
                <span v-else>{{ operationList.distributeTime }}分钟</span>
            </p>
        </div>
    </ContentCard>
</template>
<script>
import moment from 'moment'
export default {
    name: 'agvyszlChild',
    props: {
        item: {
            type: Object,
            default: () => { }
        },
        systemCode: {
            type: String,
            default: ''
        },
        deviceId: {
            type: String,
            default: ''
        }
    },
    created() {
    },
    mounted() {
        setTimeout(() => {
            if (this.deviceId) {
                this.getDeviceAnalysisData()
            }
        }, 150)
    },
    data() {
        return {
            loading: false,
            totalCostDateType: 'all',
            operationList: [],
            dateTime: {
                startTime: '',
                endTime: '',
            },
            chartType: '1'
        }
    },
    methods: {
        changeTime(type) {
            this.totalCostDateType = type
            if (type == 'all') {
                this.dateTime.startTime = ''
                this.dateTime.endTime = ''
                this.getDeviceAnalysisData()
            } else if (type == 'day') {
                this.dateTime.startTime = moment().format('YYYY-MM-DD 00:00:00');
                this.dateTime.endTime = moment().format('YYYY-MM-DD 23:59:59');
                this.getToday()
            } else if (type == 'week') {
                this.dateTime.startTime = moment().startOf('isoWeek').format('YYYY-MM-DD 00:00:00');
                this.dateTime.endTime = moment().endOf('isoWeek').format('YYYY-MM-DD 23:59:59');
                this.getDeviceAnalysisData()
            } else if (type == 'month') {
                this.dateTime.startTime = moment().startOf('month').format('YYYY-MM-DD  00:00:00')
                this.dateTime.endTime = moment().endOf('month').format('YYYY-MM-DD  23:59:59')
                this.getDeviceAnalysisData()
            } else if (type == 'year') {
                this.dateTime.startTime = moment().startOf('year').format('YYYY-MM-DD  00:00:00')
                this.dateTime.endTime = moment().endOf('year').format('YYYY-MM-DD  23:59:59')
                this.getDeviceAnalysisData()
            }

        },
        // 今日
        getToday() {
            this.$api.getQueryInstanceFunction(this.deviceId, "todayJobData", this.dateTime).then((res) => {
                if (res.status == 200) {
                    this.operationList = res.result[0]
                }
            })
        },
        getDeviceAnalysisData() {
            this.$api.getQueryInstanceFunction(this.deviceId, "totalJobData", this.dateTime).then((res) => {
                if (res.status == 200) {
                    this.operationList = res.result[0]
                }
            })
        },
    }
}
</script>

<style lang="scss" scoped>
.data-btns {
    position: absolute;
    right: 7%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &>span {
        width: 44px;
        height: 20px;
        line-height: 20px;
        text-align: center;
        margin: 0 4px;
        background-color: #f6f5fa;
        font-size: 14px;
        font-family: 'PingFang SC-Medium', 'PingFang SC';
        border: none;
        border-radius: 2px;
        color: #7f848c;
        cursor: pointer;
    }

    .active-btn {
        background-color: #e6effc !important;
        color: #3562db !important;
        border-color: #e6effc !important;
    }
}

.operation-list {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 10px;

    p {
        margin-top: 30px;
        line-height: 30px;
    }

    span {
        display: block;
        color: #000;
        font-size: 20px;
    }
}
</style>