<template>
    <PageContainer :footer="true">
        <div slot="header">
            <div class="searchForm">
                <div class="search-box">
                    <el-date-picker v-model="timeList" :picker-options="pickerOptions" value-format="yyyy-MM-dd"
                        @change="timeListChange" popper-class="timePicker" type="date" placeholder="选择日期">
                    </el-date-picker>
                </div>
            </div>
        </div>
        <div slot="content" ref="contentRef" class="table-box">
            <div class="tableContainer">
                <el-table v-loading="tableLoading" height="calc(100% - 96px)" :data="tableData" border stripe>
                    <el-table-column label="场景名称" prop="sceneName" show-overflow-tooltip
                        align="center"></el-table-column>
                    <el-table-column label="天气类型" prop="weatherName" show-overflow-tooltip
                        align="center"></el-table-column>
                    <el-table-column label="周期类型" prop=" periodType" show-overflow-tooltip align="center">
                        <template slot-scope="scope">
                            {{ scope.row.periodType === 'fixed' ? '固定' : '自定义' }}
                        </template>
                    </el-table-column>
                    <el-table-column label="时间表" prop="cronTimeStr" show-overflow-tooltip
                        align="center"></el-table-column>
                    <el-table-column label="状态" prop="status" show-overflow-tooltip align="center">
                        <template slot-scope="scope">
                            {{ scope.row.status === 0 ? '禁用' : '启用' }}
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" show-overflow-tooltip align="center">
                        <template slot-scope="scope">
                            <el-button type="text" @click="handleListEvent(scope.row)">设备</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination :current-page="pagination.page" :page-sizes="[15, 30, 50, 100]"
                    :page-size="pagination.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="pageTotal"
                    @size-change="handleSizeChange" @current-change="handleCurrentChange">
                </el-pagination>
            </div>
        </div>
        <div slot="footer">
            <el-button type="primary" plain @click="$router.go(-1)">返回</el-button>
        </div>
    </PageContainer>
</template>
<script>
export default {
    name: 'calendarDetails',
    components: {

    },
    data() {
        return {
            value: '',
            tableLoading: false,
            pickerOptions: {},
            timeList: this.$route.query.date,
            tableData: [],
            pagination: {
                pageSize: 15,
                page: 1
            },
            pageTotal: 0,
        }
    },
    created() {

    },
    mounted() {
        this.getTableData()
    },
    methods: {

        timeListChange(val) {
            this.timeList = val
            this.getTableData()
        },
        handleSizeChange(val) {
            this.pagination.pageSize = val
            this.pagination.page = 1
            this.getTableData()
        },
        getTableData() {
            this.tableLoading = true
            let data = {
                ...this.pagination,
                date: this.timeList
            }
            this.$api
                .getQueryByDate(data)
                .then((res) => {
                    this.tableLoading = false
                    if (res.code == 200) {
                        this.tableData = res.data ? res.data.records : []
                        this.pageTotal = res.data ? res.data.total : 0
                        // location.reload();
                    } else if (res.message) {
                        this.tableData = []
                        this.pagination.total = 0
                        this.$message.error(res.message)
                    }
                })
                .catch((err) => {
                    this.tableLoading = false
                })
        },

        handleCurrentChange(val) {
            this.pagination.page = val
            this.getTableData()
        },
        handleListEvent(row) {
            let path = `${this.$route.meta.jumpAddress}/calendarDetailsEquipment`
            this.$router.push({
                path: path,
                query: {
                    id: row.id,
                    sceneId: row.sceneId,
                    timeScheduleId: row.timeScheduleId,
                    date: this.timeList,
                    systemCode: this.$route.query.systemCode,
                }
            })
        },
    }
}
</script>
<style lang="scss" scoped>
.searchForm {
    display: flex;
    height: 80px;
    padding: 0 16px;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .el-input {
        width: 200px;
        margin-left: 16px;
    }

    >div {
        margin-right: 20px;
    }
}

.search-box {
    display: flex;
    align-items: center;
}

.el-table {
    margin-bottom: 12px;
}

::v-deep .el-table__row:hover {
    cursor: pointer;
}

.table-box {
    background-color: #fff;
    height: 100%;
    padding: 16px;

    .tableContainer {
        height: 100%;

        .el-table {
            height: calc(100% - 96px) !important;
        }
    }
}
</style>
