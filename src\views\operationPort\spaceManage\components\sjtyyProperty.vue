<template>
  <div class="contentTable">
    <div class="contentTable-header">
      <el-input v-model="searchFrom.compound" placeholder="编码/名称/型号/久其编码" clearable style="width: 210px"></el-input>
      <el-cascader v-model="searchFrom.useDepartmentId" placeholder="使用科室" :options="deptOptionsList" clearable show-all-levels :props="props" filterable></el-cascader>
      <el-select v-model="searchFrom.useStatus" placeholder="使用状态" clearable>
        <el-option v-for="item in useStatusList" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"></el-option>
      </el-select>
      <el-select v-model="searchFrom.warrantyState" placeholder="保修状态" clearable>
        <el-option v-for="item in warrantyStatusList" :key="item.id" :label="item.name" :value="item.id"></el-option>
      </el-select>
      <el-select v-model="searchFrom.affiliationOfficeCode" filterable clearable placeholder="归口部门">
        <el-option v-for="item in onputDepartArr" :key="item.id" :label="item.name" :value="item.id"></el-option>
      </el-select>
      <el-input v-model="searchFrom.oldAssetsCode" placeholder="旧资产编码" clearable style="width: 200px"></el-input>
      <div style="display: inline-block">
        <el-button type="primary" plain @click="resetForm">重置</el-button>
        <el-button type="primary" @click="searchForm">查询</el-button>
      </div>
    </div>
    <div class="contentTable-main table-content">
      <el-table ref="multipleTable" v-loading="tableLoading" :data="tableData" row-key="id" title="双击列表查看详情" :height="tableHeight" @row-dblclick="openDetails">
        <el-table-column v-for="(col, index) in tableColumn" :key="index" show-overflow-tooltip :prop="col.columnCode" :label="col.columnName" width="180px">
          <template slot-scope="scopes">
            <div>
              {{ transfromDynamicTable(scopes.row, col.columnCode) }}
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="contentTable-footer">
      <el-pagination
        style="margin-top: 3px"
        :current-page="pagination.current"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        :page-size="pagination.size"
        :page-sizes="[15, 30, 50]"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import { transData } from '@/util'
import { iomsUserInfon } from '@/util/dict.js'
import tableListMixin from '@/mixins/tableListMixin.js'
export default {
  name: 'sjtyyProperty',
  mixins: [tableListMixin],
  props: {
    placeIds: {
      type: String,
      default: ''
    },
    assetParmas: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      deptOptionsList: [], // 科室列表
      useStatusList: [], // 使用状态列表
      onputDepartArr: [],
      warrantyStatusList: [
        {
          id: '0',
          name: '在保'
        },
        {
          id: '1',
          name: '未在保'
        },
        {
          id: '2',
          name: '未知'
        }
      ],
      searchFrom: {
        compound: '',
        useDepartmentId: [],
        useStatus: '',
        warrantyState: '',
        affiliationOfficeCode: '',
        oldAssetsCode: ''
      },
      props: {
        // 树形结构科室自定义字段
        value: 'id',
        label: 'officeName',
        children: 'children',
        checkStrictly: true
      },
      tableData: [],
      pagination: {
        current: 1,
        size: 15
      },
      total: 0,
      tableLoading: false,
      tableColumn: []
    }
  },
  watch: {
    // placeIds(val) {
    //   this.getTableList()
    // }
  },
  created() {
    Object.assign(this.searchFrom, this.assetParmas)
    this.getOfficeByUserRole()
    this.getIemsDictList()
    this.getHospitalOfficeLastLevelInfo()
    this.getTableList()
  },
  methods: {
    getHospitalOfficeLastLevelInfo() {
      this.$api.GetHospitalOfficeLastLevelInfo({ ...iomsUserInfon }).then((res) => {
        this.onputDepartArr = []
        if (res.code === '200' && res.data.officeList.length > 0) {
          res.data.officeList.forEach((v, i) => {
            if (['30021700', '30021900', '30021300'].includes(v.id)) {
              this.onputDepartArr.push({ id: v.id, name: v.name })
            }
          })
        }
      })
    },
    // 使用状态
    getIemsDictList() {
      this.$api.GetIemsDictList({ dictType: 'asset_use_status', ...iomsUserInfon }).then((res) => {
        if (res.code === '200') {
          this.useStatusList = res.data
        }
      })
    },
    // 获取科室
    getOfficeByUserRole() {
      this.$api.GetOfficeByUserRole({ ...iomsUserInfon }).then((res) => {
        if (res.code === '200') {
          this.deptOptionsList = transData(res.data.list, 'id', 'parentId', 'children')
        }
      })
    },
    getTableList() {
      let {compound, useDepartmentId, useStatus, warrantyState, affiliationOfficeCode, oldAssetsCode, costTypeCode} = this.searchFrom
      let params = {
        ...iomsUserInfon,
        spaceId: this.placeIds == 'all' ? '' : this.placeIds,
        currentPage: this.pagination.current,
        pageSize: this.pagination.size,
        compound,
        useDepartmentId: useDepartmentId.join(','),
        useStatus,
        warrantyState,
        affiliationOfficeCode,
        oldAssetsCode,
        costTypeCode
      }
      this.tableLoading = true
      this.$api
        .GetDeviceAssetsList(params)
        .then((res) => {
          this.tableLoading = false
          if (res.code == 200) {
            this.tableColumn = res.data.headers
            this.tableData = res.data.list
            this.total = res.data.sum
          }
        })
        .catch(() => {
          this.tableLoading = false
        })
    },
    // 查询
    searchForm() {
      this.pagination.current = 1
      this.getTableList()
    },
    // 重置查询
    resetForm() {
      Object.assign(this.$data.searchFrom, this.$options.data().searchFrom)
      this.searchForm()
    },
    // 资产使用年限
    getWorkOderType() {},
    getReckonCount() {},
    // 转换动态表格
    transfromDynamicTable(obj, config) {
      if (config == 'money' && obj[config] == '0.00') {
        // 修改资产原值显示问题
        return '-'
      } else {
        return obj[config]
      }
    },
    openDetails(val) {
      this.$router.push({
        path: '/policyasset/detailsAssets',
        query: {
          id: val.id
        }
      })
    },
    getWorkOderTrend() {},
    handleSizeChange(val) {
      this.pagination.size = val
      this.getTableList()
    },
    handleCurrentChange(val) {
      this.pagination.current = val
      this.getTableList()
    }
  }
}
</script>

<style lang="scss" scoped>
.contentTable {
  width: 100%;
  height: 100%;
  background-color: #fff;
  padding: 10px;
  border-radius: 5px;
  display: flex;
  flex-direction: column;
  .contentTable-header {
    padding: 0 0 10px 0;
    & > div {
      /* margin-top: 12px; */
      margin-right: 10px;
    }
  }
  .contentTable-main {
    flex: 1;
    overflow: auto;
    // height: calc(100% - 45px);
  }
  .contentTable-footer {
    padding: 10px 0 0;
  }
}
</style>
