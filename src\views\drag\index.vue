<!--
 * @Author: hedd
 * @Date: 2023-02-27 14:35:20
 * @LastEditTime: 2025-01-02 14:18:30
 * @FilePath: \ihcrs_pc\src\views\drag\index.vue
 * @Description:
-->
<template>
  <PageContainer>
    <div slot="content" ref="contentBox" class="staging-content">
      <!-- <scaleScreen :width="parentBox.width" :height="parentBox.height"> -->
      <dashboard v-if="dashExampleShow" id="dashExample" :style="{ width: editExampleShow ? 'calc(100% - 220px)' : '100%' }">
        <dash-layout v-bind="dlayout" :debug="false">
          <template v-for="(item, index) in dlayout.items">
            <dash-item v-if="item.status == 1" v-bind.sync="dlayout.items[index]" :key="item.componentName" @moveEnd="moveEnd" @resizeEnd="resizeEnd">
              <component :is="item.componentName" :ref="item.componentName" :item="item" @all-more-Oper="allMoreOper"> </component>
            </dash-item>
          </template>
        </dash-layout>
      </dashboard>
      <div id="editExample" :style="{ width: editExampleShow ? '215px' : '0' }">
        <div class="round-box">
          <div class="editExample-title">
            <div>添加模块</div>
            <p>请点击卡片修改显示模块</p>
          </div>
          <div class="editExample-content">
            <div
              v-for="(item, index) in dlayout.items"
              :key="index"
              :class="{ 'editExample-content-item': true, 'active-editExample-item': activeExample.includes(item.componentName) }"
              @click="addStaging(item)"
            >
              <span>{{ item.componentTitle }}</span>
            </div>
          </div>
          <div class="editExample-footer">
            <el-button type="primary" plain :disabled="hasStatusFalg" @click="cancelStaging">取消</el-button>
            <el-button type="primary" @click="saveStaging">保存</el-button>
          </div>
        </div>
      </div>
      <!-- </scaleScreen> -->
    </div>
  </PageContainer>
</template>
<script>
import { Dashboard, DashLayout, DashItem } from 'vue-responsive-dash'
export default {
  name: 'DragIndex',
  components: {
    Dashboard,
    DashLayout,
    DashItem,
    personalInfo: () => import('./components/personalInfo'),
    workOderType: () => import('./components/workOderType'),
    warnManageTable: () => import('./components/warnManageTable'),
    msgReminder: () => import('./components/msgReminder'),
    todoItems: () => import('./components/todoItems'),
    quickNavigation: () => import('./components/quickNavigation'),
    logisticsService: () => import('./components/logisticsService'),
    wasteDeliveryRecord: () => import('./components/wasteDeliveryRecord'),
    equipmentPatrolTask: () => import('./components/equipmentPatrolTask')
  },
  filters: {
    stateFilter(items) {
      return items.filter((item) => (item.status = 1))
    }
  },
  data() {
    const workBenchList = [
      {
        id: '1',
        componentName: 'personalInfo',
        componentTitle: '用户信息',
        path: null,
        status: 1,
        x: 0,
        y: 0,
        width: 8,
        height: 7
      },
      {
        id: '2',
        componentName: 'workOderType',
        componentTitle: '服务工单类型统计',
        path: null,
        status: 1,
        x: 8,
        y: 0,
        width: 8,
        height: 7
      },
      {
        id: '3',
        componentName: 'warnManageTable',
        componentTitle: '报警管理',
        path: '/allAlarm/allAlarmIndex',
        status: 1,
        x: 8,
        y: 7,
        width: 16,
        height: 8
      },
      {
        id: '5',
        componentName: 'todoItems',
        componentTitle: '待办事项',
        path: '/drag/todoAndmsgRecord?page=todo',
        status: 1,
        x: 16,
        y: 0,
        width: 8,
        height: 7
      },
      {
        id: '7',
        componentName: 'equipmentPatrolTask',
        componentTitle: '后勤设备巡检任务统计',
        path: null,
        status: 1,
        x: 8,
        y: 15,
        width: 16,
        height: 7
      },
      {
        id: '8',
        componentName: 'logisticsService',
        componentTitle: '后勤服务数据统计',
        path: null,
        status: 1,
        x: 0,
        y: 7,
        width: 8,
        height: 8
      },
      {
        id: '9',
        componentName: 'wasteDeliveryRecord',
        componentTitle: '医废出库记录统计',
        path: null,
        status: 1,
        x: 0,
        y: 15,
        width: 8,
        height: 7
      }
    ]
    return {
      parentBox: {},
      dlayout:
        // x=> 4 8 12 小中大
        {
          breakpoint: 'xs',
          numberOfCols: 24,
          items: []
        },
      workBenchList: Object.freeze(workBenchList),
      reservedItems: [],
      dashExampleShow: true, // 工作台显示隐藏
      editExampleShow: false, // 编辑工作台显示隐藏
      activeExample: [] // 当前激活的工作台
    }
  },
  computed: {
    hasStatusFalg() {
      return this.dlayout.items.filter((e) => e.status === 0).length === this.dlayout.items.length
    }
  },
  mounted() {
    this.parentBox.width = this.$refs.contentBox.offsetWidth
    this.parentBox.height = this.$refs.contentBox.offsetHeight
    this.getWorktopManageList()
  },
  methods: {
    // 获取工作台管理列表
    getWorktopManageList() {
      this.editExampleShow = false
      this.$store.commit('settings/dragSidebarCollapse', false)
      this.$api.getWorktopManageList({ userId: this.$store.state.user.userInfo.user.staffId, menuType: 15 }).then((res) => {
        if (res.code == 200) {
          const data = res.data
          console.log('get初始化', data)
          let componentItems = []
          // 有接口数据取接口数据 无接口数据取字典默认配置数据
          if (data.length && data.length >= this.workBenchList.length) {
            componentItems = data
          } else {
            componentItems = this.workBenchList
          }
          const items = componentItems.map((item) => {
            return {
              id: item.id,
              componentName: item.componentName,
              componentTitle: item.componentTitle,
              x: item.x,
              y: item.y,
              width: item.width,
              height: item.height,
              dragAllowFrom: '.drag_class',
              resizable: false,
              draggable: false,
              path: item.path,
              status: item.status
            }
          })
          this.dashExampleShow = false
          this.$nextTick(() => {
            this.dashExampleShow = true
            this.dlayout.items = items
            this.reservedItems = JSON.parse(JSON.stringify(items))
            const hasStatusLength = items.filter((e) => e.status === 0).length
            hasStatusLength == items.length ? this.allMoreOper('edit') : ''
          })
        }
      })
    },
    // 拖拽结束事件
    moveEnd(item) {
      console.log('moveEnd', this.dlayout)
    },
    // 缩放结束事件
    resizeEnd(item) {
      const resizeList = ['workOderType']
      if (resizeList.includes(item.componentName)) {
        this.$refs[item.componentName][0].echartsResize()
      }
      // console.log('resizeEnd', this.dlayout)
    },
    // 添加/减少模块
    addStaging(item) {
      if (this.activeExample.includes(item.componentName)) {
        console.log('减少模块==========', item)
        this.activeExample.splice(this.activeExample.indexOf(item.componentName), 1)
        this.dlayout.items.map((e) => {
          if (e.componentName === item.componentName) {
            e.status = 0
          }
        })
      } else {
        console.log('添加模块==========', item)
        this.activeExample.push(item.componentName)
        this.dlayout.items.map((e) => {
          if (e.componentName === item.componentName) {
            e.status = 1
          }
        })
      }
    },
    // 取消编辑工作台模块
    cancelStaging() {
      this.editExampleShow = false
      this.dashExampleShow = false
      this.$nextTick(() => {
        this.dashExampleShow = true
        this.dlayout.items = JSON.parse(JSON.stringify(this.reservedItems))
        this.$store.commit('settings/dragSidebarCollapse', false)
      })
    },
    // 保存工作台模块
    saveStaging() {
      const items = this.dlayout.items
      const params = []
      items.forEach((item) => {
        params.push({
          id: item.id,
          componentName: item.componentName,
          componentTitle: item.componentTitle,
          path: item.path,
          status: item.status,
          x: item.x,
          y: item.y,
          width: item.width,
          height: item.height
        })
      })
      this.$api.saveWorktopManage({ jsonList: JSON.stringify(params), userId: this.$store.state.user.userInfo.user.staffId, menuType: 15 }).then((res) => {
        if (res.code == 200) {
          this.$message.success('保存成功')
          this.getWorktopManageList()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 更多操作事件
    allMoreOper(type, component) {
      if (type === 'more') {
        const path = this.dlayout.items.find((e) => e.componentName === component).path
        this.$router.push({ path: path })
      } else if (type === 'edit') {
        this.dlayout.items.map((e) => {
          e.resizable = true
          e.draggable = true
        })
        this.activeExample = this.dlayout.items.filter((e) => e.status === 1).map((e) => e.componentName)
        this.editExampleShow = true
        this.$store.commit('settings/toggleSidebarCollapse', true)
        this.$store.commit('settings/dragSidebarCollapse', true)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.page-container {
  margin: 0 auto;
  height: 100%;
  width: 100%;
  max-width: 1620px;
}
.staging-content {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: space-between;
  #dashExample {
    height: 100%;
    // background: #f5f7f9;
    overflow-y: auto;
    overflow-x: hidden;
    :deep(.placeholder) {
      background: #e2e6eb !important;
      border-radius: 10px;
      opacity: 1;
    }
  }
  #editExample {
    height: 100%;
    // background-color: #fff;
    box-shadow: 10px 0 10px -10px #c7c7c7;
    box-shadow: 10px 0 10px -10px #c7c7c7;
    padding: 16px 0;
    display: flex;
    flex-direction: column;
    .round-box {
      width: 100%;
      height: 100%;
      background-color: #08305d0a;
      border-radius: 10px;
      padding: 10px 0;
      display: flex;
      flex-direction: column;
    }
    .editExample-title {
      > div {
        font-size: 16px;
        font-family: PingFangSC-Regular-Blod;
        color: #121f3e;
        margin: 0 0 12px 15px;
        height: 22px;
        line-height: 22px;
      }
      > p {
        height: 18px;
        font-size: 12px;
        line-height: 18px;
        margin: 0 0 12px 15px;
        color: #999;
      }
    }
    .editExample-content {
      flex: 1;
      // height: calc(100% - 50px);
      overflow-y: auto;
      padding: 10px 20px;
      .editExample-content-item {
        cursor: pointer;
        width: 100%;
        box-sizing: border-box;
        height: 40px;
        line-height: 40px;
        margin-bottom: 16px;
        border-radius: 4px;
        padding: 0 10px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        background-color: #fff;
        text-align: center;
        color: #121f3e;
        font-size: 13px;
        font-family: 'PingFang SC-Regular', 'PingFang SC';
        &:hover {
          // color: #3562db;
          // background: rgba(53, 98, 219, 0.2);
          box-shadow: 0 4px 12px 0 rgb(17 19 23 / 10%);
        }
      }
      .active-editExample-item {
        background-color: #3562db !important;
        color: #fff !important;
        border-color: #3562db !important;
      }
    }
    .editExample-footer {
      text-align: right;
      padding: 5px 15px;
    }
  }
}
::-webkit-scrollbar-thumb:hover,
::-webkit-scrollbar-track:hover {
  background: transparent;
}
::-webkit-scrollbar-thumb {
  background-color: transparent;
}
</style>
