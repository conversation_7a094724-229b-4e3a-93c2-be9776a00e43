<template>
  <PageContainer style="position: relative">
    <div slot="content" class="whole">
      <div class="left">
        <div class="title_box"><svg-icon name="right-arrow" /> 空间结构</div>
        <el-input v-model="filterText" style="width: 230px; padding-top: 10px" placeholder="请输入关键字"></el-input>
        <div v-loading="treeLoading" class="sino_tree_box">
          <div class="tree_div">
            <el-tree
              ref="tree"
              class="filter-tree"
              :data="treeData"
              :props="defaultProps"
              :default-expanded-keys="idArr"
              :filter-node-method="filterNode"
              :highlight-current="true"
              node-key="id"
              @node-click="nodeClick"
            ></el-tree>
          </div>
        </div>
      </div>
      <div class="right">
        <div style="display: flex">
          <!-- <el-select v-model="filters.functionDictId" class="ipt" placeholder="请选择功能类型" filterable clearable>
            <el-option v-for="item in spaceTypeList" :key="item.id" :label="item.dictName" :value="item.id"></el-option> </el-select
          > -->
          <el-cascader v-model="filters.functionDictId" class="ipt" :options="functionDictOptions" :props="functionDictProps"> </el-cascader>
          &nbsp;<el-input v-model="filters.spaceLocalName" placeholder="请输入空间本地名称" style="width: 200px"></el-input>&nbsp;
          <el-button type="primary" @click="inquiry">查询</el-button>
          <el-button type="primary" plain @click="rest">重置</el-button>
        </div>
        <div slot="content" style="height: 100%">
          <div class="contentTable">
            <div class="contentTable-main table-content">
              <el-table :data="tableData" :height="tableHeight" stripe border>
                <el-table-column type="selection" width="50" align="center"></el-table-column>
                <el-table-column type="index" label="序号" width="80" align="center">
                  <template slot-scope="scope">
                    <span>{{ (pagination.current - 1) * pagination.size + scope.$index + 1 }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="空间编码" width="150" prop="modelCode" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <!-- class="color_blue" -->
                    <span>
                      {{ scope.row.modelCode }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column label="空间本地名称" prop="localSpaceName" show-overflow-tooltip> </el-table-column>
                <el-table-column label="本地编码" prop="localSpaceCode" show-overflow-tooltip> </el-table-column>
                <el-table-column label="功能类型" prop="functionDictName" show-overflow-tooltip> </el-table-column>
                <el-table-column label="归属部门" prop="dmName" show-overflow-tooltip> </el-table-column>
                <el-table-column label="空间责任人" prop="principalName" show-overflow-tooltip> </el-table-column>
                <el-table-column label="操作人" prop="opretionName" show-overflow-tooltip> </el-table-column>
                <el-table-column label="时间" prop="opretionTime" show-overflow-tooltip> </el-table-column>
              </el-table>
            </div>
          </div>
          <div class="contentTable-footer">
            <el-pagination
              :current-page="pagination.current"
              :page-sizes="[15, 30, 50, 100]"
              :page-size="pagination.size"
              :total="total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            ></el-pagination>
          </div>
        </div>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import store from '@/store/index'
import axios from 'axios'
import { transData } from '@/util'
import tableListMixin from '@/mixins/tableListMixin.js'
export default {
  name: 'spatialChangeRecord',
  mixins: [tableListMixin],
  data() {
    return {
      filters: {
        functionDictId: '',
        spaceLocalName: ''
      },
      functionDictOptions: [],
      functionDictProps: {
        label: 'name',
        value: 'id',
        leaf: 'leaf'
      },
      treeLoading: true,
      filterText: '',
      treeData: [],
      idArr: [],
      spaceIds: [],
      defaultProps: {
        label: 'ssmName',
        children: 'list'
      },
      // spaceTypeList: [],
      tableData: [],
      // -----------------------------Pagination
      pagination: {
        current: 1,
        size: 15
      }, // 分页数据
      total: 0 // 数据总条数
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {},
  mounted() {
    this.spaceTreeListFn()
    this.valveTypeListFn()
  },
  methods: {
    inquiry() {
      this.pagination.current = 1
      this.pagination.size = 15
      this.getSpacelistFn()
    },
    rest() {
      this.pagination.current = 1
      this.pagination.size = 15
      this.filters.functionDictId = ''
      this.filters.spaceLocalName = ''
      this.getSpacelistFn()
    },
    //  ------------------------------获取空间结构 Tree And fn
    spaceTreeListFn() {
      this.$api.spaceTreeList().then((res) => {
        this.treeLoading = false
        if (res.code == 200) {
          res.data.map((tree) => {
            tree.ssmType == 1 || tree.ssmType == 2 ? this.idArr.push(tree.id) : ''
            return this.idArr
          })
          this.treeData = transData(res.data, 'id', 'pid', 'list')
          this.$nextTick(function () {
            this.$refs.tree.setCurrentKey(res.data[0].id)
          })
          this.spaceIds.push(res.data[0].modelCode)
          this.getSpacelistFn()
        }
      })
    },
    filterNode(value, data) {
      if (!value) return true
      return data.ssmName.indexOf(value) !== -1
    },
    nodeClick(val) {
      const { tree } = this.$refs
      this.spaceIds = []
      let checkedNode = tree.getNode(val.id)
      this.checkedNode = checkedNode
      this.spaceIds.push(checkedNode.data.modelCode)
      // this.getTreeNode(checkedNode);
      if (checkedNode.level != 4 && checkedNode.level != 3) return this.$message.warning('请选择建筑或楼层')
      this.pagination.current = 1
      this.pagination.size = 15
      this.getSpacelistFn()
    },
    getTreeNode(node) {
      if (node.level >= 1) {
        this.spaceIds.unshift(node.data.id)
        this.getTreeNode(node.parent)
      } else {
        this.spaceIds.unshift('#')
      }
    },
    // ---------------------------Table-Fn
    //  获取功能类型字典列表
    valveTypeListFn() {
      this.$api.GetSuperiorData({ dictionaryCategoryId: 'SPACE_FUNCTION' }).then((res) => {
        if (res.code == 200) {
          this.functionDictOptions = this.findChild(res.data[0].children)
        } else {
          this.$message.warning(res.msg)
        }
      })
      // this.$api
      //   .valveTypeList({
      //     typeValue: 'SP'
      //   })
      //   .then((res) => {
      //     if (res.code == 200) {
      //       this.spaceTypeList = res.data
      //     }
      //   })
    },
    // 递归处理最末级数据
    findChild(treeData) {
      return findItem(treeData)
      function findItem(data) {
        data.forEach((i) => {
          if (!i.children.length) {
            delete i.children
            i.leaf = false
          } else {
            findItem(i.children)
          }
        })
        return treeData
      }
    },
    getSpacelistFn() {
      const { functionDictId, spaceLocalName } = this.filters
      let data = {
        modelCodes: '',
        spaceLocalName,
        functionDictId: functionDictId[functionDictId.length - 1] || '',
        ...this.pagination
      }
      data.modelCodes = this.spaceIds.join()
      this.$api.spacePageList(data).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.records
          this.pagination.current = res.data.current
          this.pagination.size = res.data.size
          this.total = res.data.total
        }
      })
    },
    handleSizeChange(val) {
      this.pagination.size = val
      this.getSpacelistFn()
    },
    handleCurrentChange(val) {
      this.pagination.current = val
      this.getSpacelistFn()
    }
    // ---------------------------sino-slide-Panel
  }
}
</script>
<style lang="scss" scoped>
.page-container {
  position: relative;
}
.sino_tab {
  .sino_tab_content {
    height: 280px;
    overflow: auto;
    .sino_tab_btn {
      position: absolute;
      right: 0;
      top: 0;
      margin: 0;
    }
    .line_style {
      border-bottom: 1px solid #ddd;
    }
  }
  .sino_tab_btn_box {
    height: 280px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .floor_box {
    line-height: 32px;
    margin: 0 100px 0 20px;
    padding-left: 15px;
    font-size: 14px;
  }
}
.checkbox_group {
  height: 200px;
  overflow: auto;
}
.el-main {
  padding: 15px;
  overflow: hidden;
  background-color: #fff;
  .sino_panel {
    height: 100%;
    border-radius: 10px;
    background: #fff;
    position: relative;
    font-size: 14px;
  }
  .sino_page {
    height: 100%;
    position: relative;
    .el-aside {
      width: 260px;
      margin: 0 16px 0 0;
      overflow: hidden;
    }
    .sino_page_left {
      margin: 0 0 0 16px !important;
    }
    .el-aside,
    .el-main {
      // height: 100%;
      background: #fff;
      border-radius: 10px;
      padding: 0;
      .el-collapse {
        height: calc(100% - 40px);
        overflow: auto;
      }
    }
  }
}
.title_box {
  box-sizing: border-box;
  padding-left: 24px;
  height: 50px;
  width: 100%;
  line-height: 50px;
  text-align: left;
  border-bottom: 1px solid #d8dee7;
  .title-tip {
    display: inline-block;
    width: 4px;
    height: 16px;
    margin-right: 8px;
    position: relative;
    top: 2px;
    background: #5188fc;
  }
  .title_name {
    font-size: 16px;
    font-weight: bold;
  }
  .title_btn_icon {
    float: right;
  }
  .title_btn_icon i {
    margin-right: 20px;
    cursor: pointer;
  }
}
.whole {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  .left {
    text-align: center;
    width: 250px;
    height: 100%;
    background-color: #fff;
    border-radius: 5px;
    overflow: hidden;
    .left_d {
      height: calc(100% - 20px);
    }
  }
  .right {
    width: calc(100% - 260px);
    background-color: #fff;
    height: 100%;
    border-radius: 5px;
    padding: 10px;
  }
}
.contentTable {
  height: calc(100% - 58px);
  background: #fff;
  border-radius: 4px;
  padding: 16px 0 0;
  display: flex;
  flex-direction: column;
  overflow: auto;
  .contentTable-main {
    flex: 1;
    overflow: auto;
  }
  .contentTable-footer {
    padding: 10px 0 0;
  }
  .alarmLevel {
    padding: 3px 6px;
    border-radius: 4px;
    color: #fff;
    line-height: 14px;
  }
  .alarmStatus {
    position: relative;
    display: inline-block;
    padding-left: 12px;
    .alarmStatusIcon {
      display: inline-block;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 8px;
      height: 8px;
      border-radius: 100%;
    }
  }
  .collectIcon {
    font-size: 16px;
    margin-right: 4px;
  }
}
.sino_tree_box {
  margin: 10px;
  height: calc(100% - 100px) !important;
  overflow: auto;
  .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
    background-color: #f5f6fb;
  }
  .el-tree-node__content {
    height: 38px;
    .el-tree-node__label {
      font-size: 15px;
    }
  }
}
.color_blue {
  color: #5197fd;
  cursor: pointer;
}
.ipt {
  width: 200px;
  margin-right: 10px;
}
.el_radio {
  width: 120px;
  padding: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
}
::v-deep .el-dialog {
  width: 70%;
}
</style>
