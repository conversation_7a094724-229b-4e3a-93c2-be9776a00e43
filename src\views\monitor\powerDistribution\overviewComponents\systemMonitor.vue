<!--
 * @Author: hedd
 * @Date: 2023-07-12 17:25:37
 * @LastEditTime: 2023-08-08 16:07:19
 * @FilePath: \ihcrs_pc\src\views\monitor\powerDistribution\overviewComponents\systemMonitor.vue
 * @Description:
-->
<!-- 系统监测 -->
<template>
  <ContentCard :title="item.componentTitle" :scrollbarHover="true" :cstyle="{ height: '100%' }" class="drag_class"  :hasMoreOper="['edit']" @more-oper-event="(val) => $emit('all-more-Oper', val, 'systemMonitor')">
    <div slot="content" class="system-list">
      <div v-for="item in systemList" :key="item.name" class="list-item" :style="{ backgroundColor: item.bgColor }" @click="goTo(item.path)">
        <img :src="item.icon" alt="item.name">
        <p>{{ item.name }}</p>
      </div>
    </div>
  </ContentCard>
</template>

<script>
import spjk_icon from '@/assets/images/monitor/spjk_icon.png'
import xtt_icon from '@/assets/images/monitor/xtt_icon.png'
export default {
  name: 'systemMonitor',
  props: {
    item: {
      type: Object,
      default: () => {}
    },
    requestInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      systemList: [
        {name: '视频监控', bgColor: '#3562DB', icon: spjk_icon, path: '/powerDistribution/overview/videoMonitor'},
        {name: '系统图', bgColor: '#FF9435', icon: xtt_icon, path: '/powerDistribution/overview/systemDiagram'}
      ]
    }
  },
  computed: {

  },
  created() {

  },
  methods: {
    goTo(path) {
      this.$router.push({
        path,
        query: this.requestInfo
      })
    }
  }
}

</script>

<style lang="scss" scoped>
.system-list {
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-evenly;

  .list-item {
    cursor: pointer;
    width: 166px;
    display: flex;
    align-items: center;
    padding: 16px 0 16px 24px;
    border-radius: 4px;

    p {
      margin-left: 10px;
      font-size: 15px;
      font-weight: 500;
      color: #fff;
      margin-bottom: 0px;
    }

    img {
      width: 48px;
      height: 48px;
    }
  }
}
</style>
