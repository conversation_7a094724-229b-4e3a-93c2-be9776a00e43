<template>
  <PageContainer>
    <div slot="content" class="targetPlan-body">
      <div class="targetPlan-content-title">指标计划</div>
      <div class="topStatistics">
        <div class="topStatistics_img"></div>
        <div class="topStatistics_main">
          <div class="topStatistics_main_item">
            <p>计划总数</p>
            <p>{{ planStatistics.total || 0 }}<span>个</span></p>
          </div>
          <div class="topStatistics_main_item">
            <p>待开始计划</p>
            <p class="toBegin">{{ planStatistics.notStart || 0 }}<span>个</span></p>
          </div>
          <div class="topStatistics_main_item">
            <p>进行中计划</p>
            <p class="underway">{{ planStatistics.starting || 0 }}<span>个</span></p>
          </div>
          <div class="topStatistics_main_item">
            <p>已结束计划</p>
            <p class="finished">{{ planStatistics.finish || 0 }}<span>个</span></p>
          </div>
        </div>
      </div>
      <div class="targetPlan-content">
        <div style="height: 100%">
          <div class="search-from">
            <div>
              <el-input
                v-model.trim="searchForm.indicatorPlanName"
                placeholder="请输入计划名称"
                style="width: 200px"
                clearable
                maxlength="25"
                onkeyup="if(value.length>25)value=value.slice(0,25)"
              ></el-input>
              <el-select v-model="searchForm.state" placeholder="全部状态" style="margin-left: 16px">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
              <el-button type="primary" plain style="margin-left: 10px" @click="reset">重置</el-button>
              <el-button type="primary" @click="search">查询</el-button>
            </div>
          </div>
          <div>
            <el-button type="primary" style="font-size: 14px" @click="addFn()">添加指标计划</el-button>
          </div>
          <div class="contentTable">
            <div class="contentTable-main table-content">
              <el-table v-loading="tableLoading" border style="width: 100%" :data="tableData" :height="tableHeight" :row-class-name="tableRowClassName">
                <el-table-column prop="name" label="计划名称" show-overflow-tooltip></el-table-column>
                <el-table-column prop="cycleRuleName" label="周期" show-overflow-tooltip></el-table-column>
                <el-table-column prop="templateManageName" label="模板" show-overflow-tooltip></el-table-column>
                <el-table-column prop="indicatorScope" label="考察范围" show-overflow-tooltip></el-table-column>
                <el-table-column prop="state" label="计划状态" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span
                      :class="['targetState', `targetState${scope.row.state}`]"
                      :style="{ color: scope.row.state == 0 ? '#D25F00' : scope.row.state == 1 ? '#2749BF' : '#86909C' }"
                    >
                      {{ scope.row.state == 0 ? '待开始' : scope.row.state == 1 ? '进行中' : '已结束' }}</span
                    >
                  </template>
                </el-table-column>
                <el-table-column prop="startTime" label="开始时间" show-overflow-tooltip></el-table-column>
                <el-table-column prop="endTime" label="结束时间" show-overflow-tooltip></el-table-column>
                <el-table-column label="操作" width="180" fixed="right">
                  <template slot-scope="scope">
                    <el-button type="text" class="record" @click="operation(scope.row, 'check')">查看</el-button>
                    <el-button type="text" class="record" @click="operation(scope.row, 'edit')">编辑</el-button>
                    <el-button type="text" class="record" @click="operation(scope.row, 'delete')">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="contentTable-footer">
              <el-pagination
                :current-page="pagination.current"
                :page-sizes="pagination.pageSizeOptions"
                :page-size="pagination.size"
                :layout="pagination.layoutOptions"
                :total="pagination.total"
                @size-change="paginationSizeChange"
                @current-change="paginationCurrentChange"
              >
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin.js'
export default {
  name: 'targetPlan',
  mixins: [tableListMixin],
  data() {
    return {
      searchForm: {
        indicatorPlanName: '',
        state: ''
      },
      planStatistics: {
        finish: 0, // 已完成
        notStart: 0, // 未开始
        starting: 0, // 进行中
        total: 0 // 总数
      },
      options: [
        {
          value: '0',
          label: '待开始'
        },
        {
          value: '1',
          label: '进行中'
        },
        {
          value: '2',
          label: '已结束'
        }
      ],
      tableLoading: false,
      tableData: []
    }
  },
  mounted() {
    this.getDataStatistics()
    this.getDataList()
  },
  methods: {
    // 统计数据
    getDataStatistics() {
      this.$api.getTargetPlanStatistics({}).then((res) => {
        if (res.code == 200) {
          this.planStatistics = res.data
        }
      })
    },
    // 列表数据
    getDataList() {
      this.tableLoading = true
      let data = {
        ...this.searchForm,
        page: this.pagination.current,
        pageSize: this.pagination.size
      }
      this.$api
        .getTargetPlanList(data)
        .then((res) => {
          this.tableLoading = false
          if (res.code == 200) {
            this.tableData = res.data ? res.data.records : []
            this.pagination.total = res.data ? res.data.total : 0
          } else if (res.message) {
            this.tableData = []
            this.pagination.total = 0
            this.$message.error(res.message)
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    // 重置
    reset() {
      this.searchForm = {
        indicatorPlanName: '',
        state: ''
      }
      this.pagination.size = 15
      this.pagination.current = 1
      this.getDataList()
    },
    // 查询
    search() {
      this.pagination.current = 1
      this.getDataList()
    },
    // 新增计划
    addFn() {
      this.$router.push('/targetPlan/addTargetPlan')
    },
    // 列表操作
    operation(row, type) {
      // 编辑
      if (type == 'edit') {
        this.$router.push({
          path: '/targetPlan/addTargetPlan',
          query: {
            planId: row.id
          }
        })
      } else if (type == 'delete') {
        this.$confirm('删除后将无法恢复，是否确定删除？', '信息提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.$api.deleteTargetPlanById({ planId: row.id }, { 'operation-type': 3, 'operation-id': row.id, 'operation-name': row.name }).then((res) => {
              if (res.code == '200') {
                this.$message({
                  type: 'success',
                  message: '删除成功'
                })
                this.getDataList()
                this.getDataStatistics()
              } else {
                this.$message({
                  type: 'error',
                  message: res.message || '删除失败'
                })
              }
            })
          })
          .catch(() => {})
      } else if (type == 'check') {
        this.$router.push({
          path: '/targetPlan/targetPlanDetail',
          query: {
            planId: row.id
          }
        })
      }
    },
    // table隔行变色
    tableRowClassName({ row, rowIndex }) {
      if (rowIndex % 2 == 1) {
        return 'warning-row'
      } else {
        return 'success-row'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.targetPlan-body {
  height: 100%;
  width: 100%;
  background: #fff;
  .targetPlan-content-title {
    border-bottom: 1px solid #e4e7ed;
    padding: 13px 24px;
    cursor: pointer;
  }
  .topStatistics {
    width: 100%;
    height: 100px;
    margin: 16px 0;
    display: flex;
    align-items: center;
    .topStatistics_img {
      background: url('@/assets/images/targetAnalysis/targetPlan-statistics.png') no-repeat;
      background-size: 66px;
      background-position: center;
      width: 66px;
      height: 73px;
      box-sizing: content-box;
      padding: 0px 48px;
      border-right: 1px solid #e4e7ed;
      margin-right: 22px;
    }
    .topStatistics_main {
      flex: 1;
      display: flex;
      align-items: center;
      .topStatistics_main_item {
        width: 30%;
        text-align: left;
        p:first-child {
          font-size: 15px;
          font-weight: 500;
          color: #333333;
          line-height: 18px;
          margin-bottom: 16px;
        }
        p:last-child {
          font-size: 26px;
          font-weight: 500;
          color: #333333;
          margin-bottom: 0;
          span {
            font-size: 14px;
            font-weight: 500;
            color: #7f848c;
            margin-left: 4px;
          }
        }
      }
    }
  }
  .targetPlan-content {
    padding: 8px 24px;
    height: calc(100% - 183px);
    .search-from {
      padding-bottom: 12px;
      display: flex;
      justify-content: space-between;

      & > div {
        margin-right: 10px;
      }

      & > button {
        margin-top: 12px;
      }
    }

    .contentTable {
      margin-top: 16px;
      height: calc(100% - 100px);
      display: flex;
      flex-direction: column;

      .contentTable-main {
        flex: 1;
        overflow: auto;
      }
      .contentTable-footer {
        padding: 10px 0 0;
      }
      .targetState {
        padding: 2px 8px;
        background: #e6effc;
        border-radius: 4px;
      }
      .targetState0 {
        background: #fff7e8;
      }
      .targetState1 {
        background: #e6effc;
      }
      .targetState2 {
        background: #f2f4f9;
      }
    }
  }
}
.record {
  color: #3562db !important;
}

::v-deep .warning-row {
  background-color: #f5f5f5 !important;
}
.toBegin {
  color: #ff9435 !important;
}
.underway {
  color: #3562db !important;
}
.finished {
  color: #7f848c !important;
}
</style>
