<template>
  <div>
    <!-- 选择题型盒子 -->
    <div class="questionBox">
      <!-- 全员职工盒子 -->
      <div style="padding: 10px 0;">
        <el-radio v-model="type" label="all" @change="clickType('cleanAll', 'all')" @input="radioIn">
          <span class="allEmployees">一、全院职工（全员发放问卷，可指定不发人员）</span>
          <el-button type="primary" @click="choosePeople">选择</el-button>
          <div style="color: #666;">
            <span v-for="(people, index) in checkedPeoples" :key="people.id">
              {{ people.label }}
              <i style="cursor: pointer; line-height: 20px;" class="el-input__icon el-icon-error" @click="delPeople(people)"></i>
              <span v-show="checkedPeoples.length > 1 && index !== checkedPeoples.length - 1">、</span>
            </span>
          </div>
        </el-radio>
      </div>
      <hr class="hrStyle" />
      <!-- 组织架构定义 -->
      <div>
        <el-radio v-model="type" label="union" @change="clickType('cleanAll')" @input="radioIn">
          <span class="organizationalStructure">二、按组织架构自定义</span>
        </el-radio>
        <div>
          <!-- <el-collapse v-model="activeName" accordion @change="collChange"> -->
          <!-- <el-collapse-item v-for="item in hospitalList" :key="item.umId" :title="item.unitComName" :name="item.umId"> -->
          <el-tree
            ref="tree"
            v-loading="treeLoading"
            :data="treeList"
            :props="defaultProps"
            :default-checked-keys="selectedByDefault"
            :node-key="treeFlag.exhibition"
            show-checkbox
            @check="handleNodeClick"
          ></el-tree>
          <!-- </el-collapse-item> -->
          <!-- </el-collapse> -->
        </div>
        <!-- <el-tree
          ref="tree"
          class="treeStyle"
          @check="handleNodeClick"
          :data="data"
          show-checkbox
          :default-checked-keys="selectedByDefault"
          :node-key="treeFlag.exhibition"
          :props="defaultProps"
        ></el-tree> -->
      </div>
      <!-- 组织架构定义结束 -->
      <hr class="hrStyle" />
      <!-- 指定人员 -->
      <div>
        <el-radio v-model="type" label="people" @change="clickType('cleanAll')" @input="radioIn">
          <span class="organizationalStructure">三、指定人员</span>
        </el-radio>
        <el-button type="primary" @click="choosePeople">选择</el-button>
        <span v-for="(people, index) in checkedPeople" :key="people.id">
          {{ people.label }}
          <i style="cursor: pointer; line-height: 20px;" class="el-input__icon el-icon-error" @click="delPeople(people)"></i>
          <span v-show="checkedPeople.length > 1 && index !== checkedPeople.length - 1">、</span>
        </span>
      </div>
      <div>
        <el-radio v-model="type" label="noLimit" @change="clickType('cleanAll')" @input="radioIn">
          <span class="organizationalStructure">四、不限制人员类型</span>
        </el-radio>
      </div>
      <!-- 指定人员 结束-->

      <!-- 选择人员弹框 -->
      <selectPeople
        v-if="peopleVis"
        :arrData="arrData"
        :tableDataList="tableDataList"
        :cancleDatasLists="cancleDatasLists"
        :tableCancleDatas3Lists="tableCancleDatas3Lists"
        :selectTypes="selectType"
        @closePeopleDialog="closePeopleDialog"
        @chosedPeopleFn="chosedPeopleFn"
        @tableValFn="tableValFn"
      />
      <!-- 选择人员弹框 结束 -->
    </div>
    <!-- 选择题型盒子结束 -->
    <el-button v-loading="btnLoading" type="primary" @click="doConfirmBeforeEdit('save')">保存</el-button>
  </div>
</template>
<script>
import { transData, ListTree } from '@/util'
import store from '@/store/index'
// import { getofflicelist, saveAuthrange, getauthrange, updateauthrange, getDictValueByIdentitys } from '../../common/api'
// import utils from '../createEscort/utils/utils'
import selectPeople from '../selectPeople/selectPeople.vue'
// import MonthDays from 'month-days-element'
export default {
  components: {
    selectPeople
    // MonthDays
  },
  data() {
    return {
      defaultProps: {
        children: 'children',
        label: 'deptName'
      },
      treeLoading: false,
      btnLoading: false,
      activeName: '',
      hospitalList: [],
      collLoading: true,
      treeList: [],
      arrData: '',

      showEdit: false,
      dateLine1: '',
      dateLine2: '',
      dateLine3: '',
      dateLine4: '',
      selectedSex: '',
      selectedIdentify: '',
      dateLine: '',
      sexList: [
        // 性别
        {
          value: '1',
          label: '男'
        },
        {
          value: '2',
          label: '女'
        }
      ],
      identifyList: [], // 会员身份
      treeFlag: { exhibition: 'id', obtain: 'office_id' }, // 树状图默认选中 要取的标示位
      selectedByDefault: [], // 默认选中的节点
      checkList: [], // 多选框保存数据
      type: 'all', // 选择收集类型  all是全员职工 union是组织架构  type是按人员类型  默认选择全员职工
      data: [], // 组织架构需要展示的数据
      pvqid: localStorage.getItem('questId'), // 获取问卷id
      pvqName: JSON.parse(localStorage.getItem('localData')).name, // 获取问卷id
      getDate: [
        // 获取数据
        {
          key: 'questionId', // 问卷ID   // 给后端要发送的key名
          valeue: 'pvqid' // 需要从this中获取的kye名
        },
        {
          key: 'authLevel', // 答卷类型
          valeue: 'type'
        },
        {
          key: 'questionName', // 问卷名称
          valeue: 'pvqName'
        }
      ],
      authDataConfig: {
        // authData数据配置
        type: {
          transformation: false, // 前转换
          // 按类型区分配置
          saveConfig: [{ key: 'id', value: 'id' }],
          value: 'authData',
          key: 'checkList', // 要获取的key
          saveFun: 'typeFunSave',
          fun: 'typeFun',
          type: 'SignUpByStaffType'
        },
        all: {
          transformation: true, // 前转换
          sign: true, // 不添加authData属性
          type: 'SignUpAll',
          value: 'authData'
        },
        union: {
          saveConfig: [
            {
              key: 'id',
              value: 'id'
            },
            // {
            //   key: 'pmId',
            //   value: 'umId'
            // },
            {
              key: 'deptName',
              value: 'label'
            },

            {
              key: 'level',
              value: 'level'
            }
          ],
          // 科室区分配置
          value: 'authData',
          saveFun: 'unionFunSave', // 保存转换函数
          key: 'selectNodeArr', // 要获取的key
          fun: 'unionFun', // 保存转换函数
          type: 'SignUpByOffice'
        },
        people: {
          transformation: false, // 前转换
          saveFun: 'peopleFunSave', // 保存转换函数
          value: 'authData',
          type: 'SignUpByStaff',
          saveConfig: [
            {
              key: 'staffId',
              value: 'id'
            },
            {
              key: 'label',
              value: 'staffName'
            }
          ],
          key: 'checkedPeople', // 要获取的key
          fun: 'peopleFun' // 保存转换函数
        },
        noLimit: {
          transformation: true, // 前转换
          sign: true, // 不添加authData属性
          type: 'SignUpAll',
          value: 'authData'
        }
      },
      selectNodeArr: [], // 当数据为 组织架构是 选中的数据结果集
      storageIdentification: { flag: false, authId: '' }, // 之前是否存储过
      objArr: [],
      selectedPost: '', // 选择的岗位
      postList: [], // 岗位list
      checkedPeople: [], // 选中的人员list
      checkedPeoples: [], // 选中的人员list
      peopleVis: false, // 人员弹框
      tableDatas: [],
      tableDatas3: [],
      tableValLists: '',
      tableDataList: '',
      cancleDatas: '',
      cancleDatasLists: '',
      tableCancleDatas3: [],
      tableCancleDatas3Lists: '',
      rangeDatas: '',
      selectType: ''
    }
  },

  created() {
    this.getTreeList()

    // 获取会员身份
    this._getIdentifyFn('staffIdentity', 'identifyList')
    // 从本地获取问卷信息
    this.getPvqInfo()
    this.getauthrange()
    this.getSelectedDept()
  },
  methods: {
    getTreeList() {
      // this.$api.getSelected({}, __PATH.USER_CODE).then((res) => {
      this.$api.getSelected({}).then((res) => {
        if (res.code == 200) {
          this.hospitalList = res.data
        }
      })
    },
    getSelectedDept() {
      this.treeLoading = true
      this.$api.getSelectedDept().then((res) => {
        this.collLoading = false
        if (res.code == 200) {
          this.treeLoading = false
          res.data.forEach((item) => {
            item.level = 3
          })
          this.treeList = transData(res.data, 'id', 'pid', 'children')
        }
      })
    },
    // collChange(val) {
    //   this.officeId = ''
    //   this.pmId = val
    //   if (val != '') {
    //     this.treeList = []
    //     this.$api.getSelectedDept({ unitId: val }).then((res) => {
    //       this.collLoading = false
    //       if (res.code == 200) {
    //         res.data.forEach((item) => {
    //           item.level = 3
    //         })
    //         this.treeList = transData(res.data, 'id', 'pid', 'children')
    //       }
    //     })
    //   } else {
    //   }
    //   this.collLoading = true
    // },

    tableValFn(tableValList, tableDatas) {
      this.tableValLists = tableValList
      this.tableDatas3 = tableDatas
    },
    getValue(val) {
      this.dateLine1 = val
      // this.saveRange("checkList");
    },
    getValue1(val) {
      this.dateLine2 = val
      // this.saveRange("checkList");
    },
    // 权限控制
    // checks(code,str){
    //     return this.check.isDisplay(code,str,this.routerAssign['rightOfWay']);
    // },
    selectChange() {},
    // 获取开始时间和结束时间
    tiemChangeFn(val) {
      if (val && val[0]) {
        this.dateLine = [val[0], val[1]]
        //  this.saveRange("checkList");
      }
    },
    // 获取会员身份
    _getIdentifyFn(type, getArr) {
      // let identify = this.$store.state.loginInfo.identity
      // getDictValueByIdentitys({
      //   dictType: type,
      //   identity: identify
      // }).then((res) => {
      //   if (res.code == 200) {
      //     this[getArr] = res.data
      //   }
      // })
    },
    peopleFunSave(arr, configObj) {
      return arr
    },
    getauthrange() {
      this.$api.getAuthRange({ questionId: this.pvqid }).then((res) => {
        if (res.status == 200) {
          if (res.data.authRange) {
            // 之前有存储过收集目标
            this.storageIdentification.flag = true
            // 之前有存储过收集ID
            this.storageIdentification.authId = res.data.authRange.id
            // 给单选按钮赋值
            this.type = res.data.authRange.authLevel

            // 指定不发放人员回显
            if (res.data.authRange.excludeStaff) {
              this.checkedPeoples = JSON.parse(res.data.authRange.excludeStaff)
            } else {
              this.checkedPeoples = []
            }
            // 获取按钮配置
            var authDataConfig = this.authDataConfig[this.type]
            // 判断类型  all 类型不需要赋值
            if (!authDataConfig.transformation) {
              if (res.data.authRange.authData) {
                // 新的接口返回
                this[authDataConfig.key] = this.newDataBack(res.data.authRange.authData)
                // 起掉转换函数
              } else {
                this[authDataConfig.key] = res.data.authRef
                // 起掉转换函数
              }
              this[authDataConfig.fun](this[authDataConfig.key])
            }
          }
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 新的数据返回
    newDataBack(data) {
      var officeList = JSON.parse(data).officeList
      if (officeList) {
        officeList.forEach((val) => {
          if (val.id == 'birthday' && val.startDate) {
            this.showEdit = true
            this.dateLine3 = val.startDate
            this.dateLine4 = val.endDate
          }
        })
      }
      this.treeFlag.obtain = 'id'
      return officeList
    },
    // 复制单选按钮F
    contentClick(str, cleanStr) {
      this.type = str
      this.clickType(cleanStr)
      this.clickType('cleanPeople')
    },
    // 点击事件
    clickType(str) {
      console.log(str,'str000000000000000000');
      
      this[str]()
    },
    radioIn() {
      this.$refs.tree.setCheckedKeys([])
    },
    cleanUnion() {
      // this.$refs.tree.setCheckedKeys([]) //清空按科室分派选中数据
    },
    cleanType() {
      this.checkList = [] // 清空按身份类型分派选中数据
      this.selectedPost = '' // 清空选中的岗位
      this.selectedSex = '' // 清空选中的岗位
      this.selectedIdentify = '' // 清空选中的岗位
      this.selectedBirthday = '' // 清空选中的岗位
      this.showEdit = false
      this.dateLine1 = ''
      this.dateLine2 = ''
    },
    // 清除选中的人员
    cleanPeople() {
      this.checkedPeople = []
      this.checkedPeoples = []
    },
    // 清空函数
    cleanAll() {
      this.cleanUnion()
      this.cleanType()
      this.cleanPeople()
    },
    // 按照人员类型 转换  //预留请勿删除
    typeFunSave(arr, configObj) {
      var copyArr = []
      for (var b = 0; b < arr.length; b++) {
        var obj = {}
        for (var a = 0; a < configObj.length; a++) {
          obj[configObj[a].key] = arr[b]
          if (arr[b] == 'post') obj.postType = this.selectedPost
          if (arr[b] == 'sex') obj.sexType = this.selectedSex
          if (arr[b] == 'identity') obj.identityType = this.selectedIdentify
          if (arr[b] == 'birthday') {
            obj.startDate = this.dateLine1
            obj.endDate = this.dateLine2
          }
        }
        copyArr.push(obj)
      }
      return copyArr
    },
    //  按照 结构树 转换
    unionFunSave(arr, configObj) {
      var arrCopy = []
      for (var b = 0; b < arr.length; b++) {
        var obj = {}
        for (var a = 0; a < configObj.length; a++) {
          obj[configObj[a].value] = arr[b][configObj[a].key]
        }
        arrCopy.push(obj)
      }
      return arrCopy
    },
    // 按照人员类型 转换  //预留请勿删除
    typeFun(arr) {
      this.checkList = []
      for (var a = 0; a < arr.length; a++) {
        this.checkList.push(arr[a].id || arr[a].office_id)
        if (arr[a].id == 'post') this.selectedPost = arr[a].postType
        if (arr[a].id == 'sex') this.selectedSex = arr[a].sexType
        if (arr[a].id == 'identity') this.selectedIdentify = arr[a].identityType
        if (arr[a].id == 'birthday') {
          this.dateLine = [arr[a].startDate, arr[a].endDate]
        }
      }
    },
    peopleFun() {},
    //  按照 结构树 转换
    unionFun(arr) {
      var copyArr = []
      for (var a = 0; a < arr.length; a++) {
        copyArr.push(arr[a][this.treeFlag.obtain])
      }
      this.selectedByDefault = copyArr
    },
    // 获取 按照组织结构选择的工会信息
    handleNodeClick(data, node, cks, dks) {
      this.clickType('cleanType')
      this.clickType('cleanPeople')
      this.type = 'union'
      // 保存 选择的 机构信息
      this.selectNodeArr = node.checkedNodes
    },
    // 获取问卷 信息
    getPvqInfo() {
      // var obj = this.getLocalStorage('localData')
      // this.pvqid = obj.id
      // this.pvqName = obj.name
    },
    // 从本地localStorage中获取问卷ID
    getLocalStorage(path) {
      var getLocalData = localStorage.getItem(path)
      var obj = JSON.parse(getLocalData)
      return obj
    },
    // 获取请求参数
    getData() {
      var data = {}
      // 循环要获取的配置 获取this中的值
      var config = this.getDate
      console.log(config,'config000000000000000');
      
      for (var a = 0; a < config.length; a++) {
        data[config[a].key] = this[config[a].valeue]
      }
      // 赋值authData
      console.log(this.type,'this.type');
      
      var authDataConfig = this.authDataConfig[this.type]
      console.log(authDataConfig,'authDataConfigauthDataConfig');
      
      if (!authDataConfig.sign) {
        // 转换函数
        var arr = this[authDataConfig.saveFun](this[authDataConfig.key], authDataConfig.saveConfig)
        let params = {
          type: authDataConfig.type,
          officeList: arr
        }
        data[authDataConfig.value] = JSON.stringify(params)
      } else {
        let params = {
          type: authDataConfig.type
        }
        data[authDataConfig.value] = JSON.stringify(params)
      }
      const userInfo = store.state.user.userInfo.user
      data.userId = userInfo.staffId
      data.userName = userInfo.staffName
      data.hospitalCode = userInfo.hospitalCode
      data.unitCode = userInfo.unitCode
      return data
    },
    // 弹出层提示
    popUpPrompt(res) {
      if (res.status == 200) {
        this.btnLoading = false
        this.$message.success(res.message)
      } else {
        this.btnLoading = false
        this.$message.error(res.message)
      }
      this.getauthrange()
    },
    // 保存选择的机构
    save() {
      var data = this.getData()
      console.log(data,'data0000000000000000');
      
      // 获取存储的配置
      var storageIdentification = this.storageIdentification

      let oLScope = JSON.parse(data.authData)

      const { officeList } = oLScope
      data.excludeStaff = oLScope.type == 'SignUpAll' && this.checkedPeoples && this.checkedPeoples.length ? JSON.stringify(this.checkedPeoples) || '' : ''
      let obj = {
        SignUpByOffice: '组织架构',
        SignUpByStaffType: '人员类型',
        SignUpByStaff: '人员'
      }
      if (oLScope.type != 'SignUpAll' && (!officeList || !officeList.length)) return this.$message.error('请选择' + obj[oLScope.type])
      if (oLScope.type == 'SignUpByStaffType') {
        // let _index = officeList.findIndex(item => {
        //   return item.id == "post";
        // });
        // if (_index >= 0 && !officeList[_index].postType)
        //   return this.$message.error("请选择岗位");
        if (officeList[0] && officeList[0].id == 'post' && !officeList[0].postType) {
          this.$message.error('请选择岗位')

          return false
        }
        if (officeList[0] && officeList[0].id == 'sex' && !officeList[0].sexType) {
          this.$message.error('请选择性别')

          return false
        }
        if (officeList[0] && officeList[0].id == 'identity' && !officeList[0].identityType) {
          this.$message.error('请选择会员身份')

          return false
        }
        if (officeList[0] && officeList[0].id == 'birthday' && !officeList[0].startDate) {
          this.$message.error('请选择生日')

          return false
        }
        if (officeList[0] && officeList[0].id == 'birthday' && officeList[0].startDate) {
          if (officeList[0].startDate == '' || officeList[0].endDate == '') {
            this.$message.error('开始日期或结束日期不可为空')
            return false
          }
          let mon1 = officeList[0].startDate.split('-')[0]
          let mon2 = officeList[0].endDate.split('-')[0]
          let date1 = officeList[0].startDate.split('-')[1]
          let date2 = officeList[0].endDate.split('-')[1]
          if (mon1 == mon2) {
            if (date1 > date2) {
              this.$message.error('开始日期应早于结束日期')
              return false
            }
          }
        }
        if (officeList[1] && officeList[1].id == 'birthday' && officeList[1].startDate) {
          if (officeList[1].startDate == '' || officeList[1].endDate == '') {
            this.$message.error('开始日期或结束日期不可为空')
            return false
          }
          let mon1 = officeList[1].startDate.split('-')[0]
          let mon2 = officeList[1].endDate.split('-')[0]
          let date1 = officeList[1].startDate.split('-')[1]
          let date2 = officeList[1].endDate.split('-')[1]
          if (mon1 == mon2) {
            if (date1 > date2) {
              this.$message.error('开始日期应早于结束日期')
              return false
            }
          }
        }
        if (officeList[2] && officeList[2].id == 'birthday' && officeList[2].startDate) {
          if (officeList[2].startDate == '' || officeList[2].endDate == '') {
            this.$message.error('开始日期或结束日期不可为空')
            return false
          }
          let mon1 = officeList[2].startDate.split('-')[0]
          let mon2 = officeList[2].endDate.split('-')[0]
          let date1 = officeList[2].startDate.split('-')[1]
          let date2 = officeList[2].endDate.split('-')[1]
          if (mon1 == mon2) {
            if (date1 > date2) {
              this.$message.error('开始日期应早于结束日期')
              return false
            }
          }
        }
        if (officeList[3] && officeList[3].id == 'birthday' && officeList[3].startDate) {
          if (officeList[3].startDate == '' || officeList[3].endDate == '') {
            this.$message.error('开始日期或结束日期不可为空')
            return false
          }
          let mon1 = officeList[3].startDate.split('-')[0]
          let mon2 = officeList[3].endDate.split('-')[0]
          let date1 = officeList[3].startDate.split('-')[1]
          let date2 = officeList[3].endDate.split('-')[1]
          if (mon1 == mon2) {
            if (date1 > date2) {
              this.$message.error('开始日期应早于结束日期')
              return false
            }
          }
        }
        if (officeList[4] && officeList[4].id == 'birthday' && officeList[4].startDate) {
          if (officeList[4].startDate == '' || officeList[4].endDate == '') {
            this.$message.error('开始日期或结束日期不可为空')
            return false
          }
          let mon1 = officeList[4].startDate.split('-')[0]
          let mon2 = officeList[4].endDate.split('-')[0]
          let date1 = officeList[4].startDate.split('-')[1]
          let date2 = officeList[4].endDate.split('-')[1]
          if (mon1 == mon2) {
            if (date1 > date2) {
              this.$message.error('开始日期应早于结束日期')
              return false
            }
          }
        }
        if (officeList[1] && officeList[1].id == 'sex' && !officeList[1].sexType) {
          this.$message.error('请选择性别')

          return false
        }
        if (officeList[1] && officeList[1].id == 'identity' && !officeList[1].identityType) {
          this.$message.error('请选择会员身份')

          return false
        }
        if (officeList[1] && officeList[1].id == 'birthday' && !officeList[1].startDate) {
          this.$message.error('请选择生日')

          return false
        }
        if (officeList[1] && officeList[1].id == 'post' && !officeList[1].postType) {
          this.$message.error('请选择岗位')

          return false
        }
        if (officeList[2] && officeList[2].id == 'identity' && !officeList[2].identityType) {
          this.$message.error('请选择会员身份')

          return false
        }
        if (officeList[2] && officeList[2].id == 'birthday' && !officeList[2].startDate) {
          this.$message.error('请选择生日')
          return false
        }
        if (officeList[2] && officeList[2].id == 'post' && !officeList[2].postType) {
          this.$message.error('请选择岗位')

          return false
        }
        if (officeList[3] && officeList[3].id == 'birthday' && !officeList[3].startDate) {
          this.$message.error('请选择生日')

          return false
        }
        if (officeList[3] && officeList[3].id == 'post' && !officeList[3].postType) {
          this.$message.error('请选择岗位')

          return false
        }
      }
      if (storageIdentification.flag) {
        // 修改逻辑保存  发布范围ID
        data.authId = storageIdentification.authId
        // 修改逻辑
        this.btnLoading = true
        this.$api.saveAuthRange(data).then((res) => {
          this.popUpPrompt(res)
        })
      } else {
        // 保存逻辑
        this.btnLoading = true
        this.$api.saveAuthRange(data).then((res) => {
          this.popUpPrompt(res)
        })
      }
    },
    /**
     * 对题目操作前的提示信息，此操作仅在问卷处于非设计阶段用于展示提示消息
     * @callback：点击确定之后的回调函数
     * @funName： 回调函数需要的参数
     */
    doConfirmBeforeEdit(callback, params) {
      console.log(params,'params+++++++++++++++++++++');
      
      // debugger
      // 获取问卷状态，状态分为design: "设计",publish: "收集",recovery: "完成",
      const pvqStatus = JSON.parse(localStorage.getItem('localData'))
      if (pvqStatus.status === 'publish') {
        // 获取问卷状态为publish时的状态，状态分为 0: "暂停",1: "收集"
        if (pvqStatus.statusRun == 1) {
          this.doConfirmMessage('问卷处于收集状态，无法编辑！')
        } else {
          this.doConfirmMessage('问卷已暂停，无法编辑！')
        }
      } else if (pvqStatus.status === 'recovery') {
        this.doConfirmMessage('问卷已结束，无法编辑！')
      } else {
        this[callback](params)
      }
    },
    /**
     * 展示提示消息确认框
     * @messageInfo：提示信息
     */
    doConfirmMessage(messageInfo) {
      this.$confirm(messageInfo, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
    },
    // 清除除指定人员类型外的其他
    cleanOther() {
      this.cleanUnion()
      this.cleanType()
    },
    // 岗位list是否展示
    postShow() {
      return !this.checkList.includes('post')
    },
    sexShow() {
      return !this.checkList.includes('sex')
    },
    identifyShow() {
      return !this.checkList.includes('identity')
    },
    birthdayShow() {
      this.showEdit = false
      return !this.checkList.includes('birthday')
    },
    // 获取岗位信息
    getDictValue(data) {
      this.postList = data
      // this.centralControl.axios.centralControl(
      //   "getDictValue",
      //   res => {
      //     const { code, data, message } = res;
      //     if (code == 200) {
      //       this.postList = data;
      //     } else {
      //       this.$message.error(message);
      //     }
      //   },
      //   { dictType: "post" }
      // );
    },
    // 选择人员弹框
    choosePeople() {
      if (this.type == 'all') {
        if (this.checkedPeoples.length == 100 || this.checkedPeoples.length > 100) {
          this.$message.warning('页面显示指定人员不得超过100人，如有需要，请重置之后重新进行选择!')
          return
        }
        this.selectType = 'allParts' // 标识，全员指定不发放人员时，identity为3的无法选择
        this.peopleVis = true
        this.$nextTick(() => {
          if (this.tableValLists) {
            this.arrData = this.tableValLists // 将选中的指定人员传递给子组件
            this.tableDataList = this.tableDatas3 // 将table表格数据传递给子组件
          }
          this.cancleDatasLists = this.cancleDatas
          this.tableCancleDatas3Lists = this.tableCancleDatas3
        })
      } else {
        if (this.checkedPeople.length == 100 || this.checkedPeople.length > 100) {
          this.$message.warning('页面显示指定人员不得超过100人，如有需要，请重置之后重新进行选择!')
          return
        }
        this.selectType = '' // 标识，全员指定不发放人员时，identity为3的无法选择
        this.peopleVis = true
        this.$nextTick(() => {
          if (this.tableValLists) {
            this.arrData = this.tableValLists // 将选中的指定人员传递给子组件
            this.tableDataList = this.tableDatas3 // 将table表格数据传递给子组件
          }
          this.cancleDatasLists = this.cancleDatas
          this.tableCancleDatas3Lists = this.tableCancleDatas3
        })
      }
    },
    // 人员弹框确定
    chosedPeopleFn(peopleList) {
      // 判断多次选择的时候，最后一次选择之后，是否会导致页面显示人员大于100个人
      let lengths = 0
      if (peopleList) {
        peopleList.forEach((item) => {
          lengths = item.length
        })
      }
      if (this.type == 'all') {
        if (this.checkedPeoples.length == 100 || this.checkedPeoples.length > 100 || lengths + this.checkedPeoples.length > 100) {
          this.$message.warning('页面显示指定人员不得超过100人，如有需要，请重置之后重新进行选择!')
          return
        }
        // end
        this.cleanOther()
        let arr = []
        const { checkedPeoples } = this
        peopleList &&
          peopleList.forEach((item) => {
            item.forEach((val) => {
              var flag = true
              checkedPeoples &&
                checkedPeoples.forEach((itemDouble) => {
                  if (val.id == itemDouble.id) {
                    flag = false
                    return
                  }
                })
              if (flag) {
                arr.push({
                  id: val.id,
                  label: val.staffName
                })
              }
            })
          })
        this.checkedPeoples = [...arr, ...this.checkedPeoples]
        this.type = 'all'
      } else {
        if (this.checkedPeople.length == 100 || this.checkedPeople.length > 100 || lengths + this.checkedPeople.length > 100) {
          this.$message.warning('页面显示指定人员不得超过100人，如有需要，请重置之后重新进行选择!')
          return
        }
        // end
        this.cleanOther()
        let arr = []
        const { checkedPeople } = this
        peopleList &&
          peopleList.forEach((item) => {
            item.forEach((val) => {
              var flag = true
              checkedPeople &&
                checkedPeople.forEach((itemDouble) => {
                  if (val.id == itemDouble.id) {
                    flag = false
                    return
                  }
                })
              if (flag) {
                arr.push({
                  id: val.id,
                  label: val.staffName
                })
              }
            })
          })
        this.checkedPeople = [...arr, ...this.checkedPeople]
        this.type = 'people'
      }
    },
    // 人员弹框关闭
    closePeopleDialog() {
      this.peopleVis = false
    },
    // 删除人员
    delPeople(people) {
      if (this.type == 'all') {
        this.checkedPeoples &&
          this.checkedPeoples.forEach((item, index) => {
            if (item.id == people.id) {
              this.checkedPeoples.splice(index, 1)
            }
          })
      } else {
        this.checkedPeople &&
          this.checkedPeople.forEach((item, index) => {
            if (item.id == people.id) {
              this.checkedPeople.splice(index, 1)
            }
          })
      }
    }
  }
}
</script>
<style scoped lang="scss">
.hrStyle {
  height: 1px;
  border: none;
  border-top: 1px solid #dcdfe6;
  margin: 5px;
}

.personnelType {
  font-size: 14px;
  font-weight: 600;
}

.questionBox {
  font-size: 14px;
  margin: 10px;
}

.el-button {
  margin: 0 0 10px 10px;
}

.allEmployees {
  font-size: 14px;
  font-weight: 600;
}

.organizationalStructure {
  font-weight: 600;
  font-size: 14px;
}

.checkboxStyle {
  /* margin-left: 55px; */
  margin-left: 33px;
  padding: 12px 0 8px;
}

.treeStyle,
.treeStyles {
  margin-left: 30px;
}

.my-flex {
  display: flex;

  /* align-items: center; */
  flex-direction: column;
}

.my-flex > div {
  display: flex;
  align-items: center;
  padding-bottom: 12px;
}

.treeStyles {
  width: 220px;
}

.treeStyles .welfareTree ::v-deep .el-input__inner {
  background: #f5f7fa !important;
  border-color: #e4e7ed !important;
  color: #c0c4cc !important;
}
</style>
