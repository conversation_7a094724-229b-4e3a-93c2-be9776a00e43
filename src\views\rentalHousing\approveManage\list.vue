<template>
  <PageContainer>
    <div slot="content" class="role-content" style="height: 100%">
      <div class="approve-manage-list-view">
        <div class="header-box">
          <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane :label="`我的待办（${stayHandleNum}）`" name="1"></el-tab-pane>
            <el-tab-pane label="我的已办" name="2"></el-tab-pane>
          </el-tabs>
        </div>
        <div class="table-content">
          <div class="table-search">
            <div class="table-search-left">
              <el-input v-model="searchInfo.name" style="width: 200px; margin-right: 8px" placeholder="全部流程名称" maxlength="60"></el-input>
              <el-select v-model="searchInfo.applicantId" placeholder="申请人" style="width: 200px; margin-right: 8px">
                <el-option v-for="item in applicantOptions" :key="item.id" :label="item.staffName" :value="item.id"> </el-option>
              </el-select>
              <el-select v-model="searchInfo.statusId" placeholder="全部状态" style="width: 200px; margin-right: 8px">
                <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
              <el-date-picker
                v-model="searchInfo.date"
                type="datetimerange"
                start-placeholder="申请开始时间"
                range-separator="至"
                style="width: 380px; margin-right: 8px"
                end-placeholder="申请开始时间"
                :default-time="['00:00:00', '23:59:59']"
              >
              </el-date-picker>
            </div>
            <div class="table-search-right">
              <el-button type="primary" style="background: #d7e0f8; border: 1px solid #d7e0f8; color: #3562db; margin-left: 20px" @click="resetCondition">重置</el-button>
              <el-button type="primary" style="font-size: 14px" @click="searchByCondition">查询</el-button>
            </div>
          </div>
          <div class="table-box">
            <div class="table-list">
              <el-table
                ref="materialTable"
                v-loading="tableLoading"
                :data="tableData"
                height="calc(100% - 10px)"
                border
                :header-cell-style="{ background: '#F6F5FA' }"
                style="width: 100%"
                :cell-style="{ padding: '8px 0 8px 0' }"
                stripe
                highlight-current-row
                :empty-text="emptyText"
                row-key="id"
              >
                <el-table-column label="序号" type="index" width="70">
                  <template slot-scope="scope">
                    <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
                  </template>
                </el-table-column>
                <el-table-column show-overflow-tooltip prop="contractNum" label="流程ID"></el-table-column>
                <el-table-column show-overflow-tooltip prop="secondPartyName" label="流程名称"></el-table-column>
                <el-table-column show-overflow-tooltip prop="houseName" label="申请人"></el-table-column>
                <el-table-column prop="spaceName" show-overflow-tooltip label="当前节点"></el-table-column>
                <el-table-column prop="rentingUnitPrice" show-overflow-tooltip label="申请时间"></el-table-column>
                <el-table-column prop="rentingMoney" label="状态" align="center"></el-table-column>
                <el-table-column label="操作" width="120">
                  <template slot-scope="scope">
                    <div style="display: flex; gap: 10px">
                      <el-link type="primary" @click="openDetails(scope.row)"> 查看/处理 </el-link>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="table-pagination" style="padding-top: 10px; text-align: right">
              <el-pagination
                layout="total, sizes, prev, pager, next, jumper"
                :current-page="paginationData.currentPage"
                :page-sizes="[15, 30, 50, 100]"
                :page-size="paginationData.pageSize"
                :total="paginationData.total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              ></el-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PageContainer>
</template>
<script>
export default {
  data() {
    return {
        tableLoading: false,
      emptyText: '暂无数据',
      activeName: '1',
      stayHandleNum: '0',
      searchInfo: {
        name: '',
        applicantId: '',
        statusId: '',
        date: []
      },
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      statusOptions: [],
      applicantOptions: [],
      tableData: []
    }
  },
  mounted() {},
  methods: {
    handleClick(){},
    /** 重置 */
    resetCondition() {},
    /** 查询 */
    searchByCondition() {
      this.paginationData = {
        currentPage: 1,
        pageSize: 15,
        total: 0
      }
      this.searchInfo = {
        name: '',
        applicantId: '',
        statusId: '',
        date: []
      }
    },
    /** 获取流程状态 */
    getStatus() {},
    /** 获取申请人 */
    getApplicant() {},
    /** 获取列表 */
    getTableData() {},
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.getTableData()
    },
    // 每页的条数
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.paginationData.currentPage = 1
      this.getTableData()
    }
  }
}
</script>
<style lang="scss" scoped>
.approve-manage-list-view {
  padding: 10px 15px 15px;
  box-sizing: border-box;
  height: 100%;
  background: #fff;
  .table-content {
    height: calc(100% - 50px);
    margin-top: 10px;
    .table-search {
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
      .table-search-left,
      .table-search-right {
        flex-shrink: 0;
      }
      .table-search-left {
        display: flex;
        align-items: center;
      }
    }
    .table-box {
      height: calc(100% - 42px);
      .table-list {
        height: calc(100% - 42px);
      }
    }
  }
}
::v-deep .el-icon-time {
  position: relative;
}
::v-deep .el-icon-time::before {
  position: absolute;
  top: -3px;
  left: 4px;
}
</style>