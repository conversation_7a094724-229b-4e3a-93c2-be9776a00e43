<template>
  <el-dialog v-if="dialogShow" :title="title" width="30%" :visible.sync="dialogShow" custom-class="model-dialog">
    <div style="width: 100%; height: 100%">
      <div slot="content" class="card-content">
        <el-row class="details_content">
          <el-col v-for="(item, index) in equipmentData.parameterList" :key="index" :span="24" class="details_content_item">
            <p>{{ item.parameterName }}</p>
            <span>{{ item.parameterValue }}{{ item.parameterUnit }}</span>
          </el-col>
        </el-row>
      </div>
    </div>
  </el-dialog>
</template>
<script>
export default {
  name: 'normalDialog',
  props: {},
  data() {
    return {
      dialogShow: false,
      equipmentData: {},
      title: ''
    }
  },
  mounted() {},
  methods: {
    getEchartData(data) {
      this.dialogShow = true
      this.title = data.surveyEntityName
      this.equipmentData = data
    }
  }
}
</script>
<style lang="scss" scoped>
.details_content {
  padding: 16px;
  background: #faf9fc;
  .details_content_item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;
    span::before {
      content: ':';
      margin-right: 5px;
    }
    p {
      flex-shrink: 0;
      width: 70px;
      font-size: 14px;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: 400;
      color: #666666;
      margin: 0;
      text-align: right;
    }
    span {
      font-size: 14px;
      font-family: PingFang SC-Medium, PingFang SC;
      font-weight: 500;
      color: #333333;
    }
  }
}
.card-content {
  padding: 0 8px;
}
::v-deep .model-dialog .el-dialog__body {
  background: #ffffff;
}
</style>
