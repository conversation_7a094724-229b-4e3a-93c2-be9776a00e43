<template>
  <PageContainer>
    <div slot="header" class="control-btn-header">
      <div class="search-from">
        <el-select v-model="searchFrom.roleId" placeholder="角色" clearable>
          <el-option v-for="item in roleList" :key="item.id" :label="item.roleName" :value="item.id"> </el-option>
        </el-select>
        <el-input v-model="searchFrom.userName" placeholder="人员姓名" clearable style="width: 200px;"></el-input>
        <el-input v-model="searchFrom.phone" placeholder="手机号码" clearable style="width: 200px;"></el-input>
        <div style="display: inline-block;">
          <el-button v-auth="'userManagement:reset'" type="primary" plain @click="resetForm">重置</el-button>
          <el-button v-auth="'userManagement:search'" type="primary" @click="searchForm">查询</el-button>
        </div>
      </div>
      <div class="batch-control">
        <el-button v-auth="'userManagement:add'" type="primary" icon="el-icon-plus" @click="handleRoleEvent('add')">新增</el-button>
        <!-- <el-button v-auth="'userManagement:import'" type="primary" icon="el-icon-upload2">导入</el-button> -->
        <el-button v-auth="'userManagement:export'" type="primary" icon="el-icon-download" :disabled="!multipleSelection.length" @click="exportUser">导出</el-button>
        <el-button v-for="(item, index) in batchControlList" :key="index"  v-auth="'userManagement:' + item.type" type="primary" :disabled="!multipleSelection.length" @click="handleRoleEvent(item.type)">
          {{item.label}}
        </el-button>
      </div>
    </div>
    <div slot="content" class="table-content">
      <TablePage
        ref="table"
        v-loading="tableLoading"
        :showPage="true"
        :tableColumn="tableColumn"
        :data="tableData"
        height="calc(100% - 40px)"
        :pageData="pageData"
        :pageProps="pageProps"
        @pagination="paginationChange"
        @row-dblclick="handleRoleEvent('detail', $event)"
        @selection-change="handleSelectionChange"
      />
      <selectPersDialog v-if="isSelectPers" selectMode="2" :visible="isSelectPers" @updateVisible="() => {isSelectPers = false}" @advancedSearchFn="selectPersChange" />
      <roleAssiDialog v-if="isRoleAssiDialog" :visible.sync="isRoleAssiDialog" :selectUserList="multipleSelection" />
      <!-- <setAuthDialog :visible.sync="isSetAuthDialog" /> -->
    </div>
  </PageContainer>
</template>
<script lang="jsx">
export default {
  name: 'userManagement',
  components: {
    roleAssiDialog: () => import('./components/roleAssiDialog.vue'),
    selectPersDialog: () => import('@/views/alarmCenter/linkageConfiguration/components/dialogConfig.vue')
  },
  beforeRouteEnter(to, from, next) {
    let names = []
    to.matched.map((v, i) => {
      if (i > 0) {
        v.components.default.name && names.push(v.components.default.name)
      }
    })
    // 进入页面时，先将当前页面的 name 信息存入 keep-alive 全局状态
    setTimeout(() => {
      next((vm) => {
        vm.$store.commit('keepAlive/add', names)
      })
    }, 0)
  },
  async beforeRouteLeave(to, from, next) {
    // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
    if (!['userForm'].includes(to.name)) {
      // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      isSelectPers: false, // 选择人员
      isRoleAssiDialog: false, // 角色分配
      // isSetAuthDialog: false, // 设置权限
      searchFrom: {
        roleId: '', // 角色
        userName: '', // 人员姓名
        phone: '' // 手机号
      },
      batchControlList: [
        {
          type: 'accountsEnable',
          label: '账号启用'
        },
        {
          type: 'accountsDisable',
          label: '账号停用'
        },
        {
          type: 'roleAssignment',
          label: '角色分配'
        },
        {
          type: 'resetPassword',
          label: '重置密码'
        }
      ], // 批量处理
      tableLoading: false,
      tableColumn: [
        {
          type: 'selection',
          align: 'center',
          selectable: (row) => {
            return row.id !== 1
          }
        },
        {
          prop: 'serialNumber',
          label: '序号',
          formatter: (scope) => {
            // 当前分页重置
            // return scope.$index + 1
            // 跨分页序号
            return (this.pageData.current - 1) * this.pageData.size + scope.$index + 1
          }
        },
        {
          prop: 'userName',
          label: '登录账号'
        },
        {
          prop: 'roleName',
          label: '角色'
        },
        {
          prop: 'staffName',
          label: '人员姓名'
        },
        {
          prop: 'phone',
          label: '手机号码'
        },
        {
          prop: 'deptName',
          label: '部门'
        },
        {
          prop: 'loginCount',
          label: '登录次数'
        },
        {
          prop: 'loginDate',
          label: '最后登录时间'
        },
        {
          width: 140,
          prop: 'operation',
          label: '操作',
          render: (h, row) => {
            if (row.row.id == 1) {
              return
            }
            return (
              <div class="operationBtn">
                <span class="operationBtn-span" style="color: #fa403c" onClick={() => this.handleRoleEvent('del', row.row)}>删除</span>
                <span class="operationBtn-span" style="color: #121F3E" onClick={() => this.handleRoleEvent('edit', row.row)}>编辑</span>
                <span class="operationBtn-span" style="color: #3562db" onClick={() => this.handleRoleEvent('stop', row.row)}>
                  {row.row.state == 0 ? '停用' : '启用'}
                </span>
              </div>
            )
          }
        }
      ],
      tableData: [],
      pageData: {
        current: 1,
        size: 15,
        total: 0
      },
      pageProps: {
        page: 'current',
        pageSize: 'size',
        total: 'total'
      },
      multipleSelection: [],
      roleList: [] // 角色列表
    }
  },
  watch: {
    isSelectPers(val) {
      if (!val) {
        this.searchForm()
      }
    },
    isRoleAssiDialog(val) {
      if (!val) {
        this.searchForm()
      }
    }
  },
  activated() {
    this.getUserInfoList()
  },
  mounted() {
    this.getUserInfoList()
    this.getRoleList()
  },
  methods: {
    // 导出用户
    exportUser() {
      let param = {
        userIds: this.multipleSelection.map(item => item.id).join(',')
      }
      let menuList = this.$store.state.menu.routes
      let firstTitle = ''
      let routeList = []
      this.$router.currentRoute.matched.filter(item => item.name).forEach(el => {
        routeList.push(el.meta.title)
      })
      if (menuList.length) {
        firstTitle = menuList[this.$store.state.menu.headerActived].meta.title
        routeList.unshift(firstTitle)
      }
      this.$api.ExportUser(param, { 'operation-type': 4, 'operation-content': encodeURIComponent(routeList.join(',')) }).then(res => {
        if (res.status == 200) {
          this.$tools.downloadFile(res)
        }
      })
      // window.open(__PATH.VUE_SYS_API + 'userInfo/export' + '?' + qs.stringify(this.searchFrom))
    },
    // 获取角色列表
    getRoleList() {
      let param = {
        pageSize: 99,
        page: 1
      }
      this.$api.GetRoleList(param).then(res => {
        if (res.code == '200') {
          this.roleList = res.data.records
        }
      })
    },
    // 选择人员事件
    selectPersChange(data) {
      this.$api.UserIsExist({ phone: data[0].mobile }).then(res => {
        if (res.code == 200) {
          this.$router.push({
            path: '/auth/userManagement/userForm',
            query: {
              type: 'add',
              id: data[0].id
            }
          })
        } else {
          this.$message({ message: res.msg, type: 'error'})
        }
      })
    },
    // 查询
    searchForm() {
      this.pageData.current = 1
      this.getUserInfoList()
    },
    // 重置查询
    resetForm() {
      Object.assign(this.$data.searchFrom, this.$options.data().searchFrom)
      this.searchForm()
    },
    handleRoleEvent(type, row) {
      if (type == 'add') { // 新增用户
        this.isSelectPers = true
      } else if (type === 'edit' || type === 'detail') { // 修改/查看用户
        this.$router.push({
          path: '/auth/userManagement/userForm',
          query: {
            type,
            id: row?.id ?? ''
          }
        })
      } else if (type == 'del') {  // 账号删除
        this.$confirm('删除后将无法恢复，是否确定删除？', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.operationUser({ delFlag: 1, id: row.id }, '用户删除成功', {staffName: row.staffName})
        })
      } else if (type == 'stop') { // 账号启用/停用
        this.operationUser({ id: row.id, state: row.state == 0 ? 1 : 0}, row.state == 0 ? '用户已停用' : '用户已启用', {})
      } else if (type == 'accountsEnable') { // 批量账号启用
        this.operationUser({ state: 0, id: this.multipleSelection.map(item => item.id).join(',')}, '用户已启用', {})
      } else if (type == 'accountsDisable') { // 批量账号禁用
        this.operationUser({ state: 1, id: this.multipleSelection.map(item => item.id).join(',')}, '用户已停用', {})
      } else if (type == 'roleAssignment') { // 批量分配角色
        this.isRoleAssiDialog = true
      } else if (type == 'resetPassword') { // 批量重置密码
        this.$confirm('是否重置选中用户密码？', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$api.ResetUserPassWord({ userIds: this.multipleSelection.map(item => item.id).join(',')}).then(res => {
            if (res.code == 200) {
              this.searchForm()
              this.$message({ message: '用户密码已重置', type: 'success'})
            } else {
              this.$message({ message: res.msg, type: 'error'})
            }
          })
        })
      }
    },
    // 判断当前页是否是最后一页
    isLastPage (deleteNum) {
      let deleteAfterPage = Math.ceil((this.pageData.total - deleteNum) / this.pageData.size)
      let currentPage = this.pageData.current > deleteAfterPage ? deleteAfterPage : this.pageData.current
      this.pageData.current = currentPage < 1 ? 1 : currentPage
    },
    // 删除/启用/停用用户账号
    operationUser(param, msg, otherParams) {
      this.$api.OperationUser(param, { 'operation-type': 3, 'operation-id': param.id, 'operation-name': otherParams.staffName }).then(res => {
        if (res.code == 200) {
          this.isLastPage(1)
          this.searchForm()
          this.$message({ message: msg, type: 'success'})
        } else {
          this.$message({ message: res.msg, type: 'error'})
        }
      })
    },
    // 获取人员列表
    getUserInfoList() {
      let param = {
        ...this.searchFrom,
        pageSize: this.pageData.size,
        page: this.pageData.current
      }
      this.tableLoading = true
      this.$refs.table.clearSelection()
      this.$api.GetUserInfoList(param).then(res => {
        if (res.code == 200) {
          this.tableData = res.data.records
          this.pageData.total = res.data.total
        }
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    // 多选
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    paginationChange(pagination) {
      // this.pageData.size = pagination.pageSize
      // this.pageData.current = pagination.page
      Object.assign(this.pageData, pagination)
      this.getUserInfoList()
    }
  }
}
</script>
<style lang="scss" scoped>
.control-btn-header {
  padding: 10px !important;
  /* display: flex;
  justify-content: flex-end; */
  .search-from {
    /* padding-bottom: 12px; */
    & > div {
      /* margin-top: 12px; */
      margin-right: 10px;
    }
  }
  .batch-control {
    & > button {
      margin-top: 12px;
      margin-right: 10px;
      margin-left: 0;
    }
  }
}
.table-content {
  margin-top: 15px;
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 15px);
  padding: 10px;
}
</style>
<style lang="scss">
.operationBtn-span {
  margin-right: 10px;
}
</style>
