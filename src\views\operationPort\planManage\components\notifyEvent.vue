<template>
  <div class="notifyEvent">
    <div class="notifyEvent-head">
      <p v-show="!notify_isEditEventName" class="notify-title" @click="editTitleFun('notify_isEditEventName')">
        {{ notifyData.eventName || '请输入' }}
        <i class="el-icon-edit" style="color: #CCCED3;"></i>
      </p>
      <el-input v-show="notify_isEditEventName" ref="notify_isEditEventName" v-model="notifyData.eventName" placeholder="请输入通知事件名称" :maxlength="10" style="width: 200px;" @blur="() => notify_isEditEventName = false" />
      <el-checkbox v-model="notifyData.showFlag">显示拨打按钮</el-checkbox>
    </div>
    <div v-for="(item, index) in noticeEventList" :key="index" class="item-form">
      <el-form>
        <el-row :gutter="24" style="margin: 0">
          <el-col :md="13">
            <el-form-item label-width="130px">
              <template slot="label">
                <el-checkbox v-model="item.checkFlag">通知部门或人员</el-checkbox>
              </template>
              <div style="display: flex;">
                <el-select v-if="item.personName !== '消防站'" v-model="item.noticeType" class="noticeType" @change="(val) => noticeTypeChange(val, index)">
                  <el-option label="人员" :value="0"></el-option>
                  <el-option label="部门" :value="1"></el-option>
                </el-select>
                <el-input v-if="item.noticeType == 0" v-model="item.personName" type="text" readonly placeholder="请选择" suffix-icon="el-icon-arrow-down" style="flex: 1;" :disabled="item.personName == '消防站'" @focus="control('selectPers', index)"></el-input>
                <el-select v-else v-model="item.departCode" placeholder="请选择" clearable multiple collapse-tags filterable style="flex: 1;" @change="(val) => selectDept(val, index)">
                  <el-option v-for="item in deptList" :key="item.id" :label="item.deptName" :value="item.id"></el-option>
                </el-select>
              </div>
            </el-form-item>
          </el-col>
          <el-col :md="9">
            <el-form-item label="通知方式" label-width="100px">
              <el-select v-model="item.noticeWay" placeholder="请选择" style="width: 100%;" :disabled="item.personName == '消防站'">
                <el-option v-if="item.personName == '消防站' || (type == 'action' && item.noticeType != 1)" label="手动电话通知" :value="0"></el-option>
                <el-option label="自动电话通知" :value="1"></el-option>
                <el-option label="短信推送" :value="2"></el-option>
                <el-option label="APP推送" :value="3"></el-option>
                <el-option label="公众号推送" :value="4"></el-option>
                <!-- <el-option label="客户端" :value="5"></el-option> -->
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24" style="margin: 0">
          <el-col :md="13">
            <el-form-item :label="item.noticeType == 0 ? '联系方式' : ''" :label-width="item.noticeType == 0 ? '130px' : '0px'">
              <el-input v-if="item.noticeType == 0" v-model="item.noticePhone" placeholder="请输入" type="text" maxlength="11" :disabled="item.personName == '消防站'"></el-input>
              <el-tooltip v-else placement="bottom" :open-delay="500" popper-class="notice-event-tooltip">
                <div slot="content" class="tooltip-content">
                  <div class="content-head">
                    <div class="deptName">
                      <p class="deptName-name">通知部门或人员：{{ item.deptName }}</p>
                      <p class="deptName-num">{{ item.noticePersonList.length}}人</p>
                    </div>
                    <div class="deptName">
                      <p class="deptName-name">联系方式：</p>
                      <p class="deptName-num">{{ item.noticePersonList.length}}个</p>
                    </div>
                  </div>
                  <div class="content-list">
                    <div v-for="dept in item.selectDeptList" :key="dept.id" class="list-item">
                      <p class="item-name">{{ dept.deptName }}</p>
                      <div v-for="user in dept.userList" :key="user.personId" class="item-user">
                        <div :style="{color: (user.noticePhone && user.noticePhone.length == 11) && user.haveAppAccount ? '' : '#FA403C'}">{{ user.personName }}</div>
                        <div style="display: flex;">
                          <p :style="{color: (user.noticePhone && user.noticePhone.length == 11) && user.haveAppAccount ? '' : '#FA403C'}">{{ user.noticePhone }}</p>
                          <p style="color: #FA403C; margin-left: 20px;">{{ user.noticePhone && user.noticePhone.length == 11 ? (user.haveAppAccount ? '' : '未开通APP账号') : '缺失号码' }}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="deptName">
                  <p class="deptName-name">{{ item.deptName }}</p>
                  <p v-show="item.noticePersonList.length" class="deptName-num">{{ item.noticePersonList.length}}人</p>
                  <i v-show="item.noticePersonList.map(v => v.haveAppAccount).includes(false) || item.noticePersonList.map(v => v.noticePhone ? v.noticePhone.length == 11 : false).includes(false)" class="el-icon-warning"></i>
                </div>
              </el-tooltip>
            </el-form-item>
          </el-col>
          <el-col :md="9">
            <el-form-item :label="item.noticeWay == 0 ? '手动拨打' : item.noticeWay == 1 ? '自动拨打' : ''" label-width="100px">
              <el-button v-if="notifyData.showFlag && item.noticeWay == 0" type="primary" plain style="padding: 6px 16px;">拨打</el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="[0, 1].includes(item.noticeWay)" :gutter="24" style="margin: 0">
          <el-col :md="22">
            <el-form-item label="电话沟通内容" label-width="130px">
              <el-input v-model="item.noticeContent" type="textarea" resize="none" rows="3" placeholder="输入电话话术内容" :disabled="item.noticeWay == 1" show-word-limit maxlength="50"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <p v-if="item.personName !== '消防站'" class="item-delete" @click="control('delete', index)">
        <i class="el-icon-delete"></i>
        删除
      </p>
    </div>
    <el-button type="primary" icon="el-icon-plus" plain style="margin-top: 8px;" @click="control('add')">添加通知内容</el-button>
    <selectPersDialog v-if="isSelectPers" selectMode="2" :visible="isSelectPers" :modal="false" @updateVisible="() => {isSelectPers = false}" @advancedSearchFn="selectPersChange" />
  </div>
</template>
<script>
import { transData } from '@/util'
export default {
  name: 'notifyEvent',
  components: {
    selectPersDialog: () => import('@/views/alarmCenter/linkageConfiguration/components/dialogConfig.vue')
  },
  props: {
    type: {
      type: String,
      default: 'notify' // notify(通知) action(动作)
    }
  },
  data() {
    return {
      notify_isEditEventName: false, // 修改title
      isSelectPers: false, // 选择人员
      notifyData: {
        stepName: '通知事件',
        eventName: '通知事件',
        showFlag: false // 显示拨打按钮 0未勾选 1已勾选
      },
      noticeEventList: [
        {
          checkFlag: true, // 是否通知 0未勾选 1已勾选
          noticeContent: '', // 电话沟通内容
          noticePhone: '119', // 联系方式
          noticeWay: 0, // 通知方式
          noticeType: 0, // 通知类型
          personName: '消防站', // 人员名称
          departCode: [], // 部门code （逗号分割提交）
          deptName: '', // 部门名称（不提交）
          noticePersonList: [], // 人员列表
          selectDeptList: [] // 部门 人员归类（不提交）
        }
      ], // 通知列表
      deptList: [], // 部门列表
      selectItemIndex: 0 // 选中项下标
    }
  },
  computed: {
  },
  created() {
    this.getUseDeptList()
  },
  methods: {
    noticeTypeChange(val, index) {
      val == 1 ? this.noticeEventList[index].noticeWay = 3 : this.noticeEventList[index].noticeWay = 1
      this.noticeEventList[index].noticePersonList = []
      this.noticeEventList[index].selectDeptList = []
      this.noticeEventList[index].noticePhone = ''
      this.noticeEventList[index].personName = ''
      this.noticeEventList[index].departCode = []
      this.noticeEventList[index].deptName = ''
      this.noticeEventList[index].selectDeptList = []
    },
    control(type, index = 0) {
      this.selectItemIndex = index
      if (type == 'selectPers') { // 选择人员
        this.isSelectPers = true
      } else if (type == 'add') {
        this.noticeEventList.push({
          checkFlag: true,
          noticeContent: '语音电话示例: 急诊急救综合楼5层空调机房 空调机组01 设备故障报警，报警等级：重要 ，请及时处理',
          noticePhone: '',
          noticeWay: 1,
          noticeType: 0,
          personName: '',
          departCode: [],
          deptName: '',
          noticePersonList: [],
          selectDeptList: []
        })
      } else if (type == 'delete') {
        this.noticeEventList.splice(index, 1)
      }
    },
    // 选择人员事件
    selectPersChange(data) {
      this.noticeEventList[this.selectItemIndex].noticePhone = data[0].mobile
      this.noticeEventList[this.selectItemIndex].personName = data[0].staffName
      this.noticeEventList[this.selectItemIndex].noticePersonList = [
        {
          departCode: data[0].officeId, // 部门code
          departName: data[0].officeName, // 部门名称
          id: data[0].id,
          noticePhone: data[0].mobile, // 联系方式
          personId: data[0].id, // 人员编码
          personName: data[0].staffName // 人员名称
        }
      ]
    },
    // 选择部门事件
    selectDept(val, index) {
      if (!val.length) {
        this.noticeEventList[index].noticePersonList = []
        this.noticeEventList[index].deptName = ''
        this.noticeEventList[index].selectDeptList = []
        return
      }
      let selectDeptList = []
      this.deptList.forEach(item => {
        if (val.includes(item.id)) {
          selectDeptList.push(item)
        }
      })
      this.noticeEventList[index].deptName = selectDeptList.map(v => v.deptName).join('、')
      // 根据科室id获取人员列表
      this.$api.GetNoticePersonInfoList({departCode: val.join(',')}).then(res => {
        console.log(res.data)
        if (res.code == 200) {
          // 人员归类到部门下
          selectDeptList.forEach((item, index) => {
            item.userList = []
            res.data.map(v => v.departCode.split(',')).forEach((v, i) => {
              if (v.includes(item.id)) {
                item.userList.push(res.data[i])
              }
            })
          })
          this.noticeEventList[index].noticePersonList = res.data
          this.noticeEventList[index].selectDeptList = selectDeptList
        }
      })
    },
    // 编辑标题
    editTitleFun(key) {
      this[key] = true
      this.$nextTick(() => {
        this.$refs[key].focus()
      })
    },
    // 获取科室
    getUseDeptList() {
      this.$api.getSelectedDept().then((res) => {
        if (res.code == '200') {
          this.deptList = transData(res.data, 'id', 'parentId', 'children')
          this.$nextTick(() => {
            this.noticeEventList.forEach((item, index) => {
              if (item.noticeType == 1 && item.departCode.length) {
                this.selectDept(item.departCode, index)
              }
            })
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.notifyEvent {
  padding: 16px;
  background: #FAF9FC;
  border-radius: 4px;
  margin-bottom: 16px;
  p{
    margin: 0px;
  }
  .notifyEvent-head {
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .notify-title {
      cursor: pointer;
      font-weight: bold;
      font-size: 16px;
      color: #333333;
      line-height: 32px;
      width: 200px;
    }
  }
  .item-form {
    border-bottom: 1px solid #E4E7ED;
    position: relative;
    margin-bottom: 16px;
    .item-delete {
      color: #FF6461;
      position: absolute;
      top: 50%;
      right: 16px;
      transform: translateY(-50%);
      cursor: pointer;
    }
  }
  ::v-deep(.noticeType) {
    flex: 0.35;
    .el-input__inner {
      color: #3562DB;
    }
  }
}
.deptName {
  display: flex;
  align-items: center;
  cursor: pointer;
  .deptName-name {
    flex: 1;
    overflow:hidden;
    text-overflow:ellipsis;
    white-space:nowrap;
  }
  .deptName-num {
    height: 16px;
    line-height: 16px;
    padding: 0px 6px;
    background: #E4E7ED;
    border-radius: 99px;
    font-weight: 400;
    font-size: 12px;
    color: #666666;
    margin: 0px 8px;
  }
  i {
    color: #FA403C;
  }
}
.notice-event-tooltip {
  width: 600px;
  p {
    margin: 0;
  }
  .tooltip-content {
  }
  .content-head {
    display: flex;
    div {
      width: 50%;
    }
  }
  .content-list {
    max-height: 230px;
    overflow: auto;
    padding: 0px 8px 8px 8px;
    .item-name {
      margin-top: 16px;
    }
    .list-item {
      .item-user {
        margin-left: 16px;
        margin-top: 8px;
        display: flex;
        div {
          width: 50%;
        }
      }
    }
  }
}
</style>
