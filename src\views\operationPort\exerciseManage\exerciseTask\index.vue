<template>
  <PageContainer>
    <div slot="header">
      <div class="search-header">
        <div class="search-from">
          <el-input v-model="searchForm.taskName" placeholder="请输入任务名称"></el-input>
          <el-select v-model="searchForm.drillType" placeholder="请选择演练类型" class="ml-16">
            <el-option v-for="item in typeList" :key="item.id" :label="item.labelName" :value="item.id"> </el-option>
          </el-select>
          <el-input v-model="searchForm.headName" placeholder="请输入责任人姓名" class="ml-16"></el-input>
          <el-date-picker v-model="time" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
            range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" class="searchInput ml-16">
          </el-date-picker>
          <el-button type="primary" @click="reset">重置</el-button>
          <el-button type="primary" @click="search">查询</el-button>
        </div>
        <div class="operation">
          <el-button type="primary" style="textali" @click="additional">演练补录</el-button>
        </div>
      </div>
    </div>
    <div slot="content" style="height: 100%;">
      <div class="contentTable">
        <div class="statistics">
          <div class="statistics-item">
            <p>演练任务总数</p>
            <p>{{rehearsal.total||'0'}}<span>单</span></p>
          </div>
          <div class="statistics-item">
            <p>已完成任务数</p>
            <p>{{rehearsal.completed||'0'}}<span>单</span></p>
          </div>
          <div class="statistics-item">
            <p>整体任务进度</p>
            <p>{{rehearsal.rate||'0'}}<span>%</span></p>
          </div>
          <div class="progress">
            <el-progress type="circle" :percentage="rehearsal.rate" :width="70" :show-text="false"></el-progress>
          </div>
        </div>
        <div class="contentTable-main table-content">
          <el-table ref="materialTable" v-loading="tableLoading" :data="tableData" border style="width: 100%;" stripe
            :height="tableHeight" highlight-current-row :row-key="getRowKeys">
            <el-table-column align="center" prop="taskName" show-overflow-tooltip label="演练任务名称"></el-table-column>
            <el-table-column align="center" prop="drillTypeName" show-overflow-tooltip label="演练类型"></el-table-column>
            <el-table-column align="center" prop="deptNames" show-overflow-tooltip label="演练部门"></el-table-column>
            <el-table-column align="center" prop="headNames" show-overflow-tooltip label="部门责任人"></el-table-column>
            <el-table-column align="center" prop="deptPersonCount" show-overflow-tooltip label="参与人员">
              <template slot-scope="scope">
                <el-popover placement="bottom" trigger="hover" popper-class="down-popover"
                  :disabled="scope.row.deptPersonList.length===0">
                  <div>
                    <div v-for="(item,index) in scope.row.deptPersonList" :key="index" class="deptPersonCountClass">
                      <span style="display:inline-block;width:70px">{{item.personName}}</span>
                      <span style="margin-left:10px">{{item.departName||""}}</span>
                    </div>
                  </div>
                  <el-button slot="reference" type="text" class="popoverBtn">{{scope.row.deptPersonCount}}</el-button>
                </el-popover>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="taskStartTime" show-overflow-tooltip label="演练时间">
              <template slot-scope="scope">
                <span>{{ moment(scope.row.taskStartTime).format('YYYY-MM-DD') }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="drillPlace" show-overflow-tooltip label="演练地点"></el-table-column>
            <el-table-column align="center" prop="taskState" show-overflow-tooltip label="完成状态">
              <template slot-scope="scope">
                <div v-if="scope.row.taskState===1" class="taskState taskState-color1">
                  <img src="@/assets/images/operationPort/finish.png" /><span>已完成</span>
                </div>
                <div v-if="scope.row.taskState===0" class="taskState taskState-color2">
                  <img src="@/assets/images/operationPort/unFinish.png" /><span>未完成</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="taskCompletionTime" show-overflow-tooltip label="完成时间">
              <template slot-scope="scope">
                <span>{{scope.row.taskCompletionTime? moment(scope.row.taskCompletionTime).format('YYYY-MM-DD'):"-" }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="操作" width="200">
              <template slot-scope="scope">
                <el-button type="text" class="record" @click="check(scope.row)">查看</el-button>
                <el-button v-if="scope.row.appointState==='1'" type="text" class="record"
                  @click="evaluate(scope.row)">去评定</el-button>
                <el-button v-if="scope.row.appointState==='1'" type="text" class="record"
                  @click="designate(scope.row)">指派</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="contentTable-footer">
          <el-pagination class="table-page pagination" style="margin-top: 10px;"
            :current-page="paginationData.currentPage" :page-sizes="[15, 30, 50, 100]"
            :page-size="paginationData.pageSize" layout="total, sizes, prev, pager, next, jumper"
            :total="paginationData.total" @size-change="handleSizeChange"
            @current-change="handleCurrentChange"></el-pagination>
        </div>
      </div>
      <el-dialog v-dialogDrag :visible.sync="dialogVisible" :before-close="dialogClosed" :modal="false"
        :close-on-click-modal="false" title="指派" width="30%" custom-class="model-dialog">
        <div class="diaContent" style="padding: 10px">
          <el-form ref="formInline" :model="formInline" label-width="100px" :rules="rules" label-position="right">
            <el-form-item label="所在部门：" prop="deptId">
              <el-select ref="deptId" v-model="formInline.deptId" placeholder="请选择部门" clearable filterable
                @change="selectDept">
                <el-option v-for="item in deptList" :key="item.id" :label="item.deptName" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="指派人员：" prop="personId">
              <el-select ref="personId" v-model="formInline.personId" placeholder="请选择人员" clearable filterable>
                <el-option v-for="item in personList" :key="item.id" :label="item.staffName" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="dialogClosed">取 消</el-button>
          <el-button type="primary" @click="submitAssign('formInline')">确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </PageContainer>
</template>
<script>
import moment from 'moment'
import tableListMixin from '@/mixins/tableListMixin.js'
export default {
  name: 'exerciseTask',
  mixins: [tableListMixin],
  data() {
    return {
      moment,
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      searchForm: {
        drillType: '',
        headName: '',
        taskName: ''
      },
      rehearsal: {
        completed: '', // 已完成任务数
        rate: 0, // 整体任务进度
        total: '' // 演练任务总数
      },
      time: [],
      typeList: [],
      tableData: [],
      tableLoading: false,
      dialogVisible: false, // 指派弹窗
      formInline: {
        deptId: '',
        personId: ''
      },
      deptList: [], // 科室list
      personList: [], // 人员list
      rules: {
        deptId: { required: true, message: '请选择所在部门', trigger: 'change' },
        personId: { required: true, message: '请选择指派人员', trigger: 'change' }
      },
      rowId: ''
    }
  },
  mounted() {
    this.getTypeList()
    this.getProgressData()
    this.getDataList()
  },
  methods: {
    // 获取演练类型列表
    getTypeList() {
      let data = {
        page: {
          current: 1,
          size: 9999
        }
      }
      this.$api
        .getPreplanDrillTypeData(data)
        .then((res) => {
          this.typeList = res.data ? res.data.list : []
        })
    },
    // 获取演练任务统计
    getProgressData() {
      let userInfo = this.$store.state.user.userInfo.user
      let data = {
        userId: userInfo.staffId
      }
      this.$api
        .getQueryTaskProgress(data)
        .then((res) => {
          this.rehearsal = res.data
        })
    },
    // 列表数据
    getDataList() {
      this.tableLoading = true
      let userInfo = this.$store.state.user.userInfo.user
      let data = {
        ...this.searchForm,
        userId: userInfo.staffId,
        startTime: this.time[0] ? moment(this.time[0]).format('YYYY-MM-DD') : '',
        endTime: this.time[1] ? moment(this.time[1]).format('YYYY-MM-DD') : '',
        page: {
          current: this.paginationData.currentPage,
          size: this.paginationData.pageSize
        }
      }
      this.$api
        .getQueryTaskData(data)
        .then((res) => {
          this.tableLoading = false
          if (res.code == 200) {
            this.tableData = res.data ? res.data.list : []
            this.paginationData.total = res.data ? res.data.totalCount : 0
          } else if (res.message) {
            this.tableData = []
            this.paginationData.total = 0
            this.$message.error(res.message)
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    // 获取部门列表
    getDeptList() {
      this.$api.getSelectedDept().then((res) => {
        if (res.code == '200') {
          this.deptList = res.data
        }
      })
    },
    // 选择巡检部门
    selectDept(val) {
      this.getLersonnelList(val)
    },
    // 获取人员列表
    getLersonnelList(deptIds) {
      let params = {
        current: 1,
        size: 9999,
        sex: '',
        pmId: '',
        postId: '', // 岗位
        stationStatus: '',
        officeId: deptIds
      }
      this.$api.staffList(params).then((res) => {
        if (res.code == 200) {
          this.personList = res.data.records
        }
      })
    },
    // 演练补录
    additional() {
      this.$router.push('/exerciseManage/exerciseTask/additionalRecording')
    },
    // 评定
    evaluate(row) {
      this.$router.push({
        path: '/exerciseManage/exerciseTask/exerciseTaskAssess',
        query: {
          taskId: row.id
        }
      })
    },
    // 指派
    designate(row) {
      this.getDeptList()
      this.rowId = row.id
      this.dialogVisible = true
    },
    // 取消
    dialogClosed() {
      this.formInline = {
        deptId: '',
        personId: ''
      }
      this.$refs.formInline.resetFields()
      this.dialogVisible = false
    },
    // 指派确定
    submitAssign(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let userInfo = this.$store.state.user.userInfo.user
          let data = {
            id: this.rowId,
            userId: userInfo.staffId,
            appointId: this.formInline.personId
          }
          this.$api.designateTaskData(data).then((res) => {
            if (res.code === '200') {
              this.getDataList()
              this.$message.success('指派成功')
              this.dialogVisible = false
              this.formInline = {
                deptId: '',
                personId: ''
              }
              this.$refs.formInline.resetFields()
            } else {
              this.$message.error(res.msg)
            }
          })
        } else {
          return false
        }
      })
    },
    check(row) {
      this.$router.push({
        path: '/exerciseManage/exerciseTask/exerciseTaskDetail',
        query: {
          taskId: row.id
        }
      })
    },
    // 条件查询
    search() {
      this.paginationData.currentPage = 1
      this.getDataList()
    },
    // 重置查询条件
    reset() {
      this.paginationData = {
        currentPage: 1,
        pageSize: 15,
        total: 0
      }
      this.searchForm = {
        drillType: '',
        headName: '',
        taskName: ''
      }
      this.time = []
      this.getDataList()
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.getDataList()
    },
    // 每页的条数
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.paginationData.currentPage = 1
      this.getDataList()
    },
    getRowKeys(row) {
      return row.id
    }
  }
}
</script>
<style lang="scss" scoped>
.search-header {
  padding: 16px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  .el-input {
    width: 200px;
  }

  .searchInput {
    margin-right: 20px;
  }
  .search-from {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
  }
}
.statistics {
  display: flex;
  .statistics-item {
    text-align: left;
    margin: 0 100px 0 24px;
    p:first-child {
      font-weight: 500;
      font-size: 15px;
      color: #333333;
      line-height: 18px;
      margin-bottom: 7px;
    }
    p:last-child {
      font-weight: bold;
      font-size: 24px;
      color: #333333;
      line-height: 36px;
      margin-bottom: 24px;
      span {
        font-weight: 500;
        font-size: 15px;
        color: #7f848c;
        line-height: 18px;
        margin-left: 4px;
      }
    }
  }
  .statistics-item:nth-child(3) {
    margin-right: 30px !important;
  }
}
.contentTable {
  height: calc(100% - 16px);
  background: #fff;
  border-radius: 4px;
  padding: 16px;
  display: flex;
  margin-top: 16px;
  flex-direction: column;
  overflow: auto;

  .contentTable-main {
    flex: 1;
    overflow: auto;
  }

  .contentTable-footer {
    padding: 10px 0 0;
  }
  .taskState {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    img {
      vertical-align: middle;
    }
    span {
      margin-left: 5px;
    }
  }
  .taskState-color1 {
    background: #e8ffea;
    color: #009a29;
  }
  .taskState-color2 {
    background: #ffece8;
    color: #cb2634;
  }
}
.ml-16 {
  margin-left: 16px;
}
.record {
  color: #3562db !important;
}
.diaContent {
  width: 100%;
  max-height: 500px !important;
  overflow: auto;
  background-color: #fff !important;
  .el-input,
  .el-select,
  .el-cascader {
    width: 300px;
  }
}
::v-deep .popoverBtn.el-button--text {
  color: #666666 !important;
}
</style>
