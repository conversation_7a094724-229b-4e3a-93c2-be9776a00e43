<template>
  <PageContainer>
    <div slot="header" class="control-btn-header">
      <div class="search-from">
        <el-input v-model="searchFrom.userInfo" placeholder="输入登录账号、姓名搜索" clearable style="width: 200px"></el-input>
        <el-select v-model="searchFrom.loginPlatform" placeholder="登录平台" clearable style="width: 200px">
          <el-option v-for="(item, index) in logTypeList" :key="index" :value="item.id" :label="item.name"></el-option>
        </el-select>
        <el-date-picker
          v-model="searchFrom.startTime"
          style="width: 200px"
          type="datetime"
          placeholder="开始日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          :picker-options="startPickerOptions"
        >
        </el-date-picker>
        <el-date-picker
          v-model="searchFrom.endTime"
          style="width: 200px"
          type="datetime"
          placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          :picker-options="endPickerOptions"
        >
        </el-date-picker>
        <div style="display: inline-block">
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="searchForm">查询</el-button>
          <el-button type="primary" plain @click="exportData">导出</el-button>
        </div>
      </div>
    </div>
    <div slot="content" class="table-content">
      <div v-if="selectionList.length" class="listTipContent">
        <i class="el-icon-warning" style="color: #1890ff; margin-right: 5px"></i>
        <span
        >已选择<span style="color: #1890ff; margin: 0 5px">{{ selectionList.length }}</span
        >条记录</span
        >
        <span style="margin-left: 10px; color: #1890ff; cursor: pointer" @click="clearSelection">清空</span>
      </div>
      <TablePage
        ref="table"
        v-loading="tableLoading"
        :showPage="true"
        :tableColumn="tableColumn"
        :data="tableData"
        :height="`calc(100% - ${selectionList.length ? '110px' : '40px'})`"
        :pageData="pageData"
        :pageProps="pageProps"
        @pagination="paginationChange"
        @row-dblclick="detail"
        @selection-change="selectionChange"
      />
    </div>
  </PageContainer>
</template>
<script lang="jsx">
import axios from 'axios'
import moment from 'moment'
export default {
  name: 'operationLog',
  data() {
    let that = this
    return {
      tableLoading: false,
      startPickerOptions: {
        disabledDate(time) {
          if (!that.searchFrom.endTime) {
            return false
          }
          const oneYearAgo = moment().clone().subtract(1, 'year')
          return moment(that.searchFrom.endTime).valueOf() < time || time < oneYearAgo
        }
      },
      endPickerOptions: {
        disabledDate(time) {
          return time > moment().valueOf()
        }
      },
      searchFrom: {
        startTime: moment().subtract(2, 'days').format('YYYY-MM-DD HH:mm:ss'),
        endTime: moment().format('YYYY-MM-DD HH:mm:ss'),
        userInfo: '',
        loginPlatform: ''
      },
      logTypeList: [
        {
          name: '综合管理平台web端',
          id: '1'
        },
        {
          name: '医帮手app',
          id: '2'
        },
        {
          name: '综合管理平台client端',
          id: '3'
        }
      ],
      tableColumn: [
        {
          type: 'selection'
        },
        {
          prop: 'userAccount',
          label: '登录账号'
        },
        {
          prop: 'userName',
          label: '账号姓名'
        },
        {
          prop: 'loginAdress',
          label: '登录地'
        },
        {
          prop: 'clientIp',
          label: 'IP地址'
        },
        {
          prop: 'loginTime',
          label: '登录时间'
        },
        {
          prop: 'loginPlatform',
          label: '登录平台',
          formatter: (row, column, cellValue) => {
            let obj = {}
            obj = this.logTypeList.find((el) => {
              return el.id === row.row.loginPlatform
            })
            if (obj) {
              return obj.name
            }
          }
        },
        {
          width: '400',
          prop: 'loginResult',
          label: '登录结果',
          render: (h, row) => {
            if (row.row.loginResult == 1) {
              return <span style="color: green">成功</span>
            } else {
              return (
                <span>
                  <span style="color: red">失败</span>
                  <span>（{row.row.resultMessage}）</span>
                </span>
              )
            }
          }
        }
      ],
      tableData: [],
      pageData: {
        current: 1,
        pageSize: 15,
        total: 0
      },
      pageProps: {
        page: 'current',
        pageSize: 'pageSize',
        total: 'total'
      },
      selectionList: []
    }
  },
  mounted() {
    this.getLogList()
  },
  methods: {
    selectionChange(val) {
      this.selectionList = val
    },
    exportData() {
      let params = {
        logType: this.searchFrom.logType,
        userName: this.searchFrom.userName,
        startTime: this.searchFrom.startTime,
        endTime: this.searchFrom.endTime,
        ids: this.selectionList.map((item) => item.id)
      }
      const userInfo = this.$store.state.user.userInfo.user
      let menuList = this.$store.state.menu.routes
      let firstTitle = ''
      let routeList = []
      this.$router.currentRoute.matched
        .filter((item) => item.name)
        .forEach((el) => {
          routeList.push(el.meta.title)
        })
      if (menuList.length) {
        firstTitle = menuList[this.$store.state.menu.headerActived].meta.title
        routeList.unshift(firstTitle)
      }
      axios({
        method: 'post',
        url: __PATH.SPACE_API + 'loginLog/exportExcel',
        data: params,
        responseType: 'arraybuffer',
        headers: {
          'Content-Type': 'application/json;charset=utf-8',
          Authorization: 'Bearer ' + this.$store.state.user.token,
          hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
          unitCode: userInfo.unitCode ?? 'BJSYGJ',
          'operation-type': 4,
          'operation-content': encodeURIComponent(routeList.join(','))
        }
      })
        .then((res) => {
          this.$message.success(res.message || '导出成功')
          this.$tools.downloadFile(res, this)
        })
        .catch((res) => {
          this.$message.error(res.message || '导出失败')
        })

    },
    timeChange(val) {
      if (val) {
        this.searchFrom.startTime = val[0]
        this.searchFrom.endTime = val[1]
      }
    },
    clearSelection() {
      this.$refs.table.clearSelection()
    },
    // 查询
    searchForm() {
      this.getLogList()
    },
    // 重置查询
    resetForm() {
      this.searchFrom = {
        startTime: moment().subtract(2, 'days').format('YYYY-MM-DD HH:mm:ss'),
        endTime: moment().format('YYYY-MM-DD HH:mm:ss'),
        userInfo: '',
        loginPlatform: ''
      }
      this.pageData = {
        pageSize: 15,
        current: 1,
        total: 0
      }
      this.searchForm()
    },
    detail(type, row) {},
    // 获取应用列表
    getLogList() {
      let param = {
        ...this.searchFrom,
        pageSize: this.pageData.pageSize,
        current: this.pageData.current
      }
      this.tableLoading = true
      this.$api
        .loginLogList(param)
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.records
            this.pageData.total = res.data.total
          }
          this.tableLoading = false
        })
        .catch(() => {
          this.tableLoading = false
        })
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getLogList()
    }
  }
}
</script>
<style lang="scss" scoped>
.control-btn-header {
  padding: 10px !important;
  .search-from {
    display: flex;
    align-items: center;
    & > div {
      margin-right: 10px;
    }
  }
}
.table-content {
  .listTipContent {
    height: 60px;
    width: 100%;
    background: #f3f9ff;
    border: 1px solid #d1e9ff;
    padding: 10px;
    line-height: 40px;
    margin-bottom: 10px;
  }
  margin-top: 15px;
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 15px);
  padding: 10px;
}
</style>
<style lang="scss">
.operationBtn-span {
  margin-right: 10px;
}
</style>
