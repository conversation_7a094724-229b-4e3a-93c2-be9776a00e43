<script>
export default {
  name: 'FloorEdit',
  props: {
    visible: <PERSON>olean,
    treeNodeId: String,
    id: String,
    // 在管房间
    roomCount: Number
  },
  events: ['update:visible', 'success'],
  data: () => ({
    formModel: {
      position: '1',
      // 楼层
      number: 1,
      // 显示名称
      label: ''
    },
    rules: {
      number: [{ required: true, message: '请输入实际楼层名称' }],
      label: [{ required: true, message: '请输入楼层显示名称' }]
    },
    dictData: [],
    loadingStatus: false,
    // 人员选择dialog显示
    userDialog: false,
    // timerId
    $timerId: -1
  }),
  computed: {
    title: function() {
      return this.id ? '编辑楼层' : '创建楼层'
    },
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    }
  },
  watch: {
    dialogVisible(val) {
      val && this.id && this.getDetail()
    }
  },
  methods: {
    getDetail() {
      this.loadingStatus = true
      this.$api.rentalHousingApi.getFloorById({ id: this.id })
        .then((res) => {
          if (res.code === '200') {
            this.formModel.number = res.data.actualNumber
            this.formModel.label = res.data.displayName
            this.formModel.position = res.data.locationType
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取详情信息失败'))
        .finally(() => (this.loadingStatus = false))
    },
    onDialogClosed() {
      // dialog关闭时重置form表单
      this.$refs.formRef.resetFields()
    },
    onSubmit() {
      this.$refs.formRef
        .validate()
        .catch(() => Promise.reject())
        .then(() => {
          // config request data
          const params = {
            actualNumber: this.formModel.number,
            displayName: this.formModel.label,
            locationType: this.formModel.position
          }
          if (this.id) {
            params.id = this.id
          } else {
            params.unitId = this.treeNodeId
          }
          this.loadingStatus = true
          // do request
          return this.$api.rentalHousingApi.saveFloorInfo(params)
        })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('操作成功')
            this.$emit('success')
            this.dialogVisible = false
          } else {
            throw res.message || '操作失败'
          }
        })
        .catch((msg) => {
          msg && this.$message.error(msg)
        })
        .finally(() => {
          this.loadingStatus = false
        })
    }
  }
}
</script>
<template>
  <el-dialog
    v-dialogDrag
    class="component floor-edit"
    :title="title"
    width="550px"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    custom-class="model-dialog"
    destroy-on-close
    @close="onDialogClosed"
  >
    <el-form ref="formRef" v-loading="loadingStatus" :model="formModel" :rules="rules" label-width="95px">
      <el-form-item label="楼层位置" prop="position" :class="{disabled:!!id}">
        <el-radio-group v-model="formModel.position" :disabled="!!id">
          <el-radio label="1">地上</el-radio>
          <el-radio label="2">地下</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="实际层数" prop="number" :class="{disabled:!!id}">
        <el-input v-model="formModel.number" v-if="!!id" disabled></el-input>
        <el-input-number v-model="formModel.number" v-if="!id" placeholder="请输入实际楼层名称" :min="1" :max="99" :step="1" :precision="0"></el-input-number>
      </el-form-item>
      <el-form-item label="显示名称" prop="label">
        <el-input v-model="formModel.label" placeholder="请输入楼层显示名称" maxlength="50"></el-input>
      </el-form-item>
      <el-form-item label="在管房间">
        <span style="padding-left: 16px">{{ roomCount }}间</span>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" plain @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" :loading="loadingStatus" @click="onSubmit">确认</el-button>
    </template>
  </el-dialog>
</template>
<style lang="scss" scoped>
.floor-edit {
  .el-form {
    width: 100%;
    height: 100%;
    background: #fff;
    padding: 16px;
    ::v-deep(.el-form-item.disabled) {
      .el-form-item__label {
        &::before {
          display: none;
        }
      }
      .el-input__inner {
        background: transparent;
        border: none;
        color: inherit;
        cursor: default;
        &::placeholder {
          color: transparent;
        }
      }
      .el-input-number {
        width: auto;
        .el-input__inner {
          padding: 16px;
          text-align: left;
        }
        & > span {
          display: none;
        }
      }
      .el-radio__input {
        display: none;
      }
      .el-radio__label {
        padding-left: 16px;
        color: #606266;
      }
      .el-radio:not(.is-checked) {
        display: none;
      }
    }
  }
  .el-input-number {
    line-height: 30px;
  }
}
</style>
