<template>
  <PageContainer>
    <div slot="content" class="role-content" style="height: 100%;">
      <div class="role-content-left">
        <div class="toptip">
          <span class="green_line"></span>
          角色
        </div>
        <div class="left_content">
          <div class="tree">
            <el-tree
              ref="tree"
              v-loading
              :data="treeData"
              :props="defaultProps"
              :highlight-current="true"
              node-key="id"
              @node-click="handleNodeClick"
              @check="treeChecked"
            ></el-tree>
          </div>
        </div>
      </div>
      <div class="role-content-right">
        <div style="height: 100%;">
          <div class="search-from">
            <el-input v-model.trim="filters.name" clearable placeholder="人员姓名" style="width: 200px;"></el-input>
            <el-input v-model.trim="filters.mobilePhone" clearable placeholder="手机号" style="width: 200px;"></el-input>
            <el-select v-model="filters.positionType"  placeholder="部门权限" filterable clearable>
              <el-option v-for="(item, index) in riskLevelList" :key="index" :label="item.name" :value="item.id" class="set_zindex"></el-option>
            </el-select>
            <el-button type="primary" plain @click="resetData">重置</el-button>
            <el-button type="primary" @click="getStaffList">查询</el-button>
          </div>
          <div class="contentTable">
            <div class="contentTable-main table-content">
              <el-table
                v-loading="tableLoading"
                :data="tableData"
                :height="tableHeight"
                border
                stripe
                @selection-change="handleSelectionChange"
              >
                <!-- <el-table-column type="selection" width="55" align="center" fixed="left"></el-table-column> -->
                <el-table-column type="index" label="序号" width="80">
                  <template slot-scope="scope">
                    <span class="spanStyle">{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="userName" show-overflow-tooltip label="登录名"></el-table-column>
                <el-table-column prop="phone" show-overflow-tooltip min-width="120" label="手机号"></el-table-column>
                <el-table-column prop="name" show-overflow-tooltip width="120"  label="姓名"></el-table-column>
                <el-table-column prop="controlTeamName" show-overflow-tooltip label="所属部门"></el-table-column>
                <el-table-column prop="teamTypeName" show-overflow-tooltip label="部门类型"></el-table-column>
                <el-table-column prop="userType" show-overflow-tooltip label="人员类型">
                  <template slot-scope="scope">
                    <span>
                      {{ scope.row.userType == '1' ? '院内' : scope.row.userType == '2' ? '院外' : '' }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="positionType" show-overflow-tooltip label="部门权限">
                  <template slot-scope="scope">
                    <span>
                      {{ scope.row.positionType == '1' ? '组长' : scope.row.positionType == '2' ? '组员' : '' }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="webRoleName" show-overflow-tooltip label="角色"></el-table-column>
              </el-table>
            </div>
            <div class="contentTable-footer">
              <el-pagination
                class="pagination"
                :current-page="paginationData.currentPage"
                :page-sizes="[15, 30, 50, 100]"
                :page-size="paginationData.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="paginationData.total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              ></el-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PageContainer>
</template>

<script>
import tableListMixin from '@/mixins/tableListMixin.js'
export default {
  name: 'configureManagement',
  components: {},
  mixins: [tableListMixin],
  data() {
    return {
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      filters: {
        name: '',
        mobilePhone: '',
        positionType: ''
      },
      riskLevelList: [
        {
          name: '组长',
          id: 1
        },
        {
          name: '组员',
          id: 2
        }
      ],
      multipleSelection: [],
      tableData: [],
      advancClose: true,
      treeLoading: false,
      tableLoading: false,
      organizationTypeArr: [],
      defaultProps: {
        children: 'children',
        label: 'roleName',
        value: 'roleCode'
      },
      treeData: [],
      checkedData: '123',
      dialogVisible2: false
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.$api.ipsmUserRoleGetRoleList({}).then((res) => {
        this.treeLoading = true
        this.treeData = res.data
        if (this.treeData.length > 0) {
          // 默认选中第一个
          this.checkedData = this.treeData[0]
          this.getStaffList()
          this.$nextTick(() => {
            this.$refs.tree.setCheckedNodes([this.checkedData])
            this.$refs.tree.setCurrentKey(this.checkedData.id)
          })
        }
        this.treeLoading = false
      })
    },
    // 根据角色code获取用户列表
    getStaffList() {
      let data = {
        currentPage: this.paginationData.currentPage,
        pageSize: this.paginationData.pageSize,
        webRoleCode: this.checkedData.id,
        ...this.filters
      }
      this.$api.ipsmGetUserListByWebRoleCode(data).then((res) => {
        this.tableLoading = true
        if (res.code == 200) {
          this.tableData = res.data.list
          this.paginationData.total = parseInt(res.data.sum)
        } else {
          this.$message.error(res.message)
        }
        this.tableLoading = false
      })
    },
    treeChecked(data, checked) {
      this.checkedData = data
      this.$refs.tree.setCheckedNodes([data])
      this.$refs.tree.setCurrentKey(this.checkedData)
    },
    // 树状图点击
    handleNodeClick(data, checked) {
      this.paginationData.currentPage = 1
      this.checkedData = data
      this.$refs.tree.setCheckedNodes([data])
      this.$refs.tree.setCurrentKey(this.checkedData)
      this.getStaffList()
    },

    // 查询
    searchClick() {
      this.paginationData.currentPage = 1
      this.getStaffList()
    },
    // 重置
    resetData() {
      this.filters.name = ''
      this.filters.mobilePhone = ''
      this.filters.positionType = ''
      // 取消左侧树的选中
      this.checkedData = []
      this.$refs.tree.setCheckedNodes([this.checkedData])
      this.$refs.tree.setCurrentKey(null)
      this.getStaffList()
    },

    //  ----------------------------------------------------分页相关------------------------------------------------------------
    handleSizeChange(val) {
      this.paginationData.currentPage = 1
      this.paginationData.pageSize = val
      this.getStaffList()
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.getStaffList()
    },
    //  ----------------------------------------------------树状结构相关---------------------------------------------------------

    //  ------------------------------------------------操作表格中数据------------------------------------------------------------
    handleSelectionChange(val) {
      this.multipleSelection = val
    }
  }
}
</script>
<style lang="scss" scoped>
// el-tree-node is-expanded is-focusable
::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: linear-gradient(to right, #d9e1f8, #fff);
}

::v-deep .el-tree-node__content {
  height: 32px;
}

.role-content {
  height: 100%;
  display: flex;

  .role-content-left {
    width: 246px;
    height: 100%;
    padding: 10px;
    background: #fff;
    border-radius: 4px;
    margin-right: 12px;

    .left_content {
      height: calc(100% - 60px);
      padding: 10px;
      // overflow: auto;
      .tree {
        height: calc(100% - 40px);
        overflow: auto;
      }

      ul {
        padding: 0;

        li {
          height: 38px;
          width: 100%;
          font-size: 14px;
          font-family: "PingFang SC-Regular", "PingFang SC";
          font-weight: 400;
          color: #606266;
          line-height: 38px;
          list-style-type: none;
          box-sizing: border-box;
          cursor: pointer;
          padding-left: 27px;

          &:hover {
            background: rgb(53 98 219 / 20%);
            border-radius: 4px 0 0 4px;
          }
        }
      }
    }
  }

  .role-content-right {
    flex: 1;
    height: 100%;
    min-width: 0;
    padding: 10px;
    background: #fff;
    border-radius: 4px;

    .search-from {
      padding-bottom: 12px;

      & > div {
        margin-right: 10px;
      }

      & > button {
        margin-top: 12px;
      }
    }

    .contentTable {
      height: calc(100% - 50px);
      display: flex;
      flex-direction: column;

      .contentTable-main {
        flex: 1;
        overflow: auto;
      }

      .contentTable-footer {
        padding: 10px 0 0;
      }
    }
  }
}
</style>
