<template>
  <el-dialog title="事件纪录" width="65%" :visible.sync="eventRecordDialogShow" custom-class="model-dialog"
    :before-close="closeDialog">
    <div class="content">
      <div class="search-from">
        <el-select v-model="filterInfo.incidentName" placeholder="事件类型" clearable>
          <el-option v-for="item in eventTypeOptions" :key="item.label" :label="item.label" :value="item.label">
          </el-option>
        </el-select>
        <el-select v-model="filterInfo.alarmLevel" placeholder="等级" clearable>
          <el-option v-for="item in alarmLevelOptions" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
        <el-date-picker v-model="filterInfo.dataRange" type="daterange" unlink-panels range-separator="至"
          start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd">
        </el-date-picker>
        <el-button type="primary" plain @click="resetForm">重置</el-button>
        <el-button type="primary" @click="searchForm">查询</el-button>
      </div>
      <div class="sino_table">
        <el-table ref="sinoTable" v-loading="tableLoading" :data="tableData" row-key="id" height="450px" stripe border>
          <el-table-column prop="alarmStartTime" label="时间" show-overflow-tooltip></el-table-column>
          <el-table-column prop="alarmDetails" label="描述" show-overflow-tooltip></el-table-column>
          <el-table-column prop="incidentName" label="类型" show-overflow-tooltip></el-table-column>
          <el-table-column prop="alarmLevel" label="等级">
            <span slot-scope="scope" class="alarmLevel"
              :style="{background: alarmLevelItem[scope.row.alarmLevel].color}">
              {{alarmLevelItem[scope.row.alarmLevel].text}}
            </span>
          </el-table-column>
          <el-table-column prop="completeRegionName" label="位置" show-overflow-tooltip></el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination :current-page="pagination.pageNo" :page-sizes="[15, 30, 50, 100]" :page-size="pagination.size"
            :total="pagination.total" layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
            @current-change="handleCurrentChange"></el-pagination>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'selectEventRecord',
  components: {},
  props: {
    eventRecordDialogShow: {
      type: Boolean,
      default: false
    },
    objectId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tableLoading: false,
      tableData: [],
      pagination: {
        pageSize: 15,
        pageNo: 0,
        total: 0
      }, // 分页数据
      filterInfo: {
        incidentName: '', // 事件类型
        alarmLevel: '', // 报警等级
        dataRange: [] // 时间范围
      }, // 搜索条件
      eventTypeOptions: [], // 事件类型
      alarmLevelOptions: [
        {
          value: '',
          label: '全部'
        },
        {
          value: '0',
          label: '通知'
        },
        {
          value: '1',
          label: '一般'
        },
        {
          value: '2',
          label: '紧急'
        },
        {
          value: '3',
          label: '重要'
        }
      ], // 报警等级
      alarmLevelItem: {
        0: { text: '通知', color: '#3562DB' },
        1: { text: '一般', color: '#3562DB' },
        2: { text: '紧急', color: '#FF9435' },
        3: { text: '重要', color: '#FA403C' }
      }
    }
  },
  mounted() {
    this.getIncidentTypeList()
    this.getTableData()
  },
  methods: {
    // 获取事件类型
    getIncidentTypeList() {
      this.eventTypeOptions = []
      this.$api.getEventTypeList({ objectId: this.objectId }).then((res) => {
        if (res.code == 200) {
          res.data.forEach(el => {
            this.eventTypeOptions.push({ label: el })
          })
        }
      })
    },
    getTableData() {
      this.tableLoading = true
      const { alarmLevel, incidentName } = this.filterInfo
      let data = {
        ...this.pagination,
        startTime: this.filterInfo.dataRange[0],
        endTime: this.filterInfo.dataRange[1],
        alarmLevel: alarmLevel,
        incidentName: incidentName,
        // objectId: '2ba6b90cf6974350afbaf60cef19f42c' // 测试
        objectId: this.objectId
      }
      delete data.dataRange
      this.$api
        .getEventRecordList(data)
        .then((res) => {
          this.tableLoading = false
          if (res.code == 200) {
            this.tableData = res.data ? res.data.records : []
            this.pagination.total = res.data ? res.data.total : 0
          } else if (res.message) {
            this.tableData = []
            this.pagination.total = 0
            this.$message.error(res.message)
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    closeDialog() {
      this.$emit('closeEventRecordDialog')
    },
    handleSizeChange(val) {
      this.pagination.pageNo = 0
      this.pagination.pageSize = val
      this.getTableData()
    },
    handleCurrentChange(val) {
      this.pagination.pageNo = val - 1
      this.getTableData()
    },
    // 重置查询
    resetForm() {
      this.filterInfo = {
        incidentName: '', // 事件类型
        alarmLevel: '', // 报警等级
        dataRange: [] // 时间范围
      }
      this.getTableData()
    },
    // 查询
    searchForm() {
      this.getTableData()
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog {
  margin-top: 8vh !important;
}
.model-dialog {
  .content {
    margin: 0 auto;
    background: #fff;
    border-radius: 4px;
    width: 100%;
    height: 580px;
    overflow: auto;
    padding-right: 20px;
  }
  .alarmLevel {
    color: #ffffff;
    padding: 2px 10px;
  }
  .pagination {
    margin-top: 20px;
  }
  .search-from {
    padding-bottom: 12px;

    & > div {
      margin-top: 12px;
      margin-right: 10px;
    }
  }
}
</style>

