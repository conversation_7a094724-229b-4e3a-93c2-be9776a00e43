<template>
  <PageContainer :footer="true">
    <div slot="content" v-loading="pageLoading" class="table-content">
      <el-form ref="formInline" :model="formInline" :rules="rules">
        <ContentCard title="新增用户">
          <div slot="content" class="footer-role">
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="7">
                <el-form-item label="人员选择" prop="staffId" label-width="100px">
                  <!-- <el-input :value="formInline.staffName" placeholder="请选择人员" clearable suffix-icon="el-icon-arrow-down" @clear="selectPersClear" @focus="selectPers"/> -->
                  <el-input
                    :value="formInline.staffName"
                    placeholder="请选择人员"
                    :disabled="$route.query.type != 'add'"
                    readonly
                    suffix-icon="el-icon-arrow-down"
                    @clear="selectPersClear"
                    @focus="selectPers"
                  />
                </el-form-item>
              </el-col>
              <el-col :md="7">
                <el-form-item label="登录账号" prop="userName" label-width="100px">
                  <el-input v-model="formInline.userName" placeholder="请输入登录账号" :disabled="$route.query.type == 'detail'" clearable></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="7">
                <el-form-item label="人员姓名" prop="staffName" label-width="100px">
                  <el-input :value="formInline.staffName" disabled></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </ContentCard>
        <ContentCard title="人员信息">
          <div slot="content" class="footer-role">
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="7">
                <el-form-item label="人员姓名" prop="staffName" label-width="100px">
                  <el-input :value="formInline.staffName" disabled></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="7">
                <el-form-item label="身份证号" prop="card" label-width="100px">
                  <el-input :value="formInline.card" disabled></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="7">
                <el-form-item label="籍贯" prop="nativePlace" label-width="100px">
                  <el-input :value="formInline.nativePlace" disabled></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="7">
                <el-form-item label="性别" prop="sex" label-width="100px">
                  <el-input :value="[1, 2].includes(formInline.sex) ? (formInline.sex == 2 ? '女' : '男') : ''" disabled></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="7">
                <el-form-item label="手机号码" prop="phone" label-width="100px">
                  <el-input :value="formInline.phone" disabled></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="7">
                <el-form-item label="职工工号" prop="staffNum" label-width="100px">
                  <el-input :value="formInline.staffNum" disabled></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="7">
                <el-form-item label="办公电话" prop="phoneOffice" label-width="100px">
                  <el-input :value="formInline.phoneOffice" disabled></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </ContentCard>
        <ContentCard title="人员工作信息">
          <div slot="content" class="footer-role">
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="7">
                <el-form-item label="在职状态" prop="workState" label-width="100px">
                  <el-input :value="[0, 1].includes(formInline.workState) ? (formInline.workState == 0 ? '在职' : '离职') : ''" disabled></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="7">
                <el-form-item label="入职时间" prop="workEntryTime" label-width="100px">
                  <el-input :value="formInline.workEntryTime" disabled></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="7">
                <el-form-item label="所属单位" prop="unit" label-width="100px">
                  <el-input :value="formInline.unit" disabled></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="7">
                <el-form-item label="所属部门" prop="deptName" label-width="100px">
                  <el-input :value="formInline.deptName" disabled></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="7">
                <el-form-item label="岗位" prop="job" label-width="100px">
                  <el-input :value="formInline.job" disabled></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="7">
                <el-form-item label="职务" prop="position" label-width="100px">
                  <el-input :value="formInline.position" disabled></el-input>
                </el-form-item>
              </el-col>
              <el-col v-if="hospitalNodeEnv === 'outernet'" :md="7">
                <el-form-item label="启用时间" prop="loginExpirationStartTime" label-width="100px">
                  <el-date-picker
                    v-model="formInline.loginExpirationStartTime"
                    style="width: 100%"
                    :disabled="$route.query.type == 'detail'"
                    type="datetime"
                    placeholder="启用时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    :picker-options="pickerOptions"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col v-if="hospitalNodeEnv === 'outernet'" :md="7">
                <el-form-item label="停用时间" prop="loginExpirationTime" label-width="100px">
                  <el-date-picker
                    v-model="formInline.loginExpirationTime"
                    style="width: 100%"
                    :disabled="$route.query.type == 'detail'"
                    type="datetime"
                    placeholder="停用时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    :picker-options="pickerOptions"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </ContentCard>
        <ContentCard title="角色信息">
          <div slot="content" class="footer-menu">
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="21">
                <el-form-item class="tableBox" label="分配角色" prop="roleSelect" label-width="100px">
                  <el-table ref="roleTable" border :data="tableData" style="width: 100%" @selection-change="handleSelectionChange">
                    <el-table-column v-if="$route.query.type != 'detail'" :selectable="isSelectable" type="selection" width="50" align="center"></el-table-column>
                    <el-table-column label="序号" type="index" width="50"> </el-table-column>
                    <el-table-column label="角色名称" prop="roleName" show-overflow-tooltip></el-table-column>
                    <el-table-column label="角色编码" prop="roleCode" show-overflow-tooltip></el-table-column>
                  </el-table>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </ContentCard>
      </el-form>
      <selectPersDialog
        v-if="isSelectPers"
        selectMode="2"
        :visible="isSelectPers"
        @updateVisible="
          () => {
            isSelectPers = false
          }
        "
        @advancedSearchFn="selectPersChange"
      />
    </div>
    <div slot="footer">
      <el-button
        type="primary"
        plain
        @click="
          () => {
            $router.go(-1)
          }
        "
        >关闭</el-button
      >
      <el-button type="primary" :disabled="$route.query.type == 'detail'" @click="submitForm('formInline')">保存</el-button>
    </div>
  </PageContainer>
</template>
<script>
export default {
  name: 'userForm',
  components: {
    selectPersDialog: () => import('@/views/alarmCenter/linkageConfiguration/components/dialogConfig.vue')
  },
  // 路由前置守卫动态修改meta中的title
  beforeRouteEnter(to, from, next) {
    if (to.meta.title) {
      const typeList = {
        add: '新增用户',
        edit: '编辑用户',
        detail: '用户详情'
      }
      to.meta.title = typeList[to.query.type] ?? '用户详情'
    }
    next()
  },
  async beforeRouteLeave(to, from, next) {
    if (!['userManagement'].includes(to.name)) {
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      pageLoading: false,
      isSelectPers: false, // 选择人员
      formInline: {
        userName: '',
        loginExpirationTime: '',
        loginExpirationStartTime: ''
      },
      multipleSelection: [],
      rules: {},
      tableData: [],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7
        }
      },
      hospitalNodeEnv: __PATH.VUE_APP_HOSPITAL_NODE_ENV
    }
  },
  activated() {
    this.initInfo()
  },
  created() {
    if (!this.$store.state.keepAlive.list.includes('userManagement')) {
      this.initInfo()
    }
  },
  methods: {
    isSelectable(row) {
      return row.id !== 1
    },
    // 清除选择人员
    selectPersClear() {
      this.$refs.roleTable.clearSelection()
      this.$refs.formInline.resetFields()
    },
    // 选择人员事件
    selectPersChange(data) {
      this.initInfo(data[0].id)
    },
    // 选择人员
    selectPers() {
      this.isSelectPers = true
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (!this.multipleSelection.length) {
            this.$message({ message: '请选择分配角色', type: 'error' })
            return
          }
          this.formInline.roleId = this.multipleSelection.map((item) => item.id).join(',')
          if (this.$route.query.type == 'add') {
            this.$api.AddUserInfo(this.formInline, { 'operation-type': 1 }).then((res) => {
              if (res.code == 200) {
                this.$message({ message: '提交成功', type: 'success' })
                this.$router.go(-1)
                // this.selectPersClear()
              } else {
                this.$message({ message: res.msg, type: 'error' })
              }
            })
          } else if (this.$route.query.type == 'edit') {
            this.$api.UpdateUserInfo(this.formInline, { 'operation-type': 2, 'operation-id': this.formInline.id, 'operation-name': this.formInline.staffName }).then((res) => {
              if (res.code == 200) {
                this.$message({ message: '提交成功', type: 'success' })
                this.$router.go(-1)
                // this.selectPersClear()
              } else {
                this.$message({ message: res.msg, type: 'error' })
              }
            })
          }
        }
      })
    },
    // 获取角色列表
    // getRoleList() {
    //   let params = {
    //     pageSize: 100,
    //     page: 1
    //   }
    //   this.$api.GetRoleList(params).then((res) => {
    //     if (res.code == 200) {
    //       this.tableData = res.data.records
    //     }
    //   })
    // },
    async initInfo(val = this.$route.query.id) {
      let validateStaffId = (rule, value, callback) => {
        if (!this.formInline.staffId) {
          callback(new Error('请选择人员'))
        } else {
          callback()
        }
      }
      Object.assign(this.$data, this.$options.data())
      this.$set(this.$data, 'rules', {
        staffId: [{ required: true, validator: validateStaffId, trigger: ['blur', 'change'] }],
        userName: [
          { required: true, message: '请输入登录账号', trigger: ['blur', 'change'] },
          { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: ['blur', 'change'] }
        ]
      })
      this.$nextTick(() => {
        this.$refs.formInline.resetFields()
      })
      this.pageLoading = true
      this.$refs.roleTable.clearSelection()
      let type = this.$route.query.type
      let newArr = []
      if (type != 'detail') {
        const {
          data: { records: tableData }
        } = await this.$api.GetRoleList({ pageSize: 100, page: 1 })
        this.tableData = tableData
      }
      if (type == 'add') {
        this.formInline = {
          userName: '', // 账号
          roleId: '', // 角色id
          userType: 3, // 用户类型 0:系统管理员，1:超级管理员，2:普通用户 3:基础信息用户 4:其他
          loginExpirationTime: '',
          loginExpirationStartTime: ''
        }
        const { data: persInfo } = await this.$api.GetEmployeeDetails({ id: val })
        const { data: unitList } = await this.$api.getSelected({}) // 医院列表
        const { data: positionList } = await this.$api.selectByList({}) // 岗位列表
        const {
          data: { records: jobList }
        } = await this.$api.GetJobList({ current: 1, size: 99 }) // 职务列表
        const { data: departmentList } = await this.$api.getSelectedDept({ unitId: persInfo.pmId }) // 根据所属单位回去部门列表
        let newObj = {
          unit: unitList.find((item) => item.umId == persInfo.pmId)?.unitComName ?? '',
          deptName: departmentList.find((item) => item.id == persInfo.officeId)?.deptName ?? '',
          job: positionList.find((item) => item.id == persInfo.postId)?.postName ?? '',
          position: jobList.find((item) => item.id == persInfo.jobId)?.jobName ?? ''
        }
        const { idCard, nativePlace, mobile, phone, sex, id, staffName, staffNumber, entryData, stationStatus, officeId, pmId } = persInfo
        this.formInline = Object.assign(newObj, this.formInline, {
          card: idCard,
          nativePlace,
          phone: mobile,
          phoneOffice: phone,
          sex,
          staffId: id,
          staffName,
          staffNum: staffNumber,
          workEntryTime: entryData,
          workState: stationStatus,
          userName: mobile,
          deptId: officeId,
          umid: pmId
        })
        this.pageLoading = false
      } else if (type == 'edit' || type == 'detail') {
        this.$api.GetUserDetailInfo({ userId: val }).then((res) => {
          if (res.code == 200) {
            this.formInline = res.data.userInfo
            if (type == 'edit') {
              this.tableData.forEach((item) => {
                if (res.data.roleInfo.map((v) => v.id).includes(item.id)) {
                  newArr.push(item)
                }
              })
              newArr.forEach((row) => {
                this.$refs.roleTable.toggleRowSelection(row)
              })
            } else {
              this.tableData = res.data.roleInfo
            }
            this.pageLoading = false
          }
        })
      }
    },
    // 多选
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    }
  }
}
</script>
<style lang="scss" scoped>
.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
  padding: 10px;
  overflow-y: auto;
  .footer-menu,
  .footer-data {
    ::v-deep .el-table {
      th {
        line-height: 23px;
      }
    }
    .tableBox {
      ::v-deep .el-form-item__content {
        height: 400px;
      }
    }
  }
}
</style>
