<template>
  <div class="content">
    <div class="detaiBox">
      <div v-for="(item, index) in detailLabel" :key="index" class="list-item">
        <div class="list-label">{{ item.name }}</div>
        <div v-if="detailData[item.key]" class="list-value">
          <span>{{ detailData[item.key] || "--" }}</span>
        </div>
        <div v-else class="list-value">
          <span>--</span>
        </div>
      </div>
      <div class="list-item">
        <div class="list-label">文化程度:</div>
        <span class="list-value">{{ educationName|| "--" }}</span>
      </div>
    </div>
    <div class="detailImage">
      <div class="list-image">
        <div class="list-label">员工照片:</div>
        <!-- <upload :fileList="photoList" modelName="hospitalBaseInfo" :disabled="true"></upload> -->
        <img :src="this.detailData.avatar" alt="" width="180px" height="180px">
      </div>
      <!-- <div class="list-image">
        <div class="list-label">签名:</div>
        <upload :fileList="signList" modelName="hospitalBaseInfo" :disabled="true"></upload>
      </div>
      <div class="list-image">
        <div class="list-label">印章:</div>
        <upload :fileList="sealList" modelName="hospitalBaseInfo" :disabled="true"></upload>
      </div> -->
    </div>
  </div>
</template>

<script>
import upload from '../components/upload.vue'
export default {
  name: "memberBaseInfo",
  components: {
    upload,
  },
  data() {
    return {
      detailLabel: [
        {
          name: "姓名:",
          key: "staffName",
        },
        {
          name: "身份证号:",
          key: "card",
        },
        {
          name: "出生日期:",
          key: "birthDate",
        },
        {
          name: "年龄:",
          key: "age",
        },
        {
          name: "籍贯:",
          key: "nativePlace",
        },
        // {
        //   name: "民族:",
        //   key: "nation",
        // },
        {
          name: "性别:",
          key: "sexName",
        },
        {
          name: "手机号:",
          key: "phone",
        },
        {
          name: "办公电话:",
          key: "phoneOffice",
        },
        {
          name: "职工工号:",
          key: "staffNum",
        },
        // {
        //   name: "文化程度:",
        //   key: "educationName",
        // },
        // {
        //   name: "角色权限:",
        //   key: "roleName",
        // },
      ],
      educationName: '',//文化程度名称
      detailData: {},
      photoList: [],
      signList: [],
      sealList: [],
    };
  },
  mounted() {
    this.getStaffByIdFn()
  },
  methods: {
    //  根据单位ID获取单位信息详情
    getStaffByIdFn() {
      this.$api.supplierAssess.getUserInfoById({
        staffId: this.$route.query.staffId,
      })
        .then((res) => {
          if (res.code == 200) {
            this.detailData = res.data;
            this.detailData.sexName = res.data.sex === 0 ? '女' : res.data.sex === 1 ? '男' : ''
            if (this.detailData.birthDate) {
              let birthDate = new Date(this.detailData.birthDate);
              this.detailData.age = this.getAge(birthDate)
            }
            this.detailData.avatar = this.$tools.imgUrlTranslation(res.data.avatar)
            res.data.avatar
              ? this.photoList = [
                {
                  name: "",
                  url: this.$tools.imgUrlTranslation(res.data.avatar)
                }
              ]
              : (this.photoList = []);
            if (res.data.education) {
              this.getEducationName(res.data.education)
            }
            // res.data.signUrl
            //   ? this.signList = [
            //     {
            //       name: "",
            //       url: this.$tools.imgUrlTranslation(res.data.signUrl)
            //     }
            //   ]
            //   : (this.signList = []);
            // res.data.sealUrl
            //   ? this.sealList = [
            //     {
            //       name: "",
            //       url: this.$tools.imgUrlTranslation(res.data.sealUrl)
            //     }
            //   ]
            //   : (this.sealList = []);
          }
        });
    },
    //获取学历名称
    getEducationName(id) {
      // 文化程度
      this.$api.supplierAssess.getDictData({
        dictionaryCategoryId: 'EDUCATION_LEVEL_CATEGORY'
      }).then((res) => {
        if (res.code == 200) {
          let arr = res.data[0].children
          this.educationName = arr.find((ele) => ele.id == id).name
        }
      });
    },
    getAge(birthDate) {
      // 创建一个 Date 对象表示当前日期
      const currentDate = new Date();
      // 获取当前年份
      const currentYear = currentDate.getFullYear();
      // 获取当前月份
      const currentMonth = currentDate.getMonth();
      // 获取当前日期
      const currentDay = currentDate.getDate();

      // 获取出生年份
      const birthYear = birthDate.getFullYear();
      // 获取出生月份
      const birthMonth = birthDate.getMonth();
      // 获取出生日期
      const birthDay = birthDate.getDate();

      // 计算年龄
      let age = currentYear - birthYear;

      // 检查生日是否已过
      if (currentMonth < birthMonth || (currentMonth === birthMonth && currentDay < birthDay)) {
        age--;
      }

      return age;
    }
  },

};
</script>

<style lang="scss" scoped>
.detaiBox {
  display: flex;
  flex-wrap: wrap;
  font-size: 14px;
}
.list-label {
  color: #7f848c;
  width: 120px;
  text-align: right;
}
.list-item {
  width: calc(100% / 3) !important;
  margin-bottom: 24px;
  display: flex;
}

.list-value {
  flex: 1;
  color: #333333;
  font-weight: 400;
  margin-left: 10px;
  white-space: nowrap; /* 不换行 */
  overflow: hidden; /* 隐藏溢出部分 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的内容 */
}
.detailImage {
  display: flex;
  flex-wrap: wrap;
  .list-image {
    width: calc(100% / 3) !important;
    display: flex;
    .list-label {
      margin-right: 8px;
    }
  }
}
</style>
