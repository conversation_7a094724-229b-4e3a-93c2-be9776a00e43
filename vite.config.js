/*
 * @Author: hedd
 * @Date: 2023-02-24 11:05:37
 * @LastEditTime: 2024-04-11 17:47:50
 * @FilePath: \ihcrs_pc\vite.config.js
 */
// vite.config.js
import { defineConfig, loadEnv } from 'vite'
import path from 'node:path'
import fs from 'fs'
import pkg from './package.json'
import dayjs from 'dayjs'
import createVitePlugins from './vitePlugins/index.js'

export default async ({ mode, command }) => {
  // 加载环境变量，因为 vite 中不会加载以 VUE 开头的，我们得自己指定下
  const envPrefix = ['VUE']
  const env = loadEnv(mode, process.cwd(), envPrefix)
  // 全局 scss 资源
  const scssResources = []
  fs.readdirSync('src/assets/styles/resources').forEach((dirname) => {
    if (fs.statSync(`src/assets/styles/resources/${dirname}`).isFile()) {
      scssResources.push(`@use "src/assets/styles/resources/${dirname}" as *;`)
    }
  })
  // css 精灵图相关
  fs.readdirSync('src/assets/sprites').forEach((dirname) => {
    if (fs.statSync(`src/assets/sprites/${dirname}`).isDirectory()) {
      // css 精灵图生成的 scss 文件也需要放入全局 scss 资源
      scssResources.push(`@use "src/assets/sprites/_${dirname}.scss" as *;`)
    }
  })
  let define = {
    'process.env.VITE': true,
    __SYSTEM_INFO__: JSON.stringify({
      pkg: {
        version: pkg.version,
        dependencies: pkg.dependencies,
        devDependencies: pkg.devDependencies
      },
      lastBuildTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
    })
  }
  for (const [key, value] of Object.entries(env)) {
    define[`process.env.${key}`] = `"${value}"`
  }
  return defineConfig({
    base: process.env.NODE_ENV == 'production' ? env.VUE_APP_BASE_URL_SUFFIX : '/',
    // 开发服务器选项 https://cn.vitejs.dev/config/#server-options
    server: {
      open: true,
      port: 9000
      // proxy: {
      //   '/sinomis-sporadic/': {
      //     target: 'http://192.168.10.35:9310',
      //     // target: 'http://192.168.10.165:9310',
      //     origin: true,
      //     rewrite: (path) => path.replace(/^\/sinomis-sporadic\//, '')
      //   }
      // }
    },
    define: define,
    build: {
      outDir: mode === 'production' ? 'dist' : `dist-${mode}`,
      sourcemap: false,
      commonjsOptions: {
        requireReturnsDefault: 'preferred'
      }
    },
    // terser打包速度过慢采用esbuild
    esbuild: {
      pure: ['console.log'], // 删除 console.log
      drop: ['debugger'] // 删除 debugger
    },
    plugins: createVitePlugins(env, command === 'build'),
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
        '~@': path.resolve(__dirname, 'src'),
        '@components': path.resolve(__dirname, 'src/components'),
        vue: 'vue/dist/vue.esm.js' // 解决el-table 生产环境不显示
      },
      // 忽略后缀名
      extensions: ['.js', '.jsx', '.ts', '.tsx', '.json', '.vue', '.json', '.css', '.scss']
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: scssResources.join(''),
          quietDeps: true,
          silenceDeprecations: ['legacy-js-api']
        }
      }
    }
  })
}
