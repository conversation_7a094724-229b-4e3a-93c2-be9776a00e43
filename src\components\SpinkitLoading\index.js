/*
 * @Author: hedd
 * @Date: 2023-03-23 10:12:01
 * @LastEditTime: 2023-07-11 10:12:08
 * @FilePath: \ihcrs_pc\src\components\SpinkitLoading\index.js
 * @Description:
 */
import Vue from 'vue'
let component

const componentsContext = import.meta.globEager('./main.vue')
for (const file_name in componentsContext) {
  // 获取文件中的 default 模块
  component = componentsContext[file_name].default
}

const constructor = Vue.extend(component)

let instance

const spinkitLoading = (options) => {
  options = options || {}
  instance = new constructor({
    data: options
  })
  instance.vm = instance.$mount()
  instance.dom = instance.vm.$el
  document.body.appendChild(instance.dom)
  instance.show = true
  return instance.vm
}

const SpinkitLoadingClose = () => {
  instance.show = false
}

export default {
  install: (Vue) => {
    Vue.prototype[`$${component.name}`] = spinkitLoading
    Vue.prototype[`$${component.name}`].close = SpinkitLoadingClose
  }
}
