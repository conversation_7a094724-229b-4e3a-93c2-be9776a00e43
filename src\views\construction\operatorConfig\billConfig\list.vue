<template>
  <div class="bill-config">
    <div class="bill-config__top">
      <el-form ref="formRef" :model="searchForm" class="bill-config__search" inline @submit.native.prevent="onSearch">
        <el-form-item prop="name">
          <el-input v-model="searchForm.name" clearable filterable placeholder="单据名称"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" plain @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
      <div class="bill-config__actions">
        <el-button type="primary" icon="el-icon-plus" @click="onOperate('add')">新增单据 </el-button>
      </div>
    </div>
    <div class="bill-config__table">
      <el-table v-loading="loadingStatus" height="100%" :data="tableData" border stripe table-layout="auto" class="tableAuto" row-key="id">
        <el-table-column label="序号" type="index" width="55" />
        <el-table-column label="单据名称" prop="name" show-overflow-tooltip></el-table-column>
        <el-table-column label="编码" prop="code" show-overflow-tooltip></el-table-column>
        <el-table-column label="单据类型" prop="documentType" width="120px" :formatter="documentTypeFormatter"></el-table-column>
        <el-table-column label="启用/停用" prop="state" width="120px" :formatter="(row) => (row.state == '0' ? '停用' : '启用')"></el-table-column>
        <el-table-column label="修改时间" prop="updateTime" width="250px">
          <template #default="scope">
            <span>{{ dayjs(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作人" prop="updateName" width="150px"></el-table-column>
        <el-table-column label="操作" width="150px">
          <template #default="{ row }">
            <el-button type="text" @click="onOperate('view', row)">查看</el-button>
            <el-button type="text" @click="onOperate('edit', row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-pagination
      class="bill-config__pagination"
      :page-current="pagination.page"
      :page-sizes="[15, 20, 30, 50]"
      :page-size="pagination.pageSize"
      layout="total, sizes, ->, prev, pager, next, jumper"
      :total="pagination.total"
      @size-change="paginationSizeChange"
      @current-change="paginationCurrentChange"
    >
    </el-pagination>
    <BillEdit v-if="addVisible" v-model="addVisible" :detail="detail" @success="getDataList" />
  </div>
</template>
<script>
import dayjs from 'dayjs'
export default {
  name: 'BillConfig',
  components: {
    BillEdit: () => import('./add.vue')
  },
  data() {
    return {
      searchForm: {
        name: ''
      },
      tableData: [],
      loadingStatus: false,
      dialog: {
        show: false,
        editData: null,
        readonly: false
      },
      pagination: {
        page: 1,
        pageSize: 15,
        total: 0
      },
      addVisible: false,
      detail: null
    }
  },
  mounted() {
    this.getDataList()
  },
  methods: {
    dayjs,
    getDataList() {
      this.loadingStatus = true
      const params = {
        name: this.searchForm.name,
        ...this.pagination
      }
      this.$api
        .receiptList(params)
        .then((res) => {
          if (res.code === '200') {
            this.tableData = res.data.records
            this.pagination.total = res.data.total
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '查询失败'))
        .finally(() => (this.loadingStatus = false))
    },
    paginationSizeChange(val) {
      this.pagination.page = 1
      this.pagination.pageSize = val
      this.getDataList()
    },
    paginationCurrentChange(val) {
      this.pagination.page = val
      this.getDataList()
    },
    onSearch() {
      this.getDataList()
    },
    onReset() {
      this.pagination.page = 1
      this.searchForm.name = ''
      this.$refs.formRef.resetFields()
      this.onSearch()
    },
    onOperate(type, row) {
      switch (type) {
        case 'add':
        case 'edit':
          this.detail = row || null
          this.addVisible = true
          break
        case 'view':
          // 预览单据模板，当前阶段使用链接的形式打开
          window.open(row.viewUrl)
          break
        default:
          // this.dialog.show = true
          break
      }
    },
    // 删除单据
    doDelete(id) {
      this.loadingStatus = true
      this.$api.SporadicProject.deleteProjectDocumentConfigById({ id })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('删除成功')
            this.getDataList()
          } else {
            throw res.message
          }
        })
        .catch((msg) => msg && this.$message.error(msg))
        .finally(() => (this.loadingStatus = false))
    },
    // 单据类型格式化
    documentTypeFormatter(row) {
      const { documentType } = row
      let arr = {
        0: '作业',
        1: '作业证'
      }
      return arr[documentType + '']
    }
  }
}
</script>
<style scoped lang="scss">
.bill-config {
  display: flex;
  flex-flow: column nowrap;
  padding: 16px;
  &__top {
    display: flex;
    justify-content: space-between;
  }
  &__actions {
    line-height: 40px;
  }
  &__pagination {
    margin-top: 10px;
  }
  &__table {
    margin-top: -10px;
    flex: 1;
  }
  &__tag {
    // 停用
    &--0 {
      --color: #f64646;
    }
    // 启用
    &--1 {
      --color: #00b42a;
    }
    &:before {
      content: '';
      display: inline-block;
      vertical-align: middle;
      margin-right: 4px;
      height: 4px;
      width: 4px;
      border-radius: 4px;
      background-color: var(--color);
    }
  }
}
</style>
