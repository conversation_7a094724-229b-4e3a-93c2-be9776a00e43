<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-06-05 11:05:50
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-08-03 14:31:34
 * @FilePath: \ihcrs_pc\src\views\alarmCenter\components\alarmTableDialog\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    :title="dialogTitle"
    width="60%"
    append-to-body
    :visible.sync="visible"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <div class="dialog-content">
      <div class="search-from">
        <el-select v-model="searchForm.projectCode" placeholder="报警来源" clearable @change="getEntityTypeList">
          <el-option v-for="item in alarmSourceOptions" :key="item.projectCode" :label="item.projectName" :value="item.projectCode"> </el-option>
        </el-select>
        <el-select v-model="searchForm.entityTypeId" class="style-interval" filterable clearable placeholder="请选择设备类型" @change="selectIncidentParam()">
          <el-option v-for="item in entityTypeList" :key="item.entityTypeId" :label="item.entityTypeName" :value="item.entityTypeId"> </el-option>
        </el-select>
        <el-select v-model="searchForm.incidentType" class="style-interval" filterable clearable placeholder="请选择事件类型" @change="getDataList()">
          <el-option v-for="item in incidentList" :key="item.incidentParam" :label="item.incidentName" :value="item.incidentParam"> </el-option>
        </el-select>
        <el-input v-model="searchForm.objectName" placeholder="报警对象名称" style="width: 200px;"></el-input>
        <el-button type="primary" plain @click="resetForm">重置</el-button>
        <el-button type="primary" @click="searchFormEvent">查询</el-button>
      </div>
      <div class="wholeTable">
        <div class="wholeTable-main table-content">
          <el-table ref="table" v-loading="tableLoading" :resizable="false" border :data="tableData" height="calc(100%)" style="width: 100%;">
            <el-table-column prop="serialNumber" label="序号" align="center" width="50">
              <template slot-scope="scope">{{ (pagination.current - 1) * pagination.size + scope.$index + 1 }}</template>
            </el-table-column>
            <el-table-column prop="alarmStartTime" label="报警时间" width="170">
              <template slot-scope="scope">{{ scope.row.alarmStartTime }}</template>
            </el-table-column>
            <el-table-column prop="alarmObjectName" label="报警对象" show-overflow-tooltip></el-table-column>
            <el-table-column prop="alarmLevel" label="报警等级" align="center" width="100">
              <span slot-scope="scope" class="alarmLevel" :style="{ background: alarmLevelItem[scope.row.alarmLevel].color }">
                {{ alarmLevelItem[scope.row.alarmLevel].text }}
              </span>
            </el-table-column>
            <el-table-column prop="incidentName" label="事件类型" show-overflow-tooltip></el-table-column>
            <el-table-column prop="alarmSource" label="报警来源" show-overflow-tooltip></el-table-column>
            <el-table-column prop="alarmEntityTypeName" label="设备类型" show-overflow-tooltip></el-table-column>
            <el-table-column prop="alarmSpaceName" label="位置" show-overflow-tooltip></el-table-column>
            <el-table-column prop="alarmStatus" label="报警处理状态" align="center" width="120">
              <div slot-scope="scope" class="alarmStatus" :style="{ color: scope.row.alarmStatus == 0 ? '#FA403C' : scope.row.alarmStatus == 1 ? '#fd9434' : '#999' }">
                <span class="alarmStatusIcon" :style="{ background: scope.row.alarmStatus == 0 ? '#FA403C' : scope.row.alarmStatus == 1 ? '#ff9435' : '#999' }"></span>
                {{ scope.row.alarmStatus == 0 ? '未处理' : scope.row.alarmStatus == 1 ? '处理中' : '已关闭' }}
              </div>
            </el-table-column>
          </el-table>
        </div>
        <div class="wholeTable-footer">
          <el-pagination
            :current-page="pagination.current"
            :page-sizes="pagination.pageSizeOptions"
            :page-size="pagination.size"
            :layout="pagination.layoutOptions"
            :total="pagination.total"
            @size-change="paginationSizeChange"
            @current-change="paginationCurrentChange"
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button plain type="primary" @click="closeDialog">关 闭</el-button>
    </span>
  </el-dialog>
</template>
<script>
import moment from 'moment'
import tableListMixin from '@/mixins/tableListMixin.js'
export default {
  name: 'alarmTableDialog',
  mixins: [tableListMixin],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    dateType: {
      type: String,
      default: '0'
    }
  },
  data() {
    return {
      dateTypeList: {
        0: {
          label: '本日',
          timeRange: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
        },
        1: {
          label: '本周',
          timeRange: [moment().weekday(1).format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
        },
        2: {
          label: '本月',
          timeRange: [moment().format('YYYY-MM') + '-01', moment().format('YYYY-MM-DD')]
        },
        3: {
          label: '本年',
          timeRange: [moment().format('YYYY') + '-01-01', moment().format('YYYY-MM-DD')]
        }
      },
      alarmSourceOptions: [], // 报警来源
      entityTypeList: [], // 设备类型
      incidentList: [], // 事件类型
      searchForm: {
        // 搜索条件
        projectCode: '', // 报警来源
        entityTypeId: '', // 设备类型
        incidentType: '', // 事件类型
        objectName: '',
        dataRange: [] // 时间范围
      },
      loading: false,
      tableLoading: false,
      alarmLevelItem: {
        0: { text: '通知', color: '#3562DB' },
        1: { text: '一般', color: '#3562DB' },
        2: { text: '紧急', color: '#FF9435' },
        3: { text: '重要', color: '#FA403C' }
      },
      alarmAffirmItem: { 0: '未确认', 1: '真实报警', 2: '误报', 3: '演练', 4: '调试' },
      tableData: [],
      timeOrType: '' // 排序 0.按紧急降序  1.按紧急升序  2.按时间降序  3.按时间升序
    }
  },
  computed: {
    dialogTitle() {
      return this.dateTypeList[this.dateType].label + '新增报警清单'
    }
  },
  mounted() {
    this.searchForm.dataRange = this.dateTypeList[this.dateType].timeRange
    console.log(this.searchForm.dataRange)
    this.getAlarmSource()
    this.getDataList()
  },
  methods: {
    // 获取报警来源
    getAlarmSource() {
      this.$api.getSourceByEmpty().then((res) => {
        if (res.code == 200) {
          this.alarmSourceOptions = res.data
        }
      })
    },
    // 获取设备类型
    getEntityTypeList() {
      const params = {
        projectCode: this.searchForm.projectCode
      }
      this.$api.selectEntityType(params).then((res) => {
        if (res.code == 200) {
          this.entityTypeList = res.data || []
          this.searchForm.entityTypeId = ''
          this.selectIncidentParam()
        }
      })
    },
    // 获取事件类型
    selectIncidentParam() {
      const params = {
        projectCode: this.searchForm.projectCode,
        entityTypeId: this.searchForm.entityTypeId
      }
      this.$api.selectIncidentParam(params).then((res) => {
        this.incidentList = res.data
        this.pagination.current = 1
        this.getDataList()
      })
    },
    // selectAlarmList()
    // 初始化组件数据
    getDataList() {
      let { projectCode, incidentType, entityTypeId, objectName, dataRange } = this.searchForm
      console.log(this.searchForm.dataRange, dataRange)
      let params = {
        pageNo: this.pagination.current,
        pageSize: this.pagination.size,
        projectCode,
        entityTypeId,
        incidentType,
        objectName,
        startTime: dataRange[0],
        endTime: dataRange[1]
      }
      this.tableLoading = true
      this.$api
        .selectAlarmList(params)
        .then((res) => {
          this.tableLoading = false
          if (res.code == 200) {
            this.tableData = res.data ? res.data.records : []
            this.pagination.total = res.data ? res.data.total : 0
          } else {
            this.tableData = []
            this.pagination.total = 0
          }
        })
        .catch((err) => {
          this.loading = false
        })
    },
    searchFormEvent() {
      this.pagination.current = 1
      this.getDataList()
    },
    // 重置查询
    resetForm() {
      Object.assign(this.searchForm, {
        projectCode: '', // 报警来源
        entityTypeId: '', // 设备类型
        incidentType: '', // 事件类型
        objectName: ''
      })
      this.pagination.current = 1
      this.getDataList()
    },
    closeDialog() {
      this.$emit('update:visible', !this.visible)
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .model-dialog {
  margin-top: 5vh !important;
}

::v-deep .el-dialog__body {
  max-height: calc(88vh - 110px);
  height: calc(88vh - 110px);
}

.dialog-content {
  background: #fff;
  display: flex;
  flex-direction: column;
  width: 100%;

  .search-from {
    padding: 5px 15px;

    & > div {
      margin-top: 12px;
      margin-right: 10px;
    }
  }

  .wholeTable {
    // height: calc(100% - 16px);
    flex: 1;
    background: #fff;
    border-radius: 4px;
    padding: 0 16px;
    display: flex;
    margin-top: 16px;
    flex-direction: column;
    overflow: auto;

    .wholeTable-main {
      flex: 1;
      overflow: auto;
    }

    .wholeTable-footer {
      padding: 10px 0;
    }

    .alarmLevel {
      padding: 3px 6px;
      border-radius: 4px;
      color: #fff;
      line-height: 14px;
    }

    .alarmStatus {
      position: relative;
      display: inline-block;
      padding-left: 12px;

      .alarmStatusIcon {
        display: inline-block;
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 8px;
        height: 8px;
        border-radius: 100%;
      }
    }

    .collectIcon {
      font-size: 16px;
      margin-right: 4px;
    }
  }
}
</style>
