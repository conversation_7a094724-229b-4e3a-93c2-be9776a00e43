<template>
  <div :id="randomId" :ref="randomId" class="option-wrap">
    <div class="virtual-dom">
      <!-- 使用虚拟列表渲染el-option组件 -->
      <el-option
        v-for="(item, index) in virtualOptions"
        :key="`virtual_options_${item[value]}_${index}`"
        :disabled="item.disabled"
        :label="item[label]"
        :value="item[value]"
      ></el-option>
    </div>
  </div>
</template>
<script>
export default {
  name: 'YxOption',
  props: {
    virtualData: {
      type: Array,
      default: () => []
    },
    selectValue: {
      type: [String, Number, Array],
      default: ''
    },
    label: {
      type: String
    },
    value: {
      type: String
    },
    allowCreate: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      randomId: '', // 随机生成的ID值
      virtualOptions: [], // 虚拟列表的选项数据
      leafNumber: 1, // 选项的子节点数量
      optionHeight: 34, // 选项的高度
      scrollFunc: null,
      isShow: false,
      root: null
    }
  },
  watch: {
    selectValue: {
      handler(val) {
        if (Array.isArray(val) && !this.isShow) {
          this.showMultipleLabel() // 用于回显 多选
        } else if (!Array.isArray(val)) {
          this.resetVirtual() // 用于回显 单选
        }
      },
      deep: true,
      immediate: true
    },
    // 监听virtualData的变化，更新虚拟列表总高度 和 数据
    virtualData() {
      this.resetVirtual()
      this.initVirtual('init')
    }
  },
  mounted() {
    this.initId()
    this.$nextTick(() => this.initVirtual())
  },
  beforeDestroy() {
    const $wrap = this.$refs[this.randomId]
    if (!$wrap) return
    const $scroll = this.getParents($wrap, 'el-select-dropdown__wrap')
    if ($scroll) $scroll.removeEventListener('scroll', this.scrollFunc) // 清除监听
  },
  methods: {
    // 生成一个随机的id值
    initId() {
      this.randomId = 'virtual_' + parseInt(Math.random() * 10 * 1024 * 1024 + '')
    },
    /**
     * 工具 - 根据class名称递归查询父节点
     * @param {HTMLElement} element 当前节点element
     * @param {string} className 需要查询的class值
     */
    getParents(element, className) {
      var returnParentElement = null
      function getpNode(element, className) {
        if (!element.parentNode) return null
        // 创建父级节点的类数组
        let pClassList = element.parentNode.getAttribute('class').split(' ')
        let pNode = element.parentNode
        if (!pClassList || !pClassList.length) {
          // 如果未找到类名数组，表示父类无类名，则再次递归
          getpNode(pNode, className)
        } else if (pClassList && !pClassList.includes(className)) {
          // 如果父类的类名中没有预期类名，则再次递归
          getpNode(pNode, className)
        } else if (pClassList && pClassList.includes(className)) {
          returnParentElement = pNode
        }
      }
      getpNode(element, className)
      return returnParentElement
    },
    // 处理多选回显
    showMultipleLabel() {
      const vIndex = this.virtualData.reduce((arr, item, index) => {
        if (this.selectValue.includes(item[this.value])) {
          arr.push(index)
        }
        return arr
      }, [])
      this.virtualOptions = this.virtualData.filter((_, index) => vIndex.includes(index))
    },
    /**
     * 重置虚拟列表，使用场景有两个
     * 1- select选中时，保证选中数据正常为label值
     * 2- 下拉框展开保证下拉菜单回显正常
     */
    resetVirtual() {
      const $wrap = document.getElementById(this.randomId)
      if (!$wrap) return
      const $virtualDom = $wrap.querySelector('.virtual-dom')
      const $scroll = this.getParents($wrap, 'el-select-dropdown__wrap')
      const _scrollHeight = $scroll.offsetHeight
      if (this.allowCreate) this.root = $wrap.parentNode
      $scroll.style.maxHeight = '290px'
      let vIndex = 0
      const isArray = Array.isArray(this.selectValue)
      const isSelectValue = isArray ? !!this.selectValue.length : String(this.selectValue) !== ''
      if (isSelectValue) {
        // 查找选中值在虚拟数据中的索引
        vIndex = this.virtualData.findIndex((item) => item[this.value] === (isArray ? this.selectValue[0] : this.selectValue))
      }
      // 计算可视区域内需要显示的选项数量
      const showNumber = parseInt(_scrollHeight / this.optionHeight) + this.leafNumber
      vIndex = vIndex >= 8 ? vIndex : 0 // 解决有值时初始化数据不足时渲染不完整问题
      this.handleRender($virtualDom, $scroll, vIndex, showNumber)
    },
    /**
     * 渲染虚拟列表
     * @param {HTMLElement} $virtualDom 虚拟列表的盒子dom元素
     * @param {HTMLElement} $scroll 滚动容器
     * @param {number} vIndex 当前显示节点开始的索引
     * @param {number} showNumber 显示的数量
     * @param {number} _scrollTop 滚动条高度（用于矫正开始和结束位置偏移）
     **/
    handleRender($virtualDom, $scroll, vIndex, showNumber, _scrollTop) {
      // 使用 requestAnimationFrame 充分利用了显示器的刷新机制，比较节省系统资源
      requestAnimationFrame(() => {
        // 直接计算当前所滚动到位置的数据
        this.virtualOptions = this.virtualData.slice(vIndex, vIndex + showNumber)
        // 计算偏移量
        const y = $scroll ? vIndex * this.optionHeight : _scrollTop
        // 解决可自定义新增节点时 列表顶部出现占位空白情况
        if (this.allowCreate) {
          const firstChild = this.root && this.root.childNodes[0]
          if (firstChild instanceof HTMLLIElement) {
            firstChild.style.display = y > 0 ? 'none' : 'block'
          }
        }
        $virtualDom.style.transform = `translate(0, ${y}px)`
        // 设置滚动高度
        $scroll && ($scroll.scrollTop = y)
      })
    },
    // 初始化虚拟列表
    initVirtual(type) {
      const $wrap = document.getElementById(this.randomId)
      if (!$wrap) return
      const $virtualDom = $wrap.querySelector('.virtual-dom')
      $wrap.style.height = this.virtualData.length * this.optionHeight + 'px'
      // 获取滚动容器
      const $scroll = this.getParents($wrap, 'el-select-dropdown__wrap')
      $scroll.removeEventListener('scroll', this.scrollFunc) // 清除滚动监听
      this.scrollFunc = (type) => {
        const _scrollHeight = $scroll.offsetHeight || 290 // 解决筛选无数据时，高度为0的情况
        const _scrollTop = $scroll.scrollTop
        const showNumber = parseInt(_scrollHeight / this.optionHeight) + this.leafNumber
        // 当前应该展示的数据索引
        let vIndex = parseInt(_scrollTop / this.optionHeight)
        if (type === 'init') vIndex = vIndex >= 8 ? vIndex : 0 // 解决筛选空结果时，列表空白问题
        this.handleRender($virtualDom, null, vIndex, showNumber, _scrollTop)
      }
      this.$nextTick(() => {
        this.scrollFunc(type)
        $scroll.addEventListener('scroll', this.scrollFunc) // 添加滚动监听
      })
    }
  }
}
</script>
