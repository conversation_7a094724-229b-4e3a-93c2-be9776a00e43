<template>
   <PageContainer>
    <div slot="content">
      <el-tabs v-model="activeName">
        <el-tab-pane label="指标周期设置" name="0">
          <CycleRules v-if="activeName == '0'" :type="activeName"></CycleRules>
        </el-tab-pane>
        <el-tab-pane label="指标等级设置" name="1">
          <LevelRules v-if="activeName == '1'" :type="activeName"></LevelRules>
        </el-tab-pane>
      </el-tabs>
    </div>
  </PageContainer>
</template>
<script>
import CycleRules from './cycleRules/list'
import LevelRules from './levelRules/list'
export default {
  name: 'targetConfig',
  components: {
    CycleRules,
    LevelRules
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      if (from.query && from.query.type) {
        vm.activeName = from.query.type
      }

    })
  },
  data() {
    return {
      activeName: '0'
    }
  }
}
</script>
<style lang="scss" scoped>
.container-content > div {
  width: 100%;
  height: 100%;
  background-color: #fff;
  padding: 16px;
}
.el-tabs {
  height: 100%;
}

::v-deep .el-tabs__content {
  height: calc(100% - 40px);
}

::v-deep .el-tab-pane {
  height: 100%;
}
</style>
