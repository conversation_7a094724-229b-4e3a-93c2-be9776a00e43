<template>
  <PageContainer>
    <div slot="header" class="control-btn-header">
      <div class="search-from">
        <el-input v-model.trim="searchForm.queryCriteria" placeholder="单位名称" clearable style="width: 200px"></el-input>
        <el-select v-model="searchForm.enableStatus" placeholder="请选择状态" class="ml-16" clearable>
          <el-option v-for="item in typeList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
        </el-select>
        <div style="display: inline-block">
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="search">查询</el-button>
        </div>
      </div>
    </div>
    <div slot="content" class="table-content">
      <div class="btn-group">
        <el-button type="primary" @click="handleListEvent('add')">新增</el-button>
        <el-button type="primary" plain @click="handleListEvent('import')">导入</el-button>
      </div>
      <TablePage
        ref="table"
        v-loading="tableLoading"
        :showPage="true"
        :border="true"
        :tableColumn="tableColumn"
        :data="tableData"
        height="calc(100% - 40px - 50px)"
        :pageData="pageData"
        @pagination="paginationChange"
      >
        <template #businessImage="{ row }">
          <span v-if="!row.businessImage">未上传</span>
          <template v-else>
            <img v-for="img in businessImages(row.businessImage)" :key="img" :src="img" style="width: 50px; height: 50px" @click="showPic(img)" />
          </template>
        </template>
        <template #enableStatus="{ row }">
          <span>{{ row.enableStatus == 0 ? '启用' : '禁用' }}</span>
        </template>
        <template slot="operation" slot-scope="scope">
          <el-button type="text" @click="handleListEvent('view', scope.row)">查看</el-button>
          <el-button type="text" @click="handleListEvent('edit', scope.row)">编辑</el-button>
          <el-button type="text" style="color: #fa403c" @click="handleListEvent('del', scope.row)">删除</el-button>
        </template>
      </TablePage>
      <Preview v-if="viewVisible" v-model="viewVisible" :list="fileList" />
      <AddUnit v-if="addVisible" :id="detailId" v-model="addVisible" @success="addSuccess" />
      <ImportFile v-if="importVisible" v-model="importVisible" url="constructionUnitController/importCompany" @success="getList" />
    </div>
  </PageContainer>
</template>
<script lang="jsx">
export default {
  name: 'unitManagement',
  components: {
    Preview: () => import('../components/preview.vue'),
    AddUnit: () => import('./add.vue'),
    ImportFile: () => import('../components/importFile.vue')
  },
  data() {
    return {
      tableLoading: false,
      typeList: [
        {
          id: '0',
          name: '启用'
        },
        {
          id: '1',
          name: '禁用'
        }
      ],
      searchForm: {
        queryCriteria: '',
        enableStatus: ''
      },
      tableColumn: [
        {
          prop: '',
          label: '序号',
          formatter: (scope) => {
            return (this.pageData.page - 1) * this.pageData.pageSize + scope.$index + 1
          },
          width: 80
        },
        {
          prop: 'companyName',
          label: '单位名称',
          required: true
        },
        {
          prop: 'creditCode',
          label: '信用代码'
        },
        {
          prop: 'businessImage',
          label: '营业执照',
          slot: 'businessImage'
        },
        {
          prop: 'legalPersonName',
          label: '法人姓名'
        },
        {
          prop: 'enableStatus',
          label: '启用状态',
          slot: 'enableStatus'
        },
        {
          prop: 'remark',
          label: '备注'
        },
        {
          prop: 'operation',
          label: '操作',
          slot: 'operation'
        }
      ],
      tableData: [],
      pageData: {
        page: 1,
        pageSize: 15,
        total: 0
      },
      viewVisible: false,
      addVisible: false,
      detailId: '',
      importVisible: false
    }
  },
  watch: {},
  mounted() {
    this.getList()
  },
  methods: {
    handleListEvent(type, row) {
      switch (type) {
        case 'add':
          this.addVisible = true
          this.detailId = row ? row.id : ''
          break
        case 'edit':
        case 'view':
          this.$router.push({
            name: 'unitDetail',
            query: {
              id: row.id,
              handType: type
            }
          })
          break
        case 'del':
          this.$confirm('确定删除该数据吗？', '提示', {
            cancelButtonClass: 'el-button--primary is-plain',
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.$api.delConstructionUnit({ id: row.id }).then((res) => {
              if (res.code == 200) {
                this.$message.success('删除成功')
                this.getList()
              }
            })
          })
          break
        case 'import':
          this.importVisible = true
          break
        default:
          break
      }
    },
    // 查询
    search() {
      this.pageData.page = 1
      this.getList()
    },
    // 重置查询
    resetForm() {
      this.searchForm = {
        queryCriteria: '',
        enableStatus: ''
      }
      this.pageData.page = 1
      this.search()
    },
    businessImages(imgs) {
      const imgArr = imgs.split(',')
      return imgArr.map((e) => {
        return this.$tools.imgUrlTranslation(e)
      })
    },
    // 获取应用列表
    getList() {
      let param = {
        ...this.searchForm,
        pageSize: this.pageData.pageSize,
        currentPage: this.pageData.page
      }
      this.tableLoading = true
      this.$api
        .getConstructionUnitList(param)
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.records
            this.pageData.total = res.data.total
          }
          this.tableLoading = false
        })
        .catch(() => {
          this.tableLoading = false
        })
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getList()
    },
    showPic(img) {
      this.fileList = [img]
      this.viewVisible = true
    },
    addSuccess() {
      this.getList()
    }
  }
}
</script>
<style lang="scss" scoped>
.hide {
  ::v-deep .el-upload--picture-card {
    display: none;
  }
}
.control-btn-header {
  padding: 10px !important;
  .search-from {
    & > div {
      margin-right: 10px;
    }
  }
}
.table-content {
  margin-top: 15px;
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 15px);
  padding: 10px;
  .btn-group {
    height: 50px;
  }
}
</style>
