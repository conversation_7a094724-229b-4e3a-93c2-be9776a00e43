<script>
import { msgIconList } from '@/util/dict.js'
import { mapGetters } from 'vuex'
export default {
  name: 'ProjectTodo',
  props: {
    item: {
      type: Object,
      default: () => {
      }
    }
  },
  data() {
    return {
      tableData: [],
      loadingStatus: false,
      // 待办类型
      todoType: 'flow'
    }
  },
  mounted() {
    this.getTableData()
  },
  methods: {
    onTypeChange() {
      this.tableData = []
      this.getTableData()
    },
    getTableData() {
      const param = { type: this.todoType }
      this.tableData.push({
        code: '001-海-001',
        dataSource: '工程管理',
        flowName: '医用气体施工',
        receiveTime: '2024-09-29 13:23:22'
      })
    }
  }
}
</script>
<template>
  <ContentCard
    :title="item.componentTitle"
    :scrollbarHover="true"
    :cstyle="{ height: '100%' }"
    class="project-todo drag_class"
    :hasMoreOper="['more', 'edit']"
    @more-oper-event="(val) => $emit('all-more-Oper', val, 'todoItems')"
  >
    <template #content>
      <div class="project-todo__tab">
        <el-radio-group v-model="todoType" @change="onTypeChange">
          <el-radio-button label="flow">
            <div>待处理流程</div>
            <div class="project-todo__count">3</div>
          </el-radio-button>
          <el-radio-button label="question">
            <div>待处理问卷</div>
            <div class="project-todo__count">1</div>
          </el-radio-button>
        </el-radio-group>
      </div>
      <div class="project-todo__table">
        <el-table v-loading="loadingStatus" height="100%" size="mini" :data="tableData" border stripe table-layout="auto" row-key="id">
          <el-table-column label="编号" prop="code" show-overflow-tooltip></el-table-column>
          <el-table-column label="来源系统" prop="dataSource" show-overflow-tooltip></el-table-column>
          <el-table-column label="流程名称" prop="flowName" show-overflow-tooltip></el-table-column>
          <el-table-column label="接收时间" prop="receiveTime" show-overflow-tooltip></el-table-column>
          <el-table-column label="操作" width="85px">
            <template #default>
              <el-button type="text">查看</el-button>
              <el-button type="text" style="margin-left: 4px">处理</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </template>
  </ContentCard>
</template>
<style lang="scss" scoped>
.project-todo {
  ::v-deep(.el-radio-group) {
    .el-radio-button {
      margin-right: 20px;
      .el-radio-button__inner {
        text-align: left;
        border-radius: 4px;
        width: 180px;
      }
      &:not(.is-active) {
        .el-radio-button__inner {
          border-left: 1px solid #dcdfe6;
        }
      }
    }
  }
  &__count {
    margin-top: 8px;
    font-size: 24px;
  }
  &__table {
    margin-top: 16px;
    height: calc(100% - 88px);
  }
}
</style>
