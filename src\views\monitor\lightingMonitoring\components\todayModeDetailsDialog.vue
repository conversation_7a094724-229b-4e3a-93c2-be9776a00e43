<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    :title="todayPattern.patternName"
    width="40%"
    :visible.sync="visible"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <div class="content">
      <el-tabs v-model="tabsActive" class="heade-tabs" @tab-click="tabClick">
        <el-tab-pane v-for="item in tabsList" :key="item.value" :label="item.name" :name="item.value"></el-tab-pane>
      </el-tabs>
      <div style="height: 400px; padding: 16px">
        <TablePage ref="table" v-loading="tableLoading" :showPage="false" :tableColumn="tableColumn" :data="tableData" height="100%" />
      </div>
    </div>
  </el-dialog>
</template>
<script lang="jsx">
export default {
  name: 'todayModeDetails',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    projectCode: {
      type: String,
      default: ''
    },
    todayPattern: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      tabsActive: '',
      tableLoading: false,
      tableData: [],
      tabsList: [],
      tableColumn: [
        {
          prop: 'timePoint',
          label: '时间表'
        },
        {
          prop: 'switchControl',
          label: '控制策略',
          formatter: (scope) => {
            return scope.row.switchControl == 1 ? '开启' : '关闭'
          }
        },
        {
          prop: 'loopsNum',
          label: '回路数量'
        },
        {
          prop: 'planExecution',
          label: '计划执行'
        },
        {
          prop: 'practical',
          label: '实际执行'
        },
        {
          prop: 'offlineNum',
          label: '离线'
        }
        // {
        //   prop: 'lightingStatus',
        //   label: '开关状态',
        //   formatter: (scope) => {
        //     return scope.row.lightingStatus == 1 ? '开启' : '关闭'
        //   }
        // }
      ],
      initData: []
    }
  },
  mounted() {
    this.getTodayModeList()
  },
  methods: {
    getTodayModeList() {
      this.$api.getTodayModeList({ projectCode: this.projectCode, patternId: this.todayPattern.id }).then((res) => {
        if (res.code == '200') {
          let data = res.data
          this.initData = data
          data.forEach(item => {
            this.tabsList.push({
              value: item.id.toString(),
              name: item.timeTableName
            })
          })
          this.tabsActive = data[0]?.id.toString() ?? ''
          this.tableData = data[0].timeTableDataList
        }
      })
    },
    // tab切换
    tabClick(tab) {
      this.tableData = this.initData.find(item => item.id == tab.name).timeTableDataList
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', !this.visible)
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  // padding: 16px !important;
  :deep(.heade-tabs) {
    background: #fff;
    border-radius: 4px;

    .el-tabs__header .el-tabs__nav-wrap::after {
      display: none;
    }

    .el-tabs__active-bar {
      bottom: 1px;
    }

    .el-tabs__item {
      padding: 0 20px !important;
    }

    .el-tabs__content {
      display: none;
    }
  }
}
.model-dialog {
  padding: 0 !important;
}
::v-deep .el-dialog__body {
  padding: 0 !important;
}
</style>
