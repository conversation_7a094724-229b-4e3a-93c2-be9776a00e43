<script>
import { UsingStatusOptions } from '@/views/operationPort/constant'
import tableListMixin from '@/mixins/tableListMixin'
export default {
  name: 'BuildingGroupList',
  components: {},
  filters: {
    statusFilter(status) {
      return UsingStatusOptions.find((it) => it.value === status)?.label ?? '-'
    }
  },
  mixins: [tableListMixin],
  data: () => ({
    searchForm: {
      name: ''
    },
    tableData: [],
    loadingStatus: false,
    dialog: {
      show: false,
      id: ''
    }
  }),
  computed: {
    OperateType() {
      return {
        Edit: 'edit',
        View: 'view',
        Delete: 'delete',
        Create: 'create'
      }
    }
  },
  mounted() {
    this.getDataList()
  },
  methods: {
    getDataList() {
      this.loadingStatus = true
      Promise.resolve()
        .then(() => {
          this.tableData = [
            {
              id: 'G01',
              fullName: '门诊楼',
              simpleName: '门',
              code: 'PP',
              buildings: '门诊楼',
              status: 1,
              remark: 'Nothing'
            },
            {
              id: 'G02',
              fullName: '其他',
              simpleName: '其',
              code: 'PP',
              buildings: '东门门卫、西门门卫',
              status: 0,
              remark: 'Nothing'
            }
          ]
          this.pagination.total = this.tableData.length
        })
        .finally(() => (this.loadingStatus = false))
    },
    onSearch() {
      this.pagination.current = 1
      this.getDataList()
    },
    onReset() {
      this.$refs.formRef.resetFields()
      this.onSearch()
    },
    onOperate(row, type) {
      switch (type) {
        case this.OperateType.Delete:
          this.doDelete(row.id)
          break
        default:
          this.dialog.show = true
          this.dialog.id = row?.id ?? ''
          break
      }
    },
    doDelete(id) {
      this.$confirm('删除后将无法恢复，是否确定删除？', '信息提示', {
        type: 'warning'
      })
        .catch(() => Promise.reject())
        .then(() => true)
        .then(() => {
          this.$message.success('已删除')
          this.getDataList()
        })
        .catch((msg) => msg && this.$message.error(msg))
    }
  }
}
</script>
<template>
  <div class="building-group-list">
    <div class="building-group-list__top">
      <el-form ref="formRef" :model="searchForm" class="building-group-list__search" inline @submit.native.prevent="onSearch">
        <el-form-item prop="name">
          <el-input v-model="searchForm.name" clearable filterable placeholder="请输入分组名称"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" plain @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
      <div class="building-group-list__actions">
        <el-button type="primary" @click="onOperate(undefined, OperateType.Create)">新增</el-button>
      </div>
    </div>
    <div class="building-group-list__table">
      <el-table v-loading="loadingStatus" height="100%" :data="tableData" border stripe table-layout="auto" row-key="id">
        <el-table-column type="index" width="55" label="序号" />
        <el-table-column label="分组名称" prop="fullName" show-overflow-tooltip></el-table-column>
        <el-table-column label="简称" prop="simpleName" width="100px"></el-table-column>
        <el-table-column label="编码" prop="code" width="100px"></el-table-column>
        <el-table-column label="建筑" prop="buildings" show-overflow-tooltip></el-table-column>
        <el-table-column label="状态" prop="status" width="100px">
          <template #default="{ row }">
            <span class="building-group-list__tag" :class="`building-group-list__tag--${row.status}`">
              {{ row.status | statusFilter }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" show-overflow-tooltip></el-table-column>
        <el-table-column label=" 操作">
          <template #default="{ row }">
            <el-button type="text" @click="onOperate(row, OperateType.View)">查看</el-button>
            <el-button type="text" @click="onOperate(row, OperateType.Edit)">编辑</el-button>
            <el-button type="text" @click="onOperate(row, OperateType.Delete)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-pagination
      class="building-group-list__pagination"
      :current-page="pagination.page"
      :page-sizes="pagination.pageSizeOptions"
      :page-size="pagination.size"
      :layout="pagination.layoutOptions"
      :total="pagination.total"
      @size-change="paginationSizeChange"
      @current-change="paginationCurrentChange"
    >
    </el-pagination>
  </div>
</template>
<style scoped lang="scss">
.building-group-list {
  display: flex;
  flex-flow: column nowrap;
  padding: 16px;
  &__top {
    display: flex;
    justify-content: space-between;
  }
  &__table {
    margin-top: -10px;
    flex: 1;
  }
  &__actions {
    line-height: 40px;
  }
  &__pagination {
    margin-top: 10px;
  }
  &__tag {
    // 停用
    &--0 {
      --color: #f64646;
    }
    // 启用
    &--1 {
      --color: #00b42a;
    }
    &:before {
      content: '';
      display: inline-block;
      vertical-align: middle;
      margin-right: 4px;
      height: 4px;
      width: 4px;
      border-radius: 4px;
      background-color: var(--color);
    }
  }
}
</style>
