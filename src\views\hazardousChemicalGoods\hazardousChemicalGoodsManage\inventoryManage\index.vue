<script>
import tableListMixin from '@/mixins/tableListMixin'
export default {
  name: 'inventoryManage',
  mixins: [tableListMixin],
  data() {
    return {
      searchForm: {
        warehouseId: '', //仓库id
        checkId: '', //盘点人
        month: '', //年月
        inventoryTime: [] //盘点时间
      },
      warehouseOptions: [], //库房下拉
      inventoryPersonOptions: [], //盘点人下拉
      tableData: [],
      tableLoadingStatus: false
    }
  },
  mounted() {
    this.getPersonListFn()
    this.getWarehouseListFn()
    this.getDataList()
  },
  methods: {
    // 获取人员列表
    getPersonListFn() {
      let params = {
        current: 1,
        size: 9999
      }
      this.$api.staffList(params).then((res) => {
        if (res.code == 200) {
          this.inventoryPersonOptions = res.data.records
        }
      })
    },
    // 获取库房下拉
    getWarehouseListFn() {
      this.$api.getWarehouseList({ status: '0' }).then((res) => {
        if (res.code == '200') {
          this.warehouseOptions = res.data.list
        }
      })
    },
    // 获取列表数据
    getDataList() {
      this.tableData = []
      this.tableLoadingStatus = true
      const params = {
        size: this.pagination.size,
        current: this.pagination.current,
        ...this.searchForm
      }
      params.startTime = params.inventoryTime.length ? params.inventoryTime[0] + '  ' + '00:00:00' : ''
      params.endTime = params.inventoryTime.length ? params.inventoryTime[1] + ' ' + '23:59:59' : ''
      delete params.inventoryTime
      this.$api
        .queryTakeStockByPage(params)
        .then((res) => {
          this.tableLoadingStatus = false
          if (res.code === '200') {
            this.tableData = res.data.list
            this.pagination.total = res.data.total
          } else {
            this.tableData = []
            this.pagination.total = 0
          }
        })
        .catch((err) => {
          this.tableLoadingStatus = false
        })
    },
    // 表单搜索按钮点击
    onSearch() {
      this.pagination.current = 1
      this.getDataList()
    },
    // 表单重置按钮点击
    onReset() {
      this.$refs.formRef.resetFields()
      this.onSearch()
    },
    //操作
    onOperate(type, row) {
      if (type === 'add') {
        this.$router.push({
          name: 'inventoryAdd',
          query: {
            type: 'add'
          }
        })
      } else if (type === 'view') {
        this.$router.push({
          name: 'inventoryAdd',
          query: {
            type: 'view',
            inventoryId: row.id
          }
        })
      } else if (type === 'delete') {
        this.$confirm('确认删除选中的盘点数据吗?', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$api.deleteTakeStockById({ ids: row.id }).then((res) => {
            if (res.code == 200) {
              this.onSearch()
              this.$message({
                message: res.message,
                type: 'success'
              })
            } else {
              this.$message.error(res.msg)
            }
          })
        })
      }
    }
  }
}
</script>
<template>
  <PageContainer class="inventoryManage">
    <template #content>
      <div class="inventoryManage__header">
        <el-form ref="formRef" :model="searchForm" class="inventoryManage__search" inline @submit.native.prevent="onSearch">
          <el-form-item prop="warehouseId">
            <el-select v-model="searchForm.warehouseId" placeholder="库房名称" clearable filterable>
              <el-option v-for="item of warehouseOptions" :key="item.id" :label="item.warehouseName" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="checkId">
            <el-select v-model="searchForm.checkId" placeholder="盘点人" clearable filterable>
              <el-option v-for="item of inventoryPersonOptions" :key="item.id" :label="item.staffName" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="month">
            <el-date-picker v-model="searchForm.month" value-format="yyyy-MM" format="yyyy-MM" type="month" placeholder="年月"> </el-date-picker>
          </el-form-item>
          <el-form-item prop="inventoryTime">
            <el-date-picker
              v-model="searchForm.inventoryTime"
              type="daterange"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="盘点开始时间"
              end-placeholder="盘点结束时间"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" plain @click="onReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="inventoryManage__actions">
        <el-button type="primary" v-auth="'inventoryManagement:add'" @click="onOperate('add')" icon="el-icon-plus"> 新建盘点 </el-button>
      </div>
      <div class="inventoryManage__table">
        <el-table v-loading="tableLoadingStatus" height="100%" :data="tableData" border stripe table-layout="auto" class="tableAuto" row-key="id">
          <el-table-column type="index" width="70" label="序号"> </el-table-column>
          <el-table-column prop="warehouseName" label="库房" show-overflow-tooltip></el-table-column>
          <el-table-column prop="month" label="月份" show-overflow-tooltip></el-table-column>
          <el-table-column prop="checkName" label="盘点人" show-overflow-tooltip></el-table-column>
          <el-table-column prop="takeStockTime" label="盘点时间" show-overflow-toolti></el-table-column>
          <el-table-column label="操作" width="150px">
            <template #default="{ row }">
              <el-button type="text" v-auth="'inventoryManagement:check'" @click="onOperate('view', row)">查看</el-button>
              <el-button type="text" v-auth="'inventoryManagement:del'" class="text-red" @click="onOperate('delete', row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        class="inventoryManage__pagination"
        :current-page="pagination.page"
        :page-sizes="pagination.pageSizeOptions"
        :page-size="pagination.size"
        :layout="pagination.layoutOptions"
        :total="pagination.total"
        @size-change="paginationSizeChange"
        @current-change="paginationCurrentChange"
      >
      </el-pagination>
    </template>
  </PageContainer>
</template>
<style scoped lang="scss">
.inventoryManage {
  ::v-deep(> .container-content) {
    padding: 16px;
    height: 100%;
    width: 100%;
    background-color: #fff;
  }
  &__header {
    display: flex;
    justify-content: space-between;
  }
  &__actions {
    padding-bottom: 10px;
  }
  &__table {
    height: calc(100% - 140px);
  }
  &__pagination {
    margin-top: 10px;
  }
  .text-red {
    color: #ff1919;
  }
}
.el-form-item {
  margin-bottom: 8px !important;
}
</style>
