<template>
  <PageContainer>
    <div slot="content" ref="contentRef">
      <div class="back" @click="goBack">
        <i class="el-icon-arrow-left"></i>
        <span>返回</span>
      </div>
      <el-table v-loading="tableLoading" :data="tableData" border style="width: 100%" height="88%" @row-click="handleClick">
        <el-table-column label="序号" type="index" width="50" align="center" :resizable="false">
          <template slot-scope="scope">
            {{ (pageNo - 1) * pageSize + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="工单号" width="180" :resizable="false">
          <template slot-scope="scope">
            <span style="color: #3562db">{{ scope.row.workNum }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="sourcesDeptName" label="所属科室" width="180" show-overflow-tooltip :resizable="false"> </el-table-column>
        <el-table-column label="服务起点" show-overflow-tooltip :resizable="false">
          <template slot-scope="scope">
            <span>{{ scope.row.olgTaskDetailList[0].transportStartLocalName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="服务终点" show-overflow-tooltip :resizable="false">
          <template slot-scope="scope">
            <span>{{ scope.row.olgTaskDetailList[0].transportEndLocalName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="transportName" label="服务事项" show-overflow-tooltip :resizable="false"></el-table-column>
        <el-table-column prop="designateDeptName" label="服务部门" show-overflow-tooltip :resizable="false"> </el-table-column>
        <el-table-column prop="designatePersonName" label="服务人员" show-overflow-tooltip :resizable="false"> </el-table-column>
        <el-table-column prop="designatePersonPhone" label="联系方式" show-overflow-tooltip :resizable="false"> </el-table-column>
        <el-table-column prop="questionDescription" label="说明" show-overflow-tooltip :resizable="false"> </el-table-column>
        <el-table-column label="状态" :resizable="false">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.flowcode == '1'" type="danger">未受理</el-tag>
            <el-tag v-else-if="scope.row.flowcode == '2'" type="danger">未派工</el-tag>
            <el-tag v-else-if="scope.row.flowcode == '3'" type="success">已派工</el-tag>
            <el-tag v-else-if="scope.row.flowcode == '4'" type="warning">已挂单</el-tag>
            <el-tag v-else-if="scope.row.flowcode == '5'" type="success">已完工</el-tag>
            <el-tag v-else-if="scope.row.flowcode == '6'" type="danger">已取消</el-tag>
            <el-tag v-else-if="scope.row.flowcode == '7'" type="info">暂存</el-tag>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="pageNo"
        :page-sizes="[15, 30, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
      </el-pagination>
      <template v-if="workOrderDetailCenterShow">
        <el-dialog :visible.sync="workOrderDetailCenterShow" custom-class="detailDialog main" :close-on-click-modal="false" :before-close="workOrderDetailCloseDialog">
          <template slot="title">
            <span class="dialog-title">{{ dialogTitle }}</span>
          </template>
          <workOrderDetailList :rowData="detailObj" />
        </el-dialog>
      </template>
    </div>
  </PageContainer>
</template>
<script>
import store from '@/store/index'
import workOrderDetailList from './components/workOrderDetailList.vue'
export default {
  name: 'WorkOrder',
  components: {
    workOrderDetailList
  },
  data() {
    return {
      workNum: '', // 工单号
      flowCode: '', // 工单状态
      feedbackFlag: '', // 跟踪状态
      showTimeType: '1', // 申报时间
      dateVal: '', // 自定义时间
      tableData: [], // 表格数据
      total: 0, // 总条数
      pageNo: 1, // 当前页
      pageSize: 15, // 每页条数
      tableLoading: false, // 表格加载状态
      flowcodeOption: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '未受理',
          value: '1'
        },
        {
          label: '暂存',
          value: '7'
        },
        {
          label: '未派工',
          value: '2'
        },
        {
          label: '已派工',
          value: '3'
        },
        {
          label: '已挂单',
          value: '4'
        },
        {
          label: '已完工',
          value: '5'
        },
        {
          label: '已取消',
          value: '6'
        }
      ],
      feedbackFlagOption: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '未督办',
          value: '2'
        },
        {
          label: '已督办',
          value: '3'
        },
        {
          label: '未回访',
          value: '0'
        },
        {
          label: '已回访',
          value: '1'
        },
        {
          label: '未回复',
          value: '5'
        },
        {
          label: '已回复',
          value: '4'
        }
      ],
      showTimeTypeOption: [
        {
          label: '本年',
          value: '3'
        },
        {
          label: '昨天',
          value: '5'
        },
        {
          label: '今天',
          value: '1'
        },
        {
          label: '本周',
          value: '6'
        },
        {
          label: '本月',
          value: '2'
        },
        {
          label: '其他',
          value: '4'
        }
      ],
      countData: {},
      workOrderDetailCenterShow: false,
      detailObj: {},
      dialogTitle: '',
      free1: '',
      typeName: ''
    }
  },
  created() {
    this.typeName = this.$route.query.typeName
  },
  mounted() {
    this.execute()
  },
  methods: {
    execute() {
      if (this.typeName == 'dept' || this.typeName == 'deptfinish') {
        this.getDeptList()
      } else if (this.typeName == 'person' || this.typeName == 'personfinish') {
        this.getPersonList()
      } else if (this.typeName == 'start') {
        this.getStartList()
      } else if (this.typeName == 'end') {
        this.getEndList()
      } else if (this.typeName == 'transport') {
        this.getTransportList()
      } else {
        this.getList()
      }
    },
    getDeptList() {
      const userInfo = store.state.user.userInfo.user
      let params = {
        curPage: this.pageNo,
        pageSize: this.pageSize,
        showTimeType: this.$route.query.showTimeType,
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ',
        workTypeCode: '3',
        workTypeName: 'YS',
        sourcesDept: this.$route.query.sourcesDept
      }
      if (this.$route.query.flowcode) {
        params.flowcode = this.$route.query.flowcode
      }
      this.tableLoading = true
      this.$api.getWorkOrderDataList(params).then((res) => {
        this.tableLoading = false
        this.tableData = res.rows
        this.total = res.total
      })
    },
    getStartList() {
      const userInfo = store.state.user.userInfo.user
      let params = {
        curPage: this.pageNo,
        pageSize: this.pageSize,
        showTimeType: this.$route.query.showTimeType,
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ',
        workTypeCode: '3',
        workTypeName: 'YS',
        transportSelectType: '1'
      }
      if (this.$route.query.region) {
        params.region = this.$route.query.region
      }
      if (this.$route.query.buliding) {
        params.buliding = this.$route.query.buliding
      }
      if (this.$route.query.storey) {
        params.storey = this.$route.query.storey
      }
      if (this.$route.query.room) {
        params.room = this.$route.query.room
      }
      if (this.$route.query.roomSpace) {
        params.roomSpace = this.$route.query.roomSpace
      }
      if (this.$route.query.transportTypeCode) {
        params.transportTypeCode = this.$route.query.transportTypeCode
      }
      this.tableLoading = true
      this.$api.getWorkOrderDataList(params).then((res) => {
        this.tableLoading = false
        this.tableData = res.rows
        this.total = res.total
      })
    },
    getEndList() {
      const userInfo = store.state.user.userInfo.user
      let params = {
        curPage: this.pageNo,
        pageSize: this.pageSize,
        showTimeType: this.$route.query.showTimeType,
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ',
        workTypeCode: '3',
        workTypeName: 'YS',
        transportSelectType: '2'
      }
      if (this.$route.query.region) {
        params.region = this.$route.query.region
      }
      if (this.$route.query.buliding) {
        params.buliding = this.$route.query.buliding
      }
      if (this.$route.query.storey) {
        params.storey = this.$route.query.storey
      }
      if (this.$route.query.room) {
        params.room = this.$route.query.room
      }
      if (this.$route.query.transportTypeCode) {
        params.transportTypeCode = this.$route.query.transportTypeCode
      }
      if (this.$route.query.roomSpace) {
        params.roomSpace = this.$route.query.roomSpace
      }
      this.tableLoading = true
      this.$api.getWorkOrderDataList(params).then((res) => {
        this.tableLoading = false
        this.tableData = res.rows
        this.total = res.total
      })
    },
    getTransportList() {
      const userInfo = store.state.user.userInfo.user
      let params = {
        curPage: this.pageNo,
        pageSize: this.pageSize,
        showTimeType: this.$route.query.showTimeType,
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ',
        workTypeCode: '3',
        workTypeName: 'YS'
      }
      if (this.$route.query.transportTypeCode) {
        params.transportTypeCode = this.$route.query.transportTypeCode
      }
      this.tableLoading = true
      this.$api.getWorkOrderDataList(params).then((res) => {
        this.tableLoading = false
        this.tableData = res.rows
        this.total = res.total
      })
    },
    getPersonList() {
      const userInfo = store.state.user.userInfo.user
      let params = {
        curPage: this.pageNo,
        pageSize: this.pageSize,
        showTimeType: this.$route.query.showTimeType,
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ',
        workTypeCode: '3',
        workTypeName: 'YS',
        designatePersonCode: this.$route.query.designatePersonCode
      }
      if (this.$route.query.flowcode) {
        params.flowcode = this.$route.query.flowcode
      }
      this.tableLoading = true
      this.$api.getWorkOrderDataList(params).then((res) => {
        this.tableLoading = false
        this.tableData = res.rows
        this.total = res.total
      })
    },
    getList() {
      const userInfo = store.state.user.userInfo.user
      let params = {
        curPage: this.pageNo,
        pageSize: this.pageSize,
        showTimeType: this.$route.query.showTimeType,
        hospitalCode: userInfo.hospitalCode ?? 'BJSJTYY',
        unitCode: userInfo.unitCode ?? 'BJSYGJ',
        workTypeCode: '3',
        workTypeName: 'YS'
      }
      this.tableLoading = true
      this.$api.getWorkOrderDataList(params).then((res) => {
        this.tableLoading = false
        this.tableData = res.rows
        this.total = res.total
      })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.execute()
    },
    handleCurrentChange(val) {
      this.pageNo = val
      this.execute()
    },
    handleClick(row) {
      this.detailObj = row
      this.dialogTitle = `中央运送（${this.detailObj.flowtype}）`
      this.workOrderDetailCenterShow = true
    },
    workOrderDetailCloseDialog() {
      this.workOrderDetailCenterShow = false
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>
<style lang="scss" scoped>
.container-content > div {
  height: 100%;
  background-color: #fff;
  padding: 16px;
  margin-top: 16px;
}

.el-table {
  margin-bottom: 12px;
}

::v-deep .el-date-editor .el-range__icon {
  line-height: 24px;
}

::v-deep .el-dialog {
  height: 80vh;
  margin-top: 10vh;
}

::v-deep .el-dialog__wrapper {
  overflow: hidden;
}

::v-deep .el-dialog__body {
  height: 93%;
}

::v-deep .el-table__row:hover {
  cursor: pointer;
}

.back {
  color: #121f3e;
  cursor: pointer;
  margin-bottom: 16px;
}
</style>
