<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="addExercisePlan-content-title" @click="goBack"><i class="el-icon-arrow-left"></i><span
          style="margin-left: 10px">演练计划新增</span></div>
      <div class="content_box">
        <div class="top-title">基础信息</div>
        <el-form ref="formInline" :model="addForm" :inline="true" class="form-inline" :rules="rules"
          label-width="100px">
          <el-form-item label="演练名称" prop="drillName">
            <el-input v-model.trim="addForm.drillName" maxlength="20" type="text" placeholder="请输入演练计划名称，最多输入20个字"
              class="inputWid"></el-input>
          </el-form-item>
          <el-form-item label="演练类型" prop="drillType">
            <el-select v-model.trim="addForm.drillType" filterable clearable placeholder="演练类型" class="inputWid">
              <el-option v-for="item in exerciseTypeArr" :key="item.id" :label="item.labelName" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="演练日期" prop="drillStartTime">
            <el-date-picker v-model="addForm.drillStartTime" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
              placeholder="请选择演练日期" class="inputWid">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="参演部门" class="is-required">
            <div class="deptName"></div>
            <div class="rightBox">
              <div class="operation" @click="toDepet">
                <img src="@/assets/images/operationPort/addPlan.png" />
                添加
              </div>
              <div class="rightBox-bottom">
                <div class="dep-box">
                  <div v-for="(val, idx) in deptData" :key="idx" class="dep-small-box">
                    <i>{{ val.deptName }}</i>
                    <img src="@/assets/images/operationPort/deleteDept.png" @click="delDept(idx)" />
                  </div>
                </div>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="演练责任人" class="is-required">
            <div class="deptName"></div>
            <div class="rightBox">
              <div class="operation" @click="toPerson">
                <img src="@/assets/images/operationPort/addPlan.png" />
                添加
              </div>
              <div class="rightBox-bottom">
                <div class="dep-box">
                  <div v-for="(val, idx) in personData" :key="idx" class="dep-small-box">
                    <i>{{ val.staffName }}</i>
                    <img src="@/assets/images/operationPort/deleteDept.png" @click="delPerson(idx)" />
                  </div>
                </div>
              </div>
            </div>
          </el-form-item>
          <br />
          <el-form-item label="演练组织人" prop="organizerId">
            <el-input v-model.trim="addForm.organizerName" placeholder="请选择演练组织人" class="inputWid"
              @focus="changeplanBasicId">
            </el-input>
            <div class="tips">注：任务执行人演练后上传演练资料</div>
          </el-form-item>
          <el-form-item label="演练地点">
            <el-input v-model="addForm.drillPlace" placeholder="请输入演练地点" style="width: 850px;">
            </el-input>
          </el-form-item>
          <el-form-item label="演练任务描述">
            <el-input v-model="addForm.remark" type="textarea" placeholder="请输入演练任务描述" resize="none"
              :autosize="{ minRows: 3, maxRows: 3 }" maxlength="1000" show-word-limit style="width: 1046px;">
            </el-input>
          </el-form-item>
          <el-form-item label="演练效果评定">
            <div class="radioSelect">
              <span>1、演练分工</span>
              <el-radio v-model="divideRadio" :label="'0'">分工明确</el-radio>
              <el-radio v-model="divideRadio" :label="'1'">分工不明确</el-radio>
            </div>
            <div class="radioSelect">
              <span>2、演练职责</span>
              <el-radio v-model="dutyRadio" :label="'0'">职责清晰</el-radio>
              <el-radio v-model="dutyRadio" :label="'1'">职责不清晰</el-radio>
            </div>
            <div class="radioSelect">
              <span>3、演练效果</span>
              <el-radio-group v-model="effectRadio">
                <el-radio :label="'0'">达到预期效果</el-radio>
                <el-radio :label="'1'">基本达到预期效果</el-radio>
                <el-radio :label="'2'">未达到预期效果</el-radio>
              </el-radio-group>
            </div>
          </el-form-item>
          <br />
          <el-form-item label="演练记录上传">
            <div class="upload-title"><span>已上传</span><span> {{fileList.length?fileList.length:0}}
              </span><span>附件</span></div>
            <el-upload ref="uploadFile" action="string" drag :http-request="httpRequest" :on-remove='onRemove'
              :beforeUpload="beforeAvatarUpload" :on-exceed="handleExceed"
              accept=".jpg, .jpeg, .png, .JPG, .JPEG, .word, .WORD, .Excel, .pdf, .PDF, .doc, .docx, .DOCX, .xlsx, .xls, .mp4, .mp3"
              multiple :on-preview='openFile' :file-list="fileList">
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            </el-upload>

          </el-form-item>
          <el-form-item label="演练备注">
            <el-checkbox-group v-model="remarkCheckList">
              <el-checkbox :label="'0'">有领导参加</el-checkbox>
              <el-checkbox :label="'1'">有安全事故</el-checkbox>
            </el-checkbox-group>
            <div style="margin-top:5px">
              <el-input v-model="addForm.taskRemark" type="textarea" placeholder="请输入备注" resize="none"
                :autosize="{ minRows: 3, maxRows: 3 }" maxlength="1000" show-word-limit style="width: 1046px;">
              </el-input>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <!--选择人员 -->
      <template v-if="personDialogShow">
        <SelectPeopleDialog :personDialogShow="personDialogShow" @submitPersonDialog="submitPersonDialog"
          @closePersonDialog="closePersonDialog" />
      </template>
      <!--选择部门 -->
      <template v-if="sectionDialogShow">
        <SelectDeptDialog :sectionDialogShow="sectionDialogShow" @submitSectionDialog="submitSectionDialog"
          @closeSectionDialog="closeSectionDialog" />
      </template>
    </div>
    <div slot="footer">
      <el-button type="primary" @click="submitForm()">保存</el-button>
      <el-button type="primary" plain @click="$router.go(-1)">取消</el-button>
    </div>
  </PageContainer>
</template>
<script>
import moment from 'moment'
import SelectDeptDialog from '../exercisePlan/components/SelectDept.vue'
import SelectPeopleDialog from '../exercisePlan/components/SelectPeople.vue'
export default {
  name: 'additionalRecording',
  components: {
    SelectDeptDialog,
    SelectPeopleDialog
  },
  data() {
    return {
      personDialogShow: false, // 人员弹窗
      sectionDialogShow: false, // 部门弹窗
      dutyRadio: '0',
      divideRadio: '0',
      effectRadio: '0',
      remarkCheckList: ['0'],
      addForm: {
        drillName: '', // 计划名称
        drillType: '', // 演练类型
        drillStartTime: '', // 演练日期
        organizerId: '', // 演练组织人
        organizerName: '', // 演练组织人name
        drillPlace: '', // 演练地点
        remark: ''// 任务描述
      },
      deptIds: '',
      deptNames: '',
      headIds: '',
      headNames: '',
      deptData: [],
      personData: [],
      exerciseTypeArr: [],
      rules: {
        drillName: { required: true, message: '请输入演练计划名称', trigger: 'blur' },
        drillType: { required: true, message: '请选择演练类型', trigger: 'change' },
        drillStartTime: { required: true, message: '请选择演练日期', trigger: 'change' },
        organizerId: { required: true, message: '请选择演练组织人', trigger: 'change' }
      },
      personType: '', // 责任人 or组织人
      fileList: []
    }
  },
  mounted() {
    this.getTypeList()
  },
  methods: {
    // 获取演练类型列表
    getTypeList() {
      let data = {
        page: {
          current: 1,
          size: 9999
        }
      }
      this.$api
        .getPreplanDrillTypeData(data)
        .then((res) => {
          this.exerciseTypeArr = res.data ? res.data.list : []
        })
    },
    // 人员弹窗
    submitPersonDialog(list) {
      if (this.personType === 'zrr') {
        let headIds = []
        let headNames = []
        this.personData = this.removeSame(this.personData.concat(list))
        this.personData.forEach(item => {
          headIds.push(item.id)
          headNames.push(item.staffName)
        })
        this.headIds = headIds.join(',')
        this.headNames = headNames.join(',')
        this.personDialogShow = false
      } else if (this.personType === 'zzr') {
        if (list.length !== 1) {
          this.$message.error('组织人只支持单选')
          return
        }
        this.addForm.organizerName = list[0].staffName
        this.addForm.organizerId = list[0].staffName
        this.personDialogShow = false
      }
    },
    closePersonDialog() {
      this.personDialogShow = false
    },
    // 部门弹窗
    closeSectionDialog() {
      this.sectionDialogShow = false
    },
    submitSectionDialog(list) {
      let deptIds = []
      let deptNames = []
      this.sectionDialogShow = false
      this.deptData = this.removeSame(this.deptData.concat(list))
      this.deptData.forEach(item => {
        deptIds.push(item.id)
        deptNames.push(item.deptName)
      })
      this.deptIds = deptIds.join(',')
      this.deptNames = deptNames.join(',')
    },
    removeSame(array) {
      let newArray = []
      for (let item of array) {
        // 使用JSON.stringfy()方法将数组和数组元素转换为JSON字符串之后再使用includes()方法进行判断
        if (JSON.stringify(newArray).includes(JSON.stringify(item))) {
          continue
        } else {
          // 不存在的添加到数组中
          newArray.push(item)
        }
      }
      return newArray
    },
    goBack() {
      this.$router.go(-1)
    },
    submitForm() {
      // 参演部门校验
      if (this.deptData.length < 1) {
        this.$message({
          type: 'error',
          message: '请选择参演部门'
        })
        return false
      }
      // 演练责任人校验
      if (this.personData.length < 1) {
        this.$message({
          type: 'error',
          message: '请选择演练责任人'
        })
        return false
      }
      // // 演练文件校验
      if (this.fileList.length < 1) {
        this.$message({
          type: 'error',
          message: '请上传演练文件'
        })
        return false
      }
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          let userInfo = this.$store.state.user.userInfo.user
          if (valid) {
            let data = {
              ...this.addForm,
              deptIds: this.deptIds,
              deptNames: this.deptNames,
              headIds: this.headIds,
              headNames: this.headNames,
              createName: userInfo.staffName,
              createId: userInfo.staffId,
              userId: userInfo.staffId,
              taskEffect: '',
              taskRemark: '',
              taskUrl: JSON.stringify(this.fileList)
            }
            let taskEffectObj = {
              dutyRadio: this.dutyRadio,
              divideRadio: this.divideRadio,
              effectRadio: this.effectRadio
            }
            let taskRemarkObj = {
              remark: this.addForm.remark,
              remarkCheckList: this.remarkCheckList
            }
            const taskEffectStr = JSON.stringify(taskEffectObj)
            const taskRemarkStr = JSON.stringify(taskRemarkObj)
            data.taskEffect = taskEffectStr
            data.taskRemark = taskRemarkStr
            this.$api.addTaskData(data, { 'operation-type': 1 }).then((res) => {
              if (res.code == '200') {
                this.$message({
                  type: 'success',
                  message: '保存成功'
                })
                this.$router.go('-1')
              } else {
                this.$message({
                  type: 'error',
                  message: res.message || '保存失败'
                })
              }

            })
          }
        }
      })
    },
    // 参演部门
    toDepet() {
      this.sectionDialogShow = true
    },
    // 删除部门
    delDept(index) {
      this.deptData.splice(index, 1)
    },
    // 参演人员
    toPerson() {
      this.personDialogShow = true
      this.personType = 'zrr'
    },
    // 删除人员
    delPerson(index) {
      this.personData.splice(index, 1)
    },
    // 组织name
    changeplanBasicId() {
      this.personDialogShow = true
      this.personType = 'zzr'
    },
    /**
   * 文件上传成功
   */
    // 限制文件大小
    beforeAvatarUpload(file) {
      const isLt20M = file.size / 1024 / 1024 < 20
      if (!isLt20M) {
        this.$message.error('上传文件大小不能超过 20MB!')
        return false
      }
    },
    /**
   * 删除文件
   */
    httpRequest(file) {
      const params = new FormData()
      params.append('file', file.file)
      this.$api.uploadTask(params).then((res) => {
        if (res.code == 200) {
          this.fileList.push({
            url: res.data.picUrl,
            name: res.data.name
          })
          this.$message({
            message: '上传成功',
            type: 'success'
          })
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },
    openFile(e) {
      const url = e.url
      window.open(url)
    },
    // 移除
    onRemove(file, fileList) {
      this.fileList = fileList
    },
    handleExceed(files, fileList) {
      this.$message.warning('最多选择一个文件')
    }
  }
}
</script>

<style lang="scss" scoped>
.container-content > div {
  background-color: #fff;
  height: 100%;
  position: relative;
  .addExercisePlan-content-title {
    border-bottom: 1px solid #e4e7ed;
    padding: 13px 24px;
    cursor: pointer;
    font-weight: 500;
    color: #333333;
  }
  .content_box {
    color: #000;
    padding: 16px 24px;
    background: #fff;
    display: flex;
    height: calc(100% - 26px);
    flex-direction: column;
    overflow-y: scroll;
    .top-title {
      font-weight: bold;
      font-size: 16px;
      color: #333333;
    }
  }
  .form-inline {
    margin-top: 24px;
  }
  .form-inline .inputWid {
    width: 360px;
  }
  .radioSelect {
    span {
      margin-right: 10px;
    }
  }
}
.deptName {
  font-size: 14px;
  color: #333333;
}
.rightBox {
  cursor: pointer;
  .operation {
    display: inline-block;
  }
  .dep-box {
    width: 905px;
    min-height: 40px;
    max-height: 85px;
    overflow-y: auto;
    border: 1px solid #c0c4cc;
    border-radius: 5px;
    box-sizing: border-box;
    display: flex;
    flex-wrap: wrap;
    margin: 5px 0;
    padding: 5px;
    .dep-small-box {
      background: #ededf5;
      border-radius: 4px;
      margin: 2px 0px 2px 6px;
      line-height: 22px;
      padding: 0px 8px;
      font-size: 14px;
      color: #333333;
      cursor: pointer;
      i {
        font-style: normal;
      }
      img {
        margin-left: 4px;
        vertical-align: middle;
      }
    }
    .dep-small-box:first-child {
      margin-left: 0px;
    }
  }
}
.el-select-dropdown {
  min-width: 200px !important;
}
.el-select-dropdown__item {
  min-width: 200px !important;
}
.tips {
  font-size: 12px;
  color: #7f848c;
  height: 14px;
  line-height: 14px;
}
.upload-title {
  font-size: 14px;
  span:nth-child(1),
  span:nth-child(3) {
    color: #7f848c;
  }
  span:nth-child(2) {
    color: #333333;
    margin: 0 4px;
  }
}
</style>
