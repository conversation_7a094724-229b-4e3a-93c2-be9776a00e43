<script>
import tableListMixin from '@/mixins/tableListMixin'
import ContentCard from '@/components/ContentCard/index.vue'
export default {
  name: 'PaymentOrder',
  components: { ContentCard },
  mixins: [tableListMixin],
  data: () => ({
    activeTabs: '1',
    searchForm: {
      union: '',
      projectCode: '',
      paymentType: '',
      specialOfficerName: '',
      projectLocationId: '',
      queryTime: '',
      supplierId: ''
    },
    tableData: [],
    supplierList: [],
    multipleSelection: [],
    projectLocationList: [],
    props1: {
      checkStrictly: true,
      emitPath: false,
      value: 'id',
      label: 'ssmName',
      children: 'children'
    },
    loadingStatus: false,
    dialogVisible: false,
    drawerVisible: false,
    drawerLoading: false,
    detailId: null,
    formModel: {},
    dialogLoading: false,
    dialogTitle: ''
  }),
  computed: {
    queryParams() {
      const form = JSON.parse(JSON.stringify(this.searchForm))
      let params = {
        ...form,
        pageNo: this.pagination.current,
        pageSize: this.pagination.size
      }
      if (this.activeTabs !== '3') {
        params.type = this.activeTabs
        delete params.paymentType
        delete params.projectCode
      } else {
        params = {
          paymentType: form.paymentType,
          projectCode: form.projectCode,
          pageNo: this.pagination.current,
          pageSize: this.pagination.size
        }
      }
      if (form.queryTime) {
        params.startTime = form.queryTime[0]
        params.endTime = form.queryTime[1]
        delete params.queryTime
      }
      return params
    },
    OperateType() {
      return {
        Detail: 'detail',
        Delete: 'delete',
        Create: 'create'
      }
    }
  },
  created() {
    this.getSupplierList()
    this.getProjectLocationListList()
  },
  mounted() {
    this.getDataList()
  },
  methods: {
    async getSupplierList() {
      const res = await this.$api.SporadicProject.getProjectSupplierAll({})
      if (res.code === '200') {
        this.supplierList = res.data
      }
    },
    async getProjectLocationListList() {
      const res = await this.$api.getAllSpaceTree()
      if (res.code === 200) {
        const l = (res.data ?? []).filter((item) => item.ssmType != 5)
        const arr = this.$tools.transData(l, 'id', 'pid', 'children')
        const list = []
        arr.map((item) => list.push(...item.children))
        this.projectLocationList = list
      }
    },
    getDataList() {
      this.loadingStatus = true
      this.$api.SporadicProject[this.activeTabs !== '3' ? 'queryprojectPaymentOrderByPage' : 'queryprojectPaymentGenerateByPage'](this.queryParams)
        .then((res) => {
          if (res.code === '200') {
            this.tableData = res.data && res.data.records ? res.data.records : []
            this.pagination.total = res.data.total
          }
        })
        .finally(() => (this.loadingStatus = false))
    },
    reset() {
      this.$refs.formRef && this.$refs.formRef.resetFields()
      this.searchForm = {
        union: '',
        projectCode: '',
        paymentType: '',
        specialOfficerName: '',
        projectLocationId: '',
        queryTime: '',
        supplierId: ''
      }
      setTimeout(() => this.getDataList(), 10)
    },
    onOperate(row, command) {
      switch (command) {
        case this.OperateType.Delete:
          this.doDelete(row.id)
          break
        default:
          this.drawerVisible = true
          this.getDetailFn(row?.id ?? '')
          break
      }
    },
    doDelete(ids) {
      this.$api.SporadicProject.cancelReportByIds({ ids })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('作废成功')
            this.getDataList()
          } else {
            throw res.message
          }
        })
        .catch((msg) => msg && this.$message.error(msg))
    },
    getDetailFn(id) {
      this.drawerLoading = true
      this.$api.SporadicProject.getPaymentGenerateAndFileById({ id })
        .then((res) => {
          if (res.code === '200') {
            this.formModel = res.data
            this.formModel.paymentType = res.data.paymentType === '1' ? '首款付款单' : res.data.paymentType === '2' ? '质保付款单' : ''
          } else {
            throw res.message || res.msg
          }
        })
        .catch((msg) => msg && this.$message.error(msg))
        .finally(() => {
          this.drawerLoading = false
          this.detailId = null
        })
    },
    activeTabsChange() {
      this.multipleSelection = []
      this.reset()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    beforeDialogClose() {
      if (this.dialogTitle === '生成完毕') {
        this.getDataList()
      }
      this.dialogVisible = false
      this.dialogLoading = false
    },
    dialogClose() {
      this.dialogVisible = false
      this.dialogLoading = false
      this.$tools.cancelAjax()
    },
    toVoidFn() {
      const ids = this.multipleSelection.map((item) => item.id).join(',')
      this.doDelete(ids)
    },
    async showDialogVisible() {
      const projectCodes = this.multipleSelection.map((item) => item.projectCode).join(',')
      this.dialogVisible = true
      this.dialogTitle = '生成中，请稍候…'
      this.dialogLoading = true
      this.$api.SporadicProject.manuallyGeneratedReport({ paymentType: this.activeTabs, projectCodes })
        .then((res) => {
          if (res.code === '200') {
            this.dialogLoading = false
            this.dialogTitle = '生成完毕'
            this.detailId = res.data.id
          } else {
            this.dialogLoading = false
            this.dialogTitle = '生成失败'
            res.msg && this.$message.error(res.msg)
          }
        })
        .catch((msg) => {
          this.dialogLoading = false
          this.dialogTitle = '生成失败'
          msg && this.$message.error(msg)
        })
    },
    toViewDetail() {
      this.dialogVisible = false
      this.dialogLoading = false
      this.drawerVisible = true
      this.getDataList()
      this.detailId && this.getDetailFn(this.detailId)
    },
    downFileFn(path) {
      window.open(path)
    }
  }
}
</script>
<template>
  <PageContainer>
    <div slot="content" style="height: 100%">
      <div class="orderHeader">
        <el-tabs v-model="activeTabs" @tab-click="activeTabsChange">
          <el-tab-pane label="首款待付项目" name="1"></el-tab-pane>
          <!--          <el-tab-pane label="质保阶段项目" name="2"></el-tab-pane>-->
          <el-tab-pane label="已生成" name="3"></el-tab-pane>
        </el-tabs>
      </div>
      <div class="build-group-config">
        <div class="build-group-config__top_order">
          <el-form ref="formRef" :model="searchForm">
            <el-row v-show="activeTabs !== '3'" justify="left" align="middle" :gutter="10">
              <el-col :span="5">
                <el-form-item prop="union">
                  <el-input v-model="searchForm.union" clearable filterable placeholder="搜索编号、工程名称、报告编号"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="3">
                <el-form-item prop="specialOfficerName">
                  <el-input v-model="searchForm.specialOfficerName" clearable filterable placeholder="搜索专管员"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="3">
                <el-form-item prop="supplierId">
                  <el-select v-model="searchForm.supplierId" placeholder="全部施工单位" clearable style="width: 100%">
                    <el-option v-for="item in supplierList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="3">
                <el-form-item prop="projectLocationId">
                  <el-cascader
                    ref="icmSpace"
                    v-model="searchForm.projectLocationId"
                    filterable
                    :show-all-levels="false"
                    placeholder="全部位置"
                    :options="projectLocationList"
                    :props="props1"
                    clearable
                  ></el-cascader>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item prop="queryTime">
                  <el-date-picker
                    v-model="searchForm.queryTime"
                    style="width: 100%"
                    type="datetimerange"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    format="yyyy-MM-dd HH:mm:ss"
                    start-placeholder="申请日期"
                    range-separator="→"
                    end-placeholder="申请日期"
                    :default-time="['00:00:00', '23:59:59']"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="3">
                <el-form-item>
                  <el-button type="primary" @click="getDataList">查询</el-button>
                  <el-button type="primary" plain @click="reset">重置</el-button>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row v-show="activeTabs === '3'" justify="left" align="middle" :gutter="10">
              <el-col :span="4">
                <el-form-item prop="projectCode">
                  <el-input v-model="searchForm.projectCode" clearable filterable placeholder="搜索相关项目编号"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="4">
                <el-form-item prop="paymentType">
                  <el-select v-model="searchForm.paymentType" placeholder="全部单据类型" clearable style="width: 100%">
                    <el-option label="首款付款单" value="1"> </el-option>
                    <!--                    <el-option label="质保付款单" value="2"> </el-option>-->
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item prop="queryTime">
                  <el-date-picker
                    v-model="searchForm.queryTime"
                    style="width: 100%"
                    type="datetimerange"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    format="yyyy-MM-dd HH:mm:ss"
                    start-placeholder="申请日期"
                    range-separator="→"
                    end-placeholder="申请日期"
                    :default-time="['00:00:00', '23:59:59']"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item>
                  <el-button type="primary" @click="getDataList">查询</el-button>
                  <el-button type="primary" plain @click="reset">重置</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div class="addBox">
          <el-button v-if="activeTabs !== '3'" :disabled="!multipleSelection.length" type="primary" @click="showDialogVisible">生 成</el-button>
          <el-button v-else type="primary" size="small" :disabled="!multipleSelection.length" style="background: #d9001b; border-color: #d9001b; color: #fff" @click="toVoidFn">
            作 废
          </el-button>
        </div>
        <div class="build-group-config__table table-content">
          <el-table
            v-show="activeTabs !== '3'"
            v-loading="loadingStatus"
            :data="tableData"
            border
            stripe
            table-layout="auto"
            class="tableAuto"
            row-key="id"
            :height="tableHeight"
            style="width: 100%"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="40" />
            <el-table-column label="编号" prop="projectCode" min-width="100" show-overflow-tooltip></el-table-column>
            <el-table-column label="工程名称" prop="projectName" min-width="200" show-overflow-tooltip></el-table-column>
            <el-table-column label="报告编号" prop="reportNo" min-width="100" show-overflow-tooltip></el-table-column>
            <el-table-column label="专管员" prop="specialOfficerName" min-width="100" show-overflow-tooltip></el-table-column>
            <el-table-column label="施工单位" prop="supplierName" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column label="位置" prop="projectLocationName" min-width="200" show-overflow-tooltip></el-table-column>
            <el-table-column label="申请时间" prop="createTime" width="170" show-overflow-tooltip></el-table-column>
          </el-table>
          <el-table
            v-show="activeTabs === '3'"
            v-loading="loadingStatus"
            :data="tableData"
            border
            stripe
            table-layout="auto"
            class="tableAuto"
            row-key="id"
            :height="tableHeight"
            style="width: 100%"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="40" />
            <el-table-column label="编号" prop="code" min-width="100" show-overflow-tooltip></el-table-column>
            <el-table-column label="相关项目编号" prop="projectCode" min-width="300" show-overflow-tooltip></el-table-column>
            <el-table-column label="单据类型" prop="paymentType" min-width="100" show-overflow-tooltip>
              <template #default="scope">
                <span>{{ scope.row.paymentType === '1' ? '首款付款单' : scope.row.paymentType === '2' ? '质保付款单' : '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作人" prop="createName" min-width="100" show-overflow-tooltip></el-table-column>
            <el-table-column label="生成时间" prop="createTime" width="170" show-overflow-tooltip></el-table-column>
            <el-table-column label="操作" width="120px" fixed="right">
              <template #default="scope">
                <el-button type="text" @click="onOperate(scope.row, OperateType.Detail)">查看</el-button>
                <el-button type="text" class="text-red" @click="onOperate(scope.row, OperateType.Delete)">作废</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination
          class="build-group-config__pagination"
          :current-page="pagination.page"
          :page-sizes="pagination.pageSizeOptions"
          :page-size="pagination.size"
          :layout="pagination.layoutOptions"
          :total="pagination.total"
          @size-change="paginationSizeChange"
          @current-change="paginationCurrentChange"
        >
        </el-pagination>
      </div>
      <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="400px" :before-close="beforeDialogClose" center append-to-body destroy-on-close>
        <div v-loading="dialogLoading" class="dialogContent">
          <i v-if="dialogTitle === '生成完毕'" class="el-icon-success dialogIcon"></i>
          <i v-show="dialogTitle === '生成失败'" class="el-icon-error dialogErrIcon"></i>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button v-if="dialogTitle !== '生成完毕'" type="primary" @click="dialogClose">取 消</el-button>
          <el-button v-else type="primary" @click="toViewDetail">立即查看</el-button>
        </span>
      </el-dialog>
      <el-drawer v-loading="drawerLoading" class="component form-detail" title="支付申请单详情" size="960px" :visible.sync="drawerVisible" :close-on-press-escape="false">
        <ContentCard title="基本信息">
          <template #content>
            <el-form ref="formModelRef" disabled :model="formModel" label-width="95px">
              <el-row :gutter="12">
                <el-col :span="12">
                  <el-form-item label="编号" prop="code">
                    <el-input v-model="formModel.code"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="单据类型" prop="paymentType">
                    <el-input :value="formModel.paymentType"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="操作人" prop="flowModelName">
                    <el-input :value="formModel.createName"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="生成时间" prop="createTime">
                    <el-input v-model="formModel.createTime"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item label="相关项目" prop="projectCode" class="full-line">
                <el-input v-model="formModel.projectCode" type="textarea" :rows="2"></el-input>
              </el-form-item>
            </el-form>
          </template>
        </ContentCard>
        <ContentCard title="生成内容" class="form-detail__table">
          <template #content>
            <el-table height="300" :data="formModel.fileList" border stripe class="tableAuto" row-key="id">
              <el-table-column property="fileName" label="名称" min-width="200"></el-table-column>
              <el-table-column property="fileUrl" label="操作" min-width="50">
                <template #default="scope">
                  <el-button type="text" @click="downFileFn(scope.row.fileUrl)">查看</el-button>
                </template>
              </el-table-column>
            </el-table>
          </template>
        </ContentCard>
        <div class="form-detail__footer">
          <el-button type="primary" plain @click="drawerVisible = false">取消</el-button>
        </div>
      </el-drawer>
    </div>
  </PageContainer>
</template>
<style scoped lang="scss">
.addBox {
  padding: 10px 0;
}
.dialogIcon {
  font-size: 80px;
  color: #67c23a;
}
.dialogErrIcon {
  font-size: 80px;
  color: #d50606;
}
.dialogContent {
  display: flex;
  justify-content: center;
  align-items: center;
}
.orderHeader {
  padding: 0 15px;
  background: #fff;
}
.build-group-config {
  display: flex;
  flex-flow: column nowrap;
  padding: 16px;
  background: #fff;
  height: calc(100% - 36px);
  &__top_order {
    display: flex;
    justify-content: space-between;
  }
  &__pagination {
    margin-top: 10px;
  }
  &__table {
    flex: 1;
  }
  &__tag {
    // 停用
    &--0 {
      --color: #f64646;
    }
    // 启用
    &--1 {
      --color: #00b42a;
    }
    &:before {
      content: '';
      display: inline-block;
      vertical-align: middle;
      margin-right: 4px;
      height: 4px;
      width: 4px;
      border-radius: 4px;
      background-color: var(--color);
    }
  }
}
.component.form-detail {
  .el-drawer {
    .el-drawer__header {
      color: #333;
      padding: 9px 16px;
      border-bottom: solid 1px #eee;
      margin-bottom: 0;
    }
    .el-drawer__body {
      display: flex;
      flex-flow: column nowrap;
      overflow: hidden;
    }
  }
  .box-card {
    padding: 16px;
    .card-body {
      overflow: hidden;
    }
  }
  @mixin normal-text {
    cursor: default;
    color: #666;
    border: none;
    background-color: transparent;
  }
  .el-form {
    .el-form-item {
      .el-select {
        width: 100%;
      }
      .el-textarea.is-disabled .el-textarea__inner {
        @include normal-text;
        resize: none;
        padding-top: 9px;
      }
      .el-input.is-disabled {
        .el-input__inner {
          @include normal-text;
        }
        .el-input__icon {
          display: none;
        }
      }
    }
  }
  .form-detail {
    &__table {
      flex: 1;
      margin-top: -30px;
      padding-bottom: 0;
      overflow: hidden;
    }
    &__footer {
      padding: 16px;
      text-align: right;
    }
  }
}
</style>
<style>
.build-group-config__top_order .el-date-editor .el-range__icon,
.build-group-config__top_order .el-date-editor .el-range__close-icon {
  line-height: 24px !important;
}
.build-group-config__top_order .el-form-item {
  margin-bottom: 0 !important;
}
</style>
