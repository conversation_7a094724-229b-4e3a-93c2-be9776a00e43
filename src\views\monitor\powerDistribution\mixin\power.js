// 数据类型包括相电压（A、B、C，多选项）、线电压（Uab、Ubc、Uac，多选项）、
// 电流（A、B、C多选项）、频率、有功功率（A、B、C、总有功功率，多选项）、
// 无功功率（A、B、C、总无功功率，多选项）、视在功率（A、B、C、总视在功率，多选项）、
// 功率因数（A、B、C、总功率因数，多选项）、不平衡度（三相电流不平衡度、三相电压不平衡度，多选项）、
// 负载率；
import * as echarts from 'echarts'
import { monitorTypeList } from '@/util/dict.js'
export default {
  data() {
    return {
      projectCode: monitorTypeList.find(item => item.projectName == '配电监测').projectCode,
      typeTabs: [
        { 
          name: '原始数据',  // 名称
          type: '0',  // 类型标识
          api: 'queryRawDataList',
          dateTypeArr: [
            {
              dateType: 'custom',
              name: '自定义',
              unit: '',
              status: 3
            }
          ],
          typeArr: []
        },
        {
          name: '极值数据',
          type: '1',
          api: 'queryMenuList',
          dateTypeArr: [
            {
              dateType: 'custom',
              name: '自定义',
              unit: '',
              status: 3
            }
          ],
          typeArr: []
        },
        {  
          name: '平均功率因数', 
          type: '2',
          api: 'queryAvgPowerData',
          dateTypeArr: [
            {
              dateType: 'month',
              name: '月报',
              unit: '月',
              status: 1
            },
            {
              dateType: 'year',
              name: '年报',
              unit: '年',
              status: 2
            }
          ],
          typeArr: []
        },
        { 
          name: '谐波监测', 
          type: '3',
          api: 'queryHarmonicMonitorData',
          dateTypeArr: [
            {
              dateType: 'day',
              name: '日报',
              unit: '日',
              status: 0
            }
          ],
          typeArr: [
            // 电流谐波、电压谐波
            {
              paramName: '电压谐波',
              paramId: 1
            },
            {
              paramName: '电流谐波',
              paramId: 0
            }
          ]
        }
      ]
    }
  },
  methods: {
    // 折线图
    setLineChart(data = [], title = '--', startTime = null, endTime = null) {
      console.log('data.map(x => { return x.date })===========', data)
      function colorAdd(transparency) {
        return [
          `rgba(255, 100, 97, ${transparency})`, 
          `rgba(255, 148, 53, ${transparency})`, 
          `rgba(53, 98, 219, ${transparency})`, 
          `rgba(0, 188, 109, ${transparency})`, 
          `rgba(115, 192, 222, ${transparency})`, 
          `rgba(154, 96, 180, ${transparency})`, 
          `rgba(250, 200, 88, ${transparency})`,
          `rgba(9, 205, 143, ${transparency})`
        ]
      }
      let colorHalf = colorAdd('.5')
      let colorZero = colorAdd('0')

      if (data.length) {
        // 补全差的颜色
        function rgbColor(transparency) {
          function colorCode() {
            return Math.floor(Math.random() * 156)
          }
          return `rgba(${colorCode()}, ${colorCode()}, ${colorCode()}, ${transparency})`
        }
        // 相差的颜色补全
        let len = data[0].list.length - colorHalf.length
        for (let i = 0; i < len + 1; i++) {
          colorHalf.push(rgbColor('.5'))
          colorZero.push(rgbColor('0'))
        }
      }

      // 极值
      let xExtremum = {
        type: 'category',
        boundaryGap: false,
        data: data.map(x => { return x.date })
      }

      // 其他  原始数据||谐波检测
      let xOther = {
        type: 'time',
        interval: 60 * 60 * 1000, // 固定x轴时间间隔 间隔24小时，也就是一天
        maxInterval: 60 * 60 * 1000,
        minInterval: 1000,
        min: new Date(startTime).getTime(), // 开始时间时间戳
        max: new Date(endTime).getTime(), // 结束时间时间戳 如果实际的最大日期不确定，也可以不设定这个属性
        boundaryGap: false,
        // nameLocation: 'start',
        // x轴的字
        // axisLabel: {
        //   show: true,
        //   showMinLabel: true,
        //   showMaxLabel: true,
        //   formatter: function (value, index) {
        //     // 格式化成月/日，只在第一个刻度显示年份
        //     var date = new Date(value)
        //     return date.getHours() + ':00'
        //   }
        // },
        splitLine: {
          show: false
        },
        axisTick: {
          show: false // 不显示坐标轴刻度线
        }
      }
      let option
      // var a = Array.from(new Array(10).keys()).slice(1)
      if (data.length) {
        option = {
          title: {
            text: title,
            left: 'center'
          },
          color: colorHalf,
          tooltip: {
            trigger: 'axis'
          },
          legend: {
            // icon: 'rect',
            show: true,
            top: '6%'
          },
          xAxis: this.choiceTypeTab == 1 ? xExtremum : xOther,
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: '{value}'
            }
          },
          series: this.choiceTypeTab == 1 ? this.dataArr(data, colorHalf, colorZero) : this.newDataArr(data, colorHalf, colorZero),
          dataZoom: [
            {
              height: 20, // 时间滚动条的高度
              type: 'inside', // type的作用是指定数据缩放的类型，slider表示使用滑动条进行缩放，inside表示使用鼠标滚轮进行缩放。
              xAxisIndex: 0, // 作用在x轴的下标（因为x轴可以有多个）
              filterMode: 'filter', // 间滚动条的过滤模式,'filter'表示滑动时间条时会直接过滤掉不在时间范围内的数据，'weakFilter'表示滑动时间条时会逐渐过滤掉不在时间范围内的数据。
              start: 0,  // 默认开始位置（百分比）
              end: 100  // 默认结束位置（百分比）
            },
            {
              type: 'inside',
              xAxisIndex: 0,
              filterMode: 'filter',
              start: 0,
              end: 100
            }
          ],
          grid: { // 让图表占满容器
            // top: '0px',
            left: '50px',
            right: '30px',
            bottom: '0',
            containLabel: true
          }
        }

      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      return option
    },
    newDataArr(data, colorHalf, colorZero) {
      let arr = []
      data.forEach((v) => {
        v.list?.forEach((sonV, index) => {
          let obj = arr.find(ele => ele.name == sonV.name)
          var value = sonV.value
          if (obj) {
            obj.data.push([v.date, value])
          } else {
            let dataObj = {
              name: sonV.name,
              type: 'line',
              data: [[v.date, value]],
              // symbol: 'circle',
              areaStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                    offset: 0,
                    color: colorHalf[index + 1]
                  }, {
                    offset: 1,
                    color: colorZero[index + 1]
                  }])
                }
              }
            }
            arr.push(dataObj)
          }
        })
      })
      return arr
    },
    dataArr(data, colorHalf, colorZero) {
      let arr = []
      data.forEach((v) => {
        v.list?.forEach((sonV, index) => {
          let obj = arr.find(ele => ele.name == sonV.name)
          var value = sonV.value
          if (obj) {
            obj.data.push(value)
          } else {
            let dataObj = {
              name: sonV.name,
              type: 'line',
              data: [value],
              // symbol: 'circle',
              areaStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                    offset: 0,
                    color: colorHalf[index + 1]
                  }, {
                    offset: 1,
                    color: colorZero[index + 1]
                  }])
                }
              }
            }
            arr.push(dataObj)
          }
        })
      })
      return arr
    }
  }
}