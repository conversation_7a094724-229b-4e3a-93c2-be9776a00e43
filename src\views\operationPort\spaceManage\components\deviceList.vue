<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="content_box">
        <el-table :data="tableData" height="100%">
          <el-table-column type="index" label="序号" width="50" align="center">
            <template slot-scope="scope">
              <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="资产名称" prop="" width="200px" show-overflow-tooltip>
            <template slot-scope="scope">
              <span class="color_blue" @click="ViewFn(scope.row)">
                {{ scope.row.assetName }}
              </span>
            </template>
          </el-table-column>
          <el-table-column v-for="(column, index) in tableColumn" :key="index" :prop="column.prop" :label="column.label" :min-width="column.minWidth" show-overflow-tooltip>
            <template slot-scope="scope">
              <table-render v-if="column.render" :sc="scope" :row="scope.row" :render="column.render"></table-render>
              <div v-else-if="!column.formatter">
                {{ scope.row[column.prop] }}
              </div>
              <div v-else>
                {{ column.formatter(scope.row) }}
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="contentTable-footer">
          <el-pagination
            :current-page="paginationData.currentPage"
            :page-sizes="[15, 30, 50, 100]"
            :page-size="paginationData.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="paginationData.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">关闭</el-button>
    </div>
  </PageContainer>
</template>

<script>
export default {
  name: 'detailsAssets',
  data() {
    return {
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      tableData: [],
      type: '',
      tableColumn: [
        {
          prop: 'assetCode',
          label: '资产编码'
        },
        {
          prop: 'professionalCategoryName',
          label: '专业类别'
        },
        {
          prop: 'assetModel',
          label: '型号'
        },
        {
          prop: 'assetStatusName',
          label: '使用状态'
        },
        {
          prop: 'centralizedDepartmentName',
          label: '归口部门'
        },
        {
          prop: 'startDate',
          label: '启用日期'
        }
      ]
    }
  },
  created() {
    this.type = this.$route.query.type
    this.getDeviceList()
  },
  methods: {
    getDeviceList() {
      const params = {
        currentPage: this.paginationData.currentPage,
        pageSize: this.paginationData.pageSize,
        assetStatusCode: this.type || ''
      }
      this.$api.getOperationDeviceList(params).then(res => {
        if (res.code == '200') {
          this.tableData = res.data.list
          this.paginationData.total = res.data.sum
        }
      })
    },
    handleSizeChange(val) {
      this.paginationData.pageSize = val
      this.paginationData.currentPage = 1
      this.getDeviceList()
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.getDeviceList()
    },
    ViewFn(row) {
      this.$router.push({
        path: '/detailsAssets',
        query: { row: row }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
  padding: 10px;
  overflow-y: auto;
}

.content_box {
  height: calc(100% - 30px);
  margin-top: 10px;
  background: #fff;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.contentTable-footer {
  padding: 10px 0 0;
}

.table-block-text {
  width: 80px;
  line-height: 24px;
  text-align: center;
  cursor: pointer;
  color: #fff;
}

.color_blue {
  color: #5188fc;
  cursor: pointer;
}
</style>
