<!--
 * @Author: hedd
 * @Date: 2023-04-23 16:19:30
 * @LastEditTime: 2024-08-02 16:15:42
 * @FilePath: \ihcrs_pc\src\views\monitor\elevatorMenu\components\elevatorDetail.vue
 * @Description:
-->
<!-- 环境监测详情 -->
<template>
  <PageContainer class="monitorDetails">
    <div slot="header" class="monitorDetails-heade">
      <i class="el-icon-arrow-left" @click="goBack" />
      <span @click="goBack">电梯设备详情</span>
    </div>
    <div slot="content" class="content-details">
      <div class="content_top">
        <div class="content_top_left">
          <div class="detail_list">
            <p>设备名称</p>
            <div><img />{{ elevatorDetailData.assetsName }}</div>
          </div>
          <div class="detail_list">
            <p>设备型号</p>
            <div><img />{{ elevatorDetailData.model }}</div>
          </div>
          <div class="detail_list">
            <p>SN码</p>
            <div><img />{{ elevatorDetailData.registrationNum }}</div>
          </div>
          <div class="detail_list">
            <p>归属部门</p>
            <div><img />{{ elevatorDetailData.useDepartmentName }}</div>
          </div>
          <div class="detail_list">
            <p>启用时间</p>
            <div><img />{{ elevatorDetailData.buyDate }}</div>
          </div>
        </div>
        <div class="content_top_center">
          <div class="top_center_top">
            <div>
              <img src="@/assets/images/elevator/bg-elevator.png" alt="" />
            </div>
            <div>
              <div v-for="(item, index) in serverData" :key="index">
                <p>{{ item.name }}</p>
                <p>
                  <span v-if="item.formatter">{{ item.formatter(item) }}</span>
                  <span v-else>{{ item.value || '-' }}</span> {{ item.unit || '' }}
                </p>
              </div>
            </div>
          </div>
          <div class="top_center_bottom">
            <div>
              <div v-for="(item, index) in elevatorServerData" :key="index" class="left_parameter">
                <div>
                  {{ item.name }}<i class="el-icon-caret-right right-icon"></i>
                  <div class="point_cri"></div>
                  <div class="left_line"></div>
                </div>
                <div>
                  <div>
                    <span>{{ item.value }}</span><span>{{ item.unit }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <div class="top_red_warn">
                <div class="red_warn_left">
                  <p>七日报警次数</p>
                  <span class="red_warn_span">{{ elevatorDetailData.alarm }}</span>
                </div>
                <div class="red_warn_right">
                  <p>七日预警次数</p>
                  <span class="red_warn_span">{{ elevatorDetailData.proAlarm }}</span>
                </div>
              </div>
              <div class="bottom_green_warn">
                <div class="green_warn_left">
                  <p>上次维保时间</p>
                  <span class="green_warn_span">{{ elevatorDetailData.lastMaintenance || '无' }}</span>
                </div>
                <div class="green_warn_right">
                  <p>下次维保时间</p>
                  <span class="green_warn_span">{{ elevatorDetailData.nextMaintenance || '无' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="content_top_right">
          <div class="top_video_camera">
            <rtspCavas ref="rtspCavas" :rtspUrl="videoUrl" :videoName="videoName"
              :hasCavas="Boolean(videoTabsList.length)" class="video_preview"></rtspCavas>
            <!-- <div class="Intercom_box" @click="openCameraTalkDialog">
              <span><svg-icon name="phone-talk" /> 开启对讲</span>
            </div> -->
            <!-- <div class="video_tab">
              <el-tabs v-model="selectVideoTabs" type="card" @tab-click="videoChange">
                <el-tab-pane v-for="item in videoTabsList" :key="item.vidiconId" :label="item.vidiconName" :name="item.vidiconId"> </el-tab-pane>
              </el-tabs>
            </div> -->
          </div>
          <div class="bottom_warn_table">
            <div class="echarts_title">
              <div>报警类型统计</div>
              <div class="btnGroup">
                <div class="btn" :class="{ 'btn-active': timeType == 0 }" @click="dateChange(0)">今日</div>
                <div class="btn" :class="{ 'btn-active': timeType == 2 }" @click="dateChange(2)">本月</div>
                <div class="btn" :class="{ 'btn-active': timeType == 3 }" @click="dateChange(3)">本年</div>
              </div>
            </div>
            <div v-if="!warnTypeShow" class="echart-null">
              <img src="@/assets/images/null.png" alt="" />
              <div>暂无数据~</div>
            </div>
            <div v-else style="width: 100%; height: calc(100% - 20px); position: relative">
              <div id="warnTypePieEcharts"></div>
              <div class="warnTypeCenter">
                <img src="@/assets/images/elevator/icon-warn-type-pie.png" alt="" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="content_bottom">
        <div class="content_bottom_pie">
          <div class="echarts_title">偏移趋势</div>
          <div v-if="!offsetTrendShow" class="echart-null">
            <img src="@/assets/images/null.png" alt="" />
            <div>暂无数据~</div>
          </div>
          <div v-else style="width: 100%; height: calc(100% - 20px); position: relative">
            <div id="offsetTrendLineEcharts"></div>
          </div>
        </div>
        <div class="content_bottom_col">
          <div class="echarts_title">开关门次数统计</div>
          <div v-if="!doorNumsShow" class="echart-null">
            <img src="@/assets/images/null.png" alt="" />
            <div>暂无数据~</div>
          </div>
          <div v-else style="width: 100%; height: calc(100% - 20px); position: relative">
            <div id="doorNumsBarEcharts"></div>
          </div>
        </div>
        <div class="content_bottom_line">
          <el-table v-scrollbarHover v-el-table-infinite-scroll="tableLoad" v-loading="tableLoading"
            class="table-center-transfer" :data="tableData" height="100%" :cell-style="$tools.setCell(3)"
            :header-cell-style="$tools.setHeaderCell(3)" style="width: 100%">
            <el-table-column fixed prop="iphPoliceTime" show-overflow-tooltip label="报警时间"></el-table-column>
            <el-table-column fixed prop="iphParameterName" show-overflow-tooltip label="报警类型"></el-table-column>
            <el-table-column fixed prop="disposeResultName" show-overflow-tooltip label="处置状态"><template
                slot-scope="scope">
                <div :style="scope.row.iphDisposeResult == '0' ? 'color:#5188fc' : ''">
                  {{ scope.row.disposeResultName }}
                </div>
              </template></el-table-column>
          </el-table>
        </div>
      </div>
      <template v-if="cameraTalkDialogShow">
        <cameraTalkDialog :visible.sync="cameraTalkDialogShow" :cameraInfo="selectVideoTabs" />
      </template>
    </div>
  </PageContainer>
</template>
<script>
import moment from 'moment'
import * as echarts from 'echarts'
export default {
  name: 'elevatorDetail',
  components: {
    cameraTalkDialog: () => import('./cameraTalkDialog.vue')
  },
  async beforeRouteLeave(to, from, next) {
    if (!this.routerNameList.includes(to.name)) {
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    const routerNameList = ['elevatorMonitor']
    return {
      routerNameList: Object.freeze(routerNameList),
      requestHttp: __PATH.VUE_IEMC_ELEVATOR_API,
      warnTypeShow: false,
      doorNumsShow: false,
      offsetTrendShow: false,
      tableLoading: false,
      selectVideoTabs: {},
      videoUrl: '',
      videoName: '',
      videoTabsList: [],
      tableData: [],
      pageData: {
        page: 1,
        pageSize: 15,
        total: 0
      },
      serverData: [
        {
          name: '运行状态',
          value: null,
          field: '2741',
          unit: ''
        },
        {
          name: '是否载人',
          value: null,
          field: '2745',
          unit: ''
        },
        {
          name: '当前速度',
          value: '',
          unit: '',
          field: '2711'
        },
        {
          name: '左右偏移',
          value: '',
          field: '2698',
          unit: ''
        },
        {
          name: '前后偏移',
          value: '',
          field: '2696',
          unit: ''
        },
        {
          name: '当前楼层',
          value: null,
          field: '2700',
          unit: ''
        },
        {
          name: '梯门状态',
          value: null,
          field: '2713',
          unit: ''
        },
        {
          name: '运行振幅',
          value: '',
          field: '2694',
          unit: ''
        },
        {
          name: '温度',
          value: '',
          field: '2748',
          unit: ''
        },
        {
          name: '湿度',
          value: '',
          field: '2750',
          unit: ''
        }
      ], // 电梯运行数据
      elevatorDetailData: {
        surveyEntityName: '',
        alarm: 0,
        proAlarm: 0,
        lastMaintenance: '',
        nextMaintenance: '',
        assetsName: '',
        model: '',
        registrationNum: '',
        useDepartmentName: '',
        buyDate: ''
      },
      elevatorServerData: [
        {
          name: '运行时长',
          value: '',
          field: '2641',
          unit: ''
        },
        {
          name: '运行距离',
          value: '',
          field: '2639',
          unit: ''
        },
        {
          name: '运行次数',
          value: '',
          field: '2637',
          unit: ''
        },
        {
          name: '开门次数',
          value: '',
          field: '2634',
          unit: ''
        }
      ],
      videoList: [],
      queryData: {
        surveyEntityCode: '',
        projectCode: ''
      },
      cameraTalkDialogShow: false,
      refreshTimer: null,
      timeType: 2
    }
  },
  // 监听侧边栏缩放 动态改变echarts图表大小
  watch: {
    '$store.state.settings.sidebarCollapse': {
      handler(val) {
        this.$nextTick(() => {
          let echartsDom = ['warnTypePieEcharts', 'doorNumsBarEcharts', 'offsetTrendLineEcharts']
          setTimeout(() => {
            echartsDom.forEach((item) => {
              if (document.getElementById(item)) {
                echarts.init(document.getElementById(item)).resize()
              }
            })
          }, 250)
        })
      },
      deep: true
    }
  },
  destroyed() {
    clearInterval(this.refreshTimer)
  },
  mounted() {
    if (!this.$store.state.keepAlive.list.some((e) => this.routerNameList.includes(e))) {
      this.initEvent()
    }
  },
  activated() {
    this.initEvent()
  },
  methods: {
    // 返回
    goBack() {
      this.$router.push('/elevatorMenu/elevatorMonitor')
    },
    initEvent() {
      this.queryData.surveyEntityCode = this.$route.query.surveyEntityCode
      this.queryData.projectCode = this.$route.query.projectCode
      this.timeType = 2
      this.getWarnTypePieData()
      this.getContorlDoorNumData()
      this.getOffsetTrendData()
      this.getElevatorParticulars()
      this.getData()
      this.getVideoList()
      this.refreshTimer = setInterval(() => {
        this.getElevatorParticulars('refresh')
      }, 10000)
    },
    getLegendLength() {
      let legendLength = 0
      let innerHeight = window.innerHeight
      if (innerHeight < 768) {
        legendLength = 2
      }
      if (innerHeight >= 768 && 900 > innerHeight) {
        legendLength = 3
      }
      if (innerHeight >= 900 && 1080 > innerHeight) {
        legendLength = 5
      }
      if (innerHeight >= 1080) {
        legendLength = 5
      }
      return legendLength
    },
    // 接收 socket
    setDetailWebSocket(data) {
      if (this.queryData.surveyEntityCode == data.surveyEntityCode) {
        this.getElevatorParticulars()
      }
    },
    // 获取电梯详情
    getElevatorParticulars(type = 'init') {
      const params = {
        projectCode: this.queryData.projectCode,
        // entityMenuCode: "3aacbd7d672648729ceeb61c93d24fb8",
        surveyCode: this.queryData.surveyEntityCode,
        startTime: moment().format('YYYY-MM-DD'),
        endTime: moment().format('YYYY-MM-DD')
      }
      this.$api.getElevatorParticulars(params, this.requestHttp).then((res) => {
        if (res.code === '200') {
          const data = res.data?.resRealTable ?? false
          if (!data) return
          if (type === 'init' && data.assetsNumber) {
            this.getAssetDetailsById(data.assetsNumber)
          }
          Object.assign(this.elevatorDetailData, {
            surveyEntityName: data.surveyEntityName,
            alarm: res.data.alarm,
            proAlarm: res.data.proAlarm
          })
          data.parameterList?.forEach((item) => {
            this.serverData.forEach((e) => {
              if (e.field === item.parameterId) {
                e.value = !this.isNumber(item.parameterValue) ? Number(item.parameterValue) : item.parameterValue
                e.unit = item.parameterUnit
              }
            })
            this.elevatorServerData.forEach((e) => {
              if (e.field === item.parameterId) {
                e.value = !this.isNumber(item.parameterValue) ? Number(item.parameterValue) : item.parameterValue
                e.unit = item.parameterUnit
              }
            })
          })
        }
      })
    },
    // 获取资产详情
    getAssetDetailsById(assetsId) {
      this.$api.getAssetDetailsByAssetIds({ assetsId }).then((res) => {
        const data = res.data
        Object.assign(this.elevatorDetailData, {
          assetsName: data.assetName,
          model: data.assetModel,
          registrationNum: data.assetSn,
          useDepartmentName: data.userDepartmentName,
          buyDate: data.startDate,
          lastMaintenance: '',
          nextMaintenance: ''
        })
      })
    },
    isNumber(str) {
      return parseFloat(str).toString() === 'NaN'
    },
    // 获取报警类型统计数据
    getWarnTypePieData() {
      this.$api
        .getReasonStatisticPie(
          {
            projectCode: this.queryData.projectCode,
            surveyCode: this.queryData.surveyEntityCode,
            dateType: this.timeType
          },
          this.requestHttp
        )
        .then((res) => {
          if (res.code == 200) {
            if (res.data.length > 0) {
              this.warnTypeShow = true
              this.$nextTick(() => {
                this.setWarnTypePieEcharts(res.data)
              })
            } else {
              this.warnTypeShow = false
            }
          }
        })
    },
    dateChange(val) {
      this.timeType = val
      this.getWarnTypePieData()
    },
    // 报警列表
    getData() {
      this.tableLoading = true
      this.$api
        .getStaticPoliceList(
          {
            projectCode: this.queryData.projectCode,
            surveyCode: this.queryData.surveyEntityCode,
            pageNo: this.pageData.page,
            pageSize: this.pageData.pageSize,
            startTime: '',
            endTime: ''
          },
          this.requestHttp
        )
        .then((res) => {
          this.tableLoading = false
          if (res.code == 200) {
            this.tableData = res.data.list
            this.pageData.total = res.data.count
          }
        })
    },
    tableLoad() {
      if (this.pageData.total > this.pageData.page * this.pageData.pageSize) {
        this.pageData.page++
        this.getData()
      }
    },
    // 摄像机列表
    getVideoList() {
      this.$api
        .getVideoList(
          {
            surveyCode: this.queryData.surveyEntityCode
          },
          this.requestHttp
        )
        .then((res) => {
          if (res.code == 200) {
            this.videoTabsList = res.data
            if (res.data.length) {
              this.selectVideoTabs = res.data[0]
              this.getHlvAddress(this.selectVideoTabs)
            }
          }
        })
    },
    // 获取摄像机地址
    getHlvAddress(item) {
      const params = {
        cameraId: item.vidiconId
      }
      this.$api.getHlvAddress(params, this.requestHttp).then((res) => {
        if (res.code == 200) {
          this.videoName = item.vidiconName
          this.videoUrl = res.data
        }
      })
    },
    // 改变摄像机地址
    videoChange() {
      this.getHlvAddress(this.selectVideoTabs)
    },
    // 获取开关门次数统计数据
    getContorlDoorNumData() {
      const params = {
        surveyCode: this.queryData.surveyEntityCode
      }
      this.$api.getOpenNumByDays(params, this.requestHttp).then((res) => {
        if (res.code === '200') {
          this.doorNumsShow = true
          this.$nextTick(() => {
            this.setContorlDoorNumBarEcharts(res.data)
          })
        } else {
          this.doorNumsShow = false
        }
      })
    },
    // 获取偏移趋势
    getOffsetTrendData() {
      const params = {
        surveyCode: this.queryData.surveyEntityCode
      }
      this.$api.getDeviationByDays(params, this.requestHttp).then((res) => {
        if (res.code === '200') {
          this.offsetTrendShow = true
          this.$nextTick(() => {
            this.setOffsetTrendLineEcharts(res.data)
          })
        } else {
          this.offsetTrendShow = false
        }
      })
    },
    // 报警类型统计Echarts
    setWarnTypePieEcharts(chartdata) {
      const getchart = echarts.init(document.getElementById('warnTypePieEcharts'))
      const nameList = Array.from(chartdata, (item) => item.name)
      let sum = 0
      let warnData = []
      chartdata.forEach((item) => {
        sum += Number(item.value)
      })
      const colorIn = ['rgba(246, 180, 58, 1)', 'rgba(183, 187, 202, 1)', 'rgba(81, 136, 252, 1)', 'rgba(0, 221, 197, 1)', 'rgba(255, 86, 84, 1)']
      const colorOut = ['rgba(246, 180, 58, .4)', 'rgba(183, 187, 202, .4)', 'rgba(81, 136, 252, .4)', 'rgba(0, 221, 197, .4)', 'rgba(255, 86, 84, .4)']
      chartdata.forEach((item, index) => {
        const randomRgbColor = this.$tools.randomRgbColor('array')
        if (!colorIn[index] || !colorOut[index]) {
          colorIn.push(randomRgbColor[1])
          colorOut.push(randomRgbColor[0])
        }
        warnData.push(item)
        // warnData.push(item, {
        //   value: sum / 100,
        //   labelLine: {
        //     show: false,
        //     lineStyle: {
        //       color: 'transparent'
        //     }
        //   },
        //   itemStyle: {
        //     color: 'transparent'
        //   }
        // })
      })
      // colorIn数组每个元素后插入空字符
      colorIn.forEach((item, index) => {
        colorIn.splice(2 * index + 1, 0, '')
        colorOut.splice(2 * index + 1, 0, '')
      })
      const that = this
      const option = {
        legend: {
          type: 'scroll',
          orient: 'vertical',
          top: 'center',
          right: '0%',
          data: nameList,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 16,
          textStyle: {
            //  fontSize: 18,//字体大小
            color: '#353535' //  字体颜色
          },
          tooltip: {
            show: true,
            confine: true // 限制tootip在容器内
          },
          formatter: function (name) {
            var oa = option.series[0].data
            var num = oa.reduce((sum, e) => sum + e.value, 0)
            for (var i = 0; i < option.series[0].data.length; i++) {
              if (name === oa[i].name) {
                return (
                  ' ' +
                  (name.length > that.getLegendLength() ? name.substr(0, that.getLegendLength()) + '...' : name) +
                  ' (' +
                  oa[i].value +
                  ')   ' +
                  ((oa[i].value / num) * 100).toFixed(2) +
                  '%'
                )
              }
            }
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{b} : {c} ({d}%)',
          confine: true // 限制tootip在容器内
        },
        series: [
          {
            type: 'pie',
            radius: ['50%', '70%'],
            center: ['30%', '50%'],
            hoverAnimation: false,
            startAngle: 180,
            selectedMode: 'single',
            label: {
              show: false
            },
            itemStyle: {
              normal: {
                borderWidth: 3,
                borderColor: '#fff',
                color: function (params) {
                  return colorOut[params.dataIndex]
                }
              }
            },
            data: warnData,
            z: 666
          },
          {
            type: 'pie',
            radius: ['70%', '75%'],
            center: ['30%', '50%'],
            hoverAnimation: false,
            startAngle: 180,
            selectedMode: 'single',
            label: {
              show: false
            },
            itemStyle: {
              normal: {
                borderWidth: 3,
                borderColor: '#fff',
                color: function (params) {
                  return colorIn[params.dataIndex]
                }
              }
            },
            data: warnData,
            z: 1
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 开关门次数统计Echarts
    setContorlDoorNumBarEcharts(chartdata) {
      const getchart = echarts.init(document.getElementById('doorNumsBarEcharts'))
      const nameList = chartdata.seriesData
      const valueList = chartdata.yAxisData
      const option = {
        backgroundColor: '#fff',
        color: [
          new echarts.graphic.LinearGradient(
            0,
            1,
            0,
            0,
            [
              {
                offset: 0,
                color: '#13ace8'
              },
              {
                offset: 1,
                color: '#5188FC'
              }
            ],
            false
          )
        ],
        grid: {
          left: '0%',
          right: '0%',
          top: '20%',
          bottom: '0%',
          containLabel: true
        },
        tooltip: {
          show: 'true',
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        yAxis: {
          type: 'value',
          name: '单位：次',
          axisTick: {
            show: false
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: '#000000'
            }
          },
          axisLabel: {
            show: true,
            color: '#000000',
            fontSize: 14
          },
          splitLine: {
            show: true
          }
        },
        xAxis: [
          {
            axisTick: {
              show: false
            },
            type: 'category',
            axisLine: {
              show: false
            },
            axisLabel: {
              show: true,
              color: '#000000',
              // interval: 0,
              rotate: 30
            },
            splitLine: {
              show: false
            },
            data: nameList
          }
        ],
        series: [
          {
            name: '',
            type: 'bar',
            barWidth: 10,
            label: {
              normal: {
                show: false,
                position: 'right',
                textStyle: {
                  color: '#ccc',
                  fontSize: 14
                }
              }
            },
            data: valueList
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 偏移趋势Echarts
    setOffsetTrendLineEcharts(chartdata) {
      const getchart = echarts.init(document.getElementById('offsetTrendLineEcharts'))
      const nameList = chartdata.deviationDates
      const valueList = chartdata.aroundDeviations
      const value2List = chartdata.lrDeviations
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['前后偏移', '左右偏移'],
          right: 5,
          textStyle: {
            color: '#393A3D'
          }
        },
        grid: {
          left: '2%',
          right: '0%',
          bottom: '0%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          },
          // 轴线上的字
          axisLabel: {
            show: true,
            textStyle: {
              fontSize: '14',
              color: '#000000'
            }
          },
          data: nameList
        },
        yAxis: [
          {
            type: 'value',
            splitNumber: 4,
            axisTick: {
              show: false
            },
            // 轴线上的字
            axisLabel: {
              textStyle: {
                fontSize: '14',
                color: '#000000'
              }
            },
            axisLine: {
              show: false
            }
          }
        ],
        series: [
          {
            name: '前后偏移',
            type: 'line',
            smooth: false, // 是否平滑曲线显示
            showAllSymbol: true,
            symbol: 'circle',
            symbolSize: 8,
            itemStyle: {
              color: '#3eb5dd',
              borderColor: '#f1f1f1',
              borderWidth: 1
            },
            lineStyle: {
              normal: {
                width: 2,
                color: {
                  type: 'linear',
                  colorStops: [
                    {
                      offset: 0,
                      color: '#00DDC5' // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: '#00DDC5' // 100% 处的颜色
                    }
                  ],
                  globalCoord: false // 缺省为 false
                },
                shadowColor: 'rgba(0, 221, 197, .5)',
                shadowBlur: 30,
                shadowOffsetY: 5
              }
            },
            areaStyle: {
              // 区域填充样式
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(0, 221, 197, 0.6)'
                    },
                    {
                      offset: 0.6,
                      color: 'rgba(0, 221, 197, 0.2)'
                    },
                    {
                      offset: 0.8,
                      color: 'rgba(58,182,224, 0.01)'
                    }
                  ],
                  false
                ),
                shadowColor: 'rgba(58,182,224, 0.1)',
                shadowBlur: 6
              }
            },
            data: valueList
          },
          {
            name: '左右偏移',
            type: 'line',
            smooth: false, // 是否平滑曲线显示
            showAllSymbol: true,
            symbol: 'circle',
            symbolSize: 8,
            itemStyle: {
              color: '#5B8FF9 ',
              borderColor: '#f1f1f1',
              borderWidth: 1
            },
            lineStyle: {
              normal: {
                width: 2,
                color: {
                  type: 'linear',
                  colorStops: [
                    {
                      offset: 0,
                      color: '#5B8FF9' // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: '#5B8FF9' // 100% 处的颜色
                    }
                  ],
                  globalCoord: false // 缺省为 false
                },
                shadowColor: 'rgba(91, 143, 249, .5)',
                shadowBlur: 12,
                shadowOffsetY: 5
              }
            },
            areaStyle: {
              // 区域填充样式
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(91, 143, 249, .6)'
                    },
                    {
                      offset: 0.6,
                      color: 'rgba(91, 143, 249, .2)'
                    },
                    {
                      offset: 1,
                      color: 'rgba(255,107,113, 0.01)'
                    }
                  ],
                  false
                ),
                shadowColor: 'rgba(91, 143, 249, .4)',
                shadowBlur: 6
              }
            },
            data: value2List
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    openCameraTalkDialog() {
      this.cameraTalkDialogShow = true
    },
    backEvent() {
      this.$emit('closeElevator')
    }
  }
}
</script>
<style lang="scss" scoped>
.monitorDetails {
  margin: 16px 16px 16px 0;
  p {
    margin: 0;
  }
  ::v-deep .container-header {
    margin-left: 16px;
    border-radius: 4px;
  }
  ::v-deep .container-content {
    margin-left: 16px;
    // margin-top: 12px;
    border-radius: 4px;
  }
  .monitorDetails-heade {
    display: flex;
    align-items: center;
    padding: 0 16px;
    height: 40px;
    line-height: 40xp;
    font-size: 14px;
    font-weight: 500;
    color: #121f3e;
    font-family: 'PingFang SC-Medium', 'PingFang SC';
    border-bottom: 1px solid #dcdfe6;
    .el-icon-arrow-left {
      font-size: 13px;
      margin-right: 8px;
      cursor: pointer;
      color: #7f848c;
    }
    > span {
      cursor: pointer;
    }
  }
  .content-details {
    height: 100%;
    width: 100%;
    background: #fff;
    padding: 10px 30px;
    min-height: 728px;
    .content_top {
      height: calc(70% - 10px);
      margin-bottom: 10px;
      display: flex;
      .content_top_left {
        width: 16%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .detail_list {
          font-family: 'HarmonyOS_Sans_SC';
          height: 20%;
          p {
            font-size: 14px;
            margin: 8px 0;
            color: #606266;
          }
          div {
            font-size: 16px;
            color: #121f3e;
            background: #f4f8ff;
            border-radius: 6px;
            border: 1px solid #c2d6ff;
            padding: 0 15px;
            height: calc(100% - 50px);
            box-sizing: border-box;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            font-family: 'PingFang SC-Medium', 'PingFang SC';
            img {
              vertical-align: middle;
              height: 100%;
            }
          }
        }
      }
      .content_top_center {
        width: 54%;
        height: 100%;
        padding-left: 40px;
        box-sizing: border-box;
        .top_center_top {
          height: 40%;
          width: 100%;
          display: flex;
          > div:first-child {
            width: 20%;
            height: 100%;
            display: flex;
            position: relative;
            img {
              width: 80%;
              height: 75%;
              margin: auto 0;
            }
            &::before {
              width: 28px;
              height: 28px;
              display: block;
              position: absolute;
              background: url('~@/assets/images/elevator/point-big.png') no-repeat;
              background-size: 28px 28px;
              bottom: calc(10% - 10px);
              left: -14px;
              z-index: 2;
              content: '';
            }
          }
          > div:last-child {
            width: 80%;
            height: 100%;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            > div {
              width: calc(20% - 10px);
              height: calc(50% - 12px);
              background: url('~@/assets/images/elevator/bg-blue-select.png') no-repeat;
              background-size: 100% 100%;
              background-position: center;
              display: flex;
              justify-content: space-around;
              flex-direction: column;
              padding: 8px 0;
              box-sizing: border-box;
              p {
                text-align: center;
                font-size: 14px;
                font-family: 'HarmonyOS_Sans_SC';
                color: #fff;
              }
              p:last-child {
                font-size: 18px;
              }
            }
          }
        }
        .top_center_bottom {
          height: calc(60% - 20px);
          margin-top: 20px;
          display: flex;
          > div:first-child {
            width: 45%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            .left_parameter {
              height: calc(25% - 20px);
              display: flex;
              justify-content: space-between;
              > div:first-child {
                width: 30%;
                height: fit-content;
                text-align: center;
                font-size: 15px;
                font-family: 'HarmonyOS_Sans_SC_Medium';
                color: #888;
                padding: 10px;
                border-bottom: 1px solid #d9dfe8;
                position: relative;
                .right-icon {
                  position: absolute;
                  bottom: calc(0% - 8px);
                  right: -8px;
                }
                .point_cri {
                  width: 22px;
                  height: 22px;
                  background: url('~@/assets/images/elevator/point-small.png') no-repeat;
                  background-size: 100% 100%;
                  position: absolute;
                  left: -11px;
                  bottom: calc(0% - 11px);
                  z-index: 2;
                }
                .left_line {
                  width: 1px;
                  height: 250%;
                  background: #d9dfe8;
                  position: absolute;
                  left: -1px;
                  bottom: 0;
                }
              }
              > div:last-child {
                width: calc(70% - 20px);
                background: url('~@/assets/images/elevator/bg-rect.png') no-repeat;
                background-size: 100% 100%;
                box-sizing: border-box;
                padding: 5px 20px;
                display: flex;
                div {
                  margin: auto 0;
                  width: 100%;
                  border-bottom: 1px solid;
                  display: flex;
                  justify-content: space-evenly;
                  align-items: baseline;
                  padding: 5px 20px 5px 0;
                  // border渐变线
                  border-image: linear-gradient(270deg, rgb(219 223 226 / 0%), rgb(219 223 226 / 100%)) 1 1;
                  :first-child {
                    font-size: 22px;
                    font-family: 'HarmonyOS_Sans_SC_Medium';
                    color: #5188fc;
                  }
                  :last-child {
                    font-size: 14px;
                    font-family: 'HarmonyOS_Sans_SC';
                    color: #888;
                    margin-left: 20px;
                  }
                }
              }
            }
          }
          > div:last-child {
            width: 55%;
            height: 100%;
            display: flex;
            justify-content: space-between;
            flex-direction: column;
            .top_red_warn {
              background: url('~@/assets/images/elevator/ic-rect-red.png') no-repeat;
              background-size: 100% 100%;
            }
            .bottom_green_warn {
              background: url('~@/assets/images/elevator/ic-rect-green.png') no-repeat;
              background-size: 100% 100%;
            }
            .top_red_warn,
            .bottom_green_warn {
              width: 95%;
              height: calc(50% - 8px);
              aspect-ratio: 73/27;
              display: flex;
              justify-content: space-between;
              padding: 0 25px;
            }
            .red_warn_left,
            .red_warn_right,
            .green_warn_left,
            .green_warn_right {
              display: flex;
              justify-content: space-evenly;
              flex-direction: column;
              text-align: left;
              p {
                font-size: 14px;
                font-family: 'HarmonyOS_Sans_SC';
                color: #606266;
              }
              .red_warn_span {
                font-size: 26px;
                font-family: 'HarmonyOS_Sans_SC_Medium';
                color: #c71717;
              }
              .green_warn_span {
                font-size: 18px;
                font-family: 'HarmonyOS_Sans_SC_Medium';
                color: #353535;
              }
            }
            .red_warn_right,
            .green_warn_right {
              text-align: right;
            }
          }
        }
      }
      .content_top_right {
        padding-left: 30px;
        width: 30%;
        height: 100%;
        .bottom_warn_table {
          height: 45%;
          width: 100%;
          position: relative;
          padding-top: 10px;
          .warnTypeCenter {
            position: absolute;
            width: 25%;
            aspect-ratio: 1/1;
            // background: #fff;
            border-radius: 50px;
            background: linear-gradient(112deg, rgb(81 136 252 / 0%), rgb(81 136 252 / 100%));
            left: 30%;
            top: 50%;
            transform: translateX(-50%) translateY(-50%);
            display: flex;
            img {
              margin: auto;
            }
            &::before {
              content: '';
              display: block;
              position: absolute;
              width: calc(100% - 1px);
              height: calc(100% - 1px);
              border-radius: 50px;
              background: #fff;
              z-index: -1;
            }
          }
        }
        .top_video_camera {
          height: 55%;
          width: 100%;
          .video_preview {
            height: calc(100% - 0px);
            // height: calc(100% - 50px);
            background: #000;
          }
          .video_tab {
            padding-top: 10px;
          }
          .Intercom_box {
            height: 36px;
            line-height: 36px;
            font-size: 14px;
            font-family: 'PingFang HK-Regular', 'PingFang HK';
            font-weight: 400;
            color: #fff;
            background: #00bc6d;
            border-radius: 4px;
            text-align: center;
            cursor: pointer;
            svg {
              font-size: 18px;
              vertical-align: middle;
            }
          }
        }
      }
    }
    .content_bottom {
      height: 30%;
      width: 100%;
      display: flex;
      justify-content: space-between;
      .content_bottom_pie {
        width: 20%;
        position: relative;
      }
      .content_bottom_col {
        width: 50%;
        padding-left: 20px;
      }
      .content_bottom_line {
        width: 30%;
        padding-left: 30px;
      }
    }
  }
}
.echarts_title {
  height: 18px;
  font-size: 14px;
  font-family: NotoSansHans-Medium, NotoSansHans;
  font-weight: 600;
  color: #393a3d;
  line-height: 18px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .btnGroup {
    display: flex;
    align-items: center;
    .btn {
      width: 40px;
      background-color: #faf9fc;
      font-size: 14px;
      border-radius: 4px;
      color: #414653;
      cursor: pointer;
      height: 24px;
      line-height: 24px;
      margin: 0 4px;
      text-align: center;
    }
    .btn-active {
      background-color: #e6effc !important;
      color: #3562db !important;
    }
  }
}
.echart-null {
  margin: 0 auto;
  height: calc(100% - 20px);
  width: 50%;
  text-align: center;
  color: #8a8c8f;
  img {
    width: 100%;
    height: 100%;
    max-width: 80%;
    max-height: 80%;
  }
  div {
    font-size: 14px;
  }
}
#warnTypePieEcharts,
#doorNumsBarEcharts,
#offsetTrendLineEcharts {
  width: 100%;
  height: 100%;
  z-index: 2;
}
::v-deep .el-table__body-wrapper {
  // 整个滚动条
  &::-webkit-scrollbar {
    width: 0; // 纵向滚动条的宽度
    background: rgb(213 215 220 / 30%);
    border: none;
  }
  // 滚动条轨道
  &::-webkit-scrollbar-track {
    border: none;
  }
}
// --------------------隐藏table gutter列和内容区右侧的空白 start
::v-deep .el-table th.gutter {
  display: none;
  width: 0;
}
::v-deep .el-table colgroup col[name='gutter'] {
  display: none;
  width: 0;
}
// 这个样式不加的话内容哪里会缺一点，估计是因为滚动条哪里缺的没有补上
::v-deep .el-table__body,
::v-deep .el-table__fixed {
  width: 100% !important;
}
// --------------------隐藏table gutter列和内容区右侧的空白 end
</style>
<style lang="scss">
.video_tab {
  .el-tabs__header {
    margin: 0;
    .el-tabs__item:first-child {
      // border-left: 1px solid #d9dfe8;
    }
    .el-tabs__nav {
      border: none !important;
    }
    .el-tabs__nav-next,
    .el-tabs__nav-prev {
      line-height: 35px !important;
    }
    .el-tabs__item {
      margin-right: 10px;
      border: 1px solid #b9b9b9 !important;
      background: #fcfcfc;
      height: 32px !important;
      line-height: 32px !important;
    }
    .is-active {
      background: #f4f8ff;
      border: 1px solid #5188fc !important;
    }
  }
  .el-tabs__nav-wrap {
    border-bottom: none !important;
  }
}
.table-center-transfer {
  .el-table__fixed-header-wrapper .el-table__header thead {
    tr {
      background: center !important;
      .el-table__cell {
        border-right: none !important;
        .cell {
          font-size: 16px;
          font-family: NotoSansHans-Medium, NotoSansHans;
          font-weight: 500;
          color: #647181;
        }
      }
    }
  }
  table.el-table__header thead th {
    background: transparent !important;
  }
  td.el-table__cell {
    border-bottom: 1px solid #d8dee7 !important;
  }
  .el-table__fixed-body-wrapper {
    border-left: 1px solid #d8dee7;
    border-right: 1px solid #d8dee7;
    width: calc(100% - 1px);
  }
}
</style>
