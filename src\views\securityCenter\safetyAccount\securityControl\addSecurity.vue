<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="content_box">
        <el-form ref="formInline" class="dialog-form" :model="formInline" :inline="true" :rules="rules" label-position="right" label-width="110px">
          <el-form-item label="小组名称" prop="teamName">
            <el-input v-if="!disabled" v-model.trim="formInline.teamName" style="width: 230px;" show-word-limit maxlength="50" placeholder="请输入小组名称"></el-input>
            <span v-else class="check">{{ formInline.teamName }}</span>
          </el-form-item>
          <el-form-item label="小组编码" prop="teamCode">
            <el-input v-if="!disabled" v-model.trim="formInline.teamCode" style="width: 230px;" show-word-limit maxlength="50" placeholder="请输入小组编码"></el-input>
            <span v-else class="check">{{ formInline.teamCode }}</span>
          </el-form-item>
          <br />
          <el-form-item label="组织类型" prop="structureType">
            <el-select v-if="!disabled" v-model="formInline.structureType" placeholder="请选择组织类型" :disabled="disabled" style="width: 230px;">
              <el-option v-for="item in structureTypeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
            <span v-else class="check">{{ getStructureType(formInline.structureType) }}</span>
          </el-form-item>
          <el-form-item label="上级部门" prop="allParentIds">
            <el-cascader
              v-if="!disabled"
              ref="allParent"
              v-model="formInline.allParentIds"
              style="width: 230px;"
              :options="bumenList"
              :props="bumenPropsType"
              :collapse-tags="true"
              placeholder="请选择上级部门"
              @change="bumenHangdleChange"
            ></el-cascader>
            <span v-if="disabled" class="check">{{ getNameByIdFromArr(formInline.allParentIds) }}</span>
          </el-form-item>
          <br />
          <el-form-item label="负责人" prop="responsiblePersonName">
            <template v-if="!disabled">
              <div v-if="tableDate" class="person_div el-input__inner" style="cursor: pointer;" @click="addPersonShow">
                <span v-if="tableDate.length == 0" style="color: #dcdfe3;" @click="addPersonShow">请选择负责人</span>
                <span v-else>{{ formInline.responsiblePersonName }}</span>
              </div>
            </template>
            <span v-else class="check">{{ formInline.responsiblePersonName }}</span>
          </el-form-item>
          <el-form-item v-if="!disabled" label="状态" prop="useStatus">
            <el-radio v-model="formInline.useStatus" label="1">启用</el-radio>
            <el-radio v-model="formInline.useStatus" label="2">禁用</el-radio>
          </el-form-item>
          <el-form-item v-else label="状态">
            <span class="check">{{ formInline.useStatus == '1' ? '启用' : '禁用' }}</span>
          </el-form-item>
          <br />
          <el-form-item label="管理区域" prop="areaId">
            <el-cascader
              v-if="!disabled"
              ref="myCascader"
              v-model="formInline.areaId"
              :options="riskLocalArrData"
              :props="riskPropsType"
              :collapse-tags="true"
              placeholder="请选择排查区域"
              style="width: 530px;"
              @change="hangdleChange"
            ></el-cascader>
            <span v-else style="width: 530px;">{{ formInline.areaName }}</span>
          </el-form-item>
          <br />
          <el-form-item label="安全生产职责" prop="productionResponsibility">
            <el-input
              v-if="!disabled"
              v-model.trim="formInline.productionResponsibility"
              type="textarea"
              :rows="8"
              style="width: 530px;"
              show-word-limit
              maxlength="800"
              placeholder="请输入安全生产职责"
            ></el-input>
            <span v-else style="width: 530px;">{{ formInline.productionResponsibility }}</span>
          </el-form-item>
          <br />
          <el-form-item label="备注" prop="remarks">
            <el-input
              v-if="!disabled"
              v-model.trim="formInline.remarks"
              type="textarea"
              :rows="8"
              style="width: 530px;"
              show-word-limit
              maxlength="800"
              placeholder="请输入备注"
            ></el-input>
            <span v-else style="width: 530px;">{{ formInline.remarks }}</span>
          </el-form-item>
        </el-form>
      </div>
      <addPerson :dialogVisible="dialogVisible" :tableDate="tableDate[0]" @closeDialog="closeDialog" @sure="sure"></addPerson>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">关闭</el-button>
      <el-button v-if="!disabled" type="primary" @click="complete()">保存</el-button>
    </div>
  </PageContainer>
</template>

<script>
import axios from 'axios'
import { transData } from '@/util'
import addPerson from './components/addperson'
export default {
  name: 'addSecurity',
  components: { addPerson },
  beforeRouteEnter(to, from, next) {
    if (to.meta.title) {
      const typeList = {
        add: '新增部门',
        edit: '编辑部门',
        check: '部门详情'
      }
      to.meta.title = typeList[to.query.type] ?? '部门详情'
    }
    next()
  },
  data() {
    return {
      typeList: [],
      type: '',
      dialogVisible: false,
      disabled: false,
      loading: false,
      title: '新增人员',
      formInline: {
        teamName: '',
        teamCode: '',
        structureType: '',
        responsiblePersonName: '',
        responsiblePersonId: '',
        responsiblePersonMobilePhone: '', // 负责人手机号
        areaId: '',
        useStatus: '1',
        productionResponsibility: '',
        remarks: '',
        riskPlaceIds: '',
        areaName: '', // 区域名称
        level: '',
        parentId: '',
        allParentIds: ''
      },
      riskPropsType: {
        children: 'children',
        label: 'gridName',
        value: 'id',
        checkStrictly: true,
        multiple: true
      },
      bumenPropsType: {
        children: 'children',
        label: 'teamName',
        value: 'id',
        checkStrictly: true,
        disabled: 'disabledFlag'
      },
      jueseList: [
        {
          name: '1部门',
          id: 1
        }
      ],
      structureTypeList: [
        {
          name: '院级',
          id: 1
        },
        {
          name: '科级',
          id: 2
        }
      ],
      bumenList: [],
      riskLocalArrData: [],
      rules: {
        useStatus: [{ required: true, message: '请选择状态', trigger: 'blur' }],
        teamName: [{ required: true, message: '请输入小组名称', trigger: 'blur' }],
        teamCode: [{ required: true, message: '请输入小组编码', trigger: 'blur' }],
        structureType: [{ required: true, message: '请选择组织类型', trigger: 'change' }]
      },
      tableCode: [],
      tableDate: [],
      staffNameArr: [],
      staffIdArr: [],
      checkedData: [],
      aaaid: [],
      contentLoading: false,
      bumenAllList: []
    }
  },
  mounted() {
    this.init()
    this.type = this.$route.query.type
    this.disabled = this.type == 'check'
    if (this.$route.query.tableCode) {
      this.tableCode = this.$route.query.tableCode
      console.log(this.tableCode, 'this.tableCode')
      let ids1 = this.tableCode.allParentIds.split(',')
      let ids2 = this.tableCode.level == 0 ? this.tableCode.id.split(',') : (this.tableCode.allParentIds + ',' + this.tableCode.id).split(',')
      // 上级部门是 当前的allids+ , +id
      this.formInline.allParentIds = this.tableCode.level >= 3 ? ids1 : ids2 // 所有上级部门ids  第三级不能当上级
      this.formInline.parentId = this.tableCode.level >= 3 ? this.tableCode.parentId : this.tableCode.id // 上级部门id
      this.formInline.level = this.tableCode.level >= 3 ? 3 : this.tableCode.level + 1 // 当前部门目录等级
    }
    if (this.type != 'add') {
      this.getControlGroupInfo()
    }
  },
  methods: {
    getNameByIdFromArr(ids) {
      if (ids) {
        let str = ''
        ids.forEach((item) => {
          this.bumenAllList.forEach((item1) => {
            if (item1.id == item) {
              str = str ? str + '>' + item1.teamName : str + item1.teamName
            }
          })
        })
        return str
      } else {
        return ''
      }
    },

    // 获取组织姓名
    getStructureType(val) {
      if (val) {
        return val == '1' ? '院级' : '科级'
      } else {
        return ''
      }
    },
    // 获取详情
    async getControlGroupInfo() {
      this.contentLoading = true
      this.$api.ipsmGetControlGroupInfo({ id: this.$route.query.id }).then((res) => {
        this.aaaid = res.data.areaId.split(',')
        this.tableDate = [{}]
        this.tableDate[0].name = res.data.responsiblePersonName
        this.tableDate[0].id = res.data.responsiblePersonId
        this.tableDate[0].phone = res.data.responsiblePersonMobilePhone
        if (res.data.allContent) {
          this.formInline = JSON.parse(res.data.allContent)
          this.formInline.allParentIds = res.data.allParentIds.split(',')
        } else {
          this.formInline = res.data
          this.formInline.useStatus = res.data.useStatus.toString()
          this.formInline.allParentIds = res.data.allParentIds.split(',')
          this.formInline.riskPlaceIds = res.data.areaId.split(',')
        }
        this.formInline.areaName = res.data.areaName
        // this.formInline.areaId = this.aaaid;
        var riskArray = res.data.areaId.split('>')
        this.formInline.areaId = []
        riskArray.forEach((item) => {
          this.formInline.areaId.push(item)
        })
        this.setAreaId()
        this.contentLoading = false
      })
    },
    // 回显部门
    setAreaId() {
      let ids = []
      this.aaaid.forEach((item) => {
        ids.push(this.changeDetSelect(item, this.riskLocalArrData))
      })
      // this.formInline.areaId = ids;
    },
    // 回显多选地区
    /**
     * treeData 列表数组
     */
    changeDetSelect(key, treeData) {
      let arr = [] // 在递归时操作的数组
      let returnArr = [] // 存放结果的数组
      let depth = 0 // 定义全局层级
      return childrenEach(treeData, depth)
      // 定义递归函数
      function childrenEach(childrenData, depthN) {
        for (var j = 0; j < childrenData.length; j++) {
          depth = depthN // 将执行的层级赋值 到 全局层级
          arr[depthN] = childrenData[j].id
          if (childrenData[j].id == key) {
            returnArr = arr.slice(0, depthN + 1) // 将目前匹配的数组，截断并保存到结果数组，
            break
          } else {
            if (childrenData[j].children) {
              depth++
              childrenEach(childrenData[j].children, depth)
            }
          }
        }
        return returnArr
      }
    },
    // 点击确定
    complete() {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          let data = {
            ...this.formInline,
            areaId: this.formInline.riskPlaceIds ? this.formInline.riskPlaceIds : '',
            allParentIds: this.formInline.allParentIds ? this.formInline.allParentIds.join(',') : '',
            allContent: JSON.stringify(this.formInline)
          }
          if (this.type != 'add') {
            data.id = this.$route.query.id
          }
          delete data.riskPlaceIds
          this.$api.ipsmSaveControlGroupInfo(data).then((res) => {
            if (res.code == 200) {
              this.$message.success(res.message)
              this.$router.go(-1)
            } else {
              return this.$message.error(res.message)
            }
          })
        }
      })
    },

    // 所属部门
    async init() {
      // 管理区域
      this.$api.ipsmGetGridList({}).then((res) => {
        let treeList = transData(res.data, 'id', 'parentId', 'children')
        this.riskLocalArrData = treeList
        if (this.type != 'add') {
          this.setAreaId()
        }
      })
      // 上级部门
      this.$api.ipsmGetControlGroupInfoList({}).then((res) => {
        let planText = {
          id: '#',
          teamName: '安全管控部门',
          parentId: '',
          allParentIds: '',
          level: 0
        }
        let list = res.data.list
        list.push(planText)
        this.bumenList = transData(list, 'id', 'parentId', 'children')
        this.bumenAllList = list
        this.treeLoading = false
        this.checkedData = this.bumenList[0]
      })
    },
    // 管理区域
    hangdleChange(val) {
      console.log(val)
      console.log(this.formInline.areaId)
      console.log(JSON.stringify(this.formInline.areaId))
      this.$nextTick(() => {
        let list = this.$refs.myCascader.getCheckedNodes()
        let name = []
        let id = []
        list.forEach((e) => {
          name.push(e.pathLabels.join('>'))
        })
        this.formInline.riskPlaceIds = val.join('>')
        // this.formInline.riskPlaceIds = id;
        this.formInline.areaName = name.join(',')
      })
    },
    // 上级部门
    bumenHangdleChange(val) {
      this.formInline.level = val.length
      this.formInline.parentId = val[val.length - 1]
      this.formInline.allParentIds = val
    },
    // 选择角色
    addPersonShow() {
      this.dialogVisible = true
    },
    sure(list) {
      if (list.length > 1) {
        return this.$message.error('最多选择一位负责人')
      }
      this.tableDate = list
      this.staffIdArr = list.map((o) => {
        return o.id
      })
      this.staffNameArr = list.map((o) => {
        return o.name
      })
      this.formInline.responsiblePersonName = list[0].name
      this.formInline.responsiblePersonId = list[0].id
      this.formInline.responsiblePersonMobilePhone = list[0].mobile
      this.dialogVisible = false
    },
    closeDialog() {
      this.dialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-textarea .el-input__count {
  line-height: normal;
}

.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
  // padding: 10px;
  // overflow-y: auto;
}

::v-deep .el-textarea__inner {
  min-height: 120px !important;
}

.content_box {
  height: 100%;
  margin-top: 10px;
  padding: 30px 25px 20px;
  background: #fff;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.form-inline {
  .el-input,
  .el-select,
  .el-cascader {
    width: 300px;
  }
}

.el-textarea {
  width: 400px;
}

.person_div {
  width: 230px;
  height: 32px;
  line-height: 32px;
  //   min-width: 230px;
  //   max-width: 230px;
  //   overflow-y: auto;
  //   overflow-x: hidden;
  //   display: inline-block;
  //   border-radius: 4px;
  //   vertical-align: top;
  //   padding-right: 20px;
  //   box-sizing: border-box;
  //   min-height: 42px;
  //   max-height: calc(42 * 3);
}

.check {
  display: inline-block;
  width: 230px;
}
</style>
