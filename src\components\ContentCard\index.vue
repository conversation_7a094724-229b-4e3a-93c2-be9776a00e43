<!--
 * @Author: hedd
 * @Date: 2023-02-27 14:35:19
 * @LastEditTime: 2023-11-30 20:38:31
 * @FilePath: \ihcrs_pc\src\components\ContentCard\index.vue
 * @Description:
-->
<template>
  <div class="box-card" :style="cstyle">
    <div v-if="showTitle" class="card-title">
      <svg-icon v-if="hasIcon" name="right-arrow" />
      <span class="card-name">
        <span v-if="greenLine" class="green_line"></span>
        <span v-if="required" style="color: #333; margin-right: 2px">*</span>
        {{ title }}
      </span>
      <span
        v-if="!isOpen && showOpen"
        class="card-open"
        style="margin-left: 10px"
        @click="
          () => {
            isOpen = true
          }
        "
      >
        展开
        <svg-icon name="down_icon" class="card-open-icon" />
      </span>
      <span
        v-if="isOpen && showOpen"
        class="card-open"
        style="margin-left: 10px"
        @click="
          () => {
            isOpen = false
          }
        "
      >
        收起
        <svg-icon name="up_icon" class="card-open-icon" />
      </span>
      <slot name="title-right" />
      <el-dropdown v-if="hasMoreOper.length" class="dropdown-btn" trigger="click" @command="(val) => $emit('more-oper-event', val)">
        <span class="more-operations" @click.stop.prevent>· · ·</span>
        <el-dropdown-menu slot="dropdown" class="dropdownSelect">
          <el-dropdown-item v-if="hasMoreOper.includes('more')" command="more">更多</el-dropdown-item>
          <el-dropdown-item v-if="hasMoreOper.includes('edit')" command="edit">编辑</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <div>
      <slot name="tabs" />
    </div>
    <div v-if="isOpen && scrollbarHover" ref="cardBody" v-scrollbarHover class="card-body" :style="{height: showTitle ? 'calc(100% - 35px)' : '100%'}">
      <slot name="content" />
    </div>
    <div v-if="isOpen && !scrollbarHover" ref="cardBody" class="card-body" :style="{height: showTitle ? 'calc(100% - 35px)' : '100%'}">
      <slot name="content" />
    </div>
  </div>
</template>
<script>
export default {
  name: 'ContentCard',
  props: {
    greenLine: {
      type: Boolean,
      default: false
    },
    // 是否展开
    showOpen: {
      type: Boolean,
      default: false
    },
    // 标题
    title: {
      type: String,
      default: ''
    },
    showTitle: {
      type: Boolean,
      default: true
    },
    // 自定义样式
    cstyle: {
      type: Object,
      default: () => {}
    },
    // 是否必填
    required: {
      type: Boolean,
      default: false
    },
    // 是否有更多操作
    hasMoreOper: {
      type: Array,
      default: () => []
    },
    // 是否直接显示滚动条
    scrollbarHover: {
      type: Boolean,
      default: false
    },
    hasIcon: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      isOpen: true
    }
  },
  watch: {
    '$store.state.settings.sidebarCollapse': {
      handler(val) {
        this.scrollbarEvent(this.$refs.cardBody)
      },
      deep: true
    },
    '$store.state.settings.dragSidebarCollapse': {
      handler(val) {
        this.scrollbarEvent(this.$refs.cardBody)
      },
      deep: true
    }
  },
  methods: {
    scrollbarEvent(el) {
      // 开启滚动条隐藏事件
      if (this.scrollbarHover) {
        this.$nextTick(() => {
          setTimeout(() => {
            if (el.scrollHeight > el.clientHeight) {
              el.style.paddingRight = '13px'
              el.style.overflowY = 'hidden'
              el.onmouseover = function () {
                el.style.overflowY = 'auto'
                el.style.paddingRight = '0px'
              }
              // 鼠标出
              el.onmouseout = function () {
                el.style.overflowY = 'hidden'
                el.style.paddingRight = '13px'
              }
            } else {
              // 移除onmouseover、移除onmouseout事件事件
              el.onmouseover = null
              el.onmouseout = null
            }
          }, 250)
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.box-card {
  background: #fff;
  border-radius: 4px;
  padding: 20px;

  .card-title {
    height: 25px;
    line-height: 25px;
    position: relative;
    display: flex;
    align-items: center;

    .card-name {
      font-size: 15px;
      font-family: 'PingFang SC-Medium', 'PingFang SC';
      font-weight: bold;
      color: #333;
      display: flex;
      align-items: center;
    }

    .card-open {
      cursor: pointer;
      color: #3562db;
      display: flex;
      align-items: center;
      user-select: none;
    }

    .card-open-icon {
      font-size: 10px;
      margin-left: 5px;
    }

    .dropdown-btn {
      position: absolute;
      right: 0;
    }

    .more-operations {
      cursor: pointer;
      color: #3562db;
      user-select: none;
    }
  }

  .card-body {
    margin-top: 10px;
    overflow-y: auto;
    // height: calc(100% - 35px);
  }
}

.dropdownSelect {
  margin: 0;
  padding: 3px 0;

  .el-dropdown-menu__item {
    line-height: 30px;
    padding: 0 15px;
  }
}
</style>
