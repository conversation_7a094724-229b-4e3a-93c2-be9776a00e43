<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="whloe">
        <div style="background: #fff; height: 100%;">
          <el-form ref="ruleForm" :model="ruleForm" :rules="rules">
            <ContentCard title="基础信息"> </ContentCard>
            <div style="display: flex;">
              <el-form-item label="模板名称" label-width="100px" prop="templateName">
                <el-input v-model.trim="ruleForm.templateName" placeholder="请输入模板名称" class="ipt" maxlength="50" show-word-limit></el-input>
              </el-form-item>
              <el-form-item label="问卷类别" label-width="300px" prop="questType">
                <el-select v-model="ruleForm.questType" placeholder="请选择模板类别">
                  <el-option v-for="item in treeData" :key="item.id" :label="item.dictName" :value="item.id"></el-option>
                </el-select>
                <!-- <el-input v-model="ruleForm.questType" class="ipt"></el-input> -->
              </el-form-item>
            </div>
            <el-form-item label="描述" label-width="100px">
              <el-input
                v-model.trim="ruleForm.describe"
                style="width: 50%;"
                placeholder="请输入描述"
                type="textarea"
                maxlength="500"
                show-word-limit
                :autosize="{ minRows: 4, maxRows: 4 }"
              ></el-input>
            </el-form-item>
          </el-form>
          <ContentCard title="问卷内容"> </ContentCard>
          <div class="question-item-container">
            <div v-for="item in questionArray" :key="item.status" class="questionItem">
              <el-button type="primary" :icon="item.icon" @click="stateJudgment(item)">{{ item.questionName }}</el-button>
            </div>
          </div>
          <div class="question-content">
            <div v-for="(qSubject, index) in arr" :key="index" class="preview-container">
              <PreviewCard v-if="flag" :currentPreviewSubject="qSubject">
                <component
                  :is="questionPreview[qSubject.type]"
                  :previewOption="qSubject"
                  :index="qSubject.indexCopy"
                  :pathName="pathName"
                  :diff="diff"
                  :isQuestionNum="questionPreviewList.isQuestionNum == 1"
                ></component>
              </PreviewCard>
            </div>
          </div>
          <QuestionDialog :visible.sync="isDialogVisible">
            <template slot="question-dialog">
              <component v-bind:is="whichComponent" :questionSubject="questionSubject"></component>
            </template>
          </QuestionDialog>
        </div>
      </div>
    </div>
    <div slot="footer">
      <el-button type="primary" @click="confirm('ruleForm')">保存</el-button>
      <el-button type="primary" @click="prev">预览</el-button>
      <el-button type="primary" plain @click="$router.go(-1)">取消</el-button>
    </div>
  </PageContainer>
</template>

<script>
import notice from '../utils/notice'
import utils from '../utils/utils'
import QuestionDialog from '../QuestionDialog/QuestionDialog.vue'
import RadioQuestion from '../RadioQuestion/RadioQuestion.vue'
import CheckBoxQuestion from '../CheckBoxQuestion/CheckBoxQuestion'
import InputQuestion from '../InputQuestion/InputQuestion'
import SelectQuestion from '../SelectQuestion/SelectQuestion'
import PreviewCard from '../component/PreviewCard/PreviewCard.vue'
import PreviewRadio from '../questionDesign/component/QuestionPreview/PreviewRadio/PreviewRadio.vue'
import PreviewCheckBox from '../questionDesign/component/QuestionPreview/PreviewCheckBox/PreviewCheckBox.vue'
import PreviewFillBlank from '../questionDesign/component/QuestionPreview/PreviewFillBlank/PreviewFillBlank.vue'
import PreviewSelect from '../questionDesign/component/QuestionPreview/PreviewSelect/PreviewSelect.vue'
export default {
  name: 'newQuestionnair',
  components: {
    QuestionDialog,
    RadioQuestion,
    CheckBoxQuestion,
    InputQuestion,
    SelectQuestion,
    PreviewCard,
    PreviewRadio,
    PreviewCheckBox,
    PreviewFillBlank,
    PreviewSelect
  },
  data() {
    return {
      arr: [],
      flag: false,
      questionPreviewList: {}, // 当前问卷的题目列表
      ruleForm: {
        templateName: '',
        questType: '',
        describe: ''
      },
      diff: 'title',

      rules: {
        templateName: [{ required: true, message: '请输入模板名称', trigger: 'change' }],
        questType: [{ required: true, message: '请选择模板类别', trigger: 'change' }]
      },
      // treeData: this.$route.query.treeList,
      treeData: JSON.parse(localStorage.getItem('questionnaireType')),

      isDialogVisible: false,
      whichComponent: '',
      questionSubject: {},
      questionPreview: {
        radio: 'PreviewRadio',
        checkbox: 'PreviewCheckBox',
        input: 'PreviewFillBlank',
        // array: 'PreviewMatrix',
        // paragraph: 'PreviewParagraph',
        // sort: 'PreviewSort',
        select: 'PreviewSelect'
        // nd_select: 'PreviewMulSelect'
      },
      pathName: {
        check: {
          isShowSubjectType: false,
          isDisable: true,
          isQuestionNum: true,
          isSetDefaultValue: true
        },
        preview: {
          isShowSubjectType: true,
          isDisable: false,
          isQuestionNum: true,
          isSetDefaultValue: false
        },
        title: {
          isShowSubjectType: true,
          isDisable: false,
          isQuestionNum: true,
          isSetDefaultValue: false
        }
      },
      questionArray: [
        {
          icon: 'el-icon-burger',
          status: 'radio',
          questionName: '单选',
          component: 'RadioQuestion',
          previewComponent: 'PreviewRadio'
        },
        {
          icon: 'el-icon-burger',
          status: 'checkbox',
          questionName: '多选',
          component: 'CheckBoxQuestion',
          previewComponent: 'PreviewCheckBox'
        },
        {
          icon: 'el-icon-burger',
          status: 'input',
          questionName: '填空',
          component: 'InputQuestion',
          previewComponent: 'PreviewFillBlank'
        },
        // {
        //   icon: 'el-icon-burger',
        //   status: 'array',
        //   questionName: '矩阵',
        //   component: 'MatrixQuestion',
        //   previewComponent: 'PreviewMatrix'
        // },
        // {
        //   icon: 'el-icon-burger',
        //   status: 'paragraph',
        //   questionName: '段落',
        //   component: 'ParagraphQuestion',
        //   previewComponent: 'PreviewParagraph'
        // },
        // {
        //   icon: 'el-icon-burger',
        //   status: 'sort',
        //   questionName: '排序',
        //   component: 'SortQuestion',
        //   previewComponent: 'PreviewSort'
        // },
        {
          icon: 'el-icon-burger',
          status: 'select',
          questionName: '下拉菜单',
          component: 'SelectQuestion',
          previewComponent: 'PreviewSelect'
        }
        // {
        //   icon: 'el-icon-burger',
        //   status: 'nd_select',
        //   questionName: '二级下拉菜单',
        //   component: 'MulSelectQuestion',
        //   previewComponent: 'PreviewMulSelect'
        // }
      ]
    }
  },
  created() {
    this.questDetails()
    this.getPaperQuestions()
  },
  destroyed() {
    notice.$off('openPopup')
    notice.$off('showQuestionDialog')
    notice.$off('getCurrentQuestionAllSubject')
  },
  mounted() {
    // this.questionData = utils.getLocalStorage('localData', '')
    // if (!this.questionData.id) {
    //   return
    // }
    // this.getCurrentQuestionAllSubject(this.questionData.id)
    // notice.$on('getCurrentQuestionAllSubject', () => {
    //   this.getCurrentQuestionAllSubject(utils.getLocalStorage('localData', 'id'))
    // })
    notice.$on('showQuestionDialog', (selectedPreviewSubject) => {
      this.handleQuestionDialogShow(selectedPreviewSubject)
    })
    this.registerAssembly()
    notice.$on('cleanOpenPopup', () => {
      notice.$off('openPopup')
      this.registerAssembly()
    })
  },
  methods: {
    prev() {
      this.$router.push({
        name: 'templatePreview'
      })
    },
    handleQuestionDialogShow(selectedPreviewSubject) {
      const item = this.questionArray.filter((item) => item.status === selectedPreviewSubject.type)
      this.questionSubject = selectedPreviewSubject
      this.isDialogVisible = !this.isDialogVisible
      this.whichComponent = item[0].component
    },
    questDetails() {
      this.$api.findPaper({ questionId: this.$route.query.id }).then((res) => {
        if (res.status == 200) {
          this.ruleForm.templateName = res.data.name
          this.ruleForm.questType = res.data.type
          this.ruleForm.describe = res.data.templateDesc
        }
      })
    },
    registerAssembly() {
      notice.$on('openPopup', (openPopup) => {
        // 监听弹出层打开  页面逻辑
        notice.$emit('openPopupDialog', openPopup, this.questionPreviewList.questions) // 把数据抛给 弹出层组件
      })
    },
    getPaperQuestions() {
      let params = {
        questionId: this.$route.query.id,
        userId: this.$store.state.user.userInfo.userId
      }
      this.$api.getPaperQuestions(params).then((res) => {
        if (res.status == 200) {
          this.flag = false
          this.arr = res.data.questions
          let questionLength = JSON.stringify(this.arr.length)
          localStorage.setItem('questionLength', questionLength)
          var indexCount = 0
          for (let index = 0; index < this.arr.length; index++) {
            const element = this.arr[index]
            if (element.type == 'paragraph') {
              indexCount = indexCount + 1
            } else {
              element['indexCopy'] = index - indexCount
            }
          }

          var that = this
          this.$nextTick(function () {
            that.flag = true
          })
          this.questionPreviewList = res.data
        }
      })
    },
    confirm(formName) {
      let params = {
        userId: this.$store.state.user.userInfo.userId,
        userName: this.$store.state.user.userInfo.username,
        id: localStorage.getItem('questId'),
        name: this.ruleForm.templateName,
        type: this.ruleForm.questType,
        templateDesc: this.ruleForm.describe
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$api.updatePvq(params, { 'operation-type': 2, 'operation-id': params.id, 'operation-name': params.name}).then((res) => {
            if (res.status == 200) {
              this.$message({
                message: '修改问卷模板成功',
                type: 'success'
              })
              this.$router.go(-1)
            } else {
              this.$message.error(res.message)
            }
          })
        } else {
          return false
        }
      })
    },
    stateJudgment(item) {
      this.handleQuestionSubjectClick(item)
    },
    handleQuestionSubjectClick(item) {
      this.questionSubject = {}
      this.isDialogVisible = !this.isDialogVisible
      this.whichComponent = item.component
    }
  }
}
</script>

<style lang="scss" scoped>
.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
  padding: 10px;
  overflow-y: auto;
}

.whloe {
  position: relative;
  width: 100%;
  height: 100%;
  // padding: 15px;
  border-radius: 5px;
  overflow: hidden;
}

.ipt {
  width: 200px;
}

.question-item-container {
  width: 450px;
  display: flex;
  margin-left: 30px;
  justify-content: space-around;
  // background-color: #5188fc;
  .questionItem {
    .el-button {
      display: flex;
      align-items: center;
      height: 40px;
    }
  }
}

.question-main-container {
  background-color: #f5f7fa;
  height: 100%;

  .el-row,
  .el-col {
    height: 100%;
  }

  .question-content {
    background-color: #fff;
    // height: 100%;
    height: calc(100% - 320px);
    overflow-y: auto;
    padding: 20px;
  }

  .preview-container {
    padding: 10px;
    background-color: #fff;
  }
}

::v-deep .page-container .container-content {
  background-color: #fff !important;
}

.question-content {
  background-color: #fff;
  // height: 100%;
  height: calc(100% - 320px);
  overflow-y: auto;
  padding: 20px;
}
</style>
