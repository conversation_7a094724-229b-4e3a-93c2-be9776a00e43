<template>
  <div class="circular_diagram">
    <div class="title">{{ title }}（件）</div>
    <div>
      <div ref="circular" class="circular"></div>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
export default {
  name: 'CircularDiagram',
  props: {
    chartData: {
      type: Object,
      default: () => ({})
    },
    title: {
      type: String,
      default: '配件入库统计'
    }
  },
  data() {
    return {
      chart: null,
      option: {
        tooltip: {
          trigger: 'item',
          formatter: function (parms) {
            return `${parms.marker}${parms.name} <b style="margin-left:30px">${parms.value}</b> <br/>`
          }
        },
        legend: {
          type: 'plain',
          left: '60%',
          top: 'middle',
          width: '100px',
          itemWidth: 8,
          itemHeight: 8,
          data: [],
          formatter: (name) => {
            const item = this.chartData.data.find((i) => i.name === name)
            return `${name}：${item.value} ￥${item.price || 0}`
          }
        },
        series: [
          {
            name: 'Access From',
            type: 'pie',
            radius: ['64%', '90%'],
            center: ['30%', '50%'],
            label: {
              show: false
            },
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 1
            },
            labelLine: {
              show: false
            },
            color: ['#A0B8F6', '#3562DB', '#FF9A2E', '#08CB83', '#985EE1'],
            data: []
          },
          {
            type: 'pie',
            radius: ['60%'],
            center: ['30%', '50%'],
            hoverAnimation: false,
            data: [
              {
                name: '总数量',
                value: 0,
                label: {
                  show: true,
                  position: 'center',
                  formatter: '{a|{b}}\n{c|{c}}',
                  rich: {
                    a: {
                      fontSize: 14,
                      color: '#666666'
                    },
                    c: {
                      fontSize: 24,
                      color: '#333333',
                      fontWeight: 'bold',
                      padding: [6, 0, 0, 0]
                    }
                  }
                }
              }
            ],
            tooltip: {
              show: false
            },
            startAngle: 225,
            color: ['#FAF9FC'],
            label: {
              position: 'center',
              formatter: function (e) {
                return `${e.name} \n ${e.value}`
              }
            }
          }
        ]
      }
    }
  },
  watch: {
    chartData: {
      handler(newVal) {
        this.option.series[0].data = newVal.data
        this.option.series[1].data[0].value = newVal.value
        this.option.legend.data = newVal.legend
        this.chart.setOption(this.option)
      },
      deep: true
    }
  },
  mounted() {
    this.initChart()
    addEventListener('resize', this.handleChartResize)
  },
  beforeDestroy() {
    removeEventListener('resize', this.handleChartResize)
  },
  methods: {
    handleChartResize() {
      this.chart.resize()
    },
    initChart() {
      this.chart = echarts.init(this.$refs.circular)
      // this.chart.setOption(this.option)
    }
  }
}
</script>
<style lang="scss" scoped>
.circular_diagram {
  width: 49.5%;
  background: #faf9fc;
  border-radius: 4px 4px 4px 4px;
  padding: 16px;
  .title {
    font-weight: bold;
    font-size: 16px;
    color: #333333;
  }
  .circular {
    width: 100%;
    height: 200px;
  }
}
</style>
