<template>
  <div class="main-container">
    <div class="whole">
      <div class="page-title">问卷设置</div>
      <el-row style="border-bottom: 1px solid #d8dee7;">
        <el-col :md="16" :lg="16" :xl="14" style="height: auto;">
          <BreadcrumbNavBar />
        </el-col>
        <el-col :md="8" :lg="8" :xl="10">
          <HeaderButton />
        </el-col>
      </el-row>
      <div class="bottom">
        <div class="bgcTitle">全局设置</div>
        <div class="addBorder">
          <!-- <div class="everyrow">
            <span class="span">回收限制:</span>
            <el-input v-model="Limit" placeholder="不限制" @input="changeLimit"></el-input>
          </div> -->
          <div class="everyrow">
            <i class="is_require">*</i>
            <span class="span">开始日期:</span>
            <el-date-picker v-model="startTime" type="datetime" placeholder="不设置" :picker-options="startDatePicker" value-format="yyyy-MM-dd HH:mm:ss"></el-date-picker>
          </div>
          <div class="everyrow">
            <i class="is_require">*</i>
            <span class="span">结束日期:</span>
            <el-date-picker v-model="endTime" type="datetime" placeholder="不设置" :picker-options="endDatePicker" value-format="yyyy-MM-dd HH:mm:ss"></el-date-picker>
          </div>
          <!-- <div class="everyrow">
          <span class="span">提交时输入验证码:</span>
          <el-checkbox v-model="checkedSub" :true-label="ok" :false-label="nothing"></el-checkbox>
        </div>-->
          <div class="everyrow">
            <span class="span">是否显示题号:</span>
            <el-checkbox v-model="checkedNum" :true-label="ok" :false-label="nothing"></el-checkbox>
          </div>
          <div class="everyrow">
            <span class="span">是否显示进度条:</span>
            <el-checkbox v-model="checkedPro" :true-label="ok" :false-label="nothing"></el-checkbox>
          </div>
          <!-- <div class="everyrow">
            <span class="span">是否签名:</span>
            <el-checkbox v-model="forceSign" :true-label="ok" :false-label="nothing"></el-checkbox>
          </div> -->
          <!-- <div class="everyrow">
          <span class="span">是否控制作答设备:</span>
          <el-checkbox v-model="checkedType" :true-label="ok" :false-label="nothing"></el-checkbox>
        </div>-->
          <el-button type="primary" @click="doConfirmBeforeEdit('saveGlobalSetting')">保存</el-button>
        </div>
        <div class="bgcTitle addMargin">收集目标设置</div>
        <div class="addBorder add">
          <questionTree></questionTree>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import questionTree from '../component/questionTree/questionTree.vue'
import BreadcrumbNavBar from '../component/BreadcrumbNavBar/BreadcrumbNavBar.vue'
import HeaderButton from '../questionDesign/component/HeaderButton/HeaderButton.vue'
export default {
  components: {
    BreadcrumbNavBar,
    HeaderButton,
    questionTree
  },
  data() {
    return {
      ok: 1, // 多选框按钮选中
      nothing: 0, // 多选框按钮没选中
      startTime: '', // 开始时间
      endTime: '', // 结束时间
      // checkedSub: 0, // 提交是否有验证码
      checkedNum: 0, // 是否显示题号
      checkedPro: 0, // 是否显示进度条
      // checkedType: 0, // 是否控制作答设备
      pvqid: localStorage.getItem('questId'), // 获取问卷id,
      configObj: [
        // 转换配置
        {
          // 数量
          saveValue: 'maxAnswerCount', // 保存时要转换的后端格式
          key: 'Limit', // 当前this中的变量名
          value: 'maxAnswerCount' // 后端返回的变量名
        },
        {
          // 开始时间
          saveValue: 'startTime',
          transformation: true, // 时间格式转换
          key: 'startTime',
          value: 'startTime'
        },
        {
          // 结束时间
          saveValue: 'endTime',
          transformation: true, // 时间格式转换
          key: 'endTime',
          value: 'endTime'
        },
        // {
        //   // 提交验证码
        //   saveValue: "showValidate",
        //   key: "checkedSub",
        //   value: "showValidate"
        // },
        {
          // 是否显示题号
          saveValue: 'isQuestionNum',
          key: 'checkedNum',
          value: 'isQuestionNum'
        },
        {
          // 进度条
          saveValue: 'isProgress',
          key: 'checkedPro',
          value: 'isProgress'
        },
        {
          // 进度条
          saveValue: 'forceSign',
          key: 'forceSign',
          value: 'forceSign'
        }
        // {
        //   // 大体设备
        //   saveValue: "isShowDevice",
        //   key: "checkedType",
        //   value: "isShowDevice"
        // }
      ],
      startDatePicker: this.beginDate(),
      endDatePicker: this.processDate(),
      limitCopy: '', // 回收限制复制
      Limit: '', // 回收限制
      forceSign: 0 // 是否签名
    }
  },
  created() {
    this.getfindPaper()
  },
  methods: {
    getfindPaper() {
      let params = {
        questionId: localStorage.getItem('questId')
      }
      this.$api.findPaper(params).then((res) => {
        if (res.status == 200) {
          this.Limit = res.data.maxAnswerCount
          this.startTime = res.data.startTime
          this.endTime = res.data.endTime
          this.checkedNum = res.data.isQuestionNum
          this.checkedPro = res.data.isProgress
        }
      })
    },
    changeLimit(val) {
      var reg = /^[1-9]\d*$/g
      if (!reg.test(val)) {
        if (val != '') {
          this.Limit = this.limitCopy
        } else {
          this.limitCopy = val
        }
      } else {
        this.limitCopy = val
      }
    },
    beginDate() {
      const self = this
      return {
        disabledDate(time) {
          if (self.endTime) {
            // 如果结束时间不为空，则小于结束时间
            return new Date(self.endTime).getTime() < time.getTime()
          } else {
            // return time.getTime() > Date.now()//开始时间不选时，结束时间最大值小于等于当天
          }
        }
      }
    },
    processDate() {
      const self = this
      return {
        disabledDate(time) {
          if (self.startTime) {
            // 如果开始时间不为空，则结束时间大于开始时间
            return new Date(self.startTime).getTime() > time.getTime()
          } else {
            // return time.getTime() > Date.now()//开始时间不选时，结束时间最大值小于等于当天
          }
        }
      }
    },
    getcurrentTime() {
      let date = new Date()
      let Y = date.getFullYear() // 获取系统的年；
      let M = date.getMonth() + 1 // 获取系统月份，由于月份是从0开始计算，所以要加1
      let D = date.getDate() // 获取系统日
      let H = date.getHours() // 获取系统时间
      let m = date.getMinutes() > 10 ? date.getMinutes() : '0' + date.getMinutes() // 分
      let s = date.getSeconds() // 秒
      M = M < 10 ? '0' + M : M
      D = D < 10 ? '0' + D : D
      H = H < 10 ? '0' + H : H
      s = s < 10 ? '0' + s : s
      return Y + '-' + M + '-' + D + ' ' + H + ':' + m + ':' + s
      // return Y + '年' + M + '月' + D + '日' + h + '时' + m + '分'
    },
    doConfirmBeforeEdit(callback, params) {
      if (this.startTime == null || this.endTime == null) return this.$message.error('请将问卷时间范围填写完整')
      if (this.endTime < this.getcurrentTime()) return this.$message.error('问卷结束时间不能小于当前时间')
      // 获取问卷状态，状态分为design: "设计",publish: "收集",recovery: "完成",
      const pvqStatus = JSON.parse(localStorage.getItem('localData'))
      if (pvqStatus.status === 'publish') {
        // 获取问卷状态为publish时的状态，状态分为 0: "暂停",1: "收集"
        if (pvqStatus.statusRun == 1) {
          this.doConfirmMessage('问卷处于收集状态，无法编辑！')
        } else {
          this.doConfirmMessage('问卷已暂停，无法编辑！')
        }
      } else if (pvqStatus.status === 'recovery') {
        this.doConfirmMessage('问卷已结束，无法编辑！')
      } else {
        var flag = true
        if (this.startTime && this.endTime) {
          var startTime = new Date(this.startTime).getTime()
          var endTime = new Date(this.endTime).getTime()
          flag = endTime > startTime
        }
        if (flag) {
          let params = {
            // userId: this.$store.state.user.userInfo.userId,
            // userName: this.$store.state.user.userInfo.username,
            id: localStorage.getItem('questId'),
            maxAnswerCount: this.Limit,
            startTime: this.startTime,
            endTime: this.endTime,
            isQuestionNum: this.checkedNum,
            isProgress: this.checkedPro
          }
          this.$api.updatePvq(params).then((res) => {
            if (res.status == 200) {
              this.$message({
                message: '保存成功',
                type: 'success'
              })
            } else {
              this.$message.error(res.message)
            }
          })
        } else {
          this.$message.error('开始日期不能大于结束日期!')
        }
      }
    },
    doConfirmMessage(messageInfo) {
      this.$confirm(messageInfo, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.main-container {
  height: 100%;
  padding: 15px;
  margin: 0 !important;

  .whole {
    width: 100%;
    height: 100%;
    border-radius: 5px;
    background-color: #fff !important;

    .page-title {
      height: 50px;
      line-height: 50px;
      color: #606266;
      font-size: 14px;
      padding-left: 25px;
      border-bottom: 1px solid #d8dee7;
    }

    .submit-container {
      display: flex;
      justify-content: center;
      padding-bottom: 20px;
    }

    .el-button--text {
      color: #fff;
      padding: 12px 10px;
    }

    .el-button--text:hover,
    .el-button:hover {
      color: #fff;
      background-color: #5188fc;
      border-radius: 0;
      border-color: transparent;
    }
  }
}

@media screen and (max-width: 1366px) {
  .main-container .el-button--text {
    color: #fff;
    padding: 0 15px;
  }
}

.bottom {
  width: 98%;
  height: calc(100% - 130px);
  overflow: auto;

  /* background-color: pink; */
  margin: 0 auto 20px;
}

.bgcTitle {
  padding: 10px;
  font-size: 14px;
  font-weight: 600;
  background-color: #3562db;
  color: white;
}

.el-input {
  width: 200px;
  margin-left: 10px;
  height: 40px;
}

.span {
  font-size: 14px;
  font-weight: 600;
}

.everyrow {
  position: relative;
  padding: 10px;

  .is_require {
    position: absolute;
    top: 10;
    left: 0%;
    color: red;
  }
}

.el-checkbox {
  margin-left: 10px;
}

.addBorder {
  border: 1px solid #3562db;
}

.el-button {
  margin: 0 0 10px 10px;
}

.addMargin {
  margin-top: 20px;
}

.add > .el-button {
  margin: 10px;
}

.el-form-item {
  margin-bottom: 0;
}

.addChange ::v-deep .el-form-item__content {
  margin-left: 0 !important;
}

form {
  display: inline-block;
}

.addChange ::v-deep .el-form-item__error {
  left: 10px;
}

.el-button--default {
  border: blue 1px solid !important;
}
</style>
