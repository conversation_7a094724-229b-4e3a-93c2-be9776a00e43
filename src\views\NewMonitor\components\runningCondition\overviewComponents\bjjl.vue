<!-- 运行监测 -->
<template>
  <ContentCard :title="item.componentTitleShow" :scrollbarHover="true" :cstyle="{ width: '100%', height: '100%' }"
    class="drag_class" :hasMoreOper="['edit']"
    @more-oper-event="(val) => $emit('all-more-Oper', val, item.componentName, item.chartType)">
    <div slot="content" style="width: 100%; height: 100%;">
      <div class="control-btn-header">
        <el-button class="btn-item" :class="{ 'btn-active': requestInfo.timeType == 0 }" plain
          @click="timeTypeChange(0)">全部</el-button>
        <el-button class="btn-item" :class="{ 'btn-active': requestInfo.timeType == 1 }" plain
          @click="timeTypeChange(1)">今日</el-button>

        <el-button class="btn-item" :class="{ 'btn-active': requestInfo.timeType == 3 }" plain
          @click="timeTypeChange(3)">本月</el-button>
        <el-button class="btn-item" :class="{ 'btn-active': requestInfo.timeType == 4 }" plain
          @click="timeTypeChange(4)">本年</el-button>
        <el-button class="btn-item" :class="{ 'btn-active': requestInfo.timeType == 5 }" plain
          style="margin-right: 10px" @click="timeTypeChange(5)">自定义</el-button>
        <el-date-picker v-model="requestInfo.dataRange" type="daterange" unlink-panels
          :disabled="requestInfo.timeType != 5" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
          format="yyyy-MM-dd" value-format="yyyy-MM-dd" clearable @change="timeListData" />
        <div style="display: inline-block">
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="searchForm">查询</el-button>
        </div>
      </div>
      <div>
        <TablePage ref="table" v-loading="tableLoading" :showPage="true" :tableColumn="tableColumn" height="270"
          :data="tableData" :pageData="pageData" @pagination="paginationChange" :pageProps="pageProps" />
      </div>
    </div>
  </ContentCard>
</template>
<script lang="jsx">
import moment from 'moment'
export default {
  name: 'bjjl',
  props: {
    item: {
      type: Object,
      default: () => { }
    },
    systemCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      requestInfo: {
        projectCode: '',
        dataRange: [], // 时间范围
        timeType: 0 // 1: 当天 2: 本月 3: 本年 4: 自定义
      },
      tableColumn: [
        {
          prop: '',
          label: '序号',
          formatter: (scope) => {
            return (this.pageData.pageNo - 1) * this.pageData.pageSize + scope.$index + 1
          },
          width: 60
        },
        {
          prop: 'alarmStartTime',
          label: '报警时间'
        },
        {
          prop: 'alarmObjectName',
          label: '报警对象名称'
        },
        {
          prop: 'alarmLevel',
          label: '报警等级',
          formatter: (row) => {
            switch (row.row.alarmLevel) {
              case 0:
                return '通知';
              case 1:
                return '一般';
              case 2:
                return '紧急';
              case 3:
                return '重要';
              default:
                return '未知'; // 处理未定义的情况
            }
          }
        },
        {
          prop: 'alarmType',
          label: '报警类型',

        },
        {
          prop: 'alarmSpaceName',
          label: '位置'
        },
        {
          prop: 'alarmStatus',
          label: '处理状态',
          formatter: (row) => {
            switch (row.row.alarmStatus) {
              case 0:
                return '未处理';
              case 1:
                return '处理中';
              case 2:
                return '已关闭';
              default:
                return '未知'; // 处理未定义的情况
            }
          }
        },
        {
          prop: 'operation',
          label: '操作',
          width: 100,
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span class="operationBtn-span" onClick={() => this.handleListEvent(row.row)}>
                  详情
                </span>
              </div>
            )
          }
        }
      ],
      tableData: [],
      pageData: {
        pageNo: 1,
        pageSize: 15,
        total: 0
      },
      pageProps: {
        page: 'pageNo',
        pageSize: 'pageSize',
        total: 'total'
      },
      deviceId: "",
      tableLoading: false
    }
  },
  mounted() {
    // this.bjjlTableData()
  },
  methods: {
    timeListData(val) {
      this.requestInfo.dataRange[0] = val[0] + ' 00:00:00'
      this.requestInfo.dataRange[1] = val[1] + ' 23:59:59'
    },
    // 报警记录列表
    bjjlTableData(id) {
      this.deviceId = id
      this.tableLoading = true
      let data = {
        objectId: id || "",
        startTime: this.requestInfo.dataRange[0],
        endTime: this.requestInfo.dataRange[1],
        pageSize: this.pageData.pageSize,
        pageNo: this.pageData.pageNo,
        timeOrType: 4,
      }
      this.$api
        .getAssetsAlertPage(data)
        .then((res) => {
          this.tableLoading = false
          if (res.code == 200) {
            this.tableData = res.data.records
            this.pageData.total = res.data.total
          } else if (res.message) {
            this.tableData = []
            this.pageData.total = 0
            this.$message.error(res.message)
          }
        })
        .catch((err) => {
          this.tableLoading = false
        })
    },
    // 分页
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.bjjlTableData(this.deviceId)
    },
    // 日期选择
    timeTypeChange(type) {
      let newObj = {
        1: [moment().format('YYYY-MM-DD 00:00:00'), moment().format('YYYY-MM-DD 23:59:59')],
        2: [moment().startOf('week').format('YYYY-MM-DD 00:00:00'), moment().endOf('week').format('YYYY-MM-DD 23:59:59')],
        3: [moment().startOf('month').format('YYYY-MM-DD 00:00:00'), moment().endOf('month').format('YYYY-MM-DD 23:59:59')],
        4: [moment().startOf('year').format('YYYY-MM-DD 00:00:00'), moment().endOf('year').format('YYYY-MM-DD 23:59:59')],
        0: [],
        5: []
      }
      this.requestInfo.dataRange = newObj[type]
      this.requestInfo.timeType = type
      this.pageData.pageSize = 15
      this.pageData.pageNo = 1
    },
    // 重置查询表单
    resetForm() {
      this.requestInfo = {
        projectCode: '',
        dataRange: [], // 时间范围
        timeType: 0 // 1: 当天 2: 本月 3: 本年 4: 自定义
      },
        this.pageData = {
          pageSize: 15,
          pageNo: 1
        },
        this.bjjlTableData(this.deviceId)
    },
    searchForm() {
      this.bjjlTableData(this.deviceId)
    },
    // 查看
    handleListEvent(row) {
      this.$router.push({
        path: '/allAlarm/alarmDetail',
        query: {
          alarmId: row.alarmId
        }
      })
    },
  }
}
</script>
<style lang="scss" scoped>
.data-btns {
  position: absolute;
  right: 7%;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &>span {
    width: 44px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    margin: 0 4px;
    background-color: #f6f5fa;
    font-size: 14px;
    font-family: 'PingFang SC-Medium', 'PingFang SC';
    border: none;
    border-radius: 2px;
    color: #7f848c;
    cursor: pointer;
  }

  .active-btn {
    background-color: #e6effc !important;
    color: #3562db !important;
    border-color: #e6effc !important;
  }
}

.operation-list {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
}

.control-btn-header {
  padding: 0;
  margin-bottom: 20px;

  &>div {
    margin-right: 10px;
    margin-top: 10px;
  }

  .btn-item {
    border: 1px solid #3562db;
    color: #3562db;
    font-family: none;
  }

  .btn-active {
    color: #fff;
    background: #3562db;
  }
}
</style>
