<script>
import tableListMixin from '@/mixins/tableListMixin'
import { SuperviseDictType } from './constant'
export default {
  name: 'SuperviseList',
  mixins: [tableListMixin],
  data() {
    return {
      searchFrom: {
        // 工单类型
        workType: '',
        // 服务事项
        serviceType: [],
        // 超时类型
        overTimeType: ''
      },
      tableLoading: false,
      tableData: [],
      dialog: {
        show: false,
        id: 0,
        readonly: false
      },
      options: {
        // 项目类型
        projectList: [],
        // 超时类型
        overTimeList: [],
        // 服务事项
        serviceList: [],
        // 工单类型
        workTypeList: []
      }
    }
  },
  computed: {
    OperateType() {
      return {
        Add: 'Add',
        Record: 'Record',
        Edit: 'Edit',
        View: 'View',
        Delete: 'Delete'
      }
    }
  },
  mounted() {
    this.getOptions()
    const query = this.$route.query || {}
    if (query.page) {
      this.pagination.current = Number(query.page)
      this.pagination.size = Number(query.size)
      this.searchFrom.workType = query.workType || ''
      this.searchFrom.serviceType = query.serviceType || ''
      this.searchFrom.overTimeType = query.overTimeType || ''
      this.$router.push({ query: {} })
    }
    this.getDataList()
  },
  methods: {
    workTypeChange(val) {
      this.options.serviceList = []
      this.getItemList(val)
    },
    // 获取表单选项数据
    getOptions() {
      // 获取项目类型
      // this.$api.getProjectList({ page: 1, pageSize: 9999 }).then((res) => {
      //   if (res.code === '200') {
      //     this.options.projectList = res.data.records
      //   }
      // })
      // 获取超时类型字典
      this.$api.dictQueryByTypes({ dictTypeList: [SuperviseDictType.OverTimeType] }).then((res) => {
        if (res.code === '200') {
          const data = res.data.find((it) => it.dictType === SuperviseDictType.OverTimeType)
          this.options.overTimeList = data?.dictList ?? []
        }
      })

      // 获取工单类型选项
      this.$api.workOderType({ page: 1, pageSize: 999, ssmType: 2 }).then((res) => {
        if (res.code === '200') {
          this.options.workTypeList = res.data
        }
      })
    },
    // 服务事项
    getItemList(id) {
      let params = {
        id: id,
        nodeLevel: 1
      }
      this.$api.getItemList(params).then((res) => {
        if (res.code === '200') {
          this.options.serviceList = res.data.list.filter((it) => it.parent === '#')
        }
      })
    },
    // 点击搜索，重置分页并获取数据
    onSearch() {
      this.pagination.current = 1
      this.getDataList()
    },
    // 获取分页列表数据
    getDataList() {
      this.tableLoading = true
      this.tableData = []
      const params = {
        pageNo: this.pagination.current,
        pageSize: this.pagination.size,
        workTypeCode: this.searchFrom.workType,
        itemTypeCode: this.searchFrom.serviceType,
        timeoutType: this.searchFrom.overTimeType
      }
      this.$api
        .superviseQueryByPage(params)
        .then((res) => {
          if (res.code === '200') {
            this.tableData = res.data.list
            this.pagination.total = res.data.count
          } else {
            throw '获取列表数据失败'
          }
        })
        .catch((msg) => this.$message.error(msg))
        .finally(() => (this.tableLoading = false))
    },
    // 重置搜索表单，并触发搜索
    resetForm() {
      this.$refs.formRef.resetFields()
      this.onSearch()
    },
    // 删除一行数据
    doDelete(id) {
      this.tableLoading = true
      this.$api
        .superviseDeleteById({ id })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('删除成功')
            this.getDataList()
          } else {
            throw res.message
          }
        })
        .catch((msg) => msg && this.$message.error(msg))
        .finally(() => (this.tableLoading = false))
    },
    // 列表操作相关
    onOperate(command, row) {
      switch (command) {
        case this.OperateType.Delete:
          this.$confirm('删除后将无法恢复，是否确定删除？', '信息提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消'
          }).then(() => this.doDelete(row.id))
          break
        case this.OperateType.Record:
          this.updateRouteQuery().then(() => {
            this.$router.push({ name: 'superviseRecord', query: { id: row?.id ?? '' } })
          })
          break
        default:
          this.updateRouteQuery().then(() => {
            this.$router.push({
              name: 'superviseRule',
              query: {
                id: row?.id ?? '',
                readonly: command === this.OperateType.View,
              }
            })
          })
          break
      }
    },
    // 更新当前页面的查询条件
    updateRouteQuery() {
      const query = {
        page: this.pagination.current,
        size: this.pagination.size
      }
      Object.assign(query, this.searchFrom)
      return this.$router.replace({ query })
    }
  }
}
</script>
<template>
  <PageContainer class="supervise-list">
    <template #content>
      <div class="supervise-list__search">
        <el-form ref="formRef" class="supervise-list__search__form" :model="searchFrom" inline>
          <el-form-item prop="workType">
            <el-select v-model="searchFrom.workType" placeholder="工单类型" clearable @change="workTypeChange">
              <el-option v-for="item in options.workTypeList" :key="item.workTypeCode" :label="item.workTypeName" :value="item.workTypeCode"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="serviceType">
            <el-select v-model="searchFrom.serviceType" placeholder="服务事项" clearable :disabled="searchFrom.workType == ''">
              <el-option v-for="item in options.serviceList" :key="item.id" :label="item.text" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="overTimeType">
            <el-select v-model="searchFrom.overTimeType" placeholder="超时类型" clearable>
              <el-option v-for="item in options.overTimeList" :key="item.dictCode" :label="item.dictName" :value="item.dictCode"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div class="supervise-list__search__action">
          <el-button type="primary" plain style="margin-right: 10px" @click="resetForm">重置</el-button>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </div>
      </div>
      <div class="supervise-list__actions">
        <el-button type="primary" icon="el-icon-plus" @click="onOperate(OperateType.Add)">新增</el-button>
        <el-button type="primary" @click="onOperate(OperateType.Record)">督办记录</el-button>
      </div>
      <div class="supervise-list__table table-content">
        <el-table
          v-loading="tableLoading"
          height="100%"
          :data="tableData"
          border
          stripe
          table-layout="auto"
          class="tableAuto"
          row-key="id"
          @row-dblclick="onOperate(OperateType.View, $event)"
        >
          <el-table-column label="序号" type="index" width="55" />
          <el-table-column label="工单类型" prop="workTypeName"></el-table-column>
          <el-table-column label="服务事项" prop="itemTypeName" show-overflow-tooltip></el-table-column>
          <el-table-column label="超时类型" prop="timeoutTypeName" show-overflow-tooltip></el-table-column>
          <el-table-column label="创建时间" prop="createTime"></el-table-column>
          <el-table-column label="更新时间" prop="updateTime"></el-table-column>
          <el-table-column label="操作" width="160px">
            <template #default="{ row }">
              <el-button type="text" @click="onOperate(OperateType.View, row)">查看</el-button>
              <el-button type="text" @click="onOperate(OperateType.Edit, row)">编辑</el-button>
              <el-dropdown @command="onOperate($event, row)">
                <el-button style="margin-left: 10px" type="text"
                  >更多
                  <i class="el-icon-arrow-down"></i>
                </el-button>
                <el-dropdown-menu>
                  <el-dropdown-item :command="OperateType.Record">督办记录</el-dropdown-item>
                  <el-dropdown-item :command="OperateType.Delete" class="text-red">删除</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        class="supervise-list__pagination"
        :current-page="pagination.current"
        :page-sizes="pagination.pageSizeOptions"
        :page-size="pagination.size"
        :layout="pagination.layoutOptions"
        :total="pagination.total"
        @size-change="paginationSizeChange"
        @current-change="paginationCurrentChange"
      >
      </el-pagination>
    </template>
  </PageContainer>
</template>
<style scoped lang="scss">
.supervise-list {
  ::v-deep(.container-content) {
    display: flex;
    flex-flow: column nowrap;
    padding: 16px;
    height: 100%;
    overflow: hidden;
    background-color: #fff;
  }
  &__search {
    display: flex;
    justify-content: space-between;
    line-height: 40px;
    .el-form-item {
      margin-bottom: 4px;
    }
  }
  &__table {
    margin-top: 16px;
    flex: 1;
    overflow: hidden;
    .text-red {
      color: #ea0000 !important;
    }
  }
  &__actions {
    margin-top: 8px;
  }
  &__pagination {
    margin-top: 10px;
  }
}
</style>
