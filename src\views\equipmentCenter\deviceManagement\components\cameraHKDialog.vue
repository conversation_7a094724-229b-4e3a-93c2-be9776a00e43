<template>
  <el-dialog v-if="cameraDialogShow" title="选择关联摄像机" width="60%" :visible.sync="cameraDialogShow"
    custom-class="model-dialog" :before-close="closeDialog">
    <div class="camera_content" style="padding: 10px 20px 10px 10px">
      <div class="header_operation">
        <div class="search_box">
          <div class="search_select">
            <el-input v-model="cameraParameter.name" placeholder="请输入摄像机名称"></el-input>
          </div>
          <div class="search_select">
            <el-input v-model="cameraParameter.ip" placeholder="请输入IP"></el-input>
          </div>
          <!-- <div class="search_select">
            <el-select ref="treeSelect" v-model="cameraParameter.space" clearable placeholder="空间位置" @clear="handleClear">
              <el-option hidden :value="cameraParameter.space" :label="areaName"> </el-option>
              <el-tree
                :data="serverSpaces"
                :props="serverDefaultProps"
                :load="serverLoadNode"
                lazy
                :expand-on-click-node="false"
                :check-on-click-node="true"
                @node-click="handleNodeClick"
              >
              </el-tree>
            </el-select>
          </div> -->
        </div>
        <div class="header_btn">
          <el-button type="primary" plain @click="userReset">重置</el-button>
          <el-button type="primary" @click="userQuery">查询</el-button>
        </div>
      </div>
      <div v-loading="tableLoading" class="table_div">
        <el-table ref="cameraTable" row-key="icmCode" :data="cameraTableData" tooltip-effect="dark" style="width: 100%"
          height="310px" border @selection-change="handleSelectionChange" @select="selectChange"
          @select-all="selectAll">
          <el-table-column type="selection" :reserve-selection="true" width="55" align="center"> </el-table-column>
          <el-table-column type="index" label="序号" width="55" align="center">
            <template slot-scope="scope">
              <span>{{ (pageinationData.page - 1) * pageinationData.pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="icmName" label="摄像机名称" align="center" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="icmIp" label="ip" align="center" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="icmSpaceName" label="空间" align="center" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="icmManufacturer" label="厂家" align="center" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="icmCode" label="设备id" align="center" show-overflow-tooltip> </el-table-column>
        </el-table>
      </div>
      <div style="padding: 6px">
        <el-pagination class="pagination" :current-page="pageinationData.page" :page-sizes="[15, 30, 50, 100]"
          :page-size="pageinationData.pageSize" layout="total, sizes, prev, pager, next, jumper"
          :total="pageinationData.total" @size-change="cameraHandleSizeChange"
          @current-change="cameraHhandleCurrentChange"></el-pagination>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="groupSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { transData, ListTree } from '@/util'
export default {
  name: 'cameraDialog',
  props: {
    cameraDialogShow: {
      type: Boolean,
      default: false
    },
    cameraDialogData: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      cameraParameter: {
        name: '', // 名称
        ip: '', // ip
        space: '' // 空间id
        // icmVidicon: "" //摄像机
      },
      // spaceList: [],
      serverSpaces: [], // 空间位置
      spaces: [], // 空间位置
      serverDefaultProps: {
        label: 'ssmName',
        isLeaf: 'leaf',
        children: 'children'
      },
      areaName: '', // 选中 下拉树的name
      checkedData: [],
      tableLoading: false,
      cameraTableData: [], // 数据
      pageinationData: {
        page: 1,
        pageSize: 15,
        total: 0
      }
    }
  },
  mounted() {
    this.getCameraTableData()
  },
  methods: {
    // 获取列表
    getCameraTableData() {
      let data = {
        pageSize: this.pageinationData.pageSize,
        page: this.pageinationData.page,
        ...this.cameraParameter
      }
      this.tableLoading = true
      this.$api
        .getHikCameraData(data)
        .then((res) => {
          this.tableLoading = false
          if (res.code == 200) {
            this.cameraTableData = res.data.records
            this.pageinationData.total = res.data.total
            let cameraId = this.cameraDialogData?.split(',') ?? []
            if (cameraId.length && this.cameraTableData.length) {
              cameraId.forEach((icmCode) => {
                let selectRow = this.cameraTableData.find((e) => e.icmCode == icmCode)
                if (selectRow) {
                  this.$refs.cameraTable.toggleRowSelection(selectRow)
                }
              })
            }
          }
        })
        .catch(() => {
          this.tableLoading = false
        })
    },
    // 选择下拉树 数据
    handleNodeClick(data) {
      this.cameraParameter.space = data.id
      this.areaName = data.ssmName
      this.$refs.treeSelect.blur()
    },
    // 弹框分页
    cameraHandleSizeChange(val) {
      this.pageinationData.pageSize = val
      this.getCameraTableData()
    },
    cameraHhandleCurrentChange(val) {
      this.pageinationData.page = val
      this.getCameraTableData()
    },
    // 重置
    userReset() {
      this.cameraParameter = {
        name: '', // 名称
        ip: '', // ip
        space: '' // 空间id
        // icmVidicon: "" //摄像机
      }
      // this.handleClear()
      this.userQuery()
    },
    // 点击查询
    userQuery() {
      this.pageinationData.page = 1
      this.getCameraTableData()
    },
    handleSelectionChange(rows) {
      if (rows.length > 1) {
        // 只保留最新选择的项
        this.$refs.cameraTable.clearSelection()
        this.$refs.cameraTable.toggleRowSelection(rows[rows.length - 1])
      }
      this.checkedData = rows
    },
    selectChange(selection, row) {
      // 选中复选框
      if (selection.length > 1) {
        const del_row = selection.shift()
        this.$refs.cameraTable.toggleRowSelection(del_row, false)
      }
    },
    selectAll(selection) {
      // 全选复选框
      if (selection.length > 1) {
        selection.length = 1
      }
    },
    closeDialog() {
      this.$emit('closeCameraDialog')
    },
    groupSubmit() {
      this.$emit('submitCameraDialog', this.checkedData)
    }
  }
}
</script>
<style lang="scss" scoped>
.model-dialog {
  .camera_content {
    margin: 0 auto;
    background: #fff;
    border-radius: 4px;
    // display: flex;
    width: 100%;
    height: 100%;

    .header_operation {
      // padding: 24px;
      margin-bottom: 10px;
      display: flex;

      .search_box {
        display: flex;

        >div {
          margin-right: 16px;
        }

        .search_input {
          width: 200px;
        }
      }
    }

    .table_div {
      height: calc(100% - 100px);
    }

    .paging_box {
      display: flex;
      justify-content: flex-end;
      padding: 6px;
    }
  }
}
</style>
