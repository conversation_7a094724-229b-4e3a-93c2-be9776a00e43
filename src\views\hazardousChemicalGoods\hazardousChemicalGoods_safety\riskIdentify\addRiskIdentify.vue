<template>
  <PageContainer :footer="true">
    <div slot="content" class="table-content">
      <div class="warehouse-content-title">
        <i class="el-icon-arrow-left" @click="$router.go(-1)"></i>
        {{ riskWarehouseType === 'add' ? '新增风险记录' : riskWarehouseType === 'view' ? '风险记录详情' : '' }}
      </div>
      <div class="content_box">
        <el-form ref="formRef" :model="formModel" :rules="rules" label-width="140px">
          <div class="toptip">
            <span class="green_line"></span>
            基本信息
          </div>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="仓库名称" prop="warehouseName">
                <el-input v-if="riskWarehouseType !== 'view'" v-model="formModel.warehouseName" disabled> </el-input>
                <span v-else class="detailContent">{{ formModel.warehouseName || '--' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="位置" prop="gridName">
                <el-input v-if="riskWarehouseType !== 'view'" v-model="formModel.gridName" disabled> </el-input>
                <span v-else class="detailContent">{{ formModel.gridName || '--' }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="风险等级" prop="riskId">
                <el-select v-if="riskWarehouseType !== 'view'" v-model="formModel.riskId" placeholder="请选择"
                  @change="riskLevelChange">
                  <el-option v-for="item in riskLevelOptions" :key="item.id" :label="item.name" :value="item.id">
                  </el-option>
                </el-select>
                <span v-else class="detailContent">{{ formModel.riskName }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="18">
              <el-form-item label="说明" prop="remark">
                <el-input v-if="riskWarehouseType !== 'view'" v-model.trim="formModel.remark" maxlength="500"
                  show-word-limit type="textarea" placeholder="请输入"></el-input>
                <span v-else class="detailContent">{{ formModel.remark || '--' }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="riskWarehouseType !== 'view'" :gutter="10">
            <el-col :span="12">
              <el-form-item label="附件">
                <el-upload action="" list-type="list" :file-list="attachments" :limit="6"
                  :before-upload="(file) => beforeAvatarUpload(file, 'text')"
                  :http-request="(file) => httpRequest(file, 'text')"
                  accept=".jpg, .jpeg, .png, .JPG, .JPEG,.word, .WORD, .Excel, .pdf, .PDF, .doc, .docx, .DOCX, .xlsx, .xls"
                  :on-remove="(file, fileList) => handleRemove(file, fileList, 'text')">
                  <el-button size="small" type="primary">点击上传</el-button>
                  <div slot="tip" class="el-upload__tip">文件大小限制20M</div>
                </el-upload>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-else :gutter="10">
            <el-col :span="18">
              <el-form-item label="附件">
                <el-table :data="attachments">
                  <el-table-column label="序号" type="index" width="50"> </el-table-column>
                  <el-table-column property="name" label="文件名"> </el-table-column>
                  <el-table-column property="size" label="大小（M）"> </el-table-column>
                  <el-table-column property="option" label="操作" width="120">
                    <template slot-scope="scope">
                      <el-button type="text" @click="handleView(scope.row)">查看</el-button>
                      <el-button type="text" @click="handleDownload(scope.row)"> 下载 </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">取消</el-button>
      <el-button v-if="riskWarehouseType !== 'view'" type="primary" :loading="formLoading"
        @click="submitForm">保存</el-button>
    </div>
  </PageContainer>
</template>
<script>
export default {
  name: 'addRiskIdentify',
  components: {},
  data() {
    return {
      // 正常表单
      formModel: {
        id: '',
        warehouseName: '', // 仓库名称
        gridName: '', // 仓库地址
        riskId: '', // 风险等级id
        riskName: '', // 风险等级
        remark: '' // 说明
      },
      rules: {
        warehouseName: [{ required: true, message: '请输入仓库名称', trigger: 'blur' }],
        gridName: [{ required: true, message: '请输入仓库位置', trigger: 'blur' }]
      },
      attachments: [], // 附件
      riskLevelOptions: [], // 风险等级
      formLoading: false,
      riskWarehouseType: 'add'
    }
  },
  mounted() {
    this.init()
    this.getWarehouseDetail()
    this.riskWarehouseType = this.$route.query.type
  },
  methods: {
    // 获取库房详情
    getWarehouseDetail() {
      let params = {
        warehouseId: this.$route.query.warehouseId
      }
      this.$api.getRiskHouseById(params).then((res) => {
        if (res.code == '200') {
          this.formModel = res.data
          if (res.data.files && res.data.files.length) {
            this.attachments = res.data.files
          }
        }
      })
    },
    // 初始化字典
    init() {
      this.getRiskLevelFn() // 风险等级
    },
    // 获取风险等级
    getRiskLevelFn() {
      let params = {
        userName: this.$store.state.user.userInfo.user.staffName,
        userId: this.$store.state.user.userInfo.user.staffId,
        status: '0'
      }
      this.$api.hscRiskLevelList(params).then((res) => {
        if (res.code == '200') {
          this.riskLevelOptions = res.data.list
        }
      })
    },
    handleView(row) {
      window.open(row.url)
    },
    handleDownload(row) {
      fetch(row.url)
        .then((response) => response.blob())
        .then((blob) => {
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = row.name
          document.body.appendChild(a)
          a.click()
          a.remove()
          window.URL.revokeObjectURL(url)
        })
    },
    beforeAvatarUpload(file) {
      const fileSize = file.size / 1024 / 1024 < 20
      if (!fileSize) {
        this.$message.error('上传图片大小不能超过 20MB!')
      }
      return fileSize
    },
    httpRequest(item, type) {
      this.$api.constructionUpload(__PATH.SPACE_API, 'SecurityLedger/upload', item).then((res) => {
        if (res.code == 200) {
          this.attachments.push({
            name: res.data.name,
            url: res.data.picUrl,
            uid: item.file.uid,
            size: (item.file.size / 1024 / 1024).toFixed(3) + 'M'
          })
          this.$message({
            message: '上传成功',
            type: 'success'
          })
        }
      })
    },
    handleRemove(file, fileList) {
      this.attachments = fileList
    },
    // 风险等级name
    riskLevelChange(val) {
      if (val) {
        this.formModel.riskName = this.riskLevelOptions.find((item) => item.id == val).name
      }
    },
    // 点击确定
    submitForm() {
      this.$refs['formRef'].validate((valid) => {
        if (valid) {
          this.formLoading = true
          let params = {
            ...this.formModel,
            warehouseId: this.$route.query.warehouseId
          }
          if (this.attachments && this.attachments.length) {
            let arr = this.attachments
            params.uploadFile = JSON.stringify(arr)
          } else {
            params.uploadFile = '[]'
          }
          delete params.files
          this.$api.addOrUpdateRiskHouseData(params).then((res) => {
            this.formLoading = false
            if (res.code == '200') {
              this.$message.success(res.message)
              this.$router.go(-1)
            } else {
              this.$message.error(res.message)
            }
          })
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.container-content > div {
  background-color: #fff;
  height: 100%;
  position: relative;
  .warehouse-content-title {
    border-bottom: 1px solid #e4e7ed;
    padding: 12px 24px;
    cursor: pointer;
  }
  .content_box {
    padding: 0 24px;
    background: #fff;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    height: calc(100% - 50px);
    .operation-btn {
      margin-left: 20px;
      line-height: 40px;
      cursor: pointer;
      .addBtn {
        color: #3562db;
      }
      .deleteBtn {
        margin-left: 20px;
        color: #f53f3f;
      }
    }
  }
  .el-input,
  .el-select,
  .el-cascader {
    width: 100%;
  }
}
.toptip {
  box-sizing: border-box;
  height: 50px;
  width: 100%;
  line-height: 50px;
  display: flex;
  font-size: 16px;
  align-items: center;
  border: none;
}
.green_line {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 2px;
  background: #3562db;
  margin-right: 10px;
  vertical-align: middle;
}
.detailContent {
  display: inline-block;
  width: 100%;
  white-space: wrap;
  text-overflow: ellipsis;
}
</style>
