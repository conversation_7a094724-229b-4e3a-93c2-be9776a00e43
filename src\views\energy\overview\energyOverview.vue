<template>
  <PageContainer>
    <div slot="content" v-scrollbarHover class="energy-content">
      <dashboard v-if="dashExampleShow" id="dashExample" :style="{ width: editExampleShow ? 'calc(100% - 220px)' : '100%' }">
        <dash-layout v-bind="dlayout" :debug="false">
          <template v-for="(item, index) in dlayout.items">
            <dash-item v-if="item.status == 1" v-bind.sync="dlayout.items[index]" :key="item.id" @moveEnd="moveEnd" @resizeEnd="resizeEnd">
              <div v-if="item.id == 'SU035'" class="drag_class">
                <ContentCard title="电能耗统计（kwh）" :scrollbarHover="true" :cstyle="{ height: '100%', width: '50%' }">
                  <div slot="content" class="statistics-aside">
                    <AsideBox :data="'powerMonthData' | filterEnergyBox"></AsideBox>
                    <AsideBox :data="'powerYearData' | filterEnergyBox"></AsideBox>
                  </div>
                </ContentCard>
                <ContentCard title="本月用电类型总览（kwh）" :scrollbarHover="true" :cstyle="{ height: '100%', width: '25%' }">
                  <div slot="content" style="height: 100%; overflow: hidden">
                    <div id="powerMonthTypeEchart"></div>
                  </div>
                </ContentCard>
                <ContentCard
                  title="本月用电类型排行（kwh）"
                  :scrollbarHover="true"
                  :cstyle="{ height: '100%', width: '25%' }"
                  :hasMoreOper="['more', 'edit']"
                  @more-oper-event="(val) => allMoreOper(val, '/powerMenu/powerOverview')"
                >
                  <div slot="content" style="height: 100%; overflow: hidden">
                    <div id="powerMonthTypeNoEchart"></div>
                  </div>
                </ContentCard>
              </div>
              <div v-if="item.id == 'CM020'" class="drag_class">
                <ContentCard
                  title="水能耗统计（t）"
                  :scrollbarHover="true"
                  :cstyle="{ height: '100%', width: '100%' }"
                  :hasMoreOper="['more', 'edit']"
                  @more-oper-event="(val) => allMoreOper(val, '/waterMenu/waterOverview')"
                >
                  <div slot="content" class="statistics-aside">
                    <AsideBox :data="'waterMonthData' | filterEnergyBox"></AsideBox>
                    <AsideBox :data="'waterYearData' | filterEnergyBox"></AsideBox>
                  </div>
                </ContentCard>
              </div>
              <div v-if="item.id == 'ZQ4400'" class="drag_class">
                <ContentCard
                  title="蒸汽能耗统计（m³）"
                  :scrollbarHover="true"
                  :cstyle="{ height: '100%', width: '100%' }"
                  :hasMoreOper="['more', 'edit']"
                  @more-oper-event="(val) => allMoreOper(val, '/gasMenu/gasOverview')"
                >
                  <div slot="content" class="statistics-aside">
                    <AsideBox :data="'gasMonthData' | filterEnergyBox"></AsideBox>
                    <AsideBox :data="'gasYearData' | filterEnergyBox"></AsideBox>
                  </div>
                </ContentCard>
              </div>
              <div v-if="item.id == 'SU05000'" class="drag_class">
                <ContentCard
                  title="冷热量能耗统计（kw）"
                  :scrollbarHover="true"
                  :cstyle="{ height: '100%', width: '100%' }"
                  :hasMoreOper="['more', 'edit']"
                  @more-oper-event="(val) => allMoreOper(val, '/coldEnergyMenu/coldEnergyOverview')"
                >
                  <div slot="content" class="statistics-aside">
                    <AsideBox :data="'coldMonthData' | filterEnergyBox"></AsideBox>
                    <AsideBox :data="'coldYearData' | filterEnergyBox"></AsideBox>
                  </div>
                </ContentCard>
              </div>
              <temporaryPage v-if="item.id == 'mock1' || item.id == 'mock2'" :item="item" @all-more-Oper="(val) => allMoreOper(val)" />
            </dash-item>
          </template>
        </dash-layout>
      </dashboard>
      <div id="editExample" :style="{ width: editExampleShow ? '215px' : '0' }">
        <div class="round-box">
          <div class="editExample-title">
            <div>添加模块</div>
            <p>请点击卡片修改显示模块</p>
          </div>
          <div class="editExample-content">
            <div
              v-for="(item, index) in dlayout.items"
              :key="index"
              :class="{ 'editExample-content-item': true, 'active-editExample-item': activeExample.includes(item.id) }"
              @click="addStaging(item)"
            >
              <span>{{ item.componentTitle }}</span>
            </div>
          </div>
          <div class="editExample-footer">
            <el-button type="primary" plain :disabled="hasStatusFalg" @click="cancelStaging">取消</el-button>
            <el-button type="primary" @click="saveStaging">保存</el-button>
          </div>
        </div>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import { Dashboard, DashLayout, DashItem } from 'vue-responsive-dash'
import AsideBox from './component/asideBox'
import temporaryPage from './component/temporaryPage'
import { optimize } from '@/util'
import * as echarts from 'echarts'
import moment from 'moment'
let that
export default {
  name: 'energyOverview',
  components: { Dashboard, DashLayout, DashItem, AsideBox, temporaryPage },
  filters: {
    filterEnergyBox(value) {
      return that.energyMapData.find((item) => item.type === value)
    }
  },
  data() {
    return {
      defaultItems: [
        { id: 'SU035', componentTitle: '电能耗统计（kwh）', x: 0, y: 0, width: 24, height: 6, dragAllowFrom: '.drag_class', resizable: false, draggable: false, status: 1 },
        { id: 'CM020', componentTitle: '水能耗统计（t）', x: 0, y: 6, width: 12, height: 6, dragAllowFrom: '.drag_class', resizable: false, draggable: false, status: 1 },
        { id: 'ZQ4400', componentTitle: '蒸汽能耗统计（m³）', x: 12, y: 6, width: 12, height: 6, dragAllowFrom: '.drag_class', resizable: false, draggable: false, status: 1 },
        { id: 'SU05000', componentTitle: '冷热量能耗统计（kw）', x: 0, y: 12, width: 12, height: 6, dragAllowFrom: '.drag_class', resizable: false, draggable: false, status: 1 },
        { id: 'mock1', componentTitle: '能源流向分析', x: 0, y: 18, width: 12, height: 6, dragAllowFrom: '.drag_class', resizable: false, draggable: false, status: 1 },
        { id: 'mock2', componentTitle: '能耗指标分析', x: 12, y: 18, width: 12, height: 6, dragAllowFrom: '.drag_class', resizable: false, draggable: false, status: 1 }
      ],
      dlayout: {
        breakpoint: 'xs',
        numberOfCols: 24,
        items: []
      },
      reservedItems: [],
      dashExampleShow: true, // 工作台显示隐藏
      editExampleShow: false, // 编辑工作台显示隐藏
      activeExample: [], // 当前激活的工作台
      emodelId: energyEmodelId,
      ratedData: {},
      energyMapData: optimize(
        [
          {
            name: '电力',
            type: 'powerMonthData',
            title: '本月用电',
            titleIcon: 'power-icon',
            echartsDOM: 'powerMonthEchart',
            echartsData: [],
            useText: '月额定用电',
            energyId: 'SU035',
            children: [],
            dateType: 'month',
            echarsDate: 'day',
            value: 0,
            unit: 'kwh',
            rated: 0,
            loading: true
          },
          {
            name: '电力',
            type: 'powerYearData',
            title: '今年用电',
            titleIcon: 'power-icon',
            echartsDOM: 'powerYearEchart',
            echartsData: [],
            useText: '年额定用电',
            energyId: 'SU035',
            children: [],
            dateType: 'year',
            echarsDate: 'month',
            value: 0,
            unit: 'kwh',
            rated: 0,
            loading: true
          },
          {
            name: '用水',
            type: 'waterMonthData',
            title: '本月用水',
            titleIcon: 'water-icon',
            echartsDOM: 'waterMonthEchart',
            echartsData: [],
            useText: '月额定用水',
            energyId: 'CM020',
            children: [],
            dateType: 'month',
            echarsDate: 'day',
            value: 0,
            unit: 't',
            rated: 0,
            loading: true
          },
          {
            name: '用水',
            type: 'waterYearData',
            title: '今年用水',
            titleIcon: 'water-icon',
            echartsDOM: 'waterYearEchart',
            echartsData: [],
            useText: '年额定用水',
            energyId: 'CM020',
            children: [],
            dateType: 'year',
            echarsDate: 'month',
            value: 0,
            unit: 't',
            rated: 0,
            loading: true
          },
          {
            name: '蒸汽',
            type: 'gasMonthData',
            title: '本月用汽',
            titleIcon: 'gas-icon',
            echartsDOM: 'gasMonthEchart',
            echartsData: [],
            useText: '月额定用汽',
            energyId: 'ZQ4400',
            children: [],
            dateType: 'month',
            echarsDate: 'day',
            value: 0,
            unit: 'm³',
            rated: 0,
            loading: true
          },
          {
            name: '蒸汽',
            type: 'gasYearData',
            title: '今年用汽',
            titleIcon: 'gas-icon',
            echartsDOM: 'gasYearEchart',
            echartsData: [],
            children: [],
            useText: '年额定用汽',
            energyId: 'ZQ4400',
            dateType: 'year',
            echarsDate: 'month',
            value: 0,
            unit: 'm³',
            rated: 0,
            loading: true
          },
          {
            name: '冷热量',
            type: 'coldMonthData',
            title: '本月用冷热量',
            titleIcon: 'coldHot-icon',
            echartsDOM: 'coldMonthEchart',
            echartsData: [],
            useText: '月额定用冷热量',
            energyId: 'SU05000',
            children: [],
            dateType: 'month',
            echarsDate: 'day',
            value: 0,
            unit: 'kw',
            rated: 0,
            loading: true
          },
          {
            name: '冷热量',
            type: 'coldYearData',
            title: '今年用冷热量',
            titleIcon: 'coldHot-icon',
            echartsDOM: 'coldYearEchart',
            echartsData: [],
            children: [],
            useText: '年额定用冷热量',
            energyId: 'SU05000',
            dateType: 'year',
            echarsDate: 'month',
            value: 0,
            unit: 'kw',
            rated: 0,
            loading: true
          }
        ],
        ['echartsData', 'value', 'rated', 'loading']
      )
    }
  },
  computed: {
    hasStatusFalg() {
      return this.dlayout.items.filter((e) => e.status === 0).length === this.dlayout.items.length
    }
  },
  watch: {
    '$store.state.settings.sidebarCollapse': {
      handler(val) {
        this.$nextTick(() => {
          let echartsDom = ['powerMonthTypeEchart', 'powerMonthTypeNoEchart']
          setTimeout(() => {
            echartsDom.forEach((item) => {
              echarts.init(document.getElementById(item)).resize()
            })
          }, 900)
        })
      },
      deep: true,
      token: null,
      spaceSsmType: '',
      spacesList: []
    },
    // dragSidebarCollapse
    '$store.state.settings.dragSidebarCollapse': {
      handler(val) {
        this.$nextTick(() => {
          // 页面重载+插件缩放大概需要800ms
          let echartsDom = ['powerMonthTypeEchart', 'powerMonthTypeNoEchart']
          setTimeout(() => {
            echartsDom.forEach((item) => {
              echarts.init(document.getElementById(item)).resize()
            })
          }, 900)
        })
      },
      deep: true
    }
  },
  beforeCreate() {
    that = this
  },
  mounted() {
    this.loginAndEnergyCategory()
    this.getWorktopManageList()
  },
  methods: {
    // 获取工作台管理列表
    getWorktopManageList(type = 'init') {
      this.editExampleShow = false
      this.$store.commit('settings/dragSidebarCollapse', false)
      this.$api.getWorktopManageList({ userId: this.$store.state.user.userInfo.user.staffId, menuType: 14 }).then((res) => {
        if (res.code == 200) {
          const data = res.data
          let componentItems = []
          // 有接口数据取接口数据 无接口数据取字典默认配置数据
          if (data.length) {
            componentItems = data
          } else {
            componentItems = this.defaultItems
          }
          // 遍历增加dragAllowFrom、resizable、draggable字段
          const items = componentItems.map((item) => {
            return {
              id: item.id,
              componentTitle: item.componentTitle,
              x: item.x,
              y: item.y,
              width: item.width,
              height: item.height,
              status: item.status,
              dragAllowFrom: '.drag_class',
              resizable: false,
              draggable: false
            }
          })
          this.dashExampleShow = false
          this.$nextTick(() => {
            this.dashExampleShow = true
            this.dlayout.items = items
            this.reservedItems = JSON.parse(JSON.stringify(items))
            const hasStatusLength = items.filter((e) => e.status === 0).length
            hasStatusLength == items.length ? this.allMoreOper('edit') : ''
            // 已调用能耗初始化后 变更页面结构 重新获取数据
            if (type == 'render') {
              this.getPowerMonthTypeData(this.energyMapData[0])
              this.getRated()
            }
          })
        }
      })
    },
    // 拖拽结束事件
    moveEnd(item) {
      console.log('moveEnd', this.dlayout)
    },
    // 缩放结束事件
    resizeEnd(item) {
      const resizeList = ['workOderType']
      if (resizeList.includes(item.id)) {
        this.$refs[item.id][0].echartsResize()
      }
      // console.log('resizeEnd', this.dlayout)
    },
    // 添加/减少模块
    addStaging(item) {
      if (this.activeExample.includes(item.id)) {
        console.log('减少模块==========', item)
        this.activeExample.splice(this.activeExample.indexOf(item.id), 1)
        this.dlayout.items.map((e) => {
          if (e.id === item.id) {
            e.status = 0
          }
        })
      } else {
        console.log('添加模块==========', item)
        this.activeExample.push(item.id)
        this.dlayout.items.map((e) => {
          if (e.id === item.id) {
            e.status = 1
          }
        })
      }
    },
    // 取消编辑工作台模块
    cancelStaging() {
      this.editExampleShow = false
      this.dashExampleShow = false
      this.$nextTick(() => {
        this.dashExampleShow = true
        this.dlayout.items = JSON.parse(JSON.stringify(this.reservedItems))
        this.$store.commit('settings/dragSidebarCollapse', false)
        this.getPowerMonthTypeData(this.energyMapData[0])
        this.getRated()
      })
    },
    // 保存工作台模块
    saveStaging() {
      const items = this.dlayout.items
      const params = []
      items.forEach((item) => {
        params.push({
          id: item.id,
          componentTitle: item.componentTitle,
          status: item.status,
          x: item.x,
          y: item.y,
          width: item.width,
          height: item.height
        })
      })
      this.$api.saveWorktopManage({ jsonList: JSON.stringify(params), userId: this.$store.state.user.userInfo.user.staffId, menuType: 14 }).then((res) => {
        if (res.code == 200) {
          this.$message.success('保存成功')
          this.getWorktopManageList('render')
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 更多操作事件
    allMoreOper(type, path) {
      if (type === 'more') {
        this.$router.push({ path: path })
      } else if (type === 'edit') {
        this.dlayout.items.map((e) => {
          e.resizable = true
          e.draggable = true
        })
        this.activeExample = this.dlayout.items.filter((e) => e.status === 1).map((e) => e.id)
        this.editExampleShow = true
        this.$store.commit('settings/toggleSidebarCollapse', true)
        this.$store.commit('settings/dragSidebarCollapse', true)
      }
    },
    loginAndEnergyCategory() {
      const param = {
        username: intAccount.username,
        password: intAccount.password
      }
      this.$api.integrationLogin(param).then((res) => {
        this.token = res.result.token
        this.$api.getEnergyCategoryTree({}, { 'X-Access-Token': this.token }).then((res) => {
          this.energyMapData.forEach((ele) => {
            let obj = res.data.find((item) => item.id == ele.energyId)
            ele.energyId = obj?.id ?? ''
            ele.children =
              obj?.children?.map((item) => {
                return {
                  name: item.name,
                  energyId: item.id
                }
              }) ?? []
          })
          this.getTreelist()
        })
        // this.getPowerMonthTypeNoEchart()
      })
    },
    // 获取服务空间树形结构
    getTreelist() {
      this.$api.getStructureTree({}).then((res) => {
        if (res.code == 200) {
          this.spacesList = res.data
          if (__PATH.VUE_APP_HOSPITAL_NODE_ENV !== 'bjsjtyy') {
            const ssmType2List = this.spacesList.filter((e) => e.ssmType == 2) || []
            if (ssmType2List && ssmType2List.length) {
              this.emodelId = Array.from(ssmType2List, (item) => item.id).join(',') || ''
            }
          }
          this.getPowerMonthTypeData(this.energyMapData[0])
          this.getRated()
        }
      })
    },
    getRated() {
      let energyIds = Array.from(this.energyMapData, ({ energyId }) => energyId)
      const data = {
        emodelId: [this.emodelId],
        energyIds: [...new Set(energyIds)],
        indicatorType: 1,
        isEnabled: 1,
        pageNo: 1,
        pageSize: 999
      }
      const monthData = {
        startTime: moment().format('YYYY-MM'),
        endTime: moment().format('YYYY-MM'),
        periodId: 8
      }
      let promiseMonth = this.$api.getIndicatorQuery({ ...data, ...monthData }, { 'X-Access-Token': this.token }).then((res) => {
        if (res.data.records && res.data.records.length) {
          res.data.records.forEach((item) => {
            if (Object.hasOwnProperty.call(this.ratedData, [item.energyId])) {
              Object.assign(this.ratedData[item.energyId], {
                monthRated: item.quotaQuantity,
                monthTimePercent: item.timePercent
              })
            } else {
              Object.assign(this.ratedData, {
                [item.energyId]: {
                  monthRated: item.quotaQuantity,
                  monthTimePercent: item.timePercent
                }
              })
            }
          })
        }
      })
      const yearData = {
        startTime: moment().format('YYYY'),
        endTime: moment().format('YYYY'),
        periodId: 11
      }
      let promiseYear = this.$api.getIndicatorQuery({ ...data, ...yearData }, { 'X-Access-Token': this.token }).then((res) => {
        if (res.data.records && res.data.records.length) {
          res.data.records.forEach((item) => {
            if (Object.hasOwnProperty.call(this.ratedData, [item.energyId])) {
              Object.assign(this.ratedData[item.energyId], {
                yearRated: item.quotaQuantity,
                yearTimePercent: item.timePercent
              })
            } else {
              Object.assign(this.ratedData, {
                [item.energyId]: {
                  yearRated: item.quotaQuantity,
                  yearTimePercent: item.timePercent
                }
              })
            }
          })
        }
      })
      Promise.all([promiseMonth, promiseYear]).then(() => {
        this.getDosageData()
      })
      return
      this.$api.getRatedData({ token: this.token }).then((res) => {
        this.ratedData = res.data
      })
    },
    getDosageData() {
      this.energyMapData.forEach((ele) => {
        if (!ele.energyId) {
          ele.loading = false
          return
        }
        ele.loading = true
        let data = {
          emodelId: this.emodelId,
          timeCategory: ele.dateType,
          energyId: ele.energyId,
          startDate: moment().startOf(ele.dateType).format('YYYY-MM-DD') + ' 00:00:00',
          endDate: moment().endOf(ele.dateType).format('YYYY-MM-DD') + ' 23:59:59'
        }
        this.$api.getModelEnergyDataList(data, { 'X-Access-Token': this.token }).then((res) => {
          ele.value = res.data.length ? res.data[0].value?.toFixed(2) ?? 0 : 0
          for (let key in this.ratedData) {
            if (key == ele.energyId) {
              ele.rated = ele.dateType == 'month' ? this.ratedData[key].monthRated : this.ratedData[key].yearRated
              ele.timePercent = ele.dateType == 'month' ? this.ratedData[key].monthTimePercent : this.ratedData[key].yearTimePercent
              ele.ratedThan = Number(ele.timePercent.replace('%', '')) || 100
            }
          }
        })
        let echarsData = {
          emodelId: this.emodelId,
          timeCategory: ele.echarsDate,
          energyId: ele.energyId,
          startDate: moment().startOf(ele.dateType).format('YYYY-MM-DD') + ' 00:00:00',
          endDate: moment().endOf(ele.dateType).format('YYYY-MM-DD') + ' 23:59:59'
        }
        this.$api.getModelEnergyDataList(echarsData, { 'X-Access-Token': this.token }).then((res) => {
          ele.echartsData = res.data
          ele.loading = false
        })
      })
    },
    getPowerMonthTypeData(item) {
      let data = {
        emodelId: this.emodelId,
        timeCategory: 'month',
        aggregation: 'sum',
        valueName: 'value',
        groupBy: 'energyId',
        energyId: item.children.map((ele) => ele.energyId).join(','),
        startDate: moment().startOf('month').format('YYYY-MM-DD') + ' 00:00:00',
        endDate: moment().endOf('month').format('YYYY-MM-DD') + ' 23:59:59'
      }
      this.$api.getModelEnergyDataList(data, { 'X-Access-Token': this.token }).then((res) => {
        const list = res.data.map((ele) => {
          return {
            name: ele.energyName,
            ...ele
          }
        })
        this.getPowerMonthTypeEchart(list)
      })
      const ssmType3List = this.spacesList.filter((e) => e.ssmType == 3) || []
      let columnData = {
        emodelId: Array.from(ssmType3List, (item) => item.id).join(',') || '',
        timeCategory: 'month',
        aggregation: 'sum',
        valueName: 'value',
        groupBy: 'emodelId',
        energyId: item.energyId,
        startDate: moment().startOf('month').format('YYYY-MM-DD') + ' 00:00:00',
        endDate: moment().endOf('month').format('YYYY-MM-DD') + ' 23:59:59'
      }
      this.$api.getModelEnergyDataList(columnData, { 'X-Access-Token': this.token }).then((res) => {
        let resData = res.data
        // if (__PATH.VUE_APP_HOSPITAL_NODE_ENV === 'bjsjtyy') {
        //   resData = res.data.filter((val) => val.emodelName != '综合急诊急救楼' && val.emodelName != '北京世纪坛医院')
        // } else if (__PATH.VUE_APP_HOSPITAL_NODE_ENV === 'outernet') {
        //   resData = res.data.filter((val) => val.emodelName != '中国医学科学院肿瘤医院深圳医院')
        // }
        const list = resData.map((ele) => {
          return {
            name: ele.emodelName,
            value: ele.value.toFixed(0)
          }
        })
        this.getPowerMonthTypeNoEchart(list)
      })
    },
    // 本月用电类型总览echarts
    getPowerMonthTypeEchart(arr) {
      const nameList = Array.from(arr, (item) => item.name)
      const getchart = echarts.init(document.getElementById('powerMonthTypeEchart'))
      const option = {
        color: ['#3562DB', '#FF9435', '#00BC6D'],
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          type: 'scroll',
          top: 220,
          bottom: 20,
          itemWidth: 15,
          itemHeight: 15,
          itemGap: 15,
          data: nameList,
          formatter: function (name) {
            var oa = option.series[0].data
            var num = oa.reduce((sum, e) => sum + e.value, 0)
            for (var i = 0; i < option.series[0].data.length; i++) {
              if (name === oa[i].energyName) {
                return ' ' + name + '    ' + ((oa[i].value / num) * 100).toFixed(2) + '%' + '     ' + oa[i].value + 'kwh'
              }
            }
          }
        },
        series: [
          {
            type: 'pie',
            // roseType: 'radius',
            radius: ['45%'],
            center: ['50%', '30%'],
            data: arr,
            label: {
              normal: {
                show: false
              }
            }
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 本月用电类型排行echarts
    getPowerMonthTypeNoEchart(data) {
      const getchart = echarts.init(document.getElementById('powerMonthTypeNoEchart'))
      let colors = ['#3562DB', '#00BC6D', '#FF9435']
      data.sort((a, b) => {
        return a.value - b.value
      })
      data.forEach((ele, index) => {
        ele['itemStyle'] = {
          color: colors[index % 3]
        }
      })
      const nameList = Array.from(data, ({ name }) => name)
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        label: {
          show: true,
          position: 'right',
          color: '#121F3E',
          fontSize: '12px'
        },
        grid: {
          top: '4%',
          left: '4%',
          right: '10%',
          bottom: '5%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          splitNumber: 3
        },
        yAxis: [
          {
            type: 'category',
            data: nameList,
            axisLabel: {
              formatter: function (val) {
                var strs = val.split('') // 字符串数组
                var str = ''
                // strs循环 forEach 每五个字符增加换行符
                strs.forEach((item, index) => {
                  if (index % 7 === 0 && index !== 0) {
                    str += '\n'
                  }
                  str += item
                })
                return str
              }
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            }
          }
        ],
        series: [
          {
            type: 'bar',
            barWidth: 10,
            data: data,
            itemStyle: {
              color: '#FF9435'
            }
          }
        ],
        dataZoom: [
          {
            orient: 'vertical',
            show: true,
            type: 'slider',
            start: 100,
            // end: 100,
            width: 8,
            left: '99%',
            borderColor: 'rgba(43,48,67,.1)',
            fillerColor: '#3562DB',
            zoomLock: true,
            showDataShadow: false,
            backgroundColor: 'rgba(43,48,67,.1)',
            showDetail: false,
            // realtime: true,
            filterMode: 'filter',
            handleIcon: 'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
            handleStyle: {
              color: '#6580b8',
              borderColor: '#6580b8'
            },
            maxValueSpan: 7,
            minValueSpan: 7,
            brushSelect: false
          },
          {
            type: 'inside',
            show: false,
            yAxisIndex: [0, 1],
            height: 8
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 点击查看跳转对应的二级菜单
    detailJump(type) {
      this.$router.push({ path: type })
    }
  }
}
</script>
<style lang="scss" scoped>
.energy-content {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: space-between;
  #dashExample {
    height: 100%;
    // background: #f5f7f9;
    overflow-y: auto;
    overflow-x: hidden;
    :deep(.placeholder) {
      background: #e2e6eb !important;
      border-radius: 10px;
      opacity: 1;
    }
  }
  #editExample {
    height: 100%;
    // background-color: #fff;
    box-shadow: 10px 0 10px -10px #c7c7c7;
    box-shadow: 10px 0 10px -10px #c7c7c7;
    padding: 16px 0;
    display: flex;
    flex-direction: column;
    .round-box {
      width: 100%;
      height: 100%;
      background-color: #08305d0a;
      border-radius: 10px;
      padding: 10px 0;
      display: flex;
      flex-direction: column;
    }
    .editExample-title {
      > div {
        font-size: 16px;
        font-family: PingFangSC-Regular-Blod;
        color: #121f3e;
        margin: 0 0 12px 15px;
        height: 22px;
        line-height: 22px;
      }
      > p {
        height: 18px;
        font-size: 12px;
        line-height: 18px;
        margin: 0 0 12px 15px;
        color: #999;
      }
    }
    .editExample-content {
      flex: 1;
      // height: calc(100% - 50px);
      overflow-y: auto;
      padding: 10px 20px;
      .editExample-content-item {
        cursor: pointer;
        width: 100%;
        box-sizing: border-box;
        height: 40px;
        line-height: 40px;
        margin-bottom: 16px;
        border-radius: 4px;
        padding: 0 10px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        background-color: #fff;
        text-align: center;
        color: #121f3e;
        font-size: 13px;
        font-family: 'PingFang SC-Regular', 'PingFang SC';
        &:hover {
          // color: #3562db;
          // background: rgba(53, 98, 219, 0.2);
          box-shadow: 0 4px 12px 0 rgb(17 19 23 / 10%);
        }
      }
      .active-editExample-item {
        background-color: #3562db !important;
        color: #fff !important;
        border-color: #3562db !important;
      }
    }
    .editExample-footer {
      text-align: right;
      padding: 5px 15px;
    }
  }
  .drag_class {
    width: 100%;
    height: 100%;
    display: flex;
    border-radius: 4px;
    overflow: hidden;
  }
  ::v-deep .box-card {
    border-radius: 0;
  }
  .detail-btn {
    position: absolute;
    top: 0;
    right: 0;
    font-size: 14px;
    font-family: 'PingFang SC-Regular', 'PingFang SC';
    font-weight: 400;
    color: #3562db;
    cursor: pointer;
  }
  .statistics-aside {
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    height: 100%;
  }
  #powerMonthTypeEchart,
  #powerMonthTypeNoEchart {
    width: 100%;
    height: 100%;
    z-index: 2;
  }
}
</style>
