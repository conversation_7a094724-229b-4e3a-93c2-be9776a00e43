<template>
  <div style="height: 100%;">
    <div class="search-from">
      <el-input v-model.trim="filters.dictLabel" placeholder="标签名称" style="width: 200px;" clearable></el-input>
      <el-select v-model="filters.dictType" class="sino_sdcp_input mr15" placeholder="安全标志类型" filterable clearable>
        <el-option v-for="(item, index) in riskLevelList" :key="index" :label="item.dictLabel" :value="item.dictValue" class="set_zindex"></el-option>
      </el-select>
      <el-button type="primary" plain @click="resetData">重置</el-button>
      <el-button type="primary" @click="searchClick">查询</el-button>
    </div>
    <div class="middle_tools">
      <el-button type="primary" icon="el-icon-plus" @click="setDate('add')">新增</el-button>
      <el-button type="primary" icon="el-icon-edit" :disabled="multipleSelection.length != 1" @click="setDate('edit')">编辑</el-button>
      <el-button type="primary" icon="el-icon-delete" :disabled="multipleSelection.length != 1" @click="deleteData">删除</el-button>
    </div>
    <div class="contentTable">
      <div class="contentTable-main table-content">
        <el-table
          v-loading="tableLoading"
          :data="tableData"
          :height="tableHeight"
          title="双击查看详情"
          border
          stripe
          @selection-change="handleSelectionChange"
          @row-dblclick="dblclick"
        >
          <el-table-column type="selection" width="55" align="center" fixed="left"></el-table-column>
          <el-table-column type="index" label="序号" width="55">
            <template slot-scope="scope">
              <span>{{ (paginationData.currentPage - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="dictLabel" label="字典标签" width="150" show-overflow-tooltip></el-table-column>
          <el-table-column prop="dictKey" label="字典键值" width="80" show-overflow-tooltip></el-table-column>
          <el-table-column prop="dictSort" label="排序号" width="80" show-overflow-tooltip></el-table-column>
          <el-table-column prop="attachmentUrl" label="图片" min-width="40" show-overflow-tooltip>
            <template slot-scope="scope">
              <div style="cursor: pointer;" @click="showPicture(scope.row)">
                <img width="30" height="30" :src="'data:image/png;base64' + scope.row.attachmentUrl" alt="" />
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="dictType" show-overflow-tooltip label="标志类型"></el-table-column>
          <el-table-column prop="remarks" show-overflow-tooltip label="备注信息"></el-table-column>
          <el-table-column prop="createDate" show-overflow-tooltip label="更新时间"></el-table-column>
        </el-table>
      </div>
      <div class="contentTable-footer">
        <el-pagination
          class="pagination"
          :current-page="paginationData.currentPage"
          :page-sizes="[15, 30, 50, 100]"
          :page-size="paginationData.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="paginationData.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
      <el-dialog v-loading="saveLoading" custom-class="model-dialog" :title="addtitle" :visible.sync="dialogVisibleRole" :before-close="empty" @close="empty">
        <div v-loading style="height: 100%; background-color: #fff; padding: 10px;">
          <el-form ref="formInline" :model="formInline" :rules="rules" :inline="true" class="advanced-search-form" label-position="right" label-width="110px" :disabled="disabled">
            <el-form-item label="字典标签：" prop="dictLabel">
              <el-input
                v-if="!disabled"
                v-model="formInline.dictLabel"
                placeholder="请输入字典名称"
                autocomplete="off"
                :maxlength="50"
                show-word-limit
                style="width: 260px;"
              ></el-input>
              <span v-else style="width: 260px; display: inline-block;">{{ formInline.dictLabel }}</span>
            </el-form-item>
            <el-form-item label="标签类别：" prop="dictType">
              <el-select v-if="!disabled" ref="groupRis" v-model="formInline.dictType" class="mr15" placeholder="请选择标签类别" style="width: 260px !important;">
                <el-option v-for="item in riskLevelList" :key="item.value" style="width: 260px !important;" :label="item.dictLabel" :value="item.dictValue"></el-option>
              </el-select>
              <span v-else style="width: 260px; display: inline-block;">{{ formInline.dictType }}</span>
            </el-form-item>
            <br />
            <el-form-item label="字典键值：" prop="dictKey">
              <el-input
                v-if="!disabled"
                v-model.number="formInline.dictKey"
                placeholder="请输入字典键值"
                autocomplete="off"
                show-word-limit
                maxlength="6"
                style="width: 260px;"
              ></el-input>
              <span v-else style="width: 260px; display: inline-block;">{{ formInline.dictKey }}</span>
            </el-form-item>
            <el-form-item label="排序号：" prop="dictSort">
              <el-input v-if="!disabled" v-model.number="formInline.dictSort" placeholder="请输入排序号" maxlength="6" autocomplete="off" style="width: 260px;"></el-input>
              <span v-else style="width: 260px; display: inline-block;">{{ formInline.dictSort }}</span>
            </el-form-item>
            <el-form-item label="图片：" prop="attachment" class="is-required">
              <el-upload
                v-if="!disabled"
                ref="uploadFile"
                drag
                multiple
                action="string"
                list-type="picture-card"
                class="mterial_file"
                :file-list="fileEcho"
                :http-request="httpRequest"
                accept=".jpg, .jpeg, .png, .JPG, .JPEG, .bmp"
                :limit="1"
                :on-exceed="handleExceed"
                :on-preview="handlePictureCardPreview"
                :beforeUpload="beforeAvatarUpload"
                :on-remove="handleRemove"
              >
                <i class="el-icon-upload"></i>

                <div class="el-upload__text" style="top: 33px;">
                  将文件拖到此处，或
                  <em>点击上传</em>
                </div>
                <div slot="tip" class="el-upload__tip">可上传单张图片,图片分辨率不低于50px*50px</div>
              </el-upload>
              <div v-else style="width: 100px;">
                <img width="100%" :src="'data:image/png;base64' + formInline.attachment" alt="" />
              </div>
            </el-form-item>
            <br />
            <el-form-item label="备注信息：" prop="remarks" class="sino-form-textarea">
              <el-input
                v-if="!disabled"
                v-model.trim="formInline.remarks"
                show-word-limit
                placeholder="请输入备注信息"
                class="sino-textarea"
                style="width: 480px;"
                maxlength="800"
                type="textarea"
                :autosize="{ minRows: 8, maxRows: 8 }"
              ></el-input>
              <span v-else style="width: 500px; display: inline-block;">{{ formInline.remarks }}</span>
            </el-form-item>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <span v-if="!disabled">
            <el-button type="primary" plain @click="empty">取 消</el-button>
            <el-button type="primary" @click="submit">确定</el-button>
          </span>
          <span v-else>
            <el-button type="primary" plain @click="empty">关 闭</el-button>
          </span>
        </span>
      </el-dialog>
      <!-- 查看上传图片 -->
      <el-dialog title="查看图片" :visible.sync="dialogVisible" style="z-index: 99999;">
        <div style="text-align: center;">
          <img width="50%" style="margin: auto;" :src="dialogImageUrl" alt />
        </div>
      </el-dialog>
      <imgCarousel :dialogVisibleImg="dialogVisibleImg" :imgArr="imgArr" @closeDialog="closeDialogImg"></imgCarousel>
    </div>
  </div>
</template>

<script>
import tableListMixin from '@/mixins/tableListMixin.js'
import imgCarousel from '@/components/imgCarousel/imgCarousel.vue'
export default {
  components: { imgCarousel },
  mixins: [tableListMixin],
  data() {
    return {
      setType: '',
      addtitle: '新增字典',
      disabled: false,
      tableLoading: false,
      tableData: [],
      filters: {
        dictLabel: '',
        dictType: ''
      },
      riskLevelList: [],
      multipleSelection: [],
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      saveLoading: false,
      dialogVisibleRole: false,
      dialogVisible: false,
      formInline: {
        dictLabel: '',
        dictType: '',
        dictKey: '',
        dictSort: '',
        remarks: '',
        attachment: []
      },
      rules: {
        dictLabel: [{ required: true, message: '请输入字典标签', trigger: 'change' }],
        dictType: [{ required: true, message: '请选择标签类别', trigger: 'change' }],
        dictKey: [{ required: true, message: '请输入字典键值', trigger: 'change' }]
        // attachment: [
        //   { required: true, message: "请上传图片", trigger: "change" },
        // ],
      },
      fileEcho: [],
      dialogImageUrl: '',
      dialogVisibleImg: false,
      imgArr: []
    }
  },
  created() {
    this.getDictValue()
    this.getData()
  },
  methods: {
    // 页面字典项
    getDictValue() {
      this.$api
        .ipsmGetDictValue({
          dictType: 'accident_type',
          isShowParent: 0
        })
        .then((res) => {
          if (res.code == 200) {
            this.riskLevelList = res.data
          }
        })
    },
    getData() {
      let data = {
        pageSize: this.paginationData.pageSize,
        currentPage: this.paginationData.currentPage,
        dataDictId: '1',
        ...this.filters
      }
      this.$api.ipsmGetAccidentTypeList(data).then((res) => {
        this.tableLoading = true
        if (res.code == 200) {
          this.tableData = res.data.list
          this.paginationData.total = parseInt(res.data.sum)
        }
        this.tableLoading = false
      })
    },
    resetData() {
      this.paginationData = {
        currentPage: 1,
        pageSize: 15,
        total: 0
      }
      this.filters.dictLabel = ''
      this.filters.dictType = ''
      this.getData()
    },
    searchClick() {
      this.paginationData.currentPage = 1
      this.getData()
    },
    setDate(type, id) {
      this.setType = type
      this.addtitle = type == 'add' ? '新增字典' : type == 'edit' ? '修改字典' : '查看详情'
      this.disabled = type == 'check'
      if (type != 'add') {
        // 获取详情请求
        this.$api
          .ipsmGetAccidentTypeDetail({
            id: id || this.multipleSelection[0].id
          })
          .then((res) => {
            if (res.code == 200) {
              this.formInline = res.data.accidentTypeDetail
              this.formInline.attachment = res.data.accidentTypeDetail.attachmentUrl
              this.fileEcho.push({
                url: res.data.accidentTypeDetail.attachmentUrl,
                name: ''
              })
              this.attachmentStr = res.data.accidentTypeDetail.attachmentUrl
            } else {
              return this.$message.error(res.message)
            }
          })
      }
      this.dialogVisibleRole = true
    },
    deleteData() {
      this.$confirm('确定要删除这条信息吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let data = {
          id: this.multipleSelection[0].id
        }
        this.$api.ipsmDeleteAccidentType(data).then((res) => {
          if (res.code == 200) {
            // 删除最后一页的最后一条数据时跳转回最后一页的上一页
            this.paginationData.currentPage = this.$tools.paginationData(this.paginationData.total, this.paginationData.pageSize, this.paginationData.currentPage)
            this.$message.success(res.message)
            this.getData()
          } else {
            this.$message.error(res.message)
          }
        })
      })
    },
    dblclick(val) {
      this.setDate('check', val.id)
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    showPicture(row) {
      this.imgArr = [row.attachmentUrl]
      this.dialogVisibleImg = true
    },
    handleSizeChange(val) {
      this.paginationData.currentPage = 1
      this.paginationData.pageSize = val
      this.getData()
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this.getData()
    },
    // 新增 修改提交
    submit() {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          if (this.formInline.attachment.length == 0) {
            return this.$message.error('请上传图片')
          }
          this.saveLoading = true
          let imgUrl = true // 是否传图片
          // 未修改图片为beas64格式,接口传空
          if (this.formInline.attachment[0] == 'd' && this.setType == 'edit') {
            imgUrl = false
          }
          let data = {
            ...this.formInline,
            dataDictId: '1',
            id: this.setType == 'edit' ? this.multipleSelection[0].id : '',
            attachmentUrl: imgUrl ? this.formInline.attachment : '',
            attachmentStr: this.attachmentStr
          }
          console.log(data, 'data')
          delete data.attachment
          this.$api.ipsmSaveAccidentType(data).then((res) => {
            this.saveLoading = false
            if (res.code == 200) {
              this.empty()
              this.getData()
              return this.$message.success(res.message)
            } else {
              return this.$message.error(res.message)
            }
          })
        }
      })
    },
    // 重置新增的表单
    empty() {
      this.formInline.attachment = []
      this.fileEcho = []
      this.attachmentStr = ''
      this.$refs['formInline'].resetFields()
      this.dialogVisibleRole = false
    },
    // 上传图片相关
    httpRequest(item) {
      this.formInline.attachment.push(item.file)
    },
    handleExceed() {
      this.$message.error('最多上传一份文件')
    },
    beforeAvatarUpload(file) {
      const isLt5M = file.size / 1024 / 1024 < 5
      if (!isLt5M) {
        this.$message.error('上传图片大小不能超过 5MB!')
        return false
      }
      if (file.name.indexOf(',') != -1) {
        this.$message.error('非法的文件名')
        return false
      }
    },
    handleRemove(file, fileList) {
      this.formInline.attachment = []
      this.attachmentStr = ''
      fileList.forEach((item) => {
        this.formInline.attachment.push(item.raw)
      })
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    // 查看图片
    closeDialogImg() {
      this.dialogVisibleImg = false
      this.imgArr = []
    }
  }
}
</script>

<style lang="scss" scoped>
.search-from {
  padding-bottom: 12px;

  & > div {
    margin-right: 10px;
  }

  & > button {
    margin-top: 12px;
  }
}

.middle_tools {
  // margin-top: 20px;
  margin-bottom: 10px;
}

.contentTable {
  height: calc(100% - 80px);
  display: flex;
  flex-direction: column;

  .contentTable-main {
    height: calc(100% - 40px);
    // flex: 1;
    overflow: auto;
  }

  .contentTable-footer {
    padding: 10px 0;
  }
}

::v-deep .mterial_file > .el-upload-list {
  position: absolute;
  top: 0;
  width: 500px;
  margin-left: 430px;
  max-height: 160px;
  // overflow: auto;
}

::v-deep .mterial_file > .el-upload--picture-card {
  border: none;
  width: 300px;
}

::v-deep .mterial_file .el-upload .el-upload-dragger {
  width: 300px;
  height: 148px;
}

::v-deep .mterial_file .el-upload__text {
  position: absolute;
  left: 57px;
  color: #606266;
  font-size: 14px;
  text-align: center;
}
</style>
