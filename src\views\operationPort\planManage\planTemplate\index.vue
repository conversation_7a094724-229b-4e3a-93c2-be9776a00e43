<template>
  <PageContainer>
    <div slot="header" class="control-btn-header">
      <div class="search-from">
        <el-input v-model="searchFrom.planName" placeholder="预案名称" clearable style="width: 200px"></el-input>
        <el-select v-model="searchFrom.planType" placeholder="预案类型" clearable>
          <el-option v-for="item in planTypeList" :key="item.thirdSystemCode" :label="item.thirdSystemName" :value="item.thirdSystemCode"></el-option>
        </el-select>
        <div style="display: inline-block">
          <el-button type="primary" plain @click="resetForm">重置</el-button>
          <el-button type="primary" @click="searchForm">查询</el-button>
          <el-button style="position: absolute; right: 0px; top: calc(50% + 5px); transform: translateY(-50%)" type="primary" icon="el-icon-plus" @click="control('add')"
            >创建智能预案模版</el-button
          >
        </div>
        <el-image-viewer v-if="showViewer" :on-close="() => (showViewer = false)" :url-list="iconPathList" />
      </div>
    </div>
    <div slot="content" class="table-content">
      <TablePage
        ref="table"
        v-loading="tableLoading"
        :showPage="true"
        :tableColumn="tableColumn"
        :data="tableData"
        height="calc(100% - 40px)"
        :pageData="pageData"
        :pageProps="pageProps"
        @pagination="paginationChange"
      >
        <template slot="empty">
          <img src="../../../../assets/images/operationPort/yuan-empty.png" style="width: 307px; height: 226px" />
          <p style="color: #ccced3; margin: 24px 0px 38px 0px; line-height: 16px">暂无预案模版，请创建预案模版</p>
          <el-button type="primary" icon="el-icon-plus" @click="control('add')">创建智能预案模版</el-button>
        </template>
      </TablePage>
      <el-drawer title="法规文案" custom-class="content-drawer" :visible.sync="isDrawer" direction="rtl">
        <div v-html="drawerText"></div>
      </el-drawer>
    </div>
  </PageContainer>
</template>
<script lang="jsx">
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
export default {
  name: 'planTemplate',
  components: {
    ElImageViewer
  },
  beforeRouteEnter(to, from, next) {
    let names = []
    to.matched.map((v, i) => {
      if (i > 0) {
        v.components.default.name && names.push(v.components.default.name)
      }
    })
    // 进入页面时，先将当前页面的 name 信息存入 keep-alive 全局状态
    setTimeout(() => {
      next((vm) => {
        vm.$store.commit('keepAlive/add', names)
      })
    }, 0)
  },
  async beforeRouteLeave(to, from, next) {
    // 因为并不是所有的路由跳转都需要将当前页面进行缓存，例如最常见的情况，从列表页进入详情页，则需要将列表页缓存，而从列表页跳转到其它页面，则不需要将列表页缓存
    if (!['createTemplate', 'templateDetails'].includes(to.name)) {
      // 注意：上面校验的是路由的 name ，下面记录的是当前页面的 name
      await this.$store.commit('keepAlive/clean')
    }
    next()
  },
  data() {
    return {
      isDrawer: false,
      drawerText: '',
      tableLoading: false,
      showViewer: false,
      iconPathList: [], // 图片列表
      planTypeList: [], // 预案类型列表
      searchFrom: {
        planName: '',
        planType: ''
      },
      tableColumn: [
        {
          prop: 'icon',
          label: '',
          width: 100,
          render: (h, row) => {
            const flowData = JSON.parse(row.row.regulationsFlow || '[]')
            const imageUrl = flowData[0]?.url
            if (!imageUrl) return <div></div>
            return <img style="width:48px; height:48px;cursor: pointer;" src={this.$tools.imgUrlTranslation(imageUrl)} onClick={() => this.viewImage(row.row)}></img>
          }
        },
        {
          width: 180,
          prop: 'planName',
          label: '预案名称'
        },
        {
          width: 120,
          prop: 'planType',
          label: '预案类型',
          formatter: (scope) => {
            return scope.row.planType ? this.planTypeList.find((v) => v.thirdSystemCode == scope.row.planType)?.thirdSystemName : ''
          }
        },
        {
          prop: 'path',
          label: '法规文案',
          showOverflowTooltip: false,
          render: (h, row) => {
            return <div class="twoLinesEllipsis" style="cursor: pointer;" onClick={() => this.control('viewText', row.row)} domPropsInnerHTML={row.row.regulationsText}></div>
          }
        },
        {
          width: 300,
          prop: 'regulationsDoc',
          label: '法规文档',
          render: (h, row) => {
            return (
              <div style="cursor: pointer; color: #3562DB;" onClick={() => window.open(this.$tools.imgUrlTranslation(JSON.parse(row.row.regulationsDoc)[0]?.url ?? ''), '_blank')}>
                {JSON.parse(row.row.regulationsDoc)[0]?.name ?? ''}
              </div>
            )
          }
        },
        {
          width: 180,
          prop: 'operation',
          label: '操作',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span class="operationBtn-span" style="color: #3562DB" onClick={() => this.control('detail', row.row)}>
                  查看
                </span>
                <span class="operationBtn-span" style="color: #3562DB" onClick={() => this.control('preview', row.row)}>
                  预览
                </span>
                <span class="operationBtn-span" style="color: #3562DB" onClick={() => this.control('edit', row.row)}>
                  编辑
                </span>
                <el-dropdown onCommand={(val) => this.control(val, row.row)}>
                  <span class="el-dropdown-link" style="color: #3562DB">
                    · · ·
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="copy">复制</el-dropdown-item>
                    <el-dropdown-item command="del">删除</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            )
          }
        }
      ],
      tableData: [],
      pageData: {
        current: 1,
        size: 15,
        total: 0
      },
      pageProps: {
        page: 'current',
        pageSize: 'size',
        total: 'total'
      }
    }
  },
  watch: {},
  activated() {
    this.searchForm()
  },
  mounted() {
    this.getAlarmSystem()
    this.getPlanTemplate()
  },
  methods: {
    // 查看图片
    viewImage(row) {
      let icon = JSON.parse(row.regulationsFlow)
      if (!icon.length) {
        this.$message.info('当前没有可预览的图片')
        return
      }
      this.iconPathList = [this.$tools.imgUrlTranslation(icon[0].url)]
      this.showViewer = true
    },
    // 查询
    searchForm() {
      this.getPlanTemplate()
    },
    // 重置查询
    resetForm() {
      Object.assign(this.$data.searchFrom, this.$options.data().searchFrom)
      this.searchForm()
    },
    control(type, row) {
      if (['add', 'edit', 'copy', 'preview'].includes(type)) {
        // 添加 编辑 复制
        let query = type !== 'add' ? { id: row?.id ?? '' } : {}
        this.$router.push({
          path: '/planManage/planTemplate/createTemplate',
          query: {
            type,
            ...query
          }
        })
      } else if (type == 'detail') {
        // 详情
        this.$router.push({
          path: '/planManage/planTemplate/templateDetails',
          query: {
            type,
            id: row?.id ?? ''
          }
        })
      } else if (type == 'del') {
        // 删除
        this.$confirm('确认要删除当前预案模版吗？删除后数据不可恢复。', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '删除预案',
          cancelButtonText: '取消操作',
          type: 'warning'
        }).then(() => {
          this.$api.DeleteBasicConfig({ id: row.id }, { 'operation-type': 3, 'operation-id': row.id, 'operation-name': row.planName }).then((res) => {
            if (res.code == 200 && res.data) {
              this.$message({ message: '模版删除成功', type: 'success' })
              this.isLastPage(1)
              this.searchForm()
            } else {
              this.$message({ message: res.msg, type: 'error' })
            }
          })
        })
      } else if (type == 'viewText') {
        // 预览法规文案
        this.drawerText = row.regulationsText
        this.isDrawer = true
      }
    },
    // 获取预案模板列表
    getPlanTemplate() {
      let param = {
        ...this.searchFrom,
        pageSize: this.pageData.size,
        page: this.pageData.current
      }
      this.tableLoading = true
      this.$api
        .GetPlanTemplate(param)
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.records
            this.pageData.total = res.data.total
          }
          this.tableLoading = false
        })
        .catch(() => {
          this.tableLoading = false
        })
    },
    // 判断当前页是否是最后一页
    isLastPage(deleteNum) {
      let deleteAfterPage = Math.ceil((this.pageData.total - deleteNum) / this.pageData.size)
      let currentPage = this.pageData.current > deleteAfterPage ? deleteAfterPage : this.pageData.current
      this.pageData.current = currentPage < 1 ? 1 : currentPage
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getPlanTemplate()
    },
    // 获取预案类型
    getAlarmSystem() {
      this.$api.getAlarmThirdSystemData().then((res) => {
        if (res.code == 200) {
          this.planTypeList = res.data
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.control-btn-header {
  padding: 0px 10px 10px 10px !important;
  .search-from {
    padding-right: 180px;
    position: relative;
    & > div {
      margin-top: 10px;
      margin-right: 10px;
    }
  }
}
.table-content {
  margin-top: 15px;
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 15px);
  padding: 10px;
  ::v-deep(.content-drawer) {
    width: 40% !important;
    .el-drawer__header {
      height: 56px;
      margin: 0px;
      padding: 0px 26px;
      font-size: 18px;
      color: #333333;
      line-height: 18px;
    }
    .el-drawer__body {
      background: #f6f5fa;
      padding: 24px;
      div {
        width: 100%;
        height: 100%;
        background: #fff;
        padding: 16px;
        border-radius: 4px;
        font-weight: 500;
        font-size: 14px;
        color: #333333;
        word-wrap: break-word;
        word-break: break-all;
        overflow-x: auto;
        p {
          margin: 0px;
        }
      }
    }
  }
}
</style>
<style lang="scss">
.twoLinesEllipsis {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  p {
    margin: 0px;
  }
}
.operationBtn-span {
  margin-right: 10px;
}
</style>
