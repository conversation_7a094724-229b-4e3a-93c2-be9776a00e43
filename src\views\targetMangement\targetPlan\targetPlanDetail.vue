<template>
  <PageContainer :footer="true">
    <div slot="content" v-loading="pageLoading" class="table-content">
      <div class="targetPlanDetail-content-title" @click="goBack"><i class="el-icon-arrow-left"></i><span style="margin-left: 10px">计划详情</span></div>
      <div class="content_box">
        <div class="detaiBox">
          <div v-for="(item, index) in targetPlanStatisticsData" :key="index" class="list-item">
            <span>{{ item.name }}</span
            ><span>{{ targetPlanInfo[item.key] || '--' }}</span>
          </div>
        </div>
        <div class="scopeBox">
          <div class="scopeBox-title">考察范围：</div>
          <div v-for="(item, index) in scopeList" :key="index" class="list-item">
            <div class="list-item-main">
              <span>考核对象：</span><span>{{ item.objectName || '' }}</span>
            </div>
            <div class="list-item-main">
              <span>对象类型：</span><span>{{ objectTypeList[item.objectType].text || '' }}</span>
            </div>
            <div class="list-item-main">
              <span>操作：</span>
              <span v-if="targetPlanInfo.state != 0" @click="operation(item)">最新指标结果</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">关闭</el-button>
    </div>
  </PageContainer>
</template>
<script>
export default {
  name: 'targetPlanDetail',
  data() {
    return {
      pageLoading: false,
      targetPlanStatisticsData: [
        {
          name: '指标计划名称：',
          key: 'name'
        },
        {
          name: '指标模版：',
          key: 'templateManageName'
        },
        {
          name: '指标周期名称：',
          key: 'cycleRuleName'
        },
        {
          name: '开始时间：',
          key: 'startTime'
        },
        {
          name: '结束时间：',
          key: 'endTimeName'
        },
        {
          name: '计划说明：',
          key: 'remark'
        }
      ],
      objectTypeList: {
        0: {
          text: '单位'
        },
        1: {
          text: '部门'
        },
        2: {
          text: '人员'
        },
        3: {
          text: '岗位'
        },
        4: {
          text: '职位'
        },
        5: {
          text: '空间'
        },
        6: {
          text: '设备'
        },
        7: {
          text: '全局'
        }
      },
      targetPlanInfo: {},
      scopeList: [], // 考察范围列表
      manageId: ''
    }
  },
  mounted() {
    this.getTargetPlanId()
  },
  methods: {
    getTargetPlanId() {
      const planId = this.$route.query.planId
      this.pageLoading = true
      this.$api.getTargetPlanById({ planId: planId }).then((res) => {
        if (res.code == 200) {
          this.targetPlanInfo = res.data
          this.scopeList = res.data.list
          if (this.targetPlanInfo.longTerm === 1) {
            this.targetPlanInfo.endTimeName = '长期'
          } else {
            this.targetPlanInfo.endTimeName = this.targetPlanInfo.endTime
          }
          this.manageId = res.data.templateManageId
        } else if (res.message) {
          this.$message.error(res.message)
        }
        this.pageLoading = false
      })
    },
    operation(data) {
      this.$router.push({
        path: '/targetAnalysis/targetResult',
        query: {
          planId: data.indicatorPlanId,
          manageId: this.manageId,
          nodeCode: data.objectId,
          nodeName: data.objectName
        }
      })
    },
    // 返回
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>
<style lang="scss" scoped>
.container-content > div {
  background-color: #fff;
  height: 100%;
  position: relative;
  overflow-y: auto;
  .targetPlanDetail-content-title {
    border-bottom: 1px solid #e4e7ed;
    padding: 13px 24px;
    cursor: pointer;
  }
  .content_box {
    padding: 16px 24px;
    background: #fff;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    .detaiBox {
      font-size: 14px;
      .list-item {
        margin-bottom: 16px;
      }
      .list-item span:nth-child(1) {
        display: inline-block;
        width: 100px;
        color: #7f848c;
        font-weight: 300;
      }
      .list-item span:nth-child(2) {
        color: #333333;
        display: inline-block;
        margin-left: 16px;
        font-weight: 400;
      }
    }
    .scopeBox {
      width: 904px;
      .scopeBox-title {
        color: #7f848c;
        font-weight: 300;
        margin-bottom: 8px;
      }
      .list-item {
        padding: 8px 24px;
        display: flex;
        font-size: 14px;
        font-weight: 400;
        .list-item-main {
          width: calc(100% / 3);
        }
        .list-item-main:nth-child(2) {
          text-align: center;
        }
        .list-item-main:nth-child(3) {
          text-align: right !important;
        }
        .list-item-main:nth-child(1),
        span:nth-child(1) {
          color: #7f848c;
        }
        .list-item-main:nth-child(1) span:nth-child(2) {
          color: #333333;
          margin-left: 8px;
          display: inline-block;
          width: 180px;
        }
        .list-item-main:nth-child(3) span:nth-child(2) {
          cursor: pointer;
          margin-left: 8px;
          color: #3562db;
        }
      }
    }
  }
}
</style>
