<template>
  <div class="inbound_and_outbound_statistics">
    <div class="header">
      <span>出入库统计</span>
      <el-date-picker
        v-model="dateList"
        size="large"
        type="daterange"
        range-separator="至"
        value-format="yyyy-MM-dd"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="handleTimeChange"
      >
      </el-date-picker>
    </div>
    <IineBarChart :chartData="lineBarData" />
    <div class="chart_box">
      <CircularDiagram :chartData="circularData" />
      <CircularDiagram :chartData="outCircularData" title="配件出库统计" />
    </div>
  </div>
</template>
<script>
import CircularDiagram from './CircularDiagram.vue'
import IineBarChart from './IineBarChart.vue'
import api from '@/api/index.js'
export default {
  name: 'InboundAndOutboundStatistics',
  components: { CircularDiagram, IineBarChart },
  data() {
    return {
      dateList: [],
      circularData: {},
      outCircularData: {},
      lineBarData: {}
    }
  },
  mounted() {
    this.handleGetData()
  },
  methods: {
    handleTimeChange(e) {
      const [beginDate, endDate] = e || []
      this.handleGetData(beginDate, endDate)
      this.$emit('change', e)
    },
    handleGetData(beginDate, endDate) {
      const { username, userId } = this.$store.state.user.userInfo
      const params = {
        username,
        userId,
        beginDate,
        endDate
      }
      api.getInventoryStatisticsDetail(params).then((res) => {
        const { inWarehouseCount, inWarehouseList, outWarehouseCount, outWarehouseList } = res.data
        this.circularData = {
          data: inWarehouseList.map((item) => ({
            name: item.materialName,
            value: item.totalCount,
            price: item.totalValue
          })),
          value: inWarehouseCount || 0,
          legend: inWarehouseList.map((item) => item.materialName)
        }
        this.outCircularData = {
          data: outWarehouseList.map((item) => ({
            name: item.materialName,
            value: item.totalCount,
            price: item.totalValue
          })),
          value: outWarehouseCount || 0,
          legend: outWarehouseList.map((item) => item.materialName)
        }
      })
      api.getInventoryStatisticsChart(params).then((res) => {
        const { price, count } = res.data
        const lineBarData = {}
        lineBarData.xData = count.map((item) => item.materialTypeName)
        lineBarData.series0 = count.map((item) => item.inCount)
        lineBarData.series1 = count.map((item) => item.outCount)
        lineBarData.series2 = price.map((item) => item.inValue)
        lineBarData.series3 = price.map((item) => item.outValue)
        this.lineBarData = JSON.parse(JSON.stringify(lineBarData))
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.inbound_and_outbound_statistics {
  background: #fff;
  border-radius: 4px 4px 4px 4px;
  padding: 24px;
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    span {
      color: #333333ff;
      font-size: 16px;
      font-weight: bold;
    }
  }
  .chart_box {
    display: flex;
    width: 100%;
    justify-content: space-between;
  }
}
</style>
