<template>
  <PageContainer>
    <div slot="content">
      <div class="title">周期规则详情</div>
      <div class="pageContainer">
        <el-form ref="form" class="form" :model="form" :rules="rules" label-width="140px">
          <el-form-item label="周期规则名称" prop="name">
            <el-input v-model="form.name" disabled placeholder="请输入周期规则名称"> </el-input>
          </el-form-item>
          <el-form-item label="周期类型" prop="type">
            <el-select v-model="form.type" disabled placeholder="请选择周期类型" style="width: 100%">
              <el-option v-for="item in cycleList" :key="item.id" :label="item.nodeName" :value="item.node"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="周期设置" prop="setType">
            <el-input v-if="form.type == 6" v-model="realTimeMin" disabled placeholder="请输入" oninput="value=value.replace(/^0|[^0-9]/g,'')">
              <template slot="append">分钟</template>
            </el-input>
            <el-radio-group v-else v-model="form.setType" disabled>
              <el-radio :label="0">循环周期</el-radio>
              <el-radio :label="1">固定周期</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-table v-if="form.type != 6" :data="form.cycleLevelList" border stripe style="width: 100%">
            <el-table-column prop="name" label="周期名称">
              <template slot-scope="scope">
                <el-input v-model="form.cycleLevelList[scope.$index].name" disabled placeholder="请输入周期名称"></el-input>
              </template>
            </el-table-column>
            <template v-if="form.setType === 0">
              <el-table-column prop="startTimeList" label="开始日期">
                <template slot-scope="scope">
                  <el-cascader v-model="form.cycleLevelList[scope.$index].startTimeList" clearable disabled :options="mockData(true)"></el-cascader>
                </template>
              </el-table-column>
              <el-table-column prop="endTimeList" label="结束日期">
                <template slot-scope="scope">
                  <el-cascader v-model="form.cycleLevelList[scope.$index].endTimeList" disabled :options="mockData()"></el-cascader>
                </template>
              </el-table-column>
            </template>
            <el-table-column v-else prop="timeList" label="起始日期">
              <template slot-scope="scope">
                <el-date-picker
                  v-model="form.cycleLevelList[scope.$index].timeList"
                  type="daterange"
                  value-format="yyyy-MM-dd"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  disabled
                >
                </el-date-picker>
              </template>
            </el-table-column>
          </el-table>
        </el-form>
      </div>
      <div class="footer">
        <el-button type="primary" plain @click="goBack">返回</el-button>
      </div>
    </div>
  </PageContainer>
</template>
<script>
import { mockData } from './json.js'
export default {
  name: 'addCycleRules',
  data() {
    return {
      cycleList: [],
      form: {
        id: '',
        cycleLevel: 0,
        cycleLevelList: [
          {
            name: '',
            startTimeList: [],
            endTimeList: []
          }
        ],
        name: '',
        type: '',
        setType: 0
      },
      realTimeMin: '',
      rules: {
        name: { required: true, message: '请输入周期名称', trigger: 'blur' },
        type: { required: true, message: '请选择周期类型', trigger: 'blur' },
        setType: { required: true, message: '请设置周期执行方式', trigger: 'change' }
      }
    }
  },
  mounted() {
    this.getCycleTypeList()
    this.getDetail()
  },
  methods: {
    mockData,
    getCycleTypeList() {
      this.$api.getCycleType().then((res) => {
        if (res.code === '200') {
          this.cycleList = res.data
        }
      })
    },
    getDetail() {
      this.$api.targetConfigDetail({ cycleLevel: this.type, id: this.$route.query.id }).then((res) => {
        if (res.code === '200') {
          this.form = res.data
          if (this.form.setType === 1) {
            this.form.cycleLevelList.forEach((el) => {
              this.$set(el, 'timeList', [el.startTime, el.endTime])
            })
          }
          if (this.form.type === 6) {
            this.realTimeMin = res.data.realTimeMin
          }
        }
      })
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>
<style lang="scss" scoped>
.container-content > div {
  background-color: #fff;
  height: 100%;
  position: relative;
  .title {
    font-size: 20px;
    font-weight: 700;
    color: #303133;
    padding: 12px 20px 0;
    margin-bottom: 12px;
  }
  .pageContainer {
    width: 100%;
    height: calc(100% - 120px);
    overflow-y: auto;
    .el-form {
      width: 60%;
    }
    padding: 16px 24px;
  }
  .footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    padding: 10px 20px;
    background-color: #fff;
    text-align: right;
    border-top: 1px solid #ebeef5;
  }
}
</style>
