<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    title="液氧充装记录管理"
    width="50%"
    :visible.sync="visible"
    custom-class="model-dialog"
    :before-close="closeDialog"
  >
    <div class="content" style="padding: 10px;">
      <el-form ref="formInline" :model="formInline" :inline="true" class="form-inline" label-width="100px" :rules="rules">
        <el-form-item label="设备名称" prop="dutyDate">
          <p>{{ itemData.surveyName }}</p>
        </el-form-item>
        <br />
        <el-form-item label="充装时间" prop="createDate">
          <el-date-picker v-model="formInline.createDate" value-format="yyyy-MM-dd HH:mm:ss" type="datetime" placeholder="选择日期时间" style="width: 300px;"> </el-date-picker>
        </el-form-item>
        <el-form-item label="充装结束时间" prop="endDate">
          <el-date-picker v-model="formInline.endDate" value-format="yyyy-MM-dd HH:mm:ss" type="datetime" placeholder="选择日期时间" style="width: 300px;"> </el-date-picker>
          <!-- <p>{{(itemData.fillingDate || '') + ' ~ ' + (itemData.endDate || '')}}</p> -->
        </el-form-item>
        <br />
        <el-form-item label="本次充装" prop="dutyDate">
          <p>{{ itemData.fillingValue }}t</p>
        </el-form-item>
        <br />
        <el-form-item label="本次充装">
          <el-input v-model="formInline.ifrFillingValue" style="width: 300px;" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="充装前数值">
          <el-input v-model="formInline.ifrBeforeValue" style="width: 300px;" placeholder="请输入内容" />
        </el-form-item>
        <br />
        <el-form-item label="当前充装数值">
          <el-input v-model="formInline.ifrCurrentValue" style="width: 300px;" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="充装公司" prop="matter">
          <el-select v-model="formInline.ifrCompany" filterable collapse-tags style="width: 300px;">
            <el-option label="普莱克斯" value="普莱克斯" />
            <el-option label="北氧联合" value="北氧联合" />
          </el-select>
        </el-form-item>
        <br />
        <el-form-item label="充装人" prop="matter" >
          <el-select v-model="formInline.ifrFillingPersonId" filterable collapse-tags style="width: 300px;">
            <el-option v-for="item in fillingStaffList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="接收人" prop="matter">
          <el-select v-model="formInline.ifrRecipientId" filterable collapse-tags style="width: 300px;">
            <el-option v-for="item in staffDataList" :key="item.id" :label="item.staffName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <br />
        <el-form-item label="备注">
          <el-input v-model="formInline.ifrRemark" type="textarea" :rows="2" style="width: 300px;" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="confirm('ruleForm')">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  name: 'fillingRecordEditDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    itemData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      requestHttp: __PATH.VUE_IEMC_API,
      loginError: {
        errorStatus: true,
        errorMessage: ''
      },
      loading: false,
      rules: {
        // staffName: [
        //   { required: true, message: "请输入用户名称", trigger: "change" }
        // ],
        // phoneNumber: [
        //   { required: true, message: "请输入姓名", trigger: "change" }
        // ],
      },
      formInline: {
        createDate: '',
        endDate: '',
        ifrFillingValue: '', // 本次充装
        ifrBeforeValue: '', // 充装前数值
        ifrCurrentValue: '', // 当前充装数值
        ifrCompany: '', // 公司
        ifrFillingPersonId: '', // 充装人id
        ifrFillingPerson: '', // 充装人
        ifrRecipientId: '', // 接收人id
        ifrRecipient: '', // 接收人
        ifrRemark: ''
      },
      staffDataList: [],
      fillingStaffList: [],
      matterList: [{ name: '事假', id: 1 }] // 事项列表
    }
  },
  watch: {
    date(val) {
      this.formInline.dutyDate = val
    },
    itemData: {
      handler(val) {
        console.log(val)
        this.formInline.createDate = val.fillingDate
        this.formInline.endDate = val.endDate
        this.formInline.ifrFillingValue = val.fillingValue
        this.formInline.ifrBeforeValue = val.beforeValue
        this.formInline.ifrCurrentValue = val.currentValue
        this.formInline.ifrCompany = val.company
        this.formInline.ifrFillingPersonId = val.fillingPersonId ? Number(val.fillingPersonId) : ''
        this.formInline.ifrFillingPerson = val.fillingPerson
        this.formInline.ifrRecipientId = val.recipientId ? Number(val.recipientId) : ''
        this.formInline.ifrRecipient = val.recipient
        this.formInline.ifrRemark = val.remark
      },
      deep: true
    }
  },
  mounted() {
    console.log(this.itemData)
    // this.$forceUpdate()
    this.getStaffList()
    this.getFillingPersonnelAll()
  },
  methods: {
    // 获取充装人员列表
    getFillingPersonnelAll() {
      this.$api.GetFillingPersonnelAll({}, this.requestHttp).then((res) => {
        if (res.code == 200) {
          this.fillingStaffList = res.data
        } else if (res.message) {
          this.$message.error(res.message)
        }
      })
    },
    // 获取值班人员
    getStaffList() {
      let data = {
        phoneNumber: '',
        staffName: ''
      }
      this.$api.getStaffList(data, this.requestHttp).then((res) => {
        if (res.code == 200) {
          this.staffDataList = res.data
        } else if (res.message) {
          this.$message.error(res.message)
        }
      })
    },
    // 重置
    reset() {
      this.formInline.createDate = ''
      this.formInline.endDate = ''
      this.formInline.ifrFillingValue = ''
      this.formInline.ifrBeforeValue = ''
      this.formInline.ifrCurrentValue = ''
      this.formInline.ifrCompany = ''
      this.formInline.ifrFillingPersonId = ''
      this.formInline.ifrFillingPerson = ''
      this.formInline.ifrRecipientId = ''
      this.formInline.ifrRecipient = ''
      this.formInline.ifrRemark = ''
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', !this.visible)
      this.reset()
      this.$refs['formInline'].resetFields()
    },
    confirm() {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          this.loading = true
          this.formInline.ifrRecipient = this.staffDataList.filter((item) => item.id == this.formInline.ifrRecipientId)[0]?.staffName
          this.formInline.ifrFillingPerson = this.fillingStaffList.filter((item) => item.id == this.formInline.ifrFillingPersonId)[0]?.name
          let data = {
            ifrId: this.itemData.id,
            ...this.formInline
          }
          this.$api.UpdateFilling(data, this.requestHttp).then((res) => {
            this.loading = false
            if (res.code == 200) {
              this.$message.success(res.message)
              this.reset()
              this.$emit('update:visible', !this.visible)
            } else {
              this.$message.error(res.message)
            }
          })
        } else {
          this.$tools.focusFunc()
          return false
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  padding: 20px !important;
  background-color: #fff !important;

  p {
    margin: 0;
  }
}

.model-dialog {
  padding: 0 !important;
}

::v-deep .el-form-item__error {
  height: 20px;
  width: 300px;
  color: #f56c6c;
  font-size: 12px;
  line-height: 20px;
  padding-top: 4px;
  position: absolute;
  left: 78px;
}

::v-deep .el-radio-button__orig-radio:checked + .el-radio-button__inner {
  background-color: #3562db !important;
}

::v-deep .el-dialog__body {
  padding: 0 !important;
}

::v-deep .el-textarea {
  .el-textarea__inner {
    padding-bottom: 20px;
  }

  .el-input__count {
    line-height: 16px;
  }
}
</style>
