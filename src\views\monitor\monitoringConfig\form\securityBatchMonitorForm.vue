<template>
  <PageContainer :footer="true">
    <div slot="content" v-loading="formLoading" class="form-content">
      <div class="form-title">监测实体配置</div>
      <el-form ref="formInline" :model="formInline" :rules="rules">
        <ContentCard title="基本信息">
          <div slot="content" class="footer-role">
            <el-row :gutter="24" style="margin: 0;">
              <el-col :md="7">
                <el-form-item label="监测实体名称" prop="sensorName" label-width="110px">
                  <el-input v-model="formInline.sensorName" disabled placeholder="根据传感器名称自动生成"></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="7">
                <el-form-item label="监测实体编号" prop="sensorNo" label-width="110px">
                  <el-input v-model="formInline.sensorNo" type="text" maxLength="16" show-word-limit
                    placeholder="请输入数字和字母">
                    <!-- v-filterSpecialChar -->
                    <!-- oninput="value=value.replace(/[^\dA-Z\a-z]/g,'')" -->
                    <template slot="append">{{ sensorNumber }}</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :md="7">
                <el-form-item label="实体类型" prop="entityTypeId" label-width="110px">
                  <el-select v-model="formInline.entityTypeId" filterable clearable @change="entityChang">
                    <el-option v-for="item in entityTypeList" :key="item.id" :label="item.name" :value="item.id + ''">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </ContentCard>
        <ContentCard title="监测参数配置">
          <el-button slot="title-right" class="form-btn-btn" type="primary" @click="addrow('monitor')"><i
              class="el-icon-plus"></i>
            添加</el-button>
          <div slot="content">
            <!-- :span-method="objectSpanMethod" -->
            <TablePage ref="tablePage" v-loading="tableLoading" :tableColumn="tableColumn" :showPage="false"
              :data="tableData" border> </TablePage>
          </div>
        </ContentCard>
        <ContentCard title="控制参数配置" v-if="monitorData.projectCode == 'IEMC-AccessControlEquipment'">
          <el-button slot="title-right" class="form-btn-btn" type="primary" @click="addrow('control')"><i
              class="el-icon-plus"></i>
            添加</el-button>
          <div slot="content">
            <!-- :span-method="objectSpanMethod" -->
            <TablePage ref="tablePage" v-loading="tableLoading" :tableColumn="tableControlColumn" :showPage="false"
              :data="tableControlData" border> </TablePage>
          </div>
        </ContentCard>
      </el-form>
      <!-- 选择传感器弹窗 -->
      <template v-if="harvesterDialogShow">
        <harvesterDialog :dialogShow.sync="harvesterDialogShow" :dialogData="selectHarvesterDialogData"
          harvesterCheckType="checkbox" @submitDialog="submitHarvesterDialog" @closeDialog="closeHarvesterDialog" />
      </template>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go(-1)">关闭</el-button>
      <el-button type="primary" @click="submitForm()">保存</el-button>
    </div>
  </PageContainer>
</template>
<script lang="jsx">
import harvesterDialog from '../components/harvesterDialog'
import { monitorTypeList } from '@/util/dict.js'
export default {
  name: 'securityBatchMonitorForm',
  components: {
    harvesterDialog
  },
  data() {
    var validateSensorNo = (rule, value, callback) => {
      if (value) {
        const reg = /^[A-Za-z0-9]+$/
        if (!reg.test(value)) {
          callback(new Error('请输入数字和字母'))
        } else {
          this.$api
            .surveyNoIsExist({
              surveyNo: value,
              projectCode: this.$route.query.projectCode
            })
            .then((res) => {
              if (res.code != 200) {
                this.sensorNumber = res.data
              } else {
                this.sensorNumber = '000001'
              }
              callback()
            })
        }
      } else {
        callback()
      }
    }
    return {
      sensorNumber: '000001', // 监测项编号生成六位
      // 传感器
      harvesterDialogShow: false,
      selectHarvesterDialogData: {},
      formInline: {
        entityTypeId: '', // 实体类型ID
        sensorName: '', // 监测项实体名称
        sensorNo: '' // 监测项编号
      },
      formLoading: false,
      rules: {
        sensorName: {
          required: false,
          message: '请输入监测实体名称',
          trigger: 'change'
        },
        sensorNo: [
          {
            required: true,
            message: '请输入监测项编号',
            trigger: 'change'
          },
          { min: 6, message: '长度不低于6位', trigger: 'blur' },
          { validator: validateSensorNo, trigger: 'blur' }
        ],
        entityTypeId: {
          required: true,
          message: '请选择实体类型',
          trigger: 'change'
        }
      },
      entityTypeName: '', // 实体类型名称
      entityTypeList: [], // 实体类型列表
      currentStartIndex: 0,
      currentEndIndex: 50,
      tableLoading: false,
      tableData: [],
      tableControlData: [],
      tableColumn: [
        {
          prop: 'dataServerName',
          label: '数据主机'
        },
        {
          prop: 'harvesterType',
          label: '传感器类型'
        },
        {
          prop: 'harvesterName',
          label: '传感器名称'
        },
        {
          prop: 'parameterName',
          label: '监测参数'
        },
        {
          prop: 'unitName',
          label: '单位'
        },
        {
          prop: 'istId',
          label: '图纸参数',
          showOverflowTooltip: false,
          render: (h, row) => {
            return (
              <el-select v-model={row.row.istId} clearable filterable collapse-tags class="monitor-select">
                {this.scadaParameterList.map((item) => {
                  return <el-option key={item.istId} label={item.dataTag} value={item.istId + ''}></el-option>
                })}
              </el-select>
            )
          },
          hasJudge: false
        },
        {
          prop: 'dictAliasId',
          label: '监测项目别名',
          showOverflowTooltip: false,
          render: (h, row) => {
            return (
              <el-select v-model={row.row.dictAliasId} clearable filterable collapse-tags class="monitor-select">
                {this.aliasDictList.map((item) => {
                  return <el-option key={item.id + ''} label={item.paramAlias + '(' + item.paramName + ')'} value={item.id}></el-option>
                })}
              </el-select>
            )
          }
        },
        {
          prop: 'operation',
          label: '操作',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span class="operationBtn-span" onClick={() => this.handleParameterEvent('edit', row.row, 'monitor')}>
                  修改
                </span>
                <span class="operationBtn-span" onClick={() => this.handleParameterEvent('del', row.row, 'monitor')}>
                  删除
                </span>
              </div>
            )
          }
        }
      ],
      tableControlColumn: [
        {
          prop: 'dataServerName',
          label: '数据主机'
        },
        {
          prop: 'harvesterType',
          label: '传感器类型'
        },
        {
          prop: 'harvesterName',
          label: '传感器名称'
        },
        {
          prop: 'parameterName',
          label: '监测参数'
        },
        {
          prop: 'unitName',
          label: '单位'
        },
        {
          prop: 'istId',
          label: '图纸参数',
          showOverflowTooltip: false,
          render: (h, row) => {
            return (
              <el-select v-model={row.row.istId} clearable filterable collapse-tags class="monitor-select">
                {this.scadaParameterList.map((item) => {
                  return <el-option key={item.istId} label={item.dataTag} value={item.istId + ''}></el-option>
                })}
              </el-select>
            )
          },
          hasJudge: false
        },
        {
          prop: 'dictAliasId',
          label: '监测项目别名',
          showOverflowTooltip: false,
          render: (h, row) => {
            return (
              <el-select v-model={row.row.dictAliasId} clearable filterable collapse-tags class="monitor-select">
                {this.aliasDictList.map((item) => {
                  return <el-option key={item.id + ''} label={item.paramAlias + '(' + item.paramName + ')'} value={item.id}></el-option>
                })}
              </el-select>
            )
          }
        },
        {
          prop: 'operation',
          label: '操作',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span class="operationBtn-span" onClick={() => this.handleParameterEvent('edit', row.row, 'control')}>
                  修改
                </span>
                <span class="operationBtn-span" onClick={() => this.handleParameterEvent('del', row.row, 'control')}>
                  删除
                </span>
              </div>
            )
          }
        }
      ],
      scadaParameterList: [],
      aliasDictList: [],
      monitorData: {}
    }
  },
  computed: {
    filteredData() {
      return this.tableData.filter((item, index) => {
        if (index < this.currentStartIndex) {
          return false
        } else if (index > this.currentEndIndex) {
          return false
        } else {
          return true
        }
      })
    }
  },
  mounted() {
    this.initEvent()
  },
  methods: {
    // 接口数据初始化
    initEvent() {
      this.monitorData = monitorTypeList.find((e) => e.projectCode == this.$route.query.projectCode)
      this.tableColumn.find(item => item.prop === 'istId').hasJudge = this.monitorData.hasScada === true
      this.$route.query.isiId ? this.getImageSelectById(this.$route.query.isiId, 'scadaParameterList') : ''
      // 重置form表单
      // const data = this.$options.data.call(this)
      // delete data.rules
      // delete data.tableColumn
      // this.$nextTick(() => {
      // for (const key in data) {
      //   this.$data[key] = data[key]
      // }
      this.$refs.formInline.resetFields()
      this.getDictionaryList()
      // })
    },
    getDictionaryList() {
      // 数据主机
      this.dataServer = JSON.parse(sessionStorage.getItem('dataServer'))
      // 获取实体类型数据
      this.$api
        .getDictionaryList({
          dictType: 12
        })
        .then((res) => {
          // 获取实体类型数据
          if (res.code == 200) {
            this.entityTypeList = res.data
          }
        })
      // 获取监测项目别名列表
      this.$api.getAliasDictList().then(res => {
        this.aliasDictList = res.data
      })
    },
    // 实体类型改变
    entityChang() {
      this.entityTypeName = this.entityTypeList.find((item) => item.id == this.formInline.entityTypeId)?.name ?? ''
    },
    // 监测实体保存提交
    submitForm() {
      this.$refs.formInline.validate((valid) => {
        if (valid) {
          if (this.tableData.length == 0) {
            this.$message.error('请添加监测参数配置')
            return
          }
          this.formLoading = true
          const userInfo = this.$store.state.user.userInfo.user
          let reqInsertSurvey = {
            userId: userInfo.staffId,
            userName: userInfo.staffName,
            entityMenuCode: this.$route.query.entityMenuCode,
            projectCode: this.$route.query.projectCode, // 项目编号
            sensorCode: this.$route.query.sensorCode,
            sensorName: this.formInline.sensorName, // 监测项实体名称
            sensorNo: this.formInline.sensorNo + this.sensorNumber, // 监测项编号
            entityTypeName: this.entityTypeName, // 实体类型名称
            entityTypeId: this.formInline.entityTypeId, // 实体类型ID
            parameterList: this.tableData.map((e) => {
              return {
                harvesterName: e.harvesterName,
                harvesterId: e.harvesterId,
                harvesterRealId: e.harvesterRealId,
                dataServerId: e.dataServerId,
                dataServerName: e.dataServerName,
                harvesterTypeId: e.harvesterTypeId,
                harvesterType: e.harvesterType,
                parameterId: e.parameterId,
                parameterName: e.parameterName,
                ispParamType: 11,
                dictAliasId: e.dictAliasId,
                unitName: e.unitName,
                unitId: e.unitId,
                istId: e.istId, // SCADA图形参数IDS
                state: e.state,
                ip: e.ip,
                port: e.port,
                paramSource: e.paramSource,
                projectCode: this.$route.query.projectCode // 项目编号
              }
            }),
            controlList: this.tableControlData.map((e) => {
              return {
                harvesterName: e.harvesterName,
                harvesterId: e.harvesterId,
                harvesterRealId: e.harvesterRealId,
                dataServerId: e.dataServerId,
                dataServerName: e.dataServerName,
                harvesterTypeId: e.harvesterTypeId,
                harvesterType: e.harvesterType,
                parameterId: e.parameterId,
                parameterName: e.parameterName,
                ispParamType: 10,
                dictAliasId: e.dictAliasId,
                unitName: e.unitName,
                unitId: e.unitId,
                istId: e.istId, // SCADA图形参数IDS
                state: e.state,
                ip: e.ip,
                port: e.port,
                paramSource: e.paramSource,
                projectCode: this.$route.query.projectCode // 项目编号
              }
            })
          }
          this.$api.batchInsertSurveyAndParameter(reqInsertSurvey, { 'operation-type': 1 }).then((res) => {
            this.formLoading = false
            if (res.code == 200) {
              this.$message.success(res.message)
              this.$refs.formInline.resetFields()
              this.$router.go(-1)
            } else {
              this.$message.error(res.message)
            }
          })
        } else {
          return false
        }
      })
    },
    // 监测实体列合并
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      const dataProvider = this.tableData
      const cellValue = row[column.property]
      const cellIndexList = [0, 1, 2]
      if (cellValue || cellIndexList.includes(columnIndex)) {
        if (column.label == '传感器名称' || cellIndexList.includes(columnIndex)) {
          // 上一条数据
          const prevRow = dataProvider[rowIndex - 1]
          // 下一条数据
          let nextRow = dataProvider[rowIndex + 1]
          // 当上一条数据等于下一条数据
          if (prevRow && prevRow[column.property] === cellValue) {
            return { rowspan: 0, colspan: 0 }
          } else {
            let rowspan = 1
            while (nextRow && nextRow[column.property] === cellValue) {
              rowspan++
              nextRow = dataProvider[rowspan + rowIndex]
            }
            if (rowspan > 1) {
              return { rowspan, colspan: 1 }
            }
          }
        }
      }
    },
    // 静态加载更多 table
    handelLoadmore() {
      this.currentStartIndex = currentStartIndex
      this.currentEndIndex = currentEndIndex
    },
    getImageSelectById(id, params) {
      this.$api.getImageSelectById({ id: id }).then((res) => {
        if (res.code == 200) {
          this[params] = res.data
        }
      })
    },
    // 添加参数
    addrow(parameterType) {
      this.operationType = parameterType
      this.selectHarvesterDialogData = { eventType: 'add' }
      this.harvesterDialogShow = true
    },
    // 监测项事件
    handleParameterEvent(type, row, parameterType) {
      this.operationType = parameterType
      if (this.operationType === 'monitor') {
        if (type === 'del') {
          const filterData = this.tableData.filter((e) => !(e.harvesterRealId === row.harvesterRealId && e.parameterId === row.parameterId))
          this.tableData = filterData
        } else {
          // radio 选中的参数id
          const parameterIdsList = Array.from(
            this.tableData.filter((e) => e.harvesterRealId === row.harvesterRealId),
            (e) => e.parameterId
          )
          this.selectHarvesterDialogData = Object.assign(row, { eventType: 'edit', parameterIdsList })
          this.harvesterDialogShow = true
        }
      } else if (this.operationType === 'control') {
        if (type === 'del') {
          const filterData = this.tableControlData.filter((e) => !(e.harvesterRealId === row.harvesterRealId && e.parameterId === row.parameterId))
          this.tableControlData = filterData
        } else {
          // radio 选中的参数id
          const parameterIdsList = Array.from(
            this.tableControlData.filter((e) => e.harvesterRealId === row.harvesterRealId),
            (e) => e.parameterId
          )
          this.selectHarvesterDialogData = Object.assign(row, { eventType: 'edit', parameterIdsList })
          this.harvesterDialogShow = true
        }
      }
    },
    // 弹窗事件------------start
    submitHarvesterDialog(data) {
      // 将监测参数抽出赋值在传感器数据上
      // tableData中与data中相同的传感器id数据过滤掉
      if (this.operationType === 'monitor') {
        let newSplitData = []
        let newSetTableData = this.tableData
        data.forEach((e) => {
          newSetTableData = newSetTableData.filter((item) => item.harvesterRealId !== e.harvesterRealId && this.selectHarvesterDialogData.harvesterRealId !== item.harvesterRealId)
          e.parameterList.forEach((item) => {
            newSplitData.push({
              ...e,
              ...item,
              parameterList: null,
              isdId: "",
              istId: '',
              dictAliasId: '',
            })
          })
        })
        let concatData = [...newSplitData, ...newSetTableData]
        this.tableData = concatData
      } else if (this.operationType === 'control') {
        let newSplitData = []
        let newSetTableData = this.tableControlData
        data.forEach((e) => {
          newSetTableData = newSetTableData.filter((item) => item.harvesterRealId !== e.harvesterRealId && this.selectHarvesterDialogData.harvesterRealId !== item.harvesterRealId)
          e.parameterList.forEach((item) => {
            newSplitData.push({
              ...e,
              ...item,
              parameterList: null,
              isdId: "",
              istId: '',
              dictAliasId: '',
            })
          })
        })
        let concatData = [...newSplitData, ...newSetTableData]
        this.tableControlData = concatData
      }
      this.harvesterDialogShow = false
    },
    closeHarvesterDialog() {
      this.harvesterDialogShow = false
    }
    // 弹窗事件------------end
  }
}
</script>
<style lang="scss" scoped>
.footer-role ::v-deep .el-input__inner {
  padding-right: 50px;
}

.form-content {
  height: calc(100% - 0px);
  overflow-y: auto;

  .form-title {
    height: 40px;
    line-height: 40px;
    background: #fff;
    border-bottom: 1px solid #dcdfe6;
    font-size: 15px;
    font-family: "PingFang SC-Medium", "PingFang SC";
    font-weight: 500;
    color: #121f3e;
    padding-left: 15px;
  }

  .box-card {
    padding-left: 3%;
    margin-bottom: 3px;
  }

  .form-btn-title {
    font-size: 14px;
    font-family: "PingFang SC-Regular", "PingFang SC";
    font-weight: 400;
    color: #121f3e;
    margin-right: 8px;
  }

  .form-btn-btn {
    padding: 4px 10px;
    margin: 0 8px;
  }

  .assets-info {
    font-size: 14px;
    font-family: "PingFang SC-Regular", "PingFang SC";
    font-weight: 400;
    color: #121f3e;
    margin-right: 15px;

    >span {
      color: #3562db;
    }
  }

  .assets-info-close {
    cursor: pointer;

    &:hover {
      color: #3562db;
      font-weight: 600;
    }
  }

  .parameter-box {
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    margin-bottom: 20px;

    .parameter-title {
      font-size: 14px;
      font-family: "PingFang SC-Regular", "PingFang SC";
      font-weight: 400;
      color: #121f3e;
      background: #faf9fc;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 48px;
      line-height: 48px;
      padding: 0 10px;

      &>span {
        &:first-child {
          font-size: 16px;
        }
      }
    }

    .unit-style {
      font-size: 14px;
      font-family: "PingFang SC-Regular", "PingFang SC";
      font-weight: 400;
      color: #121f3e;
      height: 40px;
      line-height: 40px;
    }
  }

  ::v-deep .el-select,
  .el-input {
    width: fit-content;
  }

  ::v-deep .monitor-select {
    width: auto !important;
  }

  .camera-tag {
    background: #f6f5fa;
    border-radius: 4px;
    font-size: 14px;
    font-family: "PingFang SC-Regular", "PingFang SC";
    font-weight: 400;
    color: #121f3e;
    border: none;
    margin-right: 8px;

    ::v-deep .el-tag__close {
      color: #121f3e;

      &:hover {
        color: #fff;
        background-color: #3562db;
      }
    }
  }
}
</style>
