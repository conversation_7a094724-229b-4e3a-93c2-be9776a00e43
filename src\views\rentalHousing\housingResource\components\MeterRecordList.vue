<script>
export default {
  name: 'MeterRecordList',
  props: {
    id: String
  },
  data: () => ({
    tableLoading: false,
    tableData: [],
    searchForm: {
      date: ['', '']
    },
    previewUrl: ''
  }),
  computed: {
    OperateType() {
      return {
        // 电表
        ELECTRIC: 'ELECTRIC',
        // 水表
        WATER: 'WATER',
        // 燃气表
        GAS: 'GAS'
      }
    }
  },
  mounted() {
    this.onSearch()
  },
  methods: {
    // 表格相关操作总线处理
    onOperate(type, row) {
      let path = ''
      switch (type) {
        case this.OperateType.ELECTRIC:
          path = row.electricityPicUrl ? JSON.parse(row.electricityPicUrl)[0].url : ''
          break
        case this.OperateType.WATER:
          path = row.waterPicUrl ? JSON.parse(row.waterPicUrl)[0].url : ''
          break
        case this.OperateType.GAS:
          path = row.gasPicUrl ? JSON.parse(row.gasPicUrl)[0].url : ''
          break
      }
      if (!path) {
        this.$message.error('暂无照片')
        return
      }
      const url = this.$tools.imgUrlTranslation(path)
      if (this.previewUrl === url) {
        this.showImageViewer()
      } else {
        this.previewUrl = url
      }
    },
    onSearch() {
      const params = {
        pageNum: 1,
        pageSize: 999,
        houseId: this.id,
        startTime: this.searchForm.date[0] ? `${this.searchForm.date[0]} 00:00:00` : '',
        endTime: this.searchForm.date[1] ? `${this.searchForm.date[1]} 23:59:59` : ''
      }
      this.tableLoading = true
      this.$api.rentalHousingApi
        .queryMeterRecordByPage(params)
        .then((res) => {
          if (res.code === '200') {
            this.tableData = res.data.records
          } else {
            throw res.message
          }
        })
        .catch((msg) => this.$message.error(msg || '获取抄表信息失败'))
        .finally(() => (this.tableLoading = false))
    },
    onReset() {
      this.$refs.formRef.resetFields()
      this.onSearch()
    },
    // 显示图片
    showImageViewer() {
      const imgEl = document.querySelector('.meter-record-list__img>img')
      if (imgEl) {
        imgEl.click()
      }
    }
  }
}
</script>
<template>
  <div class="meter-record-list">
    <el-form ref="formRef" :model="searchForm" inline>
      <el-form-item prop="date">
        <el-date-picker v-model="searchForm.date" type="daterange" value-format="yyyy-MM-dd" start-placeholder="抄表时间" end-placeholder="抄表时间" clearable></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" plain @click="onReset">重置</el-button>
        <el-button type="primary" @click="onSearch">搜索</el-button>
      </el-form-item>
    </el-form>
    <div class="meter-record-list__table">
      <el-table v-loading="tableLoading" height="100%" :data="tableData" border stripe table-layout="auto" class="tableAuto" row-key="id">
        <el-table-column prop="electricityNum" label="电表抄表数（kW·h）" />
        <el-table-column prop="electricitySurplusPrice" label="电表剩余余额（元）" />
        <el-table-column prop="electricityPicUrl" label="电表照片" width="80px">
          <template #default="{ row }">
            <el-button type="text" @click="onOperate(OperateType.ELECTRIC, row)">查看</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="waterNum" label="水表抄表数（m³）" width="140px" />
        <el-table-column prop="waterSurplusPrice" label="水表剩余余额（元）" width="150px" />
        <el-table-column prop="waterPicUrl" label="水表照片" width="80px">
          <template #default="{ row }">
            <el-button type="text" @click="onOperate(OperateType.WATER, row)">查看</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="gasNum" label="燃气表抄表数（m³）" width="160px" />
        <el-table-column prop="gasSurplusPrice" label="燃气表剩余余额（元）" width="160px" />
        <el-table-column prop="gasPicUrl" label="燃气表照片" width="90px">
          <template #default="{ row }">
            <el-button type="text" @click="onOperate(OperateType.GAS, row)">查看</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="typeDesc" label="抄表类型" width="90px" />
        <el-table-column prop="updateByName" label="抄表人" width="100px" />
        <el-table-column prop="updateDate" label="抄表时间" width="160px" />
      </el-table>
    </div>
    <el-image v-if="!!previewUrl" class="meter-record-list__img" :src="previewUrl" :preview-src-list="[previewUrl]" @load="showImageViewer"></el-image>
  </div>
</template>
<style lang="scss" scoped>
.meter-record-list {
  height: 100%;
  &__table {
    height: calc(100% - 62px);
  }
  &__img {
    height: 0;
  }
}
</style>
