<template>
  <div>
    <!-- echarts -->
    <div class="echartsWrap">
      <div ref="taskAnalysis" class="charts" style="margin-right: 50px;"></div>
      <div ref="timeAnalysis" class="charts"></div>
    </div>
    <!-- table -->
    <div>
      <el-table v-loading="tableLoading" stripe :data="tableData" border  :cell-style="{ padding: '8px' }" title="双击查看详情" @row-dblclick="watchDetail">
        <el-table-column type="index" label="序号" width="77" align="center">
          <template slot-scope="scope">
            <span>{{ (paginationData.pageNo - 1) * paginationData.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="questionCode" label="编号" show-overflow-tooltip width="200" align="center"></el-table-column>
        <el-table-column prop="flowType" label="状态" show-overflow-tooltip width="100" align="center">
          <template slot-scope="scope">
            <span :style="formaterColor(scope.row.flowCode)">{{ scope.row.flowType }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="questionDetailType" label="隐患分类" show-overflow-tooltip width="180" align="center"></el-table-column>
        <el-table-column prop="questionAddress" label="隐患区域" show-overflow-tooltip width="180" align="center"></el-table-column>
        <el-table-column prop="createByDeptName" label="登记部门" show-overflow-tooltip width="180" align="center"></el-table-column>
        <el-table-column prop="createPersonName" label="登记人" show-overflow-tooltip width="180" align="center"></el-table-column>
        <el-table-column prop="createTime" label="登记时间" show-overflow-tooltip width="180" align="center"></el-table-column>
        <el-table-column prop="riskName" label="隐患等级" show-overflow-tooltip width="180" align="center"></el-table-column>
        <el-table-column prop="rectificationPlanTime" label="要求整改完成时间" show-overflow-tooltip width="180" align="center"></el-table-column>
      </el-table>
      <div class="paging">
        <el-pagination
          style="margin-top: 10px;"
          :total="paginationData.total"
          :current-page="paginationData.pageNo"
          :page-sizes="[15, 30, 50, 100]"
          :page-size="paginationData.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
// 引入echarts组件
import * as echarts from 'echarts'
// let echarts = require('echarts/lib/echarts')
// require('echarts/lib/chart/bar')
// require('echarts/lib/component/tooltip')
// require('echarts/lib/component/graphic')
// require('echarts/lib/component/legend')

export default {
  components: {},
  props: ['filters'],
  data() {
    return {
      tableData: [],
      echartsData: [],
      paginationData: {
        pageNo: 1,
        pageSize: 15,
        total: 0
      },
      tableLoading: false
    }
  },
  created() {
    this.getData()
  },
  methods: {
    setEcharts() {
      let myChart1 = echarts.init(this.$refs.taskAnalysis)
      let myChart2 = echarts.init(this.$refs.timeAnalysis)
      const optionData = []
      this.echartsData.questionStateAnalyse.forEach((i) => {
        const item = {}
        item.name = i.flowName
        item.value = i.count
        optionData.push(item)
      })
      myChart1.setOption({
        title: {
          text: '隐患状态分析',
          left: '30%',
          bottom: '5%',
          textStyle: {
            fontStyle: 'normal',
            fontWeight: 'normal',
            fontSize: 14
          }
        },
        backgroundColor: '#fff',
        color: ['#61A5E8', '#7ECF51', '#EECB5F', '#E3935D', '#E16757', '#E16757'],
        series: [
          {
            type: 'pie',
            radius: '60%',
            center: ['40%', '40%'],
            data: optionData,
            hoverAnimation: false,
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 1,
              labelLine: {
                length: 20,
                length2: 16,
                show: true,
                lineStyle: {
                  color: '#ccc'
                }
              }
            },
            label: {
              show: false
            }
          }
        ],
        legend: {
          icon: 'circle',
          itemGap: 15,
          itemWidth: 10,
          itemHeight: 10,
          right: '6%',
          top: '25%',
          textStyle: {
            color: '#656565'
          },
          orient: 'vertical',
          formatter: (name) => {
            const count = this.echartsData.questionStateAnalyse.find((i) => i.flowName == name)
            return name + '(' + count.count + '件) ' + ' ' + '  ' + count.percentage
          }
        }
      })
      const xData = []
      const yData = []
      this.echartsData.questionTypeAnalyse.forEach((i) => {
        xData.push(i.questName)
        yData.push(i.count)
      })
      myChart2.setOption({
        title: {
          text: '隐患类型分析',
          left: 'center',
          top: '5%',
          textStyle: {
            fontStyle: 'normal',
            fontWeight: 'normal',
            fontSize: 14
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: xData,
            axisTick: {
              alignWithLabel: true
            },
            axisLabel: {
              rotate: -40,
              margin: 20
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            axisLine: {
              show: false
            }
          }
        ],
        series: [
          {
            name: '隐患数',
            type: 'bar',
            barWidth: '60%',
            data: yData,
            color: '#61A5E8',
            barWidth: 30
          }
        ],
        dataZoom: [
          {
            type: 'slider',
            realtime: true,
            startValue: 0,
            endValue: 10,
            height: 4,
            fillerColor: 'rgba(17, 100, 210, 0.42)', // 滚动条颜色
            borderColor: 'rgba(17, 100, 210, 0.12)',
            handleSize: 0, // 两边手柄尺寸
            showDetail: false, // 拖拽时是否展示滚动条两侧的文字
            top: '96%',
            zoomLock: true // 是否只平移不缩放
            // moveOnMouseMove:true, //鼠标移动能触发数据窗口平移
            // zoomOnMouseWheel :true, //鼠标移动能触发数据窗口缩放
          },
          {
            type: 'inside', // 支持内部鼠标滚动平移
            start: 0,
            end: 40,
            zoomOnMouseWheel: false, // 关闭滚轮缩放
            moveOnMouseWheel: true, // 开启滚轮平移
            moveOnMouseMove: true // 鼠标移动能触发数据窗口平移
          }
        ]
      })
    },
    handleSizeChange(val) {
      this.paginationData.pageNo = 1
      this.paginationData.pageSize = val
      this.getData()
    },
    handleCurrentChange(val) {
      this.paginationData.pageNo = val
      this.getData()
    },
    getData(data) {
      const params = {}
      if (data) {
        params.deptCode = ''
        params.createByDeptCode = ''
        params.startTime = ''
        params.endTime = ''
      } else {
        params.deptCode = this.filters.structureType
        params.createByDeptCode = this.filters.structureType
        params.startTime = this.filters.timeIterval[0]
        params.endTime = this.filters.timeIterval[1]
      }
      params.pageSize = this.paginationData.pageSize
      params.pageNo = this.paginationData.pageNo
      this.$api.ipsmGetHiddenManageList(params).then((res) => {
        if (res.code == '200') {
          this.tableData = res.data.list
          this.paginationData.total = res.data.sum
        }
      })
      this.$api.ipsmGetRiskAnalysis(params).then((res) => {
        if (res.code == '200') {
          this.echartsData = res.data
          this.setEcharts()
        }
      })
    },
    watchDetail(row) {
      //   this.$store.commit('planIdChange', row.id)
      //   this.$store.commit('maintainCodeChange', row.maintainCode)
      this.$router.push({
        name: 'hiddenManagementDetails3',
        query: { id: row.id }
      })
    },
    formaterColor(type) {
      let color = {}
      if (type == 0) {
        color = { color: '#333333' }
      } else if (type == 1) {
        color = { color: '#688DFF' }
      } else if (type == 3) {
        color = { color: '#FF0000' }
      } else if (type == 2) {
        color = { color: '#47BC7D' }
      } else if (type == 4) {
        color = { color: '#091ED9' }
      } else if (type == 5) {
        color = { color: '#FF6100' }
      }
      return color
    }
  }
}
</script>
<style scoped>
.echartsWrap {
  display: flex;
  justify-content: flex-start;
  margin: 20px 0;
}

.charts {
  width: 500px;
  height: 300px;
}
</style>
