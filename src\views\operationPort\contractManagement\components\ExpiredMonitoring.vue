<template>
  <el-dialog title="提醒设置" :visible="visible" width="560px" :before-close="handleClosesubmit">
    <el-form ref="form" :model="form" label-width="80px" :rules="rules">
      <el-form-item label="提醒天数" prop="expireDay">
        <el-input v-model="form.expireDay" placeholder="请输入提醒天数" @input="(val) => (form.expireDay = val.replace(/[^\d]/g, ''))">
          <template slot="append">天</template>
        </el-input>
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button @click="handleClosesubmit">取消</el-button>
      <el-button type="primary" @click="handleSubmit"> 确认 </el-button>
    </div>
  </el-dialog>
</template>
<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    selectionList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      form: {
        expireDay: ''
      },
      rules: {
        expireDay: [{ required: true, message: '请输入提醒天数', trigger: ['change', 'blur'] }]
      }
    }
  },
  methods: {
    handleClosesubmit() {
      this.$emit('update:visible', false)
    },
    handleSubmit() {
      this.$refs.form.validate((res) => {
        if (!res) return
        this.$api.fileManagement.setExpireDay({ ...this.form, idList: this.selectionList.map((item) => item.archiveId) }).then((res) => {
          if (res.code === '200') {
            this.$message.success('设置成功')
            this.handleClosesubmit()
          } else {
            this.$message.error('设置失败')
          }
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-form-item__label {
  line-height: 32px;
}
</style>
