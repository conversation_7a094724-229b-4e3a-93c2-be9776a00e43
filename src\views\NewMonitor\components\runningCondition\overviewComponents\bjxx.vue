<!-- 运行监测 -->
<template>
    <ContentCard :title="item.componentTitleShow" :scrollbarHover="true" :cstyle="{ width: '100%', height: '100%' }"
        class="drag_class" :hasMoreOper="['edit']"
        @more-oper-event="(val) => $emit('all-more-Oper', val, item.componentName, item.chartType)">
        <div slot="content" style="width: 100%; height: 100%;">
            <div class="content_top" v-if="hasChart">
                <p v-for="item in dataList">
                    <span v-if="item.level == 3">重要警情<br> <em>{{ item.count }}</em></span>
                    <span v-if="item.level == 2">紧急警情<br> <em>{{ item.count }}</em></span>
                    <span v-if="item.level == 1">一般警情<br> <em>{{ item.count }}</em></span>
                    <span v-if="item.level == 0">通知警情<br> <em>{{ item.count }}</em></span>
                </p>
            </div>
            <div v-if="!hasChart" style="display: flex;justify-content: center;align-items: center;height: 100%;">暂无数据
            </div>
        </div>
    </ContentCard>
</template>
<script>
export default {
    name: 'bjxx',
    props: {
        item: {
            type: Object,
            default: () => { }
        },
        systemCode: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            dataList: [],
            hasChart: false
        }
    },
    mounted() { },
    methods: {
        bjxxData(id) {
            let objectId = id || ""
            this.$api.selectAlarmLevelByObjectId({ objectId: objectId }).then((res) => {
                if (res.code === "200") {
                    this.hasChart = true
                    this.dataList = res.data
                } else {
                    this.hasChart = false
                }
            })
        },

    }
}
</script>

<style lang="scss" scoped>
.content_top {
    width: 100%;
    height: 100%;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;

    p {
        width: calc(50% - 10px);
        height: 46%;
        line-height: 26px;
        text-align: left;
        font-size: 14px;
        color: #666;
        background-color: #FAF9FC;
        padding: 10px;
        margin: 0 5px;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: center;

        em,
        i {
            font-style: normal;
            font-size: 18px;
            font-weight: bold;
        }

        i {
            font-size: 14px;
            color: #96989A;
            font-weight: normal;
        }
    }

    p:nth-child(1) em {
        color: #942625;
    }

    p:nth-child(2) em {
        color: #f53f3f;
    }

    p:nth-child(3) em {
        color: #f77234;
    }

    p:nth-child(4) em {
        color: #ffbe00;
    }

}
</style>