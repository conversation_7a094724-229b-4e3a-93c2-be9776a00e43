<!-- 空间申请 -->
<template>
  <PageContainer>
    <div slot="content" class="staging-content">
      <el-tabs v-model="tabsActive" class="content-head" @tab-click="tabClick">
        <el-tab-pane v-for="item in tabsList" :key="item.value" :label="item.name" :name="item.value"></el-tab-pane>
      </el-tabs>
      <div class="content-main">
        <div class="search-from">
          <el-input v-model="searchFrom.applyDept" placeholder="请输入申请部门" clearable style="width: 200px;"></el-input>
          <el-input v-model="searchFrom.applyName" placeholder="请输入申请人" clearable style="width: 200px;"></el-input>
          <div style="display: inline-block;">
            <el-button type="primary" plain @click="resetForm">重置</el-button>
            <el-button type="primary" @click="searchForm">查询</el-button>
          </div>
        </div>
        <div class="main-content">
          <TablePage
            ref="tablePage"
            :key="tabsActive"
            v-loading="tableLoading"
            :showPage="true"
            :tableColumn="tableColumn"
            :data="tableData"
            row-key="id"
            height="calc(100% - 40px)"
            :pageData="pageData"
            @pagination="paginationChange"
            @row-dblclick="operateHandle('detail', $event)"
          />
        </div>
      </div>
      <spaceAuditDialog v-if="showSpaceAudit" :visible.sync="showSpaceAudit" :item="slelectAuditItem" />
      <cancelAuditDialog v-if="showCancelAudit" :visible.sync="showCancelAudit" :item="slelectAuditItem" />
      <spaceAllocationDialog v-if="showSpaceAllocation" :visible.sync="showSpaceAllocation" @confirm="confirmSpaceAllocation" />
    </div>
  </PageContainer>
</template>

<script lang="jsx">
export default {
  name: 'spaceApplication',
  components: {
    spaceAuditDialog: () => import('./spaceAuditDialog'),
    cancelAuditDialog: () => import('./cancelAuditDialog'),
    spaceAllocationDialog: () => import('./spaceAllocationDialog')
  },
  data() {
    return {
      tabsActive: '1',
      tableLoading: false,
      searchFrom: {
        applyDept: '', // 申请部门
        applyName: '' // 申请人
      },
      tabsList: [
        {name: '待审核', value: '1'},
        {name: '已审核', value: '2'},
        {name: '已取消', value: '4'}
      ],
      tableData: [
        {}
      ],
      pageData: {
        page: 1,
        pageSize: 15,
        total: 0
      },
      showSpaceAudit: false, // 空间审核弹窗
      showCancelAudit: false, // 取消申请弹窗
      showSpaceAllocation: false, // 空间分配
      slelectAuditItem: {} // 选中审核项
    }
  },
  computed: {
    tableColumn() {
      let column = [
        {
          prop: 'serialNumber',
          label: '序号',
          width: 80,
          formatter: (scope) => {
            // 当前分页重置
            // return scope.$index + 1
            // 跨分页序号
            return (this.pageData.page - 1) * this.pageData.pageSize + scope.$index + 1
          }
        },
        {
          prop: 'applyNo',
          label: '申请单编号'
        },
        {
          prop: 'applyDept',
          label: '申请部门'
        },
        {
          prop: 'applyName',
          label: '申请人'
        },
        {
          prop: 'applyTel',
          label: '电话'
        },
        {
          prop: 'applyArea',
          label: '申请使用面积 (㎡)'
        }
      ]
      console.log(this.tabsActive)
      if (this.tabsActive == '1') {
        return [
          ...column,
          {
            prop: 'usage',
            label: '用途'
          },
          {
            prop: 'applyDescription',
            label: '申请说明'
          },
          {
            prop: 'applyTime',
            label: '申请时间'
          },
          {
            label: '操作',
            width: 110,
            render: (h, row) => {
              return (
                <div class="operationBtn">
                  <span class="operationBtn-span" style="color: #121F3E" onClick={() => this.operateHandle('auditing', row.row)}>审核</span>
                  <span class="operationBtn-span" style="color: #fa403c" onClick={() => this.operateHandle('cancel', row.row)}>取消</span>
                </div>
              )
            }
          }
        ]
      } else if (this.tabsActive == '2') {
        return [
          ...column,
          {
            prop: 'applyTime',
            label: '申请时间'
          },
          {
            prop: 'reviewDept',
            label: '审核部门'
          },
          {
            prop: 'reviewPerson',
            label: '审核人员'
          },
          {
            prop: 'reviewTime',
            label: '审核时间'
          },
          {
            prop: 'allowSpace',
            label: '已分配空间'
          },
          {
            label: '操作',
            width: 110,
            render: (h, row) => {
              return (
                <div class="operationBtn">
                  <span class="operationBtn-span" style="color: #3562db" onClick={() => this.operateHandle('allocation', row.row)}>分配空间</span>
                </div>
              )
            }
          }
        ]
      } else if (this.tabsActive == '4') {
        return [
          ...column,
          {
            prop: 'usage',
            label: '用途'
          },
          {
            prop: 'applyDescription',
            label: '申请说明'
          },
          {
            prop: 'applyTime',
            label: '申请时间'
          },
          {
            prop: 'reviewAdvice',
            label: '取消原因'
          }
        ]
      } else {
        return []
      }
    }
  },
  watch: {
    showSpaceAudit(val) {
      if (!val) {
        this.searchForm()
      }
    },
    showCancelAudit(val) {
      if (!val) {
        this.searchForm()
      }
    }
  },
  created() {
    // 随机数
    this.getSpaceApplyList()
  },
  methods: {
    // 分配空间选择完成
    confirmSpaceAllocation(val) {
      let newArr = []
      val.forEach(item => {
        newArr.push({
          spaceId: item.id,
          spaceArea: item.area,
          spaceName: item.localSpaceName
        })
      })
      this.$api.SpaceAllocation({applyId: this.slelectAuditItem.id, spaces: newArr}).then((res) => {
        if (res.code === '200') {
          this.searchForm()
        }
      })
    },
    // 获取申请列表
    getSpaceApplyList() {
      const params = {
        ...this.searchFrom,
        spaceApplyStatus: this.tabsActive,
        pageParams: {
          currentPage: this.pageData.page,
          pageSize: this.pageData.pageSize
        }
      }
      this.tableData = []
      this.$api.GetSpaceApplyList(params).then((res) => {
        if (res.code === '200') {
          this.tableData = res.data.records
          this.pageData.total = res.data.total
        }
      })
    },
    operateHandle(type, row) {
      this.slelectAuditItem = row
      if (type == 'auditing') {
        this.showSpaceAudit = true
      } else if (type == 'cancel') {
        this.showCancelAudit = true
      } else if (type == 'allocation') {
        this.showSpaceAllocation = true
      }
    },
    // tab切换
    tabClick(tab) {
      this.searchForm()
    },
    // 重置查询表单
    resetForm() {
      Object.assign(this.$data.searchFrom, this.$options.data().searchFrom)
      this.searchForm()
    },
    // 查询
    searchForm() {
      this.pageData.page = 1
      this.getSpaceApplyList()
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getSpaceApplyList()
    }
  }
}

</script>

<style lang="scss" scoped>
.page-container {
  margin: 0;
  height: 100%;
  width: 100%;
}

.staging-content {
  height: 100%;
  width: 100%;

  :deep(.content-head) {
    margin: 10px 10px 0;
    background: #fff;
    border-radius: 4px;

    .el-tabs__header .el-tabs__nav-wrap::after {
      display: none;
    }

    .el-tabs__active-bar {
      bottom: 1px;
    }

    .el-tabs__item {
      padding: 0 20px !important;
    }

    .el-tabs__content {
      display: none;
    }
  }

  .content-main {
    margin: 10px 10px 0;
    width: calc(100% - 20px);
    height: calc(100% - 70px);
    display: flex;
    flex-direction: column;
    background: #fff;

    .search-from {
      padding: 0 0 0 16px;

      & > div {
        margin-top: 16px;
        margin-right: 16px;
      }
    }

    .main-content {
      padding: 16px;
      height: calc(100% - 48px);
    }
  }
}
</style>
