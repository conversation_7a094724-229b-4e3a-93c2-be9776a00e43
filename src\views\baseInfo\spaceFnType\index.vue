<!-- 空间功能类型 -->
<template>
  <PageContainer>
    <div slot="content" class="space-content" style="height: 100%">
      <div class="space-content-left">
        <div class="left_title">字典分类</div>
        <el-input v-model="filterText" placeholder="请输入关键字" clearable></el-input>
        <div class="left_content">
          <el-tree
            ref="tree"
            v-loading="treeLoading"
            :check-strictly="true"
            :data="treeData"
            :props="defaultProps"
            :default-expanded-keys="idArr"
            :filter-node-method="filterNode"
            style="margin-top: 10px"
            node-key="id"
            :highlight-current="true"
            :expand-on-click-node="false"
            @node-click="nodeClick"
          >
            <template #default="{ node, data }">
              <el-tooltip effect="dark" :disabled="showTooltip" :content="node.label" placement="top">
                <div class="nodeLabel" @mouseover="onMouseOver(data.id)">
                  <span :ref="`nodeLabel${data.id}`">{{ node.label }}</span>
                </div>
              </el-tooltip>
            </template>
          </el-tree>
        </div>
      </div>
      <div class="space-content-right">
        <div style="height: 100%; display: flex; flex-direction: column">
          <div class="search-from">
            <el-input
              v-model.trim="searchFrom.dictName"
              placeholder="请输入关键字"
              style="width: 300px"
              suffix-icon="el-icon-search"
              clearable
              maxlength="25"
              onkeyup="if(value.length>25)value=value.slice(0,25)"
            ></el-input>
            <el-button type="primary" plain style="margin-right: 10px" @click="resetForm">重置</el-button>
            <el-button type="primary" @click="searchForm">查询</el-button>
            <el-button type="primary" icon="el-icon-plus" @click="handleRoleEvent('add')">新增</el-button>
          </div>
          <div class="contentTable-main table-content">
            <el-table v-loading="tableLoading" border style="width: 100%; height: 100%" :data="tableData" :height="tableHeight" row-key="id" stripe>
              <el-table-column type="index" label="序号" width="70">
                <template slot-scope="scope">
                  <span>{{ (pagination.current - 1) * pagination.size + scope.$index + 1 }}</span>
                </template>
              </el-table-column>
              <el-table-column label="类型名称" prop="dictName" show-overflow-tooltip> </el-table-column>
              <el-table-column label="类型编码" prop="dictValue" show-overflow-tooltip> </el-table-column>
              <el-table-column label="类型颜色" prop="colourByHex">
                <template slot-scope="scope">
                  <p class="color_con" :style="{ background: scope.row.colourByHex, height: '12px' }"></p>
                  <span> {{ scope.row.colourByHex }}</span>
                </template>
              </el-table-column>
              <el-table-column label="启用/禁用" prop="statusFlag" show-overflow-tooltip>
                <template slot-scope="scope">
                  <el-switch :value="scope.row.statusFlag == 0" active-color="#3562db" @change="switchHandle(scope.row)"></el-switch>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="190">
                <template slot-scope="scope">
                  <el-button type="text" @click="moveUp(scope.$index, scope.row)">上移</el-button>
                  <el-button type="text" @click="moveDown(scope.$index, scope.row)">下移</el-button>
                  <el-button type="text" @click="handleRoleEvent('edit', scope.row)">编辑</el-button>
                  <el-button type="text" style="color: #fa403c" @click="deleteFn(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="contentTable-footer">
            <el-pagination
              :current-page="pagination.current"
              :page-sizes="pagination.pageSizeOptions"
              :page-size="pagination.size"
              :layout="pagination.layoutOptions"
              :total="pagination.total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            >
            </el-pagination>
          </div>
        </div>
      </div>
      <addDictTypeItemDialog v-if="isDictTypeItemDialog" :visible.sync="isDictTypeItemDialog" :selectParent="selectedDictType" :selectItem="activeItem" />
    </div>
  </PageContainer>
</template>
<script>
import tableListMixin from '@/mixins/tableListMixin.js'
export default {
  name: 'spaceFnType',
  components: {
    addDictTypeItemDialog: () => import('../components/addDictTypeItemDialog.vue')
  },
  mixins: [tableListMixin],
  data() {
    return {
      isDictTypeItemDialog: false,
      showTooltip: false,
      filterText: '', // 树形结构筛选
      treeLoading: false,
      treeData: [],
      idArr: [],
      defaultProps: {
        label: 'typeName',
        children: 'list'
      },
      searchFrom: {
        dictName: ''
      },
      tableLoading: false,
      tableData: [],
      selectedDictType: {},
      activeItem: {}
    }
  },
  computed: {},
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    },
    isDictTypeItemDialog(val) {
      if (!val) this.getDicListByPageFn()
    }
  },
  created() {
    this.getDicTypelistFn()
  },
  methods: {
    handleRoleEvent(type, row = {}) {
      this.activeItem = row
      if (type == 'add' || type === 'edit') {
        this.isDictTypeItemDialog = true
      }
    },
    deleteFn(row) {
      this.$confirm('确认删除选中的字典数据吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$api.DeleteDic({ id: row.id }).then((res) => {
            if (res.code == 200) {
              this.getDicListByPageFn()
              this.$message({
                message: res.msg,
                type: 'success'
              })
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    // 上移
    moveUp(index, row) {
      var that = this
      if (index > 0) {
        const upDate = that.tableData[index - 1]
        that.tableData.splice(index - 1, 1)
        that.tableData.splice(index, 0, upDate)
        that.swapPositionFn(row, upDate)
      } else {
        this.$message.warning('已经是第一条，不可上移')
      }
    },
    // 下移
    moveDown(index, row) {
      var that = this
      if (index + 1 === that.tableData.length) {
        this.$message.warning('已经是最后一条，不可下移')
      } else {
        const downDate = that.tableData[index + 1]
        that.tableData.splice(index + 1, 1)
        that.tableData.splice(index, 0, downDate)
        that.swapPositionFn(row, downDate)
      }
    },
    swapPositionFn(currentDicData, moveToDicData) {
      this.$api
        .DictSwapPosition({
          currentDictId: currentDicData.id,
          currentPosition: currentDicData.sort,
          moveToDictId: moveToDicData.id,
          moveToPosition: moveToDicData.sort
        })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success('操作成功')
          }
        })
    },
    //  获取字典数据列表
    getDicListByPageFn() {
      let data = {
        dictTypeId: this.selectedDictType.id,
        ...this.searchFrom,
        size: this.pagination.size,
        current: this.pagination.current
      }
      this.$api.GetDicListByPage(data).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.records
          this.pagination.total = res.data.total
        }
      })
    },
    // 启用/禁用
    switchHandle(row) {
      let data = {
        id: row.id,
        statusFlag: row.statusFlag ? 0 : 1
      }
      this.$api.ChangeStatusFlag(data).then((res) => {
        if (res.code == 200) {
          this.$message.success(res.msg)
          this.getDicListByPageFn()
        }
      })
    },
    //  获取字典类型列表
    getDicTypelistFn() {
      this.treeLoading = true
      this.$api
        .GetDicTypeList()
        .then((res) => {
          if (res.code == 200) {
            res.data = res.data.filter((v) => v.typeName == '空间功能类型')
            this.selectedDictType = res.data[0]
            this.treeData = this.$tools.transData(res.data, 'id', 'pid', 'list')
            this.$nextTick(function () {
              this.$refs.tree.setCurrentKey(this.selectedDictType.id)
            })
            this.getDicListByPageFn()
          }
        })
        .finally(() => {
          this.treeLoading = false
        })
    },
    nodeClick(val) {
      this.selectedDictType = val
      this.getDicListByPageFn()
    },
    filterNode(value, data) {
      if (!value) return true
      return data.typeName.indexOf(value) !== -1
    },
    // 重置
    resetForm() {
      this.pagination.size = 15
      this.pagination.current = 1
      Object.assign(this.$data.searchFrom, this.$options.data().searchFrom)
      this.getDicListByPageFn()
    },
    // 查询
    searchForm() {
      this.getDicListByPageFn()
    },
    handleSizeChange(val) {
      this.pagination.size = val
      this.getDicListByPageFn()
    },
    handleCurrentChange(val) {
      this.pagination.current = val
      this.getDicListByPageFn()
    },
    // 树划入划出效果
    onMouseOver(id) {
      const parentWidth = this.$refs[`nodeLabel${id}`].parentNode.offsetWidth // 获取元素父级可视宽度
      const contentWidth = this.$refs[`nodeLabel${id}`].offsetWidth // 获取元素可视宽度
      this.showTooltip = contentWidth <= parentWidth
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  color: #3562db;
  background: #e6effc !important;
}
::v-deep .el-tree-node__content {
  height: 36px !important;
  position: relative;
}
.space-content {
  height: 100%;
  display: flex;
  .space-content-left {
    width: 280px;
    height: 100%;
    padding: 0px 10px 10px 10px;
    background: #fff;
    border-radius: 4px 0px 0px 4px;
    border-right: 1px solid #e4e7ed;
    .left_content {
      width: 100%;
      height: calc(100% - 95px);
      overflow: auto;
    }
    .left_title {
      font-weight: bold;
      font-size: 16px;
      color: #333333;
      line-height: 22px;
      padding: 16px 14px;
    }
  }
  .space-content-right {
    height: 100%;
    min-width: 0;
    padding: 10px 20px 20px 20px;
    background: #fff;
    border-radius: 0px 4px 4px 0px;
    flex: 1;
    .search-from {
      margin-bottom: 10px;
      & > div,
      .el-button {
        margin-left: 0px;
        margin-right: 10px;
        margin-top: 10px;
      }
      & > .el-button:last-child {
        margin: 0px;
      }
    }
    .contentTable-main {
      flex: 1;
      overflow: hidden;
      .color_con {
        margin: 0px;
        display: inline-block;
        width: 38px;
        height: 38px;
        border-radius: 5px;
        cursor: pointer;
      }
    }
    .contentTable-footer {
      padding: 10px 0 0;
    }
  }
  .content {
    width: 100%;
    max-height: 500px !important;
    overflow: auto;
    background-color: #fff !important;
  }
}
::v-deep .el-tree-node {
  white-space: normal;
  // outline: 0;
}
.nodeLabel {
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  padding-right: 20px;
}
</style>
