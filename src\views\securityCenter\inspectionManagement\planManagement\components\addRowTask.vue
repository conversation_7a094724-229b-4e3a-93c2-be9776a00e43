<template>
  <el-dialog class="sino-dialog-device" :title="title" :visible.sync="dialogVisibleSk" @close="closeDialog">
    <div class="top-search" style="margin-bottom: 15px;">
      <el-input
        v-model="dialogFilters.taskBookName"
        style="width: 217px; margin-right: 20px;"
        placeholder="任务书名称"
        maxlength="25"
        onkeyup="if(value.length>25)value=value.slice(0,25)"
      ></el-input>

      <el-select v-model="dialogFilters.taskBookType" filterable placeholder="任务书类型">
        <el-option v-for="(item, index) in taskBookTypeArr" :key="item.dictCode" :label="item.dictName" :value="index"> </el-option>
      </el-select>
      <el-button style="margin-left: 10px;" type="primary" @click="_resetCondition">重置</el-button>
      <el-button type="primary" plain @click="_searchByCondition">查询</el-button>
    </div>
    <div class="hasSelected" style="display: flex; align-items: center; margin-bottom: 20px;">
      <div style="color: #5b5b5b; font-weight: 600;">已选择 ：</div>
      <span v-if="!multipleSelection.length">暂无</span>
      <div v-for="(item, index) in multipleSelection" v-else :key="index" style="color: #5188fc;">
        <span>{{ item.projectName }}</span>
        <span v-show="multipleSelection.length > 1 && index !== multipleSelection.length - 1">、</span>
      </div>
    </div>
    <div class="table-list">
      <el-table
        ref="materialTable"
        v-loading="dialogtableLoading"
        :data="tableWz"
        :border="true"
        height="300px"
        stripe
        :row-key="
          (row) => {
            return row.id
          }
        "
        :cell-style="{ padding: '8px' }"
        style="overflow: auto;"
        :header-cell-style="$tools.setHeaderCell(3)"
        @selection-change="handleSelectionChangeDialog"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column type="index" label="序号" width="65"></el-table-column>
        <el-table-column prop="projectName" show-overflow-tooltip label="任务书名称"></el-table-column>
        <el-table-column prop="equipmentTypeName" show-overflow-tooltip label="任务书类型"></el-table-column>
        <el-table-column prop="projectExplain" label="任务书说明" show-overflow-tooltip></el-table-column>
        <el-table-column prop="createTime" label="更新时间" show-overflow-tooltip></el-table-column>
      </el-table>
    </div>
    <div class="table-page my_page">
      <el-pagination
        :current-page="paginationData.currentPage"
        :page-sizes="[15, 20, 30, 50]"
        :page-size="paginationData.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="paginationData.total"
        style="margin: 10px 0;"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" class="sino-button-sure" @click="sureWz">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  name: '',
  props: {
    title: {
      type: String,
      default: '选择任务书'
    },
    dialogVisibleSk: {
      type: Boolean,
      default: false
    },
    flag: {
      type: String,
      default: 'maintainFlag'
    }
  },
  data() {
    return {
      dialogFiltersSelectArr: [],
      workTypeCodeList: [],
      paginationData: {
        currentPage: 1,
        pageSize: 15,
        total: 0
      },
      props: {
        label: 'gridName',
        value: 'id',
        children: 'children'
      },
      arr: [],
      materialTypeID: '',
      areaList: [],
      multipleSelection: [],
      tableWz: [],
      dialogtableLoading: false,
      dialogFilters: {
        taskBookName: '',
        taskBookType: ''
      },
      taskBookTypeArr: [],
      buttonLoading: false,
      tableSort: '',
      tableOrderBy: '',
      LOGINDATA: ''
    }
  },
  created() {
    // 获取登录信息
    this.LOGINDATA = JSON.parse(sessionStorage.getItem('LOGINDATA'))
    // 获取任务书类型
    this._findDictionaryTableList() // 获取任务书类型
  },
  mounted() {
    // 获取任务书列表
    this._findTaskBookList()
  },
  methods: {
    // 选择
    handleSelectionChangeDialog(val) {
      this.multipleSelection = val
    },
    // 点击确定
    sureWz() {
      if (this.multipleSelection.length > 1) {
        this.$message.error('每次最多只能选择一个任务书！')
        return
      }
      this.$emit('multipleSelection', this.multipleSelection)
      //  this.$refs.materialTable.clearSelection();
      this.closeDialog()
    },
    // 获取任务书类型
    _findDictionaryTableList() {
      let data = {
        dictCode: '',
        dictName: '',
        pageNo: 1,
        pageSize: 9999,
        dictType: 'task_book_type'
      }
      this.$api.findDictionaryTableList(data).then((res) => {
        const { code, data, message } = res
        if (code == 200) {
          this.taskBookTypeArr = data.list
        } else {
          this.$message.error(message)
        }
      })
    },
    // 查询表格
    _findTaskBookList() {
      this.dialogtableLoading = true
      let obj = this.LOGINDATA
      const { dialogFilters, paginationData } = this
      let data = {
        projectName: dialogFilters.taskBookName,
        equipmentType: dialogFilters.taskBookType,
        pageNo: paginationData.currentPage,
        pageSize: paginationData.pageSize,
        staffId: '8984ffe1bb704fffba1fa8eb8d4e6cfb'
      }
      // data.staffId = obj.staffId,
      this.$api.findTaskBookList(data).then((res) => {
        this.dialogtableLoading = false
        if (res.code == 200) {
          this.tableWz = res.data.list
          this.paginationData.total = parseInt(res.data.count)
          if (res.data.list.length == 0) {
            this.emptyText = '未查到任何记录，请放大查询条件试试！'
          }
        }
        this.dialogtableLoading = false
      })
    },

    // 条件查询
    _searchByCondition() {
      this.paginationData.currentPage = 1
      this._findTaskBookList()
    },
    // 重置查询条件
    _resetCondition() {
      this.paginationData.currentPage = 1
      this.paginationData.pageSize = 15
      this.dialogFilters.taskBookName = ''
      this.dialogFilters.taskBookType = ''
      this._findTaskBookList()
    },
    closeDialog() {
      this.dialogFilters = {
        compound: '',
        brandName: '',
        areaCode: []
      }
      this.$emit('closeDialog')
    },

    handleSizeChange(val) {
      this.paginationData.currentPage = 1
      this.paginationData.pageSize = val
      this._findTaskBookList()
    },
    handleCurrentChange(val) {
      this.paginationData.currentPage = val
      this._findTaskBookList()
    }
  }
}
</script>
<style lang="scss" scoped>
.hasSelected {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

:deep(.el-dialog) {
  .el-dialog__body {
    padding: 15px 20px;
  }

  height: 580px;
}
</style>