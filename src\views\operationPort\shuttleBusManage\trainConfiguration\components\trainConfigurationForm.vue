<template>
  <el-dialog
    v-dialogDrag
    class="component dictionary-value-edit"
    :title="OperateType == 'add' ? '新增车次' : '编辑路线'"
    width="750px"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :visible.sync="dialogShow"
    custom-class="model-dialog"
    :before-close="closeTrainDialog"
  >
    <div class="content" style="padding: 10px; display: flex">
      <el-form ref="trainConfigFrom" :model="formModel" :rules="rules" label-width="95px">
        <el-form-item label="车次序号" prop="trainNo" style="width: 460px">
          <el-input v-model="formModel.trainNo" placeholder="请输入车次序号" clearable></el-input>
        </el-form-item>
        <el-form-item label="路线名称" prop="lineId">
          <el-select v-model="formModel.lineId" placeholder="请选择班车路线">
            <el-option v-for="item in busRoadMapList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="班车类型" prop="busType">
          <el-select v-model="formModel.busType" placeholder="请选择班车类型">
            <el-option v-for="item in regularBusList" :key="item.code" :label="item.name" :value="item.code"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="乘客类型" prop="passengerType">
          <el-select v-model="formModel.passengerType" placeholder="请选择乘客">
            <el-option v-for="item in userTypeList" :key="item.code" :label="item.name" :value="item.code"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="发车时间" prop="departTime">
          <el-time-picker v-model="formModel.departTime" format="HH:mm" value-format="HH:mm" placeholder="请选择发车时间"> </el-time-picker>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <el-button type="primary" plain @click="closeTrainDialog">取消</el-button>
      <el-button type="primary" @click="onSubmit">确认</el-button>
    </template>
  </el-dialog>
</template>
<script>
import { uploadAcceptDict } from '@/util/dict.js'
export default {
  name: 'DictionaryValueEdit',
  props: {
    OperateType: {
      type: String,
      default: ''
    },
    dialogShow: {
      type: Boolean,
      default: false
    },
    trainConfigId: {
      type: String,
      default: ''
    },
    regularBusList: {
      type: Array,
      default: () => []
    },
    userTypeList: {
      type: Array,
      default: () => []
    }
  },
  events: ['update:visible', 'success'],
  data: function () {
    return {
      uploadAcceptDict,
      formModel: {
        trainNo: '',
        lineId: '',
        busType: '',
        passengerType: '',
        departTime: ''
      },
      rules: {
        trainNo: [{ required: true, message: '请输入车次序号' }],
        lineId: [{ required: true, message: '请选择路线名称' }],
        busType: [{ required: true, message: '请选择班车类型' }],
        passengerType: [{ required: true, message: '请选择乘客类型' }],
        departTime: [{ required: true, message: '请选择发车时间' }]
      },
      busRoadMapList: [], // 班车路线
      loadingStatus: false
    }
  },
  created() {
    this.getLineList()
  },
  mounted() {
    this.$watch('dialogShow', () => {
      if (this.trainConfigId) {
        if (this.OperateType == 'edit') {
          this.getTrainDeatil(this.trainConfigId)
        }
      }
    })
  },
  methods: {
    // dialog点击右上角关闭按钮，重置表单
    closeTrainDialog() {
      this.$emit('closeTrainDialog')
      this.resetForm()
    },
    // 获取路线
    getLineList() {
      this.$api.fileManagement.roadmapReplyList().then((res) => {
        if (res.code == 200) {
          this.busRoadMapList = res.data
        }
      })
    },
    // 获取表单详情
    getTrainDeatil(val) {
      this.loadingStatus = true
      this.$api.fileManagement.trainConfigurationDetail({ id: val }).then((res) => {
        this.loadingStatus = true
        if (res.code == 200) {
          this.formModel = { ...res.data }
        }
      })
    },
    // 表单提交
    onSubmit() {
      this.$refs.trainConfigFrom
        .validate()
        .catch(() => Promise.reject())
        .then(() => {
          this.loadingStatus = true
          const params = {
            trainNo: this.formModel.trainNo, // 车次编号
            lineId: this.formModel.lineId, // 路线名称
            busType: this.formModel.busType, // 班车类型
            passengerType: this.formModel.passengerType, // 乘客类型
            departTime: this.formModel.departTime // 发车时间
          }
          if (this.trainConfigId) {
            params.id = this.trainConfigId
            return this.$api.fileManagement.trainConfigurationUpdate(params)
          } else {
            return this.$api.fileManagement.trainConfigurationSave(params)
          }
        })
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('操作成功')
            this.$emit('success')
            this.closeTrainDialog()
          } else {
            throw res.msg || res.message
          }
        })
        .catch((msg) => {
          msg && this.$message.error(msg)
        })
        .finally(() => {
          this.loadingStatus = false
        })
    },
    resetForm() {
      this.formModel = {
        trainNo: '',
        lineId: '',
        busType: '',
        passengerType: '',
        departTime: ''
      }
    }
  }
}
</script>
<style lang="scss">
.component.dictionary-value-edit {
  .el-form {
    background-color: #fff;
    padding: 10px 16px 0;
    .el-form-item {
      .el-cascader,
      .el-select {
        width: 100%;
      }
    }
  }
  .content {
    width: 100%;
    max-height: 500px !important;
    overflow: auto;
    background-color: #fff !important;
  }
  .dictionary-value-edit {
    &__color {
      vertical-align: middle;
      .el-input-group__prepend {
        padding: 0;
        line-height: 0;
        border-radius: 4px 0 0 4px;
      }
      .el-color-picker {
        height: 30px;
      }
      .el-color-picker__trigger {
        border: none;
        height: 30px;
        width: 30px;
        padding: 2px;
      }
      .el-input__inner {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      }
    }
    &__upload {
      &.hide {
        .el-upload {
          opacity: 0;
          transition-duration: 0.5s;
          pointer-events: none;
        }
      }
    }
  }
}
</style>
