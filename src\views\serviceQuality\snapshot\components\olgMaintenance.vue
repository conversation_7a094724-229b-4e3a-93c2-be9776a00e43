<template>
  <div class="content">
    <div class="content-left">
      <div class="top-phone">
        <span>来电号码</span>
        <el-input v-model="formInline.needPhone" :readonly="dealType === 'deal'" placeholder="请输入来电号码"></el-input>
      </div>
      <div class="service-event">
        <span style="color: #f56c6c">*</span> 服务事项 <span :title="selectService" class="select-servive">{{ selectService }}</span>
      </div>
      <el-input v-model="filterText" placeholder="请输入关键词" style="margin: 10px 0"></el-input>
      <el-tree ref="tree" class="tree" :filter-node-method="filterNode" node-key="id" :data="itemTreeData" :props="defaultProps" @node-click="handleNodeClick"></el-tree>
    </div>
    <div class="content-line"></div>
    <div class="content-right">
      <div class="kd-time">
        <div>
          <span>开单时间</span>
          <span>{{ kd_time }}</span>
        </div>
        <el-button class="sino-button-sure img-add-icon" type="primary" @click="placeOrder()"
          ><div class="img-add-icon"></div>
          再建一单</el-button
        >
      </div>
      <div class="order-message">
        <div class="service-event">工单信息</div>
        <el-form ref="formInline" class="form-data" :model="formInline" :rules="rules" :inline="true" label-width="auto" size="medium">
          <div class="formRow">
            <el-form-item label="服务时间：" prop="appointmentType">
              <el-radio v-model="formInline.appointmentType" label="0">立刻</el-radio>
              <el-radio v-model="formInline.appointmentType" label="1">预约</el-radio>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item v-if="formInline.appointmentType === '1'" label="服务时间：" prop="appointmentDate">
              <el-date-picker
                v-model="formInline.appointmentDate"
                :picker-options="pickerOptions"
                value-format="yyyy-MM-dd HH:mm:ss"
                popper-class="timePicker"
                type="datetime"
                placeholder="选择日期时间"
              >
              </el-date-picker>
            </el-form-item>
          </div>
          <div class="formRow" style="width: 100%">
            <el-form-item style="width: 70%; flex: auto" label="紧急程度：" prop="urgencyDegree">
              <el-radio v-model="formInline.urgencyDegree" label="2" @change="urgencyChange('2')">一般</el-radio>
              <el-radio v-model="formInline.urgencyDegree" label="0">紧急事故</el-radio>
              <el-radio v-model="formInline.urgencyDegree" label="1">紧急催促</el-radio>
            </el-form-item>
            <el-form-item v-if="formInline.urgencyDegree !== '2'" style="width: 30%; flex: auto" label="" prop="noticeBtn">
              <el-checkbox v-model="formInline.noticeBtn" @change="noticeBtnChange">通知</el-checkbox>
            </el-form-item>
          </div>
          <table v-if="selectNoticePeopleRow.length" class="maint-table" style="table-layout: fixed">
            <tbody>
              <tr>
                <td style="width: 42%">联系人</td>
                <td style="width: 42%">人员电话</td>
                <td style="width: 16%">操作</td>
              </tr>
              <tr v-for="(item, index) in selectNoticePeopleRow" :key="index">
                <td>
                  <div :title="item.name" class="one-line">{{ item.name }}</div>
                </td>
                <td>
                  <div :title="item.phone" class="one-line">{{ item.phone }}</div>
                </td>
                <td>
                  <div class="one-line scope-del" @click="noticePeopleDel(item.id)">删除</div>
                </td>
              </tr>
            </tbody>
          </table>
          <div class="formRow">
            <el-form-item label="所属科室：" prop="sourceDept">
              <el-select v-model="formInline.sourceDept" placeholder="请选择" filterable>
                <el-option v-for="item in sourcesDeptOptions" :key="item.value" :label="item.label" :value="item.value + '_' + item.label"> </el-option>
              </el-select>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="要求完工时间：" prop="requireAccomplishDate">
              <div class="block">
                <el-date-picker v-model="formInline.requireAccomplishDate" type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="选择日期时间"> </el-date-picker>
              </div>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="服务地点：" prop="localtionName">
              <el-input v-model="formInline.localtionName" readonly="readonly" placeholder="请选择服务地点" @focus="getLocaltion()"></el-input>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="工号：" prop="callerJobNum">
              <el-input v-model.trim="formInline.callerJobNum" placeholder="请输入工号"></el-input>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="电话：" prop="sourcesPhone">
              <el-input v-model.trim="formInline.sourcesPhone" placeholder="请输入电话"></el-input>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="联系人：" prop="callerName">
              <el-input v-model.trim="formInline.callerName" placeholder="请输入联系人"></el-input>
            </el-form-item>
          </div>
          <div class="formRow" style="width: 85%">
            <el-form-item label="申报描述：" prop="questionDescription">
              <el-input
                v-model.trim="formInline.questionDescription"
                maxlength="500"
                placeholder="请输入描述，限制五百字"
                type="textarea"
                onkeyup="if(value.length>500)value=value.slice(0,500)"
                show-word-limit
              ></el-input>
            </el-form-item>
          </div>
          <div class="formRow" style="width: 100%">
            <el-form-item style="width: 70%; flex: auto" label="服务部门：" prop="designateDeptCode">
              <el-select v-model="formInline.designateDeptCode" placeholder="请选择服务部门" @change="deptChange">
                <el-option v-for="item in teamsOptions" :key="item.id" :label="item.team_name" :value="item.id + '_' + item.team_name"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item style="width: 30%; flex: auto" label="" prop="designCheck">
              <el-checkbox v-model="formInline.designCheck" @change="designPerson">指派工人</el-checkbox>
            </el-form-item>
          </div>
          <table v-if="selectTeamPeopleRow.length" class="maint-table" style="table-layout: fixed">
            <tbody>
              <tr>
                <td style="width: 42%">服务人员</td>
                <td style="width: 42%">人员电话</td>
                <td style="width: 16%">操作</td>
              </tr>
              <tr v-for="(item, index) in selectTeamPeopleRow" :key="index">
                <td>
                  <div :title="item.member_name" class="one-line">{{ item.member_name }}</div>
                </td>
                <td>
                  <div :title="item.phone" class="one-line">{{ item.phone }}</div>
                </td>
                <td>
                  <div class="one-line scope-del" @click="peopleDel(item.id)">删除</div>
                </td>
              </tr>
            </tbody>
          </table>
          <div class="formRow">
            <el-form-item label="申报属性：" prop="typeSources">
              <el-radio v-if="workOrderDetail.olgTaskManagement?.typeSources !== '3'" v-model="formInline.typeSources" label="1">医务报修</el-radio>
              <el-radio v-if="workOrderDetail.olgTaskManagement?.typeSources !== '3'" v-model="formInline.typeSources" label="2">外委巡查</el-radio>
              <el-radio v-if="workOrderDetail.olgTaskManagement?.typeSources !== '3'" v-model="formInline.typeSources" label="4">领导巡查</el-radio>
              <el-radio v-else v-model="formInline.typeSources" label="3">巡检来源</el-radio>
            </el-form-item>
          </div>
          <div class="formRow repair-work">
            <el-form-item label="" prop="repairWork">
              <el-checkbox v-model="formInline.repairWork" true-label="2" false-label="1">返修工单</el-checkbox>
            </el-form-item>
          </div>
        </el-form>
      </div>
    </div>
    <!-- 选择班组人员弹框 -->
    <template v-if="changeNoticePeopleShow">
      <noticePeople ref="changeNoticePeople" :changeNoticePeopleShow="changeNoticePeopleShow" @peopleSure="noticePeopleSure" @closeDialog="closeNoticePeopleDialog"></noticePeople>
    </template>
    <!-- 选择班组人员弹框 -->
    <template v-if="changeTeamsPeopleShow">
      <teamsPeople
        ref="changeTeamsPeople"
        :selectTeamsData="selectTeamsData"
        :changeTeamsPeopleShow="changeTeamsPeopleShow"
        @peopleSure="peopleSure"
        @closeDialog="closePeopleDialog"
      ></teamsPeople>
    </template>
    <!-- 选择服务地点弹框 -->
    <template v-if="changeLocationShow">
      <Location ref="changeLocation" :changeLocationShow="changeLocationShow" @localSure="locationSure" @closeDialog="closeLocationDialog"></Location>
    </template>
    <!-- 创建工单 -->
    <template v-if="workOrderDealShow">
      <workOrderDeal
        ref="workOrderDeal"
        :workOrderDealShow="workOrderDealShow"
        :workTypeCode="olgTaskManagement.workTypeCode"
        :workTypeName="olgTaskManagement.workTypeName"
        :dealType="dealType"
        :workTypeId="workTypeId"
        @workOrderSure="workOrderSure"
        @closeDialog="workOrderDialog"
      ></workOrderDeal>
    </template>
  </div>
</template>
<script>
import noticePeople from './noticePeople.vue'
import teamsPeople from './teamsPeople.vue'
import Location from './Location.vue'
import store from '@/store/index'
export default {
  name: 'olgMaintenance',
  components: {
    noticePeople,
    teamsPeople,
    Location,
    workOrderDeal: () => import('./workOrderDeal.vue')
  },
  props: {
    workOrderDetail: {
      type: Object,
      default() {
        return {}
      }
    },
    dealType: {
      type: String,
      default: ''
    },
    routerFrom: {
      type: String,
      default: 'local'
    }
  },
  data() {
    return {
      itemTreeData: [],
      defaultProps: {
        label: 'name',
        children: 'children'
      },
      filterText: '',
      selectService: '',
      rules: {
        callerName: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
        designateDeptCode: [{ required: true, message: '请选择服务部门', trigger: 'blur' }]
        // sourceDept: [{ required: true, message: '请选择所属科室', trigger: 'blur' }]
      },
      formInline: {
        needPhone: '',
        appointmentType: '0',
        appointmentDate: '',
        urgencyDegree: '2',
        sourceDept: '',
        requireAccomplishDate: '',
        localtion: '',
        localtionName: '',
        noticeBtn: false,
        designCheck: false,
        typeSources: '1',
        callerJobNum: '',
        callerName: '',
        sourcesPhone: '',
        designateDeptCode: '',
        questionDescription: '',
        repairWork: '',
        designatePersonCode: '',
        designatePersonName: '',
        designatePersonPhone: '',
        personHidden: '',
        typeThree: '',
        typeNameThree: '',
        typeTwo: '',
        typeNameTwo: '',
        typeOne: '',
        typeNameOne: '',
        isDispatching: '0'
      },
      kd_time: '',
      selectRowId: '', // tree 选中id
      teamsOptions: [], // 班组
      sourcesDeptOptions: [],
      changeNoticePeopleShow: false, // 选择 应急联系人弹框
      selectNoticePeopleRow: [],
      changeTeamsPeopleShow: false,
      selectTeamPeopleRow: [],
      selectTeamsData: {},
      changeLocationShow: false, // 服务地点弹窗
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now()
        }
      },
      workOrderDealShow: false,
      olgTaskManagement: {},
      workTypeId: '',
      selectServiceCode: ''
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    if (this.workOrderDetail) {
      this.formInit()
    }
  },
  mounted() {
    this.getItemTreeData()
    this.getSourcesDeptOptions() // 获取科室数据
    this.getTeamsByWorkTypeCode()
  },
  methods: {
    // 表单提交
    saveForm() {
      const formData = JSON.parse(JSON.stringify(this.formInline))
      if (!this.selectService) {
        return this.$message.warning('请选择三级服务事项！')
      }
      const personWorkPerson = []
      if (formData.designatePersonCode) {
        const designatePersonCode = formData.designatePersonCode.split(',')
        const designatePersonName = formData.designatePersonName.split(',')
        const designatePersonPhone = formData.designatePersonPhone.split(',')
        designatePersonCode.map((e, index) => {
          personWorkPerson.push([designatePersonName[index], designatePersonPhone[index], '', e].toString())
        })
      }
      const userInfo = store.state.user.userInfo.user
      Object.assign(formData, {
        userId: userInfo.staffId,
        userName: userInfo.staffName,
        staffNum: userInfo.staffNum,
        officeName: '',
        needPhone1: formData.needPhone,
        typeThree1: formData.typeThree,
        typeNameThree1: formData.typeNameThree,
        typeOne1: formData.typeOne,
        typeNameOne1: formData.typeNameOne,
        typeTwo1: formData.typeTwo,
        typeNameTwo1: formData.typeNameTwo,
        appointmentType1: formData.appointmentType,
        appointmentDate1: formData.appointmentDate,
        urgencyDegree1: formData.urgencyDegree,
        localtionName1: formData.localtionName,
        designateDeptCode1: formData.designateDeptCode,
        typeSources1: formData.typeSources,
        repairWork1: formData.repairWork,
        num: 1,
        requireAccomplishDate:formData.requireAccomplishDate
      })
      if (this.dealType === 'deal') {
        console.log(formData)
        Object.assign(formData, {
          operSource: 'newCreate',
          operType: this.workOrderDetail.operType,
          workNum: this.workOrderDetail.olgTaskManagement.workNum,
          workTypeCode: this.workOrderDetail.olgTaskManagement.workTypeCode,
          type: this.workOrderDetail.olgTaskManagement.type,
          id: this.workOrderDetail.olgTaskManagement.id,
          sourceDept: formData.sourceDept ? formData.sourceDept.split('_')[1] : '',
          sourcesDept1: this.formInline.sourceDept,
          personWorkPerson: personWorkPerson.length ? personWorkPerson.toString() : ''
        })
        this.placeAndCancelOrder(formData)
      } else if (this.dealType === 'add') {
        Object.assign(formData, {
          id: '',
          workNum: this.workOrderDetail.olgTaskManagement.workNum,
          workSources: this.workOrderDetail.olgTaskManagement.workSources,
          operType: this.workOrderDetail.operType,
          taskType: this.workOrderDetail.olgTaskManagement.taskType,
          workTypeCode: this.workOrderDetail.olgTaskManagement.workTypeCode,
          workTypeName: this.workOrderDetail.olgTaskManagement.workTypeName,
          type: this.workOrderDetail.olgTaskManagement.type,
          tempDate: this.workOrderDetail.tempDate,
          isSubmit: '0',
          sourceDept: formData.sourceDept ? formData.sourceDept.split('_')[1] : '',
          sourcesDept1: this.formInline.sourceDept ?? this.workOrderDetail.olgTaskManagement.sourcesDept + '_' + this.workOrderDetail.olgTaskManagement.sourcesDeptName,
          sourcesDeptName: this.workOrderDetail.olgTaskManagement.sourcesDeptName === 'undefined' ? '' : this.workOrderDetail.olgTaskManagement.sourcesDeptName,
          noNum: 'B01',
          personWorkPerson: personWorkPerson.length ? personWorkPerson.toString() : ''
        })
        this.placeAndCancelSave(formData)
      }
    },
    placeAndCancelOrder(formData) {
      this.$refs.formInline.validate((valid) => {
        if (valid) {
          this.$api.placeAndCancelOrder(formData).then((res) => {
            console.log(res)
            const data = res
            if (data.success) {
              this.$message({
                message: '保存成功！',
                type: 'success'
              })
              this.$emit('save', true)
            } else {
              this.$message({
                message: data.msg,
                type: 'warning'
              })
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    placeAndCancelSave(formData) {
      this.$refs.formInline.validate((valid) => {
        if (valid) {
          // console.log(formData)
          this.$api.placeAndCancelSave(formData).then((res) => {
            console.log(res)
            const data = res
            let msg = data.msg
            msg = msg.replace('<br/>', '')
            if (data.success) {
              this.$message({
                message: msg,
                type: 'success'
              })
              this.$emit('save', data.body.workNum)
            } else {
              this.$message({
                message: msg,
                type: 'warning'
              })
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    formInit() {
      if (this.dealType === 'deal') {
        this.selectService = this.workOrderDetail.olgTaskManagement.olgTaskDetailList[0].itemServiceName || ''
        this.selectServiceCode = this.workOrderDetail.olgTaskManagement.olgTaskDetailList[0].itemTypeCode || ''
        this.formInline.needPhone = this.workOrderDetail.olgTaskManagement.needPhone
        this.formInline.appointmentType = this.workOrderDetail.olgTaskManagement.appointmentType || '0'
        this.formInline.appointmentDate = this.workOrderDetail.olgTaskManagement.appointmentDate
        this.formInline.urgencyDegree = this.workOrderDetail.olgTaskManagement.urgencyDegree || '2'
        this.formInline.localtion = this.workOrderDetail.olgTaskManagement.olgTaskDetailList[0].localtion
        this.formInline.localtionName = this.workOrderDetail.olgTaskManagement.olgTaskDetailList[0].localtionName
        this.formInline.callerJobNum = this.workOrderDetail.olgTaskManagement.callerJobNum
        this.formInline.callerName = this.workOrderDetail.olgTaskManagement.callerName
        this.formInline.sourcesPhone = this.workOrderDetail.olgTaskManagement.sourcesPhone
        this.formInline.questionDescription = this.workOrderDetail.olgTaskManagement.questionDescription
        this.formInline.typeSources = this.workOrderDetail.olgTaskManagement.typeSources || '1'
        this.formInline.repairWork = this.workOrderDetail.olgTaskManagement.repairWork

        this.formInline.typeThree = this.workOrderDetail.olgTaskManagement.olgTaskDetailList[0].itemServiceCode
        this.formInline.typeNameThree = this.workOrderDetail.olgTaskManagement.olgTaskDetailList[0].itemServiceName
        this.formInline.typeOne = this.workOrderDetail.olgTaskManagement.olgTaskDetailList[0].itemTypeCode
        this.formInline.typeNameOne = this.workOrderDetail.olgTaskManagement.olgTaskDetailList[0].itemTypeName
        this.formInline.typeTwo = this.workOrderDetail.olgTaskManagement.olgTaskDetailList[0].itemDetailCode
        this.formInline.typeNameTwo = this.workOrderDetail.olgTaskManagement.olgTaskDetailList[0].itemDetailName

        this.kd_time = this.workOrderDetail.kd_time
      } else if (this.dealType === 'add') {
        this.kd_time = this.$tools.dateToStr()
      }
    },
    // 服务事项赋值
    handleNodeClick(data, node) {
      if (data.level !== '3') {
        return this.$message.warning('请选择三级服务事项！')
      }
      if (data.level === '3') {
        console.log('@@@', data)
        this.selectServiceCode = ''
        this.selectService = data.name
        this.selectRowId = node.parent.parent.data.id
        this.formInline.typeThree = data.id
        this.formInline.typeNameThree = data.name
        this.formInline.typeTwo = node.parent.data.id
        this.formInline.typeNameTwo = node.parent.data.name
        this.formInline.typeOne = node.parent.parent.data.id
        this.formInline.typeNameOne = node.parent.parent.data.name
      } else if (data.level === '2') {
        this.selectRowId = node.parent.data.id
        this.formInline.typeTwo = data.id
        this.formInline.typeNameTwo = data.name
      } else {
        this.selectRowId = node.data.id
        this.formInline.typeOne = data.id
        this.formInline.typeNameOne = data.name
      }
      this.formInline.designateDeptCode = ''
      this.selectTeamsData = {}
      this.formInline.isDispatching = '0'
      this.selectTeamPeopleRow = []
      this.formInline.designCheck = false
      this.getTeamsByWorkTypeCode()
    },
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    // 再建一单
    placeOrder() {
      this.olgTaskManagement = {
        workTypeCode: '1',
        workTypeName: '综合维修'
      }
      if (this.dealType === 'deal') {
        this.workTypeId = this.workOrderDetail.olgTaskManagement.id
      }
      this.workOrderDealShow = true
      // id workTypeName workTypeCode 'newCreate'
    },
    // 通知联系人
    noticeBtnChange() {
      this.changeNoticePeopleShow = true
    },
    urgencyChange() {
      this.selectNoticePeopleRow = []
      this.formInline.num = 0
      this.formInline.noticeBtn = ''
    },
    deptChange() {
      this.selectTeamPeopleRow = []
      this.setFormPeopleData([])
    },
    // 指派工人
    designPerson() {
      if (!this.formInline.designateDeptCode) {
        this.$nextTick(() => {
          this.formInline.designCheck = !this.formInline.designCheck
        })
        return this.$message({
          message: '请选择服务部门！',
          type: 'warning'
        })
      }
      this.selectTeamsData = {
        type: '2',
        id: this.formInline.designateDeptCode.split('_')[0],
        deptName: this.formInline.designateDeptCode.split('_')[1]
      }
      this.formInline.isDispatching = '1'
      this.changeTeamsPeopleShow = true
    },
    // 获取服务事项
    getItemTreeData() {
      const params = {
        workTypeCode: '1'
      }
      this.$api.getItemTreeData(params).then((res) => {
        this.itemTreeData = this.$tools.listToTree(res, 'id', 'parent')
      })
    },
    getSourcesDeptOptions() {
      this.$api.getAllOffice().then((res) => {
        if (res.success) {
          this.sourcesDeptOptions = res.body.result
        }
      })
    },
    // 获取班组
    getTeamsByWorkTypeCode() {
      const params = {
        localtionId: '',
        workTypeCode: '1',
        itemTypeCode: this.selectRowId,
        matterId: this.selectServiceCode
      }
      this.$api.getDataByTypeTeam(params).then((res) => {
        // getTeamsByWorkTypeCode(params).then((res) => {
        if (res.code === '200') {
          this.teamsOptions = res.data.list
        }
      })
    },
    // 获取服务地点 start
    getLocaltion() {
      this.changeLocationShow = true
    },
    locationSure(item) {
      this.formInline.localtionName = item.name
      this.formInline.localtion = item.id
      this.changeLocationShow = false
    },
    // end
    closeLocationDialog() {
      this.changeLocationShow = false
    },
    // 删除 应急联系人 start
    noticePeopleDel(id) {
      this.selectNoticePeopleRow = this.selectNoticePeopleRow.filter((e) => e.id !== id)
      this.setNoticePeople(this.selectNoticePeopleRow)
    },
    closeNoticePeopleDialog() {
      this.changeNoticePeopleShow = false
      this.setNoticePeople(this.changeNoticePeopleShow)
    },
    noticePeopleSure(item) {
      this.changeNoticePeopleShow = false
      this.selectNoticePeopleRow = item
      this.setNoticePeople(item)
    },
    // end
    setNoticePeople(selection) {
      if (selection.length === 0 || JSON.stringify(selection) === '{}') {
        this.formInline.noticeBtn = ''
        this.formInline.num = 0
      } else {
        const person = selection.map((item) => {
          return item.name + ',' + item.phone + ',' + item.wechat + ',' + item.staffId
        })
        this.formInline.personHidden = person.toString()
        this.formInline.num = selection.length
      }
    },
    // 服务人员 start
    closePeopleDialog() {
      this.changeTeamsPeopleShow = false
      this.setFormPeopleData(this.selectTeamPeopleRow)
    },
    peopleSure(item) {
      this.changeTeamsPeopleShow = false
      this.selectTeamPeopleRow = item
      this.setFormPeopleData(this.selectTeamPeopleRow)
    },
    // 根据id删除人员
    peopleDel(id) {
      this.selectTeamPeopleRow = this.selectTeamPeopleRow.filter((e) => e.id !== id)
      this.setFormPeopleData(this.selectTeamPeopleRow)
    },
    // 选中人员 提交数据重组   服务人员 end
    setFormPeopleData(selection) {
      if (selection.length) {
        // 注释eslint
        // eslint-disable-next-line
        const name = Array.from(selection, ({ member_name }) => member_name)
        const code = Array.from(selection, ({ id }) => id)
        const phone = Array.from(selection, ({ phone }) => phone)
        this.formInline.designatePersonCode = code.toString()
        this.formInline.designatePersonName = name.toString()
        this.formInline.designatePersonPhone = phone.toString()
      } else {
        this.formInline.designatePersonCode = ''
        this.formInline.designatePersonName = ''
        this.formInline.designatePersonPhone = ''
        this.formInline.designCheck = ''
      }
    },
    workOrderSure(item) {
      this.workOrderDealShow = false
      if (this.routerFrom === 'local') {
        this.$bus.$emit('workNum', item)
      } else {
        window.chrome.webview.hostObjects.sync.bridge.GetWorkOrderNo(item)
      }
    },
    workOrderDialog() {
      this.$message({
        message: '已取消',
        type: 'info',
        duration: 800
      })
      this.workOrderDealShow = false
    }
  }
}
</script>
<style lang="scss" type="text/css" scoped>
.content {
  width: 100%;
  height: 100%;
  padding: 0 15px;
  box-sizing: border-box;
  display: flex;

  .content-left {
    width: 35%;
    height: 100%;
    margin-right: 3%;

    .top-phone {
      display: flex;

      span {
        width: 90px;
        font-size: 14px;
        font-family: PingFang-SC-Medium, PingFang-SC;
        color: #7eaef9;
        height: 40px;
        line-height: 40px;
      }

      ::v-deep .el-input {
        width: calc(100% - 120px);
      }
    }

    .tree {
      height: calc(100% - 160px);
      overflow-y: scroll;
    }

    .service-event {
      color: #121f3e;
      font-size: 16px;
      font-family: PingFangSC-Medium, 'PingFang SC';
      margin: 25px 0 10px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      .select-servive {
        font-size: 14px;
        color: #7eaef9;
        margin-left: 15px;
      }
    }
  }

  .content-line {
    width: 2px;
    background: linear-gradient(180deg, rgb(126 174 249 / 0%) 0%, rgb(126 174 249 / 40%) 50%, rgb(126 174 249 / 0%) 100%);
  }

  .content-right {
    width: 62%;
    padding: 0 0 10px 30px;
    box-sizing: border-box;

    .kd-time {
      height: 42px;
      line-height: 42px;
      color: #7eaef9;
      display: flex;
      justify-content: space-between;

      span:last-child {
        margin-left: 15px;
        color: #fff;
        font-size: 14px;
        font-family: PingFang-SC-Medium, PingFang-SC;
      }
    }

    .order-message {
      height: calc(100% - 60px);

      .service-event {
        color: #121f3e;
        font-size: 16px;
        font-family: PingFangSC-Medium, 'PingFang SC';
        margin: 25px 0 10px;
      }

      .form-data {
        height: calc(100% - 30px);
        overflow-y: scroll;

        .formRow {
          display: flex;
          width: 75%;

          ::v-deep .el-form-item {
            flex: 1;
            display: flex;
          }

          ::v-deep .el-form-item__content {
            flex: 1;
          }
        }

        .repair-work {
          width: 95%;

          ::v-deep .el-form-item__content {
            text-align: right;
          }
        }
      }
    }

    .maint-table {
      width: 70%;
      margin: 0 0 20px 80px;

      td {
        padding: 5px 0 5px 10px;
        border: 1px solid #121f3e;
        height: 25px;
        line-height: 25px;
        vertical-align: middle;
      }

      tr:first-child {
        background-color: #fff;
      }

      td:first-child {
        width: 35%;
      }

      .one-line {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .scope-del {
        color: #a1c5ff;
        cursor: pointer;
      }
    }
  }
}
</style>
