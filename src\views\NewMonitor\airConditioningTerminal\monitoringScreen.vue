<template>
    <div class="content_left drag_class" v-if="elevatorOptions.length">
        <div class="left_content">
            <div class="select_elevator">
                <p v-for="(item, index) in elevatorOptions" :key="index" @click="changeSelectElevator(item.id, index)"
                    :class="{ 'active': currentSelectedIndex === index }">
                    <i></i> {{ item.assetsName }}
                </p>
            </div>
        </div>
        <div class="right_content">
            <div v-if="videoUrl">
                <rtspCavas ref="rtspCavas" :rtspUrl="videoUrl" :videoName="videoName" :hasCavas="Boolean(videoUrl)"
                    class="video_preview"></rtspCavas>
            </div>
            <div v-else style="width:100%;height:500px;"></div>
        </div>
    </div>
    <div v-else
        style="width: 100%; height: 100%; display: flex; flex-direction: column;  align-items: center; justify-content: center; padding: 50px;margin-top:80px;">
        <img src="@/assets/images/newMonitor/no-chat.png" />
        <span style="color: #909399;">暂无数据</span>
    </div>
</template>
<script>
import { monitorTypeList } from '@/util/dict.js'
import moment from 'moment'
export default {
    props: {
        item: {
            type: Object,
            default: () => { }
        },
        socketData: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            currentSelectedIndex: 0,
            elevatorSelectId: '',
            elevatorSelectName: '',
            elevatorOptions: [],
            videoUrl: '',
            videoName: ''
        }
    },
    watch: {
    },
    mounted() {
        this.routeData = this.$route.query
        this.getElevatorList()
    },
    methods: {
        // 初始化获取所有监控状态数据
        getElevatorList() {
            this.$api
                .getCustomGroupingQueryList(this.routeData.id)
                .then((res) => {
                    if (res.code == 200) {
                        this.elevatorOptions = res.data
                        if (this.elevatorOptions.length) {
                            this.elevatorSelectId = this.elevatorOptions[0].factoryCode
                            this.elevatorSelectName = this.elevatorOptions[0].assetsName
                            const selectData = this.elevatorOptions.find((e) => e.factoryCode == this.elevatorSelectId)
                            this.playVideoByElevatorId(selectData)
                        }
                    }
                })
        },
        // 改变选中
        changeSelectElevator(val, index) {
            this.currentSelectedIndex = index;
            this.elevatorSelectName = this.elevatorOptions.find((e) => e.id == val).assetsName
            const selectData = this.elevatorOptions.find((e) => e.id == val)
            this.playVideoByElevatorId(selectData)
        },
        // 播放视频监控
        playVideoByElevatorId(selectData) {
            this.$api.getQueryInstanceFunction(selectData.factoryCode, 'previewStreaming', {}).then(res => {
                if (res.status === 200) {
                    if (res.result.length && res.result[0].url) {
                        this.videoUrl = res.result[0].url
                        this.videoName = selectData.assetsName
                    } else {
                        this.videoUrl = ''
                        this.videoName = ''
                        this.$message.error('暂无内容')
                    }
                } else {
                    this.$message.error(res.message)
                }
            })
        }
    }
}
</script>
<style lang="scss" scoped>
.content_left {
    width: 100%;
    height: 100%;
    /* 设置父容器高度为视口高度 */
    display: flex;
    flex-direction: row;
    margin-top: 30px;

    .left_content {
        width: 250px;
        height: auto;
        padding: 16px;
        background: #F7F8FA;
        border: 1px solid #F7F8FA;
        margin-right: 30px;

        .select_elevator {
            width: 80%;
            margin: 5px 10% 16px;
            height: 38px;
            cursor: pointer;
        }

        p {
            padding: 10px 15px;
            color: #333;
            border-radius: 4px;
            margin-bottom: 0;
        }

        p.active {
            background: #E6EFFC;
            color: #3562DB;

            i {
                background: #3562DB;
            }
        }

        i {
            width: 5px;
            height: 5px;
            background: #96989A;
            border-radius: 50%;
            display: inline-block;
            vertical-align: middle;
            margin-top: -2px;
        }
    }

    .right_content {
        width: 95%;
    }
}
</style>